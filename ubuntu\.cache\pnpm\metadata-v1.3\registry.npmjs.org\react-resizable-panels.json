{"name": "react-resizable-panels", "dist-tags": {"next": "1.0.0", "issue-297": "2.0.8-rc.1", "latest": "3.0.2"}, "versions": {"0.0.1": {"name": "react-resizable-panels", "version": "0.0.1", "devDependencies": {"react": "latest", "parcel": "latest", "process": "^0.11.10", "react-dom": "latest", "typescript": ">=3.0.0", "@parcel/packager-ts": "2.8.2", "react-virtualized-auto-sizer": "latest", "@parcel/transformer-typescript-types": "2.8.2"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "0492da2063a515d85c51d4686dd2db3f1afad6e5", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.1.tgz", "fileCount": 26, "integrity": "sha512-xzV4wgnGeHKSXR+GMF4xr7WONRW39NrwVUuGtM0bm4QJLfD6fxhfDl4T0xsfyeiPGV+cjoOKQoharTpokhJI3w==", "signatures": [{"sig": "MEUCIQDxFau2h6ZfrgdjQWTtV0s9sKEKi9qlMYRijJxkGTwnOwIgEwJ526Fg/VMBMNFEIDVnSWTftfrVbEzbhiW/OAHhO2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4140475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpMB8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpohA//X39U5glRBOux7XP0GiI0r74vcr4T3j59FYiOZHZ1h/pSwisN\r\nwzIvOh4sCP3OAf5JK56vHpATSLB5sUiny8svGl8v5nU95TSwPPLs04Bij2sg\r\nUErtBAH4mvgHqPpGKPThJ28t0jnCvAoJWxNCfkDS1ufZdx95mnvwq2WrAW/K\r\n6eT5KIGdtKBz0aC12b1+x0v39GjecH6hqg7SqIeWNmcp4QJ1hROy7lOjpQV3\r\n8kIzrvJV+OhUyNdKwrx4oCIXmNTL31OSxu+gq8x3JmPI8kJTLlSnrQ0N6R5H\r\nOq4qyeyVuK4v7YiiJq0g/PThl/NUk+uKdByHifemnMR7FTN3c4nt1mdkNv/g\r\n4+/ooHZM1cyekM9LCWY3qUxCvpOBJBN7HfnCJTDw5Stw7LWRgkLI9iv8MhUm\r\nzWnD6R/dlF3+qqzfAqhPK4LuIL/B1gOq1hf/EodfuKecUKpFkqm1825Be4T3\r\nYjpWpOFkTkiXriMVMpBSmcc/WTv6ZdUhOq0VxPhRxRzIfW227XUG+9NQvZdF\r\nAOn7lhk/trPwdrIm6i6F8/5lzrjNytw5gbprkP1Q4wqVaLiqddHVXeW4Uj+o\r\non+92MnBu9mvE0CGOcMq2ZiphMzuqWn2zCrPvLkoijMOJseQvwCyrkNTT5K5\r\n2C8vRrCT46lXvi5CHxwG0I7Tzp8PCxfo+H0=\r\n=Ikff\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "react-resizable-panels", "version": "0.0.2", "devDependencies": {"react": "latest", "parcel": "latest", "process": "^0.11.10", "react-dom": "latest", "typescript": ">=3.0.0", "@parcel/packager-ts": "2.8.2", "react-virtualized-auto-sizer": "latest", "@parcel/transformer-typescript-types": "2.8.2"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "3023ac2d3f93dd08362451807122e63a5c158c11", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.2.tgz", "fileCount": 26, "integrity": "sha512-5wVKQlQnE4OHs9U6mE6lcSM+pX1NHZPGENWlg3YhpWNcESBLqMOiTCJBb/QyAnbqDXaJz9qcuOuJWlxFjMDpbg==", "signatures": [{"sig": "MEQCIGnZZy9R/q6RPeXPZr5oWSXDU9Y10K1YZzFO0DBSZuiGAiAcYIIOP9fMIp9bJnLRbBsWhd1HHdeb3ZWD4BU6a6F+Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4140571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpMIYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaFA//T0PR2ve7ZS5ROjQtlu5GwPrL4WgnhOXMxbzlRfoDqVLJoajz\r\nThdVp3t2qZ4v4NPrFI4Bgmb/5sq/dS7v174BgOh1po7+aXSVOtupMFozZywm\r\nyXdH614jTwHqwT/R9XWmIbbLFDnrqVa0jBQgu6Hw4uDtP8BlPzgDH+a9cSwW\r\n6/QPy+lnTLZvRmI7TsBe9L+ajLWbTur06x/cAQO7Daxj/5S+1CSYVBZE2kFh\r\nLRUk248GRiOenSw9yAmnGe/VrQUHeSwB8r/yVEjb2L7y+O8op3EacOjyaVlN\r\nIFB98cwg1zv1nPGeyBwa22u17h6wTt96nB94iKEQjM7hcJCpHJIchz3yGIXm\r\nB6XKgnSc3UQq38PN/uNleCKeGGgg/6N7byaZ9K0UQHO2iZOd0mdbbZqVfr4M\r\nPrMyvZpRFxowePj+jM0sdF0E4meOov0JpMxQpDJB0l4FcKVhyFGzbqpeWbZo\r\nWNCoZuMbqajI3+y2CcICMpfJ4IO93/pjn3J8nVVf5awDIOToNXrst9WEcxZO\r\nnKdl3bZql2qyY00/em+WO1d/4ApiqGeSrciGenjumPtK9QlXjWNxq098SYV1\r\niAKsNAK5b1HKp4P/MvqTXtEqQ3euKk5mak+Kjo1A0PEn1YHfWVK/B8tTal9i\r\n8gkl8BqSPGz1iUWlbPplQIVbaWJLXRk1n4Q=\r\n=B8or\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "react-resizable-panels", "version": "0.0.3", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "a6791ee9ca9d42cd5436e6d6d3b98d29be2edd2f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.3.tgz", "fileCount": 15, "integrity": "sha512-aiUchOgH5KkFrYulgCNuMiQp12tL+hHNgHfaJHnU//krzdS7A1iUdAlFxCQq3Ue+8HJex8ib3Ix2Sn0vuQeEsA==", "signatures": [{"sig": "MEUCICyXEyxA57p7v+Zbl3W63nPeuWRPgoF7xIep8BXNnP5OAiEA0d1r0DvON2YTxoxB7FsG0cXHYfnwDDh9r3+3o1rD/oI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 86923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpd7RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog3g//ag4fz2l7OaYD+iDcjiOLVWfi35ka62eMwji/uFWhTseLNuhi\r\n53BoaQ6L1Y8PsAYxHMKAKpR5QBBqhCPqHEhiOjVr/IKNvZGmCczrBrHiIwVg\r\n1rnkb4i+NPW7biS7WCeGAfPiO3wW5vF+Y0Q3JV2llYT6780TrwhlgJl0GhSm\r\nAYl4Vi0nzno7s9OMh2FtzQ0rQA6Nj2ZDktXE3gUjSxsHT65E6OrZvyi74Y7s\r\niSsflw0uJDYA9d9YwIKNKSq9JHtciLbahiEYknY8X6E7OewWmbhHO3B7QGao\r\nrzp77jTulenZJqinqMuFVt8SOUzo7gxgJBzMi8NQqqNnlYFae35uROXT4qNW\r\nzJex7T2yJyUtXZ7cQlTQIAmd3nG5hZJoKCR3q8rHdRmZEIMITjTTVYSY0eeN\r\nKhk9Q2nmkcq8yGj5lxtdTuoilewsgHRwins1tbVwLubzD3RhNrPDy9eRvWZg\r\nmelnr0fU3XvnJKTF/IYUp+lQsiHX01I8rPxSerzGVs6UzduUfws/TE095SZG\r\nnOzqaPfELq5iPub9l9kCKmETaHhjZReKMnSLipYXDRATy3oRmkd+vHq82P0x\r\nZdmLfcp/tOvNg7zs5EX/39oqORmPnZ6bbEpkbNbCB8GSJ5L/4RwmxgGJFu2b\r\nsnFU3GORQ8DcJhNWkqL8M/jyL1RFD43rJ3Q=\r\n=+IqG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "react-resizable-panels", "version": "0.0.4", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "87e5492c229fa422734c88c7565beb2c55320b0c", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.4.tgz", "fileCount": 15, "integrity": "sha512-bxJnVNTPrkPE3f3pR3T5szVQJiItgIom/+IxiSGJbTilJnKhAIGNXc7MKPjO4lXXEhrsXBOoPGzBR9UvKIEibw==", "signatures": [{"sig": "MEUCICtZKgFCzHWeNXQyv8smSY6c7QLfSoGVmy1+2nXcO0udAiEAxDpBCqzj2He0ivgt7tcCc2EfIDt1IdBKv7N2YsY+MUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpecpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpbrw/+O1FfsyGVFK5BSAv9NvDQUA/EvtE6USrt688SEZH0+4CDooop\r\npccFoqWdTHqoQdZoKeeT3ZpsCghquge9CiNhOY6sdD95aeR8opQdpmhKz5Si\r\nT+l/KlIZw33I/28yy0v01Gr13O9zERtTMg6doLnOsO3rNbRV1qmidvjP+4qf\r\npg1zqy8wbdgjepDPLSRXbsvkKzuHqYh3rtXpbsrpAqEW2rXE6bRe13v0yEOT\r\nAHg7iibPa+AuzeZHNrMIhzL0HBEu2yEUznEztTmTqNn/7Kp0DH981bus8wUj\r\nXQ5lkHDIZwpOCAKq5MfuAXFiSHIDejPWCATGrd3WOh80Qr0Iyo45jqWxFNty\r\nwmLQ06jbOx7vuKkVePJPfTAEZMPikQd1I8kKuIJyuEjs0rVp6RE6yZ+Rn+z0\r\nW3EonJKy06GLEHy42+JibzgMrcNuTJOUQiurHxCWcImmF3avgycCqrGimMUl\r\n04CaNEBohlkhAmDxCsBjZU026OdCi9AaA6CSu3WtS5s2HW2VNlXnNFdpPQOi\r\nh6tRCsCLCF1I4XQY8M6lSneT+ZsCNKQXwmG3mguRmWOVJDBa4XQLdBjs97ZN\r\na9VMAstMbdlRHJvG9dCgMvCnXfo9YgT3CfjbGOQkiEa6JyELWcI+YhEcFnSC\r\nVEzf8TIL+31t2OKXr7WpE5N7DBbFuyg65RE=\r\n=Nc5B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "react-resizable-panels", "version": "0.0.5", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "fbb3f35fd03b190f9488dc1b390a38653c7a58c1", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.5.tgz", "fileCount": 15, "integrity": "sha512-RODMe2CfPZF7KzP06D1NNQmZN+phC+W1sdoVCdIycAA0FxwBHO/cfSRD9d38EADYIIfOJxJzR3FAtHcllr09wA==", "signatures": [{"sig": "MEYCIQDJJeRM5/U0z8Yuz9FxIX6TvBjy8m+2B4fM+ci2mGdOwAIhAPffthth28FYJjDrS3RL4SZTfvVpqvodN6pgNF9vgQDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpe0pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6vw//Tr3LLCGTozxlvts30mtWB27Y0jaGna8NaIXnuQMec7XhPS2q\r\nmptOAjlAiNR3a3xgZlW3hwr9387IRdXrhdMYkU0ayzKW65i5aoG0LyWlF7yx\r\n0u398O//PWV4garl8ymNtgh3vE3WJwCkUPGDnXqSpKwjHrCBBvqkes/lwaUi\r\ncHjJZ7oibiHanxtpQ6M81ztBOa4ICpjX9OYpRN+RIUrYnk1wDeHwYBgWFFW8\r\nmcSN+ml0JSjr6qC8aBt49Ix/EQnC06hWcgvXd7YSIG31eYdo1frFVSRwNbp+\r\nUANbk7GWcq6+KhuQ4WjCcKJ+Nmf8Pa80eZsdpLzRkWvdp9fuDItWhXAfJurd\r\nx6d9r4wrTcHlLdhvWyUGj3FJKBmNBuzT0Cggyf5jXWax6PkodsdXvgbO8d1+\r\n/rlHPx3LAnCPLGV6TTY07Z+f97jbcCXADP+fbVQnMiJZsE/+BOjH4653Msbf\r\nQgg6GZQtCr7AypJOgXcWUbD3sdYDJvMZwRgzq+hgYHBi9t40WUrBRROnrxW+\r\nE7Y1Z8il383lQAG/59rCNyYangkaXi2cvOf701Nf8E6v8QeRQLsJ1auWD26x\r\n1dTKILfegRrsBAvjpEUCUcvIxgnAVLipE4XToB+uUlXp7zakRERSEfdjcQ2y\r\nFAmiA5jT2c5SrXPy6RmAGGMnwW8KH+8HEGA=\r\n=6mag\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "react-resizable-panels", "version": "0.0.6", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "791fa87099632410b1ce4b435205fe58db95c7b4", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.6.tgz", "fileCount": 16, "integrity": "sha512-Q2h+1ZN0pE9Oj0ighP7mj+tpvWwxuA1R6EqMwI//dQZF+Tl5v0aL+cUMhK6wJQVxyy2ufg8uX74s4agPo42JJQ==", "signatures": [{"sig": "MEUCIQCxkVBAXptaPkgetxdzgLFuJwPr1JLrrMBnvpYDajqeSQIgadPHxqj2H6fnm2KtZktq9PCyDchGOv9lebUtZtsxLYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpgjvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofCg/9EzV4gCqG8JbOBNBr331C8PD6sfPKlDJDBvO3AgNSzr4D6hWk\r\njCrrip8gLjThK8bmTEsBb27qtQxAzR7A5MhL0IGuoFwmQ+TVGlX14XabEvki\r\nKcVcji4ImEAgY6QXFWZ0Afzxt86YLNBvqsk+JEkOhlgDFwxidP4NfiTMutaO\r\nkvka+PBk7e/rClIS4JSRC6SREH7GwW2WVHf6qenYp0JmNpKJoeFyzFItQ0Lk\r\nKwn9bQT15tcbXpZ9lAcU7RrLaot7S06y4114Q2G8kMZlYCFfSJWHICqvkydn\r\n6VRFm3vEfvlaWFsm+UAEYvp2yrN3ujejnnAsMxUhoZhNPd5yluazUvXEpWOV\r\nStrJY0W/2Xe+Ll2g+qKDCUQlxyzvY9bCbVK2RQFHwxEv6rVCkWah4UkVUW1h\r\ntzyrp67gR3z9AU5dMXoAJI+Ze/uiNn6JnVbiZudPaAh+JRMT/IoRK0LJgenv\r\npY1X6Wydk7lyp7oa8e1xkCn6iapcibz5af5/zFpl/KQgy3tHwQ3wmoFtI/67\r\nl8fVttXeMN1a3Y2gCPQFvvU9NEwbKgkzr696jKPg2goIGYqXZjxd91kmjQnK\r\njaE3iOvAfRRBF8LAJhnQTPbzIeVs+LgI5yK1phkpcgG/slpNaDdPRTmptJ08\r\nDMWuUtSWWWEAj18cxl1PeICGzIFvW7gL2Nc=\r\n=56ql\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "react-resizable-panels", "version": "0.0.7", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "ab8b34b893e3dc394abd3ed34931c422289f2c32", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.7.tgz", "fileCount": 17, "integrity": "sha512-DPueSfsNqNWrOOBuwFJT8c+j/LXWMMH3wTeSHPsIVzJ61m9yJ4qvPvpHuhoMsR0gYjur103kRZdZymN9xIJtgw==", "signatures": [{"sig": "MEUCIAxIieJW68Iezqphs2TTwMb23OgfjLu4rrTnH124caHYAiEAgcLVa9LKOB0wsnOVcq3BZuNLTpqKC5ZY9bUq898lpxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpiuZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpevA//cg2oJIa9lOCpR+3/j/doRil9+SUjydKzCQK6xPJDXrwVInIu\r\nUB4yZXuuAD8DNNceiVjcCOzN/WMNea6UWgTnArpu7FyxpE/XbiYVFBPt4ovj\r\ngqacljIhQuPhk3p/LkDslpsdUur8lkhp2jyRzn2/mc73FKPG6qGTTR2Z2xwW\r\nYh4dJLBzpj558l5VK2/d4D9JzagoE30O/uYZoWUjLuVezSAwM27POKOaeSYW\r\nYwe8W69IP3mGz53RM6rLxboldjaVXPzGX5meHgPF/M9dkhtosW66WuIE1MG2\r\nKYfrrwAahCGV01tieYTmJnUY7oO5VxPsCw4PvF2HBdpHywKi0m6Xr0VLWj4h\r\nmWMpAlxIe9/ei+5xXvIQYCcuZvHLm0ACnNz9O3dUGR6Yl/BiAGg7hqxlzvgF\r\nYM3isuZX/XFaEoan5E+4GN+zjTQPa/Ny21hiGxk6j+rBMptjx2O2JJ4UIpUp\r\nP+J0hmVwyDPptSMDFAxrF/OXEwaLZBcHOpOFUYqcpkpK4R4tIrRMGV0xO4+i\r\nHYQfYAdOrXRvPAwoeKqXU5/MvtHKxsg0TLDp2yXcFpKAUk4/8Cwukm57MMwI\r\nn18+itTDvC6GL2eycgamH/dah0hyakUdw6h3o+M3wwuu6gC/iXK5V+Is+i4W\r\nWfbfaUQcXfNraxjJjEN6j7Fhw1dThGLsn84=\r\n=py/V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "react-resizable-panels", "version": "0.0.8", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "11fc04d4ca3e7ff9f8c5b5f5f4749e44ba1dab61", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.8.tgz", "fileCount": 18, "integrity": "sha512-qPEezTN8wQeQSNgnKLTFl3RQzqIU/6X291g3B2ZOjPbl4/nFaccXs5n6gQbgQhO8T9v7wmZgImJiTk7+QwDo4g==", "signatures": [{"sig": "MEYCIQCQuqdjMYtn0dQZhTOjlESu+zhm4KWMAGSIRBKV4AqXdAIhAMqd+nb3GVcfCZgmKtdvc8pv5afJZKa1rMOEPR9H59IH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpyq8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVSQ//Z9/OBT/0PFPWRVBWCPaOYuQmlFIbeRL4fQg6gxQ0KSfJbS69\r\nexQFSbwJ4nBpAi6DD4OV0cEldENpzLmvY2lM7ozNbPvGJy0gwecXascayyP0\r\nJ9j44Zdk52C9P1h2k19hX11oprzT46BdW6qVO0V0l2lKmn13OdvQh0EIobKE\r\nY3ND7IQVXoSQ8fLq/LAiZSW/sCBb4Gxaa9/lUKFnKBrOqm7KraBloe+Fv+1L\r\neLxJIGa/mNVVODn2KGsw+/AIHzxfPFsjZL9wiL2fuGZa5G0TUChFuz+7PaVN\r\nvTdiLuzDv5+4TdMiKpghu9trlOpdQtvUVGMbBzVMEAKPe01uwxb/M4OH4Uyf\r\ne6P2kr/ct2jTlL2vy3n8HW61p3VhvBvJbLsc/xQAGmPvu6wYIb4d+374K1Va\r\n6RXdw8rNLqilEV6g7RsLpIY6DcdO+ofvRdL/p+VbvfhpFALsGd7q0QgQyQPS\r\nuLaS8CpfxzlUwHD2uqEZZ0SgMztt9l90G18dU5xc+1zM5FlygWHvfhZZ2bPd\r\nEoZ2wFgiT2UwdH2hnoXT9jMJNOw34DBiTuew19kC8LA1/0G0SAl4oRmZ/87y\r\n9VuncAUOv6s6HTgbYyINRLXtQNQUnEuJBJlEbhNsR5f+yRg+/giEtojKzNIO\r\njG4Q+gz02ttzYMlxzU1cCuzhMgNBBflYVDY=\r\n=vDai\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "react-resizable-panels", "version": "0.0.9", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "8cf4441ad028fd94e62083c72cc5f2a1ea7964ab", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.9.tgz", "fileCount": 21, "integrity": "sha512-kR5gPpKle1QUi+Zg7mzaPlCyiqkXGobVo0BmnHvrbfwuuU20/P+XQBaNVJ4fCBlZ2bWj1bXr3/zUsf28KyBNsQ==", "signatures": [{"sig": "MEQCH14tZ+E+ZMtmGNKCkHGazWclr5AnjPl2JlNgArknZC8CIQDDgxUdBR4g2Ha1NafAvTKBni2V5lTPj66i0+sYYAADzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjp1/0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwVw/8Cg2qQJKA+To0L+rNSLBpMsuuM9rgQLRhl9cDWbsaTrDdYaF9\r\nah3fhyRqb3f1GPGeSnAKmaEgQWBg/Vzrw31lwd2rMU6lmpl7vLe6sti0O1I8\r\nGY3MecNTyACG2J7n1nVfpb4qBwT+GvNYG/XdiCii2lfUu+beVSAJeO53iXAg\r\nX5YXDt5tpAq+ar2PqSNu+kZDvSA9I/PWKzOOQB22HPjoznB53ewAOboKlAnk\r\nPpeN58xczFJPyiQ4yAwYepMvjPq7x7Cz5Qxi/hwWyqf+N/HT3cFRi9oDbb7K\r\nOMgIUGTM8jiSMLsWj+K2t+hrWh851jpGnNOudq71raV2b0IUO7R8/U6P3hhD\r\nvvsAe5hDsaQi5SDThf7QKCXTuS0innjmIqmrCaxpyihbsrFtlEc4dirfEp+2\r\n6sPsHLWeiwINEP9KMee4+xtpZUlWcPU3mbY85Rc1IYjjdLJzJ8JtJM6Ma1mL\r\nMMhpdBiEgyfs+XDRHR8fYVr7kk5EIW8UElZzx3SyvPRbawecjudYv53HKvM4\r\n+UjlXas4DzKysCnKGS8rMEYJO5JZYI3tmzI8dBkdZIJosXcDjeG7zvSVIvkD\r\nKDHy9fb1Y7OsO0LBhw6bZU5652l4WWjAWua6fPm3zNCH5v6+oIQWSUd1Oz7a\r\nBztLBJZtyLKxIaTqIvSyEnoA93XfBkXvzlg=\r\n=T+uJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "react-resizable-panels", "version": "0.0.10", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "5e7bc7c8fe2cdd537e9216dc00d6ceea86001e25", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.10.tgz", "fileCount": 21, "integrity": "sha512-8xDZOC3LHTru0VjHyWs/75TtAwTEs4wHgiG2H/hDAqu5THvEcyWY/WGilLDxi+Lh8h8K4Gpnm/b9LTyUDnPbwQ==", "signatures": [{"sig": "MEUCIDiHxawF+shXH4NBNeEGb0RL2Wb2iXQp/oy5lBeJ+V6EAiEAv5yl8k90e7ptIu9nqLGAgCQa97ee8I5Dvf6qjJeJ6uI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjp6/sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiqA/8COos/ClOB0xHylcuI8gBiRjGmVYiOnYZiSrKxUzfSxiGStrS\r\nP4c97QUfspUVcCq/a0FYtqpzwRs6vwodIzNjhUJ7R8mokFBp1gnnPyOkLUDE\r\n/16ENNtWPAURL8+dCIk+DeCLCRHM9l3wsQ/YjFXY7gnMSlY1NDnT4bt3Fgat\r\nNtoTIP4OiAqHbF970uTdsHJRAgK5Ntcui1Q73SCu6F75iMZxL/iZycgHFY3L\r\nSJrRgUEeAgoa8NNA6Amu+WxHvmLyJSKZ6Q0wL58nRvdYGu2W7JZobfQ3ftfE\r\nEBlmAe1tBOB6+I5PwFKtoxKH0p3KVTmuwV+RZipA66pkZDc5p4QvC1pABfjW\r\nXh4ck25TpZ8N6Rgpl6P4FXkWlVrMpHQdRUUZhPDyfQs8FKIHzpou5Tt0sem0\r\nw3WU6VCPRoSLdMezI9P5omKj+PjXL23oRRsGIIxZR0fIVajJQQKQIHSrw4D+\r\nz7mmDajMA6U2utZ4Gcd9tYRcaxagpPoyVxHhukTOxIVCHEq+Kpb3RplTZaRr\r\nhOPW19O5CzarTk852KTFvR9nCwFJcu1kPrRag2BUg6kFh1BXdUdonXKsmRHC\r\nPD575mpLJUvzxHf5sLWBbGQF/iO9xD2fWKAEkAKxRq88wIlcXgsE7DzRMFz/\r\nNLIVW/nLnEY+228hq2v9PDZNtcNksIxxvi0=\r\n=A224\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "react-resizable-panels", "version": "0.0.11", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "93a14fe793f7b8a5ac19ebfee59859f1706f7427", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.11.tgz", "fileCount": 21, "integrity": "sha512-XjEcEf/4fOpAvhzuscQVwD/9foxFmExwfT5xxsBVTnjeqL+eOd6U2dFwHK5bwZOBaFpVhIaLdei0zKAE98LV6Q==", "signatures": [{"sig": "MEUCIQD2fyveHimRHYsP6y42ACBaU3yigwHVQlecFwZnBOiVHAIgP8Dh61QnvowFQAMQoMU2EzSRTJ7pYAFqV+WDgmgeWL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjp7GZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosJRAAhF2qjoQBGOZAoUcomBITlyB7yjKC2CRElkHwYMfVt1AkbjKD\r\nX6lJqwq6eLhAqoNORGdWpWh2x43UV1OAhqqoaOmzmJXPq5wjVXkFfAtfRWP6\r\ny6oREMz6+hXh37ptBZxFwVn4+ER93gT0BZw+ONDnlsBW8xNDCx8+9PseOWpr\r\n1p3Wvsvk1HjJxgXHtBJ9aB33taI/qVJYf/jyLuDtSttyBFjcIzpFjPUtQMmi\r\njTDJLq3lXveW9+W8ZgCYKgpprfnTWZFbo7cQJV/qBDAjQzeYuYhzDdOxsmco\r\nvBVcY969PbIi8YHF4rdwMqRoL2qohNKna2L+5VxbEbqM6wywte6kZ57FveHn\r\nlhxYOfP+zuqMK93ckpFApjSVdGy4VQWYYIFFG2KAizdZZ08JZFJg2APfELlB\r\nltA3pGD79hFdIy/Iv67f6763FsE++CpZIzCjUmqz/dEVqSq4VWdthHGCbJOX\r\nPztwbVD9Fa0o2Z0bjPOImNPaSowWbT8/GGeb/qUJCBvvNr/sYB1XLyTz0hOZ\r\nQkMMfvQv+h1F0ZZQTEGaryion0N2kNI0B9ve7e++MMnHRcMSb3rqWH98FcxU\r\n64iFKrFVCGsrsaou4onKtclzslYwpIvSbFMU1H79Z/3CoNFTN8OTx993dC+5\r\nIhdZx96wW7t6RHTYP8iyP6yb00jHCRkewkg=\r\n=i7eW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "react-resizable-panels", "version": "0.0.12", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "60dacea9072be00558e71e1e71bb527d7a4832ba", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.12.tgz", "fileCount": 21, "integrity": "sha512-RKrBMr/xlbkwsKZt02HDy+J2FOOfhzdT6R7Thegvx/0vV+ZecTczqbnJW2kEnIO7ymTSyPXMp7ldAA8xoV09mQ==", "signatures": [{"sig": "MEUCIFyi+FL65b6PeY1OK9+teoap8bHHBjlXLCmh+WCtizMiAiEA2twhAgyKKAUSJZKuZTaUuMkgpUmOhmisV3OAdDzoPWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqKqDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMYw/+KBLGoA0+zzKpYRO3MTOrTgzoOlEORv1PEkIk1XGRMMMFs3eB\r\nvRgFTBdwTRsjR0D5gbSeLkebFh3AyWQUul8VmSat8gvdHUKaoOjGxRHTjsq/\r\nhWQsCQaSxOrIIiFZakTUzJOrrJoDgQiB3uT7j1B+clIALwF0Kok+8gUxIJIJ\r\n8J0CiM9+pLXDDlctNWQoEzPhtHTJo/akdRgjqAbKj4hkdoqD+2J/5lYZuzXQ\r\nkdkTu/0mo4aJu6jKx4jTRBAgdCGu9+Jw7rLgiIbrQStOxCpxeh3Vierw7eJt\r\nwrbaYXczz2zk2+2tUOCoTsln90m7crOWS8TwFm2so9gnqYBnhr2dvLnPpHk7\r\nU5UCRMJ0MhPMma/ZAnD3wRcIRyH0Xv3YMvaHl+vUHX0yEBrPiPjfvCw5mvhH\r\ntySqDuIITlokUUBsq7nwXxuwrrncMQrqiqJ5xvsGNw8gR889c5vIrKnntYhG\r\nAhoDA3rYWqI5WA7RIS3jOt2/b1NdEarCr5lr03cXdIe40yjd8BL/jOWRuFon\r\nae3pmESTU6C4XXRNLfIhUWklnsWn4tLJxVlZVGrhji9uA21q+9EyG3hQTpIJ\r\nddidNLWyaGptUcNaIFRHwJadsNmsFRLiwHypCJETTqes3H9b9wJ5DYt25PUP\r\nUnr37T/jBucAi+qWnrfwWurEFC7TGmDql/g=\r\n=tfZ2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "react-resizable-panels", "version": "0.0.13", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "305beb182c3ce0fbd14cf41359dedca1e0062268", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.13.tgz", "fileCount": 21, "integrity": "sha512-U56o6qt54pxYnCMCLbJ3VuIIrLBmnn0UcjYcwdrs/4WPQMXmPMfw1peq6LMa0dAnH92DetX6Db2gu9vYrHFDHw==", "signatures": [{"sig": "MEQCIG7FH2zhywQui5GkVQjjxdTq9p5FB2nQXXtOfZbubcXoAiAMCdtW6a+5S/9ibapqH0glyhd/uCthpUvBx/OwrXQMSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqbOaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpcBAAjpnVEa/tcicvyv+K0/fMfcJ/8VuRA9ZzWpsF5+BPC2TCjRta\r\neUvwhWrcL4vXU6gehxU9MWCmS0WgRDEM+MK6eLkWvgrnpquYBYLsiOysT5q/\r\n9DncgZig9112bkfbwE3XwGHaDajOLOd5tZWdxN6KNjK57A3gsD444Y9S5vPu\r\nGv4q54anyrL922wsRkEUGYFhGt3f//fFFlyT/8r7Wfa3YuJaE4aYMHaIzMEK\r\nOM/dJDA3L/gcOJhig3vxChpdMo9+OYvaArQJx74/xqAvRXiIDux2N/GjhrIX\r\nYT8dWYagqwGXYBC3q3694xukq2IlOgAPZItCerfiy9/S3YONk/HveyFM8S8K\r\nUSH6T96Eh6A+iMQLRelzB20ph6aDTMjYuXEs8chSqjxk1xA3CZ6ko2l7Js9Z\r\n0GOAXa13M6Hs1UZoz5FlhDkAra07Z9aa1WGdOXlLDNojsLXDR0fqUKGBmYsW\r\nvJKq9AAeiqqpVyhyVBYFUO3/OyJ3rMp/KZlTvK/7Cj2BZIUMGgjFvI1/+KWG\r\nVpHXiAHHvlGFhXpE5ZX0UtmZ8KKCtPdhV5lNCER+mjdRT2boXoeaEJj0QZwn\r\nC2NJUXcFOcGz2ERsUBYrZURLef19KptXif3iq/42fV6hCXhiPQmy8UUPWKtu\r\nnXx6VjoyHuJL9sbPLfBMX/xGUAAJPUCBc28=\r\n=AvP6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "react-resizable-panels", "version": "0.0.14", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d0d92b8c75e19d1ee6df04f1fe91e6adc1e8cac9", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.14.tgz", "fileCount": 21, "integrity": "sha512-mzvhZR0/5oMLiC/ATUvZ0OjSPSXNb7i/8PL74IhCARXPsiw63Yx3YgGdgtGfktrHri8sU53Z4MdTjp9K9Qlzsw==", "signatures": [{"sig": "MEUCIBv05mOI3LWUJBexLJ7aHDHg13SCuT8so38HojajV/tiAiEA4/WLR8rETnDarpk+PeveZbWZ/RlGYlwSrTmVrsUQZMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqbgtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN4hAAljY4ypLCyhbreoVZXOMwXyKmdT+esplHvBv1MJ87AhvLCHG8\r\n7cCVLwVHOGPMho7QmYhLvlR6w5GrJEq1BSgEYrBX/TbNBtUYfR3hl2ja/73y\r\n5hhGr5WPni3v2LBYXuU3aTXqiS/AWCnhnxXSKrp+ZJrwgJTj4gv6Uk8ecFe8\r\n9zNCDdZCtW2abNrpIStA6u2c6xQon+EMAwAjB6tPOmiTbJurRygX0Yjya1Ke\r\nir7+Vt0c+wL+i+CrksJsK6iUrhgiGa3SKuNm2CEgGxecDHNmG+i7Rsmm/9U7\r\nXqWr9IASgaSIaDaHs/x6tnQ+js9avNjXToK94xOYRJGsfnMR+0Nl2lDCFdly\r\nul57VWFTMdY/7oSk+uXv7yuBPolfubeWGWeSywFw+rqxCE6aJlEF/n2TY5rx\r\nrSPYLklrM+T7hNU3lPrbQi0atpHicIkjkIeV60DAjOpXHtW1m1MuMnpZK1A8\r\nRM0Txum6L9cNlM67Kmz7hbl5rX/91XmJfhBdDhMICoX8zAArHYZrlmsaUUOr\r\nwcjXPyWASuSk5vR7Ss6/2uuXNZXKks5xhf5J7o0KvRKAGvDy4MWqnF/ZRIa3\r\nEfpb2p8I6kqA7JQzrKu15HFkrHiD1Qs90q5Sj8rfjZSMyCyvD+YbBxnWUUxu\r\nUbADAuTPotk/GgBJBWvqyNLv7Xu0B1SIczc=\r\n=JxO2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "react-resizable-panels", "version": "0.0.15", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "366b87d6bf8d81b6faa043ceb5350dd349b7f9ea", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.15.tgz", "fileCount": 21, "integrity": "sha512-Rb4UHEmeny9zqL553z/iwNxlBfZ7F6O9k5ryblCsmmhC24HIUBqLL9iFI5NSpXkTLcyJ3ViLgGz15Ac+6080Gg==", "signatures": [{"sig": "MEUCIQD5mZtZqkKCYC1r/ZIdaLkYAX7SVxjp7/x1zw0/6pl1YQIgZYXa4MXaqNJFev580QSLRraXXk/hOY1fqphsb1qLQ68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqvgQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqL9A/8CehSLA6XZCGr4lzw+/qmBSD5HRAwxhw1tT1pvdDjB1tyrqZa\r\n3o3lXFy7n8+usZlE6Y9R9DEB08lmuEUk6DKh4YeytfgqhvzhXFHzeHdQ+0H0\r\nbM4G3foWhocmhnwpLsXmITmnnyZWSGLReWQpayxpOqFFA8hVzT+8O4cFtkJl\r\nudfa8o17xcavNc28QQw9rCcja5d7pRW4EHOxqLyWTn4fXOitlPIWPxR33X1l\r\nIj+TKgF69tPsMIhUpVcTFAGfcDoK5J8GStdmuBQsqSSsedwZSW8Fts6lxxd0\r\nrquJPWumx8jNNFLEJ9S1cJ9BOEaztjX4yjgG6o+Y2hOKQsQUpwl9OdTMUYBs\r\nhmSR+tH9TkM7xAIAD9TzE7uUi95etkKUhmu6fBae/Ie9yk+4swQG2yCtoeAV\r\n+mLlMF6UDaD6a/J1UDoXxIfle58y9pk+zjIXNRK1r5ZNIOdAL2yOeIUNli0a\r\nDCVTpszBPFBwZtdgeY121uXEwAXGGI+toZUa7oCUBITZX3KKfQRFOHychrux\r\nQjMGN78wkX7vLQhiDZfapZwKA5awzjnXhn34FQTIAFqzgMW4wQ/mYGrlMIXH\r\nnF1/K2+spK5OJudHg7Pfyrb0E7Q+fwW5S9k8r9L9rHAn71EZeHDQBQ299Ky8\r\niYgwUvYp42AJfb+V3gJhOTi0XWT624eobII=\r\n=YddE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "react-resizable-panels", "version": "0.0.16", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "1153e0d3db79c0d980d928264254d6a1f839bc30", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.16.tgz", "fileCount": 21, "integrity": "sha512-FfI0VApi3/zQLCoyuqUnW5DsHCI9ErVgJ6VrFEZZq/pOamXmyYpWkuxewsyNTe1qcUsNmK50pdbuCMFeZTEwnQ==", "signatures": [{"sig": "MEQCIFTVSDnBTojxjX9spmH3bTHunNXwGOaR6reQYsmbbjxUAiBiYD6Juc8K5DEDe/D2JvWs2Jg8u+pwGiQS7Ys+QR2iYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqxTyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQfhAApIXQPQWlNZjD9Y/PTDduwAkOiV+SfXuG3Mw4NGwS/WsG89vZ\r\nnM6p3r3/PjIa2+dhPqgx32K04t6sNr1mjAuqqh5VY5tn+xP8dPXYy4AZKSmq\r\nh+hxgYm5arfvlACJzLCgUlLPYRp6uvBjLmEDxY/nMDX/0CiSOMBU1wePQVUC\r\nN9Ke7iqyNoPFlNGfHJQFnZ4zdfNMy2wrYVp0R2TKv3VJVUm+E3itoqKhTiM8\r\nrP21j0bpD+bl10P3E+WTi+L3YFHyp/bcHU2do6R9aS+B+eGC0WmCrdp1HSjW\r\noHRkIFCE+p2+yO5a8rY6X0svCclWIGf+cAUnujaXHcGDsv6VDAlMyzRgJktV\r\nZxLJMWVtixM2IZHaYUCfzj2au0ozGnqV1h3d2+/ia1eEKQZJv+GStKFWDLSN\r\n3CBmc5eH92ExMJSSCUhQTdciCBfTcpzkdlG0GpWZ7pgW39WJNLZoTIolGcFL\r\nmxYQmKwYnpPoezZFsk7hCDVjON80wRyRLSwqfk8bR967jv+CLCPbp6tWSybN\r\nrp1KjejKXmCpp5w3ALmwHQQT98IiaiGZ9t82BAob/aIIVcLXMTmidDMTZD+5\r\ng6Cdg/2BRJKhbfWAvOfadj620UAZIZJZAUgZAp3PMe36LF1PmKGdg9qv0QZs\r\nbS+0bn4SYf7DW2juPTVrGXxmwVxz8pN6tfA=\r\n=01Ve\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "react-resizable-panels", "version": "0.0.17", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "b93a96c9403265c02c621ee209db5f38b68ab9f1", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.17.tgz", "fileCount": 21, "integrity": "sha512-8y7AwG8KxG4WfTJGTjgE2B4xFCq404qP/GnNdP1pqsz7K4EDZF+OIl0k/VY+2EArYXNPnSrqvIp8KA3Otal9aA==", "signatures": [{"sig": "MEUCIQCe0XbREze+G8wVBd7mYWiqSDbkj+dDO9phC1VaKa3ZqwIgBXVDUokfA1hJtDA1iNIeJAfi0fsDlWRUe/U5Pn0HQDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqyr6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUrg//bfLkO5cFZyBOtwXIR6XFVBQXw52iAVCQVLDWl/FsR/y0nvY1\r\nz3RgX2oKoYam2VcfodE01yYXOjCutrWj8uDVrE+BtdvyZCLf247YuchokvqS\r\net+S187wcg0XeY/rIiVN7OWo15UxqP3Rq/EPi5qs2mE7l1Y/qwESjXe6TXAv\r\nBc0Ri66Wsm7e5bjaTfTpk0Z3vEDIx162OJ/lSLLn5VyjCWz6WSVYhyC5vFO+\r\nFitRqZeza8TTj7B77Nf8EbfKsFQgOuX02HNndgHhQ2G8KWWwqXORQ1u9j6f8\r\n7tDt1VJS2s4XEp+cH1Cz4ccv8R1Q8o29haH+QIIZWSxa2cozZm3sNk5RpYvm\r\nHmFCcaBD96ZdXzB3eZHBq8W3108qqTbFfcGw0arxO8TSzDrosps5GAnGKg/x\r\n2q0M6qtjn3JtH2FuJ+8iwr/npew0AuUMrNd9LFjtQkUlYz9g2NJ+D3+7ub3d\r\nORqm6RegK5jasN1ZigQySAKp6tebDr+ecD6s8IIQ+cPIisF6hCx76kSlFFoi\r\n1sgUjWiWDcBGGn5XswcDRprmvB+JhxTRAfPFNq3a7uApi7qHHERZ/bIFACsn\r\nl4Lf0Ux5Go0Uu9Uz/xZ5rEVfxRr99wU1x8+9VRLot1SEYvxxdTSlt5yZ17aI\r\njV6ZGtMIJ1R7rRrujjRSTXbVG1GStyhpwZY=\r\n=hRMS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18": {"name": "react-resizable-panels", "version": "0.0.18", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "a0850d303538d66878a63c6b564839829421fd68", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.18.tgz", "fileCount": 21, "integrity": "sha512-YVbzfxoiAwXz9442hpVDGKQ4GGLZC8g4krKQJMJusYsFH30AJnny8WY8lJ9c0gblo1RpWsg6QvCRUt4/SFZMBA==", "signatures": [{"sig": "MEQCIDsogxYm+pQH0Jsi8RW7pqAoEuDIgIoAUCOblObnXSQuAiBU7FuBeqK/TsQF/E3t0X8rNfvCjWRcY9UzmHJ1cYnVpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182010, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjq1sNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSIg/+LTbjs37ffiT3K5HuLiqK+a9PC0TyHOuK0SV78Pa3fbcnXoCe\r\nHEFnBVWjq5lAKEX40ni8tGxJmksiURB4xau+0Fu1WdyIX5XoY5twsVTshG4/\r\nReHkV/VTtPHB1ama7PXYUDo2MN2pjHkCr2AlZAicEc6LJ1eWp2Y+8nHtL5xW\r\nI/TULEGs0V8qJYpxJKn9eOULGbcGkqOa12JmOPmCmVCHSC/+anl7k5NMkeu6\r\nqQmy2nGShX7J/c9LafeukSxVPfUbbV/Zewo370gA7bLu/9c2pstvalX6gGW2\r\n1IIYfIXvSoC/ZVRQtik5LMTsikQ7HOorYSNgYVuQXWJpOrcVRuj+t/sRaUfk\r\n2TmVY01ApDsNTtYOgplydkstq26UfwE7MgLB/9YNYB4+a1fzMbXNB22zZ+Aq\r\nDXlXOD3aTCTLmH35BIO72E93XHyxeNyDU8tnKVLvdHbWXd0efPhgoxa8pVLQ\r\nfH199yVGd74rFJEW+J+DecwoTIVe0F7hNt8kqJ9BAfl3q8FqdP07OxEhGYMk\r\nK/Njid55GCqw57nBvGZ2UV9EIRYb2+UjgfqrpW2XX5QWTWWACR5Ah3DcLQNw\r\nE2PLm7MshCROxqlIoo2Ot4Wq4GAp8E9+cAKCH5kGbRXMhIguoZCZJqnFCrM5\r\nAYR2WdGFCGCIlhyEPxjTh8mEQFLZ55vDfgM=\r\n=1uTg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.19": {"name": "react-resizable-panels", "version": "0.0.19", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f80db41c1ffbf8e939c22b4d15b2f404a5559167", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.19.tgz", "fileCount": 21, "integrity": "sha512-YfnUp1Uu93+QGNH29GnEzFriPvppGYEI9df6krlK1C7LQF03JaWrZCLSg6M7SIrA5bK59OI5DSzZXRk9CMqimA==", "signatures": [{"sig": "MEQCICqpLF0/XrCgza7DdYDNfC0GqQMU7Bq0shBXWgxmF7U+AiA0LVieq4p6f8v1byRQ6z23TA3zhmCWA8Px4LgDgu0MBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrF8vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoogg//RkLvv1O8bNlTfomV0Tn6Z2Liy+CbGeZWjKQQvVRopDkWyG2D\r\n+q8OAK2W5T/ApMKVKLJ+irUGTCuoY/MYrMmXdOWaaYM+kQAX9AVis/NLp4bK\r\nDnbQ/htvqT5oRHvk/xy/4XgdhsgVqrT12bV0Umaov70uCIbTV6B5PZp5r4qD\r\n/bxSozK5a5MK/pjZLzJP0trLKEkau2VNJix2bbbE8Fer4V1mcFhQc1E+Vts0\r\nq5Y/VO+HBJpylW5Fyqb4VOZLyNuA5vX7UNXdSc99L1qEOMaGy+i8xKkDHJw1\r\nNN5+zo3w1NRKChRvMarficKKIEsoP508f9Uut3wUgdmnELG1B3dTYKk/1Alr\r\nGLAH5NLIdFXqzjLhXMW8fhpMbQermDu9wW4mNaBLrAt8YqTSKzE9UJQEWTGC\r\nKuoDlz4WSTthvNMnWPI6wvILSBBaPARz+3YA/qwpDCBpcQFjtmBWx4AXW86v\r\nRz1nHdCy6UNEB99tVn1WC6iszHXbxw4nMIRoqkXXSVQqWkUMhWtrU63rimF/\r\nNkqwqa6oQF4Sr12Vq4g85Wf/BeWEFUbBK9iw9nvflSgVnAyj7MSy1LPKOaD9\r\nIya/Mzvo63TbV1FM1vWGLIPx71qSX4o32DYTHzz/YTFx/pfGAulnWONSFtak\r\nazOM345SMTOfQuaD/0hxS4ZPfTeTYIQ9VqA=\r\n=R4XR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.20": {"name": "react-resizable-panels", "version": "0.0.20", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "6643342e927fe9b56d5bc741751b198d22c7aa9c", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.20.tgz", "fileCount": 21, "integrity": "sha512-7G7xFN9jorFHhRSzW76ZO9WhV3s8q5fh5xGC8kkpo3QaYLjW4zTZTqTJrcrTevekFr/z5b9dbA+NAEyxedoU9A==", "signatures": [{"sig": "MEYCIQDP/YvcRtTIPSRut1e/tNTSuYDydVDs1ZjzC++o6TMOuQIhANcAP5IxQVXqJ4ciIQ7UMBkEbk2rS9qByFLNzHVdNvA2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrIJSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohWQ/+KiL+1gPGjMAg1xbPqoZe35QC90OtxcLladdCO2EoDBiXD2m3\r\nbceTQUYPHjzD0eVY0nYxqkG/Md9EpWB7oAA34BV8KQbTHH8/GHsjJ4MuUF3p\r\nBzBBrfD7jOdGEE2nUINFEgEMcsBphRewMTK7hLbEVaiWkyuttA+YPF/oiIrh\r\nqX8LzJ+eaatob8DEMN84eYrMUQxfYKHeDt9F6R2NYRGWXKLqt94doCi4MlAA\r\nvH62XSDWIdwycuXdX+hkTiTP9ibk+c/xtQYplXbqME7WswXxznYVvHjAEx5V\r\nuJZ3TehpDeeRNcRXUDiLWWuOnystye5JPeGaVBCo1lnVSCa0YeTl+cBDKwe7\r\nc6LsEnfEFxuDG6sB5aBxWX9/H61cCc4eeUChcuQ4SttbFipFFpmjzZz7uIJk\r\nVOtn8gHTR6aL0n73U+3D2AhUcsXEdGvFnvYLDB6IPeXOLwE751uT1O8P1bC1\r\n/bYqIf/GUIg6i0SurPwhIULUxCBGY0i7s7v7CPL21MBc2qD2NoesW54BcDUJ\r\n/gR2qu1gy0JkVlcoadA7Yi0URA7awirW98J7SLk6WYT+cZF6gR20pUhIwKOM\r\nyqCEyalnNj7IJOkYMlMw1uF3OxFQQjxnIVRkTaKJoufjOcTLLKSamEtYmXVw\r\nUpivN1KgQZUQ7Bu7ROfZFmLUoYWMszQ3VyY=\r\n=TEvH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.21": {"name": "react-resizable-panels", "version": "0.0.21", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "c004610d6c3c22719fddb2a14e8a7dfa8dd0e507", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.21.tgz", "fileCount": 21, "integrity": "sha512-Btk8m1WmU+oh5S2mXObNJOdea96932Sp2cqsFLXjaNBuycMz9DKAorlixQjc2CaqP+8qN5ADLGsPBNbnyPN4ww==", "signatures": [{"sig": "MEUCIEJhUrUKMoNrPbyQZrRJqix8dxaBYGkpHLIkATIiVFV9AiEAoEx2oANVmT9/MkFmrwI6VCbyDXYGDHnrUQyQibDKMso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrIWaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0Gg/+P4ibBq+8r/5UWvcDoYLij84J5FpAjPyEPfG4A+ttL+nzHhgj\r\n9mIBRYBqgquYpccKe4Ooem4TFDL9ORfZwpiSX/wFfgA20Fqp3XecBQXCkcp+\r\nMcSQzArbGLYehV4MxufHKUhAFlBsNLr4b13hxCw1gqQyqIu419rBnuQZv7be\r\nssXHlaGrC48r6g7lZj4HMLj4f7yAGbNpyC9l+z1HJ/+AaNPP+pRH/o7NQBYK\r\n3wt9U2/hGPDBAn3lr65NXsXLF/pyb1CSi1SWyOj8O5OEKbzNm1H1GABRCRu0\r\nql323rI7jcZ5zUrLByajoAjsMmKIbwMN18fT0BkeDGNG2HI8/xOYFbU9g6HZ\r\neOwgmcZqTo31p3JvUnLcxttNOELDLv1hqVHTP60Enpkd2Jg0rC6NRKxlf9BF\r\niBueTsP4tsS8Ap+4sm4M2YreX21VZwnwpuACmc646WlT9d05cRQtUX8F7/j/\r\nDDaiWBTjrhY/f4OCxp3ps7OLQIW6yw7ZE9zpbBPRfA7AVY6zo9y7th7I8tOy\r\nB0xcwU4SQImWhf/2rQLKo2XtAf1M62JZSid2Gm3QqMbUTRM4yh8zzOnBlev/\r\nmYL4XJenrHK0hQ5qKCTAO58WO5HvxwYi7urIZrgVXBGcrvhLka1AwRsWMjRV\r\npDA6vS1uDccN4PICfvjbYicSaqJD1HeEfwA=\r\n=bVe+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.22": {"name": "react-resizable-panels", "version": "0.0.22", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "03bbb8c9100ef00884eb56e609d6d308eb4c425d", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.22.tgz", "fileCount": 21, "integrity": "sha512-K8xqxFudZVMDB1e+ziIhqkLM6gbPo6PgJNxn+uc+wIbLqSAiZLX6qlWaQY7ut8wH1YHy5qiXprsqsVi4Qcqxew==", "signatures": [{"sig": "MEUCIF5hohCA4jhns4oahM1/S5eOiO2XEs8De7UXeu/pAtYkAiEAwHSJaJsWXuB6gwyvc2ykGN2O0VVy/MZUgaVI4F7KUco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrPKSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFVA//fFabWb4cgactwKKef3h7DszvfeWsQxTwYiBqYQFjo/AWM+L+\r\nUjkO0VhvYN1Tcb8I+c66uo1Je8rbACBeaN0L9SPy6eSdW4KTnHr7oC+Xm3we\r\nq2SL5J4VKe+UINBuzXO36QwVcWLbQKnfAgEoHIwBbFGbwyCOH73IDjQEMk3H\r\nFy50QplbPRXM0NW/MMZ9nx+8NaUU1oEIPn4qYXsLTTYMR1ixBVqZ5WYzbxEL\r\nTEAU6ncS5qW2L49CNVOwIn3VY4OgBKnV/JOeycYEHO2+Lgn4LqMtomEtFrAO\r\nFS9xVCD6RRr6tBE2G8Nb5tLbid43J6vqIi3EbXt1VEfnYI09EynSNveUx4d4\r\nfL+pLqp/ZBEwy5Gc3CxXZz71ZpgnBxu+VR4VSDLfx6hdV34VOoAn/+iocHcZ\r\nfGd5VXVNGfD8XDbE22kwPS7TPg7q8nJFBZ4/B4WSP6GB+ri59zICWTOD2DNl\r\nTx1vj5okEDQwIjvT5DVa7fkbXus7jvH0FyANnxiNnckNvORAMojrgIHALgOL\r\ngbn8cvzgou9qqNidTQ82h5jBRvoxmFuhMg++IXa0ZNj1fGVNEO27sO6luUKu\r\ny495+r55K2AWcZH1c2tBaybs8DeDo5AIxZ0OHlhjcZaxph89+dMJ88IVQFzk\r\nDRpvAtdRa2E7Qqp+8XbXobqvqthXU2GQgEo=\r\n=xpgv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.23": {"name": "react-resizable-panels", "version": "0.0.23", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "416cde555df4bd8bb75ee39a8236ff09b45e7576", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.23.tgz", "fileCount": 21, "integrity": "sha512-5uu+B9Zl81/01H0JyMjHr493at20EZ09+NOnt5P7+m8MPThefaU4D5vzT4rC9nJXDBSIkYOk1bk0HK/EmKi30A==", "signatures": [{"sig": "MEQCIHuFJYnFtt6i2DiUuKIU7yu+cLuJ2PiMjZx36iIkb3IMAiBVtXosTD21kaPrfBxYtrB/laVmvrUJMnCnDQ7OzGjJDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrgboACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbJQ//YqphuwlPTmaqKE3FtfXM7uMRXb+hkizM7lciZxQEf7d2MUjA\r\ncuTOUJxGUc3n+qfTAFV/6EDxQNOAaKyfC7v/eNgR/YaHMBIXIQLDCmleLuau\r\nCQCrRrKIJnfrHqblVX4LsGfGXAiYEghe9AAB+V3XWoV+UdyohPLgm6ndFBNg\r\nivokfVRHOjgBIIGhdShkLnrTDTnmx/rfLLzE5GP9oMG1Z1hoiHDEJskvl2lZ\r\n/Q7cis+0Rh5Y+YouNWrdGK5SrD1jUYVO0IjW6Yz0xmuEwv7gEhYB4eRiBjgi\r\nS9s7ikyQ3W5QLRqeEjKgVgow9PaJO1D23Uylw1t8TzKJrfH5cbj3t7wjRVzL\r\nFR2pRwKID9fVJJSua+qzYBEtpykRs7KgL0mOlN1mBR1UZwJYE9Aqwwm86N0w\r\nox4VukePycDyL/1YWGkzhv73QvdZ51C23d6qzowI9HtmXcDMsPDtaW+04YgU\r\napI2FXJWL6IF4iMN1rHqt1CPBNzEEEqnOrdBE1miQuREStH2YHAVhQmwF2Fu\r\nDlUDY+BJHrIg7bnh1TlocuHIQ9XEZIrfZShJuEYs/cmdz/Xs0h7cfFCDn3OL\r\nCUOChoqXrg4D1mf4q0mpKK4LHi1dFcd8HdUC8daxhm4A9FHJQNI1QOhsV1G5\r\n8K33yt3O4kzXGJS2lSEiC6R+n22CVIfzAaI=\r\n=SOyf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.24": {"name": "react-resizable-panels", "version": "0.0.24", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "335dd1f44d4bf2df96c5970f306c71dcaf3f1b41", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.24.tgz", "fileCount": 21, "integrity": "sha512-FyTLrttxyp6ns9O9n0r6Bl0EypvOPtQjmWwWH7QoC47cU1A2y202aaOdV4hX9wpyZ7pkLH7riEFKPcZTdN5Few==", "signatures": [{"sig": "MEYCIQDHr50ilKQlw5b7thCEurM0UklLwIzM9mabLbkm6XBM8QIhAOjWmKleCBLw2qK69jjo8ge98HC94GG7dQ+sXOldAcM2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjr7rYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+Iw/+N2mftufZW48ErFYSW6KWHJ8yp2Kbd/S6XmspQv5YGcr3geCr\r\n5KTB2NkSckGdn7o9eZHAXrGcmgO1Zlw4DpA/lfAmXQP1jo2hDl2wz3lEkZDe\r\njDXJwvwaeeKP2Mlvnae7Z8BZEPYOr0v2h6mUdbOaQoguSgV+t5sDAL0jlKC1\r\nNTexEf9FAvbFcMF5TkE+QTG+CBTaaNKtJOFl3S4kBF3tTLgU0kHcZ5qWyPWT\r\nFZSyEnsXtGnMYHPveIYGf36QO6jFCPpR0nMumme/glW6rRFh3cTDo88EA42I\r\nkrM6mc4qj8kKU5JS16VWSIQNRNqWyP7I2FfY/uKflpsPP6y0MEn11xeqDFdl\r\n29eQVONDUpDACrXs7LNp4ZfkDoOZVx97Ttc46KLDN6lC6H9u+sTYANScX/k+\r\n5kexZNWlM6j0zg6cM5uVKlMqJeNZG0ABPv2ph6E2cKYMfezY3HUGZHzRdFEN\r\nFBuz3Qa91HfLLRgSGgOM4UfTND5CMrmCCLPhwxHcLM3jdXVZcpisH1391AZN\r\nH9M6aHaag/yVYxsPlQnhcyDd1Vh2M+25pJIu0jR7sGyzbvSb7rhwz/mT7oP6\r\nIDoSSZ0W09SvgeK+ZTuuux7wyvd6guu+VppxkdOebLDcNj15tg5P1s/g7bL7\r\nO37zBfDAoNXE2+rbZPzONPa9M6gDbmVY/DQ=\r\n=2T2Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.25": {"name": "react-resizable-panels", "version": "0.0.25", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "ad7ee7c6c4d3f337239e23525e9fc2afcfe34de7", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.25.tgz", "fileCount": 22, "integrity": "sha512-rKTRk4fqLtSo2YrpP1RjtsM3/eog4gpEetnDOQGknzXZd1eKr/D5tnM0JKYlCmxdmKXjBGOZu+CeU78wDPb61Q==", "signatures": [{"sig": "MEUCIQCZAPu1HxA6ZX9NYRR6JbxkhbACGcXPRkSJT5nsqFow6wIgZfX/Nz7dF0qKmZF7uMzZqAplepAIZq+9AGcdiol7XFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsHDpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMiA//aSL7JCY+katvGAuWghtxBuMIjJmbarQFqpR6v+Qai0Gy4Up+\r\nsvO6vBjzjiuSUhWp+vCCbwPbqsxsaWJugEGwAcPY9sEv2nwVJSam3bX3iWN8\r\n1oeTLQv+BdcUdz/dnSH8Gp/g60rZwv+N0ufwbAD302SUA9bFaXQQCWaag+RV\r\nwWKU9fTOK4ir1jC7NL6eU1dJehetrv3EtStu/4QzhAdfYnyrRh6aou9NqAWB\r\nRtB8b3OBwn0gQpyk48nTYEHTB6inSdDuKeKnxSccybzXrKnWjQVxasyXpjWI\r\nKF3Xgy8mDPUysWTbVrbPDerTJiQk2LCia4uEG5I4Kql7oJ3BdJkPBqYIAOiy\r\nhTTcT3VrgAcnHL5ErOvtIy67MJnhGp6mHGo/tww2RYKIjgdfevQMBc1Y3iLS\r\npc8oIzUjCV6MRvt8RA24SfsPAUy/vdTX7+cIgsOORf+gOFlD6pv+SK6UR4oI\r\nMs17q1eIdRja7Ho79haanIgaQ+dantOpoBUiQQNrTLYJCt3a4dgoqUQ06+H0\r\nae9kzxtdRh47S+BdxJSeHWrhM/1z5p8eEHKeM6rnAos1JBTPuXCnQfXaqNrd\r\nRnsYsm4lIocOkEATzDhkFdBOTgIQOfR6ddSGb25VTZ49JsQS6Dse40qV5fSV\r\new52K5h2EWMa8ThBsj5/40fYIjV0/OAOe10=\r\n=0jQM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.26": {"name": "react-resizable-panels", "version": "0.0.26", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "45c62304e9bb8f182fc03763259acc19aa6e8a18", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.26.tgz", "fileCount": 22, "integrity": "sha512-aQteQNPNW/voUA5sm+Zg4VoPiQp4C+vaLUOycMrblGqoGVklIo7Sgsbbtd19/u1aW/b1XouxYyUxQnEOKKxCLQ==", "signatures": [{"sig": "MEUCIE2peVxz99flVAQxTcGuDazS7guRBSX4NqgBxxOPOdzFAiEAqaa64HnBZQTqqD7D8G1U+3fDYkASKuT9BsrqZnZCLVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 221600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsHM7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEtw//boA53xxP8KJzfjoQhmmjxOwTCCRy4NvMyJE/khiJJLbb3aLf\r\nnIDqvcXbnv4lK1UC/Q7gdv9OBi6rgh1uCeEupLCUbOhKNwpJrXH/zNTAc7iN\r\nntT8KoFW1KdV3lj7hQWcs8PSTfYsvrD7eQLOk1UAYb4HSlTDsb31OzjAMp+N\r\nh2am9MeQUPyBO4Wh7XibgADg4PZEJvDXFmPkZqXjV8LPF+eMo9lvRvCEL05Z\r\nbzOvXj1J7b4eng51jNNvLfD2gqwOaJxYa8g+PEa2glpe4Lb8BgCNoEAlbsYm\r\ni03tUgZ4rzGCHLkY1DS0eK7/re+d9ML/dSkyD4bQlzrGvZuyC8HbB3Fj3vuB\r\n0uqeqRobJN6qOJpiYPq9gLhaPEVnUritfwE/fXpk2VzMUu9mVlnORUuf5x08\r\n9AHCd7wyaXRWO9JbMHoUlOKAn5bSYZs/u1BJeNaxdJBMCPY4DZ12C5NsT+jM\r\n+OMKNhq2nOi72VlHVJXcUsePpugmMXMAPifxpYMhe5Dh3f50OJg1TbLdt5fK\r\nGnQLw/06xfaxhMAH/rhgGY9dwJSPKigHzJefPeEmDO7V9fhhaDywxgyVFzjx\r\nExF+BhGoBMEPyzWEBrhjR/6T1m6+/IdwLobbKYEUf7gYwsmfcsbjgYT16k0E\r\nEIDkr+6QkfeGnipE7XwLt6mMRHR8P+sGV0k=\r\n=7s1C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.27": {"name": "react-resizable-panels", "version": "0.0.27", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "a3e84eb275d1e18612b62048eb5603ead4e2712c", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.27.tgz", "fileCount": 22, "integrity": "sha512-eSpCh3YG5iLDSaVR0bVo1xM2gCdQttyxmuocuhxt9m+ZeWncHbZJAMuZvuFp/dqlVh/FDXQ0p0qTbuTdYjOcDg==", "signatures": [{"sig": "MEYCIQCIFof7I3jh4qBUgTDxMO3xekmSB83nUyo5DkI19vEbCQIhAOW9vXMyAjzdZhFBiYwO+ZLzW5qm8gdZUg1fZRgsQBbc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 236923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsyUWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1bBAAlCUwn1X0kyomHpKKPNi+9NTUFAYKwrVGLDDwfzaP/n8lrQlx\r\nd9OSiRHBObVQIWtlqKe2uaaNtj4eRE27qohV4DKoeWk7A2oIcwSV07A6JK9o\r\nydrX4jkSq548WcKIvCrerNFjd9g37lSn/GpmZ8PTFZ9HO44T2OuDkM7iLgmx\r\nQbe2KJaVfOQslbeVi+HlXcRxW4NZhWygt0xSnMdnLFMhS5OfhJkNhTDXfNAs\r\na2hlea+M0pdqcAIyL9fO29xct+jiKSl/WRmeIu98OUWbU8qJS+BAeeWyDlCZ\r\n583Eve2Hy32qUtR9IrUHQbRmzDNOHAkKULBe2eD7KX0ZLlU5Wdj00RBnx2qG\r\n/NE1Uz2qoTVqU/nYwy3Vt5XX/dmWseQgSzJlnF2EKXYpzTfj800mpQAkLipa\r\n+/W5EyZtTQnTVfTauIJuItHGJiC8l0l/wMTzh/KLo06ngmvTsNUEY4L7iMJn\r\nvQwlM5KG/maaG9GSNFs80w6Ct3cNCwYwCYYMwGTgfObiQFsQgnMuAt5GCDMa\r\nU3JYvXVDkb68GnYEn0WIA9AT9W+c7TuqwwvRcMkqL3xc+JriYwFN9dOLM8ds\r\ngFuayWZC/wfTqvqFninenctQD3Fk4NdHyAX7P/74YXI8h+EOGKP0VajwRc1I\r\nmEqgeQGf7Lkz4uMZKg4WH/p5tMnBWsc2MzA=\r\n=D0IP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.28": {"name": "react-resizable-panels", "version": "0.0.28", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "b3617256ae412326ad9bb07dd31c0f4283a5e622", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.28.tgz", "fileCount": 23, "integrity": "sha512-0vAOtixGsll6nSek68HSwfU6/v4Rm261iGvI5/uldNeDzi+xY9yRNWnikYyblRTeMHYQOjO3G+breMGyHGHX0Q==", "signatures": [{"sig": "MEUCIQCFAahSsXy6fcZrBcAKBdk+N7juR96qh5tIgK7nJK6nqQIgKn1CubEbWOjZwZ5ZUwf5n154eKZIwuzwmJyyo/OCPBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjs55cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpabA//ZqCuPLCtIBEHo704Vum5f97k2vM6SvBzp3FT/DsjAfw85lyM\r\nShGB6tfpv8z0mCwPp65nhZwMJKUmllNjeF9jURa8OT/Uwn9gl4ITmtxWa8o+\r\nCpHVe3nOvTo4lnBvXyY+BStg/BudaDvxMl8byCh9k/EKAMLMRPRUySrwEple\r\ngwt9OSoRTE3u/h4KqhAUJ2wqtmCfseBkpANekhU4OdHZezsMqEAwa0y/saEG\r\nU7qGW8LOqhvtnuBZPR+iOectjXKCOjaix8b4IoETXyFk6JzfJpjISkAwrK70\r\n2avoytBH++Jpe9IWDuXbmJW3loHePwwWuCCeyNMaOukK/VXA+eMT7CRv1XKX\r\nkayw9uogOC4AUgzXW+Zilcck01BiA3POBBIGh0Xm6ym3la/8jxj/G7JRDAgJ\r\nh/CiojnwHiZECSimX4kYWSz2rXKL0tn1eBInqL6q8Q+MiwHLw6foTjVZIWdp\r\nMBYEwOKjdz4173nooM9UPTvlm0OaO7x4qW8wOXXgAftxF8qS3rFEz4JM8ykl\r\nveH5zqQ7aqb0NhKTv3lVRpGnHDRiVZM5YpKBGpzcdF/v+Yl82xFe0J7qZOAe\r\n4d5kJu9zOKys3kmhdTyCJS4KUqDhD6YUIgzFCZDCOmWncgqXkVqxOBx9dOhg\r\niLSl3iCLVHhPDLZyCajhThGLgEpdV8K90Tk=\r\n=nw60\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.29": {"name": "react-resizable-panels", "version": "0.0.29", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "284a5b55119c3ccb0a9ea957fec8b907429125a1", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.29.tgz", "fileCount": 23, "integrity": "sha512-XWr6xH1LuL+5ztNoN+wXnY/S47tl04Q5b5jO1uuyyrH45C0X4z92/Lbo76o66yivWdPmf89i3r26wcx/I2nhGQ==", "signatures": [{"sig": "MEUCIQDQIdpNzdgf8YSMQwOHdmb30yRu3etU//8QaOo4JFNhIwIgcgcMKpMn04MaDt/XgfxDxbd4275ARqOTj19hBC34QaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 280212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtukeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv4w//ReLi4wpTnsuY3IZxPDW+YS5k5i0szgzXmrQ2rlOzAvEe8UAE\r\nzyRH3HgVbp3XG/7f+do6sSN4kYI2y2hPaIHm8WjZz3KfNWnz5gj2gyXTMP2X\r\nhO1ypFdBNaHnekUsfePxJW/pYxB5aG0QhMtVVWAetde+9XLgOxZ4dfDWRaYt\r\nmdG5QK1r3Nje+1qQJnSngl7AH4ZuQ5C/8ftgHJhEYc11U3CHF51QVzO2qwrb\r\nySC8XvMVwzBLW/jqacKhDK/ZZ0Oklx3VwV0vDyJZJEo/IJmEUhRTTTZOF7Nw\r\n4Iex66XKSef8fs6D6kDjr6Sfn2wts2CezIf8jxx3SB3auDd8cGDtJafYjgcp\r\n8OCAw5A0LqYYgaMdpSnQmool1diu13gt7aDCgEFmw8XGw645e6eXR/bqRxsw\r\nRSTNCeVk+x11oNhi9ApxEzBuwa+z5mxufxq0H4K99a9ClzpTmLKbUrKSEeXx\r\n96FoggZgmW5B5b58/xpEaWZ81ieBZjsLKhzZ5QH64kzaYA3NqDgQab8waCDY\r\n/Xgp5zSfnKkJklGi+dxRRfuE/NykTEuXOvPYsvvs7915hI8A4Ca7KLyBxg/c\r\nFqIJenlNfsv8deWgQpHz4oqCanLh4dMSUxDqTNtVEHf6nRv+nMGIUPNvlf1w\r\n+aD+uGZLh5FpRMrGmDQmlabusRXKNS1wnjQ=\r\n=FAyo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.30": {"name": "react-resizable-panels", "version": "0.0.30", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "6f8fa642777f98ecb36e2de6f200edb96609b8c9", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.30.tgz", "fileCount": 24, "integrity": "sha512-VvyPqYJ/+pDkWLUf+XdLlvSyV4p1NFVXB3PP7oZ9l5bUOE6Voe0nADzIKOuSdeTvQ15Fnsv7AKGmRA8WJw+AuA==", "signatures": [{"sig": "MEUCIA+seNgNo0bcfrulj8XtGBR/g2YP30eh5RGJyrdia2+mAiEAynZ4f6ofllTO839kcqr1I2AAGz0iP8VQDTKtS30Ho04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuDaqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaKQ/9EWNoCZ4OpHl2HJ6ZdrgyR+vaJljzob53MWqw1NhxoE4/BIkU\r\nBdiaFUm3j3aCHJgzYwoB7UNcyBNY6uJXlX8WCWqJTW+iWc/mjYXzqs/CkaUs\r\n9it/ejEad8HRtH/YrqDj2ee8jacrAkHKfbH3nVyCC1btJP+KNB7Z+PpB2KmJ\r\nTWqVbOR5XhkbiP3nkVshGH46U2G0SC44J7YjVRvK/stQbuK7OHzDAfoBG5kX\r\ncbWiRTvjzgr21TtGTJHEa0hct8PcSsAQy9UNh5ls5pMS8w1nFnAM4YvZsGlV\r\nx9Ml3pNpqFp0SvRoKRhoxMCGwthC78ec/7gOpb8YXJ4UGmbRBr0Ccs6ISF9J\r\nPi/fCahA5l9v2cfKWjTTkxi4Q77dqo52SPZHYPLkDFddPEyJvylVto62Dhlr\r\nHafkMVWoNKg6l0x63+sZIiKCtlR7Cjg3K1Lft/U8zdRarI0Uhqg1302dYvL/\r\no+XgEMzIdofhskyepwDKUH2Ey2aExpUtRJzlC8Nx7eXeBtxGCA+DkoQkIZzA\r\n8KongB4IFMs9HvToenK7dvS7V9iSf5xzPGkLqn+P9E6tzUvjMwdkxJqUkf0J\r\n/T2vqkyPIVHze7luSHQCzweCzAVCPE04329cI4wb6OKyFb+q2kcDYlOyWqRr\r\nUU2lYCCFDdySpuULej4Mee035BuYwgJFiV0=\r\n=loRv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.31": {"name": "react-resizable-panels", "version": "0.0.31", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "4539c8c29d704a4ca4d2776f375a8f73ea520e0f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.31.tgz", "fileCount": 24, "integrity": "sha512-AQpp4g0IzBJcBRvPM214Zn354QoV0nOeqvsIXQvTkIWlzkajbGuVkykwxDTOgkQy0IdcxnIrus3LYf7OVUqBDQ==", "signatures": [{"sig": "MEUCIQCIplLB5WWVcm+Sylfle6ZIaH9VQXmRppY/qSFTbFyHSAIgAWd5BqukD4aEuZq4KFjSfKwZs2dChG6zk2R2SoX93Cc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvMryACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbDw/8DwKDQNvktGF9c6Ch+ErH1zkKS55UMLBc0TPg44vpc91NRgMX\r\nXk59/LSG1eYRLT24ux87vycgMLJs71UzgOXtHSId+AbVUzu3r6tJxee+kYaz\r\nVJDOe2XqVuFdcZ1a3SKExiov9FrZzqlGAQozbU7KwWxtr1DGw0XUubmNcK1f\r\nXRgaF0zC0gSi03hRVfECcoxOjoBaz8SrBlkxLG3RrgALjUY4OM/ojIPW38hY\r\nwEoBaa2v1z0ci49LPotwRSp22U9fbiq1AeAYNMwF+2GDb6hnKTPwDRK6hpcM\r\ner8aLp8WwXQrY8kkliM5Pl7ALsV1WuzulfHZhcMI65qE5KEKIrAFdxNmiA+Y\r\nT3MxmXhI3eg7iqySNQEVKTqJ3S2bX8fUzpIVmgOv4nacPLYQbBB0QnfL98FD\r\nuV9Z7jFatcY1G2kk+6a8e8FUmzSS+46qhEg/brEk6cfsexJlbyp98oWJ/YhZ\r\ngIYuFiAkGGJZHnuSdPRH69xr5ARts32D1EP2GQWyuZ9JOYcyHXMEXkzmfr92\r\nJaLflwLl32JUW2xlsBT0fcOQDD2nBQYgh9D4SzmyzmCgTYf86z6JikRC16QH\r\nqAhZcBZbGYSxpKLhWuYSvzP7zYjpW8EoG7f3ATCmFb0qD//84zDf73eaJFru\r\n5j5/RmvWA8zDeiQkS7xdywVGwsbzRdE+4tw=\r\n=rCzZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.32": {"name": "react-resizable-panels", "version": "0.0.32", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d86127ad2c22852a05e2035c16a545307b459190", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.32.tgz", "fileCount": 24, "integrity": "sha512-rjL+RoEubWrdzj5DAPNnx9gpnkug1RRW7RlBW2rEV7Gqr/vJBFIKUC3ckGdRu/7mPjOXaNTy21kSjwNQQFWv1Q==", "signatures": [{"sig": "MEYCIQCJVM7yA2BdaadNmDuLqUg3yM0kg6oW7yKugpmrSeh1oQIhAPv8ujUJm9UQfQNcSycOHHR1mgYp3HtVwgu9gzaDu+Hg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvbvoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrSw//fWEYq7UE9QQwOYo9wABiZ6QuP65hAPqGEeax4xZSajw1aTDz\r\nUniFQFq0NC8kyaEOtr4jFuG35yxKvO4af77enYNI2FEd4rvD8Y3vfJpi+I64\r\nwnNKGX1SDgjBU7AqCf57XplVcmAsVfRAaKe6A144/IeN6U9WYNyg+065B1Fz\r\nRLoELbYS9LixrRvBysJXTNyx2/IDVpm4L1WlTUOPcbqdwgn6bASqTtrZCNiN\r\nobWEUdUUSJ6njHXMnc58RlZY6MMycMgR41rlMIpXl2RKtcw1540yjUaEHD+y\r\nbkxxVTpDNlcGf3vL/206eaWbE6fuFS3POSg9zfzR9AzXjGAl549nbvmAHkwd\r\n01u5viafwoE72ZDOlc4MbO2uWXoR3VYaXmktU/m7Vk5OGdASZyaUVHNXQp19\r\n0f8Jy743P14Dbq48ySQS08PMRtp08CEEzlr8PrLpeBvvIgCiFjoK14VOPOOy\r\n/9oz4AnQ1XzESdsMGogaCD6rCjO4lx0jsJjS/9VnuZgl59F43+onwDRLn2OW\r\nZFalnfm431XmQIckGUUnKcPmZYoynFuLHC1/cuqhVjo3w6sBxUy4smDN5xOn\r\nti2DaRkCy6dqMBw7/d2VojLuxG83ZBWkdfQxKMLq32Zsy+ONNj0ARt13GLJH\r\nXkTJu3F999YAE4f4aQNWI9EXa6nnxFnVwa8=\r\n=7Xxh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.33": {"name": "react-resizable-panels", "version": "0.0.33", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "7bf1419c7d0201b55ccf1aa367fedc8e902cb9b7", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.33.tgz", "fileCount": 24, "integrity": "sha512-j1tALhVb/HqCMTGzCA7Kf5uLj3cVCmWvFWEOFW90X22qGWajZ+FXCISevaPOFzFHaEpn2AF1LB+xApOFSdQv4w==", "signatures": [{"sig": "MEQCIFDxFnrlYMwm8TARn+bYgRrHLuWm2h+0KyLAZ+unMN4iAiBNlLJDeEfyfRAnhUlLXInXdfOmEw7s0IcAgFLtnB31Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 306511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjwBE7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBuw/+PpuKBSOFXWrZe6fDj68pf3T3chqR7lhpNWskzn3mRnnO0viY\r\nVLM+9CQXP/8gzH9bt9XzI3A0GdzBU3prKqmRoU85kYHSb8vWvphuvfifTLT0\r\nMo1MG6Yuf3QyMoMu3IjZkUqFtMj+bSRj7aDqyYl1P5cWE3GHPADSdyFck86V\r\nLuOTUWf8//SkoAO4etNFXOHOlAtoLownTZcewLSGeroIUPyN6nB370WbtvNj\r\nf1SK04ZH5GF8ldkHBqezyKy0Tc1fAxLYlQEVB70nwQSVMSQMhYFaHocTTjiD\r\npkxXtxsyWKfwUSfJCmZ52LvbA9z3FNq4G13ACiwzsWJ7iEJT8czyjAME8WId\r\noaddWIby/kDwSCYB1Q9mgvg8uBkcYg/u2GiqJMwo1177SJF4EFv95oIm3JdI\r\niU4rw4fR2wIPBDO0drIqNawhsmYPFsQFZPtynawQMqW5ns67cKo0Zy2n0Mkf\r\nUVIxhzfBkfiCu/B2zJTKAx4osW59TMGeyOUEDhh0csq4EDyRIULbo3f3I/8W\r\n9Ksj8NydLEhxkpqCF76cpoy3JNlvUHO/VMiPBHaysYYwP64dcZU0u719mvwl\r\nw9BHQGfyUb+FUdJfyBcjl5NhFr5Y6HteE5c5ZCXwazWrOMS01CaIeJFw3V+c\r\nc9Ckirzdks08cchgLxEurW5/KcrBwM+G89s=\r\n=KAwJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.34": {"name": "react-resizable-panels", "version": "0.0.34", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "a90918b24d7c53fe111cfa721d12f313371e34ef", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.34.tgz", "fileCount": 25, "integrity": "sha512-GGT69jbCiK5Fmw7p9mopb+quX63g+OA235bSHtj8TD3O+wsFNgrg9j5TaRI6auP1J10SBmR0OpJ7tX3K7MFxeg==", "signatures": [{"sig": "MEUCIQDi4FLSkfrPDAlHRnMNijirRDJdYqL7iLOyAsIcz+RsCAIgEHLfhoagWX/tSyUeE3ZjTOvo9TrAq4OOairzP7pZRVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxwPFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlKBAAnW0YNo0L8MpuUA8AXjtSUal9QdM2j8o7k9McNKlPf53rexMZ\r\nYV9mqmuAW5+Nqhwyk+3lpmNP3scRt2Sj64LlzsgHGJDVBW8SCmRhlRVYoY6E\r\neUWsfAEPVzwVNBHCyY5w4ETzNz6MhHtVDgQ/F1mLEQPe6ze/e34OkXUzGzNM\r\nb92xOlI5KO8v6Gw/5W7Y1dYvjOX6B2J10iDtAEi99s559vK6+uBE8hz75CTd\r\nw7S1DpMQdg2PPk6Hb1qonXXPKfKANrupWh7+P2qbsHOeyoqY1LoAQqKRt2WH\r\n55TatNTZzzaaKXEDEE6YuiT71Gbnfz20qm6iW5GHIgllFhxNTJjASXrAvkAF\r\nm6I+vQyAfMJU86Gb0dy+8YJxIHdKhBGf1yvT9UjYznyxb0OhNtozKBOK2y3a\r\n9G8vCnTXSnNt4j481QCMCREDAZs/K7WKfcENkS31knYxdyYFOC6QYV/m1DeU\r\n+wS8loI0TbKr3ixX+fXj6h8Ak1miKJkB03/O5PueaXuJyNo7R/IEfsDqasWO\r\nteD4DzM5l7xxlENPNGWJNPcFy3FsyVpP0et5xj96Rb/q3hD6iiYPgDoPKyI7\r\n0OzM12mBul3mOUSRkDZjimlGCkcNj0repioZ1EYwFYcYql+4IVje/zOTkmxL\r\nSWV6c5Yj5/YxMLusdr1iscDokky65hiT2yY=\r\n=QNl+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.35": {"name": "react-resizable-panels", "version": "0.0.35", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "ac8c8918c957bbd5c37b4b93ca50a40b5c012e6a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.35.tgz", "fileCount": 25, "integrity": "sha512-8HFm5w4AG13aUN2+JWM/3unYP+QVmW0xs9iWM7Sa+HvIGCQhdvLEEx3lFmUyEiWPYmsBnPZaq6o/4pAi2dt4yQ==", "signatures": [{"sig": "MEQCIDR8ccdhJoaSg3NC7iO+riYGQ/OjnK5dKIypEHkVmAWrAiBg/wOd1Kt0MT2XEaP0NO9AEXRXVaIjZ5UVxJifjgoj4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyC3RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxjA/5AFIFxKoBPhBsGsKGnG3K2FBbWz0lwawGXg2suLw80SvBaKfJ\r\nK/2ak7LeeqT3fIro6/RjcjE3vn0y5q/IRR5HXFf7bZNCoAU1LeWjOzA3HLcl\r\ncUXJp4MSGAexTBXxIgCCMer/o7+h8dT6VmE9/eDZ4jV8Yv3JLca7ZRv2zuFv\r\n++nVKDE+kTN3t7D7w8qIEFliU9vwCeTK8EwdRtcsFo58yKDsyxlq/s1nSbmF\r\n73XsyRnP0PHTXJrJpybS6bAViGtbII5nKbZwIP8IsOkVgP9SRAUh+LLn7dtj\r\n+E5JaV3uq2yN4Sy/O2kvNHiNJQl3xfOt6tw9e7FjKnT3NGbWt8e1yj0LFBFt\r\n/FGxqhwp0C1AG/wJKji6qT3XsPDopOrNt9Fyp6nxIkurOlCUaUE9mBi6+VSp\r\npyCsi5FHTDH7Rn+pHIof5f+zDs+jx5vtB6GWtlFri0CocwUgXUHYerVeIZEP\r\n7n21OG7wYXWWhPVxf3L59mwRFyLQL0PvzbXK2JmFkK0QbgIPI+4O5vCphtjA\r\nNtuEbiQ5eNnRc8k4hUGc+4KrloHjywCNk1JXEYstx9dAH9xmYjNs4KqHUSvl\r\nXzCcAQmRvtWNoH6XoZ2nY1UnK5MPo7X993nihtH/VwAsrEsqWiGzC13YTpgG\r\nOAB4O008n5vaYkrbquZ867T6RQJ92kGBrCg=\r\n=qnil\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.36": {"name": "react-resizable-panels", "version": "0.0.36", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "2747695d60969ec3bba68bf7cc42dc4a524d77bb", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.36.tgz", "fileCount": 25, "integrity": "sha512-aDGw+DTe/RwsUsdk0Z+xQ/Iz81CZRxTayg3WQ2d5PVAV/v9rcYikck75NpYnRuY3oOOu7+yl8H0Fj24Hk7ymxA==", "signatures": [{"sig": "MEYCIQCTd6+JtSVE/H0F3KgPkMEBwDkjuKtU9ONcxShViYUspgIhAJr0OKYdXi0/aaUaychS+77tm1lJPizfrWg+2912Jveh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335296, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2ShsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo48Q/+OvnWM5C/0/G0Q79XRXjB/57MN3oAoLTIRgB9uVI1bDMBf2nR\r\niLftsVZx2y+D7qW2eqHFZF/qux1WkIHzR3566CI8Prk68LSyAY+NnB3vIQwg\r\n7L2OhhDLNZhC20CkqO1QDIc0hkm+f9kybS8oeMm2kbGOaXCFIeps7Aqgp/NO\r\nLNRmz985mbXYLtobysUxh3sjyRmBFTqP7Xce3yLTDKcV9pyZqarxorsT8I1+\r\nt5S2Ou0kk7+iry3E3cs62VKdjdxbVyrrTl3m88F7oFQdSGKkGrY6Hy393TLy\r\nxGp//p66PrQxirylCW6ojcNuNh0lSO592RXPZn8hOPbLIFSuENICu/W5U7eI\r\n6wrNR265d+m1ztGH4iD0QmyDM3pquhMMqfr45C8A1hzN2/V2OVUxwsmbL1bR\r\nozNo/btjTvD/6zTvxVKgw7HzLbOedezy5FuDJxOtPRKQEJAiJJeT1+xzp6VJ\r\nZsVNFVhYshJhoy5epxLHUyTrFsrvuEhbq70ZnL9AJ/liAjZaa8+R1zuI1UFa\r\ns7VkjlqWaI/epkL4bp9UjIZjGYhsHulHj8JNNkf3m705CEBLrDvP1Yi6y3T5\r\nSX4ccNsGMY+LKejuJ+qOuot8XmQql1P5cyXVBd+H/ytfd3RB/oYKQ3lQi2Ek\r\nfYnjSxpdTYd5dOwZhUHkSX7nl/URAPnlI24=\r\n=RLWm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.37": {"name": "react-resizable-panels", "version": "0.0.37", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d2d5d98fb91802447c35318d3e4061c287b1ba95", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.37.tgz", "fileCount": 25, "integrity": "sha512-8aTW4xyd/TZfsiDi84OUUJvbepjcC46I/RZyveXsauL12IzyygsxhfY60YghKKhmeglQ6OJq41LKIlgMTntZOQ==", "signatures": [{"sig": "MEQCIHk+wH+FKdNDlgjA8xKW4qX55bsuYMt/uyxeQThf4uZfAiBS3U/eB4GLGIofGdEtak26qAyzv9jK12dusw11bII8dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFfJyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp42w//ULSnTwRqhcptH4K9R5r7bcUJfs2UfMYRaXf27vJDck9Zz/om\r\n2aPc5XO0wsrNwUKeBm3+kka5dVNQ4IxkI2XkZkmI3a0Fwj9oWz5MAvq5lweu\r\nmEC6uJUAE4zZlq++OVEWX/uIwpaXmWAWUT1cm7nJ5VSNByrtVbEMpKO3H95c\r\nQXdGWnCKwQDLyNf3F0qOHex+ZCKlt/gNh6rLyP/95SUAULi6/QOW6Hf1o38i\r\np+rdnn/pwm3UImzOrDXmw9oHPJGE52MsoGkFD/EIX8XZ0UkWy8UReNbJcgrK\r\naMylHCDbzYhlDPi1wp7gf4JqtQa6dXvipgpRt2tT/OGvDYURG2QO88ODfV3O\r\nY4AiPDArg0LctfT5gnARcQ1amM1GCtdExn6xxpnHD/T1bfUXuta2Fo9gHPOi\r\nxx+nggK1HqknafLjKZIlUQTZ7BD4+FlLtCYMkD/1UngxWEySnrxW1XwnxX+z\r\nPqtiuu+nDmLWOPeVgcFhQ4PmtjGjES3ph2cvBiB5mXkNfQVJc6lccPb3oZHO\r\ntPyG2uOunD8P513QfaEHFqdJLFW7ZyY5IrV4G6prbF4SbKzj8YapDOytlrn4\r\ngj0sNte9/NWObH/rT1lERkuvX1I5d9mDlo1CTTV3xArl5BlPkc7nDVlSaoVS\r\nKzIG9Mzmus6UXv5suGKRo9dI62j0Ubl6xK4=\r\n=pSxA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.38": {"name": "react-resizable-panels", "version": "0.0.38", "devDependencies": {"react": "latest", "react-dom": "latest"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "490dc2de7649e6a5ab97eaffa4d78320dc7ed459", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.38.tgz", "fileCount": 25, "integrity": "sha512-MSzVM3KmzPszIBahIxugKlfnKxdQv8TOv8msPiehu8Zob/I6CCrMXIw8GDJGmByqUF9x5fz7WIm2KEP3Ip5H2g==", "signatures": [{"sig": "MEQCIBEzf9KVEMMj2b9pAH8d1GHL/ytkKLNHLHnW+Na3gBWKAiBUNM8FvERyWIaX0rE0M698LCq/0PHGXta4dvWTjOERew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 345440, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKPmJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3mRAAi4RR6xI4u8s6mlU/Gn1XGUFhb5fGfbwA1HD04qByqLrWkWDQ\r\nNCJWosAEzyXZqAr09HNw3n8h7LVUfzXYI4wyhzgDYMWSkZsj2kUnXPoUMnHT\r\nxlDzoUHT/JCfuhHj2d7aCuYz678IjXYaN21VlL6s4rJYoHH4D9mQXwxQPJd9\r\n8oRvmYO1KceCkpft1KuKoV3botZJ2Bx4afHiQSacXK4Pi0qB6rTk7uE6m85i\r\n76EhgRQH2PaG2j9mjZpeSBVLa+yExApv5c/dgMhQ8Yn/SKfVOteAI02EVfOx\r\nlAkoxIr+dyolib3A1820PqvsYL0BFglBifszeCaND+e2UkFl1vHaAnY8CsxX\r\nO+3EykO4ykfi8CTXaFV73s7Hawab3A4OkcrYIyXSC8ovTF0tS+/+GWU8fHw2\r\nzN6q0yER/Z+IrP5uwVGqf/JzdOtINtIwvwNW5sT+Nhlo5I7ukYNCi0NAsKIe\r\n4rbTy/6zSEueEpTHrA3CvJVdRy0XT7A1FE2j/Cq5jsBsfNdgmULrNn8BySHF\r\nJZ+VRkYvH9KfJKEE5RvtRNKenckbdiUHETEztVnCoy1gu41+PLw+GqHg9JPe\r\ndXh01rreh0EYtZN0sKYXzUkKynGDoQUQ8EJ0GL5IA6wP11XSUUjIFUYu7O6J\r\nxuDro2Szr2VAXSfEJFZz7kyVs86DE0qvzfo=\r\n=YMQW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.39": {"name": "react-resizable-panels", "version": "0.0.39", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d144af1792798ac0c8ad9f08c036e719ca5a9f1a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.39.tgz", "fileCount": 27, "integrity": "sha512-qQDZtC9a+CxW8gPQ+E1aTK9j8dvKigRiBYcoWqZWkJlulsgPZda+GjWk4SXZHZzsCNJmyVjEVW2QaM850rNRUQ==", "signatures": [{"sig": "MEUCIAPXvi4xBPpyn64bkAstmf/Tb6AEpCNy0e8Dp8Bmzx1wAiEA33W9rWf9FKuyJlIaJBlJbcx9R16C2E9ZBfsFlDY2rmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 356117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkKY7ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcAw//We/wWot92Lfa9jWtfcLvkGE8x6PDw/laNkVIwMhyLjeaNfSV\r\nB1gK1Hi8mu/JIa2qejnlb+AT+bA5EB2I0ssGa8ml54Lf9hPaUNq7TnBt4pvX\r\n/RdWV5+zA2Js+1/hoqf+qb6ipTAgdeh+VdrzjTvYBGjY+C/FD9vieR+eOCky\r\nLJ86ViU9xZ3vXOggmsZTpkhVp/bcwr/AZK5GuB080bLX3g4brVMk9zvHxNOm\r\nMu5PjcbgkzulH7dqWn34vmW7qoSeawFFm+C8adoURFZMSQ0VK1MIUsp3Lm+r\r\nu5FkhgciR1ZTc/3Zy/zSkC/rzUhY+ItAjQQYn1W2DEqcy7OTDi+Q0Pb7btsQ\r\nrx+a5afXN9/TPFB0WB9YxnapIC6cdnzPJKSJZPidDKvTRfNuGgRr3oENQLPx\r\nTLTg2rSKaQQCcITa6T6lAOoVK9vFZfxppF4BhhKxPXrGFbWVamsrE6N11d90\r\nxcP9PIO2K+cxhfqgHlbY69udWqcavoX0pq/f8r+sQKmtGcAlTKHWkBPIqcys\r\nS2C0bQoxeiT35GQIwU5RHFHmK13ax71IemQGYf9jY15FAXGBlZ0E5MiycAn+\r\nnODLd1eHETaxL7QBmp364g5l0fnSr+e0N8G2nh3YkxQHBz+65sU43AcgKZyD\r\nnmM2qx57QmEGXroQnbucLDerw1mEI69b/GE=\r\n=Omge\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.40": {"name": "react-resizable-panels", "version": "0.0.40", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "2696c9339ffbcd6e4eb30fe42cd4a1dcb05b0047", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.40.tgz", "fileCount": 28, "integrity": "sha512-Sfc1O6yjkIBsfWET1mxTX1Jshql1VYtiO8LZsJzfbnM9zD90r+HHyHkxM2++nd1JKIf+A+nKDwEG1GqODqCU8g==", "signatures": [{"sig": "MEUCIHrHpkq8cLhUpf6f2sWbzb3/BYaB326GpxTOpAn1wjgdAiEAkM86URpjggD4syvBORLBhOA817MiCW0bTBwQHtbHJKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360225, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPZoFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhIhAAnI8A0uH2tYhleGWqWHIEU+iK4kuKplN/C1DQnqyKN7HlBijd\r\nh27Qxr+dH9IJacAdyhzumC5KZkYQrtM8jjGj4i+zMQbUGa9jvpReQ3xJIJfI\r\nZo5ckpiEQCsvJ6GOLJ5ljoHft2O1IA4gueaXzSkMVTotfskyqRoDZikIxLNf\r\n7Tj76DSf9oxmm1p1e1t0shtXfq7r8kFDwZTyfhlZAAoFqYpfPlOps94Nkfem\r\nRrhmSGdm1inRv9tHcpED/+7MkacQ1mRaR0jaUAC5ZhO91d4GTJhvYAUSYtnD\r\nC676k43BAOXK2oPCr7+zOyQdCFa/ZHN6ceqFGRW3GEJmwiR9gxsGu/FxpG0S\r\nXyx1agIEnNBwy0Z7USrEdP5kXVYTn4VD8xjvEoxXt41vjK9mc0SAONV0+Mj7\r\n8L5KrgmCKypOaHywoK+FrEtxac0WPLmNNFZq5N7+HA+3DnnOb/ZOfczmelnn\r\nVTIeMsIUiR7g/cx+7s8W9hG2S76Ilz1HfW3X+kfeLYNd+M02g2l4/Na2dK3s\r\ncQyiGnLXky8Q2h291WZhaVLfosGfRzb+c9MEePbhegOcwoANLH9aAYlMnPh0\r\ng/0Z73L6AEfEX3kQopk6B7+uYrbuu6Dd1T4pCmq9w4RaVwcfJJ6nxIS4PWAX\r\n1Rs79j6pCSYgzhiE3yHdfUQ061x9eY7k2J4=\r\n=IF9U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.41": {"name": "react-resizable-panels", "version": "0.0.41", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "89727117528eafe221bf279d5b4e86975ac2710a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.41.tgz", "fileCount": 28, "integrity": "sha512-pRRpXvYLSACD6evDfz+CLfG1GY/WVPlnwf1sKMjJBZwF1QxrjOjyAFUTcaT+94K5myettFk6qL8PUSYprayzLw==", "signatures": [{"sig": "MEUCIQCPW6IdJqbHTXT4v8DN2pfxUtRsY4dL0HqDe1N6gyTgswIgZ7OvZ5DD2x9pBgtsw4GB4+rQNRYuoZ3yVBoMUqURMDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRFpkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe7Q//SPGzRwV+fLYGczhqKFGKWO8htqcG5xWjbfMFipUmJwjVjT6G\r\ncNeH8x4mVU9+y8xrguy6V2AgMgmLazY5gKOwfp2OUWHWKqNcc4QvxoknDFXi\r\nJ0SWDz+LN9ZYc7MFjlTOvEdU/rE4/b0gg1zcJU9mkmduc5QIZ1z8rNa2h6Ww\r\ndC912ipYmWpUIwrxmioCeiX0irDdzN1Bro472sXasmBFWkTrG6H5NmGVxmdS\r\nd00a8df16OmkRyZSp+trlHhbHdMkyz66QR0Q65oiw3QrFaPmdFuE7aWOfkWA\r\nOjYSjV4eC9nlhpLy7Rjrbb32tVMMjQtGmWXHshKN28XBrL7Kc+pM8IqBrGNe\r\nj2zpeXMPx0Nrg/E7AwqgTcEMo1T/u54C91RRweX4uRRwWjM9icm+uZ8B0QWt\r\nvk0dDMGYh9Ytyc+z2BH4AM/SpJpDDA2oNIAluKUzG7o/t7TZD5zRIEjiyVqQ\r\novW4GmBZMZqja+iG2vjdBrM5DdmExj1OZ3X+7Z3ZxTmuZXtSLm/jeq+adI8N\r\nFv+ljZhVw3hHfEvwShvcFS1/PsLp22Wc92CivIJr3fUUqCctwI15ItM9/Joy\r\nh8TY7tIXyhJg9pn1JIN5zdjjE1tYz/gDcekdaWb24ypRN5EfPHIHhO+3Bs8v\r\neMt1t4+vHQohOfONDayprHXMF6kemqhtMOY=\r\n=GN41\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.42": {"name": "react-resizable-panels", "version": "0.0.42", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "e1a5d7fde7be4d18f32d0e021a0b4edb28b9edfe", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.42.tgz", "fileCount": 28, "integrity": "sha512-nOaN9DeUTsmKjN3MFGaLd35kngGyO29SHRLdBRafYR1SV2F/LbWbpVUKVPwL2fBBTnQe2/rqOQwT4k+3cKeK+w==", "signatures": [{"sig": "MEQCICEziwexF5hGkD0Cay39LhD8r4IhsZms1cwkizLfxTOFAiBAxzkXh35SUKwO1MLL5FsC3ZQJhSFTbFWAA4O8fqWz5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 364213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSWH5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrybA//WMeWzzSyNJSWfpEEd3xaKsaQUi70v8VQompBIOVOKY1evAE7\r\n6vi9qUIb1EwyJuG0YRv55iiV+YdPSCxgAXGadaCZzWp2XSWJ7yPIlQMR36UY\r\n/btbwqhvB5z5tlMOHGOOT5RkV/RfVpA+lgM6QcWtg5JjKi0b1/FZO4WAroLA\r\nwUfRxhf3zeoRbeqGXQhdiE1gR8G+IOxrzgyZrqYeFAA8by4KD1Bupzw+dehf\r\nvCbaYRsmpkkph5mKbTNjAvp+YJ21cGTRgDJ8EzKl4/jKF3LoBnvga0F9vS1/\r\nDjCy6eLxDeS2cvz255t+UYmcGrnkCSKekIeBszVG9KQyNSBJbhm3wyTqNoj8\r\n085vHBycyeup2Kputyr1dqaR8nCsRNAtmHoyYKxowN3hFQ/w2ofiq2NurDlu\r\nCQP8X8Ad982rQUKOS5kBL49Fzxb5qJWimo/S7l1uH3FrIrc9pL3NZUSWZYp0\r\nuNvEqEZoEVL97Am6JT9bGJK0OC1RIUVvErnHVXDJ98SkKpKQJ41zQfijaWCr\r\n6e+bdZZMCkoz60xqBaS3AVpK9c0fSOKGXh8sKtAOP2DxYUFj/3hemKpiOcyQ\r\nce6W56wgtqIyRcDUZ+DQ8OkNmugXJ8mZaNa2vyZQaki9EYPgbv6wW9ZWxuGA\r\npFrw92q3I9W8T6qRcIVXakTPvy0AYocYSQA=\r\n=CQlk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.43": {"name": "react-resizable-panels", "version": "0.0.43", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "1b019e0e9d2e5d030fbe95ec273756bbc896fab7", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.43.tgz", "fileCount": 28, "integrity": "sha512-AUihA+8aaeavHDy3rob+asPXAiDRYEVns6Bi6a9Wyb3s/HzVNnCvRUcHLQjoD96Y3e8ITX2Dp4wMUgZULbFOUQ==", "signatures": [{"sig": "MEYCIQCcAKdG9NR0Pj4yIlAgHLrfcvDSLhD0T6dTN/wRFxF9pgIhALJ1QZboyVprGgg+Lr5KmJSOhjAzBJ/sUEhMPCnLuCSH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366772}}, "0.0.44": {"name": "react-resizable-panels", "version": "0.0.44", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "20544396a0dafe185415c817042e5dfc3677c69b", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.44.tgz", "fileCount": 28, "integrity": "sha512-TU2PRlI6DAFP+Pw2gtWJKgohhLwg/pPSAJoaUzKM0nrBFj/SlEjRD7dwCpUUR0dfY+J+4wi2IplVAyALYOJ4cA==", "signatures": [{"sig": "MEUCIHX5k8Na9y/PVbs4rdT391huRLkTpVOgUWlhVReFqNmYAiEAnY3ubIMIzhZgA22MKutrawDIln7nVIkRGm2qVFuMOlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372664}}, "0.0.45": {"name": "react-resizable-panels", "version": "0.0.45", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "4dc67df5cf09b1dd8c549224d44bde5287973366", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.45.tgz", "fileCount": 29, "integrity": "sha512-ZgeW43qQemzRDLPnVc75l0U49r6u4I1WAfon2M9e6858atJCimXANsj6xxdEBXUd+bWptIRQ0JjXL3Q625cDnA==", "signatures": [{"sig": "MEUCIQCNko8lNrOfz+XFvHgOug8oJFbWB4a+5qQ49FqB2n2cwgIgB0EuW/MmjTs2U8CW+N3EszfmHBFxmlwRxfQpS2QnCwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 376938}}, "0.0.46": {"name": "react-resizable-panels", "version": "0.0.46", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "a0465e644aff8e7ffeae2c3cd1d0631f416de410", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.46.tgz", "fileCount": 29, "integrity": "sha512-lPCJ0ONb380XipeOkOLxxTjVs+pTkZJhb6Sho7Vxj8ljrAqZSjZw88hDznDaSFANt06WpVIUIiK0K2Th2VKYOw==", "signatures": [{"sig": "MEUCIEznKx3OkjdE0qDMujKUXli5QjpaBrokBZsWkmKYUukuAiEAvnC5AdVMtbdFY+41k9zyeTGh+ReKQNNbFNILVl90J58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 381840}}, "0.0.47": {"name": "react-resizable-panels", "version": "0.0.47", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "5ae3839a39b9b4d6d731b680fc7c98a22dffe830", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.47.tgz", "fileCount": 29, "integrity": "sha512-sa7VGkRzG0y12H3/lVdcMsxUJIMXwaeR/Ftc9Z6bPFuN/tVDDMt/1iktaxlaBUpz+EqokwThDqnMJRuQ0C+uHA==", "signatures": [{"sig": "MEQCIEbGFbUAbdq++jvRFltTAAf4aDUKbYUprzCDQM9kk6vSAiBYddgzF07vHgNPXe7sUNYCpsbQOIEoKDa6sq9yKU937w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 382589}}, "0.0.48": {"name": "react-resizable-panels", "version": "0.0.48", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "472a36f278f504d336fe36394d1177cde3602b83", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.48.tgz", "fileCount": 40, "integrity": "sha512-fJa3itmJ3HLLmVG7y8tka80wFW63N6ai76q7MGwU8nSXeA0qkX36vnmPyXm34lvtsGjn1Cgi5IPhPQnf42SVpA==", "signatures": [{"sig": "MEUCID2BC7rxddDMSMHGgrn3H7e40h36pTC+sMA2UjcmhK10AiEApNkTsmKCWLSGST+s2VwlSm/vynOd1Vl2bzThjYFwIC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271931}}, "0.0.49": {"name": "react-resizable-panels", "version": "0.0.49", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "643727ed510bab9e7c05542df51c90a898e85930", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.49.tgz", "fileCount": 40, "integrity": "sha512-QJc055mHsjgFKUA2s7ES1OR0ur2EEdRkg7lJawTb9kOEhCIsbuB2WyJDyw4myh4Jcs16lNdvhFcouYVnWRWOeQ==", "signatures": [{"sig": "MEUCIQDJqqPoSkh/aMNeGxKJ99rZEQqSlAdkhljWhnF315KZYAIgVdf3P3bqatJjeZAhbAannn0AgRFZLiAlscg30Ed/qIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271610}}, "0.0.50": {"name": "react-resizable-panels", "version": "0.0.50", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "16dcfdef7a16e4fc0fdbd669966c4f3118ebc76d", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.50.tgz", "fileCount": 40, "integrity": "sha512-AX8KKGBBT9lUWOxybvkjjQjOwp2Lm8/RTwCIsQawuH0StNXHT6BGJk6zh6a5/iUBvX9v50QT8tEpnhTPL1p7Cg==", "signatures": [{"sig": "MEQCIFojc6Gb1TzVEpiSKUUGkm6dIiTpt9dGIdkivRPNVPBnAiBW4zAWKTK5b9Keq7JNXKDoOJZVksIk/rl1/hxZqDKKvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 272207}}, "0.0.51": {"name": "react-resizable-panels", "version": "0.0.51", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "5059f0046a9eb41091aca0fa0716a26f2a885d49", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.51.tgz", "fileCount": 40, "integrity": "sha512-KeZYRS80x1DUGe7t5WloPAho8l1oeZQP6dSorbqkAG8i62l92oWnJEZE4re5xne35ky1G4ktkYxiSFfiU+Jy1Q==", "signatures": [{"sig": "MEQCIFxmdefU2Jg8DDALDCkNy+3FxJIdY+EezgcDLQqWejvEAiAvMjpWyE5R8fzw0fK8AOWTSiUvxS4BmQxuYgDgtFq46w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 277335}}, "0.0.52": {"name": "react-resizable-panels", "version": "0.0.52", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d8b1df75c9c13ebef327e6cdf1cffde7103617f6", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.52.tgz", "fileCount": 42, "integrity": "sha512-2vMvh7lEUCn19mqnmRLBd9BHvtub2zXCrf1UvzD8jLPVNvX9288PsF+vJwmdd7hXPMc9gdd7/CwFMfKvNnpUhQ==", "signatures": [{"sig": "MEUCIQC7wISKy/IF94aLdaFzSkHr/UzMXVJMjB7NoUGLL5AsiQIgZzuRLDnUND4KCC1QXYKOFbpsrL1BucWHkGcB0+oxBfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 462699}}, "0.0.53": {"name": "react-resizable-panels", "version": "0.0.53", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d169e45f33ccb38cdc077e0f99049385e89883ca", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.53.tgz", "fileCount": 40, "integrity": "sha512-lGOJF0Hh5+Y+Usi7x8btmBTi+6CQV1/RKxnj6jVrzvJ9vLbftbSoJPzymOuX8ZCFimlEwP2AKsGtQVKG/KieHA==", "signatures": [{"sig": "MEQCIAnrB5IsNykNUrGcbNgjyRV7TA3bv+XzFv9/6D3fjY2rAiBXZ8/05qMN5NMC57jIoAhUn3EHiz7/G7QeKnW2uDfRdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283254}}, "0.0.54": {"name": "react-resizable-panels", "version": "0.0.54", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "04e6a31b5da5d158b687838388cbcc1eb6f61a04", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.54.tgz", "fileCount": 55, "integrity": "sha512-f8hHdQrqvXoiZGdRNuoOi/C2cdYT2nEpaOb1KIWVWorSTPZmnE+ZQiamGeu+AMx3iZ/tqBtlAkBOpKXzTnDCoA==", "signatures": [{"sig": "MEUCIQDorp05wwp28mEWciNhMiHbozthcYtACTAp7Z3eKrph7QIge+KdM1Nm9ip5J4wEglwXSUnoEUpiluijVXZVdVPphtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 656716}}, "0.0.55": {"name": "react-resizable-panels", "version": "0.0.55", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "adf06d35ae09748ab7051a4bd2c5be8087ef1a66", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.55.tgz", "fileCount": 58, "integrity": "sha512-J/LTFzUEjJiqwSjVh8gjUXkQDA8MRPjARASfn++d2+KOgA+9UcRYUfE3QBJixer2vkk+ffQ4cq3QzWzzHgqYpQ==", "signatures": [{"sig": "MEUCIGlRcog4bZjsZo5VG8ok1jAVWX74hk5QBFRp167VDfAHAiEA89cCrYK6RN/TqmhrU6hdSZiuHeyyV3jhNloIY75g3rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 990171}}, "0.0.56": {"name": "react-resizable-panels", "version": "0.0.56", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "8656835331ad87e62498d0d0ddc598a712039346", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.56.tgz", "fileCount": 103, "integrity": "sha512-HqIseJVADm64uVR3Urac+zb1DMwFjWNc40Kvan8vN+8Zwi3VHgTRUy2gGc5X+RkrAN+YzJnRJa19JG/rQYn7uA==", "signatures": [{"sig": "MEQCIAjzQgnL2hw7DUDrGtCHPsDfJDQsfT0U6GfFAm+XG4utAiBKhNFvSAGqTwGhcguiuUFhmkJEWoRJXXYJ98IpH3DHAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1216354}, "deprecated": "Critical bug fixed in v0.0.61"}, "0.0.57": {"name": "react-resizable-panels", "version": "0.0.57", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "b15d6da6570392ee051c0581deae41a9cfb68980", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.57.tgz", "fileCount": 105, "integrity": "sha512-JXdepIfa28d8cyU2xRVw3ZIIUTkkgvPuGQgC4Y+znVbQlPS/mNttGJzukSI5Xkw8i55i2TdB2uvCCNsBvFhqMg==", "signatures": [{"sig": "MEUCIGdoMRcVmLoZMCbC/J5G2XeAPXnlvmpJEpylkghMWsjjAiEA4L+fq+GWhTyGbmWH0GFwTbRHq3cCIubNMIH+na3fLhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1247067}, "deprecated": "Critical bug fixed in v0.0.61"}, "0.0.58": {"name": "react-resizable-panels", "version": "0.0.58", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d826f856a0f718adb2e8365536a97c15d339b487", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.58.tgz", "fileCount": 105, "integrity": "sha512-8bbE8V0G00HJZpidrPLP5PoZA7cgOKfm7PNQlwpRuT7FCKruXkmHPONgUdipQEnNEzOjcRtZ+pK2YwhQWTbnMA==", "signatures": [{"sig": "MEQCIAFWenfi3XeuRwDos4ypIkSZJgIsBXLLb5kzxLguhkAUAiBoj3VgTxbGx+Mnv8XHp5dJyce5ZQjT0SrOWaG/61X4uQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1305279}, "deprecated": "Critical bug fixed in v0.0.61"}, "0.0.59": {"name": "react-resizable-panels", "version": "0.0.59", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "1e32250499fa8375f1b1ee57ff101e85f3b54330", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.59.tgz", "fileCount": 105, "integrity": "sha512-WMEjUMjW8Q0Cmp7CbzyMO2YkVDjmVZ4Ch9vJqTQRVf8jLG0H5/DQes8fUZLZIGWitrMMxNJ2N6bGqJI4JvhfZQ==", "signatures": [{"sig": "MEUCIQDWDkt3Qb2yNIPJgC32ohzxtcFJLRL+HhU9M7UiA9pxuwIgd4iYhCp/23LxkGYfB4pCUqtlJdKtC61AvPRGxrBN59A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1330912}, "deprecated": "Critical bug fixed in v0.0.61"}, "0.0.60": {"name": "react-resizable-panels", "version": "0.0.60", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f2470836dbf799d21ff61377a7898316fd9c6ef6", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.60.tgz", "fileCount": 106, "integrity": "sha512-rB4xm+spzqWTJE9ZQCMUgidqYPOZvHR5LqO6RQemtl0w6jzX78R/jMsAg6yPO/4O3s5UQgNF6FrAs8QgRPX/4Q==", "signatures": [{"sig": "MEQCIGkUbMGXoNkvlN40uR8/iFgWQn8u3042NfKsinp4CAUhAiAh4YnQidnJplDlLlModfkekWShrbbaGiwSSFhK9z2kfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1378375}, "deprecated": "Critical bug fixed in v0.0.61"}, "0.0.61": {"name": "react-resizable-panels", "version": "0.0.61", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "2260ec8ca591431d367d172bced49733cc3308aa", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.61.tgz", "fileCount": 106, "integrity": "sha512-Vk2a4LEHWkI6hGPnPmXxa/2twLYMAMMUTyA2PtR1ijvH2Nkg/AhGqrPsIi/eI85uVWtYCFNZKEsbR3uGuJl/yg==", "signatures": [{"sig": "MEUCIHBJsMODRG3IcrHgASvWbZ3dQcbbG5/y35ZROD2lDSAqAiEA7K4msMl0NsdgfF2CIMRFxa2Ba+W2klxvEKY8Ro/lbHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1387206}}, "0.0.62": {"name": "react-resizable-panels", "version": "0.0.62", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "332c55f4da0cf41e41a6b85486e07de693d9c422", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.62.tgz", "fileCount": 104, "integrity": "sha512-tS4vaaq9Hkuta4jIiKlT/6fDu7HzpzjK5hyPF0SPzTsyiPHq7UEktJk6SJQgq/471AfOC6Hcvwdc2qMPnAeZKQ==", "signatures": [{"sig": "MEYCIQD/Fso04QeTKmuFtCGw5NoOS9RTDsfpe3TvQ/Bg1/n/SQIhAPc7uljHUwonEukrnZax2AagNKtKR+KSDoqIWJgDv6Y0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1095739}}, "0.0.63": {"name": "react-resizable-panels", "version": "0.0.63", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "d364d84ee5927bfb0e56c7ea75eb6504e9041a21", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-0.0.63.tgz", "fileCount": 106, "integrity": "sha512-AfA8b6kouhL4rBvgUGs17uzWVlYPaJIwwTCVeWRxNpUHJlCG1h9RIMlzA1849AZGsaNJO3j/SNdI5SS4GZDE3g==", "signatures": [{"sig": "MEQCIDc4Ogzgj0CChKmKtMzBqeYetie1OAMPimexiATLNpWwAiA9q+QpEF/35OBAuGyaKSYdzynO2i4SuHfpd5JorRh54w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1389115}}, "1.0.0-rc.1": {"name": "react-resizable-panels", "version": "1.0.0-rc.1", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "b683d20c0f2f63a22b62c3bf1b1ce7974e64bf9a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.0-rc.1.tgz", "fileCount": 96, "integrity": "sha512-l+K8lT+x0dC5DNL2qps8+rMqBD++jpNKP9tQaK2v/iN8+ThAYm7UnHAv5XDNEwoUABLxg4jQZoVTv7s3hByR8g==", "signatures": [{"sig": "MEYCIQCvWa3JOTIVgT/zn0+1ST7lHm9NccC0b7z6U9G3rmTWzAIhAIgFKdiaUl1SrHeWknmmRDKATVwCTg20trGA0MDtHIjS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1154093}}, "1.0.0-rc.2": {"name": "react-resizable-panels", "version": "1.0.0-rc.2", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "3a571e037f228443f13d8eb6dbc17a19dfed97a2", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.0-rc.2.tgz", "fileCount": 96, "integrity": "sha512-Oc3vFEd2RXlso6bFHopUDSshT2gL1/9PaPZhqffBWePau95ApC5MaC8kqvNifyeHVTyG9MK2K9dS34nOPiDUyA==", "signatures": [{"sig": "MEQCIB3uUoGL9mFvd6COAvkinDOOdn49tQTo4l/2wmHVQ3mBAiBeMp0xnUtXnZlLnz/YKnA8UneGudf4NwXQoztoao4QIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1207420}}, "1.0.0-rc.3": {"name": "react-resizable-panels", "version": "1.0.0-rc.3", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "11935189a5f6cb6deaffa9dcf258fbff303cd643", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.0-rc.3.tgz", "fileCount": 95, "integrity": "sha512-lX2yDK3f0EmRgi5u5HRckNrelIuuwSEUvc/cNBYymybAvxxqSQFW7lgf4mAMfwM93eSQagFFFi6cHCd/zAsZFQ==", "signatures": [{"sig": "MEYCIQCV+f7GbAk3GTbXyz/Ksfcelqm24+B5jBOZTv/psLDdyAIhAPqlu3kUDx0fbWVMbNxC2B1r4wPOLVsjkQ30dwPfMBmd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 928471}}, "1.0.0-rc.4": {"name": "react-resizable-panels", "version": "1.0.0-rc.4", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "3fdf2ede7aba147e80c2a8d971d06d802e317c4e", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.0-rc.4.tgz", "fileCount": 94, "integrity": "sha512-muNnR1rfQdiU15AZNVtovOL3fqK/9uD/uMu/KrRpi5ykkTeSkCTJvvWjt5bq9NCQqXXqktyexSKzuLIzROBEgw==", "signatures": [{"sig": "MEUCIQDZ5u3/fohuGSAJtHT19VsEcobK1nMeULo6aOcKJH76hAIgMYiJ5WMgbhtswZ4YWbuiS/s7asiUZHgDblsIuwHCy+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 885962}}, "1.0.0": {"name": "react-resizable-panels", "version": "1.0.0", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "ca7ff65d4d871f6e18e400e1b92bd1389f42d5bc", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.0.tgz", "fileCount": 94, "integrity": "sha512-ms9GMs0qV7LSX5TR511h1K5Ax+YYZQ2HMctqaWnsdnGgteJoKP1baA//v2XbicGo9xbuK0cw/8qFXS6e6xl8Ew==", "signatures": [{"sig": "MEUCIQCBMA66Ppf0C7ChanGdmPYoDF2mcmzf4x04yY+/Tx3wPQIgVFBVNOlWre3HeHWL4Wa5KTNszAhSXcbf0rxK9DPLGNs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 885954}}, "1.0.1": {"name": "react-resizable-panels", "version": "1.0.1", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "fe8add36018347f6594c36ac5f25d87b2622f0e6", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.1.tgz", "fileCount": 94, "integrity": "sha512-bFKrVLO6VCDp9+zXvLybB3Ldd7MF+Q6E+qE6sxlDfVAlIwEWksJ94CD5RNXTN9a0E3YyAZUkhJEw3a9aCgymzA==", "signatures": [{"sig": "MEUCIDv4avrWJHrFj3msGgIGB/2ep36yoddhGMPczbpxfd4eAiEAlrf3aQqeb6JPd7axesKbqKBv05kTsIXiywRjnP/0DlM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 888230}}, "1.0.2": {"name": "react-resizable-panels", "version": "1.0.2", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "e576992aa54913d4538086f4d5fffe2a809a6b1a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.2.tgz", "fileCount": 94, "integrity": "sha512-THNUZo6q5Use+ieZ85hR7vTZVOQh28Xe1qfQ96rlmu21MDn4V0YbLnVu7D8t6gaf6LANk9V1XIzIRdUZgZCG2w==", "signatures": [{"sig": "MEQCIHEqgv3gKX/VyTe2NmWb8PiSSNBwFC3fsjtK26EP0TtfAiBeoUOdbGxrI+Dc7LemDV6fcPNVJkjfEIOYUVZ8vKqHgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 888876}}, "1.0.3": {"name": "react-resizable-panels", "version": "1.0.3", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "b54086d099dd22592e5de56243cfe67fa09e45c9", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.3.tgz", "fileCount": 94, "integrity": "sha512-d54mBF/T6S51ZAU7s8SjLMa9hnLIhBHi35GJkdzC+gsEOpm8IEQ5odJid1AWi8neGfGm2HDANGo8Ndc6C8ISdw==", "signatures": [{"sig": "MEYCIQDBQh/yGzRGVXN5fldqKpDPbxTAMkIgqb5HDg7ZI2KkBwIhAKOVSA25SfihHUSQ/IbWaAShumuaziK/lfRHTuzzpId1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 898944}}, "1.0.4": {"name": "react-resizable-panels", "version": "1.0.4", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "9d13c718947df1ffdaef2a92409a047fd2629a03", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.4.tgz", "fileCount": 94, "integrity": "sha512-NRq2qxPvumZLr33MotBl8nrFx4dm0uxo8sJkztjnfTusJpdVNYnEDG9OMCiDyyL9UmyILyOvLR9vTG3hconWtw==", "signatures": [{"sig": "MEYCIQDovRPM84MDKRFwjP/XPyX2jzvRTg8tJJND5XievOBgGgIhANKqPSI0QsU6atgkB716FHJhVwpDyRbdbJQe9wX/p/wR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 899841}}, "1.0.5": {"name": "react-resizable-panels", "version": "1.0.5", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f6274758eb5f05a3fb85a077ac55f937a4dc8d80", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.5.tgz", "fileCount": 94, "integrity": "sha512-OP0whNQCko+f4BgoptGaeIc7StBRyeMeJ+8r/7rXACBDf9W5EcMWuM32hfqPDMenS2HFy/eZVi/r8XqK+ZIEag==", "signatures": [{"sig": "MEYCIQD583uU8uom7Em15xeAfrgEofyv25DGeLC0tjN+36ZufwIhAMLbij3c2VNqMB1OT3X7mTUvDJctP0ECqLl+kw/UvHUB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 903093}}, "1.0.6": {"name": "react-resizable-panels", "version": "1.0.6", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "bc48cea80b3aab89c728f93caf214d8b39bcafb4", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.6.tgz", "fileCount": 103, "integrity": "sha512-yZQiOP/uW2nTSdESDUBlBkQ1NQjUABpRKfBqonUQnNYSur7qRDy3W2wEEmrEyUY+W3opshpMiHf45zngIduJ0g==", "signatures": [{"sig": "MEYCIQD/lAQcUuRCwdawMKYdp9rebEz/M4s0GksNz25BPdUzlAIhAPdgixDpob9b1STPJrsD15POfHr+raArSg+27nChzMsI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 938616}}, "1.0.7": {"name": "react-resizable-panels", "version": "1.0.7", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f48b4dd42f143bde01a0441580578806fadc16eb", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.7.tgz", "fileCount": 103, "integrity": "sha512-CluJkHQheeNqIJly2FYDfri3ME+2h2nCXpf0Y+hTO1K1eVtNxXFA5hVp5cUD6NS70iiufswOmnku9QZiLr1hYg==", "signatures": [{"sig": "MEQCIFVU8ZXC4PoiLZJElfNjWvXgZk1gcHdh6BEZMIxcitIKAiAjwq3wpRcoJIoB9mEVQQw1Y7bGGUfVVNWMcTGXoYWyDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 939045}}, "1.0.8": {"name": "react-resizable-panels", "version": "1.0.8", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "325c532f53af4b3c055efcf794f16e92f93b4fb0", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.8.tgz", "fileCount": 103, "integrity": "sha512-IuivK06FWN115VSN8TDGYuIoAzplC4oPUCDZ5d+VWJj0p6N3SMfwjggpjMUGSpQJLvMi0FXPSLLn4rGVmESjmA==", "signatures": [{"sig": "MEUCIDxs2fEYA7mDvUSyY0NBLYqq99xJlfWQe2iV8VGDHTdTAiEAk+zQA/1eJFOkn+8+wA5pe0A2NSWNGc+CaZvj+9Fow1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 960671}}, "1.0.9": {"name": "react-resizable-panels", "version": "1.0.9", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "600c1d3491f0c236dc1527a6ac6b49d632522d44", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.9.tgz", "fileCount": 99, "integrity": "sha512-QPfW3L7yetEC6z04G9AYYFz5kBklh8rTWcOsVFImYMNUVhr1Y1r9Qc/20Yks2tA+lXMBWCUz4fkGEvbS7tpBSg==", "signatures": [{"sig": "MEYCIQCM4J64PVy3mKMUMlPAj4I7vAa0QoZaSFFPQJNHC+8mWgIhAOX5YKHh51MnMzSpKshqwGnojzVWzpE7GgUF+U1X9zQP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 964646}}, "1.0.10": {"name": "react-resizable-panels", "version": "1.0.10", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "51262e5530cdc77bba25605b7b2a58604ba4f9c6", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-1.0.10.tgz", "fileCount": 99, "integrity": "sha512-0+g0CNqregkuocr+Mi+e6wgWVARnKTYIX3U1QK7GlkLQKCmbymZakx80YGwcRO7HNnKJTQ5v38HlBos/cGxWvg==", "signatures": [{"sig": "MEUCIQCCoZF17EAY/tThaeik67atBXehIcfaNMhRuDlBLQKYZwIgDpYmosUD8ItnpE1odAoYNA0aVQs/F2y/3oa7SBalgEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 965039}}, "2.0.0": {"name": "react-resizable-panels", "version": "2.0.0", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f701b05f5016fa02471caf8c3144fb11268bbf1b", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.0.tgz", "fileCount": 103, "integrity": "sha512-Qiwt70G4Hfn3ad+VAywMQpdcmOWF+M9QRTxgcRD1oMoDwc8miilrcakIhpm+8Hm8pAeiq6QEV0SHbqbndVIiSQ==", "signatures": [{"sig": "MEYCIQCKBJM3MnOaDrFHT/1qRh8y7BDriCUVUg+mUERTCb5RLwIhAJ/yH7tdGvWS2fp3BON3PhV1oE9/MfGKp9JjPrv+wbrJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1051435}}, "2.0.1": {"name": "react-resizable-panels", "version": "2.0.1", "devDependencies": {"react": "latest", "eslint": "^8.37.0", "react-dom": "latest", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "bbf21724b06e1bb44a913123f3272f664aec0c97", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.1.tgz", "fileCount": 103, "integrity": "sha512-h+YJ+E70/UVsWjq4RVJhW0ua+yYFo5AcDkSUaVXLqBjor5CLYl9TypqoAlrYoWvlnZIQuntuo2g3aScTT5XiNw==", "signatures": [{"sig": "MEQCIHm1PzmVQdwZ2+4mU/X0v96/jaBAE9AqzXNnrmjxD0eIAiAmNORkVIW/Q7hFDlEyLebNRzXpGAuAl+Aw3xx1SxNFsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1051579}}, "2.0.2": {"name": "react-resizable-panels", "version": "2.0.2", "devDependencies": {"react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "39ff69da387148fdb069746c4a960391464bf2ad", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.2.tgz", "fileCount": 103, "integrity": "sha512-BIHRt1cuR3M5YuaP7Hj9Qm0TVJF3ZIlYfuf7yvH+Ys6Sg0LaWHyoiT9o4NmvWKYxLbI25L6dzDXiyQeKufA3hw==", "signatures": [{"sig": "MEYCIQDtqb/vwCAjfLuxd5mE7Fdce51f+Jth11YxJxVALxtCLwIhAJf3MRj9OpTr0ecOne/SbpZ/LxjOmPQAbFD4iEm3WnEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1055785}}, "2.0.3": {"name": "react-resizable-panels", "version": "2.0.3", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "63c47721b86f82dbb740b725595d9ac08d651aa5", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.3.tgz", "fileCount": 103, "integrity": "sha512-m0upi03cdqCt/g/dUxRQkHiBzo0UDtMIYwcf8qhKM+QG0cnpusOVFKOD2ElpGrtxB/oIxTItuKYsDOeu76JHvg==", "signatures": [{"sig": "MEYCIQCcIlYj7vmYQ7dMUstbk+brwrHf+ArIxMAlNHL1EAw8UQIhANnvS4MC6n8kMcityAyDKrnIQHPAwc+iLDvFanYlzIuu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1061700}}, "2.0.4": {"name": "react-resizable-panels", "version": "2.0.4", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "67b742c77df2f4a3c4a7428edad7062691b22070", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.4.tgz", "fileCount": 103, "integrity": "sha512-rVPtUxxETRaChQePc6xwdwcXCH3ZoGmALVFye1NllTHMBddvOtCkJ5c7CLLHErfNwInBNYSrF9OOm23DLop7Vw==", "signatures": [{"sig": "MEQCIDglF532w1b9kR/672rs/FAwj7d28FIGWi8nvoYCUKGtAiAehBEfa5HlIRp08Nj62SKNOrE2B7iSnvcsznled0vEbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1076327}}, "2.0.5": {"name": "react-resizable-panels", "version": "2.0.5", "dependencies": {"stacking-order": "^1"}, "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "2788b79b1beefee88ab5593d1f0e4a4c003dae8a", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.5.tgz", "fileCount": 111, "integrity": "sha512-2fX4sx0K48tGWpBRppYaGoG3tpq3Xg4elTQ1s/MbqDgoPrxK6N1SaWSk/wG8fYEYRHg5DEsKbU5jMLQaH72hnw==", "signatures": [{"sig": "MEUCIQCtUbLW/+j9DuO9ArLKrWLXAMy5l4bFAot7pu5AB1KXUAIgVWYKFrmlR1Kge/dmHSlZ/NXHz4PjTsxqltVdPovhL4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1128268}}, "2.0.6": {"name": "react-resizable-panels", "version": "2.0.6", "dependencies": {"stacking-order": "^1"}, "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "11195af52c0f7b8f47fc72a91ec4837a4103216e", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.6.tgz", "fileCount": 111, "integrity": "sha512-GGkXtlDsUZvoHai4j1IO0GavLjxZJgFjfgx4CBN3cJQ+MN5HaOw3zMx+SsorQZ3caMYPYvuIArYFUhIo+q661w==", "signatures": [{"sig": "MEQCIGPt5LXBd5UPx7/T1BmoS04kBF2mR12an8Q6RLMPJJPsAiAzEiL7SyuFzRs+TT1vb8OUzXzyiWCbA3XFHw/j8l/Qvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1129003}}, "2.0.7": {"name": "react-resizable-panels", "version": "2.0.7", "dependencies": {"stacking-order": "^1"}, "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "08c414921140611db6f601f509375d2f0436617b", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.7.tgz", "fileCount": 112, "integrity": "sha512-+gD2FLfGmoP95DbAV6VgoGxvlGxRWrxd54EnIlTgSvA+t53Pvbe6UOMz4yTz/8cxi3JvbpHlgbAiTfTofKp+wA==", "signatures": [{"sig": "MEYCIQCtZ/dZKi4hR8NVryRzSCoajrkmvGOzqrfLYb5DzloKEgIhAPJsYgIOkKYcxy+/wyXOAFGJNWiVtuEbSQN5y0veWiep", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1131903}}, "2.0.8-rc.1": {"name": "react-resizable-panels", "version": "2.0.8-rc.1", "dependencies": {"stacking-order": "^1"}, "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "5030f67a1df4a9c05e56a4b73d258d16e3376fed", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.8-rc.1.tgz", "fileCount": 112, "integrity": "sha512-CCVl02FS+EMoxo02rzVix8nwobuw80AqrEiNvkjuQV40vqCU7NYumHcl2HuEX8pBjbNNnRa4zj5PmDcb3HveTg==", "signatures": [{"sig": "MEQCIGCNw5pGiT1nJcVWGd+HwR++ez94KBL2JG+LzzIzc+GoAiAzpqhugGa4Ar0amOGn4yD1VrWZ3bZN9ew9jC0sYqmIJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1131752}}, "2.0.8": {"name": "react-resizable-panels", "version": "2.0.8", "dependencies": {"stacking-order": "^1"}, "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "032f97508b001c081586e8161be1376fe3459d44", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.8.tgz", "fileCount": 112, "integrity": "sha512-iAazXPOZJcH7wF9V0S7VoloXoDvz1UZqkcGjz4tCF6vxZjA9hkccns5ItXPxrJ5m+qZOARaf8OczYi9C+eZr7g==", "signatures": [{"sig": "MEQCIHS2L4YJpiQ/aY3R4DIA+INBlkfV8SyNap0HRGiwem0FAiBIUlXyjgyoPqL1H4wjrUGRpmoLHJP/oInnWuD4jXVRsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1135451}}, "2.0.9": {"name": "react-resizable-panels", "version": "2.0.9", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "35acd8116475ebebb916c1559e75de3594004546", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.9.tgz", "fileCount": 113, "integrity": "sha512-ZylBvs7oG7Y/INWw3oYGolqgpFvoPW8MPeg9l1fURDeKpxrmUuCHBUmPj47BdZ11MODImu3kZYXG85rbySab7w==", "signatures": [{"sig": "MEQCIE37BwigLlTV8LBoHJWqhGJeEgtfb0NwTymxXk98jq9sAiB6KnQtDhbPICmTpyQgqiyVDq8qoom2l0LDlQ/gHSt1FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1180970}}, "2.0.10": {"name": "react-resizable-panels", "version": "2.0.10", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "9b83d52952047435a4ceba0b6dd0799e162816d4", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.10.tgz", "fileCount": 113, "integrity": "sha512-1cQl5rp3VDWRL04XXX92lzM1hej2Fe5x+vpjYXcldfOjKwLV5JVrt5T6q0cTB5yfp4Wh+JIdYQ9A5EnZypO1DQ==", "signatures": [{"sig": "MEUCIQC28C77q+/KEjjPa3W2bHnzkiEi/WtQa60aCTDTMWQ4JAIgGytMZ0zKaZfFiRp77i6bGiSATOr6z61A0R2GB+Ub1n4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1202307}}, "2.0.11": {"name": "react-resizable-panels", "version": "2.0.11", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "7f7e116d5fb7e1a03c436a69be9f567fe0ecd531", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.11.tgz", "fileCount": 113, "integrity": "sha512-tA3OvGFEK/U9rKuEg6TpXcr+i+cN5X8B4UIvs7jqr5lby629pDTGvqRjo1EJLhBpRZfkg0Zz1INJlSYigaS99g==", "signatures": [{"sig": "MEYCIQDnX61I6HI3VrPJB+j2oq9EGu3BHlVGtm5HeWkIm4VI/QIhAPviO78MkGNsl/SWESgQFJMC26nSjnhZfipTbe2E6cQr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1202554}}, "2.0.12": {"name": "react-resizable-panels", "version": "2.0.12", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "0717658043f1f7b5b1b0ad4a58737aa8ce7a5900", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.12.tgz", "fileCount": 114, "integrity": "sha512-m0cfwlVQ+460iQbOSVfs3MljVniHI/LGpHGQBa7aNCYUYwaERHcf5W/lCTsoRhbGQfMW9An0M8gxUHILA53Jeg==", "signatures": [{"sig": "MEYCIQCXdCHmu/osPNsE5oe8ZYM1UJ8Ms/BttlJjky94Qs/ktgIhAKxMrnE5iAh5H0jbSlcm5vqFtmaeL9zIM6Lhm6v6ugwq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1222649}}, "2.0.13": {"name": "react-resizable-panels", "version": "2.0.13", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "53cc5a4379dc499642f841d04dd536786621398e", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.13.tgz", "fileCount": 120, "integrity": "sha512-DZxLv5Pc6rfuqkgQ+2JW3eLPiX3BgAAR38Cd0lXuCVHXEZLrD+3W4Nag5TqCoNgEM/4IUU9L/pLM2toOUOd6UQ==", "signatures": [{"sig": "MEYCIQDboVUY42r9tkf77arzwwcUsiNtqKgUTXQspL+pHoJkkQIhAPwHAUCu0yChWb+Sjphp7ePee28BRMR3hzDtAaRUanaG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2569890}}, "2.0.14": {"name": "react-resizable-panels", "version": "2.0.14", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "26ccc89de2335d2e290a019bb1fae2128d714ee8", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.14.tgz", "fileCount": 114, "integrity": "sha512-LeAYx3FsIdc9KaWdRpQVTL3AnXzuW4U96S8968H5iCpNwXljFWTFXhbyh73Tw3x20u/ZWSCfYED3WFe37ZbvZw==", "signatures": [{"sig": "MEQCIBPeu0PxN6DJ7styLfJsY/jPwCEbqzWjxexFeUmJhyfaAiAp6FzyAqiquwkTY6nNTkOm2O4WX2V2LHooVajwWGS7Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1229293}}, "2.0.15": {"name": "react-resizable-panels", "version": "2.0.15", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "f316d8b87ff7ce7546b4f8d2989356a0bffb2719", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.15.tgz", "fileCount": 114, "integrity": "sha512-ZMLZFcOVpxxnIjP/+QRqHhqNih0PdtcelBEnpYDe1OWcVKyuGfs/Vc0gy9o2VRTCYDGpGiKvWm/ifuxiahHxwA==", "signatures": [{"sig": "MEUCIQDjqv/65PqSr3HhI6ScTGVqD3UKMgCo5D+MfP+wIwNxcQIgWdMf5I3KIIq4pIK2t5vtyNbb9/1meejP7MNbUPYo6Ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233337}}, "2.0.16": {"name": "react-resizable-panels", "version": "2.0.16", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "bb7c6ca56c6320a0a0b821a67c6aac5c88981517", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.16.tgz", "fileCount": 114, "integrity": "sha512-UrnxmTZaTnbCl/xIOX38ig35RicqGfLuqt2x5fytpNlQvCRuxyXZwIBEhmF+pmrEGxfajyXFBoCplNxLvhF0CQ==", "signatures": [{"sig": "MEUCIQCHtProlvUV/UoHdtcEevekUmPIo5cPJOX3wz6jshugogIgSj54Q1DAL/rAI9Du0aIa95eBnKrpK6LhrYCeX/scTEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233222}}, "2.0.17": {"name": "react-resizable-panels", "version": "2.0.17", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "2b32718a4cbf77d3be150043dc8904dc6e3107f2", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.17.tgz", "fileCount": 114, "integrity": "sha512-MhnHUjYB6NCZ7rmTXuTg/8+IKaj0PkQP8dm+r3Riljd+lGPElqbTX+mfqr0HJBDWJF0JH30cwe6CnuiNG6RIIg==", "signatures": [{"sig": "MEUCIQDS+GmefSMfPrhW2WxddZ6fTP8ltEK0e+ZmCWJQEmnTvAIgO9AO0+foP8+z80CnQvs0T5Uz5GAgpT5Nw7I/oU0vYIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1228951}}, "2.0.18": {"name": "react-resizable-panels", "version": "2.0.18", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "976d317a58dd1975edbd55197d9a7d849b63bbc5", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.18.tgz", "fileCount": 114, "integrity": "sha512-rKagCW6C8tTjWRq5jNsASsi4TB2a+IixL9++0G1+kPgAgvULPVf7kE0VbHysC3wdvcGYMa70O46C9YpG7CCkeA==", "signatures": [{"sig": "MEUCIBR2PpqETDH7COE8yoOylaxn8iLZ1XXpJQ60Twh/XzYZAiEAu9CGVPo8V+a+RQqQhXI8ISSzTKpQGFNM+Bn62hIYga8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1231569}}, "2.0.19": {"name": "react-resizable-panels", "version": "2.0.19", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "df259898c682cb774af65c3bc38c1b29c855b99b", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.19.tgz", "fileCount": 114, "integrity": "sha512-v3E41kfKSuCPIvJVb4nL4mIZjjKIn/gh6YqZF/gDfQDolv/8XnhJBek4EiV2gOr3hhc5A3kOGOayk3DhanpaQw==", "signatures": [{"sig": "MEUCIQDwO42v5cgNxv7B7n6z8nuGu8B09hMvGJISz36455SkYAIgMsnpoR/CSjnKsUDXa+V7ATu/LcffhYhSEw9rfavrxjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1237337}}, "2.0.20": {"name": "react-resizable-panels", "version": "2.0.20", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "fbf7e2153117d70ee507faf95a69343bc1f8d44f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.20.tgz", "fileCount": 114, "integrity": "sha512-aMbK3VF8U+VBICG+rwhE0Rr/eFZaRzmNq3akBRL1TrayIpLXz7Rbok0//kYeWj6SQRsjcQ3f4eRplJicM+oL6w==", "signatures": [{"sig": "MEQCICFZyuDjIRrv6saYSFoBeentOnwlH5qtYKmYGbJ4dCtDAiA/sRyJ2nnGg2WtIPDQI8T2KU4TfZVM++iLRpBWfIe8Aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1245877}}, "2.0.21": {"name": "react-resizable-panels", "version": "2.0.21", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "36df75b3b7666fb785aa49b86d8a1cf841daca9e", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.21.tgz", "fileCount": 114, "integrity": "sha512-rFRjZ2sJ+iut0hY+5NtIxoIRlQqTWqZ6rMNahaZeZDpRPQyg8xiKjrlvXMq52WswFwpg47GFJcxn7f9uAFnZsQ==", "signatures": [{"sig": "MEYCIQDQbLSZtWaqSkhM8yqUTK6JULYbCwavmt4ln/dIgq1FaAIhANXIeM8E2AhJaXoQ6qF0KIN/Gz0T56BXsViN6ZCDp0zr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1248175}}, "2.0.22": {"name": "react-resizable-panels", "version": "2.0.22", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "de0523852dff997ed96547543910d1183fc45ad3", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.22.tgz", "fileCount": 115, "integrity": "sha512-G8x8o7wjQxCG+iF4x4ngKVBpe0CY+DAZ/SaiDoqBEt0yuKJe9OE/VVYMBMMugQ3GyQ65NnSJt23tujlaZZe75A==", "signatures": [{"sig": "MEQCIEIBNtU/hSQzo879fo9WsNNFRSyrBafo3z9dr052hKY/AiAA5KYvaODfDkff25CTIxPkdt9/Sy2v2sz5A8R00Pvu2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1251588}}, "2.0.23": {"name": "react-resizable-panels", "version": "2.0.23", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "7a4296f23028c32ffcbe8086aa918cd934e7e87f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.0.23.tgz", "fileCount": 115, "integrity": "sha512-8ZKTwTU11t/FYwiwhMdtZYYyFxic5U5ysRu2YwfkAgDbUJXFvnWSJqhnzkSlW+mnDoNAzDCrJhdOSXBPA76wug==", "signatures": [{"sig": "MEUCIHV0rrNqM44aRqjeldMIkzgvKzqibwzyRivblt0bG61fAiEA0zLJgLW1xAnf9Rqu+INT+HCtXW/NfYOcOUnguAuFdVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1252169}}, "2.1.0": {"name": "react-resizable-panels", "version": "2.1.0", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "43c6ff159c33f6a3cc379b6f7945679632c7843d", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.0.tgz", "fileCount": 118, "integrity": "sha512-k2gGjGyCNF9xq8gVkkHBK1mlWv6xetPtvRdEtD914gTdhJcy02TLF0xMPuVLlGRuLoWGv7Gd/O1rea2KIQb3Qw==", "signatures": [{"sig": "MEUCIQCmrFo1LHvK23g8Xct8FFF1vV/lj5WaQPMCpkn7ZKEX6QIgMkVhpcb0KaXNahYLrYO4pYfut9S+iHn/63T9p0ah7MQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1260488}}, "2.1.1": {"name": "react-resizable-panels", "version": "2.1.1", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "efcf4ee0d7b45d9617db2f2e3de1ca04589e6b7f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.1.tgz", "fileCount": 118, "integrity": "sha512-+cUV/yZBYfiBj+WJtpWDJ3NtR4zgDZfHt3+xtaETKE+FCvp+RK/NJxacDQKxMHgRUTSkfA6AnGljQ5QZNsCQoA==", "signatures": [{"sig": "MEYCIQDffDj0BX8LBiQ0GSl2tp92mIHMjI5GtJrr9b1gUCbxiwIhAOTaBP4wCXV1iO9MM1yabq2PNNPgusUKT9EOvXOeF0aK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1261444}}, "2.1.2": {"name": "react-resizable-panels", "version": "2.1.2", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "42945db30d9677d42e12b2c0dc39870f7729e6b0", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.2.tgz", "fileCount": 118, "integrity": "sha512-Ku2Bo7JvE8RpHhl4X1uhkdeT9auPBoxAOlGTqomDUUrBAX2mVGuHYZTcWvlnJSgx0QyHIxHECgGB5XVPUbUOkQ==", "signatures": [{"sig": "MEYCIQCXxAwQeeED5/AgJ/rz0AXxfwoIkZCNcYM+WyLEBSSqEQIhAOJXtWTCkyBXQEVQjJpvZ3FKBtyIni67dCl7HnmGU6Gs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1262543}}, "2.1.3": {"name": "react-resizable-panels", "version": "2.1.3", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "4a8255c9293d6dffacada553c27249d266a482b4", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.3.tgz", "fileCount": 118, "integrity": "sha512-Zz0sCro6aUubL+hYh67eTnn5vxAu+HUZ7+IXvGjsBCBaudDEpIyZyDGE3vcgKi2w6IN3rYH+WXO+MwpgMSOpaQ==", "signatures": [{"sig": "MEUCICOQQopXJSgXAX4MwemRolT480TchGq970BXIvKyfN1zAiEAxTdqrhmHZmDUZYnpNUEHh/Ht/xRJBwZzf4WpWfGrmlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1269060}}, "2.1.4": {"name": "react-resizable-panels", "version": "2.1.4", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "ae1803a916ba759e483336c7bd49830f1b0cd16f", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.4.tgz", "fileCount": 118, "integrity": "sha512-k<PERSON>e8lsoSBdyyd2IfXLQMMhNujOxRoGVus+63K95fQqleGxTfvgYLTzbwYMOODeAHqnkjb3WV/Ks7f5+gDYZuQ==", "signatures": [{"sig": "MEUCIDU+TxqY1MRcYoW5jMsNTBCs+uO7VxLCLh+7S+ThQmqVAiEA8Eg0sOHHYRkKmWqIhKz3LEfpTW6lHZsPgAbJ4t7Xq+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1270962}}, "2.1.5": {"name": "react-resizable-panels", "version": "2.1.5", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "99d90e2a37c3941aeee5ebac75fe50cc1cbcd8e4", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.5.tgz", "fileCount": 119, "integrity": "sha512-JMSe18rYupmx+dzYcdfWYZ93ZdxqQmLum3xWDVSUMI0UVwl9bB9gUaFmPbxYoO4G+m5sqgdXQCYQxnOysytfnw==", "signatures": [{"sig": "MEUCIQDXEuLlBoQ1bnmI8ujfGI2AvW2gfMcCAEyYuHavaViOmQIgMs0cdJXbgy3sQN9VGtNxfwyMuQ6qz0iWWbT8RaG2wb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1540712}}, "2.1.6": {"name": "react-resizable-panels", "version": "2.1.6", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "d65e009d3e59618305dfb7bb9ce3702525b9875b", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.6.tgz", "fileCount": 118, "integrity": "sha512-oIqo/7pp2TsR+Dp1qZMr1l4RBDV4Zz/0HEG5zxliBJoHqqFnG0MbmFbk+5Q1VMGfPQ4uhXxefunLC1o7v38PDQ==", "signatures": [{"sig": "MEUCIQC1y53+pqj9pj7geGvvFK9YS/OK2FmEIvxBRaAmdiCZ8AIgOQVF5CYCaRQwSKctB0Z7wgKht9qqL92Mp/IArzWiLso=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1274375}}, "2.1.7": {"name": "react-resizable-panels", "version": "2.1.7", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-no-restricted-imports": "^0.0.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "afd29d8a3d708786a9f95183a38803c89f13c2e7", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.7.tgz", "fileCount": 44, "integrity": "sha512-JtT6gI+nURzhMYQYsx8DKkx6bSoOGFp7A3CwMrOb8y5jFHFyqwo9m68UhmXRw57fRVJksFn1TSlm3ywEQ9vMgA==", "signatures": [{"sig": "MEUCIQDNPIElXtAMVKC6gdCJdYTdq75/9tOg7HrYTDLaB2cmmwIgMBmsY5gb5YUhC3tT5c+pVDPr4yYBMBdyfDrxuQmSkOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1007212}}, "2.1.8": {"name": "react-resizable-panels", "version": "2.1.8", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "4f31057854d93c30a6e221ff8c0ecb7addec55a3", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.8.tgz", "fileCount": 43, "integrity": "sha512-oDvD0sw34Ecx00cQFLiRJpAE2fCgNLBr8DMrBzkrsaUiLpAycIQoY3eAWfMblDql3pTIMZ60wJ/P89RO1htM2w==", "signatures": [{"sig": "MEUCIQCXLFOyI85B5WnywG139P/GRtBFm1WH4JxrIk8QPEzyxwIgQ/sgXRwFQzGpY+kKpAN5WKeNchVhgK5inN2HUnmCza8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1012033}}, "2.1.9": {"name": "react-resizable-panels", "version": "2.1.9", "devDependencies": {"jest": "^29.7.0", "react": "experimental", "eslint": "^8.37.0", "react-dom": "experimental", "jest-environment-jsdom": "^29.7.0", "eslint-plugin-react-hooks": "^4.6.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "874847710f4f122df749b5f08ebe9c72a1e338ca", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-2.1.9.tgz", "fileCount": 45, "integrity": "sha512-z77+X08YDIrgAes4jl8xhnUu1LNIRp4+E7cv4xHmLOxxUPO/ML7PSrE813b90vj7xvQ1lcf7g2uA9GeMZonjhQ==", "signatures": [{"sig": "MEYCIQDhtqpb8LVOiG8KbelYznx51KRKbAZnMilYkdp+F4nENQIhAK/BXm5kyfQmpRtpVUoEtmN2uB2Rq5kqKayUw/S6YYWg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1028536}}, "3.0.0": {"name": "react-resizable-panels", "version": "3.0.0", "devDependencies": {"jsdom": "^26.1.0", "react": "experimental", "eslint": "^8.37.0", "vitest": "^3.1.2", "save-dev": "0.0.1-security", "react-dom": "experimental", "@vitest/ui": "^3.1.2", "eslint-plugin-react-hooks": "^4.6.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "be93adf38807a5ab056c02e22506688813132ffc", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.0.tgz", "fileCount": 31, "integrity": "sha512-UPFAMvU5UfXA4yGpqhjvY35zEC44ML0yRHjLvoK3nQrFFcU+mWUGMmURYsD3mp7P8oZdrKEwR++pFIexqSASlw==", "signatures": [{"sig": "MEUCIEwP1kBgGBwMt13RYlG/2dShZH7eBrtNIVFMy5wgBEc3AiEA2kwTE3RNnu+x8M7iI4dnk/H5ZRWvz+s38W+0P7V3aG8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 525530}}, "3.0.1": {"name": "react-resizable-panels", "version": "3.0.1", "devDependencies": {"jsdom": "^26.1.0", "react": "experimental", "eslint": "^8.37.0", "vitest": "^3.1.2", "react-dom": "experimental", "@vitest/ui": "^3.1.2", "eslint-plugin-react-hooks": "^4.6.0", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "857f29509e9afb7895dcbe076f19748576d93f51", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.1.tgz", "fileCount": 31, "integrity": "sha512-6ruCEyw0iqXRcXEktPQn1HL553DNhrdLisCyEdSpzhkmo9bPqZxskJZ+aGeFqJ1qPvIWxuAiag82kvLSb2JZTQ==", "signatures": [{"sig": "MEYCIQCpAHsd4/UGEw0SmcymjtdtiRnhjCGFPPrpmokt/HAqHgIhAKTCVCDHWkPNzH66lADJ45MxNELxMIaZwqE9XVNg/MmV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 526100}}, "3.0.2": {"name": "react-resizable-panels", "version": "3.0.2", "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@vitest/ui": "^3.1.2", "eslint": "^8.37.0", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^26.1.0", "react": "experimental", "react-dom": "experimental", "vitest": "^3.1.2"}, "peerDependencies": {"react": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-j4RNII75fnHkLnbsTb5G5YsDvJsSEZrJK2XSF2z0Tc2jIonYlIVir/Yh/5LvcUFCfs1HqrMAoiBFmIrRjC4XnA==", "shasum": "c059bd1317eb24ae0d1065a15ab2280f1e7f9e90", "tarball": "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.2.tgz", "fileCount": 31, "unpackedSize": 526934, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD+zQlCHGnMtDhvwBMV7iX0KQhrwMLnxsamxwluRP7G8gIgB84/zdzgywu/iSPLUA3hvb2heOexFEJ2OL682DSWY8g="}]}}}, "modified": "2025-05-12T22:45:46.258Z", "cachedAt": 1747660588128}