{"name": "@radix-ui/react-use-escape-keydown", "dist-tags": {"latest": "1.1.1", "next": "1.1.1-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.1", "dependencies": {"@radix-ui/react-use-callback-ref": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2007db580787b946d6f6711c9611ff4872612580", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-DP9skVIKabzT2lj56nfBu3Ff4oae2tBzckAi2VrkLRpTa5de+RLmh6BsPE4SbrWp9D3nHBmQTPr6RhESuKR57w==", "signatures": [{"sig": "MEUCIQD9Kv6HnO3XjfVRNSYEyPQkagidFz1Ts6smHwwAvhpB/wIgVFNNTjnoLElJEbJvx7dpJwf8mRcwzFuuDPZp7sOZ1kU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9xCRA9TVsSAnZWagAAAlUP/RnOypBeJIMgIKiETlMu\nyvLEC0LjKAeMYx9GvVvWJQ8lzHwAIWldal95HjhjNYoOsPTdgpZEtOt6i7Oa\ns9U4SvuNIGbaM8I2JGrY3N1hUufwn3uA6Y4otkAh41fZNum4ynzCxmsQ9/VD\nbAjcujrU6wDB99u0WkPR0+bZL6pb2aijdmz0b1qmnRTAGsY3rxPqACx4It+V\nK26LOJE2g2fs6c2apbk87feclI5zNLqCb+M+RP7DAfxlPDXZxpZNTSgA5axk\nzM9QpQGxcgNk3mfyVnENJpEtJ1YUuavi5JDRKI7xIELk0HqOF+96K3L4D8LR\nvgk/TjiKRMZIJ2VAj9u60/fm7VA5g2yrjPdCIaLjRPnVDQL8FTTiyWZsd469\nAAEi8eBc8bw/6ZjhQ1TEqyrugLKLbEoD8pEr4fRZmK/ArhKuXczN3wHw02Y9\n2vtfI6dDe/nVWp8QwxAjbzz5DmZicG4qV1N3ynGxz2DE6a2cG8sQKkqxqMae\n9R0JY1TYZGx0dvnOfE00nVg8Bu3lBO8KajlR8Ch9IBsJ6Y7Hox6FxZHQhFCJ\niYlgqseKmDIdtz85CGxnYMBhKYnjr4ttkTdAgCZBcxHIqZin8jsXhuEgrIRu\nv8IqNn4ePIl/3IZE3kY4F6hoAY9Jxxb++B+BG+OfanINgTOJSDjDuitFmAQ/\nXdxf\r\n=vCK5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "567e31dfafac81a157acd690e383be036f6587fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-6CAEkUQy+coAL0WanrA+OATswzZFwyPVelE3jbCQanH3REVH5RneuR3Au/broJvr+gpjwye1zpZIbR9QSF54YA==", "signatures": [{"sig": "MEUCIEUrF0yyfXh4KkZCvBL+TvFUvB+qg6udVCJy38l11YO6AiEAwm7FYXYaAIV9bGTYRHBx92iDBhHcLnPkh5pxGGDDXBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPKCRA9TVsSAnZWagAAMVAP/1clEtQ8R75+iupGkfKy\nfMr+30Bj16De3SaWU2pgfTtAplPvYvohEjk+1pIpntrAsEget+uRx+B+CkhB\nZi/dkPcowrvTJjjQiorm74gZC9p9FgWv4lxa2AYgFqjbADOg7feHVOr/WxeT\nHggc9q5KNzeIRoXqnoAE6JwRK5C4KFCVZn3zSfgUK13Z27aUi8eYJFHjhXek\nRRF4PTJQ8C4DMrXQoKA9e/3AKLNfGRBSae5m72rzIWJfbXFd0ARPUhwhr92P\ncj2xbE6Xfx6w2GGPw0aamKSlBqNdyX7Mc+uJuhJZdj1tTiRd8j+264D7P9kS\nEpxzhb5pROYMbpgRKMxFaYYnHA6CprnM0wWlt5plGuV+ddX34LWkX0rJe/XB\nQz2/0r+s4FBcnjUDqgYV6BtkRGksYoFLgvvnq8ROSr0gzmgsNtJpaOFBsKAG\nwtvuG2Zs2CZx/WjxvCkD60bGye41zBNHQg6x6z+BI4jDtkqBjSF/311A4HNN\n/gQOC+uxaqlBAd+3qMwXawR2k9xVOUAjxLuW6x2X+fFJVSmyh9AJ7aCZe2Wk\nVHsUfqnpUf3Mrw1GWUIVDp5OFnBaXW26l7QULm41qZfvL2iOS/YqGcfAmfDi\nNQsQ+/DYcK2Av4Y4N0zhergBVG5+wtJn6bbVk1E8NL0UeVuOJWXYNyNsjZTT\nEznL\r\n=Ykkk\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "87f89d1a8e6a2ef4c029b87612c480d590cc91de", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-eU+3pn57NQ5nFBU08wAedZ30OlH6LpV2Bin4drq4f9oNn4CPHZO2vsRovzPp/NDarxy+lnetbDei4sRp/xac3g==", "signatures": [{"sig": "MEYCIQD+xoup/qa9n9vBy9L0kg0J8Z+cNlYk3rDYJYga2MVFVQIhAK1UxMNwQD3vim5XgannxYgT3Z5VXOs3Di3zcmBxDSZr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0hBCRA9TVsSAnZWagAAiOQP+wRdF35EkmnCHGtdhl/N\nIXTyZG6TsHNSuOtLduVcyWsM6JTBc4ylFMEdLnzFleVap6SVP0f9rrHxUoSw\n8G0MuhkPfDGxShpmynBCvHOi8eAUE6AXVX8w00leCns5o/dStDd9lLiVTLlY\nRWHhL/zlAao9pK1ic9YbQogoy4vz5fa/jGEZZJ+++ddgoe4a8yjWQKkUQ0JM\nqh/WDQw/gLRJgGIAWUmUIl/NJXn5T7dyLIqQaVP/zjmM820C1SERcwQSSZKt\nwyu7y5gNBAFjVCpJTSZp1LqEUBG2Tk1BMzmYRNgankSt6oqSWcPQgWRvF2Sx\nRyRsFLgGk5VxX89N0YOdP7p6C3KtMgE6hZpIfUUboDQ4veQLHIF2Kut3BQWG\nzbnotuAhAVIYFAXAKBxx1L2d1VljJg4WAkHBj/yAFZefmo6h8gE0WNG9NeRH\nbwhofF6i8vM+lOM8l6esPMIxWLKe3YHmVOz8foEpBz7wC6QjxpJPk8+G3dXT\n/4fS3c4UVCeG4IDgfCdTQU/ENTiooweBdTB0+Ufdb2TUr/FyCf3i/oPXUxkT\n2xyeIkb3wp0Jo5tyF5bAy7b3yLG8N8SLyA4Bu0Bg+szqJYlQZduL42iB1BZu\nY7vQG5Y/ao/8ASYDHWPC+OYrkOptY/IO/+hNMiHZYE5igEm3K2sxWE603BdK\nx19u\r\n=SC0p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a47be033f9b501109004a96c630bd91f4f175275", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-MYYlZszKRZ8afVzD0BBEkEujYtO+hMVhzOllJEIA30wPJlE9ileS5LBh66pbqNMGhVuC2zzE+oDF3zmY9D+keQ==", "signatures": [{"sig": "MEQCIDJDRKWS4x+PsRnzFwCajGfq17wZmdXoNpfJM/ATVV94AiAhwA9na1WrzznDegSsJyEfgb/l//LSO+9V+Rnb/UGLuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1INCRA9TVsSAnZWagAArIkP/jdvZfjy5DQZhmmRxbnk\nxbW1QAYmPjKZLRSdcVmuCjPgjUqH3DY5e/WvnMUid3HDQDWg1ikPHMiGIX2H\ni6hN8SCB+OkxZ24kxTTlrAJ3YGp6muTcOfZlDrg42uelWAjy/HOYgXui+VVE\n9F/mAUeNqqerGGRcmWiyz8dkszT4qozH/+UuGjbCg3KDvAQh6qEHnWPskWME\nK+09UON69rUXQjlBmz/C6wccsepXVlVl+dsnmZe1Dsuc1wG3p7ZvQ9rBecw+\nAHdFQxk1TRjc2aWSjj0T212oJlwmdAiJAvoXLknr/Asj9+yLGeZFaqFgeRNN\nBxPPQcrDXf5BHpRde99JZ+nYBG+wij8du1tmi0KvUmZKu2FlYQWrt9MKYG8Y\nyL6wzjpnYBg6SWjf0G//Wx1Bf8oMaBI7ZIz3Y3g3IhF2ajrMxgHlR7/WGa07\nMmROPY1ew1TaPem3jK3pwlHo4G8gijHVxoMotJ8b+W7T9UeUVYn9oc0y6b8x\nzdFJXLBh/A1m1CeAaqYvz/secGhi/A7yr/HZizpcF9URu8dLljwMZBz7uLsx\n/O5wnkCImVQEZmFEvV4E0qti3yTQkHhdKJkOWwudnkOdf7aRhR6gkPJ61kHW\nJRmFa9bLK6WjiDvAJfGT6WfNiZ0/CK+vcwHLUMF25LHPnKLvIl/VDkojUpcI\nvMRd\r\n=jY2f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2811535f8c0968ca959383a3de903628e9f105c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-gCkAtg+YbHZOwV9o8g3eV46kDK+nqpsfzZxJM4BZ9mLbcJrE+exJIoFLsd/3/xr8vedrMXTzR/V71QWzxKYlNQ==", "signatures": [{"sig": "MEUCIQDlD8uwSjMtx3bBDcKjekUUf+hfXdIWZB90GFvpZSB8mgIgARxEtnCiAi6epRWLr6XwGF8E3AlRCstXfkHbgMvXuQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wECRA9TVsSAnZWagAAChUP/RVkA1pq/hcJ4K1fTCPj\nrHqDR0hJmdIrGbt/X6rGRekSHa2zwVj3shtTqrOCmBKdRx22mE+wHC10UmWs\n7A+aZBgwcqpjfLddLUHienyT+52/UarPpt0IFqmGqcK873EOgXZlOWqLT/WP\nogjSu6l0KLwsAu5cDp5xU8RbkAzNkY10GDBkJO0I5cd2yIxM0G/KUp6zuB3i\n75S+K3VyXZq+l0fkFrfyFqbqvankHzNVU5Zius87Xp88c+scKaUnfE9VfRK3\nUp3RtVvW1WRWw7dlBxJoLIBjEsHNh5maufxWr3meJ5WS763Iw1nfhOB5Psl6\nQPGWRkldo/1KfS09qfyjtPeS2NSoEHV64cbnvkIQ18tpeMYR3nuX426opmDP\nUEc7RuynIFKbq7EFRJskYAG/97R57X9pwGL6FsqcadkzFQ/3EnzhVdiixBCV\nCYLEbrFmZHUkrut0zn88nFCYy1r4E7gg7pIADFbSpP/mbRGq5TqK6Tzi3j7m\npY1fVBWboKjyE67NjB4RTw+P8sU2UTgeyqadkgr47gedH0X4PYcSgdZg6YmF\n50zEpkahRbYPqiAL+CnfFxC+yD4LsPEmzgfoLnAX2mpB0+KTenzb0voPEbpA\nmS9of84PjjF8LlpOibP1dXZSCUVTguAiEp4Z0+q0S/WIH8UAqVsxShWDKwMZ\nL+r1\r\n=vyTl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1ad1c81b99961b7dbe376ef54151ebc8bef627a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-MJpVj21BYwWllmp2xbXPqpKPssJ1WWrZi+Qx7PY5hVcBhQr5Jo6yKwIX677pH5Yql95ENTTT5LW3q+LVFYIISw==", "signatures": [{"sig": "MEQCIAzIrJvg82qvQDcezEmHI5B6sEcauKLNJWIeVTGKLAydAiAg5s7NmTze1dWdpiG73a+FtCnMSxaMzSVCst9f7NtqUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm8CRA9TVsSAnZWagAAAyIP/03UJFN3QXII/84ICtOI\nVCMS04ekxGHoO0DpFUN+LEZOCzpTk/uP/2m4msVU8sMFaAH1O9m1nMI/YWH1\nM3ag9rOnKeUM44kMGOhhUJFZv05AqvMhuwebwNilOmLkOf5kqZHzEQGM5zaw\n3srf3KlxUPq6BouDxydv6jqAUH2rEWg/vGgdN8TOK460TmhsGqbOUMNc63Vc\nPA8RMNTgrrgTMe5aNFp9rBdws3f4O7AW/ALick195nxhJeLRS/PTo3CpFeOM\nKZCLJj4GJgnqeCzP30ah+dNO7hX7hJiXDPP9c10aIJpe6/7sHKEOctW6AY8j\nRQ2cw1beG+UHUtFbH6yWinJXzn9RiH9N26deoyV1/B88XA68mt6oCIMiE7ZB\nkAunv5WyqiTW8HGBhZ3E1zE6dn2bzwhvzH6OavPUASFw/j9TAi8Uz02jy2yU\nHI0MsYiWuvHBgUALEaB5lfYxj6JDFVwgdU2lfeZ9TpSY69QbMWRoLP16vcti\nuZ40WjbN+vrG8yxqYerEOzEq15UdZ4yVW4SQtP+pWrgxm++QFw1jfkR78HUK\n9P2AdzvP3um+090K2zo/yIMcIpdfgy4ZIPZ9fzrJfNnkhIQPrBHwCrpAk/A7\ns0uNaKH4Jcl5EKHGyuUUjtWrxKcq4fMBZqdb/uYa/lDUbS3sa5wZdMS2FaF/\ne5eI\r\n=tNLS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "94348fa7e5d0c9bcc65a867081eac5e8c66cdf5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-F/0Dd8HG68nOREBEzClLORfOH2WAlScCWSqkBvfjvA6rzBdwyDXoUULTcBjBIRw4jWcHDQC+fc+lSASVQq89Ow==", "signatures": [{"sig": "MEYCIQCaRv1PPHfihU0s4QkEX35kOx3UncociMJczcfippxJnwIhAK0oGkm7knsoe/D4Wa/M2rRoL3N2bLWP/gaTctR3xDLC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpzCRA9TVsSAnZWagAACmQQAIznDdqDlfamJpWSqzgd\nL7UUEQe3ZQajx3Zm+NJF7OMBP0tpOKfuIzNg4rHjec/cp+LVstAp+ITVoHCB\nXXslYdKvVYlN+sm0MJVRmIoW6QSuNYGr8jjpf73EVVkn8UtcFVJUVdRINXeR\nHfjqHcG1lr9ZsphqIz83D53h41D9O1fqOqhDuIYQruz6+JtWyASm5hpNGM5N\naL5lhT7fp09Ac0GFh2KnGcH8eopoRFDY//eUmK+HDVVKUxagViFPEZgzAZuc\no+eCUg3viFJpVhLxrfM+EUKw52LbO8X9n4EL0aXkLMadDsnDQ8b5MDv0dH7M\ng3VL9brGDiLOhDYooutgzqmD9VN+fFknxmsAVv0jHEnijwEJjWz40hDC859u\nIz0surxuQM3PO737V/Kk9iB7EUzaKvMSxGid16DTT5rGr1aE7RkdifxKcAmc\nmFxQq3bDwuc/ocGMgYNY9hbiZlWmDDvGczDHM9mGTbFfSkHHANH5A9DpH7z7\ntTCKFzILtRUiHxUYMyVzrTLKLJxTnzroBcVrf+ahm4+CURGQz2TIb/ILzbXU\nulIGAG1mbbSOB5JaD0h3s4OQV7/aEqgZKp8MbddYobkox51SK68T3sPhkDqs\n0iD3rj2js3jnJBDgMBXFHwSU7J469PvK/x6WMTdROSTOgCV7Ys/6MlBEmpFX\nBdFl\r\n=Mea4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8b8125dba81370938d93ffb0d8464e56da46f6b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-u1/8Lpoifg+ElT9g+PxYBLH0dgptN2Ls1GUA8MIUwO/bzwQD3OBIFO3zpHSf5RBdrXKrJqKe4ccCceSKRX9FYw==", "signatures": [{"sig": "MEUCIQC5fCHAMb1DzU95Oy2uNvix0paTTS7FiSVds71l2uBU6AIgR6BeDRQSFYepUHrgcCXkMOZQyRRDF2ZlTlwa07vhDU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhylCRA9TVsSAnZWagAAP6kP/RqDucZQU3jVS1W8IHN9\nXv33CS0RmkSLuPnFC7uU4972c8QQK92DHpa7k8W7w2ZGF9XqAPVO2BmWipwP\nyZiSqq2/ddZTIiP7+Nnxg4cdZc5eU3UdaT2xzeBLBilNlQSb5u6wPviZjtQX\nUB2gSLzfyX4czIyJ1N7sVYi3g5bQJiTSRtTR5OlFJdbQrJO5szkqheNAjM1D\nILl83cQIgni5GK8M6h2JgAS5589HQxrQ0OSa/f/T+mrVB9c5rtdUWjsbFZsp\nOAWpUGAJtlEzbxwmg7VtEkXli23as9TKlWSwjuglA+lfUSkL2DgfLsgbEFD4\n2fn1huGwRoIn/BUMpvhdMY4yL6Csn6hZ6nVVNwkVM30I+wep6quRxWRZzQNY\nl6YRX8jUtS7+WUArS3Gz7Cod2JUE2SRyRIb5FV0J8SseI//k1cB7iXQuTu7Y\np2pcNVz5wqPZxy4Ll2JLU2kZpsON5UujxDU+/34YFIf9tdWfmruZnN3wiUW+\nfE5poYo5ShzlfFWqhyEjc9H1jQfF/Uih8o4rPvyCxOYP6ImhuYCA+38tn+Cu\nOkO1FGyEIHBRpI83JEhYsB46SNGoW3iiaweBsVtLnjg127qoY2souoZ7+iIi\n8/V1cHCvI1HpTsIRJjUkAv4w/ZMjGVO9JmegbeopYNzJ5gA5azkPvwl9szMy\nehWR\r\n=iqTj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dc80cb3753e9d1bd992adbad9a149fb6ea941874", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-tDLZbTGFmvXaazUXXv8kYbiCcbAE8yKgng9s95d8fCO+Eundv0Jngbn/hKPhDDs4jj9ChwRX5cDDnlaN+ugYYQ==", "signatures": [{"sig": "MEUCIGspYEF10doEaXz87N0bR3u9zFcAvFwa4GnA7wokXQUzAiEAkuYiFmjVuGWXyrlnuifn0o+Yf5flQ3FoVqc3Vjbbl1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmwCRA9TVsSAnZWagAAai0QAIrarO+VYC4boAtZC3HH\nXW81LWiyaFPaaRsVT6O8yPqPkb9azHtQHmkHBavEOexw9xCuZIQ27V+7htDH\ntk5ucGl0z1xJ6MHXX5pmzob3eKhwVDqhC0UNc7yvzi8uEyg7C+goOTyDpYj7\nyPjGIdzc073qyjlxuUs38AIpZy/fNnrm7p3/2cTsGpd8Eyxy9x2CKheIPdWP\n2U7RrsV8WvAxXFFsii+Cz7GccPX5iVUnVxydNkSpZXRnj6S24hrejTyKR9Od\nKWMH5346YmFFxni92YCISvMannuVtwjSKaCqs44/E7akCA45Ze3v6SLIS7HG\n9G00VSw3A9Dr3da/3D4QGH7ETKsnc46QtRHJ9XcYGgOEMLahlk1M6rx0Td2F\nVNziIIMH5DsYCTHfaTeT1blhBnwdrHMGPFBXPNCDuwcJDqlRWqVkmjvn/uCS\nT9UnWjNquo0Ya/LCgo0BDWa29O50DTfEJq5fSMmEiABTJtjGWyw4xWU20u6Y\ndg1lJyDMya69KT+pNP9fZZuhOC7phrRjkadl9ey1OI+zyTIkuR8dghdGh2rF\ndLJ8SJKitz8CplroAF75phTGWrDnypdVMM6bnUqYAxwbugrrEAsdgIJlGROY\nN7c/diDNy3UJpZyLbVtvg+LBCnpDEYgHv5CGhjdDhT4vsnBGkETX9g2h44o3\n1Rqg\r\n=WC3f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af5a3f29072355155f432bceeefeda5f11d9ad89", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-J/8L+qt3EO2g3rSnoxhIKkgyTcAP+4GO/G7bNYW44ICy2mhsVIyxP5RxZMA67lZxtZrmDfvaco/LljwxY0X7dQ==", "signatures": [{"sig": "MEQCIB0CYd+fyQGOKj9xZqxfMOLZys/eZJoW37bGLP6vuhuCAiBTytnST/B/Bfzdo/4ZFB3+SDPpZpoU5aLKUHJIXqFNmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWASQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbpBAAoycGQq9oXWfalS/rciZZjTxVGhLxe2ebDu4EzuMFRwQUYUka\r\nT0g7H0BT4vLMfCC1eih7ewSG2uW8wpJkGRM+vDr5JFMUwf9z0H8wL13M9gMi\r\nUpSyJJL5yMqsZZFXEZ4pra4wFZoUdoXei2CaQr07mSHn7ybbuO0tUyVssV1x\r\nnNMYecjWcqTB4vv43SEiLcJKOSA/0+cwgktTe1mywSpymUps2INKpUnXXrCn\r\niJQNyzOlUHXdxlIgvCIF/i2IIKNM5EwkgdkLjm12V48hWAUE57wY8U+bzwRi\r\nr5Rh/lx71Zdg8lXqIkO/yqtE0J/HIEnrumIdpfzOBEPgnIMZFdQJYh1b8zQl\r\njHWQzSnFeJP3b0K72bSX9sAu0tq1HRhCGxnVtJa8mazR8zr72sRqIEeVIqHN\r\n5vHkKyhpGxOO1tiKPXvu67h0CPVQrml8zQ7em4UYwKmmIoa0qGCpoTrpqXrw\r\nNc7Wccm7ZXWEGAca89gLrQuLtJ2qnH3PPiljtb+yLV94C0AQTSnpcAlHEyU5\r\n5kjVI8nNdS2DdXdj3Ed/bgieGh8AFfeyHgvN04NK93Mz2CodMjLUOZy2Ehqc\r\nPtIKYYC7SpadSjYSEjQ6FuyMgRspdbHha/CVC5rm1/1e/+RI01SFqdogcgeR\r\nI1auKnYgDoFMKurWWNDaJOUwSnXXY/sSh7A=\r\n=8G4V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf0b8a086ea8eb45b0b305274fd6b65b24fd613a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/9NRqhL2A5T962q6abjSggv/5Kwkh7Q6RyU3EWfhMHYWswvWXvQtPvU0foyeawdeamGFGRQaWhvBI2F8PWVL5Q==", "signatures": [{"sig": "MEUCIB+cKVas+ETMpRIE/6xuyRuob5wPNf+0DcKB8Qnfe+LlAiEAwzEoCmUd5f5erx038PiSdHMs+ZqVFU9rut6c+UNFmkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJtRAAm6HK/qEziqxS8lPB+c45SznD40pXE5MPUWaeXG0c3gEGeUqe\r\nnns/fVjUWhFGrU4m7RazEVw9YGAtJoV8R8oMO0cV0vabWI33tM3CNdVZatfW\r\ndab0lJ+1ypozWd9bX4QQTzhO+hcKYl/VFQ6RrPBayuMzOCSVyTeX3x+o+zIk\r\ndX9CG9Iqt2HPc4RK1hry1zqW19LOK0vaTHDzLQ98vUVxeGs/FjOTeD8dXu3x\r\nTmkNPLdGCWCOQJOvlDgy6uM7zFWS/pF4l3ze1i6Oe/JJm12NKyLONYy5ANCs\r\nJMGPEO6tvZ+gQasdxClZFLZpx63aD6OxnfdNFnZWZvIEU/N7ihJN3FoNSb9H\r\nZHheZzzycfQsHKghEJLJVNXmHZd+YNIPC4kzxG6sVIqg0bb/2ud/rcv7Zv3k\r\nbjEyhVPaL9rbJEOCzveCjAdUnRpQFbkhCfBGdT+E+huh1nuc2MKgEqlfaoFa\r\nSqMM4fp155OwX1Z98O4myxbRsEaZwJ+89TuuCOFTnfdNU+P5ZAQEdm+EGreB\r\njd/rSnBiGR7Vm3YO2cKc6w/PeyuEOCNVS+AsLUXZ4dTlVwDwIvedMWb4rMkZ\r\nHnMw8MStLaP2oLEzKirgEbHKXqSQ7eUsEQf1hw2BXyf2PCk3Noke2cTDVi2z\r\nTYAAeVKnHIKO64ewmHgL3EWcWqR5yHjeqms=\r\n=8XgA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "24923db84626b33a41de133a8d49509eb499049e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OMFCu4WiTC7Q202n9GfNJX7JBUTL/h7XCh7Ai1DtiVTbIsd75Wz2hgSkQHW4l+tUi3C/2K6vBpBklhOQzq1mHA==", "signatures": [{"sig": "MEUCIQCsqaEdipeW600gm5zBYH1tDy/aQkK+utDoti8UjKycuwIgRQSn3OMSJ+Her7XfI9RWRIsFLcEwvkNh33918mT/DVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYuw//baiTEC9Gly7t+mY/rfSKUNPLiZJ/zX37QMJewA7k7YWZHCrK\r\nU11gzjCXcPyaFkAqo1WtB6tZemfY0aIepRe6KGwkXHtXxINP4cEqPv1GSfYd\r\nZZCuBFJutLwK70Dwisk3icVWgdKz/mtNdbJH9sI8LXrWINEXBBoI3r93vMR+\r\nIm6KRq1XM9M1TWqNWXzJOIX56bxnaUP0kS+T1nPfqP1sAKBVB3JttHNYBdov\r\nIh9I1mG1q1cAT+Ejsa5NG9FJYhKS8GsJNuTddqAlVAyaWHp3pf+z8V/KPZvm\r\n6Sg10fG0dyT292sgP7YDZvWpTIg8zmAFrzyIDYHDljxmQuBEgfBw9h2Fcqi4\r\nU1S7lNo17Mcl4Mtdn498fJEr/8F86/sP/rNfyov1QEZvLhBUy8lHrqE4B/Ab\r\nXtmPRCQhwUh+zEgNMuugwXFXf2UI61sBzg/zCWSvOjEDuXzOe9yFIHg0jHsO\r\npcVzdjBadIb8TphFV5TJDa2346xwrPBleWbQzL6TzSStR2hs7KxaC5B5tO5S\r\nRJP/sf3GJzi6fnciol3cqyl3zDXazV1WfrkGWt4omjfqyl0ZCUI6wJPPJebx\r\n8wzvWklEbFmrvy6LTFxgPc1isJOgLw2Mk8KivgjhLAXB5+soGWfB6lNeiiTU\r\noleiWuByUzrEHWrmkqeCQjUyaBnpz1wRQEk=\r\n=uZJ5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a06f8bad9095d48818775243391e74cbc088f87b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-qlix7ANsgETiVFmg4u2Rse8zGkoI13O5dy/+74j4WXIMrCK91AUFcGyLgp2AcgiMzfZyKh4r/o09ragtE+nh0w==", "signatures": [{"sig": "MEUCIBpUJM29Atw7Q+bORAih12rfQG4KDjUTWA18Pk6nbQmNAiEAmDmazFLBdR95QC7a2ay5pHsiSUevyaQMNEA9gvH916w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpekQ//X9ZnC4mnpSXAQYnBmzHS3/UCrvB2Q3eMtP1Op6FtAYFad6Jx\r\ncGafL7N6C7UbLdilo0Hd1kdZiaFon45c4OArfAbtH7jAZeq60b5Tqh6SHHTr\r\n5PtFZxNYdf4fKuAHPifjyDwtpHg/ebvV633YxnQa60crW9oYjfzdTOTrr38O\r\nBmJ5i9P4T9Qe117tHp28WiOxx1E91Q5XolZRlNFUKp3z9H60giCdOcW2ELBm\r\nPrh6heJDwxr2KaeJ8U65/hN6vd0V8Jvk1FD7LsrdksySATSG3RGOw8eahxfD\r\nPld1v7o9i1JX/RI0/CcOPy77jIl/KDLEremJcrZEglTgd1NJP499TY5HcjDR\r\n4fBLXV/uDFh7oU1cY8hRrC3XOHfE68j3YADrZzqlJn3BYIWLdPkMQ3jFLlX7\r\nEhZHJpHHyvAuQ1AxWHGCu6IKixBdImZOUWMs9jnDp6AKphNf1xy5DgbzICwp\r\nw/bzps7YDsBHysV1Rk/rw1ZI2Mya+gtc0r0nfhvZmI5hpgOrL1RxnU5BmEjN\r\ni8ybqf2b4fScwzeHszLNAGcugO6DEjGyIX+U0KSmLgHpUu8ShMVLkK/kgJ3H\r\nQ/yuHpeKlqtqbYZGUF4OMm9BrCUmcDVPWHA3LpbWpnULBb2q3uuijNRj1c+h\r\n4Oo2VcRy50SfystyKuzJ2AFYhabBh+FkbiQ=\r\n=D11V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0abc8b9bb6e4cd49e0333b49c392dab93aa36cf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-iA0ZCX+kF7kTum4s1EdDMTXiYR79OLtvFJiudpQvCxU1c/THuNARqiBKCx9rLqD1EfSdIhUzHo3EUpJRUTMoQg==", "signatures": [{"sig": "MEQCIDJV0oA/7PvNF14AOBLy09YOpTdl45bQlncljGXmDprKAiBqysPpFRjag+ZPX98rOXcSjig9Xa276sPziiH/DvUH0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmolng/+K+0e04uQ1xogNUmhGiiz9OiDuOjv1p8CHnbVwCtTcb7Iw8yZ\r\nXh6wdJo5ahUvCu4v0HsHoXKOId7TMchIc+B0ecxYirPgUse6wrOmo3qiAKkL\r\nCU3dSNu1Y//UFBsE1cceExyxy9EjRJMl95PTJqAmRKqx7/jWE0xBlGgQyEh4\r\nCDsLzv9sgTrYjkXZ80wBKXXyb7+CVdHWEhCiAsGDR4X9TUOU18ugpC2JeF5R\r\nxl+kWg18Vj6DGz8tz1w0aoUs46hdaQsB4HUVsHg7frKFFkovRTx2/1D8cKSy\r\nGNooTIAOtXM7fI1UapoWvengZgFmCfoA30JE2nkFevvwR5Y0V6pk2aD3eYgJ\r\nmgwK/bfHV1iZW3VIbVPQ01GdxgvHGZpaH9EoMwKw5dcKW4uGUs39H5jjKqSs\r\nQPOUpmydxS5lrmVLDWhfoJPp3bnONJVuIZMJMh+4amlRZTQe5HiMmFAK7nuT\r\nE1fpo1QUaqpKi5+4Gkke4crq1Z9AAa9fy6l+U40XjEu53As8pTChLSwhRsP2\r\nqSFV4KMuUH6rp/2tX1GLts1XzGEjnMzLxwjj9RTI9OUZqhcvznLzhs6WJDwc\r\nB027/rnFyW3ZFeOqqRhI2zlbNLg8KQ33Z7Yxa0GR8JMZRzDhGwqIKRQZklX0\r\naPBzJLCDdpeRdE74nB9iiPEEmq+08qHx4/U=\r\n=Z7fy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e77a21465e4682f415725358f7419646ae5aa4f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-PajI/eYyQU9lvlFWMuBzXMVVRUJKi93vdjm9wkhaBsIHPealc16wgJo64JCd994IK7Sa2rbo8n0+85JP4Jjgmg==", "signatures": [{"sig": "MEUCIQDHZxoYYrlHtAIDKBqWDGtB6KztcUNo8A4VOc0DXLP56AIgTiGAMRlSwOA9EWJzndZNSTvM7xCnGh16j2x+jlUAqg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8ymACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAXQ/+Kww0dNrpM6VgjUOFh+zXWoWxrBCzHKmR7tkm76XHN9HJfKQB\r\nTqZGzJ0QFvSxJ9F9OILwhGpCf6jbBR0Jd3HBtFKIewP07PyulDS26w0PuBdI\r\n49GNx9QDwKlD18Xc1URhTTfmOQbUdyV2bMwogaWHXMQArbQmQUzt8WvZCCvd\r\ndYRu4DRfaD/EoadszJ8MLIkdHfJst1cuLgn9BEP1u+RYKvAiqQKV0cjSxxU6\r\nDtxKsroWSxK6uu8ZDi1CTmn0JssRAgJ+itiV3lEMZRUdqM0EWDGX5vr29E74\r\nIPzCgU/jVM4kN378C8XKK0P8+PGLiREXDwr+NzIxEX34E2P8TKARPj8L2PGP\r\neb3Kml+jhJ1dlPQsZi3PxSTnd8xXSSDqBCMXE2tFu3EEkcNr8qLgyynrk5o1\r\nKWyYy1/B1sKYDXTbnb4lCcNqRd7/O1D4MSvjMwMF4KQlsWHWktnzGD9Rak0h\r\n1avutzu09fa+ZSSPW9T25Qo22ejbGZTWobEklCSjOmrDFx2N1cifRYFE6S1T\r\nDtQI5DTlminnt5Puc8gSACMCdkIwXX19bxb7lrvnKSK1OKasbH4cht+LjjrE\r\nnQx+6vHD21eHYXqX3hzNCZrPouO8owLtssUGypmIK0h7N+AdRhOF5B73H2HC\r\nNBtDpg5OMrmvJ6GqSo6v70XF6uN4ipGMPhM=\r\n=7voK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "535eb08bbd6b0dce0d82675c20b63011411b1a36", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-I1zbxMyskSl3VtJYXnoEyz1aUld2pjFwhJIT7Y4MWDFfNnVmL0H+i9qj/JojBpo9L8Yb4z35GfwQxuyebMVppQ==", "signatures": [{"sig": "MEUCIQDB/ulif/bUqL0SHHIyGYkfneHfUPSboDu0D3+B8Jzz+QIgW0HTWz7kvn4jVgOKP4znfxCHU58+3LiOIwx0GBkq1bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRwA//fs7Ia2m4WUvwXksLfJG0E49SMzfktYxfFleSnMiFSyiUqTHC\r\nt9MZ+k7A7qIN049vAauz9Xhbh1RgShSEy/cn4vowy6ZGWbMkovpUa/eW5cA1\r\nImfKXyVFh9Hozvrgmm3wvmiOSB9n0xAYbT/XlbEwQa1GYGsjwfsAeOkClS5P\r\nHf3HUUYHsIzlrtfUq01bgmg5F1ZBm6rnnTPGrb7XQUjfFeq7iewGe/PzlYL6\r\nKtk/4UV7rs58CExm+Vyp4bpB5zYKk2EDeJUJn0gXwrQLx6Naj10yhRBMoUk5\r\n9/WsHgJFdeKwqvssIMoSNyzju36Ihaaxr9GVdJGxYLbjLKJdP2Ko6Chaji/B\r\n6w708+I+JaRa/23fNaPnKuFv1ik/x2Wu57sqUj645NdN02TYZWV5qfH07CpE\r\nkvGdsMt5I9OO/+Si3d4lOYPsXmrU79tUn661oQw4MicZX+I7f4DnW0mhlAKs\r\nlxm3zMPC/rXyXMCUPCU8AobZDeY4msX3onTj1NzC1lKdmLGrAnDrwQza+pNT\r\nob1XQ482xaYJ4sO3R0KDnUTtkWgS1vKTUDEou1t3Qjzlh2D20zU78SX8JBcu\r\nhOHwjUuce/0tmZBsSDQhlirKLKiftG5kLUD8L8Z0Po6ysv4IYpIXLrMEyvRd\r\nm+ybNUkg31F/RxbyQ+B9K8IIdLxPhhHd4GU=\r\n=Ahl3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.8": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "580ff8c1c583cd853f5a9c54e896247c9043b007", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Yh0aEpsYrEK7bUJYnJosz6OFMzEBRldwoC8m7ExNA/hv/uIHhNQRoI46R0t7urHHFNQXXOyV37C/mWW81qi+YA==", "signatures": [{"sig": "MEQCIEjZspkP0Ea1d0Hdiz47hd5+NmfBi/fgW7ujWagdwLv+AiAIzNeWjv1hqUWve2XT5bssIpQjVT0xaSSYOAbLndXnzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGZw/9Hb6TO6XuuUn4edrybGlCPXdztfvFK5+fA1GMfiFt0+gkBmxV\r\nQ5R1DlLU7f1Z29dTqs+gEWnP5C+hLTbg9U5Kg6TSzF8WZwbtSw+xWv8tRWvW\r\nzmQ/8uza6bEMV9qjncWq7y9iXXoyn1TW4iOh8IhUDSy8P7H8b7/Xo8UWzhCx\r\njTwsBtsB7pg3vPxoxiLAUHf6ZhphRrnBIFtCuQ3Sw7HiRSqCYA5rPfBAfuWI\r\nvg8AX9DzdYdydW1bWwWtRBGjc8GeIfAFL5gJo0aLEHsam2fYwqPJhhYQ1//W\r\nlf9bbsB68VWg4RR4UUE097lMGIjeDUqS8cb5jG/R6wlnZNpCLnA2zu6BetCj\r\nDGET7o/9m8J1Qv8nG3yxcET0F82DS4o/EGZUtgZ0c9PAJ/uqGpaXheR5UD8E\r\n37mbhKHyZW17R0ld380hknWQAM02hpokqirfQR74wowgirAbWNBUx7hNwD0w\r\nn23qfx8VZFfjCzKTAoiKO7yE+S7XkFGErJvfniBe+hwoekZTKCpQmdnf8kdG\r\noc81ICrRrmznNMAC3wDOjpBMDF3blVp4B6akn8lT3NPmXKvBOuHxJw/vFg/A\r\nZ9J6Z2rflPygh9SdwrUCHm31RedykKhO9flQ0llPkb58vIQkao+ERFonS0b5\r\nzXSU81NljN3HDqwulL1nd+W9lPKvsitPX2o=\r\n=6hPL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.9": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "85a7018db76962a72cbc06473dd91e148d189fd0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-SsQeXkBkCNGWW00t4cyl6VzBzkmXdwqp/XrWs8YH/z4mqoEGiN/IkovqFN053L6BSzU+qcC8AZd/E6xImCh3XQ==", "signatures": [{"sig": "MEYCIQCZgXXDuIazx46cSbobtCZJ6m1JFBvh9g9T2kXVT46E1wIhAJdu5MYikpKgCgYEHrDT84UmxlaC6W7Bn63WQ/NfZC0d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNilACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrumRAAi/CYGTpSR6QXJ8aKms7x8VgJYm5LQkER2VK2EW5DTZmnfe3F\r\n9V/ozedgMqFzIVzqjOgkE6dt6GJ87I82QOPJ9NZ75Mo4zQkha2H/WnJ/2tlj\r\n2qwTUgjGUjUDEZqPNyy0sadZd0udmJ4onk4zSQPO1ioVqcp5zxe2BEsuigGu\r\n4UMZhxsTE3xEK58PDM9KFMBIXNS/2QiDjYRSDgNbLJuYdPX8e53L5kggQt0t\r\nbCXojoBbIiuKqwk7o7IswY3FgvlhRRXZrm8XHDk9Ql5o99DXdJCFNiVH2Pxb\r\nWm5PjUqcaAPeMFh30Vo+DVUHipPtgqlFFpBM9iH4oyowDobkthV9GvW0hWPH\r\nHTWOcIGEnFtmXUuUDnnrgAY1vxbNOR5A4z1Jp7OE0wWV/FErqqNNvd2iCaNJ\r\nWSH1rG/DvN6dmkVN/Be4JIOe1JnBsV1hpT4AqAqb76gfxPOW9HngreEDQZ6s\r\nCkr9a1tIm3xMEmAVcoAN2On3cu85Rj4qJ7pYjqezRfaOkkRZat7sj0LPgwYY\r\nMLQNoyeEpEheUhp0/Gn0vVuHutfbN1mr120R4t9XNXuS2MSTizdwpB8hxkmC\r\np6Gp397UWqcCPC4dicNBYcVclUPi84BN5zqW7+VKcMpwW8aY6q4faHQp+r2E\r\nJMkGGiZzHCv6eG4BK/Jg9FMJmh6xR1Ceprk=\r\n=nlLN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.10": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6055e84a304967bf1fff7fd18cf7368e3923e3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-XEWEmZlqT9OeBY9y/PJ4REx0a8KU9mpbV9iNnngYnJD4/qIraCjwgaCcHPNeBpY2V9hoLU+uzpH9P84u5wiWMQ==", "signatures": [{"sig": "MEYCIQD+958GBqA8JCre9MoJtwi4t3jALts2PS11BmxkRjagDgIhAJhYl40EWQbyS56LWJypX6IIAaPdniSgGYRSLaAUo7XI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEJw//R8cqZGYGUizIOQqLKGKYG1GGXR7df+PU2rYYZSTj6tc/9/hb\r\nXeNrVyKyZFyZBujuOdsuJM3ex7sRcVNbM3ukbCLm/CrsxCEJ/VVLNuNnxn34\r\n4ATUmJb3lEsMSZ5X6PrdNZTYFH6hEUzS2Hf0f6IBRTA9zE/Q940Hqd1VAEiL\r\nuwInaotQ9qsrUCRR+qGxFZBnp7J94Qr4of6BUQVeoXCW3loUbLOuM7XokThg\r\nGkhI2uVXqBAbSIGZvufVzHS3G393vdrIwy4Snqt7ouTIp051bqjazes3eo4l\r\n/flsLH14w45C1LZm83p3xTp9Nv8fvhJXRbReUUdMpJANxL2xSemq/WJlEYQG\r\nOMvVFrjJJ08AMb5nynsJo5m7OG4c7LkCHmCjwGjebS3I3mmgCOXSnrhr+gp7\r\nApKgRrAqvq0gziWk6wLUcpALOzqn+68aNGNnSGbaS6ri7gg+a8I4Kg11V6NV\r\nl1zVbD3TnKl+xUQ7arPrIq5LzwMWLyFjAAZEZGCl3+ZLKyOj6ruvUhoSiIOc\r\nSPlovygPad4C61Qqxld18bP3mE5HtEGnPX1dR51tAEStfcoNqxTbnH7cdi1e\r\nHRHStoTnXtgC2sBrpMIsiXwgguy1MQXii0lKs7f9leGO3p+r+cFoTygJ5OkI\r\nWM+INICAycjUjz5N2lVnUdXeqcti7N5wg7o=\r\n=Jetz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.11": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "248f4b91488517e65ce2ee42683995cfc7f64a33", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ICDvwJaEaVK/3QsXZtpzbaQHNja5o4UVEgU9uNdpxQa6n2RLBqbQtqAZ10J846AnKqR3xWMWpcnJeP5IM4DrpA==", "signatures": [{"sig": "MEUCIEDjOclKqjl9LXAMh3/p3emAscWA2efzyFVrNFXxPvE+AiEAhd3OzovPTaMbZFV28TcQW2Mex3sD5Pb5uPBU5wkAeWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSmAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZ4g//dCuv8Sw0AXOBPpiV4WQkeSwV1qiaMo+l0g7L/PT2Pqy1yDVn\r\nM1nQB94vyVYKPzUoNuagPNURePSrwd5JWjA6cWBfSTLrxaX42qzO5+MQ8JnY\r\nshU1n0QvdibDrxNe8mPZKAflV+pFRoPugo0a6a7yYsoZjFIaxyUjvIxYms9l\r\nFo3wrPsou9emwA+PH0BGpM3c6f9zPRJ803900KpFknd4CY6ueC5K1LV+allW\r\n4XU61dyQtcTHDd/1agjsu2ZWjj7M5r+C3rOBlG7yy/3urnsb/nvJGgR+qL6T\r\nmbsRwz7X+BqRKHGnZQuY8ohOpq7POajM5nUxO/y1wljK1+SY1s9MfQu2bfjR\r\nTa0eKHWgbkIozRNtPB7FFlMVbtwJdiv4zlUD++vyVtJbv8zRnUS+uCsevIjI\r\nOhKixfon2161EOEtILSCGyj/c9At/JXceHevrvFv3HxKoKVKWdwCnnuRst2C\r\ns7JB6wxNjxpl5c4F0tI8FHKUYOfxsn01VeZN1FPvBLmSzC8j98vJ33pRkQBF\r\nlZU9yLxvI4PtPC3IxLzqJUyO9Tqi24Zyyf4te4mvxbVqs+3LlZEfC74jk9XC\r\ntKZIp1C3OXWQVQijxT02/qlqfce38nfez3D1m2qYTEIRXHcSIG0A7CQ6FDLD\r\nh6iPEHgrcqf6aOp/TMirvd4E9xkI2dYZvSs=\r\n=OoHe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.12": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3683e3cae3a0f12ab0aa5eda2aed58af07eafbc0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-iFoAw5Hvus7Mmi6mIeESvBbSTIGKr5yZiH7hG+xhtLOzGmCvltFLUA5/w0r8Aheeo+FUqegx/y4E/VDzh4acEw==", "signatures": [{"sig": "MEUCIQCobukrD/RnVjNWgUWTfBeZQAAflJ2EPipjYyfdfcsAyQIgQ4gUKK8X1gsA1mn0iMRhxDz/2Tjqh++HZ6az0MiAyG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo0w//Q5Acuu0aRGEJR/vjrbTagwqVxE8bfuvrKhfB049iOkJWM4/O\r\nLBS/x4nt/VRazTQXvWcl5A8UBdeH3L+2vPjbr52D9jtTtFMCk/ieQ9T4xtFV\r\nmAfi6i7k+s+V6U3M824Yc1ABGJ15Ms/YmBsmB5PT9lQLWbY8cvxdSk1oF6u2\r\nafyAobQLs25KMMn6YtBBoirPaywdWZxdJKUkQIUEkv7AXzT76k+6j4URQrPI\r\nvx6pTRF6j4lCUjYOujtHOnFqyMq86tAe1Q0PpqshYvoO61RblwYzuHpsM8gt\r\nw1UKPUvseTkLuBbO8ONyQDYsdwzR0TUVkIDTBAje7SVbQaBmvE/ZdY8VX9sX\r\njA55e0/YPMj9Hbo9R+iUeJW87nfLjM0LIOCWU+QXfqOth0xdXSuxfEr8Ui9Q\r\nD5webGqUyPH/1y6ujkp7WozIh6MGE+UwskUQMUVbo5Bjr5ePHj9qtOKH5z1/\r\nHNUoMqqEbBz43KyP/PoQrjqu6TlJypFq6ibHon10jz/RCPCpyelkL0jVNFbb\r\noS7I7ROKiIFG623R12n+UvIpYylwORnnFTQEj8AdH+C1bwpdGKA704rNO2uz\r\n8Xs1hJ+Ps/vuD4s1xjM3gTZ1k+r/zxWd4zaGGvJ7xxWetpTLGgNO7GVGYu8q\r\n6V94TYiqhm/IR17yhvFEl/1cxtwTnhkziGI=\r\n=Sa24\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.13": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "32e8036b80446acb320c3fac591dc96d23281fd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-CMLeafIV7Z2pApJftWoltRaFRB7qnTEIihvHbfYe5PCXxL8j6qURzgPOyIGN7Uizo2JtMI4vzJkuDRbTyefs8A==", "signatures": [{"sig": "MEUCIQCpMkRc8+DKGaIi0YoXCTfRPdDEJdhzM1rm6fD19t0oAwIgFocNpQD6bhbD8aoUb43S+aH7czm2rURi3LbFNVFs0Nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepKMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiJQ/7BSV7xEeMWhH3SE0PJHVGCh3+NgbUMQ+sl1hjKJ0MvCkj9ao+\r\nFY9m4SRtuSKhYqBKJ5YOOCsvPEcalh6moiyT6AvDuKkjih6AbgsOBorZ2u99\r\nqKMnkQBhhMVp0XtN+5KYfm61HBm4uGVCmG5R/ns7AT6QjLJAz2xML6+xPjDz\r\nfx6S1ftNRWrPbKI1eNvK+ltyN/+sfDpfPWDRVTpWpn8RawBxj9PLVtkcH/cT\r\nQHI6+fiMKBoXAkLR5ugHb2r4RMwac1Or6/SfMXiPDgyp2JuilNL+tl1TCrEJ\r\nY3Y243ZO9WAHDVJ2XkQ6Zv15FxUJEeYOSbMSyd21ycDiMb/+Ofrc68Nm5eAP\r\nM+kLwtcKm0jNTKsF09DRjwAZvpyqZqihg1lJQt+podo0paBd5OnWmtcVeH1b\r\ngc23sQHlEPVMYzsmJMPE4B7ofIuYm1mZjdTpfg/ur7WT5J02pNUh9pdqRUtJ\r\nMUhR6xGoA6WOE+UNKJEyT4iLdT6apx3kqaMtp1JLgEO/OpOEgwOE0OZHmZ33\r\n1nsBa0HwSwRLfV/5e41n6yqHVKat/VgKH1fdfxeg6gPH4PLGPDsFCVynBopR\r\nqIB0jJTPwr1d28smJxPMsTVbtV48niLDLioKXMUrVY4qormGCgU6bl/xGDj4\r\n6BuFNc9UqRzwJNx6jeOnG/5zSw7BXY7exvg=\r\n=NVwb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.14": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "03d1ca7d951498c3437727b3f11bd4542417e1e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-dIoy0THiSq5uNXfMM0Qsg4lpg310kYTCGlEfbqLIJ7h2V7dBHIMIK/1Z54ubXLk6bUhZWcrxFTRB6HW+1154Cg==", "signatures": [{"sig": "MEUCICAGcrjMwww3XDOIHefWAnM7JxatKHmgkpDO3roySWWQAiEA9HUgAyHhJFdxZRmUxP89nDdmE6YYx2JaNqj4RDnAv7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpmew//X0+iA2cX4MGaf2L6cTwa8B54DnAyqhpZxUh2aexD6TFs2Kvy\r\nR3IzQfvWIVKgjp4YnTQJMezuQ7ufP0K8P2wFtdWOx4Lvd3bTrkpkbdngyslm\r\nWBpPlYNfJUC4rQLFFy5rg7LMtwWLIQbigzRcaUU6RYEMtKM0hB/R4hmSRzT2\r\nWdwAj4ESOq1dpomgSe2Xj/KIe93r2en64szNK7LVuDckVJJglq3gz7Vfj/9z\r\nOw2DxixUH67bDeDvBl6YHyHtJGHgywQqvc0Ot89vYeNhnydtgo/YiC1oeTrj\r\ntURGpxx1cmkCHGixApQDBA8J0x/OKhQUnZ/sU4MK5qeCDxMMvTWuVN4do6/s\r\nf8/TykXnF887zKT+A1JZkKbaUN60DKtWqF+fYvX4KKhyfshzmwpGu3yKaSeR\r\nAv+WZznEa8Pc2INHOzmjcRBD1bmkRWKjw1Pttwsv0e2xJLYVSLun/FvlXABB\r\ngvGLUpDUy0PAMNQr2z+26F5nGwmaNi6mkUY9SJdXCPudDpWKRxkn/3swY9n9\r\nquJuW3zRVksgm1pjhANJ0VHCULXOXhG1+fNcICeYUkE5ntn/E2js0Xlc5quF\r\n6hdySzlP6BVnyqocfVUE2iAv5jcOAL3z3FCwwBIthSf7twnGNDSWPUbY808V\r\nzCPbfdIxxKcTclpPYkMJJ0NVs/gYDlU7kRY=\r\n=5Weq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.15": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dab014c178b4f8e5b8a8d0c4b73bac1ad00450a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-KLqseSSyPEg/XK6en+g4lXUoEgwPIqjbUlDKEa0tie2AZNTdM0g3pbLn3KBfr/DYdVQzWv94Chob0kozEdmR4g==", "signatures": [{"sig": "MEUCIQCTg71V/dy7yqD5HAR3pqYOe7gYLK9eFhpjXq05+dBfmQIgQ+6+Mht6ympNt2qOFi5Q23lfApSJBfkhE0o53GDB4T4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA1GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi6RAAlX+GRZCuSUwkTGKGdpYY5vxfSTFcJnc4rl6MOfs7R6CPvhGx\r\nyB9Y0gqf6zDTFYvGAPv8boP88QnG9gaoIvFPIy6sReDkT/BjOEwaIrWzgb3f\r\nIXZV/7vO81mT8Lm0CmBXHQNzykXC8t7ruPkOemJebM7sw8DKSBuCuO7IOaXz\r\nefiKYvvr9j1yjw3Nc5/9Jvchbexwn9MYeXPp3On2U8hnoXCN1yDvmlBUC6y3\r\nl9V3VFqBU2wrOL3pIeo1/Gy/paWFZRj7uKg2zMjP+KkD3ok0OFs2J32ewacM\r\nQxPIJE85q/Q0AxmXnjzXcQrGjp70pqePJpLlgVOGdNoh95wq1fxbaLW7sVcF\r\n48bMBeSh51Is/qn0T+dzmu1DQuYdXWENWIzJZ3QekhqZymP+JOFk6kJK0JTu\r\nCB9tQ7KRsyPTmk8g5erjizwapaU7lsrD48exz9ipwlLvIBfKLhawkKxod0xW\r\nwppgC0aeOsT4d9Z592YvhKWTebESFvtCx93QALeuDM7IYYO/T+XNcrQ4klDj\r\nU2HHCSZhKEtDYu9GqZTVonMRU5bWKVc7hBoj70ESn5YDC6dG1AsYGF5LBVzm\r\niKA8HYXNJ5cQvWSqBwBPxbfRgaBHpuzBMMG4/8UONW9nTmvSSG2DZlYX/K5s\r\nZnCSNl9Zfb8TWCEiMNqABBR8LeV+yJWpOBA=\r\n=WmyU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.16": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b2f6c538def52175bc5c43b9fe5c5f43a797887e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-QrBthlRUHk6+3Gktgl1vkGqLPhAoLcvwnD7hMPjNN5QsMAUClIzD6oSTUX/dPhJTtwCMA5SN7a4oQ0tty38KQw==", "signatures": [{"sig": "MEYCIQCqJuYO0qrmQY7haWG3TuWM35a5lABYVeL8JNvU1ePW8gIhALhkObCr1c1329ww16c+fXd0id3Q/0LaeEZreWHL9HGe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTssACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlQg/9ErMovJuhurWsrpTK/X1EJJXtsUCJmVcow49HGunKZbT9ktrB\r\n1rDCV6ZFKG2VrO/FwMzkNixjohpGoGY9W8/oBxI7z02KCEEDGyekWYhaem5C\r\niVW2ioZ+lBRXScQAWwLyg9Sv5ptL3Gpq776cM5uPyZ+0TFlqAN5nyMmN3ASd\r\ngdSsHl9TuAdAHTHfZ44g0/ROaWfUS4DtVl96Q8Px3Xm5SK7gHAhbkgGedy7v\r\ncWcc6PJy1ppumyZfIJ5FyojIkuw0+rYaTs4anCYb01qhvumfG/mollF7d777\r\noDMAo5Zsi79mgClVFYC7vzuMUFWd1VdMpDNlEMHDQoRTDGU9HTr9S6c9IXNt\r\nXrYN2KILUVMy/zMLaIQGm/DvH/6BXWZs2uzCfVKOkp14/XZ2fvC+1Ls8tL7V\r\naMagImRE6o195Ssvk/JbOe5F10lYr49JUV5egNKRNOHINeuiJP0NJ5F53OSd\r\nDqmjBtQVRgFxCjXMipKC478P+Rdq0QEneAaPNRuRZDbVUf7Avq5lMcrmxi3r\r\nw3+QClFYf7TIMQ+LXsrXIHPst2TSQ8STCy0qb5H2ls0SN80s0AkAys1SG6Cv\r\nocD+vxmLQPRn4UuRXTChZHjjMEHEAmH8jqC6vFAWY1+ph4fIBgKohFV8GQ+i\r\ncaOGXnNbgFwovC7OUGrROHQVn4dTTZQqazA=\r\n=Arz+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.17": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "66325d8f3cde1176946b2ced8ef8bb8cb02be18e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-dSPSw3H1GysY7lifWmJKaxZqo8Y62oUvWlFhWAxePEcttuM/bPeCn9oBzsHYpkQmmKGjAaLof2JsKZyMbczj7w==", "signatures": [{"sig": "MEUCIQDBED8D9g55kYFXB1a4//4GH1HvdCvE/wi0aMycUNyNYwIgRRN5IiHAo576crdb2pqECpJdIRKTzIKrVwzesANif28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmVw/7BTDb69EysXMO7Tj30+TFN4bndrVAi+qIxZIRAsb42bOHeeIN\r\nimBt25Iey1qV+IbUHYyA3ICGH7vwBg/LQv3ktwdRScYf5jp7mjAde7v1AB/p\r\nmUnPsKwEpsryIOr9phXDf02DbpTkxWCSztm2hciMU1ZycaCWMxzAOUbc+xU8\r\nuec3qIHrNgHLYYuHi8u55rk6o5A7loen+yj4akqjICFUmV2gpoDhmoBnVMWB\r\nTlPeNGjvjcOc9ZDUG50iWY8HJWMGugGYzEtxueTyFDMu8FKnBYq0UGwR/BrI\r\nW5bCRKSnKkvopalTEZJ7YY9aB4zXeTuJ/QiLb0fntmWUa5VMhhSrWFz1C/O/\r\nTgfDqLiPUaN99XdU5TVqFVRwLohXiUjiSJhPobM2QeHb+X3g495bwtu5eUxX\r\nzSJGKwFFSdkqT1DVMOq64axJ9UPU2TTooL3mExXIl/JD2QHDGP21kjcirRE1\r\nERf41ApafwdzNVvS8WwgUkKrru/ugigK9/rGQYymWc4mR6z+2Rbh65JZiGfw\r\n4GnpRQg6TbbFUxDHBwlup+yTlsBJCu9iNlS7j27fCd0EdU5AL9jL+IwOZu80\r\nV82LEco1MN87l2UOU7eiy9PxvVc1L3Ya2zFdMDgbGVjVsCpZrjSwvU8C+u5q\r\nMQFnx2XBbJ1w8UFDYH2/fDBmQenobYPxA4A=\r\n=yQQV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.18": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "82bea76e7191d00023b4e1505d8acfb136726c05", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-z+syfHS88smrl7YpNTFNrUpT8FgFX0ejNnEu8STXyofuHr+K6uQHv/jjn/ZFRtHKXTPgkqL1MIxR3WSvlMlFAw==", "signatures": [{"sig": "MEUCIHqMC9HzqJQ4JypucFoJb/UtgRrOyCtHHe/AktwhgSK0AiEAo7aO2aLSDkoJ6r6b9fdcDL3PAsXBF2a0DKzutquCL4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ09ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtOw//RAgrn0jdfxKaYz+oZCfDicMNDn88mnMG3MCw/WGQuxt8xVQZ\r\nPFAgcTyJU+vLxj2StvdvHXySa8WbTGGL1ddgM4Z/Ya59N86s5XC1nk8O+Q1G\r\nwXAXP87TEsaGI8B1KKagbB5kug5baig3e2CtmNktZhwDaGlqcuS6lxTTvqRp\r\n4Oktn+buZfEfCByL6ByaoZxECEv8+8bzhn6QACx2TzFitau73rYOnG9AI6Q+\r\nWkoxDZh7pGLttb7e1tTtAksijWcD/TxVG6Zf+RleE19LmQgUPxxl8ajyaK7p\r\naEp3ittopHbrNyuLI1a7XS5MqRgoNXoO0MWJsgzjWgOagiW4/jzelZOrIgrc\r\n4FRg2gJlS+3cZg/NEkkbDw0XLrMOdE4byIM+lowF8zNb7HCdHohZempIt76n\r\njHnxYFQGa7mWWcfTK6wulbf/qqxSE8NSlsFAxISrZb3M+nOzCbyDuiYFhRJm\r\nGxL0oe7Hena+3NAWgClhni+Z5WM/BeuqTlCl7kxQKNIUbWFyZY5JfAcScgQa\r\nuXBUAGBoXpCNpxtQ7mDXU8P5ruxKWWfkiLEn2wjdcPT7TghggNBfekE2p7Ka\r\nSDhD27QTUZgz9odNySGTWa8HbLFPK/fHW4OFsPqBFVLM8D2InFZhwYu8pNpM\r\ndQnk4kECzuODlClCzjDyRKHP/xVnilgZ1gA=\r\n=s+5/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.19": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "147d08ef082c1f2e20322569661b12d839f23cf3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-rnCdHwjgx8Nb+ytAZ8GZ+4pnDznUVjHyIt8DiaGt4DdwLc3Csfl6pfZcNZ5aWF/G81rbPKeNqCMb4DxhBNjHNw==", "signatures": [{"sig": "MEUCIEF/AlKUqYEJKPNm+5xZVFnDDTWdrGZSjTWEHzvz4E0BAiEArzXakQ06hIcknckwVe+Pxb85I0NurSat4IiMTt7iXBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDCA//TSFmOG5Sojs1qtz6GylgN8pONlNSDMhd5DTxiWgluHTUYlX7\r\nWJKdoEQFmAuFiYP0jZX77n9o85835RJA6KcRrWDfKW5GQ/w4pzvXW4pHg0qC\r\npeMa7zRANLilzh/QmVaG+1NnZxnyBsfSePoRTgt29vTxvA3HnvQox4Dvkman\r\nSJZtcmHv9Ueit890UOio3QrvV7MZXDf+HwtifYoYe1yCy3HGoaGpKJT6tt4U\r\n+tZVHBkhGmm0N8lc6qc8HzLN14SDPPe5ppB1OcVolI2yABgQBRWfrS7fdpI0\r\nsf3aqpWLPz1UnUPH68epuMPoO/eeWzIoBQ04gQkZH14LCIxJ21Ua7h+/9z3G\r\nVIkkjkQWsftgZecsKz3C8HARMz+Ifmm4X1Tdb/Eho7KOUuD1/eKCQ4HmCpto\r\n8SehE01ifYYhFQmtTD1dgeio0E9s2hhj7aRKSTvAUhjf9Z8iFjJvLqqyROJJ\r\ni8Pye0wKXkJwp+USWT4NUzDgCKJ+5psKk21Et/YfHrMdyi/WNnEDiUFH/DfC\r\norsmq/WkFXPs9x8y/MxXDuY+RVKIQ+SOc1oekFZGykYTCtyUQL9VA6ZWl1jg\r\nw4RoT7KuLJ1f4k+9aGDwsCHHjLOk3Wxw4wQkT15EdyZJ8Hni5T+6kGzVslb1\r\nq4JHheGkRs1bPw/d6IwN8Gq+WzBQBsgAeKQ=\r\n=4+4D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.20": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b75877fd6624d7b5a35679eb72e45461988b5cf8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.20.tgz", "fileCount": 8, "integrity": "sha512-VIYx7DhNHa3wcbbqZaSY+89/gVUivL1hosl/x0ziGnrk2xxOCmX0w6QUivMYMYzKBb+6aOm6Vt3SIm03qG54Jg==", "signatures": [{"sig": "MEUCIQDPeVdJ+uBIIPdUFmxCFdFazKK3mpAjkHMFQh2K8CxVUQIgIpbJGWF0+b7go+FjYpLvA+6ouHNOFoGtNqJg87ISiFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3cCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7nQ/+KfPxHSDqhqSjde16MlYm+7WcS9PjNopQLmIFO5SlNUol/Xv+\r\nfkTMVJH16+WhQ6JEVku/IpI/T7r04kSXYnYJFqhGbdP1IoaoFvMWtrmf6e+9\r\nKYePoluX6AnyZPZYIPPTDbjdoTgyG0dWq1Hhth3aQcOafNJL1c2POcJVi1rD\r\nvRO7CUa/Dliu/tMDtQjGCrvRpqjRgBtVPIUBFMWWqjP4IOv2d0JK1sb4iDmr\r\nZNRPHmAGZaEx+mIz/Potz3HNlhWSOX5Hb5/0PQVmAsrywhIRxWtr/IkregpT\r\nrrzwC3mNtytUpRbBz3a4qP/m9lKM9zCrm8V3voxJrPpIorzVVWA7PDK0oWJe\r\nR3uwO7sIcDZkaci8sG6mqD3ce2zmW4T5aC77A9qwIdliKtourO97bLG0nRLq\r\na6Te7EkpN6TJbiUQ17YIPySqt5sgJD9DrVbfRNxLqSbB9DX7tNe8lcPqBDfS\r\nZ5LOaqwtYXIoGFBaH0yaSj2Rynomjhprk6V7EEz49RDC4CZylPESbIotqSst\r\nrXZXG2H+qFXF7rk9bbjc38hxpXX6k5/s+L6XRcPUB0mhoalGQQSR9CcIivbS\r\n4Fv1s2uVwrThVV2DHIU0M/YhGPBzgaq0NX2X9nzrsLS3+/s9pSN486MCJ6Ds\r\nC9wl1AS0gmv0V1c4FYcm7gJVuQRAnrHhycU=\r\n=naRx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.21": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8ae9fb10792d32c96b231866021460a9f33dec33", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.21.tgz", "fileCount": 8, "integrity": "sha512-rDTG/XA5Rep/OEeYfBvq6mnD2LUrWKXNF5owVr0OdndiUhywJfHAZYlmQ3v7QI7X5ZDkfis9d6d8YvXBn4OggA==", "signatures": [{"sig": "MEUCIFyNN45510OF88vQeKTPBLwMuq9tb2J0ZxpaVHng31LHAiEAyu1Hw0PiwVSPxFXd1J7PuNahT9aPvRLEGBbcwA0a9gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0TBAAk0hY3a+ZIbWPNt02gYPNOXDwRodO7mtVoshASrxQq9lzLtON\r\nL8JEq6uyx8bqZvS12Kz8Vdj5mTZ38gTV1DtM/MvMIhy+qxzi7RWxiqIXk/wU\r\nwiZebxh3D4CeBLL8X3Io0ZKz6UXml/8hKGd82WbYXq7IDBpjLMxL94YJsQBU\r\n/kpbxiGU2ssBPBdyOhPb9w4xYPsr8568CxfcAsY8A+Gorc+ZLHTM28NsZycX\r\ne174qaweuZLfVhWLmBUSVUm2x0QXDpm+6PJMGHjdWQd0N6iX4WGY9UAI/G44\r\nZvQETOn6PdA9pjHUA/KcUxhvUysb/vFaH0PaSDMRmxeB4zFlxKsH2Hjq13Io\r\n8/I1jTQwed9Yzrv+HYq/iBpdj9RxGNyd901/RAGWw04Etcp9HHyMcjPQ8/zb\r\nn602GnNjjjaXV1AGh1oaO+TDclctGhgsUcKYIM61DoU8L2N/t8N4aNvMhtaG\r\nivGSkPGFW0YkZ+tSpD5nbiTvl7eGQmZOzn5PEXyfztJRDsqfxzwCgxdN73pN\r\nE/QmU02LVRg2Qs5kIuG8eK2AvJfXE/SoNQv+tLg729KV9ErkoXk0wDA/+9Aa\r\nQT/COP8aS82zboP7pdMPr9RPIAcBluM8GgMFdt8lCUHqaqsOAWmeli5XiZvW\r\nxHN423cUQp9zw8IBw5pjFHYfI0eo/+p8MsI=\r\n=LihH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.22": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1da26f00a2f3532afed95227daa368c0de9456bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.22.tgz", "fileCount": 8, "integrity": "sha512-6XF27K3dg7HZ6CDCky1tfiKxhMBOaltCcKa4Mg0pS7lrbOlF3wWahQspCygqNIjRy6nl0SWsw10sGo1PMDro/g==", "signatures": [{"sig": "MEUCIDzRz8t0dGjBSzapmC1UWnHwqLBgfbjZNZg0OPUiOTr0AiEAw61e7qdeawKWt68cHZCEHSZuqrAhA3VrpcdtQ+neGuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrylQ//dpdn4VC+xlOzZjcOr4jsaO8icHS6lAANWZF0lwjfVOr/SKjD\r\n24aJOmTd11hgsLL3MxLy0kxj18QKhkZsnc7KBorrYml9z6P+9tSWO/L/ctcF\r\nJEM5oTavIp1JVf2l8UszTd6ncTe2v28ghiSiDDGE+kqcn7ZR7gmyxYgTZCpB\r\ndkqCRIF3Ga2C2VErVwhATK71FMWjPevb52eMoigiBpGaPqO2R6pGOitt08/t\r\nWgrVi39yeMDl5i5dWB3TqGlW80ZBLbIVDt6epSOxTaZlLvTXT/We0x609OsM\r\nkT4o9yILB5b3j01TdkIwYQRGv2GfzRgfmpNGbxaUgJHbg11t4S3Y3d3HuwwB\r\nQa3763mROM14HgR0J+DkO80nfP3qZaa16Andb5x1cq23pFjYlhHvSxEk+s6o\r\nFyCnmu4Ux8vKJWtgK5SE5VeSvWmPu+VeLprup7v9eWJXI5z1YUvZHkdYq1s0\r\nFKnAM/9QR401Z9Gbxpux5jtkbuhc3vPuxP8E0yJxKvQnqXEFulhAye0FOBPF\r\nVhuvOShu2EDXND37DSeVhLBGIVh9MN8jmI90xGBKf+8ldRlHqeS408PRlogv\r\n5XzKNnkyvFLikt9nEpCGktY9T7SzunvzUHKrhZ6brddkOyxaSwKQmxoOP3EK\r\nYQ9E1DRUg9yuiXTZfOduYHCQZ9UBotWBABg=\r\n=heaS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.23": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "384248288037e032b3150c7d68e384221c2036f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.23.tgz", "fileCount": 8, "integrity": "sha512-5oyRUvY5MVr1Qpv7VN8mnCZ/LDaN2I8A2EAtT58UVR0TLurgrTsT6xCxcgxld+vHVKg/gqCdxr1WYFspDI2rIw==", "signatures": [{"sig": "MEYCIQC1B3h7nghCo6GaVjSGY9k+20KNjl2npofQUmdJ2/W0jAIhAKWjOyrxewRboCJM2aGIi1OIVUSJk7q+88q15sQpvHLY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKH5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYzw/9EfhXvoJNQv32Ww5Toc5MBYqixVUb9yoU32atzTWzzVWI5SvR\r\n2jSxn4Tny2RJR1XctgaVN8dSXwnxLHB8Zu8vfKfQbrCo0wCrOF7iAiIM/u7z\r\n3ax37HNTRMxhbqWhjlWqrP0vjVYEOk4zBGduZLNRnf+xCYvFgMsgxDXga0ap\r\n5SxmzIeTK58rwSjsyHnopsu6JkeIGjghnHJ72myqOmVVdA8lg8M9Q5Rps3nd\r\ngd2oGVdD/g54z5sbhSXecNsbcCeY1NTv/TamuzZhdko4LPQYUspc9PQCL1Yy\r\nJBufBCDE7dMIHdzgeplzt4NZD93pVLu1Xpz6Gio9FL1d2rJR6Itl39ngJGVq\r\n3ZEixFxvXv5bzer/RLqWCRp34GMful9QxF8M3ELMasA8/jCQugqldMZV2qei\r\nQBpaj2am5BQw4uhP/b6rlbZ/pTjy2zcoZRZdBbhTHK0/rwy8Wy329Nr4kDZa\r\nhF34XAeTDJD2VqA4MuQhQkO03uul6DQekib7JoBofhl+JVcr2HUvdXV7S/K2\r\nTMbM8kQiX5rpfIf0zQ+GMl4G2v8AWmj99XcfT0tre4GXn+LRteKrxaorGD6n\r\nnmhluaxZPEx12XNA8KBxh+StbtJXzEE7m/5TNg/69RzN91gpmgFc888GnmNX\r\nopJ5S3CN/cQ12mNwMg2FKcIPtF1BLPwIQ1A=\r\n=GY0O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.24": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "550fdf9595392a967305b7c9e92c01d5351cace6", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.24.tgz", "fileCount": 8, "integrity": "sha512-ewmFNihr9kQCZFHuPQ5XGz66e1CFyIJiojrw10jrsqbJhAWYAfYoRdOdqDXkwi/yNVAgtY91nVoBwCZcrtyZVg==", "signatures": [{"sig": "MEYCIQDYfbY+2YnIIojKhH/XUPx+cG3KVP5BtCyaQB9ZXNh8XAIhALf9ABgAjoeB8U8VqDlGi9AtCpdiELv1cy9G2Xc4vgyq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLiIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooFQ//UeZUJ48QFVsQrhXXZwIIFhcvJu0dpHCo8wjrfL2eI/cdq/DS\r\nXq4ma7jrfvZ0fpIwS+OKoX/hntREeh9/QflBkgtUbklpQ9Sl+5lgpHyeWvp3\r\np6ah/Jd6wsSMBr+gQ46DIqWkGe1+fFwnMKgQRPT0fZDF882J/mmDrGY1cDxQ\r\nyTeVdE8GW7LgWoRMoIJo3Eim7hAz+A4TAOepcobWPnFn3S3Gz1xMqFb7Xl8N\r\neXReM7AkNODMrbvouXAgGqiM2gDs5zk770p4v0sptsR1BEpK5qH62oZt85Tl\r\npfXyTZndIAtTmOfSIGCkyp1X+/y7V6Yza1mPur9SAftE3iX7gKogiOpo/Ew9\r\nypC0UQW+D2J0hgzpJGE7djOUTFg97fmFYkR8hBmn8WjoRX6kCuF6rE+A8r/7\r\n1YRumhtYzom2LhsU208WU9NUO3YSykdczn8T1c9b+HI6dJGpM4BOthSuDzPO\r\nvSNPckmNi97HG94x2SWBGpQ+W+x1IofvW4+3R1Z9p2ptxzxTvQ2wKab5o17G\r\nV1uSs4NPAYg9DZgBXrcVU8kLElZkztiDKB1rE4+JOkUsvr7yCkrpivRcV1LR\r\nzEubjx5EU2c8AVD3/hhVTJr17KObjTD1G26jnAVCEM5uPoEVG/iOBicwKiAz\r\n/Dtj0KQfG584mXTGj9JkjmI1tC+U3MqV3G8=\r\n=MOgT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.25": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a86b23b571fb007b99242f4a2352f7d2ad39f8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.25.tgz", "fileCount": 8, "integrity": "sha512-GcfmJXo77UsnViVPmxZICb6tMgEIZ5D+Tw6/BmdNuM/sEuTONHIjxSc9IGCS25J6daHjT5us6pMzNwHZA5VUFQ==", "signatures": [{"sig": "MEQCIBezQNKoD6kNG1Cfbysp1T+EBgmwy2XGJwr9xRmPCFgbAiASAzDAgMjSH0lqCLCWN/DIN0CKua1ugvQV4iR5ktxfSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogqQ//VebDv4CUor9/oqpq6GKXslVzh57LJlCl1nJC+DonIwdwA9OE\r\nmJjBhWtqBlPjdjVbGRVbS52mHq8VwryG2we44FrA/qfzbvNR2gJlMbM3f4H2\r\n2d13aUbY/A0kTcufe0YdFRel0R2Md9i87Jb9vlpIjJLiPZlTbuGB3exvniI/\r\neZP6UoLuXixCAwPA4n+PnATBzavznuYpCyJNCBz2yylZhlyLtgvSNxT/M35B\r\nPYjRATtPBS0NX57ybno7VVp2WDvaEKjV3/3LLR1+CWF4oa3KihV+J5RlF+bs\r\nlcbEs44pOdzQojmVrEN5TwQ2jAkMylpFusGFNMNMcybom11k69S0htXBNyYK\r\nzzTn/hfgZ7Aqz9sNgR4DJQbsYVZQjJpP/8t2p+e7njBFSXWqbgTeL1JdQyqQ\r\nJS6orvT75xaXOZbsojWyIABQPwovwVHesXe45JOxFzJBDav5pmZDbT9jKQ9I\r\n0NDeyfQoJlKmDinmSecGhbOAwF8K6/c+WmFdX1uosALVuELxGc3mnKVSyQ8i\r\n0fqvL5aMAGG5jYCXJ710INRomoiifIrwapvf5RWZnOp9Yb0BGvhnHK4Azhck\r\nyFL6WwN2+ey4roV+eqywagA8XfOtDLqyEIllHJjOBkkmlqfllPTbRrY1Gb7t\r\n2AJ2ARdFAjIAX5mx4HAq1HD4XUq+DL/DX84=\r\n=rTZT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.26": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e67cc0373442962bf8ac90ef0f342c2d33329034", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.26.tgz", "fileCount": 8, "integrity": "sha512-GxMbgUjdSZttCbnL+npOjHlSL5JeTfkCc2Lu17Z3jAkjW51jIIJvEvWukSZFn3kOw+76NJvqo4dSYBJcDFlk7w==", "signatures": [{"sig": "MEYCIQDxwoluD0u/K76rSvJhFOkmjO34nfQuqaNL2yGOm9t1rQIhANCGGae4lKOy31M+XjhVmmS6Y2oqUngntHeoe07qqztZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl16ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZ+A//U395UQUZDy70cWfTDC/KGpsLIc7X1ZXr5lzMdqP6FXbOOvV4\r\nZZg8vDlxf7iuiOPQs7nBC45sIH6Doyg2tKPI/Bg6ftjW0lm4nh/QYqfd7PGT\r\nyHXqgt9g3vGrmW6tk2qdUl5XU26HtK7Oiy/3eLHbE0o8zUMvOjKz9UqOffk7\r\n4/YAI4Go1CDE2aNl44slgoy551wpumLtM46dxqnotRzOoPILuAiRLqxjWiHP\r\n3BJZsjDlcZ0r07Qzyd99Dkbn0w9WoSYxDcIRjtKuLe63GizTp6HEAnVq7Vmc\r\nSp7qYoOZEX/XHydcmzeIMX1rB4oUOHqSWHG4HKF/7/07ibgAt38I3xjlUXTT\r\n0m7k97ARdCw2eCGaoAuXjchoLBpPz7EFIFZnI7a+Du9+d87c17CsrmPrs9sO\r\n8gufZ4HpUBu8Sr/yQQVe/4E6uHaTmrrUHe/Vq1orUynV/gt6OI0SC3y+Tr4E\r\n+asLHZoy9iNWyFc9qgbhet25bFp2F1Lx1DZ2xXpLno+XMTeSRlojrL97nMul\r\nu+KfglFX9aOXqMyaGJNNFgd5ZCcXazYpGvkH1o1udQRbSMpvdr4EFX0Sxsgs\r\nn/1g/zfpblqirmtiFRw6NN43JvATafvc179ydpUUNmOpGWxLE574TLG1cgTq\r\n3w4tR2lgyTRlkLSFOy23fNQllFbiHWzwC8M=\r\n=swVK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.27": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9042d3a69787073bc072988b474577096f58fc2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.27.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON><PERSON>RGK2+eCZ0Jo7l2O35GVj/tOUuC66+H6aSqFBar/M/QiPgH678SIbGpOpTFpEV248rDw9kpeDXYkfISkRHw==", "signatures": [{"sig": "MEYCIQD7ETyv5Gj7So11jdi22+65wVAqPWPexM31EEHAmg+LfgIhAPdm7Gs5yPKAFcFUEhvLmwzydpHK+FK0xf06oJVJyAXx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ3DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprtg//c8IZjuctYKPp1G0rEqkIHm7msoFvHmKa9FKWei0p2S9EFZCL\r\nugpVvahOAOnuRMrPj8u2gMZjs2p2ajOH27p9z6B3DtTrsLRTp1fSerooWtvJ\r\njBv7qoi81c5TkWnRU66d/vv/dQiMarUAQz44lQTx4tw/nWg9X9EfSfXFAnke\r\n4UyP9jp0dwNc75Pb09KbQWDG0UFhuPgvtUh+h4cjX76ZAYeHQx9CSwzmL7hw\r\nXzF90zQKlKNoi+9CIfK67+KwYZ+eX+8f11HRZCW/hci2ulWRb1JSfaLYlSOA\r\nzHat59kNI1pscT+lWQir8d+H1wK9LEHOe34uOvW1sOPIVoI3uCdKlukwfoOx\r\nPm1gM6lf6iCWXia2UsoPnLx2kBe4gnI1uU4rRIejqVfN8ENEbPKXEY1ZTpZd\r\nUyEgGP+ZmzAUrbmbRlvSZky29H9Vd/kQ0w50KQdL0jiyR820lcDPMG4935Sk\r\nSKPqqBhcxM2aqitRh2AT7xa+i1rVG2YDD6dNvVQPyHz+bKIx+Vs3Eijm2Y86\r\nZINnICCoC58+sKZyZx/LUftmmquZclUrUH9zCT8rWuQ2GESWrKxpXXMObZyO\r\nGacn15ubSN/99jFPmNSJJHyn3LPSZPwX/coKNoR4bMITjKF3Q/t8OlWbHXJI\r\nssRwzm0igtjTgPnfll/FHPtpf+IKSqDcNm8=\r\n=fEj0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.28": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a1de1adb331edc84895bcc6040793a361851ffbd", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.28.tgz", "fileCount": 8, "integrity": "sha512-zF2WfIrjZkcTX1O5RcW2InhATKAgdSwd+zm3mp8Ufg/pGXBnIXkmtZdJDmDnCf82i91rVMPnVY6RDew2VmuDmg==", "signatures": [{"sig": "MEUCICQGTXhIh7HIR1ypMknAKc89N7KhGv/Z5ZXXhHpO0tiaAiEAxE7VirMfDy62DCfQdY2UGLfqst620CKTvE2E9TV57m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildOJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoArA//Rjb2612hUdNPFdzT2hVUBuWw8YzVM3fhxP1ZdGzBa3aqws+V\r\nf5BY5ZRfyGl+D2ZwE0SFmpchaSqBADaGjQBQQEn29IHEZRx6c7gI30NExtl7\r\ntDFDGMe2FG28xh4LvU8VPN+5VuEniUzTkPDkhUrA4Z1PO0ZlsmQwb6xejRja\r\n4mH8Lxlo4s5XhdVOLajeNUcz+xBtaK9X4fA0xhAa2Uq3+BKqABph3iCOAGy7\r\ntMNKJEhTQ+LQNofAjXOpq5Wlj7P12+z6bQxw9OvRzbf1x55rdbrmH5aVtijm\r\nmbV7BUggZlpoU0euvon+THyFdRRKO0i7eevPveJhKF6DPEMvlJHuDScor6Rk\r\n1+mN+8ZWZXCnxMwF/1KQJNTU/mH1FyvJUTrSs7C1/vyiTTEUoUAlKoe0yzA0\r\nFa2oKABa5yStRzlyN8C11+/wBJyYTmkql87+9gsN5EFBT2Lq7tm7vryWnwY6\r\n2SeW+IV9GDNGAKD4P1ayUV/bHgv/73I3y0X3NJTWmlpwDKzIdOhPumCGUDdd\r\nOdOOOLOGhNBtgs0S9WQowsv03H2D89bl02e/p9bwmrtEfU1GKRKUtyJyxscn\r\nDyintKI1XZSlAyEwf2mDfvYABdLYYASz5elB5xbAHR/15MUI7hc0hW0kfUg5\r\nLb1oHRhqpe8+gZRZQmaaBWbb+AWJM+i5Eoo=\r\n=dR5z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.29": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d8793294423bdb56c4ee0e721a2a7b1f58332d84", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.29.tgz", "fileCount": 8, "integrity": "sha512-s54HLljjN1O7gZOH5/z6fcJeTTfg8bj1l3iAqAg29nCXbTEZYunRgGivNimweuxOboFSILi5OfOQtOvEcNiMow==", "signatures": [{"sig": "MEUCIFAG3RK3jEOG4i/xTgeF6NNdu+mhSuhjASmY7Ii++b8gAiEAxaHbA6mnBjokNFbmVIBHSvp0tY06KDpvzK0biLrVDgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildr+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjbRAAld9+qKhMnXTSekiXK1hGuFlzBPHED3OBR1X6YRFv34NewU7G\r\nn/Gpy0jyPrxY7mV+Ll+5HgkBIfcwFd9hMXlVTC+VBnZWqtpMbmSJUR+OJRQS\r\nQEUI/1GlMd1zeOz8nFGMX84yqN7+P+KI2TitVakCVeekp+GzfCErhFSqvjWr\r\nhH/3mn8w92f32EJAsMoPLlqdhS/nv9T19zYHloZ0wLXNNDRqbXCn8qkaScOW\r\nmyRhzANw3SRNxdowpEw4acIPw+YecT+MYp7mLfQK18PLlYJnJ8Su2J48NodS\r\nULcR0ODR9ARQe9mhSBLMFRiEXRX6Xmp3nlNXQP3UmGfgGfFiDxp2Y/YKva7S\r\nlVjk7tD2Dyx1qX23cVPHrypkNEqeluCINET0nV4dxGjf4OercmrkWtPP73vk\r\n0RAgy/K/5rpqZjmTYIsV/sS/AQTu7PYA+ETeUfqt9ZP6L8EKcgCt1OK0ogv7\r\npgJ2lBCXEDwJdLU/lWkvhI+2QDQrrpmXdzsXYg6nKExiwZ39G+PTBV9oW7aJ\r\neCX/5m1nOlC9Tmm8OqOzIU06lP1HV+3lS+MDPwmEMhyKrP92oLxERa46EA/p\r\nzFocSMLTTyf5CR9Pi4FS/2WW3eQ26YV6lFwCH8BigKPhVUq9PBFdfTDP0Edr\r\nc+2GbD95vEgOoqo8Dxlwz69fFAqvkwKBFBg=\r\n=1mtU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.30": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76635499f6f4317df2fc3307b01aac4985363497", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.30.tgz", "fileCount": 8, "integrity": "sha512-YOdK8ViQz8QCvdiWOc5V48xf2bdWB7thT6y6IHQQXB8nkw0WLADqe8OTfBSSFnCxEURP4A/vhslorKjg0Efzjw==", "signatures": [{"sig": "MEQCIG7dFMlkePTqqB6pArUoBzO2G9enxzyTDy7uMVqFMs40AiBN/B6xFNM4QL8ZlkeUebGsoHmxvXHlQOEk9+95EJzsOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile20ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmppwg//YItvJHUJbGtBvSWfjjjsDqB9A9BSJrOdqPVDJnHSfmNrRY5U\r\noQaHe7veL3ne+hNG4Mfd5hAKvzoRo65oa8GiORsG82EMfCyX5z1IVm2zDdh1\r\nntYlP/zsesGbD6eyvjmYo+maqXegVOAMNtrvgAXkiTsL+Gfk4Pf2ITQSsplL\r\n6wbs1M3rdvFUrA9P60BFyGz7e4OcmKRGsB+5d9+KfNadR6F4o0nXsuw62CiE\r\nke4uW+r2pPV8CAEQf/49Y+ZCdw5E/0iVoEm+lj1Byd++Avcm9N7pZnIyljfW\r\n4CA/F7kKwWqf1tIge7j6vO3KWpmkBjP79ZqvNEYnFN6tioBUMzuE5esnEhgN\r\nNihb16OSdSt9iM83+64I9+a4KmG57/XkxNzdpBOENCFXNl5pJAKBYLJ/X5Yr\r\nAS0XYbTqoAhIfe4Of+NnIt+6rr5pFLAaKcSc5wgZQJ2eZz9+mH2JIowZyoLu\r\ncOGexD97LuBr1sUxIm9nBbNeM8Cx8Xb/p0IAQ5s5HjlxVx8683iVmI9gRQBp\r\nBoXZURxw5AWckkwjIkn2zkBhRHKtmlGXe5PasdLkbqgZoFwCvgSTiOkleJUi\r\nJnF9MveT0InbTxcy0YErCOEziRwiHjCDtctI26ZVbSDWWDwb8E7PxnzY5TcI\r\nCjZFd4bZDEOcmCoXkGZCg+l4GncU80auLmE=\r\n=dve2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.31": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76ae26cc8cc000dabc33790d3f19549992f696de", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.31.tgz", "fileCount": 8, "integrity": "sha512-ZwYj8X1TnoHXhtjETmNzAkxVNHKahZwGHKa7sKDzB53fV8bymx1LAH/Pnw2U/C/AcKJPPFAPHO+X78nehv/9/w==", "signatures": [{"sig": "MEYCIQDUtGQ6lQG0U5C80KALTxWDbsXDHR/wdNy6sjsEGGMbggIhAKOsHxSkJUFGrEePE4fQgrjZp50scXzTQweu/RgwD43O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2lg/+L2ZQeX3FqeZH3vAcH+ufrS7Fa6bNiPbYIrDnjN7YrdrsLYI7\r\ni5aLh0Z7yRG23iY/z3zYlBTkQXT8Bxkx48tq8p0g6ir3uW+BloGgRzQRztpk\r\nKe5GKb1hKf8DosD3cLU2quG/BjqwS1OjIInvIZa+t3zFbJathK8blasDe7lM\r\n9pvWTpCgB7PcQRw2zEI+yne1JTkR0ngWqsYnckEFOuZS2/C/tA/0VRtcEhib\r\nTDlii2yt9RzPXxXQxyQFb60zLldntOKLQu/ZpI4f/F5kWh49Uzz0cwJJlWen\r\nnTs9HyufmYhFZaYO19j0mVW7ecAnua2TekmzatqugESQewXypLwP4GH27kB6\r\nrGOzXO3Oe4X0gM4t4dZp1lrd6o1tyS6kwxM29xqCEMBK2i/kYagLDSL9IHbv\r\nGqj2s1CfQpYWlXl9eRFifcw3zkYX9ZGoqqVS1OFpo6KRVe855lnUsOVXMxRz\r\nkDm96fKFcDFGqUtj09al0VXoZ1De+TXSCzdyPeHIlXuoQq2QYnmFxbnflhgk\r\ndFdmSn5XNgKx/1q4YozSIQFWHX2In15uDPnDaY/L3SNKVNbrw/Xvd0mdHRxO\r\n7pXQ2AZvgqYkE/5uIddCLf5ZH/gvJYtbDh4LvBQoG3LWxLUeu6zjSu7rNYVt\r\nbpc+W35SUG9vYpzbCoxycXb9kQh/0a6gkoA=\r\n=d1hL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.32": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ce4ed5d90bea0e1d19201dffa5f23b21ca1a191", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.32.tgz", "fileCount": 8, "integrity": "sha512-Ya53LWfsSVSYt5fOxDKff2lNl8Ok4GilipyxujwcvSS0iIjWEp+QmBgdPveKiPuwPL60cVAsjXHuYgL40RY5pQ==", "signatures": [{"sig": "MEUCIE3AqV3xGSTP1JB0+NT/qpW4qsB8VTDBrMH4/4vqGImAAiEA/89SnRpc7OrWV+3hrvD4X7kv8iehRiSt5pH6rWwbAnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniScACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLMA//bMXWRYJcJnFDwZwPMjlmdlX4HdWsCmsyUCpIXOT6EOnp0vjy\r\nzeANQ3qC8oPTiJJmXh7bbrTNwPCMwiwKE3Y7y7PatVglPT3ZosbZoA1kSBN/\r\nstHrQUw3x4mgjv5hf7VhRoIUCeRs/i3enx5l1NjWFYQjhG8863Fu2wO4SY4H\r\n9sIOzj92bPgBdq72aqSHcUqNXZR+dSWzPxsPFXeKgdCBYyMpPCOSERU3oQHA\r\nGJrSajt1b+stNKTCYVm9h03NmglkvT2zGmE0IJE5hAWg7aJQtbsNpm9AkJ2L\r\nOjw9Os1zu5A53pGC3S/dgXlENXTfl6ptNUAiFnOS8wWSW6XcMidkC8ueAVHv\r\nZRuJEyCzpded3SDPh5OKmplvEPIZrn6YmXilxiMlYo+6bYPm6oFNFXhgNI2S\r\nj8rx7tGV2HNWUAHcjuk23igK+eZo2X8WN7Rr2wjA7G6HE0L1RKLResiFVegc\r\nUrBKhk1DvbDX4nmn1MdeV6zo9b66eOXDos/Ruak0vuMAf+YZ4Jd2cJpMuiWT\r\nvUWp6slU+6KXaQO0yfWWoh23b35KqCnhC4kBvzvRqi8QrQzhvof0TxF6xKk/\r\nCDYUkMvltWJTKPQg5MPoTa/vqxCv6ecQlKBBXtRBbLFDWC/SMg6eoJNw2oJF\r\nMn/vtZy9o5pJj7uFyvc0Sx6321GPIcMjAug=\r\n=AqeY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.33": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "987a4dfd81771f8ad299acf1bf73d3334a9cd447", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.33.tgz", "fileCount": 8, "integrity": "sha512-JIwsB66Psxy0t2t/SlvgYnAHomP5nJXxudZ9kyT1pl1pQKsjv2dC0Q7QWad2dJ7C7UsyWB6aFdybQNDPFbAZCg==", "signatures": [{"sig": "MEQCIF7WPl9D+QBT/+n28pXw134MZ2U1GGOEInQMD9Nrg8sHAiAplWyhid4yflA/5aEYbMi+WsVR0vF3DsR2JW1VWYqLJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHc7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqO9A/5AbF9+Zw3KaUPCcr1ZCzbBZqd6l4UbuGtqE3R5y8krxT5zILo\r\nEKH3ageZRVmwegNe/S5lT4w/iWfwc5A7IBBrqB/g6EHP3syKmaFovVYqwzX7\r\nbWD755161uAaARjvaPYhDsnnPjQ10fCCaA+2bJ8gbxe5m3tCdxPhPWX5++0q\r\n2TB4S49Adla+w8kn1K6WgRGSXwncqjD8cZ3AZU6Gki9yAx5OuquykC4cXmXd\r\nYSQ8HEDlP2CmVXpcB/f+wA/Xhnez4qPAp6TGGwUluCRab96DQsJfX/tBOk43\r\nkBDIyl6MHifOQy+73cYaJMHEdrtj0TgpmdFLs+B4PASikDNIsF6/Mx1g4U4C\r\n7OyERVk9Tg0wd3jNitSPcJbUQrac2nWkNpRkEynBHKDti8j1G62ExuATbcbI\r\n28eRUx9GYzrfkn33Jn3JaaRFBVJxisUqamBbQfo286WMMhB5nNwGddTApWX3\r\n1vkGLmTQnzoNuzrIoRvj/Bslw3MAsT2M4ilNAc8S2/DZ+IR9jSsy5T9d3qN+\r\nRG26lwLLtU7fKOwxxPaiiF4yjVpxtt9+U5bD4ip4G53YGun7rkqPjVoNmObU\r\n4ZIujbfDoGr7KObnyIhB0/rxvlB9hd0d33fkT608ewSJx3peVeT1STjk4tBs\r\nsYBMupepfvVzphqwCoBIFALYqXQGythQwc8=\r\n=QFyf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.34": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0fa96ff2af217fb2b1aa959e02a9385dd9d4e0a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.34.tgz", "fileCount": 8, "integrity": "sha512-5oRPnebNKvWoW3pG4Ixgg+kHmVe7J4NXGcIbnS/rJmT3h7SqL2NzLepazk0HIxA3jBzLR1FdL0OxB4pgjy660w==", "signatures": [{"sig": "MEUCIQCtGrYOBYXzKJ2AGCZs2mGoI+gWHPgPvZ9YupyyRHYzIgIgDUFe5ZVXCRzTa5gV8+FQUJGPMKHYj4eLaPIYDN5qfXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppXA/6AyfUgCBy2um18EaWxuRYz6bIcm7nFk7OlSmelLImF9t4l3i/\r\n4myYt12aOWDq9wtDhqqRwKCboJHaopsb9DVZwj1blW88BhteW46cjYJ+XNsR\r\n113g2NMKLFGlx9xKOmZgxkrss8TgxzR6KcX5qLLHgsJYKlR7vD66uoptwUzV\r\nkr+Hx4jgpLi109KfhjGoJxm8ezfn8ABp6knHE1AXG1xjy4OSm+iatkxmjxTT\r\n457BnH0mg1x3EPVJQaYarszkGpDH1iVc0pDryEal+p6qteKGqt5W2tpw7lJJ\r\n9MEQS5UoYdRjRqEyxJwQHB+EgI+/75/8j/0sGLX9GVbIlcL9sL7HfJvSAy/+\r\nUJFfe6053DFGTa2vlFnn43SxykpH0oGvv+XpEU5HzjYz24wy9nlkVUwDzN9V\r\n0HHE/uEUcDN0jQ7XhlfuToU3vyqYK1qwCfMMYsCWBqY4ITr3NIdULEUBmZwn\r\ntQsc2mcdmy2022ZDfB8iVq+L2ClmuqCv1OQlTYv41Xn0mSjSgC295WPIPsai\r\nkV+9V9qLgauUt22Piv6K1XuwwARjLaaN4Gz0N9d8ZgYteHo4yFov6/iL+g85\r\nJGp9qB0Ftdmt8IQ5HV/dirgZoFMHTRoZrxkfNvLIw+RtA+/DBpvDmHjCjCiF\r\noXkX5NwkbVwcSbc7KStfte1kfLq8XDgZCA0=\r\n=k2NJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.35": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e8963395068594e66622001ce3946af81c5ad42a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.35.tgz", "fileCount": 8, "integrity": "sha512-3l9kmjfRQj3Y8YriGBjxwr8GusUw0kzAqLNo9IZ/FaKrlhHpiLzd8Y4XZvDBOaXDf6rjN6Gq7Qp0Iy0CRt5I1g==", "signatures": [{"sig": "MEQCIH7ItdGPywzAiReMqaPzrwabdBMxCXXVMWwTO7U8KE5YAiBSLHe1QmDTMP7pH8dfGWNuEsJFZ+PemO7u89/bqkcbpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJog//Q/U4E1Pe+t8LmALMcJn8JXUESLCq2mr1+VX3lG5urkkQ6H/6\r\ntI5erMcpKix21oSGYwDYORBBhXV77YAickQHo66Ro/VZZ9HK2VvnwUDoesy4\r\ntLjiUorN969DCLlKN48N1EV2C5406Mv3X9JLimtGl/ZvDBfiT1X8PsHRN/AJ\r\nmCtgLUagEgA3aTU4bhCWhW8+sLwCkbdjjzaAKWJKNlfZxhfnMjTqcUnm+1bG\r\ngWm4xpN6MRSr+vsfY9eWoeXEw/NNrIsZer+dUUOWvuSZZg7Lix3h3gTogypc\r\naZXV/CtR3LZCa9Z41nTWEfJvgFhArH8rweo+Fc3l7qc3uwhN01ceNEwRDJyG\r\nm80DQlM5M4Ka2K+Q6HL+wFrSlDM2sxBkilykAmFQTjuJkHpaQYIM2/dHg4uU\r\nOVtuauM6moBL64/2Malqel7aWpLEWSMK20x8EBvVRl1FGqvShi82gKpGcWp1\r\ngGuO0HCrvZiR6fEA71irCGUqa+BmoDRtUl5mDsIoof9CNssGlyvFYiCeayN4\r\nk+ddBhjZJsBdP9kvUFFok62z7Kbq+qzff/E/EK7y3kOoycslwkQBuYqxS+Zs\r\ngxCADeJ07tqrstIvtx3Utkz9SmipXR02ASzDhI1pR25By8VZuectkuTngIHI\r\nO6wCYfj1pLX5UgBP8lDfWe6ZUptoymV7zm0=\r\n=XaTP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.36": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ba94844a9e5e1447e8eb612527b6de59f93b9620", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.36.tgz", "fileCount": 8, "integrity": "sha512-xZ1ruRRQZ2IgWRydFSWTKtOz0jamyOvxzWqRLeeVVGd38wnaeHfHEWgXeLO4ot+mgjewSupIvLESfkBejzAmnA==", "signatures": [{"sig": "MEYCIQCByCEXnOXDiv61qkpDkBMxFx8EuZjlfn/OprMJYKQGxAIhALV8FDCcABVBC05lBYUR2doagDNMU0Oi1AMxDbDKGmlh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMbQ//chfqoyKIvOZt2tfSqtQ4zX7XB0fFpbW62MOQGyeuk89g1MEh\r\nj5FlEVZxv2mKaNFjWzEpcgXlNh5OuwhQ0oNwP1F7qBuifenIcrZHJTuHvrjr\r\nXJYtr5yucKIHAaFnpQQ7LUyKQ/1JXBrYJMcDjVSTt7XryftKX0fHGfsgM0O2\r\nTF5EgkJdSL9JY+l6Lei1ANbOlX9o+OGYOo5INjFISbOR9JtBWe2AXH1NHusp\r\nhfNFF28piKdyn/4RYQARdend/fi/YTRRCUxreODKasnOGRpBI0NAnDDFu18O\r\n3WXEJ2xfJmeFZI/t1MLf8GvALKf+bhqQd6D8lBvP4hZjx9tBkKFx2rMyfp4f\r\nKmpPmq34K8PLcKq6VS2wZnPvlFly0s3zH/d2GoBiToZZ83IQs+J20Z7df2Q/\r\nFcBejYQVsStk/g75a9So5wasDAuXwwXBnWkmBNk57CePGIB36uGckIeIVixm\r\n9ig12wspjRDnx9bW5KVE5DOmJ1v0TyZYsOKu51czk5u5k2K13HVNTbpCGiuZ\r\nbRNpzpxYQSLE9b2gk93434Tkp8LKLWqgVxDmb6MqlEY17/t2x+/vnVU9Qwx0\r\n2ozcpRjUqeAVzoeY3u1yoTbLeXv7/Hruq667DhmOu8DI7hKcVP9oKWkJD1K1\r\n6O1FhpijXtlfeY3d7/W7Hy9tR8m+j4xH7dI=\r\n=FPZC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.37": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bd2f0923de79d95d20202c3177138c9db238514f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.37.tgz", "fileCount": 8, "integrity": "sha512-7zCBkzSCtz55xPlo32M/2neJNPDUtALUFocRlYp5tUKgbogSF/EiqpyEZTwY4EmrODa0GO4da6drEeCxgjIPig==", "signatures": [{"sig": "MEQCIFX4atLPhN6YLaNyqfIA+95IqV810afgcLLQLajxzll5AiBDXWXm2ZWkW47efTeiRH1gYGL7VDRbr8m/cTVWB//MDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0onACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOCxAAiL/8FZbBrAqoHVgQhciW0uAlFeC2Yx2sqrMArkHvwDbAFApq\r\nwfZTs1OVNTvsgDj9QVqP1bsmy60tboTvmd7HjR8tXp6CPyRhK8Hp/WJBxxH5\r\nnqhM2AjHZF5MBPjRIVLdmm+hb61LB1Ahp9eRuN9BWVyiHCwITUWI0ojXcKVY\r\nNc6gsmKImxDOdsDAVuDGk7nGWPzA085zDt8IHubpAVV0Ts3RfDfbHEEDmTGZ\r\n+0iJ7BaxmzFT34rpeJKHVen2tMZxYab9RES8vH3+BuqgHOhZdRL3dkRN/EeW\r\n0ffzB6+4seV0SNZWFllkJq3b0Un/SsIqSaQ1iSGMtiS6bdDhI38hCDY1QhDL\r\nxK/JWiCc2SePKp3LDFQElyMeOVK3LnAssjmD/Mnz15uj0xg8hD76x/NbRc1U\r\ncev2g3A6CJXw42PAU/igl71T7VdsuCY8OgAp2M5Wzvh3TlnrzFIT0LvK6S9X\r\nWkX+5LY6tdnrkKKopM40FDtcwaoExj6+/ZIxo+RwIoGONCuVq59bVS2IhVmg\r\nR+cpu5Ftbod0gjzZBN7aSyzJSDEbxtsnd+kqYC1xauQcmEYcpywfqBdMDJ6x\r\ngWEbLZpB5bRQHv/vNHC/RH5cZhtnvv7Oulf4NLgvxUTU2TBv6aF4uuVdTaIh\r\nJgDkKEOPCBVEhzDhen4oFm0X0x0alneb3ac=\r\n=h8/K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.38": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a8190774ef205ae76145dc166b4099b77058154b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.38.tgz", "fileCount": 8, "integrity": "sha512-4CXEj8myozgb8F95oDgOXSt/c6f+ijVv4UasrLVYl7A9/02CEH/5cq3sR3be7bvB1QfI743g6TclbmCS5ktK+w==", "signatures": [{"sig": "MEYCIQCGV3J4/5mCRKimkCHLLfXs+w8pIubeWSL7f0Nt8EEGFAIhAJWUtaed7MWu1xCr2rT5D8D5w3XtnUIvLhDFtMJhXnfH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCVQ//W1R+4TVkcMHARNmxywHlbI7BwkGxxTupjYWhqI2hOgoDJJVH\r\n1rpJ01EjDJ2CixmCjYmjvOGetLUbBcPdMwd7l087opt5YnuTtDcVfGzAvlMg\r\nstuwh1AZngCFVp+aBzG4Hxn7QTZ7Te30BiHVJ4GbfMW7oU3226sR0Nqq3ziA\r\n38UkhLGvsjqI5dNc37wrRH6ZqUD4Q/CXbz/XBnUfiwHcr0Fyq4+zCPoOqX/y\r\n4zbxKRLttOCOriC0SS0SpoXsokwi4riz1mhIZtezkZA++epETpHrJ2Y2KczL\r\nFK3ewOM/8/giJYT1UH9Un4nKFhvH23hc835YIdEMJQA8kOMcqdzsy2KSwgMG\r\nfsqSTMdAq3FBPY9Xx7CeM8eyuZzNhfj7Qkec7H1jTyJs+JHOGe0zgfttapFr\r\ncZIWyv9TmRE1IbiilGxKshka3AjOwXP7jHBhvBxcoKb0c1KuncaaI5MDEkWN\r\nP7uPhyBlbIfYBLE9YrDfHaqjewAXUVPkmYwXn5uxnkqHc7OSXp8eSwn5bWJn\r\nVam9ygQXBMxvehMrbtvtx0cjVYGb4PHqddivkjU8g5fdmsy1CXhLV7zfvU1+\r\ni+kmBMda2HN4YU1Wzoh42+2t9Po6Ze9Ue8PsOqziojArbsZjR1a7yo5Wd8C/\r\nZ00IkKd8dxUDriEJBs43PVdNYRG/VfuqbF4=\r\n=L4dL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.39": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "79445d750f100f8d2eff0437ea556e659f2e8157", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.39.tgz", "fileCount": 8, "integrity": "sha512-x1I7AH/RPOBpP0pAKSFvMauib5eGzbKbQAJpZrGmLBvgeClYifmqW89HIXnYMP+ntjd476aumADhBpfL34ZoRg==", "signatures": [{"sig": "MEYCIQDYsznvo5qdRkAtAmVctQu5yte6FDAxoTvRnKrG4IiK5AIhALQsfwEQ5L4YAnQLTD9FhJt6rTLdn8yms9zWQFNpQRnb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrALg/8Dwdsx1gDBFUtH12y1bIYR/41yqIRHnj7Eb9QlKwanBFk21CY\r\n47h/N3urLqGaL4E+yVu5HkThlmpDweSrUi5Su1u0YAGzPGt1RUsV/Cxo1SfF\r\n/W84b6Lo+ho391E+wJFV1n9T3s+Q/yo51pIAgtNlGbPtw+n66LqqRxro6p5e\r\npIFiZBST68rz8WtrB5ruAktSbl1zyJbVixhXstd0FMTCg/pJtrIWKLG4FZHd\r\nifTjVcm9oVneUgf7d95Di07EQKryhhW06StM/xBPYrfXZCKmt8iG9mebMVhR\r\nConaI/j3gm0DceK3OXyoSdpQwO/Vf7u1Msrov6plAzuASvzmdxqcouV+7Dtl\r\nmLwUqblv37PndW4J/90GNBuInvX3aHXvRfEjq6osRN7nIIqfoYcm7TWXn9DR\r\n1Px3edQWZxPjLCF2hr0AhO7MOZJ2NFfdUZFCYISj3MDkodxEOnOPpesfiBoK\r\nTk/pOmhln8YcFmc2RE0IfpAaYMaQFHJkr5/pRcI4dCDwl+G+khVYkLZEs0rF\r\nyoPxYCtMo6JHn1Ry1xNCnMPNInAvFS1NgkvsskFz0Zl/anndn2HSwV34YxHK\r\nzTrOAGkJsH++xNW/ZnNd+QRh94aYnAgOjEMcWP1RsEWtvxx2KZyG+dM4nru3\r\n6nsP0cyhdkY5IcskndGEU7Z0r+1X+i+oiM8=\r\n=2oyS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.40": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7b69b562fef54a9dcf781e0b7d4eb6bc4a89eb4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.40.tgz", "fileCount": 8, "integrity": "sha512-kT21S4TBtGxEx/ZofcZ6A1lCXEMsp04lUDqHf6YCVY7GdMsFx17of4gy4Xxg4bg0+GuRdIZZtNGX1TeHQudhGQ==", "signatures": [{"sig": "MEUCIFSai+rd4ROTt7jw5ITWrCbA4jYLT8cKT2hVwauFXFGcAiEAuBzkRPxlhuUXvo1ItISvVuikheyfnV4uFBoZGppaZjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqcA//dZ8v9ZLjyXAkpFmLENsIaov5PgZap3lWA/sGeRDHosNyhe/w\r\nsDc//anuzPss5CDoCRRixN9Mw0H3yGjyOIXYiwUbksEUUb0z8CuyIgWcARAf\r\naNNgeaYMu5a2Adj7RqsiRsynbkyG70hFxRPNW5G49/oK9QJ3ul0G/IJ8x8ED\r\nU2YuMPAP85AvqORJpsWohKghEegaPDSEHTWEFJ0B2vXpZBV3EUsqvd/EDnU9\r\niehAFbRSOXTOvaL2mJ6a5vVfBu79wZh3RaeN8QhICVIBLpVc4SMKBWdP1bmj\r\nW1cGClg711fit/R3wum1HNUGIZl0Qu+5dTXKRcPTtlBD5i9/TPXtbGK8PjwC\r\nYsDdFmsKk3YlSceoyq9UKaGlGm6CRcxxkRjJ25KA0Sp525eM33k61aX70RLj\r\n8AAfNOQoy645ZJg0mpoWXjIt1Loi8hk2J4FgOIhFrWlfYL+2bojcOVfVhvw7\r\nGoe6/D5hEz151tb3LIf5mpbhqm8q1OmXz3OFQCOeSNUgYEmaZlQY79MCYXL9\r\n3NtiVkufWiP7nfmq094L5nfLkDOnL+IvS8+/NXeYMuRQxPmLc0UGISJMIs4k\r\nm1OwTjxHZktWaMNHApAkPB2b3mRbDqn9lFuEwFyIRnTZ3P7yrDCESBlaJ8JA\r\n7xPv7cc8ouQQSXBdIf+6GYQzLdk80kfd/DY=\r\n=8mOm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.41": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5dc44c5a3114eaab73a1edb9d2121347e63697ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.41.tgz", "fileCount": 8, "integrity": "sha512-jruosqMM8J8p6rDUEFLPvNra1/2D1YuQxlQ2mPn5qLcOSXATS2Zja2FnwieXDqptvtulycOpxg7Ma2572+fb6A==", "signatures": [{"sig": "MEUCIC067lQzyrzcgUf5pjF/AaM8xTeTERulqHugXCU9OzdkAiEA+OkeZVzrIwfJ4HhdSAB/xtkY3kWBc1tX/zX9OaY3Td4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqiw/8C9fcSs1l/XSdExd9aahRJetxk/vpz8wl7dB8lePGmJ45iwMW\r\n64K+L4/8OOR5ulHKZtbZlj55F3DZUgpSIeO8dFsQD9n/0K5nHJ4laM6IV1kE\r\nX02kxJcMIexxH6vpHWVlbrq1zSaYk2aeZ6tWglKU8zQlLW/au+UnQW66HHAR\r\n52YmYZYk7p28q7sNq5MqEZBmAcx5xNa8SjE2ezjnK6+ZmL4DLbSJCoPdZPpE\r\nLhhehQfGgBPfyQIu8tQNCKs+nQvR31Vmc5ZGH6ysX5rvk/zTDcMsqFOFPyCp\r\nrWK3e/fSnzQWrVGtsli2NEUErM5BTgO18hM7MTRcCCCKh6lfoLkDEzMkBEj5\r\nPgOqUrkbNK6RhVVJPywgMWPYZIX67TPc50G3Ros3ZqUFiJUXxZEvc5QX3Sgs\r\nNcR0zjCQvc5FZpMBDyz5SXtkgHlyzRuCrhQfQwf7emEtBKMmtrlSSjOAIE4K\r\n5X6jtROKK6YLYRY6Fq3JAZpIpv/UNYrSTBapbzE5/w1NuSQk/+Lia3inpAfF\r\nmqvisqsVd7GowFdVn8eq+055Lwl+OBQEl6MriSqcuNjQfQyPYa3HqKU47SNI\r\n1btiaGro7xa2dOdIHK5gcidNn5DrJHY3aa7kppaBvyaWZR4syzYZWTfSF+5v\r\nKH5NCOegfUCNDMwLWgUH8ZndAl/JVmousvM=\r\n=93yK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.42": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4eab75032c89b1c7a8733df3e28a8e74f2d254a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.42.tgz", "fileCount": 8, "integrity": "sha512-x69654MVA7uKbcDz1v54udRGGeAexqyiSki45nDF1IbkqMDxVJH3hMWYZfi7UoAo5A8oh3OMOmNPgE/I39UX6w==", "signatures": [{"sig": "MEYCIQCd1KWGCf9Jco2PLlLi8t7SYlDyIUq1VhBtYG6WZziKLAIhAKFqc+i6ZVycRDqpa2QjPD0w8LUOGn+U6mRKG6TA8fj7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvejACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7sg//RPksvu3S6wtTKcIImPddou2uRT6ix06xmSD16hyjqmEIvHXr\r\nND9JkvtGCtQC7jFeprHxERSP7OLyW7qLZ4HuDljtuFxXHKrfujZ9imhOIQcZ\r\nLFApmhICjYo/n80FFJMJOGqzH3N9daH3p16YAo0XA5SBTUBkvWVmF9cmcaVr\r\nNyHzaRI0W+KI3MEnGMHYhNAuDSyzV76TUqB7d3XKMMPa3mPip+sUb8h5A0yy\r\ntU6Gi6XgIMY5f20CpLhafLSOkU7MwN7vQYYERrUH/dE7JvMZWrejyADQChhd\r\nVgBvxvTC6NjeGtWhLrWOBmuvSnJIBN6jkqL1BCdOq0Qdu8wtebjg/o5xCSHj\r\nwdApwwdRKzo/i/HV+hOyh+TrT3lYGo08imy2i5oHonKcJmqB5MYQMEu0H29f\r\nSngEya3B3FYqWWwwm4YXFqVmjzSi1QX9MIcz1Y7eUSc5hmrCRqFrlBK5mG4t\r\nAwD0cFBRkDmSr/5OAwLXt1NaRGJl+X31WA8TV+je/veSovx0wdmbMr+yMMoy\r\nAkErb/bpD1K0rpAC2J1JnffQ23h75kpmxHSdv+rDFtT/es5GqqP7KFDEQTrh\r\nQfQR9M2+qGeVOT/PPKyUT1MJtorpE8/7zgGuuP24bDsQAJ5T/okKPtmwAPc+\r\n8GVMk3eToPIr+beflmwa+HjAXOnD+uX/60E=\r\n=xI5s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.43": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "49ca69528f6988e31c148c13d0c89facacc6278e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.43.tgz", "fileCount": 8, "integrity": "sha512-y0/2OtRpRcVwME8OsK/ZUcVlMgHoiJ9pTxXDfEzt5KfWfX4xg6RCYy8MQJ7OU7Kut062jqOOicwwMj+/F+lroA==", "signatures": [{"sig": "MEQCIA7cTK57oq3Scme40PXnAdag10/frvh95pSUJogFN07YAiAdzX/GzuVCingoJUvqla05+xS71t7roEND9KCJeZhjsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvs6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpb5A/+L0v+SymZ42/NP2UPKRcwce2sA7bL/XnMbxAF3MMmyutYdvXr\r\ndD3JFPTU/sVV21FEdQVz++fuTzeNlbnlgFc4NQswrA3rannMe1TvRgS9VJC3\r\nPIyjBKOaD1iWnHV8xabqqyzk2gHf5/MR9/SoBVT8otuAn1wXcrawlK27wU5h\r\n0E3MS9FzSthoZqAceibZXK1rbwEH7UcnLmPME16r4FiqMxRsUbn9i+Po4jA/\r\n1mp6WTJA35uLsCZepAaDb9swVyd6OrKPpwFJdM9yoXO1+t5O7U3zfH8yuO7M\r\nQSgBx5A2KvN219jGigWIAYRyVM1kgeGa9jb+D4SSOiWIrSiI7CgSf1EypGX6\r\nJPvA0sBFyuIEY9Ljd/3u0Uf6B0p8aAqBDt93qwkXX4fWScuDs67VUX65Nlko\r\nzCiTtQqzajv7WhlRo+nLE2p9AWBmE0XuTgIeRIY2aG+J+tURfe20rSV0MalH\r\nNx90PhBDXZ4etRf8InQq7TkNe9OLGgoH2F5jMzVwlp8zroQHtMGERxh58MTX\r\n2Ypfmwi8E+4DL+Pkmuh2rEDaWdrWm83EzZzyhS3K5Dhnk3q2eH/OTVXVy13/\r\nnsW1f84/YQUCsVXLjSbtfvYHh5DOYBDy9bI9YZTIHTccTtF+AojF0l7KKE3p\r\nkkXnY3hgCP8JPdNADtGi6rLaMv7Bl5obj9o=\r\n=lFA2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.44": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "252108e998c3840deb92e7abf0acfba56e179f58", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.44.tgz", "fileCount": 8, "integrity": "sha512-M44CFvhGUJfvb1uPfFnB12NL64PkyCfqAMyCmj67XpcaCEl1B0uCbrtZmHzOtJLit3JW6ATdUpDben24MrVgkQ==", "signatures": [{"sig": "MEYCIQCxkF9fR6wX3ZfIuRkaGcnnWQZ1m0+wOKSi2OFPbNbGdgIhAONNI5zu187Q5Uyjud7inwo2dIfv0vWk22rV36t+U5Yo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYUw/9G/l2Io/NhiLvQO6Cc8NpHou+cvaBpeDBPJbtDkyyn5Bn3W4p\r\nalwbFpdhGk2W9dq1jXlLAIhKOmTl5NmYNWhyrOUjUzrdIMHxLR3O9sreC7Rf\r\nwm0m/rMkWNTR2oNP+JbQ2P3BZZh4rtwGMAUG5zgppvRH+Nkk0AF/FWaFE49S\r\nom647Do7Ndrwr/T1Xt3PR/h03xMozTRW6e8a5jkitqQ0JDNDs4TGncbIh0YB\r\nQ/KzlsJEZZ2xnQnsaIisLATqG35jIc0F1G/8juThrt/K41L0osNGPC9qA536\r\nQ9IlciWaLZNgx73PXVLnhCBclkIBJG5AQ8NIoIPBmyisB2YzpM+c7tSMeP2c\r\nGbECLdLlO1c7QRjl9XG8H0aPIuB+9bGZd1VaSgBFJx6DnoedMxV/rBFOQkNS\r\nXJyHO69/Xmg9YLhoJjO44jte3DY/XpfhrbFTpY0y5kvaUlJqCIaERTHwlPOo\r\nfTtbQdqdUGPdpm4G6Y6arsTbVsB5La1C/dL/K0/j+8e8LG1z/UbPrlZP0Iim\r\nMQRy5PkybLQshxRF4HDTFl7Gyb6mWh1nrGFFIdwQeWSbCNida2ZSET2phDyX\r\n2C9FkY6tRA/5a0eq9M7PeANrfNNMt9C/ZY9SQ4HWGOeAOzyHJpgLJToGbuoC\r\nS6qprGT6xZXB61IePrloDS9XVMaLlDHmo40=\r\n=c1nt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.45": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4daa516fd646569ae22200b9575ddd96785ef9fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.45.tgz", "fileCount": 8, "integrity": "sha512-EB5j81C5Iu1M1HKnTajZVyvopuOEiyvJysdj5mXL8umB7hUgFog6tE7rf4p2VFTE77DsdW9TPyPOd5i4Cla6AQ==", "signatures": [{"sig": "MEUCIAkMR0UiN3stYcdcDCIVDZhNaSI1tEmxs5ZO5fv1zyCsAiEAr0sSHZ9wRDKwHj/rH2hHrhH9kBY7mhjd+4aosKmBHUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wW2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGew//UTRnI+RdJmSjhP+wB84QSN00KpBkdlkI4nC1i5x3qvHoF2YA\r\nSwciJox9rOLFc99Metjvgx1qXtsQ4bitBsxL/m/cCmnukt14hSAZTUOWcAbx\r\nhwWOYpdue0MIvoWbzjbdd38vjTOfo5ysVwe+RCxvG+8yflWJvnVQLJZ2fHNi\r\nwx67JLLXffUIR1TG6twAPiJmRBrqfboUZz84IcKCuTCCFEUFNrx0ElHBuAZT\r\nNarVe5bKpEtKAHUcyVbsBC/fhmDDk4RrdmpMsZgJpec1qCGbd1F/TxsnqJXh\r\nbz4NJINTDee+owIoJneK0VhcK8zdk+ZnHgc3lDCdQ7gMdcHtRLw/o6HIvoT2\r\n8Ws2BUvmdQlmIr+x1rbFgKrKU174AS947sUYnaPEv8iyXmEJvKeeP90atOLY\r\nKrZj2zoMS1oHtRuhRZhAxEqOS6RD/iWJn+Z4H7ysTyft0tVEJork0h4FvXp4\r\nuIvjif/m/rVi4h2DT6Eq2x1U+ZTbeuq8l/J/dmasensgw+EHW36pNDm+0BXu\r\n4JuHMR4Gpq5hQUrttWdkvRu+ltC2Wkf3z9Ie1ZWqjEC8ubQ41DSdtPl2IZUN\r\niJlyIVtUvWmiAiNMhGi+8Hnw/x62NbRI/vVddPIy4fnKb8NNZQ8bjzARfc88\r\nia6YdlcpUHiUt6UXx8favyJV7/+MCabqZqU=\r\n=aCgw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.46": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34571c15776bdaee961b90655dc5d0124c72e5c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.46.tgz", "fileCount": 8, "integrity": "sha512-hQQ2/cdWbLwOQk4fj/rc4mK843pCuekTo//81ToHZFveLdCX0bqT1Of3N288uve5DQN+rEoKH/03v40qBvezUQ==", "signatures": [{"sig": "MEUCIAFJxp0bB/SsPJUBT+V/WZcP+GZUi/ILFrja6m3XzjtVAiEAz0eOx70S6udsdgz5cfH06Kh4kpUN125HOJ/L+LyHK+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCxw/+Pb+ZAtLdGjFxwJkuMEp5cUJ8Nf1CVL7jg5Qb1m2hpNZzv6mc\r\nKtiqAT71lAAcIz/260ysnB7yqMvigesQR2l1lM8vfHXbeRx/uvS47MFkfG4f\r\ncMXQPOK00O7DCn9oNqZ2l1revSHkUujQs8ki1uBkbo9wxMbsFB6+otR2kDsb\r\nWvSOHegGRYjhho/SqhlBR1DvkGN8mviPMQPrHmut6cTcofo3pH006un2Rjuv\r\nUyE/WhveqOpJx0eWmCAzuibALbI0ab4MM2po4CAFA9mkFPZWlZeEYzw4LN48\r\nCglAgRsmciQ5zdgHw3EEKknWZChU0JMNpyx2qI1OfcESGmIMciVCDbVwTZTp\r\nvQVgHb+vvpRv4bS5uf2ly5G+q6gghsqKw+ImukHpi5uC8C7gzSgjFsV4jrak\r\nlKvHgGUyOV8pSstdMNwr0xolpiguDkGeFPkpwuuhY/Y/OkcMCWuMlyGNV9lJ\r\nIQL/9SHbnweF2XaH3MT983XM7lbxUxkPK78zxJgpG5nFRV6tS00ygse/kIbp\r\ngu+gHKceJBEXbJaJe4SkqjIQxznqmoZxDw65Wplva8J630r0lHqNqBMakFT9\r\nZkGkOZq6uTDVWCtz03KCBnmCJPZcVi4q3tC0F0TpggQSbaLVkRMgS9MrUAxq\r\nLZKexFRif7vf4CdbGjH6/foDNxtnC5ehe04=\r\n=AMo0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.47": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.1.1-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b6d60e839d5f106512c6e309fad0e10fe08bc285", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.1.1-rc.47.tgz", "fileCount": 8, "integrity": "sha512-a+/9xr5OgHfJCuGLwu8eoc6Vtee3ts5d9yX23sxUoj9q5utDJnodOslkzYmjheP2FOuI/IgrltjwPDdOZcqYPA==", "signatures": [{"sig": "MEUCIQDffKeRL9D3Zxs+O5JVgSXJU6uORU2dO3MTywcdDAB7/gIgNEHL2gfMlfpRZFkOCxa7TPNia7gqq8/KuBcKLmJquMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCzRAAk1zSkX2rhCqaOjS233mMx1fAFyqKz5lSe/ahAsAzNSJ+NdX7\r\nvrIS/R4zUiALIwgnl7/6isqjcOXRWPTHQ5FFAnCXOcuLma+uvRW/i2/Gs3VN\r\njRSgFwrlubfa0zI6LbRC291hR6gYv2o0PAY2soH8Mjp/q8dA9uWen6/f/AbU\r\nEVDs3pjRr/lJqccACUH4I7eBteDl41t4UKy0IUaW7EYBxE5M+OYbde81XOJl\r\nLio7z1Ko40cf31XPYvPdl9NuTkXfkqXUysk/1I2HPvasnUS25VIwJHaZaRqF\r\njeUWR4epZUANrhDSs1MuLUuIkRSBVY6uzLLTVcdFK/ia5RoE/raOVoO9pTOx\r\nnj10uMuEIS848ptMrrlJA/X3NJs6KdFXEFCdqgEs5JAzHaeqknidgkBhzIyC\r\niaDtC+6j6UjQPf1uuS6ccnS3iHvLY1s5+YRhSbVElvq42buian/zUbUcs1ek\r\ndTaRw2hQZH9Khaww2D8xAao82fOZK6wqFpi1W4XDkeNy+1g3lCdTPK+wjPyc\r\nDjTwzv9SdotrfUG7fhzy0h/AG/3lfh3IJgJO7SWrVl/Xuf/H6+C5fGunUtil\r\n+OACO43AyQdt52KTpu9PBU/IpgYPyiB0/4v/BtRk0kFb0PPBBtK2/RTDTHvT\r\ndNNtJEtml0QbRBw0GmrQ3GL3t2TcoP2Mshk=\r\n=t8JX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf690223fe8e5269e4e26f8fbe932cc76f97b40a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-2PGKbrwowT2eGAp83/PtWi8SVoWjLqAXg8i/pjXYKmh375LDoioImFRFKxgKCvF0ka7efVtkRlieoAZ81iHCQg==", "signatures": [{"sig": "MEUCIQCEXUZIdA2avJqckUVl10zEICA3YDpUyPXLw0EBGsL24QIgcJd8dWjgkUMvzCj0bShYc5zdtRb7txPuOVp7CU+a6zA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EwBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSBw//V0iC41u5cjp8r99OCQaMIHa9MGcnUot7RN3EAgKueqr52StJ\r\nV+Plwx45qpozXl8V6mDYioKTekm1GrdKUmKHjZRxsDdjtPJQUuPoDLWxw7bs\r\nuykBQ8nrciN6JW3e11FLT/3C3qEftP8mbgTYE2NmHCVLj9g7ukBgOp5L3BOx\r\nfVPIGR9t/liF5vBkcmwM8fRewkjJN7q/SL8uAqMCshhIMHDsdqyjB2v3pKH3\r\nlS+XmXdvunf0LcQOi2IOGLv8PtQJzUzqqZCdKr4XT5JXEyuK6xhES4KPfx8e\r\nWWPNwUV3QKap3dqQKo4epzMVvBhYnOtmd6KtmPffym6U4nHOW8lQOBrY/yl6\r\nGEn9KpWITfGlqzCo7McPb7fQVSLD/ehdwat8mnuER5MD1gXKIjCXAsYVwoDl\r\ntpn8xrR/z74WaYYG6mveNH5MNknzFJxFNPuS13W6/Ihae5v4kJWA5VI0t4zj\r\nqEjNHQzVR3Lv1H27bnKcO7e6n8vTlHCHON0Vj2UnJzJE9kYnv8tlsmkjAbqi\r\nqIl5Hb8CirlMeLX338hFAS2lDbbDm9At7/eKFOchfVLJz1K76m903fgPLmMK\r\nHHyu4B3Abio98LAUtkMABIvD22xtXBxiek5gGyZyn+owiWVaM0JQD0NLctCo\r\nWpblWf7YOGEWC5xQB+cQKrquzoeF5VFkWEg=\r\n=jTUi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aef375db4736b9de38a5a679f6f49b45a060e5d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-JwfBCUIfhXRxKExgIqGa4CQsiMemo1Xt0W/B4ei3fpzpvPENKpMKQ8mZSB6Acj3ebrAEgi2xiQvcI1PAAodvyg==", "signatures": [{"sig": "MEUCIQCHus/vAMLQ6ilX6uoJS2xMPkj0wacdFhEknMDlz/ubYwIgOj1fJQU1rxz/1eDOb3dVx5ub2NViB/fBu1uFOh9lnIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm9A/9FWYhO11k+3Keie9a4m1WSwXK1n5dmSeOz6KcVIs56C+ybXHe\r\nZwWFwpQl65KCGtXWeUqFS5+C/dBpk2P+D59NHEsSWkctydhmNnRFfDVceTlB\r\nU4YSFQNn0PtH9D/5/ASKY6p+nRQ072EoqceHtibY7tA59mpJhIPwcbtI6KSk\r\ncCgqxrDj0XGCBO8eMieB9BBF6r0IkbRFtv/CT9rj1rukSFObM8HhNTmIlcqr\r\nZMGu1UtoMfkBgw8H7KvtD9clU7xCZvdpDB4pEhrkmsW0QwRWd8dyoUSPYStr\r\nYZAM+qtEZQPC6Pvxjgje1F8RY5ElMhEpJ5m6iwCdgSCnCY+tVhD3GWtTRBnv\r\nv/yEQ+/5jjx+9bMkp7dVnWUUiCJrAu8nRNaN9oVDHlhFLheGVGwqjQ2UrByD\r\nB09yStLhiuX4ws1pNlsyCLZwyeU4ITfigg0Zmke1U89FDjNqojt8kmETRzg6\r\nXgR+21bIGQNREICAsyZSfuYS+e2hOkXkoCa6hlsW2AmV0kkREXzl5sLOqOpr\r\nQK+ZH/BnBvfFwWp0VnSnlW3fkqsrephtnsduFw1ojf/ZOx+rqOo1lI5Y0Cc/\r\n9ixv8A1WN0gbGi+prda0zqkHQ75pFxOe2YiqeYx9nHiTwq6s518izxBSE5ZS\r\nYr0q/dkyJNeGeFUcJtduHr+pjrbZxog2/Uo=\r\n=9fN6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "218cf641cbafae864f31350ec079cfff1591c3d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-9p7LK8hSlFrOi3goz/8OORPub8nvSNpW8bCd1H/B/VyndOh6mea3EHqPI9zpNnSPCY9BCV/X1dX8Q24gGHc9OA==", "signatures": [{"sig": "MEUCIAv2JAB/VwM6CLYWjP7MaK68AJOZQLhBUb7hWeeRECZKAiEAuB+CIt9dPIVD681lq/PuIm1hFXFaAFxrf1REhDU3oyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfByACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwkw/+MRo9RLMt3YqOPUCz5gH/5A1dw5a2SrnRbhpdX4JNQruAo+ue\r\nPoPzRI1IFYcZ+e0s0QNC7oys3nKUt48S5aMCeOFf1L7IU7erAjvFOzoxrlVC\r\nHz6+waRcyPzKnKZdti1NL6EVUAj7bIYhVR68C7zckZi/U8vAgxQa4inYvdza\r\nKAsopyq8rNl9J9s6PEO3JQHxLI8k/1FlpIbawC9RbadUA/VCdJAfx5KYqAao\r\n/QfSmBlUzgejw2zzx9lUH0l1X3kdRaphNYJKS0+udmiC88g1z1oox39McQFI\r\nL3vEQzujjICnsGr14TzRrbsBVZz0IyMzTJUWYC10ydhKQTZL9wtb8IssB7/q\r\nMYMoSW8S858VcnM6U2kUaFB41JTd54U0R3IXvoplPaZ+NuWltYHnjtVkypra\r\nLezoiQZnn3gEMFWr163ktGAlUZXfUfZLfNqchbR8Tm1RMdiGH3HzGj8ldho5\r\nFfcgITbALnJTT0qQTWt5JIKAxi+f6L1KKYLdtwI1omfxE8mIQaNrSc1fX3On\r\ntO52maQpypsC9zekhBNp+BIn4HTb0yrzq+Q52/rwtVWNkGZ/cUxpUdAJbwa+\r\nmgRkKKBBObHtLDQQr9j1Gre5f8fZ5LOVpv08EEEe641ICYQqkDlNdclHHuHO\r\nMPyF92OvcYfra9gjfT+ffXFjbSIS9tdTfto=\r\n=e6lH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "216e772fb911fae989d60aadc6c12ddb060ee041", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-sKG+Ur6Puz1BIDH/jrfK4hO7X1YS2OPRSMcsFyhWhIm/Ojhbf/ZWC8ZZbtAdu7+LWVgcuLMO+HwaH1whjSCFLw==", "signatures": [{"sig": "MEYCIQCo6Td/x12bKuvd8UpKZiteFzgwpg6OfO61QMP+OdB8nQIhAIeozufyMPcuB4N+mialIqkzkDpYhILJv+eqFENFnrc1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr26ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobMxAAncPhDs43VNLlCu/r1GMNeMNcFNEeaEzuEragR/mS5IvNG3Ao\r\nRcdYSKaReui9MLw/ltBL8ZnlPRgKrRmGptzPwI7yhEqY0nDV5VY5A1UNoFit\r\ntRFLDQ1rsqmOudtCHggwRcQeuJSF98weEIXoVYC+xv39wv3e38prLj830ZLK\r\nUqcg4v6DN/6uqWpfc1x4KYiJctSlmmWHwlSG9BqwcsB3ggB1E1/t+aO6DyL0\r\nGKckdCRLIdstwDWtwLXN4F15sVhClaR9JDjmhUW2tLG0IAXKVhBIdIfL/RLd\r\nPfhV7jBDJRj12CQcNJtDDVZ6Xm54GQ3Yv7j6Zgs2LwJO+NpsjUtGWQ5NRGMl\r\nPaPr3J3luc1UPqsP+vqUW71VQIqsUGIlzggiceF5KWqKejYvcP/g/Fs47P2R\r\neYG2jbidXvW3wGtmiadyg5OP72LD1913PcI7xSvd//8ieXqMWEg1znp9DbBf\r\n/tdgftSE1b74T7eQtIsI7X1nSVpWHoqlz0eK9lWKyyUah2sRjlnSXDpnKXtB\r\n2zzzdYxRTyz9oiQwU+/NSMUMC+EhVKPSdP7N0/UnjDW1W3Zjek+HSYFnsHYS\r\njiOZyh2fKg9nNnPMDnfkKAkL2htXxt1ZSIbw6ixJ94lRFiWwO6FeuvJADhpG\r\n0rq2z30cGDWyMG6e/B9f35uwliukpjWeBlA=\r\n=5tPg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "03fc3180c325dd843a9d49fad71e7bda388bb41a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-hGrM4i/t7cXlKP5veBeia9K/OeD09F+xBj9wTrWcudPQG2f0k/mALp4Mf4Aq1atzqqZ3YU3bZrzB1wdcGjTtZQ==", "signatures": [{"sig": "MEUCIEF0IAaMR3oBhMgFSaA91Rx3WanqUFCPxMz3ae4DMol3AiEAxywJCkq6KmERZFQr0PaG6XsbgxnC3EpwwL/91oJtDdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwQEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpuxAAhoMLNoTvB6K042mYXnGB4yq6Zyx3Sklvr8YigL9lmsftLPzM\r\nQFiQQPTjBUFAWKWrpIaaK2cC68QdfkMNRNUAMNx9j6dDVwnq10whK9azyA2i\r\nGGBvIsxtl9i4LqVngxV5nrTzTWk1OioBGfW7qy/EsWrpjS5wmQ2sVBe42gfu\r\nCK7bYDrqCvBrDnrxTmV8swZc9XuwaWQf9Tza+cePg92qzMrh8zdjXZ04ZSbr\r\nuHQ7Uah9Qowt4FRP1ZO7ck1/0THke+3YeQhGwUei1QjXuOAi0bn+akW9dxki\r\nrEHjAO0IaSPny0/+JnqERul6IGebOI9Bxr7weRSgaKnBQ8qemYD2znxmYkgj\r\nWoFYSINcauApwzw0Gp6VivdUS+Ai6G7mg+ItYuLLfX4RcNquTR0nN1O7+7CU\r\nODIyyMSbBJ15R870vKA4zffTPKFElDu661bOxxrmSzsIpkXmQ8HVRC8f5AvT\r\nV2myjEcUTR5NQEv6SzvWB18lCUwe2FRENLQe3mL4Z/uNQ0baHcl4ywCNyaT/\r\nHK9ZgcP3gL+kfP+LNEhMXAT8pmFW8Fax/RLXddR+42g0ZPfmdwLsoq3TigO9\r\n2053dWgE2UF1mXsY0pgOsaxKoiVXmxmXCNUOLQCFWq1GRQUAvuudX5O5mQva\r\nlD4XRO2IIBQaCIZ4SPrUM+eiRlTrXGGBVfc=\r\n=ndKK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4a9e75c1195486238c16fa5637de54857f6fac8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-4HZ3gar3iiDu1GIbdMuf4yOjnDPwz218FzDupAVxjKOVE5m3eJBMrN7cO6/nemBpY2bNON34ZEJQ0iS7i1uPLw==", "signatures": [{"sig": "MEUCIBqcm5izG0e4CccCGYBf3F3C3axENYI6q3PJtuGnMdZZAiEAn9ZQ7HpPZ7FMQ4495F0lCodCrxRsaW45FDr/0C6KPkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKjxAAgC4JFIHxArfSpVoj0JEknzxQ7btjWFlLKj6lbPFDLbTpARzC\r\nteZztwV52RkJvsOUZyETr5N731xVDRHXpjTWEkhWMQlIUgNe62nLagPeddAh\r\n5lq8orsELNjqqJkN7eyPWc5RBMh3iGMCYcdIpT13fsMDqpCl2XDkJ5Vl0fKB\r\n3+D8OblXjLhjtw1SSLO7WhlV0XnVijvQq3YZwVXhDNQv0sfALqii6A7cqMlw\r\nuuePRPychw1/xPH79WQwwofO5U6if+Ncf0BcCGrrBvSp+EMsChQm4BuxuZxz\r\nbBeuwsrrsdeeDSnXQ3qU8hVvHtJk5NsCVL3QjLAlqNiiTLj3eUrlFfm1zmSR\r\nrqVs3gOgazkvM7HbYLhRhVID3x3bZdN0MoEn+WO49taS3sh2H1vu/nH1FYsC\r\n1NZe6XIbAFT6Bgt0JQF+6E5KpLaI6hc6F9TarDUCxv9b80USoVucfLkAfFrI\r\nSDznU3A6nZyokbbF27hZiNAjqGZYYwKvDsE53Iqhj1ndZ0wLLrV16+XCSiNu\r\ndz60a17agvoQqeXSJZSZNlHUlXtjpqJFiFLcA2IMNUrQEb2Dt73yhSFtZ3xZ\r\ng6f2EwYtLSEvb+xMKEky8RrY7yXjsSrX/ZMBMq0o7SBYsvY1KoXEHq/V+Ch9\r\nOZOBLCohRA0ybeOFEnEvqQrFcx8ZG/LafyA=\r\n=9lu+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2cedd59771e88370fe6ea5d253b7d8be66f7a7f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-b1lm2e/aL0ZKBJ5dE/hY7xm4EyUlYJdUpH8DM1+bXuv+TOgZrjbY384UQJti1oRSmFuV3qCgzDycfQYXKE8IRg==", "signatures": [{"sig": "MEQCIF7e03CWNxFFEDAiNzzBzeXgN2TUW1JSVm4bKV6Uhb4jAiBWWVmY2kyrDFzol8QFVVH+mZ1Lu/5p0uF0sMpfv4bGSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+hGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqznA//fA+D2XP75q7rtRBPrzAb7f17Wyl/Abpa/MhsBBiwTahdTAyS\r\nXGq//kfPdGVqey+VBvqSaokqYh4biQxhGKref2x0qSOB9SzWCRJbGeGTVb5f\r\nWlOiSzjIS3EWKMOWtxd88+JTGFcTd/3Un3R2RynOFKzGcRaSE1jZY5IW4n2H\r\nXguP3z6Fw0WSb6Ez/iZIiQ6cCJYmVynIkYAMx8ebYC5YnJzNRkIZilM0FJb7\r\n3KrniLoF3gOFryIi8h9yAtjkJRT9yODYfB+JxEUjKV7WqiKelj97gLq/iMP4\r\nU7gYJ+y7GOdAKT3Q1/5Bpg26MxVl0snelHVzYabfxYJNLq1/u2E9uojeM5VY\r\n9Xf1HfqvSL564Me72bS5mx7f/7x3el+fCYJ+J2rlh/fhfy4h17EHaHIsFu+2\r\n9c6RdOfGFu+DD4q9WvnguYoyQ4Ve4KScsN+NEPt9u/i351H6OrPSIyRjSQBn\r\nW63RC1dSvwDR78Aff9QIQgUg2UIKRgCDjH4naMERL1kd6o50qQiRhiT9gaj6\r\npIDVC8bhX6KUk8NGZ4q1Dne/CJdvJBCZBsgH1x1skaktB0m7IJFgKFEz+11d\r\nKdxQt6vpQZ1Emt9dh5+NTGVUFVMFUUV59xPpM2PNypJfruwRbAkyHghlFOoP\r\n4WVt3YKWhZDdAHdSOcRBhwg9Z9APamawavo=\r\n=b819\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d14063409e786528d0fe6cd6e992861e174ebf6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-5275Pt6oMGZBesSiYK+JXR0F8jLXp2ELa0wRZ7p6SqMhkiurAjKqQxNB3JL3gCJTqwPnR8+u2ycXMR4V41fMmA==", "signatures": [{"sig": "MEQCIGnA/olGRwtT31L/tXFXPA1u/bB5dqoizNqgmhAxU3tfAiA8H9P8Jb9eHWSwKSrBYv5EuPSW5ldx7I7rqAUlaxBU3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7AA/9EObuzOdhvupcfXA+0lUtvU/6uN2QokL60U9OVYQOtuArbhuo\r\nKfmIUAeba9xfclv132xAqtPmTMMgclkrrfDQEanlSiOV4TCDiXrVbbwjmd8V\r\nCPND9hxuM9Je7i8LTy/zZodrA7XzQcvg7fcYwXF2Yp85rvkXapskJkkBnvzt\r\nIb/DKIKLyX/P4TLGUfd7NpwuRKPTK5fhlYxUV+l465fCOmKMyhVDpZFula9L\r\nLzcgAMPV1u/vTzq3mVuJkikTXjXBJqb03kcKODV7tI03bLdPmF2+rCJzQ1/y\r\nWges2UkXJuPlIA/HTwn8xbXGipJlXc+LMVdrViHFADOIfJp8lpry+Azs3wRh\r\nxIjStvdkPrkKRuZiJ5OnPi51ZChSHuk5Ld/tomiVQ2qCYTa6EZ6opPY2x3iq\r\nYKekhIycBQTuciYGrmRMMvKU7VLLhu+nU1DPuFC86jS7TC4tglwOh5Yigp4b\r\nuXplMCse5w14ueBV64aWHDfjUD8lG8dq87aQhSDHNxrugAJTdKxhtnUBjvYN\r\nqvmLzSkE0TocWBqfXFW9BLiO4ob1U+VUZr6g0LE4UgoXVNTDQILD/3gvq/QH\r\n0MPPxZ9yEevLyhuU4eRpMjZ/VsMN3gV2h/DVP2yTU6hDdxPRnk0m43oohkwC\r\n53YQ2ShSsWJa99AYDQ2X2bQ6Sy461bC0YkI=\r\n=KwjG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0bb2ff79845a55307856ea64ed4f225f7cd8267b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-lxMAFtzWBVNdGYZ3es7SYSNmIjz4UxKDh8EBvQ00NSeSifXep5BM41YAb9zYyugQBxR4Z5qEhORpc7QAiXY/Pw==", "signatures": [{"sig": "MEYCIQCr9DJxHrfbc3Z0MD+ct+Dg57IEZbJjvIrwLEdYYrbD0gIhAJyztjuypZkM7VNqgwaK2VZCtZwH5jtgD82KmAclNh1h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrb0xAAlNWDUBcKjZZPwtOSDRDc6OFBCoP7ZlY4o/9rP6JnrVg0h1Xp\r\nedGx3LuYDTcA3fO1ZbXJwdpVQWepF3Bl5JHL8fXaljuoadGzHstaT+fUiagz\r\njEW8tOsikYawcnv6wN6M7tfdl+CHrrmhP+dyLFy6zwvS7NdZ/cXJK9A83Awb\r\nmvTUx5qozm09VNId70YsgpfmGfmlvtgDeiURVXUNYf+8dGm4321RNys+w/BN\r\n8vcIzLcVCQmzbJnTX4zh0zbvwy+hgnpK8uc8m6hlD55SKmXgYbAjRz6nlcT7\r\n21d8gedk0ZifM8rbbYVjFuHd9OEG+RfhLgLf0C8Q9TBVRJGnQV3PkWOArcAc\r\nE5i4e0tNaMuKhSBLr7qRb3FVYUvmrygastbDs6rkl1Pz4M3dVPk+2UDp1WCT\r\n+GKxbuaoWSC9csgonGTYSxz4XOE1NNbSx7EvTtDZaG64SW07hs9LwjD/ax9L\r\nhg8GnNgAitRoceB5j0fjH2HOlQLdB6rctveeybUWqpEYcgxqM3ursfswPwjd\r\nUorPmRqhfpA+cEZo0kOKw6urnuQrr6H6UYAS5U7N4uxdW3vD20jYZbSH/9+I\r\nGJGlPQiZlgtH3+zj9wri1r8nU/3i2OGgHEFt4skL1kOxLfYnNSu0Jq/fWIuS\r\noo/hbL3NMgnG5ByW4Dakd3j+rdyjHtLHrx0=\r\n=q38i\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "202a028b9a176d47f310df3c674c82e86520c4e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-xRsKQbUdtKZ6SgdSr4t8OV666ZDuE6b8RFfzVnGRGPrBbN0RoZODoODXk3jTBPRKRwMv4gBAIys3xITG7d70WA==", "signatures": [{"sig": "MEYCIQC+qu3uGuSDunmR/79DXoVJ0UIbf5iox9cTRhEaDBK5ygIhAJ676lSyv8DGAJw6QIq7dR3fm6rRRpaZ4yQ3lh41EpbY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRyCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOmw/+IpVnU9pl8fLAzxqXK/RrLHDGWIYqk8vYV6He4508Gxtrmw8O\r\nj0S2yTw/GGL0wxL/uhpWHD6ry5fG/wjg7Nc20OZthA4CIpJqstZ9WsgnW58S\r\nbbsti7tU62rxLQIGmtqmgtxKPFtL8fBO7y00H2SfUKytS+za2nqrqCx/n6vo\r\nqPkStjz7bFy6b6UKEZ8l4UMon9t3KCMsldVE1pPisoCUEkD3l9Rw84ysC561\r\niwjgSc2QpwiX0i6zHffrqTh5RZffptRKfgKmuEDZtKuTY9SBzXyzFZaP+lLD\r\nqCwmymXHgn0iPeqM0kHFtFEMdFoc/c55ch4BpFC+dbJBilbDN4LJckDN/hFg\r\ntrLQC6nW72ujJGcL1cLC4irc9/wDBuCFx6y95KNHO0Rk44sqEJr5arOpO+ER\r\n5LBxc8k5dLVyl4cuPoAYgGKsRIdv8yClVJi90P5aLhRAvZWwiXc4OQKyyHb+\r\nQ4/qbgZdm5RJU1CJTdjXwWnbRcjz2lHJbDqGYGzgABenP45U482hDkhgsmnA\r\nmaZzd6nkFWNn3k6wqunPRV4klb9J7vCaCkfPSXuzZ2cjHVyTCOw08Ld0GKl0\r\nX6erW/otJnpzvSt4zASqABYSDAT4WmQMp5qVnHseH8zL0Ym1xGwikCtR5/Sp\r\nbv8nsYb/qzpjpS7aullO1ZxUWeNjfWdZ5PE=\r\n=Mi0i\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ec54730ef812c17b4cfae5a0a12b52d4b8102fac", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-5NEohdJ28eHBKL3IscFwVHu/IWX+bhk7sODXkIj14cmA+qOAP/w2GOyg1vlSnnUgzt8vEWu1nKuks8ttFlebOg==", "signatures": [{"sig": "MEQCIGv2reFJ3QLpxECT2yu8WHWQ7fUJKeihoeoanOhLc68xAiBsRdD/M9OoLy0QxOvM/2/DcNsylxQMvGwQAz1CZZJ9qA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVM6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsfRAAi5Mk2W53pfkJ+Ga/jT+SWavoyyZPcy2Uat7uenPbZ7ONnzfi\r\n32mM0KgHBdYLNet47dvGQecBs95LPvsYbjl+JHesltR0sYgCqZbuLUJpEfkF\r\ne0aLR4y8yHYLkeSbjaeCjyDGM1H730sJ2oiTWrvSyTFy+ad1nXMmVEWSC4OU\r\nSH1wkC0JQ4nhmu/6+Cu3n7f6iTU9SLAZxzD6AFwn+MP0Y7OpZoii/cVZRVVq\r\nKEGxCefKeaILz4Du6X9DacwRc88cFJem913IMpdqMwib4Rp6Hl0HlBz1WJ9W\r\n3RX0BCrw8aUUoNtz8ucZflH+q/4K2Z6DmlPlUkjkuV88CuwNCBN1qLK5MZ+D\r\nziUs9G+gXJxErKX3gRz6vIcG4/oXigJAmVS9gNDL4j/JfYx6gmgap9jC/fHT\r\n5VIetDYyY6PmxzFRvnlhUmhp+L9+AAlpFTBvh6Ea1vyjwHhQj0+xpKrG+Ny8\r\nuK1O4qgYnc8fdsNa6O9xtDxZwjcC7pGbukg4lRsNXM4EI9miHODNIu9cYpjO\r\nCj+OkVjPyq3PUYPy0+21qerWVLvW+2NlBuqvDPPrN7GhGY2cON92tpITPyWd\r\n1RlluHP6PmuwiIw4jiTBAwtkNqqLFgygjBmFFKDHr1b3mFlNQ1az95h6nTpG\r\nA4hcnbIj6gM+cf/JYLfqLTeyI1+RaU69oZ4=\r\n=0VJQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f90c015ea073e293bc59f31ba50eaa875ff293d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-yg3h8oe6nuJEtWtDqQkY1Qw2WIRN7pQZhqS8cl4vTbb1q2C47Jgfzu9JhNkwEryeJB3C8EvrjtxsoLP4pOuhDw==", "signatures": [{"sig": "MEYCIQCBU8U7Rt554uMMS7frbWdlwlGM6WE5LIyot0WP2+yJuwIhAMOZNFdQ+3L+iQsjeDp/D0+YK3DZcII5lImkj436VEb9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnLPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/dQ//Wc+5rbmGzWAjKBeBJaYkLAXr0V2yNV1MgUGvRorN9kD19Yx2\r\npl/XM7w8tDT5Hy/gA3Qdok8uuBokWuafCITIdJgrvRToVvudx0DYSsjqQyIx\r\nx2LXzxNMT72334EFbc1sYYU9oPdz6OKS8jttz2gAOp2greStThqvmKjkTNeu\r\n4SdYXu/UDC+ugdVy7IWk6Fh8pHl48b4XlRCP78XskvdT+AtDSvbUYJjEbmmY\r\noaLPKrTrP5x+LqGAnuWxKsx6B+f8PYDcU+3mVC6HQ/2kdkGF6P8PVXa0QvP1\r\n4ljtvOBMrLj8y/2TGjLOghqdSqeJryTUdTYSg0DwFfnWMUhKcq+D8Nu3sH0I\r\namNSPs3XFe7zL01SoLOQZycP/G9Ef576WDeVjpJhOJjhUQIDuShysb9PR59K\r\nunQJhphnFStBVbzr+X54HAqhy4ZSNmf7zNWqN2qiGvPZsgLMB2BXaab/ZX/g\r\nmyTDwmyBKpETc0414DoohkQRd60FBEZU9xaXTlFJ42ixFszXtMeCM+FYhicG\r\ndjAnEjgGEMDvWUVIdkoSe+p2Aq6mYuyxEsfdrb1RoRijnSxel4l6sDqp0ERg\r\nvEHxZw4MlWrrZMh234MnAv1/MTqZAOLSUMLJSYtcoVcNu1fqNyFBefN66TCI\r\n1aDlq/Fim8APiIrwR0RFZeE44QCsU4Fjr1s=\r\n=ywjQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6390acf61aad7b6ce5fd34fc834633ba743a736e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-DlIQQCQOCUaJbrV4vcPoyPfqpgB2Z7RVGvTUJIUolSfTWgxW2bDRYbWlqnrQISn7fVqebbD8e6/Hf0I9dLp5WA==", "signatures": [{"sig": "MEUCIAFNFVuSjDCQ1poyBJGMUEh0mQE9+PjRyiaK6//9Wa3mAiEAw5mh2xRtoel7lcz46oRjKDe1EHbgng8WtVWeFWEEXx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3kw/+IZ3lIoXnpq6g04eOdzh2eiKTJk2VmuUcHdLsmKnB5rLnYrGh\r\nfofNJLa2p5XBqP67Z9ofAKcRdpDd0NQLteU0InV5Q/LNQ7ALR9u2Ttlmax7n\r\nl3uyNbhaEsDR9kH0zH3sYgiytpY1kj/yE1CeYLL7GyQqnVoPYywxCOOUJaDl\r\nP7YF2nTKPe58Ln02iUgJXsh4bQcyYgfK15HE9u6xXObSA113WcxqV0kVpwKD\r\nUeSAgA1yN5xEHaQftrijiHjNUlwQteCPcZi5TnE/UFL7grXs4+bkREnWEADt\r\nkkxLRselgnv6sykQPfbm+Rx8dZuPtN2ywwIfN1cUSAEbI/0U3wNSdtmPYnMd\r\ngEMxC5LeaosRi7T0JaUuxLNfO4I7irZV3bPf8jF39Hg0yNkRFGpADCkxO+zu\r\nnqd3fd01/HXO16DGzGKT4ySSdn32qtA27q3YPBcEB73WAFR+OEzKkrW1QwVN\r\ngwdUBrH9YC7StAyYpwyMzvYwfAjfwV3f6vAPLLHMF01rBE+FBuLnkffZS75E\r\n+z3qySDr8G+Zj9HGCymmQ80sL4+AJqq35T4zxMbE4SRTVvbcxFnEcE7T8fHG\r\nZIbxS1HAF5C96alIDhXweTct/kmHqJesOrIivqbZxvw1hQyzwJefuWN5sabN\r\nTvIh7hnIpfpx64+TwEDV4yDDwUt4KChkonM=\r\n=ktzW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d135a30ceb19eb348eeda00f528666b2b9053d4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-WQz0iITsw3ivK3qlvOdQ8EzySMHhZWH6r4WDZNGQET9dV0eBh+ur9uLbJ/TWUoGgaklPt4nmEIud4gdWDCKMOw==", "signatures": [{"sig": "MEUCIQCGFlq9c06Zid9c9YKb+1QBCa5t/Lt8wCpxU7aG+Mq5WgIgRF23W2XRE0kZ3cb723o/0l9zVZqO2E2OB6gkqoFNbG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSULBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMoxAAjm6us5KRLzl7UEF0Hwlp39P4yvK4V0sBc/3G8YSSGlVztjWR\r\n9pQUeS6kfakpCp93kyP4sVN/N8EM5DsV6nf7sd+nAJz6pyDg5Yb/s8sHeG2n\r\nnpotWbZZMz6STmBm8DLGRSuLNiwCyqmExoYB2EnfSuzpV5Z2TY6+ZFg3zQlf\r\nD/yagDWFJZKp91/le8A2xP1arPhywXwRL53y84LL6sVWxvPY+hJL+JYigiLW\r\nFkVCl+pkIuPg8A3BQhhCXgXaMcgyIilNtef0dCbisDFbgjOoRgliEbtxcpEz\r\nDrpy2HY9pWbvkoZ0PNOKiD/CizE/AG8Gmp13oOIAEJ0moNpQsUkmPDthO6tK\r\nBpzdt78iAgvJ5zFRW5qqUEK5KZWiGzClgGlC8Grc1qlzSBOfahtU33rhv0a2\r\ndV6Ct5cLEjkWd4M+1N6St/h9SgyHbEFTNNnKyVjxlLXPmwGtVTnb1nZlbgln\r\n3EdEUGIxEXeHSN2PBfivlj98aiX5sO/XE9pK1DBI3kvYRdrugpGCED/S5cUo\r\nA55Cj710OQojxZj1tcxVbg2eIhl+xDsT9ao5RPjtHdS5nOtX9hIvUmuh4ybO\r\nXs/bkD3qEln9JV6W372vk+0QDNHXg3PRMBtKfArKz68YkNvnhINu8sisq9Wi\r\ngF0tT5aTd8lh+3ke9HvgyEMx8/+LKdilymE=\r\n=jGXs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cba00d8705bca26c29b4ce34c21826c356344475", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-MpMgHvdUzwku9FgGD0GxH4w6mV1EFMOeUSr/BgD42kFhRX1oxN3ABTmZnnV0XZDf/V6jsSMmbRmlbr769sr7FA==", "signatures": [{"sig": "MEUCID1cNlFzPFklm6vZQ5xDIM68IcmTgcUN54+nKJbNQCkKAiEAiP/Io62QUt8q30SAyU527UtDIaBDsuUyA7+g2RjG5aU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRffACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgjg//ZjTwkXSu9osoi6uipUZwx4Eb/oQHa7O9pfrNYhPRI40pqPVh\r\njGWF8u/GrldNL95DMJ3rN68YwPUE2fZLOptR9zpihB28lCU8oZCpqFjdaOyh\r\nn8YtAzBxUWFcKMoC/r6zuDHp76eJMd7YdEFy/MFllElQZhuD5feDpd4DXXq9\r\nl9w1kNsyd0/c+l9CJiBXaoxndYhreX2IX+lNbvwJECZQ3NWe4lR+vp/rfBep\r\ndZf5yiwgnvGpZGMXKHoS8+++QK5SWhorb+ba3acw8Lesw9xA0zJN37KT4okW\r\nBVX6RqZPaVOzGTMb1Pl9U0pnXOCBco5I0xaB7DuT3s3pXcG0NA4//yYFDgbN\r\nylNgKYXBShSvRZqajvt6kDxPnNljuWyF9zEXZg9M7BZbNRHPhDiAKkOrsNE5\r\nB+BgLBZNjoK3WPTFY3mODU/aAVoZ4sWQabn7Mdil/eStuWD6aYAXyiGO74TW\r\nbJkfylZEusFEUsl+XXZ/0NVuscWmbW67BAlmXL75MfvtjJD6W74VBBumzHpu\r\npjQKIwCko/ToIljQwkFj84Tw5iimcCVLhfXhGPbbnayhmAf6ympuOC0X4qXd\r\n0oHHTONYjcdYgS73MoULEiZhM+yySs6Et+LjCEKpMQMbOBUh77h0usj3jcPQ\r\nSZtjSg7ddXkniDCdmIfi2ikPT5ZBKhWYRuU=\r\n=gxL8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0c3cfa921770dabc458a4e6964125931b63c536", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-rTWOW6LR5dVwk2boXMDJYjYzfh/3Hc7A4IckViUb6/PM8RD0MRuiaHLFRd3LnTX0drCZ4Xx4O6U1CgNJHDviBA==", "signatures": [{"sig": "MEYCIQD4/jODIJVsGyELQ6mUVz/jjrRNigX4tvDroqsKNr3Q/AIhAKOki53aoU4FTzVkUz6SCzGRshp0LaFa9lzrNvMVc9aF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMkQ/9HOp0ZmUDNYOruE5UzyIR/xFJnfvSomZL9jmq6c+bYU8dIhtG\r\nx9eHGm+TeGld4nCFzT9cct5HF0eROrlLAdAFfXtK+pPWcIgX7IlNdkyyf4Fs\r\ngU+1EpZGlj2vK8C+ee/HQS/NmER5bpp6b4Q5IK4RCWV+FTOSvNZyNNcYYYYd\r\nNy+RC5Un3BuV9gT2YSpgK1SwGS0M8uI3X6w+z6dTOT82tzJilooILw2uEbY+\r\nv7l3HWh+HNiLd8OrQBBQRgpvTRsBYTPswuLVzNMQyDiIUhZ0/6SaioFGwd6L\r\nLvRn+BXTt92lVduxSA9mU5yc7L4aMepMKVmZQkQwr+OnvVo8RfY9ao6t7wVa\r\np1SMvlbTblB9P3OWwZj+VT//OhUG68sEEwRciH+P+AJFPsRGlfW2PWsspGL0\r\nVQQy/yN9/U1hATxyh3Mp4xtbUshy0fk/1ORIW0rV4OwUvf1Tu/zi+dmb/e1N\r\nU6N6/QIusR0aZztP/ERjFDg8vya7bkI3h3+q/pZrLqqT2izhHkv6aO9dnFuB\r\nSzXPIuHJx5zIWcn//dQ9hxVMLY4BbVImDlol7zY8Ei/qbb5O5lDWuajXNVrq\r\nH95m1YnxvrJZOqeEKS51vXgoQjtwg7bkBNQa0PR2peosZTggtkc5rUJbzCum\r\nl53vtJq6ozE4roji0DHfIgj4F3Jwfl/tLbQ=\r\n=wayG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "50d73e481ced413ca2efcc103b00f2ba4cc55d16", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LYXSeU+oPi5xR5DtfpTnAHZbBrHwcS9115lbOtzTzt3RgX8AU3xHyoIgh768TEeEQzyB1BXJPr0mLam7DRv3gg==", "signatures": [{"sig": "MEUCIQD8lcmpDLxQARRsvtYutZtIghVTbA09xMFMS8NGLZ7j0wIgYpY11C2qofLIHLVMO/j8q9EWqfpMxx66ME9jsjHTsJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS9UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKmA//cOCBmissxxTU/anohWFnkp7GsLMgdsXxnCufOkHhYi4271DB\r\n/uCxPE/F88vdrZuvUbueNwp5zzRvkvBUJls8YeSP3trSZhXALrwq/Bn1Zk8s\r\nzk/SNhtvXnIoq9aNIWRCMf3L9/VI/0KGzN1ik1zp4DLgxkuC4vbJEGYDkkvL\r\nV9TVgse3YbM2Q28tN2e7aDcmyHtl1QHw887ucjRbCcRoDr2lAEN+ZlMJMqcS\r\nIE7Zb5mSjxiQc8F3eItgtpaOsp6NlCCs/REGWoQboctUCfwkvnhGacCknd6/\r\nv8Bej8nJcgocedUE70mquAM4pG5xBNMF+v2U+rcdxr0iHUm8G9evKZcJ8UG7\r\nqdZZNJFIIpSaDWslPPlYTkAYgb2rbygXQPYDlDRgo6feqsH+FhhoKVLByqgQ\r\nyuuWFqmiwTkev9m4cJZXEIQJIPbUSDat2G66DIw53XKAJ8LQRvyqt1pU5HCG\r\nA3o+stsfwCAafj41Gq384HeQ53JkW6oGtWiXkO2zJctzCrA4pqVdVyyoGGbk\r\n5+Ei0ijgLuFZB3Z1YIKRbdHT67fEGBFN4KMiRVN4aJNJ1V0SPpUeEvC8js9V\r\nsHjAWGncT1w5zkKLAgSSVI+2m8lO1WeGMBPTbgD8MOfhcvP+o29O7uWJAml+\r\nYD4afxB4XXPeN491ozo1Z/+EtZ+tRYMyLNA=\r\n=kGhp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "09ab6455ab240b4f0a61faf06d4e5132c4d639f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-DXGim3x74WgUv+iMNCF+cAo8xUHHeqvjx8zs7trKf+FkQKPQXLk2sX7Gx1ysH7Q76xCpZuxIJE7HLPxRE+Q+GA==", "signatures": [{"sig": "MEYCIQC2wfLmvsRV3b0dFPmewbSAoSbsgb4HFmnE0NR0IG/WxAIhAJHx4kxL5XFxcN6KQ49+7e+FVxEp9HzeRWcU2X1k4r7I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS/SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4yw//emGdvli4D4LuLhZwHMtIRGp7gCY0vSixFDy2Jpy42TpOqyHV\r\nF9dnC9TUE4p+VXmdFfwm4u/3l32EX2C1IXkhHdJrjoLzW3WSg+PuDNvnKuTn\r\ncw1VWAeObV37wE9j5G74qS3wzvc4g5tkeoJOMac/buArvtxckGduxjlcWibt\r\nuN5PaQ12L/vJ0rUax7vcpgKA1FLFFQxu4pkHJsNzK7Wx5e1frTRxpRSBsg9O\r\nh3sHF6caIOoNVprB770v9gfMmpNc19HP25u/Y4cBiu46th8MomWlw/zaK8F9\r\nOFGa7rNqe3QY413x3YazXXQGf3wAtg/wYUFSC6t925A/Phity+OoAouFkSz7\r\nmkg6kzrYtYv6zPxQYqi0U0DrMI4i0hcqBuIeo3CuE8XSdmE3OskpZ+DE2Ez2\r\nUHtMrnZBe0FZz9ywe0CoMXnrst1D2qsrAuKsbYSIBz8fHbezsxPx1nlokYBc\r\nmIL8tTqM9muXjRjl1rICeMQoYmM0tg9tK9dqpEwIRwvz88cUgzy0i+pBG8Rb\r\nqr94Kw61y6HQXkugj0sUDKzQ/OzdEYhCiW/GxmVMnN8WtcPPJ1vU8vGTo13H\r\nkXtlkSgKibTJmXwIgoITW8tfOMgyhRr5jd0d/W1D5tpqpK3N81K3ZWkBBovb\r\nwD9McEQsMxP1ytoccMV0wkAGdVhVktOlTv8=\r\n=3hJe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "17c0f6a14d2befbef208f519204f1b44c40435ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.1.tgz", "fileCount": 9, "integrity": "sha512-ntPsRAx6S5dnEqWyzYH0c2pSFM0tC+0E8dAfa3qwNX65pIv2yY7aIABgn0N6LbstwYXF4CAegzPi1Eaq9txmzg==", "signatures": [{"sig": "MEQCIDdMKZ8Wde+TrHhKtjdUFWllXZF/2HFrTymxohvhRRoDAiALp1vvb5mNFQglySDwj60lBozTowTlvQeZ5DS9NJ6IYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7697}}, "1.0.3-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7850f5246db8687946528cb7b5daebb8dd684420", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.2.tgz", "fileCount": 9, "integrity": "sha512-LetKReJVnmwY8QJpNagEB3rxOZ8hkZLFFrApghVWZwbq/5RvjjcONZI3iPw4DVytRfc8Ekj/Its2cqRckBaf5Q==", "signatures": [{"sig": "MEUCIBoqBTA19MeExqGta7e1epa87roMco+eLk7x/iKLkW7vAiEAxkvjScCPPki5c1kW9LxHdKNdlUTxcsPdq5ja+/NXyYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7697}}, "1.0.3-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "2938cc0e1bc462a4e7cc5d577e3c3ee81034fc22", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.3.tgz", "fileCount": 9, "integrity": "sha512-/fTW4N4j2FKGQBq5Df7xwTusUQkCgrMa80oN3ZP/NjaG8HD8OLGdOp7dmNy+wyg38uy3KmEJ631/3FXR810f7A==", "signatures": [{"sig": "MEYCIQCM06Mc7xbeJy2DyRxg86vhMkg66gfjE+PjErg1j8ja2QIhAPh1IIpNSNCcJD04EXZznulL//SXOIG935xa9B/hOe4q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7806}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.3-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "079cec9e9624099ea169e4355c77f9194b17fe9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.4.tgz", "fileCount": 9, "integrity": "sha512-/X/0IPYn1TgF/Mj2zIM45bt/v1nXD7lolhb6q2NFo0314cDpSw9/H2q9odnLvNhc5+SWJh2qd7wq1T5Ht8wI0A==", "signatures": [{"sig": "MEUCIQDx/lc7vi/mKH2AFQBpc+TZG3XQcHqC+vCSmUUalDMymQIgahEw35H9cYvxLn445DA+c+Y7KsbuMDZsGLiBcTL9jgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7806}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.3-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "52e2e0f83d7288426af1e7f791f3e8cb01e47810", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.5.tgz", "fileCount": 9, "integrity": "sha512-iOdKR5hpti76DpTkAyymkEJqItL0N1YG6MoTtGciotZD0D9hQVZzT65D8s67c7yFQiwFxbnhPNNG0vH9bTgvfw==", "signatures": [{"sig": "MEYCIQDnV2w4z6XOT64JgrYmI0UBL8SKNmw79QjL+2GLcFPMjwIhAPCxente24WrfRioWybeDTlE6IGM4qD4nOpYuslxNP2b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7806}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.3-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "d848211e3fa18704ac3c40b3f64bd05b8d91c913", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-i3UzSpQWGw9reb75QlPr1IGaPXnbR0Ytu+Sd8Y4o7LM7uAX0llf852VHTPhnTlrX5boQYpwCfk8uKjStLaL9LA==", "signatures": [{"sig": "MEYCIQCntYsVHJS3pKLXe9WSrJtDzUgTaF/DcJQzrMDuMNKaiQIhAMEo2+X1Z+6yVfT/VFHQg+Dcu9IFK/aRSIHs5BusO6iQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7806}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "217b840c250541609c66f67ed7bab2b733620755", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-vyL82j40hcFicA+M4Ex7hVkB9vHgSse1ZWomAqV2Je3RleKGO5iM8KMOEtfoSB0PnIelMd2lATjTGMYqN5ylTg==", "signatures": [{"sig": "MEUCID09ae78WFtjfxeLfCt3JxwFFN1hlCqHB/OcP7JGsp1cAiEA7Y/cHgFfxiDZDjnjU289wmYQAYkR+en/NIizEa1G3zE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7768}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "897d5a689d6607e4ffd216e352ea6cad99c019f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-245ha9CjBizxPfO77AqrMJ7TxOA/5BS9loKuMmRkxSEWy6+STwrnUUB7XSVUmFiTmpwtGH4p1SR9sksgHJwb1Q==", "signatures": [{"sig": "MEQCIFnZZtRURVboBVDpd4+CaEGopO5v4o9SuNTJmrAELbTOAiB+xqsAxJg0IJaxMQkjRD2Cm4E/q2oXyD41/bKCyy+oCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "641fea513b82902053b15c5360690fdf3055d7fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-KSIZld5UbgsPKjLgOuIc1pW/TVr0yLb7iV0m++F8LPPy20ypwrTts6v6CjBxRx+amf9oZKipuCOC0KmRt+qCrA==", "signatures": [{"sig": "MEQCIF0/BfG1lnxdVuXG6CxTSbFMPf+wxOa+OiJAojrn33ciAiAwmmxdyGnei75/hWtPDwV7tC12IgL1ZqOZVSAMN9yW7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "995c7b6e07b397023cf9ac6cd3487b1f4cf772fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-neXANx/+ac3/GfFzKhQ/giFfdBvrDRdeXE0EfwUAmAwGfTwvAUZ01kFApCRaUyagOa3GvDfJO+y336nL8JLuAA==", "signatures": [{"sig": "MEUCIQDU4Ll353NoIFxir6bvvO6GO19x+IlOS8Cd77HYbG5THAIgM6YGObknKnuEMDZ6QrMBRAmT9eszeKDxlBCBK9b54iI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "cf032be523ebe5952d650340c38e0d7bf0a9a15c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.4.tgz", "fileCount": 9, "integrity": "sha512-e2PFzYHf3Dn/IVCo/4X/lt/SHXN/MVENUhZvVzfGYJqb4y9gFZnxrUc8wcKwhFBV8HVtxpyUVvhyItFyF1s4ow==", "signatures": [{"sig": "MEYCIQChB9Mzh2qpuQ3hM7O4VD1vcGDNB+BYXxYjf4uGrMLhJAIhAODeY9HhAdouylFsX29uGdHz2BHIIhxkBqJjADZ+MowB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "9d6b30ae84b70bca925c12cd69fe38cc3ddd54a4", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.5.tgz", "fileCount": 9, "integrity": "sha512-VleGPcJKPvP9MsNettpFK/IhOhzdBIg9RgN7ug2qCJyPKwQNOS54Okr2J2gRcmm7uG53tI+PAouulyaMbkzxhg==", "signatures": [{"sig": "MEUCIHvv8LuxOAGUCjPjivzJlgnS9NfkAPk13zmnQvFIq5SEAiEA/EzDsRTFVlbBZyRxnKUrqLSw3WlP/biZ0XnXuLMv0oQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.4-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-use-callback-ref": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "608cd8f1023b4ea0cc97939c242b56dc9e8e36cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-sjgO9StzSrAq5KMRBsvFIT6frDHa8fnyl7Vc3riycJRb5MLNN64Q8mafN6jd0fyC21Tg5ASLasRPMrGWleni6A==", "signatures": [{"sig": "MEQCIHPbudJQL8umG/bzJf33zWA/cCcawbbU4s4CRB/9GjwVAiBSNpBfQPVIn7gCEWQzi0+hh0P1tEB1LGdyljDIG2kdRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8211}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "8341afc972b78d9d0ef38e1dad6130f206451d06", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-AFrRfKcvAFBSrer8Eye5q3v5qA+RxwsdN26rO+UfBRhfoTA+wnGykRex0iC5OSTb9hJintMqlEQOakj+TTRiKg==", "signatures": [{"sig": "MEMCIFv+veCE3r7GWwGQD2POqNXbgWo6ImtcNMk80HX6gb3DAh9OpXacnEX9wRuqnSXgSlEzKPoGIN5+Ylw70Kya5iEN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7506}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "d685aea0c94d8cf2e5db3451947c95d7f65c4e41", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-eo8Z2e1OPDEbbGjqfMxnnZaoo8eEiZ5wErqPGRpYfvCJGWEXhAKYI0OgJ+ZvWzehgSSR916TXSbYYApy0i+ZDw==", "signatures": [{"sig": "MEUCIQDVBh9UYR1PIKBn+Z6FNcDaQlt2B51PLfAQSGULewQG3AIgU8yVBiR8cz4obD5okSf2JSoziDXspcWgcaVrDqNZ1G8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7506}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "a8660cd327b2f62c3e7fc33b822985b9c53d2be0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-8jeSeWfuUFzl+ZICqCiL1H/F7iulH9DRRYP2QkA1lvhTliNKO/tGulsxHHJefwUWet1G1kfecKjOIa2ZhxaK+w==", "signatures": [{"sig": "MEYCIQC93QjbeaJj8d5LvtZ0HFxIYDsGiJIEJTJ2wuxnSfTnxwIhAKfMCjjTIByPACoHleyRj/IkFjNWmOdnF6FeMCUFCDDA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7543}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "b9897e9481e5d7f4c598c5a780372ac88f2e809f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-3EclE6UMKEBEGnBIgImoguydEn5LmyI21sqHBJa71lB+2m1pNke+KZ7/QCe8SLW4nQTnNICmMiKF05rlf/d68A==", "signatures": [{"sig": "MEUCICukRTnqOqERYCXAVgZoW90PFyuEGrXB8KRh/r2FK7AhAiEApkLYD2bL/WEGH9KkrGmP3pQyCKw+zdaK+V4u0vtFFhw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7552}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "d030366d9223d377a9e75ed9c3ca048947771ba7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-u3chfzShjjZpnNrTWRzHCdUUuvX57isInPBuCacze/A0CjbwCZ8DpIvrstodAh+I0rrIHs0/+N9gcFT/DDJgow==", "signatures": [{"sig": "MEQCIAcIAdorq2EjfB5z2IggDA0Wg8OdNZOS1Z2IDLwgN/epAiBldTC3146JHAuAnxa7fdDXq0igpzTv39/+id7advBQnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7552}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "8b480dbbc3a64837e83fa405de7e358fcb865d70", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-EDccJfsa2n5pvf+dEoPKDAV/UpHVMGsJwup0oXMkaqw8RBk5LEwyWV8bHgUCr78LrhQ6MigwgXlyJfR1aAv3pA==", "signatures": [{"sig": "MEUCIQCCgY87to92RgEBQmRLqFDrh9ofjw91ZmKqZzulQ+xC1AIgYcqhc8yTKkAWa9usKsL+TmPb2KbMGUt6mSP8LSwL6q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7552}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6b79a2f43ed33461591e1dd0e9e8f1ad0b63b267", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-3lmXZf4oRbs0F92znkvvbOG86rDU+z4oiOThnLar6PDiiw6k8iMpgY7tjUeWKMIuYaVEXfdNO3JFlEpZqP93eA==", "signatures": [{"sig": "MEYCIQD+h+abixLOSPS8nEFNeLsyMV+/7hOc5uBmK9HA2Yt7CwIhAPDjHjG0+grtYMKN4F99vBOT9R40+uacvPr5HZfWAFsW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7566}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.0", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "31a5b87c3b726504b74e05dac1edce7437b98754", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-L7vwWlR1kTTQ3oh7g1O0CBF3YCyyTj8NmhLR+phShpyA50HCfBFKVJTpshm9PzLiKmehsrQzTYTpX9HvmC9rhw==", "signatures": [{"sig": "MEQCIGo7w0E/1Jusresriqp5lC40gBS0fkhpwpFEccEmuxAfAiBGDpbdOgjyA3X+E/JZbUba8i4m5/+ZZArs5uNk6zKSww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7528}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-use-escape-keydown", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-use-callback-ref": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "769319a2b294c0248a24a68b5d7a4c6e0fea148c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-p+Z9ao3rwupVTjQy2qeK4g0daT29Or7MV5iUFSTV0W6MM9oAM4gb6hbuphVH9BV8G8ZRHChOrPRbkKLq12y0Hg==", "signatures": [{"sig": "MEYCIQCO+xU8RHpsYm5jvO7Ubmbo/YzI7jo/5s/4YsmnkH1+7QIhANrTfl0+gG5K7gmt0+xcA55JvbiEi3gKikqhLIzc0zHc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7519}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "2b93401aa9cd9a77a539e8a9381d6b72d022952f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-USWDPgD64ocofY/G4/v2j0HZxGXro7N8TWESXoDhPCgTTs3sZLY6vY6j7pp0HIF2OM2gDXwkT6/jAOk7rjsE5Q==", "signatures": [{"sig": "MEQCIEgPIAYW4H2u/fv2i0uMpK+GDwQSx1tuEjiYlmAwFkY5AiB6VD9b6NjiNInojHS9kN1dKjsTjNMCiReqtRSh17hLAw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "14b9424785039808bca520ab17c1bcba42f117ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-0zZHgebC+a056mCz+NIqYj7lySXg1uSVn8ONGWnAFtbohcVleorbzNDDENVR9vjJYOYVm9IDcd/7+wUsyB3FKg==", "signatures": [{"sig": "MEYCIQDpLt+AEgAYxkt0Xw4EEhQPih/fQkQJm3aWXr1WK9+lIwIhAKgXzi+BHZZ6A3HsvxYiWHnGgCbTOL3+9uWdODv8zSOU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "d6925192af786f5528acc2478dfab0d99919e626", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-usejkb/p3LtUO8tpwWYMzAGHIhCAV+bnIfZZpR705KF3Tg13RfXrF7fOkd7+QFxFUBanAFWEzRSOrnsixffjbg==", "signatures": [{"sig": "MEYCIQDfOe/AITwDTsz9wQmJpaTf7myN9ytOeSdNWTVwaIBNNgIhAJmnwvi59cbdKQMH4/5MtjnK0Bv/xuKAR1Nlc2Gqw/7T", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "3895dbe8094208720dfcf561e442462974b57b9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-3uebLOBWmI6Rn4qVM2sfub2NnuJqsQ5sLJCh2fJ+cN6fqt1YNYIvybfMwaWWBxMzACUJjBaO/c1w88fz5kCgoA==", "signatures": [{"sig": "MEUCIQC1HUFurhPJj2GRjDqTN1oQJsE9TD5avuFu61qqiQxYTwIgDtD1tHcJ2bimdYM6lnCDNnGzEkP1Yn6nrrb/BHTXrxg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "4417b4e68ac69be4e3d224ba5bcafeada30568ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-hn/jtfhWsxSWXJ9bsBOqqSc3/vhNI6WZ2G9aO1R3VpI5xcOUTWv81nfZoOLC7R4XA4OD2BTDwPXwpt6TUsel2g==", "signatures": [{"sig": "MEYCIQDn094+BvjlolgVODh2Ndm6sFdTDQnk4KPGHc3E0d0GlAIhAJ0kd5ZFS1sNzOMPtY1wyzdOX8CIoEtKfDFtibfQY3SC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "5f70333cefbf5de090b45f3aa902d3fd4584ca96", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-ANPBa+4sSueyuFplQ5xoTYlDitWpLmD3AzuqFaw2jjrwV5TR8FdenfBg3JZzwICeLIPNxXbVfWoB4zCAJH1M4A==", "signatures": [{"sig": "MEUCIDNGL7TYmyybjnXGAkhuk+bhMKK5CY031gMEzb5WP+2GAiEAq4gdQ780XSl1gPAM2NWLonsIles4fleaKqNEl/CzQ6g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "b15e5ef282b29e26b6f2e16f6bc6ba7f44d58ea1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-XkmY5qPbnl8SWJstqvgl3hpLyWZspsYKQK3FBDMNioWfrg+yyYLV5Fz+cte5Bsanf8LFXeaBl4SSgjF/hOMKxA==", "signatures": [{"sig": "MEQCIG66UZwvqhYWnsqYfr+Hz9uUgx2eh470jzIgyqLagZjDAiAi5x1Rp6X7EnfFxKlcv67AVvbR8qHIkuJzYSmOymQ7xA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7899}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "bd3a776809a0884548102a97c427dd69028c6434", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-OoO95nc0XdSh8mjbQEZDb5Y7d7d5wY2f3adXmFQaJX8DcKC8vzDXiD2PDUH7gQXPUNbOiNBXSMtZT/uuzmO/ow==", "signatures": [{"sig": "MEUCIFgALr0Kr06fIaewYSqRl9+QVdZHH+M9lyxWRgnFRdSzAiEApvJpTE6xgNJM1lh2VsykELUrVeTRLvd04nY43iOZPpk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8290}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "a049d80001aa9429a58e0a10b6429131a6fdd588", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Qu/p1Zk5TKo2G+hFbIDjNwlopVIxpLob5+Egip9E1QWnMyXbHt9rMMOwNOFvD6Gj2APdntCv9vGu6qjBG8HDzw==", "signatures": [{"sig": "MEUCIBA6Epldp9JtjDQcwuTewZ4RdhOUsx4LzcJ8XEqMN5bXAiEApT3ALwnEUXm0J7bYEFNieWzDP0VleuKNj4CCXHNm6RE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 8290}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-use-escape-keydown", "version": "1.1.1", "dependencies": {"@radix-ui/react-use-callback-ref": "1.1.1"}, "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "b3fed9bbea366a118f40427ac40500aa1423cc29", "integrity": "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g==", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz", "fileCount": 8, "unpackedSize": 8252, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAKXvLpxmHp5VsVkrjnzNYIL6mPGb5TS+S2rrwh2EOVLAiA7XBDebZJnkYpUqu1slGH0dsrM82FSXqXqOk2s3qQe+A=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:20.140Z", "cachedAt": 1747660590709}