{"name": "@radix-ui/react-collection", "dist-tags": {"next": "1.1.7-rc.1746560904918", "latest": "1.1.6"}, "versions": {"0.0.1": {"name": "@radix-ui/react-collection", "version": "0.0.1", "dependencies": {"@radix-ui/react-utils": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c3004c04b7ccffc2ea3c8d8f0c26007de4079b98", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-cLF2LQkb4/byjTVdaE3Vzlhzy3vmuT1F8SZK5Zn+M3vBdcK41qmXDNDSke+dyinj8UQgrT3MmMz2rglI0RCVsQ==", "signatures": [{"sig": "MEUCIFE0dhHelsTDuKtkkkqZk2UCKFIQHkZU6cAxOepPiOC+AiEA2Kj/cKlP9Rw6NeKWQSSqGpTOE0xcy8/VKdITLRPRHEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbICRA9TVsSAnZWagAAEQgP/31uc429CEj+POy3v0r7\nihQzzRQPFeS4pkT0fhNqPFeNfXChPTG8NOGyiXl6/CjTsBz9o264K4s3fher\nFDQ7KIBkEKthRDr9o6CE2HxkZkpd+5DhAsXXxx5jnzLF0Whf03IJGjTO4v2F\nkVjwb88A3+gjT6lmtyw1EcfUf+3Qmx3RucXwzgkcc8/6yp1uhewYDwuhZBpy\n8mhQwEKLkEvQRUlvwWkmvrHBurNO2F8O7KCp4y5cqcVXcZYvFc9GIXRtl7jb\nJH4m3APwfokl4ZO/Z/d2cb6fZVkJxG51Y8piTprQ3FcostqU547DepEg/dCC\nB+4CZpPR2UWZpRoujExj9+HciurCnU1KvlCJ1OsG6HrxvboE46Tr+9wemxUW\nB8GVWtfg/Oqv5tEurVq5G7HRcux8FewlnOlNzn9OGZxx0xFHf7vGZJXYkjTy\nabrEumXaiurRNKRUSEH84gyb5Smczl7ca7U3BUnavv1QDGkr6lM7kbTMRLZ+\nslv4EMoUInegtWwUNeS2pGvb+jlL19WvdopB5qwBsxCKaKAUdVVmZrx0t8kj\npUdCNtbbtggqH1zPlP2N8tgJPOMLFl3tTma5Dh+WkyC0BKcIo84p9ephmJhl\nVzn5VCojyqPaLlKkBWUJOQ9RAsDjQ6k4Odep8ynaSFI3pqKxQiBaWNt/D9g4\nFzt7\r\n=t2iN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-collection", "version": "0.0.2", "dependencies": {"@radix-ui/react-utils": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f83f389e5a2b227696bfc16f3affa1296054c6e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-Ba/7q4P1wZQJPnTPBYhz/DRRmphoNM+dSy50spatOvdyBZTe7XIpdtAURFw4huzzj8/GLvMeMoCPIG8YF110BA==", "signatures": [{"sig": "MEYCIQCkc3+5g51McH30+Vi131GCJtg+l1TxR1ly/kiQkiJY9wIhAPwbMOD9vowRbeOyato777gtmya3NL//wdKnyey9wN4+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvDCRA9TVsSAnZWagAA6T0QAI/WMUwNtjMa2qBbe6nc\nxuN9TwKBdgTpcWzrCpQ4rSp7+15z6QLRXTMpOAxfnGT/F5SLfF/Egd3pe+wN\nx1ctBiq2WAYmUJzQlLwKz2MCHnoSuXN3opdigCbHbSezvrNsHfSUfUpRMT97\nYPu7v85/uVxPQWGYGewf9sGJTQjPRdMhqPZfADaJYRblICcR9tpB/txfz6fu\nWK0rL6JaDZFcmJMSiA5JzvAo/ka8410QAUrg0rVElhac0xUbZIg5AhA/YRxb\nvwPQuDuGm0VeMTt7xJjQb1JDzOFCaccm0dxEyo65TG05OiwTJceh8Ocn+AfQ\naSTS94yXGsegbYIMvZjgaPu2VQ04W3SsR0kX/R5SG2Y4bhV87TSCAueGnPoZ\nrNbGs6rWst1dmoJOv5POjpLb4jS2Ea1Oh7lQmM0lJ2c9Ixidpx6ohqSorihl\nrI12CqB/qQDuU2lAtfc/FyYYVCOh1Tr3BbwL5BcMJnP8qCNTEMEoiBDD5Xtl\nfwU2j5aSaqu2MRcAS25v35gdErgNqwCVkylEbNpVYXo2CYneZzOexqgzVbvM\njXgw1x5ElCdJ+7kp1FOIju3gp9BGTQgCoduFsTl+M26RcjN+FtVKFeVhRB6V\nmleDn8UmooY+Q6dVhUwKIXC6Q43lX7SWkoiZDcb86nu9Xh1chtGMNXuzKiHq\nS5pV\r\n=6mvX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-collection", "version": "0.0.3", "dependencies": {"@radix-ui/react-utils": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "777b15cabdb9c8df9c2d69d917b060fa28795d7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-n9kLG9hOYS8WMorYeNToUKLz6yFqwA+Y2M14ncwd3vX3u4s9rR4HcD8xcyXv9HWVwhttVFBFReYxh/yF04kWjw==", "signatures": [{"sig": "MEUCIALURxw69cATR7kA7uhBD7bNwhDYIZD9oKVbV78iQ/3eAiEAijlOHFE12wF18Qq3nCmVcZ6T+vtphUDTnWFmYDY1Dq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETs9CRA9TVsSAnZWagAAvmAP/Aif9axh/fNxL8WIZUYY\nZpVtbHEjTacTpRpJ7x6NrbaIWXWcjX/le89B3ykj893/RhnpwDiqsZI4ZJp3\nBoHuLJDFgdbG/eyesHNK/MK0PVoGNPRrfZQa2rm2i/H8H19WGwh7wiJy53AC\n4xg1In1RDHtqXfgvA+sZN/7i5aqVQBIKK9QhrM6OTwdr+Ajd/uCQC/D1z3qF\nrGhQOOGV4BTEuNYpzfEKWMEOqLYFcz1a1ANhblgKT2z5fNYoKrku+pvDFQ8p\n8fJs0rsaLrnNzvzs1pIEWEd0YL7zVb0GdWNRJN8QmGdZ82WqujyzICG7AG0p\nXrvpfKt724C9YCUPOrIUPpQ5M0rWBVmo2L3tb/Aw2SJ02SyhCONizFpNZZy8\ngt8PkGKeULgwbSI0NhkOTHroiSgWYQ6j57nLfywGYe1WFHjfHfOUEryIWnSZ\nnt4puPI/cFd6W33J9pmY2s9bHnPhaT3ygM4amdJ/el3NwApEdu9ecwnV0zxk\nMmk9cZLXV7rb8NeLjA73OQ7lPEOvo1UZi6t71sutBPIS8coOOzGEfh+F6fb7\nzzbwmokws20Yg57CywcR9Zg8CNvgObQku2GUeGqXV/WMzGALPs6TiEIIPmap\nTqK2G12zsJGNbCd/tE665nQRdMR3ToRyWaYOP4eWFPKqbcuPk0TJvdA1N/ey\nw4JR\r\n=AVKc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-collection", "version": "0.0.4", "dependencies": {"@radix-ui/react-utils": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ca17f83eb7f94aedbf11f65b32ddb6ed125385f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-wx/zJtw+i3FFfCK5eavn3WD2sGDPj354WZfkQFXRkZWLc3LXoX6FC2BwzNyejWLL1M2Y03sQeVcTGKjPpnUdIQ==", "signatures": [{"sig": "MEYCIQDa0GPKbN2+HaK9oqskzCPUUiec4JrRZKQ9FWl1r5bfGgIhALuFYsUr10CkaW22pMB6h9LcOz7q1kUxL4BvTHW/fLof", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/1CRA9TVsSAnZWagAAyHcP/3IA+ZlU08m9pqjoGRIb\nCBNRcvFxT3LwnH2ZpEVTsAz5q7Ec9Ki1ldSL1NBuT9aTWW2a8b3X55R6hGNA\n0R2CEB12kicmmTEaSBnYpsIJhcxXCkdN/YayXGJup9QS1KyGbQ3Yjn5zMeNp\nUYl61+R6+IrPXXq3bH0TFdLC0bYLJ8W3iOKEV4N/eIEdF7XkVJVLbBAKIF1s\nd3va5yIeRR2U+DaZwBTisrRemhRoSWwFavfIKCVAkSuAkB+PULwHsUKSHjfb\nxjFdc4uBCjJq5pOheHob6dIgy/Br31z+gKSKwZk6trh3i/DAmQVNzPruH9/T\n2rajWf/kApzNrk14xvlp9B64fUVD6bALJHFiRFRQgZ9aT7W1BAkadwF2YbjC\n8ei6lO0K1NUAwTLxcvwiOWNLXhOvorlOY+6dyvjDrYtgaKgc1wBrnBHnIPTX\ns6W6kwMX+bNYTmt2jt47zvyPyA02yu5h6Ka1O9cL9OE4b7igWX+cI6rqGvNt\njoPhW8AijfzEoXLzGkN8SKcNFmkeOMbiruuOatKTpNGZ5vQ3ayKCnXyZaS40\ntdifmiXaOCHAsCmGw8+pHmOWVoDz00g5q6qPdEHcu5uQHxFEaC8JDpTfR1Xi\n0OK7SLVWFLIa99pSIzGH9sP5zKLko1LpxTtI25RbLVWXqjCGLGNL2bkMmTIx\n3N8z\r\n=vSCy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-collection", "version": "0.0.5", "dependencies": {"@radix-ui/react-utils": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d7fdc779d1b6139c505ea79bd86f347a0c5de48b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-vM22+fim9T3D/XJO4DFvL4pThlJ6hyJpsivipOYFFLYk3aBYMn/rE4kKv5q7eHG7lYamqLtU9LybhMWQeZqcLg==", "signatures": [{"sig": "MEUCIQDdT8em49YdJaNpB51CEi+fnvFgP0V/b7K3v0/075cgywIgWQCro/aICVSL0nWbciRuLi+dPqQmPIvtrc6BQG74EvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VuCRA9TVsSAnZWagAACfAQAJjJsKcCQJf7tLfVaM34\n+lvxgdeBi1ksHb/kt+9iC8GB35SNDyxWyHFpEOVLwhGQp3dmkh3cR/iN/mJ6\neWbKiAydf38TH8iGHjRBo/xSiCx/UB+L76LulkHFd+37BTUvaa+902FSS3cv\nTAiryH7OJjVC9KTdYh85gytcwb1BVO9douN1J0u7duM3KexXKZF8xMxsu4bJ\nqJL/LvRzgEKgj8yfiIuJCKdaaYgIZsyNPWIFiVh4eFdEvUClWsut0Cy+P4O1\ngxFpKzPBFkdNYhSZuY3QqyYqRnK5K+dXSxOfdohag2lQmTQeU3yfiCPl8sAI\nlhXHLMfR2zBauH9REEmHamZfe0rZQ4ZXSn09azXl732ZAB3Wg879KEGf9tB9\n7PB3cw2RJk3AFCYVO8VU/itj4C3L7dEjh94LjJF6jYZbll1QAo/oxV2YmCYa\nR1gqkqovb506HTEouJWxQ6f+yKy6Q6pnT1gKD1Z7w0Dw1PafLDaKBGgeuCaL\nbmxjYVKZ6G5dPzhjk1K/Op85PR5bRGa95YDeg7Xxx/KCIi7PlXQubw4IbZVk\noCiw3ehX3FFne4GMrBuxDtH4jP4Be9tAZcBFJ4pmeUXhFsklrPB3XYlr/6Ml\nZmTPIcNWxUn4ZIoIwmFzKAbBtQ+Je52HPZL2Hwne7o7bvtfWtOlkgXl1PkTP\n0N+f\r\n=G3VY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-collection", "version": "0.0.6", "dependencies": {"@radix-ui/react-context": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3dd5657421be04ba51cf9cd3eaa9a7993a5bfc84", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-knzR0PeJtCy683wXeQ/YDEc8mYPRXBRzK66j+/V2mewNBaLKUuUS3PEIzkcxCvWL8E0Ls/o4LAnkAn/11xKVxA==", "signatures": [{"sig": "MEUCIAT/UQ+Ds2leSsdlYXcjTo3q/b0kiiB2mUeQrxurRLBCAiEAoWjWM3VDjw+goSV/OXllOR6xMrTVcAMamrv0sipRYEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VFCRA9TVsSAnZWagAAieoQAJLRgmOQ5ku/a8g6LrIZ\nb0MfrtMFGRdsG7SmOxNT2Dv3B/xuq81zek3QxLa3kzhY27qPBnRGcy5SPhcX\nkdCBuCUC0V/29aQUguPvayV+7PR1YHGseh+HhcrhQseY3An62gnfgEsZf6Tt\nGSEiYpHPqmQeMTxCRQ9SGLfP0tQn+OreWBTP8HcYIJpWSsrviOCPuiRFg+wG\nNdznZAdipqB5Br9CeVOdEdpRuvT7E4mYuhxrtyYRYMSeItBJKc75B/qJC9bS\nE5QDR9jaoUxdIXqxyTfD9EZ4RCwe5xK+Kyb5KsEafsDkNGAkesQM+R3kJyQ+\nwage7E2r3ggLA3cC9NpDv8CqSlZDznJUg+Q3DjU26NMk1a2OEo0FiAxTft1n\neWE7DqMhJD1Z75zPIIvjUj4+XboKW9IXkY5b84rEhSbPOalpgEaFLYpTWIS/\n0yNhe2EQ/fkvzC5BpECK4E1pDuj0y4a9pHngoz1I2d16VGhUc0r8+JTZpQed\nE6TzUx2tqoPddyayMoiv+etEOkeiGVR2vb18LGyve8fgDH1VjyCynQHGuyFP\nvXvos0TOdJNRBcATZ+Uebr4/C7fJdsrRxK4RShLEnQI1ch4MYhOkPNwvLSAy\nzZJXI8jdbu2kh1fZOJl5ke+nWjF7DHiMcD1wt2vtgkxfRqD7As8eXl9W8RU5\nZ7Xd\r\n=ZgzL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-collection", "version": "0.0.7", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3f8002b7413dc558863fac7ec153efcd3f8d86d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-Scej7hzIpBf0fBC7DhfQdt0keGmOo5D92sq7trm8aGvjfOFT3SWaAeyw6c9apwAD19/kKQW9SAmJ+EHSmeIwXw==", "signatures": [{"sig": "MEYCIQChqFbtrMi1ESPV3Dm2fglkQS1u1P8YcwRvlHa71L9+MgIhAMiiq5zWqmFpriwkYC3OSMgxZpdwjknKUgTNu3nZa25V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOsCRA9TVsSAnZWagAApLkQAIybm19ILFdvL/K3psWg\njzezWqDKH7HH3V375uo1371VpwDpkZ7LTO2UZ2Ckb+eqA4NgIhX49sbfVouA\ny/GsG1Y9Tr3jTE7HC66VigaUDgPdNVGGpU9HWDhqrzketChbOYUiIznCI3C0\nUg0v48BjfX/jY0oB6P3hzTzLPoB2OLBfYa2kxHzM68rC9zHgdArAuit3d7PL\nY8jvfb9pCkpltawGeE0s3anP4n5yS9Cto7tf8exOII+bxTODB/HJ/HOXXWuR\nq9Q+q0hA/1rOs8hkj7VxjEPjIUwyjeH13f5Sjd1E23eLCswzEcOl93PbNXhl\nSAGb/5r30E8P7/AGJgsIVSqnCkKvizix5iuuaRPzcpKhcZidTeI7VGWADeq3\n6msLhSJiNmI2xB3zdUISy4zipHIS9qQ7Y6DrOqK8aT5w+k6kR6VdAwFDw5rE\nBYzr7LhOdopVWpIBgf9rE5M3A1gW8YbWrqQyulRnYaDdWxGnfiTyTRNASNqQ\nfbDgiKwE6tmaE/jAf9kHMmVLZfTHIH0JFtef13akOnRJ8JlGQwFl/gr3rG/c\nItmAmSONxOjyuDO5VNkg/oF0Vx8BfVXX9/nPDG0fjmxjhQaomUSfHp5v+OFG\n4jIfdDPM6t23smA2ZFFQ5hZmOfPnrO1AacvoZTDBrrujl74+6tObZnu4HB5g\nnbq7\r\n=b+M0\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-collection", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "25cb57f1a3612c58dc01b4ab6b35a36dc4e24a4f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-n3v+MaDUaVfSBZ+rzj+E8iMyvstWFYlnvhsk/FgjyHqz8LvuA8Gnx+E2n6B9FTiXWGnstP60hpZeAmEjrWiAhA==", "signatures": [{"sig": "MEQCIFU8A1c+vMs8Qzem3DkzB06xf6cPP8up3eLg5iNQT2LPAiABbkPDYtzHWB7jLoYRuhvOlOlNOO0PsWt13U1s4E1v2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gkCRA9TVsSAnZWagAAGSAP/iXFIaQfXJJ1N/EG5bRV\njkirPCVT1fcRVTgNk6OuUOqvk85hrA8kKTMiaxkJf3FwCCm4m+96k6XfKfO3\nuR/6drLAKLlAV/GTJijrcl94CF2oZjZIL1qI818jP+gAWZxW0J1hTBotnTj9\nWHv8Fe6qIsZk99t+V9GL+YTz72sq9O6YVGJAPs4FttJjWp39r4ktV3XiQBsY\nqYECFzdgCeWv3oAHgM7K/dHgVx1Bkfxm0QD9hIUnCoF0kAFxZKhXScNc3GHe\n5HGQngFMWGkF/zOjKP4002NL57ehY0cq/DBFoPKvS0nfmyqtLQgJp9vL1/EK\nMrXI7JanPQtLvAVQOfpRbsyn4Ig0o03A6uONJTCWxpqdO9aXlNS7muMsHe5H\nmpOCZsIs8xv+TA+RK2KfZ93kloqTv8KECsaFTqDCZaWs69OzTyaUeuBTwdp8\nBzwkYpJPQxCdbp8jonkNxko6lOXwEbbtKSAuFGO8eA0b5K36jWoeCB0Btx82\nWyQ9rjcD8SY3+mnqh6QhUmM73GkWNk+m1xE5fjtmQPcsoXJ4tIk//tBOM1MN\nF/C1Mr+cSZUP5oFIeUhP2G/j0quX0ikSy2orRxontAJ89O8ab8B7dftqT36F\nk5FK+/WBYdaBKEVqzrWBMzcTfL00rcO5c6WuRJ3sYEVMg3f9z69GkXpqXsZE\n/M72\r\n=RwCK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-collection", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-use-layout-effect": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7d24c92b0492a277d1feb58e8b0eac6d8554ceab", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-4PP+UAYOgC3ovCTKWWyhgAhLQKt5SFF9ca4OAofNyDBWXIWAGDQWD/hTHwozl4O/QAcbPsvFko4NchPRX48zWg==", "signatures": [{"sig": "MEYCIQC1NuMk8fz5qFW66B5z+Wsi1hQP+Js8uGhNqazDF/afBAIhAI5a61YLgSG203oLFyZQLnRsSghvC2ZpYJefR7415GdB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HrCRA9TVsSAnZWagAA4J0P/RYwS3j5S1S3qGF+htHT\nGwRiQd9Sm3gbqhZ5yvwYDHM1qS+QJsBwXOIsgBCk4oclkNhPundTMYuwUKSw\nYFVshkqOSF28UC/GqtNReSeVyNfwYHg7W/dew1vsvVYt2mZ0Pmxua+EoXfrb\njfuJNv5GNBhcMQtga5bBDHtdYHRhWtcZZsWBuO92Fg9X0n+18U6FPrXWwtK0\nnbX8OEHrqXrSMrCyaEPIU3ltQvrw2tfmQxBo9tmsEadAExN4IToeszWGu22h\nxx/hqjrqNAuRDYiRl8h+KqXDxsLOjx93wqijMyjKulDe5M8xOhUQG/pgfUDo\nDDUinSL/xJVJOJqwKB7uqAvPPhm6s4nM/3Wump69FZr7HBMhglmav+XD6our\nNuF3hpTV3HYAGOGIjmVehEdEBgEZWOk50Hqyww1vv7eNaefibHEKcUagK7V3\njvgaGd5h3MpqYIWipPuDqIGMXnTzCw9UOWKzxfBZX7S9Ee2gvSI65yweMSfy\nwni4CyLIGkjK4TW0UkEISaYsHTExcmwDUku/Ovfko3M8pFpYqL2bLVRusMUE\n43FRNXObCRU86Vd34HlhPnxlYaiSj4WvimClxYNp/oPzw5IM71jk3PYENpQi\nTMhM+aHl9c0QuFsdIpkeonjlNxlaACmERiR4HP7TW+7cDLJresHyjdXKYN+K\n3CFp\r\n=Y71I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-collection", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-use-layout-effect": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3902c0e4574d696bd64d8913d9e4303b30efcca2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-xFQHJ/LlYHseJwAPhF1mzRSKHkTVvvp240B7Spz4/+/A+QdeNqq054aLjo7anAEbIZP/MLyBn8FSaXD1cPZC9A==", "signatures": [{"sig": "MEUCIQDgwFaf/oDCvGe7Dp/Mgh41dAMbkm/raonaJLu6irBP5QIgIEO368qYeEAcR3pw09/3l2OIQ7lJbQrOXpj611Lzn30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vmCRA9TVsSAnZWagAApswP/23PjTp3qFhPaWUiv3j6\na4HhZWUss0+6KPh7c9aoJpo9x/KmdEEujABd5Oieb5y+gOJTRLWcJcRiM6me\nDdz2V703/OO9OYlL5oJxzJFelaMu65IyKjBH42W7xGA57hd+/Uk57mFziGvm\nlaSrcwfxNqMrfO8omJTHX8wcEwQTCYQOJJbuEEVcQVVOEFdlt8TCn5q8kLNI\nf2tkqQ0QqyFyMxnTGIS3YwOZH9mDppHogVgddRhvfkdqy1FGaudrgDkE6O8O\nU4vhO9irMSnlRwj0k47oV7z8GpAobwj0UbshOh9uD5gfF3FK3MTkyqotxNgW\nYuUREeMPVGUZ0egOOOIcwz0m+WVFJD9Rw2QeiGoqpQb66acmdOJk6yA46uhN\nEr7V9YTx1KlTPeV7uVseeQXLZ8DFsTtc9PKeqXRetMqEQW8YOi7AmuOolhBp\nkG0LZA/CzD6Q24rmFbcNi9c9owP4AuRD6m8aentVvOJXUMk+ybrSOAoiUVsR\nHjnakvSGojGjUj8j62AnMqoEsAVrlMAErfTVQk7asespMk2aamnBfVsyrRve\nMsisxoP6xqXo2EoVaSsiHM9a6zb2v7ftFx7CwXO94Fge/v/y06tsq7KAJTC8\nYtkfilJs1LI74Gdm3wHFkGN6+9vrw0H6eTR5DZj/iiA7kpa9FKqLvEzDhDtV\n9DsD\r\n=1tXt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-collection", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5d107cdf40e9d03a8caf2327f614a3c5aedd43c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-eu4rsgwFeYpkWaOeGLlCWjZpMTKuQGlXj1GHyVN2ZQqePLaSQrpsmdGu9cOecED1P4Si5fCp4UXg2QRqTJb0ug==", "signatures": [{"sig": "MEUCIQDPTND16K4NFiQ5p5jBbSiGnnK1kbYCjNyBxG0jpcFa0AIgUxZHBDVD0EJyQWIqUp3ww11xOH+TPa+nuARwj3LIxdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmbCRA9TVsSAnZWagAACz0P/2qRb874cYqssSxskyCG\nWKFmk+Yw1WZP1i/FyfMJ8WmB33kDoCJFBaMnnfQ5Il6qEJbfopmv/o/JiCpu\nmKOrcGlYxsYiSj4cQYw5oOXceJxB6cgZjTD5BYAaRZG8vMdOdp7PmJwsJoLA\nEQuLP/QGnID6rsQ3/S/1l0JbfuIvbwQeA4ngNlzDWXmJYxLRlSqY140aXN+o\nyzzsDXoXD91G3hq+hVxK1glDU7UYbXLRQLqMNJEIuo1svaZGSUZD8XVVrjJv\nvDzSDn1HF0BjsTdZTiDLluPHmfxoFYo/hzPNBfgmx4EO1Z3GpsUUrcICUNQh\nbK9BmV1PxkCJoUE2A0IGKVl4vcYZVUs79/seFSJEk6TDECAs3gXQgpBM12W7\nXCKq1TfYPGsctkLBhfIWOvQkQfbGX+CKIWyUB7u+BAThJjysJrqheEIOrRLh\nKOROA5PWbr+ADVvNJelCiUPbPXnfbYn3uUqs5EUIxamjszhvcx4Rizgyk2Ei\nr6mUsWFjALOlwK6Eleb1HB6YRbkRJxqHT84qhDPFXgn/o3PU6XeRfqelkuAb\nIriE+ZrXo6tyR8rHFzozj16ZnrRUlplf6Ud/8Fnlq7nBttfW41iamYfLvJED\nP0A7mBYL5aT/NvQzi+pfhl3LxwBh5nhwgfxen9mfSdpmCOaRD2fkskSi404F\nuJrb\r\n=Y+dt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-collection", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5cd09312cdec34fdbbe1d31affaba69eb768e342", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-jUP99/wCHpXm7ytbmpcjhhxFHBsr2lptEzKO8DUkD2CrJBS4Q3XHMKBDOvNQQzIqJhsz0A5JP6Fo0Vgd8Ld3FQ==", "signatures": [{"sig": "MEUCIQCQ5PLUtw+3kBl3PFnjMQnz9OcXAXFj1Maaeuew6W2GzwIgK41mipluE+eY4LpXOLTUrle7lRnZ4HckW1H+AkZXLYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7LCRA9TVsSAnZWagAAku0P/3SpYlDRoILLG7ugUsd6\nuI8CkuFBVjGmDfi9cBB7tdJBpsgcT1l5PfP8xNTAk7dThPSk+SW+G4Xe9m80\nMxeqSssQ74KOmpWC0smKFKNfsS4/Qn224RBIvkme9Z0udZw2aWaYbInui/H+\n/+kjYZtzOOanuLjpBDlNaeSuXTZU5whmfkL0fzwAJ86y505qFlIQQM+aQB8x\nOPUZfYmA5I9dWjkoABIebCUhWDEZhiC5htgkAzjgUiAOe1B5DpFjjNGpR9cu\nGnZ/Mp5YFcjSALpF3uRIPZhnoS15RO8X/lCAhW713nXHbnsssVTo3Ys0QJ4b\n+p90BV8vfIJW/2flfegK4FjbSvoLwwsS7tMRHLCn47DuEoGBKKJalYLCNO6i\nbFFeWdzys81lVvVRPwE90QfsTTUMS9tMPiMALKPVVmweYY2HPcxNop4ppnUT\nFh9VxemibSH2DCvocwUn+3ECCZEyO4fB7utraxbiWOVX4Ptwj3peXpB5PhEP\nRksfDpLQGOAdaMs1O8DIk8OiqJ7zvl5HFM4Mzftv0K1k0FdXetCTqhlr76ea\n13UJu0/vQCmszt3iXQLKJDG3CPMIks45vo/fY1z4mYaWjTxthS/ugGEwWPvW\n6FZ/URoM46XYi7w8JECG3SSdsUXue5q1szu+7S9tsDsQf9Lz4TcSesuvPe8j\nm3nP\r\n=KJKs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-collection", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "050fc9b1d84b58ab04a5852812b181767dde2e85", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-TKUUFLeRlxp+1S1FQz9pQkP1VbRwZGOEH8oUdgWjUsNc+GVURlS3H5twsDtSAAfvebOc6YmDTE23mvvhfsPhnw==", "signatures": [{"sig": "MEUCIDjN+kYlTHzs1vI9hCNkIM+6bN/d2GTz5WtUhFInp93/AiEAqrkd0zSPj+Rt73WXLTb1B1St5pJY5jjZmagkyONRJjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9mCRA9TVsSAnZWagAAetEQAJzjKTot363YEvBUhvLP\ns2M5OewUtY/5NrYIsuVUQbcqdzlBdEQAcug9HyuYV/Jjq0lKVYqflLeEdIgH\nFAirkgY+z7iTyyIGLXbwZ06zdFqSyssP9lIPw9oygrYNw/SxDRz0TonikIxD\nMyjeClEMVKmiDAkFDu9WlbP7kr8BmX5SN8kgVaXyCG4h8buUAjAvFQzASzCl\nTOMW+2D05WpBv/HcHUBYXH+UfbjnLPJJqGHQOOEMRMrb9qhFykXj5vIAQtGv\ny8/UeEJViNITvdnG7kZjKO37VLUzK8Q3yP9kR9Q5AFxoMrIGGTwlcJ8rBinf\nI7pvTnMKIBmf1VtMdrCSi6NwhRfOHSrlnbMvPv7Cqs/uEa8DmHqKOZ0Pramg\n85JQushVn+s+UEEP/IpHcPBAYmxDSdJ6mAY51VchRLSgSS975lHdgfxcDdMs\nAzvviwHFDwcchhVudJj29T8DLMYMTROFaA6I2AoHuj9GIWWHkRkqvEWKoRq/\nfA36pn+4oPMF2NZK7yUh2in4Vq0fwrXGt5Tcky5SqdV08mByaqMIekJYwYxY\nvasa2AXGWTSl8P3f0HEcAnhBhaHD5OfeQHhHarnz0mXQjNBfkNeQc1C/MqZN\nVuHLFU+iRyYy7+1rDSgy/KpLnQRfqQPxZR172u3/QXml7sYOadEGQn9wPKn3\ncvBQ\r\n=q+nM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-collection", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c51c790100407e8eb38be2b67538a5ff7c52915e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-IM3ttcjArKWItPY7vaCHyqWFSGK+k4Lb0V7RC8vCgB0LXEep5YQ1d30o4TNAqiRX3+Mz7T0zg+gDCwK86visdg==", "signatures": [{"sig": "MEYCIQDa1Bdj2crpso6+lS90TjSatB4PpdGHtQPOOh23VeWSaQIhALsYKhq1SDL6DK6OXVwQeZOHMpUtwOY0XGRdRdcvZvfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GUxCRA9TVsSAnZWagAAd9sP/jLXMJA8cxVymK3Ja466\nDccWr+tN7EjX7QOj94HVMXp5vcHzNeFt66MlBDuLO5jIvt8JL2DETDr8LlQ7\nHalZO+g9kYD5nfYkaI7iZGIqEQIWDIhi8z2zvU84aX7e66wgAeJ2npRf3bPq\nkRT5Ohy2ebfUt3hNKID2wIGxjF5I7UkDlFHNlnoIf6zaLAgk4Xxsdhl2ZHWQ\nrsuUStHRpNXncKVWsHNkCzS2Gz66Aqknj9aU8hvNP5jTM0kDwJLhaZ3i7jTS\n37+FCiYFtbw7i4BiXlC7Jmw9jlGwDw4z2yyms8BP1+u7Hcce3DuzMQKzznFE\n+mgDybcyAkGMjGB93SHnp/254woQGewbHXT7407Hke7dMAlnfL9vT4hvG9cZ\nTz1ONJPFlHdmBZE5b/uIdu3V8hnuGCeLYP+D8mgL8G42wfJ/QKKR5p50TyMr\nOK0nyVx2JNa0k1KEE+5yaQurFW95jynd+i44SsKMAfl/6IC2a+A0sCwkE2oc\n4KGonoc1YIbnj7oweda0z+AIlz+pAR8y9EDMNCOWXzENjipT8rjkhR41quZ8\nznmVbvgYitnJwYdaOkYnNphWv1PtIChrQQFQe/tj8sFjxfY0q45SQikcSQgv\n/cOG7utpKSh/WNqOgc8uiOV1xHBpMzBOOvC2K3LC/cvv0a8lFYO3joy5sx9C\nNzPC\r\n=R26j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-collection", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1b2ef5d5c0361ad28fd168917e567e4d0f845c43", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-h82YPqKxIfrXpd8WJCdfgl1c8u2kj+Mr9syNwjcYcXv6DulkT8op771q0ry3+CcL/4cOOyR4ULdfuvMODTsUeg==", "signatures": [{"sig": "MEYCIQDqtGcV+x60bw01futBhMPRYHG26uv9c3FL7enxX2BReAIhANBw9oA09ccrA5p5vPAfpgjnPs/NRmYX7wWR8YArSHIq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnToCRA9TVsSAnZWagAASusQAJM2L3eX0Sj6zXi/qgod\nH7Gy7x4OPnci/OXr5SIKO7kBcRiUc4O0hMHFbhZ54M+eXj7RzSkeJNgUP+YV\nm71vtyXghx386WJGWbpLV6k2uBFrWRAHHj0G9A3saQKHYJIid6CloceNzLyS\no8/xzNN7VJGiM4YZ7FYHb/bFWIQ4jporHm065kxdPs9bAvNG8N4rdsXirc4V\n1XFk7Ehg61fxNU+JTdb/LjmMY4pZt5FJ4d9pQdP6cwwee2+uubTCmt1xhu+S\n/5dJVkkpb7zn/AcGIzImn6CyuAaHjwPW6/WFd1s5cFanEKo1WbGbWY1ATxqK\nuV9BfUXKmlyNG4FdI3MMfwfy5YeABdy0+Gvow400YCJ3RPMUPicWIl9FXk0V\nSAzSLaqBC3GJFU75eV9UNECagf2WkwDQkHRx16lUguKHAEPn7vQd9exqkNqa\ny3jnXdPR7s2iPr4GnMNO4PLuQbuMDL9g5iZDUPgeh8MjBjfDJFWAdIXMK3Ep\ny+TPTz111JmOvpeXJZ5sFigQWGjbNcBcmGIMAuyfgOgt2tKAk7Dqx4pi17EH\ntQ7SpulKtWA8QiP351IV4IDTg5mcqiL/Et1I+8vSY1lRgRmOa3Ikt2zmkXvr\nA4M5LCxCpyFeJX1uYClpr2c3XvNKSowPveX87kazOGjceOGrdXrm6HrCJ1NE\nPjyn\r\n=euBo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "55e971e004ff76ffda57a9f23ef1bf214c3b4155", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-oWiEKC7/52V0OgYkmzMq8afYRbHIj3HBRQR3hWsy3OEJ778dpJuU/HBqxy58BTkE0DPLc3tnIafTt6VnxgNYZg==", "signatures": [{"sig": "MEUCIDrqUHKUtVE2XXsxYS/XkSpvNDbHKm2rj5rmIUUbrJOsAiEA/M/YARqTdI/O4OO3zDqxG7FkxL8ok/rQPA653IQPVQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpUCRA9TVsSAnZWagAAxT4P/1Mei6SQ1ayHtiT1O0S2\n55tYQ9IiJ/Z8877895R8E9cGBtiAHXMuhxTPM//59Ks8I9bFitPE77aRjvOx\nIM3prYFxvzMEvgoO7Axr8N8e28eItFzvTxp0z5h1UB8RM9NQphKGC7lXCMlb\ni3FlqU8JjScykGCxZuFiOyv/N/JOBT+0nj4TSw6nV7UttHHdVgGFOE2jnh9o\ngTKauQnpdGlatSBAcVVqdsimuI3uElrv5Xf2OHzvfqeVNJG2KXr5LcEXBuTq\ntP89g1tMiHJP7gWkOI16oB/x2Xp1rAozExZRJ4uke9o2jm5GdEUXgqM6t+C6\nLE5FuuYnXHgZQAYnQjdsXNj4Zi2PSFdMaM43ftnDjhRcaoBQoYG0KgvHEhBM\nAeqIA0lInFUX0XkHrLOkLICiWDKvFSSqTFn7fHU/TxchSoIFlGAORBs1E57q\n1WrSfkjzmbhr26Z94ZAe8azugwKeNdFAsY9I9+NAG3P84cc750Sw2C5xrL2R\nwv7dqOk17scQoXdwEhTPYpjjjz36c5WKc5FaeexzwphfKk1EXHTk+flP2vOE\nuEyAywuT+HS7MWFPaEQKjlovRrvvokiShwUdp10oupqcfsy8pQD3nPN1Z5Kc\nUsAUHV5Wy3lekx37b7MiP/SZ/sxgozFgjnfFQJoM9y41TZo2P3CtxWDZQluC\nTJRt\r\n=u3el\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-collection", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eccc0be77d347568d1cabe0928943ac618fa7d35", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-8vKRXqrQ1vY+A01bCgV4t1/WBmapchm3vbvxQ5jfJWo/WkroV5MUwDGAlmx0GbYGv5HwOrvOy6OZmsvduaQfYw==", "signatures": [{"sig": "MEYCIQDW3ctEFktGGd/Pu7T3M6vsqY2zISnRZhqKg3FEWbnjgwIhALiivXwJVQ3aKefCwTX8tAPaqgROXML0OOU5hh5ecn1z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17791, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyMCRA9TVsSAnZWagAA+ZUP+wW7cWfy883xcK2bHv6e\nuuebLwpkPk0Nte//V0o1ESBSmze40nLewEfF2wVgoVc5BKzjIgWAyJWV1msS\ndsA2qJGtahmQKB6f/OsJvzZd7/fCJ4Rye3kWH8pAkbqxNqKmWlKXmFo524eT\nlEihNRplLLsdiQ0lZlldLzfahaIXfD5VQqtbAyUnu82pZfy/xXuok4Gyy/Om\nNALJl0d7i4xl0orQ8qFvijkU6esVjXO2EQzQ4r9o0Ga2EyT+rhJ0rI8RnjSD\nSB4UKuze9DgZxjU71Adre58HJK1jE1AQobPoETi5AgIQcELV/NP3x8XGwB/y\nRRBkZRa5v9AC87rilYeAE6bAMeXi0nC+vL+q2qP3gkR+ASAIEpCWzhTXvTXq\nAXDN3qOfvhmo9IeYK5mrGmUpJvvDvCjNq6B0L32KjeGjWW0fgTkAGDfAR9Y4\nbKWgtPth8zxwgmxoRiMJl4RYexORFCWZkFgCE+WixziLoEBH3Chne3Z/02Hq\nfoQBT/q+tN2iKJmah1T4QBgyn25L26PHK6KtnTy4JL2bBILEur/sk4rVC0sP\nKcwpueVJWC1755jo2jepvOPJvfQkG6g70n4hHbMRGCmSSt94NXgmQFpe0hUf\ngV2ldXPK8P4uw19Wzh3esWN5onmNfQPYuVWLSECb78EvGQq/TO6VaR2BsEyq\nslzs\r\n=wTw6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-collection", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0a82b4df4987e10c6ba0a9c4c312dbccd345b7ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-7rA4o4Y4GSULTqVrUwKOO/9Ih2EsrDKFWz7IYQ2hWot0OEoxP6qJXFNxSDyVKnfvpQutFKPZXoKsh1VmtjQFFA==", "signatures": [{"sig": "MEQCIGioZQpgWGv5fGkwqaxHBjXlkmJdN02Hp6I/0Ef4ckB3AiAZIbXfHnZIpW/LYot7A2rDpqKz76XmwrYD6uQIFSag6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmYCRA9TVsSAnZWagAAp8gP/0c6rAi3zjK7GLb8p81F\nbmnbH/XYph0F8JmZAFASpNXaS2xkIf2Ywzowkq3NCaXff99926fYNS0S7ohd\nYTrDvnzmoU8gnAvqZBUNcMTr0kOgb8poZr/U6CRY6wY+HNQYQHt/b76KCBXH\nekCYkH5pF4h48LY/KiOkN/1VQhwKiBzCJ63MKqBsoE64IyiwC0HDSb9weeKh\nllykdByRLhTR8sR0nbz0MeHhYveZjJYs/M9phmCiWRSFb2mnBcB6PYmM02/j\nCpZWOAKkyj6Ig03wuGZfyLgx337kqbpd5B1hrcByHjnRADz88hwFUskiM3To\n2yBLKS1Rsh1gycnWpyCs32LUzlP8/jhCKYLHKrogjjECNePUa9V6FrZAWFBx\nwzl8zT6+j9xHgq60YgIDrDS2DxSH6KV4qPCDHmc+wBUh8uDvcenZmDNUFOIC\n/wR9mLJ6N/1kUo1vl5SXAztYLzGOU2GIw1TkPk1XBJVkg/M/yIAe86lSFqpH\nZzTWbcPnq9nXlz9N6k4Sr/XYoaQHpucS1UMeBdFUoSDBk7mgHGnkhCPzk0Gq\ntQmZ73If7Fncmj26QGGk/QaOrITxMlhCDdaANu75PIlUsbwGxWqoJxPlyb2x\nvWMs/nLaoZ9+l7PisBsrwnX6PSaEsK3ENcgk51NRDovVGk9x7lZldUiURin/\n0S/c\r\n=KP2+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5405284094b222a9a3d92d7fd151b88d3d194944", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-733jA2VfFPoM1jXdaW1fUe0eIKTIsIfJAk6s2l0676lfCiOteYziw6rlJV/Jfl2CMgpgCaN7nUmeWcWYZmNabw==", "signatures": [{"sig": "MEYCIQDj6KhsIQvq+TfSxbNym7mrvj/2AF+K39lmaBilcTY0DQIhAN8QLrhay5FvWfe0I5Ic3osc0AW4o8qO+mWgyLUnW+Xi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImcCRA9TVsSAnZWagAAnA8P/jrWvt5x9CJ7jSZW7Y9L\nDY17sutzC6Jiyu9hlLAvSs9r18QZBioE5RQcw1+IVvT+pKPRHe1XfAFmWelj\niGXbNdAGtxaMSO0aZb04Zc8eLoIg5lNafs3Et1Hgm4l0rWIWJXWIcaOv+EnW\nMnUuxwA4ggIIC0tsKRc1gdmiA0ILTk8MKMw9TxLNutxKSQCuY6xBRafBtjCK\n4jRm5IC4eni0uv6OwIrVfM8LYw4VuzYInqoztC+BrF96nlE68lqaqi2Ea8QF\nkhtyIcuRwBonNtzM4wv5t85Rf5EKUKAHpuN9Z7NyDnGoFYzW5JlAuKuAjiUI\ntaBYzkJeeYrqS/FD7Aj4hQ5HmJo+zR7raN1nw0iKnodIBDp92TRW1VfD+f7F\n4NZ/jpIBisLGTY+GeGtCffsy2g0i+/zFSOSHurLH+QyRfShq9v/3EuRZILee\n4FY5cYcCaokFS6KsYlZiwrMsc//DEmpRp/ZdHv9vN+gP8+zYFwEJogkEmgFc\nSv4jzAYfwsnaqR83pmoxwWeZBJ/3qiHq9DZ/ynYX4dOA/3CUkofCXum1ZSsu\n2hDsPxDKCl6KTawyBFN1BWI0xfanFpN4Em/y28SBGNNLmRA09hnE2zNCWQLz\nRqLp9jd/lMZ7bSCeV0mXygzUJH6pjDzhUHQYeMIT5jeNY2o9Lf0IdgCzB6Tg\nrFvT\r\n=N3bn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "07edaa19a4eb96b8d3bcf14c05db3f9f0c076725", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-4CFy8rd7r5b35xO7dVmGziwdvh/ItOAMDasbEVzOkFOSAfGQLG0KaTw5vIb71m+Bwx8uCM6/XQrfESg7yN4ekA==", "signatures": [{"sig": "MEYCIQCphtgT2qg0LLheWtrxUBDutCw3Npvcp8Fmcdjb/Q77wQIhAL5Adu61MhhBpJWFAw7EysVJnRimsNdiXuAlu1lb1h9v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdvLCRA9TVsSAnZWagAAiBUP/jy0s2pe4MKjpTaFfTCA\nj99xDjR0s7J9kNN9WEhGeW7n05TGnJjSPFTOs3e6alDhcHict8KHvs4tq3RY\n12wtRBXMEa/uvI9pQkecvyysO+amZ4Tz1p3Lthdo8QkZ4q7Jjkxp3HaBWeHt\ncxFavkTK1oJ/yrxjWojK/Okkx4R56itIlRvcfUXwWmpd8Je+2MMxB11yIXGr\nc3649EIgBKzARNCURsRE0Zx/kbifzfHTF8DpBdlf4AawR3AeWKDqjcY/uW3E\n9dg2FYDxACFVwidqqSv0aSxjD01YSQdJTgm6kCvdIaa9R5Q2+OxYppYk2pyC\nUjy5y4CB0NUbUC3WDEsPeTC9xNH8OCmHEDGQXAdxqMQxRTFbZKPTPehGfWeD\n5ze1ilA7RxoSnJC6MHed6cr4/2ZpSXFlBwqW6qH/51iZU0jx47SCDbGHfsS1\nsmtzAdFXgDhI0neMoa1wNkp2p2LIDoJ/KKCiUVsUXirhg2FfSJ21hA7yv+jE\nNtKfWdngFanEjUeeJOjgmqfT8+Xe60cUVi6EHTHeayRbmaIS8g07WJickI1L\nO2KS6DnWcy6O48t7s6IApdFa8sZYJlcy5J+23T5Vcgb3+DZrJxkLMEIMo7g0\npzWXshn4pVJhTDnXAbvfhgSMQ0xgpX2/2hXsImXZPjKUt45AKjDL/3zPzO91\n9y4z\r\n=+/+v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0b9cc720c3b0a80717beda8139d2bea52342a089", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-vaoMk9+s/UPuEdu6lmJZQE7YiiRVFfVCYs15K5jqqhkUp6tpk6y2UWHXigTdq9qAG9t94M33+0EsMzRl663r8w==", "signatures": [{"sig": "MEYCIQDsF1V9/+yAkTZ8UYRYaycYMgqZX4PowyejwCYEdKjd0gIhAIN4s27s6dXcf3qmkxWzAgFMS3aPKaGTmxeOCVBew3Lu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TUCRA9TVsSAnZWagAAAAwP/jjzziouKJl98OPPZ6JY\npgrHaTtgVqdaetyUYlT9uelSOIZdqsST70vgoNV/zigcfoU248sSeqG9BPRg\nKesDwVMAhpZR/9Ww5AW3IVBgY2trEQCtucBhHaspwtJ2tHRWtlH/bo/AJ6dR\nYQ44IEZaLvrkZb+IcJ7FPkdshwqzYKWL75zH9hNgF2g9wJHGHsHtbN9e4Z5J\nlKFTtFI4cEObbNR9PMNctmofwznnXYFZd8fGEgxIyE/ONPfqV4nEcjhbazda\nvmBMWL2gA331iSgdRiOoeyOld77h3rmVOHZphb+L2Zfe5/U6O3/icxer8Vti\nGo2e+/NvBfmFSOKe7QHULnXASdl1kkN9IpHXueRx+DW7dbYx10L/KzZ/AfrT\nox2MyCFq4Hk8E8NWjz+5+NvgaQeTEMYcEmLfr8I+el9NS57wJOGHDgKGuXS8\ntyEoDFG2Q/CC+sQNQ3em3H0H6/Cl8bjMxJwHbrCzcr/o4/Enm8gx2+zCg10U\ntURz5D+T7kmWlDuPP25REhD1RjSz/1EbYhPn3E7RsG7FeiI+KfrEeW4ljyt8\nqWGxfBj/p6L/RYbeVwRrRSN2etb2rvH7iCuEm6TCJOWvhoFdecdFz2+Hu9nH\nrzpzwC8jBCw7oA0Ip/AKNqrterewXNMsV1Nxd10M8DtdMTwzWuNivA5/BCh7\neRgS\r\n=jvNF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c65f26f015b11674dc6d1617b87909fc9d98b8da", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-eMiSqW8jYtPHbx52w5d2BaEImMsBD9Mj7C/Wn4Xg6eT83ZGkm79Dx1Ryj5igJHN21ZbVuATOMLGdJPyx15udzw==", "signatures": [{"sig": "MEUCIBWbK0Z9rAoJqLoCxWCnkNM27sIRF9qnibv4mMfRLVSzAiEA0iicMpBwXceetqGbbXRuOBLxzkQUYvuYmn15l3JeYUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1y+CRA9TVsSAnZWagAAQ5EP/2Kki1CBeotWYJs65Fao\ntNLJxcBmK4AUpD/VwL4qh+WB2NxLoWKTeLXhIqfm7GbPoUA59Mb9S/IumsAK\nSxpWtdHhmIjXjcrTDe+7oejHZ6ikdqslXawGlCKoxWTFYohuAx3HGgLFPkgo\ne8YhtD+gWsmSQQfvNMFzkXMX9gXAcgQyzzKlGoL/NMPeoTpy6r2rKFcUm6nF\nDif/9ASm7OZHYcYy/BlI6TRmJrGuvRspkgaKn4yZHTlFEM4Dv63CJfYRrToy\nEKNUHaTlWME9yx8zXW3AL3ZXIK4RuPwBsw3bsja+abUKCwMG2hI+2+tcUNzN\n8sMliPNr9xmQxM0Jk49D/NMNP1rEB//QQUqIClqBISnKebLgv74uEQLzNLa0\nZX/5imWoOIXi1FGqaHH64akNt3RwZjrrnL/iXDw00+0wyAz3yuGgPOeb3Pe8\nnSSK8UGg2QYuZRK/T0FBSLAL4RSBpbsTThMeFIQy70FhrLK6kGAsHBBjjqXV\nIoUD2YZl5X5IuTTZ589VFb9uFSY2A+SDLvygQ/u6rX30NtTJq8yF184DAbFh\nSRvWQMFr3KY6URpVdCr0teD7zE85gO74zHh/tZDIvcTYMipnJBDua9RmtW0o\nmpV5OJHP2OTRGbta64R3AGV9AKgphAMUzFrKAhzuImuE9NN+iqX5CyItk/YL\nXibr\r\n=wdtN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bf43cc141997601140dc3b2eee5b1e009fa8b13d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ooxD17gKeNUA2B3bn7JdezSGrxU0IfUCeRb5H2wMFlIpMGntVnvOL3aILnb5l9sI5KyBlZwamSq0u/Y2VQrksw==", "signatures": [{"sig": "MEUCIG0opTNFWjn54MoF0PB7/yBxHWUYW8HkMPUmiwOgl8eJAiEAsJCvyWa6iVY4wtIJOgUF/lE4kkYL+rumDH/kjSDN5XQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFiDCRA9TVsSAnZWagAARQ0P/3/NifMsdc4j8PXzn0/2\nai1SQR01THMbk9cjyOOawpgqzxa1R31kBGxNr6g5PFBsLIvOiUvL5cDTcUFm\nR8/rrKxfFSUT0D2w8kLY8ClDyxhwHufjq1cUC5raFlReOdhrC+7YsYI+WAJF\npjiANLkfg6KYP2E0EpiUDa39/pbg2QziNdFrxec/FJHr0qQPE2V3hhoSCjXl\n5/uHAl/BAuNBS08hh2pldiVyCDIs1A9clpx+I+dIYMSMWmN7/pAOdKNCMok7\n0lgYa08fkE5Lfto0dAtG7YaiebFdhYQ7wtNzWOHRvYUzK/7lcewxz/WgWqP0\nhtaTm9KeyXdm3LG+kUGGVlGfSPSMmVdtTbwlDFvehfPYtByNeQIUGMnBWKZd\nLcMKL7prw0xiJ3+kt669yiFtttDp29YCphciQm2BbyKsBzd5AfkJLlprWDIJ\nq8C7GDmTuA+mF2xDuNZHwU1HnSzKpURudH8gjHVNDzNHKufrSusqqrBf5n04\nYvNWv99l5xhtVVik3vGYwd/pfpe+7KvZls4yWuZ5VuS64n1zRjI1nUdDiVO+\naf7j04nm5gsMmflhwSCP20MaPsukGEppwEzF9uooUeNqJLbAgua8IAU82vMe\n8C2rsvI/Pb1EwVWuQ/2weUEr69CGbtNY6db30YxdJgJamgnDtlbFoDLyWCQS\n07BR\r\n=X67C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4f9ff5c1e51350eba7207a394ee92bd9c6bac366", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-bO0MWJvMRqDF76H833ZSVZS5Dtri190G20F5Y0ggCophBx8YN7a/8eq5gOlUvSIqGhzr7/0ZaXJT1UHa9x2Nag==", "signatures": [{"sig": "MEUCIQCVbN5zwCfXA/sw+k8LbdJx29I9Mfq7THNQpXsKXzqNTwIgQrije4o1s6GrU9aHuOXH26QW1bHmet2Ci/BbrgJfF8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17785}}, "0.1.1-rc.7": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b9fa7f6ceb0423e5037b5c8b000c9098d66607a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-pyLPxtNp8FVyS3YQ8SryzHSO8FrKU3FTqe+S8SrsYld+Hhg3OzD223J9A8nQVa227ftEzwX274VzWnO3iloLLg==", "signatures": [{"sig": "MEUCIQDRXhc+0Y3qTBkD0L8f4xAhRZcpoOcOQNYxQAP1yNm8PwIgJHgwUlR19JdLGSrEasHLRBdFonHVgws0ulg0a3SlXF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17785}}, "0.1.1-rc.8": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0ab781fa96e981566effbb52b5ce30e596991b83", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-xVa6Z6lsvk49jvMsKWoUVCVlw03s17fvOMXBhbAk6/JP51lXBNb12X03yQ2tG4QP+xuDClKlicMhMmjQHs+yTQ==", "signatures": [{"sig": "MEYCIQD8+AG6HqFNNpPjyG1FgaHThYpUO9f0kPOhHabUApfagAIhAPqFE9Z5WmU/F0KamIQDcJmSBeqaUYBAFULdf+w/Bgvi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17785}}, "0.1.1-rc.9": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e45ac5e6816ba2242f518dbf5d313b74db7384a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-7wuqSl+7DdiNoZaQ4xgGiC3pXxT7UMQSK1NAZj63Y1HwZH1ZCWmk8VRcnAXHYMBkxKRK8bNxUJdTauR1uKpO6Q==", "signatures": [{"sig": "MEYCIQClu1msFQsqa1ULdGHdueuYnTrEcJ9I+6R9dOtOv1yKLwIhAMs1bS6Jf0/HMnkBmDgWGpRdto5uMBujrRa1+Ix4BiSX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17785}}, "0.1.1-rc.10": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "60ee0d7f8949f53c9fcbb1b683f6927b63781424", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-ekrZdIE/lSbqkk+3qItoSvbDcSO7RXuSPhp9RzGEpe+AzCxwdNIyu1OwqG9vHekRWvxe+a17gGc/HqGCs6Ja1g==", "signatures": [{"sig": "MEYCIQD4XENX/T2me7VEt3Yg8NwVkp6aGFdVfZBtvR7w47IgJQIhAMQV/eo7Xf2v9qelWyK3pxXU2uPSPRkXR996Age+dkk+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18331}}, "0.1.1-rc.11": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "602303ff3f69abcc12f513daf5fac509bcb47a16", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ziotw56P6eTg7LNkBPdSpLWHhum7goMpCLQFRodrhlOLPhhTub0AXbuio3sMVjp4h//slBb0yhPPl801BaulFQ==", "signatures": [{"sig": "MEUCIAKxYbU/AxiyIeagRs8CA7uNU5k1yIwfoA6pUNcLpwfNAiEA0WQJWvlKz3DK5DBU44UQ7ujNfAMvnzyRaEmGpOGg3tw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18331}}, "0.1.1-rc.12": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1efd9357ad5749320cf2594618c55e4c3c22e24b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-djyl/o98vg3jevzHXMmDmN4hiJw5+DhT0lVgiG+u3bHV6laH1UxU9Z9hBftd221O3UDGCuFUOUfo/k4brCjlOg==", "signatures": [{"sig": "MEYCIQDpBAxsGk47xTRHf5SNAgPlcPVKteKI40F4tJdJ/1ACAgIhAJegfhcWcixbKN6+6Gdu7ZTiZXNSSAN90IgHAS+xxZ3Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18331}}, "0.1.1-rc.13": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cc180c9769f1434f06b7bd2ce45e62ea7207e2bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-m+7zFsIoxZSWXNxh3LOmZLQ2+LtLvm4hKQlSc3UtRWx+5/5JlKuab0KkjVzM80ShkyqXTFhrWRbrJSlzhmICqQ==", "signatures": [{"sig": "MEUCIA/wWEVvjxqbV/eqcPfMQ6wttvcXRikSkn/+VSsiXPHXAiEAxf+GQGp44xjKGIz7+neYBUwNlw6tqyypjPFdzdt5jBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18331}}, "0.1.1-rc.14": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0a3408cfc8fad39abd1e760fff3cd08a2c12835f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-gY/uUXPiB8caSxqtgpkNIN7o+JFx5iON9uj1t++mjqaJ/t58wMtuuhzjg35ec88R7zDrZlv8qFUM9LQroHpxSg==", "signatures": [{"sig": "MEYCIQCRC0NilwzGItqW9/TOR9asU1w54Xav8vp9V9Z19BzGSAIhANjG+ddV0lhmDSUw7cGJQWocKNRCmeNI9FJEhZcgJ5lB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18332}}, "0.1.1-rc.15": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0478c32f6617e1ceeb5457434da7144e4771be93", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-YGX0xsAZdEc6RuKQJn84HZmtj2lb38XRXZN6/RWQY3MzqAteSy3r2LBLaBER+fC2KixzdoZ/tGJsim0JRqIhYw==", "signatures": [{"sig": "MEQCIFej0amSasxiw2w/8rzMmXETMdiBywoY5b5KEFodMavIAiBA9MRjrgwgcGwcYlrRkOgnzgjfJ7kZUBKU+kzM7oNNVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18332}}, "0.1.1-rc.16": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a2aec105c9c40cf0dfa22b3814f97897f1a19218", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-/kSsPjU2eKt+G7MZMfMprS3aC8DGArLAnLUo2w7ztmIMc4XzbmOq+uFVu2ZL80ryQ/atPPY0HanJMdh45v6+bg==", "signatures": [{"sig": "MEUCIQDlI+fqANGzTb8JdXheIraGIFQFvbQRroiF2qcfGW2T4QIgEERT8b8kZ4oqn1AsiwQqAx9FZ1DVS6i4R2wyXCDXrCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18332}}, "0.1.1-rc.17": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b6f39dacbe350a51f5f9df8347007deff881a62f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-WKLtp/kV0oslQOIy5uBFWBvK03bKBtEEbUyYuBCO150wbyn7anH0t3WVzP1ECzFEKwft3xqTz8r/njTQl2i2fQ==", "signatures": [{"sig": "MEYCIQCqHSTV8NKN0wk1mSUQwZysb9XaDKobb7NvhPT1pAUbYAIhAOp/JIXUig1KnBNz9R6gtPFiUKvWlyV/oO1aomHwxeXP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20764}}, "0.1.1-rc.18": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9e5b96eb1316472ad45a6477fa29266e42531a5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-YNtPsVJir0N+vj+X2OLH2YxdZnfuxNd0Q5fewU7na80kYSEyJn0v3fNPSOXxzdwVbwR49SX6BEUWXNv4aWL16w==", "signatures": [{"sig": "MEQCIH8WlCcQS3jCcVx9Dc9CKYZQIkzCkBlycr/Mj0d6znwMAiAU0Ajr5TLB2ycB3LieoKpbyT2AWQwYqVvycpwk/CNP6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20764}}, "0.1.1-rc.19": {"name": "@radix-ui/react-collection", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "14dd40be2817df4dda78f864f31102961a3b8d75", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-o5QjWd+n/cl0C/XNlyEUL6HsnyC4+1tlOhb5HuKBnLksZHmg3sT4nuXVZUCCONaPYpZh8vBBc5V+R82GZODZrA==", "signatures": [{"sig": "MEYCIQD6AnvzUJdxHy/ayrK9hPSaq+QaOIEx8yxE0aVZyPv0BgIhAJjXQ7a17WncALmEezivDDyhGaG0huS7C4pcp0Flt/YM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20764}}, "0.1.1": {"name": "@radix-ui/react-collection", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c03b671f56b3e7af03f50929b513526c0b71c62b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-WabFzfkvG1uCMHVQd8V++W6qnDqvr+QrbCAXhzzWheKbiXSrwsvA2lTthMn1L6aPn1wyXlX56Xvbzz7Z3nOJAQ==", "signatures": [{"sig": "MEUCIHhSVH96SMpZDQ9Pev5vfFWpTryMexCd4ANL/lC1UEgoAiEA1k74IouW+IfoCVhCSHOiaqLu5DAl7OKf8W541ZoFcFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20713}}, "0.1.2-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a346513a2a9d218f9885418c561334950dedec30", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-c45BzYzA7kiJT7Kmw8UaD6/BkRMuaogHVWjWQ7urim0azsjPv4AWpeXK3yKlUYHSS5jAgzMXQigfK4UBSL2t+g==", "signatures": [{"sig": "MEUCID/tD1ZDz7aIc5tqkI81bc9+6zVt28SKSc64bsB7jzAtAiEAh1CIkD48sVBdFf9IqMayzSjaUohth/XPFkGra6QpOkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5m1CRA9TVsSAnZWagAAg2YP/j6KAlMHSQSjNfURWo03\nfaUdWxTn7O9/zFPyB9L/SwSCcGFAvXwLuBet+VRMrMduvwZppBbl1guI7F0L\n3aZPaOtJxinpyHTF9/RTkMgnE/DhWojX4CarCVHCzcY7bo1xgqkLYRj+7aUO\nRi6f4S+h/xSX/WjCFB+r8KpRmHoXF6i+BLo09M4lyqpk6+jd/o/XP4tvtF2i\n5nXqL2DNyeNIpExz19O6Bkw7xcZJyBDGsgJR0IsrlXwt1rtiQxplXtm3Ugtu\n8luozjkyPOSF2QloWs+SQSUElMgs+43Eld9qrXfYmtrLKJelPvRzH6wWF2Dz\nfGbSL+hfaySIbtQ2Cn7TUsy+h3TB0m94fol83TsJGEuTP98oLgIBAIUPScnH\nkI/d6M1G6eiClOb9Ypn50nxv5mNtCxOK7+w0BvqQ7soUmV7AzUtqaRSgEAhx\n9TB/FFYxmV5pMDeAxn7pzGAWWMu73+a7wsf4JjguzfsfGYINygc2FsE045pE\n3OPVeMcjHyRgJink8eZYV1Jbnp/svrM+YwzTJ1bLNbvzR9U7LH8tF0s/G0B6\n+ksoUSKJoLNdWzINBo+hsQhh5CIf9HR6dVcloiA2NqoPeYL5tOYb5EAAyQm3\nXoGXuIVJv/6agNzF3l+EiWE4oxj/9uCH83vCRCINuM6/hbdcCLqExS8BYBzU\nhAlN\r\n=ugVk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c23afda59f8c76be7133d025be8df6421ac4927e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AhMNmmBFUxrCv6Sd4pI2vmpBU3VHZuu9URA6cAOaWgZGZxGEHsE8PScjPOty3Zu65B8cKJOJkWYN4W7QG3XnVQ==", "signatures": [{"sig": "MEUCIEH4+Z5ST1pqKodGaoVBQ7FAv/4EUw7AB48LCNjuG1I3AiEA6uJNY55BjNssyX5Avll2ui8ie+CAzFp2vbCOFXPkcA0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN4dCRA9TVsSAnZWagAAK9MP/Aqm6T0zzQK7Lv9TuJ41\nLd4hm23EQyZaCN0iN/hKIpAYPE/PvzDoNo6Eo3chAHb0yC1lJIkIioEeuhsK\n6qAwHRwxoLy9wd5kJI6X7Wpbn3BwW+suSou+AIB7BMYBNN9tOjCg9Z/O4emA\n00DEF0l7zHP/XDn/IvuQjj9XTErRJxE9rzsGqavg3IBDq2VbJLYwK0x42lKF\n1Z513xOa8hOW0cqSiuGIu/lSvWXMdRRkUkb2J5e7LJ70tf9rKYCHwT5Qq/Cl\nS6NtSWliIRt8FrqCp4WE8UYFkOCf/PZFch1ccR2C4ydn8OeTtO8Hua9IOuhi\nVJcJ/HA+RZyRK1v31pexJLBCpbQK7sLXUQVuC30gI63Q/WftyYWrnqz8cesx\nXb7r9TlZNNpVbcWZ6FaQGs1w4Lo4xclXBSxFiPbf66sGnqT4dO+fpqxCMCxp\nIJPVs0SfXbGUs87Z/aC1c/KTj70DcXElJsehc9WIz7AiJ3DlusoAnZGV+pb4\n5gf5rP67/dw4SkVbauK81HtK9wrQvtF+e/BgGXtJtq2TOnJ8hKb0AJk+zLzL\nOMGHPlZzykiyB4zUQlAnemJnqX63tWzb2oEhUsshiX/fsOsDs7+r/1lLPS0L\nFi8ZMZxIfcscN9qnGwwJtKUNZiLAgJifct6iVJCXbvFq/NLrfyBjvLp8WL6r\nnI+c\r\n=sCxN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "087b0f4b1d76d04131db798e2f4bb8e512aea21b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Y+8Jn31V5mQBqVrRbYKJEC/8dGT1ylbz4/rlKVrvuYvDLWk0m+3BLE1ncit8nURLpAu3E4D1hayoH7d/0adJgQ==", "signatures": [{"sig": "MEUCIFy7SU/kl2L0o5UZTfJJZChSFQliFuDeVGWqNxo0I4TAAiEAqlr9R7y29i5rJp4d/i9STw6YUgI+Ui9J1vpn75VDevc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoTXCRA9TVsSAnZWagAATQgP/Rv4gRANqY32APCdX/Jz\nE/tOx0zcwdYEU2qw5u33Egv4kkzhZkoADVkjhh0jUYwWAmsV5lLjzvmHRBdE\nFGy4V0xsDGg8npzejm+JtKuU/U9kjxcBBK9Y5d/j0TR2/xe8SiV11NG9dbdV\nkVXGd5S3E0wtyhnJ9BGkQDfcHTudNhn68ONQExzJaghjr4Fdr9txalMGllCR\naPrQ5J8kVCj6Nb9o7huwJRFHzIgcMEMtO7QPihBrGF308UIZRcxnH9tUrNPr\nxSUzB0wDaS0cXdcQe0QMwQK8NI6zXliwHCn9wrPvsYlWuCBdAixRxB9Jql+w\nztG1/vHWuhao6d5p4cFHqkzn01p1Wlb1f6W2w3DM1gKJZqvgIieYAJCkWQru\nNwlErLhg9hjs+XmSJu6VUk68BUyD6xAsQ214rN0sURAJTP++hbPeDWYk/xHU\n31j3DTjheTNVw7GLNl5hTvWMv0DvaybcK0c3l9db4efNSzRysEMSkh5VVN8d\nZy0pMQ4JguwhstSea/ScVqVd8ujPQOCYIV/mr34pXakTBOxIpC4nYHc/Gw7F\npGUTqpKz0NdQuoks/mRibYXIP5EabaRJK3exi69OpfAkxN6izpJlhPLVsZRC\nUDhAjaaR3GqBPtJ3o4WAz6K4MtQb4YaMcaFQUjBHKIVr08MHdYMYyGpucWDI\nw+kJ\r\n=97hV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f5cb4add5cccb00fb6c6380be408a6131334ea23", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-1D2GzzoNPOmiijBrFsm02cJGq7fbr4XlILv1qX6vxb7FOZ/+jWuWXZBb3xaDVZUs1apNF3Am3e7gItYzkCKHzA==", "signatures": [{"sig": "MEUCIQDSslz0WIgqPocAFGrm6RFvta5y3o5IKTpRqyDJeHcswwIgLTCoS/AwxTJSLfLRDlzpQTaXC2HJ5K2TuwL+H/jzukA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/rCRA9TVsSAnZWagAAQF4QAJYTI7d/i6kKi0SQmpIu\nBAjbpjStR2Uq6EyWPFVKePUaU5FalSf59FVUlN4a8h7sFcQWfn7WTU2TEkBi\n01KFstNfmHp0afNu8NUbmikdbGbWEiBsVPDROW6Yn8jEGq7N6pkdYbubrGeF\ndT+TNzfzy0+0oKbFxvSh2PqrV1y4id6hcr25JPkkDVeN7QJviqOFYA/p9YYx\nWr9hISqeWh4fTMD/lj+WthOCBWL36tH8il4RkaNjKsyp9hc5v0/LHAQTMn6x\nh2JCJQVnXeecCJcPfoR7g/w76MQuHhr8jsWwNBEw3I55wGY4Dt2P40K4Icwn\nivNJaUUuaORUXMtK3JWx54gZywSglTohJb6+YXXOrKx55rjhWklxGnKjnp/4\nK7PTHRlzaUm/dFhO/9CM/nr+f3QvS8JViU5CkK0Q+4u3aBuUoNCrluEnZx9I\nnpK4YY6j5d91cz77leM+5K+wZQWG3ahv44L772/wqVfU2OmnWBsxc4jqam/a\nu2gIh9p8Hpp1fboF3sYmL4CtR0tZ2Ajj5L2aTLo1Tz6h0gnL5O9+AVmnRsJC\n0lMFaVl70PACQj0YTVR646e13kTBWFvZ1kdm70C4s6Kg0Wp8MOS5Dyz7Suai\nccWE5XA3XHjygxO4JCTODBGfLG1XGu84/WddqCUecd3xLUCMonpzWKcRHmZ0\n7lLi\r\n=9epe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "58e89ec0e19cdc9e716800f2f503c64099d95d38", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-PB1BchSjfGgqoVEoheoybTfhiYT0IHw8Cfb/9YIv1vNIR/NTyFoXcDGQyrvocscDxv39Nh1z8dBCxHb5ohR8tg==", "signatures": [{"sig": "MEYCIQCjqEcnPZfUr+DxlEz9UgJUlqWxO5fCiwHpvjxEy0gcrAIhAPfT7TxBlDX9BQekvXlyKdJVrFD1lu3sU9+8Zhv0Or91", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiNLCRA9TVsSAnZWagAAIQ0P+QBkYnvYeH2G5WaQdiRo\nqB/vOHCf61skMqvGS+fVl4PZV+ZB8BNbbrJWcy8Z7xreQhJ2P3f19FIXMFPJ\nIGAmaR2ofk8WUpKdjegwLZ2u2e2wmj0Q+MMA0AAo2BylG58FzPHMRLPiOOh5\nf/MHIKSCWY2qM3TTFb2ZAkwO8AZ/N9/7KKH3jHg54oPWL1TJ1JsAZVDewFkm\nQ69yXLpbxq5NrqWSH478jdn7x9dEO5WoQ1W1HSAylCx2qj7FuNYrbhhDb++/\nNrFVpRXqpqiA387I98jUBjZvduXeOpBLqV4lHDN9ZjW5ulX21JY//sSmlOMx\nzyQ2DOjGA89TatfonJWjXfNfGznPNTnIlld6hH/RjQL9nSl5gFgca45qEf+I\nbl1a+5YvCRxLLCWKxqujpgGJ8zb+gT71J7motmXGE8sEr9HuXbgXWNvBZUPT\nh/ghYYm8XOXHfdiGO7AM7U1AKaF9qFa4O27XiKxnLvfjqB8gEtQRRW0EMn6H\nnwB3wnBwsrlIVw6TYeCdBZerdvnw4Armw0tag5+iE9SVf+YjEcZVmFww0El7\nx+TifCJmMS87D4onj6iVMtRbvIjsKlReGAQ45S3PtTNZmnVK1MOfN//fRyhG\nZSxtc45C75bYrGZicaQ2LFo6U1H8tEdD2hdLSSVtT0v65NZ53kwjVzi1fMnR\n2BTH\r\n=CDpq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "34193ee266be89eba961426132c70039056178b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-HO4QhHCWe79lTbArGqDC54z5CY3zmfjpdw4aDpBZMoKIGT/xm/aABM9wFiU07+UbddpF5GqCd8ShZE6Upuqyyg==", "signatures": [{"sig": "MEUCIQCUW0CcHjSB6/5bIEEazTkLJKotR7cPfJrEGL+d8cT6IwIge6Eom4zmbON5OqVoqrN9aI+RHFjBBjbHGpSBrinZIpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryiYCRA9TVsSAnZWagAAikoQAJiK45EB+ctSZvJpV6wk\nT3m+1XjdwKGraqpgyWyhobpold7FfAviHzBGk8nLuxR+7vYE8nrsJfgyYJsg\nCj5DnxtYqIYlzR1ak5jNzJxxQiCc3j0r6y4x8IUf5VDjBGmXMgAXHNaG+gCF\nK2ELzLXeaurmAoPAiJ9GWv7jndUB9n+oo8nr7fD/X3fU5l34TEwBZfEALm8t\nv1Edy8pcvCQiJQyFPcDslfGz9tfktY50RPodXipXtVtv0h8Tp1en7B5GOBHx\nc96H3mGVw0RjohSg38IQZ7okzbwGDuSMOuL6izyh/4gul+eNEa6Yv0vtiP45\nOK7/cTk99QVLOCHILCN4MoaKhytL3W4AV49nMsVmT65PLMPwClfhX5vb7w6T\nzzjR4RxWFprQJN457iDfY0fAkjzk+AQ7khPS9MaZsz33FjCnypGKonpXvFNs\n8fr0/6k9P9gnz5xzBUlPAxMwyrF/9wey9nvKpBTHJmpAKAmphdsBrkt9cDcZ\nfTmVIS0tQOpkGvm97dE+TnZSmbiYeqAAldQsinVVZeyA7aDHBacsJr80n3N6\n7ofs1yfg7waJIs/AFCoCJT42s0qRug7AQQvopUOJT7zzvyOeLca6shD/cE/d\n2DyojA9kXaT1bNPPaDgS4Au3n60+Zrv2XNATlyf/6Q7HQXv7rgIm7LjmeD9l\nAXhU\r\n=tIEo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "27bf53b575d32f2f19e04d0e90d90aececbaba55", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-5VD863Ad51a9FbphJuyDO7R+P65WGjVeBw/KB+t02bF3jQ5lDmYfXGWV9JeD1T0fIozg1j2542+ZqJhSSQOwPQ==", "signatures": [{"sig": "MEUCIQCxvfveRKfUBXPkv/2hHBahEWVXtIwesmYzCCfllxFUVAIgMW1blbQoCV9TcJrDkbn1eByMOk6k4b5Us5cD0DuJntU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzQfCRA9TVsSAnZWagAAzd0P/j/Dr21rMBYzZP+dSizQ\ndoJNCB1XftOPrJ7EY10UNZSlS/UIMGD//YVkYsJBKBw2wwDjPwJsYplsm7Sx\neE1T5PVXeVWHlIDpbeQYDL89NIP3nDmGgqA5D/hWEt68C9UmOrNIFqq/cxPN\ncBzkfCOOE2kQvBEr9PEkRFZnNvYgS7w7lPww7Y82+bk2yktdMKbw32U+D1xB\nY3wKEXlZ0FhN1uYJhmQz9q5Tjzv2SwEQyH+olyf4Tm5h/8+Wm86XogY/x7oC\n4F1BP6/j5BI5a3Xa3LGNJ61jh+lqan1joFQ7N8WPQ4R2uemJzxTr7vZFj/WT\nlS1U13mHai8z2FQrIMa6vYgIzPoEJqkgP8u7NptDKbKkG99kJrSA5RePdQXv\niL+nqWyKaCLF0lWGlM6G6zXFH+p1uPzsOoJFSapj6MVoj6vwCnUSQAOlyqxQ\n+cY9oJBbUnA/3Rvjkh7FoTnLAitP5yVla3/fh8QEXrpJ2Pl7CVK5TCetsQ/u\nLEP8192MwCdYPPbqAAxdbzUjEx3RF1lt79vQF51J51UbEuOT+clGOQx9zBY5\n5x882SJ16QmVcdcx76TYIILYv7w+jKwSOjktW4Dz/8ObMWQpXAigjUn3iXHs\nssfOtvN0kSy4veUyK5UTCfmGHNUMbUibnEkrc967NIL5ebS/85ftRAds6EBM\nV17U\r\n=a45+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-collection", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "846e7d2ada3e3a52b6e6705b0928876771de8a82", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-e5Zh0L+Lh2N84rOA5/R+cGKyIUtw+fICJJ0lB8wAseEhHgeazs2JIF3bHXHs58aT5PssspUw3f8+o2kK4S33kA==", "signatures": [{"sig": "MEYCIQDycneXMst+lWlLAJ6uFsU35J3/Dbkw7L5Q1M5WAOse6gIhAILi7fn/CC/9pDwyJMz5YkDCcTboib6LY9pyC5B7ixTB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41yCRA9TVsSAnZWagAAPGgP/A8esI3iO2QhueXuntAg\nKWtiqbXhsdtH97WJQlGsulcd9xqFeF+LI/JvlFM3qvx+iN1Gp1uq+3brXOIr\ndLgoxwC1I7wDpj1omeU0jslFwBs9VyuGhrRnpxIeFb7PV3UIBvq5uvv/j7X0\nFrn+ThyfB+vH1p0hBH3LTutohKd2O8Jh6jGNC+FKuFTaFnLKPoGltA7kjwaz\nn2IFDvQw+O8rFQtnN3mBdCJxWpsO+iRFSTOJZznMLbCxf/zpyU6TVSqeZeHz\nGn2rfwHYLaTNTlqukhp4zsghkL0qCNgC2RLqSUBOUyi9mSG5qeVa6BGxPj85\n6BDCWVCPXJ/sKsRox/bAU+Uk/Mn7hNlCpxgkv0wQCdSGDd6Wp1C0q46ESXAB\nSMuj1zZtEzEl/w6nqIYvXducG//N26S/tDx5qTfaMQrNrLSskMzfT56MKMD5\nEeDOV3NM6SikSyvo9uPxr8cqZ6KIX5LWXs/r0Qli72U9BntvReuQvvME0daU\n2mrgnn/dNn3FtQL3/u+n7E6JMR1jEdMc5rKSGq8UEmx9yAQttqd9MlBU0pEw\nM2j3/1MSIJ249QcttQGRsy0t2YTrW/fgt8mIWpwkbndXz6JHBiHeHs7ZDNEL\nmTXnApKl9shmVdCoiEN++GHsSMMwejeZUo6tg+QTeP9bmS94euGXNkI943QV\n6O5Y\r\n=wgOA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-collection", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2b757dc7c5aa1aebe0eaf7dda2c46776987f3187", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-sEIlbAaOBm6k9Z/tsf0FrHlsscd8u8waHKzlualgN/CIuouy4rKXoVuysevgQvY7h9HPV5x2AekWq6BABTBOkw==", "signatures": [{"sig": "MEYCIQDQHLqkNKOCSrKQtULUciZZ0wJJGC6wdFnL2Bz6impSOwIhANkdZLyFaVb1jmcj94SP2eLENK0p1v5gf96AnXXoK4/b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDnCRA9TVsSAnZWagAA1DIP/RTy2147calUaNtpZNK7\nIg2DfPAgJ2tj0zNpU9DRzZ2W76GEV9D5XGYO2+W3kJhmssJY8CbdH8YBLRqF\n8cAQw0eVTb+Kv9XRkAfD1l65Ky4+SR4mRKGiUssERrPbr+XhbS+uropNTggr\n8XBhim3BvWmyll1rpKabUez8WVqDux/uE3hIZwJCLLxcqauyJfrFobTCJjkP\nrofj6S4wXVF984pLX/CG5Q4VLMK1fGzSinUsNfCHefCMeAhkFJzEn8BdgJNe\nG/liWlU42fRNJ8mzk1N1eBTT8bMx8ZJEZ591CK6Pl/eLWQG+HYiEC+w58j06\nazgso4WLjU+y40iZ+jUqcreyx3ubGCLydYqqhIUKWFBJe8kzxs8eey/kJnyE\nGGDH+ZIxtfpdBsw5g6oAZlsd2V+eB1Co4KsBbKnPw0AjmQeNdYPGQv/vjQ2W\ntBxZ8qGKSQC1jMBIEkWRbTpHWSX5OKbv74jJtL5B5NiVf+kZ30/43OBP+qN9\nwbuyz10mIYWHu9+FPldIC3ejkSUGiXEZMuFTZjN0Gmj+IlAFZZWKGs6YjUO5\nqUYJVHmbaUvzr6+XQ9RjPZCYbcxdJhY7Y4JtXB8RVymRPeZ8tdHE2/1ntxz+\nHKGQedXho0gZSVAOfo7MJdLZtXu/+n59QpBpJQaxHlUwpY/TbJrGJKkyMC44\nj20g\r\n=Wqle\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-collection", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7b584f5db40ce165883b87c383d3bd16c0000d69", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-tMBY65l87tj77fMX44EBjm5p8clR6swkcNFr0/dDVdEPC0Vf3fwkv62dezCnZyrRBpkOgZPDOp2kO73hYlCfXw==", "signatures": [{"sig": "MEYCIQCP3HaIZ87zHDEyEa+/0bxCUZxg+3W5YFCoFqbxi7h1GwIhAMOnPF+4cTuVVbqW4SV6Okexnt1I8+D5SWg8zmcWthpP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLidCRA9TVsSAnZWagAAvboQAJhjL0n+Tu5tmlr16H6m\nSl7GxuBGqSThyiVzuvCiJU620RqG6NVDsUxtxshFkRRZBfuJtjYuVp9KsF7w\nOMGAluaxn7fbj3q+IilQn0C6EAc7NOMLbt0FyZrHRsKez+5WXAfSkeUFQff/\nU7BFjGjcwAWamfVpH9vvN90Ma3odLsDOZ/QmfYbHULuGLHfmNwwdxSrA5FCj\nf0fvro4Y++eD1AiMVj5lTyG5OFTZVdUdE9U9ktnKAMybUM0KnZ1YwDMQqQ/T\nwZfMxMSjtyNl62sIKu6elXH8x/ZE+3sIV+S7eHyJQR2OpdJ9WZyvJm0HXwrW\nT7d+9IZDl5jvp79LpvSJ0ea/rbs2hURf/MIxEioHWyKDKgzTl43EjXiPCSTp\nCrKSup6dC/nqshkC7ef+aS2//W9vCVxqwFbZ5eZ7iN7ZuK+ToL+q2JvTBRdC\nypzjnGpEkpU/fGDogUtFHRqI2AaMPXP3z+fmfD9jrOS6YLduJQm41YIgdHoF\n78cHZvyVNvEpLhSA8lPuesGrXvdsB+8X2bTZWSxGXF7lYwwgHGBOn993t/0D\nOp1aEvqOCi8rGl7jWOhQlbuoRKa0PpP/Cuf1+ZsC9Wql7kgxrUf0ehTeHw6N\nVuJBy0YA+3mstGr0THVQ2VwP8ah7YHzm9JeBWokBp1ctuH8zKHAwpmbKnzYQ\n6x/x\r\n=/kZj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1f64667cfb5ea8503d3b65b78103f068f221ff67", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-rItctRgNMsm7GJ7c2O3colS3S9ESOOeh+cKRNkYvmJUGfZ9nFlAKSOoO4Kr1ZpmmBMnWL8chIjUwV5LxPqge7g==", "signatures": [{"sig": "MEYCIQDfnKo3oBo5G1qT4j5He21/HMsZ9M+IdkvupTYtJqLEZwIhAIFv/I5NqAkqB75mjAWkO2AecWBVizwZE7Cq+0teoBbi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLisCRA9TVsSAnZWagAAH3kQAJy4ju1gfXWpEUhMQIiF\ng/1FGTQ9ooJc7nmKqbn4ohPTH9MOhpqZB5v5QB8g/robcNfjQZBghJhLfM27\nOEw8TmKy2EWTzTrg0HygpH+543HJ5IWoni7VTWvazymLC9VD5lC3NY89eQkY\nbVgeYQ/zwpoVvo07Ef25oTl+Pr7ebWVbXM8ZWoF/VZ6ZGKKiHCtxHnjeAz6W\nTN7LoUNd83z/1FgJrQCj3u4Px9R9gx+OtbFtMaoGMvxMDthLjzne0ThLkITh\nxxt3Bjl64gloUGMnxw8o5FGTW7A6/lC7+j2dvRiDR9voQ4BfG9/Gm50GPrPT\nd0MUxZNR8q8N5uFy7+D6p3hYIsetuKuE449xqm7A4SLoG6QntOPgP6wsB+Ax\n7pII4Sn1wY9/7WDMhkubAOZRUsTH1aiWZBvHG6jWo19Ht3ElxFHKCJO6dRbE\nft+oOTGb4kSbfL+/5bYf9Yjq+gsUmLa+scTPN6it0Bvxxd/Pn2C7Qn9TkNKY\na3Y+LwEx3rTfVTIcYNpA6grkfFCrdpgnMUbaQDE1ZJTfsq7fBlFhSL2zEeA2\ntpQgXocn0zkOfJeh59Un6dP9pNpyO2TNSqx1XnczaJqjdB9Xnh1ZzC4wDLBP\nB/o6ZbwFZCU/pFJK7arxVLk5ShBvZ/SYNHFUJm3kXgRquB0ufWoa5Og04KmA\nHMv/\r\n=fv6c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8452b59c624929f09dcfa94faea3fd9406a320da", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-gnr5ekkO5IPBxsn8PIoA7mFv1zbcVzww96iYc2f5bvN+f7NJ4YLGif4YM/eUov4a96to0jnZezTjuivOJAEtpg==", "signatures": [{"sig": "MEQCICif9/MvmL6veHQ9AjyYDJXmCQcHMvDy4YTL6bHUDAlTAiB8RURpPWMPvcqNgPlKuNPDd+o+bMMmM76qv6DGDzR5Qw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31qRCRA9TVsSAnZWagAArRcP/jp+ySccMeLDFYqkdGwt\nTJfGZZMk7zwWmFEDON0okVmo2VZdulWVqn7y+n8N0s5SdLh8yy8ycLigXD1u\n4mKq3p/C2jqZ3KsLrBPyPRDlElYbTSJNJ6VvpYe7GNOTaCpgD00kNzdpbiFl\nCa5HA0GLVXJxja0kEGLq/F8rBVm+ogsYRKa0lrhjFHNiyKz8E1s9ZKUSRo1W\nSx3NJZMALNJgqv7cUgcyps82tGcIqx13Fca8hidub4zY018lvZMekYnZ3/TF\n9SEdmJzEPOJNDmw2vHnwz3Rd/Dn9KR7Sk7pByNeB8nppv8c+0GmLOJW+s/JR\nF68NMHN4o3nIYWffyH377eRZHfKMlbosF8m7g/kyV9qgNosrZidy8EUmqVwx\nreU7zvDnINnI894ghtRWfso8nX2wTZyddS9iyKBsbNTLPIQMKv71hERUlDMq\nHMkpKYB3A4ZgSugPSR056g29MU+iJ49UHpxrt94x3eSdkulVo2PLBdqGauqw\nu6ov3nO9BOkVeFLuWGGPloqRPTl9HZe4yDbbr6JGu1XSPAiPxnePz+HEvKUC\nRaRciPLQDinw4wgyRkFeWxv4L2a2AZ/ZwVnRXzgjXpmc242XOyrhTOjrVXM0\n2pFu6troYdzVYFAGhngAgGet4IiyrdvhAPtUfHpTJC9Vpcv8M84L4m5u1IuF\nBKgd\r\n=bIQe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9cc80cac2db89c77de74727ec159457d25a35102", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-bEcISQ/ATqtYeOzZ0jA+HFbs+hVAn6TCGDCDzHmFXllqkN6N4UjoqqZ5UqRL2eqXkntpUCu+iAUAooj8CC4GGQ==", "signatures": [{"sig": "MEUCICGAZWqlsFklp4ijwGtN4Oeci+h8o+j58cogjN69QtdLAiEAlgFaEIT94Hc98Z7OYLhivLQfzfvepZ+m7HPcpdEZPDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BDMCRA9TVsSAnZWagAAhJ0QAI2wyFcDr1YiAVvnAteH\npoAOJYqinrqW+lUGfT7K7zK7qWDOCaWC8tjr8lhmShcpkdI6DGqR7R6WdBdn\nEru3tfsZixH55vN5FpDsop5hxdfJB4fHkfyBtchBeT7Jmq6zm9rwLboQ5O6j\nrhjBwtR+tRB1yGxsNUaV7umF93HL9z2QIJWYic29FlO1WtGRN8PwfUOWEwLH\na54IAiVcwCoxwCNK2gPS6ELaT3vEBS9ruBLjCm8jUTFr9xLDdkYFIN7t7EWJ\nib4BNlh+NE51p5Z/W638syUHq8zheQB4LfgaSfS6YLPs9XpcJpF7JUptPDO0\nt7iKQIjmIPhLmJZcvmdNHpA7UEr/cOtQ39Mp1w6ZgVmAvv6se6ik0w4OR6W9\nO0a48YWv+5rkBpp0nLJ9AyNvD/U9zlyvBwzCU4/a2p32TJJMOsPN/8J+5C59\njN9AOQkjblw7d0/MRozfCkNVagJs1EOU/c35iusMssv3F5Js7xH+2bSwueJn\n6jajw36V1PMzitmVcbGuNdutilEN2hM0Wb/at5a02xAevMqca0SoZBYa+e5M\nPTZUimcfFAEk8rRk/wvm2v7qjU+u2kOQ0XXrHHc/PH06P1wKyJr3ubUpgAVL\nsqK8CD8vLmmgiJ9Rea3KPYgIp6IMK37Mdxb5kE3IIaGhHwKTunsIrgFiQBgL\nHx32\r\n=Am4O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2fd74dad810632ffd27d904c0e653fb12f2ba416", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Q/M6NjaUQUuQXzRy84p/sAyi650G3lbtmhaQryAcxdG50HoIJQ83IPJVQvRf7/w7QWJWhd1Tp7FpuydB0wF7NA==", "signatures": [{"sig": "MEYCIQDmMN3+/pGe+wz27GeVX/8IUkAgG3+5TlFeaIBk1N+JFAIhALqYcMbvRHHuDXiHx03Xt+PTA/t6nkJuqv8LPa1kukZ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4ClwCRA9TVsSAnZWagAAlhoP+wXxysqkymBOBAV9yrWV\nxhqlOZrnHHzRd/FpLuiIQ+M2JLRJsCqNH1gd1sEnO+Q391tP62/lms5CAIFE\nn7NDyKWQC3HgyjagGPDWcdvKAK327FRIf4lqNvUlX76XcpdsqMz7ni1r5gNi\nBfIbRZAj3xfJNMSUGlM+wE2G7YZb7p8nhxCyyiZ04yl2U6UPLKRVUbYxA5tJ\n1ellU8qti3GeU2z17oTlZzR54P6dnZdqYxoBzdYrQIDCUOaK7TJpIh5fwhJN\nRsnRVX/tGUSBTKL4bCKCZkzFW9+kbYmY7ZCaNx8cUJUXS/P2aNi5fJYMlJR5\nH2Z2p+IiKdyXRaOMhiH0wr8L9F9TQVAu8/Fri/3HLlLpu798kgY2Rro4usT0\nSiBWc9FZZtbYUmO0EcpS3EYNPyizmlaIB+FEnZ+ekwKVM7i9SX91I1Fj7iy9\nS/Pl5AxmGANttWSxXuEmX0IRzueyHPgnMOZkhjw3UzVXewO2OAHjQ/K/hAje\nVnkqtr0M2UWLlG2PSQLNNymHk7HkzDU+5ZQhIegYQebhRorpxWMoUosCNmPO\n2hX1Xlbr+hfvSZeoXrP+KmT0FtlSSCufoPBlg5+GeTPsxjzKYLFoTMNcpKPc\nSPdAXdaoQ7HQKumo/613cJm9vkeP9Y59zDXG3/QwHVhMTCtw1aIgtZOMM8P8\nk5vO\r\n=szP6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c6d212a145b28cd23d7b2369beb78bbd83bcb5a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-vvFuJB37bgnsHJx3aaduwmoDC9dwDpC8sWnzU0lC7spAVjcbmqQigccwOXfq4Mhij3Fsf6CmdP6Y0ud0CBm9Gg==", "signatures": [{"sig": "MEUCIQC69E7ya7OcJfzWJJJWt7F3KWgkVeBAEneKgKj9IHCp2wIgHRievKrHcykVllW4S3ABcTdgHdn+OGLIDfePdkizbRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GpbCRA9TVsSAnZWagAAQtUP/1ecMqDmnYh+MD40NyGd\nIP7/NFOQyRYev2gYMIMKS7z7mWdZmI34oP3FNQ4E3eRtfqqN/8+IzfGYp0Zv\nqLTS+3+QhrfFCEHL7dryoYTCj40I3mrNJVVuUa1sINBvjsYAPbJmmFnwzsVL\n9GjGRhfMpvQONhyTK3qqnbruWqFiK4QmCVjcgta3TsUIakdztYgSmCG70RU0\nVSLLURXvdFy8oaZqTkD8S3A14DA3nnHvr90lsdY5GTmz/LhE8bxC8/rjt8EN\nU3BaUbwgnDUfslPwJS4YG1dh4EG1eF0gaDRsXI+5sRCb1vIOOMQxjMgsPUJo\nR+rDPlgL217V5HksobnIuEdaSJlolMhT2zWZNTIGnFe7CmSd0icKwmAOjVhh\ntOdAnDYYxzDfymOaWX7jiOd5WcbyqM6s7uHdZD5iWR2Exkwz04E7SYPLYeyX\n2QN8NITroWeij++ilwzAl3NLfGLurM+NnRBddj8mwKgva/MBNprDwtVcLv3y\ncnRB/mSH5hsOSb5D7VdM3+FXYpvG2/SYHOkozFDCKoM4TnXIhB+kSrEChsuo\nhMHhvWQmmG15xO1rLPoMnBLSeU3QaJ+YhS0i7Jj9k8ZrEeTTcWiDGeCZdpSn\nYLTVCu3mYeydiKykhnGYaNdbxzp+SoyzjqEqRu7Tyr2cO3jinkSqvcKab+Dk\nTfXE\r\n=HnWA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f56621da62834efbdd1bba2206966a2e2639d115", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-nbeMwPRj/PfLAEtCfxyjUuLjppBk7L9dzWotY0uSJrU6P6JnFVQAUgt5Ju+i9S+Qt95LNg00E+Y3rElVkojfGA==", "signatures": [{"sig": "MEYCIQDOVXpF0iW/ErmuGodx86Ni8A1x1j7LcmRDFKuHt8+9tQIhAJRBb31/JoWxEgXn5/EK2fRzVqA9bUNXDI7n16m3SQLj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbhCRA9TVsSAnZWagAAsHgQAJWLotCqi5J1RLHP5WMZ\nvxRaK1F31hK2QuTlCSZJ4rSuk1gL4gguAAmCRxMReaFma4kSdT9fsRurQBMZ\ncHVNLOQZca9Dy3/uP3itJdaDjoKmiMSPpkDHODRc6O+FK5eoZV3EKgZ8UB+c\nUgcLQ2a3D/A/1e0epyUIQjV4D59T+C2JZ3SVuzBRnUWL9k4/GYnbBV+O2kZ1\nyL0YXlfYq3/4mChpziaGDCEir/0ZWhUlrKZIjjWKDMXaqi86uDNB+xIszR74\nr2Zebv3n0hgkOFD5KdcwgIC62WnYjnVk0alzzEbUY5Hm1b6djwWjDLxp1742\n05seUkIEI2UISBdlj55RXhUxt3QDZF9EPLft1U9QA6lNyyw8DhniNcl/P6/I\njWeYPvHPmw8+j83Ts+0AghAgenEzja76S1nIg9RtENUV/Ht2ttw7T1RTPe3G\nocLBhhWj3/0ydmqgXnoPuXB6jyXHRku530pI0T4Eh0C4Q+9nGkpgeqA/jS15\nXwA2+CzwF/MPFdjM6Rw1mDmWWgTueo+DM1OIVRw1zWJXt8C9FIbogfpq8MPY\nHQWHb+AXxyOxAF1EqJQQBmVzK1o0ir/sC3aakqINFlFE0gxPqWyvFbo13M0X\n7jBapTKTmOLjOetRWXWghWZLE8edaOJfXDwZFSuPrXyjQNxEN6l0Z/83LBsQ\nmMhi\r\n=hMrX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3b3f8762e66b3c0e642e752758633d84ff4d69ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-+5+Sv0/NcCSwDVMXWKdYzmWdwjIJVX4Mk3BNe7aukfEirxLjETOCzM9RM0TqXHLIIueKWeuS3Gt+/0P6AckaTw==", "signatures": [{"sig": "MEUCIHL0dDjRL+3p28VMNo0F84pKMqerPJE0T//EYhwmHkZmAiEAzdxVz+L9+KY/KCJDleFX+IIa3W5GXjt1mjDHm7f4BwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6Yr9CRA9TVsSAnZWagAAd+kP/jZAAsGEBcGqMWdpaH83\nO8VDqzK5IdQUOrPz8OFOHoSCXFCtSMFpjODcIKoTxqBApioIZfTCrk4sC8pU\nVE1d8TAxNbYk4130hH8Ho6Kt4Xy/A0bWhocQg8WUAkAaIMHZPz0zxC7bpH5D\nt3zFWH/6PY45F32/LTosfQJcBoO5tiX2XUdiUWoNiYTfp3CDoFsGJUduA/pq\n2Fop/S8EVyXAakTWN3/XNvV9vz6JafxiCvFfta6cXjf6ArE11C9YroPoK6C+\ncDW0YM//s9+S0pmsT0S55jlEXoCVr1kTUJsQ7pHfMHGJuAZRsX7UXdRi1ewl\no1oX4BuLJC8uPBf/3s+aClndV13/RPErlfEZ/4SH87dWjZE6CWb9qSFJkffW\n2XlsH+tPrIGGb1Gn135rWFfDjYoKtZYeB/T7hdrrItyP048T2G+DktOlkV2+\nVIzrr5DJwk3I2JXeZTqa6kexmEBn+lXGdDcT0SCG3MfMKcYkdX7AgtSPIoAb\no3CmYQ5xfuH2mygGgxTbUsggq1G1LvsCLmnOGu1TGWF7UtyUJQH4/Kg6wvro\nlfKyK+aCOFb+6RHJxwZ4okB6cE9sO+d9DGRHlOLXeZ6hcpqo6Wnd0GyRIByz\nY8edzG81hzXpRb8WJaX3n4BashfRG0PH48ZozRyMD0JUs7HKgIU6V0bCfWGq\njtri\r\n=GN6K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "abd5cbcfcc32e05100df91f3ef9bfc1f94da93bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-lg9gTD+FjIyPybK/0icaMG3qTv5bg4pvpIJgC5OrdjonHM/YFf7pdknhj9jdNcCz3FiUFvkrFfE+whCXeCQy9w==", "signatures": [{"sig": "MEUCIFp37ji2sNn0Klfz2V0zbbOvpJ5ZffcGgp1JbhnV2o6nAiEA0pByj1gtAH6OhpMkawc0g6wHQyOERVWNIaUITt01FVM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scdCRA9TVsSAnZWagAAL7oP/R9TsbpukCizsswrqv1o\nAW7Jl+wvnMOd9s5KcS65fSVZPpl5oUWCIh3t01gCRZNz/PYg6ANKkazAOLRY\n+KI56NHQvCH6zV80kEjowSlkUr09/ykxGg47OSv7DsapHkxBeFlVh9wTvBpD\nG1yscM23p4sPSpsfBOwHWYGT4Ei56jbk3ZnEcDebPZvyLMDHZAgeJ7c2OGyc\nXzaXomJ/TzkhK367h8Zwj5ruoED0K/QCdNyeicO7wMQnenNsucjiSWFHoFCO\nMiV+3xOA/HGLACC0mjTaGDzTifPaleTCJMrRr1ZmLlPso38CRo2Q0uW/4sMz\ndBNHKj/iWyvO5GbP+LuIbf/cXJ1Yd+OWyJN3vz40lx6L85XONu0Mop6bhe2z\nd2/M50PvVserj7WOzdffkEgCs5V2ssjfPWK6X6kQUMuL26VbvV07HRtXESZU\neG111bkou+rxkBPBqM26HFWaCp3bCnVxwFPUThwvN6HFeiMiNGopQAb6ODL+\nXIYdgoYfAbYHS4PNJRhSmloxkPzhDrFE1AywhZQunnJ+WbGjuPwj2sGilZoC\nqXGcliDMUB9dIbqWD38NoH7Y4Sd9Wn1KXNWzbJR1HQiMIjiBJPjQa4k1b2mH\nFN4ZTLVOFiN4ZCVbp9mtXNxOBaiW7L1r3qwBlE3+/bl/vu6Lhtco0r8TdhJo\nJrZu\r\n=dues\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0b363fa33f0c2dc7c16f89c5000f29dc1e03825a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-cdo428EMPGrWxgt1o6wttYhgHDLERpx20XiGxjEPGepCtC2QoiitaN1aIjyoKEzcW8rbJR4HHzR7f4/pLxK5hA==", "signatures": [{"sig": "MEUCIQCcHcSkZ387AOhNa3KBl+gtFv23Js8V8aiTCRR3c4Xq0gIgLtZw9YUZP6Lz0huI1Vx3A3YE+QwvYZLYI2R+/hES0WY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCgCRA9TVsSAnZWagAAH80QAI1a8ng1aCFeRPKQvu0/\n5R7q51QKdECupXG0pUgBphTSP5X8St7siJTQ5W0nag3i2h2YP6LcsNC9HgdI\npiIF8zV3tVPYJAjWLGPK+EB51NSzDt3gRW0+KX6z2XIuuRm4jx9yW6AmrVrY\nWm/Jse2MGgGT+nSVO8ZS3YFAqIFOx1cv0FvYG00w/iprD4UOD13/Msa7al6Z\nPHwsBxdiVCvlhcslFks/NXilMQX9uvt7EJtWNqhNijBsibquIUJbXwOS1It7\nnyISSs4H3yNw3avga5KSvUT9svbXhGzeBwlty3SK88naYun1XkzhzFDQITkr\nRyCnCAPPQHfqM/MAKuJMYvkGkdiZU85R5cgAN8EdKTqV/xR5OS5YYB8aBERM\nb+PPdFDIPy/Q6eCJLqOUZxog+JvjKG4rhS8YXx+IZqJ1G87TiP1iZ5QcGNBU\nCPGwAnERLrUfAB6B5nuLB5Ih3tuqH59o3jZeW3/32O/mZQwR6bR670r8lqkO\nXH734LEL26bOhkrzhKYNnvY2TRBFTI5jK+cbYy4wVjMWTrDrhXJtqoV6X3n0\nY4/Sa7jlN9l0qSRGmRScw1nceOnvi8pZQJzrYiB/uhCE2ZI8xlcIEW5IyiLU\nDqvnctpk4Vpb3C+KKaHV95WYzA+L8pympcI5p/m06iHs1013+Wv5Sp6ZcS9r\nECNz\r\n=y89h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f25c6f6edd805b12d061800b11887b3046a2a659", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-OqIDdZiEsUOHq7Sb20mzL/QdHcQ0Kil7kv67PRrAieYfoEifMIqMD1V0BB98s1WfSCwUMIorWC5B8RyLgEE8wg==", "signatures": [{"sig": "MEUCIES3sIqYedweLLUAHtVABY7pa3ajBRhzdxJmwApiwfK4AiEA3u1+r5fDtkuozAbNEKYHSOZuAoYP/bUxtGUCJ3+x3fQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xKHCRA9TVsSAnZWagAAtx0P/3XGD+OquMRlw7SuRX6H\nXoNJMbdHjmwlWzLwWArMpJvSc0cU2JQcaqbs6VSBcY1hyFWC0FWcuuUnFzql\ng3+iMEZ2lCljGMqGav3facYG0qe2LFgrdRRt2790JKwG742bNDnSFOuMQQP0\nvnajbcRUVU9hPtTWL8ClDy40mq9NZ0pwJZIoaxykyIC2zXihZLJMLufvzhc1\nM6f7TiR4UUacGW60krpOjirHX+Wr+f5abhLmt/ra2RiOyXZP+aC+3QUKH2YH\nvXGI1c1zd1KMOtiF64ocHao7L196JP3WucvOigXvFHm7HWnX9+MduZOeOC4D\nrie87Io/LEkSVbaKRDTpicgMzP4z/Ms5WFLdPHQ/51a+MGWQYdHKb73gfts1\n0t+3yUTFtOWbrUHhSWhtJIZeh9Q2R//U97eOaqXvXQ7TbSxWxbaB3fII7U97\nTqc6VULoQ28o7QSTM0lTkptdARrLcTMJ+T5pTh6FOc4p0y2B6skmsd/u9rro\nbu3mA0qGL6URhJ6PBoAI4hq6M9U3+7Vbn6YME/wM2KEWYy7cUaTYUN/dIywQ\nsBtwucErjQ9Sf3DuhLw96chwj9+UTuBttrfK6CrTW9tetEHlp3N97qQphmjR\nZU7yB7xS/y/tu+onOxdoAddJTUpWnp+W7MkGUyOkUKS0yr6+2s0wH0uA1BKs\nZ7Kp\r\n=PDH0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d124095f038c5ba8b001368c1ecd3aa76de75dae", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-BhpDaBy/iiyY/N+PwPAdYP7+Bf8Rx2dbkwu0DCcKvukMDSabOCss/YF0EXi6TaAe5dnNul6c/vt0W6cTmoLIvA==", "signatures": [{"sig": "MEUCIQDIFDQ0/4G42Pf14Hkyf2190aUgCd1frQuDdaALw+KlmQIgAPVlxlmGly6sqmEQaccDA4WzezQHLIRGNe2rSYA/XBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DzFCRA9TVsSAnZWagAAc3YP/ikCcW3OxsQYhrjzT2aD\noGWsbeLDt0cuXMkRV4N91a/PhqNlorz298rjrrx7phvRAu027sxI6quZL4SZ\noCf8egiD4JjBQIZEJf/7b9VQgbZPT/YzofoHQv5PNiHMGwrq5aHwtmoj9Lw6\nS+TIybdYIluHnKpzKMxIHlr4IzHvVWu/aVGDyOSCp2E/nIo7q660IxfyfH6R\nI1TWVdNvVjCsEQCF6uVdxxCJuYvI9HJSFOAZj+/Bp5IZDFAvnSNxVGAA4dzu\nFY+zbRBa64lsC76OmGO3MVFl/DI2e7Fw4POoMQIDMImWt9QHEwOhVSLEgrFi\nJF/+xjkFfMZ17mDfNEuU1YKj7Qw7R3yWk0bsawHxIieG6SYOsFEVChtu5nK0\nX6lA8tYW036txmCfByPDhsTTXJOfvjfpuZtawVUh73Yo612HRuifNF40lwR5\nIizntR4hvcO7zVbFc9mI/cMlCeBVysqprVHbq04oJy5A/jUU4D5xfx7QGvRF\nuOB/EN2VHYaVPLxo9enfhwYQmSCID+qCrx+i3MrPqh06Q9cjo3jmLdvIfXl/\nhGhClvuO0+V/Jc02uHp430ZWoTKe7VgteNntiHG3rjzwajiJzt8lGhD3l6zl\nsxJKLXRyeNScwpI1tH43BFK43bSAetD+3BesvzswKFJvJxODxakzPmaQNUF5\n7tvE\r\n=MHpv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fb751d74299b1803d313fb2d5b0ba0b2b734a54b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-QgyZf5VOiFAjWNE6tA9Q66CzJhGEeyHignYLNJezJfQGD4mOvvTIR3dMPatuTbsoEMY5ORfHIAV6bgAzuj5sbw==", "signatures": [{"sig": "MEYCIQDa42rkNS5UsgcK+r0Dsk5E4m9BVYk6KcxnMLexEMSO2QIhAMaZuWbVmDOfCeuRHY9Q/dmhPxQu8AqqbNhAaWIK/Gzg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SSDCRA9TVsSAnZWagAAv2sP/1x34nXbicQZq5O54JLo\nTnIN5ZvUMGe2ETvYDqKe+3kteHtpNFOb1HbbxnkNJmtYj0jMShMQJ+7olihj\nXBEqDBA6UWk9KrDYBpzUtHhhs016GpmlOIXtPwMP1F+3ox/WMzMVlR42YQRu\nwIIgeYbOdzGLVT3rsk3LowUQtjTDmyyFvGn5ZwMwXyMAJx41uXxjaoXH93Cx\nojkgHSKROKWSku2gkAvo4Ny/FHb4RH2xksB+GJ3gOa0a7m671RbEjkqPIM2i\nUcz/3BIWF1g/LKKIZYb0OAByYcIhLMp0KkIQ0t8GuLFKYCIVZ/NdVxV1dGua\nkeZL13JV0k/gZlBWTlNtHxRtAL1WNzo9LRmgnk3LLbjqomzsRnzOp72ywktM\np8Qso5cy0dm1HaTOjAMez0zliwavZKz/98DINo2S7k/FIWtlEtPA4mATkogM\nn7zlz6l/wcKypNhodrjbr7QyiofiQTVIv4rlwb7fHSD7GQdDzLs2/LCk8elO\nlFM4V4vNiG+OrvfDoLqflR1+LAAhNMLdbupGeQwOlfJu0KqWYZH/rq2IVs4c\nOEEJ/D5SM73N8ih4Ru6d058HCpTFfwWE22aeM3OJKsyy3AnZzveDkZx7VlZO\n+1fjra2qoage5o575XZC3Je7NYKPu8diYIR7Kb5EyPrLeMwFvrumsh/71oFx\nmaBX\r\n=tWCy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "50398f4c62322e451040d8e99c2a177209f671ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-BNjvVQWRaMFAVtPbqedwK+Vg6pM6M264Qmyu5A70rjLMxWtOtyaR6TyWaAQT4R3snfZpdH5mN6loLzID87g6ig==", "signatures": [{"sig": "MEUCICa9Eu4F/Fk00ScjPYEpUqxVPRIwDEzJJlbZzlHw21KYAiEA8lOol0JUS82eGsi9Hh6UifFTHqqabS0PZzJNoQnpzP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZACRA9TVsSAnZWagAAzpYP/Azf2X/cDgvtrTJQ9Cze\n6/TtnNsgEfowpkb3HLGNXYk3Wayx5+pkRwqMziArcIyiBPDhUOuwVXzCyo/V\narfeye+HGwRlQLB6Cuz4zy93nXgfqAc2+HsQF9kySbOsgfj30cV3t9Iq7EL2\nvd27WXNI9JbwmVQyoTQquw5g+wkUmI7WuYK0VUZbTnd8ajPpQEFUkSbwykeW\nrFssJvt+LOhOFEMMavVekaO5tqf0x9+MToybMDZMJDu05qvaL5tMY7N/5hIF\nTsIzWJzY+k7EGwdrUBMR8Rzc4n6LReSatR6cu4mPRArqy2TU3QrUoSNKj6J0\n9HDAT7tvIlr1Hv+lrl3j1h93xoe4aoW88moZlPX7Yn/+E2MiS2l5/DaVUGPj\nwE99I3eX/eXUr0UvBzEVwN5MzM9Ww+sd/aeh6amE41a8WS7v0Heg7xTWceBq\nx5vSAQGbG4wjz+aeSO+YA6kffahgA6I0+XP9FBAqeyNVpeIA8/HvesDekiiK\nEwR98fx7KHoI1miyNkjL4f3JX0wGSg+Qfir0oPXmi8XxWvv0/VgT6XHAlqoC\nJ0Zfpact6kvLHRycRb5pPpqGNHvLYSpcb6ljtkH0QeW1vATe+BqvLbpLJrT5\noJZjoltGhJpxms34Sxm0hZu4ROA/k2MLeolVlnb2+Zy7Z/ipSMa8TylECnnD\nCzW6\r\n=Qa+Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b3e25e534289c594391b6f9a2ae780758d020464", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-thv9MLPJqBVMePm+zgYH6b8Zy3qEEpew3Z3fbNqhtzPPMtJ6JaIrEl3xs7tp3YI/vuvWIpSS/6/usTTE8+0AXQ==", "signatures": [{"sig": "MEUCIQC2BxVB5bJG2bWpcBlIogXQcXXON5JUK4hqw34dzQTSoQIgO2glYSxHUFRFpe7zpDQKlbdodb357rfHSt3z6ORgD2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WnECRA9TVsSAnZWagAAfBQQAKOy/+LWVt0+71z+KV9M\nnXYCXsOsiFoo5oXFC71Q3v6pN7e1snyl9poTOzSIcqXaxYfYMlJHCxr9B58I\n+K2YVe+j/lSofA41QPzyAghBXj9vDWNI0zhzlgfNrCZX3RwqDgtZGOaExUpk\nSTdgQiEQktYfEXY5gMz9OtTusHnOeUwfQ4r+De4iINV1vCZ1Z7Hnhm6/Asl/\nFe49AncsgPeit4uP9CHA+dikSKQsClhiuXyc3mLPoYV/BkjBkc2heCtU8e1T\novQ25+NAeQ1A0Cal1z97L7ztuKXT9Pnc0Lc/BisgZLkj0GOEmSfUG1iIeekk\nYTRSv08qL1RXIF0iAVExsU2HDxfXWoVg1UApydfvaxvpX3U8ZqlEOUXruog+\n7dlcH1am/cxd1cQVXuJmIZhSGofrI2Q4xCVMpF5GnzwvXoovwqfEYX4Cot/k\n+aJUlwkRn8zNsS4XK5gofa9O5A2rsRvWC/Ei3/lLSm2lxrXAMZuAf38lT8mg\njNJjKo/aLjjAvEsgzltjVG69xxkfWk4Nu7pxiwA9kyW9zX1qo4+qkY+fhEBx\ngRqNrto/91aUYF9TjiTEJit3AYwl3MBAlmzYze7QG9uqqLFtAC/3/1N7+cb1\nzLTxk/L4c2R5g5MyglEDAE0ILf8M3K0yYCg96iunQQxjTnKjecNZ6bSS5ot/\n5TFu\r\n=JbkY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e6edda7fb1308a3abd0a74f5a7a0956310916260", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-JXVnJlKlbELcpaDCJHEhb3RV/I4GgSTg42Uh3N6uxBeS6sp9ay376DubwQooJYfFUkylUjggfG2jyPpJHc0FEg==", "signatures": [{"sig": "MEYCIQDHltfvxpB+cVEtRIXHF3/WhQqMAePB/rfhAIM19fP5SwIhAJUfjepwvtbwBGabsQhN9RloiiFlnlQHzf2k0Ksh3fkB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTeCRA9TVsSAnZWagAAGysP/j8CawdtMViqxbEfPAF9\n3pktt50TM1T9PIMa+e30NZYMFzNH8+lPtgArmFNrEnQkxvI10N8y6xIqMkyb\nGO9G+1BeprHhcDuxMf4erNel1uBtcJp2TbfH2puW6mMbnVMXkiRphSubvSPX\nRVmFMpEAjANRQbV+sPIh9jDKnJmCWNWWUoUywCxbrm4z/z5+FVNVVd4bOp2W\npg6XB2p8c90i3X6lal4jG6WaDzgH/VSSE9W7PlOM5L0iQfs7oDgairNpGxFn\no2LIAAMvrNs43VQ/hNqwsUCrJxcAnzegrrf4Hsxy+byEfVo5k9iBp5GHtRqG\nuCHItJH4wEeOIurhaspHH8Ac5pEXlLefVvAEq427ekLX3Lm6hZ49rc7II9np\njVPWeumVopFOrl/jyDCsV1ZzrSvqHPaJe21Fr/CXRYFfyRhh8nbKV1/wmIo3\nvH44cl2MsMK08rQZnZhT5poeOeUwnXlN9F5H2RlLrEkTzXgCND3MrPiTSxEF\nOKdoMJKWqZl+O7q2jDttXkMOkgRdyfaoHfjCuJoQygt9dySbQaxnIN7n8loS\noM+aB5FA2/z1VzFF4ansIXAM/etkfT36M4cYtoyQgrUVojG+fbrSYg/73wZc\n1+RP7HDkn/GPnqskUKaKU4BzfhT5AMrrgYpsh75IP/RAXo5QthxWT4s7VWt/\ntF4s\r\n=AeZr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "41f56a042641bd7fe225b1ae2dd0242e3e2a1bb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-Crefu+KuNhh5oYQDvXdU0KrE6TkmqqtAOdMD/QjgMLmQEIALZnw4w0gjDmUabKV1K7eUrimiJvbhO6Hnnje2+Q==", "signatures": [{"sig": "MEQCIAfLYDKiWaOccuU35D++Uuh+d9FlstW2ejVej6a0/zgzAiASAxc2a+NzsUrHDly/ekvgn1p6WUJS69f0X/uj9O+etg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/mlCRA9TVsSAnZWagAAoHMP/RYfsdQmoqGubwd24yO3\nWj5cQZh8qgy+jIEVUeaqbwbhrVJBvSmwuLeoyaNYzFUEJtU434TYk1VKSMCY\nLQuyhCog2BEdFOUmZvqXDhPY+oiY/Kse5mUy1w0IVaBr64VR0d8aZhV0gUjt\nhKeNtvFRSOA5NSWafRJNcEHdhjkOMtVPotTDEhMVuuGXdlZM83jYQnykaone\nWCTKIQSynWP9QrOXpCMSBQUgFGg3g1vgfffrIYr1BnCtYvkXxpx5ZMepEikt\ndybmDX93na+Jne6zLSDI6DiLFd/kCTeHMxDnvYeiSkxfpBJM8KUCvlJlPIg+\nCta9g8NnSXUT/LNjwqbrS2YriHYytnw/jKfdpSodMoAY3ZvoYHEeUjDfYolO\nAC/e2UQ12IBDby5ie2OWkLjqmNPBeByhqlSOUJKZq82Ome5tldNP/lG96eOP\nIveC8rfEhe/gpN1d6oWcmWvobuABddiwgjlP37s09APkQEYzdMl7gkhbDPhO\nYummUNUajWwC4RfDI4lBKpw1QGBjzlvM1KNxhumeV25LFQI1W/jPm0W7Lz1O\ncsT/XrRisRZvAubbclfc5w2rW8PcFy2yzVDM7Ui7XwAWI0iwA4G56C/ozvx8\nH5O8ETF2Ba3+ytYltLPrlDmHjBuAr2NEM1C3X2Krq3IsxHvOuveuXuNr4g+b\nqqCy\r\n=s7Ln\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5a3e65cc6ef324badc40a9227977ced3c59ef55f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>qOLaZ0AhOI7SKMT6KeKEzMWgCSJNOsbVoxmZqSxcVEqPpN4cNTQz8nbukl8PRhddPGRnA3aK9bV6VmAgQ==", "signatures": [{"sig": "MEQCICHWk9tgY62Fmqs5sPStzEn59hCGvFFkUsvRuuyxz1fUAiAqcQ8RrWdtlJr1AmDIZ+7UG8DE6bfxwQia7L74TFQ73w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBG/CRA9TVsSAnZWagAA3S0QAJvwGcrodPk9OxiDr/Kd\nsLQSIUJD8g/5jiY29UydVftrel8llItj/ZXSN6gKrnUjXrx446VihZi+ymRR\nJwHILSxOl/H3C7gWJ5nYLldm4cWeHB6q3d1x5xT7uPuy0jfFzWFJuNpwejOK\nbB+vWQ6cQXIGJ+k6U7E+4PkTnTpKc92e0dvmi++o0HfRA+TLVsfMir/12/6c\nkaAf4RPJcziX6Dg0hsQx1rU6Cnm2dONqesvRuRrLZCQ31dhHU4gRD3tW05R8\n8Ewdx3w/qNeVyETeA2mNvmmZBrvjfo/bK1lwctrrhTL2csInqwpvhMFQKCNL\nzzS7pG9Qbn1PftwebKdkJkS/HLjt6KFO7L5nNoyhML7Fv2o2fYC2kY+nxTR5\nzP2yHpC97frGmZ2E5hvolvIHUFG0P02jlmGI+RU77IzA+yaetjHP/tO0vTBE\nC6lpjm4b1uam6iy4wVcffGLpMlCxDEOp2JREugSk0I3rXxW+CtA7MzRY0FFA\nKo8DvWkHknKUzVNcBZ4IyN+5gkkxVOxDZA7WeJuWz/fFxRom58dWGhbW/oLy\ntCAB/scym0Ds8TgLKTHOZIC30mIST4J7j4QjJQo45rgSxtKDOlrUDX2HT9Gb\nig0kBjT+kydGl9Wr1J3Ol1QuL+pSlVo0y38OKj1CCoHzM/4DOZjB5X6aqjL+\nb/xj\r\n=A+ii\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad7260562913f8bf3df70748adaa4e0460d8d2a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Ib80q+GMvmYMjC+NvyOrQb9qLWB2sET1rDqr7mXIYudTTnk+yGJZjelSp5GLdCJ96Qc4Zt1pHckQuRrBpKvn/w==", "signatures": [{"sig": "MEQCIFDtC8hjIAPPqSgFauY4kAsoaYuaB4ZZwK6kje9+l0zBAiAmeFgQ5jeBIkTKjWzSKQkCneXQ01Cpv5Ng6rdvrCu71A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBXHCRA9TVsSAnZWagAA70AQAJyqYMRwaQJLASc1+l3/\nTah4HOR2Nkqn02bR6JD7d/qznw9dNJmAdI9k4VdnE+8+cc72Hq2qN/t4/wku\nkmxTSyfuZATPcwolFIo/v9z+YlS6njtJwY6jxn8NB49bCpRvw+4aWxZNHtbX\neE+7LXZgLktoHluMfc5LmmHaIdUXjvwSgvCAZv5FNbbGffChkMj3QYdBuQAz\nXgNMqAyvBTwKjiJ99TlHDSkcGlEwldnMP6Djd9n/HvRtygsLWAP0XOsSyZlU\nh/GHsvRuR+S7zjt8E/rxeSa6+gHGmp1GuSxNftbX9vS0LiaWbWKTo6SbDvnB\nK9RzwcVXmdcH9Th5eC/A8ujIoNE6mAgs9eymDqnKyYW6Aimh/3Zuy1/ybJU3\nN1IfE7PsEsfUBSuQGx5ZHnjaicx55shgd5s9aRPcA5eCyn/7XpP3sYXBuXfo\nhICuVFmOlfBkpzeqUQgH3zn6myEFUYOvdqPky9sa3bBGykn+5WwA+JxQjSWt\nDvNRb8jKnvdiktXv8+Em4HO23luDz99E7kQVvhFlX5HHDA8OqjEWB4Cf6Pf1\nPkYwchlLKUfzuqtFRH9RzZxJ8VxGRV1uM9AFVkF2ADW1JnK9ssz8uT1AYhjL\nI8/BmIXg/EKy6RtaFCBZ5kYDsQ6Tgt3IARd0dC5meWz44cO9owUZmyP0Fym4\nbzwt\r\n=9kBI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b4efb3059f42b439da861080001547ff470cff43", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-Z8K+ydBKvamErHnsP2M+bw6w+u4l2oZtxwIa2pgwjqjeiw3XrQ3cVQ8BvgJSXXUBFdcCKjsPBPJZvGQPW0+O+g==", "signatures": [{"sig": "MEQCIEcg0PrV258DxYhANW7SgKcpoE30HSKeX7GMNf+bFZjuAiAOr61ksVNIDvEZH0N43VhuITR2DfvGh1gvlhblGszQ9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlk1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6XQ//ZbOMKNHKe+R37BB1OOmq/5e0ICHOOmJPixDFWW4cgncaliKf\r\nPwhB7y86tK6YMR6ROepeM0uhwwLc65dmjxkWbO4VdvZWz7SUpq1nXlgvP9aR\r\n7+khGOY8zgLEGhVgpiFhRtT3Ji1PwT4QgoSiBDf6ATaydB2jNqew63lGaAhe\r\nE9DuzocdjTbu7lnWbmaejaoxT1RgUFeRyO1MVHgTQLMpCB5M7qJOcnSN7Osa\r\nx/cJ3ZI9EBrtsLNPO7qqowCcDIO97/amOVkxv8OoIRjIZToT6ijrKbkxFZZc\r\nYBRQhU1elE0uepxDjcJtkhsHrTX1vV210ajo8u2o9+efBBrA1awtvmU9wkqt\r\n1a9IdNYkxg8gdG9PaWioMwgpn0RwaSMuHYwU7BghqqXywkxru/HuHI0TiWC4\r\n1oEikxeP7cYr9K4qdoQSSFI4pd3QsMJJF2yEmmKiS2YuH8Yvoo/oM+jZNgnQ\r\nyzxtAsX239Y27VObd00Gv49aJe+CPYTVKAkruH5e1hYpXZTQWSwA/IK2pnhp\r\nzLq7QlXXa3lmY40AkyN03goSFIGG27q76Vrrj0wjnHhwukVkqkP4OaB8BigI\r\nyxfRyuZWmjYbJzafNbOijm+KPkIDL2eQYHaqKWe9PkegsMEijz8smegzFghY\r\nmzGUwgzyqoY85VKkVKFBIjL1sky+jGNviSs=\r\n=0rnj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3828d6cca8ca5cba5d73c0112ba695b53e2f2359", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-2Jrf5qGjR94qiS8TnhAvf8L7iLoC2EpUiWran9EHTC1jSr0I6+aLDRgWWxzBU9dH6R5Uhp4yyxH6fzLwUI7Gtg==", "signatures": [{"sig": "MEUCIA6S9whAW7K5DT6/gpm/jA3gPiipgvS+Ms93xmR6FtLlAiEAypWMDAUqhiFhanzx73dn2Ic7h3OvUouGmLocx6F7S5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo83A/+IUmsm8bzhgri3ihXftSNBA3YFC4jpwraboy4qQsvyjz+pQmw\r\nSlCWJmKLc7KT8fNIS7r4N4r/WBLoAUylrXdCsTtAzh1Uf/r3QxLvquSB0jF3\r\nqMq7ZDPFacNxkCu096FCcgFnaOHyH9uwHD+kQLNiVlWyan9mzbuRWOa/+vby\r\nOHsxKpiZd5DxaRcDrUTKVoQz+SsiUBcbXDrRbGia8dP1Gzy6XifTPUK8aRQh\r\nISR0o4l4OtJb6+OfjeHe+OIiad2O8B4lxF6+ptF71Ac/nr+VsgrENcBkILDm\r\nqyLxJEq9h8Qo+i4fcR5gEqp8TM/YyaK6HJTlcVjpomMG0/4/Y21y6Hov6o4e\r\nOJmb43ifYhSNCzZ63YYtEEgHX7ceVYHVad6LVlR7RoJUVppPykGMCxv9IRVH\r\nnCGTeF54gTL8WxULwjoU3cp4gJNdMb6ZGeg4yZ0+YUeo5oW1tAB9CKBEHkQb\r\neXlCT/GRUBkmCTxXiCT+8S+wp6Kn3SCujNMj0hRrBQA2xlF4LAt1AGmTrDCY\r\nTtcn72VObGGTPFt73ciVr34+j3bps/Ii1xzc6qop3/MDHL/zKhDKjwGe6+tp\r\n0poWavFTtVPjQmuD1A15gUIEswP9JrSIgxecOnNT58ravnFVv4FEj1tsnqUf\r\n8dsCyRpgnzhs095YsZFmONnbDdQSkSA8Mes=\r\n=a+5g\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0076e4e97ae2bad385428d4b1d7a837963630a5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-3IF1GRiwL+9HuWaL9uSR242M7eXHveuTd0PwrAOp9gGyZZ3ks3+0urCJUGJsdSkPyIvxv0OngaC8fFZjfAz5Og==", "signatures": [{"sig": "MEUCIGZEA/+sD7zw9WOB/FfEzAJjRY0i+HonjKhdqJkbN4SAAiEAiaNFtDTYw7DPjrs40AvGFgBWZW8RyYCUmrAFQGXP1rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoewRAAhQ5jjLiL7w1Hi3SISda0F+WoC/eYD2XP4Zv/Ox4pVIGzUgSf\r\nkwghrm3n5SYuLTM+PQZ0YMkAo8Jqyq4aDA3H2WuqlsBSzl4LhMzajFTad23z\r\nTt6ZgdIAiz7pJC4WEFd9ppr5XQT4Xy9GExSFFPMsmdHYGAzjZkiRberFefFU\r\nzE55eNZjRvpp8/CrKkBwuatSNgw14mGWJ+Yqx92IqNQIof3Y95RUKHFWEHsz\r\nM3ILibTNKosYnDsHw59DcwFk56/xJxx3dzRc7tPaCBy5RUUGZpnUoMCy3zUL\r\nyDeckIMTAI0AwU0nyIv7bui0Xh6TbGGPPGrmyOjnsN+OuwOIMwyCy8+tr/7P\r\n2MP/AU0JjAX7GEHlwa/hhIF6EbeLFh0RGtqlGvINoF1s3nKvf5gH74ZAbULT\r\nM4u7rl1MRdDlZUtUDVq5zM3PuBRIJbK7cwovPSuIAwJHqTPWiesMRUnoAYGZ\r\nc2yxozb2ajKm1EmiAP8jTkG/J2SJbUHDI9e2aegQD2V//E6my9QC/B/yAq+V\r\nf3U/2FCYF0ZLiJZBOnjAEjzxSFtNcL5iMmD/Ctt6z8F8GN7IlbY4okZ0pRPT\r\ni4n6n7QuBIHVFxJh5IxOCczBYIc+l2LhvDzhdaE8aw7N2TkMy289JzhmZFmc\r\nd5LCFFoE7NigPjfrhEgK3KOheEm4uQ7eqds=\r\n=0+/H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "89b98d3516718c67411187cdf3c025b61148bbc5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-8YM/e3lllHFoA/DnYHWjoL20GvgiMym56G4N93RRQGwP80jb7+I4fulwlLDqWP23y7MtVBJtQs+tSVMujol+EA==", "signatures": [{"sig": "MEUCID0642JVYhWX5GiE+Kp3PxQgRJ6CbCiOS/T+bBmGiSwQAiEAt0o0ZU3sFMgO2VC5m94Xg4G8WAgcTVk7TLtjrNlOjjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/LQ//dYEyRIM0UGkEBCCeRo+LhR3OID6llyaDCHQntMXUyf8YyXhY\r\nXtE3z3yYZbZazK8/VIPAH01NPU4YjB5DBTmSiFe84e8UOUusq8hTMR6e4PHl\r\n6YHXAbV5EyuTPUPPVjMmpA0W4ZwhDxQltdWBrjDmwxo9DEh3mrvSl2vZCWgo\r\nPDLePnA9eH19pW4CVyBB503P6P3+f724GG0itTQ98jZCqFpdmTxCaYK4WC8t\r\nJpEpamQcj3zuKSpMux78c+fxL8RWOKqI8gSxHKbgwspDGixcPRdejRHDQSUO\r\nbkOgjyRgrtnCd/JxbZ9DYpyCRjkm8QHoIik4QyyZmHIvXHYsW17xE7cDt+IL\r\nOVVTf0fwpck0dX4DDsLAiTm1ZMf4VDmKGgJBnWQd9CInlaYw3qCu9dEybRgU\r\nxxkeBhotdjzeQMajHLA9w9Ekgp8WOzQOehX4usH64clWKV1KD6XJJL5mPOhG\r\ntzG6DfZgVYfqxMp0800hjCilF6t1WFIfVOV2JhJApcc+1eMwKC4FuEcFupcO\r\ntNrYgU6pbC1YUZvw8abuZUruSkpU4hLJwOerRx7AOZvmC1DcUb79lDJo4qJv\r\nTY5dR5Ws2+Bnr+l68/gZjl/0c3wwruZkxQXxzCWc0qOmNtEkOr/zPZE1zvNw\r\n3hSMLttwRRYhi0XnXB/mAMNumJEz1R10o4s=\r\n=QYhn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ba2157ad03363d912c6b85d2ede8757c81849ac2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-LiL1Hykp/ClYVlLtNraV6RmEJ+R3GGk6LuQlULDoREGuHRryfHf5lEUl8er1IGpvg57pLfSIvziIRco3TwFDSA==", "signatures": [{"sig": "MEYCIQDzbN3I9dxqKLUI+c7y1ab0X3G5YLwwO7T/hKGuLAoKQQIhANWRyriXAiLkqF8iexrnSdf3Q3PhS8ojX9aZnXOwzKD0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP8g//bFrKVjNGWkILTWDV5iwoGBtTrmH5lu72+vS+xbcDH9BXQH7o\r\nJXj6mLIfzbzkgnlc35pNv6tEW1OJVcJWWIJJUUlJVe4Y6Hy5zJb840Am/eYw\r\nXXcZu1J7THtaLTN+HENkVcAMQqHiLFafqDT1w7n44piSY2IU+hvPgHf0PrEb\r\n+nVnKBGgXcOSIkVAPMNagZJE7a5Bmww+nG+zWeUFprOfoSTllaAh6DGINddx\r\nYWG+kDZInYYEID90LhpB9s6vAbYDpge12wx8YQbVMGtXKChL81U0HAeR265z\r\nXub/nVWo9rIztIYmMxM+c9qMmPAwxDwBmHON0iKBPcQRxwNT4fpVY/n5v2yo\r\ngxcMUt99rPqDUNLEeL/EEKasQF0enrhTyMEwcGSVv2nIGgtFIsNTWbjqRsCM\r\n8eKpxdNOZJ+Nrn7RgInDSqJW04HQrvYVM8rZ74XI9kvAAPpYdcpowk1lFm2F\r\nYUWBOjgdgzd8pxOrLLl2mBYhmzqvH6m3qf+IFDWZBm/dyLdASyuG20ThEq4f\r\nW3XYyp2Rr3C/qCbe2eHcEzhGeORviR1i0WWASHw9N3HaL87dJhjNl8R3qEbX\r\n54TewqGAmQ2iapmdFHuJcTH0EVx9ruMPNyfr6gwXs/QWYSgxmTiZWlixWHmW\r\n8BJmMePbYDNK4g0bWbN9Xq/oq3sZkoC86PE=\r\n=28Yo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f9880f6a9988fbcd1314bdce27cb7d936c22061d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-ThcovlNdgjUxYXIBaKWM0oLOsiYh1BKd/sVJl7gqOXoJFnH/9nSl0XeI9VCIXtuZEQyk/VTcCxhMISTtsxOoUQ==", "signatures": [{"sig": "MEUCIB0Tu2s6OwpiInR/cNRWYFhjkADxrVDig+7UBoV5oxGTAiEAhjyAQ9cAahS/PXAxzwzD4hPfZlaKREWS5xgl8GORjr0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpU9Q/8DiThVI2TiDOpw/U/XFzzoqoyGb+toIg8eEWdnLUEKWtXqeqr\r\n8eNZxmfsUMqQKEHiwalTE/X1fhErjsYc+y9cH4r646CM8N4cGjxR9wASXHPM\r\n18i46iZD+0PfKi8Mm8UaTpV9gqKO77BdamW1SIxIJFo8DuSbbPBipycXjz5b\r\n7YHleph/R4YDcSmteyzggdHF6DdBl0z2U+XuY22pwjALiNRDNVUACe+MBMBN\r\neMeQ8Rwq8oHlyCokgkazTDqW0janu+GpEcp0LZvgM2qKIfVb2mvHzZ+SZCb5\r\nngCm/+sejcGB9XVq4mpJbA2YpN8OlmagFSDt07cmkInf7CH/6p6mr5cY6NMy\r\nbAd/ypunwGS8UIIh4lvtQHqEzS0mPNHA59XZNJTmqGomgKDeqNmdnl7pTT2W\r\nIIKC/tlQRxF8E4C6Ykqd2zFNVw5EI3zJsyqPERbHh2GFHH/ExBLUGB+/r7ws\r\no/WaUdJpxSWWeJl9yRJD9+0Lg5UY1chP1TnhK2xh9lmF7zk2kDMP8QjX4pXV\r\nYJ0HExXT0jecGknO0iHsSkHWoz27IXuoNVIWhOk5RuuTUPwAwsR0tVwuxS0+\r\nkOSXmVcERkTQjj+wWDpd+w6mKQC1bYTFKLExZv29HLOKE8Jzpb2uZvWdDF9w\r\nU4OBC/QgXojKiSyCdU6b8XFMyj2amgRBwr4=\r\n=kTUG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "34821b40823d9bcebd5552947a60a05e58c04c92", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-PkuG+4qTwfTbuIQYNWRF85/0YrMHuxro5PUUZp5L21bC3tUY/ra/QF+hBaBdj6dHENaYJuurYtyFBE8IREZavA==", "signatures": [{"sig": "MEQCIBZ7JxMqLRjm1mLjc33fG/IcDpAbqCxp9ujijkt/wPQoAiAsb6V+1iroIMK9+ByHa+NAM22hb+BqYUHBdkvUjGOPZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooZw//eXiMwWksVKjT1NK0uxnsP0hzADikTl0theyRvFzD+V+VMyUq\r\nmq5uoVlp2GyUNJRW7+fWGAOkvqZb/NBYXlu/2nvTx2XkUgiKMDB0sQjZSSjJ\r\nTpbTRsv3pL4Eaa/NNM5JVXhLWHprr20EPn0VcnqZJXuuJxGPlGdv/OKUYyT+\r\n91ILol7yVcojj6Cc6XbO1S4gYYtCld5zQztTXIU7eKdrs96TQ0azFwmGKn4O\r\nGDPCJb2uI16qG/ifHEIqc1cyWgJcuZKlxYMzMcIY6RHjS7NGgZOEvaa3StSj\r\nTPpAyBfHu6CUcMzS0ciUdAdcwE2nK1t4PKmTH6o7n+joJbFzvv6HsS6w8EZq\r\ndS4+kJzR978+UuVFXH+GVKh/0uxUG49wfiToJyEDy1poVKE5xfZkxGMFpmhV\r\noPEI6IuQWIrXZyTJFtxgynIo8YQS2oBWS1XOHWKp5zhLS0sZohnCfggtz7gd\r\ne68o7NlSVCdO2kNEYbYeAs4tV+c0Zb9OVyONqHVQxJ1eIdEHM8p3cmV/3q9A\r\nIfrE6hybwMpQTfVd9QysIBoZg4G29Ye/IcyVPX3d8z815P2gp165aKcFQ/xe\r\nzCFvamyNmYpUkst0vdaoQomhlbCj591s+CPionpaExQt18oz5sDwZskPHIsG\r\n0rlgRf6aSpHiBhTyj+6PuKYM8qgC4CtuddU=\r\n=gDaV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ec0f5519a446cf12bd6caa2c00659df98b3efe02", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-/kR9Jx1O05CuTDNVa4B8R2LpqG2KPIottskOElrUHuOISzdzhNUnb17Wf0AfZNZPzEFbUEQpg3LNRR3XvWeatQ==", "signatures": [{"sig": "MEYCIQDJZgPiGpS1atFgqJvP4tp4KygpHyLZRQF4BDV7AQ0crgIhANhy91EGsNlef2BZ9W+/jb1APkTARfupEeTK+pVqT2Wa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBPA/+OHFJbpuRsIh9zthZ5ZnTD5OXDnWZbtYoovAdfn4MLcGkPMw3\r\nn6HPgFsy6TTI2yfckx1OJb2w8sEK7YfJ8QXeHVsHIMpHPnY3bEmVNAizs8b+\r\nlckOvzRnnbYwfH0gXGN0iQmt76GOx3YqBLBGfLt5Uc8VLTjEJfyEypxmSyLj\r\nRTlOP6UybwJQdKll8hclMXHmrwXZdJDNOUpIy2xX/DgDjAlZHOwSKpkeB030\r\n0SdgXVv0kADyZOaG2F8e9kvBewsi4uHNIrzButs9tVxJHOs7SjolclMPxWoe\r\nqSF3MWIMstX31Dig7BQv/n6DGee+YyT9deSEMn7MZv63v3CUbNMaGx7GZFWy\r\nAFyryE/qZXzSKtHcfpLVbw//s8dhmb50fjr4Fik+qaTsrbXYx+pOAYVIjwXI\r\nyVQbrxBKJEL251NWY7IuNmrJRCE9Y8u49Dc/ZVt0PfCiud7SJWwWxH2qzXY5\r\nJHYcREQy4BI8CMdTvBIxv/xPNSo7g7Sx2sCrwtZ5sG/AT7LQfvAAhvo7kSg1\r\nA4xB7d6UNzi/5+p7n5UVmAlkxmN8f0rtSHvKUgjA4d7n+kdC5i0jLrVuGV9r\r\nH6f1irQqdswaeWaIzadoaZgAZk6xocux8zP+Zk8pf0OxHkGGJ++yMtNskffC\r\nOFtA1eB2NxlNjf3l8kjfcrWzVPfjV9dpxdA=\r\n=jAqJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-collection", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fa2950f84774c2e60a771f286100fa140b4d6b3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-hA4xlhaXEriTYgAxPbKqUpTkLEkggFtvSKV9Ir9R8HBRRNevvCnjKoPJFwb7c1tzwoZ2Hg4iUD4uZJ9orIdDag==", "signatures": [{"sig": "MEUCIQCnFngN8LYD8Sck4FOmog+m6ysmBIjN+6iYfehxXYq6QgIgOlcForReZzmhfEHdHWU3HxU9XepvAg85xF4nOu70YZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjzQ//c9nrcwVQES34qg+OYsQijX7g4SFLIgI5jv7CYIwYUknvR8+5\r\n0yGgJeQuBYXQfVk43TkiFpVfYjGNImYVeaS3l6iQTfoVjqbjAWkXANatop8n\r\n4/9SIQG+M2QTL38sjSMSKef2S3yW4goZbBb0AA5zREQFj54T7XwmZQz4Jzfi\r\n3KvTIgJPvsDpuHnriGILlDPMAI7V2W/MncDJnktB65EASpH9rLBOipE5Mwtg\r\nrZEqB8eYBC+LEjBvGKbSFVYopSj2kJ4ptGy1xBiFOce8jIVLOEpBsNwnRN2v\r\nKV6cN43hdC2Xg/OwkPG2EaKf+ENbrzOW4aSU5n/BvWRdFT78Mgpw4bbS6xQr\r\nA/KE5k2g/m/VtnF2vhbXLuO+8ZgwSoccQxJTUXZeyLifFY4eo7eBhWvMtg4N\r\nhwxxI1/0cNUeLHdC3GmOr75Xkn32cLx/IKJJaidCSKQFIb7hfexnPg692zzh\r\nR0xHi/VEHKx6llafSRjVjX7LlYDR3hn0gQxW1/SuI8X6qFVW4fMGOp2lfHnR\r\nRasep+wfMWteeqotEgYmyNnQ0h6CQaJEVbEGu7LuxI0OzbbuKxck0DUYCcsY\r\nK3ngF3YZpHIDYySH0gMbVz+cndAZ+je0BY/b3xbYSq3NGcTVfZJ4J0gYt0hf\r\nWV7y2TVcl5a+miTURI5sq9nUotBdF/fjNEc=\r\n=5zdS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-collection", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "734061ffd5bb93e88889d49b87391a73a63824c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-3muGI15IdgaDFjOcO7xX8a35HQRBRF6LH9pS6UCeZeRmbslkVeHyJRQr2rzICBUoX7zgIA0kXyMDbpQnJGyJTA==", "signatures": [{"sig": "MEYCIQCDkUQnKkfAxGILlwbu9eL2eFCbpYzSKDFI5LcdSi5XngIhAJtOKX5UQKDDXWiCMPMMmDDFYR6nWExUiJ8hnh3ZTZCF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMmw//f+I0RhyTz3iBbFiyJi4lVQ0M2Hvh1d7tmGywrI4hOSOJSipO\r\nziiyfFoLhq+cDlajug5y7wC06V93Q1Cfeok9e/QivjISELsnmcoisNiwDmRp\r\nHXbIRBU2eM1Uyylyhqb7AYVb/0Vl0sdP1WzvDD4HZ+VtfmZp737QXRKPqdMQ\r\ngjKBpGfO2gM1qhvNUmk1BpLmL1yfJILR+724LXJE30b3zoCxEDoCUZqZC3Gk\r\nQkMGSHNbvWdKfqCqChDLwfrKGDg4rli9k+otNdUcazP6Q+KlgXjjA6g89wPW\r\nvSr+mMR+3QuyuI9oc7zJlBS43QQIiiRcHc+mo0ULE+mLiglEUUwnlUTHUgZV\r\nQgz4z6iK14J5fWS3AovCYP3lhOmRM5SXq15816wYCH/1i9p3W1mxb3MPilYE\r\nbMBaOp5ZB8+BNoiqpO37wCJxTZpvYpeE5t+nWnGJxI9lOF2PACB4OzfHzmoV\r\nXiqIBsIOp4a2bMvXZq33LQUpg7VTq7S3GLD9NajC3ecF+z2qaGg/WyVzKdl8\r\nuEVALEmIXA8lRxg+myX/cjo/u/bpwzmb1IH80HkfmJwM9hDT44S5d2XhwZVX\r\n33CXPMLVSPLGjYciQLiIiXba0AObPeXRY/2CNJmi3W/4wrDYbtCFFRcgy9py\r\njlrfZeiNHiBnxgWNwhun5H/3qVfwBzoAuJ8=\r\n=00wb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "157699ce0a54235a1ede1b7876d7a9fda2c282a4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ICR1GguoAVnrHpfJEanifnmIRuUulLqAfOSrNyIjoLycfgscHlqbEZl3AO0ycrqELSTHtYAB7jTQ+ybVewqlPA==", "signatures": [{"sig": "MEUCIQD5wc2927G+ozgED3Ypn6qxEfXKmP3HSytToQxxWtglbQIgeluCYCKiEVO3rOw14M6xhKJ/NDbWGuXRmlgQBd/Qaks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIDg/9HJ4XIwJRP2OPUJ+J+3glSeGoZyrcdssC/uJajMl5DjdwO8WZ\r\nhtAlZFu6YUbwH0mmbwbYe8wt3+C4vI4Efw6SxBVJYqajlwbqdEUyPOhQm6QS\r\nL0K+YMilAyafdEJbvmhQuExyqaZBi7QX0n6kcpEMwUVhxlj1/4A5g7jSmKWb\r\nzEnNMAHkuJgPxFDIVXxBj/hJ+ntDpN6cIq5W0yMWO7RcvLZv+xKnUCY377b9\r\nOtIvs2cW+WjOqta87hOqgC8RHOU2MQ2U+sqKi1nNJ/liakPf3V6lI3TkwCrm\r\nbW0xoBzjUIgbkz9bq9c6RnafVFEK3/eDdyBggcFVIEfnwiOIQM/1j0AD8b33\r\nQY2TVwME3eg/u1eeqLoaOG8e8FRmA1vpmmauzlgqtXCSRBN8bmVEqvlF7Gl+\r\nmAFS8IndivVnOjGUNf9789d4xu2F75TtoDJBhxlnBmpQn7QyJ01USfbNZVP9\r\nlUurAPnAHo+QXbQZispVXwg6rebykaNkN8HOMVIgekWagz0rANSFZg9wfYDo\r\nBL64F5eCvmZPExbPKPoW1QSAq8glxLCbzebHXhvICL+5yX/hkKaPzk6OsUZ5\r\nNUaqQNxzNOYmnuC/Pc92lox/JdCCyu3kU+OtYy51Wdo1q+Vt3HrxY9c+zvhB\r\nDXBWi/lL+LEqNt1Oux6WFZOkuTxq9/hB3Qs=\r\n=VAoy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "66a85478a885da584a6f087e1aee981d808ba10a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-PssGRUChrGSutRSFBu9wA/pGABZMkdCYWeJi6hIpAXghFE1YVk/tzWdRBKk7dyWeE2po9lpGA8MMs8qdzJ4A4w==", "signatures": [{"sig": "MEUCIFbU24I5uHp406C4qaiRmEtWmd79GxDifs7/how7Hp7SAiEAnA3wz/l432smmmNi60tmjUzaFZhxpAQXQri0y/FRXQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20970, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAIQ/9HVgBK0D1j2YyPa6lQXd4z7CNH7sDVHulGqqEU8wo95gNniSh\r\nuqY9cSTuDShk5TWJCbEbRMXOXjMCC5+OfevauDNHzbnjeJdJ+qdHcBtO28FN\r\n8Rd3V3u4jcVTo8QF9y3/fhsMuQA/4p5xQXw/lJGnkmzxx/Q7Fd7Tqnty3X30\r\n6KP+kol4uelWXMix0dWnZQS+Stc+ij9aR1w/qJIBPFULg/a2KwiBiXvLvwVJ\r\nIqScoRQ/aNKNh9wINuTAycXlXM0ZrZpShpRwpbk2omjK+Knp+GWQWYZaSL0b\r\nzP+80fi9a7eLjy0fll7MGLyUVgzmQwX0V8iN11JMSD6I/j3f4t16R9dqclDo\r\nXMJ9ts56tzmW8TNXroc6n4MmTRLR/5tuYoxInQnRAb77uDnmYuoXtEVk4IpY\r\nDo8sxQjc3+SPNxPSyQ31o77uWIGSxEY1bdsTrS1GopC8n3rTfWvndxH8ApzS\r\n5f4A3RyUYsDq3vBDQDA+R33lP0AUCC/h6ttC8V2wYmydpolIgbSafyF1zWU9\r\nK/GMo4Dd8EIvoGQ0IvHBQubXm6PyBTfIB76tjNQ1MKW0hyenIpy17zZUHKDm\r\nqgd8k8k58xqObZxBu2jAgSb8UQ8WgdqYM57DiagbgUEzEMWmJgAOR95LUMM7\r\nOSXFHwVmPLZ67vC3n9omyaYIJDbxjkErbJs=\r\n=x+Ng\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42d99ec20575ba4aeded2e0cfaa6ce0dc3eac82c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-0YInU2YrZ+/jw1RyafpxVNNFlEnnk1TIBU8/c4zS+4AK/++XMX7ZMcNwAuQhdnZfAxyPBeP1w8ikWEC1aWOCLQ==", "signatures": [{"sig": "MEUCIQCCfxwD4itwHWtWRc9gGBCcsr12B3F/d+n7sdvW9Edb5wIgFM9cMiRMKSLYgOmnzJX/XyI0r9UE+3evIF2RjSiLz1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXahAAmS28s5QCtW8EHSUCgIe+jnz28rIysDvDUyH2ln0L3iTdUIDE\r\n0pPskUJlzr5pa+H+/XeJhAKNTy7owF4BV50UKzo1rRpnzZOzPnhJxs03dIPg\r\no0AHmyzh6Aw3MuApGdrj04dySYls/aLikBetmpO2HcRXYHnYqRydztZR5Dwv\r\nLrE/9TwWwNPny8BFweXN9MDtWhOkoXhwfdI+CtwyCtO8ayWYULswPvVLQeA9\r\nLtTxpjul12doo9GXgIQE3eyXBfzEx6RGGtoCVzh+IYoyrQEjqPtS+b1FF8H+\r\nGfBVNaC1atbRkPV13xAoGlEnQpu2u1B4ROuQrJ1rTobrwLCW8OobLl2xgICI\r\nyPGdVbIYqF9W7dvocbqqOAiyV7jm3mmiGx1DPxWpxGrHNFECXQjZ+q86Uwdz\r\ncmZNA1AUyQqNwB7ZPAaGlKKC/wtfKAbvp5tabugSbDN1qpD5LprZWbuO1ntf\r\nSR5nE+t5WJD4P+7aIQT3OolVH2M1HmPqPaBbl2UYfXJI4NPhgsflua/lkQZq\r\n7NNRt6JoTz4mOR9eUc88D+wscz5PGqhxcA2X0S8HMaumijf+vctSbBpEYPdc\r\nHj9hixeNeUcYGvXsC+PCppwwIeNooMALXIVk3o9P8KdR8IHI1MLHmxdn/lmJ\r\nqcUjdZZf5xrCc0YZt55uBRZ48vXAjMxLz74=\r\n=1JrN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07fc5ec21b9341950c6d0e0829e65ba483ba2b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-2/GqAfYXKWUk95zcDcS2tMowvxdAZF2JSnTf0KwOlovXzqFXP/XT4ge+I645+f04Ev/5PWPaCgi7dnwEDt+eew==", "signatures": [{"sig": "MEUCIQC5aZsWY1kKLeMdhXxoUNVftJHUWeBzB8ojPy4JpeI+xAIgZvBUu41VwkSjWvHW3taspiRvk/vOYd+BxakxSdOLpHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+Xg//SLDrRIyZH8WMxBstutQfrA6lZ2USXd3W9xKZOKBR8Y9QHA3s\r\nBle0FsDDeen/fmYD59Lw04rhwbbm9B+bSngRmaKUUAscJGbTISgmfZ9TKHXC\r\n/Na9cr/gbErGOqitbT8+Iv9CfS2YME0OPMNp5scndh/0989TvQLf26PKOyXM\r\n76Xe52CZWjPoLQ0W3zn6CJ3XKoHhohU3/NMybDp+UCtvQE7L1JLZYGuktBaz\r\nXNihA383dbkzdWD8lMF3t2h3zYzJ39/LaAi/Y/EoSLnD9VSysfpVWJyJzz1e\r\nRIaVH5Mdzz0QDFkK2ztcfrdxSXa65EJDN9iDqBxtMrjG756B32h75OGUfxoT\r\nbcZ5/WZQ8b8r6Y4/ct/ck891svi49QMIK/ijh8odV9yFDrxPj8ssmjP9BoDr\r\nU961pau0s4PWbAMzeFXEQmUFWVTnmPwUPlZPCmZEvpoT9jVHOZXSfp8+/pdJ\r\n+VoF72Mty3QAeRer24RE5xNUu3Zwuk2V6Eb8a2t61COiSG7idrKKLWZ35qvk\r\nxh5LfBDuo9hz7ELe+n9KlBcb3lyZFilgPenj8LoE+/g3vSA4Kg34k5bAA0mo\r\nY+qaKnWjIJH2ZdvemEbwxqzPXA2P+55ez0XvPnEQVn1muI9Li1ullD5WxQWe\r\n8rC4HfL09Q/uHu9vjx5r0dTEqiEXNCoQmhM=\r\n=7iwU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e97f0e5ca269ad2eba98ff365cfc5fa0c552a6df", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-jWMYFFtDgX4skp1Cz3/QTeS4YGjRsRh5rzXrLutFuZWqch0KINKZ6UQzUTwWedkKIHPeSvRbGzaKzA7Lg/0oRA==", "signatures": [{"sig": "MEYCIQDeZBQG8Z30raXANs/Wwfr/DgG6lCOZZd6xIg6gwIXwKQIhAKKlBfec+EYv9hFXaKiJhGMqsnFbUiinblYGS94NNrJR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsfQ/+P8OMNZEszKO8H/21Z2ZaPQeEBFQpMR65Jrkv7KtgmfGoYi7t\r\nnlwSZkx5kLRBCDc8RNVqflwfjuMA6Vc/tFQpzjbjKbPEvJ/jH4kJ211u1VT6\r\ne0QQMnPooGQtVHdEVJTJP+ms6VDUp/WoTLoEGOwI+jngxsXoz+xb09XGMmvv\r\ntKOBLQCvZ6HURBZkrhJbTCTxPa2I8ya+ZzobZATdx5kCxbkFf73Dffs4jMUy\r\n2t4crYG4UaINYS+7HomuNcD5b9lWzKjyimjE6yPQQWf4EtjBn3iNK58XTTK8\r\nJezdTBHSUkP2C5X0tk+IvJW/MG0sPbS6NH2Edyefrlyy8dhL5Bf0IXNo3Dc0\r\n0O7xAeOfaTzRoaOQj59X/LwTNjVMF19p6YWLbyYB/zhLJ6h3iQnUQh5a6G8z\r\nx2DZbPMYwRuuPzciJZpxq16dtgJkKcISKI8V5CYQCyDEr1cvMsP3FBUVHwog\r\nZeOkwpoAYhzrYe76s6g0ZPUU8P2nqVkAFRLqjo3Ts/zKloTlPEN7beBpvsMz\r\n9KKsVgN3Nu5KwxFyb4AcuSI1BrUJ7hhX+8mVBj8smy4zo9l/3ngzcc9fzlsj\r\nTNXkJnn3Ca2oipZrRRk5oJRfM+iLyLScBLqdRl76DB58eygJtB+LS3jXRI4J\r\nV/OzSwDLJCiY8mwED5/hqKD25kGswCiYUUw=\r\n=N7ic\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-tvh71sWGGphh1DtjrVtNVhQFdLJ5z7ed9h3JV0iwlBy3NtQN9HKaP5bxmcHKvFpSVI7tWbaga3PpHRmS48+lwg==", "signatures": [{"sig": "MEQCIFf+hVmd0pINYEVn8izTc1SmT+av1nSyGvvdTu2lUHq2AiA/7KB5Ar7F+7OWcgFuSKiYNMzi2TuJ1pMoNGVU9YmFKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKGg/9EuCHmAyQddNv1alqChe2nhnYspBxNM08Nq3/UTeCJHk6VIrO\r\nH9EmZBS8LNRd26OIu72d0rqVMEFNxTkMt5cYCOZErjR7h1NVsMqHZSzf2Wi+\r\nILGCnHTRj0NV1ZzghN9+Wh/Jfg2Vkq/C5nIycuu4EP+pZG6m0rjFck+0FjF9\r\n0BymszTv5lp3JF/w/tEAaaJoVw/jQWqjwNB5LDDwwRQTQkKhMpq505RjkgPb\r\nFTnPDENrdDBfSHCgTtscPfuOQdd+5M6/qToQph7kv5GAmrVsW0RrLxb4sDzW\r\ndPyrpK0aPty7UZ4hd1/wr4hqyTnuP8Ow6eJBJvFNYeLiQudwLjZJm5bCe7G3\r\nHkJaraHIZ6qZCXLjtLfoYOVEK4xsvRbPjbnmJC7qd9onlBcJIsTSoQKZ9z25\r\n6l4NTLetkeI+p/kDsax1nJna6ttwNeA0cfLPjx5tUV99XkUDwQCvJTeXMFAO\r\npfl638GHHT+Y4zprOHc+pgNDAjiBOMJGWW1qEPqEaQVx2ETr9KZaohxMyer9\r\nTWmlKK8cQodIr9usCSfHUidlmONZnHphCiQSIsqXGd8y/LvX1ANlXFuTd0SP\r\njqRlkkEkQBfgPq5B7UDNDMjV3knVM1HfYzLZR8CYYtA8YtHxS1HX+CZ367p7\r\n8F05QZckHQokQjRvcIAIKfucjP3N6ySrVK0=\r\n=Nysq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "796cfab9439430f0ac85560da89611e1dcbfa5fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Ag5RC6k4UUP8Yfmx0iTJdfe24kJ1B/HLN+b/Ss0IRniPBAYKdROgEVXzkkFpPMIPK9oxEKAZ0FFBqr+MZL9Ckg==", "signatures": [{"sig": "MEYCIQCHjHHx+pg8NAHn5VqxOeBcZw1GAIFYfJSmaTYtuJKS2QIhAK8LEh0F6sMMGefCA0WzQ3iMPVTOlvIWdlihRc25TfYp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhSxAAhqOmUJ4ZIRlTnA6BpR+m7SH5P54EQN5wVCdh43t8lw+fZoSu\r\np17Rp/x6t4SzOhveNLgUVineGF/pYBtLs4HajxCjQremLGxVXT/mjBs5T043\r\n01xNOf5KfYELVZDwM53b3+ikctxcE7sLMMDJVCdcSY1YgtMOsOhESZ8h6B8d\r\nYj7u+UMtVyjbVIk4359GymUfUMrxv7NASG0GmtkN0INVEMzZexb2+IoSTr68\r\nbZ8F5/eHVDUxZMV/bIXlMMwb8gjnr/MODYDobXCG5oV+I1wwSxzQYPPjjZ4i\r\nPSeHX2cKHK8YNzl9O7b3hXZ0b+KX/ak2UOdObs4GJrPRLBfGUedmFkjbkjLi\r\nT76k8E26NLH1iF4P2yb+dlx5Rgr10tx3/0+HU91KlKplT2aGS4E4Hp0B/JvY\r\nCGOb3ThFYN7U9XTr8NU/0YSPBjK/OyMsuu4spebM/R4Zui7u8njBcAy0jsqm\r\nBmo/sfWdIxYGMEcuogvNIoxqTXlTf4IQ5nk3TXFtwjYCpU8EVQutRYfswfRs\r\n7VbgS1iAa6hyjxQKsCO18gf/PgJZy2+k20bRQgm7gh4FysCtCsobMf3BnUIb\r\nEtChiLhvG3TL4nf65n4nY2gRvi/VoQv6iY/b9iVvFaa+YuaRjJtOylXxafRy\r\nHZkkGu+XUyCegMTNo59INLiwWIfs+U3UONA=\r\n=y49y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f1ee6ddfb4ce596f3db4b473383feccf50c79b76", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-MbF12H9FLGffOdTIjVUAwwBXHNOMbPCIivDMzGiQTKoonN22STsmqIWTTAIPZHUFfW0JzDkE7VTFs/jLP5F5Tg==", "signatures": [{"sig": "MEQCIFMpQs/b6T65cSI/HMwj7v0M+i5WPoUM0zTVoUQxHQS0AiBDDdlEpPSSauKq0J2D9TOZi9a6smqZudpIEE/X8mGK2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpdag/5AejKmQgZPK2ALCjHNNrbp/583MtXvXl9r2ZokzJHAcDwldMz\r\nQW9E25YFMdl5F1F3mj80T+jEgS0lizTVZV6ojszPVeoXL3ziIyAuuJNi08Qn\r\now0+q8QBqaHM2Sb9Gkzwe8BFQ2rck8DEqK/zwyJ8i3Q3ikKUCLoHhIP/u3FC\r\nbZciEbxTpCh4wXiTzO6pOO+zwFl3doPt21/FXX0sJho0qdl7mw5Kyzg09KVJ\r\nKR4Ez9ui+VfiYrjwzGVCMW7Fcon09dGnSvK14BPgoi4qlenCRPTPBEU8RCer\r\n9FLaY9p0By9/f0A4FyNosUIGAawVJ52ZETGxSXbu29jfFdGxYSSXBK/RkLWb\r\ncV16FFuBx/kBHJKwX36z9RS3iLjvd0cRMHqXndf9xpKuvgUW18vk6GGM7E1Y\r\nxbyYH90rKicBi35SlyAmaKDlxYOjPf4kLBhfYTkoKaz2QkKbB0+ELjCp/pal\r\nF0IKSCX7YiRF9JMBKZA7SQO04MEE1UZRod6IRlDl+CjZPjYrx8BFhG2xDkMa\r\nU6P8XcP3SoEk1QQKz1UHts+AoiecWp80nwwzwDeF3pNVv9TnVC/inmlkDfoL\r\nEInLayWwUbmSEQadgGUxPyVpMeuq4BtT8F9SSxJ6B0PCM9+/5tkSf4zVxp+l\r\nWSEUXbOxtxEW0blrA2+2Nm6etEQlFyCV97k=\r\n=t3sU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ccd8e332ac304e30800ba13704682e6d3b8498da", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-yfzNPZ1idM+DmpX+7HTGT8Oa7piiaOiFaq7ewawKI3os9WY1sQ+Xc4nxR9/C8K5hlg3pJB4PqqVoXXPFeBxgwg==", "signatures": [{"sig": "MEUCICK0Pf6frh/MISn1A7Jc1O6DEwlo4iEGQDJxYunh0rn8AiEAhqP4AmcHFgC72nTC/gPscQN3mE0QoCXvFYvcdeLR2W8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH4w//eUlbMI4pkdVUGkbwb6ZpuYHJg5tyPRy3hTfXp1I1o63kSSmY\r\nqjC4JtF6rUiSPJ7i68ZcpBzQwZn86/jLlBFh/xaKE4X1DnrxG1xQ4fIShkQW\r\nEXzMIMyR/hAfZJPAtx+jHt7LP5fyprdeXv5YHjZdUkjB0mcQQfGCsx9vpJ67\r\n4VSxmYDtecFjlVkIijRf3PuSYFB0OLZKlEB5FNxlznDQZ2f3EDt6vWmAJizS\r\nKDWtpHDIRQ3O/yqgrTKZpF0xqJKD4/k7hqvixURDElnegJyg00uPlXd0/yRk\r\nvNhzliP7IBwMACNX4whlCFj5FSZ7c/gj211rgcBoGGSgpcH2OAsonsw76B8d\r\n8jSD9iOLa07zDn+UrH3aMR5TislpIbjlPnPfOSOeupmCmLBw7KuNVF0Thp4D\r\nH9uDg4/Bzd447wy7JqjH6YlCHtIO1ipKt561A/jRD9a8yTu3Y+rDYFeTj3TB\r\nFHafQEfak5K3JN9Ogk3gESirHx4rrvIs7bvT4SeJdPnZO9wd65W24V8PgzD+\r\nQ4v72Za+uer1d4e3Q8eMIVUPWHa2+eh/bFc1APKJ9NthmYtPivhCGgzL5b3L\r\nIRiBRvlokq/iEenusALYj0lOwiwWDgIiU+0KrAjQWeTmNNPcAkgkYFkDKduB\r\noRt0l54DkJIZYgFxERODDc/jJvgvYpTuGDA=\r\n=zVEy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4f2385a6ca3d2da95d2f853204bac791f7864d11", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-WX1OCaxJELfbEKwMQ7X2K3sNjRb68Fh8Lbw819ywo4SPSZM46yeTmUUDHlLH+fvl3VGRiVR25otLoDXm4WuaTw==", "signatures": [{"sig": "MEYCIQD4+oi5D9uRoR58hEbbiB53U28fpS8E5IT7xXR2Rl+2RQIhAJyJDIkvHcgxNfZIrS7twfHvCuIA+I3DNuFxZxpIBDZh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN92ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcMRAAn50AW4RukrzYehjLB7cqz2SX9n2utOZscHD7LxlPyF0IPUe5\r\nSyNO2nYJ3YVmCmPRg+x0/AIx4LrqZ/nytqtF2ZWu2EGaDBDiPkuKF1pmW1Oh\r\n09o04Q5qYJw8R4A6TJvVSGYk4dl3odWdX0Tfi7T3ilAsXpnugfIL2IpBLvBU\r\nXt7Livbufg15IhVB6PtSKeizN1yKOxGS6yvrR/9IfXCp4gTJNngltdnBZEw5\r\nsSi8SF1jGz48tLARdq/VCZMtMOpC5N9Ic4t+VnP15mP0YPLb4SQcS14YR9n6\r\nZq+938rgvCHkyrCuOoxJunAY05tWvT3wVzMcA7/8Kouq3GqfPyY2mbvOciwi\r\nqUPEQmUMfntH5AypUvPQPV8cJ6qsF1TLseaiMZTq23WfeuCjOGc+qr8yAm2x\r\ncZWF4l7fHtAWj0e4q8LaCcGU312Iy6Z/jQ+Gl9ivGsuEGkrVZD05yXR71FsF\r\neYM6vLj/LPUZc+GRdgiqymbEeh6PQFhX/DRIASayn4E5bl1QRpKPGMLqjzCa\r\nIBQr9V9Zxt+e2QMaOSigIaEGHlpyTXJtvq9fT2/jLYshfR4F+24WLguUwUaJ\r\nyn61sxda9scKPG8RyqHMZq0IwmnF19jyvAGt9MC+qseTuUGRz2ZtWdqYb6ot\r\nEiWCgUn7KFNmaDNSlZYAL26zDmaR66iAjFY=\r\n=atPq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "02523923b7815b5c8e5ef57a077bfd93204f2bd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-yC8iseH5ig7KwsCKe7ugBkcZ95f6Ik7BtH39VTy3cYvo8yPrneKYaIR97dgw386pEQ5ApXtxNgE9YOpHjRSOxg==", "signatures": [{"sig": "MEQCIAuDz7LHd21FxKr5PORcoLe69hpBPVpK0pv7Sin+YoXbAiBEYYKRik8a8GzI1k1ceWEnVtm+upTvwI5GhKk7HCWjUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSk1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4cw//aS+BwO4axnrXV6lZJ8qBeaz8m9bG3yTV9kC9HymPIhluOb3U\r\nJEfjQMJpug/X0XAdQQsmCPBTF4VaBS5gz8N6eEO5rseKdmG5d4eRXXcyFcTH\r\nvNfewu8vxqTMOHeiQfcS1GI8XV7a1dYKvte2CMeZ34hHZHFDNIse3lPS5VB7\r\nPwNFyCs215bjw+DBFfeYbyH0XJ/H3aQh08quLf5EZYc8O0X/5MxB0zB41GUB\r\nX8uGZLhutwFUoSEc5Nt8fPiHmyTZ7i2WcdtxX/M1fIJlDFV5BPH3G3L9P8xR\r\n6WIm5hQbEaans5jzxtyQFosJ6zpXyQ+8kPAh8UZpkmxRYwGMHtUAvihNHDFV\r\nO3zhR6Eggvfqrt1ufxCiGx3kPLPV2JUY7JmRoy75DcJOQ/ijcQnoxEQBMufL\r\nhsrwfMVlvl0nI5D0cX8vGhMSR/B5lkNZheiSDehz85WkbliR46MwmGxplrZW\r\nma3eLw4jjgKtU1oYy5o2Ig62wUsY9O7G9LCkoKt4R/Z/o7O49UMPX/81V0yw\r\nVWy/oFHTmCBaeGKyYROJdtJSkJhM/Hpne4xETMRWi5lnmtiKlAvwwwvwWO9g\r\nEGneAGdxNwzcrMNVE3aNrXkv7M2JXyiQvlARRgN41ZuZH05gyildj0/Lp/rs\r\n0gu20SBx13zAV8KLd4GmoZjtAWQRw+ImQeA=\r\n=Wh0M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "12a820e97769b2d498b619c9028f7b5642bfecf0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Nwbm1rXnQqM8Qegasub+dGh7vhr6uQXDbo5NFI8fnMds5tMJaWvMEGQx+T1q67VOOhRqFWGZO1RVGKTS3G+RDg==", "signatures": [{"sig": "MEUCIQD2sHZ0ul7XTtM4TjTbrZn0FSAd8hAMpLwH5nDXwnXT6gIgNnCii0UkbPUhRU3CSvgxv+x4CyPPnTYoWAmNyy7LaQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokhg/+PQ4ZMhv6VpUK7Qyf9XgPYTw7bixp+oqxVas7Fp0XQEKxD5KG\r\npKtbtD2HZxhxT3e3M0KThII2y31bZSgut03XBpEmyq1R2/q6AydFJllUqaiy\r\nILBwdTrhi/9ZdtE63xVO4i+5apXCLPQGSIf0oTsxhIN7V/ayE7N8Jw4IRitY\r\nCTmha8FliCQjvK54/zYfeFWMIanX3kBqRzFNGx9JafudnT+rAV3Ro/35sk9i\r\nZbF/TPVd53FdR2jDo59cWhH/5SQmgyWlX56SDMQI5rhTsQyxuE27kZm0d8Zy\r\nASNz08WGlpj0mbUYTrlmB5UMVjHH8QbKKzyymhSXmYXQDiJ2EtpRC/KIBrq4\r\nqB0q2TzSZQuiT0WLGmrB2dP+sdPItysPRr47vCP6RXpO37vLNbCfTMhNETcD\r\nr8RQCqbGtBB1PLQ8OD44ljbuIYrq3xz7OiqTqAi/HYAEswNY//NTPh9OXYrJ\r\nifdNU5ma4+IT/jukUthv/68hS+xFPMCYVABMggUi7gyLmJR+fYYdYWPyobKn\r\n3pC+NK8GyD1JR30tm2/neP4+MqQqYyXIUDHFeN/NQ+BpBlvso8g5RzznxqjW\r\nUM4yk98+jvE0qgPg9Mt5p6pDj9z9Bhs8oqaDEBkv5J4eQaCrS2glP1iRYR4W\r\ncqxXnEBuU50WGPXdNQMr0gsoP+rzAFGuKAw=\r\n=p/Yn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7abc73fc79b0019fb0e87b1cb31c521d631a834f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Q+XIh96OqKIeAhuIoiUTnjXXE3A+4qdcmp5HmyDyjoBAeBhOlEm650kNUzOoXISh1gyHqntvoP02PapgiE27Sg==", "signatures": [{"sig": "MEUCIQDYmmQzC8GPBFmaKSoct4r2432ncju1sZNO3yUiQ1l4EgIgAiyfXEHk8eBgLwsP1NN4cU8Bsrh3OhmJgt7Sh3744F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoD4w/+NVfT3TZZPbNh9YEpSG3mntatIlYXFqFmPXIveeU9xHvCMeU4\r\nuQqe8DFCeeObHn6gFEPhGsW8CxYzRX0F5MvNSC1jVWk9QLGAOVazM0AGzeyL\r\neX+fvDb58DzHPnBqI4+Zh+TMZ3v0Jnm0vNijA5v+KvHzH1Q8XYktmSJ08k5T\r\nnkic3xYEOAW9mNSf5XfVykGQSR6BtzBW2m1lTIrE/3mKQP5GdeBvb/4D3KWN\r\nAyXgAE5vdeS+n64MR2Vhx238G6tS/DLLU6xQNlGkOZIWKleIac4ZpjBZ6GCd\r\n1yiWmWKrOkHyC84MPtNB9WxvkQad+jsHHiLCaasV5UyFtFHfeKTyPyJ8rzal\r\nCtBHeN5FpXdvSCmFcRZGbdvCuZDjRQRj7tjSAZEvaDHqpyA6o0IG2tNakG81\r\nf+x5ZukV413h2kgerZPqcGyLcTrkkrF1kF8j6KrGpDbAA2F+KaHIFhZ/FReP\r\nPEVj+pOprWklN0/YBGEgkBKh89MhYTCAdf7UltyOmDcJgP+6yo8LH2KmAzsa\r\n4PzhaB3TFoPtHTO/5PZ+bOc4J7BjIwu6OBPQy9ZGhHxMzLPX9RxEB6CaAfzG\r\niPbOIHAXLmSJlmE+OgI/tb2FOEIuJUhVVgg9p8rqBDeUf8J31H6vcT+qWoW4\r\nvvXcXqdpfiNgrwnCcokwoU1IrtNs0oDkPCs=\r\n=sbmR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "de745c7102dcf7d00ceb67396525147ae86e1f7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-14MHlwKsjo+isSLjQZRg12dawASx45vzKjrl9EQdsjUnhI8t7g9s5zHp10M18T/HUL/WHQGbYRyQXx4/Ikjiyw==", "signatures": [{"sig": "MEUCIB3qlPcNMH29iFZPMtCcyvksW04RWlIwzoLbtuWel80EAiEAwTn0Sr2IWTzoLRxvOyyvlGZBNRnP8rN71vTMAs5N+rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmocaw/7BVzuzNNUw9NFp7sxh6dX+EzuvVWBBWyNbuklFtI8mmAqqIGI\r\nuBHfPnhFCS6zBqbLAfWtrkLxssd5STI5e8otMIaXNOqx7tMBOnej01OEjWue\r\nrxneBhQ/KPFqX7perzaQE4RkrIsMzVjGC9oJuGJn28lcSEb0+5DNJAVdF45Y\r\nro53R/mMf2EuRmuJIJoX5l9279H0w6ZPbpjakBSxmcxKOzr6XBYFgH7wMDTv\r\nSwEr8xElA9GubBTIofSuzEPKnvRCpqjbPsNmshBaYvLbWtd21c6bUvfDGMJq\r\nvGb9UK+PSWr03k+YWVEspbvb55KZs5gFhcsWaoNXaMNbOW+YxSuw563qY6yD\r\nR+1u1p4juqn60MvsKVLwz565C8/DPKmHQAy1bayGzkSossInHs5tyq5M9ORO\r\nKfc8mZZJxN3NENJfm9n69UmhwWblr4sMGJ84zHXhZdWub3Yc8e3AUNxeisOt\r\n4Pz16iZEtgRbEYBWZBlm9JkbSOsjcA63xik5WfMfRyTbwskSbjDUTe0+Ngab\r\n3yRbAEwgk1Jj6cOQ8/U3f53xCuvcRsJ/qy3djlLYT+jXAucUaKJruThY8yfF\r\nBJG6iZVWam+Zti/6qSlHOuhWIIHHV4f5IwqeOMkrIw61mC0yGc79eg9mHQT2\r\nlLMrwR1Kmxv7yNGNnFoTOREuRh+cIRWITdA=\r\n=rUF2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "108a2c64d025c3759788cff68923703d5f5ad96f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-+uh6KARd6hl1e3zplwOXH8w7rNTD2tjpatH+FOCtMt9nEQD/GSFAL3Sshr8YEnPn8j0/P4EpkcNbKtos1AAztw==", "signatures": [{"sig": "MEUCIBCrowseiEf4t8+xTXpseS9JLSYJSyExSW8P4RuVkNNvAiEAy9r8M7D8S2kY09cV2OE2qRBPHgsrnLMTU9Jd+CZA8fA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPRA//ewMbDr7lrKXY1DmhMfo2gw68R1T4HyEji/64tXR+XS445XgR\r\nhnCqB2kyfqVOmkQcmGWYRBlqxnfup3VRPtNQ4m9snbDdcD05eeuWIuk5eA5y\r\nmGSbtkHY5bvILWQA8tIaWBLEPlwbED7KLm3ZyJaEkZV/H2AmBtInW915fJqo\r\nyU5svbGDqbCHQy+4E2BQRFbEF8HrqljkEu3tS9NgHPA92Q6lfeMf2bFKsfuv\r\ni2Lt3AwBy2UUv+/0ilS1tIx/HP9ykIsvVnTSVbI3tgteZDILLVEuyWwPj5Z2\r\neNftITj9QKgycSCkwXI7yTrQHkTYLiVdE1tttvNOnSMfNOE9JMrXOZ1YsQK0\r\nn8w7er8fIJSZBTd4kz3GT51iOHI2LBMNS2ZSGdUgd61tvTmMdiluNE6auxb8\r\nXkZy2wNsK5RMA2Z+S0Gm70pryZEegf5o0+C7p/PNZKyk4gxbHOi++MbIsATw\r\nJhtaarI5cJwhDtjifvmc9ZgfipBvr2yokMuRRTzavI8E6wtTHCxkcMMrPJOg\r\nh68HuPKvFcFWdrtJI0E8Tzm70n9INptwI8reXsKFRCqwJRox2KQAghRKkbOu\r\nCOFfXthjMRlNXzbaB/oSJ2S9dmowzpWEpJSxyvlQjDuBhHtkthof+4KHXP2K\r\n88PuxrGVRvFcrVd2EIzEdT0bVEvgwOh24Mc=\r\n=V2HQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96b8a8024978552d38c835812297011b594660a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-8xWQxIgs3UyaXT9azixzsWbPFpIvJfUNbwjdQXJA7/PIf6QArGIMZ1GknLJSysLA9OzEddAJCYp2NuifH9UMbg==", "signatures": [{"sig": "MEQCIDPcTNiDLaIjrUMmJFC+DTmC/x2F18CsT2OD9D7aCk6dAiAlok+pMbRsfFYZJuQdMaWciKRXWtdZyU/eChGkzG821A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJ2hAAjwZ6CsRHchh0dvHQwjQyFTP8G3bOonrPqkDxc8kGXqWdAzhI\r\nSvpDvA+PpwWrrGzFfbZG63+iOJ9ZnrwSAN/Ocs+ttKyZuYrAYgUY80XtKgZR\r\nGjVFAT/IjvU514yT90zS9z+La4KUDvTdGbrdaC2eVHy8dkty7NriqnHL1OpA\r\niV82TiEfnvDJsUP505HmvRcZzAo8ZuFzfhXQ4HPyMTCBM44SH4YpPePs1rtL\r\n4LuqvwY57pop/p2MEUF/86ny3TuLZEinp8LSpf3mJPapHtmeQZrsC2rJZAnv\r\ncEujz7kYbB+K7FHdZRW1AwD5+x1v2sus5zKLAAW0talFMi6TGzG7vAi1Fj/3\r\nIhv1I2ic4hBwoTcwSg6Z8Z8ZkOdaTxcwJPj7Rep5xBl3IxaJnwDwA8wvSI57\r\nFO0VyqNTGB2tsNm+k126I1xlPrxrOO3XAGqwHqKSq2qopDlOIbZZpMn6wFg4\r\ncL5AzVOQh6lyGKfYPVJz6Wfn2qRWg+BGX/7O3u+rXnSx9n/3HIB26YUA31tp\r\nRS6B9gZAk229lKId0ePbdqjFXPbJWPmywkDysvwHAop1DOzIhS19Fumf3FGc\r\n+nzhor8SIXGclzTy6/ItktZY11yMXctFL4J4dvaWhsSqIqjvXRRlS6ws9Cqe\r\nVdmeVHYryXWAbO6HyU3K2uotC8zfXOEalPo=\r\n=C8X8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e8bc61f4ef6ab4c5a7131a9620ca3c702c5b3a3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-evbAoWgoOxe+QgJPkiBPkB0DaEudWIUGWQZ0tta60mTvu7dOHbaEPumd/AfRfHslPCDMC3SZhsu9WLuTQquDzg==", "signatures": [{"sig": "MEUCIQCZ3hwYiBhxHXxtJ0QtUQRxlfgxgX41OPVsHudRUHvlmQIgB6A+5da5IoAb3UF/sRaCFAR4muDQnWO0ny/vSKa4MHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2/Q//c+iMtJa7KRxO6hfxeax6ZH56EGTXrptItRrBeXL73+NCMJcy\r\nnx9FjKy9CKf6HQ6kZAJSEiQ5jwGd5qLAV2sxbSgrZIz8BaEZzx41CHPepCii\r\nLrCZnsP6GfPBsMA11G48szUNhfJaKa5LpJKvW3GYdGmKf2qifX43F5x7ZJGG\r\n3gTngLR+KfuqUsHPbEcZeCUplTgbt3vZ3JPxPBdVXRR2OICK6RN2jN/DNdGt\r\n0k0Qqs3jLyZd7p1iBJqM9Z1Q/nucCncycIX43a0QLj2NlM6PUhkDbcWFqkN+\r\nQ7hrp+jusiZxul6dcnWvVm/ThVEAIySoItDrmaf2+CsEwXJCVAbjh+T8ZObl\r\nC0h3YJPrXbbHpyvSI8KrbrDBhowcZ9Caez3dFHPet3UgCXjoq3NsO/CHhd/R\r\nTNtHzjbiVDyTXeIUBNf+iSTRn5wc5xzZxNhAq1v2PDf/qh024vJBtsRGnYZP\r\nZG9b6ljBTczTxRqMDyYI/VQihqKwgcbLN1kr3kWkYnEHLCiI0sulYPEEwF49\r\nYzEXrkud6gWbfThqA63MyngBd/ZsvZMHYIc9wmveGT29oS2O/cTcVVk0cA1P\r\nXMt64XSPvxdGF4OdINHSZNymQS6ynlExq19vByvTcIze1M2WDyiUYEx53YAq\r\nJdkaM7W9MpG/hJU3F4YKPOrS4U5PxXG+xxA=\r\n=I7u1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dc03a8f464643748c0dad781b472f149d671d5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-ZTxuynXn3FgNVQ2+1/G+QLBoNH7F826OIUHAHc/tkuMsSjTKv1QVUxkE1t+UuyApYS4aqvFcrgyQlbcdqp4MpA==", "signatures": [{"sig": "MEQCIEPF1s2Ud6uWULIkv6Ow4++dqA7rAygnqjczZUEoqMc0AiAbUnBMARSomh6/RaQ5QrZ48m/L8FR2MO/u6xk/agiqSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmor5w//XWRos8+70F/TEZbiSKMKnOqkDZw/M5aad1cv72xkVzSwkFhx\r\nlXfy1rPTBRXoJBp4202lfOLL16pIbw+DNMbMtly6cs7ZFf4ld/hHQngi4z7e\r\n1MdKM7rq0xag0i1QXpG0lLbSIMxEHE/MI6RxioOsspDeq0MZdOzm8HRu3OMM\r\nIISzUDbUokjW4xrYHWs+etzTt5fOMJ4oVCe7hXyWfn7/HmgtV9cOMfcmxwt6\r\n5lgMszQEE4zafyrNH3UjbUf2rAlHpBk1i8sZw3kcEOxhCorlGfefs/MWdfZk\r\nJwlHszRUd4g12EPnT8jCeVZ0+LEwZfVa88TP+4JkVlyAAfUOiSal2bR8szVS\r\n9FdkjVZU8Ymz2KXf3ZoIkfKqGVvexGzhkVOII6LO8RIGxgRu9BM51kYm37bz\r\ncoi9JYcX4GDxmkN0G3hpkgmpmKPc7Ear3J309uV4rj5DpB2Oc9McmMuU+wdp\r\nFQ82Nti+y0+vJFWhVOMayCGAtw1pEwjPK+xAXsBo8GnLcK7nWW3yBTMJUVJI\r\nELemKBY5SxKjN0m4q4GN4zccz8GFEde/4YseB+NXwgqHVrMbpL7gq2uewmIV\r\nCcJdmoomgZ6xpAFhwCnZ8CvJsttpoykM0rC7BnX867qCrrWfBU1YAa3GG91C\r\n2/KEY8POrYmMZZAe7KUp2pfREulkmn+Gg3Q=\r\n=b2/a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9aaa24f0ec08fbf60c68700da6dfcca79ae67a6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-HEqNq+GgoH9LQpJYjx0fmiST7aaex/AkXib0wG3qPKsG8w38wk2S8gNqgUgbSAxaiyyl9cUt3ssWL6s7tqQ96A==", "signatures": [{"sig": "MEUCIC+GGrbmFGEWYZ8b+HOOmvqZwrRQJ5NlkCr03V9DaQHZAiEAo1efz8QV2Ohx3M4IaQEK6aljCcAbN9P+7ZN4yGV0qHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrkvw//VVh4ET0uMlPYX3GiphGBI4x37Q7EOvKtHXvPH0QRXv7Jk4Yn\r\nPEMjNT64r0+JM9vUX1AH7oeicEs74eZMIZvRoT8/YDfr7JANKaxttFopF7uP\r\nzGz99i//Uz/TE0yY9qAJQdTzHH+Fsgtf8r/bBnpsotV85EfdoF5b4jo1OPWn\r\nXTn6Zr4vzTg8GwXmUOX1fi62euFi3GufUE4Q7hERKpvleRDV+jqTe5DCC5xX\r\nJyl9vIqeYIbaMlluPO57o0gkGSMqrP/ztvS4lWlaHB/KZU6tCO4u/VhBjhNv\r\n65zz1ZfAXjyophS6+7Ml+sc+lNbbhhANF1UqScirbgdUMif4xg8uThchyB4i\r\nqeoBRpE+iSwT7e8SQdrY7g6LFzys25gGwSsjSkcgzf8w3OFF4p+7WG0Vd7im\r\naxrKh6ywDyC8qPrn11uYaZpwneA60Wo1LgxOCXFaux7OCK8+yPIdDnqm9rgS\r\nsW2fdIJ1AQDgEyCKkoYGxxbmaq3ifWapfspEF53V0GDj7z9pv+C17OygH9OW\r\niyTwRjkHK38ky46oazGcff31GtuBJN9Gs/N6CJ7MzyXu01nvxYP/aW1wuw48\r\nLiVfQDCSSLSB3E1M7H3f9ZkEBR2vx3z9eOXNjbpKf0m/FWCgMqwzEj4aJGM1\r\not66lKjuXqS8/p5i4KBxW1P7quuxl8jtdpc=\r\n=UTcs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5dfde6e6ffde873623183042e3ed8213c64cb229", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-UWv8K/aLPXY0FlIsjxA2DHHI2EP9y2THVQ5wp5RLXS/QI3fx2Kln7Hf0xjrY8fiwFWVCM1BtW2VA2+FTTaWPmw==", "signatures": [{"sig": "MEYCIQDQ8uVC40UM2jWJm5pfcPukKgcCc/7JxKXcdFPCVxORZgIhAPukxJHfig5k8gS1L4C8tQhDtGEEowcsKVlC+iHobObG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokgw/+Laws5SkFpqZwLgyfidILal6yqtThriEfvZFmCGOI2jOQGy1M\r\nluyxl11L5ARnSkV11P+CEOfyuklmx8Nuly2HYxCykzikNBpy7Phno8V2Yw87\r\nsiszvgjsu/MM8NZCTqU9Kq+p1xFUQRJO2Mwj/lpQC8Y3eKslBo3AkBQJdy6R\r\nTJbpn+HkNE/vU+UogCAAwIImAeK1RPmcdh39lp2UDPnFPpYpdvS42e5G4M4m\r\nkm6lUtzPCGYCfojed/WOTVQosf7zLCKoIwwNt4N1+YVCWpap792NsIGV3Xju\r\nMpMtd9B1JZ+29872o/Jxn1AXxTcJRvSQNDox54pO3wqCoqu/3qW9a2/rxlrH\r\n46l3tX8PdN9tQWlk3VN6fiXz6sAaO8U9o5lFM0lbZa6BtzVNKllIRPE2MPzo\r\ny0ujsiET2JnUp8Twg2dZQ3owkQBWYBS40pRlULp/gwUpMyJg/m1gc/JAt5GZ\r\nWn8arEXQAb8Xb31uZHLqZdd8qdSz//imKVpWItQNlS8vUgdw9XtQkb/7SamZ\r\nGSAZNal3C/nFZW06XPO9bqUdvQcOGUVcFSpVmDyEENOUSvQlyILEYC95/hWz\r\njXolNpU86Q/kXLONL2o2+L7TELH/o5xa4ECzz8VktreXOlNnvC9/CrVndbd+\r\njufZLDSSjjfo+HcfyrgVlC1c6lcSVw4J+SA=\r\n=wFpQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c687eee7fcf86cf7ddf4b33f3d9b3f931cd659d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-WtsPto9ceB/9ui/rZ3ptG53WdfwxHWzSPIHCRcKNasELGGqljElxKyY8iTYRh0hk9xClf5kM0upEd+KJKXrRDA==", "signatures": [{"sig": "MEUCIBTWMFhoSeO6YjwK7yR/I/fMSspnJUsV1+zzeplKZlaVAiEAgMZygd5/i8AR/XogJ4NVxkA2yycywMtFhk57bww3cBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC2w//W0lyfNy8z+pKYsBBdWNsiqxo/vywZrvQywRZ67wJ/iX+uYbP\r\nD4KqMf+7WeNckJ/ZSFijrSAy3OPANJLAkY8pRE1/NsnJ3tCfcH7z8Z35mSyp\r\n3sZqDhDXkgQAMkCXADCEEVq+SMUttsfm8GBxGfXCmgNGsEIpINhdJ10/AR6t\r\nkRERrGKHFyAgDHn+L215JIGUJXxz3h6S7CO9/UO+N2LEbMPW3mrMoBfrK0Yt\r\n8bSBnWlycMsj2RS8LTmt2fGjB3iD5Yska+GdwRZx8geEmpe5hZRBHebaEqYi\r\nOxgymVNL2vpGiWkPLQ3TI7xSsTrKhjWF0nSXGE9oSc/xYC8lQJYGvI65288x\r\nX25Upm7ttyo7ZbENpbqL1p17wVGEQsZl41v5ftHlAVWkh3dtU65QlJswKfJZ\r\n/QS1rO9ksmNYPbO8itvlCsMiOtKttVEXjr0hY86H/qqqKqWB3n3nL+fi7hj5\r\nUm47aCEJovysuxYls+2rnxJtN0mUT5NQvXxux5y9h50VCtGVIaZioPLhOAaF\r\nBu2ZMGujwAkwynKcS36UrdoKgd2AslS8nXpObwuJqrt57dmzbCCzCg6X3+rr\r\ndfYetRNJEIRRB5jyoeb81reRet/1x6LWkp2Wj5GlYHxVJDsq4EYtcnemw9HN\r\nWZtjXuRtzCSqOKiezntmwQ6DDLMT6XcmUFc=\r\n=TZxG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "57d09df45359115fa54e742fc56364ecfb0bc9ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-mBadywXSD0uH5nP2qo6cvhz+SYKRoGtRd/8rnhoPMw+T2bwdYsnriRnPh/0sGJzrusLkmGP7mUKblnLuQmhVfg==", "signatures": [{"sig": "MEUCICC0899+pgpfcL2TOJb6Q+L7BPl5DLeikvTOVajl3fqdAiEAje+y+bzThq/+68JI+okV10Sn/Q3YI+bwWZpdN6/cMcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg5w//Q7QT7qcaGS1aRS2r1do7vqsC6wAD+k4oWh+vx0QXOLmSUITD\r\nOEXRurPM+qidjO8knI9akLE+COSMAXVYu8rRMesWZNqvKzLDhrIKmDeJ0LMa\r\n+0OnikS4DKc2+wtyJQ/G0opgOOCrQLH4yGIGWWmBTLtL+gAdShjOOA2Jstf4\r\nFDIO4r7fXgjRUk1Q4Qi5KgNfNCoQw06H0ruCzxEtLUcqsbzkZ8BEg1hEyJba\r\nb2XkZohg3R92LGRwBKqlc04rZSoVu21LyzhLLvbQDRMis7jAVvbD7dPSvcaB\r\nzVBHsUj8eJKQxAecYp6lU+AO8qcyAsf5J9I7SL5d60AVvV+hvQQbAIBGqrm4\r\naYqnWv22QM8rRixK0dD1tbBGKnvtOOe62OYoBIQEsqnZuDxkhYavmMRiYTOb\r\nXGVvYKHTxX2O56uAP4hDcij6s554ZsNjJeaI4cR/KBMB6juTyln0aJkUleG0\r\n9IE1QdD1BVsa1KLzSDj83RBsddBmUuKvaYVEAwlPI7lm2e0k/kG/CyeVz0Gf\r\nCyoof/rC2l6PZ+5Dj6Ay6cmajJO1EKmT7JOLHYn+sASyTnPtYYnAYgxyzeqh\r\nFe/9aF/AfVFxrd6CHNFPJxf+r85GtRAsY9DutSNeYyASe1z7Gaz1xU4QTNsP\r\nm9VU+BQo98LwiCOB7W3if03sZlujCnpLBwY=\r\n=czgg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "30cf9a34623b42d851f60819c837277e2638c771", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-/Q6xamVwR3mne241NYrP3JiXa+JCPY8OgO9SlV4983dfLNw+tHHPjigvHDv2wy2xVhuL1X63/YS07uzhkB0KJQ==", "signatures": [{"sig": "MEQCIGBpMi+SouVaizMkcRtgJeA8/y2emRN5xTpBdZBapZWGAiBphoArYnblgz6oO2AfWo9jlLCJn7ibSbLAjlmeLySPng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4tg//auxhz4D5ggpJmBUANswmSlIpbPjTCLlyT8zsBJK7iprniVfQ\r\nuo+7jleFc/lgljXuYvMRW4oqPtUFmyBRY0coWowB/S6hanD/Y4NMUBMoN3yn\r\nWriPnEHX+QKOqo3qkdrdiGWu4AcPwlw/Xhqy868kcsKKd2OOEM37wee0jpo9\r\nA18cp7YNT9iWS7aT7u13LHOSPSbCBaBdej19vjLTw0EvAM2Uvwlfsy9zHeGr\r\nxKMkD9ZQt17zJQu8iT/aDGOmi7x9Y4XXcQDTagC92IFI79JiH4fiZIpdEn04\r\nfGOcute6nex6CW6mui8ArJzV9pm6PHNZRhkhEZe+1qPJrxpno/Svn/ijbEW5\r\n2ct9gBK7/rMEye/z9OE1SUZZk+KyTqzqPci/u3XNwfiz7F82mV9oyX3uZ46y\r\nL3ZehiXp24+wErQcogYvppV/cPsZkB7cB4e87trgNYw99Da2W+zLxy9osg5A\r\n180+PRK9XgRB2LN6fIsd6A5eA5RHOaa7+6S79M0JwUw5+LjTaGoWIH5tJD1q\r\n+kB0/c2vtJD63fM9onr99dUst4GUxrn70mIWTbfOx4DWxV65hmFvwIDCWVKz\r\nZVMYeA/scOfpej/vl+9jYG6ASOaZnOUl2NPJ2pHkuNz3A37K43b9PeXG8XIn\r\ndUFBjK1WVmlzTi/iHW+OqSF0KktTx/kJuNk=\r\n=Dani\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b953a3f5cf2ef6cbb154bf5ccceb7d037f7b745a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-i+qzmQLthy/sPWdWiXjxxpPI8Gr/ud3AETsl2Hmn9s0unF5YR4DxyySf+5sR2lIQXk5OEH9Q9pgELa4XifYXfg==", "signatures": [{"sig": "MEYCIQC60XHzHvQGiPJOJyUG0pgJHTUhAR6jveNDzzbQxlLWOAIhAOEPpBWKAYIRSYRNNLUc0FFr2nDd6rlnQLvyhClwlDzB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKnA//bJ/CYkGcS2j/KsxY6hgpAWXvFeNhalCHZq4rgfY5gdlhN9Nk\r\n8i78qnCKOImCqO3d+Vit4zkAbhikkvJnsrJqJWcwdKwvYys0UV6m2zRFoZQm\r\nx3qPwXhzGm0lMT3GU9uazN/WFA4FHpKsgIn05ny0XhKKM/FQsqap/A+ef5lt\r\nYoLS74/3ULThjFvFj8qqyfjfw+MtZKn7+WytPDxuuPOfvlXzgp95h6uSYlD3\r\npLdY8kZV6J1s8UnPKwlp7/PqvnYstPszEx0/z73RRbUYnmqOmaEjPEks+Lsa\r\npbapEhN1p4T3VJ/fSc1VPRVPhfjsCykG/YjTXrqepEcWwQwGBv3lOtagnI3S\r\n0UBiURnoBy3GoBwkwmx8lG9qKdS5GyMqea4Iu8Uq2ZXhKN/OQE/U9I6mQb76\r\n/ddKj6pHVkkN7dHM4u6RF5ZQkm8+a7FQHfefD2WMpbQ/4zq4dC8PUxujhiGX\r\nNRQj2zpd7wkojMqTYZ2umOMin5P9Pq425Zgnbrf61/iaw2mQ1rsho5UPs4Ks\r\n3+pNmKY8UQtOyP6jzAydt98+ijiQbZUV/RlvKesu8r1QFFADZoUvaUxtf9sX\r\nXJTdhlxuxdqeKB7fViLw5vmiI3Z2kXDP9QFd4iN1Yuad6UNAYx41rtq795oj\r\nsgLu9b8CHhKO5aK7JnVDMbahx785pXHR1aA=\r\n=Orfo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8422941bf087edb7cec4a8eb390ff1197c063f35", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-TbgBeE7ZKGnrkAI/k7Xa9fU0/bAxmlxpL4zW8qvSLKgfBOhcCYJbxA25QueyhUCpIqAFYUM04NYvBd/ZRIuAfA==", "signatures": [{"sig": "MEYCIQCnWHDd1BJVZCYbWtM7i820BIhwB1lc6Hr+CxApCKPtiAIhAI5EBjfQI34XjG6JpiZpg035iy4YcWzn5bV68Cm5yjUl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrp8Q/+KOuiOuxK2xMy5VH5lK4AD8mTqAoG1halcpsGaz/8zw5XzYxP\r\n3MKzUIHWEMKnneu3IRG1bd5CQaCvmO9eq7/XiLmZdtDRwHL6RMKNuHHjh6y4\r\nAAoqVMiY+cSqa9Pi4fZBINrfv6kx0X53Wc0T1wQvVJB1KujSaQrcIFuuULEs\r\ne7Rj+XvM0E0z7sr3dgFGmHvaqBVQvU5HPP0H/5nGz4BxLvw2zL7TN0HXG/lB\r\ncH9oIIEA+cxgQvjj8C7GMO+vLYw6f13uw021sKpbVqWKdF84m0zOyYwnFKza\r\ngh8mqeQeX2ThKrC3no9Hmw96W8wGMTlrEIFsfEeJmWmwYfGtDdmnvFB0e7f7\r\n84Mb5jDDatQjyHTjlxMOnrR20anMdYWwPNSbdK5r143YxNuLEdNJPJ5Ezs2e\r\ny3LHHT0ehQluAWQZ+5vw5R3ANzbNK/kc/qHZ7cJb5snNF8AhlERiMYLkpTCI\r\nr+RpYxYrUKDqgGE8KZNJVYwhfupJWkf5MO0yNz8k6Jz+EPoqIjFgER/SX35F\r\nkjjbCP6TsedkUNdjR5k9GIJCXbUg9fgphH0xPDwAcwBEgzzh2tioTCOq5W4R\r\ny3zZoJLIqbEGY5eUj5fBbQCElCV6Tvc+UESJWlVmOR5ckv4Mf7d/93+sm2n3\r\nch4aYyJihBv6TK6yzKzpfbSEdTsx1qqCp28=\r\n=EInA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0de37764cccd6e0f7726c6819b0b7e304af5d9f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-FZQXhGxeJdG9DvA3aeVJRJEEwuWmANNPUsVH6cJ4LC7OogHNUd3yBG3bX/Fpk1c3y3LN7N0bdBzhQf0W0BokCg==", "signatures": [{"sig": "MEQCIFbAtX+4BtyNE4mwUJNym21WA5JKUBnnXGftPmEdVrYDAiBlcBMDvUELCupi/mRORFEIQBIgCqptfTMOeMc+lfCrjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2XRAAkHgV8GqmROeuBBzWRYc/FhG8XMh+kmkabXDTF74bxtvF/9cj\r\nX37qtOS2v1XrkejZIPlimVXKABXDd3MuxfASAsaZU9FSgvm7hu5if4Uf+dL6\r\nH/nPUO/E1v+c0RVPsD7bIcy/bh0vy8fUsg6F5rFdLrDsAcm4EkYWhaZCPusV\r\nR3xgBLs0VU398L8BiWsaDYpwzogxucT689dMbk7RNgfh/GqLBsyYxwwze2p7\r\nDmHxpJKXdfbShUQR20t7Awh4aOC1DgjLtJo2NmoI63/Oq/ZJB/PGQodLUxa8\r\nlJk6XqjPnEhDHWV18u2mvUxGSKk4M/00IYTTwR8LJVTvq83QkwMvv7caB3q5\r\n02UcHxkLCtuYf2mtz/pq3s0qx2933yKDNL6+CP5f1+VtnF/DQsHYWKM5PGkI\r\ntuRaab/FeYcYlToHxkiaCJQ3LWfMgCZwN0OZTt4EtYN3RBxv2ACrWMYyLClB\r\nf0CpzjbUPrK2+5U5NSexpvaWprKp3+u1cQun6VVuSSPuk4NIkoQXZj8jH4FD\r\nw6cuW1aKsVhGmTT+2c7uRKTYiqmgUVGf8J8xOvh4wNCh5r+akYKg+ppnPo3e\r\nU6yvZ4/9VC/1+jvc4HmbfllwpV0vE+Ibc6SDCB8yi76jPTSxFD1m/W6dZ6wN\r\nzbGqlSb8jd4gb8uogl3aGezgl86VYfOcHxA=\r\n=mf0C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33f8c865ec970d411ff7810f34fa02d684b4a133", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-ZF7ipjZHt2ZTfNVT0BIR+Y1c8jA6XWLFNyvbMb19i4XQo+XHx6IySXzE9gC+bLBh4g+20G0Ui8KAoOWJvG9SFw==", "signatures": [{"sig": "MEUCIBI9UTK8Pd8EDBxJsPC7N4QXd6WrYgCTq69tCmU+Lt1EAiEA5CfGChEQ8BwbPOqzb1EwSFhMONkUNk+/9FErKgt41Ec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ0/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogrA/+PsBX7EzwH8so/vvfKrHXgYrnwxRTN9e0kq/9vIS7aF+ZiGEx\r\nJhH2a4SkxCZIVdQm6SSXWQEWF+RRX+jFdl+oO3d08RYjGJWXc0rVeiaiyZY0\r\ni7N/oXULSPI12m3wmnwQsRWX/8a31jG2qZHfJRcyXORAjB7bi60XUqc+Go+n\r\nUUq6VAIhN90hxmp2dQNZZxJ8pEZIEZa9YzNWYhOvrRB1Pm65TWKSMmEhjrAX\r\niW9XKehGIx2+C9IpsfGVWhkxPx2/4ZyXpnx7I5K31Vn5yllAxIMieYq7hswC\r\njYDa4RIpqxj2G2dtOEZzgACqBRzzJXHjnZCoH3Iq/wkG7Y83HKz88/kDmzQb\r\nBr89ZCFxTWpMUb9YQZu66jqStpKvqncYg2NmLwuuP+VR+BFG5jeS6uf359X2\r\ntn28ZQTRekR+3dz7JhBAIc9+Qm4opnb+t20dzxQEpb4PAmThZ1eA87kOO3Gk\r\nIFUf4lm2Wm2giV+fl3bKg0Ik0/OLZj6QISahzeoFBZwOJwgLsSMnSO2oY/Qz\r\nBo5CRVOcWKY6lV6e5mQVSPavY4aDv4lcnCkVrV5+p5mGFiQQ1xRuqRB4/X5d\r\n44R0JYgALfAlnkJ5FJmXqHmNj7EZ0zp/du8/um49BkmlNS9CeoeaQtR7IYOh\r\n96/IG31uu1YNhmTd+8ZNxJrMAk/GGKf9UtM=\r\n=iLOl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22a0d72a46bfde746733c5b5f5a44847f487de42", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-cDSCO6uiZsOjLuYfjQnasDW7B0uoRxvsJIsdkScAHu+EPs0uqCLFMebgngvf/ytK0fgPJief8sUtyXFXywDlRg==", "signatures": [{"sig": "MEYCIQCYGNDLonGYOgaLRw2jE3vzalxSgtvTuN/CX6GIW0HwngIhANGl18PUXrkLEm4dsddqqPMX1eeNFJTwCqFLVKYHTuqM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzdRAAoTeD1z4g/eSGpGAEkH2AkmUKD/wlHMosULlG4Tua/K00mAl4\r\nfZqAubev0f2z7UQJer7Hzri4faHRGvKklKtZHxIZQTFovNxRzbh1xIJVNYFm\r\n/QQKwTz9noYpebpByw37y+6Tl2LgsTRCjZmn48jdpJ6LcdHJJwh7Oi6gVsrk\r\nj7H16kGFv7ikOsQ02IvBKJzCYmVEFwu8CFH97z/1f6hlj3cFZ0krHwM9hAOJ\r\nAw9N/u6UNsT19Usxv04GDcozfUnvvMIoUiyMnctSF8LxlnyINZFFzwWrxjrH\r\nRIzjMu/m2C8pykd8dCh2K/5njyIZ+gWC4XSvWZjITZhtTTAFXpzx9bM+gfgy\r\nfdJOpv5Q3Ypil2wNGuM7fgq+iaY0c/rq3/jrqi5i9mBxL5os1wNld+LKKUCk\r\nLVuZi01EqCzSl/bWENUcqxZJqsjc1zX0llgfPrTDwRix92DZjGjnYR0q5ero\r\nCKUQqNUnvH46sZJ0H5CVfauT2cFrd+wlicBBiz6TFZB+Mtx6BgeB2MH52lln\r\n97ghV4YO7C/FMEEx8w3/6EJGOpRIrsMlr3tVe3xPZGN0iMRsZzZSkvnbIpbo\r\nH479msFqGWmJEA0tyqZR+HpajAMZIJnVHAS9UvKr9AFc3OvIo8m/VfWLxgfW\r\n3gdGRKfavxMkIsWMdNHHYpE9fNnCwqjdXcU=\r\n=yoJg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "752a21c9684abb3425b56fa90d12c3e7a848e9f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-fXLZkIxI1cP/Mk28otFwGGM2x1gWq8BgWEwUELLfuxgNNf9YmmWj1P7j+gJnWI1BuQHExEl8fGx7cU99kGyJ2Q==", "signatures": [{"sig": "MEQCIB0OyjGUzDGUQ0wYNLGFCwHOHDr6uq778pskzmQv5efBAiBEGsw+3byycwmE2ME5FzfOBS4gdFfjatFuvY6YMk37Ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmra6Q/5AAnst/ZBX5cKdd5FLWQju+YY43ctfWUuZpjus9SjlGY+ortd\r\nAY6uB0bqkG1p1BaWe8dJoE+hgmWLgPHAcStNrRUJlXL97kt6yYyhaKfxgR6J\r\nP3S7Bh7Gzrh7VmSRZAvk4tCBb2rOm1hdaXDbM2z1t5YDUD+WPmfYbWBp7rI/\r\nA7kgp8oAtB9LOIAWForzb38x1B/2oBDNZcvfHuH2ySMHhwJNFA3O5Z/HyLUC\r\npexHMLhgNA/P2p0+GVdPNMJrd4SwiECJEwjozOwaU4T1xuNnAWnSNzRj5/+Q\r\n+6WzPY53VNkkTkHAcPJY/RD37dxMtNf9vMps8A6iYwY978meLeMdbAmoQhH0\r\n7fy/5FKkk/hDOMa9XUKaP6wNaLR854BoYQ42dLmY3FHAM5zOt5utFPNl2zhk\r\nCyYjRLVfRsf0RzKBjuoFi1kQZEgVw6Wr/EPDvvYCrDm9MNBIpX5eelBjLeVW\r\nJdE3a2mKVAiW+R6I9/V+miJGcu/LExt6DfM/m9jyF2Xd4Q5f/WNxsU6CDZJ4\r\nVu7JYqcWwQtMdfgrPZHwloXavc8Zw8+J5ShyBIqs+wMbT48cWfAognH7q3ul\r\nVjA5A2nioKXN2avly8pJjjQA2urYubCpY0na9cbwlAVEY10gAd7EOPWsJqWX\r\nUELE6gB31XFefq6dFpE85X1ftIazJJj1Nfc=\r\n=ac5H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "43828b96d981e98b1af3c4c9673e406d11031a24", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-+yBm0pmo0RdtntoVMVK/IHFI9eyzlepuZ42UToZffmZhUcdnX2IVRkrH+c2EiT+KB5XbxUj8iVplBaJbS2/Giw==", "signatures": [{"sig": "MEUCICtogD1j+qcdhwLNGKT+qoYWhq2Rf0s9uPH+/c/gaD8xAiEAn6OuGvC9zQ3XMr9WLVuKnA5xtKu3CFwy0NXe/8wjok4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile10ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+fQ//clPQBnHYWAKYNjNAeBi73a5d7qLWDgg2D4Oq6qjB/y8bw3c6\r\nz4jqhrmQziAp51h6v9rS4mjWPP0iJUVQleopg//6iyJrGs1+AthfxSivQCme\r\nWodYwKIX6+UkE641Zm6d+3Ws0wv/wG3BIUERlfRY7guGe7Hom+3GpGjf+EO/\r\nGYiAI2LZxRsARzAKN8nFmUROrvT7b5lVJT8yLlLFQGMfSl0nzxXhb1ubZ41U\r\nqfHwBIjlQBniEVhQdSzjRkc3cU2UbPvG9y3y+1Ga09GjBj3AgdKVwIvd5A6D\r\nnJf1ChwbR7CTC1fFV31Hd78yOKspzauAS4VQ5rUM+KioWe8ToP+13rtDZuw4\r\nVYiTUxCFPVyA+Zog/lNqXRWuu4NgeXwHaDR6Q6Mg66eqpHfsYPE/Nm+ZVOVv\r\nT5fyIgN3e2jHplhE7sz/8TJpcmL9hJLFXhY1t5DOuCYDE2qn1KsmHI+7jWRo\r\ngGg+0P9SnuYS5PoxoQt1Y/urla2lax08FrzptvprxwGjKjvrc7fXkFnXdIEW\r\n2kbwaHFOD5upaWljnkoqM2kVvH0dMbchCkkCYDI33dxGt/I7muGEBOJRwwQ3\r\nXGhIKqZO2viZydweeicendYWv+gwEnB+g+JkgZTr/j8A2cctAmMyWPU/8MAe\r\nmMMiQLasfVVOz3CdlAXcZW1nz/7rb0TInM8=\r\n=vWPf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "01da487e1788e60e6a3bb5cd351f11212caeb7df", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-SI0PlE6Yw/hDsi6MLAr8kxMA6SBx2+ZOW3tEyS9DNT/fGxvec9/Pug/mi4U1yjCbq4MWQ0HXrTOpW6lmLJyUmg==", "signatures": [{"sig": "MEQCIASBNvlMenrwI1yMLLLrLpVjjq9d5rYIlgF1SVWJTiAsAiBZlr6JNN5MF+OoiwotDyqnsKR6EbLTLmcKsncMsZ3cDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrmlg/9F1bbsdo+D7/y+xokbxLbB4dSvlf6IaVt2U3HclMV+uTfbNez\r\nrl2IpsTuOvalenqkD49nJMwhTlx6jENcpyeU0ESD3ciS0Igf3UlPuxn2BMM3\r\n19ENnhY3WGBNmlSkyoAy8kZb3zibAuSAbloLoDEaPecUXTfrbenXKJTZQyWS\r\nCRI9mML9xHMcQizGE2tMsHl01e/vvP+jdDawKw/Gh4LVr0WLKIb9AmLmNC3i\r\nryPO6LOYBMJ+T+TeF3oOK1Y7HiuwC0k8dde0LI3cYw9pDQrNv3NjjItQgSy1\r\nWhUTfMQ4545NTAmSNbqVYL5qctvTB1tjoYrW1leNfMTWmZA1cvTlLupfmDMu\r\nW/hPpdg9y9txWXUkkVy53O/j0YwtCyi5OrCpdbNNccYGwYEJ71y+zabFVmJc\r\nYZ0B9ma2ekFX8dNq8TtGWZmpMCAYHSEP5fuI90lOMQ+4m542oWr5/mfMWnS6\r\n+PqMGhRcITstZuDu177Osbhl68RupMYyRKLn3rmCyryP6L9V3VtgfG8yxtbd\r\nkqHi9Zg/8xFBQ9WAjzK61HZS65mcxYeRtehevh4oCE6UJ8w9OrfDp3uoswAT\r\npnthIYJTic8uu/k169wVvMJWQs29Ws+eLs+YysVvXtJ+AoyUiiTAB/GI2Dal\r\nuAATk9RQzWh1MroKG7TBM2yLfIeQnWmVPdM=\r\n=/A6A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "50d0bd56574350fd16a2496d1b044fc7cd551bcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-u9qZemzZjFBo1v5nN/m0dbDibmkW6oUj6F0/Hrm6H2u44e6+WDEAe72SGDxQc3NPNXIM5/EW/ZdubLCQ6z5ZDA==", "signatures": [{"sig": "MEYCIQCRGfssS3OTDnCTkgyGOh/t51/mf38/lYaK536g82x2WgIhALY4kHCnLITIoaz/VPaqUFBQDN6E0z9pvRPzN/JBxKCk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEbg/+Noij2/SMLQO+DY7lAyg/yAW1xGs3lingCy1RinOI4uUZ8HYT\r\nXXax69QBCtTq4NyqQAVLllrZnqJBX5QkcVFR+TN7O1suR5+WM5igflzy1C4S\r\nr46jEH4F6hQc/SZIfeswfpy7ncpU+OAOiOFXhogn+PfM3VnmXPRPX8HeKoHZ\r\nlO55mOUzroJ+mzEGDvEGTFCsUpiHeZzZawqamgapDuAFO5x82Bc8m8xUDeA9\r\ntJejqaZHURg1EPaNy7NlpFfrVHIAsD+fcOC8l2XvpeVM2UgAKo3i0W+u3o7q\r\n5O8QRrMnsECQc9uWDfLLWq7cp83Kh/8I4d5xu3AgGCXqQq9GM57al3hB+eUp\r\nbNWaQclHMeSnlYPGpXFnZSw+ugF9DiUDfjk7Rmgwcmln0gt5iLYeKV3pbUy3\r\nsUC8ixhwp56R3n6KTMaY5PEMjGTxBHLyewWd1u8wLjmQhnh5BZZ7j6BVIuxd\r\n24lKq1fwHFCxorgF76vlS7vADVSC292t2Tg9n4687oQAhIZAoVsSsEPUUdDk\r\nNoBH+TAdGe89FSxTThhZWDnPpo1jY8RHCCzMIjcyHcqYZB06wqGTdnLTFuhF\r\ncREcZ97HiQfT+kEbLocnWhanzrem/8Cnt3DWs+Cn/cJwuw3N7oPCQv5Phw9J\r\nvEI32GfDTLXwWBV/4IJoa3RtnhGm0SDIuwM=\r\n=T+1v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c6638ddde40acdebff0252d251cbc41cc599e6b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-sizuh7bhvmOgnpZ8ACFUBhtOMlFgE4MHiblEhAPkE+5eGVNZfmmTWzMf6hhMLs7ZDJpPuPUYRiRuaSOkH/Zc1Q==", "signatures": [{"sig": "MEUCIQDkITRFWjWiBnh+TYpFY/d+m/OrUkfk50G+ztq+Cqm/BgIgB4Vvl+qQwKOys7Vj8M1D4BW/hdQ61HeC2Fao4wiklxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvqRAAm+fTlb/0fAeh5IrIlmp9W1COs5md+m4KxcQDbHz/uTk3MdN0\r\nqOm+pJwcza6EHwU0VXQ17tmsaV7s03Zm+zrufF6veeQFwcpUvT6YTRT0by1C\r\nHOOhVzTH5gaz5jjTDGGlTn0o8z7PC0jPRI/Zsl5DfEBFORUBYq6OvAwuGH8h\r\nTdi2LlPYrCJZ04PIGkfS1tBiOcqgicvuO7huclOHNl1Peu9K5sDAROntLn7C\r\nA7kcCZu5FoxN3Y9cUtpYsemDh3TE3fneqF7Iq+qcsZnoZUNTx96D1k6nv06D\r\ngygyqz5mlGuAYsw/ttOMYkMnrKif7x1rJyG8bLjf9UxsBEyJasJRkD49CWWa\r\ni5JsAkXl0XURN6c7pP00JUU39EAAWOAigbWrQx0WRUR9TNm5gc1xXg6tlzuX\r\ng1NrWH+5+h1007Vw9WcicwZx3nol4j3cf9Hcajlm7P/Y6laFMOLe4hlo8++0\r\n50dG1NlYpLpyhhWvsBk6tZlPNeCP75wxKFgA4Eqaw2xZAW3F6L9tcuRNiqFW\r\n0Io1mAF4MY9o8RwPloxji4l9V/pgTy2p2JUWPgdgItCe2yaQspbzpbvb9Vwn\r\novifbVBUYnI+xy8M5srV5x+n3OG6MsC3C3jLNiOhuzqF1a6n8M1V6YG1c9RQ\r\n51gAS59rB8vhksniNjVembEpqpJdKSyppsw=\r\n=V3Rw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "66d6fcf197156d0bc0eedf5920e836b6e4258450", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-nlbCtdyIFci5kZhKHKsKysk29H6aFnciR2cTC1BNV7YGvIW88x9aINBOUBMZqgoootKzCnRLvQEbnl/d5WBiFw==", "signatures": [{"sig": "MEUCIQDuYiNQjcUMqOE9MmRyKwSYUjfnPNXAuZmaOeZ1m/GD1AIgAb1wG/fhrySqmMFXdosr/ZpIsl/Fb49QI9ktRJCjVyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0EhAAgrOHhXRGtNOImZg1IK7754k/pAe1bzFS3A5xx+b50r6cBWIT\r\nwdWjql0t67sNpOv0eDuH6PaQBSFetSam2UPHa6d+x0OnG36EPZP8jIZOrsmQ\r\nnl7CYD+PA8x092swBrwEwx4Km1SulRupmIxsB3vQid1w1FdJqDSZgvryRoQt\r\nBYtXzybywf0PL9xfGefE0orcvaiEDjfUwxZg5CvYHxiPDEYneeZLkXqNqgKq\r\n393eMAFCOfb1EeWCSSunCCBeAD/V+RDmqixE4FmGISo/0A3o3YWcM9bIOQHQ\r\nQsYLOLniByLU5fPMgeYhy0MwGLY2kYbgZtNlfvP+3EZFVwQhd37wzIQasqaj\r\nwlsFaD4l1bE6NnULg6M4TY3H/15eMlWS5JSE2itWAq/8b2Kf4zjP4263V1Gb\r\nGB0E+uPucEupYnawioBumSzdOT4iIUjjdCe+bAtNB7zADynxsK5n5S984mQP\r\ncw2JQQa8+odkHDgSUdc0BOP3dqhdVxVy6NgSuHYgQEPOG46wIN19XVyWHjn9\r\nCTmAJkToXHQzta2UpEcqu5UebMKyGNgA0Utfp5objUzO5Hcdy4MaOPxPzNL0\r\nrm1/2UWTTr+AbVnEmB9S/SkDj7bvRTqlqdLUss0dfk2aIJNDn/OKL1mnJLoR\r\nx1ncXyUhGK+w8KU0zdKWqHs96/mxCr9zWFc=\r\n=VkmI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "32d76e76bef625da22091bc21b5fcf0c29f065ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-HToyq4x7SKywfV4cxUOaDwox5Dre3tT0upFksXK9SmRv/05wHJfmA7uxO4D9oFA9fW+MRTvWg+fKVtBAtcUf5A==", "signatures": [{"sig": "MEUCIBX6eqFyBd2Sw+BwH2qG0hB2+IabZKRYb8NwwSaVAQlAAiEAmp+FPNR/eHffPmxRDxgkJo9aNOxxr3Xn5fMKTIw0K90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotHxAAmObEVNUSdIMQCKfR67TVrzh8lTKt0qM6ElaDGy89qmXqGJQH\r\noLXU1qNwUBII7gl9umhc4X97kwYbqLRa1Uf8WRGRT+xz/rPYo7OMJqB3wJML\r\npjyhSoIAIOHfFFnVgKz924LjdJCKcEuYsvobDMLrYRDsz6fznNzFrrvGi+ej\r\nxqEOUes8F9+DIMDS5pWWJMTV2vPM67271De/9VLeWyWSu6Qbo6Ukrmao8kPg\r\nXRtpEX07BgMnFhJkVLkxO7zA/93AuyxmR0VmbRMlwuRPOHB6kSYEN35RErvI\r\n/004Q3zyILEj/oQWUWKW4P8xk0LTNsxQFPzDGOwIxdp22zsnqOd/VA6om2nU\r\nnFbwld6xf28Pya+qANgW6xEx0Cks18Pg+YXyWN9gdQkv1LyjjvoDqFDQuTBG\r\n+27INwf9y4/HSswFhzDnpl9w/91vOUkmnQcfawgttlBdV+bAs0gigq+upAU5\r\nJ/OZV4YteMC8ZfZsKsUtuqeit8yKm7rj+cDpy0DYjId+R/Pe0WPAuozu3DCU\r\nlHpfXic7Sic+PS+9xHc/RdXlmv6x0NaSCknrC4gNM0irKvmY9IJ9YsLTe6m0\r\ntMoynHp+7AXD9HyJmf+dlcyMBOdKfLkkuKR//O/yA8fdI61yc08a/rdj76SX\r\nH4e+SRcpsC1hBDWgwJM9WnGUIDHvMLLLWnk=\r\n=rKGU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "900baf80f28ef34b1f8c1236cb6611c3a04e0d64", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-Aipp2uUAsH49rQmTqCLIwdHPdN8Hp7IW9KoN8smQFcENuW/PQqD3sNZjZQjWWwVZUxp190aFCdSg1T1iZjWMbg==", "signatures": [{"sig": "MEUCIH05yVySA14qjxBDBPYLsUIpGi6+BqSNSM6hzkHCg0/IAiEAoUBjZiADBV2kSHfepLEbphU/p9Q6KfsCBwaHSsae9UE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMSw/9Em3dOK6KjasGHYUnI+Mnh4qVgwP4YQHKtEAJcNn8gDW5hilT\r\npYTYo8JrEFLnNjymANOZxPLpQ+ALpwIukJvxvh8Kh7J9BGA7AgnWbatzZ0wl\r\nBk6oPBeWwdEAPJ7Zkvo9KMPfyLlzvpMW2gTRV0TltiwMIe9XCVo0Vt+1Vt4b\r\nq0kY/PX8ILXf6GW1Webpjk4lvHrhP3K6Xf1rGKtAa4IqC3ZrQWBXez4cX7La\r\nbuBFIZloWbEzJXlELBW/Fn2TquA03pbDZJsSjMaHMLH2jV/994pmvMnHLRcq\r\nCeisw1S3kniytfLZB8bEGCOOabZ9ECjdtZ6TUXseqtKkV40S5U6l33/G6r3X\r\nfw6Z62Hf8BmYn9mrnsH3JFdbiD29PstiJjN2T6UFsc2pQkj8UGOK3DtCPGq0\r\naUzhC3RufNAs+CzsBJShcv5DKxCCXapVYzA1bPNl3ZY3fpieA4+EFyCfjkoZ\r\nrUWswUvGEYsYq3vf2NLmoyFIATUFBAPch3sANRufe0xk1HJMmlFpd47eyt3W\r\n5WZ4gbbq+rH4wVPXJkEBVsh2IP86fe37syW4JRtQ2cnOyQifAmzYShTNsELz\r\nEbs03TqoGSx3hboplIMqtK2ym+2jMJjXcO1WEzbrA3s8jNU8GHS+a/1TXMOs\r\nd/2y1rLI0kK4swn1+eWwMBSVeU/d5Rk7xxk=\r\n=Zn0R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ec344e414f92aedc32bfdd2de84f621f072a54b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-LMcYls9Njh10jqg89rCObMntWQBtKI5/EhtQvzWyE3zKc78IhODk+DN0lKNmB2i4rEjmfY9z/CItmVvfcl8ViQ==", "signatures": [{"sig": "MEUCIBXAY6vAjfXRJKiQusSsntlFoWnrt4nPANkVuUDO1aBGAiEAlZKaYm8Y+WlQGQS9ISDSWEjUWs0Cppd0nzIYX8kA9HE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0neACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoraA//eve+L6gZNMclntmxCth2F2C88ahGZ0Y8Uanpz/PNXEhXgyDX\r\nCBlli66pLfqxlAUwtOp0iv0k/NAuH9YLTqvBuTb/QDDNYW82rJDSGENZTxYi\r\nNhgwrI3H0EUuI3RSgxjJeG6RFOQjCcTcZOM+qPR4Js/I8FuEDLB/cJkRkfPH\r\nnEF0ysGjJ5VN08NJdEZsDR6PUIY1IddRa5+G5pUoFtPtojJCYgJ1EPl3FxVW\r\nqL2NomdwhtqloRCvdABOTctnUmH8mz0lTJjF5jV/n5HCpbB0tCowrvE07RnH\r\nPZ36t/cZKLZ+9ipgRqWQ4nBhR9S5Pb39nfIaXX5bMpu+YdlcyxxdfIE06uwk\r\n845UYJhZkeGJycJENA1Yh7ojtXWPWDuic6z2qdIQXb2fEHO/2FWgQySohKDw\r\nTPTLNwPyHMnM9MQpnKVISoAuBy9hrrk7q6H++0WoNx8f4seUlmtkeqhUSmd+\r\n2je4N7tXhNvzbBNjxyO9XmXePsDrLHGr5wNh1id02BdrKq7JXDbuw7ItVeDO\r\n2ZA92JLS9zGlYYlKJHvuS2yPmnztlJkC0nlWJMrx8A5HuAztdKnMc2VJYsWV\r\n6NT99xB/30emhPYtFrvmmnqErbn7C+Q3G1vt435ro+lQDuoWAwTdM+x+aURA\r\nL+hv+DMvOuaYKInZpPiPlWFpX4b0a2VH2fI=\r\n=vtkR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "848df231d39d98f7c911e27a2e8560bc1748098d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-PYcvsCr2OqtqoA1S67vSghKlbyBe42dJ/c49IDw/daDuamx/CCYrVYnE5d7Gs0qAYIUeSAA3N1Nf7GSz8ENgIw==", "signatures": [{"sig": "MEQCIHl+J1OTjgQozYgHek0leaLVTSZEvxsCq/gTULk3Vv8GAiAZije5+zjLQWuDa0UtIaFsxvAXTdEC9a2Ns5XkSCBwUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5ZA/+Oo8kZwNZlPuQUas5k+JE6iEv6JMzd+vKV48gLrn+DPZztKCr\r\n0uWR9JhmeLnX7Rr4dlERy/y6rIoGNbRagBPerirKOHO0injAb7XLGwa8Z9ed\r\n8PVF3Fn/Roe9AggsQesmSfqnPIyjPV8RnYRmJg9/6LM9O01Dv5DNW9wAJ0rU\r\nDA4keTaHt/oEmRX27NC0FjBzsJA616HC/5tR4SalruYftsy9JWrkPtcMVgiK\r\n+hdWnQTvQ3rk7aoopFOzAGA45NX0GRessk232IUih8oegM9zeI5O1BjmZ3dV\r\nMvKUq/fhVYSOVv9pJrh8DN9hj8U5tmEr9WASxbvn+bFAaEzUGcyNQELyt5vD\r\n/Ehu6/VErz3+yBoHfNHglWDVJpQ/LWNFCh2fJkNVCHVwZCdxVuWzzzX30Isi\r\nsnElvRA1j4tVORSUgj5wUSilMDh0xHCZ589mf2mIEs89HNocxHHNhm67BrhN\r\neseDbsL8pf465Xyy6et3wa0qEhTlqoqIVJ+A7+toSqKzKQN5Svd4LI4NJ4f2\r\nyWhHoflFdNqiey3RZEHoDGafWsozz5eoRoLq9trtX4xZuYDPpNO3kxItexD0\r\nogqw8eQybm+5owQ5kP03R2RoIxjsPTNROjrKQBxHIbDvQUBvdSUYP7yaQXaJ\r\ne+Hl0c2yrsayp3h9E/K+xj8mA3WeGbmxg18=\r\n=HGXp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "046b80a963307fa0b41d3beb44f1efd9d0853263", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-uVwkw7QTv4oU8UULDoAt7PBNCUZXqEHdXFw5BaWA63zQIiU2rZJsAuKtpZNu0hgf/nybeaxIlLdR7rUkdJNinw==", "signatures": [{"sig": "MEQCIGcKGxhJOnuPeJ43T8vb9WOTpLPrjzTZ2CnJZ+xp3xmEAiAGPK5oKQsV2Zgg+P+mCIcUm7fmUxNUY2a3/ALdbK5RgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIOA//dvkV/Fui7H4qO/+RugzjkIJZc40j9/9cyIhGTBj1T9Dz9h14\r\n9VsPYkslt1M4pghLEO8eU0FOoh42xx9jYSlwXmnTGPQ9bETfC5J+4e2s+Mpv\r\niut8YuCs/QmF8dlbM7jHmLMYhyHOArYV9X6j4vwIvuhudnZypZPyjavFYjSR\r\njL+8JFhgEr6d/stxv0hiTjEoQSZtqO+nDuPaMd/7uybwqoSuzjMoev0jTG0Q\r\nwckX+1RgAdCze+oeGpvl+D9NEo12VbF8t1MHu9LL0wt2mtfqt1j91BrvS+2w\r\nQ6hlVoc1XcBdBg6gqxgAyLXk11UuM1bZqr6yNhn4SUIiXxfTLPsLZ8HToC07\r\n7llbk5QO/2Bp67DD+jZLGG0fSIZzEybYTvjHocxuYuN5ijdjufNQAR61erok\r\nVjLeb2hXcnrEzjJEg/OXPytwUp0PJkfI6ZdiHw39O3TCf61DOxtoy/AKSADX\r\nrGmkiJ7iiyokPsCvrDHCvZAzpeuhk+T8JSG8R8lqS8Viy9SMtDhkMErIuPLy\r\nQYEKvEPLoV20cO4YOH7fXEInkei1y8VP59L296b7zaKPlnE+5S0mwUycTBy6\r\nWF+KK4fRPMcC1E1/14iEIs7vOdIIYbE/QbXpoRwCQEt+fF/CriI5gP3RXxPS\r\nfFmPsEi1FIJtxJjqjk5zjOaEt6Om3l2N94w=\r\n=YrcS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "042726213c4c97821013113db47618f4ae0e05ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-2kONZM7It6Tn/o2ldXaGzyPB6gw/r6RbdHmKANrXgnBl6HRS1Plb2dttVofVzKsCGsQZFJ4xZShIGcI6QwKvxg==", "signatures": [{"sig": "MEUCIQCbW9ieq13suewdVPcMhtdVFR0+ke6sVnob4lr1RJZr0QIgY0k+tHc/5KJCceEYzDzYtDli7IZ7Ccm6xwES4uKmJBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0ViACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5PhAAk4u8X8MERu2py4/Gt2FMR9Zzwp3nYXRnthlE3wGfO1exO96z\r\ncN+WMO78GiGfOjlqRfQgISuwShrseQ6PI/zl0+ygMfSW8D5ij5Y5ulffik2u\r\nbiW59sm0fzeTRgIAHh8FHVSzGZ/Rh2tr9G0RUC0RaEzAfFsgB3dgLkDLLHkj\r\nZcZ8cf47OqVBL6CX1ubXWVIKCgDxSNA3oKxiLuTv73IFSHJhwzW9QPVgzDLn\r\nxzNUkZPJOKYpJwpNYu+frNzbNh55ZxogMk5ctqn4u1KAyOuiJ2Q+B4jkMmP0\r\nFFhi9cGz35FvUO0WxfRYdqJcn9eIOZRr0aR/ZqXfWyvCKE/OA1S0VsXi/yv/\r\nMrui8afhWMK0ffO3W00KD7rymWUKZERsC+MKrKulsWRT7z4drJgJUWFrl3XQ\r\n1T4b+Gim+MvPEp/WWiTlfCXTvfp1fiuuY16bSxnr+EWhNEIOz17iJLLErN47\r\nuj/d8Mk/qksiGjXmvkc1TgvCcSUqg+Uyzf3yYrgRFScXAaBh5i+fKAV1mvvn\r\nFB9EM7jZLBi8uNJltfdQEi164YgYR0/zpX04M5dUouO+kTaqsc2CMht9bIs4\r\n9jfSWGMHkO7ArTPiJf//O7Me0oxIlZpCJRmb0gMuVPSAx0uLV+Z7rPWTUGAN\r\nmUHFftM/7kReIKGwADYEkMCQOTRYsqrAL4A=\r\n=IBPY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b7e00b52fdb0ef76d139bb63d734a5f8aa23305", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-ZfZ6pKs9KCaoRmQqjYbklc7glQrGynwNryFQ7y2LUY9L2XeKH0VMvCWuWjtmCp4hp/H5ZvZI3KggOY5PsiIxRg==", "signatures": [{"sig": "MEQCIGUYXsuM1htRBPt8ATOeTtLySI/GEyUoFqG7rdQN4cwJAiAjJBySTefVbx0kEqJOc6sutFPuIVeWNDDYczlnObLSfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQtRAAk9eNxpoR/uhmyEaXrry61xyQw0slx/LjgqSmQYbakYu1n0a8\r\n+LxViYOwpy5gEPJeUhqIttqq916kNA/HXVYGaWW3w/OGnAHNGb4pTIitKfo7\r\nc68Z9alYC6PBl90GEzEPc9QuHgre4lwD2XAC6h6jyiUA+hoHL4tVMQexcCK6\r\nslLsaGN7Em9O20l78g6ySF/1DNs/g6zKy1i7zvSpKHwQAp5XVmvuOoeRxuxD\r\nIApu4NTDsDT/Cjwbskqz5ZWyF6ePZ42EVgFWOjQunfxW4UqRjkVTf/oNNSuD\r\n3J/1ISOGTR+tzauD+uOCsva3N+OkHKL+EbBu9ssTlneDjlsqwmdST4jHrNU/\r\nJx1D5j/FFuI6ZfEJ1N2Ulek0dH5xBHx2HVN8YxkUJoXEQ5kyOHO0V8IK/WS/\r\nMPPxD1OQxvG44ozvWDlllQLP9kWuzf0Xu7U8GZwyD9al0PByqijtXxGPmWMv\r\nhjgvXsdEewedoOptLuQgkY8ooD8aIWECEvAxAs0+xaJcwAy03rLz7i88Hgna\r\nQTranOplzyxHC0VB8ZiG478oYp9aF8Uxv/mGBSMTymT67HpA9oBp6dHtNwNJ\r\nrIEtmDnZ5s2mDJWslmgpt1pUT01eU9lRX3hVXbRozwkWqdITdLZ2zBALsu4g\r\nZev3n0MuzrDMmUHCuX3v4mQy6IsmjXsfvpw=\r\n=tmwp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5f3a52cd575c0aacd8740633fb40e851cddc3e62", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-mNPU2PFW/2lExjWUsDCaoQLH6O87HqQbBl2XCRrGPkjZgzdxE0jpmYsJU8mnsSy8gXvnt1wIBOEojJSbe5V6bw==", "signatures": [{"sig": "MEUCIDt3eSqmbNLSr5YE87YjXmnL0jgzRfQffpQ6A/BigzCVAiEAyobLtCWh1RMZHgJFzfkCHcFKces2+yUWDCh7buEnkzg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7FRAAjnWJ9hJQNOpSmEHV5LtFavK1WiHOS7RRcgi57DzwnwRhpy9l\r\nTkgxGU1tXLOcgr56mpd47tI2Kw9Vr/iCqTvbWs9Sim4k5sZKshJDAHRpceUO\r\nCwXx5k3mMXs40ZYC9Tb2F2Ak+Wd01IkYzX95+uD/0aUU1iUzmLg/tQpAJnEa\r\nk3Y6rSYSrjdX0WngYM+sh461cvw4OTegNcRxtrTjNCA5K3EO7pnBjDeoOUzo\r\n4yKtZPL8dmdjIj2jCQmi8a1vE8lnBGosH9WDZXvHciILNYNcoN8xuxqr5XSl\r\n3bGjhISmVLuVT2urUCP2fJWZnZeCzEHCAE2ZYp51AKzhP/l67hDRn8eDMq7s\r\n8PGwtD/g3K0FdoZqXb0HuLbiKUI4FdKzcRg1FjtktLK6ne5giqlcL+GU6BSZ\r\n7ixHe+PaMMg2SzZUQ5aAx+Rg619A3xDiBOuMgYcJUuUrmhf3xzMYp5ahHCjs\r\nsZHiuHm3WF3v7Cj6KneVVdMwubfXKe/k02ELzKMHPsOcUhcnzpxjRz0wgKhg\r\n1biXJLKghYfnVEPXA94/DjQd5/412xXqgQE7bQBT2d4K3J5sR8+qPuMRN+kV\r\ndG2VbSej0Lb8UTOxBQ33zv/YP4lTK2Tg2flljLpEuYS9JrwVTSTCLj1t17Ub\r\naUXgAxboAFOiYyqC6Kel7IYEhkyPPfL4Hu8=\r\n=/pC/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bc0b5c17b462506a2bddf22fb36f2b158c3b8318", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-seIXNejltUHoFeknAfKxK37X//ZAYSS0pRTqqhRUXPyeuXYWkvZLebwm82riHupMn3S4j0J4kchMwe2H4q4O7w==", "signatures": [{"sig": "MEQCIDEerCfXowudYZ93TA+CIc7jpUM1mTmjdFuy5/VfBcD9AiATv4lVQMfZ7RzgsABSr5LaBDrXkylsxaY7OpvmCRC2UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7dBAAn4Fx83NvhPa6tesuSjhfe0Zx8gLXN6Vyj3h17JQKo15cHdCG\r\neDTltNrHtcINJ1EQvAapf6SiIvfG/mbeWS5fzMwvm+FaLo++BXUFAfwXnZuu\r\nFO0YyaGKjPTfkajLqXBNlOUmXQ0z4GxkyqXKrdmEYiIwMTbVosiHZDxomdnd\r\ngh1Rr+8f+CWl+JqRjee74I9kWJixM91leNeWSRWFdoUrAYQFmSrvPfBPpyP/\r\ny9JaVtVkzw5LxQ2W11DfPHXJDnJldzM36mI3IVjUZq1DJMaFSaW4oXgwSiTM\r\nFFOa44TgifsyluE9n5sRNNyFwNaVVYAi7NBBLrZ9mWYH8L2XJtwBScuISOYk\r\nB2jJK4lkZ9ANDwur8baJVYHVP+zqJFmWD0qzCRQuKiTcxEws1djePcELPBi4\r\nN9CQXjJfg5TRoqNrVEKJkTvMre3z4NdxQAcETe3n2SAM9tm8zLFi/jeHvsNJ\r\nOAG9JkmtOL4dbTr1r0Vc+e77D/NNqWEQ76guXMF91yKyOyxbI26PrPPKFyLn\r\nbqw6US208e94fBieQGbPt0B32zHJ4DY2Abdy5JOsYtXvQpxmDvjTdraWrhSL\r\nMpWmdsIhsRJp7WxHE4TkDYXeF3pEmJEaBYaaw+TPQDNRxJSD1f1saRQhU5Cb\r\nzpTuL1s9Hq7rQroS/7JGw7arZCx6lu/i3A0=\r\n=+qTW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4ba402244569b5a7bafe7688bba362a43740c845", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-T+xz7xI1WsCYvMKMHKdYQegSkSkyhZN7k4qg1vkUfKxxVha+B53jGnM0XEiKGiW4RGmS9gl2EguUig6zFSUlOw==", "signatures": [{"sig": "MEYCIQCNVjxx8WFX0Tzjjja5YKSd5s3FM1HcUHIawRZj6v30tAIhALo5Ar0p8v+awpcJVQPGna07AatxXSU2g0yGCnRkLDE9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpIg//S9PlxdCRisS2UissL1MYS69eNv5C0uR03+TJmdw0ejM8fUFa\r\nwEGbdNY/tcN3ujxR35NSGSXh5qsdtovti00tCxmuGKoMIku2dCen2Gq+AGdW\r\neIjKoWTbN71rxIfS+lMx1UQrv8M8asW/Uxl5qOFV+PK/OYQiL7jETjcsPsB2\r\nbZmHNNw/3MRNTFCrIXwmDBymwbG6jeGbvWwCAy/gW6GgqNzIvHdCeJbr5ePQ\r\nZiT2kuj4smapLZvOxZ3QU8up5jD1mZwoPsq0QcwinGrttAsHUty9FXklQQJv\r\n9JyWehgVAz2U9ABCv4Jzrde54xJJ725Uhewt0nKTVN48bZkSAPdfrM50bagN\r\nrXaHSn/su5OluxRIkxO0jITaR94fT0oU3WhCbBFvuZhgFr9CmbOS6szT+ycm\r\n0Gnrkv9t1X809LlCWbOHjTuneqXqyyBnSRXr/YZvDcaSQzFZ1lyQI54DdZA+\r\npMIVGvXnPrRLLqqZlctU7tsY6GSQHKRlD+QkMeAeZZBK8sOiiYp7tUriIK/o\r\nWoRcwLX9/aqBjK3LWa5bYXmoazaN96jNrPHTHKksu4ymnI9ek9bZ3tyDjrPO\r\na9I3Ngs8bCwjGdfRmlagp4Hli2Sa52wyCK5Tj3ql9SXAmV5O4Kgl3Lg4s/VO\r\nktwsk9HKI9BjKhvf60JaF4T0U81iycJTplE=\r\n=+A7y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.45", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "557224f4ba543a225db2296c3986c9fb89551ace", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-/yr4xpHljPlVGW5A2HkvlsibzgCM1DO/KK0lbh8QQQ/RvU/8o4dUKagr0W4OQ3/65hJ1iEyZ+dNSw11RTudt+Q==", "signatures": [{"sig": "MEQCIAmIP+FqkVmo6alurHPuehHKRUrzdzsHYDIhlvpLAnVAAiArCP6GI6gP3l6vbAI4+PMNR2tZ1hAfvasxFf5OtkTJmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq83A//SAGmlKJgOZAgYmI31+N1lqdggHWdPW7H/wDWXUlbWfUQj8ev\r\nUCt6EfaDpxyGk1WOLbPRwpbTwadOLG+5cTMyGJFvZs1qxtfZLdjqtctX5DhN\r\nakMxNBVclo27+lVAQsbfHhh5BFBRwKHVriEM+1aa7m2npC/9ppr1pq4JxaXZ\r\n1gHBqfCHPnW7Hg7Yl5L+jFMNyef8+AwSIzE+vhSClUmVxpkjaDftZgV3nHQa\r\njwbXXXgg85KhzHOPDLK1bXa66EICGib8xSlXBv3QBOeKJVnFtk8S8kALNAJS\r\nxVmYmfJLJY73x7xS3sGfL+wBg5Sl2y+rloRcLMg5xCU7gLy0hRADc1q39nAB\r\nlCREvB5rzgCimJEQXFmQGScFhnK7BhFyYwzHF9DG2s0fz8o2fhVd0TOYIl4h\r\n/gLfXBSnI7ioYT8kJxzLpu5yDRjrC1MscnnxuqqNETp7G8VrOFjyNK5emQ0X\r\nD9OWVZJ9q4hb2VvWmMn7ikYraGEI3K4pI6xZhjnT8rdnJoZjqoFZ1ww1lFdq\r\ndBjS3HparF9aaxRmw0Sj8rePbe2+3SSJ2WX95bZAZzY49N1hjeaDB+qqJC9z\r\nA2rj54HZ9Jb5lribr1hsi+R7Nouuwnx0oMF3D6NwQNu6AklBaJhc3V20HnX4\r\ny/Dxa1e4qhAlR3jjRXCUCvd4IyAoxp8cJ9U=\r\n=Rnwe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.46", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c3f812c2608d418aa939fe511c3ba28ae493eb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-JoX98V1CginS1OqhBqu/sqyA7b+sCh3oMseKfwQgYugOKrGn6tMc8AbTcgKiBiRrmMvCGThcTmimb9m6f9q05A==", "signatures": [{"sig": "MEUCIHPDCkSHL7b636+wXx7U4+pgPeR/X45YDvTyxbcljX9JAiEAmWRNea0FaW665EPSh3LLMW7LeVnw+k2tes3ANPVObgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrz+A/8CYOD+u1p7ZaUDaj0ewC9aCjyv1kT65pytlEjFEtYoSRo0CCY\r\nZ/5VeOplD5u1gAlIJLP/iuKPa82SeGlfgbhk+3Dv8d1HAPns5b7FufHOX9YY\r\n7PoQwm+UtjXs3iaSTUVHTqW/3dJzkn2RvcLexO/xFOlLaNfaUZJEm+gI91sF\r\nU0m8hF1DM/kzdmNWFAXgR4AO2cF5YjSqZIVDpFCjJq/YVM6RbW1F2D93s49L\r\nM3mG6jvstjJUa/DpM+5dn/GMff/CQ5Px+qX3WoiuAUhrN4jHM/h88B+yWeyz\r\nOBaX/a1qQcEZRB+MFU0SwJCPVzWtZVPQTKWGNd/wZYtXt/yNSZJWabuPKUdl\r\nQkVmT+E85fgfC9M/XHg0851qlnSmHm725Qm+FMsUhWKNxhSKRUCh6XHh6vDc\r\npH8TOtXQ1we9SHwKw+Sbc+fRFIlzaaTwK9vfBTkH1C+EWvVBqAASvBwmSGYq\r\nkBKGfDUaarhyyWDi8/9AM2Oyr+aM3PC0den02qNmgeqz4y12d+KbqdxSBo+h\r\novc46vkXtaguFNcbGpZlxI3+CXhFnR0HPC/4bMA5iP6zpqz5cnfbElpXDmlQ\r\nvjJsjuBZe3iwPJwkYX8d91HrQi1J634OBL+C3dRrFlVAvUv/VJR+aiZvLd2n\r\nvyh42091wxTx77bpMTEw6T5F2zoFKf0cNx8=\r\n=KNC5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-collection", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.47", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "45bae3c7577310f3cb8141b408cedfe11192cd1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-5Z2BNtU4mCiYUjW8GtUq/KQ5JKl1omug09DFoxww+TQha6moF0ejhzEPPkOuWfKKm09zel4LBbOsbo1xB67gHg==", "signatures": [{"sig": "MEYCIQDR5wKLB3egaXaznqwGhqgfF8OnjgqKx/IRMIghSFD7UAIhALi3wQIwC6l3yfzXzFaiR25FDrdQTTA5C87uxjEEVA5s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+wA//ddbvQ0znykPBJC9iE7jO88BcotT3Liu/HXEc9FfIH+kVfOsS\r\nNt6L5RuxDUohGQ72KVdMnEsXxSqc1fhW3VcFN+4UczbCrIbGtgK7I13djY19\r\nE/HNbDOgyJYUwXLVR8QO3lqD310uNC1jf4TPfrycmrgXU/GUuCehC97UwBRt\r\nAPialNfEZWBPhb81rttsyIHWZtQR07o4JSc47AkoppuKMQCai1DWO1FWPa+g\r\nLVDUTT1Z/4tZGxRuOcYBck2NfrD+0QE21eQbmEtQuZiZXkksGcHvmXTaACr+\r\nQnud17UFZhX9LddIDC58pWBZP/p4KWQdD38XiU9aHlbycpuKQXWFU0TlxCfl\r\n1/9T4/HrU7TH2XQuGknWMts/rL/5hTzRVsje4ridmMrXwz/1lybWL86FgajN\r\nlJ+/PfpZCWFLuiNAJewgFKBYPCUSR5+J2oATmJFHOdVC8B5R+eQdPOqCAf3+\r\nHRFfg9kp0hyEqmHqBULVi8OX28jjqIHo45SPlaYcpIqVfNizPESXmWRtefJM\r\nwzyWJ6o99U5s0pMj97f80hI6/9RCFdHp0mcWVXOXuQt37dD6d4F3N9m1F7nb\r\nxFCrSSNmitme78++zHthiQjyKoyAPcWTajy5ihcPfPbeQjATMibpWkk9j+8e\r\nfUW5+9lg4qUv6UpfNMHfdmB6Zsm5lqrXUIs=\r\n=NpRb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-collection", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dad4e7a0a353cba643651eac560527b10a4db389", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8bTk2NIQBn2gd8KlbVAfp1oWbzUluiXq4RJ3WgHFXju/g3Zh1I+K/C9lPdczKp+IAo5XpKH7eFCHT8a/0YH73w==", "signatures": [{"sig": "MEQCIEnI38H87IIiClpZzW5l/SwGbZGhan+nOaqqzHV8U+L8AiAsvb67ObgbXHy9lWCgoKAl0+855627DrLNxmm+m1z9lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Eu3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmryfw/7BBzBwkCZ12szj7Kuo7MNXAEPOVgL8vB9DdtJ1goi4on+ApIs\r\nSRFlkmi5UZ5KNs2E36WY01MVM4KygVtra2gL5tsxf0sepJI+WEpNPEEedTg3\r\nRXwxnBLh1rzqL1FMXfkVl1pVe4pZuiX0/wzndJM72RniUBNyLJANCO/I5ZKM\r\ngJ33GFnK5qxstBIw89Pl5mJ+aie+UFxDau42hHfI3oRwwaGVkWB0QgAmyxoE\r\nBTE/GGvk7ayv+dwQE+tmT4yrKuKwJGlNZOgZeC97+K2N+GzEsxDYL6oSxXpr\r\nHFWcRJJqy7AOyo+TeGHsEKBgF5Jm07mqMQpiiq+PF1wJ8tgOFvfLgjoG4BOh\r\n0hOKsh583eQhJWrxeUTwy/c4lJTtjWkUmamwitDAcVVuaOmm6upMeNJtH/zO\r\nBZSyYhw88QVX8KcP4o1MlTopXkgmAdTVBtEv+S6474JKhthkyJweVMz7QKRQ\r\nFCvsNI/DLFZMrgeyvGK+0STWNHEd3R0g6J8JHiZw6FcgdLT/JkAwjPc9zxWp\r\n+PrVIWEo51ADQp7X1mAN05+H8XKEekzUmZNlQD9qWydEt+2iTDj6/5lptvQD\r\n+XE1QOAUuHSo5fZo78LC2J70ju8+XFMAJ6S49Gre8iOKI2/1xtFfK2INqQev\r\nfC0neHHrMcSee/2wXGlkOkI//ys3UphCnYM=\r\n=W6U+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-collection", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ec4c72fabd35a03b5787075ac799e3b17ca5710", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-8i1pf5dKjnq90Z8udnnXKzdCEV3/FYrfw0n/b6NvB6piXEn3fO1bOh7HBcpG8XrnIXzxlYu2oCcR38QpyLS/mg==", "signatures": [{"sig": "MEQCIAumrbVbIPI7tUZ4kS/26lN4ZpPl4y+u25kPQ8hKdnV+AiBUsQA62+MtcnZ/FucIkKzq1cAkO0wdHNZSUcte1aN3qA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJVg//Zkx1Egyrc92AvmbJU0oiQ1mINOrAOc85kFgjwmX68tM9oqrd\r\nVTNyGIW5E2aBDLcoZ081EjYImPpysG4gEt1dsKY7RzP9KpBs2Vy/AGeUCuwy\r\nmaN3TkeYK6YFSmSsurucUmWcml4HgRHcxJIjqaNwoU4THHWlqJs2s9unny1w\r\nN9FFi0g2vwcVJGfVXgX5RAQ8Z+RcH8Iu1az1j0K+WWbi0cgzv3FFson7tsq4\r\n3uVHxmzZ83CZ8yMOMCWzfu3L6bGGi7Un1sVWAccfGhRie0UJFvtJOSnDJdEM\r\naYdFUPA5NSlYOrHnpRUTf4va6UZPIFzOCAQb9KYbqRBQ9uzChvdzcLfiEDUR\r\nGu2mDC2XxFEqnb5M95NFe2ETzyO/g47HqyJ6B+LkKZp80HveeqQyZIA7Fuer\r\nISeiF0rVGMIDZVZPKisd4Gs+r8UK0ZErMD1iN41lzmmApVIci7mcDCWx4tFd\r\noRPEm8XVt4gNUyetkz1J5IvWLhy7byWLlwIp/gqI06pahug6Tgme19EjsXk0\r\nwmPNEFCjMT/GRgMdQivS/oECwtDnYXL3V2OA9gYUouy9daDi5+7nOcz08Pov\r\nPuxajcnz1O+lZOLXZ/MrJxVbWQGScBZLNKawOEELY/0eJfHABMyf1swa8cvy\r\nvmYh4JQqDlMND9f3NpfhvvPvYN9RNFrwLvI=\r\n=pqC9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37488155efc39d51ddf4208d4a8dfa843b77cd7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yz2Jf2JQcmf+sAYy+QnaBjDiFP2jFc2jCaFWYJKRzkzD2+GxM9yMSH1RiE7HAldqN3aMHo8KFs6KEtfm6+Vd5w==", "signatures": [{"sig": "MEYCIQDUFmraYsbCdiGYfdvJiUzzQPeDhl9cOFKpHidIta32/AIhAIuRihdmRusENQxTZwUk9XD0qemwKSda3iagUwsyK3JE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh6g//Zfz1KXL20WH9SV0jckfTobv6ywNG4SdH+1HxMczwcun/W9DJ\r\n4DDFvIMCdMYqdb4IH+LPxW0l0Qd/UHwqocAmCPDpOAU6RNqqd67R73f3742r\r\nXtQn305YAIWN3T5TbQyeJpQ3YGipOUSqUJiAkgAblM0IchmTfhpPWRAUT5LW\r\nPmCctGLqooA7mZMVzrlSVXp/t8uHfB85sBFNO6K6zXPGcMJZ4Z/tGj1cC4/7\r\nyyEQLocmeIpMJ6Q9tOoWsiBt89Zy6/fAH6uI+bD4O7/3goIRfVjtSC2u2wUv\r\nZHUZhKXgTR7XWERHDqp7oNF8TxV3v8iqkLSpst1qiPxY3FJD6vAPDZnjcrot\r\n51JJnpwpn6MQ3bk1omsS74PbBBEebTQuvwLu16Mfh6DAot9MZyYshmkMBiNM\r\nd2oMFGFNkUPiNpufaG5B/Bg8TG18+pMO1KcJcFJLwtQcvIc5TU06/VYE8ujj\r\nCteZAT4zPH5BLF4l7uGushf54tERiIU5r3ZAdAgKxkn+EWDp3P7Dk82BP1ji\r\nmZdo1nzo0YVXwepBRcRJJAMqBmBsKREtyGiBae6IRjWgL0oYDAuxbIpQmJio\r\njgTzUgLSugLR/xDE6BFbRVrIqEG4w6sXDB8vAn2tHDGuiBGCQDlH/pvJZXCn\r\nD5GlV86bCRkWZZjbomHuFd5iLT7L3GULO1M=\r\n=5t13\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7e49e395494baebd63731f65c5b3c10c0c6a8443", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-s5tUu4u+hEObEt7SicZ99pSk/VLyfECrpTa7Vo+4ddB1HMZDrANboS/qDVOLXa6INLlzTBWx8NybrinnqTH4Iw==", "signatures": [{"sig": "MEQCIACZzJbcEdm/T8tePeCI43QPkXhPTlr3NcW0RsBPG+vdAiA2z3rrskkKbzdxVvn1WjNW0kT5DnEJi5A/NJou8LA7LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRQg/+Pm9T9W+reHw6IRio8tFN+yq6y9asBL4hCuNIVYuKQdSw4k8l\r\nWdohR3k+3jbeQOT487/EH2CnvP3+DzVdyp3hz06fG14KjkNgmNjojiHd5e95\r\ntMqzZGz99k4Rj0BGES138zOtNxG4OCoM8X4dRCXyP/HM1OW4CgaMM9N9DWyF\r\nG26OHFW7p+s6Lf1RTtZvFrLMlCO19sXOtQtYPgoWcXX44ZjOH4ZzKgoSuw5a\r\nwdQBSramqB5DavPrH09n2/K5VW3cLGUNXqMiY0x78FZJIwlNx5sbS0emZCxk\r\njEFo3XJvJJLN+TiIDnkZ5AIHDjbSUADI+7miirsdTgLtN28rkTNKV9uyLPvN\r\nWR6jhHR+subejMnELBnNZZ02fClSsKPR20dLieNawuP4fYkKqnrmMxZEUpPr\r\n5N4eQCHxS0s2uhv2LwnBhHZxoMqzkS/WMWIW1qZzbg+lLu9RVNj7p3XgQGkM\r\n5B47M/03QgdC2nwb3+4guGKfiJ47WvDT4ReJI+dHuenkxiVliKeoQQHU4tI3\r\nLlNDH0BnOEDAld0oKFrTmrZNET6Z1S+NnLu0hb4ODr1+yfuI1b4Kx3UoQTh6\r\nngrhZx+s2lVOuoupfmrWwFDkKVdTWkiUc/gENht4mLuEPkgk4CZO1LdSuyBH\r\npc7mqr3WHlRqIbHn6mKWy6z/SJbfhgwWnKI=\r\n=Ywic\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b71a38f63a3d821cd22d5663fca99ea0f65f55af", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-NVKRsI6+gklH2mdQNDBQ75bBltZpfWMmrUFuDy9k75cdQELRVqE9S1CXsg0932EEoC/wQYsKuAcUVpkgLkpYzg==", "signatures": [{"sig": "MEUCIEDVq9v4+Vp7dX2kUBTFtNwXyJlfvBPIys7y32nbU+DYAiEAymfSFZ/RZSLE77YGUg4vlXgwQgV5p7tfKonDH+hxfdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQ1g/9EPo5QKqSLAWYArLsB07zhpytUky94sppLal2FmQW0JgvMD1B\r\nSa6J6zGWsYrsowSj/nDWdCkSqS/gqWSRYqJniwdJS0m+Z1GGe3SE60kqB7ch\r\nUTrWqtUaGJUw3S3OVqKVp/JjCDllrcrIuV/K+SElJqrrbFCuusfFW9G4OUMP\r\nSjca8KhnnWH2cOTj9ZPjIRTC5hCzwQe0BkOoCjIfD54UtkVfR2y8BbOiQy9/\r\nRS2NTnEh9kPvGvJkbdfatXIDfUo1c3kGkD2msx+XIpJanUyGb1ZX+ZT1zP0C\r\nzaycVGKaAilO6sDj5jJ9DNnjxvlK/5RRWuS9a/Axllhzia5KelhBUMooJirj\r\nsCSI4XnooRneLw08oUsuGYGq7VSNg5WYKAj//pQcAigsCazgqAt4OWu/8iVm\r\n0xrBqQXKtS92yG57bp/OwoGtY77nisEQYQstIX3xXeZnbnW5s+3uQUPe4W0y\r\nLB6nGOTQTKImjE9Y15bQty1tdBYNNn4jMInYvzBF4GBrdz0W/FELKBL9w8LL\r\nvL3uQ86XsBlYg59sVIO42IU4+Rkgdn3++GgJXDnbFNJV6P/rwcmgxg+iswCj\r\nmDVhpjk8AkCRwuWz/Uz5b4Oxcrjv7LldwdYPvgkOXuWCTPaga+r1NDlCdO3g\r\ntHiPeAVR+i4FJCHyPoNSY+DH++75se+y4Y0=\r\n=IZUG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d2d985ad57a626ee190230a1afc9b3f21f2c2061", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ik48Nj1P20eiTbqaMpAC+84b45UbQ3+Js5KAsiySayRxSDGx5gLsgQFBry3ZHTg9ra4uMRx5E008LuTU8etq2g==", "signatures": [{"sig": "MEQCIEzNGFmSmxMVtgkWlE0+WAIFivdgEbI5inopF6YJfHI4AiAO8LBtiFeSoLypJIaTrYIu+qBji86VmdtAdjpUMzAjrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwPhAAk21nBlYgcNFsxUGl0P9kyIpB62WQbnhNnK37CkM0tqpoNoO2\r\nNMAwmF3nBrEamMpzXjpE/QwfY44PBGvrglG3Ncr39lpcB/nNa5RQQ00bUBgN\r\nPCttSfwYHZ8kFVTz99L6mbwlg4Tq4BdVGp5Z9kfdTvPN3s+MmF0lOXInerGL\r\nLA6yX/zbM76HCNX3FQL2RikXcTyIjUVtHZKunu5121Oy1Et4LRERD9XVNLjr\r\nh3N1w/Bs5cGFZxbW+7jwP4Qp4y1R7bYg05BrEpkCihoa2/87CybnN0jZvMoa\r\njJHHBKeGC5x4TBGbllFUwI025p0BS5Qh/4vQ1ehsGQ1eE1dTUO7BLIj8JXSa\r\n8pBKlEhiT0sCDiHbE0zw5Okbx4psjHNbb9rG/RAmzqWDmagfX1PaEpj1a4p+\r\nTMJlNxol3g6ERwH5E+Rm+8ZejZYiLwm0FYukpCOx9x0EmdO/wpJeieex9auT\r\ns50GSO0RhbGtif4D84Em72yNSibV1I9JQwcK9zAv63TSdYtTjy39GcYlPQiA\r\nqZn+ssa3hMZsV4mWc0QWrNd/uO4kjkqSdYE4SEpQM7sNNPf/kuAfvKBhTnw7\r\ntLg+uQQn72+GIOOIPF0tea3HXoYTVGYalVsnXUaDxxEcsSDd6G6DFgnmeDsO\r\nenjmt+92fjkENdUzmnEg8w5KG5/7iLyoSOk=\r\n=7OJe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a944244e7d29ce62e78ab195b266c30b62559bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-dCckOHrW/ZwajQl9ufOa8uKpm6KnUEawqmm3MGC4pIrT7AiNDFxDU4MN7fM0eJFb14KdPv5hAf4jCxF3SkfdUQ==", "signatures": [{"sig": "MEYCIQCOeFvY3bI6HbbrhjrtdmOSF4TGP+f/p4ksi9z0NdvtAAIhAKGsgYXPrgZq9guCXkBhDvot0h0kBZFVgC/F1e7DhIK0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr11ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3vRAAk4NIQwlaR3SCk0hqADM2rzWN03mKhQXSeHKybeEx8Hvw5Xhb\r\nUZwOitUAgs8646rKrUJK9Y8d9+3tb4rlzuNfpMpLAwRMmXW2Mn7DDqFWNSkO\r\ni4+/6nBLK35HjDX7srna+Dr3Fp7mEf7kfL4/an9aIa4ihfQBqWqjfSpg7YQ1\r\n5PrR5Cu4dGtJZQ83+HTyN4Fe5gqvGkJPAcCsrfSEFHswhpiRDsdHkdUHbcaD\r\n9t71YwlYshIIJ5QLio2IcmuNzo8cTXN9jXnxnl0c2JIfXGr/r/z9Nvq0hsPk\r\nf84lBH4cgS92wckGcKUaeKJwDCko3TGD5uQa5DTw4Pg6bjFa3kO3vI5xRXt+\r\ntY8dqjPedlOpc68XA7NsV3HADWfQHWQ4K9a8vsThALtSKoN2EkxKUuF3mrAs\r\nP0eBfv4+9iOzXB2cuF4ur+gcKLC5mkCmoMQfiUxvMzJ3GaQAN9iQQ0Xi+QlW\r\nmnbUhT10s6KyuWDDTwernS0nX58cEn1CoRs5NFZRXhY8YGly6jWytWWOV7oI\r\nRL6Ps37671pAjwzrQyeffOA6S08lm7a9Qn9CqDhdyhjxwff0j5oQSSEHsKTY\r\nUk54hJOLmoGCCrZdwohBo+o9XITSTE1wziGGb/8aAbTi0jTb81iD1yVyaWXi\r\nGcoN4fqVk6WHuu72t+z+IJHnjopnx6cTWy0=\r\n=tj1+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5dac878efe6560ae60469afe576e35ac080fc46c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-lhXU+9zRIiDAiYNtsENYkyHfFY8X2dZhukrZOU3Ewxg55lfCJ6IXFiwk1QCLYcVDjGKwX+5bBh4/0eeUb2rNRQ==", "signatures": [{"sig": "MEQCIAXJ7AobEOR+JuGYWAlKePtUdlVgJ6fbNVcdQ5iQrCL6AiAD/E0IQYVztY3OAEUQM6HdBifuhRD30f9Rujxmnms6+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwO6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpupg/8DkOepcN44ynjwwSL7D0g3kGmitrKcs6cjwKsyS2D8QjWPEa9\r\ni4fc+YgfTLA43xebdYQ20dYEvspScnBTzw7SwrghPkdjvOduk2oZyA53nzT7\r\nWsZFqWOB3rwVnh9dZSVxmuHGPrHgIuMmRH03RhjAiifr6MG42mnLDm673wIB\r\nHkxzqV/IJFew2QMgEVV+x+wFeujGWczpWZFSNcV/KfdMmHHv1hV4Da51NTwy\r\nSixxA+A/qFL0Gg/7pjNZAKOQHT5GngHDqn7NRJUZr2cH20iTCkBXJe+ARFzt\r\n9BG8dEZhoqoxWSrgkTFNSpFeqvYju0kuz6al/4q4g5QC2LLLh9uE8b82Lb9f\r\nH/OluQgc2jovKeuiQerMgTjd4y+J0lOHLHhDAqm9En8k7SkzqH8OI9VAukCf\r\nP0evJLRdBTCMSEpmSk/wMMDMU7VzBbWQ9uO9YoiLsEx8mCfQDIQqNSq3kQAV\r\nilhzRKBjUXAfSzjfMz21soVPotgJ7bIuOKAzBa2r7W3PuXLDW4H94wtd1/MI\r\nRUIKv7Hno1F+/mjvTh1N9QdOFfxM1aEuFI877LVmqWRX01XvqV/Hr4RzosE7\r\nHkR+2FelHcCgFjG6pTE/84uOnd5gPIjyb9KRJrkoDj7YEtLYIerwC0c0bKnF\r\nircWiKM4mnvWF2QdpEgUJW4yYCTbfVysCVM=\r\n=9mzk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "72b4aa92dac0c164fabbe69adb45ccabbb24c919", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-GA9XLdwmFQUCfKq4o7SU5EoZEzxdeSiSKXJG7GDPM94NWhE+SdwDK6OANxZNeaxewuF1amz57ADJVI/hAG9F9w==", "signatures": [{"sig": "MEUCIBmmQaBqSpmwI2iRQNgbUQIJj1XNqdI+hMQE825xGHppAiEA4+3tYXHSJrkpfHn7hqdmLdyP3QERsQweuxaey1NP2GM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobaA//SPkWBGDtu+F0Bb/krJCJheM1c1UehEQSuqPCv8wrgPivrtpu\r\nZa/Lrpf9THQNOOTRiJ+Bz7g20ky8N5SewakgcRT8EYTXbQh8/3IUqjRk44Au\r\nX8/cAUdSdf1afGYUH/tbe3VF1TXxFSTz0+U971pyGcvaoZJNxy4isBkslRfk\r\nVMyjMjomkLeIkSMqpXPnRP90bbAVjj7wjyrT0B8lud1WJ9MTZfh11fpAKenR\r\nOQmWobT0LpwTGV2j7lb4kUZaWGKFDOE2nDUU520KAZdEC2NlP9KxWnLVZalj\r\nKRfDeMK39pEXnH3gk2swDCep7WUbSG82gtpFub+UFgTrPnk7BBu4GJmes63u\r\nt9ZSEB0CtPmtQQNUyqnxCMDqukdOWSABTyWtPybvfC9WBfnb9tFfUHx4c9YF\r\nXNTyY/sBlz2M7jlDBoKiRlz9BlUQ0Xb5xk5xDvI4kMn+YE4ME5jHFjqwsfzp\r\nyM6kzGDOjnEscHNn7kO1tjAdQ/gk1ZOENaN7pTmA/kJHzpjs3yNzZq68hSVE\r\nLt4DUUXH/bgurBw2a7qTg1pzjEpZTkQNR9X9ENiowzdcXAGT9IYT3pRKy1BN\r\nVyJhQAjXVF5z8Q+x6rFSHuybGbrLOPmfsRaIhjbA95kZf1QnF2njig+K3cNy\r\nZRV1k6glt8gNPlxL2eFBuBfHYNz/NjQK2TY=\r\n=xeCc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf1a9af712221b553c6141ef533765bc1e94c2c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-uaEYy1uiT1ILKVsahCJWE1kQdlMwdtUFTIokuuYtV0uWsqrfotPzoG9EPLDr52uC3VgjiYqB32b09lZuJH3PsQ==", "signatures": [{"sig": "MEYCIQCHbZkPvNujvFISu4vRMgFLPYvth8+xxx8Nlm6Wm86oxQIhAIRZbz8fM3FGFOrxAmzA+dVWD8ED4llpMVHHyutGHBRx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMtA/9FaqCrvr6rXI8hnrHdRQq5a5WFoM+uMduYgvzm6w+QIS5RLAE\r\n4uqxxkUa5kCVxreB3PEj2cQ79dj3MDMeeVPks9mxP+tnYr7dP6lVceIypUD3\r\nCru6CCnABeQE7muNDuFmgoKjgPjr2wYTYFfDMj9degKLHfiEeXvYhSgOID5e\r\nFPNL55bnXD5IEfu2xAzsvISnmsbvrpBleXoY7qKiTU579RMQMcbLcFN+z+R4\r\n3Rftsns1nZzxuk965cL8csc/fNBreNuuH0FW1fcS4/sf1DTN0V9370zq2S/b\r\nAo9BmDIJs0kmJ3hUH8u0IkARGldkEnzgjBURtzfjx5uPB9h2djopMRiECiQl\r\nJ/w/2T+cHyuyCgTj3rqAAwLwz3/6ZGrZc8aL1zEsKdHtsBiF+4Kj03XzxrZi\r\nz9eR4nOX8itKDfjMZvmX4ZprYqdTvXrYF0OOONdxTiyKxOpe2rYih5R/I0KQ\r\n6cpwKH6t3ssciHE37YM2q5+ZLO5wrWBcUli8QJgVC7V6lJOw6RCVRt2OF7R+\r\ndwTGzRyJw+o3v4g90GMDzTg+FFCO2vSR0tDf4WgHYSGpkBuvF4SNxPU57BYn\r\nYmO6JUkk04bagr5qfdXvkMU+duCMFLtDF4h6M3TR+J+TM2Jh8FOgLHlnkVwn\r\nbH7G5vYbnA/M3Vg4M1lBwTING/Jw4WanDR4=\r\n=hj5n\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b2bbbd868fff11e0d6d829eb106ebb94e2244b90", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-gb1VAmO/ofy/y9HFXaRTjEMB8+MWsp6ZQ3n4467vzynU2BLReUiUb6vTf2HfFBY61FyevnXMTbexkDiImxitCw==", "signatures": [{"sig": "MEQCIEKZJVD7X9j9LbhusqBYwub4YnKtdY1JHPkdRMWikF6/AiBpCm4zL83ySo41Q6j346+EiRjfGF1V3pWP2wVodHmXfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/a5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWyw/8Cdyi72cDSo0KyuR4spfu/xdWxEYBiVFw5pgEC4vXYzmksNwZ\r\nqpDckDIVewYiMHLyOSpugutQ0fh9BDUx2q04rV4g5c5Wat+jLEcqUWuDg7Sf\r\nCUIkiNVBZu7cXlH2BzH4Tibl3E6viRj1Ut/NwY0KL2ueLi56Yq5jo+L8UaNB\r\nJPVu5n6sbdmrcr64toWjvMxk1MmASJbFDHWgbgk/q5/M5cQxBv3taTj/LN0C\r\nW07Z6Ykjcn1Fl16HgWdbidf/eeKkUC3ne/mb9x1IIMAQDRl0dPB77FBefeds\r\nQmJosCiRv0PRRzJiu28aGIN9DHCyVeaqXf2mBJbTKZnUVAB4ZOdigod4+Tbn\r\n3RnCJsZ7VDcjo8fNavwAO+5rMvi6XFuMEs77ORuNxEOQ6T+bC8AkR+F/dWqQ\r\n25PZaiJEkyiBdm+x875QUbsG/4gJf2Bscwz4gnSRBi8IC8nkTuZtfZfkEnFf\r\nfufQdMQUQUCwKjzCZgTtPQoaMVIIksi/FZLZVBa3Dc6Fqpu+il9JGxCMSxvj\r\nM+8RPQdN3ted+7wvsXSUHN5+wID0K9uqc/0XFF8WIh9xJgoFbW1G5rpllzvD\r\n6jEgfjQ0QI/3rpqSZ0u1QbMFCr+UnLcZtfITMaJ8lc5rcllA6d4p4TIfmXe6\r\ngoBS+hBLJ+uEh3QV0oB5dRSJO4nUn7w03WU=\r\n=cZEn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5ea73d402d90f12f82d2625b6ee556ad093c8bf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-MEYHaeaNHjjzbOwQv0OJbJ9WEJrmL1rUu7KvGufkA5tMpV+Pb8sTbRrRhQ6lV7idJIcZAJy+1iOwAq3HksJYoQ==", "signatures": [{"sig": "MEQCICgcuTiiBdbOLKF2i+AoMa/bzoDRW1dlldpwm2k3RpxZAiBiN1GU3i0CFE4yhBUM0ZP3gFx+L/Ku4DYr3KEZvL8rvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZcA/+IApmYbHggC7cBzagip98+MDIcxKMuXeuFUisxBuG2RMR0qum\r\n3P+6118qgiiWcVSrw3rMuCsPMEsmGY+qwwyjEcb4SeGQtI2FgUO/Z8TCdKVt\r\n3jNdxQd3mPztYARsQI6uaFENr/yTJLlPReC5eXK9SBQV9bqYlyk6ynwoCR5O\r\naOyYmKy9RChZwohNF87MCz16PBKolonRDyWqMIfxPQYfFhbRPMhA8E8bjbV7\r\nhAYUza/NKGtIL1YYU2j1Y99J5JJq0+FgTmEdul32KhrTeLmRIi6nNiWQK4Xp\r\nZDvJe+axMYIgel6jRnyY8flFVwEbYTTAYHGUInV/eBhlvjew+pa8qGV7BwG+\r\ngkMAo5BT7QSO2TEhhuJ+51A7xRG1iINJjOtEZRele5nuSJXfapsJ/1mbSZY2\r\nby4WkL1Cnae1VBC5v/PJVbibnSoRJ8LRtnw2pPomDW4ucOaAJ3y6Tv8d2ESi\r\nR9Eeat149mBCriFNRlSInyHJh8x0F1Hv9Vd3zCtU8OEP7r+anWTv9unOcXwI\r\nPt0mrGOAl+ozTa+o7uWwTUgK4u6nLMrb8ymqWfDyT8gERu/2wMZZcxR7tA9f\r\np7RpUvudb2L18auICCVUCyPBfjFsmYuhf3CcLX4OW4t7lKT1Y3vZ03SbcFbu\r\neCEUJWcGCG7j/xic8At+DyP+54BYtmecDKM=\r\n=xURA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f7d8e0389a123f816578be00380de962dd0991b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-bZRCh73P+9OX/8N6fv4c57G1fVaNP54H2mKK0BYfYHxoqSNQ4jLmF8RaUYcOO9gJFA7PoJ90k4z+G85GffR+zg==", "signatures": [{"sig": "MEQCIAEAWx/Qm0PnFWCYFSux79gcw1hrjgqLPXZDTg3lCwblAiBVs87EVi69tpRRrUhBgtE4MLs7NFYPbKSIOrN5e04UpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtkRAAlMjy3FYBjIjjCMCphuCT6KMwblSdswgc+Mm5lnF3sRh92Df7\r\nex3l8J6HG0wZsDXvBMYw2hcSnM10eO1G2LCuunkXwVaz0a1L5+kgZE3vd0Ae\r\njOlWrV1+6RUVbEhPaTZkKg5bYwcu+WwhBOyOn1OYUBe7bCr1Y15Dclh4uMP1\r\nzqUH32/uu4M/2DHxiuzMRON6O+wHy5y5UurR9/vvzRzyGP2kddYgozgkhLmr\r\nh2CTPCMSYRfDhA9C7DggIewbcc+iSyVwBsV285qjbTadgCNyRgSumlZdHHEa\r\naNVQvni+ngkGSFNOwW937COz0qNB3TcPZKKCJ9g16pf7Fr1lnXn2cUyWtplR\r\nDTHfU26+GRZuIgMD8r5AlnxJPnAg4apcfBquxdC/e4D199eF8XGcg2Zrljcs\r\nK8HDhnHLRQv9wyxIdllSbyoiCaH+MGYy1RFs9PcERh3/ct+X8BSvABYfoWb7\r\nn4Aa1FsXCeV5T+L0eUuKoVdiPzZKLTS8J20pV9llJEFdHaOTlavo0xvn5gOB\r\nQ6NxcYFg/rb5jP7VfItInxKawoFDGHwWfhin+T767OVauSt1drP39L1BVcFs\r\nZUtT8dYiwYjs+CVUlEky48fBdx2oylLOPMEKzwZGOpgYtsH9naR5NG99T+xr\r\nyUiJxBacIvZHkd4AEOKxoK/lmUJdvmR5mgI=\r\n=VZxQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b58bd969e15f20e50b375f61e0192e14b6702230", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-aSc561TqdR4mdXaV8rOzPL+wWwCcfO46fsAHr6GdmiiqKIYA9u8xVkjEqKECsgRwoZNILv/fXSTtKpQF4zcCoA==", "signatures": [{"sig": "MEYCIQD1460nA/IrkZLjHeR09yGM8AKkFN52PGyXnjet/LC/FAIhAMVLiYDH3vKMMHjMtm3XmePczG40pjXkuo9aW6Rq9hdj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVL3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj5w/8DsXyCY9Gbon0gTnkMvat1tV2SQ4qLAwApBsWKjv2vdQXaR6v\r\nOCkw8rIIpc8KhiTpibz2SbG7u3BGYCdAiGYzk0wwXCAAENclfgNfXjMTlEla\r\n+7/4dZLKD+wGOKA1rlRGjU9jnegwhFyPs16UQRmyF9dLkIDdqFDr068enUfw\r\n9TwoR1IXRNsRodTnUoqFf0OqUpSz7EM6FwdI/EyFUaKspIsIH2tKvGee7gJv\r\nrLDd2porvutbPA1c0mzXvl7RK/nK4+Wr9llYfXmouhZVtYtKsMSl3M8BVJ0h\r\n3Z9Yg5+E2RiFmd+Qvj6FX949txsLZmhgy89+uTlE0muFTTvqtDunDG2aqtyV\r\ngPKCfLfFYaFAQHp0au+r285UrKq9kKM55iQBaK0iYEM4p+rl1aa7r5FncaoY\r\n+7gToZ9i+X/nEpfqixUkDj6EckwSwCsjHDsLD3DJfe+l/m3o5DawyS4ZXEwS\r\nEkrFMOCA2XEGaOHFHT0Pm5ra/XZG5VRUJhUrNd93OLZtuJRUVgPt4W/5AVN3\r\nndD6dh4gwTBzCU/cNCOC7k67FbC41YB3t9IWHoGKOj/9wK/14taD/sGbgXl9\r\nw9mr2ma1txcwHTlwsLfbqFZ0n6ELEnQckvgYxS1I/EYQLp0imMqJszkZFpPM\r\nd5+pbByWNOQP4wjYM4GxSsgypRstN4Qec58=\r\n=OJkR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e3eaaf02f0c660fab95cfa730aebafeb32d3ae5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-XRLlbPgJeqZaHKLaEwH802Hk0bn5S2BUqbR7eRcXwvByQKVoYvGhzdDVJjbexK1GC/GJjxh3Mb/YttGVcQoArA==", "signatures": [{"sig": "MEUCIQCkOMka9fqh/72N1LhUounbSCMB11BHtRuW7qLJgL4eRQIgHTwI8FBsXc6QvgR2a7WwUZJjzPDzZjJQRvVbfY05Z7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJNw/9FxuUkTc8tscIIdshiH7yG2pjZmAc02dHoJFXlMj/xtEo1Gcm\r\naicw4nvS4n3jQZpRIae9vvMWZiiULnX//1+CMHoBFDNeZ+VsKB2a+Inol9/H\r\nT5qztnQIxnj8AeydbEVgfBShEMl2mYNtUW4XUt15E6Ba30BwKzfuRGuUVDhr\r\nr450fCL2EQn7TYQ0jcO2cZaNPhAKmWeoDE+UJhSFx8qsVk7JhK2hA0x5i04J\r\ngSl8Eay5OgYX56Xmb/g0nu8wDSTL6RbdmmqSZnac/zEGMIJdbzbtR6e0gbXf\r\nF4Nial5NasaHtrw+kYOFJFITvJEqFr6f1ZkgoCBVa9KSU7pltsMDVpNATrnZ\r\nPNno+j8B1R3unnagVPsIdF3Mt+bHnUfbLImgZMsnEn9BAXQc4wRpKrnbup9R\r\nXucN0733304sZOMssJDeUMsB+d1NgIzZ02zsCz1ovcCUZ932CsybqMq9rafJ\r\nY0L8rZuxlh5wTQZU9BpBb2PYynPIJW6Rthkn/nmNSK+Prx7AwcRYYt5LrqN/\r\nftvQMUwYZ2ejCi9Err9w38cdrgLuyMwoGizmElJ44BsnK3BE7HITFUEvMSut\r\noCuHGBEKwsD4t4LsFOuxij797vOaFuig1xPBrI+Bmro2VGZOfPmy3L3lsMLo\r\nPMSED8j5pnkpE9ouYPgACIdX8gXIyGoP1K4=\r\n=RlW5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ba3b24335d5963320ddeced8f05d6728ceabab09", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-BNQ33BA4ZeZCv9f9kBSiqZYtdNcGZXUb4G1Wj2IuvpnDRgtZ0zBbc+g+TcGSYhadMBYiwTXcWXROAXorI0Ailg==", "signatures": [{"sig": "MEUCID9fR9UV6zdfz8xEXzM8BmXmj8lzV6i/kBsEn4Y2WdV+AiEAoAUdUIR89ydpZPzHiGXbLpg7+ZBk1skSg4iIYSYnTnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHRA/8DktfSU5h7ck4KM1Hyh7bOAtzDkKDYtsUPCKhUjJ6pPHWFwYC\r\nsJ19eGZvUjzy0qxuEyOCMVOkMnOWMZWZttfqEaqzEnoQb/KTOLgQuHDi8UeC\r\nB1v3jSsY0By+jHBrrDDRm0mpSipv81NlMu+RWdHkv5eJnsiLkvzkSweAzr1+\r\nN/3x7aBqEI+AdLXm+x+2fw8vewx98z9PWMVZLQxkqT2osrYYXY7teBc6gYmH\r\n9FcKRZ2fVO1w0sgWsmo/QW/zyGIAullk86U1guoyA+GYV75FjBdCkNUEEAjs\r\nW913HnXxJOBlNF5iuLXCAJojZXLcOQZxzNTyqknG4vjjMZu32KqD5JBcCKw9\r\nRQHMGcYq+CaAJGcX7//7ypBPzOP04mP7JCPFBuNt4VmQ++J82pzwWCZu0Svs\r\n+ynw451iRWjVGoNZmh0tB+DkUyDe2Dvxn6w2DgdVlN0Ll7fNMcYtnYq5BMHD\r\n6cArd55yRyYb+gCW3e0r0hS68OT2hHFRwtSy5ZHrH+refLNE9q0NNTsHDRm+\r\nxUs2VR0qDBZHuhaELBO8QgkwJcOlNxZut9kMa7MpQRFRFGmZ8QSjJpKhJsM6\r\n7pdRavwo2TFoMXSFFIltND1GmhHc7Ht9PCqo4cEcqwGlLAJI4gmzwaRILcQZ\r\n0+mIXkVBzVUhiuh8KospSM/7XC3EhPqWmeU=\r\n=hW6V\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "541a05e27adfe96eaea4a2ec639bbbf9a659b43a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-zFZEHaQvPzwSSrwb+1sK+owhzwOUxGjH35xM2B1WPrccZojH8VwzsXBjuKP/vGHkZbAHadejid4+G1J8WWV4Vw==", "signatures": [{"sig": "MEQCIC7opqQL2G25WZhfyxeOxhWfrVNHZeYr+2T1jDfugv1aAiAclbmN9jP5QFIRUzHrj0Kkp3Q7NPflb3dM8WCW05+odw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJ8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4Lg//cPeaec/R5Ya1Hjf9tWfe8ghX7i7PTmmPX+cnwQZarrLJdMQk\r\n2ymNVhxwYCT2P67vFx6W9z5/6WwHr7g9berOHzBXAFEFgm9lMce559HV02tq\r\ncqNqpA/+UeBUilMxhmbKceyI7y47rfk0weHg5tRUIZhc74Elb89P6Dc0Dkx4\r\nSBeXfFugl3xHzAo9CAhJFG5wXLC+6IsS1tH+wtwy8jIMrOiNNQYegXI6qACe\r\n59exxpZ0CUQJeqhpSWb6ZEu+S18OB/jeoDwvh7laHyh6WqwEsH67ppeqGzAG\r\nVBSbcY/BbYAHK+KKEFgCZmTyoi+dyMHijmoFMyV4fCo+1buWG+FmTuOcZAQW\r\nt75Gp9qq8eaEWNQXz9kgqGlFXdUeTt5REohIsJ3/lIJtwuZSmGcS502l3tbb\r\n/b18NvC4RHbZqboeylDgBEQmsGwS5sVVuIVU3tAG3KbdQUO0NI0tWZKXU9Bf\r\n5tuoVttXk347YKWrPazG2+5dTxRg+2/HiEBXHV7LaqKhZjsXAovlrwtaGkGx\r\noOJ+iafeFH+ojsOskwbMLGEyfnSPgy77JOLYVAamy43vMtAUAFjMKkostBEH\r\nRVv08sCYD5UVR3gDNH2sZ50sV7xHGypv8VEe1g2R6HSruhO5rdiCglcaRKk9\r\n99OlYophRhIbbOA2HdCb2NVlUMtc3uD9uPQ=\r\n=2iZ6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-collection", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8c3e8ff5477eb008b0deb55d76fd12f785c6465c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-NVM2gI6fRMcpHPXAsAfpiXYGvRkjuUiHs1W12zziWxKrnHhFvqZFqLJX93hOsNyACy8zMJxAlAl6Tz0xYtTBww==", "signatures": [{"sig": "MEQCIH723e1QsNW69GUcFnuE6IJc9m5WM0+IdYsSUrHRMzi1AiB1vlWeu4YNVsj0Tgkv1fajkaHkXtXfa9JiGc/w3Oi/fw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRemACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdlhAAhp9NJrU340PRFt0LsauVmSdhwrs3qFtAsgaklyaES3wBL79R\r\nMpQmk8iFssqpzElskvTwWdUMLX/XbDSOg0Q+6iBr+YZx03PWFqRRQrdXOBX/\r\nrXr4hgGTbLs2BC7tj4yEqclAjAw9bUqKGogyCVXpQY6dBW0EVNL1OfflTDUz\r\nztGIifaphlKWDLv6KlUT8EUgW7RbeIJZhpGQsPcbtHtQZU8mVFP+DP0JNgAY\r\n15hISfa9ZLmE220hnmoS4OnZ4NjpQ/UaEBNXDSfq1g/evhwh1QbBcg7piIcQ\r\nxv5MdOlBf1WjRwzHbehnxADI/chUYrbj9J5Asv4GJYKhdj8V6Mc+AErwCg4+\r\nrDq9nmWjkn58w5wYtcGZbieLk8v+q5AduQIdPdcC0YdRhphLCrgs6xQdgvxZ\r\ntsAmQ7nwYwrwgnhYj5Ti2kgOAf4aBbWEtpnZ7ByGEJOyiy+qAiSWNqLjFUbU\r\n4JkCqHExnBQkDxeFBj+toVQI35c2Zivy0v1lYXhawP77JZ76VAqjsIFKy/UC\r\ny02gF9wwto3R71U1OFV1oA5grqSVq2hwUfWIXYMcNlgNlc54Qp0rJKL5sF1M\r\nlqAD68d2j93jBUN4uoMX8f8xKCQE9D+puqATtfx4uFudIKCfZZnui//WJu7k\r\nHLiQFHc65HmmtcYDHySp+0jkj3jyYQS57MQ=\r\n=zjNb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-collection", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "259506f97c6703b36291826768d3c1337edd1de5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-uuiFbs+YCKjn3X1DTSx9G7BHApu4GHbi3kgiwsnFUbOKCrwejAJv4eE4Vc8C0Oaxt9T0aV4ox0WCOdx+39Xo+g==", "signatures": [{"sig": "MEUCIQCZXo28sYaVwNtOh2cZsCe01iZrEKBNkmKzL+BeLaz8LwIgKJdMkW7NpoQ53EPc//aOVMzVFlgputLHQl8zUYQECGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBVRAAixVpgeMqUdy7U5GugIDGkOzBTjXwhciOVv3KQS2BtoY6Kk18\r\nrfjjNAGwoEb6XOoED/sQ2wRWgeddOzQNa8n89BVXZJNcrz/SEtiX/U9UgyCG\r\nPwhn6uhSOA3sKjyPRDXwGSGTGSVj4r6qSCFwQ+efCeQ46BU08WJLY5QqCbse\r\nTH/uCynjI+za3zgGJJ+4ncb2QhZsfMu0FsxKyZTNUq2WSYjFwD/E4rSqklKc\r\n+8g2WvFuuwm+3hzxE7vhOHBkOrm+4RnaIDCRB4FWAdDHs4cx5yZ90t3o4B6v\r\nck6u5soHPTKudRimTM8mVwi5T0YtgHSICzrR54Mi7R90KGdcBwnR2Jjg18PI\r\nBEoIxnp57q2UmzijE+TZprLMYImvdOfQLfk7qCkjpENbTgqV7lEVpXZCNRTO\r\nttpLfWVexRnnIlaSIBtEDQT6Mx0oFu8cV8vCQw6yi9cK5RW6l1nQUvl8YFCR\r\nWA0u2kCeTr5CKnLhG9twDLcRcPyQJNuD9tlX/b9NUyIn8zSD/ZGT6klYgUPH\r\n35jo2s9kgKrQdAYamsgZIY4wUtaGDYkcAi3jLPODLnE93le+CZWI8CUNHFZY\r\nT9/Qvvbs6sD+Rq4HEWzf/Az30JufW77noIBmh9kyNpTfEwp41JeUKw5C4p9i\r\n4JkC7OOAjyoL6jLuPFhDCmQeQipeYgRgjio=\r\n=KXcs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-collection", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ba6e9192e627e5b2c3f0bd747b981e3df353dc3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8F/xsLymAfoRoAJx9FLWwXD8tqkOvTcaLcdcHJlth+v4DXklOGFJdnrDniuPrC8CN0iQG74hcoy+seKUb5ZYKg==", "signatures": [{"sig": "MEYCIQCOZPwtgQTfIzO5BTeaoMQWf5PmY3+eYKk1Pdo4pXpQEgIhAMKyddpzFCdTR8fia0R4WO80b3oWTUG6kNXYkrqUk3uJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyNxAAkcbpccoadnhwJ5W6HgHYNQ+gEDyrbGU/QJjKE6xKpO+4fPzr\r\nBnW+jKDxQqMaWO5zAaSqO6gC0nwif316x4YMAdkHW2rWdQ7ntIUNypbF25FN\r\nZtDEZgKkA+yOiwuV+vkk51K7+rVH5zVJjLizw6DdPtROu+170oouwOmtXVMi\r\nrnCmQpkbm/fc49RVM7JClAPkh0WhjO9J5oJQBgbT1NueoJbpRQnED3Y9Fou/\r\nS2Zj1nK+PRslcpZ1Mvl1AKK7ThquQwUd4yc0yY5fCbjJvs/3BEWy3uD65hpk\r\nNxRZ7v7gNuYwHHd28Y6X/Yz2TPOsDPU3jgYs94QyanaUq9zxXUAdl2WLeS6o\r\nhycfjyQIOb/k7ki0mZ61+apx/uOLSslOAszdVP9vJLP2/Q0asQ5gwyvAouX3\r\n1f52sDtqrR7ZjFalJqm9ueyz4JCkOAl9cRMxA7YLrcZAo9JdNiLa/y2bUNbO\r\nmTjZroQIJ8w9HA2/fobP8Yle4OtvrKRFPB+gjUC4B2y9lJJpfROJwGSdl0+4\r\nstdBcKr9rd7cGuoFpQBsKMPs9U5FHdsV6qzpoNz0swZiyAZrq4G626NDoAmr\r\n11OMb937erXWra9PtYnpzrNXMtfmyO3Eq3hwqBaGkdAytBuMgkAl2TW0Y6vu\r\n25ro1bZZmgUMA64mMJMpzylK1jnBVgj50Hs=\r\n=Nsxv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-collection", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d50da00bfa2ac14585319efdbbb081d4c5a29a97", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-s8WdQQ6wNXpaxdZ308KSr8fEWGrg4un8i4r/w7fhiS4ElRNjk5rRcl0/C6TANG2LvLOGIxtzo/jAg6Qf73TEBw==", "signatures": [{"sig": "MEMCIGyrjL8O59KzIhirFcEw4Khg30juNhDuz/6GSkbZZzbsAh86TEiFSPdsiMGATum3LWFB4UcG52NlaM3B/ng7TCgP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJakACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdCQ/8CKH1TR/xgS/M7VHWv81fB9AXU8bkVIHaJ2QvSpKQI/EdATOG\r\ntZoPMzNh4gu/gVFN2bn/iiaD4dLy4/IVJpi8E+6aO6YeF+d2uxRfgolRBIxx\r\nMew08vnAfdfPwFge6964Nzpab4hUMJlmAAuww40cpbOUSLsSRyirnACJCZVl\r\nMb02rKPjnAQPM+3mlTYIu6ygvZz0X5k2XOx8BtWFwNI+pQ8YZuFwJfCYT8BZ\r\nIpIYA3oIAp+jmTVNe/KNPtYOJ6OqmtEo/qfcvXtYkLiPPNI0qA6yC/zkQU7S\r\nKODoUeCJLX6aKqWTVsOvNDZ7qFw+iyoCxD1r8Sd77Qhm8F+kuAoQM3QQHM/z\r\noRSIZAe2QuS2Z3Pb54ky6FxM5UN85QlyBSQyX1WSqeBQUu8tB7PvyXRmS7lE\r\na774sfeLsH01+YmDeb3HHmG4fpQT6KqQbmpMC8gmAhYgcUdWqvumvD6lvvUz\r\nQoKXwkDmOSl1VVE0Fm7yyOZnYiU88tgR/Y0EaPrm7p7phyn0GKYDwMXIm1dH\r\nPmmm/d17UvjQfctwyaK2m1f3ZCbPuqTukTp81rEuqFlH9vO3tp8/ZBO2Axjx\r\nBrEkNbeGUkDVhjYLVpmmbORWSoLfrmzQfCUxuB6/9qwFpg4V5eO95yMsYDWs\r\n6M7MzYxK0CP86pL130KTjbqTENMrCLDMPnQ=\r\n=pGFB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa179bfb839169b30e3bde431fd17742ef0cac7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-98YJxwdHGd7WRB9jM1tpiFc8OdYxZYz6d2M+vamOwwN2oFy9KLTA+8UHpfjFh8zCmyPnW2cR5rt7EbOrjM5r4g==", "signatures": [{"sig": "MEQCIEWLErMF1NtCDO5nsjCCvzDkYXOof5S9SEkiGmlp1Z3WAiAnQW94VG6fz/TNf9i1mbXspN6gpWVqc8XTffsktYv+yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6aw/7BD6oEEtg+GCFwQYt20CbiOXqb5G0VxqaenNasZFrQE0S/6X2\r\n7Tiv/kIfr19Wiqay0rJTGspRXZCQiqioCPmE/IHlQXyE/97kPh8pTwv9XJRF\r\nB6iF9NObq+WTOpsfbUAqfTU9+NSSEcVnhF7zKSe0nvjdIib1kdEQpCvOiNpt\r\nZIaRCOBFwmj7jAVNp5LGGixcanyw8TF3mQH88U1EUIPMS1egfrSUs0N1UvjN\r\n<PERSON>7cgKoTdQVV9FaghdIoY0uWBD8ZN2IKDFpaq9YeM9InrJx0lSxTNLGY\r\niMK8i/KI5HH8iJDcMHUjiKGQIBL05NlIqZrqa931i+zsuWfJ1f6kT9HfNJFV\r\nYQazEccfaBGMuQUX/8Z/giaetsXO5ArD1ASwi7nSdGWGvr9F16TAOa1KUczN\r\nz3PIatMWjM9o/Giaq02tzI7oLpyWarsYkI1OUb25eY2LH41McooJSwjbdspm\r\nlx7LqQaFhxK8mT8DgWIuRzW12gDNk48VVmI4eanmMtrbchNd+yuHLFNpaGY/\r\ndqOEmhKJkqMDzTUFO8ZEPAJzygYy/5ve3hUoTatQsqM+oQbkFXwrnlSujc/j\r\nK14mpzPcLjDEAPhze9ESQ8+tbmIW/0tFtiQFBer87Dagf5JLjCwBaIZJl8GL\r\nPKIfqi8wkk4hxEVI+JtSabVC8EqiSVu7Tbs=\r\n=Nfvh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92fa9937740f9293f449fd9909628565d0d15698", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-28VM4Cxf2M/R790uppIuSptN5jJyzIYF/INPSEBJ9xn9vnm2QGNXmDR8sBaMY3WtB705D4P00OKgx3a3qFGAIQ==", "signatures": [{"sig": "MEUCIQCxKqrPVrpaP37bPXHgEbDh1VLmk0GP69snJYDHZqjKIQIgSDvcA1qrneit0um0vwGlK5JnTMuXBQJIAXeGj8WimxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32386}}, "1.0.3-rc.3": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "10a1e3653a4ee90ca5c67fead60a000e0d8fb0aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ePMF2NP5Rb/yxhH5x05+k4cuT5TjfTDWszLtrhuWkvh+351pExt5/ezewiOk1iaiw3zoy8tdI4MLLeEE26dptA==", "signatures": [{"sig": "MEUCIQCUuf/uR/LstwkPDyZl3Z5gFh3c06snma0cYii2fmIwlAIgcINA5mZ0B3I8oC787Bw2XWG2OUoAkSQVOBvuuB+fYwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32386}}, "1.0.3-rc.4": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "584ae4dfa957f9295ac013779528b6e22028f952", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-cjE/3IYfJYf7eZdWRGYT7AkLh8HI9JrVw2IVy66uc/aggH5HGl1bwheNkQQtADKGoTrXK+97hR44RV4a/hImIQ==", "signatures": [{"sig": "MEYCIQCwfXkA3kzJODz1jZk7M73CX6lscHDAbxmRjipH02YWJwIhALVX+NO2tl17obiGzCJ/u9U4j8Ib903CkgumwAC4ynAX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32386}}, "1.0.3-rc.5": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5b8562bc2bcf00c28f8a795178cdf911b77e9b42", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-7NcZA9NYz6XbDgLB3jt/G8aBAzK75M8UdeQrzl7WrHXkgD0D90RNJWdylP+q1Z8n/res0i7i1i7P8rE4MLXV5g==", "signatures": [{"sig": "MEYCIQDOwUQA9rCvoW8bAlcP8BKH+RRfqPn5U8rHexum9HLhNwIhAPpO8uACpEUQ+E9zylM28ArjHfM3RzC5mQMt6O9vdDsh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32386}}, "1.0.3-rc.6": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.6", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "70bd730a234c6cb2134f882f8bfc97a4e903a508", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-UShpN5//+5IhBKGQ16slpdk9YfnG9V5/7QGewfFPlBj7UztueCT5IcpTSB0824+R/q+FL5JH8vrqY2NEJtJKzg==", "signatures": [{"sig": "MEUCIAeRfX+NguppySFq3pIASONgt1eAbhvyRc7lBm0oKo5/AiEAq40OfFG8a7WXoCVGxfX2xdB2gMt9e24LUBF1fFRAhOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33554}}, "1.0.3-rc.7": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.7", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15ded5abd5d229ca9eca3ab2563cd92b82e9b37f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-o7ww2alr1+f7DUdTwXwGzMAbyPZbApr9z0aQ/9UXXMMMBpipOmSwc+E8uZC2sdrJRyY1Hh/9z6VwLte5Jd0yRg==", "signatures": [{"sig": "MEUCIBrUfc7fijzRbMofvvMw+y7mv1wrFjI8/4X7gTXqdM1yAiEAhRJtxVYI4JeEeaeHxptRaW5qb4A1O1iDtfbOgtzab6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33554}}, "1.0.3-rc.8": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.8", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c005d428169de7d4c6f996528c5b28cd1f4ffc18", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-AcyDdYhxUQeyVHQKFkK3jAgVVKdaXzYODPunBr6XHexNjZ6GtAVwx/Bl/OlLktGEoh1PTGVfYo5RBHx5w+ClBw==", "signatures": [{"sig": "MEQCIHBo3A/Lc8HnvAOcwJm8q1Y423Xmq/lXjBmLVZp8/7cJAiBF2s8sxNGCrxheniv9RLTlYnV7eBlAbISpn9G52MziKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33748}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.9", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d424153f0ff625d527f04b6a65066f30186a7dd1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-2bm6S70VkwbxFxOK3wSfFSMhbjcklfAUhlQ2oqhTJZnrmgN5DQZkCq0Ywdlk/rINaDwB9n21KtVPYeDzJoaxnA==", "signatures": [{"sig": "MEQCIEIHJD3OSGhKb5tcD5h8q367q5RaCZGwVS1nrUE4s02jAiBJqd6OD01BYVRw0iQliUcBor+Far6y8n4ywBjBj6vMGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33748}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.10", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bfb2aa740060b4a9579d23c982266fa2f5e17835", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-MH49Osjw+I53chmfYOog2srAJqkRrbCtdxMZAhWOKo8IFMgU4KJT6Gif/Ti4nI0r4a/u6eNLoKtGDyrPtJKB0g==", "signatures": [{"sig": "MEUCIDiq48IE2WaT5eQOFtzHbDkGcbaKwxs5hTbe8OM01VV2AiEA4VaSxcLtM/n//ZM8Kq2S4Nq9iH9SdLQr7+p3wal52pg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33751}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-collection", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.11", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "da81725a5bda12e01b227073ef3280212d6edf39", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-rCCF8OzyYqAsRF0rNJ08NQ2DQsoJQ2wEmG6J3AwikkLm3UJI9eYnchkKK0qhLljfbZwr4TA47F1POhlkIHvsEA==", "signatures": [{"sig": "MEYCIQCOL68YRBv36p/70kE2f0SfGK/3+r9C0/xUvDZVgXbamQIhAJximDS4dn0Ej1gzpO+DkCJgvypBS8FFI049P5QQoY19", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33751}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-collection", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9595a66e09026187524a36c6e7e9c7d286469159", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-3SzW+0PW7yBBoQlT8wNcGtaxaD0XSu0uLUFgrtHY08Acx05TaHaOmVLR73c0j/cqpDy53KBMO7s0dx2wmOIDIA==", "signatures": [{"sig": "MEUCIH7awUEyRqMSbsp199/PxaKc5ztgVg72N4/PJIWydQbQAiEAwdBE264x8wvsPWHN1lARPSepsnfp2p8PxMr+yAHfeFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33695}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1dc13391f796ed673748d460ba2e3ff0b4a31132", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-CR/ZHQSthOFwMpbkuiDXPVJQq6p3pWVK7c5Sdxjy9oGv5sOZDzKX5wU3YMlTEudHi3E+CCG9QFbjWI0uJPK5Ag==", "signatures": [{"sig": "MEUCIQDuqy8XdSEs2v/NXyxNKeVDk9XzDB8J/+FGLb+k4Id7eQIgZDrgBPPXVdXXgX/Rx92rEWF4A4cDPOz7GViHl4FeCvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26454}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "86e2f4858e696184d0db67eb7b828a09b68a459f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Hc6n8e2UXKP3LbPLxve8TtJ1yugqjEX05uYGkmEE+6IGXACBulrqtMqDIppbOZdwsC/PyeUXz0DzKcsKRvFGjw==", "signatures": [{"sig": "MEUCIQDMBStfWX5Gimm9RSs7VEeTMZjdOv7ieyNjwlsm5gIo9wIgArPA6sce2WFwuFZ2Li8jiO8KsxZYOrzPqAnMhiOlkdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26486}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b655000ba14a82a7c506a43d11bdea53be2ef346", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-VYZojrGeiJfjCBy08iXdPIBEgC0j3BASRd84qttfDcsKcQlCuyG2zEzVrOiuFYeK94NjKfxPqPn3DbwJ2h3nxQ==", "signatures": [{"sig": "MEYCIQDxH8JzomyqcYJ3zc2HOFemFsX9p30DEnJVoclDddpCjwIhAPHNcHcQZGRC6nfeJlyQ7gtYuosZY0Ki06SuABKd9qQW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1ac591061a98e6c1580b9a979c917513fa4659d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-qIKPB2LXumboflMsMrAfCC0JoeJozHjzCs5X6p5gvXQXhyxxjOeY8BaGryOOOFOyrSvsgHwGQ23dc6mflSAV3w==", "signatures": [{"sig": "MEQCIGuND+knTqG0l1PiJF5j3pOS7QxV77vT7JVOMvfacbALAiBXVsW+YZkHRYNvKYTAvHpemwYfq+QpVhT5b4U/syJGnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26233}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f55e5f2256734a1fd6048fedd5d3c37205eb675", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Ce0dFqXJVFkMvlnpbYY1cJrXE10fEilaPhsAQEXUTRh2NIjZmSoO4XcZLjeTbfkQkYV1UB7jLvw/ov0PlEm0Nw==", "signatures": [{"sig": "MEUCIAchx1ir5QOWKslfb7U8E0XJMtW3O+6ljKA/T2IEWVZdAiEA4zAp5fhlkufPZZLw5DxDTzHB+BGFBJkxjgeYQJv/3aQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26233}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f2c60c2bb0533ff92a791f061afb7983e982cbb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-YZ1WAKxHi3290C/M0Si6XXXvlNQ1xaqxe2SycemiYMgfDYfxL7dDGYeSuWu3ek7CTRIbH6QUN0pCrdqptHE/1Q==", "signatures": [{"sig": "MEUCIH7O1dobz3nqiwfARDNrK1dPZvvD52NYQiNFB/XUFBUBAiEA77iSEili+WXkhaJdhClEu1NLX1qaUsnpRJOx9/+dM3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26233}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-collection", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "75cc88438dea763f3fe7b939c84e251ae974ce1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-4DLg838/G+UejMZaAwemPS6UTTEXcilaJJuLTw+BTHTQeP0JWmaC0BByiQlTpE9ynw+lpMGT7+nELUfB/LBDcw==", "signatures": [{"sig": "MEYCIQD1fnc2a2wuSjtGoq2rPi6WpzG5bLJJ8/X2UhvqNq3BlwIhAJLl3jJ/oImsY+MxDMZd+CatQtMzmlRu60x9xc/vMmhO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26261}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-collection", "version": "1.1.0", "dependencies": {"@radix-ui/react-slot": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f18af78e46454a2360d103c2251773028b7724ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-GZsZslMJEyo1VKm5L1ZJY8tGDxZNPAoUeQUIbKeJfoi7Q4kmig5AsgLMYYuyYbfjd8fBmFORAIwYAkXMnXZgZw==", "signatures": [{"sig": "MEUCIQDsGDzYW17RG2FVhScMfYGMyskTxTYsJu2Dd03PIQmU9AIgeSx+mnq/rjOKuI52ZnhdeoIrLWV1UDAYLNF9IyT7ZvU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-collection", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b8c32941295ea34a2d9ab6fa23eaab54c3d6e74c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WF2NuI4t22TvSMYPyISeOIPkXbtYFUHI9orpR7lBN2GkPzmlWvp3I5w/6SBJWqR7JWHFhYmCXhcOj1RmkMZjvA==", "signatures": [{"sig": "MEUCIQCJjmlIpztBzSlHKhyR8Y5Tv2UbdzdiSggviTzejZe7igIgUjXBJfRqEv2z8pzr6WKEXY6k3HhUk3I2AWA0Bfon/dE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25822}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-collection", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1d2ee01acd6ba0f625c4b80ace034e864f183e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OgNaXVEXQFJfxNDn8LLL00lsCTWqzLuZHUOSlN8tXE9ebqsVCLFk2yjVNzxU8CyoFmuXlzv0G4SjxDVozq1Ypg==", "signatures": [{"sig": "MEYCIQDbfkJp7OwE2vt3YV3ZZez/4+PZQAlQN1nxar8Wj7yTYwIhAN1378RnGnW47Mff4SW4aZQm29ABD57fcgLugGMHCMlw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25822}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-collection", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ce94e07fc1da388c4671aaea1000ea41566ebc8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-0e/RFEo0TdexVVYC9MRHdHv5AgXCJlmm13I2htUqI6O8BnKZ4wdGM8UdWg+37uxU46OnLEZZzpJ9zjTIa5BMqw==", "signatures": [{"sig": "MEUCICqhpuweJd0J2Jw4GTo8pmcYsPbfPKeRhkUlhx8SvJDBAiEA2jIW/+aTcvwUINbcLGQSSYqJPEz6bYrtMmTZDy+sKME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25822}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-collection", "version": "1.1.1", "dependencies": {"@radix-ui/react-slot": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "be2c7e01d3508e6d4b6d838f492e7d182f17d3b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-LwT3pSho9Dljg+wY2KN2mrrh6y3qELfftINERIzBUO9e0N+t0oMTyn3k9iv+ZqgrwGkRnLpNJrsMv9BZlt2yuA==", "signatures": [{"sig": "MEYCIQCKhXAGFA5LcPqK9fx6dZSX2oQshTRQFf1OQPSQXjfh6wIhAJwX9/cQzkteZWGUZSPjYPAD4/SOop0JBFXneidlXCfi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-collection", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-slot": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7bad9582668f0f16ab88f71e41d12e8fec2db00c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-zT4bBAB3JdjlY2qEgVZBwbvAoiH750xqzDNWKcCeQlV9g2y5H3mzT60cHDTzZN8Jdu0jVjxc8k6BQvM4CnJesw==", "signatures": [{"sig": "MEYCIQDV00hBJG1DemSkwap0rZll+BzQvQu0kT0yidSq+zGYrQIhANUtPz+yNROj7ILX7zGP7LzP2egZwyzqfl8Ydz+8SVpX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25783}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-collection", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0301856e646fdfe2ee6269378df3690560a25db7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-C/szUeR9D8AT6v4EnOCPgzx+4HLieVP5KVsRaRxwePeHEHHQ/EQeHYrBmQ/4WRqn+HrRip74ujoVHHjKNtEuzQ==", "signatures": [{"sig": "MEUCIQDBhm7bbPfWswhWGLRYKPBucmSEyTiBLvyaKsq7od9OmgIgESXuJOPj81U0uZNXdpEC/Y1SITBl+krcLVj4FzMI8gs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26030}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-collection", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "87e700dfef98f65c59f7d68d3e8edd6a3819a049", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-a8X95naEHIpjsO3krg/z2YQgtEaPGn1F0POcCRV04wikcf57QqdNO1p6EMJ4f422bc6C3F1cfGRB6EKmyBfHEw==", "signatures": [{"sig": "MEUCIDZ8KLJ7zXTwWzT1/qWkHz6mEcv6m5cL5eauIxnlxcQOAiEAyd0v2xPgQvz4xyXWxnqakQNd9mSANBIb4rHhUW5XpkY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26030}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-collection", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c9bd3f33c477bcc729bc01c1d50008c66bc526ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-cuaXSB1Foy1FuSSppsXVJPtEsRlAJSbfPqVBrW2NxBqvW8UVHUFfoXIO120bqsxsJYqlGkohOZJHIQZJJd++7Q==", "signatures": [{"sig": "MEUCIGk1lbx09vLBwPNVdPkq+Amu28D6mLjAntM/IeVYxsSdAiEAlPg0YHmewuiX/8KWwtIRuqUIMpqNluXxv5H1L1BxcOY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26134}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-collection", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b1332bc87ad89db3393ead457c2c2482028b51b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-FKqxaG8ePRWuarbFOEFNTenLvjhxcpv/bkUQcPFBvhNnjhnXFynciTJQ1s5oLqOc9C0XkljDCCM0TTnFNX3VTw==", "signatures": [{"sig": "MEUCIQDw7dFt1ZqATaMBFBfPt9pGPA35AyRxfu6rrIL4FH4UWAIgM5Ew1l6+0mwgoVBRGcYJE97snDsKVaDgo5Xv57cFoL8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26134}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-collection", "version": "1.1.2", "dependencies": {"@radix-ui/react-slot": "1.1.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b45eccca1cb902fd078b237316bd9fa81e621e15", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-9z54IEKRxIa9VityapoEYMuByaG42iSy1ZXlY2KcuLSEtq8x4987/N6m15ppoMffgZX72gER2uHe1D9Y6Unlcw==", "signatures": [{"sig": "MEUCIHrxCNozfDA/QVwwDGEOs7o0BjRE9YyMXiqDW6u0lfvYAiEA+SIt5RUwu1x6Cu3Z1vgZuUdF6oOqlo+bEovdO2uSjrM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26091}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "217e14fa4e2621337c64165a355304ce7bb1c466", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8VxOJMmRKEciK7wagpMjlC7JjUvCYOKNjuDwdhmDaRh2OdXCfZcgBrAoOD84/zGX03Q+QkhioOaZxSXZd4030w==", "signatures": [{"sig": "MEUCICAaIi9kyHeI8QLImIMkUVViX/5dMtZ2woH73UsSo84EAiEA6SdFioTunZjBIBPSungBSTzJqfvk3UdI2+TUpdcw0W4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f0c629635a27a3808314ceff69db6bfe99b028a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-LEwciK7nrdlKeN9xTPbPykFpneYZyCygkb5riBcbsBzWjiK2i1hnGvenEblJz23FQvreIi7aicd2yuk47v3tVg==", "signatures": [{"sig": "MEUCIFnFfyjQnDxsS7x2JRPGt1SNFjIPUscmSwq9w8O/mXoZAiEAh6kF1cNgMjgS76AtQ6rnvAZADjlFjfGf5RsbrYY3H/o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d1dd9bbc9162234b92e06741615e36b9c88e4c15", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-eK+kCCKD+3A8gkXIRs8avvofGMA/Z2whY5UnnQlqMz07jOaukVVh+Dfy3ow2PYCznSsxNGKVldlsZcUt9QF/Yw==", "signatures": [{"sig": "MEYCIQCcap4+gjSM1PqVE6c/FB8RFolRFKDgEi/yPpfmigUM5gIhAJHlpJ0tfpqNtRW7bW0WOt5tY69eFCoFHQ+l2m6a2Uc7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "febe0bba8c1f755e8e4d12719abc04a5b9bf0dd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-SPIKAGf/1Gk4w8eFqoLHk8XIEFHMXBdP0qA7FlFCajWZ6uq7G8XHzFN1pzJbAdhxlOGRyKhF/PwI5HTTVZy/Uw==", "signatures": [{"sig": "MEYCIQCUCGbV0Da+Rb1jPJg/FFA31eWYomPrzxxjacztZxZOUgIhAJIqkaMUL1PXU8bl9R1/Ucq3vPpBK7zy5Jmm0lb1mkBP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.5", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81fab7c31767a76b1f492025c916a0539f1822ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-LCvke6NzVmDpLEbgkKkrhyJmsPcqTaY4qfEwgNTzc8QjsfLVPFFTMLT+LEjBWXSCHXzRdsGI3iDVo1Yku2VdVA==", "signatures": [{"sig": "MEQCIDXQnb+UmtY2rnySPbKPpmMcd3fvvsc/ZLK1SO+mGfTJAiAfNa4tDkTzydMXnSknzWlZ/mIcXqAqlk1c/lgujLnXrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 26150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.6", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.1", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "791a577738bd88846f13435d759ac5ef42e06e41", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-gXrx5h6rOFA3BCErwlG8xbXmWtkiE+L1KwLQJ8xMwAfPvSdFA0IHqh8N+EdAECzRbvwlYL2qDf7v+M1cOCIS+Q==", "signatures": [{"sig": "MEYCIQDe3yDvv1lhhwZKifNNtagfXWUXEayHUf2Wz9aqojfV2QIhANkmtdfANHUIDX7ce/ZDWCxZDejCOAzgT2uuEV0xOjrI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27020}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.7": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.7", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.2", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "02cd209de9d11024168ccce93e757dde86816cbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-JVZDVbdMrPt9WpfFx6UBhl/XiygDhclQOHEPxwNTv4oi9kXvQqz3jYu3cP3yLoRBycUICPWzxxsBh5cqV2MLug==", "signatures": [{"sig": "MEUCIQDQTWuTiWxS9tg+Huhy4mLjChl1w8z1Oo1SdSXJO/oY2QIgS3jjMXpupCXQjv7ABiPfthbr4QXatUr0te4v8ZXchuQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27020}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.8": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.8", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.3", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f0d983568e731df13b46a213df3cfb4ecc47689c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-sTh2hJvw75Gzgd1Y/F4BLkDSIGZGRNfEmti9ER2hUCqc75DZnQLIsmIZ1QAvZKZoiwPKZLQtNyjX5FUiXv6CJw==", "signatures": [{"sig": "MEUCIQCHiRE96T4qZ1C8wVrNdEyypD+aFN6SicDI1OrFxXTF1wIgCGvABZvKkSVkvuJJkNJdNEfj3fcbKVe0bc9ssyg/49k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27411}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.9": {"name": "@radix-ui/react-collection", "version": "1.1.3-rc.9", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.4", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6ace0ae15e3436589de2ad3a5dd941bc650f7402", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-hYIdFnV6+yuGCzRY26jkakgwf0k7sbJlpltd7r/3FJNnajEl8/IE/R1PGvLdVc1nL3DnI2pvB/83yrVeUg+UJA==", "signatures": [{"sig": "MEUCIQCQKUqSYmfyhSWEDc/lioRtH8WoDrUpi/2ddAZDXuAcBAIgN2Waf+34ENaOp2l8NM4eFnRCWVSg3KxZ86/If0zLO6U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27411}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-collection", "version": "1.1.3", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cfd46dcea5a8ab064d91798feeb46faba4032930", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-mM2pxoQw5HJ49rkzwOs7Y6J4oYH22wS8BfK2/bBxROlI4xuR0c4jEenQP63LlTlDkO6Buj2Vt+QYAYcOgqtrXA==", "signatures": [{"sig": "MEUCIQDlLtop+kfygYcCldleY+HL2TEXXPQTVkCezWbILqEgZwIgeQGiNT5ij7KRHAhSi2ths4MtEB71DyuANY5ikUi7opc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27358}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744311029001": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744311029001", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1323a8c71b387b3f5cd30c7c63e4306664a34979", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-NgSPIIbPGkf41BZ72aAXpKstJ7yHtg/IOELzOsnCO3R36aO91BTd31hhM3BRQv60osQ46QRrZ4yMNX4r3Ob7+g==", "signatures": [{"sig": "MEUCIQCguOwYANYmMMXjZ4fWvL8tuaDgBtiL8nJt9VO1WlSM4AIgYTqVwIDxugqLqlAuH8DtRO3w70ZROENh6WLJ2Aj/KH4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27924}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744416976900": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744416976900", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "378c0705988bd80a17b190ba4420ea009beb4055", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-E7M5NpaKetoIqNupmSOMH7NrH6g+FCNupVWGLiGZ9njrogEMOUfEX951353HD8DFvVvszMgQnaIYeij5r7acJw==", "signatures": [{"sig": "MEYCIQD/QDrAziBAuo1Nmd0Voe1hzDzBHbMVnZcjXlxOu3wODwIhAOqzIamTfui999eNhybHG6QcUOLUsZ0qJV/2MMyaysxN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27924}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744502104733": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744502104733", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "872d6a11cebed08d67d37544f825e948bfdad5b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-E8QljRSxevQQx29Qy74cmdz7TbCF5feNEgesvnMO9+D/Z7WxFM0ETkdkU+cEj6BNbtg7F+Ure0Rh8RJwa/nVBA==", "signatures": [{"sig": "MEYCIQDrkZKYNT//wLy2wwVeFNxDhZaPbt4k6P3/Y15buRT94wIhAMOHKCxxylVOmEwZ1SurHlSYTf5x/yr3OVVdJZ3qs/ik", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 124969}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744518250005": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744518250005", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f654cac80f83a8e4aba94d6ddabde7d2ed85e0a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-DtnU/ZRsa2yajtXF/BTZExkJluPWwlrV7j2mhiBzD2sVMilJHIOGhXPBzAY/s0wdCPSLdEkyhoQkjrKq3a0HiA==", "signatures": [{"sig": "MEYCIQCaaRmsVQA7vSn6B3XaBVkbr9TdQBKSBfc5LfX9hD7NOgIhAPhAxLUKn7d9v2/zDFC1Bk9uXPatobzNtQZTY7S1QSBf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 124969}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744519235198": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744519235198", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "31aac82d8c696361d5c8a61a20eb741db67eb056", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-ODuOIjyE+ux986Gm+E9XNHCh3VVec/El+lvXU7fqsP3uriAtmdRWexieNmL8mAwVxO2yiXU5XtRJDivXzHxsTQ==", "signatures": [{"sig": "MEQCIBd5/rrqJL3LuCeJLTvNybJEBDQkWdiy0jz7Lvmj55UEAiAM1s3NT/dNp/q3CTMJjAg5YSLSvYcNsorklssCcViWOg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 124969}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744574857111": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744574857111", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bd4a77702606a89cbc3d30d95cba31a158d66ddb", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-Lp+4QGehRT/l5ZS9FegzG4/ixuAVdnQVA6Ihx2Uwh3EqRzmLnqXV/focaJ74GXNya7NlnK9Pg9cGi0jpPoCgOg==", "signatures": [{"sig": "MEQCIH08Tfy6BLLJeWODkx/btwwjNiHJpE5BzFUCJTDQcJeeAiBki3LvYDGfoYhRXAeCMbjVpmzCr0LT2DY0W5wDk8aNmA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 124969}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744660991666": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744660991666", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d7fd9eb25b52200bebcd0f45f21cb01adb10b9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-rGbYGf8Fo0rRSlaAyf0hvWvrDls8q5WRumu2qZoBnjpiWBTpmXtcc90Izq5+Km31I2dpr/qLR1yNunR4H6o6bw==", "signatures": [{"sig": "MEQCIDY+L4ednQdEgXkgDLxWG1fJ2fJoXY0j4AUBqa2oSVPGAiA0/San6nNPolsuF00HIN9dlIFYOYyx5Eymg61S4rKfFw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 27924}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744661316162": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744661316162", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d42d81468f66997a8be784bbef8589e0ce8331d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-XTfuXfOvkM7eu7YKPffoGdP9K2kIqqTs98MF9qa988OPKRmFIrBjrGTrZL0gkjCHpt9ZiZYEJqm8vKTMQqTOSA==", "signatures": [{"sig": "MEYCIQCeV8dk+fq4i2f5pVV3qWg5onva8cMBrh5vTVE8nMgUgAIhANKIjpEncZsQD5fgItBK3QBHP4NvozZHgYGzqGaWcdoU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 127121}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744830756566": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744830756566", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6bf09fe4e47570a702d022d386980db6a10aa5d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-tfrU8CxXYbNzuYp07pR+z5VIyBWHwpbRiiTxi9O+eFzCig3DtlohCZ/RNdvYvIK5uzKLST9PqGUbu3avLakIag==", "signatures": [{"sig": "MEQCIGD+8B3iaHTNk6w/QYrB8RxnHO7oWfG+Zqn5N/fdKwuCAiA9lnFId4kdmroxV4TfxGhLlIFKOxtXxEWkmTBXsaKSkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744831331200": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744831331200", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "313c3c851037bd19bb01ba3060b99a8d6e8926dc", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-gUBc9rUMT56xvyIdcpW3ARhOnQnofTHwbwNlZxGqTGmHF857s0GPs6S+p+cvWmEm/0nWxgKFuMuqqyX4PbYKRw==", "signatures": [{"sig": "MEQCIHWyEwuuy0v0N0hZYJ1eyRri5UogAC/y1VqdvDeGBbs+AiB/0VnuoESFXSSqednldYCaueMuqjPBdew8bJAKlt5GJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744836032308": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744836032308", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ca2c8cce5e67baa41ac99a509ef0583c850b3234", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-VJpnVYZXVWeLOmPo7vjzi49i1fvSZqU7IkX3JucrNX4l6Hm/P+4Nc4X3hyGieWBUW1/7mLT5lRoAJIV+M1CiVA==", "signatures": [{"sig": "MEUCIQCQAyafJgfR6bz3igFPgdK3CCB3bMnriCEkLn/zdgHhTQIgO4Zz9CVQ3NIjzYjSIjUQyIPzs9FLWcAqDYplHipLiP0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744897529216": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744897529216", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ce980e37ac64438500557f56b8464cdf934f85f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-/nKtosK0L0EmKxyUVImmEYk5YExol8pQYf2fc7m8i3mq9yJar2WQ5bPZhGnjbAlvdF2fxjdriFtiv4ddu3+82w==", "signatures": [{"sig": "MEUCIQC1tsFEZshoi2NV2u4ongDcg2pVh/W8AVmtPHuFmPJVzQIgaTRfTCaOwKwKRzzeCwXbxTqsMblJabfg5OMl2yboB8s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744898528774": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744898528774", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "83f3ca471a2e81c08069c598a63b19c3041c0a26", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-L6aoxf425xpftjCgiic2th6MySnZ2XpuSkn54HbRVb3ZrThx8ANpZ0LfIHsh88wV8K3lz8K0C0yOk8PGtRnmRQ==", "signatures": [{"sig": "MEUCIQCyC5VPYHM17X1oybJdI1otTvqeUvfRV2mIGZ7vL0xHZgIgKccuzM7g38dghlyjuhzqJcOYtP2u7ZWKn1c+u/e918s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744905634543": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744905634543", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "da1fefc57b0a1c3091321f9901e1fb2c8f77bb31", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-wlrRHsz6hWGs+tO3r7EJYXcUuRHiwWIN9+dGdr7CWpDUgpO5BERDqHJAlq2C1XH8WSMcpdoKaTjwAqeWLtvjqw==", "signatures": [{"sig": "MEUCIQCTmkczdgiTZsOy1ExgTDEMWPTXIaZfnmVAmbh5Rmhv+QIgaeX5tbsCQb4ZwRacgj3+2B4ENUKyyAfLL2HFTYB+540=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744910682821": {"name": "@radix-ui/react-collection", "version": "1.1.4-rc.1744910682821", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d41b067fd68db064ea0ecc8587a172aebec8a79", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-r4S+qKVGz1mma4NQp/7VRBGbjt7P7kzKTfm0Z4hHlHYFNCacVBh3lNgevmJNI6GbGL5XkfIvelMQi7uErnIG/w==", "signatures": [{"sig": "MEQCIGZNh77EANJL2sc4PLJjBf+jwCbwhb5CFiD3iP9ihd2wAiAZgRJmw/fVQV8YPBNQTCUkMDoUA1gIuMzIVmYE6dZvyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132285}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-collection", "version": "1.1.4", "dependencies": {"@radix-ui/react-slot": "1.2.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "45fb4215ca26a84bd61b9b1337105e4d4e01b686", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.4.tgz", "fileCount": 9, "integrity": "sha512-cv4vSf7HttqXilDnAnvINd53OTl1/bjUYVZrkFnA7nwmY9Ob2POUy0WY0sfqBAe1s5FyKsyceQlqiEGPYNTadg==", "signatures": [{"sig": "MEQCIB4htLe05154R430fi3bVzONun5kINoqEMG1dHg3jZKlAiAzyeZojQj8FRSMEqsvj3C66QkD6gEqsXMxqhadMGnd4w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132251}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745345395380": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1745345395380", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745345395380", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0c090ed11a56d345bba4aa6f53026206ab0eb65d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-p+HkQQs367blGzn37DhCadXhEiIfoYdlRCUU2dxs8PuFli6NzV4arU55t6Mwg6+db0YZm+s75p6wgovOG3ZKIA==", "signatures": [{"sig": "MEUCIDRLe/mjvaVnWHsYDRSLHm3JJ2waWXHTOzwN+j1/kAkrAiEAi9+GyWx5Em+GypohpN5qhS6RTB0Bcy09s7vMJP7s9t4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745439717073": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1745439717073", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745439717073", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88d7788140407c687b7488f1c1baf410f6e7b8a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-EZh1VNcAHzd+1sH+NpxmZQ9W+wqWnZNbr23lPhsN6yA1jo5Tb2Yr0ojWnXZC5lq450ICMJ14+Nn72KDpFntcxg==", "signatures": [{"sig": "MEYCIQCN7AEiVq9/hxTfOkNvj3Y/g9sRKjBeuXhrVVmKoUXdygIhALMMi+OOBxJBXgjw2NF1L386Hx+/KTAWu1xyOkRwYQvW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745972185559": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1745972185559", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745972185559", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8a1d2a3bf2452b1b4a1428e4d3a72088ae2b0cde", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-fAwhuojr4Zhtw7GJ69rPRrL5ndBhLtbxMgWWaOW983ibGeN/tHDJL5t4H58XwwDmLyTRMKrxCJvJ+iSfjfQCBQ==", "signatures": [{"sig": "MEQCIGnrb6Sjr7l2+bhlgpGmP8y3QzbAy82NaRCqCNnATej/AiA4bBWVRAu4Wlkf4itiE4lO59FVxKuG5unNXVfDbcEWYg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746044551800": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1746044551800", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746044551800", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "782416a266acdf9660b49f0da8971b3136a5fc3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-AneWEgU/zYZGmN1n+mba8F+OU6W+02lxAbjON9DpzIClrmrf+bRqxzPozrpFJjV1tByygOgaf+Z+TdFLMJTILA==", "signatures": [{"sig": "MEYCIQCZdOhap5cmLmjzsw60omY2sKjlyMHnaVO2WluLsjfd5AIhAPT57mmU9K4sZBmH20WmBMyWAQKOiK11Z8KO4y+jR6HT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746053194630": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1746053194630", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746053194630", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "90e7719785b3e1162acc99df8db7b928ade0205f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-bRZU/5oxMdyNzl6TXxhaulBn4KdrNgUvx7v/UkabBwBURIGjCcQYqjOjQSBjEIIAY3mv15a7LnKQiXm17gMcXQ==", "signatures": [{"sig": "MEUCIQCpIDiEt2QVWEhiY422/p0g5cIXfIoatv4E4Ak50ZONwwIgWw+5wmGE/bEDz/wff4hv3/MUp9lEd77CZu95CGufjKw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746075822931": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1746075822931", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746075822931", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "645715e22baa4fc553d67003de28b2b7a39b2508", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-BSPyJXEH1m7zTpyxalF9N1dsqzaQ0oMzV6Y2OoMNK28WFxFQh9lYDKNDpy3yBOl47yl+ynLHWb4ACj+2pLE9Ew==", "signatures": [{"sig": "MEQCIA69yS6Qu4ecsuj7awlLYwQk2wIEUQECTunqXwsP4mMfAiAQdpnTXcd5PSgTan/qCsp4qQ5CbXYxZ0jG1RAqIUADGg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746466567086": {"name": "@radix-ui/react-collection", "version": "1.1.5-rc.1746466567086", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746466567086", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0d043100b85286088876ab8335e14749ed3b589f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-tUHrAEGVUenlx5Gx9telK9yr7GEqwg5wU8uJ35CZ6U9G2SO6fgAfN+siBsBwsnUQp+nQr1fM5nx3jvcFYjiyKg==", "signatures": [{"sig": "MEUCICvUGV8Ot4/I/gLMgW7X5KCLde0immGj6XZb4YXXnTGrAiEAxS+IUzO4BEMoH0QeUG3YLO/EL8bskbepI6eFj4wpwCs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-collection", "version": "1.1.5", "dependencies": {"@radix-ui/react-slot": "1.2.1", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5e0ba9126a360acc62ba270545c1d9b103198de2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-BSItQMpSTKJmqzoUvWcE8QxbObfA90sC02qOe34mprZwTzx6DTj2EesFZrP2E9cYA1YJrUrbkuscMvSbpUsXoQ==", "signatures": [{"sig": "MEYCIQDD+SonwhrhuwkTmRD6sY5a2radNuxGEykdHQKJS7qm4QIhAJP15jwccpwWhEdyhvu6H/BzB/2L0Iwq0rBauSsiUiDO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132251}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-collection", "version": "1.1.6", "dependencies": {"@radix-ui/react-slot": "1.2.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fecf74475e4660ee99c7eb1ebfa5ccfb1a219fe4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-PbhRFK4lIEw9ADonj48tiYWzkllz81TM7KVYyyMMw2cwHO7D5h4XKEblL8NlaRisTK3QTe6tBEhDccFUryxHBQ==", "signatures": [{"sig": "MEYCIQChgnnRDjHv+C5BPElTKNFYyjPnuxptF1Z9QuOUgvU0RwIhAL9oRyDrAewx0aOfzQROaCoWWrUjKf3tj0bHG+Rk6uYp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 132251}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746560904918": {"name": "@radix-ui/react-collection", "version": "1.1.7-rc.1746560904918", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-slot": "1.2.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-kUS/LsMzBddxYcE9BSGF4/zmTsmq1qHljEwfrDBpzwcln5oJkl2sYMtQqOaFxc4KDOy6sG2yQnAZriPN/n++fQ==", "shasum": "af4d7bdb757443106b9bfd0763952ec29453d8b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 132302, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIAK17pHbn6AU+V/dutV1HCQ3fwR6NjqukmwITae9omtVAiEAyoEHvOiXxZKnUmZ2BejH8w1LB1fRNWlsx7CAfrlyTSk="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:50.958Z", "cachedAt": 1747660589291}