{"name": "@radix-ui/react-use-previous", "dist-tags": {"latest": "1.1.1", "next": "1.1.1-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-use-previous", "version": "0.0.1", "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1b538983ed5637d6f66e66061bc21c9035eb6d47", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-r1UPtyRXCVKrqIacv9R+yJwn6PKReTpOrI3FzN0iDQ0COaDdzeZV2zohrz0CCw5RZHu0V0zbCPMnwNgyETWmeg==", "signatures": [{"sig": "MEYCIQDfk+z3QqmqgbuCK7B5ykAa61GxfZKwYtNswhfN+7vZnwIhALcPEubil1nt+ZtIl4sVPUtU3yc+qieEcignAtKUDENh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VgCRA9TVsSAnZWagAAnWoP/RdQjEcUrW/YUCw70kK/\nq6Hx8/4bxvcvFUgv5f1gchKb123Ue2Koq+ptJB3lVlWQuUw/ei4doRFI11gw\njiWJPg+B/wYsQrVpfkpwNYXZh4IRDwdjlA1LQhlvDxJl80YRkAM2AWsdsnHv\nC3VdQkvjW04sMKi09+FXvm1Tm1sUoSBNEn5f+b3z0bQUGFvBntKl4EUPtP7d\nPXYmyIVbnprevXPH3zCrc+vM4FJl7L/MxD/4wq4vTaslR8ei/sNK0NjsE0rj\nMn/7VnhraThBRqghtdRoKmwHGMk5Corf2H5WiNaMJBRXGOCrXBq5qjBOumFq\n8qLVx/n30NovTcSW+UM4lxoBPel566fOYtRr9Pugl95oISJgX7P6yIfPsWk2\n1PfejfsMVrdjwwqbyMTSpa441bykXyDCMqfjuTnHpwB7FrxRjTv3hCvjmQKI\ntDCIB/NMPcVySGmA1YZBdpJoBN3vfv6isyNoAwaOXDQz23TNziUWZdynBQfN\nZ4vTIMx6aPizfsnfh89Xcej4uQsf+rjxjbEtHp5sQlrMFb2K5KNP+XzgyR80\nhXR9cdeTDx8MjTQXgaGbmgj/O9BGGIVk5YZ7tZyRjTICEaGlrHUmVUJUZzvc\nQk4R4Ikb8fht478Q7Y00ErUZ+Lq9wn8jiW0//rjd1hODtc7jd0IZRCuwmwGb\nOEoA\r\n=K9Ti\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-use-previous", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f879e493edff82818eb7587b90bd59c06a59a923", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-nAXa9d4b52eMdf9sORW8oRWLjiwgZYJ3c6UBGL1/aQ6eQ6Tv0VXT7MFN5q8EaDdnQx6ddXsyMvpRH1OptLTQOA==", "signatures": [{"sig": "MEQCIFVpV6fyB5oHeJuo5koh8BCq6eH8NniDrIUz2JT+u2HYAiAfb9ap8ufhLozb93exuJ9P8I8ataCu0l+bnFhVM4v0gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPMCRA9TVsSAnZWagAAE5oP/1rt+NvvmBEfYysu//kp\niESEPzbaYO20Pi7QMV8et26wv151Ma7fskijZaKuQa6tPRrbyD6lR4t4h+Rv\nv7Ko2LqETT/sb5BSbQ1zSjeZP6VsQ5CK75/9y7iHJAtGp/dkSf41HhiBlT9K\nohuJQcEY3AjtWKu7epvFHgAdBiMU1HTq6xz1XSzT6F9uHXVO9bpSbRnuHI2z\n5HHT7Sm2/xpm7uhYpW3F435NJoLz1S9I6Us+3BUTV7TTyo7iowFq5axciZDm\nBZbtyu05RFX9QqghNrF0YBm5MwZJoxhcoY1Iog/pMtk421vUnwRTDgZ9VZIO\n3QaPILnsaA8zmsTZqQnjQRjkBlZeW87W5Cg/Z0HJ8f+RlF8bReMPmBZomS2D\nutjtYe6144WIfE3pA5DdYr5KmQRSYVr8ZiRPsBMgh3ERpNOAhGON7zeq8nr/\n7ZMWos4IE46pQAqWD6cZPcsKVh5yDtIq8PSY7ZbKh0csGAYVeFhLhwZFLKvk\nxBhUv03ApMV073eP7FGkLjvh0AbiXnonN60DJng9XCYziEDRhNd2pgOK8gDa\n27B+UWFg9LsBxq/odPxeo2yiRWssrHGWhuW2cokmidVHK6ecLvdceZyd82YQ\nrYp9DEt9t/s8j/7cmqF4qxcU+sSd2RcEPLV30XpBGZ2hTUHxGEz6+P/JADL3\nYAjK\r\n=dV1i\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-use-previous", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bfc70d0ba6be2be34574397c1acfd6abecabf9cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-DnMR1eSvs455D/M0z4fPNuIKT8O8FJqdDOsRYI+EhVwbIbj14c1TpEjMIGU2E5NggewmMWpYmDUBVIfAi3Wtkw==", "signatures": [{"sig": "MEUCIEvDn0VBIWvUA86542kUa2O3dSSkL59R8uOZplU/6xHeAiEA4CwP264RCAQM+tjuHBwq64rkTOCWGoEOa3rzxgCfsuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IQCRA9TVsSAnZWagAA+NcQAJD4VvKKwlDYLWfXy/Lm\nl61OfshiIGSVEhloYIQQAHsnTddrNEAl/p63ao8uP/WWEzKS7Gfp2/H0find\nlWJ4RLhcb90vQfDM9tRzqcA1Uz4MjkSE9IF7RZ40FLMTKLr+F3Uy6zuEcJbI\nl/A99JviwV6anEhmRPmjJ2Yd5pwLc1h3b012F7PE+TPdjpKNp3jYbQStCkPF\nAWpa3dk4M1nQ37DY8z6WK/HEz14I8N8ep7r4VAvoSve1ZxbXZeupKlJHnIqc\nBmgqfUDJxWh9oP+3Gj3qmf2sS01VbTmTOd+1lZnZB4wdNvUb+4K84gaWCt54\ngDWkswfYwJS2oUnHjp1cRrr2ge9lRlK9dv4hV5+6puQjqZiwf6D6e7drM0RY\nj8oi5tWSDYklvE5OIKdJRUgasIDu29YmyAEhB6hlF8BxDPwXfjs9fjzWfSkv\nJBA05JSyhDXv7SN/AFIvsNgyCnwITOkBGx+G2n3rgbM0U6tyiMCmB0vExzYl\nIZRM73ZK2FLGvhjlEWltsShHU5mJ6GphBN5x8CQFV/yT0pKwT1yLildOYiRR\nA7gmFoKpvwMgWagsxDP1SFOEf/VxW4mK4nuMoChUS70y3UbXX7iipbR3xCn/\nklartl2SbS9xbqVTh/mvInPShRSy/3KKRlepNaaMTdEoIzHT8SgFY7tMoCEc\niHTf\r\n=oIgd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-use-previous", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a93faefef5545c2873ea2fe3165272737d46555b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-CbMYgybfapmwYKSS63JEbUyPaQBoe/Zo0GAMAZ4/v1IsBsgZXkSkh3wX9tDZIMYWuyKQ9YhY6sXv+8aohAYNJg==", "signatures": [{"sig": "MEQCIBI1vp69nY/MlpFQ9GZhMlGQh2AwLVPSsHDRfcA1TaHWAiAW35MPnKYdOr5+GU23WdDg+BI1QwO8u73cLOPqqzOeVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wHCRA9TVsSAnZWagAAFMoP/0IGLg26WaDv5vFpBAac\nMx2HD1gJMqhzFZy8gdoHdPaRKYFpHV9I3zFaa38lA3znexn9TwXKSrIJ38H8\nRMTAvzc8RO8ahkUEIn57pOCoDryxex9BClE4XXE61DJLgZgw8XiZP9T7qGuq\nShd7aULl0jW8U7c12eJYKpyWGwR5pMM98rne80FgWR6LTF7mrKMwXfSEPUp2\nFx1/EEMM5Pg2cezscb5sG0vOilTbrqdrMF5X8X6jIm00amvmuDaTBcs7J7+Y\nXSiJqVVyhf7HDakK6q5o8qTJyr1druWcAqUBRAn/D6/dK9A8eLWQTXRv7QJC\n6RFXRRAz3zfS9kvJOlCKI1Bc86/6D4N/DHUpmhscXpqeyKWKW0NsBa6vePC/\nJB3NeNF0NfGMhRz97+NQaU2YwdE6OF2tJvqS0AzCDFdQ+HcTqppmYLQ8YTrz\nq9S6YIhr8RfmDTDHRGf0hkPjFu4hp5U2CHLQybrRkSIwSXCS3YLdfH7EFyW2\nQRHZosM8i9bqPVfRSpsfBtMFqKDVqPYzRyfZ2JIsZi9tx9v0z9kZ9x7NHpyP\nq97GD0no4+I5VqEYK3SkOdZNCnwoK/nt4hqJ6Sq3LBdSntIjW8Jrni3GtkXn\n9LCfFA4OUH3cIlTnWm9m5eB09qcOU7yj0XnrYTQY70Hpl5imf/zI2+aN/l61\nDPak\r\n=ORYk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-use-previous", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "75191d1fa0ac24c560fe8cfbaa2f1174858cbb2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-GjtJlWlDAEMqCm2RDnVdWI6tk4/ZQfRq/VlP05Xy5rFZj6lD37VZWVWUELMBasRPzd2AS/9wPmphOgjH0VnE5A==", "signatures": [{"sig": "MEUCIGO7m4odacIv+BK+1rJkynJ+JLUTVTIsld4Yh/ky8D0sAiEA0scphNKkwqHdFqR2vuZ1BpDJSJDIZ054w8OouXzziDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm9CRA9TVsSAnZWagAAxI0P/15E/vlzRr30mWrzD2l4\nYXb7Z4BkCsSAyGuQpCXl9ehLbuRQXtNWIPzvCecT9ZFc2tOw8vivlGg+zKrl\n3nLcOPrifqlAHsioub+NRILbT+sl9itQPnv70TxaqmK38YKTrcuFSHXyvp71\n1CK5grzvp4LPebF8dIGpnIsmmOcqlJe5vGnptyp9UsVKB8HHYyV2IiArIz25\nWRxcacHywJ6oZovCDZ5QH3BiQxJi3o8rGg4AAoLTm5NW1+J+x3UXX4EDDPBB\nfMbkucXuR9K/t257wIBUxFsAO/N4MelATBu7Xuopd/VWAIR+YqhFicniAZX+\nXoedZm3oBv+jB3O+mSj+Ea59gfMC9h78Z/0JG83CK+eO4UWrkwztr9eTfX+O\nENG+YTDRpgF5Ou1nmGX/DR1zbLyp4NsdfExdnTc0QrQbOj9U8Cw6ftYLc3AL\n4W0OAh9gj9wNoJP1GbObS0ui2Ou9BOpaxHocO8JMiCf1kZcuUsGSUDzUVe8p\n27vqrWLlrrEBlitkbQs3XqqsUfwy6xJabZIpuWBPXZg1wk3C9ZgjOHOWGjsF\nBOV/JsU2ykzwkGKjn/SRfTWLApw0rbbgMD7HdBcoZb4NjcL47iFGkpcp1YbZ\noTcaNjNwSJhB0dP3BAIE76PZCUdPbZwlb0ybk1k5budS6XMqQYJty3x/r7XA\nz+bx\r\n=IClO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-use-previous", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4cc706b290e15ba5a3d4c77fc3f646516b0d5e65", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-pD35Ppz98/6k2XxORuBR4bI0ZuGuHdhRAYYjKAV+LcF0ysvSQr7CiMsJ/+TI4bT7HUr/Ut2ZK2DvLv6ILXGoqQ==", "signatures": [{"sig": "MEQCIH2onwAUMz6zSq/469jne4xWqg332ahZvsUB7V865hkZAiBCua7+ChrtVicSR5JIT2OCaPcWRl3MC09p6O2vzVC/7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgp1CRA9TVsSAnZWagAADr8P/AzTLi5Kffl5zRixhsin\n/IpGPTZ9I6CyswfEbJcum2s8PM3ew2wnDyd8cYPKn7fEEEhb9MW5ageklJjv\nYF/lXpRlpBAlCsHTkxi/SPW4nFliklXheC2ac1APtHYHLKy3iyjC9K+gOoHW\n+8+9P54n3W6E61HX6/IO9YTpZ7JHv+JXoxvxcMVUu5kHS0rMb8isiXg8lhdS\nILwAAlXjLaP83GnqVDNjWHcfWZSW0c2bGQMYbL3ClgyOZFWrASKBi6m+P7NF\nEqb69BqFxAsz1k0weFQH8kTDEW+jpaDyIzeI9zEtFl+knrecNjVP93NU1exP\nBqpjzmjeDhLVOiS5+pFjHxRI/0WQJH43xMZ2SjnhRIep8tszfP7wlFunhmb6\nQLogO1KX+BWVDHHIFfNcybfBzMdegWNh8/eGsbTJaan8wgjpRk/YRJrJMJXo\nkj/ShfP/DL/7lziNoIFUL0xZ/qMSKwofSK8LbmpPKaWtniy/76Mx0jn2YSuV\n0OE27lOx+j2O1wdC2lzL6dABuz4oEIuRo0Fcnu3Hha1bReXOJz6ui2/NhQ+y\nr9w/vAJWOLjhRqxdRqPBJCPrZ91QiPSbwZvvr8GZomlx+GKP3/w7KtjiNn4H\ngzhjOGmZ5nO19TKgtt91pqtgDIUIZRE4/kUfCWH1g2NL8akDG3UfSYRHZ7Ij\nS37j\r\n=CN6L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-use-previous", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "99a1773e2d08eb3dc9c37f1d7aa4b1ba40766910", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-tV<PERSON>ujBZRP4yx9IqmbcPNl8hy3Lg5E2n6iOKaHJEy3pYQ3p5luxRhQ+lac3kfqE9LFZAMcDkr4jfw3z379mfJg==", "signatures": [{"sig": "MEUCIEp84SvdEERZ2jEFToQbhdQsCHPbSVKtHZMGBwO1eEoeAiEA5RQcA8mcjRzugBL2tL0PA7iz44Liyw9JmZyobeDuDxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4863, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhynCRA9TVsSAnZWagAATAwP/3yEGFD/KcDpNjHvr2iC\n2AAkfGHXWbSW8Y2ZNii34PGA9MwwpMotc6hm3c/J79N679Vz2y/crOvmAKQr\n2K60pczUT5g31qJ+lVfE32rDZojNFN3C0sfB+EzC/iAdqGGnXqTWS6nwyXuF\nD+uOGGGp6vURXEVG0v49YU7DzEsv9t4GMRJtfhP9fh81saKzmfQk53Jk3Es3\nAXHaoQJ2/k8IjZyp+eeHfG2nrOztwEwDSRBMFIcUxe9g7FOiHNrvge2CIiUi\n+OJ7K8iFK3D5bODTHhDWPcUB5JuY5csMLoxkjKuHR7zE7F4XiX4zUtyKzu61\nNPCjSDiTkqRuPs04Fa01QQ6bkUO6XsQ1VVQlu2vrMPB50i5XBGv7LfLVGNDz\npEWqy5s2edYscG67pjG6RwZjB+tDOgixBIyVhCIYqkSNmw610knfqWr4Ng0M\nCjTqQ3PvVZkQRpqYWjzj8nF16vHtO4C2QBaPdgSUTbnscKI0n6owZ6RNM9s4\nILzWnDn4i9ndSPj4uOh2n9qtbJ2p7Fyt0Q3c7Y1lLO/Llxg9TxVaMabc2P1l\nNq0qipZC1N239qx3ng4iywx1Ty59y9+F+pvkCEruZrKCF4Km6wjE1wyps3ru\nydpeUDznLIY+KehOUAgAp8DJJTZ25ol9mPLGv6BzCZOo0EPdZxhZKy6TmHkq\nbe1K\r\n=OOrf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-use-previous", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fed880d41187d0fdd1e19c4588402765f342777e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-0fxNc33rYnCzDMPSiSnfS8YklnxQo8WqbAQXPAgIaaA1jRu2qFB916PL4qCIW+avcAAqFD38vWhqDqcVmBharA==", "signatures": [{"sig": "MEUCIQCzkFQHkUA5GWUaaE9cdIVD+qpNfuxNKVy3SUpx7UDihQIgDo5m0LUpBZIBNwS5DTVcr7xg4Fk5ufm9+7KHpHIWg20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmzCRA9TVsSAnZWagAAlHgP/jygzOpvhS9m+nXs+7OF\nssiPPtQYCehhYK2Nczstv44vLvNMA36+4bAbJRj8JGyFIwJrVWroS9oEF8Ec\ngwScgmEte45FZNSgNhX+XzOY5miqn+sE+r22ypmGkrnN5wVSODFs3QebV/fk\nNXEEJqSZYSCoApHf9XAtuXTNXrz5ABNENAGwOQcm27DExDhiLBnvXTLVOsvm\nkZDh8EWxddrzXjGcrEEjYOClL0si8KzeYAXGVrYcD/hzR+vbmjavTHmEooOn\nIPn74EZ5z0ZDBtH8fvJgcy1vfG4fokln6Eu+eV9DxAAfH6Ra/Th880KNp42r\nW4BZNXdxZm43SGmlrxo3pM3YmhxpNMx3ktfR2slgh8sqYttFmH0+tt05vgrL\nhf4LzQJuGzEwHltFNfyCX8qrfaiDyP/3tGhB3F9v/PKU7udlseASkoQi1zo4\n11PqHmSQbvm9+LBSQdFMzsL9JbNVoplgNsfm5wmoI89Ik9M9/7WSdHz+dpia\nZTPEOwvXrvuzXM3aMwNW/HQX0GU5e+6VUxwCIedFVJZXAtkeqI59DjXifNmL\n4rM+KAPyCbq2qZD1suBNSblyiDc1XUqT7nEb7ljmsEUBx5uoa5ux0rnLKrWF\nHSvoxqKyGYL2hGNJZR+SDdVcxZFANgQhU34xLYnuwnnjVvxL7gZnSJhvSmhm\n6wea\r\n=LKbN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cbfae554745d6aa23faec78537a3406bfbcc0461", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-QRCd1+8hhmPFtUeqpWX0Kl7AAslBiYLz/AqF+4DxM4K1o7HiG8ZHgQuht2wZSVafKFxM+dqpJ8jWPuTXPBqifw==", "signatures": [{"sig": "MEQCICJQAev30GvrUimgw6Ei6xjkpdXS8WtigveMBHdVGI2NAiBSa+0RFfNXpsq5Asb8T4gfOu+pA3dqeT0fhQIvlqdq4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3dvMCRA9TVsSAnZWagAA+DYP/RXJmz5HHkRWwHcUpH5w\nWq6VlX0OE0gBC/vGQlXK0Di/UD5Q+2wn2n4m9yJ2DYeJ88h5zIS3ZLf3jc/J\nANsHCj0Ci6t68n6gv+y1bkmms7uk5Bm8haIQblcEJYzszIwIXwtXAamKWT8s\n0dhsyMgfX3npL7cHEpFDe+uIHRrxE31JvHAjOrRBmCqGqXHANFBN1elx0w7w\nAmMO1sXfW1lfvnx5YZ0+2AH940AC8REC8/9Hgoh3kiMwdsBN6k4pUaF63Mja\neO6hOB0ChXO/rHhCSpfxECHFR3YCZx7JAorbOnnDPnbfupe+2ztgkEhSh8Pj\nLJZ0Rg3YUZ/aalWXlfXQiAeQ30HIOOY5bdWcCXts5xe1IArCgEfQyUZBrvVi\nKydgOTwl3dihMtRFqGbtFnUntujCbeTm72mN3KqyXWAMHU7fnRsxW6pEW5I9\nEH3oB5vByvG4TLdx60Obvv7lqLZsnUDN6/vAv8rf5tuiXLfEAoYw1GrceCdc\n22x8bcM4yVH19zR/glzGP3/SzNPfVxbPLrCmmNZXirSynGNSf1Hjg9TozW6D\nRcnfp8x7N4Tp9LS9iEWmeZMf3Dp+WY8C31535Owy+VXKDUS77B91mzq9RljX\nxQseE5DleviNHPZWM2KHrzopdDIFXLd7aI8MRGPxmAKIxV+jdNaEGDUWSfhr\nB0Uu\r\n=0FMK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f9ad471ac9698d36f29fa2d32d006dc75fcba488", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-evwrlHqXm6orWBiXdhf3BgBlYL6AEsh6nB7TNNBNk+wJ1jbYWimjzP8cUfOcln5Y2ItlNbsEftQb2rYl8eKiMg==", "signatures": [{"sig": "MEUCIG6J+fqKFMZ1Xi+hS1UGzDO8/AuqfpoR+4JlIcm7qxIWAiEAv4fgFx11Kl42l/e+ZAIZRlAID8sPMrFde975jrNfkgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BE4CRA9TVsSAnZWagAA4EMP/1wASEbMQnIoJUUyeYA6\n7lRU7wmuvKAnUZ+Pvw4WjrfODpSBcdWLr/1DE8O82Djl2KKSjgYfCGoXbiCo\niUaStBND8u1nA65jPP/IVCI8/bzKt14+pBoAeA4ulohyn3yTuzjRAJwBRNIJ\nqd4Q1XI1JiZn0gdOWz4ANHOi292ynWPamLZY3RPXSMqdS6X8cqs+LCutjd8X\nuLVpr8wpOXoSKohtkERIrZr9+/trnU1e9+IH+YyX22gBpG71p8YeHjv/2gR/\nmuz079buOvyrTlMmz2iGvptPdc7isPzxV4dmnzWH9PWxyBaUJnLbM1/xweS5\nZfhsLFe7Hd+kuYCvrS7pim4lCqJLEiTYr9ErISPEO7u0e9AiuwwE0vfIUmNP\n9+6/0edld2kB9XHo/8wC9WYbCXKsDm+sludX0P6MiP1Wn8XzI2SrwM/q/g1x\nqz5AqzeUMaiwafm3WMdMDtmkqO2uZY3HW4HWcdO7lyuYy++u148Qci4uQRJp\nLqgJOH/FjrAQAxSY00dCBzecWsWmeuARF3/IQpAnBye9luwGHXKur81/4ok4\nkE3gUdja2VvkT+LgRYH8ybTWD3SejKdjOD2GXSHhhbU2javKedzXOyo3RtS8\nl9uPDLe+CTr2RgNJ3nJeJKZYknxiEW2R70eEyNlNVSrnejrkn7M6IN76XCRJ\nYm6g\r\n=r/9w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d338c90202172464351fbbeeb14d16a9bb0ab943", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-aZUOungKTli+CNAO6cBjiIZN/NIyADsEOIgspyrOuAx6cYrO+gBwyppFcw6cRv45TgVNq778beS0h0V5AeepKg==", "signatures": [{"sig": "MEUCIGPuZnwmXRzYDV9IYs15C5rM+ramlTQGNWUXFXahsgMXAiEAhEwwCxVV9S/i7hjKY2Eb0/nCxQ3vyKbEtjiID/fFsnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CnfCRA9TVsSAnZWagAAkJsQAIsn8qZdGoPahstVRh9O\n4OGcAn9SbjpCfX7BZEcHLy/oWAWlfXcTU+iQSFxTeN0qg70DqVfeqo3C7nQP\nvXancr6FzoFSQyhmCqesV5bNeqIbBImypr4V5MH1u7+n857EYu3OfhZKPa3G\nBxRy4BVtg7AUHtleJQGIdP1EhbXREUhAEdyCLT3n8YjIbVnZmLyJryUv2tR7\nGbifiUtoIQcL2+tSQrVqgJa3hWvZMZTmcppEeeE9ctYpahjZqA82F0WS8X9D\nX+xELu0RFtQSPEryDg5e2xP+bWBq3iwop/P9FpQ3lm2sbWXYFAtEouCYk+gV\nuvFaMaFTdu2hgDZkqRzLw1+mvHiOq7HTa+hjAsXhsKawPKoedAZK2jI2VMNb\n/5tKw1H5/DsCKufPxy4mLZFvvZVW78eo1QxeuseqUZJgS62qQXhqbHkQAAts\nkgrUo98RyZ2ukpFRAFwn+WqLWqIt45z0uLW3Dd+mnx+MBgEUKARxekI9U5Zz\nyWIGTJXquo6e9xoBbxJHJ/0xtwt2O515pl+4YhnsAajTt+S9Kpn4YGlfA1Q5\nECtbj2/Nmo4tZn13lFfirCC0HK0RspscNfVhhDh7BN56uE8CTXfztxj5Rp90\n95eInVB3IxxIc5k5xwciMKPoNj6RYv5PrGuebUqt0mjWmnILe2ZQ/7ndmQap\nOJkt\r\n=PscS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ff08853c30d651277cb340f31765d3103bf4ef2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-r+xYBFhQ6MSiUuF5a7lGo25sFcN1pWUYSOQsBHLybZRqSurfjZH6rOwSb7Ef1F9bSiIdzmRycIygG8IuhOpZig==", "signatures": [{"sig": "MEQCIF7C2QU//+bYhiE8rFWuWg+SiROd7mGqFBztQNkMq6PhAiAUbvWhnvkJTxcPaOjKvwHMfb0XGjuAckWbtRCZgcrK2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GrmCRA9TVsSAnZWagAA/hkP/11c8GbHaJEYjg/xbkuu\nIctHO+SchkJSjXKS7m+jqFaHU58iI64CW+Pa3fCR/h4cBHbTzWB6ZgSk4uc/\n/sH+eL/cPIq+nixEbnMWjNtBimapVouB5yw1aYlEXvgrVzaC+1PGRqzCXkfs\nT2WzakPkmYqObpgFCiak3Hl01HD9avA7PkHasrHPKihLpsv+N5Jezmw1qB7g\ngD57v490E70qA2DDT/OH6V7PlWJmmlRjVDH1hL4775DpTiDGjQ3Dy5B8p9Y3\nSTXjCzhlkr3PIGIQNFOKHSceHJrtfd7sSinwzy68qeRXfPN/TIvvq7/msbSn\nyjeAlgD6qZYGD55r16yir/P0mqS8raOBN7gLacxiHQxYUKpQfFh2ONrJn0ZQ\nGLzHWYlauDxo+1R29guNxiOM7plWQKmKKp90AxRkWlBOAKQMpvwF3x6rN4NW\nVubO5lyVtDsoIWNphh4CBZ5pJxkoxhK7Q+BCicI84LHETC7DWqUkBfW36RYe\nK8VokvDP9rPioiK7QmuA6WqCQQpuKoTVR2bFVZTEMOQ8Mz5Ne6N/RxLKyw0p\nTOzhouLFdkgs/YtlUWtqEWY7MIUgK1qQQu3JO7Y9hv7yKjGq0SSTmtKSMWAb\ntF+YshmcAUaJVQFcchIviECK9MiLX+2P1KE9+cI18REoyaFXQmXQXw+Mvl3N\nKgXz\r\n=yhcg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "42388c36f73a895f1aebf1312fa289d562d9f6fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-0zS2iXuWVKi0+9NKNIqw9H7+GFOO98H/YQehRv/cjrhSYyxA6pm/KK8ktnFMzkzMG5PDKrxhQznSbAdpHz7Auw==", "signatures": [{"sig": "MEYCIQCClLx4cPMo4yNOr2Z5m6nn/uouic1+jnoe7RS354ZicQIhALZQGoRSA6VBaC6lChKRO6v2stw+NAN+VzXT91gaptSj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZdYCRA9TVsSAnZWagAAESEP/0KOhlqDkEV1I93Cs9mf\nSLDq2Xqu55X5GaG6e7NXGOnfyC3TDuasXoxd+4grpSzxUDCwu7/HE0WNat6l\nnfxXlYU5Wg2I8siyMwQQJWOZZbXoqFMcyj9Vl2DdHZOvG+UknUPpoGFept8A\nRN9jSPPG0eQLPHDWLXJHXWIRmKIDpha4DYyEk7vorfhTaPTCQ0lZJJRXHHlj\naV9kFBtmQ++gAUorqGgLobJViHbGzOFwWWe1q6J4SjpqUw1FLvlbWGtHw7Hf\n7NUrinhuJsh8NGfvrbpggw+5aQdEAYLyNKgN57GtPIGcyOiLJwsrv63l0xDS\nu19vbRjsDNJgscfDMUmujAfGcDtvPYq9zWU5gyICnSeVCCCFJs/1aO4XQcKD\ngpMgNgdkwpXpr1TR9uzjrnuEg1QUZwJjkTAZ+Gzig8WK2/0IWRpuPeLhzWTP\neIY1gDA0TSVpLtVvVkOxn5yR1xqHXkoUz8lmKXvuVj9ucNXgvIKm3UIt1GZq\nFob2VdI+ZdF8MGINuj8SGHOnSINRQU0LGJZA01Y2NvWI7zwL3vRO8Dg3hEYR\nJY8j9QwrZR1+TwLGZPX8M/f2LEpbwZD9h++g29Ey1M6O/3IA0owQVnDvamW4\njvVk++UOTxaUXzXbqxQZLpG4UwgjfEgIyG6F3TnL6KUBHtSoBblYY2QiL1jD\ntm66\r\n=mDMz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "38845010fe2324e33279c01a8e349d287eb4a699", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-2wWXTCb5UysvQMHuIH1OW4FiygaMbn8gugw4u/w829l5tDhAo30CB0HV4ug35uv4DKOb+q6IdQY4YPn02fQosg==", "signatures": [{"sig": "MEQCIDSrvfxNUNtqr5IAB21+VoGeO8R0PG5bOw7Ziw44amH2AiBAQKxSbPsRskhtSX/Q1c2M/1yzsXk1N6VTmx3IMjQ0xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6Yt1CRA9TVsSAnZWagAAecEP/Rpk/4HYOFr7h3Hc8Dnm\n0W7ThqMqP/TJ+KXLDmM2m+tcFC9ZcLEvS8To5awVn4LM2vxggp0bI3G4fVw9\nc/Ys91XnkYTaoipTAYnaxnwaKHs+XPi0YT2L/H8ma/2Y/uzeTz6oesU7e0lE\nWFkn2UeEQjVQ1yr2Oh245HpnOgn2eIFdooVMPVLIbORp8ceOWEJk+Gt4hVv3\nzfnyqLpbmWjtvkDGS+EXOfT0xFNTTTBt/89bJldzslttTJd5dEqcmlJwUwTa\nijyywRsM0TOznUbl33njPMTQVbGDkOTSWVuYWbvRQBu2Ho+NkzjhzWYXn+RS\nBevuPf726VhvySvJws8kttKYMjrGgWiR124HiLDHP8nEw9ZkSvie1Gf96BBa\n1mqBVZEgJo6VEFa3IvssfBj6GJUjxz8ttxcV4vqd331SxN3/RyRpbEf9QZCO\nBLoQ4/ORV8mowTGy0IF3B2mdrWd/8CzC3kdR9wIbqrRyBKB/n70juo+iF3eW\nUiUKH9qqQNdVGGWNlpLBjU8KkOeLIfoBp/W5RZ1GrP1dTACK1CifwIbDWasm\nVzsaxJn2el6FG/T7Qj4xrJzY5SMCTO1FvKU+pyQYhMn6yQ2kBWGKAw8k/Gkb\noSZLphiPq0lFF3I0hsCb3heBQr0BkPJZih8UD5E7siCtP1oHvuEbSM0bQkJJ\n+My4\r\n=doOd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f4b55b554a0f9a1bd174c7a18844ff1f41cc2bb7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-h15Y1fK4z3owaODcSnTgTlgrcI/4O8UlBxBMLlqgBEQkpOmRFKtggX+H/v8LCdmiEPmRZ6eCFgcUR7W9S80u6Q==", "signatures": [{"sig": "MEYCIQDu4wSCrseKFLVR1zL7ekfVV3rk5r1pYQsX8zi9eODicAIhAOLY6jHhBbYuWCKc53um4BJe3Jg/ike8LO+fZqOjZhVj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6seOCRA9TVsSAnZWagAAw2kP/1ckU4sJ0NyeSxVT5x+K\nS40zSx8tzFllsiiq2Hxr9d+fJjiyGWdKxnNu/zuEW5i4JtDzqi51dZUcnC2T\npc4/YEU3XRTQBo1fz8Io4FkT+z+fqk0Xjqij6LHeRLMe5NkNF451i2iLGqXO\nLDViqVt0eXe8ZNMqY+Kq5l2ChB1Z7MqzTALPEsd34119w0Q+efKC6uKFwWpN\nYbVoh5wO9E1gqqTYPFuN3qml/EcEKYnYT+119uQzj+2zS6pQZm6NmKz7kWa6\nYIbTi3tgSWRfnXWyvjnk62v26mnBcuI6hcD/M/pzdaN9EizNeQl6sHM+XxKC\ngAilUnvWtVCgvaIr56+jcVUowyxJ5gqDoIgOiHpMf3zUA+fediTxXnbNPj1E\nrUl6PFq1CVaLz55R/MCtuul9TNZeP4UneDWsOQwAsBV7qj0J3q5Seeo3J6Kr\n7X91ZXZikhEYr6jaKw6psDqaH/MXAU31Fsx2uUF0G544qPkO8zpQ/i6J/BcK\nAcLyPmr/6uRNriyRzhBAvffmJs1uId4blrPUBMOfuTs54VL58tN2nObZztUs\nL1Au/CPXMCjTvkf5gty+bBAcodjNiFdClnShe88QhirX+el7I9qkyITJ/IWA\nRGYrsTR0EBWu3CNGqdM0iFOG7te4/cUJetizGrPZckttqEQOcMR5wymoN74H\nNaAk\r\n=RrIp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.8": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6082ea38a76565a4dc389c5aaf46b348685a91e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-nzCOlDOW3pIRnaHwnvH3qosdBS2DThBfbCfpJt8tUMJBJkJqQcbfHolfnb1pvSUhEl+jEDAHMbdGK1lNG2rT/w==", "signatures": [{"sig": "MEUCIEQU3mywHUY6kEe0U3nC5OhWu7euygQJXmqUr4EQT4pDAiEA711Proopb/KaeUxWycD4e33FWtvcc7C6jyFxu3xO/s4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xELCRA9TVsSAnZWagAAXkYQAJiKWrAXAQKN03RXhndA\n9mh7irUYOAn2W3TO3W17xzGoi7lCKnfy3vn1aK+d0HMbGtLcgf0pdi6n3EHT\nH1ptmjujDphG3uR2GvoRRESyxqDiPdMFPqQh3d0/BEL+ijWL3rweeKBTKf4L\n5I5qXZibtcwi3iLsnmSifFkyDMCSAPpLIOeaC1ZSVSIAnkWy3cLgy+zuGOPV\nFJ8Z/t+kIlcXumx1i9YdlSRAOr00GL0OlQ2czZXXVpy7pzsacz8km681je2X\n1M9Z2VpFfLjmhZBEaq0L3nZCOoXW7/CfKCiL2dtojAlhbWJ8jkXTAZ9DqBAA\nMelDPbuDAoTuU304SMg3Os7biPXjhMQMZOL/9ZLpeMQ9icGsjgdg8nOGMnq5\npOj0QIp5w1KFRdI0o3rHJF4LCX/HrNFFdQmx2bwJlIwmSF251CNY3PbbyKUh\nym0iJI1z/TLR41eusKxDXAphAb6pjTrAWg+tT12dEgOGMOqPYm/ulbSFgjZm\nT28LhI0RPfjMA8X/9bOXOU12LWDIeIaEzllmqm9Fhyh4bcXHnEOSDow+n0wg\nudNxMHaYzANp5AGhTybPfWmJG/dkEOzPChxjaQDlYW3QzfM7hiHNGBCO1VK7\nPRtqZjyl2se59+ItH/IVgjUr3SEWaPDmaikO8UGGh9B3xWqrJ+hPiGzZ+H96\nL9AW\r\n=nOA+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.9": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c3537fab874a4f9e13a9378a7cf3c7dabc9acf85", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-1L4H4/1YqlM2CInpPW3enklYHbg7iaCYJGA+ANqIlJp39Yi0W89VCUAzd4K1PCCYi5CIVNCb8l70wrxHrmibGQ==", "signatures": [{"sig": "MEYCIQDX6o+zWCwsVtFeTluBXj+Qv+j8E5nX+AZw+W81bcTSRAIhAJbgeHYV6tWSSsAPEJjqcYU0t4eGfaZ3165t8LCTTryB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xMMCRA9TVsSAnZWagAAEp4P+QA3/bduwH1CJ3I7fimb\npzwBZ2oa0UHpWtU4aGXtlMr5Szp0zmhiDToIaQbXbEDwKRs7WSrcKdNzK8iI\nRErne/p947iRmGOxdiOI9E7C0tbEVfrTBSODt1wAMXD7NyU8upk0v/412cp7\n/tDPq3GUCiZ9u1z1hGLpbQhvCYVUcuXZJnUmBqX3wiQ3cV9XqhSLORT8kdTK\nP2nuXFoolXHnJF/lWpeWKH7wzO0SiblJMmmcq5+2GoOZJbYw/y1lAjk/N4nO\nD0gbgTGZlF/vwMilnUuRvgX1U3xA1Vi4/Vl9ceS2D8eiMUs1pwwFbf60aYqi\nGp51pvbp8nlMLtjh/BtG7VJTVANq4ur5vfwooaCW2AGcZp0fenRNtZFrAZ7b\nN2aewOFcBhWqDF3gYkoCcWu8rq0JOT4WJuxaPhwHLLFS5gZAKWHOcwij/ZxC\nzFE4FaQ3IjMK0/26MDV/IdfNxWMq9bLrvTAHbnKWfNqP3V1XJWOXAHzFl3so\nNjzNd7bDrk/9eqKRirrgJXAF8z3uoF7MWtavKTrJYPqxFhsY9juije5ud3ET\nQ3UdL1X93jLJnvOF68z6PivJ4YfGWENS9z885nDe94Jl4VdyQe0TgDTfx7MJ\nlfhodhcYurp5BIDmp4scbpQD7P1SCoqcjDOF5CgQ4PTwU5PgScfJv+aREeIZ\ntAvE\r\n=qNEe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.10": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e8ad19db494e2bed7dcfa0453926d7813f4786bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-g0BpuJSGbjMgbmTN0JJ6CKjH8I0NeL4x+ab7plcNTGsh3PKw8kLP/YJprwPYRK9GSvhZjHnEknSRyUMHYU39Nw==", "signatures": [{"sig": "MEUCIBPT8fYXOvuZkE00MuPzK1fDAh/h2YHdW6jPttTkVcdTAiEA8eqQ4hTdYcWMrdgOL2RdWnDRyFyCZGBHeDhdNpArCjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D1WCRA9TVsSAnZWagAAAawP/RiRSqWlEKioK227jR9T\nRAmYmb0Q9tRrUacIAzNSaXA2Z66NAD4bUm5ATOKDTxh1FAx326LO0hWL/2rs\nvi42AY/oetd5IOyagYyNlIpl/dvqobhfBslGcO4tizcA+H6dPhnpByIJRKxW\nTaVtwtIRSAfDyuzQqbY1js09QliOi6yhz6KL1faOtWxiXMSugMbwwn8cNJeR\n+4EWkwOlXqNX/1w3uiswAf+GTZr/H57vtz5sSIhfThSiMuuu5WbW1EO192zr\nSGPz6AbcHXSw05adTLEQAkHk+azUlY6GUvRIOGU4UPla4csbrPjjlTse11sG\nfS+jSfMSdnQphH+Ef0VlGwxfP321RRnnRbVE2/WCkCmz2M44Q2/XfwmYRACm\nkHXAFuWcMXBiSTFhOGOUQJlmZPUmvJm8ZjM92S9Bs2A/wyR28heNWF5ECuCG\nte2QZZZLO40dZqGQ2iOG3l+ozPSlvg6PyMHuMfU06SFqOG2dc17udoMlXaJU\n4wCoi5ISa2NZwe2Dl+eZ5Qh+X1vcpbHSg0spBdK0DnT8Woz55VkaGNoKr0BH\nf5N6HGlfBeSSrUnPEyOB4WI9pDyx8seNIi5AQAMJVTTIV+pWu/8LWZ1E0HTg\n47c3MsWBzdMACTxYeQjwQgoZbJWAeklnVtDfF/Qwot2XpEatb3JTfXeTlGck\nBdOX\r\n=KuMj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.11": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "92ec330d5bd34231c1ca760f6dc0f1c8cc829fea", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-nMkvPwwYoZ5wK6xZ+ieYfBViR3mDtm0SgvFvohCP1U6eihsrqRK4VA0JlSCdrsAqWWffCXMvROO6VFUr7fHHOA==", "signatures": [{"sig": "MEUCIEMvdC+fKhGlBpPOC3Tng6CgTX5Fvg3LJOmf3I6KosEYAiEAxhBXWK/4wB/ulz0eSduLa7AyftFsIe/K70jsGeCNaGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8ST+CRA9TVsSAnZWagAANrIQAJ3bT+e2FDdWl6JBKADt\nD+HOG1j++qO7GWFuNR+qKbRs4uneSLN1xRIFMFArP6taszeOCuoXIgWFdc4u\nlDhBw9KDBQKzz4dJyNBfPS93LZkctoqjRba3GW1HzHtaL8p7lwDcJwyK64UH\nfeli33O5d4s708zvxo/NUG63NPiMQ+w1PhzoZ4bqD6oaS6qDB+bvl7vbOMSZ\nH/Zrc2Eg6T0PTtpfNo/Bhem5aOQGFxSwgB/PqH1uwNd/dDVB2lmVAwIgwQwM\n81HkzPYA2swYIS1Lw5lScFdQavgWKwEqXcp6W13jOlwJJxmRwAAzwZd2/AlX\nVi9dUl16qwUVB2Jx3q3u+K7GE6wX/StcMJdGLIIL7dbfOR6+UOkWHG78fW5k\n2/w3oxtB3Q0mLzcnR0DfgQVtvoJXSCWIUEWEbgzkN67KaSMNaA8Jy36uleHC\nn2NEflopwqXmcsl5rt7Zc8faACDCANF5UI6PNybFqP1lnihPLE3cnuFEdVl5\n1D5eDN/BbfqDL9h7PrGW2P3gF8hsplWdJkJT6BRMtT708bOy6lZkyIP3LCOF\nho+P/KJ1Z4HbFpVwznG8kFs1XEhRAkRMT7dZVBPkQCOnPn0zt4by/0qhKRPO\nnGMCPQKYN8ohdN5IXxpmRKBx6sArDD622j8Q5fXLwzVBH/KR4ihzihnP4QUp\nMJ0U\r\n=H81d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.12": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8efcda1e1a99ac3f8648383a2a82e24fa73eef43", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-52RWs/BGZub2y/0Yk2L+0FOeRjt0LO4CQshp9gfb5nAdfwcxiou8AoUSK6wiJ0za7cSOzW1A9m4LgRXgu7ckCg==", "signatures": [{"sig": "MEQCICT0HT1qvSxKlIs09hxZZZ3EYIWmw3A9lYhVL2K+nK7nAiB6GR5ZluYmK1wAYTvAplqYsUwIueOUO1iFD+fkL2240w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DayCRA9TVsSAnZWagAAt8sP/2yRMMc6WizGEqEDKQpv\nSFKJm5aKZgAQOrCldcY/lu/4QXU6ldm+vUgxWeE67NF6tNvGiqGqks+q9f/J\nRaTb3uucZqKNNXutYKhTv0nvQiEy7QwkAkHpZSEdhiO4VbAApMqaSPJN7OG0\n0QPo7iLTku/hRw/u25Vgjc/Da10/0VpQEgnZTYiuq8FgTq36OYbU1WkjHgUC\nXJqG299VJR/dZ9zLuZuEiszB2DGbT/QoXoduhKkVvNgxrLBL3wQPDsdKnSCC\nAMMePZMGhuhjAQf6W5i/hAuURmQu2V6z8sR6dOVxw+QQoDGdhQhXovRk450/\nr+wlhg9BiBo7dL0raj+TDR3A/+t3ansMgx14HKGtMUxqLhfQJufLZmk9rCdY\ndI2O54TlVKgDF+6mqS8C3nxll2sQQffqRz9lX3BpPvn5nT4pOfRo85w3dcTo\nSgrqv3aIf8JbUYO8b8kJsYkqerDr3xjbrgsKPsYWKf5uHXibZ4OT60U4K5yx\njs/BFn2mI1eiUkJWDNlzHsTSYUdTEZak97JlbldhVh0swnp1teXvaOYy0DaI\n729vE8MdnFpZhjCEVyhS9tO22cPMLi6+3Inp0kEe/xIe02mIj6HS4Ax31YgL\nYC1spK3T3aBHABF+u533sJv1tZ/g25hpab3IfuevCYLW5zkIRaSFhUGfjaHR\nHtaI\r\n=oQss\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.13": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7f8be6c062afdf7501caa4cedebc0c51ecf13767", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-eorwyTHo1s0bjOW9gBafZNSn53TfJn5JWIMrCXGWFHNEzoMDCy8ReMp+jSXkOvw9aIktk0aWdlgCc+sCbUVCSg==", "signatures": [{"sig": "MEQCIDf0t7EspzQ4jTqxoK3KGiZ9aGSy/cQst7Ajb9BRkj20AiALQgdRvnKjD0M8BUam7goiXU9oczYO23KRrUgYQ01uMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WpJCRA9TVsSAnZWagAAlcEP/3/t07K8KnYJEeD3FbjH\nc4P46LmUCfzl+0eL1JGMpfGs4pDpxrQUbJtaOrZxvCbZtvyrR0bs5MA2NXw+\nvQE2Y1Trw7dhECo4Gp35EsqbvybipLq++N41/oNQiMJ50I9UDoHRbiCjA+YD\nURmB++7ijJ6koB8SMZsWH8a0kumBqp0TsAkLjJxcT95DrmaLspxCxpzprbwm\nD63oJ27F4LjyK7uFsatU3VTqQ0Bcp9NKdMcFbrpT3pYfgeebCv3/mO/pRDEg\nN889iygCxwrd3C+8zq9CV6rsUhTXykxC8jej2/EH3GVSr2734YKmm8BxNMrT\n5fJ6SxSLxL4DSIGsQQSLTsK7aRwwWkGNHNwOGPqIUEvKYD7tWsXvnHmiouWz\nmzcdjS1sX9TBHTnz0yOjQEX0C3Hbgy3MPNoaowAWlJD7HvQFBlAian7sTX4w\n5Gq1GzkRM2vkyAphIymGQucDwlwUCjCKcX79L9pB1Qmpvw1k8Q5vqZ2p700g\nENJJggi0lnEZ0rcaD+J0sOpHmOjibe2YnBaUmJxFhAteYC2mcCqbVP6NVAXD\nJZwkKAqQfl47uH14Wu1fvFENJF0P1X+L7muVWlHWe1Kgoi3jH+Ewk8+IA93p\nuVS4wxemaxKqLk0CBdKPyN9ZUM2JRJ+lViZWOuu9w6EEKyN0PXWxzjcovJHn\ndUrk\r\n=Zqh5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.14": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4fcc77ab5e09cf52474ae1658a99b01f786a48c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-UBmU8a71ji3CLgXSZk2aRA/0KbLfxQksneCT2DCqIDVHPC+bBUBJUtGFXzNeQS+EMBbQrNhf/a+p+Pk99rf1Tg==", "signatures": [{"sig": "MEYCIQDIC/9m3ObTk7ZvC9h4d3gM0LMcC/R/l1EFHZj0NXwWMgIhAKJD/6jowsFb+wh32VrfprUXd8tJ3jf1r2eQTIjvWJS0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rVbCRA9TVsSAnZWagAAIgMQAJst8nG7reroPxQKXPd5\nT0jLGUVRmp0bnu/zQBFKdg0/PFJTETKrrGIg8sPVqP2Q6DK8O4lG6ogiLlHI\n9D9r1H6PGSFNTbOVeW1Jl0LpAOWyP/OZKLcOnPkP0tfdJLBYNqX/isZ3sbcK\n14xvIvLoBW5i/nAfQZqbgHGvDeDjx6gSOSsmPicT0Xo/O8Nan34CiTnOm8jE\nljsLdxsckfSy0RNtp1LK3JV9IxrOOm5uwareR7aY7kv0GgJg0GuE5JI3kwF6\nSTCp3VPVfl9SDCvkmJQ1Shq5o5j6CLQ4rXFxgX1Os1SbdVE1ImZNW/uJgqOz\n76esqrZQ5FVjM1IjoFe86DKImYvYb9fQzP5V6LISe/3B6v2hZ6WjZkZf803T\niWh3gd9St2JkeZdZARCEcSmY4ucBQYYUGecGjMLRgob8XiK/PCJ0ykoXWmQx\nLnO7Sb/iJOa8ZVtMyUKW0ws3vQn6msw8/eqetot50ZfOk76xEFrcSUXnNz+8\n69i8N2e0mFqNkQNP4WSl4kezKMMyVc4zJRoECfnN6kDFKMhpzZnmLMAJ/1Uz\nvZdN9Cxl7I5Fw8WmiJN5dtJ4UirG9sf0GHrQPFJFq2uCWHGGjpndT+X4lt4j\nCugS9WCD+rf3TugzHhi0fatYJNSe8Z/vlsdNGH2dsIN3rEBQQgxyRJqHpiI3\nb79a\r\n=l5lj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.15": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "af02923cbe59957250f1b927de717eda9e66a6cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-P7aV/Zp432hBA4ik9mxapnNy6sbu1oazU3TobkQsLwvmH05vS7kK5FJHUwpDukwSbY9Fc+oXqQ9QZcfqreoA4w==", "signatures": [{"sig": "MEUCIHKv/lRT3NRysqhqaJIbgZwGJ+/v9QE+zT4TwW/kxrS+AiEA6Md7a9ikXGfYz3FKqql4HVpksZon40i7jg6KxsQu8zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/ofCRA9TVsSAnZWagAAB/YP/ioFfMHbVtQUGhJE6zCn\nQjnPl9mxJGbfbpRdvIrGQcQd+4lcaan+qclOxJ/rxcH0tTHwm6VX9BV/fjCh\nq46TduzBPwm1sxu7OnFw5zWEWJSrYPnTH4qJEpxRnYt2oagW1cP4ZB8itttA\nTGEke0rBhytqDxVH2wrlLwOkJCAAb/yHRNsMNYq7KulAqFQhaZgcbuuZoHrw\nMU14sNhGFdXPgQvrUjP/GblGllPQjCPfnDiUDU5cw6N586qqGPE6uqaIvkXO\n6O7Vt5StPUYmDTGf9E4prroquoBb0tD4xssQdK3XEVj5XdK3nO1etYXc1XPg\nRKHAIw3LU/VIbkROiEHje66tW/GV2Z7dYiAK+4iVP9Yd2N3lQMifjJ39h/bh\naYWSKHg3+7LLLdM+FoCscD9PswVFSIQkI0057jhc/Yj2zjXHHF5chQG4kWBS\nThlXJuEpmFSz4Ki4aAO+RU63OxpXX0nwVHDVDPGGMwXgC6X/4BnQ45D3dFGo\ne2ha68zsy6WPKPtg+6RPfYCzKujWFVFHvsF+fFZqQV+x6obrqnGZmDdPZqH3\naPs+IseURPzR+rJxldj2KfqKlAWQyXS9IwfirBzYXt5ey5utuNr/M1UhqGwg\ncKbMvrVHlunLoix0ipKYKihUi0l4/e5nxz6tgodmhaz17dixksNgP9MPe+Kx\nzqAn\r\n=mfIK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.16": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "02bf7643f4eb89ad94740df62276b7d54b451041", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-yysRJ49uiU8d3ysCZDr9wrXnTumRQObwG54tFvd49/wN1QgnqihPEWf4L6+6NKlN7K0lJdwEsxwOLW9zorde5A==", "signatures": [{"sig": "MEUCIQCkOhZuYvH5RAWTv5XdAHlrQiQxq5quF6L9Zjt2MUrjEwIgWHUWDAGw8wBpn+fagzreUT1vtd1cFg4DPDY9vIskDXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBI/CRA9TVsSAnZWagAAxt4P/ArpF+dVEnSRK1nlunU5\n/nb7Spxv7+uYi2sCo3T2NPPjNl26q4XnBGp49J9ppqt/jah4nGAmkPCOov5I\nYOV+XXAs+kagpTl9VrNMHaLWpkHK5PRheDjztraeXpaJ8TQdP3eYxcVXN/19\n1aG0JGUy3p4uoTt7JmK6rHD1j280OUuUf7VrWYuDKMGqR/urVwPfuxwipNIy\nATsSv+zilv1/hFz4jClOQtajwb8dbNkf2NMqBy2e2q67MugedPZ09dllTbYW\nb2+SVYygxebNdt/5iBhGPkmqzZvQmlNa0wIogGxrDDijAY8KdqbxuBNLtWIx\nRJXQrZZG2P4igYDfAiEsmq7EInGHmIjQvluk7ujkDYWKwRc3LAr1AWbERKeb\n2LcvW9j4M8kl7fZAhIE9/PnZm6sM3as6WZuTxmoBPCD3SqwKW6Bcpr1tpbu5\nAKfTodRw359JTe+TkST4+iYjd5hN+A4t8BpUYUycnH5wsP44rzgKuIHaolCa\nZLlXA1PKAPC7K81YJ2i4j5o2HkgZUO2vIWuNi9uchdjhBFoAbOht8XgNFeFT\nFN9bARgj0lSodm9fUVWleIukEhyeocjwLGBHrdW5pgCWANRIiF1+pHKU6PlP\nyURXrjoD/qKnOZ6ixGMAQyPnPEXC07dgWWf8JZJpTuwQB1fO3r26+nuU/SBa\nLX3j\r\n=c0H1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.17": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b86a9a15847b9265706bccdee17aa4e57fd4aa21", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-WG/LiK48/ARj0S1Y/4YtKE+g+77xhbCYf4SEH1G2isdFDbxnQRJQoKrjdI70G12awWb9PIC2TzjR9McFIT3/gg==", "signatures": [{"sig": "MEUCIQDDA8vItwnCIaQi2WwETX7O/mcNFZYcu5S5NdypvKjlVgIgTHXAed8nqwkCIuhS/E8XaNJvhOZXX0azs5FomUKaIjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBZGCRA9TVsSAnZWagAAFOUQAJD0M01cGUeiK92WSdZM\nKechLUSjGhUZ8jewH+ciaaAySXZQiFamwtmwUt1/Tyr8dRXMnvj5C3DWwKhg\nBJ75Cb8jHXICJreArBH3aSkHYyL3PzXbDQxMCpZ91nBKRCwKGiDJC+ew3wyY\nfab0hbqzvJuFrL1wqfkYKgAf4rxnwpTXVbCe/tRgqjTydf/14f34aPd0uFKN\nOjMRZxiGMo8nQUp8p68cPbfFPrsZH6D3ujwnSmVbTXDvabJatJvuqRE+pj2n\nDVbxA8Ae4b6LfJkLEmGhDD6C0/LDpY3bNvI61t9eV095ml+aFTItc9/dxJeq\nHM0CCrEDyxwbE4xXAd6eyQo4tjZWuVPIe8iTJcBW/GGKKMv8Gt3HZfM6znGM\npx6g8UPeYNk/SQ07OQRnTj4Hdi0Zano2erS21duEcB1u6d98wMAOT2zHIHBF\nree30ICxl5ksRjEyba/imG2fNMg/vu7gM3a3ZlTJwptxLLUsUNf72EsB2wqy\n4TFW2OYq97Ab47wDu7AaJ4UiOfjGlKcGjWgOmDYw98MG7IB+HgngX+i6fc52\n9YRCLmU8Bth+9bhsQazjZdGstAKjPHXuXp8S5vbXCqTYsAF+GHt/TZCL1tm9\nDI+yoMucQ7oKLRpWkB2FYjwAw/qlK0wyDXLE2U/XyxIT/b1eCvYtEOzHf/Bl\nICy2\r\n=AXzY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.18": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fe7313a41e215196b7ff7804ed83cbf8c3530e0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-2F1MPh2aqRXMiKg5UrXjSj7a3ChbbT62ccJHsY83xqbHPmtCll4NGncjdb+DWddhHeC+HZaDrj/i2EwViPYVdQ==", "signatures": [{"sig": "MEUCIDuYpiy1cQ77gG7TnruZU0r51d1UQ6oYbbg3fjm/Q+M6AiEAk8dlZ7v7cUPlpIVzjtAJGKRFG+l+APwgX1zGMutjTs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlmAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrURA//VAmRC7npO75oaFza1OGjuSq3g0gizcnkkGdoNCiTg431aRBR\r\nJIYu0LVlOYHMUXo6h+PLPgYgYzN7c6+DsZgYaeTrP6tCAvxRNtVomclp+U8n\r\n/HnDTuriV3VrOL9lCvhQS9+xqgNEjm+9gDQ+/pEleg8w4AYkrKBvUqeHWpLf\r\nt6LW4/V/yEeEe7luj/4KRtUYNsI8B7r03RoNi0CDAxgmiQTe2qIexE1bwy7r\r\nxTQ9nGR57WspzLNTL2O2HtWSfNQ3WFZjQaI6fVJhI+9DB6es6rU3vIyOv2oD\r\nbOmaRnRF6oSZNKaDZXzAS4qO7qctw3vri8jazLCwLYp6qa6yX5p3QHg9/7xB\r\n3PUq7Rid0rSL7PIlpaY6q/FbCvRXmGPu+ncjG0BvLhI+lwfnLdcaOvr/yoK5\r\nzH0tAf4cFx2gixYZGBhSbRwRgqxR+eoyQCAPj9g9o0tvqyFulQSHjDjMgaXO\r\nhqa2zReTGIsvblh8IbjSZ/VUDg9ug0VWRWxwI11V3ebZ+kiHtqg629pWIjBW\r\nJhfg93PdJcEH2yNIpVm/6K3X8mKjS8yArU7wiQO6jZTiV7aZSw2LwMTs26Jd\r\nRmM/cRUB9LTSQRLVV6s+gF9bsiRpmyeXdmDiTpze8tWms/2gDrujA/BTaek5\r\n079URrIulu0oGhaljA0ahvdFDSndEsymyUw=\r\n=fB4Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.19": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "579942591298c6b3b286d1d9bf5b14f3330a3b8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-LNXdBhsHZPyP++nb0sIhJnYrPqHFkGzPsO3w+mbkefrX15vLhwFdr5H3OsN0D7YA/et6pXAGcVNk/r3BE959kg==", "signatures": [{"sig": "MEQCIFNmgnQxGoaOi2Y/lP86BAOBkczGHpnRcdL2JXMhI9vZAiB9y3d6koGDTg/dI981JUqPggJeCqLJatcGfKiuHoNDGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhfg/8C+uCb7fyiyY41qavrNouHZsoVT3YsFVmeTJe8/RCGDJNuwyZ\r\noc/w3DhXg4/PfyCmmFMmb681wydTtdkKu87+ZReO1QIqs4y1PoQ4aH3Qbq8b\r\ntlDHgBOe8oA/eG5HHYVjZK27h/bta6qeXCcC3pwS8/PnnAr7ogogzQ0ldnD7\r\n5v5p5s3cdTjLmyQfqQ5XsH9XcirCzsuhUCLD8p8hTUbEOTbmvrah38/f03QT\r\nDn7F/EgOd+iRzs5uCzDgtwlW2PdPw0ATFnMHonWESP3qH9mUddu9h/kAlCPu\r\njKDD7B4KkUTxo6IDuZVDNAMx55ZaUbD3T4jbcFm/oaezgq2Kxv9CLhKumNme\r\nFZ4f/GkeTz7QrRYye57f1L+DSuoqzXMUw5bPTfq6S76NvzmM0BabEMRT6LNa\r\n/w6Y9eD+FymLQZtUODUK6UAC4OcT3lEoGlMlfENfSynWtwV/mCoeGVNA9XSG\r\nA0nfi8oRlsivAMJEtHn+YfQDNZPEcveZhsfLLJqZUyles5Bnyte2O25wGpf0\r\nenzXCQH/Gi9EbqnMOW3SSSFKAykeMc6883aVf18TfCTYr3sEvajjf5FDbVIk\r\nxn+j21kPoIm0juFQjags3uwYk9tEd++o4qZSncfd+68EJbqFZApqAFDZs2jd\r\nPgGeOX2gnUeTx9rl5ETC1RveLGeVoUyC3VM=\r\n=FSvf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.20": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a7566dc18f1b0b1f78b0b7fbd0f52b4937e86ed4", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.20.tgz", "fileCount": 8, "integrity": "sha512-jmXBZOZ0ESwOCl68stA9Zn2uC4Xxo/EkobKCXieJ/Bwr9VQ2KpCchk6DHEbwAR5ZgMj1MOAHpuAnRSqj5C3yRA==", "signatures": [{"sig": "MEQCIAFfTNosUcAYO8lZU3GQJuXbO5U0qqvpmUiDJVctdBXZAiAikzSTNVbAoM55x3w+QwTHeO4h8a9QmJ86wukm7P+hJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkd6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2+g//T94culKOFCPWbRwgn3PXIXKgBClAREHpwDxfryBNH9ChTVE+\r\nQ9kZBlyr/HsSfa9IVFgPQuiXOEkgY8hFWluqmpocNbAQWz3LpakQfoSn5u0a\r\n3GJZZtaFWSqXOzoQlHRK4MvP7V9M71uE0ZG7F8JxAskXqhpeWaX7MTPo9i7F\r\nlEBgzJgy3Y2JzIE79LFa7F/z2hNU5MclhEB4mXNukKdcRwtXogB7Q8n0FqkX\r\nj1GUYAPCMGUqthN5Mo33CvmofCGBfFgVLWnMewnv+T3jIm/Bv8MJ+xQn7SCc\r\nnZrKapcq1fsm2/ZhF7y7hSUm0ckshmoJxQy1tQqh+6+HZm/fwIP03QuixVcL\r\n4zGBxvYfC2akwb+Ve+cE4hg3Hv6Ke198E3JbmmjhQ8VKqpVXr30RnWNKyuom\r\ny70/MYOcb0Kvcs0P2S0/3b4IKh8KLq561iEjGskxZ06vZPn1bUX0GnuFu4bc\r\nhdDHy/NiQKzozufhnAlFmgC4Pdtrd0OB2bazAUDnoObRTxMubTiqOXqhcEyd\r\n3YnLnwr6pYtXwWzYi9b1HTLl21TL2Mkpnf4jxK07xyZEIBmRJCChNq5aUv3z\r\nISst8PZ20nFGavZMPJ2+71i9PkBr+jjIZoQeRvhLORsVoA1jy3yaMu5340SA\r\nTzyQQsSbfi2kZE/F0jTdZPEjwHbeq2RgcTU=\r\n=ST5p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.21": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f1842e1886b84aff534626a6053009d3ae26d320", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.21.tgz", "fileCount": 8, "integrity": "sha512-5x6EoTMVNPMqMToYlPuddVHyrIHysl1ZytwtOW+yCFB5bBOKlUT0Sf8JKx2oIsf0SPKhvTYoeRs0V671Q2CMSg==", "signatures": [{"sig": "MEQCIFiz0g9+EfKuWNjQUliNqyT11SeoBMZa2o2AMvjbxaJyAiBdE7mZPdvAEtHuiQmXCNFJ2QGZZpFNCOMXYjN1b6B29g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmruyg/+Pp2987nAMFb6DNvQN913u8Hfe60TIdwJg5ZxxguvMqaB98jl\r\nC8TW/FeeqWpCrtyiw8rA3zubs/1hqUMkl6gyqHL/TaTxEm+/zkxJVTcNgk94\r\nNUwBi55799pIfpoUqeeuKeH16Wtg5pue4qJqsX93jXJNIvEvHFtVlSPikTQz\r\nSupkee1SSpNlxcntAtqX47zaIXPCf9NQ2aou4UPV/pi4sDKfmMovC6JL+lUK\r\nYAw0fCARB0/ZuY0kkl4h/7PcZCvnUbOvwA0UuT6am35khgp2lAm5480moyGb\r\njvBQefFKhnLPtrzj7dg261GnMeL20UZ5+tJ0udLiIIFQ7feOGCbPBxsam/cK\r\nT48cm/zlgdeg5gt4RedVWfwvzvvg+V3RVRfwbpKc5yyS1pHM6D7b1sxrhfYP\r\nAzxcE8PAIZWrVmsuoknsHmEz6yEemTAZo4jp/e5DMN6ZqSVOkEzyPn7lS5WB\r\nA5PGDK/TCslbBJrxI2CTVFQ2lKZtFC1Orhh4uhT1+TSSBNRHDck79MfnhyBU\r\nsqrygDT+A1MhmvmVoaTXl9K9FgtvyzKiCnuXKZvhpG1JXNVh7LnnrO5GtOFS\r\nakZofyDVH7B06MzyEFYHw0zNQ+GKfBBt/pSWJblH9vpnJInVtZwGt/n7obf/\r\nJ32CcVdsfVcf3A2QQgpgd/ZJ8vxT/NnImd0=\r\n=dsVy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.22": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.22", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b7f0e23a162d40905c6f1f0efedbd4b3f3e7ef08", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.22.tgz", "fileCount": 8, "integrity": "sha512-xQ94tZI1iVT05UreuaTQQ13HSaPAZcazOcRjtsM7qysKT5QgnU2WPym7pJp+m3wcLubzn9bpEA5QFxBXxD+o8g==", "signatures": [{"sig": "MEQCIBNcUiMTFl9YjJP10ASw3UXTvKQA0ZTzmYMSoGzVZjrdAiBnkWSgqeqWnC77Ew6wTdUoji1cY9Jtjwf340BzBOgA2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFGQ/9GYj6Ksvhrazzn5YC8rX1YSi9UxFfaLRrvY2H5Cc/d89u3FPM\r\nQ3xyVI5L1P58hpLRp5tLSuv+mnWVoJF8f/QkZgdWp4lcPqHFrUeax56wUfLf\r\nj0P0QbvVx8V7klnYVQrKkG5Cly37X3cjYLXJfW5iSUlZHhdVQd0bvIycjS0Q\r\n9z4iUkRmSUXrY6wKcEbGW0XaZgTsydDit9N1Hz7aW1yewYvr238WDF0jpKFs\r\ndM8U6Y5+oQx3k/jemwuEjuwXzz+b7a0fOzsKPe9gD1S4UAGt/whzrWh8Ypwx\r\neni2nPSQA4RZ8JiXxqq7lxIFEoIpYV3Ck4QeAGupvM47UFZ9iXtuSnHT+zmR\r\nr4MEWOIcf1gSElblXfR3mWuA1LTEt+m1uGG3kA8mRg9C8qec6Zxy6LWKG9WQ\r\nJ+wYOe4+HrPPJOPAhVYO+8/e78fneobH7tvvUJnWubtxswXjrdyEFA6Db7aM\r\n0GZP169/cRXP7iCtTBLyuYBO/IdtmsqlTZq0rgm9o75PIH2GNZKa2kupXjv7\r\nad/fh/ZXoxIQNoS9ToHjwB1JQOEsqmZbp9obL74nmwE98sWMPv1AUXRm4kdg\r\nzYGSzBnisQOIlauyCfZrJE8gF4d5XeII+qR/B8YxxH6nybMlFhDTCt7lWFuY\r\njVYGKjTqrf4KtM2CnSRh0YU57qr1jrRqsz0=\r\n=dKTO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.23": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.23", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06fd10419bd15a3915f026f26301907fd6ca4694", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.23.tgz", "fileCount": 8, "integrity": "sha512-wzsW75/dperoUvNegnIqSZtlKHACih56KJ173kxRPH2qoad43uxj9fTACEzH3V2JMCkURy8v4d5ZLbtfwaH9UA==", "signatures": [{"sig": "MEQCIB8bz+XBjQeQ9wFtrzj8ykPFs07aLP6Y3sGYFwR/6KBAAiA5ywv1KK/spjcOMjHV/MGMtRAR0cOpS2vR11EQbRltrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpEbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqefhAAocyXSgfoHJG1Zb7BymXrgUDeXfUABYEGKgGigHkQt0g8ovvw\r\nvcjI/rZk1suSAOfcVWXTX4L2hjRePcIxyCJV8Zxf/83s/X6pfPUYGSamZglK\r\nxsRlKv/DjB4N+qYrSyYmtSLfUzeClnxJU7iZ1Hf9qetzOoNxS0cxg/jA220c\r\nhakOM8L8iYRlqlOXDrBNUcp2Gqiqp9gp8oDw6CkgnI6bbIUu2PECnyoV3Q3d\r\nKkzLs/9UDmkqWhLoMN+9ksiMEy4RqdVvO+tE5AN2t8511/MW1GvB7OhAmeGU\r\nYId4rwI/J2EGpKQbmH9unztLPgJhm1M+wYDjcB145LshcLF5dmT6b31svios\r\nZMKT/QEQxrdWWdkDD9n9slK1YHV4Le05E1xxz6uWIactNlZDMQsnT2sHVTAC\r\ng0BdLgplhe8AO9PIvFWA71alEK5Vx5tIPmZaVvBsiKrFJ67TyRBdbEs2dvwx\r\nX9TIb8CkFdczZY87TTUIANvhwv1jXVeGG1CwnOOQd/MByh8GlUH1MGFRkyJg\r\n2hxtziTflLUNQ8IFL2T+6QlQ5e6S2/B5L1qvmAcQfQ3HVO+vImCZ4wpkGmwg\r\njRZHy2R1DtmxVsJnDisdxyt3J3E434aOfiumdEr0mBRrjSK54mdRgkRGsFHD\r\n7vtsAu1O6C3KF4BrtnoWST0kaIv5vD9VoYo=\r\n=8+vW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.24": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.24", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "373444b674998b18b0d4687c59ae0acfa3b47010", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.24.tgz", "fileCount": 8, "integrity": "sha512-TZGIta8ilmlx+fjRerghZI+G3C3pfdYwY61QzjAY/PDm51dB2xfwyZqMrb2WmbRPsnP1EqUEC69Xczikr8VZtg==", "signatures": [{"sig": "MEYCIQCwhr2jnWPWTL3JKyNNrgNl3yARlm2xkClfnfam/kMkeAIhALeX8v56Q0tYRHnK/dcbmbQMZ2rEtLt+EZL/WC12MyE5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF317ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR6A//e2u9RvUvw6HqmjrXxTbI5gsnja439RSKKD1+u6yz8xBWWCtk\r\n5TBNWYEDA1A8Y6Ul3XFcnlg+eF37yPgRvRdgRT2aknUgyuPk0t2bCU6LJ6sp\r\nh0QUPGsklnFrVjCpt+CWAvmwQgAV/hR0aQGHoFoocwwTJ0JitSoD1XvTPOAg\r\n25ioqrPb+BhcNyTbM3uFUj/11fn0oR7868IbqApwOp9vg4lBGCiU4Qt30mCl\r\nbw6nDMJ0poTHF3FH5D3PBbf8SmkG9oR+NPYEi+dyMtUzDNPO4CHNWuA/RTPq\r\ntEBzrqcwtL0rajCpYHu6tyAI0EdhdSY0Nx6mRxLpb1nbOjp+kMVM8QX6PCzJ\r\ng5qBSBJELXpj/Ivte+p+p8cHSgwCQzsyhgPrYxQV4EyVvKSenWhKIvMMfQZG\r\n/ebtAy0GPHTxbvldka16qvyT/P7GoSWH9LpZu6Tzc8DnL25IOmGXjC0mh34N\r\nM6CEv4mO/1Uhh/XYTVU0yurYtAhvCL4PfvALFEr5hiqm1ss8vGNu+U90KHaM\r\nYVp8VEcn5m/XANUDjcStYj64XlEtIn9mF+Uq1fWzM1A7JVhWmzcy97znWthA\r\nchwYo+Ky2P5GUEfMrLUIvCIvgxW35dHq4nEp0SMlYC9IFF09CsaDaCSeiD/N\r\nPsvue++sF3MZfBVLbd9kmcHeZNzIrjl7moA=\r\n=NHOZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.25": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.25", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d8d24a11653564958c012aab148d8c0f9cfcd570", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.25.tgz", "fileCount": 8, "integrity": "sha512-Y51sVnxutSOk8ox8n21cxSwqktd0hdCdDl8EwG9ap+oRSStHW61pv3HoFH4uA3KHiRjOhbqxnp3/F3gTtkGijg==", "signatures": [{"sig": "MEYCIQDxZK97P7eaD7Adji6NUQpbYxCRLWNJjHognA16zSTScAIhAPpVAg0580V+sQ0K70pm5VGzMftodQWFR2jbb4ePy4P9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOzw/9EkDV9ex50MWWTeN0pAi0jDNytuzCr/LKnmc8opQUpyRi5JQJ\r\ngvD5f2fj8aH+Af/j/RQn+vfmTOiZJ5FsoUoZCoRGNcDfeXnmlgF/UXKmrj9f\r\nfBQORALf5TxhDo8CzMiO+8VEvMM5mLKDs5oUqf6SDoX4pqbgwD3dn3mr2JSF\r\nCWvcjifzAkrfvuoSqjRU+pB9A7//Op2pHV+dcU3chenO4TLzfNBZgMutNKB0\r\n0pWFW5Mv3Nads48U5rLiBljRomQvEyHqqobkTq0PNuFAZHi1qOimIlmN9iV1\r\nadqRhS/86JW2Q7/1RKEgY7/XK0kL9jutmpt4CGpQRZgSjMLDUhd63nDTjUdc\r\nxUdC+sWSzT5S76gRNX9ZpnSXIi6huwNurypCb/G8SS/iFQOvTZUdifZ7v56R\r\ndHDkajxZn7Svydy5iCbsXWubbayCddqKumMWNE3VdvRdaYm0mnGhfkj3ppz/\r\nTUmyYertICqtOxHNQTy4Ky/VRWozFwSOmJDvFePEAAfWuJ4yTOvCWLFLefGO\r\nvfdjqEsm6Ne738ASyzPcEj2tfvhE6vu6H7ZrNXIxhRm1fgttjAwJMMkoiTXO\r\n7h0lZPjtocDTBrkk5H2aIXqPZblXQhpOhwWEgLUWGWKDMbmCk2QtfcvtctFP\r\nE4MDy1nPDELTBuoFMsQHAGP2x6QpzgiwMFs=\r\n=62b0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.26": {"name": "@radix-ui/react-use-previous", "version": "0.1.1-rc.26", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "64366a8caf5128a85ffe439b4aa3bbd1b918f3d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1-rc.26.tgz", "fileCount": 8, "integrity": "sha512-kN62XQ6u+WtfuHU3HhXoRhVWYJ8u1SJQSKYSztFPQLbIpZyqNgb1Y4TIMCR7r06lYKGJXmcY+oc7yNQq5jK3hg==", "signatures": [{"sig": "MEYCIQCiM7Z2KwFc88C1nz3av1gkZ8qH6p0kfRtM4dV6OOOC4AIhAOd/61uaPn3PfY+IN+UKPjqisr18EKgJSoqz1TELvQBZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8aNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB8g//VruX9fx6RelUgnmngS9RMa1Bkyj9vXpm28LR9oebOyNvklel\r\nn4BjQDgkqHF9BhULx/K3mS9jyKBMbib3m8mLcSisVsDXyZ354eRFqEXDNGMR\r\nH8HM8lCHNWj2ensXvLYs5JJFNaX99v+H9aFxr8yilakhIGJH99kk0Dv+G8OH\r\nN+5i/Wy+GiCNGqTBJqJScj7rxawJwgGqMhw2HmFeqgOyciKkQOciJq7oyrwX\r\nIu1k1aLFcSEiccZRlrojaUyoX49X8CSX5wLaQcv3jeTflzE0GURixkn7Ugwr\r\nrvOdX5nbGpQV44HXjnYmWu1V3NUJU9TQ1ny0wP1G1uEFnR1o8GiHvHR32yLg\r\nC+ysV3+gSE0ONmWkuUp9eyqv2/qXRXn+9FQ/xJvASFDmUQKgcdZV8miheayS\r\nUwD+iSFF5ezaqk02WbJZ7jy7zEwDL1v8+tKGVhnLkKnuOYJcLIe466fO8pY5\r\n0DuCTyj112YdjEbQUhf7L3QA99DrNHe0WApZtPHFyUdd8vY56HpJ4mYpCC6D\r\nY8EP2T3Me1wBS6aLfxgzJHIy0/SEc/gPrzkpH7OM+7DpiSh3XqtXhqVUs0H4\r\nWxpv/+JBSyj57Tcbma0pxgh+Twl/5v+L87TOra/exWei5oEwRDAab9NgaxmF\r\nIRd/2Hq8TbYylmU9EXuYbk7NFycoCY1Tsfc=\r\n=9Y+c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "@radix-ui/react-use-previous", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0226017f72267200f6e832a7103760e96a6db5d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-O/ZgrDBr11dR8rhO59ED8s5zIXBRFi8MiS+CmFGfi7MJYdLbfqVOmQU90Ghf87aifEgWe6380LA69KBneaShAg==", "signatures": [{"sig": "MEYCIQCRD+XCiJeNfJBhkWjx6SIFaNlxPh+TCAkVYomDdjZQrgIhAPfnK9afoiPufyTsupV0PxS0xZJ5OCoeF477iYuBTug+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8klACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu8Q//e8IPbNfztI8fJqWmdZzdVtxNrlUTINTHVwP3Zz2QbYyWoSTQ\r\nJ/66bwnDxeNepx8diIpgoQVJxHyvFLBrkgYN5zA2dBkAOfmG1k8KkdjAvhRs\r\nVT2fz4PMILpiLAA0uyT19W44ytclNRhaabN//y/V++kAlP2xkFh3n//abQtf\r\n4ejY7lbEAgiWb2X0k8EQxaahHbZ2FvZiXjGHKmwP7TnpD1OORYMHOv5XnYA8\r\nOnQEt7Wup+MQnDqLpBb4s6u7lOoroxjycTGxmZtpdy+Fmwjix2Uz9QGzekGy\r\nu5cXUW/Yc7CwMMHxFqZ3CVm/HS19yEPxe9XGdpRaOOya817KSqhLLQXobzQK\r\nXnD/1SviyGe9biexqDl45k46BU0/UXt89ldXePuDOsCKuR3qurxX+TQZXMZo\r\nmLVB/aeaJEdtfN677EJSt6mOHmT5+bSN423UqVZTnS5/iGOeL7F7/3C2f8Vs\r\ntmwEQahh0GYh7uDJkrCbROP8Go66JHCJ3/sDpMdGVHvMlRXvbIf5qnWtUYEg\r\nLmU4diiwcUlQyJjLQkPsSxW/12O1twz4Lzwp+/Vd1n1Rg1Pnw2nQ2Ltywbow\r\n4/VyiqlTl6iEDPDehy2FrFlQPMRJBoUxFj005mUxGnX+WlX48lpWd8R+kBty\r\nohG1LWJHVRgT9Ehf1+/+c62WE+GuqQAXpaM=\r\n=U0uA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.1": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90a9ab80e593f5a542a4161609f6e94e33ca6fd3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-EFEzvUKxOUjZFqMTgaXQbaaJF6bNnW7zGxvGGoM6SwnplywK5D0A40fFgIbdKZmb8FAtSLYhjIQUDY0cZkXjhA==", "signatures": [{"sig": "MEYCIQC6AgR3Glk418Qlyst/KsMdCzleDc5o5NT0PP9PuLZ6PAIhAPQKcD8XJCeXIkb+KSJ7iihqlSEU76+SyGi3EG6EY4Hl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWASTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrxww//X7a8nj2a1VDRlXH/lUI+eCKH71MKJmxRCpjj3XnoqvmRpjrw\r\nfPQlKRHPp7g1+A9hoUPKElBosgFCfyTzNx33oJn+yJgGWLZFdaKx5OKygTld\r\nNLqZ95C1iAdPALTxbIhyQ1UzMQrt6ySOBsPPPddQjjPjAAxNKoTWh8L/e3hz\r\nO/UeGAva3MKysz+SRUfJAUc83vQcLMnzD6bK7zd/JMurh1jGwnjuKy7du/cs\r\nUj0gqVbKUd9vKN4SS7zGhvqzmEudKSvbCaVdufEKMy0KNAykW5kniBXcd87P\r\nF802qX3qQQsqNuKbJsjCRDFnCIru6UdrNwyzu3mxjoHpOM3lgyA/GPHNSbCf\r\n7upNojaCL1YMPxlfOhK2Sj9PYOYoXv2coPJyf4rY1Sqo1+U0C/7l8AQst6nE\r\nGOqrwSiX0GWsuCBWcDwYllUtL+P2SdX9Hd1SZ1EtzOmYGeuF39/4kDkkjtjm\r\nAHF/Lj4raUnO0gn2MC33PueKV9rd+R2iofvWV0duMbakEnH7OwGQCjyiyr2h\r\nSQCuI1dlYJYkh+BqYjCuHoVjT8gk2huzGxULL7bd/J+3OXIR0vQOD8jnJZtV\r\nvCX6fCbWSDYFo9nNx8pMuD64rXTXM2IIp3K7SG9OrgdB83r1VB6UCwf7fRd7\r\nWVm8tca9h6hbGGZ6vskgm5w4jt+3TqTJTrE=\r\n=+lzO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "779d01c56b1ba173f5344e0c29a25254eafb1c47", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ybQT7wEyL7XSOlaD1XXrdrj+jFubNYPvwKEpwTu8QGXuUyoOAFVUiMK61Fsis0KiwxO0XNnhqDnI/sDLScD05g==", "signatures": [{"sig": "MEUCIErdMbVbb3NRiLLegTMc91LWo0puhnAXuo63n4qVGf6vAiEA4lco3Hv10dGGruKd1OeWa9ihCc5PwgrMwAnyUBVDhto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSYBAAoDh0QZmy5hmsNSc4f5WhVprwqXm6mCx0DnW7YE3zdSkcl5G3\r\n8ohDdpUyoLtDJkXcqLYhYJJ04EKrxZOHPC+hySW2riUz7Bk+FOsHi5D5sFxa\r\nE73aHcBsKnBW5GBl2kBEVcS1rOtqXq2oVdhhu2qSJmgXQ+UHbnqTzY4snRrL\r\n7ZQOaMpr9SHPTMQrES21260sU7t60SOzRLjFgaybKIHVTCi0pMPZcUjEjXpA\r\n3xmr/zLP45kJ/KhuL4QGzPvkSAWpS0dcRd3lsaIUblAc9Pb5D9I15F2X/m6W\r\nlasz9g9YEpVuOe4+D0cMGURPHl9JPLRyXt2mvQZPFNWFieKrMN/ynKRmQ2KI\r\nb242/reBX9vo8eKiMeEz1BpkR1TImvJNgOOp6B3G0qpB4vnGbbaf8lkgeBdf\r\nL1FFQLu7DR4css2CmH1XNK/8fcwiqWSJ3lzOn8p/zTC2p200hAhKmzWN5flB\r\nv3Aj6ckFVBlFYu7D1ZhhWiutCTar4yKl69A3ACopb8yF7XTjNCpXwgZ34bKq\r\nfRkdVwLq5W5M8/CuL0rbnNaNBE6AZ8SjMK4XHEbQovATPYU+HmypdBriWG1x\r\nKOeWSidSe8h6GvETnyRh9zf0ra92QIZcb906tzLGZpcEgDu4XqxrLShsTFej\r\ntGYpoPyybL4lHIa41zbkf1O4TmkjRvzA2zU=\r\n=HjwI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e4ab0617e61864712cfa7a8fb2724d9e76a3212", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-H/VKgBIlqmSwhSYYUu4/0H7/YWOOjQiWaDFhSDSd7RFvYmWAKKQxFF6+W3A0Y7qMr2qjgD+P9GDwEdQjQU2bxw==", "signatures": [{"sig": "MEYCIQDho01+Q5J6RxjL0SgAw/st1Ew46WmKta1niwIgAayQDAIhAJlMfXzL0Sr5fFqQU+pZ/GLaCqxwImHZp1in81Kx6YcS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpb4g//XfVh6Cb0Zlza4s1oa0ykNgvuXE8hDAWHJbOmJfeKp5jOjNd8\r\nefyw2kyQrciFay22ANzp62z8NyWmUP0ooeZOiD99E8ZGLEVcjls0miT/i6VY\r\n/fYx3OAGP0+KSnBiJp/9awzw52CpKtxz08px6kzpJYRzrYlSGniDEdWlD+RT\r\nRnKkgDN8i+mIl+GCVDfE4NJnPLMbantIFuX8x963LyEJ3onVAf4o6Ne20ekz\r\no4p8+bNuqXg7V69nPIY5ZWZuaL+dauB951PfNFyZ8Wr6xpPoQcSMPJ7Ecr4g\r\n6hyw2xd5RdltuHViX05Ll6QSTSaPj+m3UWZBiMy7wHJBOaRUoan3p5dlrJEe\r\n5iuqqN61OBn/8X1PupP3WqcSPpfCS8qmGcYefvSIcB43QUD4C7LVsk4RMVgF\r\n+FNAILnJghQP7BojhxDni86A2RqRbYVk8dyH/67uq4OyoLoWkME4pVV7x/O8\r\nTuTSNHDFJrPEUHfdMdvNxFTPPZu3nMUoDF+L7l4zORfcPJXrt8E24YXuC6jy\r\nsToXCHUUWcIbbkD1ZP98M61BbtCXAIdQWne4adOqwSey8Q0+lCHPanobRaId\r\n+QgyE9ycHfSe2lTP7q7jdvoI6sB/ffSCjfRiCnZ4OCE+Bs5+zaEnCKkT2d+T\r\nieVH37kzhlkllG90RVQ7/QtfruNXC0Y0VYI=\r\n=ci0z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "46bf403e71a89d4bd04443293103ce86298118cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-20p+4PfqCMG0D2XCX7uNJDGfEMu4J3qSl++WpNSrLYTOrcRpCP4VoSSMh8yyPS+5n28Qwraa0YhJm0FZs53Fug==", "signatures": [{"sig": "MEUCICsmtgaUWec6BdBaGVVXoTdnxQj72XDuGBw7tE1d3WpbAiEAz8UrTA41yW59y8q87zfJM+il/ndmTbgRp+19AXINk8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3Kw/+Ovdms/Mi2NJh1/DjXvyQn8DQvOxl6MoqQlPOTjiih9ETlPE4\r\njXMpDvDBrc3eNimopi77Wpf3N1Ni5I+pznmxjS970xnWv1a+O3D9/eVknLIC\r\nvxR5WF6weu20yS372Q3wtyrByOrD+xqLF7gsrdR64vOvD0DuZblksVhe69Kr\r\n567zyg48vgtOz3heVR0dqfxdDPoTfcC+SOMybmfL+bKkTs9aoAsFeTwI7OPb\r\nDpLgtpMp1Npxr2nQMCKCjNUMYPX8USJTTPDDKN9XcJr+gpMRRiZYgvTM+7BU\r\n+VIkIfl6bd+C5TZG90x5IWyFW/tuzrOLkOjTPFInOhU7pfXPDSc1ORsqDQA9\r\nHAh/YCMMkjLfnXVWsKOzrOuEhO6gollFUmE101MAeHBjE3seN0Tsl1fzt51a\r\nNP4FuXKl8R5m46dVzoUmuKZj/zPVaUi1HG6aQ5rS6yM/WVA9zPSzwfXsI49D\r\nB5U3rWcEdGdU5ohkwNiKie6LY0tIh2tCBttXFO83Nhj2QDpOsJpKQwJSfUWb\r\nGCBGmYXMof3+PBOS6MdhLOJtEJO0Ayhq5mrZVc2Rhd0W07VtSB0vBxAJMNUG\r\nHyBLXyM4ZfuYH+usjHj2WAu4ZcKHezWH/+m7w90sA2RaoYSAy+PsDy887shg\r\nwmg+NL2Ia3BNoAiynUdH0TsL7UIIeYQc1g8=\r\n=jIWs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c1225210edc31bd4ba9736f7b9588983653b9208", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-sZwZaOWNwL9pU3nKTve8kmajwBrgfxx/sdYG9RXFFjxMxUjWEUiRazY2zQEPD/QKOOOG8hOBHpLalmTK47a84A==", "signatures": [{"sig": "MEQCID3g3OHAVe07UC9liLPPtIWRSBP1S/YfXdpfgKREH2TVAiB60uQCMCxTC/0xjbWNGQWXt+al290RHDpi/6dVTIUmmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJ2g/+N0NCAeHFfFbn/MuOOk80xgQ6qp+EkbWxHszhgQs1bTozim2I\r\nY6LeBMQf7znmGX5dshVej50EQz104qOWNuB/KedajGseLnZvcf6BT3cBGQ/m\r\nErckk1dJUvavtOYzgKG6wmgzl8rQZkPvhaOD3lPO8JCVcq9XCEQL7cOy98ix\r\nppmuP/c7xrtjBSIyGj1VzT785Rpyx7ILZArXUgWWc9Y/PKVnFEgAg2EBrsXm\r\nbKsjvYXw+t5ANM+D/H6uTPMu/BV59txcHbXi/Hz+7XMe0ZRymOBQiKR38Iif\r\n4PEAjZCESdGrC2tIuGo0pBUAQw77xEytBL22K/iXfHa9Q4qDEH+3w3ht3ci+\r\n5ix02b9b7kpw3tKRCcf0Z7vCG1S8OtQMrjYUNiz6gA+RLu98OnPl1FMwHsYq\r\nDBSRE/gvBdcmCd5iIyo/XSYEmM9JA/9X1fVqXEVktJlklwC2XWAQTYCFH528\r\nMUArAa767qwUsa3adIUSWbT1tEA1SQRVRZe2XrJU/JHXOLP3Q+7yDG4EHzdL\r\nUko7/WNTAT35X34LLqXGpK6DOXH/jFDIaxRMa+vSYwcWq2yIVlkrbD/CGLMf\r\n5Sw1zIKrYYA18T5N8zvlEpWiVA4Aj4cIFoaKbDJV6WunVnKaFaYNckSwLQ3Z\r\ngEzN2Rn9toB2BAa+Xu22obSaG86eW3pmivo=\r\n=HsEj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dd2d99d423dd54232451646595fb7c4d97c39499", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-qiBfEtzlIduOleT9vOpXFUe9vBLLPmtEjBHgx7mvTmY9W/9mBkgMdrGrDcAXoqF/gcn9/ANW36XWsyzuB3q9RQ==", "signatures": [{"sig": "MEQCIHgfGhURzYpKXHzc77MYeHVS7o88Ze/JICHTmnZQkyO4AiB/HBO3I/dDEMXa/kxIQ/lS2X+mrXD02uULB4ytrjAUrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyzA//ZrEsFj9rGKA6wGaT+HXsrqmKh46nxlRiuBzGU/wL0g17r7kA\r\nfnbuNhBF+qz4GhaYC3XC8MK1imSkQTTdBUGoTxHrZ5fDgRlVvIdfnVP1xU/k\r\n6Qx2uj2pivfSNmyt/6ghrpgpwGeTab6txNXC4flxum0liI1xth4WYfkE+wHd\r\ny2pnc1b2FoycoGYZ8HyHInePDxqHovNIHVpFPmikXgBla1Fj0B3JUNxr5TXP\r\nguQuKNfA6ZyXS0WjLEDCKH12KAs02iVpVXNSUKlViru0LLy31ogqWUtqxgMj\r\nM6iZvF/PkYeec3Uy5aCIQem9rwCLut3XWdb4RXAbSgQHn4qCrpwuOrvti1VJ\r\nEpNbAaXtsKtocoeU2mi76x0fnZN1xUJsl5K1CML9NO0ypmxZfKRsyi2k4OSR\r\nEFOi/U06u9/jJEost/TZLIhyRr8TEuL4w56IPUsBWizeAQk8XTR9mM+hH437\r\ngMcXGyVmpdF4Qm8fCkAuo5deLgXY5MbuWE1PdpycdEPGN0L2cnhAmrukUyLU\r\nniMywvV1fu1h+Qf73jVUL2bA+jDgn1b+8MEax5ujNRegB8RRvR4rLRZVTsl8\r\nbII0RjMtjBpxNMfjYShecJTWdGh8crU5lh90dTzzTXuQ0e+YL0ONPBA188I5\r\n1ZDckgoW5HCyJ7NM5oYyQm4aVB/EdWY5cuc=\r\n=EaJ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "942c9ba01a538d0f9703891201972be5018ec90d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-1FTtn+BYFq6gk2XEPAHNx3mz0qnbRiRljyjCUmZmDfEeR1hyWF56YgRj3shYpMrJ72BhNgXNHcCLtg1O2XocXg==", "signatures": [{"sig": "MEQCIHdxoa2MjUXz4c+6QnY+bHcEnEGtalzJuORYkdlolyfxAiAKv2QEGSQ+Iv8SonkD39WHuBToAnt/aBNwXWWWuAmNhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzFw/+IXYRwLmeU1ub4tSe6wFfmGpdE4E3oy4MGOB1oKDgYKvohhZf\r\no673tGviKmakJ0lzToJZhMWExB9qDChtvvcNMfSNFsUtjXeHNbFYORXd9RE4\r\nUeQ+/sMLUygcYrFFwbBkzTTq7n/1fwAm2nvHwF3YDpTdVUCX8k96uS67kWza\r\nvWeUcl/Bo/KcMiBXAiB8zJGxxRl1rm1hSAf3E8oRI+f2piD+QjuI1SAzFlPD\r\nGmbzoondHSTEttlDbjOOpwc37qc8j2nAMV9UBZaYpVVX7sxENMoaVC69g4hb\r\nRQcyHhbxJd1qEk+jlNTaAjLgE/ktukMmuIsQqDV+D+3sfDAiw+9Q8X1wobzp\r\n/eI8Nf+r0WJuiXfFggsv9rq07AB4EDJDrLNAZkgWTX4X5Xs73XziWvM6riPf\r\n+gunaFqSIN+mmRlSbwDmNFvUeNasmq1RmJ7FMqhCadoC980CJE95WGWp3y6k\r\n62zSweDmJlQP7oEeN2Q9ta2WMj52n2s6SSi2fVZ9yEhamfJOgCJ2Z7WWRz01\r\n4TwGG6JEV6XuKyg51HLXgAmlnc0+SPFIKzwb2s/hWAAjmco100Y10Z3yNBqB\r\noJUscMqHH5DY7dFsEEVbk24coU3XjnaaJT6lGPILxBt6hlM5+1X6Kv+Moor4\r\ntSaULBqM5c4eK0iP4SBCgmjsIZ6DVqW9pNA=\r\n=K9ps\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5619037a591eb2e96d409bdf2f09f74b3f5cb58c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-0jBCgxbZO/ylYoD9/jr2m3DOymrUJcJS5Xqhvn7vC2+766VFqhduiy+TWizuI/kIV29RCiwSUQmX6z8h2mz4Xg==", "signatures": [{"sig": "MEQCIBcp+r8eoAFTgDklAUBZdzq9097EgxGiWqFjTsbmJY9tAiBYkc73Q8MietIG8sypUfyyv0nQMXcJNBbsFxUHbgEG7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVi2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2SRAAgd8HhDoeGn2usOlrVQiByhZN8OCZs3qA/hz0oHoFhBfcO9c9\r\nvJt3nOBVyyruFPl48iBmP1RXGZ90UBqjcKNbeigS5ijbcaBhaG5awpCaiflc\r\nkCN1Tu4t12g/ilK2Cq3ywI8sx0/ouS6N2FLVfRJ+e+nZNfHryZSoof6G07Qv\r\nWbzgkVEZGhVIQ/CrKNJ3HKwMT0AO37N2K+rUa7g/0gkC6zTLmkkSt1xsyYgo\r\nxT6VTBdA2rcCq+23dqUeEc5CQnDHo2UhhPspcgh6b70zFmtjMBgPVS3ew4QH\r\nZp4Avk9RJvWTKxTJ4B6syyA0g1RsJynQxcSi2r5YXPFXhfLqfaNJ5DuMVAFU\r\ndxPOFdisn30odgxW+ycDxyc3UbxzbAB3qy6pe96aQ3uNxfGrqtnRPzPtuFlx\r\nEsUyF8Q9jTiJkS0myrIxD+6R9HYNjygLeCVhCZfBUm+0wzTbjjpVoUFIHXg2\r\nUTgC4TLHQt58IxvyMBEYt0q066v6Lfp1ES9fvVVj+zmuDZ5BuOohD2DJkOdA\r\ni+Hct0OyG6zhLDYNgTVVJzMHmqkn+B8qyhP8PWjhrEf3IAuSGfC/bCTDnEG8\r\n81yLODZMX91pYn/twIyviRwu+F9ODKsnFqaRrN+MceXJLCSkCIyevZMgH7NR\r\nV9GOnh5JiWI3YKMvofOBeHYDs8JcsJbWXUg=\r\n=5LVv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b912dfd426ef77bfbcfe5e0d215e635a87641f46", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-4gMNoQZAKoGrY1+Kd4bO/3Z9KD4lDsss4VZ8d8VQksNIAdn3j36yNUBF3o+pqtjyQ1//7kvHLMx44A8gwTeRQg==", "signatures": [{"sig": "MEQCIFFU2RSp08hUhEAMyaQyA0E/e607jT8AG2QexLawYzqNAiBD1+CWZE2nH4zPopijmc6T+i6InSF+MjMWPHB98rnc9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNinACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYWxAApN4EI3HtmKsid2Z27+Wn3sumyQQqjqS15ZzYmi1mLCaIC7wy\r\ninptx4NFDTw0s7YtzrmA10fDJJjqkQOAHN8r8/gw+6WmMsa2/NiKcEBwbypl\r\n4w0O2M8OygALTHVCABwn2kkywTcnUYu5pSMRNW0ri1AwaGgx//0Zgdu1znFA\r\nG6pn5vp7PeFlB7QCWCg0Rg8PCiK+Q4wRH1fTBIOcyvw2SXGZPFok0H3IsZpv\r\nc3i9dhYFHhMM+3JOxsOncJZznZH75XZpdNbyoKSPw3ymE/k+NTPSO3dtIIWf\r\nL5MrcTMxqXB8bK/gEvxkffciPBUnEXMm4se3p+AnuicMRQ+8ykhf5SGPepnA\r\nJpoNGp8xS8WKr1p3J+Ff2lN4uviyDtAcq7b/SwxECroIqO9MfDJNG+DXvv+4\r\nf+KEM9SK0wA6MPlnyBWeHfLEQogwc+kkb9sK52ueiH2VZSRdDR21fYBWYXOO\r\nlycT2B9wkSh3d0b+2k3bzYEpPGEJE5lHStl/vLBz1bmxGoT/QBkJY3M676NA\r\nDrJBxBY+mfAJmC0QMgjdkG6osFeBxi68QwhUfgbYO7/Skc1treWq0nfQtXuu\r\n4ZPHjmF+LjA18oxd6kWzAqs6tzAphfijN24/vywWRHIi6f03e4WgZWqvGgLx\r\nat14NV5qoy80ZcqSQzOq9AtQszKYFLMkqOE=\r\n=0hSo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b2f9f2ccaa3290fe53664fff9fea24b079699f5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-GqQbnzmcexhsC+ouxEBp33Hl9EjcD2MjGOdq2uicfrnVFvSTZUtgiq7cNwsKfkvDljC7cag17cBXt/aHuI326g==", "signatures": [{"sig": "MEUCIAmY6NOvAyXP/hTT7HWtwAZQNHaDTvRiQtEr7B6l14JYAiEAzNqzTMC7mbC6IrwkQR615iR/IU7vgCO20NGJGv0n72k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYRA//d4rAm0O8Sb9r4PblFUP3V9mZO1yovDxjluh3bn3YjpQRFt7f\r\nHumUmWVJoY5KKkx4MpYAIg1LqxrYYJBEIuJLjLuGD+JgrLM0x3A1KglZsm10\r\nhfSukJ7r8SdAGV3uy8PfK9W7UMagIan2oxPqjHqBR/EaOvGMd3e7ZzUgEAVd\r\n5/nfHEt3fI2igKjI9cIbmplj7O1RObz+sMCpdGZkA1KblvP2AVTGwwuT7Pqp\r\nC5h8mlqzLM+T3rBCFnI+wcK5rnfba56YoawwEHEeVIIyovVfsyCBX60bqUYz\r\n7o04ZOyLPyfQLAgOSawkOSVhr39HaIT9wC6cTeSCG3cx3qVzOy8zaQXld4Gh\r\nD+lfQgTCCcDKWYgUFOO7CAXpQ/mMYpzly6/JgEsE5G9DjISq2FhTF/rXz3zd\r\nuQp/TmH9fK0pUl/NHS9mvr/cn57FYo58fYJ1sKhZ1M5/KlkoYMSLJ9kYN9HS\r\n0P5TJwTZgarWC6s6RgXNQluC2og5ExYE09dfPFMSP4AtjTHH8e+B+AhFd/u/\r\nsywWQqJ4UgKe3IDSVcafnQn+w14TQBgluN/7O++B7p05S4XxzPyTqLn5DgZ7\r\n/ilA+BvaJcCVd1aqx7alKdMmlrY/Dl/FYuTerziAPEIk/LZcyAuRT1ssroKO\r\n3a9G+tOmsL2LDNhVgKMoxxzFwbiQ/AydkpM=\r\n=rvr5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90e31b74d8631018a79e14799139b73ade4818d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-d1GcjrtOfAbNXcg9OKTU/axUzTRrnXf//cqkyTTd1YDUoi9/3IKlaLUOLYMviw+s/5fRMWc2i2zBVrlhOskfbQ==", "signatures": [{"sig": "MEUCIDzfd0ACQ2PNfmrn+jxKX/b5BzTSsZPXhf2O+E229qlFAiEAg1dTXYflNCbJdzOLSLzAjLxZnmizPfKetvFEwxe8TaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSmCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofBA//T3QlK6N2LrZ/F6zsxzM06SGJu+pLPt+6RuDOt9OSItiq4tuE\r\nLZ5Zx3lFxXIf2+VwDe4TFUvStDCOmdekukHkaidncOaLoDxbZ+CGQDwIeILu\r\npmAk2HzrYjvdrWa+29KneKchPK00oCOokvFHgsZTKVpkM1sSlakFBqceffxW\r\ngBQepbYQFC6r/jUr+SXltlOQTj1B5aHDlKwJPevrhYTOLr7AW8Isx2XB5MUl\r\n6Ml+GS+O5q79Er/E6Li7j/AGa3c1cnjpdAJX1il+QRH/ElHGKVV968FGDvyC\r\nYYVIw7tsjCbZH7rt7i2UAUGUb/b5Yhx0+rPN2nbdUefbm9Nc8MV+VdWEgkFE\r\n6dEVOS87BRFGz7sjQosFhRsxdoCyPhOCnpoeD7iwoeR+P5Ez0ZX7zsF6Ajkf\r\nO6lCffvgbFk/p41cRsQ1GslMgLkA9gWdTLu7jMyrH0jyb5WOJX2FJhglH9zR\r\njxycBgVmzPM4pwzAo/81+EqN3dgh+8zJTiUWhWWV4tri9Q+kgaJqkgXTzlnC\r\nk/dPAV9EHcliwQ7jbRCdRYbJQE3cRSxkvEq+BNlCU1/6ZgH9i/tGvBW+Thil\r\n1t/o8pNRqMLmWPJSpRz1E9LU1KfXR7hn57ua53+NKmODD/GHEzl2/TDiv4vr\r\ndeZM3jonV96F3ydZ4HMyFtPeM/7WxEElWv8=\r\n=2BWv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e216988073c8e38142a675055a72743e4027abff", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-GE/cACArBJnY7qOxa1x1B/9yjzUi8COPudDM5Tqn3HDVO+2X7vB+NxJIafAlaVt0RuS5foGbrgWUZU2kl5vB2g==", "signatures": [{"sig": "MEUCIAyRzPVXMu8o1J0BcJ5gKmcmSyfk1UNbH2x0G9b/xkmCAiEAt6zhnPwKxlpwBajll20Z66BpDfODaVuQRjFqtsytucM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYsg/9GwnUbUHiCuqLqvqwYnj1pCJjSbOjXVvxg4H5+B9bRljKDCOn\r\n6/wb99GwLp9Z6TpfVG7Nu7jm6cxhmZcu6FB+Nc0ZxWb3/RpN7BOkZGmbiDJL\r\nCaL08GtticGq05N5NFiDRTUjZl9dx5HAjXltHquDEi07XhXupRLMga1hzqe2\r\nl/PSogzSU7qMonTxRbZoZirIc2FTK/seAcgsOXa/auxQ7ZLiNrncxiagSnKr\r\nbcckQJtuJd9F2cmQlQNDsOfaOP2YuTvugvJ54ACoItH9UOTLZfJD+MKfTAkJ\r\nom0fSbueKBxKr1s3mez4X/vmxEkJKN51qRxo/Ril0dZYlMkHexw7tHjFoaWn\r\np+PMFNKB1qjqPHn9jmfRywuwQoc6fFFHiJAE5pTos3eKnvtTi6npzen0X8qF\r\nY8XEGnDXf4V8JoP8zh8QHxG3bPZzqsOM1+S43YujEvgCQidFVeUywbgl34Pr\r\nWDjIixD00K9+loP8wfJ4+lRS6NBQ4T0EuQ/aBT/zkQZGyXSWig1KqpLmfq1V\r\ncVRor2f5o4JcRRIV0KuHs0W2FoHiHZrs4FBKkqX94nLdGtPoPDywNSNXdxUv\r\nGoA0XL2eCbFhLtYpb9sLDHiHLt5TJmZrbDQDDkJv0r37SWRAl3d9u/kQ9VuU\r\nc7Ie98ZDsmQX1OmpJVsuKbbCJQFm4dzG/Eg=\r\n=oYvb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1471a9d914ee7e48897e29b1861baf09fc797885", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-XVccxAFJo+VppHYvhM84KZcKpyLCZduAJ/EE26w0B2d4hKgbaqOjs9Dl75y8JWsRID7WFj44eUFmYEIeWOlPkQ==", "signatures": [{"sig": "MEQCIDauCSMUIcE3pTTpWoMOP1LtIopbJnZymGE5EKHJG21qAiA+gUeH9dcMRNzBKJPnkyVYAVh7r4RJzEURtSdMNalT8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepKWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZMw/+Mfc9qkOKBLF8QVJgMWRcbmWeVpdxRjxqpmEQ+xOjg0+0yMlg\r\nr6ks32VN0gfa9zL2r7JyHmwJHxU4FsmPpKLMVelykBvZAivS2LDnbDYwDhUY\r\nPj3kgR6DeLtqmAQ/ezxWjPOWqzljDJH9NZV7y3zELF9bzKEpmIEbFdKNIkb4\r\nIOboxMT6RzosQMWQh/Yu8Zqq1G2L7Y8784eQqHLz+iTeGDKF/Gq1xBgu9lUb\r\ns/XLJ6gcfWUNartCzu3aelet0u4bzAm8JIN9J8qr+aJULawDQc/jAtRC0/1R\r\n2Es7ioYOWcwTQFQi/obMUUZIXejReJ/ZIPleFHnSj8KomZvEL5x5rOMZ7cXP\r\nb0cO6s+nSRzO/l5fvVemfHc361Bf4wh5W1eYeffFLjye8faLbKDjtg7Tr6/0\r\n0IVPnep5XVH+5Yw5AxhqSbpsDS2iuVeW0i9PfgzeDg3KCTeQ0elxzDMAXhvR\r\nlpgwNPDKd0jA9lYyCfYUTj2RDZKBNVhepsYG1FdcSv6JqVKVaxk5UqpVs5Tn\r\nWWm359DbB6xwSUEX8ezSFBH2S8Vg/N2KtUAV0DyAagKfttRjiQ8FmqB/kU9j\r\n2wSV4127IwAvkG8xGE3OT7z4Ry/pBeAcr0Av/Gs9xMtQ5lTs6r4ICzVP4O2A\r\nD0YlbmlxeDTFN33qd3OSLbRiHj1FPs+OW1Q=\r\n=nbGD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc1a8d7be0a5d5410632f7e426c31067e03915f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ClNdJwbSby7EfCbJcnl6IM68F6xlrJjDfXyZvKhkQG2FvwYFE7mOe70n/b/YpafRvb+6hnGlcOkUQuTfGvETsw==", "signatures": [{"sig": "MEUCIQCarW8gFQdH6698WwOtXvDAyTZmV3iRZ6BDcV4tSMxM4gIgKhNy2kDGkjvuMxCR/mE1ELQPmN83RfZIg/N4rRR74bs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFJg/9GR1JgewOHZIZOnyf5557OXd6L+Nw5+VEnPMuArHsPMypHP14\r\nCnfIz8cpAQSLS+jVNWAgxwYBqj+F5aUbfpc5lwca6A899SBKb8LrxJOqUUdG\r\n83uQRXXTBkxpgB/BIdYkN06r7HSACrZXKYE/FB/fnX7m8ryBU4XE4Jl6nkX1\r\n6U8PzYYEd4uhMjOWk5z9UBG37rjDopNunMfxXIKa0DYCBZZ/LlyFHfhn6vJ2\r\nz//gcaz0u7+VYPs52w8r5Dl9OQqPjaVVCO377Mm7wpOruCh85Oqfu9mwPXE1\r\ni9U6Dnqdm/fieKdm4P5/f9IWHaYr//zUQZwbdzvzccX7ZdTbyBBi5VIG1XV4\r\nf/yDytIDhx8TDv/WW4UgWo2GxwvgTe1M5moR9kunFJmT3lKuOmPhh/DhKEas\r\n5M+iN4oY/8tbKrxp0WMR5Vj7lCQMN5mbdfE05UWdVOM2nkNyDN3y3MzHcXGv\r\n1DjjnsJkdqPdvRz63Mte/BScY6GE+FeQOcOXCqy9paFU8B5KrlKUd2Pp8XVS\r\nkh6z7E7oxkPGd4KXO2H8UM5V0rezYRWeMDp9/8aAObPmqQ9gOFOfw+1icoMG\r\nSApejL2RyFDM2GcCnQpL53D/37GzCKOreAH08P9rsvw+dCblNKAqnHqnrOZW\r\nbLolKu16UPe45oxyHK/iILoQbpexeu3RALU=\r\n=EjYD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a1565555d383302c602c6f38857a65e5789854b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-8XrzQVQ7W3jcIB3WRu5aV6RHMjRz74o6PiVy3O1EPRtrKPtts6Z8byZF+79rgmWNF/kjMac1/i+tqEyqvMrHhA==", "signatures": [{"sig": "MEUCIQC8TeFzmoJyYL0WbnDq8jCwngtY7b9hSbVBL7k6DJELtwIgD0CbkZKdC4rVtgI+Suie+CCbHn0J2q7X8Nis93B31Ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA1KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsmBAAj2LhRZtQ0ixJD42ceojwLi2oYIqcLYY9Nd4df7VJxcx7D4hL\r\nhIT4Y/cbuaJGHAsxat62neiq0YxFZNcswoLEMghxw5v0OS8Ur67DMXI12NF5\r\nzx/oPk1g/aIibWbFshcdAApYeVjsH363XGgOo3d6bPEjg37DNGgYmmuJoja8\r\nUkPx/dLX3Hxj3IhIDsjyf0mnHzBBWAZdRK4xh9TlC+cyKwm6XhtI3t08DhLs\r\nmLnyNvw58q00Yin7+fD4k1sguQToyTiyuf0olMOC3w5x+U7h456aAchl6lO0\r\n7jUVPEv8UcD85tAzaYZ2uvnDxF5XAsnzhkMVT4FgPA6q5A0iOr4CSc3qmcXK\r\n5DpFMJUb/JtuEp6fPCHuCI6vvNpLSzdkD8vHmPZAwZxFMnSaW7f09RX+amd0\r\niTm2yeDDZ5W6dKg9qLZ/e1OYFZx5bEwakNmSncZ3v6VNgW3Lk/fb7DE6TS0i\r\nOKkE2Zh+sw+lFYpAHb2unkr9ef0to//60T5UQdJG7L3gFqj9No+CNgA3KUNv\r\nT7EZIUIvVtchLiO0HZMiwrAevx52+lxlbo8J3jAv9q8D7DlrTc/lbrDVTab+\r\nL0rysAjDH62bUAyoVF3c0LPV3gfJVBKAn4G3pc7J5PogSUpTSYNQjxrVAjhK\r\ndU1GYi8H9A9SS7Ofs0ZbE4SNKZggynIdnZM=\r\n=+zuK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e8366a4c680b6c43d43047bc83e119bad619edd9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-h2IT9s6wW/WKMOmVq1/p8rBUcACinlOHoAIA0kMCnVAX/LQYLOQSX57n/nBhHXdGA6A58A9ABSUa82t7NFiq9A==", "signatures": [{"sig": "MEQCIE7G6VXVTm8UKo6cj5l+6Ilk0bewVeS8UTklyTpdZW3pAiAhq1D0SABDms+Jto9bj1/K4YrQF2YqRydGJYwzxQyS7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwTw//UZBsHUsCU6x2tpQz5kiLNiHU9j7k4V1arIoa8xlKF+/RGS/v\r\nz0yX41JOLaNUbwOaTou0jmlu8R5SF/uUc5Ah5R+et53f6JZw0aX1MgczLGgS\r\nTYshdl8Oiz4r9Q22oNlyHoxbxwODxx45btkxXTVCyxJn0VBJhnVv+J2iGO4g\r\nUJIdDtVEycu6qsl9JrWfHKY6MZeDx4nzBAmIEiCruMaAn3mGuQSCUQmOHyUO\r\nPMDQdb5RM9IaXTzvhQQrMjYsLPug54TD9nGq4mSnFVHp9AdzN4wPYFjRoIUb\r\nnzBEfy5hKUpMFb0Qnz8Amyq9zf02VeNr8qrH/IUnAUNDbcPv3JZ9hwxyp3wc\r\ni8UM0TSzx4SsaIQ3rLH3qjsG+kIxP04/ekBd9hfSVcydH47DYU8bNO4EaXgI\r\nLiV1H51CzGDFLBZSUJcGxQd/fTvbiOP4h0OLnmEWI/Dev+mlH2fgsiZntWo8\r\nojbNPRidtvVRh+5suzL+mfYbLW4qIfxAm3uxkmrqCfri+8a8X6a1z7I82JAv\r\nOICdYzntzuEysPX9oLbC0hii1xNDsPnlSF0AL2TuCyYa6I/CTSYhrraxhWjS\r\nztkg/7ytpSVJxCmmqM6JjdyPcWpeaJkAI9nlPsQH8xxSwwY8ZGArYzocAfTR\r\nR5srhgo+3/hRCc9TgNWMVX365v9Pbe6p24w=\r\n=D7DU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3fcb4a27d75bb60f0778de1996dcc0a3f8ddbb74", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-sq6hnJ6a9nUg5Nb5DveR6JxyYNcdbMCoKwtsJ3vJI2HRCoju+AgdnaSYiM/yOM2a0Q5FZEe2z0TBq/FnFGzNDg==", "signatures": [{"sig": "MEQCIDi2oZ99hV5yg17Tyx4ioLBr/glEiQwb7TKwhGNsRF/3AiAYh1xkD54IapzTP0Qux+4u+0D1k2yoQE24IS1lkXksdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcRQ/+ORxlMZV+rzMbu2+Yv/NDYY9qRJGgDkIfBr+wQdJc4U9wuzMO\r\nACVtn4+Y5FpGUwPfc3OlKnO07TqZbBZBbALkYyqVFvwerbW9G3uklQTPC2cW\r\nl5K1NmCNZW1xq/zdHhXzKyFjHTMq6TOvShDiIKPUXpHywkOiCuN+A/cLrATJ\r\n5wRzhYl+dbIylTSyHhf/vHaonrBzTM5X81ywqUAjKRSuSGNl16fuDxaH8m/a\r\nUl7ithgEsyxfh3j2NVyTmtC08VxaFIBp7EVDVzTATZSjDCS8KhJ7MGWNyQJ2\r\ngwfX8qf4eJh7AbrarRRotRrHMAPcnlTju9DOb50n6mMFna99N2OhXL2er1VL\r\n+V5YQ23NNKxzdvHCSYNi32aX1iOHnrpnGU0eN8NwlfnTjSPgXwNQOn3gHWgz\r\nCFLIJka0pMlSl6X3pJJ94t6quWf2BjWyqpZi6Ja45JsDtSYTsC6QKhhLJVk0\r\nGO/bbGDo9+UlngZk3zDFZ9BDX+pIfrvqHD8lbReyw8j9+LN1a/5Rron6X0ic\r\ns894wnA/Ps+QSc3ObzRpEI2jjWhJ4MwwTXmlH30+MkjSEPEtfjtdnfG3+GFj\r\nKE+1CQM45NattANA2c8X4qZtuyuC6QjaSdtWzOBqFLLZLsjtPLvpW8KoiSds\r\nDTsbuQ06KG+UYkORTTrKnuR7/+Qgz+BGp9M=\r\n=1fqU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a94a4a8914849de595b3a5c5f9d6e0b7e12e45d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-RbdV/yXkVtgMZwcvgtH7VbhJWO2ZUnnHZWZ3CzVoroEqvSunlBSYMZngPbnFLiO3ZKTSD9jFsi+FS6MJAj3iRw==", "signatures": [{"sig": "MEYCIQDOpuzGebgsCWcScbIHitDfdo38UESuAqD6qk7+lZno5AIhALyl5gPZhU8trSYWf+L1FD1LXULcl1t7g+3lsh3s834F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ1BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNwg/8DDwRzgzCFhG3jSld9K2LZNdlYUMTiIKr8KHxqbRTajyTTNhK\r\n3Au+rowFN5v9ki71KGRu96KMJ7CLSEJCgbQQTDzHgzPcpYvXz5+VZs1bq7TS\r\nt7cwUNvD9bM1vHFlZibqWHlTX95y4U3rlkMh9gbJuhbpVVGMmORkjFmxJ53v\r\n2W0w35CxDQda853dfvlt49fvbX1bPe1E3snMJSgtnfqv61R9nD3NQdtWD/co\r\nTU7UrgHB8dffQaXQR7+WctrZRUXrXUb7wjVuZgyukoums8R2f9Q5FIcWPD8E\r\nJbuPpcjpXWEghrCAw/Td+5qyu8RoDqLO2kEBMUXaidKWaQgp0XhkQdEHmIFN\r\nh6h72XUAYgB6CCRnTBOvkRLx9/8cWZnTjDb6Hc/OdNXJRDlDPXpND+iTNfe0\r\njQneefBaj+a1aCu2TJw7fs0Xq0wbL2ZoCRkrPXb1J/GLG0Eh/52Zy7eU5ufV\r\ngfsshrFdt9W+hDn6agJna0qZUghjAABnccWNzH7C9nH6Hl9wXCjoyrzeOfm8\r\nQ5IuZY9JYKLfXLAHxAW3uopikKGBchDt8559AW6APaFZA66tBEC6efpREL33\r\nWLkEyQjMctfeJHzTNds7FS2lk1G5uYaE3QHvWhFj9KPI7AG/VCdRnt6U1WdI\r\nKFmSjYzcn31aUJQ50RLj+weN65J3zoKdUbM=\r\n=EtD2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.19": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.19", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "81e4d1f8927eb7e165ba10ed342bea55947876b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.19.tgz", "fileCount": 8, "integrity": "sha512-fjYuXHPk2mdFANc/XYocd4WvVibotGXF4kRQLeWrpH30x5vY5MKActdmn4dgSFNMQo5F4leoTRAziPn3kq+P6A==", "signatures": [{"sig": "MEUCIQCsFfhvwFg6D4K2MV6dqGqgAD0UV4CLeHhmH0lYvz56MgIgB6TOuAZXmVe+kogq0euAzeKTTRzNLlJ9jP0Bz+vgFFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxyRAApITdiWeY3WOfSC9lU6ArjtlkwLA7YhCUnaMgWlF8bW3agVa/\r\ng85lRhGC1l2X71XJgSHgz9EKZ8S4Gwl3wXJ7uxYBsU3nGdZc9uUOgLd+3m2W\r\nwRl25uUSp+UY85xpuNtaQnhVIxX65YnU7V9phUDtdQTrlP+NPSlf+NurvZ4D\r\ndTGLWTFczti7ZKblNjQEGw1lLlfk78meNErCj0V79ZLkKfkbDH3xDi/9+O3g\r\nU82oMhjcnS3v59XAtVxsyOoO8tgPGYAq/KfzxZXKfLDP46VHrumC84ppBYVo\r\nrM+ZNL/Z28KvHocpGRZOr2k/huR6l8WOAo0qkkn8AhWvgFFrFA1hl+l5J2Dc\r\nDRn4qZ7/BU7hNjxNdgBaPC+EsaszlFJCOZsPIWhT0EJq9WwHkHQRPoTDjNdZ\r\np2cy/8q+suhEqtzqvDauG8CBg5B8txDiwlc7/J0Ko4EgsiLM+MiUjYttzO9w\r\nQfepbqJROPXlIPG1msXrsparDFHxxAOzGcicZqSlgOffH3l6G0zCnd6X7Iwv\r\n5WuE17aHQxK6efHF0ItxxH8ERuHAJ1od4M6HPZMUIjl7WZy4Hy6UmAs21Ay4\r\nZj/wTsEuwayIomgjTYTj1zmg6ZA2lQgA502HhiVG2itbcH5bXsjCulEwry1G\r\nBf7M0Mos4pPbNNvTOtoZxaPUlkGE4hy3EEg=\r\n=jaUa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.20": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.20", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "65ba1614873df8ed9bf6a1e1838864600fed14eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.20.tgz", "fileCount": 8, "integrity": "sha512-pdguxrX9H1T1q8aH3yoOikZ97/tnY3N+7AicCXbGER81tffyIWuJPyJEns8pzddepohdTe9LIT3JECrya7WExw==", "signatures": [{"sig": "MEUCIBFMr4oVYdak208E8IZukVoLnGY7cR8awDNWNo3vzal7AiEAiIig/Y8MWCJHWJsR0xrMhj13ECxgnEXJGHfPJimAyig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3cEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo43A/9HLm0YsHb84yatvQdHti8pr5FY/yDtnTBr4vH2YcMjUMQYlMq\r\nMde35Wz4lIrx0mo1rDLdATcRau5FA34+yTpgEf2uZCxsK+dkjrhGAqAUGhsl\r\nAHZu37K3HNKV0jiz78klyOLetUZM4jJfq3qI0EOb57ub1GC+WBBnbPBr7hfC\r\n70HvmhBIOBDUx9HDcdCu+U+Na7cIGvwY4VIOy7tVueAudAxAszio1d/YmpkV\r\nq6BEeiW23OBfJh4aECCnaJJ2+cUbEyZYQJpK4LjCh9ioCGrz/wiLwjlojBFm\r\nA93g76uqziSyWfmjyigMTrHWdOrOZ+TXQmQqqoznjJi1iV5K1wy5CfYmiqO6\r\n3U9iKKXYlHyqWRJn7cAJ6rG3YOsNcOmAH+mS9QrVV4FHEjMc8n1AqGcknysB\r\nmBcvmT441cW9MzDU6F5Ew8YuyIwX+IR25tCWzFR6b2wmP1glpGnbtIe77k2Y\r\nu5sO5MG5010ceSlp+rW93/SbgHKp9Jhtw5jamdODh6BuXq9E/0ydtz+iGfH0\r\nSlcgiEZZ0drYgRnwkuSz2OXqdcV/vBtTaR0Pa5nHT6zD615tuIH8xOBVysX7\r\nFtLgMMxT4z06/jUyS2J44JbCQfQFARpo8datoD1n6X7MkaWxYVMTB+8D66j3\r\n5CmVn/qEGnR8VeNlFdhrmqFTL3e43ZFEq2U=\r\n=kBQe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.21": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.21", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "91c35d7ebbc8643b6f36dc651c79fbc9cbe5dc33", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.21.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON>+WUkn4DgS09oI46AYKFQ0iY2czc0w1xS54IC34S/NUK4mNEnotVaRQsSNZ46SF3tCaVJzhLlY5ak8d9ObWuw==", "signatures": [{"sig": "MEQCIFRDm9l7Xnm+TS1sVhKimsdu+FKkJRhS9ez/rC3Hhmz3AiBH908yrVYaumorWp8O2WJVVJfzaXX+lp7l0ccPgT1siQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgCw//XapFnY4faZ4S0bz8WqqvLZMfESVg9F2dNl+krFg6wx9U2opi\r\nrtbfOZmdHLBzz6/qolOqFnpknWCIGa+4q0Zm/s1KNIoWpskjFShl4cLJjTVx\r\nw8dITYx9PSSdDYDxG3epnuINIZYx9+12QXos4XluRN/m9brFJHSf0IG/zAtk\r\nsbjF11rG7t+omFWhXIRBIzzDzLumvpLXieQMOi8rpuWQ5BBfBIiMzq6sfb+/\r\n1wEYqKxBEVwuguJtHBM3B+fe5SmyIKXjctyCLYH2VUtmlrvjAVhr+ThfYyjo\r\nwua4RtAoEIqNk+Z8prAUS2PwUfRovzKBfIpFHZ4QDUceoKvpA8J311XAkd4y\r\nlZcu9xoZfD4NKQXUiG0+NBT9qkghJ2vt1lBG/VKuc12Fy70RoVobv5CsTgRd\r\nsmkXmmOWRLc9MGLPCaMemVc7qakry2YGbxPAvsFuQ/1GftywwyfssiCTX7Iw\r\nrDR8LALgSH89AI6SFfuf2Uwu0ytjJUiij0+6xfS9pODtjHzbasgsVLc5w4bo\r\n6XufAWduHjX0W0euSXQWNbQul3J8XENAxBmc/5J/TDlIreNvMPZ9+VDZ8PEB\r\nvW3paKpuxaGGEVCw8AJvQQLH0hsFeIYVXK6yN8rG5pR4/ZABt1rPd5P5MbGa\r\nKSFLVFMWSACXsx9egxL+1SGd+a1Ozim7bXo=\r\n=9G5Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.22": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.22", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0036c0eb478997180f567eb2876277bb05ec61a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.22.tgz", "fileCount": 8, "integrity": "sha512-1oLCexuNxlGTWuLkeW30hsqO445x1yEcE2zm1d0KXQHbK0HHOwheAF9Bh2E5kRtZnFTeYiYHDwqw+uGcmtNwIQ==", "signatures": [{"sig": "MEYCIQDkZM5eKZ54mfDDQBowUFgAefIigmaNf+hyytyo6g1UjQIhAPcY7jyHDKr73MbtnH38UOPSbt2O0i4bk1v+hJmzPkna", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+fg//WevDkonZls71Z1X33t1LctWe75AwRmv+Q+mEX7HKG+ZYy7Ic\r\nbLoXOrMXDuTZpDyFmvbd/N594AoJru6q94JQRWIJeiHYAUtxBWVYOm/Q+lgr\r\nGJdPjj7FUqtK4aHvhx6jpZpS9js2gBjgrCpbiiaUGxYGH61/lDaqQ5SFgAXN\r\nYjjXJ+JMCoLMhUA57CpZjDOKQJxETpCD5FhymOVreiYrMxtp7N5I3M7suewO\r\n/1XttMp3ObnbmEfS90/WHxklexnCu5lR/U9+85iGS2cRcL/ex1j2jDzn1fyr\r\nooXH3IVCkvXRhi8pscrmYvQusLnE2/o6VrhReW7U7ikKoNGoa/O7vs8WRf3Y\r\nQjPjRd1kU9VNKpqqrR9u6eqGtKpCqDAe8JMH+7R29yhveA/4oCeb+otaIgQA\r\njUxadDrcRuZyzEwTRkam1wgANt+a1IbtV3F8DVPELxbg99OMK1MkCdLAo13X\r\nRnnFDmGybSqBX5svPuAn0OxPmTASLimkynOZHpxpMxF2WjOZD2m3QbPZhnWR\r\nlEqnTX2WYw5VCOiZXFpOE54E0lvtm+0KgC0Y5iWCN2kQzyYN3rc9qWXgz25R\r\n/mml6rcK+2J0CVmhwvI9Hz6T8YOSBfk/nPdUA7KreGdyqDrivQkrL0ikrHmx\r\ns7MponUrbUapxFTleRoLa/zhDMJVYMozT3w=\r\n=irCf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.23": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.23", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ebb22a98a73a4a67dfe8830dec4395d6d0d80719", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.23.tgz", "fileCount": 8, "integrity": "sha512-ylZMKr04z+FFHwDni01fIA7fCBvLHYe91QEJDehK7X+amwYFCW38CtRetA/Q90sHfNiIoHTML/PT+9sleDWzMw==", "signatures": [{"sig": "MEYCIQDnczE0DsdEdzlJsIL8ao0ad2KuK0tvOsS8SpJd6c7sigIhAJpj+5ARv3+ivdG0qu0zvo6KOQv4cw+N7CKp/Mx7yCYm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKH9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreHxAApObRTQpNwhya53fOMN28J5AHkvEBvXmEsHb0KIzR52vTY7ul\r\nbFn5vvBXpd0he2+siF1OxwWJrIJnJ6NtHJmQm4cT1gxwvnCO1VxzznnM+C6v\r\n7yLPxMi6qooRHBSVy08XP07VTRUqUbYxGOPfkJ01fq6ImWIasDi3D3QJ2wga\r\nW/SmwWmHey9NvTTixi3BedEdcMsgKPYb0WmmRh4O6oO7ckH49oqs+FKd5G/i\r\nj3UNG8EVyUqDhlqN3IMB3psX4nrNpxrTcl5rNvgl2vHlCmgI5CDJP6xui5hm\r\nC9lAwcOLr05nh4jLyu5aMDYUojdfNj+OslXwyQlvGmbOpmICPAgI0fJMfutD\r\nfZ3jFEaCUYg8XUaZ9iSfpEEfqlpIT4URab44gGQqGeKPKFCPAnQ+Yr68oXrJ\r\nBF0ZY/tvy1k0PzLwP2DGiPCvugw9sKusuO3PteVKgkwrrCVoXitmwmfKKYLV\r\n/euqVNPGNmOg4dJYHLgRbJCn4tqiWWIRf1IBO2ZjuuHeATHKpnAG+mKYtVpS\r\noksDYdYT1Xpib8uxFkEd/r81DUT7UV+VN32d+GWH6zdseCMho9YI7funJLWL\r\ny2h5Eckg1KPXfNGbW8zhU6lMVMsix1uZHhDe9GoYqJ87sPxWyuVl9FJDwe7T\r\nJ1Kmfi41SjuVxw9KE6uffMqNRVVWukxyb70=\r\n=UoOy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.24": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.24", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "83055ebf2b6e27640a5321126386aa09c39d47d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.24.tgz", "fileCount": 8, "integrity": "sha512-CUlSkeCFwWCCcq5I8ZvYRBYKj1sKe9Phk3M2Qu7vxKpUIjVKj7n5QwU328nQWsoTlRULfPgUG8SB/BleJXrJOA==", "signatures": [{"sig": "MEYCIQCgRE+YsQwI8cqci+71EtatrXSgtFfYHrWsp+QSqcKmFwIhAOmg5QhpGhfAqXNhWOtJZhxuW3t7oJwyglSYqF9g6gKO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLiMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqw1A/9H0YvOB/xdFjTFgszVepxqM91TNvVymEz+1BAG+UPg1yNyCXV\r\n/ovYfXTjmX+TJGvnEd0RQ1QxASsW4KgTieVeQb7dIZUIRDA+bwI5Z8817OBD\r\naqRbT9RL4e7aJq1ebnUCCx6flrsHpZ688479Q66/tIbtpoQ9jK5R7hNHsUT8\r\n6LUFkgH7QTfTqVrjaFKnfy4wS/dU8Vh4fo0D5ZNTeGCwf2w7pa3a9GjfleOi\r\nJK34rMY8i0gD7YV37x3STlufSDY4DmaLfXrN3/ElGcnYa5DRyQrOnFQsCuiq\r\nbN9iV3uLESDr3w6gVwX94YM1CP2jW87srX+g6BG4gt4sY1ksmPgNpKyHCEti\r\ngxxAUhCTUDwRM1ullvnQKKG53fPGpcaIgvqCCrYbOp1mNzh+llx3w48qcs00\r\nO1havUAKKmeakYjsoV/a3kgYZQQ5hOUF9m0qpewf8JXnn+NqeVgaajitod6s\r\nMX3ENmZDa0yjVIogzrOzijK5yXgzOPKWCTQSponA7XmC1QzfVjm2RtSunoYv\r\nzF0j2lKLkTP6+FWFKek2/9BYpCJXe75NHu8qlPDtI3MdyZdB1Shzo65hzr6M\r\n2UhA9wS/nuuHQaT4DM1r1YEfJSuY5nA/xJg7cdGcuiM6cCwHpaBa/3HyqZr6\r\nKpkdJM0vkKxtuhoNAjw3GBlv1TGkpVdS4Rw=\r\n=f110\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.25": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.25", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6244bc4c5bf7a2b2c3eae8e625e8e2d4df9f6a32", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.25.tgz", "fileCount": 8, "integrity": "sha512-Fl2U+r+/QgxZhUNp9fwPYGSU+5+lQGwq3jPTF9fxByUPjXGZLFx7/4H3TiN5HpmapswLRdjeI5OoGdzPoMVoyg==", "signatures": [{"sig": "MEUCIQCgh7jdmuuhTNGIVufWA7FGqkg/7+1NTexwBCOnZISS2QIgWUmIPAtrQpBrweMd+7PHU3qR/JW72vx0oaI+qQXUrFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj5DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvXhAAhRcu5O1T2/7+tjXw6HMDN1zIUDJCa4xhdXmVc0b8KfA9IyfK\r\nsBiJqZUF1EJQP0iZqRKl0vTWTKZZRAxbX9KncMyUwvA12SJXRUXf1kru5m9U\r\nopxsZ7+XcurcEMTzGdem9VdvXPwXb/1VANouUf626f8aPZ7q5BoKj34FV6H1\r\nXEx68e42vqa2AHkyC4McLVW7yvU7BM62NTCgtGsywl120jDI9eGTqpZxoMoi\r\n+rjEJt9OdRPspFONjrRi/qnTBK9sj5hc8x3PKfHprkfYrQEHOWAufkGEyEKw\r\ndOFBveFW6jvWyo5M7SJeb3+UNkqjGiGg6BRABnDvP+Gf3me12GsfERXq0PfU\r\nz7jZxT9hwADhRq3fy+ymhaHRb+45qZaQetWGpItgPtn2wjUhpbMBDj5aqS6G\r\nYTY/TafHYOFDzFYPXo5v9JgqGar2qHUBZdoQD6LwdjqoCBM+N5mp/qqJmcws\r\nOv8yV54ClQVHDZU7lXw6Mb++qrynN3jBEXABgOsqmjpAbWM4USPEoLIwa5Z+\r\n+RA5uwy5wrFgnBxKSXJiQCC93T8fbL/1yZolRZNvfWqHJ3XVHvidUT1Rdrbb\r\nx6utGCkrxPBjQBdinMkYpzFJKF5815AMaoyCE4r2qmN6jzCCgNgMd4NwxDpQ\r\nGke2LXRl1sN8wsow/9CzjXjVntMNXD2fl3Q=\r\n=/hE/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.26": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.26", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3e66c935523c672ab3652f00262c0a4eb443719b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.26.tgz", "fileCount": 8, "integrity": "sha512-U97bwMk9YHHniFPG8jbf7Ozt7Ohr0ng4PB8gwkPeAaA/e1qR6kprkKUoeQ5vzn+aaDfzTcuUeaDNSovEU0P/og==", "signatures": [{"sig": "MEQCIDXNbNSmpboWd9HYaj4ClfXuFlp9H7qoohwHoiP2jtoEAiAoJwRQi94a094F1hDAUIKScLmI2CDfQCAMLk4YGvFmJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl19ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNMhAAjjoMU+yxrKhklQJ+xJA3uXAo/eHZPqKDXd148OJKGt6Wqu6D\r\nV2ZGPVRpSIFX+cVoWfS2MTZx2avVywqSvJDxgGKIMCRSvNYruE8eqTn02vb+\r\nHnFJSB7YfLlJCb1lvlYO6LMYmdzE2wfpS3PP4USd2925vAZsCtY7tfJIH6qc\r\nzxVz7/TBiG5MMy989u2Lz/J6flbppV0e0OnzIo2c5dYtwcwtx+h9mVk2l/7V\r\nwEzWMuO2s2O/PrKec1j+PUk21iCgq2RONtDLM3lo02PKWyMmBsGAl4AcUeb5\r\nlRNLm0/SAGFL4kvLsyJsQWPS37iXK5I5cXL0eCh1R4f2eQ0EvIg71P6LOumP\r\nMrJx92T69Rg5wr/vqzw3tf68Zo/YcQ5G9mW8UUfYl03xVRMWR2IM/IKxrh7v\r\n607f8jfEyCqgIyZN9yI7IJyPd/JlSTagGBrP9gKmqqxylEMlN1dmmWr6Ga4h\r\n10J3S0vGFav/R0+jhmeP4XANXCcmxTVCUFtLwd1c5yxEmayZ8p7/2d1R/R0V\r\nmdWFEQBScI1nMwd+5M2904NDAdNThQNssWVNT0kLA/HwLlVj8NQHWKKT4Cgm\r\naqdgF3oEmSBRaQEjasI1NB9VBw0B6ao9oNpFV/2c4qww92JehsYsXSPPtiGn\r\ncdIFv+K+j+LG7MVNqxWR+w1NyirgucDocAY=\r\n=noch\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.27": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.27", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "087b1be56b7aa9fe41ab945265cabf9debcafc27", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.27.tgz", "fileCount": 8, "integrity": "sha512-tc8R1KPc65cPv7qowbTOK2QEdGyLryz+rybCa42loCzazTr75QalAANMNVG6ftoi7wCxthybQgrWuxiH1higdQ==", "signatures": [{"sig": "MEYCIQDL/PgjiwOPzKPh2EHc6qbmFFL8eDDxvQZkhyUNjL40kAIhAOYbG4bkHUlUWAdCFflpN2TBd2H3evoHcmDqjTLzF+E2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ3GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlsA//UL3dNY4E0Slbf0wyWLSJEpArFlQ8UtNJ1simhL1qTvuUr2gR\r\n8ny3YTov+BZo8vkXW21fVieH5828xJqiA6HfdygVsDODf1o3Zn9EIRGxi13C\r\n5okesqvDvNhjIow32i6EGzAWvKYWn58S+hhhqKzv5//61vzk4kFPBzvtOzSw\r\n9PmAvtJMvfZzvYnE/u9QIqEeSMHU1fDcP8f/FOktrq0eJvJebyatP+P36ccl\r\nHw8sjeIAjUdOKXrFpl4PWz4zbNcJv+/kBFoDmmgHZjXZr03ljW3lqGcNsR2/\r\np0W/hPXSMYAKoV4VNr32A3VgKY7Y+e7fx1OWtfUvgFobY953F8AGPG00G7uf\r\nSchIQbUCDO6/KWWZ3ahk2kiO/xNuqQMGiLRsV9NF8Ueek7p++4opqWPA4536\r\nQnFIC5rCbD6fWoWZpxZIqjQbC5+o0myIDi2RSjCCSaY/RbBhsK1Bqj/XueaF\r\nUnErk7EjPYZIY5wrV8wzk110fvswC+QD3bizFmcC2Mg3zDrfKmOZ9eKLzlPC\r\nBFC55YEvXkN0wAea+v9oTWJ0IxfhYzkSWA+KsGnhHCg87Tj63IjZBBlZcnp7\r\nf9orwLk2Y8SUvoEsn7KVG5OWxSBdhQAKT4MOxedS2wzkU3zcVCwmPGB3MDiX\r\n+MdVjl2358zyuBbxNYxpIpl1ydKaiM/Ww4k=\r\n=kAHL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.28": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.28", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "71562aac260b24b38fabd1a13efa620a5044db83", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.28.tgz", "fileCount": 8, "integrity": "sha512-VkxUUA+5S0ZlwXEmChp1l8pjsF5/cpbuSYfT0jaXkPn/tvidK181yDkr/Jgzx/VdQki8l2SnXcWqJoUOZMVnRQ==", "signatures": [{"sig": "MEQCIB2gbKNUvk8ZkTfSbATJydlRQoc4LnUXj2E0ajMOYEhIAiAPS9uCX0VW5mMHsdkTCOTSmrrHRQDjLYiFSmIp493tdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildOMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFNw/+K1jUrGu8kLwM67+kR3SEjMU4uHeUTPoU+mG/RMoDVac/28iy\r\np7oRpV9+xunL8W6UNpLs0XHAUvmK1mUdo7H+y3oS6+LebFIXwNJj4RSJAZpr\r\nt7mnb7wl6da3rAibBo5pmPPsH0S0iU+sl8t7Iu+2+4ggOkPDayGewpUSjNLH\r\nFMr/Hr4EUneRWpIaEx7hxkcmuL7w6y357flGrvMqYRcjEgu5uQnecvwVaaYE\r\nUrVZ56Gh+yncosXS+NSH75Fgk6iM6zBbBE2p864+LAk43VG2b1ja0iEidV8K\r\neSsG44jBQ2x8yLvGrbVE3RqPN6ggOcl1z48oOdccxsTr7smqs/V8DRpHkA6W\r\nrlbyUfNCIHIu10b0ycN9wgGlNHwDthi78esQNbDhbNKzyByi1iNztKzxXbsS\r\nmb1OFF/xFuyvuenrQAKh99dI7MXUllPc/AecsOov0IMpXsBpyohvFI0IEiUN\r\nBrqCfYtYJD+K2juPYpm1EJWhvTo7OkemYN2QzP7BLUuYui+iQo76h1rHsFBW\r\ng1jxcaGbTHFfZ1jwKmZN5u20mKCHpOG45XvMY4sUlk/+5UFF0ZCuivEGgkYV\r\nDFSERDFTIENzqA7wsgcgVMBrcq/R1vX4k9/ZP+Cn6Lp2BrdKs/vsTVe0QSTh\r\nVvmye2rCvs+2h8mlsSlMWVF1FwfMD88L4Ds=\r\n=i3LY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.29": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.29", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d1f6330063b473a62d18c61f38675d39f008832e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.29.tgz", "fileCount": 8, "integrity": "sha512-aJK1ComGhci8DypnpvBo/bPXT50K9Oy+hUnHYLpH9rDy1LbsVCjblMw9EHDHpCry+MWBme8RlnvVBPAP75PpSA==", "signatures": [{"sig": "MEMCHzOKZdHPwx2tdebJrpFu+gM8rnXyvGh1QdgaWGtQJp8CIBd1D2RB/sf0N+JsYQYR4WyjAVxEIxEqgtiMBm4lcSoF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildsBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop6BAAnXSp/qKxdVktvYZTsc0uFMzet4Sz4C23FY9G/aJq9i0baYvZ\r\niKLfMU3J7a7ZED2pYhfRKGAIkXTY5b2rH1GgtebxPm7iMWeZMo910pfc5J7T\r\nxPXXrjpQOieQIeJU7tyQBx3HjW6vVBvXdfp7upv0PNkyPMThXofo1ow6iVK5\r\nmLaVep9AtK686KXaw9lAQ3XO6sS8X+qr+2GA6VaBT3C3v9N9kG16SaT46Ltq\r\nd3qIYXkRWpSd6IMGcQom2lnyLsg4R4Gix1JitzWhc1WYBBqxxyYhTK/WpgKm\r\n92t3+CM+IWIkhN9TFzzVJ1XeX6o+MQZIWdExDXGT9POBvE4XoCanqA1LyH0Y\r\n6/dD1EfAcL22k9eN1SNszXCnKnPLdc2TdJCFfvGWhFk3DayEjW0eJRMP4Py2\r\nog1JlZbtzHzLRvhIoGJVr8L1MtizLm+kyL/V58NLelHmqW2PnX1rYSrI1CJM\r\nEIKD4xd35IagHbhHZ3gZYIf0f0ZurSkvwREG4nwqwz7lb+kQpeovNbovHwBO\r\nk1qLuCxzcezKhX6btWq06CRyEScYp2V+6ACR6YopATncbsG8dk+6U2rwrCKu\r\n2dQxB/ie4ycUqgd/Bfqpwlk2CY8wg701lctblGTCEComLrS7+shXvjmIV21Y\r\nwbfUejDEs+4dpg2EpBBNTktrf78sZE/SCD0=\r\n=w3Xt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.30": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.30", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5f405d36c8a314ecc21003850a866a9af018cd9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.30.tgz", "fileCount": 8, "integrity": "sha512-DQ/9kOWVy7EogclsS7XQ6HCIwa+9PZhiXl61kftepRQY7qCJDtOXYcvZIgN4nBxOZrQQ6/bRsaxBYRf3uEOckA==", "signatures": [{"sig": "MEUCIBKfIoaWQrAMO8I1N+INet2Ux8PQehmv+MNPkqxgKBlBAiEAv4m+/y8dJHaqTOQbTYXVzkuspwlfVfA5RHh/UJn/lts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile22ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAqQ/9HlK1xQh239KtBRxoahzKcojQu0Z3uZi+0Kn08b9wQnc2X3S8\r\nY30latC3dBUXVnPy/idfVV+vP2mPR4+wrViZcdtY8dwmVPSFlrT1p3EPnXiK\r\nb7MavaXH18yyJPPWt1KKHLe2rfPFo8xNkRa51CBPGjngVRP0pwBnPPoTzUdt\r\nik1uMrfL5eQQLhvp2C9fXNVAzOaPKAfUHxKL6tC8cQUy1KuGCa+egmRrPgsX\r\na8y12RqiankJMO7yFTNCMxQaqfpeJFGsCCdZvlWH2RRKERzlFB/Mgbjum/+l\r\nsnjJq9NIDc8N4kcEjQNc03FHC85IfkPpQUX0/hsThhp1ugSc7yD/21D1NbY+\r\n/CAxrLpTEJMOKvg5ty8J/uz5MTLmJqiQDVx45/+WIxuLJIsvH5UE93vLvpVg\r\nvI9cTP0LuYkgWv1Wwd9QEUFpBDIy5O41asC7gifnltoItcNBek/nmJ2woyyF\r\njc7ANM7K9a6Y1l9y2g9vUb4VSjlXe7j/SAzSC4sxYgwR9rfQ/8VjJ+P93TXa\r\nLerPg1We+ka7P4ojtTcPSq3eo2YNMyyrzBomgUEUulm8XqcgeY/9zYHqUgGr\r\neP9BgjxNP286jLhUPIOOySFtXKyomBK70UJJs/lmiMzGt+A4N2t/YffKsmp8\r\n5z+wHVjb9FuAjyQ01vjnrQS+toa+rMKAs2I=\r\n=Oyah\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.31": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.31", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7c037b159994c05911a863fe3e58eedcd83437dc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.31.tgz", "fileCount": 8, "integrity": "sha512-lItMYsPWh6wVCLIMJhfkXzBjBlvf9pfM1w7goqXTL9MUvWRQqZOHc4QUS3g88ntjRzm8PEI3DbCCn8nOvCiW1g==", "signatures": [{"sig": "MEUCIQCKwCJXUCF2dtNDIg2wXDVPujU+kk3FkV5g0sJdEapt2gIgDrM5RLonoRf58QauXxZDYfa7hkDRHpBx02xaTE9r5IA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg4Q/8DmOlspeupFZ0wNpzomqmBzcROJLsPkXmdPZfMoIy/1thCslW\r\nomQg1AnaFF35r/7jP5DVsdbtJ/ncI8k7HKQLhmYGAUvBX5vG+wdyMe4fRwfe\r\n0CCnkwljo/nlWPOfFcv8BxI6fPohlNME8dzzE+x36E4EzT+99pW4AZSTqv0o\r\ngND/2PVf1prL/GN/QRg4eX7VjO/iD/98q2sSPtUveP0jr2eqeJwQqsUpvahx\r\nlbJjNdbyql1lW4dP3s2ijps1Y9MJ10UYrg3YIOQut1W06mF83JQ0KZh5tTE/\r\nhVx7QXN3u3q7OM/L8R9Uz1ek1RVYZNY8DPzGNuwI3ZHo2LUrVFR/5sNbK0jl\r\neesH6Qe0rqQKcbVxRQRumXT+IIHsWB0PAh4ZbuoA2yYCpICB8PacXyKCgvgW\r\nkHROkT1RkQOTr18VeR1PXkFtHREPq7NNS+naY6jOu0RUX1DJZFto+mvAeJVL\r\nNbI8DwpBzvDigYsKKoy/bArHJv8SGvpkUAYYFdXT79tTi2YlvVmzW1PJkOya\r\nSh9XUmEUftKlqhk8BCmVmIYb/2kr+kk1Mc22z11nVpJX3IdGr/UCGlPc/ubK\r\n0K6ksSDzkOBdD1701eRUn10AwNf6jHdehHqAx6AEtdYmg6BxxZ0p1fm74hX8\r\nW81U3GUKrnQfgOimf37uMo7N0HNI1oOzexI=\r\n=/0zp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.32": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.32", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2225dcf1a9e00f4c346bba5b7c6cdc58cf124fa3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.32.tgz", "fileCount": 8, "integrity": "sha512-9BQCKMKZAgiwQuBQjQ3z3IJprPg38itP8+ogFw8ESgSsWrD2PeLz7i6CYuv1tVRYVi6JC1D8Onso8ydc0yazBg==", "signatures": [{"sig": "MEUCIQDZ1w+K38uLmtKVlQqCHIBsfVsl8s6jjip4EDsXmbb/gQIgAg98ezp1v5NQlu63vxDO1G8qV/+plnQfHsijpolRkWE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrB1g/+LQEEyORNtT+291+Skk7QV6XXI2LkiQYHfaL1uvd669NS9GTI\r\nssY/BXvOoshXAgdZ9EmTXWf9oZOO28n1WyJN/V6nx9PbvbkCwRS6KAqlLVtw\r\nhpCqOvwqhUMHzSPE+hsghF5QFb1hBnUx5llIoninHqCsqVChRZHZ2Ig84oYy\r\n61xQhJZkI9KOgL/pdZyIYkjaSrPocKz1c1u7K2Vg51sN/1JlB722HFMZvp0M\r\nVyEwDkeE/9tDZs0iknr/novn0VufY0kyopXQaLTPs0JzmgCECMUj8/s+EOnW\r\nxiFA8SAwYBfSXsl3Yn/GgIxlh5PNCWaO0m/l2CWXZ+FcGwXaZUA/e3HwEVWl\r\n/wc2DbWQCkKx1t0e97zPUKN/TP9e0/6gke0F1gcv9ousXSq6jWCkEFK2IyWO\r\n/QQ1W0rFqJOA3woh6j7h/Rjuy1rKtxLCbM0+ATmUrqWOaAB4/XC2jmBbhC/p\r\n6srV/9vi7lA2JUItbPF+KSpsxi8HtXDaOs88Ylhcw0H0Jz/TjL5kBnf7atWu\r\npjg7sPH57vFkuu3kChYLdX6qnRGyNNet8sF4PrmqGSHsBtj6WV4g2vAk4OUk\r\nmAdGf9atB5UfT/zlxpj0XCRynXDJoUSJtOSO1NVb+5NOvH6GWWXv6GfIho08\r\n2tFSd72Tb2lEq4lY2ly6MEDoe+cq8G7Uous=\r\n=Cg/F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.33": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.33", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b00061b38c6a716f24265bb6c1fe6cd0be19a34f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.33.tgz", "fileCount": 8, "integrity": "sha512-Cbhzc5qtFBcMEaPA8KkRJB9kZsfzpQAfnr6OlhKk7XkSOkbDZQ5A4LmY0hKdahW77sxYK3wswFqncRZDfXdIVQ==", "signatures": [{"sig": "MEUCIAhORsQvMWHRrB2UQdZ96uft9aXUb5LGA8rqN5LuLW1MAiEArCB+X8mITx62xFpKGJdbDAl4Kv6oPCsV9ZR1teh/VDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHc/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWGhAAhDOQIhhM/oVZ8sjYoF6iiwr+THt1wxNH1vYA+wjjPN5FXM+9\r\nthDp5k7GCv9UVjPnff06Jqp5eEWt6hOIs3LC6v6TCC9oPYaFoU5MFPl/56Kq\r\nvrxB6AJIJoNHE1K/7X0syMQ4cHL1JoUvrPSvJkfC1dhnHw3Q0Rhxz4FyNkIK\r\nULWR7FGS0skGmWcfX1LGVQ9vDDWxLdknlhoAgxlGh6OfSqmzseNGzgLm5R5/\r\nRN4N75LI/4U75ofqXmH+EFXHCkHd8QYlZcXMlZgLtmsiXdXuyAJsjPMHLskx\r\nqLfU2nYURDaY0dJzdAHPAxCMuhMUZlDGjtGhytGWcg67texFfXn5FA6yOYF1\r\n5z5tKdvUHtC2pfAa0dZvo65xl9tUhR8l02McSDDmnIPHhX06PrYLeT1R4PKy\r\nNbyyLchjMexAMo6Y9/93mZ3eLj36eCaYtrtnl2MCvrNvJ++sWfuRThT62k9F\r\nzaKzvwUX+s8MQmV5Z+maOs5oKIecc44CkRzz6ElUq9CX74oTKhGV0FG+4zir\r\nfL16NFqLF8AQ9/p4gx/kpoWgzTmW/Da4pDbxEiuLFWJi8II8vhNZxOh6K2Pz\r\nKt9ZTghHF4USxipui5F1l6PXNzDEGTa92OIuk5fB9REIKynDk4Q+gq623yJI\r\nQ37lv6zM508RikXYxp4KkuV9wSkY/506/Nk=\r\n=miGH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.34": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.34", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0a8def415379c058976264e2635f3d19ab8755ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.34.tgz", "fileCount": 8, "integrity": "sha512-OZkovfsQoYum/cJlBRBJ3JUMxZYyxA4wSgA+a0iMWTjZPjZjWMIfJamawKo8nPCWVONBhnC1WiPY77SbaxJ7MA==", "signatures": [{"sig": "MEUCIQCztz0+9Xg5g4uj2tgVUiYGP+hucpr3JgmjMoT8YlJZ0AIgTBb+zQAZfQOkfD8FpVoP1OtibGrx3Hi4+CSKg8DNR5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB9BAAoWw2nunuedY7Ori9qTPpT1DTyXxgn1gZDewEBfyFEDNyoIAH\r\napdFgNxdFRTh1cgwRe35pSFGYUFHcU2pax49nEl/XYmz3g+uL4/UOnqpSGiJ\r\nt9Wb8SMr37lzOhxU22gsHxE/osk2Fb6c/By4ygYZKza8KuQXcWXluVJpj37/\r\npjeB8HQfGgcBKo1Rft2ni1U+jl0bxMiFpBVNaZySTjy2y5wCh+D2LYkRUbtb\r\n13CrqVP6T+IOm6i7uGqb/5TRhvKcPtq2ujh+F5k7C0nrgrA7e7nb/GBMSXIL\r\nTu5B4J6ToLu6vifl8zk5hI4+SVyelVHSK1Bt7O7JixSq+jLoQ4EDnNu/DpE2\r\nIFyppbrUhWiUcltpUf8SgOyGF0VQRIde4cxSppzeB2gfxgwh1FnSq4xkAmy3\r\nrwZaPUus2Dt+GKY6PU4PAlIV9BZeTLPSP97zovDfkFgTSwis30NmrV7jI54V\r\nFvas1jNPZ/gw/B16Gce/1YWXknITsa9MY7vKsnlBfw3fdp3hvU1Dx3zBybBF\r\nidMHW52VCLkBC7DY5/zsXMKn3xnaw0ijx7OLGJOi1+e1nwUFjmlo8HpEuKH4\r\na0/tqDUpTsBLwhaKespTBsPHDuB3k+gCQ7Xd3ppBymFzMB5zjK1EFTUz4nGA\r\njkcu1QaSMOX3OxS7hNhNfcua+H76M2BJMVI=\r\n=YaM5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.35": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.35", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5c0e369a1b409696d86654e504241f6777e31ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.35.tgz", "fileCount": 8, "integrity": "sha512-BnrC8PcVcfIFIi0slZZZBERC8s41QK9wZ0Ho7nyIpBsqciOaPFxk1lApC3ht4ZaO3He3lUQ68xCm7AEFvQ+1aw==", "signatures": [{"sig": "MEUCICeLGQ5Gnpuj+eFLRu3qbqRVYII5seysGlb0mEjjbecXAiEAhWNtAmIw0eLzuZyYjegEw2AJQsG16B81qUSuGClmOyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeLA//QCd8eZIy7txCE60+lzmAeXtFB4guRZm3v1UgtCsmk0JkG5aD\r\ncBKZKqspyVpy4S7K+W990B8I8WJG3zh7x34VFsBBolHdI3UCFeq7CfjaBOfS\r\ngRDmB3CXPM22BmcMsSRGyp6OKKGmnzfDUWos6r50ktp2ANrNh5JcOx0tcczq\r\nQvjfpafeaTxtgWXQO3Aod8IG5Uqf9sfPt8+y1+WCkFcD1zqqScR/OVKU/Nn6\r\n6c1KaeUDPjV7jpfg0HsjaKKYoIaykZ8BpcIlJx3Vbr1iqjDWntOeeo7RBbBp\r\nwobuNu767PUJxv45cDvIdMjt9MbNcR3KTFNu7vF4M7zgxFyzKLzTRenF37Sx\r\n3e6FC7Sj24qoIDk9C8xeJ/oB2gSLm1UPMep8C0NaIhdMrSygSvXwbucCl+vm\r\niDtE/TycX+a8FE5gcS6nRUJ1ka1Y854eeEIOWSWtf0fRladolGikI3PjhA9s\r\nht191E1GL4Pb5iC4bcN7nU2Dwp1gNL2zVwRjBbl31wDcY27p3gW4JNCYkp3b\r\nPv9b3Y0PG3MVrBJLf9xzNZ42fC0iH24kFQrhzR+hfaT6Ky/xTP9Ssio8hvfD\r\nE4TyRMPBt0Yad7k8LH2wEcu0r+dKMc7FJYk1TTKah62nPc7ZaFea0iNcr2j/\r\nottY63vixCyXN1I/do2Tbml62BJPC3q6GMQ=\r\n=kosk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.36": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.36", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0b157ceca8f99535156bba3ede2f3238ef94ada", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.36.tgz", "fileCount": 8, "integrity": "sha512-Dt/fMRWPhiWedTfT6O9TEZzkf4gb4F96mS9ybe0cTbhnjIC8yPH8ePhsglsWjLXD1R4Z9JB50398lrk9jjsjiw==", "signatures": [{"sig": "MEQCIHiJY/8f674ar74L+o0TH5kwEP0XuYIb7lFLu7QYFuCpAiAgJWNQZcIf7IB+8zPgOcSkxFxD2uAYAXIAc84eJ7oA1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNCg/7BONfYtMbm1L7oBHWwBPelCWJSV6CgfyJKawyYByunnkDDF+9\r\nTzsEeBwRJYkZUk1QFgAWR5QV3khRMYRDUsCwuzGUzA1fGTc/+nArdCqYSUcW\r\ngH89lZMCkVINuuMjz9Ndu+aYB8CaMH2Ut8vKP7UXWlA5sFHZ1T7esnClWrSu\r\neTJHf5qrtSLoblMsvMxKZZoZ9gPbhiPfNWQ4/O2JjNuBQYzSfO29n65NMAOE\r\nGJw1CWJmTr26wfxW6jHqqxnu1ccZlP4BVwj+nQPiadCUe44f+Ya0XFPq5u1p\r\nKsSx513ExbTGDXdB+dVUp1RexhjD0cejcoGrdlKi3uGU7mM98kZEL76lyTnG\r\nFybLVUam7GYIbmOkZEsV5h8p8J/imiOq5Vt6AtRQtojR2BBs7wlpSEVjPhNv\r\nnWbrY34MON97jam26IeHWmvHmcS2JbTzQNgH5vH06JXqJEe90eiS1yYQg3k7\r\nEZlE6706uaV+HjKcf+Fan2zeFowd0DZ3sqC351VF+s9lGOVHyF7VwI0WiRVm\r\nN9gQyDX8T60FCWpuOZozCL3epdqRyG9N36c6AC5EFftmzvLxaxxsWlJrnS+I\r\nUD3gk2r5xMMfdW8us6FT9PIyRx7PbjldG9j7H3WFae1frV8HrCZm09OjnSHg\r\nKGfqXG3UlnCKVurf5z2MDuHHNNxDNX02fFk=\r\n=KFtg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.37": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.37", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "79077ca4384c51a65c6d52d3c8eed80c8b82e5f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.37.tgz", "fileCount": 8, "integrity": "sha512-Hm78jqiB8uASbZsxEwkes5/KxlJFKBdOSoJiLgGL1VQdM1aHfxgrs9aVEhqvr7hb5GHLECIq+IrlomwZQzpFWg==", "signatures": [{"sig": "MEQCIBh+4stLsLWzqME3i4u9gDDs82+STO4tkgNfAcGN+PxLAiBnmAdNiascDOzY7XMekcRP1a99guFW1o/Ip5POrDTtuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFDQ//RCMjFkgshb52cmz3TKcJ2/x0RKULOcPAc12uXv0oYrnN5jpK\r\nkL311ODOCAQdHYh8YD9hGVW2eGhkbwUkExOpvcR2xUOc39km65qLdu6Y0ifA\r\nbRdIW3zJXR+innodDrXDcKyCg1l6qSNoyEMv522WMCAtVw7ZCcoDzfnuHYa9\r\neMkyk1xWHbxyN+S11y+RDEO+20UwHjxEJdyT3X5me1D8J/iGZQOSLA2tlD/k\r\n9y8IJ1aXb0TiQwUlJA8N3ayIOwkZuFe5OOLhsUVHgef73zc7ah9mSwGriSnq\r\nPBWrFz2lesidEt2GTrmj+mRpuJBLr7dY2YRBdV+Oo438O3SbFtUGGYfpA/iw\r\nmAORcr59xKXRpq+4MwlwPrZMNTr76a2Z97WGKXiQGPKStASLddZ3IzyC4zkK\r\nGxOFE78jCd2RPJcgdq1tjTmx+VWzeFUdcKU0e+Ssl0Qhs5rCJ9d6C2iX5Klu\r\nDXeG+7wr/g+3BzI3GCn9tpONUFmxavq/AkoV+KOoRlILlQz0PiZK/3wRvcXm\r\n173KDWSR8T5giC7QuN3VqYJZwi8IvRS/1vXzJgx7SojTEDfZw2UP5JeBuMR0\r\n4flii7+xd1euUylKsUx50SGu3V6xnyTSYDhyCLL+3/dgEAsDZ0MZ8vvwOnG8\r\nI142cycSW6k459nMcqC89d3S5JF9lGN5UGc=\r\n=KMOJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.38": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.38", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "510215b4cfbea132aafd3714ab885d2219664764", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.38.tgz", "fileCount": 8, "integrity": "sha512-j3RU9GV1CDxpUqzJuBCkZiEXi7kpFmtLHKy15P+hxMlItzBlEqD9ZRSv6+QZpouSFljWfuAHB024wEn3DJdiYQ==", "signatures": [{"sig": "MEUCIB/dr3uR6BEX8sO1M2WosOUzBGeST9ahuURoZWK1QM1EAiEAhtsbTPu9Myjgoece+GWqVohzCkKfVG8e+Mc752PRL7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUXQ/9EZgX9IsiLYebYWmg9m6PO4kZa9feH0yF3nFDVnGuILiuveIO\r\ndFfAWZR4jau482WutO4+GtiP5EtT9T0eEqu9GqaKEPpA/SnO11lujyap9cgn\r\nWgVqaGmFOxpPZdCyPHeUuD5gf1nwlHEkOI+UWy1SQx9VvbrnJwf/DrHKGHZr\r\n8cadsPypNo8a51FWnZiO3QfvSpX5leRughSElQ/7ChhshhGyi/gPcgmtIB11\r\nKfhU8463lOzWxhTmppWJTSh5Ghw+zbmCCtjYOTD/poNFUP3rgCFoRt+HwRz9\r\niINIs1PVXFXroyfif98PhCiH7gPujtLB5nyEoi9wPsA0cJ3MLTcWCGqHrg4/\r\n5hx94UV0UZr2deY4oQ5++J9DB79ogmJfUn6t68DPkeslznbQAC3MIJGEW4ph\r\n0j/c7CNSwETE6qucbx8MX0zz1CWK1YyaAoUPqW2rANMi3T+jz+w45YaCfBWm\r\npIotSrISosuiGbUuVTwHZabqjg7X8FojT3723nDL6cmK+AqQla/stUb41X4l\r\np+ZpDM8G/C1FMre/neTWhDmzjzhF1DakDKQX7JHeTOZeqfWTW7ryRm/tYpHh\r\noiU7f9ZH5AdCejotidmmqR1Fek9gLjXz0kPXjGfA/Y+kO2yDeEXbxwPFMFrs\r\n5p8lZlrGIfFbTEWRrV/GyGVUXiV1zzA9T48=\r\n=ja1w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.39": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.39", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1bb0e023c94cffea8aac4302fcad0d4fa16db9b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.39.tgz", "fileCount": 8, "integrity": "sha512-50f5I8nMpAyPtysKMYGBHiZ2SNdE091J1ftuUAIymKQ5NuU2iRt4c7A8Ax/Cj5i934Gg70JBFgOGc0OO2RSi1g==", "signatures": [{"sig": "MEUCICibL7spdK6+/3DeOz4mH/suVeg/R7oCSuLSNSbm316DAiEAnynBs7bYGV71KVo9upFwRdD5Id2VYr+qgu0z2muj8qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNjQ/9Hc+RrVLjf/EF9XsiG9CV7DVjrs+LaZPaWZCX5ck50/vu1GlY\r\nzsCP+1JJGZ5SPwlDE7L+O44YAyyI4SNZuXjY9P8nsOctZjTaWANTfl5+FJ7i\r\nDLLomNxuanqzrYMFfYCSe4dLdNukMSaBAsnm+w0CcTWGsKfQRJqbaTa0WywQ\r\nDU/X8G8ZDVaNOT519sFsIcUPHX3lYwg5ZXQdY23L5q5+VZzzVIssz07+JSgb\r\nKXfeQ0crWcZW4MhZeJsENAxkgxP/2n80ogZxY0yHFpxHdcJHzCrcLCgAvS4I\r\n5V4HppnpjfOQPcQ3iAhXyDx+Quz8zF+mTRWbqzsevTSlTM7XIJ0b9cdIE52j\r\nmbgimaqV429tXQwMa8YzuPyCLgodW1iYDTMCkZHLqN8LrUAMud0AV/52Nt+9\r\nYd+cueq0T4TguEOvWvbz6ERUWAj6INzqxjl3IBcBU3k+4ND41hr+NQj1q3XG\r\nQOkXpLAxi5yQxqITrMYgLlXn/KJm3aVxiwAVmVKyOjRNIkSmgKdhYraU7737\r\nnSag9CpPGDU/0YGf0EZv0xg6Ou5+BEDJ2UUvJmQhevM4PlZDBfUirDbqL+e/\r\n4UowPEnSkuTRNK6W6QFpe2hvSqiamUdwZTjqlncypBTRqr+8y2DqFXYb9Ei2\r\ni7k875v1soZ/BLX9Bg7ffM4CoQqkYPEXRr0=\r\n=w/aK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.40": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.40", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "36647d5b9078485c87ae1212fbcbbfed4edcb971", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.40.tgz", "fileCount": 8, "integrity": "sha512-gRh3c9Ua20zTa+QXebcFmZQ0Oj5lib41EYuFE7h/qxLA5mkVcp6DKvgMvNOaHkcfa3MfPBGLZ1zdM1rcEl1Jow==", "signatures": [{"sig": "MEYCIQCifpVFi2sK5cV2tTTwHh2M+uQqjx2MVWJ1xt/NvbuZfgIhAI3ouOsnODm4MjbXDqy6o/WaIF6VfKo+B8NclxD+Gysp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmKBAAnKmji6BWpxtWQh+aeAJ0E9s3kVuxuJsYWXDcG10W0KyZfgv0\r\n1BViT4PYKpEjBxDbcPiN6Vr4XDROrOGT9Vqw9cHgJzzaWSwt0aA/i29+CJoC\r\nb7DpCou+6bNvZmj4/eHMAJRSgv9X1wdtHfjGzhJpOiXO1NKfZyRLYIxbG4R7\r\nDJiTlaNEsd+K7dMaOupmlYKVFfMpZ/TIGOU3mf5Fjvuj7/XTf2CeT7V3kW8+\r\nRpiSEzCDClqXcn3y6ABlRhPEojdWYTqxyCI8mrXd7c+aHs0qYXicoSMe1uTV\r\nF5hFmfmDVtR3SqS6jB5WLwQUUeah2gscdY9l9lnKdew+tAn24k0XbrNoP+cD\r\n8ElDf0QpOSEiFFWrfCR7jAQJtcEzVcPJ+2YrmPGCuxmLE+QRTEkaIECF7QRh\r\n72D6vFpdRVUCUbMc+GR2ZF2TC1yXM3Cp/N3r8ACWmo+zCnKQZgOumNe12HOI\r\nxLqyDRg+cT5xhDgwT5VfbTq2k+r5SFcY0xoKBA9/gioGwcVUrR5u2Oc/ytvs\r\nKxecgrXPMK/bK5gACYbSoHsztdsGSV7xNMWmMwQ+dgePPO8WFjjXh4At3Dtn\r\nu2U+fG1be0+QgXmTZJ6GdRRkDSVc3FjL1cKhtWI0AZBCXb/8/71UX6n/vtiU\r\nMQ/kdl5SNtfVGhUiO7X2UN5QykS9hcmjnFI=\r\n=pHax\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.41": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.41", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "159277762ba8e76dd694cd55670ef3b1ae2739b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.41.tgz", "fileCount": 8, "integrity": "sha512-5lq/1MakHY3FhZatzGhl+9L3dA3Ht3XlZIIkQzy/c5duj0qOrcam5gVr90O22G5GGCmCmsYj8dV6pYM8or8zdQ==", "signatures": [{"sig": "MEUCIGizz4v6+wI43pLHpIsbnXEDAOJqIhJATXuNngmhLoiaAiEAlBsCiOZDp4PzVZmRIiBFtauEZ/FaFwXax/MHRvlPsMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZ8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVOA//WvpRbPnpPlKmIBz0YXsL3+VFB9q6YNifLeIzXCFTxzS1LUmI\r\n+7wLV+4avvq7UD59UF1bf/nPF3i54uImxUj13PKifIGXxGu1Wm4uW2l8uRi6\r\nE+1A/l/nLmlRqsKM6VlxMnr5+3o9ShMU/m6Ki/FAeLoMHjLbB+Wx3AKBZTbP\r\n1rqU67TZcf/Slrrm58qEREm4e9kY2aZGzVaqwUA3YVsAexGiI/xIIFzTXT7C\r\n4eTv7tQE4z44sPe23Rdyn2JADD+Qh5GvUnc32zQMzU8/KoQtvdvS9tBE4vkl\r\nuDwreRn93fc+Lu6pKrNGdJOOokXAbwvt0+W5ZOHuv5UWDAnYbYjn4GWLluiQ\r\n4QcR4qKEbZU05i3EW+omw60Z/joNxokYqVlLijFfCPega/uginMRUsyj1e+S\r\nXeFLa2mS+v49kUnk364GsrF+syWHBsdzrG5N0SpwmOniQelCUKcU3GwcZHic\r\njFIPNCvIVgeGP2aMlrXBCGO8giIOEx8rHHytZJ05gVrMzb18/4aU7aXjx70M\r\nqNmIL9+BHEKxoJOn1JY+T/BQL1gBliHngwMzrk1/eJvHyy04wbtr24uQ7alj\r\noOk5lvBpC4YdYvM13pFHGv1TOk8gpB/kxGMQwsglozDGyiXu6zj+DA4FrGIN\r\nHamw0OoUYAGCUiwWWlaqZMQ2HmhYiIeu8dk=\r\n=TGxI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.42": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.42", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "38025a831902e275390df1419047c9f5dac1f22d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.42.tgz", "fileCount": 8, "integrity": "sha512-eT2eoSDz5QkIz7LC9uQNSMJqaECu1wpijnTZokmqgj9bQM06F+deN+N+rRf8GRw6tfbjCDkzyj+gjQ+v8e0vFw==", "signatures": [{"sig": "MEUCIDGBSm6NrAW0OG7rnHv1yuby9sRvoI1kgEqE/txqJkE8AiEA650jqBXa/GzlH/yST+fONvsxASoCT0+DOupxOcT/B5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOGg//Q3NuD84+W6/yyk92S8GCdnHm2sVep4g7HSOPWcwOnioBpfnU\r\nyspSyvlV7/aLM7gPR1DgYKt4kD5aDbqWgdew61ipIp9ZjIkWM1SdCpaBPsbq\r\nWv/+nZinu51UtUkfav/EgpalrKel3gV6f+IjFB5g904QpT9x28/J+OaYHp+R\r\ni7VOCbHjsKPi+FnpaAl2uGFpqqawH6eV3XhCvQ2WGSac31X9AFMNh9rfc5Y0\r\nQY3+t50+MLJPzn53CdPdg5+IjBqWMMhPSpLdOsTyB50spk6cLJ2CRlu+c2hE\r\nttVLojAyGf+fW64NqV+Ssl8pFudyctCRrbPS2Iq7q7IZ4fY7CHkZ7IAt9553\r\nPvhOxUNah2QPEC8SmYSns9nNcO3I/BjOn2pK9RXREsbQxFwBQllbDkDM0eKY\r\nzuSVyz0g5/Ru530+IxN/SnYuNIQre4EcQ8ZKXGALkVDW5DbKGZdXka4v3f+6\r\nSsSulChkcn4mJ7bd1BoIfLe+YlWf7fnxvVEDTwLdTtAebGZt/3NXV1qTZz8S\r\n+KHAGMrls5lAgXH0O6UDG+beqcfvL0kFrjRkIs0EMupKXWNb49B5o/TamJs3\r\nX5OUxDBesUWUNVHCv0m8sHbhzPhrjL6KQI5DxFR0RkoTRDamkHvJxdFVdZcl\r\nLuVQhAHoPxBTaVlgz+lHnfBnyBAbf3+mZJ0=\r\n=VddA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.43": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.43", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7caa7528563fe3f105e1a958102004963578915e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.43.tgz", "fileCount": 8, "integrity": "sha512-JYSIo0iVjjMM4gRD/oIKtqJzXf/xEkDb5qNxEDldbmV6KiQP2vXx4q92zA0DVQpPil25YFR4DMyJFvaRRq9vVw==", "signatures": [{"sig": "MEQCICVY6uAcGz/8PH+nPaZVNOY0avp6Roe+QX8ohqILVscxAiB6o1agG+n84Ef6g6gR0dpTi6SxugrrrU3ILwq4nArLuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvs9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyQhAAlmdYyVdSaCERbrNcJUnwGn/sTuqid3vCtGZ7zUjz3p7xhlNM\r\nBixNJZj692ftmaj0BZYjVNZfmlYqPTBpTuLu143s0jEY3t9I2oA7C7zNU4B5\r\nQwWi9lPwn0ll4z5fH8iQdsNP3ReusKFG4hUT0c6/dfPPW2GBPoccEXbwG/es\r\nuLTe6lJvkFc3/kraRJN58KfaJjSklugRO4pgJWgWch7KumpWL9mzlGuAiiWc\r\nvLMW4A3tn0w2qem8mkVCicPYC3hLHy1AbSh+Ibz16MouZZLriFHkgcIGdzN6\r\ng5eI++MM8me0leF1FSJWMpoTOoEkKTUoBIflcrdbe8fw7hS4Zq0+SDAOSV2p\r\njBBw3sYhWVJCABLm1ygcSbdCz7ZLVjcXU3WjtVMzrmxxWCIqCt/UuLF6/Yyx\r\nyKKpZCyJwruf6beqeLKfALIWoJucoLePZeLKOHmTEm7FKQJtxBZDt/H6a7GD\r\nzO/SwxKfOTKWX3NhW6Yt2sHcq/7wrYvO+FqEJGzfp7F3VU2/KP3dLNQItshD\r\nQ85Cq4GUwPObRvV99ksu4tlX2XGdNn4H978KfSolYpG6f20e6fBTAa5VdAPf\r\nG9n8n7ZuRW//UDnqbwbZBDDDVUzIVVBJTcqg4QCmOZipbIkzCUhTEUjexwjI\r\n2FFSk+S24nH0EeRtiycFdxGPg7yDivPyxTo=\r\n=ndZ+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.44": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.44", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84c6ef2ce4241cbe8b4f7918db93f3c0de6a090e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.44.tgz", "fileCount": 8, "integrity": "sha512-lf+8154Cg4E7OlJ+vSAajfBnrm/CcW4Q6u3eKttg0HPOM3oNJNDz6urBTUYrNzUWuzu31oErYutMWiIP4RrnJA==", "signatures": [{"sig": "MEQCICT6foSUxSpL1IhXqU9EoNf2njrRlpqs6ybE+/WUkESrAiAGHdCGFoLvhPfp0h2aTbZBL16pu7Vr/JMthe5WmSdUrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/YBAAkeCOmFnnHPN2gAEWdQfB/uWwH9CnR3C7UFpasP+ugOeYOYex\r\nKndMZsEurXUmekmOL7Et5H2MO/8t+C1Rxo9Kq5yDrp1DMJmo6mgTcr6I26El\r\nJ7GgJXpWhJBWsoR48tKSlR26Xq06PWJzFueXwUl3NaSZioHeZ+/+ZohQMa+j\r\n2EsQBNQ6Df7cQDFd7yAoBecssUNOFOzutIlSq65FZNDnGDG9h50XJk2mSJZZ\r\nvG542snnUa8hep61vW9XiW7qMOClu/zcLsdN484Q/Of8GyJYBHTN3/lzp3Q9\r\nCyjc5I0KGiPltpHMBZjz4UAiUzyew6ZvQ56/bVymrNmrDMG+4EiOn+XuYitL\r\nODmG2uC47H3QMJ0xbprSz5wbVN4WbfTOnhPbinCZHU6a3bhByitX5TfvgMFo\r\nu3UVjwwpqZsDBDazZD+z8zK62YUwmnvy82kmF+gwspxp5SOMWdLyAMsH0FUn\r\nu94als+Xr4jr6Z0PkkaiBGQh0cNjWfb+eBzOmMF43ogyanl7h1VCyOnw0HUU\r\nsvEpDZQpV6YUU1PlVZOCSOzLWs3ANqZPBt3t3pr+uoIdwu5n/QI2a53uhQdb\r\na4AG0X5mV6ihOsYCwJR1sfQ5yhrmgM6Ofu0Y4pmrpxyq892ErATp/akA7d0O\r\nTyKkOY6TOWZtSRXPorOt4I3xisesxpkmZfQ=\r\n=SIT+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.45": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.45", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ecd069dd512a51dccd30962029beab14d289fb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.45.tgz", "fileCount": 8, "integrity": "sha512-oFJpAZs3isquRn4o80HGqx9//X+ZVL3bzFGz6sxxcrHpVS5epqBzYSypjAftqVqhkHqeo1boqD0njJXHFb3l6g==", "signatures": [{"sig": "MEUCIQCkkRuF4bIeilR2Dh8jK894MedxUAej8yCiz+xJv5RPpwIgXaIsqRNn44lb1cQtSS4/7m+wbvDVxEx0tz53kqKvjCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wW5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJUQ//ea+7JoIqJmwymlfojhsWoXfgLXd0/OiveJuS9/FJMNXF/nrp\r\nFi9C8wHsjSjFcXAFZwmTvjE0SxZydifKRJ+4kCQiFx+mxjS4hEf0Drr9u5FD\r\nLN4FCD15H2cof6hZ7NmtxFV6AbIg95wAswOtjLM/HY+6RHLHuaC5SvK641/b\r\n3rIVFRsoQP7bk9biTVqiRQhvhaeiS/gguKnrfpzwwDq0rM4QubZ8aukCRYYW\r\nvxMKv2+H26iNebYwI65aHakGmdrZg8DE9HBf2eVColHyKRIyYXkIkFPFEWCr\r\n+LB+8Qpm4CtEDIcDYE4Mm4IchEIYheS9dvr2yLVnaJx8iu+vHswemfGsVB0P\r\n/U4oOVmOLiJ0jFvQlJLt/NrBnO8AziHkr74IA/gQERhBeM5IfKQSvSn/cn/m\r\nvLntlrwFLJDlHqhyLsHevFuGN1a5pivCjmxtX5tpto/I2xccCxwAuM9puBrY\r\nR5FA4oGKS1PM/njxr93oMjTofMAnqRiitE6M/gxn8rOZ3RFYMOb565GLzUWF\r\n3bRjP/CJyDyGJcy4wJeGh/csETsDA9CGFUflQaYMMEpWzTp9Q/XldDLuL9U+\r\ncKuoM6Clfy9Ij9BbazCGgbEs0W3+DUNu8lKfl8Y3ZtmfZiJNiHqAqbcOU/z+\r\nrweW42ZIPdNJIICddRV1SYQXQy9B2BnKuhw=\r\n=YrpL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.46": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.46", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1989ef49af5bd4cd2810e9c27520f495f64cca5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.46.tgz", "fileCount": 8, "integrity": "sha512-zM3WvZlkhPFqAllwMPgRxUYQPZvmsXfDsHkuEQKpfqzQ8+OEP7Wl7dBfiU0rLpYi6Mj/doKayTVV+mZlK0r/lw==", "signatures": [{"sig": "MEQCIBuqpjbZb8qdJCeoYfVsY516oqH9TZB6eOjhMSHvz6ZCAiB6PGjyV2fI5Mi/4Pkf+ouLKhkF+6h09IqehY8AoXPZVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLvRAAjeoCoUU1ujGcjaW73wlaNp/I8uVCtKcwWpEvYN9gH0e/E6wq\r\nNKG7AmRLL+o3ih1dFXHU/IyqKvV7/RfEosthGVqeQT9blEFgLsAj3nVwpldi\r\nQ/fExM92PZiaFVF5A8c49KI1XgIaOETem77suDO/JZzCUMSJQmXT1zV4Z8cZ\r\nJta/m6Gth9EDc4uO5Tlpg9izQXllguBj4prNRDBKoJcM/7GHmWFTjh6fyL4e\r\nWYkUJjYueMi37zJplBq9/soJ+ZmGqMaExd7DFEc9rxY3GGFhANy0OOJovJiv\r\nlledfm75CczoHo7BwQdeNDEngkfSu/+pvHCL0ukCR8xEv7abfCcjjUvxcVfy\r\nFw6N9H0Yh7vfniSWUmPsBPeXDuJRKTI4HWentu/QHqd+zrjsYM2X4vjjsWQ2\r\nylFiYoQOvarSM1Eypwa6V2AmUZjEWKylPatBnCmznMztkJfGrQPGlvmgD0Hl\r\nVRC2gXf6k2j8RdPhvQkQA/4em8QUFLhznwFhotTPqzgUI/VaZdS8Oid6Hs8/\r\naw62MCnKo8vraq612NvNGeLGBp8TPbPdyhYA6XOzU2W+q8bAmZcUCF7wWvAs\r\nGh++3PJK7VT8jRfj2nVEJlpMu+iha4njtiH1BszgCpwLkZ4M5eD4gXhn7ZNn\r\npAb/OO2/QDbPZ/q1OxaMmB06jOjmXf++mJk=\r\n=/r8t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.47": {"name": "@radix-ui/react-use-previous", "version": "0.1.2-rc.47", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d30fdcf9cc602bb665cc7232018888462495da59", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.1.2-rc.47.tgz", "fileCount": 8, "integrity": "sha512-Klx8H+xHJB5rpqn5gxAuRiEf/XvGM2bkL6+WDgKszyGDYjfeqFsadjHDV54OT8yc5kaJYaoL1g7t+ilJOP6ajg==", "signatures": [{"sig": "MEYCIQDn2maujWJKnqRxU6AIwONrwuV/MQP5fOzi5OpLn7vu3wIhAIkpCGQBBRtJ6IJ46WsWFERYdJxsp4aDkUP/5eTNoJnS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFCBAAg6ue4LOXeD/4Fbf8bBLMkrvvGElbb7DSu5FzbO8IF0xNFS4f\r\nJ761mwNmV/FCf1W3m309okxQA9sNwIRG0AY3FCLOzRiPLgBQL3Xs3lUBOHIc\r\nbS577P2bzdBedav/EhLxQJm6Jdbjo2TnpKWoNDCdZ0HnOwX3FBnW34kLwvAX\r\n6ECfEHmEvjWbLLpDOj8qSMWYvP8qO666e6i07Ayq2OFlbJQjHSyKoyokuJNF\r\nfaoNb/a/K7FZvlqasGnbhli+HFuvW45X/ogPzB/I1Nr3y8kIOIZ4NxMh4Suu\r\n7XTi7J91hSJDJsyxIUyAABNiDgHEiezFG2xhsvHCTUpGhzQw4Mzq4mkP1BmQ\r\nrz79cuZPeuAKi1FB0kIphoC0R4K4kt5SXo2PgFcARuhEUvUTrqw+oMyqCKpy\r\nhZRUPe1MBu9LfT2UZ1XDYKtgQq4dQI1qsLFc+hCMYfS+YISaDK0mfS4LM+nP\r\n3MkNmiLRt6o/2IlZPo48WUXV3IWtC38wL5+Bb2E5o/MyQrSN4vUxgTHN/8bN\r\n77X6Pd4A4MlzkDOiXWh2u5YEZ/1aI3SWJ/RmKbNAYpiTyhesFzSG5H/gwLFj\r\nOnN9a3MvemRikiXZVEVhfqiqqqTMerXnjqg5Dh4AHeBtQHxPXvvjJBaGX6AG\r\npJB0EOGTkWYvzcaWIU8yuYeC2AAUn1RN+x8=\r\n=qH6Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-use-previous", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13f9f90791e73f569a7e9c78295b049f5671ad59", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-TQZIkeTxjcDiH66vX22pkWn+76smNzy4Tnx9BfGpFN6oxG9V0vXvrOJwkJD/96Icm1ElTzvamFOB9u++Sixz1w==", "signatures": [{"sig": "MEUCIHhJDVI0uQ/hop5Rk1Z2kZFGT9EpqC9xwsaXK6NMTAqmAiEA8m/HMdkqgOLh1jY/1KqM0hWMOsKCAN+jopA7FlT0+Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EwFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlwRAAl0wOQdH//2YTH5ucebCUvU4dzldBhKhpVdJtG0tn6vLXY4VF\r\nFbqCmBUaP2gKNfuEd1vgOIvGv1XBo5NRNpIhuAQO4aAAlFGWVg2vgJ2llr0C\r\nI5DUOIEVkU3+Glp+gU6/HHXmsU0BpkdSZ+Cu4buFOYQT4oDTESsS026w/cH8\r\n5bmtgvA9mEMUuJRrJha02kWyGp9zfCNQgUxykzqj2lrxsGJxllDOuELYiOJX\r\nu9oMmJgbolyBzC1tA2R8/oAOQP9fHi8f/6Jhc7/wzFGpK4ohNiIqL7gl/fvO\r\nVDB+7z60xUn97cVzxIiofGUKxNdIOLgoe6G8GVGZkJPiwgGpVYZA/EnuA8AU\r\nRX7Ov+VCsc1IIsOV8rV0UWJZU9/xHcd2Z6rsZd+GFPOr7D7z6PvixaWEYFgK\r\nFH8U7r0YdVT9SD39bACHskBGM60OCsZ+R/CYZH52ocV1m5HCu9HzCThUt7zV\r\nDDdEZbEpq1IwFOe2PO6a0SA2mqUnrs3SAgGOtOg9gKc+ROd+m7uYQgKePoRd\r\nEYSPA8NmebcGwmMtaUC1mJyA/ytcbTALJ2uOCJGu0VbByq1W3h70KA5v8O9W\r\n579+1jO6Qpkzf4Aw0utnFm78FEuAYKPguJjoSfjcjFSWdRDHD1VMNTxIPjdU\r\nUE7SSZBPZFNH40boJGpFJy2cOlV2XzzbtiY=\r\n=kxrL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-use-previous", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e48a69c3a7d8078a967084038df66d0d181c56ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-RG2K8z/K7InnOKpq6YLDmT49HGjNmrK+fr82UCVKT2sW0GYfVnYp4wZWBooT/EYfQ5faA9uIjvsuMMhH61rheg==", "signatures": [{"sig": "MEYCIQDRtUL+L19Cwkrj5OqEAcCPrmPRVQLG2jWwFt4WYxU11QIhAMqQZpr4kAn7AWFm/y/BdR50yYeidYSTofW5gPZls21j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosOA/+KNpLd0b+YZ207hvXGLJTkVXvZIH7hYdrvCYZh/eZuMdLsz70\r\n//auqQmet4BkL3YBp3IiljLYl7FfBXnKjklJJQxgeZR/xSQ3i3i8XPrtNOFm\r\npC/IBBK2CiRsD7/b0hjhovNtMEhPD4dSlbIocE3lt8Ze1+0TywPdFckdy69/\r\nwQhHPIPfNazGxtxJIPpBh0l6U2lkE6Hpx2AmGUyangatOMWsIWa089LX7ovL\r\nFwZwUpeBlKYY3xbwcTxtDaHd6m6eydnba6ZPjlJ7sc3W3U5gUOf3HX7AgVnn\r\nGt6HltEjkq+MH8EavsFo/eb3KCgAuKe8Ynw5peszya7jAXmDi70q2Hr7vIIg\r\nWTYu7B4gCCOFTd97Z9IF16MFYz/eJU87tncBw00dScnrBaofRqixdI+hKYK7\r\noWa+3bUmppKhu2slVN+bYFStATU1nGDwfYPpPaG/EPiWBNXaanVLDJcoPtEZ\r\nuAMc8YaCuxB8X8Y6teQQYLPlWAfPtdB1CmMdRYncJVB6DHlK1/bwZYU1sCnH\r\nq2GeqSRA/FJMMOcSTu+NW7LU8jmgg18u6CfvZMh0qVMl/qXnz8LmyeXkQR7c\r\nk6o68jELBQy+/OyK/p3b0lhmv3TW+LmfZ9Aqewcnhwcu1+O6UaOJuFwOxTJP\r\nguMRWm6k4/JL5dpuCTrkPYXaXbeGlcBBOwc=\r\n=isgu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e686ead69bfc504a7a62e3194c3649b0462a3c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-IXlawgy3hsFafU7HUzb5JASI6Hc9sv/Ut0Jk4NhOTxOFvKl1O/r/Q0PeJKiuI1yUioAkjNM0Q1VEs64iaTQPtg==", "signatures": [{"sig": "MEQCIFHPD7Wwq8bd2l4BUIAp/xxn2D0J5PChXLGsz1fazXnxAiABH5/MnR/jEoQhrgUIDGq3BscZyaBokQL+2YNv7ra4fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6047}}, "1.0.1-rc.2": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9baa7a2c2607d25ebc8a2c55092c9e9f8f03575", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-RQmd1E4+LT2xRx5BtLdbUUj6sNdNEzt6czRoBdPW+e8LqGHzSp13jHyuoUrVVI90jmsWbPCnSw26EoRwpBP4Ig==", "signatures": [{"sig": "MEQCIFXD+/eP871I1zHPEOytMVxHJ5R5UDQsnCi5r/o3j+N9AiBS3Ewe/dvA5eGbmu9fp4/MBlWAYSsnTH+iUjv6azPO2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6047}}, "1.0.1-rc.3": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "cccfe498d56003bf832378cca5cff1428677e594", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-KbehX7nUd8z9PSaeOnpgaDyJUgyvScEeoEV0EyepAT1IfWW9qaBexgine6F4IzwdrJTiDlC9mZJQ7Yod6//XAw==", "signatures": [{"sig": "MEUCIQCVTPPC959rggYHRWhO/lyqYS3by73n8ctDZS6xZxXBEgIgU52DotG1W3xG4lQmA3qe2n7FYQmNzK0Ov1jmolbRSO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6156}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "cfd4b8c6030056c07c8245d8a20c636416919e3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-wrxLo/Vy4qrFSJ/CoyLgavlYGgU1epV6TOitVSL1Y/C4VB/FGvenAeJEolvygd99HNHXjCAB05qxNLmlrnow0A==", "signatures": [{"sig": "MEUCIQC9S6OEH+9hbTHjh6bU/u+6ogqr/sWyqyQZD1AtNh9GQAIgfes+KAxkhQo4wSO7XCnX9fUhTYqPsIfdKAtHiJSsSxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6156}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "301b515aa90a138c2c517abcc6deaf47df97d04d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-SA+82D+2bywtKzhBCVWG9ZnEbrJjWSjI/qaz7VIJTGcOnhL5bUsnRGphnkc3bZvPxe81RSeZ/Jgiblr5a50qhg==", "signatures": [{"sig": "MEUCIDp/lnlNMYGMeOUpNlIC7nfwkN4C7O5xWsTUqMzLqpA/AiEA1YLxW9rsLASaGSDVyfhPHfMKGnLL1tblEcy2k1G8Zao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6156}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-use-previous", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "d39784afb5b9fcdd31a3ab67d7d9d80d07e7a164", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-97lgwbqmP7qrWcjmkPyuswQmrRWol6MjQCxz6c0495JEbdpW9cZm0sddKgy2ng+bi0oH9LSBx6g9joUuNxe26g==", "signatures": [{"sig": "MEYCIQCewMQEgYvoByUtApQt42WnzDVIDatYo5z//3/atbeLxgIhAIvvV61mBjiD8qzqKIoiwGekTMjNdPBMBOwzT8WMJJYv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6156}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-use-previous", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "b595c087b07317a4f143696c6a01de43b0d0ec66", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-cV5La9DPwiQ7S0gf/0qiD6YgNqM5Fk97Kdrlc5yBcrF3jyEZQwm7vYFqMo4IfeHgJXsRaMvLABFtd0OVEmZhDw==", "signatures": [{"sig": "MEUCIDGBl+at9pEcbCYntU8Cnq70Vxx8Jd1pvw/WFFHsJwtkAiEAlq/T2vmgYKQitzm/zvHRyHA3hrGicuQpQ8cgSGo3amI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6123}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "fb266e9b358966142a9c6f0c092e16e8159b3d84", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WkdsBlrV6Fcyan6AvHs5uArVQngqX3YycsA1DCze+1kfLnuZVtyp5DdrFJOTXXFfC6E2iFZm3m8qI37dxMlSlw==", "signatures": [{"sig": "MEUCIBrTQJePRY0BVfmi7w0MJ3thxdXuYdKkOzVwWd53VxVRAiEAyEhYBypY68pPiV2G9doWdKqbDfzfppR40Lp8VhyyTiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5748}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "c9f92ae4861de6482ac54c79065874da53483a41", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FjDhRFfuTmubwmhXjV8dR/+tRWUmStxac9yMhj6ttAGZKRY+NuTWcGPrk5NuAdc0rIoKgf3aTerLniLkp1RTNQ==", "signatures": [{"sig": "MEUCIQD1Bj+XVD8VP0UR7PGp7P+shSMU1as/KxzdJMyYDdp+/AIgJSK+/+0ZXvQPYrmOoTb41P91KVM7ZoEPU4FH8lcEUN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5748}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "aa6d3252bc671461430c97ba001a48bdf91b0dff", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-2bUfwAlpJs/f0jahkPSYQHWiyV8/RVi/HWwVT1oODF3+CMkgSikKX55lVVhg4jLDJuZk6bMAijCxDuEXynyHEw==", "signatures": [{"sig": "MEQCIACaCtGZdN9xvDAMYN/0ClHjDOmoZbvW0MBDNMQOrAYFAiB1j9AWz9ERCghCnTLfVI6r1WOyLz1xj+OlvcmbhhtAmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5767}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.4", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "949214ce900de82bc4752cab3fea0577bf501da6", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-V0xMzLRHOW39C5bJCSu9loH/1DCaqLwpvVDz1DUtDofeJQ7HtY6xbiMrgbs+ymgedGXLjbCO7+ydGR/sQHwv3Q==", "signatures": [{"sig": "MEYCIQCkoKv7XaUeitiXp2GKHjGXZ/RifmOxSmFGiH+SvtBDcQIhALhLBCQfJiUsv3JeWlpnR9ZYYTMKIDZL6t9y7jJ5Jy/T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5776}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.5", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "516165b47efc58336b8cabe3c224f08f9e16a33c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-L34Btv/YPZ2KSaQ2oFpwyYkcy+sr9Rk0gu/ZRW1VM+f3AcN4d0QHrDBE5TCmw7gkY2n9zpIsvkmkpnFu9ZYtew==", "signatures": [{"sig": "MEQCIB1Q7+JvnjHchr4Uocix8nLRmHUy/9nK24K681HDzNJBAiA54jEmmTogblPL5odWbaaNmnZbcpkpJ1PVv7x4I2s1uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5776}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.6", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "6307ad8eebc355837b2dcebde97cfe61586b6957", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-BP1tcZevBEAdK1pTaMAzBI1cbIA3sNVDLo+QbM9iWXfoPCGRNEiJu6DztcEdYWBX3OJiPH9a7qClUOn7FLlI9g==", "signatures": [{"sig": "MEUCIQCeE8UCObGpFfi3YdQ2pGyRNQh+gxD0mv1eHuEzX+0xegIgVKc1CNh/s5MvwR9JfSVZOhgttt45PYGLyppyfEW6irM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5776}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-use-previous", "version": "1.1.0-rc.7", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "d00b9f7b7c475a6f991b65f44bd872bdc190f1b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-9eu2wVEcflAcCWnyY7bDH1Up1a5zC7mxhnpMSMFSlDc+2tN4TtYlFE752k71V84SRFODWyM/Z5aAdCLtaChohQ==", "signatures": [{"sig": "MEUCIQD0IGP3FMuO4HWtbvHKSNK98TEEsS27KVHjp+gXBCqOWgIgGj7zi3TinxCoMy64iVTt098jj325X9yV20G12wMl5SY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5790}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-use-previous", "version": "1.1.0", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "d4dd37b05520f1d996a384eb469320c2ada8377c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-Z/e78qg2YFnnXcW88A4JmTtm4ADckLno6F7OXotmkQfeuCVaKuYzqAATPhVzl3delXE7CxIV8shofPn3jPc5Og==", "signatures": [{"sig": "MEQCIDeE26mxvMwtbNjNleuL5/nuB1RBVmKKoVTeEjAWfmF0AiBJlo023CK1K0XTzpSkKvV0aMNptd0w3qyazXPVnTfYIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5757}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-use-previous", "version": "0.0.0-20250116175529", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "a9b9d9510edb3e47bc94537bc948f5ea3e2bb0cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-5QzyaA7qip/JAZY6TiZJgpZMd8RSnY6yKMw5ATkEb5nd+22go1UyvFxVTSl1jFwe9zZqjPNYxYaCazX1nOGcUQ==", "signatures": [{"sig": "MEUCIQD5rPsFt1TRsiygGlJt/MuU8i5Hesz6ISbXNXoCJjXk6wIgZVKbZZUJsrGMT92bXHlKA544ijDkF9hc3i1Y44zRK70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5742}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.1", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "13d7f4f95ba5c9bf371cdcad360f482373a9a613", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-f0vQi87zcBtl+ahfddOktN2KJg1wU1fyYdfO4YBQKYOMqczs/E3DeWmXSupwFk6HCOsrYKXXIssA6lwhSFNrmw==", "signatures": [{"sig": "MEQCIBuNh1REmBiCPjXQOxSrYfdxxzr3xUu7Psmf5PtQnxhUAiBOtdJYUjJUL2wQflkwNpw0hz5Pgh6awdMugK0ILUOm4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.2", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "94ed19dbc876ca81f94b8ea78956bc0ed596eed2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-WSZTesg8KP4CaduYV5xyb+96Km7RqYwyeTScTleXCSNDYRTKOuUkMZ+g5B55FSvbco/LeKcFG+2DmIFki2SZmw==", "signatures": [{"sig": "MEQCIH4zZ8i47SRdjzQHbgL95SU7fCdHuj2Nhzf3ewCpb7BbAiBTh31b0XLj2pI6tcxB/cMQ1ARLJSadpIR+sSjEMIcYQA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.3", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "1c8bb905098946d51afbd9d3bb02e9a5073b2aef", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xiTJhc4z+KmT1j/qCOH043e2KcQSgWurIF+zPSdfxsAs9mCih4szIqcHAU4RJ4ls1DBWJDGoLlloVorK4pjLUw==", "signatures": [{"sig": "MEYCIQCfWf18fSL1cCC7J9VZCX6qb3SjnwNvxK0w87ee9CqsqQIhAOjFzdLGCwCVvEjcbw9zPgDiwV+fq/wOcH/i0v2+vfhG", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.4", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "72294767559dcd6ba9ac8cab42675c38fc30a7fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-gk5t+WN6lIwygVwAEm9zYD6mK8AembO77fclLEH+HXIHrFmQJnjcE9AFSvQ+qVGRfl90n4SyujwSMIzfBOAtkA==", "signatures": [{"sig": "MEUCIQDjqygAUk5fdc5aSuDPTS9GJyA7LqPPM96k2zGYnq8F/gIgbnchv9ak7XRlxXbjH7RuBO6KFI4/EZoRoWi3u+Hwz/4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.5", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "5ac936ce71e8259b1203051c710744eb0640ac5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-SM2RANr1ONd8I39kSSxFXGnoXoybMhmwyhXKBW0nyXtrnfwVotFjhdb7TUpXpgRpJuRksERBdLaAQSu19BeSWA==", "signatures": [{"sig": "MEUCIQD7p7KBgGQ/L0GpjM4c6LSEBZLxqHrm+22mVtCiwvhG0QIgczjf+x0xYwixLJHUFFrCOKe9Rdn41ZqTXxujjT0noCM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.6", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "3540bd55b0b5e73c07a6f9f8911e95984dc126a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-bTqjiJa1STFT7oBGPrq2MTl+0pXsRo2kLcStweBlJlVLiNH3h+sytfdKd8Ejlf75m111BzfxQLghsvoZbd0BSA==", "signatures": [{"sig": "MEUCID5e+q6zBjHLDHGkqtld00dz9B1ppJ/BewttbaSziD+iAiEAsNEMOJq846xxse9sc7jlaBrpPpX7srxcO/ybvNLi72w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.7", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "0471b8ae540400a24d55390748452a072ff85fcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-oI59i73XALE6+GR6aA6q1YdZmvins/QJOK/sACZh+7bFsexxouflcODMUUd8mVkenwGJgtclxGedGHb1OucihQ==", "signatures": [{"sig": "MEUCIQCO3iYuae3TNVWAqquGnijalxdxC+iq/ymGXT/TgyX5sAIgYZM4ItMuX3IbfP+ZlYbt53SQ9X0pqNFFrst+eInfY9U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6118}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.8", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "849995fa5b976491b7b305c4884f1a06f6ca256a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-egYAZeFFc0dDzk8693h9RAEBJ4wpXBUv1vvD9clXshwyXGdiQaelp3aMLQeS3Py2Wm6Xn+rcfktyLZTqIlXdSw==", "signatures": [{"sig": "MEYCIQCit3lNib7gBzQMDGvr3GullK6WjZMuf9ku/zCgBTu9/wIhAMIx9Tpnv7UboMKlcIbBjCizq634uE9+TWsxVo3ys7UF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6509}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-use-previous", "version": "1.1.1-rc.9", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "0f4cf79cc688e560e15d66e9ea12892fc55d9dbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-sY7fNIHFkuqxrTW9fvHrogIUHczVklxkAfWSTLYriuBWAwk6ASVS/cIyVBUwteb+SHFAk4nO4nbHX8jvMHO1CQ==", "signatures": [{"sig": "MEUCIQDnOmfPama7vFm2ukTyj5yKWNFEoJhbTvgFO2KVTGdk/QIgHYoNYHDrkFkOtJZ7FRm8KLYeUQEADnYKhOlFTkOEO7I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6509}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-use-previous", "version": "1.1.1", "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "1a1ad5568973d24051ed0af687766f6c7cb9b5b5", "integrity": "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ==", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz", "fileCount": 8, "unpackedSize": 6476, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQC+Af7pCMz1j4JRpAxOOpiLRu8CBRTbVAoDjq7zXpb7wAIhAPKsNmUAdIBsYQH+aBN+n6Gbg2Kt9v3AbEs1UgaIc8th"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:19.923Z", "cachedAt": 1747660589404}