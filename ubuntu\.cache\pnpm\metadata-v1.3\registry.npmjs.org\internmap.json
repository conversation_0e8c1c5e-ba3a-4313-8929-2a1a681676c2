{"name": "internmap", "dist-tags": {"latest": "2.0.3"}, "versions": {"1.0.0": {"name": "internmap", "version": "1.0.0", "devDependencies": {"eslint": "^7.18.0", "rollup": "^2.37.1", "rollup-plugin-terser": "^7.0.2", "tape": "^4.13.3", "tape-await": "^0.1.2"}, "dist": {"integrity": "sha512-SdoDWwNOTE2n4JWUsLn4KXZGuZPjPF9yyOGc8bnfWnBQh7BD/l80rzSznKc/r4Y0aQ7z3RTk9X+tV4tHBpu+dA==", "shasum": "3c6bf0944b0eae457698000412108752bbfddb56", "tarball": "https://registry.npmjs.org/internmap/-/internmap-1.0.0.tgz", "fileCount": 6, "unpackedSize": 10965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCePrCRA9TVsSAnZWagAAHVwQAIkClENbTuTqLxF9lskD\n5U7dKGOEMzkrJ0l3nlKURrWIES240W/QT1fhBoDuKUIIJWkMcGrd3tFREOBl\nEWT4+pklMTqJzGXyaoRF0TpzqC02bE/lIk6YPZxVk9JagwQFBw/eQA0QQs2g\naWHJNFB+wF61yEA1Xp5mGnIxzp7zdXhcH0aYg3uo09VchxiuFWLsMypNQC4J\n0jsDZ3EiB7nbXgspj6jVEls8KXAdNCUJmkQMqXU5SqH2eOgyl5mS0+5AZMy/\nwTBGsH4O6OL2d5t1gOHiAKQYojy7xU3NHFaeZVnRDzjGNEOdqlF/MUp2zeE2\n81n3cLSSFzBWsLRhzFI0ViJ3G5uJbw6leR/cC4Sj+cTjtBOgkWsmyp6Wl0DC\nqseLjt0uIC76LhibWTlxOqQsB1c55+hbn/M8bq8nDkmTeyvaG5GpzuI/Mo2N\nWAqmL0lKIVzeEaNaNPHECPqTQ5f9RSefYTY0+sti/++AfDS/yIXkN50zZZh3\nvOoXPtw06n5+AHLO7eMhgqqaEjJHWBVuuOPgsEeDC4H1d+vgjeLCO3ZbS/oO\nhj0fnBmmR5N6JC/argLTVx6OX9UCKIpwfDa27uTc25kNQt1yhCrXdmdil5DS\nC39262PIZ4Fh1R858cGKNlT0SOrxnLdx5OgRhTeuv/3Rh/Cyk7SYWJQLz5QV\nzJYD\r\n=B4/q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9Q9fSicnsIYoosVVyyxJWyMyhRE/2iZQ/0i7OoZuQkAiBcloSSDxkJiGQLwACuMTRoxaLZmSiZfnlQ2ztEv/FHiA=="}]}}, "1.0.1": {"name": "internmap", "version": "1.0.1", "devDependencies": {"eslint": "^7.18.0", "rollup": "^2.37.1", "rollup-plugin-terser": "^7.0.2", "tape": "^4.13.3", "tape-await": "^0.1.2"}, "dist": {"integrity": "sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw==", "shasum": "0017cc8a3b99605f0302f2b198d272e015e5df95", "tarball": "https://registry.npmjs.org/internmap/-/internmap-1.0.1.tgz", "fileCount": 6, "unpackedSize": 10299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSSWMCRA9TVsSAnZWagAAT8sQAJfe7bpni/ymeE1lQP2C\ngT7JHufyDsblp+0FOIH3OZlHlPHwu78RiPbjoLLRB++Qb7hp7F1SXHi0UYVL\n1CCGe3tH8E8fOoH9fAPSdReztIn0Nj0Ll4CRSP5mtfuM3A7hkLIGei2q/6yx\nZsGWgVtMPTGHyQEdOlYDtFCda3ACmSJ8LXzYsL+ZFO/SNBJWhxbbN7ADelCY\nFW57BQAH6ghmWzJdFuKknQXFkNk96OL32pKlELw0/HMF+Rk/YdwW9Pb2dKl9\ngcA61JWbCpSEeqbtELtK8E69e51HNwIeM/Neo7ayP5f8lnMR6cPrR1+Vt/Nq\nYZi0XoPBWraIa0LCGZqMibH4ZXfwxJtpjbUgV8dE09s/cFGu+Z33VzDDV5aU\ncGVeiFZAn2qQNv3JX8//pkkSt2d5OamhPUnbICERdwFWlzJ9/H6oR2PhG9s5\noakaZg2bWnrMWscQ60ck+NnS1AWTtd9k4eNvBHfS65hgsktrZtYqf5MjdcwE\n9Jap15MfjcLPBxSF3RXC4sl5A1yA+Blr1JgvVmduMjEEYfZ/Q/oExU4YDTrW\n9rzuOggSzWeqb/tFvRaeHYnxWf5XtV5VEQVeM9EIT7a7bCqY8/apgJCtltBC\nuUlMvE9Zt7BIDNnYw+HwRcmXAofxDlb9uEW5aPsRHO7UwhPccvzvPB/vJFag\nEE3Q\r\n=lQ19\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDKJJ7ihNkLc6pKu9o1xLq58Lqb6KmVdtVX4DR1o0Kp2AIgXnVwv9CO/idwKtACJzMOWFqkgCb37RlM/yNi+h175i0="}]}}, "2.0.0": {"name": "internmap", "version": "2.0.0", "devDependencies": {"eslint": "^7.18.0", "mocha": "^8.4.0", "rollup": "^2.37.1", "rollup-plugin-terser": "^7.0.2"}, "dist": {"integrity": "sha512-XpWRFi4iwmWoSbMQQPUL7c3Qze/wfEnJxtpMC32awNzo7QC06vNZ6xXz/bLZyHxmMGoiX93eSxXEyTFQB83ukQ==", "shasum": "cc38ce0c33eaf4501c07c990673c07caa782179a", "tarball": "https://registry.npmjs.org/internmap/-/internmap-2.0.0.tgz", "fileCount": 6, "unpackedSize": 10465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguWL5CRA9TVsSAnZWagAA7fAP/1svHx5Olca54GPSItvk\n69DRHcS5tyOmD4gpjqXyPf3Tdn/+Eth8PQg9Tesyx8dyq62jfGGAdAfSi7yN\ntSRkQROK/7U9BPyg2wdFWDyY+mlwFXKmGHE3bVKEqboyjfJLJIOXlp/JNkzX\nYuiJZcZZOluvMTlTwiOwi4neGRy4cM0waBSLhOlWa+t2s0FtvApPs4rRqV0j\ngqKB6EJ9iQeakqLFTJ5Zc9+qgUAg89Wc54/t4laIezFVdI2aZHniDMlyJ03o\nw6GDzE5QHy9DJIGdyyY5L/30R7FZw4CQZfiLvlvOFlC6CFQYD68Gkhxqteor\n54x1ovo6mgNs2c7gWsz0GbIxJAK0zOoIz1H4Qt21tfy4U6McHRYO5brcElRm\nNKSNtIWHXcvHzL6IiIw+/zNaosQlO3p2Gropuv4T+1zfkXb7AcOhytClf4U3\nf1rulE7xy2paFfO0CpvQnIRzORWMy3NGcbzNBsV6Lo6/tst+lE+WLq4F6EsS\n/3dSnM32L6Aa7tk+Enmi7qv3/H86+0LD5kamY8U5Zy985+X1/KpHe3LPOB3y\n8CbuKlZCGnqClc2To9VACJB+MYo+T9l5tn9qjjcu/Su/QKtiOv6IArNHTBqe\nQw6bR/NyXVyHdGouGxVuEeK9pM9qPRxXG1t5z+ldbWM2uuNOsTEgjTN95r1S\nsvv3\r\n=csb/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC9fAtfiV1DZ4aY1rpWSD78VHrukB3kCmYkqi6Mdj68YAIhAPRQoBznyYFcIctgeNS6qqd/IAfN0kcGHhHkGui2F7TC"}]}, "engines": {"node": ">=12"}}, "2.0.1": {"name": "internmap", "version": "2.0.1", "devDependencies": {"eslint": "^7.18.0", "mocha": "^8.4.0", "rollup": "^2.37.1", "rollup-plugin-terser": "^7.0.2"}, "dist": {"integrity": "sha512-Ujwccrj9FkGqjbY3iVoxD1VV+KdZZeENx0rphrtzmRXbFvkFO88L80BL/zeSIguX/7T+y8k04xqtgWgS5vxwxw==", "shasum": "33d0fa016185397549fb1a14ea3dbe5a2949d1cd", "tarball": "https://registry.npmjs.org/internmap/-/internmap-2.0.1.tgz", "fileCount": 6, "unpackedSize": 10442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgu74vCRA9TVsSAnZWagAAQvsQAIVaJnSZSXwT8SunyXQG\nduZXvRjWGuI441aod1OTjaoBrixGDq+vVs+NMPXPMJSPgcRpiTs5GZVk3jW5\nxc6l38wN67G5es840+Eh4XGu4UbQ7Jik87inrug1aX7t+xuT8WNQ3q+aKRlC\nS2lSI7/Syqua4OE/GDq1s7aA6Q5v/6ddDwlBcutcTDxvO57ALtxE+f0SvqNj\nFd9NksiowooZnjwEFdSaN8f56mZWZbCNnUAbNCaZXU01UMqp23If2Nna8N6W\nlQOwMIqeB0WtiLcpe7NA819B0jgR9X9MfD39DVTnsBpVsfFSJ5XKncYxxqIR\n5dmuQSlyGD2pyZ9S8XRtAtfFj0L0QfjfJk4vFBkuqxsOLkRiq+Md1SAImifg\nqXKEGxU6s3vyzBvUfZixBspBwcMrpelM65GVFPxYEEpa1JcYVfrKW0jdW6EL\nTaWsw+c8IRzkaZWDB8MtbzXBUtdjwILaYvROoUuLSGoy4rLqxdSMCi638wSr\n2OukoF9lhYk01+9YdfmXfxJgql4nnYDCcl1of+zFcmjDSFAkiK0MIH9/uY4x\nHP6fAYEv/jg+ZjHy0GoE+iU5yNOaTk5flBq4GC5TsdFqYNTHmUfLW4bnFfyt\nmiU7drtSEoqDm6PlfRzk5vrwQqYMYHHCUJFA90wCgq21kuLtzDPta9mEqi4E\njd3A\r\n=I4sA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpcdZIuw3OwiinD9MFTl1TRd2no8XkELperWFjIcP3JgIhAL27AHB3jxHK35rzIgpmau3gGt4+vxji/+kSppUgsEWO"}]}, "engines": {"node": ">=12"}}, "2.0.2": {"name": "internmap", "version": "2.0.2", "devDependencies": {"eslint": "^7.32.0", "mocha": "^9.1.1", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2"}, "dist": {"integrity": "sha512-6O4dJQZN4+83kg9agi21fbasiAn7V2JRvLv29/YT1Kz8f+ngakB1hMG+AP0mYquLOtjWhNO8CvKhhXT/7Tla/g==", "shasum": "3efa1165209cc56133df1400df9c34a73e0dad93", "tarball": "https://registry.npmjs.org/internmap/-/internmap-2.0.2.tgz", "fileCount": 6, "unpackedSize": 10438, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQLO/8I6DEk2UhgJr8xIyc8cq2JCwNbzd+X/sVxpVO4AiEA3/677O3riowvq2EDVxGMNwZdlWomnl9cHWTjdPa4L6s="}]}, "engines": {"node": ">=12"}}, "2.0.3": {"name": "internmap", "version": "2.0.3", "devDependencies": {"eslint": "^7.32.0", "mocha": "^9.1.1", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2"}, "dist": {"integrity": "sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==", "shasum": "6685f23755e43c524e251d29cbc97248e3061009", "tarball": "https://registry.npmjs.org/internmap/-/internmap-2.0.3.tgz", "fileCount": 6, "unpackedSize": 10470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2v5WCRA9TVsSAnZWagAAWbEP/ilrFg/K98onVeSM6Xtt\nItphGsXHdqBq64GB3Akf5cw+RykofcbjVZyyvRNt56IlZHSOjP2hFc3iZSng\nEFS+0gqk0nBao45G0LWGtM+MrFtrToPYjSId5r5dHwDFMTfaLkAB8rg8D/Wh\nWiUqC1REF6PCLGhLzd6kl8+dYpUfPB39NwJMjWYSEFgvWMgBfwmsD2fXrjgy\n1F7qAZdzSWhb4TaLFVlDTB15K6lHixSu9+x9r/q1Y3R9mKz8DsacxXfIOeRg\nw39hAUHxY2zPXIoY3Jnc74VtqNGV8Bf/3Xzp1VvwrZhordwzcOiwrjAaF0tb\njBDkmDKDsxathcbgtbtiVeuQFNFS2+lfq6IxcQVtQCelReF/kH31fpVEyrki\naAYYTCFnG31tmAsIUxQd1+D1KL/wsTeOHrB+6AagumV0GG6lpy9zpOFgIZPf\naxoSn+RNGxcllyO4U+9rZGa6axLcRfK1gyTVbnTHH07tPOBTEjeQxkE3RjqX\ntGh8yKQeClu8rFFMpQnx1x4ITRDWTJiFRFbdv0sfUUJ+TpcBH/YIalQ5+K5J\ntD32/QMpPd73KCU/hR0Y7GKtrdsv+kdyCXBdo2wu3wkbi/5/j9fOYtFqzNww\nG05AaUsDu0m6+0RvwzPmraaIjS/iarMgfKcbP4B4asQ+rHF9778ZgJemVehT\nCO7P\r\n=stVG\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHBmnd3DzBtSez8Kl20W7WvRu+LemFDZKGhYGlSGKBa1AiAtnwWs3fEoalUgmnEDaLcfWfnPJTabV1g8Diu9IUIMgQ=="}]}, "engines": {"node": ">=12"}}}, "modified": "2022-05-06T01:05:30.176Z", "cachedAt": 1747660591743}