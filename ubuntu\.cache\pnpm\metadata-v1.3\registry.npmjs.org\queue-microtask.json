{"name": "queue-microtask", "dist-tags": {"latest": "1.2.3"}, "versions": {"1.0.0": {"name": "queue-microtask", "version": "1.0.0", "devDependencies": {"standard": "*"}, "dist": {"integrity": "sha512-uRLNddT450Jm+YDyCAXZev+Il2f0hH1fnUFBsDj5d+qqPjIGrqILczx5slPNTCwySvvL1ESwJ8dy6oZhfLsnPA==", "shasum": "c635d7bf52d1891385c8f5feaf68127b6ad98635", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.0.0.tgz", "fileCount": 4, "unpackedSize": 7269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddBITCRA9TVsSAnZWagAANKUP/2tani7idk2rw6vw3QNN\nlLthHDLEvJJxSxnBugiy3OtpbBwwQGMcqKz93bAPe/yTLYRR5ZqxTDAqtgz6\n/yNsfDx5bc69BvzxxBPjlkJQvQlN1TBNAQX6GAKmQg+mG8Co4j0NuuzKBYo1\nwOgUJJ9QXzWy/3XqLZdo5oJKUaGSA6sGdHMprku3MN1SZ8Bzoi8MQVp72MbS\ntiUrqMXelSYIMHxgd6C5hWOoh0bxWJltkJkn2S/v0wo8fYtdljpRdUS8zmM3\nKu6XRwKI8rhJUa19A8uaq4W048D+JlVJ6dpnUywymlPQUxuGAXX53ofwbI8z\n8w9BpeFkI6YMhCfDDOeOtzEScNsz0+asy7lOsL2klMarOY/38+XPk3lTjyot\nGqD+xttUnH82QnsLs4/NXMiNIEdwySVQrxDAi7H7dzTuXYVYO8/RCZdU4IvO\nAqixUSPmPRXFbTksG8cNbvgDzJ5aQMKrYbZ9gcf89ddBr56dN+qpu4DmV+z8\nWCe50f6sScIpbdRHW8/luxwlSARaQqCT3wsfFnimJjPASDevMbdbayrEY3Nq\n8n0Gems4tD+sSSK4w1uPyJ2KOeH0htHpwCAIRGF3foXOWAN/jlwEKLuHJC6f\ncAiJf/Gk9xyjNS/2RwGKIHlZswTdDXZ1jatNKOmS9b+ZdmIN3ArXP7opcoHC\nbo5T\r\n=Nh6e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCmbHmIHyaYBYyxTTTD8VDKslASD0ORrIo2w70nxRj94AIhAKumoFoDgNH75wO6XgdLJedQ7Mk6jrOLhjG6tdn3lTeX"}]}}, "1.0.1": {"name": "queue-microtask", "version": "1.0.1", "devDependencies": {"standard": "*"}, "dist": {"integrity": "sha512-DUN/b/GDEvVNaKHYej6OtaMmJ2NvGNK4Fdbn6RslPlTXoS014oSzGtbxv3NZSjjr8ZtNdCwTSIEL8zZ/rEcbTQ==", "shasum": "139a9ba003b3bcabd3a742c57e1751e45bd22f42", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.0.1.tgz", "fileCount": 4, "unpackedSize": 7392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddBLxCRA9TVsSAnZWagAATgUP/i738zonWocN6hcF8dIQ\n7PGPTwKIo1rR8gQ7y1/1sI+Px4ZWN3rbEuhui1Q3qBwNEJhE9TZB6/232j83\nPng2NVlgoe7g+f2ryO2ij1NcIguoiLLxAFx1bG/A2m2q3uNN7q64hToikeAd\nhYeFwoBoGd/CFPnILaH63/qUjMVCBWwrKm2nTW1GeGWz7aAdu+iH6t7Vez86\noOPZ+J+dGfieTjbTr5c08bPATLXOhJZyZIw+hM4SbJ/S+sMU6fUh1F0bxf1x\n4A4G93uADhN5K9mUL6VKCbVlJP58euAI46ldpBSpTBjDvQuoHSrUgfj6kCRy\niWT7EaQjFAcWMFho+FbR+KRjB4xeE0H/tgLAT+GagVL75Twu8LMI6WVUw6pE\nBmHATEk81HYJXKOhTejE521dOWj+axSHMAj4U225Z3+vwGr3UrVVxzc3hbRM\nSBFiuhgFveBrmpbFSb0E6y37zWgb5DZW8xAQr9LXoiBPSmGSPmV35Y9juKnK\n/SWG1/wdxcFOZto5BUVSEUAT9EohPzJlB9R6IkMjz/Z0sqnKB88lObaq0ZL6\nqyrf0tDYhrbXFLdpi5UYPQP8LByCFNqhNZxNWyYoy7y97OU2dL618C+dv0cJ\nyanr8KiCzKfMzDnO8Szsb2bLce7Qb761I7/cVT1DIyNWGhm/apW1VD0geiao\nICPF\r\n=QfIk\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFp3LZiVIt0xpSHgEoAY3wYgkXsBSwnZaXgCaJKHH5R6AiEAsb+EpV29Hnh/nXGScgFsfQ6DdJOy7uf47oskf86DM84="}]}}, "1.1.0": {"name": "queue-microtask", "version": "1.1.0", "devDependencies": {"standard": "*"}, "dist": {"integrity": "sha512-PRj3YNAunIw0inyUL3uc2t1vaHDPUBTkL/uZl2/fc+vnho9IZcTf+Tg6gs+a/ojZoZNG4SkwnocDNo9gzidFqQ==", "shasum": "a07045637ac989d5b2f472f1268e335c1e21f5ca", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.1.0.tgz", "fileCount": 4, "unpackedSize": 7685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddB+vCRA9TVsSAnZWagAAb/YP/RWUnI7qO9dHhJRWYcjy\nEJ+lpAnrKp+mZur+ZVTP3sNdXaSHNq6mE8hjG860g08sj7KG37bQ239B9N/d\nEH5QT+GX5ktHFxZUSpNLGu78cSqjgS6L1JPfX3mCnZBpE6syFbo1w8Hn2rDT\njuGW+WmImq+Yb0i+I71lPxarjJSl75BuXpVT+R2gvkfbbp34ra5dfrL/0MPO\nMf6qVkcJkmSVXf7L7Ghn8VkkBFjcORFOMjQyENxiqzDwY7SSOC+Y/oXYyGoU\nrAuTqfAe0wDwml1aaLT34NPNXVkbpdEqj42l9Budb4gqMkGj4I5f2NdYLxrQ\naP80EWIRGbZ5b+VhywnPhFdQNa9ppeEUvWA89R3oyuH+OGlu8E+eQxEXpOvj\ntdh8fRgpIF+9yAO7S42IApVAEoz01he/UproOjRuBFUygbmY/B+Jv2sTMBJI\nafXHADj2HcIhOXAb4MGLBWIb8/XELIWzlp3mQzYEjXYo1h3VFGhxCbrbWK41\n3wvq16tl2Wf+0chJjVK6r4Ky+qrqTr4Po7B10d1trOO4xxuWhuyugSEqDXNj\nqXOMh/maFx1QfZrZSbp8Eqgp64Fm16dqJeP4S700igNpA695nvB2N/rrA/jZ\ndshf4S7JQtSGNunWxlOrcf0UdLaRLBn4VAE5wkzx/CrhAJn6OiroIorgEpKM\nEt7s\r\n=KE5V\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHCwJtXGC32YBo+Bf4f+lKq3EBWEHzEdFX73IF1ZwJk2AiAxcHoawLtqy4JDf24b644bXCCFbLlo3Y/S1jhzhPVV3A=="}]}}, "1.1.1": {"name": "queue-microtask", "version": "1.1.1", "devDependencies": {"standard": "*"}, "dist": {"integrity": "sha512-oRF7yHbXUCW50TLJ+DRv1GHJAstca+7qoA35SIA7Z+xMyiNu5tOANsbhJlw+VC2dC1iKycAzTEdZpmnZAU5auA==", "shasum": "968d2a24d79fec1f7e6dd1680d7615993c5b2e99", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.1.1.tgz", "fileCount": 4, "unpackedSize": 7709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddwUxCRA9TVsSAnZWagAAUccQAKI92Hx/kVbfS2eUOdxP\nbynvh0gS7WZg735bf+yozyh6+jlJtVRPB3sI5FQhE3DLW8+x+dAxjGA0sp2g\nnULMxKSLse5Z61zyOQatJDSKAfoKgfUnaF2xgvlPACoofevJQsSfBUCYopU1\n12RTkS0EdQbb7skMOntR1cBEGw+Qzja4pWlK0cvA0l/NUl57VP/GrZiZKoPs\n0gvZ9fTroUc28cEn0SqekHG+HB1b+aMKyHR+sL8Ds1yCnF16wR27D1SGEf20\nbEoWTYOfnudpovg3N0YzqORBPOmmPkWbbGJhXwZDlGwMiBGXunJ7U6IQdpCO\ndVBs//shr8AGiBWYGZuiq8/9crGmrO2Mtv7JcaAlatfrpYUnKbWX72xm0DyK\nJaqXOhaJG37ZxaMrholHM+OdZhPX8pMszCkkU6xfpHIxA2Yy2PlDeg0EX+kn\nQUQj3M7tkZMVF6f+hKCZGg8GADFUGGIZa3ZiJt1GVspML3buDvpjhqBEDyIA\nPGQO1GVe3FemOuazhLDOFheP5fbYi0M2cbXVPzGgzDq4z1zbIDvGlsNh/9fw\nQbuq687ldgrUSdw7d6OyzNa7IqVcUrvlmj8W1yoafhSw8E1/TxPf4zbQnnz9\n8klcMpO/MrkP9BlGpTlbiwTLQiTeMfp31OU85+waf/KHhH6SqyrvY3SQl7Wn\np1jF\r\n=2fRW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHJ+GFfo+vGU3aqE4R1+IVchZi4rA+VqfBSXiLFuFVlhAiEAig4Q6gVMYjj+G5iBV7quaO2a0AhPb3YAtu4/0qFaO2g="}]}}, "1.1.2": {"name": "queue-microtask", "version": "1.1.2", "devDependencies": {"standard": "*", "tape": "^4.11.0"}, "dist": {"integrity": "sha512-F9wwNePtXrzZenAB3ax0Y8TSKGvuB7Qw16J30hspEUTbfUM+H827XyN3rlpwhVmtm5wuZtbKIHjOnwDn7MUxWQ==", "shasum": "139bf8186db0c545017ec66c2664ac646d5c571e", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.1.2.tgz", "fileCount": 5, "unpackedSize": 8488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdd0DsCRA9TVsSAnZWagAADcQP/i7UBInU2i++FLt2Cfnl\nOwYSMLYHxeD97YXfWL/+Oca4YuM8AmdQz5h8/w5NkEQLaqxh+/e6G91w8Nkw\n+KpVuF0uqdUgK5uD7LjdMdTfETT2dd+IuX/J8nsi6pREVju0NnHCF7hYqUNO\nd2sNGTAQH0X0+ZDmPC2Au8FtVJW2/U2Q/mCBobII+nDwKEm3iBZc1/2QMU8l\n/Z7b9mrYvPG7SGTQ911CCSTpM2EQoXmY/bn0TmLT6iYTMQcINOwAETYO5HEA\ny4GivqpL+vVyouR5sWzJPJU7lJ9EzQKzBz5RzxwEI/gY5o2bl8r7+T08is7A\nGEf7eum8zqHMRi/FAhdD42I+XLeL0pfMpAgch/UIlxERfIMZVACostjSoL/4\nB2fWniR55DRQMihyJhA+HzwXi/KtIDH2I9fOwVkBoMWsPNhLOuM5tnU7yjv/\nvMS+iH/QEs1D11YN+wU0kzaWX0RlcBC4zPrkdzVew5Ow1wmJmHYfr8MQ7sx0\ng1frpzTKgKvKbnY5UBLiicEFMM+SpRnuf/rn8LDI2taiyI5FooZnXvmvMD91\nk+vJcYCXmefLTdT2sk4RVfH42s0hJBA87TRQrXjaq3cMv+nfx8NkUxNC+noM\nWgddb28Whvqaj8jIwhUxnXh5caCENOONtiRnRZ4dShHwu380WabhMig6JJNH\n3ual\r\n=oovl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDswhJrMXlUyuh3SuKP/afp81LJWsXVT4vmzhkEYCLqowIhALqIPJHIfcWbZptptf+yoN5/5e+LvbCeX1iJdSmAJKpm"}]}}, "1.1.3": {"name": "queue-microtask", "version": "1.1.3", "devDependencies": {"standard": "*", "tape": "^4.11.0"}, "dist": {"integrity": "sha512-zC1ZDLKFhZSa8vAdFbkOGouHcOUMgUAI/2/3on/KktpY+BaVqABkzDSsCSvJfmLbICOnrEuF9VIMezZf+T0mBA==", "shasum": "9188ce1b10f9350330c509982c8b1c640e0592a7", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.1.3.tgz", "fileCount": 5, "unpackedSize": 8861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2tR+CRA9TVsSAnZWagAAwcUQAJWLrOt+lqxD0+FysfZn\nLEUDIzjAjsIh3+DHhR3YzVilYPvJpJPf1NEBEzLwKELBijOtBisRw9S3xl/9\nObKcrahmvG0f8o7ib/toJ4Ncs332zZas78WmoFUTS+x1GAQOW3BqpCSn+VFg\nhFJTQbiOSNNfq/kGP7csPQvADm0r/ekxyoATX0NwbEbZ8CwMHxuEVwpP7aC5\nGaOV5mEQvWKtwGfTyPxh86jFyo3/YQW0NTsQHdr3SQaIUwQElxIJuklRgOz8\nrehzJQxfZpr9QFd0rqeqNQexbvt1Dy6XlN8fU9n8e0obRzoKVSWec/KfrPq/\n2TdrNiBO77l73C0Q7B/LUuGgMYvMV3ZWUCL+P5IN1Xc0xvhMYZI6v8SpZxw4\n/MKbbZ/xBItWLporxgKuebGcm+i688lGZ5uYfKGirLS/8gaLFO7HbNL6u1WA\nHHQDBHlnzy+fIjcL4MDVoEjaVk93baHloWFh9FXq8qTT5nIbJTge6MzX8jAK\n+bpr+v7JOTh0Ig2glMDCREHOi0VOdcFjWfiXkX3CUrL9pQIYwMX/gR2bZcEn\nnb9kyzvDVos+Tz2/XLffRG+q21LXnH1paQyQwQp5Ba2MYZauCYo5j7ddzxNf\nTSBFpMeBVQBhahEHr7YBKV25CvzBunei+iDYXlOWBOXO1kIjozAUV0Y7keTi\nEqfI\r\n=IetU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGbndSksz2T5WUw/35EmkRj+Y4xxQk3JI7LIl/XQCy5fAiEA1yuyOoccuPhllCe4taz/ZzpBxXTWGwj3REf8bKbtjvw="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "1.1.4": {"name": "queue-microtask", "version": "1.1.4", "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "dist": {"integrity": "sha512-eY/4Obve9cE5FK8YvC1cJsm5cr7XvAurul8UtBDJ2PR1p5NmAwHtvAt5ftcLtwYRCUKNhxCneZZlxmUDFoSeKA==", "shasum": "40841ace4356b48b35b5ea61a2e1fe0a23c59ce1", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.1.4.tgz", "fileCount": 6, "unpackedSize": 8939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFiSNCRA9TVsSAnZWagAAFhgP/AnFC49jXAkAnBFaEYw3\nyF0zODWEXs2/d4CArqjb06KCyyK4g+CrRKD3lZ/aTCb+gza2eCEPI9cw1mOo\nOt+Ga52a5UWmfG/+32MK1tKKlN9M5BckFd9Ky/F8c5shlVnJ2DV3mQZeO9ea\nJzRUOJOBL3p+wS05CG6O7u8cn7O2GLss2aLzrq4JAt6I9EGFXBxABB/v8hmJ\nMqoLHcnwla3Wtyp+9vTITCQWrLzrosvizQMLYDKbFCXv3yWVDYDRZ1YfMhBJ\nXIqBlPcYZxwYR48Ssrkyatef4kl3D8c4F4EJR8iY7J18kFxZF//EeD8qK5ox\nDf50iO7+EKMK//Jwwt72yDYpa/Gwo6GLTPg2eyVeg1Lo8ZrV0pw5eEmEPrm0\nIvxdHDYYsw4dCpe/f+xfhgYbqqlpnBuPF3gY1wqj5QRpOws5l9aNxAPtPp98\nUBgb4YVjPbtaiL1fh89kQ1PTNyQQtnOgF2pD6Aviz6dIDWF5AweoB+Ml/Jtz\nGkPDLDN10EPfAJM9/VdW9rNBsVHhSqnut2r0AGagVCIkupyzCti2TzmtFg8Z\nCRbfqnWFPiV3QmhSFJMzTVzTQEDp1mwCytirTNqrX/QEK5p4QauW3s+W/h55\nFVoWx4rzg5lsw7WHIiOag3wdVDt8sge5BGqGn6PedbtEF+5rgGPx3L42IUHY\nqfAn\r\n=1vD7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhtsFeO4JBoQ+WPzkDHl0y9kceuzunqo2fPJrmPWCiUgIgTZmUfQdB+1hHqcUu5H+fsYL0ENZW25DOVwE12KDz0hU="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "1.2.0": {"name": "queue-microtask", "version": "1.2.0", "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "dist": {"integrity": "sha512-J95OVUiS4b8qqmpqhCodN8yPpHG2mpZUPQ8tDGyIY0VhM+kBHszOuvsMJVGNQ1OH2BnTFbqz45i+2jGpDw9H0w==", "shasum": "f27d002cbfac741072afa0e9af3a119b0e8724a3", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.0.tgz", "fileCount": 5, "unpackedSize": 8238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkxwqCRA9TVsSAnZWagAARmsP/i9NUNcOL8ELYlvA6mrl\ntoNJrwTQrGBqs847xXvE1Rj2A6HgS39+FoG8yp3MZXsip3h3UW4st9T/0nfq\nj4BcJx9qYIhdMEfgTTpTyv5ps4xiUTyCyJ+2NpBsFecHgjXLYFiQCV6YpH11\ngo5VKA0QEMFOPMFuIUBJ+Fx4MW/jjghEbvprYTpvupvubW+guLhDRQJkZPzb\n3z4GdogsVe/mRBUTDDMHNz7ErLbzHRNwxprobfhrtbRnlMzDUnAFGIyqJXeK\nq2D3Ha/trl2y4bcAu7wM/v1nG4E28jcCwuuG1gFdGRcJ2yNND0cdNHBimSMu\nPeZm1SavQyUfmdhdZ2fMDJgbgj/HtbUx+Cum+E+rbGyWZyzOx1tiLP1uXLz1\nR4eEl72WOqmvlqs98PiAoJpQPsJfgdAe5X/vtxbSdRDCZXgJZhJ8Oun9AVo7\ncSjhgXn9sHEdbp7HBlJQDlVSP0xjwTo7cwZqMsXU1tmZXPEKUYt4Z3C98VEQ\nkpr9EpM/q/1mNhRmFtbqAmqVFagpwRIO2F/5Fs7lqopnV9ILG2ak6u54FrKq\nebQvy/5YQazbvYezVHtBg0Ia9xkC29rlSEB1DbjoVdgPzBCLnTiFVvV+lxqw\n3ZodgFr5kNPwOTZiXKhqBaxlsYHZUy/+fiUctAUAXEVTQTvJVtxROAbJLjJn\nNS93\r\n=O/DJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDjmOFvsWLXCm3V0MCYMonixD6/h7qb0x+IGpGRKF1GdAiA+fljDnmz0iE9oiYZzHcCYP3lP7n9533NKfgOop+0ZOQ=="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "1.2.1": {"name": "queue-microtask", "version": "1.2.1", "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "dist": {"integrity": "sha512-MXDP6d9DIR+7/Tf2vxfB0Qacg9uRs6bQaWwT1RieFIMQVXDsbJ/+AZOI7tHTrzY+f0VsvSM5qQyh/orfZwOVzw==", "shasum": "7cf431d47e6c95cdc5bcaa46fec55c9dbfa8207d", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.1.tgz", "fileCount": 5, "unpackedSize": 8161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftY7QCRA9TVsSAnZWagAA5k8P/jshWHZoieHpxGRCDKoi\nBxW78uT7E1SsXW6SCjK1ZavzsDuuGtGCCCjy0WoEZmULYjooOO+VagDD0mox\nSZlN7EL43nUUmLSkysZ7R3OlKXwnphDhbuS6DW+XmyOTAmAFvQwTSFBSATue\nCgtI+F024lo1v/YKi2XjCIOzQ6qEV8bAo9iapo/8iOUFgHZMO4m84EeCQ8Ns\nAUsVq88TsbQWSq4RFLi8GYJE/xCLJshO9VtH1pIfVW1digp6Nxo9GV2BaxBL\nF8iD3tpY+8orw0psaV66Kk8dvMQ7baCBT9J9CfWcmeVR3RF1mFyNJkchiI6V\nHYJPU2caIa3sitJbj9+eKgZMSHSvWu0oqhkkel04aki/fEOqq33J9J5a2Ln9\nWlFNxHwz0E2ytJxeiIXo+UCkDeKm/Wmv0ko6sZm33sqSUKaDdjhjfkg2du+K\n4VI2IJQYQVTFCb/FfhLKgm1dH4BbUWpZN5LOo4ZgMdflek85E2rdZmgWEF0m\npbi5L2YiK7ihOmrwQCm6jCuo9JpaYvFW+dXVpo39TrVbgyURuPefQAl+C0Ky\nYtAwACHD7uwkPUpUj6ISn9CNtMHfPiOqgUytQllJOcMoXXrw3K6xNNIfNyCv\n2waE8QNE9DZgRkVBlMwsF40EqJ4mbCaGUNjahBOS+Dw6CNfOFVX6b9DAZX+T\nTLJf\r\n=fdAU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUmQ+dhseIHtqbhaq7r5Lpp3Okw6E1R+ZipKLAII7EwAIgLBkrPFmGusEQv7shR/P3mmET7bdASKuI2FcxkSjjlHY="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "1.2.2": {"name": "queue-microtask", "version": "1.2.2", "devDependencies": {"standard": "*", "tape": "^5.0.1"}, "dist": {"integrity": "sha512-dB15eXv3p2jDlbOiNLyMabYg1/sXvppd8DP2J3EOCQ0AkuSXCW2tP7mnVouVLJKgUMY6yP0kcQDVpLCN13h4Xg==", "shasum": "abf64491e6ecf0f38a6502403d4cda04f372dfd3", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.2.tgz", "fileCount": 5, "unpackedSize": 8352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvC5sCRA9TVsSAnZWagAAtQIP/RhffWENvwU9NloVjFxw\n7fbwHc775w63JUTfq9tW+TEmdMftWGyI47cekDMCpPJbdw7N8ojnbDiqmAL0\n4Cfwu7kTsFA92Hd9CZonFkCrlTTIBreCkUb8OHBOHmzP52K4LTkyLllUZ+YE\nsJixkbIxqJfr6u2ITlOQb0DrtiTVxJQKkstLQ1hW3pwZTv3ii3B9hrM56vHk\nfCBfrBPqHz8lzkpg/WV9LmUP3oJx1IQmugaLcLC/96yqoP2lD+1MauolWZjV\ny7JnRE3HTuWlXChBwK+W9pm2r1xChCn98YtHSyWG+TDN8bu/25fQiTdvgu8I\nLETPtCrT6TWRwDRv9XqRIEnrngaLkK3Z0QDhshNTdq40l8XujfgRPUmVc48e\nwJoN6snpENucpS7AIC4EFBowMAVaOLyRIcyMLel5kIMgEKzksul8GfPbtxQ6\niGPdbZ6bC/BIg+YjyyMNMulyFlH7Z2PKA7cy2HWAefB4p9yoURsyZAiLWM8x\no7/gr0+mB8dTuz+jHco2aP8e3vGWkgfpGy92YvK9q9KZt2zNFZNbhZd3MlIR\nJy/dV8uA9m1nIdt2bwLpo4aCMmAIEh39x6G8ntqb2CWfN4rMD5RhUrM9wOeJ\nEdD3BXpmN/iBym64Rp8Gct3lQf4SsNcIVF3V2kFg/ic0ILgzpm64FFa8ycE2\ngO1z\r\n=RDe+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGip37he/ZDwdKYFqAvRCJG6Sb2cCzm8QGAZMd6HwixuAiEA39hEXbK2GIdGN5BBWff9QbKAG6+0gFqEll0Da88Pcqc="}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "1.2.3": {"name": "queue-microtask", "version": "1.2.3", "devDependencies": {"standard": "*", "tape": "^5.2.2"}, "dist": {"integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "shasum": "4929228bbc724dfac43e0efb058caf7b6cfb6243", "tarball": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "fileCount": 5, "unpackedSize": 8367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWC0GCRA9TVsSAnZWagAAYZ8P/2NYQ2i3RWHcF/7BtPmp\nly0tXxs2uYyyNMh6BffugP/E+eJ0ENAw/sfAzSMvqWq8i1XdCZYiG6AMqLDm\nAWNRShxEcMLVn9uVYX6yAo3FqQWUhq2jFklMp7wKLmjsnICwRxochjG1yYsB\n7IBSRVjcq7vFUd7di/d+YempvDNpBkp1cMHV5hUSowSfrHyM8L6GAYF/oAWA\nRxZB8hmKIR88CrwGFweJsemYlcI/w/gmp4SYDyjIimQcotXNDggAfq2YDdJr\nuYRX7dCQrrrOreayZHFeRIeRHWVcos60vJzrqmOKJarGIPI6aRTw+IwtMiqW\npaDQ1S2Zv2qMNjMlQ6jvZTd2c09MQLdftP2X3gID9wP8qevyuDAJ59zWbOSa\nHT5GGgfCZh8l8ot/WYA8adAf5iba2aFGLL4NmQ7qgevnexXNp5Uxxpatdx6o\n45EBepplujdml+jKJLM7mBS0XYfodOuvL099B7YSoMKHTJMwMHqXT9p5P8KY\nDSR/DcnkcZgBSjaWR64oO1nx33Ci6Tlx8MNmwUhMSyOb1aNKHXBRKxf1+eQ7\neTG5OFi60lPdByqFXg7euuUXnZJjN2JpuonAzQ4GmzQ7pDKaxRKXwDPjGvhb\nknpOwskiPKAlQTeDzl5QTAEqOPi7hV1HJjABBggRTrOAs/dQpLPAjpELkjoo\nkY1c\r\n=pKkp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqk77plN0xzC2Uoap7aUOjMQ8KPtOc2nuDP74ljJ9AdwIhAKFYkIHMkat6jqfRULkSOd2sG6KKtpqrPpw3zLb7PoMQ"}]}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}}, "modified": "2023-06-22T16:33:42.516Z", "cachedAt": 1747660591847}