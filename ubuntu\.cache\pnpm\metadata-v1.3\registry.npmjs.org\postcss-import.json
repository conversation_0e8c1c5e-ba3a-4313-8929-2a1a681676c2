{"name": "postcss-import", "dist-tags": {"latest": "16.1.0"}, "versions": {"1.0.0": {"name": "postcss-import", "version": "1.0.0", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "parse-import": "^0.1.3", "postcss": "^2.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.5.9", "jshint": "^2.5.2", "jshint-stylish": "^0.4.0", "tap-colorize": "^1.2.0", "tape": "^2.13.4"}, "dist": {"shasum": "3bccc8042bafaed49a2697aac7cedf82fd6466f4", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-1.0.0.tgz", "integrity": "sha512-m6z8OM085+I6BGpT6IQZkEL185ridR+6/yDW9BA57Tr6F+3YFgTZDSNeHR2diVEeAii4Y81wod5dZ3xtNJMu2Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDB0ow0vFQHUSN1s0XzcykHoRk8HMYfK85s0IUcjpAzcQIhAPOKq28hn1USgVUfAXbBEOHrqk4T3YwisE4w5MhIgBU/"}]}}, "1.0.1": {"name": "postcss-import", "version": "1.0.1", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "parse-import": "^0.1.3", "postcss": "^2.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.5.9", "jshint": "^2.5.2", "jshint-stylish": "^0.4.0", "tap-colorize": "^1.2.0", "tape": "^2.13.4"}, "dist": {"shasum": "f499fa0320b303e84bdefffedeab683b18c88731", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-1.0.1.tgz", "integrity": "sha512-L4+Zw0dHU05xbnsv27eM7qke32/NQBDE2BcmFFRgpm+qIIC/nNqb4cdiDPHTgPYVHeIyPoe7QY3ElD/EFkiF/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIESKAXjXj5IC6Q0Gxkj2fCGZDUw/wtxfF2EB7RI5FAILAiByR+xOHTgLlMaQ45sNDokcsjeyOPiCKG2xTfVnqhbX9A=="}]}}, "1.0.2": {"name": "postcss-import", "version": "1.0.2", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "postcss": "^2.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.5.9", "jshint": "^2.5.2", "jshint-stylish": "^0.4.0", "tap-colorize": "^1.2.0", "tape": "^2.13.4"}, "dist": {"shasum": "efb48da8c2557b397e858c0cd96d81e5c45d97de", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-1.0.2.tgz", "integrity": "sha512-5q8M5xn5/n+njcr07h8wJTzvUd9dh1WgjT2PBtMBs5bRvoe3cHH5jOkzTwdSr5m+2q7DnHb7KiE5LdfP7tua6w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDeYHV1Es+IdYC9866WIzxJOz4PNQKuRVO/qJaugT9nDAiBzU2Ju/U+ickXxGxwvSPv9fyPAaZUJ/CkiyO+vypBk1g=="}]}}, "1.0.3": {"name": "postcss-import", "version": "1.0.3", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "postcss": "^2.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "671fd9a9c518d451d89dcf3de59d61e854c1ccc2", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-1.0.3.tgz", "integrity": "sha512-bC9JsUBf63pvaDB2O0VQa++KNQ8FfkRNYQ/DIijmkQy/KQ2/JXaMy82tQxkHaZQXXvQPRIOfGAbzXhuCOEzrrA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyjRsbbIoz1ZBdLcSGjNVhFMsWjLPG+7e3dRA26p6aVwIge5Ul/IJec8L7XIwdl/fkb4feHv4L9I7xuW9eifYxfZo="}]}}, "2.0.0": {"name": "postcss-import", "version": "2.0.0", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "postcss": "^3.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "1196e8bc88a44eaa41d36c25ab4146e5059f2eaf", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-2.0.0.tgz", "integrity": "sha512-63iiNa+4fimDs5X5DXZ9YGtVUnfllcLsYCeMlio5GqSiQTej2LCebvaNLhI+rGF+PbVb5Eqa1R6Bctc51E+WFw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF0Ru54R24ZPdV7DotV9z/jhGxZhCj6Ny/kK5klXAPzgAiBlf5fY7wj29tVuqveYBHLxxp8lWCWb2vRblPreDkcjJg=="}]}}, "3.0.0": {"name": "postcss-import", "version": "3.0.0", "dependencies": {"clone": "^0.1.17", "find-file": "^0.1.4", "postcss": "^3.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "8c8589801db3017263bbe4cdedd9d0ae877875df", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-3.0.0.tgz", "integrity": "sha512-hs/bKpXo1klC1CJVeALsFIKP474JTMIMvf+9EZ0OcLceCXE5NtdbVjwZXFZR8lNMxTbsN+WerKqcC/oX9jKdyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDONE2z5APR+wVNkYwEml08NwBkmvjIQzyTmrMnCiWwWwIgarEasrh/yMvuAW/9WofRByHw9ZosouqZHazd5WsI5j4="}]}}, "3.1.0": {"name": "postcss-import", "version": "3.1.0", "dependencies": {"clone": "^0.1.17", "postcss": "^3.0.0", "postcss-message-helpers": "^1.1.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "17a7a2f5ed3746f980dc1208a236a03e3346da6d", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-3.1.0.tgz", "integrity": "sha512-LtuQlX+KNQK5zQhqVL4J24mzBbGLxu3/hNjXILxgD/d+vhMSLH0V2cMJNKWbKqAMpcPtRIEEdvkEO7tu2n9S6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDv6zctYmywcn220JOJFzJrVAfsKIlROTI6D0ZE3CZdjQIgPPR4/8erwzxjBAyNimFQBV0LREc8dK/ZQyrKRocUhBc="}]}}, "3.2.0": {"name": "postcss-import", "version": "3.2.0", "dependencies": {"clone": "^0.1.17", "postcss": "^3.0.0", "postcss-message-helpers": "^1.1.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "f579e48707885d5f44f3d7e779e8978bed978a5f", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-3.2.0.tgz", "integrity": "sha512-rCIoEVGv97IrAroYNUbz0tscpfWKfmX649HbsmEaQGNS0DCX5NRiAQhzwhR9V86ADHkb2E6kGMKQQDwQw6RE+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZYSqSzDfOn2twlKGzbLAqde6XC6ojv1eQB/nZYjiHnQIgCxA6xO+oTIg3HkrOqOmTeKJqefPsU9NB8Wa8dO+KyPE="}]}}, "4.0.0": {"name": "postcss-import", "version": "4.0.0", "dependencies": {"clone": "^0.1.17", "postcss": "^3.0.0", "postcss-message-helpers": "^1.1.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "3295b78079bb72ddd89870592b3d07f77bf905d2", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-4.0.0.tgz", "integrity": "sha512-QLxenjDgk2DM2Mvg5Qod+k4eCz3Gqf/mJnkxQW+S2dal2R0kIqm7zot+UJKqAqLU769oK5xN1vGknvUIqtyNKw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDlSq5Zzo6nrPF3AS7Pp3vZsxuhVWOJPPNXTOXuG8y+AIgHYST/7AfFk2utddwhU/RstN5HYaE6U7RvncVTWMVfDY="}]}}, "4.1.0": {"name": "postcss-import", "version": "4.1.0", "dependencies": {"clone": "^0.1.17", "postcss": "^3.0.0", "postcss-message-helpers": "^1.1.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "2d8c5e10381936439e91013735c736cbf9b00d21", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-4.1.0.tgz", "integrity": "sha512-ozXTBvZNhsrqRYrGdYd8G10deNOz2S1Fxmko2MnvKmu25RIN1a4Cj1IzbBqjKVvJmSHTbPwHWgXQOYnAVjoPFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHBv2v5Ktnz9t0oWKNKqaX8ZASUskI0f0x1KZb0GgFN9AiAVYp7Y1IFxgkWh74TVf1Cw90bIp4RozFuqwh/vvE0tIg=="}]}}, "4.1.1": {"name": "postcss-import", "version": "4.1.1", "dependencies": {"clone": "^0.1.17", "postcss": "^3.0.0", "postcss-message-helpers": "^1.1.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "fc56b2f3975a736a7de654a6b11dedaba648cc4f", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-4.1.1.tgz", "integrity": "sha512-vfS2JOOUDUFJl89K6FUIyiVsgtczJ4r26rALCRa4XQnaGywik9Su6X00jEjoPHWzcJlrFcoouZc/bdKpkt0oRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAqGo5AG34YyAjqR6zB/LWesYM3DQhI8YYnglNy4qV1wIhANipGCfPe6FS44c6/kj7p4AZcRd1Wra7cZLHu4Qk308H"}]}}, "5.0.0": {"name": "postcss-import", "version": "5.0.0", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "698168b1c61f29221b956524c937519f77f3d98f", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.0.0.tgz", "integrity": "sha512-BxFXmAqoEymY7peC1ZiczzOMTTMWtmCY2OwPlUqsjYZUhucK85uhJGv3Fsy53ZI3fQ/O7BVGH9jrQa9ucFxscg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEpSEa5+qVU7t9D/EuMFK/t9KZz4ejqfwakQ+AleIKhkAiEA6Ou9NEQ1MMu28/HCb9RoYMaX3huCFUI4pIQp3uElcbo="}]}}, "5.0.1": {"name": "postcss-import", "version": "5.0.1", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "520b3ac98ae9b46f871d5b962388a1a1c0d9fbbf", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.0.1.tgz", "integrity": "sha512-fnGohWOs9+KelkFlc9p52xwmrzE3wK9uCLFAUp7R/sCEYX+uDG4qWv+MRhkBQ/+Um2AGJGt/JwcKsOaL6qSXeQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEuG48mqVsqKVerjI0/QSZj7qOGZ8lhnJdFGxIwNk38gIgJ1pi6W3tmKUcgONgNJJgMvEB0gHAJlHUjAjwbD7nSLY="}]}}, "5.0.2": {"name": "postcss-import", "version": "5.0.2", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "e3d4927c33fa8f85f37e95f7ece75e64e6b43bc7", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.0.2.tgz", "integrity": "sha512-nrV6YGi2MpwZIqWsKDSJfq2/aVQ1uwPZ3QHtGxdv18qsXgrgG5pWSuqWDGZBxKFOZK+bQYrTgSHeJv2WlAUBog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGGD204fQlygNN/Vo2etPjK5YLzgx02OUu3Vf8EtbzK9AiAS++2QMeUx2g5UgkmLuQFzm6Zl4sWjkZgw/aku73vJhw=="}]}}, "5.0.3": {"name": "postcss-import", "version": "5.0.3", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "83f9b8b1388e046d947b629dcab8c96bc82f532b", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.0.3.tgz", "integrity": "sha512-s3/dWmnfKpL0Rw1NlhBLV6q2GVqhtubn63bjfwWZ8wT4IpU4Q9DVTEYgiEOq5hPZ+NqA7vcyb15ipsDSzPU0QQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCVR5LVNVwA0BiG/qP2QCVp6m57s0jiriVO/Xn4LnzchQIhAJefy5RRISP6c++UYv3oiLLeFGWdJ9CY55CwptWBpayO"}]}}, "5.1.0": {"name": "postcss-import", "version": "5.1.0", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "1107b0fbac46790b989310f23791a352a431f614", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.1.0.tgz", "integrity": "sha512-Db8ypf1OoaQPw1FslLE7sZXRdBbsixNKk3iNolIY11qCkBB1YwsbQ+0D/urE8iZzsrAEQC0XbB8S2tOTu6b9vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHsC3rgytTNwLnY1VS9VGVd7Qxs0d+flpyhW8nz4YRTyAiAu2XhYxvRZw1bSlNs+BEsK17HGs2C6NwTj9Q64X1Hqrw=="}]}}, "5.1.1": {"name": "postcss-import", "version": "5.1.1", "dependencies": {"clone": "^0.1.17", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "4fa714ed8e7bc47635aa415c0f8114d487597d9d", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.1.1.tgz", "integrity": "sha512-pdEpUMWGYtTg+X6byP3uuqCOK5LPbgtfzrTsprYgUTCgLBrCKUxlK7cn7956aw0eXFB1gBzIprGDbip4Fvi6gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGPAvhaKGNoM+ase1XB5D8rc0eOKLTSWZZglBpI2rqhoAiEAvubNpQOP0KHP6Jja68MQePWKlER6S67xa2qS7xD/Nh0="}]}}, "5.2.0": {"name": "postcss-import", "version": "5.2.0", "dependencies": {"clone": "^0.1.17", "glob": "^5.0.1", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "a8d012377c66c76e128e06ad203821c8ffe89380", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.2.0.tgz", "integrity": "sha512-Zlb0ToXWiA/z3h3bXIrrMnhKJiXXoU7l3y15kyszMr7cWL+iGAyctZGDqhJSwnApY3RUd6Guxn0YBWBfk44vTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIH+Ohd/INsnJvs1z31ySg0CxSp0iW7s2SP8rSDX3gMczAiEAl2H2sJ8HxzddnYmIyItVJEibAeZZohatitRbqEmgatM="}]}}, "5.2.1": {"name": "postcss-import", "version": "5.2.1", "dependencies": {"clone": "^0.1.17", "glob": "^5.0.1", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "e38668ab87aa2913c6f81e6756cadcca78bbd042", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.2.1.tgz", "integrity": "sha512-/s5g6EICTPFVPA+qRxh96vaY/faa3s23TEroFDdfWakp01zloq2vY6s29X0F6xSKJTtI24er0v8FY2C3CqvNdA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnYOt90D/1e2mJ/scnLlN076HvVmgFiJuoV7XywQNsgwIgfcb25RWeMdcMnbN5jd5wedGXQYTYifdGvpaK69huHIM="}]}}, "5.2.2": {"name": "postcss-import", "version": "5.2.2", "dependencies": {"clone": "^0.1.17", "glob": "^5.0.1", "postcss": "^4.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "jscs": "^1.6.2", "jshint": "^2.5.6", "tape": "^3.0.0"}, "dist": {"shasum": "2090ffd4028713447d711e0b353eed99fc972736", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-5.2.2.tgz", "integrity": "sha512-fnCy4ZAXCUsJZn2hYg+5FbEBeQqY87AU84HjBt78CWZJE+BayHCCAmE41/zr1x83O6suHMAljLf7OBoB3wlZ2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWVz19j3R2Rff3y8PCLhBPA74U4te3CRL+i8JtFoicEgIhALs1xsvuqZum03NzMksBe0k+tImdmRcAPhUK8Dh1JMqD"}]}}, "6.0.0": {"name": "postcss-import", "version": "6.0.0", "dependencies": {"clone": "^0.1.17", "glob": "^5.0.1", "object-assign": "^3.0.0", "postcss": "^4.1.4", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "eslint": "^0.23.0", "tape": "^3.0.0"}, "dist": {"shasum": "9ef64f6a98a5aa60887155dc98f6c787febf2a7f", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-6.0.0.tgz", "integrity": "sha512-PZxNzx73u33ALK0JbYWn4Vo84yW1G3kbVL4Kbe+X+cPgwlTUfqRN4ENFxwozaMbvzgplY3vBh/GI5xEd8RnzGA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE068tehzNP3yJPPyaBiFGd9TMkKiH9b+VwSy3RvWWe1AiADGX3okFEzcbuWCOYwPbyL/GvF86ymSVFiDtyAhJR94g=="}]}}, "6.1.1": {"name": "postcss-import", "version": "6.1.1", "dependencies": {"clone": "^0.1.17", "es6-promise": "^2.3.0", "glob": "^5.0.1", "object-assign": "^3.0.0", "postcss": "^4.1.4", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0", "string-hash": "^1.1.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "eslint": "^0.23.0", "tape": "^3.0.0"}, "dist": {"shasum": "ce9da07ae6bfe2c4c40ce52a0f98f03123b4fcd9", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-6.1.1.tgz", "integrity": "sha512-Uq2mfZvauzlV0cfRJjBs3E3M96Jp5NdZtjftBgqZm5EX9Q8MJMj6+Y/XjcE0haTfcTU+Ss3xp6VRiMH+puiogg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWoSnjQypCnRATqxq2eQlXCm1KewygPcdxL8PW7EbTwgIhAIuIxsRh/3IhIID+Oi9RTnj6ZbHDsJ7CmtD2sCpemk6C"}]}}, "6.2.0": {"name": "postcss-import", "version": "6.2.0", "dependencies": {"clone": "^0.1.17", "es6-promise": "^2.3.0", "glob": "^5.0.1", "object-assign": "^3.0.0", "postcss": "^4.1.4", "postcss-message-helpers": "^2.0.0", "resolve": "^1.0.0"}, "devDependencies": {"css-whitespace": "^1.1.0", "eslint": "^0.23.0", "tape": "^3.0.0"}, "dist": {"shasum": "6ee17e8ed8aeb2b351048f2f3e2f7ad6f8ecf73a", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-6.2.0.tgz", "integrity": "sha512-6XwtcR8dS/5ytT1BCfROWnBjhUxDW3Ta2Pwmyb0tlKc1aTvz9jHcMxbwMNNNRrD6aYwOokC6VOJ1wqfoChtzbg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDd6d6kMYN8mVCzUc5GLdeMLaGsnTjl0rheiMPvJ/lhrAIhAO4ucNxjjJsubsOblUKzcmHCksHRyqMl3g4TnctVLHSZ"}]}}, "7.0.0": {"name": "postcss-import", "version": "7.0.0", "dependencies": {"clone": "^1.0.2", "glob": "^5.0.14", "object-assign": "^4.0.1", "postcss": "^5.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.1.6"}, "devDependencies": {"css-whitespace": "^1.1.1", "eslint": "^1.1.0", "tape": "^4.0.3"}, "dist": {"shasum": "8468f039b94af874d0db762227b4771e1e12dc7a", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-7.0.0.tgz", "integrity": "sha512-4r5qhLEdsrEPYYbHRgZug0ilHFlClpfznaTLKcGIqVMz91KfMQca+AWzNTb3UBKmDubohunVer5aPeuFkqagyw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC+zstnpIncgym6dcVbnbo0D0i3lKTSSS3bzRIL/4/BQAiEA8mavO1D5+v/TR2vfuBlA+o1sBrncocUM/hz/l9NoPhg="}]}}, "7.1.0": {"name": "postcss-import", "version": "7.1.0", "dependencies": {"clone": "^1.0.2", "glob": "^5.0.14", "object-assign": "^4.0.1", "postcss": "^5.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.1.6"}, "devDependencies": {"css-whitespace": "^1.1.1", "eslint": "^1.1.0", "tape": "^4.0.3"}, "dist": {"shasum": "f89e3498b8b08fe0f8e55b6dabf30214fd83350e", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-7.1.0.tgz", "integrity": "sha512-p+pEImZChTGLhxmZidyWWeCJyoHA+ZGnp7WRVi2MGzwx+UoUvKMFJfuiM40TMH1So3Q/Ybr5iqpDOs6/RBVH8g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAjXqnanx7RbQCJRdX720aadPnjY68o8yyV6GK837+a0AiEA1UjyKxohA2vGooD0d1f61C8TP6sv78NB1reoMXXNyuI="}]}}, "7.1.1": {"name": "postcss-import", "version": "7.1.1", "dependencies": {"clone": "^1.0.2", "glob": "^5.0.14", "object-assign": "^4.0.1", "postcss": "^5.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.1.6"}, "devDependencies": {"css-whitespace": "^1.1.1", "eslint": "^1.1.0", "tape": "^4.0.3"}, "dist": {"shasum": "6438c9a7c183c38a4293c5061b1690edf9bb57a9", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-7.1.1.tgz", "integrity": "sha512-E6FGwimOarDlybgrzTBeIHE+0McPZ3/ae71QybUWK5YT8s8HADrXPrRq45tBjnue97q9bykrL1RnFsk2L+izLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVcRJu5xEW+5ZaWvXuOWqY65J/hGIibNfuv4xAyEqMfAiBLr7iSWPF1Ju2zEgwvnHpyxJiNEUpg0JZcRlKgi4EHyg=="}]}}, "7.1.2": {"name": "postcss-import", "version": "7.1.2", "dependencies": {"glob": "^5.0.14", "object-assign": "^4.0.1", "postcss": "^5.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.1.6"}, "devDependencies": {"css-whitespace": "^1.1.1", "eslint": "^1.1.0", "tape": "^4.0.3"}, "dist": {"shasum": "f2347029419f74f0fcdb4a5ed56d39ac67baa34c", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-7.1.2.tgz", "integrity": "sha512-nK6Rtx1dbp7lKSoNY4qN8JggQ4spBBvPftv2GFVjvsjScLQO5G8iUQIRIWk/DI/2a/SuWOgVEFTtYBo25bDy3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAyIsWBzWxrfdE25Uv5aKnFCEF+gvYzUaEVAd6+Ju9u8AiEAhk4a5mKsvamHqaOF4gMdhfEI4wsSUp48+ZMs4LNTUtg="}]}}, "7.1.3": {"name": "postcss-import", "version": "7.1.3", "dependencies": {"glob": "^5.0.14", "object-assign": "^4.0.1", "postcss": "^5.0.2", "postcss-message-helpers": "^2.0.0", "resolve": "^1.1.6"}, "devDependencies": {"css-whitespace": "^1.1.1", "eslint": "^1.1.0", "tape": "^4.0.3"}, "dist": {"shasum": "92d1021af2a60df4ddc0acf3aa7ff7726a4d6c90", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-7.1.3.tgz", "integrity": "sha512-8v7jSjmd1XnI1fC+lZnoOlpo+/2Yj1sEFuiVVFRDxZEecA5pu9ne6sSOmgWWLy8+Sd+SNpPbaJhadUOxq/s2oA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCaecMjmkgaVh64DxaZWfpE1GTxdJ34+D2IzDsA09kDUgIgKZ1iZHrqEflyd8wxAUM1pk404Wx+kfly3JFBdku6kNg="}]}}, "8.0.0": {"name": "postcss-import", "version": "8.0.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "1083f69d6b291bde6099b398619ed040d30d915c", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.0.0.tgz", "integrity": "sha512-LNl8Xo8ryayjB8UqaD3vHVPmTKbw+aKmQN+kT/p2XVCE+evBj8brX0liC4dvRo9M2hWPSF9FN2vJ6P4vz8dFUw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFRlv82oFHZf93AMpLBWHl+Revih3avyEZEa1lpYqFb1AiEAlkwuoUQaeVPmvg8t5DaOEGd5NIfWwJRYJRU20UJn/qc="}]}}, "8.0.1": {"name": "postcss-import", "version": "8.0.1", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "e98fc11b28ac0c144305cc72dd4e248e6a79f799", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.0.1.tgz", "integrity": "sha512-YOjCKmGuC1qwal3hHJ8q2leqKXX4yTlX6MRTTchBGMYqaJzDBboH/X3+zdIeXum0le+UeROFzEEU5zBeLUknuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGtKX55OL30hufvXSr6E/wDdIttQk7Pfp1NpAA3yt9VrAiEAqxQR+suEmy/BX6obq27ckF7OpkTfHq0Gr2glAca8d5o="}]}}, "8.0.2": {"name": "postcss-import", "version": "8.0.2", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "59cf54d92cfd25ea96a82ee1e404acad6196fc22", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.0.2.tgz", "integrity": "sha512-rd70IMgiRtR3aGYTPaK97JNtxZZC50Xh3YQ73UM25dOZ+DTQdG9nYagBZsdsLuJ5//Rl+k6iTbX3NlzThKEW8w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDaenTIdQe6E2D5cDaDW3zC8BqOJfPGvyykPCTx8rDPxgIgYuG6Ml/V83s7qvq35GLlNHCMQXKJXnYRsG7/oKUKPHI="}]}}, "8.1.0": {"name": "postcss-import", "version": "8.1.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "4027f5f37c20b3953c7b30f9a8bfeec20a3cc14c", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.1.0.tgz", "integrity": "sha512-fwfKsVV3W5TD0HH7hTnIK+fTIwpq52JSI1gAlUHBitg3uHqVC4AD1PsvJL+1pQT2Da1HV3GPoAwwceDH3frE8Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCk1BXROs35qZsMrfE81Kyl21M02IC//PK5Fc4JULmmqwIgZP3yig0aEjrkJDXGCZ8uoC5ELGv/LNkcMz/oN23MWKI="}]}}, "8.1.1": {"name": "postcss-import", "version": "8.1.1", "dependencies": {"object-assign": "^4.0.1", "pkg-resolve": "^0.1.7", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "e9000375157b9d9363c7ba0b5835c7fed6a53ce4", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.1.1.tgz", "integrity": "sha512-BdOlJfgwyJzVF6+330P7dL60zS9zZpHjG/QJpJSO/cHCUSJ++PmJk7QP0CJoycRjcr8QQwSHvsnpK7XbxxEyYg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBOv4KnRCMVhU2VQqA06R1i1wkV3+iNh/sc7GYR3ykruAiEAw7NqTO3l17sen5AcZeXEjvFD4QEU0b+zjtANDDkqhzU="}]}}, "8.1.2": {"name": "postcss-import", "version": "8.1.2", "dependencies": {"object-assign": "^4.0.1", "pkg-resolve": "^0.1.7", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.11.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "f1dbcce590c93b536a121ffedcb63f1b751749f9", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.1.2.tgz", "integrity": "sha512-ZpyqvhBLOalEwzx/+3hiCaAISzw3GKMNg0+bAOcF/Sbb4Sm9ZOc8vwZ7p4E4c6j4TcEThSAXZ7Bmy2I1DAnbMA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHwOFXlNr2eJjLw6u2RpEE49OdHevnGkH1VQLU23Lg4sAiAaEosC8EXvCSSpeQDDQldzs8rGOtpXuQfBib90Frbw1Q=="}]}}, "8.1.3": {"name": "postcss-import", "version": "8.1.3", "dependencies": {"object-assign": "^4.0.1", "pkg-resolve": "^0.1.7", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "promise-each": "^2.2.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.16.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "e37ff9bf6e10b9829ac0b65c226f4f191dc502b7", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.1.3.tgz", "integrity": "sha512-mpO16UN2yWJ3d90CXiKneyHoFlA4X6V8K6RJiot0hD+JolL8NlVwsOUkqLJ9XOu7NnLUXP+Y5wjuR1eBB/HvYw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEv6cfGdFv9k3pVeTNY3adR1ppE8585dKC7g9uWDu4ukAiAnk0egHhpCvsscp2bJGRdT4flTZuoHIwnhmbUVSffaLA=="}]}}, "8.2.0": {"name": "postcss-import", "version": "8.2.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "promise-each": "^2.2.0", "read-cache": "^1.0.0", "resolve": "^1.1.7", "pkg-resolve": "^0.1.7"}, "optionalDependencies": {"pkg-resolve": "^0.1.7"}, "devDependencies": {"ava": "^0.16.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3"}, "dist": {"shasum": "f92fd2454e21ef4efb1e75c00c47ac03f4d1397c", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-8.2.0.tgz", "integrity": "sha512-S/bPBRCk0+Ww5kFxE/11/RiQm8gdX7cKru9EolsBO52HJDJC1tuoSjVv7fSIG+2pKZfUNVJk78Tpy87ZhBeANw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB3QaPS81JbPQI/QJNY3ARfK4PBI4bB3OmhPHhYb/SjMAiEA+tLVMldbUD0Tip32tEK08SdexkeyADXSNSP8nqzN798="}]}}, "9.0.0": {"name": "postcss-import", "version": "9.0.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "promise-each": "^2.2.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.16.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3", "sugarss": "^0.2.0"}, "dist": {"shasum": "751fcd21c53eec6eb468890384ce3c114968b391", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-9.0.0.tgz", "integrity": "sha512-A2C9yXBTe+w7pFkYFkN7Rp43zPritM/jmIS0JiBnQakn5JkfG/LGmyIy2UFw9VWAiCNANedW7p7RC6LqHPYvWQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBUdnmgVyoczh6/bQ5E/mk2LLfwEVZaGkbP+cWPwTxrVAiEA8FER1GEFOPgGiIAMcYO4J40bwo+VbzHQktaFWqndIz0="}]}}, "9.1.0": {"name": "postcss-import", "version": "9.1.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^5.0.14", "postcss-value-parser": "^3.2.3", "promise-each": "^2.2.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.16.0", "eslint": "^1.10.3", "eslint-config-i-am-meticulous": "^2.0.0", "npmpub": "^3.0.1", "postcss-scss": "^0.1.3", "sugarss": "^0.2.0"}, "dist": {"shasum": "95fe9876a1e79af49fbdc3589f01fe5aa7cc1e80", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-9.1.0.tgz", "integrity": "sha512-d1zn/qUoq/sCRJi9gqQKaLt2r/ThlpCv5VjenA2mZsBFwOWW8MFGjPNBlJqSzSR+l82VnLWhB3pa+eRh2XNVHw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBlDxAP8Eg+rO7c+YmcH2glm8c/cfa6zGeGNLSxgs/vRAiBYR0pRWoEIsIsuv1tQJNUWT1dEDFr4UQu2saK5ICeW7A=="}]}}, "10.0.0": {"name": "postcss-import", "version": "10.0.0", "dependencies": {"object-assign": "^4.0.1", "postcss": "^6.0.1", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.19.1", "eslint": "^3.19.0", "eslint-config-i-am-meticulous": "^6.0.1", "eslint-plugin-import": "^2.2.0", "npmpub": "^3.0.1", "postcss-scss": "^1.0.0", "sugarss": "^1.0.0"}, "dist": {"shasum": "4c85c97b099136cc5ea0240dc1dfdbfde4e2ebbe", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-10.0.0.tgz", "integrity": "sha512-tU3ZSSdREBRjndNDxfyaDOozz2ODOlV0DP26EZuZ9b3YVr0PR/AyGiGH/nhqNX1j0ku+D7JgrbcnZd8S6iLwFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHVtCR+dRkR7I78HmW9KlAAu6kbh9oEso1EkBZyiueX0AiAKthiMI971Rgud1Gt1B1NYsZg2OCJtzAjSmDf9tm1oqQ=="}]}}, "11.0.0": {"name": "postcss-import", "version": "11.0.0", "dependencies": {"postcss": "^6.0.1", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.22.0", "eslint": "^4.5.0", "eslint-config-i-am-meticulous": "^6.0.1", "eslint-plugin-import": "^2.2.0", "eslint-plugin-prettier": "^2.2.0", "npmpub": "^3.0.1", "postcss-scss": "^1.0.0", "prettier": "^1.3.1", "sugarss": "^1.0.0"}, "dist": {"shasum": "a962e2df82d3bc5a6da6a386841747204f41ef5b", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-11.0.0.tgz", "integrity": "sha512-zZ2KgwR1C2vDyt6aAqlqhjQCkLernwcJHRi5XwPuSjvbA1P49tDXhzE5mx4DN/w4unmtzHDlwvvKwAcyid4wpg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEL3LKWzQvZf/XfHeZS+vaTYIcFYna683qf34VA69rXWAiEA8aU2ojnWykAx7Z97mD/85IJqkRcTjlKdCR+XRZ0gbg8="}]}}, "11.1.0": {"name": "postcss-import", "version": "11.1.0", "dependencies": {"postcss": "^6.0.1", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.25.0", "eslint": "^4.16.0", "eslint-config-i-am-meticulous": "^8.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-prettier": "^2.2.0", "npmpub": "^3.0.1", "postcss-scss": "^1.0.0", "prettier": "1.10.2", "sugarss": "^1.0.0"}, "dist": {"integrity": "sha512-5l327iI75POonjxkXgdRCUS+AlzAdBx4pOvMEhTKTCjb1p8IEeVR9yx3cPbmN7LIWJLbfnIXxAhoB4jpD0c/Cw==", "shasum": "55c9362c9192994ec68865d224419df1db2981f0", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-11.1.0.tgz", "fileCount": 10, "unpackedSize": 33896, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm0JS6lZxYX0soHC0ze9ItnWYIF8jS4C3EA7A0TQ/3OgIgTbcs026CzQQnxLsUDj/Vh78PXySjxenGEtkPvCnTroU="}]}}, "12.0.0": {"name": "postcss-import", "version": "12.0.0", "dependencies": {"postcss": "^7.0.1", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.25.0", "eslint": "^5.0.0", "eslint-config-i-am-meticulous": "^11.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-prettier": "^2.2.0", "npmpub": "^4.0.0", "postcss-scss": "^2.0.0", "prettier": "1.14.0", "sugarss": "^1.0.0"}, "dist": {"integrity": "sha512-3KqKRZcaZAvxbY8DVLdd81tG5uKzbUQuiWIvy0o0fzEC42bKacqPYFWbfCQyw6L4LWUaqPz/idvIdbhpgQ32eQ==", "shasum": "149f96a4ef0b27525c419784be8517ebd17e92c5", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-12.0.0.tgz", "fileCount": 10, "unpackedSize": 34079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZe/LCRA9TVsSAnZWagAAXMkP/jQ8KCKRTadHj211pq8o\npdzpktpzuCBDORS2mucS3riIVCSxmp5+4drbTBoXbx/hqZ+hOZhs2qIJCcH4\n9VxlvQv2KQ1QIjXLenrMWiiKQKgNhqG0239hbwiWAlhyzequvWGILn8sVeET\ndpFB+QFYLvtxkdNKspfquKcH5z7TFaY73fAJqiE/MzXyF3eE4rQBV3lK6Ip5\nj3q7jXuMqbksjr6PL6q51Ts4qxvWAv+nfYzkmEplozB9cOGOA0aj1axE63Os\nqmxJlpoZVLW+cwkRoUVc4i0+2lhP2IfVQLfyeI6Xw9prIGKU+cxPh45hpWpu\nEcDMgucrj8j2YGk1bQNWpTzmvTMjM7J+Aw2du3wBOpAE5WnunwE/SCovPz0F\nUdNp7KvIahDGMTQWJpLQhvwtMlwFEEnOl2IAVrFlWMiBDSzkrMqMO/hrHSey\nzYQp+qOkH6DU9Y76fYewAdWnk9gtn/NYPyKRm0OauSYpSkKIybvyMK+A7/Jz\nDQsI++FZ63zdWQWdsm8hrHjbxKjuwviH1B2qaNDBx0Bb66FW/zpDb0eJTd5G\nprfwJ4MBy14l3quWi4F4GLX9YDPilwWy8FZVWDtzoaC2Wi9VtzrreIxhI3RR\n4mBLXcV8hJ8jSlVerFC0wCD10N/EUHsCq0WE76IVKs3J6ael3bt4wEkyxuUP\n7Hmi\r\n=ytJ+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCa6j+gUf7MAJ0wMb72uHT01ZEnHYjSoBm2NDdPBeXw0AIhANPjJfQLdKtA9V3I3esj3D5SQOD3nwCATCZ/1PhBkWpZ"}]}, "engines": {"node": ">=6.0.0"}}, "12.0.1": {"name": "postcss-import", "version": "12.0.1", "dependencies": {"postcss": "^7.0.1", "postcss-value-parser": "^3.2.3", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^0.25.0", "eslint": "^5.0.0", "eslint-config-i-am-meticulous": "^11.0.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-prettier": "^3.0.0", "postcss-scss": "^2.0.0", "prettier": "~1.14.0", "sugarss": "^2.0.0"}, "dist": {"integrity": "sha512-3G<PERSON>33dmCjyKBgimqGxL3vcV8w9+bsHwO5UrBawp796+jdardbcFl4RP5w/76BwNL7aGzpKstIfF9I+kdE8pTw==", "shasum": "cf8c7ab0b5ccab5649024536e565f841928b7153", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-12.0.1.tgz", "fileCount": 10, "unpackedSize": 34261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzoAyCRA9TVsSAnZWagAA4FMP/R/QtMMtwWA0SJ4DYPkQ\nZLyJkSnCoQrxHQKoCTdLXk1vQ7J1xj5o6AWeHqrNvZot6PXQIHVtiUWrpZfi\nKmqqUAxu/OgBhjavSxnms5MQt1JkaEhopCMfJBBcU5r6gQeE4O/iHmTXCv1D\ni3QJz6VLo5fuWCZC0UQmfhvEorRmDFCgyEhqW74xa5zsa9JrL0TWT/tKBla4\nZJLxwCdeXlRtylqxS/5H7MUME+nPvMDxLTVE5JNcZtAqe43VfiQ/c0sWR7Xr\nULLuQGq9Tx+c7IZdw8MUzGrx0tn9jKAdxOboWfmYLdd2Qc/CGjmhAB5NYjDp\nzNL9NmV9BWKh+7AUCa+sq66trq5b99/lBAhEyknPXCjuQoywG5vjMEup3OpP\nbfRCcPY67EEhz8aRSfH8xmGSXJ5d4F+8i+mBtsEtzIHmKNj7v7iQwWev6lQU\n2vwleCTLXmumpB+aCRnDox9RivnxJqbObkJKWzTYuPu4kbZKHQfMOiEE16Om\numXAfq/NIP2RKoj9Bz/1R2Y62r81H0/XCUG5C07UR6aSfkOEkElBwXemVFd1\nXyA6bL3kMDnTX+yv03ZwuRE0x279byOC8Y7B/DpGRXe/AK+/rY7bygoDE3HM\nQd8IE0vBsW6j3B94h3q4dlbb8teGz5WDuFy9YdUpzla5eRLV6Zy0tsVN170a\nl928\r\n=MrUB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGNDCaKFUB8/lw8jf3UJctkw0yIt1ccn4IM2g2Lyl22rAiAraVuA4jMPpkz6GASrNQk38/PkrPvBx62B5/Vtf/8YYw=="}]}, "engines": {"node": ">=6.0.0"}}, "13.0.0": {"name": "postcss-import", "version": "13.0.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^1.0.1", "eslint": "^5.0.0", "eslint-config-i-am-meticulous": "^11.0.0", "eslint-plugin-import": "^2.17.1", "eslint-plugin-prettier": "^3.0.0", "postcss": "^8.0.0", "postcss-scss": "^3.0.0", "prettier": "~2.1.0", "sugarss": "^3.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-LPUbm3ytpYopwQQjqgUH4S3EM/Gb9QsaSPP/5vnoi+oKVy3/mIk2sc0Paqw7RL57GpScm9MdIMUypw2znWiBpg==", "shasum": "d6960cd9e3de5464743b04dd8cd9d870662f8b8c", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-13.0.0.tgz", "fileCount": 10, "unpackedSize": 36019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfj2oiCRA9TVsSAnZWagAATIYQAJSStQ0L3e+HXiU5t4qR\n9fZ//xsdwfgLSfOgOm1yPK+gK6ijsPOPY4eL/2wjAu7DLh8Zpi99WAjZXH4M\nthyPrif5i7yqH0vBw5I8baPfsHycE+xbd+6KBCe2LQr6MJBdnNZqh9QOxMOu\nf7OgI4qRE2Br5PImJz5M5iZldqUUKG93Vvbds16r7NU5F6TzL5yRzyU4xO48\nbI2pxp73XKHEEeF49ALexfR/jpYPhSdNEyR23AitqiqBibaSitT4QM05i1n6\njOauIUP+TtHfspIpsg7nU8JrbZTJMuIA4dGr8oXxVJ0d9CicqcY4uv5aZdbf\np4ow8EZ3JlZx3BC/hivGWe+J0OnPLxT+xVzTJZdL69vHuoQikwizMJPpt4Iu\n3RPVEIF0BQORq6E9fYm5ZRH+MP44FsxNjZ+DnM2b9pP1579ZjLYucU38tFAS\nLYqz14X2m8QtUzbRGpSouaVF2INWt4e+r8H/Epov5S5b4+fGnstW17xBnq3p\n4ZxcIdeOeblmEpo5fgQAqCP1w4osQpXCfHJhJYmlu5BZIGY0iWzKfyhl5VPn\nrIKvNAaEzh3MX1himXLmzLAtM8O+WBXHUNZYhnxHD25CYnGT6ZKSybQIgADL\nw2lnZeK/jUZ9tF2sdUvYAHM5izb9q+l1acI+A8dWnewr47WJABJ7YfYEADIk\n2rEv\r\n=xNzo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIXR9kYg7vGrsClR4sofsipyXfHJ2jviAFYqLXT1eCLgIgMw3xgYMK3BfHVLAojdVQAAfF0TUObAEmqT1/o9owkoo="}]}, "engines": {"node": ">=10.0.0"}}, "14.0.0": {"name": "postcss-import", "version": "14.0.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^3.0.0", "eslint": "^7.0.0", "eslint-config-problems": "^5.0.0", "eslint-plugin-prettier": "^3.0.0", "postcss": "^8.0.0", "postcss-scss": "^3.0.0", "prettier": "~2.2.0", "sugarss": "^3.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-gFDDzXhqr9ELmnLHgCC3TbGfA6Dm/YMb/UN8/f7Uuq4fL7VTk2vOIj6hwINEwbokEmp123bLD7a5m+E+KIetRg==", "shasum": "3ed1dadac5a16650bde3f4cdea6633b9c3c78296", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-14.0.0.tgz", "fileCount": 10, "unpackedSize": 37437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf17tSCRA9TVsSAnZWagAApj8QAJejRthMT6s5ApxxBRXJ\nGZQ53P2/c9bHx3oDvhNlYJJDkRZS8e+SgyatknAEHM3kMnf4eJ5UEBxIBFWX\n2OIYsSXE6t/W/CGOl3GHjDsW6OdnBCG6FFple9X03Zzfil7QxphH3kFxyRXN\nCuYujUXkGglVQQqxW3sARPyP6xdqu8UuHj9xnjCvlnvt21kjDI2KxvnpdtHq\nsLdtuQdl+P5u3UQcy3PCNZ71ourpgH4UsKJIDxK7j55vOtOqHEf6I3i598Ve\ncxfKUNSlH8kwRgF8Q22NkOK9Ym5NZTMGhaqcnCxM0Fj68rw8ZLOkb6C/ouM7\nO7+jQEZ6xYyolqST/zhTKm8JWzy9rCNJnujZoDXbMyI0MtUnQU0ULu7gi+Cz\netY2ciMlAvnfnffVl/ioLsOsj1ZAqJSTXpcnzYyurE+ctJndNkNvBJiQvpA+\nQK1XPa8rbaUudJFkerYmW9oshcGBiVjqoAFLtO4cLdp/JU+eQLili2speYH5\ni/iqac+tjbIpDjS4RUaN0Rml7ZkCSMM9Z3ExvKAz2+g0Fcu1dlRGM3bUmBjO\nOHnTp++T1OhGFQQ6Jv4z9jj+Ma9sfS4m1b6Ay35GwRP6iIpd+sq0szvATfwZ\nFFHrm0giKqLdfBkdYp7v0DPZlAIotvNdhHtmnPmngSlR8q9JeKkJYBzF1Omo\nKLnn\r\n=eTli\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAVKTgINFkkCh54QvTrHpCMV06ze1Hv23X4RowbUD0znAiBWntLzo16dVxMaBz/AJTZ11u4qi8N60Ifo9RYAv5l1UA=="}]}, "engines": {"node": ">=10.0.0"}}, "14.0.1": {"name": "postcss-import", "version": "14.0.1", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^3.0.0", "eslint": "^7.0.0", "eslint-config-problems": "^5.0.0", "eslint-plugin-prettier": "^3.0.0", "postcss": "^8.0.0", "postcss-scss": "^3.0.0", "prettier": "~2.2.0", "sugarss": "^3.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-Xn2+z++vWObbEPhiiKO1a78JiyhqipyrXHBb3AHpv0ks7Cdg+GxQQJ24ODNMTanldf7197gSP3axppO9yaG0lA==", "shasum": "6a3f8f2ce74a95fc7c72ecfe3eddfa0e9124e677", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-14.0.1.tgz", "fileCount": 10, "unpackedSize": 37665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZP9cCRA9TVsSAnZWagAAFygP/jh1jMEhKZa82GplnZKd\ngltGNhSn51APnoogyTcJxhvPAXSvGPUlM1bjKZ+ZHoDMN+XpFzwFE6I53mAv\nOvovDLH6856CTrtcqAkujVDHyBCLYIfs+KK3uakpMa7S2aQ5tkpsDfBdC2ww\nwjiLFV0FEAqclU6rn5gOalsaTbYS9CmajWCFQJDxgSSx7DKviL2tMXOrDgNf\nrjD4QzlKclBmFL71B7oo0XkZ+X+0JhwStQRmnIaW3v4cXKbFiBTclhSckTLZ\nn8DX63AU3YZ0pysbWfu04DaPbJBJM9l8nDSTVG2jprrBLjgx90B/3pHOE0a8\nHgBTaUaSiFq6283xEwA63eI3hRix6p5AWt7wXD9cVytDvG2QSnhgYu5cxU0E\n0b1wldwBNclzduiB/gpTQYM2F8uRwDdMGx7nnlXo7wGpmbHuUvkDcHgg8Mn2\n0/zgNzO1O7uJQq/ScIpkFqpWNiliOIl5f7Pk+5jMWxnDVptjhE4D6WDMWFJS\nWR9lv8uq1/IeVxLFFSHRbfgkotFuVFNN6rt8uKMR5FUrab9IkbfJKDlsy8aM\n7PEy3P8Ec+43nK2et2WjAWoXC/Xz4RNXn486/baPgtJ/nnAVaL8Gj+jNmVp2\nPTxJQIsVYSDC6Toa1EeNVmYWT/iGg35wKiuY0gziOJNKruSNJsYuUAbEbLpf\ndIBk\r\n=6d7l\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDjp8ikeReDyDTJZBLB64CnOuRR71Z9IPvlk84ggvBp+wIhAIN58ZkVr0g7r3lRDUf3e9lpxxwEJIk4UaOvRHEzn5xc"}]}, "engines": {"node": ">=10.0.0"}}, "14.0.2": {"name": "postcss-import", "version": "14.0.2", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^3.0.0", "eslint": "^7.0.0", "eslint-config-problems": "^5.0.0", "eslint-plugin-prettier": "^3.0.0", "postcss": "^8.0.0", "postcss-scss": "^3.0.0", "prettier": "~2.3.0", "sugarss": "^3.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-BJ2pVK4KhUyMcqjuKs9RijV5tatNzNa73e/32aBVE/ejYPe37iH+6vAu9WvqUkB5OAYgLHzbSvzHnorybJCm9g==", "shasum": "60eff77e6be92e7b67fe469ec797d9424cae1aa1", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-14.0.2.tgz", "fileCount": 10, "unpackedSize": 37668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmXiGCRA9TVsSAnZWagAAZZgP/0tA8n2OZwnet4qOmtHi\nI7gVXgSDKIt4900allwuuj2A5xNbcvO9nuXeWGKFP0BJNQg6aMQUWFaglUid\n2Cx0jLM60VqSp3fJnVnLK8njesPmS/+bmKyN1iKQFWT+C+AnEzvU5h0GBjXj\nXG6fAqu1SbWZ/3f3Geun1vfh/rR+qwnifmovmZyjc5KaVLI3qdqVnb+tXLUp\nvlwybhEGRY+IXUyxX4khHhf6AucTfU1swsgkdB/kAMPX+szalmbdWYBClCt3\nTaLo6PLEZJX/gbv4UmHSjrLAZ6IM9hS0TZgjYs/obIxSdBxHxwl0WgttpkX8\nOwiV3kySapVbB1kRI3owqaODlA7oGHCL84P9/fKjy1+wui1qZSjHPCAMoBU+\ngkb+ZYVBxS3nwb5xYCrvKV8PpyeHEMI+G8UDRCiyRfBysMzdhz1yPCSD4LLP\nqCgzGMBHtjqeSs9+o7dSbIHgOi/EPxc31ii3oLPC9F/7D8jqB7zFxUOqbcfM\nRx0N8CvvMp1/VPCVgHiS5MSYD5H+ghsvA1T+AGZIZ0hDkXbP+pj42z/4dfcT\nzoq5P8zbbm2ep8iRMjp85ZVH7wYdwe63S7sY1xAuDnArbNF4lV/W/Ohy/8zk\ny66GV4V03uj490K/FCbhzk0lgSHGIz7ZF0ZgPKm5w61GInIRkj8fTf0rkJOw\niA2h\r\n=FJFb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCIaMHTYxlg7ZnBfGDzGV6fwIb1NBUbt0VrR6Rr3yGEVwIhAIxNiEf0KyAGZ76C29SrKsViUHhVKHtVkvhhtZJbPjAN"}]}, "engines": {"node": ">=10.0.0"}}, "14.1.0": {"name": "postcss-import", "version": "14.1.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^3.0.0", "eslint": "^7.0.0", "eslint-config-problems": "^5.0.0", "eslint-plugin-prettier": "^4.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~2.6.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-flwI+Vgm4SElObFVPpTIT7SU7R3qk2L7PyduMcokiaVKuWv9d/U+Gm/QAd8NDLuykTWTkcrjOeD2Pp1rMeBTGw==", "shasum": "a7333ffe32f0b8795303ee9e40215dac922781f0", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-14.1.0.tgz", "fileCount": 10, "unpackedSize": 27007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOeb4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ3w/8DWyMVDCBJfYBZiNUa03lxIej1p8OzkxK24CteeYOgFGaYMzk\r\n5oE+0O39zcKYNMdVFG9ZUiqN3kfPdtRRO2Rgxr6sAwtI30Inea8+CmWIEnd3\r\nCkz4xOdqOjpb6HHQBO6+fVo5rLg9nUwxXugNuW+YqKoN4CYjG3fZThf4VA8P\r\nQgUsiYb4R51HUPR0m8A9ISPVoVt2Dp+vpSqEp504Pv396giykdcCEbZqcPFD\r\nW5GaSeILdFObFmKmdqzzqMYt0ArfOhZNvHGm4uv5fn+XVR5LKiLnU2QoiC4K\r\ncxy49pDDKppLw0mPx9hIQ88Bw8Hz7OFGbko0C1kUMob6qevB2o0+ckSSy9AB\r\nOXmp0dawCzsInxQ5v2hreqYdD5glzec2UJE/NeceA0ZOguC1n7VH9uoR/Et/\r\nGYcwRN+vVObI4QcKExRwKY7oVj+yKha9+PYmPar1joTBUu/TbM+t/7V8JYpv\r\nrT7vA9u75Ek+ODn32Bps4uaYm+jkQud/K6iB+0yWSImwHNEHzkNKIdLVa9tW\r\nLwt6EWkgJB1JmEB8GKMgLZ6vynUoQbqKtsjOr9PWZKooROiiX+ndsJh62Zqj\r\nOfw7QUK1WedWzHIfOZqKjpC87lpYHu8WgcZlNsx1HngoqdBp6x7Eb3iQXHsT\r\ncP4wEgOZ4C7uE7sf2AV1DQNMCjdGRkkIh58=\r\n=2mO9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG2x+3zePJARORL7eJNBKtIX/RaAgBuZXgLx4IArHfT1AiA2GtFI4htlE4XbLTKbeDb3o0feSgKnYkLeFLFSGB0ymg=="}]}, "engines": {"node": ">=10.0.0"}}, "15.0.0": {"name": "postcss-import", "version": "15.0.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^4.0.0", "eslint": "^8.2.0", "eslint-config-problems": "^6.0.0", "eslint-plugin-prettier": "^4.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~2.7.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-Y20shPQ07RitgBGv2zvkEAu9bqvrD77C9axhj/aA1BQj4czape2MdClCExvB27EwYEJdGgKZBpKanb0t1rK2Kg==", "shasum": "0b66c25fdd9c0d19576e63c803cf39e4bad08822", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.0.0.tgz", "fileCount": 10, "unpackedSize": 29195, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEPd9KzYGLNPAHDBytoVW8MioDCwQmFhr1a7Emx8p1ngIgUJCh3y5PkyXaPTZyTnmZAOBT4uNq9akamoBPgNXmKpI="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDo+GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8pA/6Ar+/cc6t4KoL6pCaA2kk4rrQUagXITdBWEmEKwXb3VPVTpOe\r\niRhAAsX39EuiIJENzRD2vovZo1q5mIULIQEFoSqJP6sdgGghU5ChGFmzZCC2\r\n3LkJzagkUi7dioRiDruWNpbmzWcITgD9tJd4QM1Df6mQvFpoeMppTz5cKYxJ\r\nYiwIOBQbb/2Td4gBDq6+TpUPvWkbyGVxLCVCEjT0bxtoWqArmLbs8IvteSMM\r\n6pbqkuFyabwXeWSkooihXgqDz3QHn47nqAo8QW9QNTvrqeCFRtbUI7XV7jfs\r\nTanNE27ZD7Dkoe8TbmH6xoxf8AAS4mOZ6q+EIWcEUkLJLmCIH1uzhl4W74q1\r\nMP73OTWNs5IWzHGXnIWMPylvmxHZl0SDTHzTEbAeH0q7WuY0Y3vkkg3FvdL0\r\nSeJWBEbJFy4N3U4GUrXpdwG0AZ/aAnBz+N57uvPFCzssT3My/OnZ5P34gdcj\r\nlLVaQW8HDTvSSKwSYMObeoVFko3ZV2SOl/NdnT9OA+GA2K1DiXsEaCBKt/34\r\nRwZK6WDwqJj5i/UHaMtS3KTf6U3zqAUASf9fXbp7/cTbt37pNedhiw9NKlQZ\r\naLjSo6zUhHQVsLBz2aU4ws/AISamrPF4iJFGUsXs4amiosp2m0WwB/V8A6o4\r\nLw26wesk7Hk4zzvs/mROsDwnkYiSMLQ4t1Q=\r\n=LTau\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.0.0"}}, "15.0.1": {"name": "postcss-import", "version": "15.0.1", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^5.0.0", "eslint": "^8.2.0", "eslint-config-problems": "^7.0.0", "eslint-plugin-prettier": "^4.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~2.8.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-UGlvk8EgT7Gm/Ndf9xZHnzr8xm8P54N8CBWLtcY5alP+YxlEge/Rv78etQyevZs3qWTE9If13+Bo6zATBrPOpA==", "shasum": "5887da24440ef259324d65e08343437a43ff92b1", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.0.1.tgz", "fileCount": 11, "unpackedSize": 30168, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFF+vtYCbgrYJr2sxXIvcWrxjmuoy5jUe2j6UkePADKeAiEArWosMvEavnncn3yoDm8zjeVU1iZvU5a8i9PLb0uhA+4="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiRl/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH2A/9FMmqe75v9HrLmSoRekfYCMY4WbvpW7PImFd7XNzXsicXFqmb\r\nGlMlNJJutUfw9Zg8yEnFemPOf1Nd9efRSmEjIRNUYfXBncDkzYTZoNksM3Mk\r\nuU7hcVYKgHQIqDNCb2+t4tf3MrOUa0kbojkrAEzWiA994edZcmaACgEUPct2\r\nphVsX1cIjaSunAv0wayKymW8S4gQKO1jG/nhUe9WPu5P5tasqZLOws6NopuH\r\njUtmySRKTNTQ75Ppm+TS22qa7ajoI2zLR4DvAHKd11DIFeBTY1wq6/YS46WN\r\n0aaGMTGzeQfJp5kvI+gnzE+Wnx64F8/9lxtaKUTFmjvBZbI36UHMZ4CUWInR\r\n+5uJmqv9L845h1HAreeimzCfu17sqQqs8FWMBWUhnxRPERAAG9jbs7Jbcmgt\r\njXVreRqJ5WzrtrTOp9jKRZnxDG3f04ABGgdtL979/cgMKIx1jjqwGsZ/y99S\r\n99TyHFoRBz3ejQcns9YC1C1jbImVW3FaAiofFVOpZjzkoniiiamFVO3Ld+CJ\r\nn9DYJSkNn+AI2Lp+isP/nIV8E8QStC/KJ4Qv9uhc1Cv9Dm27sLV2BlENutUG\r\nC+86/vBpDnx6JYtQV/K7C8Opi9NonxnpHz5SmmjAIlrWKBOuYV0HyllNr+z/\r\n5+jxJpIWiwKubT2XdOyTw/LsYV8Rz1EaD9Q=\r\n=Ix3B\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.0.0"}}, "15.1.0": {"name": "postcss-import", "version": "15.1.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^5.0.0", "eslint": "^8.2.0", "eslint-config-problems": "^7.0.0", "eslint-plugin-prettier": "^4.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~2.8.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==", "shasum": "41c64ed8cc0e23735a9698b3249ffdbf704adc70", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz", "fileCount": 12, "unpackedSize": 30855, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1CoJ8YLrQLmTuqyY0tXQ9BJooVmka/ofTtsm06R9UMAiEAmo9yZfsrIHi33I9EA9iEdnJfRP/bdQwQQ+c3udiBKTQ="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkQmCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoihw/+L3UbZJ2MbKwEjtKIXqhuXJR25X8UQoPNvgfRyB/FuTZpKZU8\r\n9urPFxBGx8vN8RidfN7tclYWGQmTFnluyLUf2eD9Ryf/P6AZCQB3dn7YI8jO\r\nD6UYB5NixyHUyC3CEG2onR05roFkKRrQvRfLNEPpBgrRAnCBmsdoc1yf1NF3\r\n7dygt7eMKsXWrPqcWFvKnb97SxPBTHITVFsJD45a57svS6XjUGWCLK9mgHeJ\r\nnDw0oOcWmkMi/dWvEzXz9amMOq7UGiXm8j2+w3tgJ3L1HDW8eXLtj3mkwNe1\r\noOgAg8kvFPxd3QniB4AqP2LZx9IqACRiLMaViFCK8kT3KdsiH3MbuYApWfv6\r\n1098TMOjolXWvDnqoBJhhomOglBmg0hla9uHn0dq5GTYgZ81dl27o9hyfhUV\r\nCxkB1ySV9LxPmiuAj+cOvi05ppg9wiRCdGIoxfYXwmCMUhy6ng6/wss9b9W8\r\nbBOK13jKNwI3H0CiDBWQaWuaV2vS098/aZUZ9BIGQShJ0j3PBx+j4LUd2o2f\r\nN7I2/rEfw5JGZmfLuQWVTwrjtwyoKCdcBXA/LKSjpsIyI33o3XIlNKw4vuP1\r\nkwJS7r8rs4+E8fGXe/kL7USLFO3JNT616QzlyvOYpyi5DmabSeq/CLNpi9ca\r\n4CA0nyEk67yQ77dq3+X58JrAw3Vrxq1ghcA=\r\n=lSVR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.0.0"}}, "16.0.0": {"name": "postcss-import", "version": "16.0.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^6.0.0", "c8": "^8.0.0", "eslint": "^8.27.0", "eslint-config-problems": "^8.0.0", "eslint-plugin-prettier": "^5.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~3.1.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-e77lhVvrD1I2y7dYmBv0k9ULTdArgEYZt97T4w6sFIU5uxIHvDFQlKgUUyY7v7Barj0Yf/zm5A4OquZN7jKm5Q==", "shasum": "2be1c78391b3f43f129fccfe5cc0cc1a11baef54", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-16.0.0.tgz", "fileCount": 15, "unpackedSize": 30273, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFM8mjAZGgtMoaMLZ2LSyS8jR3qWRDvm6Hl3W7J7tXQtAiBpaolgZ/R7WGRcHXMrpsr86M+x+xIKnCvkdPhJ58dRtw=="}]}, "engines": {"node": ">=18.0.0"}}, "16.0.1": {"name": "postcss-import", "version": "16.0.1", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^6.0.0", "c8": "^9.0.0", "eslint": "^8.27.0", "eslint-config-problems": "^8.0.0", "eslint-plugin-prettier": "^5.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~3.2.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-i2Pci0310NaLHr/5JUFSw1j/8hf1CzwMY13g6ZDxgOavmRHQi2ba3PmUHoihO+sjaum+KmCNzskNsw7JDrg03g==", "shasum": "c2e0478c5af8cb39ab3964c35d8fee8e70c362b8", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-16.0.1.tgz", "fileCount": 15, "unpackedSize": 29971, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICnPWKlq7kku/tz/Eb+86IbHkqPcKDjS2wKNEmtDWXp5AiAJKhbgOATPA4UDVTIzBqGVOa9hxXxOs9rO1jdBd0a5SA=="}]}, "engines": {"node": ">=18.0.0"}}, "16.1.0": {"name": "postcss-import", "version": "16.1.0", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "devDependencies": {"ava": "^6.0.0", "c8": "^9.0.0", "eslint": "^8.27.0", "eslint-config-problems": "^8.0.0", "eslint-plugin-prettier": "^5.0.0", "postcss": "^8.0.0", "postcss-scss": "^4.0.0", "prettier": "~3.2.0", "sugarss": "^4.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}, "dist": {"integrity": "sha512-7hsAZ4xGXl4MW+OKEWCnF6T5jqBw80/EE9aXg1r2yyn1RsVEU8EtKXbijEODa+rg7iih4bKf7vlvTGYR4CnPNg==", "shasum": "258732175518129667fe1e2e2a05b19b5654b96a", "tarball": "https://registry.npmjs.org/postcss-import/-/postcss-import-16.1.0.tgz", "fileCount": 15, "unpackedSize": 29924, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHipKKOLcfIN8AHcoXRLbAQN48a19XMByIRcw81X8ABLAiEAjxKzE0WbRNLQNclGyjts4dpPexvq8utxAge71vLOBzU="}]}, "engines": {"node": ">=18.0.0"}}}, "modified": "2024-03-20T20:31:03.934Z", "cachedAt": 1747660589184}