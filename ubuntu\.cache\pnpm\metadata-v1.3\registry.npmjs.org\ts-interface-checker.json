{"name": "ts-interface-checker", "dist-tags": {"latest": "1.0.2"}, "versions": {"0.1.0": {"name": "ts-interface-checker", "version": "0.1.0", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"shasum": "2bfc188fe87f04eccdbbcc52db3da5a0918d5a1f", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.0.tgz", "integrity": "sha512-VzzOl1FHHNep+yyrNBDOAglNeh1NPEJk4VVBwRlXkIx5gE1Et4dnFPuqica9HYoKMu2/VjIy3AdtLyFf3piMjw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCunkvRQ4n3SkMeAX+rVf4qYcsEZbz//VPNI80B5rN9iAIhAM4xTMb9j62lVnuPzm9fJPWnRYFm5+9mKqYZa5m2G7AZ"}]}}, "0.1.1": {"name": "ts-interface-checker", "version": "0.1.1", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"shasum": "baa7787ad723c3d15799c7193e02f63af8575a23", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.1.tgz", "integrity": "sha512-mFN2nLZGiyUPxsUSN42YiqUTuNo5yygMD4pRHdIxdncJZmn/xwld6axY3e+SN9hRWO85KEQddjPpcWS0rDWUYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDtFkBa2Rw0IY88IHjmIvHw3JW3NadJFKu6Otem1KIdEQIgS+zK0V1xy9DzUyAvMXj3OttujHVYJptREe8Amcdeogo="}]}}, "0.1.2": {"name": "ts-interface-checker", "version": "0.1.2", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"shasum": "ef9bb1596a4c4c6886fb359a6005124581505230", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.2.tgz", "fileCount": 9, "unpackedSize": 47206, "integrity": "sha512-uh3VhLdN7GNf3jjdfT6h/2BCUFw1I2VQ54GYpRKHJZmAfCuCFLRdy+m4hGX1e2ytZag2JmmdzgdVxcD3SsOKvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCt0d9/9UzSHW2Bkt1mt4B6t++U+YZ1eAoYh1bU4mzvoQIhAM+y0+AWn0de+Jh5LCSQDQiZqXD+TGTcxckGdIpDIp9C"}]}}, "0.1.3": {"name": "ts-interface-checker", "version": "0.1.3", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-pGc88wk92iFnlYx0matsqu6isU/n7t8eoJt3M+DHg3d6zB14RkU1tEPHiwgRWa+FZZJduaqPLopGyDzi6ixDyQ==", "shasum": "fe2e0811444702f2d90f87c9489f09db1ea43ada", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.3.tgz", "fileCount": 9, "unpackedSize": 47413, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC34lpksSULet5WYSdlyGK7FN9h5hdZGqZDwAle1SkJeAiAizWKZEMm2sNYucJsM+pyHWA0B6uwKdiJfuq6c3Muqkg=="}]}}, "0.1.4": {"name": "ts-interface-checker", "version": "0.1.4", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"shasum": "3b669c5b8355223a4680c972c1ca11e277737342", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.4.tgz", "fileCount": 9, "unpackedSize": 49697, "integrity": "sha512-X17YekjsTqDiCYbWAsNtXV8SmFfaFmI+JmO1N8oJWsEpqb8vNO4I+A7BjxxBHRmn3JbpoGrqlqVrnlLFIn4viQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCPcvC8XbXBQ0I+fJImzYUOa//mYY9jkV3UcfCqDQEV9AIgIi65Eyn3f1zYPLTmh0enItSQb90EaaNZqQmvnhk3e00="}]}}, "0.1.5": {"name": "ts-interface-checker", "version": "0.1.5", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"shasum": "60d554ac29083bfb8cd51f95d85c21acd1ea662b", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.5.tgz", "integrity": "sha512-C5rfMfVB0N5GUrNPRzvWvd1sg+lrtEYYxD6CuUKemBdRJDb5TT9o71/g628vCJkNiKcZTbOSd7Jd1XvZDMDq3Q==", "fileCount": 9, "unpackedSize": 52192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0/WgCRA9TVsSAnZWagAA7usP/2mxKJ1Yx8trlRoiPbim\nriL68FCi8+gXVKJ8Kms+XOMf7q2h5LwhI6FJ9nPvqAvmPX0B8/jHa5AUEUlC\nieVU8gDZDtLDZ4LiidvjVYnVLTEzJrSON96/g4n0IfZu+4evhU4E5pW4q/ra\n2ZCvTOH78cww1rjBoOgjZ7/iWpt9DkYRO8J3LjHm3vEzcBBtbiUaKKBeP/gl\nDObcf79VnsI1c7IiAE6tUNZLTCIKXM0gz5qD+l167YowMgPcoALKRn69Yu5t\nwGTEj5gvSvC87Vl7GAi97yCWWFIZxWdMF8jZHl970dSJ6Mw4KOmqxSQxys2C\njJQH1+KeWJ0r2njkDqC3Bh4ceobQXaAF0V6B+kxvwUYMlSbyXieWjlHdXo2k\nZBucRspkIb9PuNypX/WCQpKTWz7ngmpTGebu1sMrTQrPRp31g07hFoNuE2Ge\nas3ts1b1+BBMUirsa3Pz2jR3EwO/ipNe1ELSIvch7ReiE3eQqSPitEORTQ0I\nYsbOlF6WkBRSxFEAqhVWfHybZt0gWSgsKf85Bsgd1ccIczgBEsZKB24l0wqa\nBLa/MDf8gJeX7gcxjshCvEs8GoguGLRPf0eHs2SRWP9VtnO/k189rkB6K/QH\nljLQL/xkjWBb4oMPxEsIGbS236NRhHkp1A8UgemlKQqo874xrglttJ6klP2b\nUqhN\r\n=SBlP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBs5ssfbAU2xBJjXmTo+8dMjzTQMU3BLxmMYkWLVZpzlAiA08ixYpy6+GbXMfxMH5GQPAeUJnIlDv+yVDoSXYvFqHg=="}]}}, "0.1.6": {"name": "ts-interface-checker", "version": "0.1.6", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-vuhYd/G9vX11FQPUUp0YWJ8AKQn68VlCKVzAXMuN5/4lVtGtlnDsCfo8QkkCAb6db9xoxg11QixGeggH5NCF3A==", "shasum": "89e7e0df05b254e591f2923bd2a2c261f4320dbf", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.6.tgz", "fileCount": 9, "unpackedSize": 52392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1+9nCRA9TVsSAnZWagAA4wcQAIjbbaqKLI5pBf3Vkr9a\nTBQ2gwye4mckKiEFAcAmsK2ZFC6LuBhG/2StKF9dcZYbWNyrFnf5wADCeMwW\nxF2zQZ25Y+xn4IWGv2iwH4snlSiWM+Yixj9tQOaK7sD8RggoD1I4CCR2nJta\nJy8pnhnQED2lrnKeOTnQkIzipG+/O/+6EZANuJx5OCG7UAgvDV7othYCsSCh\nfUYx9n/n4eRivDiBLKJSRrJFIaly70LlfTY+aUcDEu+EROnfUTZ+WvEGzaE7\nx26jOlm0Lbq6/8E8nBN9sHW5LtZSVtAxfTQ0r7Xso8PmoqugMoQ17t0TV/l9\nELsPTNWSoB8FsIJWxI0ITZqyrMIO0/6IFKND+XljAcWA2WxKf9krmo/fkONZ\njKI+sAGIyT2y/2aisFikGhLzoQp232cVq62RLJ5D40fZHdnKNjfe5t1Mq/xV\nKC8KLT10zsfB8SYmQRPHzO5Iv3M94uBkfyMxJCgW7VhWl4HO+sQAOviVFv5y\nwuqjJQ7BF1EovLX/OjER/qbBCydDDM3dNAVtFkqudq60qhsMQrtOhQ2I+R6B\n7/y/HcuO4xho5QJlZWVHieltxb1fOiY0O1lhct+Eo5vNHiOXJkMXr+arl25M\njV0Z33Jgg6OLjM7h49vz2SG++1GfapR6G9bRa8c8MHpBVn/xM5YiCD6chyCu\nYeeo\r\n=T05Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICXx8XuH6UshtCNpbnWJqu3uXhslGuWF3nvM+R/DpZp8AiEAwH/0dWWZzCGR7WwOSr79ZauzcDYcwt1Rj5Hkjwo++DM="}]}}, "0.1.7": {"name": "ts-interface-checker", "version": "0.1.7", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "^4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-nneiPmUk3zegVC5NA7bvNMgzmcljidic35Z6gMDNggVd8xKBnLzVVZzm/Cdz0LIU1u6uw3qcwuWfKQnyRL8+ww==", "shasum": "77977d82510444974bd5684041a16d4a0de7763a", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.7.tgz", "fileCount": 9, "unpackedSize": 58507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPp5NCRA9TVsSAnZWagAAX/QP/iVCUi1ebmLD8xzpwIAq\nvdGtG6lSTUcXsqvUTZX8pfag6aTIQApUEfA1s7FDGHFtu4f2ogi2HSgFEuEZ\nVxc62Ifcp0cCaVXNZvx8OStFkGMwgE2bGNq//yOneWT3UF95QqA9kxMHmQo/\ncal80hG+EMln1LDrXunObIk1DDKXkpaO0HNEs5yQXsC7+lAlGlkXy/EwWJEu\n66myK/K48tpaXaoZVVsXgCQbqzWmWYGaR6WM5Z1X9jJofl4MnDlJCvzwN8Yw\nI/U0XNxdpSj6cFJYaQdRElq/mNUsKk97Qf9U1m6v+LmKMarepAB4Nnh8PJXa\n/aXALUtUOyt2YGxa/WY00qUow3dkg3bxskSppiWDWg4p2/c5dZDoO0+/Ermz\nyBXD7sl2TJ2O4I0F/vtRMJjCabWlbl73rB6Rg/EiqQbIIiT6QBIVp2fqGd/J\ngNBnOq5H6QJNj3ASwB4S/qJD7kRGrXCZL+U0D3VE1E1ozsyZB9g3DdfyVgEo\nMiO+JaaFrN/dnOwNSRE8ec4zBApJsYBJWwGnblbykPMCIarNUsLFwYDEcyeo\nBQe7FPvU/HbLJA193sT8G99Z78XaOrH9eBHrE6WU1ctVhi8OAslEIZ5a82Zk\nWj0GOSvBg1DXTANQCeBFLOgpu/ZgOpba5g7PNXayokAOTtcIp6IbMPyhwm7e\n3BI3\r\n=wcbA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC5LijQAig3uFGz88+F+ELR7ICIq6BXuuI+bUWGd1QaBwIhAPOoh1MbDO2DjOHjo94bnOvtxKAswejle9iIc+G+GtNP"}]}}, "0.1.8": {"name": "ts-interface-checker", "version": "0.1.8", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-qDrLZ+M+wYlC9YAwmFip7b3qFe5mxiZaFwDmwe6C6Hu92gevxSvDabqSat6qnDy9CHV7FNy5lXRuiUYoxQ+PlQ==", "shasum": "c9dc110b2d37621cb8af701f41979dceae7aa080", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.8.tgz", "fileCount": 9, "unpackedSize": 58953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZNPWCRA9TVsSAnZWagAAQF8QAJVI8GPdEBLG5sY0khEA\nPVMCYqB1ig+EpHugKhDekflUYL0hlT49kJU/n8ax4eT8uxET0rxxdx1dJR0a\nQjyCTgNtMNNQ+HxXdaYFuC80XDjxnWRMEgEjAXmxCPI5rpP0rpnFKg0it9/g\nhaYHd1bjO8Mhw7+BBBI9szZFIiqdmqwhOSQHuptc0FTWEuGktUxTXUEkVt6M\nQjz4ntgiY5OMwg1r+4leFqbs4OiEIaQnxZ5fsVGNC77GxUiqimDv183tanWO\n4POi1tWep/YhuTLUvw9+zLWejqmlI4UFPfDaiXQpaFd+kgIXtGyhLaypaeSD\nwNneqLhyTtCUlS+jgCS+c3myJTg0uqtezbsFHqt0ttBLQ55SVY6pZgdU5kXM\nofrWQMeUX1KyGVa3zNuB6wYhHlGP9gcxse9PwaZH6vBomXfR/YN3ZuAdqDWz\nsox27tx8hPJ4pg7+yqiMEjIZ0kfpbIMJp1/McmEsbT0OnXrKpeFroCTWJCiG\n0nGzqXojLxJELZLjdo8Hex+OA+yhd+rTFrPUo6oZzVa0mB4aJBdafygztjwS\nttv54lJo9MgarsBsHjPj+YV00+/b2QPXva+ANtmVUsdIw0PU9CzWxaUTvmmJ\nDgw1bwpLF+9HY3kriZuNPP7NxWixGv+EpRwsmcNzpG486vkZTb0y9LqRJEEM\nlwVc\r\n=+ukQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG8lQ0lmXa8M52AKGiMmwnWFOft2WMccM3A01mYJj0J7AiAoxxgI1lqCMAGWG7e7gl/5ZHZMHNijdsj1sFN6IiFU+A=="}]}}, "0.1.9": {"name": "ts-interface-checker", "version": "0.1.9", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-QSh0df1vUxNFhdPrdUy5bzpJe4nhZwGmx6TSlGKiuTYrawotJ/bOc5aKe2P/s0xhzcvxiq8IJlviv3XYGQ3ODA==", "shasum": "2e20fe64d04bcbda9470252eb18a46b412437c91", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.9.tgz", "fileCount": 9, "unpackedSize": 59656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdZ1wTCRA9TVsSAnZWagAAtiMQAINHaY39FUolofM/bnnP\nmTb5Tjm7CA7UcuONJL92SHQvs+Q5dLde21yHuuHM2+3XTd7NYqtMnyYU/xL2\nC61sg64/dHPPzXaf82dU8gVV1bF1icFjMm2+B3ePqJ72LKocoBtyanVvi4/p\n/DFuV3Ywqfjadv2icGfsVL6+v+D9AyqrwKT8PbXAgI+XmhxyOxuRDmtdJCjW\nXk4KdPuF2UQgkxPHK6LRmJTMAf/nbjSf9854kIlIdMF3j2MiLdwWQL8hAGAQ\nn3+3wt5iWdbQz9fefzCbyk/5uXUqE9EbB29Dp9GUwMmHP18vNCnofRCG0f55\nzJVpHUheX2g6aA2l9EWezfv9Vor71mBTUwNW8wDL3p/Qr227Gv0dg5KH5IcS\ncE1Vm7QIEFRLJx0chFWm2Uwh5DYeSBf+2W6zOQ9B9pjRnKWBRZTMRAAR0Yqg\nJAf4JIjsPC0mPruktt3/fRI8olVTl+xLEjXQEYSijZ/8wJtq2OUX330RNCAW\nh3nGVcqYsv3328r8tA6/i+HTRfEzmZ3yqgonG8Y/LizqcslNYUxvNaIelBuW\nr25OP3mSi1FnWZwNG4nu6i7PQBNTfWDfn3olnRuF4Mp+GSI+f97S5O0rTN85\nc9AkmGybv0XlOhr9MK3vZZcY6UHc+aeyCW42Bm36xJnJfCG3BCU1XBROSSdH\nKVxi\r\n=UqNQ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLN3TtUsyC4CPt7cITJYUP53ok49WTBv8+25ay9iuWaAIhAJ6uMo+sR0N2UI+vUyZk0GkKt9DB6Llm0eMTVwTCgiLy"}]}}, "0.1.10": {"name": "ts-interface-checker", "version": "0.1.10", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-UJYuKET7ez7ry0CnvfY6fPIUIZDw+UI3qvTUQeS2MyI4TgEeWAUBqy185LeaHcdJ9zG2dgFpPJU/AecXU0Afug==", "shasum": "b68a49e37e90a05797e590f08494dd528bf383cf", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.10.tgz", "fileCount": 9, "unpackedSize": 60216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+YWiCRA9TVsSAnZWagAAO5EP/iQyzYhiuSyt2o2oN8P3\nZZ+Nm8dzjx3wTe/EtOMRB1WODVhE4PrIy5B6gw9Ea1Q4O0xfQYE9xNjsJPD8\nOIzMMzJ6R+zKR8DCPr2yCfjN6UeUfBuGyPYT8KdRSkryaB1PN5uhsmFwDQWv\nBmKlNOM+9P4ujQfgKRrwq5DXLz3394582EGwRFkosQnK8HPK6HwJtQ+oSJMr\n2W3nWxCkS8pVkcd2VCnDZAvUSxUdXAN72ecsjYtDWoP8mMPILL+aB8LlgMrf\n3UXjP+ImEa14ZKc2FsizXueyhbsE/+Al6CmoDnmXxhLf9XhAAIaTZCD9RwHo\nmmy9yQDQoJaGvFOPK4nyvpPuDvxVAKod3WNUNB0IcIFrdvBgABG9anJtZH7d\ndt0pAK16iXZjlieiHDgdROcVp6iRdd9Yhoqlo/aHIwSzeJu1gKGLAj3fmU+T\nLffpzd1GRy7wkY9PHaeYhgRSZdUMt/QlKXwofqvyvf5cDV/yucCVTER78gr+\nmLN8L36zTMsCw60bLlAXuDeg+SxH8c947Z++fK4+rHYEqbAmSV0qQUgKOxj/\nIAzvLWpxGsRPFch0L3VyxpI61ImSbH0biBS4hoRDMq3HoyrLFbWYUsbkp6D6\nQr5L+ey6IrYqIawr+lD4hQTp4OSxKQ9brEcyoEV1btRPzb8c1M+30TTnZH3x\nUME5\r\n=aHb7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBbOUrXdWY3XS82R3F1mxIXoEML+C3lOElcqX18e9/hdAiA0tXFG4wAHQUd+4fW2tCj0LxR1vVoLr0+lbfGrdYXAYQ=="}]}}, "0.1.11": {"name": "ts-interface-checker", "version": "0.1.11", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^3.5.3", "nyc": "^11.4.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-Jx6cFBiuCQrRl3CgoIOamIE/toZ8jQJbIlsLGpkBiUpCEUyFcyZ2pvjP8kSXIcz8V5v/murgm/5EfIQapUmh6A==", "shasum": "07e7eddb08212f83fef12c253d0cefa8c70fe1bc", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.11.tgz", "fileCount": 9, "unpackedSize": 62065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewOtqCRA9TVsSAnZWagAAnw8P/RgHTpa4APOSW3bEEo8K\nN9R4gRurcG1oCVOkA64nAiUNe9461CnA7Cz0RBOYO2d49QHYnEp33UOHlnlX\ntn+U2ScYc1B/6IKYxsDEBHN57U0ASPR2KkbDlTNoaV3P1Bw6JUM9rQiRN6c3\nq2Ug25kWxOvge/VoJWyPzZ5a3VpENeiTBzwNfQfA8GriS6xljPOkTL0OE56D\nei3kLM3/1NHBshwa56w3ozGBWB3rG/68Jj+SsT2BDWVrVC/5gOyGdEYf4mSr\nBdI1jpcnIr2GQy6H60dzeY8veHENWvC5WyKegBH8QrUoL/A8Ev5PZb0ZKAy+\nwwI71zgZiczUS4nu6BxtBC8oYI2CesOxetEjFscHVDxbjcDatrYExgIts3me\nyKGzk5+T5LpEDyhACz7/XRDsR90ExK6mvwDUBs7lA4b92O1bN4PZ0X5yS/8P\nAkXrKTkqiBrkBh2oWxg/Qa2PaviNLRMUk6nsXjVnGcNOOsqQiLvlLGIFbtY0\ndfcusZOl6hgNiuaTEEjASGUR9DbDNARM2DfnweXfNMjRLMv48FY1nM5dxGlM\nQvUNdD3dtS21YBadk0i9h/bMmMZ8WdrydnvG9wuQbznkZOaOzraZvb6wJRsO\nznh1tfLAb0bbAFOj1pk5DN9N/3xW27RmkLO2K3qNivgEYbf4C/+xIq5hib81\n4uX8\r\n=BHXS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFH/cJAw6d7Yucv/Fie2KlRwC9Uvo02SUclBAriEh1uJAiEAn1ZhuNri25Skvq72OJWPRj8Bb+Ytz6eGJ0fSSg+NWp4="}]}}, "0.1.12": {"name": "ts-interface-checker", "version": "0.1.12", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^2.2.44", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^4.0.1", "typescript": "^2.6.2"}, "dist": {"integrity": "sha512-zfJsb81glBbt4iKclqwWj8ESP2qUq1tccdnYpbUqEy7jj2JFaN09vpgtnPtKmL2jRd/U80rYLo5H4ErxJSAXQQ==", "shasum": "5cf69fa301778a412f4b2f564533576516fff019", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.12.tgz", "fileCount": 9, "unpackedSize": 62405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIO+qCRA9TVsSAnZWagAANgQP/2l5O7Vx7r+OiVEvpVKV\nxeIpqo+xYsQ0M5Tp+ZP0WdOozOSPfrXuxbyy6NXZ0PPpYgAe1S4rKxoZuG5n\nShkXrRI2lSEi4++twCOixigjBR7UQov7OFRmlNLW1fqK7yu4cRQYQc/7IQnG\n2e7VI/9v7L5CwEhFQbgP4qGLIntnMg3ZCIx/p187+1chq9i83H0wHxi6t7wd\nAp0gXZ1Vni5ECssQBbW77wXQIHGX7vbKaRiix2MxQ9kR7CG6fPdybk+n2RJC\ng9HyschIKpBcAkQY7OS8L3y8s0JUdixEEC20Nu8lfHevHOAN73hf6todaEDX\nFfU2g8/2axOFO5rJzKUu9JuqQob+aiRRbzFbE6ZR2QVDho/XU+s0oOij85se\nWFghI7FRgbbJAYo3hx65ZUlSenloednTOVk/3Q2L0Q+qnPAgHdgLmYcGX4AS\nwDRCYzKzeSzVQS+HSl5QNZCHxwuJp/40M+u5eh7YfHoTpSt9vqJ3806ESh0/\n/xfrGViYx514GlPoWflIW0Yq7cDXtg7iR8Rp44ohOgHi+B3jJHClXXkR/4kN\n3HciKsafJBdpHAHNECpFUaj88Ws+pGFN4SsFBY8VYFxny0f4O0Q7q4SgkqNT\n9K/E3Xxp2efBCGVmF4p+6DjdTSdlZVV3ELbYgAu0+iR/o8+muwhrrU6L7Ayd\nlKs0\r\n=Qs1y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGVoEHPSXC48CSZ+o8J3LirnLT3vvZHOQD9v6HsR7cqBAiAkNCwEHOeO6+FQn4sEchFnmcqV6kxbo4lD/yrEohTb2A=="}]}}, "0.1.13": {"name": "ts-interface-checker", "version": "0.1.13", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==", "shasum": "784fd3d679722bc103b1b4b8030bcddb5db2a699", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "fileCount": 9, "unpackedSize": 68036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfKdEgCRA9TVsSAnZWagAAIUQP+wXIVpZwugnjL3whqVsg\n4CsLyxl/kb37ZmUjkKqKNfArlujFIFmTiRfhg6xwq8ae6xJrI074PtC2G3H+\nzDpT1EWqfTkjsAOFL4akaLRXkRqPPYyMD0iokiorQ3AOnlr6/DS1Pqcmmo7R\nWR/Wg/AjGjiR3sXF8dX39v2IvrOw1EmTIpOynCEB2nzSlI7Hk+ohNufiwbAe\nEzagR/6vWElKkea6WfoBDpQUh3CVahwZamYy+19PTbEf8XMeAl9Iy61mv2Sn\nL3wljnmcsKH7Kn+IH2o4zQWysLgGBlTNak2JRQV0k2Vh3gX1sb4G7ZYxm16B\niLfN6nlVc8f9yokUWcHXTUYBme5/CejNPSsUWuhy24++kiMQ5O9an4aSbl0q\nYahXR9/H+4bVuz0dYhkJ+eghTRqy5xotFRlJga1bAhXA+N3M2K0X/cP+QKxv\n6ZN8b2Lpg+VxKj8ng/2fu99MePBUq70nWmEov27s4pMt72PSJV3R7B6nCwf1\nbJJNFYwEigOf2ElrA5esZ7X0QGYFUAz0I4dW2M9RvF3OkJL94TKaWvWeZpP5\nWhIlod6JUnBeDB17ZO0Dsu0YuGryNLxDJ5moksIDL3ueISrUD/eRzEuDFMXr\n+O81dbE6Xa/kYv0SDuWOzRgXb9Yk0FInpO6lCOpxeG8Jexp+ZFsb6G3Bv1VP\nmdcq\r\n=FjRu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHpO4i6RfloGdEFsSaKiDv9dMIZ2gHCWeTuPn/d/RXLjAiAK8CkZRfnjG9kYxiEb+Hbo9fs23tlNN2WRSl3o/qNQ2Q=="}]}}, "0.2.0": {"name": "ts-interface-checker", "version": "0.2.0", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-wkqy1TC9FxALfAl10ov0joZK5aXjIhnOYBe26XSPiRD0UemFuQOF7D/LowiCFu5gbjYJc+ao0H+I50YYU9oKVA==", "shasum": "4a87c0daac4eceaece401b37c86fa98486caf699", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.2.0.tgz", "fileCount": 11, "unpackedSize": 72759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgMf0XCRA9TVsSAnZWagAAxugP/RQ9OLplsz1Ycfqn7eg5\nVYfojkFjauKM7oOmfIazOI8coebanHyl5bTR04VBnmUayr5iXt/XPc7jxgHi\n1So60diEVDCapr69Z44Nj1BxhxjebUWPUSlEZcRvdIGRWdJYJzY8f8zcWC+L\n0ZlHSnYTIt0xcDwZVUdDX9hnZFR7CjsEpdWShv+oLx7aosc/A9Kac+uvpHXs\n18LyRdUH52YRvhLwSsjREgY7Tle1eTBgpdCpR3vVHrsRT/XvNhn5ve6CI1Bh\nh3VfCjNxXDa3rBjXNjJ03RtIm92+AsC9XtDtBm0xVGah2IhRTMMyZdzMKM+n\nuv1SdEl5niht6fnSbIAMkGHls41OfW+mGEJ355TOE8zw93JE8h210xnvi5SE\ncqTIZe2n3rYjzEH06StqdfhG2QqslvjePsu3upXjz1pTSg8SRuAQo2KNM3KH\nqJmt5VHgkCxnRVZyITjf3k29ezB5dNtlBHKVQxiqx/6i5mwYNT/kQrPgABS3\nixDE8ofENP5+LDG+IFO0sZ0EXVInPaGza40lICnxnAmzsHUzmtO9qSp2OxvO\nA1l5eDOzg8shD0KAXhf10iIDlUlSZZno9pSu+G63Ew9pjGkEesQg0xrNxo7z\nZPRcijxTBlygJPlKM14OJ6B1Em2TfHiF0UwgpDzy3qEtbHmbVIHmmGGrIbl0\n5/iA\r\n=jS33\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAiMaMv2ezB1jD/WhJDIgbMzxucdUdJZ9lsrvpBztuMwIhAPv7aD3GVwigQNhozja8ZwB7gu1iA/pssUW3HbhnD1Rg"}]}}, "0.2.1": {"name": "ts-interface-checker", "version": "0.2.1", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-BTNtU9j5yohY3XkwiwLocTrDMDYqEeqtJeULAUFcTDw1GMl1XqD6IksIDX6mt9ACI7DVm+b1gqvYcPuXsi49OA==", "shasum": "f89437412519dd0f7817abb5739a263ed5850a63", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.2.1.tgz", "fileCount": 12, "unpackedSize": 73251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOG2pCRA9TVsSAnZWagAAgssP/i1P/NJEG6F+z8Zgjs3d\n1qZC8dZPCdZB4gcx43bk2dHVBd2FtnL7ZUhfPxuKlIFkeahU9VFU70cQkIru\n/tucta+4MkZDT/90eG/IqdjxSXJLRNhxOzvxfC269D+euw4Cwjv1q5Es928q\nATcLKvxaZfNgMVIoTq+deQSE7+ap7/kiapSf6F42CQzdb2LkLBKTQ61KK5wM\nHWmZ+A80EIoeY7HW6A6ELfY4ezvN+iy/BMY2hD83CBVmcviRMxLyHnwxq5RJ\nVczxIJMBFXHbVe5+2qnebhNajaMQnZXrbfWOb09LvSjATbJZiyll7O159b1g\nIAsvB1NRRg4RJZrJPJUnUES3isppejrVd96rW4g54HjPOsuqMwgP31YHB4JD\nQGreeGQYUOTari3pm9XxyupnlV5VmMPgWAG9U/WhFeC1KJX9Yh41+TnAqO5k\nQouwNgb14m7fBMR7GHzya1Y4tGKVljonofSJ+4ky6xq8ih+gaDR/dgZHu5IA\n1AI20jZquLWUzpdVAskwFODnFU0GmwKFJjTYeCkXffjqCPheW6DwNdIcPruW\nPlmXWlBGmW8gX6kktGhKhcV0noCspHBeSVd+C70QWDLeNAhLLQkAddyyXoa9\n8MR8sZj4H1hGf39FtHiTH4vAf+80j8bNETmSmDAp0BYZhjOHQhEi7Hgw7L/8\nEz0Y\r\n=vpO0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCH1VKKevUwenPK3114F/nxI9IgyyzbHx5Juj+49xdbqYCIQC+Yy73QtbpxAShEzEmIybNGf10UNTWBdYcRlKXlw5vJw=="}]}}, "1.0.0": {"name": "ts-interface-checker", "version": "1.0.0", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-yUeWbFBDiwPodNqrqpvQpGWheL6PvNu2/pVAb9yy2vzdkkflCgwVA4U2akByPCXzYTum3/5/nB92yKuiLpSo/Q==", "shasum": "2c637389f24566e0bf7db8e4482bad1db7d3bd87", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-1.0.0.tgz", "fileCount": 12, "unpackedSize": 80721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdorbCRA9TVsSAnZWagAAfAoP/iWZbeYnH65HTbS41fZJ\nQX8qGnm1hEAEhU3rz7PXYSuP1cMW3zKoo+jjL3sVtgEK9HZVZB13sdlqDbgA\nAhrYUnGgcxKl1U0+EkIobcjFXuczI6KD3xa6xZ6lsYkEjwsOqu3CbfQW3JrU\n+efuImVAo1+Rp6vOl/7JHQsq7wcf6pbVJGuAKY+jTYQ5FFDgoEPrqcO2cVCd\nxnbkKWZ8JKfhZz4TcAgrQZbMEw/WvoZIdBdHrwwEPCsHdysREM3grSEiv/sa\nA+Va8ZdftyIkJt7G3apPKD0JALwCnwkTo8MbKIJbaiwadOEMUjEu+hNcIMh7\njTdxUdVZ512UlAOop5t9n1pCxWFgaSZto0yZOn5rdGbEaxBOXQewD0cee5bX\nE/S6Eq5E2LUe6IDQcsmtl0r5j5q0wF1CyKzmWeE+6By0j9U98T5lzCHrkykp\nj+kh8JnCaDwH1DkfJ2VI8AtcgwOZELDe3/bj0KtEGm+LI4tjId3h/UcnZVy/\nw++Z/BzhoXIIPsaJ8leyIPhJUsdrhZcecXoGZIo2qhKER+TLL0iEOuaYr/oM\nDqi/Qla1zVXbtpSTq3ByJ6xGN0pZJRDpT/kJmtXQ1bkq72iMUOCY/M5YUwlZ\nrGqgB+97iCFJ2Q9lg2QXoAAQho24nb1QhodF9p9KLKLTGDmPioi3dVes1Exe\nincp\r\n=lbqT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGfvWHE4OSs85FWmIsGzpQpoae7QFpzdmJ6sN2c2ysdOAiAoI6KcLsO2tnUOS2498YKfou24mTTEy6VuStRjRguGSw=="}]}}, "1.0.1": {"name": "ts-interface-checker", "version": "1.0.1", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-RX1YLroVeolfLZEqLeTgFuae/vYZk2o/HKu40Ry0xIfB65cbkvN8zGpqBB47KjF6Odt6bBRjqYaoYosdxGOLBw==", "shasum": "f62bf53be096bf30714ea90011c815483470cb87", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-1.0.1.tgz", "fileCount": 12, "unpackedSize": 83294, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDTvaxDXWth6mvIpChPdEEILRETuPrmAzBykFuPjMMg4wIgbcny4dJ1tHt43e+1RB2uvn5rJJeMgyCcCuPgcaGjuvY="}]}}, "1.0.2": {"name": "ts-interface-checker", "version": "1.0.2", "devDependencies": {"@types/benchmark": "^1.0.31", "@types/chai": "~4.0.8", "@types/mocha": "^8.0.1", "@types/node": "^8.0.57", "benchmark": "^2.1.4", "chai": "^4.1.2", "coveralls": "^3.0.0", "mocha": "^7.1.2", "nyc": "^15.0.1", "protobufjs": "^6.8.3", "source-map-support": "^0.5.0", "ts-node": "^8.10.2", "typescript": "^3.9.7"}, "dist": {"integrity": "sha512-4IKKvhZRXhvtYF/mtu+OCfBqJKV6LczUq4kQYcpT+iSB7++R9+giWnp2ecwWMIcnG16btVOkXFnoxLSYMN1Q1g==", "shasum": "63f73a098b0ed34b982df1f490c54890e8e5e0b3", "tarball": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-1.0.2.tgz", "fileCount": 12, "unpackedSize": 83294, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2+XMCRA9TVsSAnZWagAAKgAQAI0rol7xtvQf4DPjgOqC\ni00h4nnMOK+3NJfosaLj6wwucmDfQVlIliiXJP5iqT7Qm4r2EJMoin/JiQv2\n5BM2cmHtAunkwtis7vUDKmW4DgT8o2frRPN2ADjwFfUtKOwBlq/Y29mCnKNp\ntblYvjN1ZusjVnwrADQXJ9c/2J8J10et/q7KkdKcLwM7B3BKY1PJrbivAHyJ\nw8Lw6jz6x/5uet1qzwOkq9Enm4jeigy+eplqDTFUHA72RxG4cGJShr1lftWw\nvGLbW7kB7dQ858pmARX+MpOqhKcD4HyXSZTzuscTNtqVQmt302CikGsgzl4t\nfZaL+9tXYplg2tI4XVi/WoJu6FtR98siGp1GoECDFj7yWNE8RX/n686DlkBK\nro8JJnTD6PaTn/NEFpCPFTfanmqSPzkTs6XXW9ASlgCCBNS9bG1EuRfcwbR8\n1ydSIBbOICVpbvi2x9k24WRJz8h41ItnP0zOCKzKT367D633fYS+MEdhp/w6\nwFsLrh4C1GNzzp52gcoNCFLWq9lNplZKMXmTrdvqvXgXBZFQsc/T/XMZfr4C\n8X/QYriPRdIOJZfWCM4v6h587gfnbsqOKmwrtyJj7PHvorTm65A9AWrKcp/Q\nf/v+SQJDAxcfIIKfUGHHtchCudR9XaD3f3QXwOFWvA2n/RhG8yopFXS8RFWA\nP/lx\r\n=lyu/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBD5WQZl6obhXdXHVI2kRVbJbNntUVvnKWsGtoZ6rrNzAiEA0yq5iOmi7zpwkKL26pa9TEf0IUJC9IuluSnappvAiE8="}]}}}, "modified": "2023-07-22T22:36:13.865Z", "cachedAt": 1747660590433}