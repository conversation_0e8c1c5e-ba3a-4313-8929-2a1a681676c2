{"name": "@eslint/eslintrc", "dist-tags": {"latest": "3.3.1"}, "versions": {"0.1.0": {"name": "@eslint/eslintrc", "version": "0.1.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "3d1f19fb797d42fb1c85458c1c73541eeb1d9e76", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.1.0.tgz", "fileCount": 26, "integrity": "sha512-bfL5365QSCmH6cPeFT7Ywclj8C7LiF7sO6mUGzZhtAMV7iID1Euq6740u/SRi4C80NOnVz/CEfK8/HO+nCAPJg==", "signatures": [{"sig": "MEUCIGpoD1Xo/n0m8tnaIKkfVHl2p9k9LjV+eyEKEWr35aR1AiEA+/MmTGtrMy5Qt0nb5vhTuO6jhK4cGOvzWvalnufC1ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRA5ACRA9TVsSAnZWagAAEFgP/01TPQjx6pgNbtXkPVhK\nrDhieUQLzIg3aY0eGSCJBrvafGjfBpK/jO2z4lCNrHmSUI3JiDxsz6+Hm1Qp\nwi0+r9r3vUvFMZ0Ik4ji9bN++VeHHPZeytZt+HZYyjl4JnbuC2S3v1d0tnjB\nS8b3BYQSCOtzwaVg69Qsxcm2zhQ2tSRGzMKZuNd4J9OXCMe+wUDjD4gfRVVc\nkkpKflsA7lb1qVmg/Sjlb2yc3/ABg6UCqdrwGwhTjf5XVf5RNa9Rk40WLxg4\nftkuuJxid3eGaxEWrO/qhOJK8g1Y9EiT3KGWrIsjD5Wjs9KLsJFZ0S3aF+iw\ndzAcSpYD2gpyBpIddxgi0bd3v7ZIQHQBpAvzidmJ1B49Z+UodqmSdIdi2dlH\nZ8cgfs2IoWOpPE48jtXzP0nqPnwSA8rk7NUyVzIIq9CivccCkMpP7qyrOtVi\nIFA0b057Cvd/Zl09EWjLYs6Gy9DSSqdoaLmtwb56FZDs4Xj91NIE45zHlCyG\ny9mu1v6aSye8teXcomFiaHA7xLu83iMHgrTN4gUZhziFJZy6XpnnM6gGwVbg\nPg4Oe8I/MKmnpAI81LYBk+sNWq+nE9coDpFA7FX0klJ9lxKWhvydTmAOFM1J\n3Mhs2KyOowFrrTWE2/w2olaS26TR48S1uKIWJy8bkWkBqoUji7wSeBQyZz5E\nTMFe\r\n=aNZe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1": {"name": "@eslint/eslintrc", "version": "0.0.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "594bc720e271fc9f0ddd472f3770e1f66d68bd9b", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.0.1.tgz", "fileCount": 26, "integrity": "sha512-RVbGQvudNYPPA0+EJB4djxmoKzgemqIFLJ2IBx7hb8HtBUVYiLDztcl5m9bdNu9vJIRlvrc4ASf+1Doc4jOL3Q==", "signatures": [{"sig": "MEUCIQC8JdQSxLofHbb+k47gIxXVAzPXRTTZnDUCT/t1pJ9zjgIgLbT9+/KTtiWfgo1m3wk+SPbR4baOjh25g8ucy5IuHHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTozACRA9TVsSAnZWagAAbt0QAI5UqPep2nqQYFqzAhQH\nxnRbiXYKuq/4Gb/VbrvaoZSe5189wFjGWy/DbmF6gHAQFQcjm5TLuhPRrZtL\ngxeXAqJFCBplRLQx06ONSrFUVxpSzQx/Xi1N6G27P+lOh0B/Fs5l5QfImpQr\nlvtplA3Nqzwr4K61Yq9S/7UXPnAsByS+4bUl1d70GmINoNnZT/1Ki8hspUZw\nA9spDcgc9dRFAxu5lbH5QKpWnXvSpVOMoi+ag2AKcBI+ZpjzN8LkMwl7nNKu\nqTHkRl77A+asm6dYRdBzL8xifcrnHaZqV7czzoBkna8Q8A54x797jJcvsdnf\n+Wou/8OsxRg5KTQmj6KSjhhiyUr6WN/M71JMJkknMPsokZBGfDQ82ImwT0U9\nK+5ebTb9p9H/cb6XkUew3aCizl1Vz7I2Qr8m4a+Vdgmd1E68FBGRia7zJiLU\nXQd5njYJWG/KhLZ5IwGBeLWnl7bu5GbN6beAxVFwUECRWb6tLFnpDqVMAerR\n5zGSCBQ+eF1Ns71fmH6BSLUdAgI+ruuVMtgzBjGetSR1qWpzXUplQPsNk/oc\nkpPFPOQ6LQkGhKwjZvLkMkOscGlaV/mokPCb1yER2V0CjUo3FVkVjzYP5gfR\n36LSSOZxl63gliA0MqWQ/mYNRZR31tGZCc/wktTkpSgWpREo67S6ATPqAydg\nb73t\r\n=HZvI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.1.1": {"name": "@eslint/eslintrc", "version": "0.1.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "6716db56f07ee3bbad0194b1692bcc2a880af91f", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.1.1.tgz", "fileCount": 26, "integrity": "sha512-eGlpLxZfbHGnwtUFZIXMB+RXGu+CVy/Yz47AF9QD28C8osiiPNVbGwVcID42J820IqgrAIjes2PDw21pjhBW4A==", "signatures": [{"sig": "MEQCICdGUL6YtGp3D7gKV7UbN3gzBLWc5yWtbX3jD4b8HeXCAiBARMatZ1Jn30wrESs66ug58eyrTQjVzyC3Mo/GSUro2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTpHeCRA9TVsSAnZWagAA5rIP/1JN9QIedutYUK0izxz7\nkOgTGZgxEjhu2cUjsP6wFkHSyx4XRiDw+wB5OTgnGouzeh/IE3+Vi9lvdcXo\n/VYFbQFNkaYadbIQhQE977SwQ53S6hyB+tUN7qNFjh7sS5SJaTrV9VYIombq\nRhKRt3o+ByWmYKj6b//ynNofk7Xe3keVlyJCrXHEPAx6aKVSLGXiPuTVIkUc\ntHnEyXoLuZ7zNvAT56W85trbb+weSPAdZfcMC5GnMhdyGl6ijffpnCKhz60I\n9vXUBJ1IRgv/6Fx4P62O0zrdFxdfwkg9mVxw8QxtW/8G8qHQ+ERacuMRqQ26\n6Xf01XpxodTkcL1RbV6MCTPLXNSvWwnsDaK5QpcMc0RsipG9eS/4ye3IMpPN\nCB1Kt3sqxpyQG5WNN1KINLjmjsNgYu0xFyfdigfXwCBvxJs+GWI5T+7QSvyp\nigN9k+4akypTg5O6kBf2zRfD/tu4/CuF5STD2xNjfhNpyHwqJqG7z8jdfuyd\nanOHqj7EIWOqyxGrUVGzKoon2M9prv8sKwuP8pBmgvXfBTceqRoQRWoIXMw+\ntg/uptGZnXCllsiLDzIxaLzkJQsH9rzomS/Ne7xttmR08IShjwv3awCoPZHI\n/s7xjV/EJivTIqFMM00YIRyg6fkwz+y/lXxjjr7hGCVRJD0wFgf/MjLu2muB\nvJnS\r\n=mZQb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.1.2": {"name": "@eslint/eslintrc", "version": "0.1.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "c889b4f710d4e48cf499dd64888d691143c2fe86", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.1.2.tgz", "fileCount": 26, "integrity": "sha512-qyFK2+kQifSPE0VBo9VzF9tCfN3gLMBm1YGAtCugRcKhY4oW13lmcKGmyQGhEAgts9JoiXucAGKQUgc2WThF8w==", "signatures": [{"sig": "MEUCIQDbMaEB0P4zllBscACYqTIQxpT89IJ99bX/3d8srRi4VwIgcRi3hr0qKqKyqV65lPOkWTYKSYYMI22aAI9OsvDrSRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTpSnCRA9TVsSAnZWagAAmykP/10Fsq2mNJ/EPGPuD481\n7bd9kNXMLSoRKHXSUwFEOH/ZHQCx/Fct4eX8jgF0dmFaRBflyoOQB5j0qTG+\ngwd8cTUHnb71GzOh6aAm0BQwwbFZW649aMdKQ/IHmznKef77/wermVW4kPaX\nuI9deiJLmNvxSj8GaOxxRbV4u4v+6EbqP5OCdFYBs5vTkczm746j1ZE8wRdd\n0DtK7pWe5opL3/OLpXmMsiEoaTq2PkHhOk2hnJGS+NA0mNhLzJWJgySzvpF3\nZ7ZnUL6ZgfxwbWwbwzFhGSXtoSv1Cre/PxZWyF7DsdkubeXve/Ut5cm8uTov\njjZVgMucNzZ62W0zIaDBRRFF6v2JsITkAXPqmankLPcJl+/ZHLE8Ip4T9Si0\nbCbhtqIhGNIOX9Aa/kG0q5JuWfkVbE2s6crmWFGpk0ELky/TuVDWYqaJsv92\nMJUqdbqPQJOHWBKzW2p2pfukimd/1QrTy1RDc1ox3xXMFEASSCO73p8J6cTY\npq3FbcpzljmoLRuIDgciBXahna6i1hLv6bWLrgiWAmpUzLykLG7Vwe4p3CRv\nA40rki1iQqxbXvUNTOFwCXPtL584Qm3jpiQ9I64L/FdR/ucAova3+B4JRDMn\nt3g/FE8lUAFtbmK0XI/n3X6RX39Dh64nlVwCp7AsaRuNIQGsNy+VuBX4wWD4\nCaAp\r\n=gvO/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.1.3": {"name": "@eslint/eslintrc", "version": "0.1.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "7d1a2b2358552cc04834c0979bd4275362e37085", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.1.3.tgz", "fileCount": 26, "integrity": "sha512-4YVwPkANLeNtRjMekzux1ci8hIaH5eGKktGqR0d3LWsKNn5B2X/1Z6Trxy7jQXl9EBGE6Yj02O+t09FMeRllaA==", "signatures": [{"sig": "MEQCIDPCxqMlUjZGDSw07GFtBfvefWHqoBDRRIcWRf+RcKloAiByBNKG/kwSeNzCJReo2C8+ci9hcYFaBcXLQrjN5E0CMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTphTCRA9TVsSAnZWagAAI9IP/jWVGEo6a3sgJWRmW52/\nh8cQKKT4w106TMsm0GTsAEq6XaHqjSxUyOM9tQ2mZZYLQTcJzYIQHq/6TUtE\ntZCfGc7kyr11tbwRVsb6Y/TTH2jvB7CYZx937GDTPehKMr+G2goKKdlp+vlH\nQJChyS5+pUSEjVUdUv/3tXyFLqkMw0u96tJhiAfhOnsgbm3Xp4rlldDYy8fI\nWvsos0ohuxIftNrNvAsqwonA48W/DXE6+P/jIZQneH/0Kz6y4xBIvqp8Z4ge\n1fNENaky83AZ8VmSEjvuw1iL1VXHqPVHF6yG+bVXGW7sRyDqE4f/JZEGlB59\nIfARY8GiihL2x2LGExUuumTcNSh2Yf1AiAK8E8PlHysYYL7x4vNy6zbaJSw4\nnuKfL4MHr5P4xJLYCd09ijvjIrfW//b9oX8+XIFZa8qClxdQ8jUP95shD4SG\nbSHt42hrEiRexn0IRQxsMuFQE8oA7SmmjDGXZdb7rYs/9NXJos3EHeN69J8z\nYZLZX+kpJq2Fa4qOIf+PngcXCGBOcfzwVzGGdVRzeYi3Pu9fBUbt98I4cZRU\n6Teas8P4CAENWKw50jT1rVH3GrHnSnVxQL3VzLyvZgWJkrVQNEpQUxQGqyxV\nkA6qKgnaQWZ4n16tmWLy7BCE/obPGpP8ZrLQgyGALgeW3iNwoHvuGy6/D6dn\na94S\r\n=EcFd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.2.0": {"name": "@eslint/eslintrc", "version": "0.2.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "bc7e3c4304d4c8720968ccaee793087dfb5fe6b4", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.2.0.tgz", "fileCount": 22, "integrity": "sha512-+cIGPCBdLCzqxdtwppswP+zTsH9BOIGzAeKfBIbtb4gW/giMlfMwP0HUSFfhzh20f9u8uZ8hOp62+4GPquTbwQ==", "signatures": [{"sig": "MEUCIQCJLuBeD2drcYBOglziK/sxiTcaQwpRkxZ8iE4Gn6of8gIgDXbAXAp9YcW4SMYLAVnIGAETB/A32GcFFKJzy2F1nAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfidy+CRA9TVsSAnZWagAAeWQP/RhnaK4KSH5GwyLsGjWf\nUC1pDpHQP4DwagryswZiKJvpKgIIKraOY4yQLEuRXj5IFyofyK5hkV5zM2wY\nil+u2pLZqAX9S976b/DO1Bg068D4zqXDBT0liJUv94berGeDazn5z8qScNEf\n8Nv7NfoeJ7opXmxKJZ4BJ9JIoJsHlYvLMRiYIpai7rcVmV41H4Vh9Hr6LiAS\n5FadD29kK15uiXp+mK3ffWNUeY1bGP8V873L2RnzGoENREVBlwIU3MHJnQ/K\n6P6R4BTP0r1ffyGLPCVuJMu5ushvoBKISM3e5IY2DdwQoAadu700pRQQlMvI\nmSUJvsFeyZ8pNfcv4SSExNo1U9UVcNS8d/TiHBOyVRiAYEcCv8ergULJfyOr\nYOKeiDaqDao6g8q5ulUrEhALImw9MCjSssYA0VrHOXLuiGOKtPISHYOQEXsQ\nI5p1xEHXTI4BTQpmAcEskPan5sLtrBjEnqVPkpYOtx5tqWXqdykBxeIy6sEv\ngfh4vxkoCjNLMHWyAgByAzW8KQ45kYHa12cXH1tJ5i3sVjEoapJCXnZlNA+s\n/FIJjqQzmPj2e8Z16gGTyp8AbU2qVdqVKJTz5teNljBZGjpJKC8t0uskgzhK\njP4sXpZKpO3OX0yc0TmpkybZbW2jLo0cveIHwZbaDHTbrYahU1Rs+Iajx+ne\nAu/U\r\n=FpPx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.2.1": {"name": "@eslint/eslintrc", "version": "0.2.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "eslint": "^7.7.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "f72069c330461a06684d119384435e12a5d76e3c", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.2.1.tgz", "fileCount": 22, "integrity": "sha512-XRUeBZ5zBWLYgSANMpThFddrZZkEbGHgUdt5UJjZfnlN9BGCiUBrf+nvbRupSjMvqzwnQN0qwCmOxITt1cfywA==", "signatures": [{"sig": "MEUCIDTP4hIpW63+lX70S1vr43S6VFavUYUDMI0GNrtsJMh9AiEAxU6v3gUTc/CZdImOPghBc2fuLFRQkJ88x/EsnY43vg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfl487CRA9TVsSAnZWagAAdVEP/1E3qXJl76wS8ShoIOEz\nT9Y2UlTYyiKVQviAgX7cYtSjvJLv6ncyRnO/pGEdanMlQCF0sJXEOKb9JD/W\nPzjyGgsBx4FWWrXpwkWZH/G1SLeI37rRhZ26PwxUG+JLs49y44IC3r2AH8QZ\ndBeGgSGWQ85trNvihcXgb/qD6w89l1agsK1tOGXsILfEupW2xmhcCTQdC7cP\ni7O/+iYh8eOA7bPrKe04TxaJIA9cflkzITJTEq07IeQtTuzu+tamQjqXzwwd\nrLaj09QsiDKm8GIbJW5nIB7sz7ao7ILH0J1QkRsLea9SUMkD5YCu6zo+LNaa\nclMm4BgyujExK5LEZpwXl5lQDWeafcGwh3S+/2vYNF0DRioesaQA/Y2oTKxg\nwGHfG74ifMCpAlcaJ9cGyrs2uIHJ2zU4GrYUIL7VkmiGBQ81Np/6UAkLBsH5\n1VwbILHc4sSopBkDwY6qN74rrdIqCCdYxdsGMmcoNVGqrLH1uzv3qhB1mFag\nRhZ1av+SN/J2ih6C4Roy38lt7AarsOW3DoZ84pAT4dWqwGvaltWauVCVf4G/\nY+v9rm73hD3BZe5Wh6lBhBxKdVS3fD0/5F8e5veZ6xIWvFZ6R0uIQdR80dMJ\nZ10SvJVGp0932dvxMaZ4cedJ4hszx1VJe9UCRc9kxjhmIYaifrZxrodSI22X\nFk4l\r\n=QWFy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.2.2": {"name": "@eslint/eslintrc", "version": "0.2.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.19", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.7.0", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "d01fc791e2fc33e88a29d6f3dc7e93d0cd784b76", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.2.2.tgz", "fileCount": 22, "integrity": "sha512-EfB5OHNYp1F4px/LI/FEnGylop7nOqkQ1LRzCM0KccA2U8tvV8w01KBv37LbO7nW4H+YhKyo2LcJhRwjjV17QQ==", "signatures": [{"sig": "MEUCICKpTMb21Afi24UgE0eXNMRY6zI8WRd+VrgUm2PZTTtsAiEAx3Og0ZstNt+AXfLo35tJHXXS5WDpvpJ7E4ovzfLQB0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyxhrCRA9TVsSAnZWagAAAxMP/21c+9DtNC93Q8slBEI6\nv/IFLu4bOVu2um1ZnRwQmjFIjgCa631Pz6htduj3H2MqUcACAYS0kn7+VhAX\n56PxOZiLcP3K0W44NRscE1ZjgOKbJjsmQ5fr72K8rSkywegNOCK48VsxRzFn\nLqolNoyV6EidfFn62qPn0lw7pNFphfBu+uSn6f/cflPGcCjeiGxwCIh1u5Z5\nMkt8vJjopbT7cwQnu72BP+bKY3sbYwIABxjeaMyJKfS72EARoK6/vHQnRusB\n4ttllHDyOmkYsNvQegTNjIDZYnGKGSQOLWWZDzpL2PVHwCTIERYPgrh+GmlI\nGXHZjQKCXaztVmvT8jYEX+mIPNGB8pGuGO5VonU8A0D9NrqQ8iQXxUVrj3Rr\np+hbazdO8aiWSmM8asOxeXJODvVXHaCOp7qKb3y5Ir/D7MYa9yuOr1qkoIfz\nQ1KoWqwCbB+9jQaeMowx3tZ/ZeqTRvFUvHOqSNTYOZO4hOVkCW1OrZxFdIvz\nHn4FXcEnXHkgfKwD//pj6PcY/ivek1IifDev6NJxUuJuoHPO9scYXk/SImVG\nYv5ctZX3r8bJAmXWOFPeuLZBL9rdZitzo4nMPerkPuhsBk05KArKMImlGDn+\n8Qgk2gOBSZQPbQzuwMA5Z8kjAlM88Ok+ox89uh+SL75bGlEGNmsU8uj95t2d\nRgVl\r\n=5R80\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.3.0": {"name": "@eslint/eslintrc", "version": "0.3.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "lodash": "^4.17.20", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.7.0", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "d736d6963d7003b6514e6324bec9c602ac340318", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.3.0.tgz", "fileCount": 22, "integrity": "sha512-1JTKgrOKAHVivSvOYw+sJOunkBjUOvjqWk1DPja7ZFhIS2mX/4EgTT8M7eTK9jrKhL/FvXXEbQwIs3pg1xp3dg==", "signatures": [{"sig": "MEYCIQCC1YmE7LwXnK5Qjinu+nZM+BTxGkJHK6V4ewf0n8lsNQIhAPXB5iW7E0qkvmQ3Iw1g4jXMTcrfabfvP36mtR4ltPO4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgAhMBCRA9TVsSAnZWagAAdzAP/i/AJOdPebvCryXMBGdv\nVRKXoaWyxfPKkV4Qd6Oy0oBwxwxfaRmCaF3qJDpnbkho9fPmDDGQMB+5kB9V\nPkCBJbN9pAfO/kYIs39S8OLZ+1eDQrdJY6+h5yQHkuBqXHKwZ1V08vmWFWzW\no5GlwiEnu6U6wwvWhskQyDoYnUyR3nr1EkFIyo+qnt2QsyFYmyxXyZ7wcHv1\nCKWRy29vbO97JaBR6oqu7FDSSNI83kRgYl/0sMbBUcmdajWhxWPqYxsQ14YR\ncSAwuMEIw2Ytwk75iS+DzYzYYtiKDqx8YNnPqQ579/IhDdPA7bOOnZymi3st\noXSpnLh5ImlACECtINnLv2/Vxf8W/feiIcZoTL4Wn8LNr4I/2EZEqdzk8qL1\n4oMZ1VGKSW1SS8UTMvAw2E7Cf0dur+IaFH1IB/JYlICrgUHGP2qHdn1lAoZm\ny+TzCzuOkdMRAg6y2IvZQB//ZGayEv6KHA3r83NhGQBKV6kRdYpJjr3oMs3B\nvjTVE2GXy97WNDcAROj+2yAMUQASHgNDqOx2BLmuI7PT8lel6eoNt/cHl+/Y\nArw+WVWBg6LOIVTlOFVvi7aj3uYYkla+XHh2/svihgGxKLttMe7BtX6siSAR\n4UjnPyDcRF5f1hABaXSIGykUM0xQinPtXhWCyyF0sGCPZwvQNuasHBnjOLej\nhqBY\r\n=MFR6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.4.0": {"name": "@eslint/eslintrc", "version": "0.4.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.7.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^22.1.0", "eslint-config-eslint": "^6.0.0"}, "dist": {"shasum": "99cc0a0584d72f1df38b900fb062ba995f395547", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.0.tgz", "fileCount": 25, "integrity": "sha512-2ZPCc+uNbjV5ERJr+aKSPRwZgKd2z11x0EgLvb1PURmUrn9QNRXFqje0Ldq454PfAVyaJYyrDvvIKSFP4NnBog==", "signatures": [{"sig": "MEUCIGu5hRIHm0rUZM0STHqph9YKewESeG9JKcE6dNYyfhLlAiEAqRr6m2U8deIA2P8dEaCzff9FKbgPLb+/ioTLjD2Iph8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOrh0CRA9TVsSAnZWagAAkdoP/RBMh/CTfPx4nzN9lI67\nZyNyrIKigUZUGUosqx2MU1dt5++uye/68dwC0xus8VsrGYT8QVXGM08Ruxi1\ng/ygZxA0OqUu8CNeNybB1xqWumR0Bjvc0v3W/PTtV1CMnrWtGV0f1omvpAtL\n2VeTDW83xgs/tI7IoeSKr1i01SeLg/LS8E1UBBdKqPZolh3MepnLz1/jLhAG\n2CaF0A1Mzs4zZyidUVvuPZBjL+yZedhOaYAHLhJscPY+AkR1l/zm7wb8A9N7\n+Ayp06rWFgl6tWymDJn1tJr58lk87bBdG2FXoZitgR5fMiLtKlM7BpgYI3ej\nXeeXzp2Tt+jr+qk9hI12n6IrjJSVaV7A7c5L7hlssWTcmSZSYt7RgcLw7KxW\nYVt/cKyvtOCOPMoeto7Z1XYJo2AVJBNMzk4hszF8LawwkLB0nsSBRKCgqqKN\nNObe11SiSo3FXTKeUjJVdZC39xNrUclbTFRANGjCnv7+cKGbIX1wzR1a7RuI\nX7UcmkaV3+X1neAHx+7d/gvc55O7DkY4G0Ak2f5oecVh7+X/2VBEFf8+Tf3c\nwQuFvlhi+vOqEjELXIC6JuCt03k5nODMY86bb93U2DXdUAcvQ6UrklofIQ+e\naLffrLd2HPhbsLEHwQyeWcPwyla06djgaH19Bd/2c8uD1HX3VrczAPTjoZl/\nusr0\r\n=xlJc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.4.1": {"name": "@eslint/eslintrc", "version": "0.4.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "globals": "^12.1.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.21.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "442763b88cecbe3ee0ec7ca6d6dd6168550cbf14", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.1.tgz", "fileCount": 25, "integrity": "sha512-5v7TDE9plVhvxQeWLXDTvFvJBdH6pEsdnl2g/dAptmuFEPedQ4Erq5rsDsX+mvAM610IhNaO2W5V1dOOnDKxkQ==", "signatures": [{"sig": "MEUCIQD2sWZ7ODE2wUKLQDGI9RMY/Qpc74nQmloS6jAP9lzFbAIgOAsJU55hGQeYu0GaGHzh+v8Vh5U/Tk/nuJ2tsVSHcr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgldp2CRA9TVsSAnZWagAAAfwQAJRhG39vyL06XwkbIPma\nIS70NLp3wRXVYRULSoJDeY+JSydmM5/1J5Asb+HDfWpl10hFe2BRhteu+BhX\n1VC0USer3iaqBifoj1kIoFcpq7SDz9m1zgBP2gqcFyC4qk02fBSwN0by4jlL\ndvGApdbGMO/LwS7t5pJiTyChEG65NgLyiUdeS1nbbUf+e0nR/l7UAzN9XIG/\noK4D+vJD3nrjZ8ELuOGYoIY8pTXqNYHaPWEGZCLal+F8t1ECw5+itUMWbB4x\nsIat8yV9IYmWnDeb4LWVwlXBMuUpStPvemwuwpgXriIxk0geniCprtWEob8h\nl6Qbq53z8AboxIz4xrGdt5OfoTH9dEj9zrPLp6zUnDMBmrC2fnCfD2Jy2yGe\nBYmIXSzI7jxhC7VCCZh2+B3QWc4gPqPM6x2ZutmrKOXMOm24KM+mGbOoez+v\nGagjwKv8P2eMP3FU0FHFcorj9523JeJK2j7S37pWPgDwVC+HPI4ncxkmzuOS\n82fX3nkyKzZ8F0zgSPoQ/W4YpsBaGReMd7LnHp/F9kNYcmdlyjx1Zl+lSKRA\nxt2lh+S0QpmZSc3gwipJ9KMuW3e1hcG14WXPPSkOCRUP6sLA8WBoZIHNgscM\np85vQW7NzJGkycmWU1StFCdwBdaLpkDyp/nOX73h0eoUUhgAXX81bYGSwwLf\nTZn+\r\n=pCuX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.4.2": {"name": "@eslint/eslintrc", "version": "0.4.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.21.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.0", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "f63d0ef06f5c0c57d76c4ab5f63d3835c51b0179", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.2.tgz", "fileCount": 25, "integrity": "sha512-8nmGq/4ycLpIwzvhI4tNDmQztZ8sp+hI7cyG8i1nQDhkAbRzHpXPidRAHlNvCZQpJTKw5ItIpMw9RSToGF00mg==", "signatures": [{"sig": "MEYCIQDBTeGj+OvwhWQUQJpC9xGDUDa+fxhczbhjMP7PE3KQcgIhAOMf47qRalNyLlaQCcpBvWmvoF4ynp7AJ34hmqRKmI7q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguqFaCRA9TVsSAnZWagAAQeoP/jvsGranx/gV4Gm414bc\nV2PmtwebdA+k64USprOcg98CJwEyPYQVHWfQcoY2OT8FkdDXiC/gRONg/j+W\nhb2DuSe5cnsH6c/l0tc6gD/LXhLqnOwCHfRh9rVIwCUoPmmYsE0FNLLk1SYo\nmjI3UuUMUxqvN42e8EhQZSWyF921UL+CL4IpFz2NzhncZ+NelQJL7eyLITfg\np98FUIw22i2h3EMJ7+T0VP4JinHwZm4e36bVRRTQx1vqt+xdQilOCTh1ijt1\n6l5rncLw7MAU62wHMwYCo3svwAloaaA/fCL1iRDsfzYgVUQ+fzLkAqFXWgvw\nj2EZDpiSB0c8AKppZ9r17MKy82WBbTwSf9qfZ06hUOxP7C+0javqVzRG6G5p\nZ4NQaGzPiP/tN3gvOT0x9e2qFJRkOvaGeGppqRnWhjzMEXzUj/HeCe7R2k+G\n6ny7P1ZpeXsaxe4KP9fcX5EyT5GW8zp7UtaCqDN83GuSs3AJD4624qUA4TzZ\nVAxJiYyuLuH0rRMd0LH/idW+QtgNxJgFFnCfE6FxwQJPnXbmTsxCoO8UqIZH\nmqwBsNgRxJuJcAwERjhKPBBR8AkuXVYC/RsomS9zGZR/FzMt0ul66rei6aWB\nLF4IuduhaYl/Y982K+0X8cJzBrJ0ncovxUBgiLjlpEfriuS2TeWu/2MzDoc/\n2GVL\r\n=+WIT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "0.4.3": {"name": "@eslint/eslintrc", "version": "0.4.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^8.1.1", "sinon": "^9.2.0", "eslint": "^7.21.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "0.1.1", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "9e42981ef035beb3dd49add17acb96e8ff6f394c", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.3.tgz", "fileCount": 25, "integrity": "sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==", "signatures": [{"sig": "MEUCIQD3RWa8moS1kq0urpS8iLcsK7MB462DiNS6IQE8RRNqxQIgcG8oXiEGcBFU6cCGRum0CgBTpfNhFMcX8MlVNQXLcZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg80pVCRA9TVsSAnZWagAAe+8QAJ6HHnYSS2SktkGrsMSM\n+quiMNbY3Nkkeo5xfNMbeF4ndE2DIb4v2k6N5U0w/8PVXnxJ6BIT4hc1f/LE\nX05wAkaI9Z4hG3Ln7ydh1kALssuXeS6OrzvN/xms1OmZSiDQT8msRnfAajTd\nEzucQMoTgR8J4bDem60tbZUewb4NEsgra9fzMFP2UuuxgyRbHMzhdOV9vRtB\nDxZvzWQHRSP1ji17/q79pIShGqnlrotUeYQg8z0k1/6ZM0pekYqVLV0BImy2\ng5XmDR2uA0vrt5r9NvaeEvcNHzBmnwi1By/Bd7U0Klvk8UtSqOY8sFnDwkNn\n6RoLZD7V7AcMw6mauH574rM53QHWZHOYqcT4k09AiooRKbvDh0PXAMDr6HvT\n3ERdKD1QSvKTNwKM9nskmV4cQcp9U3GBH26WVUsdF5GSUxonTb14/dRjwxHD\nWFGpyOtbuJ0Wdn/NaV0VtX1X4pB/l3OvN/i/x4y2lSRe5paYGg3EbY5xJTcs\nxX8NCF0X3CHTzLI9M3btGXRtXhFIMeWxGP/FTrqVs2xyRhmomjxPXAoHWobF\n5SjVrjW+v/jibi6QtXk/bhyGvEv1wX7Dv8n8oop/LaF2HX/CYxsK/RBOUxoZ\nPhOigQLkQYQHiTemZYhy3c4Z0Kg4YKngnel6/zf2mhD02e9qLJ5Y4Z/mUgye\nrqtc\r\n=wUgH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "1.0.0": {"name": "@eslint/eslintrc", "version": "1.0.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^8.0.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "08309b915051dbfcfea8c3cdfc832375303396c9", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.0.tgz", "fileCount": 29, "integrity": "sha512-FPnE4y+crrKBF0c9PckDHFuPDQl+wRX6S9jeSw2WwM2YNmrdRch3gx3DOTWpqpQu0G9yoJaeSSrJLiV/29tGyQ==", "signatures": [{"sig": "MEUCIA+O1EhGRordm7EzXvjJL6o9pcyr6k1qWe20qyJ3gZrrAiEA4qRzCDqqMewbfmCHBdnY+1k6M7O8Xr+qAwsjZwTA/Ts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640174, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDdS3CRA9TVsSAnZWagAAzpoQAKJVqmjUJhwyM33wINqa\nFoiAEWughyX6gMEknOV1Ni9MUjl01EBjKzk9A+dvj52raXXCBta/+9aRMp5V\nwWHXqgd0YLs1ECbO5qKMhB2GDbwym3zuQLitkT/zbodJjlWjzIRr03PA31Em\n5Lcrjfv6SCECgNWKlYiIQ7U9SwAj25LIMlEVepbnw0Q0pBUWEd9G1AydFOD1\nOgVM07JNpXkV3eTRzpWeeIBseJDnPomg+E5PW1Ijiz4mobKjLDVHH0HT8VrS\nbkevd9fieyman4KWYj52EiUnYtO/IBtq3IDTJYOLH1jNqalHL2cBU5Yje/gS\n3sIDCVqQlxBsMOWHBut1OdYlY87yUpcGpzo+H4TyfLzHxl1DpBqloyVSCeoe\nByx9eVEr53ePNga15GxyCxOcmea6sK50C9RS28rcT9MUlWZxuNWDXJ5Vtd6H\nw49sbMovjCrDG1jI6BizO2CIxp8+CDEwN6nDcxargHVTp6NkaXutXe/+f9kC\n0+G1Dlx3zxrfKTvdsbhaq7p3rZVRAI2uG/JuY+gD2/WeOhpOZ4oh8G2eEwxO\nyN+PAqB4pmyRcONP5V4Va/4gBrv3DVVDH82mYImxo8G8JF8OxdamTD6t0s3K\ny9aylCCEpL/a9/6uUeIIevfk7RsUsd5GEaTh02nDR36vFxo6lXhINtroUhv3\nFn4M\r\n=KAhW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.0.1": {"name": "@eslint/eslintrc", "version": "1.0.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.0.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "13a610699a1c5b2d03eddb8cca3d84275aa833aa", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.1.tgz", "fileCount": 29, "integrity": "sha512-bkZOM1byYEdqFpWUzivekkhxD0diJ5NUQ7a2ReCP5+zvRu9T5R4t0cxVGqI1knerw3KzyuuMvGTHRihon0m3ng==", "signatures": [{"sig": "MEYCIQDTf4VuiBMOiBfMt5mT06xApfwUS58HC7Eb0UavFZ3LsAIhAJYVkWTHD40SJOmxVG8p0CIxz+OycTGcDLqKJAzFrgUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhO85sCRA9TVsSAnZWagAAtp0P/i9xocXCoelXu8wLkueR\neP/APRirm3mvXfFDQK/UNPhntA519KoDXcsDLxo0z63n0bioxiyPqT1MQmMJ\nO7ohcId+Y8rBn9CusR2IHksdPo22owQS7NQViqvNgN5fmWVFunGV9GRfgd5C\nNTLn5v0MDMJpJ4JFWXLwkq5VWq+tAJ4BNQwIpGAhPa/kO/Sq/4wJ6ODRwTpf\n2n8iXAmWR35s1HqjF+6CB1s80F2bSbmlEQusl/4vmhHOgZo96hPHhAfij2JV\neIyvkLLAo7uYDS6xZc6xZojU9+QE1VeegAKmLj9dPwFIP6iLXgue1wtwxJuP\nMGRAHia/s005uJj6Ged2lKVEOAUFFyogJUcM96ihobXjh4QpUktE1mG/sHAa\nnGth56PoFtdTay28rmBOpj7G1aeDeODcQ1JaI8HXOCgeO4OORWQwl07ad/iH\n5dxuComBoTV/VkY5rD0bkvBy5vP9dDjzzwu3Xt9wCBhv96xLpu4y1qiswTpf\nIfdu79XPto+UG5xXijCQwLRuptyvbgF2FZaaH3YcWKjFiVXf0K76PS/Bq9rd\nwiy2CTZyvWUJtbeUyrqGmjBPGt0iCJ2rzkXKx+G/QTIB2nIuxM80qTT7lNIS\nCfmQL2GnCmvBpDaG11tpjviAVv1soD5SKkk6wEB/2C5lkxGQtFRdgYB8Yb0N\nOprx\r\n=Xdlj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.0.2": {"name": "@eslint/eslintrc", "version": "1.0.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.0.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "6044884f7f93c4ecc2d1694c7486cce91ef8f746", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.2.tgz", "fileCount": 29, "integrity": "sha512-x1ZXdEFsvTcnbTZgqcWUL9w2ybgZCw/qbKTPQnab+XnYA2bMQpJCh+/bBzCRfDJaJdlrrQlOk49jNtru9gL/6Q==", "signatures": [{"sig": "MEUCID9ikz3qcg96UBpwgAJDEdW4cA9A//xFdugluLS+evpfAiEAh1WRDBVmvM4cbNgBt9qzzmyUcchOV8zlXtMh3CMfzuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640122}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.0.3": {"name": "@eslint/eslintrc", "version": "1.0.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.0.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.1.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "41f08c597025605f672251dcc4e8be66b5ed7366", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.3.tgz", "fileCount": 30, "integrity": "sha512-DHI1wDPoKCBPoLZA3qDR91+3te/wDSc1YhKg3jR8NxKKRJq2hwHwcWv31cSwSYvIBrmbENoYMWcenW8uproQqg==", "signatures": [{"sig": "MEQCIHH9WA9cRj6YY3R86G71D/HR/9l/C1UFSOU4Ty0WRW91AiBx/o7pf/Cd4Ju4v5bnGG4PtmIOp08+MuUsoJEvcNqZPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640703}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.0.4": {"name": "@eslint/eslintrc", "version": "1.0.4", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.0.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "dfe0ff7ba270848d10c5add0715e04964c034b31", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.4.tgz", "fileCount": 30, "integrity": "sha512-h8Vx6MdxwWI2WM8/zREHMoqdgLNXEL4QX3MWSVMdyNJGvXVOs+6lp+m2hc3FnuMHDc4poxFNI20vCk0OmI4G0Q==", "signatures": [{"sig": "MEYCIQCbzJufZzIvjioyxCiZxEuL0JmaP5/eDlOm9Nz5fElicwIhAMCYmLhAJrz6MHsgTNNsrjTz3o0LokOzhi5ZS6/C07dx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640678}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.0.5": {"name": "@eslint/eslintrc", "version": "1.0.5", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.2.0", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "33f1b838dbf1f923bfa517e008362b78ddbbf318", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.0.5.tgz", "fileCount": 30, "integrity": "sha512-BLxsnmK3KyPunz5wmCCpqy0YelEoxxGmH73Is+Z74oOTMtExcjkr3dDR6quwrjh1YspA8DH9gnX1o069KiS9AQ==", "signatures": [{"sig": "MEYCIQDYrSV1Q7uj07lVKTB6uYbEaisGhmIa6FsLQkATUyQPdQIhALwY4w5EdsJxR2gTtUmm5VCPrqIJmygLj2SeRicZ4iBG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 640678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqsz6CRA9TVsSAnZWagAA6ugQAJddtrNa3EH+SiEXH6o2\n13oBy2twE3tksF96hqjn9EdX0R2Md6PZGsvszod+zF/vRGNhVFUiueXjhkfC\n6bQfOTHG24iEXtwP2sdJlOSwg1Emstwn01/Htz1W/QI3W7gXPSGs4aJyBi/X\nd1nILxcMcywMrzUlKeCIIUDU4eq+aWopUQm3K8ZY1k6u2Ps5sTcj9NClXEVG\nAZvS6FX9xTJ1xEHc3EJBsa3mm41Af4UBnkrnoP9IGrcIkFNR+fD7YfrDMWdB\n3SitGtcqG2rQ+RiDsDQiLUY8Fc2YLX7XZxuhr5sCxm3PKwLHXUZgdGoJrwDc\nyW6R0h1ZQ8rOwHdVyGMags/RWRmzTmubV0Sq51hzVYDIaL2g1iw0JEVVZhtP\nuzbxJQKcHLHU43qa6k+zs2BIvOFweUxrOJPvU7NCU+Gf+xwyqe7H3i1Siwka\nhlgepbMVOK/U8+3uvTvJ9zEtaC4i73itrI87e8MLSLYrH5VX44oLNcQrR8J8\nY7wvh14PGApEKQCwNiHRB5mmohFXEChqX4CvzPfrgk5nmQJUmRIb+hOFZ+Xr\ntwOtACuKzwq162B9bSGGkgAvcQMtCMIuouCjqbxWN7Rkkp3L+iFkRSc053wy\nEzM/28+Arwz5G0u9taeIeX6QDtkQXnp7lUJq5MvGBhPUMLqgSC7dxfjeTr9q\nQwB1\r\n=GK3p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.1.0": {"name": "@eslint/eslintrc", "version": "1.1.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.1", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "583d12dbec5d4f22f333f9669f7d0b7c7815b4d3", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.1.0.tgz", "fileCount": 30, "integrity": "sha512-C1DfL7XX4nPqGd6jcP01W9pVM1HYCuUkFk1432D7F0v3JSlUIeOYn9oCoi3eoLZ+iwBSb29BMFxxny0YrrEZqg==", "signatures": [{"sig": "MEYCIQD9MvZlbaapUVokBsJmlwsdqDZtXvVM+d8M7LMFBtvqvQIhAJ5lDbysNp8uVV5Cu+JpnuiSMDMe1byU4pcbzXyToxRb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 644780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBvLOCRA9TVsSAnZWagAAcesP/icgiC8v3LSiNbvovhLq\nWlkHWvfs1m1cKswwymGJ/bg4YjLw28xIl8M/x/K2ZNp26TqYRPZphuhY7aes\n3THmB+rh0M1YRVCCwBGcdw1Tm26R3LXJ74m6BnosxBWf0q9sQq2qmb28RQPx\nyi+OhnsDEGSnjxJ8bTkRbjGfRbvM6fSQ3tcSLKTeT+uvvJcxGeRWmtYn2I/k\nSprpcuuuhX34RyCX8pMU66ERXtE20VvxgG2oWj8Sqlxc1RCtPtqpRtBcO+Lq\nuYYoSZcDVgnqymI6KVu3HSQGzw7oydQcl/Y+M/XcXuM6/jKzcD69oYhcnASm\nDTC1xd1OZ4hteNlQ8Dt+bUla3GP5v1zo8GxadxKIJThtVmHz5nQwXTTrrs2c\nrn8Tz7h/cgKE6Zecy3A2K23i7YMJ90+BP1Lju3BnGUSmlVffBpSxunqdRSsG\nHE+syFyEGury6oxKE5zfYG38MRsHiiRmGlxOMoH5V9Fki5JSj68femlMwNl2\nWKljXW5B9lnakXpk28SzcvUMFEIkKU3rwILdak/EcsKpbOhjFf7VAbu7um8g\nEtZdoPbjZLVBgK1ImpGptNA4ligSOwi/fQEXxQ1fsM9WYAHhqbuW13DMWrUd\nmIDIRyZguM2bDe/ebHbeuCMebOxYOsIK6HBkWHTPWwhkVQi8OuBGdZm0R10y\nxTJm\r\n=LcgI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.2.0": {"name": "@eslint/eslintrc", "version": "1.2.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.1", "ignore": "^4.0.6", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "7ce1547a5c46dfe56e1e45c3c9ed18038c721c6a", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.2.0.tgz", "fileCount": 28, "integrity": "sha512-igm9SjJHNEJRiUnecP/1R5T3wKLEJ7pL6e2P+GUSfCd0dGjPYYZve08uzw8L2J8foVHFz+NGu12JxRcU2gGo6w==", "signatures": [{"sig": "MEQCIH0Lt2kljeSfpyMVHxkW52++HR8poErkdCXvi0eamayJAiBBekw/Z8x4G4QezlL0tbPjofw7tF5zznFe22iNCMcNHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGVL1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnFA//Xfsc1IE/3wuxVpKZ28u7GVws05c1Wn+8UEjHRdTsjTA5uep4\r\nTgrZHNpqoPT+xFsZsJhQ0wEAO2vBsCekVjhld5sIf+bUSxdgWk3sANIeLhtg\r\naveKgiKqqJ2cFcdph1Fmmb0UYEx6E8jcoVTcatvDfsqitpYXJztURSUttiBM\r\nxD/ZCFh8pKjPV+X9NQeIQRgwFZHdF250XtaqWpsrjGrrC25lSDpJHJ/3FQy3\r\n/SwAzOTw6lgQa+03JK78kvEaAHVpy78DOaYEKDqmjnsADse4axyfx42WkyVI\r\ncB07Ln2IAWf8kSGXgw0/Iq1e3dhXtrZOmqKJVAq+d9DtuvDerOA83lsokzPN\r\n6s0HnDMG419u1O3otQEg+p7AS5HR3bFSmiLdQgmxqH/8JRVesynkBhtv0GZe\r\n4IKpYzT1fDqWpg7oFa/buBcTJH646v3G9lRrHfdwdgiokqtM7Z+soJDLXcq9\r\n7yWjuVo4yTgoCL+H3W99mwKtyQ3AWeYPNGX5DesT9UiU00p82QVWBtG2Pa8s\r\nTX94HBclPISQSdfBQBFpjtu+0I8/jPyPjrlTIRBg54013t5KPUhnwIT/HuQZ\r\n7hI8xU3vlC3MXmjDRpWaxtf2aXJtMWUkO/QRvW05XCahT6AqT6PpwIONptUO\r\nRByouh5Vzrfetv+fkg/SILAD4tDJg0myyZw=\r\n=oIgo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.2.1": {"name": "@eslint/eslintrc", "version": "1.2.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.1", "ignore": "^5.2.0", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.54.0", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "8b5e1c49f4077235516bc9ec7d41378c0f69b8c6", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.2.1.tgz", "fileCount": 28, "integrity": "sha512-bxvbYnBPN1Gibwyp6NrpnFzA3YtRL3BBAyEAFVIpNTm2Rn4Vy87GA5M4aSn3InRrlsbX5N0GW7XIx+U4SAEKdQ==", "signatures": [{"sig": "MEUCIEtkZpzJHsXBA5PgWQ4hKFK29rrDv6xbcWj4f/RUgWZgAiEA1U+oabmLjF/z0yUciNsxe10wiWitF80h5y2ibQEb19A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiK8QOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvBRAAiEHLMpa0Lui3O3JB0AWDDrNJLckteUKE08SDAFeFFyyc/mRC\r\n2xjnH1Oq7Z9ZL4X2UQPGQuTOdNiWZfWO90m1bsS1azujg0w6SNZFpI9n7xJf\r\nr85zCAbcfdWzDVM9wu8K6fSlOn05yAZKxebiiBgDzdg7SxwOaRp9zideB737\r\nG2FNWtN9AQ1MrvIaA4lBMSaQE0KI9N8fVprliH4JbJGof3JwcFqQXfNFNRS+\r\n3O8kAk7lBm+0Q1qCM4eWp6yEIFpdz0NGk8N1+iEwwP5oUfnXJTQ0/sdj9u08\r\nGQSyOutOUDJSwz2rTcyl5B8Wja/H6Qfx09bNoiyvc5kgfrnSKH8HgsoeWpAW\r\ne/qF2/pCdtcHAuwpANOADrrltski/I9/TL8tve+xqMyDyIrolvi3yehIF8C9\r\nmleLrD5HC0339GWbFgwWM5EONEwCaKybjPGeu0PZV34cfeaMK2QZpnxsLjHe\r\n8cjuuA6VNDmzaJD9C4oc8CQowgX8mly6dtDeaei5B/ZROKDfz4m3zxrC4nOw\r\nv8ZqHlogrA+4cyyevDFqdA+Fw/AKbPZM3UfJJPjV4SIe9Yvm+f9wlcgv3RWV\r\n+mkXpWfyqS2Ms644zqUkcoxNcVkjlBiQhRyMDESKRMoShY0V4b6L+C3KCq3J\r\nI3nGTiA+bXm5rHuvW9C6gZbRjPQE4cNBZiY=\r\n=8Kd6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.2.2": {"name": "@eslint/eslintrc", "version": "1.2.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.1", "ignore": "^5.2.0", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.0.4", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "4989b9e8c0216747ee7cca314ae73791bb281aae", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.2.2.tgz", "fileCount": 28, "integrity": "sha512-lTVWHs7O2hjBFZunXTZYnYqtB9GakA1lnxIf+gKq2nY5gxkkNi/lQvveW6t8gFdOHTg6nG50Xs95PrLqVpcaLg==", "signatures": [{"sig": "MEQCICvi5vsmUQ7vhU2w0eRBbrCUCJdSzqWbdowP+sMyS7FRAiBt3ZcV7O+SRbb04pT8ZsIcT6KGGsoqnQrywubDjxm/rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYwnfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEMQ//V5siQGkemjvdzJFNEyFNlJM4+8xYH3kEctzoDJs9zmmFwVQU\r\nIMpjVBlJ4wxznPgW9eFNv61cQ/unXYRq8PmZFdYyyRJNU6XvaQxkWMchxBvD\r\nNgKdtj4UdUiB/lWsbq6jtcmH1sSIxLZeR9jdojf+fuat7O7PMDe0zQZGDUU1\r\n33i9xKdkoSAtaRjN09348F6qKQa94FMAAeOzRdgEFVb4zb78hjSj834T2Mcn\r\nlQR3WOd9ZYLhzpglkGJWAXYd9oddoJYZ3Ss4TNGvznRo10wN9F8aZGW/XWu9\r\nepcbZnTeREl1/aeroL5DqiHDcLz7Rx9YzfgFPRgC23JxaWM0lbj0bng64e20\r\nMK/NBtoA972h3QRdEEwxyShwyaYNhb8QJJZ1XQ7D0uuNRAanc9WuRd0MhJfC\r\nYVzRlhT+9axV2DkKnUM8JRuG45seEo6IJb9MIK9+WfDnM4XR/VIjYT726WSC\r\na3zyg9xIAP2lDncZCdoTpgqujTSpsoVXpKPzIRSkcVpbtQt0T6bU8aGW9J9T\r\nOt0iTNkQGQwotod1VPZDOBJIeNgcvAj4VysrFiRogUpOom13IjTZSL332Hw6\r\nii0OnI1O+vbu964lMJcM3ZtM21c0vu20F94+00uBXca0jUH00ONob5W8Os4P\r\npDbEE3TThpXthjHWV1fNRRpUpMnmZ10+QGc=\r\n=TNYa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.2.3": {"name": "@eslint/eslintrc", "version": "1.2.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.2", "ignore": "^5.2.0", "globals": "^13.9.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "fcaa2bcef39e13d6e9e7f6271f4cc7cae1174886", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.2.3.tgz", "fileCount": 28, "integrity": "sha512-uGo44hIwoLGNyduRpjdEpovcbMdd+Nv7amtmJxnKmI8xj6yd5LncmSwDa5NgX/41lIFJtkjD6YdVfgEzPfJ5UA==", "signatures": [{"sig": "MEUCIFq1wEu1jyHlVWhWR2NYUC7/QQdqBtPASYQ32Y6rtoSBAiEA8DeHXQl7QEn/+V5ayywvtiftQsiam+KXCgjWEeQnNCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidYb0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplFw//fesAlx19Z0mp4TUccuf0lvS4izu5byrI6CPoGFf6+SRsv+U8\r\nfaUtTw2iOVibhG1QX3H7tnB4ttUBBOpmn1J2/FBAFlqlWdk/4oF1xA5aB9kc\r\ndfkTqNtN7IluaNUfW1C5582s0BT8J4N/JjCWr36PCyJktLY/uhK/+TBK7VuM\r\nJ2DIVQsvO+Cnc1xkoe0PW+G90yrA69/7NqT3xctzN5uCTr2+kEkVAdKNSD2j\r\nJIEpQdBKekMGdRCF6sCN4TqOLtYWAfHUlsMisqZ33D61VVhdfLQQEA+8f2oN\r\nrXJGcSL7Foln0Qo4Um8rjTGx8N4e80dqpNGsT229rQ+H0tyBy+B9l+xzfbj6\r\nRp68w6snpKO8vzqTC09uYiEJC2jtirgG6xDEt/gTQ9H7KhTioUNNAgLw3Jf2\r\nRp3he3EqRGJmR5EGPI7pFYFDfMj+pB3Y8d2j+VMLVK2X0P21YpVzckSxGmig\r\nDkEwru2VE/6dZIz9coSpy+mGZ/9K3310COmswVOdPAe4BEw5Ok6b+keIsLtV\r\nAHX2P4Z6utc5LOK8JepPVHJhpNZno75PhuYx9m3FVVYCNN4jfCrZkWGlCpn9\r\nYuQLJ7RwI2hSgH+EafpdzTo3yw7GtZacXZypRapLNDDOSGzKYce6gnRbMJbD\r\n1YgpeFjIDFcCYMGcEirwW5SLkkbAWp+L4L4=\r\n=6XN6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.3.0": {"name": "@eslint/eslintrc", "version": "1.3.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.3.2", "ignore": "^5.2.0", "globals": "^13.15.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "29f92c30bb3e771e4a2048c95fa6855392dfac4f", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.3.0.tgz", "fileCount": 28, "integrity": "sha512-UWW0TMTmk2d7hLcWD1/e2g5HDM/HQ3csaLSqXCfqwh4uNDuNqlaKWXmEsL4Cs41Z0KnILNvwbHAah3C2yt06kw==", "signatures": [{"sig": "MEUCIB9A7Ou3ZOOqpBeLIzzOvQ/SBwgJkCaAKhu461ollCfaAiEAtuA4Q40sRMBsk3/KBWNYeI7tUfKWpIR2jx0CgVMsAHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiiAhJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroyQ//cizPFfzSiGl3IwRBCOHUERRE5s1SI39R1r55s0DHFcoLjlxi\r\nJH7y+XcipKb4J9AL/Ki8do+OLjvt5DabemuCQEeFpNeYdu+aDj9Pk/HbGUrd\r\n+OOV6oEfOEIRS6UqEOhVhxA9y0WERmloNB1rAAdvv4jExso0Fnyk2tFeHfL5\r\nSunfqru3rNjxWOdViimpd13YFosS1eDMGIK9rrqKgbbBF5rrHroP9XdAzAD3\r\n8DDJh7xv4bUHhXyQEjKMUxmWj6rzu3oWH5/20hm2fQOhqv0hSTlUZbBi5RI/\r\npMYj8gSS+FoAZoQF86BmKuT1miQX0ihAH819aigpFXsIFVntnS6GbuqmWHAS\r\nUiau+LXkAJ7mGCIniWJTgXR/S+/vhQ8DwxCcg+lOo1bWyDYWwpxCa9nR1k2z\r\nXNneqs7Lyf9LwkH/aSKRrmQdvIWDsGq8WWFpufH2s8cMINzDIIf39UAFtrJ7\r\no3XwNqRA+1Fpn4EdbLy/U8gBHhBNqstZsXXsH/OTvccdEIGzGDn65ng3RIDD\r\nKodBtGy+i+9UmImoax/JG+CgLsDoMykkm+29u4XnKxzePUPd7YXik27PLCoI\r\ns30zNRCaNcpBc2xLLgCfANjJ4wg+6Bcn5dzVBK+6unJMEc8p/+Ga3FWqvxRO\r\n2Qb6fh29LcUlxAvyCLMpaps1h6b2BCEK10Q=\r\n=+g7Y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "1.3.1": {"name": "@eslint/eslintrc", "version": "1.3.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.15.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "de0807bfeffc37b964a7d0400e0c348ce5a2543d", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.3.1.tgz", "fileCount": 28, "integrity": "sha512-OhSY22oQQdw3zgPOOwdoj01l/Dzl1Z+xyUP33tkSN+aqyEhymJCcPHyXt+ylW8FSe0TfRC2VG+ROQOapD0aZSQ==", "signatures": [{"sig": "MEUCIQCpq6GcBvsC26HgQdZJF0gVEk6GzoLSEhPLE73+FcqCZQIgWIpGv6kSgF+yQ6sQi0mT0+74MaJ2CHekMWrLdZ9t/qg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCULBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoBQ//Rmhhfwm+tif17k6wMCUtFcVOh1sK/pQ6sVzFRwEFMTxid1Bi\r\nSI2vBtwk32Iq8vkWmpyS75zn9JLVwSidqipc2JIxS0Yipvg/unWfEyHWqu56\r\nzPj+jzXyYtekF9QyMaV0pU8CztWlyjg1uuHVBnwuxC96otnGsMuHDfPouHu2\r\n5nPHaf5uYw833O0F58xgKvJjOFIAImwBXI5zfsAlK49iV3KfwMBEaF3w8QMS\r\n6fMzKFStlOQ0UOHcY50Vy9Sy1I42xsu9D5/rMljKo9gfUxTnt+wYjdXX2MEN\r\nICzJtRJyJ6s5Z8e3yO/m8ASTUxDzSwIJvQsRNoghhoMccVU7ThwP/oQyWycO\r\n8bOJXjcU74wvTEZGb/SCZ1KJt+zYJLeDwA3JDphMGx5r+G7B8GeUqZdwX1yJ\r\nKPRaSW3O+FA4jGgnmbXDAb0x3L4Zde4jmlxYeTQcqTm3jxXU7yMxkzMsIizf\r\nZw+wRAxBkFekbZr7qzbdzoDddrYUHodkEkA7mF6OXoqnApSM0zkeM0fyUmwS\r\nLcADZ2uCj3IvKWyROtHgKDsqrC3LjmtOxgZFy+KX9VnvrNQ8YkDxuKAjwmOJ\r\nlWWDiOD8e0z2qHCUKAs8SM3Erk/njIhCTLunLIX1nnHqtcCSG/2bzW7Ewhdr\r\nO3jI1sabBU2rfczTonc161nD+pVD1AIKuz4=\r\n=RB+C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "1.3.2": {"name": "@eslint/eslintrc", "version": "1.3.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.15.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "58b69582f3b7271d8fa67fe5251767a5b38ea356", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.3.2.tgz", "fileCount": 28, "integrity": "sha512-AXYd23w1S/bv3fTs3Lz0vjiYemS08jWkI3hYyS9I1ry+0f+Yjs1wm+sU0BS8qDOPrBIkp4qHYC16I8uVtpLajQ==", "signatures": [{"sig": "MEYCIQD89kPLrTFg3Tk8+d8PPFOy3ggRTcHJU0tZKVx3Yp2iBwIhAPbiJG+/jRDa1jeefI9FRT+0RXxv1wAd/4R2OK9rA/W7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHryFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+yg//SDVpTE+pUgrdvutK7bfCkpffs7047BXjMHcxRV1tS6L5jviY\r\nlC2l4y1HFfy8UqDPWDm/19AItI6m9yUDkRQfz0EJNWD7+VOtGzj+H4P6IiIW\r\nWg5G2sO4WmIa2F9x7o68Om+Kg91EhgksP5HIn/6q8QLiNS09QcCO6dEQnEgq\r\nCg+E8yF1KjDor03m50z9zivzlTzCPiiDH1/P+tFh6mcka+5wqeW4u8gJWM0V\r\nic+zgGMXMDysmDBM77v84q+ewXhXHODSqJItjDmGK9hzwRkiEAISMrvV38t7\r\n5HKPhP9FWyU4R4QS8w5gmvZjsC+Yiu8p8vz87NjvXsIk6+/NII8eaOMajBn6\r\n/GAaj4c+RFyzDTJRyoB74EzV4KjXIQhd+s7APGVs3VpuvDdJI5cVXtrzxTjD\r\ndvw03BvhO6ADhpHq07gpw3qbVkLdUpf93aIaEgx2OqzsXEFsNrodxRLTQbzk\r\nJQj8GM8kHIbIKr4orpAhrksUp+Tr97nstw3rga1ptLjNTRs6YnZMTOomC99i\r\nhk0VikirGK7Ja8R8xGKpTGKb5pgpTtB8SuGPq9W/Kvj+9Qx7hTUBGXaqNTHN\r\n2zy4flAjeJkv72AgJRhKOZsjdawAZzJ91yZCEIJHaEcZHa8JNmJmpbF5b21K\r\nc1L9/SW/Vo2+5T/tpvquoLIY0Lo8pKKZS/4=\r\n=UcHV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "1.3.3": {"name": "@eslint/eslintrc", "version": "1.3.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.15.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "2b044ab39fdfa75b4688184f9e573ce3c5b0ff95", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.3.3.tgz", "fileCount": 28, "integrity": "sha512-uj3pT6Mg+3t39fvLrj8iuCIJ38zKO9FpGtJ4BBJebJhEwjoT+KLVNCcHT5QC9NGRIEi7fZ0ZR8YRb884auB4Lg==", "signatures": [{"sig": "MEQCIEfFDhvLE/KoQlw1nODTPN+fiu7v31rSZw1HquhVqOXIAiAv6Cxe4C1zV91NcpUfXFTqdtykuoA4RK1TMdQbmRTJ+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQJ5BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxoxAAnY+Dsb4qoPvfZxdjQhmcDn5RZeVf6rLpUQpUYQwV1BzuyM6R\r\n+FjHSMHhW59q1H3lwqdht1O8y8JH9J986XfuyT4Q4ZlSIrZeMk8EgfktxyKm\r\nwp8M/acVtenaUdhuwRUnXK1Pd3W0/d065hWP/DTrKxRYufD0P8XDy/bke/lU\r\nRq9nIS39ckVeWKsmoO3jxS8bXDT7DnT97RNP6YXi/tng/ajeg/kOiiwMmq96\r\n09/zxpM3voibsaYH7An5+Lf/n73/EE74cS7LxlNMc+2hCtGdWAkjnHqEuKVi\r\nRWvQk/nnRa0ZdYw2hCxmsEbubNu1qSqeNJu153SzENE4Zz+/6AMXTl69PrwA\r\n8WWpNaCezndowNdmFvDsy3pX2AXGXMMVxiair7EMYhfQ+wRPF0J5xjxl6oqj\r\nsymzfm4zPFwLD9Yzk1PiGyANBrystAXDkLrvXPg+71Mv3SW4L5PVVA9xgaWe\r\nKzer8nj9GFR1A57ZZ40Ry4iV/jSApSWBDAC8l4NTJCF8+Qana/7urA3d2LxQ\r\n0fruH6Jjr1fLTbpfgX6l5/EK4lLNy7kLykMoHfkeF3zhhEvfQeacAX80nyOy\r\nnqqTeIwU9FDxM4Gs+dflT39HFemDqKM5GGuqf0jFPjuvp7gP1g5Rd9pe1SmN\r\nwM2qorB0Rdg6QMe43R1cyXymhH9PQgiUDd0=\r\n=WRWb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "1.4.0": {"name": "@eslint/eslintrc", "version": "1.4.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "8ec64e0df3e7a1971ee1ff5158da87389f167a63", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.4.0.tgz", "fileCount": 28, "integrity": "sha512-7yfvXy6MWLgWSFsLhz5yH3iQ52St8cdUY6FoGieKkRDVxuxmrNuUetIuu6cmjNWwniUHiWXjxCr5tTXDrbYS5A==", "signatures": [{"sig": "MEUCICQYVSToe9wJDfvhtICMe83q7AXCZo/vWYf2zBo8s6ENAiEA/d4cdIbrEKmryI1GgjrE6R0W0alF6JNp5pYf3rBTfR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnPXJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooLA//Sifb1p0GamEL5P0DQYdenx5ESuL1WT3bOeLweezj59+1yxQh\r\n4X0qbd+ZJHLej7LGGeqwMlhqgmvw1iqvDsVVI8Ic0ZEINtMT3uXB8+u7/hR1\r\nnOq2uyHRtHwgl8Vp3lOdM0/bJCwAvYgewZl/Gnw1Sd/v5uBi9hztiZNIADhB\r\no//K8cNbY9PnYgSc4G1hJFkXLrtjZAPH0DFu0nL3sK7ErDIO54UeNvawseIL\r\nr3lfXfil0Y6iwA5vm6zXnL03ddMX1hEqR7se3wtBhviVVA7bUKR6G2ULk2v/\r\nLoH2HlF7zvLxP2LnDDF2fkOGAZ82dK452Fjy5k/eDowKF9SxmTqo8ENMR/vM\r\nsZO/TisG13T5T4TmNbi68GpBSay3kpoLExiqxHUpb/fn1aizRhv2L3gMsqyp\r\nX2K7yuq95B4vPLphS/DbhZbbmS0odH0SZDcWILx4UweQl7vNWt6hs5FdI9dr\r\n/z050HGH6LgPEATUNOp6OsFpNSpcXi0MIzfa5BSLd6t58HPraikLu0jZ22NA\r\nbRluvAfnqCis6WhrMEkxsTv6Vp+IsB5xBw/xomE0d0XnWv2aXf54Lq4DMQR3\r\nkHeELCrmW8F3PeL3g3uBwXF707zrhSFfnKDpdq7wovR4ZD3F39KK6Lp5vqFo\r\n6D7ZZ1s8LKjYkyEtG6JyaSov0jeTuoPgMBI=\r\n=rQxi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "1.4.1": {"name": "@eslint/eslintrc", "version": "1.4.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "af58772019a2d271b7e2d4c23ff4ddcba3ccfb3e", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.4.1.tgz", "fileCount": 28, "integrity": "sha512-XXrH9Uarn0stsyldqDYq8r++mROmWRI1xKMXa640Bb//SY1+ECYX6VzT6Lcx5frD0V30XieqJ0oX9I2Xj5aoMA==", "signatures": [{"sig": "MEQCIGm6utqvF84oVpR0C7imP19tJ9/pGXpcADGbqf0IryOSAiBsO61/9Chpayma+7rl/TSUbiCfjOK4erV5Sef+P1+4AQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652462, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjr78TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooGg//ezODRLCxfxJgit04Nr7404Mmjm4CCvANRpu3sUV0YlOHtuo5\r\n3HTPXuLJ1h3N4mQ2dQyaWbSI/CcXllrT757FI95Y9WCAJIIxZ5JTC4kjoIQ3\r\nNGFnYWBL5YSyhc5SvWPliMEbJeZvOzrPRlGdMFsN6jvQV98H3j64DMPzYViv\r\n18cQAlY4F+o+LoIWRR3sG3wQ4i0dutflDqbYEPpmscinTn/NzlLga7mFGMWP\r\nTPggYQmkrbkzFhpRcHIDw5VVEuetqtBLqZar0uwxOrl8XThZZgYoVJ7dKcQB\r\ntUZd0P8df8vNBtZdIuusN4euULwboPg+qO3aHuC2Rt5/NUrgK7YnqwuOrvrM\r\ng8cRPHizRUnNZBWb/KVQsil56l6LaqzOeD3hZeh5rN22MZ5aENacsfb6y+YU\r\n8z1LnbAFzEzzM+2DL5QXdmdSpSU1v16oRPTQGR6gt4s1CK6jjaCRPQq0lNsy\r\nNHkaJTtAgR9+lQ1K2Ug04Ru6rSx42+G7ucftg6S9NxjYdEbKzi4LoC70cK5o\r\nYWgQTF03qQvVbHOtzkpce4JlI2qWlyF40PeuLYb2YB0Pfd4PJpyX7o4tv50b\r\nBeaIIZeaVNJRbsygasJiUPjetVG00mO4xlKNXBUl450coAJxLNgxvLUx7re+\r\nSUhJli/sqUvVWZ7jx1F2xIb14NluEw2pOy8=\r\n=UP3W\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.0.0": {"name": "@eslint/eslintrc", "version": "2.0.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.4.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "943309d8697c52fc82c076e90c1c74fbbe69dbff", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.0.tgz", "fileCount": 28, "integrity": "sha512-fluIaaV+GyV24CCu/ggiHdV+j4RNh85yQnAYS/G2mZODZgGmmlrgCydjUcV3YvxCm9x8nMAfThsqTni4KiXT4A==", "signatures": [{"sig": "MEUCIBlqOrLMLrc/+E0l2Gr8qnMq8rvNo/QdJu5JHjLy77UKAiEAjvR2U8y7yHGNrY0gGUwtpzBN+iHG5G5RQEC9RMRgYGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+qkeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFxBAAhT5kRAEaTYPjbhBjHwSQwpqgTif+xfnDcmJPXNIR2K6VKaET\r\nn3oVUpfzzcyyPwaDs6WbSj1sYsTkBEC/DQwT2k4tNTuaYKBOYrNw+HMEBKB+\r\n/CC4u8p3L7Hb8vDuJJbx3Q0veoKIisv47hKvBA/msaq2HNLD5QoaUX5ta7J4\r\nIoxLvFK6gnzT6NmrOqa5czMlGo6XjV8ctCwgKpmSw8KbegzMft5HPbp3SMoX\r\nZ1Vw73UhjgkxFs3m1Sch3LrEeVkno/k0U452WjE1yWiyt6hpoRGlf8Sved1L\r\n4beGvCchkyedrORVF/6tLQMZMSGJi/JLmtglmB4tEOV5V5AsCDGWBoCrvuNY\r\n3XkywLg5Z9jB43ZHoOs2hQN8PGEhBy8+/TEVJ/OihN7j+i4OWArfFp3AehoQ\r\nryLk70cIcnzJTNApGpQ8XlW5hmXIFXuSob6DnO57LbbEg8WtUXw+qjvt3Kpq\r\n33IgE9/b9D2jZ4Jr4BUalBa/UdEsZE/OarsJ68DKuM2rpr1BnbCZuJbFObRn\r\nHOAJwRR62k51opiQvggYbMsRqxbAVA1nQb7ag2gBFVRkZi7zkpvJ8Rl2zYze\r\nV4Kb/xybNuoBowzpbJ1ISV3FAU9/lheBkiT37DINE29/isFNSYnLek+FUKA2\r\nMZmmd9dEPQ+ah1PHp4+tsspgb/J3AAD0zTY=\r\n=cQlo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.0.1": {"name": "@eslint/eslintrc", "version": "2.0.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.5.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "7888fe7ec8f21bc26d646dbd2c11cd776e21192d", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.1.tgz", "fileCount": 28, "integrity": "sha512-eFRmABvW2E5Ho6f5fHLqgena46rOj7r7OKHYfLElqcBfGFHHpjBhivyi5+jOEQuSpdc/1phIZJlbC2te+tZNIw==", "signatures": [{"sig": "MEUCIGku12khUIffJf87XskDv1/3BBIWpxN+AwzlAnmhYOR3AiEA0yCNlwkFBiAB03EZZ+JHWmCeTDpQ6UZFrwI4kG8RCxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC53hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYug/9FYhWhB5vpNgEB0i0+beEKaiyOocoVWs9kb32NhXOeNiLoXo6\r\nKid22y/3ogfn5H6bNC1Y324p5ZV87P0zR2uwI5SEfCjSJeZ4oD5YFVDYv1mp\r\nkfXOed02zmSeuHIsTn6iYKg2w19oCjG48byvj6JNssFGBnfZzOxdEBIU4Ho4\r\nhALNZoO79axIE6vxjz61LaaJIwMNDw9pBzH/aJyhu5jPhjuOhcCynoVyrO1B\r\n5Wpo35TDzfrXY1+ygzQRBGL2sBXOoyoX7grPDijjm5mIsA/NS8yNeuyaI6MA\r\n2dwrymZMgN8KTFuWgqr3oNVFvstUY4ZxaETjb9/CY3VnW4Q5rZ1dbQHCyqDW\r\n6qIXIgSgxgDh9O2x1sx5a0F0qJeSbbhoHn52OZEfym7pL0aapkGKWBfgCrFX\r\nkfSGta2YuAYYtj/H62SNd1EM0OW6CyYrMObZvBzTmUUJ8LcHI8uAH7F0a9qr\r\nY/VJVBlDN18e6k+yuOq2ja23fTNCgjssoP+qY/4K1SOMZnEMcleg1YWjvPbk\r\noxdAWT61WLQgvWQiP2x3conRwsJQmLB9nonk/B8s5Lo4i5p50vgTTQ3irpfx\r\nnASCZ8OwS8QjKumkbJ67aFkPS2Bo35xOXyJEPvcFOQ3HfrBARN6TgF2PmRb4\r\n9GJU0ArvDapyRDN64GN96hkc5mMvAXeNHf0=\r\n=FV8N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.0.2": {"name": "@eslint/eslintrc", "version": "2.0.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.5.1", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "01575e38707add677cf73ca1589abba8da899a02", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.2.tgz", "fileCount": 28, "integrity": "sha512-3W4f5tDUra+pA+FzgugqL2pRimUTDJWKr7BINqOpkZrC0uYI0NIc0/JFgBROCU07HR6GieA5m3/rsPIhDmCXTQ==", "signatures": [{"sig": "MEQCIA126E+s3qCr/C6g4+xM4AX9ZKlNHs1vDFKQBwmnD92MAiBe3RXF7tDgFnd4R40OXg6c0M7M/9z8fH7+gORFJatS+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkI11QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHxg/+NDt1hM4TFH/MEIy6cLsn8Glw4rCmxgeVVkZihfGqZ6cMkz/f\r\ndE/Be17sIiYkaVs2eDqB2GKs6tWDjuqi8BguEvq0iI98fUJbQxzoXQWYpC5A\r\nHEH0vk9DJWKGGVKUpJkST8vi5H7+Rznb27P8hK0UPRk05uf2OmsB+5b2cuj+\r\nVPXBcbeIyI3UWxIZLKjC7+xO46Rnt3IzyEmVyz/V+zIxEpLPSEN4qKaRV6Yc\r\nWGhhwP1TRw2dEmZTy7gn5vofXTgMfOko1/85GDpCmcVFeymo3XT2XHa43l9f\r\nYoEx/ak5VDbJCgKDJbwCH5/ru+lluWnMgYKxfP3kMHQTxcHH9Jsney3toUmB\r\n5LE+D8BMYciEl2GFCFv0IW9XAkQmjERECEHyZjglauJgfQMdoglqsAbBFEfu\r\nwgJTb5LLY1lnLFMwICoGdvML6yovtlhBHEsSVdC8Ne+cHSMwPcBwi8MQF0a0\r\n3HFvg6JcdmAIOkyLWAaszj7nBOo1di2eCS3m72pGa4i7YU3sxQeGPNP3zC5e\r\n4l/TGD5I5k+ICezvXqNdBhYpsirXqsNbrZ2sCR5gjPqet9QlD0J3zjntA24r\r\nUO3//8ViZikl/Hj6YZbep2zomfv8WAbIiOPrD3rf0MZGbaV6geFtUu3IBaWs\r\n3ssTF8GGApnnlEbE2VJw6/e0CdDw7OpQLE0=\r\n=5U/C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.0.3": {"name": "@eslint/eslintrc", "version": "2.0.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.5.2", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "4910db5505f4d503f27774bf356e3704818a0331", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.0.3.tgz", "fileCount": 28, "integrity": "sha512-+5gy6OQfk+xx3q0d6jGZZC3f3KzAkXc/IanVxd1is/VIIziRqqt3ongQz0FiTUXqTk0c7aDB3OaFuKnuSoJicQ==", "signatures": [{"sig": "MEYCIQCTzX1wzjenW+MshumQEqPeY3W/Hm+mEuPDYiXDgFXzfQIhAIHWwUl6LXn6G4anz02EH6Nu3tMqrrrgPk0eVaah8ZX+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652699}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.1.0": {"name": "@eslint/eslintrc", "version": "2.1.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "82256f164cc9e0b59669efc19d57f8092706841d", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.0.tgz", "fileCount": 28, "integrity": "sha512-Lj7DECXqIVCqnqjjHMPna4vn6GJcMgul/wuS0je9OZ9gsL0zzDpKPVtcG1HaDVc+9y+qgXneTeUMbCqXJNpH1A==", "signatures": [{"sig": "MEUCIQCRYg7VKXeSxogmiAbVWcbadBxfxYmL9HphS4ItsnlCqwIgQ/s94/nQqnjr8VuHamR/6SYzQNxspxkLgCqnj5UmK4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655225}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.1.1": {"name": "@eslint/eslintrc", "version": "2.1.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "18d635e24ad35f7276e8a49d135c7d3ca6a46f93", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.1.tgz", "fileCount": 28, "integrity": "sha512-9t7ZA7NGGK8ckelF0PQCfcxIUzs1Md5rrO6U/c+FIQNanea5UZC0wqKXH4vHBccmu4ZJgZ2idtPeW7+Q2npOEA==", "signatures": [{"sig": "MEYCIQC24UCXlwns8/dJApo4q6otwP/2kaDty/pJZwyI+6lxzgIhAO/vYlZDTg5g3CRrLE4hnG2Nw58SansAgxGCR6ri/6nu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@2.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 655225}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.1.2": {"name": "@eslint/eslintrc", "version": "2.1.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "c6936b4b328c64496692f76944e755738be62396", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.2.tgz", "fileCount": 28, "integrity": "sha512-+wvgpDsrB1YqAMdEUCcnTlpfVBH7Vqn6A/NT3D8WVXFIaKMlErPIZT3oCIAVCOtarRpMtelZLqJeU3t7WY6X6g==", "signatures": [{"sig": "MEUCIQCFZnNz9/4fsMMWhhtHBRR/zPUCA5xTwB0m+QpG98999wIgHm5tJHDDldxWrXWnrL2RgbeiI/uUXjWeMkMSBLb6dk0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@2.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 656044}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.1.3": {"name": "@eslint/eslintrc", "version": "2.1.3", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "797470a75fe0fbd5a53350ee715e85e87baff22d", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.3.tgz", "fileCount": 28, "integrity": "sha512-yZzuIG+jnVu6hNSzFEN07e8BxF3uAzYtQb6uDkaYZLo6oYZDCq454c5kB8zxnzfCYyP4MIuyBn10L0DqwujTmA==", "signatures": [{"sig": "MEQCIEjEHGdRO34SMTpwN2zbb1T1hh5s63Bi5OG//522qriDAiAQwwoOt+SHRtmMdgWX2+WiUbeRT8c2PODK1rVaXsPhhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@2.1.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 657201}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "2.1.4": {"name": "@eslint/eslintrc", "version": "2.1.4", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "388a269f0f25c1b6adc317b5a2c55714894c70ad", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "fileCount": 28, "integrity": "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==", "signatures": [{"sig": "MEYCIQCBJKP7mS1FB6msDX0VkBr6HPMmrd7qWQEJ5yo/Q2JjUwIhAPzr3lGRdA0OOIvAXY/iNq49Da06WYIdu95k/SAMubDV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@2.1.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 658746}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "3.0.0": {"name": "@eslint/eslintrc", "version": "3.0.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "eac0198d2dd82d11356e5bbb8a9bf0fda08d5ea0", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.0.0.tgz", "fileCount": 28, "integrity": "sha512-R8p3jN1kdWvFRiRfgpUxZ4PMgfJJFt6NuLGDnnqLb7RKmsd5Xa0KqRMjmaqRO7e38ZbG/9zKPgDjeJeqsDofSA==", "signatures": [{"sig": "MEUCIQCTvT+e6NsD9gFgSZGyJVf0Fu3WbDVEyv4fgk+v8bySQgIgcWV5TMHibybWaVPRGTuSGKfKfHDl57U6orHisMu6IGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 667179}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.0.1": {"name": "@eslint/eslintrc", "version": "3.0.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "ignore": "^5.2.0", "globals": "^13.19.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "ee628808e945cd7782df05ce50eece525cb48e7a", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.0.1.tgz", "fileCount": 28, "integrity": "sha512-xXm39r1RgOSmPCqlhn+E10KPJ7JKrpuBwsAVw/++5dS/Sa4GAi0smby0r0wfTN4gNpkk9iij2hssJMXHSmQ89w==", "signatures": [{"sig": "MEQCIC2KJhFt8ATbP7oREZMKuowi+aRvWT4oYHQEoqAciVjyAiB7qW7CFyqEv8dfke7sNc3X4yVWDheak9LeiZ1NKHakMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 667929}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.0.2": {"name": "@eslint/eslintrc", "version": "3.0.2", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "ignore": "^5.2.0", "globals": "^14.0.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.4", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "36180f8e85bf34d2fe3ccc2261e8e204a411ab4e", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.0.2.tgz", "fileCount": 28, "integrity": "sha512-wV19ZEGEMAC1eHgrS7UQPqsdEiCIbTKTasEfcXAigzoXICcqZSjBZEHlZwNVvKg6UBCjSlos84XiLqsRJnIcIg==", "signatures": [{"sig": "MEUCIDW5GnkTbIl0Nm42HsnJzNjWmFvOa4ioTDTTJfmgrPuFAiEAu/SlwVxqUJnHG3l4Sw/iqtAu2XZ8B0LV0dfeERGI+gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 667928}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.1.0": {"name": "@eslint/eslintrc", "version": "3.1.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "ignore": "^5.2.0", "globals": "^14.0.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.5", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "dbd3482bfd91efa663cbe7aa1f506839868207b6", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.1.0.tgz", "fileCount": 28, "integrity": "sha512-4Bfj15dVJdoy3RfZmmo86RK1Fwzn6SstsvK9JS+BaVKqC6QQQQyXekNaC+g+LKNgkQ+2VhGAzm6hO40AhMR3zQ==", "signatures": [{"sig": "MEUCIQC5qMDb1DZ3+ktVYswfflpbBPdRAo6jGOdb2yor7raGkQIgFz3dXfhvN/Cy4/vMwfi95DEolERuEjn2MD78isVn8UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 668106}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.2.0": {"name": "@eslint/eslintrc", "version": "3.2.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "ignore": "^5.2.0", "globals": "^14.0.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^7.31.0", "rollup": "^2.70.1", "shelljs": "^0.8.5", "temp-dir": "^2.0.0", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-config-eslint": "^7.0.0"}, "dist": {"shasum": "57470ac4e2e283a6bf76044d63281196e370542c", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.2.0.tgz", "fileCount": 29, "integrity": "sha512-grOjVNN8P3hjJn/eIETF1wwd12DdnwFDoyceUJLYYdkpbwq3nLi+4fqrTAONx7XDALqlL220wC/RHSC/QTI/0w==", "signatures": [{"sig": "MEYCIQDC1uCIorp73TLMPPPznoE7H9ZJBi2/jubNNhGAGEUbVQIhAPbGbTje5y4yBH5Fm6T7Mj4LYOuSrHNhOTit6Bf7C77U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 679783}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.3.0": {"name": "@eslint/eslintrc", "version": "3.3.0", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "ignore": "^5.2.0", "globals": "^14.0.0", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "import-fresh": "^3.2.1", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "mocha": "^9.0.3", "sinon": "^11.1.2", "eslint": "^9.20.1", "rollup": "^2.70.1", "shelljs": "^0.8.5", "temp-dir": "^2.0.0", "typescript": "^5.7.3", "fs-teardown": "^0.1.3", "eslint-release": "^3.2.0", "eslint-config-eslint": "^11.0.0"}, "dist": {"shasum": "96a558f45842989cca7ea1ecd785ad5491193846", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.0.tgz", "fileCount": 31, "integrity": "sha512-yaVPAiNAalnCZedKLdR21GOGILMLKPyqSLWaAjQFvYA2i/ciDi8ArYVr69Anohb6cH2Ukhqti4aFnYyPm8wdwQ==", "signatures": [{"sig": "MEYCIQDPiiPKpBzIumV56cakX8W0hzzsDvl/5s+C6vet19iztwIhAJ4mWS81N961XDE1OYZ6u2NBM3nl+k/kNU4MFS4YRsS6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 690735}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "3.3.1": {"name": "@eslint/eslintrc", "version": "3.3.1", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "devDependencies": {"c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^9.20.1", "eslint-config-eslint": "^11.0.0", "eslint-release": "^3.2.0", "fs-teardown": "^0.1.3", "mocha": "^9.0.3", "rollup": "^2.70.1", "shelljs": "^0.8.5", "sinon": "^11.1.2", "temp-dir": "^2.0.0", "typescript": "^5.7.3"}, "dist": {"integrity": "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==", "shasum": "e55f7f1dd400600dd066dbba349c4c0bac916964", "tarball": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "fileCount": 31, "unpackedSize": 690806, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2feslintrc@3.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCu1TP/w3ic5X9YSQnYuSfN2gcyHe7CKm4jgPfbQAbExgIgR9FPxJm8w3zT+XqHLvWTNMmvz5Pt72L5w4jPspVXZoc="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}}, "modified": "2025-03-21T20:07:55.125Z", "cachedAt": 1747660588857}