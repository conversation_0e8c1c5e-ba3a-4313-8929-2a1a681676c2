{"name": "@radix-ui/react-toggle-group", "dist-tags": {"next": "1.1.10-rc.1746560904918", "latest": "1.1.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-toggle-group", "version": "0.0.1", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-toggle": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-roving-focus": "0.0.6", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ebca95563c9e68ba71b3a2e71cc4e754f8b20e3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-pIpLxm0Jd0DePYBBcLi11QzN0hEn07VFbM4LkpKVgah8CY4kUOVFLdNr8UrQXiE4r7SsePCs/JsBqq84PzZRDQ==", "signatures": [{"sig": "MEUCIGNKgSBWh6ldyuJskkYBDNfxKCcPty1ljW8zczISG/p/AiEAjOeThFnBp6Paoj6jK85ROjS9tRjcple9gs0sLXoM+sY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9vCRA9TVsSAnZWagAAQRwP/2lxJjuf2SsXUaMYE2SX\ncBxkHfmHrd9m5OdfJfNKgvlbVEhxcB1jN3LJbVo2s/ygY0WKA1BYiZA512au\nBxBT9DIGbhpOJwyENk3cDsre319oIV3MoTAp0eS5ehUNBbLF09ip6J/G13wl\nYmlbFjylK7My6cXZo5zqm4LMlc0cAzSaSRE+4SEwUjvogCz1556dIffBahfh\nF1nTZ0IX/YEYjxtMCmKn1TchiznHGaVcnVfBKIiOJ/DH3dnr5Yc0V3SAfomS\nLRm0ZwReGwIy2MbvwowE15qb4FT9o6iIAOw8zpViwdKdsYib5mb8ErmUiCEk\npOQUpJ6m1K5bADmWRsOnFZncfJIdbM2BSjVdtiNYRf57QELaWH1eZNp3Ee6Y\n84wHLJr1b2Dlk27qIyhFrnS0EUVWlJmpE/F5JVXrBGnsGoCdCULX+ezGXT3x\nRZTXaAZuGQ+/i6lJ26vsu6L+IzhbHzjoaVxR4Xlu+E+Z0Vkq9V8Y/CicZv4p\nBvJz2lx5EBSVfD4pV4o7CKjyequSraPJixFiFkMpkVTAn1/uK2D32G93jACS\nrZHFkY7seoyY7xkIf9nDIPpq16hpTEZg+6bzQCDnwY5agdy15XpabDd+8GWL\nzcPcbxIZphmdihrzkxhRzRISEF0N1hg7WoMnZkeOolwL54ke5onxyZ2LhpwY\ntqoC\r\n=4tvE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-toggle-group", "version": "0.0.2", "dependencies": {"@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-toggle": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-roving-focus": "0.0.7", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f1db65f950cda6c50077fe811b94e791255093c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-pjkty5QyXahmn0X+Vr2ZWN0WL0ENy3JBwm7D+6HyN6hBUqbGg0/5pO7mKXY5EflKcE4NpQkXFTY29J+B9wVH+Q==", "signatures": [{"sig": "MEYCIQCtKTS02AdQCdP3Zbnyz+/C+bR26e9MM6dEjg7BfEceNgIhALgIxMDXerJrWKiPutsOm2RjUke1/uWvGxb9ae6VffxF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPHCRA9TVsSAnZWagAAvucP/jxNYEXPixUMgNh10uhw\n6X5M2wzXIxo67JEmgrJRwNziMxyeZ26qQbuOxO49eI7nIRrt+T0ZHR2DBc5b\nmHTpo48NX2HT9isePbmWccvPXQBzi6amIpU5//tphrVT85HyZEwfdv9nXXyY\njYWuUyiWONn3ECv3+QXo3rccfiQGxfoTv4mgrnyyAGREooNSIcx0k7LW5twd\nABHJlRC0tbhPh96MkeHuAW45Yglyy8TT1x/LgLAWXXy8Px1TREAWxfvhSljq\n5Ut3pWjebEM8dxWLd2k9DgHVkeo79RgL6/LCaNKVUWF4RCE/HmGQcYyzCiTn\nMvtfjF2M6KmUmEct+VRWew5ded9O1aKFcrv0Z+Hj/huY94PGN0jL2eRZhYip\nL25YvY4UOfDm4IFm0mKg556MYYBiH2RMRIk8q+xW2noHUVqXAB9SgQWu6X8W\nsewDwmw8/HFNxyAX9w+PfjN9pukivIRx8TuQJZOcUE7BsLKWhBMTpN3TpEu/\nhnOCYy1K6lko+qKgED0hy4zNkAeXQKNqWfCh4TOb8WPxYt1/s1tJ7Ogs5wDJ\nQPYnWQGPPXZxIja987w/SKjuc9SUGAnb+5AaV8L7/kxYEh0PlEzC2n3pe7IC\n4GNN4j9clv5pRi8rdN+7mxtC2fsgj6KsRLSW//ay+qkUos7EPIzgnLFIOeW4\n3LX6\r\n=Mdk9\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-toggle-group", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-toggle": "0.0.3", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-roving-focus": "0.0.8", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d49cc25e49aaae092ff7a866f10cf655b640c940", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-Dmytk9Zhobxp8D7vOWHbtnpge4OoHPJCmwL+ANA3aISGF/McuBOrCM/wS+551dlI9heMQ3W+UUTeHu7rNtKfGA==", "signatures": [{"sig": "MEUCICoG3kV65FVaCrJSIgk9xxN+/5UTzuEYALgTmHO3O66jAiEAkxV+xErLhmo4OOwZXtTLzFdHw74sG+9z/sVIz4F9vgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g6CRA9TVsSAnZWagAACuUP/jBhJ60aA5mMl6v91jiY\n75xM8Lg/lDX1ZqLyuzIwGrjkZIy2gP+7nl38M/dK+Ty2FRXR9XDAd3EPrQkl\nzP4Dn8l8117hpsCpAutdUbWqhedwY+ikxDJvwMRh4ZBSS8mGyeQJZN9aIEx3\nXPUFffyPnK8NtG5XUMBGT5WwWQWoqLHvqOcoAt166JnegGoQrGBZcGDKBaZx\ncXXPsuFG33MCoJ/Usjoy/y0x2j7WpJdqNOVLC/RKuUqB/j8v//y+6sT3SVxE\n3Rv5dNilFlqX9cQqSN+3WyF8ueqjC1glgTKpTDG1S+tl+JypjxjWkREVxYxN\ngJj2GRmIhCJWtAZscXD4tW7ertAiBRQ1HOG+G24U4N6LZtKOjQQ1RcYK2QF4\nr7g0++tcjJdnHWe/8sI4a3ISi1yJiGfJ80BnEfXTz9AwN7Fi13ugaYfgklyc\nzomxjqHblQUx85KvznpLz9snf3xdLKhF884egAj4tcVGnrU3p3E6T6B5rE8F\nZ/yijzdfMJJSrdvY6Hs29dJk9m6z8U9U67l0KxPlHYfXaZ9o1S1lblao0XF7\nSd3B3bIpFFj4fE0/f5nd4fD/s0Axl5jI3Xv1ADFxcXw6p5tDoeYV9kfajqsV\nknfbVDuChWjpAL4PAsBlf3Ab9pOJOibu0LPCnRGGIYJsEsfrWNn3YTKmHa/q\nDYrg\r\n=ZJKB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-toggle-group", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-toggle": "0.0.4", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-roving-focus": "0.0.9", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1057f9e6f413e2e68a779e0f105f6548f7bdd780", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-SFCtTlRAHFvVs7Ydrx5zmll3pNI7EuYUpKYhrqE6l4EflMRy2pFvw2SG1PqvCtofYqSL1B6QRKxWZhoW4/SGzw==", "signatures": [{"sig": "MEUCIQDHDFH8ZF0i8AzxqMh5g+gGanIusg0mqxt6AYiCQDp2zQIgd2/efNmpLR8ottj1+s3qBt6Fyk/vcQ3t83qOyF1CKBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IJCRA9TVsSAnZWagAAKpoP/2JPz+XBXNjE8Ibm6zKH\nF4c4Ms9Niwu9MZsQiG+63BQodIYCWtGEmo0oEcGQATx+dYJL87nsGS8zVsBj\nP3DJQ5avgu9lbd8wdRZ7T1kFFLZZECsNjDlKulPnjrA5VL+UUFEMU2Fxi3zc\nVDGfM0m2sddbeoVd5vDaVhP6/RsQq459/Lg0zyDfuy60d6J18BzqjiGzl40I\nIaZBDpzMcCrcQjpSs5JE/M25Hj2pFXTSE2qlrj2pN1PtJG0Rk5lWYaYRmu2x\nDPHsTt9T5+orZp/wVpDkmb6AkJaMdHroc7QvQlD1UNmta6o5e+L8l2lNif/p\nCe585UULqYC/SeTBfl5LvMwuNei21HQ7ysfBu2tgrC9euwZ9yn+tLKjhDTIX\nzUGLic/nLJSHB7tezL0zn6xlO+Lv9LJZNz+e0jmxPxklkdpnQDZ6zUS4lZ4K\n7Qw/tkDaEmqA2EyWKpHd/r56JSaKwyh+T/MvOk+CWgEScUSL7gQEAvCgTfPy\n6cH+aNkCfmWXwI9+VDYiu7jr22Xka5gcpIbzT0OZ6jc883CsWhBVfoCXR7GP\nUEx8R7GAS1mqPhGpKhIE2ZBJWu30xO/raaIunH2U+xTbKLGu32WN7pm1UQ7r\nxPDf6uNnM6ugGBho5KfxxJQhbSLpOQMLxQ5Ch6ujAU+1BfDvtm6ZBU7Nn4r3\nPPNE\r\n=klWW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-toggle-group", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-toggle": "0.0.5", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-roving-focus": "0.0.10", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "53334b9ea24e6177903f822ca27fcd0def137bbc", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-ssn0M0x9EjD2haOUOUUTBRti7Aa7xyx7HJvQjkjCDzQpZXEG1ipB1O1VxrF40lmvHr1Iv9Iaet23vM1zdeLpTA==", "signatures": [{"sig": "MEUCIEMgWtWk7vGPilZyjY857sagNk9FaNnRWo0tORUe+lhNAiEAl6bXdMmDfl9CqiT/CanVJ0y9l9RNQezSdUE3LkTAWCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wACRA9TVsSAnZWagAA0X8P/1ASw4fB+uO9tXqrblgz\nXXbNiV8aX3RCKgg+eFiHwHTtW+UVsbBQhJWS44yJ4clXuulySLAK3a6KMEIW\ncQ5PrwuyQAdPoTRg48bPYb+/8gLO5uVCHKR4BMNMuXDfJSj79klXMFu+8xib\nsSuUmPVMyFBL2FFhbLpNx6fPbbrOXc2uv+wmcFXnI3rG3ZmRvlUga7pIowL/\nw4UM+F1h8GtwKisr7sd5bJsdidCG3CW/CbfIJQhfEIxJNo6BkGQ0vhy2uf38\nMLeHLy4SQAr9cjyZxnjsi2/tOx1Uv9sdZf6Fad+jjMKv57ndeZMgQBdQk/uy\nRf198su+Kwb7IJ4KDYLpPxa+Dx8n4cCfYCn538Cq8KU2I5tEg4Jn7L9XX3qW\nipB7Tbk4SSpq8frXL7QVpyLAVN6ZNmhd+tIaxd4xRjztxoqdwDv9TPGJ5bmb\nnY5FYsFxjIVHmt0VHfgt6MrbhzP24OwrRFFdMlWOuqKWktbfFVAASJocWJ0B\n8YscJTapRqfx8Gam67OtfCwMI847uUSqWqOjgG8Fn57g1IU2MlkaRXOaz/JF\noE9UBG/gk785+kzfljXkX19ecmU8kCYe/Im6okdmVcZcrg01x6EZFQESE4gc\nxbm48TEZduYY6shO7+oKLJYe7qSMlWhyVqnNjBCVe9HbSrHVmQKeZLZzYOWZ\nRwk7\r\n=aGwf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-toggle-group", "version": "0.0.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-toggle": "0.0.6", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-roving-focus": "0.0.11", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8fb8df3e552c3b34abe0a907238cb22a812209ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-sP3oS3uTzKvtXQ5eFRaEekCj2iG+wL7gquCYBxcINyxW1vnDPFK5yMlVomHHhoU2woFzTS4nhRZfZo5YhBSngg==", "signatures": [{"sig": "MEQCIFBd5YzHhe6yHmn080mDya9ZxnfKPX3RCOr2qUhG83j2AiAiYg+9cc9giX6p56VrxWXkxYDPiNqEgfdk64nRQkQohw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm0CRA9TVsSAnZWagAAWlEP/RcxhiEh4Z4+4mfPCfdX\n4dl3njqF2ek2o1FuRRJsH3BE4StSdC9Avor33+qhDwAtc8Z3JhEa17pz4+6u\n3FYs3VI5y5nYRgQLsHmlgpLeiiYUG6gJTsxLJR3iJNpq4A8CjxAb524u3C53\no1OrRStjxmAy6QSkKWgYt9+WoRoFVqXEvhrITtthzJC/1YtRoULdr3N7MIdZ\nAWpgSEV8qQN5B7TG1HNrML+gFMJCSqF5njmQCyM//xk4ISyU7hfbp0QuarZa\nsbW95Yxat2oQOXC2i+T0IQrNjIoN/83nyXJbgFpZtccVKjfFgrUnEC0ss1y4\nz21/TBgVqVksMo5YVLfOmw1CWZOFpcjxd+ZR23FLwHJjwm1Pgajf/EwZLJFV\nZie6gjwXxPJuQtVJL4evhMJ7gT4qv9J4LcLH2OQZaugpjcK+XO3lFT5acw0T\nPBiaHjKe5M1SRiVoFytN9RRd/xaGbZ7ydNaVvQHSFY1VbYRalW6D0jVYALfa\n2vu2HP6gAJNu5bGz7tcUrSfq3Tklys6Xjw8GfUs442THE7B9iajOeW6SRUct\nGBczllVY3vayubLEzGGmFtTJbVNrzlpWDxGwbMO1RRzetgWbWc0iLnfgDCC1\njTECPfHNWttFAx8VVsoJFq/WRvYgZMZ2h/nk+run4aG5oqRpSSgsqamLBx49\nO1yi\r\n=pSKm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-toggle-group", "version": "0.0.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.10", "@radix-ui/react-toggle": "0.0.7", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-roving-focus": "0.0.12", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c219e6435345ca761b304ec6fe597bdc9289aa8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-X/hpJ6630+1xEKMQlt1wxfXuCq28/wh53ncwhuNya/YYW8KRxKnu8FwnIpPwuDEPRlPEqraBCytfS692AHTBxg==", "signatures": [{"sig": "MEUCIQDQAbcX61mL6Gce0VusPgtoqWZsoR9FrCT0NWYP7QqJ/gIgOGmNppbvaHgim+DNA/QxQ91e7ITsPIKtvLpVaRod2gE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7fCRA9TVsSAnZWagAA+5UP/1l7a3AjEwGy32ZRLFlr\nztQ2aJUVimN6HBJX5d/vsBDNiecUx18KxEED0EWVksMgDTQ79VEXIBsfgjtV\nMDu8A4hpQTXHtQ8BT/kmEdBy1hJMHE2gw8X3u+XlP2lwSZxdoMMLsgnYSRR0\nfTiXzQEPchJb9jVISbOG3ahKFsctQ43UcIDYrWM2HjyQJGwyM2FVXSZ5SVdT\niVTXsj6mxB9az8HkqOYoowdFFZgUlS8FMF/mmEhE2fH0cjHgU2stFwYIoTqL\nwLmkYZcdesi/apum5qYrrF3plLt54GUI9tHPXZgm1QuJJbfChaja3FG9y/0x\nP1G7t5rqt+uY79p5/zon03ni2mTdxdY8PN9mfsWhadJyNrqM6vU4xtovD08i\n/E4LPHzngiXgqb4q8g7jsiCgJP19tQ/2oHWB4QPcdgeIHkeHH6z+NnZR2jln\nykU0bWnwciTRPrB6RHpKgjj+tYLr9pKgNjwlHjtp/Dg3HKoms+3P/wnhybq8\nxy1B9GqxQue62tZiUdhvCPnjg15L0bgPh/odUSK11XulMWCkDHFpVEoRZuvE\nGb84nHxYRvwmUbUSMJbOPAfGw0L2sTcBdDLq0E17F/4dOOWqrUjhywWS6ziJ\ntZ/KSoxHQxOcianx8eg2IplBxrP9ftvqXw8em9wqF5cl7YWoC45GLABS3k1U\nKHHk\r\n=pu7p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-toggle-group", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.10", "@radix-ui/react-toggle": "0.0.8", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-roving-focus": "0.0.13", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "86953fce395286051d45c9365785ee89217b08b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-hRg7dkJTWnirUXPDbnz/7y2aOM7E6lStEPHd1EkAdeWqDT8O7UMjgEKXfkjgDEvifLx1ybG0hyPcQzWwZrYYSg==", "signatures": [{"sig": "MEUCIQCsZwsdbFSXae5rqalPKaNjqGe+wQfbYCJXtg3E9V56zgIgeUsH9KtxsoU4lrF6RZuG5MJZwUNHbtLDH87abOx8Zs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYKCRA9TVsSAnZWagAADzMP/ja/TCUyY6UqaBjxAo4E\nocMH3TfSS37BQCiG6fFdVR0SyJyi2ta9lWkX/z06jlpWSRl04Ng72z4PnBtS\nAwC1BHwXJHuUBp+8sYjEt8YQ9MTBNn4ZRf96A68YcZXe9YkuXjt1Kah51fd2\nyJoMzTjx5dPIS3Qtdm9kKB8jHp8mE/FUKP1IFkMQHz7H86+LKgYZlj8TmbN0\nlRBU1twuy4VTu9TwLU6cl0iseiQ90It6cTrLjJKDT/tTgb0NshTPwNxVFBsO\nK2k5r/BdGHSq9l9byKjP+sftEJcjxd2ipA7FNaDfFTtsgyoXZnbmp2CzNZl3\nqCvTUmKVei/GNflM67b15gfiIcLY9APKXRnlbfc/3ZEPcVHwJa1Q1akEGXOb\nijYXcI8NyK5g5eFbDWAu6Kpp7emgLvnW4d/ARtLz/N7MYIkAeyfP+F5aNO5z\n9GFZx3xIJJSlS64lz/ORWEV5FwBaqdNjOhlR8hzg5Qox5tryYa6jMnUIhCPO\n2nq470bcLgj0kH1Y6t1iy3KDynLa3/P2Ru0Lu/K3A7oglNLDdSrfLmpB5DDU\nN+T1bFoq3qcA+EIIfFiu5QbvvN893HFJ4Td3WeM82+ttTGvUqisyH2kG3ImO\ndXuM00Oy+FctDJXY6NaBCyyeeeonfEPEzmIhCqJOrtdzLyN6Ximzeh9FclFY\njGDn\r\n=uQai\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-toggle-group", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.11", "@radix-ui/react-toggle": "0.0.9", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-roving-focus": "0.0.14", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b0e9f4d4cd2c2289654d5d0aa69b4c0d13491a41", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-y4wg49n8NfNnpReOjQsYKT/N+Won6Di8BkisY19AeKbR4h218/kYnadyEHru7VaX8zwbC4tCxgbF7kvrQcWLOQ==", "signatures": [{"sig": "MEUCIA+ikMISspc44bTaNrbfsPmsWE8SZ6/LuqgCBfGD2zJ6AiEAzL+qHECdvNyIJnFJv83bg/643sXowg9cZmz+vxisg3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ96CRA9TVsSAnZWagAAjHYP/1Q9kBQ5iKqxxUIoZf3T\ne1uu1T8VGCCGtt8YrvrAK9MRre86g0+uiUhLLaL16n1JUhtcZ+VnkCKB1CZ1\nSYAwKZP4RKMCj3mmbzswTkWnawbl1GZb8Vp6dc1RLrSyZ9FxDEJ9WQIFyFCs\n4rVkgfQdHideZl4Vi7iyMlh4GlmnD4/AKyhj1LQt0B4jKiIbbAyjzln+11kU\nWihYRT0z9JhUBFbF9vQCrYZHriJHfXC2mfLof8k3ldqjD6uiwhcFXlBzvGym\nfm6x9XsdNvXJmoqRZPE6KtpdphYMgZPkxTEOr46ZezBBPbq2jYTceo0HuVhK\nhCDydeuIvyBlbR/H1KUKDp5yHbndWGDo2OjHpz7EjTrc4weOAsgNpY4ovIFV\nJW9+oc6oxuKoRWNRXbIxY/pCPtLZJfGyW1lFwFhpeQML/T/2rhjII9w4dl6u\nHPH4e0vzfz0pGNHz3/GM7iO8PBmw0EeFh8O68pHkzWMgJNSKw24SSs75sYhI\nGsWUJYv94FAe235awcb6TAsmrtSX59CPIrGlgYDVS2jbp76tRD1o6+Ms7QZ9\nncM2NmCUgmgU4308/V5429k7aXhpgyO5L0dAxoxFg7JXNjm4I3bGW1X9op7q\nUqmeOdMKmr/SYMHDSR/+U7s2HSzKxSitC5HorNt0FPeExZHu/IMBSWCyxAj7\n/CcM\r\n=/bMa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-toggle-group", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-toggle": "0.0.9", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-roving-focus": "0.0.15", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f128ae6ff5f66e5db2da8f5c38e5fc076ac3e94a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-er3dvXiqCwn10kIoOxpXton/4a4JE/WDRuQxgdgBIqXGgZTkxvOGub87VZ2i3fJc4ZUpOAmbPRP9EhQmEHGGQw==", "signatures": [{"sig": "MEQCIFOXNyyVdYKHNvFCO2TFG+hYZ9uIREeaV4IKIDJPATWiAiBBlTc7u/WBM47RJoXO4G3L7mEl2mMy3ymsy3m0rFLhdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GU+CRA9TVsSAnZWagAAXaAQAIv12BDSyaHMBT/qNGBO\nP0Hbqyeh+qS9GtYpURR9h+HcC7Nr1Ilo39olfWWbufnQH1xsbQx9sl/4NT68\na+cIyoB38YOunrGbrPHZUgpq+fEnngrbKQqI/8o3W/ih62ocunDLEeEg6tgB\nDdtgT7h6RJT/1Yrm//oM62surw2hDGHlsNfTQaHP+vUP9R731zLekFIzUK7I\nduI7vOjVVE0lO0B/+W+7m/8hzGhhNgajh3efhBCAWeYLJ7a0p5tm9/6/A9WP\n2hGA94twvUBA7KKt5T6S5DxtRUbPn1/gGJynOWytGRDiaaknhfB/LvVuweBi\napZMvF9hJjkPy58AAKBtAW93Hmr5fIVs4YAzIIz4AUyIfSBPtoAd94kcr4GU\nxDT6mCSTOMnkbd8lVcNGFjSN54K042enP2bbLvZ88yM1E//pleIG7sP0Iljv\nrUrsS6Z42PA01/VoG4irSx++DEddALIA4ZSxQKkXPzP3P2KK1NlpwCBs36iG\nxNRR8ovPG366KHG1P79cr+f/wMmpk3pit4T51+QdlpUzq3sFM+XGy422ZSSO\n/4YKTzFUcTGzc2j9/tK+6QA/6v4rZ7VK4d3vHrO9G1emuUNEvIpsb/CU3mLN\nRpWI/t1xLGy0gZy8XkSbchOPy4n0cxJrBGzWrzi/8XJ3H4GZ+MKkwdAs6o1D\ngG3X\r\n=OxZd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-toggle-group", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-toggle": "0.0.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-roving-focus": "0.0.16", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2dd80abf92580c0b84a9adeabccc60670ea5be20", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-OWHsTDIrKmGNWmRzlyOL9m/BwA2VTz4n4FkUCjZI5kY80fMlh1QgTLjuS+XU/8GKmwO0D3v+AtURSZR5Cf7H2g==", "signatures": [{"sig": "MEYCIQC03Ck/AmhWdYszq+ihfsBl9ikVaziH3v3HSMOlE2fppgIhAK6+ExIePCKQGrJwzPNDTjRg22349AenqzYZUbhqNMc2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnT0CRA9TVsSAnZWagAAEUMP/AtqcozCoVY1160yfTsn\nVuVIS+UFs/qXjsokA6GvEXpOj2/4FdZap68DHy2SnIU4nIzL64nCKDxkyXBv\nU3lm2WEOaCadERrboTe+7fqXexB9fe5IMZZ4X6abJMmeLoD3rCEATBoxtTD8\nds+Bg+DiBaSFoD25Ex1zvDD9xDRYVaQKzA1L+tcESeA9tWBTnOTVFUtR1wDw\nKFfKZcQW7qI2fA2W8dK+0njzRXRSggiVzbJTT0LGBCQxd16KHLnGpst/w8BD\nAqVJb1Z/pJi7wxJeYTp8c5LfW4uaJaBAYBkB6WHDhI7tkdzNhUahcyiFMIhD\nXIBDQeATqarPQZOju8xXvxN/vfsQVtPniT/9KsszdE0ugT8NnIChuqsGkYJC\nkI1XjMQqaSd/kQ5XtwPAMWpl92ZrQkQ92ex6uvZMGGsDQeZxNnzhSWRa7IQF\n4FArMV4x1Nc+JX3uHRiT5OjHJalq7zggqvzr7YWLta6Dx/980RVtJCyIpxYa\npa8afbtnpIMX8sgWbrN085mvJvZnZqGnUpxmWrvLCU8481w6AGHs/gvTJ1mW\nGn7Y4Hr8laZoEpAavT3mShN2hT11zDldRNFgTTzZTIASC8erBZpHY0eMDDeg\n0cA/MTWrWQWAuV348oLhIODwXk5TH7DWMYOwVWRNuzLx0LiOFa1Nkxoou8LD\nd6kU\r\n=hhdP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-toggle": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-roving-focus": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0b11a20d9febdb27952133cb4ecd40f6042a9c95", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-4LhjXY3FtQUmvLLTMHhOAbSXlghS7y9gSjJRPVmqXpUZPO5OrxWT6dtsvpxxt+gjpzshcUvM0mfsSE+y/Y0sag==", "signatures": [{"sig": "MEYCIQCczRkjq6SsGQJ+sCvmjxmCfVXAlbhi3xWcdiZgZmaH8QIhANJmD+OAMRlS8OkoJnaQOUV/dDHxXN0Kr2j3yDdaQs1K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgptCRA9TVsSAnZWagAAosoP/1GkHhC14Oj4+aYo0gap\nzirrjgMxwyA+ZuuwqH8Qp9w6isZ85NTgKsNMpBhhzh+u/PsWgHkV+JveAS9z\nD4KkwAfb6G59v3IJ3I50cZIZLwmm57vC6dTeNKzOi3FYv1LNYjdzWFhSDjoL\ntJiSjKlOuDgLRhmQfZXtvq9fd0Dt3Y8NiM/MFIViZq1InXHvCzHRX76jCg04\nBfGVNNX+LmE9PsfoSMN7zrC1kNW9h8Cxmwc2yAd9oOZeuNPKmQsnwg8WIBLx\ng/xEZSifk9XcsOk+D/vrVlI7roWErRLB6inSoSjWK8pdoWXy+7TxEoDlytp5\nHuuT5BT0f2mRNlBfYcaNEqSKh9y1H81hWL1MbDSHQQ1Zcu2z/l0uLe1W+oXn\nvG6cR+cU/U2gxveox7U6ZrnpWktnCkOwE3XdTNCm7W4B2hJ3sIDjxi6+Kcwx\nMe4AwTSStrSSVSSxvtnM9q1yMSLRcDmo+1G4f5NUilYjvzCCWaM/X95BDWwC\nU3HYJBXI0T3exnZnzQB3yamDXDjPFLVa/5++R9qcKkJxuvTRzPB4qmGaoiNf\nNvUjbnUHmkv7Sf1l88O11gtyk7quZczW3ah7t0qNULI/xZESynxZYhJjt/zq\nfW1H8+11x2dB2L+8VOAjeaD/72DOhV/QjJ+iCik7RaDhcq4x1Ok4iUwihGOZ\n4v82\r\n=F8hf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-toggle": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-roving-focus": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dd52bb409c53f8d925e1f05aec97003e98b6c74b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jh/uh4C8E0PJomEpAx3DBM0CJEMCES8KxCG6P2TU9KW3CN4351qHOOQ6yEJusRCWQhhCs483NKhq1iTXpACvZA==", "signatures": [{"sig": "MEYCIQCD3d0qO029JfYHKLtVDONUAw5s+/y6V7dHxXi3YoQLTAIhAMVgbcRQSlFOU4T8o5tQ5dnQgVPse171bGzEgIPYQshz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyiCRA9TVsSAnZWagAAE0IP/iRfssN5Up9O2TmOsc/f\nK3qcpS7lOn04Nu6LCE8Xc0AT63zLIFBZHhN7B7y26ewCEgtfFFGF2mCsaMQB\nm1ao7xWzPS/khYcvqmolTk3iBD9DqMfB2U8aa8Maihcc8RKYvosWT25J94vJ\nrjf3nLyAda2x4P7by9ZaReLOOPPu3QtwqEkq+3dRR66FZuEqo1ZlmqGWdJ/x\nRIOWTucNg/Vjfgq9433pHvqJ0djwyPnUJ8utb7Bxa2vg04nlrEqnwfXZqexb\nBwSKVTdDEPy6bp26ppMAB6wF95VmGfQ7vnA67/gKvoGcKGTLv0Pm2/rtOgVb\nCUcoGpxWQ3JZ7NkKL7/MTakMIEJhKmrw1BqF+scd/yH2phTuqXlhqwsauEfT\njymlPL9GYhuJyzkVySjpVz2msvi3ogkVuCY/iEC+SzwvVQetLHOP+JJqN6ru\nRFAs6TE+xkHTY/pr7w5vF9wpUR3qNM4MHXtY62R0Vn9NsQmJkOXGExVwuJqD\nuB+/EaXsi0QZ/Eiwm8fuAR65bN0+3pEKFQds/U7OADEdM8BcnwMhBUcA/Wfk\nj2SXTaX5vfIgTcY1tZhFzueaX9R5pA5L+eQhxhEnSN0BGj22ILln7ZBlGr39\nFcFoB0GsrttcYLN6OhBdPTdDEcWmWn881ZY7KRsznMnGAgv4wKMA4aVQcUNx\n9GSm\r\n=UGHr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-toggle-group", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-roving-focus": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2b4c4fb7475399afb5b8e27b1fc01fa74ff18223", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-FG82AtsFlZXvoRGaKKkVLadiwvgU3GdrvoSq/pSjFHb6L+PmZAKX9tJ2gQeZ/LYOXFJ5Qd7Fk//yq/dJ41oxHA==", "signatures": [{"sig": "MEUCIHkXnmHiN43X7iR5XEivobNahMDK8mI3r0OrlotJKgW+AiEAic+yrfJ07RvemLKHqbkJzpJsUJNzE0dXgS2tjHa+qPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmsCRA9TVsSAnZWagAAAMYP/j1m4/PhTETyLi7XYLFD\nXBlTANR3XJ8EqLaB4uEW0O/FylSUeXIVQhu2vaK//pZwoVUI2noCVTNA2fWB\nFYrXUvpTHfSPIQGx+A3X/pl4ttvvhfbGxalDN8KI1Jd82AjEMONaDOWWYQyQ\n1UvgTe9V29IeGL6jWXZnPpHTmzFi9ZWuYkB4yDNCL0WZJcwBh7V3SbpiERnb\nE7aSPabkfgXx8KrBm8xrtWcnrnGthC88Dg//Mz7gbq2SBrLuJ//H8LRB2FJx\n6V7kuZYUQggPvs9iSclCO2RSGWG1HT3b7PNhYRIT8z9oO84h7oiU88mpuDEH\nRsvj51UJGHzU7vhs5EUUTqBS0dUQ9ox2COPTiK0cAanxRYhqKAoWreRr9u6t\n0uVYcTctKFeYu0xF1HVTOFk1I/fTWqpLrouo1J9EcvwbqBwSBRs+4bjs74sU\n1YDbv2u0AGMbQVToIRksfCKCpvlTd1MWGIG/J5eNa8UhIo5dQDbpJzF+EXFd\niE9RJAuHQIijLrMR1yhEQM3JQyclxudqW45d2twHFW7YaFGsOHDnvQD/XWUj\nD3R/D/wyFu/D2NWfc69PTh56Qc7Sm7B8KczuFC+qrSzdWqdy1NdaAhiFk4EK\nhFwT9n9qYGnx3kr3Ks3vbI7sTrDVXRq3WhUSnpZfxojiDRderdHp2YjyFKpg\nGcV2\r\n=4YPn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-roving-focus": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e40a5bb61fc95f0ed83b2f3194c2890020b0f08d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-+ozQMzZuIiuP9gl2BkdIKCh7qdnjFe6Gua7tZgbzUBwM4KwrQdOX5PjrdUJ7GonbhSCrUUkJnx9v2m6y8ungLQ==", "signatures": [{"sig": "MEYCIQDD6mqlPqjAMqypmL+2wxRzk9e6laAzO+r5ZToIdvxjfQIhALjTRLlFlQbJvAId9BJa7jk74dkv1NmqocQykW8Z5TJQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIoCCRA9TVsSAnZWagAAZhYP/2ZkGc6RHyHQ54qj6s22\nVjzaWymc1UJuvW0BQOFwwIWZKnYM4GFdhlC3tB7nfetUL7Y7GFxX4yWYA/9q\n//jWpv77qaEn+qSEJLJXXX2MINkMAS+zItWYtO5ul0dKbwQu70hlobmpypfh\nM9m2IkYWGrE8T/HPU7UjbVL5qdBkuGGHV28pRMAOJ3idt8mriCpxDKVH5kMK\nbtt//rFJQplZYo4ahcNEbGIv4E87GNaBnhC9N+775Kmudi5ir5065NsgMbWS\nEgbImiE4kX5e4Byl894/EbYGO2pZdpjIGSNgwfruPRXIWM9SFGwXO0r7BMRj\nK/MT0YaZiJ6+R9+TswmUqyCyOiUagWixXfq4pCGsB9JvCz+8mbOBt+YmYLFd\ndHDYyyJkCNneKo0Dy2/Vl34SKadQQVmWQNHjwZ86G9YXwYhRUmi2+ZXK6wtu\np2Lah/FCUT9DxwGlFAzhMoryoFHpZc7YMsTYe/RG/b4mmbPzFq1p4WM7SVgA\njB6M+0mQdtbbI0+DCLfIJQnNhKjXHK+vVkZOLnlJOiN529u+PmQOzycvJ+cp\n+ZzeB0L48zInOeITy+rg8w3/t0Z1eS98B5z7xNkbO0dnLUrr3ZLFjNZS05nO\nel37WYpUZQ2JhgoMW333W8JplcE3TNFC5i6md6w4HQE3JpruQDOcf7YFyh7G\nqSmQ\r\n=wvEn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-roving-focus": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "31929dddf007484adb8fa7c9031cbabe54db9691", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-o6ej8UZbaK8evyJm8RIjxYba6b1gStld+hGPvLV14HHX0oX8+7vyREBBEa3oVG+OYd9LSy1z/ye856WkUgmT1A==", "signatures": [{"sig": "MEUCIQCjrcUmixFGyZq+M6rNfL7ukWapYUUuaAAnR62czPDIfgIgPJSWAtJ/+yT/odQL36TpgMTDONFvDdmHXNr7YHwg2mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwvCRA9TVsSAnZWagAAjroQAKSFuBspwdof6W6BF8Ne\n/TNbJEuVqy5o9GdkgkcGTdWBG3czWF9WCmiC3lMGcWHOAGjWKWxCUoD7BgCe\nnIqmXEq8IBOMS78wCZDj1xfhzhvUCCeFcOu2H9UrFFMcgva59nVrH57Z31Dc\nRXrhW7kDpqZP0QuUdKKzSKu5bdeB3xdQ56zKvmH1p862iotmrEayT4ZwjxgM\nOlwa9KYMm5vxRQAil7lmb+BBfhjhUbWctXGTMTHQ8xlHGb87jqsqp+IIMWcl\n7UeD/UZNnKwMNiAcXcNF0XMk/cINAFezmw0FjprRltOgRFeD8KyuLr6Ay6/W\nKnWLws0sHrneZbxuQ03hMP8unwSVc84/gdX1Wk8WdHCAr2dnt+fknUe3U9+0\n6q64AHU1wRonM5dPxfGZNdyhwoQjJLAl2DgPdPW+cVvsCkWo8EQoZOQlsyX1\nIHrSXvbTq3pFNxglzDyyju1Jr5mPpSBbrQNMVx4R1IecYHWZaQ0tk7tH/0Zy\n9gKU6kAKJJebnXtWqaaU/qV79XwzcveBi84xEowrmPQYdOTFsjS8Uxg7FjuF\nz/IHnCH2d4Es9HGkze96yT+I0DVmOWAeIUuNhWRZVHyVCuZe22sb+ugIDk/U\nmMHATH5VOYi633uVCOt2ribcT1CXSCrgKhial2juchODSTeBh+KIsmVPlDPv\nyQX/\r\n=HNlk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-roving-focus": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70af7ea38f4c94d3371c4bae5a6da0346bb26ce7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UEHJBtmJ9oiM3HbDvH6Caeyem2ADCvU/zqlBKyKRllA2MQnvVtcFm6MMefYhjpY2jWV2pEzjPxcpY774+YtpWw==", "signatures": [{"sig": "MEUCIQDkwVZFMpdzCW7Wv/MMRtR50BRi4r6ui6iIBTcwp9SNGAIgTZDTOBB/hQl/MHMN0RQmzbACmSsEpoXxMdMjPQ10ryU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UvCRA9TVsSAnZWagAAT0wP/2cJtBP2w/qPoLLaOwUM\n3EHX5V09lk63Cu2ldtZ2sgprVCcObCHd3Am3IBKwrNhPtp0wg4IGLVbFXDQM\n07MUXqE0KRwaoMfwO/h2lXAhAxgP4te8Jpfg0c11iTNPMvUzCXPuxFk954A7\nqOfpbd7AfcipyOj0i3jRc/dkWxWTTKwPakxWy6KPn2fxZDP/HWD4n7r+sM8M\nzIcpySoH04F3lDmpIva6/vwKFzjK4jLCuAV8V/WuCGGfQo4PxWJjLcJQeSMk\nu/tYclx8m+w39FYLJyiHCl8C6LgUSxh4twMiUhS2yCeUh8mNFrTPIJ+9jr4S\nMpL8M3SbaAbpg2ey0pEDqeRnE5YiYbUUjnUjh79O9fPkaA1lwbBpP4NDeEAU\nsjyjdhm5UxdZvTLpDoEw2WvORWJmExaIjOjptEhDMjynOXS5vBbgxYL4R/lR\nIOBCc0DWhpsv00AVjQDT8cTA6zfyZXDX1qz17hyNX2HVRvhLEPNuRF/po3zH\naHJFCA93KYS7LM+e9lawciBWPFDPS5oEeEnVthOSbDBRkT6tOqJunz6HimZI\nGDf87cGoeZzTHkNsB/BbCnq6gATc4E8Kgo4ygwBUxLlNCldZjhD4ZiXE2ITx\n7YzvSDPjXZ+TLjOY68FhA56wk0j6WGRRE9oTIia8PGdd9Qt37vIdU6/58Vwz\n4tqp\r\n=m/Sn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-roving-focus": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "584cfe960ada6e26e141dc701aaf44691472ad8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-OVovajtcVbh69qo0PSnt8QQMVMHpXDOHa7KMQBmVeWwc2qCvFCfmY/5tP827RHAXCfw7uwnY007gO61dsT14kw==", "signatures": [{"sig": "MEUCIQCqhJJlx9pyHcBRODfUpx25MgUtve+Q17+Gm0+TQm2gvQIgSr70/9jwwgUV4yzv14IMOkQuswBDmhh1rv5IMXlPRJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10pCRA9TVsSAnZWagAA6/sP/2bC/mnZbfMTBjBGlavG\niLP/9NPl9lyZoyMsvZ+oZ2o7DPXoGWrqYpM3GoK5OzuDKpdLu8JbNQ2a4bWC\n6AyaVu/S5nyn4zuFkl1V9O+wOLRFPuN4Bif1ObrCY5bPsEiPfySEkiZiZ6D0\nV3zSUfsAbSlQp+EA1xpFCQpv9+vQ9q/08KZVaFbBayc2PTYC0/0GB2+zi9io\nHw8x6PAVx+xTXj4xWgLeCv8S7qRJojNGo1raLmSmpTXjlevpiWt8cVoSVf3v\nw0g5g2AgMEMUolpqF2brWdNp6xvriWRNIO+a9v5UVHXE4ZsT5Z+697cGNXHk\nZVbjaPhdftU6Fgz+0/zPzNbS2YV0GpNR8OsxtJIQZfRTwv7xCaDQVeZS5i+E\nIRyZpbyNQI3ZI5ZYUGlJAZWP/iEpjyQn5h+xjAwiLUqzQhBRzSolBwKXYxz9\ngi7dCSNFi8tl1W0Kk/v4PNdjyBYe3DkASxJXmFm8Sr6yJSYYLCnJLcju8T32\nNsL9YFANS3Ngd9sMWrCUWRr6Qy7DrwolZ98xOC9CzMZPjv9M4GOaCbjpEbld\n3apohjKoc5DxO4Eq5p9GRbQySzTvBXb4J5wvg4BdAME0FP0anOVk3mpc0+Yq\nF3qs3wsOSRTf+P1/Tqgv8md/htv8FYLmAg6yk8tlVCmv583o3JH2nwap8VdU\n6XCY\r\n=g1v3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-roving-focus": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7ab37a761df1ce848c084bd49bf6453e68dad52d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-unTXF/m40ew7ugrhQCDKrIfCJtZsT5trbDmWqKNozUq1b/JpI+4DHCyFZUbfhWwFR8zCpZnzXw3G3dB61cJMJA==", "signatures": [{"sig": "MEQCIBlsQdhJmnD/Qc3ACjgOo4gCwFXyZcsvefjKUGn63r9gAiB2qvKBFCqf9C7OmvePNNoJAvOoVi2WjKDAtUOmMAp1jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFk/CRA9TVsSAnZWagAAXysP/RneF9hXT4PlknfrNTQZ\nMw07EeDN0mBynEA482uqqLwdnI+662U18YD9Gygm1CbbHBxCprHOIUgRtQau\n0/PsABhpp3uN4KO8Lx+k4ogKOi4MCHFBfST46M2OsKlPr6iwmO6/Jw3aAQQc\nictJfWVOkyqOTwdcEXqQkpAZus3BRY8OXKCVxtTtWyyzALstfVUoPQwmSH+B\nyeOeop2HFhyrwDUjd3ZNzp5kUW3rekmjyieTxpDLdEMBEbYyRQLXo/bvkQAI\n8kTTHNKOqLOpB09PFSKiYJA85vdcpUmmNR1WFNcqSNc82YmwdluyS34vzzyB\npH4LE+Zy2PxxcrkLJZEfrQNMfcX+T/M77E+6zRBW7KOxuSnJCqa2Z9pkKWKz\nYIUYmIKZ9kDOSxSTvpOTkkUS9gpK+CFo86CcV3zuUE+mbdYV6bvGXYW984T2\nKCMY5a3L0ng81ThrtQlHwAWcdCmDO6pYGsVRRlyFs7WBvxLSueWnemCRYe9O\n3M8kLnBeSVYSOwjUW8TGAxgePfXRql+dbPlph8Ed3l/kYqjjWGG6s1L8hshz\ntWykIoiN0MXPyfpMv85ycQVTW2mC+Ps3MYO7WQv59lPNGheemdm/Q0STnbQA\nn2agNKhsVZI/hP87ERXZ+ENonLfmwpNjokIHepQRGzctjrh15vYI0iW2ed6K\nEH87\r\n=B21B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-roving-focus": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8b50bebd0003cf6f0b505bb792c4f8fec06518ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-E5V/xmo+FVAKPkGkXmt7P+HhtbY9YSZ6R2su9Cz2zoQMT2wMGAJ5r9znp76ObU/sVV2iIcXx2CBjZYvLlVUaGA==", "signatures": [{"sig": "MEUCIQDLjw8lvLH9A/oMPXYHV5f6ehT8cNEjXXqf9vvmBJJa1wIgQJJ4tspKJhe07DbQtHBDrgrIP68rJ/jOQmHTgoQsmRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713}}, "0.1.1-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-roving-focus": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "200aad8538f952f46ff9ebe465d5321a2882d539", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-IaEoL7iLHvPCgbJnT1qJpB8hzBOXs6xYzj6y40+JIvRyUpDRaqs8AijDeMe/Q0lKTI0Zc8q6vyaO3rg8bvRukQ==", "signatures": [{"sig": "MEYCIQD4wkwa+0aFJRETLfvery0Cxh3ZDrdEX9ehcOLoazpg9QIhANbXyw3XN4T+9oh0D2IHck6zHPYFINzoFQFL7PqbfYSO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713}}, "0.1.1-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.8", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-roving-focus": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5e8c8dc00dc9becac95e1e1e079927b4a9b1243", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-jlmCyD2IuRnc4gCFc+qcbfT3gu09IpjUGyvRxKvk+eau1nj0Cf0iEEZvAx3fWYtjxOK2AtfZMoyVup6+xMMLfA==", "signatures": [{"sig": "MEUCIH7W26nATNe3jvhZ6CaimbQG+WbwY/A+lNEgWMTXSwlUAiEAkzKlE0XjD5JvODrscwczSVa++Y+guCHWsWEw7361g58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713}}, "0.1.1-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.9", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-roving-focus": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c3ea1c5c308ae23af94062fefc7a3a51bff1a955", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-LhVJAR4jeKxXfqq/QGOzuu8I+utyYVF/n8QVim3LXd3NsB8fjuq/C011Ic5FSQyet1Ko8Ii3CeBhEhmPe2uf8w==", "signatures": [{"sig": "MEUCIQCLPebOLdtgty4nlKssOV1YrbiPisdTpGb8N0k4VwzR3AIgaQNxjeIDoivyO1Tqcf7oRsiixMAw0oYm9GhY8KUvOyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38713}}, "0.1.1-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-roving-focus": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7e1767fa2743305e58543dac07d8abd28a3457e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-RwRizNE6VUZ6sgiax5IGVa0rIfTcOVdjfn2qF8yaca4+DIB9AAqiQ/6o0MwYPp5VLbOBg3lfXbfXh9TyK97oHw==", "signatures": [{"sig": "MEUCIQCdIKqC2EPx1hgV6H3nbHYKc7cxWUZwnCgsJZ4M6WYRRAIgCWzVOIUwPfPdQIOZfwVm88hnwX44eWH9XOQRskzV2l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.11", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-roving-focus": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "425aec9a1253e6b41993ca31f701ff6f46fec4a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-t1k38hBVZJF0PdGvhNmtAW8spxbnSF0Q5Lx7ni7a6cLgoYrH3uuD5nhohqLsRmJkeEd9e1VgrwufroYIDzYFpg==", "signatures": [{"sig": "MEUCIQD5cpJ9MMuYGAtzf1tCVzC7vfZRdlsCim/8POwoMl3hMgIgKgbkQjTnRjDSsrKpZ6p5ODKkTLpDjpH2/uvwcFLNBFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.12": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.12", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-roving-focus": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a596a8c54114d867900e387d7e57033dbb832c2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Tj21sB7D1tBfpz296Hjoc5uDutlCA7tjNhZg6hFguNA1PrCOetr53LXeoZ6ZKaLyP+H3V0ocw0/czK0mzQlLyQ==", "signatures": [{"sig": "MEUCIQDGpM7ih+17kWan2SdTHjAMnynxSF5sfUmAqoJRYIwj/AIge8i703h42Ku0pEWBIeQOUKqO5ErpNG7Ew09JioTZefo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.13": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-roving-focus": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "add373953410be2c0941eac3c76fe0b8d3ed7870", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Ga+TZOqLnya4O0nknMuFxoQoSqGNdfRrPmYaK6BKenD+176KirzzfNuQ27mxb6Zh8cmIoDcnQ3fO0N+moi5I4g==", "signatures": [{"sig": "MEQCIE6PSx7zAvW7KVHYzP0cgmtyheCboD5TOU2F6vUegCmQAiBqoR44WZIkMl0IYh1SGizZS/IPJq5xkPaztN4WXBm7vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.14": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-roving-focus": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2c1c64fbfc8c6be8840c73fd805561451035c0e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-lD41JRIh+HD3uNrnnF2XwgJrquK1MUjkaEVjkCH+h93jSCxyzIqflHDsdcUKBUQwOcDnpasrF6+bCvDhIIKOrg==", "signatures": [{"sig": "MEYCIQDKcYirql5w8nekw3sqz9PXte54KcvdxeanDVD3ZO32rwIhAOIy0agU45bj+HxRIwt990dUZZeHf7CNqBzzaFvNdJ4t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.15": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-roving-focus": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a531106e1be00a59e6cc125299a65438aa170f51", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-s0eXfZ4AP4iU2941xpNIlKcdLZ1yhNnCUp+cz/rlx7o7nlVf6TQuiW3iEe+ke5YRTyXmOSOl8lI2svP+tZXYjA==", "signatures": [{"sig": "MEQCICDLBq2H+IrWiY2nMUsoTCx9prxmJItcrBuCAkYh2polAiArDtgZ5tXbBwdIQEIyoFz4TnbssRsWZkejL3cdCsNeRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.16": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.16", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-roving-focus": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "02f1a7d22ec90087d8e44c7f2fe7feb36722501d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-ZnGY60MkcrwhpBK22ZKsc5yZz23ihZ8JaurR6d0tlKuzpRdss4UquCSMw/HiWcuJc5pH/70K5ZW7JhkeewkQTw==", "signatures": [{"sig": "MEQCIGIMyiPZsknX1KUsqU5oobgXJiGIwRVdlchwcqRuPZZIAiBkdP1zDHabTEoaqHUBheUDiKW/VGXKw29c0OoM38MKsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38717}}, "0.1.1-rc.17": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.17", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-roving-focus": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "635d09d8d76ee71962658cf161a5893a32e33985", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-nNTnAbWdj4hdX8wzXSfUAUISWk5SnCsk6d3dqJdQnSdQSc5AV4uqluGqV4mvrhhcSVlrpJ63+XTOdLtcD4Lb7w==", "signatures": [{"sig": "MEQCIFVA+RHx8qHRxSg7wftq8T9fTbb9s8THxzIu0hgwfY6cAiB/GArcME2ffmUmIq4pJjmUdjKzx4MFIt2zXfme4gbxhQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43261}}, "0.1.1-rc.18": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.18", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-roving-focus": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "77243a86360ea915f43b7d5753e24d415b5ad2be", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-jfapTeXv04awMec/yEVomLRc0nsVMmv5R1TCr46ndwnuLhVppWzY2nGVlK97fSsE4DfjQNAh0rpjrxW54kkcWw==", "signatures": [{"sig": "MEYCIQCOOZb4PK1kyvU7DkkJQZMmNUdt5PPh+b9R8aDn9HsNXAIhANPyYsXmW3BHrBtNG+AyRBjD/MRr3QsYz5xPCKZ+278y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43261}}, "0.1.1-rc.19": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1-rc.19", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-roving-focus": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b987e2be1faa8646fa876626a272935bec0209b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-BC8L+3W<PERSON>64PYSAF68Ki0W8gu3m0v/DiGfMLNP7kN1c2+GwDSAIJMD45f0YL0uVEvcPg41E/ixuoRy+QTdZ8AQ==", "signatures": [{"sig": "MEYCIQCZaZZYEPF8T+tZqTjKguhoIBq07EH1fXBqBS8dAXVpJAIhAPTulOduSRKMW/u3DG/qER/Q/ZzzA3mfPdsHHpWHxGMc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43261}}, "0.1.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "56f5c602f28ff749be659a045c7400643c79acf5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-SQN0mA6se5g5shr9SDHL7V2dOYf4PUKXzw1pq+3fcXn3NfIG5BtGzIXRDrVLKj180LWzlw9VCoT7fELRIiknZw==", "signatures": [{"sig": "MEYCIQDhdINBERIg11iQB1TdV+0W/OsCNus/RXT6xLc9ftgj3QIhAIlFgBqh5iRZbniA4uAhVHmUa+euCIGA1xLZb1BwXo3f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43204}}, "0.1.2-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81d149668c4a1e9e31e2d5ea1c550096cc7585ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-eFIbXqcV0e2V8vmHfWfQrpyq678YxAmq39neHTMtLbbpjkfZe+gOj2gm+edMik26DaCVItXYWN/KgR4nGvnmNg==", "signatures": [{"sig": "MEUCIHEK7Djb8VsEDfBsv2iwtQZvFKNI/4VSAl4gl2UgIpIUAiEAh64qge79IV1F6PXWOJSxzI2oVVFBkOn7O99v3+Y0Fck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242}}, "0.1.2-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1e6c0a0e63265e0c048cf1cf18feaa5f1f982979", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-5eysAhvmVfU0uHLEO966nUILuBgIK9mxL/Gwp33QJo4alKRXfax1NAM366cNj7S/nEio7XBhMs3B6H8ktehhJQ==", "signatures": [{"sig": "MEYCIQCru874wy26sIZ9riCiIrNjSjDv690ee8hATgJzStEjKQIhAL6OWfmcnGsVk2uSVJFkNKeA85bG1IERMq6zuihOlDE3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242}}, "0.1.2-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9bc5934ad79997c7f71a0ff5aa2a090881008ae0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-N2uuaRkgjfkgs06HPhXUChx60dasRmtZJbaXwOhxvSPkCL5NNUJBjG8SmSwi7l2SiDBD6frRHucwdseWyCD4+A==", "signatures": [{"sig": "MEYCIQCjG48yTgEe2xSGt+oeQY3TcCpBh3qWCHH66jYR7KGrhgIhAJjZdmrNd6/Qw4syhqDFV+2Nftv1EZgo2wLlNbpkUff/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242}}, "0.1.2-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4f4acfc4211361cee2fd224bb39481d874fa69ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VxeAuj7KTv/usLyt0pw+gv6TbtLn0/iR3ZeoarwbWnqDYiN6fZArybfEmea1WPhfz39T4cWUtGmgZz/Q+0xtRA==", "signatures": [{"sig": "MEUCIQDToLpN4CWuBvk6cJbA+e7uN49mfiEgwmGNqEjvfuc77QIgJEvb16uDJQt14fZQCNKORMDWP48CpNtAKP0rJB/M9kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQM4CRA9TVsSAnZWagAAri8P/izrDur6eXrGqcU3GyEM\nlEtDKOvEkvgGqJGdAtMjSVV/ZaaRLpTDOMl6l/tB6GprlnhuPZ8/HYCMZt70\nv/8OUSjYWICl0hX51VAIKT8caWRGGsxdJUwBWe643RZ8Ibko9IrTeox/0X1U\na4dTxXMosvL1sXbVecVpD/qppBACLmeCxKRzHbyXAMhS+Wnn32+1uYdKeSwA\nPLPXIYcidlU/hYjh5nuKq7Vyg0I7T0osDgXZ+0Wqo4FgKAPwT4KloVvt2BcK\nXTLfDow+BSkiG2cNsmFzFD+gnkN+WjmWd1RchkRT5d4e96OE/b6+V7sr8eur\nhR+t4zrS3verddQsZ2eSFt+jTN0KeQ1bK5J6/hDEnpMfXouOiLdC190sAuu4\nK1ktTycnOKIGJyHEbIqqh9Fc6XNcxyZlVqotN92e4jrM+4WqugTDHUxelrTR\nQEJmheZt1k4+CnGXSw1gXZfZNMDCCzlh00ODFAGmHHXQNCPqrYq3uWxzK6go\nFj2xvnZE+xp3cvrlYIIgcSxxybqs1Jvob6GrbWki/jvMKwCJD9otptV1/efZ\navB0wL8HnFQ88ESRUxmbTDJCfZokiSUlVzlf4x0vczfaVYrINLe25xSazXbc\nPS1Wh8ddGDTIkF5sMDh6+vkY2A8rJ0uuO195dbPz1HD431GVbB0OUopYoOJW\n8CIA\r\n=aty+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1364991afcb566d6a425fac508a49a2e1a91f439", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-FG+LeAjmK/QpkReSuAqFuH/s/koWrX8VhZt/ByWvp17ibpXPaM78/GWh12syJy+gu46egN0OjeKtIj3i1Fisjw==", "signatures": [{"sig": "MEYCIQCsuAkf3OeC8cwdV1avAfn57ysTIFThpKFHi7eNCs555wIhALC1YP3+u/n37Kt49eHeR94v9icAgwKlqPGxEwMXp+dL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQlECRA9TVsSAnZWagAARz0P/jPAgiABbToTjmX4rAgj\n8ZCWo49LLRaxOVDt4WwTWC1EhM6FOPAyUugW6aZbZ1Ir6ZEBGy3sE4BJmspc\nI7CWG8dGSOZ5ics//pcQR/QxcP1zCUWmVDWkG7QUwMCSA8VIyhevOTcRIFEu\nOwknnFxsV9BFPBKLWErqdUn7QF6j9TLi8scrE/KME5hmlC2h0Z/HtUN4briH\nraQQtmHjaiD5vKp0gYlwlLcQs/nVnUKXIDnVDiSblCUBEQbR+al9a3RdoXdS\nmqjZVmmywC7PGMtFOe8seYK0uGlhduD/BNIyZFruiQtljiKvFcXChG76Xb04\n8ib0/FG1hvZ44wepNJ82nunQlI20HK2JdyDBdPQZr9Sf6hiZIeJkxR2CxmDM\n4uVZran3t3uPadHdefWoenZ2ZE0lLDWiKiLo7A54mfX2W39hQ2zUVrZ5A6aT\nXlnRPxlvxiWthxpRTWWk8ynyMQDqORfcNJxM97O8WWwDCltJiYKuSZg/KqVC\n8+fSQdOkTnKUpcNHfeztfUMThS7Sccp5vPUTXdstaM7S3QL0VpV4hEMRybMk\nv/5QS+7FL4jVWfJschvrt7G9ecTSzNkSQZ5JREwuDr2NWXT4UAc2b9vvTBo8\nxTBS+4+iF3/S0rJXuTF0GE6DR9tpRzbQOTmnFQzinPbnC2e03XOt9t9FvJhT\nxgrK\r\n=S8Vo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d4f0680555b4394d913a0c0b159c1c001980996", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-0oc4upXyiVEDPD0Wgp3q3gqiILnpyrwqtM79nkqS92TY09/DvrDR1sExuVQ1tROA9hK6ypjM360uC4NZDCCxXA==", "signatures": [{"sig": "MEUCIQDhYWWd0nDf2eOimJsTpgD+2z1r/fD1RWFmXmVe3zM1ewIgTctTl47U27DPd4E4SOPEFH0RwW41uTkIbooutKPFMVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljH5CRA9TVsSAnZWagAAhw4P/3bcKO1jxMtsREk8P4Qj\n8XaEjUoSimBKCHB4r13BFwJ5uH+2CeCFbic6oWyLvJDlsinFumXs1WjbW3G+\nx+s0MiQXdggelvpxcvpUR09ooCY/GQ7uNWHS+nMLNrGgfnVWbPaOJpL+tG8I\nTq6tpyT0lh0bxX5ku2whJ9GT6HdAJg/4dF67PjlvrumUj7qEJ51hdVa34+r0\nPYD0p2iEmAKdtVCCWj+y03FjlvDxDozIwvC6NNJt30ADFeFrEZudki6axS4s\ndTKw4mLWTVl/0Rc+f/LQIVTlacW16OB4y1+kghN/CDlVUFv91Lh8y67lGLgf\nhxxgfQBqAcMq3Ll3AsT3HjW++9C4GhElRiovg2aonTRmNX2OzjcM9tPnPKT3\no40xISWgSQhEIchQSsEOmH1Iyz/++4n88KkK3jHZBQ1zL1oP/rdTWqmQOrQc\nARQWvy+U5LuQ7ZCEyvjpIbkiNalwTyrZOPRVMIyVzIM3mATRDPs9dBFKBn4r\nNURItbNe2NBnaugMbHmuJxgAAHVWGl5rpkX1yuax/0Jt+J/0zSfthheWMcyG\nIYo63J+LPKKVs2cFZA+MecfYlcK2pEN7pC9AJTuyuv6H9e65oKcPGS8XiDsa\nE5oxJFA3hzsidtk2qawVQMATZXeK31HLlWQapcY9P52VQHsbtvKs/YU0QwjR\n15Qd\r\n=Z8gC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d56a7dce57109d4caef7099e3eed3f1a2d0bb3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-ioA/o2TXdHfkLbw2iwoNIsHjO7ail9IJuGevtMGF90c4VhAYGwGYM3whWs5RicpZJmsxs54YwHvqmB0Fmswjsw==", "signatures": [{"sig": "MEUCIQDa7btVOVA9UDDuSCQawCYNVnjkddQm/27AsOb9XMTouQIgA/HivsFRtVwRF32o9GijUewp/qUmoj/hbrLxCF8V5GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmcVCRA9TVsSAnZWagAAMTMP+wTnLNJ8DacEhgcXCfg/\nEGvvlEHRKUOwW3LrDGBTMIPkQE4Lnq5C81e/eV4y1qcGJr16MposYi6QMDGP\nXMCK6UsVbGbIUd75tSWGXgImiPqE3K2xcav/2xhNP+RCSotYP1PLkzsHxNBW\n+ZYRVGndXADGTVNSJQhL+sjrpiT/kOa71bvoSRAwCXKgmmEoyB88XmRFVecg\nhOw4C+0/RFZD1PgnRMZ6CoPu0DpLIQ9SDFUZ/sMFxHxlPt9iYfgj/wjpp+l5\nnekxAEsV7nqvkYubPG5lxmOwTyMlivKmRsMN+8lmibb5ypHdvrTEh/xDiGIX\nJEyA5Hu3Cc6Hvbug/s68yhdy5yEU4js34e6Fm3D5PK/AHHh7usBs+bbdDqRn\nKz0xMJejZmcnz2x0+TJH/tVBfQxJneSkcu/j8swDFuSyXHEoD4ZCgY5S5BCw\nKZmQsbOE8D4JMdsd/L56IvA7q8QnpWu1beayv+9b+TIBrY4ecPoiVRCIm49T\nhHx3IkSwtvx8iq6xXBsLdd2DjCZffHtN6oWGCaxJyT+cUip/vwOwPbuaXVHF\naEx09kAf0Bg5wFuWw7DiBNsIUohTky3FFQWzSOEckoHUBzk1UmFAiK45l754\nWCfYQeb6tIASjy2zlebHMQdPDM8E0WJL5C/P3WzVF9xfKm/5Qr3IXRpR7KBK\neTVA\r\n=bPhj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bc4dc44f240de918138c6414ae4ef3eeb10623ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-x5S1OGmY9Xgff+Wi7ZPAWMJNRRl5rIoAnZQzvFPlFCowXiB+cU6Gj8k1JjeF7wPEsN8t4/c2RoOcctDPAPwwbQ==", "signatures": [{"sig": "MEYCIQDJRFbu1TS2Ns4BAd2RMN6tRyGRfYXVYOJqZlErWAwtFgIhAKkp0QM43PVNqOSDnlGqQC4gToDnwS0GGnu1+2dJqxXn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5oDCRA9TVsSAnZWagAA/x0QAIjx+Fd2Aad1V/++mTzI\n150XYo5tWMOlkGh1V4ob9NO2y1dz6JF2xMg2gTUubI8xSDDbt5uZQH4aDhvo\nE54KNySjR4PoinRnxhJ14Y36ODGeJLGb9cvdxjxyb073D4BxQ1A7bSms9Edj\nyNSbZ0CR+8Mqxzx7/hlRvPdlSoGAUMdVmdtOOs5GjIfZELQ+WntzfKe/aYVk\nsWMH8LAym+wMxMMW5ZGqmXq4N1nZsolrMYv4QJ3qWxJFJpplr5aP3Lpo77QX\ndzTGosz+dIoPtvkX2oSmwKc6MsnTSaP1o7CCTqehjzQF9wtjKg0ZnRM+PzKw\nq5Bec/yeDHTBsRkRIwZgItPBNUSkDrdaJ7X95XUbJYhB28AqRfAJy6HtXNS/\n3AKqeCL0FjwyXImxtbr768q3WJ7ybnwGq6/TU0AU0JMcaIa72Fu/la25nHrN\n5qUYW14DRf7zCbNGVEvTZ7HErjQrprXWojAryO4GOQUohGoMgeuypgKTxSOX\nnAqtEzr6FxNNucUp3foyGBXoF1Ddv1GYPimS1jcmGfEbaJbBVRLo+SZjPM36\nZDci/pgcMCdy9+aQqQqCvbg8J27DGK1F4Lkhxdew/e5fifjYztD7yqYtdsvV\nc4LmJG6+w/ELcgByxV6ClgR1jD2fQ/LyLZRES/TsJUzyCl+LTYBTm4MrOmR4\n3a3I\r\n=yoKN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1c7fdb5d91d6987d9643327800b12e9d7c2715a4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-bR1oQs8mZVMu5iCwOTt1HNn5TuSjvfcV5x8BJp9R7YYSIlNfgwxceGMEtl0sI1MpiRirPZvjcg8xp+sMD1ljyQ==", "signatures": [{"sig": "MEUCIDjLNjc62BvtoDC3zxbWwp3xO5fBr/4rZOyVlb1hH8kcAiEAijq7c3G09fBtzinfDeHnZuuz7dhhkM0MnyOCLNxr3u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN5qCRA9TVsSAnZWagAA3eAP/2GAKUWj/E4uCLwMOef6\nrWzhSKTAndiTQu22cCP0crpPTOdq/o8hvIV6hwOzqizDXdKnXav2/F7d3WjT\nEiMHyYp0G+04YWz+m6JpwlF8/1xLQOULf6EKQj73hoyrZXdWJ4dEa6o0IK27\ncHJST/g65VGgkj/GGBCWiAKfQ9PRwuU7WwJQN3nfcSLGCuSD912PsP76I7Vx\n8/ktn348Vl85dDyejbXfCPiTmQbEEpXHkwY2qMK061EzJWMI5Zwv+gWZXuWU\nlBp53okW5XJmdFCjaoLcS9Q+YzXWsUICLZYszRnQbhX+XwBQkq2xAPd55yHZ\nemM4byVnC0A0aYtnnII86Nh0b0btT4rxfc24HhKmkS6SuHYzjjMmYgzEDnxo\n/fieP/DRYFxk2vAny7MYKoLhPKKGL35RIxFMV0RfASpfFjqia09VqcaHYBD9\nS7bk3zS/dLJmkL7qDpCS6tSrdt6R5f2xqidde8KWBz3iDOFYXVBzy9AWiQwE\nXHn7HSsokBykgKxJHWEcoLxb8ted98/lofVHX02p/HtiF4o95+zx77WR1Rwr\nMSVoOpjTqbnAgQF8Of9PXOSLpusjcMHtt4F7kM9jvrr+103j0gxVtEdAYqtf\nygL9WwkepC2Y4O37LEmlTS4dr4bmvZmnE9Q/eG8ebKf++QXuQc1LRTUtdjLg\n6h52\r\n=VQ5h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "72b684884751ff23f8a23dafe6d55a3b5ce87018", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-nKggxsEHPhvDrMda6b6T8XlvOGsruXbjn88oPRa7gLk2fVaSq/ZzBG2HEuMAyPRHF1sps5TzXmy4RudC8hM0cg==", "signatures": [{"sig": "MEUCIQDA/SESG7VX+6HDnJk7GH3iUgW/tyKjL9IBHKzgZ3U15QIgOm5MOG9Gi08vI5jbuOVZtEPQwW6ZL2WMPgO0hIyZmnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoUtCRA9TVsSAnZWagAAuOgP/A4gtGD/jbfDZfK0yu9y\nQjnZI0FzI8aZxwXEcgpsyocpPsRigtEbWwPE5L6cDumSkga55K8Zlnhj4TGH\n52ipJ7yq72QBrVuA6LUKq5y5f2/mRbs4gwXVGxTrZTHqlru5tp5+cniGwg35\nhvawdXUoa/penjJ3wTPvNH+XHZtO2yDaA9BpP8eRRtGuFOeUxJLz5mYIf+kl\nyRjaO/1kd9gMHEcueKdCCzS33avV64i2QyjIQ1omcuoxPlXoeFcWriL9COaE\nR9Nf+hJX7LolpKTNTesMWzFa6fIC6Rv3NbjfhxFZ3khg5f03KxUKLNWCly1P\nIMYyEbKPa0edK60CIDoi/EL5hWrHRC9rndmw9f+v7fSOLfWCiKIYGzEsLA6L\nmzEKbvG2XX/A9Z+tQH+cC+eIVeWHA2tyfZwwqXPk5iduGimzAJeN1RuEkapj\nIKCpRjCGaG9vPVYS8IC/qArL+wpYc/px2su/MSNyOguSIy7rebZVn/5FYH0W\ndCxJcz9121qoInkXPGEDsHuAnH6OTUmvUMDzRokDw+nlE+BhprmMwjHlwtKM\nRLdOjhav5lj9tVORlCISwgGEUcZyC8EyrB7UumSG3ElMv1WfpA1dN7wQvRI6\nBNM8n70w9tBTb3tARvJd0DDlxevXaMn3ed8yB7VvP4fTww1nYD6Qd6Y2XVdw\nxPdc\r\n=63RR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-roving-focus": "0.1.2-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ccbaf65bcf2617067738fa770cbfe1f493564cd2", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-yXkjPMgUX7HtMSGTUeVLp1MHGPuDnqmW8rlM0xULBurlEiuQm6O+Kbxv6LRJ6AJBGoM19ZIShnsiZ0dqBLiUzQ==", "signatures": [{"sig": "MEUCICUdrRC66SDxztW3kaElzR1bhEibSVWpxAh0vMn+UUi/AiEAx60UCwgKpqhwwa59MwRof5oSNODXFUPwlZp9tn+QDtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiBQCRA9TVsSAnZWagAAUVUP/icYdIShjy3o7cPeO6nR\nvytNrbcTI+jSbgntK0/z1cTAr/w3Mh//CgBEaaTHv1hf6syV5yI5qrxEwOf1\n9YDRA6c69hyh0kC4kI2/sKiCb8iDDvHcSr7NsFN0PVYwnik6M160xE9X0Cuy\ntqKfnLO8F3pI5kxQd8+IbcDVzgKUi/ieovKROB6DbCPVW3YNyrCPC5jVORN5\nkPh90zvoc2UQB/BysSVchrtlid+7MtB4SWzDb/omYMw+E2V49/SRHDF8QkPA\nz2ga6Fe1VTs0iO1ChaI6MSO2EPmql//KQoD6wT0xutt840obVUqB2O38Ygjk\nWArr3Ln1Nz7iuJ6ctb0vjgYKfrN554mEmgcCIvGCt45vZXnkwWu7CJnf51r1\nbrM6n1AUwafoJ747mFlk2DSjKp6tL/n2/9iinDecFOR1LViKiBIWhCJWFOAk\nSdcWFTCxkvWNGU0cDWy9ZQXF2ViYviA2dr81/37lsBSePthjwkt/mzbdZ1OB\nlrKgFgXHvOJQjwCV0lkh6gO9sJw+udNLPUf+FtYrmip09Gykf6Xd+aN+vIon\n5FIzs8i38GIrg7QqJRD2r3wJryG+uNnY8JdSvIQRP3dX7553EZjMZx92T4o+\n0HLmWLUFhOCukg1wxbr6ROSmC+P27QmgUNmouz9WQ0BzqPRx4kGOiGf0fuMT\nY+p7\r\n=Lw5m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-roving-focus": "0.1.2-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4bad63caac1dfb349f9efa4b04c3251b55a42e9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-hZQlltWKFd5fOIAUftHfOWJueOsN3n6a8cTV9lwSre4wMOpCd+Yx9CtTwo0NfhMZMtJ1Tw25nDGQuG0ltb6ktA==", "signatures": [{"sig": "MEQCICnnRQFHAZZVgTqzM9DyE7i53LoxXKMX/Fynf2U1ZW3WAiBlmUMAaBG5uWKznEehoUXLUbjEaY7idM86kpPoA2lorg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiPBCRA9TVsSAnZWagAA98oP/RykDGQGDlz+JUk6xgev\nzfz4WRK3N5xYeK4Zqgpl7NHjw264N49dVym094qX5FCtNiK4bIQ5jvKPdMcR\n9/3IZj8i9lMNs0c6RsArvnBX55urTLee64qIQp8it+Huuo5GeIennaYrCwyY\nzV26cGHnI329wwotk0mA4DIth1vpvA5f36XZW5NPsYzKsAyssEW8JV5LR1IB\nX6w4H9mQ/c/nkLNtQxaDZ/PwjWOPd1Uo74oPbgy59ZpYfOAKYcQwcXECi32c\nIqVqm8z/iYnNypOzZ87uIL+EcaqiqhDvm7qwqRtdRU1PDJF50IR5U+wHOr0d\nunCe2r+fxNzv5MW4THf6ARZxVSej+uSEm8tScqh4pp9KJrFTCNHd/u+1XEW3\nQDPrWUkgEtzSt75oCZEZkrq/xOMshsbaEbHKGAcHiUFP9Y1mjeP3OPlnQb2H\nt8hXy0WtTUaIEO7GkTv07uK1zW66JOdZLVNzKXlplDhlsSTqVMQd7CTNHBtz\ntiemoozSPAhHxvOgVVIuEpfxrxb8noVwg3gRD14Hi3D0C6neZeFQkP8uDsp4\nqvX8JM7HRYvG+vjYKTzxnQDYEu9Ywl2VaTjpk9kgbsuQQkle/uu7QC3Qc4oJ\nMsReb2exlm0M9j6/QBnZcRDC7PteYx78XL5r/IyvTu4b628RLZDQ8LtmmHWB\nBzNe\r\n=VTdy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-roving-focus": "0.1.2-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "94d01e67399ce1796d8722ec1416c2716cae22b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-18vwQ+fu7ap1RGcE7JYup7E43o1XTe/HnkJrhIfKswTdHijPS7iwh5pfSzziMXJaG/HKFy6LrTIERMzCDC1Q0w==", "signatures": [{"sig": "MEQCIHxmsxdlTjYt12ABbI1m3ewIYaAamJzAaW5kYMgHGkyHAiB5ke3XcA4FHRSC4gqKrOEk031eiL3+bH3Tq2zj4Yn2OQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrykBCRA9TVsSAnZWagAA+EAP/i1d2NgdFI3KLSE0VOBH\nHtE83cfAF/VlyaRD88H2sp49mp+stJ98eiclL5/4BmrBZSz+ZNzylczuqWL2\nN2TsMqmNakGCo9aaJkk2XEbuhidiC387e74OqOQ9ffLQa0/COAatN26rl/Jy\n/eO30N4aqmqdM3Ba4HeqLcRRfds9X537NIvntkXUCKgw2464WxeVUc9WP1F9\n7aJM5xOu3ElwGz8CWl9rT9Crqj1gVDVvuFJAUSEWdt3me1UMlpHs7QZN15a5\n6jCZqIPiyi6BLkaVlKPEG0BCtXKxnrAQEdqyg/6hETjiFy+iSUiKMrGufSfK\nAsvwsBtY9CXNuhkuPi7eV/ofRR+xmE2exlAOy8FZd/92h8MfS0yuqyZwTvuR\n5O9HJbtumizbSC0omPO3e4QSaMoHZ9RVHvKNh0M66Ah1JB5xAh/ajn4WQVNm\nfuCXnSAbAKT+2JykszQPtvbR7RB8Zc1/yngLbn/cRQhgiCtuFB2zZAK49z2A\nEq+tShfancFpL/s/50AJy1WnhHCNXgiwKp6FN0eyzOzPBk+axNg5MoiRUkpE\n76L+Mf3QPzOmiEDebVBQA60Ik747MSARP75P3669wGI1+uUGqwX7VV/OcYhu\nFCO9gNdvqZqM1jhtV97RDrxKxwSNe/nesPVl5Louz9WrcKH5rgRmPDlg6EwP\n3Myt\r\n=yeqW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-roving-focus": "0.1.2-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6ceca013259ebceb645e681f14d60ef1919a7004", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-GE9I0ktb/e2+MvxxO50Ute1wnYwJZiXdw/S5YBeToBZ3V09lzGBLM8ZLoFYBLGRAyFadeL/fY0B1I9+VWh3EXw==", "signatures": [{"sig": "MEUCIQDhNA9QxWQ85wOCjti3SnI+7wuaj4V65y6mXcdXgYmmaQIgSJsKFtCM8wd2j0kZ10mZPN8VSq06TpwoChUXpfN2D7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzSGCRA9TVsSAnZWagAANgsP/25+Il63A55nQQKxbdS+\nLUIUdlcodJ1dxWOjT56I1nvuckHj+jpM7dgF7VBcNp8wzljEZiKeMHqgRhxV\nokkzQLfkhdXZBCWoYfiPZhueKGU4soCjQMtlfil4Vt332HqyohrBMx3cEKB7\niluTrGm3oqBVR19rnZRZSoTVmI5PuqYKiIBhKvn/0xTgNbkTIUh647MF05o8\n63yI75fiOE74fQDSe1H+ZMw9roDLQtP4EqhuLkxQllz3sOlCEvOJfZ/4PWiM\n7m0ae4GoGHYwbGfjm8RWKrPeJFeDuZi5EDCIWODM3p0hLm1aIeiM6QLjkRvZ\ntom9+Wy5XEaL2d2iIarElMgAm+gyx6lgDrJv0cdqL9Z1X9tGLP4ZEdEcWf3L\nq83Sgl5pntd+07+8TTnRsKPaWbdzwFbHACYmreezcaU5/iS4oL1epMNybAd1\neRoDgziSgIuHj5Am2I70AtEp1E2lf3vM9iqseA37G5MY32To3Z6TEDrzWRSr\nW7tqZT17LbZy6hBV9CGUmTYleqX6b6p5S27cDIsZkrJ3YlxxMrb5Ckw4gvpj\nflQeD/DnhRJF6kHb2In/s0hnJ2b0JmpkDn7Ir5lE3iZl9mkWb7cF/mdmKf+R\nVsY+ftIguYbNmMlulEJomjF6dYbP5Lxy8X3uPKo3kiTXy0ZILLwBwA0gYP53\nesAH\r\n=p4bx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-roving-focus": "0.1.2-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "12e98ff0905da0c3bb2ef5279c2d28287fcea309", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-tnoeKuGJe5GZsH8HPGDQSnlUik86Ams7tF4iW9Zsb/MffraNoN3tytuOWhnk8QcINPumKg8cIdlMSbVzYAtrIQ==", "signatures": [{"sig": "MEUCIFWqS0E4cGnwQy1gPtURxiBepjCjbKzJNx/yfZlMd1TWAiEA0J0LqC9u6z+vsK2UR90HHrd9xS9H+1dV9/KKF25fBoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr43dCRA9TVsSAnZWagAASV0QAIWJccvU88r3Hog9j+Ns\nXbD6bZEA7BHmBOClmGULWfPyAzrNk6cXZUUJZ57DmV+XOV7EUcRma2iycqVq\nMdeboyRw/ur26ghQsLPtXW3ZCp8P5Jj6pnT7l0IDaBv7FrSFJ8oFfiGzQdIS\nipuds4MYcfIpExeGD1Et7gRrsSzYL6Ye6cGkoYXs7HwOU/3lC0YlMb49N2iN\nOQd+osZvLhXsYpc/11tMZGUod7C520s4lhk4I9uvr+z2P4U8Hn1jqLN8LaHB\nhJQuUcer2t01gog3yaVsQ4xalODImvdhdzSzEm0ofFA7abWeBFoXw+CaHSlH\ne3BozoroCsu4ktZeFDmZF7svIKXxFn3oKZlKVXf1dxaAWzWcTp3IUUDMsMSi\nIsQwzDBJqfMN8g/odv83qWoTW6DTW7mpAOv6Jg6i9OwCfcruiv4ISZMZwwsh\nXPCQnSPF0x8qfrZZQz2sskrFVZ0cgWlsQ2RrZDXeh5B263M4uXkrtoe3t9nx\n/gHTCfwIjhEJiKQBWGE7Eq2Ar6BoZt/3yZdNkhLrQjXdRCTgFvjPWXaYvGTL\nkbMWpgxNwx72bfEHjEqXKAYd3Agi1VnKN06CKsBml4WHBKD5S5vqVdtSzpaW\ngZUrf3ROaePsaogN8qw4oCIOYVe3hxotU7aT7aOH9RPGD/wErLDc5ATTzAUz\nCgKI\r\n=t5O5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "516ab31feaf813f24ccae5bdfb59029a90a7184f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-qZV6m5u0ijvppqp2QzAxAzdN43vuqGWp1kWQxBGeoO6SrrXeIvuJ1Gizq4OQqioOFnX0uU6Z8kUzTsSNgj4L0w==", "signatures": [{"sig": "MEYCIQDlZ9IIyv3QNxaesKqa1zQWyM/0OhcdKlG5oLKmiCntJgIhANxwTu5HN+i+HzSdXhZpFabLAoQ4Vg7Y9ZmPtWJVmkdl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD8CRA9TVsSAnZWagAAoCkP/0cHXhk26erdeGBPVue9\nE0wqlFfW7v1hJyTvTGQKk97UZrr2A/EKPabxvgSivzKyhHBufuV24dQCF3FT\ndFAicEVJ7gU4f2ATZDX63OCTzL6oXKeMVixjd3vBYrBqKb9Qh7iBX0Yvwcge\n/WDgt8wyoiQJg0wyanLIodFgAjTVANWRaHX+fu4EvH0GPayRfZ4+/zj8WIlh\nNcpb/42HFTmDPWmoP+i6s8fDg7LNSj3jpf/yKqpIOlmSVu1qA2tFwwwO0ivK\nrRjvwb+FE0BO0pHC6ZWJlNLe0w/5RweLdawQ1GhEdZD2ThKWPYiFKEsdiKYr\nzXJqDpZJMwer24WY+2oOUjvp1W+1Y2KU0suCocONBW9XcS/dhDPVmz39zhYR\n99fIkMHXjLg7tW5GV4gCLj151TD/oB3ZQPJscVYUMSomcGYZ8hkw9ByeqwWc\nii5gR7Ln8juavs/xIhOyuby8wymqoFpJMWqKNGguXKNRXCHLrxdp7vG51VKW\nkIgYeQjc1L+D/v95F8pgfxxGDnPwSi+I+VLXnR07dL0+JjqBS/L9JcUOEE20\nm/pszgtuN2jidCSpj8of4+rfC8IKvm+TJDrSQF2urCJS6vs4LcXXd+f+QvoS\nrqZy+MuR3uyuinGXiKMG9Jou2+FXY0RktCDnS53p7xoJ1Ty+/m61mPx3DIPY\nt+Pq\r\n=mPkO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.3-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "39dde3684808e822d002c6cd641480447e9cb378", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-rcO4K1gXnifm9jWhFvMuzicJKNMqev7xDZgsmqjpv2XoGFRx9SAMRrvKtjfi8zryAihLu6+StbfmzZtrYwbW4w==", "signatures": [{"sig": "MEYCIQCnmt0N0yU/adGFl+NwedWxyn3C8jBEj85ja0JPPGmDnQIhAPTGfKlVkztT8vjNNQEQk3NYzklVnWEI/R+F27yQUI1Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszA8CRA9TVsSAnZWagAAS5gP/RqlbPY+YEdFs75w0J4M\n8py0NVDnpK6qH8LrQeBPq0I6e1agRmAlC2z9t4wU/6QlsiGAx8PAxIjopbds\nyhqqacWZvT/vKFRoXU6yINVVJKbXR8UbBS4gSksXbttoE/EOeIOeh4lEW5Ai\nwaDFnojmpJwu38UU27WyKxP7wMX695XPg3qTCyuY9X6hisV76XyN1EJoF6Pe\n2C68QJcKueo9x0971K8nIVparN3bndU+aO7nHigcCVfkMBhK8cC2A1LuRyEm\nRcGx+sZPM627B+0ZK<PERSON>VcZWZI0Colm6IMRj/N2HPbWt604vdIa2NrGxgbx8HU\nE5u9pDdqabJLNmhp6KMfvD6Ulh7f9qWZsn+3pfjhq/wCDjurmAZ6NJ1EEYvW\npZM1H0nQu6YzMIGxF9gEciyb2A1pTiA2+AMML3cTCljDYi4Jt/OCogfT+RuA\nYkxclROkgz8dt40MrcsZcwJcOsratmILqrEx2QNnspUbFpf8U7eC9j7l+jBk\nfIZkiZgzLDITubntjnCp7EIOkvmYvkEyGYzIP5r6n2GNqnAXbBaLl5X2U5dv\nsTl5OaWk79iWW8/L7TbuNzi6mbjgXV/GA++g8G05UIuxrrUesBHsTMnvHq9e\nSrnByK3dkZDdFCaL/+0xBvXb4EuzLwfjcPQawHRxVLwCACXh9t2xV7IAjRSN\n8iuK\r\n=Gg7I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-toggle-group", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1e97319335451fbf098963ec2a63be7a9d1de0b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-oqEm2essC++lfmyE5bk6NAa42yxwqZVnOPYhvGEz5kim07QEL9oTBOESmAPPlJ8ZZw1q3HCjzzB/d5j3GV0/qw==", "signatures": [{"sig": "MEYCIQCZZf52tf98w32l1M8jexXIiwRjjTKZcqL2YPXFe9X+5QIhAIjHdzlKcm3S9CxlMYClpITBms0th65hNkGrjwgwOdLL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszr9CRA9TVsSAnZWagAAgOkP/3i21++bzkdf9Qfn8/bR\nYvCgJti4KBDlld9lKRV1U1C5amyhI4Qsm7bJrJIXZf++ZXHB7Os0GjqCMHmE\nZm4RLn/+9E/hIeSaYK6X5mlYIoNEwQ2Hcevj/4o68im7Utmzqh9g6qQWokh/\n0oxZ1JEB1laNNcBK6WktmJmDcBPBop5n5R9WT3U7N+wEF8qcPlTpG8f/GGgA\n8WSAkiZIcT0ZI+9A/n1a8wr9lqVFIJYwaiz2mFV2HvJLSGRsqy6Cb4vAq2S3\nu/M7Yb6rGI3zyQo6Fw/A2n0Nhe3ndaOoZE16pViBHf23Zb78SOpgsBCYggR8\nbaxHSZpUj4S72nVAOZA8uS6OG6juFB00RM15tnuulJFAOhiVD6PzzFv+xBKY\nV+STqr9Tmo1XVe1nMWzvWfcLkUz9ARJiArtUm9kmpAzFFhz4W240xPfXshAB\n4DNZngSCiaIrFHcqxKcgeM/xKeKIzXljtf/jkmlZBIP5e4vpSWCm3r03KLQE\ni0nbNkjSjVyugQpanflmToSZ2+ZLwD5pxbQK3xx6hTE037to++BMqkq4yQ3w\nIHqGT6Caj8lVburZRlz4gy6I29nvI59V83IMnoLBEoU+Fb5DWKezhWpTM7+s\n9stNHYzN6JCti9dsT/jjZ/FWmAfFjulrvG++Iz2b1MK1CmLQiRnHE72s99z2\n57tK\r\n=Qnb3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-toggle-group", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-roving-focus": "0.1.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e8204e1c458f0673acb58eb82677c35b867c04f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-pzs3Cc5I9JKnBdCBCB4xfhbQpWNKY9dYwKVxcYLzGmaUsVPUiAq2AYr/Ht3LebhIxWYHkggjX8EOTeO6ExMr8Q==", "signatures": [{"sig": "MEYCIQC9wNdWqjGwtzJE68GKnZTZ/CIUM3cXVbj07OyN7OkX2QIhAJPgaAj9Dc7rP2D1ePTnosEBbQlXJPZQkljkp+B/IGXM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiuCRA9TVsSAnZWagAAcHUP/3td4Ccx5MMMBkngzEKy\nmWaus7lIiOMUPYVXccPRFbCxWlRoCjTe8NhwjWwZXg/6cZYPnE1JR58IIgHP\nP3mdSBPVow8Dn7z9PfaFoZEJZ85s/0Qkn5lKmnou5RQblXhyG2uYaVqURkAc\nrmLwwBb807ibEpDY8oCeoK2BBZms81lFjRHrOM6vFBpuIT51KB6nv65NivW3\ng37GrcoBf3hImFlXxl1h6HReb5vTkpGQcDkBo/iX6MPXq93lOa8ek/MF/378\n3YIlpboA+FHch9x41GySXDK4ZTEdAD7fxxYK2nom5NpCoYLlQWuSdFuHwj0v\nYi2jTT7TyEbkwG2ARhqQRxbulGYI6la2d1VjasPVLhLqWa6pFhEhAswTmRxH\no0IKH6l8rUX2gdOZZc/KlKClhIfd/6rpPcNElQs1/6HgqfTaXO317CnOclYw\nbOOfpOaLdYscYyZupTdEnksneIti5T6lW0OM4+hKEKsQn5GpDmlXQBKPo51v\n4cGmtREuVEcpGBm8I2hn85vKAq9jGMy6FtOjadVwqH1xF2ZAwu7n7SfoHS38\nWZGyekMxiVfPlHPHjkKJ2NQ2hRp8QTXyTzaW5bUc4ASuPIjm5VhCdmtIPF+I\nQ/afXzjvtb6FPaHvqYXO7+pKZ7jR56wlnYPGCDwyQY0+lTtgF3oZ5zB6UfKa\nYX2b\r\n=/NGJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-roving-focus": "0.1.4-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bb51a5b3e227d3d2056f34d701b5f9a7bd7d7b42", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-6F5dNTUrVeNPYjx1VIpnTvlmo7+eLgq2ccFJJ7n9utURXvAILrwXyf/w3nGCee3xLW7twlDp0oCmG48PPMGBMg==", "signatures": [{"sig": "MEYCIQCYxY4ygdeYMWL4nYl2uMDtcPUgPUcAtUulgCuaN6tWIAIhAJ2x0kW9MLErsdiSM5X71T070SGvxOEqjmYKJyvYqihW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLkNCRA9TVsSAnZWagAAQvoP/irNi8tHNNrc66AhYzHi\n1Wa/XtTWXlv2v+Ejp4lefk7Vw1bXcRc5rt7UvUNlYNQsROLtzNS3nW0nw7BC\nrw/xBkGHSUHUEIRMLedHoh42ohsHOoKPHnW9r92FwJmb4ipb5uf5pBz4evYb\n8n+5adnqkQGjQU0JjKKuenDDXQz5L8BqdObyIO9oF2vERdlExVcXkmlBm8Q6\nGIgH+zrav30FAiPLXp6QfLPOV8phY67IFRnVJuyYIkR5YFL6XXiRlFc3ECgF\nz/JEEBtTBbhB5DcbuVW1McxrMOCnP5A3Ug2egwDvk9OV+G5vswPFmmwSVzev\nM6xyCwiVKn0h8/JRW2NRRaiYU5owutqNHk1B4cYtmYsranNWRXb+D2Z7wAmx\nWC7Vb9rEkiI1GG1IFOLYD3an9IFqCS86g7Cj3tgbMvQy/eC4l0fTe4XMQK2X\n4J+sUwaDS8Co25+/D6KQ88LFFgQeZ14KQerXi4jZgsfUhPaSVlifaGAzrzK6\n5iKl7HUw2z3V8NA7XlhtycWQ+A365tkekZPSyr8KSXCATh11fsOPEaGjd8X1\nex9djtqKj7n4jPQ+xo1vX3wOcM6lP6dgUukw9Xgb1NHq/HeunTMRXCat6cKO\nct4viNAfoMfvMREBJmONv9VaAQyg0LtJioTvnD2Y53hqM3tvoVIjLbK1KjCh\nPaTP\r\n=GKwJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-roving-focus": "0.1.5-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "68561588cf0a7189c2038845fbdc7ae1c064771a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-s11Y+E3y0vemWIY/6QlYm7VoJznHc/2uKLAKy/U/0BXRbedl+r1HoeVZ2s91eF/E0RpA3CIAQI4OhmPiLes29A==", "signatures": [{"sig": "MEUCICIXlASt7TfKXRV7VzTkaVeWy1glFtqmaemdyUMl6qGYAiEA/Sk74p39MVyDilqLDFxSK6clGzD5nr69rekm1Ld9ukA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rrCRA9TVsSAnZWagAAat0P/3unrEWcdoKPwdFU/l1o\nOdjAKXi7fxlmPvaFzyeBuc3EJkwfhKPbLjNF6dTt8YmQju2K7iYQYeD5CqF9\nXMtfEe+AxB1NsRqBbbEYMuj7nEqGnmwrrSa8wZcdfSf8pIx+co5G9vGjHkuX\nHNgSaSj27ApVdO8XVPxDxNVclkfnVD3sgeRkeQ8BKQ5ZkT1eXWuAqL852Wi6\nmRr61rchIBGvWY504ILQv9NGij584BM2r27llj8cXE9pPeXw/1RWhZE5L5Xv\nWnc7iIfFdmdCjbKGjLVZVSXPSyvqQWi0KwkRE2gTWsRXh3DFl0/BZTresUPE\nqHtKbYy1QJadB66CiFkb4UTsE2TwdbUkxb/Y5W75Fjnkz+9cJaU9L6oSVO1/\n/7rxnUoaivqbwFR328jLrntmJif9it21tkeNwLHeh7mT4CWF67Qv/NrT4N8S\nUEwELSa20RKirRhosgrcam/kDdG2RcZIbmv4dsy7YotSe6mtKuLRJJlpWhoJ\nsPgiyJFSEBZbJoSJsrk1c7zbk0tZ6R8xjDaXBA+FKkWO5bU6FE1MDUGzK/Ne\nlIFgicdWhjrAy0NGnkawpF9R8xZQsjFF5421nek63zvMmbVNozx6MSQvI4FO\nEzST7a9sbn8r4UVZUsfF+abpPlnyhye++DmAB4oEAbMSZhi62Z9NScdkQIkW\nBFkh\r\n=wQip\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-roving-focus": "0.1.5-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "98702c34b61ca7ab34948c7938801530826faa7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-3zGNck3z8IMFkZ3XU1lWK5+R2ZavUjAGNuAuTeJH+Ggl6McRmpuwQHO2BeMuIx2Y/1lkyIuPuMVFRD+lCPNmAw==", "signatures": [{"sig": "MEQCIHk15ruvKicISJZCRkwtslCznDdfwjkq/SmxLijbl1E4AiAFwlxgM3RbvnHB2g8TKOpcPfEAFaBNGdCyOvnJStaiPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEnCRA9TVsSAnZWagAA8IQP/2hfSiu1Am30WI49Zw+X\nzIgjD2TfySzP9mo6U6HOUGLSC4i22Va5M0kvWgUcGYXL1xA2LF/t4ccGwaDY\n/A/iI+fbXOl5p+MAuC8YwMPUhYWjuczxUCLZrPE7tQ0IqjIaAQ1m7pba0qhA\ny53BIqyvmH1lLNdLnCtVMh6YNj4H6uWoJQ+lv9NqqiJ0obz3Hyhfv5+VtL8D\nMh6MJPjFlD3bq25rP61BtORNhUO3byfOfWYQkzNb7mo8JQ1XhBOqqKOppu3n\nGYU5zOtcLoOoMv18xKudhwUfy28na3PTH1a0bH1tX9mz/ksm008Kovr8JxyI\njvfUtfT3pNpheQj/cEXgKwVk1bGgBInJyV1wJZ0j3rxnJEcWm+QyvUv+uG0G\nZmwVqiiHS6UNicKPbS6zBJtcvkCuiFP2mLDSF12i5Rjsb7Y3inzTonU0q3SB\n2Lz4FuHfB2mV24zGa8yHsKWd8sH6RJdBRViAFxRAGnYl+QmUD1BvtmqZI+xb\nU70bH/OO0NR59gcc2haIT4D+g9P+HD6VgZCtp3afpzkfTlj3r2tx226aZAdC\nJzJTzVxtO3gIkI1ZBjRdWZeUp9pqjSRQgVYslnfXapQ2/1KI3W+N8tV+51Q8\nNBF3dZ2AhvKn8CE/+xY61JXjen5JG795TrdKylOFPlo2BAF6BUYANgTE5eh5\n++7B\r\n=6UKD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-roving-focus": "0.1.5-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a746066fbb9b0ee344a8c881e5b57ed43a6cc58e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-HWP66MtYtgwomYhFK7wI7OWsOwy7R0VMR+ZIkfl1aun33LRXDPcULRCRL1eb6mfQVMFzA2bsW/K8xC/N9TdRLg==", "signatures": [{"sig": "MEYCIQDjWaSGj5TCVJqFErUMntECyqxsGkQQHj6s/nvfgwNrNQIhAORHLilo/psDUdUyqs6mEYVN3HmeEeVejjvk5talgDhH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CnPCRA9TVsSAnZWagAAlVoP/0GVaHmCFiZz9Y8wP0ld\npjOtxWiQ0Lgi2eHplWozsxp4ExLZb37KRwzEAyqFq6DxrUMv+tX7GeUfTshT\nXpU8K5E1hBvu2v2v5sth0BqcWJ0bP4TxFYA3BxAzlsraWCY8yvk25XKw/o4/\ncHodPUYXUb3OQK4gin3hfQKV4TQISJ80sMCrH4LtAjd3Dh1u5HrrvJBYnhEb\nTgQ249vxW36Wp8yGsoF6j4PUAFYmhpgX415OhArvydsd1+L7mZMQ4Xmw8Z46\nqxEtXMhCBqWMTtBuOrX7zi1jlMyJa539zBMe9rvOjLz4vlUqCLi3BlNR6HmV\nsg2SXljlzo6OPxI1Wn29raz9ZZpgb5dgtbn0M4MgfByYJrxsJi4FeOpxzD+g\nHXaQL1sXo8ia8zKisHeIIKBeTNDZeFJLqqP66TZdSkGOTqMuJDVEbu1Y9j94\nTGw8Ivop8nI9jg/e2XTMYCbgPqTrkkErqYqD6YDH/Yfk7j3+6bv7ZfF/LlNA\nr3UoJeH/nCWuJe1m3FljqVAxOYJAuhsMUJNDnQ71t6cgIlWuYgvm9J5yFTHm\n99ylcpEY1xG5VEifm0hKBj4vjwQAf8BSHKI0BrRWtE0Ll5qomAS5c3TjwPq1\nHfsd7PKZ0i6m8iAiyBhcz9eC7jjyw4FGPrh/OHtKQqtS6gQZIpwvNr4UoB1I\nA8XE\r\n=5abx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-roving-focus": "0.1.5-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "791ba1973851a49f0de79b9a347459b95b05e440", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-LzcIAQKvIofxR4NVE9YleNxR/iYZBJBwTgLjCrONVXor16CSnnKrymqyW4YgjrS/uOe0wwQKzgW0qzn2HdmN+A==", "signatures": [{"sig": "MEUCIQDeSNTLvqtyRNpVNsTX6xxgHDfy/TeT+5J2BGqnPxw+0gIgQ1cqXk8J+cswZHk3FzKWBDHnsTKmFMyJI4WghdGrBGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GrUCRA9TVsSAnZWagAAEm0QAIhFI6aOEqYms+9dxrXa\nc54eo8KjxBXQkyXC/N2Rw4xZ55G8LhDPBpXeSEzYnhrK/yNhZDBUr+Fv1Ojr\ndViGAz+sEUZQfxKC0PFuWdrFSW5uwEW+AODVEgxu6hdq9NDboUYnT2UDV0D5\nNpg8rT6RoKGQ6DsDO2CHhGJLZ6C5kMe84e+8GiOTdZzujyhQyasiVArYdEMC\nlKMQdV1/QqaPRcDPV7M4jKUCbJZKF3/7q5255IASQGL5Uxxl8B2YAb8hSWci\nQZl3ta8IMBrPjIBCchgeRTG8Z/srSudwvGQ4D5wngNhDHXLJS9YX8o9XzGtl\n5rT9vnWyopLVcMDKQo6irEZCBk6nN/g6eeX14xBI4sVPtuhinDm2Z2yvLlj+\nbJLJWIGPGimBx4p4Eodu4yGwJrZ/cuX0ODHwNtQv5YUjwqpc0pGLkiWYQSqB\nLZ5LL5PDfLZFXieoYZXdBb2Tt+cFJRSyAXg7y5vLeyzAa6bDZltUiHelKNoi\n0CdrSKJjkXT7MCg/u5mKTQHONipSAhUVlLVvPKCeGmrOF2Vhn/Gc0Gtow9au\nYjr9ln3wN1u0pTC5eKnO4Ec1Hy5FzDmvHkFCPJD0GWIkGuczxapdTNqjHODE\nDe6QA3khv32reKiAZcom6WqnRT6K9w199BJ11nb65llYkT2Zy7mNTzXm8Mee\nc5ZD\r\n=5bRN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-roving-focus": "0.1.5-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b06983da0d9407bc7533e9955a3a2c8cdb18e5b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-3vD24adOOSJDlNW+NOzddSVOj503sZj0HCDM4l5yHxcKXB0J5XMuKIZDzNwQrAHjDvQ5YbFA9+8wsBhNvO/5Xw==", "signatures": [{"sig": "MEUCIQChccirpFsT3XLw16G23fhizDkM7iTxdzBPMofjkBRTKgIgJU1xvryYJIZvvca9667otU3bTR6tcPe3jQIz6RgaQD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZdGCRA9TVsSAnZWagAA5BoP/is1fbuEN29LdaMNesoR\nrCbEdAUJHnCY5pAM4DudfEPD4HLAdzliJt12VvXvi2eQbWQ1BveCuKMjy/HY\naiagEcAINozXIu50kLjPP0LAfbqQ3aqEaGEPyzuaqPO4pNqHd/cIyyRrPl1R\n3GXAIcgnerlAQMRPAxCylvaAIa5pCBeVY2LfFbq//U2lwOlj8uxy9pN91vPq\nzYubzCDH4yTqpdWKQvkNOHqTfsUq8NlnJSia6nCmrECUXm4dBvDqxkTZKNFk\nl59uTdkt/Yn5quJ2c5V1+dgn7NS69/q+WYi9weOoBoqPLz/7muBrleBE0Dpc\nkn5RcUG75fnjLMDyr4uetZZfoc62Xj2NMOG7hjlPLGdHp8E0dcxsP/M6tsuT\n3YN8O9JkL6rNd5GNtI6YhbiogHwhziVqrTKzoPoVSqTc/+bMKR9sdC2pEz19\n1KrGI3DAMPH1wDNuD4O4sgbLVLbFAumvSKvrNYAdT/Bp1UBiw02T5kl3VgLG\niN2OFagxGU7aqU0xk2RTQGBtmnWTtAhNeeShFjLQNwdYH3rcKduxcE+7R/v/\n1PS9p+RbZziKOzcUVdpJ1vBLWlakYNbGisoFyFlh7/mkos9l6bcTLRpJACkv\nV09JGCQWCiJLXkeDOwMWRhi3lxx/XKwuKMn51e7PMEj+DKZCHYxxbAdav9g2\n99gw\r\n=aAWV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-roving-focus": "0.1.5-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "775bfb3bd939dd24ece14084647d3fe4caf1cfc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-UsUNTRBBur4J9MM0WTodcCL38NNxawUpk3bpHY1QxucigGsAyDoD+6ns2tgXCbyhAKxkNbdHToJ1oskDhYF3nA==", "signatures": [{"sig": "MEYCIQCnNOMmreJlDyau1LQ34Hlq0YghaeGJVDGHNg5d02c1oQIhANVfYmM0zGwNiQCK/keMtBTEbiwNDHT5Ypubypuz/A4S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtjCRA9TVsSAnZWagAAdtIP/ijUfaN+46r3kznxeZtN\nP5gqAkAka86MLbtEVXLeVIwZkV3v4XK6YjPr+5FoLh1FItgn2WpyIt4LuUgR\n7TXeaPejE4bAGl74oKHTukU9DqrUYboGYjnoJOxlr/KWeXELIl/LRv98P6VR\nps7Qgrn3FVQPxfaSlb+rs72HXrCjhi7NfS8/NxaIW91wz1BdqqnZU12r7tlq\n6ipO9VYw/b2rNDKVzsjdGCeounekunoOFZmaX0fQx3Be4b6i4AtAB4Ql+KY1\n47pXvMsk88Bufv+brnzDomKsRdr7sPcrQup5XTwmeyIyDT2GXOpTWFhJ1Qka\nP991Z7I+mT6T8h1asqyfeE6dNgAokPqIAjZ/c6W3EjBXBlSG1BdBagl/P53A\nrNHqssXzdrRYXtVKCscQ09j/M1TuX+HNgpiudKjQ0P467+igJSXfeCgMjQ4P\nrHLruE/N11a5DdCWeHd8dqh6u5GOcQhzZ9wqSkIJsG9gvcScvJ38I0BhgkPQ\nEwIczzx+NZWVCfhpVVVBmsj1vuTEweUaEKRKI1KQVJZaT1DpjWHNJ/cEpEtG\nNzrIvVPfIGlviBxoFlUbBLe0hxHDFlRQq1Dz7VpSPRFokG+nZfJwGD0dLGQw\n9p0Q48KZHdvU4/f5QexZoPBCO2fxpT0/oOsrOKXTaBNl56BstYXZIyxn5SB7\nH6JE\r\n=UKS5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-roving-focus": "0.1.5-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "11cfe3c525fc2b56d9dee563534de263c43dc169", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FozsNqcu+aVdj/oSMdZncuH0UkVYThPIPkOmGsERTQdSrBf8a+YKOZGDwA513wbNdjrKn9BF3fq0gBZsF2HgvQ==", "signatures": [{"sig": "MEUCIE1H4IWQYfzILsAH7P+x3qwo1gPfe8rtgp8emYdfPc4bAiEAujCatkdhzBRB341hp/Dq5QuQ2RjilS4Dj0ha1v8+gCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sd9CRA9TVsSAnZWagAAKdcP/2lkx+47TQgQEuRSCXCy\nzaF51o1Jg1sIt9Rr9tv7h0xaQySu0rwvvxlL79lyCJss+2GcAP6s6e/xTg/i\nkEpmcUf+xTk4fFpaAKSyQnMLgtB37y5TJVBRsghv/bmfS1Jy0BR4M2gxZ51c\nwsUlZDdoH61Q6X7ZhjTtePXrWrg7Mwc7HvqX7+IP8ad/A/colaMIjQ2tdykN\nBL3ngyfx3qkc3N1sfx9iO3FnhEPM3yzs08tEsYdd4HRTllz4QTbJ94jo/x0P\nUkUM2nZb3I7QLjodTO4U0eY6l+k85WVPdt6p+Gr3yfQeCgtnPJhiz0/1w3RO\nmLayeveuWcV9y9nFHRE5sQKL4EIvvzB0mweLESf55WwG36FkOAmy9nKhCXSN\nrj0VrveQ+PwsUnOqltu0gEMs4QGiITIw8mWSC82yYs/lXSPXxEFavwTGPbcG\nWK803lwV/KHnJUrgDPkGks04xFO385XUnl6SxVsV9u3wvwBCcON7gH5OCdCQ\nl69mwMGjfsZOn5K7SRd9/BKaatIQRbAGzqfvrw7KvGifxIgCMz6kIkDipwp6\n9mFmewu943meaULPs3oAqmIrwOUI+g4T6L8YB8PFF3hnDeKhDpJ60AmXyZfj\nNE2UgB9c15EwVY9EOTV/5SJaJR65y0T1AuAAOIjGLONyMvPqkXk2JYoZMOSZ\nEO3z\r\n=ITu2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-roving-focus": "0.1.5-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3f7728a9eae6507cb0db29701f08b653e37c90aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-RqOav6ZtE+lt0o25F9bu43anGcZ1UwBHTmN5RgFX/ICpIZiLY/62v4YIvLL+IjJ0Zft942xM/GreQe1fk76y0w==", "signatures": [{"sig": "MEYCIQCqE3mUO8q4sVJen79z0ecnw0CNS/GcdMxkOgx8hAtbKAIhAN36qywngRI3XPvMf/wneivkGZr9ug/Eq8za5z2DisYI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xD5CRA9TVsSAnZWagAAHrUP/1llj2jtvbbwbMG00VY/\nmRw77XmxY+Yk3U7BKOYeivpz9hp4bxK/A2UXzPAvZ1vRiNm++DocMgLnZBUC\n9jnC6QQQKH4R3i9Q2BlJQcgD78xzQcYpFicBxGNx8ZqX74K6r2WEVxiWx0Cv\nYOccBOJ1BKgJRmeXScJcE/npmWK0kEuIoek8dDWjDiImShacPLtiXzclfFjj\nZLYPqvOmOv7UNF+94IiMkps3djEMOgtfZP+kp1hmf9XVsFIFCR2WRpUuJOIH\nLZEWlHUQ9TZt5gQAfDJRlNoFG7IzKJ1bhDxRcNeJFXkMHMkvBvIUGr9gHjMx\neQwELOlwQA5X5hnzy5OkbeP8CWWOgHEnVVx1SkMWmgVYR7gaCRTjrCJwmsSI\nEGzildKgA6y2+0J21p1rh9DvpxuKHK/n8+JHY8JSLXUw6osG3oDlTeWpJ9ZG\n56UN7h+R9gw3419vH1OObebfUCUEl92p1qo+5va+RmiGZNaXWhdsAd7usViY\nUsRDjHTHXQFbv/y2kSok9iBL5lrT8d/VD3cvdWZogSkNH8Peo12nBT2CcPz4\nkEU0m+HjfQSuiwANFs8Ebw0B1H19h5tGSWeYDUF+fJpfpEjevZ/1LEVY3KvP\naGvbV8vjYgqqy7saSCAbzK+WbfJMzAoYIgDBbgYCMg99qQHpu+t7pWQuIPD6\nz+ay\r\n=9KN+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-roving-focus": "0.1.5-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "41145d5ae5f18eb5d656a9243cf330696faafc0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-pvpZ5Igd/jDJXUIdvxB7hOIcsmKS4xS2D2FH8IVipwJNcePY7iZ+snT1zn6Jn0h3lQZezJ9aQcwOcKvVXFnoTw==", "signatures": [{"sig": "MEUCIQCWQd6Y5wXlm0HIJuF5JlYKv+qbR0jS2l1JCBevALyUOQIgTUiHMhehFSvl7BlXOSEr/obEqNkj3nfNBn5ElzqPioU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xL7CRA9TVsSAnZWagAAR90P/3Ra2Cr0/o1fRcPAZtSv\ngNlTaltxFF92lAmJIgTcy50PiANt2UNp/7xfLUBzst5k3REgjQmO5JH0yayt\n7GeogjQa/xZtZEX5H1b39OFs975DDsvQ9PaOfkb1DNUta3X/uD0TTF9MqNIY\nOUMFP/SGNJTTW+NoKwnuCai7gMId8Ywgh4Ukq9bPI1kPmE4we2oWFJeQPu9E\n1sLN9nZUEGehfkO7vfnHzvE4PrU/K69DMXw+n17ir/pyYiVa2zMNeMwDCAR/\nzf/HeRtjMY4CvOAHObbCymOmzwn/06qdWU521oeZ1RKr1qFrS0PKY4PdiPli\nUooJSuXLTpUNgQxpB8S7zft+JkCLwsRLB6ymVMTnSVE9iy8NPsXu5ZxktRsk\nlmo0Y0sIkRSp580TWRRwN6jBxg0z/dBFo3A0ioY6IgJmXtNXGWQcqWmVUDOo\n5A5JLNtyH4+9DzgYq4/MT3Yi65EWAHc1/gPpQOeTjWMfM5rDgIhxNFmUL/51\n2SL3xEMIzrvkLPEmyuz09h0GSR5y0w8bvvWsAdknNdAr8CcGEXIaStsXPc47\n3XvE5g21UCwAeSU3CNcQcP5p1luj6x0Yaprqflg47cC+r6hsUL9VhxegKoAY\nkKjAuqj2vmfgYGnhfn3kCf9dUgUaSa2OkdL+4rl5oHwiiurgahZM8SmcDSIF\nTZso\r\n=9bZq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-roving-focus": "0.1.5-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "20b6b5232d7abd938af2d1b5151b86b45546c0ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-xWjMiJUpu52GCHn1l38kMi6iR8Ta9hR9u+FvvRvyDzq8bdpqCM/qKa+p9gKNwq/UoFyy/iJMZYquKS5xo80cPg==", "signatures": [{"sig": "MEUCIQDCULnhR1Yb0v7b4uEBLW3rhkeRf4x7549NpZX+or21eQIgEjBFttFQrOyyMoQAzESSWrBcu7HNJCIy1b+TfAPKEh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43256, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D1BCRA9TVsSAnZWagAAhi0P/iJkMxp/Pm1ESfImNMg1\nyXWWac/u86ED4wzK+YV7zskIcHRbG/w3enFNc3bTyL4DblKHx+XIF9VAuUH4\nlETA68IobFoGhEYMyM+iuTJ5V3d210VYlm5wC82teVcJEioPs6+fpVpH+FDP\nUoPYlFvhcpWQ1XMMkRFT6Ar0perzD78WNBdvVnUSFfIZbIhAaAq3RyKSgSNh\nrrwMIhI1DeWLsJGPYU6FvClyZMU2IlnkHYEWmU8J43DgFqds0n+g4K9hUd71\n1bciKNbAuF4l3zBjB5we7STlCJgk0thiWBryxqucZUGOadtN9ykLstUeQG9v\n/Jc3QrgggrfUHYPKPikmeJt+xAJyq9DDHuXCRQjpRIMLSwemUPTZwc9GCHAo\n5/aIokiNtuYmaqtnjaNzllfSpgqroFuJaPd3lwkulrkceLIgLdS2D65z0jp6\n7/kmm9Um1+SUShFsQcvBxckuH8ZEwqfpp3yk3bCEN4vS6ERIslXJuUc2g00Z\nRBHg3+mnSqGo1J5Qo6E87Ju0bTvrVQ/Ky5w2nvCtemw3P9JIqTtvSTIdsRnR\nDA9i9P5S1zjPkvFCdlff94Ebz/FHQg7N0AXnm+TRRQy3jDTrJcp98O327VP3\n0RWDxGnz2U62kg20t1liB0xsz2wYBAuU5OCSsYrr2ZmYaIiUrwowDbWEzAKG\nxIit\r\n=pGfG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-roving-focus": "0.1.5-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "df4cea77991d348de966a921db2a2ddd31386f2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-xH2N0pH8My59cGK/z8HmrDMupoPuFUNKDmq/PSQmTyKb9RrRaBZaCovUHdXrqndr0ry/yUDPoJJ0OXWbijzMag==", "signatures": [{"sig": "MEYCIQCAotw7v6bAFQjQytRZLBcyUzjPdTB0rrRQ6ipKlwFmQgIhANj6sbUELDL7k8VoNOG60skARLEBqtd/IA1bGMcNRSak", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SToCRA9TVsSAnZWagAAl3EP/2ldDJzAou9pAzFZ97zE\n5RywtN9GvjCIz1n1Cq2OsiIoKzAeHDrj8VHwhVjP3Xphj3EwLylAkwqOQUYL\np25P6iGkSDcGppE5/ty7NZmP2s3QY+OAXI+I+ekXYwVoQoC85aH4NUX9I7xp\nEWPC/kLPgrkrfyhRYKdD1RmWhpXa8Gqf9nzkLlGupj8y88uCKTlTWQ3qpYCm\nqTORBHzu3fMG+IeCmZVHMLN3Ci9Izwuv1vppHdjOYANTWUuDkFk4elrE7/lW\nVQCuTfcZqXONKxm0QWDbEemR1F49DhNXDyQWDjkRIW4KPT9oX4NtdeMHCFis\nbztGZaCcbs/En4CMUvEZmEQNKwtK+Sr4AMfF9m13OcIDRACVfMKjTbjm3sKG\nQmXaxlikES+ZiiONJ/A0fcRrYVSWNbjSMoUaWQIN8PhiPpqdwO3lA0g2Kyhk\nbSAsUHBrtjR7aHr3tn8Fk5myciO9b9/XlU0cU8BQYtw3lebW9N7ae8iOAfAX\nRg/QcaSj9lkKuX+CD7IpYQoBd/GUbT6X7KMBqM9NjPPSXtO4rBgpWixvRkqX\nFI0UC3IePTzwXau7/zk0XsawlIjsCISc0kvNU8v6PSbrSOBsCou+BCVmlrTa\n+XHs1gNaLC/sAW+2A07uJ7zBTh9Q+7HFwUctce6yO7Q/yoYh+YMX0Sxiyl38\nb94/\r\n=Z40D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-roving-focus": "0.1.5-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17dfecaa4842d47b7077075dd7fe2d264f996a6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-l6K3i5zG0pA9j5i1ICwgmHHMvc3V3R0t+6hWPqqLZ47SA6gTN9xr8d5CRj0FJ9bEZJMUjwNLZxfgJ+m1DjnBfQ==", "signatures": [{"sig": "MEUCIDmOegTFUWCBozTzEhEk7Qa2a5MiCOLU//Tuz9apXaU3AiEAmOZCWd96QMrSPPrQ2vH2uK4e+EhQBX6S8254df+HClI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DahCRA9TVsSAnZWagAAUd0P/iybxUgfHWkXvSW4MVRH\nzW50ksTKNGxxXFRde35M48B9uNRSU/qMdIzvBOH8R9o4ejEiNzZbhW0cDgu6\nolwgKb9KXmVs4e1QDBUQC5frDgRjrX/dWzbkhY1G4s2Czbyl88Jvh5Q/pbb2\nF94VjU+JupFGT6MMwKlOvXTfdy/rcZTmNocPdjeEPcmFoXoJvS4AyudIcjAT\nlzqSRALJTLPlbdVReGZwMRoXUGx6MeEX+gXRP5qF+sgHod+kahdA02asHpqJ\n5A5wLUiECV8T7Gmd2WAAxJPkKsTzjegbGrm3nnPEpMpj45EM0VdqIyv85v3h\nPECzthPDyijndtDWnv5S4UZ9qjd38we6rDF1P02mB/tljDS37sQjSM2bmUFs\nV6C+HwUCYAJ4TC/bBT5Olaq5cpo5arP/6F28N2UxoIs7/la1tVKIHcgdvZv4\njRZYhCbEntXwNFVIpuaesdzeAcbF4GlC335zPcWS7iMl9oU0NPMQdPhMOreZ\nHpxySzj/ukDar6iWma96/5eJUqcqkb7MItc2AC7/jcsoUs28eJpQqUOj1GUS\n+ni2tjqJJRO8ddvC3zuMdmWZwVgodcbkskCPNa+J9kplDr3OgWTicoZc2TeV\nIh8XTBwkZB9FJd3M4+OgzCjoMcgQUoer05RHA/RRAnAULL7k79888q2MIeRb\nQGrV\r\n=m2N4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-roving-focus": "0.1.5-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0cc82d216fb7f53f7679f0f793b47bd638b0d172", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-xlBehwmVrHMx44y/2sgHVKK6/CrZF6qzch+CjRCdB45MeAeAWIDMq2OhSY3QTMyYN60pN6QIvAmSTfWVbOnM6A==", "signatures": [{"sig": "MEUCIFXpNlbqTKq0VkG/HGx+GcC1HD2iqQB0qvvf24f/HULfAiEArpLh0RTPzXVms2KsusDkIeDaUePV+IwFCSG/P3n6nm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WoyCRA9TVsSAnZWagAAvJ4P/i66suET7Usft0bnaY77\ng07WRD78nrReW5OpEbWp3TV7INYJhNxkUKbA7h4lkuUeMEm9qdECiUH7inRh\nXYd4NRC/31zxiHsXgpJMjdrAJeoN8WG5BhLbDTuoj50HpfphJEcEZyA4LI9C\nPauhzr8FMyTsjFENFJp/AZ9jrDhtZl0G09zdIOrv1CF0fSZZW4H27yS+C8dz\nOW6w8oSAe96xz2F1vN+FAHg9IFpT5fm4Gog95lUzRtGqJ2hOa60kByGIlsDS\ntBdrxugz+qd1AGteINCI0vC15t0LLUJcwpWc3FoZdPuCrTkRdVxJR00zLW0/\n4UDtIDvcdGOrItKzB7GXa995oXaSkhQaaVLg+9v6i77JH81ks5cvdRrjpXTy\n71gnA8pXVIi+QrgL9b1htI9qlxVjIevYP1NWSXxG6pm//ItCQlZNWdTAZDCp\nsyKVRmdD88VZ8fLD8xCyfV3OVhcv35tmStP+BDwwb1U1r4OcuAGf4BFwwBic\nrqOC3gZjQrvfA/PwZIXEdvmx+pIi+p7E2Lq/mxf2TuPGWYp3f4/5O9KWrp60\nwE769tsOzSpynk2dqIFaLdOYD07Rzmvp+jUaitxAijbBxfVbt/va9MztSFqA\nTxn+UGIY2BG3dY8iqMm+zea/K17qa3xogKmuT8xJwaWCpZe8VcU3dgxQwlqY\nGtyh\r\n=6rfS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-roving-focus": "0.1.5-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9be314debcd8c0b7f2827e4d4c675ec913ec7a22", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-GHzzptPefnqH0UYRAj4h21R3Ia8tzVxq+txhucGDo0eA+v3QUU4Wi7ylPwHU6s5eE+9XZq73NxzrYcupUkHgFg==", "signatures": [{"sig": "MEQCIEbnleXTkh2ubL7XzKvTd8T3UdFTN/nT2Q88XbU9zTZQAiA/ptLLPUpRaqOomqKRvof+x2A7Y4CofNTap7hltgeKvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rVHCRA9TVsSAnZWagAAnTsP/2S4czbSZ3qEUP/YW7Xi\n76MxixU1rS2ByoXJoRBqMYQCWV2W5mcfl/xV5rb46KI8AppDAuS61fQ66F3H\nutuQiwn96jFeeMVHBT6/7ZIE2PLN48VUZ7qvoMU0QEH6Q1phh36O10CieV+t\n19iSJrpQl/P47VH7yIsHCbaiN0gDXCTVZ7df4WHCpj1L5FJ40AEqBFd5l6Ee\nni5y99SYzNtc+5n24Di+FdSg9rg0Eq5iIfkRMSBQNojdi5+5z+bmh6uEULJJ\nYtdACl3nnCE9yzPpp+tuJLNO9A/jtg5Rk+x/rspMyOV1oSJt/M2huyoC9U3w\nyWBrlq3QSeyf8OFAVU6y9MKHRT0mnF72nklLfbVcJVWYZe3d8cRAHXgIzJSj\nXhBctNxLCSWtml9Tqg6cqQehMkWpMhXdCyb8W/ATNt8IDLrYH/KGA1xXTHp2\nXSoGLdJVuiNQry/ndOaaGGR045pTeVsqR3rye1ukkFMCTxVk+ZCkPd7FVSRr\nCectOvgWbPKPkGKxt2uvZL0Zps7QktFdz03IV/Rf+D3tBj2h/LU1t99gOQzw\nTU7OJa8M7d3NBtsPW5LUgXjp4pYbpYjRk/wxvRyj7SGGtcthS0StKbrNOoTl\nueavNHFLYQ+fa0bEMwh+OynwXlWoAnHYV6SadC7HYMopAAZWL267N9UTSXTW\nkWQg\r\n=m2ZH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-roving-focus": "0.1.5-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5cd4ae522a1d125106a82b072efafc523cd8e52a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-R33eZ0hVKOqZ+OSp2wEpaIISYVTT5SCa00rryraCigfkMQhGUb9l5Qq/0c3xkg2cbu/pDi4GZLrsl3d5JZbFiQ==", "signatures": [{"sig": "MEQCIGm4bNI7HVCK1+iijbsHZpGT22zIisheXVambuD9FnhPAiAP8GOIfP5HZW4//wTqZa1Z5raocvDDALmoa4kmKdw+2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/oOCRA9TVsSAnZWagAAsnQP/jhCZ7ohNWVeJZExATcz\nQe2xn+gC5YHWhMg9x0jryt+EkMGDfIG0Hmm0SgVuKZEhgG7BPJr/U5yYKq/O\nylQxl7dTnqacozIVn3RoKIQJb/2xexjnxjDFfp4j2Uecu7Gf9QdOfOAPKZM0\nYe8jFf0BR5HAdYghhZ3jJSpiZhQAzYv74erq7FMmPJ0lJXVN/RoaePVurGH5\nLjK8Kim2l2wPnZWw63/4UNEL5fDbQ8HDR1VBnKjNR6blw6jKb9Ypig2abXGy\nz0zITPe1lLWapUnt2gH8RDj96gpAw5zjVacg6jz5m1k6z1wibU995Be+vYWo\n7glzOJu5UBV//bIg0zABsHMRyKg8N5Nn75uAPb+4+MukiJdBa+L58ra0mgq5\nojgQMhONzTDnWoGTLXhD9BjWd4ZasJe9uZV5X05q/3Ne1FbqvnK3anRJ2i0G\n9EYQ3e8czA15cPIOGNn21ENz0t0HZgnpi3p4gui5KyXg2WnWSXCH5srZJGCG\nJ2nd55Y5IcywkTT+ZBJEKTnnXpGEMdJEXVpiZhjtaPyhqGD27peAynn/AMBP\nw0EykRNB8FPF4esLXkqinP3HyWhK2eJzgbzRZkirt3wW76mBBwkEmt/m6IO+\nJwOxICLoxGu69WpCclUrVhfT5Fg71/4Se06qy1f98HsdEvOVb3gWlMfQRzC2\nITZL\r\n=thYe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-roving-focus": "0.1.5-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0474e83eeb4b3c14549d640108c92e60d3e4bbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-lj/yqTfj8SI2lHhkHKEatYehnTQAZ28JUMVv3ZiPyujzBazukDZOX3qkucHM54TUp7MDmPW1QbrV62sMkJeWDA==", "signatures": [{"sig": "MEUCIQCz7xv0VSn7DpjeraVevJUkHOwFvqKHNVHkzJ/K3hKrMAIgHxswC0HZJan8gsXZmbppSYp0bvev9knceO44v8bq16g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIqCRA9TVsSAnZWagAAQoUQAIzHiEYxArMISUfPBd0V\nR3aBccfBR03XY4BDWOkODeZ7rOx73ASPNLWMuwF9Kef0tZps4uD/9JnXx+8d\n/gvIYXMOEi8+8c9xtwv7/P5sq6H3Em1khQFbIi2hyJO+ExznnbaKxwdN2a7H\n3B6gTpUpMR8DyBY9aNsLayCZZkHBFnST3anhZDYxc459qIL/4txxivc9YRm/\n3ASgpZg6R7SxXuORssjRa3ARmpdd/VSjHzLRNLuxUCV/kwbJ8eduKgOLksNR\niO4wqbG3BQlMJ5BZ/NDnV1u7RZ1pb8oyUwNfhRao5Rexs60C/TEB3tYu8Vhf\np5q2I+oPZ6ZCUV8oZtyyeiT2YO4E/8d9Lmg7ZGYo4H5838Mzk30F1U0rJAYc\nQHi4yeclWbDN3EjponEeOIzqfHS2BXLbBjT7+Thbjps+a+V5bLGI6kVBvoci\nmkQJsUnFzGehWzAOQtxHf2Oh6KZdf0J/Qg7UvR0oSaY/5EHboZGZeEbZhVUP\nCBvi5e4vTq3hrZYBOsjUhz+iknn5BwVADxmReKpRcSCMmgFtCkYyTVl5RiYS\nK2Rc77tL1LoQKqOeP95mFwynmFONoqKlpUTW/jr0VquSLGY+L2u1BzYdwsbx\n5zHAhiMuXzeOkNInNOGUCVtFPGghjqkbR8iTscFqWrfskIKUQYOwHWQpKTyv\nDFwT\r\n=yWVM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-roving-focus": "0.1.5-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3f14e107383ec4cc6a283917c107d5fe9f60956b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-QQsV2lB7DJ3WqmNbNB9sXxJ6awBLGZv+Uks9pZ9hhzBCbGYxUPpaiDIw0y+XrO5pROCRFLMwvm7xuzxR26ZlKg==", "signatures": [{"sig": "MEYCIQChzJC2/xv2EF6KLZ1ckmzyxIDKex8D6FmxpfSvY1QI6QIhAKkz1y9ha540AT5/hlCFTXZ3PKIG9+cTIFEru8tE9G3O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBY0CRA9TVsSAnZWagAAUGoP/RbgK2WPus+2ST8Sq0B5\nalHNYzvzbsYUNtR7+xhD0ft4+NN0nFzt2oh/J7fO+cBya48u+4g2RnmLyICM\nVpQNDDIm6nFi7KVNWEx6+YOkzCtmdanCKevtQQVohR9Vpw0pjupKKcG2Dm6V\n7+CctvtGkmhnAMWZ0lH9QXui+tBniUf+yFYLnfZCEu8fbQ6GZySZFcpDGQoB\ng9F+pSW/O7kq+Epa+2EUZ4smJniP3m3t38f2JoGOGId5laK27t6kaEGNQ1qb\niqQcYkeswgDuzmdzz7dTKzyDwtEMYOCFA2DSUBAEdrPyeZigTwPpd6y3NV88\nTUsVy9IGfYRnlSTkyZyBeeS2Km74o8aUJKkl5D0cXvfXJzV+Luq072s1m7XS\noFBk2BbnsZKYsbj4cgzGL/Fh84FQYC8xMed25d8TZn1tgD/wGbcPk5JRXNDB\n5HvTDAxDaZmeejmjgR015FwAY8XgVa2fWwXqnYZ/3MxvGsH7+aS8CtGOrqq4\nFrjNERAhplxy2LOexIa9Ea8Kd0ilP1eFfjv/Q39UeX7j54+xFRW6CvhKCS24\nPbQZ1oea8qzrwqsSMFW038NkK9L7JZp5lwu5hKtQD4raIIcL8BdIGl0L8jfg\nNfPlPAcWpQadL55Y/DJmeHTScbcer5nTssjTHAAZnpWu2y6QhzTc/3nVQbFi\njpuq\r\n=p5ZY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-roving-focus": "0.1.5-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4cc097936e5f7ee57176279d5f003ef43ca4b49f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-4n3iYPo1QD21U5EWG0gqJ6rJtn+PEJ7CjWrLMCPmbWOzvg0O0h55F4OwW0JWj/CuTPW1sMvjyHjS8PaseW5GBA==", "signatures": [{"sig": "MEUCIQC4kk0z9JVuhibxmTsZ/2i99VNiTayQSOP76IlJwwx7kAIgaMfg2XXLFQnKkINGSWi/HrsJCh0klQ0ooEI9f6AILOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoagQ//Th+YblqzW/dUmZDLpqDFBrFTcw8K0uwVgtVwOIDpShCalM3Y\r\nvYPrFJdwkhHSMkPjM9Ucmw0X17EwbBkHcWh7FfPNZshJu1cFUdylSir5/T9m\r\nXLebUB2zymUiM6YvE84WluHSUA2bUhmDWIXpp190DRQ54TTnWb92hJwEMgyl\r\nJ5T4ag/gqYsk4L7CuqCRyCEuF4MA4IGHTE1sGZPrON/0XX8BwWN/Fxh2ZLzi\r\nXWe60sVezjGsY2cmekDRkCKFQbirmffT0E8hhOTmzXrVdLrha6Gv7DYE5hUz\r\n6pQHQrrw0FTAcHp7ZzUH/ZDHl3dR6JW0BKYUqUpsDCwrf9Y/8JpLAR6hayh0\r\nGGACAFe0WV+aAaaV4WwrRi7qZHrACa0vqcnWKyC51lTq+bMQHY/4eJSrYXVJ\r\nWVCppqXFUUTdREMSqnO5g5pfErqyI59adKm127jywZHap/ASkvrXYTictzNt\r\nsaZu+nIlYYcnGESMX8E2bl4TgJJWR31+glx+cg+CXFOta2P0gLCrWZPTRU8C\r\nA42uZIcB6q7PiY9A6H9OPL076aIMnls/zbRe8I4UAUgBcn6mbamVeW/FlbRb\r\n5z7uPcE2r3xry++VckhrHLaTBlz+fUwZ8ce4771XEnJJcr3zOVF8isBFuNHg\r\nJewQFlppKYjRr0+5idMPYxdrjOAG717Bq1k=\r\n=oREH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-roving-focus": "0.1.5-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "73de8c15e201d399cc6b4981f15d0a32c06ecbdd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-ozM4LNDeUJkAQe3LBB81GBC7N1wisSRU6kxtcbMwrxLXQuH6bDcQ1zMcv5dSIfY4fpGl6UiZ/3igj7WqTqrERw==", "signatures": [{"sig": "MEQCIEqNub/CERQs9IEdgpXC3J0dK7xOQUvuYUmjJZGbz8WAAiAAkWyEnAXkoaYwAauiw2if0CPEz/ADGXBaVAxqDUu51Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOSQ//WT8lRm/er0rc9GFSULkz/1QYieiM2KxNr9uw9xgN0WQTH+L7\r\npzJTs1pfO9Vs4pYPb6YTTeO0UKob9Ee4idi8EnQ6XX5qH5mUUfApO1pLS0Vn\r\n53TflSkmg31kqrfQuFiK582/n0RZLHxcbUWAxFlu/x79Okjl2mhO8GqJZHbA\r\nBzkuK8deNpPNoXDARp1o0Dj8JhQ4Zq0VV/A5c+YOTM9WLaoerLxVM8dRaRth\r\naRw8+ktVTEdeEZ0/RV8fJYnzH84xSN+rKmivWgAmcDrEfr0oEMNhdYspwK9Q\r\nNkPKM4EOTkmn508lGgxL28iz4fhTB2Jxkz0C+1vk34d2PbD5bHLkbXuwiLPc\r\njWEbmGtxeC6qc3VkUbgsNALOI9mVmD69IGqSm7ScvDqJtArseHsXFL4eN9/7\r\nta3iSs+0crVeCew342p1MyyBFE/Vhyt+9uteoy36DpHi5IXkiVMsryUxV9Ej\r\ns3zrqjhfB8mihTYTWUw31VuTmKP3vAdWR3CXnGjcxx57zuhVv56v7PQG4jsm\r\nKIPCilWMO0xvGiRHr/ASB/XwSFlx3vJV/YGYH+LCuNl9XSoaaV+fygzIIsao\r\nzYcX5KBZCC7eTdsUBWREOx1NYv96Xhx0XJC+WnEPvnVrsDcMRaA1YqhL/0oq\r\nMnHC6zQPacnYIXU4aaGKrJDoRKPVnzTtik4=\r\n=fffc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-roving-focus": "0.1.5-rc.20", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "39be897ad0199c40c9c2a4e4baa5d40da2e8895e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-2y53zXVgNSnEo+Vefw3GuC4pKAe/e7UIn77OP1CqGjCOwamHhMM4CdN7p9negP8zM3a+ib66k/yq7pSc7MVFGg==", "signatures": [{"sig": "MEQCIBdP85V0mwN/snhE2NaLcGr+GEG4mLGp9A6qlpc0StR+AiAMvVomEIhGJQf8+eBo6WIQCrbcEYGyLYAxzWMslFviNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpbgg//eOQObD1fEgqXWvy48MVFBDEqLxD15OEBhWgqZHiqpVzADM+C\r\nQtRK805UZUx9SzvFUxthkawLIwPoajWPrlC5nU2AgmKf3CPv1MCk/qaugwOv\r\np9lXZsuiqQvtZGV9a3N57otkHGIXCTphM74oe41wGmfTEgwqplRbCKKj/YZp\r\nc+VK3LAFze9/4Uccb+FiRpvhgZkuf7ztnIvnKKO2AgHzNTrbyOP0FCfYakzt\r\nkjsWZ6BMFnthH8UGauf8349BQlAEcBM2QKgqGED7jMD+vuY9ZifiKJ7cql3v\r\nQkG3EBQxtECPMbtEvo+DUp8goROO17Vz+IxP3lkCrd1kWM3jm2H0OxKPuIed\r\nxDd2ChOJSsBcr8hfeV0Hwd14ccO5c+F6AV2kDfbI0rt+ZWW+X4kx/Pvcp4rd\r\nUWmgodSXqvtg/aKd6eq0iqqNjhxb7IfPxF6Yh09f1JzimWrxt71VVaDbggDR\r\nyAgJr2K17H/5HjqO4gyxVZ9nj0ErdrU0ccapX2Ogk7qmW0rtGoOzcKJ6fJKX\r\nXQQ6eB5ko3UALSTVfkudaAAZQyGD8uV4OVnrhStaeXkCLRKg50CJi3duxDx2\r\nP1Qt/qpKCOV0LPOPv9tn/V6HJ369q9BdSh+j4uHeRMz+hjz/rxEtClKeKNct\r\nXy63xkuhyLSEpWTMR32s+l4MQTpGuEgU84A=\r\n=X5hh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-roving-focus": "0.1.5-rc.21", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7a2608857aeb71e3628a2bfbe6e827fde4d82b1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-VobBmpccpZ2LAYze9soUgaspz1rdZUOssB1ZsEM+TTIAxpSZIucL9WMHeCqAiKsy9a7zatl3X7NlgiiaRybPUQ==", "signatures": [{"sig": "MEQCIEQjH+rWS3GW4kme8FiIMOphynsk4PhMkhlKRiBsRTmPAiBg9IvEVfXfyOj3zdundJ4M8dmkoGvh9WNW8+CsvV2vBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8xw/+P7UpVdcHS+p7fuzvdDwNReoeXQc6p3feEFctNc7g5vrBXYgA\r\nKZJUwW4OZu+RdoWoKGHSnMZVD1ZBulmb5LayShmVoKXqNRhIOHECLrZ3C76T\r\nCPJTI8yAKHTNtcVNZ2Vk57n+MM0bXxGAROG+vUY8AkN8TApukAEEEo+c9NqS\r\nRoirqxzzy8BZZ9DxzKe5P9JS1fqrE+5L2bOZdHS7RWAbj46HUBSNhAggAgiH\r\niRUt+SGxMwylXU5zAswjNB9mdq48fAuIdO4J8jZnZjfHw65PiiSVRgm94WlS\r\ncdVyT6f0NCHj/46g7vYdhiG92XQnCyOQ6OpzRa9a/VGwgGkidGpDaTz9YKar\r\nu1wZ1ykBGxA6zmUZC+KSmdc8SVPiP3DcW24A5huGdrCRJDK6O5Z2eTsRcQRn\r\nEhBg2GADXj5c0d5uQVc/1MYPmUEPGxfY8amUrls+zd3Mav4wWqkRVhPH1ohy\r\n8FEsh4LMvWhI13TgUfCyXpk6rYSpudDmmstzv0SblLX3VCeaRuliCXcHj2sl\r\n6OlObCF9DKeLE9eVcQcFrvX3Eqd/MF+CjX9rW9vdzDaAH1lMRRK7J/j6Xi4i\r\nuObiedQNUChUGHaiCZArv4nYrNCljHfYJf5d+edL4Inp7Qy+n63NiepUI/bn\r\nSpZTYnDWyMVTa/VFTkJF4y7TYhGdFVx32tU=\r\n=g5xk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-roving-focus": "0.1.5-rc.22", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a8e361e69ca52d7fae5f072e90d76da4eaf3a5f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-onIdCHep7vccR8s05Xxh10arrn6Unl2RbtX34EodAlDyK6SVkXWJ8EPvEVboGp455rQJ2GhxpTQHIl79iclrsA==", "signatures": [{"sig": "MEUCIHvHrYrJW6FrCyQ7ThEVj/lewJZ8wYgJRX/gTds41ZtEAiEAyf9YfH8pUzrrfU/XhJrLRWDvLQhcDC/ZZ4uIs+HZX/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDgA/8Coa9t0DlAOfBkl9lXm4msOeUxkQrNkWFBYrEwbxRLyzhYUIb\r\nO3cWQAZsOtLb1EzC3+/Io6Aij0dBdM/DYxSoRaeDii7p9pSRThVLVpm0Z8nt\r\nM+p55/lcx9civHn0Wg9tTD/hERljP+xIRYMjgv3WTiLv43X64NUjl8ODJhfY\r\nI2Dxu/QGFgSY1IBLoDcGfXmAoTKJwUuccxZ+ouh5ESKhBKrc5Yue+wN67AZ9\r\nm01cW80VvFRQ27gOqZdfHCCOHUwkEhmVGC873Ct45Iiv75/Az+ayrY2oVuMW\r\nVOlrPzRmFx2QbrO3VgRaTbY8mbTNXklwogFa2tqjNbI7I26G6nHssig6xKtc\r\nRAFhL8Ct0Q8oF45NUg1VCTVN6RrYuI6uikWoocdbVpKfn2XeqrK9fWR3KjFx\r\novi+6BKotmKFeuys4vHziBGCng9sqKZDhTZXpkajUDN/sT9bPhRTiUkAyz5+\r\n/vHjkY00JoRXzm1+JQBrmI4HJ32/uJzNwTbUJkP1n57G0iTgQWqZBxAQJYIf\r\nbeNXoRgQoBqhTtWWmeXOOr+0mfcmWsMH7SYiDTxiy3OA6Cuv+iYKtnDpz/4a\r\n8bcOesBU5NcHryc7uHDxryEKZUx8dWfkLDX2gf/DApRmJKstasOza+QsRoM0\r\nihzLUVyrhiVHZfRWMdMaSYscytW+A+8x6Q8=\r\n=m0nw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-roving-focus": "0.1.5-rc.23", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fa11dcc9ae1a486d41fe8e285e467872b21afbaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-GwLk0mW6LjBqZOO1ZxJIBQxD505++cUsylWoL8b9M4haYLRaASsfYX+u7RwsNcWHVAXUi+7hxHMMauiuRoF3QA==", "signatures": [{"sig": "MEYCIQC/EpDOnmunbpOIDEQv70LjpIYs3DOTiQT4F7ohX/bvvQIhAI79fxYQmzBpA5AQmPnhTZ/PXF11atjZK/JIJZeDR0Km", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpENACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRlg/+LM9bZB46o5VgElv9sxnG/+Sfto3sy9y0xghOk9FUIwR6j3GM\r\ncb/vjNH10dRurgaerRBMKpN/PlwZ1sWfa047w2GqedaSJzT4xn4qCs4jSFeE\r\nn5xYC/6aMO9ZY4qzPKe3P4KPmfYL+25FraglsNuJsvmwnNoo9UyEHrOBANe1\r\n/Ey/CDbiMDNmiE2eQweXs59y4RWA+Jg8ne5y4Und5JzE11cGSgddNWSKoDqe\r\nBpwzeGzpy2LYNNICHL1SFOt6nNgpF/m5k9KYZddeyk6eZR5nGYwXr3k6gZmu\r\nPJoYMC3OUdCk22hqmc9N4tnzyjAAQafQ38z1T1rpwNkY+vZ9m0gaZrM4GiZd\r\nQGFBhAPfjR+bNfLb3dCqvjkculNxj+d+nUCU3GXH2hnc5M+Ie/HTX1PXzaSM\r\nBD72a8YkUmJXbu/pedImMcsKkf/JbN/0cztm2d0586i23fCB3AJaAMz388JG\r\nXZKEPN19EsO3yQ5c7eGOdT7zo11Omb5R4EGyd5e8y8DjtOPcMkm2uoEP+Whz\r\nUmrT1ujccAqFmuQ1kmaBlBSBkVhFUmNd9AsEh+ARuY1DVeMP92+UjQfdyKf4\r\nxZSYitslJ2gg20KeLnalUPLVds9T0Ljh8T5e1YTXs/T7HYq1BXSCEaCJYZcR\r\nMOZp4P0e+vyMny+bwsFY932lubkM1dodz8Q=\r\n=dzh/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-roving-focus": "0.1.5-rc.24", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8190b0c1ca1b1b776d0d175fda9eb1c138b834f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-jHQ6RYNjI0Kxs99lXzD5nJO8FAia7RhXUfgL6QCy9L1HTzE2XlVe8PTUigjkU2lmG3qh8kzP6kvosXjeT0458A==", "signatures": [{"sig": "MEQCIAh5lSUB9ZW3Jw7bbKNxQxPPr0Ne3AUeUzBkLLgz8RlqAiBxsplYtvP+nsLIboaGm6bU139Hv75uM7Y4GItvxIyRsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhihAAhX8M8FPEoMeyYm6JOp9ao/fJlnBNou6v377rgDKSGGWk//tA\r\nJ+3EmshKJEZL4AP4SQROvZt/H1IexhWUwwGYlIVZg42ybVjA/u1dBdPNfSzT\r\nM7aHczunI21yC/lgfyw8Ai8t8pvN2vRzLj+hqE8CZcbMojp88UI+vvjq6tzM\r\nwYv72Z+LM7Twf4zBuf667kd+TPg3M+3aRvXt2pyP7L2FtYSNQuVZK7iEJJaO\r\n4VsWqIgKrUpaefFOhsh6gNhEvKdAAhL2S7RtZ0qU8ni6/rmMBpAYc+3fnXba\r\n9ubVSAJRuCIWgaePG9EutBS/WL9uRLXZtcK9T31CQOOyJfDprkKPzF2m7Prt\r\nSIM5RE/n2xp0usBvFic5okHIsM+v86TAk+hq3n9dct6Dq02jzHJW+GgcAM3f\r\nNH9ybMV+Rytv46EBkXLp5VUYtHi8D+kXT0PVXLY5+dnQgtcle0/qE/exqoju\r\nyBaIERT/HnkJOwNdh/PvYRSoLOqZI3m+ZnrvhYXidkRdQeIVkreJnVhNHE2f\r\nsQjLtL7vw5YJEP6MWfsDW3F7clnAF4esriwrraFQl0uxj2Of/J673xT2gorG\r\nClK5YkvEYQdbpSRPLKPcfC7PliezkyGIxE5oTa9QbPxaGKuHx5PMDZfXyYrP\r\noEEwNDY+CsUsQgRnHtUNlKDvnetAXDBi8BA=\r\n=3ej5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-roving-focus": "0.1.5-rc.25", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9db013df6ac909f0ce981de3698d4813c8a3fbd9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-hUndvxrJahaJk625b1j9XW0N2Z9UL4BNlIRrubepkRwPSy/vaoCvOZGIGuyxEc+XVi/V+A4qqYnAtJrVl5SN4w==", "signatures": [{"sig": "MEYCIQC0YX4W8m81HDRFqZyEgssaGjTNXR3yD+Qi1Ly1REatYgIhAO8bAbyWXByNaPswehL5SuJKLnXYde05+Iql59lAm8zh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpW0Q/+Id7TbGBA8QigKIY66+JkNmvfpva8Qvd5zuPr2ZQdr8MBj345\r\nhTpKiOfocLfexEQK0V4Tl3vIeLK7WhjSGwL2paWFUuBEY9p8RdSI5h9C8zxD\r\nAqI5BAswrkL4ulmZ3jHhvYExJ+YOG7H8LnnmuP1URNg4PZId4lkSC2RvVXrh\r\nEyBN+RDk5rhsEvposaKbeL7TAL3UHrTvw/bz0B+mwfq6fhBUEYHlO9s3Uk+k\r\nlTqSmxrFG2zf5LgRB6Pmnp0uUKIEhSkZNJFO4hqpjMhieqUHsdxNBRqP/fx+\r\nTIlfNDqs5xj10gWYcF3s8TTTElbT61yyzU7Qe80e01j0WP3mahhbqvuYkgr6\r\nb22AAIO9xL2lP/Iq5q5pcNZo1wdv1s0zsDNCOc7dyp99pK88YaBSxFuey0RO\r\nulj2TvsQt65PsrzE9+0xsJ7xIEvNQDtDCqfEp2+Ds3exK455k6j9KW/HK7MP\r\n5OlHjmfPAi+QtjBOQRPfE65lFEeZ5I2Js3auAyKTJ4vZOetHD8kAhBExtzn0\r\nnIuj4vhWBFMSvbVw22TKA+oxmfuUX5/NqVmz04EpI87Ex68cUkC5ETcyoIbC\r\ns1Z8xzx8PSao4aoUUztcKaBtTjBdLaqhm/knMnXiForPoBJAnJM9D4sgGoxT\r\nMXNO+9GnyEKRAGXjosF/Chnm1cbErcEDAl0=\r\n=tnDs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-roving-focus": "0.1.5-rc.26", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f4f3f4a91f0afefae744bac1262105c27845fac2", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-+PsjMCzt8fPjg2C1WpyykKUldIRjzcNdP9joixoKxmKBovcU9vpoaE7cxqqRjasSyUk1tLVGuAMBnzGMOEAtRA==", "signatures": [{"sig": "MEUCIQClQGHuB8tK9ipFrFSaeOUgimpgX7n4YEQB8jpBHQ+5BQIgfqdF9vTQZ2QDcPcOTCA4E0rbtmlgEZ9G7ni3TBvH6Y4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8aBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEqw//VZZKTdR5IwXjvDl2tfuqI/TGW+P+sb44i3UvSp4OMRXnVllZ\r\nN4Nn6W/z21rgdEX2D+uA1iyCFy5CxZ3WafTjdh3yWUasxtlDMt/t8ep8lY3J\r\nMavtny20pgWIu4aj0DjVGNRst3A5we+Gq+c2AD9wx9nrs1FgqvBYkLMqhyH5\r\nyHiiZLmA0SqkTu1TXsRtPZ6m9Pkr1LpSCBabVfc2Do1gJPrVfzjtLFLUL+4p\r\noOgOCeBC377mCRJUXvXz9EB/9i7msjNAz7smbSKEuFDnbswIfrJ4IZq3fKZc\r\niFL/Cp3xvKORG3yFLU5rXTMnZtBgMzrzttbAvkTu+DPd43gVdKKxwT6IAsOE\r\njUYNN2gJdGHfUStr3fHpbY5iCD5Z5bEdpGSUz1Ob8mft8F9m5YN/PLqa1SB9\r\nwjQUo9vV9OQzGlKBaeAqhrloAfkMBxYS7mfxCujvPCi858iC4avq1OT8LX4g\r\nZYwvMWjmnXxYx43fwXleD/SA1xUXHdvF1Gl5tzmlDc2AHKFMy2Q6oqrrPABj\r\n9Djk3v6SeaWV7TuMrhChr2mTCI7CSNfp0/kmeJ4FmML4snDquFvSLuYkXo/9\r\npqaEDQjyWKUXaWv8Mfsk36Es/q/GSPgnIRR6S51MFCV7a5LZJCKuuRQHoJBu\r\nFEmDdM6ynyTDmZe5CLVAtqgRSsgX502a/H0=\r\n=jSxy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-toggle-group", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9e4d65e22c4fc0ba3a42fbc8d5496c430e5e9852", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-Yp14wFiqe00azF+sG5CCJz4JGOP/f5Jj+CxLlZCmMpG5qhVTWeaeG4YH6pvX4KL41fS8x9FAaLb8wW9y01o67g==", "signatures": [{"sig": "MEQCID7BtXuYW8KffeKGcXHwRPEVnFI/378eY9OjbzXtDKEDAiBswl8btjGah/600xBBRMvuAKDnmweGeGj6OiWUbOKIEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8keACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprtBAAgJSQ5uFgLDj1oOgdWr+s2+Fy2fmhsdYH70KXMpFKPzqpovUE\r\n2FN9uD6Vh2pKj/KmjnjKF5I2aKFKZ8UVvN4nJUOO8bk5FDsKbl9OVqKRZFKD\r\n9/JlLGoWy7CWSqCLrmYr26gFsfSHSJ+F9WRhefj26Xlc+0E5Sod6f1Vu/3h0\r\n2i/l/9nOEi9/WV/PuJoThxA2jeAAe2PWdl/d8IBGNY1cxbvsvuRJsArQP/CA\r\nn86C9xB+SR39VI64gB6mKzgktuZfd6j7t2Xuwu+++xCRK6ued0ampgNK54YN\r\nTqXvyZfRi70VvEPUJA4cf56Rerz6rN06gPPWMIUhG51c8gTTvCx0D0dgmUQR\r\n1eD+fwF9/Vnl4AvYMGJBK+l2U2SsOeyYmKKp+/exYoATC3CDYzBlM4MjtU51\r\nJOx+kZnIRkyf54lF42C2YsRqyX4qhTim9WAwKA+aOvdkRI6atfBee+uEJ0i0\r\nHodyGscng9vEoNd/HAKuTwWr48N2wXhJy66dNopnCLeKybiY2zUr8+BBLJ6D\r\npt8M6MSo4Z4rVID9fHLzjwo1krkY45TQgyc/0RKLdY56ozPe518YmDBuT2kW\r\nSat7GMaoFAY8HJy35DJVIQndeCWZLXQMjx/49VvYquxzxfKHn0Y+ahI0/zu+\r\nIpy63dfH+MVTqnh9VdGJKJu9VOkfTmyDrKE=\r\n=peNL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4304ca59be27212ba59036a64377a73d87a69f79", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-J<PERSON><PERSON>31esSUCKqWd/TT35PKN/OKEIiSlOSLcwuuatG5j0HZAsXSqL/xkO3QmyT645ZmRScQBC44C8DcAu8uMoSeA==", "signatures": [{"sig": "MEQCIHjfFDfsHpknoeLlczS+JR91x8aVwildjO3IL15b+kNYAiBea6w37ue2iTDg8cpMPqmhXZZRN09AveOc2Fv98CbTHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD7UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpP0Q/8CUFX4Ku8CSsiOdOWAZtGuDeEqJ3QRiJMVVeDHms5g7rhX1wf\r\nV0i3d2MfblmPU1265yXTvxROxhzQGhqPMvBUjzQ7hf166Fu5S1sWSfKGU6Qs\r\nUbuon3XBL89iy7x1yAk3LBAqMr1PwYhQvi5illGM37/B/h1C91N6/JdplhZS\r\n8tqg+ApWXyLAgTjwJdlvZ0110OfqEm6H7tMj38B/z/OcWYba3AO3B87ZZh8R\r\n9m5/ZPMSLGmim4GQVmZuH5Llz5WRqipJhqz+N88lsrKfK/7oT/dCXmydRU3U\r\nNIFVGJorn6KJzgg49E+DgkdPvy6gRYk0qmYO1JeSXNwlSJDhdwKZey1sV0rb\r\n2mL4E3vWInaD4yKek+XryfM2iRMFXfiFBpqD2N3c0YzeVnKwkhnHcsfFE7ZO\r\nJhBelBGYHJQ8iv80IlJYq7vhZ2CCpOrml3zWdGMokAg78AL52FTZkZPSH8IZ\r\nZU6R69fYvFB1jjWas8Yg2mm3O/xLtdcQdHAQP+CWxRtkPR0UtT8Ma8DnVMa3\r\nNUEWg6YCrVxRnoGh+UO+FrJ63Gg+9OSbb5IGY6lCdeBlT3Tp+r/LZ+oPavH1\r\nMfBGdnS6FqhRHRPSuQmATNJwU+rxIuT5bp1QRayizwtV0rIdbw9ha12UQt50\r\ngjX9irRlx1c/YMcCUloaQ6rmmmDnbJ7Wh0Q=\r\n=aWgy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "759abdfa5c02493807dabf6d618f8acb3999b635", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-qBzLSsz8R4R7ku2j4+Ozge77F2ZJ7PAWJhfqNpXmgS9BhSkhirupgLhPHBXluQLCaRiSNmL+840KsJkYkC9n4Q==", "signatures": [{"sig": "MEYCIQCOoMx3mIVDEqZp2FLE8PikLK/nSdtptCXy5+fajnpL0AIhAM0BITgeWWfqGf23Aqn972NKUmLzrHVyr8sK9zvjuE6P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEIYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrT7A/+LFI0VktL1NvCEgq9sSyPhZ7u+PbHpbtFgYA2fc0bNi9sUFdE\r\nsgz677BLkWz49dAI3xlWU6hyL9rBgOhvoKbsmvBdYgAfMNGILUqHYwg3JAaD\r\nTaLpoGL/dSf+r+hxdTEpQ4mMhvRtYv8PGXg7kTnqgUqqsOujybDMUb6JHF0F\r\nSnWz6xjSLBfToKxm6BTXhWRchC0sVY6z4TDLMCU4u2PCn8X2tHVfTQLnZ0ll\r\nAJzlTlxIyLsVKS8pBULzxIsTlrCHgKoi2BWTCUombzN/1SqTcwtxTmcW6v1f\r\nL1knbVzlhhFT17p0EtUn07QeSUROxw786WMENZG6in+QYqbyULZq3WK2n4AG\r\nWcrjV0Vz7ulaJ/X5+xw/X8MLRT0dPV9Fvw0zwoth8FLUSJoLy9oDcPMTkyg3\r\nZAQK7lr09RqCrS/xq36I6xDSzW3lbITJ1FSzXBIaf/a6NlkM1ulireGwsv3m\r\n+Hks6edggT8hsGiBub/FH30Qb3IHEReBCN3ZNxKE6vRzrDXDPDurHYqYT+1M\r\nvylC+KdoBjl4FF1Yt7mLCyeeDUJXFeStKiRoCm2eJD4RurPnZJKtXAYz19t/\r\n8fgGa2tMh1y6IwEV0y1IcGAfAUQJxskwI7yuawPpsbcxOxIRhXEt2JXziIj+\r\nefN5ysCd27zY9snmIeK9nmtYB1Ari/iWDho=\r\n=elkl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.3", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a879688bcfe0d9271691173238ac3ef955ecfb3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-liC12hsVreOTdVnuQacaKYpK4etlFpvVZmglFayw7g4Kc24E6LTsSWezmvgmaSSyuEfiGJY0ZpVJV2QzsR3QeA==", "signatures": [{"sig": "MEYCIQCX6a0UvSkiSPlpYUxniA8kxQygWsquw/65n52sk7HMOgIhAKp0d49A+spzbKKVOgKMvhahdyYnF0y0kKPxmxSR91kj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUT5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJPQ/9EtxBkIIf+ti/nrrUfbl1nmft90T8DVJ/ZReGi6XgBiGLhCYJ\r\nvPCshNe9UODNjSTsyMcJyArcYsD96yjzp6HpMhpo07X/zFDMY/ntFffFdIxV\r\nn1gH5XnIHGNB5PK5m3mTTAmU2hyxQkVso0Ru/8sfUyYwSr50u5woeZkIgrA9\r\nTumxNBFwgwEDkyi+Qd+5CBOyoNBuL7xYCD6YzxHcy6FDQG/wy5tkE8pbNfSb\r\nTgchbFLYxwAfnEGyLoSNBM6mJi4xnfh1/8RlmJKRJ/zkFsb4IOtCCvIj7UDe\r\n8sLnH2iM8e3kAOevJe4SdprYNguKPaPNd91dQnk7bxryyRpwOuwO0hR3vshE\r\nqzUPp4lQVBWvzh2ByxWgvwVd255bz//hLCLpKeHpOSR0fdEluD9n4/J0T0jc\r\ncNN0ZHkeGubeqWuIteKBVafBh0Hi8aHl2WRtDGVOxQYcACIkNvTHwJHxssYT\r\nYR//b6MjDmVTwF/dnfVpVHPtxi0YvF3Sjpc/d4ExinmtO8IffEQuH2zhXscC\r\nzG4LSfWJSpwY+/E4qa5uhm0ca90V7hxBorFhzvD1jdX+pdx/V9ue8MkxA3fa\r\nQ3QmkfXQgZPSAmqNtfmX1F9+4HZHwUtA8fzvZ7KtQ6vhtuoF7imnYu3DhUJv\r\nZkJDiIQja2qFSZY7zCIYHZv6DxdVR0Emj24=\r\n=Dz2a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-direction": "0.1.0-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-roving-focus": "0.1.6-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4d37e386da096117c4b3afee965116d8df0e8f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ZgVCEjsez0GvuWwGXAYbBLgllOQI4qO54Zi6UcUbmtlqOYsHirNn5zNL6eWpSuA7ooIlE8BBYbHCJTojPnp/Tw==", "signatures": [{"sig": "MEUCIQClV0agcgithWUi8U9pqd/ne4mTza3nLH7Kfv5yPopCLQIgIb+rZhwMj88LBaKkkGxsa+0lTY16Adq9NFP34p2uRes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWASCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/MA//Xr0NtoiXRfefUdNDLG+Bb5dIaL7U2v2GUVzVUqLlP/CxpBPs\r\njngneU3SD6t/5fwMKWF+pO5qS22v3iuYo69/0tEPS8OFC3PoftTg4mffWpzT\r\nDwTVlwydfDOubqEc8zb9HrJQ+nTYH9e2RDDr0OKzVqJqN65D2+C4wJ7ORmqs\r\ncPtaNEbfyDzN/Uu41zZT/ouw+oo2diY/y9wDLvPiG0CbP5JOCwTseJ1EcdSY\r\n31iw7DS7a/I0D9joys1xgFksqABgSlIgTdfiDuTVEv6Mj9ObXJd0pG0Gwqrl\r\nUteDpqSK6J8VtPvXSyjlCLfNCvvI5Xb/G0+qVcOzMfSv88/MRj9dQZxJp/K3\r\nSeQ+R5rarMQl37UA0+cAd8r7mOVOKyy67MQKV2baOdYLvzjkd9eJ8Lzc2KD/\r\nm42bvonLLoLrtq0fo3A2ZL4lirn7P48Zh7Qth0RtTsN0hKYuTB0l6cUIJLaU\r\nFL+PZ10U6uI4QLZX+kB2RypV6dWzxQRB9gZ++HcP2PfERuChBvCWm1xcfrGe\r\nKCxjuiaCy4+tot4sXVywztYwTTf34qX9ctWVT8mbSx6rYmnXJsFKP4wdR4f+\r\ndsBDA/a8TbsNUNdW2uFIamlqb9mfcjEESnZ4wsfRub5SNtiCgCRMsiJwy6CK\r\nF3/0FHG+W/ryZBUP8USvXytFSfn2B0DEtNk=\r\n=sIaG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-direction": "0.1.0-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-roving-focus": "0.1.6-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e8abf819b40a4f214a6a9bc439b5a671ff4eaecf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-LdgfiI8wvw2jGstouvrkudyuJna7gz1CapbsfuaHufJ/WSgc3kpmtrTiCwbR9xyBvpyCxm8oD6UeRQ/4xdWPqw==", "signatures": [{"sig": "MEUCIE5obs+hc7lCkHg+WBaqcfakSlaJKONiQPhx5jzFR9NKAiEA1bR+UjKkKiguYri3TbU5Lk0l0tz+VjsOufIqGCOJO2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBShAAgMj6QIMlUFVcm121SDvkOUVXk1TXY3UJLMTVcabVpIBaA9d+\r\ngSJKAbSGg5OwUOW9XrKzaXtKMx9sInCTZOHFClLIb91FjMewG88FfZ+nbJFA\r\nFiK+YV04dSngyOOoJykU3B4NVJvZKw3XiQS2DS93MSnaJBZB7f7bhYcvqPJw\r\ny4fire121fsDHzYx7ikZRhPJ4/WmBPH5QRLHNsKU+XOIGLZjb9zabPjiPqC0\r\npD3/t+e5p41lxuhyL4sXW/Dc2aEFJlDnqVPVUIBclLQKBfJcnBcrPncNQjV2\r\nJL+sowXORCCgBNNP89FrtoIgqYMmE3ZcIS6MZBfrM49PvBiRua6FSXf+LfOM\r\nW9GhEtyPyltuj0/oaJlc/4B3ZZw0S9oBUF1NrDQnmIdxJ+ze7JXugi1CC9Nj\r\n527Vno2MhF4v71EH4szkscixUX9owQ64Z66mH0W8h6NIUDKzjQMbJq58+kO8\r\n0eDmuGT/NwCvnDe15bZxPE5DAath9J3HJdcMYxHXYX9HZrQzbQNszP1P2ab2\r\ndrfRpxEgGzaTRhyUOXwgwfJFSbhleF7xwivIOC9MqltUn1DqxmKX9BP6d6xu\r\nYqvm3beXVyfnKHaZhxtQgqFVwxkMy6z5J8cELPNpRRYic4aEo5E/DIUj1PV2\r\nClNmACCji+RhPxNKs/g53LUXnLoDgVCMFKs=\r\n=0eFS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-direction": "0.1.0-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-roving-focus": "0.1.6-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d59d90b26742a5639f44711a853c8dda13b52d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-JEjpJ+Zf5dp6+pN73S3f3SI1eBIOwt4YB/TEOjhDhHg0VKanJnaJtDw3SgPFlpSPZvWeaR87QqMm/nDZl1nV5Q==", "signatures": [{"sig": "MEUCIH7P0Zt3TAVSl/5OXEiAWkosTYEyYe3TOvBoZD6MOpJzAiEA8B+vr7+G6+/0uGtMXooBeRVExHunJkI7TL1SnCY24v8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdHQ/6Al/Xy/uKEmuSl/K8rgTy4FiWr9Jf1PYKyOXrs/VrMOEhAYV3\r\nMkRFaKmweFomwWBTllae/eVZdVfMrvzpdhPQszpnD5PudkW2nTfBmX/zmaPz\r\nN1dObZP5SF3L/5LOK0Zv++fAtxaJa5Z9n2sxzVbiFIYAWs42cO31hhb7Hb0O\r\nJPJJATt5zBc2/+lID+NeOw5u1Q610a4yexLjOwm9BlQDXkt1BxyaMyShtHuG\r\nW8/hzOcXLLvuTu3JGdlLBlJtGyOWRDfGuBAxckS6gaoPiIHvAc913k+QEsSc\r\n/VNainIwlYI6j7ZQd9zA/Y+7vSAzstVUYhUlvraMVAQwPXqNsWzDMATiN2Tf\r\nTM3JokWEjIIkv8Fs7GAujP/nHvXse09QzNy8bFrjmsUsxpAoGCbP24TZvXVt\r\nrNu1WXq+MHXSv3vo7kBOdqGGMNkXbBsclFY2avg8hKxcoS4zCk5AxE7mGcxF\r\nzK8IMWqoC2pT85wrO5oYzQfC62r5PVgAuWy/A7qG9n31X7ont4Xq3jkGwNzD\r\n27RYNgrJ3YseIvS73zWYwvn5No+3lq6cmOfgfzpUGuEt/9UWcYzHm4ixN4Mr\r\nXepFIIHMtWq4cRFbTFdeqlPh9BFUCchnmVFnuqhb3clgp9boEEEY/4Wc1HsX\r\n1EjYtpn/04NnviV7KfFMt+fIEfkEmOJq5u4=\r\n=+Vkv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-direction": "0.1.0-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-roving-focus": "0.1.6-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8fef6d85e11202a9a9c9b9e0ce78905660ff9c66", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-PsLpPsXYS8ifgD2FUsSwsHSPoBmLXhDhqaTPy/c/jCubmLA/KpH7lN1WCCDEHuZs+Rn41FYQfuafxdGPL2kq9Q==", "signatures": [{"sig": "MEUCIQCZ6PqM4YTC5e9Vdd9Q8LVxsNmIogsMXZuYu6HoMtiHoQIgQtDKJVlVPFm9BJRRxR/6m7Ycovi2p3g+Fz4VTGQNZas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMPg//Y9PGd2hEIsdJRSYIFWH4Q57Wgb6kZo9u7RD80P0KbpI8+K/j\r\n96vVwdL6Jboc9tHRhbiKW2vsao5DOAjS9PZANm/b4Qxdqz9LiXQqKvTIFhs+\r\nuwh9Cp2OgBlItrasEw27OMF8ineOl3D1iOjHI/CvOsAYA0sfv7HlP4q/WGUP\r\nCm7AjFjbUpfC9errAIeF8A1zGr6yxPnJXGLhtSmogz82oEQwEY9vvhasg9O6\r\ne1V3HPvaI67cKQge0YeV9JvzVbjaJ0/ysXyJGpX9vCc6ECmMAilSVZU1rUw6\r\naN0H52qPvc9EFXjQ1nOdXMvC5T1zyM9z/ReFuciKLyZyQig9+5BXWe8hUuuN\r\niT1wXna9xYAbueRcTEirxxm3QIh5B39IWtEh/PNOfxvgk8P0sx7/ejLZBj+x\r\nv6NGiI9ps9xltlKsSFF4o/PqOuOP7rIXK6krvbWmCBn5V4UtXMeTAMC9Pfe0\r\nbAjvAkUI7Sws1fqqIDqRgwxy2RJ9+XV5+GFaW93VZ1BwmP9JhaV3Z3iUx6Rv\r\nxuTsvh8hwmvZk7h+39f8no+0iGcL7wOqXUy1pfKV422eaLveUcn9HK8+IHVs\r\nvKxwIeNVJefseb3TqLJJdcI8mC8tyGOkCuwsFkuRae2WFhXdAwycfKUS99r4\r\n/mfWVvp8oaOQ985l1UbgZijvnVhfiXSiVeU=\r\n=y+JL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-direction": "0.1.0-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-roving-focus": "0.1.6-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e26c38934a5dc0004f61dad0ab61175e40fadc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-hSHTLop71SwKr7XFRDNNbFLZGW6mPAT6gH/g2aTiymgXwAAjgbH5+VXkdWjsXtbuxtiCgzEH1qxTA6SUbNUL3Q==", "signatures": [{"sig": "MEYCIQCs5Cx5BDtaXKkDkUo4IzhYVZDzK41Nh8uRjmYNBAoW8gIhAPoooUuLlaVchEsV2xCWfREemMoydLxR2ucE8maOnp7A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFnxAAlm1kzgoKHKOAZBm7d+iKjMSBCf8uyy4Wq9gbm16mWjUuSHmp\r\nDPeQepbeecIkwp+x/Qet0KzyNHXk3Msr6sRBl98xBSSaIVOxG++xxcPP1CDs\r\ngr2lQRTQNnZ1BHxtWv8uW+gwrfbjQQPc+vMpO7ESqfq0I66BylX2JNxHKPYP\r\neEpoK/R67nZiQHSv6T7A2kGntqaL2LOcbv0MjJU5BzOuj9eqrLiaxJtJEcaS\r\nNn7KsEPOgVc8qp3Op9p27X8O3ctugiopILCiNC+pIBsC5T7eE9YjFQp1A0DO\r\nHn0sn549r37MoxUNgoAOoapj+QNPkNG+avAsOlekv9M8ff5txszSNywclUWK\r\nO3jryvIuGUnwBdRtISzbciDtPPB1MOOZ+QPqJktUDykrw3/AjQJcn4l+Vaqz\r\nYlf/Bub6dSRT10Ggsk+ryL8IeKrgiXfKhw+T2TomF7IcLVfqRbUZAsN1MOKM\r\nv31Z2zQC1zTi+PQ6uovzLO0m0geEbtu9iZnc+8WMzzWVsN0TYSZfoFjOhMnm\r\nCFIL3H2Iv6IJnhGfmXMdUT/X6Oq2GyznetsbvgvmG9zjJxhZ2fBo+GvSrVZo\r\nXEzu6ki6mPwFgw+cCR9FxZPCrxh2Kf7wTyy/YozRa7weQwP84p+F46BSuVvo\r\nBqrkyyk4E0SndzkWhp/h81QJoopUogyiKfI=\r\n=mzMs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-direction": "0.1.0-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-roving-focus": "0.1.6-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3b2778bc42beb24246e6c041cddc00948316aa98", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Dgc/FgRhWieV0JLwgSKGxBVYqD61VHausEUuHP5tu5G/ZN7AuNmt+eM2qmdqwjdEup+0VHuT5n/iV2YiIv5MDA==", "signatures": [{"sig": "MEUCIDLIVcJqdnCVTbYa287Arwa304e5+KHGYi1M6Dw6UfzRAiEAuZk8pVMIaqxCPKY8CCeirA/NEZqbMcFFgn4Swac3Vh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFsw/+NIIr0iKRKfr20iw8IPK7p55i0cerqxexIHsmRFqrxkBYQ5QX\r\nCs6hQUpzornbjw8oyF+epTPwst2uCDDtdWs0wvgfrYsalDt/mT/gJZfmbvJS\r\nO7I8MrrE9ozW+wW7C7qrwH33PcJNjTwHrgo8z30tk8Rta2FogVnDabMuureV\r\nicIi5QW32MopWinzOyNabfNqDefCkYz8I5U8m2496DtMSX+1770oTknJOt+t\r\nJnDJV9GEkBaXjl5bNOn6H/aVmmWKsDYmsMSzD4ln1ESiazZAEQVKl/v3/0jh\r\n2v8UsA0SxcIqje+YdX+8Tiqq/wmwkW9lq1kH6BWAFpKmExvpEoyfcCvgC9u1\r\nGEvulZZKcnU2nDU9ArqNk76eExnBjbdEvpKDgg9SMTg884bZjU5kDijxo7O/\r\n9w3tLZBFVNPb6RoHta0crQfxFKvIOvCZjEi7X58flzUEJxGyUt34bX3K5LtN\r\nsHLPXC/cLwkYFemgfKgAdMNQRkhYpk0xLND7G31dejuloNI1Zl/etLGfG08S\r\ne9r5+OJcaFiTbOZD4woTLontEfEeyfIM1R27cC7uTFMymUSN+snieicMU8CX\r\n+JNnbBQrzdv29na2HA3zlAwJI925Wpw3kucHPzF7B5VTyzvT7hjn0xunfz+c\r\nxOaOFMvkZUYZ972oDpAjaNj9RnqyH9KJSJs=\r\n=P/Ba\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-direction": "0.1.0-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-roving-focus": "0.1.6-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0119f912be6e2c38871027cf0f530a6c6e780ebd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-r7U36bKmXCZbNNHaSoleSz8757aeHoGwnvUnS+3AEkdNoVelnmgiI7iFgRspeZRxxckqDiKPux9XqC0A/mKkUA==", "signatures": [{"sig": "MEUCIQCgesj71DtEmz8m+POnp1fCiOeAbFi37dNR67l/BdchpgIgauSfht5eAN8udntjQ7F56Qd0rmyjt75vpllmahUqq5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmps8A/+LgA5CF550l7D2bnx8U/IsnebpQGU8tFunq32Hqh+YM2r9GpR\r\niFYY8iWhYfdXylmOL/a70zT+nQp/CElhHjQ16PnCC+dG6PnsGw7/AldqO7dp\r\ncknRVH/DtAE8IUvkwXV+Q8+90b4nh9F04QScUt38zYjhC9Ui5qcmkMgri7vJ\r\nkpF4mALuB1ikkz9clchBHmtzO2P8mLVqHuPzAk5i724pFNsvWM6hRyXuus2/\r\nowWW/BWvBf01/Vq1XUISjwuAVnFxQQfa2iMQUGs5YFLxzYRZwxLuvQIt8WIX\r\nxxh6zgJ2rpgH9DJAlVTaGe1T2cl9hxEj99FyXSoOu8V1HwFLzZUJG5204k6R\r\nR7Y258TqLIlRXL9aS/XGbel8p1oZYdSTLt/FwDB0eeEnZMOhZy63rour2P/h\r\nWwVISfLafz30z3kPw+b3HonW6wxFwP7hsugdio5vFCWrNXVEcvdUzEp3Lsh0\r\n6MBEddH7ItJHB1VSwFEzJAd/VAaa5gEq3ZZUpjH9wXJHRNfG8drP+H/fQ0Mm\r\nGVHLdChhBhQWcZgg2Ue9mX7QIgdBWdOnnoQVCHpT7hfQgkc/2esJIzUnOuNX\r\nFzIrDKDHc+N2DG3wE7ZtQScP7nU1m6InGWNS9U8WXZ/JFytLqAVA0p79mXqZ\r\nNK4jy4RqWPIGSRkybfM9HqjroM0PDefAdg8=\r\n=i57w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-direction": "0.1.0-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-roving-focus": "0.1.6-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1224fd1f3916ae26336511f0a20f4a4d7e06a230", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-OwTcFOYz7jxSVB3+yaySZIOiJHrL+NBFOhr2y9mBDPny/bgnMJKTVJN73KcK5Z1xf+FEb1+l+ciI+9+pZXNNmQ==", "signatures": [{"sig": "MEUCIQDKSCY8fViTmpfD6v6Qg30uh0eYt3lRoxnxovt3YMVdQQIgH5ZUnBJGg82lMHP4VpxxYTglnjHbtYaCklVph8atsrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVimACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr8g//Z3iQY3vZbHs1MN4y3E8M/ekuZNaPDuFLsKJ/KXFxD3JQFR9E\r\nSv57MVvfG54cwOOCv0Wke24riiO+0yYrLfTftnitnYSPCsyAKNcf/tHue3Pb\r\nHDHRYck9hHbqxSIFeJZJ/stMk+0kJ2O4WaSofmMmk2KWauaU5pOIhHuAUg4K\r\nyM3CiO+OfpNUL8EvWqZffjrRawr1KydkgULPbfQ985dd9g1xh9FP7SZVKFao\r\njQhFEL1zb8cY6zsdQi3X5vUztK7wzZp2rSoQLeG6kkh/QhvCBB9YIPaq6aA+\r\nHXRLDMRiDm+22WlREO0UZIM/phZkERyL4KTXZdcMfdIRzvP74WOBBKFbFE8F\r\nX7UPkdoLdjL+CF6wD1eeAvnsmVhTAEigAHoEeUfT/l6BzyCiue5oDGrxc+v7\r\nz8IUklCy825ARJVK288LziybHSfB0O0T3dhB92Mw2thrQV7VaOkg+kEOHglN\r\nhu9DM2E6roOhRiBuRXR2zwKbK70MeVhImQT5AMSefo3dgG9kDG9ABJ/8kN5D\r\ngBe3Nrn44bSJyfTWS99vFYoIGSaWU/DKkgJk1TI9WlooPdb6vkgiN7ZGjig9\r\nX6GLBIpTZLBCWkTW1borgZhAk+ipbaAEPg70D8ig6uJpaWbMCwMdQmtiXdb3\r\nXNf3Koh0NySzo4K5va5sarj7gBh97P2fGRA=\r\n=1T6P\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-direction": "0.1.0-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-roving-focus": "0.1.6-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4542e19b1df4d19b143184a97f82686b8f020041", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Y/6R5M7Sxt8BkThuwyViZ2yuAxxJfkoyMkXfjAqYKrklvlr+j5QHdvEGn2P917HgipEILzlCOXcjviZ5DNuokw==", "signatures": [{"sig": "MEYCIQDpWKdQs6M3HR8nsjtEjuPsWa7WJw5LR6aEdq1YHjaegwIhAPd/oCsyYcc5Ti4vaU0Xp9UgGhYtpl2BoChP7PfhrUqQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIPhAAm0Nm/bzljcEBrzu8EHjt+xtknMnOheq2Ln9eNGF8Ect9trlq\r\nptfxl84qICTeCc3s6pFpS+IGsZsr6K7vY9vhTdn1RRaJtBaS/3zhscopSGxi\r\nGmIxfmTP34vENbhBFrDsTyhnOpcOrY90ce4Pjw1uJEakI4AvyBbAj2KkfyAC\r\nbiqilnS1W50W9W89y/ncmnWrVOK2DMvlntWhQXDkXM3afDGEUUKKvdg7Y8ok\r\nQgFgUjMBPbi8O/4hF/Sls40L3wdEto9pxZNiabHAh6Zi7kfAjlfmIb8sn95U\r\nLHnNUosR7hlao4ptjce+4PeLwg7gpTVjVkBALZXw/adeyszr48rOk0KF55rN\r\ngNkH6s0fd8sbb/xu+B9wD7bYrSq+Iap97dZexPv09IRJdu4EP4OmzN8mPs0X\r\nm+f5fwSKVaubYQq0D0fANwASgNjJbU7NyAxr3o8lF+FBIEl5/KdkSUbg7tot\r\n/+dVZxjqk74L9ipTzIXEu1jkykcYf+YFk8evV+/lljvBC5INj9jGjJ7sHHEn\r\n9498sf50ZjTOWT75YH7opCEfj5aMASgnSRTf0Zgg4syfZn4tOhVvfo6xjDrl\r\nIJ8IwWPWAnogXh31erQbm/bL7QbFAJVyaB4xHJDw3nkMH/Y/Q4COCtOs78kB\r\ncD7jonfE3sEdb91Ev6ppWUJkgU1l34mPHOQ=\r\n=j9Et\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-direction": "0.1.0-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-roving-focus": "0.1.6-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15918f6b31a3dcad7793870ed5713d3510a2932f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-uAQDKP9/qCfC2aAahCNdufriWz/T4Y1tzigU0P+CkeEl5iHmqWj/UljgqBifpUnG3sxy6+1LKzlNRWkX+8PB+A==", "signatures": [{"sig": "MEUCIGmYxtALxgfbggiCqcMJ07tGNL+MAC5Ng8n/BI8HhfT7AiEA2XcxBg2vVUkP9FuH/NrGDUbEFMNeFsBAwD3pog0L9Ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmxBAAkAqk8bB8jMbmhlOBomPpNWxnpntdrA15kXZSJrNZ/1XHftTa\r\nusdvVLSD4K1zNtirjaUQorU5a2Nr8lCmIXBTQ4iyKep7v+wWOjePJo/R6yn3\r\nYTSyvDsAkzgFzt5Duqx4hd8M2tbYItJnBj+JXGPAIm5yIa65Yi7gJIXkRGQj\r\nVM2Rvj9yP5fQo8GYyT35EMedBYoGeXQ7ipeiWN52E8D+DcqB/1K1NskJbekE\r\nv9FqTFccBnjirI6Chk+ye4fVZR4E9G9H4m8kPO0vuYmYfxlvJCA8dluR6yoh\r\nGpQBNZPyPYr9DN6deVv5Ehybsl+fdKb+7CImVosvATLk5lI12UOuFwiYXXUt\r\nMYl2HFb8EBSED0dPdxhpgZmHNRKBOBPuYpxxwVof3o3LrA5LQFDoCz3wf+RT\r\nxa3j9s/8GRk7jRRuyy1AjazMx5FcH360DO1Sd6YTt0MWPesW9QpgYr45dRY2\r\nAumIiHhBllzKju7+OmOOLjeTtlacCJD1tkGVYOaMTZVeWyDbWKxR3i6kAS4E\r\nzbDwwSBaOYM/VKelC9q0qMntWZJ2oUhQT0O9wRDRWBrsjcqDfsuwijUjmhPH\r\nT6VFhKaIFk0rYBVMid+5J0XsOkNsD8/E2EnYnMBlIqbDo+0lAjwqUqmKpY1Z\r\nmZABcthzZ0Rldcud2TyAEN4CRFWr4Xk61TE=\r\n=TCgu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-direction": "0.1.0-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-roving-focus": "0.1.6-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1ee641857391e8637789a85f96eb436c9fdc6267", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-LhPQ5E5Y8WQ7OzhqZBNDHX1trvkUVPd/TtBLvB99dbMALQioGsneNg8G29O0EyliPfR088Q/8ffg2LP1He6e+w==", "signatures": [{"sig": "MEYCIQCR5uEEcXIK/Dt7fE4SNckKvluwEoR3Uy3CyZlEzW9L4AIhAJmZ/2I0xGrt5kgFLOKJPMhDIsR8pilHGh/12k6YATJc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSl2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbtA//Q2FRnPYPT3ICt3zzN/MtFDYmVvHWbFcbhufk54uksM9aiInc\r\nMnukxc+6lnG5iRO1DEbfaAPzdqVcz1pMcrlQjfKnKTEkNpGp+QY9aNKAwMHk\r\nnjz8pMhoHWPFpi0I323Yts8A+Pxw+cKw9z0uFezhtDYr0RXlFpKwMuN89TJX\r\n7+uhUDK9o5hXFA9ST9WLH+50M1eWzbyf9iYBG2tfQCsgm62Z9oIRH68Vjhj0\r\nNZ6hugxZcEtO7NXguMbCRZ/RllW9wRzeyY4Zt7/v20EjzUDujktk6yOHaMV4\r\nsr20HymSvj9+zR6HI9TycEdznf4IooQUUDAwBwHUo56sPsOPd7vvp7kjuzIl\r\nBY1plcvT74cRXTCHiqz2XFFa/v8Ckrn2m+ynJ8GkWRX5KlRs8LUE1/8LJ07b\r\nU5vFpHulorLBXrCUdnGdxlWoQOKn8aSJ+XFL/IQT5ChGQZJ/Ic8Uebe10ZEn\r\naAW5DrwqLfVlSmVOWUQFhR9u6bq6g/TWy2TZ7QRDCJzvSxqLOxL5iWmJ9kfl\r\nkKW10rDcbU9LtYOhn6grjriuNZYo3YUmy/rT54tiKgomDX/F9qrEr92wkGxA\r\nzGj++MtYKHtllUFjwVr2pDamEwEvrNdGquZsvkFQOy+nnqpAyOlZBzkSN+pn\r\n54SK2wGJ08jcwgGBhbcn7tcFB6aAS4pEk/g=\r\n=Qz8A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-direction": "0.1.0-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-roving-focus": "0.1.6-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4f4df92e4e4248fc077562f5f8e40d8d9192bfb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-vQcs8LnQutbuT4hfgTdGBjD0bhfdjGoGEswqD1mNI5BEXrOrDV2z7glSCkF2/5YNI5L8Wj0Fo0yahCewfOqlUg==", "signatures": [{"sig": "MEUCIH3E+q8zno3jt1DbVXUbVs6hpjGaFnCR9sJM7Ck4af+NAiEA+pkePw19I8tVN4AAZVT/Votdrs34oEvRR6ZOf2eVVxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou9g/+L6xjjnWAoO9DQrqIRYAYU89Xggps9aq6PCPBGq9Bo7tASRYB\r\n6xlTxk2GeaZ7H8d5yN1RSpKs8hjLn2H5/dvUvsi4faRldjcNnQEg3BE2atjK\r\nQPzUu0/MR5HPdnP5mtcdGcSOhdBGwAjqAbc9hIys914XeueHRt36IayTbsgw\r\n1cOfDe0pXhy97yZl6AjoJYxOB5txOFtq8CpaCskTtPAethsE5VvvYbCfbS2g\r\n4KkynskYZBp1a8z4TZDlvg0AHmQCPzhyQI/rN+ueAAO2reu0pvoVF/3GLK6n\r\nYUhvZCa3GoC6OkWuM+8sDnzB93IXegEIa3sDSUbAHpdnLCaxIraMZvl3URg3\r\nkDfo+igR//cDdNPXNsb2AoqGETPvn8GxqIOmy3L3BJcSKdo4lqq8ixu5og9D\r\nylZEze/7hCsvmCD9JYEGreeOC3CY1LtmbZovaIregos6zpUwkwb4k5jcZLN1\r\nbnbVU3136zscnHVdsL3odTqfl1iZ+p6IT/Ujwuk/A1E0MGtN+QnpCu8qZ7VE\r\nTN1gTTK5UGB5vZUUnRuR+8I0YpLfBHVXw6xm2Ah/pto8aU1HU4FOmBQlGWEh\r\nkGI1pwLQfOKXULqu5+kKGQBbBP8LuZyk/9jZT+ZqRmBhDc/JTo/gno3lAwmY\r\nEfczdFRxbklz+pq5ClewByaIHQkq4sM/AhE=\r\n=C9cs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-direction": "0.1.0-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-roving-focus": "0.1.6-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0269e586623418a9e236dadd6366e410b239b782", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-4+oRDuaaXB5ah6j5+rrMfcKxbZWf62FVXZgR3tGO3912f564f6AYUPwGyL1DrmVAnGQPcQ8Z4fgZQuW3ycibMA==", "signatures": [{"sig": "MEYCIQDRNKQxxwJ+lah3j+IPhtvgoSUasZ6zKnJt3zJSmkP66wIhAPm2R8u7llxlFzbWiYWY+dodH4x0+7EvhMIZv6F4om/0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPXA//fpJssmOvRD3tc5UnpA1BMZdMvRQkXAdo2a3indI+dQ8tRNsm\r\n7iFWxrltCZh+YMjH9ejxOZo5+bGfzNYSr3t+fbYkOmFK/GqeqKr/M/btK8JG\r\nNQWhT8Y6qr2d7mYMuvGWP+lDwWP1HlEtx8LgZKArU2206PFtDA6ZGGAp2Owu\r\nJa/JmC1dE5AdWz2caU3CwLZwLSbjZjPF5jDCIks67ox5xy4bmn8KM/n7L6w/\r\nxMrwWDxgSFA6kXpigqTb+LdbR3u86ktoXRpxxnHNFe9hev/aibD0BktZl/3Y\r\nujXpsCWOIhplmx9CuwmvJAYS3mvNmQ2qFgEMusifd2Fz0ZVTMhxFchonZD18\r\nY0htfNzACXL9uM4i7kBB0sLaLwtBWC+M3FmWJHPDvVRRNrUn/xLeReM6lmJ4\r\ntxgCW8JjXSMi41qWHFU0hwYbDs+SLdPvWc/pL906jRQI3T7Ow1o+kzqE2R2/\r\nZvYrXfbaOAjBKZg74JLq2A62DBax6NBYL5fBG0TvH2KRtGHtBH3OTUtX/Uz+\r\nMaR7XEBYVXHIuF+c5/N9H0Tlcp3kpQuWbY8LPyzUKh+2kgz3bTKv++5eltad\r\n0gJh8U6m+aqQBklZvBT/BZgt/amxmnpII71DZrHABOL1xpdlhaJIZNeTQtht\r\naewi1dKOL3fj2Sq6vBzS3SwiAtya6Qeb1N0=\r\n=v5uy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-direction": "0.1.0-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-roving-focus": "0.1.6-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "72e285f39895a9eb968fcb80c8d94ca7069536ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-6Bc5UVtz5tUsb2p6qBUIPrxK1mJZDiIDSr6j+OwXeZk9B3crjU9FytRyEksn5PcLUBaPaKw3HnUez3kER9FjzQ==", "signatures": [{"sig": "MEUCIAlDJgrUmgLNF9iNyudQvsrIRRzGY2JH/rRNT56b8UERAiEAs2gxkOKVxlyhYur8votnlYk2ALQAkAQgejEirpZ2p6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoxiw/+Kl+TpNSwGSALOBonVb6Quo9WsvHXeoQhy278ZuVHRACKBfhr\r\nJsLzbyDi4XJzmV505D0In8TDT3TqYx9hfC9TB7unu/h+Br844fzyuoxMDs6E\r\n+aWxG2nkUrbUp1zxyYeDjYxhePLrQ/jIzH+RtPPoUy9UrPefd+zUgFbmMqvs\r\nlanXUEDUptvrr7I1doLB87KxpMxQGThLV8COl9j4EayjU/a3LZKc3h0TiP99\r\nbOdJJQZUFyFIB1uy/fbo+ff34ZdYTyaaJiox1a5npfic+0eDB2IPPiAFdKis\r\n/wb468CnaxhJtsh+VUNL1Opw2x17OSyXFUbtmzZyJVSX7cvBSdzl39OJM7Zr\r\nv2uugPG0C3olKtx02xsTMsX53onqfdP3n6+cmw6w0zcQ0i6+JkeO6JDrcY3u\r\nciWcGD+baNNApIhs4o3doL2vwUH/8cF7MXe+JNaoyPu23jiCLB/ViHNxjejs\r\n1syEKeBlNzuPiIAeu9bHOLKlF6payaDkYpsshv0Gyq4ouTy8FcGF3G+bFfTf\r\n1P6VQSDCXiuxeQRw2PqjyU03p09NxiOJrp7AknmmEtTLBZVVb5hJyBeA1uid\r\nd/wKgoEcWYOd/a4BtDrlU07CeJwn0/un50C0kBLKqfvzA9/ZpSoxvSCcBphV\r\nEvHCTqWs5bD1WxUpP+imNlOxf+GpF1P21Ho=\r\n=EZY/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-direction": "0.1.0-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-roving-focus": "0.1.6-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "456c232051d64d19663735f0e0f3746e010bad9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-9D7htUbrwsL7UlvF0N+TAmfYv9GDItuseCTfB2/Z340c6tDbJNcQckt4FWHlSdw+r5FChEdMrK22AFB325XBlA==", "signatures": [{"sig": "MEQCIFWP100+NBct8nhSBkTPK5cTqU/RUryV2gsX4Sy+jgrTAiBi5mWrCDqkC3VGzGl6ohPpNN4YkYiOvpSx1U5oUbiL9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA09ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmretw//UwTMAzrkXYygvwwYb0PUhStPzM5PrwsBfELneJWE0VpvNCkI\r\nplucCEr4+BQgM62T6wGs8lLe9AKRlLPGvpoJaj16JkzYx78Q2WD5NjcqFPly\r\nX8jLnVZ0jQbSzH6MVkTj8tNtNKPZB/z96ZU0g+0W6hsyAI97aaWtu7gltsJR\r\nC7trEMmdO5PymnUzob+fJGR0gdCWH+TvwrE5eHCJ6nnM8Zfl+H0YophobrOh\r\ncWBx+GUDMVsnpE9tmgELnGOqQmZ+G0A7qXrizW7I5f1Z/kNH8ZEAuPzrS2q0\r\n9QHGG2Q4FGjAsVU+Bd4+VT04H+2Ag/HkO7wMBI4CZdLOBu2XF7geH60Iil1r\r\nZatzGjau3Ixkxzw6Ooxq0uUvQV+TWQ8eTPJ9zzc+mXEbVjpzXEEm9DfnxTew\r\nrqQx1HGR+u4MnsC70q9I12h9iS/Gytr9eGDl+m+hjba0xfR8lwJ9TdcMJ/Od\r\nnPPaxzuB4Hzhdtf6RlkxuPlWTnvKkX4KvYM8DMHyfOD2XzwJH7xkQ3xrw0cm\r\nYuW8sLK3VhattUVUfKINstH2RtEsJqMuMuocp7r66PJd9rvib32G3IL/ASnP\r\nF6UTSngVWOqiaTAGkg9d6QoEYuztqW2X7uQ5/jc13h3h3ia31yNPjVu8hvXS\r\nKpcN614fJTHiuSZCVHFUXxN3hbRKAQuWQfk=\r\n=TC9J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-direction": "0.1.0-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-roving-focus": "0.1.6-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5767fa446c10554e008dee6df3f6e19ec0317a11", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-7rsslodAfuJv6K5LOzrWRMw/cexcC+KD+qYPYqf1wY8hnGOO5GHhSzUBUttq174kypmOAVBSSlQqNy64wgCgRA==", "signatures": [{"sig": "MEUCIG+9OEp7iHLxArrDfGYSsiFMd45p3qmgDU4qEq36NIpYAiEA7qyzg0yLz9zfXeHTYc4PCPbiz4hkAu+FYgxNS4KEEc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTshACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmNQ/7B194Fs0RVcRYxliLXf9HXxYh1jmMC3dW5px/ytInmF5KdouN\r\nuqzyIjU+cQUK4pQFGxjXMCIVsRJzeu4d0iCtszQgFZ6y32beA0b36cVkBOSx\r\nOXjfZIoQmyZb2GwRvpx6Ih3wWDdPP6znp4STQeVjMyVDmSgRyBTQNCWxBwrU\r\nUDKclkj6DvNyEtRk4wF06qWgjR8ezTYv5ecjxgzASR1jdRU9b8OZpr2nL/1V\r\nvfyn2LxndXNdeyuI2RF1jPkjCd2QCzLhDmWuJxx379v+w/ukAnSbL0YPWdFV\r\nEbckjaEAQfQNJCVlPT7c4m++usBMxshgGcNwQKPdUsPHi7iF/vp42w3LhGx9\r\nmkkM/G6yvsGKajRJzuNS2KLV/VbdAqzwkQdGsQkCjtQrGf2r54R9fSH1L+0a\r\nP9G8WxgohN9PkaMYdrSuXD9TGJ+fP2S0cMpqbpaVGVUrfBJM7wWigHWS31YX\r\nqH5yoVIBbPLFTBJomIRooqlMPfteIXix1rahjp69CW2ijn68BRuFwM5C+ibw\r\nvZZjXOWXkyLSce29WWaEDLkZ2OypdKycJcbM0Z35aZnK3TKuyyVkUmUk5q0Y\r\nkkYFlEhGcsjAi0dpbPrmUqR3Y0n/w5XMcxqF3F1fP/+iU/46GR/8zYz1UzIM\r\nfzEXTBafp172p2e54+KDXaAg9iVVzvvCAAc=\r\n=Ov2u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-direction": "0.1.0-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-roving-focus": "0.1.6-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0c2256de39f5f0b200e7d26ebf78d4d0ab85d2d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-BNEJYtAfD+kJA1IpoXCci/kyqn+o2JRn/kdqCiHLTFasxBJLTCl+ocnctVJZGMwTYNdgahQMer04+go6M68gxQ==", "signatures": [{"sig": "MEQCIDP1vUUH4rSFJ37BAqv4Skzk0iSLia3WtoFD/4jUrh4hAiA87U+4wLoxxIxtz0QFnngY/UOLmHiAwR66MlG+OPjVLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpW8w//XF3G2PGdh8lnY6ksPdF/vTIEZCB6Dnhq4r1pKgYn3OwN7jlQ\r\nbvQzybOBH9LkKtGLM2M58FSfOb8CbOmF6YEDo0eQ6rfWCnZVfBT6ZTcA3IYZ\r\nuyn5W7OTNj8nGwqaQieB8if9vjdIHae63+LTsSmadNdsmH5j8St5F8Mgaga7\r\noAnzwUevseswQJL64tLX/LHjWrP6sVQM6EXczmH1oeBmpoC08ZXaazL9dQ/M\r\nSt6zYVuhtjAFCKSqPnapRfKis8sbdbbuRzO7tEeiAPpDgBauOmSMUqoSdvms\r\n8CzuToL0EQc8cUMpjWXxlJpM/C7S9CKWg/dZOXbkXF3zaV1TdU1svkqSt1yS\r\nqDlWACmoDKbYrM/1rV+FAHMtOYoR4pGbUFOm7+1iS9XDij7n9NQPqVphqdIz\r\nCAZE3ymOdttHaIzVfDBH1gTggGamrWwK2c7dGcWcP0+NZBrotXwTOKCg8VYK\r\ngAuWaW0ERx+8QXf+X6LymAM7cBcn5xLEBqPImfIX9I0kaMeyQpUeSGL/Bg19\r\nhftyygEdvGZKbPV8wGZjwAdTwsIVXH8+V9Q/rwQNT6RY8+m5x9yXrJm5CtHh\r\nnHlZGi8I6P5zCYIbo4feDYsajlHfFNvKm6nPb9QDr21HwUY3YJHHkp1Beg53\r\nPU2sn/5Hndfw988zW6DqHIRt4MjQDqeXL7o=\r\n=Rcep\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-direction": "0.1.0-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-roving-focus": "0.1.6-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "02b5dee2b36bfb4d4563637fcd37166275e6845e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-LpNG/zhUWLNF/UF0oYHmt5FrKLNQdITzD0nZw1Y4T6kGpVxj8pIOdLGcQCM4qtiT1Lbk2O1AJFaKPnFeJLfuIQ==", "signatures": [{"sig": "MEUCIH5k3AAKL6V5hOHwM1FvEkFF4Dfr9TzogKsPBG3S6di8AiEAwklR00IX/3+tbh3aj3q6q097UaGXRiZALOnlq7wqOC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqx1BAAix3RkrfPR+AIDFAaXn7tc3go0CsGsN9dZFnL35/fBd1423gs\r\nNScO+6ZDea971tYLwA6gCwa4LyVDYshBxCvf4Ch5CFaFkN5X2aadwj5/liMd\r\nYCBHUcbzO1JzQrNF7XaC0SyOhPAi3lv9XE/ghDjc2L5lz1LrkouEAUIwTZ2A\r\nEdiKCfRPhHTZIaK1Z5MyrYbI4kbMsFMgLY/Yoi/YKouTceL7XxF73YVa1yal\r\n/7acWIVveYrK/XqG+5re0GT8Bgex/yeR2gI4xYOg1pgLuq2Y+Hu5kTx2as4p\r\n04jhhZYVuQBHzRiRy33RaCmgNTKZAOIMfSMrojIulBoPJ6Fr/5o3S2+ihpxH\r\nMR5ZlPWvn6dkMB8hItAi5QW6L7YPcD8m4Ccaz7ZUpqyP81onSDjhMprWaFx3\r\nugAl7i7vim0e8LL5LTw0cDxpK+Sih76t7ru4QG+qLn1bmy0H/jlLZOwubwGG\r\nGI/w6EnKFggID7FcjVxpQ8fpiWaNs6cvO7T/sTp/we1TaebqVR4jBk1Lp4oM\r\nhf+NSg2blT20nBE4GeL7hbob9uQAYV5juy3NbL+RieBmTngCHr1rmblOuFK0\r\nelqEr3vD0af6nFGWOSjW8KDTAncNGGr8xrM+hnPZmmkcXjYzidjTDqOuYVqs\r\nL8PioIzWHwrU+6acv8GpXs/ZJNHKBJFkCJA=\r\n=Tc2a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-direction": "0.1.0-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-roving-focus": "0.1.6-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e34ddb5acdee5ebea8586be3b235b01771cb0bb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-zqEQkboEQmQJf3BoVL3czxGLvB6Bba2CXeWF3z9qDXCmScN1gu1gMrzAVBDqCUr9ZJ5lwncHQqYNaGkZc5a9mA==", "signatures": [{"sig": "MEUCIA3i5kvtBrj7wIO5uAHPLD2GLRvIsr5/mbPk1VsyVCgaAiEAl2tONKoLLXRG8Ro0M1GDM6ix/t8SDHorUnOzC+HQiiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWSQ/+KdpdRDWXDxft9eEIQQO146TfBGU+C4dmyeldZR4XwdYlPeMZ\r\n8pnizBkCGSeMtFKa6tVcmwAJl8lAchLrKbQkfSWSQDOSTiXVPTSCjSlEiuk7\r\n7hApEc2rrZiCvz3D5v48fZd0m4BIKX/9FkuEiqlRUMePUgSi40syqH3QLy+8\r\ncY0Z3a0Q2psZuZ8f+MgTzgSw0IHdgQIb3vcMUzItgS4LLJQwffM50h9y1SJY\r\nL/Z8DnOfxS5SZooms8dyu5W8rVPJMx8VTR+i67SuG7YHzQkLoWXuVXBP8Cy/\r\nEN3LpLDd3fFGADu18b58spPXolHobOGkkAk2i8hGCrH5+tSi/Vm1X/CiZb/0\r\n9tyTEkFVCqM4OHOpeT1wJ8okYwGflrQ1Wr0bV7oW/ELw34y4h0DewVF1F+6C\r\nU5NtheGJ4CgjnwduylZ1Z6YfQIZvMBMXnyqrWDjByJkBKl7b4nWJ7+q5JTI/\r\naOqQIaq0MZXOB6TIJDkYccJIxmSTPucgktzyLHVlCAIrajgU+nA/2H1MVC8j\r\nBX3HVYt9WriJhSDH9kncgZAQtOA5KKgsJeOgstnUnKOBQ249w7WJZQeTqxSC\r\njaPyLwLVAW0QQjSN+7bvERvJYCU5onFuzTieH5uOI7ppoQIgP5gbbBsBbkEM\r\nwbVSxPLVNS3QEsloj4gyH7mdA6ITJD/5ADA=\r\n=Cuc/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-direction": "0.1.0-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-roving-focus": "0.1.6-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0a87b920196d6f13d00ce3cd86b1fcd14d39463a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-TiROqUuaABKkGJhMKSJsUlmoUNuDrHflYgslQgd8OfKKzL7M1FkgLchXFQEsoU7hxwhgz1adM7kjyjKzqpQqKg==", "signatures": [{"sig": "MEUCIQDzyBv6MEu+2HGRH5un2Q844l6XNgwaG+xpJR7BKQzEzwIgUSLq5NdiZ22KFahwIPQZsOwNowrh+l1t+qe6/0aqA+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqY9hAAkMCl05KxBliK+si1Z6/uYXnwgjgFAESJn+q8OL5sUFn7Dc/a\r\nZOblTna8s8ETFrT4aMg+1cROYyF0M1FLDtL5jPppmHvUfPP7WkrKSxqbBhat\r\npnFKqG8id6+yQePP60xvXjNNcvcmLE1nOFQeLYHdlzzrVblOYLehiII3qNYz\r\n2QGOoxGHSa2dCAvUKrjdKN4kp2dtAUo9gfHuhMFvN1DPpF/Wj7QK/jZ+qPG2\r\nqcrFt/4NX8hyrhtNWaFWhGvezAlfHTB/mvaH1C1ZFXjsxeqg2KL8Al8UMAbn\r\nFAyy8aTB1Rst3ntLVX03D4O//QQpVlvFaHGNxJMkJW2D4Ry0BUhE5ON4xDYb\r\nbYeIxiBYPIzP/GH3W9pthovpbWGAAe2nFVhJVNV4WTJ4fjYMQhkta2yOdnW/\r\nOMRoUlB1lvtZrYxUSZ/5jCE4QdNYSGkTPSNt5UthHxsgKSWSOmEcT95R3lAd\r\neIrVOcL/zMsWjYB9AGpvs9gkpfmEn48ukz1nwgPfhvzcxpXmu4DzzGFDhhdr\r\n/t4C+joao1kujgnKR40RbMLtUM6/wViCU+ILlAkBePrb0yb2M8g6THZXjORN\r\nc0xXrWji46RMvzL/IeTnP4dNpPhqfKG1LcCQi3BkOyfWX6v0KXM2KF1VgDfx\r\nAoEVFpEvzxUXtOC9pEtIr+20ZiuI9prgfjo=\r\n=MfSd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-direction": "0.1.0-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-roving-focus": "0.1.6-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "be27034c39eac4bd07d0464c07f0f276ead91207", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-IOPHJ7vnU9PmJTFV0ckgyzqZKsdqv612aTkvYrEgQPXyXemB30efy5wfHjc5RzGSDGjQt5x791BAJlJux8gh6Q==", "signatures": [{"sig": "MEUCIQDGFEnR5R4Ii96MBKZZuyH6f/tTeKhSZcIgF5QbhAeszgIgAiz9rpVqHhiNFoQuWO4SbkSH6dAHolCr7GXafXc6CnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoImQ/+P6FrZLeLyHdxn77YBlLIsYC2Qrlf7I0VkmX3fP2CsZS1fRCi\r\nihlLunuI3i3XykpoEw4WcmQhrMnFUUGVqIDk4JtkDxVPqaSg/DEdy2DlB00Q\r\n9bpWGG+MZMe5aDbpjA75MNY8b6/EaFhCVx9cCuLxGGhFN1YC0/roQmqY+Q0C\r\nn70X30BPlS0xeio9ybPzXwGrqbLBA7T7XHtlXGZN66iX0EIllR5sbqcElI1S\r\nOGYzsfCZKnD0eMXqNygpk7dnaQOurq3q/vbJ/FQRCTinBbyYtXbN2LnekFid\r\nAaH0BF4DhexmFvV0Ly6Mvm7W6VosWcDwKOs58HxOa4/gnUCHJ1am/FLDpgJS\r\ncPz8ezIzzCvU49ouY29RgULYUoL+nXs7p6ybTsk4VEWSaw3nLGKYFbcRvDkX\r\n/s9mkvh8wZki1v8glabMK0Y6cgvQNmvchKcqoD8sZCGD4Fy0KSI9iXSQ55NI\r\nM52nUTkrlTrAZUcvLBzcSZC7ydqksp1Z0PvRdZUzUKcQo1Yy2V3td9n3B4rk\r\nydn8xj1F/drdVQBB+L5SDukAWnC4RBlc1vYZKxt7JGJXEzHWfoagvp/KMsyL\r\nRMm4vdFyWFCcle5oUKfk4AAuwlGziHh54WEvAACiJmn2g40ngLQkJRWQgKBh\r\n/i/4UnuBnU+eZQHVGY5hmSdxuwsemSOAGB4=\r\n=Gv2L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-direction": "0.1.0-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-roving-focus": "0.1.6-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "00dbe16e045e2fbb549f83f12bd833883f491e8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-frlG0Y93ggY6X/hwrdHZZmDnJXb7AeeECRvkSKjqTOIe3UNSI4jHgev/iEt10IbA9EU3eqDqd18cX9imbBWoOA==", "signatures": [{"sig": "MEUCIAYxyEaL3iCjuTtrsc/5U0DdDMTZrZGoI6zskuam0ySTAiEA2XDjAsP4/lxh2H0+X8PhTux49BmJMVGHdUUKCa1UrxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoifQ/9ETAZ6kvd5phppCnpFQpoZKnpdzswigHPoDTLVzaR3xTDa0Uq\r\nlnfrzRIsOZ+bH2qvgKLpIuj6xUUzpxc5MyD8RWzf0xV2oF2aU4y846ddaN0c\r\n5AH69644i80dRwq55DYK14PUhUhKX5KIroq76oQiW0IwnCjesO/9KM2BzFCY\r\n79PhDHmCpBwtlHa1gbywJjXFn7de/QMNm7KkIb2Q8zSZJdzIXXi/MuS7iMg2\r\nkzCGovYb3jqJ9Ix50gxStHsNzfS+jXSCBnKI3CTLNkFlcUV/a2Ale4+Y2dQG\r\n1tn/Qom1G0HcKqU74PhgbuMfWPj0pidsXKyfy8WSpWS52iiU4q94/AzassDw\r\nxny3klOCLY6SjdAosM7p/XHnrNiaPoBJxT43B4LYeVmv9OQAEry6HbCRUT9U\r\nQtP3pu584k4kpccRqDOo8AizvURFfHxEVyWkjywgax3Y9M/vGTWH1dH8rKRc\r\n/cdTpc4x60SLIQHQbWtPDICK9WBrLphWfYaAnZyOaKijXAHaVsWfL1Ei0IEn\r\nKtPhWS8O+0LRAcRlgb4uS+EypvYAG6KbwQZxdKwxqdRB8mt5Gf+sABuqoRZU\r\nGygKKFcTsFrqppOlpX59p0CeviQjO/PTwUn0lh7SNthWKRGSRL+bxfIyI4FY\r\niQWQZorUF7vCd0Qt95DmcxduD9mPxKfCb80=\r\n=wREF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-direction": "0.1.0-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-roving-focus": "0.1.6-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f297769f375302831297b823bcac661a22f4ae56", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-kE++VtbtLiis5iJv0cH3oQoHchoiunEKvWRTr+nj908gvtB4szYWdKt26BO+1PyxVtfbUh+kvOU1n9fEYC0QBw==", "signatures": [{"sig": "MEYCIQCimaMvIsy5i36UMBAAKwIG2yo7/o1rNPKVPRh8mit1xgIhAKj80efT7Zmud/bRLmhxJroHM+8TAlMTqK4FVKD4XuHm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeaA//SdkAPq7pmWJqGzy1rss4iq8JRWjl/6anmgz4N3H9ENXKnjkI\r\nOKK2D33d5dSxIAnoXhQ1Z5t+P1sdo7JgsaQ+Mf40s5qLChtWa6FNJGXeSVCx\r\nW+XbDK7IUqmfBdZRYHO7LcO74oAx2c99OFPZrreFAIs0Zf95yHnyuxgJv2n9\r\n1ZsG7eioxcLcpN/g64+dNSRSd1V3dCfXAJ1tJp5968HK7sEINFsBFqUNgr6+\r\n4HL3ElViRRprPFTax14UWZVNfeyIDeeLM/HV+XS1uJJl8T4OWVx+8EXioO9v\r\n4g5da7Vj+3j092d5SAJSSx5ScB4Id30EYGsOZ1cM9u4Vl0ScBCOUHrCAeAIY\r\nLCsSHlqumcppwYRn1Rf5dfL5Chwry2Klh+dP3R7aem0fIIlQM1TH3fTfMmYv\r\nT4SZ/qmDO7m4cBB1fDtmV0fvIJDkbJLwGdpdytA6wBI8idEVbekVJye4Bf+K\r\nDrmBdHmxivnT7cE3q1KN2ZcrDQksf/oE1JMQ9SLJfR+2df1dk9AHTHgNg2wy\r\nuGHTysa29HTqywcdDF2fGIKVN/Mq2m9+UpVLtYt3D8DdJwQqJ2oqm2H11q2P\r\nJqEsrLlthkMyTWueUZdl3fv+NHLUgKYiZRJxGjauE87F3xdIj6fphW/H2tNp\r\njeeedMFZPLMcO6l+Ki9MaaXzNmadnC93Zp4=\r\n=85a+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.27": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-direction": "0.1.0-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-roving-focus": "0.1.6-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bcccd28dc2c47c725b4975ef82907a783fd5a512", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.27.tgz", "fileCount": 8, "integrity": "sha512-acevEfg7habqPOlRtrM8Rr5K1VPY2pqo+ajMHmzn+aO7anxrBNeFG5SHQPWSOmRdCHA9KHMfL8Ej3gDQ5gX//g==", "signatures": [{"sig": "MEYCIQCwVt9L0S78Ar4QQWBlByYRG5Kqxi8x+vE3lPe7HSGu9AIhAKdzRFx7T00i2vtLe4OMnyg4avMjdvyrXb9LQMbjIg2o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLiAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRpg//f7/X/7g5c2vBbozp/GoH0H4dLx1EHqyu9qOOiHgruXa8oo+9\r\no3UVbqhOy0ItgfT5dvPvGNzWkI46s7c3dq65VhtsmdJQ/ZqVUNh6lwCIM4EG\r\nkheY0nv8MSgPFbdxmp/HDcwBr96JUJfDkWl/I3rmNr1okOXET1lyoqm1myDT\r\nBYFSCMQbYRMmFEdMwvPytvuiA6fmn8zb+AU+furcz6trTUdl1YqeJLKcDS4t\r\nWKbP2kyuFsbjJsiWlDViAB/t2tWr0s1CmqbfgHrlGFLMcWml3Rcsn4UlN8q+\r\nJqpl3euMXlxfJSVjZQvQe/mGZtYhF2MVe/DrU4WOmgHiOo+7l8JixQi1fXtm\r\neQXhO9OOPddHKwpRSRsXxNIb+NCZeD4qEi+f2EcjazUsZC+2GLU1teqDW/LY\r\n7X1/8rg8XDZq7/G2h6WNYFhMDrd2AZoFSGW36ht4CEevVW9KeVNiAu3YrVCp\r\n8+QaTAbdmuK+e0ZbK37kr17AgvERIZI5RbY6cKgUdjYxVpO8giHoi9SkcYKS\r\nG4Piq2LF55FhrbB9mgbjq+UpW5xtAp1aTkbcTL1sZ9DMrOWvqvstmhQVDK/v\r\n4ATfD6E3S9orSbUipI88oGbrN4HXzgYEBoW6+JpULU9CmN1W9/Opaeg9EHLq\r\nPagFpQNTEIjgx9FHt7kQjgKmJN8dIt1o8Vo=\r\n=ZzQ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.28": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-direction": "0.1.0-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-roving-focus": "0.1.6-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d16503c05a66f85b62c62a9960f44a2f4344d4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.28.tgz", "fileCount": 8, "integrity": "sha512-aQx4C00mHYxHw35+ZjJDG1iA6lkpc0oF6OK02jLhfQnRCoWYttup73M1+YdKmzB84jzRHbuWPI/CC9u9T6b1SA==", "signatures": [{"sig": "MEUCIQD527BSCxcrlKU0MHF1PeKGh3JKN6o8ROnN5hGHpEivNwIgAjtQx0AA8CeGGAKFnMNSu0TZ9Hd2WBB20lmt/n8zKtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj41ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDZBAAiCCIXOcJrshII2Pid6zBoNQsjt2M0iR6Q0fUbFwbkPttiq9k\r\n1OO0kLjSEzD7zW5XRR7cixzI7yjKbzq6NPbow1b7dcM9XCdmwUx0aspxYl2G\r\nVbW+PgkdKbbboPbDsB0CzpcvcIgJEotz7wPk2NuYxpieKhWg0PwTajom6sPV\r\nLUKUFP+j7XNr0mwQuIw3/5+Ph4fG2IBNm7pPlLrzSBCgtIJ0Vbj3BR3o6kK6\r\n3gBxPARvV8vdAKRQBDdVsmXKPuFu0pPvllmdPduBcr9Zpfu5cJAgAP70GTqM\r\nML71HqH1Q2W3ln3jIXl5UiLSPEwYp/MZDlxfGhkcSXqwVFnT3WLOxNZO33/5\r\nhSbFC7cB0BCRGB2I7ode6LmTkU5/AXlFz77hJZpEApUj3VhMSftCu7jK/rGp\r\nJPR1GONtRi5nMzUVhfgdMszicTWUcGVJA+hHuRvUxskWJyXSTLDz8jVHvzmO\r\nvpCs6WscjEN1ze8RaJMM6RR7UINVlwVIDtmSHOF9xv8IaBk6VRhaR7nYKoch\r\nJRSMGPpoHniv8pPNQKQwyuvEqxiEN5G73MOYwCYqiD4FYdX+wfkuMdQOxFXT\r\n8bOCcKnCWKi2Wjj+AChVsGtKooKOVGwPemGBoNgHJQFh+75AnBlAhdxkQc8n\r\n035QN5M48fmo1taH7W3BOzLhmg1xE9c7Ipc=\r\n=E5TT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.29": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-direction": "0.1.0-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-roving-focus": "0.1.6-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7c13f3b52a728834874ed97b3f5bd48f2f7e98b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.29.tgz", "fileCount": 8, "integrity": "sha512-mLPnfVKmovjOe3BEFuGB27zvU6tFEi2sgRKhA0gR9R6BGmmWKX/SIfDGKdJHXc0BPKBqP0CGGYEnRqAT47JBCw==", "signatures": [{"sig": "MEUCIANRiJHs3wpGrKzpT/kkPNsXYAjnWice2VhZGNkCV80VAiEApX75KiFk4PPTTcwHqHglCLYfwYK0unoE/xCMLq9vfzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovfg/9HaEAUX9K255B/mRplE5Ip9m91dd6cSJU512AfB8Nhicoqx31\r\nDA62fD3Pi70td8voQWaXKybfpfyy46364C0PhgzUcL69/QWxAh5lYs5vD8OA\r\nACvc0vPFGxqad6mX9/E9v3FHfIi9eFp+T7xlg1KCFRFaWu7D2xp1LFCiwsSO\r\n8mjyBRpQr8N5Li4aWfxck35nEfCSBYrBbxLNwNijII1XUkP3v3zUnCNArBI8\r\n/domPzMYQJ46JJSu4iG4maS2jMjzV+5OilFswJqr6EdK5iU8+WBe7dsvewPi\r\nqHAkzwjNxsKl0aKCRGltYcv68PtBdOa4lq0RrNRI3zsqYBRlenYMPsjkKjE6\r\nGuXMBXP3WY3immC+H78W6klUQZdKfhZ+W0nGIkuuJaMHlsjOqhs7/l417pPJ\r\nDVvIS+l8ji90MmKo4oA/qNvDdTyilyyt2/WuRgsJRNpRfYsgYtfzP5Fz8TUl\r\nQiGTozNSkw2XdUfg3G7r7e17F20rvATtlUVOFvYJP+5xkT0SojjEiaA4G/dM\r\n4lz+wybW8XBalTY7hSsl68Qi3H1dn207p60v4T+DQfSq5olWncxojuAHFPSz\r\n97WUlFLApncXESDtyVp2gMgeCF/xu+DQtKQLFST1oPKoyx8AT6n5mmP5eZOZ\r\nLjz60m4ckldY79/2Zr/Y60gKZc7iwniLtLs=\r\n=OO5s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.30": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-direction": "0.1.0-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-roving-focus": "0.1.6-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "385c3d40a41bc8a777fbcd95290d511d44ee7673", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.30.tgz", "fileCount": 8, "integrity": "sha512-8QkLcLDluqqnh78gLwMidMm4bNlJxIGn9IN1otdwhyEGLnNXiUQviwCFKypDyjvCn24koQFNwu+aedomEwcIOQ==", "signatures": [{"sig": "MEQCIGAUmswzmP1HuLoSa4nWnxQc4gS9S3Z+fRM9oByyn7XFAiB/FAtMdz7VGPqk/WIoci2bz7dgULdvJmygztmXq8jNKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+cg//QAN/0dfONYlVPXeYjk2c+fAO9bDFMegWduGHg1k4qQl2sG1f\r\ns+CkUSuF7EpdHdJOye6vWmgyupAy+WISncogN5X2nZH1PQVDuFIB+L44YAjZ\r\nLVm/0kmrAjz0e9A58ztytj1Wrp0yOCOP4auFcUFhj2N6FqkLlDhl2XqHmLT0\r\nSpdKl5NSs0qvt2pTwZL1bj2lHOVVNfDlaBeME2K5mBuRg7TyzyORaxC2cScg\r\nnFxLnIKxqVY2TkimZ47KvAdXd63SufFKakeI0wozoZ28tFmpkZH3rnRcuKoS\r\nYK7QuWie/joGzLRO94sefzoT/ARsdJ8/6ZvcV3lUPWGimGMjKuKjIOTHW1o2\r\nbNyzzsVP7tr+kYVoE3JW3mU3OgAc7lAzWysf9U1OO7JFbNiwAlTKQ/Nb7CXb\r\n7167Z3fwI3pP+eHPDwJ6Zg9eJmtbTFoRw6pkJ9Y8UhlL32PZxTEAHtULb8ij\r\nsrZ0RIM0URFYp/Evyc8B47cnG8LJj1aK0JRtWG2XXmbWJOAssbi/HQJWvKBu\r\nFXnKztvWTN+z6qBlw2qOKuNC6zYk7AgTC8Eh66cpEZeQbwvdin6AEEZlxArk\r\nN03eP9hoOmGaYZGUmgYy6fWwkCI4g454oTyz5K0h3wHjElY05WEDIEYYtISb\r\nGeXvL6m0UCxm0xSBy8Oq1P21Sz1qejGEER8=\r\n=4vqN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.31": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-direction": "0.1.0-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-roving-focus": "0.1.6-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1f748052d983be13cf10603bd6a1a44ce574381b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.31.tgz", "fileCount": 8, "integrity": "sha512-eKNEp4y3+fFJ6ZLMD8sQx1B19XpgrbCGieA/y6OubQv3MCCRjYTqOq9L8fCfGeWDNG534rICj9OsjcmE9voDeg==", "signatures": [{"sig": "MEYCIQCsiO2eAEuB290fkOihy+H73vhfK7AgeHg5ODaPaBNcJgIhANJ7Pd37kGqCUG1XPfclow/hHMNQ6Z8I0tdZ2Ft5PXW3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildN/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl1hAAkQ1Q4qoFZuhSblwsELzDAe0FLk7RSbeCVoVgJZ+DQCKggB+H\r\n/FyhFpPbfyF3eT0TkXqLh2X8aDWZGQYnIjQMA9KZgw4B50WZXbvPSH4xp86y\r\ngywba537lNamv7RA0Iztix2+3ZnogRT18NNTIDaKM5DQFf7r3c84MuCrkIZM\r\nmo/KVsIRULESPi+xE4XyX42QO+lS++IcNU+eE3cZkknV9IGgybbtJiDAdBf3\r\nFDmlp3E1sOVcZac6Mo5i7iVc6HljUxSF2ACJOx+TLyBM3nA9dprue2GkZWBG\r\n+9+eLqzeqRtZnY2ehTyFUBlJvbDFodsziI7NYLeF4+o6bgVYcFj+FxjirNOL\r\nTW1eQZ9wWcMIKyZ2nD6D0eKay1NpxQFZHkcqtuuqKV1q6BUSg7CsaPb5caoW\r\ntzKGn9aMj4P+UGlG4xF30lgJ4xX5nkS1fm3IPi2YJwu7HD8YN9fdokF7gL8M\r\npDB4rydV9n37gREQt91bypxQ1HJ/qZG/Kxf3OVXqIogm8e+mXWyL9ghZkOOP\r\nTu4T0Fh9xDinDgqdyk81hPdVdyDmw1RQvQzxfvlO1rRxyRsLH/v5MUrkLtyn\r\n5z4v9QxIG4PdsLUbTQMmHyESeebC73i2ra7GJ7w2vrs75CvOWBazHyQ9XDKU\r\n5jUMhyrhbqtYECNz2V0Bd0dSQWIE6CS6ITc=\r\n=DK3Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.32": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-direction": "0.1.0-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-roving-focus": "0.1.6-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3278903fc3e81d4dce8457b4de2446d899959824", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.32.tgz", "fileCount": 8, "integrity": "sha512-DH2mkRsR0vbQulSfWWjG6+ejrTx9GX6GDy9bwOqEMur2vSFM0HmoYSKdjgZrzaDby1rElk3DOtaJKAxeOJOK3g==", "signatures": [{"sig": "MEUCIGTi7isi0sP1uenBmSb/OB63KYqsuRfEfc+Jiv7k2x+7AiEAvQ7ENrhIoKUEw4xiyx/o9bqpt9Oo3ReDnA5DrOZEEKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildr3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmouew/+M2YMXauiIjVecaRxm118nXLbxxdXKivtjpzwBPICsHDejvxV\r\nHmj+fasA9FqUfu5GzcQ1SJiz/1hN58lAPNBL4m7kZ5FtHXMTFHvIgLqfa1ym\r\nLZy1qaCxhFScMYdMKIXqh4yqQImNf97mzMV07h5xG+FeGzhkfTwG0zXcgaTO\r\nFZf+UpkMEut5YIjZrQQAOv2fMMza0RnnIqfdMaqfLHaRtpZeY6VanjbDO/gS\r\nBZ9Y6HWHp0ehgY3MhszRFE9tBFd3OMjZ0HLwxoNoxXPei2qewjkJtaGgtTPv\r\nxWcW0C+s2j9F/52KMpHcLqN0rAugA8y20TAELv5smao52m+dWto3iX1zfCPj\r\nUhlSFkgYHBpBNNvODPt9BNoXs0fFp82tCaqupBOgiNzzBcum6zjEb6QItGcm\r\nu73w+jlJPxUYk30C73pmRNGTtYqXgHKMuw/DAPvOiSKnHLoh6G1+HeHvhzCg\r\ngQlMxHMst2m3ly6aWS7pDNeTsXcZKQymsBuKH3KwXsT3AYnC2xF3zy9zbTLO\r\nicybPh/ZT65CLkXSsGCULHE2Bv9TwLvtExLXwJ5EebIBdNxk+iLPbCglgH32\r\numBMC5Bw33Hrh+QhzGjKOQzGepjHfjQHDkEbr9+8ovZJ1Sj3JHtvZ7MTiVa8\r\nFfPbJh6qd8W+4fY1MbSYPLvJjLQjEJQdgME=\r\n=R1hZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.33": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-direction": "0.1.0-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-roving-focus": "0.1.6-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "255458c3ec281c7517798fdf13899dd2645e108a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.33.tgz", "fileCount": 8, "integrity": "sha512-LtkwbwU6XnmwGhuYgzFJx+bZNU2JGNf+i+flU6htBZT+MYPIy0yWRMH+NVbHjdVzTHJfG6G5RysXd2ARTEV4NA==", "signatures": [{"sig": "MEUCIQDjirZQ3Khn1daubODZE+jJR5TmJcy2EdVZMOSe9W4EuAIgIFol1zgewuoZrcBERpJyWgVprHRmzwIokmle1XoqnFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7jQ//b7HKM8YkMYbX9DBDJ2BP976WSDWLLjEqoHSrDtvxqnF4IZpN\r\nDIeV9QAfzWw+bIO+eBFJgY7+b4cHQuY4Ua3Cbkt/YC8N1kf9vGYU6NXOmqPi\r\ncm6s9trohJDEgUmyTGgqCpvy0IvirJrBBPoYJ+rTq7erBPMblgkOBMOU7TQU\r\nKHtBcIYkGRgRrLkWDgMfAT7vlWNybiTQJx+CxXMzky/xbdXVIoqsj/G5/hDX\r\n8eiaq/z2yH8r8PC/HbXfrpEJ8JPx95zhKVotKn3MWH2OZoJBaUgyKE3Dnhvs\r\nQSvZ1EALmR13/upPxdHJK3RYSyDTp0J0oF1G84dUOiARYsRdboeOBjtmrY3z\r\nQrvQpAJAbwCDyQdj7BALD7SHKH7xMqC55m8kYe2b/JyWU13s/0tXNAkcBiuf\r\nJ9ed/B24cdMDWob6HjP27MiB4DhpGDWfrolu9fsVeYprIozwm2ifOpx/rmGC\r\nbe5GvC6wscmVy8tbE/O4hJZTUO/0NEx6EL8vUUZFmJSkiFg7O4xKqoV+Mj8X\r\n5QxKNeoDVJme6ve+s7Q7t2Il3U9biYAx67SnnV1ZburPPcXrCXe13xzLpixw\r\naPcwZFF56trVLzoX3lNnabxDnFSpbtV6YI0jr/qHi7fpXS3gwUlJVX1WvPlC\r\nUXkaDne1tEmdfRqVPtxACwFoNmOCTOsSGH0=\r\n=g/Bd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.34": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-direction": "0.1.0-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-roving-focus": "0.1.6-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "faa07a1c0377991dcc0803e7fa75a9cabd18e97c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.34.tgz", "fileCount": 8, "integrity": "sha512-12Ne2yzjEvA8Nv3RbPWRPv5DcnwfmnhYHpviLAkaSaJxPHTlknZRVVzpcuC+aT5w/OW2IEhBTubaZ7KZVcXPIg==", "signatures": [{"sig": "MEUCIQDOCw4pVeS+kuGayQnuYzwBPi4d8IGhjhp5CIauf91GsQIgF4oOZeYmP55KPRIP8wYloft9HYPHt7Nz+zF21uQtn3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxNRAAmH2ZYxHfkSbd+gM6247tRo/qZguTxJoI13pkvrxRPLcCbPv6\r\nXod0c9C2nsWdZna5PfNslxXMG3mbr4fWD8i+sdI/lyGHdFREjhJTp5q7ow3Y\r\nnTTFY2ZKTGLdNM8J1PxVBMCxeM31VGDSxpXdDkymV2oOcLQRz1qMC5eTOzV1\r\nyJzxLr8cZkI/gBZH4M2yaBysuajUms3i7uY5RXm7kP8xLEGfrmLWB+5UM3Py\r\nd517FuLW7UQn0Yl/fQFPQHUuS/O1GOnVEJNndkWoBC12U49Tm6fDrJvS4Qv1\r\naQ5E2gwjDrO9hdSbF0CmPCUYQNb19V++YsswCAmb3Imk4QLnfqSaY2NOJQfv\r\nmf6k6hSbp0ZgrKqRdioeD7WaXPg9W+u8mKqKllnBksaRazFRE3bKZtLjfyTT\r\noam1FvzXRSf9Lgjl/OVcurjI7mcgggX4E0nkgf5Cr1n92t02FeI3nM4ShraL\r\nZCeJApHDkxElDpW2Xn/ngMc6SCaBGNXSMqOYeUKm4KAc1smOflWxE2Kvbnhs\r\nNFVtnVSsBj3ePpOFTmOsbHvKQ4Kfqgzt2Wj3EoKNCrXBskRXBTFke+JX6fLN\r\nuOd0g5nTeAkTA4OsG0SmztnvEB6eIMy+TGcfFCgOHLtS6gsKme51K2W9qzgK\r\nVCIDTDOJBvmN8L952G5Ld20+R6fCdSKCLy0=\r\n=yigT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.35": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-direction": "0.1.0-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-roving-focus": "0.1.6-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0cd328c1ae5eb4e739df3f750336d934cb66f1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.35.tgz", "fileCount": 8, "integrity": "sha512-Fqxu+xwypxzGus4edRysxsi2cvql2hXJpNVvNA0zRA8jrxAS87a79O6Q/YqiEbooCLk/JC8opnTakCHJpjm/yQ==", "signatures": [{"sig": "MEYCIQDFUYGlEVofdUuZY/2+ku6foKmFTbpN+Tz0FIxDTBwkfAIhAK6sOR+U60VytGxMz7WALDO+45JxLcdK2yM4rNH1moTG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcLBAAkVuK986jbLm5is2QQHN1ms2DaiJTZg5Ln19SqlUcsa8kGr6P\r\nwffEODdp2bRIKznA89484Vaain5UZD83qhtaWPWDilbDtyig86yzlZodkLJO\r\n2wkVvGOHoOfdwsP+Ih41WnI6W4o5zbeHCmxlJsVwtdUtmbHmIkKc3HkrWwoM\r\n8QzGB9XyncijUjpJJj+/vXh0yiQxDuU7R7gJqc+GE2KVBsQJ2NgwiZ99bA8z\r\neNBMZXF/JP5EspS/XyXva8FINDH6cfEULrZqxNMY8lByEY6JTHK31ywzlzxb\r\nfucLIgJdyhbNbHrG2NKEWge/b5NLmcBbxt8zSFdQ9K/tvVKvuKRPLtgqozxn\r\nfbwWHcI5XjoPVr8BL5QImKH6AGkw+bGM74bq1LH3xzORD/Y/2JR5TRFvqLrm\r\nnFYNbiKNMghIbsIP0oLqD5+JVc+xyhJHwFyhLjZiELjvrV3wMnyjoNyXdqk9\r\n34nx0+988ncz3ZGcpKcfHZ7PpzlFSCn48AJKG9aGN8lNAivUJZCpIFFcyMlH\r\nVfJ5yV4TjsAHyJ6Zg+/L4/3UKYX4umCJV0QpoHE/7jD9kQGRAOHQiq0go2rK\r\nxu5nmVsgGczSVbjGCX0AOiq3Pycd5W9FBdd59rhNzZbl9ATTIOTJGlxHoTdG\r\nUE74ivTi7hO27MENBZVvvb9CIRDzuNQtX20=\r\n=f+WD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.36": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-direction": "0.1.0-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-roving-focus": "0.1.6-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8854ab19aef3db9b40f640f29d276767600986b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.36.tgz", "fileCount": 8, "integrity": "sha512-zxvi/U7e2MH/EU4R2XU1vXJD93yJ3DFy3nT45ybCdJ4d/eYvxFqbuwEJySOIeEyYyih/4pbzFagIewIyk9xrCw==", "signatures": [{"sig": "MEUCIQD8a+OMUYzeGgLHAwmp5+Uwr46IkH6zL3lpl4hC7XWVAgIgPdtxWPdyjhUGp4LUdqkx0VgbOld3zRwTHWHOzN3zJ5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodZg//X0WUIcAv+uBKkeTv+kqGy7WF0TCibsq3xBlxmAkw8VEVs3+L\r\ncpJjKbhVhQDpGR4gRBQWCn+9YuVmWGCnyG3V3Vsj3k3SSrSP+TLOtYfiaZSr\r\nwit9GUX4gKq0z46tG7Fb0rCZgWTPtYxxxmmLQEwkmhQ+Sqzic9p/ffl6YiBO\r\nouSmEq0aUzCCCMYzdisx343D44AgHQmJ97GMi8x/+BPX3WgxmPV3AiDPBYhr\r\nRw17sMx/nt3EArWh9UUlOwKJ2JThF7Zv0H641zCPL0YlcRis1Eg0uX1VLN76\r\n81N2dj14UAa0tlx+9a+YeMW3lKOzPu7LyKjqiZi/lMEGlu49JugL6/vJUfOc\r\nvOmQUU7pB1X4yI74QvAwDIOrhgxbpGD8ymFjuOFWRPRX5PiR7WedLYhp45NA\r\nr37yGJuGNa4cZkP7dUjKaEwDG/5tcnhiOg0bGTP3QeCCVIYDj9qEgZmQxXph\r\n3VnPn+DAJKQAGhz6S5mWrQp72bbNNDCMWCfrU0d2ni9ZsIadukKQPu7lv69D\r\nlFw7A5gDKllLyf2J92zevuozQOuACOVgacSY09+nz/QKsJ4+ZkHlv05y5ZEi\r\nvOu9g/VyU+1gfyVtfGBn2O/Yq5PyO9btrjJLaYmyMwuqFv/blJSTWeG4e/Li\r\n3ZvvGqLzsfwWGn/31FHSZHkTy+AWAMuXUkw=\r\n=FJJe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.37": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-direction": "0.1.0-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-roving-focus": "0.1.6-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4a12e10b095fa6bbe8738cbb25fa630ba29a2cab", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.37.tgz", "fileCount": 8, "integrity": "sha512-OIFKnPglgc1nRcOHIsR26k7MDq0DNh4OclHt0AgZif+CAZOzAlU8Zdd0a3Ivfe/80BDGhcN5O00bP1/5DzC+zg==", "signatures": [{"sig": "MEYCIQD223j8sbyqpkQ4GghmKPtpst6gWLPJ0DuZVqKRpfbbiQIhAOPL7Qn00THn/un1KkWXKqnqxVNdFG28z+3Dw+do9hWd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqazA//c82ou6P7/6kjzH1CBhTnpT2tHc+3SCTziRdRRMiwlMqqL8ZD\r\nRDc10AqHMBRtp32lNQpaf74nodBZRPad8qSN5zBjF+7gEtvT5i54KRN8egSI\r\nqVxh9Hx7JHQd8jPJ9BcMRHTo9g3JQnCBF4PHICoiA0acHtpgI3GVhftpy+yU\r\nLX0E8RPi7IDZasO4Y6u5veq35f5ENuw//e+G4wLuPH/f3LdFkx3F9pD49Shq\r\ntrzFEGL3eTB0GE1h/rt29b6DgKIIVw3h+IWxATQH145R1VVaZbo88YCR/PDe\r\nfyuxnAI9C5EbB6Wp6ZdZdoEQoOdxvB1KooRiSqoroGAUAWUAI8sslmdXpSWY\r\nPAejHzuL6CIkvhT/4kVX/hXjWtn13Ou6Cgui3+0Xlid73PusQZiQMxD1L0PC\r\nq2MP0TAOMPJrPRJ7XpJZ8WafUd1DMOz2LcfGteFByoP7PpaTZqSwfEE4Hmni\r\nSjEXLCskYyeX6AlhIhPs6GDlp4x4Ggc7YXkU9iybaVFpYLz6RpdNrO6pEt0S\r\ng4sHNjC1QPF9Bp4xX7KDU6YVQ1ODZBpcd10cWU5yb2QUMWuznblQKMfjZ7ha\r\nAeUowKwsEp4poACW78RDra8HE6mC00SRBZmWWO2iWdovB9H3ddI7OrGza7y/\r\nkBXBDaMHkSUHRjWBs0YjuXkIiwuI4JDRM+Y=\r\n=qwRQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.38": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-direction": "0.1.0-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-roving-focus": "0.1.6-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "da30cd736d7ec1e7dadebc74cd903b5d911a268f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.38.tgz", "fileCount": 8, "integrity": "sha512-pfZ66hH2cEuctagVaWhKIghsS55b2zisgMxAPObCWVkyZ9IduLCeVWWRtPro9BPBmi18MkCY0fq/cSSahFKGvQ==", "signatures": [{"sig": "MEUCIQCDTl8SaHRDffzr5n6VxvEx+UyJjgjSVKr+oADt+rR2EAIgfD4Wuixgr1oluS0xFli8ppqmnHnzEGLO7lsNwUuSeTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK/w//S/jn4ldzZaISXha/JIlfU+JeJaTNqGwi+ZVaCTa9ZS1bkCVS\r\nD68sk0DFgj+YlRm3b5Aq9iX7DDV85zST51L5QXZL1vgiO8+PpJpKbTMSi8+1\r\ntdR5XSTgMR/LE/7UYDllhUXdvqDcFBPxRd7RdrWE/i8rjaKTwWO2n2SrHlCo\r\n21zmx3XnN8dP22QMD6WvX9TyClgUZa49awgoEeSG7FwpNQQ6mAqGUPon2U3V\r\nVuejOLJmtB3nGPE0dA7SnERY23Wf5TvDltGNaA//IKkdSfjtGC2wnbK/suIh\r\n7lxB00gLOjPSvjC/X+ZW3qhr/PvGPcWx+oIre2wmll0s4R2rueInCdiZCP5x\r\nXXx358ozmh6w+KfsifvLmreYz1sqyX9Ui38Hbvm2UF/VqyBj+5suxyOP5WbI\r\nRk5Ezw2qO2NYqH2Z2lLJZ9l+NUKZT9pS/04Ec7cGiXZF7H1vVtyJ4wpXueZY\r\n5upmNnTudYTYWM4JEPAIguBlh9eVPT1bgHJV3jyIU4er0pT49laQfc44EF+o\r\n+J58yi7B1pzid+aNlR+LR+CfH3331Zqgy7uxCJWg0fB6uh+si1watyUWIL8t\r\nu0yLRR7a8dXcVhT9pta2MyXztksAiXpBarPd2zT2utXPe2+2GEVzX8iXnCZC\r\n9nBm60XN/g9DKdJBI1RSKNhOQ07EN/qLlE4=\r\n=MoNe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.39": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-direction": "0.1.0-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-roving-focus": "0.1.6-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a79da207ecb435507a0d3a977ce0171bac1b4bb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.39.tgz", "fileCount": 8, "integrity": "sha512-4ZSHzm3uwuSUMb3ssDHwcVJ1U5qnYoBdKwTLkdSM34KFPCyj1r72htAFlZ3CSYr7mdKumCdJjerhRHo/S30gMA==", "signatures": [{"sig": "MEYCIQDFGBPpx2Z5KJH2Ht1pRBPQfBLvTZvm2meAH9Bp0ZmIpAIhAPH8TL7vDhfXlBWe6jLyeqZL3fmxO+Ul3v0KSgA6m19G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaDQ/+Mctd/+KG9JwL7YA19nrCHwCfqN5G3GWeaVaLC3jJ2/Uy0A54\r\n/VM1tj7y3XF+L+xatm++IgExkojy7J6k/Dpf4gdsm+DW+RPBKm9+tG2uPeNH\r\nubrel/iCcmGg34Qu0HY+nIwXyeqzZQYhUhlCThgbW9gFMbRct1cv43XdbePY\r\njnDI+J0Q/31qcLv6MqCIDZqkClmKN+kgKlBVrz3tFmamdrYcC+IU2xQawfc7\r\npXZ0mGYHFOwq7fhUw0cDiWDyUrdir6cN8PHRyPJGf+PLdxfWJONKWJPihuHR\r\ny7uXvVBGBPt6SGnM9A/DR68ljPiz6Agbve/YeIlQqhUlYd7vo+i5ZDwTyYYV\r\nXkbHPpTF/d6IJQWS4Je+AAVg675dGLn8mpqYAmqBvmTM5Qr9AlE/ufBt4tBo\r\nGgglVVLF8Wdp+1WjsneEnbP7LAIhd8KB7ellzx5/8ghoM5JJn3MLYd51EfGC\r\nlwwb3E7iD14iN4Pm2G49Y9mTDjGZh2f6SBiycLTcNzDGmxqn827MWBqqYEes\r\nAsm/H987f0WVajtuKd9vpFpabdJ/iwqf0Ub4EqyOBhH98c9u0UEtlYNzqKR+\r\nRNNDL9aHs5LOYbfiNXEWxZVEcpB4fq3hkfvcDNeuA91oLnkAwDjIFNqxTmfx\r\n8w3gxgVRerMcji3bwuYKoyIFNQmJQsYZCJg=\r\n=PeH0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.40": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-direction": "0.1.0-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-roving-focus": "0.1.6-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "24248a4acfae234dc92dc5b6ae709d25cf4b26a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.40.tgz", "fileCount": 8, "integrity": "sha512-zhazfsj5jqpNgWBjBWLVWgQB8BQbCilUXPIMNpfgQBpQupN2q59BLvBFT9a/lp89ckc1/9jAXNYur2twVF+p5Q==", "signatures": [{"sig": "MEUCIQCQeaOnr0/lesrRiPQdhiOFttr4wf/ZHmabBPunQkqhywIgNrBRAWk+ZshhUq6EdhZehWmpz3Lvww9N56OlaNaVm6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0obACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEsA//bH79a4HltLJMF2EwF7F7yl6W1LKxj2fHxpcuQ29yynXgEAdC\r\n1jXxdeY80CY58DNxKgCJezSzRSN8ddgG7BAT7664VXF4PnAIk7GxlTmlVaQ7\r\njFGVXXzuI7au2WCMEwHVOENL8l09/43CPTle4KfZ20JNEeBWVWdHabGD3R+p\r\nhZb8Yy132KSa1b/1Md53HOgpknVkHEkZPXI8+4st7QCm7JiTrk6aXM3aB3C4\r\nxaWMSEwz+4gEp6N2d422D5wvXRo9154qAxZkUelxgYcHBjSzpHPG31p6llV2\r\neSaCYJO0c6mHGVXIdJGu1YkF5t4sUOKFtfVwcKpCkawb+dVRB8JavrLf5ne0\r\n4jnBQX1SZx5y2WE2u5g67hcpIKrwPeOL0G5D0ej5TIn9HssLrHRTH90xiY2r\r\nQ96CDc7uMg9q9B2Guv53/WeGhJvP41mFLaG66YGPILnlpDMtwyN9wHoJHySg\r\nT1qSm/utje+aJnKGs4EZ+SfX6cmzB2IV1KgtKgfoygEL9UmNquiHsbWqlDLM\r\nxn40u+9r2wW+P2h0z3rJ06W/QY/ltbsFhC04CJZI/uinybgGZ4eyPjlm45i/\r\nLLqQPm+7roUuRSCYWt8ss2LmMKJGRIbsUDOsK0HOV6DKai+frgT5rDUxoixR\r\nitHmtOz4VqMlmxwySed/CjC+JiN1fsebgP8=\r\n=7Z5d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.41": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-direction": "0.1.0-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-roving-focus": "0.1.6-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cae6d7343977b1ec219fa49e9812d88b08acbe07", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.41.tgz", "fileCount": 8, "integrity": "sha512-A8JFXjwXBAzU7vfzcKPI1dMR6rdy5WC3ousVuhlKN8n57SAdjL4ml8vSBZec0oF29MbOIBGoHzmen9kX6m/OAA==", "signatures": [{"sig": "MEYCIQDmjawHbZrvZGul9UfmGcB8HiLcGyQxOPKWu8h0XixKqgIhAPfFYazNu/G3Ga4lH3Gwq6fzaxpz6ThnvZrs5ZRPf+sk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZcA/9Fm0D+n3QoKTAaGS/HlvWxS16MwDTuP/jB/KyAFd2oO31Jycn\r\npdrCqN8KZK7/jdZZOM7/8vcsTYyLx4d1u6LtsCrgqdOxK02VE9qDv79Sm3Ow\r\nfNDhfFAaJe//mybwbSdMYz9YuI61Y+bVY7dJgIdsMqRitV7JeBwBLp4wCCP4\r\nwCCNw78b2/smP2bmyeT5xlzZvLp7eobM16QSQgBbzlQWGXunycwZ1sJoENjJ\r\nm5A5eQJ8B8coCQzXkpFt+9784n5MQ6D2UDTDtEmLleGwEo5tDl3yAQxwJ8o5\r\nCyzG91GIKmbgkakuXmsAyKntA1qvZHIyOQjPuE0Bcd6wLV4sGTSF4USvCxvZ\r\nzwigf4BcX2Cr/UDn9jNYzcGpivrcBmddziLc+OXe+AaHuyeuRFF4Fx5g0JtF\r\nBxPU7yOTZ8UGyXXkT0DKSc+5791EpCMV4ksWbSUHsbDPz/nPhqXODNER3948\r\nNqiYf933jDctHHeNyOyhawe3ou49xrooXgSxHMCFudXuA35+DPGRBS2nyOgp\r\nYiRByjyNt9t0U6/toYLjvdHtoHS78NRSiU8lgmW+NGG9V5MTLmxt87wQXl7L\r\n74qWtU1wJhsGRkFQbuL3CishZohOERPu6Fcytn6mNDAc+zD/G7YrOes6Q1L5\r\n/Ar3cNBBvX2soPeHA3RxZkEBtKsdIIs8//A=\r\n=kpcL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.42": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-direction": "0.1.0-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-roving-focus": "0.1.6-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ef5f965eb31e40d2e7420599bdee6638b36924b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.42.tgz", "fileCount": 8, "integrity": "sha512-cnicPmC6NjD+VCW4x8JnHhPgSiU0aKMnhy6AP9tgqhiES0kqfs4U58u7Tq6bEPHQ0YdcWH3dBDy7MMS5krbWoQ==", "signatures": [{"sig": "MEQCIGJfir5tb5Aofgsp/18D0Y84mOAzGhv/ZPyqGW4AAhrhAiBYqBOdntrA5LLCwbewXF3+UqHOX2DEcszgWsp8QQGTyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeCRAAlN1QOX+T9rgEC8K0K0aaJRHZpZ3H4EL5ZngmyEbj7YMuYDeC\r\nztX4ia8HAvU1Us5rbe++WcIxO6CkF8XOocgG9Vei3HrqfNtNjLnsiW5Mnjh4\r\nJOSdo5t0B2bJdYmt52d0suvbJt6UfAykA76hk+1PWTPIESRg05iuOu6n9gDi\r\nb3jExvg1lB3K59SPbkuu/G1rWQiAYOsqBDRqMWph+VGoo+M2T/nXtimZ9n3m\r\nLp30csGHeHuA98Cjz/IBitSNoKXxgUTjQtNUHtXCdvKddJqldBnCcmlQzS8A\r\nCJJjY4xjCyJV0B3InU2RLzmbmMsMTdCL6NyiF72eRrso+V6o29KjijkmQ4BI\r\nrxweKOjF2Ti6ThPU8VxpiU0oljeNp2MC37QZd+5i41RVBBN80Ch73Sq10964\r\nvR36ly44Ag4MiwnBtW3YXUXskaVl4XrjPySii54VBmIItF7GC2zVXamvXGzX\r\n6w5D/hjrtaaqywgj7H4R+jqQORC5+d308U6ASySbipldKtjfeBGPq4y733+d\r\nAIMOFAt29/oy85/oWVfSBN0vpJoOhcyKgYqnz2fviCxJznvtuXVtvSlDY2KM\r\nmd/GBXmK2sRJzwvWy+InytkjdOAUeF76fWw35VVT8B+9q5g12SORvI2WzfHd\r\nfHDHJspTgTUuzTO18qOqIXbM2GQ/ATLSmsk=\r\n=QGEQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.43": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-toggle": "0.1.5-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-direction": "0.1.0-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-roving-focus": "0.1.6-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16df3aeec2b5cb6cb77f86d71c8ab19ca2dfaaa4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.43.tgz", "fileCount": 8, "integrity": "sha512-1rmqpBNLYd3MM/bRziRIewOWfNfVM8FMH2a5lsw87rgxdNoVdnvFcxVDe80qtB/g3j+iDVakmvIPfa8uFEDwRA==", "signatures": [{"sig": "MEUCIHXLd/MOjdAZopTLfCN4BW1gLKQLu6e3svUsKgccjVH9AiEAspz4trGLM4MVhzLw43JpjHJ+yJP5PDWjWICSMryK2aU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj6w//fdyk1+bgvVwiqZEZ9t3t/0GwP3S+BqiFX0NnNhzyzysYLNCG\r\nUt6Jbot9yzVCVJpQaOCW+MTxzGigL1Y3KMOnGIKwXmD7d93o9PmMLbCveMPf\r\n0EdkgYQPxFIDugdLKBBQ+zRn7/mnDp30SScLPwD8HKm24Ag7BbzLD2fM8WcJ\r\nrfVytomyFlmegy4osmKpB8rcZvkiAW/m7SJvXhG3QiXJzYEkE8MczrQ+hOBj\r\nie00XGouFA2IV3S5i81Q6ZO5GZfcofxBwpDzEchS7YpMjpsq0F1Va13VyQaN\r\nxTyL/cl+VD1XdAtBSyeeVLIx4DNTHgcAOSQmizmjbgjNmYH/AqyOTmKAB6eq\r\nvzAV3X0R5tr8/z7YO4b+UME26DQMsiLLFj+/b63nloUh8qDApxKwjqOTdTKs\r\nxnR6FkJtXLxhnalA6UKDjFp1Udy5sNZY+cbofehOksEcOxb4DaZTeL8sLdeA\r\noyQsBCSo/YcRP1JHVqvhiaEm7Rg7AwFleegWT7XdYgFKAM01OS5ldQxUswgd\r\nt5ahvH69Bi6Nyhck1Dr3ffYxotxUGXr3Q0ZTp0QQClSHciZwWpDaukmy3RKD\r\nkAF0jO0NTMR/uvMrrGcP93uEBEiTqp+i9S5agqlU8dr89mouwEbv7W1uHz6N\r\nKEM3SBY6iBUb2H5vpq6yF1pnLw0HLyeO2ic=\r\n=M4aD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.44": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-toggle": "0.1.5-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-direction": "0.1.0-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-roving-focus": "0.1.6-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fcdf8d48024c3a13c4ed32549703be6dbf7797bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.44.tgz", "fileCount": 8, "integrity": "sha512-3uWUDIsW+4Drw6opKpCS6PsoBUV/60rqc35xE9W/+cKIUFVIKQaFsxkcPWeZgldEC2eHydidEENCBXi7oVJsuQ==", "signatures": [{"sig": "MEQCIGk82Q4ArNx/zwmSeyCJ/jyHKtFJ7BlOo1+xTwr2teCLAiAb98A5GXPztUFG4LTyrNYbxxuirEWXFta6ABmnX84V3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvxQ//SY7waI/gwe/VkGraZyzEejE/2q3b/uolJ+U6OHP9jhK+Hpm7\r\nO9Dvbpxbxt4jFLSkJNkiAqoSQ/DlTR/njNeiucrFos+E08ti5/rJeWfmfPCs\r\nVhvb06YdN1Clc1vMRc2Cf9IbOA4R1QjUaj5NYzCW4cbU5AWAo5vhER8Vpgni\r\nCyKCsOl3wWRDd/51k6OmAZI2obEJWbO2VJ/K/zjqjeC1IMqC0Ww43RRQyNvt\r\nkF4WZk8D4WUUQ+EKponagrrgni3LXRJKNs4Sj0wK718T7F+9hDXZhOORAuMA\r\nx1StV7/Ruici1db3UJrDFdTchAzf4ou7z60dJwcDjYM9D9IfTlkuRmR6H2+i\r\n07dKbHJ+YFvSzzp0Ba+vP68q2KnlsCjCRwrGksNb0i1ttWhHUV8u6i/nrpNs\r\n2oj5MezbGBGjBW19DQRn1vnPye55i2YUILuW35pziqmppKX8WXCiT5Apfj+7\r\niESUTmdIncWeQaQsoYeJZOaltiEpaPH1TPioOWMQpOg+cD6w8r4BOfT95q0h\r\njouveh1R6tLaqEB5v/WU0xxqzrrutCArUpBmxCCfrOID+ATabpJynxDFCyR+\r\nT1UjQbz8pGHl/RSoA4TsFenBk+nQt7Nc5VcOta1YMYzTrZxRd/d2wX0te4SU\r\nIIs5dsL3SNogqI0pwIt4c02umFMUoNRYeNo=\r\n=bQxZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.45": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-toggle": "0.1.5-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-direction": "0.1.0-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-roving-focus": "0.1.6-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1436370c8c1c4941bee7aecab7af74bba588dcb0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.45.tgz", "fileCount": 8, "integrity": "sha512-pEFuVs86JwmCTRBy/gvS2zGy0FuFj/1Hmh+7pdRuzVxSxSSX/w1+4QX40Ayh6wXxJC6GtUZ2uJ6kT3wZn5QLzw==", "signatures": [{"sig": "MEYCIQCXnftM9r7XhhnhTV6XJMx7EeaAWXNymIL4no99COUrygIhALfehKstXg0vaABQxtg5dRJyS9502BU25mMKNmGhnlRo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2lw//dh6XpG0BU2dYUVIq0o/0QbkmIVTxHuN4XnkoF33PdOigBE4C\r\nKpeb1aJBq0CCcGHYkGE22aPOZ7GEGU0GaaXYRbO6iw2ZtVLXMj/CAzay01s1\r\nmDImjOJ7p9/XnT/aIGaXPi1xrKv/kUwG2u9J+7PCEaS9o4sIqMAi9VdH2REw\r\nslqOQYT2PTraZ5JyAqbQYAWUcmty+SIEDIV594Cq8aqglHyCJgx8NUAunhRI\r\nKKMY7cdD7YPlnUg5DtncqiC4scD3VWZDupYramQKRFM2qV2YzZGJVG0NRRDK\r\ng2qm3QdqjJmglHtbwj6inSfTH4hy0Qxk6EAOvkz6ylrvvrhfr9zfAFfZP4jN\r\n1afWhpKWX7P5QoAe2Rbzg+oSWcaLua0l6OwJ+wB7ct4xRJTo/QfRl1Etark2\r\n4tqwBmT4WWUC/v2glPaWcHfOKZo54UAV1EZ9vpUUN+7cDuWmEJeD/lC7Elo1\r\nYHkG8U/O5ko+5cbOStcKzxF2X/ny1dRNA/RPUI86oY7PxVg4/UpHNQXKCnup\r\nJsctos/JUZjnOEUT/GoqRX1xscWgKJTBkgZFqIDlgue+uiTuXzVusarYUDGS\r\nnZANJsKwehht+sgO7hRgXWOIaxFhz7wHijTFhGgbuld0gCcQBPeTLKzhMIc3\r\ncTpI6nRWmcrsUnsBb26qUYEz7k9HIrb8SQA=\r\n=9/ZZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.46": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-toggle": "0.1.5-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-direction": "0.1.0-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-roving-focus": "0.1.6-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e6bcf0e6d2397d3a8c49e4ec29671a9e2767b9dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.46.tgz", "fileCount": 8, "integrity": "sha512-Jy8wEdaq+aRU7ta5fnJV0wsie5mXjlcrMEpcIADja0kOJliYRwfwsLgcCyp7ecO2ro9UDSViPVm39YohiS/QyQ==", "signatures": [{"sig": "MEYCIQCu9cPYxaWD75lEMIwDON9RH0a1CClTkx69orMj+mtjygIhAMeBzydKbBU5qY9t+t58NbTluaL94m9NUhAgHIE+dJpR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFNQ/+Mx4zyuscHndRwJRTh4GaUz3NLQPRqBA72lhiPWpXkIODa/QU\r\n65OPyeMG3V3ahVfosCWbiyQJYYP9rj9BZ/nV/UamGKcV1gndlw6VKVyZYVmv\r\nkeYhY54qvWnoSPJGPx19oTi1W3xe9z73isrtCFM9JB9QR9kaOZ2rlwOtvUAF\r\nWdq3kxpOvxfPzI8xvppY9shd/QB2kV3wWAFLGiSKXNLGhfeftoKLOnTAajEq\r\n8R2LfzKLTMxAD+QhqR+nRa3QQQ7iKyNOo6o50t/UUtqjyKfuLfShoYBO+GUM\r\neHit03mFiXfjH4+Bzjf9UZPmCa5uD3WuBLbjbA6NJbIwZs55Sx40CjN/ZeDT\r\nmsNfKaJ2Ew0ZXQJHooYhV2tEUsTvJ8oJ3nhD5xHxW6MywYcAa2bqAKQpexFY\r\njeUU1KgI/nEG/IpZ12M7ITCpkjPSlUcJe0yn51ghVuJB2ND85NGT5q6u/A39\r\nouBvrfnjYJDlN0rOR759EX8Ipf8ZWAkuI3fA0T6eaJw7+I2WDiK3cDOTShwr\r\n5PVYCPouJ4Ge7KNoZRwKqfCR6KLFrxISZs53lwm72fE7MtFmKDjE/T6CVggD\r\nFZBoa73+63mWAfUzYLOnm0ons55Ivj2J7mV6RBaJ7RipbIbx+gCSm9WWhOsB\r\nHa2/F5DX3MKt/Q++wdxrLVDgB7zYzMVXTJg=\r\n=HuEY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.47": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-toggle": "0.1.5-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-direction": "0.1.0-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-roving-focus": "0.1.6-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2f18ada5af9ef41564bf2a8813e5b3e77e70c0af", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.47.tgz", "fileCount": 8, "integrity": "sha512-xiKzYf94FvJcy8//mD2XbgFEp3vp5hjwDQZ5PkDrhkG8Mb+YfLGrAjDELXIuSbDn01VFCelRJ8bKsYpQ/JyV0Q==", "signatures": [{"sig": "MEUCIE4crBMJ6SrVrN1RBV4iSkAX4ilLyMAXigXRLdCmCpPeAiEA2kd5QM9OX9hy1kJ3AWSYZwlKNN/MQdmyE+mfFrbgkqo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoACQ/+NHm1d0Clzt27zL3BxtTQ5JpfV/kA/UJM8WDnWK/csKtOr8kN\r\n9/JTMrRqbbHwrZ3unqu2dlVKvTXGiLkMSV4GZq7P+gM503YHB54eJCJ+jLnX\r\nH7CKPirzRXRMZh83qb5cKOlZbXOPxKmAxgSaJyR12HRWr4QMShW3833n0pH1\r\nc7id+f2VZtMXmj7yd2Nwm6FIcgAxiGDPqN6bvPf8zlsazT5SNq6M5J9QH6gs\r\nWvsPW59GaT2r1mDQOkRX5qIc7mjOf0cAkn61ctE4xhbt68PGa85C9Bb6lEJW\r\nVfTywwqYCpJCZql4ejsBDeU3yeGwfJbHdvyKjyOkiuPTWNloJ/YARQ9DBgpE\r\nnQ1/Stn4Fa1oz3ts2KlHVc4s7QYH0XYuNihUWdlh61mOwahoBG8XENCAc2GW\r\nOUezrCqS/GcynAPptxrrMnEfs5pWsYQKgyLg4Qeb2iEoFk4Gw3HUyPjrIvPi\r\npqTJr2CQYG9261jDTgdf4f8BmJkUv3itYOkqejXTg1yjpASS66v1wFhT0U1C\r\nEWl0ibK7MUNAe+3XMRQdjfEfzFplcQuo0VFC32J4qRCQhPpAL4VZ9uXMV06H\r\nuKt28fTZGeoHXnVQWSo+ZlvyosJQtaEcbsZBVYQHTyyHLGkrzKXA40SACFdv\r\n7ChId1e558c+RdxGULpIozrK0l693gKfHco=\r\n=NQBm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.48": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.48", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-toggle": "0.1.5-rc.45", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-direction": "0.1.0-rc.48", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-roving-focus": "0.1.6-rc.48", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2a5d4c3961f3a7051bb74d81b31826502499b30f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.48.tgz", "fileCount": 8, "integrity": "sha512-P6dku8aLWRkmfq2uRuJiF1h7vzs3LJth6a2fVuqj1oMzEiONuj8TME5JstEnOZ1I71xvIs8pknDhGoW3UhXxdw==", "signatures": [{"sig": "MEUCIBDBeFHFX2pYywStgmtuNFzVc6xJ28jiShWLcPgQ7GHCAiEAqymtvf7m/ObCWp6bLhYZnbSuqHw9VxdVvqcgAb1m7ig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5UxAApChs2c0B9KyZP7OWwb7Ws4C3emJzVs5GrmrXMmInOCsaK/FF\r\nSMpCjd5XTvRlUBrnhN33sUQREwxkVHseuRzIiJAJZNwcOwh6BgE3N7IRAy6k\r\nSuyY07sRo74xFmz6tmoa05+HZdBW2c3EDrSz4/D2yeWs9wZ5e7QIGXQxAsMu\r\nvqfMQiLmNcYkztyf2cEyxTCphRTvRvMah/DFAK2Rf4gWBjC9H4IbpszVwpJ2\r\ncpnCWiq3/ZecEpfpWgtzMU+WV8ct4HJ4qtjxq1ZP30OfzxWwzCteegNDrEEV\r\ntF+nqar/utcA0jMJ8A6m6dVNEWMhDOydapJXNGFpUslVR2R+M7dk3Env0NCC\r\nrxWI/gTIRrjTd1IMyUrcGq9p3y5TKrS1evEhV7509BBKT1rDsW4p0X5pvQC1\r\nKS9a9qPS9GUFCSZZ9KidQwMhTrdYOo4rkG23BOPw2ihgUH9BrQd45wiyfVoc\r\n1nMz/5Aaq0Boyd0b1gVdejTV5RKpZH10+qVYMHIvf+5rbr3XijEmxiSyq1CZ\r\nDuB+aoLYcB0Hltt2uku5vof3G5Ykt6j64RzvDxH8NjtXhFrMV5vfLT413cQi\r\n/2xim160g8kQJX09AH6uolnfTWGiqafrFJ6Kl1SYgPwZBBfGm1NK/JY7XhVb\r\nTHfwVPYzcfqvwe1B3JB07QUQL//0gt9cXOI=\r\n=cBjS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.49": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.49", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-toggle": "0.1.5-rc.46", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-direction": "0.1.0-rc.49", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-roving-focus": "0.1.6-rc.49", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "537c74dc0486bd93b36eecb13615b9228584c76e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.49.tgz", "fileCount": 8, "integrity": "sha512-iiu75lk9uO+GPkAawy7o5ZfygDzSLYmbJyWhHXlEj5NB0b6v49S6jXiZvba8eY2TNAUeY7Qoi9yDYsAcEP6epA==", "signatures": [{"sig": "MEQCIAHN9ZjVGPc4uv9Lsqo7xbLrjFN+4yooSQpv5XFEP9+MAiAcKdA6vyqB5X1XT1qF/jtD5NTUupVAPgusdKY2/nmHQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPOA/9HhVD1OCrZDd0TlZUtyLufieIlMGF2vMT1Q373+RJv4paubcn\r\n+ZGNtT5r+IZ30ASgC9QspC1yixJ5cph7MRxRhUjr6w/l8muLRNDRqfjUVk5g\r\nxZP2eOMn4C2VYMJqlTKl7OuhaEC/D0DlrW/bVhnBOSUswWI+1aNOOQBAvSf3\r\nWo1S2bmY3nfliQ00CgHXbkbjVd/XoJLICXqGybkt2Z9SICiMd+Jujl/tvYuz\r\nyCBwmRar/b6bTg6/51s+4f0XqrZcdcR5S4XjLnqIEzUqxpc0LC4k7LgkADmY\r\n5PeLIMPqRm13ncwKJehKKSYAlkLKUIOWP5snlXmN/BeyAxe/oIfpiFIBr4yO\r\nRhgsB6zdd5JbB39V2BifmYOR751e25j0SCAWWONFXVeTTkotLnnwNnaxyJy5\r\nAHH+gEBBF14KjK+jrRpNzYF/RcV85E/QtUjlscGWR3+N+G/EhdgbHFVNES7g\r\n6SDzI7w8IXYpO3+gsDfDlmHl7aZi37pfvG3RT9bFEDWdwXeRnVnuqV4ukBfw\r\nfqtxJ6hQkVBriXRtmNvGm5gWjHpVdIoKuSjnRU9sELLD4N6up7PgjUjDga9u\r\nH+3CoCcZfChEoIGiR4H1GWLJPQTWsOpTYiSuQ2rrPhhwzkjh6XrZvwGFRdya\r\n2Kmww1bZh5gZuwzrbqxAV9j6xlGUPa+9bo4=\r\n=Dx5R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.50": {"name": "@radix-ui/react-toggle-group", "version": "0.1.6-rc.50", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-toggle": "0.1.5-rc.47", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-direction": "0.1.0-rc.50", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-roving-focus": "0.1.6-rc.50", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0e2d04c6bd4853282fe0d07a5dd6460b1053c0d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.1.6-rc.50.tgz", "fileCount": 8, "integrity": "sha512-cv0vWPzOfLTumRNJt2NMUI+qC9IairgNa10y/eggnZOozvQGFayQLAa9FHyuIL9MtXPrSnlhS8Wju403NFZpdA==", "signatures": [{"sig": "MEQCIG3CwkWoRTJZN4kz3FtiWt+D6za+LvMNzC1D5qdEWspTAiBMi2G44n8nrHwGd67mH31vcCxTl8u1TwXKGg1raYGHuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRLBAAn4O7EyYUXN6lxhz6zli3Pjx0AoUgacfv3PLIzjmTim1VzdB8\r\nyyhler6dcqzGT4FMVrPGiCA1owXaOyrID2LqzBAYwjw06wC2VdVANjkK/Ma0\r\n2/qLQsAS+rE6hrLm3uvLGf0qsHT6Z92QPRskaQXpod/IXb0JqgjDEFSRmySX\r\nPl8EX6ukacw3ubxVryCRlFGfKI59VBDlEIFXzLynyro06M4D7ctbwVdWqfq9\r\nhmCqobcYddXE8HyAg6BsJthf55R7cSc16/0SaGXbgjL5LJA1pQT76YIynpW4\r\ngoYMRjQJxwGolRll83odbrmOQa4j5Shuk7lu9VI8InlSGRBQJwiQTjG7pWnB\r\n7U9+qtD+gR8HQeq4Tm5H17XABgOWDURRjnjI5U64r0GWZHrn1GA4+BpQP25g\r\nHjIkSxhLnnejavUdiC5GBtuMBA1Kz7qHbmxIBwkYl2TGdqOax7mSyMdpkEix\r\n2ko/+K0xjaIEoipoaDL//XqnN3AnebbaBZCtTupTVW9N0OIfBrmxcaZh3HWe\r\nWdhVdQdf0u+AJ32oIoGoEm9/6ZbxnKy2Nzfap/DCtjSoNwV+WUDfwBB6ViN2\r\nfL+etCu9Zvsojo5T0r4S65JV/PxkhX3kwPs8tld7ZksYecgq+plT0th4HG4s\r\n44+EhmoU5n6b6HDlj2HdOEK13Rt6pErMlzI=\r\n=fjI3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-toggle": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-direction": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-roving-focus": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "66f8da247b09ce76a98cbe13951bfd456c547463", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-qMyomc2ccX7m8QF9GcBIBRVyXCYofmYONIO9jMhv5xcNnLf1ClERoi3sB30gYbmSorBZXmNvcDVrBSHPOOSjfQ==", "signatures": [{"sig": "MEQCIDfCtX24Ot1noPdoHGCzAKt0dxUQt3rkzNoMfeRzM7wpAiBoZtFmkoUhLRZiYAc80SMtXcOCqarVVCDLtOph9ellxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Ev4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoc1A//a8cahCETWHeQ7368PjTs6j47rr1nLwjTX+8pHmbRs6R4Cdru\r\n4FWaF3idO1OjeyZAci3v0yIL6yN1h66PQCCWWdQhBAMYt5NkCEUJ5krFMB0R\r\n4nJoLQSmhXdV7G98HQ6VNrcnmN81oVMur2SJW+OfG2h/nlauWr/SkD24V4yw\r\nv3rHEDTCL1W8xWMzPbqa2gB4PnJtn3A/0XA01DSqRvuPCjCNhMln/FTGX0KQ\r\nnCQrOU9uCPf015CrkEwTdnF4w3pSLTVRIwVsemoabJqOALUS/x6fZL6UbxWN\r\nzQGMTkf245id/XDl3V7kqQPoEymGQ3QgDhCv/3DNB6mJG2pdOMT+iCbZ0vHq\r\nuv4P2mafB6KzOt5CiZ04krdBg7OzPIMoQnjwTf8h7UeJG1+Gp74Q3tijaYfc\r\nHNJaoeMGMdRX3ybpgpzqxjuwU2RhnEpECGkJP1qXQ75I3NRVXbQZsrLZv3uZ\r\nDkX+lBLbovTwNECwew68KA0l/GTPIQAxu/kGQVQKhG3B9dqMsWLwKisHVc6x\r\nX2gLySddtBmPwXMc+BrSybhrWYZVTCNfDoW5JURoGEgAt925kNexVqhLm+yX\r\nwO2EXI+GctX8x7TeeUn1DNKUysEIh7/yuah0/l212VpAgvyWH4ArRE01ooKF\r\n0LrwsV84tg9ewkeyucFSGd2b3aKMN3eCPQo=\r\n=PlwR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-toggle-group", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-roving-focus": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6e78ffb13e0b4c1f789828930330638f4ba9c06d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-R/5sK4/BPgOYWAsheFaFpNFh0sLPHdqsBcqO5KW2+Foy36B2KBYrGd6Hu4HnzgivawVX+mSmVNhAwHA8Yb1hLA==", "signatures": [{"sig": "MEQCID7c0QOZzA86jrtRqmiqpTr6zWwmlMT+iQkcBcUVTlvvAiAnl4GQ4FMJ8PJhJ190/ri/Gs/4CHSWrg37LzUEV268jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ0g/+IQbNvg7f+0lVHK5uekAoXWZBRluioucDx/r5xpPfpoKa9asB\r\nWi75SS1LjDBk7JNH63MBHlpAR2IJvR/+k3wzyKIcKpXjbA9ugLF0w4PMIEit\r\n0GJ1v8zt2+PkRut+B+XSXMAMpEgHvXCRbcp0eo4GNjHgS2aHJGDlAcCZV7qD\r\nTJMvaSY4Fan2y233EesnkYBtkU13vfl8E7rmWbBh/ZZdWBHYP6uhtH9dIlfJ\r\nbsojp01Xy6XoXjwmuDVoN7ILGZL2wzrSSNpBSLYcpVBKBKRIY9cz6EdV7W08\r\ngr0Vc4tlgDOfokGbIolGLT5f/nzQj7mhsNkZrKc87c+flfw0AvlJztt153fC\r\nRvGpn77OSMzaB1AuW4xaRZg5neEEm9UJHHF8vneQngejpQhT3WYiwR6WJ2ds\r\nwel51WOxgqBr1+ErEnLuA7mPO8qgeqwLoFveSwiZtxp2SDqSDFy/WDp1K/6I\r\nZtNrNe9s++X4XyrM1B0Da5xbkR4qswyHZavUjLMlDFeApR8cCIbFoorvxiUc\r\nKc8T+oGECMg5eUDSK8WQRGDyRCATR5BwMDVO1njkPJphwHabDCVVB919jHd+\r\n7g2ruCs6es9ETamTPKTXgqSOsupxiVSr2tasv6PnroI49y9/21wnCXIhgqux\r\nPksKXz68DpjdBvn9kElUy8n3+hw1Fu/pZSc=\r\n=BK90\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-roving-focus": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "09bfe9414790ead54c04483a347779414ce1c59b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PRChc4HVR55zRhbwmDuiPriZ7KdpzPoRGdhAHqEH9j5Cu98Pjp0dWlSpjJ7Qqnd4IkLy1A5eOQETjQdkMoR9CQ==", "signatures": [{"sig": "MEYCIQDaAQ+q/LrMzZtduPUFbLOvp6L4vBY+ASAilkZ2B4t0PgIhAIZxv/RL4fLBfY8weXd8MJWEqeaUDzQ9gt1G4PHllbDO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO7Q/9GxX+Jkul+KvvRsAgwOE55KglhlylbyKXzLz+h1KZ24HdP8Rv\r\nu6FP3KUCZj01cF3dTewnri4nukB6EE2S9ZWmD5wG6FZUEvwisPwFlKX6TkGR\r\nEkc2l9vPhkNImUuE1Y84A1vnH7/yJ7oFmMhYiBX9iSq4Ur/0gWVbUOaejN31\r\nZ/H1HqIy2C0M+Q8TuHq4WMDLu4GIGeV4tllRqeY9PS7hptK+LP1Cc+t3kXgg\r\nHb5yuczL0P36JyBNLujSGefwzQcdLYS0S2bCiXwNrIHp7yOF+v3FlnBa6s/1\r\naZm6ZjehHASToAv9sw/TYjUppGADiKFP69utu90B9h4Bt/OLzsXF2VK2MTck\r\nm2vOx+R7286wQ+jcn0C9fxJpBVlpqps9mRfxutx16RasmOMp0Yi5Q2qTs1t1\r\n5yTn93S3kRkuI6uHwhyuLGD6j8ad2Wij/LOY5NI8wecmEuvqdf0xS6jERq6P\r\nugpMC54kHP4zJ3EzSpVwNQbWtyV4jPyKHhRQ9Q6wzM80kfrcY2Bfc+M5UXku\r\nqGF8636V3pkV7Tohdq5uUJamecH1KnuOnV3NbDQkcvXQvzfKSOqNOPyVa26z\r\n2ek9M0K11A8yRv/+Ia7czo5oBMRUDh9xjNPZ1SaEPlnl2g1PVeUc8+32/jJO\r\naxz/UROE60rzYlAgVd7gb0mfMjgC+USHGNk=\r\n=E6gO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-roving-focus": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "63f391243c7d8aa9fe5d8cd020f92edc35ef4054", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-MPL8ZLlPc6K68vEt1zw77d2Ach5poRCig7nlG5deAMdNTpFY/67a8TLuKbH/d8Cor7daR00dwWZtAQOBilNWbQ==", "signatures": [{"sig": "MEUCIEdcYH9A2rZKfgLPMznUCRLlTquu474u2y/SFiSHz9piAiEA1g8+LDkTH6MGTAeKhnr8/5sX8MpW3RG2wC/3T+q8Ekw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKz3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQVBAAgNiOiybA8kWyJejKlJrVjZ5I7CgoJbmvUBAOJgSgrYX8AxaU\r\nGEDAQa8XiTKLv8Jj6SM6DCOSJ5rcrDPOq7b1cI7Uukq8RYhQ6POVppFWIXWA\r\n40M3gjOL5jZaci7IT+8aNAmldiMyUc8QDyrPev1vq292HH5XamOgc9kbchKw\r\nW5tUGAIY6XBXB/Gh1sJ7ymwba8yDzAtacK2c+hR1tRPxfbn/8wSNk1NtnBZ5\r\nu9K9IUEOwoDMY/VX+vwyQGWZrwWMsVp4D2UU90SUpMZM48V8OwnQCrSO7G5s\r\nYm2MZcBuM443AgG7iCXJLffRDd4c1GoXKinUrJuvM5dU6EObsvZQ87U1Afw8\r\nDgBaECiqwH5tvX1ICI7NmbjYLqzy24FCto5EpYEt0BQN5vYQku44XysEEOiR\r\nnALWvO8W2E5i8/hSwPyzqMQfQt48Sr43Kg0j5BVqx/LeQSyb+ny6hZSbe+W0\r\n6c8GGTaMnpy/KAZeys/5Ym++u2VaHVqergYXaa29/bxpFm6wzdkEKU54G/Wc\r\nzpSiJEljaxf/ylKEl5Fl0v7ZfyLxFR5HwZtvDv/N92NaXZadWa9Zszyv5BO/\r\n0A7MUynxjxe3EjSzUfVn/S6VNAOR8Szi3dKzY5WhP3Cd4C8zQR0vXCtFCSyG\r\nvn+h0L+G9S+cYN+p1MUICch2GcU2/jady20=\r\n=ko48\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-roving-focus": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0f2382e612d1fc9d565927ba6c4b2cbfe91f7539", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-miji3nrd+sFJn7T2BwXJexW9jDx8pUMvt2ZVj7OkezsDZgy443kQHF9aSBvPTmzRF44eCNAUSX5tXx+0iA1lxw==", "signatures": [{"sig": "MEUCIQDm3OMLJthjjErB5sp57J+UFoSsitR5iqaxmfrvQ35JBwIgL76SuIjsv0TZQf7AdziWRWypmJDO+FNXBcvtIkrgBrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNaA/9GwSMJlJa9/cTIfv3ZtKuKp02OYBQyRrDLIIylcw356KVNiVK\r\nkkR7Roy2MWkn1KC0rXhX68sPjjJooLic0xVmrcMT4MkOJOIIYCaLEFMACGtz\r\nDriOh4JpV/fVwqor4RuN4GDXBxPokdFEo4QctgmRB++aRVYc+Xvh5mnA2DX3\r\nQ8eJbSt2e83qf+jprIOieqkOEVVZHz3tOWy7MULUZA/clMBcfXcZmjZKpznd\r\ngxuHRA8Eg2ExH9Qb5b2bHQvzGmZbc4qxEmCf2I4cg0xrETioU8buot3xx2oV\r\ne4LQuJPJg+qQcHAkURtjyS5yd22tZoZBhlF+CO+/smD9Ub7tnc/t/dL8gbSq\r\nAxhDHEXDkSUMni+7mTCsGaDa/f0izJUB1D8zfFcmMB0tiss5XZ89e+Jrcr7B\r\nQ/vnH87X1QsKI573m1Pjtl9BDV8J+QWPQcA7JuL20CpJ7Y0n4molNe2hJxRG\r\nuwRWOOnE00aSBiXABbSP5LolTmfLTXLm9WMrqpupFjFay4B8o+LDEqo/373K\r\n/Wwu+EO4ByuntEMfqWYkWIzmxp6Z2FTqen/P1s7zGFVNwlmmBD8nKryaFz+7\r\nSjHDdUWCI4cyObR2P3iQopTRMsE7dl2GoOU8XBEerfp47kFavHMoO1BFsnD8\r\nuj6HWaRLlI3c0Wml/VdTE9SMZIVkrPIqAq8=\r\n=u1k0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-roving-focus": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "11b9fc7cf143cb6befa028f28d33397707cba1c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-7Qib+aCF0v4SuN+7y+4/AAtSH1UtCx4uAqBpWRMp18q6CwX9j6C9G5mAwumBUaoEIh1croD2te3ztE+Sx5nwsw==", "signatures": [{"sig": "MEUCIQCNPBGwMAKpVnULPoTXw6P6sWfXRoE/wux1pa7IZXDcRAIgfBgrIOv9gaB82XJxQlaHUv9ilk8gowaTy0mDuYpMGjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpndg/5ASMgGLR19O0nwP0eiACnvGJpaXlPco69TlVYoLq6glnWdaq8\r\not09rQyYn6AajXuYss9z/TeWWVODbW1qku+iHeuO6132sgchYUsfXCjDSv8/\r\nGZ4qK/GFKx60hoydkPLEOr4eMl8AvGxh2GsQoY0M3UHNzQ38n0KnzQVVXTZt\r\n8isDl2yccoaAU00dymklAtPiAkkSRvBCRMDlCP3cJdBgeJ9XjWEkCc0TSUEP\r\nWPjcHydmST+RnLkTL/09XE2gR5bkajhCUdDYrZiPdRTnZjdLlePByTf8GcLX\r\nlMvh4XduvBHqyMtWJ+D5fOvozetGeGh9mFBDKvU1ifzbN7m144es2DYuzClf\r\nL3FPqWY8HfNnVq8CI0OFD+JGBFrxaTbgyzEP2BNRdFJyqqVrt9W3XYqdlnH6\r\neGc0XWF26QwbSGZapzw4uv+IY28NRAEm2YcuJYBhoINLiGvySbltwX5a9sG1\r\nH+U1c1iyUzD38otFw6fCXDKg9ZcbHaZVIwa1j9hkIwcqVwAdtEbciQAiBByZ\r\nH6GQ9doUrT+LRPe6lISl0TLRWLSW/D6M82o26rTTV+hana4JWvb8qAWoJLXG\r\nhbzrPjpcvmlE7hbSFxYd4vQXx77ZgWvjbyCzlVXQ30rszjuMRkGEOBvletiC\r\nSAHVnAi2wieoFyzOuAQKXmqZVTftDzqb5Og=\r\n=6aOk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-roving-focus": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d139cba6774a594a6cad9c709fb8ffd5d5b1f54", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-dFwXjQ3t4RYp2J3yJjV4HBgQl1N9EwFL9YLXxBL/Xftz8ttaMQdlJ0wxIPr9l6BgtnqSZY8uxOBnkVmXb9saog==", "signatures": [{"sig": "MEQCIH9vgcFAuu36reKNTTk/PDipF93s1gkh/VFEbO/iDW2GAiAn6Jk+08hsfKvNworzU+DJKVfXRAqdmOFzJd4JAjZAdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2iw//SWLI8cb4qXPoSvnwX9hvEUKxV2A2AyJUtSZ97SFF8QSMHUJ8\r\nUN0lgZL66aZmLInUvXm0RExo76UrTM9Pc+tBU/nBydLMulPrB19vqF588QKM\r\nr/j53DdiRgi2gmjpQWapiQcebe9qRTpYs35PmF7FSdP7hHHwIZT60cFHMiPr\r\ns/p5KzeHH93LVb0gsfucM4AJT4nHRv/bR5s4jeL8WKhyZ/CM4JDf9pX7NQ+y\r\nbsakBOpVneqoq6woB/V3cDycyNMG00EJ9GbwzqAwgWWBPkpjPNo6+l1rRopr\r\nkZKOPZxC6ljkERFq0xhvqUnnu/cJ1W2lS6sf4X2o76PUWN6sPeSmUJ0xOAZZ\r\nDLWWE0UNyYjlt5JqWFXhJ9lXZ3ytgO4fGrAF9x6C3udRC/famPbsyN1Of8Hl\r\n6csxHvD0AS4/g1nqMkvIt+XUvZSbfuArWXKkCgR34GsBNasExQwGyqDggAcp\r\njEhRROzfuNK/1RMBuqtLEnyHURTPWPbyqQlcNknVFo2zRl9XohYdJrcJsrCi\r\ncx36SkzSw5uOuSAjBMz/FABxpa+oU6G0+MSpA224548RDYpHhGINOLkt5yos\r\nbx0TOLuOUXau86aSeAI5ynm+r4t2Om6Z7zy/GRyHTjneUAkBJwF/GyOkpyKP\r\nej8ZnLKmpFSgriuaGMbzcza7XByBCg9j8Qs=\r\n=6rCv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-roving-focus": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e4eb524956524849d3333c543f2cb0f2377150d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-UcxwBeq0x8RAKvt5YO9BaNdhOOTTzFmp1h2SxAungMc9usLe4cvnkZ9h0xne/dC7qyNCGFjgDL+U3/syBF/cKQ==", "signatures": [{"sig": "MEUCIQDQWRZJkamoiuUqRQtv4fSVn7MtRxTFPyMOyLLsgufBRAIgeZeQ+ugk6uVH04b4ppuxbsxc2zrMpR011iMaDkcx/UQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwP8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNlg/7BzZNS5cCPn6zVqwuqNhnglQUw4SXXemAas27hHbNc9ChFvbr\r\nDq1XtEFjRGoSJNbs4m9hX6dmKy1gwFnBg6aZybgRQs+qig5b4CGZh+XbR84u\r\nshaiH90JHCEy5CpUXgAZxpL4Q7Nuk7msPUL7vfXlP8YOkdDZC4eh3ASI+7eb\r\no1/tXhK6isLQ3w0fLHSGTWrd+XFSWnAQZQd+UuJckiKkNNLcnGulWKt9A+eE\r\nzo+L7WKaF7tcFVk9cMg415bDFwa/zT0h+wwArBJAvRuKxK/3NgEvmi76M4qu\r\nPvhWtFKa5M/ofD5Qr8OdVMBHZepwHlaQBXShw5VEZVi7gumBZVDRSr8IK+l/\r\nkE/jqDrxybVGll+TVvAX1v7uY/qTKMisr4ZGvEAhDUJIvpLDfQJjHB4v7Lbq\r\ndwjQ6DawrBxYS4oDAjUCQpNbH8qgZdNYK1cQJkvGo3AjMKZer8UtfGp+g8NV\r\nJFJKwh2+XNbHbVNtj9IXA8RJ0WKAnoVjiRj3zLhEyDc/EQ6vppNeM9F5L4YV\r\niWgoTx4ElHHu5nxE3S/XBsOZrpjJ3189RTWe6zih77KOjHVPBsfi4c4dFKRz\r\nJmo7wj4SJ4BLuCm+atFcanxEOVdXewspZeQkaqKwga6mmrNDv8PLkBxKDJKb\r\nw/YPPbn0WDaXW4RFDtP0Z1FeG+5Urh7MqaQ=\r\n=oYmp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.7", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-roving-focus": "1.0.1-rc.7", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "44c1209bc17f5438bbf52b9eeb104d266e09e228", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-GQRHcaDyyyvr1eDMQBIebESUVDyPtk+Ao2x7j7p/jqpp+5uQayAR6H/Iyyelr9fkwBVYNsIOYgnqEb/G+u4vQQ==", "signatures": [{"sig": "MEUCICmk83HRxeT9Conj6plcBe9Nv6KDDY7fkuq5P1TuibFyAiEAiVK1uZWzOvhW7Oy81lBz5hn/poSiVQU578DS2cuZ9Jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgyRAAlmjIPYeOkDYDeKsaARZT9HBEKiQpp91gIxSN4ycapEfcYK1e\r\nwzVgee6UoenxIeQNrhriDOOVxM0tggwGA/kvnSNUAOcFnLLPPLn6nH+R99WB\r\nFvPBS4BKdlBsxTSMZ54/UfIywpKRnwSDKgfKGknftq1LmULqZoCO8sI/iySG\r\nRnN2kL2ftxKr17XEerfaqkV/IJ4lPvoXc8rP87Ak4o9ylrnvESWgu/Ew2ypK\r\ndLDcUVeueBIN7OHLomkIrw8xDA9Wrbeo5JlrZvSstxdk8/LnplKi2wsCCw57\r\nqpkU6muISUePWJSRFHnM18+FuV4Gb+lmvKxiHjXnFQipYbFnrFtLKXo3pa8S\r\nf++c7WNgC5+EdO/jIYrFzDs4aF2bmP4yHOg0uR32tu0UyvtnKH2rQKc6iF1u\r\nmuqd/ry+u1FM6i9y+xkLQCY8dZXhTbonxYGXFUez9lgJ+z3SA/LGixvwoX8/\r\nzVvYI4KCMCF2MLOvlHo0xfNOMrAtrm96TYvMO1LJYakx4HKUoUeZUoaFbBAJ\r\nNKzvd1oQPJmvSLYJPqoDKlgYXhUhkqPgTMVVJrpmaKjv5goMZ7OkPBbQri0l\r\nVK405MMhYD2mVxzMDMMuYQMIfvu+9F082fNDRbCX2ZyFSi5mDYhCo0AkISXb\r\n2IDgE0aBHKZtSajgrEyt99JGbv7p0q1hF74=\r\n=Gu3N\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.8", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-roving-focus": "1.0.1-rc.8", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f656113a5aa129da5d1d95ce468083d3df8c76bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-mriG2TgcmWHCAZ3vXi8oW/poMkgDXqAIkmF4Ok3WyJyoCVSViRK3y5dgbjCl9oGEs3+P+zu6KM9ErPliI8qngQ==", "signatures": [{"sig": "MEQCID9iP7rtMVpDUHcvLEeVvlVewHr5HkfeVm7EzasdxD8XAiBzK6xxNgCON7KuQWw7pJMyUL1ZUF+AASOC8viglbRrCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+hAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQuQ//RNmWZ8nXbQdqB6z/8g1bSfaz4sjvykRYY9O+Mm1/vdMbk1xS\r\nwtcgdpGwqirAKUYVSIJeJG6Dyf6026UZ68c8rHOfDwHAH1/+bUfbDgGuO1FT\r\nZXM0EfuSpkYog+6gaRM6k5tEQ7mm0ocS2Xfk1/nlOBLpHnoacLQEhOgScYUF\r\naLTXzyGQEF0ejUGzRQp1A/+UCuH7M7vHiIWDLfzQSBTA9+t14f/F2hP8rGCM\r\ndkHFogX50Uhyuf2ODFfYa1xXlIshOIPPsy/QQHepEkCU3M1sGlnMXmtMqD7O\r\n20INUG1d62LyHCoGakuMnKJaWclk/C/Sao6PmvozcGq7nfIUlN37a7utOdRT\r\nvMxgCZdnW2Yc1cPic6biOvBG0YvTwAu2V+K+DDvdVsqmuH6BaWSso7uNop3n\r\nIr4+0JM+r1KwcwSGYr9JddHj4TEvrJPJQLtPDfBj/1Pb0cuLEQzw8lmdYJvM\r\nBpFmr9Kw6vW3cuNLot4Txx0FKs8u6GST4BShdgJIu7sMwPz+0a+XpSM2sQPS\r\nZoJRduB6fpXDbiN/NWE87oSje3vcTIwkSFr4axFjpJbNWEaLP7q4PuTlKgQB\r\njoe5sXR16JFNjv6HNX0hSQeOlwhXiwOHM+RJps4K14Krkxxe/He+45/JenYX\r\nppBoOESYIWXNsR3xp4Mu3u3rsiUy1i1/KR4=\r\n=s2tY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.9", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-roving-focus": "1.0.1-rc.9", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "847c247d6726fd29272ee1f1e9b4ce44820c84b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Y7KJ01+MoLz87Clxk0gxI1coqA8Cw8RsUJoOC9K3NUv90feUaExG+M0IIZyN3F1K1gpu7tCOm6x6Ogmc/g9brg==", "signatures": [{"sig": "MEUCIQC/7capMLOv4XpNdgLQ2DcFlRw1yIZ8TpMU9uvM8iZKrAIgathnpbnJ0OewJI676XTZkTZXYa7WG93MHc8+GnHU1YA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqkkw//WFRyC+6QcJ9M9Xkz3vLvIerEqhjP+SLBPK8z4LkBUcLbxkqX\r\nbAEGEfWdW76iiWRbN1qIQiFVPQLfXHCwXpYbZzXmvmDjS1IOzAGHmtdNxgAw\r\nbcFnXBnAJWm2+KiVcp5loUa0pUkF7Hj6TxaPUtBPXgxvmno/V2fYkxLSqZ8q\r\n0+GxXKQ9oZoFaNftXzQCUwLQbRmPUjpO2XXChmCCsD09e4Rw/Xz1ocCtIuHN\r\nb+8UIEhLoezyg54PH71d7GuC9vRjHfpiAkwRZmfbGqQI3gry0G9Zewo7SGsj\r\n3uTzj8e3f4PyyNB4Q2mRhJSVY+Y8fcyRcTU64ufy3g7Wh/H5qZhaTLUeA2X0\r\n2a98Ow7db9w7MYLQMI+SIbSzd122kiiOBJ8AA+/qLH7n+3FtnsxNsYyullcZ\r\nuHY6gtxAvOk4nH8gl/8tbCpQitTv8paAYWU47YAQkG5XIVMDQExHdIaytrUz\r\naG6iqLjhyM+qvzJpcUsSow/1pX2HrIU5LOt5EHG2tbvIB/OqQQiqelzT2omn\r\nmGFkA54EUwEA3RaJEmLvIOpWmsThy4+h9eZMM/Cg1MQZ6Y91eL7045mAe/75\r\nqMi1JxaRRP12HDXR+Rou+2LTHaqHet4b1Gu5HbSEKZTTNt8zIhViVafAfgCc\r\nkHpx7enaNFRbYT3CgMwFPih/kS+637XC7HA=\r\n=YkjL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-roving-focus": "1.0.1-rc.10", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8d69f616aeec038d265a651e1565db6832664236", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-pS1Xq8QwA8XdLwE/LFgz1iz+OpIjvtVbNDhhHzLQe2Atku4m6N5W5In2U7v+He452E5l85YLObEPry/j66bpbw==", "signatures": [{"sig": "MEUCIQCc2JWRlwrTcTvmg1Ln7LY52lp5nQTZXJtP+dmY7oAqEQIgR1a79b5HmJO2K9KApnRYep635clqWTDzLGhWbdmPPo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSFw/+K9boMkNouB7+sMJhhox7IeBLG2HcYcruap6S+oiK/GPRsfH7\r\n7RJJyi5jboSP+axzS30R/XaSm9lBEiuZBsehCgO8gYxL7gXb8TC3ZYk40NzV\r\n84Yo24g3ct8U52oTUgrr8yyR+3bZdSsct31Eo+ltdhwt0+l/vVlOM8MLgdT2\r\n8PAtL8vNKSPHrWSR/ZI7bQm9XJ/AKqMnAEqvgHxU738EC2RI8KBmKkg3Jzui\r\newQ7pLF7tpYxl94h1S/CeNwHJlI4bULq5LPKjrl7dt645J7LphSK/UU+URQ5\r\n1Lm/DDbZuuB18rM3PE7eTXuE4QAYai9QTXTfvzspCsLm6uTfIHCokMT+/v1d\r\nXHV6WgBPvdEQA8GD3qxgcs2uoR3B4EgJtJnY9jkghnByulO8S/Hf2rO6VmuY\r\nTY9G69qFXLAnpPJMiY1frZ2c2lsCOXznbs0iAL+w71fNHNxwGW1xXl3EAZK+\r\nsVd6yz6OQlVPTwuUB5nmOnjlE62fJRXY936kkpE1cTC5zMcd3YkdZUGWObld\r\nWVBPTR67e63jTI34w244Ii99FVmDsaTMtGQHmR5wp+zqr5LOL0T17MdfqPAb\r\n4mnEKlqfGXsIod1PGrKFpTAGBDLWMWByv/M35nDK5HoIctahrwZOd8IC5nHY\r\nnFT7W4Qs6ur2iPXEhWpqqsvn5WGLK8MkV+s=\r\n=J8NM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.11", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-roving-focus": "1.0.1-rc.11", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7444529c129fed65d9a51d679f08170c587cd863", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-t15IE0k3XDlmJ77KrNUAzl0xgTqU6sdExLABRwC9aeeXcOa/xsjWiQapHNI0YohlqYW3tf0wiTYiMaGyCU69HQ==", "signatures": [{"sig": "MEQCIGIRce9TOgJtOrvnRm85TwxhFngE34igAcEbK3EPFwTeAiBc1iUouROSqrU+ByQfeMCNuD0z7MpE8vzxENCZKCr2vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRx8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMNw/+JHCVyuPU4MLAm1NL9Fc6m5Amc9sURlEBhTVqymXgRmIklsl1\r\nf9orPpbNLRPkUWI6Cy/+sNryBJnyj7O9FEafKQzMBaqVZu4oGSZdnlh3iNnN\r\njAgatxHNg2JtTBSbhogcVFbldQjL6jgXVKNmXOY8CFBAzmOzAs2qFLvIwVF5\r\nK0AEz1WsaH5iAMMXHAFEm/bnWDa8bxq1imYJ0qGZBDUd+hfA7Dx0NDT+zsHe\r\nWVbW7ewmx9dK7344q+YNPCLyR2gwG4sRqhJgFvjiS7eMwEf7m7whWLgYyBjB\r\nfu7PcrpDY+1y5ITAY4owEdXikIm8ILQy73dTru7gPmODERNBfjEZ7A5Dx+Us\r\nDungcZ2LU0LvIQmiWZHdxpmyhavTKBbNbnlHLeytlHD2WIHGKdDfE6thEBxX\r\nRRjN9YIzUr1BITLDn6WJkZprBphU7yzYtXRTaRTql43g5er7SH/vcy8t0Nvv\r\nqcCcSYuXSb0nZezVv2jCwEZXm5c15bFjlYkXo11/bY5Uzu1BKVBTEiiNA7km\r\nAxfwU8mHxt204F6gIn2ziarumiawfOFnRG93f5oeTxLnAT8vZ9fjm5okdSkn\r\nHAuUgn6k570eoZCeYQJNtGSRzIrZ2eMqRlXMIUh9Ch9KXrgjlVSNJ/rvYmRm\r\nHuLAg2HYzzl4m3bq9w4qFzYCBvT+Zq1xob0=\r\n=dQM4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.12", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-roving-focus": "1.0.1-rc.12", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fc473846e683f770371537880cc3cf9df93dcaaf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Jw38L5SWZIyemDGWE8EntkeHaaHZjppzjA4mtTuGFL4gqcLugQ8AcUyetAdpSFq80eHYtR+Ena/f0DDceIa5Aw==", "signatures": [{"sig": "MEYCIQCPfW/lPb7O9FYlfb5SkcropBXZB7Y0eB9hW7Gb9kV2pgIhALQvX7G4M/wn7oqLs0RY6uFPmsMQCymbpaYUZqE05aFg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPiQ//bWpjDXrqcJXFvuZP6kQZgNzmvraC1AfKqvcASyu7beG2218j\r\nxVcdu+/8j71ztay1JI3TW4Qapap7+pA/3cWEwvTXOJQesPxsWQHC+FzQREhp\r\nj5haVGCLZWCMpDdZinGtccIQrPZ4ViA+g1z4a2VJJu0QeWKq+sn2PnkMEyOF\r\nfA/hwYbmIIVZsgMqpfvXuDtUsW/yp/JFSWIVttkMe8mbNOHpsYHoA2k0vCbn\r\njib3tAu8Mbun/tqbLVzM2+uIOVPKzyAFK6xggZ7aty7NxkK35nBaJ6WNE54c\r\nbFZLolv8mpIk9QRCtlovWkptBzwAbFrUqaHnSVO47M+3WsXTp+jP1sfy2P/D\r\nOCy5DXU8sxh/5Lx9/YRxI5bqocrtwyhiXLAThb78sR1rM30/OGu3YwYu0D9z\r\nqbZSdPMEA8d5mvVK/zsL8FmdiP0C00oGQn3WIny0koV3+JtJxYe4EUSo7+eO\r\n0A8d5rgcb//IEuztEgbbwLmSiMDr1gEtuxCp3wbIt8IGnXhoXKyU4J7w/vId\r\nCZUm5k2FtfMw28nTEMfYalVLx0F18Q1prIsjTqD8AMlvvlhwclt4p3pgpL3l\r\nhrlrrIb82AicvtblEFX21RhXW9SHugtg8kDjHmSgWELeoCpDPIiKgnLiZ3D+\r\nvzPeN4a6wC+4OBTlbSKsaZSXzQuPLoR5ONg=\r\n=8/lf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.13", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-roving-focus": "1.0.1-rc.13", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3f5554df7217e7f9f40b6271ae79a44fe0c1940a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-w1+FLyVDk7bYTzZcmXJfrJR/B/QO45JN5LQUgaTBJGSMV/DlBI/Kk17u6swx07Eklraw0Jqw8N4pW4mlQF5i6w==", "signatures": [{"sig": "MEYCIQCxL3ivdfdYOIkZzijnjKZM0rf/0I/hOTfAsp7QDuUK5QIhANpA3qlUF0yUkuyCP9CjteIlq/iTXuKyEwoSAsDJ9ZuQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnLGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcvBAAjhl9hP0FfOQjPV5r3p+na5Zv4tJhRQNRKKz7doIuUnWv5Mcf\r\npkwL0XE3ogl3JrW/KbYzAYCKY+eRfpaKBhNOZHissIYztdtqfgdwsg1XnaZm\r\ndeTR14q2GesEi/EO1QvBkY9ghkbtJz0nMe7Y1mdXYV1b5ftxWfCEuWWP2zNq\r\nYc/pU9ck8y+KNx/rORoK1YtLoSj+/56P7AilYKf2F4siuJC3oBlx9llDPasO\r\nKT4UtJ3qAwD/ce+UW+dRVJEsEQB3LZaxebKEXUt7X4veYnGeTE+4wQ5qoBHJ\r\nFlzsjv0b53FaJYPGQOANuR2YXY9b3pGrst+YS+sw71x61lpI0gp5MbyoiA5P\r\nze6Jg+57r2j9/zise6bTVJuXlAdUyUraXf5I1GAB0Do/oEbUCHZt/GcD3zHz\r\nKdABZc1WTizIO/uiuY6otKN+O/Srtbds3xWzEPTag8aUPL0rAqSBu836DN4F\r\nC/MSHoDPjCf4Wnc+RhWCpZffoV1j5Avh9oHB5sETsqHSXsXBe9dOMgoC5xIo\r\nVfgHc+wLCJak7sfwXtqaWgx8uWExRcWXEUP0hrJtRJEO6QO3YlXXE4iIUFTo\r\nNkA6jo1POj7a6suweRoI35H2xDy5TKDCNFufNv19Gx1idzzXA+y94HkvEbog\r\nuSybRCraIOmJi+bcmkoJ+DhCMJKDoo87J9w=\r\n=dWRX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.14", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-roving-focus": "1.0.1-rc.14", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "17f7b533e23f0ac453e0ae8af5db0ceca2a51ecc", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-22eGga4S6rC7sY8r0ecs8xREK5ZnWCDEeM4gA4g1xNXBvWl4/Y0Ie/t8akzk7/NMOaRXGJRxtvWv/Q+4touN2Q==", "signatures": [{"sig": "MEYCIQCKk74WyxEu0FYJ4jhr6EJVee1TuEfw3GZ5ACFvkS7MYQIhAK2SaahX1PXr9QqPbsNLXABsGij3MP4hzrqrK1sKB8WA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxjRAAlQrUTfj7If2dwC81zEVJJ4TiP/ddZh1NrYxmlmBtTkahqq3r\r\n3yM1UH1kBdvHxEH97vep9H8XkqqTpLWmbquPkwhkCP9zfHw7dx/F7BjUYVa5\r\ndG8fLeVjxUtMMuzB8KMqVCFdgSZ2JnjhIqjE06F/q/ekIcVhk4ucyrSYQPhS\r\n3+x8NzbLKvBUMcXsXKgGqmn+36u8wcmmbcw6oi2C8lpxIcvueMxTw5v/14zx\r\nSv3bL2tvdD5bCxL3Kb+7iGCDAufIEB8V3tAc5X+1l+HABBt2qaDzcrRyUnMJ\r\n6E5cN8ZYy32o4dJShkNinkmaoW2taQQ3GZSl6P/ynfPzZXkVmq8YRmiZYdFz\r\nvY9d9az6hG5wBy3u96kG1iFkL3I2sc4yosVLAv5pEjzBuaRKd373o+GfvcPJ\r\ntq4QyvN5qyD/oRGs6he61qYN0dKzqhpG09yu6hQU4sq8cVUiKnVqMCV+PVOy\r\nyafQ3wsXw91lye47hDI5jU3vl2Ei+0lLc2dK4sW/1z4XoG9yAj4fIBBjDFRB\r\n8lMV1wBH9mD+RCo6jQ8EzQU3vHCA+X8Aqkc4hN7HVsBI4wCPThd1WvL0Byua\r\nwUJt0OuF1sHp6snp+kQZDXjyJSs4d+Hii+U/n05iz12ByO/b1QNZe/kaI0hL\r\nTvz1IGhv+XHoQTvsdtdMo85o8ZLlnrmjxtw=\r\n=/a5I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.15", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-roving-focus": "1.0.1-rc.15", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fafc6fef66f69978cb298d11733f82eebe3fdffb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-V7JomfA8AGQ1JJOlvfzMP8Rby28gcFl+LR8NJ2dmq8EFVYsxKmAYruoIxO00ZuVI9en//s2h5yq6VHRowCTFSQ==", "signatures": [{"sig": "MEQCIAMcei4+VtruFyKOqR49qNduM1BDyWja63RhuLslm87+AiAkmr+sOOXjZjaKxNktuKkDMn1446h3TaRPjpTBhb/V4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUK5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAmQ//dk97auuRgAoNmqB0l7XBq1yQbP3LcXoMt+9UKb5wr7CeSq3O\r\nqIMrR/IZ5itQKX7Re0cFm/T8uNHK4jNITbo6QriMXRmuC7ixRAObzyQBYjM4\r\n43/6sd3Zsq+znjsf4yrncOw1GxbhQOLBkosicdvbSNtW72Qe/XBPjcfgEWZB\r\n4HfxgZQ9jUs7Fj5KFkGnhB2vF8oFmPQN6Fcl4d874KJG8ZxNeuE7Wq00t9fP\r\nx5ZzaPcQmRk4KdKijvcdbh3c41qpUR5EMVmhFCkDfWwJfXD3al15AzO9Vs4v\r\net4xzjR1iUNo3E6StpFUMjDtn8pkrTeX8faqqF+R+2KG4MyqnvmNF0thNs8Z\r\nDSmgX3edEihzeyWV0SFGm2kF794emk3ijlL6FSnRhlgaBqDExgLKPZs7XOR9\r\nhPyIBviaztcABIYUbkDPMFuOqgJUjb+7SzvCrieBimZFmB5DyKPEln0O/8e4\r\nCwY9GuMV9HQkHS1H8eRV6bZW3WzIDUW/xRHdvCnnFOXNYQR5XumspoglytSA\r\nui66QYqxBOGJXIpwF4DN1Cz3n8QFFJTWcQZ1ueSc29aXCAeEoxf/O6JNn0vt\r\nYanzZ6fJauN4NzZo8iJCCpEIWMAAZgbgXeA7QTiyLR1RIic67WeedZFp3Qz9\r\nPYh0s4igCIfw/FBNTUD26BaP279JXC6jAqE=\r\n=RE2+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1-rc.16", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-roving-focus": "1.0.1-rc.16", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0ae2c83b9c452dae3f335d5dd83eb3bdd029cb7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-cFBtwfOYaJh7BwtLGE/XfLjrAyQ5/JxM40ja3JncInGrcIV7LNeZBl4rRP1gjZN5P0OQsznVRwLv73VVzSmIbQ==", "signatures": [{"sig": "MEUCIHApJhLxtzAWOD2eR0h6eboLlMj7q+ZNskS/DXDCxqCiAiEA6g/wsJtIl4qfWtrpBvA73xoRKqBnCaGbJsuVP1B1vUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVOA/9FMM3lY8z3gVRu9so1Png0qTljIqWccb1nxap3uGO3ws0GzEg\r\ntye6j8ADGpbCTRs2MLTZDA62iHrAvfIiHM4DZ4YoV78bUDZRq5kWiSfS0hDS\r\naZcn/jv4DKqJPAaaCuV1Sl24/xD70kITBI6RNtqXgUGzAFfqK23AIt5b4GMx\r\nD6EWoVLBhcGgVeGWJbm7wktiaz8k8Z1tlll8wXBpk1tEtxXMiSRBbqMa53uN\r\nh9ylehWEPKVYW4HYROuayBdrJpnbp+hj/ah7Pm/F32D4qiRitp2Zv+f29YP8\r\nXMijsHGYfeez4QCQeJUVcc8G84S1jXECh4rwGmCcKbar+lv9OgNFwGJlldqH\r\nE722+6qPXks9in8e+bQC0sgAeHwezVADgVoSA6NN73h16I7mZluQLEn3fFOg\r\nOpA+zyynsknzHTLhhBWp/rrWQPelHft3kM2r+CnidRnFrcxJyyBlidrM21mI\r\nx20FvbeCglmj0osNCVmZN+B8K/8xC5Um4v3ZhzoZRwOziMfVfBfYplXi4azm\r\nkpBfGxRi0YFM8PXi7y4gDwcCI0twXW31diyJKUJ8dC1uwdnJdHvkiTMoGsxM\r\nwVbOf2MERUYeI29Y2aYQ3gnmEC54zmyIWMoeNv0DL15QZdXGVGCbFqphvfwW\r\nBu7zQuRTdsHbZFcC5BFdhoZ3gawe0S0Tyog=\r\n=iNP4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "26ca9629b5b5e76680ddbd1d362d8bdf359fa14c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-eye/gYvzy82xtoSSeu5Pwlzrh6N2rOcDIwAI7xMatu622Qjlg64LtwB0PSh3iWdmn6Wqy1Fjo5twNPQsp0guiw==", "signatures": [{"sig": "MEYCIQCJFfj4Hi6BBDs2YhFDvYIcTiqOzD87aWzMZVYa/5zu+AIhAJv9gKgNUimC7TXkUEpAGIUePBu2JUrEc//h+GUthn4K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2hA/9EkXWoWwa27FBaTnJEYL9mESWPVJiB2MDolL6gKyoCmvBhH0u\r\nGAzTLaM+sBqEg4970UP8X/S6BPQTxw2z7j3MsUFTMyydphuj+fM0aGASc54U\r\n6U/o/7iulBOfdda5avxq+9ghCNjVnhtHKAMDumzUoiLEw8hWa5zLe0I4bmSx\r\nRUJDbNjtv87xLKJYlDlc6oe9H8e6nig+PWGz+EUlFgM1IKEmervzmTeF0IxQ\r\nC5FjQanC43I0xP9tODSodRF1kxvq2Q3QKg7BIIecvHRKMWJ73yNIYBuZaQ0L\r\nGdLlbxfH6MvS/FDUqelhPDVOzRVcHmwFXyhW2h9j2250jzO7OjvCDDa7K0Es\r\nB+z7H7j/gGACTjR0jNJ9V+FJoFAc5ng5o9YvQ1wFTXV9W9n17/LzvgycOMNE\r\nJInqBZ6MaKr3AmJXPuZkHxrWnfwOcqDrxly5XfRuAf7QinW0lY1ykBHIpcaX\r\nk7LSpOdIbAs5fOXTpMoj3UbpHzZP+qyKHfdesXkaZFYmMUUVgJHPFxne3QW5\r\nJePWTZKIo97+CHEmHjaGH2BKm/Ch6y8EfgzLBi8ZU4gJDRvsjNdocDtamsp+\r\nANdwnfEGoRkKH2qOEVGlnz655ljEB/lp3age9Szeh4GxXey4GoEfJnX4j/SO\r\n9SpMv+MxxfLsSWdUB5ymswN2lXv/9/UBoK8=\r\n=RcJT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6bf96a1911e948417a8a74ffefc240b6235d1458", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-w7gA+THzanMen42As3gAR+jly9UApteSNN4+VqaoMhk9ktfp2bCexOWr1l/W82NIh89nNLy09TRuaTcoBmCbgA==", "signatures": [{"sig": "MEUCIQD5s1T6JRpQc0ppA4hFbKMGHweGvaed/TI756N4MsHTAgIgJZmdAOuGvwYdWKyPdgjr2UcftmDnmDkMcq6mXYJ2pvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxW9RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmLg//fMQSmzOtJtejLDMc1eXzuwbzMJR/w54mP5RN7Wg1I1sYC9tp\r\nPiA7tqR4TMFU8uL8kkUs+23RA2RBgPkvbEk2oU2oqm7AoW2PQqeJS/pnledZ\r\njeERIWLE8oiUqZdxmVS4/67rT5IWu96svEvWio+0/6zARk2wV92+1ph4fMQI\r\nuCWMOCOUpaub5sHl6Wtqfvq2Q5ulYgsTMQVy6H27WmJh5jx90/a5/xZlIpFU\r\nBcBry9MQ6VERhcVfgpRdhYBOfxEiJWxTMUWl2NU52t5NiFQHEfwerXrBDaaR\r\nW7iZ+4Ib7k8NDmomlaASYu/g2bNdSGhJoOVu1cPFvRBAVnyXroP0DajMcCtj\r\nQ6pEzGiKmX50hXdwuz2FPCZBWPHxl1XMwOdVKQb3+YDXtYnl+JphztPEjXLI\r\nkFyupU5lsMvUF6ygUM6LQw/t3MmnIntPQrkq8yFLO0cdbEJ2KAqw67Vb4TvX\r\nqN4dwXbDZIBXZferO/tEcF2f3dZVHorUeKDhlGp65Yinun26hybZjYj4T/Y2\r\ns8srH6nAPCLocrIoAz9rjKXwMXNjzPbVVDfi9nYGr6/mUthn7J6F3PeBjg4S\r\nY8MCY4/5HpPppffopu6fePZ10ghFtRd8tzjdg1QxOQ1IiHsugAHzGIYKOfLp\r\nvL39v+TXadaLXJCCYYxW/ciBwkBd4zmfFOc=\r\n=TTdR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.0.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "df4cf951a4ed83a8aabd8225ab3768cb82431e35", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-IxtnPq3/05PcMngW/6wev508sDvMNIKsX1EGXWkZqCVs3miwlm7T77UzmnoH1Jjc1DQWb1iMasge0eZLpN0UzA==", "signatures": [{"sig": "MEQCIF7W+ebLbPiwVihzWmYMhvR16NL1DV33LANXHomtufDMAiBtfigUiZwxSFZQZF7YR17ouDevSLyihucgT2PCQUD5yQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxp2vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFDA/9HskUqLlwIyG3VR6+nzKTC6GawZBn/EderMO7Ge6B0Kn8MWHS\r\nyBv1HPQIM2IPLiqkX7Rk0WmJJsQPvwwMID2xW3AO0GrNS2rN/lxCggsz/5hz\r\nfB2D2CPoPE23WDVcVijgJivbKHmN8qAX2buBQGAAjRhehskk0QSouveMLJdK\r\nTPxC6PpNntiopU8wfzcNmVgZjVfS2x2HXKHxekCkvtUaDrmjUUZLpzTQaskm\r\nUf02c4Gqv3y3sZAQBmI4eNZXVDkuED+V//6pU2X/SoxqrkflsAWByVG8FzYH\r\n976LEDSCqjkqpy3bG7zkp0R1yV1wWbYI4+GtqGi0oXe3dg1VTtRzuqWf5EcB\r\n75d4WZiDA2OGWQXm7fv8Axq3bPxak3Ow70UIt1D1PLjovVNwMGNCKVY/EWza\r\nECShbhMLOzejvdF0OhcN3qu7iVFXb1cNIFr0JXNxduyz6VzjmS8gbV4RczhP\r\n5yVLFSS06eYzytSmA1gmUUF35n2BrgrHndB8VKZmIznbCmcV1j2Nqbg8aCIS\r\nFeD/tj10m2Jj9x03Mx6bXVlApd+1TqagcQw5q6wyfxTKKTOXLZOiiCWSxKdX\r\nKkY1nwL+8AgNmLgqTUgKqO+JPzhNqnIeMh0dE0hXlQ24+7fBxtuFygnQeHFd\r\nd1DdnC/H4t1iXjY39p+al20e3oLGodTjsBA=\r\n=ZA8O\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.0.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "41e78e5b281643dc132f6d7fa874427fff46acfe", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ngnTFYDdrzf/CrrH32nWlkG7SizFAmwtRQCW/MOBGzADw10c4Sxd/iRse4rohH5XIHHHEsC0f4jPJbbjgcFD4w==", "signatures": [{"sig": "MEQCID5Y+wa9sE8gZue2g1BcCS26KtHF1UVjeYLcO9tFaOiwAiBqeon0ZtlmaH5kl81vkP0yI07coB/1jAqRBh8ER62kzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqFYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGDA/8D/0haKKtmy7/2nvRyD0LZz41teAjkUrsSO1//onOeCgkiIgX\r\nBtjb2mJaBzxPhakn3Sm9yx5mH+sbBowXhtZ/vtmydEV+umlIoefujj901zJI\r\nK3qUzc+WtMU3BFuImRq7lNtsYFnCNFgcrU0z6rW/R717xVD76b7GD+BTapQF\r\nlQG/8J7wGMOzM8zAj6sdPNSllcp1zymTwQlUn4Yn1fFa49IsFjXfLWDgkT5S\r\nPCoa1qRBvQV8VB3jGZ952BDhpusMTc9a/vDiAsUOKgq95T1o6EWs/89t49Vk\r\nG12flk+IyvRMzY7ReHdgIszrjRn2DsQG54pizJuDpi0+Gu47pyi7vN0OJWIJ\r\nmMboIP6pUGcz+ddz8iFh1zwWYLrKJjQcboGcXDWmkvPHa9DqPkUNKgIO4/5m\r\nen5HVumdTbh8/vErLGwj3OL8wC0yDkR8cf84HfYWdIafWY8ZcxoWpY08GPjS\r\nlAs88Zed7ofdYpNq+YMLGBEd7vQBs9KwEQ0e3laJpIA1QCrMAYZZ0K3NhBBh\r\nmUP8j0j6ni8JurQMqeNSyG40Bl9472ZDSTCnQ2d/cqsU8vKc7CAcmNQTnEbU\r\nZwU70ASN67iaebFOEpCSH38Ha4MPhh0zFmveDLxugReVyda5jvZzBU0bVhAa\r\nD2pVSPqLgkb7t+/pz8jFO7IE28hhIKl6BQw=\r\n=Md7N\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-toggle-group", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "85161db71554cb3809b9953b892f475a8dcb3a13", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-Tm5Rn5pdgnr6uexI0cM3X3EZKqYfxlt4iTYx4tBbhSJHtgasgSyXezlHm3tJ5OfSK+1ZIQYD9xV4izifkLuuug==", "signatures": [{"sig": "MEUCIE25z5usijTKJgQKexjY2wdAD79yMxaYCo7ZLU7K7q4yAiEAonXIcQ5u0Yaq33T4NjuL4g7wFtV0eYQgxwPTvKOtaJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqVhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJlA//T9KQ2vbz5nfmjcnWuJIt5rHX4XgNFqcFFnSyiCdnRBjEDIPj\r\nsZ88ekUPukRWTtrQk+RyoqeJTTwNyTfuvp28yFknhDqf6y7cnT09/6+6gMH2\r\ngQfiNIh8n/wi95btYpi0qWW7NpqrnzP67zxOY+GjGI5REFXPZSrDWyN1PjIn\r\nkTmRby9ilOKQAAM5Y1ccD6klMlNULRU3h4eDTSgQs+0j5JtkpvCZXjbeTzYp\r\nTY2xWsThH++4cJ5z82TdhGs98WKbSiSF+GAyhSJ47TdSX9vwqlHapPXJBZnU\r\nvdJfgw/Wofop+oYlkDYIP0yLgpiRG4H+I3k3x4qtKmKLY+19m4r9f2gWMXyU\r\nTtrJjSFs0rcpzSZR5K94eZSF2LR5VA/2dD5W7hSVhp+JqUw0GMZ07IWA9Qdi\r\nX9VjPNietPpHFLPJd42Z/kIH/APD1jzjjCz5uzk3MqSQXlU4T/xztSdRNXFQ\r\nMqVdpCNGhaF4PinIOILQ93g+HOZaG/GyXwZ/JK1lSlM7+rXQV5vEBg3vM+yl\r\ncFOf0VJdqpdFd7lQBDq3iDiAqb6krh3J5Rg8BXvmXE3Ke6BMN5fETwaU/vXT\r\niouPnPOO4B/wg25Ea6o10cxDgftqM27IJ1iXrhf63YdH20/2Y/yOUmr4ZfiX\r\nEJ+ExnL4RfhEws42ca5nDhRRvp/kJj36FvQ=\r\n=kf+6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.2-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-roving-focus": "1.0.3-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff535732c8cc287cc395ef0c001f877ee8163f92", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-YyrZMmGVg1LhEetemp0IdUNFUQYvHzHiV3A4bIP/e9D/BwOLBt6ICRa8WjZ7HAzU7H+9jqA1AKgHqFNITHLxGQ==", "signatures": [{"sig": "MEQCICU8+LFY6Fwu9w94TuDrqbP+MX8t287ka5mW6QWeXiUbAiBfqJK0Hju9y7zIzTo0M2a2sCrzO2cmiPDc5kFOIYpIXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzgDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG+A/+ILc5JUBmTX4QmmKPQpcBnVBLQk8Cw17Y9tR2jUKjeh2+qIGT\r\nPFldd7QZyllh2cRGh8T/jeZiTLBrF6Jap5+17lyk8e6cfTm2GaBYIs9kDGsk\r\nHMZ/J3EPqAn2jca9uTVsQlfXR3lAGpql6ADqpYb5tNobsn1ubYjXTzf1UrkJ\r\n5/I33rrmEBUJXkf34fcHMyVXP61jmEAl2to3ROl9jK4H6l/oq4j05ELE7MqY\r\ndgB1EhxpJNxDUejhCZWe92ho0pcltpq+S6L6VvYzARYIZoFj6mR3gV2ODVwA\r\nhVTkgE6i0IKMf+0UCBD9QmMZ84PRzl5aUz73xfnrCKovJKlK95HIlsOPhu0N\r\n4ea5MhZiCoRHVyVUTbqfhDFGxcsI0ljKL5Vsovbo2IbmImRcASv02bdqlizG\r\nMFTkLJBWVqGVJb4Rwywi+j9EZTswqGqtXsWV+8D92Cl9za58VCbg0n3a7WGO\r\nhNtXRYixYR6qxciRVHcGMlAaSOSEbl4PaoH0hwywMD36Y08UxAYEWQTEqb4e\r\nLbYv2DpSPfkBxN2fNBvx41MgVzQIhEwSFzA9si+4BVS1FUNT64sBSyJ3D64+\r\nzIEPgRJoLGGB80p+ga3paSGxq0guZurc2c6AFkqA4Q3+Fbt8tFLgSFGP8PDu\r\ni35nUIBnmurECf0alOxgmMNssCl0+a4jjbk=\r\n=WYzn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@radix-ui/react-toggle-group", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6e37d03ce7d0102250ae84a26cb740c8268db26c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-fl7yQ3Ty9+jeWb+TwzMkidXhTgFc5HKFxaEDn9nShjLPS1ya/bA5t2JK8wkUaUH6mo6yU+M3vcmUkxnchlXYtw==", "signatures": [{"sig": "MEQCIDpnLRJowmBGET+hszJuVoqtdcR5N+0qNM4yw0MhsnPeAiAyfoQzgF5ai+pNuEkJxFrphxn1rncgIcfZlPTQbujrSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjiA/+PGzm8w/DvP2F6oSBfO+bX68hYNl1Y/zkABD5xlzgyzmqTm5Y\r\n2QTtP0v9oJmNBss0NUxoZnsCGWQOZxuF0u+j2bfzSv7PqHHVfXBk5XarSgzO\r\nFokwr6i+DAlE5WLXKYvmo9gQt95qNLQ5l4G44BLKkbbHG4qdemhwqwcOftQc\r\nKgIVYIF8/8/dFES5h055VmOZen3p1ugUUBp2fakNOlLb737/jGHPJ+hDJq4v\r\neO8NRrJtPeWGfnERLRdm5NmPQABAurPmG4ej3sDeI0wvn4tNCijavcZxSMHu\r\ngiN9UFqMKFUzY+AFeD7CFhG2idvumLNwHc1YG8RWcyLG1Z7T/tbMSPg4J9Bs\r\nQx9HxBbWV/TkS9JwT4M1/mlaNEhoLBPY1XeWmGUMhXKRRkdagUY4qwp8e6Wu\r\nRCO6oMciBkGuCeH0zG8JHkUZjnXDcRau5Lnk7RF6vqKif8wNjCMpxyyCmGHF\r\n9a16W0kiE/neOgPi/0uY5j/riVuOY7XdOPXfv7Iq/WqspP9xmp4hQc7yjegS\r\n0Oa6lKYQtk2jjzrfrcKvm/ziHPP0A1HyZkSufSpmRTY/ktyIQl53xQ5rgl+M\r\n46RRkLfrgn4L7RtFXj+U8jvbuuAjpObIxh+XcLudjfOrpgOW8NV2BVGl7NWp\r\nvrI+TeTrjumwnMeJ7CfAcp1Jprr1vfr3FVI=\r\n=5LKI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.3-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-roving-focus": "1.0.4-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc3f7e06f9ee4079e02e25ed2c62e672dd71653f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LJ2R7OJ5vBSJn+uON7Hs1nO9MD8Y0SBU8fDy/TNVHknp8+P9GaxCnGFG9XlTiP16mE5WjOXN2BSYAEB08nbm/w==", "signatures": [{"sig": "MEUCIQCTkBJkEQR43TilNua2TtHvBGjcw7b6qxIMIIEIpFTaxAIgDG3yuAg1l/+aXlsW9VGbj0C4LbeQYUphkjhyEYBbdMo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/vA/+NpwwoOFd8fHZEJV+f+bWaeyOtBgW8p0V8nFBBlezpPnU5pM/\r\nm/KyYjMMg+kTnoRDzdQf1txK0lkTWr0febpuwLsWbwh+KiFm9g3iLUVBCtwP\r\ns5TohpOhcdVsZo/b+V60L4ygN3J01E++uKIFceyuo3ftr+TwbnenvwPIPLVf\r\nDlwQ4lFQ5iS3wI2txKoTXLph1Z9KwV9FWuLGdRMe08j+EBTEtTJZ01uKRg80\r\nUmxdlMNbWbwT69Wiiw3hrBUj/FWNvVDrrXLXse55cDtlkTaBRHP3iH2wIR47\r\np3TmKF+pMMOOuL+3X8cftrU9f2CcRMPl8IjG0vhVRTRKn/Guh9Iq77gO+F/R\r\njGxSPy/64hsK3Og4yunuU85bjCtw4G29c2Q/LVSYwPtR/6fEfLtKfV3XRMjZ\r\nGBjELoJtc22d6Rgh85Kc9KimtBAYyOmd2Mm3N53x//ityHOhUrHouwIj0AGC\r\nASpA83asLo24ZgrJ9boQL+8ySoxrJEB0jX2guqDmpzi8kmDLY8BydmzOeExP\r\nDZC9HZVR0DBy7d4JMbL1j7a/rObFCVAymdruMEecrUOhJjbF2xF1WtdcvuZO\r\niP1z9pVOisj4EIziGPUPw7XOn/GTtbj2l0f3yupS8tIhh5kAaPTn2bySfq/U\r\nFfr28ks3aQAp9jcKhIzP7JTu3apklF/YM+Y=\r\n=/UMl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.3-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-roving-focus": "1.0.4-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33ad60ce3f40fc0b2e7326d1f856aea05ca07de5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FZKFHtpyYXfdEoohukPXmQVZebRVmOmMvEaPFn4fgd4YmWyHUfY6xUth7AYSW+AXiYigUTdaXKBNJkayPlPrtw==", "signatures": [{"sig": "MEQCIBcjPOfREky2GKMXyUG6Lv9lWfxgjibumvfG5J294agVAiATJy+/OrX9VHNcht7QR+TGGghIhIWl/LCnXKRrs5eN6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388}}, "1.0.4-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.3-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-roving-focus": "1.0.4-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf9e5fee48c1b2afbe7f4c22244ac1bc0be500ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-3tcq1H+sLmLJt2lY8a0grNs5LIT7ifR2uMsgujSNBHxnloxeiIU8MXpgjHH0b3cPJn1UBqaHQWT7u4BgkRWKmg==", "signatures": [{"sig": "MEQCIDY2K29YB66XWuoneXe0B4qpCsQci+vZE/SqErhugIDGAiAshIETjYd3oAJCBsrH4BCiIS68WcPvXatauQppC92ceg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388}}, "1.0.4-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.3-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-roving-focus": "1.0.4-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f0a770d858cf98ce169bd7faf42ac814c0d784e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-iFVm0eB9qEdGv8WALqydla41sRtvUjXye+6/sMpfS0UgKng9TLk9CBxm2RUwcHI/Fcw/hsa3Mw1sUF1ErDgAsA==", "signatures": [{"sig": "MEUCIHFLf1aAlhVUniWalFC0JpSp6hiweEufW8zUCYFjuFLeAiEA44JQLSXzJttabpuHOM58CaI5jsG+9El+y5a6SdmUQKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388}}, "1.0.4-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-toggle": "1.0.3-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-roving-focus": "1.0.4-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f41ab626d3edc2c9fd5b71e4712053e882010864", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-RjFM1tvvKTCJRDIeFsxt6gzXSdx6UG7vv+62M2yDO5uvyBV315TQbq6a3xp9iN1blC/PSWZeDID9kjsUOIDnIQ==", "signatures": [{"sig": "MEUCIQDrldlFDBMm3w+3tdXZvJ+EppJreuE4JcLK2tyg7oNblQIgEDBmak6Jg0h4crierGfW3KTADXlhJI1ggAeBv/7WHNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65388}}, "1.0.4-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-toggle": "1.0.3-rc.6", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-roving-focus": "1.0.4-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ebbc5aea8c2612a1151f5a2296b06f4d45106bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-9ExH7XRoXucL5K9FAB3smlNPIB4HfQXZ7HIcOHVifu0HQXdjoJOETUk3NfuR5pYfhMIVoZeh78cmWO6QsE2RTg==", "signatures": [{"sig": "MEUCIF61ogQcLGmImgHgizWbBN+gAG+tr6E2LKxD/GxllkoBAiEArU+ho/ZgOFEG7L15DkvPkXJTDBgBdytiiMopUcnQB+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68804}}, "1.0.4-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-toggle": "1.0.3-rc.7", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-roving-focus": "1.0.4-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bbd08d75af8630acb5034bfd26c2c2f96d18fb41", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-iCAsFJ7ZLcWvLFHmaWk2J62RzHh5OB5HUr/nG7VVPQVxZa4RL/HNl6cttLTQzryebybuKIcGr28tVO8v709aPw==", "signatures": [{"sig": "MEQCIEV8ujcDmS4rsYAfanE5sThwxfCg2/zPs9c7j/ajaBkrAiAQXcQHvNkicHu+n+4RaVbwjylNtTv8ilWY0h3r3X/V+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68804}}, "1.0.4-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-toggle": "1.0.3-rc.8", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-roving-focus": "1.0.4-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eea0325ba261f30ef9fddbca7ea9b3a1e5c46e82", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-J1n4vnzOgybWFWLJshYHCaxWUaNuBiJtn4gWcS/vEuYVaeGBPOUYICq6C21EAiHh3QIpQ3cuzbG7ApCJfxeX7Q==", "signatures": [{"sig": "MEUCIQCfzpVmhPtM+IlSlETbLaI6XomHtWE7WzeNzWcklmrDEAIgEvaKcrwWqsCPCGJoae9uNE6X8QaLkKWrgCPFwR4Wlz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68998}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-toggle": "1.0.3-rc.9", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-roving-focus": "1.0.4-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f00bdbdc2d25faf84cf05ae1092ffbc6f071c282", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-HwKpePDgHPIJaptaCs1AeHlWWlHdVjl5V9OSGJtJOFQbRpMqgdoseZu2/iK0qWgiANkCFZMV0S7sU+dATRKn8w==", "signatures": [{"sig": "MEYCIQCt55WPtBTBF7WpXIwfGUv77YIm0kS/SzlPBelT4HTg3wIhALSjvN0/4wBVCU3YwHbLSkPC0Dtul2qLH4/VLpCrlyp3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68998}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-toggle": "1.0.3-rc.10", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-roving-focus": "1.0.4-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88b75fe1dbe315d096faa5d98c34152afed7b270", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.10.tgz", "fileCount": 9, "integrity": "sha512-VNiHm5aatr2H0PsCmNb4DNWj4KGE+G2yq0Z4fRGQlRwUwGrFhTUZkz03PRQjfCWR4FZ897jK6WdCNbyzTocEZA==", "signatures": [{"sig": "MEUCICiRKLxbzCxL4w6pRXmeGERakhv1KjRIClGkFpWy50s9AiEA/JbMvlVyw+icV4+/TDlX4AZX3wW5A3jE0DIkHL/WGEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69002}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-toggle": "1.0.3-rc.11", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-roving-focus": "1.0.4-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d65ad0c05b7e6f905b48c3d8bfb779b395119a9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4-rc.11.tgz", "fileCount": 9, "integrity": "sha512-5B93gOIEFK92fNAezlqJCikPNqy37A3m31RFToikBICAs3ofIYdwlW+3gwY/+xvlIWPiP7O2JkSkXHgoHZOA8w==", "signatures": [{"sig": "MEUCIQCRHMFVtCTyOHxwCfrSBcApoJ80TLUtn2hrSNw+7SQJPQIgVMrOCMJk0WbxBMhCmQ2hfn4x+H8g6va3SvgBCpOutaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69002}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-toggle-group", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f5b5c8c477831b013bec3580c55e20a68179d6ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-Uaj/M/cMyiyT9Bx6fOZO0SAG4Cls0GptBWiBmBxofmDbNVnYYoyRWj/2M/6VCi/7qcXFWnHhRUfdfZFvvkuu8A==", "signatures": [{"sig": "MEQCIFZGnTo7dt+HkW8oV8sp4prFa3TeJhi4uTCKkevQ/4kOAiAnzjIDG5Ys2qDo7dh1jH0bU9J+EMvfdwONEQPpI9M7Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bfc2efc2dda8d5acf9c630000918946bb1caa6bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.1.tgz", "fileCount": 9, "integrity": "sha512-BuxnNGzuB1rNXC96r0mWDGzwvkao4Z/elog0fCSmbJUVhVIgmUAelu/Mzl08uTjcAp4nOcnoer4smC9vu8CPug==", "signatures": [{"sig": "MEUCIEx3VEjZ1hCXVrrf3frKleNtnQqvkgpTJACVWBI81zA2AiEA0V+m9PYgEAxhgON9h+ulJDeEbGRzlCJteCPdFz1h7u0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "01e8a795fc554979a22de851c1f41cbf6f0bff04", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.2.tgz", "fileCount": 9, "integrity": "sha512-DKI8SkE6cTCN+xoCYRzaqZXvLj+5OgxzIHzO3Z0/2Axi66HgxvntHQs7XkgKf9+QNKQoC0vH7JpqXB5bXke+dQ==", "signatures": [{"sig": "MEUCIDnSvEt8IruoX8S+K+QiVjDOYVYRNhAQTpb7QHz7fIr2AiEAhLaPjoPwypzITufU8wGFM4eX/h9NOoN8YLr/g20X8J8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0300ad6f5f72912b9afe2d711e18c5982ed945ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.3.tgz", "fileCount": 9, "integrity": "sha512-ajLxiAzAgGkKjm2OOPWp220PBbxCe6zmnbafpTEdQ4OEG76Gh+abdo0hgRT43RkI/nBPGqDwKv5QbZrbgELf6A==", "signatures": [{"sig": "MEYCIQCiTTlOQFUxl4FUc5lH95Du3TeZk6yEQz3f/fsdN+QSxQIhAOnZ2X8XW/s8B5VKWszJ4WrnlbBlf2Hmdel0x7RRsFLl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "72ca8eb64e888710d1210355f0263c89c04f6808", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.4.tgz", "fileCount": 9, "integrity": "sha512-zRruRxHi1EpVhcAjaoEJeOVSoSpUQIhrZYzC/cYiEz1jgLDMuQwcNZRThtx9pDwAdtj0hje+tF9aaqk9ct9y0w==", "signatures": [{"sig": "MEUCIQCJTBNuJvPv5X4Ral1E+hd8YhG/46KSvdmRrHsjFk4/CAIgEmCQJIRJ6966bbbIS0Z/N/ITRBLtPrswgbt9VwQofks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a56e4adcf55e67ce6cd8c602972b509e60b507b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.5.tgz", "fileCount": 9, "integrity": "sha512-d/KGbGirAlkXmJChrwJK2PLMeOh0TxkWKP9dR00jH5UJlUXv7DMhYtvAFkZAy2cs5nkZ2pqAf91ZuKGkn3Nw2g==", "signatures": [{"sig": "MEYCIQCrG6Xx96Pa7cjXD0IHrbY9ct9+O1uG0Yi/HAQDkK2dkwIhALpdQXhlcf3Pxf/RgYqtmObsQ6y4AziZd0H2fSC75/IG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "84c0463b4066d68a4cf0826e003e7a1f3484deb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.6.tgz", "fileCount": 9, "integrity": "sha512-uufV1pXJGBUt+l5yb3IU3c0sF+kmHYtjjFM8f3QXJtgj9DMElcm+DJSxrSO+mm5PcD7yFWPvihnrOPqqped4zA==", "signatures": [{"sig": "MEYCIQC4ERsoj5A+kYCQIsk7dHjPDztHeekCfgr1LjUawL1flgIhAKna56MCDm6GSJIawWmb+KFyerovzbTOAI6FCv3kE1q3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec4d526bb507b731ac6f8c11109b8becce38bc87", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.7.tgz", "fileCount": 9, "integrity": "sha512-1PTDykzWlWigp6A95/MjQwXBUu8CcDQq/K1AuMsbFPYNIX1m/3wlVI08nKZbpuwalw4lU9E27PpseBvs1nejLQ==", "signatures": [{"sig": "MEYCIQDTx/Mj/zVcvnLCnvdQ1Ef1MtKikx0WyZF7EHqjextLlwIhAIlWOZgrB6hTPA4KTcZGQZjwJnsujVdW3Tk2cluHYrwA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbaa4af4cef501e0e5fdf44ece8bc74d4de6f06c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.8.tgz", "fileCount": 9, "integrity": "sha512-TlTwlT3d29WyUGD1JtuFrV6lo7c0GnFyIeqND/IZrUGUQJBXS5X449+8F5ARmq5xvPwF0eLn9nehIjPCJXDf4g==", "signatures": [{"sig": "MEUCIBX/Zpob+5q3SDJFbcMGiHWMf1dVM4JHBc7U5XVVzJNlAiEArpBXDoRKh32UmNx+4CN3hiSd5XgqfNco7HDH4qkq5jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "67f28d8dcc8094fb72782a56165ad340716aba78", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.9.tgz", "fileCount": 9, "integrity": "sha512-ezbZ+iy4Fc4/lLumq5pX8TtqytBrvGgk1gevpQ4mNtpnAQ9QJGI6YZAnvjO6rS7r1X8vLOcWU2A/9BT0TNAM7A==", "signatures": [{"sig": "MEUCICKANo3RWTmkA0meYyMIgvoodKrwaGb2Pjjvi+1m4MAEAiEAzsBVwaAr1PcbSmufStyfnKGUQ6w7ZQaZY9GJZ+Qs5hQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68968}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.10": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b36354ae3a98315fc53b087297ecc5eb327102e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.10.tgz", "fileCount": 9, "integrity": "sha512-CRWZugbq+41cqd4rX8ETm1KIHXkdUGND88C1siwRgr0UHwV91iP5+DIc/8quF0MkWcUmwRIwq4RR6fo6wJnxhA==", "signatures": [{"sig": "MEYCIQDxNYGRrA4SDU6czZjvuZL9z1hv8uVINvDAnpxniwR1MAIhANk4OicLW0pvyvJAn2UXvqR7dkFTYrm+3cZYMEFiXVe3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68970}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.11": {"name": "@radix-ui/react-toggle-group", "version": "1.0.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-toggle": "1.0.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "606bd645d886bd9406ca254d8df6f375cdca4655", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.0.5-rc.11.tgz", "fileCount": 9, "integrity": "sha512-8cG9VFhI8HpzXZhm0a5lmRi+ZCH3bCKa3Ezd6PrK2oBFVv1dxi/wm051V5hNJCB+PM8RX9Z5rk3W9lCUf0x6/Q==", "signatures": [{"sig": "MEUCIQCbuluvX3JOuFN3xGO6GBqr+pAY9anBu6sYR5BKMbwS9wIgWcVhkTWqzdh+LExyw4IMeAenKFL7g2pIuyflTJ3D8jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68970}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-toggle": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-roving-focus": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1a96af8534952752331038e67e27de860d1d185", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-sABwuABCAnLpaE8HNLP8eYkXSgwTzRlVdwP9ECrEFBQjEiNYs30OEfaP5SxDSMz6kcr4V8dmJU1KE1ESv0bQ5A==", "signatures": [{"sig": "MEUCIQD/vFJpMq3JLuIN0pqdcXehKPgIea0siZASonvLaQ1jeAIgIQ0sMDJogdCDhePzcfsqWJZ1lvPbaYeY4cFmeCkpIH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53789}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-toggle": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-roving-focus": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "66753f7c4c1a6a2223484e7983f66742a273c49c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-KedjX27CkhWxYc6yptFcSZOCBDnbXdAyHN33KHfe6UET2QJgojq4uYYZOh06m5PD2zllRG4flK6ev/MjTqN0Rw==", "signatures": [{"sig": "MEUCIQCrowr51jtZGlBgiYX97A3mDe+8VDuNcgHzAyrfeMtD6AIgGQylk1q67IMqoZZmj9r+ZSqARXc8RMp9502WIw5mfPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53821}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-toggle": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-roving-focus": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ad440747577d37a66d95aa075a2f6fa53f1102d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-NTdx3N8zAnQ63Ly370EdhgiwncTpD+wTK9fNRSZxbJpF0UcxhXuetA2CBoujJ1cdq3y3fsxLIB5RHsJKz4J2Tg==", "signatures": [{"sig": "MEUCIFnrGmiac+8W0tmUKXHdWVZDOdgMtT1r4wQEfoTI/prdAiEAh1gzBWblECnra+OPf9vxZyFqsZk87+wkyI+jYgh/k6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53903}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-toggle": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-roving-focus": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "45cc8be077a25d34ff204f356d2f006bc7ba7bc4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-JmoLsh25q1A4ajO+SDGBt8jNaW1tAxR+UpPsNd1cFHflKIiY1pErWS0Yu5SpOSYIisrtfmGp7oL2tgI0YUTUTQ==", "signatures": [{"sig": "MEUCIQCdtf+wJpb+bXr8WPiQJYALtjGybh1IWiK+YuQmqrUEPAIgS7kLInnymi+T4zbNdSosvwFCHzznI+/XEUKKlJUm2g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53675}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-toggle": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-roving-focus": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5596d1f04f018cee08e16c1bd6d9bcfb20989b8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-2cXUykGXf6yUaMRfU1RVkNWxoX7kciAnE1wh9XV7Y0SKwdWOyfWBXaq3DvQxSbM7xacDXHkybzM3W2e5nDhknA==", "signatures": [{"sig": "MEUCIHCjEVZ8K7+wusfZica1f2MYULmB2m00vHw9DumpJU+ZAiEA8LPgSvzCmAO0XG8s4zieJhidk2sdTXPJ3/0dTCt6Ixg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53675}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-toggle": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-roving-focus": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb0af78192577002051bba4bb763479a701e0888", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-nHtC5jLUI8JfX7g8hOKU8HHJmYERZQrbRcqnJGYu3FfMPcQI5309tUh8QIiPRVAL2RYRQpcjsUONmQER+hxPYw==", "signatures": [{"sig": "MEYCIQD3wUeAz05S3IX+wYjcgSGaQAkoUjNo+73DYVNNPonwAAIhAM0+RlZ9n/bvLqZO5l7vqTNZjbM2PnAgVQvGfAfgv1x6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53675}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-toggle": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-roving-focus": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f1c6ca3a29bad4d726c64df03c55f49df1bf3a76", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-8ivLzgOyBiWdUzhr6tAWaxvgrzfLiBYn8sVVWGxR9gtSfFs49/NZrQYzS5KAvAeSH9sxPUYlY+zkd40BDGB5GQ==", "signatures": [{"sig": "MEUCIEITGRaaHKRmyb6qDx6wq00xWKSmx6CNZUCM41+7+RhtAiEA8bXeWAC0x9Wam+VGhkTGIMo80JKWaZV3eecDTrgc48g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53703}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-toggle-group", "version": "1.1.0", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28714c4d1ff4961a8fd259b1feef58b4cac92f80", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-PpTJV68dZU2oqqgq75Uzto5o/XfOVgkrJ9rulVmfTKxWp3HfUjHE6CP/WLRR4AzPX9HWxw7vFow2me85Yu+Naw==", "signatures": [{"sig": "MEUCIQDc4DOVFVFdAQdO0/CjkO57gJuuo6M4MMx3hFiES/Wj3gIgXqwAaZDbQuAnaMbsjncI0Cwbw2RLotPLnnrAlod7wTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53635}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-toggle": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-roving-focus": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbb60736eece1848795bf9115e64acfcf2456f14", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-06I/TWUa7pQFJevbHRELWOOTiTRurwQf+mRpDW0AxqcNUaT2Uq4HC1y7tEKkJltxmf1rd+DwuthV8gYyeKrU9w==", "signatures": [{"sig": "MEQCIEPUUv9p3bX2OWuYvqRjyigitQZQgVvV8Zep9ri7QFonAiAvZiRH5/QqfEOTXQkirsZVpW9VKVDHHm/suY2rHvn6fA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-toggle": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-roving-focus": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3540a3665ca19143504e8436bceb8bb7153e2d49", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-exHGFvw/hlWgJy7Jgr24z2eKL0rpkPOXFV+ycbnFvrBiSRJ8duHZE7iFruffHwk3EO5ne0bbRqyPkMKmSQqrAA==", "signatures": [{"sig": "MEYCIQCoGRmv2TbGm/s/trjFT3XozzlzACegPwN75tjWX6zDKQIhAIyjJIK94QGMRrZbA+VA2l1MZ4IJV4maV+O1cJZ/jrit", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-toggle": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-roving-focus": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d26bd33229a13fbc7c09964b0afe51f2bb086a49", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ogRwSjV5q+rL0TcrorHqbSOUq+dFAaZm3d+abSUDe+eFxO05t41cjkbY64uXSW8X/06pI1a74Tx1fCZdK2O/dQ==", "signatures": [{"sig": "MEYCIQDfvYBNUbVBFtexoKvqCmWTVqYRvo0Z+DpqNssEuI/c3wIhAPo0Y+kyzbHTXsYwGWNzfO3VoWz82SeNDD8c2Y4pCreI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-toggle-group", "version": "1.1.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81fc65212758f3a4c9d505d38c0053f463c2e247", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-OgDLZEA30Ylyz8YSXvnGqIHtERqnUt1KUYTKdw/y8u7Ci6zGiJfXc02jahmcSNK3YcErqioj/9flWC9S1ihfwg==", "signatures": [{"sig": "MEYCIQCTFGOO5DdVoKg7lxsXTBqbHb4jswYY8noxYB6E+rIZTgIhAMTnveeEyqjsrRcwJ6fkctSLdg559W8WxyRdTT7hvUoN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53085}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-toggle-group", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-toggle": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2df1aa7c0ad50285600421f8f8e3492f661b3b46", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-bFSsqUTOtrmmBjq9Ip9CDs5ZEnNXz3rtm92RNkMq11FUK7LtCD2IRRFJS7br+wHgIHCttXv97fIEk3OVIKtPdA==", "signatures": [{"sig": "MEUCIQCfxn2Fsm0efqEt0gTuBfGtTf2jFgJJAosglHlyagX2mAIgDSEdweMttJN7Pw1ZAgELVD5N66ogvZxbiGnC66A2Ch8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53112}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-toggle-group", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-toggle": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a316ff9d4f337d4e2f90f0c96aa66d99c0b1c4b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-7K4s0d5tu9YM24cgJ4clVZshNP5Ju9dnhcSB2vUP+5kBRG+HnSAceOX+kiDvKx+rkGiH/qKO5I/VbKGjyvk2TA==", "signatures": [{"sig": "MEQCIFUFrUyH0fDfApx0pucYaifuOgEr83YyE85vdGAy4+jSAiAl2s/GkQqDgQ/hjXjI9haRC3qyNs+1wK8CLAQ22+GFTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53112}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-toggle-group", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-toggle": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "0.0.0-20250116193558", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e27633101188bef24baa6249209483932107283", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-CmiqQohj0j6Mb1ykatHat3tFWRKkG+sTga8Y7QdR7N39RqAZwyDeGGtattzPwtlbe/MnY4aYEYUqrZC7vrMbMA==", "signatures": [{"sig": "MEYCIQDZRTrq6EvGDifVszqUqjgiPOCO7qXni4clIzVITSg1UgIhAIaeR4mWBa/29+dp6OlslYOJ4HsFmebQ0NUWG9N33Smh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53370}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-toggle-group", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-toggle": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "0.0.0-20250116194335", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3ca2db18517cea5980a0905cf5e4933e2773bbec", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-Zr9NfsUtCaFY6bul/IjfPSyS9K+hG593NCcyD5wGr/lFF5x0X233Sa5ZesmvaWFlx1JHbJUaVNzVR8kkg1pklA==", "signatures": [{"sig": "MEYCIQDx2GQCuQUB/TzOhy7vfUCEZ37ubzgIZK4Dne8npHlYUgIhAMk2dZGJK+gtMY1WyMHLy128kOPrB+U7UPxkU9ofz1hp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53370}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-roving-focus": "1.1.2-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "65a898ced77a0b6db73e6bb8703198df3a315b6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-g+GS0U4QU25CQcEaEGLFTV8AbK+3Jgi+Nbqel6ADl99cPxz8zPJ3pIOtw14JdpX+mq+6A5S/cLC4o5zQUutVuQ==", "signatures": [{"sig": "MEUCICbkS7MVThKrwNTnn7gWU2bbTv1cpl1HJLZ1Ovp4WZ8kAiEA4Al8PL9m78pS3kxoSVZImBXevdeBRzGDmB6SoKHBrt4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53346}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-roving-focus": "1.1.2-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fb2638d4c080531f9fe6aab331edf6ee30876d79", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-8AVrjgAFFs0dr0S6XMRmZXTA1NK/DecGV2gSPtTh0xPoF7IjzCRovGvJTyzeOXR7UEDvKeUvvBjcnAFF1X78tw==", "signatures": [{"sig": "MEQCIDsTJB0sDkQoEY17aJOqQnFLIOFMyRtEhwyvu9KblonOAiBR0jeSERJsKVFgn4s0y4Ky/dYv+j6hGdIGp8ZNa5+O0w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53346}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-roving-focus": "1.1.2-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e58ff19d312361a037584b87772a2bf7cdf44e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-cZQEzl3l9QBrU+Kq+4F7kainUAdjrj36MT+aIaAk04Yv0yXv5SgtDRarmnekwEij5i7T46ja8U7RE6wpcyrAVQ==", "signatures": [{"sig": "MEQCICpJtgTcv+aY9YZuAW9oRpiyUvLLCeQjZ5w8UNTjp5YwAiBuBa8RGKWDr1j1TJEgwHXBenSkO0v6mTsIR2p8S6i5dg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53450}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-roving-focus": "1.1.2-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54e0928362d97efef0d76dd26536627d2238e01c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-NFonseViEZauz4NHJiOwRP8d8NG3ukUVSTFeN17C5WnPFQUWacjAFz9qEAgafNnr5tNIgqhsU/YQe0gWZEnfNQ==", "signatures": [{"sig": "MEUCIQDU8ZxgKtP7TEBvAyqsPM92NTVJs3/qH5ay3KcnXaOWeAIgOlgtwZX1/DSbWPE0hQoKjkJ1J+rulbQqy1HOp2EUUtw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53456}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-toggle-group", "version": "1.1.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-toggle": "1.1.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-roving-focus": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4063a06124a008a206f5678018612e3ddd5923c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-JBm6s6aVG/nwuY5eadhU2zDi/IwYS0sDM5ZWb4nymv/hn3hZdkw+gENn0LP4iY1yCd7+bgJaCwueMYJIU3vk4A==", "signatures": [{"sig": "MEUCIB3RfKZJ4LAcawpPdgBTDwrW+Wq4n4YMtUhGq2gztQ5bAiEAqyJ2JOGD/qL471PomCUZIm/SncaNhEupo3S4lhYcQUY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53408}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-toggle": "1.1.3-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-roving-focus": "1.1.3-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "12b274a25d6e418ef2ba3ada0028eb3a18bf25bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Fy7tG/O0io2mhdlPOPsmpUT8O2iz89h/LHT4q2VUoDY1j/NxXLolWtC3Gqzv+RcSRD9tWCgMkvO3cidFw0Dexw==", "signatures": [{"sig": "MEYCIQDw8wNoophLOnh4DjPpnVI6r1YNQdTJR8fGSjyFrdbbmQIhAM+NQL588/FXOXd8WtxGSzyTxmKTiWMEgqsT/tbQJXyx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-toggle": "1.1.3-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-roving-focus": "1.1.3-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c29163a796a8a2e0330e37c41155d25ea4031ec1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-QnDyJYJw3AHy/PFfFx3TiHVwj1aq1Z2UO/CBGGWxqS8nWMYUIJgThipYHK8l7i1/1cPXed+RV9lTRvj5eKxfhg==", "signatures": [{"sig": "MEUCIC3b86kxR30FInys71F9KWuYSuupdryqqrcW0qKmPRxWAiEAzMKCcONqbwcRjeWNV/x6VXkuUNYu8rFRrcFh4fSMYsc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-toggle": "1.1.3-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-roving-focus": "1.1.3-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bf2fc7f78f429c9ab9607ceb24bbe7925984339a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-9mg99M/txm5i+ctVaNyWP/eAaJWn3SL97tsYu/r4TLqRnzfkXayoz6nsRqHdbrNanLscK8Vzym7GDiAdN76meQ==", "signatures": [{"sig": "MEYCIQCo47SFtMU7nBzDDo3dglju3qqFGQgIO+TK5NKd5EVekgIhANMxKGICIPZPJ/cou8ik/qEqR306wBpGzm8pMpFD69Oq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-toggle": "1.1.3-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-roving-focus": "1.1.3-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b1d19923471fa66a37a3217edfc233ab1acc131", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-c9HR78Ug0o7vUfjfNqXjgWFJJtB9jSsw4MXV8ovHtazpAjncQ7Gb6JZaUQVPD187lmdyHOfU1NBlBPDE7CcUgw==", "signatures": [{"sig": "MEUCIQDOwTCRYB6YCLnvXJrkBX+fWjQvMALC2aISyyId21PB1gIgXSyDPTVWELG4xizNq/cYeVhDbkVWajv8/CtaPmEtfp4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-toggle": "1.1.3-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-roving-focus": "1.1.3-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0cd741e04c9afdd5071efcc68b2bb219a2643b28", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ALLKURYnzBUh6RmZPQWgxlOu1i9krFRaYMyufE6I77nABC00nlyAA5AuhgpjAzBMHjpK65aamNNR963et6rpHw==", "signatures": [{"sig": "MEUCIQDeP6L0A19bjn5u9TwVMt7l62S2hoylD+DGanC70/B0UwIgCDnBREH/dY1sHkXGVuL2rUnGhCYQD3Hvs5nvj8ZZ9eE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-toggle": "1.1.3-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-roving-focus": "1.1.3-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "626fa75d79588a626ea8cddbe9365606a46bcadb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-sH36xtpM7ePm+OuQqfBid/IKdCtocjIGrcaccWRDLzgGWtt/MJxlUHhAXpzoxMdxM5GpCp9KOXeXIe2muXCdaQ==", "signatures": [{"sig": "MEUCIQDZ3zcT4CtT82Lk6Kxky8GBbmHGwXc+pG+S1eSUOecbPwIgTfnFpmJdrKTcg/V8JoInIAmZlwkOO+S9ZtxATFf+fII=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.7": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-toggle": "1.1.3-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-roving-focus": "1.1.3-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "79f4dee1182afb8a34a6d0e940901b1cc9483f48", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-LOTCYn35Rcv0a3zTnRl1dTYSEkEkZ/RKueuY2IlTMY5JJeb+u0OshHJSP3BxEnkeMKqLbMGKv9JEJAFYJRRTPg==", "signatures": [{"sig": "MEYCIQCiQNelhCIXs1k2vPz8AHjONrQuKLrJnKCbO92pMy50eQIhAOW5DDAkYMDgIH+iQnJaW3LRRTt4RQaB0D1ja/HBHEow", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.8": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-toggle": "1.1.3-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-roving-focus": "1.1.3-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6c92192c0753c2960886fa529df1a370e9fdd608", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-oTAwiF5znOjzC5lF8RINsX8G4it0GDKlYTTJ2hfejKpaz9Ie+aYCzGYOoA2dC8cy8pXmjZDI0V0BmG4Zb5YvGQ==", "signatures": [{"sig": "MEYCIQD0a5rzEcIGSpv8PKu8UeH7NVbW01wx9PWXzMz70HE2PQIhAJpgT5sKZ7KYOEVUD9tHk4tNf5/KxosGCaBvqn8CTzhI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53873}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.9": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-toggle": "1.1.3-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-roving-focus": "1.1.3-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a65bc500aa1eadadce8682b8536866b8a3c96d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-nQ0jLTLNKh+5p1M8NpMqtKMCcLTMdJ1CHREOs4b1tl5k68rAghm0rwvcjLPq/ScNnpcKmySKBs8fstL0wOhZiw==", "signatures": [{"sig": "MEUCIQDqARuvKgCi8QhqTbkw9sRq6ySKgZclHGf3ngkw5sF09wIgR3O5AkaE/AtHdQPTvq0gy+ILPtHn1HKITackBSqNEcM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53873}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-toggle-group", "version": "1.1.3", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.3", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.3", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "39ec34b5c67416bf285eac19ef6898532a0c56c6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-khTzdGIxy8WurYUEUrapvj5aOev/tUA8TDEFi1D0Dn3yX+KR5AqjX0b7E5sL9ngRRpxDN2RRJdn5siasu5jtcg==", "signatures": [{"sig": "MEUCIHSDjQDKqZ1dWkDL4GHF/lwJgr6AN6lIMVOXI1HBbDQ3AiEAgK1XKUNIfEoParKu+9ua/+GDWPrXY5KNHS9xa2f1YqQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 53805}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744259191780": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744259191780", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744259191780", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259191780", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7866b7e72129895f9787f164f09af1a1395ae5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-BS0H8NLRC8/Qe2A3iLg1XPRMq3I26jfC0M7Xg0PQ+x/pnrJnEitioMXEE/yVLlSWKTMwL97+E6lXx2WWs/37vg==", "signatures": [{"sig": "MEUCIG0cih59roleqRzni0ClhJTwb/EdHuyPKKi64AQ9iMsFAiEAuoVStsX248gyVPfJlda7BoajF+6DwDCey4LDUSm1MIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744259481941": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744259481941", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744259481941", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259481941", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "288fa8d0b9458df0edbb37c8aa797417d36bd4db", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-ivZCYLpG7QorbKuLWuZd+Vqvcrf9wgE754hk2P1y5r06lDSwOvK5bqGEPEyEzfWNJ+XEO6kbRQlv/5xiX/rWgg==", "signatures": [{"sig": "MEUCIQChvzrt2QaRIRyqscJuKwifp8+SZD7g6nw3cg4o68RJbwIgUm4JY/EpUALZIgcUjKI9PO/ETHxguwJB5juk/EF0Si8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744311029001": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744311029001", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744311029001", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-roving-focus": "1.1.4-rc.1744311029001", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2bd430e8b18825b9c7c7a2bc70d7f6e8e7e05062", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-LQxOiHbIjDkATFdLZyyeU0t6rP2bE4CxjzlOT1vmlVpI+4NEkkVEgj4gRSM8lakG926hi7a4YWEMYkQ7CSjQwg==", "signatures": [{"sig": "MEUCIQCLqp1FI3rriE2/FLp2kPfKDtLJiTNibQPScV/PFF2TRQIge7Fi2GXX3Twy0quJKIyFRqw9TiXKgVADL9t/kEsHoiU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744416976900": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744416976900", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744416976900", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-roving-focus": "1.1.4-rc.1744416976900", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fd67f676388df499f34ab06eafa0f6766ae31e54", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-JB7LnFnSkg2oA4OWunkKmxCvhKVe0NRDOIteVIK1fSyfzSfGIhUQqT4Czx1/gb536+C1ymoVHoQuBgonghf/Ng==", "signatures": [{"sig": "MEQCIEydfrnWdWwdcYZ+BWpmEaVfCmqFPi3zB/IPwj8W0rdlAiBndMe8obLdG2Kq/8frr8bjYchiuLGp4+GrM2FN4HlmFQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744502104733": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744502104733", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744502104733", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-roving-focus": "1.1.4-rc.1744502104733", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "89d5b4b35194ac5f93d0a2ebb296d284ca706663", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-CNE9RejYmMkXODsBGyBFsrkhNZXXjYaBH4MmtvzsJ0ApHtAgjlrbeIbrI48iy3OPxJ8l9Z75WK1wvd1LmNEyTA==", "signatures": [{"sig": "MEUCIQCRrjDs+InwoP4rAaqoTkC6E/PGtRhZ65N0e1SIvuBhQwIgWKHTC1PknnwIcZtlHGCrJsb2cTB6OCwsFBoxi5AuWas=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744518250005": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744518250005", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744518250005", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-roving-focus": "1.1.4-rc.1744518250005", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43db69f852e49c86f6f2411bc959d72c6f9ba7cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-nR1s6h05yv02FZQF1RBhja70mvdoFuZ7SCNG5D/kKV7+3aP01noq86AbkM2d8qyCGM+Q7rCak9G8WHATWYOD7A==", "signatures": [{"sig": "MEQCIDhcPJy2nNVs8x8ZJ7xIS8V0v4SuA7kQMSwyFWIHcUu8AiAMxTEoy4EYIaCUOuZzQDMsZ7MVjPv8ZODSN2dFE7lU1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744519235198": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744519235198", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744519235198", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-roving-focus": "1.1.4-rc.1744519235198", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1440fc9cda74a7a4d24730cee905cc36dbbe7c19", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-ZotVXXEP3mrNb4ccufpLDFm1pYNzWI7b22PVjRwn8LGNEZrnum6JfLorMhGin5rRQ3HRlxkUnLS8DrqKVjQ7Rw==", "signatures": [{"sig": "MEQCICd9LHc6bepufiMXmlbDQ9mmRX703KxDG8ylfvrFOeqKAiBR0zLDN4/84tCyyaFHjAi8i4jFKauLi9JRvt6mwWQhCg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744574857111": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744574857111", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744574857111", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-roving-focus": "1.1.4-rc.1744574857111", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "970c652dbf5da7b0e84131ff4294b917fec2f82f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-erYyhjx9Q/NfKBUUiGmZXQskqTIZKgPzTnHyUDBIbW8G4hGSLxL8aPPqlOxbSSuPgGnUUWTyhUpbaycd6N2R/g==", "signatures": [{"sig": "MEUCIQDtCJOrDFLyCN/B46Nn+A3uD8r3BnEIBhjI8/SdB65JHwIgDngz79o7AZo3mO/4LGmK9w3F046wiC29kD4mxfJ+TeY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744660991666": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744660991666", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744660991666", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-roving-focus": "1.1.4-rc.1744660991666", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b130ca32ec47bd837ed2d758d9b343048f6e4be5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-P/s+ItK7pAd3cZ4Fiu+WP0NEpSARchG/IgUcG8fGloB+9nxHHh6BMAZw1DLHKrOuQlMEMxwYAR5rTfmVE6+uLg==", "signatures": [{"sig": "MEUCIF76d1taquhfehmMGDAMn1TI++udFpO0UuUsQ4kxbnhuAiEAi7BJeqiE21lvKNltIjXWxbHG0U2idKFLrm5UdQTvxuY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54412}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744661316162": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744661316162", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744661316162", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-roving-focus": "1.1.4-rc.1744661316162", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1224bab22ae3659bd529f8ede5a974e5553cce1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-K5yOliMFk1dJbDKSQ/GA9fT9D9P7uKiD6l9IsKaLOfwIBpno4s1nGz58HDZz3P1zqkvkCLVTG8BqemKJ9zcl7g==", "signatures": [{"sig": "MEUCIGokgZ57nOm5ceT0+evxLQDx5dQLgclU/i0ecswBVnX0AiEAxmoYRchzP68gU9mC5IGVocZSGKjM3YKE62IUoaJwCnc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744830756566": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744830756566", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744830756566", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-roving-focus": "1.1.4-rc.1744830756566", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fcfad0cef2698efca03eb7d706687c286e88293a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-jOw9q3sjIMt8vRIYk1Z4SSE7aTWhqLcxAO9sPufFDtDUPMSsCqtDjXp5ILz14SnpfYEafx9cywrf6CqvyKiKEg==", "signatures": [{"sig": "MEQCIAFf3o1DzUiOT5437wpQcFalxr5pkbKYiLzgpy+jODJkAiA34h4hCqTBYilcGnVinzf3BIpNBQm5E41S3LlJvkQrYQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744831331200": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744831331200", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744831331200", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-roving-focus": "1.1.4-rc.1744831331200", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3fe55c5c70b4220bb312ebe2e73f314b500bee6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-BX9E3ebGdt9KFFWHjWJZduTixOd2IK/Y9VNtiA0X5UlcjYLkzK0T0Qlxg0r3jqKReJrh8D/adOcdCCWTvR6ZTQ==", "signatures": [{"sig": "MEUCIQCwBEuLdLi/wEonBDIUmSCO012dWk37+f/YGRqpm1ToqgIgcveXE/s7z+VJrSBeB2tCXU5RaWelXr/kLNnq9mZ2cnU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744836032308": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744836032308", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744836032308", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-roving-focus": "1.1.4-rc.1744836032308", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "29c217b1f7d8d2ae982fdb328870ae42f341c8a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-ix5+nSUZdHURMHymiy309iHsCXhH0RU1YHvVOfF3IalNg+MgT0QSDtmxzlyrrEh8cjnDfa1r0RzfFGXP6Z7/iA==", "signatures": [{"sig": "MEYCIQCWQtFKsGe3If2YfNTeP2QmgdVoJpH3VfwYTSLzv2o/hgIhANgw0QnLLxRhouUcJz457BXVIAJ4YKNNq+gSAgJtbVkj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744897529216": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744897529216", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744897529216", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-roving-focus": "1.1.4-rc.1744897529216", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b58e30e4813ab1d1a165000b763cce34f34eb332", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-xznnab8dG/PjGWH0lTinatN9do4IOgfwfAVkonBoDq98YqkUChcrlBWOovAzF/C6uGdU+bdpbRltcLSWpDkkNg==", "signatures": [{"sig": "MEQCIDT0QCoyptlw1+utw1JEn42uTITq3fSyaIvAGP7RsiKYAiBfhj9XGn1FRQfI2PJKwK79uFCviGziuKNt59m/jvdL4g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744898528774": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744898528774", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744898528774", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-roving-focus": "1.1.4-rc.1744898528774", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5093feabd33c6eedace430a3992f1d3d60eaa709", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-gHTWd1BFVpn6s304ApGYyxoj9ZTZjrjBBm5u5NSRxvBpTP40Qc8oJu7BtkFs88hDdro8xYAr/0GRzO76u5Wq9g==", "signatures": [{"sig": "MEUCIQC5tNfqPCr/T6aqyH7C3oZ+FVRz9CX/DxPq1Gp9zweArAIga+SvTnKeR89geSYDSaIwAm5TEUNeUyI+CQtgTUu/4X0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744905634543": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744905634543", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744905634543", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-roving-focus": "1.1.4-rc.1744905634543", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9190ffe83499eb60605f5d87037a71ece9b9356a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-jKco8drhHUKipbR9CM4hrNI36kwsJgTSdXjj2Y8rFfhyWRja8qvP3ZaMQDmTE8ebfRO8POWZ02+LQtp3fA3zww==", "signatures": [{"sig": "MEQCICmvK5b4RWq0Vm3OMCPCd4pwpIemd8smAWoDo6H8RmouAiBH6lqNsZ4QFnDyv8B5ixuNf0xVAmOledh9g3pidAwdGQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744910682821": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4-rc.1744910682821", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4-rc.1744910682821", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-roving-focus": "1.1.4-rc.1744910682821", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "197d4c29a8458ad49833356d012567bc5b782ba0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-QP5BEcNaLclZZe+J46ZHdTrlNTlW70eIgJZ7tnuSOsXHjCMvfvcDRGZK9GvS0Gi7LVBxc/YjTvd58Yr9NgMBHQ==", "signatures": [{"sig": "MEUCIDv9w8EeVSPzgM6AJ/CXNUaeeeu8xvIsmEu446z0iFhxAiEAs6WgtULYDnYmOgHhUMGnn+f+U/8bMuheccBD7bhxeO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54989}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-toggle-group", "version": "1.1.4", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.4", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.4", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fbf851bfc09d274d922bad3a52edeb6a95ed84e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.4.tgz", "fileCount": 9, "integrity": "sha512-GQDwZbUkMgrgC/eNUWhBYDASYLuAn8iPmQ0ZAuCOVgbxO4Cl1C2BbpR56X+krnJY1zti4Yd19MQsQrJLkIGajQ==", "signatures": [{"sig": "MEQCIFWoraxh00zbNs3/FeHmnahJGZ/P36RgFfAjNVqR6S7uAiBMQjWlX+nfH0lRLARKi9OLngiTxtwTY4Kj8OXewGRUJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744998730501": {"name": "@radix-ui/react-toggle-group", "version": "1.1.5-rc.1744998730501", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.5-rc.1744998730501", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998730501", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "787f90645b8a6d538d663da3ab4505efef178810", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.5-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-qGmUHFNDhQ9gV5nrz+txd/rVFzNgO1AjOPDK6X/uwmWSnMXd2HEN7aqzc8ieATGKX6BcmXbZBZV/Pssn4TBe7w==", "signatures": [{"sig": "MEQCIEkKbuqP072kN+yrXDqzvHQJHWix8Fv8TkG+yMNv0uDOAiBqHSBDOPjltb3UsvFQdzN7WzycIOSflmH+iuZZbijt3Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744998943107": {"name": "@radix-ui/react-toggle-group", "version": "1.1.5-rc.1744998943107", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.5-rc.1744998943107", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998943107", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dd189e2da80bbfd95cbc68209bf54b7a7bc15411", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.5-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-oUd+U2l3sSuElXOFxVk6yQHMDidIvWrFRkM/MpDB8JLy5NVJEyPIIcXmhdKeq5wuC5YMiwTDr/77n+vph7dSNg==", "signatures": [{"sig": "MEUCIC3DzsafVY+MTinCxLDzMnxJQ4KEyLdP+8e/to3Fo+KjAiEAppgsHzXJfFTk5zhm0IZmnBbglgswZPnUxw/biWKQVS4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744999865452": {"name": "@radix-ui/react-toggle-group", "version": "1.1.5-rc.1744999865452", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.5-rc.1744999865452", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744999865452", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "66609c5bdfa786a795704d4843fa32de604951d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.5-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-MgTO7h3nBV9tHIQl80ZUKUMqS9Yl1LgoOUmACt4Tj52QjLypjDWVflQovqXBOOk/SvMyXkBMN7zEWja1x38u6w==", "signatures": [{"sig": "MEQCIEdAsX356tu8UUtpuPephI79TLHpDQaDhvtgoNje/JHQAiBLEZ8tvdCx+XtJshDwSrxLqdfNK1ooBPZERB++HF5gEg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-toggle-group", "version": "1.1.5", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.5", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f5cf162be5a93d069d4a954195c768bbebac7d2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-Jks7EvKescOA3F59rCm3YpDdUMfQKVLWeYIjq3Blhmkex7OZIYcjVBV4+V13T/i04YdwTXeE5hGxyY9fxUKQNA==", "signatures": [{"sig": "MEQCIB6d/xODR6Na7qRGXHiufxbW8yh+6cwCF+H4+//ZsLsCAiAGa6R5yyrcNG7+u3MEuQTeqDRv5JRTL2GL7hp3ftzQ2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1745001912396": {"name": "@radix-ui/react-toggle-group", "version": "1.1.6-rc.1745001912396", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.6-rc.1745001912396", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6-rc.1745001912396", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a818d25f8c76ab1ba3480c080be8f89a86490c7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.6-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-4ZnoJHrBXQk7xBruhjuxNvHEjPf7B37AM2iWGctItcDd1v6RQC+9+OlU92rDENpZ/nydFpA/YntSIENV8hxMuw==", "signatures": [{"sig": "MEUCICkPy9veAa92J9oJFavnuDsc0RaOf6W/rzQFnI5wjflhAiEA2mnfqes/C3FV3dEq3h1cOiBAn0o/UoC8NiG3jahTOb8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1745002236885": {"name": "@radix-ui/react-toggle-group", "version": "1.1.6-rc.1745002236885", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.6-rc.1745002236885", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6-rc.1745002236885", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "29bdfa52a2fa5d34547039d1bb82ce2d2d1b4381", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.6-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-2r8/bY2ds+7u0SNTn1sCXtDm3b1WEqYBPBYSmLyGiJBk4F0kTATVGnwXuUC6tRI66X7ULj458ladR6Gon6fEcQ==", "signatures": [{"sig": "MEYCIQCa5hGQkg5zWvW0XaI3TAq2juFg13OKIHB9KtbZdqUmyAIhAOh/60ZrhQCzsGoUhyH6CuOvw3LnlxjJj9C62VYuASH3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-toggle-group", "version": "1.1.6", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.6", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a6f08a799b970bc535147e307a03d0d23e0d9403", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-XOBq9VqC+mIn5hzjGdJLhQbvQeiOpV5ExNE6qMQQPvFsCT44QUcxFzYytTWVoyWg9XKfgrleKmTeEyu6aoTPhg==", "signatures": [{"sig": "MEQCIHF4HCRI3OqYimr9lxzS3xK9KOs0OyKs3EMegUWXulwmAiAGGsaQkGYNJxyMZ6aMx9dz6V/Y7KEzOzSG91+xkmn9IA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745092579314": {"name": "@radix-ui/react-toggle-group", "version": "1.1.7-rc.1745092579314", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.6", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7-rc.1745092579314", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "72aaf8a1cc05e0e9958decdc4831229a213654dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.7-rc.1745092579314.tgz", "fileCount": 9, "integrity": "sha512-NY8k6G7tChENqQVyvmm1qrSZ5qeY+aY4y+kgFe1ZdCNwW5XFeLOmFZ4/j4goh9TB+kQAuQrzz/4mqlYMhrHBYQ==", "signatures": [{"sig": "MEUCID0duofIrc7Zu1z/0SjWxN7etAdn3UJXfWHZxmNcri5rAiEAqUu9scethZ4PEvrLoTc/dNXsCqDkIpiq2aD9CGGEpRU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54938}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-toggle-group", "version": "1.1.7", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.6", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0eaca9e4f8fbf2536f01e33a6211eac4d6cfb83e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-GRaPJhxrRSOqAcmcX3MwRL/SZACkoYdmoY9/sg7Bd5DhBYsB2t4co0NxTvVW8H7jUmieQDQwRtUlZ5Ta8UbgJA==", "signatures": [{"sig": "MEQCIG7+obsE3xhBWC1SxGEb8CbFY4fr3hn8bIWLrBuBq0SeAiBA4TvlDCjxN8yGkE2qa9/ejijcBmhW6ow6e2olOeO+gg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745345395380": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1745345395380", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1745345395380", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-roving-focus": "1.1.8-rc.1745345395380", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ca2f0b38f35989801ceaae976f843f62e3979d3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-G6qV5vWRVvTwEG71FZfyQEes0/HAA77xHGe3fbTEDz/nb9UFlWNOCm0tpGW4+zARv+qldyroJXk7CGPtm2FwZQ==", "signatures": [{"sig": "MEQCIDmlF9WnKiJd+syzwAWoisPiZ1rPdT9X5fdbcDMMTLq2AiAHDtd8eWDB6M8rkV18x096lKsYVejgJz6pa0VAnWoI+A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745439717073": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1745439717073", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1745439717073", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-roving-focus": "1.1.8-rc.1745439717073", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bbf83e34984731b69fbbfa7a2f74d615afda4ad3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-w/6ACWvkeNMPKFgrZi2i8igqGC7e+qo37E6amD6mO4zPLGHYwo3Ii/xio0n51Ki7F+aoUyacRDbONloeSE7FpQ==", "signatures": [{"sig": "MEUCICY90NYJWqsyaYoe+dfUiTwTK17EEzqbb3DniUUgZS7MAiEAij6/ZxB0zfeT6WndJpERbx0ZKDgWvq6Gb/dGUZytx04=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745972185559": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1745972185559", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1745972185559", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-roving-focus": "1.1.8-rc.1745972185559", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0bae3d09b29ffab9d20987bf11b7ac0be4a8b9cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-VAD38jr4CxD4rLVhoPSvHsYG7pO9pMIdPcVHpproFUBIr24OiM9TDp4qWRwXLAnYQ+HVuShEb0UwpXN598JhYg==", "signatures": [{"sig": "MEQCIF2UUMVCW+M4X338cu+rN1AcTTfFE6WkD4niPg/ARQ/qAiBuwjhoxMMBGpI0D38ExIPP3BRioZr4xgsRX/g0Y/wgHw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746044551800": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1746044551800", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1746044551800", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-roving-focus": "1.1.8-rc.1746044551800", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "830c1bd11db3d1463446934d388bc3bc7f3c219a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-/O4eeS1zFjEgmUm6zOj2L8JEjDLGDJi3rrNQfDq3RiePx3F46hKYOOyW7sCBvcL3/Ke8KFAArgu5p6uSPtdFag==", "signatures": [{"sig": "MEUCIQC2670sE9WdtsJUxuK9FN6keFAXh3/oL6Kz80LSIks0owIgZutuU6DcJq+nH2uCqJorYXYn8aNeEepVSPR+CGitBP4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746053194630": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1746053194630", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1746053194630", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-roving-focus": "1.1.8-rc.1746053194630", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c3af74ad327d0ea0c89edd2bb33aad1682cf73eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-VLZRddpOBTsqSVYy6TH2xxcKHK0Kg6p5Nt8ZOxtQ/ZtIYSKKexE6ZdwS2r0uqDG1/TC7ctbu+a11ltC8TcGwtQ==", "signatures": [{"sig": "MEQCIBPI5D/RPGoYmROT9boUs1Oijt98Ta/ZDcd2sbmZt8WGAiAvaK2mJGQ61LUbOxgmtnAAVOmlSf78LRPwd8mGugQ/OA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746075822931": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1746075822931", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1746075822931", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-roving-focus": "1.1.8-rc.1746075822931", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5f975bcd65a381b72bf745096fd7079211467d5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-k+FrUfBlvqwHynV8WMEygwgkexJ+eAb/3lf8q2gyWHvCDSd2fIwWQTJxeHdtBef/Z4mH5+y0YOUGdASyD1Vtpg==", "signatures": [{"sig": "MEUCIH3CWLNtEN/0Nv1gghH6cJQ6w25cGjhGf1Zx7EetbVUhAiEA4/yDqm9T2qsnk2ILiMMK/R7u2EE2DHOeFRRRhYIPMTc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746466567086": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8-rc.1746466567086", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7-rc.1746466567086", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-roving-focus": "1.1.8-rc.1746466567086", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c8dd46658b0b29c8334cf59ccdb50b389215517e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-KH/G97SUgN1Vt01s+9gHD1d6dek8B9dYBRBSicc+9qUb1McI6FOddGKWIPMP54fFvEoQX1qzw7f2Fw/owF9aSg==", "signatures": [{"sig": "MEUCIEBcWYy5NuxvRqzmQZgXCDUqYiDZQQSMqgBu73OWQrAHAiEAgJdR4q71t1I8SwSXQeLH4p0q/p2+jfN4jmvCX1X8GCg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54972}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-toggle-group", "version": "1.1.8", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.7", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-roving-focus": "1.1.8", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "876dc1e803c91812b337335e083ea3da8dce4b8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-SP+ENtgp6NrnYqZfyKutIoO+KjZRv7M/9ONPk+u8Y0rP2XTY/W+PDg9ig+drz9zk7wnYNLJ8HOF6G/BLpnDUXg==", "signatures": [{"sig": "MEYCIQCKYk5bix6ibEiFV/+Ia4goD0E0cjB7wB/TAzQXjKwojAIhAIlFaf/yKGlTfvCvRwvn75tdKlDILzkunbpKdY+vDWh/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-toggle-group", "version": "1.1.9", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-toggle": "1.1.8", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-roving-focus": "1.1.9", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "92d4fdbbb08bf5d883ec0e56fde34c4fb0c55e10", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.9.tgz", "fileCount": 9, "integrity": "sha512-HJ6gXdYVN38q/5KDdCcd+JTuXUyFZBMJbwXaU/82/Gi+V2ps6KpiZ2sQecAeZCV80POGRfkUBdUIj6hIdF6/MQ==", "signatures": [{"sig": "MEQCIAC54FpvNoYeJqv7kr1FZ30eVSi5vxv9l/EFK4NhkGOGAiAcA3IQfXZmSWsZyqwHLI7w2BK745jqqhuXkxNTY6G0gQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 54904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746560904918": {"name": "@radix-ui/react-toggle-group", "version": "1.1.10-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-roving-focus": "1.1.10-rc.1746560904918", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-toggle": "1.1.9-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-XTiU75LRgqaJ9vZ+gAM96gIpbdiTuBw4BO3g6B7qmLfn1wP9Wvv5kiZCVNQ0NBg8tvU8Jc2T2O1feqRrmk9wVw==", "shasum": "c1b21ac0b43d17a413e49d11b65e65e481056f86", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.10-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 54982, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCID4yzwcBx3ICOPj7z/VCyj7jh29qPA0iv7YXx12H6y/qAiB10i7ws9/+A059ExZ1A0hkG529Yu2eJfp1zwJDcR1sJg=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:55.613Z", "cachedAt": 1747660587983}