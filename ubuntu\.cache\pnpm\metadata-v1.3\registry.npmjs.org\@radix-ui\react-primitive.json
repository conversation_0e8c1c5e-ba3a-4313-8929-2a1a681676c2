{"name": "@radix-ui/react-primitive", "dist-tags": {"next": "2.1.3-rc.1746560904918", "latest": "2.1.2"}, "versions": {"0.0.1": {"name": "@radix-ui/react-primitive", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e6f95c791153f3efd45b08348518743e6c89f937", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-tqzevhX023u7meAEgbjcCb3pGKdzXFCHBB3Vq5XBSAp4WILSka6elzT9871PqiKrIEdXboqp/N6Q1y63AB+b8A==", "signatures": [{"sig": "MEYCIQC44CxMfGE6Ns/FuKo0UJiTpmHrQ+OZA3YWVLjRKsxHkgIhANwiNan+jVjFRFDxj44WrG4kKnqf269zQ9+NklRcJcoP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvPCRA9TVsSAnZWagAA96IP/2Ms1vHKLyaIhTDRzaVa\n7IYisPrPnRhTUOLJqeETIgS8SjwKn70cJPGFFDgebc/eSEQIvVAEnP8TJkXY\nFm0j8Yv/d1RSnq0Gl6iNpUODJ1fsWhSXMgAh7ePS8Pt9u65k0qexw+h9r0ps\n+/TZtsD96a7TMZs3/uwvyZS6BuSYBHhsyqUjU2xvpeX5FfH2QBjCQvCp05bs\ncSNqdYxsO/3k+6L/8FwUt0gsFk7eoDteV/KDzYjHWJcuzQPS1IixFb4O74a1\nWPS+ZmBOaoZB+GJ8n2lShB3M33jc1mj+7ps+5cCq2GoQE6ZaK+wV8sVfDZTl\nlAtBnwhCZdri/KMI8OIjAvWv+S5746kKM1IpI+dXffRJhsgFM/JMl/+qE8Gs\n3MKmh4vJ365R+KLkNJ2Pir1QXXJkbLNKQNXt2LhXvWB2txn/VMQDrww4F7hF\n2XF9Wk2L70Hm//WzdtH4h0hQ0gGwRD3bH3QOw3IHvYwjMiYG0pdoenPI2lxH\n0e1PwtCvw2YixdJS4V+ym8r0ZF2C9KAJNkfTkisNd/v/Z2csKPlcKbhAW5G9\n5/2M01s0hikOzOh0A33OW+Pv20vwn8YJutsCic7xqqFTJk8j4IFBBhxcQIWr\nN0H5jErVnSrZdS1mBn7ylw2SvC5BJNWRqV9Ln6JaHbbk4JXTsWt9IP6BQO8b\nCYo6\r\n=/gam\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-primitive", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d92556fdca0220ab0eeb7d1a790cdacbd92a0d2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-o6YNKPzAainy6xPMoK/MEmI4lVlsjuLnIQYITy3SkheUUCKOulVv8S/XdHGJ7wHSKbrti9w350K1lwDR2eMjqA==", "signatures": [{"sig": "MEUCIEMwfQL6B+KkJNJzzNhDo3pwlA4jJml/tphsUK8Qe9dSAiEA+7+FIXW7/sSzlyTnFzmM/grxES8kyt3Uu2mj4ZNu37s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtUCRA9TVsSAnZWagAAh8MP/3vwzg+AfUUuql0aM0Dn\n1a8WEC7w9y5o9mu2QjCskRBeAwi0TIzMDoS4aytmVEdIcj1k8eR+XyXSB1dr\n0hGjBLawf2nMI2jdL6/tItEbCoATBdoRh7/WV+WE2gkoLhaL9n8ky9+fXBde\nOff1nCHVZjnfmwUbpKTybvn4unFeHapfpKTe/2NhQvkuXItaS72DxQ2YHCsX\n3op6nlDZWXKv4ry3bMmlkVY9WmII021wH0MET5G5yt5DaNB7Dqwaj66pYHSY\nW9x1mHQQeub5XsgmQCWTXryhqHg1JDJBRuHwzOWnS/g0NTrhJbyf8oU5ogXI\nhLnFL+C3DOZWFesvrybbn/DNHNnlL7PmK/byMfVsqM0sUcpkzLacxboy7d5u\nB82/UpcgCZtPuKKnwxM3P4XQKGPBUCPcKPTCV3CHzeT4G4L2jYN+qtk/Fw7I\n0uJBykT7QQvYeZLh9UNF8K3hPHTdaThYP/ZT5onxGJPevPtJhvKt/Q6zCHP/\n13EiiKnP4iGGpEO2FCBpMCTC76wQkRfMLdugQGJfKqvjPfTVaSzEDqWrkJTB\nRZCIBTmi9VfBbayyrscrQlljswtJsg8/9zAWkIIqrcXR/I3eK1igsKzze3sv\n2ehcEyIVH2+HRbRCLYpHRiwQxbTNLRuKVi4nOMhOvSh3iF5qmxKAxm1e6Xvh\noEAj\r\n=z52i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-primitive", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "208501ee0fceeb7595594529fab17d40cf224b09", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-pAn9ZgOayQJelWY7NThIrOy1qF18AX9vErnT0tDRaTK5YSsRHljTjSD14Mj2A0S/oYp2sGU/dT6n5ERO6iOLjg==", "signatures": [{"sig": "MEUCIQDba5LyNEwOXFWcP8wswcqval2zBJyWat3LQoYu9ym/qAIgN1mp7wXIR8DJSB5MHhM9m5yfJ2ez07cxx9Rzd2/0cVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAOCRA9TVsSAnZWagAA0g8QAIrc12j7yznT5a1tnoXS\nB60yeLVQAFtT6fB3usmsQ6STIbMT7SdrLLgLA76aJJMa3pXE7AD09K6PVgJs\n06HbAMJBkMdGSoyGxF2oXG1/++rDk8kjoeoKBYuC3GoSkc1U6wmqIy3ZvftA\nicmbCpXLV9voLaHk3nUSSULi8LFTD8hwGyIby/IYte2cgEG5n+c4S1bEzMJ1\n2e5FSNtK1MOAd1OsDd/LrhQifM1F5A932Nu2tcMsC9kz/6XjVtFbpW5hSI/C\n7Pm034CgbcWoZz8HBtFUTUySWib9e/KXxB/xDWLv2tEecxV9THCFcMxyiuWg\nHP8co22wBy9LnrjDD79p1du1wnRraBhJ2TJFttNsoMR2bj0XL0G4d94PS80U\nSKGUk3rIOoyzUbPwIPqjwYfGNwMPuNYFWprz2lpnGkXrjKRHq2lMGpE44ZMQ\nt2neeQVMpzLTbpIzRF0cIkyGZMJIr/KeT/23jdIZ80mjZPzHP9ImsQvtR7JH\nmMeVEoKF83zLoM7vKngGzosr2wKRr6JA1R5RsUfgILpy3FTT+p/t5GzHno8v\nV0eg3NyYRtof3H8te4JmhUKNlzCUPsLWaeGmn3jAK6uE2gn1uFXPYU4ZyGRM\n5AmX2/N8/EXBImAnTrHD0ycWitSZFERi10y/3Wl5eLkPdR16aZ6++cZEfXZ1\nxm09\r\n=BTw+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-primitive", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c4ff716e8f5a57358beb75a3d447fefcd72c7a1b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-FRiGr6WJRQG7/9i7bUHiXmyS9hLsBrAbMbsaqapYR8VPYlJyAavkNi5sXBXUoWa/VJdeZPI8jUKe/CRZ2ye5Lw==", "signatures": [{"sig": "MEYCIQDE4uwv7Kn+eNpu4tnsSsfBr4TG7dn67k6ZidLyvjY5VAIhAO0qDRSHCre6RxaM9M5GBPmZ+aTug0G9ARYruA8dMLxB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V5CRA9TVsSAnZWagAAKAAP/iqiNHyFjbmUydOsS8nQ\nQuMnEAMmntP5pZyTxLuBbT6I4hUZirPh9JyfS8ZM3U+uYjzTTq7cGLrBDQWh\nUvNz5ueSEETA1z2e0Gw/jnuFOf+vm/nChdiU01+ZG5cw+8nfpd7DckfwwQoz\n8wXk7T+6biyIgahL4EpJLjUTxMotr9cSsNSYJmwVUqRptEtRA7NEknK121V/\ndqgf/OY8mXKEWvaG1F9cjE4BgMM4m8ZV55NZ4WdxAOSXDnwG01jIxUJKodse\n2n4ykF8h5yRd0MBFMTyOLLP+ueOGp9MfH0LsoRYtLnK1OZth+Q8eoQDwhvvE\nf6UPY1dWyOGH5t0Uue6MWGQNTktNkUMrYkyVEZPlSFZdicq4TSRM4u7EDyEw\nW260Y0ro1jOnYpMmwe+am6DnDAU5hXm0I9Xig7b/HfWYFNXh0MhVvxYjnSz9\ncaVEA/IzhP7Ui8GZNH8g1ldfJfwGR3t5a90elOU0WX+PRIvVxLzUAeHNLb5Y\nvgthNN0qVukcb6NqFo9antHep7JgX3N6syPpqN31R3i0c+JR+tBGFtVuNoDY\ntsrS2HTg3e5+2RDy4QSSEjAu3c0d6ZNF94VlVg4+skyGtL4wTt4d52Y0VwBN\n6LFUD2gthDt7iU9lc8pSHQHcSzG3kjRhEQQa87g3Gnojt0kl5r4s5jm1MA7v\nlJyx\r\n=0SVY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-primitive", "version": "0.0.5", "dependencies": {"@radix-ui/react-polymorphic": "0.0.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2e56cf75ada56258458957ae930771e502f151b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-zZacpav02cmljSaBfuc/jTqJdsZmVSQsp9Eh03v6ksS/6sGmLga46cwztDzb2by/x2izodke5NdHrVOUNAMbeA==", "signatures": [{"sig": "MEQCIEJk99WhHWGhZPXGRlc/5PysxYAU9O1HviaQD8Z0AVJfAiAlsIyM4/TNU3EGqZmU4jOhinxGT/QFoTNMGkAZdIbCSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VRCRA9TVsSAnZWagAAAscP/0iW6rxI8+h0/PzajZBu\nGUD2zE/fE2K1+ugWEEp/IFdvC+1FW3TJmoZ+eBTrOlPXr6RskX7rS+ZyHDow\n92shpwWn0+wCFJuTyLTQt2lqnLh/lCwcODn4uhk+2P7BTSNpqibckqZXvxSZ\nB4Gf9mizoUquJH0rWQZNtqxwOyayCc4fzZyvc2U3ehxj2krCXeUrUh2+CNr6\nZUFKD8v4+nTg5LoQpkieATHwXBSSiwu0RDQqfHugq0MVqdpjXC1EWiACrx8U\nlizKN4IpVK1OEWWjGqa0h5FbpRJMjO2Wk3FJfyghOLVEMOOTtpGR2/KLPbLV\nupl+AJPm/bWOxlvyou+5++L1PKbynRksZRU0jzWGTZb0L8hkC3kwX9B8EMUQ\nyBERWFvGMoWb4VUiywytzA+nXSOVOezoxMRLlliN5TgXZ++WSCpmW263ZRed\nr/bg+Ha9e6BRUb7LSaH2aCCW7AU/5+OM+zJRoQGus3lG6plOlANUdDr0hqJW\nxqiMrFTPpO+VLM4+zYajIMWbuXJHezvX/XfxAAh5LkIcaKLNYSjPdWdPtBmR\n+V/HE0oe+CQKQNmfdfUYIN+89v35h+v2LfEq1HUNj3/uuyvbY2yO7HK9KmaM\n/1DrGh++ct7y9/tIQ+Zgp85VmTvzmU+wY2rYyM27cUHKtKW0EqX0VIH2Q4zj\nCTNE\r\n=vTrJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-primitive", "version": "0.0.6", "dependencies": {"@radix-ui/react-polymorphic": "0.0.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fa26d8fe60e7a0a66b35f8c485c603d3d948be83", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-Z3NuzZvTXF36y/HnUTuWUqbLstH41LD/kDGtn5Cz6lwzvy9Mc3rMHwnIXGjXZe8MqvZ9+FxGO4p0+2cpdc+oXg==", "signatures": [{"sig": "MEUCIGQtwcLF4w12/aE0snMPFy2gu9TNjD3kzJ7Wz7fBcEAlAiEA6Dr+sPNG5IGfigYOHwWFNHIHfg+gU1oqnfW+U98Zv74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmV5CRA9TVsSAnZWagAA7OcP/R6IM4hieJ31arXjKgYn\nAWpeJbSNC0mZDUJP7P5UCvN66K7jcR5ccvJEfIMzvUAqrvTtQ0z0JZzdJeBz\nL8ViWSBN7TIYiUMO7VHYyOF21u6GiXsgPh/CqBWNFWfRel0845HORUnFAE4k\nf0J620kAI/7jTZg04PeW/cMAuNqoLnBFS4OfkvYqC78SkUvohSrU3oXKKQxh\nFpzmnmjxroaSfwgY89uQ77gSrxqKlBA2wOy3+2cu3FqI/JTRlbVnB0qFctgz\ngexsUsa7kyn23FJtXOX+wO3AlqiajiTGvoZAPlt0HqoH1U56C9GW5fzJ9cGM\nQMN1qZMgcVqeYNaCggiNZReqsrESkUIcLpqFDPyvVigyKRemke+jtVq8Mxga\nzSh65/BCtcf+T2xTXm4cZ3EIfMiP1mglgqvjCwd0knBR+5AiUJTXJAJEx6TS\nvdyPRVAXvondAZB2S/oBJoZzmXdVlBnTgriF3TjhjX8yMYKNhTEatGfhbVrT\n08oFtuqGKwVzo+Q4lklblDncztEFA0aC2WJXWwIHVnLz+11OwstxAtRfS+qp\niG2E23BlrGxIjdHW8A3rx7V4uUeDUGwohfklIQwP56a7TBPW67kKypaNIrY2\n+XnF9rERDUGwIbpTRnEa+L5P31hL3Izvf5jvfirVMPSU25fDt9qEAu507pYn\npcYE\r\n=+STI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-primitive", "version": "0.0.7", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1086fb292687dd1bbf3e3cea01296d7a29f1fc52", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-Yfg9MUivgLhkFXNuroqBItX7JpNGqNGRUOz220wxmrZicdqVdOC6ZNsn4BNEAj53Y/Hdc9LUdrgvjo8dQ26Klg==", "signatures": [{"sig": "MEQCIEQIO+VVg00EXyJQjz0xWQJXlAOxdVLyyYwgpQTR1P9JAiAbksg+RFFQ4XFCMNRqyOp/Ewr33Eb4OYidVaPFOv+h1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmO6CRA9TVsSAnZWagAAyJMP/A3TsVncUx7oT21sdDYk\nNEeDJMFkIrjOXzhAx/HwV6PXGnbFCUg9SN8HXbwry85ABA/3WOK5SzG2SqGF\nK2dfioeJ7LEhNO5illQAz83VuhNRWlac8jDq2u6u8ODehOsIiuYC/Rrl+eTA\nJNP9tSg2i4PF6ELLGY5P0eISChAuCIZ/AyKdxwgiwpR5LWhct+QZK88WVq0Y\nSQbNCnjJ85WZNaxPFGIlR41xvfcAHllXL8VE0ebKSnsIQtAws/9/cy3RKUmD\nVmtO8GNnffEzx20xG5WBsrdqLh4SnjJZu+phXnXIURL2UMk6Fx/MAvn7Sy4T\nVxt8JEUDqXsfGX3GVos+9V/eTjicnHdmYelK2pFH6VFU3HwlcV2Fsok4x2rx\n3Nq75nHSA6CuWUbPfvBKGevqjDmAHkOTojca0ZhmvMzTR+5RkVaDSy9LebWZ\nDzNOSTKg/Q00eewLlSuMuS8lJ0u2nemNgQwLlOfslOHhSL2zAuPPkn991xZm\nIoxgnfXitXAORzDcFlqulhEYKe7fOCLOQsVNhmPeTd869X3zxqrO6oKbQJz/\nVnoSUv657yoMjRrWaYT5ylTmNiJjiN+j7hvl2Re5nry17fM4a6ibJbkFEc/Z\ngtcp7JdrWKqxcAe7mA6JKMhDnFSu2k3nKb9IsFXIRgl392J/BKZZ+k11E2Ok\n/GzW\r\n=LakG\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-primitive", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cd04ea9b7eec2526bd1f3fec7cd0301772e26311", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-iQSgSqRWGn6HCn5xZG79ON6FWIrAPLvAKyIU1GSm/iBU7Z4zqtIGzOSVN0DKS+/oBIMEvtjjR0jJ0GOAcXurMA==", "signatures": [{"sig": "MEQCIAQp2s4C4RTz3VaHGoY/s9S/FFqIlXiARl1r1d3jvhXnAiBAg2YXCuD9kkcFFqR3ZWwf9jmihhHBja7XJTgfEOh/Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0guCRA9TVsSAnZWagAAPIoP/3TAFu1KnwsY4To36RVT\ndHr2EdjlwuaUqSWrghWb4/EAKQiPjL4ILJ5QP8eXC0Plzs5GyZSUKjlMc7Ir\ncvi/g3RmDa67fs8ut7XHFtqa5KZ3ObUtdOabjgGl1z8Byb4ej8SegYFeh1Lf\ne89HrIWAg3AeLGaUcB7WPZIM/xNhQLCVnDXfCITG530SM+UsqqTavi5myvrI\nCLiBnhgq+WvxhGtT3cLMzxKPdRzqlQaafwMOVzeXADKML2x0lmxgIT33xLCm\nNXOmI9e1RBYfl+B315qEXYQFVrfkbxyX1PXOGol42Y+Ta3J5Hw1ZW5tBLxln\nUg4n1jjn5k1Yla05PufCfUJq+56/z0XcMAa0FKWu0d5OxugNoQpGItobnpBC\n92VrVhls7tX8c5SrSFPpb73El+6cI5mGSMxOXHd4Lqg7RJwGac7fj2VL+N0J\nnJMM2LTsopeaoPtSP7EgmB9XHBvveOlHvBLd0hS/PujkhTKx7JffY7+Uk2gD\nK0ExqiIcqN9JELAftF/g4F1QOPYG3hB/63TY8rpnixT2Mrvrys+RdkCnwCcQ\n9AaUSd0BJb7/n8V7ELW1ZDkrrmIxOWviLe5KYg1tRgiqKDei1dOIZ4CPjtZ5\nYRUiDYBYTa5f+BZrpZ2kY2i6nG0kxzmGP14VS/Ew1WOzDl+UNbDtur1N26JJ\ny7cp\r\n=Gg00\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-primitive", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.8"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "19301185ee05f2a86c95168a604afd5e4ff7a314", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-QMRJ4EM4CbRuEkqOcoQvT/amR1X6r9tP+Xo8aF00aC8gg4Cj2PixXg2OY9L8BTGtPt34fkQeCDq/lp6DyEvdkg==", "signatures": [{"sig": "MEYCIQD1wMvbcikSINqQ0mDEfFaxIn3Ayem9JNGAtXpRZLf1iwIhAM5bUeII0XXPMx+BUm3uM5brrbtYxweWrrH53TCibcXA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H4CRA9TVsSAnZWagAA2sIP/R7Lt6rh/WJWP1kzrzVM\nMlHLT6JOTPu3QakbO9/RT3GO8WkDt2NBOlGzJAt6nau8poTHBApZm4RPSoJg\nLu2CyMRhEhpZg1Un1xSPUk5ZRfkboCtcF17+vsMjg60HPnY8G7S+fxodtit9\nNeXQaz5UtDUeIgNdJdSs63UrcR6DiENmJXsbI5hbx6/Djo86rDdXA/mndl3z\nfrG52QvgajkWWVjAk0RrD4of9q4RzwmwnYoS40c4h/9dCCYvkTz61v47GHiV\n8lH8J1qsEE/F1kbpKMGvjcFbDRj/k4oe69WQVHPP9Nh0k5Uap3v4W/BYkJ4n\n4V5TAN0zPrFBhJqF+RsYu4vlYopmfy8npv2FO+7TdlplEZLtV/RvhhTde74x\nsOa2VnCNR1VYnuPx7RHYOk5/K9IcTEJ5RssodkjbLmRd63t1LamBg95YR9M5\ncQlzVgr+6xcoo7yAlQMY6zPkKDNvte3tjkuaDZAeK5kUtSPYrOeKY86vvFyD\nrEKnxacwZT83VYNSfwFGJ7tTfaFoDK1yHYv7Ma5opYpvhUWPSRg1c2NqJpaO\nn5unxSsuSKDZY19a5kk1dEi945bUI0Pu444D86xy7401YOrzGz7xsQ6nKy6E\nRqKVuhRyeXt3pmF6YEDO/6ETOcKJG0R1al/qRBUddf6N8p5p9XZmhfDrIpjw\nm+2p\r\n=6Fpc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-primitive", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.9"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6f32829665787ddf7936899dff11ad6e49e7df24", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-hLI79atTJSDlqW2mornJEKvHz/IsZzSvCuiT0ogqVAQsUZiCWTP2T1j8DTQdDj509C//UsLfhdR1h73+0+xSCw==", "signatures": [{"sig": "MEQCIDSLR8p5TNnMFKkCZFMACx2+jdtnYaup/eR9i0KfloGhAiASx76Kia9FK/CHfSevWagNgpHXHZjZQzr7BnUoGh+ElA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v0CRA9TVsSAnZWagAAqn0P/3F3K0NApRJJUM8j7MNk\nyBKyd72+500O7jlTPAO04sZk1152JN84ouHJAOeKsQeeYyu0MEq/ywQSEa0w\ngJ2h2Sp6yGLA+GBg2l37mmj8UXgsvQf/W9Or3ul05f2SuWvpIPPFzqHkG1dr\nXjtepWCxDMMWfZ8UJ8iIOVvxPbq+DxGs+spY081ICDdaylb25NeoUfpKhfBf\nR9V4m2dZ4HATYDNmC8QU2io743bNzt7NNCSSESXC/kY6OT9MCvFAXSnmNLrN\nzG4y1gRfe4gg3ca0//AyYeiF0hM6Vh6Klg7AH0r5QlO2+aygXZOMVo4BJb75\nn3lWCY11dWhvJVQtqkKXAfesNJtMo8Z9U3cWJqngpJegcis6J11BTrWeikBz\nwHKZLwna6bRs9P5G1gCnC+kpml8TH8BUTLeY+uO6SqkO1w0Kxf8Xgq9VdQN0\nSqCnmWw06UjxgFPKGJuy3xJbxR40B4Qd4ok+LdcunDfoe3fioEcWH/RVL2Q+\nJ6qu7OvHCz3w8t6mSrR1wuaFbCH2IBo0drVqZ5d0Zhb9mHiISVandmNGxp9q\n8izC4SbzBQSRMkcMZe5qCYjqIC4G2U3jD02XilXpC+YcVFGGrMg/POZAceaW\nIosSQAfMYUNaD8p7NciL1wA1IXq6YITuVrMUQMNsezthqn8u9zyCVnxg2G3l\n1tlp\r\n=orz3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-primitive", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.10"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30056af6a4874803ce6a4f0c1f90dec756331b09", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-JAo18CbcTuBwhGtK4ZfgwlEVtXU7h5pi+XSHyPMDliu0umPhRr0/GBXdfAHqgujDwQ4dXKcBmU0HWhIy2vnWaA==", "signatures": [{"sig": "MEUCIQD6d46uVqQLrVN3P5WSsYxOJozzKUyTMn/EXoY9zLugBQIgezgt5EJuhmU+40TgnLfJqehedMz6RqlP/IAewldGHsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmpCRA9TVsSAnZWagAAYw0P/iBiYT5EvfENfrEI/K0k\nMdnNhHJdWSHf2uX2dH4ujXR3YzizuRpJVL/0zekForPdVjVslt84xDLwBNzT\nNrKNtEnu2p7dGVLKZiR5Ih51eFiMlsXNp3DDDPAFjFx+rHH0cukyfwOa3uCJ\nEjaIP525bsK38L8e+HDFlRrn/0hcJ6HBqeOWxruyM5vhHPA9Qa3ilchZ6RUP\not/Vm/O2oSwMx/LDMo3Hkx0SNpipNZ0zrt3UxMDbWQWepZDEp4lzO5czMErq\ng5Je6bVadadY6lR8UDBpoKbmN3+Dg6Decdi8J5QVSJgwIDP25jQ2diP1HN70\nCEf46s669qrbehC9qilqCa92PzdzaOjYR4QTTqEnUGkJd3xrtFW6Wsc4OCJg\n3hoEcUUatlIU6Nlmu2aLhls4FQcMkLrVXm1QBzgKTWoPG55576B9SbCjPizO\n6VN76eZvza1IaaLbomXT2TeBPYK5Iwy3Ss+4s2ilW00xCdrrEUMUDYPonS4W\nuvvi94qqA1LjvBsTNuPwm/xn8UDERJuCIBWlwPcKe4H+j5ucAg3E4yE89pKY\n+EEk75dyZkJ993qldTk4NryPAlUT5iAfkpg4YS1ryelw1GgMXHtpaEwYFRbH\nRJjUQOkypw0V/G5LzEQNzmMJncOlGumS4ZrIrA4ckFtDUb+wE4ZrLJGaKRBV\nnIaL\r\n=6Y0k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-primitive", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.11"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b4bad44d95c415cf16aa3ac6cf83de7813d6f27f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-qJz8Rb95MOD+w/y6NQnEhNp/Lm8XEDfiwAIF/HRwEVBGYZ82qV/vWjfWCAtnoWlMoN6MpI2YN1pdU5mu/qQyzA==", "signatures": [{"sig": "MEUCIQCGmds1Y0qGgVbZpHQVDFKydQmFxK0AeAM4ojYXQ2g1WwIgQE+gBxOQmVfO5IKZQtFEVtPW9r/xok2FaS1mxrk1djM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7WCRA9TVsSAnZWagAAMBkP/AqgaikbZFU8UdGs0TNW\nJGP5T+WsJ0baDIzTOSWIuG43VPoc4qebme1Y8P8HkPOdutUidkImM6kbZd+y\n/m/6nTubrUKvc7damvy8+jGB0ggYJlk+hDAh9zdCdmPCCNg34jyYoGCaRji0\nHoVepdJxAgAYb2trxGQx2rB+i3/WhzXs2pY03L16clwoYcCdfN6FaMADRXXq\nVNUF4TFbmWION9JahSOzlO97omXj7auOArlPufftawvWvN5GeugUxb+LEq9U\nlQZW48hqVkFuKx+5eSLGzKzy817QTENKBX7errvjBXETjhLlsEHbgkPuWMGo\nyakpeanblFpAf5UO0ctLYWcAhSLzAUy1NsYgviAvPhCflHyWlaz6luNmI6Wj\n2jeTyLp1lGA0q+DHsjlR58h4PIp6VzchsmLC5zONurO18psTBmri4tH8uA6P\nu9xLALD68UJkSbcCOkvUOKoKDl/n42Q6pXChBVwnJPGyNs5NhbGPexumJbcy\nCgzUl4tJuXNsIcMNCHQsxp000lE5C06EEssEN1EvA2rZujQDuLT0+uCJRKm+\nPQn/FULj3zXhST6S2NHeDaopZNpLjRk6BcSXGf0KuqWzRLax9qFNyLwbpC5E\nB+UjmiJTMJtmIwGwE7MR1Hg/uOb1N00R0yYvshYvT0vVIsbGn7GpONYkLc83\nnBIh\r\n=BWRg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-primitive", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.11"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "79c606c3e7da9c377b77a7b3f4ec879b45de47a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-6xxWzw67t7ZuN9Ikn9NNQdb/HSF7dNHPN3kPcgjiVgTEZa3tKk1xjSxGjjQztE61g9GrnTLpu7mBjmEuZDI/lA==", "signatures": [{"sig": "MEYCIQDL81l01KcjUjbXPXM/Xe30WfRGhTzK5cGgNSkX5+xM5gIhAKF4o84dmPxPcZALMHpqXXETgrK8xXpJ3pFc45gh4NEB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYACRA9TVsSAnZWagAAUrEP/3Cl2Kij+Sr1NdTu22pf\nSEWsiJ8hsThBpXLaUzFY/PPaBL2UQJeLiRrMRQ1jc3GsDiMjEjjR+YD+k2E7\nigxNRsNbyvldoYJLfFFYTespNpmAm3XPssyAbxM1sYy40DNXCjH7jQ/aCo4M\n2vVYVq4bHcWzit7e8PDpK5ufJOjUszhG9I3U1DqyD9GFV9oGtkKqZDUGVELt\n1PLfdzns2PtUMO3TtLWzAcTFl/SgUVNBGvkFsAsF+ryduI1QTECzYdupXV/9\noL9wJFhM3vjCOyIgWrwn8PUqhQnIi8bxghD6VDFIc+bOm68bBDAGAD55UJJG\nsg7WezIixlF3dqLRNQI18cFpsYdBsPw5cS+Hyxc6jddkKHghI3nD388td7mq\n6+SXEu6eFNjl1vFicQQkL587mbCYDiW07uoPNy2JVBLF1qSdzeYIELW/8XO8\nQkKda9Hx9pCIMfpVa7DMoy4SxSA2keKyVokfF0D91aeNalP6Xdo5vBtEOnmo\n1/+RMG0hSlkv2Nh/QWx90kvWuuMDfmXnsXahEGFwpB7GowYUkNTemW1ivhU6\nzE1LRonMYhXRqqxarFjWm7TtP7EGn/4FGLK8OMoNs0kANOnyRrIFv9c+PNUr\nzBVMuN24+KtWW9dCbGZVw/kAxdCxGyFTycaZweyfERtUjtZl5XaYyp0lxd1R\n4vdJ\r\n=5yge\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-primitive", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.12"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "752a967cb05d4c5643634fe20274e7dc905d1cce", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-FYOWGCrxFpLdB534aWTwMK4Pjg8cxFb+745qWhPfI+cYi+aYUddJQD3ilRHHXxCBD72ve7/PufqeB7Y/QlKqgg==", "signatures": [{"sig": "MEYCIQCbdbHRT1DwKzJR83p9sA6QtPjv4PI3n1hgmAvtJM0wFAIhAKK+fbuJRDI2yI1ajUeX7tR+3P8LGJPMBjz+883WlUqs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9wCRA9TVsSAnZWagAANjUP/2lxyGIxN5DwNrVPi3L8\n60S/ign0+YxQqGJkyHBYtQWko9gbc/+E5icVcQkBwbJ9K4MGIakP8jC4x3vV\nLf5r9hn0kXdzs5ICzDGALYYp9MZg4jye6TKg8DzwuCNWJf4TkTAd4d6+7xN0\nyfyp3tx5mygwfUST1TE5AVgDKOzPXSMBE3Jk1aLY0RPOR5fst4o6RFIvQJos\nZ3sZjc3sYRA0r5EcgbgvPSRvrbuJ07r84cqQNg/t9OM4U3T8DGnlLGeZibdt\n37BXBx6fvegM/+hv4M2Juhtk+l4/ixqhSjXUaj+166BPjxnw+gM/byp9y+C/\nV8QohvTDgaGe0Wts4IcnTZczbEc+JdDMqFTkQCJEpgu++B2uuefcFKF2543N\nDDYrMOaHx0CgBDX8zvqky3SzdR2Vdi3pOt7s+lwK2783VWz+gr7heNYozNgZ\n1swb1d/53pgQxwMroBcTcdu+rY38p9cfTr/uZX9xOiB41yqvAkA8p52Zr9DK\nRA1+9zCSQRMrZEC5glf+EGsDCkY+pKOsVjKC3OPNkPnwYR7THiF9b89VHMUD\nyDOFJgwx5359yPW4v0RgKnCFxFOMX6lJpTG81cOFKa8ai4bU83iEeZy+Harv\nvJ5H1N7PHQG5ioi9KL2Tobo1JBEWIflgorct/mwRS370gBc4sSQpqRaJFnen\nXA0g\r\n=rHG1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-primitive", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-polymorphic": "0.0.13"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c0cf609ee565a32969d20943e2697b42a04fbdf3", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-Y7JLnen/G3AT0cQXXkBo3A1OuWaKGerkd2gKs0Fuqxv+kTxEmYoqSp/soo0Mm3Ccw61LKLQAjPiE37GK9/Zqwg==", "signatures": [{"sig": "MEUCIBFimkn6wsdWAZedtVGFIJmgfZFIrkMw/kGJhCz4hhxyAiEA48BWb6WD0FkUKCQh4fipTdk4AcyYBcJr0LFaq/HAtME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTvCRA9TVsSAnZWagAAQkAP/RStMKH8FL4bANF8ZcAB\nAxNosGZVd9VIyA0h8rGMrbehK0FN9Jt7DnK+iNhJOY//5WTkrGTVqg//c8QX\ngS0+xM6d6/KWJwYLQUoL+oYyFNaq8IK3rKMyGP9I3RUXaWw8feEpdkQWFidf\nQ+/OpMaOoL2tvgHMHvb+MuSjrBIIKoDh1AoFgEmGSDFAvoLFgUBvCpTA+Zfk\ncYxlsIfgR4Z3VkvLSc9DXesP6FDKI/LaUEzqpuI4XCtLmBh3aIwAExhkE/zZ\nIdNunyoAYEbOspJzJD4nFNqEN0K/ztGNXPxLB8z8fzDuCagvPS5N3+UGzW0y\nP3HQ6jCGU8QiQTckgEwrcnrclk3Hwp5+OUb5DWUXQ039pHHR2dzR2eBMSmLs\nSY5Pek0HukLuD1DquULz8Hf1WyHsAe3u3Zyu+BmAfzwQRABIWlG4pBNnueMu\n5gF4iI8Tm/RSZbDNmGTYmtvLN35Rcw0f8zqGQmqB7vyjPvN/XGvrhE9tNcJA\nya5DxdB5+SNAnyk+G/1UxxD42Vm7FOmmHF7z8sMmcrSo5Klmoz75nare7cmg\njF/nYY7quzyRpimhIvLcx9NIhYNrPXuJdYbx3/wWMOG7PgXhZbhaeMYnkCjC\ntD2j71o5Yu7K2128lMwwfO5n7am+HXwhACDQ6VeZywqTNQaNgwNGWouOvsNs\nEL2P\r\n=LI6c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0ed299c41c9ac999de58924931e757c79e097cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-IRi1PhRYAiJpXvUOZ4HnhbTVbqlBlTXpxp1rW6KsnbhEzCqwZ9u+QHfkqrkPO4oo8wJDOXwYCSRH6tNSlCkMBQ==", "signatures": [{"sig": "MEQCIHi3nGK88sVFQnJ84OYukPcurWBEcfXfA2RRQ+z4AVFPAiBr8TP9bogO0I/Er0Y5FS4J5JSxXDlDXOQp4Tfuji+X5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpjCRA9TVsSAnZWagAAKskP/iPR5CezbW3vyJWk22/E\nX4OA+L7TnJjpGashIZzJ4N0vu1g/1XLoCuCVQqPN0j8Cnf2bw11PEJbAJ4ch\nb8z+mBBfayFCGRot5FMLtdkL7ZXz4kIEQFrMSzflLOSHBOIu9GZvGRHotWIr\nfG1yJrUbzpXTJYDWCBdlcbwVEz+Q9yP0O1fT0fr7F7GYgSLkSv+dJJlVZRP4\nPAMlhCEyr1NS3QPoYNa+edYKHrg9LoEpZKKh6gtmK918l20gQPVrvLl6CllJ\n7Ksaua21HCoxrVPNX8Gzz/RRh+mX/PJMdXPKVTuxKd9FNjX6WzRsCtHfz2Jt\nGpcUhMgKsiK3N2dY6Q2+1Ap/rL9i6r+cr8XU6Le9fUcHE6/P5GQjwldMziHB\nhl5wWv1hGDW+fFuC6FYKb59N6+F+Gy126GvgyEUdrzsarFUbI/Xd9GwboBBr\n3YwIWhUnQbY9y0ANQM0P1FfZ9jxBjNyaRRoThCT1PElg6Ivh0nfJwjBhOVwu\n8+lqjzC54ysUtcmR427SDHllM2NgzezGYB+Nduq9PM+ste62Y4pJOM3JUDO4\nryxyPBdIqLhOcpjua1bZ/gSsvWYes4RjaV2MiUQJkufSzYb3yIHTC+eK5TKb\n6RdbkGnarX9XHesp3kYvbOT3+bjdthf+9H2VeAQtEU72FMGgMIP7wNR0NrYV\n4yDl\r\n=d/Yq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-primitive", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d357d468dc3e95843b552376c5d3cc228c23c58d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-oPzok6gtlMlJxZ7cgqJLFfe0tiyiAANX7SUQ+39L6qjDcN4L1s2JoROX4M0gB5UwGQwiGlmF7pfDj1oM1WhLyA==", "signatures": [{"sig": "MEUCIHcDK4jxkHSwFZuuaK7VAAPtPGxRufe9qNOj0Wi0YmGqAiEAzURf4jfAwNWJ1tjmZXWiQLigJT9ZLdIANx2+hHhMU5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyXCRA9TVsSAnZWagAA1WMP/i0lLX9mZjgHbPxXjVTi\ngnmi8yjd8G3JU4txVwbW0yuOlSey1hLLK1c9u2XM5FlIiNc/29FCB9WFYeTg\n3P1Ms4rpWS36a3YOqRdhNHj6CqaE8GdSZbR7DuLzlsC8FzKEk+6B8TvJJFYh\nZOcDqes9aWY28WP2KtH9Hcd11+D+fa/JC83FPt568sx7K+Qk4HxklIG52ccc\niBTf9KtfpSqQdaS0x1A7jCFl+lKvsBCBfcghPSqHI1lsmIgYy7kuxGJgwnJ/\nybZV77ruggjPc1LiMeC0jG9zJwEHstWP5L2DZTot2CSdXsCJTvV327ULdBll\n7RuyFDRQL6vRhdlaBtdj5YikM9IYwxU4dO2gqMY4Ovx+UfwgcpG8rP0PqVkV\n/8uhs5tIRmqzPO2C3bdibsSIiTz0CYsbo/4UnIjerr2q9UWarLEpZ5aAJZ5e\n3xQMb0rRoWaacuWs8Z6TiuvyCyxELvA3QqhorhEObMhOXiUpjTRYn2L5FCrP\neNbVpDgmOXcVwUyRnj0VRkYLQliGuX+ah/fDVoVtE3kZEMWwa0iUw906VpdM\nI5h9oVeeoPg0OG1l2P/r6mtCLs8ARV48ndH0h0xYSCOCpg5RySVcXqZceUM9\nGylM7loenXr3N9LTFObOXJK7X/CoHIvTGS9GatU8+B0Sc51NJNEB9Yv4HFc0\nNFSe\r\n=Iu+s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-primitive", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4e6fb04ede36845cf3a061311a4f879c2051c1c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-ydO24k5Cvry5RCMfm5OJNnIwvxSIUuUZ3Kf6bu1GaSsDfBKiv5JeuQkipURW28KlX7I85Jr/I02JlE+Ec4HmWA==", "signatures": [{"sig": "MEUCIQCzBl3nawNxk451vist5Hm5wvHYsL0fuNqdWWv7xHFIxAIgdEen6eAqkyoFjh4LC/ywTmpXktFnEm9EujmuQH7WCD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmlCRA9TVsSAnZWagAAeAgP/RiGuzUdw2VIZ3A+ywav\n/QyraF4yS4yvq7eYYPV74G1OXbYhqzKg5ezIKcqiPr5wka/zgdV5mQyfFvWW\nqw5uL73qy44k8wEvlVTKPNWB7uQsHOt8Sh/gupu0skTDtioQYjMW5qHm8bQ5\ntOISJzzRmXzj6RIW76doprdS7hMzafuX2x3dpPol0b9fdVihZ7zdIjlrWvvZ\nCRkVxd4aGAbDkDrP9j4AeALj3P6RBz3F/7bDGOYZZlcmSARB7PFeq23huyM2\nu7+YzYy+oRKHiSzYS80uFLiQI0bepM1NS4Hr1CsFx6FpYLOq4sh4fn++rRxz\n5CvWBO4mZfyAVnSuWwwVBiMnkEqIKeAEqb2BsoQ70fp+TGDOxmEZxS9tR+oX\nSvtCHUmDmb6kZYfnTrYe+880O82Hvzy82XIMKut0fdffauoV2MXC760v5BP+\n2Z5pTxw4wp5i3HBGIRmSZqHd2GZnT6vxkNWIgbNr8fGlHGdlNyQ1d2VFCo+P\n0WgAGfYmtQYn85VhmZNmzuWFGqjuTWmSpZI/mlpzp7xMxHFkSWEQXnK43+sW\ncexpKdf+pfL9uOO6owIVnBnl1K38c6qjFqt6WAawwIAkpgSGIr6quUqaxpGk\nC/ArRLZHP54Lx5F9vrkKQL+JBMIGt5Xv4mTPRnyaRILEC/PAOQ+iaIL5tEgS\nYYSA\r\n=fLYb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8a014186ea87ef63bcfdf6c3956771b15413c32a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LffQnGOzJcpSTsDPuKtGyyEtdMoB91B3SCvvw8zNWzF6zciUqxAN4i/WOXYywvYRJk/fpV540+96xmm7lzK9fg==", "signatures": [{"sig": "MEUCIQCNDxrbA8nFp50FpRcSE39ADuHUVQaKV/KkruyogV2YXgIgTw0BkVsGPuRQ1/Wd0lTuTTSLAfA3NvnYxIHPgGeP9Jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInYCRA9TVsSAnZWagAAW+sP/RuZp53GrJRPSlqUbvuW\nVI7PmJXD/dgiEwTXnVCUF/sIhQBakj/AmiLrfGpye/UugMA73gFRTQQxG6zh\npcMhpXlsLjfKP9O1QUEOTfhdQypY3ep7P7/0AxSwAfnQQfi8aVODUsODNTev\nSnEkrtK1L+OD7lDuc7Pu4/e523y2AxZnLt2OUZrKQHGfWARjGiP2JPESGJdk\nR+cbxc2bzT50X6mP96pIHfCVFobwmAyFeSKcesqVs8SRHxDpg+Hl7/UnxjVO\nTv/6jhhjgLtGrmsj7fotDZW9LaV4RE4j28HJiWWmkJjBGpqLV4KpJNxD2i1t\nswvKdPyRWEYaA/1KzCO0rpSoJvG9aHQqEUGVK844eBWKwcm6EYCI3H9i+4/e\n24N3mw0aMgGiyPtEWui7PH1md70tNYzpXSM46GLNh1LHLd40BZ5zZQ8zetBa\nfVytZcr9YbVyYV3YdX91ZI7MC0i7OXUrC6mAw+37Vm58mkLI0EO1yrfh1s15\ngs7Q0iGqRqpgBY4Wol9Zzgq8WGPK3lnAofoSdTvkG/zU9xzIQpzpo4Ba8qDi\nE0foxQ0vfIC+3Q3vpPw1FlZ43Iqng3Q4yBLrYL1r6giHKlPrD8sbHWaicAQS\n4IigybC+x8yh2soyc8PLpw8lLJgzv4hp6b+PyqD1qNNEnrrJfdqJl2Aa2D0/\nx5qu\r\n=/0iV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0cf2dcfa0cb7fd3d2427ae90b49249785d8e2437", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-SPAY7DFI4XKg84HMiPrg1nkWo4yfnHGbpvw0Xh+jYnqtbuzyLQwViqgi0gXalPYm4veWlKhJx8kXmumlLUO3Rw==", "signatures": [{"sig": "MEYCIQC1DRJoq4Fe/02odCz/4PiXbNTnzadxiVhHG+K4bG0J7wIhAIK6NmMqUMmuhDv8WbQCjawFqqPnV0KWf3cbdjTQlmXZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwJCRA9TVsSAnZWagAAp5wP/ieu8CrUEEOy0Wz27LY0\n91ChwsgC6VDgaPeAqP13xE7zj5YVFSISzREK1LGbg2diGlW8iCtcGkJk3pw1\nnuU09b25XEmbnmXxUuLGUqgM4EfrV12z32a1Fxt0u41ton3jaC43x7cOZrKx\nkP8Mt7wOT4G2hesA0ZhpA30lA5HkcnExAI+rtfG/CH1o6+aVFwymp+H/0+W4\nFZ0aFlpCKFvnOt+7eXM2/30Zt/ZSo6zk9sgWA6lq2owkTpo9+ndAzP4aeUz1\nyZSlbXTQ2etzY5x5HF4QG+54bx2XlmunjQBkJM6xZbccfF1JVaq+2u8bFQTk\nqtce5YcgLhibTr/xWQHtO2oDnKYHUu7P8R5TOefeAgV+6XwA7rh/DV0dO/gR\ndH23bUnbz6bHXSZsaxcV7L4ox0iN51gqW29l7f3i/yMvKSesNTOP6k4Di/Lw\n176VEJPZoCrckh7EWXMir3X/S/wX3tTKXaU/jMlAHA1tWuSuOOhqZlbvX7Ix\ngALW/T7nh3pLgfVUBQG152mHuOlNpLuJokfghXDQKHreO4Qw1XaWlSJIMNFE\nzDd3+h/2A92mRqFYKCwqJAjqnY+YR9qej6+CwbzeZ1H9Vhp7Cb/GFYptHG7/\n6sVwCKiZv2RH3uZ08JbVJM799ht/J0ZPt0dBAiA5To59c1FYSVAPFaaaTcMt\n3ler\r\n=+3PX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d98ccb240ede0fb5bebedb6f7f4ae93ac305f7f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-KeLJG2Ar4YdDqLgygEhxKLKgMBmsWHHgnQscCfu3Olf6Q9Jz1VsqO1js6afNSlH+I+zxsAswYJ7ulwLutPagTw==", "signatures": [{"sig": "MEYCIQCnzqYMThq6fsUmFNJWBlO3dfLgbWHWKk2w2OKn+fsORgIhAOvlnCAz994W/zmqbfPPJJx+wyWJ8nEAPTa9zFvOyus7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UHCRA9TVsSAnZWagAA+jUP/3Xvb39o13mw5t1cLo99\ng/8f5IKQmmvkEBGQWSR4lPSj9YEZM8eAPue7LuNQSZrbn9aobvDeUIM1+wN5\npGGBIPDEl9z/DjhP5qf2pHrdVQFYRBwcaudtd0eq11oh5CTv/j2xgQ4bSZ9g\nNHyUYt0Y0qY1FPmT06RssotXuBX7MvekteFjWKRymruKLa4y6A3EccftECta\nX5bjGgyjz3vZur3R6I8QUqHaXXZQJEgZIHUFRtXnmVfzYsJOefN3LUJafUec\n8p68zZE5fIvdZb3RGZmu4z23USSQy4TOXhumMF6VIzrbWyn2noRiiVkut9V8\nRkEhM786w4UmC33U7g6PvOMdaQaNutja7eNd28NNHybVtHPe55qZzCFfY8Pm\n4T/XttJ8sA5lDMnExBrAEWngeajMizQ0D/sSx5P4b1kacb4k3ckLSLHc1RkK\nU71WBy60WuyUa/6HCmVMeYeekM0fC4l0H37V6mzJF8fqlYBZrVEf4pKMx67r\nHR6/IiuBSe7PixQ2NvNw8xnMPNaLOskrpuAYtzvSGP9MHJ6QT45qqEa3BDZG\nHjZXQZel7slE41jn4WqmO+P3YzxzxXMZDSeZXtpbCK3KbMV0PruQTnjLwixz\n+5lEviCBYNAlV2wjaPf499f83gFOj1Ycol76S8rWS2lhEJ+RbO8h2fTzmmbP\nuf7+\r\n=TKBL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8eb959317ba6a3eff7f4e3979a8f835a04f5a991", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ih2LGMOFnyzXrW0Fq3x/0Z7Lfcwi+ERMprk9q8e9XgXqYrNN3G28aCVc4oBkW3ZfqfTNA0Ma6kYMZ9klds0bGQ==", "signatures": [{"sig": "MEUCIHBAWSIXRpTLLBGmKMicFVBvIdF/EH3do+FieEwVJoWzAiEAqZrr5Tt8W/Aq9XRqgkpESkeVpxfwDT8fAzpsp6CyAwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1z9CRA9TVsSAnZWagAAJYIP/2Ux3Om34T3PMusArt/m\n5oYmf0w+b7w6yfuBaY5A4rEDyCAXalTh8PTLU79FG7ZmS+Ev9L2M8JSCw/Z0\nr5j5nOv4nd127YTT9Vs7HJ83vCHWuZjWX+na040cdPvKghz0r9HwFQNHgX6N\n3iHxqJXWE6W+wDtCSG6J8zqJakcCVmMgYnaPW0EhziuT61MPg3zufkdV8gcn\nw9eOIL9BEQM2FB6m7E9A9EQrTziI15QAwDhpwCHaZE5y7F0iaj/iQT4ff9W6\nN46y8AXDP2xXnWBWPrpfKYYEPeGkPOXX4s7EKdZvlZj2ePrhdn0XxD3WMYlA\nKOBBW50GcIuTaxHaOSiUhmxJKYAVHFn8yCBXXM6W0TjJ31H465OJTDVsIHny\nUfknV49CcEXjk+TyudxuTKdbaGPmT4r9MWAxMowkVW9lJXRjxInU+n8iEsA9\n47BrZROufwEA9Pel+9BddpORzgX3ZmwHNHKLnPX/yQWplePeASmN7nxWDE5C\nPAAsIiCiIJWLkdWvi0x/fDb1CYj7enUC9tdLUAXLMk4ubSvEdSE+NWUqKFFc\nSFZHGgDezQg8R55tY/nQ2YVcYPdO17C21mxmhexwwq0Cy8J7ozTI1232F4Hb\nO1qjfPjEDY+v0SbwYVKQ5IivfHXh21j1NUBEkDX4btFU1rDW1y8WauCbmEwt\n71wJ\r\n=9aBv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "43cc3c5109e78522ad410d2419c412ebc06be53b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-5LgE6Ct/fsq54PBj8EOXqu271r9qIHMnxKMrkQLHihlEwIQbwZ/DL3LgC5khnT0s7UFdqhGaV27wkvJPmBK+kw==", "signatures": [{"sig": "MEYCIQCZLcCpnHlWSGDERpFQoWjxUdav5WmZYDbVT5ZO3zrJMgIhAPixE01c4g3+Hdsu98tqq3AMBwXMF60DeJMVmxivBs0r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkPCRA9TVsSAnZWagAAGPwP/1hymtCcwSixezkRvat8\nPW9yWLokBmUktIYiLTSFtBfhIAhmvP8F400Sj4T0oXbjs8+6D32rap9WiX/q\nlo7xP4MOjEt8ye6W5T+2i/kET7rVvPq61UlTDvdcGUILwY+PRAlvjarrhslQ\n1Vc3OiricpbD3eXfR/TckYpURNeOAetCYBVkUicg19a5OMwQ67Oezu7xCu2f\nDcEJDVse6j61ennNGUDfk+mfmA86YIkFoARoxHct5L88CpQd8Cn8kkPpIpnr\nDoOZGc34PMANAbozi6c+rjcn5poaXES/cB+em+EfYGwiOSvRKGVE8Hx9KbzD\nf+avp+pF0ILL+WpHl2B6btBZASm2Wk8qvL3VSdDc6LHKlLVzL4u2rocDd3gA\n1DbRxcDp7CfNr9FGIw0LwvTaMWXviqoSQIPQFNdD0EJwAm58LN1myLKIYnFN\nM6cbQENFvuZWcaqrUhR6CmXxHFXtns4rsTdh3jwLKp6CJmhEWC2VlRiMhg8N\nawIcxBIdhiceQDQqiR5ucVxJBnq6PbLDmvc6QwJpfQGZ/AfNsnRhvbeevsKw\n6wk9THqOnZaN9usEx5OTsIGRw995dZDoHh5bqauaYuyqnzrHLOC+yYg5oYSu\ncCs/zWpVNbtVkgpmZsP02IEbNqodQA/dwzm1jCyL4+tP74aySezX0KY4QArb\niZ4n\r\n=xk7p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6eb45576b172dc35e693024ac823da9f5b340b58", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-OpdXnZuVrxZEXWsLI/r7yNHVFxvxpm+fJb8lMW9YJvUsc57QMo3mkMPjsFukpecI87DvMdxLqKBzdzxscQA6dQ==", "signatures": [{"sig": "MEYCIQD51I2/bhhCAvnnVMvftCDqAc8B5daKvtgdvHIzz5arTwIhAP7M0GFSj3CyCmHaSxGLYuc5jkvHP5T6MNLx7Gk5w0lK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11376}}, "0.1.1-rc.7": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1fd59e24059a8fdf011fb61954d91ff7e5d1d0eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-5h9nPX/TOp/x0jD3vg2YJawFhWSHjZDFXYg6MsKFeMVELcV2sNd3YddIEzut+TvEetK/n2Kz3KUqrEqTpTFcPg==", "signatures": [{"sig": "MEQCIDDoYKR9Z5RlWf+R2RuohUWiY5j+G9ktyxo71n7V/SZgAiAxgLkfK8IUIg5iILr60heJdXtQdauFRpBPqGmtwAje2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11376}}, "0.1.1-rc.8": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.4"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6a232940aefb9863d83253a8dae66147932d0757", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-A8t7mae1HvVIM6ai5Qkfw4XU4+TUoN3ebg3aziciAnRGzje7g+g57BvBkso0w5ax5USKbt9ISxxufFce5UFdLg==", "signatures": [{"sig": "MEUCIBzNepTJn5YP8NwcGs+sLxWqhIC0Zwg4bAccqrKeEz6QAiEA52J6CHq+LPTFMYA+vplD3/3pAg9M7wPRSMZsqT4U0nk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573}}, "0.1.1-rc.9": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.5"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "88773c0dd02fc5879f9db65413e0f7b74efc1bf0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-1q/rl9Ws3xa2wBywwRhGe+Iv6fytbIhtJMZvvJCUcnztpFJIOAqn6L8rDm3gjvMYGE4GS+sdsw/iGv6hGXd15w==", "signatures": [{"sig": "MEQCICfR1jA+UysOCA8nhwZrt1gYivXGt9IIrmNzSCap+LdVAiAvnZwdCieuFxyElwmeEJqs2fGmevV/UEeKO44+gofwDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573}}, "0.1.1-rc.10": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8d0b0c181a99460a01cd6056ed26a0531aa55825", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-Bsqp4XALIEXfQ3FJuLC5xNEPARMgF4YF/hiUhR3F/HUahAEl3oMN6SirsuCbEMkwayxW5KYddUyz370r3qw6kw==", "signatures": [{"sig": "MEUCIQC5Gs8qH1ovTYkmLB9kmelml9tZhXVhdPC8hx34rXq3mQIgGMlD3ZL8+JBe9fRbxt3JH1YG9FjQBa/wYyqhrDzfO7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11574}}, "0.1.1-rc.11": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2ad6da79071e295e2d67df0ce963601bc32e63dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-JhzOVFFjEsbYrnQ6e4by+ttwUNdcVlNjLWmBfbLpzxkdmhHuKXovgB7iZA44l8hXjiQNANhTzscpw6zadH9JIg==", "signatures": [{"sig": "MEQCIF/N/Bj6rW75R1ToecQXV5zXXZlWWoMnhlag2fPl6zdhAiBjiTplKn97QkH/dXGtvJfXUT8tams3TJA2Xw3RHPXRAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11574}}, "0.1.1-rc.12": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.8"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8ad32ba49721041ebe3188b40d665c681d833c8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Muq0ijS9P/vauI94G5Zr5amX7Js7Xij0mlmtZPqJZrXmJP+YirOMNTokiigVFiRaD3ujftHiQXM7TNANWbozlg==", "signatures": [{"sig": "MEYCIQCMUZEfpEavTN+EmKhJNkqipJ/DJauQsxL5RDXwdvybjQIhAJNdgxNlkX7aqMc5ZxcnWTuq+Fs2Ys+dWpfJydo/+kb6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11574}}, "0.1.1-rc.13": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.9"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7e4dd539bff0a7af8bba65b62c4e6991ad2289b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-POrOPWUhlkWKyhpndfdOzvaX9273dlC+eR+QVCX7Tt2ZCsxwoX0eN9RbdYrCe0igPJW4/gOormtw0Ygykue/iA==", "signatures": [{"sig": "MEUCIQCEMKOaGTTyUBdraNtS49cLVVo1rxKp61cSji49AbAedQIgEL7VAc4FYfxmLDopoCkJrwVdgmI+Ujw/DLu/QaDIv5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11574}}, "0.1.1-rc.14": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.10"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4a78d2558f5b4d4cef8442faf4cce8936a4b35c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-MuCRBpbv3+kWRBh/7l3+8Kw8io3drIi3xMbw4k3Owh/589TUk3EGSo2gGKp5bKzlG8nw/1pIYBgWGFsqiRHzYQ==", "signatures": [{"sig": "MEUCIQDPHEDlhx4pVSp1Ift+T6hidtO+MsMx/mH3Y8UUd51ZbQIgCjT5z5WEXXUxnIRo4AFP/xsVmeKVTkLmmNH3pzOFaW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1-rc.15": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.11"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4058fa0f47532fa366d8eb7340f1a51fab8203f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-ZzzkwO+TT6czg5DJtg2dT8b8qzMGYksCJEi1K2uAmMO0XF4Y/oKOKe+to0hFvCZRN1Oyvu2pL0LGcuAyAyKWsA==", "signatures": [{"sig": "MEYCIQCSKGBCF6jteC5T+f586iZExrBJmb1tHeeLqxyY+3CT3wIhAIroD28sBxXBt7mzLk3V6zpu/GBsxEAscsogG7OEKgAK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1-rc.16": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.12"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cc729a158b13d614d4b01c441dc2abc79eb2f833", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-xi/NT8BsULPQPlpBDjBeq+lXUO0oBr/jo0wO7jF/z5AAZUmxpm4mzsCweIwoWOY2NUcfJVXIipqnvtNJSZPJHw==", "signatures": [{"sig": "MEUCIAm0tWILN4OnCggSxNsSaZYYB+InxUt81+W+RrEdvT/tAiEAwCa5iDul0lr8mzFQ2CuvipWCqMQMw7X+JPzws27ErCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1-rc.17": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.13"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3ca0814b9f2f7ce2303e9af8a7362242ba669a93", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-UsRgQWKpSmSz2xWfqtR969eP1hxk227VkLkOdARhZFJYGX1b+S5xtXFHRZVw5MZytpV0EUXBJ7bJ6uRS2mmLpQ==", "signatures": [{"sig": "MEUCIHHLPnZIIweTQUacZRQZsZHd6KD0yCQRq/ruGi9nRa0kAiEAsPluotl1ioR6EjtI0PClFAPp6T8oYJ9JC67jqfMRIuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1-rc.18": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.14"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1fa3b7ccc8980d83b05232b3347c33405aea296d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-Ro1ggjDtoPQxXTCu/tW/cTSrLMtqZdtQIwhNY8XYDxxVDss7XdsbF+/kI8gz3c8i67aQjdTIa0Rei5VJ5dKl7w==", "signatures": [{"sig": "MEQCIHslEyy51HOAIajWflxGO3N7UibcKEdjsfkbOezEx6gpAiB9Z+0VgqG/kjtVK0JUvowfCX7dIlarnVTgse4NKczgPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1-rc.19": {"name": "@radix-ui/react-primitive", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1-rc.15"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "521cb5d069c586497e96ca03f941b175b8f0da2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-BW0NK9H6JvdKh7fXuHIEE2bbZJTQMryGuAyWhh3D+LrbDlIXf+0zGsz4fjj2xRqqpRulEiMLU2ifWIzde9GprA==", "signatures": [{"sig": "MEYCIQDVeac3c01r83Klz64v3IA2bHMlXekJE86wXkjV1Xy3ZQIhAOc9Zo/YzH8OnyXCCFUgBlhB9jfY3bfF1+UI2v0hLrsg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11575}}, "0.1.1": {"name": "@radix-ui/react-primitive", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "98e64d9c3094df737d0f49f0e9e48ff2f44498b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-65GCHeDV/ikicXKR2rLSO6w+dyUQwSG2J1JD2qm4suK1259nTuRvPsPBrbhZpoXWQKj2drMZfhhclXVfzwW1Kw==", "signatures": [{"sig": "MEUCIQDI/CiWKT22ryat7NHQuoWm9hwS8uqEAtIMpYOsfevQ0gIgB3aNGs/PvcE81VUfa+v3mFdPLqa9MJHH/XPoQ1tJZfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11535}}, "0.1.2-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "14098e0b90d17766f553a84676e2933995feffa9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-SprvN9/3+S9UkIlmqzjfRxiCQ3A2AT5IZNOX2W2Rta4DRJ31UYnlFO7TcXqkajsCt/gbIOtv5RKK5YM2X+5qww==", "signatures": [{"sig": "MEQCIGZUcxTPLFgwTTN4x439KJ5wxMkt7SPls20ZhYgD2R1QAiAK7tvTQ+qEUdVVpLfs7OOFcLxt03GRvPKEKpMRIFAVxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiAiCRA9TVsSAnZWagAArRcP/32nzQkqFXTOs8F3ol7w\nQ+EkOOuIGj4hxQ2f/dZyV/QuFLS25xiBqhuaxDobNZmVB7+mWP6imj8fsqYN\n3cQRudqWMOnCA1N8mOTVArS+vDYcC6Wip+P65AkWvEAcema8ZRYX6QXkGzMx\nR82r43hwusVkkQ3FVwf9GJPbHWdtGxQi+Toi72ds+jQHfuFhFWfOTmuRBPss\nVIWMDrrQGJyk7v8DpTBQJkf/L0hqB2G2uM2b4lpZGDsZqHhbidaZp/vVNAqc\nZfgrPtw04Z6fnDsUvIRcCvl5Xr9+ve0Ni5NcT/22Hht53vgixhMjSH+aHuqo\niGTUaABVn6LmIoRKhOohYaxYcKjdu0pLnwpVbMK6wHSh1vsR3o44iFAf2khk\nWAVXsiOFc+9wG/mGsKSej5zy+PT8B0wyqdfttGw/ZH6qSexMxT3FNw9z3zod\nDPHr3+i0rJjaC+nu/MhBglHxECQ+oqDG/C4gTT6H6LZkworIyEV9G2/osCJa\nJ/Qd6WZaQM/9jtMcsfrIFcYg4rgpgrXbG7cCy0+iwLq5pKXLIJ/j9RGt3FwN\nWCd9JRG4f4yZ/uQ2lv3olSjjwtN1BHP+4Q9i6zaDQ5/Nd8WmR/COm73xv6Yt\n3QuP8AxwruXQ4pFKGTIvxN28GBgRrw/n604Rvjru08J0zVazoPeA0UiDhHif\n3wiV\r\n=iuxT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-primitive", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fda04990a58b5ad641275041a6f412e48c5aba91", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ZQMHud9RouC2mtV3r235bLvBLRYGfDW5xh3AvvddwuAZqT7kTbfmX78QJyCPUN2r91H6lkad37/qLUkslb/NMQ==", "signatures": [{"sig": "MEUCIBYExsMY2O6JdrgXYD0mKxa6D3DR8A3GwZ2BosQLVWY1AiEAq1ly1jAxaO01IWt7dF/0/GEzXMddfWmNZOsHCq6dERk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiORCRA9TVsSAnZWagAAtYAP/1DcT04kJGel8edg52sc\nEVR+dr/9rBKZoZlAFmI/cnga5VXTFO3DpcZJfB6P+JfhvAt2HfIa6B4Zg0wP\nUUQElU4xQuj/nQ4XsaZ/VomCBMTtNiLGnjSYnrvFam2wq/8Hz0AwSJVDCZpm\njc04HM5eXLI5P8hJUIqP3S6trGA1PoKlEcutD+x2UyTIGl4Mavr2Trr3XCTC\nyfySm/iPWiXteRWKXoJKaHbv81d8RmWHjVSDXeYJ2d9ACO0G+J0/x/uMZElI\npptT0Fhzc/rLz1bgc79ywiiS7P+MzLaOEC9P0I0TBXQ/jwhJ21NHiJ18vOhA\n0sIDD8aU6vvAx7/HqXdacEqz58ZvReTORjfIbyCr+oR842dyGraxve4v9q7H\nzucZwia8J6L6DQWS4VPAjxm8UFS3gwt3eXU7Pgwb9C1uGRRLf1kHYPDIuEsS\nxFgEAIubPSyTbd9oYMkwTZwbg5e9dH14qjHDTYHECTFqIrS30EqUVl/TCz/F\n+KnEYGoeBIqNLXwwsOCHOz46KJsBqa2DPUbHmCX2CPa2rcKppNiouGklG7qh\nFzPkqgqO33YayXswGf5ZCMCyQfNy3B/eY+eetXTIG1foUrKAb5vRLsDEJtq5\nWnHh1asY3EEecItIrFfxg1g3LJcdnZjnTmilHsQV9xlzYn7JCDca+KaRwWWk\nVvKD\r\n=PjzI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-primitive", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d9eca04e40f7e8c63c86aca425733ff2dd718276", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Z/gTLLBCup3JFF5VR0EfKgEeA1zNK7vyhus+qla4zIVVLtHttnUu0iOBTFmrlZK9OSGumzeMRypiDxWAtmlN2w==", "signatures": [{"sig": "MEQCIEDSTC6L8jLHxSVcWeMaqpn/aVB+zVdDwCjCjfmilHKAAiAo+3t/VX05rOnETVTKbj71aYWUvvrABauvc9LZCe7yZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjTCRA9TVsSAnZWagAAYXYP/ROy1fG615pYgZuTHysB\nRAbX0rVODuFVRQDuLXlErNcYpRYTuJTYSk1+hQkB/1R2yuq0fGqgZO/omrRa\nk1VL+0ptGMXowMnfpHTLSe29AgTSDo0gOhZcnEvKu1ot264HPzKfU3ZAFVJ6\nQCh9LYI0RHzl1FWEODKhIKzbnORwsIEY50245fh3Cwo3NHltrRnDaqzfdE11\nL+vTaAa379yKQMP6FKz+Je5puAhiMHrSetWNLvzMgZlyHk72llfG2+n/sy54\nCaNwVvW/X3ET5+gseQ4tYSYev9jg25R5sCTuqfys648cSA25lRC34G27nJ9q\nM5RZEiRk5Wzb3bZYskHPxdjKOH/nf/MVTFEqpu0bV4+A6YUl5RzkmOujAAUG\nhBCdHIIcOxl2aO3dT8MJWPLME+H58Q9kLOWJ6eLIARPRyD2nseYnnmzxDKYo\nJpAGnBVP1mNvmovCG368qVTVHZrwPM09WY1Sa0YEzG6jUojg3Gyx62GTGGKS\nXNPb0YZSKw9HMNd36gE9mKlJvKEDX8ckjwiGJWBJx4bMu9zrGIdH2dIGG+DU\nS1W3EaZ9B3HGYPl3MzoqBGyB6KSvR0ZDXVHIIZb9gGawqYduIS+PG+msUPF3\nfgzh4AwQ8zhEfl/oYXluxXGhL844Zr3QoTLSFbpCAB7hVATOhs86gTVknknz\nr0e2\r\n=bKpf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-primitive", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.4"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0c95e076d83d28c9fad1074981566f2ccdf89d96", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-2yxQsqpgdhZcgBC+3PHSYvaA7R9YTgylky8SwtfEp/9nlj7MGoizgSBrXagi3CKHCy3JTbDcMaj/Q11tw2AblA==", "signatures": [{"sig": "MEUCIQDw3xdE5WBracP9ota2pG/EuUwofQcxMxT0A27YFeXJAQIgUaXFalSOl7IeSunOGkmV8ICJ/NU38GevcQU745O/rUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRbCRA9TVsSAnZWagAAUwUQAI1eGj5WwvORBtldIPpb\nAsCY+BJE71TuTxEO8AtzCcY5ttvMkn1hpqR+nliNj6eDJaC9aZUYCc2QEFhF\n+sjefiS4fLLeJOLL69MoGxsfWi72rx8zPb5Eo5Z/jO6aTTisRi1X5j7U0vyt\nC8zjyUp3d9ZinDaIaRxxGy/4OqIcPZu7i2nNWJZYgew4j7RjqoFbwO6oNXUa\nA4B/Zpb4fpOwYFS//3HA9uUQqf3EY9R28JVmzKg9ig/GSK6/tzDxo26GVlCH\n9ds178vHLoXjsB5TJgGcdi1Wzm09qY4FIZ+4IH/ExZVlYadEgUtuyJ/sgBf8\nlvT5cz6SEezoqSzoijYwDH/dOJgwkW6ov8fzpX5X1sp0fZC5RfQL/E4TL25D\nGeVWtbV53WY1Pn22AjOpgoVkZQnYimJYBhwCQIfevEZLZ1lueGgdK5YlXHlC\nZF7orFEm2+GE21wse0BeWHUGY7z7AQm+3J5+F1NxRHqm31uLZWloERlq0W/o\n7rWSsGQD6+XQK5XteeRmKJGhYKXwY30+/jenmcyNqjnCtNElixm5LwoirNfB\njZv9V/y5iNVc9p3dm2GCvqw3I/hHjW361m6/pbYgoO7kCjXvvyGNDC1Br2C6\n7J1LvIaiyv22RlP6UtrXl/MzvOT/IpJtyfBf41EaPauZe5S2V9SSZMw3Il42\neRNL\r\n=HeB1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-primitive", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2-rc.5"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8e301e0c2f8deea403e88b73a2c4e66a963a6651", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-+NK8S0zxOccXSdjhRzogtcfbgnJV4uIf2nGnUFTv+mZmMlO5UqhISUJdl59ocVcP009aLG7AF38inCOZpmiU3A==", "signatures": [{"sig": "MEYCIQCyWAJdsgo18LLf6xYqfQBTcypxRzQjyZTCTC7mi1NmbQIhAND92FbdjgtXQgfwaQKgVnTnn8IyciGMh7Kqzqxp7CLv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr42uCRA9TVsSAnZWagAAGGQP/RZg/yFhqwZ5uRy3x9o3\n+S+pAGDMBAGdD3iyFsaz5f8N1ujjGp/JaSIKnStCw20pXpULaTALMm169lRe\n+Mb1gwgI91CfY6me9xePEkf51g4vqzGYc7lVd8OQGYbR2xAK9Xx+iHbdIP98\nu6Ddc9x7AR/wTpxEjiwjF29R/EQ2KNRlMPhKEhSybby5vBfeCYlTjRznuive\nEmLSdPbCJJIRHSrmeBbIyRawXl2FD01twmOKkcAQRZza6a62bKH1x2aEi3el\nijeZRgb8Gbo7N1REELWVJGPjQDkZt6ZW7x3hBVfs395xj7gK9MeQZJdPs15/\ntPT3DqGVM3B+k97YSxkgtlRlE8pF7ZixFkJGfNBSwP4u07GWnKxM83Lk2M29\n6JABbkkis1d9KTtil8tZ/pTjfewxLHSgcp8KKUYSeRPYnKMUATEIOIywg7+9\nI4MqRvjLL4xy3mrNrdTzwMH0ktNer5Bt4ITLGn5y6lMMGWoX6twZA3d+MiTZ\nT9Uk5Uwp0zN4diok9D/UE6sQfeOLRFZ1sfvzlP6sBYVLGSb/zz6cVb3HL2X/\nkeY/YYUWebKr4gAoOIdcHX6bBng+exs0fR/OAdDAi3Twr8qG6j2qIuFuZH5w\n+YTJouCdwDFq0S3M2yajTyYC6db/FXAvMPZ2+xbNOMMKk5YxafW0Lw9OHEpL\noFze\r\n=FMtX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-primitive", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ca20fb15fc83124eead186333f917145e5e53378", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-mVgeBkuNRZRCzHuDm2DWjZEIs3ntp4m3GtKWPXUn+SgmJXIIpVLt7KhvEmNkgXURq/DJgxG9GmJJMXkACioH/A==", "signatures": [{"sig": "MEYCIQCqk7rJsoHxni2gf7cLE0tnh0Nuz7BqZTCfvw5bWQBGPwIhAOsYCnjYWCZ1gpy20rYsu69yBJaiIFfsCWjHy28hcz8A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDzCRA9TVsSAnZWagAAQjoQAKJVHKaYCCwIOiJO2o9t\nxHa5nwcnFvZpVCqYQsPRpdjgxAtKnYRK3rpDa9JfAcRvJYTd8jXclSl3GQwJ\nm+D/eCYtD+PvDyHBt0fvCpVVHgVxsyJY1p0ZbCVwoXdwmsVTdjr4a6MEvJ3i\nE9bw+zkEtJUG4AfQ4HOMsGVC5HQAqoIfAv5nOqD5X2oxQke+bNnclOP0ZrfR\nz0kTMes2hWqC/I185UqSv1rrVZRsCn+YGp0WT4kStHqOutog7mG+Hlx1HQtY\nyc/fyZYATTNgL8yhvmd6RcE/R9m7U0Gj93GyAkYixq+a1G2blOXxbRyNWNV4\noTaDqWyF00U979vrm3+zyK21jBo0shqPgCOTqHjYCxnnmnwbuZBirmugbmXF\nlLUouItAg+O0u2zF/O5M7XhIA7vQMFc97IORMKTA7LMpnUIw3mkWM0sG8Tlg\n2N3eq/N1F9/HL6H+i+yUasWw/uYp86oWunugdpVJeVihfY7cgrmWH4A99Pcp\nVjHmoYEpfuwwNAym8zdq38cnzt7YLwAulP885HFoSg45c0BaK9Ecydy+KK+u\nCazhx50UANrcjz2HcWX1vAzY+exuRv3r7Z9+mixmLwHcoqarGvh/o1wUwdHn\nRGj5xTprVL4tMRcKE/AN9qTdJz1xIA6tUEz5sQ2h6/9MtrEHnmq1JrzxeCbS\nXWMS\r\n=qQob\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-primitive", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "585c35ef2ec06bab0ea9e0fc5c916e556661b881", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-fcyADaaAx2jdqEDLsTs6aX50S3L1c9K9CC6XMpJpuXFJCU4n9PGTFDZRtY2gAoXXoRCPIBsklCopSmGb6SsDjQ==", "signatures": [{"sig": "MEUCIFh+Xfb2FfaZVMK0j6IBmhlFHnMvUszmJBoVN1gP+LUaAiEArtVlM+QvMcvP01tqwXwROzLCTNFW2SPlts8Z0VLcpZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLioCRA9TVsSAnZWagAAJ7AP/iIAlTf5p8Gpv42g/71H\nyJw/Sfsv+XRybHg+Z/vICm1LxyckvjB3OAgzKhZ8g20heENSiwGE4TELgNK1\nfB/sbBG+fDnrbtAKG6C3suL4TkKqrHipCE/ludW6ZOrSKkzu8MC8T6weUkXl\nyjlKAfoY8AzSD4QP+a7KTJsd1NghOFRGt8qqJpigM+5wozSf8cyNE7Dqbzds\n01ASJ9LctBojosh6qCrOeHqSwbIM7fhuiNHM8NhLDgItWuLavv9k9mpXDCFk\n0UbWug4e3p2ZSUtBfKurkrjhNS64rt7m2Bk9a/2U3POSnQEaQtSEB/lL1xjC\n8b4CT6/eib7oqmKQAJOnCURsVxYmZqmLxs3lG6fVQ8U1xdmiHxGiyVMN6aMh\nOKkCsUg+stPpJX+ZYZwwTIFXN24sDFG+ws4LcQFTTbAgmV/OrUr+5VGnB/pL\nFoUtsj9G2xsRhwwWIrZwiqCPWmGOc8UcgZqzrptHcTRrEvsB0tDdy6DnP7+G\nsASgvTSzKej1/oag9a/CeSK8HEnEWKAmVolAJG7+lrQAhIKU3ZBTh8PiqZoE\n0km5DgpGHYJ1KHW+0CzgPJByopdhISOqzJB1EVy8E1ilcBKinCNyCQZm6lSj\nWjGZ/MtYtXs6LnYzWfFtiaCCN3uVbHPnWuju0F63vh+vrvUWLWVgsQVgGSNN\nCRG6\r\n=WLV4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ee3ffc75fcd692796314666171536d69cd72d58c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-tSU9kofNP+5eH1ZJEDcFgYN4m+4X1Q6I3Uh+AaJSqCog4XdTJzq7XBFFCeh3MV/wGYcJUJIFwgMbK5iXA3+saw==", "signatures": [{"sig": "MEUCICIvIolVtxwqWVi2DPVYsXpkB86Y/g2Nspi/EHe3GUhEAiEAlDhvhUZfwaOS8ZHJ0tzSGZjiUYVAhNFDqP5DxT1Wwmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLjlCRA9TVsSAnZWagAAmPMP+gI7FD56m3TsVdDeFX+q\nHVgz5CJou42Vs2xCEMSbktaoNyPok+JkyM2IO9ilmpBl2e+bZu64sHbXGICb\n9GM6bNCzr4fBrzJMK6Lnox3m8hT2VhaDt/nh2K8sFWEMDLfghQ0EqY2sq7UV\nVbjgcL0GASsDB0+XWgdjNta2hjIvC/rESdtNV4aPtcZYCZnz14ZZrZ5Lvp3M\nf8Q/Y/AVIz3nq6IE22b3wDsfe9sIeDKZe/YmTX7CvaaS24EX8L9oW/SSHdIk\nX3uBBGPYsay27t5WWv+eCPGwHUhNcZcxh5bZp+rjlknhTnheP35lXJnY9tAM\nF/KOruhGuGQ9WqBZ8yrxcG4L+FWTX5Ues8GlBBD0scREVj5dwE7Bxbg2sSwe\narWbpgB3O0PW2qmngk+S9BnEtc+KZabwEqlG8h2QEFA5kvCWgODy2zg39n0r\nTZqQL/Ol/oapLpWCbTTcoGznVNhqIsHXIV8G3YKJq73x6lSWI5EIrXVK7gkN\nvZIsMYqvaNVTwJaMZPZEXQQT5YFaMyWM8yIUnUQgG8vAEMNLhYjSMQiGatVn\ndVt/YxMTUehvmO+tqcJAyBhkt047tLF5umh6VlNICRlqE50hsy+qs3yqIZq4\nFfCRvQA1sgXVtpcFD7YwLzEt2CsOhqyT/iYvI0J5Nh4Q5c1vUuBBjvuB4+25\n+Oob\r\n=Qfy3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "55fbb17639995e1b2dc54dd3326bbd55976daa20", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Rj27BtMBuAYxJwK+2/WyJ6sB5BxroGatTH/v5uvembsT7aI06OZw9XR/v/Kd3v+xQ6p8XOLji8F2lBparlPcRw==", "signatures": [{"sig": "MEYCIQD9r3sRWMr8y1+GoQX/KoD7uoFb+dagVIJFyjnptYxS/AIhAIluNr+lA1pH2rzRQMqqSh+nvsyU3/9y0RLH3am3prFU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rKCRA9TVsSAnZWagAAgdkP/1KzGW9SBnLRHmvKJiSl\na+9etaeapc0JcSWiK0Pp+7Ex3R4CHajzGTnGkYByTZiFtqeIhWhDYsIl9dEl\npM8z2mORknE7pkZwkT0A+2K9pjLNP7Fs/75kLgJF35oGlcNVOk2VJ6CbkdCV\nehAiN+qQISWUMhCReB61+Z7kWA2vh90SauWFaAeNkMHG7k3vsOkZxgNP0skM\nWP/qq7rlkprCmk/Q9L7IxiqCSI+aaDiyHg0RZlfeoMuTnL0e1QmG9C6UiF4G\n4rWR7+7bBj2w0iWv520Tt56UzCNnDo1jlEzXP+g0xBOiwfUL8ZQThAr+LtTz\nnYHDpDFDgWtRt1FZY1Gj9JY4rJRoXRzuCcfFY8MUFyBoca918DRksquou/E4\nBd2lcI62Z/56oV/eZhXZBcuEZYqtKazGYD4Qpbt14I8cZ3VTXL4Ftqtr1lw0\nodz7nuvELG/TPvtH5+h8fH5uoUBd9eoGeIpldYAiI0+BfmLytQfHryt7TUKm\noWuayjDzYB0rpiyRzeidr4nUC+TQQoCWwxisTD1Z2S0tEtwLIutIJ8JyB4r+\nVpNvmG+CpDZW/Dlk0CcXxMmyxJFAkZlXMW3B1rpMKylwiEP4yeSW17fzMNY4\nGp/B/3wXiehKeFhspR7M5g33cTkaTRv7HJVYDZBnqdViAjuVEoYYj3cTfcoI\n9Eic\r\n=yiA2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "19ba0009128fe7201a723d2d44108e15ee8981ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-nJ02xJdbX27+ifaMqPpJV3YGt27ocj0My8/Td7RCX/LIcTQrKAmwFL0gAAEWXlqCd9cZxL+1AwSfITvK3Qd5tA==", "signatures": [{"sig": "MEQCIHwoyHvsqM5d4HIehujuTNCjGe6g7creyExy9Itu+baTAiACODG2JCgrTfE8zW9IHTQeQxoW4A2DSIOUYKgA/FEv6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEDCRA9TVsSAnZWagAA4oYP/j6RvYSDbVRvAFX/ed9g\nKkvpbqbqT1M8DoIu314t6phKWzJYhJQ7751V0JdvyaJzSkj1ZHL7Dyrj1jfL\nfceKx4sGYgKSL4QXWDdOhMw8RrjRBRIsImHpD7YC1DlyJ2B4fgeJEDCcixUK\nZyidjA22XCSV46m/HtEv8uMfxsRLWTygU30jlud1/+b60ya3fyN1zvj/WwEU\nXlAYcD8wF9AepHRZAOTlJzO626tPqzp489Y4I6EpEM3sFe4vv/XpOA0t6Yhy\n/t6g7dCY5b9SCf3++bM+YmusApp4GKPqLEHJFYGbaKwHS5KaHIXKmNvmaDU0\nIF/i/l1/ZBUeteYEIRl35gjOCeNZrEsflfbrtjXpkegB3zK3OBUjy67BS7IN\nrEp1C/3tj3TqmY/TUkws93NXYv28qpXxGoY3Z19YQWXM+g7Yvy0svnYYteHj\n/UmhCZiaWpZdv9/YHyp7fY5tn6ErfnvHLQhnOIf72M9CWZKG6m2ZPVb2ELch\nalONf/IQyRiWsrrIMFHkINrKI8dWzI59UNxJ5FnCSDrApp8iLyJh54hLJqDT\nmsO2cE1glJng/daTbd3Eb38+2A95wm0S1DCZ9Rmxg5BxtV9GIf9VGulfxU6N\n48Rn++4zimDCz1PxAeKWxdAGJ2uWPQA5EaeF+pGEE9mcySNE6WlGyjR60iLr\nMFaq\r\n=1z3k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8c3ceff2cc5b3f1b6e52c2ec8f0ee6baac395c15", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-d//2+Ky3tU/grE5iFs2on55yH2tmRJ6PmVIKUOKLnmdWQxGvvnGwe8MAp8wdx13H+Rn9zQNWsjegVahvNAna6g==", "signatures": [{"sig": "MEYCIQCX8p/tx76szKEnVPIzO/9Pcm+sjnaqra9SmEBbqDm44QIhAL4wn71KHbfakefm6dqlRsTbmESHV7YX9mHYhjBAgHh+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CmmCRA9TVsSAnZWagAAF60P/1NA6w3ZMFIRgboxhc5T\ndtF190ASMBXVvqd0xbVsIXZam40wZuPeRbcJlm9N41v6XFeMiQ/ch93HPPB8\nArqI3eVSoJHHG/ZG4k9El1OhBIZNUKBUysz1/eXZlAi7qASMy81P7BwtCSOv\nkChv5tEErkOaS+faQFLI2NOCRvZKhtaa2zBFu7X1bw1pu1fTTr9zWOVeZesU\nTxoUWZZRP/PZ8hEtOsl0LNAt0ItR1vdmCbjg6Gk8KwD62Rlo08U+CAU3l33z\n+3YPlQg8eO4UDruXoyFL6ImpmVelqfqY4NjOc/MBfSMuej0ORbtracqWMPnA\npSI+XrHqu8x9OtijiPdyNzFjSMZQSeC07NTgUWn3vHK6hMbgWwn2RMMqCcxI\nRn9hanAnKktHLilPrbLJkze7j0+ek9QMWZxOGSUzPL0edVgOPkbQuSKb+q3L\nZy+koYK92sU3RjKR7A2Iqbqxv1hkkcP/q8YnBwy5LOAorSYNpSHOJaLmM5Rp\noawPtIy893+EiC6Q56+J1mtk42GpYY/Bl+oSR9zaxpw/habXcBVPKcD2csKR\nX/BuylbHNx3s9NiK1zIVTfuq580H81Efu5F1SKhRgFFFM+9YNMiXEXBrsHbM\nJgLWBAOuzttcB9cThEDSf2j/1n0aFT0M9+4fXnnHspjiRKgHkZ5eH0D1t+cn\nodFT\r\n=WGkI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fd75e5659acdd8d00a99076a301ba4b640701bdb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-TSe131XwulyHyWCP1lIkJ6W0l4yeegIDkzORgkrWQnJ+C87VL/ciM5qMsL6iYdKo19w+Z1cSiiXWW5hJlCi/1A==", "signatures": [{"sig": "MEUCIQDzhq9glc3mi11xzX9sFKmA93KnHQAlBuhY5UKFSMW6GwIgJfeFIaE7DHuzWNVOMEZ2THxjNjHEP1gJzw63RK+q7F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GqfCRA9TVsSAnZWagAAaO0QAJcHghFXR3/FhIT88+6Y\nn6ZCo7z8WTdnDMkQt7StuTr/W9uCG+XVTZBdAYq1Xho4mpLTZFcYQ9iGaI4B\n4oaRWjrofOTc6Tz/QVIrGf0QwQ7cBErPtMmeRwCpWZ9ABQDJtieL1Jw8Ho/J\nGhv4vasQ4XhASycxnPnta+bVdH14DVDlS6+fHWc+p79PqdmqwEe4JnJ1wy9q\ncnNroiTl1yQUq14RfCUHVoAH/sb/kkIwQ953R4NUYfip0M3sjnDQMjiDPwWV\nVOPFR5Jgl/DurJNTGx2kbFXKPjrBYbUWhVpBITAZJdAgWVJwWGHEvi/jj2rd\nDn9xwUy3LZW75+eazli3Y+bC93sgZ/TaGtnOt6Oys980zneVkUr32+WSbx7Z\n2MJZDwKbLXaB6+dWAhkYm6intObJ06vXp/Ffy2b9UgXFmHxxaKi5yE1oGG2H\nW0cnIHkVQYqsM8O85gyUNaEcl+m6JcaIDhE/OA9L/D4QmavKZ43IOSmizgMo\ng8J+M98kcn7cWvrW+5p8zC71Un4STs2XbH2UsGB8pPrMS2mbBOR0moSDm4Ni\nsgz7XYMOZUoq7jZF4qL4Dfy/0tRJX+W66tlbgscJsYrXBElb+6+jPBhS6c0v\n8HSh0HDExuNbTja1ETbvncboJnGsjlMXC3/GUMewa3VzV19Gyg3TXAz5dNws\nBV4N\r\n=QT1M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3caa3763eb1a4274502402b28e2224ad5911d366", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-alecE2kpcXozxFyHEh6mRu0w0gUi9LWlXa3JRD8Xn4bBacBe8UB4TZzMcF2qi0Bk//AiQoAe/Oexh/86DfcZag==", "signatures": [{"sig": "MEUCIQD8js1+uMjDrsrcZ/8DwbKK7/Kymi6NspmK19TKnMYQsAIgf2FnUHCiXeurZObfy84qy7KUzT843y+FCEhmwTqmcKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZcaCRA9TVsSAnZWagAAuuEP/1V4dAu8KtM2o8Y9A96s\n7zMpHxl9szDORItSnDM+ybEsJ2um0qXeNpVWKkn5J17p3cAU2yRASCbfQ/sO\nJY/La/JwIVJ6H/rkk4PAhNigzizZWC47Ukr5N/M5ZmqKVTzX7SBp7elkERUz\nvlfkZFf334oE5AQVGPn3B+GA2E3ao4srdeqwOoc0MoXxrlByulNUFNJk3i+6\nLgmewhAYM/wtMUn50BAygEw9X1JpFN/IBxhss6wvf4+F3jTjeWQGBn3GKD3S\nrQsPvtNk4liik++9KSz4LnDSJ4zG57ZGTRATRbsHnzhxrw44AFTwhjt6UgQf\nk+8IltnrpvqQaQrvgwVtObLKrKuze74R83aOQwNJd3afrUBctLFOjKvbmpiC\n8UJFKFNdFx3zARKVNiLpNYkOxkcFO5PAIMDLLYQ1PPHvLMwWgOrLb1p2Rdq5\nQ1fwRT5FNVvR8Rw9mZDPhCyopk5I9N1XiF4LpsjTPH8pWpw1TPV7wcQ7i8t3\nb7NOKsxFAA3tvGOSIl+bqH6dHSldoPIi+WAxkHL2WTBgKmv1d49f/zwBMVD2\nixAADqVcPqFYRottz1cpUqZp1Ivv6i5rUoqMulfNFwQwWu29MZst8Q5xWqIC\niRlqOvhI2gpThkNx2bmyKVOQJ7A+PvFfOJZYOzPCGhJkVz2NYo+3SB0nv/ik\nUSvD\r\n=WJ5I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "af28a278dc2354112e393a16e50be7bfb51ec63e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-EZA3jgXoF6gQVXc2rshnWaioMZiwawnzcI19PuPoaUWd231WtE0XJiMU/W89bMbbQJ0sfeoH5xowde/I1MBEgA==", "signatures": [{"sig": "MEUCIHylNK4wNyGIvNZ2XEn89jbz2j/JrV75R8wipnG3cOj7AiEA07IlqoQEUCdJ/h0NcQRZuqUz+PiVUtiS81kz/vliwR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6Ys5CRA9TVsSAnZWagAAZvgP/R8Z1C8VUnaBhHYHL3nl\nm8jfrO1J/uyaFHkg7dj81BqwT8vHlHQbMFMdoRYt/gzz80wfASsBR0LS0KFc\nECqnmklSvf7ip7flslxV54cIlvPrOAR9np1CzHVnBUQxKZtLy5sYJDvUpEGW\nHdAkOwymhKaEDI+BY+ZSoQ+CnafjwDWACRjcMNsxsEVQCv9fT9Ftc6mJmWPU\nxYmnFEBkj1ySNrDFAdzC5i+G7IVYi7550DbcckSSr+4RfEIebaA86/Lk367c\nAiclUorQGfAvIXHDmY2PWlo0yfaGH/YtDz8jiNB90AOWIt9H8SpeYQ3ny21r\n1Fp/6MWFF0F6XVu8jGSz5ivz6TGzIADoXVo3cDTfgSffGsmIPaF+pj81l0v1\nN6SGgGILVC1mT1hQhSS7Vg8LPXEMFLmBblNQWaaSiFQvUhpDRhci1F2emBSh\nv4dHomOfn4X2P3TsuQbrfHHoukcBqlNdykdIuhMTEMKzJLdpkkW2Ru7oivO5\nxag6RTHEw99o8oWVMEtOG4IolITqbg9dlOwe5oNkdbcvf+7AK7wR4z9eQavB\nf9VhdBORQVVlwWokHilQ2GLhUO3LucgYFmB7Lp8AtUlDxs8gizOb/baRP674\nR2tcd87B5cL4nveVMO8GstRBrkx2fAvM7eU+pJnn4dVpqVg+T06YI2ZcA85Q\nHGtR\r\n=xgGU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9cd289a31309c2f75ceadb9db7538ea512ee33db", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-hHfsPtJCzROH7uNdjg9BwSBHCNSFzg/fqNQOKDfQ68ybhl9F+uD1x5+pNMDdnB0dzY9tEqTVTJ+HxRmBNh+v2Q==", "signatures": [{"sig": "MEUCIExPh9MHj/xgUvGYR98NsBuJFASKxNwyQvOET2W1omolAiEAx1mt1mjvllKcpFHXyFLuceXRyDpHrfJ7KPaLSx4CRK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdVCRA9TVsSAnZWagAAPIgP/RBPcsYk4V9gnsRaXsTA\n1cjIy12SnhVDPwch6EJKHiy/qfu2SAb08PslnFpvb64q6EssQZ3Ijuh52l5c\nMP2IJxidVstj6ejUpKDNcMDTiyVSRzCfdkkl73JnZT+chTA7GSJX4/4dIOXu\nXSxv5jDAWSBEh6386+Cih2TlbPStfdHzFvvnXzJFbHcNMDomylGb/CNJmErg\nMf4iLSHp6TdEH1vZsoRqnRfleG3pwZHCYuPhQIFbP92ViZB0Ghqnu7BGrNCz\nSED0Z/4bfhooB/UhHdlAUNBzc5ofPIYgNiBMVlbGibOFKW+tTPjwYXWgOg2b\nPiUMBiiRPsQJqDK19tTC6RujfoITKGlV1uwO2GdX/zntDkjXpVfie7qZ3Rbv\ni+qiJS5EUv9qtm6fqe5ZFHqWk63HZl5guNi4qRgZmlzazQudg0kKYZ/9m1AW\nHN1BayyJGDr3pgRgY+iEq/JZnpKeDHRZjxNzTczPHFF8wVarNeTscNQxQLho\n/We+ajoi3UqzgWkV4yjdv29mf6YNjUvom1GvvXP+rRLxvpD6SDrTv5xLQ53i\nf/08JOAIVq5Peezj1oyks6P2OCSYciSx6gLI+7N0t5DjKvFpJYqlzXRL/Gd3\n4I4+Y782sJlbNG/NrI7TndtjUOOdX1qWAOpm5Qgpo4JX7QLZS8WrSWqktg+Z\npFxN\r\n=yD5h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4eeba36b7bdaeb04f8a5e63c799c1946e50b2283", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-v0ecra4IdLY/OOls35VS+dRBYOsnSJz0+NvNY50kL8+A6UgxsYn4SAON1wRFNv2w7aWOlBDCw3RNsm4x0oUHDA==", "signatures": [{"sig": "MEQCIFeH8pZPI9FgwuO4vhCFpxynZWKqAMMStAIda9/k5xZcAiAeny1LCzCkb3Nor5UXQ44R8rWhZMKOzyHlGN0nYT7Egw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDUCRA9TVsSAnZWagAAWeEP/3eT460ToxDf8hb8Rrn8\n69edYwsHWcisWUNZX1uAmgtksvOx/1DRm2Q63FN75UYlI+lIAyW+01RYHmhW\nqR5qQiVYWyvp0EUvV1fosNEtO55yRN4QbiB6O6xMFu7tU64QFEyAHp6HsjIr\np3/U3BOqw4uIVAZw6UmGzEvv87iJzqr/fckWRwoBtHg2/RTqCGva1HiYMcTQ\nYHeEaFQcaekDOoBebXowK7YhH59EMFr3uuGo9SlE9I3qL6X9RalConIhtJfu\nopYRvM9HYvm7eKqxSpjQkC79T8gOps5yzArO/n1O9VYas61BYv7SASO4w//+\ndkgyVC89b6yB6fxrLmKNqB2Kio6bsz6NkRDICv7Q78JpSGHyo6D3YW8EBT7c\nm0rG7GsDB4xQcc06360CDjc+enu4uFf/Msv978BZXL3J7yBKTszJiO1+yIeK\nB7QSimMFsekhfmIDBRj6jK+aPws4UG6nyZDnBSPZuwQtRFCzFJQ2DxYxZ7HK\nrkXp2OQL3S0wno4sXTZI7UqG7Tacnr7dSRb6d4xj5krJuOIOs7fIGHplNwlM\n8WugrmuHLpFkrd5hc5lAKvtr8r4jz/E9yO9Z6N//kvtqa5ca/PnBsxt26U2U\nDMc+r3FOs0aBQMux1XVpdWYnlSwrQ6UcTpKQfH3sXhHJIv45jkWjRlud90dd\no9NM\r\n=4HIe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bd85a41e1ab92c3066890676519d3f508b4a3e5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-oXJB9H00eZxj/GihM7R4d7aZCx8ezxndN6odX5YoITiztzSD5/oehtm+wt2cbTVGrhCqC5hOq0CrYTd80/livg==", "signatures": [{"sig": "MEUCIQDWqXPD6fhDZfZo60BlE72U+5LWzlZblvziE98iXL9NkQIgB27+G79hrfGJpQ33lioTsFuoE1+O94YYWYSZt3E4/ZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLSCRA9TVsSAnZWagAAbWcP+wV191xH4ujUjdyybBfP\noDVgSRIQSalVO5c8BeNm1gM3lv347ZCiiNzTQXldFLHnxVe5O1Tj1f4Q7wXV\nd5qnfTMMBfD4IQ1n1kfy53pk+MT0EBpp5Lxo+8ID06ugmPW42t5Om1HfgIpV\nQ2KOWdYlQof2w39OOtIVBUbgEUdxHCaoQem0wN1CUBFuOUBM787ypW2r4Dw+\nJObYRaGMm5427sbAV8mWkh+eMIDqU0ju+1NkkDLjW/4eOh2Jox339kidC7qX\nUgT8XNCo9jXSbYvkux4NQSKQ/B7Z6I+7e0QkjikaRIbi1/vetvYMrOt9oCMt\nD6WGbR4vRXjuPJiudUAYxj/6Z2LS26065WGBJvUL/SYdAP2ZNCtRhx9hx+cb\nhHxG4Cg1W0/eq6fQZge2bT+evYUTj0yqhuccFwud2MPA8B3mNdJ1NPI9Kme3\na2nSTFVqQEqw18Sms84NFYumNFdw7Nr7skSd082J9TXq9S4gwZ6Z1ekUXmx2\nebj9wKcGHm39u0MfKw9d6o9QyqRwn2Y25KhsQ5qw+BfJJdG1nqUNQp17oa0Q\n4g+gRad1H/cNN13HxflUnTg6BcOHKRQhAKx0poPzaYLYeby91Ie1Q4MRP2Jl\n5DaeUU2+sBFh2pDw+YkMxltRm9cWtW8dtPi++p97bPa8b2v8YDtrplcPNWvp\nEhtd\r\n=voh0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81b68a36d0e30bf328802c12e156c504501d4077", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-yx66mjfJbYTSrAC9NkAfFCBDwlj1a2X2Y+U7ttBsrmVo3wXkKYevQNCXkFCpGSz9DfZBUxCDPMznfplVgzMc5Q==", "signatures": [{"sig": "MEUCIDWIqzLch6E48xaPNURzKysVKDNudpc6KjC85BevtRUwAiEA4U885fBVfYO+as4UB3ZpzvOsf0RG05R1IxzufWK14OQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0SCRA9TVsSAnZWagAAXegQAKSN19Sp5ryQ+UW1B7ee\nudbNrSNaSFfv8C3Uy1EzoVcz3NZm0FknG7xvd1HV2m64dsgP0WBWmDl8GrzT\niB+eDWr+J2GMGXidu379B7l5MnzwvhT9kk1wxoiaZyYMfUaM3tTXffabe846\ne2FYdx//4Ycxix+syeIsWsdf88V46ZhSiPwc4PpHX3CgDUnN0h80XGzKlXBR\nNFQ89TFceiYqr+huPRc8Rm68jDliOuH9mdXMQeZprxSI06tmhTc7ZW98/xuP\n4/kOMEiTbn4+HrZRdcbB9hweGXioW75yo6IWwgeiivUJwJY9SII7bBjZ4IEm\nhdUtCGQbXqiNunSh8yT8wx8CxwOwT1uExQUsW0utgt4RNoshDA9+UhbJfMfY\n11tiwfxch/UXjwf0OIa1sXJSnYw15G4JAVx+XJl0H4hjsJMA41D5A9ffyWbG\n2FmLT6k+IIFoY449OZxcIExSonrQTd+62z6jLjronTEr0yZwI691iRaXYW8G\nOa80rLtL3Zv9N8C3/7EbQACYedX4D+BkXZN0BJajRPeJBm6NvaP+c7j0Xfc0\n7YVfXKtX+EKbYjkpSQekZt7/wAjWnUoAoqTZ2vv5Kn9VN2Gx7LoclC5rOEdd\nH8ySjA/HOoPS1sqX8ymNFScX0CAA2N9HLSGVqX6MkiGyyoAP1RnLCtboDPn5\njucg\r\n=mq0G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "769c6635e57f2553aaf16998d87c40a9608188ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-QsLdgtYZ5RyO2LhsfJdw7X58WTGIncjmkhptfRaePLYIWW1WII6BVHOjKoPdRDJ/SmE7rjMg0DMIMNDCtl499w==", "signatures": [{"sig": "MEQCIBxjQbMbeapSeeqx1wMBPruv127X5wRn4S87yReEZeiNAiB9RnPM6t9yQm0j4CIlSfjS24IBY9UM/8jd5AhdWCTsSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SS/CRA9TVsSAnZWagAAEDsP/iVVf3ucoGm2+S2ZCS7q\nYc6lIAK1SCQk4/rt+VFMEYbBEQkiuahhNfainUeIR4tDf4bqnSyftzK0ltnP\nexUmnOwM+8TScmaXvV9wfxm6CWA9ai1MRtHxl6kwOe+eRKdqzePoiU8SPOh2\nx4Q6c7TnYhe6qqSDoECR3SAvJyQF4es+mgTmoI7gzSdPr4447malMVE88PVf\naUVxRaXe3vj6Ma3JtjHXB4NDRbC7m0zTXJ8qHtlC+1uDm7yYoVIqgrvE7FVK\nY48kERnMJ7ji4Inpgs033IJo35srxvIS8/IquLhfnY7Ajs2qcSmrf9ppibc/\n5FTfTWBzYYmSAWX3nJon603WiYMoZlOC+G7mkLtnif69ilc8QqGDjVRyVtBw\nNy1jOo2gZuItkWdS4G9gYbgaEoeqFj2EOT/cBAkmTUWecadjpa2HuT4nCZYA\nc5gDRCMVj93UK5io9mtlnxEvWe61yo05V6tTPIlPdOS8DB8bQFgKVbiK4chT\nw/1KgbycV0ckEIEx3sn0w1VTw1vwB9EfpovJSJ+iHWPkUpfEd3J7OioA9mfF\nEf6HFnJMsQAS47jB+iFTkG34SYZEz8YJBQlKsAkO8ZowEhCrzdV2XnPszc8n\nQUClxAWQx2YaYxC4iMbDF/48ZrgWFx6UH8mJzIQ2/80aXGG8xWHrVJ8Rk5Ah\nzTCi\r\n=mNZv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ccc6f071cbff754ac167277043e4c827bd29e67e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-owb<PERSON>44upefFO8aB68aXspQxPFEDbtt7P6G35DM3/zVKq9hxQ7421FU0IWC4NhawbB+i8OMMz2p7qq8mkzmzSpg==", "signatures": [{"sig": "MEUCIQD2aPGzp9cF/GV9i9/Q+B3LZyqZfHcWM85RLfGO8dw0NAIgVNe6WowwIv0J0WtrEN2hoP0EAgo+zgqSgkcQoYrjpEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZ5CRA9TVsSAnZWagAAM5kP/AmQH0DyYfUwcqHVQUun\nLD2l7xrfHHnDKMPKVfU21J1HRiYSAVv5MzdglGm/1dUTGw3Jl0hG5vZYOocy\nKouRE6eUZ2jmx1NjaujEyAW7TgM49fOwE1gtks7vHARUwbblzwhR9Abj+SO/\n0bhFwYOwsb0/siTwKZxGQeSjd9W1a5WWkNa1ZjgGYuq0PF2rD0EJVvAKFK/W\n+MQH2XaYP5TCFl1JdUe3BmJ1bbrwieOJkIJLSETFOxMLPt19jR008GfXkZ4J\nmJgpNqbOAF0bQxCSo/NL8Z8OeBhIE8lwiYV6Czu/pbOXFkcBnl03J45mwjBd\nSUHBA2bjbPwj0wVGdq4MuvV5VkvQg1XL4N/1uho+GmeKEE+91Lyj7bRMB3oY\nrIFsn526oni5E8CBUQ+lfEY6zKum69buEYMcCYVd6NydHXQo2yeuKeNknghD\nwEXlk6zkS1+oHKEoce9qwbG6B1Z+pz/vOGMLPnlNJxjTc6Wv3CfjU4O4oKQh\nzKqp2GxhSyppCp9d33/zP3e2PqPaIabxIWdLSPkVWpxBGAmw9gLti/tBVzoe\nU/Gfz0qncK1qUVhsorsMnrTNj7avCEAOikq4l9jskDuvgaqjFh7AohHGik7x\ngKgrtGcJihr0H16v3uGf8Iw6k6HWvS8cdczfqbadNYx4N7YGMYTWQ3557IYM\n0X11\r\n=UZcT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "25107d6b8b8573ccea91fbff5a8ae6e5a2aa32c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-KNcxkctskHfh+eIw+ytlyAbnuPM9WhtyvMhWAvMLShiLH0Hot9JYBA3008Z06H1MoquvsmpK32XQAQNj7l+o/Q==", "signatures": [{"sig": "MEUCIQDONHa1JWoxhCDdLVdpjbY2GdYstBQHP5a4QFcmEC3+9gIgG/D26hMcHMYL8IBAYrpEXMdXU8SLpY2Vg5vXpZCFGrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WoDCRA9TVsSAnZWagAAE4IQAJm8UuFTQuV2V6jz5s+2\nz9FaWEm8J/ENIyxvQqq1NRI1OActeusla1Mycnb62kTwUQegY63RZ3ponBrq\n4AqdmRhlVwiHXZCSiypPBpYy6JDmwvIvKPIYrsTWioJElsfYGCc0Mmli6mcJ\n7ABIYaErWNRyvA+KF77i0hM2z3Lw7U4RUzpyywx/TlDN1qRjJhOAiDhKKmDG\nmOB+AhNlecVAsb45yCrPBL8udBb/Inb6YO4arRmTREr8g7KkAJBYbHcpOO42\nrpYPQ3LsYQd4oy/y8vMbq0P1nZDXYEFGmlz3nMQIisR9ZFsqLFW1RBANKqnK\nfgViRskoiSfdBuAc31ZulJigJ/JNzEMcreqju5b1yknVy0fTez6n11yeLF/Z\nCH7Xj/YVqr45WzkndiPVq5X08NwVOnYDgXMXxD3IRn7i/TFKPOA7nluRcQnD\nDQ9uQAUqs2Kb0zjIzZ4xxCqXSjgnmqE1MAZOpEzFAqGTSioeYJ6ufGuGfYPL\nl0VCfeBR5I5ltvzJUmXy91locyrP5X0XShDOga/bwi2cAq3+15CYNUS9pfgY\nDJjaBmCnMNkb61QzOXUQw9jL9rzzHJeBjPMYHeAlMWs6PBKML81YXpUAYP1n\niFmWkNNxIaaM2mvNgJGF3ftQvARxdw5g3Wu4qB9EAFx5kTdMETi5szSZSOhz\nEMzU\r\n=jbh8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f6c71100a4da27f1c8f318434084c9d3f62695f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-uOPoP4zaI6n3tGuzaRf6Pre9EyItFncaaNNNdcNnmAXLA+0hTzOCEMA5H9cjZpX+WhnQdv6U2HeoQzCDvWCyIQ==", "signatures": [{"sig": "MEQCICToo/RU3iRbbveZQSMGbzt7/ocIWDykJAG9rhQEzILpAiAWiae/qGJJhwtapy7Tpm+I9e35ZHXpJqUFGoGRwL/oBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUfCRA9TVsSAnZWagAA7rUQAJa8soTShDZJhAU77EI0\n41jaLQCP6Zl2f6b137HxlKk6oBhoj42EuYr6yWPXr0pk1FDd594V75IEX22m\nU+T3KFHCXx8pWTTa+FvUoZtqXzRLfdYq7ZDn2HgZXCFqlzznWPBWI/KTho2q\nhWLR7XGOhKSvK16UW07tv4mEvir5nJZKCZrGtYPngz6+brvTlCFTa5mrEnTh\n1IcRTccmb4aUc2ma/tusAff0REa/OnXFGWtCvza+la7Q4pHWU5yQ4Q/aBSSG\ncPyHZMX+2NNtvQkmQioNieh6k4dB2CPrmCW+sd87ddsaKE6BqRCjrmheehr2\ne9R0tlegYeSIbZqK0UXTSE7YMG/6pWLk3jmSl3xjjJ+I6mvCPp2ZnGeM7uzr\n6ET1kNTha6gPFSAbMkbImJgs4AeWvNUG2XQY2iEp8hYPVRA9hCMYqRbxfcnx\nRQ9LSTYKhDWzjAbJ+gK7oneaJ/OnwNAqrLYqvrTpSGdVBLh6TmxOeXtx8X6J\nHDdduQHvm6M4Wq9r9Ta+7w3hBGXTlhI7MCzdav1yUwSKTnPzTXnSEqJZfQpC\nctIuMPnJVI6XjMoBs4g7CeFuTlWiRw2z6dyZzaXBegqvLyBEEAVMW0RHI2Mb\n61S4gy9vd0I90XmYMBTFfiUS458TiUQcLd9oCZw9BiRzrOecx6dasjcGfNae\nVRQf\r\n=w48Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "973569355879c8735e498ad6a61bbf53e8063757", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-gKToHjEibSAj+Uh9GL5fj6z13km639LLgk0Gfo1kPeTeMVlPf1Vd7jswUsn4ouJqFq9ZSelUiZC/tLP551bOEQ==", "signatures": [{"sig": "MEUCIHXvJaE3SzRhfJosUKmCp9KQzLqHgJjHdMeuq+Wj82lBAiEA+gqAHplsUYrrFSoDj2Y/imKTSHyRbWqZfK1NA42Tpc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/neCRA9TVsSAnZWagAAjBwP/i1hNo1M1I26fUeXuw+a\nkbT8Z9obpRPfeWI5bxEhLxI1mtGU2/NOBjNiclbkL0U9u6FoOOmS/Zo4ISHS\nVxeHjnLLu0cjBOQTeL0kkrF2nziMWwkD6KjtcZAjYcl/C57q1SiRhYmkjTez\nj4xOo/pr0vNmH+yOcKdzCYwQIKRP4yyp3l34lfNul2elT8xa7nGNqN2znfQX\nGAZg2a4nGzdMfCMaB2uBw8O1HC7a3A9NvbS1+OkiPUX3/4JV0eAe320v5yLb\no6UJKH/cbfuoTJYCUkCHvvYODm3kzTbi5q9LPjfMzdSO7d+0bmrAe4kAWeRP\nRXqeC8deyojhYXlegw4gOQvQ+vJx/W3/RqRgcLZuieUM6cuAwe8y9HEEtbM5\n2EdFn47viDO88Smt1IcWoPXAsdOlKv7Ulf8xTe+Ao+2/edU7cdV2CqOSZEee\nWg0fR5x0Ud6xNssxD0pY97UUpgCNAbX3HiWWfTJyDKthJn9qdv2Zzg1oa9d6\nP7i3RUQlbs5zAdEpbg16KJf+UFD6wmQulGqknm/+uPNnMnAqye2DJRYKmbm8\nbRNmc84LORDM/LYvWDYk4CXE9wli7kACV3zJQEqMI5cTjTNfydA+uv+/kiKO\nVGytP7jR6QZgq8Q6cyBUs52F91w72MkKRXjWL1Hk8wb76mDGubRZtsff9Jq3\nyFUi\r\n=agsM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "99740b5a3075e3738cd28d9dbc2f78c48d798cea", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-M/xVidfeO3GFhY1IHoS+QASS/742hGJ7Gbo5YmSFfrn4ZBVIwmMlbydmIGkcs06PFQxShpLptJHWN2X84xpWtQ==", "signatures": [{"sig": "MEQCICpeUWRE8rvCMDtI1Zge/bCZXbz6iEQ0bYedSBgvAsD8AiADchw3WlsgaVTnOR/i+J3jjOZBXpcON4DmvpxHRLmgqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBH/CRA9TVsSAnZWagAAgBoP/2yRAFdOBiqxyo15n2nQ\nmfst62lsvyuyzGDpMfhmmISHGocu5U+F001fE8EM00Ha23Hgf4Nb26dBiAaY\n3hMW2rUiselPIQF+tGEXYplOaVXzjiCF7OhoLVaWJL0dXJQT6mcMz9FvpuVl\nVu+xrDmURL+XL+DTYe491qF3i3+Qy4t21zR+Q6W6URVeufM6/O2QHRTbKrss\nWcUZSKgXxEyVlLSNJk9aZ4vvCnSGn8IBFj24KF5+o6dL/RTgXMrYpzHLn2Nb\nvChwjUowhJGwZoSJdBkt03nNwRksGCmsQDHB7PIH4fG1aHIi4GOuw0sNZOdc\nf7gWPqZlY1fjlg4sOGxs8vmmyLz7tBow4WpuxFOyZcWzviqiHqBTmtd8enfz\nFIojFApmXR6GrgMbRUJ/ggcIfveQxsVxSsZdbU3GmSaQZlkP620moon8sosY\nPIkCLvjhl1RAdPtb8RnV+ufdlz65n8zpB9S1yq+Tr3sOegUA3rpAw9ZK3FCP\nYkjKCOb/BLtuHy/b4QsAQPI/YtQoiZ2BydiIB02sloN+u26yUUs70EribGN7\niWMbUwItSwcVGNr438h4KPTXRhpMXvOrhGcltz2JrIVFn4E5by2WWalGfrnR\nLtydRsa6H8d+tm8G37DnEjiEt1/kLY7xlhuMs2cPhUVBTo50yZejKxbjF983\nBDsa\r\n=wVQc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fda31af46d1f427587314f451a9480d6269634d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-UJ2malm3/v3bdYz7eO3Lewjs3kOLDhAi293fpsdH31OtXuVyTKJw+8ZVtcsS48uk9nui8LlPVgdiB5Kjloh3Yw==", "signatures": [{"sig": "MEYCIQCjYLbyIP/N1s6iQVPpdD1HcGa98aKUvvhbA0oalKCjwAIhANhSB/EEIZYiX3zZORx7Xjd4EICrlNp/AorBq331/y/B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYLCRA9TVsSAnZWagAAWbQP/1tj+SoZ1LVezKV9jPed\n1Qpqqgz6T6fWTcjXuIpQchszLdqbL0ue4yCKDHbIQfbqvFys9IvRctlgPjg5\n6ZvKfOMds/Jh73T1TOusGTql+8V8ODJa5nEe+ZobGejFlScc9JDbUQD/9aJv\nj7HwaNWOf5BDoEPMYzwOeS5AhX/96LiyomWWrbghix9zNa6il+qaBmCo1S0n\nSqldym1HIvkyhcZjTES03u01KeCDIJ9G1RAAhy5ckGfZThIm1qK4oZBEYAGE\nOp60AwEaIcV+nFETwBir4PYwxDZrdBMW/5U1/YZwGBlN4dte6XJotNIg8uak\niCzf6CYXmKuzIYhttQrulLUgI9XP3IcnbE7Bkbp62iFK7JZWrwL9hJ3c0Xmb\nNa9wyJiwzq/ueICAqLAL/1cFiYnIMQNOkZ9ctF4+MDGul3x3xyDH/O3FEUmH\nVaKGRXVaLrgMRqWH+PEDzQQHaRshmS5EnTQ65ds9/8YEBQqekIHOA/qVQfO8\nGHFmqOt/h+RqzNNnW/NHFSQDHME0m1lxoja1xk5WrxoxysODTJ4QakjLMLH4\nQwrV4dnEpq3IKYl92IZ+6bDEwQV5VdfUIY1n8DPDcYSqwfNYPYU2mdHVI4o1\nkey0bPSVQV/Ogtbq3vkbHsapYLAP8kZYT6M6ZMCOjLZgmeeUU+4tkjpx1wJs\n9cRa\r\n=KpA4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad5b88554af4d0fa480a49013365d51361963c84", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-dSs5w4DTYZpK0ICph31qz6DJ0sXFkBMO8y2QMQPGUst2djwOxhs1nMkUO3f7pnBHgHQnWcseek65jwDGc9tkWA==", "signatures": [{"sig": "MEUCIBfl4PiCFPFc9OOHwGB6Hm+87VYj4Zjt4EX38eI17vw7AiEAnjvcj3SHD/LEwLs7Nu7JY31rBgS6+H/5JT+4EUv4nIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1pw//W8b5u8tgWeKzVB2NPiNSavItEY2iHh9fcOfUT9ASgRqITyMQ\r\nNtC5KovYaPjlguZQ3pRs9kAjESjBkFlbtnx5eWpQ/qRyncNx58n/9F+UtOj8\r\noli1oGk8WHbfI1WLiuSV3Mjyr5Y2ZFEsyzVJJroqLVo+FYcfnfo0t20Je/JK\r\n71vWHTH9+MfmFZJFTJcLUj5+cSXl6B3ATQSRpMQUKxNM4d6v1f+Q3vi3PRCm\r\nVClz7UeBjHZCFrscB4VWCEtnXBgDZCXmKS3i/pJZu3jRkodJ3k+l+VW7lKgc\r\nt8vscOOQB2GrEQQ71OvgPNVd0z6couW8Iiz+0U1cwmp+ZM10/kkXhVhRpp/c\r\nbnI/3sBsQxETtB9CYezQQt7NTG+x5etpZy5crr6NijaPSWg/XD1P6auUYxK2\r\n4vUzasw4CnY6WZ2JKoWA4y3JPaY/n3/5y03IMuhvl+VUIPUJDq+5x5Fl4Obh\r\n+MWzSZQxOUDrIC9vtnG8OkSjqreWR0qxCanykmyJQ5vIU+SoAk5uDL12Rjn9\r\nrs2+vqlc5Zl52zN2DyPlOW1K/jylSU++ZBR4Fzr3mPwrdA6Eq7oLM1LG9tBc\r\naNT6NpSqXpZd4rLHlIDM3d3wtV/lb4yqR2pm8xDr+92LqF7RDIAZpyxeQAnN\r\nEqH/wYC1KIvqv9r14JCMmxp3l1lj2E52KMg=\r\n=Xs1D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bc741d759d39eff0a107d2a978015df2b13ecf2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-gFZo5+jICjP22UwgcNYySSXS5qeS3G16PXft9c+idOyEa8guhGGPx7FHgYEH08Y8YoGEV3nHdP7tzXruKXqeGw==", "signatures": [{"sig": "MEUCICC+XYikYmzFmO8G98nwItDCy8eBsLTq0/7NfEPe5DakAiEA5pGlGTXzcd7QTwVkYHworqs5bYRl7him7zMNdQFSdaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkU7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+QRAAizmCB76HJWRbxe81AknbNbFkuZN9wzp3sZ+ub9uid8maa4ey\r\n7dYbiYr78ryggyaOAb2EsZbueYAcM9Eh/UPO+RWUx/hazyNiJJVCIWoXrV4F\r\nCerxrisTSMTWUjE0aF2DmNRgb7yXy5mneNbIbSKf0SzKW72aAr4NTPw0XDd2\r\nu5/Y31OYgH8u/j1Y43lmnJwrg2R6IerbbhQjO/gbbDDqeF30d/kvB9KWpU6D\r\n47Qv2w21yMJpP1yCuwW9BbRT4yDQANc06JG7CRwuI/MoG7Ftm92XriDgHXwd\r\nrTvZvnCpZahOXfOmekK5bdIkA4oZXiI5fvHHL8wGZsl+ffe7oNlORTTnUH6+\r\nt7TstsP4ivOGdmkC/OkknvFLMcQscOQzLwOxFxYs5zKqTKrtOFzE7LLoLMTh\r\nRLD3rqDQhnpKn8Yg9WqX0aEAWFZtcBZkf9kMyDBdSAxYROLTFe4DPXgypczi\r\ncwyN3jFRQt35rOMtRZ53ZmVRqnYV6IVymhqEVxJC/9xpxFqUTcBxVD1UhbJZ\r\n2UgTS+XK8Uapen8kUxHErWfZhRrqCn3/Ga60axd/rFeevkne8NwGKrbYJCzE\r\nX+VwVLfhvifEU9augD2E0ydAdq+blPoePfIWeCJ//+qr+G3/omaJyJKgIJHZ\r\n2wSsuQx3BTjw/GQd5x+FDV7hWpdKqluCg0k=\r\n=mmYA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17158cfd53d4b711402829772ea11e5b2fbdccfc", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-OJ2OzaXhRTrPWEZQjHkGiEeiAkVnlT6ToIurI15eMszST5c10Wnnevcx6wVJ6jK+Oy7giY1UsVBDkWKVtwkMxg==", "signatures": [{"sig": "MEYCIQDneZOFIzWxHLL68O/E6Erpojd8nsKj0gBNR0DLC5+VfQIhAJBvPBrhL/ofMIegJ0ei8d4LbuEAr+5k+8nHJFzZ9uUq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq48A/7BPm2jqQQbbKbTXbYFfbnHrQPVgkukZR/gcksqXMAZcdADvGv\r\nZq8sIJ5Q5jwUAV0ORwdrcpWPoQHBvRV6rjdSe5azWZzURuYhgMvCT3zPipId\r\nX4rLobg4ZxvGfyMqBR+X9p+HOOdibWgxylIrmLSDtIF5BuP9Ub/7+l9FnsRV\r\nvC5afraLpZ8f7SrUpicaChL52LxXZHG5MxvCuM+PG/quCHitu/7z7576cKEq\r\nBiu9FEl3+WdMKEVwRzWU9dDJt/2cAtVHFO6ySDvip38lWFrQBHYuYEPi/EOw\r\nfnxEuosuj9BXdiAdkue7cXRYBngHo4HvjhTobvKtHaXqmeZYi3Dj07NsaSNT\r\nGFcg3FLtucsHpCm9g2ewdZmPRDggx30L99GKOEp52gGn8ofDxIAJzXLHgq+2\r\nyNpXE1ivwwr2vKiUkVaf9F6zaWwS8tH+HCE55IXFPhc4HQBlYLH8CwSHQADP\r\ng3w4C4UPRZUa7ZVDqYOgd+texkpCrJTvs2ojaKDboj4uudLu80fBtu1e5dUS\r\ngEnhTMKMjiXlY3HCteTd4kmOYnBWrENvQl26k/d+SJELk1QrRsI92IcJtKf4\r\nTQlFS4Kc+OwooxEhHXUynSsLFGNhfevLI7tL8+dQ51D48SKnNToM/WjNyblt\r\ndjBqAJ0RUNZhEj63xI7XIdCu7bR4HJYq3vk=\r\n=hc3K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ef03a17dee45337b9f1671b323c8fcc22d8f4538", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-+sssEmxX1mxT3VUZXTEOA/rjHC8na+li4Y5vyia4QMN23MGJXvo1iM+CyNuhm8G/8nnFENMWouovMc4xG+VZdA==", "signatures": [{"sig": "MEUCIGVT60B1edQPoxIMoVLnndJWV6qtyEq1s9BO51PyYHD8AiEAtMiITK3fPKCnZINdRt2vNVvew7CBjsqUZ5xOtWLw+TM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrteQ/+IAVLA+Z4+BuYNY3Vgg2Vz3tEBjGI76YDNTfYC2ahXMjhAcFr\r\nmUmfrUAKVTEQBOfa5dTvWo8fyVwvvXU0oq6z8cgbf/jtal0SYVNh6+KZHG1w\r\nEkusaq2fPxlDjtxyOu5tenb565TMxikgC+JBM1hk8uyhYHJZeX2zHvo05Jzt\r\nOZEmhhYXvqjcJoe0XP8L2gWOSXehtTmRabD9Ubq9x3sCtLBMpftNte4X19HE\r\nijpcc9czQL47LN68FpFFe/D9H6lLy8mMWItTwtzFMLv5KBBcFT38u0HCWaXi\r\nsYWQCdguufH1nsGV0MOtroTKz+2W5NiaabirsipCYopm2uLBxTUl/bUTJzdm\r\nD+f8w7pi51og19No7bsY7LcBynBVMU91ocGu8bWLEnYM7cVb/NwDBSpSVriO\r\nXhA9VU55XZJhFcFXsUrPY9HfIKlHdcM0AZZ5dfukxCrOuYQXIg+c2OYr6R+g\r\nmBNgyAQRuD54wyH6FB2If1WIax7FU0sy0ODC6fbEIptvfxRf1ghCtrolBhr0\r\nJlB3RKubXoH9XKEQlhEuxKDQ4zrEzaXYDBVwTj7B6xe/50pR+BafFCccQSg+\r\nFnFA6xJWHCii5eGOoXBI+jhHS//iRRXBCXZz4WbDzL5jhZhFFksr/wASIZ4N\r\nUHb1VfSpyEpwIGxLuXXBlvNNtVyTLfXRSb4=\r\n=XiS1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1e906631a3926c9c13b328f6bae4faf33d454355", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-5FFhm/dhsZVFGnBYsyOO8zv0xksJ81J8qWda7dDJtpy5NacoDqRWQ1yGHx0nQnwG4FZk6JcBy5vv/zuG750Vcg==", "signatures": [{"sig": "MEYCIQCsXY7UxYiCiFUyTRO12C8fOz5yOtKqpHLVEz9DXQkuLwIhAJjUFQ50NEAmD6V3nuzvPmgS23giPW0HrHj2ZkMB7Mf2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlN1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyTA/+J2bK9lmmO8uoI6epgeVdGGM76ZurDMGqTIlJ/viAyGqx3TKU\r\nZ7qWC0sgIpQmL6zD0fggUnxKVf3vneP5atGqD4c/tGNkY3EsU1LcZp24PF88\r\n6Eh5OXZtmb6GD8Jl/daNUIX8ZTF9aB1AqgpYYC/5Ne5XMCYFU7q2XQPPf4NM\r\nGH9f757UNQIJGgFRmI3vh/GvZN+3PWvOiJGKcAfAaQjaxulyNvp2J6FvAazO\r\n97D6pgB7/ZqgsITP40vans6gQQvf1QGoGifniBIoDutF7ZX7N+Bb/W4TkXiZ\r\nBWKuS0sq52bhFtFihjU3XLL1gheAjX0ReoumGBkowH0JKYeyHyZmcCnhWZdO\r\npbScM+QwTEJ7w9ZjEd3z54WH8KKN5lJ6HkMBbA9KQ8aVtd33h0Luj+J6sWLQ\r\nRz8j9SWbKxI4S2701vf/dH9jsWjGenl3IQ5Ov+gvumtRhLWv0UFOpmqsCPvW\r\nI4KQUV2mRTzI8+QmPmvlTZ+y0Mog+dKUh5gifIslZsCc/PEnCePaiYAXC7RU\r\nsZxOjpdPGtDi+2jFP6UwjRE227Z5I7NMrOhNpFV+XGDYlqNivsQZEWnwgBEg\r\nJit5u1vuCJlF4RRaONt3DxXPuI9kgWgkEkanhG0b3z6nWoS47Z4hJdi5V27b\r\nncXxQhZp7smf+JBaM7eB0AoMi+ER7AeJLYA=\r\n=ftji\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "815d6d11bf1de77d8569f8f4cd2e1dd9df8ba645", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-8mI8Y6dhP9e+kRTtd1h92EWupdMkQzszNpVcZaiTU2QDM92yvLXOCYrQ477wrINq09Hva3KLh4N5Awu2O5skNA==", "signatures": [{"sig": "MEUCIHmE25wvvcYyrkrJDu8pcmt0OpVnx29cr+dYNjtaJWYmAiEAyl6BH7DImFumwUKZbNK/Jyeub/7mTa5v9utBL9g6l5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqY3Q/+LfuQWcqWk+A4KOBS0PR0FIJ/GJ06hFnQtPR+rYDkuGUQ23V4\r\nX4Yxlw95VXWraCy9utt5XpokQaTu43cC0gxUA6/qjmjClA8ENC9EtGBJMie0\r\nUGBwMdLXfCroBigZ8ndw8OMXdd4JfMsPwJbmDBVxsHnpR0Ee77A/Ol1cXAlO\r\nQcz/hpgkWewMvfF1Oq2i25KRDXuFc9QAFshsILgoHtlqwv5UF0ZypJZtEXVu\r\ngGETZD8axBpyTy381zOlwG2gE144GbzKawEhs6PjLx7QbNzZBgmQcu/zdtVw\r\nh0oXNBKMTsPfvr2zZ8Tcvo2t4JTbFW4Qp85g/691IldYI20zdOpdET1rO6mT\r\nvADlzjwl8x0+gT42dpkSoaUxXd5CfcQw3H2g8ohqe2VmALn70p7VqD+v91pz\r\nTqOOy9WTiaf+Mhqp99gE3V+Ui4i7/3c4tkhO/WOsKz7YFuygdPMUkeQW9Qnf\r\n/MTss+YZnmnlbGYBGVfK6smOD5FJoy+QHlgIKqlDj58UWGovoOPpPAZOF8dB\r\nnsh+G8uTP1tpwKCiFneiFUwvPOb+YkutLIKPp8kDXnAihe4j3hRwA3US/AD2\r\nKTtl+uzX+Smb0zY532rqPOaZ3DLx5xe3M8v7L7aIjuHb5lPjRAGGrRYceNBq\r\n04WFRHfnyPXAe+Osyrx6FosEXsWpUOOxhUE=\r\n=Pbpz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4a5562d5b0c4be4c823b6cc571666573011f6eae", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-snmGZddCw7N9JUx8TuIvtwqe9hyliquYBco8bjt2u4Bk5VwwTrGBt0SizP/0lziaXrQBreWHpzZSS3kyCsgoKg==", "signatures": [{"sig": "MEQCIENiiG5taDP6yfXTJTOvTZz36IujAhA1TdMfgwcd0TjeAiA5m0M6SydDGmjBap+C1l/JCWq63hEgW+leLz2kY3knMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotWQ/+ODxUqXs4Nr4+1nNU4ltEUgCF+bIThMdrLuuTxHSJ5Q1cH8pI\r\nJDwmhiiOIp8uRVDln2JkBajXNjXdLbcSWr+cVqgL3T19jyI1KuPZU19jgmwN\r\nQuzc3HBs2egL9PIf0sKbo623uu/t2LCtrUL9nnr5dm2Ma6iKi0jUMRrjJfXt\r\nYoIL+DWVAxSSqiby2mzt1u9YEwcWe18WKTaksjMntDFBZ8fymnAvatoiT4p3\r\nuJBk5WJOxnxCnbrmQJPaFxoer9+Cv0PI22o3uttxSzqbGbu/pAbHE36T9X/6\r\nF/O0Pa7WxnRnTzMk2XBocXxxjyqyNYJibhs61g/VUvieZDgKMQMI+e0LfU8a\r\n74NPEEPEfe/Z5w3DhgWxf7SBA+/IrY1GQHH37oZgcGTszuFWd6WmwtefPG6B\r\ncchA5u/QRo75OjCnALnGuinbNaDBi5As+wMUVRq23vkAIta1ML5gc5A0Cr9f\r\npQmQlSr0YXZWwkjBl9t7XctLx/HRRHDlUsFwqRfW+5HqsK04Owoa6f2SsNFA\r\nVbbmK1EHB/YQn+LOOvzoMT1y2FiCMvlWuaduuQJGsMvmbRGC49UX/JxrneRJ\r\nKrTOTeeQS4fVaIiZtL/ba/MCNhwZr5AG23XOSrfIzMTR8HKAsmWzMjxsTv9X\r\nSNebtahuEqLI9DqtTHIlTsTt1w8+DCOQJd4=\r\n=f2By\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f74d2fd954d0958af624d253ff40f83ef9545842", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-aoEZUfoAPAIxHXvaUigPcSs76H/UjQIj2cChSORoudU2YucpRs4Vj97k3/bRReJY3k9nbuKsNAArVklgfFUmLQ==", "signatures": [{"sig": "MEUCIAxFMgUCbr9d50moGQfSHAJxKZMFCw6UB5BNLlgOKwKdAiEA/n1lq5P98mHPlVNnG+prS/MVaLD19+H+zAmdt3NLeaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4X4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbXQ//eQJqlPinWIME8yc5neX1KiYd2LiOpm/xC8wGh6c4WQ2RIiSf\r\nFGFdN/YxLXwSZxAapKMs+dYovvCTm/GsTGI/ZEyemY2LIGd0W2o7sj2tNEZ8\r\nl5d3cePPLoKdu6VP/NETFjG0NXKwNJfIFchD8uiHbuJMgeh8whlhEpq1ZwRi\r\nss9B3osl8vvBJbsJkwBirlOFbek4BmDNwWiGFSESyAoTYQF9vJifhATWQ6hy\r\nhEOCN+AMzHUHhcr25NFxUcQk+b6g94zvaYz7vqyj+NbCq8Cd7XbeR6CfQ2bu\r\njDPofprTwGq34mElqEmgP5as29qy47DlQh4p/ruf0ZV4RbBGe9vCjAO7sMTE\r\nqhplf7fAWcwT2W4cTJ1bXVOEnt3Kk6TEzdCapF52Yf/LZvDTX1pIZJ2dKYCg\r\nVH2RNDB/z/g6UaefxH0+CYnoGqHc3+jIdLNnRhU6BpVyZL+4SRtHddCkINfM\r\nnOcsEFH0AioZ4lYNZPA3zSpKNsrmh/GaglgP9RMvcQIWBArQc/8PbJU+bvH8\r\nbDoXTgSblV9Ytw/eRHnlXM9b32VlWsCg2t1u47btuegmOPp09E4v0SPkUbtl\r\nWK9y194w16VI0F8vGzLFDrBDZz7qBPkuQlY52zGz09yk29iMRBV818HLBja6\r\nAWKfhbC/nrvCEH+2Bk/J1pMFDA9ppcmhpLc=\r\n=Bwtm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-primitive", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c758802ea732290bea76d817eeb10713b0053bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-TQco1yQ3GQBvBP2WxVrADOSjUrv+aC9Ty/5NsvILgKnxTL5s+5s2432YKe85isXzW0KTSckPJuZMneVTkDxu6g==", "signatures": [{"sig": "MEUCIBCaOiYJjVQeTPvj2i5uHpOtjdKvJx9VZ5Rh1J/eb5pAAiEAlV5P2YYZcSivvRgk5UnMmVwIR7JxnkJHEnoBPA8aYBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMog/+Jjwhwu/BsRSqMjvXnCz6GVjQh1TLrsM+moXDUYCZX9ibK3kq\r\nrHju6tQeWrTeP37QmD9mN2w3izL8zPLlnRnjpCxNY+TEUbWeJDClyceHdp5E\r\nN4nXf2dGE1gAKVZEIJV9Z8+Yw0xokXi9vzdsQFBKT2hLWKrSvtA/nJRFPMPZ\r\nQmr/puXCLJI/Bz3WbEszGDv8Li5arXuDsSugax0TfY8KtMSTZUWcpsrqZ7Cn\r\nIWSp7uwH4LZOZ6bZY5GyoHNANlUtvh5EVfl92aLQQiGZOf59omxHiw/k3xP9\r\nmg3KmbV8TMxNsBuKE7hhU9o8KuYRIggMgHPCMWa+CMy3WBPGaCQ66uS230IT\r\nBT5RDMJapc89ke9VdmQXbeEkb20TbJ1R811o39imEjnjZrXiCxszfDMKQHCa\r\nObt4YkQ8Rg4MW83y8khjGrLqEqBCllSHrx8vrdntuk6HMniYAz7ueo8J2Ljy\r\n9AZ81s15SglMGJk2z6vnDw+GwaIi08W4aGkmt7Z3DI52cjWJvcaneTmLKXuO\r\nyqmvjheLqZmFQYwWAaH/u8Xvmnjwaz8Ly+1wmPe59A+fvSvp4D9cQ6+MA6t8\r\nss+HH4QsJZYECohmWpTeNhRbg841G2xp9NNcbZKSteg95ifaIY0zUK4zOETF\r\nJujnM0acsC1gsGQYbycOar6efPFFQLXpLvg=\r\n=c1z3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-primitive", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6c233cf08b0cb87fecd107e9efecb3f21861edc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-6gSl2IidySupIMJFjYnDIkIWRyQdbu/AHK7rbICPani+LW4b0XdxBXc46og/iZvuwW8pjCS8I2SadIerv84xYA==", "signatures": [{"sig": "MEUCIHRpzbkjWm5tJznJxICgeRr02juZ1tGbaomAybVvXK4uAiEA6d3b/S4NJBsWT+WSVBSUdV6LmKGXbO91/H0/mBKIwi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11195, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+bg/+OQD17jV0PuQZpuY1TwXVpK4mfJxQxdWInNSrVlV9pu5E3ylT\r\n3JqH02U1cgsLLE/qZp7QgGurow4uIMI+PYADSFLDpQsO2eey2QmpHZOL6D6L\r\n4e5Ha5uhwpi41xDbxzJyBErzfuk5+Ujb3NF5IEQHRZFgkK0Ah0X0GLk0UNGb\r\nshTPFenNs+NTru/bked0kSf681mmCoBTco8lZd3vKckcWQd4X8WiQFf8T6Ol\r\nt+tf29k9H8dCGW/MtJ6KKf+9YomeyXKMzGuq8DLY/0Sb75vZSY05k45TV/Cj\r\nS3PUckRVKUlOUxJKXw6nVBVY2wkd9jtnJOjsUHvgx2Uq7pCrtuwNbKQxDOCp\r\nW+1Bk02tMRxKUaC6EWk9qxRpr5vogwmtwf0/4shcx8tiMBEXIFWcNXpAzNpf\r\nh7I/oDJNFBU+dokNe3D/gy7Z21Ew63ZkJkXPIv5FP4/zXtBeeP4DkU9UHyS0\r\nSLltoZq2vSKtfOqCmEKvVMYJJkonclWhC+jMf+f7M4d0Z0QzXOVLwTcYDQP+\r\nOChxbF3ZFL3qyUu2FmpxSwNdMfBaSF1cTdKo0+nKsTODCINn6cg9jxzVQfR0\r\nBD155kLF3QnDLZNN/qQA9OLgS1+5RBmJWucIi3y8keEs3P/vuqxA8VHsgMkr\r\ntk8J3Ckqae46Y5eQpWdH3FPvl7oFU1HbleI=\r\n=F3fK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cf158bfc084feaaa3a9de06a2b14d220db7acfdb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KyXdebVx5K75TtAL1f7dT9M8w2SDxuAqlkWmXAdgMYjfw7iByDMJKqa4OEyuXhpKXwn3FEQpVDin9/FGvZPAEw==", "signatures": [{"sig": "MEQCIEEaBqNO2Z8juzDpD9+bfaZbzWZZOBurNourqkqyAOrpAiAAriZWrpJSnAOHY3qb9p9Hk5gNLon9XK8Y1leA8XU6HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWARLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRmw//daVYYDij5WTdRm1Bks2gY4MBtU5xV0Gkh+jaPXugE2huI6YU\r\n72u9qQS0TGF1ROAXKmTm6fh2LYUePTuGVx64n+O+vPsFaWxtgEg8YejW+jr2\r\n6hzEGqseBwjv0dvqZtrj2TsmuniDgsDH5jS1oPy0k2ZeJhRXb+upKYC9QI4o\r\nxbg2nzV2ixUORCjdwv0MqJ6qZfPZzxleXHGbSO656VNw4SXWx0qzd3Jdp0KQ\r\nRZMvLJDM+nEtP9i+wGtZW/zML+7zNz3gUIx+gEdCfKbGTSG9thGGPu92ymPA\r\nWMlYsw9YWUpxMwIcAe0V0OWyvkIHPv46rC2a+BNOpbXtLqKlDt4CSPWJr3hB\r\njjKB7exoBUOOPAUZfebeE/MqPMPbw60diMiHife9usuyDkOEFfSySPZ7nBMl\r\nPQhJN+5TvAMzMdlBLvPB0fSjplsN2VTUgJ59cv/aEvEadjD/Fw5Vn3CopWSU\r\n0ONUgEp3/Cv6i9QmALf2NMPtMj3jreYjswUp/Zzm2m/icFTxSyB8WaKlPB4X\r\nI0c5bUB2bKti4zxHDuaPoA0+84UPS3UJPBek3r5PwSoDP3OFDVfel2uVYLJo\r\nnoNRqqzk3r0M8/IateFs6MKObPFMYodQqe42H5M1jlfZF8jLNC+r2BAPQIWH\r\nwf5RHZPB/oH1425Xwy1cz5UZZkWq8nec1vA=\r\n=8vzW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "459016f65b13bdd7716010051758963fe88e0e20", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-2ghc/6GNH/eOPaK5q5PBQubUNowSQZ/OurUYNUnJ7Sa0NeIt38xOj1GbyST2zRjOWCVVMSvYCXW3kGqjX/Oo3A==", "signatures": [{"sig": "MEUCIQDnuajYwLuh5t+0//ZSfaswkYHfOaoKD5oTxKQM78WG+QIgXNPFvy6rTQLhdvixGWI605Sw8+tG8lGYrV3eASSM+0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZaw/9HtizqtyGPnENB4EdW2caWMj8LXQz7mY0BiCfD4QjgT/ZQ7o1\r\n+Bw/wpLMTE1FzDVFVaMCIF88sWWk+uKX3y8n2JOIm5XikLa/C26iOyiYhPHv\r\nf4K5hMa9zjXui/e/M1LpI+orNcZ8ecfTILsUXCIeG2lQuFW8hy/wRRXu+S0h\r\nAwB+x8ww5WhqyeKFp+tilXp9J1D1z/+eHTNic2jqkhiAbKRH6ABwcGXIiY1S\r\n8Y7+17Cm5oq1QzQYTlAS1K3LxsSOFWo9gSTwW6vMbtLDJksP9CTr1JuFdT1B\r\nN5XVhaXCTppKejnSMwcT9XK3Y/qtiu1tsupPPKGrIsVIndxXlMRszdRbf2Lj\r\nfn7yqgEHCB0R/+v4Gc+8Z2cLfzaINnfKIkhdkcddMxkAWm4J8hK2wliWcXyB\r\nnqq+FgJrtLUlEUmPK6vWkjQ6gsxhx/rC6AOhGgvIYlUROnQl98uMN9o6GrJr\r\nb3SbcdyqkX+GbDAP20UI0+UMdufWpsROELgtfFKf0GOCnBX19Zf5WEl2MnF5\r\nzI/x0EdjzeHZkP/sivic4r4DlrSm4s9bOJY/eAOp82oei3P+4VFajSA0lMBi\r\n/Y75nDeuB1UrAr+1y1eBjPGrLRh4vZIXwoMfT3CfyijvNfGBlYos3CfV0DkN\r\nd8XLDCgY3oQlvqrFHtoC4WbVArwJXBCLDnE=\r\n=dAbY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "32772eaefe91ddf21e9c013164a3109456d9562f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ZdXTeGHcj/9/rSF+CZDJfUpw22wGQ74NC2ojoW25tBqON1+0yj1Y2CpGBt1+yC5RjHSkdpAou/978hQaKaRMow==", "signatures": [{"sig": "MEQCIDJBwjglyMU+yDlvJ7leIm3KVgeeFWSjANYT3qXbeO1pAiB09PW2XnA8N2xSXYc5ZQjO8Relw6r6gamwAhk+1MgMPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrk2Q/9HJebMhPRlp+yY43IkNiiyoHiW4nMTp4ZlkCKOy6UJOhAnxMa\r\nqzLydwIaVTl1bnu2o5ElWVF9lj3iFPuCiPreAWYHERLosP7EiF5Ajl6R+Ldq\r\nBy8hcICHkUY0sbVV560lq7OAoQnbLA9+ajFiiKWOB0paPeIASTsKFSeNhHUZ\r\np1j/oD7uAk6m2d9O50BXikAizCfHO8PoB2i1ZNU9E9CO8s/IflqSbOLb+0mf\r\nydXBz8l3e+R1BUrHa7mK2HEspOxOzp1gZBeJ6iE6TZOlZ0qVgqMiK335s0/u\r\nzIY+InOdf8MAC4n0KnvlwZ20GwHtDaiEh8ztQ4U2+Wbi1IFwQzoR7jReEqzy\r\n2uT4BEJoRN7rVmSVUGjO5gkxFTR+yKjwTlygo+/8dN5dX/LZZKJfPeh1WFYC\r\nj3jpixc058qLVEan19R167oNz9wu2y7SWygXOKCewZYS2R9pKQ8D1nwDn5U1\r\n6ztKPbY6KmreG2c/NEkDvWxGTXN1ggUvrHUHzeq+/OWJZiNYE/OP0k+4+4Lf\r\n5nIF47jUHB3LsqS7PmMwEiNSEKNf+i+CmdQk9UPwH6mBWAL47iA6vfXXEzHq\r\nJ3UFMYWmddJV2sJF65uFkUiDT/mWYolttVn/i6h7LlJ+v5uW/MMt/Mbccawp\r\n/ImBmo0DjgVSPNGGgH6s6GplVEiz0cDg1hE=\r\n=/jee\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.4"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb9bf4ff9923cd93702e7817c411eed3b9fb9800", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Mb9QSI7i8ULrlFuFSnb/99D4BuuEO6F12XjKAlqze2W1QoM4Ns7m0Nv30aynatLCpg9M46sCXrK7oagF0DQsjg==", "signatures": [{"sig": "MEYCIQD02+TO6tT94fSjgBgjuhFRf/ByZklesChQlydHl4URuwIhAIYsk3n89NjNTA0Y++xoBVL/ilrg5aXgwLIKxO7keVfm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRryACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQIw/9EnsZ1IebpwKSYGpsUxi57mOjV73LUkxjnmSDxoAU9i+QZPwX\r\nmTadkX6KOSd/S5Yk/74skH+0y+QjkX5m805qyuoncFJegKrQMLwKk490B7SF\r\nibNViHPLkwIEhbeBXGY4dcr+DPOm4YYyWdFekzcTIh4IZ3JEg0EUV452wzMD\r\nASFi3coUhnkGqy0o9EJt0mZnQV19ZwUWi+j7N3g2Hr1/xZOxHHs1VqGE3t1J\r\n/r2enhI9GMClRZAVcpTRgZg2f1kZtUtPWa2ABkg/AgRxJpFMMUgKL7CxuVOb\r\ntqMEM2jb3nBBWTXKZPpjX+zxgroJVV4btOrRQmz94HepnCSO4Dfw98UpzVX7\r\n4/WUrQJm8On7gq1Y9IOQHbkWkqofAdrunHLFIPYkJoDSy8uhLTuo3JMApyM8\r\nzA1hj/vA0xbqIDRpY/OKfdxnG4+QzjUiTpkjoQvwsCbApAJvqNeB6Ccx3y7x\r\nci7v4iI5fqovn5q5qhVhw40UfM3lqtu1vp08w5BnOOB5g7FrV+4882eAC58c\r\nPrDTBxCgD0vqby+SQpVd43gH51442Ur+qkinjaB+HRrdq/FGrRHlwsYBTM0p\r\ntE/3vDYdqjUTm7o60Box+Gnp7PPP0xuUgRlV4fYsQ01DJkmwXblfm4GcGQOl\r\nGG/UvTfCEoXfRBrhZIorDr+uaAl3npWr0Ig=\r\n=Pr4Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.5"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1a22c87f8d27b310d44eb85bf2c5f48cbae22c6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-QT2Kxq2uBAYom/eT23UILpf4cQEiaYchJIeBnSCEBSL6Cpcz4zLP98zIbrIcfYd14WoEMlImubHn4rlklBGGrg==", "signatures": [{"sig": "MEYCIQD85y/wItlo2ktPUvIRrF6NRYqv/RwO9W2CVn06hdxyYwIhAPqhswrV7zqF/3Z3eBneVDFrYfMPdO5hgKTpHsrBl7ee", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5YA//U3D7rY0guy6HOTfB9sBLsOxqdvE/csmazyawWek97Onxy5d3\r\nx7rkL3PIjucqTfuWAILo0N27LfcluJJhLjO5em+aOfReGXsfyln11L2xd7U+\r\nSH7Q5Zym1PPVdBXOm7F+3vIWx/jDmBVKaMLG0CUEWPpzpS6bT7x3I8Waj3tn\r\nOZWt+QW0XgMYIDGaqibbPUYLldWzNXL3aqIA/8eoYHoCWVxVq04FEkRJ0j6d\r\nn1Ui5IxSLPBQN2VDzaVnx5i0QwaHgLWUnNfa9rPRI5kvLqMXqJZI4Zxuqo3M\r\nQmTK/d6X2e7zoECIvItWV/VEMaDnNNE1NOVUoSFOTYzHii20DnYYfVlORiUd\r\nlTq7y+FmzXDPE92G7pVlfWgFsoaVbwZ/fEsLqkR4yFSSOEFUMTd4RIJr6AcE\r\nrzUx97dEcF65MEWekLWGD41KfIGLBxOKQMy1xWL1+lQsnlD9TO/+jYfoeCBC\r\n9Wg49cNndJ6Rcrf93aBoFiNZiwhHJb8chS76WZjkL3DSKZrwmU1dZzExI2Gp\r\nYjDLkzvLIFgR9mIIt/RuTKPL0C88indToq3R3bpr8nr4w5xtzZg5wmoadQ5R\r\ngt0U3AycZOjqDBRjpAkyFz+89yBU2vPBFXZmc0H06LFxbc/Cics5/OsHixf7\r\narVODNPXBBfVA4JrZRoZOokaLsEBykeQ/cM=\r\n=Xefd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8274d76372fe48c36d5a68c0c595dfaa3f84c336", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-qzyfsCb1UmfvuZnPSirVAzO3TJGu4zly6yJc4RgsJLJspvcaRMFWqs5i8nYkrM4N9Emj7/S+K0pHFZQVXV1q/Q==", "signatures": [{"sig": "MEYCIQC4bh9GhguKGdbIwWrh2ZvWhzmsL/2rFge0T6UQ/4E/mAIhAJ6zkf5ov9ASwYEZFA2l/eh6dhRmwj+wWg/WQ2YSbL+X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8x/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaIQ/+Po4LlAslyEFX1wXC2Vcy1EBsUQfqIJIhjSr2EnN/gcVbBSNY\r\nzUIaOpAZtpN729c0p5HbIMl+mC/vTyApTI+N0gNuX87e/GGeQ9JkSuGYxm22\r\n0uCljlJQ+lmdWpadisVKJNS4UM3a8j+VQSLH89uRFE25gCObZRfyvVNVvWNr\r\n362gsS8MP911IfiaQLt63fapO6o4Zwwme3nb2jswFzWDTf7oefXw5o9u1vro\r\nHmkbH+JAtaI63sO9R3nSmbLRPViYSsvDEMOUkfzM2pT8Jwd7gyxLv2f4Hc1S\r\n/acTurn5c68N8aja9IgTXEmWV4uemit8rGHIXAv8THeFkRu21gWo1/IcH1PG\r\nwnln8y4+8eMsE54I7T37ZqFw4mwbYzwdx7wsAhTfl2CVdo344pKcsr0Ii280\r\n889sc34dV8WseDQTbPaNeNVXIDPQHgwXm0jVxON1gffb7yNhtNoyheBqeMtu\r\nlTnYhdo12rN0NgVf7DwUNx5OJ5EqQDjY8P/cgwNBZytS2fKtzc9CFWlzoOUB\r\nbQcoSu3PQKBDDmpiTkBYXvWmpfuSZMHL7qd3uyotJ+c0IICYp9Ui6JlDGeR9\r\nKDxErOwAn7BwMXt1rCbSBx+sgiTR8H7iEa5gPQWLX7l7VEKB1WTmyjpyQTyH\r\nBSUSGcSt1VpQ4ppSHfO0g6usdpIad5b1NuE=\r\n=ffBZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6d15519e0f7e2ebf0e4dd9ead0b673eb4a9a21ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-yDRKxrdsjF0nsncQOHyREaPB73pdhvl+O47FgkU+5ymyTOL6pPeoN8SI2KE/am4cObvRf+BlsN8rF3nEC7CwOw==", "signatures": [{"sig": "MEYCIQDoaOs5c4uWqrqRhhL7ykYcOv1ZeJ1h19ZPU/iBcVRXLAIhALkV7+xJ7mNMT8iLHJ94JHhPmdBsU5WrcGoKmool+31F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia912ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMLBAAjAPK74PW8nOlY+/GmiaE2bdIb2CdDjzriQUcex/oT4eXZKLE\r\nAvW9Zjzg8wWqoVy10oD7nx/nzJehgwjxO4aVUgZ27C9App9qs4DsuMRcdXx2\r\nmeukdimBcDaYkR0rvgpfJ9/WmTsex7e+yyYu5A95nvp6cH2/YZLpT+wq7Ndn\r\nazcLRE2T3izMEYmAEC25YYXsZviF22fovv465vmbC6TQpAdzpZ1o4oA5S89S\r\nxcRdDAIsLQZUrkbo9jvw+1zR0jH+1AjMEfzRir0GVMmxChjwV9GZqyGrnFLd\r\nS2oQL7iJkZ30JqiiqwPC6vHg7+9gqZtMlU8iXZGrOgKZrq11e1Ub2nsuCwtc\r\nzryovegG/s1w6xMqrsFwnOoCsIzave+jhXfeVk3XJW5ZyNvJjwCcA6tqzx+b\r\nDj9wVjsDQ2DwJb0La6vT5Lu6CDLq7Q95z9Q5wzq726JTyqeU0eLR2C7b1iBP\r\nkoZGSMxRfK9Kl+AYdc67yBhAO4DO4eB47XhNmFzkhPEzFbmoeKPrcLeiPzBT\r\nUQxZ8T9wRm2pgHNWzirSPZ+hOiNV/LCLzX711hTF2ZDo26dnDv7zO3XvuDbw\r\nEhZA/gLWKkxscY+MsLu9q1nsvBPOevXjYgyD1fyCOXhsLQTZ2C4eR3F3+eBx\r\nr1WMV1jpMGhygeRDriJvS871u3LXDgduRxw=\r\n=Gjv9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.8"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dfda04221f398e1f869f2479d7608a5fbc4eb07b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-7c9o5kXPwWuocbq1zk4zMOrv2mWQDfAYx04YprkdLvXDI0z62PkG9yiqGqmVcpsQ/4j120biADLzwM7ZDh5gKg==", "signatures": [{"sig": "MEQCIFBmpvZ4Xk12hlGFkUfhf/24MxhxHK53y4FeJjm584hvAiBdfaWG4LRBZEJ/2Ika3TDAA1Jog248zA9YTi7gfwaaKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzlQ//Vugnove/OIFN6FxeLaojTuVbJ4hceJLWtv/QRFcQjKwccQUi\r\nmiUOHpOdwxdw4sRElMCxAyOJxqs1TPlLE/pnCddTohyaKU8DerAEqtP7aKyu\r\nbhUvCcSU2sx9Q4Oyk//u4pTcKtETF1jJ0xKAU9vs3cITrEuzr/1/aBN01cTf\r\nxX22cH0OmVALXfe8twFQ1W190UEldw55ZY3Jca3pDm1572FevwpgT3YBRUdF\r\nbhT2WGGTEnL5+fqeGN27SW2hf3NlLIAd120k/6siAN5XSackGe3+D/9Yuvod\r\ndKd7JBSOrb+GiOCE+/0zjl8PZcWjodp3uuWd4adqXrM6iBeOIEqL4UeEzvns\r\nUA8/9uQTcMYST9x56Wbi1zcMaJYwQUgqm2Dad7vnGPMoPmRh4Z233ftl5uFS\r\nzsO571PLPvE8L74m7xafJ5b7dIdHMUhvNKqLfbqS1kwBIu/kB2xnwx7FOizY\r\n+KbV6xdPZeRILOh+LbwlBEw4W+d0OPCkml4kRaifjHcs3c/ccfujfW/wfoVY\r\nO0n8Fo7kSjpMmKvv/SGPwggVXUGLkQcV+HL7ZLYU+6FSINs5dwCSyufc0uud\r\ngJ6Pj096qYIRgRAkVFi54GCDD38GgfnLOKTPii205Rnvdg52fIfK3r9NecyQ\r\nM6XnyicTZqmxrdb9h9t7RI5HhM2xvL3FVwA=\r\n=Ecn4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.9"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cdfbc32b8fd3a666626d6389f4ec0fe5e5a23ab1", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-rK2bTTgh6tTvWYSnDRhIISs7CpYT0ks0zWAiqD0qeQ1POLJulcUxJtcvir39E8qzrJu7zpIGufvSlAJR9roYmA==", "signatures": [{"sig": "MEUCIAUdz/FuRvlAlbG3rO4gbEgAwmrp0OfYDlRygzw9DjCNAiEA4NKfU2fhxwqH/yFSkaKlkF5mbs/spaITGAEvOSJtIUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNh6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT/A//b/b+b7ao/UCncskYSwZ7khOWGsQCZfpsXD45gBLBDp9FuNF5\r\nxRv0RmPFomMCuAE/Pg2q8I1p0umTGi80WZ7qkoPjsFJ1Ueh3x45rRCB79BY4\r\n0lc0xsy77eQY9vy1VWeUjuR/7qK7SIyuEJKyitTXo/os8bUS2XN9qcsuOVxw\r\n18fYlun/EEsZ7+LkGG+50qTTZ7eYY8ps9CYHTbMUeXqww03d+ndKlni+TbkQ\r\niqWYdkLWh+cR/K4F5GT4/rMvtDL08OMDL+iN9xx39TlyelE2Iz9xHQwTDgSr\r\nHet4p4FGOMMEu/tEblPAcWgj1+J3vuX7jxxAtNXH91KBOSWitoK/SClH2JOi\r\nDp1CNjbveyTvIMji/k6BO0X8oJAtDQBbTDVKKVC2TtXRLVyf6+3tWAnR1gie\r\n+osFbWkoqxDCJw6yR4qrjwTsfPUvu9i5xsNTdgL7DJxQ+k9m+oCTFAlGjECr\r\nv0v0VurGAcsHVJEFxGGsTgjZWkkC/tj1kAhlOGP9jpDkd3DTTPXTVrtdjeeE\r\nj1y5TU/VS2oeTDyTqtw1m+bwd/7JVGX9Lf5F5XV5lWT9y0OkDTdwYrQATNWW\r\nnkFPHWHmEkvzCEcRJVUzwxZJLo5VnncrZIa419kFYxmFY1TsiAHisHEYkFRI\r\nQx22s9egI1jnFEeAcBWOS5nDmonPgdUYcs0=\r\n=7Ne2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.10"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "427844833d4084aa3ed3bb5b90fc4dff8e2c7608", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-h7yMNfbZy2QvaSSeJlGY6jJuVPeToTmjgVN42K+DZU+5xenQTXyWqdnan4X2uJ8aZcd6E5iEEA9b/l0c+jfcHg==", "signatures": [{"sig": "MEUCIQCdGIoZG9M0dL5CmD4vKSitr3EkPEjiAqFtK0hpRgFBqwIgIiNdiG17OxfPYmWrkdxFuajOgC4ZpPPBF7WyMUBIJBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoy4Q//d+dSxFpPMWdwRf6IRhk/HN9qds+eQS/9N7c+Uc2TpbRc/fXV\r\n0r4tmViHGiG9i6UQI+baHv8+TH30PF8oMtfilaGVXUqDPIZFEdJa9uuxDWOQ\r\nThr1ogD7JYyXAzp2cL/UIsfFTMuw62P6Q5XFDeHBv7twz+n37I4l3qvA+h8W\r\nwC7XtcVJDH7xWd1OEKh7P1pSiVWuLpEZK08n58b2xkYnm0I1onReAAdUHDLF\r\n7B1XugGbPWoXfxBg/wAv3gQlXwrNqdwuILwZDX3zfk7k0jwk2QbfLf+ufV4+\r\nyI7qU+rcYHEase0YITyIb+AZ00bK1+GTMiKgIXeR2TS+V162T6qHiy7eBpr0\r\nsAX4gIOrBIHL5jrXfraxldrKqXOGOmrPV2/SzqQwM5g+w8Vam/HW7lkMdtZ3\r\ni2rCBNmidH654SfoXdVuw8qU0n+4cK3lYPemqtThoCKEl5q35ZIvo8YtVB2O\r\nwl2De+i6hp32UHmqwxElNlEyGJZdeuVEOf+4mraK/4QP69xejMllKVuluKrW\r\nVimf5DHTvg+Y0/9lt1uwqfOZLOki62UdHKsGlySvPgoFCVQlYb0OtX7doxqp\r\nWQvltTvgbYAThH9NSRsxah+dMorq+nuDLJ9TVvZElqwTZjs+Jn8kdD/hnC2D\r\nt4BhrOGcv5c0VnPawmlMQS2FZWrKV6P2PsY=\r\n=fPyh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.11"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0cf0979d0dc1ee469ae2e86d1609d78133abcc8e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-+WDM7C7qCaSBsSHoQNsMHF0JBwtuwQc6FyzfP+meTbk9XslHbIh8w87YdDWccW6y9vzNsfUQqGaEwojhu8NYdQ==", "signatures": [{"sig": "MEYCIQD6ilUCYezCMHRTOLwhd09T8GKf+/MgLmNaCgVSAjzY0gIhAIn0vermLmNBLZlOdThGXkai1ctMf70WzwPBw0n4WIDM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmondw//SgC3G0RTQ1MR7UJ+z4ePpALlMNWzJUn5B1M9s7NBKLDCHnc5\r\n5YcfKQ0TqzDoOWi3qFRRp+Mf0U75xyf20PIPmuaslDu3tssIye9Ve61NrovG\r\nSVEhEstvSomLF4+VNhKDEHJ2Exdf451bBQaF9uuewRaQZzoxMIkwE1Lv8Y73\r\ndniZfR0XYsIaCjSHbm3VBoK0aRljJ+R3NGFyWZI+jIMhfBjplM09s+4oluhE\r\nhQfVQ4Utd99OasOHY864j1aICizvDWlIcE98FdzI+QTMMU62E7nXMpDpJM0H\r\n4Z8Bfp8vu5UQeXBoPr090/TCmYYgitgu/S/uOCFkQxdENK1+2Iw1VQR/lfq+\r\nrd3ogCSzzhA0AHjjGIz8GR3ykHO8+HCRebvDJ4B+KCHnKcWxw0iI2dZXJc8G\r\nDVnW+0WGCsss/Baw5DAhBSwxWMpfeXQeH1CooSk6YfjurAiLIjwxWmalgK/d\r\nU6NTYDAItp3mmUj2IP9geC3AA28Q0o18HVlxL+7Dp6MGyIrlgZtqJ5xyON3n\r\nn6qx26FUy2RXUOx+AYDOSj9w3EWqW2lYYaWtiDRy7MQdPr8wkCFYFOr4g1dE\r\nbbE471O/4234o0bGqaLlfLwH3Sxx1dC4oKYyWIduMQVbNNxOYVkKMVnZ4YH5\r\nUZLzKvjJVuuUVEWJfpNF6yCF1EkHea7SQ60=\r\n=TYCa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.12"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0443e4dcbc2b54717e053b01fc5011f30136f92c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-SK7kMCUvLrGW3B4n4tJfgXui26nPOTFVRcsEzkpPOASa7Y6ULULk0PPy3pAxc91HOLDnmu3Ggl1vocvbMRYQIg==", "signatures": [{"sig": "MEYCIQDCKJ0QJGWyYWFEqpcG+2yc7H8HA3Ik+0H1RvDMsfDAXAIhAJwh9dBbnxEKl6mfCOGUL1nP7tHK4PYM0eYPQ16RF/Xf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjCg/9EiKylNCDoW0D/8n6KEsBzwIGD537iwHVfXJ9SQIGsInfJ2pP\r\nkWmJFvyEpzinNw9Tfo9UMCgYbJT/eo7nA5CXOCxlhwVaxazrcjTrYko1PnrI\r\n90QMwsCP+4vvTGD1lHesC+zePePEOJfJTE01c5o7H+bCKj7Lp0kkP9Umi6sp\r\nNHfJsGzWCQwirPrPZ/3wRbNsKyh8s2DOvhteR9KW+ou3DUu16Oz0hNdcvrZQ\r\nbDnkdI3SUrly32R0EqnuP+fkYaiRv/wu39pUNqvH1do3lkiCvd5jmzQrw7yM\r\n/eSh1APkip+zwbTHUv6jMHRztDtb1gv0KllAf4t61jbBCJXOkJyQ9vmlmHDc\r\n5oNkRkBrD5z0Zn3McwxjVQ+FxMQtk2aLYVjNpPIU1WFBzGyQ1jz0jJc2gfzP\r\niTm0jQ3Y6dCd1fCCTn6pdkfx0qT9v9udQrROv4MEnL+7n7pi2eJOgjv9gJRZ\r\nk0Rmxmn5stl9xnOZOYqMiebdtAgutslcWWQXNXBZOSzEpGw0WIE6aN3QmITK\r\nj5VkbWmaBeVyUJePyxty8FQ0E2r+fLvBkFOZe92DLFqHYxV2qBlcqXxs0Umu\r\nGvq/zCR2lcWvmY7lXMvcpLxeHifoW5okGONSm2ToSghtXbpiHNsJmQohcqeu\r\nh6M9nUD4dp1tELcb8sT9Uc9GolUq/mbLFBI=\r\n=uhaH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.13"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "377112fe4c9eec10cba66426e8e80254e3b6c39f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-HmEHf50uv0SBMCqbNhPlfbP6o/gKvsbXKDBC4pxhebCLdtdWZNYMeBVeIyUdwpKKAULdDSthP+C213KoioskkQ==", "signatures": [{"sig": "MEYCIQDjHBMGkGThOItmHsD61HV1y9UVze7daaQZ4+4pfvOLvAIhAMctVNXQle9nYtSXNB7+DvpLSbNoS+HIwvo1W6qCH7Lr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqifg//YCe9dOlBbfYlLIkCdhyaw4795JvfDoQdxh95EMQa2cAaFyv/\r\nuhFjwHBLHpFs8FPqqZxIWl81ZWPFxgwEfgaKqw4VXpXQN9TllGT2YpDzGk31\r\nNkmKfr3sZiNJbvjrkKY648nUVCJjWBh2igqDITQTb49o3GV4MUrSzqugZRi4\r\n8WCWUJqzO7I+vnNtYQWxVO987dcBUVSI9gC4HNP0R/+0CZrlH/VdmLd2ppjg\r\n5bNyja1AA8f2P9+Vb26jQSjTPj7pXF4ls22aQHIBQvU0Kmzb6TcTm0SyrM4b\r\nS68Dz6LEbCtAndlAlJSdDsrZ6KQ9GTq/42TLp1GCUMaWUHsOJHVGiE+cqt3P\r\n228jsa4fqYdfcSnCBy2wZs7lvJzKUYsflJXmfhXo04K14Z/bSPQS/tEhhmpu\r\nRjkAZhq2R6mr2Vr2ZluxK4975zUw0ZZcb+snbljEz3hy/qlN+JfkjsnOhQzf\r\nFVwqrvcVg1sPJQEc1so4kQoD3zIaBap1HNrExqXF4N+YYYEaoJzmfNPmekVl\r\nY6dXbUtSWLEUf3lAAHAHV4HdiMWx3cxBGPDmw0DQYaSy4JX9sSmcW1k56KOF\r\nmqccmmAhvOYOkuJeU8uYIZ9qF/aC2O47+qxcAfvZqmTr7OQhV2Goe8dt1/vv\r\nl1wo5grqSuMCPt/cyx12BWCGm+3knb/0iQc=\r\n=PgY1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.14"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4a20c17f116c96676f3b8ace729afabc34b70c0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-acu7xplhsKh24EP1CusM6fKd2SYJs0W1KJcZcVVZKVknBdBxzVUJy72Uqx4dZE10ICjqj/1RwZIXw7C6/+nVVA==", "signatures": [{"sig": "MEQCIH7Sv31gp4KjPhL/T3t30Wj4CqYjYA04vZolFK2VYg4VAiATLOZtCqIkND7C9Uab0B/08BJqWFRCluM3pnNzQLTg9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrjyg/7BJfSkWnzkJ4fjk7ghZCNzwRQixskFi2sz1HgVUHQvh+OAEe8\r\n/gdYWKv2+4T11xoKb3++ANZs4bZYTszwZ8Xok41O36Dfwl62ykel2Lz0iFFi\r\n9y4G5VsfPymRzQk9EP3SzPIxI4m6iH9WMxTgQsRxfgCmzRvu1B7Ue/8aLIK8\r\nhX8fCCqpgNgtxrf5PXZIrtFoAcL5n0VsZS5Xo4iWPT1XFWyWnljd/UIokjgu\r\nXN5uTsWfTk4LWDO9kn8NvolL2V2XJpdNqtEqV9YbRnp6/TnAsWrcpmhQHLzt\r\nbwEtVQfW1AHqVMflKH9qflQb2DZ+Ce7+gLoDkPZ85vqRbbjr9Kia9CehEUzD\r\nbgmuQXjG4My2opT9JzKp/nrFEJw1FDMW0VevvdiQh4gZ1oGDCPMw7HgEMS+5\r\n6EnpIo2ezXGLgv0u7uxhP9ekp9KRzu2bKHGlKJVbcsY7EmwqptFAzyIwy1dV\r\nw/8DD0pRGzOw57IQG4UdDUhf2A90wql1fOnpFXFErb897Q5kO65PCAoXyohN\r\nqr4RHGExuUamgmKOp/DQqjqUvoKm6GrgKMVsc8UZh7EisJDE0rcLxIxSNvPm\r\naqLu2U1ovS+2GtUkdevk76af02x/OnqFh4/Xzz5jGJ/gdtrs/wAGyhjBd4X/\r\nDooO5zuZSQtiWyAkb47YPItlD/nz0gytm2k=\r\n=IAcm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.15"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f3adec2fd380c881a6f8612cdc102faeb664db53", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-wbBP4cXShexk2EPmPQxrvMnv7E9ds+yedZVV2eTckvlDTzjW5ie3lxxzkklq+xXgg5sK/GRti/DV7Az92bciYQ==", "signatures": [{"sig": "MEUCIQDKPPdLtsnpCX4vKCQvKVbvbVtWW3m8dx7VgefdKtjeNgIgcHVqWDHYRT2Oz7VXKKaHmTC1kcHNcDmF++qovBX9CQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNaA/+K7NBFqdP8w21DNI5YTkbHs5i5HMHYovKF+WeBlj1g4mvLUQj\r\n+qRSqzfRvbwjdT/is2EgpeS2AsiZzDbw2xZD0RjPp9NIP8cM8JAEIGAh26rA\r\n4VaOMSplvBr+TOBuvPG7KvoKUO/jWd63v42P8wTMECXVoGYWn/7c3yuFCL+r\r\naw942IYo3WvSEYijNf3Vu4haMOEbcGdwWjr7RLbiVCTLOzTLpcNPB9MMW+Yv\r\nP5jUKkyDBpE+o2oTJx3Uuy5XoJ3xb5m9UN5KM4tad8jZb8KVHzp2TZtM3+yO\r\nPwo2ETm7EsM9CQTj4KVIS3zrSPtTXpykFI88MCfFu3CVh9hRgVSBUK1OhOz2\r\nXXlWYak7kyXK/++dCdmoK99L1d750UztqrZML5cN/CijHJSsSJHMdfUyMKlK\r\nJAwilUVxedp39573b4//w4uk8Di4L97R6VmOaeFJR3tyn/mY1dh+0PFmQtbN\r\n4FoOorIUqZQPfPMUpRneOimV77lV3TjmRJPppi+nI8NleKqwpgqGcJ3/WS5W\r\n5XMLtOBaoMyTEFBfdNSX7LZpvIK32o13/hCHuVGZmMtkTXaU2CBTNEHcbEcY\r\nL6lGW4/V36rFfwyK3hzK76Q545e6j37BcM18dix6qbTRbxQOzNIHfHX1NmKO\r\n82+hI2/+Fs6pLm0v6S/sD43VIy2C7iRfIp4=\r\n=CX2d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.16"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1945245b697dea1971d0b5211ca2841a4f962b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-pYEmIh1DRLIqFeRO1RKYDHZZZDTiNoF2g8QdtmEkrUKZp/HercYTmABjTYAqWlImcJqfUW7sZp8kXzeEkuY5iQ==", "signatures": [{"sig": "MEYCIQC/avfBgiDrLmwBg4CXSJLqVe+PteAai7Dfn+/XCP2GHAIhANJJxOA3gQknm+xmPnicS6U2zafFyUjDX1UfNo/crQ/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4iA/+LTEdChDep7hLmNuIPiemwgAXWAgWj6kJgZFtJE05LlJ5Uhnq\r\nynoXSO+sN36cz0IQwDwtY5LHZiMqloKMlZH2nErRmp6o63Sc2ETzpLwYk2eG\r\nq4ylsQGNFWR26/Jh8kZqtDbmzbRCz0q/her1mxH33NcNl+ZpzFoysvDXd5Ml\r\neXW+TQ9Fid+iLDUV6sRA3x1DIiqOKdl89Shouaq8M0380OX3ut8qRvq71UWP\r\nuB0zZdA8iizlYmJOtlTawsnA8+0A+USjiR7MGNCmIky64zEdexc7HUyTh7C3\r\nwATbdt23FN+auCKvdOY7/G97ZGeSXjW9ha7vAS499UlnlovIPH6ZHw6JQtfG\r\nT4i/bgI86ZNDTfkIyDvrYs6FtEDu9x7rT1TcvzNxhZoX27g4ez7HFxYtj8QN\r\ncX+U634VXmN9lDzpANK3+02KwBfKeod2+HqGElXoqXPAyFnNNbJ97CrWBByu\r\nRyO7fZ4fQuXaSjA8WM+0hGta1HenFzdzDCVkSYjCe0eZ2Vuqu50yVAxVPX/q\r\nghtIFe8rAGlI18nreXVXq0pjTRBczahPs8fUj+OWIV6KKD4kupkHIocurThl\r\ntzp3YoUDYWJN+C//BT3MltFwCKyYHY+HyplWItv5KlF4q77O2r/CLAF+QBRk\r\nGdnkEjPZL9dT88kU3fZOF9gLtFOOCV+w6d8=\r\n=6rZd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.17"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "165ab932ca5ed3730a3ea2ecec98dbe108ab8059", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-/Koso2md/MLeUx4AR1vpaCR+chwq+X/v8tE8tbuA1L6cVN79BcPx5bFZ1wW6JGgxGDxEh2FEoYsuruzHWDW9rw==", "signatures": [{"sig": "MEYCIQDAfrlO4pgNI/s227TkvtSxE7miBNlHGeCpAQ53TqI+NgIhAJXAeGcmcD6wTANSZgTHMzUGA4Z54BeXhNG7dWhucOuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMZw/+OZWuvsNVifVjdwWLFuFL+zN6zMxzrv8SrDvGxchW55B0E1lB\r\nlAXJLY+D+YV7xYZkuS2BJe0qq2V8Wdkgyhp20BrU/mgk3n2qejI3v7VPhAhf\r\noBYU8HFnj4/uVf5g7HD9o48PI8OSuepO9i6k7Ov1KykI8m8eIgVEMrBPzkWt\r\nRkYOsXx6jYJreoBX83okksBQMC6JmOTCf5sHXTWfuGkMEw1NLuzLi9ZC85v3\r\nHiWDB4ArsY6Pvr7GEUnKxYENuRMcukAqDPL7cgNCwGQ5bHKhT/FxaEXADanA\r\nb0a2OvoKoj6E+8PV9T67r8mKfpj1sVVwUxSNfGE7aMEuuRWsVJnJaqVqTRPO\r\nzOTSxtfOP79GwC01mXEk7n3RaiMm9LMKnmpYi3FOhVokqTyqjT4Yyz+9xS48\r\nxfkeoNRUkN3WChrmYVORAZvsNIf8VMDAoXBag69LZuNq+xz3na8OsxutHm/4\r\n6u1W7JGtEqH+pS2VE3c9xgQQBNDb8Y8s/s2xoA28XLWAqGDFZxC2UbMYalaY\r\nPqTANBvBOXiI4djJdiVRetU63vVCHf+TJW8mXmIlKLcmL1Jzxp5v5tN0LY+U\r\n0ev3UsGrNf+o+pWFsD6ZYG2Frtuwx4Vfd2nK/UfoQUKIzKbNrpeBpfNcIkLc\r\n2XHKVRMjaTO5z63lZPKfBgaikDOPqtYOgII=\r\n=XAq6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.18"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6ac3ddb1a96b448de2241e7af8d83335aa31b18", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-J<PERSON>+u0LSb3C7i2Pqlq0L37aysfbYvyXc2ZFvlzQLpSLnac2pJEkftih2ODGDZHyWW3OdJne/eL442xi+CeKoJpw==", "signatures": [{"sig": "MEYCIQCK+qpj1z4/3XkuyIudNlMRdRb61Pr1lkIeEtGsaW3i2AIhAPoAjTJu8WgItr/uYpPNYvirlZwft5/HKXQl6I4FQ2Ge", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYTw/7BYrzmcYVFw94D8MgYLb2Sq5KiJN05cgUu7As9n8TqgnrY2fa\r\nkSkiN4UaYHQjYmlNyPm4hCk4XNyo6M6yjwP8xPXdiIJf06/0Y6MZbQLKhOqf\r\nti0OZHI3sqgsBseYZGTC2G0CZhPQw3fNcyv+fkC0SWWphfxArU1V/BiXUw5j\r\nq7ozOImMDqGAqh+/czzotgDz4uI/iN+q3SByOe+WMXQNZX5L+ZmN39ztuNT0\r\n1+JQ79RJjJXZ9HxrvE2UL2fCtl2DxCKl595ZOazuYe0YAXypvpZjZh9Lod/v\r\n5ElGUaALwQ+49zKQi3/0NBN7amwcnPWj8x+HIH/et+3ORl8eOKLFtB5rm09T\r\nFzFoKMqmSGPe5Quii+KclzgNT65cCspeZkQmn//O2+/ntTsFSSft5MAgvgYS\r\n5Iz7cG6ldc+ngry/+F0xmTflQcnDbeS/G3yWCOsqYc+9NfIDN3PPNdV71ny1\r\nQS9reLK+hXw29/cSwbnRzqTp9c8T5IjEHe/br8mFt4cEo9rj8kEKgHr8vckn\r\n/UK8Pe7rxHgTh1v2EpOqKkhOQQsAb9+MloATGfLme6+lY99sBx/mAMOe401n\r\nRr8dr8v0WjwDgpsL50I4MOmtiHxkjjDUpP1NDZXOcIVu4vWpsvhUCfrZtCja\r\ntfUp9AI2ju9tg2wUZK2aFQyHdAJnmfyLiBk=\r\n=tZrP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.19"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07a2da692c5cf1f6afea8225bc38e808df9ee807", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Em6BSO/rPa41/smmb0iweR5j/CoG5vkvGsCyr3d1EUyzyzXKIJAE2TpouLQ6ON+W36CVxwmgr9DxAchfH4k6zw==", "signatures": [{"sig": "MEYCIQCZr6ddtSEsXMbR7JViSb7QkuuNPeg8xxJq9WVhT7pj9QIhAP6MESJiy+J90idODFYHeRx8SKNMuIb3R5t8oO/mIQ0e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg7A/9FcppUEb2DXxTqW+oTq9z2tTLs0O221o6N8ga1yntjXkjRULt\r\nMAD1SSEaRpitQsdPBLSsNcG8Bw+9D1V19UVLjB6oI8zQTxuWYPgk3Mma39E3\r\nyuwO+qw02g74SrQWYp2iLJvwG5VNxzZeG1W8ZFsUpPPLp4H3s6HSc5EEYBXj\r\nPRHHJ4RoGtnHZvDiEipWTbyjEM/LrSj2PHb9bgee8oIcRc0lzeFS+HBXO1fS\r\nT33yW0ixNxaTYBtDPJKNAoiK+wfQS2hOoXYexIkbdYtIMON0ObQ88XaQX3Cx\r\nnAx0BZqznCOw7SWI943yK8c4UcK3RpTFrsXx5UrFZ8YhtS1ISbELgxfcLny9\r\nqxHqZnXcUitPIYXtXY39isjBniFz14CEgBfQvdnJnXcwmlgYnyrMCDP1XWPR\r\n4Oe5eCnaYwPuM/Y5tM39+3g5W58Guivik3GvYnLwSnD11Fr8yUZz+WjL01+G\r\nUkQh6jww2rXKCMOz2+Oh1vb4NhiTz+BBF2AMGA9AEvHUrkgfHOElphm+gaYZ\r\nZNWIPjeSQr2JKnCnhPYYW6xy9p2WBaY5LkH8Sqlx4oQfWlLr3L3twLbjPDoX\r\naJPpIDMZSXFqpe7jkEBh3J10yY5F97JvbQTWVEKt9egbXvqodY44pGtDMSP3\r\n4TD4Y7Ks9ZvPA1qM0Fbbltors3Bj3Ieks+4=\r\n=Z6W0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.20"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8f74dcb83fe2c53a7da895703f9b619483537c0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-pLYuMgIBwvVn/IiiLtWkA9ckGbQo/vsX09LFrvtY8/iNIE8BF5GLDpu1JOV9pPlMbYGIWThqL0iw1y+M1HmB+w==", "signatures": [{"sig": "MEYCIQCoAMFik3ziCIhG8AitC74YTQPzJeE/bX+BWKQ9vbDs/QIhAPNfBuSmozfazn6j6IwVq5+zqmpgHPTreV8Rbg2Eg0Uf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrepg//bb3jXqUNEDrSVdealPLKI5zu0EzxHmqXeCeR3xkIuWXv4SJm\r\nF99RVewztK9Qa7ZMDdjz3UWNUEmisRnqfRntjXcI+9xLhMIB/gKnL+L68CbB\r\nOYyqQMhrawU+lM1CZ7Wqt4Mt8bQFAo7HnkGp7ZTjupoj+m4JSdg+eJCAi+YJ\r\n7OVdYp2DrxprAF7195rMIb8CbWjMsEO7Ig+yjH0wDea8RXMRZanXXs2Hgm/F\r\n0A8ZsoQYWL/d3QfnWXLfKSuG44EWpLTAKsO78FvVVV8NCNoeUrQQM3dhsGe8\r\ns9F3BatVURCSRCLQ49/aKy4zJpv946ck5NgiJcan4R1e7pBA88Y/7jQ+fxFI\r\nXBqjcw73MqRsvBBTvSjJ8GoduEQLasMuOF316agvxXMa+2WmJlAifP4LrYg0\r\nvUJas8mtz90f7TYbHsxvH7wQcJy+WZw7zWShKs6vtU8GGgY8AHWMxoHd2zTt\r\nYu0s9eo2IdhiiZ8YHUXx/jp+MNCmXkKJpNUFrf5ikLlpUBV6K0SfrVWii3Et\r\nN3NKkUW6kIwFyy8jqj57QMdRTuv0ziAEdlssWQkYIwVoxK4L/NuZjTZ+T1hI\r\nSf78MYx0EHVDzMAHICu+OJchlatoUpm2xPippwheevO5powmRYWaP4sdxRVA\r\n+UBvUfr2nR/jrGm+6VMpdqsr0nTthFXiQaA=\r\n=QLId\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.21"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "240735de0249e8ee0fb643b4535a78419274035e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-1OgmuQs1m5Fce4btPBkEGNzhAVXETC5nLyhzLVE4v+Sg3PF/PYooGY61rcoSb1STM/KrycOfL7yXI9Key13wjQ==", "signatures": [{"sig": "MEUCIHfHvd96t4hshrfbPGG9+3hPHPpD4hbq05ANJn0Vm50EAiEA+YpSvd6/y53gBTe6dNF0wcVwFMT+rSRu8MmyZEyHD4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw2w//YBDxY3+s+JFX23D8etxx0/mZ4OpMP5xFWExTbuT0D1Td+iKA\r\nkweuG0CGCjT81XAnomeLDSynL1mEvtsBL66rZAmqQHZE9EVVj400lk/Ve1wr\r\nJwcfqmKSykq+W2YTGkxKNewX+NbHb1/Gkwip8CXFhdL9YoHN/MqwM+n/4PWZ\r\nPt5ecF8JN3Otz4BR6keQmotSNtlWymoEKf7W2452POXudJGbDRS7bTEqDggN\r\ng6nzVjUe9MxNqaWmoJkkROLwuqsFbV7a35p5OSqSeQneXQECdY58Dk+S3sYR\r\nIYRyMr19g9Arx3JbAbdSOlQtcdSy3rTD0s65d/dF+VDRRguhDikac6ZUJNa2\r\noE9G/HUla6ibVEky2d5i0Fa5b8sJzp/MwGxGSynB3ya+LVff1HUkcddmOtGT\r\npmh7G7rR5V62z2PRusyMainC+Lt7K+1CuW8EMFyoMfePXGch8bGTGjGLGJum\r\ndt3WEgxarqhjZSJhY+ADEyjryh7EDDX3nSICREDf+pid88qwU3SKWX3s+25u\r\nWWfyN7GHAB2dtu3g8fFsTdMOXP10BqLYnl5J4K038M3THCCzD6KYTmtCMfgY\r\nwK/wsr4aa+VKCKG2K5e3xoyPZ4synZaZNkmW75LfYueMnTh9NCuEGI6A9BSM\r\np5VBCkaZSvydieDp9WA+PcnzITH1ieiMZAo=\r\n=2cq3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.22"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1622520fb842a169385c9b12b4f074c5299ae94a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-B4eVgw24HeiVEETo+NTIradqiCNORnYlk74xp0/USD2DCkE+SmQnL64by/GSog4m1YhRN3a+bjilHTDQ9mD63g==", "signatures": [{"sig": "MEYCIQCdQxP75yts3icL2nx1bfWrt95m2FuTVTEc9fZK5rUWxQIhALFMcaeOnc/U4AlI6YSPNcfQTpPcMYxQ2AEV8pXUjM4K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRTRAAlgWJQ5DzRUCIY4pCFMXGZ+dg+07b7BdEsSVKVyuQW5mPp1iw\r\nGWk/bMdwmE0XOctqzGVu+K6OD1LKKc3U6mHc6MOg/hIdaRlUn5gY4lIvv206\r\ndKmepIy9qlWg8ONNNby+WxMElAueHr2bGEnW5HFd4va9lr7Ta62yMDScu56C\r\nZQg4SLX0BHXGxqjvRgHfmb1a9FwAY9dSXTOlOP59ow+AsczD7cZ5Lig51AOv\r\nKVQBFCM+jFo1Ci15fg8iTMqfJqj4G10hE8OVWcrkfNhsEvBkS254FeOaibDa\r\nqh6Vih+cGrAmRYeW1DbcZBqQrkfyOaJ+ANupCp3j2S1zhD8rcLzTW8e437Ae\r\nsV1oIbRny/r3WiqwDOAW49O4Nd55cUlthf7Oypw+h1JUn4Lsnn+TBoD14LLn\r\n9lGhkZZMM42B+TzjjsYRmp46Vm6hBrG8hC6Ec4LzzYBb0NYXaFqaann9MdOx\r\nKNhEuMv0HU1LtJGXc/nnHcJEg90kDT+nDUhKcl3DFWS4KNd8GQj+InJOfPfK\r\ny6GNGAGT2B5fC0ceQQccNPGYjDZfoQ5Fmw24F6Q76YUbdTkSYmJ8OblVgW52\r\njaU4KftIb8ecpPSCT/GiuCg61j2PQd2wE8zqmh0QHBsGxHRKxDcOjesZnKlw\r\nqJ1+eaA1LBmry15EFPL6/1uQsb8UfOgvI0A=\r\n=LeBW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.23"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d3d1b7e6d9aa7b2f774617f2652d8238b53e1398", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-9SfZYPvL4kgiSEjuGWYQaztHeO4gZnMZh4I/ikwEVvLoGwWTOGMgXjOaoGViGXhYDGiZuTKDDxE2SH5ahYtT5g==", "signatures": [{"sig": "MEYCIQCdX9F1tjnsh+1sMHk74gzZePMNKrgS1iKD209z5qbyxQIhAMw6y4Id4Ks7tNGpsbWIpro94bxEaZE43kuGjkulgIlD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGqQ//T1pMYW7TxMiV0gw44FJTRfe88jQX3IpXmCOfXoLHnZpcngJd\r\ncu6UHvkFfscspIXGFyqY8ZxCwNjW2MXwtXbRT5aAcZnl70MFC7DrT7EuBhE1\r\nddZ7e4+BK4/VEKDoMrXBFf8vo6pZWZvbWTBZXYEDehj+sKKRraB06JW1h9k4\r\nzT7f6N+IbdTFk4DeVFPgZVkWy4GN60l6CjSEogVL5B5v/2jC0R7CMl445J9d\r\nqL2caIpSzWnf+8N1muVzO+35UqhFv3LdOqLKM3tU+I5Kj/ESmbube2uBgcxW\r\n60rZWDlWZftanks5B0RaAWMILbd7yvU+HHOKiElXWiNCldHOFVfNHs1Issdy\r\n/1jHyKFleJ3V4vMmqe/y1UhBShRbq4YbIaGM9Za3XIQlYXiZ+nfx8xaMUvpy\r\nTjm4CizZ0AryTbh0sXnX2R828lxdrNvYwrXCur5OwIjf2FQY9NRKVkZP/8Fc\r\naUvgDtd++Ppr+WhecQwKjZTcpdY5Uwn1lqUwUw3bqil2LKnyd/Ehi1sXecvS\r\n4pB5/5+kSK18IFDJotMHRF529EMI5dJPpyCF0CHX4CZ5Jzr677OxN+vgM9ma\r\ncHaSFnJbdzcmCdgHwOdPOLdQqROKQJGSRFbH/HcodBoGJRMeSy+egGStbjDK\r\nytv5MB3eqvozV/CSTUNSdyspHqhgrhm2nbY=\r\n=D5rU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.24"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "de8615b77ba48d8e6b8a3d1d3c25cc20055600d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-cOD5rERr6rShPspJWCjFRzoAYgY6BqL3XDoPsZBpJ/U7Am56T/IasW2nMiu6uNcTiNlrS8gk1/r9ls81MktRWQ==", "signatures": [{"sig": "MEYCIQDfkmuZ9tjxtWgkC1ZNbY1RRSBbUl9EwoOa5eYPQxgF0QIhAKs4yCExOqniCACIe2q5FiDj+5MSIeNU59Cd5BB+TbuV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr48A/9G33NRVFc502X4vuKVvkxvIj2Lbinp7ak1AYck+X3Z5Jr99ue\r\nh/IR4CPtkeK2/u8xMop/YVjVQoWlpRymSHqWs/AkueYPLaWJGGz2uYtffJIo\r\nGe9C01zVXKsAONmmH468LZs+WSUJc9CuXGGihqIgX4WqjGfqz1etqxvqO+bO\r\nvVrxDszrycUckTQ9ytSQwiQ9oFdSn76j2wkfs2M7cTJ0wUe9GHJQp43WaKm7\r\npdyLEVdzmWi6X8FIHeDLpOrioLNUBkjq0bqfpb8Hj5oLrttpPg/ayNebjVqB\r\nhuYE9tE45wnskNw2hTSQiWSxcPJ5a74X0ms4Fy5y1Q1OaSTchFd6oLay4Dfb\r\nN3eIj3twd8CHAFxaswu/0gZ7NuVcH/q+vWa3neo9MTvOupkhc3KX4Gk3LWf7\r\nMOe/AS8p/g7P8Kbl/uF7DJAbQcYPt5vj5zN8/oqHFN9leLdZguiw30uS7srs\r\nNjMTrnI1PlOinDUemfnmqWRP/K/VtqzB+JoDUy7fi024kgDxvwm0impZiqYw\r\n1Y8j5F8dRhd+1UGGL6pCXvMrQdBtkPVsvDaD5nH4YqMW4aZA2l3+iVkACJYk\r\nLgAG2O2wgzPC7VQvjiZocMfug0KW/XsFC0/qULls0sPCLoeSiLtdDtnLJ0wV\r\n2XJhF39kvwuwPDsLCDfBm/sVXofbnGeWIjw=\r\n=6d8V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.25"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e4a7d5b894620d7a467949a820ff85cf20e71bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-phEIb5ZZdQUCdqKXhpW42Q9J9uDYX3XW/P++jKkT7eJXH8bK5uusrnJAVnGHUXZ3lvtn8msm4R8QYOG6sNe0iw==", "signatures": [{"sig": "MEQCIFTPlq1poPprbSoSKkBUfy8VtrKC8YUvbZD2rO5GVJD0AiAaglF40aL5kSYQqULmQsGfdE53ZVFiaqM6AKc02ae0yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoE9A/+NulTAhyoGU+uuSW5t6UEpJfbNLFFItcYfHBEwWeOed5mGaxw\r\nzUHo2PWr3Jer5E55jv6QKQZwKJQSUfcvRmophk3ywGdt9AkbNXlqJrIm33Nc\r\nIhwnfbABNu41Hi/weO6CAvN4AfwaTVVTtBFBzmkX/X511BhUq0HqBNavqXgR\r\nRLJZczNjHUtH59KnRlX0oDlgfgtcLi7BPWUjly2Rsh+8I/Mh8ONHGbzVTYIb\r\nt5aEThGxxIUiKpqOj3iTwHY0GnMMIMpP7texSYufv/twMTU1Bj8RssI/1fx0\r\nTjVJIqd2mHLWjnHXfgc0d2119RMYCSt7d8k7XJooEfvHbvEXVTe9jiAMNEiF\r\nkinyXYRGHL7+pz5AeBop2tmXvxleM92w2CABUj+/I9xXUiUq0pFh6SrMLTDL\r\nfRgGeStT3yL3u4xyHA0ESsl53HJaL7nfBJn9IIOS5ljfiI+7JL5QOICjI9n5\r\n/vaWs459r52Cx9Hy31EX24wvwVNjZanbkHjLU8EupM780/vMRIDqhOKSXbZk\r\nbRdSmPB+j7U0DG6DBtLKfxi8V35LR98TIhHHlfKAYqVT29+0KgHorfv11For\r\nwomfZ5QqvVWFvsx6LR4tX4WL3MLKeGU4WvlDENg04B4I308DeIVxAWoLO6r9\r\nEyfIT7T07sA1WrCQoWI5Daevsv84xFNBWdI=\r\n=EhSV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.26"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5c6adca2a8fa0c268272fa654df99cc010f2f5c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-P6JS5NIAewihlFdiKKoPpV4jFSqttPHh95Z5VIeU30RHmbmYmnaCi2ue7gqz4BN4zePzX8F79gYD7uFYxpD8Gg==", "signatures": [{"sig": "MEQCIBy9gu4ITYr5ZwW0imugwynvU8UCFrb00JhozLOzzT7NAiAsQMiEBUyFPxYDnDjh7CyalZlL7vxBp5cUoKF/byqBrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlmA/+N4FvIO/xmdagd9BWScr+dv6tgnEDkH58ExMigzvmikYiIUNV\r\n9SNbE/P4uUJgHhUYsS72O4B8Yp/LdETO9oDGNt0Sr1AhBql35h2AfwF0nlzg\r\nrg4CL9OM1MqMZrYfdkUvHQDYscQX4PrCX/+T1smvCaj+2HbE3UTywyCwLL/S\r\n/mj01bcSXgaTwJXgUi0Gevnk8XhhzODdRYFB/VffUOB6VWThU1jzDzmtNQcc\r\nVijfgZm3Ex/tbzf4/n7sUfnbubZMPFyTWjZTBx3MiCnOhU6M09yNn/OaAMd1\r\n2eGfbUXW82nuTxjhm1eBUg+IefEptcse+80ZsZq56vtdZo54a3EehFt/6fT2\r\n8cyai7hRo7Ur5EyCDb83YAGSmXv8FGp5WeU1oqZZNjLc5B9hEIv0loR9mMzP\r\nuQUoN+9rntnyF9hB24SfD02x2b4HVRTxTDM9ZtR1rzTvViPfoZ8yla8THcLt\r\n6N9UXBHdfEdfmR3HIf5xbeyy5/y5hGIs7/UKzn6vsfH4EmE1juo5JkKVd8Mq\r\nmH47HKic8Ri81g+srihoFWbxZOuiQ1PlDe9PpCmWOkTTjyt4ykX/zrD4nGhf\r\nPXyMCntlW8xng75/2iObMV6FE+fvWtjPAztNdHZ4W4HeV+ssxpuwS3AJ4W0n\r\n/vGkTk+LILXxxdzXCNh64z+HdBiNh0UWgRo=\r\n=OBaB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.27"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc149999e796dd439112278fa824496a9a50dd81", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-QeuYCuPVOOHfTBBVhPwx7P1jyMizzbNQlrRY04dIWrtdaSMEk73+fF6IuvlTm70RXBSaifrFVqOUpLfAEdy3IA==", "signatures": [{"sig": "MEQCICmKe4PxT+PkwJ9omLfNMbG076VoFDIleOq+yELM8UzJAiAjdfsJUuso2JYCRipB8koacilW3i0sN8+Tc83gHuBaSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZtA//QqOUNFRcneldHRkE3Tjc3x6W9jY/55mdRNjJ3kyNFA3IjaQZ\r\n7A1sXx8g1PeN2UeAsLSWiQciBfCB4G1/GbDqHAMfs6vpDacQCg5Tv1PnFRbr\r\nbTMsTO6I1OqTxxo/wBd+p9O/ETzE3nbPDqlnCTYubIpmkO2T1205JpxxBWFk\r\nOIbRMn9UeY/SBNT55e1weQXTiRYvJxbkXNhxfd6/lMj9sqsAx1Zp2MWU01GS\r\ns+WTWTkHKhknwB6u3Tu/zpH31eWyNGGYceWAdhEle8pXHsdKeV/6AGZ5onhQ\r\nHNpiDu20m+0qn5Fscfhhuxfj2FJjRqsx3eH7yEs+ZvUHlplBX9ac4KHN/j+b\r\nnBmAr2OmnIT7wiqisylqDXZhRV+I5S0gqDxp0F7r74VWPz21C87pZY0aGojh\r\nVGtanNwvIuu6I9NcO3De4e4PZHqVKbY2+wmRiOFlbkHsEpy8jbcx4W2t5w+G\r\nenHFaKOhwizwCsphyi9ty90fb3EDippq67EMVcxJdIsvflfnhKcXoA9pMuTz\r\n5z0nHGKChlga4itwpJhYn0SECLtwn/xOCMTdzPQPo5eTFvKXqGnFHzJcmoZ3\r\nSf4y88KS936J9AupI33ui6b/ebKJbGbiX/UdMfzZeCA4X3zpMmDXv6S7gNYT\r\ng83OtKR480v7eXuwL9mQDRMcxh7coRs8nyo=\r\n=wm6y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.28"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b985b08af85934ba0d0ecb769aa779a2d7c8ca5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-g9cIgm7YGUVxYtoCqMxf8DrTJEFghRvaJMafIkSHcV0YBz82khjihIgQfn2aikEPDE/rhhhhe2TaqZ/eliYIcA==", "signatures": [{"sig": "MEQCIGKBPtdZcQf1HwXzdev9ajfA/f4KK4xSjH/9LUdaGl/EAiA0k1abZfiX8oW9JqVS3YoRXQ+2Pj9ce7lI6RLMUUv0Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojbRAAoggYbVUjTukC26yXT0yWlCL0C4QB/spzErfCmSz5SxF2vf76\r\nCZG8p/z3wEfVKbtnr6wtMdoxhIDMjjyQt5nSXmS8PEFcQ+jV1AI/B87cxseC\r\nQGVO0nzDhcQAUErS0/866OG7spMS41izjWPnCKV/jJCOj1D500gBz48fl+SO\r\nwOp1kh2CguP86zqOVgLSyjWV7pRj1KkPnv7bPGnCXRWryJWeMBW1LbasVrdU\r\nqHQ4moE2Mdr03zYwdTtsskDyDkM9p+VybiBZj4sYrkzO0wn26zY2SrfafcF7\r\nFh2P79h4sKSWUwRLHE9GWb2fA9G/VSI5bmso9hiYfzDlTxIL7zXXysGwwiml\r\nggrXq5mbPWB72oZZsZmLGTZMbZFDBCRk6Kt6lYKXhRylcCDEIscW+kgQebhv\r\n6v+HzEwF0xr6e5FasDM5hhY4qs0LeXttQ2vpcyCUPgYN3h5CiBBeg/AOT3NF\r\nvIDjow2za68jDV/MoMikPy49qxmyYF7Dw+160GUn2Eg92wEH6B2LyISVnjL9\r\njQBiPXGF8sv9PUC4EQnn6yNaDTCyVYowHfNPxOy/sRkMmrFRCwq1MNdFva1S\r\n/7RiUJTPuzzlGCjpueeZQC7gQOum9u80QWBV+ZgQ/ov/a1ph3xjP3E18XWjJ\r\nlQvFQCFk/8IrltTULT32ajPHjZjz5YWobAo=\r\n=MYIZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.29"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ddd6c40fcbb83f51d0b3e2bdb84726b0719ff520", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-b0tpGrbmZUTLZAopXyuj2fXRpLcykBv57nrke3DLbrp7TpMGnnLm5Q9rG6ztnyAMOOkD+4Y93qhQcR9hREjmew==", "signatures": [{"sig": "MEUCICaKZc3wsunDYqA6SAxrp2mTSH1RzpBYhgRuYo/i04cuAiEAk6P1JAEy3vSzu6ezrQaaByI0h4ZL/H5Pu0DpjJ/92f0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokXw//ansiqksP4dJ/SoVXoXczFo89s93vDCfCo9PeDjfH0rrJg745\r\ngdDbhVCElC8258TMxDu2rzDESnCEGC6Tx28E+KCxoPbPoS+kyd2X8XAKvUAR\r\naGHcrTzUEbzSjM9nkqXC1Z65WMWQc04lIV3Nupi/MU5nolDAO5wMqwLiRHe0\r\nabpo5CAf4uM4J2RvmsK09ICBFDZVsvVr+GvL0lCHN/pPEeLO0WJVhJJ3Chub\r\nGIlP2R3EyBvI1LYlDcz+BvDEisap+Vl1GYPSa7mrOqUfODi0Ft2w5szfiAkS\r\nROI3ni9bks3xUsvrJppPZu0CUYRsGz568M0vxczjRg9p4uHC43b+idaqWmmj\r\nfImxkVMMj0hSgxOnsVnbly/4avOba9xTZehRAN8GP2u7Z0di8uohmfYK25Zx\r\nOwzMXRlDpc3634YZduuxXLHmvyfQibiPuShVIW0LVEsEb/e4AcBtADAihQgb\r\nOJjTcAo1SEmC2Lm0zNz4/dOQwXr5LgTZh/N9HfGicsT9Xky387DUWpM1KAyY\r\nX2UFchwmk1fhGMZiCcMxnt6rIdbcrc5tkNYLgki4Ley7mnOivxR0AihQctH1\r\nYsUPu4yRiQFMaTuoOhT1XP04y3UkCwW7UCT3REMnhB2/CqPkvVRHEW+nQPku\r\nKLT5AJPtWCm3dTRiclz0CdWaNS2cmTvh0e0=\r\n=F7QD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.30"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "72757147b08201fad3b10d1011100a10f5bc0083", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-0qb//WDrcRHQk9e5GcOfL/NASYH4L7MB+XgUsy9F2QlW7D1aTZyxpdTgflVrHsbcyHx7ItBG/I5WskUuQ/aVNw==", "signatures": [{"sig": "MEUCIQDj8UlpnRNhqBlRQCRlRRdRVhmNDG06eEbPk6CHURTYsQIgUERX4/IdQMYjsbITLxsqj330ElMk6IgSMJCNZd8D1eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmop2w//YDUDqY5g2YPvJGB9A4XEmptrxeeiz9/sgd5hQ80ODQey4t9P\r\nF1IO+gCXCq8EPo9B8SE4k6kw+4tPBAt0PEN2aEjXNakI9C6Pd5aemF4F4v9r\r\nSK39WrsId8Ax+zhLfGwwaSLGiaP1P02Pzi+Vok44pfcQ3EvSmniTlyTIqzg8\r\nXRl5NE0cL7pSDy7UCPQFu6NAn0fh2KrJ7gD7j6eRYmqHI2OvqnhDt8LS04J+\r\n+AsA5xuuwDmyYKcP1yyy9MwOc9+5nv8oaXRpWk0Cu6kO8PTCDn7iCYrDgIiW\r\nY/3fkmmFHxQzDuFqWzhlz/btEclG9etx3fmJqf9FzyoRitPeeNPB9gZZOwYB\r\nTcLahIVg1ay8x7lQqcxAU01seklnUbwWvSKM2XaDEjssDl49+jz63vdqqa9U\r\nIYqy4k5Zo9/XvV32Kupua8OH6QpoRtnXilpPTzceH14muDR6qMfHMsK0Mh+3\r\nTNNxwX1EdorgPmOlu6B8lZvigg0+Fv2FVGBlZJ3aq2LYjxmCu+97qJ4XAu69\r\nesy3Uoe4TStEdbnP7n4CveNr5zH5Modozov+c62g/z9JafMEouvNFXtUOQ2W\r\nw12j0nxIiDs0RtDNY41medrkklPCd7KK8BapgLGmm6M4S0FsWL5opkPJaZis\r\nM32MYJbK0sqGgyAVkD0U9E/Q/L2CM/l8DK0=\r\n=Vkb8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.31"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7691a94a366439aac5fe45b2252999b80e8da672", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-osxfMCiC1QAxqtVXlMBbX1bZ68/AeROLcC351Uxu5rDuOqeY7BEeDHDtxE4M+on1EJ9E4uF7mi88on5CXNroZg==", "signatures": [{"sig": "MEUCIQC7AfZqkGF7eiiqOdQ7PDSWTpXxabZJi5GSrNxRz85TMQIgIJxHAFf52b1KQxIUpN513o3Hf+WnpQYnEpur1eZAjfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs6Q/6AhTAnkqL6rig71sKb/rlLcJHQGOUobcRGjdxXskgAmV9CYoL\r\n/zzrkNEU4ADLVW1jB04uRhIpFcX4sh16kuXpZekIpdJYjxyLY4VW/+X7SOyt\r\nmJvAxAWyv+u7JnizcvBXxiKn7kphXXxqkgQA8HEkbsxWyKICNh/2DjSV8p1S\r\nOO1Hwp0fHNf106EaGpQRVyTu18K7JAJEQuKZ7yxDRWYzooxriUhO1JX78FS4\r\nRyGVtJ0PRgIt5hlpDjpfbzHs9mzZTQIewFPIWAKXliuX1T67Gvkj5VKTzMmQ\r\nmvspPkwiyRBHPgZ54FT3zEkXf74TJt8xWRaytknj7paQjDMfgpodszw4mlUU\r\nFTpKRXtRAu3fhZ6OVj4MHWclWiyDwQuz7JCR3uPP1tsDYY64BoyePivaNkOm\r\nXVC0T7kWwEiDUWjcPC59cDCvv6V/YfcbvXllNwROwtAxMGfHzmFbFWk/9Vrh\r\nt7r+yQ6MxaFchHnAl/Q+v34M+9QWl+Vi6Nx+AjS0HE/a5W3OqmzhPr2deKDI\r\nUQhIlDw1chv42NGtnPM9FpP55eb81cW5X0Dv6inB+gGx8aY2ft8pkLVTQR9f\r\niU6TrZtLVJ75ywoYqMqdfjgvdKuUrb1mRf+hVyhdUjcc30ixKZfdwiz5NEjQ\r\nYKPYuFflFZMwzRXka2tSpAE9PB9ymoGzwjk=\r\n=rdJn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.32"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c07e4c9765f9bec7eda0cd329b3c6f2205e271ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-rxm2CASlM5+q6QNluzH/ictrdYxwObdeo3zo16RjIhVZ98Jxdhe6M0bXmg7Nyv4q4w5jEJr1CRP43GUU2l87sQ==", "signatures": [{"sig": "MEUCIQDOtSgM0oU7MZgJslGJFp1dqehkEf6QxGvKPB8O1zZa9wIgW5f3Tt7nolDqwSbh1Zic0eTeYvWxof7/aMb3mFn2mf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniR2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBEBAAj8uYky5vEavxTvDvg54rYwZl/u/03Tby9aImZDiM8Mc+6tHL\r\nht7uDYJLHXz9AOd1Y3Q0USWNQZ5+aEZchhOaF5dkGvzFPN+Pi0UAALpmDdkN\r\n7T0i2rUE98LkUIDWiEWmlkq48W5AKiaKn1f/bZdfgU56g0V8FwZAHwBVxfix\r\nFKvfKFzBU3D47+OUgb5fZMMI8koyAZiNq/6NFUa6uE4LILvT4GlAHXjea70q\r\n9BUiGacYve+ISCw1jI0EgmPg6FCkXUg5qpICJsOUFI5u5w7XwzAbqQxAQfm0\r\nMSzryRv8AQfBfIMK9H1Sqej+0A0OygoLYyTWO8chlL0XeZkugcj8JmVKeBF8\r\n3g2FByyYaNKvh0U/36RsPgrjp259D1ZRIuaG9JMLcZBQA8ai3yZqgIMZjEqF\r\nwmxqbKoiPviUxo1bOuapnnuHsrY+GSHmYwajWkKiNbsZ8da4umGv0bw3Vzet\r\nlbpkvcFq0xtpQz03sW29WNqapbgef8DYIjgNsDRiLZgMgGDTCc2E13ht35n7\r\nzYEuW+w+a5CQwWJ5u9ymsFVZKnrWGYif5Sh5rEE+D2PXZGmbF1QcQVtHRmVt\r\nEszeqRfGx/K92ofy4Hr7YzMF75v7YNatC/XxxoWgtjdtO6dFMPggc27P6DKf\r\nTmtumDMmxGP7ktIw6rfrduSzRGF40RzPhyA=\r\n=A4JQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.33"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bfa4eb71c26009bfb94a80e38845d787e2f24261", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-SfM0PoWhGLjmYyfMbI2GfvEYlfXvkYi+Ic9h5ZAwF+QXwBFFXNEskYlJpzmOtVYGhVnXh7zHc2I3a0WurFy0fw==", "signatures": [{"sig": "MEYCIQDfAZq0rpN9JFL2sjKF4o94bPpWGLSnFVKAwqDb2c7LKQIhAMWY63eHK0yNFIKvvHfAw6a/q5iPqgc14D5NMAGwbQzI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGvg//X+yynIONBboKiIiDBgp8sPEU250QVbfc9mvhxxIQkuW/+5d3\r\nTQceYwGw/owatMvugYNonfxxEV4RAY9vjbYOY5wtnCVJhkcCygct9yCWFzzK\r\niM+o6izes6g24lQ3L9+D2nzkDp47+qBlYAg+uEYakA/j7iq0lnJi3Uvohjph\r\nXR39qadgMORETtL/QnjcpiUM/5+i4dfwmC8273oHw+YHLVFhZwVG2UinK+EV\r\nc2BlaCbMTPdQDZx7yzAEDGVEfkykKl4cQqpyFyEWYn7pYC55mTjVzbH9fioH\r\nG929KdiEp+YMRbbk5giSe1KxgNA8FcQM3aFe+2K3X0eFcH80FjDfn/BuHwoY\r\nJNV06zPWYg/3WetfRJNoo2sE+G2aJPQOsiQu7ww3znSma94PSsNm+EaNqKh2\r\nue59zwUSkUKs8HrHKnrXqv3QO4ac8amHRrgPzT8n3wDvTGa5RwHYXzicWevJ\r\nmALuOx7Ow+ETEa45EnV4cK6lZMP8MbKoIdIRd6zbl5RJ4ZcHbTLYmKgAADZo\r\nygVVGtkE4EQnOrW6KJ4gyrE0V9ryAIuL6ZSIFDSVW/3eCa7JiMm6vn2BZ3rM\r\n5TbiyhHpEMX5dUgbvrZnjTNZM/Ki6Q+elHLsvIaT0uKXuQh7GYujthddNmXU\r\nTwTMlmNqOlVLkGYQj4BijCWXaTpLdc4Wp7s=\r\n=FpCG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.34"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "83f5d25668374a148c8f6e7eb2d284a0ea0b5ba3", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-dj5hZ3JlpVC81A3l0eg6zvv4gChHBngHKGXfX7y/qCCIEOgUr3wK+wVhgsxlXmz0bj2d7ut8gj7c+ZFgjJ8sJw==", "signatures": [{"sig": "MEUCIQDbAIXJaPvMLtZjanwRONBbpRf4gn3eypRK/RlaCHRuRAIgVDHD8hWLU/sS3k4Hw+YWej4Ca7SHNH47xeWNTaX/yrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptIhAAjZs9cUmVw3nH/Zx1hsFINhEQqqwPUFKnhwWwkNTcd+rZXuOg\r\n3asJ+wGhziBNTJk9FiBIHcUtg3dVaufySNWLI2uLuTENT25tXux4fpzJyKmK\r\n909f0vrq3glQVpqc7FHHUjvygXJKnLibejdpooJpXk2OnkIqZ9Y4vfUbZJEn\r\n+RLVvt/5MeTuennaFe9BdQ2w/7FTlsONXAlRYnePsrP+xsSknb6uJN1EPT7K\r\neAw4lCP2WeUkToxGsUfnmfooEFTqZTWvSpcnWS9DiOh0YIJPcw+DL6paa96m\r\nuPZdTjiXeKfO1yyEGpNz1npc9I6SnjzQY6cvD6CCBMGdVjSeirmohB8c5lTE\r\nPYw0tJ59bANA764H47Skf9ICWfORFydnqplz8LWtuYJ22Vi8716HJ92VwbBJ\r\nO5jKN5t5yfspQa+ag6jPiI3GdApGQooV6tld5gVQCPZi3zwePLMtQlr7myT7\r\nAa6h3Pdv+ZoEAYjS+PTbvXo+3ff8NYPBK9UN75t2rWrXgSo1N/ApBgNnc8/x\r\n9M8s2jiHijNLy7Hvyw4CDgz/87j/A1HZDxyVP+/miy7+2g9OsRctezQrBy+R\r\nEDXB2SI93LD8Rx4RL0kCtg6hTuMRRpf4Zbmes1xiaCWSAFLryUc1H1PsqeAu\r\nwr0ML7CZ3ISKnKv4GifEkEGyqT6d5Y1La00=\r\n=Zfic\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.35"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dade4c1d404d3e06ad354b5792a358b4ce79ef9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-MRDj5SWYzO8P2mJ1KMUGGPAawCVMqiMfiQaopgIWl9eNoJi4c6DRlv2Bfp4evV/CbEsZ9KZ87joEJleJu3mzZA==", "signatures": [{"sig": "MEUCIQDwQGb/KniF7CQvI6yLMMoZBP6vKoNvq1EIHdZOEDHB/QIgeHu7UBwIa8hhvlSJb+XhCeWgPqW569p/iULWC6PPI+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOY7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNIBAAn9l9GpG4HuUSWidKuDia16j6k6lRhD3o40iIJfTcK9hWFFp+\r\nz0sC05xqASk9p5rbNJ6FclArybDwvFy/gisyNcvPAj+Qq7BGupxbDGbDsB8l\r\nYDkbSnzl7oSpzGsUqTqq5uPb3tqRYZ+V0NpP0hEonkzBTT4ajsWp1RcgDMrm\r\n7WaaR97leWxQf5wWOHPtocwvwEL9gP352p5jFR78MfRGw2mG4j46Qu9AQVes\r\ngLLGfZ596/vOftrexCTZ+xv9ZITXDGFBIywVmm5gcPnWPdgYtViQP4aJZpWV\r\n79GsAkFEQ9QhNk0EbWwFEQFIDhSJizW+ES4KVxK3TkkkPAzmr1dCpgihtYQg\r\n3geqHw65MnGtbdfO3THepDiPtBKZT3bVYe/ggWxJwd7XS5hxWBWG8lhof17d\r\nnKNBwFVofI/skcEt5FdyQ8jQC/CHbVduolWNY56cINyYThekL5sy33+iIY3S\r\nOn+hJ4tbHSrVprWgfxPlXB6f2xWAThPnru6r6W6uG67eeRdUaYWZ0ReLwFGz\r\nJ3I2PIBR6yU4Ml/g8aJpeVrzTdyWJRM1cKucIXOTjkcGuakbUTYSvtDKCpb/\r\nOGEW65suoug+Pk1sHno7g3IJSw1KoABgqmtSltLoKSTfOK9qFcoUDAw1EyO6\r\nNX1sx1LGgUJE/94JuyLlYY8qE3xPNxUjWkw=\r\n=9GWj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.36"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7775b250f172ddfb0730d23c65c5083cba157080", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-gqmmMpfqfKC9pFMkc0IdNZhyjLdJd/UW8rhBOgz6l2ZYqD15+xyrUzXg8dlmCSPhZ+usbUE52UC5z4kV6H+zeQ==", "signatures": [{"sig": "MEQCICtk2vx7Zn51UCQ7oowMvxuUWCQyO3Qf0j32ffHep9w9AiAeuGnjFp5HMyo/ovREwhdpMFuiDoJ4IJ+VnhI6yRIorQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvsA/9FMeeJ7BMX+1K5NPLy6eFfkTQOfCfNdys87qOeMwZiT5NHcq6\r\nDP1O4Fsm5OXyVQN6yZSdAL9a1UHCVP8tGYpGJ02GizITkAT6oyY/q2O1NJeR\r\neNWIsnjfSZezmbbU7+hJZ4reCHC2TjtzCzwjeMtIX49+JPYxAlfsbCsPt0Tl\r\nWUtEKYWBofUx7pm9VKoyv7ukEEdfrkjPAnED/asx5heO6ZK9BdS0fsQXqzju\r\nTzqzTBnox00Dgm9kaaLGKlMET9gthtG74gbofQtEmt9TQ2TA289aMx/IhbYK\r\nBYEdQqCeSMTzKiaLz6f6xCN7sSSqF4Q6roX0GTekKPLgbCu7J9C3BIELgv2v\r\n4vcN2gSBojz8EPjRex63izPWQXlipNg+xLqODSmzf6sWs7Z3EQntXEBzbUWe\r\ndJ8WwLnYXJqKxN0/styQ9rHtTTzm3qKb49q5gKmKLElCL6hGDmonm9kmqkf7\r\nIDgHrhcuHZ5icmFgk3ptbO9CsdD01tBul1ezf4KbH6389x3dSPQJMIkNjkIP\r\nzoJjmJdsU6rW4Von5uhzu+Vbt2+dbbOKmASP7AgqvHH3+lnnfz54yOZzIiYJ\r\n+hkhN0/jrHOQHWjzQGy+c9M9bxd32u5ZY1tcBdtzvkBVOsWsAt0JnEIfjPed\r\nHSvzN08LnDc4FQVO3cO1y++WAwiEoxjdTDc=\r\n=yoGJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.37"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c4c54ccc036513fd656d342df7de3bdae7e683f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-Yt2r9zUnYNYmvV82Y2xoKG32oOrPdSJ7woI0DH/zc/0tsQqsyQhqkxnCa/lVTjFB8xai+RTfn29SR88fYEtr4w==", "signatures": [{"sig": "MEQCIB5KDno7tPTjFKco8/75hjxsgJ9SKXxpsi0sUQRwtNV6AiAk5njTYOrJUdGfwva7nV9xJCLffNKyDu7aoJDfVLTcVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0n/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqsA//UVlWx1l+1wCgROB0BcTa4GnrUUx7QJ4Rb9uwCC8rExVEV52l\r\nKHcQMSgB22YcUotfUPa6C+hfoAH8ECbecDasG7N6oh5dudqSgRuaC2s4mvdN\r\nyQk9oEwC5v/R9WysJ6Vpz6tX3PvwyB0MIgLsU+OdFY2oysh3YU22O3gBkTf9\r\nhC6uQyHPEwZHj/YmRbaD0B/pEMU0AcJ3AfNvVH46Eb1/CF7CA1UaJBCB0Eqn\r\nZry4CH/82IHxeKfeTv3ZTikcuGkXcR3+ySVU13zaHzGbdpUGHUltuQGeF+eR\r\n/BvH4L0hKjAcf6Tu3Bp7hU9d07vsAEIUudQMISsBuhtsRI59PzqmsyM1Z8ad\r\nN63ppiUsyxbAvIlFInn4FpzxBNE5xwC6S+/iNvosAckerSGyqlPH77u5EnI3\r\nThPivGcH4OXheYqg6LEf9hp6rw26CxlVfPZQVmvUrU92BDvTp11bTK4DNfJu\r\nKc2XSXquNPM1nChpEbHt3sjH84rL4qhaRR8NGUG+IUU3RcO0XrtdDHSylsTl\r\nn8zpMQ29eqCfZhvd0po8fK8RoILDbHVvmR2fpkLktctycDMMNKjiflHSXlPA\r\na0Ro1ERS+B2LtB5/l07ZG2rrJjAhZgqm1021ABISBWp6/hd27ir6dyjjUuz4\r\n03rFGzZnP4AXGnjLoZ2Oahe4jmm/PyLMLFY=\r\n=bmoK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.38"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3d66560915495b04db1a4a166e50d574be0c39f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-062yHbJvyP0M5TWvcYEfkpl5t8C6A0Pku3GOT9eDNy7yk92nl9z8NsOhGXLbMZ8IljfM/ihFe93UlUpbbNSRsw==", "signatures": [{"sig": "MEQCIGF9nr2sppHaXRbMZ20k6kFdOsZUR2ZBcfcBUtrHg9hEAiB7EcGDD2WA9OPrf2c18fPRGEkQqBEhoDX9oFQyp38ytw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7Vw/+KfYPEO7zV3w8KRiMWkj8j/lDgVcPrsZIgHfnITPx5B/l4B2a\r\n2lTEhMjrKx1mSwh7TwwGJ36hc5tswg8Rgr6PLpnaPNbVl7BzZghfswAG5wff\r\n8Un+ChCYNTZnaoSf0r5qKAaFM20/d4NxWSQZgy9zRLgoT4m8yDboONp+Y8Gd\r\nOIsnQHQI6cmyWfsX43KHgiBzQHmZEj8ts4DChvbKIPz7pki4crNxtaNn1Frd\r\nueeBnYtla7fOGxeshMhGZFU0/KacOgKQMoCyw+XlZI39zt/Xy2+8PHEzLSHr\r\n8hWxdWY5FsyqNZeTwG3Vg30qQC4uP9JeqUbdZfiAyCyWRd9vBWLz2HQvjUQh\r\nbKK3BRLBnqIquCU1uN0n9m5Vus4s22lOnsWASuEZdoIOZcpCoYhwlIhPmrHb\r\nVHXy4JwD0XwT22UINGJifuGeDM5vzzhN1yX2hlqbrOesHFEln94fV9KnDcaT\r\n1X+s1dFSU404r6fvoE+bbOH2ZOxlhE3Awh+dV/HRHj78djjng8VM1hZyLwP0\r\nDPDU6mM2TxvwprFpLprIWcaKpNnXH/gqVP6SgfPL77es+0mVs/iUaRkS3ocw\r\nmJGfJ3W7ypl6jw4Sg1fdgxp8Da+Xh9ToGABg32z1hzldmEDGA4Pl3Hj+2WVS\r\ngH8QFq0jvcW/5ctrrnptOgFOLZmUYCFXrqA=\r\n=ZmpB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.39"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c4674519f35655de8a2fb51f8d146363898b6c4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-AL0JtYRd+Mk4OBYrcN86CrEyEzwNr2L7e0tqpIWHIdaMYmIAbHQBcsfrBT/tBkBfs6CrF3J2dHJnfJ7Y0YAo8g==", "signatures": [{"sig": "MEYCIQCG0f+5D1ENGg+wCVmPhScflHFWqFQOBn58+MoaOafF6AIhANJShSgykkOtnpOLLc/UR33SBAR2VwpLwYl1qoA+SfPh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz97ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmri0g//f3k/n4hGKi2XshspObQjAIQpP/s5P76oC3Hs/DDEOG0OTDGa\r\nmO8VwQKeXjD4eDQGRmZj3U3ze8XvryOomfihS2Cc2aWyvDPau22qQA7prT8G\r\nzJdgInWDNBq6jrd24T2vUNwsS/66bsszOXibN15P6KHl5c1fna4u+Vi3P0f8\r\nemUZ07wPGl4lTvASCLxX7X2zsNoZklxFzEwl/wh4oFGwY9qaYBEO22561Njf\r\nN4HWgS4kDvjlGM9IJnBNu5o2AFxJcKJwj+eVr6iYv8PHPO0zu6vf0qAOSgkq\r\nDAO6mfuEVkWNmlBnE88mHwbteGySFrlGhef3AyKIcHR5yNSqsQva9cUxs2Pl\r\nuZpRDepkCkyQlWyAb9Jl5IPOuBpB4Bi9Cb1Ehn8kdBg8tDF85sv7zeB39WMj\r\n9vNut4P/EhBKg11nYintBxOfGX1M/v0NwkzcrShyrSYivIDFc1am3IBx50Ag\r\nz60Qn0PaQGa4v1mXVTKxZCtWCGmz76sDOAhIGKzaDLADPM5Lc58FVsZLk9Ye\r\n5IafbgAIEsYr6UyykgspiU678NQgEje9QBKHyE+/e/jrbypLl8pFcctfEZIr\r\nZMXIwUAZy698cBWeVhyRny76gZWWIlOs6aSLeNwgG/E+SuljtR9/6qcwdsU8\r\nZzuSWSdnGyX2MjvFFQdFLNEw2rSJpIkiw0Q=\r\n=ppJ0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.40"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa51bfa2332fa5f1a35f331a0f86d14c0be9f07d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-eUQC5INhR31OSM4eGcsZ/30IW10ESqmNELCCElw5QoPncGdWjvj3KSfC+MIQIYw8LEbOoOWDmM/oMenAgdHh9A==", "signatures": [{"sig": "MEUCIQCzaZ6BBfEHvKrdMxxDY3CXpOHQBT+qCcmUmL+8Ny22FwIgaZqOLUCD0Om7Xu18J2ZgxqfS78oO3kuAN7cFqEU+pZ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiBA/8CcKa52wp39pmg5pe9epCSxW3LNKdJaYfIhp+zgytljfvK3tf\r\noP4yJ7+VcssXwGfuFIE8uoV46wy0VzaCwlr7LIIhXAU9j7HByKWyZ29ZTPZg\r\nPOcDOC0Qlo9b33X2/3cyj7Se/kcZfZyf4dqw2ydoZY+rDOKDan981dv22Bdc\r\n0PkP2QUocHtlMZr2xoZYcn9SukY2oNb86znjoGGunGr3ViOLxea5UuS5Mng5\r\naLTqY3gTi6MYd+M6MbHYEnYyHXtY4aKbuLfZZ8oiEBWlmscKP4+3w1e/kPeS\r\nheZBOR/kyPvLDuszaIxnr8D17dyFuUcolrutZqEoiOqVgh+89goRY5+fTZEK\r\nd8dHHvUn1oF9Oz7AtH/8jAyLPgIEz61AMU0kD6p/H0P78T+Zmxt06UWLKCTx\r\nszHftjA0THt9sKYmx880t3hhJLaA3VWbf9l0Bx5CoxBt3nVGgR9Wa0Os27+W\r\nYhNC2UruL573mvHh4lOIAJy5KqRHzgSKX8XfRSUbmZd3h2yGuzvfNruyZu81\r\nlqnYFobePfxp+ezOOuVFEswjZ44rMzd3mrymN4Slt5DsypBvyz6BDG5LN0b0\r\nFz/0RPsDXTimPW2Du4oYc33pOPR2Rx1O4lipDTFpBQlr5HuP4SF21qUGMO2k\r\nji1+pUCFzJ20/eGTtUVmvogVRBgyAdLpoOo=\r\n=MFli\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.41"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5474d21970e500cfa906ddfd3b8e16a5c2a8ce4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-8pOqTA8jAseCSN9QKf9Ooh2xyi4Ei8Cxo7/Ye95d7nDc8rhSO02HgnMZ4S8U3YspuiyvBpcbRxdoY3ywJEdTlQ==", "signatures": [{"sig": "MEUCIQDkrmrukL3NUKLuHn5oQAP89ltZSB3Cp6ZIiFpJX6wfhAIgQzu+A53Lojc3iKheg4O31mwK0YDhkREkgk69+vuixfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjMg/+IFiASB9lW+ZHSuiMDOa0vijqUCJCuRz/o9Fi29kWbwYbiwnI\r\n1umMRrDpiD7B/oze6ABtE1uU107McDmir5+2HMnaUdye4r0lixzGGvW/A/Y8\r\nxVmiAGBZNg/SIetbeVCra9JQgRhHCFxP6k/4sz3+kp3rv3wlzaUC7KcpKE76\r\nfaTdUpTY8vt2LcMt5paRgkHBRRyQjZW6BpiAkaGOw/+ahaYFV2MwjfyrONkO\r\ngmFpdd1fNfwSzwX0sO0lUAExAzkA5wiaEa1g9XHRT1VVbvxeGJ0TRZmfh21u\r\nXaMi3dDdmAX0frjt4siG52CIfIFSCMAIxR3tyvjaB1jfKyQjzBZ/iOKtpqiN\r\nMZhBa3yKxSMcONC//snG93rnBBYGrwgv325FKXEM4nBHhmbkCP3X++NapOhy\r\nccRHfna0bMupKjuFvBm30xEcw1rVuU9HTTIPr+rVkpNkUEpqE33SQ0r/txys\r\nuKTLhr6vS4gsL5BjBXYTc2qC2gE70iKGVJpq6VsYFCVRRb+07M/Ftlc0QlZo\r\n+RsF97fS3mkJByXKyb1pofSA6mNGGcSmEEicwGE8XKnS9RnQqA/izrzCdteM\r\n1C+78P56ZWOwJNZb8IFaF7pH6m6elsLxrD52Pu6fFpzb3Tk1wz6GF3p63UTX\r\nFHw+YZ+78ceti70xt23uefaQoUCXKT33rAA=\r\n=9+D9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.42"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3060704ef6e30effc76b874b18de3368249ee319", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-4VYF3VcRrSX4/0ynXeAMvt1nfg+hRF35X3ImC8Ki7kXUX/J+7KwidFmr/xaZ0dcjEZ4rhf73rLHwYzhm1heORg==", "signatures": [{"sig": "MEYCIQCz3A7dpmbVy3lDX2h3ttI8SdJ42rpXcxUr1+yHYHABhAIhALfgDn9lPk7bVPZR6vFQ8GMBtULxIYXHPdyD+dS+eZdP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvd9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzCw/9GfcPspwMiSAZyeFApj9oNeEzpFdom9zR8EIACIs4374yjUy8\r\nR18dVq0xzAmiaCZCPJPgyv5iw5mfcfpgiadfloXACYliRqH7xHPhxPtg5p/l\r\nHZISF8mf61fxfHoqoRqyw4mE8QEJfRZpWUMK6HT4VHz2prmTT3f5wVi+kQq6\r\n6vmA2gNsW5fPn5bGKvHPg/qfuP80EGpzTzeIhCa0bE4alzus8xs32/bHnzdc\r\n0Kd9Q02HqqKTy55poVGfKnJHOv0+AfHA4vF9anOpkN64d4P5Ksw24D6mpVTE\r\n/XGKa6Uh1L/ZITHEclDSBxtdTYXTq2B2ren+0n+VFzBTSNzr6wGjq2NpDcRL\r\nvap2PIFXiUX+9/xnd/eIrl5T3JKBIlbnJOIdWlMGTkbXhcOzXT3cb+m10cEA\r\nukv/HYtyYcVaZ4bSd+pCCwwiCV+rM9kohsJFhgceRufL7AAqUsixI+Ht2q4x\r\nFoLgdlUIurbVR9kQXzuPK3JWVSAgsVYg4VBIZJco1P5jo+LweUNKfR58B9ax\r\n9vnAL6wX7q5Qgyvn9KSnyxayhniQNnKncLXBVujA1Pj3FmZPE+9iVxuElPUE\r\nlTT+F9aj/IwSfef1qEiaKaMhM/iPoBkA24AljMtZ8tQ4O/2wWAkh7JaYR5hB\r\na8LqdtEqxxokCtZFA/+l6PIOKXXor459xAc=\r\n=/8+6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.43"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "822dccbfe25a15ff9b508cfd75990a8055e9a3de", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-iyerBA6kXFCrL3dTu3T8DhqtzeFKiS1llnJvXdeIJOhYsTILH2+5zrLWOEePejYlxfV4CmRRK7qyaCLBRvmB6w==", "signatures": [{"sig": "MEUCIQDaiW7Wsq8f2t+7dYuX0+b+0ut2taw6XvEnDXxSs+TYZgIgKI9XN4fUrW5URQrukn+cvruVwz96+2heYmYz2WCtDrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvQQ/+NsC1oaLE63m12nLs2VKDgvUQioJyvPcycSd3kUo47CW67juB\r\nVbSP1e+bJ1wT97HnDr8dpERtvKEQl40CqClfi1QIraLs+RpySicSti2SiLlk\r\nJsyKr0zNPNS3fJQmmrOcVQRCBWvRDuYj4fPy3vr7sf0uLsrB/eF6qQSta2zP\r\nOL87/bazndw37rAkCxPGp2/0xayIFt1VFDh22j4O+TTvYQs+BW+r9oMBP6Rg\r\nE08liV/IsZW78sFFNrVjRkxhSNF3JddlyyuR8zCvBhPVqWWar1RWBpeJTnrh\r\nVftuWQGEOMs91LAp+FXBJjGMTel5lUIPpQvN6AHjNyqLqhxybrjN/jn6HoVH\r\nHKrzNJjL/ZPiri7FgcoHdO96zziZ/ZTkWk3g6GbTCqqo6mR8MrFTygODJb/Z\r\n7dQPuALWTmaMKAP+9VpP6g/ENg/+uiWmddkH0zyoT2QQyzH6fl4KPEyWINg0\r\nplDz9ZarX3tnQNpYQpRhqzQMsiF6jCOz+kmUHeJ3RYPcpZTFkAsnOFYsBdZS\r\nPnu9DPaJJ6YCNT5Vce18l1ZyExaYb9W0mzr/s8gqkbFQ9rEP7ASqwXBTUFdw\r\nDYg/aWq1pfCh/QAWsQRyK7dhZMSkmEGack7D4Ty42K5T6kZ/B3nVxfccRcZB\r\ndu/XqJt4gNDPt6c/nEhaz9FIHZlAlGYWlaU=\r\n=ABon\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.44"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d03e6ff5d67fd9d6379d022ba5d08fc05a6b88b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-azfMSuRQCLBZLJC+anLpHvLfJlAHxyGjTCjb3Jc1ifOCoeBbtoZwX1OsLQGRKdyxZIvqsvloFsjbtWfEhTJaGA==", "signatures": [{"sig": "MEUCIC00ZYL+47Ghe4dXgjCzr+13/Tetc1dggo9RhsYK8SrIAiEAsl4L8z0Yydu0amRQ7h95ujDgqLvjs/DOPqWIpnCUnkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotsRAAnj9XoSOxkE426/zzua1z3681XaEJ5rrOC91Wvhg8eUJAswLD\r\nRKj6b/6ZtEdyZVB3QOz3VzccMXy3wxDGtgYFv8yB9DadtH7lzDmAc0Lkfzvt\r\n4inUFfu/aTl4aNdSNs3+38xc3VUw7R5CUXXwa2+imlCAQVbCEp3IwOZ/DP0c\r\nZELXbJopbOcU45E0OUdz59uMqze/ZRKBmzpxxQFeFMiivtsaTYeOHP3omj7/\r\nj1Z/WLNzEA5ANON3bDoWyTOC0oIc/5wnHbxMXj/UU5DyGrYjeFoOh3af+mVh\r\ntDNlTDRz1sPfwU1PJ68x1MzTWvI4rvr5XE7NZceEAXbcFeT4Yy3btHL31G3Q\r\nRY6F1e1lyvi2Ilbyqu2fq5rc2efjFvtTFwCJHRNs2HvU+0JqCh5tih/uKE75\r\njYbgF1zQdEjxFevN6BLWcjiOo2WWP77nqlUG1ceZCnX/LH0QShhOFRDGxAUp\r\nP6WXskorO4tDcrqdHyAg622g/aECiS2DHcmDv2zbADIsMiCxZtRvJN9eV9+j\r\nXlKA/9Oa/C0wkv6TeUGnlqKa6Iftm1d7d1UpbR9Jlput3apGnqDPWAIwVlPd\r\nu53qPLIhA91Fwyqc+GLV9UpsA+yrbmfIDOBeDDwcV0RfgUK5/mgic0a2g5uh\r\nl/g3hYIzYjGm6Enk4O0DnZGMNLPJXDiVynI=\r\n=0t8S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.45"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3041797031d60e27742705e1f7c5c7f0a0d9b559", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-QoZEq+OEDEOpcRmv4ArhyNe5iD4hatccjT8CACqCJrKm1Ga7Ffmj4t4UvWrIoMTJvcBUlutRu33tSs1SH4bu3A==", "signatures": [{"sig": "MEYCIQD/NWmQJOFobTLBZtY654Fy6q8tG5qUhPCxRKiOROfSMAIhAITLucjpin5EMcTKUEe8yDzrRWcrRREITu8GFcbYl39A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQVA//dCa8+6H5f/v5gKQLBIt7EPYSad3cnb6nwN4ykK9lYK/7iYNW\r\nWmGFjY6T7JNY6BP0TU2juzNfGMtpgv3YsKcCEFxIB9pV0Lzrk9CsJGw6LJI+\r\nfVtKyLAiMnsQV7vEzcQ+G/nOWtcNgJn7hRIYA/rrlQREL5lfUiK6pVQeXoy3\r\nZlhzLgqWzGj+RMX0L5m/3ywlUuqhkRJVvFT3fIZ1GfI4qXWc6cpphmedNBri\r\nc0G7OZ20Ub5bTI88bhW6IkuzLIlLzRC0NRbUk8GpTiwiEHZlBGLEHtWPtmrk\r\nDAypRpfEBt3h6/+v+CIEMeA1NpvaMQm50sfUrs/hof7dZGBF117swBySaQu3\r\nreyBfuWarRDmbEYFGTQpPo4r5M5UI6XovQx9N9gBQR41Cf9Opffs7SMRdiuV\r\nUv6RkkEw7bcaI3tXIL+b4YSMKdkfOyylSCXlxtIPoXRiYAwdQSyiPMeSdDgW\r\nMm370TmvKx6iofqW0oMzCg4RzSyUfNq45rOTuJmu1/zvNKJC9dbYZX642WcG\r\nnQNRGIAhy1lCXRSU2rIZr3WDMQV8m/dM7wsZOBgv0WEv5jzLYl5Z4SZGl5JN\r\n4mSkhnIrHsZH0xjvq92S5cCCf+lrM0Ysu4VgzzoRvm+ZKEgcsdXdXU6hObEj\r\nGG5WgGIZYZvL1QVRCs9hm3bm0NpP6dRZvr4=\r\n=FFAg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.46"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "659b3d7de61d6cf6005409bc1e51f58171dd2b04", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-fcf64XW+c2UeJig3GHenT3dYbzTR8t5gJ2IGNzr3XFMULWEtDMykne7Y/vkINYoBqQOJk55LKl0x8+VrPV7AYw==", "signatures": [{"sig": "MEUCIDYzVA2dHbUr6fjTRHo5as69M04qX4fKo55uNzU9wY5uAiEAj9UCvUU/69KbCA7E3vHZstwFqX4TTwfZ/JxBTzsF1Wk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohtA/+PoDI9Ar1gQ2DnLv0bdFINQ8E8B3z5848W8lRRasnXZbmfOlI\r\nmlsST3cVnNK6wZD/TwRo61qhg2ISY5fRB8E9H0gq+M19Ub2o0BPcUc74siJL\r\nUp+XF9nYPg9N87X98ZJFCQzu+ZDa2T03S3VQIiPI2Bg6T3FcaNTHzYzKNRUJ\r\nTgFHcMGzLE6vAhgZbRKzetZE0X7v9FHkQy1d9ncpSwP4K3eFJLruwP3rRxeN\r\nPC25kEEkaNHkOdw7G3hCcxp7jZoVXEumFBiv+QmfsCPEFYezPkikk7/1YnCQ\r\nHVSiYNBB++qa9dPOBHtG8fY/j9ML5M3k0lB/QBZ5mQOmohrblWfglxK+2sZu\r\nmUOsnM1tbO0zNjwV3WXaoU7KNLkoR00AIRIiSBW7IOaQAKcRAQrO4zzDtcvw\r\nCUvqm5n+6PTd55henxfd+Vfcdc+cfM/ptfERDrBoAeT+//Yj7FmZ/yJFClex\r\nm+t8lW1Sf0sluB6NqC1bGaUJQlfhVOjtI4x/WLChSOR5skT8iDgR0SkRT/gT\r\ncHK0TfHt6ZwfJdxHy6whXitmg82tZ3bPFzCZIBfXcLpeQ9g++hpQxfKDCiQB\r\nIyhGSdnSqW0qTh3FxlWDx4ybKO+q8Jl7Admcz+DEluR6+484fCcrlhIEDPYc\r\nu8S6NjcKfNg8neX7LGmueZvpYLWRQqoWKK4=\r\n=vUtK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-primitive", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "0.1.3-rc.47"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4772fa40d5bb7bf87b62391bb3bf29532ebc4243", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-eLpWe5H3YoLUjmAERxRQSBjWwu1yhhgljUcxdZMisGBEZNDrRF4Kg1oQvOGJTvQNQ/gUqD784MkbOP6woFcqzg==", "signatures": [{"sig": "MEUCIQDGuzaYI9IMmm1hLiKRcCB2FeG73mXv10sDjrSoZJtYVgIgUGh76cDtwc9G2aZ/w4YCwzy3GJUYvhEXXr0hTemTmQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CEQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVmRAAmhySWmh4HCOJO6JQX/HzvwYRLbML6PxvV6jYdujACFeMpPwH\r\nzSoG8ZSUCnmdzAr0UIcWaDIolCsxgaxhZUnCENwXjQ32heTB8azc5eP/O+bn\r\nxVWNgsPaCAYhXqwR6aHQhREwtBJmwta8errGAlT7hMP1/wYvi6bx1ROkd/KF\r\nE0QJK+gMOzXLqg/XNjQR0DVTgxCXddU9rYEO9k/ke85ZL27goXn9lDjjBgND\r\nj93duBE07DoCIAhFcFB9Yutxrt4jJj2nZvdhbnL4NEsM+srtT1YjDZyz6PXR\r\nimF82z5OlkY2tEKSot2R1alJU14N4nlyE69ZAg9EULx+zjm4AF7JOl5FWP2Y\r\nJwC98+GQAIvRCmjBdgterrvoOHY6k3hbjZnwXGtxtfRStiWv2Pc3UWiZJwlk\r\nmg6GRNsnYmr1cOKt+55ngFQQjkLe43iCPHsDRvZH5ahAJyIGGDPhzMjEF3Nx\r\nypTdw3rcfgL+y8TgPTzXyC1Ft7S5dTsMk8FOAurG06pawODygR1g2JiLfGa5\r\nDbKPnfJzHtGSOJdG6J+RcnDOlantgOZOmrbp2Z6SHvNzEyjhaTD4xuPP4eAo\r\nC6XwXiPmCjqXhotxScBIxoS6CODFYI6GoGC8qB5q4Y5tihAgsyuzjvlHF9xz\r\ntJjFhGuOt5U+SyP11en03jegVpRZPtuGIeQ=\r\n=O68L\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-primitive", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1937fae0bfe142db667a8530bcd8dfcbf8eee20d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Exs5RWFraEFPvXg28T3a8301lNzPtaQTdXUQZgodPgsWutXy5D14KQyv8SX4mrH2ueVrm/JeM2nK/kn7oFw3yQ==", "signatures": [{"sig": "MEYCIQDIFE4XSRN3F0hpp/aJtUBKZvw1Ku/K5FgjuBxGV5/cJQIhAK4mxq6Sh9AdqGjC8Jk6CRo4lmoVmfA6Wz1NOaUxFGwS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMMQ/+LndBOkTuQ73P7IQTMRul8AXxtV9iB0gbEC3qKrZgT01Y2oWF\r\nBzdweGfA9aiJOQj4F5ghTmkkfT6AVZC9Sjc4z4BaWT/Pi/7tH1RNyVL0HxZN\r\nGpLkQGII6Uhflj9/ihF/TMrexO+xOhhY8cYbbfsmEslLgEBMme0OEAxyc/ps\r\nEBur7geAgRQ6vx2HV8/0ApO4B+P1fpS8EW1sjjcX6KNGwrDo1DywbUpl8Xye\r\nCUB+61WZsbZBsyh6mFa3iIbFvUwOHx0pDLTvaxiZ69UNBZwAYbL38nTN3ZJ6\r\ndWbYyUcN43NgFFu27PSwh339OVi5Gq05RKjZJukOLyb2ledatt+bCchx6d1A\r\nsO+dSxrTphGdQpWCQEUOCl5rIQ1FJrelnKLEMKv0uYKeZaCpluDe9HwR+wGL\r\nNWsgaoN6yHClHf+33mdAAA2CxOklyIwev+3yfWPmfN02ugUddkMTAAHDlqSQ\r\n4S1W0NSW5FBIkmnNdi8bcBlY4HMDBmSI++0N7+CnoFJJ9VRgul9xur4vaNU1\r\n3Ouf1/+PSgYhrf2xV92qgFy7T1ReUcTHPQ1hxfstHVFe4NpnyPIMUhs8Savj\r\nRU7wOen7G4Te6H0B8sYeurmdgaD4nQeuWYhO6AeQNy5EX1XtRTwVgzJh3riI\r\ntYDO68WQgAtLLrJDwegrCRGADNDjvcOo/ng=\r\n=SLDm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-primitive", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "376cd72b0fcd5e0e04d252ed33eb1b1f025af2b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-EyXe6mnRlHZ8b6f4ilTDrXmkLShICIuOTTj0GX4w1rp+wSxf3+TD05u1UOITC8VsJ2a9nwHvdXtOXEOl0Cw/zQ==", "signatures": [{"sig": "MEQCIEqSoi5xqpp6mvBORGcdpbMx5qEnJ3HVO9aKyTQvfEA/AiAY3OPlMO/U1cZmrgrv8laiuDgxggFbRcCyHTYVVbaDYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDqQ//dwLUdoc333BQkZHli/jkWLDE7XCOIbm+bR079dQ8E1iXNkN0\r\nrxwPCpZqrRpqSzsbF1W1lf0FLQrsIxVghVmxoVYqOdGulZA3wq9xctHUjd2W\r\nfDODVJkT7gxo1PtD9ko0ZIqdhNbyVXmTJyzZ7udMPkYIeU6YqFjtpojbRYzB\r\nplvr+zjJvrZ8V19oN+QQkH1jYBFKs5njAgaKsm8sLK7evgN/cf5053Re5ozc\r\nzZrS00Vezx/x2GreceuqtbE6UQpMXGv+AFKFWMur2ZGYaWmZki+EiJwCKIgz\r\n2qzQ/LWl3fuYWmUaAnovWb1W7uuKPOOx/I4VOJYdUg0LFHY111Acd1ORs5Zz\r\nsH+EW0PGJvDDwQERZMGgS4A6kLvrrt1velBAaZOKwsOln7Vr8gQMDkzK+qQW\r\nbbK008G77Ib2SllzBtDWnseIMG+bzTupMvncKFTZZwD7ipkzlQ8a3byXME1s\r\ngKW+JErH0THi0an0qEPDuYnWKx4eMEg+zs7HZv259EVGSF0UGccVCGAbLLZr\r\n6FfXIjKT48F2cltYergHj4FaZIgYstRjnWsA5A+p6yTTXzokzK2SnfbWxA6P\r\nej2OWknvQ0CDPloZt7vs4wobxLmE3mKkQJzOY4SWsNaPwFg5aBqvKbWk0w8C\r\nEkMo2WBjGBDmumvQ7xEhV814fkS8FlTKBhU=\r\n=lYk7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7aac190665f77a04ce2a4e6aeec7a6166c22d8b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-pkUUZJvLPi3LSxHn6NeP20ahYrN+WuYl4+13E/Twesh7Pv2iB5XrI8dRHtvgNzFsOE7ZRSGClUWZJBkkA7oJGQ==", "signatures": [{"sig": "MEUCIQC23b8JmiE5YQiIMlb+T+7/RcCsRGkGYaYij8KtjL370wIgdheP1pq8VJb9pn3R0LbB7NGNfe4wf1dY6ngNhRg0zxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrtxw//fCn2aR8mCXgstxI8ffvuZopGetYNGZLaYghpu4qKExeMywhz\r\nbSVEayfGdjeRoSCjcC95iJC5plGDOMHYP9W74W79CNGN/joC2xUP+/MPsx5M\r\nBaORAFou2kco7HcZG3xhaHBLb/9pvHR8Sn1mS51uodB8UMqs4RGwNCCcCU97\r\nJDAUpdBy6pUbVKFMiD9Ffr1Pj3A20wsdSD4mRnphDp9ltO9PTwTo9vMbUITN\r\nP0yWfepljFL5vWwFNIjSV4yGOe4YM9B51xU1uDy2FCYeGXmkC2B1kwOVCghf\r\nER5FStL4TbTzr8deueUdYz4JXq4sJFhirFNdN/HyeOEKtv7ZvlxGohxc6p52\r\nGOObe/4Lrfzt0u0s9TXRlA50f/bjU76anES9CP2gvbEMy2ixRFIIfatbSqND\r\nUu1VRy9DleyyKMCI3cFzyt/VTjBd58vKYDSZ0c1nhseGu6zdpHEIhtEa3X/N\r\nirPDkhkySPkvxlAZq9E/t67kSqATtKe3Fs2VebN3i3Z+Q4/QnDQBBi19lsid\r\nUYmn2v9BiTOI+tw2baZY6k7Si2ABuOG3QHmMRAMcNVupgLIJ7B3hvWlpPSAe\r\nJpShr2kK9xbBx9VebUuOQe3cOH9gW2HzsbKlXtJcoQKZg11jSVUJMybWXmYI\r\ngi7gSdd0p6dO98a/VPjrSlEJuj006/THjLY=\r\n=JfcK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "27bd4b180d6324bc48e9bbe469ca9886fa96815c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-MTutQ9tZhD/RKpKZurfypVBs9/q3ptjUyxPLKlt04x9wPzhoprRhbLOlKZq5EkQ3BvuWShVy6nguc44i5NAjaQ==", "signatures": [{"sig": "MEYCIQCBXCvUGa0/Jcf/U+n2OnjGOG4DUPCIdaaCLHUsgR3xrAIhAKiVYmwpsi2zLQq8lvzHKszSsZUO5/+EiKIu0M35xor/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopwRAAjUkVofS3i11LpQwFIPn+/OBVXCN/UdV6K+veo8JmZQrTw3Yw\r\npNcPH78smBzRtJ5bjdLlc6mMJhYiKw2ofMlnHjAEhTYbBGvvG8zf6bRxWsxn\r\npNgzQsz2f4ZL1cc1+zqBDZ2ZV2bq2H/8PTjQBO2BjlJG9uBId6YhPiMTjLIs\r\naU2OSvAL6x4RNd4bqv6eYMtoGyfy7OXm+Ku1bNECTy/S2R8htuZuAFpGl1FN\r\nb7axe9UDgisLan3nR1+MRxR9nCGGe4N3PJ9dzTWQMazGdZZsfzq2k3gtPIrM\r\n9HbmdiSfbTwMDPG01W3LComDZ3kTdhzBXDLNwusR05Z5Mjg7/Vf+Xe+jo/Q0\r\nbzllLbybk2Er2V1ahxKgGiraLU2hiZWh/Ko9Vzh64NzDY8hww8Zi1ql5qifU\r\ntJQEwA4VjipxtZppBHFXbkEchKoZzIBCLRxZHUJ0kM8rt0xhsM4bIqzj3n+r\r\n18239oVAbOTn/mXXeh6lAzkHulAyDArKbc97O7gLe5S9sXKz+vx8eZaxM46M\r\n/F+O8ioM0KWzKiiaaWYpwi0yj4RCnroYDQH7cYcGV4FAn9+sRdlYRUyPdD/j\r\nCeXHAc0R62jxv6v5809KjYkLQrID8UtOUOieBv1PDGopv2aQJH1UcYNuFYmE\r\nu7ZSds2cBQxMAve7NLNoW35ra728m2vzgbs=\r\n=34Sf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f6cd3da927db063b7c8198248fc4dd17a63be5c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-31gu8V94DIT7LC8XHLtd6Hq1euwHDzXoR1iuQyzcb03NsVEtNV9TkfzBepcpkvr2vulfnpMxd6pS9kTozUc5Hg==", "signatures": [{"sig": "MEYCIQCmvfkt2oNcI5efYTnpzrsqFGXT3u1MHJ9rYPa60DH7lwIhAJBPAV6ueopykyuOcPEJEfPRfjS/6Squq+7XFbolylI8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOBA//aBgFncAGh9Nt3GubBPqvLY78jN+ozS4zppFs9ac9BPm5tV9L\r\nIePwOKFNxAa7KBvG4A+5Q/Zdq3Z6+e5/ee1gQnuml+OKTShmkAMytpcJvdmA\r\nOoaYQ1HQ+5Z/mbbf2a/XFFC53jkmWgF7QXx78763sX2q+PAMv5maYe9JnzdK\r\nk8rC1IjzAmO9NZf29PmEbZ5x5iWp6lGiXuLwaoQFlygSduPLR5aHbF93uMwi\r\nUDzv5uEVv5ZZMBygXFA1R45waarVTPFG4tAFXFPaduaYGttHelWp7IVak05j\r\nVK1poAwzjWxqq/dVMKl2cbclxSK1+WyXQ9nhEfBnEPc8pNJDxMKoZxgiJabV\r\nrWaeOQDfHx7PF2EZ3aJHyAvrk5yzkLzlCjtwVD26aJQ/wua33CSAZ5yB5Pf5\r\nFGQgdm562CKRcDOrFblqof68b3w2sIrMv5L3m3GVVp4Gjyp0sTIcjdfBMqU/\r\nyHC18Wzxwpnz3sZjxd9WiKjP5rxeclPxngfDMLBjjbvuNklZAnTW5eX2Kre/\r\nf6s2i3oVHbZt6QJK3/KzYy7VtGEYp+0/TJR6eEQPPfS9I5+H5+CZlyJqzNnN\r\nAZGJ1lelg6vPYBEhwayaMvwPlx9/RSkpmfJoT3A21NXPZaWKAJJ2V8+HafnA\r\n/JCBGzvwxuFjTUpXYfEfJPIQmug1S6iZC/Y=\r\n=siyC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9a8d8bdeb3fccb8a47d59c1fe8d33c50261dc19", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-a1YSRcPPqY/I75kARUuG3bbKH8kS0zPwnIdAr/ef/gAN6Vm7Beo/9RH4QrMT4jvbfpUZQd9rqS/m+N0aGfzN4Q==", "signatures": [{"sig": "MEQCIAMW7e3kDj/bXzRbUjDbnNpFxYDXvKyItesmYCyZB6Q8AiAzXuAVRCAXpKhD8/idnxHixRaurWzX5+Q5kWAYuSV3iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ6xAAmm4Ru2WdmJoKcM8F0JEB6Re4eQ+2cLoqv/sRkQFVnevv7TrY\r\n2kr2eKlrsmupiFh1mV3FDr0PA83GMte/TYtkiO+jX/LOdXCM5GE4KCcqAH8Z\r\nEWqrVvvFMbSE/oQZ09XDZ0m1RexK5+dY3oTU8w0cuav1a8EUbaBiH++rbuyy\r\nLbagwChcXmDigB8eS8p1EVwTiMEH3/XbTEkJ+Tqhp49tMgrPRSRxDMOEUIpl\r\nTBaoPN97PAxSeY2D2MgBBLdS0xaQTQglts4XmHAjG1V4Yt1QuCfwG9M/vg8y\r\nMayxYGa6e7oiQHDl9e7Uaibagg/rAuleAz/6u22AnuiTokmiNR9Xy92CtQ0C\r\nodSAt9rDdvZDlR+g6QQJjfF9bWrSY76Y6FjJnEJnVgYwicvtTnlftZENPf9E\r\nWH6r4MHbqoYxA6kRYBSisZLwqLucktUXxq5T747c5poGcWPsR1JlHx9wmpss\r\nt/d+sdIb7ICxeL05ioNNQ+GnanU7TLKFbNdZMQXKhJA/RBJBcEsdyUgk/5ji\r\nlJ+KeDqFWLu4AP/wtC0dlukVSkCIQ1LGmGmCJe0f8TUG3DB5W7RdxJQc2gMN\r\nCcMtk/FbHBpZV5RVb5Xm96U8sFSZJmTkoYDmDqCSp48JEygk29DSsfg8oDUy\r\nWBItETWWar0L1/A1E1fTx5GDlZN3hrLptiI=\r\n=0WxO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "805fc9fae2bdd7da2f5882f022f8936d8805fa6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-cmIOfm7Y8Vy8CKdCdiyfhbA79cBHqCA4el6Rolql2flkaqCIodtwqYAAoFSqJuvx/3vNY3POFh7i61CyrCU15g==", "signatures": [{"sig": "MEYCIQCY6x6zzi4HS7REuESBVhgnJutciDcDEmh+eFsd1Lh82QIhAMXdY0FHiZH0tubSzB4/PPJhIzl26G+7ZPaweo526N4Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU6g//YOlWNHxrCvBvsh8U0AmxipZe6eN6Xbj02UlHOamKaE3LAL/j\r\niUTPyipoNZZpfox4KcvwSs3wsS5KkoJT3rXmwBBY5ezfic1NqrSf7zZ+zDHa\r\nos3R37aJS83jy+0sKZzrXcM0EiDkUicfNf+Xc+zHl6HIZCX/Vg0N4FzzYS2A\r\nD6PcjuUA17Ek6rZFURGDOz7Txm8e0OxQAsMkQmyiudK64OmDSmW3J82QqKcW\r\nGNuooJFXtUSFox1Y9SPwXrrMSrMwGab1Is3JkWacwVLy9kdH+rbZ1q8PppuB\r\nGoNtbUeFez1Ue60XsuWREU/fLPQdKW/sRDV8Prhj1V4T3FDLolp+hLcTIRXy\r\ncniXGg2hgXmLLS3PXd6a/XSRo1fj2grqqz3wLkKSlhD4BHL/W+RafkLoahxD\r\nPCf3tQViGTpyjeS5LXO/bYm5kRPXqvdFKldGNW85/rZvz/fGk4dyIjI/tT59\r\nJor0TL4UB817tu0VWiLauGEkvfXpGbjsu93eKncKIausy4R9XUbYnS33O954\r\ntKQ/E/vCFgJxGpX1PqmfnRkZ/DXy+KWOTPheHDNZu8qFS+F7mMCZiBkQGfy0\r\n6wb2t2kZ1OihyemxSFbnrMX129gsdBjiFTcXBE7r61bDNAHMhi+12wSbYm/i\r\ntkCZ+l5b2uYMcC+ZCl3D4h5GdGpvNF9a7Xo=\r\n=bCpY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0412f15e914f847b9eb8a4fffb6d0d24cb99c99", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-vBvmk67RQp6++17rzyISC6wUK6aPHpzMphvUbZUj5uF9zj88xe0fLLBm52SfOez9zTTs5/M7jtX6hCvr/Xbo5Q==", "signatures": [{"sig": "MEYCIQDxDX7uG5jt15atM93iW25/oTVhXyjtCSA2S5zIcvHCsAIhAPTk9VLokw1gW3R4LL16XOUnPGrGf8PanCBiECLCoT2Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Cg//QCG30LGnV1FiOh1sUR2M4rSflC66oKC9APMoynMap65EIRuH\r\n5wtA9+PLmfpKKoldzpaks4ZADMqbBnqY7jl5jtROQh0Dpyzp3xdv+a6aaQxv\r\nTl5/tARaO25URgNZjkW5pv4BvFuo9c5K4hQIpNkKU6PzAmZtsw199R+KubrU\r\nuEQRf9rkjll646xStuEhuGHd5xWVia6cU+DRKMJcUmCy/uBd/6G+09WczKlO\r\ne6CDtSjG2/AMlAcerVUJLQ5kmLgoIwJHmlH1hW5a1J2PWuAIUh8Lll8+EW9V\r\nw6wGOorsRqKtj+1x9hF9tLBkHmfexyEaBQj6KGS784OXSWVVdw1OW/s76GBG\r\nsW20mVCrD01vyw+qN7CqnlG89357vBopUcbgalP0hHVwkZGl3CnF1dDMqfwW\r\n1F6PmBBeOO2z7LosFrJcy5vpbdI3zA9LRzCnh/Z3vuWgAeWisj7zG88TGyb3\r\n38maGLSj+vDXzERTBhTqrPQrANVqlQf3bQ8xlG615hJcrbj+6yWsCssC0+Rg\r\nrD7g4k/E6YR5oN/0wEznWR0XAi8rbRIzpo6ZnI6aI7Aw7NQE2RbQCl7FRHBN\r\nH6GZ1L++hdES/ip89yTIYVmriX6XQLiRGYcvS7dSMGjvOj56bnBDNo/wM6Ss\r\nLuQeaA5zpPwKAYj5JvBM3ZbepK82Je6YGH4=\r\n=JpLF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c374959fa7367ce04e1f4f0205779919d4083c4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-9uQAdlVuQtheEeMiTBoL6oa9YF4oPMU6Pe5jaFKqfWi8eniYCJpD4979hIy942bQ4BIR0cflXw4lvYxo955zJw==", "signatures": [{"sig": "MEUCIQDbKIA5OL5QcuMliKbQCR1H9v+a6v/zzahVe5YmRSqFIAIgU+jcjEJq8bviZZzyxZ8qpy47Qj4l9IMZ/vrRusfwwuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeAA/+OTd+yxI09zHWMRUl2+mhvH4oluxFBx7FTDojWHqvK4R6P5Xj\r\nN/LnFdd0c10gH3VFrl4McZs32s83DVViYlt91kWzLvo3yEKp1kfjUl+WAqbi\r\niGnDzKfk2fe3L58EU1Ovp2dgu9oS6P8M6q5zMiUWjxqk2KaZ8ZrL3D0VFpgf\r\nt7X6B9Ni0m+JJf1OV4Z+/Twcjm5NITpOujOZBaplGwchz/bkQGN5xbLTykqr\r\n+NevCeTlTna4eQtiMfZkxRbV09iSoL1dDaEbJx3VVjgifB26dibFCT3WxuFe\r\nY6iPCVFtZ89igGk3JzY265z+TuNCLQ9LBk7Ow94rBvVpAHrw7h+raPVGa4gF\r\n0fRuG2HRaDE13wajp5bmucnzXJGDqLreKX7zZl+OAsCNlgyHn/nHqPeoCsTy\r\nBZcC2fi6FrTGKvfpr1WKqsso3IuJazbZgidEj0/zCx7D3V3KceRIs/lZF58o\r\nmRIXXq+8gLTIOKntl/8GL+WmNRjMHaiO4QI9UNbAW/wmL5bI4kxgKmcSqBmN\r\nSjrdIosJqPeniRdYzqSnVzFGvbVJM3sltjHnVcgYZMTu2dO8F6oN56foWudP\r\n6gr8mVibDqdvcXM3+M14l4M3Csfi/aJLKfc8zVRAsTIY2ie4/jJu/Bj7qEnh\r\n6GnIKr8wSQhcvYKYrjlRhpjZbWY+uLLPoCM=\r\n=zB8h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bc151f918af2723aa981c7339fb8710f965e0ad1", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-M44NzDo8jUNdeiIARKh72vgRU7AF9BHPhzDPPEAAWhQ20Cg6h/wKd6byN91u/zf/2qfsPJCK286XQO0Hq3wwRQ==", "signatures": [{"sig": "MEUCIDOr5hPfxknzfz3n12rcoFdfSCU3Z1b0Zc4WUv9dlBc/AiEA+vwNwN9E8cvW93KNQLYfNylPE3Tmr80xTRSIz05msxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9lQ/+K1fcDocyJiPO9TLJZwP6lTWHMnJKAZzL0yPk0VY6IM7+1LEQ\r\nw19nFvu8PpN8CEhl0+TLf3gfaRLRPlCb25jNhtHMz3yplK/cgqoK/VrrHd9l\r\n+r2rivAbDdKvb4alIyPJUGzFlOEALFF1wXE4YM207xiPIJXCh1fsSOfbTNpr\r\nlh8NF2BzWqqWzt2dAx7l+zNW3wPzxwdXOq4Qw9OKTJjQ6DT2yAe5ze0jg2Nw\r\nIyLxsm7OouJWyHthzBFD2gaCTtQYHxzSnMwznk/C5Tf6CU6jL8gb+1az6K/K\r\noUbsJ9kldLGgcNTwnGFcw729TOfFeY8wXFTtfyMj2gUDvOh0YbmgQwWrvd9y\r\nLA459Vnd9vfvy2K696sNLOPYcm/wOMERI3K3praqnXebb6I/hY9SLpcb2cUb\r\nTSTAV4H6L+Bc77Vup4Ar0wCoTKiRimBxW+ppP6TzXAcF7miiEp19NyyV5dhu\r\nh+AOkV644YuRN+sT4/PiGdeyWOfGr1L0+oCDbORiwHOhuzPjfW4O/d5J52Y3\r\nlT4RnnE8jHFVtvVayuhGDLs4nrZZxLGIMtCdPWKdWWqncWF2k8BeLTfeD9aj\r\ncle6HkAbWfg5iUPGLpuCJEXRaDldeMOs5P2X1r/P/rTCtOvw3CpL4bkrBFUp\r\nCCCob8ESsoe8kWA0Vw/I9HIikwfyu2mKek8=\r\n=wfzw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "be28779a00373a912ea607bd7730438d19fc4b9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-1GviemlE+iLqvL31Ba/4mGAso7Nda0lCu10WEJ1KjZh5VsdqD8v9UjbXh92BeIOZm6MUo81KsrK1CMI8I71Opw==", "signatures": [{"sig": "MEUCIQCkkb2mXFNgVrLYO73wfToiLfNW0MlEu9sl3/NhdHay5AIgNLWU7cD/bJQr3R1pGhS+OuR5w4iyr6tOTTNd4j6702c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+SxAAjD5oh4sXkMtJJ03S0LxZfEC+CRN4qs+HJOcjW5ctpNX16CPC\r\nmbE8adIqUuanq+sG7KN3ymABN2FH7j/2Bqzl3Cv2nKl1K5l+D46qXAKzk412\r\nUXb1o9ckMWD6Bew7zB3Exe0ya7fc5rurWMq5vcA1JKPY2q5yt789oPBlwRbX\r\nxrPSPaIWTEa2EeSQ9F+dfe+LenYk+GOu/P08d2LySRzDgPIJrVuFDI2d2BI3\r\njwCfvgo5U0+3gqgi3Uso2YRqX831+j/MossgBi8w2l7Ohq7ASZjU3liTAuWN\r\nZMqDGh95GhcnuEDh4mOUUNj/W6pL9yDbIAo048bzwh6xdyNlsM0hg6YWGRNu\r\n7JhYLQga/rQSK7mCtQ3P5Bq0ZhkPCp88Ekv1cCV5VSoNG+VtuZYZ6ZXXCnlw\r\nkHMuCM6Aip/mh6v02fCg68DYhXuAsDp1DMBEk3dwjDy9MKbogT7Bdpn9Cwjh\r\nDbgUCsF5Vy+dDDZYiGGP3mOG7VyvFjbhnWRUd2uYCBpRtvelKGeaauRTs29D\r\nsApT3Ft3GXV7Pz4Vp4w5Xshi7PC9Jt10ba5EYm1oRUXUU+VU7NYrKSkfN8x3\r\nhVxHckkM6S/YaWxhB6ARPylHgeqqnj30fTINdLqxXmJWvEWm5cZWzjpwH9iy\r\nt/F94VsdoOJAk6V8BRrDVSaHOrO8cMIzDiI=\r\n=NNwQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "832333d400d74bf3461acd93d7b72acb3819b868", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-mHQARBOvpONcEHEnay1cQMJ/IUx+8dIOX64pXzcfF+f3oZhN0hiksuokAPuVZ1saOw/MrdYZVaUtRxKuelmeyg==", "signatures": [{"sig": "MEQCIH0BOiKIKijVpGggyqRXYZ0PTUzExwZGxWRGw71V8+SdAiB44xbGU1k/Fy+h6V3m0DitRqJx3Bcn43YdcYgLdN03Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRAB+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUzQ/9E5lL1oDAz5R+Mtct5fSO6ps2g6CSv6QyNUVZEYPeZiq5owoV\r\nxcqlK/HG/xDChM9CM+D/fv3b9hmDO0jO87TxSXN45NqqdsD8MsMCytGWxZ2y\r\n3r3ctjFIpoPMlTfPt71FDijUdwXDD/oqMJZzdTamEh03PVFyG6koyS70g5vm\r\nE6xo2Kx070wZ8g29LydkF9Z7jSGii/QfpEJNvJvU0IXA5lkojJ018QCcwmGM\r\nSyN0FYzzstCePYpuGe0OqrfhcwBcasQF8V7Gj1gTfzUXNR1NYJUj4bPL2r8q\r\niAk+MmOYfKhcopRcNmWn4mpPdLtsQ6S/H1RRnol13EgHhA8Cf5i9k7/WVRh/\r\nl38uC7vd2D8KL8bjEiUpV70RsUJbJbC2iJC4hojF80Pdalr/+KGa6xYkhBQ/\r\nRLRMNEIshyqnhCfz8pkSgH4i/IIoWHZULJy/jcXDALBnHBEabhRu1+tygHLl\r\ntjef5/nHnj4Nw0uIzf1u5iAYSkztf8GuP5YGbKVXfXe+3kwxM7O3cdqffCtl\r\nrFS2MxMn7G8FvsdW5CKLQG5RatSUUbt2JzamxSsojRTjdcLRYHUjgBXfcp9W\r\nQp4aQQCb3t/S3mER3BHJ4o2AfkrSmu6hhus41dKC+sDVsuSjr00n1ZfN3eA8\r\ngWNdBsg4OHdP64OFPKwdBOODjl0khE0Rswo=\r\n=JCjV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b5261fa7a6635e037e8f60ad9ecefb2db035bc37", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-gGGqCB6Fu6/uC99Whssveu1u8+/ySm4Goey62Xev4WqB4TzKr4sFW4Llsj3z2PV1wc16vapdQLc0Gbo1bdJg0A==", "signatures": [{"sig": "MEYCIQCwnLBoplez5bUugUu8bX9xlB4i5Fn/d7GEQtkhCex8/gIhAMwBAbD5OBsHyIW69uDzK7hWC3ZcaGs7i9iTJXbIeVIJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBbQ/+O+QJ83jcJFNgHP31KHenUNhTM6gaWnYX5qt56HyOabejDKfz\r\n5N+U2XCNac0kDJBvgFOVbYKQelqWLRFK6MN7WuXifD6CPYFYbMBgYTIr+YXq\r\njLIo1AXPv6AIn0dvWYcMUDArI9awBqyqJBwGqbfOJXqbX7qR9/4pMGLtcIWn\r\nfNofLcCgoLY4jDipOdqqW+arydlDF/GdiV4WTer/blSVa9Ekqp0LyplqXMrZ\r\nnaVJYoPScxbYlRI3kkv9ThQbQVBuxQcUjPNVA+9+bI2e2PAwnPBv1+0MaosB\r\n/QRgvGbZrCv35J77WSl5zgEfCWUaS+rOprL4P4JI4JjHPDjcEX/i56kwqo8V\r\ntRFQCKElnXvvGDX5M+l2j1ob9Y9xTQ2FJZ6oQmuWcaQotIUT4tNj+8Zhh3Rb\r\nf4Jfo80vCFIK7C0knDfMi1dYE0AhrLbRVRqthjrm21P+8gvw5Qylj0lBet1u\r\navax3UNxD5x1LOv0XD4COe1J0dQkNW9Nn6B5rGN3mZwFrffME0LK17Sm4K67\r\ngnu9s9WM6xNMwQs2CyeMwNva96UQlSDQ6EeBM/ICP/Ea6tg4v8gSwvBqOh4C\r\njz2fZxBAsZd0VAcH0+Jxa4/Z6zp/ShCYZyPNymnsOH7xNjGGwQq1RMTqUkEK\r\nkJ8Q54EENfvGQSce6lcmI5OjWXft/jJXQEc=\r\n=EhgB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7c3768cc60d7d3f19949e112989de328cbccff08", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-N9q2Fk+5NYp2ijLmd8YifqS+XgllJzinxh2oDFEEyHRH5uY00FJ7BZwfjJDGeG8S+AKWjKoQoZI+GsBktlKfYA==", "signatures": [{"sig": "MEUCIFXAo/t0RcVFgcAaitzYx3rqQwIb53qRO1YqJvqF0nGwAiEA0ehlx3lqa0AyvnVok1HEG6Kc716B/aIKwUUPa/DcQAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0wRAAhs6bzUfyZiZvl8W8kQIt5lvgdquxSHyaDx1eZ1LtWoLk0sr6\r\nnulOWocOwCKq+Vb5SU6N9BcTIE3NWx8ls06BF/4DpsrRTdPFylLcEWdI+NVl\r\nj0WhvQi8MrzJ4gWJK4P8DkW7yhE0S+IO4yuqvs//U3a2y9z8g1YD1MpFf6nr\r\n93WoQDeM7icO/k0CiSY7MJFmedXVENhNrMSuBfqSEvwS26wJAC9WgvKBPRhU\r\np4HSXgvVw8m/n0aDOZ4PyImBmYoh8rd7QZOCzwF19kvlXCjlSNwmY9UQd6XJ\r\ny1z575fVPBFui9qd/3elGEGesEOKkuGYgCE0tOBzVYLF+EfLE4Iu4tOQ0Li+\r\nL7ldu78+lVbviYDUewU+IYhU3vVQ8rIEliY+vSUM7FD7HX8XDQfbSElGKUsE\r\n7z6Gn4ZAJp8Bm+IUfbzZdPHTmOHo/mDZIZCkHR/QMlX6tTmzRwXvX19bUU72\r\nuS9SAwFpwVQAglWN/QGMQS0gclqoit7/MSsUeljp9O9Zs7Pv1+1dKDQdvr+5\r\nYWrLCDTSyngfjXpZuIqT+/XGxT5sPyEfEuMET4Qvj5qOyVQ8KMOASfMmCQv9\r\noxLtfBKzsbwsrTpc9vNKbs6+0I7lgmfm250zcBwVro5mBZXn2M3erePZaggj\r\n32zbMdWHnJKldsMfMWIaCU+u+4SDzzG5fM0=\r\n=Nr03\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a17acc885b72d0ec4fa1f58dcd0c621c75539e05", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Z2+R9KxbeYgGMG/WDlBiu1PkBfQZg2unuKzIqHyUM0NZ8TbpHWDiHL/qzOHrAjnupvRwOrwOZGHZnt89IsWPyg==", "signatures": [{"sig": "MEUCIQDQmGmX2+HRYCEX2hDPr+tDej2AIFfGSIC1Zied6zgHPwIgK2yLyn9XAU5ImtVxn9XZSYJvN/QEZeq0D3LFj7XlqEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGYg//SYMutuQW2onC4b9I51HQcfBCT00cmbmE/WsLwff5kSAj7Fnf\r\n67QF0VQovObEmpNOvTolpfRxleuQ3/mAlD9LVz55aZxtc0VNB8eSUb7+ArAa\r\npTpZCz4psGHOCDmynJr61rWOfX7GDG5o/dTqKabU9jjL75r7oJSohan0aRvh\r\n++1J5kmG4g3S3qHsticGSuViKGpHVLcFK4ohpuVGuMB/6iPVbdviYgX7PuTj\r\nTf+cMVc8AhoWblVPb8QGlQYGJLjIm4EVwXSrIQ5yZ6Kb+hLzMvqd7Pxou50U\r\naUfvdemcrqwl7K5Hqq2XaPw32ps3Gqsku1UfgbGNrrBJmv78r4i7ZGaP1QSe\r\nC/9M9uqs5nTwhQnOdqY7M5AD5niLi12w6nvf/hPfjL/E37891P/qMXO+aUtk\r\nXQNBj0DgsW+Ie+3b6Dky9Qpqbsh9MMbdTDZXe8+DxWjriCCO1KI6fQV9Na6N\r\nujWo+OhNBqVQ6MRRdCdF+tNm698Si4rFrBNmvUPCNcU/qaYwqw/lhKAi9DWJ\r\niG6BObAAiUjK52uxq1GrVVKONlOI42Qm5HH03yP7l97j9+sYR19Pb0oKHsLK\r\n0zbI3svOepzhHbiMGwiAkPaxAw7a1QsvDd6sY+GSjio2twSfZjkw8wcPi/sp\r\nnjGgseBbyoHewXZKHvZfrSCmBHnHdipxtiA=\r\n=UFr/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c2fa6de8b325054a617c1992f90f4f8b688e76e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-3dVOPOlysQAXFRTD1bK4dbgKI3M7PSMePXGfka86WrRB3hSZmTusInY78XWdyxP2hU59joY4/wgNmYwkBWyHSQ==", "signatures": [{"sig": "MEUCIGuMILVStPUXCSRsLjLnjavOoooO/SFU80eKTBR118mTAiEAmYlilxMJKSOAZDpG7dO4TkIBAfMp5w5+p9VF2c9P2fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26456, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpdgg/+Ic8UXFposAzpDdqc98yUU1XsN2CraxL95hRaLuQbP8O8Yz25\r\n788bwv9u0IZHeGgWreHZcH/Vm5O9b9mT7/abrVQ96FmJYtJOfkZ3SMpAV1Q0\r\npgbVLH9NuZ6ADlQsKLJ93pE+UCbUW8GvvSQ4iAWVyQJkxDC09aXYXWgEBcxH\r\n6axThk6hDhqvYd4zCCgMBbDT06n35hz3Jbqun/cggL+swioqN9unmVx/GkyQ\r\n314/A/Cn34ljcX1Y2fDPMlXyzlcGu3LOlHBNVjgZWdoM7vRMzyualdemSE6y\r\nhCwNtSrafek1VnrZ4xBcNquQQ6G5Hny1eVG7qKrzZrNx+wMPIZLFZysMqeSg\r\nkMUjf7fotMeoBks9Vi6Tjz4GjvMv+zrWdlKYluagOUcU4EcEXndco1k5W3M6\r\nFIBQx5jPlBt+aU/tWYzpCs5s9lfVgb7vJpJDw6TODLjHcMh4PrWR9ZP7dhcB\r\nQewsJYGC0nrSJcqsOYc+n/VDGreRvLTVKOX+/b/Qj2Ov4+7jrRuRCqCq7w9f\r\nyCaHLrldG2qb2HwliG0W0uxSpIIL064SpvBT8660XZbVVcbKvyVaD0RwClx1\r\nxxagHAx1qp8lwjIzf4yjH6CIwXVWVhGbMlXBL8kcueW21hiCXTLkPx5Ai1wo\r\nqHbENi83rubblFG0Qjh38DZafBWb9V6sE5U=\r\n=YRMK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "204c2d4c80326e01dc19d2cf54a41ecf3fb99d50", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-MIKZuOGW6Yd8myN0A7nW1ar2FDEgIWQus5V4DPXDdl+urQTzwBwVqp1SFn21pNyWvfqAqoXg2Yq3Dti8Btzb6g==", "signatures": [{"sig": "MEQCIGSoCjVKW+nvm6hHfRf5fLx4kHvuMtQaGgM/+DQ8f7m/AiA4zpQszR9udssYPKs7R0r9lqJcL70+xAXEEFS74bf/CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26456, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo95Q//cgT2OTvPkimlYbtiH58AXVZsRA+9CKY9qzmF7k4w+C36lLBO\r\njMmrmCNi8zBrfY64B3TFjGL8RUIiIoXl+i4oC/tWvxrhNTZuU6Nvsz+MRKBr\r\nSPXgbSG6ho5uifeO4UlKLgljvly7ayNROZaz/48gaoiL4AboVMznn8y0hBsD\r\nNKCBWlcCg5osxQIHELm4GG+g6Z6x1M6qHTIfc1ohOmkaO1ZZ1C5aX0CImHqs\r\nsWe1IoHJeVz/L0vlHTtzhIg26XBo8wmVlmSAh0k13T5MJ2HcC1KE5MHAd+o3\r\nGgYfywh5WLIS7jod9Sy6QfejHFNIOUl8K7QOGlXpbVVQOlmD5sLNoA23i/Av\r\nVffyK+Z0WZNet7RpYOJA5p7tUCS5bqQqaDgNxQt+fxFS3ov5saQ8YcZl9oRU\r\nwOJXo5ZB8WAf0byKEeTtNV6sTIvaoznno+TK64Y6vxundPDWgGcWXTTXL/M4\r\nR1GNlZlN3QyU1QECIzjx/GDaziWIIFajIM1M2EVdzLZHe/LNiV1I1o1/xKbS\r\niRQnOPMi/2scSIkNFU1aPN8sUnHjZnCrKpcFr+a56EU7iaaTW4EhCMutqO/M\r\nFWna2woh23azaTOwmShTQpKPcNp8kXQeNgC/Bn4TgOf46LvtFZl+XID8UzkS\r\nMXS5FpvT4xNDEOlHOYAYB39aRxI5Vk+RIqo=\r\n=A7NO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-primitive", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "62e125d6fcc703ed64d608a44d4e85953b9c3fc8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-MD5vd1rKX6+NknStcooOCaPzWqnbxMArDEx3rm6IKTmjMCZ2EmXgeZa15nRV6Rlu2oFSNJ/WcJ+tX5Pbo4zE7A==", "signatures": [{"sig": "MEYCIQDCrrMn+9IS5l8Dncm5gklrWMr4KwCm72ulE9JI3+FuPAIhAPMeYb4Ca0qGGm+1HL9hkq/38smTOlNw3Gua6gJ++PMA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26456, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9mxAAle/Vavd69aRqm3Ypbuv+CCmfKjXI0goi+gito9UOuS3uzIkA\r\nbyOi+cI3D9mOTvhyxQ/Kb9OZ43/D3Svf/TsCtVt4ZRIE9tIOYqQ+QBJaXhMj\r\nGwGhS/ez5cOl3ZWzsY/tp2QCebMlfE7lxz8ywb378em14MDMQd350LlMqR00\r\n+Cy4CCBF1+2p+mQK/dENJT16/SbrY8kJS3CitE273PcU8+/9dTrnheFPd2Wm\r\n6SJfYjZ7Mb9wikFrEkqB69QJvyDepGqMNJhX+n7MqxmM4UBQkw5hOdRSSN8s\r\nlHpSpj1EApJXGmoDOxumPY5z7q4WScV2zwNLO3EY7o4qJ4fFsQsQB4fshwiy\r\nc/l+XAa3ysbsMGtE3opofhhWGVkDSusg3SCrgFP8IWJF+13omWSoy12qj4mC\r\n/2c6ZjpiK1awGDfPVPi11PXBwG8kiqmxvQ3/0fPfnG0iQTF2eQJTa9sbSoOr\r\ncvLoCa6sZAwchUDkWWH1CJ+QokvSeeIS6ZokyWcj/EqV6M9Q/I3xABgpf2A+\r\nyRkH3+jhhSmi50Dgs51NziFFH1qhj0z61k7U/1d7Fe+dVXUQlJUOapx0PtZ9\r\nD2ZQjxgF2TuAt8tm70HQp2jE6U4Mf/dGf9H8piS/n0sq2qWXCUWErfeO0FNo\r\npSVwAQJ0MgooZocvgiEtskR9Pi+63CVzJHQ=\r\n=vVNf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-primitive", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c1ebcce283dd2f02e4fbefdaa49d1cb13dbc990a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-fHbmislWVkZaIdeF6GZxF0A/NH/3BjrGIYj+Ae6eTmTCr7EB0RQAAVEiqsXK6p3/JcRqVSBQoceZroj30Jj3XA==", "signatures": [{"sig": "MEUCIQDTIIYxjSfPguC0uveA3RRWvrmrxxIQ457m94kgU0n/SwIgNrcc1zHtAui1GrVAcwJG9JcZoODrAuDCPnAzVTVLuQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHOw/9EGEsyc+Axej0JWCxNEWuZ8D3l/6lrKGPBYREC0Kbu+Qtpt3t\r\nCuMhduneNyKINNt3uRUi6Z2zj+wL3MsOzZKp7WWcanxXdQ8cVROOwroU7e7P\r\nUQa5DTtCNP8HRetHIMq7NJSXpW7dAme858ALp//2Em+ScZhfkHfaLhgCm/bH\r\nXrDqQXrHomlAKsTE1rkrb/a9jDZdMhAmYFPYwqbfyXF1yMTJq9kujSud5k4u\r\nYZkVXgxZqjeWlO5RbOeCl1GHMjG9yj8BXqJDEbRdBiYe3lOSG5vIiUkCzMyz\r\nyjwHnbq75i37gdONtQFDz4H9h1yMLS+x5bq9WVts5PZiOxhiMOK+xJPYxh0W\r\nKzwPqjpCVsGLN22R7kFtY8Jt00qBi2F7ZEJfaZ7dD8uC5Fq7jhIEr3hJ2UGP\r\nAI3eCir3P8ch+P2UuN9FlqRrQ8SJbdt3eK2iLRRNmVgaVPpT/kox6Kb1SbS/\r\nGNzqfdYFfGBmltE2LIxkyIMF1mwsSSrvo26tL+520pSncObgo+T062szzBWP\r\niEqaB+xDGSHhwiVhRbtM8PC3EGQ2YcLMrs31VZ/J3uwVIt492d4HuIdMSHXi\r\nnRunLvEnObGiwA6RCmvInllt4Nl9uW26b/ou6d1t70uxVSIgbUeF2afyATjH\r\nOkg4iko2NSvCeIRIHiZNWG4DA4cRxXDWevo=\r\n=ZMV9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-primitive", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8b2996409ed7c8cab49d0e3d9ef29b84d40a21c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7xElExg3IshQPL4mBhDRZQCL+HE/nMY9GilL0MUmKYedKzHtcWiJLSoLORlVaxT6QV8P51S0gncNsqY6DXzojg==", "signatures": [{"sig": "MEUCIDw5c3UK2hGOj+zUYg4QiDaC44COhtaiGM7yA+MrcOqqAiEA8z6kDQ6rhWF1aZkZNH+hYhk1TDFpb0bBrZzhisPX4gQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ2Q/+NONcyamqut5VD/orLThfgKNtAor0x3KpV5T7EyEpy9gSNwqt\r\nxsI3+jL/qnUA6yfPHVUGbBuD0ThAtxzxMNhnGV+L9FTrMWHnvjIoIK2UPodL\r\naHTaBUs2UCdGktxMpgcJdZ7DLFMUBnU/WxcEbZgKHEB9iBQKVvHLFK65+1a4\r\nC28yBAM9rt+xDEu62ZDJBly6N1JcnZwc9gty3neyhCWepc3rMlBAG7ALlBSS\r\n90CQ8b7csVWmNL381qniyI4na+YOuwfwbBGbma58BciRMhEIdEu9sFQtICWC\r\nH5Nfyx1slNuABXPc3LGkNqsxf1Z5+qA1uXrOGD9NLxB1w5skgaicz/8qzEbu\r\nFcWHfE2bWoY4UKhuNsRj2ZW8g+bdb4GkCukD6w6hW7VBCQx8PZDQjlQDysVC\r\nFh3XV+HuCoprBSetrOgBC0cm/D6w9kHiEkZb5eVLVOUu3q9XO1QT1R6xbIkm\r\nJqlryGmxob0IjT1z9xYHYPFHOBmgNP5sscQui/mfnrt31XrjeBvZvdvFnabM\r\nDDSGzATzm+/ug7ZOvaqynxB9JG2S21WSswgy+ReW0L6Ram//5T6AUTp/0/iv\r\npdEXnIReeux7UojdQLGUZOET9hVgsD64uJyD2tzjVncjzXIU6H3mHT4suAsV\r\nMyaSWZcSdU1Ol0w630VnXz38JftwCnIoCNQ=\r\n=Ywfd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-primitive", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "54e22f49ca59ba88d8143090276d50b93f8a7053", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-zY6G5Qq4R8diFPNwtyoLRZBxzu1Z+SXMlfYpChN7Dv8gvmx9X3qhDqiLWvKseKVJMuedFeU/Sa0Sy/Ia+t06Dw==", "signatures": [{"sig": "MEQCIECYi6vPa0bSTzDroqIx5gybCVAXBbKEsn/7oyRz4iqiAiAmwkkQogkTH+pNkoHHO7q4aGc40xR0Q1miK52V7GJUOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJazACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRuQ/+LeXaM5aMOynakiiXLE9N6XLinrBeHyIPS5rpZhOznlgemJnB\r\nG2MGQXAuNQBwoEEDenCsTuvwiBQjwp2iz+UQyxtyQdaGO28OkeLMHBKNnBcX\r\ncT9GFiYeLYyxkjbJdy1bUq5fwrnhSKpInjMyn78SssYAW3j+Kb7xmZXX9B3i\r\nE+uTaS14KhHeYRRWzsNv5pccGQHX6a52aVRkFrQmXOGrC1S0rANfcbHEJh86\r\n/IKd5+UIZ0gy0tLWdLEBp6sCaRVI6slDiABwcftyEtF8KwAWGNdQUpgH/t8c\r\n17BQnDpPMdnzGTgqDn8K8SkcqKCgQQ6p7y+6lnxpHBFLA4C7Q/Q6D4dPIyAp\r\nnCU/HolEC54tcvBnoJbSuUkoW7nrTHHY5QN2XIVI7fICPsLtmT/0PHwMZIuf\r\nxWxhES48OmdMAcCQ0sSoDZghRBXAcbREHE/VCSj5bYj0cdUXOJXatl6udFNq\r\nm5fcXY5UAILgi07BLmgBAq/0SC7CIHO/HxNgfsN/uKLBB0UeRA4ALUsSptSj\r\nkAGsBSUEUKECkzF3jwlz1CtGP4Lzhe6LS7/HDp+hOnGfE/oqqBoBiLRmJ/V/\r\nUWlydNPz59/HFLvL039ElrH3Jm5tqg1IQ/5pLGo86BlHIX4HIUuXFTzN0iu7\r\nz4q+UpH390OUYUQMafnJ6VoEvaLWkfenOws=\r\n=0w/8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "538b039c2915e92dbc01b424d198feffa50e1b57", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-L6heTcBSTh7NzDAq5c4WcTD6IamCR2xcAmz6zeX+dHldyxz4iQCo+kkjtlcnNC6aZMNTB8EZpnKnIp33unF3oQ==", "signatures": [{"sig": "MEYCIQCPaQj7UAVbnPhyNAS9V6W3oeX/XrUFh4cS5YymThsJ+wIhAOc1yaqVZdVwDfCiTvoz6e6kWFUqdw5klXvIwk4pSokG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2zg/+Perf3fdnUcQ+FiPejgJaPsHQ0OfnD2yn1nh+mBai+vTVnbKZ\r\nsBDn7Ge9gFN9JouW9u/X/NnjoYexrvi5MLXT6Ol+BXYkUPmn4voTsrX7LcAW\r\nbt2HDMu1woglNOAmfhmI5uciYIwTJsT/f2t02KCFC5u6ihh12WgU3ij4kXud\r\n9PrWcfnMRPaLCUGvS35k3EC+GmIu4atKyjV50poBWysiEy5qhM5YGACkDAcO\r\nc3rVxZ8FRG8SO2Ql2cBdPdfNepyMdZJGQITlggoFc81QVeBPvcaWYwtTQi98\r\nZOHAvZfRb/Au/oOMcsVMwwHX8ukfy1he4pAgCcAlOSYjt25pPTr1nEDeQpmn\r\nYefDjCpNG52w48iCRs+bff/xsppZjoLER2saLS8+EF6vsTqmkc9AOoPmYUcl\r\nuKkedoaK2QnTPpbL9TOUVGq7wB3sYmy5d1+Jt8qlLR+xqloCe1r6bXu5cMfO\r\nLcenb2n+qqA9YVZysqlAyBSUp4XNRZEyxa/48cKkKd8Ys9Q9XL0UckS3HcUY\r\nMOf0ZEdrxHuWs7ffXY/CyomjV057b2QIKnSF7zKGt82x8ad+mFuv3xP3CWzG\r\nKhF1QdbucjbOrLzB/63DkfNCs2uniWkKOnE4mXEcLb8xm5Y7p7vj3hk3d/HJ\r\nzZo8nwIkzUVcNuGnRuO4/WlQQ6S0Wgj5uZM=\r\n=wzDI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aabea0c1d7629b39815765f0b5cf450bd3f1c8c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OfGGhEGiPBg7uE46Zd0f0cmDz3w2qkk/nTsRfaU/hFApYlR87pPwVPc6l528IYGsDK6nyVomMM7y1AehtlVzPg==", "signatures": [{"sig": "MEYCIQD9+2Mjkz8uQ7SErES8rw1PpD/fPwn7Nm4wXeQDXt9iuQIhAOisgCUY/TC8aVavY6CxmC5kpbOzurWR8kAVjTi4LFuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26613}}, "1.0.3-rc.3": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "95ad8ab1aee82b2f63c81c7ce32c1c58abf61ca8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-oo03vr+3McllU8ZyF3IhHX4ikFAzIbq7Zoc5+5P6Uym9t2HyD2NInN53uS7vusSWXA+ejjNYYxM+0Iv5ThA+Ug==", "signatures": [{"sig": "MEUCIHTjHUyhYlk7oSJ5qmerjeT1zXk9/BbJ46GCvRJMpc4cAiEA2VDq+WXWPAWkzT26juvSLo9R8odYyi+HY/u2yPYdIAE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26613}}, "1.0.3-rc.4": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.4"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b473599e26f7a3df77076f751fc3be4fad4ba366", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-KdeeqrHdb/uT/qBfDT9W6qZPmoSVj8LR0sSY7qQihwCxpnSgme9i924nMMDvCBhGeD7bjoDWfzXC7cqVbyMrOA==", "signatures": [{"sig": "MEUCIQCxnBCqAQlsNUSRFA/5cB0/OVC0FjkTn566sk+bKAEsHAIgJTokGNI/2eSEXPRpgTmuURnNykpTSXFEdyzJC2u5ZS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26613}}, "1.0.3-rc.5": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.5"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fc188c6f553c8c9a1994484142315645bf0a9342", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-yLmuJI1UOgF4A7toFt7heyUIfCptnG/Ms8yAMAEGVWWJxYW6nHPB0CP0Yjhed0zkllb3yygz6fAUxguZNjfGzQ==", "signatures": [{"sig": "MEYCIQC3IJy9j6oeVz7dVjIRVKVv6OXc91eq3cRvuv5BtJsKRAIhAMddOCNEThkU8/ihsIqwixYDKkEB+bKVuDPKBKrRptf4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26613}}, "1.0.3-rc.6": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d5d945ae6947ef5bc676ceffe26df69f9ff432d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-ZKKzrB/lb+Eb9JxTU5grEDTx2b4U69PJ57VclHTQKta5L92E3EOy/EmZYHYDBcZZAHyrUWDMwArhndY9Gc/mZQ==", "signatures": [{"sig": "MEUCIGp8ME8cnWbmFoQPWsKBWNkD1IoNaKD7k0AXEgt2p80LAiEAvF9xSD+h95I4DUVR5MfypEJYF/UIsbDOx/bW0UYMrhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29747}}, "1.0.3-rc.7": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "da8c5f9748e74ca4da7ae0d0ff90e32620ee1d53", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-HYhXRso1nPuzJaqdvX0AHhD88VauEsqHKhWHcF8bXd+Q/+l330orJLFgyFb0G1uWPgIN2sYyMfyMnMPVk8cU+A==", "signatures": [{"sig": "MEQCIHApcpnkXm5UhK6ZSJ+i+fR8Ktu/8xaSeqNm5l+LBj27AiBWuFeie4kvf53TI0/ont4EE3YtShaa200W6uA7dzHoOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29747}}, "1.0.3-rc.8": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.8"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4fdcc4d24d22c70a3f5f28320b88ae9a78905394", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-WfTYqwcE3TQ0eoIdj3yqIWwQv/p2yxdXCFzqgtVFBmQXBn4DiwDEIUgoGakSV9mh7DqJGg+BW1hJVvjfuODfYg==", "signatures": [{"sig": "MEUCIQCSDvnkNlKYKE0YkkH55YNK086KG4dkOMA9AdFukYXOFQIgai105/nGnXg6GXA5RO/d0h9qaw4AQGI7rAM2SZzCiSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29941}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.9"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fafaf12adc9828faea73433f6e3837207807d166", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-78AetBXTbIgkrmdJMm40UFU6oMStu5yvVwjQwcwweL+5vQt4hLyAoRs2To2bp00tL0tqIi9my+oDM9MyJwMVnA==", "signatures": [{"sig": "MEUCIGkeW4+7+LDVh+HyAUVBU3cd5YKpO+eaFt1ZevTn/haUAiEA2TNtC/+chX4Qzmb+PglM/Ilk+gUQaj0osBw1jl3xwcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29941}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.10"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "26c72fafd471f15e9570b7ba8598bfe5db5f8b8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-gEwE/EvqZXhBd89LgUYrW6TYlqzukxQUhJYqjypZx/4jIHfHITfuRMkFm0fysUN0UbTEfd5EyFUSGCRHdeNawg==", "signatures": [{"sig": "MEQCIHKl5mrwQ754lYtHkps2tQI3TmofBKiEjk2yyIhshoDJAiBuk0mmIpR2O5BZQ+DX9e4iri2BXgCCy6py2LItizGEFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29943}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-primitive", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2-rc.11"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c08dd2c57177dbbb14e8677e49a04c7075ef579b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-dzYy04F22jFw/mWZn2xuUpl3CwhF3DOl+wiwkzfsSzZkLrWKMf5TAFX5ALPzaGGqzvTrH1LPgBnocBiWZ7MG1Q==", "signatures": [{"sig": "MEUCIQCDrC/kyfI5sILpWxjWGVV2nfjNcaHw6/pEX44nqj9BsQIgb9vfyCmW992x5Agh5kF9cwh01dqcucGkIzo91HEWQ8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29943}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-primitive", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-slot": "1.0.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d49ea0f3f0b2fe3ab1cb5667eb03e8b843b914d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-yi58uVyoAcK/Nq1inRY56ZSjKypBNKTa/1mcL8qdl6oJeEaDbOldlzrGn7P6Q3Id5d+SYNGc5AJgc4vGhjs5+g==", "signatures": [{"sig": "MEYCIQCF8JkjoGltFrVnVKB7N4IXk8mZ0R5aq+K5ozpU7Rd5ZgIhAJLgvAQ7mlVvNHDKmvCOpPJ0gpBJd1cMcQ9KpDzv24Yr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29903}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-primitive", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "047d27f29a96d3113dad7c103273134c2018f1f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-hO3ZRdqRkYLVwzGJ6R+YTkS6oNOyHxmZXMxFL9YOmXCl0qbFVsE/WmFRtaYCyMXvvfVHVks1CckkyjztwsqvCA==", "signatures": [{"sig": "MEQCIDxrKz78mxKrtewklVg+f2qiIn/ODbJ3UmI8GqZrNbzhAiBAZjsIUUtRQyzq9M4sFrE5ZOpwXiRQTA9V0uTK0EYwjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22000}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-primitive", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d6acef2b0ec26007780a7713a6c87cf4bb944219", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-4VG+wg1DDUxZIk0xeF0IK0L28bpSrpbfCPiLKpyFcKHdp0OmF5GGg8C5u09OYxPJpyngEoZwFTWTARtvtRw6oQ==", "signatures": [{"sig": "MEUCIQC7SB+ZBQZBV61scQ0AYLg/5iUBqFfqESuwizDpP0VbOgIgIjYlMj3UDSN1wTVa8bwNZDmC0MBB3KISCspWzmMzwKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22006}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-primitive", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "506538518b82d809429b6064a52ed3da07f335e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-I5BFyqfxWP0/HFvwp6x4w3NHWDo2lxJWsANTZR4Pu+VmEqk5Yh9AVajAVZbaE3svzm+bOEsoRuql82D3Y3B7Xw==", "signatures": [{"sig": "MEUCICpNN/y3vTboHRqBuUVIGDNrfPiVWpEidyfpFXQwrXJWAiEAtyPoBGlujpOaZupgtR3wEIJpTzruUG89WZNqvgL/tRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22182}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.0-rc.1": {"name": "@radix-ui/react-primitive", "version": "2.0.0-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.4"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e4aa5a6cd232a25e2d43f12f6db3416e7a7e69b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-CTL46/q/uUYDNaCMWfe3hBLOlHKlbojo7r6RNdPhkniOtBnobmiREuiRoifMAxE25bkecNoVv1tl4lgjIjJf9Q==", "signatures": [{"sig": "MEQCIDI8IQw9FEsXE6UKGHKRPjio7igU/5tmyDLBn+9D43XUAiBFNda5T48CuwVuN4bAX8aIZnQGOP+SlWRi/3jRJfr8zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20862}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.0-rc.2": {"name": "@radix-ui/react-primitive", "version": "2.0.0-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.5"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2fc8e9b480af099f39a2990d7ee184d86002a657", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-nmGkOKHPiZ/UFjDf1kfORK3xVEuZvXVdXD4pSDyA/PWRZtFx4Ex61m80EzA18AwNJQIjixHXYXzjFJj2FeZglA==", "signatures": [{"sig": "MEYCIQCXLgj7oqfZjIcWdqH9zNY8eB6prBiJTxvr8ryr6Fj7MwIhAOmo/EtU/DbjVjFv1gHXXQ68KOWgmkjUBj4PEc9kVlel", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20862}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.0-rc.3": {"name": "@radix-ui/react-primitive", "version": "2.0.0-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.6"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab65992af9fab2918e1104143b8c6cfb46b947e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-jkjA/IUpj7XNyfzfERnl0wYr2LxwsXR4t8pzbpM++dqnW27yxp8nN9y0v6pnWkVhXIdbEZK+yKQGu7CQZG48Ig==", "signatures": [{"sig": "MEUCIQDpCqySIe+l5t8wk0ElnviCr3uEpEcXOHfTzifLUhDTJwIgV1l9JHpbPRCEXtc1PtEAiq51k7uGVZ0RBS3DXMR8V6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20862}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.0-rc.4": {"name": "@radix-ui/react-primitive", "version": "2.0.0-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.0-rc.7"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1cee90f79cc67da592193ecc99a211f989ffbf9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-zGa/4nOyATE4EHYf7bkA5qi6THaTTZlTU/sf3C807FYC4di+V6PHmBc1OX49hJYpZkYPjZfbOyn17cLuYDQLjA==", "signatures": [{"sig": "MEYCIQD2yo8XWLR2vzNthbnWdqDZQgdHauPd2oa8OyXcx60xFgIhANhCK2rHu/kA2oeOuHZB9LvYHRAmxxzLK8sSTjGWgmrf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20890}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.0": {"name": "@radix-ui/react-primitive", "version": "2.0.0", "dependencies": {"@radix-ui/react-slot": "1.1.0"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fe05715faa9203a223ccc0be15dc44b9f9822884", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-ZSpFm0/uHa8zTvKBDjLFWLo8dkr4MBsiDLz0g3gMUwqgLHz9rTaRRGYDgvZPtBJgYCBKXkS9fzmoySgr8CO6Cw==", "signatures": [{"sig": "MEUCIQDEcv/E1Y7/LIuCXziyE4KiNsFBGTWUekFXN1jnAdpqFAIgaehWV6vXe1aJqnjI86Zc8LSGHz0htqQ0zS2K1SJ72uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20852}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.1-rc.1": {"name": "@radix-ui/react-primitive", "version": "2.0.1-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c6c2d67e444c4aa1e77699a4d306870dd0477593", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-TxdMYWfTWUG2Yhrd/mrBxX+hElCy+QvajiXISO1qw4gJ+3BO8QmfUW7EeZ0ik3hCD5B/EO/xudHexlz3QkDDNA==", "signatures": [{"sig": "MEUCIQDkfGBQbaGif+j+wDWztlrzNaPCWPbhggxzTHtwdE6DtwIgPbuYipuKm694qfSjoakJXgq2iw+t7A3tOmrazt2Fk6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20890}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.1-rc.2": {"name": "@radix-ui/react-primitive", "version": "2.0.1-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.2"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "269b7bd715d10e11e941b29aeccdaf804f52629a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-D9BF0fOy3rzn2/DmAd8OA9sVLYnvFFgx2TAy1Uq/fQSXitN2tNpf/7O+RP03QLTI87uNKle8B2vWWdwjcKKs9g==", "signatures": [{"sig": "MEQCIFG+R+ehzMMXC5hUdKQAe1XUsT4ipOoFPvxTt6s3HztiAiAyoss0PkXKXOHbKrLyiD/mXXJIAEaYucjvtVuVcYL0rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20890}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.1-rc.3": {"name": "@radix-ui/react-primitive", "version": "2.0.1-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.1-rc.3"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8b8f7aed0179785d65dc2f854f338a2aa6fd9bbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-3dKjtvPCQvyHSQIbVuBU16wktEtQr+JqbmWQCkkgMKYY6AFozl2PQM4zISMhuJgYPrYd/LJzmVyJiVuZ/0zvgg==", "signatures": [{"sig": "MEUCIBZ/r1jpHKuADYArnZui1DOhQTqEnawmxISzUCwYPCVuAiEA5IbsfhHRoAGjDy/YBUCfAts3KJZe/KFeiWSDO64Bkko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20890}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.1": {"name": "@radix-ui/react-primitive", "version": "2.0.1", "dependencies": {"@radix-ui/react-slot": "1.1.1"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d9efc550f7520135366f333d1e820cf225fad9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-sHCWTtxwNn3L3fH8qAfnF3WbUZycW93SM1j3NFDzXBiz8D6F5UTTy8G1+WFEaiCdvCVRJWj6N2R4Xq6HdiHmDg==", "signatures": [{"sig": "MEUCIQD7SiF5hVA5kQdv4nH2J6K9KSAQMG38Z5ARgbyM7eIlIAIgRtXPMHKzYtgdhWBAm+/fa69mQsW4WY8dQM3TDYEe54c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20852}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-primitive", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-slot": "workspace:*"}, "devDependencies": {"@testing-library/react": "^10.4.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b6661b97d65e7f94fa4ac9e0a70ca5a139f5976e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-cRITSyAc2825vm7QtuJoy25Rhgc1X++Y2yO36KnU7JQ1hon/IX/mUaVd7+VLg2nKaDUdqKCf/u+jO2rZuEEorg==", "signatures": [{"sig": "MEUCIQCuLHXhXxc/coAnK5RYKYSP5ZDIifXve6rS9fvfzIaeKwIgKe8DYNKJyyJkneU8IwfGgbr6sHVk4DuxXBUKPiOlleI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20843}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.1": {"name": "@radix-ui/react-primitive", "version": "2.0.2-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "15161f28cb9ef772933cd0a65e9f50abf3e231f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-iX5PbM2PidUSXf6x+tX89mLOMfyqyKzX2g05NzWr3+XGw9I/89lNew7i/pZtQg52IDQW0Ifify+c8/E1AMg1YQ==", "signatures": [{"sig": "MEYCIQDaDtFAv1Y3+QwqLaRhO0ClJoFFVqLWVr6fsymY9FQg0AIhAIc5DwPavoCms2PftoaiZl8oVIF+TkwSbQd1wQnPJFrS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21035}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.2": {"name": "@radix-ui/react-primitive", "version": "2.0.2-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.2"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1dacbed6aabecfab1a3343f028a746b4725f857", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-CgIPedjBX6h0TZdtmXAlw74qPwHQPI2DT2R4sZIzw2/FMNof/YORp35xIZ0hDJZ6yaTpmmgUhxh6n3fd0OGS8Q==", "signatures": [{"sig": "MEYCIQCI6BmzTsMayrOwoovpdw+g/st1zbbpa2C2w8QOISYraQIhAPjAjvNKhg1Iffco9TikdmyJ6ReIVafLKzVBhNnt1sTV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21035}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.3": {"name": "@radix-ui/react-primitive", "version": "2.0.2-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f4140f5eb974a9d7a19fcb1017d784353da14665", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-639Dui184Sfwfp7kYMkN6oPbdsp5v6ZGh5o0J6oYke31YAWjJa1sJBg9UCcqyjhKREX3A5SsA8GsjkM9xB/P8A==", "signatures": [{"sig": "MEQCID16G/iE89CffRVMPQiLMNhhhV2uSSwD5GnymcOoiUeWAiAdrPYn/ax6XP7GHMrj77MpmLe65ZTTtQMp6XLKYx4I5A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.4": {"name": "@radix-ui/react-primitive", "version": "2.0.2-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.2-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2ac857b651a1cd73ba030d4a133eeea16aea828e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-PpUUmK+11fR9eFjUxkBLoxvzfnElMvPmKtKSnooNT3LZzvIKA5HRhQ0wyCa9ZxKmCUrli+J/bv2OWZXeHiJ/LA==", "signatures": [{"sig": "MEUCIQDDrlq9bnrgljInpjZmfpzme2k6IgCUOqbvQ5VcCLLjlwIgfwnYyI4tYfi12i3ziiaLv4o5Id7wLjbePr2DQjuy4sk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2": {"name": "@radix-ui/react-primitive", "version": "2.0.2", "dependencies": {"@radix-ui/react-slot": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac8b7854d87b0d7af388d058268d9a7eb64ca8ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.2.tgz", "fileCount": 8, "integrity": "sha512-Ec/0d38EIuvDF+GZjcMU/Ze6MxntVJYO/fRlCPhCaVUyPY9WTalHJw54tp9sXeJo3tlShWpy41vQRgLRGOuz+w==", "signatures": [{"sig": "MEYCIQD/HLT+Ow1cN9LKRDrgBbE+XoTheBhlPnuYEtWukJrgmgIhAJolC3NrVK5+o83YtuJwvvKduwjtIQK2upviYuXXIOEY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21101}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.1": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.1", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "264954a7ad8a8a44e57d62b4dc3599936773f47f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-L+ReG/0rlRcah8EoeTSC3Nsy8i7vz+6PItB2sGaxs3mdwzXwg+P84XQhV0asIO1KeCqN/FcdMKKUjKMSJVUHcw==", "signatures": [{"sig": "MEYCIQCoL8ZJCPGK91qkvX9nwtjMgrZq3s1R+JmE2i6PsAzBUQIhAOKfiEuq8CatnQoHlGo14DaBlQgKtjf9RCbMy9pQ2KK8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.2": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.2", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "942c45b0c5d5d748679b451df0f305ce4391c426", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-BujTwm9jj/LtUUOOK36SENakGe8qIixtKDIre0Y/9tdQcqvl3mYLjchXrYAP1Ni8ZITLgROAZcIoI2+xNFjKiQ==", "signatures": [{"sig": "MEUCICG/HtPpt0zJHy5tu4I/+O/2NLMdsxrU3AKAZI5uRgjiAiEA4uqUXsGEacuIlmd7ySgUZVXlyd2gS9nUHgARpFC37r4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.3": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.3", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f721675932745c927d8d13258de9d81bb4526e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ep1cyikLKZgbXsuo3z10OewVNOK3KXtlrjcqaz3s0tZRdMIoSLf6F6azuojtVOnXJHcVNT2TIx0arK5hKO+O4g==", "signatures": [{"sig": "MEQCIChB7e95/8a2rnyO056m8GAvg8/G1/+JmJ540rLRZiKeAiBbRhniP1P0esd+a2uQw9aSSXwY324J/ttpC7cF7kBQIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.4": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.4", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "24b1df6f78bf4a3e9127981021e1105d5c4c632a", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-A6<PERSON>hlJ9bwnPOZAqpnBmOH648emi9KWyUQy1aGvALQpMcRwx5GAzb9M/Ay5qDCWKR/A45RMXUqWBIHrUd4oGqyg==", "signatures": [{"sig": "MEUCICBUodSHet99m0J+2byucp7hPjxSGTYpyq7otSZ6TOqLAiEA/UKZVbF9uX/AHa+Z6/YUFUlFu6LXxv2PkO+Bo3zG0UY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.5": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.5", "dependencies": {"@radix-ui/react-slot": "1.1.3-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ecc43d394b21229a1bb793de068e18920d3276b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-OJlP2j9V4DWlZ2wlrc9Fo0JbQ7iuHSmvRezH1vogstFuEULBAOO+8GA1G5Xm9A261BXQ9/9wyqTOQn+l63pP7g==", "signatures": [{"sig": "MEQCIAUY2884n1x9HaE3n/rGkP8T+NZV0oq/6lj9bvwYsIUmAiBABkUITB6ioz6USnr9XsuhK3CfEIada/A9/bqSF1OIIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.6": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.6", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "17e0468ffe05e777c0d5cd7bbfc42f6cc0df1442", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-q1lOM5zstw9lmwpDFXUmBe50ss91RITkmC+RO6q+tiWkhpqrzaYiI/gfwUupqgT4Pm0UWEaoBPqvNGJALBwurg==", "signatures": [{"sig": "MEUCIHcWyHbd/PgOdrl8hqBKxkbidP+k5Qr4BKpQdlhs3sYVAiEAgXwVWvXdgxGAijplXaK0SlTdcpDFAIFILgMNOLWzl1Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21438}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.7": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.7", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94d4bf6e1aadef6211dc646693ccfe6c354e24c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-GM9iCAT1wtIDoBkPWgKPwGCXPgh6Mg/ZNuVzGzNPTUp3zwLRcNzAZZwcQlqKVvNbkYMk1oFugz1jDrzyHYedxQ==", "signatures": [{"sig": "MEYCIQCPpBFEfucm/R7ShjYYprvBezSCWBK262/BRzneJFANSgIhAKLrhqzV791q+jXRFYj7qGv5jL5DXXMZouPZs2RmYp6G", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21438}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.8": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.8", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "40bd2dd576bbb82f455fb660f2021c01d1fd7b60", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-91iaJdcKN6Xbs+YU/TaljFQkwHFXoEykjnST0ok/s4GTlaj++/bT3Vi3d9rRTqDkMuJno97eqYIOlkNQcUgBDQ==", "signatures": [{"sig": "MEYCIQDCAwDWfL1Grn5umbaoX91MrGJa1ubDnS/huqz05ntfOwIhAIuF3rmzhjFuzSbsJnATbAAU08s+iZloGazvDb9qEcv5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21829}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.9": {"name": "@radix-ui/react-primitive", "version": "2.0.3-rc.9", "dependencies": {"@radix-ui/react-slot": "1.2.0-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dc9ba8de1992fc94c1063fe99de58b0470c7de07", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-T//i2yCCp2x1F//zYcSZcNMPrh8TGNyNotL48Z4SefAlPS8969vb6bDpXV4HzzsPmoHAn/x9tGZmOESGMocL4A==", "signatures": [{"sig": "MEUCIQDqAUAwEBPtcczkjZdZsZx5PRgXm92+lOr6yLIsC3bR0QIgck0xRWJIMVW9OXwyH+ob2ngbf4RMJpLKBji3bvtmbDM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21829}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3": {"name": "@radix-ui/react-primitive", "version": "2.0.3", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "13c654dc4754558870a9c769f6febe5980a1bad8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.0.3.tgz", "fileCount": 8, "integrity": "sha512-Pf/t/GkndH7CQ8wE2hbkXA+WyZ83fhQQn5DDmwDiDo6AwN/fhaH8oqZ0jRjMrO2iaMhDi6P1HRx6AZwyMinY1g==", "signatures": [{"sig": "MEUCIQC5LYci43j0hupinsj8UpuwaOHSsxogT65P+C8UDqxphAIgGqOITSZ7qyB4suyzMmMgm2DxnuQwg+hyqn1quBNPeek=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 21791}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744311029001": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744311029001", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b1f74baab1991ea7b2eafd3d4e0bc00bf2b9265f", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-rfpo/+BZmwaeqPRlhQmxma/oOJJ+li5QCnomB0j6zE9Hzoy33npo89bMUTrTYGM5o1c4mmNoZhBC2VwfhULoSg==", "signatures": [{"sig": "MEYCIQDLY0NMo8qUQ8fMOH7ZIXHyJwIzYbV3co0CIHNYX+hkIgIhAPsY4eMqzcOtlWBoYM5U0hm3wCRa0KH7CYsitRheJhCT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744416976900": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744416976900", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b09740d7e2ca4fcd899ffc442322d223dccc166c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-cnubv10Jyh/AXZUGi6nJgM2ayFYtTHask2gVF+77PtFOeZ3c4tC43N+pdoY4ARWwLNIygLiOXfLy8AJImdqcsw==", "signatures": [{"sig": "MEUCIQDlnLkbG4jd0YFypXdY7fnA8+1JQwY1UQZlNx4xzdMY7QIgSCpHs4OhH+SRc4CEP6XM/vmAAwxkrrwnsQoag4Bzr4Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744502104733": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744502104733", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c07b9da29df4cc06fc595e0ba1c423eb48c609ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-1EgzuuPkuO8Ra8bxVEcFcHxtkHZx8gNI1lZKUhGeKIAvN1nVpRCQLUsv8WxKkX6cAHqBcPRt0f4eDVmU/b2//w==", "signatures": [{"sig": "MEUCIQDezf/ykkCsXKNbdKbsvQ38qEZ6hNFdAIQOwILnD98DLAIgel5eCHc3/As1/vGJRenRPCwiBQb2BI04wQL4/PI+DLM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744518250005": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744518250005", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e487aaa2c88a6242468e762ca232ae508c34ee18", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-rS02UopoPqC0XpZXSos4i7mCPW9EriOORlxUi06skEtAIGBSht1OH2dJ5QG0wUlb9heHFBoaRSGRBlXF04+bng==", "signatures": [{"sig": "MEUCIBFm0ha5WUSKaK506kQ4En27ulZ7fKBveBeOGb82t1O5AiEA6TGZF5ydQzZHaAUaY1h2FeJ8xJVzfLT1FCjDbk0F3zE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744519235198": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744519235198", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d97c2f68e4202e6bcff866c0a84e68c2d3e3fc37", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-xmbZS+EMbseIXHrINy9F2nA3A7aoyVQbB4cgwmDO6aAdv1+doKra8CQKJd8zm9dPIo/OgejhssCb4FUspmiTvg==", "signatures": [{"sig": "MEYCIQCvCmscdkUFIA3HX6kNJHKwwuZsi3dArneS2A9YmJ5CCwIhAK5eeJFkZC4n2HMrFCt9GZeOtkfwTcdM/uh7Mrwo3mDC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744574857111": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744574857111", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "11a8f4632befff4cd92f13387a22f5504bf5b83e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-XCtHwY5iti85Ja3+R28vQGbIn5iIEe0WQjUs11HIUYjVT9f4vVrmWs5lcY0HP44x2gYmQjnHhgD23vh1gc/Z5g==", "signatures": [{"sig": "MEYCIQC/D23H6ZdjuG35IK80TXfLqiLodF+v4EYCmcZxmCV8ugIhANOpJ5ode7S8Xg/JFoPckOngg7tV2hG5B1Vk8R93jLyE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744660991666": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744660991666", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d691ba68f38a3ecae15c2810a13c7e0e3097cf51", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-3oY+rzTnSvU7rnwyi4H1MYwm6UFJgkm2jjPZlUQepESo5V03Pr4D2r575m4x9Xj5uHGqbF6qUdBXkWEhfonz5Q==", "signatures": [{"sig": "MEYCIQDkSzDinjRq15i5IuwElIH9NnxDATpQv60tXAiXwTpAxgIhAIfdczZftGUAemWiVlq0bILjXNS5eCcSCluRAAAxM8wO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744661316162": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744661316162", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "85d9736018035443176abb97c0cbc44bba3d0928", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-qybOvZM11XksD883Ut6MmhEOP15TFCBwAo3gXHE6F6vosyetrvisKyGPYenCIYWXRBZdxtu4sKfgIvCw/yt74A==", "signatures": [{"sig": "MEQCIEg0f/+H35ENyc3m8hs/baIlYH/I0nCdlMKsl7RnjIaNAiBAjyickwOmOcaWtu9r7F9Da2BUeeejRnGj9yE9wK5ieg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744830756566": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744830756566", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b064e621730ea59e7e0262c78d4fc332dbac33d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-m9yKxgvqzIycdEv5Sord0KF8wHsQLCt1bNeQyTIoduezzeiD2bMlG1DPgRDsDuBJJVfwl56J0/uJ8QFpSI6kvg==", "signatures": [{"sig": "MEYCIQDA65+8TkPBJcURL1ALnRTWWhuxtGbbxdLKbyEzsYwx3gIhANcFR/OFaakOH1qgxHovpYcb45xUXv5dYV2fwObB6Qxg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744831331200": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744831331200", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1d9982a6f9d000936105083d390441307bcbc7c6", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-zEnoyKhuonpyrQp9UNw2vYuJcewWVUg/7h289qd1DOexyZUzFYgnFgTGWvQvjmKo8bqR6XGnLAElf5nqUT1C5g==", "signatures": [{"sig": "MEUCIAcI4iFV9S5OOm7XLpDd90yw+GI/0IZOcM5FAgWXJjbLAiEAxMwTimLfIHyWW7e5+J43AbYn96a6pyjoWQriGhtBxV4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744836032308": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744836032308", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "402e9299d1270577865a9798f605cb58497473f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-ykEomVh++GjoKx5PH0mxNCem2L1AXCtQdj3ihVRYH6KbBntWqYpx/U0TIC1IQkKFrajpxPE919DSxIspEajtFg==", "signatures": [{"sig": "MEQCIFc5jssuq06N8A2DkMCw5GZdQx8hKb41ghm0nD58UpAbAiBmihfEeSgjsnzTrHdo7s0r1Zdg1Nsu/btqNJlpH+8+Bw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744897529216": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744897529216", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "91cda3b95b6552f62bca0a36399e674917690b8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-ErKDhdMxlUoUvyt/OnX2wyz23upuN0F2mYIqUAiRfL4XPhTz4FQItK0oApMKMlmCakzDU51b8f8fivBnnYrqJg==", "signatures": [{"sig": "MEQCIHWPcrhGVUl5buNehmg4Dt3NGH4bZO+Xsdg0rcFESyWKAiBSrq0GRojWcP+I8CyfktL9AsXeLJN2t9h8/D2PtXeeeg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744898528774": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744898528774", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "380ae488535d64fb81362c134a20f80f22f3ab6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-4PKzpUPlrRrhNq+TCMyP2V8957EPb4EmHMuvEM5Zdg4SYD3kcRnrdvGorW9EFmjljQvixAvW/O+RV3pF/TdN3g==", "signatures": [{"sig": "MEUCIQDibN2VzNZHZDLSs21zp5XU/gFe2Gru0v6kvBX56d4w8gIgLoNZWNWcea+ksK05hTwEI39ZEtaSpp8C9mXliwnXGdQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744905634543": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744905634543", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ddba19fb9a9bdcd545987b109adeb480fec8d02e", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-oTWjpUH6Io7Bg+SvWRXj+Qi24zMc74lVUdXNQxYUDGY84f90kbH5zDXOksGZlYPhqzP5clzkiZjPdiY/R1s52w==", "signatures": [{"sig": "MEUCIGR9EEjlyBZzkluLkkVjjpnrB1p81GDvF/akji44PHq/AiEAt7wBZsdVTtLFBPQz+/nJYQMNZwvDIz3aP2rWqNNQacA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1744910682821": {"name": "@radix-ui/react-primitive", "version": "2.1.0-rc.1744910682821", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3b39f1579d24389b391b7cb28d6b33e86ba5e5fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-6rZ6v9ZfKhQvJIIToDCqay2rQ/16f5Aslllk0JSH7fkEYRA8MjM4uYj+CtfwurxAzUQeJ7sKEE6y2jgRht7i/Q==", "signatures": [{"sig": "MEYCIQDVz9iSBuHiCBghU8PFRL25FHDRvMHqVKj9oPCxl2DY0wIhAOoQ9hba1UnY82hpP0xecSUGWq1diqeLl0aDOW0kvP/o", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22435}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0": {"name": "@radix-ui/react-primitive", "version": "2.1.0", "dependencies": {"@radix-ui/react-slot": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9233e17a22d0010195086f8b5eb1808ebbca8437", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.0.tgz", "fileCount": 9, "integrity": "sha512-/J/FhLdK0zVcILOwt5g+dH4KnkonCtkVJsa2G6JmvbbtZfBEI1gMsO3QMjseL4F/SwfAMt1Vc/0XKYKq+xJ1sw==", "signatures": [{"sig": "MEUCIH8cH5p5mGmcn5ZYR5vQ8vYL6qrUgl7XDaJgxPkSv/uTAiEAhPc2l4VQ7k3PfQGGwqCojK7SZb0lNywuC4mW+PJIxtc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22418}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1745345395380": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1745345395380", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745345395380"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b027366e1fc61dec5ae294e91ce4b00e448bb836", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-lXSNxSPKnQNsHVd/nlC6nhCVbbnnke0PwfvaT1e8NP2rWzBMBf+jCkvB5YAdXBkKB0c0qnR/ejRTewgQnKeIxA==", "signatures": [{"sig": "MEUCIQDuhLilBb/1o2uAi98x77YchnXCK4wDpPq18+m4RrSgXQIgJNLtr5IApDjJsvye2VirGwf2hnsgE0owZQJl/5Aq5As=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1745439717073": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1745439717073", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745439717073"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53a5c4ca38d784a99c1bc635fc3917f1e7d10709", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-3B4n4NaWWIYz+jc2F2oxonJy3Uns4aSwyiPfo4QMeN7mHi6Ti+YWtibjXYeDnRmAiz3LqKOpNK12f9fBMLPQ6g==", "signatures": [{"sig": "MEYCIQD3FDwEMi0JjQV3dsqdpL6BC7KQCFjdCZJtcYJgPVxB+AIhALrkLYBaqS+ViKVzuUB9bo8SSdPkERRczB8NOwMVJCNX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1745972185559": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1745972185559", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1745972185559"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "40fbc315ffc7baad4f35f164fc65eb10463908a4", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-9cdP+YK6jfonVFl3TgIqy2OYZLxU7vywf4W2FKM8ivVMHKs8pzG7PSkmXAD0JplFO3GNYZPRmhIY/EOG761v8g==", "signatures": [{"sig": "MEUCIQDVy+vlUZNOpQ3UHSmxIBvCRYsYQyVte84AfyMLfTKBYgIgKWpegLnvMS1xlFCOqxsdviQw+PtBs1nrY772Tb8ukMI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1746044551800": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1746044551800", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746044551800"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff42dcfa85a23dd4565dcb685064a28acaadb1d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-PvQMD6394Hl5EQqrU6YzdXzpzOTC3wyLidXD5fqYtRb9awvmq4NldvOglMLEefCCKoMq+Cwx8NEa6l9AAQFfbg==", "signatures": [{"sig": "MEUCICAPTWoT/etpxFjITZ5/kRNG77G73F8RmHMdTQXOLhL5AiEA+uFg+HCPpaQ8f1f4SqdzhDN+wyyyD3iLpn01U0an0T0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1746053194630": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1746053194630", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746053194630"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bbdd2d10616d0b57a89470cd49565864c6dcf14c", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-UcjGsvCq7BVCwAM+7nJiuzF4eZTsqFlYpS/lSB29zriewizVhTsUIl2jWlkBstM2ozX/zRIGP8U9whUfKq1Y1w==", "signatures": [{"sig": "MEUCIQDF8fMIZhK6daZ+q5ZuP77LTptqwd5ORjCuKgnTPKOfpAIgLXSS3O1ZzvLv8Vr5EB82+PkvQ+jy5/tp8N1SD/inGSw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1746075822931": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1746075822931", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746075822931"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b67db5f2569d8fda5329dd139d7ca696eb6ef0c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-Mo/POqMbol5jszlxRwqbJUPa4cLElul3XBfKeomI4p9KlGCAVz6LWrSwQbQeCgyXJaE+8Sh7/GuFgj8Z12+6lg==", "signatures": [{"sig": "MEUCICQLuV9/nrQzi2xzrd9+Lhxndip0TZcMcDBKCqgBoxCGAiEA4nfUtgM5NTyyfovHuW+PpgTeTFhbCEGwjX7eC5Qi6Go=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1746466567086": {"name": "@radix-ui/react-primitive", "version": "2.1.1-rc.1746466567086", "dependencies": {"@radix-ui/react-slot": "1.2.1-rc.1746466567086"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "865307d18c9841a08a2a987ad5ae6e919c5897b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-6z90trgP0F9DQR57Mct+4U/qfXF8hnhptga+hOnxOuigZsZV39gDSe6RUUu8QaSSlq4+rUtehdU7P+5lJQ+0yg==", "signatures": [{"sig": "MEUCIQDDWF+J0hiSEmH2aa6FngdiCa7WNOIHoJ2065CA54hbpQIgbuYa4olBqevoCHrnRxpJeWY3xANVHmHZuzlMBpwZ0G0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1": {"name": "@radix-ui/react-primitive", "version": "2.1.1", "dependencies": {"@radix-ui/react-slot": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0c8f175b3fed173e42a3a4efa81e3abebe1172e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.1.tgz", "fileCount": 9, "integrity": "sha512-+Yil<PERSON>eO2cNOSdE7hh0lp5AJCRI8bwl2sNVWRdl9aQN3JUXZ1/TNZomOAHFvXamLXj1/KPSyjA2p+/Evl6rRrqA==", "signatures": [{"sig": "MEQCIH5BegrbOLnIZx16UhktHvYPTZjl3PGWLTMLjgdlW5NKAiAL52ZA5/tx4lFnspSrR/qqkK9LKKLlzgj073BkSw5tWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22418}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2": {"name": "@radix-ui/react-primitive", "version": "2.1.2", "dependencies": {"@radix-ui/react-slot": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03f64f957719c761d22c2f92cc43ffb64bd42cc8", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.2.tgz", "fileCount": 9, "integrity": "sha512-uHa+l/lKfxuDD2zjN/0peM/RhhSmRjr5YWdk/37EnSv1nJ88uvG85DPexSm8HdFQROd2VdERJ6ynXbkCFi+APw==", "signatures": [{"sig": "MEUCIA4iZkh6BydioAnGCMa6hY+o5hFsUWCTcfajoX2XEr+bAiEA7EiJRBymoDodg92G8jla7wBH60WlUq8uqhm8N7dak0o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 22418}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.1746560904918": {"name": "@radix-ui/react-primitive", "version": "2.1.3-rc.1746560904918", "dependencies": {"@radix-ui/react-slot": "1.2.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/builder": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-wx/QySn9MchFdB5Uy+GwlrTFAm5IiQHJzZEp2ge3x+Y0kAyjF8qDsdWBW1Oxg21lcO2P8/6KNogn5s/KrwP24w==", "shasum": "c660a2855fc1f9f0ca29c8bd1d9186b823ddf2ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 22452, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDpxmAjDxJFiHWokbMlMZMVfykwDhONLlYhVuT1uO8XvgIgKWLKbTU8LoTFXouC69HSPpRVErXxB2csFK+Z4HJeRmQ="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:34.083Z", "cachedAt": 1747660589302}