{"name": "@radix-ui/react-popper", "dist-tags": {"next": "1.2.7-rc.1746560904918", "latest": "1.2.6"}, "versions": {"0.0.1": {"name": "@radix-ui/react-popper", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/popper": "0.0.1", "@radix-ui/react-arrow": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6de6477ff534ab56bfd864160e63cf6d2b76263d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-YeBJohRY/7WSfixTTNNeHoBid2kHV1/AAMHstf407KtlrAKDS5b58otOzoObiiaETU/w1+VSVg1cE+NpHBBAsA==", "signatures": [{"sig": "MEYCIQCH/Hy1qAyr8JNmkCpmXWFMXPnCh+fhgqOGH5oncIfZjAIhAJ4NMsdDMwQVAC+QwYpjFttBmbABW0B6zoj9W1yvfDPt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbSCRA9TVsSAnZWagAA0dAP/204ixF5UISSBZ4fV/b0\n9+Iqw894jvAUOxx9EOGOLYbYOsXiOezozSZQ+tDcbddB4qpmYjyLQsoYYA6/\npDw4l9bSS4CBUDc9VsF/SObC9o7Fa7Kp4HdYNqlJFoggPWgDGY3hMDDhvAgx\njWzcFjCDHg1XwEFWqodoczVqpduzcLwApir+u80Rtb9l/3CRZSQHGqpDLrdn\nvr2LQrv5THxUQ7NKLojQS79+2k4d5yN1t0YRSErdLIJ4lSX4psHsFYE5g+q1\nQ2+ogYJjhZ1YOeJIPA3FAwBsCr2+CKSt0Dq4KR0e91KjoCrMM3jlwvWKOyVV\nFtSC41Ag6kTnoo3lWq3NhbM8RP02T5vZKal4Y4eeryEtzfvsZxFGPaehfljK\ndGahktov9SjHEjJ/1+FSbxE5AeMd3uOA1pxiMa0jGK/LAHRuOGiQf7CKdTBf\nSzT9i2cgsXJMnxHF8zNOIHE99JMh50kUPRdyxfh+ti32aA98fY7Ol/a5GS+K\ncZiXvAzSDycp0Ev9nZmDgHS74rQ+7SRKD3+5LnD4knoSeo6YcA8dTNrVdBLR\nOwNsc00YWR1KFZHsjVph4Rb/RrqTRqpKcqXVR4W1jQJOX+fmN/YfJqC9mLGq\nWW2yOczC+cc3sdYTu8EMd0t3dF0KwT7lpgYikGcUe4uE3EYej7Bk7vkuZFWG\ngl6O\r\n=4uKz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-popper", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/popper": "0.0.2", "@radix-ui/react-arrow": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e327b053d01afb87c202a35cd9904dcbb75e30a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-bEMObjs4s31i9443t+s2keeanmkqKEkvWEn0DIRfYO6QbMORCvWoygJ5YY+QDA301iGLYKkXCvtyWZod7Su/Nw==", "signatures": [{"sig": "MEQCIATy8+anrfyCbtb9pdX98BegyamU+rkgxpzOCzzSKEUSAiBh8HEKU4nBkEqFFt79eFz+Jv/RC84/0CzRd6YgjWx29A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvNCRA9TVsSAnZWagAAC0UP/R33yi+BVmlhB0hUo3Vs\nbgvthve8Dpsys5HL87LZ56olZErG8q72kT16FVEpqBbXqhld5CLwTKxleNUL\nDgGvgW0+0S1l2gZ/6N9gCgeZ4ZzOLOedSBolAWo3vKn8YTeP2Scv32Upb2gG\nzoOGxuitNrR7MTTZk4I5DcmKNwCmPBTNJ+LV2ExnmPNyRuk4rXwZ0SU482qy\ns5LzZrFe/IWkPYULS1OHgepMRi2Pon+TL6nK97nh/t2lS9P+LhYzU/Thi5Dm\n5ujp9kKbljCH0iERif1VR24dWVgd667j4VdVLJGyvpeJqKub9hOAC+9VUGH4\n9G9z/NhTJxZAT/PUyyoP1e+olRmKUtno8VHVRi2Z0dx64zxoIvoVGb37OGSY\nesWArOUtBTtRgBFA4dyMinDacCUMoQaD5YBifk1/OcmqE1HDl6P2oWf8T9DC\nwVwXUZKHxlgS/Mi9X9CPHxstnfGRpVvF/PwtncmyVm7paVm66sh03+beY2UE\n07U4YodWC4w0ziGGAa/aWQzBGRAgP7ESG2ebkesof7pbYLQdL4lDDKSDru0p\nyMR5kghnTB6N//1JBk9MBEUiiKz7EGLA73T60FSzP/vCUfBSDUFwKP0HRZf8\nkWCpUGo/fayaxTIpuAK+IBr3UabnbIWyS3M3yTVhcohOtSaYZdVtK8aEdCXg\ntt+L\r\n=8l48\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-popper", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/popper": "0.0.3", "@radix-ui/react-arrow": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0a07f1c655db504355f962f0402fceef9bbc7112", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-9u6ZjT16Mj3JixZeI23KT7OcSzU6R5X3IeHzdFUzMUBM4cTs7MPUcXPqZSHOagg5qQqVIs+wUdrQ5Et0Ofb9Ew==", "signatures": [{"sig": "MEQCIFsJn/GILB/N1X0iDK5Y6s0BzUFiE3AEhtTTFy2JNRiuAiA+kQzbPsRdb+T4cBxWm6aTv/zzuFgL4ZbPK/oEr5kpOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtYCRA9TVsSAnZWagAAEfIP/RaZ/zRW7Vw7gW/nL7KE\nilQF0qF6rr/q6pH0WwgeKyUf1/kBAXM1riDOuYHt0kaJltTB5jpwtUpC5WwD\nTJs0eFFrvgts+MdezodJ869NAKpXYF8aoXhLSaR25CAFg/LaXyP4EBeCA3JP\nNYQq4sDpDKjo0dmKy0gyAwQ/cvsFgvzPwrLPt2bkudX37567HAew4lMgOM0p\nfnCyELQnYL/8LcW1zQLZF5bsdBdTolgQ+biFRGPtt6z4o2VR/Ij+zDp2kIg+\npZFM+ShnPUx2iA6L3OS7AfGIla4GDl6zNNFTg0mtq4fI1Qb465QrUAmE8f40\nSXYc8EABfJEZ2mR+O1Ms+RYpWUl4dchG99j6zbdX9DD3wdm3cYp177846APM\n4DzqTaVxNvaSsp4S333kMGN3n5B6VZN55i7t8DzRtfdC3L5X2QIwbUrEgTgt\nLStNNN9AHwfd3+SW73iJAii+iYATu86AxBWo0Ba35lOfrR0XcJRd/2IhG+NT\nnGTmMcXAHAwWZ/cu51fnAqKxd1d/uLUtL3/sE0hZFOayx/989BxsIszuYYo2\n4/Dt0SslUXVpOBdEBSaqpp6M1OJLgod4Ieeo+Q8yjfMhNVutHHveJAP0yMjB\nSCMXH31nTlnUo0qEEKgSLKrRhoBpwt1bPRqBbq4WxJl0g6Wmk3PU6mMwk8hK\nKSyo\r\n=KUzi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-popper", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/popper": "0.0.3", "@radix-ui/react-arrow": "0.0.4", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5a4a694476bf4d0c28f2b043f2caedf14474028c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-coeu2EK/jUyVIpIYvBGI86f6+N2ZO+fb7SWhQoBkH59AX99sJQvTFoGx+Xu6bt0J8ZomVEhycn6QGQPYk+JPYQ==", "signatures": [{"sig": "MEUCIQDH4WNMzzY3KoQ6G1iQTNLuDhIeI5cAsipaCWLgBc4E8AIgc//FIQlwIWugTCf7H3l9TPzvI1qThEMXbvB0TWVEBhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAICRA9TVsSAnZWagAAnV4QAJrDWHQijEFEYcPQZakC\nUXFMkyAKku5wVDXcKPkmTqDJXCV9KDCdTz4+7NF5F044L+ANg/IVK8wvpiyr\nyE/VaqcRhYO2+nJRfbA7eMrBqQcjQGAlENudUhgKopg+a+GF86iee3EM3tlv\nJbV7eB1iSrj8cbpC9VSFEXmu06LZpiQk4ddPE/StxZ1SxRZn8DHbwIkDTQ8w\n2N4y2tbZ5t2l13UdYTfX10jzNvvb+6qNPSkn4bKNywQ7ShUtgk0b2kahygjM\napwNAnLtuAoV88ZGT5tykihxwiy2nJ2WCHnGpW+FUuukQXAIHzGlPkuczPt2\ncdfqS1AT0R04uQHZ7HKPWOH2k85wPvIlkSoBbeVsCIJhm/bWGQpggxmFmb85\niwYdHfk71h/zJNu4uUeS2Uj0J63YCwhTisWIWDElvdl1QvGNngH9Ue3cH6HB\nwmlxL6X33Flv68UMSSHVw+apC8BCqeY3Etxq529t8cgPhivTuReRwsgUTB7N\nepWA16v5r5a8RBlD16fdASWBB28au+mZJhRU2mvmCxN+ImLszQOyWwa8BgiN\nIvKqBuCxb/NRpKLwcw3JOFhwhThRq4R9RBDNSNYjmOqX4vLNOIOfdNKRuWwX\njDRGr4BYAYcKMcNKQqVZIYSGS9/WDHDM5IO0MF48985AD/eQhJUoHaaLv/Ed\n5KSG\r\n=DFP6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-popper", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/popper": "0.0.4", "@radix-ui/react-arrow": "0.0.4", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eaef566541d2d77fe4410abf789ddb390073e840", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-pwUdqFPdPd6KQbwBJWLeKIn/GeQcHXofQtE2YZSUxWprKeJx8yPQzDZFVu7VkK7ox7SZXVUokb6NkNdzoofI+A==", "signatures": [{"sig": "MEQCIDF+lscMeikgnNVzHzBoh5mmfGFur5u/TlGHyML/Al4+AiBkjs0gX+5MmgrBWydlDI+bYdcBUYRoXOEF4HTU0EN/2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGCNWCRA9TVsSAnZWagAAZvYP/2mLTq5uqvT0Dss83cxe\nkqlXkhdhkoWEBIHNbON5YhH1r4df/vfwhiRz8mLMn6gF0AwVvlvc3YT3JOaj\njfOnGi3DHlCYvB97LGYx6AcVzbJb1wl4rd4netVAE5g5poI8u0dP6kYA7FGX\nUE4vhPyWNTsTkrzwQTn9Y5I7ZZi0UAaNrQJIU7Cc220rfjlucnVRcptkV/xC\nbEOVC2PD8egqg361vXLjfEVr0qVunH8jWsbuld1NUHdN+9Vj87uJ4sCFCtzV\nT/zr9iemZGaPs/Dx9XzFI2e8LMs0O2t+gH3GBAcOgqKD7gcaqdn/qqe+nlEZ\nwpudsZA4NTeKyU5de4BZ2q2VoZ4Wqvd7a+lGzTqv7t+rZzpLJUDjwZO3Wubr\nzDxhzCTuXDOwogwJv0Dj3tQTCJU5JMNq8KEqgOEZJJjx2Q4ECJ30yVtrGJRC\ng5ZkpBSmCVGAIbZE6dccr313x8WdsySxLw3JoyqDoT00TUUPHeGjhAdVvWZN\nkD9YRtvyhO/y/U302E6S+FM3AKt26qz6dPIEofoX9Ff0o1byeunMmF0PnyQw\nHWXwlW13Rn4VoSm6CEkaJfNAzVE6DXZVAENaZqcTIsW/RMx1e6ze5Dqf7F8+\nTtOFRiuPFEeqvtwZxPpTnTWVe5iQEseNOzuBi0lvyrISCaavDHKGys7judOg\n+t9f\r\n=DQFY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-popper", "version": "0.0.6", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/popper": "0.0.4", "@radix-ui/react-arrow": "0.0.5", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e6dcc3f35b07c342b38252a5370a110da4c0ec06", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-ivclu+yOM4Qwczdp5iCHUfpgEZYu5gdnCv1K11BY86wRMoH+LEGZWs03wrowVfRxpweFUBMekdrx+nx/5f6gdg==", "signatures": [{"sig": "MEYCIQDSz43HR2HZzNCJqOBscR5HcRXQvmE9udrmKeRZv2GcFwIhAJxS20Yp6HPUC/2TjowwcJ1nREspbBzx7bzaHa/7wbsC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V2CRA9TVsSAnZWagAA0hQP/1wzDyGzuec1eOtL2P+m\n8IBaC+eJ0eG3t2if6lUK882KCLACj0b1AmE5aEH9fv+WIQz3ojShr0iM9rmM\nRU761ia2PfWyh1Pi4A6X80eD+cWzsBvWgA3rD1Rd873xvPLNiwgqTvljRG8j\nXH7v9+canSWrCHZY/nkVWr/krocolnDnvQJa3HZYXzvRTcklKJfg9hZVEbAt\nhw+ACDlsozOFmXWZnIWvfRLuOG1frX9PWAX1K8nKPSQrlsMIwKWhUeui1vCb\nLjAeb3UTBGdmeiQR+UzAhkfw5wZXbL1GLi4QR+7XjlLFtFTXwEZ3FQFWfzBm\namS8WZNcNFwwV3Bzkvqg2DDRi6bbi1pdrcnT19cfVZyyiCCsKT36xbypTW/U\nTxY5Gjx1SbedFpO/d2TsY9e8eRWrvg5g7RCIts9X0kiCgn/1Tx3d3kXNv1w2\nCy0jcZ5jZykrq96cGF8n2qKtyssA7Bq/q0n9rtFQ+4rTb5sS7Hev27JgesPS\nEpRbVOfvGgj0Le0CFxeilza7QBwvYUANOVPqYjp29Xd0/ozCYa1MHobME/tI\noU9GTgugDN52ugBFiYeUgVox2LknqqI1/rHul3uGsjan4k1t+zc6NUBizM57\naiYs0IgHO3MUseoyg2AG2FsfQzGUvtCgdfJS/UCyK8pbQzmTMqabqv5mZjXH\neNvL\r\n=IiDq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-popper", "version": "0.0.7", "dependencies": {"@radix-ui/rect": "0.0.1", "@radix-ui/popper": "0.0.5", "@radix-ui/react-arrow": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-use-rect": "0.0.1", "@radix-ui/react-use-size": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cb199f1d1de89aa76a5eba9f8341a2f47506dacc", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-sl8Pdnwgdxb4ibcqqv8l0vTX4veUqsBx7pdjfZox98BHRBV7J+VFHtkzW8yu+kuZhEsHxfwoccAKdX9v7T+6rQ==", "signatures": [{"sig": "MEYCIQDiGzyJI1IiQnGoG16gTFg1gH+Rwzpe84zziMFiNQhikAIhAN8rbaOkq4Mrrw9i2owT2dTexaBSCuydlheVIlKBW0AB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VPCRA9TVsSAnZWagAADD0P/izeR5VhQ8Wxv3fc1Bqt\nhf+jnvYzv8IQWsSVNmUkRfPxLhp11DW29ODSTG1o7wdO8eTB21uDobx5C63a\nluOhS5O2dEloA+/blQ2rq3dGJ+UJdUazYsE41lUSEBxE+8D2L5b7WfDwQLEc\nk79wsEw427mjo2TvCFM+ahSxAlz9XlxpmWQvqFJuZ4oxr2gJPqnVi7ECASeX\np8+wOTY+TdLZcj/dzhF8/aqMIMRQ9nlxe66wGNKg1ZF9jzEAUo1uXu7kJgKl\nk8eFaHRhRX1ik9L197cqXRAa/1+EoNdM0EXZsgZpZ4UbhEEhp1w8k3gCX3FC\nYo4kZbccChLuiy8B9mUI506k1MWB2hKe7hF72uyKTlRyZAjTuatmIDrF3mFo\n62F33b/Z8Y5mqBeZIfROqoUlhsKX9p7RRfsP+wkQ0CV8IsjkhGMSLSg62OYG\nshAuqCekGycD6cZHVWk/pW18Adi/R4QlFriw2P8jRD9mNtTN/y2TDjgM1/Zo\nrIJn0Ov7XhNnAJTC4qah2YHrNSsK7m+10W5KDX5TV4QfPumzA+OvAEvm+tGI\nIDXVaEyYzp747Mk5BBSi0XZejeRwA4b/+RrBqLYaVUTCw9l0PMKYg+nAHMte\nbs1d6mSiqjqU4SRI/obiFoGn3jljNSrWwgZyn+42tSbOVjE5Ik8naMWeT8G+\nYRYw\r\n=DGXx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-popper", "version": "0.0.8", "dependencies": {"@radix-ui/rect": "0.0.1", "@radix-ui/popper": "0.0.5", "@radix-ui/react-arrow": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-use-rect": "0.0.1", "@radix-ui/react-use-size": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8e3912de8b2473f032822b26850f3e5ffffe94f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-LAryuSo8yRVKV4fXKGauoafnam7RRZxJP7iNwEA2U2T1m84zav6ccXZSQPscrcvYCxTsJIsXns6XOeY34PG51g==", "signatures": [{"sig": "MEYCIQCxtDvblKsvL5weLzB+ih3Q/4L6I3zdGBGwDKihx3x3nwIhAITEZiGkkdrcxOpZk5taZ+GNZ28lguys/IBi7/7L78Pc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31109, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmV5CRA9TVsSAnZWagAA61YP/0yGajfCPq0OFnpkEt0H\nGvNXnjuxg4aNPebSt2HBmegbPKSuZ0fg8osR715qtvVBpnoUV6BU0WH8zmFr\nZoQ040+QSvTtBJ8oerOOKIPZZ/zEGYcomP7iwMp4omW0Q0blmrl0Nmjd0QmI\n6UPToy8T9DKip1aBDlGMGAmQqY0ab/Mwiq/B4dK/QqItQ3YXhK2G34+UUD7d\n4x4geXYNesMxt7tteP9DpNNo6kznA9uLnid+77uguGG6xJSmrTsaiY5wL1O1\nnNWWYNIj4ZLog9MnvTUX/7MDR4mMcKXj5nWhGT1vgPgyo3dMm8Qd5/pwwtsj\nbGsJCzc7T5K8NiNhlSSYip12nxGKRUgQvMMY7rDwn4rOlFzR1eNr5kCyOcq/\n6Etv20Hjbbfgk4ZLd9UrQ0jiU7ejpV78lvUEoqK2UmrUMbNCzsVfomBQtlpX\nmBwRE16Xj2VyTNFctfJZ4pzlsFRSX9DPggncH2sqbpX5uJUkk7RWl5S3l9Uh\nwNicYR0gScT5afIz+VDbDwG1nrVEDjtFBZ5ld96N+efs09SJHaYeSAkwIxag\nuZxTkysJqjPCiY+hZFyBJnNVk0cZQpjIRWcbTWrgRIPTQzsYky2Gswz26PqD\nRhX0/LsJ34KD5CyjVpgQ6ceGZR2ZotgwIfaGVUGRZKygEoOrU1WKYHHO3tLB\nuqLV\r\n=NC/H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-popper", "version": "0.0.9", "dependencies": {"@radix-ui/rect": "0.0.1", "@radix-ui/popper": "0.0.6", "@radix-ui/react-arrow": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-use-rect": "0.0.1", "@radix-ui/react-use-size": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bac5a5b64b6a9618346f707164b843945dc04629", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-v9SBs1CkaW9tmbXLz65gLEmFLs1tSSsLOAmnrc3TZ1AgWFkKgbPlsuQxdAIfalcqujKyBkHUm1o8XYoJIxIilA==", "signatures": [{"sig": "MEQCIAeJ1RyrDUwRqImKa+uOCkLc4wbue56iuT+DRv6QdcalAiAaVde15JyUZwsTxbcx/08pBA5A5ZDzrPiXbTHWtQdAxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9pCRA9TVsSAnZWagAAuqkP/2c29CfhU1l1DWWy8MgY\ntDByDC/izBF9iaFhRuVyY26BitgNBM46b6YGL5hXTuBFk3FnGxD3ObHPoM7k\n3xm3LmRnjHxcKtu0Nw10lTlKx13C4FgPmFcSnIoh9ur6X6EYjc5fZ9AmbSTr\ngwUZaraFlUyV8J45p37Q70p7+E11wirjfvZRFPKos2lGEf1QmmnuXGZVN3dJ\nAm0CoKc6M1kQeYBI+fHB7TzOKxkcjhfnjhmUCWqMknnMcRtqYpjUaAvUD8Dn\nFwx4kakvj2eBTtNZTmjOOWTLeTO1IecWCYm67d4fTRL7ggruZvvDNrXJOn33\nCHrgkExJHms2sHdfixKJGbuVa8DDuF0qR7iQixP/dD21xL/h9ypm3jFMvnmM\nOnLVljFnorosRHDoPlyOTARdhZG4ChK0hus36ZEJiYGn3//SI6Ny85/99I7k\nzR4xnv5joNmeuE2upb1pLBdXwrn0UZog97tsfaL7Efala/USexG5yzgxI1YP\ntqzX8KnTdGEKZ+MNazWZhSAivrDvO3DeVSuUeZFY/QSCq1/lHXsniVaAYkqw\nQU6WF8a4rv9NqFrCbc5FwIn8Q8RfLypEctPBN1bwhDabRiTLhJLqodXXBxvz\nNT+3cYaJMmD//BuYLl0fNeOrWJVO4TUxfsR5dTAXuwnXILQM2Fmx6WFd/ztb\ngaqy\r\n=udj+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-popper", "version": "0.0.10", "dependencies": {"@radix-ui/rect": "0.0.2", "@radix-ui/popper": "0.0.7", "@radix-ui/react-arrow": "0.0.7", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-rect": "0.0.2", "@radix-ui/react-use-size": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4f79879b8e4425831775b20495faa353785a8b1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-lCWLEDYadfj20wsa25H1dFiHVZUIsn20vo2pQk9C1MNM6vu6q6QwSRSPUffBKDSCVZVn49O7Zr7m+I1k//1/8w==", "signatures": [{"sig": "MEQCIEMlJ0X8gYKELUSS8J134ki/ipqjvLtCVx1kXBwWS9UoAiAufhoXvJQFFbBXesSRgAWRY/DPAkThQi6N/R7InVxASg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmO6CRA9TVsSAnZWagAA58MP/RpcP1F2hX4pOBJwGCsI\nQUmspyHEY6thH9HSkl7gotFBatgLZ8qZT39beTa9Uj3/IRAw7qFxdgXSxmcz\nTUtt/s3Ihg9FUBwzeTK/bz6zr6+iz2Xk9Y93YwKuoQ6cI60cZDA7lw1nxusX\nrNAoTpVpbHFlNU6pjknx2DKCEbH15+C7Ao0L20GlHG5fKOriV1r+iG0f2j/8\nNViz1lO6O/5uc4RCK+Q7rVUxLbetoQlyXyXXVQJh67nT4RHS61gVYXyXLrk4\nPTfAZS4X/Rnkbw5CFnfEEscVg/BwqCAZ1fNvVvPBSbRR3TagATdIkDyKab6+\n4AOHHowh7HTaBRDr6Go8UR8DoQoAWJPwUONOoMNDTsIplkgRPIErTdJ75j5L\nRd6XeFcYiM4qJIb0tM/ztdsTrhDp7B+x2lpjfltEHdV1PAdpHqotm1irFwuX\n0Bifn+y9wBkntUtA9njSCm7iAu59UEUt5GQf8c+i5/vgZnKlA0teYGgWp7GI\nI0mfXrKZ/Kx13tJEdTr4i/NZhQJk+tSMK5H6rsCQjqUI8tyFuT/0VX3M7RF5\nsAeJJBlr3JmsoKSnU751YNqQ4Eg3DPCS9fHsnNS87pFmQ/qVhxGDs7PQ38rm\noY6ZPoHjfZmz6c0JGuv2wDh9Mw3xltfNAdttxtcfoS3eFh3J9Kvvg52ZO2hR\n2eST\r\n=f4UO\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.11": {"name": "@radix-ui/react-popper", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.2", "@radix-ui/popper": "0.0.8", "@radix-ui/react-arrow": "0.0.8", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-rect": "0.0.3", "@radix-ui/react-use-size": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "18b0a449a182fd990a9591493ed2090ad29935e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-NawWPe6WlDEsedsU5/Um8HSDeWvl//xuBQD0K9ObbpUU1X+XTiMy15ifYH2CNPtk8pYGlrSc3P92AA6U7H8mfw==", "signatures": [{"sig": "MEUCICR6hfqAwtq4tTNybtIHPQiwdoMiQMrmqFvcjYAvBfkyAiEAzoHwpqNrMmdocfvHxr/qQH/5YUpeI4SP/Win+Hv8CYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gsCRA9TVsSAnZWagAAJngP/RMsPty1nBt+wIdywdd1\nmT1693KUnRhgxYA8Hmm154U282u/erVjtS/l6Wo5VuyyQkkAFWBVrnVbkAma\nnmF1VQ6WcQ5Re8fxbOets+DUxMrpEECIzcL3P7zxCsV+jzxyDVXV/wOo4+FE\ntigyMi2VOe6ldGNlFdGBWVCSwDcP0hqAZmNquzaaX7LKM4blL4/+H0gtNtUu\nAtvdTqsibEbBsIqRUDg9WQoRYfYN9RFc4qvvKw3G9lNBCvKVmwDoWBgm/mHO\ntf0BE2Qp7LUCHBLDZIBAipfj/VxXM1MNFDsLSgqHNTVjlizbAsguupO5kuwU\nWIttZ9DJE7ByDEJF1kdfh8t1Go3FKfk70fpM/9lc5gulhe8MMUh/dIVddE6z\netikqxE/jyhr0295SV8IL+5S6qXtlGcn6yt+qmCvDZcN2ZjdHY5sgegWyw1h\nn2rlH921osXFRJU/g/sxOQ5NSpkoCj+E5FxTbLnX+WXbVa2/T7tHKEZg3cvv\nUMW/3PIgS6pj3tz4VoCQ1eO/1yHrJ2C2CmIK8uhFFZZ1PeBgQExDdZYn6WNk\nOWpdR1rbw+vBEqMYGLD1eMROdcLNY9xsY7bHZHdXQmtVYsCjQzRc4VOPuDBp\nCwVtPJN5NhQZb1Ey1Njdbmghr4NaYh0UPFrlivekXxH5+YFAikPaDaAORXkK\npfgl\r\n=tpkf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-popper", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.3", "@radix-ui/popper": "0.0.8", "@radix-ui/react-arrow": "0.0.9", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-use-rect": "0.0.4", "@radix-ui/react-use-size": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d96a7c743ae65d7c1f63695c2e544f5d43c6aa4", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-vFtTA0dPxdXx1YJDgcHAn1uSXY55FuxvAl4qd+Zk5aBsA/zFWLcNF6ph77DQxyfs0uY3Ku3QyIvD1hH63/dC4Q==", "signatures": [{"sig": "MEUCIQCmfNkdgmDhhjyCxZ1pPDRhBYRWNoGRAQyJZSQgN4f3FgIgNlqMXOR2cduhhve3GpTYjfilED+UW+FHrljGX8PRg8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H0CRA9TVsSAnZWagAA8UIP/0XecHKTJthbvZ3KLdUN\nT9HcMMMrpn2IOqx9kqaJsFpuHs5cHJcIAcuVNv4wvOLxdAePLiHLggxH/IW2\n7rxwqx5miDDU5D4eryuge/DNA9Sx6tWMqlfxZp3KBTL7xDGeI1duwV0ou1U/\nEXGGvl00emxojSVnlDys0Mg19uqi+kYu3x+v92MQQVg8LhKx/TEl7Codkmbx\nnAagBbFbfuJhPjlNxzWixUAekkt/q2wmWqzjL5b0gMLZdY74OXLHbA8gZk68\nPLEWHyL9Py6ukU8KLLOAswz8AftuxdLEJuMun7TR33PZKy4Mq9wcyt0cJi6P\n6hwPI+qHeNkfBAscQR3s0qUvdRHpBG4eeQ25qiOIpd2dk5j6r0Pqk/VyuRy+\nQ9pS5KHxAXhAvKDUieKAKCuj77xKZVHl9a3whFs+EoOZMMiQSZZk/32fySOp\n3E1xRVr4f3EhFwoZQMe57bXTSCHDJYdqjKkwqyMLKxm54kMLjDc9LUOhIBIn\nRE0v6i7JtaDWyRyULI+ZrdIeK9VwIYOE+uEgEzPNV6x4u4IBTmjWeZVh4701\n0CdjB/BesolEvG71nUgAthHhmC3jNHUGIvaevlsFFEz0XlKr4SFPQgHpAmey\n3Cnd7J7wrK8lYVyLj7eL6kQT257hNYNjONIgeJ0sHqj3FTf01Uh4HKmm4QAN\nmAep\r\n=V9bS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-popper", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.4", "@radix-ui/popper": "0.0.9", "@radix-ui/react-arrow": "0.0.10", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-use-rect": "0.0.5", "@radix-ui/react-use-size": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e9ed9040c351d8c9475330f1bd119ee8b84cab33", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-3EZ1DTE5j+p8rDtVpXu26ZC8NR11pgWc7vY35MgO/3H++kgt3y+BmRapLmh7rVeHL0NlH7WVdEOZMGL0c6/5xQ==", "signatures": [{"sig": "MEUCIQCVfCngxVQ3WFRZo0km+1ImRFkCcbrevwrAlaoJ8OZpFgIgetpAH2LJsS2PwNwU/EjXALp+0WU3R4VJrUFDLU4/ThI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vzCRA9TVsSAnZWagAAu4IP/i00bcNIomITjROIO7zH\n+9DeIcCPiKqrLH0Y29oVysuOH1DrMKmPm+BXocSaTu/S7ihcepP6OPyMeVoO\nW9B5fNiFVCJs0Q8i2PLRr7duISjWyLOkLb/IhDfLVL6vBXqDEOpo9eNoTC7B\nYKsfaDr+dhSikSPNlJMXg4dDo44T8rjm+SFGGovTRPKsd4tNFvth8kYBRlm4\nVNIlhpK8SIojkltc8YgtMDqjxyE7pCJwmhCAnVBhm/ebzrNpT+sOu9SELqdB\nLjdhRJkm0ga6zx9ylDL7kPMCuheoVjC5kRrYhNzyogf+xIzU8QwLJ4WGbUFl\nOPqSDrZogvbBIbr9bC9RatdOU/iSqs+hJIxbMfxOwsJAZC+e9XdoPuZIL0eU\ngJ3xwOz8uT0pesqk91bNJCw82gxR3XAzixX0sdQGN8XC0+/+kk43Fq838OaJ\nSimmywG9kJOKqAP674s1IWRcZ/90wrdDPL3IYXPXFvLeI+xqPgT+He7//xT7\nifxGnG5BfpweYOuzg7CrRyKpL5ElWrpvFOe6DRPEsl6mDNqgbgkhttj/cuB5\nNwLnWT5iq5i42HsbVjN+wBrrnRTVTBtBmZHpM/GRV64mp392C2FKmFnty5Pm\nTgHA3l5u4whfZynnw95rbGiFkpAHiosfFR9YOYA00FX0pY0JVBiJWuEXyesO\n2ASk\r\n=rELZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-popper", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/popper": "0.0.10", "@radix-ui/react-arrow": "0.0.11", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-rect": "0.0.6", "@radix-ui/react-use-size": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9ac6eae69a7224ebf3a4cf1191758b35dd52cbcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-1U5hRbUFZiqlT6vEgFugrL27FqstlH6gwaWq82esOA8lDQUwNTnr4fu+7fY5ZVhJa7vdzwe9+p7RWSad/uCZ3w==", "signatures": [{"sig": "MEUCIQCox3gh/EOPl6MHrL/qzMY5jx/kuw4tHuvCBVFx2VyrEAIgVLcgXXqYP8jKCaW9p6W6/Zzs3UHtISSbSg5TC4UF960=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmpCRA9TVsSAnZWagAAp90P/32rUdPEJH1dpzdNYHiq\nXYN212D7CJMxtzV3S26mV9jAIzgqsp+lu8wPH6Cq1oO7ctKvM47DvWaktciR\nBphbh7i2Zx4P/k6XrNtZMfTciUSk2MyuG6jsVKo2GhP2LXmhRyH8qIq0a9r/\nNd32iXz3GYmFsnho+INiuJ1epMsEt7mblNDabwk5WOJzBx9vDVYgkldBxacY\nxVZIC11r3/JJKgcAwz2Ak/TjCAYhHzlkkih2c9TiMd6vCD2LoYahbQ3Xv+Ol\nyxo1a+JACVc5DMbYC6Hxf2wC3fr7aqi5kVAoCu82G0NYwrJNv2p/HNpUh7WD\nurCLfeN7TrLfpB6OmtzQce/FieS3RZUuX7+AvgAAnOX9n+TLiUKtafuSGl8H\n0l6DtwnnSeeh3HWU6a4d5WuzMyWICXrn1vY3MduFfbH38/uGeyAyMCkhpTU4\nV5rJtYZNLednhiMmXIox/4a4pBFg/lhjKGN/LqtcVCQvcPuVRQuOdA4LAvTp\nT3dBeKV9wsLDeyd7xYxnxpWItx8dTXN3xWiH5optEvoFRCK84yfTw3l5QY+C\nnz2q9JHYnREUD7f2zUAeOzxpl22iU7iazxTKR74oMzz50b6UcMn6CaQ0N87n\nPBRsuypdc69T/9+7Wo5bPb6SzDessI8Cv3jvRphrjuJ5dytaHHc4bpkko+Dw\nEhfY\r\n=t7Ih\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-popper", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/popper": "0.0.10", "@radix-ui/react-arrow": "0.0.12", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-rect": "0.0.7", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4fd7d7ce1d9e1a8e0e46100a5301ddf3959f5b74", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-g/srfUswREW7nm6UG90McNQt/moI2+7F2EboM5wPgEHf9NN+suToeE3iixD+kYOJDp5Ax1EAUkAWlNv/q6FtgQ==", "signatures": [{"sig": "MEYCIQDzHwEH+xOPQtKtnaF8FxmYY+QNosgn3cmAasaiWKJG+AIhAOhOWUkLS2GVZDqaA6kV83QknlRjZ6QjeI+u0gfImV9/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7TCRA9TVsSAnZWagAAiwEP/RbEjnBoBa1Y1l5+YIHL\ns+KDUp2Nm6jtWSGjIFSBnkBiKi9IlCbMt27OHhro/lcR7Vurfls++WOvkMqw\nt9bY17a9EGLzOdoREVX6XYdmO1rvzHpaQgIcFBsAoBXSUY+Dfzr2L8pbd+Zl\n50t/RpgjEro41/mr0qwNU0e9KNRblOLIXIBE9JR023VUykChC+hCX2URJwk3\nxcQFw0AD1KEjjNcd9vEr6Hi43zGGMPjAmO1PyXCvKijfvPNH8NgXeWLcU30b\nY9K8TxkgZg9yuNz62KCu1cucsKb+O4qhIZNGh7LD+Q0PMc5I1Ha3ntzOk8VH\n20HJEq2/S5sltkN6Ue2Dlta27cBMrj0gkDZyTWe70cQGxBK2dCkF9wv5NXjq\nLnErqRPEybKn1HCHO8Iz1uppAK/Y/5YhMojxu4ddgh2fLFcM5aQCwt9j0GuM\nzQbxMHV0hm/ZPlQQe3uS3k9nsCUHUuuxPu2RUtOrm4N0/EEtdNHlZH1WUxwX\nMmBrQe7CaF4jl3j/rA9X/juY35tvLXEP8e+JqBXbU50Fj0/LkeLJ9vxdaN22\nxjHxWQCwoUXDcKyGwn2P1Am8oSSXW3b+E8WN0cXfFLCeU1dNRTRH2N1237Oy\nZYo8ZZsZ60JLC6O4ox75itJIHaLqNDw4SKdGSD7KLhl1kOcaeB79Gx4P9EQj\nxiLb\r\n=pzNB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-popper", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/popper": "0.0.10", "@radix-ui/react-arrow": "0.0.13", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-rect": "0.0.7", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "62cccb7d920dc89e076bbdc3421db8c84078f428", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-njciQ/eIKaDF9h+27Pwi1H6NliQbzTgaHOsT0w/Lxx4vfMe8zcHtiEigYVGErNR4zAYlbW72KzLjtngtNnaorg==", "signatures": [{"sig": "MEYCIQDU2xBV8lJo6F2anmGgbyXMiBaMhenMwA62g9sDE62YjQIhAN2Ni5nEcOgOtZGYkKbsx/DQqWKZWdiuDSucVMZdd5Mf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlX/CRA9TVsSAnZWagAAaxkP/ivgmDcu4IWoMaBJvX9I\n1ClXC1IE377vo95d7IgkSWHeuw7eZdcYCnGWpKn2tgrkFWpbIR8NC/PH7xek\n8CQns0nLfchT0ZwUpOXjNB7pADimP/tk7mO3slvTKbtODBzD2Bex77lftikF\n0oV8dRMksEtIrMCM1+vNQZEdIV0Whtzn+fOYM0H2nTs2x9Lk9rEGo73BUyfw\nsvsM9P6YU3exycxOrc1AZVk25jN9maok/IDd0yMSX2Aw8f8WlorvPsuS6Npr\neQu2a6UXdzXf4T4dJ3wpMBjwUHIc53c/hfC9k4U/2YOH4p3UHkFZgcmvLsU5\nTcFR3h6T/SN1aKBweFMSu2jw79aicWlO3fhSyBBomVUpx6hPEFGQLmA30Kmh\n5b9+iEDpPMw6WPxAWk7ad1kqT2QT/can3aA9Tefjtnm9R/+nuAPF4wFwpQbS\nAK6C26C5eYSWrJlhrmPRKMTHN8flWC7PQT27f0lyOKy2cOObCnJNSF74ryCg\nl5LarP/qnnUCt7MAEvGl9rzNwzvMuOoW2G9BgVZSbWOCR2M8Gnqr0Nmf8ez2\nRSb+RsCBO4Xp4z2d0updGl0a+j2O/avsvM1HK3pc9Sky7D+7TodsZMfnsTqT\nyeJ8spRrvv4TRdct8MeAYT5kC0z9tAGHXWR4NN2/bHGvCKz7if3kIiHDHexh\nY8R9\r\n=0xKn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-popper", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/popper": "0.0.10", "@radix-ui/react-arrow": "0.0.14", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-rect": "0.0.7", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a73486e19a628cb3fecaf3fb6eecf6e2cab9d0be", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.17.tgz", "fileCount": 8, "integrity": "sha512-2Lk5AjlCcN9B6fQwQ+y1Zb93f1LfyCK6u0oOAUsPtwbnYEHCdC6wuCFBOZ7AonpjHbrHbY6QDrLGcC43TY6ihw==", "signatures": [{"sig": "MEUCIEDh4rshVmaRqdNSPP+pYLmdOtmy8aaQ2qRo52iie9+BAiEAz5tKnlnvMyL54XiNUsufZJzsamSLqzOJTiVhvj+CVnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9tCRA9TVsSAnZWagAAFPUP/iLrvXfta/qpHuM2Nppa\nuZ2Wzkwm9AmEr6KhkznzvcxhxNKZG/vYZhKPN+uHh7gn7NxMYVwIaliX2lAg\nR0xm1x3pl+XPM6GLwbZtxtHqwAnFbIzyPv08fTxYByGHQXtgDVrxZ028BIVA\nF0xa6b9OAFgL1Fah13f40URTTGYdL5DY7n3LmjgT9H/zlp/EmzQPavdXao23\nQFn63/qSaSBIETDZbLGZitEfOcI2xCw+G2vq+ZTjFXcA9VOe3dicL7GOa0GR\n21n7CRdFIgR0y/E+ks2Bh1sk8T/WanN+DH/ZPUsKHRKOrc3FTW/qonGsHVba\nbSXaGo06s9dB2kq5smGXOZ1A6FW0ve60KbMP46uHT7wLzrfU/Y+mQR4xPWPN\nMb/hNfxf2wlwj0WmH1u1O31zd8kIzqaLqRnuCmoE7QS2QF/kf2QL5CS1I4l3\ngso0XFH4ajhTAHkMFkmldkAS1kp4KTk1gjWkLWMeY5Pd8SCWl01wvdLSRSVy\nnVqY8nwZfSb9X1jBwPE990wQ30CKOHky2W87etL6zXTONcZ8oXeA0vfMR5Jf\n1UUkDvbhD54Hmrvsje2milqPrOE3zPKy7bFDHwbm39lM4hETWdbsIo8cFXok\nyWAUvtKDPy3VPOEL2xfk46KGeCm7C9c2pIKUVxZMAs/6mxhV/6WojCVrCSCi\nDomf\r\n=EZvf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18": {"name": "@radix-ui/react-popper", "version": "0.0.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/popper": "0.0.10", "@radix-ui/react-arrow": "0.0.15", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-rect": "0.0.7", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e85ec077c18ffca92ce97cc19586dcc6f022fffb", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.18.tgz", "fileCount": 8, "integrity": "sha512-j8nPqX5scAmeGuyW9VQv+M4MkKsV/ulR1Yt0eu13LyGLT3L7FM2YBMt3KlbpUxrT3mrNGC0eEQAiVgm/G3/fGQ==", "signatures": [{"sig": "MEUCIEEFCgK/dO7MVKh+gSzjb+1UNyJq8RATUez7b9Ul1zSfAiEA3H/daj7MTtElJzng9vqVD4THGXFsJpacjbR2ndxHiyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTuCRA9TVsSAnZWagAA3XYP/j9HokGYeo8oO9UAYQWl\njy46UKo/iPX9SArL/kKLtLsWW10Iu5e7ww3hTj/CvyjtTj1rPJvU7OIm5ntM\na7U5uhaOqG4oh6bk29JZjqqcn3vVM1V66+Z5UtvGeS4iaJosRYzw8lbcyoLB\nDiPdRnBRJVSPGN0AxFWcYOrsYIZ7+q19cu8bsxy5zpq/fkYC3VW+XxnSvsSf\n8bRKkV6Nt9VkEerFvITQFClHBYXg5+Ff+OyQW9Puk7u1PsAtaVNT0emeAUpo\naHK/y7WYDqvLnJUudzaNjaTiSldCDuLaiAi/Qt9D3K7OWYG4+emiPa8JJfRB\nJnsluwMU9ImNtOZm2TewRCFTBH9r2LEmANJ9GWJphfPSSITU1IsNDAGaSfyn\njgoit8Cllll1raSPnRBlvCBd1axas6s/odWRDGaq05Wn9JqIIXCHjZBO/TMZ\nRe1qbVJOgs0RBFYHoE5KFJ0ntX8UdTP3K4LvHSoEc8wWTW43PREoFXSJRDgJ\nwmSIgkJSdgOCaIHvBxcXHog+7fnVhyCHx6dit9vixGiL3ET4C0xXaFjSzzrk\ng80v5mo+lkQadjC2BXAoxApJB/U0bhSQLUnf/8xIIEBULDtquw5GIfV65ks6\n+qQtdnz8rtE/bs2DP+ZHN6Ncc12FpTgPPanXcwscnS/KuV1CJRlw8djxzXc4\nx6mR\r\n=X3gD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0-rc.1", "@radix-ui/popper": "0.1.0-rc.1", "@radix-ui/react-arrow": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-use-rect": "0.1.0-rc.1", "@radix-ui/react-use-size": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8c4a144b440776c99e468df3b3fb74faa8068671", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-Il5UdOn2EjdFqtSDCGqQ1Uch73Q8g4bFoqOwMjFT3A61bkE9apBMkvqATsu1eiyMWqRJPwDXY7LG+Rq80eWgHQ==", "signatures": [{"sig": "MEQCIA8r+bxSisDhmUTa90mkJfOX5NR6amirKHepNP5AAZutAiBo5MbsGX+OUl0SPYUOt2rqzPvR+oQoJcN08BRq5PXulQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgphCRA9TVsSAnZWagAAc6sP/3DUDkAr5lrIGXEcLTne\n4rbc2jkmuow7p8h4zvTyUXMuaT+N7x6SyasE9TUHD4sLx7BaSwwgSfngvT3U\nba5Db/OEMVnj9uwV38dg1Qp31lGWHRmV5c5Sy7z80zM+1BnFtMHXyu5pK57J\ne1XMahYaMNaiV7yHFml9iWnanrYHXUVg6Q1D7OwxVM1Lm3uSQUAASFZHv8Kl\nAq7tOw4fN6ZeFH4zwmtbD9SDOvq4ieVJodOON495KhNEcp8KzxRFThItRv53\nE9ynndPIvFoP5fhP16EGOO7PY/XP2dyOc39uzd0bZB20WoSyuSeKxQjIdmQy\nVpbUv0nwD2vEyWYZNN3qXDVJo/zClWDO7iI721Kur0DQ8HRu9Om0SO2Fm89c\nXu74t9EVhi3srUpreUoA8x6VYyFRTwWFrL6AtZZJ4a2wTp3jD3ppyo3R5BV4\nQu1LLq1m+yH63dag8tdFdFPodptUpcra4lkNJEh4roYI/DDrdLuw6doElkaz\n1LknEZYSJMc6tnzOynxBYhdrEc4HZ7TIJWyhltbA39jSpNbYx99tkFYdkDJv\ntc3SGlg7AEn7e23bSVraK7PEc0QVq+vS+gcSuAFJ71AIga6PN4YTrnfhPVZS\n3KdGGCckjItZfYqeZimeO59nR3+89ByOXp5H8i6I+LP5RhjlJ9OHGweHPhLE\ndnuK\r\n=9pPb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-popper", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0-rc.2", "@radix-ui/popper": "0.1.0-rc.2", "@radix-ui/react-arrow": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-use-rect": "0.1.0-rc.2", "@radix-ui/react-use-size": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "55c262098cfd1ff389532c4ca04c6125fc5ebe31", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-4yBhjMeRI2a3uuWSv4VNuk135UVD8bMtdvLpE+Lx7bQfePAbc5CarS7eRhAfa2J+atdPtKwrxKF6FLKEiWs37A==", "signatures": [{"sig": "MEUCIDuRBXhAOEW78mgKnO/RxIXvlb/Akkw7za7xgbKuM9JFAiEAtjsjZf9KdmNSb+r8bntkCP94ad2GPuc7YH5sLgkbvCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyXCRA9TVsSAnZWagAAMG4P/1JAirbsDs/ZgjaNGNmu\nAViHNLTYLz/kBr3KIaP2i2Z39+a7o61cSAymce2KMsmLES5khzsvvDzqeDnW\n5WF6LYTVcPgIrSNjE1PRaYZvzALukunFsVJdILxfGVSg4sKNIzwxpw2CQ+8K\nmfJJcU69xnyIcbWVXnN+PwPz4TgrEwevD+h7Ufdw8dqOuNxfjI1xpXhGvLHD\nMO/DyZcjIUzbSgg8IUy4Q6FZjyj2+II4q6rLqZMbu+q0sunhHd45pyN+hIJS\n+kkw1NaWUCkhQDGOf6b4GChHpNfgft1pRMmyBrh1iHOm70CF4Duid15Vghpw\nRGUpfcaPmTDNKh3I85zkT1+21OXyROk3jQpWSBvUNvqLQQ6CWXCt3MBFP3Kf\nUheJ78HPV/r/d38FGArmMWbxHTfX2uHdHAXrC1AuCOvXTO30WV6t4Su7Jobw\nwcgc1YiY4vzDGRxKyvmp5xuenvUnfkRAPRzX9I3eWeWgzrRFFpPdDiDC9216\nGmJ1oXdil4ZolfqGuWHiWchpygZnTPlrg0UFtlv4vUgvKKR+4RLxTXAwW/AX\n9HGmEBSZzgyJr9N3l5NkDcrE2ewe1vGRjohh52wSLjt4aV26FvPXgXVVBldO\n2lu+v7H+fe+Z6WXBM4hBwm64Gm4+e7SumxeVKPqvAB5O84qWKegEVsuAvQ+P\n3idg\r\n=lrSr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-popper", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b41c806870c51562ca7dbfc137bdfe8c8eeb761", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-WfNSqAA5caPv7zJD+LPMoIBiNW7rsbyzt903NFYpd0cHDweprYdAWTAtXvjjY2wb193UVTGkJUAWwYz3f0ku7A==", "signatures": [{"sig": "MEYCIQCqESaV+rjRRq0C8iTe8cu+IszKX98CAulXRXHyaik65QIhAKlVyaIcvS0ubj15TxnRCoH3Ya3d6F7+uyqQYcPb+vPB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmjCRA9TVsSAnZWagAAD8AP/i2MBZ4EgmfsXoQu3rgr\nQDHyXJB3F4LcK705YL2vKRi0j6MiQC2S8UbytL3zT5dMEjTHkqFvdMcdnoNo\ncr2ae4veTU0vpKFgvTM8KNlfwS3/hNS0LztYu/Pci3BUlCPeK7f6h6zU0LBf\nviGdobyfWT0+V17rBqoKiXdC1Y1AkcOGJiAhLg2duRq4TtxmQoA/DI77vL4S\nLzXTDBmGk3E8XLV828+gNCDYAIe5tvrKF0mBsUI3VFkidoV59by3QKyY8+tB\n9l+sXNw8BoNWCMsUbjGOgDKI5XKYkEo8Pqhlz3xc0eJjcFOb4vt3gnVbFDEQ\nrGicoA+nxd89p/Ls103xeb2iiAzspBCIDel20Tk8dXwHYXe/QfPGfuLXmQS9\nGHnMw4Jo44abSEDAT/nijvuSFozFytOroVadIQmt8+8FNnMPeGNFrQK7/hFY\nB6eCskTzlKcUOeSIzPU0gaQtmf4e5iA9NefsPfVzWIp3XolsIEi9WLKPK5dB\n0uAV2Upx4tLN37Apgp9b1GHpyvRtZWgOjN1tADOSta+o2Yzf6gk/j6qe9e7a\n42IW9vO3jVQyrQ/eC0HjiewWXoHfuo1fVLyaPldSxCOYFDOQMDDlugQM9y9L\ng6IVbr/OPuqyKUnUki6OAoXDiSmLHSlxFjWFsMFFLkU79WKDPKW138d0TsDa\ni03p\r\n=g/Kd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6bb78a2543c1411b7425bd2f13801333f5693033", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4wxZpIhIznAYaJtDlYz4T6e5S+GlfmJtDwnE9z+87FWYiVdT02zQClLu1AbdCFgNin6OjPSW9GawusVDIV+JUA==", "signatures": [{"sig": "MEUCIHfw6vlhgK1+2iMFeOe6WjwZ07h8aI6a00luuFVgy7OLAiEA3sI7Ny27GwYxq2puXzURuVGz/mNn9IBr71eAT8+hrMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInOCRA9TVsSAnZWagAAWrQP/2y+gIBhT8mphJnLIXxL\necrMVmkKBcoL/ZtlLdjdkryU3bBnqa/uULffy3GbulAXrsf+UFBJSxw4TDaY\nTPZMg4Hq7UIngyeM9ex3ElYLKLzFif1VJmXof/ppi8UppnxtWfZqxq3m2TCq\nG0P1+1rNjNyT+acoDTi5XwwTdeWPgni4WzG5BSk38AtHSeeb/VdgSieb3LmS\n3CLvW/IGni/Iz1Smr3dxqs5aAO1KrsHyXc5IqyVsy3+M7AJsulfeS0boL7YP\nLsPWZKtZJwtAZrjs37vLPYuybJ5Y<PERSON>JIldRKUfijWT23bNoLD9GCe0fYN5/MK\nJ7j15v+h0J5Pp1d1NYwYPL4nIZJd5+VIXYzhjpYdw0kC8//yYIhU0sN8AISX\nYqtp79MqvO6Rscq9X1PQ2fyW1P/k/X2MMcM4dPuUNOwzkD/HtRYKVa1Be2Qn\nUwMHTknnkePMKKFR8CJXeF2xpft3SdVJwGhvli2kAn6ssMr9Je3ikoSb/tj5\not1k8xq40lbMv8TjE+1PVIB5yDwdkBLO5RQaTK4awSycAzN6PqepkMk5vkPq\nGSIJQk4zU8NXDUlQFpWewSRxrBD7Mb1ymZ7FWgstqWdRCoRKb0SaX7XFkoyg\n2ZvaVG4wIpmT4hJ5snM8AqRjpjkpzvoXJ15d7tcYbgN7lEmqTz7ESDjSfQNE\n3OBf\r\n=GaYv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cf0559c421d7c7b1767f187ace8e8a77495cf2c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-tQdQ+4RaAJPMBKJlp2K+48iCj62CA8k9UY2Oqk0I3/lJRprR42ijwe/C2CqZ8UdDC2iNG/Q9oNIAzo1YDhFnfg==", "signatures": [{"sig": "MEYCIQDtBMcRJvMFl2TaRzqIMJ75QIJVMwWItaRmJnkPJVoARAIhAIeT2FVgVvdfSUuXYxDxXP6S8+x2Ea0IWxlpSF1R8ewU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwBCRA9TVsSAnZWagAACNYP/2NJYvRJiA5xEdny5kXO\nNd3eruaIyhBXIuUdTZEr6Uhv9secKhgf4rGQmb2pDl6JekeJPj5/Oz/Zxewq\nOnPn7tlUDkcsQJiMlQB+tqZzxfBYi7dOs7sPg/iDWejk6KP6C4phaXUcvYQK\nVTa2cAQvLrc5Ylc497oEQ4Lhm8r08phVlL7jVyQMY6r72nZrrhuVl6gfawAs\nHOShmUfs+NbsK/2NTjUlzzfgwSD3cTgkrIgv4OUVquWKOIt3cLyFHbX0QzVS\nsmod3mppSkf+oLj+xQeuKU2wxz+IIiqeDrcyrlMYLpn2GnWDhEyg3RBFEb2c\nXWmJutIKPB4ipNNd5SOkfYa9lKJ3/CdU5FdaWoKiSbj7MxiQ58sUtNRYPzsL\nDl3KWtOIkKoslGFSBXyjLGm3c1djiSoeLDBiPEyEcG33QJpMxnLhKVlz9iPV\n9BrKMOHFdvKylyhEJP7dqEQIw1gZryj/Mo28yqX6wY1hUvG2FyBWuREWRhBZ\nVpJHaqQrHK0yJqRDGXRFPfGx72EIuGnhF3baPKK3qIUrY6ICqjKSqu0qK7Lg\n8sW7meKk0lUQ/8luNhviAHs1WVxoaCC0cpdj0xzlvBfBCiv3tHLkUpKsJV0a\nYbH/zGbJz56ltiuWufyZMn6CHW8i/xcmjyiNLAZ9TUwenVlj4/V+63tayT3p\n59kj\r\n=l/ir\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b6dcc00005c9d1d488ef91370ef7fc3aaab98da2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-tEj/tS0x0k3Oot2R8EpWdod7HAnImM+rL53+n6UXQIrw1Ss6FU9nPMrBByDjGXky2BZ0rF9sf9fpzGf8SzVEVw==", "signatures": [{"sig": "MEUCIQDAc1WYjuHMEpHjPBtwnU/a98ocTjIcEFEpGaZlmnrS3QIgWcEvJxYaqUO6zjbzD7JBQ8fbeNVg0nfksmd2cLRLVgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0T/CRA9TVsSAnZWagAAwCkP/2hZodZA+bnGFHNoj+xw\nTvbkUJB56h2a8ptv3K93tlqCC4gt8MScS3WSQz6MA+TieRF2dar70lZqa6HD\nHjMcAERZhYuwQ6mekKxziydOIBV4IVDpi51hisqw7qinFSq/kfzJRWLve+MK\nmNJBUg1PyVDlVFR7nQDMT5rS/UUr5m7/hWA5txVY33TybyEXppIMu/mIcGg1\nle70XenjgohnANsUWTWNiT5urY0UaNINIcPRWa/aWkJDF2ftDoxf0jX9l42I\nsec5fzIMq7pzQmOBkwFUoIYWslMn0KNhgvsobKp4/fdrQI7ViY394CO14S8U\nPI1zNn4xmNpP7fhLcgUoZCSMP85+9/kRexZ0SOeQmRSKeJkttQKZzym1fDBz\n15RoSgv6Awa9vD9yHobgy+5ostfq6CZSSPWMdgEI+txwmQAWT9ta1FVtyoEY\nKIOpKy3KomaPtj19qgLpiNGRfsLfHZXxJ4fY6iSF6s/5+2hR1OP/jK2Uwcwy\n6ZAOpkh6qserKX9LmBr3m17WHJnc4T1Tv39DjoTMOAqxDzKPU7v4Ufnqgoq/\nYa4iiyEV6psQ30/I/RdCNdG9XeJ4Nx8sr3Ws5ZTafNRDKviwbsEWuFv0IT5c\nZZzHD++BKQk6hIlHsx/hIqe/eQUywdC97TcPlMVHXaVCDIJ70Y0nfXQeLPQd\npXXk\r\n=dwj+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d5dd1401a1e80f51cae09a2bebc3fe3b3cd051d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-r9dZwqekeD6B+Lrc+X4wQJLyny85VQZMrGuQ9dRGoia4D++r3tCPLvfw34WsAEQKvpV4blLUs2fJ+g7ekM1OSg==", "signatures": [{"sig": "MEUCIDRjQQro3omBMMG/xGc3jd86caaOnb0SyP+ZutSk6ShIAiEAtAUoR8Z3j1lfyeLiGepB63UYUBxLTe0430X8MR0FLYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1ztCRA9TVsSAnZWagAAMFgP/1hA7TdiEBzpoWnPFcH8\nXVtgUgsnc2gvVDlCdMvBsx/OMPD63zN03jWDqZKDooW+OAyaFIkeymZNeKvR\n8ZTQD9Ty8mIzJCCMO/pfeRMQA0l5CsVGGJ9f+wE/aH4jYdPZ/cLJw1NdWLKN\n9vgy1M/L3bufyhRqz7pntrd1dNiiBsgsQpgsr0lnA9TMyqi/etPtntrlGiqZ\ncEQffZ3/aEr9gNf6W1FS2AnwQqsVCg0AVYfb7wQ1HP95oN7bYOFQVMTtRYnN\nIlb37tLh0eLlE8u3J74m5qcqBUihgW/X7JhqtLHU114iQlDuZtElhsQ8Ya4M\nsfmkk9b/G2aM36FZULOyZXxdp+7y4T+ynY4H3YX1UuD44WsxXb4YEgRwXUHg\n63aX/vldDCAvr+Yoop2f1QPcEPM/tLVAZPkrTEGLlbSoQys6wurB62rIbg8G\n49QBpPZMql7PDUAM/omI/nq6Mol4jfZb+9EdKw9omxcv2wlEvO5bYoc61dMs\n8Tw2leJAsd+Eud9Oa+r9ucjF3ztxNUOX7OkIzKdiZs4lI2usMoVz02tWM02I\n+aQ/V+wuYD1JmVyR4bG35L40ywPvcnQTcN/K5t1U/PGje3c7G2k+SFHJghP+\nNQ6atttc4HJmlv/Gno4Au/Wb6TQF5yTqQWBTgwFhP+06K+Z7rJVJ5hxW5pcD\nuegQ\r\n=lI9O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0e4bef8f62f89b18fd660fc11d04a9b14ea1761", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-g4+mbojXh6OzufURzjIH5vCbvcz9sNIr/qpOpApG/XAc+pChu1is8clN3thVgtWIo3ysYmz96J2plfdCJLtxlw==", "signatures": [{"sig": "MEYCIQCJtM0hoZYa0DYq4PLoi0oVdMxRynByVoA1+LL/doO2mwIhAK3QF58hg+FOAt2DUTLntClKSdcusXITavnTh1dQqTtL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFjtCRA9TVsSAnZWagAAU4gP/2BryqsjLrT7rfLxPjYh\naw5jvfIQ2mmNFftwS55IGPP6+rAYVj9w3XDlXkQJxfZccXmY3cc0w9/YzN/v\nMCBj+BRe3KSNjOOmTyd/NL9VXK9m4ApKi8qa/QJ25FxCSJFQ8r/JphBn8gGb\nQ4sXmwtWe4ekhp8nMZFVoCmQCi+FQKPz+9AwQCOP+m8ToOfZ02uONWKG2Hfc\nKBlIw0l8dOKWHh0zt6nmYjUGfxv1Mf7h9zsmJe2AQMPp3FRVtLhm26g+4xUn\n7EGU7C5TLNA4IPAavRfeB1S9JVU9xVa3c0a3yIyy8nxAsQqwMqdrtzH/Sd6C\nzJHS4AnDwuI2dnM7HjALj1xR5sZW1aQD4OoLOhqU10xkhW3iMervCIBtTD3D\njNK+5icZDXHteQ4ctgr3BzOtIh3S3iMtXOTgZ89b6mpfOfslhkStr6oIm+T3\nQS4lvLxsnMgQB5Jb9UB9j5uWWw0huGHkkz3zRU9IGAQ/DhIs8HKbAMPL8DEM\nicKJnAZXQ+yIrtGN32bDgYPGERevMWlzL/MdjeLFshC43PAMl1kqL1f9wuHt\nYcgVxy6qEutkzdAWhQFOtCFkgyQI3LjJH4Vy5UyK/oivOkkx9lFMEu9scV6o\ni8R4pECB7Kqw2O15n0eoWl/mS6uo3r4ljjt/jnlacKp4WaLjM6tJEVt69tnd\nZkHX\r\n=Sv77\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0c160ea0f9a9f31f01e8bd96d91000a4e19fe00f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-08Ik4XEDHqfGXSW6YdJV11GSdIJsbh2jiuTN3WfnBbky1Vt90LCaONGPGZ8PAURMBTIQSZ2NI5fOuruEwFWgVw==", "signatures": [{"sig": "MEUCIGh5l2N4ShJg8f2J3Sp7ie2Yd0tRvzHYf98C0K12U0KDAiEAu9etein8kBGTjcq5+3X7CRfxr+btNXLWEc+1Ov7bl+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856}}, "0.1.1-rc.7": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a558a1ddd77592ea902593f7fd9bc24a18ab9d9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-k0yhFkVaDJhaLixp5v4bI8sHFJ9Ad0fAx3mSPVyR6I/HOA8eTtizKUOw0a0+17pnisog7O2DW+dtmP83L9vpWg==", "signatures": [{"sig": "MEUCIDvRyE3ZuI7w8XKpolFRfL+GX5iDg5fg/VmioklevmsYAiEA3w8hIn2uK71G7jYIVmSnI1iIx2BeaaCfKgcoUcv/djY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856}}, "0.1.1-rc.8": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.8", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "42c47475b19c7247dfdf613aeeb589aafc25c409", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-PBMJRWnwV8GbtYHBAXwz0eGlqcm03WUEESHQy/DH+NwOb3ldRPsg91FHM3/VFl0JOaed+/LO5F4e5WeqT5ngbQ==", "signatures": [{"sig": "MEUCIHpsrW51GP09w4/CxGo6dsrLdzPoW1uwM7jXH4DOGhiRAiEApwcYDMvlUPQd/3yHMhEua+PTjHjxEaLbjgsH90sPOrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856}}, "0.1.1-rc.9": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.9", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "009b64ffb391900e0f1446f55beed0b52bfba32c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-bZMX5cYVjCJONOec61ApqHQcWEQMORXBCfDmjVTAJhehiDsi4WEh2JKPSkdGEOzsyCeD2Ug77hrmOqi7tmxJLw==", "signatures": [{"sig": "MEYCIQDmJEWwmvFcc0I8izcbJRuzQk27ZdXn0OtS6qp7PuN6EQIhAOSgLlANbUnG/SoST6KlNbwTk+dDZEQHvhCmQDCtB1mz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35856}}, "0.1.1-rc.10": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f56088b159459377af96a133ca9814aa938e0317", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-N2ArLXN2hpbqMULGlpfseSq16EgptIEU4M4AUnYxdGHgBJLsts7zOr2GM/rkWHWgHygsmxGdL4RQ0UsSH2K3Og==", "signatures": [{"sig": "MEYCIQDrc/y2pWrG35Xb5RIh+wk2wtEckrnQ0ayn231nLGJ6twIhALtS3R1BNuaEx4K5Fe0zY9r6bwow8+8PJ8yzox61vDfc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.11": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.11", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "50c765be95cb7400b12cc6b7b39664fe8c587cc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ZsS236H0iYW2DbmSnvwnHRBprENjH+GV1HN/dJYHt63YxcT9xOJjgAh8NPEz8jTlLDTypWktzSbXDB4iramQEw==", "signatures": [{"sig": "MEUCIGVYIl1agzpiXJ9/UOWgEXr81CrN3/g23Fmecy1ZwEumAiEA2FVxvnMZSLFWbbLpJsS6lXoc9mUflrvECUkhJf5XYp4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.12": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.12", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "da59faf10b5ce3a59cdb44c3a07a716492845ee4", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-fJRrqZiawMFa0HjmX5wAopsY/cXfldb6jZpY/BPSHcTRx4/02NPlgcyxFlh4GiO2M7EnHW+xtdJYWzr1p1BYHQ==", "signatures": [{"sig": "MEQCIA7hm/v/iqfntyg5BYje6s/jT7f15FAOn62JnYhwkUgVAiAQs1CLRfPUtrTPGfrJvaJyPgmhS4usM94zyRUIvJrhUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.13": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fbc70bca8825d6ceda3505de51f9da9d0dcd45d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-UMlkKWlkALtGXchFlOkgtrxIRkKNE5vnqMUWVAs1t/RtYtWzMQHUAKfZDRU0SSuYEhIF0J58O1dPDq8VmSSbRg==", "signatures": [{"sig": "MEUCIQCHZKjYdThi5X+sQTqL6UQm23LY+sbFgJ4KlhOS+E9W2QIgVJizC/JEclwtoiZgwr3KnhEusAX1OV4E7PGcc7AZUfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.14": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bdda723c4136767c397030c876e3b253ecf0a791", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-fPiPbKImMQYlPIjjXsfmAfdldrO9Xw2KfkIQYfkvXWTIWT9d6wDqzvgwy/CBHAndhnLmSqRYRomJiJR3TbBTsw==", "signatures": [{"sig": "MEQCIH8QhW3erU8bgHUWmWpi9pFJ68targlOEfhWmI2mmxzBAiAtLFmLuC3Boy9PiZqeAy0XpnSLmyPOQH7qsiHVRihtXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.15": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "feb9cd736da131cc76c9c3c8b37eea02c854de31", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-BnfTJDZ1812/NsVYE7weovzNDGw+LD3U7/25TmceWTF+3ldg3EDe9gjuimGAxMwDaCQrPl9NTAxbbzV5yGHNrQ==", "signatures": [{"sig": "MEUCIQDCUbw91dcbDG+mC/hHg3ml9Ft/Vk5b/rjLPoQAB0xKbQIgWprXB42J6HhD2afgqy4D87rdMmxgm0i9MaoJBR6tCCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.16": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.16", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-rect": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "60f241a96c5e388f1c32bfd898268ee1d6289c37", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-ooeMqG7IDatdWHeXIgrQbea5VJbfHJEb0GLDr4SGh93zXMkklsM0z+xO5tciE5liVym0t7aClHFApDOP5x/1hQ==", "signatures": [{"sig": "MEQCIGe3q7Rk8LmKTmHnqeuJMaEtijRNwaosBFp/amgZ9STCAiBgIpfepff3zoxLQN4NF8KcHLptGIX8fVoyprR8PLj16Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35859}}, "0.1.1-rc.17": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.17", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-use-rect": "0.1.1-rc.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7e4a48b9f810d0a26694837e6f8d440f8e874008", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-U65sR22bCmWB1dNZMJrGkq/wfhS9SO0oAvwYoeGFiKl/9qKq5ykvkV1B5KsuKp2IBS+cANLs4bcLci5vh8I6gA==", "signatures": [{"sig": "MEYCIQCNExy/XHkG974RBoz4LNsbG9XJdHXZtFcb2YZcq3I/0AIhAIDLlUGxERLHMSW9gJpu0FmrxDxOZUcuBULfXyaDRIvn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38129}}, "0.1.1-rc.18": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.2", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.18", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-use-rect": "0.1.1-rc.2", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6f9460e655cea11fce799caf2d8b6259b599c61d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-iYIjIRrCg8O0tepEL835+C027oFEKYQ2qhtbI/RxVV2JRS9V9+RCgBkV5XAu9g1Um2y2i42JkiNE/T7rVbntgg==", "signatures": [{"sig": "MEQCIC4AdWoDV6DPW3YS78IFkBmasPfxXmHrx8mtgUXoSieHAiBoXcwVKpRD6tWsBV2Cc/4FsVo0Z4RUn7amIrqajlcrNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38129}}, "0.1.1-rc.19": {"name": "@radix-ui/react-popper", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.3", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1-rc.19", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-use-rect": "0.1.1-rc.3", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30d45f393e5f8914bf48a6c796945461313fb083", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-NOmPTGT//YBJI4wp8KhV3bwYRZ0ICUI4ayNE1A3ftKGBPnzDGHjv3SGl/Do+k9gtF0mGyxOH39ji9ge7rFGVLA==", "signatures": [{"sig": "MEUCIQC+ZvBdxngkTKme0iawMWncN6/9QZ1vieM4jSG8BAiXyQIgfwn3XqWbmWrly9hDXEnmX6eJVd/Pzb8p6Y4bI5CA6YE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38129}}, "0.1.1": {"name": "@radix-ui/react-popper", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "239eac72cdd7861636f14ff736f21fcb27237afd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-LsjeV9MEdikDHi+uBvMpPyLHrDa7A8UlX2s7c9GPgqU9non7kjcijO4NERaoXvhEu6E7NTqApb5axhZxB23R4w==", "signatures": [{"sig": "MEUCIQD54F2TFDbYGrrW1YYOrfp33eKdrBHpSwo9FLI8EojBpQIgR8C0/OlqIsaD0FuvwcDvNyLn/PerqVurWqKGKJ3MmyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38068}}, "0.1.2-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5ddd434f16f4752de71cb59551c14e044abc8076", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-H/NcBwMT3eed16CGfkt9kwPxtMcBGt0Bx3K5dVeOS3JP+BtQx/TkmvAMD+sFJV0eFHm3adJXf+i9c+RKfFofxg==", "signatures": [{"sig": "MEQCIC1QAFQg2uMyaiyI1MzhMogiO6tGi5LWKgdIlL0IGc2kAiAwQ/Vmiino6To+9h7ErBUpyTlpejpRo3Fh/iP23k4mfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiAaCRA9TVsSAnZWagAAsxIQAIm/Ed9iYX2bDIU38/0N\nofsSnzY69SgVXW0HH82AHuKLdn4aU1/bdntp8iYUijpjni6H0bbDQdhMkFcp\nxQOw6sOwQmbR131E/ygb//+NZn+xWjLC3aY3EwcvLurU/FxhnYgmaZ6k9gsG\nVmtV0qDhasXnyCvZLs5f125nt0pb2Nqmxe7DBAWcvQB2IkeWT9dAPstWWmZh\nVvRQJvGRo/LrDRRLrqcs/Mw/ZXNEYC6rKdip+hmXLFqdiEFmK0vCBhE0kGYj\nuwOnd3K5TeGEwERgCQ/dXQcHZgpj5OEWv+k/Y1HiGd+/jpkT3M4bLmqqn2Vm\nB45jPI1lt302fAZ4sgEUHu858vp0yKR4IzIemYzCB0R/S/+nqEAXGQsjePgg\ntCjzkAL5u8nBPphpTAZNPeLbhFMpL6sDVEpqnAh+6nM5xsuKoNKGyT7RQmsM\nc5/NH7uPL4rAOYrTfWh9/uz6tNfjlbtzNTK0EL+nCzUlGO3ZtyB8B2740/9k\nqfCdlsb0SIotxaM5U2lwawNHvBJjr1NRKkWU5us3/4iqZ79KXqJNCF2keeOg\n+qKNtAVIVVKoTAI2tnrUCus8cHcK4gr8064rNZcQ0nqIJOnMY/QAQqCISI1J\nTWVSd6lyNF7klfqyMFPZ3rXcpMAVfZsWb5WbQ2BaLAK589k/Y9r9rJHp7jDp\n/jMZ\r\n=cOT6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-popper", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c963804695455165c5f9b226a4674e248ea4ddc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-W+L/R1AC7f5J6OIUYch3fFI5mB+YND5s8ERcwSYsHYqyNJSXRcXqBuZTLrf+NiRxRE5TcP6r4eBfwOWZ00RmSg==", "signatures": [{"sig": "MEUCIQCAQE1edXoC5zZbRQTV3cLqUfQPMXdtFEiew7MivRGdxQIgOe/nnff50LoB/nNrHVAI2bvZPJ6qfmxjrWltMHWzB44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiOGCRA9TVsSAnZWagAANb8P/RNNyJ8jBve8EAcT3cza\n7CiP6OpPrrmocLDCkPlqn137hH21uRt2z8UK6ttkHxOCp58+/PuXKL3bz6LY\nZPE/MfdZLXQslFCrqYoBSaKCVOXqsDUNlAnNZvKO/Ghb/HiuWdezxSre8V1H\nNNkI0Rsm9j6quQpzBIayT2OI0gDN/Pg8iq7jkCHhlutsV46/VdvROukb7x1k\nU4AnME3K0znrIy58625zQ3HlSeq1WpN613vI9jqSDQcru6ORHeGBi3EPw/Uo\nKETFe2wzfTCYLZ5dLO0gJW4BklOOMRdFguo5w//F2dtO9q2zZLAHKq8XWWlG\nVDxCAww0spb6bavCKktS1GkeKuND7syOgRZywm/L2wQ+mLukqtnTfGYVedp6\nD7TYMBLg5TUCoyxfItJWM3R5pinKTUknUqgeKNpKWaYjLxYGTTOTS0gnVSLv\nTbIh3ArkCX5lEiOQZZ4kjjw1Yyvxm+g4p2YquCtFET7gsyngFCj7EpDFOzJn\nS6nY/mvB58kbZ6/y601iXDfWUNfTVfsa5y7onBYOLr+UTNp4KeOJ/y0EpRhE\nfSjFBJgLaFVa4aoO0yH8ErNafoEVdIAz9wUoYRzxE75oDMSE7Rqcw+Nn4xZa\nAXLtxBaUb1P4/n7eqHQdrA42M27Yn0KbfNx8WzNqMvNqPjYvFUVM8VmiH6QE\n0nHK\r\n=dlbv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-popper", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d5735cdb47c1a9538703633d53cd664a9a2437d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-hm8ai+fauj5F6M/Aj/PJL0kQ4nidRpWL0R2TAl4xHBt91Qt9b1+TaI+njCK+cU0fS6z6xNzcYsK110/7xsxFUQ==", "signatures": [{"sig": "MEYCIQC9s0Km/QwAngl5svELfm9rHNFzABjqdxC2aMzv3kA7BQIhAOCxJZPR9uCM7rRPKiljDOZLK7cct4C7shmqm9wPwQnq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjKCRA9TVsSAnZWagAA+YoP/iUUlpO1nlrldwt+j/+P\nxc7zPrMrqYEU6vknuVVb0sKYW2bk1M/uiP72xsFgZM+ohgwTuVRWVdoGIHtT\nVqoB4XnrVIRwmWudYUHrw7NDyFmlmElz4bFHMh/Xb3ueBHpDfz0JrghnqBV/\nWCANm5LkrKpEKfKfPrSeNxcaiKDDTEwz2HJD/R2zLTBo/6eYAYWLT3M/23a3\nsHeWs1WVBo4OWuw53/FP/n6WqtQTDAdHtONUjEjqv4FoboIzpLMCT/wfuuRX\nu3xdUXFb3tYxa6rmS+g5nKBjDE8APAZVHBWMS6SETHaGV8onLui6T0HVS/43\nFC8dkvA+dsBhDiZOiAg5UE3LbRsX3am8l0KgOR4LjLWjMp8IZ2G+RER3EHRe\nDcZU53Wij7fdzrVAA5osd+lyV1i40DSIcWZEJGWjgqW/M881IrBbVVC5Ad9o\n7lYyXj6I7X9aH3z8pVCU5LO8lB3hALwBPYrVZxlbv4YlXUz92/C+riW/MTx8\nhmExSz69kHQL6D/hzTUifc+67Z0csu6MO+ww6aFfTj/QLlyaU2kfMxQmXnxD\n3ILmEBr8FmruXW2Ccxy1pBJgshGo+aqZ+WNh3YAngmq+O+xNXpkMXawQ5Ya0\nIl4ObB/+cIlRkRDomWJ7Pio5bDiY/R0gezCQ5Bq69V7YcKFPMnko+zWdK7zQ\n7R7t\r\n=t0fl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-popper", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3391b0fd9d02766a190b0c37305cb877d6ac8025", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-8kkSjNZnmF4XaHOsEVV8VURYdRXC7hcq31logQQpFROAfYj8XDwfMQIC6qDo0DWKpgiIQGP611xkt4s2uFBBzA==", "signatures": [{"sig": "MEQCIEB245q5jc19y0BR5cJj/yaqPto2NuTxxz/jXmHduSyXAiAWRQvyXCSzoC+3L6HLZhUFSbNMxjW91FwpFpk4R6ZpOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRSCRA9TVsSAnZWagAAnTsP/2GtojXi/NLArRMqYAZe\nIAL8CblB6uU/QueLk2DNAW3lmZEZEDjR+dDg7B3iCrKey8ZQHzXdo2P3llDk\nmcdgpQN5dG7Sgyv/0qSq+Wy7ezdsaF6dYiXWynys38kAfLznlc7k77GAWUN+\ng3/nh8jyTMF8nch0OckELxgKHho2Va43uSHhvYKAwDMK+a87FeWh3J/Cj3QU\nipzU8l7kQWB7REO5e8UqxsUGcJFrqDtregLZaJTZad/UxSJYhVm6wSc4tK0G\ntDYS9BGhXKzsmhzERj8JcGdZydsG21v1dGpQIIqtD7HUvnvnY0oUML3u/MB1\nHTckeLJYyn3Wf4KQivx6zbOLh2HQhh7LlRC+QLacCPmZoQP4ki8eAdk95I+o\nirjfLzhheq9V7XZ83ODFWdkESP44zd9WBI6RKhjrtNd/sCewHEwmPBj/s9r5\npzGEHGP+cTQTTD/iimBuK+j2ZIVj+RDEbX/v96DYVzNzW4Q04l/SmVJosav5\n89NcvwP5VRbBoYATz+Lhb+sxaoVZImXwganzh5ucUWCE2upI5OSSmqSYdCx9\n+99NZHlJ7rIFFuYN8603mdQmqED1QtjV5AQ1qCCAsgqQs9u2m9JdmhX91835\nnEg7rcRo4CU+FmRiuEVX7TsjOdN4FQTWRMEKNHE0kBCPc4uMkWkXDnNkv1Oy\nA4L1\r\n=cZlJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-popper", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "741e53c4194965e0a6c1af0cd4fd5ff4db567c7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-QnBcujk6dELZggKt9IKS+tzFphBZZ297XzHMmitxv3Z0aTPS52ouIIG1d5iWnCnoWJnplQjb7OBxl5qmpLTmGA==", "signatures": [{"sig": "MEYCIQCO3zPDPrawneEJt02+7nL4ixZ3FG+/xG6bIqwSpz9q4wIhAM4L/+3e/wS3w5Z9iAaXCSdDqEt1kWdMFmTAa5YkCnb2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr42lCRA9TVsSAnZWagAARUoQAJXKSaPU6+gxyXwKxDKD\nVrd/gGiBEW3rVul2UuIT2FuB5jW3CJEf7joGwb1llSogP/ziCwImyRb2KoZf\nitYN+hHke/GdB9eqXf6d0UwbnDP06jEd13ZZ6quftBZEGSxAkUN50HfjWmPc\nkWMMhOYOdy4fZ8EtHZTF04CB7GpZtn/hJKPQPDhUgbotB1SIAtrrHd+kTgW/\nwVPRlbGcEqNyhWOv4efG58ytmLYt5P/4KycroUZ2k/ldebVA7LZiABz7ybpa\nftEQ/kwHSkkszHKLaxVahaxlHU87QEYpmNwIf3A0nDs0HBe6UC+8x6WDaAz9\nQs4J/6kRb9Xxh3HBBHesvB21+sA7apCaj2+mnbhnzW3PrlB2WNIGxZNVnLl6\nmRq0GC9qORQ8PyE7b2FbJnPeICH0zaE5hguaFf49nyJiNbvakuCMtUCERCfz\nJhorhKaap5dGj7WR3zwAJZcUBRshIgu57DySI7NmAqR6f2pq+qrLh1unZWca\nFwPt/kUw4483VvfRnKIUcr9Q1I0dVH7Tavmk7E2AVtdqzmTU43TmuyUmQVbn\nvRxXVGu8HX7+SAX28NXsk5JbGKUx6MK/Y8NSg6JMH5qxA3MEpe/YsL9PM2io\n79nfzwjYniuYkdyiAWUwRBGckDClKB/LStXoU380/p7i3SYow9HdQitb6OIj\n+gcO\r\n=JR2L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-popper", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9d297649706ff183a65f77110340ca45a008b768", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-A7mmodBJckhyi/wmKIQBldX8rjLaKB0JPEfFgGBr9U1Srb2Y330YeXQ0vZH4LpEan2fLeEDNdhSsz8kDZAu9DA==", "signatures": [{"sig": "MEQCIH96j8ITWICh6E2vTGnV8JIAQTU0Y/5M1gdKXY6JMQV8AiBRfQ6kgzkPZuCHVuvQOnEM+jRShvroBimja+m922kAug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDyCRA9TVsSAnZWagAAZYIP/3Cg6cFE6Ctd8WTzZwlZ\nCgA0c/bpd1sI5aQ62jy6AUR5HoxZxwbUO59uIAilSALg5FH1o2ezPJh3U1E5\nDoGcSxaPrvCMZVPbZM9nEQq97zrPBoPQzjTGNyeWksbKzVzg14lL3Vi6C5Bi\nl33R7VHQ9x6j4ZRWkIGY3EnXCHsR75S7Y5LGb/YJCtRbqUH3o5a8NxJ3JHNl\nAy6TgDPrQDnV95xPJ4Wo6UQSvm4wWSjN03uzKK9q6RjNDXVL39ERuW6oQMiw\nV8XnA/W7TtzxBEzId6JPCZbgKzaorqgq4ziXvh8NnvK1pbRc2p8x+QMlh9vc\nMAsknzmiVsGfKCoI6A2ZEnaAoYihhSotrzXa5kPZm8o0E3rCqSanL2iS/WG8\nzS3OXJpmygVBdwPrQzbTIPdtUK2FOPDZSxBKtluztIDwXsEDTQ4nVrWtjkRy\nqKb+S4Dvl9amu5TDzLlNMqAAQ1k6vRwwU1al6AKPk2oW9YW3hlbmRBXjZdNV\nzmnO86MDhfWLJs7b+I+z8yuQ2nAtwjHrROCZE7kiukuCvgjw8ufIsYQqxudj\nA7OoquRmj/bs9Xn2hOu+kC80x3JapTaN0wK7zHqK+wiTLlilZBfi8g6pRWmG\n0Ei2U2ZV5FW/ZT145l1M1IXl9BD2lxSyntkaNYJ4deEsMmNxubeJQVk/Wgbj\nYHhw\r\n=lsmx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-popper", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a93bdd72845566007e5f3868caddd62318bb781e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-2OV2YaJv7iTZexJY3HJ7B6Fs1A/3JXd3fRGU4JY0guACfGMD1C/jSgds505MKQOTiHE/quI6j3/q8yfzFjJR9g==", "signatures": [{"sig": "MEQCIFEdTTGI9QFXxes7fVH/zweEdpf7cChs0RjHb9r3iJ0CAiBB9A83HBYSXNFRvH5pL25hgg0SoTHcYGjd3va1v1nV0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLikCRA9TVsSAnZWagAAD5cP/j/XRLYOrSETPpj0SZKb\nLLQwD7t2ia49ACxgRsNi/zuTDY6mbKTUjRpzITR/HttkMSZ4J4kBR3rVIatm\nvByiQVOfjw/gYe+nm36rX+firJi4ojBxPQ+PPJZgo6KJBxPdwfLRk57nH3fv\nV/uCbM4qOGAE1732rwLvkvy6xuyTZNYJ9RZeAIOg/QqfGXyMF6hRikIUFEnY\n/rqpeKc8ArLAAGfv89Du/6HdB4U+FFQ53HFcQs4MSI4DG3Cg7vQ6o72fieSK\n2K+oiu+2WIDjWrCqVxPyj3gklC2mOV6rgS93hUCoMOlTHjCzSI/NEMTGjNFe\ncTzOOGSEgKxNjo2P/m27g3h4/rTnXGLgG7FZI2AV/L0EnEbVadt6s3DChPHZ\n3BIaaL6XH4RqjvEIapZaRQIpfHoL85k/oiJSm6a3dHEPOsTyl7T+DfykMUnG\nz3eCoUZ3iyl6qdFN311sheWcahgspfkW6t+4PbPpO4EHpN7pbjPpoWYmMhna\nIU1CYxyjugiSIeNyfxKMBYzM7PhypmDvRUIIZR/I5BMLaebFqgaWSSgr40aa\nc31uXjAylQCyBXJp5D1g8/cKrMJjfPjk2bDPoL0h4a5CDsu9R4WJtcoojDyD\n+8RmQoJKs0XD/E191GIMAEB5/TNOTsDZkFKCU7mLBuunM3OO/n7hg3Ic4nBq\n3P7P\r\n=6bwX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1003e224b4a2615e79edcdc890c01218102eef83", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-crm3Q/BS8mZaF4vphO7C+bQ8YNoLXX7w/B2/M3p2pY+PzkmK4OwLsuQKyQqzKS26Mhy7n2uPtPLdVpjsD+b8wQ==", "signatures": [{"sig": "MEYCIQCl6d8NWHJ0JRtEoa0weM/NTDLfBdcohv/ylv/Wsi0BJgIhAKGCluxSx9Sf59Mql8wPjOS3qrJISDJyv7KADyU1cU/3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLjcCRA9TVsSAnZWagAAQqAQAKFG8GczMdTrToae7nVd\nd8R8ALViud92/3ot2N1AbLLjEe/+sYJfsZdvXMwD2WC4jZ9VwYmRhYM4u0G/\nlVpgjrj6/s6aTbgmA0Sgg0UCmPh2Q2uv9206KxaYDDkIiiIiwCDpwQAHJAip\nY7GXY67CPqzmiOs8OT6Zz9w9i00nOff4/bbydiMKfDoFxvb2vj26di0iJl84\nJzkIOXfcZTq0wDKPF5meMsDvJdb5gTw1SEcKPh9zuJusfhCcwgS3xaKauQXe\npLkuXKppuokKjwZkvseIpPSVpjOutXGmdMi2pFpvwAu8A5zaqsYAziM6qZpU\nLGc+FJ9uiqeuTeUGX9qhjHD+BITiNBYtUouDtVNX4a7gsDFqfjpBSFCStwBi\n+ek86TeRPMvrY0yvqQNDwGLNKG6oeM/d+yCiKVq9+BYBLoYoBuZ96xFmKgvD\nFqETEOKiwSZf/6udN2jGRVQeLuAPV7aTbLFPnN7cQ2ei40uKD09BCldVXdr1\nD6xXEMRbLO0HW1Y3EMCAQSnKaksORnsvuNvDp61+dlgg+Qq6O7Yeeg6OYM5E\nbddYXOFsXlYuokjIUoxpp7q5WD9KaP2+mLmB9gQ7CNWo6yhEdUvUvKYB850w\ncPM8SWfhJJEySys3ggx1nETVplly92nXnTWuog498PaA2MenfOLTKTx7zkUX\nahY9\r\n=jJBO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f1a73cee1eef2a14e219eeac62d8b372c26548e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-2zNKsWbA1t+dtg/DCTRKaZluiXFav8WqLRHf7bh1xMyifCRHiDnktbXJubtAHDKUjTn31e5UoSC9579d8ah/QA==", "signatures": [{"sig": "MEUCIQDR2/zw2m9G3IA0/tV8M0I4CSbKNInCGUFXWWyrZ5f3WwIgJXWjaxGbfn0mCihZIoCQMVoZ+Ito9hj/2ZJLD0knxcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rBCRA9TVsSAnZWagAAMnwQAICL37AMGrlgNwHxWAEa\nLu7f/ywtjNHOAJj2DLWrUiXvv3im/Iryu8yUmuJYU983xVpbTJ6yV8lhOF0n\nnmuNJe5bp56Lw/RGvsaSBuUK2kIxNPeJ9BR6Bpm3Xm6W6Jc8bK5gU3DwboPR\n/nc67L14EnLiDr9hNVH2yZJTHvyCePdwa5zIirE9g9QPNM54p0bw0/yb6MSN\nqqT+gFx8VOmq8CyqMntYxmrgD3RdMikF0/fStwfnj5NblEYfHWs8bgSW9zPP\n17UOBfVOKe7XchUIZtaJtdpKrX0/uxvfi61Za8m2ULbCU4FSrb92lemoVbyB\niSt1cXMi/uem2hoJOYR6aI8qxS/7warr1kgv7txtnci4boipMxPxpGws7/lp\nDb0yLjni69owgeTzT5SiqGhr3jXkxmJnFTqWEY1bPbJM9+10WcFHC0vQQd9D\ncDPgkUSdxnPLyculGCbRJMCet1RJuFdEQ6pA1XTTll4zYXIBf7BCriV4vhHE\nawHHaNty1Ar8eYFyiDgrLM5XzjbMQoItrAgK7QKTUKV/arUgH+EL8sOGasql\n97Q/MbLbXcNN9NwigXFQd0Li42n768XrlZsScbOknWujMYptP3P/V8DH7Cm1\ns0NaNgPQ93sSaSdMXl08KUWMnYzUbI80TobxMPAZxtveuDkggD/xUagQl6st\nomGS\r\n=tpUC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "16d1d2b0894c36034f479c2e3c3cb88e15deee4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-SUwfEGMM6bEhHt/tYhRVi7W+4R4+YkwIC4qh5MRF4zh4Vmohmp4GsnRJ7SSqFUtJCRoTsGpOxYt/WAmr2Br+lg==", "signatures": [{"sig": "MEYCIQDhOnClMEq8/gOvVXKYFBn5b605rT2nwRrZjbkkMArDlQIhAOFCdxlku9ExfHERFnyHR5M0qCZPCq/NgM8I2zbarRoZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BD5CRA9TVsSAnZWagAAl5AP/3BqkJrafojyFN3cpHMW\nw08Ppe4aF3jBnEIgpcvCpskRuSWbRXoVAhYrEwfulNMPlC1hTn4rMTDqJgFP\nwWc4KMZLycFztyYmrl9QD3tLpbJhMd7Ijbx2NiAAdfwa46xWWEz87yf4WUuY\nJlSVNHvwsuYwNk1Pe6mE60Nzggmq2X1YDnJVgxY6tkimU0eikwQjp2zYwqeN\nKj/d1XA+t4HeJw1ULCFDNHwAoYLg/RD/c1OFOH+GoXG+BcUbYFmlKsBoeK25\nxKv/fiBF+tkaA1r0Jq0IGnyHAevA6fqtGoIG580Z8f+ine8UlFrtrPDuMkrR\nZl++3gEX9fXk5JstTzMNIbJTYIuvpmZ1FKh8YA2adQRkz0BKS5BVNrUaw1cg\n99uOB9dRIxsAF84Kf9XdxV7L7uL6In6gs5Ui5XoiMWVM7zNSLnB5mgNOsLOe\nBa6XQmIj85MPhhg+tOutc71rJcdIaMHtu2fpXG45c2V6mAJ/kMX500g2PWA0\nnOE25HWY/w1RZKMNuCGYh+Va+aI0vc74P6MQWp9+DeqPFy4Lf2AdieuUX0Jv\nuJfx7Gw3S7PBQ9I+8PvW3Z73Co+MH2kuKGg9PFDRhyaNFpH1S03nFDvXEfv+\n2xhSHtLlGg3eawSMO0XCgM2+o4q8EbRCi06gfo7Yy370noSfcBIMslLsiYa8\nBkhO\r\n=RrJ+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d38e1944d1bf9c20d8603ace8b6ed3fc2b8379ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YIuOjh91kWz2Ftxgo8e7TDv2NFSwRVJ91p/juUG3grv52+aGErtcyMJmv/5uGnpCS9ouX5wEHKK6IP2izL5hBg==", "signatures": [{"sig": "MEYCIQDgLKjLQqqpH+Zwes7eL8/s0dm4YEbMCdWytrtus0A28wIhAI6T39Aokd1+ocNCfT5+W3z4pQjEaHeosMFzB02CL+hP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CmeCRA9TVsSAnZWagAAJXwQAJExlh3z6NueGIvTvQ/k\n9xvpKT+v7tsKUc58nZtK2X/pPlLuHWqhCeM9DbDo3bWm6vjYS8Oj46W47pOZ\nHha5aGdO8vlGvdGPCgeDI5KTegacWzdy4GFzS/MgiWPbFgC1BBbDf5to7RHa\nhT6ZKL/BZD4nnGkcvn1kev39p7v5xyTpIalgyh4O0vfxI+zxVEXgDKYXfCzx\nH1PYAB4W7RwE5+foqCj5ZOsiPlBctt4uolgRtytZv0rBT7NyYT6Bu3ZW5qrk\nwLAoS0ycWe25sM7Fi35ezGU6h+QlcX5BDbvi0hGBhhTXAhekh2iZapl4xc1Y\np3MtgkTrS12mV0WIOVJ7h8v1WC1XRnsil+vjjhKLopy7HD/+vPVUPDzZksY9\nKU5NbPeOkY/nT5opXfsVIKOBInER1K9mO/cBDXV9QZSOih1Hun5YsJJ4CxZq\njqTvj886djbuJIoQsvj6QgDb0XjPlgDOYrZo+6VHsUXmAD/Mv5FHop+0bhU3\na8jL25V8HTyKZoaNygWidtUgCI0isr2n2shviTYIG3oQkyf79ivjqPl2hCWB\np2s7lwfzI90KxCuOQxWqwFePzpiB0/04VTzjyO8MP/4EqWUjqN78AVb/T8ii\n16utv89vgXad7XUZNE11XsMz+8QKMRVrzlOfLKgpqO7mfBAPdihEFkJ38hpR\nSRlO\r\n=s5ry\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b9ea38cd3ae738e724cf17ca82284011909bbb0c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-yMTKfJmTqgTBLLe1F6ORfMVTTZqU0HBKPZlB4PDEOXBOIT/LpjDXFba1fuUOt1fuiZjqM8jxDqVK1GcYXlNrbg==", "signatures": [{"sig": "MEUCICdQYZ3Wn2wLJ2H986NKUqX8QaKo2gaZ9l0HW5/kPCsAAiEAzTyxrlEoT8ctV+h7IfduTMhUupdXMMB32LIgnQ5K3e0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GqSCRA9TVsSAnZWagAAyxoQAIzZ/bRCkdgc2eAncpxj\nnWzRN0cBlFhKNv+dOK3fRjul+KhmxowsdzoqbEkeZDeBgocdhQM2V2aQ8C3m\ncnUfUXU0Dw7f57fbnTtNaVbinU42NFr+NZaDPOHMKHRHLQJ9+pYPucrd/Iu4\n9MpeH1p2JBu5MKrxJrnvCxYOfVZDxk257u0sF5LmDRaC0n/aECY/EipQFbty\nehOevo9hrW9lRK6+dJ+gpD529OY9J/z3L5B/nDxzeFwDHiTYaMOvnPZVikHu\nc7zjrkjc4eWzQnL45tY4mgAB7ufcpUpicdnNv2VwsbuYywCp0v2NrtVR+Vyf\njnD2O/6s9eqvvJ+8RVTxX0iOjGgz3QpfIRVe9thoYnc9dyYw0lxRCl90KFOL\nI9ahispNn2mIS5M/uuo/EttrQhLpJ5uVFRFzM4f9vdS2PNQKDZDJ4VyFgZ3C\n2T6LN6aBYk1dLV1qoDtcInchTK7XOuLvcyqbjo3YeAegH663x7D52szmTQJl\nM++IlYGhHJba0lUrNaA2TfDLk++dkmRnwLVC+eN7zrEcFx4lD/z6k76dNP9U\neA+FKr1++5AI4S8c2E7/RJwteowPhHluIiACwyHCypyYK54Hny1D1B6DeKvJ\nJXo7prslatdj9Iz0gYNyusdc4f4RGYP04th7wwOLebhFbjc7L9vToMUlGC1C\ngC2u\r\n=cNwA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4273a0f9f33ba28ae5a38e99e8f33b9da423d461", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ZBRAiqYPZALM4wW60OuGWs79C7nldVHIK8oB5TwitvfCXqtQljc5zfv6ktugZhqhywXTb2FLCp4nJ/SyDaBvRA==", "signatures": [{"sig": "MEQCIHiDh6Cnm/GZz2WQuiaAlrYyxztnNxYvuPHZj5ZDqsWyAiAQQkcrE4rfUwNvO2k5nZsJ4hW+tdlABlnF8RI2xZh5Kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZcRCRA9TVsSAnZWagAA31IP/3yWUV3YD3ahKzF69kSC\nq3uOrEdfRg7JaE9L0thyyzbb83FPwioqQl8gax3Gv9jNVbiZzqECRUhd6eut\nFF6w+ip6E8gepYqq+CjPAuVrjh0WRTMtkbx/5I2wmF9n7hZHlH/SgEfIYErK\n3pwGzmb2uaZuBvIfDiRxHoeBM5C84dRpcrR3GtiGIUKReJlInHvFRsFnZ+4R\nV77i+Nv4ObJhw20oOx2D1SKXor2EKSIXuHccVnNllUMhzI4XvZWG+UfHeVhG\nZnVkmlI5hiXkIBlWznCKS97QJORFTnB/Z/2KrqRc+ltMtcjRpQtKP1dfd9CI\nsa9xRkMUru2Ov0Z9hk4g0jNGTC8shiYQAxeW1p0u3MESQ85+AC8lQ4ivAZbL\n4pCq35lL7vGQIuV4bOV077NY/kcd57H9q6G3fPTLabQiFAMIvvsxRnN7+FIR\nWcszUS1dEbHNmoIjlyLjQQjzK22s42bvzXUgl7LNrvpCKcajbmo0jN/5f3cU\nqSGMbnAKP2TWUxjGQHuwM8cVz45oO9ykoPZqu32tTfqEAkqbIaJ9xz7owrEp\n2HzvmE7raaLZRp3EBWzTqF2xAMp7PPeiKivsWQx794GzrgTOapOaETLANXdw\nt5tBggqZ6yBqdbC/OXOLEjvywT3QV/tbH/U9BiNVSph7Obff/5eJtnd6VUhj\n4wbx\r\n=ag7L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "78eaf6ed41a15a1072b460c9c9708d26b0cb3db2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-lyyE/QYvdLgAIsEiqbpw+qiiid6BQxrHn+8+W+c3Expi4ULruVW84V+HTsSdGeiCamt8+S6840ZPthdd7qGi8A==", "signatures": [{"sig": "MEUCIBuyHYpGODMr5j82F9J1cYIL/sAD1N8Vief1MTfam/8EAiEAkfDmLg2trN5tVYbYw7HLCoaywd69en1NRsYcw9oS+2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YswCRA9TVsSAnZWagAA+aIP/jdYvennAAlgq7ISxWBI\nyDnrubntL5Kr7lmF6B4OhnCcSHyZDoPwiV16rcIv7zBiFvmG9YBn9vpeDx/O\nOd8p1H6EvCYwU2DlYdRYgvN1sg41FzJ46+eJuc4cwOg4LE5KK2O1LjK8nVLN\nXiTZ9OctHyiIRW55XZdgBe7sLCSWYg30CWUXfm1uJ+znni2/m8HBKeR9E/wo\nlBGamaXXI4YFQ0oYTkadFdzrSwmvnt+p1Y6pkLDRXGb2vlAknfHFjDL+eWDs\nOMrW/ktq8suncsfXdNke7r0ZkVhH5K2CmY4RojQm4yhJW1BXMim2sb/nXgfE\nRZNHp/SDg6o9akAoERiUDd73YI9Kh7TUl5+M4dFLJ/CeIbwb4oUwVlI7Gej1\nF9zCzvqaE3QwaSUjBv95eknp6YniqOP05W81/dQjwjiqvJybg2El9QfdzIuB\ndy5f+xMaMzs873XduAUtyCvjXJwQl6GmHaJ5P5w49+SFOki3oImsLrxzr0n0\n82I0oQr7ilWkFokxaAwJigjRXmpNAPEHs/fcfmwcut9H+5kztmuf6VpAZE/j\nGLyM0b4pnkA8Rfk/BO2HUi3uWVM4rYtrHaKRbnj+KdhhN5kVQMgAmYdkG3Ko\nJjTPNASNFMqmvFSiWcMX6nfYJ6ILFahJhQ/0viL/LiNXSyQ1LqNrvprJDuF2\n+5Tp\r\n=aNKt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c91c2e7804bd08f158dac9aa8cb7e02768b63dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-6WgN/vYV4GnGPtGzK3fqc+ZZA5ptKeXU6Ya8IeEE4joNmvKdNqFWaAdBp/GDbXFwGmF1xXIhwoUwJifoPgC7/g==", "signatures": [{"sig": "MEYCIQD1qGHkDZc+xFdh/aZGRISjmcQ9n3hVoNrwaUWDDPjrXwIhALl3CtfVo+usbb17+KAjTPdNR8nTUo9ItykCzSK6XFnH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdLCRA9TVsSAnZWagAAkdQP/iBl2KsyqA/bEmR51t8s\npVk0kyYVPw5n4iJ72BwtCev10e2+uBnnXKCNxMtdBm83i2l/P62HVVy5jlGu\ngmBmd03jALxfTJHn3/Ob7W0RShALKuG4ymy1fUHNqlWegWiCRYscutfVxyPm\nI3dJQtrM1tj+OQ2Gv5R02I+FgqzOHj9n11O2T6QkqKi5JBNZo6e1h6rxfP/0\nMSYuvDYYjlg/D/oAn4YFwzyWgeL0a4xafwOTwEvkV/fwQTetGII+S2wXbTeC\nKimHbsGaZ80EUdxcKkoFf2iDqsVDtPhJU5GdRt25owCFcpjrIWztI+OkdSPz\n2nYyfNxx5Umpo2EIB3nqlUm0ji2nwalEqRR+eSSu0aeb9iSYxqhFh2UhRPzi\n5hCMCDe370pGdk/nFuy89YddqtH/U895Egd9ih328lk3E8d89jyNMyN8+Iry\nSco6NvsUbVvdxNu0kQQlKs+MdhjBd2vJzJ6rPR3AFhVzPx//0d1a+aNORKYR\nxGjPTvrECdlcFMx/R5ZIq9GO12fcS4LkJUyR+UuvYtMeXSN4jYELG0UUgOek\nvWpn8oiEW33R37pLLldB4wTUQ/fZKXXunl5N+Dqu8OqTuQS8fs2iVkSga2jg\nPwXuqcxrCLVCEBFZD1K5mm7SY18ZGTHbxwL23WPtdaCUTw+mtFiqBHon1JH7\nyIwo\r\n=pG54\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4509c98b10d75b981df605f660f94573882dfb4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-8yTSIUrn3jIATTVW0Rp2KPQoWPWPSqsJ7wj5m8vRKC7q+2RrMmXcbjhPzVkqVsbRJccX0v3j6uFS8oQsXQ/VmA==", "signatures": [{"sig": "MEUCIGUJNu7cz4O8+LoZ78p9J4zK+pm2Wq/Bi06kDXdDn7mPAiEA/dLVHcdzHjD0DiR2yCHZ1yj7VmIQxcHvGy8VR7U5vdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDLCRA9TVsSAnZWagAA+xYP/RL6jtrBgDtPb32s4hTN\n5gfSTFoKm5JXV4gVJPATRypqRHvI4ojwTCkkNaf/ebrfY8DIKIBndP6O/0IR\nqba5WKFC21MCpXJkKHzw46tFtLiNoMMCNbGfZ0u+GJR4t8LS+OKH6R7BT9cP\nhLHHWADoDplX5Amh8v9osa10Q7XG8ufZvsF68SlKLmrt3Nacm+ussTbC1f/V\niR/WrQxPabSKLpQikcYjHd/PCB9YvWkwt+3SEKYH68nCQR0FbuCtBQD7CdjR\nil1DpORldla+0XS9ZnonKXsBC9uO5idHKS53MQu6vu4AlspHYFhvd84RnrNx\n+SwowvF0mGFiOjWVfKDqj6BHcgBpQ+5bM5/XkEarqwCnVD0wsG0zdBibLV1t\nSxaVt/9/fMPJynLtokc/EHQJMxzHq84PBOX1U7DBgtekTfCyPx9ij6gV7z1L\nN2KpdTM5f1QLoSPOo1wTq21LhIsWdvE1WLcFtO3dmnki0zKef/zclLURdJc8\nscOo+cdv2DpHg0jacny1B/cVca2K+7XfMmM8pPmJ6dA34vcjKd1cmZ+ZdluG\n7gw58S+xOBc0tnf5xCtJwK88naSIc4vFQcCE7NkyC2lXyo6Igm5MUBZ+9Tjd\nr6dlBDtL96N5+L70JGH//rxqQ7ZNAaq3m+zh/0aqdWp3IRlBqVFcqRGop/Mv\n8DPL\r\n=z/az\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0c5187899f4186c48b6967abcc31c9ee28b5c9e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-6FsQS4Kx2itF8V+ArzX5hPA00tI9wci7SxYYekLcQw0oKQh0pmFrSbZ1fDkFXp/FxTRQnFlZ4C5Aku3iMd2+uw==", "signatures": [{"sig": "MEUCIQCB72annqNP99UEX4JuEmxdeNgmuiamUj74vEils1PqRAIgKo1zCYY6qvEZGfx7PPxBePPw50LlqXVPx6Px2r8MgI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLGCRA9TVsSAnZWagAATU4P/3waBTG0y8S6pBUlJQVL\nSfQvpK0ilrkFuhFFVdx8rsNe1jSIGyakO99s2PRiiLYVEgkQAw2wGmyutqzm\nbIm8PkoXcHwXsfDXfYpNXLmy7gQKMIgEGBmN8mujBVRVDtcMYxMYbTpLj7cV\nLAUL7TBZeSUl2KllMci51gO1+DT6VhakJPXOSU+XNjYlOhTyZ5tcq4kM4mhA\nYBn+lYn+UR8eag8sOi8O2tXAP3ICZUKgK1bZUBqmh25xuRAuWFzlzjtmtBj4\nGqfuavNjEImmAcnbtbEaL8EafPb0Cwj52G0BBK/wjZHHtZzWhV70EgaKsMTT\nA2AXB/Rd+hfIroUMg1Ou6uIStl3P4AK12099NjtQz/Ks34QpLMG1pqCZksOH\nZyav95yAgFtP/34Ms+iRHpM5mYhrpavQFC0sBE0FQnP8BAjIUD3qAg6OMB17\nOCdJ0bZK30UUoWboHC+rJGhGqWn/ESs7Nn2CWTiCEWew05p4keGIfrimY0vW\n/eyGV0PDFuCG3UDByDtd+BME9FjaNNIESj1NGYvISFgIZJyAE+M/Ii+JWZif\n9PvspJlcFZJErQNyUeZ0HNJHmcKcPoojgb1KLS0+do9G1mvr+U5zwGXxwnVR\nb3IoHXFj7738brfOd327NtHCuOiWpCgGTed67bvDJMqx+zGlDiBwL515trHd\nplD3\r\n=VUaa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "90b39192debe012093191f49bda2b3daf0e06bf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-PittLjOBVrD7+oAJ2oDlqfH96I1j9n4jNIQInC8m9nj6qCjrcmpBaB3ToYRKBca5pXLtc0iBbQ1PrLpCJ5E4bA==", "signatures": [{"sig": "MEQCIFOKwUxbNU00uHRqXdv3dt6DusnRia9zkNqPy2mpCo9fAiAvIlzm4UMdxR5K/Jql6Cbydn6c0z+TaezdKB1nxhuGnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0FCRA9TVsSAnZWagAAKi0P/RcCyhqAv9heLDZ+DEiA\nBi6jGRDX2gCBZf4ZNg/ssp4kvFZD1UcvAPd4eVoJeghkcibZ6rqM/IMYKdg/\nSdpsJRTHzXMO5CfbQZx1+hVLnXuUX5kAvDNlsA2zj+0fC0ga6GcUJWaB9cdW\nNHHoLkKSZTSEFFancHkObhfpZH76TvYveEHv6vRe/9cRlS6/jLP79apyuzXl\nNrIorT1LDiefUHaybneHnQzv4KVmQA1BZoo5uCOojsdRxxTh23fph1L2wBD4\nQhJdy9tpinTQU1UgSO12tzj+blevUGm3bHHXC0GJ4cuBko4DzsRp7Ua9xd/3\nZbdKSFjUbdbHMWtXUxyYCH8OXZ0xlb8U0S7zBswGLFdOG7FlSO1qJLma+6m6\nQ16OvUhCeplmZOx+tv0Dl1iduIoeMuvWaDTeYtCxqm+bVSfyBGRg68gKQAmJ\nDxo7N26Fv+7tSXk7cHxLd8za3fncxef1//dVCOiJN0mR4Si7ArfjUnmVLrXz\ntdyjStbozowlBB0wm/BG4hIsc2cVqcIt6GivcKbbYQvRkX5Al9E8dKhkFhja\nad8IvDni04nF5rLpmfQkneJXdFLk6qvF2A9Ftfa5qZ3AugnvuLBbszTkcLqa\n6SheMScPPyNZEqtzADpePEcbukByG8b4DK3q/x77g1gppDesJGZhziyNj5VR\ng2Hl\r\n=GYIX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c0d0e67a40f57c255f05725a8227c1a200b48df6", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-MPWeeubN/R3EUcpdiDKlAPHxKYR1zSC0sGTjtNxDpu1iS7FeiYi1FUgx2ptDgMFPjaLHKzH/+Dmr7FDXo8qt0w==", "signatures": [{"sig": "MEUCIBKBbC0hIxmu+VFyEPCNQ1xLMeznikHi51PLSnswhQ/gAiEAtt3H+ljd0/g78aPzSF0G47voy8K+FRPY+P/qkz/dUhQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SS0CRA9TVsSAnZWagAAAwgP/3Kk+UkvAXlQllYchZRV\njiQE6PNzWkWme9mWrXlfzQ5VgEcAWJdN36FDuNJjyjvKJ5o9mNWgusDenhfM\nu9vid68u/34QtXf86ZWuFWIDBxp9gqdCzp3HdZrFAnvH3FAKckbkm/e3L8jR\nSmRORWBfXvC+pYZ9Ta60WTworpv7b/aX5uHLve4eD7aLpR0goSE4Re+1Nh2s\nmRMA2dRelSUj0NDHwkrpqKaJ6JGqxNciFKaC1knmMTT4CLXjdcye5sprQOHa\nDwEMN5RLV8/cjfacckmPJo42RLfKihPotKCiq1miTU3mUww/5yOn073G+aiX\nwMVEB1LWKRITPWtuYpEdzCA1shjA/lOnw2FDfhYrJb/fnUlUD4RXbCPsLo1v\nXs01Kw9zZCO4uXKggYWgOPcuYR5NUxP8/jreF4Eg5YhHo9fIGoSwABwDlm3W\nbgkkGOMg8g76m+RmkxAfIr4aX4CWW+YUTjsP9JHBxjfs1puqufQhz8AOtPbd\nLX2qoShP8/bqoBtKV1o3kWHzNspLWHmZLlvlNyYxy5lkVCiGu1R+0n/9sJkW\ngm6H8mO2tWYCA/m4GdKDOCjUHaniB7r7/D+v66JvznvMnK0DZvLcswZnlrg0\nTEOefLZX4Jnm2PLCsK4S21Xe7yLxQe4rH8faRChh4rRqK/9Mtk6ezpS9pv4/\ndXoE\r\n=Wpa2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2647a39b78f2c2e4c26df1be35967ac075fb8932", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-vbCOOaze0ymGtU9IY5U4yv9gc71xnDhVbEy6mkBmeLHndEazbrB/vnfGecb+jApW5nLxsavxPFW5XmmrWR9yeg==", "signatures": [{"sig": "MEYCIQCMPfsOzc6+S62VQHwqNSeWAfR4YRkQ1Yph6qoyN8gkiAIhAOYl2D4gniRdoBYZtrijTLNUh3TEFbyVHhaXws+FD4RK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZuCRA9TVsSAnZWagAAZGMP/1S+0XSwXj3Y37MEdBmY\nbyt0qW0WAdWQpcNoDYMQRTvMDf9TGY0TvMXGlcnbXvTmb1F1Lh8dtIwtQ/f3\nz3uDpUms71c9gcPgv5Rhbh0Nsr+fpyoeVHzg0wo2feUlPlYavESxhJiGP0mb\nRl0LwY/t+AV4xbQrU+9Ah006cxw0ZqJN/+X4+zNIw+/R7c2sK2XM9DW82iHS\nQ/B+K8as2POIIi2pEnmhU452gScthw87YR2TFj44gjsRNnw1z82hAJLjXSlT\nHpzNHxjgh7Syph+SKITV6hk3svFwW9PZ1bzfkm+SMku2eru71Nc2EmhMUtan\nedyTwxZOop8tnIPKkRahzTl7gUsrExDfS2v8L2v4Lt177/N0gi+wIco6Nmj0\nb6pRLxkZlIQvbAsFTYj0LlgWKVJbAI7iGT7orOhX9CaWb+BDJTmPnzlTkgZ4\nrErlJD7TEeK3O5XMNZYf/Qm12VDsgW1lb9pOTxHlWwS59sTCtvG/t3iMKfpr\nGIbknnN3ededAI4hN6KmFQ4OMVT17X5cLjxwNWqJc+vZUR6LyjHelTMzjMN3\nGpazrnc4sOYW9/bSn9J0hsLM2+i4buqGyr0N3vA6rgZ4BlR7f9LlCUQkfOsX\n1Do3wQoyHYHKWOOETv75La+ZX9rx1fjBa1ZsodHvzxBAUCxF6pF0cDMJuhuu\nhvgx\r\n=G9AH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cce91680dfa611a4d0fc99581be7a5bc41c50897", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-ltBuExj9nbcfHYNbGeJrjVT+lCBTaWfPkAWf5WL1WUroNKKYwD0gurbyilCSAHutcOdw5Jnntc/7j+m8goKBwg==", "signatures": [{"sig": "MEQCIHGSX0oPbFLyluKZeC6SUW5VP1A0rUmHN4XrSkRfD2COAiB4a26kgdghLbal4tCM6l3wHVS5/2xzxGa15te4IjLr5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wn2CRA9TVsSAnZWagAAp+oP/0lwmttdXV/K9DoFKdkz\nq9vC6Ot7Gy1ygb14OxNOlL7xFVbcYK0101Zkzd9DQ93W6EsQCucvU78rWAsm\npirOE/5Sy2o7K4uJYUkVMhlsEMepZKMnn+wG5HIT0EkPLvQPBRP1mnXNxZXV\n6DCGC5W2znfCp0IH/VHNT3TlLb+2ptOGbAVICuG25Tzqa54E2UZlhj8qhlAr\nr8D4H2jXeMJURaOf0dEaR7Xrxj4+9Vr25re51MhDW1CBQ47K8LRHJBzF78s9\nnSv/+HTSXujFUONaU/ySZQKjYJRy9Co1nJAa6qLLL5MRwzwj3x400TtFTL46\nOpqLqFhRjfxpXJU3nUEbEdAGrDiODZO+I2kTk3BLsjarHcBfTxf9Ut74GxZu\nwdkkqx5bIwGnDgpUNKF4BfTJ3aMrLwGGPwI4KhnSvWDQwIc+JmsSimcSVN7J\n19ElXCyQ0fIZESRn7R0Opge6xxs9kPlE/RghC3xeyrrQn+MVN1DPjzk9fc5u\nC4pZqX+sQABmYmQvMfqNNmykYFxAgfT9o40nAq6mHbQFjj1ylQIGSPYjL9gA\nsLJg1qB9NuUMWWlYMOw7BI4MgLJbgqA4YZLLSD/6+HDY5AF6oLK0Ssb2j7TR\nCNvnyxeNArrqtETbwuo40iBZhfUDjdTKYt6pMvBcAScoPLE+QM9T2V5tiJxQ\nLaQB\r\n=eHcl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b0220334bf6a562b5b7b193bf4c3749ee8825b93", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-iAl9ohhUR3APDWc8WFziUTCAk2f1T5uVJoNRpSR6IS1SBkBSWNpkzmuGLqsJtdMzk0UD0PbTdIDVhrSMCTzNMQ==", "signatures": [{"sig": "MEYCIQCaN4oV+rWzA6SRWZVF4DgFvKVU2QCUyKowTBz/1eL+GQIhAJhT378tBLrw4lqFJHJHLthmBtLWbHLKZJd/1+jwqJAs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUSCRA9TVsSAnZWagAA0AkQAJVhJPhtZm2qkaUIeSxj\nEmV0a/oCaieO9QsMPdzmLutS2K+KQGir1AkPtTNJbNGRTNPPNrquPOexmq58\niUI0BY+WxDeaLF35ksJ0sZQPxtqYSpfJY5E2ni+pZpiuJp+m4AgrFpn4n6W0\nTZ42iunFcB0tG/1jChHS7LVxsJrvipAffAn06u2M3LUZHDF8BcM/egqovWR6\nRnIyL2FNStAmV/sxw4/pvAbJ75jU3NEDJfbV+iIygcY+iP06C+3g4TT31G1o\nKTpwvyymFMSzjaft/RqOWfCWNThggzzF5p4Jld/SzQmlGNlXewFCCgJbYslT\nDIvcqEUqtME+wjiAOSX0kD2duInvwGjF4ffiyrwV5Q/C9cuU5x/IRXrBpOfK\ni5K2qeGEafgAZkiMBwmaRNIqpa1qbNLyQJh9FSBG/93OwupmIzG+fsC23QIE\nCLOyk0M4gHUWIQv1tz4l8TVVPnRroRPCJHM5Cl3rOZtXtMhdaCg22NqYjBk+\nq7EUalewCQD+reyk/MBtRcPS/fPs3S2BmHFU49QoJDg+6EGm74qknTY2RSmq\n/DpIxqKwtjPX3lEiO767lOdo4zALkRo+qmoRKQ/L61bpCQ8XttSBwzaj6fLI\nRhIqR2G684GoP+73T7vLL75J1zp7jiSyymno/xyOZViDMOIaRXtpqsw51z8Q\n6rPB\r\n=fd7/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "116b1ab4ad212c647c9a1190a208cdaa8d5b0de8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-SzuxEnpP2RQtn0nz4TZWBS0OdC0s2o+4z5uXu4N7RhlMbeNv+1Ihddd5TNnhjmBYtYbVwk6gcqnXtMxqH2lT3g==", "signatures": [{"sig": "MEUCIQCcKoGgBK0ZHsDBvoa+KSRzyF1zO0hJZWC6lBYpxe6rYgIgO2N6l5wAax4kSzzcGYu7oM3GVdwcX9QVu2cFV5bxMqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/nVCRA9TVsSAnZWagAA3CYP/A9RN7a6wH7eJPI4fdOw\nNXgrvDhRJinIOKk6TcenbL0t57U6cd2F7iBk4tNeAcz/fwMEbNn6aEL8GlJG\ne0gBGMRUxlTGm3y32r3GZW7t7MfaZdmJ6JRx4uW1FBPanN1i5aEcSaCjFuy/\niBSXBjVnYwvgui4oG0fQVBrJfTOqikVmpq3Du2JCfsGwIXs4g2pioFQNW99x\nzBrlGZKnNClZPrzhe6MEu4MX+O9egttQ4SLdHB3Gtf0erK23mvMCWcqn15jh\niPlI0JT1y6MEVFyxeaFteuwdBJCA+nIzrRz0ZTEiIskHW6rg6Mi4G11Snh1w\nj+jYP7bX0nDDTuS4Ho8XHcs7s4ruUj0Zulz0RAVrKzM/PGyOD2nZNNO27nLC\nc01+1WyZ9j1PY29NbFRQ+3Y1QR01lXzZDmqBEUOkyR0kgmPPxYcBQjfAR6HH\n/yi4oaL7jCHPb4zpL3v9egZzjA//mVCHrNkchnYChBoo2XGrLs07xnx4R2sX\n3RSd49QWPlTRiqEfL+xY5PliFqgsPK27g24yOiZxX6ctnZ3Lh8RguscqhWBT\nnC/+hGTkgeUc/4I7Zi2pYWSXjPmxokjzAdn05aS8VyuaQZmG8ZINV6uGoWTR\ntnU11us8a40WpzJjziDnks6+ZutyMiOGuRYw3jX1hxfWZueuIp0s6bylNpxK\nU+Ny\r\n=DU3/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "91b20869422c8e54270298189131c9041ae09329", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-cPykWEiI2LnSjt1ERfTrtfqiVrrbJFW9o3F/jGmXwhUMC/I+t/VBLiOVJywXOzxeranSBpoIpp8dbGTxcDZ3HA==", "signatures": [{"sig": "MEUCIQCjjXWiBF+uOdQJfYLiDwsWOgUiIpMNUpr5U0WwzvkxDwIgVhoHHJSFyaaxHoFbttNKQiH5OPqdlfULykoengxWkcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBHzCRA9TVsSAnZWagAAevAP/3yxDhEzVXUtwwtax4VZ\nKyDjjYOkZSJk6xzjsp9gSgnJX7MSisUugpDBsqD5sDKrx0xb7NesZPVlxNLv\n9447QqDF7kyx5GJsvZXStekx3ywH69mhjvQfMIXt42DTl5bZEZZqhxRRopmW\nGD6Cf9Z0NufEI3H8Ob2q/Nj78K2+kKGwXU7iOwdGuGLjM0yDnoLMm0sG6/w5\npWpeZejlPYjq+12RHGW3KOMP5Ri3OQgIbUsxPkF3XY3v+LpIEIveZCCtiE4C\naMNO1AdeNC7ig03ROUzemvmSmUGlWOCTgn8tyPAs19IiHuT9XTzGn7QiA/Rh\nQx47im38VW4+L1MbjRF1brgkGmsDJrj6/s+RzSuOwd5WpUSRNr8pu2jm62tM\nufekn3FT20IiqMb9e5C6FKhW4mHvoHBCt0GjjnhJPDoIokIrQeYy3BFnbWSY\nrwMy2ksMpwzlJdQ4Tkq8uHr8gc9PiNCZoFzF5jZgOW8u1gYIQwBs09gc338M\nDWNuGl/L1WRYCCOu2Wsl1QO45GL/K4IadZBV8EjgwSLFVSUx5GBO2wAdDSSh\ntUPxC4lBRYubH3Bv0j6iJTcm2aynQLoytjmsES2SnMJx4dJIFJcUBtEuMy3/\nNWSeWADZf+Xo948BRKquBTlHOI8UwttpO0W6ZpZ4k540I61oA867oYMAxtyn\nfTKO\r\n=csOz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1228c1cf8d2e56ee6073fa7d71d16b956172d0cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-xlK7HdXSIEn3kyfs8jUIBOCfoC9Chha1lhEkk1e65kvUez38AOxfqoZzWodkjVYqNFgK1ubCnWXxzyfOmHQc7w==", "signatures": [{"sig": "MEQCIH0k7rWEBdqDsX7QQz019Qlfn141E105xoCzcrRcvTDdAiAhn74YkHGXxy+QqOcUfGS3iPpQMu0u78HH4CVjPEsRNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYACRA9TVsSAnZWagAA2VwP/iQMa+UK5NsCCkRAe2DD\n4tVCPkPh9LsU6/GmjUz9j9KJOUuYAxnSt2Sx/qvA2E4j6LYQ/JMNBRLjVUGV\nm2vxOImrKx5XYuyWfxHJLTPjkxiYkXGxR6jLrUecIFygfw/QiDT1lgxGoOCi\nT3arZsSdSWpziFP/d3AmeCBhJXj+3/OCPQcP9v4HPXoICcnHD5CzqIRnvLmX\n6J9/CUa8JqGOwBZnKdxCJco+oLGa75BS7A5bE8eZ7YcVh/ytsSzb0S/s9KQB\nGaWLjTNaz1TRj1MbNArNmUWSa6xragEpfSE+025PNTPzKs5vXA2balQ4giwz\n05YH+Z8RixaHm20yHulKA0OYwC6MT/5ix+LhOP4MTSWpx4yk8G6MFyGS6XeR\nWzDat0WGctNOFH5cWNdiVp+0rfbbtIDbVyaqIZwMSkMH+yFcBXxCEkfORSl2\nnmSyyZF4eyre+WrebDbxgQegfNIxl90vEjmlYpG3zeaSKmVYdhKZxs2oTjTf\n/i/YUPSyvnN01BGj1AX1Lf7J0Aa8K6fs5mSCfZzjlbUaADnUbAmMlFbPHkir\nhOjLIUGImduRj0JvX85RFgbDlJ4PvmlP9OGXt/ZJ1FzJAOOr5HF/+zGH0CEE\n6zU2+IW0maSBOmgZELlTBq1JgOiXRq9wYxi8feukZ05HWLQmE2OJVEaLUMM1\n87kG\r\n=EuqP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7bf1c4a9866d545a4b299438797fc7ed0630d614", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-C2jkudF1YVpi7B1HIpHg3YAJIDtvzzGZEOlxs8Ez5XllM3ejSLF+EUTr4NbP2OhSq5m9kzB9xg9DRcAkePUvuQ==", "signatures": [{"sig": "MEYCIQCS2kR8AyJgwO8Pvt3rc9b74ugoH0KJB6EyRP/RWgP5YAIhAPuwuoF8qgfhylTJK5PW05iaueLQV3/62PHgqr6secEH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpViA/6AnPATNqhVTK7YdkxH08UtO4hC3aFSBqRaVe+yYSTf0biDxoU\r\n8s2WUZ9uV1cL9F6BqQY5Cks6XLUnDCsLUszZjLSQ8O2Z1LmPfQK022P2LJTB\r\nKVF/3Rtq46DSSUR+QP7JLykVpU+PEf//686ehdMZgOZneJO00tzyCDUaOlNC\r\nDnYeJF3ETTUe9NNFUSCU1eaAapT+IdM7+XjwyglYZ/NPWecweHIHfxvwVZcY\r\n9KSWq+v2lvrWFNB/cSvfsCRlBKU2q4BbVgtS8yjUsDSc61FFijlcsY7n0Df3\r\nP9RgiEIV8UcYFUwOpdryxgl+I2aFFo4mWH/s8w+AExkR2vU6IGMcqZPA2iOM\r\ndImhdqxF7bMnN5/ZpWNylhCu+raVMCXYbqENZrtbfZYrHclbN5fKGi34hD9k\r\nJ17aItMXSBWK6pykEY/L1i4wHc7WCqh8zhiWUiMxh9k5FgGioHDNngRqE6+J\r\nU4MilnUYWb+Yv0FrOwlATkigrkATUc40Q2kZSpWiJ+WhtZWZM6WGKeTWi2yY\r\nca70uL+pbTuLB2eG4OSyQCuJaJLqVB5Wz5okUqxB9QvrFRFt8Xqe9bMbCghm\r\nSWvctudQ9OrzeT4obE8bFv1lEKgAxoZEZLB3RVwdLAMSFO5liN2PRfHYxCe6\r\nvGvFt9fA0U32p8T5f2Cyh1yURWn8G/w93AQ=\r\n=5VeD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d827fcb80f2599f928e244417bccce043e18513e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-vEHQU+KmE+SrxdPW4gmky99CJqH/6RuYT/L1VjJjZ4P5AGaj3n5w+QouGd+xygqszTO/oo1fl+mJOdDWNUugOw==", "signatures": [{"sig": "MEQCICmjETxwc1J+diVqrVbQLdnBTyb7/7YuvBX863sFfb9LAiBhPk2oYrYef/qLBYBl8L8Jvk7P7lzYpvsAf6M9f4yvKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkU1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6WQ/+OKkd0FrLn7ad44wk9BKUvVVhdxDm6seF5eNPk7fXseiBmsZj\r\n3KovmI94NSV+TL5MD86xyELIDUMbt4MxTXrOIKUsq3Rh7P3YeJblG/6XwJLS\r\nie+FgT+RNEPGcNBP/Lv9KOxbslMgdFx6zAgQGZq2hRyyiledAlq6Na8y19js\r\nNFbhJ02cFIL2mObs8PSxyM20vpL7U+1PWsTUAyhoySpVLRIrIChCbxzXoN+/\r\nGKfAoDQPtHXjAaxxwgaBIe6VGC/Kk6F0AHFcvUWqq9kpN4gxb96L6LnNg4Zz\r\n8BAemQGCcwv5kELbP0UuKOe1CNuflne6AUImQn5mFJJzt+VijY3NduBUQha5\r\nOLPSJZ48auHf3BTq/fvcK3oSZXIBzLr3L02YoRcY5xsbQ7J5T3dZ0r2lTQYh\r\nMulPxU0/ZzvDpL7BQAcaTETA+rLzNWghRfPVlPmDhaivI/kF+4eE8Q9/L44M\r\nyiELND9tA3m6l3z7Gn8MloWQCJmoTLJrbilQKiigPavMjY6CVbTDvDXmyiEY\r\nLJOYr7PlPuHJ4yIztMi/SPPl9ThvAARMGo6+FOqeKRxUNhztAzWCfEIGzbsB\r\nPDpnoSTgBivlH9YDaIybAASJrCemEm/1XVwocA5ZKojS1Dzp3WjkaQAno7wb\r\nCfNvytEO4qCnbdPegaGYbiOw2syLyuNDyFc=\r\n=vFE8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "376b795e2422dd7b612ef53b4bcf09d9819337b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-zOrsg/EZngEHlLO+2llJGDDFb/5M4xWJz8oKzJRtcLyScjQjHsc4UCmL68bGWuluGlAICu4U70LUsw4xLAX+QQ==", "signatures": [{"sig": "MEYCIQDZ5oKlgbqGB/32BK4++lFpQk/aLmXLHYMWmYfYgnvMtQIhAPNaKzdx9Cyb4fvFYx4sN3Jzy9QfQ6nEVH3siPm2qUzf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVGBAAg8IjkZyNO5mTz5RXLaAJZaq71aTKGYk9qD5JJrj2isF9xSpC\r\nvL+p/DmzQY3QG/OAevo5Xb25g4kSnM1+7FUK7qiqbumRUwWiwK0n+ye/Y8xC\r\nqmsLJjfwC9jBVIdnCGF5PvEFW0KAOaqmXfYxhGAz7XARN6nUJzIXM6k552FG\r\nJskqdlmIOkw+fQx9/Lot+fPq+Eobjhr9Lh2/D6rfTMQqOiTFYYiZuW3OpAUo\r\nwiDH2jvxRtGd1ytJ2U2DjBRSpuKEZAipTOAxxHZagL1z61YKq60sGZ8n7WT/\r\nxJo04t1an6bDjilUhFy89f6N4rI9DTCCqPjqAF55XwSvJNUWCrsZ+O22T7II\r\nf9oLsUM/CHuqYlUoqfkpmBIGgmn/3Xqm6C8u1SZ4P2UJFmSfNknW3FvYXnJd\r\niYwFyl6hFGP6ADsik06xfh/XbOeqqBL2YPEJv/i9g4d5k+lPax565ng7W4eA\r\nvYqBdVvCM0HSS8M1RRn8PK0slSnEKocbReaoBJFB+hV9xm5qdiK4CA/R8wNn\r\nNcxBxY1MHV2ZngePBDoODsFskcALnd+QYNxSbJejREHzWRBk3msWEnPUwscA\r\nPQqE8cR8XSjY3z3k0LkSdsnl+N2pPXxqYjC/Se8+o/wpkqeBXCHhcEdLwHlZ\r\nqt94htnsVhJZcveBBk4JMTX9qbLHtq8bVcc=\r\n=Ix6w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "265f6ed2f53207cf54edaf2c83f13ebcdc5de8ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-W6NYKvBwbthleCOhza6m74AEeChXxDsyMRYLw3Q6AtjzC0Fe9xVg9eBlbpvGKFy8GpMJNufiTR2L5GFvOXTWdg==", "signatures": [{"sig": "MEUCIQDe62nrS3UuvnOksztrY0iQYfL1mpYmZjEHY13K1I0CGwIgPF1AdqbrLfPpRB97YP46otuoFLTtqEJTUqgnZrZHwaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqykg/+NZld2MTSgJc0t3av1O8sSEtVOuNOT3TOfAUPi6MECHVIooCk\r\nZzjeDydmNzOsP0sfLyDFvB8S+CcWuLUAqz8z7FJCexFl7nzl2wZQ8rYiVcqm\r\nKSq1D/ai2ZstjNqubAEpbG15Ds4Ix9g2t98pNcaDS5b8BJGXd9Hd9i+l7KeR\r\nuqmVPqg7hDtm+YVIfUhjW67i3Q3XG4UPuCH1hojWtGI9qxegvPA5YQDooUK7\r\njWIDh9x8+QGoEJRGYa1bYTwz19wEiGItSYO+fbn5A9mTFZBg8OmaHr5pIUsO\r\nRSAUkJ6czwnHy2MKVfbjGwSrkYq12vAgT7Ruv2vfzhDs5aeB0fNMB9U0ku+K\r\nVLIruj/5mdRG4EmrFRm2yHCazKPmtXUuvYcEPIRaDhCiGW5rdO3X5nrznQXw\r\nbRE2NlO8V3xe8oWtxAJ3+U4hXhDJWianchZqpga/nCjckxISJeh0lxIPKqRT\r\nguaPiCmeGSfsxF0+Cf/G4fVib3/WRif4Hw2gL1mh7Mo9CSAxgPyNiNKLuO66\r\n4qRzoeVyNSr3H/zerSKP0EcFOHLQa6LJfFjOrHKYT1jCEMdqov/iGfu4zmuO\r\np9xOu0ym+dHlhaCBKot4NcSsxXqHC4cWY6WbiHpg3UmAMHbpGV+Rv3pjzLuR\r\n2W2p3ZumwdWD5FMPICfzNWXHNtEoxg7bcD0=\r\n=GNaz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f42111b22c9ede465c1cf2b1d498c9ffd18d30c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-8XPYpw33svdCPK58c3e3vXOCae5+BtsypM2Yvuk/xop/KPartalUeGGpQ6wsJFk+xuyZ4meiuhOTDn8Xdjvqsg==", "signatures": [{"sig": "MEYCIQDcWik+l4spS6/+NmbB1cQCsPia8NI9kgG/Nuqdaxmj3AIhAKt0UT0H6EYKpmrqwESSLitIuCw35nchaRwtzkaFqxmK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7lA//WhBcrdqsL9Y6G/UMpSVy2nM8/T6EbW70TSRkO/3kc4k3KBeV\r\ntbZcQ622Bl9A9sB2xF9ZwbfIxfScGTraPWOWOP7gNcPJdS1+vS+d9iOJY+Kc\r\nnq0IVonI3d3UUnRy254ZT/hJTalorQiA3HZc8ZQohN8MQg6LQOMCDybzyqXY\r\n8tyU9EO8eSAM9Oz9oEYGK1PnQ5U1ON6MsnZBgyTmF8QTrF2HhQpcOSF5pfXI\r\nRyXxqI5GBneX8R8GxkUqTBm3hMsJVi8i6NxV547OCPtqYEUwBh5ihtLX4k2E\r\nyLQxqbpAHarmzOc0EMc7mM/G37LFbUHYGXCek3m+R76ajXp3hHWkWsxWxTAQ\r\npyuY+YSG1TzAxId0nEzCkcwdBoc8nZN2WUt7rJtsQlfWqNc+EjSgosnU/hhU\r\n0+7msMs6x/I7Tof/trdVV5/ERvw2dCjBEglTYNYU1B/HcvroUOCkIEfNjoB2\r\nvPC+TBIy4Bizia4xQ3vSgjsFOK773SZZ24TQRQXKsUguP7ZrSUatkLZ0MOd1\r\nAj2r6uXpuerFVBwxSy5WYQzz3rKddoAlwaQ4WaUO8iRqoIp5idihlGwId2B9\r\n7zkClcgmn/Jxj/j7VQ+szYV/04zpoVlEOaIL1RmMp/79aXDoeD26uWo4ohKw\r\nvQJbG/5sMQmJzwBQsy3g/omb0r0BSE34uX0=\r\n=d7Q8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "770b3de8fb03bc1a48f7e3564aa635f9696df62a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-aUOBn3gWiFYkjXdLvtPBwKlSra58lf5GOCkwXA9XasKCgi1siVW+ppqr4+hj4k50RuQFXNnaAYTnZxJqU3Cj8g==", "signatures": [{"sig": "MEYCIQC1dtTSxYJ0pHqht4mNSSGuK0XJUoGVQHle0qng3bqhAwIhAJD+FfLw3qMQh11raFJrzVeKkbKdSLiBfbdwG36ulLG8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6Kw/6As0ByOJya6A/ZRSrWfywY4sYo2PQm/Hxu/1+VRKh323rA2rZ\r\n02rg9R8CkXyepEhx/bCeMoFeN5on8JWzxLhH/izWG+Dm1g3kSHwA4/emS+97\r\naryba8UUhBxyDHzwKiUZYS3iDAJ0yPvxLKeAKlWyxcrh9q8te7Jwlo64B3SQ\r\nSywx4KlkpOJMoKQ/ezuQSrwWYx/Qp7t54rN6xOaycS4ZPyIilyHiM/469soe\r\nyZnwJE9yr9ThhiuLOy5GzlJ82Th8ATVV3LT/Gh9C6ucgnxyhJTxRb3ZqErix\r\nLwxoVyYVpsFxnQpftoBzy6dNiNCfegDlv8eCGQHdW0YDLlsWMEvvfjYKKCNS\r\n/Ux2X/oSkGBJwSwi17CYgnA/r+OWFtSZt5KWYBYndd4mfCGQ+PKaaZFF+Eub\r\n2XJxoSGpmIAtGnhqoGzQaEkdeaZ7dPGKGtyd/R5B9GgGlQvTKOKTvTCAr1SH\r\nmrfoc+9Yqk/pqFEhyp8ny/T5NY1kGgZRu1ohN6xt13qyOgEF+SaR06OXjuYU\r\nUqn+HfnYw2+1DW4yMZTF+/+RbL8JCmMoR4xfn866qPp15/5J2ioKAtXdvOmx\r\n41+/1OboOc9ft5nsDTD/ewS1fGbF08bwmxicVGavT+G2wnW6cPfiRQCHr1L1\r\nFhp5Q0wZfpKO8cI90Zb+prr5eHj9fpU9B9M=\r\n=xGNh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d67688a3e5feac79db649c1d2f5d87ae19ce164c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-bjWbSNyYSrQXb7Z833oe1ozrkGaxZb8Y5Abvo0oH/3ZN3AJ/iIJp6SURTFK2yuGmcV3PNb8QVW7Zgmfqd5rGJQ==", "signatures": [{"sig": "MEYCIQCx/QbMi7kuT8hdJH2Av67R+9DVh4D8mtwV9Qw1bs/H+AIhAKQI2NkzzN6tSwrE78XjywCQBe3l3zMiu+zxGYEr3bvZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmra8Q//XdL4oYz8nHE3Gbkis9113BxSBfs1z+8CmYFSe+CSEs+ewN4w\r\nPONPWvUtLeaVxrPM3IGQgmqe+nINTRPdGZPOzTzJm6hMSBfjEKnP1dYwGiBq\r\no3kzxp8Pgs7KgpWNOW6xhH2b9ZTR1L91Qqnwfi4GdY2QiuDQO573o+C1tpe5\r\nu5Vyh2Gs0bp2Nvhz8/hK3bxim6PPXsNKRv6i0XHMDbx0aBAXLyRufVZLLuLj\r\n8nkwWuSJ3waz5TAZA+p4LkYPF/5qasoSDX/pMkAecsl4lujrzu4kTPRvdX44\r\nnfDQktPoXi5yMwdPbUy13pfkZOIHlGbPgCFv0DFHPlw/9iI6VSL1OakmIkVD\r\nYX6tm+IsBmW6FTaQ4MAZA6VU4VviNefvepSGkV46RnwDg2DiS1NJeOgKUlYU\r\nW/728XZF9cQ5kaTFLndJ/IIIBWtkvgilE08E4lgPV7GSM9kPt2KYk37jYHWk\r\nykOixLBRUuAk5w5+JAkE/lmOQr9/1iZjPgXaK8eV+PB5tT/4F/0SeTb6voVP\r\n3fbLYDAUUwanOIFA6bh0wI6YuoDnWllw6LJysP2uV6ejiqQnN8pxsW/+5jyM\r\nDWj92l+WSBFg1ZCY1jEBti/bbnRqtuw1SHtXbs0tzMZCrvjhR58hajDPe5l2\r\nQeYy8ELRQlxTpWK22hD0QsE5pCKmv/RcbTk=\r\n=fwbi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5561d7b675eb944d631dcd6e3c112e593700b9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-ausnvHrupjaGaow3i1nnJdahxdi3+Tpp9R2Fk6ZT2LWeT341f2O4UX24b3xo6tEznJAlfwV4ErIChh2GvT9IzQ==", "signatures": [{"sig": "MEYCIQDtzJ7XEyMhiyCgQq7qtkqD3RXo7ubLSXCAkPWwq+Y5zgIhAPfD4G5Cgy9p4WKT2ckSAVWticffvna2WpB0AXTVzrfY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfsRAAgl40AchDoxPbD0SeyTI0idDGD5fgTogr032a3xNJIrYeRkeO\r\nSKeMgZSWiEdpCzaxqmg1mAIotZGk0BOxDJxzRz6nww1Sa2lfZA7MjJpSCmVC\r\n9QT9x+2ehqWdIUbRdufpmqjAeQLU+5BzU9sF36Ow/cfDgeIg2qoYzva7BzCv\r\nMfKgm6MCT5+nMgDkmL7QU18v7TsYsq5vFgA0mi4phRs//6i9+GDmDbA0LOzx\r\nQuF/y5riw+BjPbshAJEFr91/0KGzDaZzsIToMByKSNOfscUx828GLqCOAHQV\r\nOCmY+lMp0i7vxiB5LL7EgpXcUQlLvghoRh9UJEsF5kG/vogQ0Qw2ugPXSx38\r\nHyjK7aOnHkXloaQjdnwwRt5eLTyMZZV7XY76cj9fSFBetBPGrUAZqmP/8dvp\r\nRjOUx5g6I9Bn42BWW9I9zwk5R2uwfxzR1Bez6NcVulvePVnppzUmdINumZwQ\r\nF2Vqn/z2110piV3hX2FxfACnvnqre4yaeFKrpdOImW5/qbT0r/DiQpGjc07r\r\ndUIszrv0b45EBViiSqmKh6F1Oim1UnmNONNhC/krDHYrk3BpKY4vleHRsn7F\r\nn5pSFwawPg9AnEomRMZxYR1JaUZZr58Ic/D7+7Qb3clYJU3p8jEU7PyCoPaK\r\n+EFR3DM6b1Yro6KkmMXSjZIxlpcJrgHoIAo=\r\n=uiYB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-popper", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9eb62dafe615339c014970604b680e8b9199585a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-tZXM3Q+SZsH0TWQnBosGMtdsFE/vnTB9Gq14AmghW6gpPdvFXgcpl3VhshUNiRYswoYY9kBQYmKxFe8j1aOAJw==", "signatures": [{"sig": "MEUCIQCze0Te/iR7E3Cu0W9ocaOukUpgmiGx/wjf0wkqNaD3ZgIgGcDm4Jep+PEI0T3GCmrHz5lNOInrMLVr5Mu6nLAdjTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruqA//Z2ZEWgSIysph9M/9xq7Ba0X8sOibbc2XQzGS0rG5b9rlfTFw\r\nrjKC0bcBHsFLflAiPtxN0lVh3GCrFv37Hz4mPXVovbQWleG7R22TfH1wO5Hj\r\naRCChX8773kKkV3E4luqNhsmACFtqAguz8Ke1+tAF2/0UyYVFM1gI8xk6VWv\r\nPt96+TjTd6QEJasA169zNEciMci30flIAf6Tcco9LDEgaO/QVIiU/YC2Ly4Y\r\nc16qIIUQK/HoJUim+wTYkrmycV7zg6MQWjmo88t/uOuZfjEeEz0ucgqIXrWw\r\nl15EcoTBMTuRRd85KmY48T/GHBgGXT2g+zSl8SLEqpP0KlKE2JRXSRKnwj0R\r\neruMEos+ZmN4rBzGn9MigYp3iLsN4N721nha7THCguNnqJ4F74DhO8u1Xtj1\r\nkKDmYPyZFgIUbthUJuaJ1JxS3Pk5di/nR53STIEE+S68NO6tTv7uXMoPoLHu\r\nKmaMWLGHDunEgfD3z/gXr26lRDVbfpgkzGKSrobA1tMaMn99vF5cDsPp+fQ7\r\nJI+s4D7a9/EL8dUbY5KqWzpGItAWVw5WvnXaqKWwJIz1McCRbTJveES9UBvs\r\n5Qid+oBIy2+eVB5whL8qyaK8kjCRlA0NefLaSjkwLRvcKuR3Je0dt2wUvdqE\r\n3OurBYU2MpHfXnk7UfNF9ZluEITl15W9G6c=\r\n=C5yi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-popper", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-rect": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dfc055dcd7dfae6a2eff7a70d333141d15a5d029", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-18gDYof97t8UQa7zwklG1Dr8jIdj3u+rVOQLzPi9f5i1YQak/pVGkaqw8aY+iDUknKKuZniTk/7jbAJUYlKyOw==", "signatures": [{"sig": "MEYCIQDGXegEVzHkdpVtzLdYRLlm1rpKUq2mfnnAjCQyd6OWKAIhAIAfBqFiSCqYpDVfuCypFATSy7Vt24qwGnZhfkYktMB8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqnjw//VkXGY9ELqJg0+uOb2e1/KyeDpeGmIToQ581MAM58T5XUaSu1\r\nPA04Hr2l2zGMhHwPzEaKBuYXKoXgDYGuA7ukfTmlk0XHqvuiQXeNbYPCuJh0\r\nRg5gBBkr9lDLnYd9CRbVI2Ye8cH0RJsePxv4xVTpkWMMa0gHjg34Q9HfJN0u\r\nnj9MppgzXKMmEVpIhLQeyc1GAQFcM4YO1V8ybcx7EzpQbp0i63E1M8O/2Rw9\r\njS4wffjgKTMuEdfiMVhbLgIVXRaBjC5kqgILT5zsNi9FBtGidDHLTAGGkWPx\r\nIhQ0WdmF7d6Ka2kSPViMRFeY2bqW+KKX09VJGpsNb2JeGOvLLpA4Ez0rB5Sg\r\n6v3R//i1zHlPT/LLrqGvZRArwlzA8H9JD+B99k6ho9siO+72nppzaobCu0jO\r\nTiteKOLMSrNmRA8a4zZdVv8xEhJCO/5RKWmHddgii+XnqQjyRHsywY2yuyAT\r\nkDJds/NtCZDuYmQKXuCoOSX9yhE4KZ1hR6GwRz3EzrCxNBjBf2uTo9aHpkKR\r\nwY37d6TlgZC6H+9KYqEjAP3DNRGF0/yVfm7KIav8+nAUepp6+UsUJHfWH/zE\r\njsIfz8eEkzfZ29qBlS9SwGrowuz4ipm8nuClEdG7itZjm3Dgoxr8W6wrDvyC\r\nad3+KzCTPd3KFp2nD6jVrDxFdMNkdW1nr+o=\r\n=HWSP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-use-rect": "0.1.2-rc.1", "@radix-ui/react-use-size": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e05783566464263b9d54ea8f9f3a4bd5a24e8ded", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-qkQloexmDTRcnIhpl7WzfoZaap2gYqZoeuyeARbEvT7n2GV4v2RLe3O1ISDsTolB84smLxrE+w4HVSBCcgqwzw==", "signatures": [{"sig": "MEUCIQD2yzTZ7LCOiVqLXNXCCu92j2FX8sUSyVav/KZCrDX3LAIgI7C4mRfS95H+5K2C//xfpiB06/AxWMIFNo0Ek8AZ8uM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAQ1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTJg//RZIfTus3jjk7Ud8J8xulKFrTawo3jbWlcL/48sYTxpRMazhc\r\nvNzEFyfTPThN1zHyIjMLXI14cmUh7vhfm0QsarnX4uorqZpC85NbNNBZXHLl\r\notMsMUn/HRibjj9RZyk2nWPPIzJfCIpUQwtd6B0qMwy1K+CRqvRe+7cnOuTM\r\ns4EdoKNPiKuufbM/vXDuKnEPEL+gdVBgvw6814L7z/dMeFKDj/VS/8AS6AnP\r\nCKN1MWWFOEnV+ujrMQxmsTWAi5jVxURIP1FiLEEtbKdFQwUXScDIJt8htvaY\r\nK9RZCs2WGPN8irksAHgaj1bN/JhywMlEapmjawoijSqaPsyOqZxaykPVBcvj\r\nPqi2KX/HjhOk55ESuE/XByHnQ1yH8Z/JPfAVpXUA88iAo040FezHZz+dlGuF\r\nbtjzwMWCvOlAjiwaqr2gSkGae/0sKb7OvjZEXV2rFTLVDuTfBp4k+VlJk6QR\r\nlkaarCPBJdwhLqQ1g81cVd9SiAo48MxbFe8xtqThbM0HRhF2bYPXe+dt7hz4\r\nCxkq5ubcmq7bsQgTzy7yV/RsEWCMnjZMiOvmjtzZHpWirM+NKqkK/qjX7tIA\r\nTjdaWo6nZc8Kc/ciVirnlswbRqn7Mzt8vHGseQHRXPVCqA63gAeX580iQw2X\r\nydkO4jn4nL5GQx19kvZdwQbnzJd0MJpaxqQ=\r\n=YqYH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-use-rect": "0.1.2-rc.2", "@radix-ui/react-use-size": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b1f36536ff0d9b1f1ea674844506ec8eb75b39e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-T3Jg4ZdD2Dn/5/KMRnTRaZPb8GLHnPfeGSl15qVrPLKQKJdosINENIsjmEv2prvTG0BfG3FDpVPcFVbEV7b1dg==", "signatures": [{"sig": "MEUCIQCyfpL5E7QCzy7/6Hdzohi0BWRdtBYELGEDD8YcsQOi5wIgUEVQV/jLBDLAYwiTwOo/EIBMimbNm1UAYFH9OG4Ze2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1Ww//RZ14OuSD0OaunHuuKkuxH2woUWnlaGIcHBkDfhnLr9FH5CYx\r\n4VNvieNmD4B79reaFoHlwDKRpln+nco0Pw3ne5Wn/arWGh2WLpSAhIsa2/u6\r\nED4F5GJVGxNLANxPUQ5Q0bW+Y4GFE2E+LOt9G+5BI4GtHhd5n2TVb9ooA2Ci\r\nbCK7Io3HKPMv/vJS7FaW9T2vUtHTJ8mikd99hxGIQzsbT0UTGwKMmx4LoRTW\r\nD/bU0tSUezdTZkSV3H73ggVYQxQK9sNUgHNLykHUkNnn3XfvVspQ6213ObJ8\r\nLvlbyYaUyIHaUeD6uoCN9zyAMaFkZrQQ+fMqEeqpyLP7ygPJPBpyTbjfcxrU\r\nx74E0YUc6jUgdg4RgCod2uJuWYL0u3RUGwuHIMDHs18Qv5dnw13eo0bE2VXA\r\n4gTVusrNzng4cbieQzajzsJs2txyFVk8iCRJtUu3+Fk8W606eDMkibDJZT3J\r\nGwodEyrNitisXykc3erDQlJEYGpw6yHxcpR6YfCp5x1PKqcIC4qt86giQad/\r\nonG3Qh1Hc7XbrjpF8OSW8uvUrogTw/EUHGyjDC13uLtURG4MDikJBF5R8So4\r\n4ebyvYL9O2SRtGlvIpSA6sTmprgc5TrgHTppUtOWWSGQYXizTu/cXWbInhyy\r\neSmxcxi16tM8shp+PzjbgqZSNU0LQL6buwE=\r\n=qXBY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-use-rect": "0.1.2-rc.3", "@radix-ui/react-use-size": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64c830a6dfed34b4ff2e3bd0a32f6effe4e1d653", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-m/m6RdFUvdwStuzPWnho2ZBOMu+lMeqxm34SZLKqCWsuyTLciGQ1Lw4G33UEYDr+uayU+cqDA+PAns2uBLaMYw==", "signatures": [{"sig": "MEQCID70OIPUmG/rIAzz8I6ukFurVxXIqLzSpfTjqkrm8vnVAiAYuqg9UHXIhC32RowF+BMkn1L8ucG0HJOL6K/hTqX34g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIBw//Y/Vzgxfts6PU7PtfXGrhf8Uw0ZnPSMM8WR50JLeaWNLcR4oO\r\n2i8n8u47Q1EaCDsvQI/DQXep73FWMPw4Yh7jN17405/99C12FVo0i08Eh+mA\r\nwELI5qvGBEE6s/k/A1z19cWQIVw+hYk6FQMcOML4Zc0UgBzSGyZYL0rjHDoV\r\nOCLWp/SF29KWtQAp+tpLRezmCb+EUHBDwafhvjmQVRdYVucxgAajMfYSNGWW\r\nbvjLWXs6kYecVCstPjv9v+vPHfLkoiOTAmMMCmTpmQ8upDRUmPUvwiuLlkpo\r\nQQrrU1x37bmyp/2gE+nQTukl8Jw1gt87/pFKcx4aieag/eTAlhuLKojzn5fm\r\nyfyeLJziTjgD48zkl93ipqoZMox1Ln85y2wUD3c2ZoorBUk0IhdDpgZR8eaI\r\nHDyvdmhC3JwfKpQvcB5HZCcGhoiP/ORcbIjriVdShnSq7Zw4ynfzcJQGX/8w\r\nXpStSHTh83e9vecyPRhaFKtr2uSoYQxxTkDQbzZ29aAjzAei+uV/uFapmvjb\r\nEE2bgrxX102vn5MaE+EGcw9pyIrwAr7h7KbgG2CWv+c/jSqLrzABDLCVUtuq\r\nTZTyEC/+Sqaz/EOG7bFGYJRQPbGk9m1AGFL8E6jC/8kkSlaPWnXnh8G3rUqY\r\nFzQSXejMG29Q4sycwqXyi5mj/ZIlJ3mv0h8=\r\n=2pvT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-use-rect": "0.1.2-rc.4", "@radix-ui/react-use-size": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a0cbe061677b3171657c91df93690b5108a37cc9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Ow2qM48cTMHgtyJP+utKtiAEkMhjdorAb00LyU80edBEg9VOG33EQ1+pDkqVmOkjzegm9gdvYvzaVtfvDACn1w==", "signatures": [{"sig": "MEYCIQCmi1HiY6r3e8UIy5tVkpxY0ZSfBSWUvUqndyYWWIjgkgIhAKa3ZHc/ZW2jjEGxpXM3TbBQ7lcdp3gBWKzhBaWFwxBk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqc5xAAkbVLQwF29Um8DBAs6DIlcLEmB9gQ2hkEuPVvF1TeEW6bjcAZ\r\nYeabZ+fKR9V0M1ACmpGDBlrkHzIsYufc1u7DkTjgMNBzeagRlOmYm6w2kLFT\r\n7i8sbkNxUh63AU963x2WjIXdQ6d03Xx6ZhE0K5AEK/3/4xXCgZ32bA88F4+F\r\ngSdf+AIPFUdjpYz6BINglqbBmN50PRBgYtVXmG607T+YS4uieHhPp/xEklLH\r\n1TKT4N8chmy87k7hJPFfdLarEsIPi/mjyfgXVo6oYb0AOHj3pCYpQkBXBjKj\r\nt3h1S38+jycR3coJ7DecCy1gl8BEh5ksEgHZhJo4TyFWViuUggbSIosqP5Xf\r\n0rabQ3Kv4nEzqPy5Br9pXJkjic6+ilwCpA8RN4UByzLVy2ZZ8kDgkLgxy7dj\r\ngZYkcMs5YGM6I7Mg+p8kQYansdYLW0uSh8ORPTB+78TM055UMyQNMFSkEf+y\r\noaeY+T7ikspOs9AdnPW3PctPKqnwIQEx4Sc+CY2KbWOTafes/ocC1gk/mt4f\r\nvliX5MVGb/IeGT0W3aHXKu2x9qnYznjdEVxNYZLbxdiGPP+18PqTniRws90w\r\nUuoa5fHhwt7paHJzmCHAWLUwdZ4hKjsha9+PcWhvCjFot8WBFSmhFE2l5gxt\r\n0vAIZzdj8VuiirAQLPWDZzgfusNmiDE3bJE=\r\n=CQsd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-use-rect": "0.1.2-rc.5", "@radix-ui/react-use-size": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c2c8a83c6916eded049b74d4d207250590495037", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Dmft3hb0C2Hw6MMZvmtqypFgVB8Heuv2uMUASf57IBaQ8nylNhPnd1hr/clPuBZjFaP19e7pIy/y1tkouuZe2g==", "signatures": [{"sig": "MEMCIAaAv8o6Un96JUVcVtH5UC6Sq0jZMYNnM9lOV6d5VNVzAh9nROXYwwbOsA9SrirXhWoLfM+2v/7GHlrU9Y355TX3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWCA/+ObNJjYfAML5A0ZN8C/qSuRecNTJum4YpKIXCbI2ICY7ZsEXB\r\nP8gDKxNg9kPZJwF18OsdJxDZQjzywm1pZ00ugf8SJbdQs/hkLo7FLoOMcar7\r\nQPzpHDRQe+HKxa2sD6TXiPmTKYqbU5t7x1aGObETtFUKhMXoqg4bk7N2cWW7\r\nSvGsfFHpXGbibyxbKPh1bR16mLnTZV/QF6diHvA1COTR0+U6o5dwwogKOiUE\r\n8+dwmf2KHxul1V/HIJzrnYm+aM1QstKAuFInYf5VNI0E0cWdd+dho9w07YAg\r\nc94gAzWJhJfswiQobKllwR23OmjrLjsT1AHslha7yZMRKtdVqWOhWfxF7sQ5\r\nWGG3PeE/5zfh7oJGhUklY5TE2AN7Aq39xPTd1XV5tz8kNVG0XcYEbnkBKMj4\r\nETHTB4vow9d6HTZcIMYcUsTXP6AWiKxv1UtUIkQdufPvSvX6jhNhnCP0eTy6\r\nJMiAYxhm+AYnxaC9mKCLAZg6Gh0sUpLN/y0BOpZ7DFpRQXUhuSYXojpxLvB8\r\nNKgNZPprXRz91g+uq7pt67GQcKfDQP7eTvQSRoH8bfwfjPc/CJGrjUsXoJ/Y\r\n979QBe3+OvSqzAlnf+wmhgwFfjvDhp3Zse5IruWMPnDA43j8pY9CjS3a60q6\r\nFxb/h1Hv72Gx3Aa8W53EVvWNqdFF/DLM0Ss=\r\n=89qK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-use-rect": "0.1.2-rc.6", "@radix-ui/react-use-size": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7f42f1d366c8abfb28793a82b36f22ef69dc89bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-K/Lns4z5zYOMHx1a6yMc5fJr+Z2XY06BaGh8Rdw1UMLA2Xdj+RTUR7WIT1EZ1EK+AQkEkdwXZRsebtld1V5BzQ==", "signatures": [{"sig": "MEUCIQChn1oEpMx/YtWeDHsykWD3UbCWFBPfQKdRlS7s3Pv5PAIgJRromxIGVS5LDHv5KsuooOyxIDAmTpJjExME2koDAeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8x6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0xA/+K36BSTgco4da9U/m5B15aBoqtIEHocuypnPVq6BgDvmp7/ps\r\nSsDf3c6bUwPYuKIy5bVsvwKyoJ4mH7eVqbvlfzzuIpjL7Iu5PqELHvL/Iy6N\r\nl37saZREnf62edrCuLajiEZ8ax++3dsvL8c/7VZEBzfct0K4NBrDaD6BsVdY\r\n8AMhAk4lvyulL1cvwmPMJPPtytvorrHry8cxIjNFHOD2cI4re1eh1lZm+x0s\r\nQ9C6csxsXMAuwVosuSj82LsHowdqlohSnidKPjKaldpTY+9AehifyaAxwRg3\r\nI+t7di8JQsZPC4O4BW1Y7+7lDwp0YYMzBIco/UvlSzrOrOqCsVAj7F9H5DPx\r\nonLHmdfM6NtQTMObL9gr87KYYCftdVh2FBOlUb9tW8iRSVDirhIqx+D9FKWF\r\n5fb3sipDBoNGU7NyScW5isk5OFtHZFA9wjmHEJbxExxIwTnSWGtFM6zQTgEj\r\nbr2gQXkpLX6CClxOlXnGR4jqRv+DPDlIDeedAs54aAR7bDufD7bw6wPTDMxY\r\nukV1KGQuKu6iJ860S6jTH5IEaXmNOaz9lAff/ExvMMzyuXEaQT2UgZy8XtYz\r\n/tPX/PMtyTYAhU+xorg13Pr2rKG+ZnKUFfbz8ST19UQ35VqstIfmypsccxUs\r\nHy16p2mciibMC3k5ahvNqHWRonzYUMTWz9M=\r\n=pPTJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-use-rect": "0.1.2-rc.7", "@radix-ui/react-use-size": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1781687899fb2b9d577bdeb1877f2ad36559e4ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-UkTe70N2f2LYKPiAr+nvd9o5ObPfOZXv0VR00hfxa/UqLcB1FzzmPkeyw4SmUiY/cXw/qaEHlKhomH1CdhCy4A==", "signatures": [{"sig": "MEQCIEdK3R0QsxRyhKQB31z424qS5+FzPMlzoo8HinK3f0eVAiAQEMWAfw7NjRbrYXBX3kzXht9LmCel8mxzsSDDT1pTKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr52BAAhysd73TaScC4kP/8HNkDcnPP2Kea7jGE6OV544J5KbzGidgh\r\nojkIwmE+FtITXXK2oZg4DfyXxrfxgwz0xaJZXC57h7gVki+Dbr8quCs8DXSx\r\ntkFdBMsrdUgt+2vkfVAkCK8qwbKHKeFaHcp1tmTCUzPMzhukdqYEOvmFljfQ\r\n4mRVELVBK9Qyn/3erWSacKfDTZWvHUyQFaTkjw1UtACZjAO8pVzs4Nlk/0+t\r\ndDlOZdwRjjY8FlagiiTKb2nLLIeO+mfgs6LzDaZI2ApwEnq0chi8GZ37KUn5\r\n8WOgOcBk2ZWbZaKMcHErSvSXd2fkf65jXY2G8wGJuAjiOHx/w4fjH7D6nVNo\r\nW8OHPaTi9gc4pYsdlL1JRhfvqNCVQHlEwMN6hpwwCp0u9bl6m9ron6lT+heR\r\nUQf+6MZMBu0HVuolVKQNDYfXmqD8nCu0G2V6FJqf38o4O8kpTwfpCKkLskfr\r\nBHVZHCYGbBn+/O+n54hAdHTp7J0zmcFvp8RhJzIjg3PzClVc65mGuDOkdPWg\r\n0ED+CBws5bod0SkiLw8DE/OCalc1r7nX2AlPuFBePNBZG0eu8PExkSqdI+1U\r\n/RaJGHSjw0JOLzCdm/kyXtZHn2xF+10E+ry/rLcSfSE8uPFnG1GajzHlPfHR\r\nGdFuONXrT26ggpxeb7iuiemIBDq2PlDqix4=\r\n=JqGo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-use-rect": "0.1.2-rc.8", "@radix-ui/react-use-size": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c5e2d7fcc7345956b47887fed361dc70e7b4d3e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-jSIPZPEXp3jh/RlAuWN6Iu+SRFnFqxvbwYz8nYF7lkHEWqrRKqy/42HDyQdICVj+G3kIKZccchRv6Ot59CAKFg==", "signatures": [{"sig": "MEUCIQDHjrMvrM7HQw5W0dJbq0uhSWDU/eIQGr9RCu8LOelm4QIgY/BL4TrAi9smvVzy6NCgbAXHuqGqvJvpVXRmWefp75I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvvQ//Vb6hl8qNR9Lu9chEg+8omVcaTOkxCm/v3/zPiNBot37grVMJ\r\nejrB6FI8kHxeiD+6axZoMkMsVZr1McigHh9ygbXIJRYVjgvPuwzUlq4ySTUw\r\nGXgubXmbaoINUqxm9axcOqCGEYzVKNOLogp0OQaV4vwa05XMmO4WY+wCpukI\r\nZgBVfbVNDuFpsgH5+5YBmevrmnsbdz5wreyZBHbGiPNMhjcDDx95E2xREnx4\r\nCTD3gxhc9ZIoP31s6FDJrRMfEmGjr1ma06iiIcBohhpuPkzWnG+855X/k1Aj\r\nvjdotm7T/9Ig9cYXZR8ofYUEm37DE9aqpkm0/bu4ZUw8INxPzbvl8nz4gk3T\r\nA34UAblrH4GQ1MkCY7sozvM4cfbatxc+DQfGE46HgsKTwTYW8gN8X3TXrBF/\r\nAE++Vuv0nSyz14qYtvgJ5HzlrPQWt3L6B9iGRfFlBrVSsj54HXt2F0R0VTVG\r\nTStNgKmmgIZXFkBPMDmtUxHSBVEpo2W95L9PqWxa3BWhKxniopjsd5cnC7BW\r\nqmKZ27U0t5Hlc02NQtUXZF7iL88CfBISQWiuyNVt0rkdUi21B5/Ag0XURVFK\r\nTZwf6Gi0LC8Bhrh3qYCbihY8hlkBy1WLGSpJ8Qhp7dJ7OZOFDEjk9RqSqQIM\r\n8S0ohhncorRbaxmh0mU/ls+DtX4JLKeCZRo=\r\n=gxUe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-use-rect": "0.1.2-rc.9", "@radix-ui/react-use-size": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93e88c7e08fc20472d05f4c5fef9df4c4aad40b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-93yA6gp43nHGhQ0wFU67TDseaMt/SkMVI1ve52DLQ+Ard9oJ23FJ3jX8ZKcbEVyZYPiz+MAnoRBrXpahVagGAQ==", "signatures": [{"sig": "MEYCIQCzywVIvJvZ6Y3yNox56cq4pO+aSWkH1HOn/1FjsY/ekQIhAONM7BKBrWpZ0E+8LAIFDm5F9e/R4X1xR1dygu2ZmTWB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNh0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1DA/9FdGHZv71MucCJUbABmVeq3/1w3MDYu8wVmP8XV63CFmtWl0x\r\nr+dVYEcrnsGsolnVVK1mUBeXiYk+71r0x/srhtMgfhJlMkYjtOjGiDBdEzvn\r\nxyiYpp8cB0WanBc85yOV9ZTKDJvRhzWJ/KX4oMcyKAuuU7vAEYkRoJ2nLbT0\r\no1QGTVSU0Chwb5VlzQGDbAmdvIRzvJrZ5uPEhuMm8riOaoLqbouWaJm6rjqg\r\n/C3mMZUcIxtT/ne3rMgt0IbsbQjx5olJR4vovbPdsrOHG2TraFKp/d1Ln7AI\r\nI+QJkgHHALmJZDTSePueBMB0/uYJNGhbUd1r23/uKJfeT2jyM6QqSzAyc9Z9\r\n3GQrtX+Tyf9j2Mp3ac4YaRmSPjQVPbCo5T2zYKIjPVwrFcOvmbwh5bbaZ6lX\r\nK9PCsTTo+eOJtDOTSt8IPlcHctlBqUHYjhXlOkrm/KDjdZS3apbMtHN5lsF1\r\nVi3zYmLzpJNAVO+dZKRTnJXB/irz3fR+PnwGQQ0E0mrr+Utu5oiYMkMTSc+A\r\nMlShYXHL3Vb0JC9KHNgVYp47anmZRQvQ/ibtPo82B3v8y5KsUmLoALWcr10P\r\nIp5vJKEPysBtXC2Mbak+8rUPDjnZtfNQfuLOuRm5O6pMinupUpzLoVMo5LvC\r\nmOz1UrYPVuwymsX+Qx3nEMQTH57EpYtlT6I=\r\n=r3Nf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-use-rect": "0.1.2-rc.10", "@radix-ui/react-use-size": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a33102d6d9cb473605229aa9470e275690825c9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-jWYE6vFjmO5kL+Wm4mzm+SQ7GGDbhnMJTAgfKIQIGTBVlzP9hHFjMR3XYidk1pFdbJDz1q/EMsbFcltw6W8MIQ==", "signatures": [{"sig": "MEQCIDb0xRgruDdlrtxRSbjjiujfXj3Ee9CRTalyKcGSMeybAiA0aHzuUmIPxlsCx8E62K1lCdIiPBZcSOkeNrZE6YEgtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVeg//XIue2JT/6r0EeciRHvZwkMh47RPnhFtgXXjaRhifukMKgt8v\r\nwt4Rc2N78znhS+hdqjTp5B2jnRH3nD9zoZkWjXzCcT7ftzFlXqEDPxpe9hLu\r\nFN+wufjC8ME6wXcRP3hpH8X14bpZb3SqOFqNaR0UkQPHT/Sw7WeNvNs11rTD\r\nIYFh/uCm6GfH2lx5gx1thMXXjmV8CGqATkrU6v6ow+z/OFXCcIHaBTHiOvAp\r\nORwVZMaYBH+71oan4QMCx33tYLy99h2ggL5gxPmLiqlPIhqcMYsKp6QmdZBk\r\nIMA8uCUBw8zaBLPCNv4zOBZ1YfczzqTMjMl8bO1JDh7U1MS05Su99nWp4q2G\r\nboSKXePoO9s62A+YdwCXo6oJmTExNE4fcMw62UKxPU3TbMo52KLZSbhHjhyd\r\nYzWoB0EzdV8vUf3Ak1Ni9xTfkKsLOCgZR5CJBUeEs8mbTTGrLdLki0Jt85ri\r\nsP2zOD6JTLfoFD+lep3K8UMHweyoOCRfAke+oUlr/hjXzuBj/UWFjCA+f5Es\r\nTnoEZiBvV6u8wkMkSaXxqXfsAAwiD6TrFLLpxRaxpMDGNXPT/CKJbY8++1Zn\r\n2QVxrnvz39Yd6+3JKxQDxHSMyQPlnDOn+2BhZJCmQPvLrzrwrP5JkdafGhSO\r\nY71MrIrME0duQfOvHQqhT78BX9pl2m1PV3w=\r\n=hz48\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-use-rect": "0.1.2-rc.11", "@radix-ui/react-use-size": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e65f1ba66455bdf4e6077f06e08ef9a5d31a7499", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-KKHlD2vsmBTxZEcjnCq+VDvFLxD/QBqg3HHkVWg2gYZkBJhKwSuTlcAleaaBr0oRQxL0t6vtAuOVf4MLpPw3Aw==", "signatures": [{"sig": "MEYCIQDCPjz0ro8HHh+Pk+ubCJqTK8WEuHxHqKMV/jPrU18ruAIhAJ7GhRARm4YVNaSXon5BCz5Bo1zT0C0lqRyw6Qwsx+wt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59915, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvNQ/+IBi1Bkc3RVU4Pv4mPAyzOPJF/XSezsTy1B4e1WrZCyjg6ruu\r\nT/pJeOcBvjWeTROnICFzRF+V+xIm2ESRXUfIGxFME3Xkske9NoaWijDUeO9h\r\nJjFR/Ebh8jVCuYMZxYmgRLRjDS2Fg3t6JKalAVTtc/Ky1a4NBy3oxliKoOmE\r\n7T+AtEq6aGvKmsO+YF0HzdPF/FI68aNvbBJ3y49o+WHjEJYK5MucbeUIOQaz\r\ntDBBS/GT08NGqSIInN8NSMZ531VBlzgkUwZrgAcw47XRvZx1hs2lVFyrrs70\r\nu4kpLFha2kpMV81vwd9hnXdyYY06Yo/gIDFvyHvjq/HmOQmQml3yENeWsVRo\r\nwfkR60AablZshAtj1FmzgwAgePsrphOLifaxLJWnFJuSUGSS25y8RGp1GQ2y\r\nlENmlu6WOQZgncAum4t+By+EzjwcJsUJ3zCo3oOAr2aAxjvFIj6YVDJiDpWs\r\nAFT6J49RkIQY8uDwFtyubD7CEa+ugb/Ur00wO/nahBBHHPgu8jT09YRZe/Wp\r\n1K9zJHikhAVIsHbRrT0q0/sGTUMEliDdJnb9v1RKQwg+YEzQIGJZ+6KnM4Lo\r\nCdpKD0dL9B0dkWONZxQ7BQtu6nVubETjF8lrCtexcTFKVMHRx18dWtq58H9E\r\n3JuXQyjFkYz1Mx/dibs3iOAbGTjrLUW4HP4=\r\n=Zcqw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-use-rect": "0.1.2-rc.12", "@radix-ui/react-use-size": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c2319e0db69703b0d23e1cdc49b94bfd86358f39", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-hg/AdwEioSq7TJ+YCA0wgnwiU+qBA0tV8ACJbqbdCIAW8WTNwLKI4GTM28Nz3ip1tLDkt+7RANHItJRIGT3y0g==", "signatures": [{"sig": "MEUCIQC3+Z1V/Q7Om3RmHGiPpqI7wwxD7W9FrIaqy6D/RsOJZwIgF3PK1+88nxYJuLZlrg61cpqK/R7ZHfdNGnWiSLhUBpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpknQ//YSxRagNsKYJvXtPdum82Wfz3cJxaN/ezbjoKW+WuJykibC1Z\r\njuztk/jmw5MZFlOacinzwcwgWn6o/Ukrd/75DwioCfBoZU3BZ8hZ7SQHZ8LF\r\nXK4sSNnJg7EARjYv6rdiF0Iz4Y1+CM5gwPLFnZfKQvEkawyrRNYuHTUuSkS0\r\nLgkABs/McIoqUFGNFm6Dz/uk4Jg04Y+FzYq5JTlwACusFGPtcDx5ppZ9rpqz\r\n21f+C2V//JK3WNrTyteRVZPWjh7UgtCA0toEBDoK7enQWfcJjF4Msk6Se5+K\r\nph+vDvjjfjH2pdwnD+NRrwtF7ZsI9zhJ6PXdEIOZRDxuA82C75s7HS9SuN0k\r\nnurGZaHWiLBrV8eNFVN3GTGPiw5E29XEQM9ifNs8hOzJj00E9RpP9Hxb0gbY\r\nLXe3QZxUTpHzQJXjNz0ZObDpsuuMka4V8QgrXQSHP+YhMPQr5Hm5GFki2Hum\r\nPRASc6qoJvm8H5UYVIfqjftbEyL0iux/raLdLRuRisu9QglZvKFPsG6UGkWB\r\nyv8g7M8IkiqMlBsCYkePQL7ZfP7Gaj3WI2R/JUHijZkEyuxJl9lgPiBWfRy+\r\nOrDbtpvU0BjiurQ2mSe8M/pUSn3BXLtBWXYjXoVvWdg8Eajrk9v+rEVd2oK8\r\nb62YcuPVK+5O9+NVfX6huvWwh2KVNmAZNXc=\r\n=5HIK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-use-rect": "0.1.2-rc.13", "@radix-ui/react-use-size": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6b0c81206584d48daf96413de217e55fbe87af60", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-uF1J4pC9v9vQtfM0E5rxn9KZxMsKfEsJBui6/64K79IUC9VfYpPxSfBT10M50U0KFlgTcyIeh7jAdqhN4IaouA==", "signatures": [{"sig": "MEUCIQD5Mn7Jz/jhy4oGgDsJudnCzEYHNRjpYJkE7f93AXNaYAIgS1w5VQWiOnhnZ8pQlX5LC0i58v9IVKORev4sZER2x1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHXQ//Z7B+VospNysaSrAhCW+NgkwSnMQAyfAuhdt4IPESXWjIXd2w\r\nvjywvOk4q0i7hwqeQ565oWpBjJeXDFlLsGQtkeod/6Op92EHvmMzxdw6wYf9\r\nZzgWFi1Tf9kK9q1ogZyPI8ap846gBuB02aALWXdP77AC+desSGB74gJqWSpJ\r\nDR1Y8/6tkhjSJYjDdZFbS2O5R/zJ3h810CN+CsmVmRXpGbBQWKPad1te6WjW\r\nPehdhVeoNRNuMjW2OBqY1XsL9+VdTnYZWGH2rX0tOEUv/ntasSomrZiX4AVs\r\nLDs0+04pJfzdXOOY7J7DBHk5+shGFfzTJ/gZPbkrzEjfsTNXDNn2jKEx7P19\r\n0pD/iSjZbhemSledjr+KVP2WY7W9RfZVg9ojIvMvSz4MOGk5TfXo4hAWSQe9\r\nXGn9iMPCdjoM/lbCtXt5iC8Lab2tWXnxn8VKsA135WCwXRnhq7CUg3iTs1rN\r\nms6AUWlndcT7ubfoYPKetzCQ2dUhU8ktPa1L4H+solsRF6oc+xtyFu04levh\r\nlI1Bqzp8ljx3kcs/tuwkw9EEMjatZKjmLjSrFy9m3xGPShpMHo2wpLe/UojN\r\nj5M8ckfFCAiG7r9J0PFL9AAnkAEN70qofaewx6WHNdbURQ8UFiT3M0RA2GrA\r\nf84AU4UwEXF1gZi30bdO5ZWOvj7Han8abk4=\r\n=c46+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-use-rect": "0.1.2-rc.14", "@radix-ui/react-use-size": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4e33d3fe560906aa646bf963fef85a455cc1cade", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-vN894pTr4akcOoTBw/SoXHN5+XdtuzW1uI2Hs9mQr15ZO6d+4xK/1vadiGiMH8hCOeP5MLuzNNdWg2pLoaKzRA==", "signatures": [{"sig": "MEUCICp9mSgF5nSOytxXVSxi7ByTLsH3339YQm3ETEsZ/VhKAiEAw2dHDeSyufIfqhFq8KlOe1ulzxIVziE+n9j71QY4rww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6Rg//VE1x3goQ3CK+KYTV5HFa0caSbThp8Yf2iDn51w4QGLusBye0\r\nexdqZ6TSaz6OVKM4jtK+Rhkn7t8EoZRwRlpmGFNZ3IpjwLQ6pNeAHNFwrrp3\r\nkIuAZYwEIzEgdYk5TY8ZNhPRIRFhNnEzqX4sMh9wdPBxXHK8+ekC7X27OFKF\r\nNMUenyEkd/traBwviC9LhcHxYZosLGpdxJiA4COJiEHlAATQoR6Be/5HZONm\r\niNo6EQLAJk9zZth8gIA3ZaaKv94PTOCoNQ3IFyyJmqVIGKA0zERMJRUtJ7Yo\r\nWuIqB/TXj7KAdwEZF7/kr/SOAZ74aR0XyaKa522pMmkXuRrfvERvwI/OMhfC\r\nfC6FXf4ZSZeky+Yuov8RpAUYt4sEjOigNaeV6POPszPequmgGi1JU4MbArrk\r\n7PcccAsLG1cR9HEIP8XXFAyW0NB40uGgC15Y9zJj3rqPPfyVvBKPE6rJHB4v\r\nCKJfIGMPRd3WjHOcXXbCPmPhIpVyYq8FSK+7ppdx0O8yZONkbPMbDHkRU9zU\r\nljg3yNONJ9oW0NJV4cEzz8ZyzFrukuLx9xY1AAlehHFoIYOuLv0++oxUi+dH\r\nKQOD6pyfQRJzH5cvA2zIrk4THXnh76wjPX8ZzX62n3bWJHrXJ1ywyc0wqLCc\r\nSr+0J4f8w+gcBUmX2ZPBGFiaAlaqZTaAlZs=\r\n=ZMnV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-use-rect": "0.1.2-rc.15", "@radix-ui/react-use-size": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3c49f782607701f97eaf9387ded5aee0789fa602", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-ylilqYDzvVkpPVJbvXbN0G9tjwvWmedXYWPh2V3salraI3yAUIeNO5FdwQaj8YyaCWpcwZPGdg+xsS6ob1WsCw==", "signatures": [{"sig": "MEUCIQCXbuN04j+nQxAilqJpR/u7UI21zED+E5TraZKJEzV2pAIgRuAw8+ax2wSmaWnXCIYl/c6acg4n00NfPb3+hoTQaEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5+g//b6+YwDuESHBx3yOo6qeO9fu7Kjl6GDod6McnLU0/sJvR6A/U\r\n51UYcllWKOE+ZSDuJ13lcKucRuL/KyJ5xRU1kcDcpQiGbep6IYF3qf81zfQw\r\nuhv94q+nlBUFSZ7p0JAMogw2gQDUllNHcKCcLYck6a+KbpBrY914NpMbChtp\r\n0h+h64DnR3/73++3Nz7xNqXdwYie73KqcZ/m8HyuDFn+gioFX2ZePaDKGn9w\r\nP2Nc3B8SlfS5xVjNuAJSIqrVEI0wLIfZtj/HWJxI3FYHlgFHwd+M9bUyOI5z\r\niGBWW1jLingFYrSGPjoCMcTCKVEgtE+ZC/417u/v5ixfmGAqMUocapOdLeJ0\r\nnNcvWBwHXbauQQvYajF9OGNMLH7r05IUXyIww+xD/0sGCDJ2kkAdZBRuuXyD\r\nxw//dspn+xoODpxUfWEaPd3thjxx5tgKqFz6eSg7DrMBy0M/O1MBWmtY/qg9\r\nKBySW4z2FuNG9ezJHAdKTHitAuvBmVAFG78LNa/cTtnAXU0BKzoxRAZ/WPuy\r\nILg4KjRPBEYjrNxBVNG/DxnzWdq/bz9T7YK/VZSKLJBS4JaIAqC6OnWnHMlL\r\nyy/DN9O1o6To0SCXfg8FaFEKMAgUC1fXc3rK6VFoWnSAf5/8VjkL8sZDn0Mk\r\n3BiFs4PX1aQ3O1rQiGWfbJfMYJ2WSMco+wk=\r\n=9zZX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-use-rect": "0.1.2-rc.16", "@radix-ui/react-use-size": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f3b30522a0eae7e382696db5820e5b4d6fba133a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-h9zgizfh6tG1euKuEyDR1pIBQGq/CGWB6fTkjAq5GqyDN7kDqg+fDDm1/2QbwwQfi51RZK1sx7FlXl7vaPENaw==", "signatures": [{"sig": "MEQCICfD65+TqqupZyZsNIh0o1G8ZYy8H2YcorhFuEmt/o6bAiB3R7YMcX4ejbZi2boKbUfg7KV/P4ASoJksJqfwEvMFAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTr+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoW/Q/+Ja1bkFythk9Z3a7RDSIbIf6q8yK27bHpWC0A/lm1kv8saN5s\r\npmYHSckDt4oXQ8VaXKlKbn9ta+aagpuw9eSovmMd21AWsDUFCMCGr1FbHBFe\r\nc6Q+JuPaF0WuJWNa7Fbx6V2SUEeeiBB11mU+31q/f75OenhLdhOnw7nki3Fa\r\n1evQ3nrMRJNwepdLQvxqaY2nVulZN6at+f60rzL0eDgtp8ViW1DULIN3Cmmb\r\nmdbAMyU/8lGk3HCEYVXEsY/qr2DR1FdKslGex6wudKiPNPlg7lyCcpQnqMQN\r\nQYHNwTaZ95dR6TPms/3Ja4d1BVAItSMr4Ze8379y9UyFzJZ3My0YYfx44VuP\r\n4GvNc+Kd0RXIyfNsVBcJyyVNjFkb1+sw06/RL6b82hukgT1htbsU+4igoaAc\r\nxjeA1EhRXC4obMWjTSaFdIYf1JYDiQnZSPwfHbRU+23FVsbQMuw/MgUSJmZB\r\nd26r/eo1CEqfoFNzBpTwwNJEF6tVt7zUlwHvBdgBalLaECg5dR8y/gIrDJpN\r\nvDqroBLLnw2gYsTTSaPOff5ymx8BiegSNKQiiqLLNCBeMfSCybFjpj2WAwW+\r\nzaiT+HOeWIgw4a8DD/eSp9bPWaReLWmY7ytCpTZlQ3s3WdPBHO1yDFRhACyA\r\n3nRNKxEc9pSaej2Kcx3alMCvsSxP8kSQhQQ=\r\n=eYGa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-use-rect": "0.1.2-rc.17", "@radix-ui/react-use-size": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90f0930f453aad87542448c5c843ae54f85bc40e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Hg3ydmkxa0zh4fX/T+VLeEPf9OS0aI2xu+Rx4R+2islRTlgg1A8+x0HNbptp2JCG9udyKtJCwmOXeLtT6+p9gQ==", "signatures": [{"sig": "MEUCIEP4HqNmnk1OR9Thmkw0hEfOzU6vi8MsLoRway8oDZ+RAiEA7AQ9Kb/T6hHcDJTEz0mLMdwZ4IWFhagRUq/9PRfWlus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkDhAAh+dD1fDC6ZU21EKZs18il9hI69hgywYzprl6yAieZ3mIJfnI\r\nYiEv8EurKYnL9000tstb8Y1qfxQTqfVInljxGqTb0Q3k91tcDtRE7wosNyLa\r\n90+HDrp1v5iQljV2E5H7sxjAvq8XM2nc/gYPazQxQcrIfpDjHvQYnWLYo7cI\r\nM37ITVwLdsh1rzJbqKKz0hQk3iPaHACMuxoBKoj8Z0AtkYoKSRnY6/AqLJeQ\r\nT0OdrPi2z/1KPNUIzDwBple/0Y6toWMITAZrxEfLDp5FRBlJjwJxzRc0xkoe\r\n8zlTcdS/SFy/O2hIoI+EP3vVdYT04gTXLz841EVKOrSfs6dkl9a6JDm2LP/X\r\n2CuAImgeP8h/L10SxWFcdpeygZzTw7xzmIz+SQ2UNhCziITPA2Vgy9rpMY2R\r\n747mbyTvlv283My2wfogRbvAXQTR3sfEzGR/Qw102i/XGjbr3TEKgS+1UvG+\r\n4KKYWBq7Q7yfvm74UeBKyJbKzhS5PCbM7Ri+rwJRwlMo7rZHg62l5ZeDA7mp\r\ntRAz+a5FhfiK3OemXGGiuif+Zd0YIILUUa1sXNxrixTstWBk1Flz57gJS+D7\r\nu9huE+WsF89q1cIkBmV1MedZPytNRyBzgrbV0t3ujggnP39GJXHTZRws5fJ+\r\n9OZrvA9D7JdX/2ACbaFgXFTLkVG9EKvOhnE=\r\n=GQlU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-use-rect": "0.1.2-rc.18", "@radix-ui/react-use-size": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "519b637813b349e428853c615965eb5c4d7a2df7", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-whsQeXk1PN7nzOIY+yXTsqxvlA7Bkc0xGlieP3/t8gmzUMZDSBlg2gbhYmK+bHp2/AGVQjj4N39VyfDS+Tna0g==", "signatures": [{"sig": "MEUCIBoP3ICbUZDi2wcCmMSK+crD1/jmcBqq8Zj6YqQBFKtyAiEAhKTsS6esCeBbx8YscDy38pYLlAJK8iX9GXKvU5/A4q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHtQ//X4qSH1ZU9EN4MVzvU61pcNYIooCYTIbJPykFkp4RDUsRlIig\r\nAt6ZW8Mb85FW/iLcaulaucRN6CUEFNxns8etd18ONKJoojwQfm5p+epqUAov\r\nbUPykZH/zkcog0WrhRamKI1VMBv9+RNugWqLze1dTZ9N1M0tkMJeISuHwDx+\r\nVfMcf+oeGBt0elKdZ0Jjqru0djdYBQHMi+OeF7qxE1n9Ren+XKlf7B/iY5k0\r\nIBUPEQ3PYvq2z/SMyMUkIpqy209j0I2VQ2Q4ZQUCmzrD+V4Ea1GYb2v5Yphl\r\nh4XA/4OsiOeiwVTHCcbIGqMDif2DmJ+qxqBaP1T2EZXoNLQMOZD/owU+m8Bj\r\nWzhHMORWeFiY8PoZB9qSX3yTwNt0jqYAP8YPB6jfHs3/PE1++kb4or3n7kf/\r\nl0+ifasVYfzX9bXyBVFeFws9O49Ap+Ev5uVk17tFL6FCPqCWMwSq8zl1eLjh\r\nLMTP6YIJoQc0tJ86owgmN+74IBAS0tcPhIS9Uli+s9t3hnTAs/i61g3wzZxt\r\nzDur1/YFFlwChm6lAKuAZN5UBGzExcmNHzOsV4w32J9Znc1QWHTIbRNu9Wfl\r\nTZByzn0LiWB0fLlvvIo7/ShIMYZXZywcsuPqzyIhDFBMaHxljoqcC/6U0PR8\r\nL7wTFPAsIzsRFB6mJHDzPC21hmI0As8Kww4=\r\n=Z7ru\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-use-rect": "0.1.2-rc.19", "@radix-ui/react-use-size": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8d622171f2e888f2e1e5ce378e09838f15874aaf", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-1Zi9KA2Pg0EzPIJYKft6eqVepS5j9X9QS+mxSXDMG4qGQBtI6FG4CIqAYVbovA22Vg/E1zZTX7O+60GALDuXKg==", "signatures": [{"sig": "MEYCIQDhua5Teg27xgHULL9doj3R1y6k+94QSphGr8MxxHCGUgIhAOpqnvsGl2rDmBYXfZJEeIZZM2xF4lfIWv37GmHHAgM0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwsBAAofpxadE9nIo0nCiBwP/bL357SYDxzc45U/NVMfkOcislLAvL\r\nxkK1qGa2MCa246qnOEulKuyfUgxBvZNSFnuWC6EWZa6aw0EmGil1BftwGhSB\r\nXfadQr9AaKk0KCj+hu1hJ34d1VBHn0Z4AXIbCsCt+oAID1lOVcHejUsOKjvA\r\nwM99dGjYmHP+Bz7GoPDfLOqGIBb+0REHcDfJ4Vr6JscLMqf9yP4v+nmcHjbn\r\ni9/vkzxpoy4IeV8WEh8voebegChZCTbUKLNgCc7lRmJE6oAWP78C1LL3ycvf\r\nOTvpkX/FE3N3diR/mb3FmmkfZ19/RJa3NIwUC8ZaKWxWadVyRZMBUncgvK5V\r\nXR+9LwbpOS/1luxUk7kyVsOl6V9XPAFBGQgxAt9l5UIEvK+mYDsvTiGAPtix\r\nGUS7LZAPmlyeeSp82LdhohOXFEi5k9EP99wtnJLmkCXMp2FgQ9nJqjqadQFu\r\nIqGB/fYZ3GeGoCUPs2tc7etQMBYj4V9VAcm9FD9jFVwz5hVmVkmz8Ymo3haN\r\nzG8Co8olK1cf8moHgETrBO5buLOm/STvn4aznHexz5tYOSzlYp89jb5ym9is\r\n9+kloMHelhF6IfY+wTSG7wcJsF75nK2FiGawRtIUvB1AnL22uZphQlvoeW7J\r\nOZJOWySyQoRw6YtzZt80aw2T6ezZYXy8vVQ=\r\n=RVkm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-use-rect": "0.1.2-rc.20", "@radix-ui/react-use-size": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9f7e3ca2d2b61771c08ad68ca9e88171977d5955", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-UQ10ynbFQ0WyEzCTS4ny5rcPrcOIOwyDi5wMttG9uvXPP2rQDy63CYO7wvz0FuRCL+JkKK9J02WhHxC266qv+g==", "signatures": [{"sig": "MEUCIA1+l+yJFVNvsd9Vad3WX0c7/cC6RpZXwRVWJmK/o701AiEA31HoRJf6FLMhCGV3o1ZGqOGufrzAcSR9y1iX+4tROFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3biACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqnA/8Dm5HFXp/ayGc+ZctrYMC6JaL8hWPq9GtzE/QG/02IPu62Cak\r\nsyyN8+q6E/in3KXPV0Fv58T1AGemB7ZVSpsKC77in0a6kd+mLkeLuK/Ily5E\r\nW/7m1y5uwmhJJMm1SdZM/jS3crTijqAxLXbehL/IJIMeBQchGpJpvwNXYBrb\r\nE3K/EnvGFzk7jEw4IQPZZA0CSyKA/odmcywtXtGV5lqdd9Nb0FJLt9nFMiOk\r\nfEFRpiUBBmcO/XHtg07ayJIy9fOIutbf8uJS4alsC/PAonFp/OzzIJki5wC6\r\n7Nqwf31RKs26SPrx/JmpGt0o50ui0awtm+tc//hblBJUzmT0JPxntc5u7nC5\r\nejeEuSSSZWbQ39UFNQWcYg4N4WxBJbNFrOwrlLevaj37YJw+VBbd1Ct+x4T7\r\nrI+UDp7VavAvAnb7LSuWv726Hr+htAE0oz4yS9Q33l3s6XhPsMlTkJ6CoBa1\r\nm9cdVKsd1V2lQ0xx0TOLVaKaVDVsS0xG3SoaaeC+PRy7oU+8PRatifThGXcK\r\nQUsyprYgn4tRntI4eFZqizBM5nD7sttzaVynP/xx8ZZ/njkTwg7aw6niH8LQ\r\noAQ11u+vTHnzwHbECB6hIzy7QB7xQBb8kV3CEhuEqkWhnpagflIrswlCPPvd\r\nrkSENFAdPwKrdGnWj7Rjl1cSQAoLgDuAM/8=\r\n=z7OJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-use-rect": "0.1.2-rc.21", "@radix-ui/react-use-size": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "340cfcf5c1c9bd6216f7c2ebaf1435358d3541c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-p6DUAcKBYpFOPl5AM7kFuRtsz8UXltdKrdcHn6vyCHk4TKRvaLIRfwpuN7HaH3qL41ODY2nOVmmzGdOcW/Corg==", "signatures": [{"sig": "MEQCID4/Sz4zomer16UEkRjO7wZEoeQttNddzx/DT0teJWe0AiAV2wCwJMII9j0O0eFw+IwrsbKvsfkZSllP+xGdKPwfvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroPQ/9GGeYiLV94NprKCS0zHLdMlYadNRKhF2UxO95Z0vHq5sPLqyw\r\nMiedcYjaP6BSXkRGDaeIoL73nK8gfPEn2ex10JnWeShajDE0q69/nfPJG0jx\r\ne9CGBamnZ1jgRdfnMfbD3P0MaqkvtU7yMQyoj1U3VeZoYOZKmA3+/nIaHftW\r\nW75lqUgha5bJcMbxDxmIQ6tiBnBbVDQ9GI2zzuKLmUyosjcjRYaiDd4iubbQ\r\nBWVV0dLrfnPA3BzTKrsBitQs8Zjcdxy9e7H2PjNdAEw8L1oNBeIwNzcYpUVE\r\nQ+UBbsqJ3yhsshsAYO0lOmpyLZDrfWItT1vCrbfVZjL0SQ6IeWqeGNTOyDMn\r\nDuJdmwGSgih7MAIeVxeedHoFY3yszFoh+P40rlcmZ+F4Bg6y/Aff2H+VmJVi\r\nh/1MQQGsEsClAH4R9uE5KI/kJK9NepDrgPb0RYZpHO7JMd0IJU3kBZc9F6dx\r\nKo0nuTn0YkIKHuzorVSAhcmKF9VREwdu9fYJ1tlTrM6rBv5feN0WvEBTLw+n\r\nbRMPCGUPY97IjzEJYIstVQPxNDEN81SCuWkikq6ZoC28blOpcH+RG6wCEgs3\r\nxzr8wVePtDFO6UbvCgsZ+bDx//P2jjClIQCyT7Slhs67BDNge+xYW0RNOY3X\r\ndi383W36J9d/aU1w5YxkmhRV41RAEJzAoa8=\r\n=HYZ8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-use-rect": "0.1.2-rc.22", "@radix-ui/react-use-size": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8221a6f2b0fd506bf19925990734fdb960e46eb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-0x5PV2d4WIwbtFlJ0d3CN9vr7i8I+MRhdPNcqzy9h5JSM/BPfbH2KHFHgr7d8zWXqYixNMGt/NDQA7/GknCKww==", "signatures": [{"sig": "MEUCIGB+nzJZEdzTvcFb/wRu4WwjdlzjfyWvShzzHZCqlP3tAiEAqEpIlcDGIJT2kchpGSxh2QHudoBvbqa7feAMR4FQPYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0Ig/+NHNWjMlxr6NRF+BxtuCFtWKW+nlQPYULhkCJBYDdkHpz91hf\r\n43qu2DATHvkDvJIBX8192o9K0kK0yHGz7MA2dkNoOV4s81IoSqawaceOzW0T\r\nU3JBC0W+UlKV2uZ5bBGmvwY1GUzI+d5zrKeAfTakIrQ5ai0/FJxuaqasgpN6\r\nbktVoNDFftY2AR+/u72lu9JJbAyr6c4VokjoaDQt7xgLKbwIcFmTFDCax+ZM\r\n8XNnPQl5biWojDEH4np9XkNCki8BAls+awcj2hQDS9ozjFWCQ9tgT/RRm6ia\r\nbCGzUzMa/bVC64/NcexdpIQd7zEepyxwAxN8qJEn7Yb8KfY6F1/KG6l4A8dj\r\nqBgCYJFCPq3CfGPU/O18lKPNYpusmDC54CQhUS2T2LM4eQvT5BxTf3A8/ZKG\r\nccG//GMCVC5b/kqdbpFK7cI62pUhI5+m32+aC1Zr3lM83CwOrA40G9LB34ZC\r\n9x63HpjzeQA8rgE6JcS7hf8gnVLGrDswZJBNKNc1DvAbhTKijzn5oGfPEOGh\r\nYiuFE3yMZP0VjD/CEJfISss9QoDIt5PoZ2LzdMTshZzHVzhQW7Fj8gVpUPjr\r\nurNgxunqKjd1UIdlH0CrhBxONM52Xrrvzh9IiMvmOha9tDcF8GD5HnWfPgWH\r\nIq7YM5NEG/GwqWxt0UG+wXpbCsrjAHRsOiQ=\r\n=trVH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-use-rect": "0.1.2-rc.23", "@radix-ui/react-use-size": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c4325f8a33db28dab382a62ae63ce42d9cac22a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-N4VQQfqWYUEuwJECut4vdkbDbKe6GP0niWnjPjMWK38tBCd2ZRuZ/rzoYY3y4WkGipWjVRAN/ClxgeJHe3a1NA==", "signatures": [{"sig": "MEQCIGrIL0o0rUm1OW/hVwwa+njEZ2gAp24qI1H4TFgaZGp+AiAUKRaymig4rJhh+oao8WNUiq7VHOqNwOiJ9KbCrBtS6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtZg//S031vlauVr9vF4eDpRYADnnpW62W9Fc2HL34JZWY/fs2sO5U\r\naMIQgbKPXC/jZWGDN1zEE6kYryeipOOptogV6TNDO7QN+Sw0Tw8XZmtKpIOW\r\nHLX1m0KqVpnfvN9K8GUVkZYIdWApxrBNT5OzWnuwVha+3WpNgXrqr2BIDdhz\r\nXdRD/JuBtGmuNCFQcdyHE/Bjaf38+g3KLYFSCHQbnieGvLhoPRVMvz87BJaC\r\nGXJHB+G4q6wul12SS4QjJMgVQAtgIfwL7tqlppm5yD4EgK4hBTVRaGJJWsmN\r\nk4W0JclElZyeJIpacxhOnWWp2nJGOwHjZlDMCO10zYO3wRNaXV6BCk1UdUzd\r\nIciMowVkdpdfgYt1J4ABt4YqvZObn3UakIh5jS5n2fE+e1DPnAyjJTnDTKZQ\r\nSbLwLxiIv/TN6bJ4Ql1uctVJKWG2oIn56DyhGVJMbRSh+u42fuEqfseY4eKz\r\nliAAEn58kqig6YvDlmLJhIFn6jYpcc9ZME33hL+Jk7d/46JXrkGJO2FQgsLG\r\nfelVwjnsyXskCXmYzsg1Po6Wuz8NboilyCsgipg29xxVzKJchsl1edMowOvs\r\nujsfur05+VyHkyxgx4MEUhxanmfxr5DtwVFNsIfVwoErheIf0KL9xkMsb0s1\r\ndfDcYtMOuBsp+ZWChldwvHYVaK8CYbWXbOQ=\r\n=DQzL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-use-rect": "0.1.2-rc.24", "@radix-ui/react-use-size": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37cae03988709c2d1dbdc99689114c79ac9201f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-VLLfkAqcIjk1Kqx5PeRCeoEMY0K29L3jCh10tg/3mVNhwSqY1VM0cnvWQaqiIwvJCcuNX796nvJtqC+8RkVnog==", "signatures": [{"sig": "MEQCIA7db8vAisfyaYMyY8mNlwmyBAr9qPNhWSXv+vUYMNcEAiAyICIkLmm/FWS7S6buPbhHTEseQmhc4ba5gWE+Oh+ucQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDzA//TmKQyenuoq4/V9gub01/5g/N5bR3xNpUFW1JU3JEj7v1G1Hn\r\nfELgolqkmCPimVLCVRoWgHKHm1MToJjbcz/tR9MRixv96kb6jzcu9Wo8sj5Y\r\nP/3THJiwQJcHWzweF0ZxJsDogtXn5gx6fPWSuqyhHoECFgpUdCTf3UTz/YAA\r\nBvWPNx+0PyakQ4BiU3QaomZgsrDZ6IMI3tFZXVX8FzIsK5tGWrr22jyhh72G\r\ngMnQDxWXHGpPjCwgo3cbTBNHRzvBb5jIADRC+s2eU7nXzKKvCFYb3ctQHpCE\r\n9P7tWK4tadwrYcihMETABPtePbNnMsI7k272Vuauw6HFji5goGSch82GaqpA\r\nuUut2sFlZLFALQAUak4utbF7WHJ1RBatp6p59np/xvOjbwn+P6TSpEf3j4MB\r\nJsE1xekNPocczAUi7jIldqXELVx09mIxvwcORnhozQtPjeb8wJoOWW50TenG\r\nXawHi0vjiIcUsuLv+tyioCPuZETQUigZT3aSz4aDeVg/Yc1nMdxL3E6ZdWog\r\naR8SwlkZEKLSbprnQkLHxX6YwsOKaT3nBvpXSsp1vJins8j6MNldaegacNN7\r\nW8+Ia1+QuwcJ24Mnbdp79j270cjp9qXE1WDFGUphxGI+bCOvKeLg3x9u9PMQ\r\nosdeyDI6lTpahT8nCEuFw1B0SMbaa3bGw1A=\r\n=WqMH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-use-rect": "0.1.2-rc.25", "@radix-ui/react-use-size": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6cedab07591c8f5cd0d602397505440ee7bbe686", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-IgzbzUD+7PhAzdwtEtOmeuxgfQSisXvjWMLu/5qf1hKxeXalXzzUaUCcTo7Ap600AM6kZNr2Ofe+oK5ENZ+Eww==", "signatures": [{"sig": "MEQCIHDIPjy2JoqBlti0PaU3ROjIhSQk/Oqt7wkRGp967U8BAiBp06lyJnqfX9Jstbze2hF+wnRmvAwjBDGKegePhhMOSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj31ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN7BAAjlWj/5SekSWK0P2k8dA/YAw2+HNCOmt9PfmlvxDYBujvNaFy\r\nV3ZEEhsIwUbeHG3eE4NIGwWzHL+aqrj7xjUDloI/E+XX6DzkhowXlOomXZr0\r\nX7j96I/agmTxxl2OZ/WWqEhcvTRkJAwMovo+eql9tXLjqtkCC4i0hafXA4dv\r\nSx5EZDvlM5qZ5fxElzhVl5pgCzANI7zx8m72da/cmtCxydgzAsOvXZSoQxXj\r\nNsdjbdk3CfqLSu93wXj3BxcC3YkmcQb4FKv8J+MVbQdANIEaEwYBx442Ep/o\r\nvDETt7+IocTkK44ASMbXgp0K2dCMyeUXlqqJN7HPsxH00NQbQePDY8DNngN/\r\nayzQ935hz886w4xaqBXL5qiLB0gtrn9amA8RtQzaoOQS6cG9kJeLzUn0TP/0\r\np782LcY1gYwoFqSPXNpkkxE9EbiGgwonIn9OSiSQ1xM7KBdj4gwHEsEjmDSN\r\n304acn9l/usyOdTBRW0n/sB7fOgkjy9UOPpx4qfFV7cD0pmiHElK02pXlgHK\r\n21X1FSu//ZBmRj0tKqunzsoBLE6XMprn1R8+eIkyc2PdXQm20y0NNzcH3YOX\r\nndnqUxLDGLiSGzNQLHIHjQv6LkhFdWCK37Th90XgkGLpdSBIR/7QH8a59pe5\r\ncfDDMjTnv5EOaAsho5virwOpTYhF2A63W/o=\r\n=XYGz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-use-rect": "0.1.2-rc.26", "@radix-ui/react-use-size": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "493824e081e1a322048031b99bab855736e50e13", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-tWvWoMCI5mjlZhMPaH10qqaM/yoowoYPmS/Pl4wseJXjsGc1VXadx8zggZJCh2QMmFWgUjqQ2tI4Cg4h9b1Rww==", "signatures": [{"sig": "MEQCIAvc4qZ62JbnXDXYbKeKOR/zCTQ0N1EFn6JV8FGaO/y3AiBglIN55/ajI+PCwpiY3+DIb2Q5+WDqL8Bkzvtm7p7dEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoerw/+O63KVOoWzHA7KZouXaFKl4gIJaWIndMGourhGEOKK5D+Ak4y\r\n464ou0RI4cAwxbr3crK7+m2TtPbRMEosXT5CE1riEmxxlSPZCurkxLVoai3w\r\nsuuEi823VP3s6LtdSBRk96yCxFdGFJwsAn0l6QedXbRzgH7obB4J1rwmq+PL\r\n5U8kvIY8YhhGyaYiPbzlJw7xJMvZMcebY/QkWBlXMbZJ3RMU/xkMrUhqW8Mv\r\nK8EsjLfQB96shIvc6N0989J1/Cw7YCeA9+hEwIp2+wWyhHMfMW6bxoh+CQrI\r\n8H0HpliJy4pXUbk4M9DbVAcboaKGI//Y6ClBy8kAgZ7asZLdmg9Tzllfvn1Z\r\n3zULJA66i2ZQgkMIzH46o945wNCNtV5A/rMBa2pDrL5ImycpmD2l/ECSSS2s\r\nObGxsVgcvlw8FYf54TZKcY4LHC1v31N+wed5g4kuWJx0ptwXzmgHPCn3GG5E\r\nIsXDRtnRJSmycLFovD9cuwe6U+BWAzNHnmSuW20sf8Q44TE+EbnAb/6kd9xG\r\nLCY5d2AMU2jTNgYG8n2MTZdXYOPS+q+RDoqxjbey7K6faQ4XwVItLltTZy5J\r\nlYWSwDMcoGQLOv6vStw2VnhKH3dcpMlMGYGWNyvcjvE/HOJV+ckmuzIU9wO4\r\naNX7iGTZHO/rK/yeVlc2ql+Q7DzkT8zy7DM=\r\n=1F1V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-use-rect": "0.1.2-rc.27", "@radix-ui/react-use-size": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed565efbb9debc134d2e2d997631702cad68b133", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-7fz1ArJfU6oyAzjFgPvuMKLjM87o/4L4zRgn4lhpnjtx8CVVJad7xuamRTXpVGk3ifcTBPdWXK38W6YEYatErg==", "signatures": [{"sig": "MEQCIGWiIKKKmbhhpDjivllXclxtKLJn8QCBuILPtOs6W9EHAiBG4KSaQAz8FFcgTHOQIWmxq1ttjB+h5vEeUFafDvToWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7Wg//fspc3KFWcGtakoN64DqTB2Kwkp1hrSeZfzwAHNRwpYKi0HJ0\r\nrvaRJ+3NC9iR+EhNW9ZYhpw9jm7oc1xZ8tnSRP0IOzvFx7cluUpwydpCRAnc\r\ndoFvewa45VdDJysqm4BV5kghjXmqdjl/DVoHhmlTSQDoTKKPgIZtSFWLHzVp\r\nv6Y9/8wIT55PtenHuEchBhnJls/ISsOlCelS7ovB8ZZxPTUdh7GquqPNaqIF\r\nhh4h0CBUVleAjiimExLnwBflwYSqTjxvgPgdzaO240f/zZ7x/T2cV1T1vNes\r\nBESViXf4T91AwftWAwsO3zBeQIuLk3IRRB1SLDwD9n5lCaOWv239nju6Wxyi\r\nLLLqYdzsJLgv3Yyh46zCaL0XpBvtOe5lIiYwryhTZWnsBmqyyv3h6T5O6C3z\r\n0IRDLouRw2j6nnZbNfDWgZrJnPRF2S3VOCo+osYRs0kzTRiGSSsrEp8ioKcu\r\ntu/1IQ27Yd4ClpryofkIIYoVlXoM1lPVBe73t4lSh9jb9/HZaxmy1qI7SX5X\r\nrINOyfD1h+AhV5+alyuBoZvfysQyJvSRVYI/5NDkbfRLqQKgaBxLHA7NThip\r\nuaiYe/k3osQKCAJ3xMcXst+rqKDUPl2scZWIpicYaECfD8w4XxfDE+ECGiq1\r\nGt1VTssypl6DR1Mig1tp0mZYj6SSe7PFefg=\r\n=krFC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-use-rect": "0.1.2-rc.28", "@radix-ui/react-use-size": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f6612c16913c747645e0e64115324aea547d7f2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-e9qLUmwDczEAPRtWUUw2bhNa35SJsaypCeyQCThOL6JL6DFDywaaykvpARxgBT08F98zBzPOBy309LZSViXDMg==", "signatures": [{"sig": "MEUCIFykDyVLenBlqEdxkykpxYTWL1Wc1b4/cx1W/+id/jMIAiEAzbsFsll9nYYEMWinuNWd7ZZvbDemoCEPWQlAJg2KPEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrN8RAAoJppZQEApbO3SjpjPhkCAPuou0qEAQpYABEoeFD1QnVib7U5\r\nMqp9RbV9bNtblOLDwShdWwqLI/hm788Og9nXqFCwvp5r9BK/2GOBRUJK0hm9\r\nWmn17okeaZRlGpNc4+lltAGdy4c4YxQdr3qCrrmbJ80A6mDbcBwU8MBiMfaP\r\noCCKO3RRvoDfEexJfnwSjGJ8mYyWgwaUUZ+trPRnmkOiAnYsKr3NIDdPKjON\r\nOF5PIig4y+SQ2XDdpHx2iDezaQsL9iPDC6yFBKPCEafW66ybwkpWd1Fe27mF\r\nGS0TWxleXOZCggFHAQ+tmjWMDyBcbHnwpSWg+zCtL24F+4gblcjYHEVxOpxQ\r\nTvIXogPxpPDYMtWaw+fiAljXEvprSyMYKjeN+93UZnwUuTpJzd0QcDjz9G0X\r\n49Li00B7iHBLWby8qziu0Kc9uqQ4mia6XDu9jD3ixwckUDh+4naUAgQadkBv\r\njy6+hYa6PMjqKcuxypepBtTmKY4+nu/tkxOX7EFLAL5jPFf2L18EYRGOESQx\r\nUwE8N9pb2p/Rs0n2yjbFeatiKFQt2Jt/cjx0Obh+CzvVXeABCtJBiLdaHxIA\r\n0xUhaU6o3DBNSRLsdbVP1x/dNc4xUI2MGpTP6HVXxH+5kU7XMwse3UTFmiek\r\npMF4j8opz18sjYTFVr64xjwDPmGciuWAHTg=\r\n=s7GE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-use-rect": "0.1.2-rc.29", "@radix-ui/react-use-size": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f357972300c22b5fc7635dba5b8e0de9f6f410aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-Me2kAfl/tsxZktlo3nqbl+cLmAQiQ61AhT1MeHKuZ8c6OAwIEOkLB7cu2nuPN4V7egEFlQjbmE54KNNGBiHU3A==", "signatures": [{"sig": "MEYCIQCS9fCSyrcLPDtJbPttGk+5IMCDly1l197xOOHQpE2OEgIhAPapi9MoqdYjQedZsj9pSq9LIRkYFCpYlhon1TKkdegn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy/A/+PrwWifzZR9v6VxZyAOj+IL0ZZe6OB3Dd6hK5sKEHPiu+a6u2\r\nFTpC/2+Vc7iUsno6HOuQWAnolC1CpYWGBxc1PHFMYPoNGLCLNKSGSlF/tV4N\r\n5G0xVYsVVyy/YSC2GQVJXrxmGJAMJ8vRJ2AwthNOPdFhVZtHXRI1pFdWRnNW\r\nfMafF+20WMcdEXjWMl/8Y2k4iBKiWYhd5m3YVdv0wNMdR6x7lne+VuBzybkT\r\njbhyCT9qWE2y1AGaFOG/sWlusE+me6wCLsyiyZtK97aAduEMB9FNrSM5vRaw\r\nVzlL9t7RtgWz/LbFTy7lwEusHnU7kfpHU2IyCI+uYXUOYVY6qja9/UY+gaFq\r\nvjdhyQGVpfcUK4cRYjy0cNn0fd+bXnJvPTyQzWNdJjeVkyisr00s0xnxeTcC\r\nMZft7KEiz4bRIoHVH+Llapyi6w+vpnawDJXzSUN39T6rEaxElQcHcnhrgyrH\r\nV5aBV8sPUZVQS7sguvSC40oHZqr01GknWZoTtVNL2YJ2aiXUPu5ahqOQ99GD\r\nxaj2bKZx4e3u3GeFYd9R69nejhBGrV4Mt9Gz35vqItWuFGBEVqvOiBOi6e7E\r\ng7U0lXnjuANkHLn+V24fFEF/lXkU6vWLdQUD8HZI+P7AkULKfEG/yOuw7phk\r\n8PJIuJALVc5nNkK3BQQvMQPhDQZoQsb2Roo=\r\n=iKm4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-use-rect": "0.1.2-rc.30", "@radix-ui/react-use-size": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6d01d41882b84b021a646e38697d882c3ef39c31", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-YrZw3zR+xX8aqIeL74YOTx1qm7FNbmtws4pZO2BeE6m5F5LlUA1ujJNJu0KZAD+9CyCJiYdvm/39/vYSH6DJNw==", "signatures": [{"sig": "MEUCIQDI4qYCJgpqXmbuFaB9wmZ7zdvbok3yfyGeu64nGvtJaAIgT7yqU4CFsu4UtvZQVz5U1AMN28D6Zn9aFdheelon9hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRlg/+KdasAY/BIOY1AAvuaNfT0u48VdTomwGs0boFs3TEH0MiAX9d\r\nmdkkc6l0Ugg5p9yEyvV4H3w0A51SpXHhFcEc2zQmib1pfVKOR9kPwlBxzXTk\r\nyKXFbaiWyC2SKMFL9rLHjI/r6qUi1tvfoJ4r/TC3hv4YWxc4fgZanxSkOdJG\r\nlLxqyucUdp7Hkb1ssiCtUDNhnYcSxBJizpOTYC624qJdohTyS0D25AazWntp\r\nrbmMvYkKvbVyR/yDjo7Fzdvqbhxa7Liga5Id/jL5XsPzEEA4y4huQeQKeujh\r\n26s1KGfaA+vwn7VHYPSKjEcaI82uuN3BJ3eOhoYrErS8JNeZ4dA310Jjc+xc\r\nTAGa5ZooYwHmr5epsHYVHDUW2VYBXERfbUU0ZOX8Fo9Lcoc9S5jG7OrI8qWY\r\niVriUH48bceu837sSptCwG4NE5PuWEE8uReg1rCiUogVQuiZQ53QvLQ2Dpar\r\nS1QKTYP1v1A4AOjWcBQBRe/0da/IT+xc4jLF1RT3aLvZEbR9jX80AoB0HYIz\r\nSMJIonCH6kX3B4gMzq5hD/zvlBfa669sI4bhgBTeIGriotutz37Og/zhbPrq\r\n97Uj5+BmnaH7NjSI3VydOwo3tOuicT4MmsKtwp9KTam1YK4k68XiwUTmUjgG\r\nQdRugNm0lwmMY2PRrH0iUMRV4NAmwB02SI0=\r\n=yLOY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-use-rect": "0.1.2-rc.31", "@radix-ui/react-use-size": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "06a04de7093b731492b03d58b4683c4b60e2e908", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-sfNTQ6ohRoycTR/mgpvS92RwoHSzyRDJ3e/q4K35JZ6JpBd1onAQHcfLpJmO1jtaBDY2Otdw6uRkkIM+Je0tlQ==", "signatures": [{"sig": "MEUCIGrMG8Nnx+3xGpFbEolz4lgxfJRhxIdO2igv3h6SSeVuAiEA5bYjzJzAoK01Oio1IM0uPkTuG11JPcg6xCIPcu3tqwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTBg/+MAMtPLxZ93lvFKQiuHk83WufrlhE/HCpZ4rELoqtWQ/f9sl1\r\n1e6U3vEYYrO265ivlvOhYl85kIfHHsx7duNmF8KyUvBdaecdXscu3cPf1QIE\r\nBfRhYYh9Jx0bldfM0/8v/NOmLwQk3PI+7j/TM4mywok4uEY66sA2FVv1IwQ3\r\nhIa3UtM47S2x4Lznj8zOhZ3+wY/louMAXEQsQ8OCYGTOYn5k1oz/xnC3JQjJ\r\nYf4tYemwaF2rmp3ILCamwd/+JYDljJVNyecvqMLJhO7crc5aemuMRItZetHo\r\nnYmuCL/dT3sajhgKoHTvY4i8DRu+ThSOQDeQLWBMwDbWxZV/OxfY+tz1ywmW\r\neW0vMjXoOsi01bdz8kQsFhk1qZU5PwtjOm8yilvDFAl2tfooYAQ71Ko/FaJV\r\nd1A3uqLyhqf1CvYk1s3PZSjzHZGbOcdTrwajrAVirSh21e0/rBA3YK06XML0\r\nSE+fwcRGMFSn07N5aYzkUDTYOb+zK6lA7HoZhZ/2D7MUGna1ohdOb2HTY6fV\r\nKzl78lYlhnmiGYkKDS0UxmJJKOdUVDAw9tDHRoxgCLTW8Nx5o8n7WHOxuoy0\r\nkqST1NEzw4QMW72GqUTP+3LVhU4rOyF7lc/KuVikX2+3FSEPBFSfW5A+11+R\r\nGp3pR4TuUu6NuCIm9gPQMcUDCWSzDjTNn5M=\r\n=isdY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-use-rect": "0.1.2-rc.32", "@radix-ui/react-use-size": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "04f83d5c121141d5f76bebf8403be0026c9b9d56", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-1+cJrnoYAVPmbHeHfIghQdE0Nt0ordEP4a4kY5Lw4hK5DpCMsLc+3dDb7dHOKH/pNG410m/gCYiessU6Q6q2qQ==", "signatures": [{"sig": "MEUCIQCVL/+P1BrtTGW4VuCrd++KuomvURRiONU49PVtoYkcRQIgXf+/zr7lhJqQXqnKwC9dnFrIIHdRNQXWS7R15TVr+Ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPpw/+OK28HVWIRogjkULUQa9L9QVs5XftPJWY+ZDSi7Chrehl44G9\r\nneZwS78gI4RMry+ApPUdahMBelzNW5Rktl1KbDnyi2RbR/SQC+uLe2yF5QDv\r\n0yXwQYztp9hFzZO8OqlOYIKYHsaJmQ+v6SSBBSuOolRSfKu3MlY0che77J2o\r\ndzZaYffDURm5Bb3SDoRZWAxzWw9kLsG/x34T+/+pGjcdUjD4++2zeWxr+njn\r\n92xiI+xhHRfosc0xJ7QM4DdhPpEZXWOk9uFJLgCPCY0BxgBl0KbdrX+gT0S6\r\nMVBG+rc0xgrEGCsWPNMEwBklu+DUgAqTllnpjonYUn2SM2nQ7U62Vcx2clCT\r\nHzzxo1dkmLoccP0SUm8FpPx0A0aHkYbynIf97+HZcPwXsZgiLIV37EEkrVO+\r\n7NxMkdW1/gBdzY9b9Ode8b7gKyZOkSmX87Yu4w7ouObOAWHIg2zZ2Plz32UZ\r\nppFwZCgSX4Be42S3xFx1cRb6DG3jn4uBpC904ySuil9pJlvcW+FKfOtUMo6W\r\neEJOKMxq9ryfjEfxw7UDfBGtPb38Uao1jlHEUFAW5ikkMJOazXGj3budCnkP\r\nLyMseuy3yRsU40hldnbuseZS78KEXDj5LTUk5zDmV/r33BiGthmsqVgRd/h3\r\nApzf38701d/j8pN0mPHTZfkD06LHw6cuzQg=\r\n=aeA+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-use-rect": "0.1.2-rc.33", "@radix-ui/react-use-size": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6bf98faf24c1ac903f02d8038ed5cedea95fbd10", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-P0sGWlZt7rWhyc6C8ydAQAl6f72lw4GZiIuIi9pAKN5N786n+Djx3PsIMkqOWw7IKKTssu0C1jwCSEIX/TO4vA==", "signatures": [{"sig": "MEQCICk2rlcyBfVOyb+NGFvNUIlaWma1VoceK7WMsf4nAwcJAiAXOYVQt7C4ioIWjBrmuqLmJ4Uh+eZRHD205XyVqw1lEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlTw/7Bh1L4FyQv4BYi6fiamZ/+Lmq9EcmtbVCqvIxIzGG241ByURb\r\ngCdTIQqdo1LjYdLpQ0Pf1um8WZQddFkZccXyOmcs9UkdO27vxm5+QbrzHX1P\r\n37a96lAMIb13Qs4WJXJZ8ix71xLMUCgXefnmbUbowuk5JI7dXw1hmno2iT7r\r\nsG6hj2yVB9xTAbyzc52hy9GlUgVPk9g9+RHwKa6gGNkX91567wFaYxbOU4Xa\r\n5xg8UDTZk0sQoc+es2z3LyRoJ1qzswhPMXT5+1T60gAQ8am2uOXo2C+VhVou\r\nKrVBB68o3Df+RYhdqGd7XQz2GGc+QArr+lXLomqU97sERsnYoEELNcNEF6QR\r\nazmfeCJRv3A2ia/7qjoCSfMbVykDPtrRSDiR345QF3W8JAFtVdPVy3TqJ/Th\r\nuRvjmGNhvHS8UWOlrictVE67Tyu/YXEfpkkA/aNpxu0y1keOlylMTh0Buule\r\nYM6XRrqQFIB7rJECLdumXMP7PNOBioOUfOEOorTexRo4dxONoENfZHrnGd1F\r\nNdgPyffqP7BAWfYA0PHb1gRaewTc/PM+4i+UeMM23tfynGnUgB5THxVcQHcV\r\n5azgAm9IqiiJg+qlweA6B1agD0m+LUn/88NAQ2SNgjir5cGjXLBMFL54BI2w\r\nUsLhbrWJoLDX4s8J9XC0zRDejyIJlqOOBeE=\r\n=gvMc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-use-rect": "0.1.2-rc.34", "@radix-ui/react-use-size": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f27a0882f6dc00ddcbea9277595400f4c222cb64", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-evzuZ6qwL3+IFBeokOrY+RicI/X+/1Ng5Eff+YOyqv7qrQ+8aAkvDmHDinv6SWKsrPajun0LclSk1tvFZcTt6g==", "signatures": [{"sig": "MEYCIQDK2u9Pjalis5ECPnavinUSF5iZQO0xOKwN+Ay2SHYNwwIhAPL0ibOLllJmkkGzKzmjcix6UbUfwXiIQckMZqJ9h0Kz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+2RAAhD3jH+xXKitpOux1ysLI8pzLZlkGeKPiwxLOceZcprzyvRmM\r\nUqHiDJSChPbEk5ZzYiVzfF4NvisjJ+QkigTtLp0CsMGzTM0HZZo+Y+lxGitE\r\nDl/pKL/mCAR69J7gPkf7OlaR5yCK2ISukkZ6KKbyUbuUYNC+JXzEcBByFqUx\r\nKoVFj1SkxQuvzvL7Xcn7ZypPq5kuQm2kWthWW8OPI/ZdFHkTEPrqNQvZ+4jw\r\nHU0UZdeZjGuG7VPjVqCmYKwnwR8aZOJ+oUjebS0xsP26sqUROkmOp0syXIK0\r\nAk8wnPJZQAIT/2nrxDFfULgOvja2HMR2oyqRE6Qfr20IBkLvZ0QrwfjheIyi\r\nLhLQYse8cPW21IJvkEkxYQlFDkCu0I4dKnTr//FrdM7wsM+f+7zLmILuT+t4\r\ninLlCYosLQ2WhK8BDzuHTVCLZ/Jg0VPiCH6Y+GbJHsj7JFHrFV4xo8nxnpxd\r\naXSuh6yr+06V3w3oAARa2xUbO9pdscNCae7XjvqP5EVlU57ABUzQM46/V2Dg\r\ncpkShDoZEy+gzStbKtZF9qbOTWMJgzkjX5VyAg1oH+AnP8kQatPv+gNshFqL\r\nowBO9n3p1tD091A7KYxrXqx+89Ga4Bl/SwqjT2ES0DnKm+rjBX/OceCaoK18\r\ndzsgxpzwF+2FIBNMq6hVo1YT79M0g8qd3T0=\r\n=pBP8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-use-rect": "0.1.2-rc.35", "@radix-ui/react-use-size": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4e4f3ed4f21fceb4efbc6e97114397c9a2f21d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-HMK1vmgwmpi04w2vzzBtJi32pim+SYgMIL3dqDEvT8fOmMwzTdBskv0ywXSICJMuHDzCi66KWMvUrgRfKcd8DQ==", "signatures": [{"sig": "MEUCIBI/QiMP4biLbAzE63dGmAtrFstSelk/hFP6wa4irpr5AiEA2mCr7FEfrKAvMg+GNkl7D860HmFNuGPSSnBqaVBbHJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOY1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrRA//b1yF/H6xUDGGyC5eLzLgqRZDxyMZ+6NUoQ/tHIp2ukva9Nc+\r\n7HmTGlBrS+aNp0roeadXlHjSXogBBG1VBdUfEpKjolLsnRVA63JSsAUkel8G\r\nyGmstw0q4u1pKQapv2+chq4UmVTcvulJBVqchsEf5Jz5pBM6PbBVwIuhgZSH\r\n3tG9jhwHde7N7XYehy9tHZzB9SvQdbSSFGk3BgUsBaDG8c6F0txc1g9f84q2\r\nYGOQhNCnkiR+ToyQrLakcws+53EAGIfx315jzNCj+SagD92evI61wjO7uFX1\r\nvphFWm4vq5pI9/I0OC2+zBM6jAuDRj8PawYi9jf5gQ48yucgGQ624oWWNPGa\r\n4U1GjLoHY4A97jAEqZlpF53ebsPDL8ciF+XcyBjbRv+E0qrXSw+/Pe0TO5ru\r\nw5de3ck0kDHUluVflDn547hVgbUT8Da1UkonwMcE9KbJDdLsNVcEt9cHZxvf\r\n6/6Cu1yuvf+qWf+nrAAO3NkxQz7N5qpr8TP9y0017AblyBdUsrZkHo480N3T\r\nSVafgaLUAWsSEjXVdG1dWoc2Vw4ch2/9kNDxiXiC/MaP4qDVHSKPKSqg598+\r\n4Pd9dNPGOBRbdOTjGGyegGtf+jFdCKE0uwGWiGwwu5/4gjy65Gm5fTC8Uvo3\r\n3X/dU3/q5z0+0/bnNCghRv/w4noIoldGnZE=\r\n=CL/h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-use-rect": "0.1.2-rc.36", "@radix-ui/react-use-size": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "47c8385f43dd79b01cbda205249085211323970b", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-+YXeTwz18xiGHCF5CIHDutcxqvmNKJHi5u9UReuzOuWtmUtOHqVlNBDFd1wvB09AAlaxjKP+MDk+SqfIkB3nbg==", "signatures": [{"sig": "MEQCICkmUWE/5WsHLKT6/zli2o6h7wFD055450tJSmU1s+fuAiB+L+LL5LJ6iciD8XDlWiWUGqaDqOqgo1c5p+WObqk8Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0InACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrNQ//QBeNvPg7zusXNptm9i+vnznrnDufJJHvlrB/TBgPWDqsA2tY\r\nNxqbXavA27sm5mRR14TEvrPZ0Xk/Uyvjl2I5zd7SBAGPYdGOAhmscI+YwPFB\r\nhg+LMWlXmKK11NgUHSxXgH6TX516co2KXGKw8BJ/HZvDek7LKTBO2mxWmDHa\r\nlSqYsaP77nluTcZkVAxHl9IMockm0kKI/4fur5zIOLSdCYhsahThH7UMxoFP\r\n8EP+D2c9gMgPP2J5dVEnhhuF6jnJdHt9GbkAsv2pYaR/gurB2IY/WwD1Pfnq\r\nwIsEx3/Z/+5YXBI72NARVgxEbD01uTR3654cqXMY2y/jPENHRAfizFLhyzkN\r\noS+WNgnWCYB5btW3ZSOl5qpbX97TYvq76GBz42iPe1dkNVQ4FU8Do6gtdEvM\r\nK80ycktIvXoz2W/gtqqWd+BttQ0gA5LtDKs+nKjt66AEVTmHQUsBRtgMePVG\r\nDukH2NMT1cMTN/C4/kbchjC9a4EPjWYMb2JjNG8XSr8F0upwC9j85tJ+cI7Y\r\nRPoB2rc2gPrAHA6dd5//h8yxyxeKk2+aYAdcATByZl3ozz2qMmmVhcCGYW3Z\r\nTwcXsIJDnMRnOQMEPmXxYR1IkqeOoCXBjYv+7ZfplrMvMiJnhbwFi5QLdO1/\r\nxoUWa23fwzNdEwIbwch8eyTQRGBm3AWIodk=\r\n=Je3e\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-use-rect": "0.1.2-rc.37", "@radix-ui/react-use-size": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dbf67ce94e8f21d062a368ddf28c3b8d404c5e5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-sbZuHPYFZxurHLgPXd5qGD9CggikZdzM/rzdtNGJC/IYiHK2NEFLk4S80Ttz4U9/AuK4wePGAZFUBilzyuLZIA==", "signatures": [{"sig": "MEQCIBOTvymvO3oOGPs7WzIDOdbp9x/s0v8WAceShP0yybiDAiAWNBoi8blHZxidHVsaJokPbHjc2L3C8UQlaYgQHuinSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0n6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHQg//RoAkR45Tti6btZD34tyMS8ozG/AN3daO+4NHPrC3fkI1Ndlm\r\nOyq2o4Z5pDSR0W1ncI3WBqRLDcVu1ukGoNfo5LOmeIRhTmef8DJ2UBFUwZBG\r\nNSxoB8I+j33xZxa6zzBgjo6qJihu7JoehVXd7CSa5yiqbNGDUTL06goqFDtt\r\nTNGiObXpeHDyzdzK9MPupXnU4Stmh4Zj0aPtqG6MgmNOYMxI3MWHqG3VjsCH\r\nuEc48cV36r0GRPCIJOhvHNq48+UtujS5jW3lTTNmv/4AHW+4REn9oVIqwMn8\r\n0AKnOEYXWUFKMCe2TAEH+6IoT4AvR931BnYx55i2DC8Ux4/htYVbhqvqid63\r\nZusaSnySid0644vnSDZ8ZLAG3co2PcRybFpR4X6kdQM1cz0PZw0CeA3GF2qw\r\nOtrfnQX0jmrk01Hp23u1FGXebE2eXicY459QY0Zwjy2BF0NW9Ffsizq5+4vF\r\nN1lwgsVYySqFy/eF8CfyirtPtAAiehKB4eB+C6WDQZKEoJFdQ72ahBqFys3r\r\nkupZt5PjCsg/xEFt1QVeYUvfDRdqaHB4YVjt1SxI/tdK7aQkzHsw4cs3sq2/\r\nRskDSTw9PgQjQPfzOPKfVPeKSjN13wtEiXeH/RWC3+i5HUHXPr3q6Sw8SspI\r\neHYE2+VYBuPl9kokdx6kWrIbswObfmwwpCk=\r\n=kB75\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-use-rect": "0.1.2-rc.38", "@radix-ui/react-use-size": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c8b300f2dff5129b889a2306bc951de1784619dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-DMl0mAjODE7y9e+Fgkd4DC+Afbc7RaVrITZIB8eXeQr3QM2C0BOs75ufZ7u2q52WsD5jIk5wqCT9FCrj8M7UVA==", "signatures": [{"sig": "MEQCIFpff5CA+vywhhb+SZTdFUlsNvgbnIuC9zwP6oQHxc6TAiBCeAdvLAVQD1ghrXJBGWK7cGK3UQ8Pzhq8hxmYsqIaeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzp7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo24A/9FOU1bJtPLhei6waTXlV93QjyWsUhG4LQQk7qRaytCLWLAsN6\r\nrA/+N5EcaU9GpUp6DERX0NcL2+X0QyKveks70op0motrHd35XOONN2FXbq4C\r\nXGgpKj2NgV94Hl8Nnhs2ocN/qHkt5scHTl8Gwyd/huZ3Kgm1DkB8oxc3/+Co\r\nJyvtcX4QBTUCecAu/FhAMfjnw56VWOTZO/+m3K5KFFp/gq+p3gnF9A9u/tQF\r\noHPo/lsxrwApv4Zj6CKpgw9T8Se90DYRE4Ygl0YMWqQVCCsmkM1kab+Mpg66\r\nm8FZbuQo6QNRNip0jfUFp+d8V4gpDOgHQS6FPdR4ea3PorS2WTeKhRwer3aQ\r\nKnwrte99s110oIdLv76CoVrEQiyBCNiuk2163PUw3A8KIBSQAj8b3nep/ZIN\r\niG3P/6QtMNMihyTzhst49jYuAgVKnqqIl3NN+5H5DsOwkO6ONsawAcXnjM5M\r\nl4wLU9vlngM18modptIGrDD8kopb1LbOfArecNZ/tcrT77em/kVrDXuF10sr\r\n2Hsm8oF/ouw1vxbGptF4GX0NSL/ruBgBaf5WqFzxpHXQSACOIf0mAYD6tG4O\r\nFxKdfXfa3autb1vNFwhfAFS5O292vetwGFjK2M42tyX+FLmDO6kXzPsJ1VRS\r\nMsfQ6V4iWE3SDwEYi3o9QvMcMATpFJ67Xf8=\r\n=LN4r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-use-rect": "0.1.2-rc.39", "@radix-ui/react-use-size": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "10126ae7acab83a80d35cd25b77abb3cb46739fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-mV6FTVQRS2OpmKUPhgGvGRoaciaXy2IhSVhnwl0Ig586fu/YDckLwRtvNLW3dJ2AsCVW3Nzt7XuwkBeMCg2gyw==", "signatures": [{"sig": "MEYCIQCIPJhMOr6HGrPI8HDFhJXQOftIWeE4fX4kGmfU2M97uAIhAKoGSwd+ICo713K8IfP5KJhyvLGfXXkcIYXz51OhVEW0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz92ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojfQ/+LQ4kQqmXFsTLXppTaqNH2tEdRizk3wPgGE4HZLhCyvt/V70W\r\nRkxQNinl+xIIP5AQwcT+v7/Jop+WwFtOE64sTYI3BsatCk7BIfUBuGIv55aC\r\nnh0RuVmuV9IPe0UAAsibS5epbG9nzlkbAkZCyEtfjzTDn29YMESN71iXq6lB\r\nAPUdAWwMVYTbRBKOc8+TUgDd3rs/6CcAhi+uw1GxYx1PnIbmx4ytR3tjp+lW\r\nncm/TouBnfV4d5N99bXZe5hMvHOVmoEacSBpYqBnpISOfruDvpwj2SE0lGj+\r\nI6d6U7fg7ZGg5PYlyWuzpzlSsBAQSl33ifoySC/dT+uep/ACbo6I8MRC1QiN\r\nMddSdH/5WgaU41HB8XJn9RZCkqZDMGcS2SGhj+ABEA/rUBlStcwdH9VqmrNk\r\nQAka7DL6pMokXHkVtMNsmD8nZiuls1rTFKUrWm1YSv+i6cL349HGwppA00kJ\r\naNA0GF1ntNf7S5ZHit2xMwLgKpFLuE5CcTp239kw+VzhBtcoIikztEzzbK96\r\n549kgBM/dMcX3DqYFhKYGsyNaSZCVRewAcBxUqIU9nOE8ZXHVf9HJBxbE/XQ\r\nm2k0UtqiLm4GdZHdsVWE3syVK8FK922LTY20GCskFYv9OMuyGSzwgaa27M9R\r\naY7/1V5Zv/buIdFKPNq8Y0seDEiGzWR/aCI=\r\n=n0cE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1", "@radix-ui/popper": "0.1.0", "@radix-ui/react-arrow": "0.1.5-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-use-rect": "0.1.2-rc.40", "@radix-ui/react-use-size": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9129467002ff02bc1ba4e8f6c9fc392eedfc21bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-XUGk0iAuaYLm6xRgqk0tNihE43YjAp62a7g6drGLoROuekir+KGqipLURE1tL+n3zbqADpqkT9/rub84WzBpyA==", "signatures": [{"sig": "MEUCIQDEEoDRWHIxIXggUakmYIzH5SAHigxiubywbWPb3T7oagIgdeBDwpdGDBWTOcDWTrwx8axf7YLU25fjdXc/pcZs298=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpQA/+OnS1opAl6yK1jPUw4k6jjX7MPDiTuu3chIotMZq7BVAmaobj\r\nIy2P2TXwfo8BaXLYn7u3/eI7QqzPY9xs2h9E7HhzxYne5/ukMo8wNeyiVh85\r\nDEz+tbf84+9CNpyb3wkC1avzjCbAZVn1cgoxFWic8CnJGPBVYvzZ7ZMI0Uhn\r\nt/5HAW9P01Q8rkPoF+ShrfHB1WZxh+rl8nBKdGzLeuCaQEANZ8rmZRJLg7gL\r\n18AZQm9XCALEyVCwCX9+DS3v9BIjx7TGwJQ/ZCmhulFvT9tWVWimhIfk9X6f\r\ngyjnjFkb4oIBLkBfHwNK3lLji1aDlXKahdt82VBWfjFNPLkMwf36pdkXOMZc\r\nfZUAZK5rjEBgwph37OrGRagzFpLWpECibXFxdisRpaFP9G/3kEwYMSRxuxLE\r\nBW2gnOfv74JCXxB4q9RZHI7tolCsFo9a2oSin6+DO4tnZFMUd61YX0eEQRGh\r\nymMcnKwtWfuAnnd5/vKsvx1RjDjkq9XOkCtks36Gzs1zKgSHCBi3gG42hnag\r\nJoA3dVGIj7Zz4q2BQZngpxKP4p+nz829NEcdWbuHyBn30GFzhlXzta/rLZ2p\r\nP5mOP/Fl9H2kYNU85eyOVe9xN/S0AGpohXV88ZRd/KlcdrFQMsBRVBAqHCFh\r\nBDiajFrWZt8acZ/Fjis8mRHZeJi0ehdx0w0=\r\n=kZnA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.1", "@radix-ui/popper": "0.1.1-rc.1", "@radix-ui/react-arrow": "0.1.5-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-use-rect": "0.1.2-rc.41", "@radix-ui/react-use-size": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d06a9d69d204fd0636cc57dc7ac83f2c95370f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-ADWphjFNV1jkeXKlxWTrlntQPY5ek8X7pNumeSzE2wV+jHrkLd8CU6u/xCSYNDGXhoSCAgGAx7eUYz4ve26NkQ==", "signatures": [{"sig": "MEYCIQDcZKWmbAq7311g0W52AH9hUbgdQ5639x7LH98iJJ+ttgIhAKA2Dh+xc8SWUSmfevzOmDicaj1HFnaYFSHt4lPzp0k5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEMg/+JXvK5cxJVoproOzhMgnwv+keynJzwhhLQyxRebZLKQF+3oy0\r\n4htulagTonWFY6QQgnvSC+5aw2MQ0PMzM8iqRHRwrzfEzGuLNLt4uPHTNj9j\r\nIpESlusI+tRKp/9St3TObiwG41qHdOzFvi6SMqQsw9G5t0SyTM426Ay1aEAq\r\nX4I4CMroUWIPnnUbWsTQ7INdeA1kv+yJMsuXWG5+Zlyaofth4KE4XJniOvEM\r\ndWWxEMCe77sqzdGgTYHFaFgLbsYbsoy0c53fKpqQkHYozOpThUsCZ31FGxUO\r\n3cKBgRyMTKFtuZneMZ8tnieHkdLPXDZ7jnIbpu96li3ovXu6SzdyFWddIBOz\r\nfCtfSxTU8BAMrP57hQf4UycoH0Hn1cMtPCegIkmAwwGQoIr1H5I3BMts1Nhe\r\n4mKh5UxyfqZKTrmC6xkloelOis0t8YID05jJcNyXkvdn7qekG9uTagmX5aIS\r\n24jdmulgkhnV1kIguzbaJidCNW7c9mUoFTolbbC5ZMrXgwG4LKFybcaHLdDG\r\nIgNnnRuwS0Q8rmYwm1u1SAYm+dW/yk3iMHjnaN7TQHKTSsrZD9F4g30jtLlC\r\nUaCK43jTPEUVl3vYvl8ehhN2QjiWW3ad6w4eZUjoAHKAS+ADpBrERCbmdl1Q\r\n1wcayTO3Brs+iirElOeX6pGJaHYGuoQpa8c=\r\n=NlVt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.2", "@radix-ui/popper": "0.1.1-rc.2", "@radix-ui/react-arrow": "0.1.5-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-use-rect": "0.1.2-rc.42", "@radix-ui/react-use-size": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b477d2c9dbfef3698cd102fb07e53f78f626cb5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-Y6CAVxDnmLXRfswj4xQG9T8RWbLsyto0W98eb+ZFlKKvkjuKD/XWUY12ChoWd/6N0Iwp3qsNmQ4IDUSJhByqGQ==", "signatures": [{"sig": "MEYCIQDGIWMy25t7M0rColTFvTZi98Y3/ljisUX7l/HxaYgzXgIhAMzEGqNv9eZ/wwP5Bv16/Fu9B6W7+TEYSkU2k9r/J1O6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvd3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfBw/+Ov8jyMME2wZDmyYPSqk6P2medMhM+q196+1/Ll3XdOa7sw7K\r\nX8C9otAquNHP/3IK9+9xwdIcniTyIwU503GcjFyR9lasFjqAAjWfuBXO6wXT\r\n1bLYjxJzRjMjWSuNXpJz3hdqsMXNDoANoukgfq/wZkEKs0BXzWHzXqvn9jMq\r\n8Gu2dkvxHWmNV3FmCH/uJX0Cb32Sf96PCy3N8QXThm7HSwbgj9RK0FengmAQ\r\nSiBoWyIOyv2FpV6oabh1JUFdwu2eWi6amR2vem1whribU+gGsb7/QWMUryIg\r\nSs1TCjBjH33VJ4+Qm9kgAZuGwGFB7Q42OABRp5mJ6YaeE+1ver5xQVfNLGgJ\r\nUJaHAfE1DvQUbeS6c0qUXGhUqraBHKzKofojf8jtKyJP2utmo1j9di05IvMY\r\nBcz9RuRckTmhYeYySrkW9+VfrxBtzYeKVnzhSorgpte4Cz5Nu3FFhS5lFa88\r\nfzZQ4CbXjyhRxrEhxoc15MD7eFsTcl3iHnBuMj/0POgY59eCbEeMN49V9D6z\r\nrKYI0NEsh2CzxYcZQ/8F7kUaK57jPpXy0bMjRiOXD5iajoc+MAVkb++6pkE6\r\nva5vDRyixtCicurxF44Dc0NZkj9EnV/qHBNA0P7bie64x2D5nOBAwRlC0o24\r\nETL64NmmS0En3aFRmPdkVBJUXQ4C7+F8FPY=\r\n=u1dR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.3", "@radix-ui/popper": "0.1.1-rc.3", "@radix-ui/react-arrow": "0.1.5-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-use-rect": "0.1.2-rc.43", "@radix-ui/react-use-size": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6b074149d82269e2c6de139baad73c421c404e97", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>Na5WJbJ5bFL0FlejacFTT96NBrnX0LdWGpYF5MFeH4+BnYn0GskTsVfMOwcwLSOe7P3HLDlYsRl3rJovFqAHQ==", "signatures": [{"sig": "MEQCIAoO2pqKY4OC4LLRNUGsmkYq5ZCMFTJkKf29Z2W6xalyAiBZDFqa6WaWEQ73EvrlYOInk51bwPY0n5Lxx95wrcBvrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6ZxAAgSwx5iTL1yNbs1Jb7eQsmd9dRdw2zrcNVegsqPP9Pd1vzEuV\r\nPxSKNO5gKyWbVflDiz+rIGDsKeTgarHAt9havIAbF/owFR70bY9EqIzyokkI\r\nBChEHf/8f5uDTqKZNL2Y7Yd8WgfWbNVrbxYEcm8M7Jo5+2kqXP+Dwlu6NoXc\r\nmKad8pDTUKeQs79M3yxFkT0/pd21v8BJtUVgiGyKhLjwLt3/CPEdHOv8gDyb\r\nzjA7cozQRlhGKN4wrIlcDUjibz3kVMWrvZSRM4ZSRYah+8R89GAkZ0VJPIBz\r\nKvW8iOHVH/PAibhKtEVvCj8mKAhfBI0hs27QvT4IatnFmMz1EtImkr/NTUQD\r\njnGacfvIxHaPHG679IoZpLjDbDdD0WAr0hLTPAArw9NQ1SkaeTtiBz2v+lfv\r\n/SWZ4GU/b26cxeDpWHYXGsCBlYuj7xu4X5d9kE7oYNtXQhPFjoPTBzgbDk8S\r\n2gO/IqN7EYkQ/auNK7HIGoY2UgQyL3nl+5+v2p1ZKIBg/Dr0WznIkzcAS/5d\r\n9gPt1yCCML8OcUT1vUDwcpwu+pkfSwKsJMbczyCDncZPo0hFt8dy9lgY29p4\r\nUxJqpnriGUal6C/aWCRXq1d9ZRUsda6zqQafso2A0Enc01jtM/0JJkh+Q6Lz\r\nKQ3pdSJvvwCqZ4c8Tt9M6NwjfVcxxdeDOkA=\r\n=idIN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.4", "@radix-ui/popper": "0.1.1-rc.4", "@radix-ui/react-arrow": "0.1.5-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-use-rect": "0.1.2-rc.44", "@radix-ui/react-use-size": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f93135eabaca3351929abbbf89568290cff0dffc", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-k2ajSFC1NZeqJXEg3ZbWnhSTIDW7ObC/qAAIdtOlotk/vki3o9v9+DVm1pBcWf1somJSP53/w3gg3WD5S9hL2w==", "signatures": [{"sig": "MEYCIQCKDVjknWvDOgCHBeUddv/lDVLdunp2Sg+MJYHzB9TjeQIhAPa5wyY16kDnB4ydk51HqUfc6KYVfOTtDGF48wGyOk+a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYtg//WxWZJwi0Gm3wjUMoGDtteurC04LKihPUrtdIaCqanqu04tQU\r\nfcCKG0oEMBgZjBD1B++oRlM1JX0sR/sg2OSge+5qmSGMTgWVDXNEsr5t2DDf\r\nj/TS35v9HC99FnaY6XJ7vsmEO9+p2B4NwZptV56s8ZYx11MAlvrtcC+9lQ1x\r\nInCfYxLEjJGQVSutT/Cz4wj5b4hrQfg15mxePi91genSvV+Mnbragk1yt3V3\r\n+l4Va7J4mr5aCq4aRQn2gBuwCu4tLEKExqmM+cGpvwz7AUvXVsTvdFgsjU/0\r\nOWYZxWfaqAvyl9tVa9m9FuHbpNIzuGCJ39RHariWz2ZwYRVpBljWiAFkOazw\r\naTE3Kw81zsTuY0UimBUohTQC/hVeHzGRmaz7c5RWnvjww2J50Bn9zyTUMkJn\r\nXTlcLMt+DxphV3WjoFfSCrtaF6AgMbdx780HzY3RNUn12BAV9KRKQbvoU+4s\r\nNc0dNEcmVf+gbx4Qq/Ak/kwGF+lyHFxkWXs01+lOSNz5vqx/IoDSLrL48I+y\r\nuco2SDc8zTR4W5R7kzqqtb/l383TKOx0HRdj7IjXrEHM24tm9Hj3maUImuwA\r\nf1a3vblSoi9oZsX3c4c/sW3MVbeRdldYA0xLbwB5XLxyIcOy3+QLqgpeNy8u\r\no3vaMfJhldi124qufiuAPmKt2QBQN8I/tGo=\r\n=rRm5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.5", "@radix-ui/react-arrow": "0.1.5-rc.45", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-use-rect": "0.1.2-rc.45", "@radix-ui/react-use-size": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "98c73883979e5f21d31e3a8d48078ce79a7d19ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-go/4h0aQUhp5yuZUOrSGdQLnX2RdZsq7z8H3rC3e7sV5mXLEVeJ0QTjG8dVH9vjBRezywZ2YO15rdYvJ+oyJiA==", "signatures": [{"sig": "MEUCID4P1YDvE5fG7TldtJlcoJUji3ct7D7gU9A77C6zwI8mAiEAlurkZfLXjlN4rLn9LSux8JNNLLpoHNver5wuTLVSVG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9eA//Ru7VbSJQ9/td6TRGehb1qHHT4vzmwtC5jBZUzcKIFjhXVLAC\r\n28xFIFD7YIlahOPotCuFeTjmilQFJzOGoVdRDHrr6Yyl79FF3C2m8YmieEeq\r\noN3KrFXZfa7VJxl+eufWSXGjq0WjEW0MlVOPDVczqXdHQC26rwgriaHL/Unl\r\nR9H2W/dFQL1sFihANStv49L6VmDn3Ag2OpyhsUYoYdwPro8UP2ijmNvNgG4y\r\nz/Ok/vef4fLgwbo6snKew7sNbRYAoL2TnAw8bRrdvra2W97y/aO34vZHr04Q\r\nsrdub5jLccep7Es8BJCKutB0kxTGPXWYLul4tZEDOraVkGqzGaE5GYEwMFGt\r\nMrWGVdW4oizy5Wmcj+Da0OIAgyV0vqZdOOdvZwqgk+yncISOJT664rl/BUdg\r\n56H4JrK2TWhoF21ttK7bZva1Ndf4P6+4OpdHeAZoYfF8GRtWOmNdvHuGmRng\r\n1LN5c0tVKJ2w/zWup+Gm/W/BYtBSijXlZ45sI4WlJVh+l2hmmhxQFVS8jkQ+\r\n2Py+NkWM8WUQ69l2VG4RuEEW//nwhl02GnwsporW/Ui4nY1pGtMezGMirCNU\r\nhSsiEGlu41c4DLXId1Udnd1ms1e2so7oK/P0ZLoAhOZ3+2Mxgfpm6nTr363f\r\n1sHhmMMvKsgy1HQNxl7ttJRowgzp79qH/iY=\r\n=yVuY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.6", "@radix-ui/react-arrow": "0.1.5-rc.46", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-use-rect": "0.1.2-rc.46", "@radix-ui/react-use-size": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a994d09bd54aa497ee4c5f8f8d02c703c42abe16", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-XkSVjMIeQWJM37jyOII5lj2tn4b8poquyprJfESHl9wHi7/D/gFG/nmzaN8JkkGmsITPfagO7BBxULGlv63Mvg==", "signatures": [{"sig": "MEYCIQDd6iGOHZVCZifBkeK+lEEbisRNcObbTX7rUMzYZ3WcNAIhAMz1UkYZ43F+Qt6s6BYFGWC6RD6X8kgaLbeD+D7okkaQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0pg/+KEWXacqbCxzRoeCEPhUENqowB4Syj9t3WqElhFMOmxS+EBlO\r\nmMrFa7PBejego/yPVsP18tfKzRYBqAGBmIcgS0yBiMhhtR8y2i1cy548SMMS\r\npQuKa1q60vkq7/5S+hZQ2th23USkstg+K7UwTHQR/ywgO6jpDwB9B5zt4ekc\r\nS87glYT5goqhdJlFhFLlBGG8UWSXcL0LSSyFI/j2P6xzO47FmxUo4qr2EUUF\r\nTMqKvz5mLRxmAJFokU6sQIO84oCr/ImkEkOTA81K0EyzLrtxRZv9wDv/JHZD\r\nCGaLVZ28pUuQKB2BUvUQCLh0d4dd6HDH41pbB5QZP1pIHY8FJfo2f3EiIjpv\r\nQicsSu5k+bQDC9UA7bpyZoS6yll85rT1ocTZl8HTNASEGfAi1ZJYwVWdpxLS\r\nOSK22H94O0sYO44heEl/f5RC7lsCM0trlKNVe76/gQIZuC8He5B+d5sLUEPw\r\npdSFZ77/TDWRkajyh24EZ/OZCW3mHpTjra4dUDqYyQ3wLAt0+s3E9DO5hh2T\r\nliVt4V7+Mw6XYpkGp485zf7maa6riW6hkLvzsHQtVy5bTpcglzm1ZbuMgmGO\r\nVIc8Q0isU8NFkCozb0McBkB/2J+JD+Qzldp+ub7oFdY/Z4vjFENgOd1TtW56\r\nOmLGQPC/8I82VRot121Ek/w3756gd68a2N8=\r\n=cf2z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-popper", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.7", "@radix-ui/react-arrow": "0.1.5-rc.47", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-use-rect": "0.1.2-rc.47", "@radix-ui/react-use-size": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "058ba06e7cd06f885bed070a77f2b7289e2d5c09", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-HQc54eMp/GyPEbTZO5xBTM6CsquYDwtBFJcbw5sbx359jgEIG+OzBpiGtyzGmH7WJkrMnADizJC48LY3XMAlTA==", "signatures": [{"sig": "MEUCIFyM5t/me4od6ImoCC9LfR/lY8zLwQTTyOYcRbYBOLgtAiEA8jsFi4UiHCISNRSTnX/4Dcg9t/YqnCr7+WADbhw7O7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CEIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoAQ//bXCjnEq1I4YUBPTbhgMYlsU61d4APmHx2NfNNmVvuD9Iwg6S\r\nILAoANMPc2M4khuZljgV4P6MPp6bYlYeOC7hAN7691yQhWMQNq77m6523U5L\r\n1zXZnAqyNMF/T2qRA9SSMppP+FhZb9i2f9e1Y/ajEcy3l6f9QPyuSgreLXwA\r\n029eicvlKtNigIKS2TgQ/Mb0VNxilrqlDAkMxPT5Putzblvx2jFcEk0Lz150\r\nyoABJmdgf4dSKb4nUKTgvv+UgNgL5Xr/r2+yCxsy2G9fcihHkLBqenu67MUP\r\n6i2h9lkO9cgJx6dy5dMP5pVRYEMAzOFXvpea8Sp9vfUjw3OX8qUWGl5JPIfU\r\nemgAngYtuLTA2i1B4SV2mQG1qtttfQPDU00lH8PcVjkyFigxT1ugtzxl1jlM\r\nBmfqvu5f2MyojglZJWkrQufK1yPUy6QWomqDpCH7GAjkjdu2cESVdeIC68Hh\r\nBCH4eMH8ZCRv9KXnWRWleclbsTe+R7KuOwLsp7jrFwJ/DIBXmAG6KDlR4Co3\r\ndCi26yAKfP+Jv2Enm8DD0rShXCGvRHX9DNZZvvwntOuuwlqcj7ZRKhejr6/F\r\nHsxfxtsLFe7JzX6yIwN7JbduMbaRAVe0zXSd+svIrp2Uz2LOjA7WafHfb2y6\r\nCk75gxsMBvYITgJXMsrGnI5qI6uJu4lldzA=\r\n=FGxw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-popper", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0-rc.1", "@radix-ui/react-arrow": "1.0.0-rc.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-use-rect": "1.0.0-rc.1", "@radix-ui/react-use-size": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93278b3644d1988a906f2f52a24d6ae5bfbad477", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-6VSlISj84wqw1+V00jbfBmDrSIhRH8ZFHnMRIaLTxSayU4wlvEtAJg2B5elp+eP81kEeObHUOY7//QtDRkOVBw==", "signatures": [{"sig": "MEUCIBKFiIH3BHFb+zopY5MQOPg6YxK0cKfGAXo6v8Q4ClhJAiEA7xkD2k1ow7dQx2qlCgf9uupJkQaIt5tytsS2+JUKZ40=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXRA//dVFJKVc+4JcWtmRH/PPm8MwWtmZFovu+T0KJbcchlL35DYcC\r\nfft+sZx2v60OLfX3k0HrHb2CMpaWgRqDFTSaRNi4bl42LyKWDaZg9sobOnV2\r\nzvebv0aANpWA4tUO/rkjGhFQMCGrkXV3TqVK4zbqa7jDIbHZE/waaVNtspTD\r\nWBYIv8esxR5zKwmN+XNyWA+KehtN8WJTOwAeWc3QInsxgbcJ9QkDbWbp43jA\r\nqFofYPHR+T3oEHjvsKyGzTCmX2nB5l3F94rgvUT3hUNDjZH/4GxftkmQZ6dP\r\nXJr21ClI9WY+r4DdEve2l+KCnw02UgSyK+dAS0R9pDzTo8H1DrpVkW3fe0nL\r\nLUeO8rGM5koa2uYMtfpcBO+LdsK1nRPZun3GvQ/gWpthjaGho/6WCtXPE6Ss\r\n04oQQT4XItvFEbwt2FftVe2CTltTrTuIlJoknBeUCSOfOW5JjWeEB8IyU2fJ\r\nH+5HPzpeYC2e6yp74hxZrtcqomvPZ3UNWsYKUmN0lC8Kh9SDkbrhK7nMVr8Y\r\nQfcRMfg8d2NbpB2rXDDh2elE0nxzzQSyurI9Uh3Lz3QIkJSe8JVNSPn4UTPK\r\n2jcTQQtGc1l+eb+5kGrJcIlREj+IJM4OIEUa5zmKSThz9oc97pAaiQ5vANLf\r\n/Zo9x0hI/cCfoJtInIgMsjVNL5QAIQsjsq4=\r\n=L1aF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-popper", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.0", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fb4f937864bf39c48f27f55beee61fa9f2bef93c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-k2dDd+1Wl0XWAMs9ZvAxxYsB9sOsEhrFQV4CINd7IUZf0wfdye4OHen9siwxvZImbzhgVeKTJi68OQmPRvVdMg==", "signatures": [{"sig": "MEUCIAQvPvJs1k7fjc6LREMQE+LC3Arat3lRQl4A2q/1UGXVAiEA7ldQEoBq1LG638HvwY7e9LFBOHxJTNhc515D15Nzs94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouWQ/+NlKlotqdH4jOda/df7P17NTLiF1kcgCLLq6v7630BW+1YMZ4\r\n2/45M7F2gHwZ2DvY8yM5sKI3erTu6TDL1ExaTIDQVzh74OROH2MrhefY7fKS\r\nR/820+KfWLZGied31FgsJN/+uMgD9YujyOGK2MxGP6pKDqL4D0Zhd4a72l2z\r\ngZp1ck2mzJtVHJKvA4oxVG1f/pIYxayIFTbo13plTCcFS///Su3wrIbmpPFQ\r\ntowsh7GTo6THyjb35wRgafr/dQOmyLuaICJuaeH3FcRrsxBvkJHzP5/17COo\r\ndpmd+Qn8vC9XyZd3ZtOhjhfRi0FMZdiLfUilomWsCYE/hylqJTivGh//D/7p\r\ntXr7qWj9ZEo7AaQ4KAdcnSylmo4XznoJNb7LWuomLd5ImnnaO6tdh4eKybSM\r\n+4QgIbsCUgpRJURCkDSpSudSu2pdeqP+cTxH0zKhx4ONtqrcG7P+yevUQYDf\r\nluE8sRdAmGF6EZeEzY+TE/2bcume1oXwignxv4wv+LJg7n3UV0xd9LUPW5WE\r\nkZ/O4mvs3m2aJzn1EKdIuXveZh8bG9SdDG3oE459tvKuHxZYQH07t4dtmyEK\r\nAMqZDzOlEzEDEVTQA4yyoyMWRP2Yb+B8r51O9SzmQOgq898sVOx4DOQvhVZM\r\nP6whNiQLUB8SAryESjuKFpRY6wPeOE6+SJs=\r\n=fsAj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76b7f6530a0df3dcc8ee454b17b53df651e7e485", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-J6X/0e5SEGVsioKfrXcmWu0++ZCZRfofKvCu9Kr558LdaGxSFN6eCNNPH31zfjK4nPG9ZyATTfKcOGsAiVNEQg==", "signatures": [{"sig": "MEYCIQCoCV6GIg3G0XHE5Gh35UsLNye/9Hkcrg5UWVMEzlDSjAIhAMzfXm/s8uV/FOgKEPW0Hzly/PmuklWeQu41TzNYgdE1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog/w//dIvN4h11fy2KlF/tGcUylSABdrUa6gg/O75G+k73E2cRvheg\r\niMJ80o7s5eJETPOZI90xtJSFMOOCRjff/oIABE7nHJ9d/Wc3xICtmesq0EBK\r\nLekEoG5py0m6awjngnI5PDYFSU56D7PL7iNnDuy25nq2Zzjvb7t1da0/lLiU\r\n/Fvoej07Toy3xj9KGup2hG7zAkWloUpRD7WJi1YzxNdZciqGsYRAAmk/a17X\r\n2FOXt0bMYakcr0gSYdyHPNpccd7Y9VoGNGhkJn4HIzFhLXCxtvlrIa+1EPJ/\r\nSh1VZuOkRutgFtshW4+EvEsuDWHpE7Otkbi6x06AQS+h/3uXI5ZoNyE5+mPv\r\nHCh+o6AB2hkACQlXjd9mo7Xo4Qu0+3C3ZDdkqC8s6UD2dyTpfwn3GuNxPpTs\r\nU5HkF7JcWIfDxPPqc5UQhikcMIiEjA7OdWnSkrw8bsUNj89MMCyIMnHDiotn\r\nCViA1jaSOyL3WUQ3xpuZ1frxc8Idp4XiGvUeZ2ywo9Qmjx1K4LRSNKFs5NlF\r\njk6l/Nbt3vGo5oO5X8KlvwY6hLWazR9xUxIKcv3xwiqF3gwHC9NrWmlWQ353\r\nNbrcKY83iYtWheRy4MAp8e5Rf+22z9a6wXJ/bJ4fM86Gki1zP7fnmvJusfFI\r\n2lTsD80QZkAZ0LVBZ3aS+drRBzrHhdAHn6o=\r\n=6cQM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fb38c1621360bac982860ed8368a27196482a632", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-vn2gPb0n8R0aGKg8YdzJMxuhew5GYzOkgv+rrkbSHckO2HDhtq36+DI3h75gBdqNV2hqgVcDHPIo18fGgNlPoQ==", "signatures": [{"sig": "MEUCIFdzEaM6ZLBNg/9+tGOg/D7fJsiTCYTxgfbEFkiubddHAiEA+y33ZFKKdsFn0azUhWsEwM/zPu0K/OKXFWXV260AtaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosZA//Ttfk8MmD8J+JO6/wPPZI3UXpndAXHZ+DHeACzAOiv7f8Tdln\r\nBoCUpbaxOqHmiTL1UsRVJ/vv9trYVeXCU9s9ZajaGXbh28L+ZQhWMu7KFZSi\r\nMr8LkVIqf3Hgr2C3xaeHe1OwdFZaGbN5BKe/LZLjzXmtrspTUNTa3k0XsnJb\r\nQTl73B1+wjkn2nVLqWuYpK14ymghoQYk3a8rvV2HezLnX52Tq6EVDty7BFVZ\r\nnhrXpA2iOtX9Nfy0KMHYOReHy8aQ+EIs4qd43GIb14ocAPFViBmb0Wo5Od85\r\nZ/iv4d8aLQQxTOSW7KbW2+cN5ekGGcZXnpAoKs5X1u9LUhE7kyBD0xckifpP\r\nfNez88sU4ueEBmRp63n0FofvmncFSdSTvKXlLw9lvIt1pLoDgb07zxqe9Gkl\r\nob5uqz8Vs3OdMx5rizuczKmAkENaQCVmccEdcbqRhk3vxcTy5ca5rARW7+xk\r\nXok52E/fppszT2c7RJ5CJBj8SgZoNmt9L3KiLgKyjNV/PdNAcPRK3xvOYqWA\r\nwzezxFBsj5aFPU1sP7TX/NfTbsvtOkTgnWkSjypqww7Z7cauSU/3j2Un01YY\r\nJqJrbBeLQQkFi3whCgvAXK7AlpzcvRj0GAuiXgMV4N2TSad42t8gLef+o3Gh\r\nfAhphItG/Fg90HR+UrmxFUI++Wbu1w3IY9s=\r\n=MJr4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.3", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "30fec995ff71496381e5359fd111a23f07839400", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-HsFzWj/DZee4kdozT/dN2/Nu5tjRNGWiiybb58pg9lUdbHHEgvlaB+3e0CphcsTg7N6gf+f6TD5d+swl5nXEkg==", "signatures": [{"sig": "MEQCIEWIPYmEsuc8p4wvm8jisHkbFTrS98VMqDylr6rO6kiwAiB7wlzGgm3dg6/xKESPX8QPJhUNvGI37iWUGUcc9ZWjDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA6Q//aty3thY4vTorkpQqJQbGzmrqQWiJYPgzI+YuyHa0430uqpTu\r\nQmtUTVTZEz74oe02cK006Odcz3Y53yh1hoEgDOOoWseCn+sM5odv4IilDt3Q\r\n5t0PnsikFOWUFuXVjt+hmIFVx2Nk7CrhCUS8SmNygUFEfBMC5rK4ZIMyS272\r\nuEOq2ev1G9zmaKtTwXBo4DmVLTA2yaSAm8T/upjHjBUmwlpXFzhStntY2IIP\r\nHKkb8gxKWOjHtyObiOHY0LlHKXUx5wYOpD04TDZtKObuaDnZeTunuHYJ2HfO\r\nofaNJxJDkXBb4qsYrGH32fezO2SaYaQsPMPS3U/kJURWDmvHm6oaeA04i72A\r\n1LQBeCBs2/Kc1ZoympRP1d370hEdhpzag/o+1gPVgj/lLyoxqnFUC0wI6FXW\r\nHtq3Xnr4z95aB4C7VbpYFjVP8rKtpJigd+LPsVVdz2O93q6uPnfrRYnos9Hi\r\nUYdfMHyqcyDWtFP/11NXoLt0xvoOCgzMqt+XVlMdlGyNNpJMEBVGDS4QG5rx\r\noShkf6VDaUN8tJVyahOxi+nQvIQOF+y73SUvTTXfEdxuOJtjg9hnPz6ltEIw\r\nX/t48U9QC8GrqgD9zDr4qskeuzkMq2MpaB0z8EVYOhZgmCJtOq6FGtglP4f5\r\nNUvEuGC8/ohEf22QLeiQh6aklaYbO20xdSI=\r\n=SCEx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.4", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bba44d6e1349706e155320f7c9db41aac18d1191", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-cdlD0HoUTxT+H6QbmUZ6JfpnT4gk4frqWa4md2DPC8gPMhqAdMOTQKXabFceQ+PH44SERZAyHeVC9o0keRIXyQ==", "signatures": [{"sig": "MEQCIC5ciTlSOq+gUrsfHXzU9TBYqkm1z2Eob2Xpete6MkS+AiBbKOkTkAcFiCYL99BAKILPPjjsboleuTUNCsgfWB8O/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodpRAAlKciD5lTHUkdkNZZLzsUP0UNxP6zmdTCyduQbbChc6+8sOSK\r\n0y9qKAVdwxYzl4ZyzOEsHjmcYaeVCBmolt4DVGRMWG1sxZRqlF4g1stasLUT\r\nwDIMT8ZNIYFBCaG6G1um81TPJWfpcHiFGxqYvwastZGNbDfV+4vCX5WlOOdG\r\nioP+p20FK3s+q80GqEeEgL7S1XImZJ1qPNeX0D9MIPV06fQIqHtf3WNj0aKX\r\niAehrgWJavuj8Muy4i+BloTECiG/RTMvgpp5jHpokrWD8bh3aOuCFFrKK8is\r\nnuQeDKDbkDIRYp2wKP/NS7BGoTKq0nTkY64+DQGezm7cVOkefNyND63K3mUu\r\nsYeqVCczutbCB7SKTaB/Us0QonbrzhhueGyI5nYTJ56KcALwHhOnOrNO9Xdc\r\nCznLwgfw6AQ2G2HE5mQzH7SjVAXF124MVhh+tkPbg6XzwYx2NRTkYRJcBGAQ\r\nHin52o/2JG8U1W6jTq91QHu+viHLuD6lFmYt6UyZsKmHJ4h2U8VIgtZBMVNk\r\nwtfCF3vybNRk7rvePkwEBAyNGtNCnKbWMGRR3ej/zqXfJYsJh71QvsDbdJSk\r\n1Fx+YISOF3bhGl/rYi0SabE4FkNTRz/8g0vQUlUYDUwUWgBSrXUaXR3iLkY1\r\nfEPnrMlWCDi6tE3ZunLhnq8f6oY5/qta0eg=\r\n=9uXL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.5", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3dfc170c4c6cc8ce5d089c9e49f55120b6b0c645", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-nqMNmaXoiGJutTaVPOidecsbmC6L3YNW0n5qg/XCJVIwZlAMNms/QuqyA0YKPVIxJeqzCHhWWeCVxzy4m4k8oQ==", "signatures": [{"sig": "MEUCIHZg/uUfEEzK9hV4PO44pMxWaTGMXO0YFF8NPTqbr5GBAiEAqjFE6TQUDC16WA5yVN1LCf8DFu2EHSH3/3WcXNJu7w8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH3A//WxweT+etMDxFix05QBCsajJTY0LQ7CSVEMmkr4gsNCuRT3W3\r\nEDr2Ten8TcRukhfbXIYAzHYX7jfMicgJLbvfn5o9m7b2OHPcgp+wLTEVh5kQ\r\nl9VOyxmfthDYX1H7kqm5vUMabOj0/BSv+Zy0CKTBZBxc1sV4QD5DxoF3YNJn\r\nR1elE1j1YFS4ca5vsgx6wKHEPqPZ+HPBZ+JsUBt61DfGHdqJ5mwkK6vhMovZ\r\nIKFbHAovbRttuJzdEA8OjGCXvceoS7htVVm0whf15OMa07K19Bs5nQj4l01A\r\nr8RZd1LPfJB8l8BhW6DZ8232zUBCt8G74tMtc4oY08v780MjTQuVPiu8Hg2E\r\nr731TfZ02mfsmkVcxUFSVOkMFgT6C7e7lLwPemGWWqbFFqFNnIjDl5udja9W\r\nzoEeZhzpfG8iZk3yhjXEs3mJxqECofX9klhp0u9bfXE2rktlmgAboI+KZx2+\r\nJsV1ydrOXd1Zp41Y0gHGy01hbt8dKhsOIblkVOzmOyhXVb39e3DxWO8dBdLO\r\n23yfJ4Vi9Wh1ZQYVVIwYLEXvQjtAaExCMbp8Unr1LwRQ5/7QsQ2nQyR+H2Ez\r\nhuPtOGPoQuNlLGo4Smn6gZ5916NvHbFdhs78bqPmsRDSNU86gvuQbnE6cZ6Z\r\nPP6Sm2HQ6Z0Ti1c3ROMFpPLXgHyu9f/saBM=\r\n=VNzn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.6", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ac7e1c12067b6dba1167a39f2ca2c5adefbaabb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-6TwJyDqX94l6Gu3HWpFuVExH+JLEeR4DB1+pB1aFvTchvejGT3R66jDnIy0X3VMV4Hc+QSIBanvZV8qdc/gdtA==", "signatures": [{"sig": "MEYCIQCxNfpy613cZzNi10IGR53uNWY2dso9+b1YluqmPPh0iQIhANaeT/tKaDPi2iRwCrf0hThSsRvQCbjxCzXcQWHgFo9z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonLQ/8DLnzratmvy9HXDjNvD/DrxqMefucDuYfFseWrpwSx1iWxrP2\r\n+tIvXiNLQCWOdKGVlFlp/W7hbOrXeH6JQI4UX/33bNryT/DMxumsAycFTjFf\r\nkzOYRtettKnvdHk6azGMLWTO8BxpyLlTTcAjTTGw1nDg+QpyJAyDW7gDTg6R\r\n81vLCyKm6J8/ecsL1rw8diYEL0FVyw3Nw4RwDStSz03HQQkC0/Rj2uEA7LP/\r\nytZmPzMwNj1oiJsLcyOa07Z5jQy+QTq59FdqBxsGMaTKqvPqozwKotPt0jPN\r\nxTYH7phkSDoAfbiO72I32p0acBp4POiySMMrQSYlEmkE/6fAbFus8tdtoe2o\r\nnrZIouG4dkwTS/xDoghZFLZgu1q6wxtYNlbWWrDEQWL/+CD3/rLCrk7KW6tF\r\njSBwwKOUmbT86e8SiW17DN1F6QsnRG1+PKcax0+sdMUmHB0XFtJqv518h2YJ\r\nA8PY+fjROMQUoxeHyyj+nMu5Di+Hywm4pp0cTc1JuWvVrmhxIqq5Y6e8up9B\r\nKvAFREYyTyEmnt4o3ZQ7Ttb2E+M4PgH9cUcZYtBPLrR2XE4xwbvgl8A9Duyo\r\n1MLVkJUHHHvcuqupX3t2P/wvE/tSNgfJHqpOG2z/VRkvHFHlql0uRSoOtTP0\r\nA1DPo3brP/3HHXR2oC4VUTWNDXm295b1l+o=\r\n=WE35\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.7", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16464fd9a4c21f31d98ef3e1b7852cf3083167bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-uLaLMFN0OdaS+74VCtgxgcR4JLZRNQlDHdMEB3ChqrFk8YtZ7oe9d64doY2RVBQme9tcr573YZEu9cI1hnwGBQ==", "signatures": [{"sig": "MEQCIGVa2/GNW6ZzlEDLY2iOxFIyy43VpMLsCGsMBY79BIQVAiBZwfV6BUY00QjVOjo2dO8IdmWN1ihPGgaF3uSZ/QqDyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgKA//XupZVf5PfwYl71L+KnRjE26A37V88e6yvuUggxBQCmwjAdzC\r\nxgMcseXpmfIS+aEM1YN9OdO5R/izfuHE/IcydacQ1YvfKTCxCt5Fv2wCiU6Z\r\nYwEMtdK5vNd2nD7NdjdAavnJEZAv4L8XFg8t39tX2FitzhD34Uxu/akIVV7z\r\nSNiy3mpxFoAD2HBgf9U/l0UzSNbI1tH0ztfEr9idP3EKSsSX8ac0HmDANyeV\r\n/txh1x9JIqoLzbyJ7mRR7BfSbpAz1feThtNeUKhaewsAjm2F3r9Ywn7VU1/1\r\n9SFekbTJSBCpcBlqgByat0WlKVsfGgtI8JKY33Ut5Uv+aC3jmX4iwN4BdcvK\r\n7bEIgFUbKDdYb/xZf2PPwBFpBpChGyO7bBtG5/oB/rek+2gb+V3UwPPWB29T\r\nsv4NfiHjTbmWA2nEfMNitg0LOHwBFqvKZueY7eiPbjFlE+QCe7S5j/F/xq2z\r\nKDqenU8o30EsnFVu79Mk3n0EoBlRS9EW0m49doM3d5UIJy5mBZp6reC3RWbc\r\nwyvZOvGgGP+wVowWtFzfC+O4BLl6QlM0ji5lZHNhmgtDGjtMg1yP1z79Hy3N\r\n9X30cydBDCQL3xgq1H7EB3sRGQKl065iKn5Xmotx+BMVvUB8eXKAE7mRv+ba\r\n0o8qdBWDPxBBkDBCpCcC4Za8X8ClHub0LTA=\r\n=LEIG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.8", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ffb5480b75a91c1f7720d9fd8990bac1501b5b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-EQuvZ4viBdoyxHNjY5332EH8hCFlYVGl15UxPZ6LMhs0DVp4uo5EXAbUZK1Phd1c+raIqjAw+41sCMNEav6Y0w==", "signatures": [{"sig": "MEUCIQDT6FlSy7JhktK6WbWMTIyXSUY+Cj0birg7O8xAW5Mu2QIgH5aGLkGA5weJVoCw5TnP0OA3eK8aJCC/ESh/mxkH66A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+glACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocRQ/+P28U66Yd8G8jfzzw2dcckLYQtjK+l9fMcJSo/aNzG69K40GC\r\nCCzfmbMDNz0RCOJ7yOzP+m22I8dW/7WSWaqnkSScnaHkz7FQe3/ilz+0cHpw\r\nk+mBRCqKyWP+3hSAHIZMwHQbf/F1wsVBsZZrzQqkfFLGO0Hud2TYYeuygm8s\r\n/mFIY20PYV94Yj0Rz6y6i6i5fMtyxSyJpq8WsKqp3kANcnAf3JcbnFLl5UfU\r\nhI/Pouidihs8jgM++ctjUn3lMVBMFZg2gfHu29gvtDRQCIiRlz8s6ODgh+3R\r\n8x5v0KFxYw7lLHA3MxftRTIHAByIvkS3a2fhpL2lq6c9clKLnUit3OFHCrZZ\r\nZ/GJFHtDfwpuZhvK7a1bsIngbwZkfaXiY5pe2bSgk3qFcnfF+oghbmmzpjkR\r\n97yYKMGtgcIX/b6TnMRAAs/iW9LjmeI9C2a7XVGD3CTyPgEFTQ9W1R2e/T4i\r\nvBAwyt4saM3KnogZs5WLmAebQIg79o+qwvQEzgDmpmwVJSk1YfNQRQV+csY8\r\ncHBRob/HdUuek/31IfcZX5Z8foZhibejdSz9xmhlQy6F7p8UjhUgFzterJRC\r\nfStSrV6FXn/Y7LxJEht+xS8O1FLyPUXBkmEBynNBsYN0hSDl8unflxM1vwlN\r\nj6PY/kuajUwcZHbRkIa036otg0EQM1dczbg=\r\n=WZ+k\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.9", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d7351cb552c8ac747dbf833c44c193fb230ac5d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-NTEheJExPQ9MCTxbmXAbRCoke6S5zDIPJSBU5B4P7f0aR/DOFPe7N/Cz3EaTCBblujmfwLR/c5QASSxmQxoKXQ==", "signatures": [{"sig": "MEYCIQDCWQNJl9xmaB7lLpWgN7D+uQZ2DPu+9TBx3FMtyJvU2QIhAIfLiCtPp9KDvAfdqvooWDci/4dMhxD8kBM1yXzTkPdF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoqjg/5Af8zypYUBqZLbrma37GsHfaReKjlybkJ0/HLNJ2TUT0wQR4l\r\nYgDRApmWYmo8ooWY/tZhBr/Ky8Ggfm0R3eSCXTrNvZPp4BULmOAan6n3LduU\r\nSyui3WmyYtR0rNjVPU0T1kriceAH6AZPUAHBKVklyrSZ4wJOlD9R3hbkrURu\r\np58KfkcWiEzxf649L2pHFKbUnXC2LYiMf1JHa0Q/vYvrfgq0GloW6WHkhL+B\r\ndT/0ofQjdtlehCd5CrjoPaR6ztZe7KEREntVTzmYecQKr+A7sFuPGxCVvLRY\r\nij454CQJ2EKC+CICpm7SYl++Df1G+ZprWNDa1MtNRiSFrdY8za9O5J4gxiTg\r\n4qpVVD/RlmiOJC+/bZepVwpnj7lDaA0kBpn3vh+FmnBlx30q7cD4t9Yj24H8\r\nV2BceRzmAhxcoXpJFzxw+LhhZg4nq3D8Tn97l12FEwJGQRtUmIdhG80cBsTY\r\nrwW8X9jF3VpMtFL6uHv7vQeb5oWl+VLBHvHdFRxymJdgEDOSkn1AbZsZsazU\r\nmgjETm83Hh4qPTa2NRBJn82YMc7hnO3078RUfkJepBwUSsaR8hbCTBDvc4aI\r\naiugbhtPoQF/VghJDM3eoT148fhArK3lMcCmQoip65KHeUKEC842aXyv+Un/\r\nPNvb13RCT0nly/lqsB7PFrnBJhvOmBuHWrg=\r\n=BhR2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.10", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "11406aaba59a22c9fe79797e50976216da528f26", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-P422t7Er5/JgL8NXkTAIkBOOhC7bXnpMwiB+h3+bnZLdMMncFZF5FO/f/sKJ/3aJyPaIktPYIrRoPrN0BO1CzA==", "signatures": [{"sig": "MEQCIHh5w8AQ/bgGybPgnUgN9x5ecBqquqvJfDdnKp9H4lXJAiBVrHq/Ig7zHMFpE9neWXUbb//xWWFM7k7aIHzjJC4a+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRAB5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYUA/6As18vtmpJU1dFx50Co+x7VxpElNb3//e+nAsUIZx8F9plqmR\r\nsY5/1sHl2VFIz3aXl8aFujecBR0Q6snj27EZhRWLuYFdRXo8NChLKIA1PvKk\r\nU8+gwgNAQXCGr2tt3P5vNv3mZegx/lnEsZyHqf/DUrw5HX2t07G9FkXeaQ3/\r\nERi0aXmsbGqFHulB889YHnlRUkfgCPqewvXwHhd/kMKYOrmMZi5/4iw0ONgn\r\nZqrgAZKa3hT+9esm8mmSxFL0H6WXDKSwPP/xfWvqQJZEyz9nv6+C0UHJ43vA\r\nFNBtZdANmONoElV4h0gqBmV6lbQ+9DA3KnL84ZxLXrzRuAxvPWxfx06t7VA4\r\nA1nrPVZCgxxRSX+XZDP8tmC7nJfSytnufD54qPmTM38zszgBvnJT9lR6CUuy\r\n/NRIOpt3lEwnbb6XYPtNbNkHOz55zmRwI6wJEhf90Z0SeNLDNas8vrNeldr3\r\nlBJoVsW0uaVj5ViKsMmeA3nh3kOPx2va05bKLh1BJQF+9EiLdYIL8vrmnKY2\r\ngjkrIGjyaf+jqgrLh5DMDhF7VyyxS/3aWoWHbp+mA5pOdnOTkyqKdQ8fQ+7Z\r\nLsWlX1RUz60Vd7cXxbYq6jdMH7ZAa+vks4OYUFovIoQtikQARW1Pwug7th6v\r\nFSMpPpX8LQDToTv5GsO4nTB4OnoIgi3D+TM=\r\n=+P5s\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.11", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "99f2703ddda9b3ae2395e6c5fb98db3fee53bdde", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-Oq/jx3enilBrWknaQeBOR1S0CNIT24kWhu2UAiW1Rt9eW2corR7X6kO/X3zsT63jBXfYK8CV48HawdGiPW0Ldg==", "signatures": [{"sig": "MEUCIEnV9SFfJHe7Am673VgiXuBruXGd2sMPHH6DyPnCOWkyAiEAxkbl8kRGr/53S4hPSDA0+19tJgEDy6M5MsWQs1x5+Xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHOQ//W3EfbK1wwyBq1BapheULDg5ybcm2pAxszdc+DQiX6OJDPNK2\r\n/3GbawhRJRZaJxDlUfckIrrs0LrZlIHbcMwhU7Be/ik9RSv5RNhgEicaHvrC\r\nSlzlFXebObjBG1UnPAO43IodLO2bSW/Q2iRHRrhoCUBc8ChKOEQV2XdFKnWf\r\nn0pSpYp7r/PvyBinmxI+7wsRPUbNeg4hyH8Ili4zzXOe9vuzcQailFpOLV07\r\nbq1QUkPBpm0MYluZIkw4kZJK5pa+o2r4gI/6B0xJBvsyVokREqZEdRSfDQyS\r\n/GrkUjAFxVxVDo3JDWQ3nhnbSakzyv6xWmWbTwjicPofgDwjuDHNKDSeWgnm\r\nY9G2hJJInT9Dia6s7kvx0OgpdAg4G8UAKQ7B4XaL9rKksRPm9uFnZsZQVPiT\r\nZA5JHj4rycvkf4Lt+wBO0QL4K58dCnAxwUeIj8SzSowmjmJU68bcwhqq3D1N\r\nOLdKLjeAmH9zdW2invgTECLli7P9BH/si1on0LSsnA6FNlmxv3r08LRfLKsX\r\ncjdH+4XuLynX0s1DAlSIbH3Ks4OGGutY5OvhAKBt/IErqIRaZnIyaUyVVy24\r\nvRO88o7o2sXqvaaoXr8XEQtcMGW2eC2AGWplusGkCqDHGyuXKZ/0WrZbmaDq\r\npdjXTxpf+uC22oENGp4vhS2FE2fo0r+mt+g=\r\n=BERm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.12", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6d34336f16f6865838904ad799becc139b2ea723", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-a3i67MB6UOVFUGAev7kumZqWlmR2YZpKHlUvR+cuatfIp+IDItedNghdKnXF0DkboOAR35qby/swo6QcceqTpg==", "signatures": [{"sig": "MEUCIHGMXL9IVpK0+3GMFKN4rByWRQin6qOUevAS4doGNL77AiEA7F8Ngzoh0NiICHUj/Ix2SHuVtqv5gtfz9pUPTjz8hEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRDg/+Ma+QVumG+2Vus/B9cikFtp6Gilw93lXguk77hWwk79TrMBBt\r\nKREutzauGeLoaZ1nBV/Y5tIO5P2r9DWOHKlbLUYR/NeHpvOZbU9zGMA5eJkT\r\nGHoFXXx2p8gy0ZyEd1cVXUOljxjwxplGomLZuvYlKwrxqj4LjvVNdHRtgCC9\r\nxMbS0PdewIxK51/Pr2HiKiFGQxEz3miHzA0DrZkGn8Hm931lNlRo2Do5PnCX\r\nt1FnTWTV2luxz+/N0cCet24Mhk/fZ9dfCPvfaVdRyPhd2XtQGvC8mrM7NzNM\r\nbJiamjnysXXE/VrXtk5Nc4eYzwxiLdqR8zprWFcnQ/zZVT9eF8kKEIdrU8f4\r\ndiVnek5y7oUZx0Tye2p1nyKV9AbY8ZApVXLRULpfudrfFaOYFub+aca8UDSB\r\nb1ntKuaUEtr5rhmRgc+QeqOsyW7QFsPU9cya6lGSklqkerD/6lbAYWOGO2ww\r\nGwypBagaFWsTCKZESEv+QHViRyfA7mkQWhQvYYsVOUzw5TKEndCZz/cqQiGQ\r\nEQ3G3xV6CC713HwCn861igPQVmCXX60kqbsYf5xEMIlaIJUhbIdVi7kt6Hpp\r\n3/CJkUEuML+Hs36TCBDOrS2cMlYuhK7o4p81qtcCzt8BS0g6c640nZ8eycT9\r\n28o0xMU32JHQp/+OTVuUNfR2XEg8DOWnbUk=\r\n=L2Xh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.13", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55ed047a0dc7d16d298666615cee1e4542c388a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-G+6KABdTatwW63kHZqkyH9kO51mHok4VXFGAOIxXqgJBUU3BCuAxHi4TTtHPgmfLjOGBUD0WprGuuFSrLe3K8g==", "signatures": [{"sig": "MEUCIBoP6E7kky++JHnLbVcoyhq65lOlCgj0NLrysDkHqpv/AiEA5SJPKl11xDAf4WKaFic/jVzzAzfyaGQ+whjsd8c4KXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBIA/+Odv0yUsad0tKsgMR/8TeywwgF3pvTd3VPJGuP2B/hubBB+CU\r\nGWINbVkQVDaEkv/RqKEfx7vCXOkxxzIhogYgUeBvKzo+a0Ek+IQbU6anjI6C\r\nZe7dIF6VLmUEWU5XOxfRaZqLI4alr33mTUTPQwlb1qvlDnsXQYR/MmRcHWbE\r\ns9MWt6jAK3nP8FOHe8AEpxaDhT6uFiw7hUWpUDJ3V8Wm7J+18jT7HhMVKgA5\r\n4DfOh0Fe9sW4TSf8hAu6eVnxJEW9uqNObWQVRQGiNNd/0zI7bZLMkVFjMbMz\r\nBZ+6K8yWvB2KCzV1kGVOGwaulsGLwyJYmWoO4aZ+9otl/+k7K8dSIxUaAgl3\r\nPwRj8EMs6hV4OF4GEdXmHApGhkXXi+vxolAD0ffLFpWTKt0Pp8XMCpl154ji\r\nuZt83XEGQDMU7LqHVbR0ubcoidzfk8vDsqQmKyUb8ruvH/RTjG01IZvpmGu7\r\nt2deDIs6VSGtpBrg13y1UhwqPttR9jdfIydygU2/ykIVaAGbM65v/CwvF/Ic\r\nb2tFK93v9fl8zUJHmkN+2YzQWZUiC0G+8PqftaON7oSMWriC+ivOFIl04Nxe\r\nAXja01GZ9PfsXID8zEl68vJ/r6akEVpirC+NNXqBlCDf2Zpuw9y4lq17LCfq\r\nfNraeNSD+lLOJ23zfUTSjI/GIZKZY6n6Xs8=\r\n=MNii\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.14", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b07fb4deac2a784d1426bfb67fc7f213631487e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-Q3DA3xXmv3Ze6YCYJBn4nUA/rM0mS4pBodz7f441U5XOPYBc78VQ+b1pLnZR85OFtMiMJessMYy3ZVQ/8IpzuA==", "signatures": [{"sig": "MEQCH022CaysS5G5IKG3NtG9e/X6Y6RVkz471RjdSwnLKrcCIQDxxW+DOZZ8entg6wrgbc473/Mn8BW0XIANjRyVPHG2+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP6g//RngbQhUlq3A++iAi84yU8O0OoFp68WLsY8sy4cNCJDbvhWSB\r\nfMkEyDw6J/gMVxrNMadKnrdpWmnedyJE/Q/Ixzt+cyOmm2I7xpMLQviU8gDa\r\ndK6xV93Vr3+aTeryEOEW9cSVfHHgMn/tMtpnuJo4+4/KunCgy1MS1aJ43bxy\r\nklKZdBydumTQSIB1GWuhVwBCKtH8ffRKSCTHRDI9DF1+Mtgzk7LzNmlAs7y4\r\ng4QlXqcmOYT9Dh42478I/4GUrAOyiZO9jeewru8kOlj1lhmS3d7TuitlBymo\r\nzkD6tjSebnNvVfUrVUnKmHmIdCLnYRd4LsBO1q+LUXNcV6ZB/6oifE0Db5Ej\r\nPGIvyAZ2hJkBLmoSGDtvby6Xm6ki+HtblfsmcP1wlfwVuxucQCEJyO4LIFI9\r\nkyRyKYXhARX7gIDPGAdYSIDH17L1CTSgr6DAzSFmBvcUYqu1P/E+u7Xg8Tzj\r\niMAgt6QuU5Wjlq05fc7u0h6/YI+AlbNzP9Lk3i1qUsLXlqf2+MzVCVi1ehLg\r\nTvQGC1T/ecnkM8Dz8PgDUF40pKKSI376Rf+dhv1GY8RGeGDoI6A7oJGuDqSV\r\nTh6Id9ZYyyBXEFnLZQb9m8dsggUbIMaUG2V8aTRUMPW87xuX9Atxnc+4riL6\r\nXCwZSKfwEj4yBVE0TDQdjaJ9V7bowJxodmI=\r\n=Ed+I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.15", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed0d51dc01fd123297abd02b9bbcdccfba6f5ee4", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-8t1AasaMLZdNuTLf5fBbrHSc1hCzD5+b0iDn4HCvkiyPvPevXON3UcCyyfc6EKtoYeygmNU4jQEYfjy4bqjqNw==", "signatures": [{"sig": "MEUCIE8BjZZwI8a/g3dyhOqwCRbag12IlCNIZv/lnet8Lj6TAiEA6wmiajkHvQ7znNZ+vQixjbgtwz8NHy62hHCmg70TOik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpclw/+IKRM1k4Pt27IiY5mLMFRILb3okxat8DQpEsRsWDdjFphJTbP\r\nZr7ArXFrsvTfNGWJIets/Nnch63IZ5Gj+u/igK+0zM25yjjO5lGv3dGsi0sW\r\nA36p2Cz0FuejurfonmgDflbeHwpmo/VHeQP7IN5981EbxCQjzMJEO/XaARO3\r\nR1EGnXRoyz00aj3U4aukbb9SFBDtguWZUkpHKdc+dJ6zvACPIiCFxRFxmCHL\r\nsNqF+DLnRc7X6CRAe4tBKDARpHER9sq95qm/IOxzk0zzv2VYfJNmDOxOZeQ0\r\nQusFKPYPsZRSdi6XB760axoK7pZIClCSi1lHL7l9In6Bn5IErvxTsPiPNihs\r\nXedQCubgDbn84kulKxIZCNsaVs+pdQ9oiaUOjbqSiKRYbYAsUck7NT4mt1xt\r\nPeNzM6xF5Cw6zF+kunXtRLWw8osA98h/CRsMA22BWfJc3huTECmdbw1XccdW\r\nuMWDGMWwPZLdup9nz4BP7Q0YpgAkzIgnwXScV4fdgfs0R7dNZDJGD6cqHpF4\r\nTTSoZdjtDBXGTo2xK9IvgaHAqPafI8aPpTKjvMlyyfe1Ji69X3vXWMzf7VWu\r\npIIGOgTa6dEJPHeyAW2OytW1wn3/cSTEsImUndoh0ZYjHik1WzXF5U4GrS0x\r\nyL6pq6S8c9iyAr8E7Q3ZSRTPDJMcDeXhBIw=\r\n=Y2B1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-popper", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1-rc.16", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "51c734eb0f07350da0345f802f0088bb1b7648f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-XzjH6DFRjNek78G93xZDoVDgQ4xF1QNRrCKO0/oKN67PvJqssozTy+TISxdHB3x6GRqaWfM7Cwm6Sk3t3JSU2g==", "signatures": [{"sig": "MEUCIQDYODbut3f/iPhu6UHxwJlSU0kIK994d/UQmuwamaU5KwIgIRyVh+3kvzm0jvYY2V79gSuybgzOxDwSZm2pko25vJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRe9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUfQ//VrJC2FLqu2rXONLgDcgbwD6nCdccz9RWUzlxRRuKrBGnYnZl\r\n64lAh0qRZQQsiypYfsJ2v2qOERPYCC2z6XhtV8Ax/eEcVKTMM2FBDJJBaDk/\r\nXz38gutxxMv9YbOgtqDknXZYEzN8QfOtP80Tbmvj2O+SIfxvcRehvEOh9nU5\r\nxe13hWZySOSFn8NdR1Qt6RLLcUjq24Lnz3cye3nCo2gnDLKE1sSFXzQof7bx\r\nvRLDVP2j+LBipVxEGZr33JGGK7fs93wCOkQzRfVob2h84p8l/qyd1njzi1fA\r\nR0dmUSE1ASYO4wahxg7FCj/DQGJv2BaVnQEFo4pPeg9lzw5bZvIuOKA7b9k7\r\nIzD6Y+4nHV3oqg4uX2FJQINkWSjaLi79fid/NGQpEJrkTxeHcrRfZLyO6gap\r\n8bo35hzYxTW/kKag35FrEyrhLgrurJ1M3VjUyKrWm3LDWfeypMgd5CE06vcf\r\n0cBJefKvUuZ8e3z5KQLN+EUavz5qHVTSpgytSDT+FFnKyqWzfAEtcJYiSCpr\r\nBBjF788tTx80JjIlVMpLXBjrPSd3rtS/tQIMLKSjiLgR6V9dxm+5t3U87IUI\r\nYXpnv/Hzgv3Q89zUdAvnlV1t3e4hRGArU3+NL5Dq6pYonY0jlQRWaT5IWBTD\r\nxcM0VgQQoevDd3KX5uPjXiiFTHzRaBtooWY=\r\n=5MUU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-popper", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9fa8a6a493404afa225866a5cd75af23d141baa0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-J4Vj7k3k+EHNWgcKrE+BLlQfpewxA7Zd76h5I0bIa+/EqaIZ3DuwrbPj49O3wqN+STnXsBuxiHLiF0iU3yfovw==", "signatures": [{"sig": "MEUCIQC8D7JMfQDgQ6IPRAqvtEYWuyg6Jmoq3GeeIzsYspISCwIgCJeyKl67pXLt4JmC+FvBdtSwAdvNj/ISX9TjO15an+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRPQ/9H7VQegVWrMlCRkHiZI2FgNnyEDG1xNd52Jov+YPz6TQd9o8L\r\n7Fbm2vxHpA0Vc2RqE2qrI++Ipu1BdMOG8oVQQOZn/OWiKDfqbopP1nTku3Cm\r\njCa8OMcYu0/dh3JM9yymZWHdXbNBAOS9GvoJx8N8Izbb90D0/F0DvTMc0HsQ\r\naRed4MIlb9jMgi9SqJ2MvMsORSbbbSZ3v+Gd1kACEH+suCurL5M4+fLCV0Oy\r\ndK3bZND4omewp4mRAZ8uE+5VXTiWBcaq9WC+qiXIfmGCPpc1pmvn4FzR87O2\r\nKpMzSd60uPKAjZVNZ2XIx9Okf6lIuhvTagTVGNFfwWgs/nKA8YwRSSUqlREn\r\nBNU0hNHBoA5F5avicHwpCeck7ioz8ttk88bKfJ8Bqf77GEmgFtwSGkhk2AVn\r\nLQCDLo89iev57qsoyR9t+T6Xdpjl5WaH8zp6nlvC+aMNlPly9pcQrv8QTAHT\r\nQIC56/n2T7DBtb096W6jbmHWsFOsk2Oqet2BBhpTK4CWx7znJYoBunb/s93y\r\nSPsLHkfs9xFB1chxTsa9dZAZhI3r0FhFmtCPBSWoP0T/L1T8k+RnI62BiF8f\r\nVNDTkTgeW9aJl51jZCVtQbFJmD5ok/5xAj4zJIWwS6JB+WSHFf82wC2MEFKf\r\necYpIR+r7uKxLx9UVrPdQXtJHTnrRv4fXxI=\r\n=k7gw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-popper", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f914a3f94f59cde6ebef0196fb479dcf9ad83f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-04o/WOJbs2tDfeh48mBWbDot56VLAwqjgICRWihJBErQaElbzW2lZpax7VbH4fhoeYb7+SmfzQ4o8YHvZzebPQ==", "signatures": [{"sig": "MEUCIDafIG2vQcKmv+yQ2kUmkm5KiBfFUtvD0mvGQjPYlI+JAiEA2cvuovv8hS/sj/oyqjgxbCchndFrE2pBe3rjahr5mkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxTA9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi8A//WXgXUbnKHvR84glzNclH7RzDO7IiJTGbqiLhUsrxtHJextTU\r\nZlhBTGV0Xoy6XyubPreWTMWbH3Lqc1Og8ouN0wPRfps/apmtUTGeZMiWXgtP\r\nfjpsov/wnRpWY/CCKZjRP04uR1l56q01Ir2OX3wac4zXa+hoznbhFHKXQaRE\r\nXWmBclrqxPXPVGHo2tvGKVyJOJUaItPAM9itxxEGXvo/QuG9GGve+QHHDrpY\r\nuFCbmHf7CKzb3ddceHXRyEEGPwnC6mmzQUV2y+QAOEzZv5axThqF24cKPb0W\r\nS5ipMglYwy652eBppvqoHAr7V09Qmr7bEw9e/CRNNPFq1WTDhOOxEk81cSrL\r\nqLQ5Erx73V19t2LrZIFYcJiOHa7gUPcixrnAdrnaHvmKevMtK7FkRUrYz8aO\r\nhAgOCckR8zBFHOQOQZX4gD8UidstjKvrlc+E5De91V4ODHk7cTXY9OFavuF3\r\nwoS6jPC9zmPN1tjaAjheiy+9mCvvuZ1a0hSf4RhnMtr1Y6cH0yFLGU8fHi54\r\ny5iqlLXoacYbmlVvqH6mNmvzXIN5VaAjn5C3YH62kSfT4peWSXCloyfWCqRU\r\nZUNHB/7kUGH5yZzFL1DUPoq2j6veY8bZz9kz9YGmGPa5NEwva7Zo0nCrEyEV\r\nq8/bnfUqNNx3s//NSiBD33ue6kkA1dpBqoU=\r\n=FYGq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.1": {"name": "@radix-ui/react-popper", "version": "1.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f2f3aaee79325423e8fc268261838c74e8cbb1fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8g8397uWq3sUHEoSJxxQGByO5vVjOtv4YanXc0tYnCRNsQqM57/ooRN3OlSdw44nVBEYczL0o9z42zGXgEpMSQ==", "signatures": [{"sig": "MEQCICjyGUtsQdmE8INlQMuiVX4yJLSPbR7Gvs5l2It+nbj/AiBM3gN3nAllr6T3IS3FwLPLJKJa0rZMZRADYWkvgwWvXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxUMoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrD6RAAmlY2camBNZ0ha+Pktbh4TW9Qo9jhXBujQw0GZfgvXiH5Ah8f\r\nCesQB8ROQZ6CldVLhV0z+AAke+C1rIkl8lZMFtWmkwVb+X7m5iveuv920duf\r\npH4DPRdvJVqHF8Jt4ptBy6hn9ZW2A1xwbC14nPC57RNcvwd4Jv0lZoupoc1V\r\nqIomgm9PhF/BXaSMxwRxWfWCs8kaq89NwOWCQ21hjtfQGB5CqlsU9FLAtlha\r\nSzG/Q43yZGyoS72nleze3S626lcp5TIX9c0bKI3b9R5dG05eqmPly0vQGA7o\r\n9vCApzfMpeLnqc05fiaDk1wjOyNtLvBrVWB4bCaZnAdZnDjyoHuKY/CGUDrO\r\nb1xe3hwWGKOQc1bk+J765/ZAKvyi7dvUnIhWD3fg7jM/AKXPppOxHbcqKLYu\r\nwul5Gk4to2IjbMDraSJFQCuuV5q3Ai3ahQjujx2w9ip/hcd2V+G67/zhSJVQ\r\n6qNYF/roEbTXWc7KQzg3VU6jWiCp3pOR6v0AuY+op36BW8mrSIsJunQYt0Mm\r\nL8+ja1I6Z9niwCI2haeX0D1EUoIUWaa9zy33ABuSt5uyhSyfSB/v942iOSje\r\nb14SETPVYs5NanBt6mogtpc+kCO/+80tK+nx3h4eOEmMuQOC31I99fPAUmLy\r\n2/EjpHUtia5azLBPKlufo/Z00gKoqoRUwXU=\r\n=Q4E+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.2": {"name": "@radix-ui/react-popper", "version": "1.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07a4f2fe207e5861c2222fad3f5eabaef4df0f8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-xbCBO6ZG7ItkZgftxzc9DvGjHcREO/rkPQdCtAGrW9+nRyPM1LbxL64mR/UVzOGtDk6OF6Cel+OnZl/TDT0iOg==", "signatures": [{"sig": "MEUCIH7S80kXA4Q8otwzfG/AK51r1L9i3EQnU+mtdwtQLc30AiEA1mFilvgOUkutS+HabVVZgiv3ZhZnaBB9wGEk+FPMd6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxW8zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOMw/+McSY1Z7QCWrLu9Wl3iEM2HZtI6FM03uMZd1I3GnD7eh+4COv\r\nmc78ekknfjmZDu3Vkx+5E+v6RT0h3jkOQkjBgMFBHU1dXaPn9d3P/eSg/5Ju\r\n6rt6JSGEcz8E8fReDTGB85kkXrJ8sUhq+dehR8jt0tPgveqZleWhXbAkSqeK\r\nyKz+N4Jwjj/VG6NkdKDPHDdxQaEtqLP8QbYiw+EgFvI9Ce7bsy57R13aU+N9\r\nZGnu5fqZFZoCKPz8fnSCYBLXf2OfHA7OwHRl/493a3ZAcIRApQIHn5Le4Lh+\r\nEj+ysnCCpVhGDTooWeXamPG8dewjuB+pxXlBqkGoCKcqJhL3/cKCdKWRYDIB\r\nuZiTcdK/9/z5jKNPou+qIgmcwLr4hFtQVcRUSlTw7A6LO97/SZkFpbUn04kU\r\n5lIIBSpSC6Pvmfb5kPLqOjm0gmeljdpp9fGmVDY5WHw2WAQ2n94745u1Oc/r\r\nbCKz4EwAAyk0K5nt6wW4mHk+g4bafRG4BljkG1oBnkkoXsmC7KS57wN1OUN5\r\nHra8Krsv65jkaaWNhQLxkNPzhETldmsaGKfCXjLRk43ZF1lY3rqbBvKP4NA8\r\nCM9zGbdp+BWd6Wg+4Yl9xtCGVz6ihDfOvANOf1Kqyhb/72aJwNYJLJETyPsD\r\n12NuPgB+vHR1YzMKW3VgqciBCyn08lrsa1o=\r\n=rl0K\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.3": {"name": "@radix-ui/react-popper", "version": "1.1.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "49462f452b46bb7a4d4e1467a219dba0bd29f873", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-oXbhPMO2HuEvy/+29KhRAAQ7IHCqdLhWX7z/mtbP0Zbkco4gKRnF14IZ2Kd+KsRwJKcbTzv18RPLF41zx0OLLg==", "signatures": [{"sig": "MEYCIQCNMlnf+TWVjR0YHeZWqXRsqNlqnbDCxMPqrh7EoWDC8gIhAOS1bTW+OzzFJBcJbRzu6mAWcSmzrFWHzplU6xesRNgU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxp2cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH8xAAge4rgN15DXjWKq7pmpcW+LKggxNqcBJXqoCpbhf7xUu90XV4\r\n8UmC/+VWDwp7qZmlHchvoEMwQQGmJADK9x14PY/dPIyBCrHeftB/8FZ+CWDm\r\niHFDs2Ycx7aoL7dy5S72XR1W/0OUaYPtDXvWx9FpkOtkjhA+7PesICasCWJ7\r\n68BwX3+TkhsRaM+7TeLWAnXzJhFqsxSeAIobaseiAdW5XeGkSidc4sTs2Gw2\r\nXnjCeEsNtege5pg49PgeE0aiBIOYXsvNtsl+ZaZqS2hIiOkAnwy9aW1z32kf\r\ngoJIHsFeLOh8mRUlcLfp9gScPtDQNT+l3vt/ZsDXA9JMYT8lwekqyLYQ0cWp\r\nTLOVUNtqY/NZD7iJtd2IzDZho56hbsOUEe3WgrpwJRix1BNrHBt79GgcPPQ4\r\np9Pf516sq1lQpbpHUqKUaN/EEb0FCvapsPDVLaZLzK5bCrSpBikBkVyuYx53\r\nxovESL8WWE3zZFQgvJ/zrKOFa8C2ImrwBtSi9apbXDaK7VlWnQF7x/3O41Th\r\nroKEtrZhUvZo82AZABC8h56vNhgI7GwFPIWC4o3Mwhzagn4/L3D0CEe4HxxL\r\nC1cLRR7eDAnrLes1Ug9EmEQqsR/6WP4eS8cT8aaoD16ukDN8Gv2Y6n7IL+VD\r\nknbnMdPMrfmxPdNNPCo85ZG+U2Pv8hA3eds=\r\n=w2sm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.4": {"name": "@radix-ui/react-popper", "version": "1.1.0-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a3efc6b590efcb33f31bc969d56518a73751d34c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-G8k7q4S/d/O7vGP8DdbP7tOgjYNsQ4FpQ7An5sgJZY4EgOkPTEOZuahfHRoStdveKjUU6KqF9m8/CY+hXEO7LQ==", "signatures": [{"sig": "MEQCIEgos3MIUzWM47fVctdH3bqjUPZMceK80X9PRANhRQnsAiB+Le6QLl9TJzFbzTaTRvVI/mE07DwyHBe6jGk9hzVduA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqE4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqznQ//TGprAUzr1XCSAheV+rIYEcZuyb6troj4bthGXPAqsUEaogiu\r\na9XbyRWv9vwAiZk/cmT3ANXvR4rUj3go4FWidYWL5/VoeHKIUcOclwe9qGky\r\njzaEUDZkC5+poST5wqBzij74LA2+kAKyGHZapteAeElvbvpotE13T+QePadl\r\nO25xsCOsLphNQuRhvjI5jjZTO0qhBSdAEGDSPyYPJBr9TpKUjZtCAtgxEgPr\r\np0OWdJLSSjR3tjemjNqzPHCr1k8edP7pmoCLcLjGZy5b1jl311FSeJTjyZUG\r\nkgGZaqvyDxYBHh8nAo5FkWQvtTaGXtEIp4ep9+q8pG+Ah1JvSiOtbAOl+soL\r\nTLW4MT/6yUxObZuVHX8sEwR8fSWjzlwzo5Vgde9JNstdOYuJ5u3fN+drFX1z\r\nH7uDwq7DlYbbC8cm+wfOVqwcgdm6KF2qUMSLJiYWjJ51OJX3Av9mQc68se5W\r\n03vlbN7sXjrEVCenErYhwM6bKSL0M336D2NjksZoobHPLli1xEsMt3/aQZfu\r\nfIvvEWmJAoSK12EVJVVkhm/AB+hCLPCNdOtyQxEQ+ewOY54p20ApTX+wrAtM\r\ndH7hpdx6H1Mz153vV0GwHEggj4AuGQuulbrrRuRxDIc3k2FmVl/8eVpg+tJ/\r\nOUHfu8IwOJrRUypLiYpwGomro+4cy/0rpng=\r\n=JP73\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@radix-ui/react-popper", "version": "1.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2be7e4c0cd4581f54277ca33a981c9037d2a8e60", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-07U7jpI0dZcLRAxT7L9qs6HecSoPhDSJybF7mEGHJDBDv+ZoGCvIlva0s+WxMXwJEav+ckX3hAlXBtnHmuvlCQ==", "signatures": [{"sig": "MEYCIQDcmZXy4HIvK3kxooCT5SPkXzJwEy/JDMDeVsDC10I14wIhAMRhGrl34jTHWQr+FW+VRNwm4YCU8OrH8B0ompHvHspS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqVYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLSBAAoq0Ys01RNOdYAx2ZBYitUrH6eaEmtsJUNSwg9pLWCYhCDNgc\r\nEDC84UOfzf6QNs4eEmEkeBONR0xQazb3DulqOEFF7bd1xgGihZAjXAIwGO5q\r\nghgEz9ZRCpjHeBNiQYLBtc7pEr//ItXepmC4Gh9E2nQsvC+sGkMgLknZn3ot\r\ncnae87a+CQihKocu0s7/n+n4IB8yP5ROMokhrga9Pm29/pnWtMn76srNVo6j\r\nVW4VY5VB8sEy62LgWByfY27eXhsojmNP8r8i61YxXNPoUboRT0Md7hJlsemR\r\ndNpB4yEHY0jsQJa5RsIyt3E+vEZcypuNy4XV5hfZWXIZVyLjJPxFpqiTbYay\r\nVCp32jyR7+i1b2pbZBs7P0JDgiqZ152NRLcoo9jCOACWDI2x0rosdpuaMMXW\r\n0r0LoK2cs9VgDrwKQjHtYBlt9V/jUEbE/J5AE0xJL7UqOCRzGdMPXgtEsIlm\r\nKQVhDqSLYIKw1GC25Nux0iiDz6UjJ2UUxGywnoW25HcUGzMlPIofdem5VC2L\r\nmtbtetvPqk5FHF1O981kGlB8rOUPvEcG3kVYXNcAGw71yRF1RClkDfB/v+DT\r\no19wsTTp8G5hcpie5R9zcXHd4vwwGVvjjxVf+UrIvXkxG6keh5olihqCIt0q\r\nz7xWScETXCsYYLRYQDdSMoAEcPjV0HhpFgM=\r\n=02Xp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1-rc.1": {"name": "@radix-ui/react-popper", "version": "1.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2-rc.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74f07dc27d5b0c1d3bef1ac5b986ea669e476c9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-qk0eOaWhbA6dCIcCwvApRzqzACEu1Qa6NG7SOOaeFc5GTQOcZ3AoG3jkRRm2/F4UOdR6xlWuyvrCvRAiy5XUGw==", "signatures": [{"sig": "MEQCID8h0vh++v4AwMDQ74OOTCK7qnK1xqrq5bqhlrtJN4RGAiBEoDuynO/PsH1SBBD2ZgmaW/rw2tdBuK2zgDr2GiQktA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonYQ//ShTc5JUV/7wxB0H+tkuoIpVUFaCcUFHHkvrxV1LE/NZd/LuB\r\nO8VFWcVMLFvgXTvMpbWLLxI3jyazPEEQPeCEy3VW3vJHj90vTyweshM61ve0\r\nb9ocoJhVJbcaTHR6oJWCcyiXO4RmYOQMSGlwveW5p+KHQsZxIXRWmvWMjGh7\r\ngRmRaLt9d6NGdBEXANJ5+20lGSaLHSICrz3EQS2LRpTVQOdwCNZRukMypXDH\r\nZiwYkuUUClXb6gsLGa08ZaCT9jzY7Wz0SBgGgydHnTfehnfS0hr8dWZaO7ln\r\nV5snkDOgpdJ6JYI3jy5OO1ElH623slX4ALEHFFKfV+NxZpszNVlwFXxzDwDB\r\nFKgAsJRX2VdeStc5SDmlQhcve7nJNcd97zIJl86rpayZzQw7mfVvYJDx3AGw\r\nG5TPGWQWnRyyOU7878u34lmvlIJtlKwasncfOqiPFOtgIWjP8IJQhQlOdHJa\r\n4X/Z9zcDpeA/QswZwadDO5CBmRCmv4ADDXUp99Lyh74K2NKyLEdw/ktDbGtH\r\nclt4RRn3e2HGtwKXi/1fGTZ3H7SRDbRpPVZ9xeZVCElxSlcgMywtaHrG9c60\r\n8N/MC1+PC96O/MnT9O6xhpMI7Zv2kJjPe80G9UK9KL2oxdoDa/aelOvXKPzN\r\nZTbg1IZJORYFtD5YepIIlRD1xxCNl2O+Hf0=\r\n=HCbC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@radix-ui/react-popper", "version": "1.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "54f060941c981e965ff5d6b64e152d6298d2326e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-keYDcdMPNMjSC8zTsZ8wezUMiWM9Yj14wtF3s0PTIs9srnEPC9Kt2Gny1T3T81mmSeyDjZxsD9N5WCwNNb712w==", "signatures": [{"sig": "MEYCIQDqJz4L4ssG+vlrGI5+8HLbbfZQtSe0MIYoexurU7KXVAIhAIR7gjS8G4fAoeeZoYIuHOOWolTfiK0LhU1Jad6Zjtp5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJazACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo//w//cSx9noOp8NRrHlMXXeTwvck0uMFJUMmIMx6/f24NPLbXVN3s\r\n9SLCRXt+UPgXSqFyA7eQdd11gOxBGFtz99u+CC6520lWkhUEMvKOXmxlh0CU\r\n0PTCc7efkh9rXkbWYLoevOhe5x6GDYZNOzeaWSEVO6X8KEc70CUT9qwpZP9u\r\n/s4KFFue5S/3q9DMxmesPtp9dl6RmP2WOlOVytuZJQ0oxPc0EHC8rCpZ7oz4\r\nctUpsiMf+2yJVHaHlJ37HIe5XWr2AK5X9Q/ThwcwtPDeFB1m2jSA/YgqUXos\r\n7okcZbdlXGOnQK9iiW2CDe999n0A6zaHJPHRSfBOesnyJNp7zd+qS27A/sKW\r\nEUDQLaRdK2qetJxlOPXPQTnEjVa7zmH69DGbFUxo0Bzo3wnGgROUzzno8PxK\r\nw7fBWynKmNwFCLhOUPJLVqUnUHxt+CEZtKxjKcG14DrCp56e+JRcEsCrqhnc\r\ncU53DhEbJR3cUE19rmDhzMzcB0PfMygXpbs+jc8OZaVgBhe/n4/ql/s7AIEQ\r\n1Gdkav5SGW70INQt30cgnPNMosd6YqI7vqxeOQ9GV2Sz2T2X6RvA96w9Qb16\r\n/FQf7UgxVWVnQoXdTi7ZzUpvYwb3geIIUyszQm3+297T8LB3a7EPrECV/IRH\r\niNqrA+/Qm+TTOOXH7Cpil55q1BjZ1luFf90=\r\n=fKYx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.1": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a2ec11c03c67846fce04de8ee341a7c1e0e502e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-2yqBCSKS1EV+rpgTMxrohVkHDsf4ChmDvs9ks+th1xr5Iy3fJrfARjo52zmYhPhuFvd1aoZbILmjxIybzyG5Mw==", "signatures": [{"sig": "MEYCIQDWVHENZ/h3qnHUm3ls3ZV8nZZ2gS1Sgs2ybyd5zl4vTAIhAOd65wPtmaZ4lRf6bG/YdIEvmWZYLwHXzxPB3Kji9vE1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC0w/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoveA/9EHjkoAO+w9VHAvmug16SkkPTAfzlslNdHCVkFKZyWmzA2Fc5\r\nT2wtF8RZQNQtso2VNaiS9JvGxR3pYEQ20Dk3/gGaUhPKhqHA+Pg9ty9eaV0I\r\nwhlZKS19kdvES/qfsHLJ8O4VbXUYukhg8zWrg3Z283H/8+5EByYIf+IkVw9g\r\nwy7MZ4YIAhPlJRkBuZzy6NHU3R0q+l2tfks8q228xfFU0Gu5MX1zuKzuUs89\r\n50z+kbx9R1g9MI/oaODNTZx+m6SOFrOHBLpUlIsTSVkaETDNMAWBdIHkde0D\r\nspqwJnKS6BK7/ZIZXB6x61R6l+28i3WkxFjKg1UFBiZCfSsMekwc6L2Qupzg\r\nM5jVTo/7YUG039LXuWDHh7wYjN79Hb+wI39qtdbebE2LcoYInkxL6IjLqJHd\r\nDSfOc7Cbw3+RY7oXsbJ/9TbAjis0U3WLbxNHD8K5dLIgYghpaXrpatl0XDgJ\r\ntK7XK5vWrTA36wzdFpBtoIRKMLgaj3iYpr4o0kSy39vgqrUFgnQ6cQYWT9Ny\r\nwFDPNkge2tNPD+O/byTFXFE63Z8Fv4128To2+Y/RsFEch9s7Vu2t9NQNlb65\r\nrJ8WMX9zKmK5f/N85MRLcde1zXyPPrJh1vMsD9P79R2V0OAWPNzTycR55HdC\r\n+QZGRBNXp6rW6ycFtBD7ijsouW9IHimitUg=\r\n=BXy3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.2": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8c2740251883d7d90512e970efa5a6919926a726", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jWF83UBfMKemnFWczNYqXFzmcYYcVNiHKslWQjIeei7oJPuAPz/6WWjpJCcdLebQHeVfbBkJtzXGoqSLDNWJ2g==", "signatures": [{"sig": "MEYCIQCZiodcGOxX3VhgWtkuzyYF9z8hWIUORuUrAe/N60II2gIhAIyF2YscrmJ70WwwpZOAiz5cSEkNdEIaUqnHDjOk0H4s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFHuQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmps4A/+L+q2lhBCkPLh4ILWXHmOTh5DQrxplsw27J8cupT3ClFiJJ0Q\r\nMsjwE0QCO6CPK+UZrufUb9rUA85xXFqD5tfUAURbe11h29Bui3/npTbEFYN4\r\n11gal77Gm6t+Lg4os2uxBsABHkwvpHCk5dp+vVBLFmA4blULjpY3/eN9wFPT\r\nYcIbD1Kq3ZUjjLU4Pe5P6KxbTvCjN+SJ8UMi57OE0GyfhJEKhjIO39qQceeu\r\nU3Jcxw63JzytNQYU/bB1AW4qcEfD44XLixwKB87LdxXuKUS9NnHdhbjT5Enn\r\nrxJF5cJk4fusann+U1/3ZAO1zEUIKRhe4nrZn2xI2AVlbxmmgI8mS4Q1RJYf\r\nSArLFosJx8x+dlChYCvwLlAoA4gTeL/Tc15mOUf6rvo/1CxptfpOaoV3n6l8\r\nVHciYTaQiyT84wlF9/zpEMYxbJ+kMjvI4fRCEEHR1bxxVV2NOn4Sa7lLIJx+\r\nQVGr/0h4Q/WSqSeibROJkc6vAe4qYYvqNvwQ16oKnK67pGVlyxKXcv/Z76QY\r\nH5Iqo/l1GFX/O9uJQpMNcheK2Cc4GGm3dy8fX7Upb+dcw9NpDlWEUoKP+p7a\r\n1kcGdt1CP0JDNul8ynId1jxDOtC1DG58zprMnHmwxdMl8U25oVAFtR3+ZxTv\r\nbm1zQf519zuaFhRnWfyu+jkZfw/JDGM8/U0=\r\n=ZjBf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.3": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0fcee4f24ce5756585d1b33ac0733159211840e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-DVwze0wA9gAOpqZDcXRfwHGQPxE3S7/j7MLQ2rCBaQ9Zv+ZjvbyZTlWVnNukBt5EsY5KITL6BtSSiQW36xbPiA==", "signatures": [{"sig": "MEUCIAZ0nDD0LjK+vIdFPMO1cFFhqthEzTjbVdlK2/MI6UVWAiEA0UcD8Wh7lfL77mTa4NkEVu3cRG2DTDeBQk0bxtXGsbo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnLhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqmeg/+LilDZxJulzsm5MOdynuaqt7UF+Ueyp+ZQrvK2ez2xXC8Xef2\r\n91fjc0sIWDjcN27Bqstccs98UHU33CGEWq4QzXWxWRRQpygtCMQfZlFOJQzJ\r\nFpXC96PUItiFNfUuB70iv/tf7NBaUWvIgWvwKqGLU5sSOav+kr4/Uw9DJ3gW\r\nM2MmnaA8m7wSakG90SMQRMfuF68WjibxPP7DoM20X8PPYyibEEvETYA8A5g3\r\n+Xv/eP45YWGf5N7zXjNq/3RVGIetWSCsdGi+UC5plXAtQXUkIA2HkdNT6miw\r\nzVAN1dQ9xswHYbfUjRI71uZNgEzSt4UoVii5U2UlJFQ10aNjnAQoxI6qST/I\r\nNSMgUCd+ASFY2G2wMgU6/IlGxhWCYfN8vmJexIvs/eFb85mPVkCSXx2I+4v/\r\nUE0uckN3PRZSRlAI9LBCnbsAM0zazJVWqv4ko0hBkV1/TDA0igz7FgFKVeXQ\r\ndX+KlzzE/wrwQW7t+7SSabM2I7yeytMGd5CHuGYXQCaI6QgRGYfLFK3ArNRv\r\nvv1aC3/pbZH+aBqbAKCLS9X6H1yjQXwYeZ7lvjAV/e0XHGvJE1Ht4DEuv/cN\r\nQZVaMi2o3RJLc3u0BBL4qtal4q/1xweQ2BePQ+cS9uPC2ZFXjAGsAgbz4d2R\r\nBQMILVEjpeFiH4Ah7IXcxSRNrUqQnApFJlU=\r\n=ERsz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.4": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4f7c2f614784dd5c3b2e123592d2b191f2b9d7c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-hTxgO2zjubKtUGzGz6Uyvti9VUpo9cI7oDkVDB4+TU7Yy1tfezmF/edDhUAisMSM4fb1UDRh44t6iUnTUA2jXA==", "signatures": [{"sig": "MEUCIQDn2/YsocGIwcxHeJzBymcoifgExp1AuqjZuwSkqJiKZQIgasZXg6co9ykWuf+isHeoCYgLG7emLdkvAk57c/TmHXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnnYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsuA/8CgmhyESbO8RtXI+PKtm3qwOeD4RQiL0ci2Qr/5Y0eOObuwe1\r\nSJNpArEJWN1+dVHS5WEW7OrGOg6nOBxs899aZo2RXJ7HBuH1EqBnrNTxGcI7\r\nCfl9ec3NGsBKHGwHObanVnQosCKBZ/BbdlhwrEJIR4RPbbu4X2iB/R23lzJo\r\n5LXhyKp8PNqyRBM2Q+6Cq1KnOm2LG2vd7YguY4LiUdj2YcX1Eo3N3jpxSsnF\r\nx+uv4kQxYVDcCAixIrZBudr2FYePuuv3OIinLLntCYGGYxBaqSUMxFSNqP9R\r\n8KbVK4mB46GAK+9p8f6j2S9m6ynghoA4VJMbb0etTIHm9LOq6fU55hAsSe1N\r\nr8whM3FfPDt5BZ/2aMnsZ79Esu73I/Qmerd4n8BPo59ybYByv3s6qoA3RyTN\r\nd3teOK9NIZSFY/hLG5MHX3/8j5qGmAVdR+fIeDf/s3BbmBBpFB9zbWAzw27x\r\nqaL3G8RDu5ojOHfhh7+aNo2R2qLH+odW2HlX8M/KUwzdE+KG0KiGJpK4L/TA\r\n6M2bclyLUk01QuxMGNbuq9uJV4SF89/sDxzbSM3jEsaIF+DeTq0lOah++I8u\r\nnfokKVvsT9qKmjyrTTsdsR1gJ/kbd/DL9/cEgVUo6bsyT/G7kujctN7IO2P6\r\nJIXmTrqHLB6uWkkcdiLQuH1xaogc3XRK70w=\r\n=aogK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.5": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4847d08c8cab54ed3d1e125f1443eb69b475331", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ABYjxwalGn2vCAKn69KVdXiDdt4rmOaw53GkTcuOT1SJi0bxhq5qtvbRMX96DHZPjJLAVJ/iJvGQF2PvPHpFzw==", "signatures": [{"sig": "MEUCIA0vom3RzZPYZWB1N/+vXNR+eYad/wMHjIkBd+MehCjfAiEAnoKS6olhaPa+AD8Vd5r6lKRY2+0zCjukpPlSCZvYWi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/kQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdjQ//S+ESmmr3i/nWc8AOWIYqxwnCp3JdFivmCeFXaUnIyd4e54HB\r\nzuW8b4P0z58FHFzU/0HfU0+dts62Wv/REncD7TYHfrb/J+qMjdKCWnTpLPga\r\nUshbhm7BxOyXEBDOkraHwfDlheWfSRR4uyQcbqBjrbdQf9XKPwVCTLKJXBiS\r\ncR+SjADvo6llgszRef3eGkiL+rNdA1bXBBk7mxNH4QnUfaes2Den9cLdWuwb\r\nzon32BketXAEb4pdQXTQYeJlHBhO3tbK8V8ECYv4OpXAlxvANS74ubfB04fv\r\nBWhbAjxqt8DHilNhEdpE0coig4ELFtrahtEPTJAegVC6lND7SYqml49ccTbL\r\nDnsLX+ctkom1zSWfULjKtQ+U8k0fncqUPENyaMurgoBQcPZfyibX85KvfFdq\r\nfAFeqgGoE/O6Kch3Si5x84o6I9/i6SLy/zUdAvsrUkv15mZ0fUJjLdZ1/Rur\r\nAQ5C2hiOk8v8HJZrK6ScKIJJbFjQ2qcZgaFQZnOzI0AkuLr//Anda3oQ/REB\r\noN2kh0zGcufdD2KU0DpOz34T+ukKPgHQH88GtWFXaaaJO+5/cmxU5DykY5sn\r\nHcSr5wu9b9BEQeWlTYAK5sw8+UyMS6hakO0l9qMbh3XGWcS7OtFlkoSpfTMa\r\nw9vn2HUzyvgkwL+Fd9vd6FOdlVntV1gIgQI=\r\n=rjG1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.6": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.3-rc.1", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "95729c2c65293be9ebe3f3898395322787204b37", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-LPV3T5QOtaPmBg91qduu219MmP2L3IlvEeVX/gx+q21hcAWTBR6ZfQTp+JTPLXNKUdqEgaDP6LClc586LnXbdw==", "signatures": [{"sig": "MEUCIQD5ufQsT5+DQB1AG8UendkaVx/u2rN9bDdyBOFWsz9kvwIgE5zhj+1b4fkFUU9H2P0br5D0wypahtzZwgh62kOgvng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8uRAAlydxY0IzkY6Q/NSFQGCrkxskPYZomBSRicjUZqNIrrCqAYUS\r\nWuAgKPolTxWeKsOgvizljMi9phbZsOVH0mBRrEcbM6Q78dOReYHxnb4IuJhm\r\nHO7UyiXWwwM3D8ui9KxloAUKL2O6JILYqPgiEgqDGzXQgB1JFz1fJ4LJx39k\r\nJ/6cGcLqnPTfA3PwxM2eh2P8hsk1qhAq3Xj2AEij1HidRlIHFLMD61d0zkP+\r\n7GGN0NRF9fgozxe0ZFthNLgVHMirRWFKnemk+REIOygNia0xFzTlxNXIpZn5\r\nhSFSBzGfTMO5mtAY7cXR8b+/cd9Lj0VPr76wXKBR+zTaEUiwB3JGFysWm4aq\r\njX5P0NY3PQrwyrWsLVTX/Jj53wwsS1tSPeI92xAFYaYJl0al6JoFo+3W7Ki+\r\nDW5qSUS4ygkj1LaN9gh0EcpKWqBCf13DuT2lHAoBx2WkfLjbJEkMDf0J+9hn\r\nc1zPywEmJdroYvQd83CI6M1nqFM87VlojVMTN/uqv1jusOgeC+n718l2qp9I\r\nyyGteNK1B4889kFaL3tEhcjZhnMH1m9UUnW1w4L1Xq2/AGK4WsPcG1cz3+z5\r\ny1bpRP7ZfwIBH2tV4cJkTdWcWUQ68unpm1hOEiV2Tq0nK3Ql1UAtCijALsx+\r\n+6xjRfwI/6SezgWLfnjE9mKUHzb7M+OvEdU=\r\n=melp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.7": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.3-rc.2", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6901ad6b5680be09af0deeda1d530f1fb543fa0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-/w7/dK5AyrM5IbXOJ7Q1eRQx3B+Yox4NjubB1DC/7OXnnVa8PngYu5kSFMe0xXEeFkINU/REhOJzf+WkgSdYyQ==", "signatures": [{"sig": "MEUCIHpna+51zE0QtTto1c/ny6Par1CAuoVQpyGjfT760cauAiEA71Pa1nNglqLU/AztpNC33lRkIwYlJlplRUh2xl3p4U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111388}}, "1.1.2-rc.8": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.3-rc.3", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bb9ae193dc38e1040442a117374e7e120cff274d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-q8ZdllcxHZ2rAvS93/Sm272w/I1EzK8ch/xBuS3+Rcjmau98aoppiaktLcIsDXuou+g0LwRGtv7zo+DhYpvW+g==", "signatures": [{"sig": "MEQCIAhqsg9E62VvWYMB7QS5OVCcFcSbauegNF8jEjWHuj1yAiBcxgf1BDU529FRpWZaoIgq3i/IMNutvjJGFrocSJNERg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111388}}, "1.1.2-rc.9": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.3-rc.4", "@floating-ui/react-dom": "0.7.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a4d5f6eb2076e57319735d4189db8e118c8635c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-NzGzo3n3GlbOtYOnfkdhwK69O98ondl2F+RjBgp5HDRp18wjD+vBJAKrU14H0KAUn6EnQWa9ydiZXXgNJ4GGXw==", "signatures": [{"sig": "MEUCIClYgXEQ6c0ci9Zyl4lJgzm546InXa9mBnf8PVl6InWMAiEAx0V/qeFuC9DmnaSqyrHQ/s0OMpFH1UyDa9RzfVdWTlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111388}}, "1.1.2-rc.10": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0", "@radix-ui/react-arrow": "1.0.3-rc.5", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-rect": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f6cc8d1b4a2c9859502be632d8439607cf8dd44", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-TJarE5m/ar1yGovovS1fHvPOpN4k0leJIJbeIk2nSUKZPF7CMsQuQqXWJBeWLve5PUxK6sjUP00aX+Wm9jPv3g==", "signatures": [{"sig": "MEYCIQC48hjwNgy3AWgK6n8HHcLs13z2YeAgX0Ok3w760ncNUwIhAMgi31qziMY2JhGD6wJ7eXOhC1ME/QqTOF9ZO0ZxhJ4U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100209}}, "1.1.2-rc.11": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.1", "@radix-ui/react-arrow": "1.0.3-rc.6", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-use-rect": "1.0.1-rc.1", "@radix-ui/react-use-size": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-callback-ref": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa446da5ede1504c82fcf456c74a88fb97c6f6e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.11.tgz", "fileCount": 9, "integrity": "sha512-/PpgcNUC/EKwwEviCfdUyNv5x131HIPwfNvF4tPcr7ds7OJyGOOuH9GINATnD9/XrCEdXv8NX7whJeCfCO4wZQ==", "signatures": [{"sig": "MEUCIQDeQallu2cxMmeztRQrh2LidoXiYn09f3sGuiHkj2a9LAIgDLsGoIbXDJDQ4Uq0poahEWclNr33SM1pumDL1jYGlAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102660}}, "1.1.2-rc.12": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.2", "@radix-ui/react-arrow": "1.0.3-rc.7", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-use-rect": "1.0.1-rc.2", "@radix-ui/react-use-size": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-callback-ref": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7aea9288cb4b195c1351e1f2b86563464917d897", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.12.tgz", "fileCount": 9, "integrity": "sha512-ISkG+NAynd1ZfkkZuARb7YmtjO7bBnb87V3UrLzWQCq/iwMwjMc0gjiTHDZ0BLxTFJ/EAwX2xwaTOljBriU04Q==", "signatures": [{"sig": "MEQCIF5BFglKYAshllvvZXHHwGBFHUezsOXbKS290FNVa4CkAiBCFpP8A0hiPgli8d1e7zAJArVBiRNEQny5xlYTQlU8pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102660}}, "1.1.2-rc.13": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.3", "@radix-ui/react-arrow": "1.0.3-rc.8", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-use-rect": "1.0.1-rc.3", "@radix-ui/react-use-size": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-callback-ref": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7f631167ff647f5a640b7601ff7d02364da2ce81", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.13.tgz", "fileCount": 9, "integrity": "sha512-Q4GIDnM0ywo+A7DSSUTg33ZtQrNPJwBZKDxpuk4Pik8GJAEdqHB/6JV+J+DWl+meXXghvQNyCmEliFzTnFSRrg==", "signatures": [{"sig": "MEQCIE8+6/JAaDrnkNdDHrJyPkInku8OMrI9wt/PBMdfo4OqAiBMMmh2dPbJHIseN7vGY748kxegcsGMOzeV1L8JVsJgzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.14": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.4", "@radix-ui/react-arrow": "1.0.3-rc.9", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-use-rect": "1.0.1-rc.4", "@radix-ui/react-use-size": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-callback-ref": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98c9fb3060e4185b9f1aff6cc51b4b60b4d5b262", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.14.tgz", "fileCount": 9, "integrity": "sha512-dCkj0IDHYMjGqgyBxfw1jNIav2ytdlx0SqvXPf2ZOkHo4X/pVdNEnx/kcKPjhktuQd5ryVDHUc4mH/9W/8UhFg==", "signatures": [{"sig": "MEQCIDwcvyOMevfyy/3E1R82nSDBsY5Ve/OVrgJxM635gGsXAiAR6QkWBRpkLjEkMZCdsH6p+d+u1DXLg5iJ2J+pTc9H+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.15": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.5", "@radix-ui/react-arrow": "1.0.3-rc.10", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-use-rect": "1.0.1-rc.5", "@radix-ui/react-use-size": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-callback-ref": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "59e60f868d186cb60681389875329d83e3093bf3", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.15.tgz", "fileCount": 9, "integrity": "sha512-wS25V1+yDGMtmtd0k5G7ScOBrI1N4CKYH6sWltGbonWridUo7c5lmelZitbJmB5W1vdGf/wo+dVC0Txk0UeUyg==", "signatures": [{"sig": "MEQCIE0Z/HWYB+//qgiTqnJIX5ohb/vuBnCP5IDBtbvQ0RnEAiBNZs7nk3iASnzsplEhHiGTfs2ZVtvU5RHTEkd5GTKrUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102856}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.16": {"name": "@radix-ui/react-popper", "version": "1.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.6", "@radix-ui/react-arrow": "1.0.3-rc.11", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-use-rect": "1.0.1-rc.6", "@radix-ui/react-use-size": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-callback-ref": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0cd9d1c87499371a975aa3db13000a546d9dce08", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2-rc.16.tgz", "fileCount": 9, "integrity": "sha512-gTuy8EhTZbtNRj4EyHwM8aPK0Z0d3hF8rLGPWDxNzDSyW61TzgUP8iEi7MsbCbh3BYJoNlwQrNDxCN1MXzCLkg==", "signatures": [{"sig": "MEYCIQD5vxaLuzA8BO8eyFhCS300OGk4L+XFWIbpaqTHnj1C4gIhAKwUdv03tRKMdBBpWzecAA13BHNWhwiBDCosBN1+ItEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102856}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-popper", "version": "1.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c0b96fcd188dc1f334e02dba2d538973ad842e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.2.tgz", "fileCount": 9, "integrity": "sha512-1CnGGfFi/bbqtJZZ0P/NQY20xdG3E0LALJaLUEoKwPLwl6PPPfbeiCqMVQnhoFRAxjJj4RpBRJzDmUgsex2tSg==", "signatures": [{"sig": "MEUCIQD4huZlOpAEDlJ8Qqi7euDqB0fAhPIssI8uhgQNKghyygIgaTHF/JlYoyz55NeM5c62aGO4pQKE/P7NFwvywYtP6e8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102775}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2568945dedeb53dc59745a801364f96be6121e34", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.1.tgz", "fileCount": 9, "integrity": "sha512-4TghyrcjaHEVuzcgzNe6kQCr9HfNhRoMyGbGDANQwSXzXAyfdjXe/PmtyggXDhs9GNoVZvFlI1L+Y31Ak609MA==", "signatures": [{"sig": "MEUCIGWUaBFTj71AgF7RdO+8GJOJrkEnhee6T1bRff6FAY7uAiEAvUcnL1FXJJJ0eue10Wp4FI2H9hFqaCG8xlvPYQGZF/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102982}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a17a7259bd115e1a790923e0926279474f44e52c", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.2.tgz", "fileCount": 9, "integrity": "sha512-A9zE1GWoYG1LDeBw/VSb4W6yIgbai4iQKzEEyi9Zllv006oYoRoyzJuDqOAQkBMMJDwu32Wxv/z/t0Xd6hhp9A==", "signatures": [{"sig": "MEUCIQCxT1xZ0iQnnWuottdKSHWcDNfAKkBQ1QcbLgQedYNXdgIgXTxe11wePNoNM0CKcBF5aoDWpVRanRxj7kKK2mZjk60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104583}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5dfc9e4ddd907e06851aed8ffcff7b496401717a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.3.tgz", "fileCount": 9, "integrity": "sha512-lTAFWRIw28y0IuCQB77Nkp0FlMAAeS6BsMDREUgrytbVJXjPFh4e3f0uC92yiStJ53ELRPAQP2FZnXZnH5tJVg==", "signatures": [{"sig": "MEQCIDDVQxQYRU1XukGYC5V7g1jAeRJ7g5c8kX549LHff86HAiBat4jx2Mp7z8851iQTtkyZtL7ULyIVIKPS82tul/xvZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104583}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c2e9a1b13026f30b8fd37cce81142adce351a72", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.4.tgz", "fileCount": 9, "integrity": "sha512-epWP3WMvkZ3jmZOEE3k7/dS6D2UdHyUBi7b9JBm4lx0exXFwNTqN1ukCl4HQbkr+5htfVW9XsbEtFe8ldZv6fA==", "signatures": [{"sig": "MEUCIQC/BqTVngwwoJ+yn2GoKvxh9LfeGGGF+BRGXscVK2AH3wIgOKh0RBGESuhUjsVEfSX+L2fzMa64j3Dct+p3ziPU/9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104583}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ef3344d9ad7f010358ab9cf7c20b4d670b0cb71a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.5.tgz", "fileCount": 9, "integrity": "sha512-XS5pEn2JiK4y0mvgmxQdJXVEOJz3g5OjFkIpLSWPJJOX1L26V/byka7CmUok14zNazcagY8u1gj3MFSy88EyEA==", "signatures": [{"sig": "MEQCIGMC579ReH2IWfAd244wK9IioO2U9hsau+FSZiS8wk8aAiBDCSQ6rwgBF8ZmtAm9oXJTSFmSEVpiO1ufhfHs/2863w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104583}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-popper", "version": "1.1.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8948510f2c39a3fe1e7299a278e384e519ce9a33", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-znnBsoMdkK7y81sJIH6fZZtU2a0yfYEAAA078uQAyl980xIG/8vOIxTgi4vJAtRzPr5AgmmwE11qXfXx3vcsLg==", "signatures": [{"sig": "MEUCIHCZhAqCd4knpPakyN4x09WsPoFnY3lzQZ6z25PWwhKFAiEAgg+B8KPWzBwfpOGZtl7Bganvy0FrRIIs8soWvuMotF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104583}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-popper", "version": "1.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "24c03f527e7ac348fabf18c89795d85d21b00b42", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.3.tgz", "fileCount": 9, "integrity": "sha512-cKpopj/5RHZWjrbF2846jBNacjQVwkP068DfmgrNJXpvVWrOvlAmE9xSiy5OqeE+Gi8D9fP+oDhUnPqNMY8/5w==", "signatures": [{"sig": "MEQCIBtXPoOVHwchrx6rCASdJrI3v0dOzYBVg9Z3sjbOG3/LAiAmQNB1+ub/JheRBRZjBA7G3LeWTNIGATM+q5kjEmlMwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104550}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8f3280c68dcf3f0bbe6d99e754f02b64f1e6af64", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-olnjar7tI8OlQei+MI1c2Q912hg4ZIj7ce9juU8hVMA5YY7boLAcxY+KB4BveKFHxN+ktvp9WJpell0QeNXrOw==", "signatures": [{"sig": "MEUCIDR6Pohw0QkQxoikAi7FfbyErZJIXCjOZSc8f5kL6rqvAiEAwRJpGpBtxQiPbR5pxujSuSWjZdb1RN3RNNR4i0T6nB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104603}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bcc59b7f1f24d5efcdccaba322cea6dbc4ccc81d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-Y1iLPBL1FKV2wGJCG9LSOMSju6u4RrSjw0zhimZ13n72FbhaDkD+iKCQBQnuyjW9JTD+7HIOw5JZ2jZFhLRbBw==", "signatures": [{"sig": "MEUCIQDagfwitz855zc03bQEWor1kzkLsO8EkkRtiEQnbUknFwIgAwwgjnvv9QkDEdA/vmAZUEYxoWoLwfTEeZN9+sLdFNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c2daa6df953231e8a84444733838a8cab4e8c26d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-UPJ2gqVmte0R077PRcJEmGhpcnqohAFq1suLOysFWigm5Fii/1uLk+KgQxRwj6MHFQl+oVeZ0hJXNrsEzHQkag==", "signatures": [{"sig": "MEUCIQD1x/1NA+DclHPHY1yydvuzTCyt311V+zlameAj5yJBwQIgO+6D4D29gfAOGul8AiIN/QEpqtLn4vKhNkwNZVKff04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b1e5df3e399cefb36f45210a35ea0b33dc121b65", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.4.tgz", "fileCount": 9, "integrity": "sha512-DQdgNlvjlyz2I5/6L6CczIgykxejDUQLLp0Q5oI0PhblPc/HFAV7BKxDYRPrqO0CFCGNTKpA56fmkpHWnTKJPg==", "signatures": [{"sig": "MEUCIQD0qbzoAYLIeuMMXXP/lvJUXDpcckzziPW4ld0OfRSnwQIgE9NpyHfkWkngxtFmN2t/H2ZD13CticRHSxnle2ZktLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7723f803dbab6cd2eeb877c65fa0423efb564156", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.5.tgz", "fileCount": 9, "integrity": "sha512-RwBUTGC7HIeQEj7spCkfVMB7dOa0R3bDQzWReHaoK7ajmmnyDSU9PR38kUWKJrO5NeXGfqY4G1FZLBQ2zOacZQ==", "signatures": [{"sig": "MEUCIBFsgs43Vi0FtFfl8A2Z55Rgagrtr/3NG93tZKZHf/lLAiEAidnkqF/mqcf44QJ8otd1yNqm21Yr6p0eOGAeTBgBk/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "129aac8afc93de91b94f770082f797d74ca9122b", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-4CR8VxyCqtAwSE9Cl6jQWy9f4XTn27qy4rHDgtxssT1AKOiVg4lE2b/QSu7pnsii6p+3XtrU+gQKJw1BRFH0Bw==", "signatures": [{"sig": "MEUCIQD0K3JTEP0t+UhqxiAXcxmLCvsr9m8T8ACMeY7hgJgFmAIgI0YVrXUh73+pBCnHb+cu/xP/+onitA+t7vota2mUuzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3ee6fdee809b3f2141e13a1741f2ff81afd10603", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-zm2ztNceIqAMO5mj64GtnJBCXAb3YMIgXs9nk7uMmB25exgVeeM/wzjnajESgo4WqE75DcRpNZjtE2LcnVwh6A==", "signatures": [{"sig": "MEYCIQCrJ3z31hHhaG0vYrH7RIlwhQLbTuaMJjeohoWrG4TCawIhAKniIqLzVLUsSdmMjVlXXPsVH155GhRublUbjgbef9h+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aa08322af14073c777ab98cf03b54b111640dc2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-f0xes9iMJT9sGSXbADmD1h+Pc1X75poNBM8EC3EBEmBedk70z95vcXEC/mO9NfZLeFVwJzQyrojNK1fQhy9fbA==", "signatures": [{"sig": "MEUCIQCj8aDIqQbbN5BdFUhj929B2aysT0OqLLkkoOtTQULaqwIgJMgIrJ4NkZ1kDNgq+/UrvIUn0ruzVei/RpPSRSvIr8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-popper", "version": "1.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1", "@radix-ui/react-arrow": "1.0.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-rect": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3a15cfde795db09ff4bcf0b1f9f50e4d68bea3c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.1.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-5qwLAKTxgkjGTxGaqoTPrtoajQJOT/TAtRh51qJye/fdbef58qIOWAKc65WV//f230wjoFVlrbPFitXoCNUttA==", "signatures": [{"sig": "MEQCIH/7M9MMdDPywnAef5bF/KkKPWj2NCFnyv6FAhEa7zSwAiB/8vVzQRpRJ1codcZ15bhmLrJx9TlfLgV/Va4yuYIIgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105617}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.1", "dependencies": {"@radix-ui/rect": "1.1.0-rc.1", "@radix-ui/react-arrow": "1.1.0-rc.1", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-use-rect": "1.1.0-rc.1", "@radix-ui/react-use-size": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8534499beaf4db0d5be6520e935589b89ef4414", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-mBkOmMK2InGiA+c03fJeg6R5RGjKYN89Y0Y94pGh0ApvXGe/HuhLQyanfVbCA5UFyXapZ1XLJQDNEEamSFm+Nw==", "signatures": [{"sig": "MEUCIGjDhjFZP2UzEzARiCyUGQrJiI4XUSNVkXH+UhWOTUbKAiEAzTEtbnaIocPsEpsZ2VjGMfcDaDNs6fjUOLG0BIrygXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.2": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.2", "dependencies": {"@radix-ui/rect": "1.1.0-rc.2", "@radix-ui/react-arrow": "1.1.0-rc.2", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-use-rect": "1.1.0-rc.2", "@radix-ui/react-use-size": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0cddee999764ae2d964922904500af17caa14f1d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-hymRcLsYhZwt7XR/pDly4gFxDzSAwm+kwVDG2amdTRTKPG8ewoQpfmJRHhj8VgsxUS01C48RmYgYtN15OECdCg==", "signatures": [{"sig": "MEUCIQCdyvE5iU4kBRgPJfIlfp1NrqKfzvfh3B7BvwNivbzM0QIgSDZQPZKfUj0XiNwRk6gWIJebfMInuAM9FTMoyxYU7Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75543}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.3": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.3", "dependencies": {"@radix-ui/rect": "1.1.0-rc.3", "@radix-ui/react-arrow": "1.1.0-rc.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-use-rect": "1.1.0-rc.3", "@radix-ui/react-use-size": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dd4932524083ce57b24771afdbc85b57f4c981b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-1Tm6L08m5jwgq4ZQEUkc1/l8MwGxEbKeUvJb7ZfUa4Z9sY8VvYCkwKzoOBc/XxktxmjYmPABTxtA44cnbrXWrw==", "signatures": [{"sig": "MEQCIHnIf18Z7rwYQ4mPJnm5oP0mBhjdCQ/NzuzLukYScg/JAiBoX2slAtF/eKjIPfw+ge3BHcR9Wdi1+0RAnoK8/34NAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75640}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.4": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.4", "dependencies": {"@radix-ui/rect": "1.1.0-rc.4", "@radix-ui/react-arrow": "1.1.0-rc.4", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-use-rect": "1.1.0-rc.4", "@radix-ui/react-use-size": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "992657f3c9e0197fc46e7ad4eeb39f6d6ee7c750", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-icJjmtp+Wv+au6EM0aZZMDCL6o8gXv651QIWnelLVz0LsFzOIH+Xf2RbzB4s6iZy/SiYZtxUVjb6wfyrt20HFw==", "signatures": [{"sig": "MEUCIQCmVz4kH9bVbNRC9lBXSlrEpF4JMyIP3RxGBVNLfMD6rgIgO4fqUKK5738sbvzSTdGwDmZzUsSSF1tkrHNTdQTd0M4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75342}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.5": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.5", "dependencies": {"@radix-ui/rect": "1.1.0-rc.5", "@radix-ui/react-arrow": "1.1.0-rc.5", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-use-rect": "1.1.0-rc.5", "@radix-ui/react-use-size": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-callback-ref": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "acf47ed4ace306d4d386401b664831273956e5e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-x06/J5w4nUE0At82hj6kIhxXTnUbqnqanEHBmV94ikAanNjlL21jr7VQlFeZdF3zZooprwaTlUEHp6NjLuLURw==", "signatures": [{"sig": "MEQCIC0lHSeEYmIfnP2k1i+maU6nDTIYoSJTA3ltdl3gzpJxAiA7jeXI/DDExF2WJOe8xqVncyCYlNj+wHpVHVOlr0lAsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75342}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.6": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.6", "dependencies": {"@radix-ui/rect": "1.1.0-rc.6", "@radix-ui/react-arrow": "1.1.0-rc.6", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-use-rect": "1.1.0-rc.6", "@radix-ui/react-use-size": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-callback-ref": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "93a2346bbcaee5668451d8ab0d4b34c6c33d5699", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-2kmwKH0XGhYSsrJIvy5A10QNitDGaLH+rqjKW1+dCc7z+YphLB4V6vXgYtH0WVAFLCaDwxDQ7GmbOgFOJaA93A==", "signatures": [{"sig": "MEUCIQDEgGyWcjz6vqW+ZgxmoPATOr2MqZEu41hIKcQOysZhXQIgQWj9DuHbe9A8VlwqcskuTZh+/MD0N+/xz2ycG4f2hlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75342}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.7": {"name": "@radix-ui/react-popper", "version": "1.2.0-rc.7", "dependencies": {"@radix-ui/rect": "1.1.0-rc.7", "@radix-ui/react-arrow": "1.1.0-rc.7", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-use-rect": "1.1.0-rc.7", "@radix-ui/react-use-size": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-callback-ref": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4a6a684c8e163581b7f2a313cca9062b0512b935", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-0Dg+xpWb0nSR3tZ2eMa0LBxIHMocns0nuMOnsNLxaRhnJdB5TnvwL2lFXlIs6WVPRwABjSKxoiEgnN9T/tRMIw==", "signatures": [{"sig": "MEQCIHV20dqcwm8QOPNUcJH6fsIV5TsN+S5EA7ASAQuPAmk9AiBrq10UYz48Znmocmp+h+08deQcOdOV4xFksxbgU6ZOig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75370}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-popper", "version": "1.2.0", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.0", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3e500193d144fe2d8f5d5e60e393d64111f2a7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-ZnRMshKF43aBxVWPWvbj21+7TQCvhuULWJ4gNIKYpRlQt5xGRhLx66tMp8pya2UkGHTSlhpXwmjqltDYHhw7Vg==", "signatures": [{"sig": "MEUCIQDAq+A9qjHMMLqMVPhg70WGccBE/ccUAM1W5qraJQJmLwIgaX1rTJBfqgisrMZn6toM1kvqTZFFJz7PDRMgRDC+wI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75292}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1": {"name": "@radix-ui/react-popper", "version": "1.2.1-rc.1", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.1-rc.1", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dfd19022b8f863ce62dd81b4a0226c86005ad3aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-K/g1rlMh19OyeGwm+hUgeoGJWwz6zrPRpMJEIGYS9x8FKCGkx7FDUH5wtbVNFWXg2C8/QlpxA0CvRIp+9IuK2w==", "signatures": [{"sig": "MEQCIDFlUiHW8lncZYJ2/P75bOy56m0Hiz8a4PqkF7N9WvXIAiBvvym569+M+R+F7AiVNZvXS/LI6xBv91xsntEG1+02Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75070}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.2": {"name": "@radix-ui/react-popper", "version": "1.2.1-rc.2", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.1-rc.2", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ff42dcdf2454364a44027993442759424c5ac02", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-PK1E8piJvF86fqFA9JpoDP680aLpbBmAbpY+DS4GQsO9GiDaGIz6Po4hmRiW1yS7Xro2C2S+Lkj6A/KvPBTkQQ==", "signatures": [{"sig": "MEQCIDI5ISNNoUnciYuejZoHHI5EHAeGgxM842lCgh3RXuOhAiAQkzS4LDp9OLskPXQ89G1TwgVbc/CqjRfjVd9bE0njQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75070}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.3": {"name": "@radix-ui/react-popper", "version": "1.2.1-rc.3", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.1-rc.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "97f0ea9744edb0f30eba17c1207473f167f30517", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ZpKmNKC0auwK9D/lLnCKrCju0G1fGY9eia/VJpI2M/A/SI9ELkkeKX7/kA/tXzov9FwVZvfSIlQI6iMSPH1FPQ==", "signatures": [{"sig": "MEQCIAaw6K2TTNEw0clb8Lo2/QCC1FGTAPDRSyE0upLhC6fzAiAzZ8vHI+sSoWp9iqk5Gq4eaTe+ar5kc5NY1wiS0fDlhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75070}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-popper", "version": "1.2.1", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.1", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2fc66cfc34f95f00d858924e3bee54beae2dff0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-3kn5Me69L+jv82EKRuQCXdYyf1DqHwD2U/sxoNgBGCB7K9TRc3bQamQ+5EPM9EvyPdli0W41sROd+ZU1dTCztw==", "signatures": [{"sig": "MEQCICNXByfIpZ7ScymUrPt3NEO75llx/7PkneWOXOxL0Vt8AiBLibna76NkekefOF4CBsJeve+d+w/Pd+UBKi0wOiY66w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75022}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-popper", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/rect": "workspace:*", "@radix-ui/react-arrow": "workspace:*", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-use-rect": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ed68c6a9528b1375d3a8e4a8bd467b04bb8a160b", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-9i3PiueQuJW3bfH63NIv3Z3j+0fnjfyKjcsN/6YEuN6XyWzxwPrskmtX5m5NeMVG3R4Oa4bdHZTqTRo71l4Kbw==", "signatures": [{"sig": "MEUCIQCgOHNwXo5/QDfYLYuyanxyRT4Hq27MWeVoHvyrG1KoTwIgclq6X4WJvNzreU9WoISCk1XJ1KfG8KuIwASUhZWkvjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75061}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1": {"name": "@radix-ui/react-popper", "version": "1.2.2-rc.1", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.2-rc.1", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b761de080a0ed1ec548c7e93cd819c433fe10e02", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Ns1RVFpI3O9B4zxlCb6XKJun5083hnXUKiyJGHcCZoZNjX1IEak/0oDCG4I587t6UZZDTmVaWxYkK4JUa8umIg==", "signatures": [{"sig": "MEYCIQC6tVfvzN/0T83Nlz4smpSrjemSV4Zp3T9LojbnF0/BvQIhALsCeOuA8J2NRM30sXOAJZM/3S0oiJvZICcb4TEhGy+g", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75278}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.2": {"name": "@radix-ui/react-popper", "version": "1.2.2-rc.2", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.2-rc.2", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "18553fc1f21d6e123d6dc3ef196fde7e1eaac02f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Hzx53MIj2irKjGKbtIBbaBb6KwATz/rPc4bqxFSl0H0PE9BlkPbKq72lwchSfHEVeSLVtCisOrVNd6JxQ2313Q==", "signatures": [{"sig": "MEYCIQD5NArG3FdL6scF8ECUR20jOPgUK5Re9wmvyOj9V4narwIhAPgB7aSzmVuKj6W912e3yMS4JA0wqfuHHVLl3BXe4AV8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75278}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.3": {"name": "@radix-ui/react-popper", "version": "1.2.2-rc.3", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.2-rc.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1d8c8bb71aba846f1e138c93c171cc79b5054626", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-j95rVis2H16qEO1XXkRFXvvqsi2LnePwN6aHgy3E3LQANoldoor8pOj8ZdukPLJmoM+cah2eMEhswbMRrrxDtw==", "signatures": [{"sig": "MEQCIF4glUDyim0XjQrbVg/mQSCbf2rYb4troj3CkJW2BGT/AiB5XRrMqU4O0+8do+Dzb4Dkjujidl27JWPQ20Y7OVOcNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75382}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.4": {"name": "@radix-ui/react-popper", "version": "1.2.2-rc.4", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.2-rc.4", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a52b4e775ccde75bb575ffb7a2c25ec94b958844", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-4roQHaSJEzRiLO5V32GX/Tot3c5CwnRKtDEKwHr+zWfPmSwkdCJZd5b3jScGHiw2q97yFQswYWu8SsR5hTvuJA==", "signatures": [{"sig": "MEUCIQCJwivv5dF+1N+Homr34G1NBQnEdUN5mGGM2OoVrzT4fwIgFhFyWWofYTkyU88mFGMXZ+HfXNyN62cOy81O/Quf6jA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75382}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-popper", "version": "1.2.2", "dependencies": {"@radix-ui/rect": "1.1.0", "@radix-ui/react-arrow": "1.1.2", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-rect": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d2e1ee5a9b24419c5936a1b7f6f472b7b412b029", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-Rvqc3nOpwseCyj/rgjlJDYAgyfw7OC1tTkKn2ivhaMGcYt8FSBlahHOZak2i3QwkRXUXgGgzeEe2RuqeEHuHgA==", "signatures": [{"sig": "MEUCICd2VBCoadsy5Jg1AnlXCH8aHdo1+3Q1DgMhPNeV73D8AiEAkg4N0toSxDAGa4gs6wUeV4ATQrdPNI2gJ6uEVU0a6No=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.1", "dependencies": {"@radix-ui/rect": "1.1.1-rc.1", "@radix-ui/react-arrow": "1.1.3-rc.1", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-use-rect": "1.1.1-rc.1", "@radix-ui/react-use-size": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-callback-ref": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98f5c16ec14f7ee6e201f4665d7b2ce933e61e2d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-QJN/VeRRVVoLte2AP9Cj68eAKdyvrXW6pChnEiZczXIBXJRcvpxf4ASUJ+huj1JkfV3lJsBu5gPXnsPQRFYfhw==", "signatures": [{"sig": "MEUCIBaF6wex/NXplOFZCwMo3W8cKx6gkV+CIHrStAaTGfwnAiEAtC05p3jKN/HHb4jo4gbHqsIi1HNK8jTgRllpnctV1AI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.2": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.2", "dependencies": {"@radix-ui/rect": "1.1.1-rc.2", "@radix-ui/react-arrow": "1.1.3-rc.2", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-use-rect": "1.1.1-rc.2", "@radix-ui/react-use-size": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-callback-ref": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7146c1403f62f8afa1825adaa84385c350780e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-v9UL25YHJ07/vCGW8Dd3TjGwV4uwA3rvwrlgYOL1O9nAwiOdIu2YMtB3bLRkbEe7Uibbu/gHdUWQE/8Cm7QT0Q==", "signatures": [{"sig": "MEQCIBConnWh9Jit1IgwC0e88GJTzTppwCVvrhMJbz/E7t+5AiBa+t8XPjVkLyiG4wQ7kluIRDmtgiC3016ftYJAEYDcsA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.3": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.3", "dependencies": {"@radix-ui/rect": "1.1.1-rc.3", "@radix-ui/react-arrow": "1.1.3-rc.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-use-rect": "1.1.1-rc.3", "@radix-ui/react-use-size": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-callback-ref": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aaed66b3547b43ca617577fe0588756e18d3f840", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-W+ND+KrcQQWvFT0vKz/dBAL0rvqez9vESnEjjVJWH+DQ7c5Sm+GC4YV50QnVS7TbEttTRFptqORFJesgDxft9g==", "signatures": [{"sig": "MEUCIDiWcZ0v9gtf1Hd51An8hQ4mdLcUKKNRWZTHppQhMoFGAiEAt5dlSYb7h7nStvonM8gOFapKwBQP3fwwRvXNrEDDvCQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.4": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.4", "dependencies": {"@radix-ui/rect": "1.1.1-rc.4", "@radix-ui/react-arrow": "1.1.3-rc.4", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-use-rect": "1.1.1-rc.4", "@radix-ui/react-use-size": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-callback-ref": "1.1.1-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8ff5baa01f8496df9c00d0d0087c8aa77a0582f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Zc2dACsXz/NErh5Lredqj5sQFJgYjok+BfPyiDgGR53+u9hk4gBdexLmmEjfG5NNmsMQj92CwY2blmU3jwzTRQ==", "signatures": [{"sig": "MEUCIQCDlhCb94zEQp6AoGpqBBCcJeK9Q2kjpg/pSTmW02zebQIgH0kYpjF332tKeZQC2iQnmaXuDpk6Z61nerrTy2Hpt0o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.5": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.5", "dependencies": {"@radix-ui/rect": "1.1.1-rc.5", "@radix-ui/react-arrow": "1.1.3-rc.5", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-use-rect": "1.1.1-rc.5", "@radix-ui/react-use-size": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-callback-ref": "1.1.1-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "deffd414ee8ef34eeb0da5db695b4fd475f2c1b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-te5fHZQL/Osm8xk27UYeCsuLymfCor14kllM5EzE4w3aqpmwZ+jfVxp0bL+hhQGmioB5rSegSrL69h6yjO635g==", "signatures": [{"sig": "MEUCIQCjAX65ULQPBP3GKKvUzMFMIZH5CEP88IERpBIatOUnbAIgPMie/zubDhQ+yNJB3NzhHX3niiQgIJhqsWdN5FRYr70=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.6": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.6", "dependencies": {"@radix-ui/rect": "1.1.1-rc.6", "@radix-ui/react-arrow": "1.1.3-rc.6", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-use-rect": "1.1.1-rc.6", "@radix-ui/react-use-size": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-callback-ref": "1.1.1-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28d2919a5b2f8c5925b0c514a4d51e914cc5cbcd", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xy21Y7q59nScPflSmF99QifGvjlna5vdseUxNuIyku3/y47MNiH2/s6oRu3SsfFf+NmQ6QRMk6+cWQaQqqbskg==", "signatures": [{"sig": "MEUCIBcK65t6bsG5WmIet7Yz7t68pJMAbg54Arew2IwOKw6BAiEAmkAyZNXEGEXfAWODnl6xiOhT8hARCkaEw2/Oa3RVsic=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.7": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.7", "dependencies": {"@radix-ui/rect": "1.1.1-rc.7", "@radix-ui/react-arrow": "1.1.3-rc.7", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-use-rect": "1.1.1-rc.7", "@radix-ui/react-use-size": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-callback-ref": "1.1.1-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e5e269c95b4174f2eb99c3214cdbb482a13946a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-yDzY8EtpKc9SgdXZm2i7CwEgfPS3CDRIoU86jXfswseIbLTXUA/RKOCa8hZaBPeq8U5dlGsda1C8nf1n3RAxgw==", "signatures": [{"sig": "MEUCICehcBoPngKnqUayvQtAPc/A5kReRYCwehUKLfuwdfZxAiEAqS/x0DTH1tXsQX6qyoUpuFzPrzMidxnCIyC8dCi5Yus=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.8": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.8", "dependencies": {"@radix-ui/rect": "1.1.1-rc.8", "@radix-ui/react-arrow": "1.1.3-rc.8", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-use-rect": "1.1.1-rc.8", "@radix-ui/react-use-size": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-callback-ref": "1.1.1-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "92e1cb6bd2f2c2639027e47ac3d49e38cadb3a71", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-OANm4h7zwlDVrQYn0Re7X4XcY4kB1iVvpB6pG5UhQllq/grorz3xHauxYzWDgWCnj5Erm5xOLe1xQAS4fPhfKQ==", "signatures": [{"sig": "MEUCIQDZ9F5lehkPUfoX3xS3UACSQVSO6IfqO91ZO+tRcdQCtgIgPA5HNH+yAsirgCuljfjokBvnXN4amEHQGmri5jtRQS4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75814}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.9": {"name": "@radix-ui/react-popper", "version": "1.2.3-rc.9", "dependencies": {"@radix-ui/rect": "1.1.1-rc.9", "@radix-ui/react-arrow": "1.1.3-rc.9", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-use-rect": "1.1.1-rc.9", "@radix-ui/react-use-size": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-callback-ref": "1.1.1-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9bf76d9a6e641aaa1fcadf40c18aa9a2efaa67c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-rcA7KGC0sxi+5ws7eQ4UK7aqybNnJrVY9KCZvZF435hw1Z84XFQUwP9W1sBYZmasZn1pcmVTVQakp61WMsiwnw==", "signatures": [{"sig": "MEQCIAsrtIbb/lsHBpC9b6OMMCxvW+9PYoe45d+bGAasDi7uAiB3m+ahbhb8iXMgk3DKDmk5tK50aXdH7g7XeXsvpzxARw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75814}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-popper", "version": "1.2.3", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.3", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3b6ef3388fd209bb46341e1e40125b75f07f1304", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-iNb9LYUMkne9zIahukgQmHlSBp9XWGeQQ7FvUGNk45ywzOb6kQa+Ca38OphXlWDiKvyneo9S+KSJsLfLt8812A==", "signatures": [{"sig": "MEQCIDG7jXLuW1LZvgsHjU13R6Vbix/BjOwLzhpj1zUHubxpAiBp59jRzhI6GROBHPg7tcI96irsTrmNTGtANfHDnl024w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 75736}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744311029001": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744311029001", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744311029001", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb4e850e8eaea4a38c5334fc1fc7e1647c57c4de", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-DXk9/4JZG8D+pGMX5j/WuMmKIeCU7OhTsRDiTqqyJDErzS+y0lFwV9U2DEv7fNWnObJ0Hq9jGeZ8fs8WItIp1w==", "signatures": [{"sig": "MEUCICrsXcRYZ4GC2ODkKyBfmxsTkbWHK7Ejv426Q8Az0/WeAiEA+yAqaUMl9hx4IOtYcGX7LNEEZXVcg/AGeZWZxijhHqA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744416976900": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744416976900", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744416976900", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab8323bb628ccaa0139c33e8f466e3c2d02e0c45", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-4iezb1kIL2JJX5hahgQ0OCgL0W1bTt8BeqaUwqjiV9FxHYnqD7Z99/Wimsf/L/gJdPO0m44MonKy/QvO7HoPqQ==", "signatures": [{"sig": "MEQCIFeUXlcjE7+GSD1fGFV2s2nioSJ2qIRnWDGqqnz+lSxqAiB/FeniN0ErSN/RQbeeS3Hj6c9/jToLOm0YGaSc/5TofQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744502104733": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744502104733", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744502104733", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f6cf3ad9004f451ccc0daab0ae116073e22cf2a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-M0Ku/gsGvb3vM8XCgu0AIvO7I5mDYyJwJGvhMmDxRvkLJ5L8H6yuV2aanHWKxd3/JMirvNcV2LIuP/REfRhNLQ==", "signatures": [{"sig": "MEYCIQCkneshQFG+N6SUJXBRH6dgA/y5xswL4M2ix5LfVK2zQQIhAIUqMgJcZ3q4KSjTOB49/teUNjzQ7iu4C6S922w4WdAP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744518250005": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744518250005", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744518250005", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a1048937741a4be23f942894268946abd3069b5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-UnFuzUI8ZZuQkM6Qw+5zUGgJxSTeAcPwxeIdWgNAaIRcV76BjSQxRshLkWLsZ3o9XY+9qxp2Y1ZRDM2vcgTp2Q==", "signatures": [{"sig": "MEUCIQDVebG9kLxsciHZLpkGfgPPfud59W5PGLwORgNY+icmlQIgAoS8B71MajtT5LhrPyVVMjRuVnXCJhxj7zIB5ZOYW2k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744519235198": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744519235198", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744519235198", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2aabccf40988c4c301cd7b1abfd868fb196ed26f", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-LHRvHiMqnlQT92bF9LmTT1zqsICU/BuhE0oGJDJSHm3ElzguMk37LXwNrHsXXaYm1aWNNN05YFhK987lgkCRqg==", "signatures": [{"sig": "MEUCIHvNlGj9NV6yafNCULkOX+6F910/tICtXre4KR4L5CzgAiEAqWkG2tcBSUOWmfEmj/uYbVHp47X6t8MlGIeSXTfr4vY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744574857111": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744574857111", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744574857111", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c94dc0c2c114fba3d156cdf576cea12d719ab54", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-Hxj5UXLKw5LpXYDDBlYAwc/3zZ4VvKk9CpAr7w9+UZSlkIMShZGj/WeycNTFRYPQ+17c/5PtFw+5MkOH6WAD2w==", "signatures": [{"sig": "MEUCICxw2hFtzeDlg2y4iTwc9AHHKjd6ST5jQx4IzUSryr1fAiEA0eXAVubCwGV0FTrPosjb0OLUwXJG1VIPdMhsa3WY5jA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744660991666": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744660991666", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744660991666", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "75ccdfd51f4055bb3b9e7d73977541133f746140", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-1ruq3c+O/3Ycj2vJOasYlvVhVXjq278aXqZTMGibkHe4d4BVCPQe+wiulg+1MeifFjs2J/rAL8w1r4Y9SR9kMw==", "signatures": [{"sig": "MEUCIQDIkGrG5rkMaJR9haoqQoYtl3XgbbMzWL9QV+lzZjj1/QIgDLciirkNKCawP/FdLsqrxQNhs9+XLCTA4PrtAP1sCQw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744661316162": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744661316162", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744661316162", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9677b9a788ba72cd336116e16ca2138b89f3654e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-8WOfS0KD30hb15DdEL9WSoOnzOrtkAMHu2ST2mBGc/CWjdNWnRMduKmDu2NscFv3Ppfd44l8/9MZuhVOppIYJg==", "signatures": [{"sig": "MEUCIQDuXTNc/vv4dZqc7KYh5PB0QOAFeKHy+nLZJwoJAT/hOAIgcnDk2aUcuVV4DHHnyXeWl4eRSmWzoIkvT1azp+P/CaM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744830756566": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744830756566", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744830756566", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3f0ac0e7cc5ce0615200d6ec43f8bcde3d0d8d86", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-b5U3mXTtZoB7WPXM6oEw0mEZHQSFIg8zZjZc/23sqH16HbEU5HWgZG34jvFiMR17ntMnl4XEDXzdFCkOR75ddA==", "signatures": [{"sig": "MEYCIQDqAygD9TA7QuVADB8hXv/uTJbe7frHBvVbBWyXBIgqNQIhALkI8yxO4yui0UMGV2PiViNRi6hTAyXn7rknsRINVcFR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744831331200": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744831331200", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744831331200", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "054b932d795fcc8778f1cebb804e8e6dd0149989", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-PeawWQzdDpE4CTXt6SXarFVEzvL2I2nwgtyuJcasnezdrcadUYPYZ1II0ZCxYoPj0RRaF8Cj/eN3wLk7mtAnKg==", "signatures": [{"sig": "MEUCIQD59SbbBwxLUMj/pYcTz0gKo7GtePV1OqL64QQjJd/oPwIgQr7PQINrYrdA/XhicrmNBxeTziGXQrl1UirsfoLF328=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744836032308": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744836032308", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744836032308", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b27f7b3f7a43e30baa25ca41325596a8140499ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-Z5VYgR12iJzoitdVx0G0mGuWNDnLUgvaXAbQh4dghfeZF2ARfQu4tlPE3hVXTTIihXKcarHhhvMM4mkftX/eoQ==", "signatures": [{"sig": "MEYCIQCsMRhGZI+coH2/fdUj8xCLCAPY9C1PAm7gxysFp93IbgIhAMN9evExd5rDkFzR66RddFyKJjpAs/1PkUjHy/c0xS8+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744897529216": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744897529216", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744897529216", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "65643b2ac6618c9744d31a4b21a0b7828043ad61", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-WUraCnu4FQ8mEQJc/kcbn+HEdk87pNd8mFbzn83KiAvbP+JgCUusnHxQIZ6pIDhoFWca5usM/Tczxi3BASBkCQ==", "signatures": [{"sig": "MEQCIBHjqEB6zK+nRpRfiKHNA1OVCI0smv54NVIVK+7aoyNxAiBA7/X5tT9e35xPJsNPjiKUVgyx1lD5wmKEFBPKIBDZyw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744898528774": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744898528774", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744898528774", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "17fb26a4e1e7395bcc24704ca53f3c0fd4b2f26d", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-Vz/cdEeYUIu/Ix0dbyrcz7rQbIq81Qt49fJqx/7rHifYihAYEcX6c//8fBsflgwndLwbOhoeCJmJtrD+VTfl7Q==", "signatures": [{"sig": "MEUCIGKne3Dubko2GdWdZlCRkvOlQSwdSKxpgDwydhQ4AbuhAiEA8I9t1bqxj1Ld+9Wp7/M5rGiqjdu4UpG3PcGBA/cJsmc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744905634543": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744905634543", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744905634543", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0d593182d0e3adc3d4549deda567e29cf57aa349", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-qTgDa1ogsWd+iUxJyMNIFTbz+dOkykTyJt6pkkuObRDvQ5eheLsIkBjDKFQ0dvbkgZACkSBZ+hlzJnJ/EAZhjw==", "signatures": [{"sig": "MEUCIQCi8Sof7Z2Hdj2PAlGkTxiDSnF5MkDcYgQdBqUSThWzzAIgIvGDF4nZlTFH2Ru/q5mQgLpRWEdNpRC0PK3AWSdSXkA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1744910682821": {"name": "@radix-ui/react-popper", "version": "1.2.4-rc.1744910682821", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4-rc.1744910682821", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b8f1263fdf1d64880cb9973a43a514d933c623b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-/4xY3L6Fp8Nlwsj6gS+l+3oMRu0Ot7FEzUR0gKyUiAi6y5Drb8tLnNVarkwoYs6NbaT1JzZGnm5uLPdf/uiReg==", "signatures": [{"sig": "MEYCIQDZPRVdgIBYDB0+uqyWJQCwqrOlUriij3D+YCsJiH1EAgIhAK4roJNyheHHuebWA8OduvckdkLWzwfOsy4sywsUDcJu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-popper", "version": "1.2.4", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.4", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8fd6d954fca9e5d1341c7d9153cc88e05d5ed84e", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.4.tgz", "fileCount": 9, "integrity": "sha512-3p2Rgm/a1cK0r/UVkx5F/K9v/EplfjAeIFCGOPYPO4lZ0jtg4iSQXt/YGTSLWaf4x7NG6Z4+uKFcylcTZjeqDA==", "signatures": [{"sig": "MEYCIQD5KXQ1t01cHq81oY75alZPdxAYJ2EjRHNUWzU1YkT0XQIhAO9AlbxHx/gLqDg+ooue/GvIQS5KTMpdPowKmBPOOHvb", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76288}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1745345395380": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1745345395380", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1745345395380", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1adf2e3e6cc2368da640ec3d82ff8085563bf117", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-LBAsK8/5pdGCIy/sfcehqZ5on63VchxgbvLyEItW2s1WZVE0Om6SZkRBVZnv/EkENjPde5Ytovesu8AQYOQLEA==", "signatures": [{"sig": "MEUCIQDXRfqa8Bpa06vxikmwweZeyp/uwS26ZcoHs+edtIXYQQIgY1Rcmv03TbaPQ1te8mvj0N5ie1tKdGKbjXoAe9bdTxo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1745439717073": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1745439717073", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1745439717073", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5e83a0b8e4006afa31306502cfeffcb5623b3da1", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-gAY8TfprNKo6iv9QXvrWBMhReieer9/Iw8z4yS0kuJ8SOUAjgMdzjkN09wnYnrtGRnBPcv4UWzFtaY64JlIQrQ==", "signatures": [{"sig": "MEQCIF7x+eJkzKBbW59WskYuOhwg8Jm8EQAsbsdPkYjXms/PAiAIEqcba7YVyY3Ajnb0p/P0VUYkHYy26mq6t99bBTXJEw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1745972185559": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1745972185559", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1745972185559", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "146a47f2441c9de5991e02fc800fbd0746f3af08", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-6sVcsLpZT+JN0mwSRnT3J7oX3mzotnv3Vz3Gp3MQUo8mA5r2SO5AV44mzimHi5XyJTJk9XlWAf2z4pHwxefong==", "signatures": [{"sig": "MEUCIQDUmhl4FhYXSOtkoSjVmAqxvmd1UBbos3f369X4l+KRewIgclbQCOO39fdVyFS2VUBl3QNBj3dJcioscjXS9CSuBIk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1746044551800": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1746044551800", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1746044551800", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e647a260048b2d4853c577024c2f911b7cef7dd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-JFaQSL3dBgn+NiGYDgpy8esx1QvQbjnMjFIAPDyLCFZlhn4s+P0vQSQGqD8zjSr5dBBQMs3HslPIyvq8kjpyGw==", "signatures": [{"sig": "MEUCIQDmcxGGH9Q8xvy5hePTVqt+WBOZHniIqLkn8F+RPb5vqAIgA2wJUKaZHXEl8ho7NO3UYE0/xJIa2xaoTqoCYE/vb4I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1746053194630": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1746053194630", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1746053194630", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d3ddc496ce2c6435e41fcc09a8d576817779802", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-mrmmRjgfJ6tHvGaS9mh/kQcqDKYGPPPizSX9rB0Gac+iPRoH8isSXbzeEP7pDZcx8fhWJ8pjjcVc0NrmgOFTVw==", "signatures": [{"sig": "MEQCIFLQtSl5aT6abZMl8eeyoVjBxav4l9Uh66QkYZBsnnRHAiAYWdsC8kiMiN3ALHd1tmuZf6vceEx5my5iTXIaVqM33g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1746075822931": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1746075822931", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1746075822931", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "af5bb0bb0d0b0beac9c2c1548d473aa2f2e97450", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-F1hKXTBS1BtnVHo9izoe03L7whemMGDuX2LAZa9jAuNQeIopr2tNImRTi1M7e2mT6PzxGgLULCBN+wmKO1hvfA==", "signatures": [{"sig": "MEUCIQCROMMmPgh01nnbwlXpgxucNxt0dVLzV6eVS8qdYKhThQIgYJVfMJwltwd5nXlc1/8hwnxn7cnPxxSpd6tgyyJH3fo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1746466567086": {"name": "@radix-ui/react-popper", "version": "1.2.5-rc.1746466567086", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5-rc.1746466567086", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6add69a847777b502e3e42df49700cf02268b6a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-huL94Ng4aCMP9mZM5F7zEwzJQJwR+wWGCVxkee9keLgnBT5YLffNBLdMqTO6ekxL2b1RhEKBXdBQyeOgjSSs2A==", "signatures": [{"sig": "MEUCIGznZKP3AKQzLcZubvR4sew3GllGdbOOy6q8sYhV3TjNAiEAoDj7XVEwvSS+BC/H78I34qBzzXk27ZHndC8bF/Nb4wA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5": {"name": "@radix-ui/react-popper", "version": "1.2.5", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.5", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7c670749d4fa943b3462f008c4618df494751dfb", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.5.tgz", "fileCount": 9, "integrity": "sha512-in8+gtEL5YCEm6pflDr8JBsy8+7DPYPNAYaMj/sorPT/BhzOQRWYXWrzJi1wxahPYTc1EzYuhz0oP/QQU2XHwQ==", "signatures": [{"sig": "MEUCIQC6yY538E+adTsudyUGlczrZi0wlTm+pkXa7LikhLELGQIgJgtxq1hRe0dQ0MYSDSThrP4zR18EYeIAqJENjl6Zzhw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76288}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6": {"name": "@radix-ui/react-popper", "version": "1.2.6", "dependencies": {"@radix-ui/rect": "1.1.1", "@radix-ui/react-arrow": "1.1.6", "@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "227d2882f19d80933796525c7bbd0d3ddf699ac0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.6.tgz", "fileCount": 9, "integrity": "sha512-7iqXaOWIjDBfIG7aq8CUEeCSsQMLFdn7VEE8TaFz704DtEzpPHR7w/uuzRflvKgltqSAImgcmxQ7fFX3X7wasg==", "signatures": [{"sig": "MEYCIQCcrGEgyxVIP+XkY1YSWze+9qDhdrci4quv1WNQBnG5ZAIhAOATpipMj/IN0ZFr/Y/OkHhUq+FANAsMQugFNODmzp8X", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 76288}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1746560904918": {"name": "@radix-ui/react-popper", "version": "1.2.7-rc.1746560904918", "dependencies": {"@floating-ui/react-dom": "^2.0.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-arrow": "1.1.7-rc.1746560904918", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-rect": "1.1.1", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/rect": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-dbKdhmSiSeFH+AakP615Eg5uW6j0dgNqmEPXrfsA5usrl4pJjcG+//SkQjokBq3T3ZlkEMYzpu4tzX5PSEy5Ng==", "shasum": "384aafbb7fb495c567f5e9f02995cfc2169e8fe0", "tarball": "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.7-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 76351, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCbzRZQ2bu/ksEUapkiCSL6K+OWmAu1Xnl8OVgCfzjP0gIgSAlqSC3tbDIwiYaF43z9+6iIC0pHMFiWaCjbvrPw0Bs="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:27.956Z", "cachedAt": 1747660589645}