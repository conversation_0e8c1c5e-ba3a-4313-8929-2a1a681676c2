{"name": "fast-deep-equal", "dist-tags": {"latest": "3.1.3", "beta": "3.0.0-beta.2"}, "versions": {"0.0.1": {"name": "fast-deep-equal", "version": "0.0.1", "dist": {"shasum": "18a9a0a1eb9f64308696c451e758e0dd593455cb", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-0.0.1.tgz", "integrity": "sha512-BoSRog3gyM/Op9zNY4D8baPC54Qbbim86mf8ac8zdCqZVtO7xXbwPxnmi8q+dR/ntN1Hqv4eQ9LisTB43yms6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeTW/wRgjxWpwbiUOVNi0btb9i4u2RljKC1uwmqWTQKgIgayEMJ1J6iFqwUbEfpk5qDWnJBORVyosZ9dBCaUBbB6g="}]}}, "0.1.0": {"name": "fast-deep-equal", "version": "0.1.0", "devDependencies": {"coveralls": "^2.13.1", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2"}, "dist": {"shasum": "5c6f4599aba6b333ee3342e2ed978672f1001f8d", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-0.1.0.tgz", "integrity": "sha512-hPWh1g6qiKvTahrwdMaLXVQ3LEIJVfaE4rlfjjR/3p+Yzhfelr0RPGTnBmRwxJ2ffhekJ1iqYPEgpNTvLe2jUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEKl8M/ey60TIpKvnboCbB5DIFVBaUMIq4KxQKCy0cXzAiEA7eJ5BWZ1RqTyCZ4/kTXUfI/UIW87f7Snwn7ZZUIxzAI="}]}}, "1.0.0": {"name": "fast-deep-equal", "version": "1.0.0", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "^2.0.2", "deep-equal": "^1.0.1", "eslint": "^4.0.0", "lodash": "^4.17.4", "mocha": "^3.4.2", "nano-equal": "^1.0.1", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "shallow-equal-fuzzy": "0.0.2", "underscore": "^1.8.3"}, "dist": {"shasum": "96256a3bc975595eb36d82e9929d060d893439ff", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.0.0.tgz", "integrity": "sha512-46+Jxk9Yj/nQY+3a1KTnpbBTemcAbPySTKya8iM9D7EsiONpSWbvzesalcCJ6tmJrCUITT2fmAQfNHFG+OHM6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCx4qLZzsGfwB0A9s2I3sA2oZmZykDwMXVd/bIwikG9AiBSnBjDi+XjGZpfo7tDcDYJ5/4mE8/mhBuDrc4owv6CcA=="}]}}, "1.1.0": {"name": "fast-deep-equal", "version": "1.1.0", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "^2.0.2", "deep-equal": "^1.0.1", "eslint": "^4.0.0", "lodash": "^4.17.4", "mocha": "^3.4.2", "nano-equal": "^1.0.1", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "shallow-equal-fuzzy": "0.0.2", "typescript": "^2.6.1", "underscore": "^1.8.3"}, "dist": {"shasum": "c053477817c86b51daa853c81e059b733d023614", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-1.1.0.tgz", "fileCount": 5, "unpackedSize": 5254, "integrity": "sha512-fueX787WZKCV0Is4/T2cyAdM4+x1S3MXXOAhavE1ys/W42SHAPacLTQhucja22QBYrfGw50M2sRiXPtTGv9Ymw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDAvfRPgC7Af9p3+BcVhpYXo+MX3GfYzhyVlOq1F6Rn1gIgQizMrL0sPViaiv3PJy/E3gJoFRLIGz4W4FA07UClQEg="}]}}, "2.0.0": {"name": "fast-deep-equal", "version": "2.0.0", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "dist": {"integrity": "sha512-nS7DU+NOwYHqiTuF5yHkS9DAdDOMZkqfu2+4QEmqWGBFEJjCemy1gfHF7g1vNdB/9hL5HpdDCkU1enn2sQ9xAg==", "shasum": "eaa6d36ab9bf2ffb5fe3aa55c7ed1223e9cbd8b0", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.0.tgz", "fileCount": 5, "unpackedSize": 5410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5DnFCRA9TVsSAnZWagAAfoAP/jsUlFvBzx4gvLiAUq+h\nCv4KWU/D4F8An3Jsso3VTwUPmTH4TdjSHM3p9Hp66FycbCzQdU8YR1yjEFuQ\n9e1u5OnZwuQBIWp3ZVTYwn/sQuxHEbDWfCDMMiIXHsrsgd6GdVw4G4v2GBqG\nHWOWY2s6upZjHZrb4xWGGcYumWnf284T5yMZOR+N3tf4SQMJ9oDSBYCyh6IR\ngjeTkfU3pYvcVMdgfN+tLWnwp1mFcdI9GJLxgVmC2RF4f5n4tZGlu6e7EutX\nj0vrHZLyC6iyykTOShg5qxg0tnUq899yeaTbo2ZFt/80DjXWUhvHoyRIXPGn\ndJPmyTtAO+/SQlPAMSkDKXoubqSSTGJWDYZhzWVlqyarwN8HUd5eTsUn8unR\nUqeIkm3djDJt4y54yPEbj7g20XMbwiGcJvRsSrJ6rZ+8M044pa6oMzntH6CN\n20MyaX6fffz1HhmfCSlfXiNdnQNwvzXBIvCBSo5baQdkGLnRDE5VBjAxEolS\n1JnhtGKMPXLk15e7MBJCrx6/Hj7vn2JyVnOjYxEMCrm3v3W5hJCS/mDBFzPt\n0Dsgj4rRqzqqeeDxORIxeHBARcmHT4OoFG0rp4WpO//mQ6gdwXn7eZbssiXq\njRl2GGHeuak+WlntWb48cZ2ztMFFpabd7SAfZJDeN5zDJDvtFE7tyGlGt89z\n2dnO\r\n=r5zY\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1ROrL0qeuVj+zL3D3RcBjTIbC//f0pfySHslZXHhzZAiEA4+KJEZBXUZevc7HHKmIaNQowRkhclzS4M3jKOShzfNU="}]}}, "2.0.1": {"name": "fast-deep-equal", "version": "2.0.1", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "dist": {"shasum": "7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "fileCount": 5, "unpackedSize": 5420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5EcHCRA9TVsSAnZWagAATBcP+QATaW7yrJRQQz3cs6En\nm0Io9k9vGuptXBcCTp0FHZlx40MvfQLWxZY99tpm7RsOcIjiwj08B7M41ytM\n6JjgHsoHKzouGuTOEqwUo2FsOu2OO4af4eb2C6BFbEz3ipbGYUNc1eXZanFo\nn4HoFKZOqqMI83NoC36lHEA2w1IdIQvcMtk4Drqf/wg+/jAFFswtEQMs4Roi\nGYd4WpEUcZKuK7TzH70Q3U9kAsHcAH9Ro/aHpph1EpDGeAZKZ5s9KfvYh2ot\nJXWmmOOeg/vhtbhQ6tGoFfsxIQ8ZHc3p7rlZ4CfPIw68caQ6HweOs42TIdk6\nySO7te3zo9LtUgBMxOLTmAFKX/hGnhRYWx5rfX2IIQD8QC5795FfY2+8qfxo\n3pWXTyHm08J4kEP45vF65xf0Pk57BTKPO2b4cUayztWmELaVl+efmaYWf2jg\nHpybN7BMPWIqXpLLDXwRf7AcppiGvNN0kKozVJVv81H0mxbb4X4fW/GIBhrM\nytKcoMGLQiRbeCMak1ZE2+ckP042QOrKZrRm3LuILCeaW1zdiLKkZ/F2zZ0+\nrfXrQCltWobOYgEDzgF1e1aXgJfAYdPsgKPs/l+aZjv4BaZl2F9ltz0Kl4BS\nZ9ZYpucHldI23nNi9w+EETPxfFHvbLWKqru823aowwD7N2R2kWyAA89VLueU\nQ6+W\r\n=3lMs\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-bCK/2Z4zLidyB4ReuIsvALH6w31YfAQDmXMqMx6FyfHqvBxtjC0eRumeSu4Bs3XtXwpyIywtSTrVT99BxY1f9w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFVFig/tJujGZfFzBYM5kUofgMlQ35INoLJ5erkp18qBAiEApHZxy6f1b0+fn/P0sCEl7r/51X2ZCsUJ2AAMkkQXBh4="}]}}, "3.0.0-beta.0": {"name": "fast-deep-equal", "version": "3.0.0-beta.0", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "dot": "^1.1.2", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "dist": {"integrity": "sha512-VB9eg7F17nVCQ9WcHIcJMnyrX/xxGlxB3VvfTp2FE0vtRFT92Te9B2tXwTx+MzR+ofBXvtDF4qLzptiri0TrsQ==", "shasum": "6168fba8e375247040d34c551414b50ac5aa4612", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.0.0-beta.0.tgz", "fileCount": 5, "unpackedSize": 6006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIfYuCRA9TVsSAnZWagAApjUP/RXYYl+Hlt124qpfyw65\nDVMJ6v0ly8ExIcLTepJcH75KjJ7icP51vJi5LzRjLP+jn18/jIGETZ1MtyLy\neHSyv/+fwcBpnLNJ9IFYcKqNc/gId7SfmBUrootbmhx5rtT9tbuV3NVnREtL\n+/B6bSYqsnkWb5J5L2Fh/00fQMSnqTUt/PAuyvc0+86x+ynnBM1Riqri0BCt\nPP7eWxPPQnnV2bE1fXMrL8/vNtaUxn8WrjpQCSdP3PcOR3lnQdyRNhPllTNN\ntsuB1m15B3kB/h0AoV1wbWZXq4gV/FSfzci19jMXOfj96Kaj4/yDJK4A7mbw\nhQXZil3JsJecnAF7B4/tv8p92cTdL2JbuXqVD6YiYK3ztRFlm7iCExilV2Ne\nI+4UjS7Nr+37OmED8ih8EBmfGxpYEQKygar6NHpde9/x9Di6lMhkg/4Mj+uq\n59SCyIbD0lrln7i4EHyFr42+lUpQ92MoR6X2ZOstkY+lGyJgywysqi/i7Wal\nFD2K4upTCGDgJxD5+Jz0EL62XgDv3VZPdbbsibQxYQn8Kw8IeYQsoah9rl1Z\nV14n0nV3SN5l/kMX3BtzeWnfxiHmf7U6JTqCbpETm5TxRnCY1u2j4zs81mJz\nfb6H7q2WnUY+ckVa0k1IWcuoPwX8jicqIShwy3UoIcXdFVAaxKUDjZVTJWno\nFzZ9\r\n=nmOL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFampEBYKCjL48SM/DBa4NdClRSs9xMJ4HmRBqBa6ydcAiASMuY15CVIw9wgx/W0nH/dE1m9oqB107vQ/t+Xmd2fGg=="}]}}, "3.0.0-beta.1": {"name": "fast-deep-equal", "version": "3.0.0-beta.1", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^2.13.1", "deep-eql": "latest", "deep-equal": "latest", "dot": "^1.1.2", "eslint": "^4.0.0", "lodash": "latest", "mocha": "^3.4.2", "nano-equal": "latest", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "ramda": "latest", "shallow-equal-fuzzy": "latest", "typescript": "^2.6.1", "underscore": "latest"}, "dist": {"integrity": "sha512-jCbIq8DtBhGWV8P9bKUtFcZShlQ8miECXN30SLkmjPXI0REN56XVDFd+8TH5VFjocqjEuK9OQVN8MedJL0lQdA==", "shasum": "6d3ffe12cd9f26a6e64501248fc6e0a60d99e0ab", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.0.0-beta.1.tgz", "fileCount": 6, "unpackedSize": 8738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIiGSCRA9TVsSAnZWagAAuKcP/0q4YwruHc8nLhjkUYTZ\nMRwcmEqiiNYy7v8Wz9ezYztDyHl+M0vl/OnuzwEQkwKo/n8Dym3T0oyUlWzR\ndoroUJotPOLJnuo90GWSKbWHv6vO4bxXSQEt8LnSn3UFEnfX0UYHZX4FY8sl\nEI9SSYPUfxm9llu6GZhQUToVtPXMmyT2dOp+syhSttIaHbRrZO2/Prhb3qN1\n7S7xiT4SEulRSMwyZC8h1VtZHZW6nd1BYsei1jtHJJsFU3jLvI+M4xUdz/8X\ndUHwsGkfhNF7IAKS2/OZ34EA/mhqK4HCSa7LqfC8SsKYLqElp0THDzpFhFxR\nEdDF/NFEZAKa4x9gE6Qze5GQTSGsggzYxopiGTlyRof6YnGr8cYFejJkI3zm\nSUvwohcJKunXB5x2+byShlaPIsmwTMjedDJFrJOxE81a0is3oJPYOjlYmcT4\nDYONQgomgSVb9vB4jWp7kUXCEmIlYah9As38YKEa1HsifixmhTpWA4H7q0Td\nlBc/aKUGwbwVIZ8kd32NCZF1y/LpZDJN8+kow9uLIDMwTTyg5pyS0ggTtRkW\nRPspUYdMDOkOX0oZBD4UNg2wMub9zXn+5pw+tp5Iyd/5L5w8pNLFQgXdUrXP\nRVzXSmqeQ7I+7WTbh6Hrb9q/33H5ctWGP7ClQrk81hjURPzI5ikb/RLe4hmY\n+b2X\r\n=lFrK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGwiLmuWrZhKwKunZByzmA1IuBM+/9VUTyPdmSh6itUSAiBsmlVKmc8gCI3w9/QG4xZbJURKGkUmCK4n+0ZZxEigVQ=="}]}}, "3.0.0-beta.2": {"name": "fast-deep-equal", "version": "3.0.0-beta.2", "devDependencies": {"coveralls": "^2.13.1", "dot": "^1.1.2", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "typescript": "^2.6.1"}, "dist": {"integrity": "sha512-/1ttQLbtYMjR0n+pR0dIuYELUJ4QMhT7sqatQBY+ux4snfkGS9gG/bDQq7i94IeNn3XUO2sVw5/EuxKzFEHWGw==", "shasum": "a479f3ff9c18e08c5a13b9ac42f53c021bdda9fd", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.0.0-beta.2.tgz", "fileCount": 6, "unpackedSize": 8438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcMg0CRA9TVsSAnZWagAAiu8P/1qIcEN2nb8v8Mcww4zM\n/nbJtnJKf0EcRj4sKnRh3griax6DThslcxLG+61+IXleilQysjq6JMF+x8K2\nXBBpkZwxYID7QbvAKbetFBQ8LtrhfL78D6p1cObL1OJX2A+s0U5UmYnqi5UL\nN6UGKXzq/9nYsqSChsHTdf9Jhtun1LIw/6P3FqW9p7N+FBZWNaR4lyAnLXL4\nJ1AqM4e4ou9taSL9/w8fqpDBYVqbrMwys4ltu502vBhYXI1kN7VRC6RzmMhR\nzefqfI4KpsFlyLUmCisPrMlo/PsdDuIWg/igX+H2pg2Wd0i1TZUVyKoRjeVz\nDWu5+zeIYqffZ2ivIq4kTE/gQcyjymzTXVMY/iXhgqrQR6yvU97for+aQbUb\n7E5l37lViH3Aac7M/118UtlYFKHIqEhjGxNDAWaGEg3jrZMtrr/+Ize9rIu8\nzgwR+SEzrk2o071VrNeo7wFecPaG4GdbP01SjaTUHiN5HHaymQSQQebtQEFZ\n2BQUco+HQuMsKv97ws0zhBACEZc3ICOj4IuvA+j4zY6kyWa1sQ7GC+cQ1A/p\np3m+OrkXar3Gv9+ETgkzMRYT12GZsn/swVJXOB25fIx4Sytk9pNorhprOfLj\nhGai7FjsMeykVnz2aFmyUGv6u+PfBvwCW6BxJ6ni6zsIhXLJAFjGl/spHI9r\n+EA0\r\n=gcXK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCoE0bM6pKJESdpJUGvth7gLslpdlhmNhGT9BtusSD+PgIgJcJnuAz0G4XyRzcoGOBqoCjQ/EJxl9Cpxgs9RbjBYO8="}]}}, "3.0.0": {"name": "fast-deep-equal", "version": "3.0.0", "devDependencies": {"coveralls": "^2.13.1", "dot": "^1.1.2", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "typescript": "^2.6.1"}, "dist": {"integrity": "sha512-fXvSXoWb8v2XMz0oKK+8KgOFO3w3kmv5zvXS4KKtMWy7LMWAgE8U4SVTPDlBwYIT7ZznmnkqYEo3kpfTY5HJ9Q==", "shasum": "8949979fa5eaf98ecfeafa9d72a8a943cfd2fbda", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.0.0.tgz", "fileCount": 6, "unpackedSize": 9050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4jzECRA9TVsSAnZWagAAxxUP/iL8vQBaLNYa74yO1kei\nOp7fcThRfGa3Pc7l8HFi17BrOOo/1LIz6K1RSi75qJMevh/vvrCFlStWTdTp\nUK2jyVnM6tF0PUvMLydpG4gLoFJUiYCykHI7BmNAfFfq/CGyq0qB8IRxTfWs\nJxmaIERjbVbgKa8iW3sUWZug0J9MS73j1ULxpbWVvm4pFamRQUkrDkyFMNfP\nGM7mnB6HmJIF5OeJiB3/8XrYXLAlYACqAHwPDJmmMDe1Ssc4s5evbfur8tg9\nXnsacF79p23ECxfGoXmle7A3mdM4X4jg9z2fjhe0hYv+fUrQhEPivJgLBmj3\nmTQZ0TFXxCNUE7qPH2kwNqMKfiPc2CkRyWxC6gWVFCqRVHyp1rDcNgh/VnnY\nUmYd+InyuQ8R6U16FsnRKH2PS3lA6VDpUoZLb4fcv9g5LQoqMhqRr6/9Ftl6\nUWiJPq4y8xKAEa1KV6e9ZgzVQ57s/IbGLiVIb3zZjxYZ4f7+dUwFi3rT9p9d\n4Oj6nBt9INqupcj6hmj5toYA1NU/jGhanR2im9xZmVa7A4P8UJwSmdN++pEE\nPO8SHzQ8i3FZE62pcghjqNUEUb4ypF7+MN1RpUhc1cecXN9o5VR+t8jjH7x9\nQjSAMkVfwt7kMvjofc1evrci/wiAg4dWtvsQzAREc+XXwdN2ytrakiAc0686\n0yMZ\r\n=0rZu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD9RjABhf6spMek1URwr+JnwHTC9SLqF0eijQ1FJyCWEwIhAIupkjw8IdJJa7+R8U05g4JCrZXbqIhrd7wIDTS/bTJD"}]}}, "3.0.1": {"name": "fast-deep-equal", "version": "3.0.1", "devDependencies": {"coveralls": "^2.13.1", "dot": "^1.1.2", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "typescript": "^2.6.1"}, "dist": {"integrity": "sha512-eV4KJB1HpEcckLU+u/i+FFZnvbeu34rQ514kL8m4uw7FyGWa7ltvz45vfDVFPZD+Z+h8WQL86+co5P3OWZrScw==", "shasum": "05a7fb9c3c34e594562b73c2b6e7ddaa92e5e79b", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.0.1.tgz", "fileCount": 6, "unpackedSize": 8620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4j/tCRA9TVsSAnZWagAAzwIP/1oN4GkNeoAhHDwHqyHx\nI9DGHN6TmsZfTvtli+EpOEcB98SPo0VSCddysF6fx+7kQk0nbwspIUBNbeKu\nTqSv0XGvaYIeN3OwenuUxbHaqoAgyQD0mth9wGyf2T9Bl5QEQ9jtQCAnQeAy\nPO9oaDUjR+pMuMynshw4julKa0UcfFS5en88+JIep6S02NxmOmAEcvNTXaIa\npd+D2EJYYu856Xe6oNp5B1dG+/VcNb7X4CFhDYU/3bvcYb65ebVHql3IvSUr\n+VBOlTeLYLb/ZvpLEDc8YLPIdGDeEtsmdCMZNJqsgvHViEllk8cPCYnNHuvK\nCOacW5xoJhh8OKO1/G/BMF/4znATMYOU3DiYEjjSKpTeFCdVeZ1nClngYWh7\nGkC/auWX0r4LfFqFw/63Y/cLlIEp7t2l9vGVxItHu0FX9Mu68G/zpyy+dYZp\noHfHmcXO1LoMDHDdp1Nj2BH62uoywuBnO0ugBTC5HtsWaH0wYNX/u54zEdd4\nRMvCvZaGVdPndq6MHaV3D3el0PrXIDSi8+RShurVXzehdu0DmCCRM4xaUmsv\nTNNsRVi5FccVIB5HciRSQC00sNWEMK92eRfNPo1apnotsTFsdsrCqKZjAU67\nQ6JqSXlC4t7iJuIQcqklymaKS1/X+kZ5dGINUDutelPkiWZ4FtbfBw+2vhp3\ni5VU\r\n=wEHn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCrShX+rRqPeNEf5SrFQIVDB+krjRigL1rXNjdc9YmdUgIhALdxzgX8qDP6DQ5ExjLc6CNMn7uJ2Ybem1UcCXflYHVN"}]}}, "3.1.0": {"name": "fast-deep-equal", "version": "3.1.0", "devDependencies": {"coveralls": "^2.13.1", "dot": "^1.1.2", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^7.5.0", "typescript": "^2.6.1"}, "dist": {"integrity": "sha512-Tv4ewzk1IJYQHXqSDUFwpTRMcD6JdeEEf+zS+9hdBNIJWM7pPDYwDvKmS/9Ul0h8LOhY3UisVr9MolJCBPiaNA==", "shasum": "62b2f73b6b16ef2d53cb2a17c3e563422e80d32d", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.0.tgz", "fileCount": 9, "unpackedSize": 11400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5QoLCRA9TVsSAnZWagAAXQUQAJyyfWRbnMmyALKu1ko5\nSotuZ6OihKvz+NnJPh/XR+Y4j2lRXei0YaNdVVXOkhCrOfwE2krb2269SrMz\nQv8dR4ar45G40UZDT3Qtyuj8IAnujk27k9uWNdJ7CPGCsZIQ73P13IuXkets\nOp/v2olZLcrmxTbBYPRkH2sF7AUcuJEUkfqvSNKlMUbSu5hoknOimw6vZwZe\n+SPVuEeK+nGry25Z/L7XdYXSfXHxuW2tsvO9N1wJ0aKTDKm2ezEY7ioiFph9\nSVk8/naNNioqGuPjHpX15KjqFdDrl6Id0TVsq68KSVzf8cTN+qCd3WpgaVSZ\nHe53PYJuLYgVZ3Ce961VAfUM/YYB+WReJTxew3oU2ttjweHZej07+Mbp4A2M\nQCIEdwCWN4iQ0F2IXTnhQWgNwFHck1v2k7STbS19B33Q7AWQcLzxwQIjWIvu\nFH4lHqDaFd/jDHpHN8jc0tGdQct6y5Hu9LJT1p9Ef/Ar+Nm+9pz+e4iZVwFo\nmWV4tXTKfg7iq8nBTHXewlPj1os7x/34ttD7ExQJ2i3tHWC/vX3mdfqQXIuQ\nh9nBwunNjGgBUgNk5CZkSCK7YLjlPMVohjFPSzwJDs60ERYH0iEJaJXJI6QC\n/ydAj/+sVRFxwsJnFjbXc0uTaarfAkWuqgSLWiFGEPNdzQvSPq8FtfRr9DNl\nRR/7\r\n=DX3i\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHg4Yl3Qb3U+TuzA+ysicfSdAM3T5Yt4Qx4GOANcY8hlAiEAg/uEZQA50EDvWjfsHrzbvA6IMiLMJ5tB3h5257MONGg="}]}}, "3.1.1": {"name": "fast-deep-equal", "version": "3.1.1", "devDependencies": {"coveralls": "^2.13.1", "dot": "^1.1.2", "eslint": "^4.0.0", "mocha": "^3.4.2", "nyc": "^11.0.2", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^7.5.0", "typescript": "^2.6.1"}, "dist": {"integrity": "sha512-8UEa58QDLauDNfpbrX55Q9jrGHThw2ZMdOky5Gl1CDtVeJDPVrG4Jxx1N8jw2gkWaff5UUuX1KJd+9zGe2B+ZA==", "shasum": "545145077c501491e33b15ec408c294376e94ae4", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.1.tgz", "fileCount": 11, "unpackedSize": 12943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd5nF5CRA9TVsSAnZWagAAKoEP+gJ9E6MD1MP8F6qitV1M\nAw+0FsW5XIkFHLQR4zsf+PNDuEM4rjlz8vAslIMbD43XGzpE4tvfuMpISHUS\nkKvEStpWLpO3Iy5JD4FcWCLZdlPjHMI69/p7JNOPqovMGNLXPA3b/dzWpPgj\nq8muatLMmNq4uc8XIuTA9hQlXiuJXzuZc5fIfRqL0QMbfGs5u9yfXDyPpCPt\nFK+LVpZiVu6a/ILMyUNK61ueqce2FYqRs9GFwD8NvzpDxAfzRAGAMEKj3u+6\no9ToSgjbfzVpE3XX4/mA6sqX3JHWBwaKBgWz2qkMKwrdIKrqk+ZUTCrWcdOF\n2KiVvQAksmm20IxeHbsgNAW2wbFiGAC792UyQFtgDUCzoQcaB5AxZZ3Q6sn+\nOQmMzTghZPZwB2ZS45QPtWqDvjsgfF5rXgMbBXjuAVnp1b+BvMBNcczyxkZd\n2uCwWYp+Y4DObUdLxvUUszjosyxWZmAGD2RoEpi2tOrB8sgsGGR7zVxHudBy\nRPFgijEXDBPhBFg6avnBlltrH7kF28iapYffpYlURGwftILoCJjXBLtHC/6C\nrTIECFndV3USo+ct4vvKLqYWUkxfKu45M42XL8gsFf/COOByCEg/OuCGwhA7\n8Eg7vdxHfxlTIG/FBpnd5jND4cLKIiYoDrrqlm6qUaRv9uhhxD6aBRKLbFx7\nPNzd\r\n=Fsqp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC374S8GXkh8y3bETOyOc+EHDAdaG5Kem7aLBCTeLMt0QIgL1bsheU26kW0j1wO6EBSxzNMfXacOBALwvuoR6lhnjU="}]}}, "3.1.3": {"name": "fast-deep-equal", "version": "3.1.3", "devDependencies": {"coveralls": "^3.1.0", "dot": "^1.1.2", "eslint": "^7.2.0", "mocha": "^7.2.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^9.0.2", "typescript": "^3.9.5"}, "dist": {"integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "shasum": "3a7d56b559d6cbc3eb512325244e619a65c6c525", "tarball": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fileCount": 11, "unpackedSize": 12966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3ehgCRA9TVsSAnZWagAAXoEP/AmAr6hbgGqa2L7/95pm\nB+wX1GaxB/Ynk4kSU4xeP3TGu9L72sqNhCf8Vj/WLl1XYPyJ0xA9betwXSIa\n+eE+Bd07xJAmaCM4Se583zgv4lifprYoKSCaSgGcXZ0Ay4yDxAeHLmrvELah\nNBnVM0eAJ/YxeVpwEemrh28mT5XDdq1HrXcbRq5ZnjTfth1oPvmEvfEoEtCc\nx/6NrH0tfyhPlbCt7XqpmJPJIYi4KPaDuaLbAhE+yKVzXtCfKsjf/eJVwfi/\n79sIhtQn8lINhbwbk0gs5PmFsxVIFllV6RPROa+iLLSV+MupLCoTWrmzzpx3\nJimUwaSKt4iPxsM3a/jvn73mODmqL2w4NNmyOJYmNj4Jx/ps+QjDnWJ8Vtqz\n0HEC/RnvP0M7HpObaOMeb12+xJY1PGnJqSDygMxVg9xT6WMEIoE4D55V8HSL\nyEQB9gNWpKjX2NAjolidqAYBc3XFplQrVPsXm5eg+Uvd+sbwolho8mMv3J4B\n0BVwcCcrxLgFOBxt+ZALNkcAb8MxkgU7Ptbcg+BZtqJV7FxSIXgRZW94kLXn\nMg+OorbUoGHPyGpRc8L3RGkfdXztpGdyjXHC5VDbkZHlw+1lyAQzQq7g090S\nkbQNT9BvcHpKIPaNvZsY1VjenDW5hupOCdyTfjP0HidwJkMN7OPPTWRNBfaW\nWqbw\r\n=uuzF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDaDosp9IxEQRt2XkZPq/dpNlm0nhS0lCPdWBSWBZHcDAiEApfm2QlsgGUYm5sFGQYPVb97kIorp3Voeb7/UQRiSmKc="}]}}}, "modified": "2023-06-22T16:31:52.032Z", "cachedAt": 1747660589006}