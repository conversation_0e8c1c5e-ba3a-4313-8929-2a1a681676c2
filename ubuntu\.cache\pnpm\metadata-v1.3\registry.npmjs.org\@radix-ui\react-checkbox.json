{"name": "@radix-ui/react-checkbox", "dist-tags": {"next": "1.3.2-rc.1746560904918", "latest": "1.3.1"}, "versions": {"0.0.1": {"name": "@radix-ui/react-checkbox", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-label": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-presence": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "57e5363ae32690cb69263623b185884a61b66eb2", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-RUyBqE5bOf0kTLxim6sapY0s4ZEgH3TArBgKFZNEmWE93S5HPGIdJyzCzRkMGN06r4n61h0dZVbXJ/lrO03XfA==", "signatures": [{"sig": "MEQCIG/EAITPEdXf5pQ+HtGNdhu9bN9Vi6X+mDzsnV0t8jdFAiAjLzSJarOd3qyr5QkNNyhvCzNtwXQ1tssAE/ui8Q0lXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbHCRA9TVsSAnZWagAAprYQAJUJP8FuZqYPD5roucCz\nqMBrNnKITMFVr5O3fgapPyzdwAPsE3HfoVtytmABHvH69Y9sgwjbPj1vl65m\ndrShGDiy6nzFIaw/fmJoXMsPlN0c/RtuMqFjfFXXXzUDojF75ioCXM5z0gv4\nQSlFwmmHyHLxvt3Ec/ULRyxM8EquzoVe+t569xs9XbA01jA3dNweaQcpgFqK\nwPWihKz9V9MPC6shbkZLvmChcip+Mby1Qow6tO7IN1yOF0FYJYHSmDL3raa3\noovVBFNeDpyY7DkgMAMjep/GWJhdc9UL5uMG1SOtW9EljlWtfPZ5FY3JZBvr\nWjvwYMRp0XUtUnuG1rl84nUhWfMPPTwhc/jQrUT8biCm2oEH7W7E9qMl7Ciu\nlQuqs7IlR0tT+uMAuC8F7AR7qfzHEjYYaJjS1WzfpnOWw0IIt40kdlzKEchr\nYUXK9v5cnPwG8hklFUSFzubxtTYClDvJ27kK3fkNPKPQQs0jV3P/BeTSfhCZ\nzVyWr+azuG22BPnAPnZecMbN2uC9Flp/6vfUvrDG7zvfyCTjKqc5H8KBh9bR\nGuvB/YXeoeJYRUaIiDt4Aar6/b3tdKmwVMOYHXR/qNzMD1O8XyfhYiQt2d4W\nvX7xu/ygGboEu2I+1dcJ08xhQ7veUbymhP2AoU4hj5/SwifjSuBJ7EH2DaAJ\nUTXB\r\n=xulS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-checkbox", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-label": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-presence": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "edb15e76bc0275659c730b137a9e5b6d145b5b06", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-NP1QK3DqmvYDwvWnEPdE8SAdA00BAlFVfuxZJIdLHhxX20AKIXtd8NQnYCdVbuCFkP2H6Ai06hI9qoTapu0JSQ==", "signatures": [{"sig": "MEQCIDLTCbhSBrKoCVW7eAt2WEKT6GIcWuDgRvERyhYBTMLRAiBjLItlUDKo/f3Ui2TImjFS9Rq5fstmw1Ar1n2FWsN5sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvBCRA9TVsSAnZWagAAcTsP/ipeHjikvYLaqDptG3et\nhCCLqSrn3bqVdlkXTCTj5VxcQDUIZMM9ql50yoypbdzBBQlAo4jin/qdkfWF\nThz8F57UHLeLjEMV7FD19GQcjBtKshHNDDWuHzsMgjlNSoKGAE4RB/mKasxq\niyHXSesBANi4S/84F/02sUvK76NRFVil//p97/tqF849g6mWLdhWcg3zr+gv\nRKF/zjHzorpa2qSAXnB1QGsOqt/b2NC9fLyM66nyya1tCMlAdMgkOQsAOfq4\n0iBN8Xt694oEGeFPumHmqswUia2vVZuYD8TC9yaxWHkLHW9nGYLB6jaSCrOa\nelZbJfppWQRvEXxvCPgA+sm6zPIA6AlZO6Qh1vso3HjXYS1YEKQnjuo4oJIx\naEE6El5xqn4zilHP0+ruJvn91qKcpDGgrKTdrQBi2q3bKJy7g5X5rtOFZCUu\npoPkSamSmCcRKipGwOtVf0S1hdDqiHXKLMyah0oZGGs24G34kxkfcWB7t0hV\nkqiXzdZVSOmius79ersvDMEJN9gk7IeL7dj2+G6U16z9bOFQJFJlUnkd1dzL\nRSx2MQEXbjYGY+bNuSvLBfEMD94LGjnBGu5pqYpt//O01iflJkYrQmv0lZM6\nQKjvhR8BEEvUgtg4rpFugi3VtdsoOWS/RrKu3mTTm8S0UtTvEAWTWytJwJWv\nFgsg\r\n=lW8h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-checkbox", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-presence": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9e877c6030dda7dda2772a4bab1d9b50f8085c4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-pVTGtlYsX9N20nd/L+aRQpgPh+xeMRHtWM+aCrRjp0zfzOnZD9Rr04quyhQuGiWPxV63W2CXzihlPYbRGvlA5g==", "signatures": [{"sig": "MEUCIECRdqk+tAYrTBWVDjOG7v7eBEPnNh/uMtkUTHkRXdYaAiEA0i3o9tr/SJxXugXO5aCtyCkV4t5mV1Cg4wxyRozbFEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETs9CRA9TVsSAnZWagAAERUP/RiPBZ3RihaSpsSbjgbO\n2GzUO0C9RA38hsE8Dqh61nb2BUUkP43OxcK7jWz8E67C8MEXnJePbqGqI1J1\ntlZBws40QTswkBI3NB+my6zPvw7skH3Hl//+NX/W2EoUUB3RWBNUY50STDyK\nnkiGBSA41mu2W0ksGsMTahTnNr7VQEB12PEy8LUqIA3ufpbur+sZBQ5ElU6S\nNGRkt0l/epo9ttUAAg9kQN61lvqziaGq7EeoRqIowdIxDqgcRRtf0d+h0fsv\nKXqmGpsqVqMEL+DZV/HCpgX27dz44SHs7vmlvKW20P5xHuha61NQotvGCpQp\n7g5eMNmGA2Wgp5Xvbl0J0fWCfZ1HY6hW01siBLOjIbDDVqiCNLzS6IpTQIDU\nKbbrm5yMDtCu8r6gJaQdqNXCCcffmlXCc8Ltw1lBZyXzCLC3ioBPH/120Hpl\nLqQA1iwyWsvJb3imWhStHN7eOQ08JxFYYc8x0L+BleHZozmDn8UlM3eUyUEY\nPvWDBNCNpkgCZio5Em4NFaiRnWpTTuVnfOqSyG/Xi3EzWaZ6PmAcwC9eiUbc\nPm2BsRvxBBIB2ADCZ+n53Hm4wZJVLtvTY6UOMfE4GFAJwgfgM6Y6J4qfc38R\n8PYkM0i9Z+cAdAJP0jAkjVw5vi0cCQTqGKwtavOwvfoGgGsRckdoYCOXc37f\n/pBu\r\n=N1ay\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-checkbox", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.4", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-presence": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "34a7796c8c5f0c27ec236f280ea1afcb8f526105", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-oAhp/b6+sn1YW9MnGuUsj86i9BowkCQkz4y4kOvs0hC++db6VcoEexvIpAcAcTbu8ts3aadkCLZ6W3CzF9ACOA==", "signatures": [{"sig": "MEYCIQCcH5ZYpFGxoJ+AMTcrNmwkKUc/DP+aApy53/G5xnixkgIhAJOZIRA6Y/JZevwrYV9/XAD33uD3NL/Sp5whZST/CsNK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/vCRA9TVsSAnZWagAA90wP/0kHE7s5y3SyUD0Ihfyo\ngL/x6sCpjQf0ljGSSldSEmpOFWda/NZgYdLN2nqjm1PIDAjDeBabj3phOajG\ngJ9oq1Zkuna+BrcGO5xo0k1jtmCKmtG6fYmH5D4rweb6mIidFT1phouw067c\noaN0Yzzxz45gLBwMZEgblAaOgB2nxTRwgnW2muiu1pApxrMocfg6h3jx0eDT\nKngFbkdwCDclcocFxWzdRLq3648rQ8CoojjPdUYd+vpK3JLM92oY6XvZoGSo\nl+DDwzFeWEN2OXE5OqTkoZ2jaIZvwW1FNmUFTywE0jNyLXKeLLwnJr02DXuC\nPuT2PXN4xcpm0/uPfRdHFnvINe+A9UYccsqwgvPVrcj3HUbMxDM4vtLriJc0\nIdRz+vo4CtdlyjiPJhHEW6T0adLO8w/iLkS9qeFhJz8BZWe1t3iQ1biYjDqn\nWb9hGPw977YJf1yZLkHphfvldvCGRnwBRQEYieI3WB029sDR1/+0tcqVygC2\n3l5yvhr61xXkR20p6p0aGxP1EcQb+dV12CaEtgiQdUBOJMvASWEzoSf3mGrx\n6nmtmuwKAU/bsj9N3pqZ+dwrqkTQAD9fxxMr8vpITalXNwbPnngEqdGEWjnP\niM4ghZz6gDMj9yuBKu1aZ/duzcKWep3gk9JRKhXH/O+phK3VZxFocH5zjj99\naCph\r\n=nzqb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-checkbox", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.5", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-presence": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9ca68247eb2de6dfd90c01845d85302448dcafa7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-HaAlGX9oi2MV7pzbkOplpE/YyVUyxQeL3Snq5rb0HVLLJs5nM+QQl2LehJvCv7/r73KB96mwccCCZGsBn2wqPg==", "signatures": [{"sig": "MEUCIG6L1IWNoZFVcY2GvqU2kekO/6OiHa5OHl/sTS94+HTnAiEAx6i8uMH6Q/4ePW1jUhsD3kVz5mmJbAmRbOH6ab8oun4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VoCRA9TVsSAnZWagAABboQAI0adW9b40mWBVGvFCLT\nrMzA57t0cF3z8AiFI0aoj59Ldty374R26PZBmwNdNrhLC/OYNqhCMhu0iLRw\nbYURMRyR/pEpEOnTWzDKzBfFpXbsq0WgiDDJAym8iJmA5XOphCYKSSU+M399\n3kPZycP7E+1kQk9S4cJlqfzkGDhfgbPXejNGUTwgyO2e69JTcRs+eRWvnjoK\nfC8Xrx8V7AWT4MPtNUNwrRsVDU9Drxgvum4bGyt/HAiyvjbvV+qVT2TfYcJV\n6G7kBYEWAtAIfsOgCX/oJ1vj1l+XSWJQcb3OW3POtfVDWKwzFD3RcsgvApgr\nb1aKwCivEw1PU3Euo1LIRsTRtGuIMZf0TNPrO0DbtT7QQjB67Hk4I1SE6W2A\nFwiN5NyJRBWfbmFxLdxId+G3cF+TBJKRAzogmGHw8NtP1SWdWybrge/fdX3K\nShBEHxQf4TUYfXo4WrZsg8LZoeEerbti+SQhHvehcapCIRBv1/q6OnJQytgt\nRDrhmHeq+TqGsaA1bYCTvqddFOZINbovMyf4TlOr3tZ9Hz7dPGQpgw4c0Off\nrehFhrsV5Kvr+DrHgdbaPt8kuIvlasOT4LpzZAs6nq1HoSZT9hfE1eo5pPNU\nq1oUuMkBH2j/lW+1Mck/qaMmYYje3KETRECwOv1Zn2/jvdeCqgML9spGoPdE\nJZfc\r\n=cpy9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-checkbox", "version": "0.0.6", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-label": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.6", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c9de476d7649055023d785982a4513ce6588d82a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-ldulrNBXnlBu+UCKmNg38p4MUAY2qgCPQ4vXRXNTMJYLRzTxbMIsknmTCJH6tvqssPgRsegTbghfcg5OL6dlRg==", "signatures": [{"sig": "MEYCIQCZqO9Im2gGePydZ87INN12UT0d9BDV6VrYlYVQzi1t7wIhAKg66oQudktjtT8UdmCvsXCcmnZKMeg1X+D1MH0JYMk0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VCCRA9TVsSAnZWagAAlqQP/2ig+1hWupwzr2u/KpcT\nlBz/XiGwkzeOH1jarNGW8UBjFNIquo8xfWH3Sy33C4/uNXBjsSu713EmrdQG\nXODrDAq9OrkcTp9YyaTqy48B8e/wv98XYP6zehBa1BR3ihbqFXgp3m5cUXXy\nODptwLC/qVpDTM8spysJ9hLD9MUnlFBs9+NHizmMBSJubrceGmcwXAm0ljsp\n/C76InjV7WuAKKJJYpTQDs2dGkyqVS05hj9eVMx+lVHEt5QaiVsY3jZdKf6q\ngZYjPPyoKrdxknPpSmRsKeKM1sAMBzpQX+xJntJda0w/pX1dnjCf8nnxvSqO\nsdNSzaY1lFjp+4x3+1t9GLMlXHzLMOohVX6O+iDXe5U0JwPHwAGltxPUvTHW\nUhujz58r0pAeAZ0ZfNlRih/sM+DzVgXjuAtyyzo24g4asxs0Y6Ft5WGUqJ23\niX6GL2BwbFisrO6Shlws4gOk4ft+EdktaU/7o6Y3kGAqVVlvtpP8g9O+drDd\nCU9KBrJjattDStrts/m11D4n6bOSaXmfgIg0lWpVKctnH5UUV24OXOsQLJfw\nglBK+ZlAqElHpI8ZyIqndn6e/eqBms0tZtV2HHTaCN2zfp9AXRJEiAjHAOCt\nlOidhiZ2gQPmF2jzLSrN8PM7kPdICJ09MdZRwm5BMzKp29ri0aF+540JtKO9\n3Xda\r\n=st3K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-checkbox", "version": "0.0.7", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-label": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.7", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b4bcf08ced5b6e0bb1e08b792bca0499d871c0bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-6pfD4/petSrvtWc1O9QlUacgxwHizPq+3PhS9xMZvPg46l+rZS+R7b3gINjRACCU241jscecQElMh6E4sRjZvw==", "signatures": [{"sig": "MEUCIF1In2yzFA+lKGoV26hIhzQZfK76grtIrD5flhf7FocBAiEAn2CBZKfH/TmEAmpitXuQ+xnJPyZmAmLEeEUugUzwSmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmVwCRA9TVsSAnZWagAAdlEP/30iGCiRUmVSCEpK9drQ\nRDB1Ch8br06Z/41/6TR6GBSjTAqtxk2Xl4Croq4oBFvuDxb9HiEiIILoFavk\nG1yl+0hiVo7Gwh5RyQxF2v0gFLbLPYHlmcUMtCrN53yptATBr4xrv7Ds+kv9\nduzisot1+IlbmkMMIhnYNHlSUKbH04vvxopiKmpoiwYqvAZowpbxLIWhy7Id\nDmXGFmahvfgyaY6glfBRsr8m4EIEk3oF0g+N9hzACJrm2/JBaYLfNXed8nOi\nyNfH33DHWpAm93+c657DPeIt0vq+mND<PERSON>y64fEGhaCyCQg5lV3k3OB0+WwDQ\nxAR1+lLOjaYbtXAUgwqoglAbf49JZo+OWMq6gpR/HfvGsJG96t4fQ7mD+aQa\n80oUa1hjDK75SnO6pljjOK75qHdg8kVwNrwMGDTMqg0icFVK8zBCBU0yYdXl\nhEGnE24Nw+EkgOIRuJDF3YUpupUQX92z78pno6DSINuoqji/UexOW353A6/f\nRNOLJzQutJpWnV5B7c8fxMn1S3/98poe2IDka/pqGRziwtHU4nsobALz3reF\nQgEyGEDeDwsVD2hmOhRgyse67CIZtQggvKapvadrV7nExpSrfRRKjG5js7fX\nnaTUsuHbx0uj+ppcN7xU/HuluAExGJRkXoCJoUgsiLryhHLY/5Lw09rOGZc1\nieAA\r\n=VWeq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-checkbox", "version": "0.0.8", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-label": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.8", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "212c2afb59fe10af7e1caab29539143482749475", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-XMV+OQbyc02wouRqtnVHaEq6Bssh30nM4gqCBcl4ss5EDPO+3bTIrpw0m9ibMWkAqoV69PA4vH3ILaNzUJdK6g==", "signatures": [{"sig": "MEUCIQD5xnYyE8RKqyYr04+VJfcbGJS76L46J8fJUkSc0uePBwIgJbFmu8m5hTQ1l5jZv6q6Q8Dl41Qau1skt/RAOWoyCR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9fCRA9TVsSAnZWagAAYssP/RQU0jVe+23BxeQ2Q0sE\nequMHrcgLX0HvPit3G7OIdjK7EM090e/VKblR5ZWVyd5hnJgfKN7Ts05sNM5\nTOylX5RJLkCmexVFJdJpsrWnm+SWi7RZ8xcClGYjO2Dl/w39oWhhpcoGt9Ow\nlqIvpMuvzHovcpBA19fHuikAD6yCZgKtHiWMzGcwChuiK3v/qN+BW0wL/6bt\nksuOJWjF96v+yUtirPy4IWyIx05rhNz2qah+AZZcjJv2GDI5q2x3PpmH85+/\nGnIvTnPJ8SS08Nf+gekwCCmziGZeofqCeV6FT0Lln9xqGDjosjOHgIj84t3Q\nNkHYCNJjx7Kizuw4dBARoIC69ayaPZ3KJFSdZSUdwwxmaMNdKciOYfzou4//\nIncVbIhhMjgGUzftLHJVTYv1c94SVEzBo0+rIVIt1beXsGRCMl7czCsms57G\nKNo8OEUJ01RZx8Nabe5nvJcXEpUiG8cdPrhHS5piOzzTSa75q2YXb62HxZV7\n47aNQnNIhlX3ZasqF185f1MRTPfLYR15HrFnzwpw9nGbdnXvCmDuybZcZcod\nOvqDuOwkEKpMVFFDrJD3F+bm93utKJrTJVJjqy50BQVpeI6X/JMeszrVCmJz\nJerVBGVpAujPr755ks8TrI121f/CIc6POUpCRBl3mX8ylw7M5Qox+JjwkuNP\njBNY\r\n=fsuy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-checkbox", "version": "0.0.9", "dependencies": {"@radix-ui/primitive": "0.0.2", "@radix-ui/react-label": "0.0.7", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-presence": "0.0.9", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c9b5a3255b74c6dbab63a6a9955a74aba3f7720", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-d1ehtTwe5okUj2xAWUpHYNvOgOXLZi50RaxZytaziS1FA964cZquhYkgzmA1voNDupOR6jYdFNrU8BSfgAf8nQ==", "signatures": [{"sig": "MEQCIHdQE7wya5JoGnYbhUw0IllcBtkvGN656q0bExt1siySAiBQdqzUXiumoJe/wOAj7fU8I5mKlhsbOKbN/wizRfzj0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOqCRA9TVsSAnZWagAAIGsP/31v22O9yY20DMn51JaF\npUumnfzhIC4RLTnnUTeOEt6QxQXAQjzvWchEPsXVmwwtTcr4t5S40rB8cTfe\nb2+Tx3oP61Ct7d6yMnfEpvwrHh+M83vx87xgeYMFXlY3oWmqiMehchtNqHfD\nBITT5+sD6rZx5ribmvtinsVyVhlafV2gvMDhNUMZuMkSodJJGpEuJ1MPXIvy\nm93jjjE8oE404SYmNpFSpgLDHZYEahvRbZya0MOv41WhNTwQq/qpz58jAKCO\n6KGbtioxsfpFTQz73+x6H0gBpYKwWqrQbFnbIl1yuVvOlzRrtW03s0fEXJKo\ntrTGs0TjKRQ/hjxVD2DlLGzjbVvbVQ0FXBPWHh+eMXY0O7W//Azyl5uNeR/Z\nXoD3flCX3YBVKVRY7QG0dRPLM2pYEUw+NkP1wX+taPKU+hzTHyfbqHHMrOIj\nREs/+MAd7c5P/gJ+Uy8L1wWoiIEVmIlJhle7x7npB01RV+ztIdmSeAhA/mfA\n8zXi8iC3M3G/tHKFFYXLMtQ9ZAcWLWz7AakH3oZ5/rK8CxuuLbylfQrUkQAY\nzIBuSjm7hOdNsbr029t1O/4H2U+23fFoLsLeO9vQU+q1H1EWZQU+MybzxkqU\n29gZuKadvkSFm17X69b6oXZfoSKVaH5X2Lxmr+M1xBKJQpFtp1QhQscndgsP\nWxLg\r\n=Kmu6\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.10": {"name": "@radix-ui/react-checkbox", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-label": "0.0.8", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-presence": "0.0.10", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3bce406c849781042341c6a93d3cc8e8df8a797b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-4y7urbisiTkOJ7bdw55GjGcf0aeDEZx270xgzCGqKMbhS2hfO3j5sPT9eu3vGvap4AlYeW/ZgQ3wQwuzBGxq3A==", "signatures": [{"sig": "MEQCICuqoflR4FpSTIWCJFvyvFSVd7JofbcUuheWN4AUfsUpAiBdiQvBgpL5yqcnQjAUmmAOPpUNiVTvtep7Bg/7+eQTpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0giCRA9TVsSAnZWagAAU7UP/iZTBy5ZpvdMzMJ4jA0z\nRkhkcU5dgCX5gAy1Ks/pYD5RtP0eRfYqg+VKAUUHNLWZGJZkVEuKwLxsvp4M\nIwWO6qvdily+rmhQYIjkQ7q1VDhY5cML95WrzTDu5Y5ZvYJn4gq+AWVja0/Y\n+dCx2r9PBWYpbvGHKEnKF/T0v+fAAj8rzHEM295YhUG51xCQfFY/jSBdrbbr\nIOHesF5uxJ6s1wk4DyQ0G/MrhFylunktPdpO+jU7MtB9fV66GR2bX/Fh7zU4\nGO1ptkhPZaKE61A6PtWlvkiAVWM4RR1oRNhx97rAA/nXwx0SWYZA6X/wiVDg\n19MS5KGx+m9agz4QdDX1MaLcdtZjczhldMeK+GymB7VGUsO6YtonIPG2E9Yg\nO1fEdx8TpZ3DkdSPnyR2cGbyjPG/SWrljExEQbC1J6MAXPYZsvGEZhiVTJbk\nIi0R0U9zbl/SlF42pZB1rqhfinjr7ilB6oIjvqMKJbtLlD5v1jr9IgiJA18x\nPSzOoIv7WmSRJBs5nQMhkWPpI56ho6/dog3H+Rrme2vhTRVJy95HswR45Aay\nGEHs8jHkq882+CJvt2StFbHA5uirTLMXBy0U5DT84y1J25NuVRyFcP3ffYrV\nQbt62uj/xbvcrYen2icMyRlKv1Aul+FnsJYMIvI7oz2XKYoyJxo+h5she240\nqDSN\r\n=yNSN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-checkbox", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-label": "0.0.9", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-presence": "0.0.11", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a89b2fefa5d9dc9f8e1c11068130cefaeceb407f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-oYs7Ib3W64DWrVesnN9O7ytn5yQBzzSCDynqLOp4VM2MuzrZJ9DH2hlUpjNHqdmL20mhzVkcp6PUwyEcQPMU4w==", "signatures": [{"sig": "MEUCID5rU1FO5xdOP5JFhfK5FfofB+blqniVBIqLQvEL5mg4AiEA5jlUHTVi3xV3bKKwILUzDz1D8F6ygo27yObb56Fxvcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HoCRA9TVsSAnZWagAAHxkP/jypY+i1i1ilzpypyda9\nqACbC4aYAacTbHK1Y0CILBz+uBbDFH1izrmEyumu4+xYVU+B81Y1YXYlzE6N\nQKUgN2phyN2d14nvyv6I4hPmovHce/WFmsdw/cWEGit5OUyPmL3KkO3RCqry\nvbry2glXU2Hgs4glFN26I0Kbi4Mc/88E9sI2FQxiiuylqEqH6eUdwYEKeTN4\n9cqGLIweK9fy0z83dec5luqATpKnB+TG68Zv/thtkpVX1r6EVFOahBArEnV5\n2kwjlK+6KcujI8Jv8d6mDgmqwoS88KrXm3h8Zhzt4iyhkmYRsKgTPyqLv8cI\nq0E1BsombThJ/Xo3Zs/HTeuUuo3xSr11Sx9vln4IC8VWdb9ga7BU92lpK0f1\nBb93HhYCRbVuKlwPrn1JZqOpLfOZcWUH8chpb0I36NRp74pe4RZFSOd36htD\nVBnIXXcqzhN95ZRg2NXxo9+o9UGG/6WFmLMMeO5G2e83U2g4tidaS2UU/ajK\nDEYET19UPWtB1jFi9QdfgccOtsu4ra6PqoVz3P8LA0n0A2FoaxmZMl1G+2Pt\nczn3N1gSbf8/FFKdV9YenkWvpzoqSXRp1V8x/Wl2fifnGcjBIDRwcTTZWWlJ\nhM4H3qd0OpEvXu0FUZuhVHJmH1mVIUbq6M/mgsZ6xloHDBrd45HyRpyL7BbY\nhtrX\r\n=ejZX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-checkbox", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-label": "0.0.10", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-presence": "0.0.12", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6c691a8f25dbf5175f5fc923486027a8d8beba21", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-GFIgbDvgeQLNWHNHHiSeZODsMCv3LN+7OGO8VuLR9uW5FeteMp7Y1TPfp2p8/PZI0ghdu4XlrWymVqLtDX5q8g==", "signatures": [{"sig": "MEUCIFJ7mQVX9R8u4WEp7IKwQoqZcDJaeVhr1RnY9f1QSOzzAiEA1NETou5CYTZvkKsZRuVyqEwUK/U5nYC5WkdCEIYX53I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vlCRA9TVsSAnZWagAAff8P/ihbkKqrtEmO2+96hnud\nI6Y0N2DjYugftoAd5OIwVBwXuaBkf2/wgFyHc9fIG3sUdFrtc4j2aDXlrbwS\nYAExHaDEJ8hIY3pnTUjy9zWNcwY9XPB++PPnCfd3diC7rjAvS2XRPN45323V\nBCqLW3H0U8B9MnYDHugCKsO+Z0ev+AjZtrwLBGN87QnJt1LKnuw7DuUBOD8x\n0QUiKUpXEC2clac5M2aELHwvHqwH7osaycckwKOzRzfjcXAycosHVk2K/gyo\nSlaC1CjwIm6xx9umZU5SSGxi0TXaz0NBotaRwq0XlrsQ69b3HwApy/IpbB3y\ndae36jsFzU91KMEdoPOVRWhZNf/hAwlzfadXtEeSV/SnROZUUFFAt/aaf8Pz\nVDW8otSLBDka4sZCzevFssDJU4Y/LkkMYW895Bz4QF0YdzyYuq6aGIwQNQVd\nN2fO8CQ9qpih6nptxJY4syi/fXeCmiBJYorWtFIIaKSY9g4E2D3A/38Y6bUl\nRKRGI4FciDCU7nGeTWp7JuviOnsFnRK7R3mqDMeIlH1K/kM7OwbSwvni/3Bt\nS4iB+GV5HHcKGbLY2mfSaQDzGFjnsHftxig4k7750EHqzIms9ZA5bB1Ur7he\nGgS90Tmuws/Pmccwd01mSz4pMCo3h45dPrnvOQhK0x2NANm6ehbym1GgC8/T\nM7Lw\r\n=Ioih\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-checkbox", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.11", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.13", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "27a9f0251c1108211fb4f63bf94b1e31ab20b79f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-A+Vce+YBCSwFPp+SS7AdevqiBkT/KW01NLrhUDLaKJdcIWP6nJ7wC4DI6jNXtAxK2wwRLpNC81j/yQwpdygTlw==", "signatures": [{"sig": "MEYCIQCLsnmBwbd1GRQUCTVhvPwuq0jNKR2LfdwQjmeTg2652AIhAOvHh9hqkUsOP9NxFAcOV0WBHbm1o6K4AtPj27JPcmA6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmaCRA9TVsSAnZWagAAAz0P/1/owyLTLD3vWCJQrpv7\nTIkWDcA/XQCp+UmJp5UT2KXoqFvilts65HYIqk6zcEPHKjA3DhZhsu2p6qOn\n/C35Ylir5Vu+OSj8KnjJOM08JOZTn7eJdNYQQfY8l/xMpiNXeVjqnVyTA2p0\nHQOrra8mRGf83Ew61329MhFPDq02G0sWV1XPkDsG1Lmpd98EmuZYggxaiIjC\nbXZ6DAH6uDUSJ/LTIqYY+xYIHlHIE7nVMBm1KYkCxcWYY9p5DrDDuIO2PAZG\nwjexoxZbM4A7HQj1kbd9cmpiNpCHTWPkd7W+Vic/r9tdMKHiyQyJhTrVFaVX\n+3BlKFrVU+imtfDFFd8Puf4AM682QN6zRUVvsEroBxzHEeFDZ2Ih+0+D4K7X\nB4Jgunf9hJsw9pHexLsAZV9fZf09NB2U3k5hri5NQu5Fntcwknbz2BBQoxZl\nDsYNB5FAZib2ddSbfjzOIcHbmWjq/j8RonhPu1t5kXmhukd3xV5qxthjUc5l\njWOZNKQr6/bSag0thT6wtRtPHmM7zduZ+Ntj79YAJDqzyZIO2fKzbE8Y8+lN\nCi2xPFOU7/ffpsueWcX6XI7f57p+5GqmfBipOlSGYn/oEYkr4ryapEjw+AcI\nucrwbHeemmvyYe745pFF4nM1xW0xoHfnIis1Ce2YwGGpoNHFDj1DoPQMBK8i\nS1cg\r\n=tur9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-checkbox", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.12", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.13", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bd50afa50c3e6d1bb89eeda0e73fc7a0dfd8a23d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-qZo6oHLcq4rf4GoYx9v+RgIHS1YtZQTJX9oxpKfC1/Vl3yKdbltfe0rY21RBw+lbufPliVtgsi4G93Tn9g+bXw==", "signatures": [{"sig": "MEYCIQDchwaqkXDuHiRBp+Kdj9pr+zfE7Tep1ua8apNhNuMxvAIhAPNtoUu9j5xie36NXHcfwDaEvSHA93XYZ0owcpoLtuzT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7LCRA9TVsSAnZWagAAvzcP/2rHpn64N02hQzG8VlIZ\nO3CNekm6BXk1KpYQp8xcRFbx63YE99to35150t7CPyORa6uqbeqX5Qsd3sID\nfKa1hK0XHOViJYxY/xwEN70nC2Q+fXuAGJqWkdi4xrZ6mQ9rHbl8PuBf9x7H\n+xAcKrwgyjib28r/Mv48H8FS5W3276KUSRgNrRAfCOEXME5a8BvFOpRj0kkY\nJpOnGKRUiyMPTPj8wXeMGEkxj9NUNwQPPv8aWS1rLarjIBAjkVf+XMdyLwBL\nQa7yh9Gs08o+vD0GvCniwktHHMz+O2Nuj1qBDKcEImPnEFMz7APwMuLWFnof\nC8DWF+MBOS8ZZiNtyKYfhbNzIjeg/XABbSMm5szVfx1A8A6zOjwws9CyMFUm\nq/qaVNIm2hMsJlZJ/zWu93thg6T8KLwipE7fnuKL+NEo9rmFwdw5/gtRn1u8\nbFLZxuM+eCVVe1kHA1dd+PHm7TUTz54Q6sCXOVNqeBXt7SomgbrXMIfApyzD\nqYYw26DWQ/BSxiP1SUPEY7kTUcDJCIP83vdYQeLmfrG6/BvlKPsAczgP6+Eo\nSSTGKeg6iMu5mBRKRHrOGQNFk9qzRroxEYSMhRquXr35UYWF4z//TkPLR/ZK\n6IiZTEY4kvrY+3Ijoz323Jath23r78kKi/lN5pSaTrs/sscx7etmvw+WQRYz\nzDZk\r\n=f4lt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-checkbox", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.13", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d53b56854fbba65e74ed4486116107638951b9d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-R8ErERPlu2kvmqNjxRyyLcS1y3D7J2bQUUEPsvP0BL2AfisUjbT7c9t19k2K/Un3Iieqe93gTPG4LRdbDQQjBw==", "signatures": [{"sig": "MEYCIQDjDxHkdJ9kanSV4/usF4sXZNyi6sXqKzi5x9mq+TLkqAIhAIj5xoKW45lrxoDzSi0p4+5SDbkjSPyx3/xEHSJAcJc8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlXyCRA9TVsSAnZWagAArBAP/1c1sne1O0iUXRGJEAYx\nVmijgV/HxZ5n6sU/pD9cR2vh6ww4zNocTBbAfKMU3uTuef1E7XBRCfJrX80m\n1f15C9+qiJOZcAlx7PdQ78LleLrB99JnnbvWLVR4eeLeh+7AeFSPImummloI\nVx/tAAr6e8SdnhiEQsPRDjn76MIdj3KDlFfah4fXUN7aE2ozHjOEVM/raO31\nW0xXO6HlqhOpQKHBMGF1xG+bkO/22Lpb7fvGECh7cssGXVNAcf5+9b+wwRmz\nSFf02wKdMUUJ3qyGSRYMFkjERtACmLrrr4QPquPwVlv0cgv/u5aglUpBZe84\nm1dHR+WeUiODHW3vmHVbD1X6RyoSy33XfyzKi8ZHbxquSlyUaBzjm2K5m/iG\n/MNlh1hjLsUcEdvYwLCMd58rxDQtPxm37kOIh4CshWRDbY37hWuBGUI2FhQj\n/xdjjT4hvz/EZ3rt38QETTdAMRDFEzC4qqcr3zRYbBVNXpmtiiNgBDgwV45R\nAkcIU8thKdU2Rn1Npi6tFz5cAUAYYNmUGU+3Q4a/wqrYmuwEZaMF4K5i0sbW\n7s32UyMjoZZfiAo9BwlulWzFHiylZbZkIJz7HKCPC+t7klwuakssYj6JZUPj\nfMfIkayH/ufudJnPuyZg2pZe7MY/yYXj3FvbjrxZNhe6d1WxCfWpuGU9h+Xz\nqFzn\r\n=JcMa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-checkbox", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.14", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "503a934f73745e9c3831a5fd39d3b06062813f0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-2RJep0OB4FZvGk/PTou8BEt/cISA/i88iiloZuAxlOjYgF90W4xfrQqgczLCC64t0ee3DtUkSOJyjO45M95hVg==", "signatures": [{"sig": "MEUCIQDoBVrC9TRB5vZNodAjWOK/BndGNbDHC9dMsrCMMQNWkQIgRoNqWtkXjmnATPanh/Jt6aGF+kcZZt+qncjNKQvgcUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9iCRA9TVsSAnZWagAAOgcP/juFdhLBtbwfobp5JoN5\nOFFx6MN5Tc9aB2MaA77QtuMslAiiJoTEDGjd2i8EX8mZEqc3CMy6Fx+FWidE\n75XsuhAvhkMvJR9TDE3Jg52Jv/xuMs6L3ZvqTmFddjZlv+XkFEi/qcuywfzz\ndSITE/lK9AxtbVieMkGkCIUxQk/Rx2ZlW7JT9UUGhwY+ymsoV2yVZyEQ7n/c\ncwhpE8ZLe8aKFax54hYF/s5NItZ97+9efLrqGG9WZ9ZzIQ5aLxWf35QzS89N\nuxp7oz17H/pm4CxG89VMAf06I3R6RWm2YjJWiFWdKEnOaNWUxuGl3p53UVjT\niNIsHsPmUjThqA4Oy+HdOArmOAv6/6Ta7l4XCaCGTJv0ZrYK4P0SIDvXhJBQ\nJvH5pS3Pdb9chO4t3UnebGBPWAD7g71vKUbCk0PCZ3J5E27GtbHVx2XjsRC2\nssaMmqKWYgV6vJtGzUKgCVVOgt57dn3I5NTzxoK6Zu8K30Hrpo3nbWDVznTw\nFWL07oSQHuJovIEPEt/eQaEXw7gbmMJ85EfhPC6sdqUg4yO9abvPZxY6tR9u\na0ydN5ZSPgiODIkWRuQ4lJJxIKfjs5onDVTEFZLQOtGEvGNGtWSOA0/UxAJp\nLG9Ajg6U9OFC5CyU+HRDEvPO3u78xPcZuCly7mjXGt8VbxcBY2/KQbzCe79j\n4eU0\r\n=JMK9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-checkbox", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.15", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.15", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5415ef3385e7b9c6e0a1dac409f65c3587df7bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.17.tgz", "fileCount": 8, "integrity": "sha512-ycdUKraCpJzQxkS2AcYL2v380hSrLsr+4qG9MXMg8MdeIPB8moQXyEKoypdKMwNOykcCqh3mEXD9+WmtBpupfw==", "signatures": [{"sig": "MEUCIH6tgjAbaB+VTdsIN1F1OJEVtpnHzhc+XOvxz1bX/5MhAiEAlIG5NCSXyTuiROHHXG9Fnnnwr9NYvdDfqLPl32DbpXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTkCRA9TVsSAnZWagAAPGUP/jDCBhw7J3wEzJHR2jmN\nT3X5PgHoEvFhDo6U7grFb9a2oiJrcLfzoZo0D2NP0p+VQWsGHjw7QLj372YY\nPPlLMa7Buyi3QEcyLvZkMBqZ8+WM88X0St9IXdrXDoMh+76PfptDb7HWsG2f\n/b8U5BHeQOqTTnchycU1z9rckf47XjI91gXd/EUZr6p8pFTyYTJp1XXQ0zN2\nvPMlfmAUWoKxDZ00/9O9SXW8dveYZt+4GKiMdF1ha4x+dV/0oo0M9BkBpocq\n7XdAyJ8b+zftYOyQHntnIn3mB5nPFIXevAIlKLjJSpvxU+Dms33TpHaIB7B8\n72Y2YSstgiVp8X0iePW+vMluxii/WCCHC422+T0kM3Y4Rxq853hBhATDdxJr\nIVTPFgSUtLZEnrZSGkQwi8/iRpCExQbpKu7nT1zBdhkLSwBV4SMMZFBuBhKM\nDFpZNxYvJSAiUNMqlW7vyuGs+qQ4rRqOpOCHOqApjmqSKTR3Wkem0lDrnnpM\nT7WD5ADKBodgtyY8JkJ+MQ83LJ95R3Mn7+jv9PFu5Dn+fHKfknRBBePNOiUo\nNV6y1Q+Mi5wRwN9qb9JzSSt/zevph91TCfUMC7WxQTOqW9b6d6wHH7m9t3rF\nyllLuN5Kd4s417gQ6uprRKr7Vq+PDoF/FQMwSq3006L3x4vjahPrnXfdlcYv\nzpac\r\n=qk78\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-label": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-presence": "0.1.0-rc.1", "@radix-ui/react-use-size": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-previous": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81aead6af0b4b360b64abd6b6c94dd2c94a0e8e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-SqtZwBeGvj0KxueBzLgeWF57/y+oT3ukTJaFNr6Mh44etv4rqvpRFaozi97aK9JzNGzYG2JgN+rowEtMbLhOiQ==", "signatures": [{"sig": "MEYCIQDvwQ+IepgS3hYqS8pPMIDmDz0h7+3tV7suUAdannHoTgIhAPgd2VcZyEiul8RCHvMlWi05IyWfpCDGnWkYS+64Cb0H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpUCRA9TVsSAnZWagAADfwP/0ImLH6F5u6IGLBpcLDt\nmz5ZKdKFhFUXwGQzevaniLIdHMUTB7U06ZehTbFY475pV/xzpbFNDsotqfMj\njz0SYWeJIU4t5h6MnZ34FmMLbFY7zl59YJQlJhlhzIdH14CSelWJcQKj0zUL\nnccU9vTQjyYsIaC38SPCwQQuIswEXwPAljO8MLl6mAzb/Qd8fVDGCpbsOOBX\nSTX+9dqnIPfGPBSkoWlcU5/1oj+uAC6x2MA1EmkmD1bjH6gNXpPgCWB9kGGb\ncYXlZ1BVBJE1ZaPC1p+X1PYMXpqzoj8hPa8zBumHEoocM8m2/dzbDMvllDuY\ne1XJlMBK0C2xoAsWdx7zbhPmZWqvISPTEY0KF+dnjH0SY5XeIYLDreYIhd5H\njHWl7HveIhisAsq5HeeHQSA6AOtBzrR/kSQurKtgBLHpSGsjtisRKOff7IGY\n1Rnq9qV/fuXe09T0BPZsMSf3yhrZFSoAMWkHmLbmHh9YIYWjz3FJYxdwqaDs\nafNCIpHgUE+OVWOly3Lml+UYfPpcwX3ix2Kg5OBbAT1gUrmRGV34WTA+tmlk\nhOJQWG5mCh0yEM5A5g6N5C93FvINvfMExTGHtI81nDKVing9Nc8Ev/Ok0I+Y\nn5xpWE18iWG9TAXzC8IC+AUAyz+s5ykp2RDnsI1IBNBVG3Cif1JGrfB1+GXh\nsgxJ\r\n=+N6d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-label": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-presence": "0.1.0-rc.2", "@radix-ui/react-use-size": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-previous": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bf0c54809b5953f08ddb90d5f526ee3eaed34292", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-+rvbbnePLguBoJQk9D/pJsj0yTFAbI3UoQwo3ogUKfdfn1+LOG5D16STRKTEoe4QZcCtkvxh5KhKbARQpGjCTg==", "signatures": [{"sig": "MEQCIDUajv2FQsRPnI32qWn8m/dPREresmPZ/l90bRFiT8BkAiAu6SawpP6EJcDdkUg8cZZKYyJVGFxPeFpXPmRcy7Zixg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyKCRA9TVsSAnZWagAAGxwP/0DCKsk7Yi+fI59hEu0J\nJCbuXz7gE4TdnaknN3Ab2I7Zt0+ogr9PywqIreKG5kdvfZljgRldfiQnWWbc\nhW9am5UMLokiYmWJ1V4H00eZPj87WPZD4mqpxU1orGmuHrqxdUWRjGlF5mq0\nuYGgjy1XTjmvuJqh4pzi7qYBM2Fkje+LtcAln6l3TGlBFshMpduSGtA8g453\n8IMwH5mVKTOXm+R4GCAlNrdaW1ESc8ncYYlR/IneC8SikSrwzXLDahxY+HSL\ngY0TVOkpJZD5Bh6NucXhO2v2+ibODOiBSZk9gn66diJ9xG7TxpDCWLwogliS\nO+gzsS3oHNaeSTMtMqpwfg7AkSoTgDfEqMmZrIlYS/irnpOkFDDo1IbipruG\nfMXQ240uaqvLr8PW0wOTc5M8pGePQ+rAVPOwiH3jp0pO/PBL4veWuZGH2yy5\nRop5VaNu32PugaVuVGTpoxh39w26WEAv0rXyXgKpn6XpQSXsh20EQGxnE5Vl\nhDZzf/VIriXMuJNqWs5MD1kJRJPV1fxc3hv3Q4CC455kltK5k4E6seM3i07i\n4CSc2nM406fXuUs7M6R/WByKvA0QA7A5MW43g+qgt9T3GfhVPilvobd7hbaP\nsqs1eNOixCnrAnOosKW/LZ01YDRYzJ5taAdS2Mmy33LVKe0A6V1Iq6XB7+yE\nBKf3\r\n=vuUE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-checkbox", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2d70e5620292cc56ead0aecaba3adc145fcf0c1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-wcHK6D/n5KCPXK8ydA9aDcY75VP7wyTYITaeFt8NV56kJevDBjTQmjzJ6wWOdmHFWG0Hl7bxsnAwXGwdbYUz1w==", "signatures": [{"sig": "MEQCIGM8MCW6sxHPh0YHtxSxissANuRGertYgXZHcUK3R6OPAiBnBgNlD3hbBXcAuI990xj8iwGivA6SYstUGzBBEjEfQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmXCRA9TVsSAnZWagAANV4P/3PL3ogLN+zjstpXn6WK\nLYzAJRTgUt6NC8OqaBjn4W8ycj3wXLu6qpr84Bdngv/tx+2agfkdIoFLj+AK\n0D4soUcksl4sqopn1StC/tqGi0fnrd9tGSIZHAdUj/evWqsISR39DPzJO1Ar\nm1cqucmIDJV4yTqjeH6OkjAatYPXbtASwwDMicc72A+HzjhO34GA3e/KgE5c\nVEAB3XDbhSt/nGnjsZ8+wzl+OC8yuPvt8p5vDRaKNAzdJYKVnURTOJ6WhAQX\nQ/AN3blzuLF0rFWUVUELoBChFORUWoOQIC6I68jyTFI28i0tgxmdghxsGDe0\nb4ErLII8+ngQ8t3p8GzsP38zrtoCGN6NMxWbm3NujOxQj2BI6r/XrfgT8rGY\n+VtdW3pE2e+5hIN7jhlowVPaAusvi3bSgzc+NLyHJ+Za0uCtNl3QsdkQGuQ4\n6m3dBlUM5q7vgJE6kLZ1nuDEZVQdWJ3Ycq39rjUr/ZR5ddO3TplrYjIU7vQc\nQ5eMmSVJQE5KFbXi2AWzHbLOD40OyvjeGcGmkB2JaVxUi9SB0IHZzGNqKGrm\n8rV28oay4oLRf4S1pRcuSOXmWamMZ63nRermbTqM26jYCwO/vf3ObvSRW33p\nglFL9Ih0Vs/5QETq7m8sGhlAh5PCocOdKE8TAksiyZiH2Qt94Dofc3yB/W2y\nCaaN\r\n=r4Ly\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3f950335a0916a7691bc36f7977484f7e24a5afb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xnLRLw4awfvXaK4z8z1D5kXSJnn4Px+yVHhnd0m7v9XdDV6YdSnwH23l7RRzthKYtUmXfT3xs27F0IbJv1Vrfw==", "signatures": [{"sig": "MEQCIAS3Ahz9ZyipTSKGPDy+1UBOkJ3zCSrq/N4zFcYzluo1AiA45+7bUCBWXDETrrX+b9gcmw3ewck35HUrPz6WGLUqog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImTCRA9TVsSAnZWagAAVEwP/A5xW1O72S+0BSIkltve\nO8JdFJhgn6hkcRtf7B8pSQpY5+3Q4E47inh0PJbAadZlOoJPDb/pW7cppBHm\nUjctINMIk0Txyhh4XwJjDnRFquNcWEP5xduX9JNJ2Q4Zldi6rtD+jx8liWz1\ny2ZCcZ/ikMTgg7f35czrDhMBHJvWSfdhT95xALVVU9yAdzTnD1d7SscCaIwN\nJ07umzL48d3q2Z6/RUSAE4/XbjjpUcd7rulvoAFO6C9SKcljJEzjcZfVysp8\n1cqnQHiTRaMRxioeBorIMb8otJ9rWeNNYtYBOsotf20Eo4DfAxeRhZWBM0vw\nle2No0UnWuzFdkbZ8cvV/7zFY5RMIb9PjrM2+BH5H9XgbqloSDEoL3juvzma\n7XwMsLliEOKmqV+SoBG20gggtmoQZnLbdKmHznfsSR2F4ucJExO8LvVbwCcW\n3cHaE4ZqGZU2kPD1e2rBxBZdACeFwytSf2RDbQDr0m8Tn7AEkdjAM4+bcQ+p\n8kVTehb/X98N0ZOngaX4iC9kxXFB3x5m/f+/QZ7RuMrZOZBHfqGsDvo8SZFn\nP4mg7sqC6uS8/rcNp56fmmt7sjgkwRNHzkKT41BnY4BwCKGj3Cx56JT9erLE\nX3T5XBw/aMdhr8MmQKACpA7ftCnGZwuitmZqV/S4/VGoAwRNbVbyunsSzi6g\nKCpg\r\n=vg/m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "926ce203b8f3c95533f7a8e4a4597d6945bbed11", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ZfmtQWP78f25rgWZoLbbc3DyDIJ0wnjcXZtfJKgThHE7LWhQ6W5zfj949K5nVcUMSe8xkQcAQJ9hCBTOJ4HuEw==", "signatures": [{"sig": "MEUCIQCzHpkN5Yj25JfGssLkx6RqLV/gb8ShJw0dUZ12341bfwIgLl9ntccQSqVDbFf8QQEwxC7XjbkBH3Rp9owRWG+iHAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdvECRA9TVsSAnZWagAApt0P/0SWOC77gl6RAEGCHErj\nC5YD644e5a4E8+0Nj4YjloUdb7exTzfeGD/OGtKMn1FU7vMELtTAmwTyMihM\nRlVUFi0R2as3VVZSap+WmZANDTVBEFgO37Ic0J5kfFniwmdgzUc7sMD1sbP9\n2XQUjlx3ZXyxPdCvyoDSj5J2KvtcJ6mh9vn/CgL1BEvTxPXkupNc9NjZcp56\nOABPgXBED/QCG8578luDsDD4wezF2erLiGP/tiz6YZ0sl9tKxHM5aGGxydXL\n0GAaCU9UTuIfHBvzGzGemsh+9YDZsGzJ+rVeKDloGwihfD9f9TNuwxV0u9Ai\nzjXYryQVgRMV450y9gtJupNaEGnpfBwkoUpTRNke0wy8g6mYC3LDZbVI8Hz9\nVzNz1Wm+VS4jO/NhY3rYdPE7u5Olj6Ww4J/FOJeDQOVpO4hRa2cTVAI26y3T\nj1cKVmllfmQf/fP23QLWMI5iZFVYA/8UBxAUehQWuzsbxZ59yOkG3I167jXh\nTLN58/X0RMocZjdp0EXHeJUjWFIviwhYembS0Pqom4xP+4HJ5Z1fiKBQBtjW\nXGBoZniLJj4qyfDi6C5pDNu7u2WeAgsTmMgvx2UZhfxA94EhvhLJyRhxAjnx\n0hUKUG7jGYyPDHjMSTOXPfcon7KeAy1A/5wH/aZKsiKre8V1iR/B8tvnEgQc\nlciF\r\n=R0V3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9f0317f9d52d45cfb2824cb8a013190ca6416c79", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-v1JUH866l5HErn1x20gf5/ngVqhaFsepLkkhqy94cOWcLRu14nAW3EnUqAykGc+eQfbGssRfXM/uWnQ00y2GYw==", "signatures": [{"sig": "MEYCIQCztO/aqL+oiqw6wNuWpes3RZLRSyIKZNlsRbGX1aeLGgIhAJO6VHDRY+Aw9jlrazIliZ24FmbXe3YTRUhlBIXwidZT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TMCRA9TVsSAnZWagAAbCoP/0r97TsA+Ixqqlkgii6D\nYAOT+Koo80EQyeZr6J2JKdIzwA/ukNqeV+tFy2iFKhUWC3aerL/Y7L6UxxN9\nPcR3lJhrGCT52bYr+ZIHQ4YZLcIUBm8HeDRHp7Ei2GGOqudEJGuf+zzsZNHD\nXNAYQHXUTG292W1sEAsGE6YnS9PkstE08/+N1AiMPRmjA8PpMCUmRIhBZGhg\n3LJC43sNB6qszIrnhI/vUIUn08RoaHi/Dfdlm4mW78fdbp9NlFA6PQShQTsD\n/mtv9U1+s82D/XEjwooRiK6RF5mWV0suAuGoFXuoZXk4i1FlOhxK/ypgJo+v\n+YnUyNcNMeVgS7/Ad50wdBkOkyGE9RdOx8fUK0s3C1wWKDHsAxY/2ec4sh26\nJiD6NkYW7NVvqoJZedXNFLBp7ON2R//wkNEix/epmdr7HEbFJ6UzuwRQ7KfG\ncT9adtNUh+k56FUfUthBP/al3i2Rd4VxHPm2KoJplrBFHGK5lxqCvBeAb67i\nP8QieWJjE2OrMjo62Fw2jI4zE1COgHqseXxVXScbZQM7SYTYW+dpXodfadWl\n5nBrw4YfBHGqSPfEtJ7hJLbkyYnTDUH4tLOxBqgKP6n7ngw0npCIKUEOaCg6\nabpa3gn5a+XyN65jYzQz+B0RRLqs3u/qv+Wi/LLxzRf5mKy05jwf0YJA5FNM\nJuZ4\r\n=5O3Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bbbe4e97c61e59b34d853655ce1afe77521e62f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-4P9Pk1kngXOYkrOBGGjB9Igy+U7Fj/CmD4tAzHA16QKCrLJTyq+nFik+nQvq1j5+djk0ujQy0s+FP8yiJYoj9w==", "signatures": [{"sig": "MEUCIBYemU+GdoxWHOJYM1HWl8uiVQ7u/7Dc+QmWMyT3QkR4AiEAsq66m0No2ZvXyn8HUh9o1LK9aCsnWvFbEk1qcUe47jM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1y1CRA9TVsSAnZWagAA4NoP/2r8Ygi4I0jw6ByR9Iq6\n0SINIO5dR9mLD3mAhkbS3wncs7nQtA3GV5UCvdtSJPWoln0OsZNT7jlwNU0o\nWowVI6Yf2c4ggtIbE0QC3F/ZM/Zq0+fbOCKmHk5RLiyVMC7bzMfnS5jtJCCm\nUkcgc81XvLzfilTLKuyU3FSv+f9S6BBb/iNrL7AXH0ge+UdWodLD6uf/LkaK\nc2R+AIk67LfNR9m8YBCZgHXzkxl9FUY53CjzEZWEbuBZecBGpOLtWLfFaSo8\neHJgV1xGaweZyBfXmWi4TseSFdie1tC0QNnyrMbDDKsng8t102aJOWOEmoCp\ngyyv9T71Q54DyKiHpFQ9lofPdACytyiGtsESGQ586+0CB55rtg8zcGqsx6CJ\nb5IM+p85Aj7R8vNVwidmexqugfc7XYkiDuJEMRD2c3s7QqoW+oTlSSBkGK+k\ndaL4qV36OHL3M0i+AWMWQQeA4lA2P9rohrjL0PnpTU+XEkbO+0p7LlJp+9un\napgvNGkG7J8VCZakc04mQw5+rsXJT6aWpBKxnocNfDml6+1PD+YB4F3nxIx9\niDqNWlx1ArGHUylNCzB5LxltwkXp3i3uXuLeVaMNXfwdPU9TS3tPmMvQ8YQh\n27ik0kyB5PoIYioiMcjg8nbVb/vLuxsqhLZ3zk0ChItXTiTT8vyoc8yQwYi/\n3Uzz\r\n=diiN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.2", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0266d3212e493a1f775f853298f4842e301548d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-kbW3GLQ263Wz5u3nKqBybNne8jB29T1f6/NsKfmtLN8shahQB6Gq5cwvCV2Svz9tqwG/XhMTTGqWf5gQsscddw==", "signatures": [{"sig": "MEUCIQD8aBXUKToOWz4Bg+456n+aaC7fNTeU4dqDxh0O+bGUPgIgdpGA/69LuDVHIDkDbOTUuW/KfTrfFlS7KLnCe/PqPBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFh6CRA9TVsSAnZWagAA8m4P/2O46cJSO9Altw1plwcx\noNabsSQFa7qyI3JKopYExECuFnetn2J1YiDAnAYH9bKypzfYg2eEFLfWlLO5\nUFLgdf4reVak8WnPM8vr+gN6saC0KwkUJQYidyJaeNvxlMLPgm+NtULG3fHR\n/NdkbUTFhMaC3pahxT2c3hWcKts1aMbX3gC1+/XyPT6/vm91xW5y2UAZHrt0\nS57YURpuTvQwYR1xEp9FVZz+y+KFfW9mdPKALih9jnR2sbpov9wV1hljfgwY\n6FfjQ44k96Y/MNgKq7/ZIZh06x9aZaAxGAgGyMGFVIRp9N/8QoBNPuj7ZJtZ\nqaTykxOSSp6KnQk50Qtq/B/R/yLWVy9u/YYlD5B19sC1FGIE//cAZfSJLjI5\nYRFozySUIJXAPcyeZoSEpgUK7LhYsZzpwwWzH7MRt2rlbuU3CMYW0LbVemjF\n6yn4babUMYLwfx6J8FKDAd5IcEd4WwT2toILMzQ9bP6ix649E8wLmAjk3h7e\nIo5OYhLmZKPIRbftoAXA2clZeTNSrEztOVGmL+VzkbJwBd2nP+vKL5uCoIIH\no3TZchLTL+HrIOnppu0OLCAhxwAYJaakaU3zsN7ecrIgRf2tivzUezPnzzH3\n45D866tzGeY6wQZONjNI5Fmx+CUgIYLokdQaXHyfAtOKbtLjiDvrFjRU56cd\nngzy\r\n=ybFF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.3", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ff024c9eb49cf1659c3d7aa931feda7664fa504c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-0xPz41P5RdQa35i1dR4fce/Xzxa4Y3ECBAZ/zyWrgQIVjQmKO0d+GcqVMzE3Ls5Hg6lihQAPrf5Nyq/darFB5w==", "signatures": [{"sig": "MEUCIQC2yEb38zcLtEOwUEkANFzMfJ1cDXacK4ioT+xGxvjULQIgQh0tPMUkluXmbdmbznqbKVXL3+H6OBPgAV3BAzkF7GI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32766}}, "0.1.1-rc.7": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.4", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f3fe15c9553f3e70b7e39f6945a819255dd61c0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Iemd693Nggc4mUOgdc2DmtVUZNrEg+7sJYvOKdFIy4SyOvvesTYoMOSh1qVXim1xNSNHwcUSUc5CRuZDLN4oDg==", "signatures": [{"sig": "MEQCIGc7u9UE1BW1RcXRhquPw3vww7XwQAJ+orcOl/tSR1j5AiBtQ5qOnd9qVs7qnXSxi2kUDRTnDd0wUlm8LZ0qhRCpnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32858}}, "0.1.1-rc.8": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.8", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.5", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9cfe68dc7b497b449ab04aef73a1eaa904eb996a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ehBanK3v10uy+baNBA/rLSSYgBY3UXtkjENo9ol0Vq3bD7g+sBKH5XazLxImA7OqauHii/mXA6eTWID9LbhlfQ==", "signatures": [{"sig": "MEUCIEYuoyWSuAqQumzn5gGBKQ+i5uosSJIuyq5uQdD8M7FPAiEAyKj5Xj3ehuk7Xch/XboLj7/GYveXThVqASMuiWirBTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32858}}, "0.1.1-rc.9": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.9", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.6", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8322a3d285aeb166954421a7f7d8e90cee9d754d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-oEp4lj6Qvw1mgUB9vFRxHt8neOVXLETEApZ4tj0bwEeIP+kBWzv3Lh8+8bG+O4Pa2IlRlvTDWprcSifjPc+zNw==", "signatures": [{"sig": "MEUCIQC1I5c2zDFIAK1JHDRPr+hCaPcKJVOUW4k+qI0mQymg0gIgL2lZYBuONuAZ9VJTyPeqNSuDgFgxfWJ4NKA/lmwvC8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32858}}, "0.1.1-rc.10": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.7", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1ae2a6d81441a57ab699f3bfe525b4c0386df82d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-qT35RRZyS73iM2/uKkrVYT8P6EpRbJ8uX9+lSRciGbWhmUtBuG9K8AJ5WgIXz83aZKR4rtIRMifVpKlglhq7kA==", "signatures": [{"sig": "MEYCIQDpHsazGxlZZDZpTxsSkeijprnROiUJioMzKYSUAuLapwIhAN2L5wkfz7Wj1R0XtDJD+kI2P+Dua8uAvy02OfFGDo90", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32861}}, "0.1.1-rc.11": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.11", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.8", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f998df6065a8a17de6e88a4144995ffbe791aab", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-dIwvVEUXrYm7AQSqiHakxqLKHpdXGI9U/7Z+/hr5+bYT9P9iY/gCAj6o2KnFpgjmP9TurDAsxcXHR+5Kdip79Q==", "signatures": [{"sig": "MEUCIQCb/CT4EANu9X7FwNd3wyLtxyC7E50aKVxF0/73MYlRRAIgWU3+f1AMw3kwrrm0KesKefZunTyaWT4freEo8KtdT3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32861}}, "0.1.1-rc.12": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.12", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.9", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bda4796b83860c1a12853d5c57b6319f479ed1db", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-NvLmU0ise//+OnSyvGQ/bODCM+VBP+0eNYkuY1JBUL+uPo8pf+8spaYbgtUPwSIhnWxQI8aaweiVNFqBYVWFCw==", "signatures": [{"sig": "MEUCIQDGFq0nZ/de1QPEGtrZAWDlV9vpD+glHapvt4zaHwW7lwIgOuN2lJdHfncquwD/rHQL9JhR6QU84okgmvsQiMYfkR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32861}}, "0.1.1-rc.13": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.10", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e8cd550bf849d636d8479815e41cc7a5909be049", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Q6NWS22Dx+d5jqXWGhTqXJ09JAw9Z18Ar9B2ga6Wt9YYy1FM7QHB5JIVado4WJ6cx7TjcfCn2bl73hDP65prTw==", "signatures": [{"sig": "MEQCIDLHwMHH9ZTntfC8B3Y0wJ9JzB12m1bTig+SucQFB62HAiBzoVbdGQtw+nqeYggtS7fqwI9ziRI6TaqLmDaTsnDXig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32862}}, "0.1.1-rc.14": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.11", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "670ff4555a8fe003463a22481e827cd7ea85cf82", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-mkFoxNQwdYCx//xtfsFh9YoB/xDLIip5PlEuHnFcVIbHhagFkTMjx3VOYQNCnnfPzZKweG4rdVPOg3myospZuw==", "signatures": [{"sig": "MEUCIApt8wDKOszvBXtOW72+0K16pcNtieU2yPrbp01alDrRAiEAtDM9+hDleQnj4Z7a7vOHXMi21kQcNFSOLo9L3T/bTX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32862}}, "0.1.1-rc.15": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.12", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d305980bc374b97b37dfce2afe734d0105822ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-JF5bve0Bh2vpA9KpZS4gVrvEBgEfjJ1DY6dffnu3CYVNGQAzbSleFk7qJl4PG8vaZq8uWT2cU2YNejj3P48Wmg==", "signatures": [{"sig": "MEYCIQCuuq0ccEMR1EPkYV5ajMNhG10JkcElYgp7hmSjtDUVuQIhAOlmFCc++QDUbG2LzxlY5+12YNWffnP61cvmThbOfKUV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32862}}, "0.1.1-rc.16": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.16", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.13", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "405aadd9844701c3b0672834038149ab23cc1e23", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-LZhybOUE+5LC0MJ2EyQIWsxlL55YNpuUnyXYvSuPUI+wlO+8dxF6hbgJ+nm9n8o6384q2Y58y0/ZN4sbh/U6ug==", "signatures": [{"sig": "MEYCIQC8kBvyHMQBMVE04pdPDjlC1jY2wYZW84Gh9ICPNiJH3wIhAN+bb7+DLF0NXCCB2TxjNbYofJu723Jtkg5AXB3lghH2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32862}}, "0.1.1-rc.17": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.17", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-presence": "0.1.1-rc.14", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a4ee9e34558cd0255f9dc982e9f334b7de5223d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Jvh83dEdheK6sTUGyzkF5785o4ne4Y0mERTlEU89ZkRfzWdch9yNRop18PVRC+XesJYi7byhSu80l3sdIjaZ6A==", "signatures": [{"sig": "MEUCIHFcqnz+UxwKnWbPaSUf8nNLZWPGrdbEXDHQb+Dv+WzVAiEAuQpwzc3sSxCc9goICxq7AcCMPsYanAZv/3YKPaytbIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34580}}, "0.1.1-rc.18": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.18", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-presence": "0.1.1-rc.15", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "40cdf2f8722cc1e5b118f3f7ba34d3a8a77a009e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-Xua8/uboCn/hawx85wvyBjWYicdKDs729yX/W2Xda/Vf1iFBUBu8J5aZDLOw31JRysXhniX9pJrEFG6s0aYo9A==", "signatures": [{"sig": "MEUCIB7O4/AWiVG/u5nCrH1Z8RCwMv/YvKKUb1hGHPQ1XtGVAiEA3Uuqaj/trnQ9F8asmPa17fJ1M6yer9DV0lCAFmTsW2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34580}}, "0.1.1-rc.19": {"name": "@radix-ui/react-checkbox", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.19", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-presence": "0.1.1-rc.16", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d38b8deb65e90047af48476b504b4f8556750e96", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-j5hbxuGvOAqLCgfuBVWOYFE5DDwYfYHL5shAMWEfLoWULgBmcXNosDBo+GYMHBAQJShjL6bCraTtqs8UmyTntw==", "signatures": [{"sig": "MEQCICpWXl1DySeuZmYafCKlUujkl4ZxouKtuOk2GDyYSJi/AiB+SXqfcetP1Q9adEy2J1JpGHRKQlwLz2trU8/KjtYexQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34580}}, "0.1.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ce551b680545fcf44bbea5da6ddaccce8b7df19a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-QXd3+GL9bOQaekG7J4J94FbpyjIrRkrIZow9IuEPXcko7pTFGOJMd65jaBkSN7mz4RDdNdtnDObbs/KeQvoOWw==", "signatures": [{"sig": "MEQCIFtnOg5SdzgL4zG41lcBJR6d7xNCN3JeU8CnS15FuhgbAiAIJB1foyhTUc7CY9axphaX/IsFGGRgiCr85eyFN8SuXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34523}}, "0.1.2-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e347d02b4c897da1f23ad1ca6ad2cf5cba20bb1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nxnNoil2jG8zu+5puGkFCpGlFehHJE0a0z9h0wNbhVnAu5z2h4OmQ+XtP01h7tZQsPLaFfUhCxfFI2RG8EZuyg==", "signatures": [{"sig": "MEYCIQDYH0TJyYDvIWc5PeTV1diCazhvLCJ0JNtk2rrmYXFhcAIhAOZnQLTL5er0hYgZqUueOzK3zxPi9pM03t6asQILx9nw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoTPCRA9TVsSAnZWagAAMasP+gLFYOqZDYCca8jUC39Y\n1+oCqAhjMjz8dwDQQ6ONR53SRTcKqmo8FQ0Scu/29hfqqmvIh7Sl+dnu1DHo\nbokuaQgkenyeMuiOmC/x7Giy1/w03yd+VqbFcz1UvXBerJReT1mRyfDWfSLh\nGurFSqqyMTS0ifwvexMeFJXoVCOPslNCk6+KZKVaaL8p8OGj8XtWXV+aTRID\nOA1nnmX3TdX9HpTY1cr+ef9KCpZZV+rkgsAYNQ+p52D8uisnkYAGPTG+TMj4\nlLqDABBiqnnaDu0ZI8d1YQJP4ItFEBNDmzhaBXJBqvDNLFfjZS8OqaYvVo8j\nSbfRsC28khd9HBfHdJxbPla2UNFROyqnlZZjhjuNCwRb4KFelJXfEbqHZFsG\n/GwiYxg0Cl9lD2LKxbJcYb/qANHAVecVlOAeOykYZg2YDjCthjH9YQaTapwr\nD+XRkPc8WLiINDz9aXP4fBmWhx7oV7Elhm/JszeEOfVn8GHaWZnIpa5K/1rp\n4fBr73L1reGxTL3Ax+AFccC8CFA4u1aFfE2A35YVxrm3DmKfEJiom8rQdfDV\nJaoQSRL7Pu/rAFpwg19JfArTbqCYs9cAxiklUFgAdxVV1V7dmFGJ0s7Jiu0Q\nVRlI9krEHam9lJ+dRGGZFXGXiH1xrnU6RqPBd+EqVJF1guwctSQW960HM5Hd\nj3rU\r\n=aKaN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70e85afcfcc382ae9357c40f13eec6f2b3f25366", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Bevb3f4rnkCm/RQ22sCpsdmDV3fDZESdMtnCnpaeo3gTEexaOLTOM6SWzxSvc9Y1ppFS9NaA/nkiMej55+CqvA==", "signatures": [{"sig": "MEQCIH6eJXgfVQz7Vb4y1TGRPMqOH+z7hkFIwXtcGoYtSzO2AiAgz0cz1qiBrBiBnhzKJxcYH0pDlgqmT/pCWZijhOsJug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/jCRA9TVsSAnZWagAAtiUP/2qXuPxjH1nWmG2v0Y6y\nmU6H/uigA1gs+nscHdq6odLIuuBcOADZx3HA1RWhdsSjkLCOJXd3Tg3CT/82\nw2Ed0jeVmVZgHHoVZfcj+F0gewEC3SP9eyIkcrM+1RNsRAjsXkRG1nEbzgBG\nfY3AcL5Oaet9XjU3U95VW05W1FIttyvL27hhQItfRMG1LBz/5HITnuckgTFw\nRmMuDueTTCFE5ftYWw3dOVcxQyFX9Ys/AEmmksafR2r/xU+ota0+gqYFgPmZ\n72suxG5t0TE2A8mSUCrCWAD1NdwDNTe9SJjySnFCwCD+qgvflt+JFt177qyV\nlmJvKZOvhcECYu/AScYaGYNbnnxiE0X2WBkfh7+FpQ5TGvVdbmNj2mazpEc6\nWCwmjFA/U+MdXyFT6amH3BrO0ZwtFq6EFMnaDu3MoDwUfLwsmG9F65dVc3K8\nhlRoCjOWht2amYbAJrLAfslCOQSXrZxd/9xPtpBXdw88M9BXEGgm+eh4tAXr\nb3Z7R8Xb6hpfr+T9GfFrqEy8GVvPp97EdvOa9udHcnpSLuMLxYjjWfqz2uC9\nq1Dqhrb6IYcC/TTFVCAeQuLBBDiBVw1ZZjAy3xa+HGRMC4OPiGO3c+dmcHQY\nf6C0z72yDwcjemVJxEbZuu7l89DBgWlamiq2GkzVelKSzD9JPb+SeLuMWKaD\n1De0\r\n=14e3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "71740e3941259945be1abf75900db8cf4dba08de", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-51B4QcpyrEhXu1Oc8jPCdWfEFvx+7eT9Lnt1FTgUWDj08K6460ayOTahJp0EshpgomjHm7BJCECcfVZUdNCy3g==", "signatures": [{"sig": "MEUCIQDTe4VsutLqHXEHHnH57n+qvFPrEMZ5ZeZ9KMiCtpTsMgIgGzmywbZ2T4wR16IQKyg3Sesf61BPGljO1LXvOSeOUyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiNCCRA9TVsSAnZWagAAGgMP/1jalprqnmdoIPDsyc1x\niK0oSiDQ1GpIR1Mx3FFI5FP3olonOSqVa9cH8Javqe9+RC/DILXfP55ZqE/U\nBly4aHEfQ29ju0qr3okUNjPk8JZnDuPbRqVZr5GVNffwlq3rRST2cbn2r3le\n9dBtPYELeW3DMWrsHe0RyZEMGFxkIyWJImH6oiK9hz/wtw2kcCrrEyOzHTSI\n3nl6S8AGG011k/t6fk5OUGNHNUdPZyrn9/AP+GSVjuP5GmD4pQuEyibkew+E\nX1cL9vnC3PgHEXq9v9AENXbCTXJDc1qfZSXCR4Xk7qhodyFu1M9qOOtgEfiy\nhPkoaTvrPmT5si/024lFVWXFn//HmCtq+INtTi5TRwKNy6hTXZmXTYzSf09p\n7k9iwGgQvYOrB3c3plilIs40AwTzU8ORFSHMnivhmGpbYimEbvIMDN6/i4oW\ntGr5Wt0mFtN/cEybblcG5aY2Z9zRIh9NZ92H8fmGfCSuB41r7MusB/0A6o6G\nz0bziMHXM7O42UtKhthKey3S81Y24Hxom1cNn7fK8OpBiFcVP/yzK6/HKobS\n9wGi6yu58wnBK+aMVAWkc7e+tXu2FBNkwusftsSdE+8T6M1t4nkfClQbItJK\nkBsDJ2cJgrn0LYtKEZVf1b/LHwG4dUSjooUHUFJVOCHZ1CUbh/Nl9u6Uz0yj\nznFh\r\n=cIpl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b22e858ad1e86dae6df349ffd1434affcbc95886", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ant1GalZ89ylxDl62MT7viP0UC3E82ehTJWkD2OyUQ1lLHJtoIhxnihrlP7PA56Jvo4xRbjTqUoffRWEV/Cs9A==", "signatures": [{"sig": "MEYCIQDOWunbJasAJ+e4QQRW1WrxVNpTJvG7d5atlPtDDIr2rAIhAI9TFAw1zRE7pLqugB8Y5Odc7J8KDwkuWkN06ENdoJl2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryiQCRA9TVsSAnZWagAAyHgP/1lzso03k+gJr+o9ehlK\naOe9gvq76bNU+psa0/XTbzl4NB8XTJSKq5P567f1OhgawvfsrxPYuymZxfYs\nmobgRqPoTD6QzO/1ETmLDRKNrtV6zZoO9eLWTu2mzFsx9j6ypQwXns3dr5kX\nBVwJQ3NYqPrRuaGLgm4W/5mZUAM8lHIkg7LYgdqXcjGTlid4T+ShwYsDFgWY\nETcyJcvbIUAirfGHOn5zX/LUI1zpvKbYFQJtA/fY6XClErWB/73qeDw8Yz66\nPkjo0dk8C0C4k2qFqeEO0PZtmaUC8epDU3J3D3hgct+ogLeMzgTD4eM1vu3Q\n1JNaNuKE4TUpHqPqeE2Vx+CkE2rnbriVL5RnBlHzwVIXk7w/ASFvWQDAl+DS\nQi/oJYAeq/Tv8ax0orAbsr4W4AzWWu9z6tT7n1jR+ckqiFpWJz7+BJZDBUCe\nLnbLgGZn6tYr3i1m6QEVBvpy/qHLD5OIRaw1oHojJfeZoEygGgQW7MHqr/4l\nJlks9LgoLNW7+wxGNh1izzqhn5edLNZYdUOoQnsRsjeyj/xeRYiUyFRjcLou\nSSJRxZ/04nyanw+KZGC76VFsZGwF9rpsYbyJMS01AYgNVy+muL1Xwe4epXQo\nrXkKRzrKNNECtVXf62ryxKTI15emKyrpBaNGR9X8KLBUeUbGC00C3xJ72GTo\nPNRg\r\n=PHtM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "02cce9645e801d3226160018668fe5ac78c4bc01", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-SWMWuEpEnIDXCvXQ/hB74zOxMrPH45zMimZmIkVGp1xU1vli03XzjnIILDTwufeC8AyN/TWWtDBq5zH02yev8Q==", "signatures": [{"sig": "MEQCIDp/iV9xGUpiZONwoquQsdpuLGOlvH9o8Vom/oVqjxx+AiA66VDB2T9AWvQozi53mtrX+lOTAcuMmpZfi9NAlplmwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzQSCRA9TVsSAnZWagAAtz8P/A6RzeE9ivO9MBqr8M94\nNmzoTCQjcAfyQe+ZVr/fYSdUej8tor3uUKWYmG3SXemvgQkMRX0YEg6yELpL\nmKcMSp1k6Ee6FjR+AT909HEgOlgenYfsWCQwH4bc4y6PJk5JmCPkI/6kPS6m\nGILRPNGYZ75v8qaZXXLTZUfJyBVMH+7dGUMoA/ycN/5vScbhanXicPRbTY1Z\nRaAlD0kHvEqM9e/2XIac+rQxsBl0Sq1CIGRBiNgm9MPya4sy9LcPIvVKSLsf\n7JU5enZpmUPZnLO2Ih6UEZsM+F1TJD1foqZu4b4mupBiWJtqcGE3Q4XchGaH\njbKSXeu51G6+V8XlLnLMtTKwiAXfPQPmgfsE0Fe9xoPdPzuGolq4MkloQ8Wp\nsSRCEr0vQoBS+Lv9SK84fHUZHtR5E1vHZd6ZpxCPlfHZqBLNs6Hd5sGJ26jV\n0TEdWvgjq4u/AMSnMR6hG6gMvFXwaeH6rXckw/FwnoFH7hE0s0+3H8+QBj/Z\nOit/0u01Yi3OPJi2gM3ZQSCNRBvbEQs4nPO61ijcM1KNFedI459LgaDhlQUS\ngKjjrmTfb7Ot6CvixylVnnCls+LKosgz3p2Mq8gn3hTn/WchYGwsr5nyazN/\nund2Vk6WqtHRR0PVbi/A23Dl95VJDLE3F6JB2NZh5mz2tkivWhCVtvVOL2sy\n2ycy\r\n=GWyV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-checkbox", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ce679bf80f624a9832e7df6b5c6f8295c8f01c1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-1z3nzjdf7JFpIgjMAqdZcKKJhp0FhfkjG0lrD+kXTidmlWJVU57bOsfsWNw5dc9k4HUFA03Zjg8CenyQK9iXVw==", "signatures": [{"sig": "MEQCIA/qAB37ZipDw3xRIl0rs0YHh9uEO6453tVFIOyByGqEAiAf/I+OUwG1zapqP0gp34Pa3zpg1c6b/cIaQjVGrfEjNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41qCRA9TVsSAnZWagAAjm4P/1iw7DkPAM9i3ghRQOMJ\niYaukF0Dim6twNqe8j9pKwueHepI9IpINNIVI22Y7xzbpdmMK8ghXLYLc2rT\nsNGvS+3hGecFyrW9dEF498PZyCkPpg0qmlTV6w/EVuPYJwam4/lYNpI2ODqj\nMg74Ww2mzVOycq0H4iMwqvgm6KnA8lmqJeidZCKtjaaVHavwnWityo9C+svr\ncXXocNQyePNDYqGaAi0+1CgCvJM1gYbWUf0UkwUtnvQfXN8p1ApnjnmLq/uP\nAI30Ps4UAmwW/bD6wYHf0WZ05B8m/z0fgubVcKkoi07oi2Y+xTNHiM8cqopg\n5z/YSFSqvV7DmZWLlD0aFd0xJULejWAbe5tcgv3uEr19+KibSgX8EgLwlAtx\nOR2onspAWzlAUo1SGKUU9Dh/hvb0bX3RfS9QWGNWS5oYDYE9o3vguHlYtogq\nycOaYQuBZwgSswBsTRn50hxj/mJQ+vA5SGtMc0E3Ur5l1jHtgxnVpT0piw6Z\no8y/8Bh1lIpGIdyc5DiQtTe3lkQFwB5SMXFyDAJZCVImbvY4g0hhR+bmQ68h\njst8HaePYbApgvwfIH9MTwhImICLbjXUlW8P9Cd4x9rN4qWK1aPzzDEjPIM/\nW9pQ+oUay3JVh7Dk23D66oDKrr36mde/owhGN7sJxWMhGJwQOG8WmDlu1OXp\n54xF\r\n=eQdE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1eef09773152d652e443b9f2039989958c83d53", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-+5497A9mQ70eN1Om0/jCDoRnUPssgNTGIHMzWbtQ0nufic3PDKazxAFAmDB8Kv+1FS/UnX/O/PMlowDZc+CShA==", "signatures": [{"sig": "MEQCIAZc7TEFk/6RU8bMFmmHT92UEmregBVTTP3OkBa2SBC/AiBJzEt1mmHG+j4j1SAs/bn4gMlUhGQWXPTD+NQkt8Fy3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDmCRA9TVsSAnZWagAAIIoP/R+jH204q+y11OOqu1QP\nxa3HwEysHWom5g4AY2eyYjZ8raGU+R17a6I2OvFgrLGNL5gr3jmfzUOdhNQw\n83EK167ijECX6BL9/HJvapN7S4vXP9q/GEB58nE1ezauSPJLwS27yHqVD/ws\n2Bi3Bu8LpoV4NzQ62/WMdHyZyn7WnQWxXjR+cLrvTH2/gT+9TxXlJySdlDdx\nxwzHfGNbKjWZ7Pg//cfHYqDPObtLlw+uit4bfmDF/dXb7IwVXAkfbU0A3gec\nVi9dNfWzXrmSVCzvis5mdPYPmfWd1HsPdtJXK/sI+bo4hrca88PGAXZ1Ml0V\nod8Lvl3Hg1q5jfyONPMS/M48pLdwYCzrljCf2YqJDbynym/9vSMuXC0Mm6ak\nrFdStVZhXUk/pYwIHkr3PKtshVERagpReVN3BraBCqTYAiKxX+Y6A73tXwWY\nNbzsmllDv/RnHs3W14k7ID4OfaiGhXS6bbELF1JzM9N9ttdAFD38gJnMH5cQ\nFyW8qDJqt9YsFHGCZnf+7tBb5ELMarnddGXL5q5CZxiS8HPfaXm4C+47uiIo\ny7vXbq9fR4juwn19SARUX4Snl8C5GAJ5KtoacEPKaN8EGgIpNyLDckQ5HaGg\naQFLZrxqGJ7ecg4NB9I+in/k6Bnf78O5appNmeYFkdrOHx7TovYBK8NiXfWx\nTuua\r\n=MnDF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8ecb07f51417dd9ac7c55b7c1d547865b02a28ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ei35F0STpNDwNXigq8DZHz/INOCzpkFd4aZ9x1b4KpotK982LxlOiIXjnooRRQ3YhnhEtRdIX5EfFS52+yV49Q==", "signatures": [{"sig": "MEQCIBqFG4sIKD8gsIK5OTSDeMhttoibLlcCN06IZN6ubX3YAiB1+/M3qeLEtthoJds7Fqa/Bo6752du7ckZkv7TTZXINA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsy/1CRA9TVsSAnZWagAA5u8P/i4tnThICT+/qw+YIAbI\nlJvt5xOjUyzdk4RpRDnQe+Jy//y4gpSstgysfz2ABudKu/5TUeiH3pyDGQx4\nCd2niVXKxyWyd3//Q0HPc4OiUGHfkGH5jND9r4vf+ByBTYUR6IihvE8jaxyL\nWxBf6XBBIhY433YJxkpirq3Fm2JaTwvy9DzVcB9izapuJPaYrlOzQPCXBUZt\nwTJf9RbndS5HCP5hLh1yix2HDoi7kuYMdiYyRGpzxsH4g37YWjHm/WdCbFL4\n1M7uVwGoLsmnJwSao6/NvGMCQW84626sR9LLspdo9HsONnA4CkVbQyvFUNrx\nXqeWxoHOm7mekcmDGMMQGnr3Tx37Zfrg0tvZ6j8RVc8ANkI9s0Nyv8PHgtCa\nnNBBdCL3XBTphAocD+zOz703VREdf7bDiNIPrzglJGg0PzYh3JLmWkehMSfK\nES28PLmWRKPTU4/8FR1bITFz7HZdFPYValXVUZzgkBuHls1uI8UHr8oEzHpQ\n8vcvYChMcSwRdGN2fsOhC7vrZX+ICCxgzB79/L49d5YtBCmTDQ9CkeSsCCGq\nOyWIK62COUzqEyio4rT5jo503mIkGrYI4vzr+deBnFvWUC8Kd2ZIfjUTP7Pr\nUsoEADw/APj8WjkVDM3JauncUqsd4aej4utJp8uYRXix9+o9+hO/UwaLkaZJ\nq0nd\r\n=69N7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-checkbox", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "343023cb8475975a0ab1ae1eb3fc78715cc19d16", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-UaaTPwZjTVMjzMROJHHRW9T+J59zaPiuUnD5HCYWgSY8AdCI8207FCSi6u6HCNT5D+qUnSXKZZVVghdoAUMJmw==", "signatures": [{"sig": "MEUCIQCzuVinT7ZAVKZeIjwxiS/zr34btFnghnfu9ZcbJbCfRQIgW1gYychh92KtP6jBCDDXrNQFE2H82oBIUveVuKxJKBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszrwCRA9TVsSAnZWagAAyncP/0JLHGR8GfjyAfJf7SPI\nQ8OtNnTpahbk7TmyZMl5c3/LP9KBro1hH8KXajdcAqeUJfTClwg/vyj1R4o5\nHdIyLMHln8eVHtYNL7LceebA/y3OuoejL91NuanP9EBv3baBqlgJqhWt5FI9\nFn9kcFJBYPpWLE4d3cBSXaK9I+bPr7BLUUZ7kd+auTmf8xio2EBnVWs8b/pz\nq6xcmoZdomrUZyaOpshOfgBWMDQgTwLl6b3jaulSbU/T87ka7gt1paxpETcn\n63GXe7ZIRZNt1jZWSduiXCmhag+c/Jk4DDh3m5VADl5k5ybZfmlGq4YdFtpa\nI0XKVuhpf1hZDRWXoXGUWFdt0qxm+nCqlAREqGQ9kwMagdAu/CiAiBXIL7sf\nmIxjBjLUtvVoS41HaHXQ1e7ApDoKveQEb+nQGCwtby9I5jZPmhgV8a6Mqhek\neA+29/3UYDehX6MstMd4Zrn7DNcK7Olpf23eBF1di2CoMmWHpn4uP26ofBWO\n1L+4AKGXktaos+DbKLYDtSo3gSZCX4ollJ3TirJtCivQY7lGyMi/Z4mZflBv\ne261iaMfKnDeoJhcu/ZmdkqmYkqivT8JGlwTVWvk8k3TeHr4tL6O0WWrihHz\n0AniXks/oZ2A0vO/FnQZJGl4kc8pDINvpgOiyM3jcvQ0opJKqjvsD4TOfn0g\nQ+Nj\r\n=Y4y1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-checkbox", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "170957725b0b64b9532621ec14e52c2d3e7a2bb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-UtiV0y4CNmcAdCqRaGGPxeET/asO44rfKxtBbMIxAD4BGPfSIe4+kkF0q624S5c7q07HXO0vhOYlSObR3Fj2bg==", "signatures": [{"sig": "MEUCIQClo6SRBQp8s3JLp+1VbwD4U3CbpPSXfRVQM8TLK0SgTwIgJHPjDSgzu7lgozZUIsaJ3xfx1XKFsstRsG4qJ7kLtC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiZCRA9TVsSAnZWagAAjQkQAIGtTAddHVAnNeikzonQ\nhnPwKrlkrREaEQwPMc9NMStNcUo1jOS5C31NQ4FJPCLO/on9xbT1OrlM3qHh\nv5+GtKgbsBy/sRnyr8jAnZ9HYjgD9QQE0Ga4wEj7ok9q17jW7UHNvCbe7TYh\n7A4xt/NNfHyRnSWO+T61FCpFI16htCZtegb35EKOTJBivLvieOvNrtfFS4SC\nEXJO7LyNuSPikvqJpNaZz14MX2ffWFJazqwNYOsTXwJr7AYgVhNV6dcsvvZN\nrh8gsj28TEyHSvH/MJmB0N4pTO3AuVH1pUTESWHKEbfmWoM7PCl+suf4iuUl\n/5UZPooNLhJU50ynCEeg1kZ39vis4qfi32YAXp1+vx1dlKXD/+e9NaO5ezzn\n/R4zdUgEJczoyJ0j3/H3Pz5KcbmHpPauKlfWXiqI0iu8+hShJszX93cA+V9b\nJQ+DuGFYi9WKacc5FU0/rHMhVq1EKfwpbUg6QRtV/Y5DS4eMbPVoSVFdfO1W\nUmadbxPUd8GsJE/y0RPWkrej3Sq92hadmXBFRvOXHmYbNArVyycDl5hAuXt4\nYYMlrK1oVbrs1jB67tke4QllllWk9boH362HMHsrgLmZqHio6cr2/OdlWVy0\nyf85UH9jrqsUqnLRU6TRbNAp1Fec+tA/mkcVpp4dxAt5ScrqUNmIBPj4IsBJ\nSpgx\r\n=ejP2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cfc8f89c33228747d7d3e0678756b74bec37c457", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xFkAXRQ4Fs5PgKfZZEQ32tQbYtej+k89hENJ3JbXEeqRlx0HciAt+YizyZ+UaNDYxRvz0oOtSDhvwscQ3XpK9w==", "signatures": [{"sig": "MEUCIQDEFzwbfUWpPXYka8k0wTAHS724IMYeGBZ4nAPPHfd8cQIgPZJWXHRqv4vR6rgcY5woIB5jugJAW5mGdOTNvEov6Rg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiiCRA9TVsSAnZWagAAuP4QAINfTEIT/+l5xqhMBxhi\nJLWKJKOEvnFcnbU0lg7cCOZxb67TV+u620Z9mY7dGmi95qUf9WQwWuLwBLR+\nJxSZqPrBc4X+c2sHEfvSR8gwkO7wGe5V4x0gtcBH7aKbjq0AtPgULtBu5Bfk\nZTjkl6y/U+AKCrCHU95LCvsfYpyZ+XaJl+aOww6yDK0aVKFS05XnAYpRqSRb\nOwSpk8NKyYQW5cFudI1T1jYA4s/3VoI8RRcBF/uF2kFQeOQyyoMmR0YsZwrK\n3uka3053yDCSYVrEGTEA4yVx6doaoAEUmeSrXqar+mXRTejoy5mCcab2Sx35\n8p6+8xuvFQn6EtxjulLtl+t1XxSfws6yWDMl5FEYCxyMIZQrNp0nIk4otXOH\n7bCQ2a0z/qHPYNv269Lb4h4ZTPiPfjXhBB7HQZpNjt8+hMdAz4CWUTFRxsKo\nvZGUWVSxi5Csi/LWnQTIOQHYwMJpEkw9EDLHlJEYNFZk9rWa11pvD6ua6o2U\nKeThEGA7rnQmH0p4CYG3TC035rT3ljX+IS6qXyfDrWzNMUnncgCYD/1gedoF\nU+XYTbwEwZzO+G++1i7U5XvmkmfZu3KZ9c0AW9qQnuYQUEnjd4PPUQFUa3DL\nKvcJMKjmfjLHJdt2OLZm7erIepBz1hm4wdSi6Zj4OK4eX8hu6UnSR1dFRAjf\nb7FW\r\n=fAsD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "daf718013f93a36e097549e47adfce12777d99e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-OHG++DF3rt0LKceBMrA7PYDXALw0p6WBVjETGSLCjPMLsM0xIdyHZHt9Vx2E0+sDLywKJvS9oXNtodM5vMIB0A==", "signatures": [{"sig": "MEUCIQCtVbSk5n3Xhjhu2RxCcsjFtKw5bRM0DnTudJ5qOiDiMgIgNloQamnjDRepmF/wi24V9iRM7EhCiLt+FttVVyNeaYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3duPCRA9TVsSAnZWagAAMSEP/0hvXZXIn/X/kHFrCalk\nPAUJjmSvmNrkcyNQzPkFnKFs5Cjy72p+f5PMlsLDX4GFU7Ic9uqMVDOs9meL\nqIokUzgyY9ViNUl578FtZR8hWHevDuRYm6vwwptGAeWa4CT00QK3+PkVJsLV\n7mpyv/0+LaqhtMrOyR9nngO2NpZnwPEuqyctqnh8PpLsJCWN7i0AjqDRngzp\nzwctTKo/Z8VAcEopzk/zm667nJgUvuX63KFuaFfufZ/qAS2kHrGaC1Vp+1+a\nig5dM2fdcvPiucL1/rZviSfP+Hk7+w7trCFdgPgoIDLsqhhASv5NDccJGZcX\n3eJPYxTyVjQJ7kK+zVNAfuaT9lVOYbd0i4U2HZ0iS35hRVmNJam3VSykNMuY\nPuMElVbbmLzYyuOW/hZq5tF0pdk3KBGrj2shU1C/NsD7UIbaU+5RgJfQgdNp\nmmNrEKOYgovh2v71nmAEe0LrxPyZX5FR1pjlFCrqfOu7XGqJxEu59XyUvQlL\n4KH9SWUjozU7iekByN+Gm+T6eWLMnvjDbBRjaOnAXmVV6sIgU8qaX9L9T24r\ny8jXltLh2hE3f8zsDEFjKWMA3zdRO9XNluhfpx0YwqG0xtjzY5iaTu40gXYR\n+Bg26EL+gccMNAUY52n4kHEwWvhQs1OIlZneXA+kPKm2TEw9xIr91UtZyfnK\nZl2l\r\n=B9Y1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "01a6db4d9db8bcb7286537db5a3432e14daa46b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-nM4M2fcwC36XORtCm8GE0Mr7Git3SvRfujFqUGySQYiVXFDicMEWdB+miMoGXXKkrFyn4NBn7jaUG10kUmv63g==", "signatures": [{"sig": "MEUCID7MROwqcgmbzLWq07ZV1BK0wJZg3uDHBkIresxv2xVYAiEA2vNg7+EkHJRPm8gXV4oH9IcVOiah4vDn7RZwSavhRmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BDECRA9TVsSAnZWagAASOEP/16znA+j9Vo4B9V9UeP8\nfFLKhGeUxEJNs6N9AMqltfBVzUU8ta6/eOvlJwr+vaBJj+D5f17odA0RAfUP\noVqOsIe4E6VOp+fu7cWM5cxbYWJfQxKjwEJqUp16zEHFuj2cwfRaXE6Tnfid\ndu8DRJHU9NoWQhhM5mWWOVLv4mV3jBwlvVm1U5gRnOodIXlhbfjSB/J7M8sx\nRYfE2D2KfSCRN/8pDs6LsNDFaR3IEdi5H6txihG72xwUy+YCSL1KHK9h7Zzf\n53PIGxzlToCf3RkbK0HlFahX4b1/WtJX0mmiGdGmfmJG88s2epeST1NaUSNK\nj25zb3Uj5+yaCpcm26MhdKgN+bp9fu7BZmhHPQjLpSdKJi8OubyXtIa1KL7I\nyKS/U7N1tpEP7DERdEy9YP1VjuL3Lsrb0PICCNx82zqmoy4UMKcH0kYWpTGu\nwUD+aJYUElIliHivLgRH+Qlw/zD43w4dCrTkLXJK85I6mdvQ/V/ByLAPWbYY\nn++Y8SrFjgX69IJinoqqfbw6eNGU0RcCo7jEXDf/HyT+oOJxJM4Ka9i+gAzt\nmM9o5/1kdeIDv+0OqrCTNavuYHbJBJyHaIPAVinVHA+PHG7vkaFUoFfPRtlt\np9CUnDfVtEZsZ0k2hgQK5HLB4YUROyxviJKnjsn7niMs6pHauqqMMOM5Y3MR\nSMtw\r\n=RE2t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b72ba1fa80a770eab6a0255b3bfc34f7e8e38603", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-69nMRAa/SSWArt4avjY2nrr6xXlD6qFA3gVhhd5eEjegxEnbYbIDi5Mahpq8lV2iAllMvuwQUtw/wfWLdFxEUw==", "signatures": [{"sig": "MEUCIQDJWZEXd2h8/klzkc2UiwCwC+g7qaDwPZZOhBG3DcFLMgIgTnm2qVWchvvpHBHe1C2RF53Khf1XoU2tG9MEPmTxfdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CloCRA9TVsSAnZWagAAWtcP/ijWaXYPEd/W005Mo7ZU\n4KG7uqaBOVjNOnWvF0Ae1khFpVWQd0vDJr7JqtN7CNtDLhk22+6pRZ2CnRlK\nV3ceQQxRUZfrh1eQcx4eCUmvmtkcwAMDgLC5vTpF7tRT2hHjJa7PZVnglVlt\nEO3FZo2OaKIkXtjzCqubaD34IM5Xchd69rxz0YyUX1auVid4ua8I1xMWhhRx\ndKyy+SHSSbkm4M4PV8OmalZwvGnHMCKZNNE9WoiD5eUrAgHsWoZDRvT3pLKf\n88h8t5uTUKpg3ElEc0VzW+1IpimxilXxCYkRaKzqRGp8FuWnTfseh5jmsBPg\nydkVp4xSXMUCkHKeXcAP8ZwqDoyvXraLCl9sYqtciMoDy7ySmSrbQYjQTbBq\n8d0kEEgdjthuqX/3J1opI1VsIGIsTo1z4mEjpstuIaHHhEwlx2O/jNExM39d\nZsey0gwunpxVIhgie2dV1IGo1vNoVMZLjO+jMMV7tUNoky6zWI7dsc/rvSbw\nuW2Jo3kbN9Sj5n9Sx3HeyDeFoJ119imojS72+67Y+2Nu9xJshXNosb/1wc2c\nWdSlMDb2ZxJD0TBtxN+EegHi9VHHXf78XA6PRD+RsqkZQ0SpyySsdlMZBZb1\nK47Qj/U11j2CFSJ8Xp26rDoLYZbsN4V/Pdkm6mmgfzOiDPG+nR8vIdg4Wcyy\nTxqD\r\n=+q2G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "62f86c011555735a8a0eb495b020ca8c38337cb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-g4G9KHMSlYNSPmYq4b0ESEmgXBvL1OL/BhDmpOcXgyQgdfDlbIWHwC033VJfndfLj6uub3qJk0lCZKhLt7dgVQ==", "signatures": [{"sig": "MEQCIFscOdyqunqo9YqwhW/8H3r3TLqJn4j5+ATEW0GtIgdjAiBEjJlpFXnkKNVf3r9Am7RespazUg0PEHOlxxwkh2UvPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GpTCRA9TVsSAnZWagAAoIYP/3tHecuSy+OvNRLZpfvd\n1V6SK7qdRkAjgOiCETiWXyzi0CH4GDeLsC6ZvBO8qN7vBt01lirGbV9NHqEs\nLJaCMShzQCdwZnnA/JnI+b8eSSdGsyUR4XeLreLN+hDL/4rYVTjokkAeGUu6\nnLv22ymCIZwPl2X4EkX6GndO4lW11Hdhe/Dzaorhgg1YRewNTq77Je3kXIWk\n9mgAP4sWGoLzhFjX5mTOmUEuc9D3bje+w5+8a4BWK2+nlnt/EQo99xYFyZab\nqYyd3s55DXEB39IGzBQENSKFwMk6HF8Y/gjgNXbrWGZKx9aS6pkJQl6LwxSs\nXI09M2AZuc2fBxcLVpf5DlvGaORNwbAn5gF98vaHy9JnZDhYXBvfzEI3FvGF\n5Q37MU6IIvWJDGjoceI/x9gMZ4/mYNFT8DnH/IWnjHlbyBkrfj069dpIACYd\nHt3HgXchAVfnRivNFCGJHsRUbVFQ9tHdWrpWJctZ1Crj/Xjx7Q4AedpxME0X\nIQyIAxqZp/hbIefdJmCltcqu8wd7v8rqb0jfq73XmawZJYWy9iunxJAhp/bs\npTxNKBhswmPrAqegywJliTHa2AZsIB9SAenEGpPvkf4BJID8RwmtWTgc219l\nIL/+R/ZniNLKbRUSrYvWC/kMIx77Ey+s9ptpiipYOhLFGnq+wpOQm9av/Zh+\n5dPZ\r\n=czuq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "36469b4f8d1d35cba3dbbdc636602557d0f4f610", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-KyfcaJle9lriEA7lb2fPeET5+xUMFeDjePd3jD7aaGuolJBVzQ0IL5PV0K9KB/kuN+kgRszlAzC52IHTWhtYCw==", "signatures": [{"sig": "MEUCIBW6z9GbusjeRbEoDUJ94QohE7D/bY8/yRdAh8TP8XIEAiEA7z4HVkX3rbMydQUVhBIyavDF6minsaKqy7OdDfvNkY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbZCRA9TVsSAnZWagAABw8QAJokzGgI9RVd3NLXrI6A\niW18yzpIGIeicNIYZFCD6yFBRh9ywbgOYirvs2xnJyBvi1kg9T/DGzXyeswh\nRq0ZyFT/xMKBV2hQk9zxDDb7BXh2r5Ozws/jZRAWvZInEZleW3y3XVUOpiz2\no/P42+amXC8n5ca5UIaY0YCTGkhDwp1OHxYfWEpcpR9OaUSDjsGxomYo/OeJ\nwcahCpJ5kXWqoOGXu4QbUkRy2xrJopf9Z5F4kV1cSVnVMgBCGWizeXew5pV5\nI1vD57vIJlEjh6+BBkIsQs5FZ3/lzzEqX1GcCtQZh/6MEHGgWO4sCQP8LY9d\nDttvq4Ql5aAdCpdOYes2b7ou5s4LhfvYv0nopLKkmoCSNaUS8fNIZkHJoas6\nUyGyq0aOib9vZ1iIVaef5x2DCfD610m2H1Gv83MbhaRHofGAp0ugKNNig/Uq\n13587tm+wEw1F0/kcrmP5gV5afzAnLuh1UOx8vcrLZvOVFlLgulYSSeAshYE\n8N0fdfFerzkASZGOlupvwcuGKTzltTf9wlLytlljorN7iG4lMwZzquy0GWr5\nEr3mVLY3OLwdeeCyI/F4TgFkgQoUvR4zZKC+QQYEfJz83QLJGBuLf59wyhux\ni8FsAX9H1sVo6RxB7lx6R8in20bGEiUruyztbw4vl2B9HgC+6oFsVeU1M2nB\nPc9d\r\n=9voe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "777749cadf8a4edacce71367c922b4adf99a6d14", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-b3F8fin130Jtn1DaNzl/8ghXkGZ69qDjpaXybDRnVlbbzvtVkGtQ4bKulUc+1r0k9EL2JL8MOo7VAaCWFw6bmw==", "signatures": [{"sig": "MEQCIA/x7UcCtMtmbDbnBqdQnT+OgH43/EqM21JcMmRkkKdiAiA2AEHFc3cd+9IU6I0R33hRz0Wiqv/Sr+ax0yTMwsWYEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6Yr0CRA9TVsSAnZWagAAsYoQAKOe2AqBjGPnAk968tPG\nTevttBGzEVBuler5+192HQBHjl28aO7kODDPrxDPHXZ0s+vMVnbtWR93/M12\nP6ug5wtwAqz3LKMTAr0k6HAGXQ4ZrJ1/a8Sfqj2doYCyHgrKoy/F0JwgoZIL\n9lMzhzqCuTstTlxVmSP/5w43ao5WztP9xb5d8xfzYq/XIzdPtiodYeBWmp2g\nOfZribxQWuXs1MANFrKIqfpubnCydUkW1XZe7r+JmpEVwsfxscjt4LxmkEke\nBwLPhWCQ1wWBQR5Ss4Mm1Lfl4E2PppeQDDXZrrxtvQqFaToTeiy1nivFJpNV\nZolPzTyu+k5Shrmy2DuRNAU46+07vDrVD6RlhQIL3HWN1xDnUyVbpqeJeMwC\nuOXl4XYGR/Se70WMcugs8JTZe2UwG2S6BAt4bXv2uA+9y1vxnN8cRvs+yXiI\nKE5AFl5jKKAeVMna6FFbJSnZ2a07USNHgy261YmFu36/wqqHtfzfH7nK+D+1\n5rxI2ww82NI+i0TpQdvBHU7PK+m4H/wgVIIkIBeg2CveWg0lqIZbkMUh9d/L\nzGjz/IXd8YWp3G9hLOfH7LF1oVSPwNKnt2AblxVXnSSn7gYNmxn1GLjXJya5\nr352qsC3/nZUnz3MZA5aLZ1Ab23G2FX7MWbHzSPAehJMCBittdFwzUR15fjN\nbF/d\r\n=NS9J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4a02eb6e3b2054516bce96b758a67d85854d0f0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-jY5UWO747NERF3P+omEXFAK6UHd6cXk5ukKyvl8EpMmeMyVb7oef6xS72JJzdDNjPO2MEm/KedLHogfxT02h4A==", "signatures": [{"sig": "MEUCIDC12YxzZQxKVDzBvS7Jk0Kp9M35eFxpRjyqPMtM7zvFAiEA07EzUeJ98Hh/62peCktqRtkKYKynWQShuXjPIWAtMRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scWCRA9TVsSAnZWagAACKsP/0X6+RMRizEtf175RATV\nMr2l4FChQkRZ8oknTIHbGllChxOoIjCw1We+qyRWS4dOpxQBzA4vUy9p3WcC\npWzDmcQ8oSatDK9PcF73wdsdXd864K1wtCe9FtrvCFadlrNpUqBYL57+TjQn\ntFjQyaRVFCaf6fZZgnsKGchSmcL7Oks2aUbrObriSYlarkYwg8sOW3w47RfA\njTZCZ5Trx9H6AAZGwtJ1eL1faaXO9t0/Suwlvjg4P8oEFHjfh/f4AsQlEFRM\nShPCQ64qcaLCUKIym2Tdq8jNxCRosV7kUaIxLezlFFRomZy9FuotC/H8QEDQ\nYLP5/GItIG8d0VRFy9EkpgaLeCjDPK5IUIQNbOYfgiB6DtBUzzJ64Wvsllbw\nVqkBhp9/g+YPAm+pRq68b4gDVvnP8JV81QzYn+ccgaDxzUFzIGoH1qmcQ6CC\nAVG09hJuwOgDhCOlx985jHN6S8lu5jNEit1r5sf93sgkSmKR7lsI6XBH7RwL\nmwiJTA1phqdhEHbuQPeXnrOBBtxOks5JOkdeozWo2gr0ejV4KQbTHEFYWns0\nE7tIKOenqFDY9Eq0JbvvdDRDbn+j6nzJJxDYBHRSffAQ8vytK6Ymp1Ss7MLb\nzpWTqkm6p6FqDPMdWIfyEPMNlKjCq2skAjz4tsi4vli+b+02boRV8qIt0WzT\nFiWZ\r\n=IhER\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f69b8acdc590a4748a2eb1254e1191ed171a6235", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-AQep6Y98lfVVb5UgIvpZrAFJQCO/jCDK5TuYhZ+OYTh+un0a4nIELPbYdoKS7AFbfrdZtWa9Sx9mXikUNrofWQ==", "signatures": [{"sig": "MEUCIDB5E8ynwoOH84zsjmAFQq9eixiebtI+l6zMlujh0bCdAiEA5jvpBoHczv+k9vtKdRa6B/y+78ocHyo/RabX+Ll/n3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCZCRA9TVsSAnZWagAAsNAP/2A3/nWDLElrFeADC13l\nzM6tAiXaZkvQ5b2k+Xjk6hcjVCf0oTB207XyzHcUa5iazvQ/Pay2BboUsHIk\nxPlV0nuiKsI5aVrSEE1n1wfBQ1KzxL6vyzJbtCmgzfx7Tf/nByWHPdeZ1FWE\nL+c9RbItHgUQ2FkUnXzU7ak01JtBSwGidLS7EhdPB5J4llowI7Bj9EhXDE7j\nXg00cFyH6FKdXQQOew2GA7wuGVcfZRGcJUricnUClXb+aczMjDo3pw7bvmO7\nIL1w7y+wCLuhkky7E4SCRImrI/D01w6CEvatOjmusxBK5JGaKY0ueHobcbmQ\nDEoRNguy9ed1EvuPx6Zf5bG0t76hcwv3Tp1OcNr7x8y3PbU0u+0ycrzUCHVa\n8OWCg7/fM/sWyaAPVTUqeFqfQiVMJ8D9dJXqxhrkbz7cOlnLuqPAx7eHApJN\na3BDo9/nKkIrKC6w7EO24otf4ryQqHSEr7MPL3FmBoQfuzbJk8MVrcVxODUa\nMCI2Vy/badoqWVZsVM3GGtuYo5JOIy/vUm8hpSSos6CogxPsJoJvfuJTk2cA\nkrMZtIOpU4FUOuhEPHk3AnSKu+s8EV8LfHRRByKDjaKsotsMyH3nbfEiHQ/G\nqBqGSNb2cltib+lpG5h5rgXlf5DbBQnEPh5u8LLTCJOmro4jdKdYWufE+EuH\njGo/\r\n=wBXi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fd6bad970cac1c148a8a987c86a7e6f90db165a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Iw/kgMT/TazAfCp+RlgaHhImOuytNKRIL0lsYihEysaXG74xiN68RPG24E+5dix8Qk3wI8srLIkcpqjhW3p0Tw==", "signatures": [{"sig": "MEQCIF4JwkOXNCED+JJ5IzHUnNEXNgOsQdGEj/pySR5D/qZ9AiA5PFtfroRIbcn45xM2mkckFX7u59FcG0VqqK0xlTjrXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xJ8CRA9TVsSAnZWagAAZrEQAIDq/Zeuz+oLUiKoFqWt\n+jfaloMdcDb+wqr31HHwGi6vUx01heg7BJONrL3leffCcDrXNv3tFW7rVICc\ny9TwbG0ZynzTUdb37i3WWGXQ06zzwyQ1j3q/EddoWA53fwObpbcnK9pIscx7\nP+c8KadIy7USyB5EzCBKZImsbkugFgT62lwZ3O3bnfraQ6TW4Xsx4rL/dx20\nViSCQu43kgjBQ+M9U5y1ipjhd5mR7dJUTRn0EoHvEKnny+oSpuTDPAyte70k\nV0SnAESMHRVGEFUbgWDt8UrWIxqogAGmOoOt2GcgalbDjI28Etnkz4JGZpJL\nDKFdXmOpGjkGXGj+ATauFA9w8Uxaf0BXbKSmHyhlsBAkDZ8QdhXy6bGY3Eid\n3pU9Q3loFQCPSbOK84FXl2qmvPgYjoW21/QhEXC14nk1eJw+Fn06ocV0z/Ak\nJjkwtPRBF8d3KWReTqPFhahwXJwMU/PBIVgehp5s2b1G5/1FibCM8eq4MXhK\n3PxLXNQoChBBSwSTQYljrBmmJatewqri+bRTnHJQ6/uAt7WpQQNBc4OTL+z7\nUROVRZ1QtcrTEEp3EQ4Bh13IK2zjx7su5VvlnokMrCp8XxdbLqBTw+8Nycm6\nr3jl/lzdrNf7wiT7QJbqHJkgLo8jRt+MQwCli70AylwHKcbh0gaauXSKcoGe\n1+V8\r\n=Ae40\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.2", "@radix-ui/react-use-size": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0c9ff0fd1fd563a3b30d9ad08cda9b9cd90e443d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-MTKuqipFmZILRiHlv4Ju3kKy+GIs4gPMudSO2GgczML8Vu1cLZFmpVny1dVM6ledmGqKOzLcacozju1GYqSzdQ==", "signatures": [{"sig": "MEUCIQD/SM7POa29plwOG+dwZCg1qkR+6idQyS3ejltK/sb+EgIgBL332WKOs9EcQurUtTGPfq3mxoTmrTXeIi26Bq7PiZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8Dy9CRA9TVsSAnZWagAAp7IP/2ix6B7RkeVK1W6c0Jms\nEQbPnouzIDg3L5Jf0KSxNDv5orv9SInfq5b8jq3uKjvA5unKp7H3rYF5bnpJ\nD7U5TN+Z1pBErvU8h+iPmolhf01o9faZZHNZ1s009ligtTht2Z83CAKq3BRQ\nSBR2TO7RnI1qik0pkhp3b6LIzrcu2Yy7k55EupR+Lwl3fk73cQPT8g5pXb42\nT9j4T8maDRlXKd5ni2aBbShj+ifVjZfwHzB2LlWxFqnEQGwy0LlQiNM2bdaF\niXtzhJU2sU5YvGepdti8QTcPfEgIxfgRhC0QLKAS70HVoAYeDvcfti2GYWB4\nNONIbY1Tcjlt3dcG0bLTPDtJUn77wdpLuWfILivMKRoXUqfKlRdLilRXT33x\nAW48ZS2wONQCRaGEsGaO25LrlCJlg2jMmdfbznwby5lLYKGnlaCmlC7YHgdh\nlJdVpUMgks49N1oKsykrUH+C8JyYMEsILfw3gQoB27gkvY3BK2x6BdCrEyTi\n3ymFiJ+KahwrDUuZj/O21K+ukHUhrCzzIbtG6H+7GAmq7y+kC3Y2yXlRtdyK\nj6N5ySpwRjqG9jHgBp88m+TC87IQFiAzTGbB5ENnV1vpUbs3NvnStLgEfdRE\nVyJIj3meTgbCq44YmzJlyRPO2HqWH2S0iQTjKWCfVZesRpa5XIB8TuBF9dO4\nihT0\r\n=KH/g\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.3", "@radix-ui/react-use-size": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "90855b7a0c67fe1a8a2d337316d7523b6bbf5d45", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-WIah353Od3XHZmMJefcocNQhUA4xVOga5pxvBEVfWauxGShvvHqIj7g5VsI32hrhrYjqprKt0kEGR6eqZ69WDA==", "signatures": [{"sig": "MEUCIQDYJfz2nYhuofG18rNSyXde2cYkLZj0ZzY3pHgA6WBrLAIgE49J85Z6eNUoOg+LDpgaeyTEeYBhjJQ2h+CYuEmor3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SR9CRA9TVsSAnZWagAAe1oP+wSjYb5N8iaHrpAehRlG\n5QNk1keC4oqy16j3BZqg/xK/8PoRQEM+hT61adHt1carKfogqfsDtnPZq3io\nAooj4HOx4YGFD5i63am4LptbOHfBcdalSgHPpkS/Bi/qmPmVGHKOIqhpjC2w\nF5S54HVk6pW8rzH9nCZblizgwfy5/cNJdafq5ox6SmwHvXzg/o7dU1lCWK/m\n6OL8VGBAlM8XxBCSHjWlwAmuPTCvDRthBYAyAU8BLn1fAQ1b5aUJocoPvmZo\nek/qqdPafavkb0AnCIVadKgGEjzPQ9jM//5EsiDIPmkoMNfuyjMQGcqyo53K\nsMNENtfv8fOyImJP8UXyMgPPlSztiS0zC2Hu9K6frEMahLASxV4XgTuvlr9l\nPZVHDQJhlmnHmdoi0olb/b//9644Bz5MRKaQXiVsRFAVqqZcRpBExL9sxDDO\nTxyaiME+FCUjfM+LcyPgFajSk5uo/zdURMq686NOD9pBG1gwuiibH4KZ6AyO\n+bJYtScJ9XyYTtHOcX0I7Nt0KncwEPfXcUiYAChwlfgfVBgdkfkqz27/1d20\nH0f26VzSmX7GSHAL8UuTV4XaecvCUfTF0ZmtzMDsgNqZyDzDHpe9f34osEK+\nnqkkRD8upInsk6txhrZXnvU98FeHYe+2XcKqKV2+FrobdpkS30W1a+xtc4Ka\nJRZH\r\n=sVg/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.4", "@radix-ui/react-use-size": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ab36b332c3d48f543e4d1a19903b216a7d39f5d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-ktwxlYjZH8kOmkL0y8xxYeM8YqJ3lVnv5YGalQTlU3TtnYWs9aNGKTxKBptIaJLZdHYyBLrWaIqYEoDxXsx7QQ==", "signatures": [{"sig": "MEUCIG31wEgwpMXT8PwAofz6bK0LZ4d6Ztd587+bovAI2Tv5AiEAgzVFNDK7KuZIYYCXbOAxockX1FUgaDCKesNUwUDkiAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DY5CRA9TVsSAnZWagAArkIP/jchxqYtnpinPtd9e//k\nB6ppZUP/rvEJXbQsltfUnmAfmg4lkftQB7+fwD9xyrB1bcU1yqi9PlRlfmrg\nWLUTvqt44wvVQiKWBXCqGcXB3tJuFrTeGYoCrhgl3a2P0gGVf+Q4/FtdoSXf\n94TjY5Cp9aiJGKcqfrsWZO6ePZyAysXSbJvUnzhIBnWb8ozxAqLRd0NOo+ih\nDGt62pgtUsLEksbWiPW9mK14gwzp8ygvWEt/w9OFFINy09a9Occ3s+X7UH4V\nl5ANCIPCjggacjY83T5ZTVOUUADSgI7Zx39UCF8hDi2BtOGEA2IOA3DunupJ\n9mTJ58Mal6Co9fEQAOvMIJyC5GeI7fCnWt7y8QIDpqvoNVLMpxr51LLKtVG3\nakVZ1CcbTabCO/mEU6l+Na4f1p9TirrPLMU35qZeBwoS+M6Fl6xWWXsntsze\n9WwxbP10s2Zm+jI4/FhA6vG8J6tFM+RiwuwuNA6ic4Y9JkZ93G5eEviViAXJ\nyoFIcTfHJC+F5lz/IT0IqpFEvcr8cAxtToAnITmxz6kZzfshNuN5f6aBbJKf\nbu0Qo6RxYIFJk/BxY2EWwlbkUeilwyW18hzGbfhCnrfqDeLvOxff6BbLaDQ0\nsIZPxhmCKOp95+kADbPt4KohIZfLQ/YKv0f4soyL4SGUaHGIdEu4W4pBZ7zT\n3N/j\r\n=Y+is\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.5", "@radix-ui/react-use-size": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6f3730eaa5a1817dc9799a84b2de897f50cbb0b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-H9/pFqnfLBLEABG0BUTPlmWcCkD2BDB2VDPr15iMv3wdkD5/wGrTy2a4g+R41UWFeWd5qk9bCB6KJeo/GXHuZw==", "signatures": [{"sig": "MEUCIExkvG4zYxc0Zn4Bq9JEIo61DyMu0tV6llJe9SkefxP8AiEA982GnaAmjjeqfqXR9DKx1CD/pcHAmg/9nmFgIxPlNI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wm7CRA9TVsSAnZWagAA/ysQAJmkqkvO0lh0i2K6csEK\nctBrEWbZWo3B4YI40kfO715eqqw5RaiXobz6BZyvriy2HKWcOSo9enuJX4Xh\nRUIRcflSY3JrTmASlrLh27Rf3GHOfwm842Rx8g2VzD2YxZkxd8Qc+gNABGPL\nNgLDa2TDdsLMyDu065Ze63IvRfI4YdzG4WGGA9GMQiEgG0exMqVo/IYZ1982\n+5jVLxYWmByKXtV4Mg5OJ1ZP3q2Dow7bwLDc5kOztE3+bOL8U9ubVybDl1Ig\n8zY5TmYY1Y35nf7abPz9m05+rO1d4yXWiYsgz9fy0RhaQNErZFClA/WIvBmi\nqAMbtUmalTErXjxWtESyo36Zk7knFwrmrggdFL0wZ5IMSVkP/ZXAoEyctNPb\n44Mhs3gRmfs3SkffRZ2t+ZsmHxEksG0TL3HeNpL+u2uq0zuN1pahGzBgkPZL\njraTn1jq/4weLZ+ItTAD5HhXX2IU/T+/HyzZzlNOR/+TO2m4UTOsQrRF51qO\nscjX3qSDmW3m5kVa32LsShSF72TWt56HOLIS9aSz9bdOoZJ4/habU4Lz43bm\nBPc5hKiWtT8CkTwZBT/GkMXZSJcFOAdzoJcrcWasmx6fJI3PdDqfI+IVppVT\ntFV7CqdNexS3/ecZHArXVusjajHBWB0Z3EsKw4wPhtZvziJ8CjedNJwpt58B\nOERQ\r\n=yYWE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.6", "@radix-ui/react-use-size": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eb645a3612eea161b7f3cad37655c9a2353310ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-m1ws0JwbQV6lkdgUErLT3hYohIE3YKAl4xp65MF2iFehPnGpAwjQYpmVA1VA7HxZ6ppjOzHPGvvSyFrsk+5ssQ==", "signatures": [{"sig": "MEUCIQCM5rP9dOu8lmncU5MhZX6/vlbV6Rt1ZIY1CE/X6IhBOQIgBPLM7vBflPAasvXv5DkNw7zzam1EdWsyDLuFs1jqLyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTVCRA9TVsSAnZWagAA2skP/11IhNW9G+COE5qoReyf\nc8SfA6BtgHFLmASH1Hi+yoOpB2y8gi+WfqdBoyoVHZ2Lao0IjIY+lyWdjSqn\n/xB4OBhkT5xlrCyLdScRcnndSWbK03pqYIKF8yDvu2Koe5efsxlQWPvw2Paz\nowRTSRWjrvflhdJep2KOQ3OWWKda7tLDZKcft5uE4la/+S2JNpZ+R920qWn6\nS7ntlSxn6zUeGV3HJXLwiS4ioama6JqMWcpfjMxCAz6vZ9bR8jI1YmqkHEl/\nax3tXyWJNDcmEiIADMnnvooLFoGNL64TcBwt1857Ho5J+ezuec3sDEEEeFYC\nakuu/Z2+RBJkADP5OiIBNvTxEIxJ2CfTgEaZ3INb9jARWGB6o0/mupUITS2z\nlZB167OTpEfzz8mCFOGNESrk1zxN7knOv8MYvrMjOhSLhuT2PpGUmMEM37yE\nJr/IdmsBwrShm+iN8CVb++DUMbNKk5v8BWctlhLu/uNrpVYHPYP2DLgd8UPb\nhGqf2OAFDNoUwOlPxJlmtCyzliiDkkaXIYbRzDd3sfJg4QDIYr54ad1W/U5E\nnStHqsfx8I3dNJXZvN+6bMjwuKUFRDvZnYXTZKf5wlRlsCVi31h4ZYQn0IH7\nUrZuXY7OuMTVuQGaxItxml14SgizQsp2H2GB94HJAjoYbhisMCR74OTiC+wt\nMNEL\r\n=5MgN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.7", "@radix-ui/react-use-size": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "357b10f3e59d4730bf08c40922999739d8724e08", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-7FRdfn272wk23YvhDCGAIG7OkQRrSweCTafK6sMl364Z6pL0sFY7B85EmCMPUtsT6ZRMbYfvJE4hxaLOG3P64w==", "signatures": [{"sig": "MEQCIHPqrCYp6x2CrGq4yeuQugDjT4BwRhjl8kLS9KHFlkBwAiBRC2d29bgiBRmum8SfNULGQDglAw0QYlFJ1ACvCl4Zcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/mdCRA9TVsSAnZWagAAncsP/0NL7ORyMm21IxCSFNgi\n7PWDsskmn95iRM9cMzfD3vD5ZgtvaFM7VnCczqmd/+Avibj9VNhjRz3nFkYL\nx6u49Nuhu3Rd3z5Q5DYdhzxoiDSUchEzLn4F8Kb16xo56Jx7Kbpue3nuF7cX\n4OcBlOPNHZu4L0cAGXV2B+ssNasIoj4HyBSJLyiWMrr5RdmM4qOX5jxC7KA/\nIj0xs8dcGWcVpSsqUAUz0yV0V6SYDkgg/Nkoi7gVxegVZR5jpHEMivLB11Jd\n4zvGnIomjvZ6fG+4txmYdwRtVGS1IKBRtyPWlTd9roLxx4q0lpuIUSWNpq6D\n5UX2Ww3eUzTWvrbJf5sm8pIGbv/iusU8ZY5hU1P2sTu0kY6l/1MfO8dZ6KpO\nv9nF410+Auts0JqnYwOhzQzhqXiUwvE5qS/uan+oUUpCkhHe1edhvePMj2Ad\nZU7zd7CvO4X35EfCRSVlfuhc3a63ktZbZSjATgt1qJTKqGxwcYOjDrvbWgVD\nikTwDFbnAhXZvQX11w5Nh6yos0pbBesMN6MOuNfxTsLLS0dZxL2rWIyyApB6\nIb1ksQ7x9L0s4OnRZM9YjGHMdHIG57x7MsOOkNoDIDDZyXlpigDT9cCKOikv\nVKRdDHosDa7hcIMiixbhulN/Bbo1yjO6ZZ7CGURZr2/0XJ//XvoDgY9USW75\nbf2o\r\n=iHcW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.8", "@radix-ui/react-use-size": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "34248469b8ec9bc4dc88ff463ac9d2fbce6cdf9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-hZOFiedmYkTweiCmdzVrY7OStYcmk+M/7SMkJygEPdqgPsoWwLgMWYQAQ4H082qCj64T26KuQUhTswOz8LjOpQ==", "signatures": [{"sig": "MEUCIQCOICQUyBLW5eHCkK35YSRJGiaCx/Z621EBXN/cqmStRQIgDwFxYKgal/XZmq/eQetpS98Fua7aECG3wodkScb3/5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBG3CRA9TVsSAnZWagAA7TgQAIzO2pw8SttiGSvvUL5z\nBzvTogeM3SzlJfEK/OtgZIkImETzdbDpSv9CuQ04SOkFWDrWAVa8pVjf/B6w\nQidLSEGj+9yp8QlIr2ivcs6ZsetgoqyIi2z8NbLYfGktRNOSYgwxei1yF1aJ\nqvZxVGzcE6t8HCXcGAqzqTS8Gys1DdMnplbKXC6YBvatIf4qi4Yb3/Zl8w/X\n7kzHV/cJ/QvqE+xecnzKiyopG0ofL8SOnD+xjnLpAk2YrNpyV2F5mgKkEqDo\nWwgrCBK5OuACdpijIe3IvRJFwqXdoPzHavm9nFAT54FLoQveX8j6HtRcI9y8\nzC7j9qRv/yJKZqv/qv5EHHSoRNAhh9uS5G+3yBuHlRuQIH1ntraeTlag/gv4\nrt3CGaxu2b7b5fErxOTd0UHNYvHbUjpdnHV1ji+inuie+N8Qj6q7pdcSTrcw\nmaE5irSM8yXWyM0/CfTIuewbywOOD5BjJT79zMIsf1obvvG3HcHnPq0Qj3Mv\nkLwYDDRLwrVW1SkyM33O8lyn9HpD+i1vP/w+5ncxLeDGFoXiL9IyxlmY5KyZ\ndBwa0CmIKtVIZk0vj6Zk9p39Zj/OTN/VLMnVUGmeCrQ+PuMXJ3Dtuu2+R3HH\n7qdaRSR9WUBt3dPcfgf0nCwDxD87ogtr2Qtv7nIMopO+t0CVBLIGB8ZzAbrL\nWi/h\r\n=Qwkv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.9", "@radix-ui/react-use-size": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3b352f7d9f0405c5ac0f2712fd3e7933152a5eb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-31sVfSoY4tJ1nJU16rcXyshgK3PySSBwZO7bws+pYByphlsZuOpAeIPoWVSNuxrY0XZTjDryYsAXhDfJyV2Byw==", "signatures": [{"sig": "MEUCIHmfY2gOXxJCC68TO+H2qz3fX2HAjg1tfwCbrpP49O3AAiEAxDG4QzetOGwamWpy3rPGjimnh4wsDteYFXkKcKWxRIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBW+CRA9TVsSAnZWagAA4dUP/iAQc+0LQE07zxzEm8nr\nrydUHUgyVj/KTHAPzljWFK41RQD0ranoc/7Ok1nMPIXJ52HzgZwBbwMo95d9\nGAV6bz+g8QHlGDQ5pVF2e1FrAfFbkgfRamr0I8EiLwrR+I7TEE2trilpKLYa\nbK3d9mfetzHViNPpRpT0xoWDfnrfafKzarFxWDXLXxUvyOTWi0ugbu9nw9Qt\nhi8ekoF4Z5oyuQwNPJ6Q4EMFKqPZ45WFPEHmPzSstrXnm865Wxb/8v47ZkYG\nz1D2T1EPubV/7JDA81Vm3/v+zvJkzIfY7hGe74l40BnE2JGxCw+WNEXXcaD0\nGl0k8x0++2Plh3dfDu1Gylc92CaTIhG00YD53sCN6o6KrLJe4vrZxfghCcw3\n0AFGhOfL1vmh2qaarHlYVftPrq+J5wQEgYa4qNtoc+sqWRu1IQl+e0Ih31Ry\nFqC8g1LL2m3PJLBX0mYNqZeJmxlW4dHOJPzrYCr1Me3ZAUDy1tatjvgS8SJa\noKZxwGafFZ8LKKP7YjdrRFOoRcRVr1RV48bf52U0TnPMGY4om0i9S//tmqQd\nU7eKue1/igcm3TNHbBmaZAX1Sz0ISMC1SNXLj3Gp6CtlFI6TFlDBo/VQ1EJR\nvNRNzgS1Y4T2C1etSDawDxvj+KNGcLiQDwBKZ4olw4JA29ky/DzybeJlmdEk\ndREt\r\n=CuCB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.10", "@radix-ui/react-use-size": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4c8df682da56e7f444ce1bb54d76cecd5f8d3f8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-KIVbNTUnzg6eDmxW0sFnx1RvqcoO29VcGuGdDvMSPsBHwjwm/R7HJ1uSmFRTK9+Sp8Tt72EnprnmkLGXqbe/BA==", "signatures": [{"sig": "MEYCIQCGlUsCr+JxSfEplbsfYAu433uxGT8CYmenZh31RUFYxgIhAJHSSaBMkdw4V2t9UW0ffcFjDxwIZKqLljQ+vlDroad8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlkwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/LhAAnNEfraujKHDU+hes7yFrQojnBoD676m9oQIXNZIY3JLHIVwL\r\nPWl/v72gstPTYnwFad5DxY3EzdTj1naE0v26TAYwNz4INcyd9pFU5NeEegjF\r\ncNhPz4mvsbChz/nzqHTY79GiELC0e0xBgoFsClZQyMqR0JYpqccpT+RPoQeR\r\n4Hde0d0Uq3bkAor7LueWSqMNKdZD5z+9dhEhlaMVJ8sNERKekJqqTfHejLGK\r\no/5aDC62DlZRXK6cZDJmtrWQcz9xwDlVuc71jX8wRDa+w/XN8yhQfGgf1yG4\r\nFvQoziNz0WfluEHRGK2Rf+X7OFLmiLTShF2gAlp/Kykqr2AzcS6qRBqeHayF\r\nC743cEt1WMM6jzhGe7CzM+8rcz5H3hQyY9IVUXtP53FO8BIs5lDScfkpuGuy\r\nGDiJLVfobQEe4p8/X5BOFzcHg/yrVghy7cH8+cj6LS1LUNQg2mb1d9EQpvex\r\nmwgl5nGLj1ywXzEZIydvBPaOiVKV42jU5P5MWSIXh7IgxRzjC2oKBdaBMMCW\r\nSt2SfUz2H/p+XjfMNljqJuHQBKA8qmv/0fyjkQltxk/HZbDqFfD+EyhIjCFL\r\nH4grygNuBNhxwwlhPdq1/BjJeXL3+rQXIyq3n4Xv9unO2W/D5erY6+Qwflty\r\nBdxEKrKGMrsMx0HBeRuYkFoYg+bwbzTm1sg=\r\n=Jiw/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.11", "@radix-ui/react-use-size": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f3bc593dc6cb3e35a2b5c8747dfcbdb10d29efc4", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-yKiG1Lcyy3wJUIYZquTjm4zziXQS0D3uq4L/wMCn4GU4bO/XRRbdF/S8mDDNgXlBdL76M6Qah6Izg0g2c7Lirg==", "signatures": [{"sig": "MEUCIQCUhumtq3mzHKiLNXAOq6GwO+4r+SyomEGq2F4ynGM06AIgIFqsSZ6HdpK2zktSqo2uyf0lO4yKRq1rCkuaOhJKbPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkURACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl3Q//WlauhqnAkGMbsI/bkkGtWTP5XJGiyvF97NnEFdAv1CNFecG2\r\nooM+AXun8pSvv7sk5gR4e42yZLwd/PoQj+pvoXUOhcJCKpkjYGHQ4SHgElVt\r\nPq7b9jNfldXkjpxSCv7v0MuIkLg3Xpl4b/Pu9keKudFz+bTJDwmeea35susd\r\n3KJm6nGOF77Ds9dcieT575710xo5iNHVGzkLi/eli4pDbVYRxq49kTMo2Lbr\r\nyEOjwEY3e1PLWdHn31AYptgke7CV0w0SASE0pGP3dtfoj6ZZC8EAzCG1egeW\r\nObwjLO0AK6yrCX4mD+/czEEB482XHMbJNa2SHDWXp9zBdiS0SRsDOEiVNodO\r\n/goW6FJbUyow7zUR16O3G3C+HnmFg2L2gnc/rso1X8pPMJ1hs8J1HmMhbOR5\r\noiwAvA11JRrsfKM+Nsj9uYAYJkJGgm48zRKIijS9rC2/GYVaoZT0Xf9Y5yIm\r\n23q0CdJZoMtFUlo8F27tnt4x9oViewY/mot5YqBQlWEHMKLLow3kPhpvg1HP\r\nQked1bLhFatdtYn1wFFB8b49RfxPYnMxb0JPvDib6hCpUQ86IWod0CsMKJyn\r\nV41gUiqNCfRdd8xeSwWmWCe2syR6OY4n3WZ9ZNAw7Y6Xe5ug9cewpXeUdXE9\r\nhioddXSNSQ6QANZEjwyB7EaNYghc4cPAgXc=\r\n=6GrP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.12", "@radix-ui/react-use-size": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a6f0ad92d33bb925ace68842757ed151e2ce1967", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-rgf3vpxnEeaw8C7YMf34ShVru4muQN4dUjnQOiXZ0oBAAwJA/7K3DesEAbpb95voxW38m18tjGTilWspNONRWA==", "signatures": [{"sig": "MEYCIQCSDypxUqvnd+lA/ZYLEdDqlWT166MW9To9mDp6bD2jlQIhAIgkJpelchB+4CbxMQE4hPn58vx3VRYumRAULMUCN+MY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3IQ//Qzz4r0zTvLLoqAccA5BOx14W7879fxRjN5Peo3W2ieOaaT7f\r\nOyPM2Q4T6+hGCtJAvG8RLD3rvjqs5XNWlJlHtHvT/2FVQmXByG3PI1Rjxyf3\r\nm0MJ+zcMRkuQSPxDZz5UWwrdxLmqmioNWU5wJXczz2YIrOYZHWOCwBznJOn0\r\njBZfgX7L39DPbXLsqm9ucNTO2wj4TiywS4xLI69Ki3ZzOTaOP/FiViHBGpr1\r\ncP9YInk+3WnICCAX4t31QAOHuC2kypsL2tJw7d64BY+dWtLGkityi3as4eVr\r\ni+YD6jXIRmaZla79YMVT94nsZBdujO9aoqGZjeooiLGFHcdf/dEG7zwaxQM3\r\nzy7vgjjwR/rIpeo/UesLhN/SWd0b8gicCBwnpJU8Z0//xCmslWUeFErINEtZ\r\nO4RLs2RawJHGUQCK/ExlSdTUciSpI5h4AMKGP6uOly9WLIG4hU8jvlwYEA/N\r\nV6z0ZUEFWbMdyGfuowv9VGdId5tbQx4ZEqkV0fTMOK9rXVTQC+0Vsb5vUZkD\r\nJieRThftz+ZQ9kiovEO+kNzcJiYUPwwv1FxS2JCDhHXfkbwTFqHGongywVzs\r\nTijYhHWfg67LS7pn0SZ9tlZm2hsJsE+jDVobnQCBWNzoUENBSLWSErn7oBBQ\r\naTNxkKU+FfTl+Z24BQttyOTIMT7gbMPzjzk=\r\n=40vC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.13", "@radix-ui/react-use-size": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "159c8d30144e8bd17beb10a07c41c68b80cec0e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-0CYX5H6g/wsA5Fj6cv75h5ZspDhqvCGYkXDRFLjvV1WsIFiDiqumfGVXdNwwImoq+I6mvhyMkozWthlEwEGibg==", "signatures": [{"sig": "MEUCIQCMRqCOSkMF6ollm8l4Lhp9/Tgy7xU71S3Q9qq29jCR6QIgVlfoYshHCCT7AN7zQ5Ah+ebr7IogpOgxITDqvnp9tyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkypACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPPw/8C0RliUYHABfDt03GS1LxbupWc8aBtNpryKMQYwSNt68v3U13\r\nQObNme8dwPZJRGffKEZImt619OUkKrGc9Gr6lfFPb++0FElNDsKPjsbMeP42\r\n9zU09/hVSziUqaDo1BKt3++mc+cJZnYDi6BZkSMg14GGcYMMmubyWSGVo853\r\n18gIQZkZokBlo+bx07AHNR3uBRsWx3k3mWNXfT4BhFAmtB11cZiQsEZpF2wJ\r\ne/3cErWhbJphbXFkTLz582m+L+amRuHopC5BUpgU95S482tGemhyEf+HVSQo\r\nWQCkVgjyt/Sm9n/gVyoG46bses2JWyKtUhzqxtrqV5pju0w2nzfAEtucDHfY\r\nsYUofw1Iz51jiuB9sLVBOU1wplfoKCekBWuyI/8cteDnSd5EI/x6d9Ouv0N0\r\nj3kebe4P1ftSPSWEWxwKn3V0oNiqPEFTyOTY2kk/kedMsOOegov9faf31wRV\r\n+7dKFAe925yJJY8yv/8Mwrt40GsjBkDHhZwPdOj3vL3RpXPnp26bkzBllpUq\r\nYjaBAAjp7jm9fSipB1/J0kjwES6yCOaJTOXrUGTBnOXNT78NVFlGWLrO/wqc\r\nFY/ekw/CxmEPgiYAu6FzsPjLo7WkUGsG/MCNfkX2ZsQ0DD+cO5ezD5PpflNK\r\nhXNeJs08BUEfHhJifq3pqVwD6X4jJawUkk0=\r\n=gwgg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.14", "@radix-ui/react-use-size": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c6cae4df6c84a5a40d781619245271b6946e31d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-3DU7xERMTxdmd7mU1i18TcgDJD9n0UXPpdAELIKfB2QG6L2An0hToIyKlXavDHtVzt8Blm/DyJ5eedQkhvy28w==", "signatures": [{"sig": "MEQCIEhRj1ZHu24VkH8f7e/Ng919kbKFfOfADXD4hxm/YlnuAiBMV49XTNU7g+v0syaWektX8ao3iYK7DfkPRSjyGYwxJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgVA//ZrMnL6g8dedAVo/i/vcUlLbMJy1FQ6jM03oT6w9SSvDhj6yP\r\nOXyzB96rZbLzf4dxyYCl+hzyOsYdqdFljJUbFWgku1ul50rtyI5qVzcmDeYe\r\nprWpJGk3uTk1wdY+lLgan+1TYT3ivkotd0KS38XliJ/0DpdQrq8eWp6VI/i8\r\nKwuoMA5V5dJ4f8KSv5K+vXkl0ZREgzD8H0nVg5QtBOAG7UMyfRf9iL8cHr96\r\nY6gYkbefZwv+jgGTFbPDABS82yPqZgsTrqTDFUACuZnkBHtAto6Sz+d7o2EQ\r\nzkLue8ENVOA2/hUuocNFFJ2RqfNcpOdVkGCImcIENxgSYNmCTlNxMYgendaV\r\n7oBd3zpYSZZv1gLiEkPiziR7Jwc9QwFaRmjHmw/7OXbhaH9rRfs7ASLiGeEc\r\nm1IJOttjOe1GX0Ka9LSU4xWnkgGHsB+NQPyR6w2YLMRrjceG0ijyf9tNjF3c\r\nt/FMgKuRAlFSV1vYZJ4j6myNSZoNHDK620AQpdnImejpCvI4Ggh3nS0TSf5F\r\ntTEBuASlenJIGDBxtxTvtJPe4LwKz2nb/exB5piorXf73Y4v3DJQ0GbP0do7\r\nb481XHKHVb5PNGTHPGqhI6Ma4A4rMr038hEsS0BDZHvrvHVpb9zpSirPUdFg\r\nSEEng0Mde8XJcrdbIjyLyGGqNr+RKFPP7WE=\r\n=1eyS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.15", "@radix-ui/react-use-size": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0387486bf0be31859349fd9dd60898e204e44fdb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-n7TnG/QxcEB13LBxx8ooopHCB0dnPw680efhJL9U/XNcdocDRgLOQn4RHlio3mf8gLSMRcToLQXEM1YM6CztwA==", "signatures": [{"sig": "MEYCIQCzo8sGiyV3SzKNzKW0ojlfWveidsvlc3GKMlOcy2bb9AIhAPcDCRcRCtjJdp1R2r+u+DEKY/PmY/WymOHFK0UMZUbM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotJw/+NoLSyA1pNywaIkxgO8YkxqNWkneZ4FqlTAE2YKN7w7dtlcEU\r\nukya/0J/1bgvS7tUM1Hy22smYrcBNva8u3ZF6e6oG59Hguvm1plG8YvM//ma\r\nFzvonqlxtoNqtx48ycl4GQksYfFhMkVcXd7KOB+jFClOcwriZD6RjG6Mh7Bh\r\nGx8fRmVUDcjIpgrgeQrqq/nJOImhFAgxHqvrxVezpDPRHCrkbwZ1Wf1UCZr2\r\n3OMm6OgI1szIp0Z9vRINz0n3m+QkwhHLGqE0JNP04Xu1ed+zitKqkf8R8MYZ\r\n0zhfzmxatT3nwJIKqRoIqe133tznxNUomhlW5pUSGEanC9vf6Lfuyu5HBIHA\r\nGdB82r3cL+qV+wfrQMZfX5jvdce8XWLsevboHQB+eXxXacsJj2O6WnEAHPIx\r\novZGqQqaH9vs4mlhuLsAKzMtxAoPPN+ET5t9tFtD1NVrYVw/aL4/algrHVLp\r\n3RLudXCJxY3aBh6gSGu6S+3q6NyUhe2yFiwnnHq59rxBHT2uCOo/0H8tS9jn\r\nWHNCV2pGEy+t3X3vPcJeItozOUTjjWZMTHVHPslGm4VajHeu2iTKF2G0x3Cn\r\njVCCEYM40hUWiAk8LW+Kz2v4gnsdujRFgKW+BhdYHkdAQ1woVCxjZ3bZshv/\r\nyg7dUuosKo5K1zJ+bCQuFPbE4Bmsdv4g1M8=\r\n=EOJR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.16", "@radix-ui/react-use-size": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "611fce020404044d21853a19168169e15b450ada", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-Hnv1YFNzI43U+xbuswG5PWqyrf316oqPG5nL73lUx+nQj+KgO80gMdXzGqL62wHOLuYowSS8d2IRzxOGkNQjRw==", "signatures": [{"sig": "MEQCIE3fVNyqz1dkXFgc6T02SKrA3TO600rPzKOUCZwwRGTYAiB7zAeGAGqlANP7PAvJaMj+MMrmPPm2Jh7PUH/e8oKs9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqARQ/+Nu7W9QyJ58WwkKE/dhf1K4LTMKtq+iF1qtYJvYaz2a9dmYVy\r\nlnWpFYtcArOzEU4XWElTs39O5NIF9MPXV7qyBRNiSYg/yv/dTP+h5sUlG+uJ\r\nom28BRDYyKq4wGIMOwc/TWjVNCfOoFwey/U2fKTJPluAPnvatfSI2NwhwiEY\r\nnIfNFLOb3yJM8OZ+VY55Yrm/zZ5tGNsOnPuVq+zNU4kRAKi2eM6ippkT5wNx\r\nlZ4Q2zt5NltZ9wlPRTHEgFxmUj5/Nd/975xAdfT/CKQmwwBftAuS5LLGdFUD\r\ns5CS2UfiO2X9mZdVP+aHSEFqFgwkU5iCvBB2VUacaOuEsdoMLdBVQdBS6CHf\r\nNZJ7kWPt1TiXk9a8dknVsHdMYbv79RVaaN37HWfxTOrQ4W/BIjU9RpEd/gs0\r\nz+U2ZcdbopnbI0U0NugJ1d/sCWFRaI4YAQoFdl01UAwqFdOz4HMQQ3xDsKKo\r\neiegiJuUM62tmu20+JBoCzy68gj2HgHQ8OC8Z0uWsBMM6maOaqkrcyXFB98p\r\nV1AgrAFHiUwAVjZy0QeHmMpVQvjLd3Ioq2nSr9oYEHuQjGO2E4VyZqkuYfxv\r\nsHY1z6jZs/5L+uQFJ4Px8lz9Pf9kVFGEh773QWyWEoUl1nrDDRtLOWnwlqLz\r\nj+YPqD3RsYst58tGdMsO8fOFjEDnKtuu6uw=\r\n=eVeR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.17", "@radix-ui/react-use-size": "0.1.1-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dce82157efeca9e9f4a7deeae7a321597f18b7e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-bZJvt1/EmVEdKXV49cnbCcmkJlik/+xRtWZW7PBnPWoqUBrsxzIAwEuq26TqqXkPq8j996zh6v4hEUDOHuZtZw==", "signatures": [{"sig": "MEYCIQDJLe6bbn5XFXq6OztlS1bRe67lbpg94UzXQABn1NXhUQIhAMxWhR3OWKyEGz827tJbxaEaKsJCvJ9sh7VTwug4LIgK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptDA/9GwYTa304Td/wQQB1OiF+0JSeJnZN3fSexArpok2Gr/3ir01P\r\nIXTjKowpsAb21Y5OkUA2zoY08bIaXXpRxIC6I2x6E1lgzzUmJplVaLnuEGw6\r\nsWBrlqXlXOywKxDH+XXdOFjrRy2HLEM4AjounP3NOk3DyWRE97Q9uPJ4CQrl\r\n6MRgUF6qOqaR0lTUDbi8blVo08A0h4AwkYo+nso0o0r1tYmpy6x/BZ9G+E0Z\r\nrKVzIJ0T5cTwLXBpctwKoFrvMnR/6n22JqB2f8PrdzD2OJM2BFMnoJ1iBaXd\r\nqOg+z/V1Li55HmFBCHzqbx8BpofO8rCNYojASib0IzomG0izO/SbL+laO5Iv\r\nUxUUhT1t1/hJucMPaZblwByT3BTI9lIxR/YIf1LLjmQNqIeY7L0bnxyh/Q9c\r\n90Axn/6fDhn1fJcYD34/XCR3JmgTOY8QItnOD7Vy82FuXHFvYaPbVgEc31Li\r\nPCLD7idcULQgZnUnC53dxKrqaDUdARjT58+6pI4U7ZAuZvQ8v8UMIFwu9POr\r\nSll2K57E94ALjxI2vQrzFZBkYkomaXVw8nZwatErah7NJfzD1mEwOo1W4h2A\r\n7jzzVzz45vDLvStAlkeQbE1UxKNvUOCGcrQWsrNoHttZo9nLIjlyRvVfEAbh\r\ns1EQqYmgWEwPV7mDnYOSbnsihZzRSI3YHLc=\r\n=NC5K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-checkbox", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.18", "@radix-ui/react-use-size": "0.1.1-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ce57da0f733ea705eba65804cc9f98d995720ee8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-JO4ZTDI75hTsnVMU9DakoGyVdAGR0eSZHdxXNGdeVzClMrB/KXYFCwpp7ocJTA/bhXekMQHRDcEhGFo5293I8w==", "signatures": [{"sig": "MEUCIQDWxt0EFcV/elo+jON41OFGHyduZtHOhj1CnXkS9smjtQIgHaKvJpuqNOqWFxViP3j7kAfXyO2RV1ybjth3qVR0FTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhbw//W4D3ItFin0KWNx3Up1eKx8bw5zIcb8G1JrpGTJrEKgJ333Sl\r\n1M3Nu4cTzbdAmexx4ad2IQp0zzFFF7zfJiO2WpGnbisLySzKdG9sqL/6TBFp\r\nzr9zyjyeOGtbx2qKKtItK18BKWa6+YZ4R5FcpuAYzJBiPB/65PXLyauahVqo\r\nMCkeqGXPdcy04z+yV7w/7bkIvZNxe5zTLl6OiMLjslzK+6CGO5Pm74LMCSWG\r\neFn8OlDeg+MKPIRGcQ/q3d5LITP9aXMqOv/1ZwAFF1iXQRADQTa44AZpsxKw\r\ngJhHTL9NTXU8jrDlZFR6qfWX2l9loaDpSYlA+/h7KNbAqPqVNpFf/QRL8vQD\r\nxHoXXUccCDBhz3KIuNWeTaZM0RioiG4+ObJJlawdLKXu3haTHJw6k7iNrz8W\r\n/OLwSp1TwtintUsNU8vxrncbEitvJ6rcBsBeJLuEY0Jds8iotxfdaS8X0/oW\r\nJ5PUkKphO/Uu5vnE9bjHOlSXGfOAONzbUiw0xPZurzMltgT7MvcN63356iSF\r\nHzoIdjXjq/u3q3yewgsQesWOQ1e2QnZk/vO/KfBWDQoANHohdp7yK3zSH1/2\r\nqNmH6wGTvyeILi4b3FBFXizBIbr/zF0j2Sj2hux8OAGegwCgQU5I8tNV6vGk\r\ngK+I7jP0LBpyh9Kkdd1IMJ4buIGCUhTBQD4=\r\n=RBNH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-checkbox", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3a6bd54ba1720c8e5c03852acf460e35dfbe9da3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-M8Y4dSXsKSbF+FryG5VvZKr/1MukMVG7swq9p5s7wYb8Rvn0UM0rQ5w8BWmSWSV4BL/gbJdhwVCznwXXlgZRZg==", "signatures": [{"sig": "MEUCIHHPJdVrPHBb8aiW8tzrqwbYoguPlmARmlU0z44w9yvCAiEAnov+I+TNmotS0N2tvbf2CQWjRx6vrn+WRHQZ+T2ZeCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8j8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoCQ/+IomUuVmj1TsTxtfVIA66Gq/lwNtViC5XBMkBYziXYk7OFfNp\r\nSyFuRMr6S8btk1DiKGKkWYQ5hSmt+JZQWcTwocWoLamLAIeV/zZ0hIC2wkYe\r\nux5IG2f/K5gfILuATEXBxvESKiztJRQvZyV+cUg0z1F4cEVoPXcBELsSSAHp\r\nNl0U/jebHs2vhcPS9MGSzVkQ/0Zv3xfDJnge784efP2dZY9j5x56QPBkgbsr\r\n0Ak7GzEhUSs3REUfTYPPk9rJEom2SBmZ/K31E656F4ZAgs7AliI6xSGV+PC/\r\nVtNCbJ2hTL953j/SRmX+NAWNZqdAst3oyzo+Mwsl2yf0VgELrFojBZWs/Bq7\r\n/jKUprHNsImCOhDWd3Jn76WOylqV4Jd5AXnKKzO6fS1gua58IZ2KPNIRIZg1\r\n1NuMwIi0J8LyZfgp+IUWbAOzr+lAWGq+M2jBuF3gX2IMjT60FIkAgQ/qQAJc\r\nmisjBSHV4nMH2S5a8mZlFlOPQBJ1dq8UD8tiz+OWDcej65Y+7vwJ84V3BVQ/\r\nbwyvKzPN9DySMu4RFy+UAiV+CH0g08HTfrYJ49StMfz/4YWoI3dMDk9BcbYX\r\n/eV0v+KYRtXMUZXHKLcyKRTNdBAfOJ36FlzL+1pD0rIMX4/uw+TZa1inUxYc\r\nY8yI0ef3C7xLjrE2MPCWSQaegj/c7t/2GAk=\r\n=Eb3u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-presence": "0.1.3-rc.1", "@radix-ui/react-use-size": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-previous": "0.1.2-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5b94aca71a6dd4f8884f0f6fd30f23153714807a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-i0GA0lRlO9qCJb/5udUlaOLKzaEq6qGbISd5YKZShtgJvdF5hlvmrRMnY3b+4ndXrYEAvDP+EbimmsPoTzGKUw==", "signatures": [{"sig": "MEQCIEjJM2dpeqH9/3kwYOWFgvY8dWUNQehnzaqrZOsTVi2SAiAE6IwKCIOcubk6EOF0UN0F61wx8Yj7fjVF8epYr3VIJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJOw/8CftnjejoF5jXiUY0ZjqmRz8Mm/ZwgEXrcTTqk0ZpiW9pQB1j\r\nF04sfQjHqSnWR8DCTcN7cw7ThwIuPXg1Z8qBV3qzuZ86TgnCf2ggfo/EFNsF\r\nM49+q9BqevbL+t10AAeoEmZBLvLQMjycRhddgyVgaExI8n7GnSODPsDISTLL\r\ntZ7QWYv1EEGGjwtwtTYQu241krcj/tWlsCvsi3e/qp5qKsIP6XU8JjMkqrKk\r\nAKxCZbAgnj/YPGEqhosVfBNRwH0uwJ5nXGWREIW5geu5i0Czve6cviyWijBo\r\nYfF8zr7WLjYJ0rgyvKBtPRbRzjZj/Nq8q1f/kUCjMKRz6iq5NWC49SnluImt\r\nqrs6PijQ4aQAmJKyL6tI6LAneE4IDVVVrmQOq+TScv4vEpZ25DLIk5bUxvts\r\nAjRF1UgAgMRsiIuxBCyGWqzkVBMv6rdPdJoLvIWpi+jQpQFbU/ra+8VBNWxF\r\nSu41mnQWZJQ9KzwLQCYNCjaFPQiL2WgI0k6uf+xnrf+2TMkA+6qEBZlyUZpo\r\nEJS76temowXYaCzB8h5vio/DLuj53yKKUZZOY1W8i9F/pszABijWJERLjXBx\r\nynYkUKLzk8juNj61Fw205zGDIFz3FP8w8Bq7osOgMa9aDsvdZ2ePu8x47rLb\r\na4MMn9/WEGKFr2fnN4ISQssTApqHZikiziE=\r\n=77m5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-presence": "0.1.3-rc.2", "@radix-ui/react-use-size": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-previous": "0.1.2-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3586226c34db85f2c6c69f55ef7836c358458529", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-KRgapVRWvxFRyi3WHqRhuZgKTMvFKeKSbK1KVWm1bPg7Q18zKVgPBOhdteRxc+Jy9JORveI6XHSy1DdvSI9SjQ==", "signatures": [{"sig": "MEQCIC8oZSIfG4JLMrBc/7dQ5rifm0TXnhkTZE/2eYkG0YzcAiA1SDw3/2PlglodtP5o0+l1Uroo8KVsQoPQXHSRZRgzZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAzA/9ErPa/Im0iCaod5E9Sr/RoyM6e7LIMRA9wzXjcfR52zY268m0\r\nJ3sJV1UXOgQ37WLvGWaJx5gqJsDeN+QZ2aDuTi4Pi76YTqiRYA8e7f+70JlL\r\ndT0XO+fOQJGHfF6GB6KatiWLw31Xy6jDvLMtrFzqymjjq7Sllbxnel7Xj2Qd\r\nPdAIm43lmqqeuoy3+iGjJOnFo0+vzvjnldrGChFMoC9hC+79xsYAS+qwEhwF\r\nHdw+A0A9Ve6c4gUWjdUcBjB7km7GynN7NGR2dgT7LYp/IRVn8Wb5JlgSTaKZ\r\n08dbNYxmeEITbelCnVEwrT3UtKIwVHnyetgS72EGRgubhWo74gg/HDRM3WI6\r\n5ulAh3gbv/9MZ8vYxrNxzLB7TJ8d5lCvT61o/Kx5G2eByWQzi/aCuTtepqEZ\r\nfdLMzDfOTerLXSnNlq21XeaauuBzjsbiiw5yI/HdWxdY8WoB6FTtLSCPn62z\r\n6F+nBVGB5kh3+ufnTCw4UsG7qIpYfrOkpTUU4UZqu6dXBzVtP/W3DtiGHTLu\r\neujLmf0WxPtLmegz3cNYkCFpb1sqKlEF54867S4Tm1vtqCOpzb55p8TnQJYs\r\nERtWk6dMxNutqdcTqCpmOB0vRHSJoRId8zgyuAtrmD1sHMdAMbT+PG42ddt5\r\nNxoRF80Q1X1pkMH0Z/JRJvyLbAQza4EKGlU=\r\n=miUX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-presence": "0.1.3-rc.3", "@radix-ui/react-use-size": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-previous": "0.1.2-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f64e1c6b047f6a36fa808035bbdc4371aa75c0fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-MfX6LK+gh1OZe3lt8+pIlfSuy3Hv96wdEFkUPUrJqMbcEzh5m0ldvQDBXBwbg960NN5PoxCFx0vsCEygzC6mkg==", "signatures": [{"sig": "MEUCIQCTG6hwjVF6oxTp0DOuv2cMpZTSecgmpTeH8uW7jpWxbwIgZ5rz1sbGMh1z+MEq0L1kESWma+6AeP+tHawfJ9xFwyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52956, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroYw/9HVBzGyC8z/ljkZMx4H4WEailesl3KPj01mvi2jOXm5ChNxfX\r\nmslcACz4x7Lc0l1naFA+u2uU9ow011FjzGXxcX8k3Y5xigVBpa11A8Islogy\r\nEliOcOLSNsTc8ZMuNf0x8K+f08YErDUS1srbWsAlPed22ClcrUZYjVBIBdB1\r\nJOGSbLWejuXP8Xv/FbEyYKO78xOsqea6s7bQ7LkSJKOjnlefvMR+mgUzsBX/\r\n3iPgatLA9y6C8AXUu4psK+vCRgBeaX2H168XHsuftpIJiUIBTkQHePLDHgBX\r\nyA/2BP++aZAKJKF9NWi78k56gQB24eAZCc46JXjL0L8WITcTfBrR7NUFJyWq\r\n9hMbLo1dg9g68rtPPojHLzREL/qbiPvn53AJkN86BCAUFpzf5Ws+6V2Ceklv\r\nAdFA9SqKPAiJpIjnZ1tpvTfSadM3p+DFfaKkLDgaKnP+UMiKh7YyXgJipgq9\r\nFmf6iK+BtrBHcomeA/KHxSEFS4uUTugEUklrhfTXLs1G4g5c2hV319AdmR29\r\nchh1hh7CyiKmVXjXjdMEpRC4iv/IW6mCLsssie1U0w20mnqz6LP9lJy7OXbh\r\nzdjbVjIOuPMgUof1BrkLdEZ537frxs2Ws4RnrOHA6awQCeHFxohXINHy/iOG\r\niqLTGzJyLNkeHzkg74AaOM/kC2YCND/nhhc=\r\n=iXDQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-presence": "0.1.3-rc.4", "@radix-ui/react-use-size": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-previous": "0.1.2-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8c1875ce7cb88798d1ed05236bef4eedea3e8d32", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-+iE6cSdnhXQRy6V5GGxCnPCsc6oEfK0zdF5J66+D1CIcWqRIvqrfjpxDfZHODBrdF9XW5vHwcwMUvDk4z4q9uw==", "signatures": [{"sig": "MEYCIQCYVFu9mzc87Hv94WqAZaiTO+uAUWJf0FaNZu1Kj3wBigIhAIPg4LqD77yCAszKLCkMz9JORTJWydsfPJ70WZchE3OA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHzhAAlWlBhtJAsMxk5tYKi3waB7/sWlNAj1p1tInyOmiP8F2Oqs6A\r\nK2LNGTJBzbmq4xYhOJOUuZW1XL7OJctE6tA8f1adFi89vqWpWgQQD9FMvEvL\r\nq47r8UWNA9DuvjPdMtHQcEul66hFkykjnxbb4HF+bq84xXIsKjXo8axn+RrN\r\ngu4eOeZ6JYxi9K58op8VUVSdsUkeSSheCYygkXaK5xJHqGxrtCQGfW8clAYr\r\n5tIL96EomI82Rl+iIAUkonHYO+2Se1XOfs9py7yHyTL9gVagbqR0ZksUuf9X\r\nDiUxDtYKs5ewbWNK/fUnZYtDsPnWCT/NUma26Wjav5qYO27PKPM1LgEs/F/4\r\nH1TvHhDNm1oxh1fKoZ9RLGsq2CM/p94p0w7KRJ0NUz34E4vr8UEJAsxvMmSW\r\nSZt+uM9zEWz/lK9xala0nYzIZZ4wJWX8G75jA9OEYyF6/NES6tLj5WHgBffB\r\n94zgWiZv6NX9Z+fL8JUAD10I2NNrAOcIGfWuwzaIAEgPLdQ5WRJk41UKiZXz\r\nZN8CJJjJRRsXw4Wf4jk19FtHzIIMtfbMwoRfvw/91Gj787192PsW/b2WHxNx\r\nh6LcXUSObCJMbn048kgIGZfL6YTr/Mz6N5i31pSxKf7QsGwgWcn5NVKPMBwM\r\nLssCoq9WTviirblgmh9ZFjb90iC8zypX4FM=\r\n=FEuq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-presence": "0.1.3-rc.5", "@radix-ui/react-use-size": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-previous": "0.1.2-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8d3591eeedd53f9e32713411345f970e9f99858", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-+I6rutLsSlgEyNVDSzjzZt900qhTBbOdixG61n3B/QP8wz3BvcCBR8bYN7QBYvkvt97kk0CGkSnBwrB+1VV6KA==", "signatures": [{"sig": "MEUCIDuWBE4vPo/XG9fZ5r7zUzy+mF6q5OSDGKUNF9PdJQgiAiEAxJqEISPVn/AIGX4JQ05eSaq66QP+XcdyB/LS6p/QDes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0wg/+Nhd1EX22cEU9agzLjbXcHg3QfJ6zCGHgEIeNB4pGuC74UTNm\r\n19zmyx4n4qmP+O3J5pzY2ppk2mWhAq3Zky00zJIh26pNPK2DYkB1LZHkKA4b\r\nhhJs5CvTVgNv+Velx3AcizbI6wXTNPlPWAcZBUWjH1ak+Wde1UX/14+7/xn5\r\nj+Wip8QZtm1GZxYlF7rxnrQVfYvcSBuxO2CJXRMvzRvFaYMFz4hzuPqPiOxN\r\nXMkdBxNWn4PGbUGi5LfccbshM4lFeD+9bWjDqhc0Wg5kLOSBDzUFn9xgQ0SF\r\nwlX+BdDSGrGBnUTkZXNeP0NQdjbLDy2r7FdzdD1pQI2xkfTWBBbHJF6kl3Tx\r\nEM+ogOe0UYPRL3PsRt1P9DR4nmUBg+tbbQ4WtSq8dAht1DBA156gFvs9VtfG\r\n1Kfdm1qz13bLV8MKVQ/f0EuZ7fl9ewZnJ29D+3EWMSUlAtLq4m6+fz7zanHl\r\n5K+w0y6pxYoEGznnJ6fsd9hsPqA7Pyj8PA+nslDrEKNVZWKixxhNw2XsYvuW\r\n4OlWD3ZaewDScP8DHHXuWne4GsuWzqQKiPdrLf4Fel6XnNapJVdMJUwuEAMo\r\nrSgf0ZuO7dTFwB0GhYJ9GwxBlbUHGaqwyCFUfZewcep5FpKeCp8qT5Zr2V6E\r\ncfbumj5jhsONFM8FnkeoPht/91flnBRGiOI=\r\n=MhSR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-presence": "0.1.3-rc.6", "@radix-ui/react-use-size": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-previous": "0.1.2-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5625562e4a07bff0d87dd4dc41af3d6fc4229ba8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-DWohjxhZw8WZUZo7toFKyvGRgumBBIQLbO7/ZFyJLZE9gjTQhAvbpVnBrNdLooVN39IgA9bCND15hlorNzDPmQ==", "signatures": [{"sig": "MEYCIQChBNCBPuhqZcRlX9sG9u0NHi44oEEHiZ2Y5JTC9n/9LgIhAPMMJZimTdUgFZwpnoKWqxnXUAEMV8Rl1IyBkiNhUJjr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6yA/9GcFFWOy6zAsuLyQObWMzz2+gs7NbYdGviwBKKe5XbyaLw9Uu\r\nSn1boVvbL/5P9uYRMpSeGpRDylHtp3HJ82qzpeHJ9vzzNarhZ+GCYsq2bpws\r\nzqnKXdX/WGyWnHARPJpe6qLzVhBWxqx3dvdVPc0hMczlHRmYlV1MQqBIplaE\r\nuGBiJ8zSMAJumPhb4bgTTHW3atyYpHcivmtbOpnRHmwWrC8zFcINNuMTh/t5\r\nIJAohrvkn4O0pXYZXj+9ZPYltx4ocrgcGR7KmX4DQVXIDY8miMab2mhyfT8S\r\nCHcqsiyOajQEuc5Kzwyou1FvqEFz9KupamQfbLI6n9xk4MUhcd50/sRePyZj\r\n0URXyNLTwuS6Qf2niBtZ57jkbADh/ci21TOS1Hs4Nh7aH81CiHOWSRS9iHTg\r\nsqhf/1/bGDso/UEXGpWRi6v+4WrDRB8bEPlv+KYMeH9FpzfKoj0NlVn11vx9\r\n73L7QMNiVy56zof/skBBQyUf/S+fULgokN31UiQysoCRq7K474rFhrHqnDic\r\nnBFr9qsev6hxJkSFe8j/OTw7TbcwG+adJ3ttKnPuzi0t3sf4yrRT2E64dD0b\r\nv8Ur2ZCAdRD/mVZxavLtKVYjq+WZ0bDvJh7rf/Wl4RoXjQm9c5uOk1SVjxqZ\r\nnfB/DlD6Yn55GrAYSgj1qGAr8BT475PDTLA=\r\n=NdbF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-presence": "0.1.3-rc.7", "@radix-ui/react-use-size": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-previous": "0.1.2-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "06642f7945a4022e9fa3e808de988d092ca7113b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-J<PERSON>htCJAk2Wjv61DZInuQC6pk0SitghX99OyxElmlAuOINicImC802BS5CSgmfskQoxFe56Z9BD01UzJO66lJFg==", "signatures": [{"sig": "MEQCICzCn8pU93aKF/MFuJDOJ9NZaW/tbevSPjigG3AALWVTAiB5krb/tvu1GJ6U5KcfzuiZ9DqMI7av2H1k6qgaeszFiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsHA//SoqzUABSctQP8ln5Fm21ysDOvxJy/owJUaLgozmTdagdOBHj\r\nEL9D9s5Mkioh+V4M9U6LfmmTiFbv4/ODVaaYxHLZQSaqnUTNQrCJxJGvdeKi\r\n9V35kS623Hl6wSldjn9YA2PvsBZdBu49cpUFci1Erdu2G1L7cGWIIJat0rhC\r\ngJcNubucCs8CtvMy10gcdEnQGLFsLoTGvPl1DcBQPduzpaRJNU7mx1zXmTg7\r\neK6+XFnfRGtRTttkFzmjXEOgr9khChabJRNZD6Hd2Mi7BglrFfZLci+IfGKJ\r\nZdXPHSHExaR2KScpK/pqbOHbE56u83+kzm/sEOM++lGtBa5jT526W4LAs9Fs\r\ncFpLSqNVX8ATO3U3UYnvKbBWa34AyjBd76+M45wt1rOuzzXUniVJAzZ/OXWG\r\nSXfXNCCBMcG7FdI2nMqoI+Tw29azTwun2HjU/a8nFgRDnm78uUrLSwe4XzsQ\r\nJKA4elxT6GEmBldSB7s3dPeQO5Dcyo16kPpz/wfmhIICa2bsp+yUSPEkXdFz\r\niAHTdZepU7jyl+B7Xp8U2UUFNoMXIOyjlbf1V9lDy4M6d6bx2tCdQpMcXADR\r\nlkR+H0OQJNhKiTT7aIZuTXzuB84qGrsrOJnWRII2LP+7YQnkxAXP5toSslly\r\npa33aNIEPieTFSZhqOZKzXQmmVfzFj9Ly58=\r\n=mlOA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-presence": "0.1.3-rc.8", "@radix-ui/react-use-size": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-previous": "0.1.2-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9aec4aa322376b21e3b6d0ef993df2c3a7aaea0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-kkZAazILwCMMUvADrYZimnY9Bq2YD94OI4Kwa3qI35iIwRnMLhv0+7v+omEc+PbJf0e5Y8We42dGI/AB+pK6dQ==", "signatures": [{"sig": "MEQCIE6EWJ3y4jmPFz03IacJTfkXZQwT5scb6jKBQie5j11kAiAKpY8YZumh3PYuCr0pQXeWHHaji119TiloMDeceImnZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoM6w//SZG/pVzu9S0pY4+nxaqQN6kZqdrEJ3IQHWFYYTfyLCK1TyZW\r\nj5F1VKfhcBpqpEVpt4K9yX5iadq+L8GJ1LA344bqQ5vbdv1wpy6S442rrDFg\r\nggFaCzV22nqRj+Af3PhbAKxTVqtFecA2ax72WNdt4tBhV7QfysPMCanqKpli\r\npbkEvkFv63ZRdriKGNSxalKNGNQaJF9bvTOkSOUwt2eSui7kP5Go2Mhmt6iw\r\n0wB8dya1XN9aV4lMzfpnaST40h4CpQ6+fxBPk5PMXtOPdKiaix8U9BhQ/IZl\r\noRkb6BcdWyO60h7SKYG6yK2jmcWrK+lG0kHWlnTVd3effJJS9nfC5rJ5qBDZ\r\nCLg80jLNN+0csVrYtIA7/YbUpaK+UE2avWosJ+IyrghMIrpN50fy0bHSjeZT\r\nzOMAThBZPaRymwMmAqSM8h4/soibXHlHoImkU08OW9l+/kgAE+udJQWdB9gq\r\nFB8IIhd8BISTtBh7Uu+8wh2P2hTqHawovb1rcBk/pkfbC7qhiBGvoRivnFZa\r\njLwQfcGey+5jfLXZNeoOiXBLRC8dbP/TqQ0iBfnBrIZYb3DMWbaOJ98CsYL+\r\nnv6pvQHKT2JK00022O7kZXLGcAOQvg5JnyOXVAk9EujbYErjlLsWrxI7QYHO\r\nHirYxBuvzKkP/EXGPj83TylFU4u9zP9/y40=\r\n=+U/T\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-presence": "0.1.3-rc.9", "@radix-ui/react-use-size": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-previous": "0.1.2-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "034951bc9846238986662a71d4f1860862e214f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-8+xZSBoO/UhXm/ECLxXIr1tX1LYS2VxyG+s9D3e8kMl0lmOTyHz3JD+wQ+dbhclItXQviDJ5YhaLhmPZgqfNqw==", "signatures": [{"sig": "MEQCIHx0Ft8G6HABHm5i5UX0Tl1RR2J94ZCt0K6mNUEph/rKAiAVFoSp9aF/tdc5YE5YFJnjYU/i7iM1dIe55ryLicb72g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5pRAAmLxcGC9FJbYtZqDfs8+GY1RH8YEw3ERmJkSwz2sru1Lb/Y83\r\nB+BHeUhwInS95Scu+/Q6wqTTzc4LLx+f3401dXkd3NXY0wSnCLAKM/7FxHOM\r\n8rRw3akIjqUssrjzvsrBnfLJtsR2P2GSgkENTG28gpjR+pK01v8xQ0Tnu8JQ\r\n2LbUFXYomLrTxlfFKKaI5gNRRMbNQdjLryohY5d/dhg0x5HZnOHcZLxv0v+V\r\n2Bw5vp0RjIIYyCRAooZ22t9KOdNAaeSQa58ND1c0Q4cKizHJcpyJt8U4mTcA\r\nsIZ7v5cDVXwB+JZVmZ/0vXreBqlgnsBV+GgfaMwfcHq6YEJF+Il9Qfu+mEXd\r\nQB+birhKAFZoT+H0k602wjfyWEMKzIgu/Id/nn4lMbV3BQQiv/9nnIMPX0to\r\nEq1AIxyGu7Keq+iqIs3MR8L/7q/9SAmi6N5DdEnXAbBtjjifLbN8LokxbbOZ\r\nHJF53RtDq+kfHt11RZXYkjac7plXQUZ8Lo6TWIiQaC475IOddrNYbbIpqrSL\r\n+AfUKf8E22PapZXAWZAlujLx80g0lXxS9vkkJJJzMvFgxydGVN0W+6+4ybDS\r\n/WbljH/obUijjOqyhHm/efRs6wnDVYHXE+ii8ub21pFiggt/aDzpETNVvoVk\r\nOq557hBtJFpUvjjqYJWsSJCVHcCAOBBIMMM=\r\n=q8Zm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-presence": "0.1.3-rc.10", "@radix-ui/react-use-size": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-previous": "0.1.2-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "133ce695cd4450f14b35e78b0d14886ea0270cab", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-c4jktiKiKsY5qVMEFZ9AtVfontvc2bkLOJvaIZoKLLI8qaUvRTzXlJrhPL7+t6CyNoAj6f0NeQfXrzvV7CgNpQ==", "signatures": [{"sig": "MEYCIQCwBqYdpgDAqU2Nu1mW5Fi75288xnkdb+ueq1qjAyOqmAIhAJmbn/S1zZcOOuylt10uN30E6gp9gIgDlomPRduMEaUJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6ZQ//fWHwsRzYaz6lqxEiRzu6zPRXMy2ZhVuJVCjjBXnfwm0yefUq\r\nhAhr+kEwhslysFE5bqTt7fTtl2VDVdq5hG256BkOw/F3EuT1TNSU5bR6s2It\r\nvpihP3FJmIK3YYWCouBBRfocet3hZijXPIjXIz8MO8l9TJuVnenp87LrY68W\r\nce8R3lc34ez3aH+LCGbeGUBreBmoLFVpea1SXo4xqkrZzz6dh7LXqrWVs5fJ\r\nYIJMgue8mLeis2FglaKBcUNuGXuJpRYX6yma6qhlv7KBPunHT1beEqHUljbj\r\nG3rI1E4EYrQ4f7McU7b6dNFaqeTef6RVncARDyjWjKlRr36O91TXIqamUs0P\r\nynoqmS6bD/C1QtKUuEWlB4llKKxcXrSRsgUOkNsXaQgROShVAwlWpoFlXDXJ\r\nWB8Ox/2DZOay3QB3AVWK03g3TiE8Bxp0Lv6ccYh55dwv3m5+F4Bj/4z+PI2T\r\nh+K63r0y1OMfogtG4RSqgd5YhIlCMiMNDLpWtXRAvmbT4EqquFODe0j5W1gO\r\ngTDAJ/J57g/QmrBhsFF9OAtDP8bwDdokpq/EYm5RWhh8tiU4E6QERmyaWxs4\r\nMSe1CGROfopnrNYo8WYOyLZpgeviDj6ytYH/RxiSTjNR8A7Jvqwbx5xB/Ofa\r\nTyGY0Ra57J/Z2DJRohEtXAoJyquz+BVZy1s=\r\n=FLJo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-presence": "0.1.3-rc.11", "@radix-ui/react-use-size": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-previous": "0.1.2-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "67e7d56258ccdd5e09c7d99afcc4f2cbe1b5e349", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-nxcft348wQYabRjCf/lU8S6SL2FETMpLEfQUpXX/TxZH1ErwyGHMWTgv+pjsXxdqqgslVHmBe4CJs0JTgTkAEA==", "signatures": [{"sig": "MEQCIFjaedRuKSX0a6reoUCGMZmB5GR9zf4+Ij+kenrz/sDrAiBH6VrONMIxlk5HJlJdpidEcOvlzvTYOfi0Y/TbYsYncQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSkvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/TxAAkb4ftzpRWR2KVZQ+8nAw2b9KxZiYDTm96/eefQNZhjm/zORC\r\nB1JU/S5/A0ekGa2MQI3ONbDvzTT3gC7e2JFNai3PRgkh8yrkA4IMIx7fy8qj\r\nA3271lEK4PZjSGBHWymbed+X8UbTBLHJZJwy2Lqx+u0LSL1VzvRG/7BXQ8pP\r\n0A2PnnusFECNJnXNoJYGNSihU5tlonhYTZjfxJf3BjjTuKUKQf6h9qYlPVid\r\ncRkKOAoX21+Y15UbGCixBlyayif/XvxFzYk1PaGOsVmkWl55Ac93s4RnxuTn\r\nW5yCWTwMBQQxI1EdfqdVPWylOlRZFSeL5TrdA04jIKQy7GDbVmonnEEoShJP\r\nlgkfh+UDuyuet22u9dG+****************************************\r\ntMbBfquadlSWyvdzrH3+wPpK0wqJ+8buDfgGLQe7oo9ob0H103BTXgd0lbiO\r\nLdgs/3qjA60fAjlOV+9xhaSAgj/BtcWN1EKcBWVlcjkWKETe6taoenQajAX+\r\nyTZIt0cCWl5X5B/XfHGUBn65qcQwimosVqPJn32CGlq/KK9N5AA6mnj/J2Rc\r\nd8L/pOo+1XqOykvVLlH6NAU1L36c4+vEF0mUdNAk+JtKazmotlG8QYptHvfF\r\nn5iYkDhHpOGcV19TQV1WT8IPEq9SUQvxmwA=\r\n=x2Hy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-presence": "0.1.3-rc.12", "@radix-ui/react-use-size": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-previous": "0.1.2-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d5f1214be09ee5ba8001793d6476cc005af36494", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-ZR2kI0yDpXsaAp6u6mWAHuG57GT5n3pYEk/tELWLfRlGzA0EbJZ4lZOXhM5dqRjycSE9nsBFtZFRF7XcEKeX7g==", "signatures": [{"sig": "MEQCIB5m+/7M4hCu1ndbo6YHgJjOljIaVdsfTCbyHxSv3lUGAiAuE9LgDyYpzNZbGbcK+RSr83aGYqXsx0j/ErZ2n16aLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmJg/8Ca3L6pnR8ppkkJwquecFPK3oZg5xYPdJ8CP32vuFzL401kOX\r\n0JvBcCrOqBlpx9WQcVlu0ZUE4HBdUrL55wtob6dVJm3fjM6FA2YEhY+GkNIs\r\nC0G94+OM7qZ9tgoZum9YmzOvVBvbY/Ana6h1K2yqKHYYRs/SiisyOvw+44sF\r\nOLTUOKGKGRyaflJJdT1o1TwCFvSPBh5kn23LyEYTr4yof4k+X9eI1IEqnNPn\r\nz1YcCPbQ04it9BTVyY0ruADttDGTPi8OOS8VfUnwo+S5BsDeUbPztyhcXzgv\r\nbv+XLBkNpK1dx7WZviRIAr4SSlqdIuPRIfD0+MY6wnbW+MHqvba/DB0I9Z3e\r\n1Fow0CkpKBM+aua2h4ohNi5rYUBeS8uYsNFJZYxPMWnIDmX/k6uLcGWGrVEF\r\noO4ohV/PlxzAQ+5dzDbtexd0pRKgFIkxgLyOUqNEGlGlZn3vSGMRhT6HxjbB\r\n5aYbp9+BZZhr8vpkVYt+Su7FfmncOqh5ud7hwxRNuVLx8Rbrcw5iIEsjrm+x\r\nXB6AvjRKD9PCL37BMqw/un3XDXVFoW6IqbFme5b0sKGlDbFopgBSwH8g0aY2\r\nPW7opdxxKwgfJSe1uUwzRo/C1jlXnQFIkS16elK0n5AqLuULG6adBLw9we3I\r\nznl5io2kgWz4yH19Fu6rbdYpfUROtFP7Z8o=\r\n=2Z7u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-presence": "0.1.3-rc.13", "@radix-ui/react-use-size": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-previous": "0.1.2-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d02f2926d852e1c026461f43c5aa741d291b18e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-+LgScw4WI85y6sQrKyPV9USYIvq0xqLt8cDwctkbwZG2ATnwOjmLjiJJGgxIEf0Oj4c6And2TmsO9XFnusdgXg==", "signatures": [{"sig": "MEQCIG4Nhf4i5u+6PI/fFt1fHhrea4ZvMtz0OX/S28XrlS6qAiB7F6W1Q/dWwtKnGKvMkYQV/aHUIE2FRb/GtbIJakpi/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpX1Q//exqiyTI+baJ1reBo32PBy189kIxBAckT0T44QTKa4YoHOwvm\r\nSWZBHvHIDiZ40+hCsVnewV8wH+cB1C5cYuTxlFKPQ0ysMyClZuwtBgl86xLQ\r\nrtCQ9d+BhKeWH6OzrFwf/odE0VOiTdNBDzYC7H8Tp8KYy6oAUscfPXJmSyA6\r\n/fu66V9oo3VhK8J84iXPpgFTkGiwapTkGAzOK/0wmEQ/cwNm+g0czFnpzDb7\r\nbgxRsVoMSZZxVRVJHVp3K+nCJqV/7athUQyJAh2MUmcomImnVv5ipn4SnhrC\r\n5PgiOYeFE4eLvGowUNrWY7r5UFXYarq1FXlFMjCusPEOQQZTQRF216JRauqd\r\nnskHAOz7Ge9izaOeYEDIR8nVPcJMfIZR1G/fi917m+MrdAbvS++S9xquOOol\r\nB+qUPUYzgqJJItT4RRq7O55pCc5Bi1Dp/sRh0nChzu2OBBEfkerf+/OQptRZ\r\nqzGK4QV+pF4F+9fBFIqBOaP7FvWWEXG/mAYwPwaWv36GyqcoxQXceBa2Gq0v\r\n8fJLZ+NRCXAPFfs73wx/GCn8wsEFufmbal1yCTBfjEj61zW2gqXnlXCcli9I\r\nHf3uOtUMg+dMdIV1xxY4aIWU2eTxewqh1LVFMGUMMekvU0WMKRbqDvHvOuq3\r\n/ro9hJhdNbHr9T6kjcnPC+/r2MJjBfwWeAE=\r\n=XSFD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-presence": "0.1.3-rc.14", "@radix-ui/react-use-size": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-previous": "0.1.2-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16f1466faa2171d54ce6d6eef879432448a83980", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-kOwcUatzUqNhWaFFPcWbx8q1UbU9E0hpXl+2PntpXChgeplE0VfVumBRuphBD6U87E5ARpzPWoWjBEkF3SK46Q==", "signatures": [{"sig": "MEUCIQD7XUuGAlrXjB0NW0nPJMry5g7NhbP6Gq+6sDd4bySkLQIgY0VJnuSD6jx8AaAfDsZouht3cvQjclGg+bmbe8qtdo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvoQ//ZTm3lDeppeELZRqprmZ29u9/5RxrNFSQJA3Uk1hwjeJxSQUc\r\nukDZLYls6eZHcIGp4qks9qvPtZfxDz+l16g44091DKlq4pbKyWMRDgkRSIqJ\r\ndgJibgUaSULBQNzSIZyzqfzlkxdPhfWpF9LrKsr+3X/enkkqAzXNgKE5WXeB\r\nyK5KmaTzzmwth28QTIiwh7mX9Dq/kpsPLNxU5AZi3JmMCv1tfqGLI1DoE8Yf\r\nX+L7J+A0zxaf942dXsjGZIs/f0eyh+Ng4elHMaBCOg71sBMHUARGM2Y3wch0\r\nIDsDaIksMDis1J+l8vpFTiuFGPgZz/QMfDfJW9HncrpCPgQc/mCp0+npNE5X\r\nk/Pys1NqEL7ycPNq4iXKje6aaNzqIEoZvonWXNPlVsUGkyy9VQlhZFtB4d19\r\nlxnCK8GygfSh67cu30/3DuYsUXqfoANMgcjph/QXyl/LquIitOZhxCWihb8g\r\nKjVLx2SdofdPnu+nkA2s7Q9r67B/SmU3YWgpERcEfsBKsMWBFetWOcSGnY0a\r\nRDnx0fytomcQHx8EYwVB+vQl/k3hiz/Wq6+BIji/SpO3cBPeZE0lZpg/OUhS\r\nkAkRYATI+jtWhRtSQpo6klvAcvhEiGZl3FPwsc1J6WwlhKHhUCnAm7qzDYdv\r\nmraafyeUC+DYw5MPJFOkktgjV9Lrk3t/RiY=\r\n=z8Nt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-presence": "0.1.3-rc.15", "@radix-ui/react-use-size": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-previous": "0.1.2-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed6e020bf59095310dae9261eb0b44e86d807cb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-MLB7wmXICWRp0+oy5vgPbx70Laq/n5wEq/GW3oGjgiKOV6EdEe2XEjktNkvEaPHeCiUTd18SngFh71dAHSCL2Q==", "signatures": [{"sig": "MEUCIBPFYNUjUX06M3twp1Ca0EqzxKuu8BemanRK9W1XPFnzAiEA4qGTd/W8D5a15fibsTBdbTniej9aivCr2Ibi7YlEV+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4AQ/9H1z2SMSbD6c0b+dSOrPqF2sJmBffqNFYGra+QHMJ2qGw0bFq\r\ntS+glL+eH4OxrHqxqwv9qJrwatncZFGye9j8et1kL3Rz8Ka/YP0BusN+g6F9\r\nw5TRDw6V+ZEZ/+bGQnDKS+1p4GkT6s3Lld9KVfUFHsBs4UVs/R7aGlVd6Vs8\r\nhOoHU3ndiqI4XM8DpXwei3XhJbwb8hhdB1GS3HqKyrlmRnAWLpq7fCnVu4t8\r\noVZtiqUoAwIwoGgOPztqz9YeOZazo150iJkbgjrGFjdwWMynvsod3FfUzY/x\r\nV9hHBX/chsd0qVU2c5P04l49+ho3eTgtrR3ODuYvmiT8xgcSJ56Kidp7FNaC\r\ntveBmpLXWnE3AH4IWGG2Olh6pfVsvy7kFi9g3f8VSYYE94gJBYWq7mkHHNR0\r\nj+26b+suOBwHOKKf0XMLFo5cq3x2M/UbduqTfZLDYkmj6LfY0b2sUxdMwMek\r\n9dsoJST2lwdBlY6278Nu0pIKBvwCCjsQDwxfVdPD345e3SOAOaEus8mJEp49\r\nLIa6bLaFk/kuYhg+9MlZbFuHGZtWA1+2CPGLPt1dterJagvizdUTNB+IY49P\r\nirEHXuBp6jb6CvMHzcJQ1xxUJA4HRrNl4/qGwi3olf3YhnopPdGNMq+0YD0N\r\na4do94lgoH6KpN1LRBwLV5ijaJ3NoE2dl1o=\r\n=hZZb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-presence": "0.1.3-rc.16", "@radix-ui/react-use-size": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-previous": "0.1.2-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7e5c35b05143612687f9340e1a37a9b98043cdbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-Tzjf3FypS+s+oFbuz5OWIN9IuLvR43atfaS5Z298P5aNE8UhSWxL5AH/Nn7BXsoVhqSnqWYfc8TxxwgS2skBLw==", "signatures": [{"sig": "MEUCIQCYCSX87bZtMqF5Cv4iHlUm9xYjHajac8/Hie7+y8RKEQIgehd3XG4uHHxF7mBGAcNxPQ0YYFHaZW+r7buX+jXDm9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoThA/+NF/5kkQTsvqCJD41cjutOrG7Fu5rPLJPb1K1+7QwkMNT6FhM\r\nu82UC2E4ep3+8zYVU4BHMXYfQZFpG9HctD0Kf/1ZJOzz8T4J3SbpFvsj6cNO\r\n5ZrZfd1CiCjbGQAsA7NQjeZjouLDuosiTMsdlYz97WvvhLibTbGO46X8AkmP\r\nSx165Oscks42J3800uw4a3yDCmZEDCBVWDSO0wI0TSQmrjzGgMgqtQpoSF1n\r\n2B86+mY2temXD7f/g1Vj2LUxQr50xCQpi9TTdLP0f0ev6eZSvErBSJ9CO8nc\r\nGoUOR69uAF7KdLXiLVkhUflI208jID3hMLtViTweA2VuExXI/sLQiDxUg/ex\r\nfinq40Oa7oqUmQhtnJeqZO3R2zkSOKF0vSLESrixSOnSGUkXRKWBFJ/k8Pmu\r\nbnbY8Y9PWOlswO96m6KMXzOx2iTYymGWnI9fDu9Y2FbtUac7MC+p+cUY+0kO\r\nOe6bQ405wL1sv4LvQCyC5FFalnsXBXbDwcrkNifDaMeU02pyvcTO0nz/60TT\r\n6VtFMOt8j2+CuLAxqxZtfKd9c6wbXteDWylD6FyyvFCa4izX5FUv0rhSHZmY\r\nhoM3kM8A1LJYFprgOFIiEV4dxMmrgFYzB+uIpkNDM8Dq3sdIbQ8zKSJpE+3M\r\niWbKJiDWj7XhA39oDVfU8BI8I9kkRVsRlb4=\r\n=8cee\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-presence": "0.1.3-rc.17", "@radix-ui/react-use-size": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-previous": "0.1.2-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ff8e882dad8d73cc6e9fc6baa6c1626bb86eccf", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-tVSbYvDSPC7daQnw8wHBjtzlj/H0KAejH/t0hIk9Xn5lFOkr0tq1OKHgIXR02fmIDqLgIxSiSY9vDddJh/zQtg==", "signatures": [{"sig": "MEUCIBHsuAp3tKH7tctfLt+w8PKqbGs5wNf0Hmg7XrG5Q/laAiEAkpX9xaZn2ZkZIAfaD2Zh3Mof4uRqLpLoEsggjbr+m3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifhz/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5tg//cMgxPlQ0OP6rVtJ5NTRZUAdLzOqUn53lRgSI9635tR39ThHR\r\nfBirL/Ifl/1dRbJoQU8VENdj4lYg3TiStd5ec/L07Ihzfy8NVLKdhNUs0XiY\r\nieRV9cHVgPRjUFKwJSyosNU/ZF6n0BUkgY+wQrOqOY6O+H3E/8ZzMQ3nD9kY\r\nOlHiJ0e6wsGKqYHHjwYD/eyfz2jzWXkd4WPSZkl3+bMO7GaP9JzvAZXyEpHC\r\n18G1cz+MaWatKXfFw0RipuVDluW3SuxI2eAIZg8+ehf+fWXqzct3RMXgV4VS\r\n5+Msj7MY3E2GzejPwLMpcL4sGlkCFyqNIWg87oLL0ALYRxB8b2g+WeV0RzzR\r\nJTVDZI50oa6VhvvrKnpplRrBxZzSOlIdEZVXkZF8pyj1c3JiBZoBRntmgGOx\r\n8EHhPxtLMc8DfSEngIzsqGIJCCwH/Kqpwz3JzY/U0KN2qnT8qHH+yv/62UqM\r\nbEsWhQhaKOnueX0mQN0BIdTogzQ+Kzh3uC15Zce7/Wx6sluk47MvunqGQE94\r\nODJ10YYkgrOWj3aUDETMOcYYgqoc1lH7nMT0p+U+ap+LCIMOU+x3W27ID7e0\r\n+AISI19MpfoTM45H5jO8a5PrHXb9cJpSWdHY1dZ5HVpkl4ixgooeXNu9CTqZ\r\n7Usmm6sTTTCCiZDPocbBhU1taLeMTQFIn+o=\r\n=s+49\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-presence": "0.1.3-rc.18", "@radix-ui/react-use-size": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-previous": "0.1.2-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dd4742c8311807240c8dc9253f50021124a3cb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-OcLKppYf/VPfsduVW1vp50hb+53igXZnolFM8oGIVy9gmvHYfuCiNTcx7wX0jL/zasHMRYu1z26uAFPe5D4B4Q==", "signatures": [{"sig": "MEQCIBNEWlHfWvChRSptKG5htTRVJhKFsxtgxfRJnjfTLD2nAiA67iISMGmE7YxWDt4ym+IIOdSb2wTpGzcQoFptsX8bbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRXg//SYqDd8WLLyoFHODczyGuENfoas3wuuMKNeAP3ZE/At//1IBl\r\nNnUf3nAkZd0L9zrt/7goNRvIqiKDfKaX0a0DjciNE8y1asgPoZ5fOV6bjYGQ\r\nULSWJjF3SUlv1KFXZ+0/9q8vbbRL13WpwzW5hil8qdXn0T2e9od2amIeYNwU\r\nGLcx/VQ0yBpXM91/dTXdY9JIHHhn9H7sx3ryytMI+QPpx3fvvpZTykRAAtWM\r\nzu0EwFjNuK9ByueOSYzSFHrxb9tNPYp/w1yYVw1HC6auYyuKDTOMSBVU2sX5\r\nne/nxNFy7WN0heSrl7dre1tVoQ1rJwTgHIOOy8knX1BBIIJaAwcPU29JbJgn\r\nVwVaGwLWnYDrIiv4XEIsJv8y8c1/lbHXnaypRTwqtL6pOGtft9b/RNyQ+5N1\r\nVEZ+QNMvwUw3Irv+k5SO3y86YpOJYzrasrDMMbmIyHibqaSSxkZNidsC631k\r\nqVnPy/jV0y+e/oXvBSEQLPAqS9PdZlmn8ANHmbxy7vSAjDoIfTKS+kENpivG\r\nABbJeinqjbdyHY9ubch/PjvsWljvcjZkEig28Gycq9zzCLBAmyt/yfLW8/lT\r\nAkVav6xZ6KR2j8ADHwSC8P9vzkaIitfvH1eSNatbYNcl8fdZQkn5v72tXgs6\r\nC4hvOydwtKYNMkva737gkPyXf2bDOufGjVM=\r\n=Z5vh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-presence": "0.1.3-rc.19", "@radix-ui/react-use-size": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-previous": "0.1.2-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5a12c468a94b9459cf7fc291048e21295d69d2bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-NOMzrZzUsN5FHhYlKlciPCfvHwUFjLnlfADwPcwjv81UAgXdL6vIDE7UPL4wPBA8QZFrku8LfxwyXcR/ccb9zQ==", "signatures": [{"sig": "MEUCIDfUwEBwBcWgMKejHEL4YerwB10kMH1Y+DXKN3q4vrskAiEA78d08C/sQI0NGtZDrbAujnsr4CynIsUS2rRNwLAz204=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJixAAk4e4zXZve+ivecXMFc3ASwJkzomPcmid1Tyod9s0LWuWwuG/\r\nuwlhb+EAi+SW5ovaHNO/cQwqjtb0tLS8ZyPOVCtlzkY4OqQBB3mtwOJmi/uG\r\nh3wvOH+Pzgla+Y/Kf4MA05IkCZVa79FK3WV5K/HUPXxT9nraG1P+DpJdhsN2\r\nvMbNRCJyLj6H9xLPDfhRV02tD+M4O0ndYcEnPULGmKP4O1nQ5GGRsLSHynEw\r\nV8eCuOMHGCJy/RXS0ujCz1dz3kIlqF8dkZdXqf3ZLHeH2fjH2T/FQynSlzwp\r\nY/nIaqu2QHF5VgqIi5uCP48CVXe3UomXbcxsTEOq0Lxatgzk8dpdHwExPUCq\r\nDGD4oQf7AYq9e24slQNz2EXi6UZr5t0lmC5+8vu8hABfGOZHSpmErhcGjzwb\r\nIMhaaA42zEQhZ6FwfxwPd5omDXqlh0HTF1he83J83JbGLRcKLwIJwO/kTeUY\r\nHo24QK0hk3lEogN8wvefmSqxa7XpSwpYxlaKEG8tebRa660w6I+GUR5yZuM9\r\n3+b8tzXGMBHUMo41E2v63i2ha/5QmPJpgZSRfm0f0Pm05gNYpUeCr81ZbFrw\r\nroqOuSQVxpGDCwgqJi164vqlHmBRtembFI42NOxy2d3o4rTHM9AGJnDgEmAj\r\nFaJBc4lxe8xVTpZTb6eq3Gga4+v26CkE6Vc=\r\n=tDwx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-presence": "0.1.3-rc.20", "@radix-ui/react-use-size": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-previous": "0.1.2-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f878bfbb5f87c109e71af9bb5521975c8d0fe17", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-Z3Ih/FySQstiCgSM//Mjxct++lS1O0uz8VzszS4ygiVWNg+obyBviTBiLSTua07WWDZn7IoW64rBDL9xxj8oFA==", "signatures": [{"sig": "MEUCIHxt3PZhoqEKvp4i9FshC5rYnlghOucy07AYkVBB0l6wAiEA+J1Zjg3HISIY9YrqvX/m+wq4f8GbOrUB3Sa3XJ1qEVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpy2g//Qj4uucxQ9TI8gPB4vcAnVL7AyVWPnGlSWa7D2DSe8Z1CYWT4\r\nMZxY5ad5mK27tT/z2SFdZjWZkpCHPhhDM7qIq8kz45SmULREb6tZRTkDlUZA\r\nKmPzsonSNfy/ED/JszOqLVEL0cJplLSxKIeCHC5Is5fncAe/lzTMkbvFsH5G\r\nGwq8fAKZ3nS624mNgBVDDTMKrahArAd9RPQ4+pHSNZNA0q2uJ3Jr7o3YZK82\r\nEEVc<PERSON>u8ceJWFr0KU0afPxzqXtE6KMOau95rLhwnY53kHAEcSKsLbz67mxfp9\r\ndotFxE8SOSh+QZKCeTRTYZpNgcfc/bvELvtb0TJ9htjJAfJ5Yr+1iGQYn21h\r\nNKaX1fbxGQ2LC4H42H2soBSBg0RgOEq8/id3aZcuZ2L+q5uQtn5CPx+yrJnh\r\nvMJSzCYwzOutI/u+HYOkq5f9EqrmKt37wSZNv0F0SrTsZjkC0RQMstZdfIbx\r\nz8ZVTy3wBI+gTdb1TL5Kd1ImQbwHLFZS8epe/JBvgtFOuowWhN/GRXUp/g0q\r\nMNTfadMBUgKqYzNVrmOAZ0EGLwrGYBHCHSm5E67LNukfXKskcayzH6Fd1f9B\r\ntQqZ1HyjiKiGqpTKaqGAFG34M4vvDJPeOfVErqIlws/q2hGNwKQNEP9uimHi\r\nxFidkL3dLU6Kg8bAWeVSfxy4rSc/nUT7JA4=\r\n=2TPN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-presence": "0.1.3-rc.21", "@radix-ui/react-use-size": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-previous": "0.1.2-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fca4706af58af7f813a8baeee983b01209c7a9ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-4kVUQJqobYlIoGl1WGNvbZ0ngwUAlztrnecr83q78eViOsT9xsDmtZ/AllKRrWSNMdC4dMCymobvl4pZO7ncDg==", "signatures": [{"sig": "MEUCIGpX2CT5c6y7q58NnmywweqJQ52LcmeYl8oDL5kQjsj6AiEAzWthv/wvkH2SsaZR+DGngjF+7Ea2AIDA78/E1CzmwZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorWg//VgD0Qyokb0GzRJpvhGMo6JU6vKILpn19xQJEWbQ/FHNnnWc1\r\nU1nlB6JF/RGS553RcXpHt/gsXFjy7vZVr3dzWZ7A0I9RWSIAnK2+GC3KItjZ\r\njnttuselwTBS+gKqaMOy/5EXLe2BbghGXa1of3pANcA6hnlzL9wmxMrZu0uD\r\n1vO6FnF2YBpKcT2cufroLfC41/dBdEsY+GcLLtFDZ3MWuvwxLfXEySCgmqe5\r\nvRpvC/WG2X985SA80IygoBVXNMoQ4/ACGkMNAQZAmbTlt0LvokZfuiKzAHID\r\nJN0cxYa7jmdpco/P4vlu7+TlX/g6/YNEr+O53WDfC9z/5bF/nZhvG28A4nae\r\nptzaJ51rUVdAU2MYGnDEtEAb3S96Gg5Hx5aJJ7Yl2wzB+zq8/95pUvy/DG6c\r\ncrULE5iGxP953J91006WpGMeXpnK9sD2rhTrc4aDvYAZVfd2SXMZDemPyGJE\r\nRjhSG4evgyTVRe/UlWoooCBUv6dnnFSsKmrNC0qdOq7dVCYmaJvgPLYdGz00\r\nqezsxN6705oUQHEhXf0I7J4nWvWpVj6XNIeaMsK05bjDKcpZu5Vm6LqKOO4J\r\nNJWdlJTP+st1nm9zdVL+nPx9+MQdJ/dH4g6Rrugob9S0nYpET4jncLDSXp0m\r\nSyOrPI8ZJXYW7LqanMcO1rO1AW3saWQRpvw=\r\n=8NTe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-presence": "0.1.3-rc.22", "@radix-ui/react-use-size": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-previous": "0.1.2-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7de507434c406e09aa214681a93286a38663a454", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-koN1FAiKHrDlAKVjTEuulGkEl4ox0+yqRVsqkHVe6nLVy0wd2okxjyU+FFntB00c6jigxJ8YUoCy+l8DhRG/aA==", "signatures": [{"sig": "MEUCIQD/mD2nG7O92YdFJR+4jn528KvQ9v8KpMZsEL7CxL1xGgIgdfakCyImlgdVPE7si827zoFo31PEBTswGRezZVIsQsE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn+Q//UKpqIpqllEtBNBsW1wCeBBvLEaZcB3Jjf8pBHpRcbK+3wzaP\r\nLgHTaJxop5kBBAr6+phpG+BsfBfLYkD72n1HPwcEaoNmy/qUS27GxXW051Nr\r\nv+7jKq1ovrFdXtTlGNa9ixQSjkugl0o6KAqammqsT8/NiCsf+hDH6nS9a6eo\r\nm2ruB5W9Kn2TfLnSQ+SHzLb/40QHjMSyJLz4Rg+2s69hFpnehhZDPzymj1tr\r\nOsLKXdft4cytMc5dC0EHxVQkob7TU2PLJhSGv7VugAVN2PosRDJ0z8olHWMV\r\nmEoIz5l53gg/b+KkBRhjehMED507ckopH/Y6q5QYyzEMxwPjV9uL1ykzoerH\r\nSTI6wPiAQ8nS41ftGynM5z/xMblPJA84DB8Icvr9nqos90sZ53LTwTuoxvQK\r\nEmU2UvEGAvZG02NLta5MohXwy7bYWIcoCjLp9p5MXgTGbXStwCSHhP3kAF+E\r\nNjJ5xfkU+iibBULrXsfd+m3dIrWOjzVJnOpoJyAkdmjt8VBbFj3aASCd8Y1a\r\nfeI4YXxnfpkN1YnuTFRUOIJcUCwtBuDcHoiE5Md8Ilhy1aCeSjcfW60C4Jc7\r\nSZLw1Gx3Y6V0THM24+/kJ44nS6o8mb/kg5dw1u2oHRfO9EYr23lBkH9ub0/Z\r\nNhFYnVRBHLXlPcwSMgFOAbwAz5JPYiTd0IQ=\r\n=WGcy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-presence": "0.1.3-rc.23", "@radix-ui/react-use-size": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-previous": "0.1.2-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55e795785556d44b269646212e7159e023037d2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-iaoi6eG4SaVY+RdRZnvwmK6T+WpQCSKO2l6GJ5REsSnoSaHHpC+dM9IUpMgpzOnrczdP+As5sTJl+93AN8L8AQ==", "signatures": [{"sig": "MEUCIBoJX/3CBl3sF1qBxqo3J8ZDcEjSzXuRsva9knOv5oMGAiEA6Slpk9V5vkWQrrNB/X7d6gDq/gAuhnMX/2WdgQSzzr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXpQ//QzRX32cwqgQD/DO9UaO+3R/QAISaU5mNNYja6pUe6hAiYJOx\r\nwjl+nXNQytb0h0qu9YImJjtII5BtsvAoFD9k+gqS+TUW6NV9fmEgAeFc8uGq\r\nykalwOiPUfGux9+awc9nIRz4g1XUpNcMWU8h7jAzitK63y6KEX50Oq2v3tTL\r\nuILKkgBlYSnUJWUpt061Zid9LA/rVoQpqBrVPVwqXjG7/n6zyHKxhQGVwuJN\r\nrhT5UJGJITs6TIc7aI6U6X6a6NiA2AZlkBhXu0h4Xr7dFAYwn0FtzN9bvJaK\r\nPZ9frZVOSID+hOSMiRxpo+kSqdnuHn6K7AIIqUOT5S1S6GwV8CvKgtBKqyFz\r\n6EJuy0CTvLIaGHkDIWrtioxmqqTn5jIqHiESm9npNABUb/lmdEEWx/0+0FpT\r\nzE2uOJSBTiwqm9PMUDiwSatSc3AzVnxBdWUOM+iJeEn/ZXIsocOY1/owwdmL\r\nFLyrSFhBJ0eIQXCGSY5J9CHj4XX28v98LSJciAMkaYzh2Dw7EShNV59r5aY+\r\nS2hYWOFQcCSU+eCWDKUqIN1blOMFnsxBRfjTeFrj7u0NFOdCU4OaoLGd8UZ6\r\n7ST4Qd7rAmZrzh3sn9DZ1jPo9TsCI2IwoGIlS7itGMspQEsfPkqkSEwNTURB\r\n01L++i0gcwCzkMDztKC69stIUCJUH8+++Es=\r\n=3F16\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-presence": "0.1.3-rc.24", "@radix-ui/react-use-size": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-previous": "0.1.2-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "869f02deb5509856d54d69685057095230c4c58a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-lVZRIwShW8GDESNt551zjvo3acHtfxFPd6VcoK8ikBzxBl+zO8Fj0Ky1Ib8AzES9m45i3tx0Ps3j+fsa28gfMg==", "signatures": [{"sig": "MEUCIQDJDBC3y+w1wxYfUBH4/XfgcRTaa/Ie840kgIZIehfV8AIgfC3JUe1ua6Q2DWbNGfuS5kF0C8jWF08TILdzXF3QOC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRXA//bKiCOogal+hR0Vlau63gW51fYqZYBONzGrl8y2iM1bIzxC8w\r\nfhhvCX9S0QVHQaGIkaCg8pReKqIHmpLM/Upz4WuZJVe988xVZ8LUKoXBUDaX\r\nHHcueLMT820+97a+Y/TnD0nBaAxBn47n+Jgpr4rtdqeuTsPLUd5ECKZCqTG/\r\nWyMaaP8LJhRkDswULQ+q+PkBG9pHUt+hOWxVYVpDsXwTYPOlxwfvim3fXodf\r\nFcZEdD+DAk1q24jabB5hvmuUOlcxvOi0jwfVaHNGVSAgIGv6wLCTVzKl3/vr\r\n9oA1H3st7H2srTkS8XU31KTVTwS8DiLiPPnFSF+9gFoqvvJOfIII6Pc8ppDG\r\ngalVehXPVw0cjKwcZ6DT2sIqIssppY6BVbB5huWkeSzKk+CAiwYhN9dscMeb\r\nmwImjfofimumnPRY/+yuKwHcGb+NhMFBpgRk+alBSE5Xxc5HCsRdhHSRJHcH\r\nkUBU4Wd0ockwPljU1kav4GrUjt3G7oF92VG1sQhw8N9BTIbE9441M4BwbTMz\r\nGGPYk1xofcQ7XeYt58aYDh65VrFQ0cTFGGxpQ4cqdIj0TVFLOW+KldNaZLGI\r\nGBf+3XTKizKz0n591cx1KMEXh0FcsXhaYkM83mFW53V9TTfKSAd104f+nDpL\r\nmlRYu8GRMl1O6uEO9EY+Nf9bvYOMfo4dMuM=\r\n=GEB7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-presence": "0.1.3-rc.25", "@radix-ui/react-use-size": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-previous": "0.1.2-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "894139bfc631e81b42d3de80c2c7dbc9fe7fe189", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-XFpTmMMhWYrDkzTIU7IAE7c+v83UE/NqT0F+Vu7hGD8lAl6X2hMtsSTyF719rPMPVO8ZSHFgnwycMPBfAjbLvg==", "signatures": [{"sig": "MEQCIHxEiVooE9EMeL87RISShePDaCiCyKTUEimpUCJf5GdMAiB7wCPaQthck490jwD4nLPhQxGiDmmUBdL+aA2+aPxdzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4Kg/+IciS9dwQU0qRxwHs+Sy/joKmTGxF2Dj0istHwLCN5HW8vUjL\r\nL9+hILI4AN8Rr13UKmhZq+mZdzogXqIFloQlJF2UnMmc5bPCNA7ctOJXXodc\r\nJg2TT5NPQRhM3LUD7a6fJn8REhqUN1PANraygowaTmKalRq8PuGxykWsZl5n\r\nDK2JDsMFwXiTmZvx+Uv0CLJGGXkRlQAZwkfsmKpggt2kQSFlwm6M8bJJgEeK\r\nrrwUQx/F6d494Y1JXD/2KaKqwr85ybwZKCE9Eh8FRLI4uN273YpHJdp1F2WZ\r\nm0COgMw1CDTdTRRmI3d0UjSjCX0RBcyGMXcjq9lWbbPmtqeYO1WgnZSYiEbz\r\nvGxm98HnWhm6FOYxJxiYIAcn73ZgKIm73yBh8/mLe1KhyddFGgh4ustiDglt\r\n4gqx+sBGo3wz9ZRTu5W6VWxqOlBhSYvSchG175nzfCkqH+UFpFt3v6EO50PH\r\n3BVy9FMhiPEb1C6Pu6lckMjSUo0PmmQL47rD2KEUNswxgcXtzANASlZlcA3E\r\ndn6d82yGMsgzbYZdEHADN5dTe+zHQh4y4I7AshZ1vaHL581q6Js8qj/iEzqh\r\nXVUspQi9uAxEeZnEaFQZB6m1WyHJVW1xYedRu2ftbc0mCwqsqcqpWY34wffZ\r\n3JZ04pxpsYWW3DeeseA/nnaEwEggnW7HXAU=\r\n=Bm1+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-presence": "0.1.3-rc.26", "@radix-ui/react-use-size": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-previous": "0.1.2-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "edd434b2ec36aa8fa598049b8b3d039c5750c637", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-kafWps7eX+7+dfOW08jXBRpPfBtQBGjgAwZ6vsZ/6gPkgyadoazXISGmWG6HX6QZQYaAQwXSWBQXoHLrNOndMg==", "signatures": [{"sig": "MEYCIQD0u30xowiIvbIxR5zcQmKM/noYHfNMzRhPRCTV5DGMkgIhAJa5bBON7hIlZ+R8xLjalAt8BrpkYX7Pby1TcbA+gCn0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqF7Q//cCd1xo7KAFgPohHjiNmQIq0+Nk6iQZHbPajWwuQ+nsNo/AtS\r\nRTibVNpmQNioAVIJJVWCJ3MIx6yQrK/rZLA6AI3D2G5hUqAyChBsQrA9g4c7\r\nT03Bt5akg9lAXziS5sy5ROjals9N3SC0I+7Gu1/Sf1Eg8/DVI3YYvUUmilff\r\nTJjFNLsf211zlfgP1BLjCeZJ+YIfayauboDrR2oc1/E1G3S70Ff9nvqMjgQc\r\naW8aZwZVvO39aQiQu5Q6h2y8/+D8EwH2mnceHk7r+x+lgG7PYVneJ6jDX1rS\r\nq+AEaSfCf/pM9dJpuGZ/jO8wpmsGFp6tvSDbpo0Sm2I8UJ0ay0agln93DUvg\r\n5Xv3kBnpjNfPiEDP8bMwKnF5nmUhA94Uayf4AqMt5Y2kppgiF/FzUjiV4F0q\r\np6iF5g9/iw9Kwgt2CnWf2NabCAz4jUrcwLiERF1NevrYYG4LmBtcLKKSx65/\r\nbn0uoAgThcuwRUyzd0cF/w0VFYTy4YfrOvokUKly29Cq5EGICsTmSca2hPRH\r\nll9iTr0mA1dGucF6X+MtskrqKiXlf/uVHsIleeAs3HX4HFHhI9YKvzg9kN+4\r\nwi4C1IbcuysZqw0n5fb631SKBEdsY4Xit65auwlzct7Vt3awu1eqBb3e2in3\r\n2LTgpDciHE8vlaBmQ6kJ/ZK7Op/bYRQF74g=\r\n=6jFL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.27": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-presence": "0.1.3-rc.27", "@radix-ui/react-use-size": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-previous": "0.1.2-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "46bed385d0f913827ae222cea9c8e493c0565090", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.27.tgz", "fileCount": 8, "integrity": "sha512-KCC2EoUJXa/yC+Wxx7HlgqM+W33opNEDLFN0IG7WVIzZjk79hI3QuTBVB/HCo6LksQatV20/ee9ot96coIVnvQ==", "signatures": [{"sig": "MEUCIQDQbyvnYd9XxopZJH7Ru5fhnIY3TWOqYl9KUr02Wm8QwAIgEf83om0gcjwqIPAwjFledkdgEgTLIfNRSuhi6zbc1t0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ07ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmyhAAnGKMJkW4r7XtwrvBfCXMsW+DHdSQGBE7VMXObSR/fYxLWojn\r\nC7PhT8JtBBNaM/4Md3r3y6r5faknilSCPkoNXjhVWistTHfjL9Hlr5OSNpmG\r\naoNDrx2XJA5+E5Fusxh7QiGosN5uPa0Bb9TSw1Q94AEy+MvYT4CZP5WU51ke\r\nco5adbWc/Iac4QX9qgiIXVdXQv93o3O14icRywrmBG8IlC5+ap07LFXTC7PT\r\nGYEJSR6JK0hcppgPdbaYDS+RPK7Vpl68A8NxGPem4lMHec72hNqzlEr/AHqP\r\ncCrceFo8uiuMVlzuF62RYn4Jj8R8w+pPOrEX2ld8+6jeE3mPem2EgYEA3fHg\r\n8tbT130f2yJ+fqJhriY4fOxsMCu+KM3W5bzHbnryVwiLr/Lj2LTPw4pcf7pZ\r\nzTQ5XLNpXrxhaGgxVBAhcWxCNLc2ihAhXPZ/0OMYelNweGRbwGmSL59IY16U\r\n1YpTSNbf2lYqwKvkcvIzuiLmlaeNfwSGaMiq5dDL6euZOwfH3HF3vU/iS+L4\r\n6WdxnTtqEYJfA7LwpT+Vig075hgxJ08H52bovBNLvkgBQf9OLt+cVVnULQsS\r\nURDk9yId9EYdW2S1EFQicBxLDCPdaGFjc79Bret1yG51IlBEtPeovDr4GIut\r\n592QIMcOj9tZwFzdwpBWiy7m75wcMOJBBI0=\r\n=qNlp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.28": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-presence": "0.1.3-rc.28", "@radix-ui/react-use-size": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-previous": "0.1.2-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "db809e4492e12344859a792d916fda7effdbfbaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.28.tgz", "fileCount": 8, "integrity": "sha512-IwmMfY1MfpiID45EyKxMkGAfBu0p4NxUotioZ90iKz5h+AzS4glNOYoCC8oCjdITM4uHKKWGskwJreMKTrn7UA==", "signatures": [{"sig": "MEUCIFkLlXwrdR6kNrp2fw8qs/vUva+A3QBLilFAagQx0FGeAiEA3Fgo8TRK1C2Jjw1xxPS+FQzaxqH802IuN53sS9toN5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildM9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSfxAAhmn67slWXJwmgSnjSPdP4Q7tyTINSsMHUp+n3h2YbOYoZuBQ\r\n5BKI1THI1mBRS47kIlwSzOpTwoPweBguA3VOx3Edx1x3Mdtz/JKxC3BNjArk\r\ndVD2VMWey+WHo6ZWFaeP6JE4T84Sl1NnPQX2Kxj7AgGxA+IKQp6qYAfYJg/a\r\nYVJjkVn2avJbnE0wrdQRs4eMoz6CKOVS+KKJhobPHWTV0r5ryJ12rUoqz0Rm\r\nZlAHKiKxM1s/PT43f6TQssBc8LCooaM10LWfM8UGQVojan9p1jSb8sz0/tve\r\n0DCxus4n2KKWJ2NPcBk93Euwh+wp6FWRCUcxcS6u0LNZTr17By+Amvs5SzJ8\r\nGv6xWMNhKLu5rLehSpWzElljO3jZUBNf5pkLEPBKs1CNieKQzWyqCk1ba1aT\r\ngimDUPXmjMMCnXAh3HF99+th9da4kmSF2ejrsYHW+xneQzDmAUuNq2j1jk4F\r\nTWZSlBGp5hNcgJRm2pDJZAZUvFPfzX0akE8Zv2KhHWep3QkJ1vMUDEhfSDOH\r\nUOIhDjly86+GUOHAlH5pVyeYUtjCVb/aGxqOIxSRcKAkOTh0O3bjg9r/OFmP\r\nYCFMdY7vhV5TzpEoz/Xp/o8TbG5XSo14MZYk5bOsiOjHReK/JV2aZ0Vr6wyP\r\nxWAdA6R5hG1RQQZLjnMjc9vKqECjE4gWvyc=\r\n=SGRB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.29": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-presence": "0.1.3-rc.29", "@radix-ui/react-use-size": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-previous": "0.1.2-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6ee8646d87354955e6b430d402c842c99f94a8bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.29.tgz", "fileCount": 8, "integrity": "sha512-Y+AOeKO479tQo7TIJwWE6t9Fp6NfgTAcZctlVdPOlUzlXFIlGb/jNDTvlgbKsGscpOGAfkiopkVeAxFn6UX5Wg==", "signatures": [{"sig": "MEQCIEx03yxQ+CTrMP015UHeAac6lZ2vGIX2mWMHMXArog0hAiAS5/QlATnFxNbH9bEi/sjh09DJQq1Nh+mNizPLdTC9sg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0zg//d8+NiHvo0rJzdbm1KVOkXYYtJ+eU48k/GcWm6vTTOgdCLUQe\r\nY4/peZAx2R3ppnK37b/ddPCjVAdqEmcamoZdEu5dsuGiMnXaLCy22ri1TlaA\r\nLvZ8X/+q24fjFqY3nL5i1EGzJIJ+yAvlI60a6vOKTgJRYqVcc5+dRFv8kCyf\r\nOBPeNa+fpc+A+zHOrbWWMMQ3rAN5FmZWiQmXRiMPFilQ4l7gQ8QdJxGq6EUJ\r\nJrXp7w3+oHLI9gLDDSI8MXhPjLDoc8HdY1Amvm63Kzdgxl/ppAd/0KXDmF4d\r\nYda1M2IdJ4Ab4IP0YCaXCaxxz+LtfokrP6KF86ftxPXaYMxzv0futla1uDSB\r\n7/YaShGXbBCkFfiLSrRdLxFKzKTkS257Xj9vepp5t1zCYkxAJs/iFYDrD7yC\r\nCrjKTtWuEh0W7rwfXPUkfkHvfgUyppxpAsy0DvgUuFV8vBXszjEhfgKfoIDq\r\nwd37pJSBb1es5bhlHcZQPT1ydiAVU6M5VRNIXDjAIcoIZjz5B70dPA1VpHRH\r\n6gUI6gB8F8+54bVFp92uPCO3dp712tHs2T5L4Y1gsR8/1Rj0XoXopBm+sbmv\r\nXmVqGk6RI9zF86g/o6D54/afeG2nnhnRh19oZy9NudZChrc9Q3oOADsRHcg6\r\nMmNIBSiIY0kz/EuKvjANTRaPevMcxSTnT1c=\r\n=xJmb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.30": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-presence": "0.1.3-rc.30", "@radix-ui/react-use-size": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-previous": "0.1.2-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "14897960937d33ae65943a94c11c9d2cbd26c708", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.30.tgz", "fileCount": 8, "integrity": "sha512-NVrUAhC3ynhqnUJiMH0qC22vcGEZ9MuZkGSR0xvJprSqGBZUfwS5TwzEWybiTLZBZHU56bPVm8YD3CONqbxPzw==", "signatures": [{"sig": "MEQCIEWvVLpasE/DwBL1CZI6E0fI3vIvlb0RBl/ajE81/0aGAiAkt/jXHeS0t5EZirKDicUgp+tX0qnnU98frptSNmPaDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoosg//aFPS6zZNnUXbVKrZNszCtd/9B2GllPQxhY21Ltxjrht2uEO6\r\nHa1KUUyhwUwnQPT2O6vMBSoptLMN0EQkffQ4MGPmDc0ZPLqGFc45SYcp4XN8\r\nYkNhfe25BM1DOY7jwQc1I9O0z3eGCAI7d+Qnoe3072k3bCHxj04VJbNMZFP8\r\nFWici11airv3sN9weokIJHmHoHCfr931dPzGTYZOI1mWU9pn6/1RcxLspCDz\r\nWPl7s2GWiLO5MCs15VhwqrApf4Hwt4qiH+nQ+xMa0qzCubHmCOf5G5ir97MJ\r\n8H47+lKWDrYCZ09R+k60GjubnBrGsgU0/hnLXqH6QyfGtK1dhV/nO6YXELwF\r\nwfhBpwRCc7I+UVQjHs7XfqF2xXNllEtwdNK+pF123eZohP+DK1OV6pV0WQsb\r\nVL0Qk9BxrLX0IMAi+HcXOLK6DDa/KiE43aTPLvUlYT8CWDKi4hbNLlbVBfzI\r\nS4xiv/YZRf1U4w95DMTQX0TLoQv9IUHK2+YE/PJ3ufx7mJn7qFK40ke9wEy3\r\nFTQcFMx9BGOHxBFFZEfz+8wXN+/oF1AC8yVmb4NvreWO+c3/QvZphys7Itb3\r\npvU+p+WdPbH3bqjmdxps1lIZu5cMg1w5XkGeX93H3CD7hdgcaP78FeYMjZVe\r\n2Jcg0TC8zdo4K+dbRTRJUFD/nH37nywq3qQ=\r\n=MKOg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.31": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-presence": "0.1.3-rc.31", "@radix-ui/react-use-size": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-previous": "0.1.2-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "91df4c129ff67b612dad85e523447cd0708346b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.31.tgz", "fileCount": 8, "integrity": "sha512-jJNyGwTQ6dQ/LGoKBWtdtlPoIYFCiSfCyDrMP5AFqfxq9R9p49tA+6YREShmYYxd056ho5Q4cfOcm5PkVV0mgQ==", "signatures": [{"sig": "MEYCIQDJVTn8DaWXg5PeDHAUqWA0pZ3p6SUd0DlJhujYmMokcwIhANMifF3WzGAdLprWvKM79nzlSS8A5V1rW7ZxhN8lREk1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSTA//RTeEjibd8R+Tab/wmDMZm0N+M7QkwJbkgp41zb10fI+rVq29\r\nPjYAXbsHV5rWGfftX8aimrc/WJZ13ZeUe0McaXhYk2wbaTRT4iDfoNSQvF5d\r\nLP2nBPIG4m0HfV3Ac/cPLPXNdHcwkH3Zu02MTG7FOENB5pn19KZ6VqmSfXzo\r\nW/sLYALBpvAn3icP6arlDXp4Rq+oXRIf9UGg0OavoKJn7CA5/vhHDKS6YWxR\r\n2cncwbP3p4bZJelASr1tecxgKv2FinmUKFfJZB8kbCCDXiXx3C2/G8CXhXYS\r\nKeL+gx7v6mKuLhvdJqaRkiivckuPLRxKr+yYV1wKQam94nGJft6Mj1MNK8yY\r\nDZJ7/8zENx23s+w78bwCjGxfjonyPjXBjg9HJioMEBrE9Pmo6lEBzGB8YtEM\r\nxEcAsmtvvOPkbDz0V0zpUxJBS/L26aP76zfuB7R7CGvuz10VHXHoL5b12nmJ\r\nY1dzReiK5edvhGabKxsqKhuXCiyGYwoohmsFSAxMv+rUGO2M+kVqDJJkCpgp\r\np4S11iH2HX0uwGNUSJyzlemtnZ9ZwKJjqPzmFtsMEVRL+xMTRmlnxdSbD7ch\r\nKZ8HqK4ApAbA/ljqqMv1Bq3nLdvW7R4IfPoel7l/F11a380+I23jbu3TWeU7\r\n1ctaH7vjyES9fumGWBsjt2Afc9MGEXY7iOc=\r\n=ned3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.32": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-presence": "0.1.3-rc.32", "@radix-ui/react-use-size": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-previous": "0.1.2-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "700dd88c5e6292db8d87b5bf62deba38121080cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.32.tgz", "fileCount": 8, "integrity": "sha512-UclwiQ42uIXpIJOpuuK2USfgQoGK9uY4vgIRhbc0OLBrWhWagZycuAyePNKzBMJwAEeKea01HVQrsZr4OTOcmg==", "signatures": [{"sig": "MEUCICLe0l/qiHmuurmlyO4OKn+GXPzXXZcHd31TW/dbjPlqAiEA5JYTW1RpsyuAyxaUVXe8Oeq/rEeykN5khjZmn2HmVTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUYQ//VgKXOy9flAKdJpMA/mkrkafz+iPJuTt4h2eMZTAWZSI8adRH\r\n9dJyCcJQSOgDNLQbImZ6rKX25ZmceFvkNHP/iTGfKA/QuctKzvJ2adOcVWg0\r\nVTOsb5sCoY5K19m+9CEhqCkAj+55SgTPqHa4jvfxqQNyJsTtWJPAnRqRrxTI\r\nlHe6r3bfd2i93tb8R1RLiNxVx3PT4jYcd4HgZP7qIhtrPHDMrf6feJ7ZhEri\r\nRAUgI/RcJFrX9P5aupAnSgdlmy6oFtSF82OE1kqCPZjm0cSeARp9ecnxdCt3\r\nQMGAYJ4jO1QajKMPTSskg4LXw4yj0cRhoTDlbypWn70Cn5/unrTKZ9n/DEho\r\nYY6ZTGV7lsnA5sQK4CnbzruSmvY4IWnt8JP9jghD0MSUpkcJuL1YhkfqB7uI\r\noevbve/QZeKJ9/3QSRRzhHyhcvNikSoKh0lHm9oBqoIkrVPDRyXEQ285XL9x\r\nbuksU6/JJ3rHS1cQ6RslznN8sF47XeoCdmN/oAaImJ8SSPO7TQJwNgyxVg3Q\r\ngQSf1DdKGABTzj8fNyqOKlb9+7Tk9GSIgp5eWCE7/fmOh2JYOPJIbeKUajYy\r\nR+CG8sJakxJJWnPUn2L5lgdgr8jQJM9j9EXGJIUOsjEZF7ixMfiAteF4HxZ/\r\n7e9vBINX8115TrfyY3WZulvp7jvlPhRQRb4=\r\n=GCGs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.33": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-presence": "0.1.3-rc.33", "@radix-ui/react-use-size": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-previous": "0.1.2-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1a61d86967b9cb4e1f7cf4235500dee9bf8955fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.33.tgz", "fileCount": 8, "integrity": "sha512-uHenJMQuwSZlyY/m/iQKKfZsRDEq8lGFbkiKVF64EVc2eA+o24QjD7BZHGPCglmB3vYUE8u61zjZ3r5MNv6/Ag==", "signatures": [{"sig": "MEUCIG3qKFG+swF0ZneV5gVz/C0OeekKxwvQzkBuKBeH1WhlAiEA8UZ+k4dMUvG/Lvv3dJMpqCK4lbowEUi1yQpzwM6kB3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHb0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryKQ/9EuDzG1a+UlU7M1KOqqnd20EYCzgKt8p55gW6osnPBNTDkXVl\r\nUIOY03U8dZ2oECOLK95EgVbludGKW2Ccgx2eQIggdmSMPZGxUW8f3YygMVb5\r\nmS4G9wJTyzohcC05qzN9r5PLJ+w78IoOBqFkTpeZ27Pb8H2tldunOqd+aa6m\r\nvcT+tWTNyq9zwF6PmuMSPgnyM21F/3UYhwQRtjO/ucsKoNdqUD+FidQoLu62\r\na5rY5JE7DNiAb<PERSON><PERSON>sjebdnfszaQt7BIjXnNgC8w8VuM6LdLdJMR2HZFX6Rmai\r\nJ8moohvCM59mEFB/9bKLZY3yeu8oD0O5YyfmSeNbGNrFlFcfywxH75gtBCHq\r\nCU8qh4m6zV6QoBpPlWax7Z/Fgz3yyhuJTSFm83aUag01cDR235nxfNrze99y\r\nNWNpnVEtKWdCJsks5kch3CjBxbDjkzTS74MDdlNGjBofgHpFn5hF8WYoBR7h\r\n0voNT3DWspka3W4/t3Sl9FCDB1cqDcNrJu3FyQJ1mhPUn/StjO3qNYfKQnG/\r\nEpBj+D3v5nnelsGf/vsQovVBTM8ZoJEB03hIGe1ruDIJyMSIb3k089sFkUJ5\r\n59W7kn8r++UHqsTec2lN3adgEF74er60Wr3vwzcthm7+BQe/Nr2eonEiXU7I\r\nxzARUparg+pw+hb2jmC5QS9ns8xwNKD4LvY=\r\n=V3sU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.34": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-presence": "0.1.3-rc.34", "@radix-ui/react-use-size": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-previous": "0.1.2-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f1908c464fb47622a702fdbb8f3173cd1d841e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.34.tgz", "fileCount": 8, "integrity": "sha512-1STml7c1EosqVx9lLqEd8c05D5h+09CBh3Q6t2qOpNHTA1e36x/x1N0wqBZTyCPELsua5A16GerDQAWIcr1IVA==", "signatures": [{"sig": "MEYCIQDmcGJ4RkiEWo2I1rF189wlruXhcfoduigMC3mDFoVW1QIhAIvBkCNd91UacuxPLZIbs0RjotvGII2CY2/AeR3ZuG20", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpucg//ZggjpDuViZ2fJNC0DSMfw/jlA1yQPxIyzkVunhaHDRUfsEyr\r\nf9Q/K6+Nn+MlblUvNajJHPbrHfdAs5FPTDfhH4tBLkq69w1pmvAbfaoMk2JI\r\nlZQYruza5XnUKenQG1yTuIaVG90wQsM85+ktbHOLnSQ8Xs1mamLhkQkKT6Pi\r\nCjCzgznm9t5bjtl/C6HhQ3TIOtbjLAvJhYCWBockiJ8UnSxXzi0FtsLNpVc/\r\nmvTsUQf/wnNP4lAn0OZz8MH1SP6IpCoRWuf294mGGkSPqFbarWDiGhLoO+t7\r\nwz4l2PE6YDmL1/Z2GKddIwrnnRipI9h0fdX+qY7XIoUFqFu/eSWtJPQLb512\r\n7rdC9mJgx4D1sR2lErU0FDWUsWPW3j0kH83uQJbdUwdRrxtURdYz1J+8Iqr8\r\nruDQZa5girWh+QpWhu4HVbLBI34GIxUXgg1UxazUPeuQGPkmGCsbE7Ht8qIy\r\nTqTZVAKZrXL9vtUktHk0gVKqv5+Ik0nk+I/thWbhpWbaFUbyi3Iu8/zaBThV\r\nv6SyzbL1zHw6TLk4efr0DyYsfvXHIMD8FH6hwfJAty93QmtJbRAmRW3u6FBy\r\nrgnhdMT1j3CaQefI0aK+frcZxOceyLQcG4YXR3rZq8mu3FP6siyyNTVgFYGe\r\nXxNC8WrbQy+4rpM9QWe33hsUxEg+8fll+UA=\r\n=N2Xb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.35": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-presence": "0.1.3-rc.35", "@radix-ui/react-use-size": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-previous": "0.1.2-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a15ec07ccc5364cb327cf23c77e1f54237b504f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.35.tgz", "fileCount": 8, "integrity": "sha512-kH9suvVX+aASZpPgMwmXjqogZmtf10ndD9JEkdfwIQvJQGzkpowBdPxahCOnXoy8AlgZvrWRa4gBecw7WYjA/Q==", "signatures": [{"sig": "MEQCIBKgof2QSuKRIHyHwuSAThIEWamLmHnTm/Hv47l4tnTHAiBTNTAIhODIYc2fGQOyIcdDK/lCnFIBi5pXZx+5r+6QCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHmw/8D+q2wyEVa4Y5eEM/BcoozX13fhkK70pK+sr14T+eEaME0HaF\r\nj3rHrYWAgDUL5CqNs9Q4t2mOhPPELYN+SQx1beUYHIwClqoa1Jw/gsMZ6Ljv\r\nnwwPsxx8nRgm84ixjCsLKbYptaEkSW/TK5eyC7MmkuyKJAMSv3Eb0mGlfXcZ\r\nTbi5r2R6ei5/FQMCsTGdDBuyB29GyxlpmPNRm4xTCS5rwOrs+EjqXE/2w2RM\r\nG/z6RtuhzxkQQnGehgSQ44qNR708grD7TaU1Nar3xfeTy78llt+D4Mx3uxVC\r\nzOYVyjmfLVezEnBj7JrUBbNAr/8DKjLleSQpCsLNjbGc1OCSyeRAGUSQjWU1\r\nAiQJ7hqT1mN8FdMQI4y/jSChuoJcUosBIJ1RabYyXMZMPnV7d5e9X74HruN9\r\nL8UkJALHt0ORFQ8u8p8v+r7QOL28Vn4OKsWOkFmpp6F6T6XGRzn4jQOHRfNJ\r\nKpAbT5DCSauC5Kd+94XtDCWu6Rb+uwAeKVnN9z8jW7xev0kkSOGJPUoM2av/\r\nuDRHra+cltaQojoBqoeMAuZyNMHdjiwcqM2HTWFWKfl5UKw/WUBwCN8QH2KP\r\n7Vq2bZTQAdHBXcluwgfl5hS8V0LxMGL4UJmoSiM18XS1gwQMT0WkRZxUBaI6\r\nt0iO4ENEXzG3tzLPccDidpnKHx7qnf2dZwE=\r\n=iB0L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.36": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-presence": "0.1.3-rc.36", "@radix-ui/react-use-size": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-previous": "0.1.2-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf59d42d9745edfb21d86b2e80038749a7362a5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.36.tgz", "fileCount": 8, "integrity": "sha512-Vfi92cCjtCgt2twhhSl5ItwzWC6FQQeOB9ln6p4MIahRNroweiLH8FAL0Co3Y05blMGD5bob4wtjuQsRNoHQFw==", "signatures": [{"sig": "MEQCICa5FTwjKUG5ShgYZdQrPsLsTor9sLM4MhvKfIoMBViWAiBh7O2wg2HCsp+3hRrnOL1WFMsdB+2+F8Z36zCK79yfgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0ICACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvdQ//VBrHfKDCepQOpcP5PnxM4V6JqQgB2ToBqFPOib56ykIfCrBF\r\nUOA/UMMIoJZrsQisoTU2S+H4pXZFLTjseXtZLTAWtKng3KAivbfPYKbt/y3r\r\nk3c4DxsGBhB3e9jQYgQBo+yStWKEgBpJBNj8xiBFnEuFTlxz5YbO1WiVGVLZ\r\nv34m3pTZ6fiTPOFpWZ5OXyvcN3FG/G/8jyfsy3kAYEHL9nPgpYNW18kms064\r\nRys/d7P1wfkxGsCYs8zGFN3mjj8mHq9vFdnoUw6CDI+kdVn5/0s/3+re8TC0\r\nFnhmnKQ0QHL3WtnArxuO/RCNkPV/jtzaPorbNVOAd+VmhvXk87APYBjJuxDx\r\nRwP4se4HLnMwaD+IhmQt3uPIIA8sZWN5rVc4khhxUJyA05nDWgJbDL8H3dM/\r\nSxh4PJvzhOT35cJ27uYswDa3YHjYRrU2zbQodw/cKydJkwv3qT/Y6jQvDgrr\r\nIFlPaRBFxn90mtcx2HiHcM1Am0D+bxRPT/KyTuKJ0gxfeJD53KjuHGkSy05c\r\nIdltge9A6QDOhgctuHbXv3p+d2LQc3Qr53XE02iy837y8Iw5Yz7yfIwfPyAu\r\nvuOMMr2FSSwY4QCpUp9fUxOUIofd9awnklCtsvo2xzQXp+GhWaZR2cy0QZBw\r\nJ2B09grbFCeQSt8WvljqA5ACHcx5q1XpFEY=\r\n=IDMT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.37": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-presence": "0.1.3-rc.37", "@radix-ui/react-use-size": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-previous": "0.1.2-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "71aec0a0aca2dd86af9f08026cd262f40dcfd236", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.37.tgz", "fileCount": 8, "integrity": "sha512-FNauF4jtV1RY3SqaO94iWmFGpHrO7KXMTknGplFn3UvUShko2kYo1ydhQmebEbic4kShXyjF8ifrvusSVkSoKg==", "signatures": [{"sig": "MEYCIQCH8P8vj8OIS9AYD/H2B0STTWiBza5tgKfFUHIiaRLnHgIhAOw1IEuC0OQ5T70MoBEnKEJAEW4n4sIWunoDCSdcA1jM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYig//RpftYiO0GmZcHz2tNi0I8R4m9w0uaGf9t+yTuNBW3GKlF1o8\r\nu9eAenJzSsdeYsiyVw90VUMqad5H4YJ5Y8tb5P6bcwuzeEnVVF8bYmYDn4TG\r\nf0/N5t6ksmC+TSnbDlpTUrewnKkPSJk3zoSR7341bRgTewAXpj8x35G0u91O\r\nglN1Iz2/BfqcvdCGVR4OeCk3wIRaBMlxdHcWn/OxvRTrdO74osgarQbkByDR\r\nju6EQtRdDR7o6g0U+WsfUX6a12+b5IhSoV+OX1T+g9pPmA/+TTreXGxwpE/N\r\nOu3w6FbOknyYBhY27dY+rc9VmON6yjkARkY5/6kG3XhsUS/Ufl/8wyhSOS0G\r\nqTcQu9zRLXtf/r1o8rB41La5a+WNNdIYAeU3YP0OeBpWpBevyzjNqEFsNE/p\r\nAgCHsBN2nTARmYxzqDcjWQ710eWq9XjJdRDV2/njrYUY3ahxkLXCFewQ5iA5\r\nlTC0fW5M5LjR4i1B3b8d6A2LXDN+EZDqMgRPSzjVN2Snid6ZNuLfSRGEcin7\r\nMhAF9JkG6ZutxtDsxGxjlf77LhtuKndswEOG6diG/nJ7IJYc6MwMtbcn194i\r\nUOdAjSd4GFvVZQZKNEb4EHSEG8Euj7C/Q2A8FjTwD+2/jyD4DmQ7GFyEcaFW\r\nnp8UVNpFk9NDox9YMXfIO5z2kzZ3vE+brIk=\r\n=W3JB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.38": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-presence": "0.1.3-rc.38", "@radix-ui/react-use-size": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-previous": "0.1.2-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f89dacdf67d8bf6acbb072be39ed2aa90efd44bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.38.tgz", "fileCount": 8, "integrity": "sha512-y1CHPic2KanuV8Ts3nDJItnmJrJN20IX2MZgP2rX65SNy1SspNxtzT2a7+jfttqSD+YfcD96hDXWAyeksMcu/g==", "signatures": [{"sig": "MEUCIFJo6dUcO0zNMaRGvJgq8z89RoxOjBlgeFcQwdPbwccPAiEAioddsGkxmZ8G7laUxJhn3SO5A57pixY5z2l4esWLNZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCvBAAmUxM3sGp5qVZ/4YNC5OvXOiKibbia+7LdYRBUKFPrpcO6PNG\r\nojB7Y+JhYPM/OO63FIWvnqskp5rB+Q6pKWCtsPIdvxMKF2xanZvpNkVAfxfP\r\ngbf4nekPYX2B9fmmza4p1vLZDMEiymrxbgiHGgJIc/JDCna0nsXSvig7b32F\r\nhAKxqoyaxg95uVukdxDnYjb/et9++d+Hss8/vNCMJW9361C9Ad4IAu4dvp/J\r\nQ2kNIpnElS78pp9R+/d5srGrgupjRc8r6ygsxJBgHmI6TESNrIhsyGqGGrGR\r\nulLYgQHiHNsi9weNBrPOlVPIgFAlh6gfCV+0ISpzslAkdMsdJ9z7pc5shRaX\r\nYAQC3TEJHc5t5xdJ7fBN0FzJymdz1voro5u63L/KGXWx6MeBdEvBrtQBqKKK\r\nD54WHYatI2Hij/2JQMyv9jT2pNotOAYq76bOtvyb5QxvqePLjsszpkUXR+Dv\r\n+40MaDZ045ArU9X/B2RP/Q+Z1W+Ehbnc0pPHbZTLpBqfhCasOnWq8gWT6MAq\r\nnSMU+/pz/FpiR+qa/O1TYcSRcFos0JDhXQEu41K7Q+j9aNY/B9F1rLqsSIFP\r\nfWlVVNBfwaYegzhPlmELlgoImXQNBBDyszKTLLRnXbB56swLQoijTTY8lJp3\r\n8+ZlIfNQ1Osq66mJDvokeWkBcQpXxr9/Alg=\r\n=u/Sc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.39": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-presence": "0.1.3-rc.39", "@radix-ui/react-use-size": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-previous": "0.1.2-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f97418485bd6ba12f5f8e90546dded41e99356a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.39.tgz", "fileCount": 8, "integrity": "sha512-LRYfU5bxSno8Rqve3KJtN5ZT+i5RRvSS3bY3t0ou3n6BnkCFf78g1WsDJR5cnH92OewXzbTsgYz2hGy1CxpBuA==", "signatures": [{"sig": "MEUCIE7lolOo8MnZ4LvVhots3jwQ8RRxKhbY+i15Zvn58Fm3AiEAs8tZEgVC5snHYA0Cp4PdoW5dL7Dla7S2ZEZ9hz0fkaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa0Q//TcVYVzaCYIDIzziThU5hRnI/SQF3yDTWipmdxpxVpRMRXgr2\r\nykjTpAwYHttYTXB1hlSmOyJUIo3BWHp7C6Wk1VDYLPyqINeDCusAlKMX9/2E\r\nOBO2B7Y3pAt5rpu6XHqEOhjUyx56TxUJtVMDjOIrWDo13/rSiOfJZQv4oIuW\r\n1i+nWYio09Kd4At8rOVs70s/e/sm+GwH/bk0j/TxH0RYwkYgJltvN8tRBVJj\r\nTTQinxv0fqstoJ1xWYf2fTsCDROFGDxBxqfNJehhp7pclYchAftaho4IxzjL\r\nktjfwFSGKupmOJdvO4t1UIc/o+Mu/IcMsQlt8Se43Jmt33TMu9kF7yjiSpVh\r\nRWmNnxdz16/s+CVeKJCpIoUqVoAJyJTpVisWK4CUuCMA9OCTISvsvEX7wk2F\r\ngGF/lnU/EjIEjyQj6DTmAWx5GoTcAJKZqUp62BmE3PVhyXHySePgkZIPyXed\r\nq56cgPbOdv4A4tuEaboKdTjvpnl3v6wi/Yk/VI26ipCJ1NpSOuA7tv7hTyVZ\r\nTKioLGd/z2zYV619fv+Bm6GJSQViDg+z0AvS0GW/JbjjTUQ/1KnWwvrskxlR\r\nDB5Q+0VV7yGmEVGYPL0lQCGQbM6oc8Atn22ceiNrD8FrC2Vrc/y3G6BtxJTk\r\nfhd6Rxat7fZ3TMYvRLjookkTZbq2JXig/Yo=\r\n=f2dW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.40": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-presence": "0.1.3-rc.40", "@radix-ui/react-use-size": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-previous": "0.1.2-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "78aa908d6c38b4a1375163967ea1a30dd3988e70", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.40.tgz", "fileCount": 8, "integrity": "sha512-QbMd8xEzEtLHd78oDUfdVRVAZVGYB1PcDnZs5muxoCnIjzxWfDXLgmy3lik8QOhOJw9aXGOAWUy9vUVKwkCRVA==", "signatures": [{"sig": "MEUCIQDPbJhnfTs8C0lTIP3BebSr7IgcMIWroOx4kzaHVxTxzQIgRYTxrLi/5AcGqUzviNxkJqDDdaE932iHCYVFWk0Pwtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3Tg//cDDgLvShPq2WUO3sSKUrYzRZIDwyWk/poXp9zsnKvWPz70ak\r\n/8an3xdWA9yS6ojFELJ8OEOVXl2SB5JA+zxoD1fYiWnTvybrCgNZ4mhAhXhE\r\ng4Veahodk3zmli9InSOEqXgBp2NNVJj9PEPzypGrjazmFMmBug8kwtizT/0P\r\n5r/+gXbFA7mahXOI8ys3dLVGd5/hzbkHMkUTVQXbZfpEq+VOd8aXWLadhKbc\r\nkTc+kO0Dq/tL/mblpDrEgmabj+ACayBmxmv+h+btHLqNGezBaLKPBN1FmZyi\r\nfWz2dRN66KbIXmh6jexT5m4YNBOh01/nXgJqrxwy50uZMBVNIJbyZLYxsCRV\r\nN21k7tcaUo4DbuOU8J7zzIBTp62j7bi0FlWKAq5qO9gVRSqT5TASYiplAPWe\r\nPhJWHSIIvtny3XmDyGkHfx5i6vqoizhffsz2xUUqFWi6ue3KImM1vOd+lfkq\r\n8uacoc4HXol59GKavTPkLn0sP5t+6A+yomKhtAv84eYicPXlLjaUCyUS2M9S\r\nKJwbforc8EDZjbKe8rSyzvJCH0oId6OQbaFLNDmCSZlcVvcvrgxxzv5eX2Ji\r\nbO5cfO3teMHYn+eGqmXp8lQs2WcFMVhNF4iPgID+MKEF+dahYo0KmfLKWSZ9\r\nhlSEgqWIiEIwm0XnFFfMwzyYvgicmAn/opc=\r\n=TVf8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.41": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-label": "0.1.6-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-presence": "0.1.3-rc.41", "@radix-ui/react-use-size": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-previous": "0.1.2-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fdcb593bbc9c32bd6e4c91b7aea4c23ce8f3c179", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.41.tgz", "fileCount": 8, "integrity": "sha512-NEl76n6qhI4SswFmIpABSw6sq5TAnbYQdTUBpv9R+qynk3dpvutYBF90etRmlOqYBYKbAPUKZR68U6+A4hDC+Q==", "signatures": [{"sig": "MEQCIBMh5MyYGteeTr+WVAluqPfMtK6HOWdlEl7AigOrCJhbAiAedkIju0apoGInKsA2ahm/SwOWkgw7o7knXE7i3QCC1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrrgg//avsWLHZgpp57eDQYMz3yyJ03NmZ8TKpr+D2By/mZAjvkFe5c\r\nAYgtEf9QpWMDp+O5e0IZ8TeNPOu01skmeuETWhwQFt3v131h3LUg7/fpMgum\r\n7reiB9Yk/Klu7xIKsMSs/OaOzXh6OUtWBoPZ339gqjzZfjrS3Ut7BNXWsYdj\r\nfwxdxxYsuQgNZ70jq206Z7/HCfpHvPM8EbyYyDyhkzFGBS+Sy2EfVglfbEtM\r\nmXzGqAl8UWD/JtEYjnu2TGfppMdQ67rn+4hvsJ/s3VusNrJ1HxO61mdwjewo\r\nZlO2xAQbAi+tMObPJOsrWDhtK5dGe02BDAzumfRtG9mkZOJcDb+6661HlqgQ\r\n45H8gHOoq1CErM4ZJUrSk+fuhTrx/VAQsl7jYNVSTZuxkEbGBMAQh6qtucxA\r\nNr6At0mtrCJTC9XkL2/RLwJ0LyoA/jYGnkCBN4Vb/rtWE0s2aCf05hGRwOXj\r\nCuWXhZAwgXDZuiM2bTxzbp6VIe3aWHyReAzx67XlMdOaAbM2sKyBCHJ0iN7Y\r\ndGgDzS3mXsB7itBmiV63dsyVHl6QOSX3blW2JjpARvXiZKw/7+0TEv5Ftws7\r\nI8L874wnTaTzXjhcy9Y8GNrVuMEzleQTP38WUreE9HNsMANaicpBCB7QWhKB\r\ncPOGnDFD7ogrEq5kZKgdE1c30PN7dcdGWYI=\r\n=WH9b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.42": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-label": "0.1.6-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-presence": "0.1.3-rc.42", "@radix-ui/react-use-size": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-previous": "0.1.2-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "08b7e2a38e04086dde2f794ab9469e4f49392656", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.42.tgz", "fileCount": 8, "integrity": "sha512-jf3lujp8bhVmHW9N/DHvnvdYIClRgwfq7V1mRrmx1WYgn04XYRf7b8B/4r9uWv0kRbIyTT1d2aQ02i5aCbPcQg==", "signatures": [{"sig": "MEYCIQD8aocUzqu9zyZwzgHQTGl03vSH3cUb1ncaYuv0/711xgIhAPzsDT6wFkG8VgGVmW0xzZwJU8391cggshHQGQzk0Jn6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphYA/+Lqd3guzgcPEe59GtSi01pDYAX/FfnQ2y+Dy2ognkn3WAdwL0\r\n+Nl/+c117aBL3PYDa+NTy+K4PlI5KNEaGhimlIGpb1NjHOxfhVT/cuRTnvPO\r\nQnvCfSdR9zEyoqLheGEq9husORHy/fcoJS4IouBqEybcEt85xRS5hxNACVQj\r\nJNjlkQo2R8OT6fin79PnXpHVqPG1EUr447ahjoAmpQQUPk31d2buixF8vjX8\r\nm4+FmnZAB80DS0hUIm8yRdz+N9kZMfV7JxTxoL5ROvhWhAyC3E+GrmDjE4bg\r\n3eyBE8M2nWt6VsTL23mak1F5N3ZIFFjlhoolv1V+9rZcQEasWCln+mMxko6V\r\nrPHE7yUaZmqQUPUMLEZhGxNvi/trvZyWdI3ZoVNJN+2hCc/OJVzmaBb81GDs\r\nWjsd7FoIkUfdOJSLH+J7ql3K0IM1Obpx4mdJHimYNuAK4c/H1U2KyphzuvQD\r\ns6rOgEtGa2tyniIHa8GwTlTehpPgJJibxFlWSwMnqhTRD+jv4OuC+/0rF3H9\r\n880Cj69pm27tC9JZaF+a78bPxQty1sA5Tt1c/KAN8yPCSiWB2Mdbp0X0Bihy\r\n2ovdbpP//yZPYn3h3OAjwPgdGSpHTYdc7m8Ho4U29u9Bix5MxrM4SE/vLOQ6\r\nJY4+HutdlB8ir7+VTTNjrITG8jUBq2TM8cI=\r\n=GmuV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.43": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-label": "0.1.6-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-presence": "0.1.3-rc.43", "@radix-ui/react-use-size": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-previous": "0.1.2-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d935b6dcb03c204c7e3be4e857a586b081624866", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.43.tgz", "fileCount": 8, "integrity": "sha512-DW58i0VxUWuVfQ+WuQ4a64TU34AkhL/nPBbNQJBJmYMRFEO0/ZyK4XrW+5MjFyIKLBExahBNaHDjn63/Z3BM5w==", "signatures": [{"sig": "MEUCIQDbWiFwtYHTnY4GpwzIMloLzox7ZlCV822V64/dtqu9+gIgFOxRdro0SeDH8WePBPZhZ7ALByv6g2tvQ05uILRlZ8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgERAAilD8UMvIVlHlcD+rA8moCPDne7FOmlbXckTfm7K6ZEHNmyQG\r\nifqkqp/e37U+yh53zYDIwZoWiGzlhScbn96S2o4b2kq/Snl4KceNCXxRgnkf\r\n2BlwhEeDJdXErUA1f1tG3bs4CGorBA85FrsLvx8LQjcKzqLBS/izfLN8Ln2F\r\nEhC2BsXtbFHMl+C1l7XX+w29IBSk/OfeNy/wwDvlPrbmXg2PMYPFlqCQVDG8\r\nogbCzYnISa8MU2OlVDvxORZM5J0BSWzyhiFLT5zQHILxwHU7ws/NJc2RJKY8\r\nSpugPnCFb13MgGxu2BH9jgjZcNA8boO6W8clThZAuK4yt32rVGhK5wFEkkQN\r\ninmerajqtHh/lkvt8ZX1T1MdATnnV43CDWAWj9jtapFbYEOlPxYrWTiIF7Py\r\nnzZm+2YlErucToC4ZTyMNYKCL3GgbIc5ob9ATnuFpUvMDolSymGrMjloA07h\r\npIZjXhpco7qWuIK35Mvf8Zz4qY0Ewf7/Qbi8BDod/tE7y4sgQVMxHMqMPpRT\r\nyNZLc/RSo3W4eo5bgMbUVlD2y8FShYv0+oFhde71/Syj0iKkmOXj5juGFx84\r\nQLTeN9GWD/oNFNjTu06JHNOcb7Cw/e6tIoo4W7ISXb7mzzuxFncpmkAbGGi8\r\nV6/yk9B44I7z2VwSji4oBWR/ulsYfTxVgg8=\r\n=MQsM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.44": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-label": "0.1.6-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-presence": "0.1.3-rc.44", "@radix-ui/react-use-size": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-previous": "0.1.2-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb2d89d183f875d9cf0b53a97ba0721477960b7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.44.tgz", "fileCount": 8, "integrity": "sha512-8od4zPfYUf9NkjQhETz5InrTbtTzStI1Aphl8RphJlerRV1NSQycyGxbgWos84ZYpBPlX+7qNfLIPs2sqU6FEA==", "signatures": [{"sig": "MEQCIClDvgLx+lwiInxkMgajbZgseyosrjkGOuQ0dr83bpnuAiBTqT3Smnwbr+udko0wXRcBksefq3uFX0xzJoWOpn/81A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XF+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYBQ/8CV8WVVl8moR/YHiTg2Z9yNhP8c8BPToaO7O8y/3Z8DD4q1vl\r\nVc3ejY1owcQB63BscNrgiB5znKXSftdOexuMA/iYYQrT2ehQg/gsMuepEo43\r\nx+D1gxPvERlWcH+AwWOjCig4XrYEB4EySWXIv5H8wpjapz0ENEAS4jL8N6zk\r\nc3t+EF3iJeoTIqCQlO1fJ61v8tz/wF4Wx2gT0xFN4NgreiNIW11ZcG4xKzK/\r\nUPxz943n1HAHidil94JvQ+682CFrNhrfRwmnOXaP9ZqarOjEYYB1OLwlPx0S\r\n+G54fSiqxxjSYYUtrN6BpmV5hmfwMY26a5pCdGrtbT7tXo0zUgGsbqQEDpa4\r\nuHjfY9U3r/3hgcR2KkUBmvZoZmjF8kxa+SysnO7UqQBGyeaoiPzVI0VCMLbN\r\nJExipXtMS+iXP2pBM6Qu7eVEQRPoz95DOfb1SAluyPXlSGjZ6Y0eYUWTJq6i\r\nmiSyvfoCIruUV21+/WIBh17bXLjiRsVO4Ds24H1whQIGnLoYik4Z1EzORoCI\r\nC7Ei1rugTibEUEZQY4fo4Z3rciavCRie8rbqiKETXaiIOz1V84tfLv6jrEtJ\r\nYrhnhikJt9fgvzLlLUu5yHhvjMNckU6DK5gSeyyBESTKB1iEKMSoTfq30zLU\r\nbjDNOvm68qGuxnolqQ+EuIJZnJvA4ki4Uac=\r\n=uw8+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.45": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-label": "0.1.6-rc.45", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-presence": "0.1.3-rc.45", "@radix-ui/react-use-size": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-previous": "0.1.2-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2af5160e05e03c663cdc033d00cb53c0f4ede707", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.45.tgz", "fileCount": 8, "integrity": "sha512-vSXS82Uzaf11DNKE8w4D9vl6otXOsjNGB5jt1vwiunreMxgp+YjCXtyBCf7XjYYVkm2txh1amhhWffYF2p7Yow==", "signatures": [{"sig": "MEUCIEoa+qHeD4cilzSWWecieHH63s6vkcDJQR26BKBSdxIhAiEAttd1+Eu0T129/y1hXzv/+OniNHErQxLMhaDahTwrGu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGhA/5AGsDfLMxA68/iPlI1rwQzyKcsG5IHlDMkTbHTlROcIRPMaA7\r\ntZxLCt0RkdGD6PMNFK8CLO6zwlV4bd0JxHoFSLwDb0k10m9ykX76Cyg2s/Qi\r\ndHp07N2S/NnPXAQhYuOXyxWCKoZNBE8BFRud7QVTxXM8YtLUAcCkGnGVv3jX\r\nIwg/rXKIvmAHw7943dUH8R2x6FItSQVP/Wyas6OvcAemA2GEwlSSP9gApZOi\r\nZ/uizwRh+73PHZtJOdo/bjl6fPX7fFL8SJ1M+EvJMwDapZdp1u3lOJTxclBU\r\n/SqAP1HoIdnwH0RScOVJ22yjX5xnHGjUdQoQrIg2t+VSiS9Bhphrh/5U93Xc\r\nk4FA1SGzY2ZezZRVuv7qgm/5ckiIrKjOzndDE8ukYQqUjOiO7LmlR8oA5EGZ\r\n4W50lxrLGH7ByshV8BNZ/P8N6G0aSK5E6KVhLjVVbIT0ey0tk1f1Mz1cihY+\r\nb0lZujaNY+LjAX0UwlxXVMbz4ufVjGOwKq3BNBv8TfYTg2nvSZJKFD6+qH40\r\nxQn0npIt1S5y2xja+SK+80Lzb5y9ruY0Lp9quvPqVv4ExRhppokzakXg/2CZ\r\nHgvRKkDh3DGErjhQy+pU1gpEmAoKkG9Cs8P/pTTSbki8mvK3+u90+E/DASv8\r\nJX2oNokpRFGpFW1ShhmJVFGAvOgJNpRklz0=\r\n=6tDa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.46": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-label": "0.1.6-rc.46", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-presence": "0.1.3-rc.46", "@radix-ui/react-use-size": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-previous": "0.1.2-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4bf155ce0d4a2f7283441e78c55b970efbaf1ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.46.tgz", "fileCount": 8, "integrity": "sha512-xEDOs8Ljef6NC5oU4m3L66JfqgBs6b162fq/9CMV/7sQn+9bPk5l5SNQKlKKyC4k0ePsh5A61yuor3JYtpOt8g==", "signatures": [{"sig": "MEYCIQCGxerpWrnnfezkbW7BFPr5TPMKwB6Ybnley4FreuApPgIhAPavx8u0gj5GfnLiGIW9vysmmaayJbRjSfOea+NxciyR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqozg/+JfAiAykaOnSQ5unehFDgvGrpixabpBMl4dqHPGKq55acBAop\r\nddFs5ukAcwSWA9BMohCKz0OM/DBxWMFfIjdp4ZtYfEB3KOJir23r/1GqQypQ\r\nci8azDfv1h7vGm3ZJAwDgGwhPRyjfT/z0BugK+Al0MK6/zqxdnV3rklWr0d3\r\nqA0Ge72nmm7Voq3QKbTYCweJw990PK/stuKwWdFayN3io2lqLf/RN00Ycfjf\r\nBgfbahdBIskV0JgfSRHvZb741FkWJzdKYfr8wNIneJ0M31MXPyEw9Av2ux0d\r\nMcAnkYf6lHT6qA1fVM9wINgx+g52i3WVnMjyUwHDvj4T7NH93B21Tvc2ydJZ\r\n3oYMzxYIr9+X+chZOPj3Lyar7bGZIVtaiUUD30OrpywkgDs6Tzbm3KyjvQpZ\r\nA+PF8liCORmtbuVXSwYjEILQKjUdnu3nFsRQr6A9w4RsB0SnbvKPgoV0P4jY\r\nkLQbEifVQjV5LAJh4bbg/qi73SMMSpB/CzA2pRNkue8iFe1LbIbrb8daJfg2\r\ntJHdW2JAOPX1OlVynG9//ViiB4kpathQFWqWQJ4gGXIMikgHQ4s1IWHyt32P\r\nKKOddHHCneZ5a3xet3NeiH0pcvhq6mWARdyv7l0dhN9nkGt+Ahb3DRgJ9oL5\r\ni46cAGLwolNX3Fz1fhGi6SBj+4NY31CDcR0=\r\n=EO/9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.47": {"name": "@radix-ui/react-checkbox", "version": "0.1.6-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-label": "0.1.6-rc.47", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-presence": "0.1.3-rc.47", "@radix-ui/react-use-size": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-previous": "0.1.2-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0e22f2100408e8c895f60347d369b757dedf213", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.1.6-rc.47.tgz", "fileCount": 8, "integrity": "sha512-U2gvZI7qfPHU3xUw3881BqL4toNTRCXwo16SgGTleX/7GndY9uV8I0cfCYPGBOntQdPu0d4VhuXqmsBytXscMg==", "signatures": [{"sig": "MEYCIQDoXNQBnt3GLS5AYstrvwwCwzRU+3pztadv9/dv4eXxGAIhAN6fPZGvdhLIFwiiUXxphzOwusLspvYGBlUSbWbZb7Pb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNiA//YvvYBgnhKCjU3d6khLrUvyar24kadbj6XomDez19PR0Mkaau\r\nBSGjVVwyTM8LM2HmkeNdkq8ulSRShM4fqpBrZvKdJV3CqGtPfz8IWFmERlpY\r\nI9Fhv9wzTu5GfWubZizpnZyh1ttm9DhOCgK6IAEWoCnMvhDuQ8upcDWlPJZU\r\nxMMbAr9Eh4PWOjjObBFG6pgnLhUD9bxLGmLtLNhmDhSCzSZmSfgsiF0DT/75\r\nfFN9+HWlwGm7jTYKGHnlPryhDlcKvgreNyonN0dkxB+kr8xmDYV74joA3Cau\r\nsEJCVLf62TT8WQtGH1CkFNx9uM2DhBpXGCvHzTR+Hu1OXTW9Fnk2LwR6/VKH\r\nJbdH2oHyDuH6malCgoLPI5hGZhVU27u/xpOTy2CO5/TAnMM/oiVXl0Vr6Ndv\r\n7ZsL9m8OD7H6EZzMxpZpiwVlBqNp7Ky6di9rl5Xu6NNrtnMJf3aYynyQdXWm\r\nScByYimVoDy61pRpLbMMVEVoh1R/kbXV0zv1zH6W5ICNtTHkVBQLZ/kW3Uf/\r\n5ckxszCOYRnwSwPQDuZrQxzo+G3a1IXrpNbdeRJwKcsykGj7OUGt+AxOrtDH\r\nLwMTUpmqR71ZhB9yQfW1glKkvi9Ypc8IYCWL4Oa5xAKJ+hUlcUBDV0SUveMh\r\negcTKVNdBaLknM4V5koVsPumgHYJAJHGqhg=\r\n=/I3m\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-label": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-presence": "1.0.0-rc.1", "@radix-ui/react-use-size": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-previous": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "61c3abc7c816dbdab6c36c911f2f7ba862892f26", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-tMVpBULTnYTxrDKhFsECWRMzwxgcpqI9zw7kcwoLseIiQNYBKcUB/oJOx0JxupCd9IFhtiQfk8jKZyx8VBmYMQ==", "signatures": [{"sig": "MEQCIHAsISldLQISXAp6nOkR98VptqbyRHdGrHJQzSY7A7gAAiAA2uTGutmYTpoe5wY1m2MEjB5lMmV2VGqJxK/55PDaIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Eu0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH/w//SHMaIsN1o+kTyxv63l4ECYE/0kgqJ/yo/F8WK5mXj4K52LDi\r\nPXmM7lynNHbO1Ppwqk8lYoIVgogc9A14KTsQZytFVBKkRTXLCJQmed2EAyEf\r\nvAJKrpZ+1w4gCcfcO7BFB+RnCyI3y3VsPZHO0u6PaTZ4D5VWdvjynL6zgM+q\r\nmeIP0Frb6VzLaIpWkF9S6FgiMwyuMO55v3/oD2wVvqBvEg0UT6t4/n8c7Dzc\r\nSzNad6E8t1wMqjEkPjObed/8KDGZli7Mv9Xnpn9mfgbNF8gvXK5UnfOnfeev\r\nu0eSAbbBxeIOSx5SqeWmcCDi3uupg1E8312+Nd3lOeOgqVBJ7GYihM/6pUvN\r\nZomgVScoOS5qKNoYoqiEQwX75fZXbtX2S2tN4n13SaUatZ7ClJaSpnsQK2wu\r\nTNGT+NmWlxoR/XtA6vxADqdYMAP5l5xU7Zjzrql3A/R5AXMkvPKiOyp7bfFi\r\nVTRXrSQ8UWXnRh361mjDJV7G3PkiQ6a2L3CGEJhY3XJbKZ63lp3n5MMHdT0t\r\ndf8SU36IFEkFiwnLeGUAh3S/mH+h/oiJChJMaMID1e832q6wCeaZ9fPDW2MI\r\n1DMZm0rLEjoxbGwIE2L1OzCz25LNTvf1muvlDRupizFlOJba9B9kTEgrMRP/\r\ntMq3wm6YJ5oRgYp1xBG6uvkYtafKgOQZ7QU=\r\n=nVhD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-checkbox", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-label": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8985facb0cfeb740c4751091b8ee42d5c3f7e5e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-MoboEfb4O6Akggz7UwZDKp4p9YR8+FIAmIa7UEAiAxfbTArIgSJqJLaEpdF+MPR4Uk/lSGNW727uvugef/UqWQ==", "signatures": [{"sig": "MEUCIESc7rkK3sec5b/KHaENQjXgHFbkIXPNfkAPiDiF3Y4ZAiEAh1aqZtdYQNRRMcyyKRWpd7J25CMZDBNwi3uff7RpcXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR6BAAozWpYXijqpuzOqIYzIPFeAjdYsZ7Y6sLM3WI5TFMnO3/4iIx\r\nnlF87OhUfFqoAsyWZA70wx2+3ZmOclav7ZTwScTlJueCooZ/POV6I/4xofyw\r\n9HbsyXunfTWPy0wF0iTtizeXm7JmhLTVOVsl5rgUQHCdGRTA7UA09v+49xK4\r\nzXK9wwKN6n6zlvXFi9ADc6ElRRe7cLI+eLrpDxcvsT6YlyPYtN96IPW7oEXO\r\n4SekzC6+0lP1WN4/M0g3UWBhZj5vZlByRVhZSLFO0HwT15bD/ubKEi51N5YN\r\n9qXDRNEhtwSNyaZUN3m5lX9iPMun6uzYqaGBygSL1SkPby5n1K2VzPR1+djM\r\nFA709t3AQboo2O80c1146ZYPCGYPvfIwCRE5RTUzUZQVeF6oyDmhHTMP5COn\r\nR025bb0+qQTvC4wgRym/mojqdklsqT4EsrP0voSsx0No8xwzsw7xX9YURK/M\r\n1KIfgrghT0uZodHiB2yu4ROlqjKJtt6bTjWAGEEAAn7MlYl2i1yn9EHCMT9p\r\nO1b6TaEWGd66eBin4/TI2e8y+5PbEx4vLvFhWEYxWngSEkzums+lB10iY6xD\r\n1AR0ik/rmNUKbH9T8CbC1qVVlJYWr0O842zmiwdTQjO3RJ0A3DZPHQa/UWC1\r\nuBYRB8MRy7MsfHCWettm4PMPdZKkIK+sCPU=\r\n=NF/o\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "649b3a22cba7b5ca25fae675c8ce6dddcacd8d21", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-9Dpb/eyz62LhltSC33tTYdQgCp6IoiZko6Rq3ToNfiUAHKJbVXKzRfHn/3dv62AvmGo79Dt/2zdQTKQ5XEUe2A==", "signatures": [{"sig": "MEUCIAs71yMRW+ukQHJ/ZUXt9Juut9pstE6tKRzGrVQeNDrrAiEAhZJ3d7/gyQPsrwcl6DlskrsTaODs/0BkSlddYDFXGEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFMRAAlFP0+vFbpz6TpTLMiK9WrGg1cstPcmoWtC0XG2a0YUoggJ0f\r\nryU1zgiQIuU4Cufzpxcx9KgUVIpEaLFBli951bUhhipJnWIDT+Y8E0t+geGA\r\nN5n4KxeqgOVcELc255dk+YUYXiglyDVwqMt0FcrtdP4/ylHkWxJ3i4DwZS8O\r\nyvoQU/52X4S1FVU4JxCDsSKVDJ+Lfc2i3+lCXXEgQHpgMuUGWhs8VH37fgHH\r\nYNXo+bREA290r5Jz1Aw4Hs9TAvt00I3qZ9SZESO9YPq7iZRBHRf8p/3L1Brc\r\nUvd2JgZbYfx1A2hgZZeTEkdiLBn21Rl0Lcrj9y7VRmCQxv8rdRZsaMQVicRy\r\nr0hPYrqotQzQiXLNb7jfSnDQGqvpf7edzNbk3oAH83XRCPCxg3ywDgf+vNWe\r\ncRDRTtAml0wU2tIr/OrjnmnRYxx1upvI+5jrD/Hqzd9to0Z5Tu4Cv3TIYZ68\r\nr6H83Eg7kNpkdlYpCB84JI7hK/K/mYrfJDpKddzcis67dSkRi+FOgi2gGuc2\r\nv96mh2uCFnqo1+iwvUL+yiGFB+VNRJC+fsMITgnwLnvEtFR61xQ4MKepYZ65\r\nRKFOLDUdClBsisYduLOYBQ9ErsmfxVdyM7jmP9vC1zrDUkAwgN3vRiwNeoxK\r\na5BxAzfPcQ9aAvaLwTSOf0r9N34scyothyU=\r\n=b0VJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "947845dc1565947048e30aeaa22184102655b238", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AZbjlfqaeRCBAEheD72d1qtjggROpJsU/8ueffnhVx4vEUH0LjYSh3Tbv5cah/ZdmO/K5T7hrVFBqhNT+KJndg==", "signatures": [{"sig": "MEUCIQD2dm8xC0CbUcy/INTOhBEw5wSfspo05kjNbrvQEZxb3QIgLIfgWto2SjlMhyZ0MLz2SvQ3yrG+D5z06YXz+/Cfjb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKy6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrueBAAnDxPv2ap+qBhD0BeUkYJNbJJWJKyTpckonY3RXiutbE1WppE\r\ncq188btYJLfYULzWnCDCgw4ggj+rbAcEqf0XNStvRb8potiL5UIRgGAmGTwI\r\nS0ZUzIE7EZmKCmIfF+fql9XOLx3Mku+LiJTC4EFkSMwUCac9UuYzfKZ2KMKt\r\nGcT8qii1F7v3K4OKzWoZgZ1z9sVbQRixv+OH4tA2yX0CGb4jm4gqSG1W7IZu\r\nx3jAgpXsCMeVrafr40Azq2he641biy9+lQE1D6Y44K+4ScG9OS1uIk9cI398\r\nZU6pU+8D1er04Rtm9uFqteeT/uN9wSWw2oF6icNJHXjZbXEaNUYV5GcczssV\r\nVM34QsEVqhtaUfixd/Fe9co82HkxjFpbRvnHoUKE0TRgpEO0LAIMV077EIMD\r\nKaF+0e3MPrmBbqCA2P/VROKv84wK8Orq4lW5c147YkgEmgg2nC3ffkaGCDAi\r\n5DfHL/k/7MTacN5UrGq120aLM5i0BzA/8pFtIWe9s+I0VIg8Qx85Mf0d59hW\r\n8qYiWn7cWEFIulVE+H3fz2RU++sDm3CZNz/0/Xoqmz/zIjYvjupWqQIf49E2\r\nqJC1Yny7yUPHiqTq7BWaJHNRtJueRYIXh5u5MqDqQTl9lDmLcDE10jUiCx+H\r\nGGRiXwuFmFjxZA2tD+cVGzntMU8NrinmkKs=\r\n=eFpg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b681d8f13f5346f12bb80d9b0c28a14d4ea80d44", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-tUWnhfrqk8cAJKtbkICCHopGSahxDbNvkwGlXZ4DlRkNzjT7n+wtzCQ0CKGELrZGBVopb9WMu9SwD+KS63zeLQ==", "signatures": [{"sig": "MEQCIH083fwZiJEi0ufekUpoQwJabrNvkrUuTJB8SBX1ljcbAiATDZdubaUZZDKK3tE/7pDo58m1XAkRjUF/gsqddOqDWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdb1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoufA//R5q731yPVPNvqQkHH1yTQbBkaUepLjTB7bVqisFpwBQlBHZq\r\nBv/L6BWLEadIUGIKDDysm73G4eCCL4CIxKqps9dHxnyjmx3ZUiavu5DAn2Tl\r\nTxPF/1VKxq8KgeIjBfBqYLdzoguoZ4ZY5uLItXqDqS2t9F9fUxed2zvBz7iV\r\nigs4srKR7idJKk7DcjX9i9I7ITATFffGQzNQ4lBC4k5xZ92BreaP3oNGOI2Z\r\n0FAZwzOm1BvaAADQl8s7ci3ULtD5aeWHHSzPP+f5qqKd/FIR3DIWWk8o//i/\r\nb4JffErhITAxOPu9z6DyqKvCQjipEOzrpM0mvF10Uwyg3CYmbFzA009fyl7H\r\nW7nUxBB0IbYd8rZtCx9hkCih+U4kv7QJu6Ulh6Bd4TqrU/cnAYHBUScQ2FE/\r\nMkRYaho9rUh8BYxPcQbIVieTVeYg4XhOi9oR+CedomunQL7hjGb5j/aHVI3Q\r\n1+XSgAx355UGBKU7TZONAAC2ClK0qMIhUew9bako/c1W5i9tUSes9zc+XOJK\r\n/YXUa/Qlh/tIs+vubk/u39KiqyBdASgGfj4h1kEpoSUUs5ZGsVIFMFniz+dC\r\nq9X3Wb7Fti3Q8MJV18Mtni8viyjpvpeJimwZVxngu7ZXyruhuTwtK0xD6DhY\r\nQwJFR9WIfgmIMJ4PPxUG4wpDhqko7kPUy4o=\r\n=6JOq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd86dd931d6976f09f607f63a8d098d09fc8cdee", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Nny+KM3p4thn+55gtV/MjpnAOk15lUh0uOoi74cKzoZ9px6/ySpbo5hdr8tWe+4hSB2ftgnyRszZSyLBMozQSw==", "signatures": [{"sig": "MEUCIQCw1M1vmiNHjyASwOzWsFBkVu+onbC5FAhLFQuUlhXtjAIgLPPEJPWM9fqyt7mHXrw1SQC4YmcqV/G99HWfq4Vf/MQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrB4A//SOIAP/vBfgXzgfdTdcf52uNI9G98ODhFgCavbyUbQTOH+7SX\r\nFuCZWK6+fQa2rdRB3Kzs5XumwdNlfSgGMTD72CxW7kqAM4Quu3d5mr2bJARA\r\nEpzQUahiVPywx5Hpj5fd9YlZQX+sMsnULqW5eD8CLobmdmzN9yzLHt/DbEyv\r\nqJ5xNjPEThTM7/uend9t5eBfcVjrnA9tqgFUoDXn4/ZC3dD7bFyf65UIIYDZ\r\nIkLMDJ6FqoNlNWLkmMKfIY5olfj2g5h52Y1vGPzgs+vNUEu4j6rprlsdA/NW\r\nJZBPzH6DJTAPRFXb709wO7cQ7QKh0cEVYyhPKupMRW10ok2TeyZOknetrhsb\r\n4RKkeAYm89tnWN125znRqxnscTjM2GsODV+s2V7mxppZCYdx7bfunjAXFV3S\r\nnJpHJnKhc2ToO1RfoJqNchyFd3U94AwDsmMv2Q1CpV9cqqanbLgVZLYN4I0v\r\nu7qeKWhO60p//Gswa6/Arz8FCqBowBFf4g4sSKV3cr1nhRjL9rhM6v9Q+3Bf\r\nxbpGSQSL+U6AeTvEXzTP3BInbmq+q4OrIu8rZnF6HViUzHTsLSPjrF4vacuH\r\n7efMOjV5NPzqWgciPHAj5f4VOKb7U6kI/Pg5J4LDKggcS5AhxLm9bkV+6Fk8\r\ngcQavn+oqylslTyVIsdHize3T+N2I/MK5bw=\r\n=z9lU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76448e8358ad568d883c46ec853d362a1bbcb927", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-MmpwEaU9rAEwuZvlWnpA0WfrP3o9ojuc3Th+ZEsWBC2CvpO1rHHN+l3YR3wzl+TPLL/cKtY9mO48EfahiCr+KA==", "signatures": [{"sig": "MEUCIQC9uvINW/L106Ex8OpjWQsuMw77wModHbL5lWvXB4BO5wIgEVUDWYidHWQVBHnYeRzEaLI/dgg3RaCgeptiBCj3GCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCOw/+I5hBuTdC/lUN05RjDA77cu7DAIWG1kI5jx4UpXIvHfE2dOzG\r\naUTAD6QgINzxd/dbscWUTdp0ogLmxd1W34Vx9vP0GSN1rmzY6oPgoCuWVYFz\r\nD3oErF4qyhHVAmcdZHkMYk1+/ViYE81VeRVkCIP62BUUbOcNzhHBNxVkZfW1\r\nJO/Yk1HWaGBTujzaIskaT3/khZYrrZfhL+TW+dY1cOx71NQDzV1mZ2Ze3fi6\r\nBS0SmvWudonoaZ2v3cMsK3e5xOaI4LReO3eGepB3NQidXgil3pz0SQA6YOy4\r\nmJt8VH1X4wd3t4cperhR5nCaCojtsA7Yve8hmUJ3LaJup3d8lzpmkf1ha3fB\r\nISoWvvyo7LGhh5rJ4CZgaO47XjGE14nLsL7u+C7Em7uH2Hqt8wXeG/jXQ0wE\r\nMrs0vx4xFRYYAFOM+bLHt4ml0jdHJHHhzqweOat0DHMPudZ+tWm02UdG8MJU\r\n/XpVkQS7Xpcmosq4qSeVQiG2JnMdDHqzJB4iRPvGjIRNVs4fon4Rq83iutgP\r\nMI0ofAMMH+9BbnKM1u8JA/nT8Lrd3Rb22QTm5PRRigo4mq7UY0yRzqwBc0xh\r\n6Bs5nGsav6cJzToPo2QpVUcGNH8ixz/FCDnIyGzdjHJx4Bk0cN4Ts76CfWtJ\r\n74LR4IsmkK0mXt7NZPx3/kSi3qqtEqO0iA4=\r\n=JHxG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3e3b2a4f21411b89fbd0a1acbf36292083f593d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-NpCFlQAMoIBV0VUB8iORf9p5XU/sXaHAlqx8IfiV25p3mf/8uyaJrML8nQ3PuEONGzN5uAIC5CRPrv2qzIMgdA==", "signatures": [{"sig": "MEYCIQCR9c0+BTO4xrSz12hd6wuFFnF7EhbIoNvGp2RJ94XD6wIhAOM682Me9Kioo40oPad/nDirDDFRHxmtjcpWSDXZ+jE5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwO0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgfQ/9HS3eG7/HRrUVboO4iU5Vl0pDUGkp3K2Ru6eK98POczHyWIqC\r\nFZhzU3tn7Sljh6fK4MGCoY7TDxOUSU9FxWq8zSNsyL9/lIkHAkXsVUyQNo9/\r\nau7NmpEKxuxAtZ53z4AOV3Oa6SYiaiv5tqmF2qQR2OsT2EJousLiOkvm1pt1\r\nZ4uoIFZWo0PqgaKN75cymf1w6IAPPx1l9si7xsAROYbBtAxBMO8sL88xqhyc\r\n+wIcup6xbzjEMEQ7z0yrRS6hp74j0Rz+k/TI0qEJBm01BaUSdYOPgtf+WbXB\r\nqSbbNJhBxobkItFyRA82z9JFIGkvZRT7Sbro3PWszBVnjx1ERjpQjFD/RIS6\r\nbAlADlrh4J/o3a8+GXfhH659btiZaXW1t2f+OMlLbOB+DkKW8b1tZkDwtmYI\r\ns616Wzb0cvrBi5rcdxfYK+PvYgdv+iz/kh3+JgoaJd59F5Vozeq3xKcaEHPO\r\nGVXitPP639KmxCfM95bEG0rUrTZdI4DCrMPm7MspLRwAiC/4s7jfA/OTV84G\r\n/Xan2un9HrVY+kN2cr+kv5yFnI2aopTRSTSb7Etv6Tvog6D9d78Eqzj2KbXZ\r\nqT9mV3jnAJ+Cf6ePnIoQK53fU879HPiSihsewzxkLdQRD2uPiPqZm5KBotLt\r\nCQnYlji7mV/jOfbnnJn701kigQqilVKXGWA=\r\n=WkNa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "942acba0ec9974f5980c1ae49346475896e28a5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Wbq1R1pPp0wk4aT5yGbBJjbDp39DXonvy+wKiaI7p7DpP6vGtQ9NDNSQeVQwxZRyL1bMeAVo+lU9NTTV2M09UA==", "signatures": [{"sig": "MEUCIBVD4xYI10K8xPDbAhFRKvMkG6YGR9Jn50uj5o1lyBMTAiEAm3utgpL+8x7p6TFSVPPHQuyp7MaiLHxXGpLI82aeXFI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+khAAmiZ2b547WNWVnsS2cDTt664jY3R2x9GWX6vUnsImJSXhYf+N\r\nlGZ+oEzcx1Hv1KQbiBMIKM2SfFVOOmTcKmCHb8J2LH7A0Ak8lyK2+/WjXMJg\r\nipE1ugEKsrRiqoA9z79FUE1tO5+8Aayt2y2OltcvsKc5nKJXFUd5bHOUO1Kr\r\npksmDHAkotiZWwhFtwkTDECHOASxXTqtCkCuO9CvRj2OdJ60FvEIt+UM4zWV\r\nMGaKvGyQ1zP7ZFJiIgOjoq6JMP2FYL8oPfBHR59nrl3AT/qe15jXH+bE1j8o\r\n60/D2VREF1PTpRSqIsxXzMUIRbW+LBrv2irVx6m80bBG4/tSwPZv6RtpbITv\r\n1aFx2wL7hUaOdIvisjwKdj2ivZ3wgq/LJSBH7X4Nb8HVpc4nwg5fJb4DxQGJ\r\nRuQ/Os8V0rP1B2S/oclmg7swRatdvq9EnJIhhpPhAWPwjSspBWtuXyN/4T1Q\r\nglDaQzjFFQKIVrC1I3rLqXA01emrK6xaSlTL8yX1hUyjdaME7pHt2BWPefHw\r\nkEgTDP9Ddj9H/EIjfHAVNwCCfw6m3mZ5IOlhMwfADJhWr2u3zlGA6GF4vKjZ\r\n/B0quXRZzgLEtOu0fkzlDEzKqwGuae4Bcg1fCWYcRuiZlyNlOEVDOqzX1Djy\r\nDzSIKmPw79uPpf4KmuPa/oUDAcZIki1fgLc=\r\n=uSRc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "728707eaab1a4beb9b10842e38a3eecf14d1b77b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-iIVAnNBvOjKA8LZUqnXJbgGySdIfXLOuKfqL9x1y5/an52va8evYwU7m4F9JHsmuh4VNTSbPcpHQJ4vGTRDOXw==", "signatures": [{"sig": "MEUCIQCCiXrXg/AkNl8tFYjMoyqQJcDFaNBUun5ID+iJqUeLWQIgWc+Z8+qMgSrxkFVPqtoyZnAhRbuoaV+4KTKpT66Eob8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqyTw/9FQwsM5SR/2hyjCZVEnKvClT7d6exvxtZnQu2C3QnjqagXM7g\r\nY7Y1+hLJUpDdiw8FwRJ2spVOXFkzvzlV+PYFOo45YD3oXA6roWR1ldrRfZOM\r\nS3zEpSdKwkOSQ2c2On22ypzDA8QWIsycOEH1fFHjwabRcWlBGL/5DsMjJROA\r\nNGvt3pw8OH/6GKINCuVbLOoE068GaG9kZiA3x3QqBlnegzDNq7c4/hYxJxrB\r\nkt4HF28kGxFUsgNstI2V5M025ZmWHKUMQ0S6JmV4IE5akUA3EBjcwg6kpvBA\r\nZubMEkOWXvPRsfRNYyaFtTRf6FTslHL83Sd0mgX7rI5LhL3AhAxA/ipMbX7S\r\nbfXbnDHUKjMgIMtRBbcBsb+aC3dPgQr1R+o6xUg+ptCgnGf6cLl03t9dkHKG\r\ns+/ozpyIzWYA7WmegBAIWRnhvZsRMeD/IqrbwC6wkwhI5wJnrUq1MwBNEHrd\r\nbE+fezPdq+gRIFDEtUY1jy7EgV77QQwpxT8lKAfYGHewcwvhMVM+TpbHuhWH\r\nv721UcCryNvRl85cnuEg7lrexst73Xe6mvOLjP8Mc4MmAU0OHK9lCAuf+JG/\r\nNHkWrkXWEIQ8LZhh6kal/o3sQEt1dIVOCmY/Ar169Q+8fHMxlYSR68rEEuD2\r\nqcx/LIC+WMM+uxNRbQl4gYkKzW2FwEBHcgU=\r\n=VCof\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c77b34bbc5f8c11c5773aad24bbed55e3158fb4", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-EX7E63NI0bTY+o+b1p5rH1xTulD8d2HplbDhAhu2yIHQGxvJ3/XQ4o+4ngsJyEkmS1YzcQq1D+jh9I4vCnDvhA==", "signatures": [{"sig": "MEUCIQCJrvlEjyT4IvpqlRMZhI/M5Ldi+e09V6NB0O0EDQes+gIgGy3hhjWV1xkzVuelUsk8wUXPM3FrjDSbfa0G6qzf35s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/a1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWFw//XjK1nBVggeFqfzoe9Coylhb0xWC7EdXv9nQj1D3KvVnHOLM9\r\naoaw6G2N6T2nmF/LKQXx5ySs3hwz1Al7Na3hLp8wEe/2wHop6RFGjYdcFg07\r\nTRMghuzm3K9XE4q2fHVLeMDbNdi4+rbCgjJSjI7M+9I9cRW1lqBKXoN+0VNN\r\njQ5YjNjaisw5m0uGlsZaa+XtZTldyRrrfoGgB2lsYGvSoj49kVXVqHfgOY83\r\ndCinUR3cqMbvDkhxOVSD4mCUOxefuCrksdwXexvgLNat8acPH3bkiESCEAud\r\njtMUzeQ74W6ILT1Bunj+mp8JMdt2aBHUqh9R6feiK6doIJQuvfFbV2PLShhI\r\nsn7yp4630HKnqz/8s1461/AIGm6pxlE5GPUP5rWaRHuyDVBhaq9Y1CTKyl6Z\r\nmyTYnWJytCD3Ieb9KfEWbHS8QLCowHtbYse7eAOJbl6lZSsLBSEiSqQySnmP\r\nctO9qnOb2HEa6IQofPDp745d6BBFvfJ1gZTRGHhLIVoWMkDr0SUGioEVDuFw\r\nMBK1L81Uh5EdRVe1nt8IC6E6T8sbuRzFImbFEJ4b928H3VqnMMdoxsPJ89nh\r\n7XECYqx/7FVmTFnkSW/prCP1QWyr05ENQe3G2AQvmeZlmkaydQyt/l5yWB1m\r\nY/9E2CtboHA2ZMsJ+s8gD15oCdh0VLISzG0=\r\n=DEOA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b7527cd4e07c1915981a08dcc4a67afb3680223b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-YAQ91qfh07vjzhhowMhw2rLXSnTFeBE3YcSUhWf1Y80GilvK/UFmCWMfjGY0hYM/F02UOkzDE+TUeSipuLYX4g==", "signatures": [{"sig": "MEUCIFhR2C4LQgaaR94jLwATFhN1XMdjdfBK1b2qmGYXYz6+AiEAk1UTfeiUaS7xJcE1bFY+zr7mfFor/AC0QhT13GnUPsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNfA/9E5dm8/18AwRDJkAyxGAGDdlkHkR+TVBuqRCNHkOO129ANlX8\r\nNSg569CtxEm6hR1L5XRgGz2YX5HpsMO5aO2GlHNoePUCfNoJhTT5Ars4LHr+\r\nkAZXrKtZcE6Vty6MZgUbimff4bHkmZUtaZUegTyHObBmHoOBOuzeiq0XJUbZ\r\nDImzspk8q6fbRATWqt0vXRRDugfpcFwGSZS3cE0Ger7NLQfHYRr/iLd2P7jq\r\nv/QJau5wl+VZzjpY26uxNMt8vkFiAWPEqjmgLiNQIkXQQOnJXRCgZXNz1IQg\r\n45DJrBxzUZqGbns9LeoCCditQOEPoprUn2uYwms/qp1n8SdoWKHLMQBbY6Gg\r\nlH7IPZkQ9Lj7RNQhRk1sXS6vhC/ppJknSn5Xvl7iCeK7ag46UR1JiWEGsFhe\r\njyH10RMnOi60AXODS/qWqOFzOBKLxJq2jRFlJxlrq0fcWJZl2Fiu/CETRU0K\r\nfOYAbFrYRLQ4/wJKX8YcgmsA1jj5l6au+DLHyNFO0n7GEU6WSrQfPrUhbX9P\r\n3JMuhO77mp69LScgTsp7LS9o4lCPSdUS8pgfBvyS0L5X1lVqfNOUm07bnzAQ\r\ndFhzCJO0vAHWHijSt/wBtDrRFowMXnk1llbJ8aavk6u9iOSFpdLRr5coFIEc\r\n9V+HirJt/eBx5SgCCtBJyHvU/re0gjvzRxM=\r\n=xRSe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dfaaac513c4697ec979ec314c8166ac6cc69a62c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-dmKt8QleBiTAV+c5ED3YuXN+xmzBKb0/X3sz0qN+nV1gmR3+lAIKnRPvJ45gNbIpvFJz4vpCxZ3LI5VQV28P4w==", "signatures": [{"sig": "MEQCIAHRci0UR9d6ayy/FJYzE7m4K4wSDqOusIJC4Rq9+Uo1AiA422ST8K9ggBNzufnmyPWnN3zk537xqIq8+3qvzNBg7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMAQ//eoT2L5APC0NrMMT7JZEa6vxdsNNzwvBqHL4QCjBZ9Bs0ac72\r\nGTUZcY4A3GO/rynppf3KoyQzkyHL1Ktsx/ERg7cg6DJmREZLJrq/SmxTghmq\r\nCO6dvoK99JBRduuGzw9MVN2Xw0y1xvrh8Vd9aYkrI4IVSIOsvtybsgjLp5wY\r\nWNVIXajZqwm/3adz4MvNgviibIvhvjSniItZwHc6sRxYri13TjZM+cGiyQVZ\r\ntRwRWwzHDDdB8y6V29b4LpFd0jUfZFScdB7MTHg8IrE9oMoEi6EAbCXqhGvz\r\n5TCZLhWGJBBe2cg06/oHQC9Fb7vruBN+nzJIYwF2Ikn4QhG1Eg0UsbyUejlm\r\nKnEmfNZvxTLirFn2dReryBZ80GPggJjdiG8e7cPrNTgFBrsCh1nr6hSR37wy\r\nLiehTS5b1/F+2QSYj8c/9eDB3mOugw4bwcbyJKodxbirkKMsJnDSQyAPf91I\r\n3x2bpxi4fs/Sy2T6Bn5JD7wfMd4aLCT6YEr0LEYj1TX23jkKrwK7gJpKErhr\r\nMiR9bEqfWVsxc1KsOakH3eAjkMYk5CCgkzUTOyHuVCGVYsjozGASUQD1GdaD\r\nSTXgz3FOwXTbiDq+X4ikaWMqvRFh3PSDtY4xTv29OA/auVjHlM6FQ3A6um9D\r\n1H3OudxJko36+ATMjcRlMQyASwg4NfgZ0oo=\r\n=rqB8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "deb3945e3aa3c6f97f32a979c1b69873de9c9c7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-t2vBZDgM4jXsrnmlQctK/yRD1yGTGmOrBFOhPa6H/3Ojm1kb4F03F6hTi4ekd7sQH9Is/78coGsWuLjlf2QTsw==", "signatures": [{"sig": "MEYCIQCnCy0yuNs4nWPXHYTd9tKIn/xFCBJiieF4kG1pDSDXIQIhAJ4AOdMou0BjDFYpMInPbtFYri0wDLDFfICTts/e6dqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVL0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrROg//co6fZE+bekqy8HmCzeEZeG585UhTJQK5dRQlF68gQ0xuDmli\r\n2+qpqSXdrIyN0h7S3Gm5Hw/YXfdy3aV5qJxj2WQN+kRLN+kDCo2l0F807L3E\r\nmNXAE964B4I6cZNMeqVXpLtVTN8YKFWiTnkWQhh1gIr41wqrAASKI0il4a8c\r\n4T7MNqdwi0ictqpmB25TUpnhbDqsx7OShi7QvRBl8yeKfI/aYsNYYUdwXXIk\r\n9Zw+aWYvd+Nrr9bereR8KvzmvQiEJMKyVPYAVelXhPUhS/njaQAMZ4cAbRmo\r\noxTjyzak8Ui9uSL7a4cI2yald5LrxYDfjl79E6AO5WgQDKi+h0sWgi92pLHj\r\n2zVLnDTcNGu/bpHGRI7RvqGNlIbj7gsyZDIDBNIPzD5b9x7x9roK9PaPtmyJ\r\nzUkON85WcEXSZr5jHoXwu4u2WpSnh5ntvSKzyz/2BgcZ8IxFsoUPlRmQlb86\r\n4lWDA3xNa0sB0mAD7fZOavo/p/brJSVAd0wlv6CKRY8lICm6ClvE1TkMeA9b\r\nu9ewD3MtDf20JpQQGNLhrT0Af48NQxRpKjnQFGmeJt8aKMzT10HbISi0hDPs\r\neG3O5EN9jWDKcTUx1LIJ84D3sybHSQM3NZSHg7KMXIfPT6R8qJQuDMJepjNN\r\nggbxSPTtKXMWVZCqPUJojslSLgn+AjRyCFk=\r\n=mA7b\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8e0730fe635bb558a821291580283f8761031a24", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Q8SRx0ukFAgGB7FTJG9obOaxTYcbytPwxr08fV1azR1hGxABevvjefl6wTuS0hPJMON+0qe3sjLTH6mCpeSBhQ==", "signatures": [{"sig": "MEYCIQC+f2kiuorgX16histetaBmuf16R9vXQ7+TlxGiKhW+JQIhAI0a7pHUBfEAcvsQQE3k0iR3g/V83Kq8UsUW32b8ZS+p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6KxAAjPcKLLO+w5tUnHDsUPKSazhng6IW52kX868G4b/XYNpEe33n\r\nF076XWx+uHizYZXbhIP6/lx8EMpGhLToQGm0ayRoz9PKnOIfDsaaj9W8bvSh\r\njRg54BXAUuT8J+7oMjy7DLw6EyrGUUzjzbMY6mRLUNIe6ozuH7woaTBj7jDn\r\nFTc7YOK7mpRIIbmxBzlT9Yii2kpflkEcKCTwI0fxR3VM73W2bJryOflqJtyO\r\nfh/t9AgZhDg0VMmz5EP+40tLhFFmVV70EOWpFsZYCEIa0LbQcvzUxdyzunLH\r\nRHKKZ3uXTkOnBjaQ3FOI+/zvU30bDGVqbyvqLcfjcKNyu17I2cLmVBw/CPC3\r\nUdQdCH3hBiBTO540o+y7q6zkkTrelNzd38BvxCodvI1pFuUxeSY+c3hhdfiB\r\nqjE/6QClUi6K8spJhMBveH0SN8BBHz2wFRvueUPxpQiqQ78sxLtfxrIG8mtO\r\n9G18Q28FYfdtl+/uIeWJJSWFLAbyQd1rxV1HpcfDcXm34HnSVG4m3D8jlvC+\r\n33Ym/+rf8iUf9nINzY1BXPsNV1lwZawmVEorw9YkKQWWNTez/MuUwi3fVxdc\r\nga4x1SZYKFIpDO7c1mrOfOeoMvn5D2Brvbq1/5FEeTBcx3j9REpktgp7XviA\r\n2hOBk04P+l9MuoTi1kczuhP9esEGx7A+2Vw=\r\n=ihvx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9fd48a2d7bc476f4ae6d40459edce0db891475c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-fVrPgQ0YMHlPuxz0mVVbwBc8oMseh2UA/YHLsM3nN5FNc0Rw/3JerGhOdT0CB5r/se2A8GllALeUoEZxQ6+kMQ==", "signatures": [{"sig": "MEYCIQCV0J3x61clG7fa6wtvPG8UUMpuVXdNjXLC7X/0X02zzQIhAOUCrupLyf1IZOu2W7OKxG82d7kKCHzDUDamz8RLskOa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3lA//YmnC+1ihvtmlU1pMjcznwB4EvowX26ta3biya2yPHPjoxKHc\r\n2Bdyrjy1v6PZDWAo124audpQ9iGUWTqmxc1gX9jqzLl5yQbyNmw6BVtp5y/i\r\nW7Pxn2JXPWpkKoxr2c+zfUqb9JPKGBxeIYD+DDf6b7wwFD04IpsrlLP3cSgp\r\nPK2SjarhEECxqpwlIZy92+KAH46CPu+26gGnhMzjKosUArSut6RmCE0QO115\r\nPWNFnEVMoInZ5NpJmTCqXBrEbmyk+fUyXE4jyrgx2O1E3wTqUX6ooGb44JFX\r\nVngPLE4wBo7E2s76ROR/mXpqp1K8r4ycxXySaj2xN25c1uKzqdAgQce24Nli\r\nqUmXu1Kl2GyPzm8nFXoUFu34iDw9hUVuR7NWyvmfn2Wnv/x+ILPMHlW+n2qe\r\nUmsnWFmeeZGeAl/P9Dn2xLYV1pbGoF/h9AfgF3xk1e1PpUsfpV+0N8QC8Dfu\r\nhxEeDg5JWBN2I+ZLvWTYsYMKUKqfsOjrWz7JSOpNBIElMafaRJUoy+uL32aT\r\nVd3pmwUc+0qR+qy447nJey1w0Qjjzz0ClVVtqkHgc5jFrieT8Cmi7ETwxViw\r\nDwM5+qGtHTqus7pyq77AaXzqkQ2x0UBpx7TiXDOcEKFrAaoO5UCfqKOgntIj\r\naNG7fBnidlNR2c7ZcoPB+ZnTnuHYFP7hINk=\r\n=CkRE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ea67ff4bd4c09b377ba4cc3c718cbee4258c9bbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-F3EsTI/eWJw3HBrOzH+YqotmCT+EEfbGlqKEgQmXwauBg5VrXelWHrw7nf9hQiUa6DeCHyitg2QMpulgD5mQoQ==", "signatures": [{"sig": "MEUCIQDQ98mK+uITKoH+NuD1j01bobW3T4QKhEjutmrjdNnGtQIgRuHUWcP3Rp6KmpkNdlRrb+kANPIe/etMtWRYN5fv2uk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJ4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/JBAAi4s9JWEcU1TzjFB2M4nO+lxh49+lVAS34QmKWD2234dEUQp8\r\nk25Yz7I4q/Obe73dZa7qe+Z7oZvtMD+43pNzF0TVH1u++4bStReBXHXqV0Y/\r\nbBmNOq8k7w1zWZShCMGjKA1Q4cmad3YkyS/x+q2pix9jHcph05gLtKQ2T9Rd\r\nFMBdOP9VGnYgs96KQfvKlU1Kss9RoC9mHtME/Vgy7j+ELOQ2xh3cQ95VKCdV\r\nHFadDYGH+rRxD5q+0QkRZA/HhpaAUjwbDAYHrXqyMVuyek4DnipOcFMtci8E\r\nK46CU/uQergJxOZH8hxbRu2pcUDoarER+0SEt37vrjj+HtGo5JdQ8JGNLLmO\r\nUfBMsqI/DNGADQ5gE4kNLQ8INXTRoGwzD7+jGNdx4hdqiSEGigeaYf6ue55B\r\nj2Yuw9IBJkewN0eFbzAgpvxKEUO49ykiuitdqVp3iYdhX2vBGBf/O59T1zOG\r\nyAye1UMAoIkIW1oaspc4JjnCEguhfpvHqXET9FxlXye6dAynOxuIr9Fn6QYG\r\nmDvlv7m7jJalUBJ6AQV6UE2PTcGMIhBMbpbQNA/VojP0TQx4isMlxYPqOGPl\r\nYZBQaZurdF8y6HMwr1tlxAs+QkXsmco7+SjHp6EAuzPL6ZHfSwsWc+G4+MkN\r\n9oAuDRBl3UoKqEFTd3LbXIHuxc7fBF6PkT4=\r\n=TgR0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-checkbox", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "41558fbc1b54e6681db1f3f51feb4e1cebcecda7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-6kELMcnMWC8Ib2YSiY3oXElRQ1dBq0urusL/63U46uVYwtlG1EKbQZRMBeaGCj4vjocNRSnp80mnHxtL5O9sXQ==", "signatures": [{"sig": "MEUCIHujUSIBMGmwxY/jT1pIDUvB7iIXW1U3dCYm/391tmBhAiEA94rAwfzQ5WbPb7/5DNoXq2bI0t5oYsoCne+cU0fRmvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTReiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/TBAAlyhXEPQ0sBUiJzUR0RrkgprXrrM9DwZP4w7og6dgb+v79Ari\r\n+S369Dd+BtFIhRC84moVIsAgt83kHby62GMvL+0Ts39QbaxEInDQXbzf2L4e\r\nfVm5VcVPahBZjwT10S/rHwC3Xl6GDALhbyUB/+gBZjoQ2zFn8b35Qmf+KPcH\r\nXj04DCjNUxaWqEVMntB6wHlcsVVuFIxX0qjCH0BQqBedyDW5R5ziQPmZ/poL\r\nrp6I6q8XZAKGdzTbvTDhyxAu6nQ0Mnn6T0/Mx72mBqkKGtM7hbJJRdcmOOkH\r\nX4UqLNTBszLDWCdJDV8075ZAfS11hGSlE48f9sDaTCrWuigvzB7vAdhN3aP2\r\nAu8H1juZk+5KThZTMdaZ+PFp85SZhkmQD+ujIVRz3sg9Z8zSP4rs07DPi1w+\r\nYaWUW+l66lSZ3G1l7Ak0s6TDTPvInXbuSLwud1aR9ShiF07IiWvBKlj2y8R5\r\nGcnqPAHfFEHt5IzmTFIBXi9IbIGRzo2U4tbE5a2HCpQQqjyXPrIixnH1TGUB\r\njXCl2Z+kWG57Jtm3zq0/fD0AdINvcFvUUFtVjSBav9ZEPDaoH1mkWZsPIGDr\r\n7rYGjwA18eQ8GEu29xspqgeMa/3A23ZeZ7zWycAwpAtYZWw83dD/7u5YA2lZ\r\na5c0arlTIAEX+6qI6r0Fzd6Kb/nVxU5qW/8=\r\n=/Dx0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80cb38b1fd2cbe73f3ad0f4db8aebead0ab0617a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-TisH0B8hWmYP3ONRduYCyN04rR9yLPIw/Rwyn1RoC1suSoGCa8Wn+YPdSSSarSszeIbcg3p2lBkDp2XXit4sZw==", "signatures": [{"sig": "MEYCIQDbOX5+Flu8ZSP+qxP+LKI6gHBTdfmff4H+dSbsjql8WwIhALk39vCxl4JeNWtnKwCHBtypyJjXtCG0wIgB+BbYUNB0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmChAAiJBqt/mWlcb2q5zUIXAxcs2VoWV8gN4Gs+2dK5MO0UInXFSt\r\nCCzq0/SbOEN7nS92tJhLd4ocZ3NmHfX1+IG9UasV6Qdc105H1Ywg40CmMGbT\r\nQyKq/CtsuPOfv+mBWw4c36CxjG3btYdvLaX3s/dFNFVzqT/IJ+11mHUwN58Y\r\nemZTS0ac+23NnIRpCuB0KqOXy3Pvk+311e26cifgDlrVr8RDpt6hKFkCGFqJ\r\nnlzYz7qA/kTPjruNiV+SpU3S3PCif/MTRLP1P8rjPijTOy3D05r17Rs6052A\r\nt6eCstY+C+9rpeH1c0pKEnCDs6iY2bwm22nSRbuqoifO47fIUhbRJO2POKlb\r\nrMfG4nBJtWsz5jnbG+ogKi40JVWapUhasWepo39kp/UjpIZzdm23vz9LC557\r\nmCbLMoPoGc1KhMWnctUJJ2RTRdispJYG8/bMTyL4DghAJINNtq9I13WY8UVS\r\nnTAfNGRwhg+mjJFAz7S8uhRZ6QdKe60lPzlSsgrCkz1yqkSxjihwM+f5oHZX\r\nHRWS6sQWPk0lxeukH3fT3jigIknGTxXTz388J2XzYsea+kaXwIwu6EmTsNTq\r\nheOrTExLBgGRTQHz8Mzn8Taq8r1veJFjKp70892tSmxWroa3gxkXFxw/cHbA\r\nNtyfCvfJ8BdpDswAeGUHPdj0YDSpQnRxP+4=\r\n=WTzT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c5c47699f6b5274a9cb1b43d5d1d9530c94af64", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-wYEOk3kH9HKjzM93qqsTOsU6fl/+Bz3UsC7cChxJIcoUheNgx2iSvE8b2b7kWKL1yX2aVeatw0ATL0+OwSpCrQ==", "signatures": [{"sig": "MEUCIQDntHgtdoJoXdlpSUghh/7RHwBbk1pMWIPwyXKNiEr35wIgYR1tX572v+2nZZnFBLnxqnye0/XYXpQUi34M25+UhVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1/T0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptKw/+If8MHp6QT8ACZvdNRungX16akBaEbuTfK5zsPX6AVZeZa2S+\r\nKfGU8f72SaNdRn8RM99h4zDr8We+4G8VbpYqc7LIuRQ845RXrXArQt1JnLgV\r\niPWaEQJ4sC+E9vYcGWLiVEtAqCccGgPE00PKVdmSTrkxJpnf6FFlh2aBbnQs\r\nhO64tr2tfvxI+jx5J/lEiLi/uPfU1bjbvO6GyM8SQpW84FD3wLanrSRHREFg\r\ndKUgBlkaQCgxpbAlE7nK1sUjCwG2DhIOyO8ZWDYIk5XSh9q0AwOf3nhey2c+\r\nwGy67xlv5+h9YhccrdUiBVFn367qH+kreNV+ne0QC0iFOAMAFxk1T1ViCrsc\r\nM9UTvOzYBn+qzwr3pKaEDidHdn7DJ9J3tBsxpQ43mFqrkxMCheTvKFcLXzXo\r\nMA8iv0uVrkWbbEU1H6qmuN629umQT/yKFOnetEfpanS7YpJ0e7lvf1edimq8\r\nDtt/H1cuS2zqrgThxGgHnSBYQlr2UnoenVwyqDGqSdGjx6sQO5IeB49NRy2r\r\n4y6b/dl2+fRXW7yOp2Jz0acs5dMyLAaZTKcROtlO+pcP2qXBZw3jTw5ciO/j\r\najYPASnjpN5nn6YNiUuGTX8eiXfBVlnJ+7ioZIVma2tl3VbzugWDMYr9YHpK\r\nADglZ0ZdrH5v4nU3NBYIYuxhAf9oHVIQPZA=\r\n=GDdA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "58bebf7802c01092505d82653b7f3ac048357528", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-oE+IXFrQedCEWTIElL5EkGXSlTefTH7OcDpQSPQwwB60ih1CHGXZzcP8nLY+v02HP2r87I3cctb69ukSNhFDTw==", "signatures": [{"sig": "MEYCIQDZhigCVdpTkRQz/XtV9GLiM4+51TTBtud/wt9BmzM8FwIhAOLJQCClKz9nPWcdA8yRBv6dOeEk1xvCiLNPzZEzr7vI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4iRsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqZw//bzoaZYBMgEt31m74yrqSALdlvFoKucxPwfMDaq84Qg1Li6kB\r\n0CCZRPyGV3wbjEp8s3uL0lqWjM/tNPzFTJhC4vWaGX8jbGLtUE0gggdGpspk\r\nPHo32BqruyWYwXuWHOwDPAFAGxjO445eLRobyHf6wECu878FTJFROg/24Egu\r\ngtrbQ8R3nWnBkRvt9k80GE3LLuH4/srKzWHzGQ1qt4t5LIVQotRKRU6Sfvw4\r\nBlWyow2CXeF1HEyCc7pGJrNrrq2H6dqdHXSup4FwWVyQica5dU+fl3GkufyA\r\nZVgaqUgyI4eb4wiy9PUet7M2ywmgWBdSfoS0rmnK5dgYx12ouvhHM6sVW9nk\r\n2cDsL5A+buBoZ2ELIAfJkDmx0e+58YP1NGcOryjJrYQ26R+EUeRG2yhX7K3H\r\nRXOA3Ovl3B4t7sF98dJmDWrZSjenVEKCBE9IkYruwJpm5YU9Hp++IRPKvCBi\r\nQ7/IPX5KIkVYIs0lDqGVGAcZ+NLGSwyyOlojyJDyLAv5se/dEmGZXTijacUw\r\nMe/dldcCX1k0PojgxFQmk1tA4neI6kiUaW6S35g6xXMBx5x83hrNm43pR+hD\r\nURNuU3iN/9ytk+3ZyxCwGYEnkFBuI67+KFp0Bl75O617SzFbEqTBb2cjpetu\r\n8rBlbd3BXL2nfjL4SFHCCaaSlPXVUG6D0+E=\r\n=fq35\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "35501f1b2f075b4292a324321001b6a8bdc5715b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-2uUsSb91gziIpYnaEr4C/Ojrsq0m98hmusvTVZC08RXOY6nwSmlXujiUBhyrH283XCG8LrzE5nk0i+jnVgosyQ==", "signatures": [{"sig": "MEUCIAhytnLawcCWnfdbql7XaZGc9ktFdJ+oaq7AZukwSHb8AiEAwjjgw5ffRgEHfxoyl5kHphUZtlbNlBn4Qowlwj7kMtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj45OxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrikw/7BwwJ9m9VXxNhtmMF15XvBFoH+kzjKpttX6au/p7Cuj2yjE9L\r\nWpVeavsnZ23ljdTB1Qdibe7gbWCqWlSJ/OEp2BKUUoVwhBQXKK+ZZyyrn1OU\r\nhPMJBAFKwU7Ifc53cJ1ZHtWCz2N9v9uWy4bzpsqmvBU+fikAPcpmVAPQZ0ks\r\nqHVKWbnrUxivEPdJ4/CBCNLqZHFVJcwFkeGELgRgCZX2CEDc6lyCmFOy8SYn\r\nv6PzvV4NmyyBwUd/oPkGOpZNsD1p0iFeWAOXpl0I86LauGQkXnZGYK3q4V17\r\n4P30AFbX9gdp9TNpxbgoZ63uty612KHGYayv7vmCht1VwoEasYuUklGd2ath\r\nhj9oZ8zJ7W50DYuZoCyEdKyYlvZhzTVXeEhrJyo4MphohH2w0oBXNw/ZTgy+\r\nG5c+sACKmV3Sq2xkWvslRoQXxY59VFIqaeqFr2j8S5KtENAIKmPcuf/CvRwn\r\n007fX5PEqdkISXaGBnRgCy4UDUw+e47dJBYOFc3WiC5zJrtC2CbWFfA8dgEx\r\n6iBcQhGpjDvW6O/+v8vTch3Ptv3kcDprA2JqOnfh1cwndwRb0IaF/E034izr\r\nWGvtbKunPDfvyyg6BYg2LJB/yJiWiF2Q4WI54arehxa6VZYxR12vDowy+cZN\r\nW4LOsERz078O06yKAe5EqYaEjBoi8FgKzuU=\r\n=ImQE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "adfac61f5e731c1f6b59dc5531cc3d0c14e730df", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ShS3kS54+2RgtBYE1tbTfArsDajT8k9KHXPZTz/xP2JdpK2/s4R09/zdhQnywGjLrjn0G6EMmrVX7H7cW2+bFQ==", "signatures": [{"sig": "MEYCIQDPsT20NLFQ0C8mA4Gzp5wKh5E0mji9nKOb0CRtyyZnUQIhAIdepvYSjeQsyKYObbx+C6CwKRrbYcbBLLZuI9tRuuq3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj45ZyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1ShAAjR2sijbFFTWTkKRr/ZgJ2JokfIZuQtpXL1581FktqDVgjlza\r\nlj6OmWIYbHGIyzoGyomuvTvhUxOnB8CUIZQR1zr7/aHS8jfCiIc9SQ64si6J\r\nk8ckF1DQK9ieR+hFEykox3/OmPHaLMv/PHtvbtWdbdtCnha9n1Jh+p9JDU5l\r\nSGVzwLJdngp2VfnZJyd3cz7t00Ui4hnXct7xXlwi1k7cgVajQaHXYH0A+ANT\r\n+oA+oP3hflCXqTKKC60byRMnNbAVPj/IZh/IeE8kAmP4QguAVOX3x4gXjyeD\r\nOuvEXkHROxbfl6Z6TBj+YRTQwVV2uhnPSg8WlRbN1l9pzLCDnaM1U/pgrjJi\r\nq7w7UiwB8V1NAnq3/bcK1wUMxdxAJAnGsRMf5M6ig32g5qHtiHxsx+p5MZrx\r\nEUS3aX4NJheyg8cHIt4RCtQAdAVPcO4Kic4ipiLPCYWdx7yYQsL/kJXoBuDW\r\nQ2uevGK1AfPNKvi9N4o3iWFT8WygbbyFqDWFj8ebdLexfdEogdb56t06MSyR\r\nXzfWSSx1ukLmDns8VX2fORyVjmmeFmJVi38wN7Cl7OLGLJA93JXKJ0JvsSFh\r\nvrwz28VfpVTAkG9cv1C8FNofdkRL6hzn4hcXN3sA2cBpUB4caKYck13DbkQO\r\nrwWOplcV6iwLPkntTXzfb40sJV2zD9a4DlI=\r\n=LIGI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d957bce2dc6762bd3cad53155261d8024c87590", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-fVMxHjcOYLOuh+Dan7532/qJEgIZFzu4pvJNEyo2pjFIA7B75Oq9yJ1DeXeOcToH4EqVQF1KkUtUoCUqxSmc2w==", "signatures": [{"sig": "MEQCIFEialeHPw8leUQwXlU3xohqBW8okcMJMZNv6o8TfPWvAiBtezCgc9yTvIuoAhaeR7vO94Uv3ooXQ2iUSro/Nb5fNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj46qvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqM4A/+LrT6S1TNzrSj+Ktqmm4PzroQ7tj+nGW5KkGbyP6kzPB8o//J\r\nu5nRDqjl2DPICrYJbHxuqwSp7E1fRNzHKr1aDHX+LkONA9A0zjkSb7adzKnt\r\n+V2W6jX/dnyl3f9AQbuXXauTriUmUY9g/1bW0SbM40qkEmFaiuIQKScoMn9W\r\naIqVxnIL0+nXSQWSPPEcfWo2ziIKHIBZATbhswqehAjZKA6VouoRWM1mgiJr\r\n/beA9cWU6SNvZ0KipQOFE0gmt4moj7HuMFbGAh9CnMY0hEf/cwQCOm+Y5ke2\r\nfkRyv7l5tumbPyzxCXXeGK8UjcER7T03cm0hmZTDKts7D/+FmQ2N6smlPr6+\r\nqPNhe5iW9hyAXveAKF4Bw277uvtnx6RskUYcGN/ru6sqYEjznPypNwtTWuHY\r\nZa0/nup8EMsYQ5mlMdiaVJyz4JiAwjIswsj6fdmPz/hTi5rlbEpbOOa7rj3V\r\nBagtVx4n0VpaR4Hup3EQAKsiyBR87uo/n8T0THb9fRNYTAbALXf1ziBSaI2a\r\ntCX38ofBOqxouQL0Fz+efJHGWYvN1PsnEJTROsQM/vuh7AIuM20YrYgHN/J7\r\nnfz3wMO82Tsv0duk8HULZbU9PCrXeVtBgg/OWolggkAb6hzYqNlTt54c2DaD\r\nU7CGqs8BPNFhESVbMCX8mBAMikusuEGS72c=\r\n=eAXQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "75af327491792358cc1bc41a31681cff7b553333", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-hYPYpKozNedfWl7ax0nJ1rFmlN1csHk2R8M5BPN2mzXypQLoFcuiFH6tXiUJcS1+T0L2L3DZ35m/P5hWEkOCkw==", "signatures": [{"sig": "MEUCIEMjBzb7LWTUxv5WPH8sjgvbG084LLWGJ0lvjOuBWA9IAiEAiHU+fIl9YxumfKosoS/BN0c5jruD/5jghl6hF8fkKBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj467wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCtg/+Lj+81i7NDnDqygdQP8H2QAtPP3ElEtzxSS79CqaDkKd5CvRO\r\nhCJBG54IsW9VQf1MNKUjCtW0cjsmV59qns3JvtBoqjr2ZLEcULnu9gFyopbp\r\nNlLrRbXaO89xdTlEfY6eWNwKjo0ydK5Ahrd2G+fJCPcuUWR11CkzPY5q5C6i\r\ncJQr6gz5oOEr5Pkem2ovR2/p07KhJLMjyc/eDvWfPSdIX7pKfTEyS2f/x1YM\r\no8wsaJmQD7aZLR6iOOSbCT5chR1T4XsQJYN4WwFMtZ1kz49T4sIZwz2KMPKJ\r\n7qY1tuD1saDfTCtMCrjm3/AAzOoGkH5y7zmaxlDm9EkdqvBLnEwS9FBqsIC+\r\n0V9OYW4mrX+iN3mOWFrJLCq3FQ8XHIN4d7wW2AITdx3RGG0fkeCAJ6Xt6qfm\r\nAPqlsM4U8SSoXkUnELMGqvBTlbJe76eWP+96ApAysM6dYHqj9MlGegIacCi2\r\nS1Y+WFY/DrM/hT3SM3bGeBTlh7DdjTDA/jHubyWiCQpX2rWotCcuxg7E/ELj\r\nBcWDwbsFzCu/XZJubTuuW0FoW8Kmo5zdJLoJnSafVYCLuwNbSRxvOHr8JWad\r\nvxP1TTEpHFvbxuwRZLT/cp7ZR7dkfBvHdIDq5Sskga/6Mr+U0fN8BPpL6TCs\r\nCw7VYJ2JJONFXtUoQMnWYjrVNGEbPHKsLk0=\r\n=Jmbk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ba6d7e32d95ff95979c88b9054ebd4bba79dcb56", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-EGSwqtLtBk2lZUnrI62CjXO24fEdldV7qD5+zj4Uso8oOmYp8gPayMyp/29tY1AJo3J4EAD4vJG9OOWbgiV3kw==", "signatures": [{"sig": "MEYCIQCKd7Y7gwFVzxyLwLAP3AbtEBdr29DcRC4VkV/ukwhGpwIhAKIRlrND5TEKx5Ni/Jsh793smKiwjMgdQfhSIwgWsliw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj47ImACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogjxAAis6TW3f/rvPPQnLZN1/80zqZEH3bN79tlCVsOdW5ZbmeoDbH\r\n8V6+bw24nCoKP/u4D+dW80pJrJZlQb8i2sJAmKMGlvnGs4t25pKCYrx9D8a4\r\nGXDR8NHNH3p+F7z62eSu5BtDyvpK2COGCLnB0tzzz8OWfMK6L2xLkmwPAaTb\r\nhC30OILiJMcMbjLogHdnA/LRTYVfX0CoXT6Iy954DKLqAYFNZPsvfwAGY6s1\r\ng8OHpDm+XwW20ko6LWH6QaFAQlAqhL3yUZhE4sxyvHnvEYrn139j/xo1fdlo\r\nF4Mitk79b4tpIJeUJZVy27F64sZozhseqv3dZ66325/jBmGCog+c9gzAk8Y9\r\nIw7wZrVHz/FCo3y37I9c0jnFcpbsaWcOQUhPGc5DW7FGUqlH5TGguTJvAk/b\r\nVcavJ+EmVz2OQLBS4/2PPRBBaqiAY+h9ZN9xFlRc1TZUXrvKbCh4BoTSc0f+\r\n1bE2E2p4AbU5fxQplLUYJhbXRNPqDVdQEdrLBRAUlilFtcituPKvrXocdAj8\r\nr5zRPJk28JldtJfWGKOeffPHXjaAZsw1tNg3bgyB2xoS5upQpakJoUXtev1i\r\ncEiWDkrDFq81G3aieohT3xTF9xAstmMSoDZYkamVNVpLZYrG3PuQ+jSplpXm\r\n3iUuD+rNvJn7i/cdpitoyD1IcRFonaGIDRg=\r\n=/9Ir\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.8": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9b0005c1054b0f40a45840c980c84cd8b33670ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-PKt8ZioTFAcznt0gHZ+3KttE42VHnZSQdfAdDDxf6KXPvM7UrswIbIacuFQifjcj1ynjN9YMuGTbYCkL4uDSRg==", "signatures": [{"sig": "MEUCIQDc+BpLSbw5ogbWiCqo4XzXH0vfRboziynLlzh5gBdESAIgR8ngGM/wUFVRGU9hr7JzVo0Y+iw+X6pTqFJj9IJAOnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5MAYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoueg/+In0m6ZKlm9QQejBrOk4012CeuzVEFYmRPaYN1jNmi9JDp2V4\r\nivT7I57rojyl960/o+w1cPDkmAj4uiXZCdV414NaX4HfO2Jf6MLkz0ip2oRO\r\ngB/K8hqhyKUMD+oQ7HY0U3Jp2EzGOzzRyIYIZ6p95ScCKehg5CybxWwkxw0M\r\nJPz4MqVygNqCyYAZvWYW0DTkRNvbvL9UWhJP5mqBTEMSECOrbiTX+PhFhKyQ\r\n7jtbap1dY98I7c32dIwU5ztpkcPCq4sbtNb1o8GrwJgn12KrCKO7Hl37BBet\r\np0yQxZ7e3sAFIsDSSnuHxrgup3EGeNAYcVrVLqqhP1Tl/xZmsM4KRsdBSK5f\r\n05uIvK3IOhaKyrh0GQMEN9BEMMVjfmIpgYonhUULMRu4O0W6FyG+5lATi4wv\r\nkKQhdCRllCnPG++KE8EyU35kxTBGKX6goBT68iiCFgoEpJazwEFKPAPQYamw\r\nna34vinsrTjDGLuZWIzhZlH6WRqkyarHbrdirKf+1DYxuRCA/I/THaTQVtVZ\r\n/59FgZ/1rSVa+psB2HUq+iPYtaaMg+yP0UIt9qyMwRJahmCJQdNNFBH6FhKZ\r\n9fW7ztKwR2ZPdW/4xzGSauJftVlUB2HLL//PwTlE9oMNdOyUz5bJHksvXws/\r\n+aolIRk3fjV5NpyF3DtGwzcHc/wR6MXKskg=\r\n=urKl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.9": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "71574f01cc57eb43c07d2e5b44f768cb88e77fef", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-FO6229nKv1ILaCePg6XUYsMOx0fiz+nOorT4XYg0VrnoWL77tGubYrkeBLz4s6Yl3vreFztE/tQsyXnXcAvYWw==", "signatures": [{"sig": "MEUCIGzg8AFdhOYiKZy0ogcjH+zcKb23awNjRR6WKWGezp5DAiEAuyDRpTp/sNMMClT0/Bb74p3kIgE61q2qNfHye45YTto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5OEFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+rg/+PEnWcwlghOpJACQhl14ehXVgAyT/aPJKka7rx04dg6AoW+2o\r\n9eIjXalbXo+vsIuMuu9PV9wksjKzPNiFbVBn89qOXf0bFen86Ff99Rqe06DZ\r\nXSDYUki8PZotie0ht9/dV+020vu1+YQll+EUvR6urMw4o51v59hsZV1maslY\r\nKLvpL6xQXxD6wjTkp8QmdvEyS4oHwJxy0vuXohirmcO0SgKza8tKmkFkvGvZ\r\nOJ9Y0MZogj0kCzHpnPz8lqBkonZtjeZiwTbHxhsbWkYVB2SS2ZzLx+Wk4DBD\r\n10YqTnfMZfv1VI/FFXsUlFualzcbpPYbi25VTgsofa6N2jll9TcWWNs9kJnl\r\nAS+FJBY7Aq95Qehhal3AsnESSVtDn2pJbXQ6B88PA6spQyAmAAh1yhNWZdNv\r\nXLysiGDkqmRG0JtyWxqPgEi6FJNmV5mivvRYinMUCRMH/Znk0xN3geOVTndN\r\nyZ8U8ZOMiLwQgedlyGXfbOipWHzvDUDUeH5bwNhbN9ssyEK4SRBssUF0mLwM\r\ny+Fwsjoqt6SpfDnEagb9LQPvY+Z/JmGjYf3ZScmGCdnaY6PAaEodsm3qMSqz\r\nxM+32QIM7MDv5+H7UQcrJjal8wGmmmLq48EMpzwpAoQzOfcNrKVPNw6/WGPY\r\nA1UfYjkLYn/BPFUOMIH+gkxjJ8b4wQs5cKM=\r\n=oofy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.10": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4c3a8b02624df53956e2af1c7404d0954ff34022", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-9LmOr3CkvSAfud36aOkJdbiqIDB7j2J2k9kGE9QFVncA3dzYFEcseCsq9GcG5/A3eGa/186ZSO27coL7+/ZPcg==", "signatures": [{"sig": "MEQCIBlKEmdJv1Gjy4K6260RtnwYpvSabIsocoz+NWU8Kqc3AiAlbJmGWTmD4u5O/cvkwoonsB+BC/tZFNbuFxI3WorbIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5Xd/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmor6g/5AESWnHvJTsdwaxaWyF8aOFo0qYGcMioPu/E2b2bnb5q/U6ZM\r\nU/KJshZqp3e7usYiK/MMlSxaxbIrfQcgrCUyvYcMugcvSTADUNoSNHG2KpqV\r\nwONR60xyJV8NOWfGrjnT2MxdEy83uhTbXnul/0TcZhaCGlVknukhrZGbRHYu\r\nhXdpK66Y9hCXhhuqBsnXnKt7KDZacXuBNx0t8ioU06wKfa2wMdGT0fKrPJ1J\r\nR6cDoSgn3urScYFK9FLuTJrBLbXc9sG+Xj2VcN4fxvRbLGwH0e0LK6jEnrk8\r\nm0lVtjEE1Gwopa3i8TTK7sSXIzfttlCuzu8kFiytyoKblJF7kzPaLYUSb1lB\r\nWS6rpr6Kpgq+mVp3sjtgGdpEZ8BvSb2h93USoyY/CpMb6HQ4yssGq25ZTjMu\r\nWs+LG3u/H/jEimpkx9P//XlcE+dql4NlJ70+5PVzJjkrU+Zh+wDgm6py1Jtv\r\nhbvM781sh9EzCfhD9tHlB4Ysu44b/QeNJ6+49X7PFbFR9Fmp4UNcioM5p5eA\r\nVjA13JidYkOZQh6LWbdNbi08lg9IEzW9RLsq7XGsB9JO0ou+j8nj4HR+NoOw\r\n6/j418zvfQ5nbMORWjybMx31HE1XyitlSLFeXgtKpFYAdYH+SguZxB1n2Bte\r\nTFdRvi/0dIYjlZIffF1IjU353u+XYEXqOXg=\r\n=l3Pi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.11": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "18f2cac808a1a9ff556927cb1bbd33d89c2d3795", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-RIGXnapYKbKD65vX2ogM+3s3/jw/RGwNrIFFds1W75bas1XZvOjHXr+2wH9W5pnc6c8YTtCeJ+jyzMvZQTKoIQ==", "signatures": [{"sig": "MEQCIESIrV50v/y3F5NZlcay0cloPyA5gvU9xwZoFLXpioZUAiA4FJfkewInZJMBEs25j9aPwg5OQbVILbQ7pWn9rStoTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj76uoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmDg/8CucOga1KgfLFrRBYT33zqU8dcxB1l1grYeMyDiatsYCpowO5\r\nTaSjlNmsamT2ly2KswPD/W0EeChE0EIrD0OrLRvsvqwngoDNARFSecQi8YAn\r\nUqIlEPk/8L7HuAJy72WyQh28kxAyQhBduuvzNnb/0JQdjnta+dp95F1Mlrf1\r\nn2psTKaugFBADoVKDKUN9exsk17Sdm1t6V/uyS9xJcyqGcveVDskT0zHtg8+\r\n6xKFbLTWc6t3udSLeXl+o3zBm0Jx0xYtWQBpFSPBiT8u3cCzvNP5RxQ5NpcQ\r\nSwc0RTwj+DENvDP7+Y20L/S/oQrmnFezZIh3Xp4YCA5GyIh2rJJoTRo1dIBj\r\nT/pTQ9lxNLtVoA/PYRBtnFRCxXi+TTdIfJUsEaXO0yXiykJc6R+1ofTUUaOD\r\nFjeUYzKaCGi8EJnIK95QTbpdIQD/aiynEbkO/LKKwVQPnTM/ShQz2GJ5fQFW\r\n61p3jRXUWtyERZVIEcGOIaclHCJV2l8vowRIG6UbEjIWFbvIP80P19Mfrd1Y\r\nh7/PMyfMlqunMTPBfIGfZM2UcCaUqbvp7we1TiaquGF3QfooFakN6sZ/7P6V\r\nFi2K1grbQjwD7mFUA9Km80J4pSUc38WbHqehQjDeFVLwzfNDRcgyTjoCLC+Q\r\nnBrbWQSBhB0gyKpgyNbyiqaujS0i4wHjR9U=\r\n=WqDB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.12": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a8cbb28d9448c3f0fff8bf59165d90bd0097542d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-O6Lq1tTYyPGJK1AhL7R/dTWc8A30tQ7TP7+TdBFCi7qHcci0niwN+wnrtChd9/nIz65CR9zhy80NH3O8Q3PJYQ==", "signatures": [{"sig": "MEUCIDpIh97pIhBWxQqyWNuSXUGi6HTVo990jIm1pULWNGHFAiEA4SR7DBXOfltu5ordBi2KCMuhPHCQ20YK+bs2WZKLaOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9KvCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRLQ//S8l37a/k4hDt4pVY/CUxZLQ1PFvFYlqpCld6E8TK5OxfnN0V\r\nM7ppg2z3rsBksinUpkd0p7cC/aZ6679uejtFIJ0tEFyuvNATq4/JW1E4+7eA\r\nEkfnju+A6wKZiUdvwY+X7fDY9zMKz1MacZvMCP52nUPpGPdFyskAUuvE9zCc\r\nupvKNhtVmsvKxsiIUnK2x8wGkdbitb0Bcsa7wjhvvq5ojc8RnMlgFW1Zw54A\r\n/OvF47qDZvwklfcLMAgHIvo02XCrQH21unmlaI0XBjvhvNN/p/yg0hJQTdIc\r\nKof0QREPz/B81J8ZjOUtfXW3K6IC6ryKap/tyAivX+iiWclX4jDukBKf7b2e\r\nkv76cD5PRK3AwUqPYlNcdgGFKDt6jP3wAgeFwngGvr6wo24ddFtC4Q20COV7\r\n+AbkMgIqlXkP3tC3uCJJadDnPn2hjq+yVyQYZu1ev634iRgcvvVH9l9f+2/n\r\nYCcPhqTUTgOlGh8JAHdnf+9SdeUXU8aXx2dMD5ZZdn1t2F3CEL0x/o5U6JGC\r\nRlKP3rii6HX7Zd97f+hV+uYHbrQpVs42qw93RdRQHfnHz5XFwJrMlJNN5ytk\r\nCWcOV4E9mhvfW0VOw4+CHgatWmviCyuff98IVA4eGIHf7haX3SVx+SDekhzf\r\no6kZW1iBO3xsmLGEdVVotENKs5AcnHeRMkg=\r\n=mIFT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.13": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "da91bca36eb6ca0b04eab8196c4bd78e61296a68", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-B3TDhvSKnjMW8LvGMONtKCzBK4u69KckuWK7BExL99V4oEQJGOXxAbSd4Yht5/9jc3RfsZCE6gN9LPHC+oqnFw==", "signatures": [{"sig": "MEQCIE38vjkbbVQz79CRSMdAxSlkpGBp9CPLTglefejP67COAiAe62HetEA/cl14a5GrIq+dtuqtCZ+jI8y7DDReYxyUcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9K/WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzLg/+Nxx7kkhin1S+8scwl+vp99HZ5jZxSPdqXN1eNQ54qEIIzDVf\r\nI5KRVS17FJdH+khgfNMWFJb9o0yD5tU8ZaQLB2MN5teTBWFlvcC9MmDqO9+s\r\n3O9fLxZhdQ2uVOYNOdxzv925onR4OEcG+qMxLmGXURPuK0KSu5zrXTMkI0sx\r\nanImxPU7K0BhLpNxqx7R4+BqDW8hzMM4YRuM0/KvD+IuV+7szD92LCqzB0XQ\r\nhsgSKKdWmLxIBpa02feOtelAvi9okfJA5Xov3kw0vi9QvESc+HABPnPWSpaB\r\nZiGOpk2lhLmGBoVm3jb3EdfYxb1FrG9QFHKFDwtL+8/sVHnkwwIjscPbgoN0\r\nARcXtPO5VFthAenlB7Rj8HD/9XKuCg12L2AolOqA/bhO3oRwNweDK7sIP2JD\r\n0+0/rTg1YCIZyePHzTcMXq9GPMIM/kDb6A+klyVmwHmLrYk6LlEJiBeSkVjU\r\nQxr26UGRNUNfAAfMDC79GNNjOz582fmK6eC1l3F1n9SVkdcwpm2RZy1BqSAc\r\nJDZM2xhYpYvbMkzRvPi8ta3RpVUx6SWRdK/n53lct3NBbiTjeJxRLMmlbJYb\r\nPCXorIk6y4EV4HVei+nwQPzhk3SUQpPixG2FWPup3a5RrxNhx3E+sWGU6uH4\r\noGHWqX4nY8zbW6CWcvuD4/08gNLCEPeAmPY=\r\n=0FaN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.14": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6d35979fe62cabce0c5898916cb6afd48734e97c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-Q5O8SzL7FoStDp+GeeVPJk2zGI5hB/bqR7Q1c2xWHJgQKUwDgGy9pLJdRCk35vbox4APHciD7Z3ZkzYufODglw==", "signatures": [{"sig": "MEUCIGG8Kpp/MG0TaU2LMC6vpAUIBuXtI7U2hV0cRRZ3rC1QAiEAhGSwBun5BUKJ5JJvSFL4EZsE9R8cNn6AWWMK6whS7JY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9fLCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJVw/9HmaJDDw1p6093w1aatbBgsDpZu2XYyO9hqE2Oh/6e1Zyj4NJ\r\nlgMM4M9d+7OB38vgqxWuNPCo/gZfXtNpj205lbXDHBXtRNSwRBGFm3cH8VuT\r\nyJFJ/CJiBaBPempqg5dxpyXYrPlYXNHMh8o9+nw9xnim1P0op0OaAkKaCbSv\r\npMYM6ksnfeYjGYDBzA0DgYkGU0whXOf9oghBh4YGg52ZqlGvazodAV28NDc7\r\nMCdbmYLFAplt2q0C+cvL8JReAt9eq/fBHfL/I/91eLVJKwTtSsApMaaRGpI+\r\nJv42hniB3QAU8EEdvzeE22ycg6VMfoOL28HCLrv0tl/defEUex2VuSPvq1wy\r\nj5N4TddR9zPq6ufJVXuZCMWbILHl880P8sMGemEJH/8Vv4eClJL8AQMTlcWn\r\n1wJ0jMBL3cuqhurDkd2mh3Rgxv8bu5S5sYpsGHf4bgphTm19uVdzpwMl4lKr\r\nZkeTmetwCVbwdau/gPf0CjoLYfiqvCMol9fzcaqkwTqrJH01l4OxroPvVejh\r\nyqRFMJpICKhfMM8oW50NTwdRe8YLNKNQSxTyufDPebCrits302+q5ja9mPTP\r\nak65WjbkhnbCfaAWc8+qgfcfQ5FQbxFZ8U2BwuOy8dsbmfLrtG21+7n4dS5U\r\nLSVtrUL/JIkeoied4+5Co5R+AstOv4jDakc=\r\n=ktEs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.15": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a49ab1f25b0e6e797a31687466183b3f3b0c9d56", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-ipC+zkPuhmZAZGRyI2SkDQ/DXbktTgvfsW9FzzYEOf+vCaatI/gn/CnzPDqkr2iQaGXCmaeydMCm4Dj11utC6A==", "signatures": [{"sig": "MEUCIQCYCrPwckmG1EqW8DEtyOob7PW+9fnO2J1iSLc2JjYXNwIgSsAFSs4p6ZPzUEjvtSp0Xox7DkVsyPYZl2o4BgwWAIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9fnwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppXQ//YuBevLt4IQnJZnySfvMriHgCzAgdtuGDdiu4Tc0D9bNHVJay\r\nkPGDHIRs1mNM+o9IGRDOA96Vzs9XKHN0xYr6lIe7m4vF7U7RbVXbONuc7dO5\r\nbbb5Cy0qBU4KPWSUYP7B8rQ6CsZ8/BS3vBNe7jqb0B1TZPMuWLruulDJAQyW\r\nMLioJ84RIv+o+V7om7V2E7Ea4J0Xs5Yax37JcSwNVxlxU10vmBzhzjgzTlxs\r\n5c8i2NsJgy5RKncmEr3gH7ij8ETJDY3hA7yCADzkUlTdXXUueUqZ9C6GKotE\r\ns5zBGpoqEmRs/Ptbj0FTcGz9vOGvhSTpw6PN7lDOLFU8HW4g7TKM1WWiGORu\r\n0KsAAI4KQTCJ+KUnVX+qGSjlenXZ2LRmNGPIBTHRIRb0vJQsk492dod8eo/Z\r\nE5Jh0AupA92kCN8e8PeKncq2wsXE3QCmu5ajoXuUzP5lKrcTR0KKb3sZs/Dx\r\nwQJsl+rkOTVDGWN+DrQTwO2oxA1vdecGYIxhE80vFtSrIA3YqnPWazayjs//\r\n8KMwoR01tZRnAbv/zzp6gzFJi7pImjJZNxVc+5IG6NarP34OK0qIQ30XV8lG\r\nDNuDS4UacFfmJE5qSpmkVzXfiT55S631azLQkLHwxpcDbDuWbz0xF9lkMcGE\r\ndmsmIUnJeSjL6CBkgDpZ7RIcqgBw+Aoc6Fs=\r\n=OWFA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.16": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8416942d697d72086c93ac25b5cbf037dcce86e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-zi6tzSvy575RxvMKFb2u07fCJpO2Dm6bwZD94AYlMkFvatIkr6NiWyEFvatSE2gRrGFEgN7kdcgkhqLZ8lPX/g==", "signatures": [{"sig": "MEUCIDlwjusdbDGPcSc+vKTBUBuN/QLneHCvKwrLLsau+OuBAiEA4NlAEjWQDF6hRw+Kcq3Ap7dqIg8CwjJV/j++92BazBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9f0rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxpxAAky6oxAT8hUn42OukhZ7/4yI62UnhqdAjPsLgtXoIJLaMw3Ua\r\nxDrPXUBZy8xQUyk4qnWUSn3b3qPffUpapzCc2GLSmYjKeRPdBBC0yJUkxtpB\r\nBNv/VkzMGfF0ECps/xrfcAP6A/Qvuv80ik2dj78MsqxB+TwMfct2Tzj+VzfJ\r\nzKfE4+SfXDyVLWVQloeg+cPBSvKjWhipQjoqWfK1+OoW5HW8Hh6LfAiNvS3T\r\nyBhLR7gO0BAik1NrXmCUG6VN3+pj6rwWsML87Fho98YBCv5UxYdyNBnUn9Pc\r\nsKIa1KJJYtOyk0bL3tJG95KUoi4OVFkl+58MozV1o4Dnd593pExUcJU0W5Rv\r\ntxMIkvsibzSTkCvqgoDxKz0hjALYKEn2A1EjR+N0N84ojeMWFQMB8K9FPa/t\r\nxvNBEPehakMTHPw/um4RL4I6VVolYC0EmC+mjSi0eYH/njmnU7o5rwQjCEp3\r\n3rwQy/glM2QaBfkSkQRdSF8s393Fyf9zWQYtm89teIo3O4UP23K9jjNa1BoR\r\nscm/p7n9Vsl5ZJ/RW+mqmT4QBxmHmm5MVTBZXBW9pMIBz5YZPhzk08Lt4T5O\r\nWqFpO2VD4hlOv6Qjp91g4iH5pX/wLiQMthYUpEoWB1HDMCJg38Ddjz8pW9aE\r\nl0rwFr+De5O0R9fiqqpOejLsGc1c5IZHaO8=\r\n=dYA+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.17": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "28764c1a1de7298449f1016a85c242dc6af1bbd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-cIfgVOX4/qku+j9NE0QXUy2ssZ7d+lJQczxOfJujTQRbSQJV8TQJ4B7DewFPM8VGfhdizL81yNAhkZCOCeF3lA==", "signatures": [{"sig": "MEUCIQCvDZZrXf6XV1xABryNBdEsVGDKJE9oGth7bUn8JMXaPwIgB9mhdC0sto5e11z1CDzWlvuCKjh+E7UXCCwYbYFO9Ac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9hXjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFRQ/+NqT57lX3WGUOE2glb0Lpwktyz1p0AtNXSiPcmILicvzdSag+\r\n89mRwC4TfYu7Jhe89i6QRuwWgpSlNAyzeX/u7GA9TxV2eoE6bJm9wIKby7op\r\ngWN8YZZvuibH2KkbmhQHkt/fiWW+J/BK6+q1PfcHsOYHzbl1/UDtz681u6G2\r\ns6nLkXqJ+Vl04mWdRI/MOws7npc2pWDsKnXRjPSSU5uwXfqH9Rv+X1AY0cj6\r\nCgg74hUT/T3hLeDhfwe0t6IRyVNbc94C7J6t51nTMy+CVqXX8Whj+m8W6g9Z\r\nsqSrbankqW/TF1BHqTyIHZUE+8nczzU40Z0pOrSOt+kY0hGwF3okYX1uGazl\r\nW5hzDOsE5E8OE2non+ELJ/CzrOnHtPmlc5m4Yz3PyYf7Pfwtl0AkoUwltbxw\r\nVmLAHkLCDagJwyZlFWanUFym44dnijjmKM088DvKu7hE1pepLdMVW+k2gkhr\r\nNYMuakIe49lm6D2yHzRNbDGfBKJ0RZQUkpgg8lRb4r0ImRAFwhtZKICF3akU\r\nEwUfelvYtWAd+vxZs5tpowpGJlLirlJ4hsvem6sukDt8Q8SVQff431ra0L/a\r\n0fGARTrDhBdrQ8aLQlg1Sr/4UF0fukeulsbl/MrFpPFwkB1FQ9S7I6ngmfwH\r\nZDRK7xYA/HLHiWvr6FhkiM8hE1jHyoGJMqw=\r\n=Ax05\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.18": {"name": "@radix-ui/react-checkbox", "version": "1.0.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a838eafa300c38f4bf900f722f20cdd7c899b28", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-u7YTV6Oo6uZuo6+omesTtSWRa/cVeSeQsmg4gGDvP/A+ghu2QbiU0BJp2tKZvNUNv6+8PYxFkycROBWM0w3TAQ==", "signatures": [{"sig": "MEUCIFIiiDXReV00NBdhjljpCmMCwDW0Z7i2ncFfKh1OJ8GlAiEAmdvx9tOjXrIkc3xQXmj+Coe+0w6gi6fTTPLz4N9dW14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+IncACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSsxAAoDoDLvNxa4cZQQCxogu3ku75cxW+Ht35xZ5bu/a6q9kgSIVY\r\nYvPvorTSywhr3MzsYZUER0bwCwrT8RbskbL7OwXW0yILA6+m/CBs1k92lEAI\r\nKwtpB5l/buxLhazrsMomCBDEjRDcRDM+g1fp0uaGE+JjDSpi6Lk1ehvW5aws\r\n9+cnNt5dLBp7EfMYkFklnlx8YMhh1izFmC0+9wD6Gag8MOrMS96DmyUVyknP\r\nlNQU9ow/oh58DlUrfROABB053icfVSGeABKWSWeVSJt8h69uBRUsyLj2ltBe\r\n4DU4DcTROymPKTCMKGGmHmfWrxbQpOHMWcAEH4537rmQoJQrxto1NtYBjZPk\r\nQQab2OVTZ88GFPRsx94E3k+5+Nwr3PKhlE+TE8GQpkJdF7d9OV+Hm49WOJ7k\r\noGfPo27/rHCacCZKQUEZSLxEPknRJb2OPrKxkS4xv5upJ3+GMSW7bSzgvHbS\r\nwfTdAKvTUWN/nbk5NCaBZdzI1yeGJglKeGUUU4uMJmKdYhdM9JoXylYYrYEh\r\n/VHGHUCsyPgsxL0ka/u73rbnD701pzNz12bxyJfyl9DvvFWE0DHFlsWkb3X4\r\nbj2icWrm5bwk9s0E6gYXhAwGaalqMwXLJaTcKm4QTXDj7BjN31jRIf3XIisK\r\n+RDqAWFtZfmIfuZya3CIUir1SuoZmranfmc=\r\n=HAwp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-checkbox", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d51c6b3623aaf04c894bbd9bbea3fc3747b992c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-ZaPRwTU6CM/+S3enyeyhjZ+wIVLulNiWDsF2+IWhs41QEbP/cYTb0LbAfSlF91D5IH6RZ4crP0qzbmYUAh0qig==", "signatures": [{"sig": "MEUCIHTuNqylf4s+oWTHqYMoD2yYXLNEWJUTm3FgWX3m2RhuAiEAzVqFaudDW9eeTjeK8EaqkO3MBQ4sTCjTyMGDfxX9mK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+LDJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXgg//YXkU8OB2bDoUDOW4SKpS67WIWwsTmp2a3h2jJl71aD/g6I1m\r\nML4kg+wO21xV4KtBzA756ORkSCPhbR3yadeEfRBUfdHdSmDt/iTUZ5qdPlfA\r\nWy+b9ZGaJE/+iwQghQG/VVDxA4tCPKdZqBVE7fuaV63U/Kgi8bKe1gUp4DOa\r\nh/kQ3vdUVA20zI9dyO244PtvntGnveaUNuZHKhezTY3QT2cw80NCHVLzQDEM\r\nWyteiSJuytyaikFxg0On7JaociSY/PWoUBVsLUR7Dg5Vtt8flMevPC6iDaI0\r\nG2/pyYt8acDoPorFtuv4JaS0uQ2rqpJA78EUevjK8FaOJW9vy+FAC2nt+iXR\r\nn+9jJ3t90V7mEfu0dFesWGPB4tgW+VAtNetvjg1ojvbElzTvqAyfU/5ItcwU\r\nysEiBdJAI/H08XHhyZO0wG0n/YCFSBmtIBuoMxUNX13Ixm4GokQoT11IWcCF\r\nZx0jOiUlDzxoI2ptqZkcQ5XgNiDEEkUsESEVwCZr/o1sB/1JVGZRgYbJVjJO\r\nXGaClNUQFgsIa/o9MbWtwyiES2HEQyEWHXcBfPeN9TV6Zi9Md23ZxIRDDeUi\r\nyuzc6/zt84ZUPpti5TzYS0djoV4S4xFxztp1nFGIgHlfi+T6sJYkgCEs0xke\r\nKVE64HEeqkKXCy+ZZ8o+tHKU5L1+DEj86JQ=\r\n=uvPu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55a52ecb85bf8dde7ad04b827f3fc7d50575b6b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-5Zw4kJ3SEOzaDMne66icZ/jrI/54numM48JClEu/YOFqNXUc8ES62Axcloz+8SWi1zyQNXCknyTRPH5kI43Wpw==", "signatures": [{"sig": "MEUCICQpuYFSn5ecjwPGmEoYVQ5SwF6K07AFqy+sJ6hWDZOfAiEA8bAzpam++SiARVmkSthHUp+GBTK+idglb96QnsNXsvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBQxAAh+w+FH+T26VNO9sjY1LHIl+CGZQf/3NRjChlL4aXSlHjalyD\r\n8ZmOOp+jXHqtTJUgVvKg/Z4o1Rz9UQUa+tUdFHqvbZTHwxfGxBVTGAOBfmgm\r\nNjMblwp56jEFr6yWwNucPwjRLJ9pvooXKOAxKdIuncAfr4iQyeexic+XmBBq\r\n+Xr/542vvT3igonkGl5XSSsc7oRZL3CTYIXEaFvYBVwV0QZeOZQ85/8hHNDn\r\nAojX6Q/dywIT9aRaWhKe6saLKK4si6x+wp8dNwPabO/lWIn8+JrpbKs9Jx6C\r\nAUZoe1xbqbeJ+tan5LylSq/WHiPsfwsIs1cWyuaRJ6XpLQmiVQsE1nvDHrnX\r\nyKq1FGHtoKJq5fM4vGSL/v29dRLCnu7aGn8hjEvOLhg7xizDLil222sZkwiZ\r\nHfo0sM8Y/zE3B2MZABtbCR0lmCVk8KuF+Td3fzXPW2VzAaKItpbto9I+IRoH\r\nfhTBVxdnxj0E6rwZZX3ymq7NmSVe326ZdXfYADm1f3YXTgAqF6tyLGelEOe4\r\n0kcw8Hb5s7L4a9m4mcxNruYIlZng4be32nyF3wX88oLb8mJl8JjkxtyOnsIr\r\nqgm7cLoUtBDdKqVeNQ787ve0iBTUT0twvSVJ3HCzbMbdqyvb28RE/fxikWrD\r\nM2MAfuBB1/QQFyDhIkN9LronCWKfOHvTWVg=\r\n=e9pP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@radix-ui/react-checkbox", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bab1916c04b9f66e944e7168be2217cb74c7b94e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-55B8/vKzTuzxllH5sGJO4zaBf9gYpJuJRRzaOKm+0oAefRnMvbf+Kgww7IOANVN0w3z7agFJgtnXaZl8Uj95AA==", "signatures": [{"sig": "MEYCIQC8qnWSkaWseKnUB7qQN2HAZI5waoAe47YnvoGBeMpW8QIhAPzFgAeVPVNs3M2NFWK3V5A1eWHOzqC2XwIMiFN1EcWl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJakACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzJw//eJZQFI+ScooDTyycWDoDJ7i3BBgpPMrr3jS7TMcH1FCeSIUW\r\n5kgK3rDmsoK3aCrkO2MW45HIDCI8FQ/bhIuRoTiQ92ohirzP0CwM9/Ppqnhm\r\n6kHqgmxjpNOdZ0+KfKVmvCYBkEe4QwXt7wvTmx1gVgu5BWeQmzzm4vuKQA13\r\nl94hX7pFbZ/I6yw1IhcHweUwRAEunfam31YtOcxIDn4elXqDBWPvhMbi0Xs7\r\nzNwe5pI0CqodJzJuusWvGzms4Kcbye58kDOr3dywRLSIOCDE2aWxDD1q+lcX\r\nxCrTkOZkcsLEQwPr/D9odJoQkxwfS0b30gHssshX2+OJuyshYCVmsC3R17kJ\r\n/bXrKXGC5wvYr+IMLjnA8MDnjbN9mUIlsJEiWzb1Mt8kXiDpi0P6OFNH1Pq7\r\nkQ3h8RaSPFFN1YEAp245A1yaRrZYXxzi8btWm6KHdnQZNmBP/wErcORlysPl\r\nfAh2jUZU3G8iIxc5A/gZBc/OwvCahPpgqg2svzbpazQIkTOXlQJjkksEyBb7\r\noCY+cZ7yubyZWf88e8DvE3EUFPxyPdB00T8AIM1X+iTTYpsC9UAOZdfYhunQ\r\nEv0z/l8Bi5uOUanBgcnXmRDx7OqKsQDXiWsJ7BNyyeVKY/09j2OZ5AZf3HYa\r\nN0MIg46QXzsq5ie6NdYz3RjyMbZPzuuKJCs=\r\n=0fM4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c619f841c96c509bb6d0f5d5ce785976de162032", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WgjnIz1KD5LA8dazedZ7w9ZV8tsNjB7+5Tl6JroisY2gKuqmrSsdEwWe0U1ly8jiJEdDCgTVNZpfDdzO0k7QRw==", "signatures": [{"sig": "MEYCIQCiOZIhyYGNT0w8bvI3Xm5ZXLhWud7in9RHw2lDFplE+gIhALowhbfK8bPLkhsgUuDShRtQ83BaCBIT7SWcYlLO/PFp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHCw/+Ms7vSPUKd7HwTbYryN6BFM5J5/oPRNWvf7tGsbNkjQB4uwni\r\ny1Ve7Gci9FPi+gSm6FuROcnD/iLWSErUr5pUZ2+II7GnbW9S2rzVUlPR3uq4\r\nv2ldscHy4X/9Hw322lfmdU3S/V1ikUPhLDLvMpFTkx14LEKFdswSxwNuYZpk\r\nvDT2Lx1qSlqCUFWxh+ExsEcHtSQShp/IlXjIxoTjGizRcyO9zLDSdTew7UQl\r\n8xUlted7FuwP4rp7r6tSMX/SmBiPF5QwcwVxW1ix5JYEDFbCg6tpEVnFKkAC\r\nV7XENVAfX5jvAIsruYReA5dB+dgQ8HLeQX1Y8XVw5Mml4xQiTsCXZwC304Kk\r\nxoiuFI+YQLiwV4Jv53v9WbVgKNCbr5oG9E/MMBtlc5OUaUhlCO4pV0KwsCDU\r\nymNXY6QQ1AB0wwlYNjssM0Vqc9KlUTk7rzR+SEFNDHsK4kUkkWlRSM34j8Ti\r\n2Awo54LiBY9Rr/ZeChFlB2PYY/pu9qFcKwF/cbvYcUsOnn7HB+QFaItO7ND/\r\ngj80bHYWiMDG0AclE4nlYhomCL7AEnM9Xj2P5foidR34LR3YOKERQjRXt80B\r\nZw2sbqCPiPbQYQjNiOfecgI0a+o8ALDGZVk85poMSBdSL6/A5b6Z/IIXsCYw\r\n7CFseUDvqW4Ou56reims/iEXrxvIVM4BPpQ=\r\n=DVxx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4b73a1ea17794eb9c0aaaead07c8e270cf3d213", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-V+/HrsHXoIeIPOsgNDt5oqc1wzYFmodukgdgXCYFh3dt3mksdYPXHLhL5lbTwLBWGodTPLkpM/TmiN06Zs+e2Q==", "signatures": [{"sig": "MEQCIGmoexp93rDgs4FgZx4wiETP2Be1qWE9qWgqRDo8fBc9AiBKNHM40eXGJFRJUG0X+VXxXj1/mblzWO6qoScWetxUtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516}}, "1.0.4-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5ec8556b62547d0a4a14580f69347a85de0d586c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xlo4tI8QN8VFlPEl7rB56AOO07XgYVJRm5VOq1mvPH41qaHU4NbLhSAaJY6PJTTWPBLM0mqMM7XBO1CUs8mqTQ==", "signatures": [{"sig": "MEYCIQD9WkwIUv5UiDBg2lLD6n5Zuik0xmm2qVsL88A/ydBG2AIhAMdMFYA6NJSB2B3HOZnrifX/95KBFFvFb1MshLkVWtO8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516}}, "1.0.4-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "287f8c9c069a75559b28137c43f7da5d14275828", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-af2XEMzDqfcaMoCRTi4prn1E4FCfZy1jEFCaUZyb6ZcWVtc1En/regJCdlhoGG1eHhj4w98SBBmgmTbQdQNBiQ==", "signatures": [{"sig": "MEUCIQCwoFGxQtc/Ya9pt/cVNXYd+PRQKyLTgZZOtxNLLRa0LAIgMfcsF/CTFMkp53xBHTrKq9RVyAewHo7SGDXsu1ALykk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516}}, "1.0.4-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "46a4c9585331f94382c9a81faa0a0c709aa2fb68", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-DQt7X8jDRfWcbacJuIDfQUjRnXybzNVdx1ncbOZoEkd0eR6z3HI8S6re42CTyZF8DxMsFdhKjsrFChBL/xQy/w==", "signatures": [{"sig": "MEQCIDrgJYtnk4BDkToxagQ4tUFlxTO8S0UhuKtu+CNNAyk9AiACFmripQo/M1ajPs9KxuwBR+F+kMpYAR7J34GT5dfZsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53516}}, "1.0.4-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-presence": "1.0.1-rc.1", "@radix-ui/react-use-size": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-previous": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ad6f1a72e3e2c2301231cbc12cdd1122d031850", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-i20VJtLE1kyjhzypenBSKrods2zL1gCI2ysQmp6r1u/Q0sxSDj6n4RXhx6Jx03qbwewP/cU34UmeELVDEBdj9w==", "signatures": [{"sig": "MEUCIBFwojjDql7Eu06gj8C9I6PlMMSX92NABx3ham3wQOmiAiEA/LHwagU+sftRv6Tm49h3CxpCaPP9tssbfz3aivMgBKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55202}}, "1.0.4-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-presence": "1.0.1-rc.2", "@radix-ui/react-use-size": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-previous": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f023dfb463c6c92b4fd6e25d2bb44970d058925a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-Kbtrj09dcakpgubeRdw6MA85ZyQQrF0No8sgPSqxVd2K+NsrNyT81f+Dwiquk8DT3Do+0gcAks0/2+H/40ZF7w==", "signatures": [{"sig": "MEYCIQCzZ8iIa5fEYZFLEYkt+SEWrm6i44ZRhmrdLxzImWztTQIhANXzD2Gw/diYcHL3sZXhPv2iSShvar9QntIhiiLAviVJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55202}}, "1.0.4-rc.8": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-presence": "1.0.1-rc.3", "@radix-ui/react-use-size": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-previous": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5995917d6825da9ddb3a336a51a0b63d059de538", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-7rJCrYoRAgCm2pJJGnQMumq8hQlRh4iNFhPStb4Kj42uLFx9A33CATXGndY1k6Zq1aLXMk9VyEBE4rRbl5DV8w==", "signatures": [{"sig": "MEYCIQCinrjlh9nSJJHOYYuxvVYRoRDiiRaHnMjiC1atZca6pAIhAM/6fuJlagtdRFyBV3M/PqOyNyuFo7P7rdYMWzGttdYi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55396}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.9": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-presence": "1.0.1-rc.4", "@radix-ui/react-use-size": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-previous": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eaa440f0aea1d1ba6ab4eee70db701c9587ea08d", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-Ira+aDPCzoyZrzMC/u7jQqVavTapxBMrvEB8+qZkCYNydMoeruzVxJuhICcbWnVENUhJEZCo8n9/0S6ShFNMOg==", "signatures": [{"sig": "MEQCIBq8anLDrCsMhoUcwAAOz8UKM15MQYsEF1UQIpeMy1DtAiA4Nd0VD4uTvnEMMM2mMKJMhKZ6+GK/QkVaEzf4Z5TDaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55396}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.10": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-presence": "1.0.1-rc.5", "@radix-ui/react-use-size": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-previous": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d04366983645fd0cb9e01d269e11d3bef151573f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.10.tgz", "fileCount": 9, "integrity": "sha512-mXC0bspQRW9Y74fcVgIQ8/TF31P1O3DI7jO+/27pAlbArTCthk2+Kc0S8DjUuKZs7OnS5HQug8F3Kh68S13Fuw==", "signatures": [{"sig": "MEQCIHZm93wOvks3arzV7rdqpNmz041ZAS1bzqi2AoA3UHBrAiBIDSVKtDGF5Id8h6ehBdWdX/1K8e1C7chYq8s2Por/TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55398}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.11": {"name": "@radix-ui/react-checkbox", "version": "1.0.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-presence": "1.0.1-rc.6", "@radix-ui/react-use-size": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-previous": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "407f4e3e9bbf8bacf0a2d49bde9104fb5735a71b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4-rc.11.tgz", "fileCount": 9, "integrity": "sha512-UT56RYisB5B/DOV3wCwEpMCNhOZD6OEsyoj2j+O6UwLK/rqblYPg+VeWJFtoEKALhlcLmBAEYhKBBj6ZpyKIqQ==", "signatures": [{"sig": "MEUCIQDTw+Fu2Srt4S21zpkQN33ORL3fBpB6VRJIXq7rZ3Er0gIgYP6lGdMi794Ewo98nnY6Cs8dIl7GG+Q/reWXUX1fWT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55398}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-checkbox", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98f22c38d5010dd6df4c5744cac74087e3275f4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-CBuGQa52aAYnADZVt/KBQzXrwx6TqnlwtcIPGtVt5JkkzQwMOLJjPukimhfKEr4GQNd43C+djUh5Ikopj8pSLg==", "signatures": [{"sig": "MEYCIQD6wE4Fz8oq1XyOnLvyUVI9kxVkL6Gn3WehYpKsQMd8JgIhAKn72U6J6xpcOEdl5S8XQ8624YfAA3aa47+8C3LCGlUD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55323}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-presence": "1.1.0-rc.1", "@radix-ui/react-use-size": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-previous": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7fcbc131991bde302f4496a9a73ec1507c240fe3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-1/z7WI3Th/ee6xSzeV7OIrn7YC0Wpot2a4jeWzDjqsUQKq12fNzcxZgkqQ0lxrjSumjy7tKiPHD4BnSR+6cnfQ==", "signatures": [{"sig": "MEYCIQCYmWQx0BMptiaeRDlPKvBDMUKdxNoC/uGFbhl8Sn714wIhANhga9vMgecIqBeB+b9qNs4035AcbIdUS6RBxnLRaJnt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42593}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-presence": "1.1.0-rc.2", "@radix-ui/react-use-size": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-previous": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "338ea30c7095726193e9fa7c59ff424904c80d06", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-69Ga2rzK478nxhYwJ6HJDNrkgkKyytRcTkGa0kR49pf0cUVeeRQ4ZM22Ou1paFuoCWg9vYvXTjdDthLKESAnRQ==", "signatures": [{"sig": "MEUCIQDEDyqZLGf5cyBWzRDPqeyZMm1yKbSTsiWlg+0xaO9ceAIgcvB1kuWrK2vRIK/WVJb7/8FuYrNMByM6du7cBa/XUlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42625}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-presence": "1.1.0-rc.3", "@radix-ui/react-use-size": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-previous": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "34439c808053e55f25187463cea74a7d6b40997a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-HX0jvmvg0ogdY1piMvyU1at3NTjWsQeXGRNNh8lwjysnqVijx//4bO9/p1tBbP47pmeayIAwMZNYWyaHFHvbmw==", "signatures": [{"sig": "MEUCIQDAQgEy/o33nUS7C4asyRfLqnWbZdB53AISkMyj5llWygIgR4GBtxKzgPoIq/T+L5Tvej5VdjnDOmUe+TXjndWbAgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42651}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-presence": "1.1.0-rc.4", "@radix-ui/react-use-size": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-previous": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "37903d2559d3e44a9b32ae117f1e3f3261d1f565", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-pEcKiObFWzOdF2E0izt9we5hce3X5Nvwz/MHIUCsRgeCyDPB1wQSDUGg4HbihaSpQgI06PUbWuAY5kZEBFIlOw==", "signatures": [{"sig": "MEYCIQDIGVnmUpDmQrvD3hW3vsEMzFaVWSe4RULc+BKEuecsjgIhAO9eucawsRcrNgN1eBoYl8kmOCs0froW1i53tLr+zmp0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42369}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-presence": "1.1.0-rc.5", "@radix-ui/react-use-size": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-previous": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8480cd9a6c8a032ad08c6ee4b9ca1c2985799df8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-LP6SLMuzgqBvE3/+jneew7PwCqEpIfK47ZJoudQ7BPzU8RIJc5Er41ic/uguYbdd1gItBNmAy4zmBfRiSmsoPA==", "signatures": [{"sig": "MEQCIHdmVIwqIZ1ohtNA8rVl9QSkfzZNe4s3i8HYdo+B0043AiAqbAqVj1GMOhnch6ef7P/X5RTxjpM/s83rEw9XbJKA+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42369}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-presence": "1.1.0-rc.6", "@radix-ui/react-use-size": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-previous": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bbeafc9e0ced5c49a831479599399d24006777e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xlFBfjnJcz0SZwOeewlWCY5xvgtZckfhd5iplgqCOJwfr3hxU9BQxVrShOX7nXO43YiifHlOMAMnZes0mOJkxA==", "signatures": [{"sig": "MEUCIQCDU+PlPjbZGDs0A1G8VeV7OLTPszoZrhG/XZLZhjHdHgIge0qDPPXiuHotlBHM/1iOwdzBbzG9PlsUxmzPAMgJe7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42369}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-presence": "1.1.0-rc.7", "@radix-ui/react-use-size": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-previous": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7cb14114d21fa6ffdb0662745e0841572641645", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-wX33wa00kwE6xPx1+jX9zkmBVg63IPtigQRYoGqWHbH8ZelQHHZujm0oqIoiaCz3D3efQqhDrc60GuXmDSd06w==", "signatures": [{"sig": "MEYCIQDmp4UTUv3SoOfHvqvljyAWwaeyEleF7MDwxzdMALZKIQIhAJFsK3l78XZWcDmnMVrJeBZy1b1wRyOeKaxvAaclJSZW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42397}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-checkbox", "version": "1.1.0", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ecfcdc4bd27f0606931c328836a09cc76085307a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-3+kSzVfMONtP3B6CvaOrXLVTyGYws7tGmG5kOY0AfyH9sexkLytIwciNwjZhY0RoGOEbxI7bMS21XYB8H5itWQ==", "signatures": [{"sig": "MEYCIQDL5w3gTRhWcLl6Hrb5ZJph2IZ95CXlNCCvudNVcGGQ6wIhAIm3ovZFF3MrLn0/BKW9I5myNtJOyA+ji6ZKLx83k1MU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42324}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3c6d44fa534acbbcee4fc8bff668ba753a761440", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-MlmV++7O6/oOqtKqlTA91mv5qdHP51bsTgmFdJkeOUef9r10szDTMEBROdBwQVlKkk0IMpD1rX3CamIrknHJWw==", "signatures": [{"sig": "MEQCIAD3jBLds9LCH+q2AckcuR8LyEO6mtkZIjPP0ksCBtk6AiAhucCXmPJZP5fahPChyQ2J2V6fBeIyxwOztFKPoq0F2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42437}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a559c4303957d797acee99914480b755aa1f27d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-0i/EKJ222Afa1FE0C6pNJxDq1itzcl3HChE9DwskA4th4KRse8ojx8a1nVcOjwJdbpDLcz7uol77yYnQNMHdKw==", "signatures": [{"sig": "MEYCIQCdoaveeb1MMHDmwJbPDskSniQBcTasPD4hpggN7gPQuQIhAPLOU7eg9V4AOS68Kh6OJFGyuH6Vs8RCibr0faGSaOYI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42404}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "271f21fd5129ace217b68cf59358d714e5bbb6eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-/oroAcYs3TKBPtGji27OaDx6fn9XygVmNcMtkrC0FLDKIw9thmgCbUWDcHUEsz0Bj5GFSB2B61G2lNYCn2v8EQ==", "signatures": [{"sig": "MEYCIQDtv/kZbFywYBImxFJiPc3rftdJlB11Rtuo7zmk1qKtEgIhAP4t+SG42hqFlYmmpz6+uwIoG13KAExwBbXx9jez/jDT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42442}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6fbc38320f52a8fb61aa4b39f6123ad1cd116224", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-lAqU7ALlu7sMJKh+em63fnG2xHKY+d8TbjOV9lsYTsyzwXaL+E/05BNRd0ATfP0Y79IFYz/343XhVESaPcyceQ==", "signatures": [{"sig": "MEQCIEJy7k9B+4gUH3ewobzkBrCTCSUVwQr0Fs0m6prxFS4/AiASFi+zBC1hihrl4nwxL0RNajKJDN+7j+iB6mFMbWQ19Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42442}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.3", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6cae4342350ccbf01b6965f40f73657dcd00946f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-hku03auHEoiqWQZXGKS4pwFl4s8KCzERu1Tfi5xUvSp6n5ViuMmDofQ1u4DnHA+FzUlAi8ukjuiO8oV/VZrPUw==", "signatures": [{"sig": "MEUCIDvrbujwMP9fBMTk1ufgz3DQeeYUdWGGXr49Q7JV8ypsAiEAwqKP56jJSSyMOjuCdS+d2zWq7f5NE/vEK/GsK8p3L3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.4", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "69e33dd2c8d04dec6afc621d8942d10e8f71c7f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Be6TOUCV8dv0avf2JiIYpQK3sKzDBiLXpAurZBiHsqvDWFRzDD0vkyG6vukgtPhNYF/jzlKpRMpEIBVS4hMukA==", "signatures": [{"sig": "MEQCIBABBcoPzm2e4qhBk6MVeoqY1VEzEZ9k9SFTvhFUfWXTAiBqUMbj8BnQwClkH6brU71kOmzKfzK6zwAZjfB3n8plOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.5", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b7998ca6dd4cc25896c80e6fab307d96ec55783", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-TKP0SXcxizxW9Ez1Ln6wEbcHzPBvAAaK6EUJ7sW+mFzI50k0JV6qgcXusPwiN9qFEOllVLG0peqDeJtMbRsUxg==", "signatures": [{"sig": "MEYCIQDnjmzltcQ3naGoHEmbr4u9T1bBAQoVHFux+IcO4EE2IgIhAK7tjb9k7oLcgd4Vz70I9ODtpGc9c5EMGhz14vm5y079", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.6", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a638690bdea77a379428a6cc39936ef926f5544c", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-QO7FI0u9477wSBx2rZu/DKje/ov/woB3vX6IAQ1Ixec6UvGe84jjvAmJ6Bhdu4g5p+WTuQ06Qyw2tqpGpyVl3w==", "signatures": [{"sig": "MEUCIQCceIiW90sPG3iwGEwPqH05zU+5uxgN2utJrcUmgaLmngIgeQd7fe6T7oMF1wLtLEoh5qpIQbknzXY4FS2u7AwT0FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.7", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "414426e98cfce8e0acc9c17b106975e105611def", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-y+MuIHg3o3/bzQRHPpPb/vc0ANWQHPigi4CUw5DCqb54mbgGnrCrlJu/CAjcnADVDNlWXOJCb/j3VlUoCms/Iw==", "signatures": [{"sig": "MEUCIQDsvfmJNjn1DY98+OzrRR+oLv0Pqf7ZABZa6QhqeacjUwIgGQYTf59JQEBTP9hnpei2zwRmWXz1GzjRN4mHI6BAfUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.8": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.8", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ae7e92315ee8e04520851e4d7354a9cc4f7ba74b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-5XO0m82a4LBPklyPV08JBG1HnqE32BL1vqWYQADzmVrslO3sRw9Hc2gRx6ZIdFUaQbkTubQRo+9pR/WHPXHTbQ==", "signatures": [{"sig": "MEUCIQCLi7iwquqacqURyaWUdmHNjbav2AJJ+Y/CMD6P41ueRwIgW73i8I3HrpMVuNaCysL9MtHjn7rPGbMx8GUa71eCqWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43376}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-presence": "1.1.1-rc.9", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2c408545ff1f4c905aeb20ba09b03b6b971adef7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-6oW9LDc0mjbT8dgz/7Xi54ca/SqzDVkMf7wGPpElAICtyBqUfYer8E3wZ8TF4NRksLsh1pNOtj8iprvVvmfr5Q==", "signatures": [{"sig": "MEUCIGg9n4AvNfgw3m+HilGd0VKpMbBpiXQm1AjgxAmgntzzAiEAqjFPNN+9M4HZBqWz6ptgRu4EwwZXT2m+BEg32w1qC7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43381}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.10": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.10", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-presence": "1.1.1-rc.10", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8d4b741582bd3afbcff70391db60ba7626572f23", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-4n/tesQgB3Gsm69SdM9GtN+HYYRfP6NstiACOfECEgJAsFGuSsjknFcOZ0m8+Q+4ZlzAqNFufkxTqtoxyaTJtw==", "signatures": [{"sig": "MEYCIQCYFe4xRhOaAouAi0uHBNZQI1AN3QrAQoWx/52IAmFNNgIhAKY6FwjpSheJEMM1ARGFPkWLPd2OIixGK379HPUf1bbO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.11": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.11", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-presence": "1.1.1-rc.11", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "adb84d1f0bbf7786111a0bba09373c4cd6f484f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-xywxxj1GOxv0hkS9DQy3PMkP3MUqafaufS5mzhKn1kghqP7TUiIAjH7Uigr2/0PUtvbNeHAbsgexQayqdTbEyg==", "signatures": [{"sig": "MEYCIQC6CDfMKxo0UJuxDfR//wmqw843weVd924czJTvsyz8UQIhALchs7seBWJiM6J00URjeFSoo+nLpt+gCvOK6AREWkQK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.12": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.12", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-presence": "1.1.1-rc.12", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7dd57946d2bef76eef4779f1012ff3826d73d873", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-UtX/l6WcswbOA/rcDPQG1/pWIFCWZNRmKRFuUnA/1hBkniR0BJJanwvTrtokU7bC1kTiOrMQg2SCY9x6qRyLMQ==", "signatures": [{"sig": "MEYCIQCu5eb4ZhLiq22PB9UZZFZrArd7Lbg5Tu+a2qDVcqar9wIhAP0KMPci/fGFF4kzUDCGyfaL95ykYuzRklSMT0AKynpZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.13": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.13", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-presence": "1.1.1-rc.13", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "363042a3c3718c26edd3ca86730ff40aba1b9c13", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-//xt2SKcVereL65FMTEzsyD5bJ93EYd09wns7ZTMwodVfMd3r9gUA6yHbStJdMR91r2a0vTCH4CD0q9UDPjcOg==", "signatures": [{"sig": "MEYCIQChh4f+W+plEewvffTu2exEDtcFdlGtZBocSX2JZ3AwUwIhALg95Y2wzSswUCOOuR5HoTa5dCG2ZwxUlYZZtqbElacc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.14": {"name": "@radix-ui/react-checkbox", "version": "1.1.2-rc.14", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-presence": "1.1.1-rc.14", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ee05262c5250bb73a2c2d824be829d021f9e8790", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-YBK+UlTUaXNALMoFLibg3LBPYLkfuKdnoEidS8PYX1w+TJF+q9GVHWfFf9sYYA+Vw2fO2Vx6mU1DGwA9FvAmyg==", "signatures": [{"sig": "MEQCIHgBuI3GK4RJhwEHqSqBVQonX8k2QOe5l6i21whI/8yGAiAxXlLUOuQwRfc+hdxKNHso/RhF1Tomntr26QN9Zse6ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.2", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6465b800420923ecc39cbeaa8f357b5f09dbfd52", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-/i0fl686zaJbDQLNKrkCbMyDm6FQMt4jg323k7HuqitoANm9sE23Ql8yOK3Wusk34HSLKDChhMux05FnP6KUkw==", "signatures": [{"sig": "MEUCIDN4MbviY+gaPnLE3JliiMVlBVghEK8z3jJsiS6rgiSVAiEAu84/EbR1JdG4A+AiUsDaLf3CdV2/5QIRDm3MKmphWuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43562}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a274dcac13ac66890e166dc39690f7dc202955e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-12KW89FMswlj4ZCaEtEWkeppc0OrWyWnWpbo6VvIphz5sa///2n8PBvKfFDcGUUPamfhBhC/aVOZsTHGxoDy+Q==", "signatures": [{"sig": "MEUCIQCOmA4t8Ml0paZ8VBlEpJmlFrBxQDfxQxM+TT6vrAsq9wIgEcEZgX9jTqzNjnDaYJwBxSrCs8ktMRIpUvQbetVCDLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81d06b8b5fa28cac743d2cbc56c9bebc9aef8fac", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-9E8zjf0FJ3oW5Vx4/X9Qpa5c5WsgJ/hWJ6Ht62k5T26XWRVDKCUPla4Fl++b60lwAY/3JEPfCo7ejgFpbragTA==", "signatures": [{"sig": "MEUCIQDYk/ePDbyPqEPHzuvrpTU265J2uUNTNQRIyor0FllGUAIgJD1z7u06Od+I/nolhrBO6eOvSE0/lw53+2B+jo+E7V0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.3", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d45d0bf2566984d04d848d976e87b2a12657d76e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Cs7VhBCmDO3maeghV5SMWuXeLGWsfxIck5oUJRt8sFIa2j7G5Rk1GooDCq4gJC2Gv3UWwfIcwr+tijDJ7CUYNw==", "signatures": [{"sig": "MEUCIHS2bFYo+FAvhdb95nqZNVgUXgjEeluM6yJ1yCVuMuseAiEA06Qmy2jQczZ09HfJEUv3MIJ6TAPAk9y64XhhDK4dX28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0e2ab913fddf3c88603625f7a9457d73882c8a32", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-HD7/ocp8f1B3e6OHygH0n7ZKjONkhciy1Nh0yuBgObqThc3oyx+vuMfFHKAknXRHHWVE9XvXStxJFyjUmB8PIw==", "signatures": [{"sig": "MEYCIQCabjYb/YH4jPXKOwiJHf8ie3wtQwJ3ipUcwIWKWyZnygIhAJgqhVqCiQJ9mbgxhPngKZ+2HPsqE6KFAOu11kzBpaas", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43292}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-checkbox", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "62bcda40dc34fc27d7aabde9802b80d908099752", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-KiH9fnwNRpAIFt0jQB4zSvk5BzdAXqVCz63W+UjakC7uZsHrcf18UATlZOLC4U1FKXL3Adck8WbuQ/H9rJBLJw==", "signatures": [{"sig": "MEUCIDWvvORkdt0UIbCGozR8CO0XRwgjGwr0X9qeL07af0usAiEArcBNHe6uaeSVCw7dM5pDkXwGjU1dg9o76gavxRWlmaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43325}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-checkbox", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fd68cd3ddf4ff86003f03ade65cd0f82f021b6ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-YAI69SjfvRZIUA2xeP7XORKb83BHeQCSlnkMKcDZfZ5q7lMAe4Mw3adwyAhLOyxMuI6bb8RKKnz9D7M4shyCHQ==", "signatures": [{"sig": "MEUCIQDlYsLOiBr/oP03xRAKok+BC9mdRCly4JqmCT2PtM35DgIgZFKT9K3zlbIZw+gsxTaXxNG+3P+VXck9HGBcUMvtEeo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43325}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-checkbox", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "96019d0935b109ca13298f17fc778d55b2c713b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-0AF8O1WlpCVbuKzq8kz84jTrqUGN/sTmcISK4tMfFnwXgBlPnJ9wVkJreAhYxX28WJexquLKlJx4SiiIGv8QxQ==", "signatures": [{"sig": "MEQCIGWT/4890ti9kgZqjEwur3CgT43Bd3ZToOFRJEg+vUJKAiBZlWFkLPRITwOeZkEiNgU9ptg+xkmYbRDVTuWlhpaLvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43441}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-checkbox", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4fd0dea8e4d53d0e7d2ff6df382f87a179b8afba", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-b7rvAIgSJ82ihBv9vROT6YHRFrTO79TzRIx8iISRK+udgNMN/+pEcKqHUcSHKwGm9ng/0ZyX/i7HvLPU01nM4Q==", "signatures": [{"sig": "MEQCIHNyOK+QxAipoE7ZtD2K7BpElQuILUZFySN0Nm6AYUjyAiAf4MOfun1QChKgqz5eO0EBCUvDMH+0jnNvz1UT3uQgBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43441}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2bda5b236bdd3565013064accd062c3ee6b95e2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WyuHA2UHEjw61q0U4H/j+zlTgJ1k9f1ecQdBWsYL9dOtwyb8B2fdz6Wb0fU6YC0xJVfZBWNE0EnWlEjmytxnew==", "signatures": [{"sig": "MEUCIQDas0GY11+gYwIZqLzAVJiYb6imFW4sggUX2uXH4iQZngIgHHXV9CokWjMkWeyS/e64etesyu2ji6TCPlael6YJnp0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43543}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d9bd81c8da7bdb5ae3a49bac7a3f0fb23312399f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-RIAmjKhCiJkfkkL+dzqqHuaZf33MSt/AgwhR3wJGA/Z/DWdDpkkBnXtmR44IUEm96lCSSMz8k6i4fzsQGc2mkQ==", "signatures": [{"sig": "MEUCIGtKS7mrk6FJSVh2bYimfnjgd5mA/EH9EeEL8i+4N5QIAiEA9CWs/7+3DbDEQk9gf0OaSr1BUUdwfz7FIWbkvLZEDHg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43543}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c0c863210a337886008dc68f628aeed296fee08", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-DTbC+FIeEjeYfdGvWfEUxQvo+Kc7FH4J5HEI5JhwDO3lcY+Cnpe0HL58AdomqwpUbY2JQNgpS7AsZ5sHUKMfgg==", "signatures": [{"sig": "MEQCICR9OJ0EtmnxuEHO7Ox7WfsmHmoe9zDHI++LIUL+S1tdAiBNqZ+5PMUPpA/S9ni4aYEkUopHCC5hMSGju1gkQTBQGw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b39a4a6d93cb960cbb6b1501b57429848264acc", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-/3DPFRnwI9UUD9nIZKuQisfmU9T9nMsUobK5maAyv3OW79BC6sR3+qqF/GqZxNBkHsyyZ1kPrE4EO1E6pcb2dQ==", "signatures": [{"sig": "MEUCIQCKdpDPPiIFue3yailA4RBBBkOhd7v66CZXaywvT00NKgIgFRPie8PTYtrvSqRqJF1LJdPZV24Qu24itYPwaHTJHxE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-checkbox", "version": "1.1.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d7f5cb0a82ca6bb4eb717b74e9b2b0cc73ecf7a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-wP0CPAHq+P5I4INKe3hJrIa1WoNqqrejzW+zoU0rOvo1b9gDEJJFl2rYfO1PYJUQCc2H1WZxIJmyv9BS8i5fLw==", "signatures": [{"sig": "MEQCICZczKk7Bx4JoQEg8yFXqDfV9Xq656ysrGC/mMEi+/VRAiA0zH+lZTTEjs5HDcJsenLKjZoeb13Jzb2ehJNk9bO98g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43609}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.3-rc.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b3081f290c9e482f8d6253280b0721bdccf54384", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-p9/qH+vG72AF3EDnf11fOdCPLVhPw20A1iWSHQmKMfI+JDTGrMf1s1O3POi97oYxf97pWO5RT2BzyY0IcOMxoQ==", "signatures": [{"sig": "MEQCIAFg8jp/c5/5FUFz/oaOcwORiGr8XCDWSQbFI6qD+UCoAiAnUugbIxiQQBPIs7M0iN3LXNepo6phIg6/8HS4Z8+g2A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.2": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-presence": "1.1.3-rc.2", "@radix-ui/react-use-size": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-previous": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a0155e4d5c01c1dcdc16fce92eae9a854d03fb19", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-IpCkA3tBtNs5Bg+sSX+QbqUPrha1DW+ujfjI/qud3lZUJEaJFIY+ZkBlaaiq4b497jv3VdTo9ngWq0lmk2d39w==", "signatures": [{"sig": "MEUCIHcD+6w1gGj+bMXgHsA9YJiIbccEvWNwCVaq4LcViesrAiEAoRdhLsRjh4WAxBtvjaOInfCg1/rzq7+HF+mCbMUN3nc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.3": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-presence": "1.1.3-rc.3", "@radix-ui/react-use-size": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-previous": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2ae6c2d5c2f073c5483e17b6e3cc3a843898b874", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-bstmJ9T5RmKbMqseEf1z572zrmfqa8JGj+71eMp9qbedcts4hhgFEZPQjifdnZ5bpA/1qr5q51zOF3fBf1FCWQ==", "signatures": [{"sig": "MEYCIQC8XGVenWF9qhlCIR6H7iDdwB//omtRP5DZQhusBN3zWAIhAIrbDyCK1CIn/eEB+heqLcdlow1e2tlZIEgwQbAeDhLS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.4": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-presence": "1.1.3-rc.4", "@radix-ui/react-use-size": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-previous": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "91f8164b6d6515fe387df9cdc5a60489674001b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-cy7BKlMJzNTVKVT+nqxhuKWsqa58AsSu7H0JkOs5qMaCb2006dsg/8+qo4gs+a6Xv6pFeyyX0WKpnH+OSzip+g==", "signatures": [{"sig": "MEUCIQDMC2e/F8M5b88PxTBGqR8ZDGFB5xLD14meXDnkPlAMcwIgEHLFqrSLuKMPkKbU3dVHtdDgzdbrr+HC9kDCVRgF0Nw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.5": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-presence": "1.1.3-rc.5", "@radix-ui/react-use-size": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-previous": "1.1.1-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94947a1030f1735bd5931dbafa2946818e482831", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-MdGkct2AsiCH2/UJTaZN3xlU5INRyXT8EgcaRln1bngOM+lU6yI9nQVjFL9aqlTQJRkZaVCqBde+C+CzK2Flsg==", "signatures": [{"sig": "MEQCIA2fdKja+JdAjNej5AYd65V4miLxyv+LMB/YIuD9fX+SAiB1+/GM9Rnmtse+Ti1TiO7xMoJoXPy7tk8sOsT8sqT48w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.6": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-presence": "1.1.3-rc.6", "@radix-ui/react-use-size": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-previous": "1.1.1-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b3311c19df7ff5b0675ab50bffc4a9c43af19e45", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-SX1ns/c9o9J4Nz42X+Nar6w1GRh7ZGdJ7uvhxV97IEfL21HEC3XGfEk65g+fi0LlKDlPzHC5FhfPuusNjieapw==", "signatures": [{"sig": "MEYCIQDwOIbr53iD/ICnJrDNM275D+Q1m4TzZTAIspXzE8B1fAIhAIy9RT+DegLFC19u0vALuya9thaNfUYGWdMl17G2rSyg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.7": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-presence": "1.1.3-rc.7", "@radix-ui/react-use-size": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-previous": "1.1.1-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "87b7a9aa9a5416c43cfc184b99c45be2fafa7df7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-xeG+zItDTTC4CboO4wVyJg8sM2chsNLJP0b+iFuDdAkUHzpaCA0dzC8ZLL050ORc+KhD4oIUIM8OaTG+c8B6pQ==", "signatures": [{"sig": "MEQCIDiSViAqgHpm7Arg4IEEcifarsAYQghr7h+LRGeZB626AiAocm28zjFxmT7XuepbY026vBHE7RMwR0VfVymADANrpg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.8": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-presence": "1.1.3-rc.8", "@radix-ui/react-use-size": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-previous": "1.1.1-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7dffc5e14349babaff2f7ea031ae0f54cbecedeb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-QWJ6q/h+E85Z1pAseAaSZs+NSa+gOBiNxT7RgcehosPL5ZAkjGEVK+ZHXWACpjdsbT5NyzXsAR8/Q95ZBAUE/Q==", "signatures": [{"sig": "MEQCIDr71L8muXRzFTRFyX6IQ5lDUU3uUbxwgJIdla4mRatQAiAQ5Yf9uhsuAQKhCn+OsK86zErtzcc4sl9/TGFJXpHUBg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43688}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.9": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-presence": "1.1.3-rc.9", "@radix-ui/react-use-size": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-previous": "1.1.1-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cf564888cb8e2505086b4c6965a17a8a3c874d23", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-ZPguPnDhmexd2PO94VhPc1Zxisk8g0HxQMavQQjwzIDZazl+WWKpohb7lb02OOK455Y5BpFb9ujCN6FMIBUHnA==", "signatures": [{"sig": "MEUCIQC9IbjTw3RXaTlJbiGpT21Xteupyy9hrW4BhemZx3oBlgIgHuwbJ9PpQgJU4y+HIhxjtv4pZfO2c0bxJqiKbpVcI5Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44079}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.10": {"name": "@radix-ui/react-checkbox", "version": "1.1.5-rc.10", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-presence": "1.1.3-rc.10", "@radix-ui/react-use-size": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-previous": "1.1.1-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43b2e941145f157c06c93d5aaa7a378a43fa96b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-eXuJoW1ryolQY2XEbvnjkUz/1FPjQ00lF/Q7qseacLHK7IFr6GwwU6MlWPS+SR20gJ1+vkV9/rgQN9XsEYiELg==", "signatures": [{"sig": "MEQCIA6TCsPwnEBX6kq6ihcuXxQnHfPVgwnzxjQVMLn1l4LpAiB7q3z6fEs3S6UTOXje7Hxp/xCBzJ/K+NgQufu/oEI+SQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44081}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-checkbox", "version": "1.1.5", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b27080678d751e33ac66066269453be9bdaa429b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.5.tgz", "fileCount": 8, "integrity": "sha512-B0gYIVxl77KYDR25AY9EGe/G//ef85RVBIxQvK+m5pxAC7XihAc/8leMHhDvjvhDu02SBSb6BuytlWr/G7F3+g==", "signatures": [{"sig": "MEUCIBdQJea6Gje4EfekNwiqo1jCe6AisL1DJ/YuqWUgy7hAAiEA1E3UaL9Aqr/ZsTNZ3a16ra2SHG0jWjG0ODpr14bOe8o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44006}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744259191780": {"name": "@radix-ui/react-checkbox", "version": "1.1.6-rc.1744259191780", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "01f3439bb4d6d234ed630e94905a15072b0427ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.6-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-IpajJ9EzXmHz+oB5IEANjtFiR1kj5vLmEpTAYiP8P75x0DCRwX/fVbZ6w7XnDxPdt8+yCRiiCt/xhO0ItzE3rg==", "signatures": [{"sig": "MEYCIQCKOMqnamI30p5a4SqRmtZa0URcW2t2ufjX2r54NqCtbQIhAInFrQkTO0GJ8P4ZOyn1NWmIlNHdkNTAYPeeuKsoh4Lf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44740}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744259481941": {"name": "@radix-ui/react-checkbox", "version": "1.1.6-rc.1744259481941", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cd535aaaf227511414ab8e4c277b46cd45cea7bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.1.6-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-pY1k7LQhd6sT72X8y6g71mExIzoWb9P4c6Bcj0Gk/PbnCh0oZLurDgBhLGUvRouGatocpaCiGHGpz9VimzPLdw==", "signatures": [{"sig": "MEQCICGjD8SLIiEtaJ7bU82f/rSKtsChStMQdVtcS63XakmfAiArOfS7W9iPETfNEQzwK1OnxOQo7JMcHT4EPzBhFGzWzQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44740}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744311029001": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744311029001", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "347013cf83072535c5ed6bcd973e3883f7f5c107", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-3W9/9Vi1xucUX4tvLnGwhfTS0I9Ai48YG+64uNppMY4EbPJBAUPjuteq3EdA72XgptD5pepr+krnV0GXNx7Kaw==", "signatures": [{"sig": "MEQCICR6H8Ur/TRu7zIG9CEfkcBC5xnE2BEr3arMoX8TNk4VAiAJMcnJhNOha7n+BX91q/STEROuXg+4rpwMmYsi34woLw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744416976900": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744416976900", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f265a58bbb0bfacd2fd8efba11861e8cada63983", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-lP4LgJDpS71TSmnyhNevkqnr832cCGnucr96BU6Aeu0xuF64WbcBhcl9iqDSLk+K5A8jLBxdL9S4dVBAy0NWtw==", "signatures": [{"sig": "MEUCIQDY+6uj2ubOxV69I1o0XMhlEU6zuBR4W5e45ByR3N9+KAIgG4Mcm7FaJWoULQqhPr+LX2JjKMRL38qcz+5w+VIEkE8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744502104733": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744502104733", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "889492f4a31f19cc0cf796d769e6a71313688165", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-R8Hvgb8zQCEcVLCCVym29L1P0yGO7nfTP7gvH5G/XLZaqINMo8BaaYcvsHatzRc+Qwo1Hrw1EaGr/HrKM7PRHg==", "signatures": [{"sig": "MEUCIQCze2+yfrVfINgLrpcq2g4O89KsPmWRac7DvlVp8lkcCgIga80BHLLLMkBOOmMVLcOT0Z+6WiCSCFRoI8ePciTb2pI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744518250005": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744518250005", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5dd149a5850124e79801bf0067a43e607a00c0da", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-u8pfQP4ItQ9a7C1EytFOkF2MOEsqqUF0xvZNdvrHDHmMo8T20Cgn1Z2aGR9U1Q6x6bl3ciXcikhSe4OMKu+9xA==", "signatures": [{"sig": "MEUCIF0nSzyYqMrEZOOvCw76FdBk+5kDgphXrXUexg1JX9DGAiEA7z3ZNVdd/dUOutucQbCN+jrzALC1gd+Ag+l7j4aWve8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744519235198": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744519235198", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f7bb7146ea4ef4c92301ead3be7ad18b70c8faa6", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-2LlLCg5vejixJYDYckMgRu7h4PORI0NKmv1V9HFC8C5bP9rkcwO1iA95Lz/jROcoyIDaKCRenVOUmF4jvPjGog==", "signatures": [{"sig": "MEUCIQDlqEpkvvj41S9LzYZq5Q2+WP7I6itFCb5i68CbkBN9SQIgCwIO4uURr5KM7YUSMYF8XCvnNtjYoFpIS/nO0a8o4xI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744574857111": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744574857111", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f99d651e6c96850eb64eec20155f1f02e77df879", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-5N+0fbusGCuuvMnQF+TAzR2kuqMsVJe3MQFG/26oiNojyIPWaHpJ7yX30yVFNvXdYAFk2+xx7Dibapq4USET0g==", "signatures": [{"sig": "MEQCIGevhb5CJLFepsDIMHqtPU8D0Uldfrsx3djcld7trmjcAiA/tRr0JnKu2PeKAOaHEqYLLadl/0XRN24nDCVmsSrjLg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744660991666": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744660991666", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f87624139c8d004ac23a367aae39a059ed5f7c1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-iqqjrbJ32KcmuW21cIar8P5QQONU+pGZmOVLjpG7HpV95c22gaOUJEhh+O5/7fE+AkxFHNDndGyWLl8SpJvQ/A==", "signatures": [{"sig": "MEUCIF+mcabae4xKDg0Hz+JymvrwwIsa91UXzeg1PuOpZs46AiEA6PKl9OI2mqKBvo4noNX/SNiF3ma94QbrU9anX/iavw4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44599}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744661316162": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744661316162", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec4e789569a300d07ef5aab8807118d72d269516", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-jKSkUaOx7zIsvqzoea1dEDZ0+MZVrENVjCe9Fp7x9aUtFLjgXPzjQI0hbT6xXUw09imOdEV1Syu1SUOEHShpSA==", "signatures": [{"sig": "MEYCIQDb27NJ19plo9jdtV374la7WxZ1kVbB6E1F2NMP81v+LwIhAIQr1vXbYo3pXzgJBzUyx9ylP+Lj6Ko/5MtA2RVCG4Re", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744830756566": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744830756566", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ddc8ab66b230ef1f24cb2da1957cb76b5984111", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-clCbhDTdTpcVnnPfG6M6y7rDDhnaKdgt1/KIx9fQ9L+NxztQR0oVy2VgMzQutr25D4K060QupyjgUuB2GYEOcQ==", "signatures": [{"sig": "MEUCIBVavc+CsFNJMaDNFrjxlQg51yeZZkzszY6PyGIlQNd4AiEAhYHvPKJuoonHQtyj3R9vPloJFg7ZlS9syAGEV9vXydA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744831331200": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744831331200", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "abed16443de4ca2536e0764cc0bdbca9538aa9d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-HiHFpQQhjPR4VgCX25QXnGHFg/ZG1XdxHRRU0f4lWGTIxITBkw5d9juUu8gs/ku/T7IbtiE3KdRfWsxYJcfcPQ==", "signatures": [{"sig": "MEQCIEqnmnLWZHmtYKQSS70KOtuHU0WojyBOS/Kb+UDRizxOAiAjQ9ixrSvv6y+i1jz9xEDr9dNYt2lIG3EUVh1fryF9ng==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744836032308": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744836032308", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5552dee56dc43cfc9ae7520f6ed87ee8e99c5554", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-VRbbbB3pn7y9ySFiqVTy3pbXFXgZ8NGHq8sfSclzvUu2s9LBCmz4yo5dewyqV1sfgXxbSENvhU8K/oYTHQooXg==", "signatures": [{"sig": "MEQCIBKLrgnpDlABt3RwYKunRtvVvmq5kafyRAtW1jT/u/o1AiARWaPcvWV63jYqAp7ha4oBfhvm/lQXDRdubbHECbniKw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744897529216": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744897529216", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "93660b67d059e267e095845349210bea61a7dfd0", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-pWynRtpgsF4lK8mgnRSCiJowHCb3468+FSE03I6aobVqYJ7mT3P2TxihWKeAQPVmFqoqsZspDdmfRLMVxzogMg==", "signatures": [{"sig": "MEUCIQD/NPinFvRCJcCTZ+YRqjF6FU5eWkU4iO75fAnbr9MsbQIgNrbcHK/6+jGEBk5HCXij7aeBs1jlOAoA60ILTQ26GXU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744898528774": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744898528774", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b102c607c2fd77ebcdbef4312fa15b2fa2ec01c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-vAXdj/6xB6s9ySlyBHrqEVt8ABq44+XVNh9/QVemgba3uQwaSUWkvQKpDVPycN66s36cgRSZLOKCAacJvZSxsA==", "signatures": [{"sig": "MEQCIHzpHRdzrrFzfrr/Wpgv93UfWx4L1MzezHm+9zkUpfC0AiBpVnD+CzMVvRfv8Ou1DtzIHDxTRBVtYPkYlGSGA3VoRA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744905634543": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744905634543", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6748ef817e438d86c1ed291ca64f198364fcbb43", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-lJ5Rzzrcm88rZTe5z1nCOFhD/xVKDfj28Rq1GBs+NsDAu8y0rBnDb+Gw8AtfGKO+Fnb94bTIfUPqUfCACY88gw==", "signatures": [{"sig": "MEQCIAy3Oc/1FCXkBU1NATmxAiZlDtKVJCUWnecFiBoPpy/XAiAmeBwWUjfaRdTt0jbe4D17SjGEcDGyKCaFXf5kd8oEIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744910682821": {"name": "@radix-ui/react-checkbox", "version": "1.2.0-rc.1744910682821", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8b5ff9890f3272f4115624b85802278174a7f948", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-sOxN6wZH+oOa6F0piOOaaDd9MwLQ2qQOQDnkBrhZkvOsfhJ+9B9zjUzQWKQSOAZ2FUkv2EAvOPb4zykGhkgMxw==", "signatures": [{"sig": "MEUCIHk32ogD1JNF/X1mIibIXH1f8XwkKOrzpdIyS7xNx8ULAiEAtCpp8zf+swAh8xfGZeo1KEhj5JKB5yZCDdgX6CvVhZo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47345}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-checkbox", "version": "1.2.0", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2a3ff21f9b19257b475ed7fe780323c462812a33", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-JnWjmkmoPVX7ez30+TbRSL9aM06CsfDx8G1OeN9OPBhTguAUU2NQ89RP+N02Rpx0mdCWfr0ki+P/AtjVkz6jpg==", "signatures": [{"sig": "MEUCIQCNyxjmsACODrRfHCd8DXD6w0sroxnoZfsF8QkJtJR0LwIgX5SzYLzYPPGR3xRtfylP5yCFF36UGJvxC3e0VFIL+UQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47294}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744998730501": {"name": "@radix-ui/react-checkbox", "version": "1.2.1-rc.1744998730501", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ba12808abf6fcf7c00c0f88e53fad77b1e2e48e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.1-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-s2HyU7kq7hJTkPrJUe5+yWaErMkB1w2HXlsHCyBStWCbBDtwOZbBYk4hdmDiYNc8UFnPl6mec3V6ZIww6695mA==", "signatures": [{"sig": "MEUCIFG+h/YqTXkQMQPF3GKMCZ30WGv+cHUCA4Y0koICVqUkAiEAjKbV0yiF9tNSnz92zLlIiQl/ZdXfc+mQd2cmHcrq1A0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744998943107": {"name": "@radix-ui/react-checkbox", "version": "1.2.1-rc.1744998943107", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "56556c1087b424b654826f3139c3870e52c0f293", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.1-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-uicJinmViPg8Eu1KCpckPS6rb0cHiDrc9/QekADYvnRvLFW8fTo+mePT9+CclgcaGhQ0NmvbLIIVFMzVussRpw==", "signatures": [{"sig": "MEYCIQDatb/h+ZmN0ldlKulxxZRpC/sVg4xoZXH5p28xQjArRQIhAKiCYiAOB6WKgvfADx8Kt1t9ivPbq41ExLluXitpdAjg", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744999865452": {"name": "@radix-ui/react-checkbox", "version": "1.2.1-rc.1744999865452", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f94110da1ab02fdaa804b2ae4f523f7e9c1b0787", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.1-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-4pk4Ow4Wz0BP9Xb7cP9Y1mpS2BZT/ABs5sC9fHs15kZeKNeqZ/N7bsohIjlIo8KM0BUxwJVeUuDtfDSD8811iw==", "signatures": [{"sig": "MEUCIEGyjmXhCjmXPNntGBS5VzJZlL1a+ltY281JKT1wUYmGAiEA0UWKbGjqQglqvBgywI79vyTSmGElqCgHZKnXYjhimCk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-checkbox", "version": "1.2.1", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9fa61508f9147d4e7e9c23cab6216317334bdeca", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-6a+qvM6rNAvVJ82z10nQquthwu+gcjwX28vmIh1eshhBf5lFHw+XU87nh4K6AzbL5tS2IH7h4H0+CIUlVuIoog==", "signatures": [{"sig": "MEUCIQD0hpoLWHLTaUseum6KGeQmuK2+qZpMsSVgR45cT3VIuwIgPFUjw4XsRp5VoXCwwQSzbyBYR9NcQxEPlf9Ilul3E2Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47294}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1745001912396": {"name": "@radix-ui/react-checkbox", "version": "1.2.2-rc.1745001912396", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c66164fa94b606d55fc25d279e0baafb4632f1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.2-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-zGt+UasCQi9dRNdEf/2ItHSFUA5PYPGZ0F4hJTSBeL8Z0I5kFPcGg+NsR4YvuuNF9rChOtAhCXlDpq8XYmGoAw==", "signatures": [{"sig": "MEUCIQCPSMCrpxRKTwKKfwI0lSe8x1mvM/paYFUAeuag+yFwrgIgb3WMM9y1fLuvgf34ctQeaPZ6xcVoEOmc3pPkPa2RFd8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1745002236885": {"name": "@radix-ui/react-checkbox", "version": "1.2.2-rc.1745002236885", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fea9de6157aef367241611b84dffb98fac20c218", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.2-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-88kKtouqNXs59LKHDcwQ108W/Q9gQ8oAO0GO5e9r5/7F8cR4dHOVuSMM5qKWbaUJo3L0Sd2buG11mzSBZOBukQ==", "signatures": [{"sig": "MEQCICi1P+cZRAeCO2abWu3hUHCA5Sc4P44P9Q9eqJkpW241AiA5SN/0ZjYTBf6tElwFdYdHaUqbxMJ9Z6WUguOWqiwU/A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-checkbox", "version": "1.2.2", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "18cf40fffa6021fa57336da65cba9bef08bf4284", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.2.tgz", "fileCount": 9, "integrity": "sha512-pMxzQLK+m/tkDRXJg7VUjRx6ozsBdzNLOV4vexfVBU57qT2Gvf4cw2gKKhOohJxjadQ+WcUXCKosTIxcZzi03A==", "signatures": [{"sig": "MEUCIQCFZT9bOIPUwhXCpVezsNsn1x4pe7sbuGG9OYT1UtJbCgIgbiQgLzv+nuqMUB67fPBAEpXtIQyfNk8LfzAXGhxCePk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47294}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1745097595920": {"name": "@radix-ui/react-checkbox", "version": "1.2.3-rc.1745097595920", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745097595920", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "93cda5ba4ce835cd1a3bc627600d97d7d3f19dc9", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.3-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-Pp78YTpqtyPH8wGGF0sc6eDo9w9IQCVvP+fiGyZgp9RDeWzeplxscv4tY8rs45EnU3o4q7/T2xIJjKXGF83EeA==", "signatures": [{"sig": "MEUCIQCaAjDyGs1bhrYDhpTcAp6WywuV3ZldZrQQRtsLbETDegIgV8ws8M47+apePfHRW7UDOj+bymPTE6foqO0xXsIjAD4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1745339201309": {"name": "@radix-ui/react-checkbox", "version": "1.2.3-rc.1745339201309", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745339201309", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0adce6da5169c475c2af556e51bfb1b90b0957e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.3-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-CFm9Qj8IYgtRGSungdPFpU6zd1A+wIkXWlO7Ha5aXKZDrgBKRJKxP0V7yEiOJ7ozH7MBBR//JQX1/+FVQciUOQ==", "signatures": [{"sig": "MEUCIQC8ePV7LZ4YHsoAmGxg0jg5yL8SaUF4PnNQotPCymAOsQIgePkEeA2GCLp9PZv9ys65WUROAPP1gL79875Vw+20rDU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-checkbox", "version": "1.2.3", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "808a4116f7ee6236b99cd62d6d7dc03b9cc9a04e", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.3.tgz", "fileCount": 9, "integrity": "sha512-pHVzDYsnaDmBlAuwim45y3soIN8H4R7KbkSVirGhXO+R/kO2OLCe0eucUEbddaTcdMHHdzcIGHtZSMSQlA+apw==", "signatures": [{"sig": "MEUCIQDwJ2JIOwNKF6gjPN+WY3OvoNgw2psu3e/M7/P4wXERNwIgNd12ypXLFU69luhg0jfcQHSDsf7VoUEXxQxU+8TGITc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47294}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1745345395380": {"name": "@radix-ui/react-checkbox", "version": "1.2.4-rc.1745345395380", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b95c882374e6b4b7e743e8cf4769a15e7b25d51a", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.4-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-ONmNf7q60KQhQNI7sHouLsUWCwfGo8FD9eCnkZOUuiGnBrE7L7/k49IHoUMuwFk/CHQIT13AHw1w5fWczUjZPw==", "signatures": [{"sig": "MEQCIC2p9X6tRHttlFm0JqDcJIlrTPtNkWG2CbemSZ7csQcQAiBmI2rqS9yHcEPJWVpDnSnjqXzEOlfWz8j7Llh74+XusQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1745439717073": {"name": "@radix-ui/react-checkbox", "version": "1.2.4-rc.1745439717073", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6a1cb7296e6585436b95492b3adea72fc490adaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.2.4-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-RMQXZkI94KsV/Wq+BQMt2WkNLtSfr2jKs8n5+4/dpY8tQ53CRum/l5AlhQyuNW2tmsy7HiIC4tCOaJtqPXLBRQ==", "signatures": [{"sig": "MEYCIQCB3O8RWMf0bThIdLo1vU7g/BQUyjx5W50l/TnfBNubGwIhAMod4Tfi/tF5sO5K6LaWWiZEmLY0w+t733Lr/y3cJMyW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 47328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1745972185559": {"name": "@radix-ui/react-checkbox", "version": "1.3.0-rc.1745972185559", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9fad74ac7163c9af846059925eba2016093f5046", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-eUGB7rytP1loH1TmOVUUQPUULExljR2SIIzd72O4iHnH4FnH31ciK5yRnb6tlMsEranZqXtd9k3E+h9uFOWn3A==", "signatures": [{"sig": "MEYCIQC6G3UBlvujv6qR9wMdDz7ZT3jW2tOpC6yITQrtkboHEQIhAPsZuSKrz0Fp07QeihApy5CFwuRuuxBqkb+/n+g7FXMl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 67597}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1746044551800": {"name": "@radix-ui/react-checkbox", "version": "1.3.0-rc.1746044551800", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b36abfa690f740d0c85ba215e5acf64fe1162b79", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-V1mpvGkoBFJpP0upUq7+2ESpa9R2OK9nP0arULtxqnAmHNlqImuAsovBe5oqPs9TOY7ZXGdzGkdjOOQ2LAG4Ow==", "signatures": [{"sig": "MEQCIFDjmhQl6sufgRcRFOf4ia993+OjkwoUgCT4FtlGhkJxAiBZN8ywFDI1a8SZ2MNkKh5etaJ79ENdDvxyBPOc7BrSKQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66120}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1746053194630": {"name": "@radix-ui/react-checkbox", "version": "1.3.0-rc.1746053194630", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "537453e4dcfb330d392b09b034471fd810a75900", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-3Volfx1JAWRVGa0bgyJzNchDin3LQ1+7gIIOVa87dX3iahY1fKPo3687ly9VZk2acVNJ1OnlGZo7yVI/cakU+A==", "signatures": [{"sig": "MEUCIF9BLTrq/bXlzvA6vdKPR45xARE1VNDlEIA1O1aFbx/YAiEAkW4J35VTmZb0VKO2l8jhyqBPe2PS2+rayI2JH2o6yps=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66120}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1746075822931": {"name": "@radix-ui/react-checkbox", "version": "1.3.0-rc.1746075822931", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "15f7964ce781f89c275544523da2c49d796188e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-XHonom86KHxTGuimv+EbpEkAclNH5+MGGtKCt5VOxXu3qx4+qShdE3MZx2F4gWougKS9m2uUpRejshWdza1PaQ==", "signatures": [{"sig": "MEYCIQDFqju8QsPwSqvhrEVjYxPmtTS03ZLhr82XYgoiLccocwIhAL4+M3xNIZ2veE0uX2tG/jooVr34GX2ZCXERxE3bY0LO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66120}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1746466567086": {"name": "@radix-ui/react-checkbox", "version": "1.3.0-rc.1746466567086", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d24c691c6cfb9df17b5b6f872f12c35f87814eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-BBjLQa00qLPA9uQg7QnJbn+wI73jQkQsS1ryFxzZolt46/0szBLNTlb/o7lm8rKPbTrbSIuXi+7+ThwqimTNjA==", "signatures": [{"sig": "MEUCIQD4XF+Xd7UMAnom1pjmWo9GU7naHpyRfLfx0/VezRHV+wIgI7nRCoLhYJO5PVy8wgQvni8rEIhxMXeE3mGmf9hj1vk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66120}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0": {"name": "@radix-ui/react-checkbox", "version": "1.3.0", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88b35a60dfa311ce9dbeb7c2da085a215f75cc95", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.0.tgz", "fileCount": 9, "integrity": "sha512-DuyWZsuqp2w9pgibDHxL8EmDnFCwcGLuvA1nS05cyw7o43PwJPhZ2+k+LF/QO4aEbTHLjelUhQAvLFi74L5K1Q==", "signatures": [{"sig": "MEUCICUtzWmo5tHU/WQzwu0rQGi/ueoLjhrbpuXiqDnD+5RzAiEAuvcdmcKrx3MG6tZ/cpGD1XCrkG01OOf1nnmuDTag1/I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66992}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1-rc.1746489953427": {"name": "@radix-ui/react-checkbox", "version": "1.3.1-rc.1746489953427", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fbfc755f113e1596787ebb8f0ab1246b926a310f", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.1-rc.1746489953427.tgz", "fileCount": 9, "integrity": "sha512-TBIz2CeL24mc/WYyZJG/k5P5KCAHGgFXf2YnOrexFpWTydDZHPxkulkgcRBwymOul09HGmESO+GrpkPFOnfOyA==", "signatures": [{"sig": "MEUCIQC+yydjO9pE+yAEBsAISSmv+A9mLGRp3WV4Axd/rku1JQIgWZu/u5V0X5pwHJNByGKteRO6C46EQQotwlYip5BGeqk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66961}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1": {"name": "@radix-ui/react-checkbox", "version": "1.3.1", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c5c978ed49dcc8a81a8126bde9d547c7b928285b", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.1.tgz", "fileCount": 9, "integrity": "sha512-xTaLKAO+XXMPK/BpVTSaAAhlefmvMSACjIhK9mGsImvX2ljcTDm8VGR1CuS1uYcNdR5J+oiOhoJZc5un6bh3VQ==", "signatures": [{"sig": "MEQCIFq1AeMMgOXQFh8QuXLfHNrn19BUCvrvm9qViWWEwJ0SAiAI0wxH8wSpyV6mj90sLkBb1lJ0PaVAitihBynO56Lwfw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 66944}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.2-rc.1746560904918": {"name": "@radix-ui/react-checkbox", "version": "1.3.2-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-size": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-xM/kxi0MNraAVeCbUOmqyqrN2GymPSCtID3GXHapZdVc00ogVfvWJV7hjgdiXzXprxjtI1JNcfe1yMS+1Oqrbw==", "shasum": "9a58bb3102b05b33a4503b33159c90c9cafb75b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.2-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 66986, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBYdu2HbXqmh3EzBxSlapXzk80acC1Rg5naCZ7NphvEpAiAkU5wCM9w+i0EEVLtGZCa2+F30wEdCv/cRosmtlbfyvg=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:46.107Z", "cachedAt": 1747660587633}