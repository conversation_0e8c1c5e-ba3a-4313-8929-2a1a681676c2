{"name": "word-wrap", "dist-tags": {"latest": "1.2.5"}, "versions": {"0.1.0": {"name": "word-wrap", "version": "0.1.0", "devDependencies": {"verb": "~0.1.20"}, "dist": {"shasum": "a2a8612c24f638391716d38db01afad2a7918926", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.0.tgz", "integrity": "sha512-6Ee7CUc3EEsgjsvywkd0TL5XRWF39abAFHaLT/VeJWDoRFvGlzAX4QpBsnVHvkSiYm9och7Klb+b4F04wbhH2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAivPI1BhOh06sAq+HwKlBlyZjbowqeeyliqtQcsTAKAIgXwYlFJS6DIYRoWpNYZ48ki8Z0spq6DA8axTqIOfgE+w="}]}, "engines": {"node": ">=0.8"}}, "0.1.2": {"name": "word-wrap", "version": "0.1.2", "dependencies": {"longest": "~0.2.1"}, "devDependencies": {"verb": "~0.2.2"}, "dist": {"shasum": "e47fde96d14ad9ee427731c42b4decb778127b70", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.2.tgz", "integrity": "sha512-IwvtozHB9+xilbjWwb/0uDdER6Hiku6hQCGrgvojVWkboBkjdlLa58IDgNBcHyDrSZEfglogaBnFtDGAJUJNDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBy1T+YElva/jIbAIaJlYUXyxs1DZDNQT0GQvP9BzjfpAiAMGwpiumhJz3TZE2Jh5xxcdj2HMzYy1I3EMGTBI0vrCw=="}]}, "engines": {"node": ">=0.8"}}, "0.1.3": {"name": "word-wrap", "version": "0.1.3", "devDependencies": {"verb": "~0.2.2"}, "dist": {"shasum": "745523aa741b12bf23144d293795c6197b33eb1e", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.1.3.tgz", "integrity": "sha512-N10MyEPL9oFtdKFV/BcfFYZLrnPI3Rpw3NSh2N6xD6WezeeqLssdxXmttYrGmvPt5dTFrlORrujow8jnSimdcQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCOR2GzA59bhLXkOJ3hiYD08oknqYk80U4smuVjdNCLNQIgWECk/onRa3OY5aDpaV830Fx/UjlsF/il5LECtTwjUd0="}]}, "engines": {"node": ">=0.8"}}, "0.2.0": {"name": "word-wrap", "version": "0.2.0", "dist": {"shasum": "3c6673ca3d0b26d01dd5deb6e159746e1af5ce70", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.2.0.tgz", "integrity": "sha512-op/myU3yTuVz4OAJ7denLdVRegMRsGgXN86vEwD/izVkyblZ/L9qLa0kaLPgs/hritFX3mo3rQLW6En7b1M2cA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCulYYZWSAE++qC5OhqQztM4w4C+gfZDUg5JiLoHSQSfQIgMx4yBzCuXyR81aXlgQKx0S7qtJtLCLD/aBL4z8YkmRY="}]}, "engines": {"node": ">=0.8"}}, "0.3.0": {"name": "word-wrap", "version": "0.3.0", "devDependencies": {"should": "^4.3.0"}, "dist": {"shasum": "15c75db42fc55e67796ee59d42c8d625a40fb095", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.3.0.tgz", "integrity": "sha512-RT3BNcNy5pY8oqypfifsveNN/2p0apUvw/EqYSNOHAVcjCgbvOPh2ezUo64Rt/PmrRl9naSOUC5whPuQIzmgyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQChhhP2kiffZL2sf+7WW7zz6mEOPETPUm3cH2xHWzgrWAIgMABlMLbeub9/Nil1f/KHpBF0JzzLHAc+xmNP7Rspy1o="}]}, "engines": {"node": ">=0.8"}}, "0.3.1": {"name": "word-wrap", "version": "0.3.1", "devDependencies": {"should": "^4.3.0"}, "dist": {"shasum": "7c7b5296ed5a4a9cf234ae3d10bdc1d3a637f197", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-0.3.1.tgz", "integrity": "sha512-rp7vWtSqI8kG+KRceRK52ufV/EvyFslFrwiSrytAVUZoTRKnC1jBYNcmeK1v2jct6JPgpFXBRGnKx+a0Lmo34w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCnhSN8yeztVvOC4hglckfINxMm+5s5t0sRAJe38nJxMAIgHZwmLa2KOTpxAG8EoUIfPhBwIHP+v988fe6NbaVCxJY="}]}, "engines": {"node": ">=0.8"}}, "1.0.0": {"name": "word-wrap", "version": "1.0.0", "devDependencies": {"should": "^4.3.0"}, "dist": {"shasum": "e8ef895d990bad1d2e5431b274343834bd8ad07c", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.0.tgz", "integrity": "sha512-3HlGqnmiYVeEfffeYSrHC2CvivPwl7EVEsk/nI/ngHRKsrXHohUTjQo4azFoJoRpURQLxJDlScQxV16ONNRX+w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGDHzsRoobU5cvefa9/9+tzvCuAgLYk7Gb3UHnKG5CYwAiB9Mn9sxOStdIi+yetOZFpKezsYgkN5qcg+k5PfFRHB2A=="}]}, "engines": {"node": ">=0.8"}}, "1.0.1": {"name": "word-wrap", "version": "1.0.1", "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "dist": {"shasum": "674ad9cd8c941973a042ae79d601ec5fa2e4578a", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.1.tgz", "integrity": "sha512-XBTx1ISiFZEWLRvXIFH1EZ593Zrk12GwX4gE7s37Xk1Am9wuBmHcZMwYciuWgi+NRhHUPg147IBIQ544M/8Xkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICT7oYRFVByWRA96ITXQaYmQKxqOQ/zrKFP8md3uGVnEAiAF71i5gsdGNx8ZTACNRh49eT8HfRonKnWLbUI/rOstXw=="}]}, "engines": {"node": ">=0.8"}}, "1.0.2": {"name": "word-wrap", "version": "1.0.2", "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "dist": {"shasum": "23ef4cfdc5e62c1961aed99781bc4a803d4060db", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.2.tgz", "integrity": "sha512-z2F9ub8xd03t4yK51td/GNmPgh/vBlhguWwuYlWZqMgqjn8ItSE5g3IcmVp2t9j1Nq+NQlW6SbnVXTigdb37Bg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0Lov1DFTAW1p/WGGv0twPo0hIKZ8M3VWIwRqtN3O4BQIgZkAlMJzfrREN0ICHE1LtIIqK4SVZP3m7HaL7YfHrJPg="}]}, "engines": {"node": ">=0.8"}}, "1.0.3": {"name": "word-wrap", "version": "1.0.3", "devDependencies": {"mocha": "^2.1.0", "should": "^4.3.0"}, "dist": {"shasum": "84d423b8dde01daf478c16c13e163cb9c78ecfff", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.0.3.tgz", "integrity": "sha512-pYIwXGqZIPNUCL1u3kg75tBA9VAsWrACv8lourByaaAmGan3MmNlEr1MFxdDTDlL7E4ZNyMC15z2ZrdH02Ouug==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBexanR8P6RN6pijoUkBBxiVo5nKQmahxc9dRnw3oPI7AiEA3XWHA0THOfb+KsmwB1J0QMLcr7i506n+iv+hY2WVdtM="}]}, "engines": {"node": ">=0.8"}}, "1.1.0": {"name": "word-wrap", "version": "1.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "356153d61d10610d600785c5d701288e0ae764a6", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.1.0.tgz", "integrity": "sha512-6E0pg9o7nVagpx7xVMlK/gZriE9TLIyDGqPiooYdLCOiYFazQe09vIHRWXHYMupVHBgViR88vgx5OVGeo/HW7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFVJHTZqR3Q6qRyv3MjfEEvyXbF0ubVRDPr/EmCeFVk+AiASfQpT3I4f6FGVcSoFDg/WlktC4ztUHA9PAIMntKTpSw=="}]}, "engines": {"node": ">=0.8"}}, "1.2.0": {"name": "word-wrap", "version": "1.2.0", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"shasum": "ee971b6b7ce9ecae73a4b89a1cfdaa48dcf38ce7", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.0.tgz", "integrity": "sha512-+KLeGhwldY/lp9v+iA0ltNjQ1AP1la4ny5rmrTFQFOlPSnNgYfIuHQlAkCAVUyobIgaxygJM2QpA4N0NHsMgeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFOopdDrR0BK9Pg1Kxmi/osLJqlfwvFi88gc7RkqiZxeAiEAnJ6s8mMlYXVcuZz5+d2l5b5m/eq7tzA2FhDAwH8SkMk="}]}, "engines": {"node": ">=0.8"}}, "1.2.1": {"name": "word-wrap", "version": "1.2.1", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"shasum": "248f459b465d179a17bc407c854d3151d07e45d8", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.1.tgz", "integrity": "sha512-Da7Yu/UaUZ0K7lWKtPdJZ/Zilrag1V1K6IQykT+DEMz6oN7uWYY2QjMgcFB4i4MIsRDzuC6r7zAzwaCHFjBaOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICWFBbzlEyQ//jBb8UBeSmxeE+Z+2scJc0Wcx1qy39MfAiEAmS96dt9TkncYYoxs1ndDCd/vE7ZSvlzSWOsQIzuqw5I="}]}, "engines": {"node": ">=0.8"}}, "1.2.2": {"name": "word-wrap", "version": "1.2.2", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"shasum": "8fa78c3bda3e3138c7797fabceae709968814b41", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.2.tgz", "integrity": "sha512-zOvQWZh81woequl4nXg1yLyeE3qCid2aSs4YvJa4hdrbKobDoiMb82g4xTdraoXXM9BgbNZGHzECeK8wrMGhxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDCtlxf/twT+AOWWvwArXUbIL1YjlKWoT41nQh9cZ/uUAIgENnQKriu2hzn0mUeDto6HvihkkwEQ+XpnXE8k7pl3LQ="}]}, "engines": {"node": ">=0.10.0"}}, "1.2.3": {"name": "word-wrap", "version": "1.2.3", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"integrity": "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ==", "shasum": "610636f6b1f703891bd34771ccb17fb93b47079c", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.3.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDx27+lBfLp8aSzUMAu8N3rU7IwflMyyyNGN1tja4tTFAIhALhbb46c+LQ2aZ5T0t0nyVTYcyZf4xLYpRmFw392UFKt"}]}, "engines": {"node": ">=0.10.0"}}, "1.2.4": {"name": "word-wrap", "version": "1.2.4", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"integrity": "sha512-2V81OA4ugVo5pRo46hAoD2ivUJx8jXmWXfUkY4KFNw0hEptvN0QfH3K4nHiwzGeKl5rFKedV48QVoqYavy4YpA==", "shasum": "cb4b50ec9aca570abd1f52f33cd45b6c61739a9f", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.4.tgz", "fileCount": 5, "unpackedSize": 11829, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD3FCESa1UWmAxQhJ3Eb42EIIpsRtIU/UZSeLTHm2vrbgIgJpgWYFOdWAc4BvzmmnomiXzw9tY2oNu1BMRv/ylS7cI="}]}, "engines": {"node": ">=0.10.0"}}, "1.2.5": {"name": "word-wrap", "version": "1.2.5", "devDependencies": {"gulp-format-md": "^0.1.11", "mocha": "^3.2.0"}, "dist": {"integrity": "sha512-B<PERSON>22B<PERSON>eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==", "shasum": "d2c45c6dd4fbce621a66f136cbe328afd0410b34", "tarball": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "fileCount": 5, "unpackedSize": 11845, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDHGyCX26h8kvIdRX65I9XKJwiBANVGy/41Pnij0VDnnwIgL6K9zXEXWoClMoFWFTRXfFxgpWPtnhMCi0S3OBrkFKA="}]}, "engines": {"node": ">=0.10.0"}}}, "modified": "2023-07-22T14:37:38.953Z", "cachedAt": 1747660590350}