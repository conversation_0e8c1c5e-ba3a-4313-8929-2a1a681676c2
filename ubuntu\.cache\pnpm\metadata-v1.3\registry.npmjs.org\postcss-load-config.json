{"name": "postcss-load-config", "dist-tags": {"latest": "6.0.1"}, "versions": {"1.0.0-alpha": {"name": "postcss-load-config", "version": "1.0.0-alpha", "dependencies": {"cosmiconfig": "^1.1.0"}, "devDependencies": {"ava": "^0.15.1", "nyc": "^6.4.4", "postcss": "^5.0.21", "sugarss": "^0.1.3", "standard": "^7.1.1", "coveralls": "^2.11.9", "babel-core": "^6.9.1", "postcss-bem": "^0.4.1", "postcss-less": "^0.12.0", "postcss-scss": "^0.1.8", "babel-register": "^6.9.0", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0", "babel-preset-es2015": "^6.9.0"}, "dist": {"shasum": "593ca9197c9e0ebce5697d1325a5c5efb0139efd", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-alpha.tgz", "integrity": "sha512-79aDXT8skLonBx1B0pVfwpu9BdyN9yUJvsFmdc20boOenm7oNHyiX8n8uYcUYUdI7jmpqZ+vDcT+68S+7LhK5A==", "signatures": [{"sig": "MEUCIBzK8HhbrRxWxHif8sksn4T4TTR1UBgd9GyNTGX1RRn4AiEAujpQirz9VtAQSF5MBFwcgyT9S/UzrShUjUUQ1d05lfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "1.0.0-alpha2": {"name": "postcss-load-config", "version": "1.0.0-alpha2", "dependencies": {"cosmiconfig": "^1.1.0", "postcss-load-options": "^1.0.0-alpha2", "postcss-load-plugins": "^2.0.0-alpha2"}, "devDependencies": {"ava": "^0.15.2", "nyc": "^6.6.1", "postcss": "^5.0.21", "sugarss": "^0.1.3", "standard": "^7.1.2", "coveralls": "^2.11.9", "postcss-bem": "^0.4.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0"}, "dist": {"shasum": "d296fa4f3366cc36c59e833aaa305a4a403ad63c", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-alpha2.tgz", "integrity": "sha512-HQhB7mp1vR1GNxX115ES7ZvLx31jH2fpi98JBD2veFfCrHUsf1e4uXN9GLFx8F1pXt5PFKjnYe8yRaLMcHab9g==", "signatures": [{"sig": "MEYCIQDhDoMTsB59dHZBs9b0C+gaf2Vu0KftUEKQ83QSMfl9PwIhAKMQix2OX4/LHXK0GF/WwIaivCyVqJDjLBSVK4u3PbcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "1.0.0-alpha3": {"name": "postcss-load-config", "version": "1.0.0-alpha3", "dependencies": {"cosmiconfig": "^1.1.0", "postcss-load-options": "^1.0.0-alpha3", "postcss-load-plugins": "^2.0.0-alpha3"}, "devDependencies": {"ava": "^0.15.2", "nyc": "^6.6.1", "postcss": "^5.0.21", "sugarss": "^0.1.3", "standard": "^7.1.2", "coveralls": "^2.11.9", "postcss-bem": "^0.4.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0"}, "dist": {"shasum": "f9918b736a8d2b951807edd8b522dc58828c8be7", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-alpha3.tgz", "integrity": "sha512-WCOCKUd0leEQPjzsQugm3BfXX0oL/+1Dk7v/MF5Y9voO3+OzbNMhOpKkroPf6juoLifOXeeAQCtId2oTyS8LlQ==", "signatures": [{"sig": "MEUCIBxiL6Y9L01qd4wYsmcjJBvkQSm2FEnDjpoTgHReEWFHAiEAteZhcuhc2A/qLa9vdwCFa0GGFKrGUVpBHl6EtN6ww1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "1.0.0-alpha4": {"name": "postcss-load-config", "version": "1.0.0-alpha4", "dependencies": {"cosmiconfig": "^1.1.0", "postcss-load-options": "^1.0.0-alpha4", "postcss-load-plugins": "^2.0.0-alpha4"}, "devDependencies": {"ava": "^0.15.2", "nyc": "^6.6.1", "postcss": "^5.0.21", "sugarss": "^0.1.3", "standard": "^7.1.2", "coveralls": "^2.11.9", "postcss-bem": "^0.4.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0"}, "dist": {"shasum": "a3117468cf8a650635df0a8c0cc3b042bc5c235f", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-alpha4.tgz", "integrity": "sha512-fNVVNoKL4dd75iMs8z8aUfdYDN32oH6NtSSgR+9VvdFwBOH7RyHDI2wen7LNiSavFgP7Yw+rX8xLKnc25b2gNg==", "signatures": [{"sig": "MEUCIHx9wB5D4Hso79yo8jgHj7fq+itjXHah78ebJghCqWpdAiEAmrBP92FbcY+3c5tKohIbBH8mWmzyH/zFLPjC0X1KP6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "1.0.0-beta": {"name": "postcss-load-config", "version": "1.0.0-beta", "dependencies": {"cosmiconfig": "^2.0.2", "postcss-load-options": "^1.0.1", "postcss-load-plugins": "^2.0.0-beta"}, "devDependencies": {"ava": "^0.16.0", "nyc": "^8.3.1", "cssnano": "^3.7.7", "postcss": "^5.2.4", "sugarss": "^0.2.0", "standard": "^8.4.0", "coveralls": "^2.11.14", "postcss-scss": "^0.3.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0", "jsdoc-to-markdown": "^2.0.0"}, "dist": {"shasum": "5a91188f834d4f8ed4b26381f51c92a570163e3b", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-beta.tgz", "integrity": "sha512-SyCYVXu4eWLOvDTaq9bJxHt2V8Mw/iwLcwgw/xINkcliX9mmnBqdiIlFlaOBYhBZHkhZQXX+ZQNExb0w9MgSsg==", "signatures": [{"sig": "MEYCIQD0o41X/Xji7In5Gns1JojFqV53OhjLeoCF76dbpSza9gIhAJ7G6xX2jRggmp6xVZ0xFQdtWkhreXcDKazCj/wqrLrl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=3", "node": ">=4"}}, "1.0.0-rc": {"name": "postcss-load-config", "version": "1.0.0-rc", "dependencies": {"cosmiconfig": "^2.1.0", "object-assign": "^4.1.0", "postcss-load-options": "^1.0.2", "postcss-load-plugins": "^2.0.0-rc"}, "devDependencies": {"ava": "^0.16.0", "nyc": "^8.3.1", "cssnano": "^3.7.7", "postcss": "^5.2.4", "sugarss": "^0.2.0", "standard": "^8.4.0", "coveralls": "^2.11.14", "postcss-scss": "^0.3.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0", "jsdoc-to-markdown": "^2.0.0"}, "dist": {"shasum": "8aed0d0fb94afe2c1ab0ba2ca69da3af5079e2cc", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0-rc.tgz", "integrity": "sha512-27ktOiQCqyMHK9sL157+Dt4FrGPcN+a7FN0JJI0FjPq1IieKYSQ+R8zshMm6qXQGB+l282CyYyTvnrm+nY5htw==", "signatures": [{"sig": "MEQCIE/m8fdEF72TZTKFd8q4PsIX6Ido5P3GIZE4FdRVYWIzAiA3lKycraSl0ujMGCLLz48/i5SpWtEB54VzEtcHb7qQ8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=3", "node": ">=0.12"}}, "1.0.0": {"name": "postcss-load-config", "version": "1.0.0", "dependencies": {"cosmiconfig": "^2.1.0", "object-assign": "^4.1.0", "postcss-load-options": "^1.0.2", "postcss-load-plugins": "^2.0.0"}, "devDependencies": {"ava": "^0.16.0", "nyc": "^8.3.1", "cssnano": "^3.7.7", "postcss": "^5.2.4", "sugarss": "^0.2.0", "standard": "^8.4.0", "coveralls": "^2.11.14", "postcss-scss": "^0.3.1", "postcss-import": "^8.1.2", "postcss-nested": "^1.0.0", "postcss-sprites": "^4.0.0", "jsdoc-to-markdown": "^2.0.0"}, "dist": {"shasum": "1399f60dcd6bd9c3124b2eb22960f77f9dc08b3d", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.0.0.tgz", "integrity": "sha512-aiIVcBoxSQjyYu+nszY3uqOF5+DFT3vo0EE/WT1J97P/TpOiaqSYbsB0pfXdnvKq/yhhGpRv787db4pG4dGF6A==", "signatures": [{"sig": "MEQCIE9R6IHk5elIu434StBRgPe2dKpp0hBYCmucJr60FVcAAiB9nY432qK25NxIVBhXGSh1+9hCNIe6BFOtzHsGzu1oNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"npm": ">=3", "node": ">=0.12"}}, "1.1.0": {"name": "postcss-load-config", "version": "1.1.0", "dependencies": {"cosmiconfig": "^2.1.0", "object-assign": "^4.1.0", "postcss-load-options": "^1.1.0", "postcss-load-plugins": "^2.2.0"}, "devDependencies": {"ava": "^0.17.0", "nyc": "^10.0.0", "cssnano": "^3.10.0", "postcss": "^5.2.8", "sugarss": "^0.2.0", "standard": "^8.6.0", "coveralls": "^2.11.15", "postcss-scss": "^0.4.0", "postcss-import": "^9.1.0", "postcss-nested": "^1.0.0", "postcss-cssnext": "^2.8.0", "postcss-sprites": "^4.1.1", "jsdoc-to-markdown": "^2.0.0", "standard-changelog": "0.0.1"}, "dist": {"shasum": "1c3c217608642448c03bebf3c32b1b28985293f9", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.1.0.tgz", "integrity": "sha512-tT/pIkaNIy+N4nMBndJJPoO49xDgVwr/xcJCFPZO7h8z5EW2TSqh2IHS7m9AzFV8zJx4cS2aUUFAoE/Y0gRaAw==", "signatures": [{"sig": "MEUCIDjwPETkpWPsqTBPBvSwUJpt5YjzL3jHeBKpMYW+RUwZAiEAkFHOfO277xbYsHixzZOvU20O1LwWVctL05ww1eyOWPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "1.2.0": {"name": "postcss-load-config", "version": "1.2.0", "dependencies": {"cosmiconfig": "^2.1.0", "object-assign": "^4.1.0", "postcss-load-options": "^1.2.0", "postcss-load-plugins": "^2.3.0"}, "devDependencies": {"ava": "^0.18.1", "nyc": "^10.1.0", "cssnano": "^3.10.0", "postcss": "^5.2.12", "sugarss": "^0.2.0", "standard": "^8.6.0", "coveralls": "^2.11.16", "postcss-scss": "^0.4.0", "postcss-import": "^9.1.0", "postcss-nested": "^1.0.0", "postcss-cssnext": "^2.8.0", "postcss-sprites": "^4.2.0", "jsdoc-to-markdown": "^3.0.0", "standard-changelog": "0.0.1"}, "dist": {"shasum": "539e9afc9ddc8620121ebf9d8c3673e0ce50d28a", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-1.2.0.tgz", "integrity": "sha512-3fpCfnXo9Qd/O/q/XL4cJUhRsqjVD2V1Vhy3wOEcLE5kz0TGtdDXJSoiTdH4e847KphbEac4+EZSH4qLRYIgLw==", "signatures": [{"sig": "MEUCIQDb15jNOaoO2sPgk/K0T9Yus2GiyHJYw1MCl4vEJqLIsAIgDT5L5KfU2LBXVmKAlR1L077QicluI1LpjEJY+srUX/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "2.0.0": {"name": "postcss-load-config", "version": "2.0.0", "dependencies": {"import-cwd": "^2.0.0", "cosmiconfig": "^4.0.0"}, "devDependencies": {"jest": "^22.0.0", "cssnano": "^4.0.0", "del-cli": "^1.0.0", "postcss": "^6.0.0", "sugarss": "^1.0.0", "standard": "^11.0.0", "postcss-import": "^11.0.0", "postcss-nested": "^3.0.0", "standard-version": "4.0.0", "jsdoc-to-markdown": "^4.0.0"}, "dist": {"shasum": "f1312ddbf5912cd747177083c5ef7a19d62ee484", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.0.0.tgz", "fileCount": 7, "integrity": "sha512-V5JBLzw406BB8UIfsAWSK2KSwIJ5yoEIVFb4gVkXci0QdKgA24jLmHZ/ghe/GgX0lJ0/D1uUK1ejhzEY94MChQ==", "signatures": [{"sig": "MEUCIQCzM2ZoHqE8nbwsbBz9WkLhSeU41Om5+UbAkWAMK2XssQIgfQYhO5jfngA3yBU/xq4//xi/E0KqHsQHhi08Yrua5pE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRL5NCRA9TVsSAnZWagAAA1YP/0T+gQFqqx1NGN3l7KRp\nAgiUunkOukMIoN/V6xqc3kukDrYX/EvQFKVFO4ToaKmbsxd98+JMHFjRU2U5\nMf70M8jPdtmRvPh/7h1u0wolUVuu/wMw1B/LcIJFSoZA7/9ocbww3oG5+wii\nL+RAUE6lexFMRhqC9BUYwJLtVChiICS01ySt6BMgQu830BX11y/nzctPemBK\nfHHMvP7o6qOcHSvoKGzKtjEvNbCaJudhGQaytACexzcCUVDUWBCeEVkKMbgS\nN5rnJiublgLJwgll6ntab89qmo+Dt0ZpnfjbdtHRG8QrkanEjIi6ieTa2acG\nUPFGdoyQ0ZCyMqAEWMLLASr3FOYgYhVnGaYhE5XoyLg3JOlKs6q7hU1sosDy\nz+fj/4kfQP0O/tNmVnaf0BzvEdEx/Ls8PPbj/gASvba35c3+UI+G6SSIaetT\ngbsvFdxBWLx+ChC3HusnkAYjm80LAtTJK5z7zdF7iCgRrw4BFCr7kJGt+Kc8\njKZ6GXdnzHCqIpe4+IQuHxrDfxtDQykM/ZnamzZbDcq96WvB7MjvhU2bSwSv\n7pOTuP3E/re2GlQjYCnR5lsC73YZv95eXzOIz2vb9Tu+/JmVDrlgBtOXZSdO\n1QMfz51P9bB2fqv0/SqeF0DKEBb+MTnJTZX77Azoevjpotai5seO2X9WgzyV\ns0kV\r\n=Vi5O\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "2.1.0": {"name": "postcss-load-config", "version": "2.1.0", "dependencies": {"import-cwd": "^2.0.0", "cosmiconfig": "^5.0.0"}, "devDependencies": {"jest": "^24.0.0", "cssnano": "^4.0.0", "postcss": "^7.0.0", "sugarss": "^2.0.0", "standard": "^12.0.0", "postcss-import": "^12.0.0", "postcss-nested": "^4.0.0", "standard-version": "6.0.0", "jsdoc-to-markdown": "^5.0.0"}, "dist": {"shasum": "c84d692b7bb7b41ddced94ee62e8ab31b417b003", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.0.tgz", "fileCount": 7, "integrity": "sha512-4pV3JJVPLd5+RueiVVB+gFOAa7GWc25XQcMp86Zexzke69mKf6Nx9LRcQywdz7yZI9n1udOxmLuAwTBypypF8Q==", "signatures": [{"sig": "MEYCIQCWPd8Pz/mcXesBtcYADBpPZQ+ZTjs0JX9+L7SW5pM4xgIhAJ6QHSijGfRS1jqpaf7tQwNJap9cdKJyXmc/lvCz8wtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/psICRA9TVsSAnZWagAAQSUP/3CbuveDh2vv6gUieXvQ\nujl1uUPG79i+G/4wVrH9EqD6vCEjhcn8zjdlve0kogkXmLqwSlOR2GTpHobR\nBljT089AIMHMUct1+OwUDXLDNd2/vnJt+JonxFG6tXcjexhq1h3PqTr4HO7n\ndyMjndWHxAuMNYY8eKvG7/IxjQTmhSEVHjCLFwDIwIBm7ogi8RO5ufPXcjli\nyuIOQtOsmsD3d3C1ahCnSIPbXq0Bl98HrhbrrT2bZzakMdBmhXldSLMz7EVI\nOGmY20KUqaf2Ovt632lQJauqsvuncsR7PJchSsmhfj5eW5lUDL8fbRxQfgok\nIRgwvtF4L1TrNmFwVkZNnw/fjcOOGKz1ekJfbnNxuPor/ONZ71Gx5sDK94md\nGfTxpLBKbzvHmhUIKgA9Hmh/GWNuIzWBz4RtqKB4w267bzydZj/29MqbDx/o\n0XgtYLLtrzApoQ3sHpUP6aXkVWxg4rFTQTkXBo8Ese67239KN1C7XHDAJH7P\nkWBtiY9LMdmffcvD/TQ9jrDV0TNWz2Q+xWRchcx+YCf90or4GKZRgy/HS4j8\nm1UasBvF8q+F/afxYTDRnd/Yvf6he5I6tckcWomFU1AgpDDD7T7EJqgCJ+ss\n6JkK7A2LPPWCHfFy4iXrlMau7yQh5IIJQw++8DZNCdUYmjXLbl0DV2D6LYTf\ndGTL\r\n=7zE6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "2.1.1": {"name": "postcss-load-config", "version": "2.1.1", "dependencies": {"import-cwd": "^2.0.0", "cosmiconfig": "^5.0.0"}, "dist": {"shasum": "0a684bb8beb05e55baf922f7ab44c3edb17cf78e", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.1.tgz", "fileCount": 7, "integrity": "sha512-D2ENobdoZsW0+BHy4x1CAkXtbXtYWYRIxL/JbtRBqrRGOPtJ2zoga/bEZWhV/ShWB5saVxJMzbMdSyA/vv4tXw==", "signatures": [{"sig": "MEQCIAZ7JrtBmi05F6moersGRvc6g1jRcjZFz7UAgW/Zp5d8AiA11tbiD4FmLByzSEWo0fmlvaJti8BDAVgmKdhVapSSPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfY+LxCRA9TVsSAnZWagAAj3oP/06IF2IFGBrrGkNpfWKD\n3LnLm9GbDb3L49uhdUcQRMapcGTNF+bVi5XVEVCp/JTN3sOiT2csCj6ioljC\n/v+ooefo61HS0GWREEMwYb4h4kY2qnOhodrhTOzAooRHFNpNfcLx7gwucYbD\ne/CmyghEM5qTxMfqgSwcbHwlHYQZIjxnZRMfpB848vqZr8GH3w0Vw6dVZAA6\nn+8CTyDE/RRSoINQEJbOZNkxy7Te2lRy5tXDkZXf/66TN5OmeAOA5b5Z2GPJ\nP6SqVWam7kvaod8+nIzWOaUZ6bKM0If0/58HelvJxd0bB+YhMEF5jcXJ8/7v\n/BvcKsSt5g/hpXO4i6a43wWhgvdnXRr4HhxaOzKgqphstQ3GjEMklcO9HDCg\nYTtPJjEK2pWypjaG9UuUfAdfZ37KmeA/9UeycryTzXTly387Y/fdwXT0DZ63\nERpKhkDdECJTLY3/MPW24Y8ryjOoEVtf90SeNWfS9/M/JAs0WKwk+o7YKOgg\nBjuawyvmm53m6QI2uffOpQ5ug/3wk4XIrlAqKctaVv9VpGiCKfKaPeHYPJ8g\njvAlphhJm9xVxSXXIDuh4Hv/uJgvAIUTi0tgbL+4r9Fi9mntNytXDU/D7j8I\n1lnmewq50u7bUcRU3vXrVlKPWgY7TRlqpfHOy3eQimtwGb56cQjAfwkpefJK\nmbFi\r\n=/XOZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "2.1.2": {"name": "postcss-load-config", "version": "2.1.2", "dependencies": {"import-cwd": "^2.0.0", "cosmiconfig": "^5.0.0"}, "dist": {"shasum": "c5ea504f2c4aef33c7359a34de3573772ad7502a", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-2.1.2.tgz", "fileCount": 7, "integrity": "sha512-/rDeGV6vMUo3mwJZmeHfEDvwnTKKqQ0S7OHUi/kJvvtx3aWtyWG2/0ZWnzCt2keEclwN6Tf0DST2v9kITdOKYw==", "signatures": [{"sig": "MEUCIGp0C+KmbABxrpHefCI/V+Nln5A07W90MAywpYzkTw9XAiEA666/H0n9xJ4yZYa4KGeubDJ/9f6znC+akdngQMW1Ad8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb9uQCRA9TVsSAnZWagAA7mcP/iYQl/31yrDH8GQb2fVc\nU1aSRfDElucynYlwXPg4PEhV0McurHWHBOmjModSz0fdkW27sKUXFrmv8WEF\nR1PBMjPX78bEi01YR937OcVah1K5Aos4wRb3q8/7xxlLzDP9ZrY7sJBbkdvq\ndx2//owtDHtCDj5GGZd2a+5P3vBnxyVnpWFB1NH3VtDb2hc4Y2om4l77szt5\nhcoRU0D+iZF9vYfi7piKFz3orfz72cUca9C8/oSDR4KezUJKpI12+CTRCosg\niExnaX28mVPxLEK3nKxF3axdoY4Avgjk+ROOlHxfx0cAURsX2JOUgsWlTWNc\nDjuVFq53YHpGdLzzRUC/lZ9JcykqUJxYz7m/klwRoenJjPO5Q6uxLvpDDUlS\nCky4P3pUz1GY65cJb9R3DuJ8XQ0uiroUHGsXdU30O7ep9j7lFBRycofKfK5F\nLLjROjLXE5vln6Mkpuykxkk6cCH2q+GHgGyuTfbihOV+yc2Mfa7llOToXhWa\nrpUHzJhDjrnECgo5VjnVQUdU/tKq/UXm3I9/w0xRY/fuUIxyo5CTKwjOpQCg\n+atYVh8UDF7pPSiQY3lT1ouS7/WL+xr+6oN7Mfmg1qD5tHLnebVI4XuZ73pd\nuigO5x5VF1lOwlW0A/8zfc0nIyxHrKUNp+XT/iGwq3s7hTs4IgzMn+DGDSog\nWC61\r\n=8VcE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.0.0": {"name": "postcss-load-config", "version": "3.0.0", "dependencies": {"import-cwd": "^3.0.0", "cosmiconfig": "^7.0.0"}, "dist": {"shasum": "850bb066edd65b734329eacf83af0c0764226c87", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-lErrN8imuEF1cSiHBV8MiR7HeuzlDpCGNtaMyYHlOBuJHHOGw6S4xOMZp8BbXPr7AGQp14L6PZDlIOpfFJ6f7w==", "signatures": [{"sig": "MEUCIQCvkSnnpa6fPWKhVeiBhED4jg4YEFGf6bQ8n9U0mFkaTQIgN9fi0yjk8cla3oTjNOYMC0c7d8gBqRDlfcNNjuowyI4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdpfmCRA9TVsSAnZWagAAdXkP/3qGxd4VarzVB3/Q0dMV\nsBIHIPh/opxxM+7dVp4BsRhUE40LlTmRXYI5Ava516E4JrySqJZiyibI0K2t\nvWyXvBUbsoganbkTiHvQZkGL9y9NibtPRL+Hk9MqzB9EvrQc7iYQiv0AUKIv\nu5mzMHTGjQVTI7RxFHYtX+NTCcQ/fXrAuIGBgEtGTEW8oFsWTD4afLI/Hxfr\nT5ZbtXx32Wvc6gDNl9q1nFi76Tx1P91R123jwEoMnqjEWECsoVeKMHjqNfp/\nTcW2TkDOBGWHeBOwv8aKIelfPc+l9JzzKogWaSS+LikxEYXtlB5zQ5+oljAj\nsvsO2HafRoTDNbj4hXlT6CUp6HRcxLjYSwfvetQFeDPVrreQd5CUx4MaCGb4\nETCHhVmpdF3z2sMKXsFXKuH6YfLl8IMbe74YCVaD/VYH1+zDqtbHFJjbqCFF\nkHso+hmHkqC3BlWhFfUPP2dR31Fno2ztXJ/rlnOITbbkP7CpYaFSu5l3Y384\nWyuqMfdzeerW84JD7LBvJsmlHXsLNLrjNCl1OKCuTSdPG+QzEwyS1BFP1/iI\nFbW8H9qXNKUiTz2WHOqKXwBtsePOiInpveJymVusK7NXlHDjLx6NN75Vek47\nLDDbanVZHPLX/1wuNVYAZRxZVnGrqX9tUeDNylkYrGK1j7OUkQmKI0LswkHQ\neO+f\r\n=Gra2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.0.1": {"name": "postcss-load-config", "version": "3.0.1", "dependencies": {"import-cwd": "^3.0.0", "cosmiconfig": "^7.0.0"}, "dist": {"shasum": "d214bf9cfec1608ffaf0f4161b3ba20664ab64b9", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.0.1.tgz", "fileCount": 8, "integrity": "sha512-/pDHe30UYZUD11IeG8GWx9lNtu1ToyTsZHnyy45B4Mrwr/Kb6NgYl7k753+05CJNKnjbwh4975amoPJ+TEjHNQ==", "signatures": [{"sig": "MEUCIQDB2oT8pNUVjhaczTHHseg1vq/N1DnvR1QJ6loTGKVwBAIgJ3zDap4GVQgeLzc27mQnOXMKtXbbHpTNDivhNlRhxWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgIH9aCRA9TVsSAnZWagAAe9cQAKHuLp/1ckQZBba85WtA\nk1PFlUv+Zw8jPECmq5l6p2y8qvs92Q9r0L1oHz61cUFcBEZkIUD4liRfCGQS\nFo/zIibHSq9YGZNu5BWtFFzbWlLFY+oCo8unJ75wDCTTfeZ0HqT+Qz0M/Hrt\nGnv0Tn2u9CA3D+dvSOdvDugUNLDXUbqAH7sDwe3DcDkx53KhTbZ0CrW9zemi\nR5/244vYeEf5qZCUQxV9LzCeuzSz+FMNEXp6uy01DHqyWjtnw3vnDcxD6L5s\nEUEtMQsOHyYOrPw31+9L14q8iiRry6Hk7MAcX1sDD58ukAaHRwvtnvZZL1eZ\nJs3Ai2kQWqAhCv1Pqte+iA+utt5yLYon/gkY0r2v1KEP2GQJOE3PZu8sq0NI\nyZid7n4HgJSj5qyhkpgOh0if4HbaSTdG9NTLWmCQSpkjFepb7evOnjcJZRQ+\nvCOkVPBrwfVA2YBA4HBob0iJNHv3QUiRrNT7uAXqrvaAO14QWSpaOKakjky/\no7+6S6PM7zEO2Rm0LiIkb/zWM95rXPEluy00jENd6cbcFPjdvVVhiOSQqg4D\n+qLT/I9xi5VHxFx/p8X7XkM0H3WbjksQOBhguhZ2r/NNEqkoqNoRtUufpda6\nrWmi8npW9LCwRMq0RUPnGDbncgqYAZG+gSHSeFLKpi1Sww/5Rl+5ha869SC9\nauAq\r\n=Trpy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.1.0": {"name": "postcss-load-config", "version": "3.1.0", "dependencies": {"yaml": "^1.10.2", "lilconfig": "^2.0.3", "import-cwd": "^3.0.0"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "dist": {"shasum": "d39c47091c4aec37f50272373a6a648ef5e97829", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.0.tgz", "fileCount": 7, "integrity": "sha512-ipM8Ds01ZUophjDTQYSVP70slFSYg3T0/zyfII5vzhN6V57YSxMgG5syXuwi5VtS8wSf3iL30v0uBdoIVx4Q0g==", "signatures": [{"sig": "MEUCIQCHgny2UA71WbJRNLVSlLd7BVs6/7AdW9Paw5e4NY7zuQIgHPl7eOsqv7kAVeZmeW7JbDzUeJvFWgklqBfRNYl4aBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxzrJCRA9TVsSAnZWagAAlPcP/3JGUdSyR9sAIDSUqx5x\nUPnXt/I4Wzu6I3T0/opj7iMJXFqr2/1Ku9v1i8UIuoUKz+mJi0u038+QrQYH\nyPaUBN85FcEQ8t06xnbOHW/zDOqvZ18dqWW4QZ0ouLvaEY+fi2CKKbDoILUh\nYxTVEaZ+RWJW+jvrLNT5IKhbr4XXfPc/zTR31ViKwn2yr6wxkyjLkoSBA3vR\nKQNLA/R3VUXz+xEB03Id402gt23x3yWhRcaBwWHhOzAYEOKi9IIg8pHBjAt1\nK5e6FxcEKZjuvYKu/p+2aPrb2NzzBekPtX8kYc17trZCeIc24nVTwYu5gbGe\nPEowPjW86aMD8UJnvG60kLS17ICb5nbI90FURSBR2A3s532jyn0tw7wefBbO\n1WCtCXNCQiXgjksU5lat0g+Vrrq2gYWdhanWEN/pPORobKTVzSvOvr1f+oGK\n8UuciLsUMPuI6j4MEXsDE1LjRTC0GonCMh51zUtnaM0xvl4j5Zfpp/oi+RBJ\ni8l3DTVykjAyJLAXKqpB8Bhq4yYLyFlMPmB4jDsN3f1zgOxCuC0oz+pZYF02\nqp71lYqhKcQTaqUqGL2dd/I1+7LKCm+bVqj+M3D4s1TNPvFe6g7u6b/C8MrX\nU85Gn/JVofa9xgUVysYEMHhexR94TbssJ2OTuwr+ldzOPwvFIhwkFRf0sJIN\niJ37\r\n=1qTP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.1.1": {"name": "postcss-load-config", "version": "3.1.1", "dependencies": {"yaml": "^1.10.2", "lilconfig": "^2.0.4"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "dist": {"shasum": "2f53a17f2f543d9e63864460af42efdac0d41f87", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.1.tgz", "fileCount": 7, "integrity": "sha512-c/9XYboIbSEUZpiD1UQD0IKiUe8n9WHYV7YFe7X7J+ZwCsEKkUJSFWjS9hBU1RR9THR7jMXst8sxiqP0jjo2mg==", "signatures": [{"sig": "MEYCIQCjqWkXKjAAJ2/cMEq0/D1eaFi2mflUPr+meCyTcBSLEAIhALl+omQDx/A1Lx6Ig2l0/XZ6/1hWuY3OQdhr87Mt8r6Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh0MFICRA9TVsSAnZWagAAnGMQAKST9zVCKf9+2JNCdm8i\nHtG4fSQ6cMakJWMPbAAUf2T+piKCzk7NsUSzr8GKXjyJ5y2MjXvF/ocL8Cuj\nYayTqju1RYYCEuZVvJmrYWJbt0yXqLoqM/d9LrCxQcmhZhEv81o/4eogmn1k\n5D/8l81JuwroaanhEFY0oWbzmJtKtqcZTFodmL20sft0MrGRvBvW6d31nnhy\ncoiokf+gX+Iw4mysl/emOTfTzT4aBuIYpzNkT6FDr0md9FfHH0+F5MgYqMa5\nv9ftvkQxuLbwwp7Xwr58HmIOKYpcoWs5k0+gNcN70xbObM0jrCLkMjMa1iwL\nN+AjQspRi3zC9NA3y0cBCMKsTs7C4jhMb2XvElNqDej6vewdiaKsInS6nGE+\nYBBJXIfa3s40hJCFZjRyHoelDIXOksmSuJdTwfTW/9owuQfDwSxpB41XQCa9\nNyNYPn+RD3muFtxAOaGz6sp3ZKZeUYQqTYdu397svptZFZWLOMk4ZEWt/suW\nWyBnXPXgq882RAad+nS6r5/U8zPllyq/RTGEv2JRT/OW7izdF8nDsUBrKfto\nA8xmkhGeRTFphRpnilb2ffjks/l+T7vYD+Tr8RshlxgtYEig1Gfe4GjpPlVQ\n5jIglE9p0jtAXS4yyAjDgFqzoU80S3KU5GTNI9tJRAJ+VFgQzPELWJ3pVBzm\nOjP8\r\n=Lvm9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.1.2": {"name": "postcss-load-config", "version": "3.1.2", "dependencies": {"yaml": "^1.10.2", "lilconfig": "^2.0.4"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "dist": {"shasum": "c3a24208ad168f317464aaf59655252781f04365", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.2.tgz", "fileCount": 8, "integrity": "sha512-X1NVP1itP6VE5dDA4wR6NK1g9lNlkBx9A+tgDKb/8Mnx4HrvX6k+DcTXGelZvfp6p4zCBZjh4Gwyp4aptOUI9Q==", "signatures": [{"sig": "MEUCIQCf5MpsXKrb4zELBUmySMj3QvHMBzaqICDCUu9gDz8ZuAIgcYTHmiBtqaJXBKhJesXvstexmVx1cpJjw7fHNCHnZ0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBSUdCRA9TVsSAnZWagAA+aIP/RSPAmA9UQHDxS15aZES\nlMaYb1uI7oTY+SQ209pmIxhTym/QpSazw5dNDnsgy/tRn5h7nVCoJb/qgiqV\nrQVmoWEdm4kQHQs3kM7YbI8O/fEIWGsBZ4jGGHlqIRxvDJfbvtxmBjTiCH+R\nXdBLVFSKQnDLHUGFg8Iz2YXtxIU/7REnrt/91HwoJer/gGI52NTR+qKB/5p2\nt6/glbQSbAvCWqPT4sV24pqYc5V1paLmlLaoAMZKEaMV/ZuTuiYEB0imotBM\nWZx228mcCmtGcOteDxkyXWWuavWVBxxLlzPEw70yBtLtSrhpxQz6BT2kG5x+\ncY8QxIKguSs2+gbOMqHtHK+Ap1fnifQS8Wuuy92fABtfC9Wj0uqeGCOoDR9Y\ngKjJR78m6NSzd1NuAEzscnxNVrwlDkhxLV/0jfAF8WSLx1aI1JCjw8Gdv0hp\nINxWb5DuC9impsn/DLrE6ZznyqXOT2ZDiNqybThkmpJmtQmy7F6WNYP6ixhG\nFzTzMasdVDcu7ayurnfRulwH9m6jwU4tmcVODDCXytrPtv9Jri6ntAk0n0to\nEHtuYvtsDY8kSeRlepT7tDbnbPoE4WnuTbxHcPyWZgYeAtaX7347a3m7nRL7\nk1m1Pj8Ut6fo+mYFtKld2ZWssPOq4tD5LIiaim+acUhUd3Kh2eeP4lbi570e\nAaf6\r\n=SQWf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.1.3": {"name": "postcss-load-config", "version": "3.1.3", "dependencies": {"yaml": "^1.10.2", "lilconfig": "^2.0.4"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "dist": {"shasum": "21935b2c43b9a86e6581a576ca7ee1bde2bd1d23", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.3.tgz", "fileCount": 8, "integrity": "sha512-5EYgaM9auHGtO//ljHH+v/aC/TQ5LHXtL7bQajNAUBKUVKiYE8rYpFms7+V26D9FncaGe2zwCoPQsFKb5zF/Hw==", "signatures": [{"sig": "MEQCIGstQVdSMxwY8nZgUG6ghO3zIZrMoONJklsg85iQnTdFAiBWj9L9p1N0pHqcbD7HBT8n3ISUxy45/GBnHkBycFTV9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBj9sCRA9TVsSAnZWagAAAOwP/i7+xgpezLLiXMWJdHYw\na1+7GwMDkEt0QxFfqpXs1vJa6Ao7cQMhsETa5xynx4VJcJbYEanZrl7E2jEU\nHX5o37QrwhYcGTz37bC11davY05zsB5daVsgq8JdriP4NlWLvkrBk4QjgXbs\neM+S8dW9nZXtpdFCv86CT/9tWx4eJ6f7ocZkl635tsKrOa5/Vscg2FJGKSLA\nrw0CkIPxIb6284me667dQ3bbt0EAPbMcNlknWrWEzQtYxeIZZcCNPTB8nFtl\neGlPmOcos8Mh1F82xfSDkGPYcDVI8EijgzQVYxWGIpUvhCIGBkz7rWenVxLA\ndzky2UU2zXh8zr/RfO8DJRJ06PLykoTgsGa47VsldOgXUKLqT3ne8qp1pIqT\n49CizdjjyCgcPWY+X08mAb4Kl38G3IA/Cvc4HhkXp3+5xpKSoOw4bEPohIra\nOcoSsWYKTb688BCjc3DsxjQ8o7N6ISp5J/JtXvx6vR1X8YvFNIrDakECkPd8\nQauEG29a4aQLFkb7B/z8/kVAH46dE528nd26wqxE+VttmPKDwZtjlQ+4iAf2\n0QdrUkX3CmW5ImglxIQ2S07hYWJA+2na8PjRA2Y5K2wQhTm8Q2mPxELpa7fu\nNCYktcsmiXYMTI+RIb0Nyfxpc7HrisZcfYKV3iLjSXesDT+Re7AePc4IxFuV\nwu/O\r\n=sdss\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "3.1.4": {"name": "postcss-load-config", "version": "3.1.4", "dependencies": {"yaml": "^1.10.2", "lilconfig": "^2.0.5"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "dist": {"shasum": "1ab2571faf84bb078877e1d07905eabe9ebda855", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.4.tgz", "fileCount": 8, "integrity": "sha512-6DiM4E7v4coTE4uzA8U//WhtPwyhiim3eyjEMFCnUpzbrkK9wJHgKDT2mR+HbtSrd/NubVaYTOpSpjUl8NQeRg==", "signatures": [{"sig": "MEQCIGBNi24SOTP48MP0+qYdu6ggqzGsd8zkiDBHjFGmfvzyAiBteLo2VDQ+WzXuWPA74MNhvXLaW9qyhpYRnHE5oYopEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiQuRRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmQRAAgLMe2x6Zxze3qISUnb0300c1GM6CS62olv+/i8uibCNPw1Pt\r\ndlrNrj85mICWS3sISF2xrKNU2ZzxRdi5iNdlZASQ4NkAuBrFuZlQlxyL05mG\r\nB4SmCa9pwzimD5C0SP960Pk24TI1MTUnql8vL32bRBDa5Hucyzus1jHIpy3r\r\nnywHZvf7rkyBKaRuFODhX6V7QbCkxSJoaqcAyBUNuo1PlhoFt/002b1K0ndP\r\nDKfPXzyRF29+QixziMrT90AV776nTuislfE38SnEGBSeJ5Bl8ZPktRCej/QX\r\neyIoF3HdmF/YtTgkH3RBXRThq0IYuWDrFvNipdhBTVQ/yXg2f1PYYpTJ8lqL\r\n3VJrAMDlrdsmMmsgK/W+i3QldE/M+fPT1yLobHGFffvS/0qT7Km0F1CMTu8n\r\nTdJDLv9ZAbSiDhQR2eTK0DV3Sr1HAdhkVznNe+heUX+dtvWMcESyJjx6/YIR\r\nkresnq4uX6BJyrkWF6E3dpVv+Ovo8YbAJTT6TBxtjflMymx7t+oh0tr859zA\r\nBoIHRl4iHfAU8nnEynH5w7OXt4Nk96sVX9TIp1U+cXcPXNcJZiD8CgBaXCv/\r\naPP23ItvsgoGtkQuUNQZ7UJ385WmHXMC1SVIwUVSPJIlsQYSU7FjXhN1Pzs/\r\nXsRKTaaDrrglGwhLUyx9p8RJvgrLzYtL5Kc=\r\n=PXD9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "4.0.0": {"name": "postcss-load-config", "version": "4.0.0", "dependencies": {"yaml": "^2.1.0", "lilconfig": "^2.0.5"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "dist": {"shasum": "4c95647abc531bdf648647b3bf9b64c030a4bff5", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-HBMHYoN1OobLx7EpbuW52AOZiJ408ISFJ6QTF/kJ8aKhpoOXFIDzEtcRwoh+JdEt6oUkcU7XZa2Ot0KJ5sI+jQ==", "signatures": [{"sig": "MEQCIGsu0JYc9d5EgHdaXTDWNhCyrTarlDWO9FAbUVoavLMEAiBBeMmMGB2SF4u9T1KWxaDgVoU8ynVEHD2ZmN/WsSRF3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22282, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiit2YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp95w//U5QWQ/Z4GtdpolzBCdhKGdS16szAu0hLeoNGhZvWTZ8Gu5oK\r\nNr1iNFU/mTaKpEmTFXJczm3M7NZY4VS0NhRvWJvXPZ8MfJeAkB/vYtcWTL56\r\nh7N8Iv8hDMDtzuxAA9vFqTTVFWPPpYaKZxKnrDP4DQoFH34vXBE4W6uMi8Qv\r\na/wCe0Us51h8iMGeyCqvd3c6H0TxWcWjpfZbHNiJtNWmd7FLIvhYJCeBYgI2\r\nilENbe73PUasbRpMqgNhkh9A1q+eg3mVqtAwGzWrykjeZVoHbHfwAcKt6Hg/\r\nukW2x14wK0h40J7NyeUHJC19A9wE+aiSdXP78ivARvIHjTXtuV0PY2OaIKAY\r\nFl/+7xPmktmrFFDJjKzz+kmdafnLYYknLHbCDM2yp+ZKhUjgAEult2vR2LOL\r\n/O3Xf2vlK8n+yTSca01LYl5N1PMlMOStfRQS6uk10rSEIqNl/EFqhxCeoJry\r\nCpj9V8lxqSALvEhC2T+UX7wWOAbhxUc3skWB8PE/H74Hp0WSPEPdFT4Lh5lm\r\nojFBgGC9xllM2g+7XRhqcSUVk52kPo0j6IkfSXeE718uDGpU4+v2DGA6PuoM\r\nLzHW9YvltUEP4DDLpVqYBT0ok4jCJGEacDzzHcpfdSFZ7X4MARG4xZcaVVy/\r\n2wYXDaNHTW7h/rGSFB74mT1FA2sST5hVI7E=\r\n=Zv51\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 14"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "4.0.1": {"name": "postcss-load-config", "version": "4.0.1", "dependencies": {"yaml": "^2.1.1", "lilconfig": "^2.0.5"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "dist": {"shasum": "152383f481c2758274404e4962743191d73875bd", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.1.tgz", "fileCount": 8, "integrity": "sha512-vEJIc8RdiBRu3oRAI0ymerOn+7rPuMvRXslTvZUKZonDHFIczxztIyJ1urxM1x9JXEikvpWWTUUqal5j/8QgvA==", "signatures": [{"sig": "MEUCIGD38t+zkYWSEjWdNBk0hOiDwSbJwlASBiU0siXUZgIOAiEA5B3dMydrELTS1sgPRL74drnqVKwoPQQblQN94ml2LgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilXprACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIVQ/+O5BsAQxTHkBd3fX0OMOoI7I9D/ylIhahTgr5yTtAegyp7ym8\r\nWkVgf4xMVSGP0Zy5XQgMUxV4+rTVeaMEIQ1qmhe03Y+P4mOQ6Svv8gr1fq24\r\nQqZbYqZ+fjmN1ur5nQzE5TLekgABnf2Io0TlyPUOXdDHFsVNRkH7r2U2rJ0C\r\nMdZBKhiN88pZ2OiRkBO2b7X7HoFlsdckFxAns8Lp5Brdi3SVq15ukAG9k0DW\r\n+zrWrBtC5+DrNs+URrDlLQTzyx5QtBeRCVUCbw9q86NbHGfr4Iccc3IZKS9y\r\ntXug4XHFxOnARC5BRtgXQq2FJg5gfNRg8OSNG8oEsayoL8DeR70ykLoKyzcK\r\n4LPiKtMziebkS5ppKlwYrXoxuHbkXouK/Dlay2jVmfUE5vkR8ddmZ9o1p+5+\r\nIL9zvqN5a6zB0qP2SmKB7VRUnueHU131q5BwrWtVKTXo+h2HK22tU0H/zr2m\r\nNJS/rNOYriBcG+s7TZbHXVtO9A286DyW8HDDlfitFAyPVTQcyH+5S+KZ566c\r\nfsRtSTTO6UfuELF/Zjd0Qfla/9s09HNdP/bTXi5RYpfijqM0TbHdyx0OPosc\r\nW9h8XnSoahZRT3qWQd7FvTyg6EE2uwhPhqZd8Rjv+gXxd3k+9+QKnTE5rDlO\r\njkWIIjVyhvW7ZrPOjAsYsdOwW31uiJUCA2Y=\r\n=U11Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 14"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "4.0.2": {"name": "postcss-load-config", "version": "4.0.2", "dependencies": {"yaml": "^2.3.4", "lilconfig": "^3.0.0"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "dist": {"shasum": "7159dcf626118d33e299f485d6afe4aff7c4a3e3", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz", "fileCount": 8, "integrity": "sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==", "signatures": [{"sig": "MEYCIQDTs77s0sC6mxLZKUK3UvxMmTGPFobUIEoN4iMiBKJghQIhAP5s+Mu/mZXJYOqQ/Ry/UqvGpCYR98GSZOVgFAoGsWBO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21626}, "engines": {"node": ">= 14"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "5.0.0": {"name": "postcss-load-config", "version": "5.0.0", "dependencies": {"yaml": "^2.3.4", "lilconfig": "^3.0.0"}, "peerDependencies": {"jiti": ">=1.21.0", "postcss": ">=8.0.9"}, "dist": {"shasum": "51cea9e062343692014443f0f7ec0e88a6cb2de6", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-5.0.0.tgz", "fileCount": 8, "integrity": "sha512-i069VOAUi6mpTsToA+qPAAfD7m7dfjm9Yhaqma7KEMJcQFM9jerKBU6rTGNcYLErj4sjR9fZ1q30TpFm2K7PSg==", "signatures": [{"sig": "MEUCIQCMX4iKzycPSXlRDglnCJT7Pbg2OllUP+scWqKvr0UxPgIgTBJvUt+so9egkIw68BBjnK4SzCEM8WxdRJUXqJD/eYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21665}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"jiti": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "5.0.1": {"name": "postcss-load-config", "version": "5.0.1", "dependencies": {"yaml": "^2.3.4", "lilconfig": "^3.0.0"}, "peerDependencies": {"jiti": ">=1.21.0", "postcss": ">=8.0.9"}, "dist": {"shasum": "301c462fe54903d6d1ffe1e36f7a181e29686a4b", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-5.0.1.tgz", "fileCount": 8, "integrity": "sha512-d7kcvvpqQpCeC8DkmiKFiOK1sH8Rv8Sei3O0it+o4ixd+jIllmUfVBe8qOZOorMrlSwd4Ke3I7UAE1bOU7u40A==", "signatures": [{"sig": "MEQCIBVtlOSqgN/fjCvWxmTPzcFneVTg+fXRfRKpvJhr5WsWAiAalLu9kDuMMmdlVQyrm5j/PGL0th8bjZvC63e4CvNV8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21731}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"jiti": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "5.0.2": {"name": "postcss-load-config", "version": "5.0.2", "dependencies": {"yaml": "^2.3.4", "lilconfig": "^3.0.0"}, "peerDependencies": {"jiti": ">=1.21.0", "postcss": ">=8.0.9"}, "dist": {"shasum": "3d4261d616428e3d6e41c8236c3e456c0f49266f", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-5.0.2.tgz", "fileCount": 8, "integrity": "sha512-Q8QR3FYbqOKa0bnC1UQ2bFq9/ulHX5Bi34muzitMr8aDtUelO5xKeJEYC/5smE0jNE9zdB/NBnOwXKexELbRlw==", "signatures": [{"sig": "MEUCIDHebNL9INUTAj8bNSYhoFiSEfmp8A6cbp8a8FPbymVFAiEA6z/vHb+yXbnKNqkvBcl52mFKPl1ES2FNz6UsbN4Xa0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21703}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"jiti": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "5.0.3": {"name": "postcss-load-config", "version": "5.0.3", "dependencies": {"yaml": "^2.3.4", "lilconfig": "^3.0.0"}, "peerDependencies": {"jiti": ">=1.21.0", "postcss": ">=8.0.9"}, "dist": {"shasum": "f4927637d907de900c4828615077646844545820", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-5.0.3.tgz", "fileCount": 8, "integrity": "sha512-90pBBI5apUVruIEdCxZic93Wm+i9fTrp7TXbgdUCH+/L+2WnfpITSpq5dFU/IPvbv7aNiMlQISpUkAm3fEcvgQ==", "signatures": [{"sig": "MEUCIQDG+iw2CGc1Qu6pNsCC1XZlkq8k6ytSfbkfSvJGTSLWpgIgPFWICjcXnrYoguQ6zv9YO/ysJ+eMUxvHPeY2pNZnJmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21719}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"jiti": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "5.1.0": {"name": "postcss-load-config", "version": "5.1.0", "dependencies": {"yaml": "^2.4.2", "lilconfig": "^3.1.1"}, "peerDependencies": {"tsx": "^4.8.1", "jiti": ">=1.21.0", "postcss": ">=8.0.9"}, "dist": {"shasum": "4ded23410da973e05edae9d41fa99bb5c1d5477f", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-5.1.0.tgz", "fileCount": 8, "integrity": "sha512-G5AJ+IX0aD0dygOE0yFZQ/huFFMSNneyfp0e3/bT05a8OfPC5FUoZRPfGijUdGOJNMewJiwzcHJXFafFzeKFVA==", "signatures": [{"sig": "MEUCIQCpbiLzKMMi0PNok53tK2YFU3pwXkVQNJnRqeTEsw3n1QIga2GbBjGfIYZKzH2xOTqVTTYp0oQlFJn3Kfk5jKM7WGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22221}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"tsx": {"optional": true}, "jiti": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "6.0.0": {"name": "postcss-load-config", "version": "6.0.0", "dependencies": {"lilconfig": "^3.1.1"}, "peerDependencies": {"tsx": "^4.8.1", "jiti": ">=1.21.0", "yaml": "^2.4.2", "postcss": ">=8.0.9"}, "dist": {"shasum": "6e7ff070bc4b84d1cb44a5d266e6d290faf3fc4a", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-6.0.0.tgz", "fileCount": 8, "integrity": "sha512-Awdbn0DxIoOD0y9ljT7T8nm57tA0EGhnBRN2o8Dn/qZj4XuFfQ9q3mDgB5f4+A8wuD6x23RUgr0RMaId4bcFvA==", "signatures": [{"sig": "MEUCICalm4TqprxV4dG7aMgWn5IS4oqisB5zWO0RUqvtQZD8AiEAuHK4REfKcDEzVe61VeIJqWl8De18F0HVf9kOv11Lrmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22606}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"tsx": {"optional": true}, "jiti": {"optional": true}, "yaml": {"optional": true}, "postcss": {"optional": true}}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "6.0.1": {"name": "postcss-load-config", "version": "6.0.1", "dependencies": {"lilconfig": "^3.1.1"}, "peerDependencies": {"jiti": ">=1.21.0", "postcss": ">=8.0.9", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "dist": {"integrity": "sha512-oPtTM4oerL+UXmx+93ytZVN82RrlY/wPUV8IeDxFrzIjXOLF1pN+EmKPLbubvKHT2HC20xXsCAH2Z+CKV6Oz/g==", "shasum": "6fd7dcd8ae89badcf1b2d644489cbabf83aa8096", "tarball": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-6.0.1.tgz", "fileCount": 8, "unpackedSize": 22654, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDhx6EJGL7kL+4dqtvVnhxPL+36NZiWyyrnHvC2nQ4HPAIgBev1Pa3wbILStMkE/EyrNDrH8tTapsFPrp+iw3F9UdM="}]}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"jiti": {"optional": true}, "postcss": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}}, "modified": "2024-06-02T19:02:03.980Z", "cachedAt": 1747660589200}