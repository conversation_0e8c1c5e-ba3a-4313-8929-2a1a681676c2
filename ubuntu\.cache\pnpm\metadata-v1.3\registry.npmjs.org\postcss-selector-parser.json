{"name": "postcss-selector-parser", "dist-tags": {"next": "5.0.0-rc.1", "latest": "7.1.0"}, "versions": {"0.0.1": {"name": "postcss-selector-parser", "version": "0.0.1", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "06c1a51283d9248a96079d932b5730910b5f0df5", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-0.0.1.tgz", "integrity": "sha512-jogMyRt7KYFbcYpc+oqghvvO9AOfgPXJO8+fNwQ7aJSzxEu10trJW/dD4weHFLkjrz4yvWaeaw2x65pOrPoOBg==", "signatures": [{"sig": "MEYCIQDfRPeXLeMQT0hqVxWoqDOwXx+cPteNX5Pjn7GPV1C7SAIhAOusC3eX3fe+F9/x988AR5UNcllxdU000t7R6pUBhu9W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.2": {"name": "postcss-selector-parser", "version": "0.0.2", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "d1b486e4582a22d8f0aab3019e97984de83cac2b", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-0.0.2.tgz", "integrity": "sha512-kdsxPUFjP+6IBaxYf47eEVid/9eGC9WPLrP827P1xw+MOyqWtyc+bbu7NOsZJR+aDFbVTARgr+gcuuFPrikhKg==", "signatures": [{"sig": "MEQCICc7og/D6IUsBMOp1zb+YGABvivCoSSJI9iVmvcSTJROAiBAGwgpd/ZUbMfxuKokfhJINJywSN2vVbj8I2l3PyrZxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.3": {"name": "postcss-selector-parser", "version": "0.0.3", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "ca84dc2a2ee99bbfb786fc92411de00af76de391", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-0.0.3.tgz", "integrity": "sha512-wcmH+BKiwtzVUGn7gB9hRFN1uGMKkedHVRuR919xcDpkIh/lmavsifz2WCbQPTzT+3ZBdnVOD9aZZii0zR78+g==", "signatures": [{"sig": "MEUCIEtCGNle9PjPGyUWuWa5tVxPkp71bR59w/A5wE+xDmgSAiEAivtNHlQ9kg3eh64Tc+cPClzOsYh4wQ+nx847hWzfaoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.4": {"name": "postcss-selector-parser", "version": "0.0.4", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "1cb3b5379256a14a4a3a3338737df96232e67c88", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-0.0.4.tgz", "integrity": "sha512-mMeHGvreNG8IG9/6x+PdEujHHxCKfn7V9i0S020b6pJkX8Gu9OVHBY4DXTc+YEGpFCr49E4m0u7nCcv8SmZN6g==", "signatures": [{"sig": "MEUCIHkdV+EGYq9+V97rCLjs9/de2F4RZgmdutwIs3f8UeVfAiEAosnf8bLVqz902616tarCGblyBt9pX3R4yATGc1DCve8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.5": {"name": "postcss-selector-parser", "version": "0.0.5", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "4f8b07e34121775c86c3d4ff36c378b855262951", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-0.0.5.tgz", "integrity": "sha512-vsYPElSZaVkd2MdynA2m5vq2GYBuIj8WiPTmrC6hBVteOgt1lj5Pihkfm/ZiG5KaoskYO1KTKvtboBl08PdOCQ==", "signatures": [{"sig": "MEQCIEGQoXoSSPMWmw0bUBxRtgi1k4dotcvB0XIma8AoRlsRAiArXckn+Hg+T7bP53rter17/opd6QsQIW44jXICpBuWeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "postcss-selector-parser", "version": "1.0.0", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "db060a7fa860822f36b7b01e0f02ee2777049edf", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.0.0.tgz", "integrity": "sha512-MOWY7H+lCrmVzO5G50CvfjJ1jXuegcigtV6GqrCt81NCRH0AIgAEggrcmIMMUCGu6jujRH/4DGlptbFZHygTFg==", "signatures": [{"sig": "MEUCIHSkzlMBWpNzw9M+n5t1NIV0+sbk0CWx0qjmsq0lKCSSAiEAuYVdOiIDlImQpSGW7u6v+7Hdxb28odA02peMVDkqVi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "postcss-selector-parser", "version": "1.0.1", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "fd475aaf057f9eb0c3211b3533cb8515f0e30836", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.0.1.tgz", "integrity": "sha512-WDadN4aRdwCsMr3y5XGoLJCkOLODry6ieJMfw72H4JHR/O876aMfsGPiyzbzzl/uF9i+J4JW68hNMHLdU/ja3g==", "signatures": [{"sig": "MEUCIQCgxQOMUo9zEua2jYEy5lTFzYGc39N9pg2Hpxv6Sq8kDgIgCE36mfbwyCmI0xdNLK/wjMCU+SSNbKWLq2abRe32Ll0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "postcss-selector-parser", "version": "1.1.0", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "577e369c1922dc467e37c3bd806b871b10502740", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.1.0.tgz", "integrity": "sha512-REmgxuQsOzsYiF/90G4IXv/ihaSh/pSGEw/P1ouTrZA17FcNfB/1AH+geJD8ZAzdfRErr5XCz3A/9NU3lIHsTA==", "signatures": [{"sig": "MEQCIEdyAIG1nDSayd1LOv3bQ8jBdFSDGe+eSuTeaFaHAwVFAiB9ckJg08yesgkqNrEIPSrK4lwGjgZRpc2dxaEfWNAy5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "postcss-selector-parser", "version": "1.1.1", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "ea8d81f6f378c3f8ff8d7d7c25c46f4d430b7af1", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.1.1.tgz", "integrity": "sha512-<PERSON>msLCJey95kKkbhyd6L53Ew3EEb5kF9aqTNyQdEjFRRLltCBwIRIpKlsP/CLa+yhh07x66Ka96oreQ4rkLXSRA==", "signatures": [{"sig": "MEQCIADUxRHr5hTbmb2QGKKyUCvywrpgE2Z06o9JGiGqnR6ZAiBRX2qGC7VzUnkbEM1VDUA2tfpvSNZpfgZlt5tEkakEqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "postcss-selector-parser", "version": "1.1.2", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "5d74089d0c27aa37f1c9ba28a0eb0b647ce8eb03", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.1.2.tgz", "integrity": "sha512-MJvjxdQrO2smFV3Np3hAbU16o6PpqldC+20KlgWnYfZvoH4ysWdnrFgDYQ321SMGZPD8WZGk7720stVFRhzLyg==", "signatures": [{"sig": "MEUCIQCkMCVNI49PUDYSPSeA8SJ+2N+ewQpc4XzIMxZieiZ/WgIgfCdtgzNDx/WEXRuZE5p6gOoQzcpUfvlXEbKySPSokFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "postcss-selector-parser", "version": "1.1.3", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "91322911f639db3dc150498717f88fd92fe4d0c2", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.1.3.tgz", "integrity": "sha512-KijS5eNQOHTROwbWrKHISlSR68RTg4yq1+t5lAnaRihX3ey3GceXvo3PN1gHRgpG31PSskvqmNs3QH0cyolaVg==", "signatures": [{"sig": "MEUCIQD0y1ANenCbbulHCX1OKfjhfzXnAf6UlC1KEBIr5+Yc3QIgOm6E6BLhblV9ZmjYweFb/RFEmihrRoZkBNzt3n2Qw44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "postcss-selector-parser", "version": "1.1.4", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "15aeaff3d406af7e78d9d83deea8061342649108", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.1.4.tgz", "integrity": "sha512-9iVio89SzxxviJgpXmpGq2jqtkunEXRMSX+stG78XYUYfJfoNuTAr1zSr/Im9fyFUsMWRe+pS6cZOJHLpG7Znw==", "signatures": [{"sig": "MEQCIAPEteEdDiY3x2I2zTzn7qD/2TK2GyTqatHi6JjS0MxjAiABm6PEeakhAtFyb81pnETYEFbT7RJ4sUJfLO26Sb+jkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "postcss-selector-parser", "version": "1.2.0", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "d6dca9ca0ee8a80e99c999de5f2bb980da8ba988", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.2.0.tgz", "integrity": "sha512-UYPDj+55Q9pABXi+vhkDjh150ce5VMXLj4DF/cpycm1PgONRoAws8UVP1idO6P7qFyU7myqg9xSEOLeKx7XXqw==", "signatures": [{"sig": "MEUCIFL0uXmFpdLo/KrS/GslqXNffTgpc+CbeLAvykToo+yPAiEA7Wjb0EwnEY4CSJVnmFM8BrpFP47NwawNMgHbjbK7n58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "postcss-selector-parser", "version": "1.2.1", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "bcdbd002746908f0695edd3dc79fc38aa68a74a4", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.2.1.tgz", "integrity": "sha512-ry<PERSON>bDMBuzpQjVVGMO2B9to0O73KW6cMRZeBQ8QpgUabBQMpjiWNFBJd9KOYo4MZ0/ZoDFnBWjHUA7XIYcy0ZbQ==", "signatures": [{"sig": "MEYCIQDsV+p3ibiE1U9WB3VmdfjjSeaVb+4h8Eg8g0U8ZHx6gAIhAOiogOz7c3yHA0Vo7NNT61+PoIe8ei3IPsfVLmxX43Aj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "postcss-selector-parser", "version": "1.3.0", "dependencies": {"uniq": "^1.0.1", "flatten": "0.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "babel": "^5.4.3", "faucet": "0.0.1", "babel-tape-runner": "^1.1.0"}, "dist": {"shasum": "3df60a87fd313869110f0ee4b09712d0703fa885", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.3.0.tgz", "integrity": "sha512-<PERSON><PERSON><PERSON>33qyfkVgRLRVLjwwOUsRGUL0MF265T5xlGkpkxLeiqM3ZJSXE4bTDaH2y/628HrrzvZlmPewZrQCK7Jt8Qw==", "signatures": [{"sig": "MEUCIQDiXMRj+IkQeIbxsfAxpfiPa2em63Tko36Z6+q+SFXlBgIgaCjH0rW3krYHCOEMGLRiAzOEHTK1tL8AoAxwMEPLMyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.1": {"name": "postcss-selector-parser", "version": "1.3.1", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.11.0", "nyc": "^5.3.0", "eslint": "^1.10.3", "del-cli": "^0.1.2", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.1.2"}, "dist": {"shasum": "72a4371e3b29157f7d6c748c218b7cf0cda5f125", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.3.1.tgz", "integrity": "sha512-rBahkGO8M7iM++VQlg2jDqmYh0n78cfqifNi/9bpGlKzsZKf4gKN1TqCpnWqbFvCGEngp8wIUKOJbPmdcCRh+w==", "signatures": [{"sig": "MEYCIQCWsjzzR65MGqMLQyM4ZxaCnNzNeVdA59bYwlbCQboDkAIhANYhZVh5a1cpU7NjMbYsAS2uFrhffW+GmUDVWGWYq51Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.2": {"name": "postcss-selector-parser", "version": "1.3.2", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.12.0", "nyc": "^5.3.0", "eslint": "^2.1.0", "del-cli": "^0.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13", "eslint-config-cssnano": "^2.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.1.2", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "54eda2419dbd0b9110be2db4b021c46f0a40d84d", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.3.2.tgz", "integrity": "sha512-cigl28kuHwNJg+cxHTwP6a2T4EzU7Fm+3Wg4DuFQ0dULcMQzXHBuHWoplER44u2WJ9SHKmiL8DCtdyFI86wDFQ==", "signatures": [{"sig": "MEUCIQCeouo4K4XdE16IfplseipUM4YkFwYhnUbOQNEUR9LCaQIgdZ2ZefQNSMPxVDnxBu2f5Tqucdltiw0cVjNr304x6x4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.3": {"name": "postcss-selector-parser", "version": "1.3.3", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.12.0", "nyc": "^6.0.0", "eslint": "^2.1.0", "del-cli": "^0.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13", "eslint-config-cssnano": "^2.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.1.2", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "d2ee19df7a64f8ef21c1a71c86f7d4835c88c281", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-1.3.3.tgz", "integrity": "sha512-YVWTPQprpsXhiQyZe3PW1U5stw+/OI7mMG7REN5sx9z6eaIpuzTUm5vy9RI4NTLR7hC9SqNYmxhyxTkorC2KFg==", "signatures": [{"sig": "MEUCIQDaN3Nu8eGuabS2PIHuWPfF4gbuL5xhZy9lKqh3rOwKYwIgBc6H+pbaRzVB8u6aXvddbzdjcqEcdtMOvckAKg4DbSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "postcss-selector-parser", "version": "2.0.0", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.14.0", "nyc": "^6.0.0", "glob": "^7.0.3", "eslint": "^2.1.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13", "eslint-config-cssnano": "^2.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.1.2", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "6fc229c85c5f8e8357258918512b520d9775a4f0", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.0.0.tgz", "integrity": "sha512-F7huHITXI1tRM+IhVNZKvikyGi/YW80+9Kj2TWeQNv0LmvbOse/pKY0Lp+a84cXba7Vg1EdEUAsSfHHZkeVCTA==", "signatures": [{"sig": "MEQCIDM9uFDjkMjueDdAiqOt0gaRICNxVmcvzSpe0FrJWQvyAiBoKCISLyOqKFQJGnKdOUpy/Fu8gpzST1PNBPq0OFu7uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "postcss-selector-parser", "version": "2.1.0", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.15.0", "nyc": "^6.0.0", "glob": "^7.0.3", "eslint": "^2.1.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-preset-es2015": "^6.3.13", "babel-preset-stage-0": "^6.3.13", "eslint-config-cssnano": "^2.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "1a169c616cd03d7ba82526d4320602d4a798bc96", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.1.0.tgz", "integrity": "sha512-tLj7EegeE0fzbGYTVYYYE46LG5zh+tilqXH0zCd7mRt4GJdBIYNMFewXJ8EMOZmdBPzfef6S+shFVwlsNxYwFQ==", "signatures": [{"sig": "MEQCIAqwaUtybhZF3i29HVGJ3kijbr2wG2VPHEkvccZ0Tz3XAiB/56QyUfziV0C8x1WQnQc61ceMBfvi8zlhe1jlcXYItg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "postcss-selector-parser", "version": "2.1.1", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.15.0", "nyc": "^7.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "261bfd60b447278412960032e0ebe1cdce22e5ba", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.1.1.tgz", "integrity": "sha512-LvtfHGJewitQYGUgt+CQjrZBbX6Qq9a/0VI35+IYnPzwLSQxSAtNqUGBj096IZvUpcb4katcY3jv+aQEtgp3Zw==", "signatures": [{"sig": "MEQCIFSBfE+043GJCdIO4e8FQJLTQPR03hjbus8R04vWpBQpAiA3yhK6RjnKRkmmiulvByfhZSm205wNd8TjOa9Ae6R9nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "postcss-selector-parser", "version": "2.2.0", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.15.0", "nyc": "^7.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "2b9776d2bc34cdd4ab93c7473d1b98ad4da285e7", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.0.tgz", "integrity": "sha512-eGYYm2SejHDEWODJOpE8upAWXcx7ovPImixJ7s62Pw5U7w4oEPvKH2x3ILPKCzwzF1GWClP9W6GkYbGH6VBsRA==", "signatures": [{"sig": "MEUCICEV2QSUx70U2qN1bslR+ea6EVsKQdGrTeTxUF2ISzy/AiEAzmCdlgqzW+p9gFSahmWXNaNDMZMtLmvjY+4YXuhGinA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.1": {"name": "postcss-selector-parser", "version": "2.2.1", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.16.0", "nyc": "^8.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "fdbf696103b12b0a64060e5610507f410491f7c8", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.1.tgz", "integrity": "sha512-LRzWH77wkR+qOzxFn4ZRSE0qza0b0jOqvmISZU5ZoxeMZyz1JXBCffApXbi+IqByMl3A/mM1kN+iHSIJzeVooQ==", "signatures": [{"sig": "MEUCIH14rg3FvWaK1Ct5xsDmXNxILDAH6GhuRn8RUYwwBndgAiEAz9aWtMlRyJY74hIiEtg+ccy0m9zpxunbBFNnUzcaCjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.2": {"name": "postcss-selector-parser", "version": "2.2.2", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.16.0", "nyc": "^8.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "3d70f5adda130da51c7c0c2fc023f56b1374fe08", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.2.tgz", "integrity": "sha512-Zjh9KK2A0u7Gaxwg86O6QeDeCuuLVelWbVqhsN8XG1bSFfwVZyy9swasbSpjrmZ0CEA/nEiX5xJTVtkulJv/7w==", "signatures": [{"sig": "MEUCIB4D1FrWO7UQRAR5RhgH1StSu/vNU4jSg7LbFeC1dor/AiEApka9iu8/Zk1Dh3etTyRsOcPZhZHArgL4FJCrHTDKJxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.3": {"name": "postcss-selector-parser", "version": "2.2.3", "dependencies": {"uniq": "^1.0.1", "flatten": "^1.0.2", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.17.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "f9437788606c3c9acee16ffe8d8b16297f27bb90", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-2.2.3.tgz", "integrity": "sha512-3pqyakeGhrO0BQ5+/tGTfvi5IAUAhHRayGK8WFSu06aEv2BmHoXw/Mhb+w7VY5HERIuC+QoUI7wgrCcq2hqCVA==", "signatures": [{"sig": "MEQCIB8j1QxKcl8vmSdR/3joV9bIdzOkEgAhCnORgfmz3tRJAiA4G9UIlSJ5vuv6JoMsDQ8B++N9X0NrvmMbkkc0bGQLKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0-rc.0": {"name": "postcss-selector-parser", "version": "3.0.0-rc.0", "dependencies": {"uniq": "^1.0.1", "dot-prop": "^4.1.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.20.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "dfb74f1cf3bc043d5692e37203170ac2da40a150", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.0.0-rc.0.tgz", "integrity": "sha512-2UKOTLluMJelH/L6rK9dbQ6V4dLuPy0zAZeSb2O0Qz95fRJLXxLhB9I20u0q/WEJNjUJVCGkFfS07t6JZm+aJg==", "signatures": [{"sig": "MEUCIQCX6LPy8NbHg4HrQfIfduhr4IiyGxnp6MUKqYTaWf7pngIgHdjftUvEAn0Dk9RkKhsNT4i5bPVYNCRzIaO7y2Iux8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "3.0.0": {"name": "postcss-selector-parser", "version": "3.0.0", "dependencies": {"uniq": "^1.0.1", "dot-prop": "^4.1.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.20.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "ada923d28724a5c78975aaf7c6016f63c192468b", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.0.0.tgz", "integrity": "sha512-vk3HWSUqLCBPRK8NNrogWL83cGeAB4D17hu/CzOHW4ozmT5DgS2+R08DsnjVjQIqVYBVW5fklBA/CANfoxc1iw==", "signatures": [{"sig": "MEQCIDtfD6ge4upR8Y+Kf94X7L2qaNxKj/l9jrcORilvtcYLAiBda0MOabfW5kOjQWx/OLU6MFIZ6voWONaEV2Lb4ujGgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "3.1.0": {"name": "postcss-selector-parser", "version": "3.1.0", "dependencies": {"uniq": "^1.0.1", "dot-prop": "^4.1.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.20.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "7984a6c2897747756b5a0235935782b0ee054a5c", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.0.tgz", "integrity": "sha512-naBFATn1NgOeLRmI/hKRHJEjqekIGdKwYA1PeYnq3kRZ66uhxvb7rcm4ArADShFhpktvfBThJYbphFoKG22Vug==", "signatures": [{"sig": "MEUCIQC7+ziraJOZiJ0GoxH2xBroeXofouvlG9tsLNYhS30/4wIgNnpCiM2QTDR89jE++UVALc8CPVkLaZwr/puClgbkNc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "3.1.1": {"name": "postcss-selector-parser", "version": "3.1.1", "dependencies": {"uniq": "^1.0.1", "dot-prop": "^4.1.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.20.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "4f875f4afb0c96573d5cf4d74011aee250a7e865", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.1.tgz", "integrity": "sha512-ngip+qFQyMK6HpalUODPxc/a2QSb+cp/6qVUGDUwwNNfQTnPK77Wam3iy9RBu5P+uuw0G+7680lrg1elcVfFIg==", "signatures": [{"sig": "MEQCIEvycKE7kDMgz+sQtZImHby37j9IFzoEuX78K3WLli+EAiBqernM7oMWG4VtUGy05KcKnZPzKJ6OaVsodc3WaM/ioA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "4.0.0-rc.0": {"name": "postcss-selector-parser", "version": "4.0.0-rc.0", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "2e8441d46717280b41b85d78e0dadbf853e90d9a", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-4.0.0-rc.0.tgz", "fileCount": 35, "integrity": "sha512-RSJUzK4/qG9lpNHBNPeNtG3via1MjkP3aQCR5L49xziGr4N1jbrLr+FicQEMaOHH2McziWaU1NsijCClbLz4iA==", "signatures": [{"sig": "MEYCIQCvG2+LfEx0i6/b7tmvO2kHI4FlZ+GtwTB9JBxlRy3tTwIhAMixTSI/rcto7JGhmO6pVkE7Q6BXqod+Ijt95AIouNnA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173848}, "engines": {"node": ">=4"}}, "4.0.0-rc.1": {"name": "postcss-selector-parser", "version": "4.0.0-rc.1", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "e32edb8a8f3ca8300cb20b9207b15ef41b760f88", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-4.0.0-rc.1.tgz", "fileCount": 35, "integrity": "sha512-4kH+Zu/yRtmmVTM2lWru52cRr+W9j3C5XFQWuT9lMTR87G0WseJiBd1LuPX7VCEjbDMeb5A1gVb4Km1OIy6XUA==", "signatures": [{"sig": "MEQCIFZrtPjA2dcvs4Uk7CshenVdb++XOh+mnCfMNhTyA/PQAiBoWadJyLsbfV5jeKiDJygYhiEq8IXkt4uZ9aSgJOqcZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa0dqwCRA9TVsSAnZWagAAbLsP/0hueSDDh20qq9YWAMi/\nwoTptSQ8G0LZy8uVXB+r7iaE9LTNyoVF1cTcmlAf3O3+eajwlaGJm9pls/lY\nLIPHYn3UDtYIOUk43Ox9GRZOTh04fjz+6nxI5q2As+gAKTSCZWMgUC9vviT2\nbmBi4OO9T0vpofCjDAImxuWcyIiOOGQCLRQcndty9Ba0dpb3PLVPZr4wZ8b/\n/7ZDelTYQ6l0sQeYUrvM2UzXAOIOlu3ozmNnJQYB8aK+X8AFVhcNdzdId5or\noBbGDpAJXh3qIbWsDLYha1HTv19UnSDYXZINyoFLCxAYYAzcY29k4an9p17v\nN13qnTwbyHaKV8jK0v+Su1U2XP6fIBBMBJVtUDdwqhTtxydyqBDwCCJB0atF\nZ+gZiIPIoruto46wBhQzGCBOoqHlejUpO/LIvuj33UDs+t7pgOJGW0PxXrr0\nWZrG1Zr0FuoWM64t+FpWIuO26F6z2viq7xf6gLV/+UlHw9DYMM9EIMqn4W9x\nFQnPEplo53jzyc7HpHKExoZusxJ7zvv0AbRRJtFVfxBLAbHftTmNU9Sm9wA8\ncTenTw0xvk+nu5SCmpDB4z37CthF4Od3G4u3i67EloX3CVCCE0YlwPLYkwgz\nUua0uR65JrCz+FuvSBm9dzIRsPTIkdw/bjytpxr6753Hm+abw9xivWssCNnE\nENMI\r\n=Ei5M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "4.0.0": {"name": "postcss-selector-parser", "version": "4.0.0", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "50c6570f40579036d8e63f23e6c0626fe5743527", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-4.0.0.tgz", "fileCount": 35, "integrity": "sha512-5h+MvEjnzu1qy6MabjuoPatsGAjjDV9B24e7Cktjl+ClNtjVjmvAXjOFQr1u7RlWULKNGYaYVE4s+DIIQ4bOGA==", "signatures": [{"sig": "MEYCIQCmRnvcYt+fMnWd/pzpHPlwwufJC9QFygRksdmZXG4ygwIhANOzmfnYU7iZylLRBdNDxoiRezKVtNF2MY6FoNzw0stW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3wFcCRA9TVsSAnZWagAAIuQP/iAWPULbvwk0Jr/yic6m\n7D4o678XdcIRInZLm1/0sYJKP3+i0AMF4oBwX2lXO8fhsPpduu0SbI5llos4\nHnD0fApvBBvb9ky2+6baP/m6K6ZoPhGM0kOCn/23/+yX2wPm/noocvQdM9zU\nbX4j4aUoGwJSoHieqbH/K4A/TVj7ce1BJ85g7WIkNIfEh+7Di9ps5P6sY0zg\nJYDKVS6hnG6pd71JZLuNWqDmht/eVf39L1zR9ROWIj9djRZfVSRswJF/jtt4\naoAGXwmTm1crMum2Zy6Ll4V16WPtxJFOXWZHynkKcLF+ny2R0I2F9BcNA5xS\nbuJDD26sFkTT0KSf/1ea8NgSfH75AA6K3rTwN66J2HNfOkSarPEjQoAh5PqD\ntW/Qiw+WvZIjsld4oYhEhVBXsLrxOLKpWNiXVCCL1raI3kYHEhTdUISk7abd\naKCXDy5cFqhIgSNwVdXMxZawMXqLWCC7VY0AAJHdjGlvJE+2tH1p0UJPgRRl\nfT1eiwfB45X/HwpiF97LLVXYBXwnkJK7JmabFlOyk0rOpZWdKF6Q7ttjdpjm\nLd2+Kq5zvAh9LoIcGiDfh5YZIddXVWKWb698yCJS2Ah8SpnhtJLLo2YMlOCL\ns2kW8PO2NAVAn51XL+xGVIe5owihlnekkUEM2DV1Q7mWqX1KRhAiFQuk5Qu5\nVo5o\r\n=KZWB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.0.0-rc.0": {"name": "postcss-selector-parser", "version": "5.0.0-rc.0", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "9bcb81c6d0e92480da00c3b30bddc9b80c2da31d", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0-rc.0.tgz", "fileCount": 36, "integrity": "sha512-WlFesmdzNvNMmG254JrtG7aeQGndgMyc118MNdMDOc8PiAk10sC3P6o+8sAEIvtDdyxP80MxyRM2TlGWv5EZFQ==", "signatures": [{"sig": "MEUCIQCf9U6FCILE9D4CryqH+2C1E2LIFcgaSKRUadZ/grAG7gIgYO0xol1YIKezNioImUu6Oso2o2ffj/cYwsZVNTpodBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7MRmCRA9TVsSAnZWagAA1p8P/0Xn/osIa1rkv7ci1/bH\njkhzZH6BpSDEN0DNEsLMUpAdMF2fwYvWesBLMFiT1Um+l2WD44Gnly4isVse\nAF31cc/7R3hE6ybR7CQdyk7YynspZAv74g6Nm/glKtJoLMeVYhIvrODFCflL\nT+lQsFU+Qok98VhOX/QCiUAvgkDBw2ryjuvGMTFKQ3ap6Nhn7SZGEVyAU7H/\nwZ7FQPOQ87ZEJT/c8w9BkFh6eKhCCYbqVNnRSE5RkKsHQ7Qsd3Js6iYuXYJB\nGROyXNusU+dWBXKQcrze7TbEGpKuysNI6xjqu4e6kRc/z/7FKBZwLCTxPap/\n8+TRAnAIZfM5bITDSgUTbxsPcgePqV1TX5yfSNwGNmseYgSZuE8O6Khl4jYm\ncyRQRZguIWb/+IYcW1fn8QbXC7Y3VGX78ZM7DDjpg3sb57lpdNGLGh7iYpK9\nZqYs+BFv4GDsMtC0pa/Unk/0yiS1bRpWnnfefCKx+UYe1h4xSfDvw+RXXN1O\n1k7FF2MEvV8u/4k+2Z+IU98lxc+YKQFa61dod7wLKHz13nJfqozMCRtlO0U7\n/k21f/8uVdYs/a9J+TFNatFdl0i6DQ80sDN9MUkjiC5woLvqvVLH9BFy5EL1\naFryKg1Xe7w/UxE55avatLeORp0lANqXfbx5mdm4PsVO+oJshfxxRXRdW3kA\nevaW\r\n=pMVb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "5.0.0-rc.1": {"name": "postcss-selector-parser", "version": "5.0.0-rc.1", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "da7f8970206b9c2905785a70a316bd7f001eb5f1", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0-rc.1.tgz", "fileCount": 36, "integrity": "sha512-vAVnJ0PI9D+5fPH+nvCzU+F5L9+L6drgWg6okngpK/DlvkVukxF5gF/LNT6LInt5G5kYFt5MVgD/1Vv1O0rJEg==", "signatures": [{"sig": "MEUCIQDdunQWfroihqqk8sOg8cmGRqrWlKS4CuoIOj/0mxszBwIgO72EbFkoD4CkQJByRRneo28fruzZ89X3unG7uZmFr20=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa7dWpCRA9TVsSAnZWagAARHEP/iW/mtWxTpTx9GfsJxi1\n4QW0PBIr3MVdUY3G3jn6fHm1pUSwqyxQuPrB39ypELPj1bBpV4Fj5WzUStNh\nqi3oRzXIMukXbHiGP5eEPac8kiXxkpB+PfpHfIpUIU3KdWDN2KqZMAC3aLFf\nqYlRTbGbVgA73XVLeOYgYeiTPrvXw64S3V9HM/g6mfEN0/ZOf5yTNWodhsMH\nzbUGDLI0zWUub47ciUJz1DlLFNhpaj+tWYUIbxjHortUkJZCbAjMhIp2Z28e\n/RG8CWLVbYwRM0Eb1byZu+Qlem1UUySGvfI3jAtSS70PvAAxwhHQK4ZpB3Hq\ngDlMtbCXXDQ6Auy156caQlQQzjDok9TQHb6QoFeaap7Ob48OOp3ocaBvZ/OX\nJRE7wewG+MujYnySipk5OU3sKm4K+vBywfcMD4i1SmAqGp3iWZcaNDU3pQto\nKQXGjokgw/p/CWvELOSEWJit+zs0hqZgORVNssHhpQNLoUpymZoo/2M4C365\n63JgTWWhahNbtGOV6tFqoywFEQv5Pa5LA+SV5lL/c6EVMCwNM+cyEi3TG30V\n7nmFo5GAmXmlKiG2mS+swTrYvlZab6TJ+obiFDhfNhSWXMLMRYcbyEbN2eLx\noMkJf5suERrygePxKB9diuex3GgEnGOVo1/FYZnSXPSanCYrpKKIOL29nKv5\n/GT8\r\n=x20b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.0.0-rc.2": {"name": "postcss-selector-parser", "version": "5.0.0-rc.2", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "glob": "^7.0.3", "eslint": "^3.0.0", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "0e8a6cb0c2746262b35d051d5142627dade47cce", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0-rc.2.tgz", "fileCount": 36, "integrity": "sha512-B95cTaogEk1yIc74ZYslQuU4anWClkNHomyh+jbSrsfSeHIjTf0L8C28PZJv1Ft2iBZxv2C6/gHPMs8mEmAByA==", "signatures": [{"sig": "MEUCIDSiURCRAe824egDu4DjBPCaGVAzC/DvNJOVJsyQuJsNAiEA8AuVLPzRkUlfW560jD3H0yxyDdNLBf7EcLaRQRq1eYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa98vFCRA9TVsSAnZWagAAZd8QAInIt0VyIUqqMHlWdY5Z\noaamuKidTjqIcqjQlLnx00jiazw4VNzaO6LXnKrKaZYJSgJPotjuUfdxlkBo\nOCkiZpNQkXJleBz2bW93bmqt+7cvawfpJMmtPt/tVX40y3djh2N98GetGpCL\nRASwurIXSFbNzVez3E4gaihdvrupTwULeTNwF8gfpE3G8n5KVGahQxx65lad\nWGQLYR19rpKhMpeyVw2BG8O9uW/0OA3bGvzVlyyujuwIpyqZoCFLANDRcgi/\npFhzQ77gQ/rC4rKezR7lH/R9s2jyMtVz8aF4A5ssw3fqGh8bJu6Iv38goBEm\nxM1VaXxB84pVBt427a0xQlfJu8h42WxpuOJHenk1lPUmbvEb/HgslNtY6Ifp\nevYjpfRHE6pGCKaC1DOw5bnarDluNz1K5TRn7vHaO39+qOxDWxGBeczemE19\nmLyEI/xbIATRC9KB44oBWbPlY53WehoX7p9m5vnYb9QSt1Mfc2evBBzoXYPm\nabmp+KS4ud4r8KrcPKFPLPJXDtmX3pH7wmytZoVjuzq7cLUIuOv/xjmnAEhO\nKYWxl6YsuwtnjWMu93rS6pEOghdW76wdNFMJwzus64yDeibmA8RC13vMFgWS\nMimo5e+cCJl+L84HjOJAPu0Ld7OJwhLWujQGni6VaaculNFPEVYyVdgnLplr\npSPe\r\n=8PEG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.0.0-rc.3": {"name": "postcss-selector-parser", "version": "5.0.0-rc.3", "dependencies": {"uniq": "^1.0.1", "cssesc": "^1.0.1", "indexes-of": "^1.0.1", "babel-eslint": "^8.2.3"}, "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "glob": "^7.0.3", "eslint": "^4.19.1", "semver": "^5.5.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.26.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^2.11.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "c4525dcc8eb90166c53dcbf0cb9317ceff5a15b5", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0-rc.3.tgz", "fileCount": 36, "integrity": "sha512-kBl1vc+zJgWCBmmxEXE2/15tmmYdD50lO5r6tLNXEx3K4LtszdLFaSNo8SNVuoI+BGODbWhavoG/n1DrYphBsw==", "signatures": [{"sig": "MEUCIQDvZCxDxyof79vFeqZg+EWgqWgzs8HZ3DF3EW8SjbOTdwIgfuROMQXFJMlhO+0ZD7tfOQI5MNTW0MMgNH3iJSbrktU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+gTHCRA9TVsSAnZWagAAJ/MP+wY+qC+fUQzt9BmkfFGQ\nHUnuO7EVBzreXC6mVtUf82ib8lbJi1XhYy/sACkDwnm+Sdh22tasieUEgMsK\nUu/43OhemVVNwe96xJR6VIjdKja9TRxb4Fke8z74LbEXTD2awlyZhhal7YZc\ntarkcKWeZAe+fSfe1lpYrA0bxB7MG87vfzQNIx+CTDNK2Jl/pNd5r3FqXRKy\nt/kqd48WQRysjz2Gu6tcsCM7D1jvQHJPEAVcdeWQ60tOt/jBZY7oRwH1R2mb\n7fmHTwKNlbjXdtAUa8Ra3BahBlDOMgJk80JCHoqfVt5izMMq5rsJdKJNd/t9\nGFbkZoLn6Zo+IsncrmCk1yvtBDYmTF8ILK7zAsIOaxVXKQa1Yc/b6rOsAdIO\nTtD/t1lGyI4qXslsaB4/XAeUi203frEy+HmlAyTBzuIs6VMuiaeArJJCpBAv\n5Roahyodril8pQBBQ7FmLY7XrTIYY0LMtfs7IK4ZRkD0xoWBeEYBodlU94MG\nN0m4fRqtnS3wLbSS+IC6DynjLV9p3eUJyvN6hus0YPqlkB4Hi5UgnIfNznee\njYZlFfGEv8z8psjdrVnVFWJWwFnfenaFGfidEB7mGJuO3TjJzRbcFGsnmqWQ\nYMKLuUVr+yzdkFJ/bks8lL0U9aX0900KvehYeiDsoobRXHeXHO145kBf12qI\n3ljq\r\n=Wr1a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.0.0-rc.4": {"name": "postcss-selector-parser", "version": "5.0.0-rc.4", "dependencies": {"uniq": "^1.0.1", "cssesc": "^2.0.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "glob": "^7.1.3", "eslint": "^4.19.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.5", "minimist": "^1.2.0", "babel-cli": "^6.26.0", "coveralls": "^3.0.2", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "babel-register": "^6.26.0", "babel-preset-es2015": "^6.24.1", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.24.1", "eslint-plugin-import": "^2.14.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "ca5e77238bf152966378c13e91ad6d611568ea87", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0-rc.4.tgz", "fileCount": 36, "integrity": "sha512-0XvfYuShrKlTk1ooUrVzMCFQRcypsdEIsGqh5IxC5rdtBi4/M/tDAJeSONwC2MTqEFsmPZYAV7Dd4X8rgAfV0A==", "signatures": [{"sig": "MEQCIESxBEEW8AbBLVtrBPo1FsXcaHPymm9uGI4DewZfHix1AiAwcm53MoNlC0RjRV1izeYV2E/Sed44oKIgSq/iHlnMLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0S0iCRA9TVsSAnZWagAA8IQP/0ggS8Zci7jX6+hcXkP9\nM3VypjfNsb+TD5WJYMjePERfYeSNyimBYagMEkcjL1E3j1Apt70Q/RjvU8OA\n5+asgY6eF9Q4jYywJCGVWWgm7PhEAxVoVrfBMK8Bgx1Ebs/J7xQ/qesePMex\nT5pTYypbqQYR04dVTEEvxHp1TQnvpMjc1eLyezZnc6QTPZRqBzwloO/WUB+H\ne9oUcNmLUHZr1RBOmsv+c1t0eG4T1lBpw+JQyQq6NBh+KQ84CKKUp4BxgDZ8\natZndztC7boKt+rwoZukzxaQ8ErdQnNJBTs53pQ+kldZgjlLMlOxbfuBfE2H\nPPuH2nCzhWQuBkG8Y7pUu4LV2epOCqw+NhxpEb8AsEfjOPjexrH22+w6qgua\neViRuxdqD80ewuBx8D0O9d9urEH6S7v4j6dlkI4+OVLPM0XCYOwafmnmzvya\nB+XQaiWDvPPas54TzZZ5q1Z6SEeRUEVgLMARTzVtcBqkNozVOzKFRq5xHrFC\nGvLPBAet83chzJ4AfjkzWTquXkEr9xJOjMj6hCaYiTnOmmvghRkyCJlXpYBO\nNlZa16PPMZ5aIhIyIBBPaln2C/g/okmAy/fA2jo3E0ASgOsRTTrJ+rMjbLde\nPvaDdYBri/IVrnkwUe4ECtV89yhdioQbWPvWduq+/bBq5H0tNoCItaWdjtMk\nDSSp\r\n=kvQu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "5.0.0": {"name": "postcss-selector-parser", "version": "5.0.0", "dependencies": {"uniq": "^1.0.1", "cssesc": "^2.0.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "glob": "^7.1.3", "eslint": "^4.19.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.7", "minimist": "^1.2.0", "babel-cli": "^6.26.0", "coveralls": "^3.0.2", "babel-core": "^6.26.3", "babel-eslint": "^8.2.3", "babel-register": "^6.26.0", "babel-preset-es2015": "^6.24.1", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.24.1", "eslint-plugin-import": "^2.14.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "249044356697b33b64f1a8f7c80922dddee7195c", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-5.0.0.tgz", "fileCount": 36, "integrity": "sha512-w+zLE5Jhg6Liz8+rQOWEAwtwkyqpfnmsinXjXg6cY7YIONZZtgvE0v2O0uhQBs0peNomOJwWRKt6JBfTdTd3OQ==", "signatures": [{"sig": "MEUCIQDeQipW2AwV+1EVaUZI/i87bN/zPUmLLtkYnUnN3zn3vQIgN7kiBinLyCjRBKsPJngNGjkSPLrcWmP3Ps+GGqC9YWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcHnKVCRA9TVsSAnZWagAAAokP/iIuLfujCThTTIc5cJEA\nO5knHK2TEhQ7ehGIzsTE9IbTLb7lMEHwhyyWi+yjI8wP3w4p5O8XhSWQ5KU+\nALYHv3N28YnXNHwUrV2IZV4BRHijJ41N1x38fHaZEX+mQtuWswtFiHHTPxj3\n/u9AzKLA650eKNpxmABgw2wBcqdmD/ThIwfwF+jfXwx2VL0YFJ0juPuAWsXx\nJzRlbL6UePKQkM6x5RaIUIUAR8h2k2DOPUV+gA+ccP7F9Usui8Rciw3Ujsf5\nv0sV7DU+TSwAJ26FwNUAmQzr0EwliMisXLgnVudo8gUOaIrl+vY35sSOr5YK\n5s/ucMxE8z2A/lK7ikqyPo+V0TU1O7THya52tEU6OPakVTnKVIGQqsbZm3fp\nlIQSkPB2iS75kWyGhAejx8bBMjnnXBye0tZbkg3MzAIVG9+ysfTiy0WAuLoh\nSmRbouOEiXmDRoUpwxm9/Mlw7TItV75l0+cyORH8tfQM02oveiSsjgklfWJx\nN3872aYLOaVPUoYCon4yU4pwwD/r9hvyA05ErDeXTZJXZ67Err8MKmqmi97l\nU8FhMuprFmQ4mruMYEzlrPR4nUcXc/b9mU1HsnbGqJ2fO4BAfffJOdWcB9Go\nVWtuI1wn267G3V/wApMp7zvtY44WABzIqZRPzGg8DSZ5u0rYJZEEpEpJN9fG\nYhhO\r\n=2O62\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.0": {"name": "postcss-selector-parser", "version": "6.0.0", "dependencies": {"uniq": "^1.0.1", "cssesc": "^3.0.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^1.2.1", "nyc": "^13.3.0", "glob": "^7.1.3", "eslint": "^5.14.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.14", "minimist": "^1.2.0", "coveralls": "^3.0.3", "@babel/cli": "^7.2.3", "@babel/core": "^7.3.3", "babel-eslint": "^10.0.1", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.3.1", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.3"}, "dist": {"shasum": "5cf920dae70589adf1aba7ec72e39cc882e5cf40", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.0.tgz", "fileCount": 58, "integrity": "sha512-E4EHwWgh5NIh/F44hYfQHR1jwejCza1ktpeWTetDtc71hxdDmWPjfSs28/58DBDIKxz5Dxlw8oW6am2ph/OCkg==", "signatures": [{"sig": "MEUCIFECCQDH6Hdb7FOgHkwqkvdUV6XnXIdjnOAnWMIN9GBrAiEA3r5nFT9TkK/P+jqTDbqF1xUqqMv4xzAElQiT0RP+w+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdClBCRA9TVsSAnZWagAA9lwP/RTbEqSv5WmIjdkHByFG\nNxpTK1o/kYZicLX+giTxRdMcx06FPlvZMjbzC43QEa7dRIkVGol9ZLZ0IrE3\nNjm0gVnFU+8E5qOLJti/ny7RhzCzRelh5+c+73uTx1vzxgsbMvPb97tqOuyy\nF9oKMxNHmaepvFuykpc6/n0RjSQjUTe6rxgxX5QjpCvu3hG9Yl3trFizrEQB\npfnr8eyblWu2TPJmNeXqejw04QZAzjnsNyK4a5iGB/Kfjk4Wa7XFVO5CnLf8\nyg1j5egFrnf1b2s1xt1ReuKFLGwL/gO/4J2vlX/OUc13BNJhVcefqzptR4bk\nfXfXHQXS+NPaIHsqHpG6gs/7Rrc7NUn/1DuFko17kuB/Ic9LW04o2tnYAv6H\nMlv45niPi4U6GeYswswz2iIEiu0Uq6KE8Vvo3NWCOq59B8bLX8UIRq+uSf1e\nwMrzKzzSFbZjDCXxa7q37bd4aj4mOGGUdseqXR1xVvC1+mAUc70dY7wJk8Nz\ntUXM8CEhKi3VvzDrT4M8HyxKmLT0LGp6ovSm47peOW8phwV40kEEDd94FjGt\nrzX3zlhYtbw5PVdlgb6fhpUQK/SPTDzD7f+L6iOhz/oTayLHketZurnAuoHJ\n9uIYjNC+RUrFkfL49W1btNtloilXoNICogjV46r0tlG+df5Z/cBtAJVfoAqN\nwSpc\r\n=cr33\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.1": {"name": "postcss-selector-parser", "version": "6.0.1", "dependencies": {"uniq": "^1.0.1", "cssesc": "^3.0.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^1.3.1", "nyc": "^13.3.0", "glob": "^7.1.3", "eslint": "^5.15.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.14", "minimist": "^1.2.0", "coveralls": "^3.0.3", "@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "babel-eslint": "^10.0.1", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.3.4", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.4"}, "dist": {"shasum": "7ee5476d0fc52f27b25122a361f160b9cf39b0ff", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.1.tgz", "fileCount": 58, "integrity": "sha512-bg46FvHx2lSHput5J4xCiCHrRxjza73jceSW8JcOVNzCEnlhuZF7pLa7K0KpNt8whL7C8V5wdb0bSrCRg0w13g==", "signatures": [{"sig": "MEUCIBywtt4fOLTVuIaZrQoP1AlxfwKkeuB4G1TLkNohGzSlAiEA667YaA7BAZDZrVvRam5VuoKJkiP838z+qIdRUvHQGsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcft1uCRA9TVsSAnZWagAAKCkP/0WinUJArSIQQvxb6CZ3\n+965fXUR9Ljds2CMVvItbcBoeftT3VZku1CZJ3W+JZzPKEsWRnJMiB5XYba8\nOiQdcLApg/V9gwIM8K6mh65zL81FE4fFwFclx2wa0jmo9mCV7tyiISKEpBea\nxJ/gGfko/vM4HStqB4fzhBdTEToZ0+egIR8YguCYPXZA8pYv8p3RwDED5yVE\n7zFSQCjhDCN9Uoz/EseZPQNNP49lNhd6HKyCkv1xaaBATvtZevsMnT7kxdn5\n+n8CrLdegqs6CUbWKYFblgo1GFfHz94vOd2kvICxnPfSpRsFSc05ekMDOCBg\nKNFBtU4HNKzUGdGcmjZNRDaSewf68SqUpIyD8HXuVydxx0mlFzOAnETaklEk\ns9VEFgYGxCg9BaNCfW25vUYoVlGy99wCOicfcKrl1AnlIrup5rchPfrb+Kn4\nbQMOAvPNQwiQfkLVrlltZxw8X/YW/3YzvxXNJXAHs6pG5wdyPzM0VkBGVpqX\n6NEuz4Wy8KIIz/kO5Y1yt26ddGeysKZCxSxFZbQUBFrwhJtYbOmDWjkibEgH\nBrcp4ACcwYFCK9IPwN3drZxrTdinZSQZcFAjU0Oibn6KhwRU71nYnkGgfMQk\nmmS3c91i5ADenCGcxru7tSvooeAaPO0TkrPiGy4/l15np6GsMQM6Yfk+/X/E\neyY8\r\n=41u8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.2": {"name": "postcss-selector-parser", "version": "6.0.2", "dependencies": {"uniq": "^1.0.1", "cssesc": "^3.0.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^1.3.1", "nyc": "^13.3.0", "glob": "^7.1.3", "eslint": "^5.15.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.14", "minimist": "^1.2.0", "coveralls": "^3.0.3", "@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "babel-eslint": "^10.0.1", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.3.4", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.4"}, "dist": {"shasum": "934cf799d016c83411859e09dcecade01286ec5c", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.2.tgz", "fileCount": 58, "integrity": "sha512-36P2QR59jDTOAiIkqEprfJDsoNrvwFei3eCqKd1Y0tUsBimsq39BLp7RD+JWny3WgB1zGhJX8XVePwm9k4wdBg==", "signatures": [{"sig": "MEUCIHLPtEieV2i6cqOlmYALApZuDSI4sNZLZURq2v/qGcyzAiEAxZrG40BFkqhCEf+W/HCuxyasGy/hErFNvGWhmIyLdW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgAfiCRA9TVsSAnZWagAAZz8P/1Q4NvFzfpKIjxvWGdmu\ne2uSqYeGPPmaqXXyK7bP7uVBHTMSxbejRjN6VulFVm1HEAhBAUL/58AmK5e9\nsvpLPnl0WdJnoayVXSz+iuSedzRPFCpJxKCjFe8cTs4IxlQH9SWp7DCxgBbv\n82SKNplXNpR532ZWhPcTM/5rrrZwUA9ah5g8pRi1UfMFRJzvDK8FqcJOzVT/\neN0ASe4uh6Ki73eqW9Tcd+pjKX24pqTsUqTjx7xniwEiw3bZ495bPVrpAwzg\ntSTZpNwD5lCICxJIdgPlWGhA6DkEkNpERzr1gJ9y7s7q7lvrOPZVM6DT4vXY\ntz+vQGr/v429ypCtpZ9uAAi3cjrXWylZ3YHoo+jhGjBFkKrb4yXZsV9k6TeU\nb/H1aO4ezU+EwxpngoSvTL3XVeRpQZ8KQ7xWr3r+hmYhRghwe8ihHzepL2Uz\nvJt52S+k4iRyfGf4Ls7FkfPsurbXN6NAWIA6bMG2RHG22ICwUlsQ/EiJP7cX\n0vBJa5nDhWf9iugaCohoj5j/fVw9sf4ms8oBW38iJefwf9yf/EgTqQWkeGKJ\nlkHSGKNrgRTI3xbHHp6+qh5C98uo1el8Wu3iaA18pji/YYCKkVy8rolyQzlP\nigD8LTH8jgyn/6POvJE4UEJdv3oNm6Qq2wE2RQKE6AKSli+CEM+PUTbO9BNC\n8/Go\r\n=0DJR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "3.1.2": {"name": "postcss-selector-parser", "version": "3.1.2", "dependencies": {"uniq": "^1.0.1", "dot-prop": "^5.2.0", "indexes-of": "^1.0.1"}, "devDependencies": {"ava": "^0.20.0", "nyc": "^10.0.0", "glob": "^7.0.3", "eslint": "^3.0.0", "del-cli": "^0.2.0", "postcss": "^6.0.6", "minimist": "^1.2.0", "babel-cli": "^6.4.0", "coveralls": "^2.11.6", "babel-core": "^6.4.0", "babel-register": "^6.9.0", "babel-preset-es2015": "^6.3.13", "eslint-plugin-babel": "^3.3.0", "babel-preset-stage-0": "^6.3.13", "eslint-plugin-import": "^1.10.2", "eslint-config-cssnano": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.0", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "b310f5c4c0fdaf76f94902bbaa30db6aa84f5270", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-3.1.2.tgz", "fileCount": 31, "integrity": "sha512-h7fJ/5uWuRVyOtkO45pnt1Ih40CEleeyCHzipqAZO2e5H20g25Y48uYnFUiShvY4rZWNJ/Bib/KVPmanaCtOhA==", "signatures": [{"sig": "MEUCIQCnc+tkpM2oa+JxgVAZmLYbQL3bbyGUdkriI3/RhkNIgAIgDNrFEALwV15ELCblU5s+/lP9Ya/0EPEM7E08qIiI2uo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRqAgCRA9TVsSAnZWagAAWycQAJcODAx1SSXgmSjaGSAk\n4mdkjRAWs5OWSd3jfjQ11mKQ9OVE/ag82GjIQjV+TpaZHVp9u7b4IpJ1DcSW\nC7YkrEUoHPE54Zk4NwEPpa1YNCjJ6oduJqWj8s2hfbe3FZVJ7KyNsgJUNfsG\n0ffl2NoQ7NM5eGm7ctrmmSzi3oSundfRUmy91bY+GmSb92UhVNIiTkbsF1hW\nvRMG2VdXEE4jq4rQdReYAESkjZDL/HEnxrW/c0h614Hnob6fxdJfXQCUXfJv\nlvfOrZhQmfjuwx/o9w50SFl49TcudDOq8PEg4c0UEeMt2F/4E64KSQ2JtMvL\nOmq/oT0hTCgZxDOHqslQFkLH4hxdC/cPhn1vNPlcQKGse5xS3++Sf9HQPJjS\nEEOAk2cYnmC1wESmWeRQ21d2ggw9FV3o0q8HqkBkl0M9jrNLh/APwj95T78t\nIfDIwTHL5GPzR6TigtF0837xZ7+i9kUGMHbVS/XBb2vn2yffWC7tHnNpF3gd\naxbkD0s94pt4pTsVnFCUYWSkhwK+obBQGjHES16/7dSHcnWNLLZiJGJFHkQl\nExRubQiyk+7HrTn+zF23Dstjbcb0SZW48EYVaglkjzfDIDUwPdDZ87PpqjjC\nKCtvdCCMGY5PDkafnFofOVg8FEyUygNPl/D6M0r8tGs5AIdro3GKQJ1mkoTR\nJrHC\r\n=uXOy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "6.0.3": {"name": "postcss-selector-parser", "version": "6.0.3", "dependencies": {"uniq": "^1.0.1", "cssesc": "^3.0.0", "indexes-of": "^1.0.1", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^1.3.1", "nyc": "^13.3.0", "glob": "^7.1.3", "eslint": "^5.15.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.14", "minimist": "^1.2.0", "coveralls": "^3.0.3", "@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "babel-eslint": "^10.0.1", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.3.4", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.4"}, "dist": {"shasum": "766d77728728817cc140fa1ac6da5e77f9fada98", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.3.tgz", "fileCount": 58, "integrity": "sha512-0ClFaY4X1ra21LRqbW6y3rUbWcxnSVkDFG57R7Nxus9J9myPFlv+jYDMohzpkBx0RrjjiqjtycpchQ+PLGmZ9w==", "signatures": [{"sig": "MEUCICQ3bxXixjLDYa00ZebxmqOq5V+o52Df/btHsIhKZYEAAiEAvSEGGQ6H64DBoIdCFPvHFJFe2XeDMJNf9FqIAo3+51w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaLNtCRA9TVsSAnZWagAALAEP/1XhajCH+QvOUo7qRtTM\n5peZ75gIvyc3kWRHYumAJH7qQRoJUnsYlyehp0zy8VjtKi9KWFI8UUvzXPqx\nfoAKGam4VPb8ciGJfrECTNg+3caqnXBgFEd4z2hY3Yu/077S189P3yKzpJDz\n4e5l8l74VKI3DS8aaRJzpGo/3TrEtsd6EzERap8WYIf5Sk3ZQom6NxwW8nfo\n5Qn7NE+MvzhZiViFtYCVlLTidm56oSSRA5/Or9O3yw1/oUEylQdu/EMIVR6S\njS65YY964UD6p0AJZsutVSBa8mjeSoAMqVViFw77kIDeMuCmwlLvtq6P0mKg\nz3Z+xY6Oa266asupnhF7PnCQnFluewcgL0ppkcvc8rB8t8xkKzkFgP419JpW\nkzLo5L3Cekb8X6rOkm5xxl49mLmnHse2rOqWeJAf9YfPMSeX4a0JhlFnMQke\nNTxgNyuiF3O6VmTvp2Q5L3hw5m7HyktxjD4FQWAv7qaOu1ykF/ZMQL1fpPBr\njs164GW+kYRE4o4RwuTcoqowaUnda/F+TTbsk64fZ8HxjYV9wpeqeA6hLYG0\ntPkKOKfNa3GR6Z6dQaZkshihr11lLYB8oqwvBrsZCUxL88cbs+MQa2aC86lQ\nWVjYhY16g647o/WH9fFDQYr1vRpucm5phn2liDboPUMWh+23+4SLWFBAfiLQ\nqrzm\r\n=Jnbv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.4": {"name": "postcss-selector-parser", "version": "6.0.4", "dependencies": {"uniq": "^1.0.1", "cssesc": "^3.0.0", "indexes-of": "^1.0.1", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^1.3.1", "nyc": "^13.3.0", "glob": "^7.1.3", "eslint": "^5.15.1", "semver": "^5.6.0", "del-cli": "^1.1.0", "postcss": "^7.0.14", "minimist": "^1.2.0", "coveralls": "^3.0.3", "@babel/cli": "^7.2.3", "typescript": "^4.0.3", "@babel/core": "^7.3.4", "babel-eslint": "^10.0.1", "@babel/register": "^7.0.0", "@babel/preset-env": "^7.3.4", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.16.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.3.4"}, "dist": {"shasum": "56075a1380a04604c38b063ea7767a129af5c2b3", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.4.tgz", "fileCount": 58, "integrity": "sha512-gjMeXBempyInaBqpp8gODmwZ52WaYsVOsfr4L4lDQ7n3ncD6mEyySiDtgzCT+NYC0mmeOLvtsF8iaEf0YT6dBw==", "signatures": [{"sig": "MEUCIQDIYJMOKFIJL8e+GHSRbwYityNccyqlLZmGZAk/ZOZLaAIgXUqkt/nrY7LAVM8YQ48xIREn2MmHPdH+hqEF9V+f13A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbgoiCRA9TVsSAnZWagAAzgcP/3+ddrEhlQ826DUZEKeu\nLWlTBx5DexdF0RhcHz5vGFgX5l7HlO4UvQNVZTQXSYpZDD1w2+DoUADKuYBj\nZA7Y9se+qS7cNvOJbGcUsp0nOhTxRojXRlAKmhNM4++vFlyZ5RVmFYsdT6rm\nqIsKh8cy3RDSK2BI13WUcm/oBmZN4KPFxrMov6/TbQ76A19RljklQygGU31p\nI1Q7Lzs1d9SiRPYLtCr3hin1ounSckAvtd7di7/zfmOmzBksF8wWKGE5ZpGn\nKKlH9nc8Hk2O7nRGx9MqHSp9Y/cyyqtOOauag0J4D2EAwIS6K0iZBU1F7uqT\nu/LXRgYCLzEO/yd/UPag15inJk01EFQEKOB4FpX6cmi2FSPoF77v3lqXoORl\nb88NuMAF741LMjEjKrxdUF2PdnCe+l/YTzDNMU3nVYPfwDnH/86/FIkR799w\nQYwXugZZ0y/VOIpKjMsa+7l+vzopgLqNmDeLW/bS54mab4EVtpHrrQgtd4nc\nKj+3V8eiX9etP1+ovD9thpjrewLExXdVuCycRtQfDDwIMUO2G0ydXpNC9fmf\nz+/LhfsP1qS5oHvM37V5G6g9lF98syG2W7IXX7K3Dttir/nXmEK9BzJWFZA9\nFDdwyGe8G/MDIj6jvXGQ+gT5wqUBJjgRdTwl9lWFw0wMzYLQ+mPNYueqg9IU\nBWF1\r\n=A4n1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.5": {"name": "postcss-selector-parser", "version": "6.0.5", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "042d74e137db83e6f294712096cb413f5aa612c4", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.5.tgz", "fileCount": 59, "integrity": "sha512-aFYPoYmXbZ1V6HZaSvat08M97A8HqO6Pjz+PiNpw/DhuRrC72XWAdp3hL6wusDCN31sSmcZyMGa2hZEuX+Xfhg==", "signatures": [{"sig": "MEUCIQDupjgJrVlT7QbMF1PbO4r4cM9boohH25LaI9VvMELIkgIgTHQkk+dFakQ/O9Db6KboEQfwZCEvUVgg52Ho7wk3jWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfXZfCRA9TVsSAnZWagAAg04P/2Yt50yveE6l+8WXI+RD\nhWAegbwu8N5DTYR4ruFTuZY2r/pjvas+2yYm7coDKsBCsNGqg05fczxIhpma\n1KzuRTxjyEjKDRdXILNyquR155A1Hjq2ZSvnV7qlj5lqZ+rSHuzaWqZZ4hML\nwfKfqONaPGo/dZqS/zxt0FUcxA6AV4Cf+EVodxXyo+Z1tUA2Q6vk4EcYkFrO\ni0i1yvi43+J0/+Q+Sx7AJIcmY6qKiFFIKlhT50aLLj5sFDHI4yPCia2dQeUA\nKSP6FW/E5PtwcbTvtIkh6rjxGYsBBhcoAgKbCz9qnc+2/Dxdsj0XvC52RTRX\nOTC8CnIYWOjITzdUD4VvJm0YAG8TRq+zOHRIqOvjRvZARKQ9bXONgenkmY1Q\nmezPjwmnkSKm4T/sxyueiwztVh5DBWhbaBmt7fHRsL30MxoqD/P1EY5jYZYs\nvwguWHRicUt+WqhlRYEOKOsgYPJWvyKcpGmPGSJ92wCr7o98AZL2F3q4B7fA\ngQtw3Aa78KW7iTupLjUOXxrVtGIU0jyproehcKHnaRoOTrdAYSsAyBtgcAd3\nVpH2vGBZQ6AEiK/Kn6wbg1/ZQuXy+JbZhirYlypfkiRnrIzyLbjOLg05ob0E\nre2oQChTH3p4VuzqTXuoXopNyDSW8g6ukbISLrR8sFeHOrfE60qeSN7zKUes\nM6jp\r\n=erbG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.6": {"name": "postcss-selector-parser", "version": "6.0.6", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "2c5bba8174ac2f6981ab631a42ab0ee54af332ea", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.6.tgz", "fileCount": 59, "integrity": "sha512-9LXrvaaX3+mcv5xkg5kFwqSzSH1JIObIx51PrndZwlmznwXRfxMddDvo9gve3gVR8ZTKgoFDdWkbRFmEhT4PMg==", "signatures": [{"sig": "MEQCIA0S4VX9QTl8MSIau81RaiF3a5yGxh2Q1pIttD9uQ0i5AiA23dnYsjyBdlI/3mpvQOEoyA5EoT2WY6ovZ1ym5fGQBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmrnlCRA9TVsSAnZWagAAgRwQAJs1a/gUnszCLpakAA3x\nD7CP0svnt85Rj7xpTCWKqE5tJf5ol/K5w8rSbAwg1beJ/M+vA6xpxJHQz+FW\nGXkehVC1b11gG9JBVPwTFibXWBF6utF7WBNKTrD6oEXTgy4wqCsrhb3YDG03\nB2POL5Oc8RH8IZRdinhIg/onv50RZNQkraUmG9qH0ANTQlN+KalIauo5b/Nd\neILDGrJiiqjGOCS/nSXNpxyf0OAFR+zMDPOQaddjt2aTArCu5Hl3+yq900t5\n70gdZ5nhnMe22yiQKcj1oU7eqfVAI8AzogAsWNi4tvFCdni/OnpJr+ZGZU3I\n80xAv5bL90uO+sni1bQbjvilSppYoh8WcX3qK7PN4PmxU2KyztOqeJJVyLE2\n1uJoDFqD5C/q8+oD6AMquN3ybwOPE5BGd2oDX3yuJjU4zKZaX+RvhQRc6mrU\nUydu0tk1ARiCjmNNCL/1PYN/NJKKaGR+rwtNRK/oIkEispLLrRWLC/dNK81h\nMDdBAxEW3syZXYWWdnnb3y7nrwl5K0SOqYWDwFcZEna1vWMTVJTXBx9qp8ru\n33lrRbIH3qLBcgiAkb+IBuNpJOpCztkWYOHGeMiEqsD1m6YXT/DkP9PZ+SFW\n6ijXxfwy1BGyQ6/Y5YlDxrK2N9CXjag3LkqqqeV7EFj7BIIcMntieeR0JDkS\noGYc\r\n=Zhjw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.7": {"name": "postcss-selector-parser", "version": "6.0.7", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "48404830a635113a71fd79397de8209ed05a66fc", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.7.tgz", "fileCount": 59, "integrity": "sha512-U+b/Deoi4I/UmE6KOVPpnhS7I7AYdKbhGcat+qTQ27gycvaACvNEw11ba6RrkwVmDVRW7sigWgLj4/KbbJjeDA==", "signatures": [{"sig": "MEUCICz+5wltL/H/N8RoeSn3ZtEFp99nA5F1byz+fvA6Ix0hAiEA4NlPV8VeqKr/QJ/Irpa/A7Et+iChY9fL2bv7CETFxYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsfZFCRA9TVsSAnZWagAAbiMQAJgnHCM4d3JbZfYdTe7b\ntDSBWk4MfPS48E5GRVCAS3V2ZgDU97uC2uRHPjTdH3jmtFERZsNR/OetRwsh\nu6+sz8ArR7r2bIBkWn1CpHpSpcJgUAXTPPutBBmoXAkIlFaaCOpVAErr4vna\noXYjvAK7cHpYzlIRhy78aCBt3/Fy5Kv17moAUh0/lUQRLng2nytp/Gk7+4Jb\np3q7nw9J1Pa2u2NqSYjGljuuUtgOrSzDx2Jg11q13Hj6sR+xzExPhqq0qXee\neh9xU70pGafzp8eClCPrWQfWIl8+rYddN+zqBTo3T+Rgw2NX2s7kmNu3JRh2\nwA7+NKG2ebY1lOjNQOBvWCj8lLL2A1sJvR1ZojGS/EU+cjll2znjFWG+Bxo+\nzedrxpDM26UhEagd52JbOUtS69zRgmnIQ432TcvkQHVAQxZ6YSQMoTzrZEgX\nd9RyvvM5NmQiogyAknnIlLW8Fr1mKaKTzogJ2CQKyWW/73FgX3WxEV5yJX73\noVnLaHA7hGmQP9V+EHJLLSroqrafHigqSzHq1fdIvzdQwwrMb0hfc7fp1Vtp\niQ3G0IIh8fKv+orjVx3x95ZO5TRYoqobvtQyqtV/xV6vwt0sk1ZgnQMX+cqC\nAQMQsSfP3ymIqK54hXQeO+2wZoSYu53ApRn/6uHJacbsjNCqP7ITIYMrqGpI\nm81x\r\n=hD4a\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.8": {"name": "postcss-selector-parser", "version": "6.0.8", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "f023ed7a9ea736cd7ef70342996e8e78645a7914", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.8.tgz", "fileCount": 36, "integrity": "sha512-D5PG53d209Z1Uhcc0qAZ5U3t5HagH3cxu+WLZ22jt3gLUpXM4eXXfiO14jiDWST3NNooX/E8wISfOhZ9eIjGTQ==", "signatures": [{"sig": "MEUCIBrGzHDErcH+qUbYHmDns1WPj5tS3Ux0dIXPeDiSXmh1AiEA0qHLF3RqtlKohh380Z0zqKlkLyjiCN3vqlPKegLP9bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwwmUCRA9TVsSAnZWagAA40EP/3sQBRvVfSkZ1NLrH+Yc\nzu9M0mbCNaM7cFLRRkSscThrYqQTBD3PTmE9EjU3M8vqkDi2Ve1YfZuli8Ud\ne58dTey/V9Objk0DYSQ1JX1GXNQIKbvgUlQ0m17UH/y7Vzyqdm9iC+0PfsmH\nTmM04jEGMEqhEKqXTbnn1brZ8v2/3kJSKqxh+Rb/k/bMYrqNS8kZfHD1vc4u\ngDyMzIreJPorwVAB6HVNFBd8RBs+SreF++VuKm/zC5FIxvQKrXmUuudzgDtt\nqsg/WoUIbvetHZdogeBww4edWymSv4cyBrtg4ylvjieqzxnISFIBzTgHYFjh\n5/85GZNQrfbWe8KoSZw9Lu3oIseP2hIB8GMIwbS7R0R8X8Uoho8tvP+eDZDZ\nQAADSwrrqHuNWJ02xKzTh9tsBApTFnzDnSpfPkgP3c8RYSC0DyVSCtKSbgFW\nZTvRq9JbuxOVHS41PMB9rn7RP2vpnfpUnF4dzVV2otx3wyT6KZXseWsuY0Fm\nxujPrPLzGc9BhHoCmrRSyULg+S+Mu/d3mSM5OwCxLcvqJDZUqdXZd/uXSodA\noBVhdCh18Lyg54AMmICWVW5k7sQr+lx/B84myxLm8pOZex+LQ1s75aORlBfA\n4YeURFn3oXKU/yVjI0TNWZ7zBEQqI68v5q5uEuLwAvzB2LELVD4VUGyR+MVi\n0ERe\r\n=bU9/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.9": {"name": "postcss-selector-parser", "version": "6.0.9", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "ee71c3b9ff63d9cd130838876c13a2ec1a992b2f", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.9.tgz", "fileCount": 36, "integrity": "sha512-UO3SgnZOVTwu4kyLR22UQ1xZh086RyNZppb7lLAKBFK8a32ttG5i87Y/P3+2bRSjZNyJ1B7hfFNo273tKe9YxQ==", "signatures": [{"sig": "MEUCICENvtPfprAcw7QOkllcPpUmyswm7aKnjIMmHBcF1JHVAiEA2ellFfdBV3GbBwuvIJaLrULT/ieZNs2eQKXtCrYq5Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6VFtCRA9TVsSAnZWagAAg6wP/3ZIFHtWfb4FLqlt4foD\nrxa+N6FzQsXzj1Qga9GeKX4efzNQRg+CICaAOdPL/xBOJ2lSxyFHBLnEaNB6\n/65Wn7nLTKk0Q4S31JLKHHwIfIZe74HNMV7lcyLx2Z5Sd69Kq3iG5dDuN+gl\nP9cfIbd09PVAvv+2tqXZ1qpSTUbdB4trpIhyJnYEvw4gzk1whOliX/AwJHO2\nSM00N3ifyystwsAH0orTGMp9mENDd7ctAo2fGTkAkjSqo5JZr05fGyvNfbQE\n3LhNQg/1djZoOFr7DeZVfZip6DqcC+SIplvmTk4t+KTLoY1CPknjZ9pv93kj\nyFB+ioiVIZUmo8X1FBkOmjvwUb12i8NjOJt0+7uuBXYdeNGgr5Pa1ueX61BL\nW1EiQA3Jc376pfS2wMu6d1+lHFmUbS3dGQyRrdmOjJxP9qy+awgTP6buzClj\nlI2s8KbIQZkDpPYmXicWHLf3vfeFRCV1eMaaDUsAEPyu7SGfZqDOn0B7iRdD\nhkdS97KlthiT7zNev9FwdqnrZw/Je5vtvjj/d/O9qSpZy1lwkxmysoBRzfJS\nlo/7o4Rn7FHBP/WClRDdAjiXvwE32aF4hp28ezxJaO+qrY1I63ve4qRMrj+Q\ngaZTcCqRHH+FMZBNYD3IAfXpR4NQ/r24TqVYGCCpqjjie8BgMq72tIRK6uT9\nXCvy\r\n=cUfo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.10": {"name": "postcss-selector-parser", "version": "6.0.10", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "79b61e2c0d1bfc2602d549e11d0876256f8df88d", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.10.tgz", "fileCount": 36, "integrity": "sha512-IQ7TZdoaqbT+LCpShg46jnZVlhWD2w6iQYAcYXfHARZ7X1t/UGhhceQDs5X0cGqKvYlHNOuv7Oa1xmb0oQuA3w==", "signatures": [{"sig": "MEUCIGh+gn3mGA/8QC1YdFx5UDk+MJFMF6psn1J7it6g8rYPAiEAnY4tZ2vFSguDcAW/8v3WrPrn7VNP/MR4VkttRrNRDYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRMLhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQrA/6A3UC5r/l7nUAIyFnZN9MMHKscCanHON3GCz/7nxtSnoRq09T\r\n/Km4wRkbJP7IylTs2jFbJk5spSP6aqj1ftg9bl5tzdkHHBW4+C+MKghjckfP\r\nS0BFcOQv2QPi/EubqWq14q05XjOTfH6dnjvnFP18InjpXjwo28wEY8CgKm8j\r\nk2ES27TW0xnQnUXAD+uJHiWcI5x/P94NhOqCdGtrWN6qexXZE8Bh5H05jYAP\r\n1L/iXeGayOhHSFDS+cQ5CCM3BkiVl8W8JeBJzbawPcNTy0ETZzf7YVFIAHfG\r\nZekibEHRt/mGOvrxPeZH9h9so6awLfFg1r0pePmCjKEWc+kVy7d9NhQ1DvEc\r\n65tpp3dHXOjtQ4pqMfqLDbwg55+OhL1jjvQL1/rxErABH7w/3MiafSvOiBZg\r\nohk6HwxYGcQm36Tf3u066uwIjkQnuChL9UBYiie40XZssZDifGUQyKail74T\r\nq1RbVgxzHZ73iHudkMETYqMuxbT1KjzqUVjwd/86Ep4QzauMm/vdfx8XXxZK\r\n3wdkymVG6cxSU9hqDDiur425gs1ri7pf9C3q81PAgKS3hqAFUxQiPpT7/OvP\r\nWtLgmjb0Me5nkMcni4VLd8tHKm8CDtWuto8vANi28MgxhMdd6/09+R6N96Jo\r\n0LGU9HDa2YFasDzVZ0OUqIKkJEnTyrgmyog=\r\n=kecv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.11": {"name": "postcss-selector-parser", "version": "6.0.11", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^3.12.1", "nyc": "^15.1.0", "glob": "^7.1.6", "eslint": "^7.9.0", "semver": "^7.3.2", "del-cli": "^3.0.1", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.22.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "2e41dc39b7ad74046e1615185185cd0b17d0c8dc", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.11.tgz", "fileCount": 36, "integrity": "sha512-zbARubNdogI9j7WY4nQJBiNqQf3sLS3wCP4WfOidu+p28LofJqDH1tcXypGrcmMHhDk2t9wGhCsYe/+szLTy1g==", "signatures": [{"sig": "MEUCIGU2toiBqooEc/lpg1UWRBRRWfM1qcpvlYFKh6MqqyjUAiEAsJ+RmUQ2tdDXIMVonQXYMvDwD60G+rimWjy6FdsaUTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjemp9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpoQ//WUlavHovUBjuB5g2BVKiAwX56vC8PSZwBWiEopTcd7p31cJe\r\nyVfySNViCedsxWbwYHhW+J85kvTTC6p3j2Ioh1brzgVgbfZGj9/wgb9aOIom\r\nReClkMvcEhLntnkg4GHpQ0RHh8qV7mfXGVE6W4hDYTwnhxPs0c4whPF8Zrk6\r\nNSdHtgvq83pgSeNFRBeWkQrrhsCzeaBHZcD3v7MYI1nEWsWRhmLsC6k01Pbh\r\ns1ubBJTYC6nOPV1QN+0k5jZI95MADf8QJNho5dQ0JF6J/Yolg0OXBS9qM1ou\r\nuXQyAYNHWP/m+wILAxopPyp2P5teTPmM4rKXi1GUjfO2KKIHzMdVafLKxOS4\r\nunlsIan58CMRf7PZxE9+w3XI5NINdnBy0+hAzEKHTn4IX5m68hRm0kWSsbWW\r\nH6HPoJ34fossvmJDdLbYh7/blM/Fpg8x/cmJ8WBFQbQPgmMAh5Vn+L71bP37\r\nsPHaSs2Eh4y3fMGHoeLjzwSZYW54M3vxbuQ3bqSBkltrbySc4ROVzzYHX9Zj\r\nQ/XWL/Sx2GKjEMVr3i7a3j0tAxPlrUZpCQTH2jp16LWRsDlOjsxW8/9XLUZB\r\noWknjO+o78EYA2Fsy2LBWPSFmklDtQhVOc4kG3uciQh5TDt8OtEfOzm3udBd\r\nd/SDJN+ap5ZdmBIURhUb4vx7opN480FYEE0=\r\n=dJnE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.12": {"name": "postcss-selector-parser", "version": "6.0.12", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "2efae5ffab3c8bfb2b7fbf0c426e3bca616c4abb", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.12.tgz", "fileCount": 36, "integrity": "sha512-NdxGCAZdRrwVI1sy59+Wzrh+pMMHxapGnpfenDVlMEXoOcvt4pGE0JLK9YY2F5dLxcFYA/YbVQKhcGU+FtSYQg==", "signatures": [{"sig": "MEUCIB+sXKG7VzqZgU8hLEavQ/Dgumviu03zNKaiMjsIaHbGAiEAnA26o5wOSm+krP3ZhsxD4XWMe/c4yAkKuFJ7bsRNxKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTR6WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoj2g/+I8krDd/HNCEH4TKrPhYftoV1iAlzLE0jyrm2WoLzHZEWVGlc\r\nJ1Mj4Be0hJ26QZ45VpJnhxiOl/YoAIBzoMsx9RJu/u8Lb56HKh2hRD5P3Shg\r\nYnI0VhMhTp46j5pfX/vk4yld2W/g66qMail7MXcWz5TCf3ERpDwDaJGXd/KL\r\n7Wm83jQJ3aY520CFAj0NJqzica6kpu3kafqebg/JocdhPlcimX97iFdKz1Qi\r\n0tDdchFIHeEy1N3wJZWN2l7a3TSnD9dnC/rKxhnqrS0qPXcwo+xFE2JSZkBK\r\nXwXhplBLONENlLq377tS5NyZNaR7gt8t0KmlaWgxih7XgAp4pNYv3MZ9nimQ\r\nuvW+4c0qgIj0oX+Hx8ZSAAGx8NIKaVhTDzS1ZaRG3wdmb7/p8ghvzFOrkh0A\r\n2diuLiiF6nGOSww5l+AG1msdsqvZXbOg+lrba73PdGYj4QElR9RcqEepykys\r\nQBeJIo0R4EXYxUPasGrrFAnKnrCF8ggrLVBdB2l5QHysl9ATksq0AzTPI+9J\r\nQZUIT3AFqtyOaeQ1EpJNPbbcPoM176FYGv91VdfwO4tgmtBkldpGk6UZh01h\r\n0ohDMMBNNv9QSAJYLhgj9bUBUYH6ecsG+yg3uokWAnK9oVs4PZjn9VW4lXjh\r\nCIFq72ctr4/TrOxnkq4YRT9sFmYtiKeMCpg=\r\n=ok6K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "6.0.13": {"name": "postcss-selector-parser", "version": "6.0.13", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.0.0", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "d05d8d76b1e8e173257ef9d60b706a8e5e99bf1b", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz", "fileCount": 36, "integrity": "sha512-EaV1Gl4mUEV4ddhDnv/xtj7sxwrwxdetHdWUGnT4VJQf+4d05v6lHYZr8N573k5Z0BViss7BDhfWtKS3+sfAqQ==", "signatures": [{"sig": "MEUCIQC4LOE07buNhHhohR3nzbcHy9AxE6ildFJoUy5I0+gDfgIgTc1v++DsrEOVb43FDjUKpxYgL7QttkLRM8sbpYNawSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185617}, "engines": {"node": ">=4"}}, "6.0.14": {"name": "postcss-selector-parser", "version": "6.0.14", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "9d45f1afbebedae6811a17f49d09754f2ad153b3", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.14.tgz", "fileCount": 36, "integrity": "sha512-65xXYsT40i9GyWzlHQ5ShZoK7JZdySeOozi/tz2EezDo6c04q6+ckYMeoY7idaie1qp2dT5KoYQ2yky6JuoHnA==", "signatures": [{"sig": "MEUCIQCFrdShnVaNPanmNC6DKOORrKH1wH+ciSvZJYnYOjYv3wIgaDZe7WkOcNw19Qld9pJKZYwAiqLkgY4Fv3DA4lUe2jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185674}, "engines": {"node": ">=4"}}, "6.0.15": {"name": "postcss-selector-parser", "version": "6.0.15", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "11cc2b21eebc0b99ea374ffb9887174855a01535", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.15.tgz", "fileCount": 36, "integrity": "sha512-rEYkQOMUCEMhsKbK66tbEU9QVIxbhN18YiniAwA7XQYTVBqrBy+P2p5JcdqsHgKM2zWylp8d7J6eszocfds5Sw==", "signatures": [{"sig": "MEUCIBOHORcnnaR9NwOMUX3K9JyLl6+XOar0mwjVZ9qunzddAiEA5ne0Wf3FsF07l+FKeqV7fKT9or7hlELGAFRTZFVHAZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185771}, "engines": {"node": ">=4"}}, "6.0.16": {"name": "postcss-selector-parser", "version": "6.0.16", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "3b88b9f5c5abd989ef4e2fc9ec8eedd34b20fb04", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz", "fileCount": 36, "integrity": "sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==", "signatures": [{"sig": "MEUCIQDRiGhKe/kQUSMnNGDarZ8DVW621sp2RPPQVi5n/VNfqAIgKkJSswZ87ATKDPs+cYMZdkhdWnMBUlB9PGtKEGyMUd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185968}, "engines": {"node": ">=4"}}, "6.1.0": {"name": "postcss-selector-parser", "version": "6.1.0", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "49694cb4e7c649299fea510a29fa6577104bcf53", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.0.tgz", "fileCount": 36, "integrity": "sha512-UMz42UD0UY0EApS0ZL9o1XnLhSTtvvvLe5Dc2H2O56fvRZi+KulDyf5ctDhhtYJBGKStV2FL1fy6253cmLgqVQ==", "signatures": [{"sig": "MEUCIF6TCKZbrvrRiz7zKnD7q7PzFIzM54jyjm2u+9UxMzvwAiEAr8ABa+sb/79eCOiPU86U4eZ6xY/RGe7RtRuF0PreblA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186207}, "engines": {"node": ">=4"}}, "6.1.1": {"name": "postcss-selector-parser", "version": "6.1.1", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "5be94b277b8955904476a2400260002ce6c56e38", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.1.tgz", "fileCount": 36, "integrity": "sha512-b4dlw/9V8A71rLIDsSwVmak9z2DuBUB7CA1/wSdelNEzqsjoSPeADTWNO09lpH49Diy3/JIZ2bSPB1dI3LJCHg==", "signatures": [{"sig": "MEQCIEUkv6o+nTJdCDwjYyz5WKcCh7PcYFXfVCwCU/JcXpgfAiBLhsf5NYXCm8d/0dyhoROF+wPEOVnz3FvCm4VFi5KJjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186290}, "engines": {"node": ">=4"}}, "6.1.2": {"name": "postcss-selector-parser", "version": "6.1.2", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "27ecb41fb0e3b6ba7a1ec84fff347f734c7929de", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "fileCount": 36, "integrity": "sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==", "signatures": [{"sig": "MEQCIBkefObkW1bq5fiOLUubVkU2II3fb/LKveKmsjXqb2RCAiB86Vpj7qg9+7LQnNa3UuNqIhCY1wmPTus/keJgHmfeSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186430}, "engines": {"node": ">=4"}}, "7.0.0": {"name": "postcss-selector-parser", "version": "7.0.0", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"ava": "^5.1.0", "nyc": "^15.1.0", "glob": "^8.0.3", "eslint": "^8.28.0", "semver": "^7.3.2", "del-cli": "^5.0.0", "postcss": "^8.4.31", "minimist": "^1.2.5", "coveralls": "^3.1.0", "@babel/cli": "^7.11.6", "typescript": "^4.0.3", "@babel/core": "^7.11.6", "@babel/register": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "eslint-plugin-import": "^2.26.0", "babel-plugin-add-module-exports": "^1.0.4", "@babel/plugin-proposal-class-properties": "^7.10.4"}, "dist": {"shasum": "41bd8b56f177c093ca49435f65731befe25d6b9c", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.0.0.tgz", "fileCount": 36, "integrity": "sha512-9RbEr1Y7FFfptd/1eEdntyjMwLeghW1bHX9GWjXo19vx4ytPQhANltvVxDggzJl7mnWM+dX28kb6cyS/4iQjlQ==", "signatures": [{"sig": "MEUCIC+4tBRO7xESShfQvk08G31x01hdRIGacUQTPF6XtC9kAiEA2GGTS/uzyOgQi2W5FzuZu5gdfeIJ4F4KVJPI9T2xLvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186560}, "engines": {"node": ">=4"}}, "7.1.0": {"name": "postcss-selector-parser", "version": "7.1.0", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/eslint-parser": "^7.11.5", "@babel/eslint-plugin": "^7.11.5", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/preset-env": "^7.11.5", "@babel/register": "^7.11.5", "ava": "^5.1.0", "babel-plugin-add-module-exports": "^1.0.4", "coveralls": "^3.1.0", "del-cli": "^5.0.0", "eslint": "^8.28.0", "eslint-plugin-import": "^2.26.0", "glob": "^8.0.3", "minimist": "^1.2.5", "nyc": "^15.1.0", "postcss": "^8.4.31", "semver": "^7.3.2", "typescript": "^4.0.3"}, "dist": {"integrity": "sha512-8sLjZwK0R+JlxlYcTuVnyT2v+htpdrjDOKuMcOVdYjt52Lh8hWRYpxBPoKx/Zg+bcjc3wx6fmQevMmUztS/ccA==", "shasum": "4d6af97eba65d73bc4d84bcb343e865d7dd16262", "tarball": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.1.0.tgz", "fileCount": 36, "unpackedSize": 187187, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBZSbKQXH9IRdnHstWBDbJEJRw/eSXvzihYdFI4xGL3PAiAUfI0JWyB1BDDRRaJp5WL6kHR3V0Zb/uiIZtbdSqmvFQ=="}]}, "engines": {"node": ">=4"}}}, "modified": "2025-02-07T14:48:51.797Z", "cachedAt": 1747660589209}