{"name": "@radix-ui/react-use-rect", "dist-tags": {"latest": "1.1.1", "next": "1.1.1-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-use-rect", "version": "0.0.1", "dependencies": {"@radix-ui/rect": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "faf1aa84416b06b41fd4b26208cd52c2ff4a7f36", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-kqFDh+aSLXNk58j1XrZJJTnR6127Iq6d3N1LtuVPLcluFrsbmXjU0otBctj60vBrvkxHKS/A7M5LTyuOQ5jdqg==", "signatures": [{"sig": "MEUCIGF8eG1daYXVGYQeTctn+HGCq9ztjPJ0CqAcEWCArYfKAiEAybHanJSDZXv8S1GciwD46PBj7lriLGeV1x4xx40a0/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VjCRA9TVsSAnZWagAAHhwQAIky0pE6lCazcKUpWCr5\njwxcem4DDQLIIKr4DWu0w9Tc1m5AGcPe0dvB8pph4w0l7icHGijl1i4s6oeb\nlvdHneC1YnKeO1ZWv/kXeAN3G5eupzZMbSMgCKHUxaXx6neX1v309OAqkRpG\nvDW4hQCX7TLrFVwulIV4rWbjFPuYeL4Ao2qw+IkTZpt7GeaBkg0c3j4mTb9B\nxfOzHymwnmLQpCBUXyPfA/QF48+ktfIFhNgik0e88umWdmrWo068R8rbDtAT\nclAUfbhJAMaAsRKzZ3ceJaXFf17geLLUe3TRyr1ci8XrymqqabG/ABB0ZxVm\nW88PvTTf9F2CkXS1krlolvfBCMAQiU/3/ocYDJXLar2JhzIiziz5Ys42gVj8\nzsePA5S+cN7P9l5T8r+bIspjhjk/tBN2iuPkLOhv1/qFGQGTcFzKJE9NjgJW\nmh5etazYhh7rbFFSEZesjwomoUFpW9CXg2N1+6NawVDNkGy1qwQ1YqS1cxbT\nQ0tcWESvOmmD5lyS1EbiWqGRXetUq8TeoqdKY5fxwUlQTjzqqCAvGriWVk6H\nxEILr4NxkDz7GvYih3lA9HqUndjHkVrXV0q5WL/VgwyEE8lmtbrdjbfJAiYO\nT/0OGAJAXkwIEP4Qhlwgsxa823zkH65psT/LkfZeKUjmgxoJUC0QeAIW5pse\nQeUv\r\n=8jK/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-use-rect", "version": "0.0.2", "dependencies": {"@radix-ui/rect": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0482d573b03db0b8dc0847a7c6964742140f32c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-fGH4i+vKQu33Badxtcb1W/lr3DARJBbXWpxWjJlWVHnac6j+OQJUihIm/ZYnEHhZ+xLWQKT9Tl/gt/TkYJEQuA==", "signatures": [{"sig": "MEQCID2WcjI2TqFoIL14PxmdrzRvp7AKm94uWNsyOYG4DS8aAiAPXYjFVU7tRPla3WsczDkoDj8MxiPf3YlRW5rAiiCGQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPOCRA9TVsSAnZWagAA4AcP/0ABOUopP6nhzHwg8fG/\nt24xnElobYWNJ8S4v8DTFkU8djxDrmLoiyy7+Fy8uDyiLrPE7Zw2KEH3L8R4\nIYTe/g7rLOhfTxmQw3p9WD1ITWDWJNDkuDyAtIf0z6vAdZDkPBOu1wKh2AsQ\ntNokBcpXHEvuQnIp4dKk4TSpGNY1vX75TIDtaDzmyfqLdayVXPVbb1Kf7GAH\n647nLhjg46b17HVvh1Prr0e5vqmyijdEPsM4PgQ+brV0u3/NmL9VjM6Zwpdi\ntFVjXiyO5QRyaP5GBX+d7BLtDP1tdBKIYOdShKc5uP0kyBwVOhPhlyqAb9q0\nEguAoT46ZaRjec6f8cA0WG3rqJQWx/y2SsSkApzdL+lC2jXKVQetmhUvV0t1\nk8PVLJxmUEy3TAdJd/MbBNJxZ2PFDhv39viDNPzXqx4VVXm9lML156Mg0n6J\nCiUoIiO35PDv/CBBU0Xg3yspt46SMhZmJfzA6O0WW6va0cCZLVRgUeqoZRJz\nsxqSGK38UCZqOmx1y4kp0UH0F8hbmFOm68VcNG/BAa1r9DEIr2WzXEuVKVYU\nCzSDsoOLukvDLI7WZLcsKCzlPtX8ZmcAtl6lzWUOhq8Mw/FO2cB3XtHOhrkG\nGqItJY1P1H8dSYKezrcQ8d4mrxfGN9pRB9nUWIvfkAB3jKiG8A5Og2bmhG4U\ne14u\r\n=JgHQ\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-use-rect", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "654ce5e1c2247e6324bd98a60f85804ec30f9b60", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-lZXJZjvUEnZO4jkKgMkFFu1XsPlrfB4xdqW4E4uS0EJmhfJk3QCtePFZ/NGcl+HDoAGmbXmBDRsBhRni6Ncmew==", "signatures": [{"sig": "MEUCIQCBjTd29aTUMhSU3KF4/2d60CCBQMTXePlwWEbpj4kxAwIgPlIkqVnw5SKbSlufJXQtDzcn3AYd7desDsEa9pvSk+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0hECRA9TVsSAnZWagAATcEP+gNZ6zGHikqSTEw/djTi\nx5/XK1oVDnRyvspaiGw2e8AUKqcpmM82D0cUBrttfoITs6/0ymGV9KTDFFvX\nQVqKvl02fHAOxoABxkdmTHih/oL6CXNd1qDTxSoFztkNUP+PTDALmZSxVDrR\nER0GybUqvmHDqwP3NrjLU8t2LJ7X1xKOCrG2HXor4uUym8pZGHhwQ+SEz8VN\nxVXQcBvHixciAs6lfgMb7rJS4cPTLI+l1Rq3ueYkQdUydiCRjND3WgNyQCJg\nurjCL4FnAEw3STrkJJSRk6PExWhIv5wwnd3XTj2Z3NXDIP+J/zMOPCPlWTjR\nyB8Ij5NBPuF5w2sZYQhAWRW/T1heIxRCjUErgk8WMZQ5q9iHXOqbK/ShPLLm\nWneIgAq8FX4oI515IWRhIO5Q0MEz1Tmr7i2XuIcIKb51mKmY0iH9P8d/54pY\nNsg+ZvCpmE8eiRcAiX+jYUt4XYzr3LZi9epTR5hKSGqeWpx3qOmCIvnfEelE\ngHTrI83otSOP52o9juUHpOmGisbtuPXO+ebLSQqcihG1VwMp20uve6ncrIWL\nKqjdgTT4VdaaPYKTn0s+UFo2Y5ejy9ZlDdmjfCjqQS2Qfqvjwxxs7XAeCHH0\nPME+2QlJJenUoc6mpFMRR+wOjhBYaCw5s31oP0HDVySw1BXMnvZMcoFPJbz9\np1Lu\r\n=Shpr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-use-rect", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.3", "@radix-ui/react-use-layout-effect": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7fe1240f385694e5f5daf91f99d3e17fbae310be", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-DVQBSOh0dVzNAMwCaApmjC9DY2nACoOXICduwFY7qC9n8n87adcrkmqeeF9EgrbUe4Uf4At1+UbqjLJLo9fBTA==", "signatures": [{"sig": "MEQCIAX+ROwkbOUirs+jSg57UhCP4FwzF+gDekLfAUDLx1ndAiBybp+8/Za7mafnh5JzN/+TiIj/pUAkpqgYElloa9Gdjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IQCRA9TVsSAnZWagAAcoMP/1vOHDPRnU6IJZMTXoev\nwx/LLrP5PssQQioq/akqL4VY8wR3/tOMIZJ4wUMg/CkS+IqdMZ/IBwKNPviS\nGlLnyLfty8UBvfLHnRfHORT7I8HUkxituZSDeIlysRw9VugM5ZLu7c18FKwa\n9qS9iUF1qu/SoqLOmjpX4q3okVnlcftAWNMzC7uLQiz8lNk7vWjT1dPzmAaw\nqli/TDFkK5RNUH3jtc9H7YTcMSk2Utt+8jQatGNoumo1fp8gZtzNqyzCXfrQ\nvoXXX0DwLJhONDFNbgxgGtcy0W3IpqLF6XL4EElACMCnjweW4rq0/N1k2Meg\nozPubi19HU2XnBQvdED5lCvblZCh6r5zJHSDGOmgIWuLbouAetcSYmfzBNYO\nW3ie8g2ecr9dTVh2O5yBV5EHda/yPyK5bAMOPOaXieDPRUPDnfGfdPq75iAS\neaBw8h88eHs7XaW7ieq8Q86ZXYipILuQ5vwx84YRrZ1ctVs5DSAqzWpFnktf\np2mpoFx3Jgaw537wnL+bR85txfKaaoT3frbsNzzS2U3OghrsLYDgHK6z8VZk\nLO5PqqCZ4i9C30amis/mvBiidaaFizErEp2AeRF2+sq7vfRngulxNG1ERy+A\nNJVY6l/kRfa7b6rho2EISqLIggOA38dXxCJIknWNN6JHlS6AW/3ymhbUP83H\nOcyb\r\n=GPVn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-use-rect", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.4", "@radix-ui/react-use-layout-effect": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "caea2f7216211d0912dd9c6decfd030aea7f5d87", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-KrD1kXBjEYvD4rsngZeGt+qdU80mO2Q1Rsva2sl+6VvSwWcc23V4WpD+TI6/3biTEsCwWXR6mTKeMRVjd8vzXQ==", "signatures": [{"sig": "MEUCIFxxFGa+A5qfrLSAQC7xdG9Kp+07BlVtaBSBgD3LbkiJAiEA41POsfUuhkFeNVBhVEZG43Ft8OuuXctts6ujpz3nnfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wICRA9TVsSAnZWagAAVCgP/jk0KuKYMBjLB44VRxxg\neHdwWyUcVKDZP76bdWSU4QQss2fhwkz3QnNp1LVJKr3ZlHSV+92Jp55+KfDy\nIljDoDJSq46nLMtuhicKb8NssnrceotprOBE5f2WeThGo7TawRECc836uC6e\ns6kHwwmG+s3Xbgx8zWeGLod2gAsWZEBwIvZ8FDrEwYFzENF8uWn6XRQgCsWy\noUzKfC6fbQt6ejsEdf8wIFabNW+v4bNoy1sPGgLsNTJz0Y8156ETPs28brTu\nfX7QejjHwNi6UKUt6d5LdusNlu1qLAAzY2Dtc+h+weKKQKbjJEhqC6eYsSJs\nVrR3pt+UZzU6Afa77jWy4v2uV1AJfMhcrDA464EMUt5PxvccIeP+lbKx4RRg\n6U7bVtnijpAyV/XoaXJ88znepldfY5IbrF/XeYnnlMWE1ab44xHUHSfsZFaY\nZgsmj5bIYpW2cZKn6+Y33xVbi7AknqqLVTd+2P+TLyoxSC5j9reuWxrg8SSE\n4AmA8SjpjIEAczV4y0MjGPx6wELrHAfc0T6Es/uofzbEqcySceri/bJsmaXC\n87Ga7m6ePnmzSeU11iJ9Cz404lk7uQMg3R1eDIKHL0avaYy/Ug7tx4UxTk3H\nHqMfzxP8mC+eyOfXFq5pfU4APWI6wFI9qvabiv7SIAnhzIfzhKDwbVM5j5sh\n+zKy\r\n=nsZN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-use-rect", "version": "0.0.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4b2f980ad7d1ddba132affe6844d95c129bf816d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-2FaIR235tYqJNaRhOkdnzYPE5yhKjRBqxobxOOXPf+8wQf+EEnEmF9pF6YpMP0GlkpEmUFAvK4E1N3526hozBQ==", "signatures": [{"sig": "MEQCICR3pcwxrQBE+x+ClptVHaFqTBC6vigucxcge5wdb4iFAiBSDUGNwLkKwXEpm9YdHCXO40Jvcy2JRPGpArFpaSjsIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm9CRA9TVsSAnZWagAAXDMP/invIS8g/628i7i57LmH\nXnLZnc/eE7XsZ2Wpa2uFFo+uICTGI/1I6Hj29ZxN/vvzsZMMeZpmagKb1yaU\n1BjlLuyOyxvULddPaCl1yHIpdK/v0+yezU224/smTS2iPXwILD1IHlXpK783\nmS/W8DNGbfFokl9X8j3QhfmtKoqfkOzWpqiTXpf46xXbKBm7LhMp9ePZgzU7\neplNDcXr4OzpoHWBXvrCxmdIZMqRu8PZFLsahZAg+mJfvHehsp9w3kYvmxzn\nHUhqHmL69r3J1+sOiETqk0pRfZ+lIhmyz4Fw1NjpwKighR5XrNbPirTneObi\nGA/QkSOYVYbQxwYNyaAORulv6Qsqh0ypBDd5nOF1oz2K1ejBzfFgbasPEkFT\nP5gmi8YrkpyAEciR6+6Ri1lQfX79St6gOYYsmiUoASepPbRI+RT0u1QCJo+K\nmdDDx/6dzjB4VptHP0zytmlC1FKKV8OBwla+ve+5EQQoIF19oG5iMYxt0KX7\n3PLf0qGqsskOGjn/5DZ9i/VyhaYi2z5EvFcXSM8Kz8Y4csQZ2wd4DAwWUPtl\nqlYdOpEZGDDqgYmwzqFw9m8LR7I5BSgiQwLGmvOLiSG/46qZzXwRZzCDrA3G\nltNyNX/eFgvXGfvW3vCagc7lkxG9z/818jdn+o84BvBMRHzEwv9sJN4vvEyI\nYRgE\r\n=P3U/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-use-rect", "version": "0.0.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e3a55fa7183ef436042198787bf38f8c9befcc14", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-OmaeFTgyiGNAchaxzDu+kFLz4Ly8RUcT5nwfoz4Nddd86I8Zdq93iNFnOpVLoVYqBnFEmvR6zexHXNFATrMbbQ==", "signatures": [{"sig": "MEYCIQCfrFYcBxOKoAQhLKQZOO5SIZGZ6Lu3RfeeTBKEvrb2aQIhAPHu21lLBLyGEs0tIdWK6bToCcGMYWxyEnhhkHgVne5i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5667, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7lCRA9TVsSAnZWagAAAHYP/iwsahBGT7PpFFRm8uZB\nF2w8d4EfdfJ3ytlR8la29Okllq+EMDGIkO4rETej44CH5hlrKs6LMnVYx8E5\nOdrHiBa3ARPdoHDJE3aAsbbDKAIQFE1DXTIEUxi/t7+Dmj0arBeg/dfLcINs\nYBkb7aYRpXTEg6pvKC93GypmON+i5NkSpKX+qnwUVEMs69LyeevK4HgnfCvC\ndWaCjlySiXAL0YRYH9C6SaenQAaiziqTKtbtZmZtM251YP8Zu0IUIjFY+ZXZ\nkIHmCTUmORBck/xMLpVNtH+cZ3a2325Sg/uFBhDVKchVTnDp/UR3+LPy/JeJ\n8UQObz94I98pzBMAZyEpbUBTAW8DQ0RuWVEkuExJZCm1XvoQNn1pUxJT3smH\nqKKZUrjcITyf4zf5M/ub0Ocw1pOxMymoc+lhhwNIZ3YukiNuHWF/EN7F/W+b\njRs+xE5OryrtsMyi1MEe4/QgRZkbAVPOqvA+hn7DCkofmp54pZmC+pzHtQZU\n8mj3hWp9rHCY0PBl0z3g6szbVJS8PRXZaZQ/P3VjCcPS1A3x7UB4yC7OtObG\nyNjZiLxslEtfnbIc3Cs/UL29aMuRpnOyAVv68gXff43jO+NEuo/BCYy6iDXC\nqOs4bzVi/kkKYW7MgimP4JlMscBhjCnSrHhGM0oX1Et9XADYfpF1GbBf2yTu\n8yfl\r\n=Of38\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-use-rect", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1d11b5de83295bcffa39af5d6b79d9d1dff1111", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-sxaRiU0KO2chvlb7ZmJpybJWWFxfBFv7dCSj+/Cs5jfKss+vZm1kDEedm8UZ1sKb5puGNvoc4llVyh/+56ZN2Q==", "signatures": [{"sig": "MEQCICWPb/KxU25j4Tf4/RXu3arPm6JLo+MxMsXtq2INlDPMAiAfb9VmvWSWbUpPXzH+3YqK54wziop8tjt4ao5D/vM31Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgp3CRA9TVsSAnZWagAAIGYP+wVvbj5QD7WFbN4bnTXy\nk0WMK/x/6XdgQyHAEObvrJberkZaZqdxgbEihM++yuDdKiWWqBZSMGTNdvzL\nCld+GcYp9qVl9GiPAv86ArZArqXTCA+1ZirE7J1W84jtAgi8JGsCs2o1IbNP\nq+IKpcE+n8VbkDoq1UXkxigLKPonhoGncweoMXBG9m7jFST6ffXt8nUA7nqh\nmr5+EZbDJGf5EGATcO2qSwzopg/02hs6qJxpcyEtEUMGmhEm0QoKxnLKnyVS\nBtvuV+L2FYdse4L69O9g8+i3ZeK5/dWMdzOQ57ZNHcWz71cIcFCxqFTRZlVV\n5rVnXqArQYekm6ZZQTFS0R4W71/fxA0trnmsTOFud28VIBdg8eXbJoPynkIB\nfwafQR2uZlW6aXW9y50Yv5mggc45x4zLRNGxxD/j0Krsdso+TPjIFTgC5DAh\npOlSx6szCQPBWvAMLKw0sCCpWMAQ5Q6C+wmxrqYgLYuW9Idh8AASAiciL9b5\n7PTfSCllQE1+E6kyY2rAqQ/0eIVkMIh+5eefR9SREd0RE/BVYowRU6z/r0Zo\nscPdNrD5VGq1hCS3RrMCL3GzmgwjzgCw8KqzzPnP126twFYQt0va750dkA6n\naP3Onp1zJVVmnvFVJ3VMx4S/CX+9hfeNL1Lsk+0x26b+CGTz0QWCamgMpmw+\n2Y0H\r\n=Eutw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-use-rect", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5e982de4a63fd5bd6b326a376db4911816714d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OlgHr9HHRhMc57nOCooHkxbWYNVga3TyFsEXAMVlhSsUIBnHIZlGws69m3on826L9RJROSJPLGqYxnJx5YC2dw==", "signatures": [{"sig": "MEQCIHfg7o08Zouim9a4WevMsat3kF7PzHO/rOE8FIBizk6xAiASvtwLAhwLn4PR2+JNIRdIRqJp1hGB6TL/eGlSsIdY/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyoCRA9TVsSAnZWagAAiKgQAJaNxAVe6ApVAUu7xgxk\n8AT1mpZK9Sb6QYpOFC/zR6YafclYIS2gFrpxzCpjIAf0XGBv4v3TnkMnoLK5\nrMFITokWpt9LVSztpXUfEyQ1fwcbp0kJGWtwGxBhGTKw0ECrEtGgp2TzX3mp\nqp9bIDSUhaHBS9yiXzwueo+jTJMHPJwE67AmTnNoe3jdeo40QrJQpfnpWZKL\npXGfp8KdJQNqM5DUtKiehgYQ3XkxTQIbtMCTKm01t0VA4tM0igMIfZaFT2+Q\nHlwFA6x9RcUJOAhy6NI57B2Aa35KgfqHrxNo8YE67T62KfD7eJGIDdAr9a9m\niKv6wh7GFXT1qySD6fCnt9UEHR5O3HWJqXMkYrHRV7H9k58/YYkaF7U92oOV\nNMnhCdPPihoRhpcoZ87eHofP9IvkPKA5R4G+tzX7hq7YBXdCZmMGl6ldTrrd\nV712nDgYJ9DFDb3dY2rzxzWIPvIsu5pgC9+Lptqzk3pYr52cSw3TCPhE0aLy\nsSxmRbvshc6GxA+sPkmzHYyunoxJo8CuahUhCFO/X7I/1l2T00LKKN1dFwLQ\ns9luUD0bySqGgwfc/StlS1uBIN/YrWUfGBOkYoH4T3LFIXhsfdqDpu/T7gyQ\nIVEmc7g4NzGTWnnIo8HXwnt/2ttvqUaM0UeAWLpmZWzOLEYV1a5qmhdaOCpg\nJJJ7\r\n=A4TZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-use-rect", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "074defa995c104e66319817c0b57ddbe3a22d53e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-BTj9vMz336G3ukxCOb3QLC1oEOtGrWKPTdEG0pFEw75jyg360rLsfuydYemOtGFPVamXL9Pxo9JCwMcxLWdUlA==", "signatures": [{"sig": "MEUCIBRvh33zLDofU9dkutbPfUGc+7DMU6sgtbZd7X+ZKuY6AiEAuHvQzi3fel3V7BHiRsCmaj+89mpGRh9pnOf24q0dT/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmzCRA9TVsSAnZWagAAZ1sP+gLwGq6/ZA3Up/WKiEzg\nLfKz/F1DHzfIO/hiqm5oFnytPXJCkujKUvzMOtIIW9alQVCxH/onezXBvsqW\n3+/auQl4A5XLDaA1oAhF6FBiPKtSdqbUYnDPFyOENpDpUPzxvtYLoCADorXx\nEn8q195Soeqwnd22k/ChvybpF23v3DIWFLi5j1XP9Y8+78Od8tc3sXjQ2jda\nMV2w78I1t7lMB0YTfxV+Midp1gNNB7LqclgkF1m0VCciTXixRanHXSsdcEKN\nCL0LJZK4iAc02KPAFf4Tenf6/Do2Lx+eYHHf0OdG8JiKoal0RACx054upunQ\nH/vhGLGstefeLaWinN9FsINzmxSTSu2LNyTG2ERkoPfrMIcnT/5FA5zYEa1l\nD86IOou1NrwxJZvivbL3H7HdP+otbDSRoaC5Pm6PjusqhkC86StMFvV1qnSc\nMpRmCBx+1TCmCIES9Gng8bJk69w+fXUShLGnTT7H+/M8dp80JgJgwhqTO5cy\n29InXi5Z/IHHabmp9q74zKT2Xjg6T9JiJU6Pk0W5UqmEpTu2A+dEue34+fZ2\nYyp4OjwAtZS8DOi8sD7P35zpKap8h8kNPBleVvUu4KsAZqgW3dxGwop7gY4E\nqZFogHAFbkdWZHR7aHk1cXYDR7Ok7YHft1eY4t2bV3twzjlBHXSDcmO4aHsL\nszuR\r\n=Vvt8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-use-rect", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5aad26d6844d3bdac09012e1d26b39899e0bca67", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4Sswj3HdKfQMzLbC5XS0zWysjKi8HOSoHtxOc8jlpUQxsh+6FYAM88N5PmI0t4qDVXlauwLemObbi6bu1SZoWQ==", "signatures": [{"sig": "MEUCIDnALskdmb98rmWD5YZM3TP4AlkYtouWzYLqWavp/UHRAiEA0YKV7b43AVEYDXKGlljMKGftwT9t/4vJStJja86aYK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5673}}, "0.1.1-rc.2": {"name": "@radix-ui/react-use-rect", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2fd32ea0b64f1dc470905d821fa75fc2a572db61", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-dWM6BVR9HjfSu1MKcSZPwbDLF3e4Aeel1Fxd3IuMlsG/vutJy36YYIJoQwJn/XWWfNW+8FyKsFQQuvagAXsutw==", "signatures": [{"sig": "MEUCIQCRGbnGJq/dV6YYvgcJEpDorjBdQG3bW5Tx53hOnMf4QAIgQS1QP/AVHkl5LmzO64zNVghfhse9eFEEDpBbBBv9Wu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5673}}, "0.1.1-rc.3": {"name": "@radix-ui/react-use-rect", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "79c88f345d8853842b061969db1a8081b36e7b7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-lnLYHZQdokvWePlyQ9Dq0Z3ZMIYHniMpfQGTOl73CO6+FMY2lZaRi0n5zO6jj4V2olQJQq3gc0rkFhjno7uFug==", "signatures": [{"sig": "MEYCIQDvxo6JyrqVp0yavcPuZqKs5YkwnE/P/DWik/Bb8sOgXgIhAP01CvbEfDwh/XQtbzi+e6ASfU/5ArVn9UNWI7GHs4PH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5673}}, "0.1.1": {"name": "@radix-ui/react-use-rect", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6c15384beee59c086e75b89a7e66f3d2e583a856", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-kHNNXAsP3/PeszEmM/nxBBS9Jbo93sO+xuMTcRfwzXsmxT5gDXQzAiKbZQ0EecCPtJIzqvr7dlaQi/aP1PKYqQ==", "signatures": [{"sig": "MEUCIQDvFK0oYIOXSJaxWjuE5H8UxArZDtzAjUiHHBPPkPj6AgIgOXnNQ4vNUV5Dv8HHRPaVnzO9SYNYjdGySanL2SsAtcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2X4wCRA9TVsSAnZWagAAOSoP/3ulwWlExtasMOTUfcrs\nACwTQZAe8wxpDzi9uZwFvvjWWdpBaM72ZlMlOUlDnbohfZogxSW4RX4GMMW1\n8Kk0/ToLkGyUXcXC/zZqQQgaPwNMnk1DJFqu7tOrDs89EioC/97/jq4hMd/b\n4HAQST9qI+1q9wEjhVKyQc8AN6K+yqrh9/hPoNiWYY8KOjZN5SHiUPUd+p/o\nsoUg86Ivk5yPR6Eb6JjgrMWZTNOtMhHjweQa/j4Ffrdmaul4AYlkIyn8MOeA\nPqmAU5pY14rj0AoVVsaCSsby5iiLFbiDLSAMFADUYaxmiSrKud2G9cxFnOqv\ndgWJO7wuMvi/QAQKGpk9GRQp2IM0LhXxL9VALyhcsfGpnncVHo9Ofc59zE8I\ny+/xHq7xXKYOM74p+Lnum82YQvYUGsaRpaVf5pWh0aUxSZfhXEDczYfv3KIt\nSyorKBn47iiSqoPhiZCvcdkW22m+EdAQdSgJSOGfkaQDkWn4chaxH7005brd\n7g+Tg+H1tTIYgHuCo2eOZ9k+Fy6XXaiCUaf0MMtUw+Zot4Jc0BfQvYES0Abc\nLhPp/GD2X5v8v7al9mGM0gJ+gSWTmPm3MDPmdFwP5TMEl9+GRJzNi3J/WYzu\nitoKATruQ0g5EmK7lgn+BhEYFQUt3gzWpVoGQKJk/AUGdCZDXVU5Zz0Gpsjy\njwIV\r\n=6QZQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.1": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e3b69f6e1c3742d1658724919b40c37e59e41246", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-N+KMjzE93s85gBElaYWXXob6dya89JG9fJixyMIKErMuDCaNRiLCl02AwNh4MHV/1ocaWZ1J77G2b+uw5SsnFA==", "signatures": [{"sig": "MEUCIQC9f1br8mNu/hnD22Pf0E/bqLuamiG7Px8MgdXgl3rnSgIgFTStZKF7F9FJOAL/vbtkKKolMVDKdAy0uTc8HJQsmeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWASWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVLA/+LuDju33//2Ub/bLtm51O1wsnOe4j5BF2HmOwuMz7CnhGrLbU\r\nmEmO/rRr+xziT+r6TefUTnOyjkkIQfwLLRhBEPpnxcSPwIvw+vUWLBoad4aK\r\nZ8uzJNkeUtxeCuCTW1/B3Pzx5/uQ27oS6gkMCeFKhrPoxgqMxt2+Sb4nw2Mv\r\nDp/5qO/1upgIy7+3c+fc0OndV0Uwxhs2hPOMNcbyqM0DtFGxEU892fg2wL47\r\nIIziEjZeiNbV4UPE/RmhuYktvVdkruYJtGE+yEGeBZS1xIeUwXgr+2unB8EE\r\nBOc1xaCZxFHNWX5zmNGUIA7wucXIbTSbj6edNAG5LRXBOqcQ3zYVAB0rzEt/\r\nNFrhNKNMuLwVam3bHJYUz3XhRwM7pK91EJgwZKVWbOZHFbc9FfTXaLik1p0H\r\nq/7EidOHWWbVDefUjzPQcDKjBMWa18/o9k0Oasd5JbWpGDdV3iioe0TOBcX3\r\n2Sk86t0I1QpfF1d+rUuYiEz+SMNqTeubxwdpdZznfKJdQwOeWGPwAvwvGgwD\r\nmcBR68s/mDTwwOibkLD7b0ISwOe3TAeEWi1ZVcJ95dQZxm4vLYvUT+p/B9OV\r\nf5uI1cgZpQPgdzj31mmuZLm/H49PIp+waCZTMSftRc1Ag1CW29siPjHNi9nl\r\nQES7Te2DwfPHoJixsLWgi4D75Kw8MQySfEw=\r\n=0Vp1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "19f4dcf4fbd61b92967413470e08a5a6936f49d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-zSm+BVV8fgE4WheBYkLMnBQC6oWrx2kWk9hxfPdi8DJSn4v3i6oJosfIX/lUkbIqgc8ALYGgc1/YstxnWvYuag==", "signatures": [{"sig": "MEYCIQC1h43VpLXgp8ThXoEWDzNzdOSYhsP+E0FJxbT2vKd2CgIhAI+Rc1TFbiB5Ad3OKlJonXaW9UYWfBS85ErW+xoEaPdj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5677, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRrRAAkaYL/DgjA/WOzHZOILck98PdYfE0pAaqZJ4Q8/lIc3AgNFii\r\nMIsfx/sqN7Wdy/Jeu9vzW0OoAMRt/yJOfnmrlxbkklWm4+pl6QooZYPyUgbv\r\nMcDIC8NJ2C7Uz3dqwt+XuD/3cvm3vAsjdaeaLDfDtiZoXsFfu13z0/KW9CML\r\nhs5jomwQ7Db2aF82xEzIaJ8qOZbdcHCp7qzIZ71G0EhMwp814TrvoL5kUW/c\r\n1jobLO/beMHjpDqvCdi+OSNSSmKOV4BfC5mcRYRsmJwbXLaz1kYdSfNf5rXB\r\nDC835xvGavrZHCL9g9NF4QZzcSSRYG66782Onl+xWOTxGdnWJf3kjQGpzdGU\r\ndDvvkClffc0pV4BgPkvh5CpgQ7Hpood5XPENrc6KjjxnijMZZCoWvh0Y6H5l\r\nD1HNu2dWT8JW5HhiITq95LgUnZg/F06DQ++Onw/LefesdDf1F2ovnhCRX8jB\r\nvHC989AwSKkJiaLy0G2xnB8bXLJpet1E3JXtay4NhuLsi+MWunxPWpVCHn8F\r\nh4BvDyJomuCKZr1aepegPx7fW2NHLjwQRGUbSCRbQDnxsahyLx55x782h9ds\r\n4T2NF8qfiQK+oZff8UyG8m8RS6165Pm8uF0CFwp91KXMdUi40TJTvidb9GuW\r\n+TLmsMEVt7AaUC2nCekpm5nFeWqscssQVx0=\r\n=/SYQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1516a3f365e5aa0c5e2d8262f891ed187d99b7ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-cVcGW4Vya3XlmyNEnB/14/8/DZmjTB9Q5kHJvWfGGE7dB8ble8XTsQPQ0+Nt0c/1rXxjltP5Fvxi8YXCSNs5PA==", "signatures": [{"sig": "MEYCIQC8TjAGwX0TD4rX/OUVBmZ/oS2FFckqdOfvGsZHdvRN9AIhAOiNKtw8jFudh6JhiooXnH0ZVZBl6GaaLBpVA+RXbqkE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDT0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDSw/+Isexwe/k0fR1bjDY4y9TCGl+iiiRrP7uaFr0YNXBbL3LHAmA\r\ngDp+gC95UW+yJEn9fz5jZLXsO7cKOpBoPTN9sdT6ZAqMx/+/Qdfifh3Lf8YN\r\nkAkT7REafpg801EJ52MdYCcBco7Eu2MBog4DWJurqDwGP+r1/zn83P3mjo1X\r\nnueMRIctRFrIGfs7SAnYppvrUFBOgG5OLkkex1N419gUU/XV+aeFeDxoElws\r\ntDL83EmA5kD7MAxavdXwYXiaGH0To7JEBWctvntU8VzGTmvk0ZZZK9eXf5Wx\r\nEl+uiC+wX3zvIaGidoL14XfzsEYMNm8jiq+qwYflimu0eYxUYW6QnbE0sUFV\r\nHss01twvsMslmk94OSTkg8xTtbdF6JUoZZkuXZIy01dCqnBgzwG38ES2VhDl\r\nK/9Nn10c53j2XsJvAwhyukim/k9D0WecGSeqNVF32ZuGkV0DhSQ4q9z7CkLC\r\n4lWGbcwj9HAyMIm7fEq9kTgehHK9QE/IASJ6j31wKHTpnX6JA1o6agVhQKqd\r\n0I1+bF8A5cGEdVVCaDv4qTecaU2OZkT84HUx6AUD1AmIOlUtd3o7hsz2N7Cm\r\nVsV+QothGRAGe+bb5CYxnN/RNVUCbwuMfEBfqzvfl5HcBtWKR7X7G6gNjPNs\r\nJwH6nbZE0lrpOcl5512YvGNirjC/KtH0FX8=\r\n=5Pax\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e2e6db526303486327048a573aa7c5d08695e94e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-X1b+l2N9GHFny0WoMAiyNh3mprktyZTqF0z++ZmbgQyBRY/ybw5P3hbLA1pL2QPYuK5Tx9qV4dLATB1+MsmIqg==", "signatures": [{"sig": "MEYCIQCWjShZrU5b4tLgDMBCphNrSHj+5/nJ8Go98BGGawZe+wIhAJrY38zMFgNKDLePus12oYw84EgQ1DAxCQoX2GhHd0TR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR7g//bmbKxcU25EHFVydYuV+jTx3nsiXN1Ox5vb3kJ5R0zCkAQwa5\r\n+gxOCe620qENr1U9H14qTpCODcVoCqkM+raML1SPmj6N8PpB1PQkfmpHvtgg\r\n3B5H2CnmXoxsN9g6jA2Srs+LRGAa7R7n92BmQ0pLkXx5rUZGT/hn0CKKpj5W\r\nzUfMHQcYuq20edeENcjTtCX3miwZy55DC1myDdvJqVIqO+V4IPvoFUzffAYc\r\nkGbONafS8FLvlFSEGwXjyX0oJNRiKr7UhrWvHBVSIc3CjL446qgpaY9ByLm4\r\nD6ZPUWh/CsMFIhlibbpKy0PuZSO0PfGJppL6Y4j7s9JcVoB8srXx1nisFUDU\r\nErazUai2fkZ4fFTsCGptPLgbs9oAcCK1bIZBR79GydVLW7xnd33gJiMBJ97+\r\nB9ASlMXm5TGKhqUUl75SrRBJEoNIaZprrqwXK8830OkcnUNZ+GlGes7uWJlD\r\n1WNUe8in5z2t+cAwV7iOEKoV/+kzmYpCQP8ZZ0Mqv76d0KI8MityzWAf/IIN\r\n3e/PNkV5h/Afgk1/I+qMvjP3t4ZR6vKhSK5PpHI7EbybywkobEX409Rq5EDE\r\nF2pNfilv1XAskpz9NXeF+1umLrnsMdMo3OGI7Gc4vvSrrm4VGvMklI3K5duK\r\nOuxlYpIuK5UAUEldbo2D1vAcjADFRGKoYo0=\r\n=ekA4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "579252e8aafc10b1e867cc2b7c4e21a760f6c3ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-1wXv1UfnaSunKIyylLj/lnNtUkbpv1ulHdiqLQZXZsaaiyW1Zxs4oVbpN7hBm8A/JKpTPSoNJ6r4NY09I7p70A==", "signatures": [{"sig": "MEUCIQDac/lUWTtqPUK6RZi000wLing8gQGKAHm+CFMYkzF6BQIgZmJJUX/vmAxX9PIxrNirA1P7DhWA6cm5vIca7kdYhs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHmA//e/saxY7AVn/gGvdMy6lRptGyvi9QIBXYJha/0rj47fmaW/rZ\r\nXsYfhBB7XUoQgfW9ckCFnuFlsp6P7UWMF2QBBupl4EMfZPTawBPiS2bVweYJ\r\nldE7iQk0he7Bj63/EBc3u5LPFba7n/+V3sLG2bneOTowih0J220ILbQ1AfkI\r\nF4sylhZRhkjBY2aghDarMV/FgOwEpvQaEXSy7CxytMNKurG9ZOyZ8/1pIPMR\r\n0cuCG+5qdcAr9m5piA9VA5OpDJ3X50MGcmOUYOxoBVm6M0eKGMMc7npIOxZ6\r\nKIoHN8ziRp69NVLmCNoLoTPhNHk/cxwROmci7Sxd3t6qNcwS4b4m8tZhY9nk\r\nP5FQKs1BDOKv7fbAeiYeR7WJYiueRsJY1zSMK+0Pd/AHi4L+StOhyk3uh2Nn\r\nNVZ8XMUaJw58tdGxdHGE9hGu6JfgkNmh0uW4iqODFmEVkUU3+S62IxEQe6we\r\nk2miVrql+A1apjYz9GAwbT8+LAPGFWgNdQStvvLGKN850p6PGEhb3ArLBJsk\r\n86oi5+ua7behtX7HqUNDgEsuW+JBXN0j0wSU6F2K2E70wIC0CD0H4C3zKn03\r\nPoBpRRQz6BFfgH/SjBje665ii4VmRNaqgvK3rWubwQqwG2suh8Ojx8oT9RrQ\r\nONYtWRFg8Qq+mPyQfVlWo1WVp74oxxIcR8E=\r\n=y5z1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dec8c0491d24f8e6dd75c1fddffd0317fd00e3f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-yL0wdoGa01uhW+3edH8sfAwieOjshC62xuY2Pvo/EMPKwb6lNWug7ED2rs4LBksahBlsyJBHcEbfB0AjAe+DPw==", "signatures": [{"sig": "MEUCIHMQ9MVhD1pQxVKd0fxaFJMUUuSj5zGkrgcqXlAzecmKAiEAiFRdvB04Hhovshm3AP3ZBqaZquCr/mnw1wUcy1xD6dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8ysACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVFxAAiFtuN1pjBGfTjmpYptBorAV/SAL1cCk9wKDR92AkBwPdR+IQ\r\nfEoaxXa7AxDUKFyRkDOT6s5XEz7qRAtz2fEDQ8rBb0/MfhMkGB7dJmUoc7Tl\r\nMZqkZ1gA0xKlXaTrztiXk+kpVT/vr5X+Zhl2IG3AlgYdF4H7Fq30nxELy0mR\r\nZCGBq9G/fiy6M22UxE18K0nVQLHcyaCzZIcsymBBmDkgYpemEp2Rl/vzRhl7\r\nXB/ihP6/Z4YfPA0X7o/FhcvjBIy7BDtenIjLsSGvEry9dYk7EbqxyC+Rxorh\r\nrvPO9UDyTQ6Ol+4w917BhuaU343JLESDkiFD6zrzNCJcGJ4zdjVGvbfICYcl\r\nD2mzb9bryGg+6MPlmIukr1Gt2snWKGZNvRW2HRc5AWcmvzROTkbvcf5PjomH\r\nkwpgMAU9tnlPq9VfRoknznPtcH6vpudsgNtWOBrHXdHt9hXybFVXKZeWmEsu\r\nhVXSCkfQXOb1emzcEZGBRRQam3W4qT2hzO3UdJuwkYAI6W1KIdRjs6/d0o19\r\nxrcab5/BVRo2Ax0E5z3rMf4UX0rgK/tk4c6FMSUO5CKCKt5Ka94t3WCK4vRS\r\nR3dFT5W/+Huzu4zCLXtC5LjYywCQRgDfz4lp6cQBje/MHaIQ9nTYcXVRtVkA\r\ny8hQkfpAFVij7ZQzKvpn67gCO8knvwGBpvY=\r\n=4f+k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7f3118775b74f7735f72316550c4844ef18699d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-p+uyjn4HB29lnbltJ+yFc+bWnVLRu2AR2wjtqVeUUGvE0U4xhGS3L+B/0KxeVluBNokyYT5sdbZt/9YdWtoVtw==", "signatures": [{"sig": "MEUCIFR1g2anTAEeyRF4bw5T97ygK8ljcM2rCWrnLfn7PmBTAiEA7NbiexHhZSnNINfwGnuMgtSinQj2iG2FNRVC5RPvExo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwDxAAi+57pU9jvoWoT0kaOj33s6p0vJJ5fkplgrXiIxxhaWaKIbTp\r\nHEATCX5qUQ4sF/GIb0w8BGE8/Ks3BxJLqh4OVSINSvr7SdKImQXIX3nsIhno\r\nEM2z0i6zHxrgz2RMJG0BqkD9jzu0rUV5XqujlO0UeFUqn28ai+CbKoNx9syO\r\nx0qCTCAsfmVU4hEZ4PALiuXq48zOd+CL5AXmJoZZBndhG6HCgdSnWpBtDm0V\r\nBpzz9JmjtXPo8mIwZdH5rf8nEY8cj/5RPxo5jcZKRJTiZUzATfGHFZxuCm1L\r\nktdIeZc7n/+N03Tm3qKwOnMdbIevZ84WSCYh5QMOwVP6Tm0lIu1Gh8EhzEeC\r\nsmpl7qZYtziUvU65T9FFKUqRR5QRpclxqAtAr+Ile9215reM7OkMwI0S5kVq\r\n/w+6R1iXPzxLEMof5/GcdpuzZYlSu0NJod8U12/J1S/1l2nokdnz0jSiOpN/\r\neIGO2GAJV7MLUFCN4y7xBxcz6Ib8aPQlLEjLRmCl4B24K2Jbte5s4baQTLAZ\r\nb2nsw7kS4UVQ8HHjghxOAi0R2JiuUjbpuJowjxfdT2R6TVvzEbPb73vyj1Ow\r\nUohxsSUwoWO+a0p9rJOPzVgefdWRq6rf59Ydd3vVbS9Z9vO27JdpEjQuxrkL\r\nwnfTl7MXm2pEzbJH7UQPx0Ecm5FFRd7oKZY=\r\n=GSj+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "48547f650eec98549aa4e27bb65eb2124a2f9e1d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-SS4E4BpSUfkYaWBYgV1MW735CMXQfY1EUoUyktUHwcgmximThfs03iwDK9xhcHVfy+GAD23op4/XDbrkPSPcYA==", "signatures": [{"sig": "MEUCIQCSwtDskYaPJOiXhXw2MFa/XHOxxBXK53hDKb2jxcULTAIgJJw6o8+LY4MJN0jE4WnC338HH55IA7b0NKA9z+aYX0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVi3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIww//Z97UdzR0cp1jxRKLd31rYAprLK7bDCsLvLHkbxQevcW+Oxna\r\nF2qVRxSBNItYBUJEdKZYFd32MVOK2iBuwcHHwJ/ItAvTlko7KkPjzFaKy2Rn\r\nCvc0CSdenjlrEhv8xrj9MWgsDzS64OyWQaFsW4GgAbl6TU1xOwdQEYdZzodW\r\nASZBNedT0vAyKIVxm4NEFsGkJhU1+0BU9QmKdZhqVWz96H+FVsRNe3+ZaSFp\r\n9SgkpG2eetMJZqbBLB00xB0uFQtGhzVFOcunh8Em0ayXfVUTtSJl7O9x8pib\r\nMwrm0nmbJlX1TNpVOQS4y1CrahbjRgO4esz/xvFONk9gJlJ8Yr8Z6nF5w8UC\r\n+R0T+RkAr5EoYUPxNMPqcNZW3a/yjE0ftPzzuVG2KaVMB+B76TmaSHc4c2Vc\r\nwzh6GXiEhuX9NHkuL3Y7ZUhORtfELLMBJd3FoGAPkKEwfPs8n3MrWd9eEKcd\r\nI/eIKSlJhHRz6mBU3HM1u82fNzrzIQNnZIFmp3DxBNnDVydo/eQ+ZnVAE860\r\n45stfhvRBUFAjPXpSOun3estKQaWLnrzeiY2taYLt2IBbaOrFeIcU6R1P9GM\r\ntujoZ3hqOz3Ak8oba57UtFLF6VzQTkRq0Kabkw+K8TmGzArnQ6AsAN0AVMB9\r\ntDBv0taWUhgzs32H19s/2UyxatTKewS0/1E=\r\n=Ka2l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07d58aa7099a4754856549fbe8989b686eff2cdf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-/xskGMl3evV0uxO2XpQh93LrUgmxvDn3x2ACwFyxYoGWRADI9k//VdZn6NqQS6uPr9MwzTCzEMVD69CSJHl2yw==", "signatures": [{"sig": "MEQCIAlPLQLLUm3mEgvkqviCt684/tb+4wqwXeJBEveISHYiAiAFrOiNZu4DYsoCqHd2FricbAQSuxTQ4KG6XIxTeTVRKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNipACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroxA/+IWMehqHofHMFcukqgyuQ3PspK0FpBMq2wqUOXG1JN2Zw8pA6\r\nsayrjwN+cxOAsfy59PtfKZ7nM0HD8BfmviK0QTCa4ooai1BjnZe0FkviS5ZJ\r\n3VIFmyOSO95Zesq9EKoR9N1LhsJO/dlWShFGBL1LqhCEhAhDmLziedkgr9PZ\r\nSAEHQX1gbkH+C1+oqlqnwwqReHI5CCIdNiuYarqXP2rWO+ygdEK1i28vqPkh\r\n4i5QBCEw5xxaxnXb2P/YkGF/H7NTyLoesiZjc99JMxBIsHeZgaU/Q/xESbfs\r\ny4S7y1wpqaRjzKxwBHyicIb1Hum8IHBIgs7hoxN9q8Nf8YU1xStSV1Usjvh3\r\nqlMkQnGtR+0WFuBjFcbvJvTqd2WEX4/SD9L/BPVLksaHOfVO1MWIVuGi3dGs\r\naCctUnPGilAb8zyLCQSlz3n+yd47CREM66k0uXQz51eaWOaV4PkfcpXwSCiA\r\nkK+07xQTmPu2rqA6KGjYa3j3fl9e5TTngGNpo0B9EZeAj0uO27uPd+PmjxAF\r\nGYIWcDEVdvKrQv8NsSqGnRC0TM3NEUl1ywKcY5NvdP5XpFDR1NJ0PDdJJam7\r\njXRxlIsmGjvHFfqtV4Ci3GYOibScyGDbSxQvVLEt5OaXPMjJbNRerSQmByQ/\r\nK2Om+c8OixvzGCQ+ZCREec/10NXByKmYrug=\r\n=Lhxb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "651e27e8ea2b362b73cb367da22a385265208e21", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-gBoaRHdH1rhq1Bget9DV5ZiXAt4W0wqoVvEII3ZseFcql7wf0dggK7oRp58CNnsUnh8UcoAQUKMN+5dU7CgMFA==", "signatures": [{"sig": "MEYCIQCyhQ8A62LhMmDRYqfABx177jyb/1MZo4jiogIUMbw4MwIhANSuqZvMg7wzSTYMDSC5fPQEhYeANY25aNXNKAMBGWls", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxHg/+JC+5aKI7PA/CXaMuFsf2886mXxk7MDLWWvBK2xFSdvC6mnxF\r\nVDXCX+Sjwd/3+NzQY8yDNVrrYaGFQrn6PJzsFDg56AOXnuf81RruzJrfyIvY\r\n+GlyTObb2TqDdl2l6UzKWemlefI2StI7RFjxg87ZcAZJmqUHGLWqCuCmXFa7\r\nFkTSh5LYt6OYn5QEE98620aEW0mS9xdOxuYDWdNpTs1xVE7j1f6U1doJQDlf\r\nv9b4SspX/VRMXa+Zlu8Eg65b/WSCb+qmZsCkq1gUzPwBq7EB+WAMvWxltIkk\r\nQo/dGn+cehDrnVSOfdG/7Gr+uP/B8mWNvunqgCXUOeXswmYdVbhhoFnpFcu6\r\nP5S5aS3p461BalGJ2p9ASuHZYS+la54gT/Ryz8toXf1LoXg6RcHa6E5bNHXj\r\n3qYCQoHhzXJhUJxMgklB5wZuKcZiswP9iLcI4HbHnmVLX+2Zq2Qgy2WgFgQu\r\nF0K/Up3IfS9oq/CmFT4tX0LKdl/1IxY8qCsEENfMOXkOPUBPUVyB7hx5mHON\r\ntuLT1H4nty9Xyjdn7mdqEQwPsy1UkN9Aa0Yjy2ROPdYLWaCtN04AVIrVe1J+\r\n0JW7bs6MEaehDX5qbRH9/m+3xpVAsBRNNqMW0E8Y3EUmKQR8mpkY9nM8nlnf\r\nNegNB1d+IRRfVYJ7bKMOF/bwMQhgohFecYI=\r\n=7O+j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8f68d0a1e234e042a3434ac35aba2b8aab6d943f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-QQCZ+/dagf3hO6M9XBWdpTTxqUUSuCedKHLtY4rNrnASsIjBFgLI4qLNIEhBgmEadM7UYj9DvaFmVaPP0n9FLQ==", "signatures": [{"sig": "MEQCIEcewlebxyM0y4vyvb3tASjpcVwqPhzw962OEiBelWXgAiA85JW7EeKWeZFPeetIkSVCSrIas0IzCEedNk300R2oMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSmEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrImQ/+Ix2NdM/WVJ5PL4yHLXrfXTv5yzKbARaQny8KmD1kPJsXQN7Z\r\nwTmAsmt1eC3Q+XjT/a7IOoIWM/TJXHOSs6hvi6nmGMCw6D3KcXvv0f5gwu3P\r\npzmg9pQaSswbia0C1MoahkOD2c643MSW7CYklJGo9+I9k2kkzViNFTTNFjPs\r\ndGrQtKnt/ox9qG8xSH/hX25aU9BKw1mhECClNtTX0oZZR3bIG476KY+8+tgD\r\nlmEURVA2P5gbvcCmHT9tiP9Hvf94NJHW/I7Dr0g8rluPeZCrPb80cbVwirri\r\nRHtPbHfc1fg0jC/mGGNrf7mBlUe0y68pVC1BmfmcGbrlkGNunmT3cT91+p5y\r\nPezWarB81yqhljr8yLJ40EdZNba4I7JLlLgi5CIlMwA4PZtX8JknGU8PM6L6\r\nY4PyIavO1S+4qfn08EVG13fwfaDr1O6iRPo9JiObOSXDUz2nSCD/EXdKo3dv\r\n5NgS3n4zwkcL6GaRmvi5T+gNURdWO8/2kGKnYSaydh9cJKNAnGgP7qSya7GD\r\ng015cik6tlhEfG0hPXcTeH0aVvCQW+9m5Re3ZMOA1OOk32BqnHmQAnQBOYz+\r\nf4I6g/lKfAqm7xqEDmyuQnjEAyU6MBWZ0rbseWhO4zzSlXcKyI0aHQQyprea\r\nRKRqfEtE9J1WkdZZ0aBR/WIl/DQqA0w1rHI=\r\n=zDVf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9feb963e1bf15b4646f0e40966e997918abbc8ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-++hL93K6hfRyBirqRhS74YrLVX9d6jXkLCyy+6AwrLNpUHoCRBHqnuYPEYOGnCKqnBin869SAMMxgTlyH6NavQ==", "signatures": [{"sig": "MEUCIBIPd1mzD/fj5O/+SYqyLSPqT79pafY9K5Orwfm2K9FGAiEAsxnSSUt8S0pEFd7gqKpny6V+Ud3VR4dgj9RhtWOOa3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9qg/9Hsn9QBVUhErnOTlvfPJk/DckaXAz0Xq4IkyFsbu/rgsSuXBE\r\nVOSFrZU9drnrgNpPK6RG1O18VIbOpG3K6KPk0DvlMMGFRxA+Xr+mgNIxy/PC\r\nvhl30Xlvq1YTnLX5c2ueKfJizbnTisR4rxu1NZuaYCC1TVv9L3Od2KpU7yEp\r\nRMX5C6dClKNuLMZmJ6OVsl2lrQ3sUs14a/Fzeu0yaNM3/ZYMqNNQq8SspTsV\r\nusWiJ3hRIHisUZjgjjF5QmwTs9q3zKphnee4jogdDKYOmw5LjebKP/Vi0sek\r\nE0dxhYuucK8pzjpCQljaSpAmolGWkOfptZA/ybSrb/LMcU1qulGBIw7UPNym\r\n0FB6kJc310VsoYwxzXiDlrWxOM8RU1mV7Qj9Pm9qUEmy+P62bQu6gPEGq1ZD\r\nQrf9YYDr5KOMjUpQs7dNtrKPRZq0qvWoBFXxp4D3aBexxhOAxiNqeVhpNjwN\r\nQe8bYquyKR5kukH2f3mpRt3kZo0hxPBbnT5unHPoom5y1+TvQPMXXGO/k3Os\r\ngFFD1uGpHY65PzUSyegd2WR9o8ORjgSrLrSAnWCETnasB+GpUpSAmAZBVWPC\r\nyYpLbLuEf1FYu9R/bgRjxKHc0Y6i7nefUjje0AiM89VeyqAoxGEywHCMOyCZ\r\nm1b8g4Jneutn11kcLNkG0UDtVcEJ6qv99Uo=\r\n=zzfZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84b072e7043e216184197e8a42d69ed2e6b22334", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-O0NVv06rNBj/f8C/Khs3ZiK1J6nsKvzmnQpQCY5gJZO2hY8xlDGTp8Y5mVqwF5chDy7p/yYYgqg0qyyMGfZBTA==", "signatures": [{"sig": "MEUCIGRI1BKCQ6z0w1QdmYL8JW3qI7CjDBaY1oLAcc2FX6I8AiEAhOGSns5kpPr2V3SdZjLML+e+4Ohc5dod5n6DjLohne8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepKaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreDw/+MnzNCiDEuBT+jKVk/JZwITYpneexnrsjZXRbux9Jot/wvKHe\r\ndJ/xhCFQk1Y+29O/kRsoDfY3l4IyKFwPaZ3Ui1jLMPwjZ8+/GaX6nUroLs9M\r\n9zKi2cQn0T0mWUylQt/bfXNEQhdkztQwh6+G8ZuzRbmrvbqQQ6x2ILj9HxaV\r\nbAA3SQA9Y4PYDwup5xDi9ht+LKuGVJqo+yBbMglwpV0HvugW+Y2iOcsiPeAU\r\nRu0igOkzLcfDHyOS9RdKceDmGvYKJNiJeT0X0yGZseAlVyh4nUQP/TAwnLU+\r\nt4h2e7L3WaBkXgZh9dRN8EBiH1A+nNPbbKy336ffzcM9SuZx3Kuswi6yc7f8\r\nC7GehQV/I6myshmC8gvSzQdtvwyduKkONbx6xgnoP3opBkOitT7ivgqxU3iY\r\nXvHTef6+s2M8azjwhD3W+d2g7jEpClm0VW1S6EDXxPyhITGKTB6cINe6II9K\r\nWoDg4/KtxM8kf6/OeN9bg/9Dh0MF6xovRYnAhtQ/WWfiCfR+gJIR/xAiPT3b\r\ntgtgClIqZD77RdGbJtwd/6w+mM61oE+i3Z65WEPGlHXRmQAARkXEs3U/cHWm\r\noLaSSVhos/eKbYAY9x1lufzCOXy1OMZY03+llwmnpmQG5JFeZpaRoYOUCZIN\r\nSKjy0LAFLVsK+kSXwltqF6tfvIRkh/fjHJA=\r\n=9eb2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe2506b13569bebb687bab986b1276bd4da120a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-Mhp6Z7rMW/RKen1WAze5uk58lU5i4rf7gaiNl+cbs3z0obB2c+/EOkjYp4F6jSqqbBS7Hqv2hcgfktEgIrFfGg==", "signatures": [{"sig": "MEYCIQCWW0U/Mgv01L7bIslI6U8kgAbGgE3xbsjD16+xT27NDQIhAO5nG9QH/M2yvrDL1nanu6c35KPIiwYVqLYUc2U1Jtu5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpX2g//SSbDAh8ej0cNTzQigLcZGptdFpX4nJWJ9mnlibRcADF7RlS7\r\n8aGvJO16IKBbWjb4ETDI1xRu6/b04D0OJ/z/eJereqYMP0bBrTCX5DIamcYx\r\n2DfJ1tnDXMS62ytiM6mzRTWQMPIUKdUvEUon4dIqlTTWxFPz5SnXV3AsvhmJ\r\nlHOh941zUZal9kJNjYFKhfQH7Qr++KI+j4txTla/6UZVvtm5R1etALeH8A49\r\nSBSTLEzx9vULIQGyPDYEPHPgWxKB7hGPIk4+m6/PTUKWR4Y5FauhrSP9sDmS\r\nU3vvyALEHe7ZB7Hm3EQpzVfasGBDp0WeS9xrOHztu0uSRnkJNDiHwQ0LEGXG\r\nFy4JpyXlZUTJ7pjZ8qDHxTtvBuxpKU+9uvjpmdwcbNHHT69VWKz5JuFsPUjA\r\nCjHe5tg7b7FdMIwPGmvBueG4iffZfuYmA2PtSAg2um+gXcaOhp/bXY5quW/G\r\n3H0AcpdMSrrdsmQ3ji7wwaBqpQ/rR49j7QTPsl8Wk292STirbienUTb+zrXK\r\nHf/g+fFXqD7szk98oglbAExKorK1ULdxJD6YNq0Wu9sr0CfDGZCuXMwwL9a5\r\nzyR2nuCQG+5R+AGa5phG2uWu+XZTHOOGHEmxhU6M2tFLm8Z9UkErHe2HapuE\r\neBtat2wt81uS57li2pjBW1CB2fV4gOZlzg4=\r\n=UWOG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d323967ab8943c436e3b0e5a86d1d1385ab14f62", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-BwpyGb/kSqOOnrAd1Tv5D9sB6om8Vt/pppALYQGenevRio77xci7OAoqAZfLb5b2C3iD9D92o3yGUnIuRKCrug==", "signatures": [{"sig": "MEUCICi3EBwF8Z2qKECC77ZgwqrLZCeHohXRseB8n4W2gCVSAiEA+DB+MdTkDdlqRz7IuIRgd71N/C3qsB8h6dZKB1Rj6aQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA1MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCOhAAhggRyI4g71c1Tkdp8c1uJnK0jk+mTxOmqZWJsbcSULJ90o09\r\nZ3FJh8Kpou/SAkOFgwSQjbzn0kwCZkCvTtqv6EYqIyGTt5k3xLe4vzImoIDV\r\nk1CxuMG3smHF/n0sRHK58Wgj1NLj4FyGS9iaTDhCV5p7LSHMGrjY/ssLXD5I\r\nMYCMOFuU4S/xgs5uUcpZccdbjwIpjBlLtS2y7tSa6ObjFtqmpeLiUhP//XPo\r\nnnzX4HZTAp4OjKlxuHyHYIDEcw9g+Ff677CMshgGrk0cvrCdBNcidAIe4mox\r\nr8d9smZjNtAzrM6gWRiTOWd7MbV/W1PFrsYK6J+jXX+lnPvrNroBG1M/7w7P\r\nmD1s6d+0PLzQhao0A0iMcGIG0As3idCgUCd6PddJLAAuYUSpJqrrBx6DGuv1\r\nmEhZog3/stPQXFRkrpgDFtXecM7XowHRMjZZaiXYbYyhiiW08CaV4ns/pxgV\r\nYBrNbLKBMRK4kZDuXQwgB9WRngvnX/eMCmUtnJrFHhxkIPLKK5/DeFvIWgw8\r\nTUPwXp7mhX4Df3Gu66q2WI3aVU2pPIY2LTZyh1PihWWEaw+p1ECq3+vgpMZm\r\n1PCjN7igmoh8I6HGJBpmvDwarzadI7WgeM5GHPmglsrZ997UGI15r1nHntqp\r\njFTDgDfg7ydtPFsad0QI6djIGwgVuR5Avug=\r\n=X8gE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1055e343f1cd305656865e27ea0b66d98ddb888b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-Jm9jFBR4k3I/wJu4rEGpmIxCTUV8mpFPhqV7bsLA3nWxQiCaHXJuFPMzJ2ceSWe7TlP9QQVWuSaJNiVWH0o+Mw==", "signatures": [{"sig": "MEYCIQClJ03ZRuVRzbsujT2P2v/KvWzzbunu4A3RVxEG4XmhaAIhAI1dfxh68QK8+B3mi2H4sx8I0I4xzB5z1efG7bLVUfJ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrfTg/+JcQp7H602wCLP+45dXpFsm5ZnhNTTp7YuiC6LpAaGAqCOkmY\r\nnKK5nraKAidfFZ02w2gRoLZ2W9x2esIH7Pa6eT8gSoyD7sRULW6QLcHDiBz5\r\nn6i+66TbJbKd5dCsXVUj7VT8wrz7/D4G/FTNEjGola/41cqTt2+CECN61cVc\r\n/f1vJIGmLAKt7w1vevA3eQ9jr0nml1c5H/4MV9K2Sil0CmtSLZDqiYhjvgkd\r\nVgdciaR1Qiji2z6RStuQpkTHproof8r7l93+eR2IhKGFK/YCgYhiPYI0g/pV\r\nTGGX3jnMYbNE8uEE1qFFjaiALVoYoDc6Bf9sE3AFJEVwZCQvRDv0m0IFH35u\r\nnbGpdEvvHUT9xGEfgfY8+TGpiYAGIyNqiET2Ps1L+fVMTqy0XuVYBP2dEJdg\r\nb+JuV4o9IPqUf+TClln4VkQVFXxhxcRMONt9HvymsnxHiJ6ydWBsGQPxECCZ\r\nVQf33ew6Q/2j98Uxy1Qg4LEfMdqx7uCc9OCI0qvAu+ESbF423pPEIXks4NKn\r\no7Jr/3a9aO9FDRsmUE/DAol8d7u0FbsXrqkFYjV4w6pEdHLVkHn2iqPqyZ72\r\n3EioLoOw0gXiFzRtVtxd/3W5pf8lcRlzuSGRV+iVteLXXzg/mi2ZEsDAyz+8\r\nbvy1v4xN655kgZyMSEIniIUXG30ATYgudOo=\r\n=81os\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4e181e74a3ce58b96cc6dac2e3f3437b0571325", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-ly2cHPvAB5E6KFjE+T28wAnnBfeQa4kj7aCMz6up7uf/hyb75RqZX9tM0R1AktRaEJbq4Q3QpP0k3040emeUlg==", "signatures": [{"sig": "MEYCIQCRRRuSS0EuGIYi5roEyLbmxIUwZm6Jqmv/w/s1Gif0iwIhAOuYP0/CoRXl77YNPThnzbeD1uN69cTlGvnHFrmakR3K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkTA//cvoITaAjG7oA9CCqbKvAac7tuT+hcqYpZ9S8WU/VLJQaljEI\r\nKo7AUxBVV4w68K9S6otUL+h/Oh+P6yrIZPqIH1iFz9ijCQ7NC3Tgdbs4yVCf\r\nLWGAONmcGMktXBJqF/7OfuREKwTNdrA9GeX/cJIuWJfRzS8OMvw3ErwH4Kc9\r\nDLqwCPhelqJ5MW4bl26A5qnFpgcrlXNBecsCcJSalO/O1Tc/wX3O/Ba3Pzks\r\no1faRg6jAwRgb5YjIiH9JpWTIQydFXooQjDPC20uu0ulhEscD4Hw+LczPWR0\r\nBkng+ELhyKE5FwzSAuui/dOqyEi6IgAeDhgNGNCCcNN6z7ANR6Z2NfN98GEx\r\n9q+AGLDLTai0aqNOUufYSzLi5ZRexA77URX57lpf3AdcWxC5rVroiJx4xsXi\r\n5q2/5xMzZXvCXWW7jrgrxcCrAPOF9o/SRTKNcZcv0spFT2hLreB1Oj5H8WW5\r\nVByPvbFH1YRUpLwo5MyI9uYJP6wxXyT0xVvkBvZGc2vUelaMNAKL6fBRhTix\r\nkx3VqUlYE6qxRh0OFd9gr0U5KclbsFXabnFlqOkM1hPk3W0yeDU16N3471xO\r\nAdgxGFWT9mDZkhrn2hT/h9438OpvMJnNG8Iffxo7mELftlM4Nx6lPqVJ+R6S\r\nEe/ngj5zZOBcDSOWhu9KfNc2/dnOw+O5Jio=\r\n=SbZf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "616bb811497723a59370078c433f2ad6447e8c1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-eYNlfsosIKBUQceg+VOD/Ie9GAYs7IB/HlsE+cgnxKqvqUpGJo5/epq3092xxFRpeqHtKwQSxjo1OI68MQD2eA==", "signatures": [{"sig": "MEUCIHK77CpVJS5JAO+8PowNL3Kf0HthUrAjWhG4t11FIwY+AiEA1jmv2ti/bbgwMAlAT94L6drnmu69cPaQZYzQXeE8eSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ1CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo10BAAh+BGPU9Tw1vrv9LPC/6lbzGCYK5sq7/vfsnSUJuMxCu0/GvZ\r\nrY6eBa1co3YzuU1MUsSn9MOWzMbjwTkpOEgqSa0qhC5pQWG5gumM/Vt0mB37\r\nLgtzyAoE+K2tju23RJnKQkxSHhbLTLVeFqf2iUP7txV0K0oHfz+5t/yqJ3d5\r\nbPCTwBaKdyNss5nAGMYuHcywkke/+Pu6IxJjOC5TWZoz2VzPghAAwT9Ynd9Z\r\n8YAnChcN/WLfLf2qzgjwqPsvUMntUZ8Bqauvzc2lII/x4HhPIRADdCZtDxZn\r\nTFnEvLieFebEtWgkbTlqiskhJQ/4MlPaYpS22M6WOz2PrCUuZWnZAt4k6NmG\r\nkeo0DX6vyDvW6nvm3PEgbUnFBpusdXn3Y51qSLu9Sz2ZCersCev10eKkEZxa\r\ntzLNP55aL/k83+/WVvEHXCoUlZsdxkgzzFcCrXCqzIUXL2uZpl9MiBBw/HL3\r\nAQJSV8sV6mrxNL1qgBEixbzYIeDIt9TmH5P/WUSs+1Luri9quEBiwzW6OKc0\r\nqP6qiK5sG78r+wiI48/jQWD9U4zx4Cf/LvCpHTrtcyQM8QFAe4w8l/97IvGl\r\nbb5/qPR2IJ+M85W2RR0XevL7WIqvOqpGtxCjfhs69pl3BEMSvGP/qhHeyyXa\r\nwpj7ysNc6oS1L0MwliiCIdsiXVXVyrjlZ9o=\r\n=tQ6+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.19": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6bc40523f332a4c92ca8f077cea21c73fdac2b7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.19.tgz", "fileCount": 8, "integrity": "sha512-XFc8OtjVHs0OEA7NWwPyHRGskyUwRbdJr74xAiMH4YvVBF16j14R77UFNKsjXGIf/7b38axs2rZq40NVtXpuuA==", "signatures": [{"sig": "MEQCICKak22CIxhTL1ROgT4hNdWhYrcOy/z7lDfJn8DVAQgKAiAkZC7RClKjaFemwsx3aDaQYIZvNkDI4cY2jzbme+kHfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosig//ajw5n8D4lnzkVTugMIP6qMyjrd3jh8AnlDEwqiw3/gLxjwIz\r\nvup3FbBh1C1EqZotLwz/Ug+Z4WzYH3HOu8qZG0Gaff4taTpQs6iENYJLZP1/\r\n9ZyMdVphvwOw40eFGaoWa2pKsZ3s51X0wihpYSqQICl5xpxRr370g+GK9TUB\r\nwnxZWaean0f5oRp4jI+q5I2fFDzvY0/XRCh3qVa1oR6jveNODmDdwPDac9CA\r\nwYo+1C5fOWmu/tUnZyQqdHTKCFNyXeiYClCk0o3qlFrracwZ0wyuLIb84hMT\r\nsyxppwXAlEwD8BmGDx6rORkkZXWjywWp2L3nMLdmiljIt23ptvZYmjeHWNIC\r\n2siPBXkBn/QQS/fnIPuZ4rUP9vHo1h1h88qi4R86C/lxaMVRzXFfevNTjxoZ\r\nE/Bjea6Al7+2G1llcO72WIUbxKtIHoAz43S12uo0rT2m+WgMy8EuXq2t5cBL\r\nq4A0SPuQNFMW+9Havbq/xQZzPAWapkEqgRn1mHIOP0yNuIZ95PUkyK15rqy1\r\nZDDf5TP781N4W/nwqr2f9cXO6RoDikzKn1nqij4zZXe9gaDHGyHwdYljfDKN\r\nc0BuqoWpDzqq4iRcolf4MbF5LD/f3o2TbM9glg5ZH7EiFdl3Lrir/c+3wrZA\r\nCDYmk6WGAzdaE0D4dU9Bo7cCFppSdE2pxHU=\r\n=tGyf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.20": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "264aa8a00a1ba9061e716b10bc65f85561e6c979", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.20.tgz", "fileCount": 8, "integrity": "sha512-9mV55FcJl7EJ/+Ao82OHsqUNZDd4RYR0ybNZp4hBIJDWyXZIqSWUKuX6e4ZIk3J6U/8RcZkxG+J+LyQ5AMtz9A==", "signatures": [{"sig": "MEUCIQCeJmACkAwyjwE+lGuoJvNFVbBNlAocVHm/CYaoARik0gIgX/05sn22kI/Pw6JoZEKrZiDeTQef4KvPJamovX8wpbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3cFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpisxAAk4opkkdoEV4Ma0aAcAaS+cBydrcyekUQ4ztI/yZl9Dk9LJjq\r\nyKsSrpG0eTt58rsoErz+KUjOGkvRXW23iU0oJTYjE4fhxKQy4IzqQ7pFPD2f\r\nQPC6yclhjr+nDy+JMoJ/gps/Vq16yxxMju7IKBw2JpzLhNa9d/DxChGwXWIq\r\nis1TvQxGnRlsWCctR2Gnve+ziEFzu21CmJICdfaIwcdcP1kzEn4AIHa0EWOH\r\nULaupRCYeM5LoeumTCU+wBlW6m5/QjEdtFNV+QUrYi1Wg8C7MpovwBr7HtCu\r\nTNNmwGfwrAvI7LSOY0XRn2dkmNrJpqvvNWnOsx9w6tKuDU+xz5PHlGZLYuVS\r\nfMs3DVGyw4MMe+u6TpbtB28nZqERWmGJfBBak0ZaEcbKFGXRuRoMuCZrUFH3\r\nxCUDGyS/ySwLqlEKCLqXZjDZWAQplgoke5nBX3VVO7/oZqBwem8UoABdx+Be\r\nJZ5e5b2QpIo61cr5n5PLfXKVkSf18noSBybN8eVTrUJwnI9q2QhQVWIbV2VT\r\nDiYeYUTAgi6tfBuC7GokswZLJaEuptjpfd5uD/lI/OqH0eN6eUEYHPeWDcuv\r\nowz2yV0G5quO5hM+qtcjqbXvnr3cSdUmk7JuRPzTYeVhqZZKGxYOo5QQwc7z\r\nAZ/cPG9MKQOQrbikbIAh2lGYD9bPN2x9TN0=\r\n=ATzI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.21": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7ced3dd1458c24d6ca11f2826a9ff61b0b47f075", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.21.tgz", "fileCount": 8, "integrity": "sha512-UdHZTH3Ro5nFGGd05PXZSGCUDRehvB8VKfv+cbzt81ui5NDrJcQpE8sddJXHRoUeLq4W6c66apsS/Cg9I3X9WA==", "signatures": [{"sig": "MEUCIGbifhaSnYCWayD4vza4nib7KxqQCP+4L0MCJq6+DUodAiEA2r7DCdv+AWqvsK3HFGkIQxY2f2q3TpiXIr66EU/k3P0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2Uw/+PAMMbg1Qi4JDZTdcMJBfCmc2kRAQP2pASK6VbJ3obAwKppLQ\r\nZslNBbM/xOvBHw701Gso57lPTRAhkXIJ/cgmq/hUHaNfvO9RxSosBkaLfpr1\r\nIVAzewKvuAeg6VUFHf14Hx8p4dOPlEhqCSmF5gbq0sIoQid4nxXYekaRbfJ9\r\nbbj7sMM1LQfoEz43fhoAvCuaIbsUM+jvpmjOBHxVJ/TvGRRDrpdrWGJAsoxm\r\nKeq8IsSERpxJ3MK84FFIQ8AjeftGWOm44Sc3pqBpzHNoKVLmQBfL5aCC0c6P\r\nqBkt28dowF5mwseD8aUEy5/a00Ha+/FLF12mFfJMme2FaHG4V+Hn5CPVqbsw\r\n8WvAy7j80Wfucv7mWRJ4VoMktKnbLcmXBM4NF4ZdBC9Y8tUI3WvOXlJ9Zftb\r\n5/JHnkh2unf2LMV1nt3iVs8/1QLcjMFhQpduQDweoLfjJ/k8Kdn9peouVJnM\r\nqv5h0s5LMGq92XLCzyQA/ei2zJA5dzJckkC3CX3fhlGBUNtt8n2ibMLAy8A6\r\nFqfnKsXlGs7bIjEtnrS5vsDhboG76hKJ0PrfBB8jOWjaHqg4Nx1KTks1/MUT\r\nVqiQRJ6+Uj6cYOm5xpmVK3KfVVpVm71dkCEGmdDZ/a3EzStU7yKgXgV3Dx1/\r\nNAIB/l5XvgRDAMlDOy+wttMD4LPuq7iIWx8=\r\n=UWnl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.22": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f9da41768472ebc732035c6b7532b716e07deb00", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.22.tgz", "fileCount": 8, "integrity": "sha512-BeKQ2O1CUEpt0NkoZDkzwjkKmdEfqa6vt/ffMz8EFxV/iYBk9laHJxgZs6FQgMfAWNAknZFECkc1qqZxCbqIuw==", "signatures": [{"sig": "MEYCIQC1y6c1UVxqAnq2dD5Y2M97Rn47T5JS32jhfm5+JDd28AIhAOyBc+wHjlM3ELguuHSSYy+IhKyTYsRWdAx4AW4p3RFn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrLQ//ei6B3QbEeDhIPO+1oNxSojOFooAsHI5yvCuETbAUIvDu4U8O\r\nceZHuqD+sopHMuM3Lzq0NG7Cx4fSJaGZa6skZSs7MY3sUFERpU0+qhJdmSqM\r\neq1FLVJIXrzaDCgyJbCYVPkTkhK8S8EaKE5B00q2tOJOI+AD5nNXVwx1KM+a\r\nCZMuprxCqOnbSRdA51+bANi086tSitI47GzYjsH7cMyY2bQ/A7ewElUVOH1X\r\nuetGqANtyUfqR8SulIWGHOciFL84hGnvBo+sMrnegW63h7tk+3Gbz0WnjAzl\r\nz03MKBxOE17inTjxLC3ucrGQNJJRX2sYLlEuSBSA2Wi8tNtReA6EmpKMAG+U\r\n+giSBrN40yYCWzwTvTAFCiAFu3q1tuHjhdvjrEytPJ1xiHZmV4fum9m3YgUY\r\nQYLUGxo8ukaVsfL4fxGTDxUAzCmh/CuJYrEO2TYVaKXJRUy+h3KFdaZkzw3z\r\nQRLQrk7RAc090cSFWUWOt2pGYQIv+OBrG6PfzDV0PLC9jnDUMckul/pFRkDe\r\nNesvMzWeyKH5rXfQtiT59zaDRUez65i1gR8y7MYuEBvMx2f2ifULsn60Aaix\r\nnBLcOSHBhks3s++loRl+xv2ur6KS6Szhm1knbcCcq2V3SHqgBPhHTRJh47Gv\r\npYQVokK2Rn3CBs+03/9cp26BqhjPw6h0lJc=\r\n=nGoz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.23": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f17074914ccdfe4f4177f04fb84c26cbdd120d4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.23.tgz", "fileCount": 8, "integrity": "sha512-pcHmVWU7mNCOrcLGebbYCRoQPZnrSBGIb3/UzdG2sI5RzMF6a78ZypwQatIV4rdVCIKz751TKswPKccx1oEiDg==", "signatures": [{"sig": "MEYCIQDfjnf8hkZd4pnSapboKevPcTy9dO/hTh5zvDa+Ifp6jAIhAMPTs5ggTvlNkNxyOMAwe+xGEt/uNf1RLE9BSx/fgj5g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKIAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEEA/+LAgsHm6+KJf+skWO/6HDtZA3RkWQDLzoM25VQ23UaojxGCCh\r\ngaPK/Ah6QPvk9mBlEcUaPCW/7WVvqYbodx8892lWc4RS0AxZGUuQlIcXhsTs\r\nBR+idQELLbHNAfLIMraj6tjhnyrmIVZ+QHgb1M+kf3zWrpXbAGja//r0UAUg\r\nm/exMvucRo+4Tw8GVaea0EO6XF2a1cmuLLgSkdz/1FZXhu44u/dEu80ZxPtH\r\nErpgC/JgW6n5/Kb7mO9yCwfNq1ve4XRPwlcmdq1BZWj2dx7bY+la+0T3TP3H\r\nuamCztfBhw2UJJIJJmzKB0WkXbdCcMPkUXrDfewvOyWMCRSnzYwFVIWbrNK2\r\nPNQOrfzS1cK5Asg6oF6fjNSQeRLXYtdSifnWvcMdyeAmlHVW6t7VGsrhA/Yd\r\nvXrip+JgnbjoqdYd5OMWW0MrzHKVGkAwohIkKWLq+XEYbveOWpdiFJvNPfFZ\r\n0xyoybXhj4FZrqVP6c7r+BfLnS9LrhroBXgAiHM+TY3Xte+la+o131X9vy8K\r\nF/1jmpimKZzoxC9nW2m5ESFLqpXOCqsAPK4gbe8ApyUvsNzeyNyCVvdOQi0Y\r\n+EdpVNvW7ZpD27hQHG4uDjFBJRqNW1B/sXCGMS3ftnVkUyr7w+QiokjS3LmU\r\n01k7rSk1c1IcRrnlpaY/tzQelmpCIaRbLSY=\r\n=XDyc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.24": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "54bbd9ba94efab3540b4d62cc88f69b31c66abe2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.24.tgz", "fileCount": 8, "integrity": "sha512-e6v028GEUXeCVKcUZ4Nr8kkDmZGXU4Ql9iLdBtNUk1VUqRs179oR+zHl0195m9cpvSUv8uwDXiXj8KmrTeYeVQ==", "signatures": [{"sig": "MEYCIQDw+jdpPDr9l4TXs07liOYEbMJYFh5L2dNziInpOuMRkAIhAIlvLk1R/1FXTXdcuqMx+B4QjdmxwK+73/LvujtAxJgu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLiOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJRQ/+KpxUGR8QLWvN2rkZKj/kt/adRnzmU/mH1v1ktK8YRPVRVJLY\r\noOncxoTWAPB7SPpflbB7zMZ+J0ngOf1NPGo1vDu4plgPX92pxMZ1xhGtA9uN\r\nJPD4x1B4iEeGt7+33nCqpCCSqevPDyeZVercvXT68+ZnQIwAUgKMhzx2eoD0\r\n7e4oisbuKSi73qT5SvXI6ApfbWhcuGzRE7NHF8DIli6NM8EYkJrDgKa2afgS\r\nh9nvGgb6CBXTi/CVOLdco8HVheaj5Go8f3xaDa1+o375Bw0Ichv67aZmvD0U\r\n8XK8ZZ32QScoNziEP+sG7afTffPAov616b3sJQovQMwK1i5qJTU4ayespAg8\r\nGELx3shNxCcXbTdK8oCAFF5owyeRJwA0dzKjq1EpH+yl+wra67y5dVQsnqra\r\n6S48bXs6cSzE4pEqx6W2XmJSYOGQvu7ckMw9U8O2Cx/NDDfaRkAOVWw7jVc2\r\n1Qs/lBvMHhlXSIOUiHoZgZNxQhYEj1gcConPC7vVE9qVfkqcdAw5co4GTKBZ\r\n7jepEI0NTIHUWskIVNuFyz4ImMvI2Z4il2pxqzPm+YFHUUtIF+chuCDoVPOc\r\nSFYwbRwglbt/Faxhy4xI0+ej26AcTctr926DEogxnF7qreWOa3Mcjxw3cJ7w\r\nygdnfuaAh0qePRffmHlWPWQe0UOZIPPxHyc=\r\n=IVKL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.25": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2d003264f69512199f7d10bbc30d2b42cb60d360", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.25.tgz", "fileCount": 8, "integrity": "sha512-mV+/5PYN9irv83aexmdBBN54m32Gvay8kjz/KtbkGQDSVnCYwEIkVrHsyQtMdviXg0gqHUIV8YnHKNpneSSWiw==", "signatures": [{"sig": "MEUCID6Z9mJNdw/QkPmmDpxS4GakHFfJIVc02fUZTBP04sUyAiEAkD7g5K38Y2SPncTIB20M50MlEk4kcrldEmj7aPqs/Hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj5FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJexAAiTLd7N1JoKdk+SMnywJQCC17TOGOcYzzc2H2ShhA1s8q3rlC\r\nxlp+6tAKhCsB25eJvHLTjBVriBhXznQ/tP8aCTHSaKko9AzFi9Upg5x5WjF8\r\nz/KxLj1GfVBNJPE4uo+FR5dA8jQh3FwnT5U4XWnkejlyCT2678GAjxJTRdf3\r\nQ/Bj3pn0QI/6/DfBOB4hsozCDTHUczVaN+WDLxAPTYNvJDSDkQQOBxeLm879\r\nObGedBDKk2dMDWTV2klPgGRLnpqSiZaAOg3YK5uzJ9yFnLNuOESAkriQL7Co\r\nKmSp8WWssgd9RNinPwjuCinSY0Xi2g92E3OKvB0jJJnvkmHV8Kiq7CmDcO64\r\nbcuEE3cn9yCZC/MWGapwZdeqXzj1dgPi75LNUnNBFjEFewRDYcYXBzk9GRys\r\nON8W6JPkwEMKQGi72HBtn8Zi0ztLZResVoOeuW5flAMBSlN/MTAULIz7rv6i\r\nXzpFoRBg22lsYaVmrzOH2WRAkFX3sCQSNIKLCZ91ibOi9UH9OrO2HdDknM5g\r\npjeQR0yWuMm1YipQmSox1u3nsoBXBA9u3VUq+k0q4EmRq4AoEcBZfMf5jpHW\r\nEW6FD0Ht78mXq9SJtjSOdTHdCk0gXm/BlkEqziIIK8MR5tOp5pfSl47XgeRt\r\nO+7nLPlC4xUdM2839b6bjkLRwGu0S+fupDI=\r\n=O+zx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.26": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7773d91d925baaf820c842fb9a83bacfd731b475", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.26.tgz", "fileCount": 8, "integrity": "sha512-mtNL94YHprgpFKX8nk5GrALbH6FH0tkHGqT73lw5BrsEnXtPSciYj7pgZrQ4anRoadEnvEex89eItrJERP9sTg==", "signatures": [{"sig": "MEUCIQDpmtxcfwd6Gb4Q+ZGLfczdRiceELleROBCqoWbsBve7QIgbhJaWs5XOG97nx2cwaqe+DAjAkX/bKarW95zY6AeoTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqxA//a9GGKGHgVOotnV1VIakyLCLf/JisPXJcQ2ddmYEAZmDxcfW3\r\nhPfNRrfe8VSfAysRGXL1ulsJj/RZeLnX5ZlfR5tkpsN9YeJ1F2ZApEWuBEpz\r\nmw3gI0jlpFQbgLDO+QnLPzboFFlce9JhGbizb+tf8E//LcsClFNOomooORyv\r\nMq7BQm9svW6MJeoKAKGHwHuEi8MKoXYyTenF5o+XeexMm9K/3EGDnd+wN7G7\r\nDdcUnok4/MSuej6fNL74E8Hx3M8GO7gWcq/v5UP0/xF7oWzt6UrNLeSCc0Yq\r\nbOu1XdS4+N+UpuGQx63TU6BqhimcYfs9b+tqbswDjfCK6W5e7H40FF6unSpf\r\nLkw+7rxm2NFhFhzOVfdkFcVVgxlmroxE/Cd3ANqMJ0f2fBIu5DBY7Jbm1pW7\r\nr20AzSQpq16TP7+125ktoKP1TCt8BNeZM3mE5p/b3+tqDrCdpEB+hGfjZmhd\r\nyUvqXjZTRPMBgvttWvl5Y90wZ9KzTRAxIpMedn9t0TyFGa7kmlrVPAXxoByN\r\ncU+6hHZIiAfGplpgxkWnvxUu2OVzDbDgwkJeN0YG/iwIaUvfi1SE0Wv/JxHk\r\ngkIeW7HwuVfjqbWZPrKs0GLhPkDnad53wCtYF+yXALcUCu6313hXs4pVv9Dw\r\nL6sChVvYbzApSHACN0IdSNFLTc32kTzSa54=\r\n=fjuT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.27": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6b663baab926a8ba31b966c972e7bf7f9dda7894", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.27.tgz", "fileCount": 8, "integrity": "sha512-MA0W7rHNxB3vtyPtfB0Y85o/3QOsXGcOsBSZ3sOBEjj6pkLZbj6so6/47x4uUbT0TC/dEk4IkbmIwCjxEJuUwQ==", "signatures": [{"sig": "MEUCIQCxCByMy4TquWTBkN2nNRCc9Uu9SfHDvcaIYgEbFdchRQIgOZ+eAqMJO+7kG9ErRkUw5xxq3jTdP2JrSbOfiWTSaFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ3IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT4A/+IlNVMBbTcftq1fx8kAM6w8SAjVXSV9eW8J4cyEz3tc/BkOFk\r\n3Jc3tTRc7VdWS1pSbjZJPewmKfqL798I/Ex7x0cPUc/qaOpnL2jxhQAWosOI\r\nrwnr+wP74sIY3tnc2cxVlimOh2D84pYm2RwHFPXot32k8zxjh6SBHGr1NF6F\r\nJUx7Uohn/PTK+Lg8mRn7arjWQy84exyPYhUIrHpErUMxNb28+R9kIz2OhRfB\r\nd7GKH4dNCYqhUB+Ff7lHBEE9emf8zl/T+r1tV16qkafHeOhKJhebfWq16l+4\r\nvjee0YYw9rHSqktTnus5NQo220ovVy5qoyAvyxYs5/jlP0doPAGt4zgMsYLn\r\ngWgWOUc7iJxadOnt4SBgQ1dcTlJfjE17dNjgrW0aRDE5r1su6Y/DKeevF3aE\r\ndpYmHAS5xNPC5Wxt2h63pFmX0lS1wSlMrtiejvYDjLvNbPzrzMceOwq+LVkc\r\nYIr4pmd96G4dk9Y1bYsx8rMwxsFEbv9LTCsrNF/pG5HlMBvq3Pr2bClVFdRp\r\nNEfQo46at3pHDUbuswvNJ1LQIvWURxARxWmIAI4z3fbP/KoupJzHiylO/e/l\r\nNVcMFV3rwdnN0a+NlLNYxmy0gsg5LPHSyHcnDEec5Q1irQSoM3kZPvlzpFXS\r\n6tLklvGrI5MdAUdNvOfNimz8pmT7AfiJ8gU=\r\n=wTHW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.28": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4c690795bc5982455e08cbb64f4ae2894697cfd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.28.tgz", "fileCount": 8, "integrity": "sha512-HCr5cX6y+ndt3igcSIERnjqIn+GaP/8MVF301FLL0PqVM32rFl1YQmqRumEJwroOIex6GMicVFSObsfNx1etxw==", "signatures": [{"sig": "MEQCIHUlH8N2qeTlVvzTjPzC6X0Fp/Q7ohwc0KXRJrzvE6uwAiB3HicLdoZyKmo28wn59szWVfbEqyq7qsNz4qEBYjQNBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildOOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolVA/+J/LJPrAqreQFonUWwxGwmWDczAxVMKbukvBkjRcvvhaSfg4c\r\nkkUY0ThkhRSg+Ejh79TY9r7tdXEfol9s1XHHPKty68GlBa107Sb/2Q0AT14W\r\n2cdMcaGeuE9upXXcVvDYyXtBYaNhT6DDrMnZCRTU0HrFOXGQRFUJbx+Qqmqy\r\nOWFMfEJ9ElbAbhRcRVb3D/xIgvK6Gegr1GSW94r4bpi84p04F/LIpTfdL2a5\r\nzfN+SVg19WP3HiRcQbvHl1E7Oa1aAF3tlHbenqi4sHl6DSwlcYoVHnGa7SCj\r\nQFEzQWPyub7odD3tW76GwUl0JUChNv0SoYhX/AW02J35HrnfttLbZMq1DtUy\r\nvLhcE+KRa7iAoZeWXa7o4pHkEsJePF4XCam0X19KweuBqpm/eY9KmvGBTKO9\r\nTVUg2JWl/Zjz9ate0Yrk1GvKrHMnxNdQpmzg7HAHLtnF27zGtANRVroEYdD8\r\nSYqK+dXFYivB5yddwqO6jOnS86ljiCsdvG6N+4PP7mS4zIZjYr5HEL/nCX0T\r\nynU+sjwfpv+gseZnoEVb/n6oYJdHTI8T3v53dzked0Ueo6ZkIUSWtJuaaYJq\r\nEVr7htuv46bD3hHAs6y6syqVvODQy/Xl95dTwKXC8m8TQVae+BDVPJzB9Zy6\r\nP3/zLYdGuSbU2tkLny8LmS774+r3S9y/zLY=\r\n=aqlj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.29": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa2ac7c8ff4f827bef4bdbb8b4e67f8a2c140865", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.29.tgz", "fileCount": 8, "integrity": "sha512-ku1Aw9ZxdJNGGPJapXjh92+QxR0cypA9WUijt6K0rIDZnigGXJB3ou/yDTr9h5sbMPvVJWSM74fwZTweoBTYBg==", "signatures": [{"sig": "MEQCIEeXh//NyxAfXvhZl2fdhkH2/Av7JEOAEjFZRC4wrnorAiAiPP4PAb3sr3iUoI5j2Jedm/nD2VnqducxUUEGkB5C8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildsCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjjQ/9E1LYllKiHoYySn3DR9ME8g9dmgFBN9ov05dQkPJIXvngpD/5\r\ntHWGFyv5QCX4LWgP+XogS+9bWENOyZ4q3tRpdB0mmsNXxjRM2GrkMcpHE1NT\r\njq4edr1IQBqs72OFa0X7v1GdKGV7oCdoamiRkxcwz3/ieNg0LbUWNBY++Y3H\r\n9vnCe8XyOUzgIGGJSZHnLd0zeQdaEZTXz5S7NUf/6NwNQj3tuyWz6/pWE2q2\r\nQOieLFqHpmRbvwCj9XwJX+OnQ6yegn6fP6tYkrEYB32Vkdnu4AZTCfppe89b\r\n7KkF1WHxqJVnZUd/ZohlRW47hr8GvEztrwE3z4kE+6O98zMGihIN8n0V28tk\r\nxtFdeHBnsa32it2PGQWTgROhKDAgOyCP6TS11qLMfh8Y96BQ7DgCiEvzJc/f\r\nQyVlutFWNqz1r2YGte/rGJ1VA3rrBjtHm7aUhJpbgSzeCzdCyODAwWAZNyLi\r\nKl5l3dm4uyTHUhGVGYU+d69SZGkDRJIdaQ7SKTr2NFWFAvoksYsGYu9QK38f\r\nHD93XYyisJkNVxO1sJWz9q3Y7R32QYSrVuHEE/krPHgbqnNzpbvGhd1/EWKE\r\nGhdXMvij6aibk0MO+vTLYPgJkDgvUOlxjYxj9twVJHTiJI0CtJbBFZooxZVo\r\nssdOnxQZr+3gKQDzMGs/pIgQoPsPZDoXH4Y=\r\n=zyVc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.30": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "814af9e52a743cb0a85b73e63579e9cf3b4f5a40", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.30.tgz", "fileCount": 8, "integrity": "sha512-Lq6g8a284vVWjJxeBwYMPmA7t1NShjl8DQ3iQkGvb6lxnW+lxzTkQCG6cVCz8kwKBfOuzGbwsj18fYkNFmByYg==", "signatures": [{"sig": "MEUCIF+7zaKYwAsddme/bRLxOVvjH2QSAYKkkYrFFjDu+n0fAiEAqf0DDYtCtl2fWl1qD7luU6R8ErFsx83KgcFZHPevhdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile24ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQhg/8CXmjnfwj0iMfk+vRbTh732kU0ZbTJGkU5Wlx8cAnNx4utcc6\r\nqkho8SXyI6KvCq30Qo9N4Gj34uNHubRk9J8aFA3zkJOLxxGgJGkaOdE8Zj++\r\n5uMya1lTNgXZdTZQTa0G92fnCF63L6wUHPSZs00KxroSbdb1aNWdbruI5Q68\r\noX3DRaU2cju4c6exmy+I/aIARgU4/jugxDwDVvnXw7vGx7Hb+9/qxEVWEzCk\r\npnqmM+pD+obUXPgiNZS1RYD7QhWrlFwpJKIcYnVY77FgHURwSiPme7YZgHUk\r\nmEDWezIS/rrt2ea7TdzoikoZOlC6Q31wtH8x98/et/VE1/fxjD5PdADIBVGF\r\nQHKIjt5czmmHRLwEduwmo2Nfux8okX+K0UVzFjDlSXFmvEiu1OFQFOdE3IE6\r\nHAAQQkkrm/IA7FbBVLMm/VEW20PGo5tcdKL+jQPmHHMXT/1jdOAJAPnyDG3j\r\nLVnhHpQXpKc5FiYq/1Z5vXiuNZQ0MHLq6bcQcvAxipEmAowC40k7beYeqoDK\r\n9+Ub2nnmbYVe0S+VXEUBcHOGrpYB+yt+d26cNnZMm8l1Vglw4isVETHpBsl8\r\nnFrE1Yx/mv3ooVrVaX9umTahbY0qjXM8cgDrkh47peTWGzVFn/RekR9UY0JM\r\nijaLq/cajNXW+biuE79B42ZhGdcgRply07c=\r\n=8S07\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.31": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ab64b427b5884a5ee8a446f3ccce88adc30f8068", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.31.tgz", "fileCount": 8, "integrity": "sha512-rj1zRgCQ8oehDtzqJy9hDAZNLVo2boFKef0ShmfyHs0B7BX8gTcARKIQipznFyHiDlRT1FP/kV2xOyn9E0Mg7A==", "signatures": [{"sig": "MEYCIQCEgIpTv3tZTOI1khVXggfPf69m3zhiFbkY98MZElKAsQIhAOq+eVVNTBPs9RRTQgZGW+Y/pb5o6e99R9WqaDine7jt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV/A/+Jue2MjPZDmFA9zS+FUHIuoz9XEjRpIC1PDnjrDW1jTDVUPtn\r\nTe3OwQRbln03fW/yqgne69Sf5YIquWEbwfct2bwxjPzOS/ebDIzbrjjq9TYe\r\npqaqa3HczrfHmu93HOqXkatAEfW71PvewNhFN4901BiYCdv51iTiwQS4vNL8\r\nPlF7BIetZADD3uOAq+9n2UY4+7ILw3r8Onoq91UJ4L2i56NrOo4ToKgZZYDX\r\n1pMpeVkfMAdPcplu2TOrE/yUFUih6aO93E1XuyXAxB9d4dXq6/xvDjTOG/yF\r\nWAusbfXB/SnUJHMDE3xsiJIMnoRJUWy8C1LYCJ8CfzV6ls4rOfXCMVIIxrID\r\n5h1fQmu2Zx71Gf/ykcCmGCs771njefRvSp1keOtC4GnPHuzdwsJnddMLPC4m\r\n0xzVtaDwPSHmFKKl2k4z1F/uBGCmlutEe0ftB5BTOQvBdJEnJWpixMZe5n1E\r\na2s8eugesjoSdmDCy0kpsb3AC2O8WOKJueOYrCgT+8vNeHTzNqQaNMQiU1MZ\r\nDCzV2fQZVIGKXBJuKxek1RXgDAdUSwwf+iMnpMclnNX4EonlC1QSJQdCcQjc\r\nSaFMpYZmEVQj+AkoPgOyuVjarSc/DHmAcQKAt6D9KAY+SqKr5sc//6DlHbay\r\nEqlaAg5oS31WmkxrO8DeOanF0c2EbpC5Rys=\r\n=0eSY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.32": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "65fd0124aa2c73157a82e40a2cd6174e7e24aef8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.32.tgz", "fileCount": 8, "integrity": "sha512-VVrKtkwP5bX3eQEKc5CG7mO3LHUfSkCUDJa+7QVMMXGyRNjEhebmShwqzOphJF539+mI3d7j54/uwaRyRcTbsg==", "signatures": [{"sig": "MEYCIQDTaWs7vq8p6kAX0Jq7NNXfiUtrNyNkBjn/62DPPU3IgwIhANxL+v+9TPJxF+KQXLtU1P74oB9tI9YodW+0jNRVOW7B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQLw//cBQnjeVCbTisVp8wb2G8QJSbfYEkMWFD1qWq2ZmODbW5Umnx\r\nk3LZJRf57eKyUxNZc+5e4b/qaj7BkbqB8QJjZSkFlFQSVbEFl8y6banxnkUs\r\nj/iuG4Ud3CGeNyTwnPGEbybkHfqpI32Sfu3LL4VDubHk3Lt3G7REaeqzcKqi\r\nF3qMEE2Wx/3xNqIC+8DHAncDCkd8Y5ICi/7L6LBdB/DJMJnfuVYziqMcn+O6\r\nxMMz8fS/ewI1dBESAXFBvUDAk4ALXhcF0F2nX52E/ECmZvGoLP8TDwaI3Wby\r\nDzFmLEZW3aXzOQzQohKXy9275SeDdkyO0pCSnwn1McST+Xpy9aqW3cRXnJoL\r\n4i0KiSiqN/wTRvXJva7zZfzns5olQrWK8tFvjG8c5UWR4pERc+G8toYfksgH\r\n5/uZBiUsBcr33qQQGIQvaU7uBtiBSqFlzZiP1t4CkJ1Vsq9zQjnPcX/+wOSU\r\nh4kyIWxxq1nGxvU9Lflc6laR4BEUQ8tM8z1qfgZSfMFqT2/fxZM54i3XGQF1\r\nx3Y2R+4f4POcfL0rmQZYsQZPFgnamiNXCI4Po3ILwszpAqHr9LLEeLoToQix\r\nSgrlDOwVh6pWxvOJzHRrXRaPR56+ZevUh+Kes43Lfs7nP+r1twyfmcf/H4It\r\nQ6dl51vicYE1kaGl3KuoU/88vBYsPgJU9Oc=\r\n=AHDk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.33": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e4c7af3733ec1702074ca76383a0021dc4601b18", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.33.tgz", "fileCount": 8, "integrity": "sha512-+mBUoB5EOW/WOU49bFIUlowyA5beWvdwPS60pMn385TK1X3bhJ6cTHCvOpoisnXcxAVZwQPu6Cb1lERR2uEj3w==", "signatures": [{"sig": "MEQCIBZbWzUvM2UB0opsjk/IIamJlsWBAmvBYbXPy/zX3jwXAiAKnQzhW4sVXulRsJ7lD+sIpQtM4X8S1r46mIt++kGHrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHdAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCPw/+Lu8EWW3UH1MSDm+gjzgaxBcF/tCPMMm13NQPMLh2FUOH2HrT\r\n+mcDKKdkIk+yNtQPPK56fIIJV/+NWXOVstrbzivP8QGCqiOpmcjab/u2tBuo\r\nf8lkpz1vvjavQqOj09jjxH0ryDCJDMnYUs387Xfx3P+zDySFchzAwA4T5naf\r\nMYaLPgRWMA95b4l8KYWto4S8HJANFdo+rtACGDSDvbVAlhqaErMEN2WKkcrB\r\nuQklLFnFAHoV4rMr+Dk5HZyHc1n51P926NAp94G4z1PME7oX1j9G4hgQz7rC\r\nODxY/jtPtFCFdULKP1ObmutUh9FyymgidhnsQf7E+di9joWLtZ8/8b/8n5Qr\r\nJHviQiMh7gHMn+2A6UdE2Mg17WRB6Q31YC55qgR/5NuHO2a1S5iOKokukxNi\r\nAGpftzftBs5kTXtXrqDJrlOgVUTbcpWZNkvR4WB/zdGPX6wAajeZEOEVC9n2\r\n6w/dxve3wH6Hb0Gh4AzpTQDRxabkh6un1KAyxX28KuL64QTbhoJyH1lgvR6b\r\nkehIDsHzhmnPvPFCULaPtwWeU4CWyVeN8PxsxS+d6wXhwUCTcxv/eMSMUROG\r\n8MMihDPu9xXu2J5zIUFMb4q71O0J817DfBIKcZ3OHO6eAybHT1TWQjuhtRKZ\r\nAMZrJvFqXH2FGRjQSdNvtpaY/X5vDDGhjx8=\r\n=K2SG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.34": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13a57774896ff4b04b44a359f411316cf821a3e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.34.tgz", "fileCount": 8, "integrity": "sha512-oOJ7rHiWX1Q2REZ3nZ8jM9uqZWLhtogxz6GoRu25afmFFRRovCWa9FM7gjINVMRj4w8jd4ga5AnwPj5VJrOSgQ==", "signatures": [{"sig": "MEUCIQDjBe4h2L4pFAezDcDWRZNbzkwm3pfbpsGO6ToYiVsbwgIgdu8hMzKEnOyUW8yBSVmyIbdDJTlm65802Z7wighrgWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+wA/+M2BbceTse69WqAtfZwmMS80UHSRnV7SiygZwchRHA7KsImwV\r\nDvZ14pui9tP+eRiZSKmzRJUm8iQ6tK8gQCWhitDA0+4KPnl1NegG5KhBU0UI\r\nG/Tnd9LI3XE/rw8pHj8FxVR3iXx3cDbXP4pRvTVWE1x84XOu2J0THzQ2YWC9\r\n8vQlx7nGV4pI4xTr/y2bH9VhSPJVERhD0EM1B3avPYVunQsfuE3zeqBh+DPs\r\nmB9aYUSatclmhps/nnwbd/X/6vMRI0x5B8C46Z4QOtt5RTZKAZG4lQupdNPG\r\nJPChEh6u7kO5yKQU2INehGvLLs45gvxa3CiGvaKWR8fiqD4qlNUWcszQRUA/\r\np7JK/8jvXO3AZ01LfaFC/5CM1BTNpHt1LqNUD6Oph+bR7WhWlqe5cktsx+iq\r\niDSTwCKuGVttwzVebZU8frH43d11BqqO4ndxhhX/i8c7aQmWvszxYKm7UbVh\r\nsGG3kqQJHCabHHh9RnHhizzDh2ywgB7C8yEGyDLtdTifzAu4f3PJyJ5Vx7h0\r\nQQhS2Ypb+BX4w93R+SZQsLnId9hYiTTy4Z6jLSePzjLB04dNlOR/jFSI7HNk\r\nchWn1aAYd4PgiHKp5+35WicPUV7kR0/Q6FtjbMzpPr60g1qyWub+XjyKC88w\r\n0ffOKEU39PLiRXOo5yOWP89YcJhzAWT+/LE=\r\n=Wpt3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.35": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "557bec3a7866edbf75371e6e5669e1128db1ef9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.35.tgz", "fileCount": 8, "integrity": "sha512-+7dZSewTPXbwDOzpsimCB3XYhd4AzBKjwankSJtnh51uDmS1v7T9jAqv1nKgCVnulPQQV+dqJCDyJt87ycIIWg==", "signatures": [{"sig": "MEUCIBJ+m1ebb7BqfYZ2vQ3siaWR0EPBZ1xe2W7iIZ0mvUzdAiEA3rXehspxvV+DNPQD9M4iog3hS2zB2GHyUlTRs8nWXgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrizA//TDmKd/LAnQ7H75FlwiNJ6o847IQd5bc9CTMKhJdidkrthbKx\r\nqtlWCOZcXYVB1HXu4u5ejSKuNGedtcgZ9nmPqWtMwjEouNUBMu/sfwRCye5q\r\nSHb4Tx5etDy7pTT4EztFNG+/mY6yxP928gA6d7Eb4aR1bX0su6F4aqyVmVcO\r\nf492hqZLHVbITMJbU+ScWsV6svrALgCNglXWvM7TyKU4t+8hc+VPWqTNg/hW\r\nvcCbH0tiykD7jP6tcIzg+20UDyC8w7+og0fGPXKxrMT4ZwZVrC3GtmCMfhCH\r\nMWYQtUTra0DTp1Ac+RJTlx8nN9bDJjMe44MFHGc1TKeyqT20YWKJrdLU501m\r\n1FWn9Y1xTKhuvyFHlglaG01cbgh57zesOX/MaflDySGZyWvinGT/kdCuRMtk\r\nYJSBch5VFfUZpyCk6OA6Rkz7Og+H0+AEO8i4Bq8OsWojhEcEP1POr7mfNszm\r\nP5lmqNvAGNIx0PJo0f8bLHHH8BOFJqRXwxcn75tUszQ/g/ccVbTo5M+HHzUU\r\nlp7IgnQYeyg0OpHGq2d7pDt9aTV2JdaKLSnV6yjKggtKjwTk4X0BjsLWfmCV\r\nNwEtNq8nVojBp2CKJOpOfqHQwFKirqvQWhCyYmNOPOwoM0niEvEVSw+cSn5t\r\nDkApoQS150gHPeTAMsh2Jfu4MUFGsFSh648=\r\n=j+yL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.36": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f811d86f9a142dc8a57de7b77fce0a957bc125f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.36.tgz", "fileCount": 8, "integrity": "sha512-5hld9dIQUncuXHGSBor5MyC9Sbk2fv0fqJbjUuvIHnKIsMlaZNpdVf03UHeFIZ9LB4Fq51XdRWpU6a09qEzudA==", "signatures": [{"sig": "MEUCIQCSkNykebDTQ77B/ludEc5UvJRUDxXTfQADdBCWP1gfZwIgMYAntKMs/5bQoa49vfDnt9OrM5KR5QN6iNfo8bIY1xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhMxAAjcAWxg2iqaUwxGS7K+Qx4z5gC3xjy1HDaUFdOGuqaBuH/RjB\r\nlbiYvMQLiSIABzSgRhj68mmMTc5PBPj1UX/u/m9LtSgZmScqn4Zt461tt+03\r\nW8EGDEH5v3fwfvNgYAjW7EWsWFAVhZNw53a+/MQus2jzptS7DFnY3eIYxceh\r\nB58SZz/evFe1q4jr1hovrSoa2RzECseRDp9MEL/ICHIgJtII3FUNdalsq+1T\r\ngHtdM6WVBKleQ1zjPvflpG+9LCFHYUDE7sNCB6GFKNaN1aRRZvKCACMiYyDT\r\nPGxCC5TZISpk6kcEwBit1FCcAiBp7Ii/iIDyZHh72EylpXaqjhmF+gUpg5Fb\r\nWMhSqr0AL6nrfMxhq/9MHsxf4w+IEiFwCtqYTKtEejzCB/jaNM4JPj9VSxVA\r\nX+MLADOoYd/xxxSPoPefbNHGE5ZptS42vFsJ/ts2Vs+bfXZJadDX7s3YUixb\r\n8uEWUIPVasBbljMO8XD029g0FgzrLtdzDgcLt2M7YGdtRPm7MnWpgO6Hbf+p\r\n05zUDbnOTEm/wV+7q2j9eorFMn3I4qS1/s0pBUZphsSTX2ji5mP7wwlyG01C\r\n2KU9Ca178TX/k4nrbKx3ZJV0THut0h6mD78F65FxyQf1PQhTT2TMVwxE9rt7\r\nloXC2pKA5mL2/n8JmownfBGa7fmYvQ+nveM=\r\n=4Tmv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.37": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d6f2493a604c52a67f2cb8ea9d215be116a27976", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.37.tgz", "fileCount": 8, "integrity": "sha512-RwAfk29oLDEpKZ7E7L67r6tawTihrddErdyk1H6uSL02wrW7ilp7h9WWcfHRX83Dtuata0Z/BC2wAj/TR2pf/w==", "signatures": [{"sig": "MEUCIHXksaLJFCXN6bCHGo/qtUTuk/9SGSBKJuTgwurA5CnNAiEAyalQpZMZJN4NqGQUJG5ZPGzUkgKhSSCfHPtH2kvBkig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0osACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXPg//cPH6zIGqOheqF0lsG94NwwvxhkX0Eis3fnIn5cna1LRsIdyt\r\nNbtHyWExBQfegZBwxYv16YId7uZ7E9lz6RjFvMsfOBzBuz2ru0oebujmy1UF\r\n+emRBMjULKfWeBjY27L4XDNVsJr/KGPiblzx/oEUnRhfUvetp4ukcOm2yvg4\r\nlIakMBn4xKeerk06NuoKyOX64Yu5eZYOCH5o2fmZBXpIpxT48x78qbFexIBT\r\n8kYcG3ojIa0kdsRifrlcwnHgD58DoagiLGQZpXf9OoTWnblvCQbB0PDESCaU\r\nEaaTLDoXu9Ju2WpM2OtlXMdXglfmpHxzn/mLMbT45WwdVZKmoRQn7SfZIjP8\r\ndCoubtGZf4QT3JCk3bts4NVddXeyz8xOZ3EvRrvwF+LWOWvVZL1ipcoXVaa7\r\nVzfWJZLjI9PFwL95ibQ+hxLbgDoqX1qbfbpVuUiptel/UKNkWSdcCq8hyKuy\r\nHJE6u9fO272V+sJlpYhYEeetOxj32KCLUWqefuHX/qEA4R9qu/DCyN+WHMGp\r\nRGAVK3mPu4mKChcJOFp4T6AJ0veawmr8JZR7YY+IcPluPQEEZFgn4keuN+hS\r\nCzg4iAfsHl0+DVc9QC4AupmOehFcl+MlGW9AXyK4eqljXZuvRf27tevBU90G\r\nA1ki9SFJ//I2UG2BfDa/SvSnuesJe5SRwds=\r\n=HCco\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.38": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b6267679716085119ac5cf09a8eb60690ce122dc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.38.tgz", "fileCount": 8, "integrity": "sha512-e<PERSON><PERSON><PERSON><PERSON>owkpIDpjcjbNqnO9rUci6Dbdr1XvnEP/DYzrE3AcdiEtphDCI/sA4O5ILhCwFAttHurFOWhFDJC9V/w==", "signatures": [{"sig": "MEUCIQDkVhbrymGJbmXhnmBEAVFuiUTkfTFA5S7cy/NmRH9bOgIgKohuS/7H6Bcg35QaeLQs7d6WtfHEMqp5USCbG24vM2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNQg//RbNDv1q7FcNekLcxwdp74eEAW/86oaeoFv+iy6k2rO2xTkoH\r\nCMKGLscFGjvhwnZ19s3eurF6EoxMPxEjFpwA5JIZdjfe+/i1t4M2dfaJXZIa\r\nM2tPSjisluZoWNOk7zcPD3moSZF3UvtbtA+OiLzcz9ORzwYWtkP+wlgZnTBQ\r\nVrYeCUklRh8vA2zm4WBVQBWUy3LNtoLEtPtZpqxL1tMU/8zyDc+aYjLuGDpM\r\nIK+YC/B4OwOBzFEB0QX2MI/Zp2VLTdjje77dLIK1fnBgFNFgrWaVPZIX57Ct\r\ngUK6k87kz8MvPcXTy6gr8Odg291WmmNPzdGL5sKruwoAzqguAhWE8ArbnlX+\r\nz8kQPa+v8Xs7z9lsjSKK5Fo7RRAVzT0Do4I1jChpcUk0SIMoF0m7rahxadYV\r\n4F8p3tzMIWGaWt+sLPlqoJB4P+Y3WEDbPkTGlICnfbhOlYavnQOCBvqSOras\r\n0CpgROyt6v3BghJ8dvpD6XT2LFM44PvYVArKi49T+nSG4OBxFee73ddAqYRr\r\nq3sTbtUJCTSGDsQsCDzgwHQFUP3wB91kdU+OAk4Ds8AASMExDAJsJ3CWKH+G\r\nXa0TcBv/iyoYH/0jjy0t6YOuWlUffk+55huaJZBhi4dS976RKBEYYDR1AoFY\r\noY+Dduinvuu/GKPqpl0gYEwO6Y/SDIMO0Rs=\r\n=gAKZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.39": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3ced910e4b8d3c8d89b8027f775533b426d6c850", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.39.tgz", "fileCount": 8, "integrity": "sha512-U8uGV+OS1R+ijvTMgex5J1w+6p1OGC8TwL3frzR0c2rGU5Ljn/HwQdLFQK7iKHIMMFKBZS/l4YNLfJ5WJSq42Q==", "signatures": [{"sig": "MEQCIBVmHn+7YlRWAY0ch6pmOe/NhKVBIF5oosMb6k7Vxql0AiAxT202g88HUhed0IKgOvcRMS6lHSKzhenyLqJlM4BTtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNRRAAgm+qI08f3Q6U3e3PLGIr7NESToQ/DB06p97BjhZ7WuLXii7w\r\nlz8KnB/GDM13a6jDrUpLamlGdFqsVix/+rB75C5dkOEbKHjTR0e5rtIuRsWx\r\nAdcJC83yo+/woUsynh3NAGkhu3E0rsGdwsbrudw1j5l7TKbTh/SwcC3ATbQP\r\nxcckoyG+GyMtfaPJCguc4XfdnfBm4c1rF3GAoPql5HgH9eY7YPhwQmXlsIML\r\nfOGYs1H9ebi2GNQV5zITZXO5OZuOr67Z774H3EZDfraIBodyVzPbfhbYh3Az\r\ncq6BSFN8lJmgry6vL9Tw1jTCKuEYHIf9GeqXnI4tyxCojBNsFsjwWgeH5GLa\r\nqtKvOq+G4IMy96Doj4X3jSrI7KwvNfgdWeolAcgohSvHdrxZC5B3prD8a5t0\r\nMUqx8g1TKO5Te8YbibN/MpeZGGPzUHOlKdUFEBQSuinvCYqFa4owIIkM8z9E\r\njC8FYt8sjW0o67Cal+M+VMwUdYngzpLqNO14dlKlWeKLW3Q9Ing3n2xlgRa8\r\n08xw/WXsL3SIdeSqBzP7zm6Ekc1V4JKQJU72b5FOaTMfk+buQsDnl+adjvdu\r\nysOpOUR996IswDK9z6Vgh7ELUF93CoHs3878fQFlsAiT+3G1gejibEYS+Isz\r\nJDJ5/cDyAQHZtqCVuvdYDGt7sKLtBiL9o0o=\r\n=JbfN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.40": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e58a6d53aead2777e364d0662ea49c875ccbfb34", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.40.tgz", "fileCount": 8, "integrity": "sha512-mB0uCiyceWe1OnlocrmeL8ZIcbiVROxfp6yA1GPiq1xdaQ/J0zBIs6stvcNS78xGT1uqkS9flSYKo/ATpkr1ig==", "signatures": [{"sig": "MEUCIQCNxkGaEGrGNx/7PEBDg1FQ5eaCJq7zmfVnicQg6+nKnwIgav4d4XMCG0nJ3a0c6k+OevQNJUMdD/TB2MjjyZUfjwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6875, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJSA//ejOBrtuaojI0xr3LCjcWO9XVHCCTgxtqmsQRvw8t0ky3lKVM\r\nJiX3z5lJchPCGSmcmxpWqlcuI0Nipke0t6D+jkPYz5GEGikaTp7L5fr7ATej\r\nAwD+0zXBgrNeB2K6UiEa0lvzA7/uQ75N2apHGLqKk12IMdKn3PBC6PWX8fdb\r\nmZev7WwbgWLY71srOC9x/4sLF1qqPj+SQThFwvPEGErYW0ViohK2NvbVfk2u\r\ni0SsaDWBTBBZmHYCL/1WFX95GjyeNg13yqznDUfLuCUvsBTnhpoICu+jJ9Bw\r\nrKzmRo2lJkioNjbcEAmO83azu5AioF3krDwaIm81vNWuLQFkArl0R/rn4WO/\r\n2BWg+/UrOnkguTL0/uzt2KBDzZP5Ihig7B6HQ/GsBL0KHZnfeyypDXLOvA9v\r\nwYTsizGo01/jeDFzHYJJ7qbcHHCmvpWEegrXNKypLz3awCSyOpYoHnPsfk1p\r\nlognUeM27kpSmz/myDH8DyvT7DdBMZDVhRCJnlxZ7qk49Ao1i3FzMIZq5xy6\r\ntbEMmXeya2ymWz3IP8CcPSmq7lmPLPvFvvNIoUKJfD58G3fp76uM6IQ86rav\r\ny4+RvUr1nCnlLjc5S9sDQSjY6kQEt7fGyLZqzNH5ETdRGYo664699cF57x1N\r\nci+yspzy3y9On3ZGGdSnO11Aa3E5NT6Geo8=\r\n=sv1j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.41": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1683f282b5627672b52d4acb5354973594bd71d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.41.tgz", "fileCount": 8, "integrity": "sha512-FvlO7zxmgfQ5nodkV6QJtCghI5SxD5jdEAJrUpF7OZlYSQat2n/jLFjHqBAi7+FrLvr1dR3J3tqPTbaejrl+CQ==", "signatures": [{"sig": "MEUCIQDl189JdZeLjcvE/kxJxXdxdQ/MccbPL/uVwL1thiMkxQIgLuLTNgH28pmu9j5r3cXk2J2l2m2fZZiJHabESWpx3f8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZ9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkDBAAgbyvsCXZ64qLLC7ANZnLyZZCBywMfxJXAr6k+1CTjbiO6SKT\r\nI/UW67jZky9Zl7h7ng1dtgtPep6V7NWWeefhlQWtFvA/qPnrJGpxHcvSGmk5\r\nB/MXQNJo91dGoasuoBHQ6kK6vqEyyv7+9ptiZN+NSf/4ZT/L852hNJCwr5dJ\r\nDoWWoIC388ihGG7M3Sa5niopZtku9/Pa3Do+fr6Kp6wvvkwXCj4DWqZconF0\r\nJv7sdfvx0inq4QG06HE/Nb0q2evNzymS5tICRpuU1lpOx7Twm/hpPu0+vi9+\r\nSs9QZCCn7LL/jw1MEddevfJNGK3rxmHWSHDp/1R3y1QC2EVDIbk/0lo0ZqBy\r\nAe61yenuvMxdvbG82vnEW4lEwQdggZsKc8uBlNVgT6/E9wkjCVmhNSbzv+E+\r\n5pWAneWxFTq52Y2+uk9z0gFqCzgz5zaCvlTBc/WqErdPFyKaJvxKLAqySujl\r\n154zG/tQqX442AYVui7w3p/YBT06GR4V/JCoY7+sAFxE5WceOdA8pQdSL8fI\r\nhzdQDjzm65c//yB0UkrXknN1f/40r0X0dByQyefI/2ja4I9LdjOP5ODY8V5Y\r\nV6fFWCy+UpbLLzDqk7a/w0HqcGvHTV+eo/ogMng2lRwLbAKfthgcZ3v6iInb\r\nIF2kO8ok6KU5DYJNpg0+qT5PzB3nH8zCyoQ=\r\n=38GW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.42": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ebdbf17a3213eb63a3410065383e7bab3e213cfa", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.42.tgz", "fileCount": 8, "integrity": "sha512-uF1+NrzTt/72dalSNHNjIgE+zcIPgdxbyI1M2dFI0Wgs+AXQerbI+9LqwtDdxqks4PDaYHB+SGo3j1rH/rTHyg==", "signatures": [{"sig": "MEYCIQCbrtqGJ7koRev6ZyZCCBO5UbDYsqwtdX8Xd5FtbE/fjwIhAKkbL5qTvEUOhE6a+Ctxn2oo5m0xjnlsMEHc1ZT8hg2Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7SRAAnawWq2/tl86t8SH3aocfsBaKifWNnAqNMeHBBiQ+4D9p2H6a\r\n0NJrnXFI/PwRx6/SuH9w2uQ9p/WmyY1+NBIppELoc1zSTiHhRrosEih8/D+s\r\n5RvccUpkr/1uiz6i2QTAzs8rJBuLDR2sRJtkLU/ltHZeTnyy66zWM3QpwCnP\r\nqk0MbXBeh3qVRf9QszjOHrLa0qnQU48etrmsi1+DpBtj+MNJspxJeRnXQ1fx\r\nnfVmlunDa5Np/1V+Z6YHj8oYHTnPNFGt8vOU4a6XmYVKxvgAir32JW4scABB\r\naYLOGhuS/oDHB+O7V9qmZfvxVEuNnk1n9mQsYjIqwj5ubKVPHMF941DyIG4a\r\nlsubGgJfml+9YLSker33v567NTmCo8UMwFDzer5mDjrx2eeu0Oj2NxfyMdyx\r\nZpkh8cCuIrW4IMuUxm4b4SQ8RpgXCtRmCi+QFlmtQT938HqEWLSVlcsGhJO5\r\nxYre/aC/xbhDaFhGC4NDJEeuAwuWz40e7zpdLwcWVs6Ya5X0HH7998gQFeft\r\nlG7exDnapxcYTGo9dtPBR8iHUNLhISvYNbxHTWkNcKdCwfSWFyZggXxUR1mT\r\nvdghIxhqZh9dRDWeXrpxBf1VNVb3rUhgp7r87Cua3pXAMbyQPyl8muoL1Osl\r\nhrPl7I9KbVXfCdYmHSHELGaqZ5AphVC0RNg=\r\n=qEsz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.43": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e43b1cca949744231ecd04e7e2b02ec851a5b561", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.43.tgz", "fileCount": 8, "integrity": "sha512-YCEgfr9AH0Xgllf3xQdKSfOU2L8QqRPpZLqepaL/z1XWdMDrO4WFhDXh3vOzr8BQrXgRL1jWyA96LEZo9+yMcw==", "signatures": [{"sig": "MEYCIQCtLB/Rtt9V0IyVBiaJUNftojF4e9vtqySnZRHtjFaREwIhAILJvlYE7nWLKR1xaOISArHoJRiC+4+KfMtibK4PDR5Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvs/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNphAAnjqGjHmXbTtvfLjocYnUUjIcGP83wnjQhWWR4Sr+NGgQiUM9\r\nCe7l1qTM4jUMtOqY8UBgVLP9MkN7LJuURu9TNUyNBztBXCIx7XoZmJG8zLFZ\r\nXBI9Gse3+HBDHxMTXid6YaDxc9SbchaYOceB5lYpJWpGtaJqzj5KNMCLD5EZ\r\n9Fk7476hI55cqmyZqF2wo6smA+G2oMCqdniT9l3FTmzmyh7/viGOcoZW8f9g\r\niIRcKstj72pGWursWyeAwpY9AY+fPOJV/uSDOsQNEdFWu1v4hqtUFRKw9Epn\r\nKzO3njBe3UXPQwrhb0+ZMDeh5UWZguF50AISx6gEpMXf0okKbR/ErYxcN9gJ\r\nAS/Ak7OfnL/EQPC3mC67HEwFpN0l+9tRc0xGkjLvtVDZaEerX/TJWvuNd0jb\r\np3bksbTj27i0CK/e/1Ju9aTPLgIYWUTV3CkosVepLxiHFfN433UnVEXHZT8Q\r\n08OTx5WYiQcDP/ofWst+qrrT0rXV8x6jlHqGhnfN4G8nGxKxXmcAp86gOUgs\r\nXhSzNm+aSGvMdDvkxJ65pJ8r37itrUMbBc+0/mE2L9UhBq8HaW2LXz9OpEhv\r\n6rYK8nZsaFdetqZv1wK42NVH1rr6EsmRbYZn4hNNvB3hyoSPnzxKBchpwfS+\r\ngIUrmr1OoLyscuEu+9URwRXideG2FUe3UFU=\r\n=t9im\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.44": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cbc8859f73a7028bb9c698e5d092dfa5789a6395", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.44.tgz", "fileCount": 8, "integrity": "sha512-Al3puDBUdTnBjmnfY+K93zXM73lG8CnaHqzEeG2GYgguG1OSe/F3xkg60gIKFI1tuakc3ME4r01Kj2LJzwSqGw==", "signatures": [{"sig": "MEQCIEUCDsiiKwiuGZ2xUsmFpDAMemoF2+afnpK03DQ91mHYAiBD6UiAi+wa5hD9vcZTVPIg+uLMT73W+w7NUc98NaxPPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8JBAAlDeeXKiC3BVZkxy9oxKtITUEXynrRTfZlk2OWLHZvsLyfMgQ\r\nF938qf0Fv5D2PMJ6Pmz1Jn/sEOdCC8L2Oq8qnN8+la8yJ1sAZKbc27/YO0IW\r\n5Ls9tHPW0/LwH1RoxkBr70/nHYuuCMgE9kgRlUjpBp6hq+fHl/7tVqXuMt9r\r\nC/1lGJoaVersdK48T1qD9X32XBZ2Cs977pe5XZyHRiplEBzX+OeK5gDbyARd\r\n1zBhxEbTNJWiCdLwVofs4JUggnxTTEc4nV2Qkz/TtXXmFJhU6TJmje4LyqiM\r\n8MMIpJ8A1YgEQlEmm4UQgLrxheL+a/KGBbqRVuZLYrzetnXNX45SZEjcgEGQ\r\n0ZjMjyuhM23x47nUk1hN2gYiNfehZa9Puz+l/38oQA7LE27j1DAQNtOFqlzq\r\n0rDkA9EWT6VIFZ31005AMqSAU7OVpfCKM1otTeHfzhT+KPT548Drkpyh9MP5\r\nQm1HnPpKM6W5oUpWnHmkhhOiO3slTZl/s7nREpyckj6d6O0OkpCRfqvYYawn\r\n7kndit1niveyEIcIgIv0MeQjkAOTFk9Z9UgGlCNBnttnWjRYGPtAEw4FGttC\r\nObR/P08Svroqo39Qf6QOxWndLITrwiWVTOzaj2Or/SFX/mD2ft3JGgCsRMy+\r\nerC934C3bBuq/1ErLLaRtkzBc1GIcS0DWrk=\r\n=1szf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.45": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ee8cf972c164d8d22f3698573e0b80694d3f2fac", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.45.tgz", "fileCount": 8, "integrity": "sha512-OEKYiIcs5NZAG6cacSrdDxSq40123/H/T1Gf+ijfHxVXtHl7qRlaAnVRdVbiiGu3EXfWD8/HHfbq33RjNB5EtQ==", "signatures": [{"sig": "MEYCIQCV3uQQ+JLwfFuyU9mDJPhvXT7Ibtlb6qQt2WZGkMUd9QIhALIpMEuDPeAWJQKeLRISmqsyggLG8sQpV7qqT9VlMQK5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wW7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+UA/+I85dVUjxw7d85tdz16lwKx7PK3U+RYJiaVCveZwIkzOk8g4F\r\nyooe8ylr/qS74X/qD+rPpqsxiYD6Iwc6bJjF0ksIuaSPR3/nT0hN9Rhzn16A\r\nxC8zSLTv+F2moNwpHcKLLzyY3aRllw1m9ZLSQ4TvOVBL/K34In8P0+1Nz5A6\r\nKJuAek2Q0Nu1QPl5RQud86GjRiGE3E+PT+ZXo77dKRYySsravHmpInl/g4zF\r\nYJirESVpHIZwNt3BZwsPhyRLN0qUQB+26UFma4u81CR3IpwWUt6LmLkP1Gy9\r\nSqWkCzPMeaxuw8doXZZzEWLGopsN0GBWjZ+TyEB/bjxGIHg71zFiElhMesES\r\nqbGKkz/D9QLWXrLKmavGyWxBzf4uzAf55cQl4vUMSHBp+Z9rSkTCVYtBVRag\r\nWFWmSl46R4omLBI+8GsLF1fBFi925nou6YK1HotsTaOfKrCsaQJKyszw+BbU\r\n0CWcEtHlIp0pPT9WRXHNo3rrxt1E79qLzo+Pd35kMZlJ5cxcb1GCUxi1JWpm\r\ndnNXgfpCErBWM2KuAOSTXUpG8vMfncz2MQm0TV9pioIQfyrxuV6ZGZi4oYJp\r\n3Ytw2gN4A+UBTfXVlEwadwBiKqSkHrwnAkWnHHTqp75W4/XqUERvDvJ6gDgK\r\nV2LaTUuadwIpIOrBk6Pon/aJDNMDnC2ZUD4=\r\n=OP2Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.46": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2a13535c042d735a4e033b48219d60f57b676cc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.46.tgz", "fileCount": 8, "integrity": "sha512-pwsx5aLXuNI3ULB5pmghMZeTZAaCrYbthCmEjI6JfuVBEWyajbxurNbTiVwlF7C03xRyNio7UTLoM8UMKZMVRg==", "signatures": [{"sig": "MEQCIHxxrD/u9YsXvSFdxqgoLYXpnylfzjQV+oqDlfP1jrkHAiBAo1Mj3L4Y5kz7Ty68f1xbXCMFqkLw1DIRln4ye85ecA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi9A//V/kS9DjmrFL/yPfXG0sjEdG9vOvPxddVr4UAuDY9s7PjPz/B\r\n4p+ZH6ALFAvqsxJfIs4TI4fxp4x1qc3kxdhVj6mCpJHxp/Ly81QVtoiUp2t4\r\ntKmGuC10gbKx9ygQQRRp/TkJgC43K1RHYEVFxv68epw74KAHOVLFzLLPEtHJ\r\nwBL8sN/mhdMlQGlAr7y5apMiwYs4dy30XCSa/mhMkMmjlRo8XMbNSULVsH5z\r\nYFXi+T78oppY9g3Rn/QpVrFX5Wdz4AGQ/YhL0hFnu9D7s1GNUMN6aCMMiZzZ\r\nmpJ7Nd7lt6D7GKZsEDDlIpvYAevt35YZHTqosXNNEjp0yvQUHXceTbyhet5v\r\nguANBa5yGZazVcs39AJrW/OYer1fnWru2fgPs8Iio9zNnRTZGS2ummQhnqD+\r\nijPs9+ioGaVXOA4s45CMDNcXKRIYWb2d9NbdQO7ZiXm0GvHMFh8U5Dk9StFb\r\ncNyEnFwYDx1OeccyyqBK2hgB3iGE/IxLqIOmpSs/4UI6xNDkw3HjGc3lPQZP\r\nMmXKWyuOnss8TnPMFUUC7+bvF8PXsgM+YcTIoC6xe8UbDv32GChQZRSJfTkP\r\nJkvk/3KfPWqZWGBqHcDoTQ28bohCsc/mULtz31v/zTYLsZjI2IYTzUvbYxnB\r\nXU8V2aRFmlmfuamIfkXkiVUzj59/wJA269E=\r\n=azQu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.47": {"name": "@radix-ui/react-use-rect", "version": "0.1.2-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "0.1.2-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "780fa030bb5c624d549173b98d04fd1301bea9ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.1.2-rc.47.tgz", "fileCount": 8, "integrity": "sha512-u0PRRQQvnl9tld+7qAbWsrfN2rrtjyfXqK5+9BOooMWt9H6+OWDlau20iQzHdswuJfg8zeyO3u00k56r1tVSrw==", "signatures": [{"sig": "MEUCIBQSqjn52cGE3f+AJOFba/MWXIPsHTLnD/EQoPkiFgSKAiEAn0/7yc/16turv7jovMVNCONVa+5rAhnCHkypyqu2X2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CF2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqefhAAkDyQ4DsTQObFJM5f7uUpPt4QPn8uYF9XsWEUnU+VSwOn+L9k\r\nturhWwp5XZqLlxf/Jv1dqW9Z4prPF4vk17UmysP0eqgplTLvA7mnfAoI8CdY\r\ndwSPc3+gY1hWGLavt1L1n9FKijYvOIKA49A2z5h+e7WlYULkPLscvCJ9jav4\r\nblTlpgIZVAblm/MVtZVHKtVUz3j4ZXzFPfDVqSXYs3QlMDMLXRo8kjj2Hhzo\r\nl850KYQ7SaHOMljqbVCqckta1ZXSFj7aphxIctTneP/q7nkrSiwfrI+DmTFv\r\n4PG5KNmSkNGQSfbFoIar2gUaotpglQCNsrI1XmWogYkSiiOEXeIpAHMY7bbO\r\nd7tDh0rVezIKr4zjR6I2G2i+oxOF3EUMQFa8f+DDs0vIw1AbBcZ/mixStJ2G\r\nsa0F6VIWcKerxa8qFxZqluJ5GSS8rDy1KORmVG85avOV2DA/Oc/LRsTBd2MT\r\nDB2Cdd2t5W5RPmJCyUAacOjOCag/BhmpKANKjJ/FZ90D/WQElDL9NwlSLf22\r\nqVf06ueVWRPH1N6IcCHdcF3q9T1yP3oO3nJbXUOCfmcqLgLmz6AHaYCkvoJT\r\nmVP6XtPXMtBCpl/ivX2gm0H8YTvpLFjxLqSJ8lpsrwvYnPLQja/dkpEFXRKM\r\nkHFxAF1SibqimRvxrC7HVfa2MD7ZJtcGlsY=\r\n=Hlkr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-use-rect", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "adbcd4862841dee410b54f8e2fd53a9a2fea89f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-FSAjFt5Edr7PEYOlebOd1hu4u5vE1pmKzgWJ2f+WmGFM9Kq0L3py01doV/its6r7Z7BX1FJe61T4UFNClfFHuA==", "signatures": [{"sig": "MEUCIGXm9h0fl5iWWgxotsYGBwOzMRJG4yE6NYtjLnw0SmAoAiEA5e7028RE5xOgxDYrldqXB7aQdFfn/+2tD4tYSMJLAUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EwHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmquMA//Vnzj1v/VwbhneJksRs5/6wUm91r4eUlmFPyAToM2q97MhIWD\r\ntbI+UYej3aqfom1jUfPdnmlX0AwcbvAKLnCIO+TTzyTDfW1qBPfA5Wv1L5VS\r\n163s1HjI8ahFa04hVUsvj3N54P0ICqvpUiLtFMmymDxttTatEDgly13IcsKt\r\novoAyXForRYiTb/MhozteQskQkyXFxlJP8JYPz5LSIAE69bNR/PtcLD7QDMT\r\nwGlQfHZg46CAMq4TttPid3dFRfhrLnLVphwP0G+eSNp0yTKMLl7dSJ2gLqJJ\r\nQKWe7+m6y0wuMPMtN6gu3t3On6XtqqJiC9D5SnFitWCyf98xV3EkN9L7Hjt2\r\n3x2I0STjPZscUccyO7xfr6l1S6XIZZVidqmdiGT2z3UH053Gqrq0b0aIRaKo\r\nMWbMcdkiGE9W3d0+QAJ1y346KNfl+TWynl8SqcrtrU7IzgsZlI9sLsWpjR7y\r\nqWZhdi+qxMY7mtq3Bvg6HXPJaQK3gEatMeR1IRTZPlFVtHtvC+x3IyLhLQqu\r\ntUJKDHNYZLWC8utPu6N3gzUliodrzUqUXPwZlHgPhazvue3OWbDfSxjApM7k\r\n4Ceug/yRf2eZR31L6aQ09w9PT+3/53Ty8ULJ2iA6Weep9M4oooUPLpxF4Fjc\r\npjZ9MDFJSe/jZl/rMHPN5Wzi4W8DJzpIBpI=\r\n=SFZu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-use-rect", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b040cc88a4906b78696cd3a32b075ed5b1423b3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-TB7pID8NRMEHxb/qQJpvSt3hQU4sqNPM1VCTjTRjEOa7cEop/QMuq8S6fb/5Tsz64kqSvB9WnwsDHtjnrM9qew==", "signatures": [{"sig": "MEYCIQCREv7aNhIpHb+Nhs97L1ara7AMyrPoKhxb4DmwcfWmBQIhALO9YgBulm6Q4wSLOcxLrZuTi4F4XBmt0LAw2f9CS7I2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6112, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZGg//bAcs7jvooWw4YlOgCzQyqsPFmBU3KFBseKJHKwHuWx4sD00D\r\nKuzi4zQpx+P3Oq0vtS7umQbJeKe+0IIuxYeduTyfrcJ38k/meUKWRI2UpmKu\r\nljTiZ/OPRHJ8KdeEXKGinCYYZwbMKgEgTbjN4/86q8qPgMN2ekIii4Ce9BtL\r\nNieAtGw7sIQsrV7t3cAFL5/LBN97l1SWWUcXapdC3OS5zBKeO5L7Xy7GUzTq\r\ny0tOsZVi7dQbHCPq2PDFtusMbG+Yr91zEMZBnfOs04Ss0vfMsnh0chdZD3Ea\r\nyIUmBeJlpnDsnTPuQXiMx1uc4QYGui7iS91dlgv8P9VoK5Ov+R/u77guNvif\r\nDXxJt3PMDr3wyVQRzouPXewWmcivCWhQjS4kCmFqka5hVdsNKp0TGsYKuce2\r\n/vjA1lTooHGsJIfUJIRIpOfn6uC1HA9QPCQbL/RLTCwgKpVsKlwcAxsJk8Ao\r\nqUYRO7oR9PVCotKyDtRvxwNTDlWQ/6nuWJFrjSSC6dueMHYk2/8UT7cz4aBJ\r\nu5wSlJ1Nx5jwAbJlG0CVvlbY8yhStpStdLrJriE06VtT52B5Yv71xUp/V3lb\r\n2hAUj62rpClKxI2++YMHbNtXT53ONO1Ut+s/qAijzPrvxn9kUt7c/LFTP1MY\r\nKj2Qt0lMG7HdtCsicq91nOyY5dnpKV8zjVM=\r\n=Qh7/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a2ba29225aeb08dfc239973e82609d83ccf300c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-A9lmeSKOzlsnSXXKk9JqzfXluxfxbmtwg97c/VGUfxZUlNKjxhpclqf3wBwPUbYuLn8VD6A6pJ6cj0J0K69kuQ==", "signatures": [{"sig": "MEUCIQDvaE6t33fqNuiIPpEs19LfXwRCWfWufT5YEmJN+VSDIgIgc7xl2YZG2rf0mJyzYgn//7QG2+NiJuZJJMGURlwR0Gk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6666}}, "1.0.1-rc.2": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2c2ee38ff9480032d9e2193e13074ed9fa32890b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-vRtOsDvhuPz/pdCS7GPn09SAPr8V/+HhFjfyd6V/fA58ptfuuRMOT3BY/hNPctr7/QIokieILtIySu3ChyLKZw==", "signatures": [{"sig": "MEUCIQDOJKJH/ZNXIyJUET6zBHfgOyvcQu4qG0LaBhfZsFMk7wIgcICxFSpnc/p5FBMKqImxkoMud6kPKzjKMFUM7guEshU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6666}}, "1.0.1-rc.3": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "ed26676ae50c4faa3fbeb36bb3de29b8c2b31bd8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-agUlByYWid6yvLFv0INMF+K/+8FDio3vL4t2CtpnZSrFdGOXu+c0wzbW0FvSEBk4tcJTIf+PjUbt1WHLwbhKxA==", "signatures": [{"sig": "MEUCIC3dAWeFupxSWxoggc0w9wlcVuk62Br9Yl5tsjI2cUGvAiEApdB91r0VAmbLryoy0oQW696Vn7c9MJ4c7zFeRGeIMBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6775}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "de70f4093aa7d763bf7fd7c5524c4e9c63595f18", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-Z/4/udR6AflLtnKpyjtPcAZnCKqqwj7YNvr0DnLQNCpif4LVp9/KvvjQ/W8TmDHJoaNa3/GXGQp7bLeQDBP1tA==", "signatures": [{"sig": "MEQCIC1Lyyd/61MejKo81Sj8YneRINJBb2yfwT0qkFj46zHHAiByYhEEwxfHZ1+xkxzRqL6clOC3x/ym5lbLvz8/gZTPzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6775}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "e23925a1d6e296aa43ee19c310a1b38104a41441", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-bOq3kip7Jb0SaEvSPPt2i1cTFh8SdzoE5AuKCrCMIyqjOUsppd3VZEm0mJroo9CtOU8QuLz2/i1oY3iYGayrow==", "signatures": [{"sig": "MEUCIAX4KbzhHQ/8HW3yeTtEMHLNM9cWvnOCMksoGDpdDQheAiEA5561hBcn8+dL/0lNeqiY37xJvVo6cBEeYb86GKvALqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6775}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-use-rect", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "d6a4dd4d7914e4cc0175d2f5679058db246cda0f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-ZnRwWX/raSL/xANphgElijPcLTgy+b5Xljzi29kd7cF7yxc4Dh8M25e4fvitQrz7PI6/RRDImtYG4I9kNa5MDA==", "signatures": [{"sig": "MEYCIQDaaoHAnQZSUOT36tcb2uTTc1h2JVL7PEMzNx/RircEEAIhAM4nTX8T7loCp5fkq5233oR8Dy8lda+AthmyawLRNf/2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6775}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-use-rect", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/rect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "fde50b3bb9fd08f4a1cd204572e5943c244fcec2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-Cq5DLuSiuYVKNU8orzJMbl15TXilTnJKUCltMVQg53BQOF1/C5toAaGrowkgksdBQ9H+SRL23g0HDmg9tvmxXw==", "signatures": [{"sig": "MEYCIQDUXc6WylJCmCHjv4q3Z4C3IQToScBkwHF8N0TzbCV0+AIhAO/o1d5MnDTrwryaJfxtEEtVRgX2CD+enhFba5wNuoBU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6737}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/rect": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "197c13981ac7d0910134cc307c69f27c9972ab35", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KNfW30qKJ6EROTVu6v2EKopTVfslsgN1NSkIcWRuO98oVemA5ix715j0Cyly5rxTTubcCliUJCVEl70PpwN95w==", "signatures": [{"sig": "MEUCIQCLWQvOgw1L6PL6yp8irlcCCkbXCB51t0J6rhQKBhk+fgIgHOAmCnupvEQsZ7vxh6cvUJNFwNu+AnLniczTAhfjlGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6607}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/rect": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "a790ff5e5c2d5d5b026e7c4ff2dd33d681ff5354", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-DBeImk1Brd7wFokaNayi/bXlWUCHnDpSx5BJqOxG+DYoC5+V3xgZEYq5dPdi+7E+kNHSSQQY1Ij0CdJXqXryDQ==", "signatures": [{"sig": "MEUCIBCiS7JZFoFZ2PkHCE0EJKON0iGWmtvJKMW7viokAF4DAiEA/c0zpu8iqbKTF3NcVhbQ+emLcjVunLuZzrzsRy9Eslg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6607}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/rect": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "44b764591b22bc79ddd8505b637963593b388360", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Maxt4WjxMXHsulTqTpjUeQrzt1ucwxeDzGtSOXXK0o9KVKSomr9gbVPe6ke24dVhRsEZNrOt1aOGMZPapDlVhQ==", "signatures": [{"sig": "MEQCIQCS6QVqpAvKKRgMensKKlnsbFh+CDdmKfd+zyDTEnHXwwIfT69Hpj6m9ZPie0if0NZ6mTLZkIF/hF9RLsAyLxqS4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6594}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/rect": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "6d3bf95bd636227961abbe248f40d69c62bb606c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-PktayEpw6/OFZ8vr9pLxgRsSx4LkAKBs2XU+VRVftneP5Z7by2M6fMjRNmHBv9EBpL47rTQoCkvFNMQvLcFJsg==", "signatures": [{"sig": "MEQCIAaH8n38c8hJIwTw06RwXtdrK/NhDylW/7W9WdlKhqV0AiBrV/fp8zQuLbcjVF93RbfOkz8mW1slvJ9wbuKDKZshqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6603}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/rect": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "63a09baea3b6a9c47166247f9d6e2546e0c6c1ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-fpyWy/Q0DZNgMFiXbI81vbygTepnmuaEcAyYRqVZGDydV4CfkNpNCEpQISRV3VW7CWZA8fhfnUw0PJsqVR3jGQ==", "signatures": [{"sig": "MEUCIQCvi/UI7SlKYrj9YQFFm3etdGQNURYB0boWNUY6BMFS+wIgNnVMqolNer+SGFcXajggP2zPQrHGou+tUt3fnR8VSdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6603}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/rect": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "b0e6bd5f2e2814f5a60e4e7f76cf3c717420fc07", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-R2sMYVr/WixoSrJgwrLgJEh9AFAVtOUXSkiFTjo3ojWkf5ceIXhrS/tWv0uOl0W/WWx/na8ZaNMlJ0sOzkHaLw==", "signatures": [{"sig": "MEYCIQCW5G2vk9kuPqUfEu6KzUvn2k2UcsZAAd1lEP/xUTcBUAIhAPN/RBYYrvQjI6OQfY4eiXj1+sbVtvdtPsdnAT7OxIIv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6603}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-use-rect", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/rect": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "47bf89c1c28e6cb8f12c364f0609ce1a781df84b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-YnJrAODaYNvnpeCh/dTFRrO9FTY/eM4McmL4UekpsYjL0CtuwpJf1Js0lIN3ayIxSDS5U63SKqyu4xeNa1EjLw==", "signatures": [{"sig": "MEUCIGgOgxexuuPLyej9/hTisyjjZTfy38ndJzbmA35qCQOJAiEA42+ajseRYAvBDKmC+TIjuQ3/cheezcn3VDSd34azSFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6617}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-use-rect", "version": "1.1.0", "dependencies": {"@radix-ui/rect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "13b25b913bd3e3987cc9b073a1a164bb1cf47b88", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-0Fm<PERSON>bhr6PiseyZlYAOtLS+nb7jLmpqTrJyv61Pe68MKYW6OWdRE2kI70TaYY27u7H0lajqM3hSMMLFq18Z7nQ==", "signatures": [{"sig": "MEUCIQDiqzJYIr+yJqgQX5OIZsJfOf6PO97Jyjgcub/e7uzEOAIgPTWq9+A4VGqv6irb44U83UOu2JzmA9zqdsDFer7NrKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6579}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-use-rect", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/rect": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "717cfc773973b1cf0ba8cdb062d6e714824c571e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-aahqOsgwiL2+1W/HJ6poYGsEaYG+dNLuHs7ImEA8t7UlSpCJrH0fMveMzj2vxq6Zec3N5Mw0xQmRQh3XDVgVKw==", "signatures": [{"sig": "MEYCIQDrchTvk3X/pASM/MKCgW3SbYokosykprzDDNXDbfJhMwIhAPUImxS2KHUl8sXe321E1ZvGISEBYGwJ6XeZAi2bSB6p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6558}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/rect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e997ac953dfb9ad0a754be689bbf3c9966c9c4b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yshqWRS8egpjuOKLg+CCzxd7/ken4JWmYQ/wR+dm4ExzQDYmjskvgpBV0xtXJUlSaPM32FaqW7/9BdgKoA3y3w==", "signatures": [{"sig": "MEQCIGNBzTgQS+acL+hs/yDolyG8Y3kTIdNsr3OJEj0qMF5fAiBxQcPUf/fWaMt/CGJ1VH/ZGVk8U1Q9zTEQDjK9uCYXbg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/rect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "7aba60606f2ca3969d5e44b3349628b6559fefab", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-I1ZdlO5UnYkGmhGbGylbHlLRSIcnc3bb+C9RWwpApWJVJ45rHXLDYGniA8gQhPvmETMSQNN6pPxVh+PI0TrLkQ==", "signatures": [{"sig": "MEQCIFR04vZ4gcoO1oVXiRxnA/KZxXeqaXqsHdRwoQf0oqX2AiBgzFMFSkpjxuFVc5KDxLj7RAe4pIa4lsqk4+e6kgKg0w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/rect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "a2c279430e3dfb278d6d48724c2b81a1f3a6ebbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-XS3jJj5fQB8M1HcUkRnLuX6Gk3SLDw92+MLJHq9gu/qOG2KJ+qz7fY80p0pCJMu/0zgXu2CKkx1mGHOSzy/czA==", "signatures": [{"sig": "MEQCIEWSyeYeSuSXMlN4+Tp8LSfTeEDuyGkjRh38nrFaTGFMAiByY0PQwQaPk2I7jiU3XIjqVAOR1s27weBjGNbbKSdaGA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/rect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "a9b122a780c18262bb8dfaff0f7d63a1f7376ce6", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-U7Wa/1OcoY+0hV4i/rWQTZZxzOnVu9eGCXxtOAh/eRptPUaF/s4lspw0LnNEquMZ/4h2d0pOmqtgUVb58+fMfg==", "signatures": [{"sig": "MEQCIGhWmYAnI2DZt+zminjzxyVYEkL+NbW+mo6//yDGA0AVAiBslOG5YqTQXzWNU/QtgjlYMALrUCxh5A2iL8kPJKcG0w==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/rect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "4cc10153fefd662047949364f2f9ab8b569274ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-xzFLQ1MrBvGqlb0KC5ja9EAjT4Gu8Zj1TUmQUY0VQCOaPle7K26KuTB/v3teJzTI35zcHOswTT7imQ2n06L5ew==", "signatures": [{"sig": "MEUCIG6kyagqcXQliOVkiQKik5GsD045CcnmuxlTxY6ohadAAiEA4vAsSG+8Vyzo7Zm4csW5qrdxDmgtMXPWdJEv+KJa07E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/rect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "14bddcaa96ce8ff8164d22558854c3c207169ea9", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-rByow4u/7e4o2teo5KvMBQPLfBsnu2Srqii5QudBfryU1DNcAbi3SAuZ+QuqSrTnqqFtUTzAvUoQl1H0QXJZtw==", "signatures": [{"sig": "MEUCIGe0ofdB1drmNjI6W2YCTrlaKxb25vzXnI8xyZJtbU0EAiEA3kInnWnm29gsCM/MoyV3m1yCHS42d/dNjp4Lf0h+kWk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/rect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "c21784d68e388dfe0028d965dff2390c74a2465b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-TE7Oo53OnaFF9oqdqkxv1VOtz+4VhefIpRDssxTSrjLoOcSDtK15seONZP82B5y1gfLn0A3eNJJ+gllOKua8Lg==", "signatures": [{"sig": "MEUCIQCPS5bOknWE+0dAsCdpSjw+//hLby4L4O7HRtws1HrKqwIgYnK7dE8AqJnoliEOjeySi5z9SBdRM0zld1b758bZqj8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6933}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/rect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "618a8afcfb27d59f55d55e6e052c2b73b14f07c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-lj4iLfJhvyONZPfGC19yw8hUjTv+MPh7QcEuDnKYZh2HR8fqZmvp0/ICx9fMG4Fh4zPQhPNI8B742z1b4jy/OA==", "signatures": [{"sig": "MEYCIQCqo0i1h2PMnJxy/MNOBtn3ZVUnsNCz7OQ4CQB8JO0aGAIhAKyhpxCezxFm8/wfJgrJ3NzYeaN26hPRWLNZes4ma8dU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7324}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-use-rect", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/rect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "48bd8ddbc39303c91135d86f57e72f24b99e3dea", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-/RPNDFx8M5v9v4zeTgeJgVpUn+Bu9DrDRunvAW2thqgCN7HgjMy1Rz6BWqE0G0pBVCsS5NExdQDWuQ6cY358XA==", "signatures": [{"sig": "MEUCIHd3zirNihjL6ivwMvQqFvJxKxr8HVvW1N57JUzDQ8e7AiEAjRVhSMfoa+HqKao2EcObkqYILfOkTnOvqZSm9+7/M4A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 7324}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-use-rect", "version": "1.1.1", "dependencies": {"@radix-ui/rect": "1.1.1"}, "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "01443ca8ed071d33023c1113e5173b5ed8769152", "integrity": "sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w==", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz", "fileCount": 8, "unpackedSize": 7286, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDoGLjVasNjgaadaifaNcgs1ckO5mIerMPUh6JysVX56wIhAIuNNg3c89nz6SqFJR+e2P5iD4FNBok4mPnitKpkcNWC"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:20.402Z", "cachedAt": 1747660590801}