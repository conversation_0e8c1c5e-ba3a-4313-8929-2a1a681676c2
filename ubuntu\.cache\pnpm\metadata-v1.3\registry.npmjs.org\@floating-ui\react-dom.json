{"name": "@floating-ui/react-dom", "dist-tags": {"latest": "2.1.2"}, "versions": {"0.1.0": {"name": "@floating-ui/react-dom", "version": "0.1.0", "dependencies": {"@floating-ui/dom": "^0.1.0"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "55cfb0549ba379dbdbfe8a1369c8942f920ae353", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.1.0.tgz", "fileCount": 9, "integrity": "sha512-MhPSixcbVsjfRICtqKUVUiu64qNpMtTEyrwSpcBOu/eNFqb+ZX2kdm5pXTBHk5hbvWw87PkPNl6JeMp+C22W5Q==", "signatures": [{"sig": "MEUCIQDxJN+RiMvL/aGHhS5ssv3tWr4I5g8IcmPsJfwwksiZMwIgGp8pxwe+uBzG/COskoNDrsSlbEipZahyOxpq1T+/lds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp5LLCRA9TVsSAnZWagAARYMP/3Xug5WljwX+ZU0qEzDq\nc62Mztrw2gXPcx/QuTFLYquA3psqPknyEmvnWpriNeXoZ/MibhcWB2pUiOfY\nH2tf1mc/XI3yoRnZ0nRSBze3eUZnMyZ3htC87cXUipmDwdGG7DfqgshNqkxL\nz5SC9U3aM6DXQJYO6YPNYXZ6B0ABuvX4xl38K3qMfkV8lWE8/wufwi+NYLec\nBfwif2UIVZF6Gf4fmbQfKVmQkud8sdCBwRvovteWxc8mwAx7DjSI9SRi0Owj\n9ezp0fhq5Bf5KhiawS5KfcZeIQkJLbsVHIohp3mv8Vi+sp28Z5Mo/IPK0PB3\nybYoaZfMd7X1kkSaxIWMfXrtWOEIBq0ASbMJmCMvkyL7mf8PQIiF8tSF5H0e\nJXIknyWZNffM/8P3p4SpBZWTIJ7UmUcqdiRuW96Otaz1DGnjuYkMicJmifCC\nZQQqfI7FTtB8osWd67nowI42HW/xetX1h9cHnSXn0OCjCRf4/A4tkBuwstLS\nOyCsMrVrLjmEFMVN8YM3LOP6aRTsNm6FE4Tq9gki9+OWDwbMPxIqFRXhl26w\npEj4wg0p+IbNRy77Y2r1pfKHI1DENUvZ46NfXsb8R/YF2teGoK4PmlKTQjyA\n07vwOxarqOIcGgz5pp14ZLLiUZOa7kdG/a1ftAxUk9MHBUoAP5ma/elW0z8N\nUKf9\r\n=UqaY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "@floating-ui/react-dom", "version": "0.1.1", "dependencies": {"@floating-ui/dom": "^0.1.1", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e33721d3837ee1007640d9b1d02286364452a854", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.1.1.tgz", "fileCount": 9, "integrity": "sha512-/B7pvMk1xqECuyks38TOkEtigcVg4ISc2HR6srwZfXB3aIjqdS1jmHt6TtFl7eaqTbOmlb7Qam3o/88abVFu7Q==", "signatures": [{"sig": "MEQCIB0ojLkQO5b++5STxS1bA/sf6BZqID5eA9JUcEJ6sjbtAiB2TWqE4GSWa1uyj25qBkfxDZCBNXrMMX0JzEQCWv5i1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp6ztCRA9TVsSAnZWagAAyywP/iX7FGQ1rCmhmzCkgzHu\n+5BxhtFMOD4fMHp1v86RpI/zPhUPw9+0gi3tDPIu2+5qp7ff4KrBIUxea3jR\nwSLrT5ppxciOBmmBtda4IbI2MDKQN+08f86D2CIFoUPYolA6wkF3r6VgEquw\nl0XZ2o5YJrqd7iRdN9zcOwOC8p/ifR2ZLcmYjbk2FMk4XicQ6hZYt5Rz4a7N\nfOZddxGdGJnipGFYK+UOfoOcFpUGxyf0XoQqmuxVRWWRvdWJld3+5SqR/mAP\ngoR/zyo+7LcLIW9EkGldz+ZAXGym7mUn2hCzZlQNggo4XApZhxxO9vMY1Enu\n0wpLUQ+cegq2JgsFqhBuT72gnH8MjEcX/oo+c4ovDjsz0MDE/EBb5Ee3V+l8\nI6AI+uqD9m87nImX4jYdKeBm0d0akESxL2yWjJKcED24KKmmM1W8XZPklAtI\n33WDhr/Qg51CebMd0D+pUj9SC02dbTP38CwIiddKyE33ttiUAmoMhJXQTM4q\nij43yNYSZRhCJ1VtiMftgJ+IEVmCL6EFbis+6kp2ckGSgylK2Wu1cv1VIs11\n6sKg40e6QiwsH8SDJoIpWQKFpIjBYanzOglNNu/x8VuQBoxahGaoYWA9eecJ\na6Tog7vpwC04ezIqQ7b0Zm+/0Xliuc0haIZMZjcAq7H2pNkgTBEtHOIJ41Cc\n7VRH\r\n=cSvo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@floating-ui/react-dom", "version": "0.1.2", "dependencies": {"@floating-ui/dom": "^0.1.1", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "019af752a7fa15f27e33626eba70f241be50e312", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.1.2.tgz", "fileCount": 9, "integrity": "sha512-PdjLpesBaPoifdIsRkQCJ7YzGdm6KiR96sZKfvqO7BCop1LG52U4sx8v+5wD8xmZRl3G3H2q8nF8hUoPuX5L+w==", "signatures": [{"sig": "MEYCIQDZFa8PpvkH+6BbQfrEFAAohuoHskC/yPidxIIydEGQ5AIhAOYbwCr5mWfz1JRdxz8ATDbi10diMPo1nLi0531vh9Vk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhp7UvCRA9TVsSAnZWagAAH6AP/jgU3td5X62xATD5oFn2\nQBn+HB9fNPRdpZ1aMpUq7qbiQgHklkrTN5lVvmMzDHGr3uaUYSKvMOM1Ftee\nUyJaV/UPXSB3YeDxkhQogWd/HNvPiZ1NCXeDrLCgWB4Z5VfwXaB7i3AQjS1T\nWqOXVouqY/Oj/bs1lTn8sJs9VGdPAmZIOSs8SOp02UOgvehmVPNRMQh09Qg5\ntjgAMVo/ylfOw/2+Hs9/WBUvuqK86mldGpHdIgiQk30K6WCw0apt6D3j/EMo\nlI4IkaKWm/j69eg0Xs3vhl9UOZh09RWyj38xA3mn+162/NwdK+U8y8ZwJwa3\n2YY4ydqDOsVsqGhrAAZFFsKsivvBiReWiPxVKYShXPL709IYjej6Xk4RFjPP\nZQVKwoUi3+6EZQkGq34eLb7PZrhxjB+MOHgxQhiA0iiqucz0uq1XNXBQ9SlE\n5eWApdsXUIsBKh6UM58JZ+xejnUPXs3veqQ2FGn07OxaA5i3wALjpL5KHHSe\nmpT6qD/2gkRVPEpB3UTArxggc+cSDpSONnzXtZ7cmE469xBkNVXEbe07xJZY\nxTuwBnPbclF/l1eBSpRSuRJoUDf07NV0mC7PYrIxBGxR+X0zaStzmp1jVN27\n/JDtJq1hMhIVL087RObsyjdOR5yApyexH4ONAlGbd/HWkzMWGM18pozZM6Be\nfI2T\r\n=OC/s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@floating-ui/react-dom", "version": "0.1.3", "dependencies": {"@floating-ui/dom": "^0.1.2", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "bf51fa3558edf3cf84a66942217b035669ad9b0f", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.1.3.tgz", "fileCount": 9, "integrity": "sha512-+jhV8l9EPjaKcQuDwbqqF+fKaXyHVmdhwax8V6FeJoBm8TXBjF4JyVgyfQf2jI63j+Yh2jZDtldzhwEtkd2xGQ==", "signatures": [{"sig": "MEQCIH8r2WJj0SWQBY78e7ucWwM2t05l4+HlZ2ish4fJPwiQAiBl/ud8/ev5D3yXWLBP3rCYQysuAsVN56Kx7ohwx+tHFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqEJNCRA9TVsSAnZWagAA07sP/1Zp3fdBhH+5UKcdAfBN\nbngv0tjPeARPu07P0o6bcXO6EBsSc7oQZmMPt+C0Ksi2J+geBtvETq61Fy3C\nsRyvf4eMajlMqBvLGGuz++XUo153GSSOA4IEICU4nimy9Jt00xLSTwjcDrnS\nLTaWcndKn7XfkpspGAFXC4X5E2GY0fhKDE8jl7UIRosOQc8P6n9bSPKuy8mp\ntcFUBsn0d9VJWV1yP15Tlc9Uuqi61NWnwRCsZe2qEHKrkEeGBgJjfxYe9pO/\nQmOklzB2yulgxXXApygRXJyE+9rPNoc655HVztQL+F4/SrqmqZ/N/5BdAlPp\nlb9v5EXp+RzpanJ6kEos+ueuzY+Tm9kIIwuum2wh5uyknIEPgbVYt2i41SgC\nCbPOtpI7KGRkJoY6HiiJcFrddiC4jH2hXY/EN282Dao1ZWbRDr+rJCm17e2S\ncpDstt/vtoRg0MOnpoqlLsaWQtW9+019fmSyhUZDtUwJ8T6zsj781w4/uYVZ\nfYGsFCIkIYuNjW1/94yXCwxuV7BhXbjGWPRq6DhfnSXyZLTBq4ujP5KmpsDg\nIUlTlTXVs88si2FFU69FXm1z62WZHJYerGaQmzy3orCJPaxGI8jsixweSz8q\njz0Y+btdd9eCyTAXumXYj9oaYEdfHaMOzFJvsHUtjE7+zkAkc0JivQaJqCnn\ndGA1\r\n=COwz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "@floating-ui/react-dom", "version": "0.2.0", "dependencies": {"@floating-ui/dom": "^0.1.3", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "43d053d5a92d787a1c5c1bb1d235dc55baf3f3c6", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-nv8xzKZkVJG55oMWM7vvhN8czzfOr+Zr61bRQAIfMUwo4z3rKd1gsRNzxuoQvqBRiDMQBD0O9hE4s6TXK6jfIw==", "signatures": [{"sig": "MEUCIQDPJFXJsFJXF0LGU0ruIfLjBYy1o7PQZfYx11+PzldonQIgD2kfkbwx73z0b65e1cM4mTMdU8fmP0fz+Aoyev9MVlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqqP6CRA9TVsSAnZWagAA7cMP/ik8lKgsiiE/k+i4JnaW\nUwi4Yt+ngui7k4znPn7wcRlPtToYhUIifWImIrFE/mIJrLBtUi/zr6NJKKeX\npxdO0c54ZJhS95Joh6SL5DdzMDFFOr0wJrB3aSya/pUPKWH2SPp8XIq0lX7G\nA3s77bmHgw7pkbSW/Melynscz2BFPQdiF0sX0+OlOieAJK02K1nxnx3j7F3C\n/DRJSFg5Vpy3ErlwgdJpuCbAYTMiKFJ1RUneS3IA/7eWHzj25G6gOwmb0ryn\nkc5QBzQ4ieS1OROaHWbApVVZlGQMWbYj8Ol5GUnrye7aYeAohVHphAtJcP2r\nRQxMdhH7XDXjLYdWYoqwli/l3MRDcmFi8n/6bMPyhbN+0u9iZjPkmkmMbZX1\nqqJKSG0cOQM53YNDlV4e5AaBF8rBQG48DMml+mXl5zLRTfQAp5YpFPKnSc1I\nYdYAJCNltsTwbu86i1ReQSYUu1WT1PlFrv1QTN31UNCDCAPXDFv4wydU4wKN\nfBw6EyiPUdZl7ojmCU6QPtyx2cfTezOthSr7RfWyQoNhZrka29X/JwA8LHNh\n9bUxDdpyf1wwMnC68JdbZRYsyVg6IO5MvTKxOcgzKz2VwAlte4/6ugL0V72y\naDBpjNN8rZu0KKspaih37Za3eg7Zwh8c8gCSo9WsIhyE9nRXoN8gtWJebvBc\nszjN\r\n=/ogd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.1": {"name": "@floating-ui/react-dom", "version": "0.2.1", "dependencies": {"@floating-ui/dom": "^0.1.4", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "acafe29a27c92cc59729f87f307ed0cbe82b26a2", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.2.1.tgz", "fileCount": 10, "integrity": "sha512-gtaIvCsefrhw5fbL4DXQfQt8VyeKsh1Avh4XCOqVm2pW9BkfR/+qddniBXOIgRC0zk4nWkZCm74hSObi8K9evw==", "signatures": [{"sig": "MEQCIBAhKmYgXMHhq++M5O/cA8yI1ypSo2HjM9Euj2KRJyaHAiA+8+M+w/VrkHpCu3oHGwWoh05W83PMuzTOeCNYVLLIpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqsPVCRA9TVsSAnZWagAAH0QQAJAzUGGQIMvhjmLfcyad\nPESjPWT4oPg8bPLOuN9+X1oePyf9s4f5v4/hFQ/UNZxlFNrWW8jfc881Tw3i\nA11is3tAGmqjdF3ob38LxgGozCDBFChDZs72NAf+5jxY68sYaMAWRGRuKV8Z\nkU2HviF/wP1TeAF7PH68dDytbP0alBaNL1O0Mpn3Wet8ieCZGE5Iq1Oill7y\nfC5wmj+JRiSU2IlaJg4N2VzIZT1nP9gVXfDedc76oLsYgqEbUFUjo/cqt3Gu\nM+hqEgM37D4te0m2PboOVWiovClmHWtOlm1EMbwUlnuubOmaPhcsx0S/jvqq\n72vI05Punmf9iW7Z6qdZQr3wjs/jQE0dnjP6ftuOTvQboTPQBaprsni3/58X\nFQ7F1nudWdbm8b0TAr20TMhN74uErzMjkOpsX7yXbAjSjXUKdM0wZFmcdAgH\ny+UUz4C5he2fvYU560jw82nFq5r9b/8b3nMY4EUDOvpAytfqKadw7QkoB/tB\nX4X1bssnYbM4Lv5P1Iov6mE/KYd3mBHvOipPhWx4Q8cpX1Bo7jDXBKoH1FUv\nwdm7Ec08wZdUJvNn09uBR43KWWs+8gjLq+zf+ff7QJeS0DI5+G8BKyPx3kpc\nPgQNU6g1AwE+Vu9V4E4AWSHSLtv4BS2GiWhK/v5cso0A5Z4mUwCG4Uax3S9e\n6G5U\r\n=0+DA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "@floating-ui/react-dom", "version": "0.3.0", "dependencies": {"@floating-ui/dom": "^0.1.5", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "a8829853fbd8fb44bb790ba8dca22efb9e43304c", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.3.0.tgz", "fileCount": 10, "integrity": "sha512-/OhZoyy8Hub8gtcIp7HTkTJs35dvMo+T2zYCfisqnxWC6qnba704dQL/3Cxf59SdtVjf4j56hf/B02TdohAxWQ==", "signatures": [{"sig": "MEYCIQDoRsYWW1yMNHCeiFCTKRLB5HNvpGviLxCBDNQ3S+g2cgIhAP7nuYzhjmQ8efdFEN2q/X2gXSI0z5aJi1rj2U25E0L8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrLBbCRA9TVsSAnZWagAAvI4P/jcYzoYR4kQa2QkngYXl\nsxXkyVAJ7J57gwk7VAjs4vuFjYZ6nGXikQwuV+bKbMEgm9sKryApP0eUxns4\n7HJI6xN5VcO/o2Uswb7trtrxtKoBIePxmjebISFZEQoudDTm5i3jyUFvZo0w\nprSR27/+5mwgkTmG3o/53IAscuzmTs30oY1EGPsuU4E9nZxwZvZ0D4EXBmUR\nk/AaJEMcB0VcE+Hh6UxAhRRSuUUKMDpyZfmMisbizBB0VGRX+q42vwpqEhrG\n9/SN49N0GKc6/z9XHfOJGrlrURzmcN1bzJjNGsa3dDW7X8Eor4NUs/DKIEyT\n7VsOENOa0pel88aqH9Dm/yorhvF8sBMbylHBISSoRbrzCfLMo0sQIvvKbzCP\n3lmjuK6T9KFgoFX7XKpH44HPtzgjOGwazsbyKOKyt52Fi87oqjdhkYcmVnPK\n2skVYLiTdlF+ESEEF0gAQUECKuewFjdUOF+fvwv8+XgbRcFmS084vyHcE/pP\n7v3F0Mh6r0xnX49rNXf7ClSHveDCwx2Z5AL8UVFomBAZ9jwwdAlRvJEN22t8\nYX66EKW9T9DPgtM2G/ynkacdGamykaaqsOIrEivRarJ7esPnVdS8DhcUmfhp\ntEx6buZfBYyqya4xff4OB/L9nsxQUAjItZp9lWPraawZEa0mSmcCAJFK6Ceu\nfVn9\r\n=oHNs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "@floating-ui/react-dom", "version": "0.3.1", "dependencies": {"@floating-ui/dom": "^0.1.6", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "f5e8fec7d5e77713fd7dadd4486962c8b6f3c15e", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-zqHOZ9x1MapJitI7tDRY7Gt9pdGx4GQja0jHTqT49JsdxXyCUifFb3P/AxrPFEeOFmkPJsFcNdmN0JSkNtmX4Q==", "signatures": [{"sig": "MEUCIE1J+Rw9sSXKnEJxlm8a85pdjy5143qszo3maI68s1BTAiEA3UwPYsvv8BxEClfpuNtLXgWOD4Ey08G37CgmL+CIjcU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsAF7CRA9TVsSAnZWagAA9jwP/jwAq2cp6nfSWbmUr0Zs\nrXWXEmgeGfM9ZW11jBMgUgYp14ipGHvFRrK6eh/5I2E7oqvHw1uIVymY7XWS\nG/emSil+Dm4PMkN+guQ4K/FVk80KSbZipBPxEpmLddfzT+0k+Jleu4jlXtMQ\n1CeW82PixMvSsmeR0RE9xtXXK1CWNf9A724c+DvVoHSHteB8Ev9TuRTEvO8L\noy91XwE9bhP7cfmDSjNMFBrxKGNinaVMWZKUXUHo9v5TiRs+p4T2Z4QBHDEC\nGZitHCArge2VLmUlCErrtpAy3MhEOpeWly80woEwM9bP60dydef9LEI4pno/\n35gpLPBi8Yb9YJCgZZbAOE7MpA93VQviGoDoO9boqnStLLVv5Sm65ts1XutE\nOzG15EaFI5N0FjroWtn3mFIHiz9yV3yi8+49ar5iMk1D65L2pi9Tt6yz9Iht\nmuT1FLPMeV8esQhRBWopLozxLD5XN2aO/9Z1Mkn/xjoBsDjc5k+nhHRBKLku\nxPwdNex6YMTlrZZCCXmkYUlGs128DEebVGSb5aVvSFk+Qx2RbGHCtTVyvF75\nDtzPWyser6GKk6AbhrOqyVTOyG2vWJQrOTqEkj4xoliFl8+VsAkyBDlf0T5O\n/oMf4hBYAkAFHQl6PoV6QvaJOASgPTPQeV5bT1525b0UmzMAPP41YAsbleAU\now7q\r\n=ItaF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "@floating-ui/react-dom", "version": "0.3.2", "dependencies": {"@floating-ui/dom": "^0.1.6", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d4041b655a16b3c6d08e0d34d468cc53dd15641d", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.3.2.tgz", "fileCount": 10, "integrity": "sha512-ggYLaBszX4C7NF6XasPZcSNuUJFUS/qAJevy3l6DZwkLsiNM/qnvTmtbWOk0iWFvEDporxYqQu+TZdJPPLAj/Q==", "signatures": [{"sig": "MEYCIQDYxYcA/gawxEJ0TKlm5m/HiBR7ERCSE2LTSglRnMw1EwIhAO3+ttNkL3Fi9wN0tYXj83XSEDT0dC+NCugdx7HzCJw1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13477, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsHxiCRA9TVsSAnZWagAAY6gP/3D9S2zHhdp2OMSCnQ1w\nA/mMVgUbzAPoOmEANvPcT/zHhaJiP8R3qLP9pA3a1Xs4NL3iAA2vGaO2tyNE\nJ/hDh3wkq/Tpk/6C8cWHI3v9zJhYk+H0UBFkmiESHdhdhS2K9nEICTVa8IbY\nSA+N3TLMDUmZCKnRu5T0vmdjqScWE/nP8Tb3KNWUGdG1u72RUXTratgdA1bn\nB+KKMBHUnXfFG/7zjSK2X/nD7Fk2XIpxZfEFcmXiezPqqxmgU33bBSNt+aAj\nmk377N8hLzTfc5mNxqHyyLCD/sp6/96II4Q5ft0M3R9oU8Y5PvUnd8dgpthJ\n8CAKNasllLJ0OmN0RR7idSHodBx69eoEgtwNlxaXsMg4p8wiEB3P7TUH2MtA\nDL9kyYeNfKUMwQWWkmK8BLC3RjCdw8GSxG0ubnLiWK7YRvFJ8mj1wrq/EoUn\ntj5vODZSokmKh1GHr88RJbUhMmmp8o6KvMUhN4sUJUk9ucsxHbRBG01PaDQr\nEfukp0Lzt9l87fqrmhnz52V9OHfHdR4Nvq1d27e7rMU81SSQOHYUUQ8JC9hP\n39Aj3WP85nfXs/y5tSQBerXXay56B1dz6MtSmFrwfEYLHdXuRSZz4KQNcSxV\n4M3MNCZph0DoJLPz41s9+41hjZD9e/jHDINco/yrLitFnbHh7hQ4EAJ9uPnf\nvP50\r\n=RArB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "@floating-ui/react-dom", "version": "0.3.3", "dependencies": {"@floating-ui/dom": "^0.1.7", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "47bdfca1a9bb47d3b2ec51e833e1aaa20958b944", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.3.3.tgz", "fileCount": 11, "integrity": "sha512-ALPyo9zJkRKsUFHNXtvBVB1B5zOhDfLtq4eJHYMt6DHt4wPiHgCoM8XtMiFz5s5P3cbznkNa6cUUIRn9j2F5/A==", "signatures": [{"sig": "MEUCIFJhWEeXCAI5s4wh0iJFcheNL10GDqS+Q9U3gzxR+akYAiEA/sqfUGL+6/z6zUzjBEVYB863dFwe5p4Pusx1uSU8mhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtIirCRA9TVsSAnZWagAA2gAP/3pavmWmJgWIDxw0pPqF\nke6HbNBhUznoYRBZ1eLyncjOtii2cfEsfW8Kuvmu+1Qma3NZGs4FojrtgxlY\nASoqL9UFj3n6XC1HqXqWZwjoPl6L9I7M0sE6tFFrPJyA1M6Qaj04STpBsZPe\nJB15RLZ2UYYQixwnIuVh5JsIDWsuwMYFzyDfDuOG67J2F/OiSfWaEH/q6dum\nim/XgQAHE97PFNQqKKcB40fpygA25FosxIsI/i761LhmduPOibxf+6ufzzez\ngJ7L4DcS1UOp6d0HcwsflYDqShnrKsfid5uR0l7KR1ZnKImzfYJvnC7apn9l\neAhkTMn8Ga3nRrPjpcmCNWwJREJDKorHfMeTT4h9CKPerTyq2REHIjW59mos\nvCqZTk9oPX+lgSlZQOXiurbZSqJf/LcRTAl9j0konjRimRVnfw/a1/QM0ywd\nH5frDYIkSIGyYwbejlsLcfK6W/nr/kTbi+xxysaspSp6TkU0E0sLgiELH6vn\nLivKe7R/Sinz2rV9lHzOTLaSUNMUuhNrCIaRta8ChFGzdXpDGIkMEWJW76O5\npvVMqEejB85E2+SHPG/ML8FWjhZuO35KOSAipu3RIBtC0xZqi9iWldCjLgIM\nYaa3IR9rc+hO1jwexS4ETmqrYZss3BPPAhYNQYKqs1vOGwsF1mzCKDtXvxyQ\n5nNP\r\n=c940\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.4": {"name": "@floating-ui/react-dom", "version": "0.3.4", "dependencies": {"@floating-ui/dom": "^0.1.8", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7dc7c09a4f645308521bcf2a58c45b4e9aa846d4", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.3.4.tgz", "fileCount": 11, "integrity": "sha512-/rnS+pXHLQC+q/koZD8f2O1utyJJCAzOvhxDfKBUUiEzVZmZE0ryzmpIcvs+3zUudpv8xP7cmWWSRi7Y1DsSdg==", "signatures": [{"sig": "MEQCICp92nN/7f6qAwopaj9AuetK4/wOdaFLKI8QMV9bJ/jOAiA+dJXbwa2TmILKBSg99RutOCk5q1sPKKGb3yekS56olg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwbUnCRA9TVsSAnZWagAA7koP/2YIi2vBbqikbk/g3mCG\niff+Q6CnuvOshIR+nMfPUiCO6ThJRSoqEbB5MYa9ewTzfzsVTQgZUxZTI9sO\nSGqqYfJK3cqCHFcBt8FocD6eOtuH6uUXhqokALD07psmO7kKPBvhaehTsiH2\nKNrEtgcLj3UzwTzJJ0VhdxCcOwAZSMtvee0iHEbJmpkX6rUz+tAuOKvJFfY2\nQW2ovXpf7WqVtvY0AbtGGGRrHb/UNRrsWsRUOLiw+2PeDoccCzf7ZxaVUjJt\nQox0lWdozHKa+FM2+qLi448OMiPJkztFAmqLBZxhmoFlgjKY7+16yr+5RcMu\nU4LGOkVweOzz5PqlfkZlKOq9NE6pJOGekMEj3d+cG96kEcsR6Sd22DgfcEO5\ncYHH/jtp3FDDqOAUOvFJ5ydAARv8kkYOyZp/6XmyiqxDgqcgcGzTVnczxjNu\nxUmb9W4DOLvcbsrolnyOgeGgowUoPJYDJYptwYfPe3FBenMp+dODu3+DXi7g\n2vgBFiTVqrcnJEw4e3coqAPIwzb09Tg+PeziqqUcyuFoKsjfGGaoolGqLb9v\nuKvHVLuBqYBES3iT/65NIc3wiLh9LFyZbi21g8Gb2IiWPGyxltZPuTkE+3n/\nuLLhwUzguA9rI3GsUW8Av04zzPJc2lA88WEDBfnr9ya54f9IAaA4rWxLuyZ0\nT1bg\r\n=UtIu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "@floating-ui/react-dom", "version": "0.4.0", "dependencies": {"@floating-ui/dom": "^0.1.9", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3666560336c887a1358088003c6e1b3f350195ae", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.4.0.tgz", "fileCount": 11, "integrity": "sha512-ZuXE01jFjpRRxBuPvOSbz/LMZClGTGdGvH8JHYEbD64mYzsv7Cmxqfyo4VA9XFAvlurGrZl74OMRSKCRjitxog==", "signatures": [{"sig": "MEQCIFLcg3Il8zQA0FFHkB7sANcYA5K9zaHeTYincEY64dajAiArOw1csU6ObKvWT9yWIPHB4rKhnweAhAJQrwdWYsaAOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyn4dCRA9TVsSAnZWagAApVMP/38PUdn1ogFkquNaX458\nY9+AOEAixv4lJx7L6Du70MB0kylSxeb6tVRmubJI3h5Qh17j6mtZvTCKkHmy\n4ZZHe1aHUn8kn56flFBPQ1mLCrTvD1Vf2044+M2b6v5tY+x8ZSns44MsqWV9\nzZ94pju/G6n5h1h86vm/urNVLU0vbYivBf12cAM0yYMXyKjtgP9vlbvmL61u\n60QeEdZ3UwFisDylPO5emblNpwFf+ZzcFgkBVK7QjSCbEua7qp4d6UDbzmfS\nHDnC7lET2xK2VfVt0Pav5QVfIW9MMGXvcfLAAGFg0w7v6V03X0YZpBIISE2+\nYccpw7c9tWIzP7fp5DefKD9LMp6FBfUpvKx2Pcdy+OjyYmTw1YdDeoFIllI3\nAePSls+NdMHkIf33Et7Ffza0aLMpTinQiBYXjtVhuFUPWqgmVH7n2tD7zYMI\nlSsGD72SnO410qWczWFrCAgtY3WQirNM4ztn32rLqB9Ruwj8f9lyFNuSn7RE\n61m+mE4VzWREUT/eLOFIT1vJBsGBeH/2EKLuWFtViK1mWdnx65FDISBVdEZa\nSIo48K7B0AVwsIGIGrY3VR+hJv8+VJD7uuvDgTQhQQjW0ibr3/ZI+iY66uAz\n95ik0XmNJCbtGd4FJyIp0rTFOmofUGzEgFfU/dRhcoeuiYMpowBUmOkD9Znp\nWAhw\r\n=KOmI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.1": {"name": "@floating-ui/react-dom", "version": "0.4.1", "dependencies": {"@floating-ui/dom": "^0.1.10", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "02ba79dedd3e1acad175383a73a2fb790ee4133a", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.4.1.tgz", "fileCount": 11, "integrity": "sha512-0MLbzWipGVcVliNTWSBMcD12JeGvTwzjnxpp3hoM8KHhfy9yAKVuBJwuo3P9X1eC8dSwlplVp5JNplJKsZkkIg==", "signatures": [{"sig": "MEUCIQCwM5ph8b/4/lg1RxRcwNteejlARaJWsdQJX1AVdhQTLwIgS1fLsvur5nwNu5G0JK0h0CXdwUsdbBDyj+IYAWLUHaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzbmRCRA9TVsSAnZWagAApS0P/1rpWrytvBPZzOd4V4K+\n+uTYrHkEGziWnxC0Mt6KT4IX4aV+0YkSsNGOHNKnHcKxCJhdbReq7Gj9jQ1d\n9VXMhvXIiq+My3r113Bg9rkQp1fpN4+u6WnMCJ1NjA3tkCZfmk+FdYnZoIPN\nNtwqLDkcJYI8MOi3NxG+C2whLubDWs96ygsd7BVfb0jArc1wz64Bok6aSxMc\nv9vAUR8KdH4ypzYheOVVYi7Ov08mdzwQqnXbiSEayynMkyajPl0vX17l+i0f\neLgbXUj+Qz3pLxp6bf9bHREWiCBXzWJK6zuYcdouW9RUYTJgRaf8ekOuYGrO\nfiiOYvc3Zhb6GmVylRdYZc8RUGmyMwhV0VYfRhcf1uQiSTDBvdonA3NSq8ep\nvEdekz195p80mx5QS1Pdz5wb5dmwT6+h1WlcSyTiROa+I/mKvXFGZUQE0lae\nwZP3K9bpoB1f1PWlZe4F6ym73m0UEehYjSH0tmUYdo1H6t8Ox5zU7kk4Xxhk\niXN35d3VWXL+I/3CCP/kQY9phXLqHf9RVThTIkDB95F3592/rboHpgFKAJpp\n7QNLmco4uFfX24Buk2IWOVVYy8hdkq0RAmaGoV2bd3JmooF+tWju28pDfs0H\n16Z2/RqTE1xMojePBTmMecWDMwgFDcYVXbhF3gCHbsmCqx7Rke1TOGowwqAe\nOswc\r\n=ubKJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.2": {"name": "@floating-ui/react-dom", "version": "0.4.2", "dependencies": {"@floating-ui/dom": "^0.1.10", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "38652e8c3242b894f1682ffce0d6216f07f99f6b", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.4.2.tgz", "fileCount": 11, "integrity": "sha512-o3Zj5NwbggKoBJIsARdL42J/Plsx0MFE1I8zKObFuQx/k6IkMsC3a/3Q0slbC80h/HOd2uxN07cGSU0sspHamw==", "signatures": [{"sig": "MEYCIQCYSDl44N2NAmaLOTn3BeYVaB1AFz/K8a4GOszr1/VPhQIhAPeLUOY1cCkWcgtF5ElH6Bzs5Reo++w6jWmfUoJbisOz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1lIKCRA9TVsSAnZWagAA0agP/1uQ3yx6jm131eMtUij5\n93q/gGBmilWZkE3I0RcE9HKCqHZZhblyh0SY7GdIAfqM65Y4eLL5g/SjRt4G\nTw1NNME2Bddvudklhn3+SwirLKeGH0jwpuECwNyUFpsGb16wXwRDwLQjFxav\ntRE1Z41RkLsC3LZgiYDZn3OZMi8fmsLcpRAJn9Lbw0gmBsiJM3UlwuS6iolU\nE39uB3Om04bauMAq84cms2lwoJeg1b+DA0wNIzPtDzSYheuMTE/18K6enhHa\nwbRttAUSPLjOXN2Mou0dH0U06F78YYQ3eJ/HJrR0NyJPK+u9Jf+y/E0WK0Ma\nN9iMtOJhBFM8NeKnKq90gn4DGATpHh/pV3YXO0nBWZMjfWnBFvGUppofDdKV\nOhAw4Hl+V08FR75tvFNosSLZ8Lux3iqZ5ciwq+jyRApe3Fvmgkz3JivPBym9\nQpWlecUpkYJK8q8h3xgg8xRKSVSvsVhWAxfT/VB6fHn9QATJUKzOgCm9q8yh\nnJkKSY29bG4kY/2z9eQmXRufXkglNLrdc1p+lvwVPIhwaXxjbBR5lfi7wIfl\nooPO7bMTKEtEDMKBqGcRxv1H6Ltpzw1Xvl98rjcCa4vPfyWYBms40F032uZa\nQO8dySrMsZWWM2JQtSMHG594toduwgaBbWf39HRIMY6ygRERhkKvq4GyM0RV\n9ZIz\r\n=T3Mc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.3": {"name": "@floating-ui/react-dom", "version": "0.4.3", "dependencies": {"@floating-ui/dom": "^0.1.10", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "84fc50ba9598b93d8fc6d59785bd2bcd96ea42cf", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.4.3.tgz", "fileCount": 11, "integrity": "sha512-ZL88ryd9p6sFh9jIC/+05JZoNsogcq6U09cygQjiy757QtQqxIVLQwFag+BAWWYqpNEMO0S60fkqmh8KIAV4oA==", "signatures": [{"sig": "MEQCIFSZxKXUTlFndrXx0EuZxbJjTiHTebQ2CiR91um/chFmAiA/GB2FsbVAgXasEe+pUrJQFchzayxGOLzW7hvzKlaP8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2okeCRA9TVsSAnZWagAA+fsP/jNVH/N6kG7MGx3XSged\nPsE0MwJuj5U8E6GiLispNXckBS92biLO44lYz5QNZJXBInRabMq4uTs+WtH2\nPz96IOxVkX6xIh6xLfYlaCHVJg7u1uzBPt3nfDLj4L+dak2pGqh+Jk0xHP3e\nRBjHeWiWRegHFQkL/FTJ34dIGwXDqpUNynYKljp5tZJHA3hYJ9fhrKx3uxF+\njUuHZj62JvdjsnO/eAjd1g2No0GlvsiY/aNSBWkzcIpdXMTA3GLlX6jIAbSv\n7H+yr25bB9wuuXF0PM2ewLj8J+HzzKZACHfGa8Ea1+ZH8kgiUElm6q8/0T7T\nfkgmqvslCzcIU/QypPjrjySXihcVmc9KUl0ZrOQmASHzI6PgKsZAtrliw4ee\nPsMJm6uTyz/+mS2BHRq1FO9VwQAH+f/6AXl2t0BvR1uSuvfut9M1xTU0vvHc\nyitfQYLlEP6EdRUOZ6o3rpP6TlWss47rAULJsE/3jG3HHYRrGyCS3rR0FHI4\nk6oWRa55Kwbs+VeENL1x/AiNsT8WHXGxy6IM7DYOYQus9rYBYn2CMh9inasz\n0PmGp7uM5vhjJbDhQw14KWVs8m5nQEox4GS4V3YSsILI7dM9EjM2+0jo63BE\ntQ9D6VCfmH2nNsXDM7Y2JXh5nV+5V/j5aAbSP832zFYeUb9mIuw/rp6hDahC\nrnPJ\r\n=APfl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.0": {"name": "@floating-ui/react-dom", "version": "0.5.0", "dependencies": {"@floating-ui/dom": "^0.3.0", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e3747850c45889bd0cd197f5fe434f1d813b0917", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.5.0.tgz", "fileCount": 11, "integrity": "sha512-nb9PlHPX1dXoJAJK5YGnwJjU64DscL/ec+j6C8UiCY2dUeknyaBpabOg/wr87FRwnk5v0v5jQZ0pbmK2qNvw7g==", "signatures": [{"sig": "MEUCIDGgs04XTlH2bUQTnMXk1UNGh/OCH0tQ56TKGzFLgR7PAiEAsX4lRpfHTKAv0Mi25HIhFols54RyhYUhmDuA4t5MyYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGKzBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVFQ//XN5JljPfzrcuun+Y54O8w+Q+tzLH2zAcUlFSygqEKuuElJdu\r\nTFiEKi4PG8pQJauwNChaGKDL5eDqIORWHMsVXQW6YEJx3udLLRhKDbcSekT3\r\nkdZCHfcVWc9ECnUiXPTktt6BMGS1uLkqY4vPqm1Z5y6EGRnAeLtKSUyT50kG\r\nhmbMu+fQlqsg/lEGH8vRq7KCxOWpqmbzjc2KX78cfAbaoTJ5y2k3OeUU+yI1\r\nmy2luDbV8dnJsbp3xIEFjx2uQLaaeT5tKOxPyjuFepQHqDhB7GiZX6JMIzFJ\r\n6sOxwuFTIjg90ZoWriHk+hSziuXukHdHbGmvKuiKiH6mC9b4/iF0FBi0ng4b\r\nosmKOOCbqIkPSAEwjYj0pYLD7KRVNGsRPR5Ikb34i0K2WzsXXOSaWBD6SH8K\r\nF3k3eUaZkxjmp+5Aru/W8516OyknXPEZkXDj2RGT58A7XSHuty5yEt+NdCTo\r\nTJoUZay6NGef/4Kv03oB0D/tx0reMstuMBLoJ+LQyme46anTMWdZ5gga8jMY\r\nennUMduiaTAfxHq7jeGk1HDDX0AVKCMr90Fcxbl0tStEudfgmrhC+ZiVD/I2\r\nAbXFYv2qBbY7LInjgpPLxgP7HEV5Wmzxzoby56fAme+r8DGj8JmhAkoqIELi\r\nizU/ZiXHoQopxWm4VzCt+SZqOqVHSK3w6DA=\r\n=08oj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.0": {"name": "@floating-ui/react-dom", "version": "0.6.0", "dependencies": {"@floating-ui/dom": "^0.4.0", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "parcel": "^2.0.1", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e58abf75bec1eac01f0adfff1454106bf9d3dd50", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.6.0.tgz", "fileCount": 11, "integrity": "sha512-/dF+jpAUtoonjs2lO0F+miqEQlzA9XJGOxWBiJsq/COhK2kz7XzryyJdxfaNEH+RN63vRs9osDolOJXlst50hg==", "signatures": [{"sig": "MEUCIQD54Q/c9+F5kuVPq6lthh4BK27uAMoN7eXx/jgssk1MeQIgOHx6YJFxOHYloAR4WdRuTxY0zcX3D4N18AN5vjR1vWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJz5VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLqw//RMU8ExBWhlDf8nsUpmZDoRnqnw802Hg12jW4rZIuB7f6elB8\r\n7BnSwvShcDrNMDirmeFD5qBzhsLqOKSNFpReAU+W90TuVdEpn6dLcCHTj+4H\r\nQI/cAhzG4HhHqriyuTxelU4PtuV5EXDvq5sZqcfFlBNdXfWyqGMCklHbbLb5\r\nTlVYTA9xY4vwahOfD60G3/qJtwtRFSOxSVa1ABKWOJB7MnnaC0NDyF9XVckS\r\nW9eY9YTzOiFndXIOtY9TBpkiIUR2FLvik99EIPBa0cUS675B/7fg5VVduqeJ\r\n9aNzdeUgKmw1A5HV1MZ/ipqhhp+HtcZM17iOo/CEm5KdJJuKFDhceHH5sIrq\r\n8bkWIyOKNlKR/ZYIWcrB0ykX5DpIYWlBPmD0SDBTKPjTA5qtmAU4cwiXUHQM\r\n4jV3eTaMk/YmRtm+2/K2hxcb3/WBuGkRO4sRsssJrCs1wbyNlxquGXP6D/HP\r\n6/F9amI+rhh6MP/xG8+frttMSmFPuYB8sEL98lihbdw5ANeIRMPva8elxRei\r\n11UymlWB3c6ZuYejyU5b/eyjnH8yR50bSGTYjk43uuG8mX8kstlPcMch0s5C\r\nt5fXHwObXwdjO6ygIbiToUcYaTHXxixmAgjshMMSlTCkAzJuI0c11agT6SkH\r\nUsirsEIvnbz7NG4ABupc1gdL7DYtgXLNnAw=\r\n=fsCU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.1": {"name": "@floating-ui/react-dom", "version": "0.6.1", "dependencies": {"@floating-ui/dom": "^0.4.2", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^17.0.2", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^17.0.2", "typescript": "^4.5.2", "@types/jest": "^27.0.3", "@types/react": "^17.0.37", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ca3d3d05ef00f50e3d0b64c18703e692a48b5c31", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.6.1.tgz", "fileCount": 11, "integrity": "sha512-XQbdgj1uWAViEua9PkfzgqOWjYP3dZy21pYpUYB7i5WOf1ZBAqTgCkyipjpJdxP8qGftjnhrTROLGs+DNGYnJg==", "signatures": [{"sig": "MEUCIC3LXQMWd/wqG92a/IlbpJco6qyTPxQx92SWCs4EjhNbAiEA1tjhgHlIX/eEVGV6Lokzk5yYBY/D0a2/B0f52Grvh8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiRRhRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofRw//Q0Jn4CFL0XDO2byuyMgtPUN3lt6rAcKZtcGn1fCmIlDoVeDE\r\npACNxmrU7m5e5BhHZv04SjtKYNACWZOD0rmxRYCyiDygWyQrP1VZ4HMbk5FT\r\nb8GMk5mPhy07Q+KFs4gl2/VPM606ZDFp1GrUY0WYaW/WELMuzHUy8bsMDaiY\r\nWU5G4lmpDMjg48RoOYHhCbYQFqTNissUZOW3xj3T7oXauiEiZXqZ8bm7scI0\r\nlCQ7SCY//IkwsgfIeJ5X4xJYs90cRgKCyFDl3IqGNyuH+Bcwwq7yPG6IcJCh\r\nTQvazWATwRYhJqiI6t1UBzJr/B4g5hWYFkjQvdxaKi5yWZ4ztM+RGnr17+nK\r\nsimxLWV5Pu4UvwJFUvzgYiLAHNbObTKvZCISR/A9eoYcNjIrp70yF3Yi6PtE\r\nIwzOVmZldPaRPeK5pTAoCnMIxAQlXuCZGG88D/Ril1+2DV9ffCpLY8ETMHfK\r\nwnvprrpnTFv1hy8TI0ohTb1oJz3hekBZfJm92pO3dBH1l26hlj8i8TLu9p4w\r\nekys+t+e++TBQIC5/diBISZ9EiV/Nm95hAbCnYgkd9VLwOntjBCOzOZRz/PJ\r\nj1o5ner9CwQfQuDabY0f4hyVjQMkCQUVH02xhRBuLYHwd6N2RY6TQB0Ig3hg\r\nPMop6U+h9MCpakoeCU0OcFoVnZwATlAGfkk=\r\n=7K8w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.2": {"name": "@floating-ui/react-dom", "version": "0.6.2", "dependencies": {"@floating-ui/dom": "^0.4.4", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "90747ce6da475a6d00301368632547329ec07eae", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.6.2.tgz", "fileCount": 11, "integrity": "sha512-nMk1ZDllNLagXrM4NmRkmC0BGxcDQUPIJsBxmvc45SzgjolcQK9b1Y0rGTqvJJIe6ae7xFd3c707vg5wM4hs5g==", "signatures": [{"sig": "MEUCIQCwhOGkG42OtYoJQHZFqtac/a5hKDU6lzC9FK4tgg96MgIgKJ1lXnQWi0yfTdCZUwjrBPTqIfdd16vOlfxLLsc45wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVYGdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou8hAAj59pCZqvbHHtpIjHsooWnDLWQFLDRb0o72XqH3tEiLpblfIn\r\nmjy9CVEuZkguFGhWu6S6VsYdunzzb1K5q3Xvz+SDI37ws6b9bpfLU9GDms79\r\nhsThIx1YMEsQoPCMCEwDmxn/JiOrojxjGJsxnH/PuRKOrjR/2c5ck7btlpHn\r\nXiK6Bib7oXrzOg681hEsvrYhGENcJDyc95JvkK8/wJOoeTIUaP5DBUluAINV\r\ng9lYsxRORCrnG6UESyqv+6sQu+FwlpgRCnFIDj0VlDzXozLQOeHMwdd6FOOS\r\n23a3REcmDLQlZICwy/iWfzSX9D/BSQJ2Yxx09V+bLgGsyb1h1dp1I8/5hTCZ\r\nPwbNYlwzK10FrIy2hib5aPrJWifQagC71pbRzxdiNl820ashoA195Is3P04m\r\n9CCtQNe0P/q+d5+Evom5WXS1rR259Uo6M4q5djz1Xep+/yKtaqRRSPIADE0E\r\n8E/CXQ+cEmdBCqec4tC8UPMjMPUs6GbZdRSr91cQku7GdGkvvEnMPq81AUGk\r\nZkyJJTxNC90gDbdnd1ZU4RjrgLoNQxEsnXMgMcT3BDE8NgP4tJDahmdqhfSX\r\na6BbMNY08Yk9h/ZPfg1VOV9yQ3uNjMgyzG1jFBWbm+OpwXGQQMI6qOuqndZt\r\nGzv/YqmBUO5LWotaHbJFsaG/pfmcwliIqGo=\r\n=4ify\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.3": {"name": "@floating-ui/react-dom", "version": "0.6.3", "dependencies": {"@floating-ui/dom": "^0.4.5", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^12.1.2", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "@testing-library/react-hooks": "^7.0.2", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7b64cfd4fd12e4a0515dbf1b2be16e48c9a06c5a", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.6.3.tgz", "fileCount": 12, "integrity": "sha512-hC+pS5D6AgS2wWjbmSQ6UR6Kpy+drvWGJIri6e1EDGADTPsCaa4KzCgmCczHrQeInx9tqs81EyDmbKJYY2swKg==", "signatures": [{"sig": "MEUCIQCc4o1GjUwt36qvxIZPRb6oQLWzXHal9j6/GsnFrB7U3gIgFJIEP6ybywAk+AN/U97glQa+7nezRkLilYZl6tsX0iU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXQvgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqgdw//Zn1E3XQnwC3urrW80lMZ5xs818c0TTeUYayFSMZ31X7a6mSn\r\nRJEuNPri713l9Xj85epk4lZlY40rjgtpV3PvxyfaTjMycbZa3PPymQ/HnSeO\r\nziKUCOlCxI5F0Xb+rQN30x2pmaQbij081dBY/7ky5fhyliSxeeUPYDJWpY4N\r\n0H7MZ/nWNWFhpGBQpH4wJoArAh5EUsVseiefQ8rHYhim3qF476JReAAzVps8\r\nWZ87N2nORlN8RU7YAMtIpgNDbaWJtPeRjdol1tVqltcWaF1SKUfpt5RBBiAA\r\nljXoH68dsNSYdxzImHkG0o3ulnqqTuFA639UDQg4VqSA2jIEreLgaCd79TRU\r\nNwJgf83rD4UZZfPGxoRsIzYnLmLkf1Nb0M5eKDtv6OunkUa5y4r9Uyz8EdDz\r\nx+H176BQlYsOcopiwaRbdWNhOxd7k6nzCKtePjcVueExXmo7+oNqDOVcf9hx\r\nLL8ZueWUsw69NwBWa3B/vrGXVsI3Z60U/MYlNWDC4UE1Zx+1BjsRddvIf0CS\r\n0Ht3JpyzGPJHfx4WOalLZcX4y+hW+JxYOt+VZ7zDbIuUfCirpeSwqrk6Qjh5\r\n2IMXoggW2E6xxg2n32tADF4lZ4OepKKZJL1J6YMmtF51/PmSllLR9+oImnhG\r\nb7JzUz34J/nmCAcuGXV9Y9tbDTnHKZYER+I=\r\n=lPD7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.0": {"name": "@floating-ui/react-dom", "version": "0.7.0", "dependencies": {"@floating-ui/dom": "^0.5.0", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "8f867b6fbf87dbfde6f55fd5f5c2c53cfdd1c873", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.7.0.tgz", "fileCount": 12, "integrity": "sha512-mpYGykTqwtBYT+ZTQQ2OfZ6wXJNuUgmqqD9ooCgbMRgvul6InFOTtWYvtujps439hmOFiVPm4PoBkEEn5imidg==", "signatures": [{"sig": "MEQCIAvZJqhr1UW6OpWWQ6H0ullXZ1ZnVbjlLwyf0xCoaSpjAiBJ2qRyKBKgGDOur0xrZpi28aTQ9FHMK5uWuU9bW1rX/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidezEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoplg//V8wxq/FdLmRIVa45iPDvDJf2mfhc4CNQ+ufRiqfoNIZzhlHQ\r\nIipbQUGujzIsC0jCM7ap/t1L7xFNFEL8q7zvGfdnk1zYAy/pASSiavNp1HxP\r\nLpqKgqibj1zTo8zHGHuMpYAKqU7iz3cmA3x/YrtMFg/KtSX5W0gO6/gnabeu\r\naFzvyqcV3gKkw1GOC71ppbt3LYnq0c64w9xhDRyoHAvFVRQ7wSuCJrz0ijh4\r\nTqTQSvbiWfjopmJ0HXyaY1BzZqN35PjdGDmq7Lmb89ZhU/QOQMRPDIPKBJOl\r\nIGqP7t+Vkv9XzHWOuSPISUu2+ZM4r5Vr7b3eRwVzJarPYLd1rGTPkkWev8db\r\nm4914K7ANfKRgohBfgd/AYGfUPklsMpocDrbQQDf1CXLaXI08frA66jB/it+\r\ng2x8IZvLt+M63ORv7/vI/hvBPFc2GGtDvJP7xTARPPap29ZQh2J32mvG/O5A\r\n05in1tPPcECs32KhktgjnUa6p7cxniOpUyfi6zQNle1jF2JnqIyZXIZrgJ/c\r\n/8pDxghfwivOPmJr+9OesQV+UmfeCC1leCbQ5GJEtERVXzZuXihI0amZyX0N\r\nXkEmMCUjJt11LyrdgJAsUp1+5lhfaWZYLw/2db0x+/FHu+OnVfznMkKOnXfa\r\nKJQMshWf5nWc7lSzCMNiQcFny6ZlEm+HcEY=\r\n=9WNx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.1": {"name": "@floating-ui/react-dom", "version": "0.7.1", "dependencies": {"@floating-ui/dom": "^0.5.1", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e0eb57cb05c7762d4c904bfbae73148684578d66", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.7.1.tgz", "fileCount": 15, "integrity": "sha512-Dd7e8AupUjzcjeGf1g3EItf/QRtEWKF5GGyEs5WA5n3zlHvEgZ4XrZM6ANhUnzgE3pUQAaXkcXLnibgFp1YBRw==", "signatures": [{"sig": "MEYCIQCyZXAqEwbFzO7mSVxVH0hTaYllNYRFvc1F0g3rrIoM/wIhAOMi2kC23t4GLgNLr6RVqXYe5PpmQWteiHsNZoTAF8kP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihv8pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWThAAhLNzWAz1C+GrMAY4Nay/fKYY7sZ3jHFI3rYDlYUzwRXW22EK\r\nAfkuebIULwuJkT/IQYgciU3VmiLztIInCYq08AfFAQwqIzaEUBUDS55royQO\r\nGDvY6cTmY7z6Rm0Xy/6F3TDJxQxb34hoNf4HEajhLbL6iXdtMbyI64ZJmlis\r\nvKzcvOt7WloLqm5aM/fktlGxveropYZiyXCxwBJU8tsOMfyAL/SbUaSXVtbk\r\nFjU6x8e8j9ZdI5L3uxkFotNBfjoud830KSpmPlAxm1qEzwiIZULSrshnzq+Y\r\neRp9zwYBls3czzF3RBleUJ4oxrF2ZjrwGzL1ZQfYKhnkWf9fFIsihIT5zqae\r\nWSLvE9Bg7H5gFERff9rjXrBCRC0PBSRoNI05WocBk9IugmzH0SCYpTTxShbC\r\nPoGa0ix0ahkhqdCt89Wii0qEeoMnBcukol+4L8TfLJzcLMnczaA4L0HJrhBq\r\nmoOaSshJLRFbQE7/gMzZ0Z9wgyWPAvGdzz31ZWPb45yUV5m817s6TSCx5wxL\r\n+vGTU/RYm1IDQ2Oj37XbytsPtgqBhTEBDgcYPCzfxIMYLOAxskp35Xdlgefn\r\nyzQ+50MTl5W5TZkmny/I1KizvRWIHhnfw+GxlDV+DPvAyMgn/d8Hkqf1SXOM\r\nHp4rrMItrz42sKTIH/jeh3hFnxv1NR+wTek=\r\n=cmTI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.2": {"name": "@floating-ui/react-dom", "version": "0.7.2", "dependencies": {"@floating-ui/dom": "^0.5.3", "use-isomorphic-layout-effect": "^1.1.1"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "0bf4ceccb777a140fc535c87eb5d6241c8e89864", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-0.7.2.tgz", "fileCount": 15, "integrity": "sha512-1T0sJcpHgX/u4I1OzIEhlcrvkUN8ln39nz7fMoE/2HDHrPiMFoOGR7++GYyfUmIQHkkrTinaeQsO3XWubjSvGg==", "signatures": [{"sig": "MEYCIQDEr23Q/oxy7Z6De5wo1Nwxtawj7JOMoudDiApJr0MdGQIhALJRO49M9nlqD3rIe8ZJ8aVFicI5c30sMFw0S+NWUQwM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipT3pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq94Q//RlGPuqL3wgOjNqrBSh3nVhSqFXp8afhsXrTtybbBGxtqWmol\r\nEy3oz9BE6xNKTLRrYxDex5bvFcEhjJXYkPPgoGnzxvwg5ivyqf/fn5FvqceA\r\nVXGknMoq9+OBJ0n9hI+Vvra8pgmsZzNH60ym34u4pyVo9Clt2huTosvbslkZ\r\nhEP6DcScHT00F6b4Yf47PFtZJTJ1r+a7w1szh3M0/3RR/EnvJ9n/+STvQ4Fm\r\nPp9w6p1CG1mEKjJj6wLEi+t51YPPEMlE8yITzGdPaV0fT+BIAhlRqDRhi7nu\r\nmwF/qYnUmkEaOj3Z1FT9TAMz9BZCiY66fStPeyKecMNh0Pxkwy8tVm5x7tvO\r\nx+I6OF9/MWIuNspChJmvu118nTYXEw2V2A4Z8YFsK63VTsUaBmh1ukiAdLM3\r\npMGqscAq+Swuj1JxlR4W42sePCWQWH4xHK2xxFifExnk36pRJdk25aNB7Y7A\r\nCgbVjUpaIYJ/Tcm70R4kmfTe4wusfQUGeDZyHNUFddKjsOxGPdiBwadB9RUJ\r\nSdH1iy06LUgr3RbbkD1j2QnXo0HtWy8reX0mAH5TSE1ZCnYoQvNJUuh/80Ri\r\nFE2yP2Alv/UUf6tTfRSxf5/UvNAeglJx1RIjZJNImjc9bse34gY8KbL0UEnB\r\nEBFdCTNKrw/IYjcMe57l3UatETf87/WhvZs=\r\n=KsvX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@floating-ui/react-dom", "version": "1.0.0", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "use-isomorphic-layout-effect": "^1.1.1", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e0975966694433f1f0abffeee5d8e6bb69b7d16e", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.0.0.tgz", "fileCount": 15, "integrity": "sha512-uiOalFKPG937UCLm42RxjESTWUVpbbatvlphQAU6bsv+ence6IoVG8JOUZcy8eW81NkU+Idiwvx10WFLmR4MIg==", "signatures": [{"sig": "MEYCIQDvqMzfAKiTr6fj1DOoOOmkwpiZytQNv64Zc0+XhdYPfwIhAJeIYYLRkWsosZXfX1kwDPkGvw3Pn5Ts0f9O/3ymYnFi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1pnlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKnA//TV95cDx+a4fUj2jXGZCcTsartXOj5T36aA3ErqXEakkgvDjG\r\nyD8yHT1ftKMjPBtkwfoBb6ZHXy+R4vxjhZIjDarWqI4p+UkH1iiJJ6gMbi0b\r\nMPuRunjV7K4GLsT3BMdlE84AlyNqeg6/VgBtt+wlvmX6kAdVbo7obKW+1RcJ\r\nP/yBa4+rLVSuLumjmdj8me08/UKec4wuJh4drBpQoFnsE91G+RUDJcpIgTnK\r\nlUyAdLORkErcBnpT8riAl7YSdsKo8U/1Kt194P7qQ2ngDDdL65rS00/DqCE4\r\nha8SSEnK/GMVhndsmU3go7i+LDloQFXTpBLCBmWrpNntorKTr7xsPm8ZmzwI\r\njBD/TiBOD040rFhSIrdPvQVmYWrg7+tuWxjDg8MSOA5bRXV7qsy0bRhXlHm3\r\nme5mrpZcQBO/9GM7JsAAXN26erP4Ub5UyZA0Vc27VBrxxXPVFX16o5h6pnI5\r\nCTphjba2ELMrjo6FhR8ai4jtrrQEXmDnO+ioQET0HNaUuGCznpNmqEDcPI9C\r\nL4DTDx2/Uh1tIHivrDEbQ7XmDJUKmJ/VNqWgX0ctAi1Gw/KfMrQbzwU0lyvG\r\nWvCNDxKayi3ZhvXtWotIFTwo2mATIBsJoZTwCtC4fvePs881PrN+gygfX3h/\r\nkdDLswrqQWiEQZyLPdzHXZ6G8r7UoTbC1qM=\r\n=3bhj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@floating-ui/react-dom", "version": "1.0.1", "dependencies": {"@floating-ui/dom": "^1.0.5"}, "devDependencies": {"jest": "^27.3.1", "react": "^18.0.0", "rollup": "^2.60.1", "ts-jest": "^27.0.7", "react-dom": "^18.0.0", "@types/jest": "^27.0.3", "@types/react": "^18.0.1", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "@rollup/plugin-replace": "^3.0.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "@babel/preset-typescript": "^7.16.0", "@rollup/plugin-node-resolve": "^13.0.6", "use-isomorphic-layout-effect": "^1.1.1", "babel-plugin-annotate-pure-calls": "^0.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ee3524eab740b9380a00f4c2629a758093414325", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.0.1.tgz", "fileCount": 15, "integrity": "sha512-UW0t1Gi8ikbDRr8cQPVcqIDMBwUEENe5V4wlHWdrJ5egFnRQFBV9JirauTBFI6S8sM1qFUC1i+qa3g87E6CLTw==", "signatures": [{"sig": "MEYCIQDipcsfdEAt1dVWAxA3Vqg7Mg/ZzK0ptR7MYHnas56dIAIhAL1Rwg1pPxsGZl7YkAi8nXPADV+9fY6Tqoham7xhuVuY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc5fxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorpg//ezV9dWJzMsUt7asNGZ4eNp5/QCKVUpIuc+rZrcngV8fvMxAi\r\nb/nz3laGRhF55bBRcPpxf93v+zioX8bdAR6n0PY4aoXEXIGURk2uPUKlT1t4\r\nVwg6pAfMwDGmd/4ixozrPz/IsTXL6NDCsvo/ApNlk6SlRsxY939euJC/65zP\r\nRNZ8QmMueS0okJV/vZw63SzFeHhl5LLe90vcIM/5HDDq9N1Se2FqhFjQeqB0\r\n8b3nwSvfIFO36Q0TCLeOBq3iTVS97OxiC6WrQvUKGJWwMfQRyGJ5HLZiEsmi\r\nxLNH3eVPNZ4ZuFaZC0rUQOuxiRk4mzxP14IRBn0AELVvf6rO1wnfU2vYJHaD\r\nAWIOh1iI/+KjvVJgIipcH+H0UNEo1106p/nISAR5IoeMaeeRzFWH1oFAvN1X\r\n03DmdwIMw5Nnfmm/XW9JtTdpYaFcMESkss+GQJaKhZTYJGhzTRnagBVv914W\r\n5nJ1WvPhI1z4N+r2lhAPGncDmDGgctpmjk+a+v097kHuNUcf+9ZTq826gBwG\r\nICrX5Cn/REvsJqumzJgVEAPyGhiYrobNhjhMiQlSlmnUsAlhvncU7m95X0K5\r\n/ayDjw61RjmvVm2r6TO6If/AEJTALWQ1HVSbHffKsSnz3fVpqVf8ux0o7waq\r\noTWDEtdAoOMkgVEvKOONfYQqkKaESld+cOs=\r\n=B5j6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@floating-ui/react-dom", "version": "1.1.0", "dependencies": {"@floating-ui/dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "cd212b9e98303ba76e59806e425217f40e33926d", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.1.0.tgz", "fileCount": 15, "integrity": "sha512-xyP9dMqZouuQ7h10DvbEbxZItuUleKHXe7XQFvbpttjXZrfyQMaZVTnWsE/t3hrHakZy18HxEBZRzGNXQHd0GA==", "signatures": [{"sig": "MEQCIFM7IA78KTvgx8A2dC7Rm7u1A5SWFh7ClVtLLplQcrIbAiAxf3EgQC9gRD20kprGaaCy65TGp/RP/YJb5MBLL0wnxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjqTdtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr36hAAh/u7GMSF2pvMzTWaWNKs4RnwetYTezf8LCU0Y4M6aY1BKvy8\r\nzlk/KHN8Gh316+tqHU5QUmDDy1rhqsQO1v4Fcn8UNKf5nWjwFh5+BtIwcRPE\r\nAomOaLag8oLD85MgX1mShn//Kjw8XLBWCPE4ibTnBEanJFkfUCk4nn7SuzAG\r\neKrG1tcj80ZUdydrumOnvYla1+xROwP4orABZdiYZpWLHt+0b29BeWqNTfmV\r\nxOhiKI2uaSZV77ijq7cxbV5BVFMVKDJfE6EqVAsbipG2cal7pkO9smFnNVeF\r\n/26IO7maUUvulPeqK9hnPCNuK/5xpaRYI0fU1TWsPFPaz7hzb1O2Zr0/G680\r\nZUmPV+pNuQ5cplZk9oZuQm4I9Dbslt6SI5XHhpKcnWT9TngLgZuGPYuhVxXe\r\nYs817j2VOY1C2o1cCGhQUz9WbDKs0yh08skRvkBCFaAYKOlAfeAkQYOBNLIl\r\nZyJ1X1/vpeTUV4zoZdMFlQoHs5QWJRoo9sxh772fFWZ9wkn5xDTBJnEF6Hpo\r\nnFTH77W6aFzXOKjtBPFmCmVlSE/DUCSTNFsu/0axlcdU/qrcu8ppUQnoyU7Z\r\n3V1BQIjhSJVVXJyNfPslyeSTYXoRupCvjshkEgLFA5boS5iXmnUfEzoMhhSX\r\nS3TbY3iAzSxNhWi6/+8s06aYwFVu935IpYw=\r\n=fncB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@floating-ui/react-dom", "version": "1.1.1", "dependencies": {"@floating-ui/dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "8289bd28188fa0fce7e24fc2a6e57f3d94fea930", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.1.1.tgz", "fileCount": 15, "integrity": "sha512-F27E+7SLB5NZvwF9Egqx/PlvxOhMnA6k/yNMQUqaQ9BPZdr4fQgSW6J6AKNIrBQElBT8IRDtv9j6h7FDkgp3dA==", "signatures": [{"sig": "MEUCIQCXy/lEAO5BhrgwSBfNz8Wu9IOUijMfFA6T2LCHP8UwPAIgIEUapR3OzfoqlIp8+br1Hc23VvYfyh2/KZKbnLXcKfU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtDsnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp52A//QJQHNueY9oueNapc8uGKyLsKN6o4cbizjXVoXtP4pS8Esesh\r\nYPUaGygn5FL+Of3zLXoVI8Cn/HIR3VwA5QcyRNKFehQQNv5wm5vtPrVgoN2w\r\nVDWs1KG+9gJBnBzw5gTZV0zYRc9be8GWF7pHHcsRvgwHfs8DdogiGX59Ploj\r\njc6+N/b0R0YeCWYvz04FoGpY8OvzahbtxYVfyOrPDQv/MbJmGN8/SBt3kKWU\r\n1kjQQEG1AU79H56DKSFDBWlFPxi0gWifkbjO7t06eYODO30HRV4xKcMCVGaK\r\nyxic6g5COHCKhHtaAuZXzFwboPq4DVb1j3Cao/yhtOnGStwqq12klVdappZY\r\n0TFtRwyiOM4KeVf0XboW1RRVOTM+zwzyjnm/8s46dJStkrNIwBVgirALsOn7\r\nQYKyntg9tFf4lRGU5bDqxg+o6BHsEfJRFeF/Ys2m8YnQdOZwJ0HoBHz1xVZY\r\n9PAMVqxl6fhCKvukomWRuzBeF7Ld2WROmRH/0cZy+pso64hab2Q/xmoW8rzR\r\nkMc5ukwsEmrNsjmicRrpgvKV+GSEbZgdwAt7/uVeCWuSmRq3Ii60j/lEQ/ww\r\n69e48v2o0zHmXAjkCF4YYF/q6o2qMBx74zYX+U0jZ958QBC9DDevMQquXvnP\r\n/oEqFAUAwuSf239TfVXj6HRHYemTa8zWbvM=\r\n=sffN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2": {"name": "@floating-ui/react-dom", "version": "1.1.2", "dependencies": {"@floating-ui/dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "214d395adf0ed5277e824505f7ee0f5a367ec239", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.1.2.tgz", "fileCount": 15, "integrity": "sha512-dtz6NGI9nfWll0TPcL5fZzhUoaWo7UpMSmhHYdABQoBA9V/BUVye4SiRa/affb4IocVZefN6HaX+ihrb3kj/dg==", "signatures": [{"sig": "MEUCIBMLfT0Q8zaA9G3AV//Fy3pMcU8dtb55JhOkrxJJg4aWAiEAzLgNYdCdakTx5SzjBLyflog4W4QrihjKCCrBYUqUEOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuVyiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpiqg//aIMps9/AlvPgB2mz2NkyN9JInIr2RDMGSPYtNGvCSVx3ADpy\r\nlgib7PcPiiV4br5NjUbD/xFhDmqR1prl6gSIVV0EnGQrQ0axMacJ242AGdNL\r\nax9VqoHq3TYuBi3oBeE5q4CdhMyU7lKiwWG/pYkRRPxPjTwfy9CtSaKe5zyz\r\nHkGGHouVyDHuDlXcrF7p64kd+awkHpqkkNYvL9szymVVHB06wha4BUrabIXf\r\nAovLEIToU4B/SwQFSYoowyNnHG16HNo8LnTlgpQgATSQcmPa50TdgFkZQoTg\r\nvrKTO7+PTUs1GU6ZVmhVYSB8/OeOoPdQyDGEMuGLMMDu1FMobD2obWnlEtgv\r\n2wcD9Av6ZxZS0SHNRBtbszWf66sNhQYNxIDEZBpSa7eSJEfHoN/wdRgCWtTy\r\nnY7Wf7O+TT+SMGPHSe1Utw67shY0ffxJWSimmRK/NidiugE4NQvQdLm05YSe\r\nlRLV6+F7+IExmGh9miTA9QKiTmx6QcNueu5jJYqzZl/gekzckzf7OX3pF92Y\r\nbg6/P502ijkeT8E0fAq3wSNoN+a7+66v/1lHPIP4YhJd6bUJLBX/Q5Mm1Tf5\r\nWzUcLNbsI/JqwTPm9p9JPPKTNrWTvYdfsxepPV8WEJ0T7NY4a8xRYCRjbMfr\r\nQCb+m2f26oqvP2mXKaOkab4db+vku+wPASI=\r\n=xpVI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "@floating-ui/react-dom", "version": "1.2.0", "dependencies": {"@floating-ui/dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "bd152ab3d57dd84b27cb367e11fd8b6c1ac95b43", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.2.0.tgz", "fileCount": 15, "integrity": "sha512-o1uQ9gPhgBajY/9T0boFNOw9MoxcuHOnVortmdlGCViM7I/aONbY+G1wejbqydHQ7+9j3p7hIsAUMrr3huV/JQ==", "signatures": [{"sig": "MEUCIQDKrf/+ITux4uPfbNnB8QOPKh7Gx0+nk2oQSQHR38KlAgIgQIdwLX/xCLk18t9UK9uMIK7M1Ha6UeJvpZB+epeQgi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjvZjpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/3A/+I1/ecmoa7Xt+tBAFhVqppV3rKzvO31xVxUyWEACUKc2PDAf0\r\nvogASg+mBrKox18sPpNjutRAqrCRAgWBmMi5389Rv/L7BE5ZWAstS9c5YSpi\r\nSWftOciRd8+osLVeC9v0/ooLNxpNkW6C55Ji9XMaD1/FD00Rzv/TnweDkz7T\r\nqIdJYd04Xs9PajacPUTQvr1JUcodVdh4/riijm+kLWnUECqqP+/9qKsDl2NC\r\nWwRGevXQ3avqu8Do9clYEQzYSPJaE73f/PBZiQu+haQaZhuvcWuPs6G78Wxt\r\nDmfzKqyZAvemKSwBIzouvbROdvoVwD2pLu4A8oimmugio/jJpWfCK7RFGftZ\r\n+E5t6+P0GEOlpHynrl/GTeussPcYFIPH/tISc5DmyqGXKblUE36Q6GwjSSez\r\noKZs71wCMSw4KkK5dpVoVn7VcgXl11HOjnwq2rT9Rm5QnxZPotMt0B8MIxbP\r\n+8YqBB5QvZgwHwuU/bqoHbvoR12KohH3yCV9sKtsOyBlk8mFd6UvlKIkzxDp\r\nv6bq65irOg6ue5gbJTIr4peBG/gbDEuH3cwSo6kZa3ftnzVfI3R9pzyB5XnC\r\nl8B89hgI58Bu1uMbG1X/mQX+mpNoVZ6MoeRVsVixWy7xwcXP/p2cJ4VAR68C\r\n71yC9HuZTAaLtm1qVWQ7025fdxpmqTPdTZo=\r\n=11iu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.1": {"name": "@floating-ui/react-dom", "version": "1.2.1", "dependencies": {"@floating-ui/dom": "^1.1.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7386c33fa07b0eb2b2a8b3ec9910d70a156f81ef", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.2.1.tgz", "fileCount": 15, "integrity": "sha512-YCLlqibZtgUhxUpxkSp1oekvYgH/jI4KdZEJv85E62twlZHN43xdlQNe6JcF4ROD3/Zu6juNHN+aOygN+6yZjg==", "signatures": [{"sig": "MEYCIQD7NNcN6Ycu1ysvB6Yt/Srs2bRzEw9c2Y0D5Zgjp8esYgIhAIfRkCXY7WzsHaD9JehT4T6P4HM/03N3H3l/DTjSfLg9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxYUlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDfRAAjNU9o0243/Z0kB8HOmVmUL3MJCY4x2AnFD8V/+gpO9ApYU/f\r\nMP47WJx3vRzsMtYfTn6M8NhAW/ks0CxhYqEpeldY4qhzsVZUe60u+33ZjVr6\r\nsPo0LkhxhIEq2hzd/8oIRWzseZ2UfHcvacADRJZ4GtVMyxAlDqA9IknkMd8D\r\nOYPJ4eb30NfY2WATtzpcQjhpo87Hbik6utWGRPq/FmoEMpkyiNeS+LCxWOIW\r\nDunW0/tcXe9y4TcqywY943o+C8/MAYpzDLEh+7QyKrSZH5ChtB2skyWSGHfH\r\n0XBiUBsW3Fzz1Kw66s6yFl0R6xGPZQwIapgWKafEw67DqzQV4bBlrxS/pqDw\r\nbqxhKzfaTVisN1aedr1E4IRx84nSCoOle9agcDvhnXyjcPZODiNh7jTNTKi0\r\ng+DxZQfL+uLRWNu6CM7ptBzgoEdF3Ou1qA6Qbr+QW13pLrZzsHUvdbLCLv5O\r\nzp9gso72j2kFZRLeHxp/AJv4xSsdZW+Up29tMKfisaYtrTGR9O8iGrkxV0bS\r\njWdw7nLa2RdzPM0JujGh3do1W3CPk2WLQh75K39nZsLqqUOcm9cXEd9PJELJ\r\nNawhAlb112FN7CG14DaZVA+GZ6Caca5znRsfym3mnDWHdCJVD/z2yLImS19n\r\nndaHVlbvLc9mRn0l0hNlmUpdd5V4zQS2eis=\r\n=60Rg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.2": {"name": "@floating-ui/react-dom", "version": "1.2.2", "dependencies": {"@floating-ui/dom": "^1.1.1"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ed256992fd44fcfcddc96da68b4b92f123d61871", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.2.2.tgz", "fileCount": 15, "integrity": "sha512-DbmFBLwFrZhtXgCI2ra7wXYT8L2BN4/4AMQKyu05qzsVji51tXOfF36VE2gpMB6nhJGHa85PdEg75FB4+vnLFQ==", "signatures": [{"sig": "MEUCIDoRxh7QdpeXGgFqhg1DteNf0DV02ovx+cRxewiBsQytAiEAu2p1Vd+WBhUmDnOXVJa2vMkqEiL/S0po1s+NoSYbTHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1TGgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSiA/+PNVfCj2hO8ezchzCGI3B9YoepNNRfx8bxdKajnP3iOojnDh+\r\np9gPMaJifvVlfy1y1wWMPVjnFDn/BHJfFgS0Egyw+fQT2YwBlAnsibryIEvb\r\nVyabbWjNP/df/qRfXvf6xkU0iw945AcoLrAV7sEVF+3JM75PgYt2kkk0yRAT\r\nHesmyiaM9+bymsQXdCK09edEBZvG4y8l0WmC9VHiAGeEuAPKp0nTFES8xcvP\r\nBqAKazrrGsEyifIYa7Cxal2695XKtclRz9Kdj5I7oFaxDbjIS5fcd5JVmDYJ\r\n9eGwN3FSVD6lUsw+t9kHmvpZ3rUCsyBRYMOh5T3BgBJj/i1V5HynkKy58WFq\r\ncqmLuLZLMJ3ACAzQMYr8nZRCYARrmGjClw+360pYsupensSi/JKOaN9cbLAl\r\nddOpIf6JfR2CG7j+Zs1Gqt2F1Lwpp2wJkphwzVlgCPvbrjpMaz6gALplN2Xc\r\nrdTS8OPxhJQWYBS6XWwVUgajsmN1g/yRHlsblfdYeR/N5kozHF1ghQ++J6T1\r\nARlX6Q3vKgw5PEBj65AyOOcG/a/FeqMft3kUrxkIrrvnNknAD/9boLJLpNxg\r\nTysP/1VTAas926oCgH22EJC1NHjyFWTXFF0kbjkj/g6be8S+dQbH7uewKSy+\r\nHezFQYnT2ztJeuOTGZuxJfHSJEcIQKpQmK0=\r\n=WFPV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@floating-ui/react-dom", "version": "1.3.0", "dependencies": {"@floating-ui/dom": "^1.2.1"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.1", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4d35d416eb19811c2b0e9271100a6aa18c1579b3", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-1.3.0.tgz", "fileCount": 15, "integrity": "sha512-htwHm67Ji5E/pROEAr7f8IKFShuiCKHwUC/UY4vC3I5jiSvGFAYnSYiZO5MlGmads+QqvUkR9ANHEguGrDv72g==", "signatures": [{"sig": "MEYCIQDQ2Hxtktd0DSpBUGeR9ligleaiGkZ8/+8TTQw9CGt01QIhAPay51lcsYr2yPyEHKO+ngoyj86X4CBQeCKkc3z/iroM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6LQPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl7hAAokK+CvVUsTD+SUjJxbSV2dtIdRPEL8oRrL16rHkSwIPHX32u\r\nsPQ84QRzGa/ftTEQX1DrE4Vt/P86yCJfBat/Ubu2QQ42oSQyr21bl+E3wJtG\r\nozOWiQh4n+8zP3TKaoM3vIBl+7gIMXyYIcbSBrzODWAZ24PWmRQr76wzo+4h\r\ni9PU/hbaSj+2gc428fJDXYibv35WUQmLhc7zkxi5JRpcnNSDkheJRT5kb+1u\r\nDIJzDVlyLZJgJuUiVkP2AJXpGubtpN0mzXn2FC54ZqS11mqmWcdlMzQyLdAz\r\nNftEWtttTrFX9a8iCBDZkLlv0jje/EdC+wD3g2VWXH2RIHIWBZOsJN9Q49La\r\n+bs0q4cpuH5G++xMv02qkE+uzLoRurSdxhEbobr86AW/eA5HXWj6+OePU0bh\r\n2/5Vlcs0kASZwhYt6FtbV0sV3WSLdwfJSdSGQN8ffRxQWl33TC7wq9emgJCw\r\naNxrjHbaQJLvc3VIRHUNSb1qVFRIXQw/7iu1WoDHZPISgFz766SPlngISgMZ\r\nC6AoKeP67FE22GBPJxX4LnXpkAu4StdQv6Gwp/S4robdt8RnczMwoZY/cn1S\r\nVa+B8MOCXVASGhmNMv5SST2P2ihqdHhoeGJN+UVuHsJYM429hgo1F39G5QHK\r\n2CMlGA34H6oGjWNIpTWQ6iUe6U4kfo0/Pi0=\r\n=9NwB\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@floating-ui/react-dom", "version": "2.0.0", "dependencies": {"@floating-ui/dom": "^1.2.7"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7514baac526c818892bbcc84e1c3115008c029f9", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.0.tgz", "fileCount": 17, "integrity": "sha512-Ke0oU3SeuABC2C4OFu2mSAwHIP5WUiV98O9YWoHV4Q5aT6E9k06DV0Khi5uYspR8xmmBk08t8ZDcz3TR3ARkEg==", "signatures": [{"sig": "MEYCIQD8meEFlajxS2MkCLJOC9EfAKtPFNQbn2Q+l+v8mF2ppQIhAKwWKeWBz4ofiXxZcSjfq7kOfz8edg+aPh6cW849pqmx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkThZ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBcA//QuABS789XXHD/kSkctksSsTuZIgETm4q9ew5AcjvtUnRr36O\r\n288+jbAipa0ANEu+BtDk8kQIdQvuWeKbM8iFP2dInRCUAqxuaBjJG1fH/3wP\r\nvB7We7kTm4w3GvlxcI4vRJYe0Vfy2m3uD9t7WWM1oEMDsV9t49lqLpo/AdhC\r\nwY4ANSfqrZdWEV+9Pvh4MDuWhi01HZBzARZl62nup+1bprkeiFTn5LjU548S\r\nSj2JEmmiYG3Kalb8+K4QbfcTPmNUlrODXylMxOMWa9l+LH6fZ6NGTKqMiSrm\r\nQjpJ+CMaH5UZD637Oxb4gktkVd6su4p5cTJ28neHvb/MqNfa8M2IkkI6l6ot\r\nF8sLr/QmykCnhCX17qZEnMnX2moWM8jTkb9+rfNt9aw3UK7azpQ17s1H7xRR\r\n/zGW+MEv+81asYhw/g1wXhqy9ISeAParRxOgOgxFYhl0OeOPFks9F47gLb2p\r\nZQuZXv7yfa6wLuL38oaamI4sBJB7dqldrdQedQV/QlJzVQ9itOcZd2W9U/3p\r\nMGX7CHqSa43keUDUD4kwLd1DAOSK7q2RAVy0cfwLiYc3FOgrhgqpK1n327nQ\r\nxLd0pRUjsRsQ0AAvxShHRy93mkNlImZnPefNo1fkXpoZinQzwrFTkqnDhXew\r\nnFHHx9RwSxuuHtv2oCCXPT+P5IhuKGVrgtA=\r\n=BVhM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "@floating-ui/react-dom", "version": "2.0.1", "dependencies": {"@floating-ui/dom": "^1.3.0"}, "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.28", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "@rollup/plugin-commonjs": "^21.0.1", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7972a4fc488a8c746cded3cfe603b6057c308a91", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.1.tgz", "fileCount": 17, "integrity": "sha512-rZtAmSht4Lry6gdhAJDrCp/6rKN7++JnL1/Anbr/DdeyYXQPxvg/ivrbYvJulbRf4vL8b212suwMM2lxbv+RQA==", "signatures": [{"sig": "MEUCICIADjOjX0nuAPn867POdETcrPppk9uIbCGcHrmHACoWAiEAp9p8BgJ79Uhi3tuKMLnWUsszl/8xcmUbsM1OwYGtQoU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40172}}, "2.0.2": {"name": "@floating-ui/react-dom", "version": "2.0.2", "dependencies": {"@floating-ui/dom": "^1.5.1"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "fab244d64db08e6bed7be4b5fcce65315ef44d20", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.2.tgz", "fileCount": 16, "integrity": "sha512-5qhlDvjaLmAst/rKb3VdlCinwTF4EYMiVxuuc/HVUjs46W0zgtbMmAZ1UTsDrRTxRmUEzl92mOtWbeeXL26lSQ==", "signatures": [{"sig": "MEUCIFwcNv0JJhXz9ypCh5PSO9L5x2qWm/aTa294xunBfnN1AiEAph2WUo8wYIKIath/gQZGkkOfD34/oQo5LYBu/BxUvZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39973}}, "2.0.3": {"name": "@floating-ui/react-dom", "version": "2.0.3", "dependencies": {"@floating-ui/dom": "^1.5.1"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "c2696dc16f56e443029d54a471dcaa1f84b6c351", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.3.tgz", "fileCount": 16, "integrity": "sha512-wOoKUw2P24/OXbNr3bbCqWgoltsyY7lFBDPVtjj/V4WDIJ5hja2C/r+CoWmS+Y75Ahndds3wa7eJRhnJxTCJaQ==", "signatures": [{"sig": "MEYCIQCsgUNkkquCN2PdRlKSPz0JRE/4maYnj//gG+qlwJdhUwIhALFjxPCJJzy8tqoU5oFJ5Y6K1osqiUN4nGDHFGsszuaM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39900}}, "2.0.4": {"name": "@floating-ui/react-dom", "version": "2.0.4", "dependencies": {"@floating-ui/dom": "^1.5.1"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@types/react": "^18.2.14", "@babel/preset-react": "^7.16.0", "@testing-library/react": "^13.2.0", "use-isomorphic-layout-effect": "^1.1.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "b076fafbdfeb881e1d86ae748b7ff95150e9f3ec", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.4.tgz", "fileCount": 16, "integrity": "sha512-CF8k2rgKeh/49UrnIBs4BdxPUV6vize/Db1d/YbCLyp9GiVZ0BEwf5AiDSxJRCr6yOkGqTFHtmrULxkEfYZ7dQ==", "signatures": [{"sig": "MEUCIDr6beKohRi95ZYiNoL6gbsHshpIDVkW6kqHKyz2GE7LAiEAzgCsi8FZ34PLXREH9V3wkaj5aGZEDtCsPjvSzIcVXBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39899}}, "2.0.5": {"name": "@floating-ui/react-dom", "version": "2.0.5", "dependencies": {"@floating-ui/dom": "^1.5.4"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "851522899c34e3e2be1e29f3294f150834936e28", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.5.tgz", "fileCount": 11, "integrity": "sha512-UsBK30Bg+s6+nsgblXtZmwHhgS2vmbuQK22qgt2pTQM6M3X6H1+cQcLXqgRY3ihVLcZJE6IvqDQozhsnIVqK/Q==", "signatures": [{"sig": "MEUCIQDcBEh4eb3JMBVOAsAyNW33oRasiS6MZbrycDVWlfee7wIgZju4m557C7bqeicbeX0p0ztEVgU0D58c7xK9CykJPWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57200}}, "2.0.6": {"name": "@floating-ui/react-dom", "version": "2.0.6", "dependencies": {"@floating-ui/dom": "^1.5.4"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "5ffcf40b6550817a973b54cdd443374f51ca7a5c", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.6.tgz", "fileCount": 9, "integrity": "sha512-IB8aCRFxr8nFkdYZgH+Otd9EVQPJoynxeFRGTB8voPoZMRWo8XjYuCRgpI1btvuKY69XMiLnW+ym7zoBHM90Rw==", "signatures": [{"sig": "MEQCIDE4YJStYrudf+hnfgdsnRe2PDN8SmtziVMkDFfMucxBAiBz58GadqaP76hongIxciSLept7lfGMC6eIV62eprNIqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47751}}, "2.0.7": {"name": "@floating-ui/react-dom", "version": "2.0.7", "dependencies": {"@floating-ui/dom": "^1.6.0"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "873e0a55a25d8ddbbccd159d6ab4a4b98eb05494", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.7.tgz", "fileCount": 9, "integrity": "sha512-B5GJxKUyPcGsvE1vua+Abvw0t6zVMyTbtG+Jk7BoI4hfc5Ahv50dstRIAn0nS0274kR9gnKwxIXyGA8EzBZJrA==", "signatures": [{"sig": "MEYCIQDanPYVQsPSIaq1Y8SmTOBsSGn9lHwcvI3DpgMxrdQ4vQIhAMMI4QkLFHG0+yXQ8hWYLeGKKQIjYG+/de0XHml+JC/Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47751}}, "2.0.8": {"name": "@floating-ui/react-dom", "version": "2.0.8", "dependencies": {"@floating-ui/dom": "^1.6.1"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "afc24f9756d1b433e1fe0d047c24bd4d9cefaa5d", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.8.tgz", "fileCount": 9, "integrity": "sha512-HOdqOt3R3OGeTKidaLvJKcgg75S6tibQ3Tif4eyd91QnIJWr0NLvoXFpJA/j8HqkFSL68GDca9AuyWEHlhyClw==", "signatures": [{"sig": "MEUCIAlUE7Rp+qWJQ9IYpKe6Hy38Xxclq6eW3Z0anZDbR+JCAiEAy5DNCd7JweXQGkL7OpzV0J8WvMj6ge8A7qq6YXsLSOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47751}}, "2.0.9": {"name": "@floating-ui/react-dom", "version": "2.0.9", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "264ba8b061000baa132b5910f0427a6acf7ad7ce", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.0.9.tgz", "fileCount": 9, "integrity": "sha512-q0umO0+LQK4+p6aGyvzASqKbKOJcAHJ7ycE9CuUvfx3s9zTHWmGJTPOIlM/hmSBfUfg/XfY5YhLBLR/LHwShQQ==", "signatures": [{"sig": "MEYCIQCda4rdCsWZtVApjGukVqFbLZIb0UMSr+iaNMkRdy7fSwIhAK9Dnc8lEcp8yiJ5lp3OSYYcbipFUCYnukHtENRMObxD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47231}}, "2.1.0": {"name": "@floating-ui/react-dom", "version": "2.1.0", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4f0e5e9920137874b2405f7d6c862873baf4beff", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.0.tgz", "fileCount": 9, "integrity": "sha512-lNzj5EQmEKn5FFKc04+zasr09h/uX8RtJRNj5gUXsSQIXHVWTVh+hVAg1vOMCexkX8EgvemMvIFpQfkosnVNyA==", "signatures": [{"sig": "MEQCIDCvPBvNIY54XlPgUqzFtbloxtIDoWwK+kHsfGt+2eHzAiAPmzktKUOmu54ZeHt6aPxWXDCQjfxVo7rjkxcjOsAZgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59969}}, "2.1.1": {"name": "@floating-ui/react-dom", "version": "2.1.1", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "devDependencies": {"react": "^18.2.0", "config": "0.0.0", "react-dom": "^18.2.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@babel/preset-react": "^7.23.3", "@vitejs/plugin-react": "^4.2.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.6", "use-isomorphic-layout-effect": "^1.1.2"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "cca58b6b04fc92b4c39288252e285e0422291fb0", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.1.tgz", "fileCount": 9, "integrity": "sha512-4h84MJt3CHrtG18mGsXuLCHMrug49d7DFkU0RMIyshRveBeyV2hmV/pDaF2Uxtu8kgq5r46llp5E5FQiR0K2Yg==", "signatures": [{"sig": "MEUCIQDbyUZmKooQXB/yKiMlzDF2BhHabGN6U57E+ZEYV2ztaAIgAQs+SuMlPeCuJEwOiZHXf5ilDIsHfHV4jCVrzGnZgSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60336}}, "2.1.2": {"name": "@floating-ui/react-dom", "version": "2.1.2", "dependencies": {"@floating-ui/dom": "^1.0.0"}, "devDependencies": {"@babel/preset-react": "^7.23.3", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react": "^4.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "use-isomorphic-layout-effect": "^1.1.2", "config": "0.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"integrity": "sha512-06okr5cgPzMNBy+Ycse2A6udMi4bqwW/zgBF/rwjcNqWkyr82Mcg8b0vjX8OJpZFy/FKjJmw6wV7t44kK6kW7A==", "shasum": "a1349bbf6a0e5cb5ded55d023766f20a4d439a31", "tarball": "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.2.tgz", "fileCount": 9, "unpackedSize": 61480, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIU3+nr7MuWZ2lFFtDMCGHzC0TTfqqTLR9wD7L4YbvEgIgH7ZNmKRCwNYpRlIHHpCl+LCpvJqvaC4c1yhgdnGqEq0="}]}}}, "modified": "2024-09-15T06:07:46.127Z", "cachedAt": 1747660590773}