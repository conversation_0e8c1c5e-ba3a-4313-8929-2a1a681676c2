{"name": "fast-equals", "dist-tags": {"latest": "5.2.2", "next": "5.2.2-beta.0"}, "versions": {"1.0.0": {"name": "fast-equals", "version": "1.0.0", "devDependencies": {"ava": "^0.22.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.1", "eslint": "^4.9.0", "lodash": "^4.17.4", "webpack": "^3.7.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "d8c8fafb16c0c1eaa41f8ef39bdaefb28c564345", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.0.tgz", "integrity": "sha512-v3s7bQTYWP1rXT2PPNKNNXhn4FECm6hCkpt+oSgcxe2uFi6ckJ3VhqA31zDu4BzG84tGtRLylrd2FpmooINy1w==", "signatures": [{"sig": "MEUCIQCOH85HActCaZEdOjs5hD6UNpVjW2aUELO/ToRUqwe7VgIgUJfC39ecw4r7zfaGcKrIyokQviFleZDCFwy0h9Sucr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "fast-equals", "version": "1.0.1", "devDependencies": {"ava": "^0.22.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.1", "eslint": "^4.9.0", "lodash": "^4.17.4", "webpack": "^3.7.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "157645b2011f96be544735f5bbb06ec0fb898f19", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.1.tgz", "integrity": "sha512-DQ9BIjfc2za8AID0jnf3PzduqBsLSmiVl5KjW3g2PnMGm5ggOgaNQOf8OkbS6V9hpcbeIHJ3mr9ZMdkyxniP+A==", "signatures": [{"sig": "MEUCIQDdXyweioyKDJPzyDMllJOucCl8vCWdZp5nbWeANgoTUgIgRYZwIII1Msz/WwLmAoCnjdcgvMYBI6ZNtRQuXaOqr9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "fast-equals", "version": "1.0.2", "devDependencies": {"ava": "^0.22.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.1", "eslint": "^4.9.0", "lodash": "^4.17.4", "webpack": "^3.7.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "c806d3f3dee03ea5ea2d73dea035ee47a1c5fcfe", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.2.tgz", "integrity": "sha512-dr34NNtqHVbAs+zGfSVNbGL92BT8AX63tmnEspcZOLj4aQeOlyVfHD7mFaUVtLlv9Yuy5gSqjkDoGN5Ip/XUTw==", "signatures": [{"sig": "MEUCIFlauDBkhCqCPTdEfacnAbIpc9Jc+B3oAwI3vMjcARj0AiEA7QD9JDIb0zfKdFVZF3uG3nRIlshT262B2BFaa9rdqvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.3": {"name": "fast-equals", "version": "1.0.3", "devDependencies": {"ava": "^0.22.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.1", "eslint": "^4.9.0", "lodash": "^4.17.4", "webpack": "^3.7.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "5d9f134262d2ad21b402d56e7b30fc1b51e2134b", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.3.tgz", "integrity": "sha512-Gu4hy1BZAbKjo7SDLZ1FJZSbvZjWvtJketZfQZVa5fZHOMtN1mxgn9qG1PS8pP3dpauEfwfNrREvXJgJlSl44A==", "signatures": [{"sig": "MEYCIQD0ZvwSbOPsLFgAk3dzY3RNTGWjQ+t+n+NF3cCFF+QtCAIhAI88ZgDOH/4FNgIdWMbrdZjIeHYhEmIV42oBPhkKOCVE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.4": {"name": "fast-equals", "version": "1.0.4", "devDependencies": {"ava": "^0.22.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.1", "eslint": "^4.9.0", "lodash": "^4.17.4", "webpack": "^3.7.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "edf07fd3f5111357570c4b94ddb33c2b20bde425", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.4.tgz", "integrity": "sha512-LJVoDtJ8ji+H4nBU+eMzyMdw4BWhTTdDxxvM9wzxaRa80TnCAdw8xi+Ob8U8dQqRNdUDcjbMSABizXCNtWQ9xg==", "signatures": [{"sig": "MEUCIGoYibULhsCxdoTxsTQ+/bNpM8Q5PGLNJhgnNFTtfNZIAiEAygmJOGqDMZGPJwTeiW24WBDX1a04Xjl1k7kxxnZ87S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.5": {"name": "fast-equals", "version": "1.0.5", "devDependencies": {"ava": "^0.23.0", "nyc": "^11.2.1", "react": "^16.0.0", "sinon": "^4.0.2", "eslint": "^4.10.0", "lodash": "^4.17.4", "webpack": "^3.8.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.1", "underscore": "^1.8.3", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.3", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "1c4f9486625423130f0eee10dd1d5eb3e602e249", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.5.tgz", "integrity": "sha512-jQz06IW8+uxEXf9OM49clh0w0vHILM5P2ngdQ95a7yuPrvAFTLeJAdffpXMmzCNDQXUdGKUFPMbtu74uSfxryw==", "signatures": [{"sig": "MEUCIQDtNR2NE7Ht4qHbS/syfbkh/Lyh5+15A1enFteqM4h1NgIgb8JdkkaFsZUA/LdtAFMemOmf5+Wwe4pRGxR42Hnm19g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.6": {"name": "fast-equals", "version": "1.0.6", "devDependencies": {"ava": "^0.24.0", "nyc": "^11.4.1", "react": "^16.2.0", "sinon": "^4.1.3", "eslint": "^4.14.0", "lodash": "^4.17.4", "webpack": "^3.10.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.8.3", "babel-eslint": "^8.1.2", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.9.7", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "72a4ee4b44859055a8680bea4d2354aaedbd3fba", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.0.6.tgz", "integrity": "sha512-Dd48a++qbmLNbWhw6+9sBPFpu+QRU/FLvANXmmcwJfOc6Fo+zKe76ndNkJ1/e4lBfbQQ+pQsD7CdsTyk2UCLzw==", "signatures": [{"sig": "MEYCIQCF9CPGWEB9bypVjF4N0sAkawAWm8sPpUqc3dTbvG6gpQIhAKDOJS5YzlWhKlRlKXc/vVdTJn2mKSIFrdkpfLjApdth", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "fast-equals", "version": "1.1.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.4.1", "react": "^16.2.0", "sinon": "^4.3.0", "eslint": "^4.18.1", "lodash": "^4.17.5", "webpack": "^3.11.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.8.3", "babel-eslint": "^8.2.2", "babel-loader": "^7.1.2", "decircularize": "^1.0.0", "eslint-loader": "^1.9.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.0.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.11.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "10eb07e48dbbacb74214ef6856f2bbea69f3114f", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.1.0.tgz", "fileCount": 14, "integrity": "sha512-hwGpzpGmLTPLQTAH5Iq2lHDUwAvcgRgSwyfNn48fuNpBDsTkcFdTS5iVpVet1xquR8M2ZYoPVn1J+t7ca0y79g==", "signatures": [{"sig": "MEQCICw4qn7iVIftudri4uuRGfen+tuoxa5eQW35fWJQp5MsAiBiOahBLOtA5pMhWVAZGy01gbCXfC0WGkel9VqDY9lKAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47115}}, "1.2.0": {"name": "fast-equals", "version": "1.2.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.4.1", "react": "^16.2.0", "sinon": "^4.4.2", "eslint": "^4.18.2", "lodash": "^4.17.5", "webpack": "^3.11.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.8.3", "babel-eslint": "^8.2.2", "babel-loader": "^7.1.3", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.1.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.11.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "c62cd964da68cc5b840cf8a0cd386565d4f5b1e6", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.2.0.tgz", "fileCount": 14, "integrity": "sha512-e/hvHyVYaWiWMFRd5ZvYkseam5YLZjkkWuSFdnVxnD6Xc+V3H9aXwxvqhJvmmhMBWK8AAOs6OHhHVqr9aZCWiw==", "signatures": [{"sig": "MEYCIQC465x/uVLD0Yv+slQc84ZZ9RfMqdCtbBOljZqwwr0CYAIhAMNL5mwoF987jbwSVn+sUeE01E9L83J3AEO6aFQnSGeR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48901}}, "1.2.1": {"name": "fast-equals", "version": "1.2.1", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.4.1", "react": "^16.2.0", "sinon": "^4.4.2", "eslint": "^4.18.2", "lodash": "^4.17.5", "webpack": "^3.11.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.8.3", "babel-eslint": "^8.2.2", "babel-loader": "^7.1.3", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.1.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "webpack-dev-server": "^2.11.1", "html-webpack-plugin": "^2.30.1", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "eslint-friendly-formatter": "^3.0.0"}, "dist": {"shasum": "aa1926b828213df4b528bfd9e21b17ba1c7d34cb", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.2.1.tgz", "fileCount": 14, "integrity": "sha512-7Rgsev90v//xCp60nlc1zjx6POzONKkCgAnId9qCUFONQ8r/Y/M2QQvAmy7mQyHBOHgp5t/lQvvJzKpM4YQvxA==", "signatures": [{"sig": "MEUCIHCZustFHLr54wwwY0NwJYabnlk94CSD81EovZ6hz92bAiEAxLaPrHRYXaO3fqeJZ4esO1ZPPSowuxIyKaJ6Q50+5xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49122}}, "1.3.0": {"name": "fast-equals", "version": "1.3.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.1", "react": "^16.3.2", "sinon": "^4.5.0", "eslint": "^4.19.1", "lodash": "^4.17.5", "rollup": "^0.58.1", "webpack": "^4.6.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.0.14", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.1.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.3", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.3", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^3.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "4063f380a3caae12280f2c247626179e66c9f0b1", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.3.0.tgz", "fileCount": 16, "integrity": "sha512-M3DD/J5+ochIfknGZZujdCD7hUL8jy9T0x3UgZZ6Vm86fLlrWYQGgt3bFuaVmbEfH/IPiXHJOnc0scQX+Xqajg==", "signatures": [{"sig": "MEYCIQC8avF2c4CYZjgHO2xLJFY6ZCWjo5bgrVtEKs9FKkES6AIhAMGeGzCUc/F0EsIEY/TUtr6cxiM/FP0tRqn4BkKdyul0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 346732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2EjDCRA9TVsSAnZWagAAihEP/ivHoZ0icl3bBiyYVRGp\nzEoBH7YBqpiw/0wVD/NHlmYF8F0r36inx8v7L41Q6a0Bf2jDuD/GW1yHJFop\npoLtcxU0jFRWaJtO10tqJLjcfMevuYJ3HYAzSfn0pK0XpmOBJWVf+430xWFd\nBHJshBgcbyBmXwO+N1VOiq5l9OLgjnBSfOSy/4f1mDN47OBQh3lKJFth33eY\n7j3Aizpo6XcL4QM3GGUU+kO6rII5rQ4+FNenTVN9cpKG1rhWfauNexNo3gfz\nWTwSqmvFLkdTB3Vxksdi1JDpyBQCMrtb5OVDWUW5NtVlrXNsyjn7oPBPSaSK\n+nTnB1reGXIRZszCH6aoOBypOD1Be8iK847hBguKQqvSl1aPGzind69EDHhp\nOFi96ukJ9nRF1sH5wO5Tw0lAufNiV8S+hKOSccbxXbEGy2f2Yq6J6OdiMjBb\nqskCc+1Kqtp05/3RZ7oDpSiqyxTtCYwPhmohg3gooDV+wmV8M7JDHdtijarc\n1YoAqnQ/Va+Oyt+mG16MR78uVHbm1B0owsVRS65WlcgsDpaJH4K1P6hqQYXn\njggt8EFRScxg85RkNOxWU2eh7AyTqKII8wPriTK2OpKP1uJRjyH/MSl2DEYY\n0gCwE+SSGmP6Ksjl8o6bp213TKDDyOE5HnWWRZqWojHGSoo5bvGdvM+cU4lc\nyUlS\r\n=DIC1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "fast-equals", "version": "1.3.1", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.1", "react": "^16.3.2", "sinon": "^4.5.0", "eslint": "^4.19.1", "lodash": "^4.17.5", "rollup": "^0.58.1", "webpack": "^4.6.0", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.0.14", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^1.1.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.3", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.3", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^3.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "e4f2f35beac5b22b900873d128926168db0a5e91", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.3.1.tgz", "fileCount": 16, "integrity": "sha512-KMf0osBsf6bVbMnEB68l5+g/d4E7uEzOrM8hlDoZ3RrpXouZm+uC4eBXHt2CazZsWHGHhpchWrXDPipkqji7VQ==", "signatures": [{"sig": "MEQCIGg+tEQH9VlsdFDVx0iD1ez4pTJtm9evWNVZz/MozBZqAiAfxclnSsXCasihJ4wQIYKh/wiZ2pEttuTmayESX+d4Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2KXdCRA9TVsSAnZWagAAh18P+wXYB2bcif8BZxr39qEB\nFBJS/okYu5Nokso1JkqZsQeYcgLTI+LnwGnMdy6qMgJwm1bBlWK9Ugk9ylBN\npOQUdYFMxlarSstEHCNWR8GOKYpx/wRZb4uMKW45wyGx0d+BShz+oxduF/8X\nBAw0XLoaKHJRFiIr6lFlKHCnGpgUWrrR9tt7idMxj4XT/uTpLstOLaJPXPIn\nu+wJPnFQ/NYIf+eFabFTHPL3dpSm+rfazN+bjXcxlgZ3h0fok2B4v5SVC1pw\nsAIr6E067ByCl90H4j0PsK53MzM5qCjTuNSJaZ1PwQn3rxzyIQi/vX8jn1T/\nm4vO9NljQF/yGDayIcDGCmYtviI2e9r4/eBQkjR1qDdm32Cc9gSJ2Ajm/BIB\ncQV2kW/7KDy5t8PPiBYfdCQ4n2uBdKKFEx+ve90yz8Ib5/tNSuvmW4z8uYLm\na7pW00YBdlFurTIsQ61MWghi6Nqxk2vj0qwGpiRgEBkA5pQQtGwNJk5u0VQ8\nocYrIEm+hGQ5j7IDYZswLf4UA6bqe1wMXlQNEoazz6824dh9np2ZkEhaFPpP\nX6PhjAjIhU2uVbtjTf3oms9gyYwAVGGiBynIwGZdeWNpGg86dGRg0QIRWLZt\nCO3tZJXpUzesIfm6r6Oq1hDWK2Y4QiQyegZ2rmkOi7dXpXdtimX/8wQNL03N\n5VLd\r\n=uEPd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "fast-equals", "version": "1.4.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.58.2", "webpack": "^4.8.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^3.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "7f76216f615c28c22eda148c39d53aa14bef5456", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.4.0.tgz", "fileCount": 16, "integrity": "sha512-W6YFAPVB1G4ASjNsP3Qm0njkoePeiVaDDo8/+CplWthSi63UknoJpNmwAeP/qPVFgBwOZDiB91EHK6MN65REgQ==", "signatures": [{"sig": "MEQCIGSvgfXX2eSdSLqNBmbFcEJ+B++USnwh1dNuKsEtNI0DAiB6tPTzJaWqF+PcBfB0xh0o6YOHfpHNjrssy99778YbCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9CWbCRA9TVsSAnZWagAAWB4P/RI74xPamlSwPZRZXDbD\nvP/Y+P/YAFSspsf9hoHwyCP7AiXvHXlkfGqXXl+p4dJ4nHeNevzw/MZkhKeT\noFKeLLas5u/gHQEaZC1trx5yNYAfxZXM8VB+0c+ZbQ3OT6OTmRPOvxfTlGcp\nx3ox83q2Dslv7qTZsyEBheleWTKiP557VrGE3PBPTPjwiip862lQmHkQd/px\nwBu5JaZDJDlL56OWJxXAWds4VEPX3JdtvxqZFWXmzvylaO2wBRHukfE5J27M\naWvtL5Bhh6dF6L96BwWDHB5zjUNA9SPAwR9mvbUBMCHfK15YNdRtvQrH76VE\na6MilUvdaS/mEBE6/p+uL68n+LkiFgkeWt6y5vyQlsDCO6s+l4y2LhiUIQob\nO7z6TZkwna3fE5hI1suNPSVZ+N8r/NB3E4VrPpMIk/hj6AsJIQNJV2wsDmA2\nJhevbhAiZOvQ/4FXWIZwf9ARW9yPC9IstxcFo5OiEEdoB2ojrEGmFgibpzoa\nd+K/rpmA8Vf3+KC5ob28lMh6JrLIAA8x5FYfeWB3GFlj9BvXAEK8EOb5SV7W\ni+zvqpNbAz5QtEwUQSM1P+NQ1Q+Elmg3P1ySAy7+/xvsDlcBEmvlX/9TuGD5\nEkN7dKBA0CEq0zwpx1HHT5G5GejR+YG/EGw+ZyHx4T1txACHBlnVsuNmrtn5\n9oKZ\r\n=YOeh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "fast-equals", "version": "1.4.1", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.58.2", "webpack": "^4.8.1", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^3.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "d2e4a88b22b731d4c0d86bbbf4770b3eea24f036", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.4.1.tgz", "fileCount": 16, "integrity": "sha512-IsQ5l5eD/1HOZ8SCPdYsrxJB9iayuF9YZk+ts7f9WgF0lUWcuMz0S50v6QGvFxn3f/ZG1yojT7pN7ASY2mSQ6g==", "signatures": [{"sig": "MEQCIBy6RmmtlZqVmoWoYxpmOhlGyeCl2Tr6L8ANNabqh1oMAiAaO474pxfdCaktwxgDDDULQF4hm/wmpm8nEvwhRre+dA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9Wl6CRA9TVsSAnZWagAA6u8QAIaDkI5lpCpgSc1hXqMh\nO9WdBEJBpYQTU0Mur2Cg8beJEIVps1FmQAhsJ6rpR7/MLvaty5sffbpc4Mga\nFLlLaWeKlykpvsj4MHXBowtMH2B9RWWoeVS/pUxQqKltNU2lmtYFJE883psU\n36PAKHZfb5vsRoseJp5RgGkIACohL3n3pheQaonD8RUbF6uyw3mlXu15Jc7f\nRMNfVJg9UUvZusW4c4VpsO85F7haKqCX8Kq3LRDtf7QXA27ATUsU/h8yP5yY\nWgfF/8pQATYvhETiKCnaG98QEGShzPOviGjVlzq6S4Gg+lgQ1shCZSMxbWnF\nDf5JCLZ651+bZUnWZ/JmBwvrpV4+7E5hHCvoyCNF/Hzwg2rjjXq4k7q0HYAO\n2hMIMfdQwVSBrbKNiP146VhoH+Nushdqin+luwajq/V67JgsRblEksvxEvyo\n8PATdYzQJrbGUcxiwsnoQf+mD/snrutZZA6cHNx8CgeeR7ZnpVBQu6XYEBDr\nx1oGPE6CTS2mqR3KikYRaCckddzUXIokC9yVzQusMFQZP2mrNpeASxQRxuwa\n5AtxnCIElwz6FkTUvXKvOLuE6nLcqN0x3apVlAIG5m0EBiFGK+A4HJhxyHLR\nkpJbvSJuTKS9zngcornmayon35IFbBHSeNck2zkTCOfEbziSK+RKUc9kgSQ3\nTK9X\r\n=1Ptp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "fast-equals", "version": "1.5.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.7.3", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.58.2", "webpack": "^4.8.3", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^3.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "f86c0f9526d4090195d35e236a0bd67552bbce19", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.5.0.tgz", "fileCount": 18, "integrity": "sha512-DRSuVPUR3z6/v+uP5WdjFX2Gr6x2MbgF/GIqd7BqycQyLcCW1WFgcdPz70RZsQhcyPYS5yzgbkK4lZUgeGysyw==", "signatures": [{"sig": "MEUCIQC7J7QfJM4Rny33TLl7aWKD65N9lJKjcdRmd4x3tdQCwwIgB+SYrbOqMmetSLX+p0wlnJNfAwrgPeHYrqN4jOq1M1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+E64CRA9TVsSAnZWagAAgVEP/3jXuLi5vvgrFhjiOdQr\nKn+g8iCCbfHQ27byU+v8apVCmxcDhTwu/Nh7yzfPp0ypL2UVZ4YL+OWtvQPJ\nWWy8uPUDuere21n4/Ztl/mb64FVmbcqoPjBD2lM5uMmoK3goN7n+FpWOEska\n40o/+OjcYrdGH9SqBP3YtsO1adkg+Oh7G7JgXrfrvty3miB0TwGfIfj7M0pU\njBmmYuKyKvq6MNxOszm3CJZPM5pzYwpOl4YCfKkWe3YCIfSwRakxxK2EUM24\nTYFsEvnt5AF4o2hzpdgVvvLjQjl15s3V9fx0/J5Qm6R8pzQD6mp518JjAHAQ\nn00IQFGsyUBd2LPkDtDFGBM1sGMtjINwpx4vz8pOuxyuDZaY/mS9XJRMnebn\nY5RoOVyDFd384B6GIsWmJ1U70dWf2+Ut2VmRO/lb9pmMyCM3PON8KjjMcwqO\nd4/uVoShsL67R3+nzbIv0xoNQp/abu64lmIs2HdVE12ll/WRCO/Up9E1IyKT\nPoqXiWFsv5VfWPLNW+oCGCIEzcVFI9vEKIcON0Ry8cx53OLPFJJnkXfMXlzM\nOEChCqvERlP+fMhOObU4oM4XinB8O+PRPcbh/t1S+4OVuBHztVhjQTtkDQB9\ngLIQRmhHsCjqedhrd/j8cAQHXKQL2CJhDRgKksfbEFXWQfIcPNn2CROv0ATg\nsxom\r\n=N0fC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "fast-equals", "version": "1.5.1", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.8.0", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.59.1", "webpack": "^4.8.3", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^4.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "1768dfb9770e2790a778c51a5e7c3c64bd4f6cba", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.5.1.tgz", "fileCount": 18, "integrity": "sha512-Cahcy9dLM0BNb8WlkTUZ9uj0wnvuubyqEuPLa27cIZsGybYqIQhxPaoq0nhJRCbNj+C/V/pdtXGWuqgncXulyw==", "signatures": [{"sig": "MEYCIQC5LLn+zeMgVjI6ZlaRy31kHtRsXqx4qBdgaFxx4KbtbAIhAOgAHDc5AHKvIoUrKrZM42wMCI/QtHvf0kpoSyNzMnZ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAAKLCRA9TVsSAnZWagAAmDMP/0/kJzjEGyWXLtNhi5J5\nE7venauEOaUZL1dvulebJaoa0T2YRH989dorl5T0ofnu940NxAnwrkgiPsoj\nxtf7bh53XqQ9SXBqkiK014jrzjqZsr4OxZcdO/F5cAYgG/hQ0oXS504LYbLr\nyyoY8/SgmvgkKn/VCtvGqFtnSxLSiMa+IWVIRaJMsxS8Fz/olEFvsMeXOHnS\nHSgIDUAGBC+EDQxhTY4z8Dp0lnZGvms3w2GUeBXqedWF2F2WzWVfI2HfxWd0\nNGx4Zi23Jzt79lQr0Mmx8gmTacGyYT6SrpcN82xmN4Ikzy29MvL6m3reOtcJ\nhlp/miilaXKPOzAmYh76+1ef/I7aOslyROquNgfGrZy8OH0thzdC1tdmA84f\nQqwewNSCxD3RZ8bkTdzaOdN38KyoCyNRJCBsm44RVXFgITMoRsXClWsyY5KI\nQ2Yr9MQFFBLoiw3QFtN+fmTsCKynsOlbjpokxew7z9BveHQVUIjeBe7l6QtC\nB5ohuiy/S2OlDiYhrwzPs7MZUovQWalWykI2OXLBVkpb2PlRCMzuLpauslgw\nXTz8nwHmTG8wwru6MIz6B9IBQb+T64Jt41xfMJKI9L7S8kQ0a7S5UKWkxhbx\nqGI2Ayp+pVAQwRnaSgq2lkYyHscqscGm1IPwsAomJkCYOuTWlqdD9J6tqwbe\n92T9\r\n=f5BI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "fast-equals", "version": "1.5.2", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.8.0", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.59.1", "webpack": "^4.8.3", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^4.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "e46681481d53c3b38046be65299c2d2ed37c7f29", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.5.2.tgz", "fileCount": 18, "integrity": "sha512-qhYv5A56EjUywRm/9SF/yetuelZPIEs4wxaB83rdJpFlKP19Ur5/dyEqynWNcf7rns0RQrbJB6Ws29w1g2kmkA==", "signatures": [{"sig": "MEUCIQDRHF025pd4zxu4VdM9hqxKdIDhazdTCzvap5lOuuiBQAIgYFEnpoJV5T3AN4p/TnSoEAimmFZ7UPafG7IrqCmolKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 376896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbABzfCRA9TVsSAnZWagAAdaUP/1YMGCXV/wRYvZXmXGqD\nigUr16yX1sG7+zXyV96ihcvsMpWXHa1g37JNuM4d4V07MgjAGdxZBoHgObLR\nYT34h8ARqNV3OfnCWs8g6BZRXRB3H78qEKEqCAFwdvt8WiCafVG8wdGK0vj5\n0yhp0pKJycKV3W6FcHx4Hu/9SWBJ1g5d67iohrmFwGg4YpoGGWe6FWnjCJdK\ncxlBEVl+SBfe9nyqEdueOfd4K6tCFMD6GELBeM/LEEiguXcGyQsVCEPRjnBO\n2o1Dqm4VMjwss91FxIuABBTLQMhKzKg4LC2M/RUJMS313papFWF8KX3IbIIE\nUJvKy9ZyrBe7ITsmtizANOJDJH5/kJoNkwc0IBYuKmfw3cK/0GqtvRpd2XRu\n5tAJUY/daeJO29+KZM2av64f4yCMDS/2xvjSm1ZPMHU8ZEpUjKDa01IMuNIi\nLESRhImMZFtZ7DlXqO3wSom2mLzW3PaCS/poDPFoRv0vn+lhXeI4ca2QK5Gm\nZgJ4KEeS6n77r4C2X6gCDjrRj4JSgjXjSIPwncAb61NSWxMScDqMNZ4m9ldb\nfKHUsev8y3EXGqaRlE/QsdVgUtxKBWi28EbvtkRiaZX30hU0XtBWPcU7OkbS\nVq0Mlkd+0VaZo6IsNH/mRWe/+ebeyz/ohS6NiA48zNP6MtJmL5GtrPZjFqfs\nnCXJ\r\n=VW17\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.3": {"name": "fast-equals", "version": "1.5.3", "devDependencies": {"ava": "^0.25.0", "nyc": "^11.8.0", "react": "^16.3.2", "sinon": "^5.0.7", "eslint": "^4.19.1", "lodash": "^4.17.10", "rollup": "^0.59.1", "webpack": "^4.8.3", "deep-eql": "^3.0.1", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.3.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.0", "webpack-cli": "^2.1.3", "babel-eslint": "^8.2.3", "babel-loader": "^7.1.4", "decircularize": "^1.0.0", "eslint-loader": "^2.0.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^1.0.0", "webpack-dev-server": "^3.1.4", "babel-preset-minify": "^0.4.3", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.4", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^2.11.1", "rollup-plugin-uglify": "^4.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "22087aee55a87d440b881445f19424a2f4f4282e", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.5.3.tgz", "fileCount": 18, "integrity": "sha512-+i0j+YqyNe4EuzB1z9N4J3qBtq/ZiV6IjlOChzuxPEWOx2ysFMUNxhQ/Z/eFBv4mmM0wKhFsZ7WS+udcOHO0lw==", "signatures": [{"sig": "MEUCIQChKxajd1GdBW2vNuv+URON5k8NSsXLLoPez+7Kq7nnCgIgEAQXwVGwNfHRXBBJ7jKXvDzX36ZePny5pl+9zhsjbrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 370381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCqlyCRA9TVsSAnZWagAAVI4P/RN+ufF9nbwjutottSPV\n0MU5vr80ecym9O4Jz7DE3+MoXfzGhzX2rJvbMmCykglPY4gc3s7TnVo57miY\nmQOF6leuf0nhhU50mMCrQ4euJf2Ezt9EjtDViFEgT0cGrIFv3lN1Pj2suyNy\nycz6MFfP/UjasWeAVHdkwM7fpqoRP19ixPsmDmsTQHLOlDR6kgi3rZ37iErJ\nSThSrFHi/spTU5wfBuvAD+a4Tr3ZHPUlRmdHwF/0PJ96cKrU/exQfEe7XL7b\nQFcP0EbbZWXqzJU96LRHKIax1k3z1dPPwb+TVNj/AgVrocaRYr5y4hnIBSwf\nZuJmfp3aKtmSRRKXeiLU/JFHsR5mgoi8F3Ih+77Iief07dOYXBmYcLOnx5QN\nyTJ9EnHpDO+VkKQAxMRKyDjQ7Y9uK04zoH6MptENyl6DHQuhvL/zTLiF9mF7\nwMKi2wuulNNw7eaIKZNFp5d8xNVRvfy7wybYZTTjydlZqwK6YW8igCSh8HJX\nRe3YABN5Andon3vVo7efj9v4LLSVJErZkXEsiEDf0o6bj+AbzHWpmEj03p2w\njKmQM6vqTamQSlhx7pLTcPUit3KXEwk412zbA89dU2HIT60qXU+nL3k6Dk0q\nGZ4u+inVpC07oF4ZsNJysTRQqG55OQLVzyb2gEqGBi3yS5vlSq+CE1uXKBKC\n9auT\r\n=ZRiE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "fast-equals", "version": "1.6.0", "devDependencies": {"ava": "^0.25.0", "nyc": "^12.0.2", "react": "^16.4.2", "sinon": "^6.1.5", "eslint": "^5.3.0", "lodash": "^4.17.10", "rollup": "^0.64.1", "webpack": "^4.16.5", "deep-eql": "^3.0.1", "fs-extra": "^7.0.0", "babel-cli": "^6.26.0", "benchmark": "^2.1.4", "react-dom": "^16.4.2", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.1", "webpack-cli": "^3.1.0", "babel-eslint": "^8.2.6", "babel-loader": "^7.1.5", "decircularize": "^1.0.0", "eslint-loader": "^2.1.0", "babel-register": "^6.26.0", "fast-deep-equal": "^2.0.1", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.1", "webpack-dev-server": "^3.1.5", "babel-preset-minify": "^0.4.3", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^3.0.7", "shallow-equal-fuzzy": "^0.0.2", "babel-preset-stage-2": "^6.24.1", "eslint-config-rapid7": "^3.0.4", "rollup-plugin-uglify": "^4.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.3.0"}, "dist": {"shasum": "171c5dbcae34761b337d839105a11c5e91a4fe3b", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.6.0.tgz", "fileCount": 23, "integrity": "sha512-6GjIQ6KR7Q/TzDJpXv2/AEUULEQoP1LQSgLE23DysxFmZUtNDVAoqJzqgwieJdGrZ0aptKoaansRNpssRzqiEQ==", "signatures": [{"sig": "MEYCIQCQzPT7V03q7LL/r3hVnlcFayfS457NKYd7MPJN/L7xmQIhAIGZN86ajXIMTNVYuNW3W3h1ufeCRJNHpndDBEDMy41k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbMQwCRA9TVsSAnZWagAA88IQAIDaYJYvksQsJViDSAIG\nLNHoLj3vLGKFKXmXEBXCeFleXU89RavnsnNiQfpasqUAqvDjmn49p37sJLd0\nGa8Na/GdUpLid0G1/ld/eywEk8wrqyhcpwXNnusbtdJ3BCSDxpVoZaQO/HIs\nL064OqMH/FfiVAs0R30XHwjMV0n8eCWf8vzXA9QvVSdPsvv0q4kyNuZKEnl6\neXqS8ZxuDrBkr/adYkHOdpr4gT2L5KwjBgKO2B3iV6+8dmITAfi3BY3xLZ2b\nY2/Y1E0unNc6ZIwMECl6cFvsT+hUJKAm/E/DNkXC6ct2Lo3T+2vCEB5lIa10\nz9FhKSKShwBsvyk/Y8p80yZfG8ewdM2H9OdOarymPJlTyA9pg2Bqi60QEI41\nLCEE470QHRp0p0VNTrDQ4KEXuPR57gkClg/ArlN2SyAHBh5ZSKqSSrwZre5G\nuxfiIOC8IEcHw9GBTDhqSOeZTGvY5SBWaa7rHmYKhXR6ml7+XQUQ0QSWchJg\nHh7MKviwb3dwSZX1dwdYMS8QiF3iPgta8/ytl1UTNgXkyL4fTHIt4JDS9gA9\ncY6HyKkWfqnX19NBA/2vu16N2GUPPhVQ5vJGVykD1WqDKjEGjFT3pIWvHd3h\n3YcGZWzTXZQNOLGdA3rKRn4g8rmjqEcPn5PmlT2kK1aMFqv5zh6Op6IGHFY/\n+/xa\r\n=KRVJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "fast-equals", "version": "1.6.1", "devDependencies": {"ava": "^1.0.0-beta.8", "nyc": "^13.0.1", "react": "^16.5.2", "sinon": "^6.3.4", "eslint": "^5.6.0", "lodash": "^4.17.11", "rollup": "^0.66.0", "webpack": "^4.19.1", "deep-eql": "^3.0.1", "fs-extra": "^7.0.0", "benchmark": "^2.1.4", "react-dom": "^16.5.2", "@babel/cli": "^7.0.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.1", "@babel/core": "^7.0.0", "webpack-cli": "^3.1.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.0", "decircularize": "^1.0.0", "eslint-loader": "^2.1.0", "@babel/register": "^7.0.0", "fast-deep-equal": "^2.0.1", "@babel/preset-env": "^7.0.0", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.2", "webpack-dev-server": "^3.1.8", "@babel/preset-react": "^7.0.0", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^4.0.1", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-rapid7": "^3.1.0", "rollup-plugin-uglify": "^6.0.0", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^3.4.0"}, "dist": {"shasum": "66cc5a0922ea747599f41aedf44a76ec2908adc0", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.6.1.tgz", "fileCount": 23, "integrity": "sha512-xlmDRa03F8QpqTpfeH1l/exTKMg5zhyDh34bfc3pBR/JLQkaIzayXC7hvsXsyv/Och1At//+ubJp+Jy1nfmMvA==", "signatures": [{"sig": "MEUCIQCCAotVKJEsuVMdLpo9Mq2PAtygKq9hYxeLSEXn2YNCrAIgYJ1tph5R5tiS9R6l/q/4QsgGBgQPzATZL3jZVxk97NE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 394887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbobhPCRA9TVsSAnZWagAAR7wQAI/h5os8sxXwVvLHuBZm\nan7zoIGUKUR8N3tR36Idb4ZePEU1u4A2f9zqF9uZgbnFsDT13kFofNrBfsgK\nEcUF1JnFzzzBVDm4Zts3E+pO3fLxew/0QQdLOMrBQ6NWDXLBhbk5AmPOaj+I\nUWPh/QFaxTiR6yFLUDzks1fNSGw40aQGufenDagXdEM8eLY830cwFR7BLdUP\nYmTo7EJq7i/7HFMNYLEXpYy1QO4tOrdlstOlwWx+8N2iXoFW3+TvnfD0OZRj\nBFkWAUswNV4Di0/HoeAk881Hb8xQRzN69lUTIJJtxDpebA4GTUtCrh0dHMTu\nvi/2Ls1WI8/W9glUhOA5JQHoGIy6a1Mms+wa6auOiSrmDCs8dBb0Rb/rvP3f\nfgxMWBV51UMvUGJ2wQF5up9yefkW5Izgq2baQwTfYC2MmX7Pt0cAqhvqgqXJ\nWu321HKoUY8XU/U7eZzxdIWpDofwZcX4sVXQ1DdYrLTyS+vdin5cVBYoNjZy\nAX2L4Pt7T9Fp3Zx+wT+rYlQY+g+e/aSaIy3b4kXLU7Adqx6jJWZ8FA+/+rbd\nIh3M2YJTJpzpH9dZXMOYrd2QkZ30GKraousjLEVYeSP/tFLUVNKBXBVbbNJ3\nfPrTy0fBfB9/pbNEa2W+6hY+NJDsjQ5s/dWpCoKzPdZTpSs7QLxbB1ewp3uZ\nOieK\r\n=Qzfi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.2": {"name": "fast-equals", "version": "1.6.2", "devDependencies": {"ava": "^1.1.0", "nyc": "^13.1.0", "react": "^16.7.0", "sinon": "^7.2.3", "eslint": "^5.12.1", "lodash": "^4.17.11", "rollup": "^1.1.2", "benchee": "^1.0.3", "webpack": "^4.29.0", "deep-eql": "^4.0.0", "fs-extra": "^7.0.1", "react-dom": "^16.7.0", "@babel/cli": "^7.2.3", "cli-table2": "^0.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.1", "@babel/core": "^7.0.0", "webpack-cli": "^3.2.1", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "decircularize": "^1.0.0", "eslint-loader": "^2.1.1", "@babel/register": "^7.0.0", "fast-deep-equal": "^2.0.1", "@babel/preset-env": "^7.3.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.4", "webpack-dev-server": "^3.1.14", "@babel/preset-react": "^7.0.0", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^4.3.2", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-rapid7": "^3.1.0", "rollup-plugin-terser": "^4.0.2", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^4.0.0"}, "dist": {"shasum": "f1762bde0d571b5e9d4e1c4ae0599792f6a6df5f", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.6.2.tgz", "fileCount": 27, "integrity": "sha512-6FiKwHkrHIqdvo9I92yzCR/zuWE6iy5ldcOszStPbmo7Zzj3OoVeng++GE//JqO4i6JQ4vH/BVAfCmUCki+C3g==", "signatures": [{"sig": "MEQCIGJCL5g0Ogkukcwgtn9gBcUSH+R++lQ+JwtUNmckGjefAiAj2HJU1cYx6LBaxzYQlNXXibInh9NMpmKaiYUGoXFbdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 464952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxAECRA9TVsSAnZWagAAsGcP/jdupcZKnBtV/kHa2jU+\nrx1RZ0PBMuo6H9dDalqh/wvcCGK9D07mF8YPGkl1AWhptm/JsdD/8DRXe1Tm\nxv7kneAcoj+d1vBALM7x0HTh4RUlK5dLbOaPVQEcvzTa/u/ykJqxFI+lg0y7\nmAggAJdS8I5OkcZraOJpafQy1jyYoBXNL5rk/N3WMHCAxRRAC0cTgg+0W7HO\niWpcwUwDnHltI0tapn+wCH7i6y9ik0R5wANrY4+2sz3w7OlYjTZIVm90tMmJ\n1lGyE4Ti4+Zi6ErtHT48zlmbUdMs+J2e1/pKl/CVpWZMaaGRKnnzhj8BLvfR\nYH21Jl+hI7kaZm2G7pMRR8vYDud7H4x0lHCbf0X2wrTE9SWoSiUgvkE+saXQ\ny85gIMxUnMs46oK+VR0ttAWg3KU36rBgUHusv7q7bKp0t9/FIKWNt7hSCClB\nUySuaKSPkbQeZe3SnFvOnZeQZx6nz5CV+W78xwWYpW60vPel+SXc6LnYPq0F\nIW512QTV+AzBZF/68DbUdd5KV3rHWxVZmyjf2hkQ8wjWWSZg7OU9ZADnmJE9\n8/K3IHazW3Zl55mE/lTWWEgSz9in0C6VNRe+p5zbSrgsM4gtO6Lk+QHNzzYb\ngnrzwfaZ4+lOplfwG63YPmnCnr5zeRPi7F8iTcYv6JamUMqFJJKBQyTmkfLL\nPire\r\n=6CGN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.3": {"name": "fast-equals", "version": "1.6.3", "devDependencies": {"ava": "^1.1.0", "nyc": "^13.1.0", "react": "^16.7.0", "sinon": "^7.2.3", "eslint": "^5.12.1", "lodash": "^4.17.11", "rollup": "^1.1.2", "benchee": "^1.0.3", "webpack": "^4.29.0", "deep-eql": "^4.0.0", "fs-extra": "^7.0.1", "react-dom": "^16.7.0", "@babel/cli": "^7.2.3", "cli-table2": "^0.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "underscore": "^1.9.1", "@babel/core": "^7.0.0", "webpack-cli": "^3.2.1", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "decircularize": "^1.0.0", "eslint-loader": "^2.1.1", "@babel/register": "^7.0.0", "fast-deep-equal": "^2.0.1", "@babel/preset-env": "^7.3.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.4", "webpack-dev-server": "^3.1.14", "@babel/preset-react": "^7.0.0", "html-webpack-plugin": "^3.2.0", "rollup-plugin-babel": "^4.3.2", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-rapid7": "^3.1.0", "rollup-plugin-terser": "^4.0.2", "eslint-friendly-formatter": "^4.0.1", "rollup-plugin-node-resolve": "^4.0.0"}, "dist": {"shasum": "84839a1ce20627c463e1892f2ae316380c81b459", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-1.6.3.tgz", "fileCount": 29, "integrity": "sha512-4WKW0AL5+WEqO0zWavAfYGY1qwLsBgE//DN4TTcVEN2UlINgkv9b3vm2iHicoenWKSX9mKWmGOsU/iI5IST7pQ==", "signatures": [{"sig": "MEUCIQCYJb2G5zDNBuK2/9mYKo/8QAHUCKvy0uOdiSqBYQt/CAIgWoMjNpvNIAT2psS1gCY0QEU1d/sDU3M9yCuX3MMK1fE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 465327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcy6z7CRA9TVsSAnZWagAADBAP/1r+38d1BO+y8hLxwze9\npOz+TlRbye2PA11g7tbKn8vxrlkqKev3QmScQ4WjMVp1F2RH6YYqqXnKaEmd\nPz4c9q9orHPdE3D+kBWYUTxE5XQZ1VToL8n4Mrgx0h8ViZxhlc0dYoQO5nJ+\nVi8HvyVKikVe9ScO9dVy4yqcOs82F/ZnIbHMGgiGenq8hRqWMpVumuR1lHZ8\nc1/uWtNu5R1/sp5m6nM/8niV+X8iwDvYlSVG6WUI/hW1osvKia/p7/Gt3Kwt\nDxiJkicqxmGCWDmgazlhcyVU0tjyy07CY3XIwOTgR6NESxsnwa4znT5nPDwk\nT1VDVELuFMR9QwCLomn/rJxrVY0g0wKJSaB+WtaKdze/41GGbUjFxD3JtQix\nqLw9xrRS6KzJIZXCJQWFRBzNdsF2D690sflwMJiRMeJg8nyZkSXIjGkRWJDt\n8Lb7JWdNG+qf7nm7/+PHzddZC8MdJ6iK4VBdeTgtHwO01ebXctdafARWfKtV\nIxe/bJ3XSRudh84/f2rDzD8bSMUGPiLfP1+ZHBzJf7gdzt36I7tGKl9vJ7xY\n7kkUq9+hMjmx7MZnN52tkW+JseEzzgKI+kZhveyE1ud6xRSk1HwSLY6HodEq\nGtq6yTxvCBojk47ogawHLZS0+nk+qx0s2rgyKFh89FvMSivzEymH9TU5GTOB\nT4mP\r\n=ysT1\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "fast-equals", "version": "2.0.0", "devDependencies": {"nyc": "^14.1.0", "jest": "^24.7.1", "react": "^16.8.6", "sinon": "^7.2.3", "eslint": "^5.12.1", "lodash": "^4.17.11", "rollup": "^1.1.2", "benchee": "^1.1.0", "ts-jest": "^24.0.2", "webpack": "^4.29.0", "deep-eql": "^4.0.0", "fs-extra": "^7.0.1", "react-dom": "^16.7.0", "ts-loader": "^5.4.4", "cli-table2": "^0.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "typescript": "^3.4.5", "underscore": "^1.9.1", "@types/jest": "^24.0.12", "@types/node": "^11.13.8", "webpack-cli": "^3.2.1", "@types/ramda": "^0.26.8", "@types/react": "^16.8.3", "@types/lodash": "^4.14.121", "decircularize": "^1.0.0", "eslint-loader": "^2.1.2", "fast-deep-equal": "^2.0.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.4", "webpack-dev-server": "^3.1.14", "eslint-plugin-react": "^7.12.4", "html-webpack-plugin": "^3.2.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.17.2", "rollup-plugin-terser": "^4.0.2", "eslint-plugin-jsx-a11y": "^6.2.1", "@typescript-eslint/parser": "^1.7.0", "rollup-plugin-typescript2": "^0.21.0", "rollup-plugin-node-resolve": "^4.0.0", "@typescript-eslint/eslint-plugin": "^1.7.0"}, "dist": {"shasum": "bef2c423af3939f2c54310df54c57e64cd2adefc", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-2.0.0.tgz", "fileCount": 54, "integrity": "sha512-u6RBd8cSiLLxAiC04wVsLV6GBFDOXcTCgWkd3wEoFXgidPSoAJENqC9m7Jb2vewSvjBIfXV6icKeh3GTKfIaXA==", "signatures": [{"sig": "MEYCIQCt8uvsTd03j5yfRAEjx5x4mD8yTHhzG10ySQZGlAlFYgIhAO8MFk3t9Fe0uYt2XU83AHvDrgL/kHM/XfzDonIvsSu7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczZkOCRA9TVsSAnZWagAAs4EP/ipO8vZep18E5WYpWVPh\ncMiGQb7L2texD4ZVjBiHSoHiZJUmvfkd0tXmwYz1OH75A+X/KNkBGT6aPxzB\nl8S9vOA1CJ4aviQbMveMWG01xvUQLvYRCUAGMriJLjUMp+35SaZDjGp6rrnH\ntSfRIe7auNiDuFkehCOTt/vBIN4URq1MQySJlTUtZG4Fuc5+2bs4tdJgEcFw\nZFsrqh9WBkGwUxNYIk8FjwT+I3S2AOpGzOZeKbdiA5NcWa2+X31EZlbfQCUL\n05IMVT8q/rg+KdGY3G8BZuK/ClsjMGQa43MdtfeLOrU8mW3JCJ64bR+LSyAb\nHR5n3VkeiVPWQlYwyk21Hd8/uvW8OJuYS5XhOF5s8jOXoz9CiihMr4x5UK7p\nRNOiVEnB9LGms/D/j8IVHQIAD0HWa6xbyyrWHkXyMt+ujGrBdjFhe3qRJkXY\nSJ3wMjIrSAafUKfflHWKsvpAOh1JLVNBvGXBi753L+HTSsxY6P/to2Zu9inB\nmlMXTwD7pVvJsbApiWi05wmLjVJ/ObqfwL+UZHgubjqKJ5yKou4qYAzOe31r\nztiQ/XritmWMbfxq2V/cI+NqHH/vUvjt5g8kcoTvUowivSNQd1tnjF6xB4I6\n2J7gsoNXyPU60WQY/eQZzrR2R3S+vlvKT6NElMe+ZlhDXaJrsVFH+vR4Br7r\nNmVF\r\n=sIZQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "fast-equals", "version": "2.0.1", "devDependencies": {"nyc": "^14.1.0", "jest": "^24.7.1", "react": "^16.8.6", "sinon": "^7.4.0", "eslint": "^6.1.0", "lodash": "^4.17.11", "rollup": "^1.1.2", "benchee": "^1.1.0", "ts-jest": "^24.0.2", "webpack": "^4.29.0", "deep-eql": "^4.0.0", "fs-extra": "^8.1.0", "react-dom": "^16.7.0", "ts-loader": "^6.0.4", "cli-table2": "^0.2.0", "deep-equal": "^1.0.1", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "typescript": "^3.4.5", "underscore": "^1.9.1", "@types/jest": "^24.0.12", "@types/node": "^12.6.9", "webpack-cli": "^3.2.1", "@types/ramda": "^0.26.8", "@types/react": "^16.8.3", "@types/lodash": "^4.14.121", "decircularize": "^1.0.0", "eslint-loader": "^2.1.2", "fast-deep-equal": "^2.0.1", "optimize-js-plugin": "^0.0.4", "react-fast-compare": "^2.0.4", "webpack-dev-server": "^3.1.14", "eslint-plugin-react": "^7.12.4", "html-webpack-plugin": "^3.2.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.17.2", "rollup-plugin-terser": "^5.1.1", "eslint-plugin-jsx-a11y": "^6.2.1", "@typescript-eslint/parser": "^1.7.0", "rollup-plugin-typescript2": "^0.22.1", "rollup-plugin-node-resolve": "^5.2.0", "@typescript-eslint/eslint-plugin": "^1.7.0"}, "dist": {"shasum": "ffc1b0746aceaa855bed91e6aa0e70968645ed7c", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-2.0.1.tgz", "fileCount": 17, "integrity": "sha512-jIHAbyu5Txdi299DitHXr4wuvw7ajz8S4xVgShJmQOUD6TovsKzvMoHoq9G8+dO6xeKWrwH3DURT+ZDKnwjSsA==", "signatures": [{"sig": "MEUCIQD7twAX8EloYUs0hMwiiEwbkX5NjYynWKgM+t8DMCbcgAIgIdxFsk0E2P7xU9SoNJdY071964fXIO9/GoJIB2CqiWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbbAcCRA9TVsSAnZWagAAa0kP/1rD3hLSyKYJoalGV33M\nIfdLO8TEb9kQk9nOm/45dxBdr1DFsp+xq6w7HgOi+1GV8UqiZ7xBi1uEldRY\n8sjlD3daAQ0bD0s2YkTtkB7de1kn0LHLsPzuqSK1Cgb/WkvO9DnBLC1DhXA1\neCaoMjUwUROJRlOUnXCh9xmO4LHdLEbGPImwXu/rJ181sAuHR73dLJls/WPo\nDGtXjy8gPchA87h0m3U9g9AKLP4QslafoXlsDSZuh6vJZn3XEg/QADRTqs7f\nZKJXcSrbu6L1bD5Gd5P+3UypPOACTcZrnJ4x2kZnCYM1uGZY9havOOgURDzV\nctVGZqHOEDROASBW22UpI1lcLD2n1SqfXHLehediNi9RlwYasc+QmLlpW6Yr\nmcHxmqYIYO64gHGTDzMJXhaj03Kdb1aAlsjY7B4AP196RxAFk2qNNnXCdalc\nSMCbwd9Y+BoWaj/ROZhBKDeeL05KUpGmqBLFeQihLPR0iCKQf8+QOUY3vJPk\nZArAWtJIhJMWXmcT39FqhdjqNw/smppqHXkPXTipQiCA6Gk+/xoZsdkkF+hT\nZsq2FLMZAsISdQ0ek1UZSqST8nAN+lybRYRuWrCXZBgdIATt/ZUYtAmBg63o\nWv+1eW6Vzx9E5KmFUU+5FVFer+i16LdlleZzpukt1nfWdc5ERdUDpfkNzXeY\n4aw4\r\n=3Amv\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "fast-equals", "version": "2.0.2", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.25.0", "lodash": "^4.17.21", "rollup": "^2.46.0", "benchee": "^1.1.0", "ts-jest": "^26.5.5", "webpack": "^5.36.2", "deep-eql": "^4.0.0", "fs-extra": "^9.1.0", "react-dom": "^17.0.2", "ts-loader": "^9.1.1", "cli-table2": "^0.2.0", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "typescript": "^4.2.4", "underscore": "^1.13.1", "@types/jest": "^26.0.23", "@types/node": "^15.0.1", "webpack-cli": "^4.6.0", "@types/ramda": "^0.27.40", "@types/react": "^17.0.4", "@types/lodash": "^4.14.121", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^3.1.14", "eslint-plugin-react": "^7.23.2", "html-webpack-plugin": "^5.3.1", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.17.2", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^2.5.4", "eslint-plugin-jsx-a11y": "^6.2.1", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "f4029b437d9e8184b807233bdbcd371e3bc73cf7", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-2.0.2.tgz", "fileCount": 17, "integrity": "sha512-ehHsR38w6sqy5E0QrWQxclb+zl3ulNsgCVWt1cMoZ6QBFgtkr4lKZWpQP1kfEFn6bWnm78pmiDGak+zUvQ2/DQ==", "signatures": [{"sig": "MEYCIQD1KYCxbyey3pmOxVPJ3uPjrchB6/OGsEjUBoz6Biy9UgIhAOWTFmdZ5OSlfsP0q0R+D1HlFa1Qydw9qOpc+CPSyZ2M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgl3qZCRA9TVsSAnZWagAA/x4QAJzo7devECptNettRnIy\nEtgBVdC2I/GY9Wu+G6oVyW7j0XI5Dl46VKQxrvnJ3WbmKqmhRV95+EKRL0Qp\n6QrA1aeUVZhMCjzN8Ogza+vpxWnIMc8g7mL2KIUA84cHRNYrgwIORBcKWyvN\nlLykx0PHqRZ13dtKt/xoDpYiKz7LzUAI7aRF99dktJpMEFGqxyWxbEaQ557D\nWWujfg8SRdSScDh2MFVEntK/4mbiT2lrT8rvknNfXK6a1qklxCOludLxgeni\n2BZWVHpiyLD5/vOCUxgbhHeIUSA+90AOt4e0m9V5mu5jQ7RedCypymJ9exQK\nAR4PSehggckN6XrILiFyOHhHdmt6myeMfKzdeFjWXmTP4PUen6q33jcxYxpI\nKT+sO1w0Pp/XY4f7KFlHkeHAMMxwx8y52giz3x3bzHOM87Fawk64yf7fnXgq\nwRWOA+IWYxLtV4dQtQnLljucJOYkm6MUX7RJKBh2ce6HRdMlW7VNLeCw+KUF\nYaYJICKB46ZHd96NNxKtshNVspWzVaFS5zj+RbYaWxJvnRj+brvU9wDK7Nd3\n5ur0rUPoVfA6llPnuesT/SDWBApMSReQ+ITOEH6wDXZQYVsqn+miRGnjYjDU\npPEv+xHFc15hM9bvjbZmTo3a1om+3sRaNuD1hVzSWZhiK7HGOHA4hal+tfMZ\nb5bw\r\n=t1Jp\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "fast-equals", "version": "2.0.3", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.25.0", "lodash": "^4.17.21", "rollup": "^2.46.0", "benchee": "^1.1.0", "ts-jest": "^26.5.5", "webpack": "^5.36.2", "deep-eql": "^4.0.0", "fs-extra": "^9.1.0", "react-dom": "^17.0.2", "ts-loader": "^9.1.1", "cli-table2": "^0.2.0", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "typescript": "^4.2.4", "underscore": "^1.13.1", "@types/jest": "^26.0.23", "@types/node": "^15.0.1", "webpack-cli": "^4.6.0", "@types/ramda": "^0.27.40", "@types/react": "^17.0.4", "@types/lodash": "^4.14.121", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^3.1.14", "eslint-plugin-react": "^7.23.2", "html-webpack-plugin": "^5.3.1", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^18.2.1", "eslint-plugin-import": "^2.17.2", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^2.5.4", "eslint-plugin-jsx-a11y": "^6.2.1", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "7039b0a039909f345a2ce53f6202a14e5f392efc", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-2.0.3.tgz", "fileCount": 17, "integrity": "sha512-0EMw4TTUxsMDpDkCg0rXor2gsg+npVrMIHbEhvD0HZyIhUX6AktC/yasm+qKwfyswd06Qy95ZKk8p2crTo0iPA==", "signatures": [{"sig": "MEUCIE5hUJS61N2FUGlMuLA+ReMu1hDrOYPw/jwrS5PrDhxnAiEA77ly5Ri9C6CVkz1JL1Uv0/YqMasVjCJsiqse+yVtLkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmx1yCRA9TVsSAnZWagAAsLEP+gIwAVeEqdua3TRXd5//\nfG73+popFMvMl++U13uGj1+g5gTBIPCzY9iaeGyU95lc9FODIOezB8epbD1s\nUgivHmOX/J8xALuda/ro88hN2OYDeCdZBUyZtZp/bZdCT54CNk6Ss7yChecn\ncsUBgjOEn6ev64PZeMQtCrVesm022eYDaPRlL1mm0HliVORMpSr4MF+Pqk5E\nnRVfFKKA1mal1VGV61kDxFud16rW2lrIMbfFv1vI+i16whl2yNQGPxQP4pn2\nAfIgVBDgLTwROBZ3rVvtiDBlimR4MzCNmtuu5TI5bNnDfpM2P0+wY4Iqhjiv\nbItWb077JF9EmUwZ/25OziesGvpzbXNjIHcgY3SIJ1U7BUtDpG3cJOEQiT1N\nJcG/mWtGDQ623N533/LyvO2UlNxLlXyRstjPTvu4FUn29Qcx8SclDvHbJvQh\nM8Cl7rhjlJYXVkZdIA9E++IfU0HilJzJMh4qxWqOUAYCW9epLdcbdq/XocT0\nnVObra263pwNiR8QYvuYgOqgWVf5lmrzqktVVqCJqDhrOH6x/vbPUSAqqFbq\nGHGVgLybPGL2/WCbe4XUVxfMgFdaMlaJSOU/wDLzY6IpIJ5dhng8ztlJwAgW\np0KFjsAYvrPv/SX66Y0dEHmA6o4GNLLPsKHZ5oxJbgTJXXNrvxj9pumq53/H\nnhNo\r\n=kbwZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.4": {"name": "fast-equals", "version": "2.0.4", "devDependencies": {"jest": "^27.4.3", "react": "^17.0.2", "eslint": "^8.4.0", "lodash": "^4.17.21", "rollup": "^2.60.2", "benchee": "^1.1.0", "ts-jest": "^27.1.0", "webpack": "^5.64.4", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "react-dom": "^17.0.2", "ts-loader": "^9.2.6", "cli-table3": "^0.6.0", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^14.11.8", "typescript": "^4.5.2", "underscore": "^1.13.1", "@types/jest": "^27.0.3", "@types/node": "^16.11.11", "webpack-cli": "^4.9.1", "@types/ramda": "^0.27.59", "@types/react": "^17.0.37", "@types/lodash": "^4.14.177", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.6.0", "eslint-plugin-react": "^7.27.1", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.2", "eslint-plugin-import": "^2.25.3", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.5.0", "rollup-plugin-typescript2": "^0.31.1", "@rollup/plugin-node-resolve": "^13.0.6", "@typescript-eslint/eslint-plugin": "^5.5.0"}, "dist": {"shasum": "3add9410585e2d7364c2deeb6a707beadb24b927", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-2.0.4.tgz", "fileCount": 17, "integrity": "sha512-caj/ZmjHljPrZtbzJ3kfH5ia/k4mTJe/qSiXAGzxZWRZgsgDV0cvNaQULqUX8t0/JVlzzEdYOwCN5DmzTxoD4w==", "signatures": [{"sig": "MEYCIQCsjUogHKK5fzfMWN4ehjp07wJ0WpD9//Hl8yJUBzOt0wIhAPKpFc7p4VMnzwDIWzdoM3J17yV3LUxlmD+3Mk/xntty", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135657, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsLzsCRA9TVsSAnZWagAAymEQAIpdyhTq3+JX5r2sjiqH\nFrVSqWm3w4heckI7SwdDFmJJAAHZaiYpf6MWg+ylxfzrddr7b3Cfm4QzfjJ/\nUy+slf2MgGEzkmiBaGXqsGa41FHMVbYdkvGhb3enSD5EpiiM4Z49swzn0fDa\nGQWWt3k9z7p7ZMt4AEDpSel+tm1bvMjlQkGxUvJwbqaRQKUh6ZTLp+EjYMc7\nOcoRAXP5pHMiODOIarBFP7AbrJyVX95y2a7IiHOxtoKf16DmxNiXDXihrPYC\nUzgPZugwZFAG5jK8HQVfhBzaYuJuuTORfsSb34MRv9n1LqNlUqxBQEoBEV/l\nvW94AsljkifNVr1vya4ucQjRNJNm/yQyWbfxama+/rH7Y8WBjgkRlV6BGBgS\n4l1DFqse0PVIGe7/ticGFq7UBRY6p3xJGm6+RfllGrErwKiQGfzNANujB12U\nUURsjqczVOF+OwKrXy1BHiJXZgoei8IMF6ulrDxDpfBYbNe1BBvJqLGx25IE\nEDOFu7WLxiDr6XbdlFXgwQldGxpX70wAurPRuCv6dM4VwfVXYO4HVo5MIXWe\nw1fEPLqFCNs8AN+0ZI9uLVM+6frSv0XmmoKOQfXl0JEX0jx9deRxjtp497tJ\nNTnABiMdlWgj4lI5vaMlSPIO5sJ1iLruZSGVUv5yu8LWrHXfx88RBCmHkKIG\nD5nK\r\n=aW2N\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "fast-equals", "version": "3.0.0", "devDependencies": {"jest": "^27.5.1", "react": "^17.0.2", "eslint": "^8.9.0", "lodash": "^4.17.21", "rollup": "^2.67.2", "benchee": "^1.1.0", "ts-jest": "^27.1.3", "webpack": "^5.68.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "react-dom": "^17.0.2", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^14.12.4", "typescript": "^4.5.5", "underscore": "^1.13.2", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "webpack-cli": "^4.9.2", "@types/ramda": "^0.27.64", "@types/react": "^17.0.39", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.7.4", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.11.0", "rollup-plugin-typescript2": "^0.31.2", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.11.0"}, "dist": {"shasum": "efbe679d4c0d74040f61d4dda3e6bcb3bdccab82", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-3.0.0.tgz", "fileCount": 17, "integrity": "sha512-Af7nSOpf7617idrFg0MJY6x7yVDPoO80aSwtKTC0afT8B/SsmvTpA+2a+uPLmhVF5IHmY5NPuBAA3dJrp55rJA==", "signatures": [{"sig": "MEUCIQDjUPuBgxFtx/2MaYfLYLSvzG3bETQWZVEmRhuANnAOIwIgNzI7QHilF98JbRptjKSikfX/PgdAQ3oSVemkJ9MTlqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142839, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiCZ1ACRA9TVsSAnZWagAAoSYP/A6ybHGRzo2fWSGcq8vW\nBe+CnqS/7UySwCCgzlui8GTcHG53xdcfakJLFhs8kVco3WBiTRdOBCKMOJdV\nnABPcKeNmBw9upYhwEZtYoLGxuj1dt3l7pyC+/DPSTjc67JuFBeCOd3zd39I\nFBmUPdobw3RjYL/wKKvl/dnjoynuRo9bX3Wk+sFBkvueOxtj4mLgQ+14Ibjw\ndSFSVgpdU8wwz5klXOrMzGyNO2G7v+y/ACydCeabs6tArpPxZCUVh61Xx+2U\nzpmI9WzrCmlBFTmKchHcdRnJOPZ4LPaC76RJ/omGw94wOzojYbO9LQL3NJdI\noza6pzGg0vAzQrswS6Lh79MvBZfmcWdCh8ycaP251kVzYxEDYw0aK7M/gowj\nhQr1O4Jo3b0Ep3AHmbksdzSkjXuq81LiViyPEwgYT0Ez+gZxNaKxhr64Hovt\ntJLtq+nDnQo1CtQNEky47ijAhpBJN2qJ/g9ZaEfcoLoj27lTwrzvaFYMh9Hn\ngAU4lnn5IIf1uxUVQuIH0CMr2F47c1kuc3jc0S/qqFEeC9t1OUqkg8S/Mxbq\nYZ6iCIPrRFqW2p/b64mZYTJX0+poAPMDPJuaIuAJWdemCzicpOwZiSw1YLUE\nt9JCqiqCWdR06dTBZdQVqEx5q38H6JXlpHTKOinV7iPMmKTBHxslTCFz/5Kf\nl/Cw\r\n=WkWc\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "fast-equals", "version": "3.0.1", "devDependencies": {"jest": "^27.5.1", "react": "^17.0.2", "eslint": "^8.9.0", "lodash": "^4.17.21", "rollup": "^2.67.2", "benchee": "^1.1.0", "ts-jest": "^27.1.3", "webpack": "^5.68.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "react-dom": "^17.0.2", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^14.12.4", "typescript": "^4.5.5", "underscore": "^1.13.2", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "webpack-cli": "^4.9.2", "@types/ramda": "^0.27.64", "@types/react": "^17.0.39", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.7.4", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.11.0", "rollup-plugin-typescript2": "^0.31.2", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.11.0"}, "dist": {"shasum": "1e1ae440c04a32478faf698527315cdcf3ee7db2", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-3.0.1.tgz", "fileCount": 17, "integrity": "sha512-J9FxqqC9E/ja0C+SYhoG3Jl6pQuhP92HNcVC75xDEhB+GUzPnjEp3vMfPIxPprYZFfXS5hpVvvPCWUMiDSMS8Q==", "signatures": [{"sig": "MEYCIQDrOCYwsn8deC9pNTAXtbEBsf7XO4w5/HmuQrJNu7o9VQIhALKRIO4quHA5iOx2NumVEfAG6wZ44E4xOFVzjtmtwDqG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTcHIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbDQ/+Kgyz2jHl5c1fsihkNLOPOjSEnyqfc98wz+zLpKplAVnC0Ega\r\nfvOS8fmWGihCunKevlsarg+kn87/ZLUUAWzqIQ9d4M7cbOTw741KOs9SHITU\r\nntEKSq8mKdMIHYmpTQ/fwpKk83pm+/+829gDMFCP8muRImj9kNMLuM/zEHoU\r\n0BIkKbWxjDbpGT84B3sA00BQw9hPyAMV6ERqLEVoVSz0zjm4hzpGoMNtXzpc\r\ntr6JdqeSS7bWkFAizG+jOpkuv6DfKDnU2Snmf195S0v7hza+jEvrtm3DPOMf\r\nvS/ws1EH2Ylbr7WNNELZr3eWyY+i3NolPlrmL38Oq2/63UW2vJlLJRUTcCcs\r\nkzb0g3I5DuDTiDBXZVjmG33ed1N3AUeFE1dqiGWyvCrpylv9tq886kgrlNqa\r\nj8VjxJRkGzFkbZQurOS3UOnus1N6LfJIVWgGDnGAEb8nibsrzVVjdhUmhOKL\r\nMg3hpQXr0s+tC6yME2vruZWskvr+gsp+52OP+hrjtpeHtST7gJbmiYP33gfJ\r\nnBX3sfMMF7jk5wyi9UTrAzRU1YzJd+uSdqPl+BydiEazhvRZaxJ5jMXB5uiE\r\njMF+JxiQqYLY8a8Jm/ZZawuj0zUZEj4MysLY48RCQ8v/V6Kmvhk/egSWDE7e\r\n0T60vR6SGWiy1tkxJ0xHzCsJm7XvOG9UrAw=\r\n=0sMP\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.2": {"name": "fast-equals", "version": "3.0.2", "devDependencies": {"jest": "^27.5.1", "react": "^17.0.2", "eslint": "^8.9.0", "lodash": "^4.17.21", "rollup": "^2.67.2", "benchee": "^1.1.0", "ts-jest": "^27.1.3", "webpack": "^5.68.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "react-dom": "^17.0.2", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^14.12.4", "typescript": "^4.5.5", "underscore": "^1.13.2", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "webpack-cli": "^4.9.2", "@types/ramda": "^0.27.64", "@types/react": "^17.0.39", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.7.4", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.11.0", "rollup-plugin-typescript2": "^0.31.2", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.11.0"}, "dist": {"shasum": "77f80047b381b6cb747130463ccc144e24c44097", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-3.0.2.tgz", "fileCount": 17, "integrity": "sha512-iY0fAmW7fzxHp22VCRLpOgWbsWsF+DJWi1jhc8w+VGlJUiS+KcGZV2A8t+Q9oTQwhG3L1W8Lu/oe3ZyOPdhZjw==", "signatures": [{"sig": "MEQCIFp5K3M7Ajla00d7HzdkuehVXrvXFW7io/wxYvdgOHKZAiA9JP6r4aLoYpNDkptS2JNq2KtetN6d2sQDmXz5oaOM8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia+/hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6FQ/9Ft4a09WEBhmOP39cpLZkrzlteoayZtRD1x/LXzFGIdaB9USJ\r\ntMR1Dxn/fUeOc3d7mOU5+2WmPBFLOv7jNxdikJHaM1f96IgIJ5MNdzZDyxpZ\r\nj5bBTLWhZ/XcZfI1uQYI1CNrLBaeIJN0rA7DjOMOLT+BtcFNqn7OuZVO7TYg\r\n1UCGF5VwaqyBZpTQo5B3UKkAbGVKL+EBY42S9+kaxxhWKXI8h5SMoo1lZQfW\r\nePI9zRHctDp3u8Dz9OvRmek6mQPjTZkvCTH5ef5k7wpqCJCXgHV07LM2ufvA\r\nbn22Xm3GyGJMCqhnjUxLFSwBzWVXWVX12vd8/mQccK0Ery3bCN8QVejxi/2a\r\nUMh5Mml288AQQ2ZXv+/28hjRF620EDxHiJvszPD4TU+IDkbvOzOTsvkuHUFJ\r\n49Z6WTJRMqkM/8oJVbn0vmuAuF3iNrd9YuqAdTMi4cwvkGqQc8YtdOz590wh\r\nUjDR95pbPG07rCqv8d7yz2H3QffExKfqZujwyqmvcUc9LzjxIE0T7sISCX6i\r\n/9O+m9jNy/czXRhziqbsRK8OhZK/muHrve7b41Rbtq3ohI70AesDA1JSAYOe\r\nXDcmmu+zyELYtYuLJn1+MKklBwa0USD5/CWRVvClAQxD2WJwcT3zmw5CtThQ\r\njHZKgw17EYYUhZT+f1QNAN4NT5dB3zMoBME=\r\n=ap+6\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.3": {"name": "fast-equals", "version": "3.0.3", "devDependencies": {"jest": "^27.5.1", "react": "^17.0.2", "eslint": "^8.9.0", "lodash": "^4.17.21", "rollup": "^2.67.2", "benchee": "^1.1.0", "ts-jest": "^27.1.3", "webpack": "^5.68.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "react-dom": "^17.0.2", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^14.12.4", "typescript": "^4.5.5", "underscore": "^1.13.2", "@types/jest": "^27.4.0", "@types/node": "^17.0.17", "webpack-cli": "^4.9.2", "@types/ramda": "^0.27.64", "@types/react": "^17.0.39", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.7.4", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.11.0", "rollup-plugin-typescript2": "^0.31.2", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.11.0"}, "dist": {"shasum": "8e6cb4e51ca1018d87dd41982ef92758b3e4197f", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-3.0.3.tgz", "fileCount": 17, "integrity": "sha512-NCe8qxnZFARSHGztGMZOO/PC1qa5MIFB5Hp66WdzbCRAz8U8US3bx1UTgLS49efBQPcUtO9gf5oVEY8o7y/7Kg==", "signatures": [{"sig": "MEUCIQDiWg52KD0j9WcbgNdXB/KrcWEVorQis0ds+dKVKejXbwIgAhLq9YUNKzn9sUohJvtbZJJpz8yNqym3nP7e9BF0GmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 162075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiivnRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6Zg/+L9YdUe/pFrQkphIcoZkh/JxazFy8ddeHuFBFv5PC/8OxyqlU\r\nFQf4Kx2LvAD9KjE0bhcPrV39nG3ew2tHZ0u3ORYetGKwCj7Vx95NLyIzpWIg\r\n3ias+klzjhXRPZVRvmt8tN2CIeQi1yEzxaVCwHtWZ0z9S0HVXlitVa4dHR0M\r\n1G7a+/OM0UkhxDtAkDr8GktzFWZ86YDxStu07Nkj/LiyJuz413Acsw++qGaa\r\n03BzvoihUDtzxdlI00G10jerKSCqGkkG4RD80Cr+dtsDfQQ+KBENrIRK0ew5\r\nQ1Qv2GepN80UpvFLWB9IJl+XsHIzqKulglUhN3Hwgl41xyhmoS4W8OTsqiqo\r\n9KOrqmbZ1AydjfX4K4Taqj+9xufNUVMykGJpYAhFAMweg8XwJe+tLSD3wXfL\r\nuR0DtFzIHCvkjsIipGdkL7rhUyYZI/y2w+l3Enh7LGJpuaKENTXrsk4K+eVv\r\nzs8msGw9SnBtjBarE6N7tudNdpoHDFbr7H2+MEg9z9p0ihx+OTzD89fLI6YP\r\nzFDlkLe35fKgJTthPMQGN3oCAw5PuBAsJva1y8peubzA4xy6huvuoGoPwI9w\r\nDQSEMJpg7OePeZGIzNrVjKdQ0A1xQB01THcJQLphgBBeyBKm4htPlHs4YcVc\r\nYqdufAa0s2PYhHcG5OKa33ZuD7zTqUk3fsg=\r\n=dlfK\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0-beta.0": {"name": "fast-equals", "version": "4.0.0-beta.0", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.0", "@types/node": "^17.0.39", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "d0bf58937fb414739778b871c51c3b7f5b3087da", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.0-beta.0.tgz", "fileCount": 27, "integrity": "sha512-AjVAbYwwDvfyC5tFrOJzrZg4qLvY56eE77bH7VDa0dAdVFVaxX72WJq/P6ap6R8+cxBBP6o78INTMbfQvMsx+g==", "signatures": [{"sig": "MEUCIAiysl60Q8NAh8xaHnzgejkiJJASshlSoW4syzfTf9NGAiEAtrAf9t3PYAAL5duPRkPkHGQm3pdLCYzMeKFelPA8ep8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 234623, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJim/G/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmohhw//UcUsufzZXw3fb7jf94pes1OYVfcwxtQNS8HL3AK4WbgERSyG\r\n2zP/TLyszVfpysW/7Zu+TVVVE4vmeqcGLEKXen9XMrF8i6A4BS7lc6d3egwG\r\nRAswS9oneZqzcW96000xI6jOpgVmOnJuJVeJieGON97B2Mc9OupAUxFEmCG0\r\n/vElpaHPbhIYQE9uS5SztdqHI7pfFvuuWn402EJ3HK2an9kzFCqA7TJICiRT\r\nKuh8DDqMLrpDFyT+EIoRxFD9JpPeN7s/YEMQzm2YsPeZ+eBp0F+33gjDM4tf\r\nJTdfzyfTVVInZXNmO3SYiHNCGyFhxC+yeqlFbmlD1+Suj6BKwBvAouuFh8Jv\r\nU3fjM3+YWbhBrgQxErWOqFDSAKun2Xv0Buf/LCPlhPymxUbw2T/CP+eTnG5z\r\nd6QiDFCtEtDBUQrdzmUHUEX5fOY7ZYFxnzNsBzSR9E19Mau2YxE96sLcv0El\r\n9cf+z4XtHp9yjjbQv/rv8as6DLtCkNy4E2mdElvkokn3B+Xbqz9eh9mhz6tq\r\nJRaRFese+Cr8LgHpJ3YbZThUwKJSiqdC8p71NyEIpl+98pTnwpnInOfDwyvI\r\nIS9inO+laCzYg6MBjyVV9TnP5u/1W7JpxvwbbjGo6kBmUIuzR8QB7zJbNB7M\r\nzKGtFlNLhL6/fsvGaelW0TPEEIzMqJuIw1Y=\r\n=Hm03\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0": {"name": "fast-equals", "version": "4.0.0", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "1e7234d51db84b8725e12858c5abfbb59f789868", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.0.tgz", "fileCount": 33, "integrity": "sha512-4GeTgf1vcInvACBKirEWoN+gN9CWrhboHsIYk5n6ggv/dKZgmfoxcRT4tJlVib21Y9mfQGzLX4RuAtlSAMQh7A==", "signatures": [{"sig": "MEQCIBGxFoI3CX7A7pHuhv5baeWQMHKDG4zz6jP/vGCDnvjqAiBnjDXxHq4YFT9vLiB7sMdr+SoS/UFYdVWusd5dX+nh9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinK50ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBgw/+I0gf7H9cte2231W41xSEf9VNPwD4NIXLEHrIaLXRWrHUDKVy\r\ngbP1a+WUdc9eIUEK2HQCD0n41xqsTAFfn73/vWVYV14GREKZPsyuVt1fVJhn\r\n+1aXe5KW6ARiWhtAaMAT5qQr03FbFM7JFW1wtGSalwfQU20KQHui8cdZKoSX\r\nv5UKJA7Zut00YFP5k3eyclCjUsiI7iGiUYhgXyHNfNs7yEs2jh8paE95b5Vk\r\nDb4pgrC+U6iUkOa6zo/5Wov7SJgG3DtfURyErvimfdksiO0Lp5hp77rHa15c\r\nstzoK7eUXygIe7D1wcBx/i7xlFXIJsEDNXzTKVifmrpXKcDSsCF4kGKeW/H1\r\nqFogSuqc5Ke7m8gTXO1b36K+FUHar9Jr3BW/cAeqWD9+gi64Bq6Q/O0vX+c9\r\nLlRlYPsirJZkN6EMXStnEk9uZnWo/hR7a5NkWixfq6HSrezTypmim4aCDKsZ\r\nu+aoNEVtLB4R4/UfT1LbVeSirnQ8LFfNvDrcjLvCchAGo+vpQs6t5NaDO8E/\r\nHJpi9e9EJ/l9jaak5TLsDa1kDPVtQJh+wGnwSw2GaFaOPrzSA0ZLI7WWeFJ8\r\nne5QXkW4cWteu/mmPILUUKqvzWrRdUj3eYf6Pji7Hn4FFOjtYu+XBq5Pr0E9\r\nQ3odB7nlnpsCBZdWPFJj/4GnkoY3b5N4yG0=\r\n=+f20\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1-beta.0": {"name": "fast-equals", "version": "4.0.1-beta.0", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "e6bdcd9277d326eb178a087be7581d294537912f", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.1-beta.0.tgz", "fileCount": 33, "integrity": "sha512-wupcneeoaVNnlXUEwekUM8nNfjno8vEOHOJrTC9/DozmSMA0iGwGZmfCSe6oEsHl3aYoj0gRKB4dIp+ydymbqQ==", "signatures": [{"sig": "MEUCIC5YArzGLerZee/0do2wKXigNaQtmy655PVv48Pby6fmAiEAgwsKe19+YzLcCwqH2GOqyr29EPUAlMVJlX7wGE6FRJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinQu3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpo+A//UVycOLTZXYOFZbEKGS6I4GDSey19uAwQjfUOxytJCpi5G0Bx\r\nTDMw0ddWSWpGGy+X62Vhxuz4gbxyKqblJtWSgZkZTqUDxHseLmEOIcxikaYX\r\ng5w48M3Pv8cu25rw1y4mcgM/loDoeKbig3IuuQwnzEM/q1zX5E5IMS58XK2C\r\nWqvFiqdIn+wK6bYWwRndi4MvqtihBON9awOsBN8XKpzHmGuiESO45a5X2Y6n\r\nh24djIv9FY0vxo5JbzeLB3IIaDmeP+Ki+ILT9jBukLTJc8SUNGA7pGXnCwgd\r\nGq6LvBXd4Ojmj9Buy3RseT/GTpO3RvTKB4iJcIGD/s+KJ/SLRJtZpj9l1TWN\r\nUnvOu53dr6XDcCAUijFIevvsauV8UF2QKhFEomxVDKDWqDADI/Dba8BvwCRG\r\nw50bV9l7A30pUl4fafbt84qkPFMM7BQpsUK5P5r9NTn347i8REMNcS/XhaoL\r\n/JKjRWZyU3FraqkedZccaLArgOJ3B6kgcqf5oM1FiiWK/+4N4gvl8zIzG2/1\r\nRw8fNzzEYVSMrH3nRyi3Cqs4CFbuYb5MptKb9eGZ3cs3aBBSD5yU/iCk1nxX\r\nYED2N1Wva5RlX1pd1whL7AnU7YEQrU5A2hhBPdWAp/oS843H7DYZLafZyYFw\r\nbvmEu6FDvra3D2WomHsOi8qrcVbZVXWWffs=\r\n=HXXj\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1-beta.1": {"name": "fast-equals", "version": "4.0.1-beta.1", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "a556a99be7ce01d8122990b32e11d5a8d4234d89", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.1-beta.1.tgz", "fileCount": 33, "integrity": "sha512-ARpPCxhLv5vZdSTKG7NxvOPURTgnKhbBaoDqzFMktnzH56PW84KJbnb2P/7NQ/FMrdNw9N1IaezTRlxYqsQGmA==", "signatures": [{"sig": "MEYCIQCETMYfDnoz8bZdC84HKs4aMFCu43kJNWWT5a2xAU6u0QIhAMPohgYXYqQcVF6g/gkfuz6GRuSgX98CRtkjpjD1lyLh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinRhMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGxQ/8DIloHjuH/Y0S2S5VLKPuyV2QB5bVXOT2aSKtFR0+A85Ju9EA\r\n6uJzhmf7Gl0FurBotleS6NuWvf9slvfiW7dvAG35p4GmYj2u/7YLppw+I8j+\r\nnFqUHZGkHQPbzIATvrtCt/5OJxyeRlG6tK2Jv+muvFhnoR+GKHBaVQyIdAsV\r\nfI8Sw/tYREBQ7W1HzNDK1eqAH2HDWXHSWiFkQR2Gtq+RiFNO0rl60HrpsMhl\r\nYN/mjWuYdTPm6gg0KMma4yvDdZcR+Nfj4Pwbmaz/94YPIFvLQ5OuSZWkCRQr\r\nUcxgocAWr6d/e0FBtRroX66IBDGv1LTsv7BdCBlkcMGSbC9wxRFhBOCI1r2M\r\ncu9kvXwA4zz/GipFmSXj1aYzW7JKq40UJBVv0IXK4RSLzVaAdDtFVVgKwQ6n\r\nDAMsRcxPwLNlfMDKKu11KHKFc2zYJA/+UypHBx84gbBqHOYV079I0JbsxZ95\r\n30h4zlefO6FAC7q5yI5hyYV5AAmfcCVAIy3w2HJIjDV/SiAu0nrVdLM+JqiI\r\nmrAqvl+rH79iz1TkJhLtjOzUX21M3yW77PP7yjJzllqlYWsIejd+OQS9JBJC\r\nF6Fa0k3GRXsZ1xlYX6ZEjXcUYQLkgfkVCZTWxCX9mvaccEhX64Vw3emKKAhc\r\nlrRq/Jd46Ns+k+8/3PEbdSvZ96cbKjMdcLc=\r\n=sH/Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1-beta.2": {"name": "fast-equals", "version": "4.0.1-beta.2", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "04494846a9ced3487ab002a63a2531a74bfedcd5", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.1-beta.2.tgz", "fileCount": 34, "integrity": "sha512-oVe8iQ2M0nT6chun7Y4aOsN4hwJo1UPniV9pVHYb7zQMIPwqVQVxxG+PiutCPuFHiXlVTEBFxCW1zUg8zM0mJA==", "signatures": [{"sig": "MEUCIErrkuFU8qMrmpCBgY+onugJGL6dhbq8lR1/UCdVDmP7AiEAkFOsRJJPti2jJzwccR/Ja6Odz3plb/O9Jm6+6DHl+4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinR9xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIXA/+OdFasvdnObX75YIKYDbLyvA4Bpkj+84FGNybbr/BhoLTIy10\r\nj9kAkiHpoZgxadpQpCy4Oe0HDs17IwI6MSzvRMGO4/fHimroQW/KpPDLv1f2\r\nnZWT28Wc+hVpFV+MjH3UsyZ3LUEf2us8wAzHTjVLSvm5S6EfBPNyKTQZ/IBK\r\nLkStarHx6PRrVdFfEYqVMAHNwx7q1KEfUEtKPTlr7XiaEB+Jstcv9PkZbavt\r\nbTkDNbmfA8ZluHw1e4Q0vSq5H7copeO+/ZbJlrFFWEpkxMTWEBreTqZ/by4a\r\nMRyB5KLVb6yCacrEFHxsvpQD3qmyTdVydNxv9Mu1DEqxHemMSAbAa30VyAXm\r\n75AyutHmUvDWmmm6uQkyA8S/CZPDki0HwRNJIrha8m/zl0FDwVnjGiFaf/AC\r\nY/7tp1TLsRNvj01GoiFnhuqauMR1kXoHnRFnSR14gR6YmkwVUwDne4/u29yu\r\n1bKIWlMKdy23U6YWXTWB9uTqpgpmM+krnAFPu6xvXwepGK+8S5fbTr4sCPAq\r\n8PJWkpnBjUFtyzmZYuspox9WgMzXHw97sW3vWMSirvUI9jcPCtZ5tDo/P4an\r\nOVpYoHFwitJALrrawdJMGHMdzh5vXMBZhijMXz/iEdVenkGUIttQ/gkBdlCk\r\nyd3hc//KOzp8k8Tg1+YidXsWi85W9MlTEZQ=\r\n=ldQG\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1-beta.3": {"name": "fast-equals", "version": "4.0.1-beta.3", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "aa470b9f5b5e1f63330e862c3a2c637c5d81ac1b", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.1-beta.3.tgz", "fileCount": 33, "integrity": "sha512-9whORiTLGS/MTR+u4yBW0oM0GKDZga/8D8XjD+GgKVag544AryBeSwJbLsJKwBv/g3HHcHIrBWg+CsNb7v7QUA==", "signatures": [{"sig": "MEUCIHTPM//IYIaFz9xq0rRTsVvYicn4dho9kO6BcDTOFHhxAiEAuz2uOyF64QOCuqt6vu5poM3j0RT1QK+o35wqWYEUFQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 238651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinSYaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol5A/+O2CdxioroGwQg3/QmjM1dZ08Fiu+aiE8IkaiD3Z0w953LZlR\r\nYwrtdceV1UTQkt4z5S1K1aRqhRrRrEalCfEpHvNYb+EC1AkIlZFQYXcyGYfu\r\ndWZuNURMZ968hRtaAgVKNqQSWI9QHQmKoPA0BwzYNF2AxUqUrFNc1OKD5NCB\r\nsH+zDLK56pSGQH6eNcY2fYcNcBbRjpqZ0vBonar+bHpJFSuazXh6IlREyt3U\r\nXu1wS7WpC3UFJO+1yZiHAttA7TgY1DH9n36ScD9L026pfPUouP+E6vEGpbuR\r\nkl4AwPj1PD8eqUSNRSZyANX8t50RRxKk8qY/ghZshXr1TXLSiGKfPdaXJCG1\r\neNHA9EjnxScRyKD2T7R1w1NJnyruVMXXhBxf1R+H4TiIROUvC8wNb7NwGbIG\r\nfghDrtDRPdbEPzyUqgyyBLZfVeKJrnF6wdsDJc6I2+P5+jOKGEzKeUHBMiLi\r\nMWQH57YBPjxHtyM0LwEaSLhwRWyx0DWiZUn57Emw89Gi35w/gMzn1h6SXADc\r\nY41YCfMUGJKzYBoYIKCDrgdag9Ze+r9amQyICGrq1rEQHNP+frVpNEGLIYdM\r\nzlGwBW7jCRHw44/IlazyTu1t3Ea7yz+CfBZy/ST9/76MJ76NixpFqj/ijg+y\r\n9+a+rzu6JkKAWDBM/4A8z8fLh0zfi6CHXjA=\r\n=k+/k\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1": {"name": "fast-equals", "version": "4.0.1", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "ff8f92d18f4f4130ce6fbd3748ef714d01cd0893", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.1.tgz", "fileCount": 33, "integrity": "sha512-OXqyj3MD0p8Kee16Jz7CbCnXo+5CHKKu4xBh5UhC1NbmMkHn8WScLRy/B2q5UOlWMlNSQJc4mwXW30Lz+JUZJw==", "signatures": [{"sig": "MEQCIB/fENpcWe6+MF5NyF2GGjD2oFbMKMY0ECJoegZYkTrRAiAoyc6EUlRhV6VgByx0iw9uDLSIsFd1m3Slb5qx2ADVbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 238739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinS2rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkgQ//cT6BoLrUt/pcZTJeEzIKsSfMqVw5NCU65R8hjS7Vbuk0z3sr\r\nX3XGfjO0rEJjQS1Wo+tc9TyLY2qLDRjArFF+olkBLOWKzFR7WlGVLcd0dFPQ\r\nP2mLw6ugp2GEC88+HBjkPytHGN4Un/qHV1XSEdnF8YGKniHwVrOIv75zaN2w\r\nPBlmrnhkJPac7qxsK+aKr5OfB5vBDKNd4d1ZL9V95E34xffV3GgNZejx1hUB\r\n9/U0amaKRuwcEI/zfrDYro/1hSTWCfQFjCMiT74oRv7pOlpIhJiOyU4CISxz\r\nzkEOvgvK1PkbeAk9YJDBG9z+L4Jzmunp4+uInSiVPf4HTgu0zl9+XnjoN9Qs\r\nyBEKeblsvnAZTQhx6q8oW3m5ijkwMET1DlJyzOeQ0BOHNIkPzkJzx+IKOcg5\r\nnAmcoXHVwlZ3WO/jrdDneE7RAalGW0n8EUUNy4m3GaMx/jivs8N9xqxQUdCV\r\nl6xjeytYLqcblLabNqvleAJx1SMy6Oze8FkVZFtgkNNzETqXgA6liKeMBAWa\r\nFe9/OoLsek/HdYn/Xeksy2dt4guHaxIRoZbwKsnO9WirkBoqZt/nz019OyKx\r\n42tl3veNVhQLN/2XAGZ/J4zHFIgClH5d4VU0bggz0i+appvQCCcrzRvt7CYy\r\nvEc13chQhxOxCo7aXmG4vgZEvS4ZVV7OufA=\r\n=CWm/\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.2": {"name": "fast-equals", "version": "4.0.2", "devDependencies": {"jest": "^28.1.0", "react": "^18.1.0", "eslint": "^8.17.0", "lodash": "^4.17.21", "rollup": "^2.75.5", "benchee": "^1.1.0", "ts-jest": "^28.0.4", "webpack": "^5.73.0", "deep-eql": "^4.0.0", "fs-extra": "^10.0.0", "prettier": "^2.6.2", "react-dom": "^18.1.0", "ts-loader": "^9.2.6", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.0.0", "typescript": "^4.7.3", "underscore": "^1.13.4", "@types/jest": "^28.1.1", "@types/node": "^17.0.40", "webpack-cli": "^4.9.2", "@types/ramda": "^0.28.13", "@types/react": "^18.0.11", "@types/lodash": "^4.14.178", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.9.1", "eslint-plugin-react": "^7.28.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.1.1", "eslint-plugin-jsx-a11y": "^6.5.1", "@typescript-eslint/parser": "^5.27.0", "rollup-plugin-typescript2": "^0.32.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.27.0"}, "dist": {"shasum": "f22ce2b7807bd75f71c5a6d1d9f43a6c7cf466e4", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.2.tgz", "fileCount": 33, "integrity": "sha512-sHhA58JSsyEr5XpIipzrbxUQJIW0p4hDc98OEbbB1FgEZfmnFna4ZQxZ0lYuFynG0q2M+H682m26lqk9j/H2gQ==", "signatures": [{"sig": "MEUCIQCE3KOb5glYCa919IIRzPHx1YWyXActErIjoh3I1RZiiwIgeEdr1xwc2vBXd6n6KZNrw8z0r0Nw+l71ZXc0l6C+w3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 239881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBlLOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUQQ/9FnvLkNgiDJyZTnFyTfNlH/jbVtP/PdRlk7DfSsif7LNoCOvP\r\nhDOPm8BVzU1U0GVp+LtZ1cvXZtEj3s74zQ7+kOMxHO0NffJFex8/KP2COPSu\r\nmY167+qHhjwdurWxZ61i5CELizznl8WiisV70Ks8MWvSVCnn+ZTbtmapcVBC\r\nB2qNew2Ws8u+yYwoLmvSvNfB7+bajiv1EPzAgczed1zcxVhrSHMHgyRX5ym7\r\n7YmYp8C1kjtrlqDNgagiMjIZJMhf5nvrWQWjPKpnnM4/2s9o4c8wEP9eM1hx\r\nloExUY2f1V3G8EpjRRCGdR1NRbqp1Hc1mb81rPymqeqxRSg4MdVZobKWX6WX\r\nZp2Xx3Pv8W3lZKEXGNFtlo9vPZisPMrm1jVryFmBb0d96fT0QlDePk9/yFi+\r\niC+TUJOMPOjshrZRHQXBnEirsbbs2YFPPvMaF+f0nu/nvGW24ZuKgQb4+ZHJ\r\nNsL5bka/FP8gZEfWuaB+N1aXJV/dcvp6zXFahgYoYbnVsNUDaPvcQtlFUjSy\r\nkrCOf6Aplio8KbJgXyZMUQIWFzD5KIJKOXo6NnXVLSIPGyNu5H8OSNq7I4cW\r\n4azjNeAG1Wg21Ku5lJhTkyUDrxG8Ks09WgS6kug7wumrp7R6Mli57mNKfQNv\r\n6b5m6ADpJ5dpZz8ifl/prCgLNmHlicq9oYw=\r\n=jycm\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.3": {"name": "fast-equals", "version": "4.0.3", "devDependencies": {"jest": "^28.1.3", "react": "^18.2.0", "eslint": "^8.22.0", "lodash": "^4.17.21", "rollup": "^2.78.1", "benchee": "^1.1.0", "ts-jest": "^28.0.8", "webpack": "^5.74.0", "deep-eql": "^4.1.0", "fs-extra": "^10.0.0", "prettier": "^2.7.1", "react-dom": "^18.2.0", "ts-loader": "^9.3.1", "cli-table3": "^0.6.1", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.4.0", "typescript": "^4.7.4", "underscore": "^1.13.4", "@types/jest": "^28.1.8", "@types/node": "^18.7.13", "webpack-cli": "^4.10.0", "@types/ramda": "^0.28.15", "@types/react": "^18.0.17", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.10.0", "eslint-plugin-react": "^7.31.0", "html-webpack-plugin": "^5.5.0", "shallow-equal-fuzzy": "^0.0.2", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "rollup-plugin-terser": "^7.0.2", "eslint-webpack-plugin": "^3.2.0", "eslint-plugin-jsx-a11y": "^6.6.1", "@typescript-eslint/parser": "^5.35.1", "rollup-plugin-typescript2": "^0.33.0", "@rollup/plugin-node-resolve": "^13.1.3", "@typescript-eslint/eslint-plugin": "^5.35.1"}, "dist": {"shasum": "72884cc805ec3c6679b99875f6b7654f39f0e8c7", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-4.0.3.tgz", "fileCount": 33, "integrity": "sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg==", "signatures": [{"sig": "MEUCIBkh85sSoCNpeWc877RIsjYkgJDi2XJvBmGhFOSFjB2PAiEA8dwN5UG7XlrePxwbkcVvn5FgbsfIPAunsVKlBU9KZ9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 240441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB3EdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNpBAAk3/c0R7dGBV1uLE9vtIn19o1WSiVLk8LmuWdBGur5Pvf7Qbv\r\n450+1bokxHoflhGUiFhnwKGTDezKgq8qidcOFya+aY5U3gbf62Kxu0fDiauU\r\nud2YjZd4Z88KllDqskAvQMXRwV0qC5OuSDXSRZKNDCGtF7wrPVm6Qz0Ndj79\r\n4JujJosp+PEaTQiNXjcF6WA6ORMpCbDt2oe6GCQqucZ25hEo/b56A5BpZ85h\r\nQnGv85qHgmNlV0kDcKA1dZWvV4mxY1RvFk+LdmGOJC+7Ib7mPF98niM79MMv\r\nv+9vuUjPvIkRSlCzMLV7mgtPXtLt/R8CzYuPaHF28+s+ihJVQFhsCTlF4gIb\r\nfBMT0UMs5TJfpJ4w4YViabcVrZ3w2zqYwL6DAIQXA8XltvurhRJkcjIABqv7\r\nK4UJEJYQuvGGcyTEgtgkBsFxM33OaaU0EVuIeDx1izcHK9ww2blv3mC/k0RY\r\ntCaE9G/rSqHUdlRZUH6XtnYhHkZltOrJhhyjqbBNY4cIHM0fNzeu9G5hVEQq\r\nDGpcZbK6R7H7ttfoJbcdw95KGeV5oL8a7Cig9kWyubiW/V1UWV8gmMxhM+us\r\ny6IChv+0pyp5buPU0KNxk1HR1Br1yTsMzBy26QbWZcyDXGdZBg4XzDbixP7f\r\nqIHCVoKs4XOMcsVIGLz0j8bZrK5ykSpgwMA=\r\n=bKqI\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-beta.0": {"name": "fast-equals", "version": "5.0.0-beta.0", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "lodash": "^4.17.21", "rollup": "^3.16.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.0", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.52.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "dist": {"shasum": "5ee2ecd7ca1b091bf068b8cc010b2a1f60aeee70", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.0.tgz", "fileCount": 58, "integrity": "sha512-rjYFnLR7paob7mwOjCB5LrNqaroSNsY9XBhOrcX7/v2CnujdYz1Bw7z3nFjjfWhHB3pEJJujaYpRop1wl8aCXQ==", "signatures": [{"sig": "MEQCIEkCO71MguOJ9LQYj7Y2A2jTUGMeefp7jKX+/H9r2oCrAiAkPd0MzR/OhNhOArjxYC+yCliJLQMljrooj1KBOMC+7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8E9wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUCQ//cCY3cQfw89RsFhun+upABPL376wE4SrSjurwB3XmxEGpG0UX\r\nWafGB095DXknJqWENDTaH/tNk7fHhHjCjEX7kGfGrDaBnLgHTXBaBTO1c/Wx\r\nUDgbf032NX6buwFReXynsP8v7K6xTB7RMpVRTpqqObaVDJaydVVgjAk6ndWJ\r\nTkyhszuJAi87vcocbpIwEo8gbcV+gtRnhSD+4ZCIKu4Z0T8VzbiK3mdImQ67\r\nBWR2rn7xYXahxUpRg7hSseqbfb8MF9xIlUCaJZM5tuZWugCqyHtZ3ySE29IY\r\nwYZ5SewfQHqktGSWfRgxBGtSECrsOS/765fPp6n5evXX3dVAQdS/HFOwgsjn\r\nkos9dCrnhTPeUDmr/F39OEP+TP2mRQApV0nnRRUhWVdsmNBKOkjDr+CQXEmu\r\nbZCw0T/eQLfVFby0x8xLRQvekgiLW0eY+yUkwXQ+Mf1ANoep8DTfmkyxzn/A\r\nQQU8qybzFfS/39O+2saaK6OAnSt9WHk/4NHjIjpWex5Ja6dSAtMV1nrfF08g\r\npmEoKcmDKCDlmj27vfq8Zq+aPV9ms8vFhoGb959v5bHfygOlI1ZCjIXD3T06\r\nuEUZnf1JCqGEpKOPluWMa9D5wwU/38gBXH9mYCz5Yr5LgqLOYZsYLcMiYUgB\r\nVsmSpxnbacW0PD68xFLmjXmg9Ydm6IozJzs=\r\n=0WEs\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-beta.1": {"name": "fast-equals", "version": "5.0.0-beta.1", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "lodash": "^4.17.21", "rollup": "^3.16.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.0", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.52.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "dist": {"shasum": "2cc2a50a1a632776dbf169c2bc20ca335ea8a786", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.1.tgz", "fileCount": 59, "integrity": "sha512-ecxZCvk2lumXN91tu/bF0M4NQIy2S5QkKOakRZE1Xz4F0xU7vOatwR54nApfWgLiolFBl4YODDiXmkcb2MVRzg==", "signatures": [{"sig": "MEUCIEOsggKcpQIC0w71Rb830al6Y50JQD47ICe904dUlBt2AiEAg8ZLJjV46hr90Rnbj9hG1R12y2W85wx+GgjqFLxpVZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 268737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8OhQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2cRAAhEnjb5dhabMY9BMM/K1FvNybpxOuHo4KrGARdOEX3x+0j+mD\r\nH6+tmIo6hZmqolL1oasEM+9Z9riEjYUCH4792Gya+woljdtihxB9N5Ke+lSj\r\nCGv4b8iWnskSu+2BXZa5w9per/KOWXqH2E/gIxe1RYHLZFH6jfp5sSxIjWnC\r\n+e3L6T5iLWHqVeAmHV1E/h6FvldNFcwroukKaP6L/qt0QuJr5fQ3s8QF7gDf\r\n+9+7fHZY/quLVcBfjOtj9h05ZoOQD7xAxcDg2HUjtTZKe85mWK3kzWO/F0lh\r\nVjPNaErXPvjJu6LFILgwlYRJvNC1oIrM7tu6aVSto6ypEL8SQYWLluPi/Bke\r\nnkzcQaigB+vBslxiaVZ3sG69ZubDzKiGO3pzBmNJ1qY8wGwD0rh3g0/IS+9b\r\nNDilrcfVjhMgElubtR7sgtFPQiPxLV/uTnMrLW/Vl+MQyGdmt2DDnSTUcKB1\r\n8wPkDwlQHCURb2m5kiI5ZTYdG2nJNo6/PeovsGZBbpylN0lrb5bDAtRTNXpV\r\nY8awTJ7INHzK1B4AKN//7BYk0mKCmSgPls9CgaOAFyMkuvviSySS09lDAcjA\r\nVw5qXEV7CTupmtOO4M0D9+UHFMusjHYSR2wZXFzu3VxyPBk3ihW7tE1Z72NF\r\nx4VPdXPIgnbGVuac3tsEnr5sDH93aMJbaoA=\r\n=N3Sg\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-beta.2": {"name": "fast-equals", "version": "5.0.0-beta.2", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "lodash": "^4.17.21", "rollup": "^3.16.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.0", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.52.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "dist": {"shasum": "75333c7bcbd68590c2216ff2a82a9b4c1138ee8d", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.2.tgz", "fileCount": 59, "integrity": "sha512-SOLZwK0ZDd8r440fILto+xqs0ZR3ar7a81CY7buYkeOaawkhi2maDa9l/7fKD7ivpMPVRAeWEOmkzf6oFUwZ9A==", "signatures": [{"sig": "MEYCIQC4Dx5C3ALB//Lb6Seqss3alG/l1Z1/2KwTrFmUxAWO9AIhANATM0hsWehD3Rf3k99YJC+2S+qkx9SyaLLsA5YddZ/g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj8g+DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWoA/+MaQfQ1eB978Xa7giOg9LPc64vg5mSb9r1n5v5v0d4yCae/KF\r\nAbsOawqFW1Ba+LvjJbb1lFiTkCZYSXpZ/uXTYsFTGk8+a3s/4+cnZXYbbfxT\r\nuEvE8Y+uaREkQF/eIDgLEO4E8rh903pYfvN5Nsl5C8B4OB6AfRcl3fkX8QqD\r\nku/Vqs/g/UIV/zeordCnjGGDwTJS1ZCZ1WWRTsnUSCRwD78raGdfPiwwe2zf\r\nzL9/0JcAawjLmP2/Tn9mbek5qcmspeEa0DXUMX6tFbB647iK6ssr+2NsY9IQ\r\n/WlvYPbHtNlPt6eYTooE2Xgv+65/+5hpb/e7KC5fKzhdEbZweEPAJtYkNTOL\r\nr4yn0VX7B6jEMdzcMnSV66MJ403IG3RwAALmwWhnUZp059JNEdzAXSsP7Uto\r\n8ya83FGnGCocSWncE+ZwtbQBbaajR3T+74oxRiCuNaWaDE/vn58K5tqrgV8s\r\nocpk0tcxPHHqjyP0ZMmGjWk1HGOS4dMW+nIANw8GlleXYY+fYvSut2ZMK2wa\r\n7BeYRthW6JvnTPiHF/qaZY2VoAAem91MHBjLY03K8x1IpPBy1c/05O7N1BMk\r\n9yUe2/1G3ngONq6cmhBv4zZbisQ9LtgM0DfiUaSv24YebKQi15sIGWBX1bnJ\r\n1krNN9HPOzvZmcD07zgLg3CPF6PbABUS3Wc=\r\n=8BoX\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-beta.3": {"name": "fast-equals", "version": "5.0.0-beta.3", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "lodash": "^4.17.21", "rollup": "^3.16.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.0", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.52.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "dist": {"shasum": "2daade1a7b39a3b5bc91dbf36d817a7be578695e", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.3.tgz", "fileCount": 59, "integrity": "sha512-dSd8zTZEK0tpPmwoiW4pag/NkqysX8rShlirqMek8Z6w3DF2XKJ8vZhANklUSdv7lLkq4jeSM2mMnzBl+A6VDw==", "signatures": [{"sig": "MEYCIQDkwbyuEpaf9Uu9sRvJzJrneY/Oi2FTWcQlblxtP/0MugIhAKCZqaVdLG7zS7YMcWydIt3EVw7KCZQzkS4u/WLcDFtl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 264691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9OiZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp83A/+MxWerK1BEhvBxDZxcK+eb+PzomqzDB7g5rHVqpE1D0iCtMxv\r\nbc3chdtH4mgagNUPjOVEgIYYTHJP0GtHtH8gQCwHQ10T8q5q15YtbeeYLFJ9\r\nPgyw0ywcmyu6+4/7XbT0ElrQp8jKGqRRI60AZS+FAf/3olZjT9awrZ2lBARh\r\nDblEgZiiJqaqRtcf1NSxWoZ+IRyMyceoi+OK7XKZxoZqwViZ+1WXo9IvrUF1\r\n9WZQflA79BtH6Kpe1b+iW1ahq4Q9euDvRbF+0p5Td6Yb+xP3Hn3jJ5O2Su+V\r\nKENeY5qfHlY+79zmvnQglB5zLZMSwBxEgfujNFGnaV2iE8aQEPQPBQQ9j4MJ\r\njhWEi/NiIWSFYn73m9HGSmaf7sBpVqS948t4vfyChmsTBzOsritDmVfW5+bq\r\n298muh6+k9VvQ04JSVkrPnJl7iKIiMA7B/MlDiBhVdxLfPKbhNAaVr4klwPg\r\nxBmzFiP2Yz5jFIYG0Hq3iIY09z3UhpI20zSI3pGCCPgNMSB29J1zp1nfV9sE\r\n7iLr/vNYgLwGIIdFZdu9jhT++QYRvp7rMhZUBHcjDPWRUxVsCD4NR9W9MSvk\r\nY+O1s6qISK8uBMQWbLRLkfGeHjekBPjA2v7KGSxNWAM55QpwjnqSzpyq6lBV\r\nSVhmxSEfdhTsNDILrvcSfdCepCROkbzyv1g=\r\n=Vwom\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0-beta.4": {"name": "fast-equals", "version": "5.0.0-beta.4", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "lodash": "^4.17.21", "rollup": "^3.16.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.0", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.52.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.52.0"}, "dist": {"shasum": "19c97d1afd5ebbdd2da1b7fe0e7f5f91d03cd444", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.4.tgz", "fileCount": 59, "integrity": "sha512-6elpZmcqwT5fAE386E7VGJh1/nGjO3xfXt4uysULyaBASetrtiDrhc1nhYhnXWUYyteYqThf/ouNo8Wahqrz5w==", "signatures": [{"sig": "MEUCID7FBl+Inw7TotbQvF7283INMPIYuLRv5gbZOexN1ZGeAiEAt9ftl4ZV9NS+ZsvEHZw1CH/awR25s7GZLDfZZpN/gFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+uJeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6Pg//TD5zZac1ktmjOH0NgFk42niqWm9e2AH7MOCU3XXETUhNNxbY\r\nkJLOD5Pd1jenx4/OOeadRyJ0yCafR+k+o2YbfiZ0bA6MIO7i/VGYNDeWtToB\r\ndeF5/dphTGjIBtSpSWD9DfGMRdGk7+HmD3BmH/2UUd/jYdb5f8dcHukXUPO3\r\np5iFx67waKBR7rzypZBJKT6ZhlCGgfz0VRYEqgSjqhy5oufXCrw0BlnzC74g\r\neOULvIMSB3mz4WB/cF3ARZIGWyBgI9DuZym5J3doI02kLB7LY3ywcMSEyq5f\r\nTHEPHlZdPN07j+M3IArmN6Egurt0hOdGGRZX580aRGiwv0XukJ/9/Kp+9MRA\r\n34tkSjxpIr1+YRkAZM5UygKN/cYkGJS1QQo6cIy04iDTm9y68Qa1oZvUV978\r\n23xF0BrmAqUmWK7VQDzpjFHFhr0nYDGOd6cF9l0fBVx19NVITdi+0FDCidwq\r\nlktpEncQtwnD3EuXNMvN/45NBE5n0ND4PhDffJdl/SimW+qozsIQhI0tv0SX\r\nmwfuUsWKzEdSSL+tALpX3uRHANQ0umwotcalgX2kpHtLbLxLjWsyy01u7aOU\r\nuhVbyExImihQrbJOfjKbZ7Vtix8O1DnZeVa1XHHjHXBjbsVYQ8ElW578Wtbh\r\nWuItgL1skvGS9DKonLcWujJEqgE6qyx6zzg=\r\n=Qfxd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.0-beta.5": {"name": "fast-equals", "version": "5.0.0-beta.5", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.35.0", "lodash": "^4.17.21", "rollup": "^3.17.3", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.1", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.53.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.53.0"}, "dist": {"shasum": "eebda3749c47f8ccfcd519d5aa24df76da3d94cf", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.5.tgz", "fileCount": 59, "integrity": "sha512-QY++G5ApRLcWjUkw+KbDWtF75rlqJLkvx/pJGtIW5AMgrtEb25GKDcPjFsIyDkDIJc5Tz1+2ydyE5NCrjG1btA==", "signatures": [{"sig": "MEUCIQCWpI3Cbcp+gBGXfeaYDY9VAV22zQEXHQC+sWgw2Kd+bgIgMJ4cNLqjlBzK0EuryKmnAE7pJqqta5g6yZO+I5HEKuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+2skACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCvhAAgOxL7bRlJ7GHagtSC56H9bGB9IqThPmWAxKxlUKIOTAUBAM0\r\n6nRaVRAVpevKChUrmzMgbXVgMmYzg3n5+JOE16G2fOLOxMKuoaadjBaANCFU\r\n3tg4CbR/1GO+wKP/L4dq9r168bik8OVpJwGUTplsIVu4YhFmamTO7PsU8Z1v\r\nXpjZ4W/3pWg0IvVh8VMtWpsiKNoMPmRYqITylgW0AVhl29qMUFhdSeSOVrdg\r\nOMt97XSv7pdpaT9MUTSOeycjCqcOaneJSdeZReGUhu2oHVbedJayKLE4BpmG\r\nsnV0HdC/Cxh8zBqwqJAJDJXcpkCcpfcz5hwfkwCg9MoNt0b8q8dnZ6bQuQo7\r\nbJbn9icJ29OnlzRRHZblUsMdMBCj9r9W2feG66j0S6Px/ei60WnRRXljLYZi\r\nnDzsOynaUbavrc8Z5cdWXIzspF0lR6lz1KTb5WVQK6raflbnKVAiv5FP/MOC\r\nTmvPo8jTp5p/Gu5xJ0KtKWYa6MhswxLP/XhmvIwNFpvneHp0HXKow3XKZkyw\r\nYldWaSXos7X+CBAhOrDNl6uCJEqGmsrAy0nW0fBQW1VvvnccrIP3ybnRfFpm\r\nJYYjz4+8kq7J0NmS7iglVI08DdIBHRz7Y44mpXYbPx+Ouw3p33IJOkkzmCw2\r\nT9rSnmeNYwC+oGItrQFx1TX6/8a1YZOu0cU=\r\n=sP2I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.0-beta.6": {"name": "fast-equals", "version": "5.0.0-beta.6", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.35.0", "lodash": "^4.17.21", "rollup": "^3.17.3", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.3.1", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.6.1", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.2", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.54.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.54.0"}, "dist": {"shasum": "468f893384256d82b7ce8f3fa5e9285c69098e50", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0-beta.6.tgz", "fileCount": 59, "integrity": "sha512-WNOfmzP5T0OSAHBR+qGGya5WcrGh+gGxu2aQ1KTuxg+HLtyn0G5iUVSvVWBvlx/W6gZtpJHjlVNtXtFbHd+Ing==", "signatures": [{"sig": "MEUCIQCB1k/h5FBcBLnd4SN0+9YUJNkLk/4IXMKwvTW9p5FKpQIgZm8MPO81mmBhIc2Ryjtybv0JbGZbZlWq1OF2kpvSqh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 284639, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/gAoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocxA/8CROnDtrE4oOYUQC4rvMlfxrdkqb+x5YKWMzj4vP6O037UAc8\r\nQDvXReFPVPCWtfYwk9lWonmLYfsQLghkxc3iri4NxX2Y4CX+MIk5cHimW6qG\r\nJaQ/8toSpbMaK/XirvrhGJFcbQKgS6FX0p9pp6CuemqzxVXpns4Xu0ejwmXE\r\nTjOWGwcU1hk6H0QWFYPZuDt0foVlIJc+dvxVNJTcBRoYbSOuUKF8Ty23Zi9P\r\ns6NAyvYuaPBD3W+3DU4Ly5YwCskaCLco/95022RLmWETFR4JtNjbxnzHvZuK\r\nsJnMMJHrEoD1Jl/EDGady1EwF5CPLkepMEZmzpE0BrOWli+KoaDQ8pwcbQBl\r\nEcezoIfe5pVLjoSkky3B3s0VdnCdrVsffdrCkk4jilWyGFtFM//KOZ/eluBX\r\nrp5YGbl7IjezYHpXjDIhqACFHvnPqY2kTaPa8HNcUglIZnSe9mVm3CIs+CcR\r\nTi9vHpIYfa3TdxlkvF0L6T261xW9ewyvxBvc10AY6ttVJ+6mYkv18Pk/JWrd\r\n8znlnUjjQtUA/Xy9UHjZHZbaQoVVL1r7YVR96WgP5FkbQqUIIwm1cfJoTpdw\r\nCTDNMfWeIxaH5RmQ5eZQTSK0TR5qio0/5ooYMW9SxRgscwzdTCdSz9vTm3or\r\nCs/OZAUm+Sla7S0lJ+tiSfeNovjwvCi9b4A=\r\n=F+IV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.0": {"name": "fast-equals", "version": "5.0.0", "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.35.0", "lodash": "^4.17.21", "rollup": "^3.18.0", "ts-jest": "^29.0.3", "webpack": "^5.75.0", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.4.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.7.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.2.5", "@types/node": "^18.14.6", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.0", "webpack-dev-server": "^4.11.1", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.4.3", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.54.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.54.0"}, "dist": {"shasum": "e79b78d24404758cbce747626b3601cd06869785", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.0.tgz", "fileCount": 59, "integrity": "sha512-CjA/zqeRc1HUzFu0EIvPmcvJEQnt8+PmeeEpCKyw79ssvvGYcb70a4uSn5qb410AULdewthkKyGuz+RbjHkLfw==", "signatures": [{"sig": "MEUCICGt97hcWOlcPP7qSqwaYUsDgAbsWpqOttkOZyNacFF/AiEAij/wgBuaPe0cy7aaBlMMWqxEezL7wVQeQG4G7XroD1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBKPKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEnhAAi7cnPMoZiOR3oTPnos32aYU0+9gmsJoYdySKhgg2KKsisf+G\r\nFEJZLpTO8szRfKcWeGSB2IETULddgDuwRQTcUJY90qCnAswCTG9GyMgJnhlQ\r\n3fgC6PlByJ3s2AYzpz1yxd6XR05jHLy/YFLPJbWOZnu4svoHq+FPcGETajKG\r\nZHGKDx41YeRYAH0PJn9xr/TT2Sh2orvFBPOb+TgOGDHS/mw1IOf4egN7q7/N\r\nnXxBID3eEa78JHB1NusICo5elCZ2R4JPM2vq8dgsrQ/Nw+EYzh19COcpta37\r\nt0q2mGaNP9lTrQb5Ddvf3YMj4CZguZ0cT4XDDw1lxysGv18F5LJ8jBS96/lN\r\ntN6DRaI38MdWCWtX6a0lOlsKdxZtPrmdaqiWAOD/B74etRKbBeq0+QfCAlsr\r\nYMyZ7vw9zFSIl1c/woTV6v5R3pic/LkHcQQ2iAgu7hSUuo2FFEERiClzTaH8\r\nfXWGrD/hs8byvjnv6cJrkEF4biU/hEvmaeULsRO6SfhtNXQwo6tgMOuEnO/P\r\navjVjw+j74SPr00xxAW1R8vxzZY6NB3A3c+D7cbDN/oppw2R9I+tOYJtnkge\r\n4Fyx3Uzr50cRq3S+s7dFtdF/R8dPhnijO6poGFseo+OGXr0pYHBqEyiWeygk\r\nlwGwDZI26R1iuW3ovCE14bgmze0ylOGqmSE=\r\n=2cqy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.1": {"name": "fast-equals", "version": "5.0.1", "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "eslint": "^8.36.0", "lodash": "^4.17.21", "rollup": "^3.19.1", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.0", "prettier": "^2.8.4", "react-dom": "^18.2.0", "tinybench": "^2.4.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.9.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.15.3", "webpack-cli": "^5.0.1", "@types/ramda": "^0.28.23", "@types/react": "^18.0.28", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.0.11", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.13.0", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.2", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.0.0", "@rollup/plugin-typescript": "^11.0.0", "@typescript-eslint/parser": "^5.55.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.55.0"}, "dist": {"shasum": "a4eefe3c5d1c0d021aeed0bc10ba5e0c12ee405d", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.0.1.tgz", "fileCount": 59, "integrity": "sha512-WF1Wi8PwwSY7/6Kx0vKXtw8RwuSGoM1bvDaJbu7MxDlR1vovZjIAKrnzyrThgAjm6JDTu0fVgWXDlMGspodfoQ==", "signatures": [{"sig": "MEYCIQDxBkL/+EBSAD4SPQvsNfxZz5rRQMdEe1J5oPyJsYYqsgIhAIfJZdUjOGKc2MmNmz9tzoP8/A9I35L6t4wZACpjR7ky", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFcfjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXOw//eUXEPss5X3gWZ9L1lWuHE6ElIO2oL9zYksI9S0Ak5LI5Bgis\r\nUZNZ7CDcp4kzgGUM2Ao4AaLK8v2xp3uzcSkk1eRwDBwmehVkYh264GJ9J89s\r\nq8XwvN/I8pQyzhjIyp93QaeilHJrxyvVdIspICHGHUoDiTnExH0tDt0HF18a\r\n+9bKb0oXbhvdIR/0i3KFXxtw0ySoHH4lIGBFDf/MC4sMMI9beub4VSHW3AJR\r\nLAunLBz0Kufj2KovOQTJgCBR3/YvqE+aDZYJE/niVhDxMZsfjM1MiB2KRw0l\r\nzFrfQMNkbZC4ytQHVJ6fio8XAbK9CQ7Nqx2U9Vb+F24bpnZiOt34VtqdpiQe\r\nJ+jEhpE8PWVcxicDnnjHb0OCTX7LdPmm46GWlJQDW5USEjO/mafz5ysxj4Vo\r\nZwi/vtkbKrN4gIi3nylVJpqPM0cT4gCq48HBHUXmSsiYnoquKqqBAvVpmZCa\r\njhtLiCVltER3dcbSPJL+CzIes8h/d/SAI1QbmzbikHiD2SMo6AwTnkjKjNao\r\nFCQVtjo9n0BGilqhJbzUQsQF5p+/UYsMOIO8iMdXzPkVoAOIoKB2zUT86rD1\r\nVuVGKz8wSniFILOoY9+8toHk5dGERG0bhFvfJtEvKEimQuf07S0v/RoQXBMv\r\n5iZ3SERuz3c5+4J63y7Gfp+07Wmy0seAUKk=\r\n=QJDt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.1.0": {"name": "fast-equals", "version": "5.1.0", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "d2d0b1275055497b34986463a063a85ae3e334d9", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.1.0.tgz", "fileCount": 59, "integrity": "sha512-oi94yxvoMG04PXwi5OEDOtQ8rj3Xs+ATYBSVYZf02JYrmbqZBfWlGop2mTzGpsNkKfGKxNl7f34JzMIlogtTlQ==", "signatures": [{"sig": "MEQCIGhu71P+RTeMuuJNshYU0zBP0Om8XXD4Z7ekin/Ur9L+AiAUmIHDyH3Z7NAdPd6M4WRw3BqLP1Mq8bVuu1yt0XhuKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331927}, "engines": {"node": ">=6.0.0"}}, "5.1.1": {"name": "fast-equals", "version": "5.1.1", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^15.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "0687774377fb9bd2abcf2faa8198ec891adf12f2", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.1.1.tgz", "fileCount": 59, "integrity": "sha512-LqY4tPGSpgYooD6B5sKRxtB0Fn7vt+UFxGJzrk5XM6UVGExQcS3v1QuFuDWG1CFIKt7xnitxM8zeDgVrXP4d0g==", "signatures": [{"sig": "MEUCIDI+8kv5wJdzFJhNxlInZ9ubKylXWlEZ7deTuBkPTLgSAiEAp/uvWBTwlyMeY28dZCOSjRy4hNBiaerICj7Q4uWsIPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331973}, "engines": {"node": ">=6.0.0"}}, "5.1.2": {"name": "fast-equals", "version": "5.1.2", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "2995402edd1be5f13c6f1782579438afa0d6dd80", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.1.2.tgz", "fileCount": 59, "integrity": "sha512-PYfM+Owz8Trdw7HOEJbht7O00mdT4TzGdBBioMihoiqFnh2bZpJI19yvMdx78YySYl61qCibgD/LN6lyhLV93A==", "signatures": [{"sig": "MEYCIQDkkCUZMcCHAOXxDFCHsvNXhpx3DyoaCSLKx+qaXm4YuwIhAJELEb7OoqQNob/vycLXR8R+oMNmdvWBP2HPn6hJww5x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331970}, "engines": {"node": ">=6.0.0"}}, "5.1.3": {"name": "fast-equals", "version": "5.1.3", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "dequal": "^2.0.3", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "3025d4b4e8f3cc611096842004810cf95171a39f", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.1.3.tgz", "fileCount": 59, "integrity": "sha512-6117/nJPFyrTjoCBQI7lpRFf+Oda4mH8HtlNMi28os+URb7MQU/dXUTrKhA2KR4G0O1MCfdi/KExIVEmzEh3qA==", "signatures": [{"sig": "MEUCIQCreAnXVf9QhhZeRiScbXq3ti6jqdZN3oO+7ntp19qFGAIgdJimJwxsDptYTm3c8rGXAX7/E+Y/g4cbwHwOxEnO8kY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335802}, "engines": {"node": ">=6.0.0"}}, "5.2.0": {"name": "fast-equals", "version": "5.2.0", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "dequal": "^2.0.3", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "db3b56891e7210af26b4d6e6b17032554d7f7f1e", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.0.tgz", "fileCount": 59, "integrity": "sha512-3VpaQYf+CDFdRQfgsb+3vY7XaKjM35WCMoQTTE8h4S/eUkHzyJFOOA/gATYgoLejy4FBrEQD/sXe5Auk4cW/AQ==", "signatures": [{"sig": "MEUCIQCgVkFHNNHQBD8PHkmG7WKipGwX3eAWncO3hfLTvBMkqQIgf9qrrmVS/Y3I7Xmjk8clJfhL78oLG3W9u7HtB5C9qDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334990}, "engines": {"node": ">=6.0.0"}}, "5.2.1-beta.0": {"name": "fast-equals", "version": "5.2.1-beta.0", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "dequal": "^2.0.3", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "df97a08ee512a973e22349b8f0b180e74f81e3a9", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.1-beta.0.tgz", "fileCount": 60, "integrity": "sha512-Zwty0l+3HPYmUTtREn+T1d1W6+v1q1/+a4jzs0eIUJTf2UpJAsaC0CDdaTYKcmhsVxDhyM/AVCKNYw9g9hgTKA==", "signatures": [{"sig": "MEUCIDvXy+Tfd0Zc/5zpt75Ld3js2iL8jf95DaQaFvhkcAWpAiEAsPSNJzSyB9UgmJ+Oz106X0VjQacbGuc3yfIxMcqsn3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343019}, "engines": {"node": ">=6.0.0"}}, "5.2.1": {"name": "fast-equals", "version": "5.2.1", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "dequal": "^2.0.3", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "49a956db6246fe14099b8f1e30183a9c3eb5dae2", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.1.tgz", "fileCount": 60, "integrity": "sha512-4DpZF7SS4GQO08ScYvTMuZSRik6Y+46ByOJOZR3yKjE69rooHcHB/UsO89qJyIlyvlya38296vypgnInFhRePA==", "signatures": [{"sig": "MEUCIQCj/QiDQIrYKKffSMpyViSQkRbnYjOAMWMKioQeVTfaxgIgEv68tyhMBIU+FbMQ1tTJFldrgYaCdUBxEjOC9HDgLv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343183}, "engines": {"node": ">=6.0.0"}}, "5.2.2-beta.0": {"name": "fast-equals", "version": "5.2.2-beta.0", "devDependencies": {"jest": "^29.5.0", "react": "^18.3.1", "dequal": "^2.0.3", "eslint": "^8.57.1", "lodash": "^4.17.21", "rollup": "^3.29.5", "ts-jest": "^29.0.3", "webpack": "^5.76.2", "deep-eql": "^4.1.4", "prettier": "^2.8.8", "fast-glob": "^3.3.3", "react-dom": "^18.3.1", "tinybench": "^2.9.0", "ts-loader": "^9.4.2", "deep-equal": "^2.0.5", "in-publish": "^2.0.0", "nano-equal": "^2.0.2", "release-it": "^17.11.0", "typescript": "^4.9.5", "underscore": "^1.13.4", "@types/jest": "^29.5.0", "@types/node": "^18.19.69", "webpack-cli": "^5.1.4", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/lodash": "^4.14.184", "decircularize": "^1.0.0", "fast-deep-equal": "^3.1.3", "@types/react-dom": "^18.3.5", "react-fast-compare": "^3.2.1", "webpack-dev-server": "^4.15.2", "html-webpack-plugin": "^5.5.0", "jest-expect-message": "^1.1.3", "shallow-equal-fuzzy": "^0.0.2", "@rollup/plugin-terser": "^0.4.0", "eslint-webpack-plugin": "^4.0.0", "@rollup/plugin-replace": "^5.0.7", "jest-environment-jsdom": "^29.5.0", "@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-typescript": "^11.1.6", "@typescript-eslint/parser": "^5.62.0", "eslint-friendly-formatter": "^4.0.1", "@rollup/plugin-node-resolve": "^15.3.1", "@typescript-eslint/eslint-plugin": "^5.62.0"}, "dist": {"shasum": "bb5f14dbf4dc72c0d946d5f70d82545df594fbb6", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2-beta.0.tgz", "fileCount": 61, "integrity": "sha512-mQk7wsjSrDzrqMUALA6B6o3Ac/wj0hsNpm2hf5XnjpkJBI/amOj75qWZotyMaRQnPkUKEuqGL5hzlWkmWt3GJw==", "signatures": [{"sig": "MEQCIGh2g0JRoFBqmA9MILHjjtO5sWzmAuWqZX3w7tkoTUDDAiAdXl88yhitY4pO7ip/KRH7XJcnrplM/H4R/F5rY/R51Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 344552}, "engines": {"node": ">=6.0.0"}}, "5.2.2": {"name": "fast-equals", "version": "5.2.2", "devDependencies": {"@rollup/plugin-commonjs": "^24.1.0", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.1.6", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.184", "@types/node": "^18.19.69", "@types/ramda": "^0.28.25", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "decircularize": "^1.0.0", "deep-eql": "^4.1.4", "deep-equal": "^2.0.5", "dequal": "^2.0.3", "eslint": "^8.57.1", "eslint-friendly-formatter": "^4.0.1", "eslint-webpack-plugin": "^4.0.0", "fast-deep-equal": "^3.1.3", "fast-glob": "^3.3.3", "html-webpack-plugin": "^5.5.0", "in-publish": "^2.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-expect-message": "^1.1.3", "lodash": "^4.17.21", "nano-equal": "^2.0.2", "prettier": "^2.8.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-fast-compare": "^3.2.1", "release-it": "^17.11.0", "rollup": "^3.29.5", "shallow-equal-fuzzy": "^0.0.2", "tinybench": "^2.9.0", "ts-jest": "^29.0.3", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "underscore": "^1.13.4", "webpack": "^5.76.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2"}, "dist": {"integrity": "sha512-V7/RktU11J3I36Nwq2JnZEM7tNm17eBJz+u25qdxBZeCKiX6BkVSZQjwWIr+IobgnZy+ag73tTZgZi7tr0LrBw==", "shasum": "885d7bfb079fac0ce0e8450374bce29e9b742484", "tarball": "https://registry.npmjs.org/fast-equals/-/fast-equals-5.2.2.tgz", "fileCount": 61, "unpackedSize": 344738, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDKur2Q3YfhYV1338+LrZZBNndow2vLzCWZ/W5CrlVeMAiA8yg/b8gmX6g2jcA8/dgUP4R/WsT8a6p/9FW6VCczzIg=="}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2025-01-08T13:45:39.321Z", "cachedAt": 1747660590865}