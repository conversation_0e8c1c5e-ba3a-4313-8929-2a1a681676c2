{"name": "postcss-value-parser", "dist-tags": {"latest": "4.2.0"}, "versions": {"1.0.0": {"name": "postcss-value-parser", "version": "1.0.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "3f731d9b3a4ee71c9446c0f382ceeb61043cad3b", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.0.0.tgz", "integrity": "sha512-vZPeEVwnGcK4qEMLUiXMvgssu1DYoVvAq1LrwPFrDrbdXLHhO/EW0NkFWxHfzq2eDA2RDOs52g2gAsK1wBcQtg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCobi7DcuGAudV7CFR3Z6yaP5SWSv6sRGZEKD6Uxj6DNQIhAOt1zGNjPVn5g+OL4NK+bi2QHosBD2RRLOmvbQy2o0zp"}]}}, "1.0.1": {"name": "postcss-value-parser", "version": "1.0.1", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "6e27cb230df739a9738531f061de274740b943ff", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.0.1.tgz", "integrity": "sha512-2/V8oABuLbkboGaEiqC8Oo1v77NUjJNfsdb1zo/wtkc4QiFxbnUfvis/a2i3bv+eaF85dciFnfnhzM4ZfdQwAA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHMb0bseruzkJN7gzjTehpdMg8PWIApQuHdykTu3ows+AiBlx7rjFtz4QlesbMN4q0S1Oef96lm49w8xqJXU3bxxzQ=="}]}}, "1.0.2": {"name": "postcss-value-parser", "version": "1.0.2", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "600e80e6bac96e2b955e62ce39a03cabe594bb80", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.0.2.tgz", "integrity": "sha512-Zon9T/U6kvXVE77vvZcrHrbWvf1+p0een2VrnVHbkCqWRHDwvss22SGWlbF3S0wf9Sngky+v6a2PzlSkMCE69A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEgWb8IFTJPBIBi3u7VufObVRnXgDaPrvvTjEBdX3KArAiEA8fjaXM+CtCz84Idvk8WYXUtdVIfY/phZL366ES3vPCw="}]}}, "1.1.0": {"name": "postcss-value-parser", "version": "1.1.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "7c44ffbb7ba9dcb7057cdb0c2c85ca4391e8a8b7", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.1.0.tgz", "integrity": "sha512-pT3MnEGE4oQ12KHktulIFrazUTJzaJCb6AkSYUuyVOWzwZt+ED1yWm+TdLL6rmpbjNXQ0O+sxMaF+yMQ3rBaBg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICtr86MCnw0RqqMRfJsy2nSzD6jdj7rjqHuL4Zd3iOusAiAzBq2JPwPZbWf54K2BVxx4ZfebfD4ppPop1at+XWkQAw=="}]}}, "1.2.0": {"name": "postcss-value-parser", "version": "1.2.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "ce75473bc0f80aa92d9b7f7ca5d08eb6897cf6f8", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.2.0.tgz", "integrity": "sha512-MlwPQL98XTSswU+l9nzVqubQ9t2oDItAp/x7ouwA2S+Q/JMnnYzDGxOXrcLAqhd+83JSO3LlwucJmB8LTC+FfA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEHxFD/9u7rHuIcSyKvci4WDUS4ENjdLSQLpMZdgUFktAiANq5983LWWB18x1tu4ZCEkoHx7x1Jj/K5oMVcwdY74eQ=="}]}}, "1.2.1": {"name": "postcss-value-parser", "version": "1.2.1", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "bb062557972eabb0083f4b8a0eebb1d0acf7aed2", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.2.1.tgz", "integrity": "sha512-YePgR8IVCSOo5S2NUWqwz2vGEjJP9ifqB6r1tRT3oiHMpMWUTih9VzMgGkKZmU0NPfFvEaH0Aoh9Lx2b9mOELw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDEi2Ho57xPG36QUkOhCfpabMC3uh/WqkqDDxdxwh2S2QIgQ9GmMiGfqFzIgGurIhnnlz/CvWxEhs4Tu+AORgrkpWY="}]}}, "1.2.2": {"name": "postcss-value-parser", "version": "1.2.2", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "efaa0539ff5cbc9da44e5daadc13da66564beb9e", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.2.2.tgz", "integrity": "sha512-qBfWd83rOd+dpbBPevSu3G2ESnKD2Y5hHM9djBtpXRp1EuUnUgKzZQGZG3Twonnhmx9y7h21h5qIJ2E24yCJ7Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCThHcOhBOzWyb/2LTp6OPFpFOovcmLPH0ySTtmOtrYDAIgYy5/toNDs6D/cpFJhOZztCU056h1/iJo3O14i2+5IMw="}]}}, "1.3.0": {"name": "postcss-value-parser", "version": "1.3.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "18f83ba9c0bc8170e998c11342034f03d361a6b5", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.3.0.tgz", "integrity": "sha512-prAYTC+dwQAVnnYq11gth36apIG9g3W6bf4Z+U0uFHlXyh8rOUWFznjBkdUT9CEBLmShIf26TEC1zkKtmk314A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEQFjNAuK/jiVyyr15TftPn+h4ddrMGXknpFKL2AuELMAiEAuGGjUZPdzSTuS1U8V89Fk/lsdkt3rUUjWqAg+U+KDEU="}]}}, "1.4.0": {"name": "postcss-value-parser", "version": "1.4.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "34809504c2db54fa7613f3e357041ec1ebbf48e5", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.4.0.tgz", "integrity": "sha512-wYXrHH15Hf4qkW411hcCQjsn7zhZ6vfNxm6HPoRZpB3y9aqr26RU2OBfFjnvfQEOdK2goxMUWKROpD0Qlzn+tA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCMgfiFexXVuTspj+hEB/rtbz2JRUfug5fsdaiWghK+gIhAINCQGNmssNpnF2S/XnJ1mrEobTvYuQRW456M3NVv4As"}]}}, "1.4.1": {"name": "postcss-value-parser", "version": "1.4.1", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "00f1e1a02a82c65fdf47f12eaeed23d03b573854", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.4.1.tgz", "integrity": "sha512-ApqnUgieovv4wcBV6slyxLMdcNTau5Xnikc3v1gWqqhwyaerWjFCPxXsuLbP/6S4HA63zzI5a+9cF7KovkRtSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDO0x5+XfJXGQ/OgsNq/ZbluWoz4KeizrSaYrl4uFKvdQIhAPpTruYsWJSrBAr1xWZHqeQ+fNakV40bGEMveqp90VOZ"}]}}, "1.4.2": {"name": "postcss-value-parser", "version": "1.4.2", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "1865633e13701f8a721e7834dad185cb144aad0c", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-1.4.2.tgz", "integrity": "sha512-22vc2Fs4uAZTNRbQ9M7yU877FYXkKIq7aaTgm0gg+Q9Yj+SkcMVYjZ/hJ3adaUBJlZDz2dGLcjHzSt13mAMh+Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDiEBfsoq1FAJHlgJQ59fkX9vgns/MCT1I+PRa9lXr7mgIhALq9A/VMmFCKqV71oqQnTouIKaYG6UsYX3ZH29IShRUt"}]}}, "2.0.0": {"name": "postcss-value-parser", "version": "2.0.0", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "acf509813bda64fe5e65d4a8e86adb2c7339fd62", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.0.tgz", "integrity": "sha512-tum/zdd7C7F7QFGTRTJb3DgnXJU7HsivotTIjRuIHFwBDLF4IIfHMf/aUz0EZNM3WM9JdB/Vuh2u0FJDr97pgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUnw8/sqeNbAqcaMzMzLx/e0OtqDmdV7eIj5Tv5m9uTgIhAJe/v3pXt6r78OHIETT9lQB+U3A0j+uxzd+SoSkg/cNx"}]}}, "2.0.1": {"name": "postcss-value-parser", "version": "2.0.1", "devDependencies": {"eslint": "^1.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "9b6c2da052b177a7b1a687aa5cdd9e5490194cfd", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.1.tgz", "integrity": "sha512-4X8zvZ8hpVl0bo/BJ9EpO0zTOLIbET2zOYeTjUoQhnwrM50A5TuIUhInN42WkSIk83RsHwc3PCd1brl6Hg7XSQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICY2hpfbeglw9S3aeEX6IBgYzqHqh5CwWZwPVXzv5KXVAiAxOtw3YlDcG6m7A/pEMhwI57fe9krP6EVH79oP91u8xQ=="}]}}, "2.0.2": {"name": "postcss-value-parser", "version": "2.0.2", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "f8a86f253a4531b572515599fa96a7e40336a5d8", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.2.tgz", "integrity": "sha512-pBl2xgv9DMj1mSN2rdVaz/CvdcfpD5BJRV/NBP7p482uH90Hfns5Y18dH9EJL1WdPtZd9CMm3uq1ymxRngPGZA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB0ZFDq6eCbJfUl0ExntmtX/KoF1ntMHTKhJ7dA+Db29AiBxdzXdziJnohI8et6tkxBMGq5Gl5XXaO1QxE3QRWs+VA=="}]}}, "2.0.3": {"name": "postcss-value-parser", "version": "2.0.3", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "c7ff20a698fa4803e84012146e5ca2eaf9d5a424", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.3.tgz", "integrity": "sha512-hIAIjH+gVS4nz6PK9fl5UgYhv0F2CjXOCOCFjSeAIOVUAeL9iGn2sK30lu6Bq3PT6fFCXZbffEStbicBrzLmjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC49isEUHzJTkIIhPiMAnqFzB65dDxYUTYgDqUxulhKcAiAg3DBFAhHXwZmPFfeH7bhxNWLdGU6YBxPxdlxi0l85ag=="}]}}, "2.0.4": {"name": "postcss-value-parser", "version": "2.0.4", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "60aa7d8ba13162b3fcf8a90a77ec4519a1d0106d", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.4.tgz", "integrity": "sha512-xBd7aXxeskK+8pR+XcrcmJucUxs2bHQ0B8/bmW96HJMKwaTUAMdbO9cJngvkUIoT5maEzOZUA9TN+IWorx7OOA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAgRIALAIBDlp5nuC3yc8BeEmvFux3HZF0Ko3HuBcjpWAiBTPwG25yRAY35TG/9LzgcJgKHxbNqQohe9Os2/+jhQeA=="}]}}, "2.0.5": {"name": "postcss-value-parser", "version": "2.0.5", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "9bcd9234d0f16cd57508f2b488ed533f3684317e", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-2.0.5.tgz", "integrity": "sha512-OnhSghF+eFkwdB2r9J2O5t859gVdSYVXPFk6WhG9TVdiikcnaZ/cURcfxnJzK4nr2JDi8DA8TQ+x7i9DdOIu7g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFJEY7FwT7GAJqjcgaqMA9iS7iVw72J3xDK4kOUGxmEgAiBWduEdKhK3y0s1sq17Y0HWylSdNARyTKXlZl6K6LKGkw=="}]}}, "3.0.0": {"name": "postcss-value-parser", "version": "3.0.0", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "0456648acc0dec7964b8dc202d1490bef51e45d1", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.0.0.tgz", "integrity": "sha512-SAccALes4kjKmhl1HpKOK6TVOIBWGTFJJTYLhp/bgHgdxK9WyXBhVwtfC4eJXVjWsxFe6c1QAOH0sY+9VC+OuA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCE9n8TobiDZgKlFXpgTqJOeFu3TkTv8sxs0QdWf/3K6AIhAPTEEkVodfgcRQyGCrzB1d4L9v9YOeXBT9WQ7qdEbqQN"}]}}, "3.0.1": {"name": "postcss-value-parser", "version": "3.0.1", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "ee8fe1cdeb5dc72db9320592e8e43caf900557d3", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.0.1.tgz", "integrity": "sha512-O0zaDeWEeDpsmsKpEAccu37dAqWPMkGXtBtN4VLuefJlk0lVxLEzBmWOnCUczPiSzkAVtlgqSNY6oQsnrbl4hw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZa8Flsly5FMP/5s/xtqjJMMvnsVv7KtXiylD+/bqUDAIhAOBD5Q5Av8cNIcectCDm3dR2WHkalJK1BRhL/2ypRPXZ"}]}}, "3.0.2": {"name": "postcss-value-parser", "version": "3.0.2", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "84e12e2aa9305ee8db77b02b47c31019493ffc48", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.0.2.tgz", "integrity": "sha512-rPdmInXDUeUx4DBuZOKJREiMAmrA/p1YidfegrsxC9QyMnrxVasTWuinJVst4gkoxpfNzOMhNBoIy8cwzgRk4Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0Le3PiyOPCaTDmtxJEmg7G/+tnBJNJAyx+TMzp2SlGQIhAOUlcBaPYL+cZzlDZVHf8OlPRBuR8GtPKMpZ37rQsZkM"}]}}, "3.0.3": {"name": "postcss-value-parser", "version": "3.0.3", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "58fa19a370cce59d220e58320ead9be93f3ccd7d", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.0.3.tgz", "integrity": "sha512-GPEtpPzW7bigrK/lMEtntK7DTkb2O3ejzXI6rW2OV+lTzVt73PwAjEZgeqFUuptIpyc9ND9W50WvcGxslxzKXA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWTIOKx/pe4eE0Fh2YTS6SAntzkQkbuGHWBRyt/7m/9gIhALjjst0DviACnNbMhlsVZlj0in5uHD84eX44ofV9qotm"}]}}, "3.1.0": {"name": "postcss-value-parser", "version": "3.1.0", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "a3a88d9fa68b529cc08652407a00766c11fa00b4", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.1.0.tgz", "integrity": "sha512-<PERSON><PERSON>gs5yOxnoh2t3jtXYEfSVG5EkxaxlQl9SlCS7EtVuEYn8DDbeseIZE1wvazJWzfShXFS9Fg2LwAY2FwoT3w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6BMZUtnL5zboJePkiMmX0RcCoBpNeQG8XT8nJ2b69zgIgYbn0APm/7PTaP6xW27OPj1adXjp9wBEh62ni4wgcYfs="}]}}, "3.1.1": {"name": "postcss-value-parser", "version": "3.1.1", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "274968355c74c06e8af43312f8dd89c2a5eeb905", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.1.1.tgz", "integrity": "sha512-Dn4SDTUeoYP7PROUjumLqAJbCPscGLp9thdowTy2KcSev7g5+JLr6urREHZpoIBqAeBLrl1ObHwkn/GjsLYdtQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA4cuqtGWG5G/ADLJQ4yQcTxwImvd18EpHqW2+AYVHEfAiBCGQhGJ55OgySf+MO8E7hSAETl0rVCV08CyMqQ6vJKQw=="}]}}, "3.1.2": {"name": "postcss-value-parser", "version": "3.1.2", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "5ec98f70548b8158b3b8f9b5c13a2bac46fb1837", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.1.2.tgz", "integrity": "sha512-vZRbCHeaGv1F1n5YD3vmxEHcIQ9bAhozhbKpxWbpFVI1Jwk1iv/N5LWSuDanKKUsoqPFqynRXmHkT6UTSvbyWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC9SiPU4AVJlUnN/Vl5F/qdDviIvG+jJXEuB20TyfvbtAiALdkdvgnYd2UAhFGXMy8AdAMZzXgcbrNQ4+ooUTjLGJg=="}]}}, "3.1.3": {"name": "postcss-value-parser", "version": "3.1.3", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "dba3624a7b9fd9e828a5c256e08a2f26134f7191", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.1.3.tgz", "integrity": "sha512-wcTl6oqnuZMUDDLDb9Ax2c0XGsPkj8aK4FnP7IEGI4sMFXGJdOAUzHYW0UsdCcIIu1kpxwg8oVy6Cp+mPwkCrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDpN30mkO6ExJavW3ANObWQja8xRkPc8yRRvViwEULjPwIgBSw8wGuWaZ3xe7MmThI96nD1eUGIEfoOybC0+fQAIOU="}]}}, "3.2.0": {"name": "postcss-value-parser", "version": "3.2.0", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "21f4b04a74bc5198ff05e0c7f077c338aff23c2f", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.2.0.tgz", "integrity": "sha512-Xiw<PERSON>hEjkQbUmatEI6zPXP7l8V9c0IPD8y9mSCT4+ekEvvkYw4GjEYRi0UQL2I8WL1PAF9dcxVv620GWbs4Y/sA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAk2Qr8DWGSh5dUohTl1FEr0w9M0cBRiq7zpGXdAXpAFAiBLur9nwKIgIDYqwwkpP/0eXC+trxuPeY75DBLe17HMrA=="}]}}, "3.2.1": {"name": "postcss-value-parser", "version": "3.2.1", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "5ae3681946135b5ef6bac55c5ee980f6574acc4f", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.2.1.tgz", "integrity": "sha512-PIshwBRtkFWWmO3kPGwx5aGGZ1M05VwhWaOxnUrCLKCzaxo0F8NrTlhK+ytTGpYzQoFxXMGIFnzGpBA6Nvfvog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCwqKba2upiarZw+H6Y0Gk18V7qhr5UpTLgvvY4ajZsgwIgZFqkaar72TzKerXWOHYN7JMbJIgrxhzyo+/aE55u1QI="}]}}, "3.2.3": {"name": "postcss-value-parser", "version": "3.2.3", "devDependencies": {"eslint": "^1.1.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "216e7247bbd26b7668ab9eebd08de6b96eb2b453", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.2.3.tgz", "integrity": "sha512-u1MJTOIHkKNJxBHrcxSPNPc9964dk3LAtRApAZsee7Ci+mpez42ESZyVsYJRy6f4MA4TNehDNPFkcGzbtz1QCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIGfSS9z8TC6Yl5MbkkUUyiULnL0/pWQ4ZT9P3uaMUiwIgDl8e3pva4pAo0S4H6CGPYyBTmQQjQYoQt0YUEJpu33g="}]}}, "3.3.0": {"name": "postcss-value-parser", "version": "3.3.0", "devDependencies": {"eslint": "^2.1.0", "eslint-config-postcss": "^2.0.0", "tap-spec": "^4.1.0", "tape": "^4.2.0"}, "dist": {"shasum": "87f38f9f18f774a4ab4c8a232f5c5ce8872a9d15", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.0.tgz", "integrity": "sha512-3gxd4RJ4x8StdKO7ETg4JrS6jsgWNFV83jZeievoisuPMBT8oCjZGCtFnoZpve9NCS27r+UtCYEDn4QVbhPHrg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC83bSCJKh3HXaTNh/kGR21pqNQP/HouHvwb8IEi506JwIgHQgfYZ/+zUEDeO7/lFXvxYCzKxA4UGaG+MtUpwrEIS8="}]}}, "3.3.1": {"name": "postcss-value-parser", "version": "3.3.1", "devDependencies": {"eslint": "^5.6.1", "husky": "^1.0.0", "lint-staged": "^7.3.0", "prettier": "^1.4.4", "tap-spec": "^5.0.0", "tape": "^4.2.0"}, "dist": {"integrity": "sha512-pISE66AbVkp4fDQ7VHBwRNXzAAKJjw4Vw7nWI/+Q3vuly7SNfgYXvm6i5IgFylHGK5sP/xHAbB7N49OS4gWNyQ==", "shasum": "9ff822547e2893213cf1c30efa51ac5fd1ba8281", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-3.3.1.tgz", "fileCount": 8, "unpackedSize": 19086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbwPZkCRA9TVsSAnZWagAA0/kP/RkicD0Fzop0+T7UKLqM\nF9O/SJGaW5sx+V3H0Ix1y43H4haecMjYVaXt9HMzOGTZo9G6xidxSKSYceGi\nvu9kyfY/dNrhnhU/OVRZfrqDbKi4eZ3uTygMBQoZ8tkL6+K9FGAQ/S3TyMkg\nKwTPrq9ByR1C4BNtFyUTPcTZ4KKWbM7FAqLTWgGo40RDCn18k7H3m4C4rzkr\nNp7Mz3IXD52/4tuAiyE0O6J5ubQ8sqobArsq+JPp+14HIBKC+5Jn2unic98C\nKgHmpHGJsxMFBNCKmBHP/reoi+kBQGHTBFK8X/C+T5lAVnaSkB5M2s3HAczj\n9kD09N81ClPdTLyCsHZe2dVyPfSTFkSdWQcHIMyCzYxGU2t4ORMo+3o1cBtE\noTCvOmy04IMnuaEDXsewwzlV8QZdcxTBsMzyHVtTTBbsMojgJGbLFhLzY2Tf\n0f0v5k93OEsjdXwuq8637MoTgbuzXXvc/aov1vBLNn8+vJ341mjxXWb21vOZ\nTSQ86XiaudjK8snFAv1nqOzLyp83A4z3mxuBLi/drpdtomsbCe/aY60UjpfB\nUZ+aFsIcg3kKLTbUbZokhQ2+pXbbyTqhsKIt4wo+mYVT7DB+AdoZVVIHO0Ej\nJw7tIBk6/gEtGoTJezkjRI6AGNPCsrB6OOk1wSNfMXeNFyStY7NrZCGaCenI\nRl+F\r\n=Z2iA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEmKTr1+TcDWy9dKixf8X0of1C51XQsz4J+kmuZCie+BAiBTd7quiuEVvUJndytJwwTiVADpOBLKdaBjjpGnHBURnQ=="}]}}, "4.0.0": {"name": "postcss-value-parser", "version": "4.0.0", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-ESPktioptiSUchCKgggAkzdmkgzKfmp0EU8jXH+5kbIUB+unr0Y4CY9SRMvibuvYUBjNh1ACLbxqYNpdTQOteQ==", "shasum": "99a983d365f7b2ad8d0f9b8c3094926eab4b936d", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.0.tgz", "fileCount": 8, "unpackedSize": 20716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9kJxCRA9TVsSAnZWagAAYWYQAKFASBhsxCznejLtWR6e\nWFSEolQo1HWgpIhEespud5F+hJTq4QHlBiDhwU4VgFXxkokP2/0Mv2N4GIg+\nieTXYzHyOb1QZVYH+ExfX2h5mz/KhRgf1M/Z38m6ECbjY3ZNbUuBsGt/cnN7\nfQzgBKRzCkLIqebqKgkA1SmqlYuFo7rlUDXoUeVvgcKKuWNpXFtQDFpaPjNC\nrXLFQosTJm3xqDGrn5JT4N6rtf9PVYpVvBsfZx1AQmjkeDIi6gYIL1g1qGW7\nWv7Uz8MY6/yHYMecHwagG3DoDqYUo76VqhKJiYCOISbXjB7kroinPccNJEXM\nvG9AXNa2yoBGmkXPovnD/YWD5/4gFhNHczKeygP0Mvn7cDL/uCLXGjNggUFr\nUZk4vZuDVC8pKWPDUdiya/j8Y0+Z5HiQUmAAVWjl7A1jjdPbTcb6B6IhEyvW\nG45T4xuNmOZ3UefCztMT5w8U13WbrPjh0rym5MtPsk6mia1GtRmWPR+uiYPh\nJ09dyYDFvXMhFMp2xxqdySRvZYV66UhLCL8OvLbyo69BfkRRYB4WKkySFOyO\nV/XjMAuHbC6wtWwaIOQsYmva6RIouRkJ7Kq+GHhedAwmJIgvFHrL81ggyymy\nXEiDaSwjUdB8mpBZXOndpFpXlsDPyAxBQc+kh90Pjlfch1Lt+sxESf7aH1TL\nOvLS\r\n=W3Va\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7bLC30zMHoFDXrYjUH5xJ3d1BcAl1SuniiLagQZ38SgIgRlQpyERoLsfU0/Lb9zdkFPY4onHJZflRayBSNWIkFbE="}]}}, "4.0.1": {"name": "postcss-value-parser", "version": "4.0.1", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-3Jk+/CVH0HBfgSSFWALKm9Hyzf4kumPjZfUxkRYZNcqFztELb2APKxv0nlX8HCdc1/ymePmT/nFf1ST6fjWH2A==", "shasum": "e3f6172cc91302912c89da55a42454025485250f", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.1.tgz", "fileCount": 9, "unpackedSize": 25424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSGfmCRA9TVsSAnZWagAAzlYP/j4b0rsy0hwj+0BD97NB\n0DHzFZQb+l2pZAv48lwOqC4wv8bsTSM7iYov2ZUdy0F+g68HRJZMh5WXafii\nDezzLCjrCKgddh0Mr0l6pL/92E2iZxB17Ac4ubtxCWSQcqlAw6e8XhZlFuVk\nEmVIXaoWPNGNnveMBD7wRSnozANuz40I8K5Bw/dWHtAUPge/q6mGV5oXPgCu\nRM4+oe473Ng++sS6a6e53mY/BT0PDbuGoGexuWc24rJFgX44q+2Pb8zAdzZg\nIb4IQCltm5BodsKqTKf90jHTeEIfN0maSPKTylMIPV8FHin7LMzlTqUV3fJO\ntsDX+KSgO9dsDAvynGdvFUFmX2dhinpy+JfeLKYWUBiruZxKhuZlbddSB26k\nhQFhf6BGkPl8XDe36DTFGsp015Q+Qzkk/SMKL8TE+xqsX/vH1MA9Vn9B9EaX\nGMUAM2XIeHrxOvwAvrpfRTxv7PiRZ/HStmaedH5gWkNK6BfD2pMN3pbCNh7o\ngPScRcffwVpMZgjPXSr2U/YzePgjH7F80nHvNNKXZ83YkpqpiQnJl24VwCXF\nBRXfcQY+2a5K20yc0r4evLkN5GkHoxEK7XI9dbsONJzVwqLR+dgdmgddC4On\nGEDDt6oIYfiVQ9BirbxdWjS9VKbnEc1WBFxI1KMLf87qjJ5GuTqBstGDCa31\nZyH7\r\n=u2ZI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDfJCbJ3hJGgqdZQ5LQlO+aQupTPa5z941uXmO6nO0YRAiAtKbCJ61jCPos1pfxslNiq2TUSwMdQ+2WDNqZP/4XLuQ=="}]}}, "4.0.2": {"name": "postcss-value-parser", "version": "4.0.2", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-LmeoohTpp/K4UiyQCwuGWlONxXamGzCMtFxLq4W1nZVGIQLYvMCJx3yAF9qyyuFpflABI9yVdtJAqbihOsCsJQ==", "shasum": "482282c09a42706d1fc9a069b73f44ec08391dc9", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.2.tgz", "fileCount": 9, "unpackedSize": 26281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSxr5CRA9TVsSAnZWagAAByUP+gMpKn2kMsFInpLr49fJ\nvl6GEx2MUEtXiRWLS4lXMHUJQMc85R/EGCK7LDAr22pfPgfYz6VXVoD64ewg\n6ju+H4BXVnmM+0jwy+HKeSBIhuKjyU0vWOJ06uI7LMJN/CmV7w/HDZQN+woq\nEFxShpQYZ8RhRdbp9OtIg3E1zDGng2gw2N29hfYfrZtc9ElNbdu6KQ8Hg3nZ\nT9n1ikN711drpSh8+FZmT0S6RNRyI//6J2+pVfULECRgvKOz3OidO2mgbF/A\nsrpHP5yyBxki/l87ZtUEo6AKg+fJaggGge0hUAt3kkCJGMh5VcgMRtdq3lFI\nh77qJ1wN6pSf7s1cbzVWPvJfDdRPyBdPSDjHasrhdvM+MpR/gXxiZEsImRUC\nEaTOaBjj91nd7XXfGi0vbjrlCsZJJpjY/3XAQoosVVkpXBPU2PPYnHqCKZAT\nCwKwoMKfsNkskkKH1eFpeqQuk/e4L7cdtBRku6QqHDt6iUI+yS4lZRjU0z8R\n585OXemx4yFfzULp7EXFxcpS5MJEGpqtoATuMUwT180V2pwhV8irQKmqUDdL\nBDkXyQ+fXqlvMfC+L0pzQU52aOz/rlqAMEbG1/mJ4Ty4eV4dbigcg/BxjOtC\nycAplMvcECj7iaaiXpWo31ObiDmuNac3thJvqEs+1AoXIyeIMeO/AqGKc4B6\nKPIN\r\n=fnUl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGJy1chcjenFgirPZ348fXHY6Og3+jSnbN/0Ro5Oz/3iAiB8ZQ1xKu/rMWEtT0NXeeWh21zlGIn/4FL3Uf34jrpwlw=="}]}}, "4.0.3": {"name": "postcss-value-parser", "version": "4.0.3", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-N7h4pG+Nnu5BEIzyeaaIYWs0LI5XC40OrRh5L60z0QjFsqGWcHcbkBvpe1WYpcIS9yQ8sOi/vIPt1ejQCrMVrg==", "shasum": "651ff4593aa9eda8d5d0d66593a2417aeaeb325d", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.0.3.tgz", "fileCount": 9, "unpackedSize": 26288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS/v0CRA9TVsSAnZWagAAUeYP/iHMQeHpU+F3cqZ38HB5\ni/LPVzUzICdzmv9Twqh35qwzuwhOoSfA2N20ffQXVB6B8S7VN57dbLibJ+EF\nEL09K+mcOoqsCZbf8mwmADXOj6LPScGF9/nsR4UdJz5l4x4SVz8x4ijtEI8x\nq4KYRRMQ9xZ392BEADgRPKf3NLg8DZnJSkFj/szDGV5rXn4S08P1qpcWR8Be\noHiUDIIm4NM74yKZL48aQJadqtuhtRConxom8bjUsCYXlXniiRJsvfE5zwJE\nQYaj1e4Ky5TAPGXtCufSK0YXIcbUM4oAOUCsg0STm3X6kE4A/VZOtGQFcgTr\nBGbK7GDdXSLD1MB/Fel4DtuDaTzp0SJYB/i1AFx1onkzb3Br98VJ2RrkwgE4\nUZS7FD8FeY8bX0W4XSonjWrjMuoTsvkYoRtvrI12oA0KI5IM4kvIUaEe7pru\n751cKKNMzOuf6ds1kHFQw1npaMy32majYFL2thjjvcrz7vRfkfIQXYCx+Kia\nkW1MSFs6bKJQTFe57l7LUD4Ov6kZLAIX8ktu+K8tTDSwDJ1g3v99qULNSnqV\nzCH87qugjRf2pRj3eFiwInj5xWBRNbItUgaTxlWv3i+pKblok+19k7iS5FqD\n1o7ewYEZCDdE2ZLfOqn2EWUEeAJ5KMB70wd0quf+xLvXFmf6fWb6OK702wco\nyttN\r\n=vs2Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1sfVIrapkvtYSfDU/6WkE4oA2qn4ROXWZgqpjwGCy3AiEA06wuSQ7t0uOM1v1cgrEXRbeqtQJS7K6WLqhxVnMZCXI="}]}}, "4.1.0": {"name": "postcss-value-parser", "version": "4.1.0", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-97DXOFbQJhk71ne5/Mt6cOu6yxsSfM0QGQyl0L25Gca4yGWEGJaig7l7gbCX623VqTBNGLRLaVUCnNkcedlRSQ==", "shasum": "443f6a20ced6481a2bda4fa8532a6e55d789a2cb", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.1.0.tgz", "fileCount": 9, "unpackedSize": 26322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqDmqCRA9TVsSAnZWagAAxe8P/A33Pw4yZGwOHytDn6ur\n3dpCDNxSn5s6LxSpZAqXcfO/rWGOQT+IdS8fEcTjLt38cO98SPDkcUSsRrlM\nouucfAaunBpiXPo5BRc2gLxlIf1UoDntC26lrFa0CeM2gIF0EwDbQLuhZg4T\n+dOMC4xGI+U0pFBN84Yi+sl9IUU205qwDYQP2X4TgJYc6zZ+IGqy6c/2kX4I\ng5tzS15CpOpVzQZ5AFZR6qVJbW8E5qoqMeOxgX0V3mWGnybzAPauki3HJmIH\nWTyqrIOp3UHseo1kcgVCIx4z1NDZKkQx0p8X4k8ZdKBMWEuerJgiP4YDocBE\nVogA4hGwLGXoKJ9uee6bZsN9UP3Etwr6tgyl2Mg/3/7YYVXtV0s1HcRHvSVE\n83X/Aq+FTXrIaf/6W871MjlsTm7dEHcmc4WQ8SsVdf/uyWjYI3du/xzll4QU\nc4PenELw4C9+v8HzdLlTIGDObIMD6J4LHp4QgJ1Ae02vK9Wuxxn/A+BSWHxI\nB8oby1w08r+AKWo5mSr0DhfkU3MHVsFzTNW3qBlppdi9UaUSrcx9DT8HW8K8\n/lUYDtR0nhyqEfT+kojoetcG2EUefcq2X+0KgI2h0LsKC4yl+RUbH+zqDWd5\nkeDicZ/f3RXHjzOjV18F0pjl7uH8JpmpmRmE3xU2qxQcQNsqLDJGVUdeUBe5\n19Ks\r\n=zMP3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGu2XIqvkdx0pz9SKfRLjacOPMA/3Gdhi5SMylSfbZWgIhAPPPOHo0H5F2Li7dKGo8qlcQRGjLOZP5nQqzxU4JEdSg"}]}}, "4.2.0": {"name": "postcss-value-parser", "version": "4.2.0", "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "dist": {"integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "shasum": "723c09920836ba6d3e5af019f92bc0971c02e514", "tarball": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "fileCount": 9, "unpackedSize": 27193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpLpoCRA9TVsSAnZWagAANQAP/3+FHzKyMlmrztORDqK5\nKXBDOWF1sERpJTyPTldQBNrlYrMJ/WxrnFmx5+1Ixcir1pieRL06KlzQR7/a\nR+r0R3C8TVxA2+nPoGCsyyjh4oYbMdBrAUD8xUpfHpAIxA5tAYUXzvl8UD9Q\nKmcE8hFaKyx/6huIXNJvmXiC0FK12aj5cOLAxkkqghE4CQf+ZDh7sc27P2GK\n99Ln5JOI4OoWMUYRSIXvKUcyleqpBlPzcMixhhKVzJbJ6T7nP0EGpx8UiNWH\nJYJgNDI9HYWGxg8xWQPDb+bz4<PERSON>blswlXG7b4FQOjTiwrTXGExOhTbnU7sTkH\nfSCO0WNcLdtMJIgxnMyE5MhHUPnJjE/yZVxTX11eMf1Fa8ouYbL4diAYbekW\ncyjZGHuIFNPGRwxgYkhmTQhn1iNDRZk0epkcFkux6JCgYJJuu37zYqrbR0h2\nm6nulzm4IsURI4NUYyyRAVeYhKGqiB8pEBfuNJdyN1gR64U3Jsdcd0itwarU\nzQhWvuYO9dqdklFLIB9nhzc77aOq+IVuHLBhtmq8tEdDR4H/pKV9XZWIBfCa\nMPw+FuAsOA0JuOqNh/NB0RwBn4SIp0ky4wg+CI1TiWXTBNirVcrdQcw6Cyl7\nt5DQ6AE8WMwfdCM0rdkxfqVPE5U7Ff6/0r23jxDvGRVGxneor3FfP8RaaUkT\n2w8b\r\n=emCf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHNke+7UHWHrilJ/00GKN5UcTpba+h0s7EgnB64Jfv4+AiEAjraq/TQZRHEIdNYNXMi2hmH56oVnwOju8ICvEnjMn8Q="}]}}}, "modified": "2022-06-24T11:09:55.679Z", "cachedAt": 1747660588833}