{"name": "loose-envify", "dist-tags": {"latest": "1.4.0"}, "versions": {"1.0.0": {"name": "loose-envify", "version": "1.0.0", "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "dist": {"shasum": "df40073c10a625c178cccb0db23164c0fd058e75", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.0.0.tgz", "integrity": "sha512-u/so5faDpWlTZQu3L67RSH6I5ShOyAGdNKJD4ckHNEJs/YoQTqgguXDKg1SVNpfYnbxo49oCSj69mu4Bsol31w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUhZCJ7v+3J74rGeyt/pbpsSFL7ctTMUiKA6QW258UpQIgRkaxAAUiqSPstcjhUShPBEEVZhFmW0vAN77KE7187IA="}]}}, "1.1.0": {"name": "loose-envify", "version": "1.1.0", "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "dist": {"shasum": "527582d62cff4e04da3f9976c7110d3392ec7e0c", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.1.0.tgz", "integrity": "sha512-4NSTa8hGGPR5GZqmiBXK5JbxPRzsfMXayx55AvpLGHvhsNiL01bToVMGiFxe4CBhBLJCil3Ek08q9+SNWiKlyg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCujhpM3ncLEg8LQHHpsiiu2QEzjXWTihKUpDZzOwGIbgIgI891QrwM9E8IKzzrnanV1Umy9Zr81efTv4c2dTx9X98="}]}}, "1.2.0": {"name": "loose-envify", "version": "1.2.0", "dependencies": {"js-tokens": "^1.0.1"}, "devDependencies": {"browserify": "^11.0.1", "envify": "^3.4.0", "tap": "^1.4.0"}, "bin": {"loose-envify": "cli.js"}, "dist": {"shasum": "69a65aad3de542cf4ee0f4fe74e8e33c709ccb0f", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.2.0.tgz", "integrity": "sha512-KyUZthzTKOK79BVhotg9i7VT6BcBij33rdnPMeZfafKTO5Br6gzDGqTieocXCJW1n6so0rZCiFgoYM5W10tFWA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEtpoJpeWrPT6X7fvjQGz58kqdG1BJmIXXNA+MrcLaBuAiAMXoqdBFk1jGqdZc5sB+yKV8sqEe6hShPRGiV9FyUTag=="}]}}, "1.3.0": {"name": "loose-envify", "version": "1.3.0", "dependencies": {"js-tokens": "^2.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "bin": {"loose-envify": "cli.js"}, "dist": {"shasum": "6b26248c42f6d4fa4b0d8542f78edfcde35642a8", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.0.tgz", "integrity": "sha512-500ib3eg2h+w0xlKA5mcW1BxpB5EzgfK04rocSxP7JXnOHHAYD68iqObXKPyKvFmkdJT0ptUmm2IOyFSPtrCQg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGguqjxYdiGoNZ3fbVChInbkXJuROP1NS9hmnu8nzTeOAiEA9RTY76cXfCr6xlgdVxttsjRRWWVg2wSHH0MyHK9oBsY="}]}}, "1.3.1": {"name": "loose-envify", "version": "1.3.1", "dependencies": {"js-tokens": "^3.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "bin": {"loose-envify": "cli.js"}, "dist": {"shasum": "d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.3.1.tgz", "integrity": "sha512-iG/U770U9HaHmy0u+fSyxSIclZ3d9WPFtGjV2drWW0SthBnQ1Fa/SCKIaGLAVwYzrBGEPx9gen047er+MCUgnQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeNL8+NvgCXNu+DUYJ8CpC/YpkMx1ayUZESNgzzYsTFgIgcvI3hPULdKI2NvMJwgK389kwbBvn1veK8wA/FxXNQC8="}]}}, "1.4.0": {"name": "loose-envify", "version": "1.4.0", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "bin": {"loose-envify": "cli.js"}, "dist": {"integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "shasum": "71ee51fa7be4caec1a63839f7e682d8132d30caf", "tarball": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "fileCount": 8, "unpackedSize": 5814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRJP5CRA9TVsSAnZWagAAQQ4P+wRneDh61dzk3uPX6QCb\nZ97i+VxSY74ti8gVGSCryXFkL8zPsjlBd1/yADnaS5y6InlHuHQZQ2820I3x\ngfSjlhCU9drbF8vXw7FSxIRQfFO5JvF7+L5M1mvsDVmz024M+97DQwFnRjo5\nCvSDVLC/HbnnTRX1Cfj9kQlDVS85RhLaDvYWendfUfivp++TwUizDUTS8nOu\nVIiwXygaef55bBmo+yNeQa+GmyAWET3jAaPl0MNBht6jtnMY9ZkgwXc03zaE\n0wRcOZbBfhUKk1VEk2TCN/TtN+AyktbI5rmyQXoHBGn3HjZntI/LjLmAw9dD\ndetTKPf1O2n8KLT/tGZjIRQjQWNbB51thQ4H3KsMBSfbIhdnZvFHmnDKilug\nIGjE4lKa4px0/ac+SBw6wQuZMy4I3xCbZd59qSpPB3IO0E+Qm+rhLdUiMDjf\n2y49xwMB7dixjRv7sLLqYwiSkXGkLp1N81drN7oNoZaLxOVCbJQKYoHwnsY4\ncoRc7W768FaKwZJPIWfZK2u2MAq5TL4v2+2laxAn25K5Qs6hh8rguSho7iPR\nZcaKA/maVJo8BGO+CEqvaysfUbnYZ06VOFnzG/TbLnLlmZUMMex95z8RloP8\n4kcoh3muuqd75sMUDURgjFCvXbQeORMZ3sa+G8h716pUVjvKLu5xNOF7qCN0\npadk\r\n=rcpJ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtEQH8N9PyT4xrBOIczpbwwOu4sf2mIqw6XS4KIwV+WgIgQZ76hRyzJ94tUL0jNVJ8VyAx8MoCeb7p0qmyKQUEka8="}]}}}, "modified": "2023-07-23T04:49:38.578Z", "cachedAt": 1747660589674}