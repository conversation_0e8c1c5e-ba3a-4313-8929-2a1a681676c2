{"name": "@radix-ui/react-compose-refs", "dist-tags": {"latest": "1.1.2", "next": "1.1.2-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-compose-refs", "version": "0.0.1", "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b615382fe6c838b6e8dc6d6ae0b504d141e46d8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-Am9lwUHns7zx9bYwDWQcXoLQu5mCp/uIourc15LDVNJHFT3fBhXsbTCSJrt2JQv2AfYtVM+4gR2OfdIBjlkE8w==", "signatures": [{"sig": "MEYCIQCYLQ2ZFzUBt/m89KQ1mABxXoG6SfdvH4XsdfPO9t/9iQIhAPMnocSYKGmCBVZZX1qV4jGwBJagyHVyq3fsWxBV7Nzb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VECRA9TVsSAnZWagAAuAwP/ju3F+tbb9yrNRFiuQM2\n9XCZQJkDAg04Ls5BWOUmQgqfrN+S+uNUc1rVwKedQLoMZvrAv63bQ/VpfQQ3\nikBkUJe3WlNwo2XCFmmCqopnMWBH/zZA2zmG6tlM8MQy53u3MaAQS66L4AQt\nBRfEJuqV91FOfAZ+GL5BJTYOuu3j8MoNwcI3gbB96S8ahVyvgsuUvLLETebN\nZBb/GZmu5yjbmAG4NV+EPMv2WPt25oWJz1qAR3xcCL43ln+ItvtKDJspMA5e\n/EzaSvy9Xi41PpuAskO9LzZG+8cj12gpJbxdGglwoZjPd6II8NacMTWKhTUj\nKdb9e9+SaRJZ93Atyx9MfUs1uuW4LZvKSzD7Ndh9b0j7u4K3aVjJnLUZCkxv\nh8/SB0XQChfxKLJ/jLKI7i6vim/RVwPNOTX2CBB8KEkRvJKnJGez62ljlxZ+\nYZeBQRpoPb3vjbGMGboPmntr2uudVVzQU12YRirR5mneZY3/8uLN6dtdFL+y\nCxvx4XtzSXqTIXAmtH/OZftCzP0lzHpysewetpPcI6x9fnxll3iBpWgFwaxO\nIUvsKPwtHs7j7edQY13hwEIVOOw7zIKMZg4M4lkNd5TiOYqE0E48UvSzwHVj\nqJ+mPj327voiXEpM5xXsXHqJWvF3t+VEGp9SA816mnoVQh3h44rev4JuUN2y\nkByC\r\n=jc7/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-compose-refs", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0b922c71666001148f33d87de5c4731a5dc43ebc", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-ycJSqHvt5yrV1nf1J0GuPrhX1fz71eCBpqKjw6ApjBFwda1xmy5T6HW3Vg23fYxllywV+xNCSD08c+LKxN/S4A==", "signatures": [{"sig": "MEUCIHr44WeIlJqeK5IANPx01CKioplT1S+vCyB+kTMDJ+W1AiEA+UYf1KEfph/AMWmfg4ztvW+Xm62ciCaODTbcox+m6PQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOqCRA9TVsSAnZWagAA3PUP/3+bwzHOknhSfkTpfku+\nSXDYlJaC67qWRTIyQRTFDtZnSs75zo1UPu9BS3f+0V0qXuZxux6No9QSrfpK\n6x6G61FBqu7+rASYHuuwa5N52iXpdjBYz032lW07MSSPzy+8kCKqngEBTgkw\na3DwWnO+ZcvbFbu8+Lsfsi596KjvFS+H+eHDynt72O6hjjxs+l5ryavNmU06\nGr2b5W2Iz8XOfBzCTEMGaFfMEo1SdwUA1NSsLA5qMemHLarorJe7Gqn12lO3\n/j+MFi4UjGes6yLYO1c2wc1BLLOMFThsV2kjnYSpNCc3pxrjdtbAgyCQGASd\nJXxqhYOEBmF/RNnON78HnAHgI2651+k89yz5ZuBrX8xNR1FIvZZrFzyygUsb\nVG51/xAL5QiC8m6gd2s1rXHrNt3Zdc9/pQylvQnw/g2FYOeMo6tXW3C1K0RM\nidPTpjgDt8OKnyXGfyAUF548vaDf8QfFB9dF+aNWZTQVsFJ42p7ezMmuE3Pi\nx7qPNP/fh3UaPmS0LurbgRltFmhYI3KYisEN2XwTWlJcnorLr4zFl1rfIhh/\nGCLVhY/pzbZNE1ukcsx9dyBKK1ncOQYAykTYcKz6B9kM9aLQd6r5N3SeysiW\nky9vQNgcfGYDHbZN/bz74SYxs+lTjlKSzFwAx3MuKpDGPl+5QyhLYCgDts+k\nxA3W\r\n=/lyO\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-compose-refs", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3e53df0e3d66023f06f0b09898871fef360c52fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-Vq32530lceap0gkmj26AivrYtMeYOC+07BOBpTpQR1PD+fiKvoSFe2Lh82sWAOMIxQB0G6F0wNUX2Rr6JISwDA==", "signatures": [{"sig": "MEYCIQCf3bGVaHJBt+Uv6UzRTEFTK+w07i3le8xV4bdcz1auBQIhAPw/VxjyrAaIc86qo72WbrPDAIi5h3TFs4eZ9RwL9MIh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HpCRA9TVsSAnZWagAAFSUP/2RWbdKax7FlWRMo+aix\nCAGv61N5rcGsup3F/mS58gcVZ5OQiUmyehxiLY1IhzBBGAguOX8l1GDAgy/e\nlJccEchKTU26bNs17xuHtv8/imhbQNFEUWkEW3w8iJwC+2NuV70CNRKS3bEp\ngQciPTRi8WNJMINemm/VJ10dVmf/d/Q3hfkyCL6s0M4f0s1aesESlWeJAgLW\n6OTx0cCzPHVQChB1IAAfrfYMhRqQ1GK1B4M3f9a+Bho+xC4hAsjH+lpo7eMM\nbMvtTNqcfwC4JKkJ4Q/aAYKGBEibBp32jDDv327ihxcWl6y92uI7WGwYh9G1\ni06g6lzxj61sWlCx3VDSHjCWM2pVvQ37gLVnjNKqPHHz2bLfS6EtVbyWcrDf\nxZgkbqFNb+S1M/0SxstpNUoplSHoOuV5soxsK2kA9PFbLScaQySHQdGOqiUh\nVejV7EJZRWYXqQDcepo/uOa7HT2dbEYtGcepab7HwOEsS85DFwoEMXVHoKQ5\nr39+rX/1Y3964j2L1pQ8ssj9iTC57U5liYIdH33lrZzPvp664Vj/1ByKYyW4\n1hDDSWPVsptDMXp1x3ZTykT4KvjqP0ItcubV+CycJL3AyoTtP403ARzsl6lh\nrlhHaAv+MGjbYEEhoCdkez6ql4XZDiCqSouw9l39ZaEAXLj8tCKJCdaCfhiA\n+0ZO\r\n=mJTM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-compose-refs", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "908ba0e99e57125555e7704ee9c6a3b1ff9dab24", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-ZJVowduquWp0dyGsESi6bAP+3GVWkVr9EdLUZ/zt20cnXqLSpLmBx1oqSUHFIRcEGLO3As6LT777Gta2TMhQxg==", "signatures": [{"sig": "MEUCIQDXADDAmRCbnV2zqnB7q96grZ0qE+vCIGaWNWW4r6m+kQIgY1vT7eKc9kFr1YDJ60gwLh53tiZMQuP7/lZOJU8Qisw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vnCRA9TVsSAnZWagAA1qUP/irHPneD1r3Sm5/+E50H\nPZ+66/znq6Vyx0Bu7hourNsh6ycLZXSnnZUMfrb+7MtLR4jg+nsL5w0rActq\nLwAcPiYwNLnLAKUHkCWsSC/er2OUlwkXbH24cBtesIpMPSEKC3urQC4hcbBy\nuxgr5gxIdl/0Azw4Q5fdZE2b6zpSyG6nNVr0TnOuGdAnKBH5KbITOZa3j6zZ\nLAjZ6iDFSqoOpRr/HqrwoMYRFBNStPd9ZYPP7IFZowkjLo1uPy/9BdcUJjEa\nyeH9ZYuPEpgeh7f5SXtly5b39IZd9zULH5am5nDQfnfPp99MRlUFLxKiR9Ze\nsCi7KclVL/OjKXKi5C4yTjYU3puBgwAxPqqOsdaQeszJCgl3vjbR1iqg6m/V\nbCzttuClMrXwOCavKdClCBG5JK8tf61ebmu/nwtToTkjTLw/HHDwgqcwgKlT\nJ2y/P/3Ie7poH3P3hhZyPKeXM2SLY1dzi5E47A83Hx5Sx1epXUr884HrzI8N\ndaVUAKGcJVtjsC5JXYKHPM6ZMm2Zb6KGE2R+YAx+SSkGU2rgHCBR4xl0fAcd\nTaxsGccr4FGfx98qFrN8fMb8IZ+7mrxjo8nkYGN53kz8WdPtb8NrVVBi8z60\nQBon78MbOigQLhBuya16s5rFbKlNK0CIk0TA4aa+6K69NwC5/yeD16ct7v7H\nhP7Z\r\n=HWzb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-compose-refs", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f71f0de1dec341f30cebd420b6bc3d12a3037dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-O9mH9X/2EwuAEEoZXrU4alcrRbAhhZHGpIJ5bOH6rmRcokhaoWRBY1tOEe2lgHdb/bkKrY+viLi4Zq8Ju6/09Q==", "signatures": [{"sig": "MEQCIERhSfFiKc8OsDniRS/CEPOO1bya2xlYYEbgdNtX+Fi4AiAxpHLWqqQYYRdt0ekxjj0pGAaMRq31miDar6hGxBZLtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmaCRA9TVsSAnZWagAAC4YP/RUAgdjIArrhDMk80en3\nJzL3B5EfLK2gihnHZWgG3JMgUIuuW3h4D890t/+ojdyejHSHAoMaBQbtJ3bv\n1JmMB4czsuAY66iFpNniaVjg7+Rq/r/yslc6WeeI56YWuXiYvxNeGzE21omp\n4rAjcr1MuttdYKceb21VmJQuUFKJtq4m4ziiMJ3MgLIMvrsLz33booLrgS9T\n6gdKkd22yHhYxJtat5EfmKEwwiLUyYWXaUZ1cdudxL/FkA8AGboLbyFPEums\nuAzOlKnvNM9PIYIX17c/eBISuEzEuKyII7eq02HIEs5p5/RRIUOD+YzsACha\n7+jhb96tCZ+Taz4JW3WImRKfZO9+7BkaIBwcdM0cr5Z6jwE7naMfB/Ze5Joi\ngYD0USS2sir0WL4nOr/rSWVshqSX1PU2pleOQcYsl43iXEhiJ7Qi1GACKqpU\nNg2p64htRGffC7PSf4/Q8Bf/9KvOvf9Uo7ICQ1qokxoeH7dsRWA9UEm3yw7X\nfz4brFVp0EUBlZd2aDrAagJE48kiMieTxJh/t57AlVZvkA8RQdyQletHaBVK\nuHypX/G9BjHxLQtSObmRUlBcGDNi9hUTForvV7wuhMUGAUq81kI6/NJLMneA\n+v8Yj0jnwlYRx0hl+eLiQ0g8BOucXkrx9Wh9EEQNvxyxS+chI+s15gn0t+X0\nqOAv\r\n=B7hu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "231020f2148e96318b44176ad41242571dbdbe6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-h1rayLZApNcx9eYEj6MdUsZT5mnziidQADdfr6pGJGNahpOu4XhlRx0qayawhCqxPeY92omnQb98phVVN1b4+A==", "signatures": [{"sig": "MEYCIQCzJIIgKmUImGQ1ASh+22t6//PZJBBS7TbLIkdLmqj7kwIhAJofuDYmHgeBghF+90jA9G/DvoPWVYwa+hg1IDrspiwq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpUCRA9TVsSAnZWagAABmgP/RqDvvcVfMYw/AeQFbg7\nXYf8CsMC6npdkW7osIzWGdBzjfBwto6ijsMwn0Ke15dIvAPIUqnp24f9eg+M\nkC33P1atf6h0ci4wCvTRpiuPaGsfr+G/AnRHJJ+R+bGvL1DU56EG54CYMVzw\nqKNJ0SKLTfdtFe2hcRu3GZL5j4HQOWB0g+Xbvmtfbt0Bh7p/C4wv4zoxTF7h\nm9wn66TdeXHTpuZUL7hcP57CNVXX/Rkb5Lz02N/gJWBZUJM3FWKyXIuYP7NV\njrZklcsQaRGnzREbUA3C0tO7J6kjlmnpliCMMJ7G1X/SXb2cDSmn7ZTCn3eH\ndyp28ZxJOQjfDkA2b2Ud0S9zQBo/mHrNs1Tt9NPHB9uhyzP0rPmmPNH1Dwmu\n7Ad4Gwzk86VIQX9+oOILu9tp/QvXwGG3oYeOdOLalSYS/v3mUjegbN4ZvURG\nFbzFSRsc9IBV7oHrjLC5L9gid2/uBXg35GQL9UoUoKeOUmio3SHdh/si6Rrr\nrTk9Y1kBKm3pZR4lguLXoT0SdLagwFJRMmfsRoA/a9msOm8mf4+Es2zdBH+g\nV6C4aIs6meQUzj1xTD7lfjAAjIf6P5NfvtloBQ2Ri7KQpKP6ASO/CqHSnf1R\ngsfMS7HlS8kcYmOFZoUlv0jAWvlyL1FzIheOxrAtX3nS8eW3bZEk1VwzJbl4\ndeY7\r\n=IJIo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a0b31c9dd84ea8870301b2a989b4e1d5153bcb50", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-kPlktgRwUOqbFc2z7Uo1wp/rO/TEOr4jqvUA4TExdrM4Cv8marxJeOcXz2Oyh+bn+xRDyreDM03mhOcOmJ6Sig==", "signatures": [{"sig": "MEYCIQCUDeasnbqvhfsyy69a17mEheVLILzn4PnLaVp3uqaZFQIhAIwvHMo0cHxDkzsqkz5smMqwXKo38G1KpyqgcaPYzAtH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyMCRA9TVsSAnZWagAALlMQAIVrWCDE/+GzE+0HEo/l\nooV6CDrCQtR8lzYvdQlFG+TOSZPLKq4fiNfYy72SEFAk4tGenzVYxycV2PhH\ndPUUQ2MCpiaBmijqUPWHebdvhZzj+GYfLDXHSFoPEQaEcb54wit9MJjq1kZd\n1cf4agp3meCXR7ef97t80Sn5qaJIcgHBPJ2I7neExs756ciwWp1dA8/Qpql7\nIY+JJ0PcZDWPLuu+nfIW1x/r4CP9p5vZHKb2KzQwYnIeXkS353GaSyebOylx\n8vrYHqOFGCdMXWafozzCfJ0+waeVJFQXblYbpYC8TKe9HZSlmBJQwYEQn52k\n2YtrLhu5EODb7ONPqvxPbHwxWvVgK2xdte3bAw/EB8VRIoNYNHiO+uFNhfqB\n1a6DVxmJ9xgkT7FRSHxcp0dGsAXBtyIWN9P22Ew8uT//j+n1LL9d/2lXo39u\nbFSnYSmbLID7FA/4UYzertsHsRg+YDVZQCQNwi8zY2/xqkHBzbrtXA1HaiB7\n6Y4PisTXNTBrz+Vs5mn/CglROAvFQTS4Cc2p2NOJSqvJW2+9y3VDIFEVGoaQ\nZ9UvPxqW4e5mNt2+cTR+famP2lVUnZndKE+bpzT4Qq+ByeszkXURyZd/mRzu\nQTQ4r3wtKzTn7Q7Jwrq9DBSiVK40UnrNjOsmrAgaYPpzS0TvYYToytJLk3l0\nGZIH\r\n=Zl0k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-compose-refs", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cff6e780a0f73778b976acff2c2a5b6551caab95", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-eyclbh+b77k+69Dk72q3694OHrn9B3QsoIRx7ywX341U9RK1ThgQjMFZoPtmZNQTksXHLNEiefR8hGVeFyInGg==", "signatures": [{"sig": "MEYCIQDRkJWeVhxUYLYGKVWM0BPr5vh6gJMqy9fJzeosK1vqLQIhAPEvok5lbvGDXwbRMuyvJisEqcxjP+MBNH/D6jFk7G4f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmZCRA9TVsSAnZWagAAY54QAJmzCVCgcs01eaKQ4W6C\n0wPPhmFOPgoQKYUAph+7HJ9TZe2IrDEfEpqHkz1LQ2/Fo0nAg5uWXhxDUg/Z\nONqNzp71TxmL8u+ORGzcyTsPWg9Dmix72iolOFSKi5V33z78vZIX3DJO5OgA\n8YKMq5D/XC55HUHvjNbq2aQQfjaC5TVpx9I4gMAJODUxDvPE7dtclDwIegxA\nmLxVr045IxH6I7ODL066uNT0Fg9w9z5i0ZaF/uidq8rL854u5on0tRZWrZKX\nNyqEOMHFGayZda9wJNBONAsz/z99F5veNG81xyBL43DfEf0WPJk0zsD7qLir\nOr2hXJYnttVL9hae7KvMurQ5CtJuHNT0XWcVNNH9RGZDb7ePQLq91JrGfWGr\nJIlvkrYOn2JClSH8urlVCEJjbHhsycOKV0+hwYT9XXIzs4Y8Lo4hK9ioWeg2\nbDlZIgjXT9UNIveFgxKhwON1mwqcf4WzauQNSpyZ1FhWGV5x+Eonlu1O8ugo\nC3VzvS6TYPbdY6o0S/1iN4nuUq14+MkyClqK2BP1MafYdT0uN+Z7YwWaVb9P\naHW/e8eCBDnObaws9LJPpJDqYgRnHzL7moIgvB2ZdurAESlaahHL4qzeYezz\nQ3JzJAW4UDF3LnGtL/7uLn05YQru1SNEuFq3mzTaXfTFCOknpLsNvvoDOyUo\nR8Pd\r\n=G9FR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb631aaf8eb546fd212c8971b2b7347553c90d46", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-HW/MIrNZ4UJf7dIiw28c7S3sUoryOAtdhpsfqped1RboevFjFP1eMix1yPkb+4S+KkR/hdmFuBPF1wl5/Pc8VQ==", "signatures": [{"sig": "MEYCIQCExa3PoPWUqtIiYZBk4xswSPVD6HXLI/NV+Ms4NVPH3gIhAOtcSIOEx30AkbdPKbxHLePe56Vm1YfDXKI+cfhzqkY+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAP1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/QA//eeQqpawAcpuj2cz7r9cZpupeDD5Kk7HqMAbrfk3wpEtu3FnJ\r\nfI0pZPyL21VJmc6drB5VzfI8K9SG2tWw4IZ9lCOuPJBeYTvArDCOQmdEZzcR\r\n83TWmoTZbphGoEF/akUkN9lEXHqnSVTvg3vGNs0giVdYCUUgyJR2HVrQJ2e+\r\n5MtHitI16J8tSWfatTuJ1ozTWr660I5MVunyUvVdn6YqaTllBJ458qolLfR8\r\nKtelw+tX8SMluLw7fjLCBXgU2rBY1FaaOzsbSCnCNaz7Kx9uXwdcNlPTmum3\r\nt5WpnfD6VjTDH/C5J1ZNsZQz3LfoCJiRqDw2MSPJEs3CMUN4VOR2mguJEO8c\r\nAuNKomYMwdIUcjaGMzEsPLef1wjh5E6meXmeI9sulWM6trXiNWC30sPBnodJ\r\ndt1Ka3QADfWPnzNDCXydMysm3Vu3wltDVJZyL6W+mBvl8L8ti4maAtqQmx8x\r\nQ19oTP9Y1evO7shEsGSoZl5S4nA8HdomCsNqpJbSMZvUR3Q1DgzHrM1EqHvy\r\n83GWmSwKdTNg5QmqI28Yp6VAIiVNPd0eWZ73s5Xfyun35Haf5u7E3WElrHGa\r\nDw87k9fzpHky2+d4APEh4q8BTWsMcOxyU86GXILzdbEwVmdEhKbHQtlmt7iK\r\nD3N384DXlrI2tj9fkETK7HIHRcbUGG3ldiw=\r\n=OMfm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ada12799237826cf5658c40426dc30b7a96bc4da", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-tbwueL7v9vGMD1pvIGcU93nnznnaeLG0p8CjQ9X+dH4jS9bNrBPzI8hOqSTLCjiwPaSfSJwtcI15WgdOEp0/Jg==", "signatures": [{"sig": "MEUCIQCi2LbCCa1HDR1beRzYhLDQZ3IBosUfwcCHW/VRuDfaAgIgStyTD0ugySYeXrqDDk1dwlPbw1O76CYTadQYJd28rdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLFQ//aXa0JhWXmr/Ijzhr/JMxywA5u13cka+GKjRJo22s56L8cFuC\r\n10Nvdi8Xgnyn8b/OU9DtGS6/GgnMk4UhzGHHq9vBnrqk7sQ9YscCDixfDB3L\r\nCR3Y6JvRaD4h9GgELLMy1K/JXalJbyXtTfGbh0xyTNKrf8GTVMj3PW5fSTNU\r\nBrMAspKd5ZHhVuiTbneIy3XnAgEVuBHAaH7iLw02WO53voi2t+kUYGPvR2pL\r\n+wcnfsKkIy582pgSMDlqlmvYsHcgy1uESuOy5f3BlYSoTZQCKPE92hI1ldHL\r\nC1vcwvaiOcHHtNa6L+jliQqxQdPLkHT5/18+Ti3nmmbK3HayCyUUHTDL6h2I\r\n3ozjzhJ4ViFUlm2hQ68IsnbHQQXFnJte7CcRC+j4goQMqfN8uaPtoxNhYuxP\r\nVGav7KgvO2NGzKTpme5oPePV068hSP9IpAOr9rOkY+Hbkl64wLTB66XEhSLQ\r\n67jhtUjMnsf4hKxUAnQOZAvlta49HsLX+uXUySc0+i5ebaj7PWtXfYB4vUGf\r\nDcIA0vfui5N5liNuf80qxdmd7fq6NtyMM2KkBRvOGqnGqMnpdQjawq/UZVcg\r\nMHp6xCAh1Q/UUE9AI7/yvcl8OxSnlGLTjyotflX8vzboEipJSUz4fdXEqU3g\r\nP8lU2dH0Pin/wdsZPUGSmnI4K6GYFE1ceEE=\r\n=11gm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "723f61e2afb7fb2ef0092ccf6f1068c631668920", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ypO/DuTtJ/MrFC0kUHyQWR6URpYxixTi5Nh4DnmDh7bvcJz9j2eOMcYVM8x/0igcBkoHaW+XD3aUKKgn9i85Ww==", "signatures": [{"sig": "MEQCICxofw51ecwIZkQlxPDuuwLarHJ/Jw732d6G2O2kuEhGAiAWctjlzX4Z1iYmwkj+4RpL72Ul6WPmqJaJUrc6+gGSRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrukBAAmZ7NCVvF4gnpsMj4eCwWeF1n+fgWWYxSjEI6cpnYfyqY1wok\r\nTET74TdJwGd4Rowcww99mA2kjiX49hBM0c+XROK5YyqcdhZQfII4EAcdx1fS\r\nquLuVU5hBMq5jKx2v+f6xvfB8UohAq7ZZWz8aez3Tzvlg/6dQdoTuf6BdsSx\r\nf39o26LeTck+sKNTNxn2uM/r88ToOZgORl+du6el3jFJxg88qYw3kH6m/Ktz\r\nT3IL6lDt49Jv2QoAkanemT2nhZZxWHZiF2FJ18c02kFu+9QIYu4s6Vbyz8w8\r\n2D1JVQkcOyOTFAnyi3oj2mBcjVDXMIfhRqfA9QwcdOtO8qajYn/VchdBw6gv\r\npPGlWNC56BIwyekoXd87NY2B1w/73dB1RAg7ryrn7FJscHGaIOaVCjAAPZx3\r\nZ+eekrn1yOB5gdZgJnaiR6Hzn2mQebBJFkj2e6hVWk5tg/lI5K6n6Jq2iYqS\r\nFRLuSw2kQqWeyOcuYjYbQ4HXM/g9uKeOcfKGfb+Ba3IjitdP1scLM9ftq29X\r\nRkY4kJ76BvttToJucbf0Hw0mbLxA7CtwU2ZzfSNz83BgZq5PUJDAFTDaD876\r\nnERlMe9azSQWgkO0fpNXR9XVrkKWdGXqDQiZO/gp1t1PQvoV0EgIrzEwkC0p\r\nWPNr5gUcsEsGjoOFN2UJfJQtFy8nG2Zpfjc=\r\n=UPE/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "00cb93b06317c5eee96dde6a5413257e496ff377", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-K3RO6P4lbTeTxtlMUvQAvyb1/GQD1ivG+Wzw9RbuhEjSQ9ZptHoYydvnPVejzoOVni0mSln80Dc3hy0HnxuwcA==", "signatures": [{"sig": "MEUCIQCxBaNxKfeXQoNvOuPKDn/ReGIoitBUaDpsq7J7FpTH6wIgKHTI7vpzOaV7l6RBBiY87W45yFLRML8pnzPvJLMbCi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokgg//XWhVfMwkxER7zHbxIXk3JRmyScrL/kFMjN+RP0SxnvcCYPRp\r\nCwrEqEkVc6Y7Cj0Dm7mRuHm55ib7uWyleFoGJAroYjrMn6AuQedooVVXNJUK\r\ngYTzZNNPcR2zTxDdPeEVi3+Gr6rp6MWs37aKAw2tl5Dl4/LkiCporrald7w2\r\nXafKSCO9cIjIFiQZndEn5+L0ZiVzS5N1vMAir0JOqUmS6qxSMfTjNgTP+Icu\r\nDBy+WRg7qYY79k+QzhXTATjWmbG9JL3jg4Gi1smJhKgc4meoi9uDSXkjKQ3n\r\nq/xs8p7x3t563/aujgZR7uHdzgbnysQcUCpGjIkfGuJXI91wOUXAUQiRvJfV\r\nJugJ0yg4doe6ofGKW/J7yyYY2QOYeXtVSidXRS3KDMudxuFNkSDkp9ywd65Q\r\n4u37gh3O2VnYwJvwfPegy6zyvq6tCelBbuM2rOFZ1MIFasQrmP5FLZf5WgAz\r\nXbTS4oaWD3t+eClQ0QiLGaZWmdFDNg2mOnWsOTvVsGlmoPSExFyXgV0puk2K\r\nqu/wl/s/oVSTQ566bMprx3PhVtrZuWJZ5XjUI9OE15HyfhJL4d4JaMfi1Y4m\r\nW/y0hTmnL8PgWnx6sRaYOG3q4kD7Oe+G434qtdO9tgDSkDHezA85gpMokCFz\r\nasUZQdpu4E+NwiF96ykZmqNvtLr5L8mLMXE=\r\n=xMyY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "98a4d6aff59c527fd81e2858d3baf1d8f6b1ec29", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ZDuMyLOVpT6SCa/awxstzN/YVWuXedVKeKzKh998qApQqWE0YERLoxoUyiAt1kXOI1Jj1TASwEHZHx4LIO7How==", "signatures": [{"sig": "MEYCIQCHY5sye8devvcdnNs1EH60vyd2jrDMBhU7LeDmgiCZIQIhAPe+5mRuETPZI/gwqKcTStXk3z1451n8ICE7AGaxsI8a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8OA//YRDumqBXu3+WO1qlAhfnIoS6A0QpUtKyIXoiAx9QOk9xyOaK\r\nz17Kcfy3jAe6rlPf/lCZ1GuwsQJqFFWZs6fO3aF8ruXLtdxm1KMNkMXkdtc/\r\nUAmH/bboHPgFplq93eCWaU3MTZd+ZI8kDhlGw4X7PdLkTVbb6ta2Ltzhhnpv\r\n9Q4dla7CsvUua5zd7SjVytK3sNHKBorFC7N3SAJCn3LCcYAnP80/hap5usnD\r\nufGvucbw4e7siHnvxCC3YIAfIAaUEYHDxOCGaj2H1GeYDNUhaVJ3HtHnOuwY\r\nXET1LModwLUXatr+oSGQ1fKqwzPUHnsFywkOJxoZPB2WR7KVQ9pl09bMBsep\r\nl+WFmopNuSFxOJAUgWby/HB8e8cpNtU3DY5rwgxAOG27jrFlioWBgUcuFK7k\r\neFnqseRdSjfmnY1BFsF1o02Jq+sAGg0jP3HF3vMJ+fdlLngxNsC3az9xvghI\r\ntTAFhBsmwbdpYY3rYeXAqRAiyARbjxB4yfaHzqQNUss45bvooZ+4fsydqM7O\r\ncQM4XzSU9RDIfGhou0sVbK5PG8msE9OUvAfajTkFlgtmTkPTCrGoRzGVKDOF\r\naYYYfrSSt5uYbRErj9XLUWIewpzYWF1lJEuDTUsclF7vZwCracCFPLRAXnNj\r\nk1Y3jEYkz8qOiA89hMZQS1KsrO3kcbCUMLY=\r\n=DeP1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e6e82d838c8723322370190ee79746cfeb62808e", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Vc/RwIcAqB7OVlhF4fcXONBMd7QvG2Jg2afaFLGQukUPS/EhSvQik2ZskYFNutuQStXM44AEmPXGGYIYQfOE+A==", "signatures": [{"sig": "MEUCIQCY7QdsNF3Sfwn7H8ISLFBlo06SIOcpA3ctvXQf8pdOmwIgSbqdUFo6TNXgUg3UPxWEpzMu/eqAku+ntq4xRZt14EE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwAw//Yla6ozkH9ZQ116qjNkOx4Rc0CmyjM0czlymrP/DVhyIdeVwe\r\nGuz+mQg+HUOIrsKQcD1gGyTKIrexc6IL5xEsSRU9lFvhjMhfcU4Dqpn4DTJL\r\nslxd+MT1UHx8ki5wFONoObXNBjY9W82Cgc8N3pyCIin81f6ISiFXCDy21EZ4\r\nJBUMbmEdPYf0IbRiZRArY3wa6KTJyJMlsmQKPX3dwgXpNxBJop3cbzkZsKVa\r\nQjBCeQfZOS/wgcSHJdHILR7ed/wJQQNGbfg1RlEsg9x5PvzFJ65nebv5X/5E\r\nTkj4TzM55X9uHeOzRM5Yv/XSrt99K4FLMmHo66hnivU9J7ar9v0HjaYcmOF1\r\nD1d3A7iD6Bkw1ESsPwLHPWKGk7OnTgP2KctL7HNJTV0e9q1XizG1RdL/4vVl\r\nBYsYOm4EJFqLxz1SqGIcRYxFejg0zZQmIJGLCSkvKFJeSgj9FsG2Zf23XXSn\r\nuqejmuAiyoQvd78RqJzgkncqYY8OR8T3CW87VZ0w1mnKtJGtWDVy1PsSuIll\r\nYH8NIKxXlaYirguk3RaWdeh0+jlkj8ogGzEa05n1Ja3NVF1wDPFGx8J2E4W3\r\n3anoiThZVQhbAQg/ziWLkU3j/7Ltt/k8VabEJNofw1PbzW/oqECDZP9hn3PN\r\nOaoYCQFTpNZdAT7VsUDC+7XqPuECuXm1DOQ=\r\n=DnAU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bd923a167d3bdf1fb44539a7a0a4c290df812135", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-wvkRYS8jJy8vTRcNjnno0Y+y+nOQ4WjnQwNFaGEj1EX2+sSfAYTCIEuDqsKegi8LdcM6l5643yDc/LxV/wDbyg==", "signatures": [{"sig": "MEYCIQDJ+8pE/7+ufVHVReZFeCX7ww5AwWx34HmORkPSBOTgmAIhAPjzVc9RK+Ar9xskxZw1yMmm8lWeUjjXaY2AK9Nu28y1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ/A//eTbbV6XstsWx1zVjbpP2Zca/xZuqa2hCYD6IMf0B0VmJWRBa\r\ncCaS9HoyqpSEniJSqMGQEly+f1X3mdnlaYNEfBsM19nAMRt13BuGSOtBNdt1\r\ni81oVyn4r1U3m02h4AzQ3LgZLdj/JogMd4984NksILzhtxsME+xwS2NpY8gz\r\nnWzzVgpjLVAtqgkunZo0d8BAPG6lTt1iNmhHOGjZop4aZNp9OVfwJY7L0CLm\r\nRG6f/QV7cr0FlCkRbZWTIkEnfvTgJHEjjteCcL/qWs5gKYVlAywJnJmzCzYi\r\n1Wm8SVNC/6PuySYMV4PmdX9LVdv24g4SSrgR8Fyzue5em7VAi4tqqmGIIb1p\r\nSl3NlcaFAPNVkXFMdao+XOwvhs1/XuGXPqzpbQXN3UQ6SqJLNYB9ZgcEokhO\r\nvnxVmjc5LKpXu0iXGax/uSdbEOp0MPojmvyc5MrEmXMkEGVuUiO5ixA2+aQq\r\naGlgdkra10j96WO5p2nb4Fj/7ST1U0zJfJb2HL6dFCAUwkpBu1jFoyyWFMia\r\naDuT/LdQEL8hsYuN/KgAl6P1HoZFuHTPqgxmpQhNynGAgTs52msmGx0BrF5+\r\nxU8xBQzcgNOUUrAF3WY2ptd1yzwtdSPHtPDCsBg/qGhlJIm7hFhYJ1tggO1j\r\nVAp+dVcbYHUVZpv6Oo7HgKMeFo59jUZIWc4=\r\n=hZQq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.8": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a3ebfe51f2dc6d1bb0d0691f789ce8ee427676e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-vBeFS1oC+DdBHJ2D+4+1AIP1wgg9m1K+IfSLn26JdU6xHKjDHm4vmsngJRtKRHaQO/xN5pzhQzsnQPiIol0ysQ==", "signatures": [{"sig": "MEUCIQCJV8MN6l4zQPzAI8eLQoR+px7ZvRVA55n00ulH2Y+S4wIgXs+qJ1EMtJ771Jsp7lNCyOwFjGC+NLtyCpr4J2eS5CE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOKQ//YMDp3umD4Am+SZajVG2I+mQev7EHqQJuOIL1A+mtmyNvOgnM\r\n63Rb5YGYZpOWmy+lspB98ZVd68tMKZfsbSw7b+6bx5UChpLvd7XQiXTWxMQf\r\nlEQ0CQH6Lgu1JFxGTbBqlsXwv4vLKmqINSU2Apta8DCNACiVPit8fR/+4kzQ\r\nGCgGSHGz+hFXU5v5YtMSGZCPH0gEWS6kOFC4uqHX5T1lBjXAtMnapWjL/fXO\r\nO<PERSON>jnooMF4A6O+5iw4os91w1ELFDPgfaBFEGvOu03e7UDjTQZ3rwQP/3mMZG7\r\nr+SPSs2gHq+gzPGTHsIEm5RZZINk2n4EbDsHcNb3/BHj3omg1gvo48GsFod8\r\nk8wL9z0pBF9O56KsYFR6At3m1+C1/tpe1y1ttvH13auy+ihtceLFwkiTp4w/\r\nLaZ0rQpMip9Su5swfgF6sBE5jPwgZTMygymU7dMki64DZQN4ulw+6kwZSKJ1\r\neFA8xI7/sInzoI2FzpWlqMmYqgmaIdZ8EGohww9UvdxK2+OYuNZmVeFwG66z\r\n/q95xFryWaClr+tol/o1ff3xPEq/9xGvmLjqifAgmnX28E0vdFp/vJuUuce2\r\nWn4jf6Acb3A5iPuZrHpgKVJAndnwiVPHWC0wtP0ITBLy+j62PNX0yy+c3jAP\r\nn7Oax5z2P8nPcZsoZVTu/jZlO189fWbVmSg=\r\n=xnfr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.9": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f49d521f712c80d6ae6b0f34e86dd75b77897a73", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-RabQL7GWFJdVFCzmyOCg71AcvDhEDHV2Uq/VstPqNykUy1LCtFnhaoQrcB3navdFG1ZgLWBwTl90NH/DUBCiMw==", "signatures": [{"sig": "MEQCIBiZql3Sdm6176mEIh/VYo+wYAYMHNfxday5hsrDoZ3mAiAkwBC9wIHNY2xcZaU7xy0DHxzMcaofJtyZQZJUYAQMyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyGg/8DgUXAJabxazhG1BbBcrLo3V2pRijiGKcueixgRjXf0aeU68e\r\nGO6Ep0sale4ckV5Ws/3jv81EFZIOJVHCpFpcOirU9DOqVcbPPsMA03otTyOi\r\nP4RTyUrXKd2znHkfLdFvPJXDV19IXJHZHwRSw1ES5JopBDmxyvYehBQ33+k/\r\nxRo2ELYeKBw+jo3OPuFImVRYzARthJgLhpIhYUImrhg+MeXAk3TfSvjWXgT1\r\nXY4GYIYFKJh2ukw8Hi0OOGLvLOjbX4k/4SYgx0UqVGYz2GNW1cIZOWjvyFIc\r\n/tLgaTtgGSPBoZPG5g13bNCsGNKyinqj3uoMU74Supbbk5XELDuYz2P6N3s0\r\nn8tohX81bhGPRpIQcTCV8fnva1Mru9178tt+woBj1WF/WzX97i7wK70FGnXx\r\nZKcOVg0E1dI6QvkjrGlg+GC2s71b8ePlL6Hes11LbeQAOSLlaOO2vnKc05xY\r\nJoVZuYWd8YQC9pC3KNz+Bv7vuB0hXITgyaSRAXUOZG15wplBtb1/4q2nQMZD\r\n1VuTy8CfJiXhrbQbOFTiAiaSB2TgtrZGlc1xWaUjSqd2S0LWGt+52/tn8KHp\r\nSyXWrUqeOY07qJYfCkXeHrg5wqoKkDGjOudv1R7sZx1sTlBzIhcMNU5QhIWr\r\npILVP1DiqzLc325VLAkYlXXuNlN1kqfTq4Y=\r\n=G7Vx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.10": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8f6609cc9bbc583299a2066e4366be51e02afa58", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-jTJRmB4q520fR5k6cVTngTMjaSo6Zcgbb4MjVn+qMHaHhcH3L6LDkzGkuo60mhCf94BHPT7+GNLRWUiFvzqWWQ==", "signatures": [{"sig": "MEQCIFTokhzTrPxaQvN1log1exQW/z6RHQH+1X+2mevkYCWLAiA0It+Xt8vmPJ/w9Jjc38A7/CfwTb88WokgAH5NogzTsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN95ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaTBAAipe6jgVSu1V4/0mg+D04IljAXsTh8WePU6Q4vYEEEp9SejgP\r\n4GxJIFsqHW0tAcm+85K+/m44TRoUGwjtw1kRDE5/YhZrl45LtrdUJJHR5Y+U\r\ntUh67GaLvYojm1pOsKFOo9FU3+oFY9SRqIf3E9X8n4crBriNgjK+AjuMvSVm\r\nE9aUGWSQlz9G+uXLfjYtztwAUGScKyZyabIbmh5g/ROF8cvsAPn6bClnZDTX\r\nqcx1HISknfxHIBSZc45uCeR74/wxS/q7oxQI7NQIwRgVaCuT3lPbVHXC+Fum\r\nUS96zQ5SjnFf+NXZwDp5HLiPbhtzhUo2pbeseV6KYnFM52p5uaQVW+mhBjs+\r\nokyxGEQ+ikY0uT9O3bWP/ezeoOmXTLS+ugOkHuWAhsD1KhAjFZx0Ae6vxG42\r\npMInfAu8no3zO3WUeO1ZzyM2C+ErDD3EDFwNGce19eGKN5w4WTJ3McfaaQLj\r\nIPLaR9hqka5nQmjBvToakS8NhN8d5z5ywT9NeHpI/hJ2Tk/pjts58n6MaFr2\r\nsdqLBUodHMWJrp/GM+yIahEAYJj+bRv+0hT6aEDcWO84KUj8P5TOcSzWUmrB\r\n+4zN4EMtD1PN0SbjQL3sxvsxx+oOHHcqdCwO78dTdaAesLnetFamgcqi+n4B\r\nLfrMoAAAMf9PaDp65/NmpUJBAIt7ooqlyak=\r\n=BtyK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.11": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "041ea0994c4c143c7d21f3bf0677f9e26f193930", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-qMl4EQp2C1XQJ6LdJ2iApco+xa6qW9jYfJWJgqWjzqxlG/TNG7QGGiTuI/U6Rp0nAc5McuCVen1d847mM+5ZWw==", "signatures": [{"sig": "MEUCID4o2jan774CUbCc/dohy6/sKRQgicFSXHKeSmZiC5M+AiEAzGp49u11jwtmzBJNZOkRXg8sJqyI//SkdX90Xibmio4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSk5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvIA/9GdPr9n4ahnNXsUBcxMCny++QI5laTZrlkUyUVjyNgZ3EPSMK\r\neEICSdxUP5BMWSswUsJAN24EZ8cGAnIf0VQgWd3jWjcXjZtZnyE8OAE+tFM0\r\nuATLIBERu+SP+9pk+i9F5GENJ05fu2JFtVXnJ3W4/OzxSKINdf/pR/k0QJWZ\r\nI8DYTV0iBPtBz1Y5BGuZhrrwW8jjNylsFADrP1xNXf/m0hEaa65hOGE/8Qqp\r\n/pvt2sAvQrA8SDsE5RRbzee2ke2hRb/gff8Ysyjfcv94bv9zjMANCGGtCY6S\r\ndAY4r6hChFwn+64z5hJo/BlVU3mXjtuaE+3kGeLyYk0gAA+hQmmc1ek3h/9P\r\n8OOiPGU14ZokMBr2zMnHOQ6XnhNM5iTHL4Dr3fQRlRHwDsoM4HXY/m00Nlv0\r\nRjG4kavmj7PA7qCFMNEpz4SBpYoFNfOMGc+4I+THWFBOMXE/ceGKQ0Gt0jxl\r\nfXVuuRsqOy0qBqCSk1gq7VSVm3aSnN4S5F/VlYpu79ngK/yQXtZTbtmczQqK\r\nVw/HhA3NkcP/+ev1F+pK3Qdka7+CRB6URqiEGLfsCa49pK3/Y6tmyXxKY14V\r\nQ1+fbj3922fFuLTCDej1YDl9a4SokdMyElTSBH8rtE19luxTp1+yZBbDbfXD\r\n5hUaqne2s0R5H7d3TQHYEg5QwXQVDn9HBf4=\r\n=dAsn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.12": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "deeed991da1093d5e394ec7bb964fa8130a2e971", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-t7W3zTJf12Gtm/9d3yzKs0U61TViBDcZ73/6xAgyxioZz+E6Yv+V2vD0KE1+zcCzTOoBX3xzViOg+HI2jPODBw==", "signatures": [{"sig": "MEUCIQC0TZG/DIACf/dooaWbqhuOUooCdTb7M+/9qxD4iSLG7AIgRr0qFn/htCdlJF1ZKCHaNtNCluuGipYLhkHHLOHdaOs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5mhAAiFJ8cDr7UnC0f47CWfdC5gnI74cPXR8yTZPWz+TgdLsIwQxi\r\n9OvaaAUS0LfUxPDx6qyM9qyQjnN1PVleL9f+Xl4RgALFNxAvY/lb6IPzAUOi\r\nwh/b+E0b/tCWvRywvzmB9iYMCHLMXJ0kRXdP+GNupn1T/Yq4fZJ5tKIL3xNT\r\ni1+o3WpDta2uq7o2tNH39vdeF/IDXFyPojLB+MKf9+DyWuA0rIvb11aVAkP4\r\nWSkIgLbF1CQG2skLEoGUekjbhjK836TVIV1m26l/oq1rsP6mU2a0bZ9LvYGr\r\nbRkBMR4ftwXdRtgJu6LYiMYOmDK0A107jlfUhjXMli4qU/y2BgAPC+p2AOtH\r\nL7mR1V7KCh3RFvTlWRE4E3c9XtM1ZLf1GuJb3NaGyKYFMYLHkOaQ5k89cgHs\r\n0xGcZiQVtzVsCQKBCL05ALHmgpk8xIcFiE7JpAk51pOmWd9JijJ1+KtE5IEP\r\nHh/h2x+WAU32M3grKE+aCVWxhesYD75j3GgjzWp36jmzhTVlyR8dYC0QA9Vy\r\nngJgMqw9Tfk/uCm5c1gj2aMAXAaWm+pI5v/L0iqiULeRKCAMu4Lv2QE5YKo7\r\noXlp6hozCN9FuxBAJTfW7Bz5PDfJP6B75tw2gZaht1flNadia+jJ+5mEwdF1\r\nPMiCDMibFg7YG9QsKT+74hkegNKbTQ/Ezjo=\r\n=R2Lm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.13": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "12df41b1168c53afbe71d66af53f45f95f62bf62", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-cyPUYh7W7B6e5gJ7rVSDUSuIqN23X/K3txvgNXYZK+5ItDm9qtDEA5aFcGX5n7sAD8z+Q4UqNItg0SKt+gGuMA==", "signatures": [{"sig": "MEUCIQDYA9JR8DNDFa7/pwhp3yyeNaWj+TRypY/ItjHu7tM7GAIgc6TqcCP4D2jqMrCC2ofk+jI9MX5LMJNs/O9OjwFSd24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepI0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTShAAkWHw1qlcanaWLQhoyLx17LUHnwp2u5Lo2znefHtqjpCjee5G\r\nuh4gC/arm2VL9oEUZbUU/2UtKwjXV8d/OB3GVcyDMC6z3LeNf9rbCP/Q/gNv\r\n6Lr/YfkPQPxBXCbDuchnaK+DNuteTvu5VlwzPL2kvCsAkeCwii4qCYKcrExN\r\nlTN1zCpHmvvHUmcDM/5FL/UIsbHeeQt/fr4hK4rmXGpm8cSARg8x0CK0isDn\r\nI3GDpYm87UPkcaMcCYCCacdyy3t7YOshOVXo3ROV8sNBhz+KYyIjGQrbLXRH\r\n467/TabrbIQot0MgLeYPSxLUf3xCqobDNM3j3k+2jQv8zLDEMtWfUcCRIUUM\r\n/YbebYHDhH3m5P4OvCCulnfkmn21cyuw1DH5IPe9G0UIwD8BX3Gl+YSa8rwJ\r\nAB5c3+OpvbMFsdmISEvzJ00ieW3ZSFVshbFVrHVDU+RQGckLm9/mAyD2uNRV\r\nC9rAPehL5HwSDAMtYQYsFhtqabZSwSFfObUzQEBvcfQuzzFqoW1bZLFjx77v\r\nFHRuJ9z0md2WhGGo0Pr215R4uGDUL+VNegun2m9LQpoEbNSP7IEIhwA7EktU\r\nYuJtBVZk29I53Z+3oTx7HKkVpHLV6CWcdpcBbYPMkc5SaYeQN7Vz/RGOeQZO\r\nnRevnJMNyWcpHr6/6cMaFo24o8IBcLapxbk=\r\n=eAEs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.14": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0b5c84417baaf18f57da06c3bdc2e0b9a62b743", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ih5O4YAKa3erXMBYephhb+KUtlxV+0MqyYgPkI79x/qrTlZ9ZrZgxXkM8CBZDPT9X2uVJsnTYIsTBmJp1A+rYg==", "signatures": [{"sig": "MEUCIAH4mZf9znR50YkcWKbtgmZBZ6BPVFnUuspYdNwQ3kPLAiEA5JMwDuyaUrGCWoPnuXmnHxytlZAFSUod3yMCvEppzOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+JQ//QAP1B9PCpeFSshh1dXFeYQh87UF3OGXy98OXhMeWY5WNMvVh\r\nyHbGbhwlfOydRrBjh3yJgCdAT2IBP3yosqsxbDz/m3gvCf9FAx50bN/v00NP\r\nFqBC58GjFsFb9vnaF9IJmHYBfGaMgEsLSceBtwUqXCNdZlO9TcCPcnF6N1Rd\r\nRYLiSN1Wre1sERtcyBgPCStuvy0s+yeoZr81HjNq8MH0NFZUJNbRfuLq/HHX\r\n3lO98oXb+/IS1VhELuXVeIf+VdOxLO8CwY7AC0gCbnLkn58BscSXXmOeJ72P\r\nXOn5Oa6bi0sqSrW3VdNR/VWz8QLGJzqrjOuUI/VKUYhX5JSheAK8KYAxG3uC\r\naqhgCE5oekr0LGGWA+OOaKVxOgYI07hqwHqWkUZhFZBYw7GxZpzTRKnUiEpv\r\nmMgH3Chx4N/69rKH/FOX9LDf0l8Zg3wldG5Brv40eJOo6tG1UeTw8XqmMXLv\r\nTgjBunc7tvAh8SwiGmsQi83TDH3JGFrtjwSTNwdjMlRGti/13FTlELbSmR5o\r\nbE2IMnCvNQ4OxixWEKinYr5JvmEg6gUggGW8+126edmDpDpCL+4k6PERLpqL\r\njswnwWWB9ayoYxAha9c9yA4g0boJXe4nG+tYedvlff+ErtKV1zciTGwc32yn\r\n0cyMbnWSAwLf+ybrVSjmrd33tVrVrlcv5e0=\r\n=voHJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.15": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7896ee74c4a6912205fc563e018e2474e285313", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-x0p5utaMf2N3L/Mdd8d2CkZ+gBD0AqKcePD4FBzak0QP5CztC+d+QRtu9eBgozR6FH8YZVVi0Rk6b1j4p+tL5w==", "signatures": [{"sig": "MEQCIERDbUjBTeHSF6bF+G9wken/3k7WSYq/TsZ0WroPspsaAiAiADGvtwYwqc3ZnsdExWJ+drLq/Nvb1Fvtnz96gYxeKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAz0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqavw//QpPJsGRfQe8DIY3m4xGRLGNfbUMpOStYsRoLNUsDv4tBAOXX\r\nyi4qlcxs0rWt0WCsZiSVScmQzRmsGCPaaEjCnJBHXTvGxGsRidV/cutMGQGV\r\nxmw3JgbHzLq8MjSj1fW4uSVHWayqx2amgIVNZ5s4ta72E73xpMBBAtftRHNt\r\nafamgITwEirdGRoUrBgXCHvI4K5ordK51fK9k3kg3XFaeC8zq9sMtwdrGyog\r\nrpIpXVUqVt8mLYNy2ekpUjfRcVnn8Mp1U9m0GfCC4aTiMD5KEDmPbbYIxWee\r\nZADov0RXVmaBkeuq3QIfzir8d72Gy0MvPigXPDUE7rpYcy+4WmpQB85/4YrE\r\nmp/rz6pJp/z8fywzbP9Fs7mqlEItv/Xi/Qmuggv21D2tuQcRKkn4wC+caupD\r\naPPSJ4MOswzOlr5I+1Dg9RNZHIUPhjOBo36kvwm3nHIfKlwVnwyAS8Lfi1y4\r\nvuhJgHtyI1lIMSkKiCBR+bE+32urGDausv8cWH5jI/fWobqW3HVfR/HuB9hb\r\npf7+LTLQYBb64JR5r1yafEirSlpbhXPPAEHtLUdCSWhMHSdXqrl7ZHCn+wCH\r\n8iN7vzCiMx8XeouoKy8oRXUMbNx46pUEus8XV09kcS9zLZqgQTYzVyum6dYW\r\npeCt226Vwxd6G2sn87Xo9RFboAuI92AK2gI=\r\n=nUit\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.16": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "75cc709a579d8648e9556ed9957e8db0976f8c0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-ZBHkaD1ivXJJnC/NREJ6l7zTRJJ9eEyaA9fitrLoPgFlXzI4x/AH3rOzfUFe23EXfPub8YavcVJM515D1MqNfw==", "signatures": [{"sig": "MEUCIF/fikfnGX/sf7GKceYAyP890ckIFVwxxywH7J3QKxsqAiEAs5jWdM2A7FgaDVI0NJJLtjdMdPzC891sHPNFA3avukU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrReA/9F4MknJnDEfXeW3nwswejLXTqWRWkgl6MZyRPlGoEkgETJZ5x\r\nh1zsN8MgmBL7PivQtek80/jQXX1bkZIBCSsPsKbxZhNwTmKIs1Aek2pL/pw3\r\n35Eifbh2qvxnirlbyDKJNV5E2EgOeWO6G7yrdoN8Ax3fnOtRv0pfZUCm2D81\r\nPar3E9oZG6mdDEKsOAXFHZKhbHiYcb1Ur9fnauKfekvJ/R1zITtZyGdRcg/W\r\nmluvUxbGZL8hmbHIUDFkOa4RIsjT9cS1SHIWPaBq67JHBQ+4pIXlTq5kafIS\r\nAL3AZZnpKrk+MOkSFJwP9RXsFwLlCs4+BbVJhTFIz3t9UaZXi+99yJsgzBE2\r\n95gbVEoE/F4t3WO5WwYJsRR/dCP0RPXYrhzIi1d3opzfdD8jAU8wT24Ib4Vx\r\nndKKkEvjg6TAhJmpV1+qeRWKNszkqI2STS+kV21pUcWJp2RwaS9ax4v0QFB+\r\nfx2LZehO2WXGNZeRmgdblvc7rrrvUT1ZGcm9EJfo2KzCHqF9gm20nCtJCoyS\r\nH8OaJ4/7BfbLSt9bZUIqTDhsdVL/t3VkIG3UDX6dp/5KExe2F9gaPcLVq9f7\r\nXnj5WsmBvyRjaBGrR1Xp+L+AzxxMuF/C645KJqH3g5Eq75NQB3kETnRR8Jdj\r\nD5FyZROdK/hxCrVzkgAVwV006uW2iqq7UCM=\r\n=vn3a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.17": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "60582b2840ae5829cf0767cd664282b324fe0a51", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-ScFi3u9Lnl4vDUI52EtiR+O3M31x23J1flg9QXAqDxiABN//Rie8e7ajxzI82eaEjtWsBcl5mkAKTIJh3aP9bA==", "signatures": [{"sig": "MEQCIHcw3h1Xujb8v7rALtpDfGiYmD+lUuR5V9bs2PP4ePpjAiA3aX4YC8ZIbnPzvg53MV9ez478vHWNEgXbiChJYA5KkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriDA//cUpJ+jFWX893Ge84AJY5gHvXMuw9h+8Fi7uJJtw8DjmMH2KD\r\n1/EqM+kseNdSt9gRI/v/PKUgl9ttwkM68OxwIrXOedRu55HTi/FNof4gFOJg\r\nVfa8KnzwW3a4koE0ZGFwA4IxGIYVmpKmjIJhvi5eHkJ5UB/vzC+CxIOX5H/x\r\nVkd6/ar/7Itd2PUKj4Vzfl9r3VhvQgDlfJJbuGvktYFsgNVyGGDRNlj39le4\r\neuqQ4a//ekMpJx3t4LLbPMyTSb1gNW8R7Bpxaopoh+ZyKpzuCrPlbbd3WNFG\r\nUDHzfMoCmuOjvwhtHNC9Bz3/acrYCVOsVoorLb/H2IxYIqSm1OAUeHOcxNqR\r\n3H1GEz71A/mPTur72SwdF6SE1d8g1KZAM8Lz5fAGf5MDOdOEskraNJLyZvQa\r\ni71KiUqRB/hDK6dxHuTg9518qMXzwcl5FToSXD9unyWw+gaDJNzT1ZvOeOVy\r\n5Ra0xskJS2HH84gdFBOHRKzOxHrZ+caj70KURwubecNEk9Ad/aCflJsBz5UY\r\n30Po/GBa3OtHzGyfBbiMwVAzo0CvVx2fdRpVEgYY0sXQKvAe3r/L2jsOCQaW\r\nX6kzK3Ja6t2bNfK9pMqKHICtoR+wr7qMHVQPSVMk3pwoUED84GaFTkVSYmrI\r\not9N+53m+0rNfxhKT1h27kYWPXxnSttiMRQ=\r\n=Tdt5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.18": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d6d8e74aa98f41f8e740f22067aa4c30718af68", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-K0txrUszW1rI0af8tUBGHTxcfLzMH/criPZirLFBsZ1nJTg1GItJvQdoEAZeP51ooPXRtn0VWdD1va0eHtAGVA==", "signatures": [{"sig": "MEUCIAzNCVn3qtdHXYW3ZMePEub13KB9LT5GAd+tcLXcZ7PgAiEA5LnivJA6OoAFG3gRVzy0uKvdVldfUzj+oL5oC2fYRgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbyQ/+I6id8eXio+Pi78D+MMkGEVH0Auw7i4Kh6qLLq9n4vupUtu0V\r\nhdyvHUuht8AjOI1opCFuTiKT2o8xX9LzTs8mKUi4NsANDU+ooYlDsakp+Ft2\r\nB8iGxvPoq0n5Ja3WSPacZqbry+Z6sxUoKaVrmIJRNO3+avvX4JEZt8how03d\r\nisibR6ph+qe6F33lGAjJRHdU1snuwJ0vni++85uEOgNhPxf/hy0zOTNZBSP3\r\nzwoVOeue7IdmkzWIrXIFPVBYhpw3crxWpXlYXfLbIEHZ5beIqjZzQFahj7+a\r\n41FrMQGJbkz+eP/VmFQHaPy8yCjqsFt62IT9xnX+uP/me6fpcgKXe5QxPtIO\r\nWQ8jgoe5Ka23XR+fChhUEcaDyjfLKpa99GqXMfXYZbhzRaK+GZGswg4sLIzi\r\naNxlTbuapbpoz8QWEGUG2Q1sCG9VLkUkF0r04XB81kTnDOHEuHDT1Y4IgteN\r\n0eK4g2kAejxgsWA0SAGYsqiG3mXv3RKJ/awaRS00ri3QNDSTRoriVrcKT0Yw\r\nmENuOEN0ZpKRlbUKsT5p99IYMSDB1uco6mrbdMkoLRclfMWp8YyfqXXVRzTo\r\nGmo3abUEi5yT3LMnz6MSOQed8tArYoSQJeRo/ZU5yLxJTqEMOqMJbmw/JMRB\r\nfjhhYAYFujJnP5GkCtPE+xveNpW8WgHbxYA=\r\n=eoiU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.19": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa6a759b60fa06cbb817e8d207f8f9e984a8d916", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Y5TVvrxp/LN9VrBpytc9nFm9YQ3+620dIo6N1elCQXUfn9yiw1/cCH0OFU9mBCIyr/3GRNDQTWrPct73z+y+Mg==", "signatures": [{"sig": "MEQCICWx9UIv1rKQIPA571fmjj54FTVePO9TmJFgzNXlJA/1AiB4SGn6j1r0Norzyu/ebyjBhY+UuPqFLOcNJ9lUaLLYSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK0Q/+OZI/tA9FwS6YBJHB7YmB6bdqqrAqoYDesO8/SxFBsI8znjX2\r\nviWW6HcquqJHDyAN+3M7kE/DCcxniIn5Tgb/UriVZAgAr0JOhlY1GWyB4AEh\r\n2svs8h5Zx0kGKGwLdGPDdLm1gwTnE0hvhF2ikTv5fqdv3ZRk7dqGxmBo8HRw\r\nu8JEnUBcWV50haLjPM32iNczAimIQv5s+PbmVhhoExaT8GlfU1H0CddOfJYB\r\ntwJuSuh50oyBx/kxOcHOn//zOFevsZl0jax9+qmIlH5AyCZNmS+ZzL197Pll\r\nW5PLIJbGdaq5mnqdLEWOU9o62FFclxDF3jCN5RowI5rWCfcvMliTIbfoONaG\r\n0j9LFYXnqc/HgN2pZ9WcPO414KTV2HHcduySbehyZ5NmB/HmPy2o1G7sNajY\r\nYWwx5Uo7yjU48YvkmqFm6PMalmpMp7/e8BMf9I/wyhh2BKBeKGxuWh3ujkfX\r\n+B6r9WZsQkwGpnJFEhUIzG2ryHBFKj140Bb5L3sCDdzOiLwkFNmDWz0ZyhmL\r\nGShq9ri4yB3pwG3rYjjU2I0Ay8fpnGdUhxWc3efkLHAcwMse/Ny8OLpIUC1J\r\necfYgWRySQeSx46yXki16XasrAtcf3vNFRgSVpsRwEFSD8h2KOPQYrdz3hnr\r\n6Xi8+urhPVhqwZMDjOdVDRa9QfMxcUB5nNw=\r\n=Rhwh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.20": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3e8692cff9bcf686ee505e56511051128b4eafc3", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.20.tgz", "fileCount": 8, "integrity": "sha512-vP/8O2rbFHykOW49hJNS8CG08MBYHHlc3MXQcLE2lModVMjOlo49RV1IsF2PORtDIDXAdM6bMgOZHJylVLNQ9g==", "signatures": [{"sig": "MEQCIEGfEj19IudxvN5O+DdcUThoc+A6W2v2oy/o5jXCaYX2AiBpmg/J9dW/+apWkopu2tfB1uXdceRwPwOECE9Cg3uQFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNmBAAlf7Bu7vXy2S2FiyVlqvxv44+5ZT3XUVPjgmQmkpFEvNTSpPS\r\nGnnh1FT1CrZXOnMqQOTBMY+j7fLJZhPgx0GE+g4eCtYJkt/8bfZfGLeeY4y0\r\nn4O2a3TtdjNALoet/XTp+Axs27XUDVzqFJ7uCTAY6nUwcu7om4ROZ3vmCbzG\r\nvTjLMsofD8huDPrTErstAvqsQSgyYre/CCX5cH6rTGyNU7087m0sj6Nh81lE\r\n2mEsIGGUYKpHjCHAyI+rto0oYNqIVMi+PeW7vgzlF8HTz3K3kx+j8o6pGzm3\r\nE/Q1vanijGTO4Bnmj/KgvvrqHeDL9aiEfSWByNIvIe47Jw+Bj63rH2258FbC\r\nrKhdssXuipIWsrGqWfPIqMRMOnxxdDTqUr9yiqACtt+ZpMyYZhJch1+XuiBR\r\nX9FZ8wlU47s6Uo22hNJokZ9pj2oQfJjcB8j9zvkhVjXkai77cy0gbM0I7uV5\r\n5Rvm5aQfrMuBHbynd7dG3E/M9ahyiNXPKfLObV1PnNM2y+cqb7L0yAF/E8U1\r\nOkXd9fvWYNA5LAMF/lzUls1SqznIE9K4zC5vqk6LRDkD6Y2nZis3QrC5JBsE\r\n/t40yyDgDanUj+H912CZK5z0iLGYdS2TVzfHZo/oWacVeJ0HRtT1yB10tkfl\r\nczVRKojE24DRlt82XMR71swDuL/qfWHI4sM=\r\n=8Sw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.21": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c3d57076ae234a99ddc74a1763c987f9a90e4877", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.21.tgz", "fileCount": 8, "integrity": "sha512-3kw0kJ0O5JXZuXnFDrNV3kiZy6TgM7SMEXT+7junFaZvSV4lWxUmq6gQ8BZ4sC2OsiaYbbKtzIC7XY8yqw/B/Q==", "signatures": [{"sig": "MEQCIAPaULPmxFlFq3y5Zy/B1NObjag8OVL1IWppsdMh1JyGAiAFKK0yCKOrT8i6SAsz2e+S07iGGP+i7xAgjZyyejSMzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1Ow/+OEW3BX0X8EgycrBpes+1i85kF9OfdnsU1IhsDXjMDk+MEyw+\r\nx2Dpoy8ApQt+QeEUYYa0gHyRYk9AFbWxotdTv/qNVUbagNQ+T9WSHt5noG8Z\r\nkyAG5m1pJsCNpKHk2dHpIHMH63fPGp1xfVXUe5CtfVaYp5ERtuMRQj0/mxyh\r\n0YviaQgjffCOnTmjlIZCq+oHaASWVF0vrIi1TBMEfEceRokr6U0Px7jZnNDd\r\nnYy0EfzSf4T4qSVS0Jn8uZ/oGq1Y+XJBM+3dmuOPKJkhU1wXx6g+CbWzODWP\r\n7/XkLGSFeiMFAXmOCdHwiYlqeflmFkU7Ruu+mPCCNQtpMBEcZasSFwr1lnaN\r\nVsEQAXxkPW+hHk8qWUxnGSb1sX0F/aGZBXtWOqGK9wTiIi913ZEzXktUthOA\r\no7YMoyHtdtomK3iZ8U0WMyekvUG0bCz4iI54t0l44Bd6z+qYD+6/O/pzu8gx\r\njN+pDytY7zHw8X4FqXa9gZu/jS/wSxzvv7gqBda0fgnCSoLQbyU9pdfIg0C0\r\nGyvgTvVVaZcPUQPXHu4jx2+D27G6M0NTChdRcjb9fboV6v9kVzuP+CinGkut\r\nKGCjlgI5gIS2z7PiX/Rf2RGqkEdxN8ul8DFNd723Q+8aedQC7JWkIZoMf5dk\r\nRxBGy2eT8kW2H9O0IAua6TA2L0VykEcI4r0=\r\n=X3yE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.22": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.22", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6138cfca170ca73c5705a5ffea59f93ad46f52e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.22.tgz", "fileCount": 8, "integrity": "sha512-/KLw1onypDR/GLmnRrowhdNd0lDijFUMEfIKHNnR2pPppHh70ffNcDmZqzK5Zc5RCPJhpGcqBJUnSn1Jwp6DSQ==", "signatures": [{"sig": "MEYCIQDNElSbDMFWG8XOYjAOmQqFo50AEkhRYLbEWryLM1AM1gIhAKOJbVvO9O3fE0po4AcSEEUfXRDhd226V6N3n1fgRAVI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrlug//ROeDgSDc+J0uvIO2KQFxK0Lzscp+pVEg10PX/ry5MiBrOe+x\r\nUv7Bpk+KdN9yxz3aHSlwXzgI6fITXPMlyFddmy9DyisDkAxNDxGjNh3+sknL\r\nIxWwDsZvhWM1ojDDYl+ExjSlaTHZbLHIEPmBjtEFSop0udF6xfyseS/1yoVn\r\negXquzj+uzQLciRaRjgF8G+OoKx5BaNsyH5Dgi3qztek9BKCzjY/3aZYkD5j\r\naiJpsISpZbJYZFZ5+ewrgtPt1OOTsCR9FAtpd3CNMnnRNF/M6Va3IVkQ1Nzq\r\nY46XR9hbSFVEwwZDZVIvbItSzRFNd4WpoRu4UuqNIYPYZdbRbCQX/OD49Vg4\r\nQH5KxOAuS1rvESTefv+gXRhxOncHqqz5/PusfYg57vH4iK5ywM5SYVyerGQF\r\nGarreoxKX6Mh41Ej1MFTibtxZTCSeS4CXd+y5CoDV8s1htpNVJVKwmjBUtsa\r\n12wrbRX8LrZeXDqZBm5AYS/vMarPShZc8D41donXGIMwhAopxJ+CTDWW+4MU\r\ngJmTRVR9ekeVicBqnoiYx5j913j7BOEw0ip9oAzJJNL4tlDloTJKvkgzS4cX\r\nRfiWLkYN0Ey7GqpyBoe6KTd8kv347Omi/5xnNjq9oGPGllT+R2c9e4Iny9Gr\r\n5EdIwa9ngrRxgqgBbT+lhcJFyLj84ca+ar8=\r\n=N4cV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.23": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.23", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4e447b6decd238f6aeb0b94be815083432473da", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.23.tgz", "fileCount": 8, "integrity": "sha512-R4XPvAaQmT47T/PE+ruPxz2SO9x/A7lpIwqDG/ko6cdhfRzEBObPi8vAbCOWf/VimWwOKrhnaB1gggduatXPfw==", "signatures": [{"sig": "MEMCIAkGyd9YaAhmRAhasrtHmyb52tOLe/lrbiBt3FSZuxx7Ah9sUEd+Ljxk5KQWBm3UcwgsMsZQqNp/zaIw4Cq/UiCI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvOQ/8DYaFe97L5ZwZ5/KH2BZf2wbAcxJPiRIj7RCWsU1az3Su63fB\r\nTLFj93QCLoFI40/J755dULquftJZesvIs4AGdbaNpjicS9KiC2cxZsGy0Sok\r\nXKO01hemPlYCr9TO3P3dcHADj/ejy1jU81fVMrBUduA5KKJBeoIIRJvKafsO\r\nuz4nCFavYi733OuE88WORFI9TEBB9Ew6K4q3WNJLtWIOunUoDPRX52rPSwbF\r\nPZWRetEoLnJAkdf8rxDvz2CkumPhNeb0ynDkIFZ+1h5kbLBLa2fkm+0RcmMD\r\nTEK4eYLOyTGswKzHNYz2UCoCHCy8k/pt4waUcgxN2DxhTOQk2qoq4fbqrIFN\r\nY8Y5WyRbUEA4dNn9ouFM1uMtenrsKLP7xVuREBPb7APmH8WMMj1LP04VVEw9\r\nYq2sqxZNiJGSkCpjZlASCx6b1RpMQd3xUIFl01/uJ6MIckGEl5d5FKZjNecx\r\nErQFutv5q76IgJttkLhel+YnveIUhAnqg5e/ZKQ122vQItDoy9vO0yzcUyLz\r\nrQT7fnmu/oAx0Oi8KBzhoktJEOEVSa2TdxgBQH3P8FgzyTsx0YHnSnS2sDKZ\r\nE4QjPSxUeRiGxRexCWXwahpdslG/ap50/3Gt3GliP48ssL8h48/531KVVjRi\r\nhYVc8fDl/MQi4T7wywXxR6VFMlnmwXr2gTA=\r\n=+rxF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.24": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.24", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4250d7b4251e0ff9890ec1dc5c2273312ec45f83", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.24.tgz", "fileCount": 8, "integrity": "sha512-xfOuXfhYQGRvs8mNs3YJJDIaFreQHIB+Xsk+ERxAq3apf/6kxKFemls1cMtZ4x3eYfhRsA8AF8WF+6q1MBZd+A==", "signatures": [{"sig": "MEUCIAQkqmki4216z8TlJRZtnfZwCcu3oF9hNlDDwXqPYtF0AiEAml6k30VPHQlBX4WJ90Wli8IBYaU0ZUBv1slPiyxkJ6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6lA/+NQHd3bIUwyGEs6JY7RALtV3sZl3js+Rv7+3+/ULi3bl1CQJv\r\n9PfH9qlfsVqWZ3F8n5zU+D63LdpkwkOf4/GEncq9e3eADsLgpNSmI9Ok4IMz\r\nhEEhjFR3z+YP7ahSUNifQ3AKmu3kdP0ZSKwOHaJVtK8FxZ3olJVVpBa/OoaM\r\nnk7L8HcB6PKkfivxSLJOPynpurQ2NczZ0F56o1PqaEyB6Rl+zzXOTqDOPuI/\r\nk4UIVIizGTogV6U+oVFgTEYewMRfXmpALJaQyMPLyxE9Jh5j6FPTD6yeI8Yx\r\nMSkzXHr9sC9bRaRQvp1eq4czIpSbLOOLKtEqbU2j6GyT1C7AffA745KT6ONI\r\nYCaZ4CJGBgAmMGu98O0+se1YfS9Ld3lJ71kL9UrOnCAtvSbFvwnXI5IRxx0C\r\ngdUIRl774VzanxkOXGcb4LjN3WwVKfETZNZNAqGy8Y3ZBYKMz29uwoHR05az\r\novjAPCu9Jc1kST7AZLHG2Hvb2l92L6RbS+A8EyCR12s4+cTPq+BLpCxiE9FP\r\nGqcVWsESjdapJURQ3rMdU/GYgvOXYuYsArs/38UdVhUwMnCwq+tXECRLL3Yx\r\nD01azmdBsfyJ0SLWrU/DAT9unaJvRzabRPFV3b8eubt3tqwrqLrKAxH6qd5l\r\nOPkC9If6rdRXrUYpgoWVM7yu5H6Pz5uZUn4=\r\n=SVNp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.25": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.25", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "08a4e868c2dc5354497c5b7bece8e2eb63d4b5ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.25.tgz", "fileCount": 8, "integrity": "sha512-dIaaj1BSNbkdT+KdKwl1/5e0BIJ+9JpxJPPS7PVGK9sFgBeEUslSWfe8Xv9/YWazohdVaVhjzfcMkiWumaZnLQ==", "signatures": [{"sig": "MEQCIAx5hQusSeqkkaPfHyMohfp2A6q6jGXLx9jRx/ZJ9FHPAiAHQZdHU1OYen1w8KhBDXEj6/W/9PbjEQKbYsU1LjLYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXlg//VFLOf01d22BfK87WOV8dv/dHWtmcttvf9RW2HgORRbO21i5g\r\naNuVZCkZB1PtBY6biwTGuKwj6/3Qd5asEuKoOoDYhyC1XXVTYRpYN1cMve9Z\r\nILJx47Z6sNkuC3vJt561jSH+PsUAMhk40P/yHfPD0yXa6NiRPtnu9tb6hQzH\r\n0+lTQUTr75eTzcjjxPEfEfEkkNLVwXJytQN0YGhKIw73eL2/LvVu32RGj+bl\r\npkWMyR875ulco9oMAEpF6DeDj2QMJpDmFWRmh0ES5H0wk/6HTMB+ev7cYqLc\r\ntC5tcLCq9HXQvbkRuvOAWU7LDg8XPx/lb+9YXTvWXZQKyVxN+Qnwc1enZsYr\r\n09/7/pyRl7jvdEjk0k6W5tuIdPpQwa7/dnHdSkMdKYU9ANdB/FivNQBAoPH6\r\n17SnHC607RCf221nvve4aHSqwuPfJmlc16nDN2FWtZkgz+ZOrzwXgsUxMfMp\r\nhUkhj6MR0Ny+4aQDY/Ocex8xkD5ML1LQo+dqIqiWCP3gXnUPqDxFulBgcyt0\r\ndPo/Bj4H0TToPOVAHsK6ZqlYrracvJ1dZjglp9IDWI7a03O4cMYEG/aFGGkb\r\nfgUimamw6egdoFsexmvt5DXHZLy5YwMixnhFErIUrEaG4CDLnMzH3ZvAEC0I\r\naEOEPW/NMjhdSwKxLp39E6UTgterC1Kq0kQ=\r\n=IR0t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.26": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.26", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "495eb525ad6b65d927f23092b5da83044d84a541", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.26.tgz", "fileCount": 8, "integrity": "sha512-dZ4GZtbzNmL5IvzGPjGYibKcKHdR2VLeVQSbgDXnd617g1mLTpVlgc7tn1LpXpC989HgYiU5Ep0dhoNExM02vw==", "signatures": [{"sig": "MEYCIQCiN9aA7yM7zTBHMG7DKNLW6+bObUovV8tl5FUsQEJ0gwIhAO3UJ41USuhXmTdYaRnOYcKmiziOoGuNjVel7sdzmpzR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpEfw//baB4mXhz3LtjPS8BQqDmTVeq63pthfpJOTDhfVEbAGl01KBf\r\n5MsfWm3EzNSfBFE7fjJThNSGX8nb3IG88ePfyR+FH8t3bUDrRo/ZUcai3j0B\r\n6bcmTM1SZISRGorlJZ8Wbvq+XuOL2xO/7mWqhM0irl+TYQpHBCsDp+HQpwrS\r\njrN0XETKoGwzYMHkb80MfZdK9fzgH4bBH+zfwiGL6SpsYIVCGvmP4uydfWya\r\nmN1N1NVx2aD+RtY2jOdgna3tTuObp7u2jmoK4eQrZRXSTQSQJUVDORumLyp9\r\nEqH1Qa+ct0t2Vc8JZkWXtmwpWhiMkNG6gmD+nrYcBSxXvzk2+FUN72YD1tDQ\r\nKDA7LAh5r4jFS2gEh1LxqneJpqB8E7Q5QfOkgGP4N0yw9HUiGPmKDD3abrVK\r\ngtTC71F9U+bae22E4jEToh0IawxBjyTcdExQ6K9BIynKGdlI2/9BohTSuyJJ\r\n2tH2UDMnr4znJKm+JLvmfqMTblqKKvMnySHACyQx9XkP7gKjSOTdDAb1NMZd\r\nJ9CXFKXkhqZ/0iqzFOx0aRCQ9+irOsr08AzI52YnYcSVfzluwW/7Ppuz7Y39\r\npWRv9EpPgfhzyCMGj+wSocQYhdQVon7qkGxCUs/z+f/Wtxvm5U3wPOh8r8QK\r\nS4Gjwjf2N+R4gjuXN/CrSg+WkQ/kH7Qh+3g=\r\n=iwJq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.27": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.27", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d2a52eeb1a97b9d5f285591f0009a0f4b5069adf", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.27.tgz", "fileCount": 8, "integrity": "sha512-h98A+UUKw1WAgl3n9k8XuITV/j+4qK/7aYwvO57BIcThy5zxb1Pg+6jL/D2DwREB+dSeuaxJLSCENw4zYr3UHg==", "signatures": [{"sig": "MEUCIE6FTglktWeMPWePPvjBH5BZDUef1hKWCM9+oO0/WGiqAiEAwHt+i/EJlnmnb3m+odcdHPvShh7lEz5DNmMdlQzNLKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSlQ/+MSiSQhHEmr0lZXDLsE7BD4MiBBj9aKWYfu/JV8HghCvsAlAH\r\nK5itdV6PYlz2I3Yv4Gjkp/4GYsnj3cn6Z6wdcUS8RhZUzZAt1Xtuf91cH1bO\r\nV6KycUQ7EEOMlctuwLqeb08+43bF0YP8RswAC9jh4RE5etgKMXbuwVPKEZyG\r\nrNRhI4KeY5l80TCogSNNZJJZ8bfcbvBMgczBRkAZbS37UJ+6KU9U382yHbAd\r\nM5g6L9RN4ckuf+OEIpWdk0NafYAYFlGqNq9krJJDzLrw3jUprFNdKIDVUHH3\r\n6TgvbIeihg9LpnELoibGOxumkfqg2UJrouXxpeSQ0TUlC9raHvZRYD8FcqRf\r\nW1cKHUkCt5LCh76PFWmyaTCDz1j/ig3Xxgzr316MWn1Hj26TdRAin5FWMBqr\r\nrfv+UTuTdSHhCKocy4dBt/EEbTFm4pTNpjAN2e5y0qaOOcvbIzaxna4aKJ9q\r\nrgia6uPgYZvE93phxQn8ZMih8QS750of0MSyOkX+1G1c1pN4qbvm4aRmozej\r\nnIPlI/Bfo1I7/J7PidtG36CVun0ibU3BzgOozx2/ybDlvlldHp++zW/US0jo\r\nGFbaIhhWOYYPC+BY6d2sOeA6yP3uglhRN8tdddw0PbI4ssinw08E4axo1bUf\r\nmNiTN1uojJu0TTVUXaF3RtNyBpkj3bBYRBA=\r\n=IxfP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.28": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.28", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "543efc61142c82f6d37c48fd300dba2ae04e38ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.28.tgz", "fileCount": 8, "integrity": "sha512-LHtLLU4+56CxRFtnupESd0RAyyblM5gm8/3yC24KM2V7wGwrr3MzN3ATQYQmXJDz7G7ZjFzvDBj0dTLx7pW+mw==", "signatures": [{"sig": "MEUCIQDT5GoDXnrajKrW5RwKjevBw/2w4ZC4Vzd9RjfpPHEowgIgPMbOCgqhYUPpqdNbWE4cD044CwP/B3Cxg384Hu/Z0SQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6bBAAhulebCrhyddhSDmzQYIkOx4C7AUwjI4XYup1sSyGj8S4z1sA\r\nFtArCB4agK7eg2Xnvngf6gBF4HZCUCBW0WVKJFtXiPoUyM6vHTJp/aqQk/xa\r\n4ucry3hMfOGJxN3uvZwUq79rJoY3oocdTv5aqRh/A8xRRSXWG99uVAmbI/td\r\nyIxJbfRyKSBB2C8JaHhALlWMrqMXf1+jRzvBmQqvsDPSFFUTskKrkgRpsbks\r\nlH0ow+0jFhwGlH+cdkIDoyDUowSdju3b4uH1w4a5nNJdUGd4YApx6fljjKmD\r\nWKf7ft5F2e67yJci7iKJudUvwVumE7Zv8CGH3yC6ImfJZfUYitEvZi8dMWKV\r\nvNGnIdfRKrohHKL7nlySTvGK7FtUa08uvSG+FoINGhFv6uU3KVxno+77efee\r\nMhbodFTlesAy8DPD6OfEBvXgBeMVeK57dRzzMV+9Wo8ML3IPtguM3wsT6enl\r\n6GKWqjgEZS6z1TN78afjBCKpRL5+StptIiWvQejsBlaMetY2doCOmuTeFbqJ\r\nXALYt8j+H2XIfxLj+tp68Eq8lMttuoxHAfrzNzoBjw8puVr2Z0/Pakjl3Eeo\r\nbbUf7qh2SIpouFCfXNdjt3v0WsZwDhHHJ25so7igE/8nhSiY7YAlv7P40jiL\r\njv7On65IouhmZm6+jsUmHuo6Fdsqy9IboLc=\r\n=6+jf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.29": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.29", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0302edebaa7fde5915d38766ce9957489ec39081", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.29.tgz", "fileCount": 8, "integrity": "sha512-7UTNmU06BnF2IctZCHLafrH6n4gFJhaKkFzgrL3119zky7x8sryI7P87Z38tZHGxaQFheB/qyUEtsA64VE+7Vg==", "signatures": [{"sig": "MEYCIQDmMMRLkJNAHbd/s/bx4mi40ud5k8NyQ6S/YIXBCLRmQwIhAMu9CI0STIrEZZTm/vmrP3PD3NxPndoc9Q02AZFRhDNe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHeRAAjhuKQA4q8a4qHPlKhJvb06ITFyyVI/dfXUiU9VHF3xNem1pI\r\nRJNEmAJ8t3M0H6VkeC/JQMgU1EeUp6r2K+6rnS4KxuwjCopdTZui07k801/i\r\n3Gt1sJ7+4eRbC0yMFjyTv29aVZ5IBMYmS/IGYYt5WG8wSBgMkWZFVqjTcEQK\r\nzR/p9VOkTi7NNiZraebV0Ya5an4BdsXTklZpJM1XlzhCEDjmr9HGC/C4IKJU\r\nAjoo7iY8bgfrqVbwB/qrSoLubmLxOX2YG3pRYfa7yr7ZPjaKrWWzwwwpP4x0\r\nTeArrAO3AKfSiC2HnYYkrYbSdl7cSTbNTP1Y7SbV7F0vbWSzAZ2YKFehftZ5\r\nsM9P67LREMQtucmKE1eK5+2mgJ9Ev3PqbSMH0GoHJet6IH2FItTccYIcz9oq\r\nNs90N/ZtSr1RM4nSsbjK6gEKHaAdliCPZ9fUgzO3vs02QihHW+VnJn0NK8oV\r\nhWUZFb9miAm0qcosxl5b/qiVb6hA8vQ5PNp8v9m47JfZgEQ7SL7/qsXLDcRV\r\nHArRgzuNdURhNpTVKknk5wZcqXct39wxk1Nc8qERNYyXz4Nz3/fNHkGLGslt\r\nrqei+ExoHPokwy8zilaLMsdF0yngBUmlO8h2Dei1RNzOOd/oqIAyavfkzfxK\r\nrEHC70RbZDt2o6p+yzKnAJaAVWJN9Y5jmrA=\r\n=9FPB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.30": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.30", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c60dedb919798886adc2df46ee6322a08a3332c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.30.tgz", "fileCount": 8, "integrity": "sha512-ILVFjnCiyYC/5+LVMAQ//T2zCe2D7FT3G3dorfclSK14FIdJRDtStTaqk10+7hESkzbiAJr7JON1w2k7mm4z3w==", "signatures": [{"sig": "MEYCIQChfAtqV4G+gN/NUvLGAh9cHof4SMM92hqXEJIfrwD8JgIhAMUCRtcKa9Lt3l6jRMNIN/dCO455yOJNMDtJ+rP+a6CG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile12ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPew/+OI3wXZAiyh0WhiGE+jL2zSlQJf9NJHX6RBaPpES7PxMD6mDS\r\nkUXIgOVCTgpJ+ZZJtz10R7fNii85p/H6Z7cXrkyt2Cn4qY5VEoUdBF323oJV\r\nDKuGcQTqxHf/tRV+XKRJDrgd/zlg9zYjMVXc+jfkGGgCSPVEozaz3qnNITho\r\nyzd47euDsh9hoa8Vr9TCzvZCzkGNmC2/sr21cKLi3YQtsR0CFbUtITU9PQqM\r\n+1vjIqJ1JOGq7NMdGEvE49FRP0+Cf/KpXQ8cCPFB6cduFlIWmPj797UtM+wC\r\n1LBni8TIGoHo8TjG2eoF15IrzZT1tC1/1aGIEAdQABJO0HBuhHf6KLdPjh8o\r\nQ9cWZTfkbydWTOCtzvoeVFRALxHZF9XIo4xHWu8Hh+VO3cskWUbN+kI/sfiZ\r\nCCbTJuuILr3qSwYJLTkCR2V8T+XvactrSrPasTpM8E6snsZS7P2aPCcmGxjh\r\nAP9QRgXMLuJHpl3jl/DRjTfYfCV2HaxNKWNGWDJpL9B7Jv9YP2yYAQvxzhMc\r\nPeJa1WqSpHo/f2qxTjmPY7uldQFRGzHbmh+Kc+Yx3pGB8Q/qdTCcHZtx0s8f\r\nbyps2avefMDIawqULhr+frbuNv9OEHLPFE1tNdn41AESIhqPphNNHKa17ECJ\r\nH0RyzCbK8I1e5GfEvOYomiyk1utVkmFWyZs=\r\n=l/ME\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.31": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.31", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1f07f3ed2d9d2cddfc04d45f99dd99d5e8a9e673", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.31.tgz", "fileCount": 8, "integrity": "sha512-2BuPNcvdxplgJhksEbdgh3h4ZIhYyC5WhWJW0BRHIr1i5f3HCE35b63dOz0RCmGc/J/4Mwyw384BmAhGwL4wNA==", "signatures": [{"sig": "MEUCIGERFhLnfcB1snvwGnjsJVnYyKPFdF03XEfORg8TR0TBAiEAmr7VtDY+zeoSPkBWC0zXk/q3s7nJMoA/N4+1sFgRsnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkKA//WxYjF4nvgXTwMOcWACkDl2pbfh7AJ2Wnl95i6YfCznWvRiE/\r\nNS4do5E33d/rFKSKgT04GEGCIocU8WotXXn48KJ8ivQ4ZIKRvYsnLfT9YbkO\r\n9+Ln0Y7PNQRQ9ohHWRVjzr63QXmTfFPKuwXEbdIlJ+43FU4f8y8Z57T+f+tP\r\nn5w/MLwtK6ZwNqOayfWb8hepvKtVAa0GiD5W7J/o2WueqyQgx6RXrR5tzFq6\r\nhll1hrlaOTu9mZwUAMW9w1y/W9BpR9dM1a9yxhcWzTwKBKwMp+YszfBhigK/\r\nYucFD9P1sUr21aSYpfbzipPb4+j6hmkaqWMMEGiPQ8sQUn+lmMlU23eCDyxk\r\nVNRu8bKjAbZd6QEInLlxSYgFqNoUnHkKVgLul19bHcjzccJQyixdIlznGLMn\r\nbsqwmNf0hqdauU5gBcqpGnkAfhVruSnDO7OuyDdkMz9KMPcvlC6Hor+kzRXT\r\nPFcJ9t98io8/cDXfmKJDy8gS9dgdc+yV9cWwdkVA+AwHFSqpOPBz7IpLbo8T\r\nSAHqGi6i9ZgiOfjsIa8Hmm+04ItIeq4+Ni113qEnqeZDKYIhgonRo1SCXkbP\r\nOFpOax9I9uU7pchV8Tlv9eYUEWS+vuBKkP4pAT3oGOEkTZEt9hwwEeC8whIJ\r\n294nt7PUSXBRgF6iR/wRv8WyWw/o21aZ5F0=\r\n=VyAc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.32": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.32", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7c7f06b71b9374ba52ff3fda3635b0aa2a5f0f43", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.32.tgz", "fileCount": 8, "integrity": "sha512-s2kzoinn2VP6FRRD926i9rJ2/jLcozGTMLeCLDn8Gh74QEM2FqWaWOU0z3P4BUwHBjdy3PLN9GFQvapxtLUVEQ==", "signatures": [{"sig": "MEQCIADDX3dgaoMbAP9t+jQQHK2i2adipMlNZJkNMOL1qA45AiBj9b71+y3Ynf/sEny0miguyslCzl/vIfGjUQCCS44tDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoyA/8DKcCi6bOwJOWkWIe7NqfmwEzZY8QrL3LAvqXvUwei1/svgdS\r\nHmVECEaHbpGxR/0KmrkdJbqWNqV9fvngwSGoUwTaNdG2aSVH5u9vKQM50QeK\r\nzx9TVaFrK+NwbYxZAjrKMiLxibfFvPkbeZMFsxjWPhRZqqG3vB/Lm/IjQ3GX\r\nLhS9uVJFekqz2Do5/skXCJEEmzDdFtcnltcl/CdF3Iw9VCigMm8HXroGc6cW\r\nIm0lXWTNl0OrUH8D2t6tc4crg9Sa+19EsxsoESo5tImjc1QrXLKVFtmxRlB6\r\nm2eZhZ1B9pLrIkvGLa37cr905SgTTCdYasd+xPX77vlTyWIbkCnF50G8ldho\r\nPuNodZbiEofdD3mDEQYBYp7Kzt+1O4RBRktMA3tbSZerxP5W3BGcPLf3GMxw\r\n/mrbFbAVB77pRMjYAH4BuKX4FxnqDxV5iXwXpjH0h16MrTv5KPAKKZHzMkN7\r\nA4ELE2VSBKDWN8yLi6cMpsqWVG3oKlERBDOkv2cYjtxUPOQfbBqafEBtOM6c\r\na+7IHF+kjG6FEWNjG0TYtlbWpUOVj8RiILQLpAD5abr3P/tFBypJTDlOOWtT\r\n4i+tZg9er3Lvd0mYQxeIFUdZHc3TB7fxS4XUC+jKEsVVn2Vc4Ovq3mUkpgEZ\r\n8lkFvazo9ghERfH43I/G9czq9PfNt7JKA9E=\r\n=Z9bg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.33": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.33", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e9cbe100093636aca494639508ef388186965899", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.33.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON>u9kyvPaxE976UyvMxiXDztAzlaQQzsc3g40NHSk5a6unrgDc1Duy4+JLYnuVdsb6odp562mSV9lL8LPkqA==", "signatures": [{"sig": "MEQCIAK2ZFlwRnr5moDTZ0lEKhWM3YlysJdNaODezL7TEcbKAiBpsOUPucciwDn/a8pwRtm93FNmc4xlHjsd23CpK2+K2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHb5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodMQ/+MxSvHm9sqWb7wKYi6lG31JT7DF+5GnjWcuYVA0ud4HWMPhXb\r\nRF24m9yk/DYH4tZqvPecAU79Xg0NPYyOpFM0/OZHIYapFNp6UyFyBF7FiF12\r\nCigFsPjly5VpxOBvfHG0g9IY9/LTCgji5slu7DUYXo2GXadwANdX3tPDn5EP\r\nOJHsvuj4y2quUxg+A5FvJGCJW9r9E4fqeAJSnLwCLUszyLJ1ouu1vvCt0adq\r\nsKqqh59EFVcsqLbZws8G49SYaOuqsLgRoySoFZPhhXmJtdcBiwK4UppMwST8\r\n9nWBsSqvjbcuqpmpM6/B2p/SRC5nEnxcwhi+k+i+5QfigiJ6rV0RDOj+TQpv\r\n5Tvw6GBnaMvY6DTsSIcFnpVjF65e8OJgf2GD+cVNr8zVbRBjWGDceWDXkr7j\r\nIO8e7OjYbBqgU9IeBAgGGoQNhyALFfFUl7A+JOVkSVB3tzS+hqj5dWnOF9OK\r\nA5sVn1OpWEkW0KYk3TfpP73ZLRyLdcujkPQFRz4Wre2r1THdan6soM2KTd5Y\r\n5eOmSTdvx+7UWMDjiLYxy4oUj0fGqWXclTO49Ad8mZelA0r+0F0TO91kNkic\r\nvBE98deBqUGsYZs+7C3G1McuJrNQgwCSW3B4YjlHqJ5ubJp/we4j2iO2QXwt\r\nsjk5sUDi/6ZI/1YoLpBaM2gmIKy8iIkOa14=\r\n=6/h8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.34": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.34", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1ca85598a962717f3b6f9d053c47635718ddc072", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.34.tgz", "fileCount": 8, "integrity": "sha512-fNeoodIdE6rc9pJHvLtcd1VxHK962YoNN17TqT9+/CPJPnV2VbSs8aNV0XaCFCTOsgyIwjCPX6+a7VKxougtXg==", "signatures": [{"sig": "MEUCIGzPGPSHorZ7TMuvoL3DtN8/BV9JrfbHBzBcQnmVQKGgAiEAr3vHv0X6qSrfnr+3EiJWN2DAnClV2x+AffMfiSu00dQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMcw/9HcHAQZBye8MUTy8IgWJoQtdJL1OARxLM86cIARHxh6j2s5ir\r\n6zxkVXrihqTP4fJpft3e0YymkGjLbnyaMbSCN1qFAeoxg4fO9OxsAHmwf69D\r\nTQWo3mD+7E64LI0ehX+Vf2GZfMkfRZQIWGl0B2bx4NI5LxxvGu+u4sOiychD\r\n5hJq4TeyEbAg1vW6QuB5DN4I2iGS6NirVFywPwUReYO0zo1SBaHVP+i58Gcy\r\n5eeGTOWv0Q/G48ZiTKEPrdl/2ZvwbCekmpsHnVDs56IQ1qIus6p98B1otNZe\r\nsuSYGXbzw9BBF+GgCqIkqS+rDf6UJbER91vNwzD3WLn9rhHM/xjngXmigf4Z\r\nWh3d7QsHTwWp389BLDd89FdKhoFdsJoM9ccLBrnM2tumfpQvNx3iIaeLwX4I\r\nk4+PeiA9FIGXZgo2teMpRTX/tiQfZANgNTBq6fyaDr3aqEBvUggb0jJtAyjF\r\nSewPUYxGpnvYWLhQiNl18GT07DrFkLkCvX3OemVkTi8fZ/leiQ09Vct3Nidx\r\naHhfRZXH3EfPPDMX6q3u6kFyJK/jtq+B2oMjShbBjq+gH7Lh1P+roqkTcjBT\r\nHzG0uy7LT7/LdbPsIbuFXJeoPVECfSZ3km2MuJeS3KZEvg4t+m1K1hl7PmjT\r\nwF14Q4O8hwaspQztpqqAVFyCYechOFkL8mI=\r\n=CMfV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.35": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.35", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b3c4f887f1fa70ec87bff28e2174f2806db5c4e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.35.tgz", "fileCount": 8, "integrity": "sha512-3216yDD60/rXxZSeKILx2JS2D5QWH+V+xlAcl8gJZvXkjWYJOmWd75yZosxgKlORVejKphQdR7fLJ4C8rnhdPA==", "signatures": [{"sig": "MEYCIQC54PpyPLLURjfslTpKI6idou2iS7uJXMIRjyfiqMAFuwIhAIPgHm3vKQzHRMQr8uz6HPSZ5dUnDvBCJtS+sWZ1sMv9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+Pg/+I3a8XoOk7tRdij4GGPHaWyDB2YZP2eR5Prj4+vn2TWSbzDu3\r\nnkwWOzS0GiTsj0VzdO5J63JaR4d0dxyoDifw4pqIPBRIi9Cuw8p/u3t7KPn1\r\nS1cwZwEkzrbUxBC7DzcvjDcKz++f13an8D8xuPjL/75X3cCN7bQo7HXwpZXY\r\nYv4M4E0oqIVmzhjMN/q60iygHhbgFpOqVLSeZfpZK4LwfGuJ0GAhwkiX2kti\r\nzpHooX+nVBlZUffy+X7SYvIhviCc2VcfaaixtKWiG3IShwo3SV41Zd1+fNzw\r\n5X2geeRW//CPPaOIeWjZnyB4UcVEYa98QsOhvbfSYSNZz0sagO11t7Eszo/0\r\nA5QeDWsnTtwNBcGJg/WmFl4ALcvzYn8sinIYJ062sdx7dlqi8NkfwJSTnH9q\r\nbW5kN3f6qaXgMiCI8wEacVBBplxuL1DsGzroNTiSxDhGNbnJpJV0eQaq3+5t\r\ndw6s7KTi7Q0M6NhhORrTxnMISei1qseByOQtaKVsE0ASE9odo+UDGZbuvp9p\r\nnn4dEQGTK+0mxGOyVl1IKwG/WwWbksKQQYjd4BfI8ByqdztWYMzW3fFqTKnw\r\n66VPB42PpwV6VhpZm3g3TV7XKz34dH3XlzJqMsU0vmSr1yYjm0ICrikUR3zD\r\ngoFX8MZsifIogaFqFc8/CaIoBAfMmmWeWbQ=\r\n=5yO+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.36": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.36", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9bd149816e9161dd4f8caa7e02d327871d51c3ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.36.tgz", "fileCount": 8, "integrity": "sha512-wpuiuo/tmaEsdgCL8TjCcOjFPJHxxu1/tmzVEtOu7nRtPm9+FIny2Gh4ZifzmczS0auaWMUJhY8oPFn4J5JN+g==", "signatures": [{"sig": "MEUCIA4NsvDGJsu9T0IEn7R+JcYXBR+HYjxS/kO6uZevRKjEAiEAm9OZEkYbJJQNmdbcmMjMk97Y7c2+PDFLfxLdtDmVjmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeOg/+N+rhMoI+BTng8JJYXv8nybz+nPZUyVpjez2UP5e8H3eXSzQF\r\ncZlyYw0NzoJDeuXcpUiy0f/hYaEIIBtAboT+TG/orxxYZUvsNmkRh9DY4/8r\r\nTC7LvzXAaO92WC9DP7RNTeZ8jeQAwdo7FfjCT1D7pDucH2EKYOpTVU1o0k6d\r\nwmOeQozkK//xR/UQisxaret/bLA1MHmDsNiwDt524UmR2/V8bT1bj6YxGXjI\r\nSsiq592sjWkCF2NeM167cMDCAu0YkXvObxpMZZ8XPM7QB7RUV8fjsWfXwD+V\r\n1P3x7BPZcew9zz7C8fcfWAMjlVDcT2UPC1hiMpBM95SDk7L17oVYJjlFmzJd\r\nnAg5eEZm2tntZcgXtUqCboQeUQ575bGfFNjCQaChaC2KJSdSteAZRrp1dDcu\r\nf4QhJLp/pGyJy4kqS6DpbbKVZgvI504MDQ8vsZuDCgPSv5S1vbbU2yveOnef\r\nXwlxeS9YsARLBam37z4j/e+bxvDaeJ3NsYNWeFvGy3L1y3f4pOJc3Qn/zPm2\r\ntsa0bUFHIZkFnrskFwYGIEW2t7V4ui1e47+lC9sj+kMoWbooxHijzq2oYAdN\r\nceCnbBbpVt/YkW3IwTcdyWJEwfI1OaN9w7x9gsfMbxX1e23RifpzMBNbFILZ\r\naFacM0AIrb/K3mCnfDimzAxPtpIgB51/NrE=\r\n=GWB5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.37": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.37", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "88d8cb8dfdc904e086e1545c79152e930060d0be", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.37.tgz", "fileCount": 8, "integrity": "sha512-AlGp/lEyQygOzJ7HkQ76YAuL6CPa5sFxCbUV+WUpdnAixx1h5QYmXvVCO177012jcO+k6h85cN75h64ce00W5Q==", "signatures": [{"sig": "MEUCIQCTbM4QxsKE9zeqxzrkHgNQrH9yDFSOOEYL8eCIrHkwJQIgOBwsXtp2pDMFX9/dDjOGfWhbqlO9cBn2VACc5ruY9mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1IA/+Kx6HEWyCI7hJ/l2WYxs7w9/78WXGrXeOB86AXvpgSomYQt9M\r\nKu6sMy3eRDVvSYS13qUSDiv7ur5xFGIAKKAx9q/0XmMVqRpNyl9mgG6BFRnb\r\nlzINLKVm+pHj8EPOWu5weJciMU+2lkf4PI33FBM4vh7obkrK9qmdbmgRxWcl\r\nvq7Scr3nLChgpVHtbijfcEVvlQLNvGY4oySWVJUndHkd3VWjs31minx9MFwL\r\nILQwZUO94EB1vxON/0yh7oXtL5ke1YWS51tn2WzN1K7wrAjC43pTwhdwt26s\r\nLvp6387UzZ5b+BzPeY4vaflqj7so0sxDUtPGd6Vn276k+WW/uMccBfnK08D7\r\n2rG+IcUzoCcbESVmJMw4ObvdrRSlpiH5zQn8HhUgZdo6xjPChhharbJg9GjG\r\nDpsE8qV5cZtDMvwl1T52w98ZLDBuIlTjyudgGqtPsVvcQqXtvkdWcI8eUqoY\r\nPvfzm1Ky8+FYYnq6jwZKX/WzSW6t8JdO1BM/4/sihG5FJY1GdFZwsB4foSkm\r\nYVp3qDzm0cqDEQFZTLwUoHbgDObjVwcplzaGKRKsF+nnK42JUJjnoo/D4eaC\r\n8gdmAlmwfAhu4/vLbjTu55EOja8nKszFDyx6kkNDmkqQCvqkqpXwu8Bmw05n\r\nw/VSinI9HRgY9OJ/LgY3zBnQ+Wghw8qLh3A=\r\n=bAfA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.38": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.38", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d8d7f857ed159f1f0c0d3916555c5849038b75b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.38.tgz", "fileCount": 8, "integrity": "sha512-US4C3iPpKb5vgdF21dliBjUPOYPZw8ZPwcFyhTZRaBTlqWXv1wnxfmFxNCED7Zl7QNYRTmKZPNlDDOOdGGI9fQ==", "signatures": [{"sig": "MEUCIGyLs5h+okxZd9NXG8pUkt0InDwVCUCvm6iFwMa+qwGXAiEA192ucBVw2dzWQOnzcWelREpU7lLm+njrwEltp+/cnu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXjg/7BweMfniYw1YnrlZu+EWPxQGMWYw9W2Yd3OJ0OM1cT2wHeXfh\r\nOI2Akpr/sPu3jpptB6V94mZczIpRpheLts6CQuPc7cnKecDpFSbMhFZtWSqa\r\n+CfEzYVskJdgwigMna9g3D4fEc5l0DcRAuHJloWVMS7F+4bnh0+c+/msE9Jg\r\n37xn5psdUglMRn9cavfAIH2RGArH1JD5PnuyquOsS4Ta/gGUka2NqBgI4UY1\r\nv8K6vCo2Zv2CpiBPQtuBDIiUh3dEXohEdMZNe64vs04Fxa9rPxHO7p7RYH46\r\npTI6495A8n36QTtl87tVXfreGoz/h2ifo8Pvocbx3CqXKBAUW5rMoU+GM+Ob\r\nxwYRZ+ymH66/guy3xuq62eEJA7+64glzRGs7bFVUz2XTSRXi0ZcePiVmaVED\r\nOzoSeL12MDh1ERSqWNbzZLBcaL3tCltTT9hkr1FiTMMLPCdjWfMt2JgChOfK\r\nOXkIcmHBcW5LxKDEKSyQc7tXg56d9ez5Fl4nud6mZ0Vbhh16UbuOOuPJEFq/\r\n4xK85DmQuBsib/3hir1d45/k0MKsi1Sf9exEva3IOcT46bPhuquDFG8OefSN\r\nwCyf8P58umiO0vsj7xuuzOiFjUpXatnoxBBvq+gyuc8MPLTzFgJ3m2flOpdR\r\nF/EiUYxY8LWS2wKMQzAqEPX73EoPcDK9Ggs=\r\n=C+Pf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.39": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.39", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9fcb65d171580bba45312ed66fa3f6f4cd4cc0c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.39.tgz", "fileCount": 8, "integrity": "sha512-A66ErUjNzyT0Nx3JQeNUF/aCq9Qnfott0VMnyy0Xavq9AU6NrBsv/Tx+PTILWjC0KA91khHpj26b6y2rghc/fA==", "signatures": [{"sig": "MEUCIGzMWuRKuf5KiG3jHOHJLpr804THhe/kWo3XuMpim3MlAiEAkDh51eeSiamtfCOXS4ht2N+dxsaIoMjyJLgXSNrf2F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprtg//R3b7bK6ZxM8iAEPotm1F+Kerc0hWcaEaGjxo7Si34sP0uAMR\r\nX3GWQtDWs+VOctIcYAqX3GMzMAou/YcKyq+AWRPGqcE6j0V/VnEsHxuK8jNF\r\nd1PK9H9rD6cY4lKRhCGr1hqJAC9f+abxqrY8WtG5uuvvWVpiwSYDKY0A8D/q\r\niRNrfO6Dm2btRA1mil9wDgsS7ErbETGFygb0GMvD5kFAraH0D3UY0ZUXkV/2\r\nJLsvlPdwrIvphHdgVp6n2cNf8agn2p+ocNCFPUBSvduS73Co+Wew/vUbkNMi\r\njl2KLx5lyfVaKYGWaKE9XTckELlL6e4NB94Ki45VxeU589x7T2p0qa4aG+U2\r\nVALz9NWMJRl+tz0Zsk5Q4SzBiYzMbUgK+8+0KPIk+hlvJQwwmjKtiO6NpBwR\r\nwzhpSfQ+CbuvOAuIcrC95bEFHjM5cPbhOcybl0e2WYwyv9TGreFooq/IGzul\r\nc77jVmkjNPLxTnqMJPDfH3C9TyNkN3x7/Ps4uh9YXfjAsViPw8SGG7TBahJ0\r\nNT4LEHd37cTACzGon4HbduuNFZTbDLEH65wr423Ki3nc7zkYzSGd0M3xB4Fp\r\n4LEvXcHRe2bcqFWiN1+xSyUD13WRt1XI/jsgmN2EMvdqiiodem8c91p+JAhx\r\nxnuYB6CzEMhNCsxvlbAje6jrsVDaRiFIzxw=\r\n=9uEy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.40": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.40", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3887e806b9bdb71ec5839ce4246e10517101204a", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.40.tgz", "fileCount": 8, "integrity": "sha512-tN14wn9XxR7r2b1Klh9FQsWch4DojMudesSc7YQexl/54F6fmSXBqJQQyDO3hsNbiWyyco2MK8wxDpmxX5YWmw==", "signatures": [{"sig": "MEYCIQCNR64DZUEFDymUkt6I4e4qFtuz56SKeFkeYmdSYjbBPgIhAMfdjJTU99+Usf5iCMxT0m6ZZZxsHGuJhCoQHRLY56fr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDVQ//ZpTmJ/1RBPrP7GWHXIAvK7jAzPEt3tfZqcb07fSVxTT6Uanw\r\nUPXlz/OlXiIjfqcFUBnQbQd89eDZCntzfJIAwr5Tc3ld/9JuQGh5rS83SRTH\r\nGpM5uvTpf6LR1BcZfJlGjgVrdwppI6aXHyZzesPEHMAOdomQHeHiheloz7DO\r\npZPs0lYtsWDDlSbbhkjfCvu3mJYykoy16tccdeeGDYWTlTmctvWUoZ+AMyNQ\r\ngTqwGiL21LjFwSkrD5UKisMSbY8LtAQzpTjnz12lUUnyBo6tLUF2vGCtbth5\r\nMoCMR5HeeFucqtiBHunlH307Il2c4wxh/SVETk/Ike8g7SiHqZ/HbhVP0dci\r\n/6zsltOvYzyxlVMqvjB3LLFxZdWpycVVNxzI2JYi7eceYqm0rQ0eSfpmxXu0\r\nFc4tJ5Bx+aJ853pvWuq1j/ylGsYH/XFUrYevo3GBym9pPQANDxJb/YYpzvRx\r\nSS+9bTpQbKTHGCgKMlDWs/R+xW6WaUhFe/HiqTcoAcCds4i43eFgOkGQgxNd\r\nhVF4Xd7sIJONffjRS3nYHSkkDpmr+lMl+kglWDgZUjtrfx0rJ0HU2H+sOPms\r\nCH53NM937ry0tLtUZxsMBux3NBEQU22cSMoe/XtdEV6h1CTWzWZf2tpZr2oE\r\nuXt+VaOLvk+gjBUtPX39QTCDA+6Ewf4PX2Y=\r\n=ACwp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.41": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.41", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c25f90a4e767df2bc764d84f7600d646277518c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.41.tgz", "fileCount": 8, "integrity": "sha512-8saW8f3+tz38IbGYhcfSAcbpx3U6COS/TmnLyZ0w5RKToT7RXLWctOcOU1dhP9XqJ0OeZ08C6Z7aA1MrzDqH+Q==", "signatures": [{"sig": "MEUCIA/YQGMarXjseW4HxtypLZHBKPCnbLTrLV/pQNmNXjxpAiEAhLkUMsUnmqCn4H91ef2C1qz9wZ4l+TnUJanVzL6KPUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc9A/8DMhng4pfmhHWcE+MSRmw8jXmNYw1vpQLK+vMMBbDhwECoif9\r\ntGiA47PWcjGGaR0+KPjTGf4gxQZfytSJn580nlzuCXuQnaW/HGYTl2vXHemc\r\nZgyJ9RpveJDGE99nxlaxXpLNrxZUxo+F/hPNcLAgu23Eqm6UnlcRLmfqrtK3\r\nHUyG3E6XxYiKZa7yOhcer4aFCHOp0UU+KX5O7mEcAiZTKHrjBYrg4oPEWkRd\r\nPC6mOZAo55XJN4QLLXlPNkHn1BhaX9Vkl7/CDbGwIh7epafyj2f9Bn1/eK8+\r\nk9GNrkBdVoDLH1CnnSv/v+dwA2rsJsELGx93nmb4HnhMktPH7bgqW2bOMSlU\r\nMLmGwkyVvcX/0pKGqZlOffxEwpNmSFIZK7qbac7T6gHK+EJe/mlx6QoC9gFJ\r\nd4ophdpnJTlqsn7Uhch0zkAgNdObmtFm+6UQkR2OzScSGSFHbEya9lEuOkCo\r\nKYK33014Sk/VUYAi1xZUU9DsNDwmuS3xQo3uuFNX3UJmZA6jC6gVCDea/5U1\r\nNEycJVU6ta8W9g7XHdhfMmx/gmdjWAqLAh4mbU7PcZLs8KXEdcLonRDnklFw\r\nVs03MayBKynRCNMYgbEhj1XpzltQvE66TweQIU2Op2+A9ANrtul5Q34HqxFo\r\nM4QZ7ntk04krHCBEWb08pe1PKJNF0xPrud0=\r\n=OBds\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.42": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.42", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb182fba31239c2823df0abf7845c98528f10374", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.42.tgz", "fileCount": 8, "integrity": "sha512-scWGQjlqtRslFfKh8c/kghdbsuP0jysJA3eqscd23DSdSLcB1qyrqqhfp5FJU4mZ3f/xoCPs/aNVbwe4HaM5ug==", "signatures": [{"sig": "MEYCIQCuv6npvNx5XSoCQLsCH5jVHSCiEPDYNLs4uan80E1iPgIhAMf/7aYoicwBcSXtwxd23rL1n7F3ghgZfhiIIFVlx/f0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCDg/+Ib9jy76VGb9NNeV9+znXES1yqDAC9pL6WjNLMzTzZnu+OWZC\r\np37M2BC+FABlmG4xRZAqikZutA8Bwk5+eDSL5YdYefw9E5cHQTdYqy3dBXcq\r\nmUSnWBzF1KOpikmxj5Ttv6ESCEHQ3OV/cdD696/3Bp6rYJ1YZKOhBRw2t0gI\r\n312xjLZu/fWkm3pR+8tSIxDRiD6RHVm4hAuBdb8Mev+tzRsxXCKadkTq36hy\r\n+OmE2kzDITYL+DKVTV2wcKnK4IgugSfsFkvXlsM74yOgMxMREAbaxUoquNFm\r\ntAv9H3ZVAOxwhCffd4zGQ38ahwDtyfWyZE9LVqOOnhw15QJjz9WteFNk4Znt\r\nYryAAYxieKU/7Tmki/egjE+vvys/2AW//I0HoKKLHN6u0GHoSKYh5aJdlItI\r\n9JzAJK1VSS98q383A7P2tK6lBuAnUyjvt588O+mchB4h6M2BflTnPtJMPRtA\r\nsJFS/d3nxPfdjhLpiaYgKoDKLxRg/yGWbAOQolvafxgD4cMUX9ZRGFMJyjV/\r\n4mrY6088SPkkzinUbD1XBuWDd+ohkfbEIhzDDPGW0ih1ZwxaelDAEDNGgcbO\r\ndSsqXvSDk2U7vb2V3KRXcAW807e2dQyH50onlo34nEiQJUKPy9omR7Dvi35u\r\nf7sBBKeqVFo0wSZrTtkdoMCav7sZZOIWgzo=\r\n=ZSfi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.43": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.43", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d295a879790a16aee04f2ff93c791018cbd9c4cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.43.tgz", "fileCount": 8, "integrity": "sha512-dhvOcZcxMD//1WEzCIlTgMdrWH+OHU7IYjKoOaFoiqYO5vVeC4KAAbo4hdBy5xgvEmW+riV3iw8GTPMps/s2NQ==", "signatures": [{"sig": "MEYCIQD+MBduHMQIuEpjCHjqJhXFqFyf8xLO1JVTglzHUilapAIhAOv/V2pfEslJFFM80T2F5mGsA2RQOdCy+n8XJzFNgQSS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhOg//QVa0OkB4RCIVlH5rtO4Lp6kEs0vow0SyBRaZOst+wn0/IBOI\r\nAfvT9mrFhMdRCmbWX4a6lgmOYnM5aWY0GvYvndDNaSpSmw/cdmJ927F4a6/2\r\nIGYMMP+HsXeTItg8ZwSOuGX0EVu9WAPnYu6YJWxhEB4YEmz4PlmHdif0Ctla\r\nDFNp0/KuwTLMtHPIp/Ug4zUKc5p+066nazvWCAzsvQsbwJQVXOoIyTJ7bvFL\r\ng8bvE6zto0FAq9YJWN/CFtrMY6+fmE1bfCTSekoZIbtAPkqiIYK8qh6UsRRa\r\nmHAR0IYE0Ay/cu5bCy6jLLiLx8vCcfqvYQxYr7mXzOL0GSujuG15g1BQH9mw\r\n4hhESs4z34UQusXCePjIpd5Ti8y5wOmyOdc3Ah4E6Zdxeiqr8SO5PZrsRnRn\r\nE888K3hTu1JIoY8Rdoajv3MGOIoIqfEkehfzYbo3jKMKxIZ0An/1hL9shF6y\r\nBXrC/Y9JdB6NEKnb41LuT6M99VsuhC1rr0+IgLRzUmd1AFoKR8G4OaGOyFTM\r\noi0ntymdtHdOVZRQL3DtZaTJPp/myS/oAYc9/8AEucO5M2IB1iQdEbJ93BB+\r\nx5aSDuNdqisuSTebbzCkE1+flQ1UrdO4+R42wljSQfcaz4fI7IWcVV7/FmTW\r\nQbctWVmdY0pKVisj8Pg8DO0gxWxDWxTYbe0=\r\n=fdej\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.44": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.44", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ddd1d0ae0abc8dd2b766a2fd769b1d19dda3fbb2", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.44.tgz", "fileCount": 8, "integrity": "sha512-6a2MINj1VS3ekWR+ETnt03N/ZQ3F7C/fYSh+cDs8lL8DcavmeGr2GX/zAXqfhVAHyxjdGcQnEeINAqw9GbPEpQ==", "signatures": [{"sig": "MEUCIQDgrxGm03bjgHUUFDBF4KPPqpSYu7T9twGCEYs3Dm2IQAIgB55Mz2dXwwzxzQ/0WDSH2JqV1LJjipI0PysgCJat4Q4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDghAAh6KUXf6mBQSOKHi05rWSoihL2q53gwszR/g2ii3Nf3bCCdu4\r\nSH6vUGr5KUazCxMkQuk+G0mPPgleiZ7/zX87wtZMFVt7WBirLbbnezh5aOA9\r\naFFqab21vO4prZT7IB9LoxzDkpPSVGfTvXxbk3HLWU3WSu8a9UeCgTArzFdz\r\naAXSrgowulb4CvVkOYjldXK3UkMP86dFgEfjZzUMZmQnVhT4hZLlU9Kt/8Pp\r\nB39wjfcu4eCQX1Kw9wlKdPU4tjpcQQ2B9xVmwBu2cIMs9rt2A/juY3RLKW7X\r\nrFDZTUZU/EEAhLP7PGMgU5OLB0x87P4jCzAjMf2G4Gt/CS9ElTSJbwlwC9Qi\r\nhL+wCnIZg1T6tIKQ1bh+IhrQXL/keZQcCbr27R5ELjM4UG7y6QLwJZFwuG+7\r\nyDa9hhVv5Rp6CsdcSPSDXxCg5l+/dxKh1ICleLa+8Z/jwYGXLQiEE1p0bGXY\r\nnNg2Je+Uk5kMGfRBs3urp7qIqEpnn2GyZjr2QsKpwy4CWFIaI3KerTVEggaH\r\nHYa3N/kEYHwlC2d92jRiYMfs8BZ99sUh+ccdOdWdtksNt7H1HaykMEXAGAid\r\nqkka2iF+HxyCbTuMkAKOmuG/b86xP8fzgyNO+Fgsj0410oaifAn+Wb75enMt\r\nF2f0ZETt1iZj4HKk7fg+kz50jXRzBOvdGxw=\r\n=/0mD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.45": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.45", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "73482377e081d160be56ac9cd426ec04920794be", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.45.tgz", "fileCount": 8, "integrity": "sha512-YEU2WTB7SlgjypnlFpiuokc3WyASFA3D2RVZrIbqh9uvl0nZ1eZSOF/Ysx61KfEMDQIYWUa/kjVxH/wS9J22BQ==", "signatures": [{"sig": "MEUCIQDkg1YEsuzGqVxqv8+dDLyMA13uhHb1cGlapoJWpIG0PgIgH+9myB08frw8W9T1Lbncn6dYX2u8qPc9EIRpq7cOAYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLrA/+Mg8YCqM2SeOhfPjcZqOjzDVYF5NefihtyerACMczVq0S/Si1\r\naNk0+aTZivCSouhhpdd/1ers4bHd5Acoh0orWDJvP431KlbEERCHq4St7NUK\r\niGcnTnKd2T60Yh6dmJ5z6nn+oSI6ErZhJlNN6L4qt7KgfGwBgIIimIYLzB5O\r\nK0VO3b5JOEsfFH/W7KCQriEgLZ/GG5Q7I4KNkkGjbjvHbQTQACZWH62Da7jR\r\n/DJbs5KzRK6NJM6GhmOEogO5CwYODPnkUDEjxFULq2V4FkZeBkF+tyPCSGTb\r\n++T4kTw6ykyEHCUcwHOloBh12mSdl60l3eAfu+YNmiENkhS/hUY4J8nbgBjf\r\niEjDePcSF5DnarPvpVkSu/RBAn3JIHj5BaJesMDHv0cSjww+28CCZ6DL+iUR\r\n988Fz6YcWiWD+D0ec9jvOtVWLDQH/zU+sfCRp7d2Ce8892G/4Ji1IAAtfRNx\r\nsDN8guWn+s2qetuJxfUWBD1Kh8OOaveONM1585FpEDYxtGrovHdU1aYTOFyM\r\nlBqkH/LYFGiCR8yldtgMuTHGlxkKZCXAaIyg3RoGiJAW0Mdj15iLIc72saPT\r\nXYNxph9KHysUn1Sa3+386Qtad68HOiofKZBnDLDynMDxViqauauuy3Ls2h7u\r\nfjIWUvR2sf2EL1LjrDciqRrnPzW/a1poitQ=\r\n=A2Jy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.46": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.46", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6fc4d569564c8aaab3ff1bcab3ff245c9b1f1d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.46.tgz", "fileCount": 8, "integrity": "sha512-o3v+9Q7UUimRhLgv/7gpj4/WtSHePq8IipDZ1wxL4e0UoMcCsVrZLpUqoMYc7yVDHFMmJOUpBmLtwtS+iGVjng==", "signatures": [{"sig": "MEUCICIc8n9oMJ71iZ7cXSK6V8t2ItHEtx5OJzYjxpJjmFGNAiEAu0MJfsFzEREo8AnFXyqnBjBbGxsp+wKzKNPdhD45nfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5Pg/8C8eOruyxuLOvrEbGOVEUEBTqXsk54vWWcbP0g7AGtvyNsR1G\r\nwc2oGDxS4rl9KiQMYPfSoF5xJX2fM2CplyE7DvrH00wNTeoOEw6fDvxsT5eT\r\nUCyjrfpBWD35guJSdZSv41n0NDC7fmsX/QTgQqFRuxBkRiN9HX9u7JlqSA6b\r\njJh+yAJFOstwAr2Du8vLNPE/LRku6Ctlnm+jSrJ50J5Ga3vf4tC/hY46MoXs\r\n7hKmrysjJYta/mYshil93ei7FpBTvZBVBbG2BtAZrk4wulkVU4ChJYRATC5y\r\nL1dZ2msVrO9M+sSNNdqcFbOgjYXhuK7Zg9J5jU9fQTk5JID4paSXy6l3fvDu\r\naDkw2HUGXascK1SxUDozbTnfMiWUUCZamehLCpkuGwYUfVDdvJGzdrBqdss3\r\n40Yb29AH3RgtjHcmThyn83QPtfRnfQ7mi7kw5rjgZQinkTdeLRXBAGf9BFwq\r\n8hpNyvLkiSe7kJhcxupgeXk5hVLXSRD2/9PghL0UY//AigFHGV/jj/gVuODO\r\nRF6KRbYQFBR4mnmY3CGdljAdEyvUHW9pxAcokqhJ0favYUTlMEYwMSDjmf7l\r\nbxxyAuOOREKZD/a5fw98kGA27Tg0cwU6DxGRDyjNKvJMJOjiI6SYNjKBTbmI\r\nXhA9dwJgCSkvFa+z/y8OV8OatwkjYEmO/3k=\r\n=ek5B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.47": {"name": "@radix-ui/react-compose-refs", "version": "0.1.1-rc.47", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d25d527d5bf1ddb394e228abfb9703fdf9aab58c", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.1.1-rc.47.tgz", "fileCount": 8, "integrity": "sha512-FJRPt4lAfYEFVc5XfIa1ICprJNGjldbRN6v3NsQVZjNcStQcgFaNvvKzvaUyJS7SwNip0F5Inm7wX7aXOEjxiQ==", "signatures": [{"sig": "MEQCIEQHA4iFyWT6S4g6tz3f4C5502Zb0IUfY1KSiBHjdb2MAiBqKOnHrJL2dUy0TnCteLKkPdWfyKgWb/HASUzEaFRLYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ6Q//eqSjVuiLewCGdT+djxFKPAhIFNjA5aX7TPzhfdhy9gAUopWl\r\nHsjUWWkaB8dEl5SEfvE9eloNbyAybsEvrE/E44toHPLltGFQiYeicz5USFVM\r\ntj0jUUc8g2WUmh+LOYVBEMuGDTbmix1txw3k7cIq6NIWGjvtEf6v/iRxw4Hx\r\nUXl38ebax+XHwfjWr1g0PPgrdsknGDTJ7h+7k3cTL+CzkZRUFMalnCol3fjA\r\nRXKyP5jlWWk0weT5K8nGkIRxokai981UGeHPHbypm+UfGLa6WqpgxBROHdWG\r\nIB3MU8VvGuW6hkiS9Hgpa4fyH9rFol2oFA16raC12XM2DCNg9z5phFPu/sQJ\r\nkt8zmQTbnONlFbiO+6ZV9BXqFwumpiqvzbXbhA3byxah+G82mu4JMwAPhzMI\r\nYmXjjWop/1+jENPbRHD6Ru3qlsuekRhKjypuqzuw/twOxMNfMNJ12L8Y+9PV\r\n+sBNEZ/T3J6573W13lTS0VkiozJcxx6+66NjHPnLnm/nMBBCcDPvdfD6Lk4f\r\npUtBXG6SWqXjg9oL/nsganqXVHNWeZ+VZV8w/zv5TUj2mIG3Wvh0D6WtFh9E\r\nHsNusUPkmbfkR7f2XHFz9qqiQbGD2Pqogh6N8sdKAooTa0BBAe2KByM6s+vO\r\nXdGScsEU32a94yFlErh6H94ybdpIL0uhLuY=\r\n=8GM+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74091ae5eb27b992e6b6d312dac796befb697b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-cH<PERSON><PERSON>6zoVUogXBiJ5KQJh2OGl64K2ZgjsDUIY6mZJO8TX3Fsh7RidZ7bAZXEufNaaQyZsLXrY3V8pC6tlWEm0kw==", "signatures": [{"sig": "MEYCIQD4mDgHW+J76UGfsJ1vvRD9R3wRnYGr6R5yHxq3TWK7XwIhAODzy9oZtoKWpwAbimI2/P23IJA1oJzTA6Pek/Zx2b9D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Eu5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqv7xAAgpoefAFEriE0GMw4Ld5ynvDL93ZOOIu0aDMQ+WtOHGL5X2+f\r\nPoJyudcIPBmFplQmDGafPPJ4YYtO9vWr8MZqYL94yokxHqettoIMr3HogEd5\r\nNu1Mc2DWwvwbyRs7kvTFTC/t9SHx3xIFlB1GdZWzRu9YObYT3TVxaTOOQ9Ah\r\nRLkRvAfefEM6JALx/7+kESboBWo/9bK7Rm40eNC7i3NbcG6Nu1vQw1fe+jmW\r\n+wkLjH76MO/Vo90dVI2MRL/GXdrF56nmfnA1mh/N/CmCJ7adrx8I+Y1Wj6OH\r\neGtD6+OnU9rrPR1HVV+qfW3l/RdcB0LmqGb6UhXayGNl+7br9I+T3sj8iukT\r\nDXfmp0FPAdCzepvglPdX2AypGlHar9Ca9rZOgjW9oAPMQYRUha4qBl/5KdFC\r\nlZfsVs02lT7XXRYFcRzO/sovW+t/BICgadxnYmkSpkkPt6zJ3jXOOZ/RY+fZ\r\nks3tyoUdjJaY26szgus9tdYtEMiUXCxugCmaFsZeanFr9Et9p7Qam69N2mQw\r\nWnX2UWcK6p4RCEpH3u/c1TCAjUl3PpWc7sLeOy+RWCGv+lOAqqAUFhGMx7Kv\r\nSE0bupdJMIK+Z4yH9VWV8TcPK18iskB0d5f72BW+Ed3dYaSoSgUMzRHi9waz\r\nnaAHriJgcw7uq8zH4wy4jxKnvMyivgN3EFw=\r\n=aHiR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-compose-refs", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37595b1f16ec7f228d698590e78eeed18ff218ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-0KaSv6sx787/hK3eF53iOkiSLwAGlFMx5lotrqD2pTjB18KbybKoEIgkNZTKC60YECDQTKGTRcDBILwZVqVKvA==", "signatures": [{"sig": "MEYCIQCXswpvlfaRU30qDT6mw4WyWi/U5ZSnrmyFU8IXzuaURAIhAIz4sbwmuluiPIUF3UAVPFhLn2Jfo4sERph/1xrXDhbg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8208, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovZw//TrWVONdCwjARwKKIELfO7Upyilq7/9jlPW+8F43PMcdv/C5r\r\nk07M42B2DiBvyydTKKLv2dCjBLhCIeetcWRN7Zx3ktW8ZBbT588uu1CzY+SS\r\nc+aChAEFA4xA4vxtPRmyUTlvuaJqfxqmYsY+tWaXb41ZvfGtrisf5REX1nq2\r\n8n61gvKtwFoQNIrxyFh7aEglhzgweSPSSIknWtGGn6JpJN9VSx4MyiGCMklu\r\n0P/AhfiKmSrYljfoXyZ60rtmjncb/PFFe5DLf9rhJvCm2RnrPTGCZgh1ENp/\r\nUjjTrAzLggFhgZrHLG7JoGvRtthMpVqUKATLjVcBfbLlTQErau5IisdJRf3l\r\nqyvYBjA4Ig789wNeT2pL6z8U0DTTWkRfB9Oqpq9R13FlZNnAZoG/fNjjaqu1\r\n+hLeBveSsrRdez7UMIItZq+skBsFpLNvZh6LZ5nZkSD82S9ipEUyBFOxfOGb\r\nghwaBPTQYyKnyjFmqeIGA8RUd/EOhKJUP7CCFBNpJjVLce3y6Mqd3UbwGgOw\r\n1efVtUgBjuL57EER9JybglKORmrEWqkVFIHw8CDvCXC2G7AzhVxwgCGhRr80\r\nX/wvG58L4xNDBvYv2sXTjgWtmT6xT91RzWcxtvsYFx8t298CKENAfup0sSU3\r\n+iPRU9PVQB3EYm1FiaeTVcIYglGZwOIgVKk=\r\n=b1rb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7e79de235e4f7d47dceed1cbed914bd884a641a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-tG1Yt604oI2rr2cGU0JPj0zy0GTcEIPxskJR/30ogMpToqJkH7mcyH52otnCsbY0B/gAZyTcibkFa6kR8QXmQw==", "signatures": [{"sig": "MEUCIQCOHMIpYDiabLexHBEAVro6Zqyp1jP3o8lzpCQQd/EQBwIgYoSW3VRHvuRg+XLYOfVX+DWXq94j4M+mRaTrWxVYjk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8943}}, "1.0.1-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dddcce715692d2deece066232d46a31d925abe4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-lq4WngCvZWfrlmGx2nEO6dNwWVpTRrmlKpClPFOJO6mejZOL+yWjyXby2OAUIBDuYxMqLjrN3aeZA1GaeUCMfA==", "signatures": [{"sig": "MEQCIFZ9zHxxGVSbr6Z0lVEr2vW6KLetw85Txh23+ZhkJIXuAiBFZ0/U9i5Vx+xrQOR6ZFiP+vbP0MAb+IBcex0Eebh19Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8943}}, "1.0.1-rc.3": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "8685c16ea045a3bfd6d9dae91f3635f0986dac70", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-IW0pvCa3JzQsWW1pnQvRDSLSllX0vlmQbe2SX12F1A1mkDrV2OLzOUNvPS3Q5pBY6hHdwmTUfOtnkk6KjgZ3gg==", "signatures": [{"sig": "MEUCIQC3LD9X6tbLsse9g7d7r1FREvHyQ8Ehf4tOqpgfcMiDJwIgPmRphaUu45LKEF+ExH+Z4yN8LNUugHDRuzzW6eZFNtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9052}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "0d68be5d7284496cd63499ca6df5a3ad28643d0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-0aWglnEq8tLzIkcprLwMLVYuFw/YMQhqtjmy7kpKsLwZ7kA3rhDZH58ejJSp3CUbiT59fkQK4W+IkM2LfM+ZZA==", "signatures": [{"sig": "MEUCIBMMC7JqtQK5cfsLR66s3/YIIBpwkOqqdlA+hPAIg+yOAiEA18vVQs70SrRVzxRN7hDna9SMpHkNN9W61jN7By0bzVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9052}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "8fe005301c3d70647723d7dc23c18142d64a2c57", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-6n3w1cicE9UvCXTSiSDLlVdqksG30M66LKVFqtbMMsZL3os+OLsgm9iQ/WFbJ0V3WQ4m3iwJf9nmQQMlF26R8Q==", "signatures": [{"sig": "MEYCIQCXrF6xNQNs+/w3l+0A9izvHXtTLvEcJnq9dzPgllgzNQIhAKwPmaTq0k/23I6hB0E9SUBTsK3lrpHNbVsQF+pJOTTF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9052}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "60f3b38546e330e9585e2640fc7e1ec5f3615270", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-VBeENWqNNLjxfC1qOg3zfUKep2g2JO8MnAklmXOc9uaQIefVKGp9r2qExvwa9fXg7Od+UwFAtL2pKSha0/fqkw==", "signatures": [{"sig": "MEUCIQCdo88ba74IoW8e0WFwVd8XrYz0SYs7nZA3ipSnj2CcLQIgNfB/8mrgjA1u+a4FUlDeOM/IWw2idfOBF1jjIfAP6PA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9052}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-compose-refs", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "7ed868b66946aa6030e580b1ffca386dd4d21989", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-fDSBgd44FKHa1FRMU59qBMPFcl2PZE+2nmqunj+BWFyYYjnhIDWL2ItDs3rrbJDQOtzt5nIebLCQc4QRfz6LJw==", "signatures": [{"sig": "MEYCIQDoDcTO6QyU4n0Q/odZ4V1OE+M6KMxJPh2tlFPGnL30awIhALZ6aliQUdmmr/GkSm3+6YxrvwOpX+TnoFMdqZL5SMQz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9019}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "3e9f3bf416b0b3f0d8c0b15cb32cfba74f1e4954", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-dZ5yIxr2eVWGxlY6f9dY3fd2HiGZkX++HbOVjSaQCjYV11QxvV5c/Lc3is2QBbKSgrZsbJUOfzvHZ3YOQZrHVg==", "signatures": [{"sig": "MEUCIBdw62J0dd51bImonOBVMNOAsEtFW6gw7C+5w+OGMaj5AiEA6S+gMxqEYUetUwdf1uryZbT5JmXdHN0lkFkBkKVJ/Hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7739}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "b3e7021c6df4100c6542cd58dde3b0dad9ee98e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-xYE1WROX8cPF27q4E7VRCyo96G6YoiOEUGU7v/kzyvCfhWafESBs1elY1E8QOa+fYrIz70KFPykCs0BHh+OJFw==", "signatures": [{"sig": "MEQCIDybQtJlZhpkEKMxblJ4lAtPHGWMq3pXt+W3vucSLxQQAiBACuW5kzlfYA/cCcbRVjHRq/5dg87Ri4sfWx2tlvbhpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7739}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "0bb223f6881ed830769d5fdeb70d664fa8809520", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-jDCDwXAmjBTZ8qmX/7g8zcMYuhiHr8TeiwazaAJjmc67ZKmNXamVZm+jeUyaROKNLuy67MpUAKmTEskDHT3Ofw==", "signatures": [{"sig": "MEUCICxbuRGJ5sWLPCkXzSzemB6Id4QuZXpiUAj96JRsysAgAiEA3HDjukmWhmTlvN9Rxpx6UQLyQNa428tmi12kRAyIY+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7806}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.4", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "addfe8364b5aba253f05a1cc4e927fbfb33d5aff", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Jym4vE8cTvY/hp7IsC4sdGeSpShVN1SCY1XgErn5DtoBIqb6r2ZY+RYzpDGJNJeMP2IicuMMQ8uOdwgJB3KJoA==", "signatures": [{"sig": "MEUCIDkKYenroIGPUpX/urJhhU4CL0CZlOegJRnXjk2c/AW0AiEAv177CSGVPinCz6MPEYGJQqu7ID8ijxyysDeWoIqpfjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7799}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.5", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "81c86f36edcd7338596ae1a7557407efc71a6722", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-YzJrfBBSmfe17kuiBaZwBKL7CSRGRei2V5ENQsLUWpLgGRH0tTNpWKXq9N/9cPqGfcvdfD9i3OzUPlOBLg7jfg==", "signatures": [{"sig": "MEQCIBhqcUkJKewL2wAdjmJ19rNs6UXzj1MhKjvOo3sEI5ZlAiBwKrAsNl79RfsyAYnsTapxf2/plUD6s07IgPqcCTMGAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7799}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.6", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "018e6d64e6c99bde94b5f1556faf2e7ba98db7ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-binUwDiCIJAiQetkNlkelVRzJPwKmplw8/4qiRGWcAIPujxNzrQeLkv/4GRsQm8qDhSqV5KlBTUkkkBCgrScQw==", "signatures": [{"sig": "MEUCIAuGjgJ0V658+z6VkEoe3vCUUaU+6K88VatcnE+w+uQtAiEAkvKQr8jIdZNfA0Ek8QjKQPjnBRfUgdiKBYfcnv9p4V8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7799}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0-rc.7", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "ec1a272b027da40f27f7ba8f43d01b4ff20fa04b", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-EhDWZdTCxaNq3kc5ogzQ55yDe6WpN7wFwfagqM/c1UBLMVR1cKT7fhHLJ/ADbpzkfkAAdwtvyJgtzfIzScazuQ==", "signatures": [{"sig": "MEUCIQC3xdB8JyNvNz8Zr9eK7SE1qzsD0GwZVRNPtxGTu4xbHAIgWg4nntPsPeOPjqB3tFN4dWxAaePTz+P97wTYtX0Qv5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7813}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-compose-refs", "version": "1.1.0", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "656432461fc8283d7b591dcf0d79152fae9ecc74", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-b4inOtiaOnYf9KWyO3jAeeCG6FeyfY6ldiEPanbUjWd+xIk5wZeHa8yVwmrJ2vderhu/BQvzCrJI0lHd+wIiqw==", "signatures": [{"sig": "MEYCIQCltHFsfewIrAi0Fmo/lcEnVb9c6qGp8A5eV2S9Cb0aPAIhAOc2mWejAb9COLwUHAAWKeeZLkUcOAKdwgzWaU1jB4rS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7780}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "1.1.1-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "29bcc414f70c736a9057e281fe3dd69229aad1e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ey6jgIh3GwQts9SefGHsBiKd0PCeVi1sxuT04bLLYouUUAQe1kOoC+SB0D+1SRcjic4qz1bDmbNCynV+MSP+aQ==", "signatures": [{"sig": "MEUCIQDT4AHBlOY6LxdQ+CRE0kgiAKLcQOv6evwXDKGOZfjVQAIgfH0TWg1zbBDHQPpO026Nw4raNqTla0BxKTKodpG9Z14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9111}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "1.1.1-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "fd3a0d8294b3e059b229179f8abe7874b6d32931", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-T/i3oeSYiUxIjfcH0hZ13BH8R7ml9bAmVXr4Jhr0w928Fl2ujXsOPov04Xz1SFm5C6Y2O0lmyCxI0v96wCxSLg==", "signatures": [{"sig": "MEUCIBmUauNJwjuqLC4N3V0oJuXJV5c2+ROv6F4ADwZaidRTAiEAwktWhkfvKnl+rVwc3l7aQMHEugI2okpgwop06/88SwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9615}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-compose-refs", "version": "1.1.1-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "fd9fd888dec5e2fc3de7c9aba457110b09f7b4ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-b0INegnJMOOyTOgDGT3jx4qwS3e+fFeQuVPSTLD4+/9xeeH6FWgFrfTob1d7uoyDUC7rrrkIX2eWcdqz8e+s8Q==", "signatures": [{"sig": "MEQCIC+l+xy9AFbmkZyY2blKd2NlDdzFIYj1JpOpK4rSq1XuAiBeUuREy88UwG8BE95Iq12R0gzK/WGVioxTM3CpGC3uJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11193}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-compose-refs", "version": "1.1.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6f766faa975f8738269ebb8a23bad4f5a8d2faec", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-Y9VzoRDSJtgFMUCoiZBDVo084VQ5hfpXxVE+NgkdNsjiDBByiImMZKKhxMwCbdHvhlENG6a833CbFkOQvTricw==", "signatures": [{"sig": "MEYCIQCpPahYOuphG39KSWpACGlG6Tk/RjtXq0FljbGWrNi/GQIhAIzEcirmQasVC77QngBKIK3bQ1yltTFD4mG/YTVXiMuc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11160}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-compose-refs", "version": "0.0.0-20250116175529", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "29fac00382a2fa52de144a74a95ba124e443109f", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-LgpdkeE85jJqaVEDXW8edRmuflMgCyD86GcLtX0MDOldpk+w+xcG6qoTfUSGyhfmH9nF98qeraoUPY6p/ZBGJw==", "signatures": [{"sig": "MEYCIQDSe2jlscjZ73bzHXtdmotP9fKTuSjLEsVTq2rsqjvY2gIhAOzrVv7uv1JyY0VCy0vNeB73mhLbfdO9cn1w/iJQx+Sj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11145}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.1", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "caf712a0482d3ae448b01536cdea37b07955c532", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-BqmhZ1JFK17d4QOnPJrTB+JTF4a8/lt2rYPxU/aVrduFPF7A49JGjUZo0CvP10R38ZilMCWmhu01yDc6a36ZAQ==", "signatures": [{"sig": "MEQCIDNTmlHT34IeuHEwCtE2u/9h/1XPrUrsQKAYI8Amdx88AiB4swQE4oW1UJ3zg6eeVNnjmIjNy9QcIvkoo9HiVmA3NQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.2", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "993b142ae0570dd3eba8a595f812bf4972a436d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Tts6vntL0FbY/MMVrS6xplFrMsqgdpW7rXHLzRttcFQZLyoWuoplqcY0uzJ9lyzQeVuF0sB3DmYRgccsJLbUEg==", "signatures": [{"sig": "MEUCIHHhhlXjRiGQ4thecKUqkEjaiOP5ciYX3SEd/YVvOyO/AiEApUyWo3CbOIvn9wCLKNAl8wA4AAPpTLFV6i80SEIRglU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.3", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e228cb16c896e8926dd6faa2366a43433a14b5c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YtijXrjoVTF+5ye1ylYNNSh4DXKgb119Ny4mh164SIt9FHZbCRQqh5EH800TNEOUOvWw8WdZBVgMNSu2BDDklg==", "signatures": [{"sig": "MEUCICzIiOdHKvWBKfNErp4QYz8YsmvzsReX4zufIwO8EpnJAiEA/2BzCD5SGk3KR9A7RdSgKMQDZWCUHKwcA0VvDaYBwPY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.4", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "d9703658dc4ca97f8ffc0dc4c6daeaed23276e3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-PlZkt+/a44J2bBVjEBf406DkUNVQ8faPC44i/Q//03HxWDiX6jUGUtJrD7DHzyrstHNKZcsKy01ockGMH4Ivuw==", "signatures": [{"sig": "MEUCIQCtoiAMez7M+FtMNbqlPto72vjbDZEZ0AnJw4pFhFYV1gIgY1C2ysP7EPqfQm1w7M3dGKxSjQGIBYOCqf0C/VU8Hgs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.5", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6191dfa9053f66f6f0ff488ad72410a974bee248", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-sA8AapytHc1abpubHOGk8Uij8v1CG+bFnhdf3aw+fbMZP43h8/91Vrzd51fRAX1JyEnxEbqU1owG98YT2x/FQw==", "signatures": [{"sig": "MEUCIQCG8zxdkhiDbHVKMKUc9xat+sxMEX+o10BmYjqTohum0wIgQ0MsYqWSA2KAG4raDERJwkUbWuzwPefzkDp8gBAuIzs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.6", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "b91b63f81122986337f221e266ed0a310ec4c364", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-ZePLHuDE/4Z0QP5bp3vvxK3WOza/TeiznX7QMOZX8Ktt3FQev5uPNz5/wRMJ7Qg4xy0gAcHQGFncmvCD3LTW8g==", "signatures": [{"sig": "MEUCIBeTNTK9bM0azqVIlX7GbN56XSy35NWhIJw/dEVnc8CRAiEAwoNuvya5TdtlfND1yyEkEu7Hun6bgOR5ZQ0m51vlrFs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.7", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "b409f6b877f2cf1f1f231eeb765035c7813011d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-yKur+UQ4AfEJAz26ZFUe6axzP82uRmQSnvcrPlZGW2ppDk5vT3iup5bwj6DU3xNFKBSpRk/RWQSXLTALxEVZHA==", "signatures": [{"sig": "MEQCIHxf8+cY9LP1LwZZFispuYFYSjkEnxPiDv8hkP5uqjOMAiBeLhXK9BBZC3x/zZ0yLPtMuhVhHclI168RQPfaTs+SlA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11521}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.8": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.8", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "3056f24dc1bf0d20b65b53ad504a9c1b088ff930", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-PrDTtJwOX3qMi+rxaEvnR6DeqnAziKPYvNEYjqRBC+t86TRV83Mb+WLnKXLo5Ukl6eUMDxGYDU7GzWZpBsCO+w==", "signatures": [{"sig": "MEYCIQCcXBW/WEu2uCa+QJUvdWxenlZ9g4p9gw/XxeRD7/LRGgIhANArzDJ7gNxSVWHjKwQN4gAIMIRYM6ksWm/unta/KjZT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11912}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2-rc.9", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6406e1f3be8f207264f8c22aa74db4d2c0d57e28", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-NnO6xGxu3KAMJR5mnoipSB5FAWzxcDzeGReU9p5yzA26pdMmnwJNctIPYvDEvO4e9aDPuVEFKZajiAwWLN/TyA==", "signatures": [{"sig": "MEUCIQCYlNAKDWQS70i9MJ5qQGEz/+q12ojvn+U4fsZdiHfXHgIgf428v5jfxmzyuYlLCV6Gb7aSFSxShk0n6qFkp4uHNVI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11912}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-compose-refs", "version": "1.1.2", "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "a2c4c47af6337048ee78ff6dc0d090b390d2bb30", "integrity": "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg==", "tarball": "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz", "fileCount": 8, "unpackedSize": 11879, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBtW7xvsFIILyeIz/1cUm6B3pCRUkwaroD3karN1NFEjAiAwQkflvGk93Lv9LUScKC1goH+yDGIbJeo8xr85Opgajw=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:06.616Z", "cachedAt": 1747660589284}