{"name": "espree", "dist-tags": {"next": "8.0.0-beta.1", "latest": "10.3.0"}, "versions": {"1.2.2": {"name": "espree", "version": "1.2.2", "devDependencies": {"jscs": "~1.2.4", "eslint": "~0.4.3", "jslint": "~0.1.9", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "1296d6d3aaec523690dbb3426a7843c8ff5434c7", "tarball": "https://registry.npmjs.org/espree/-/espree-1.2.2.tgz", "integrity": "sha512-gmX5+qooJ2H5e6j4DEjFuWjInHJSW+jmvxY5SksAvXxaw4H6/RzxsLqGebOZT5xuh2OSjE9COpb4y1MOOU6/sg==", "signatures": [{"sig": "MEQCIDLFvCNOdEcyujqkI+2hI4cL0T78UjtOiD47WECXFtonAiA6Rm4Y3lF5Y9ZukC9yMj9g43tSBwNi6pgZK+V/lGwyiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.0": {"name": "espree", "version": "1.3.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.1", "semver": "^4.1.1", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "09887ee6d5be3dc7677f2caa237ca3fe129ca2d3", "tarball": "https://registry.npmjs.org/espree/-/espree-1.3.0.tgz", "integrity": "sha512-R4X/DH4z9X6g5YS5aJXcQuZ0uKBMZ9k0yyLGNeXl0zFpTubODU+T7fhbjgBDx4bKdovGJwUm/s9miEGDoJOhVA==", "signatures": [{"sig": "MEYCIQChqolLc37Lgqu/Y6XTUPIL05T8PN3MRrxRhU5WaU/IpQIhAKhmm+UfkXKaCB9bGiXy6xa4Ev/60y35/HUIxjkYoBLl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.1": {"name": "espree", "version": "1.3.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.1", "semver": "^4.1.1", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "2bc942d37db13e9ccbd3b2bec6748f970156e365", "tarball": "https://registry.npmjs.org/espree/-/espree-1.3.1.tgz", "integrity": "sha512-bpd1pGDyJdQdyXSM+P5fqvDJ+FLskP21rQzp5MlHJqFgODnz6JDNriH8kbgZP4/S/773VF1OuUnDAVvjvExiCw==", "signatures": [{"sig": "MEYCIQDtlZpbwinqKWFLylPYzx+PgZQfdcveNaB+KSswhGfD9wIhAOY7qCyPiDm4kMZoA3+QMvQ+TCvxOA6h530OIlL9sIZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.0": {"name": "espree", "version": "1.4.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.1", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "3a30efb6dfcc35b166ffcf11bd038cdd3f73805b", "tarball": "https://registry.npmjs.org/espree/-/espree-1.4.0.tgz", "integrity": "sha512-03sIC6c5My83IwJxdy9pxYb6qgYe4PhbA2VdAfO4DnYEu6aYyaT6vRW8vLfRx/PbJ38Y1o5SWtSiacEVb/icow==", "signatures": [{"sig": "MEQCIA2d/nO2c3wXPhW/FC6cozp/1M6ZGyInr5L5/Zghd+IVAiAiPELQMZGpOHJRjwNK46cHWKsId4OQhfYGonG1liXO7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.5.0": {"name": "espree", "version": "1.5.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "5d171e73f4954fc55ef3830ed578fe17ac6f225d", "tarball": "https://registry.npmjs.org/espree/-/espree-1.5.0.tgz", "integrity": "sha512-U2JhN0kwjqNTgKOuzOErkpHv7tX5t7KmoIptpkye3VQIQZykRkzQzraZD8bNDyU4r61oVOux2kIqdoI5eZ5dig==", "signatures": [{"sig": "MEYCIQDpW5HP0BsKvG1gZm5eVJKxQOtaofiws3pTgpur5QhzBgIhAO0GF/BWA0G8wXS69aFuWV6cPpoc8fzmO1FVyvjYmDA1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.0": {"name": "espree", "version": "1.6.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "0ee9d27cb3a835dffc2c890669c2b8617fa6f9ae", "tarball": "https://registry.npmjs.org/espree/-/espree-1.6.0.tgz", "integrity": "sha512-YyTKHg7591OiZSBvn7QXH6D+PQzkInrB91SplLflJ/PzDkxoKGiVkAs+ySkMJPUSy5zu+e6IjwQiVGe8GUzP0w==", "signatures": [{"sig": "MEYCIQDpix5LwqR8FTRDWzMNLBfX3731g65GsPtfohB/MWUB2AIhALxin4wyVwE0UgLE3qZ5VyXdVLi4nPxx6zZswCTAlbIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.7.0": {"name": "espree", "version": "1.7.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "5ebe793ddb6b43fac71dc9fd379066f4fca4aa87", "tarball": "https://registry.npmjs.org/espree/-/espree-1.7.0.tgz", "integrity": "sha512-3LQ3eGPTGSdO0Z7+BEkTYiSAUp15N/JC0Yg2zVl+MWAqHjz9ZVBsfJgZ6J9JJ/RJFIyGCXa8d8Lxspe6n3ZJRg==", "signatures": [{"sig": "MEUCIQCu23pIVN25rFlR0K8cobR6CSakJ6C+J2DGa/TebdHebAIgWUB/VfCiyda3G7qZtjN1sNaeQdFDItdGZ5qIMte1B5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.7.1": {"name": "espree", "version": "1.7.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "894d7649345479db0997bd26bbfd84c67d021677", "tarball": "https://registry.npmjs.org/espree/-/espree-1.7.1.tgz", "integrity": "sha512-72CyfgiDd+IjxxW4gRQss9SLozq35RwEoiT2Cu0eKCw1xkNR87EtS7SoxYb3GaHBv6m7exHSlC4TWWSVbzgKBQ==", "signatures": [{"sig": "MEYCIQCRrkcxG23/7OkRLRYwPlEGgR1XSm6DzEFlfv9er4DrmQIhAI46QDvTqMw0ViOVFB9wgu6JGm5PZ9LvhZTUuOC2uP9z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.0": {"name": "espree", "version": "1.8.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "a60dc022c42290f647603c9e67cd1350bcdfc570", "tarball": "https://registry.npmjs.org/espree/-/espree-1.8.0.tgz", "integrity": "sha512-TEsrd277taviyUXdmGbKmg6IhmpA+jZGtul6NdTSfUZMGW3TBhXaS2yL+FNV0GrX5XHrX+MndzpoHiG0ATZJFA==", "signatures": [{"sig": "MEUCIAsBx8vClOgAnEI6cCdCxSkdhCndJSmxOK5GpPI3EjgtAiEA3atylU9lh8Ov5r1Kt1Jewmeu/H0BdeRhNEdZIoZeHw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.8.1": {"name": "espree", "version": "1.8.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "be915bef57224208363f9ea6ba8d57c94982c45d", "tarball": "https://registry.npmjs.org/espree/-/espree-1.8.1.tgz", "integrity": "sha512-9bh2cm/D/MzChf7TGSNQOMbhuJkaAxpJcKGnwJL7SWquhwQYKOIPSMe1wbX+5+0Tsvy1395By5/G3U593eop+Q==", "signatures": [{"sig": "MEYCIQDhMTVu5G2wIbxlKRetCYrZ07qnud2CrkSwUYm2P1wRaAIhAOUXYKeLES2glxNkK0Xu/+nBF1hPFK1SQazD/k4Z9Yvd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.9.0": {"name": "espree", "version": "1.9.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "a6585eeb7aa276510e51085a0fd781ceb4cbd574", "tarball": "https://registry.npmjs.org/espree/-/espree-1.9.0.tgz", "integrity": "sha512-eDhFbD/5nic8Qeln1/mLrjDuvxGNHcXcw0vRg3yzF3L+fOfJOdNLHmooHWpB9js7g5q1PZS5Py4IxwU4bKKzqA==", "signatures": [{"sig": "MEQCIAv949/dpv4HnkhNaSbzstnHfPb/QECcJYcucqs4DFx/AiA4x9UmI7agR6lMyYrVMud7ZQJ90bqls5brNwZ3HpIIHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.9.1": {"name": "espree", "version": "1.9.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "f1ac59e5b56c6ac4351f8beb27293c1cee73112f", "tarball": "https://registry.npmjs.org/espree/-/espree-1.9.1.tgz", "integrity": "sha512-Ru6gm7gOa+5DIi+ya8AsoVKt4dRr1oq4cB6yiwYLCFIbo6aHZ7UE1vGkELWsT53Gb9mvdoQOVa9s9AY/jBOQZQ==", "signatures": [{"sig": "MEUCICyknVMbbPcH/da5lFnkWjWPZ1C87KOOFnNX4SJNDrjjAiEA4VOuyPWuOZn8KZ3I99f9s3UFrq3fLlqctE0xn0MRLpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.10.0": {"name": "espree", "version": "1.10.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "33202ddf404cfa3f35e979c9a0258cbd9ec705e1", "tarball": "https://registry.npmjs.org/espree/-/espree-1.10.0.tgz", "integrity": "sha512-3<PERSON>r6OlFIoa9eufSD13qgBezZ9kb4f0oUTX2fM0ksMkvh94whbxkcc5oO3hJH8/VgB5VEmlDU1bYPMbLCjOAZg==", "signatures": [{"sig": "MEYCIQD/1xWufeB+ya4E6XkG7LcYzUPOtBQ4mth5odhZ6/8QsQIhALcXlGl7XgERR/NTWT/ZiPJbH8HcDKa40YnzurTKZW1p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.11.0": {"name": "espree", "version": "1.11.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "862c3033fad49ea1f3c5446fbab7650d7759186b", "tarball": "https://registry.npmjs.org/espree/-/espree-1.11.0.tgz", "integrity": "sha512-iTg4dXSpKFZz04C8nju21FAgYHjDZmvvT3QrrCWpTFeC5NuKks1d/ezATWjd+VIDRQDgfFeOIrg5KrmXdjum7A==", "signatures": [{"sig": "MEYCIQCKYW0egofTEDhshNP1PoATYJowu1Qw0Cq1T/PU3U/6HAIhALa0zwFLmmW1sIO03GwCHzXDtFp2GUDEl4oOCnCt5oVR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.12.0": {"name": "espree", "version": "1.12.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "18980ba9121d325a626d9e732e2e47f524278383", "tarball": "https://registry.npmjs.org/espree/-/espree-1.12.0.tgz", "integrity": "sha512-Ha/gfZ7pWjYGWe7V7g0VvWKeeKRSIKRNcVVtXPYrdW8hQYDaFrflPh/VkVf9qE1gfb8qihbeh+sWxR89KVGH/A==", "signatures": [{"sig": "MEUCIFSVgvZ68nh2abI1XjrIUGRoG3Xdl1jIsaedfW9KVpb5AiEAtHb0qieql7+VKFBm1E/9g3bS0QXfiIuI6qH/uogqGuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.12.1": {"name": "espree", "version": "1.12.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "0bf0e9e8b3982608cd7d09654ecae4e3f124ad69", "tarball": "https://registry.npmjs.org/espree/-/espree-1.12.1.tgz", "integrity": "sha512-prBFxdfJ+qLShkecaGXQ/uHYHGyUpWDy1HFRn9xg1VuQU+wYP3yXcVaqZHDLPT7G+VLOyHJn4TWZm91Vjt2FfA==", "signatures": [{"sig": "MEUCIQCqACkD9g5QwW1tGyuZdoQNPmoRHXW75OAChef7iDrySAIgMCm0HfDUj5MwGnlmbRlrgujwGYjMSreIKywvO5iMNM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.12.2": {"name": "espree", "version": "1.12.2", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "83a510ad9447e3cd7e1a18bc5f1cf7b06ffb7368", "tarball": "https://registry.npmjs.org/espree/-/espree-1.12.2.tgz", "integrity": "sha512-NiUg4ss+KAkjM0hh/2rbTWHPsXl0RVg+Ar+aQytCLaeynA0bn9uYzXPjPO7twvP5EMoedZ5/FzNvSDqN7tEBUg==", "signatures": [{"sig": "MEYCIQDJwSfMM5NEb5iQfm4oH9Y7wte5xUgX2vkMeaTXVW/oeQIhAJBsG5jHfcWyh0IKTQjDujbh329sU5EYqRZeyheIiJQh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.12.3": {"name": "espree", "version": "1.12.3", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/ariya/esprima#harmony", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "04ceeada91bda077a38c040c125ba186b13bb3cc", "tarball": "https://registry.npmjs.org/espree/-/espree-1.12.3.tgz", "integrity": "sha512-Ssl+UOnuLAmOSIxgJorNS8B+EfhANzOHjRUYeYC56HWvX07eek/vNAFQAvmxDOXn/ZJBN8n7159THd2zH3n3SA==", "signatures": [{"sig": "MEUCIAPgYi4WcpXmm/WbRj+sV2a/wyRVczDzHdY8SBdi2Kn7AiEAmdL5TgRXvD5/r1BIPsJ1frnczjwRMOSZfcwSjQRJ71s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "espree", "version": "2.0.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "ec62909d5092f82b249976904b79aac44c21ee32", "tarball": "https://registry.npmjs.org/espree/-/espree-2.0.0.tgz", "integrity": "sha512-OUfZ13GVZtFCype2pfboWN4f4JRFx4TksG4VKh95MagAtrEGhNPfg6+uHWCThyU0mm3J9Zv1swwp/4AwBuewGg==", "signatures": [{"sig": "MEUCIQCYrwwTzOvj9+HvXMFtOl8Lh76MAwe59JPhXqGfvv7V6wIgD/Vizm6ndU0UpQYe1kaO7zf8XKKMd7ERnf9ezBw3vOc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "espree", "version": "2.0.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "2deb320b95dd1aa5b3ef24ea038d0eb06ed0084c", "tarball": "https://registry.npmjs.org/espree/-/espree-2.0.1.tgz", "integrity": "sha512-qZCRkc765nUVVpSMJfu6Cc3KxIKKBsRcPuO/r0jkxfOodMZjKEvO/raySvkFD0U/r+J+xXu/1VcmopSqwxtFkA==", "signatures": [{"sig": "MEUCIQCu5S4ZeYpELRHYGQWzjS2qCV7NA47S8DiP5NvsAwSGQAIgWM6WMSm4N5ByhoH3hI1IMnXKdINkXSw66qGTCJjpUWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.2": {"name": "espree", "version": "2.0.2", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "adaefd803ad501779e2063b357549ae3366fd14c", "tarball": "https://registry.npmjs.org/espree/-/espree-2.0.2.tgz", "integrity": "sha512-KbEypOA1jIbIvjr9/tGHVQKIBhE4euReCP8gr1AcVUAz3evSPeNJpud7q7vEgN263o27ljYvO5hlftkpQ7GJOw==", "signatures": [{"sig": "MEUCIQD9sODWLcJNP8egZxuEFcYGNv92RWd51veeRMxZ+lYWzgIge3uHtY2L/EL54aymzsWkCZU1Zc3u0p13iTJF0E5DVqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.3": {"name": "espree", "version": "2.0.3", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "1fbdff60a410bd0d416b1ab3d6230d34b7a450e1", "tarball": "https://registry.npmjs.org/espree/-/espree-2.0.3.tgz", "integrity": "sha512-QpZUJ0lrpSMqDCuh6kCIl4O0VIqoa22CcBUQFGO5jMZUiQibDkwhfaN0cd11XHDa+rFANIQMf/kIWJXbgpCQ2Q==", "signatures": [{"sig": "MEYCIQCbYi3dGxmU9qSA8L00YFwVAbGSzqYdWJa2kd2q8M+eZgIhAOs/bwbU4BSVSsbIphAxv73RtdWR2tPBLEDHO2URNao0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.4": {"name": "espree", "version": "2.0.4", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "679289cc5aa99923540437905288a642f21b90e9", "tarball": "https://registry.npmjs.org/espree/-/espree-2.0.4.tgz", "integrity": "sha512-DBlFFjARkyOy1BeJ+SU0Bcz7QxrpYnJlAzF7TYIvRbL+DIdAaQKljkQOJWwto9KrPQneeZM8KgzTAgmJjF6UNw==", "signatures": [{"sig": "MEUCIQCK0LsS4CKlYVKzkZuwBWDUsXJ/+mw7NtflnLsJQ/ON7QIgPNkVUr3G1z7BMVTc1UQOX0q+/8kSk1/1/xbohZyvuKo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0": {"name": "espree", "version": "2.1.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "1b49b753040338f5684e47bf7b5268189baf811b", "tarball": "https://registry.npmjs.org/espree/-/espree-2.1.0.tgz", "integrity": "sha512-NdqdHMp22PJeXZXeiJ2WSHnBD6nzaloj9TgqyOlDczQYH8lTwcTpDATm0p0oJsqJ8J14L/95byuh6H8sXmzidw==", "signatures": [{"sig": "MEQCIGi32CLkG8zvwzJLmCuaqcU1ydV74R8RBewvp2yF+WdeAiAtKXaFoQ/iPzpyIqKKDBQhmrMfW1fsMzDSk85Lwkak8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0": {"name": "espree", "version": "2.2.0", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "01dc927a7ea5081d1a7b6d610249e624e7fef3d6", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.0.tgz", "integrity": "sha512-dBZlDiXjkR3ZuipMVI9O2fCIk9MIYBrSmz44Z1upE7Nj1KUBdkKKy6X/cWwx8Pg86PSDWxDMvJ9gyCWsg7h5Zg==", "signatures": [{"sig": "MEYCIQDNmne6FDgWcyR+1kNLFHAK2LmXobEXGdBb4KQGHwo4AgIhAJYDmSOgsSFfejgSdTcny7UqCBURwtp+1HZ3XUlZ7b0I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.1": {"name": "espree", "version": "2.2.1", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "2f5a9801e85cebae03470fffd41b50653165e25a", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.1.tgz", "integrity": "sha512-txRkV4DIepN3VGbi4aFEyrT3h+ft1CN0LE3YugLJAkvc1guEOc0W3PBnMoNZisyaeId+Qec6a0hBTumVqDwEqw==", "signatures": [{"sig": "MEQCIH/6LY1iUcOMYSNHJyZp6WAW0nqMWQl+qVzg5oaYGZMkAiBC4Zpawn1LFwB162Mqsnky0LtafryKc7mOZymA5JT/Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.2": {"name": "espree", "version": "2.2.2", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "16af791f3020ea4407e1f9c9a290e9d2d16e2756", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.2.tgz", "integrity": "sha512-vntT0XOyolYYZhCQXxguGwWPSfNB4OTBqf35r3DQDHIPnKJGrOOfWPrE60ADLi8OLqa+0/UgMBfBy1Qwe2+xjw==", "signatures": [{"sig": "MEUCIQDui37wzK64AjuR8OF0fpDEfdRXTsWpe90ChMUuyhLHtAIgDNq+dWYoat9NPp8sZ4dceZzIGKqL7wC+THuQvA/SJFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.3": {"name": "espree", "version": "2.2.3", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "158cfc10f6e57c6bbc5a7438eb8bc40f267f9b54", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.3.tgz", "integrity": "sha512-Fs2ZBG1sDnZh71Ml0c1BrWjzqjia7lfEjNtskyqKwZe0KBfqXJ1zIm+b5ttRsqEi7ISi1ykhBZLcEeRbFxjcdQ==", "signatures": [{"sig": "MEUCIBVcH7csia7Tg2mTGcJWyH7xW2V6zbdXmao1JAhYJzbEAiEA4q/REC0zJuYVLaHTrvsgPUXUWh1y8aIaUpYkzPdwwh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.4": {"name": "espree", "version": "2.2.4", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "1068771b2c91aaf26a62ae4f9a46e74b34481219", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.4.tgz", "integrity": "sha512-tj2oR3+DDC0IwjDID1mrCbniOWA3CkdMu/WtxAra3VptJ0w2EZ0Sh4Ggerm23PcBBbLkBte6TKSc59dsJa4EBQ==", "signatures": [{"sig": "MEUCIBrRnVBcWjBlxX4FPjw8dP7KDQby6NhZW7vbDaqL4M1GAiEAlI0uQHkJuunOwLCpJrt1kaeja7ah17xg92ptYwtxsUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.5": {"name": "espree", "version": "2.2.5", "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^0.9.2", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "df691b9310889402aeb29cc066708c56690b854b", "tarball": "https://registry.npmjs.org/espree/-/espree-2.2.5.tgz", "integrity": "sha512-HWJpgkL44cbjWiOTC9Pm34RZE57H1g9V4Ln9U14TUtiywFTLMMpMCtmQK5rkjbGBXigQT8bS3r45+Dt5+m0SZg==", "signatures": [{"sig": "MEUCIC5AS2mzBbopI5As2k6FUpdS9K7g7+uG9YCJ5yWlcWaRAiEA8xSSUwGbQUn8/uhyqV6BrhVeY22acbJf3XfHSeO6h1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0-alpha-1": {"name": "espree", "version": "3.0.0-alpha-1", "dependencies": {"acorn": "^2.6.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^1.8.0", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "acorn-jsx": "^2.0.1", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1", "eslint-config-eslint": "^1.0.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "ca1380bd81f2fae94b2638ae7cc449b71f91eaa3", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.0-alpha-1.tgz", "integrity": "sha512-HIv6P6qCt3ciLWri1KrO7EPigKPetBZwfCf0o9TuAxRBEPoUUisCepsZqvM76xRfQf2sheO4BC5R/w3UKhwx4w==", "signatures": [{"sig": "MEQCIE7S6bQ/Qo8KWyRV4CcQClIa2OXEsTLJI7Q/M4HYmqlnAiASD9fgudM1mo0s2AnTDcGnmsDRwssqd+PfJj0BCPQDcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0-alpha-2": {"name": "espree", "version": "3.0.0-alpha-2", "dependencies": {"acorn": "^2.6.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^1.10.3", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "acorn-jsx": "^2.0.1", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1", "eslint-config-eslint": "^1.0.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "049b07f21c18e768fa1ee07e18175dc1e3a43877", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.0-alpha-2.tgz", "integrity": "sha512-I9h+IUEMf5T9MVhUk2RiGtt41JhzOHrZsUfP7e9d6prx2ET6NM6RFyMa5OBBFun8La+Oz+yJdkyIzZl6tnIrHg==", "signatures": [{"sig": "MEQCIHW9+Eo+xeygcnQ/x7HtheplShlnlf/6DzJX0sDiaHJMAiA/fsnXnE12cDGsHBlWIeG7SFIg9J9YItx5sUoQGPhrTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0-alpha-3": {"name": "espree", "version": "3.0.0-alpha-3", "dependencies": {"acorn": "^2.6.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^1.10.3", "semver": "^4.1.1", "esprima": "git://github.com/jquery/esprima", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "acorn-jsx": "^2.0.1", "json-diff": "~0.3.1", "browserify": "^7.0.0", "dateformat": "^1.0.11", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "npm-license": "^0.2.3", "unicode-6.3.0": "~0.1.0", "shelljs-nodecli": "^0.1.1", "complexity-report": "~0.6.1", "eslint-config-eslint": "^1.0.1"}, "bin": {"esparse": "./bin/esparse.js", "esvalidate": "./bin/esvalidate.js"}, "dist": {"shasum": "f071b59571358325a85ef44673e75e9af3859e69", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.0-alpha-3.tgz", "integrity": "sha512-Hrjer70z8uk/+fUQDgfiskaCvn1DwGbGKuWpXThs6S68XbL97l3w1CzQ999ORM4+VFEd3kZ+8fBSvmLX0xpr6g==", "signatures": [{"sig": "MEUCIA9OMzLJXUn/kf4K1HW4UWKr+CbGfrKdxOkhdZQknmIqAiEA3gQl1lIqjNwMcJyCsO9Rpl3DmXQmYImRpNwv/o/frKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "espree", "version": "3.0.0", "dependencies": {"acorn": "^2.7.0", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.1.4", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^2.0.0"}, "dist": {"shasum": "aabe3986325a4798260c340de2694c2a0c8e04c2", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.0.tgz", "integrity": "sha512-xhB2VFvXF9d6StATekprYGbgEQwJDXgl1GPetraoXEJBFBGmsszHNxxppWPys9pv4+2r2BrlAGF2jQ3iWr4CWg==", "signatures": [{"sig": "MEQCIGDj3Zmg2pfbe153Zcski88B8YkxQ+asD+wXo+ZOAvv5AiAWEwSsoFlky5nt+OAssGA3UeAUsjLHza7VHsImlu/Npg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.1": {"name": "espree", "version": "3.0.1", "dependencies": {"acorn": "^2.7.0", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.2.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "85a5c95c168563159406b698d3ee7d4c1ca2073f", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.1.tgz", "integrity": "sha512-NuvdpYBRAa5aycKYjBlWlxBsEyVIBB93wmhOm5xUxdnGg4Y42mphig8Aedq3ILKRXzBXlzMUu/s1INDlfiYPiQ==", "signatures": [{"sig": "MEQCIDKSVqoM7TWdaSvtmUxA40hNlaRvt8/8frz8NXkVqTrUAiAV+gaUTHpfmi0rM+Ftsm3urIYdr43ft+tK5NxaJAlj4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.2": {"name": "espree", "version": "3.0.2", "dependencies": {"acorn": "^2.7.0", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "8d4db04ea74b5106aebd23817db7ca43786edf14", "tarball": "https://registry.npmjs.org/espree/-/espree-3.0.2.tgz", "integrity": "sha512-xQb/pN7PRFraMaw35R5I3dHwKSOjUF8AWGJ3VwkC1k/AP3hbmJdXC4Y/tba9PhwMUlr1EFhTFx7im5m4DPkX4Q==", "signatures": [{"sig": "MEUCIQCMKy+wFNC5UPxGEUVfVh8oCIBAY42qmyS3hrMivsodbQIgZiBkGB5ep97Bkt+geUvWVD3pG7sF3U8Ue5YSYprTxdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.0": {"name": "espree", "version": "3.1.0", "dependencies": {"acorn": "^3.0.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "527c24d5032567ddde877b31f19ce7bebda97b81", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.0.tgz", "integrity": "sha512-apVr2hmT7qBAG0ouQt031IkpHIFfF1ioNg2VFmT9IITeVu6pNpJHSYq7oDZGzffgLIRFrQx0oPmoE7HSZ846MA==", "signatures": [{"sig": "MEUCIQC2idUdEbZg3Wsqu/4kdXaaq5vY2Jd0wWU8ekVgRMXdiAIgG0uF0vdgbKAynce1PGLMfqB031EmUbNheIA89ob/PQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.1": {"name": "espree", "version": "3.1.1", "dependencies": {"acorn": "^3.0.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "54d560a12bcf414a970d6527adaedd9f6d7ba95b", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.1.tgz", "integrity": "sha512-LqCWYwgodqPbnz/ABP3OAfQ6q1P6K0HSX3XLxake9pmGIxYqzho3+dq2/gViktRFLv0Y/CESSEMLnLf9tsuoQw==", "signatures": [{"sig": "MEUCICHgGclVcyWkLIenMn96S1YXBsyAv4wiAzG0fC5/g5pEAiEA/zGK9ButrWg0gUEFzaDfzWTHyOkIlte0tl/+SNourcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.2": {"name": "espree", "version": "3.1.2", "dependencies": {"acorn": "^3.0.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "b9aec731e9c1a8e14cba9ee4699a7524fa6bba0d", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.2.tgz", "integrity": "sha512-kU4LmBblNBvVzBTvodwFlMq+oGfp2OGWo6cxZ1XTrnzYuvePfjRBuDeHteFIYvhb0rtfQ0Ambq1Z8EaPZAEiSQ==", "signatures": [{"sig": "MEYCIQDVPZ818+RNo17xSAqaXb78P5Ta555yvQ2STF1M4I4b8AIhAIoqtBeY/ik1w6Bt9PXkf4Zp3r/Yl8XOjKfvj1BgIyL+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.3": {"name": "espree", "version": "3.1.3", "dependencies": {"acorn": "^3.0.4", "acorn-jsx": "^2.0.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "optimist": "~0.6.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "a77ca630986c19b74d95541b845298cd6faa228c", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.3.tgz", "integrity": "sha512-sWWih6r/0x15p84q4VReDoM+r4nwyFCyXqeF3VGywOSs5ElaNu5SXS23QLglzFyOfeAbHvE81edc02KA7ZoFbw==", "signatures": [{"sig": "MEUCIQCvsBLus3XQRbsanxQST4IlFT3ZIwfJR9eYKYooP2whxQIgWZxn/tRFZqFCpkgIzUK5By2G/G+kdMLIWCsVoZAZIyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.4": {"name": "espree", "version": "3.1.4", "dependencies": {"acorn": "^3.1.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "0726d7ac83af97a7c8498da9b363a3609d2a68a1", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.4.tgz", "integrity": "sha512-D5xv+qBAZ0CsmJqXxA0qPoE0d40V/6KocHjQS4/9AZtltoyfWbh5DKA9a0HtLM4TLg36WP2089eEM37Z3urJ2g==", "signatures": [{"sig": "MEYCIQDiz02pRtOYlbj/NrW7edckGFFrliknxhWi7Bb23kE7BAIhALT3bWm0NZJACVUJqT4q+EV5cdmy2oUeUcvDZywf6pZY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.5": {"name": "espree", "version": "3.1.5", "dependencies": {"acorn": "^3.1.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "f2d5a041cf19789dc916941be7c0b554f93bc33e", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.5.tgz", "integrity": "sha512-NOlNm6+fcXj4wNoeqpah6FPXehgNMOHT2DY62/EoMT2G7/DL3Ffo3zi03KLUg9yuB1vi/iv8r8qswgESVSe/Gw==", "signatures": [{"sig": "MEUCICkMOlUq16DlCvc4CLg5EpbrzPtlVe2HESAlO5a2UuiwAiEArW830EL+8gDafppWBGzQ+baJv+K2zK/jDhMyF5iPrQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.6": {"name": "espree", "version": "3.1.6", "dependencies": {"acorn": "^3.2.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.3.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "b26f0824de1436a0e17146e65cdcb728681e21f4", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.6.tgz", "integrity": "sha512-TGdfoOqvLSLtI8E6MyYUrnNe9xyNa79iJUv2BR8y5rsa1YOo/nlRxMXye+mwODdxt+mYed+e4XjSE48+gGQ79Q==", "signatures": [{"sig": "MEUCIQDLkwMnfMmV1EKuY/tZ2Y8wsh3JTswGQh5eM4B9NPkqbwIgcw6iTeLL0lpSlUi39KUcA2xyzSJ/49v5VUM3r9cqXqk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.7": {"name": "espree", "version": "3.1.7", "dependencies": {"acorn": "^3.3.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.6.4", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "fd5deec76a97a5120a9cd3a7cb1177a0923b11d2", "tarball": "https://registry.npmjs.org/espree/-/espree-3.1.7.tgz", "integrity": "sha512-VF3ZpqctFaefWt4R+7jMidx4BUMbd9rxaUoM1gumrgDWcKByFT2YSV73vT6rvdCNw4ZoOAR2z7bamCg4VN9m0A==", "signatures": [{"sig": "MEUCIGwkL+JHfcdgOVx/6cR3bb/0aJM0bo8nd2QG6dLVYpWQAiEApKhzghlaeJDZHhgbAIDUb4Qa1VtCen5lmEYutSBW0Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.2.0": {"name": "espree", "version": "3.2.0", "dependencies": {"acorn": "^4.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "f99e18207c220f4c4c3f88a22ab3ff809ad06923", "tarball": "https://registry.npmjs.org/espree/-/espree-3.2.0.tgz", "integrity": "sha512-+1SZSrUKN5wsmfBgt/1+e2lIzH6g7pBDQNK39M86PspixvjtgUB3AD/QgKEazC2pHPlt8/lkPXOl/Fo+XnDS5Q==", "signatures": [{"sig": "MEYCIQCa2qeFzfhZwpQV94OqH+rA5QHlxj09y8H3HuQmNy5WcgIhAOkzUHnR/NlhHur494v2WOi+0XaIkkEvT9bcYwVe7z+r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.3.0": {"name": "espree", "version": "3.3.0", "dependencies": {"acorn": "^4.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "9b32fc5127eeea573c339b99873046638ed91761", "tarball": "https://registry.npmjs.org/espree/-/espree-3.3.0.tgz", "integrity": "sha512-EA5+ZCTOw5+0Ovpj56rFwJUen0xPgZz39fQTbCUF7HZryeg7emJnq1YW5267WvfoVMqv1rYFVCEGrxCvt7hSfQ==", "signatures": [{"sig": "MEYCIQCxKBi/7NKB+gviRikLAVYSXQa7gb24QAu+4pA9I7AXcwIhAI7wZO75gzct1OO4EymmQR0EF98pIqGEAsf+2L7V+wr8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.3.1": {"name": "espree", "version": "3.3.1", "dependencies": {"acorn": "^4.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "42107376856738a65ff3b5877f3a58bd52497643", "tarball": "https://registry.npmjs.org/espree/-/espree-3.3.1.tgz", "integrity": "sha512-SMKJdN8/Eq2lfeFzQysVRiTvjrxvgcdOWhd7mWWsYC+k4yN3WNcuJbuAHLAOZAKmenpx5wBbdNOUR1KktT5hZA==", "signatures": [{"sig": "MEQCIDBqoqNQSYvL23IvILgXsI0GxSgu6lTvslY/XEA717eEAiBNRtNFKAXSLbCWI0oDNTmmcn6IfnVJqo6Sz5ZK0VClvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.3.2": {"name": "espree", "version": "3.3.2", "dependencies": {"acorn": "^4.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "dbf3fadeb4ecb4d4778303e50103b3d36c88b89c", "tarball": "https://registry.npmjs.org/espree/-/espree-3.3.2.tgz", "integrity": "sha512-XXORqWJvMVPdihuI0MxuLWZYyRJVTzZfiRqvTEio5t0uIdp/8SiqabaIclPaUIgfFf2Ij0iTlsEe9Fk/mp9suQ==", "signatures": [{"sig": "MEUCIQDGfxUG+i/4f2Wv7W2mZnHe76AAenEZZYpw25FF1OMYuAIgS6317lGHZDhhCrQIXlA7B7LsQ2qYfbterifUiHBmBFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.4.0": {"name": "espree", "version": "3.4.0", "dependencies": {"acorn": "4.0.4", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "41656fa5628e042878025ef467e78f125cb86e1d", "tarball": "https://registry.npmjs.org/espree/-/espree-3.4.0.tgz", "integrity": "sha512-iu/XZszpgK9KWSpPDxpojYGHsjkDyhzPmVE5hNP+woJ4sHU0McbJQkHvw15i2qGoRXfErFWMWp7gxHjtYCN8Jg==", "signatures": [{"sig": "MEUCIQCzt7A90jbueOHquCpqbBBZedSVn7teZnIy+l5WK+KiYQIgO6JDwgnrC1ECl8bFmkA4YtwznJ71WMKWrk6DTHO1GxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.4.1": {"name": "espree", "version": "3.4.1", "dependencies": {"acorn": "^5.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.0.0-beta.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "28a83ab4aaed71ed8fe0f5efe61b76a05c13c4d2", "tarball": "https://registry.npmjs.org/espree/-/espree-3.4.1.tgz", "integrity": "sha512-yLqw4MrxeDutRwVlkAZ0ygDLaKlcjG9D9ZFTpmRJu/QGg9rd7erKO9F70Bpavs5wIlLmzh2EjX+3CSFFXFbgMQ==", "signatures": [{"sig": "MEYCIQDvFBfkRSJsR4GtY0zRwQvGF0uE5b7wN0r9V7Z9esRvoAIhAM/7bADqqTD9bYyhHLTRiKoY+4EEoYh/Sy4f7puhPyEN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.4.2": {"name": "espree", "version": "3.4.2", "dependencies": {"acorn": "^5.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "38dbdedbedc95b8961a1fbf04734a8f6a9c8c592", "tarball": "https://registry.npmjs.org/espree/-/espree-3.4.2.tgz", "integrity": "sha512-zG81bvF0sVF0iZx5kk99Is3gGcXsj/5jUtNx/qReJIbn65foeaHc+7Ja8axncenHTx6F9FVC2iNsfpdh/4Ar0g==", "signatures": [{"sig": "MEUCIQDbqJ+/imLcAdDBYsrJzZw9KDAvuu0AqbmJjVdzKm/MKAIgNgLx8gQ4Rut6uPtWvkyEcCIcNj+OP+n6gZ/ZO1Okjps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.4.3": {"name": "espree", "version": "3.4.3", "dependencies": {"acorn": "^5.0.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "2910b5ccd49ce893c2ffffaab4fd8b3a31b82374", "tarball": "https://registry.npmjs.org/espree/-/espree-3.4.3.tgz", "integrity": "sha512-Xqn0i9fqQLP/vV+/kw/kg94qSqoQME0xuoroSuTieHOC3SoYVumn/zq+aoqc0EkK0IqiFhsfN+R+ACt6RExJgg==", "signatures": [{"sig": "MEQCIHcnd8B3f9sKOeigtxOjHca9wpzLPAmIzfEB0btuygMHAiB39wFyMvcdzAkUH3taCkez80M2MKhxwMq6qOx7ldh4Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.5.0": {"name": "espree", "version": "3.5.0", "dependencies": {"acorn": "^5.1.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "98358625bdd055861ea27e2867ea729faf463d8d", "tarball": "https://registry.npmjs.org/espree/-/espree-3.5.0.tgz", "integrity": "sha512-+moIVPwPiYiVp2ysmEwZgwt2UcetIRt4GbhAjodGaAM5tdnMv8A7qXG6Bn2G27N9qq32U+zZ97SwLiGLI+dqBQ==", "signatures": [{"sig": "MEQCIFpSkJophnetyX+c3RXHq2Z5WzdbHgYyI3BZlwOjIx2zAiBJ1FqTNmdNpemolGnlW2AvXUADgP0OC3Rh1C9XRiWaBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.5.1": {"name": "espree", "version": "3.5.1", "dependencies": {"acorn": "^5.1.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "0c988b8ab46db53100a1954ae4ba995ddd27d87e", "tarball": "https://registry.npmjs.org/espree/-/espree-3.5.1.tgz", "integrity": "sha512-DD7Gyg3JXZ3EtvEFBWuCM9alDKqwwjNEQnMvTKIgCsgGstTOBQ28GiQBQcsLxPc+eqhiSnmpbxhqyekHEQW2TA==", "signatures": [{"sig": "MEYCIQCyq+uliUBnbOAOkUUgxSEa35aacOu7GvVbq7IkADdDegIhAK8zd7u61mRH2bCUSToApEQK4GefzDr5UD9KcLrEXliD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.5.2": {"name": "espree", "version": "3.5.2", "dependencies": {"acorn": "^5.2.1", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "756ada8b979e9dcfcdb30aad8d1a9304a905e1ca", "tarball": "https://registry.npmjs.org/espree/-/espree-3.5.2.tgz", "integrity": "sha512-sadKeYwaR/aJ3stC2CdvgXu1T16TdYN+qwCpcWbMnGJ8s0zNWemzrvb2GbD4OhmJ/fwpJjudThAlLobGbWZbCQ==", "signatures": [{"sig": "MEUCIQClI1QsAkK/6Ll++5ydxy6YRHbxdXwTuLmn3dd+ck4bxgIgYzblpwL3E9xBZwEVYBiwItGr2j3YNmnpL5pZ60P1Als=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.5.3": {"name": "espree", "version": "3.5.3", "dependencies": {"acorn": "^5.4.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "931e0af64e7fbbed26b050a29daad1fc64799fa6", "tarball": "https://registry.npmjs.org/espree/-/espree-3.5.3.tgz", "integrity": "sha512-Zy3tAJDORxQZLl2baguiRU1syPERAIg0L+JB2MWorORgTu/CplzvxS9WWA7Xh4+Q+eOQihNs/1o1Xep8cvCxWQ==", "signatures": [{"sig": "MEQCIBwx3py/sVDqntt7JgtUtLCXxfSj56FZ+C00/O7AK/WUAiBcWMayt+Nx+4oBV8rjw6HyaVJMQCACO0Di2YY0Ro+a0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.5.4": {"name": "espree", "version": "3.5.4", "dependencies": {"acorn": "^5.5.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "b0f447187c8a8bed944b815a660bddf5deb5d1a7", "tarball": "https://registry.npmjs.org/espree/-/espree-3.5.4.tgz", "fileCount": 10, "integrity": "sha512-yAcIQxtmMiB/jL32dzEp2enBeidsB7xWPLNiw3IIkpVds1P+h7qF9YwJq1yUNzp2OKXgAprs4F61ih66UsoD1A==", "signatures": [{"sig": "MEUCIHb3+trFuCcQbxPFdZEkeYUjz0nNMPCG7Sr0YDCoofNNAiEA4SAJvURGJFPZ9sOWViYSnKvp/YZ/8PxftI1Bm/bGx6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77794}, "engines": {"node": ">=0.10.0"}}, "4.0.0-alpha.0": {"name": "espree", "version": "4.0.0-alpha.0", "dependencies": {"acorn": "^5.5.1", "acorn-jsx": "^4.1.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.10.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "c12e621c27cdd74655aaed07011664dba9b8c22d", "tarball": "https://registry.npmjs.org/espree/-/espree-4.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-vA3n7BKk6HRKTtsj/3/dyBaCdFtDCUhD/6ISQeOPjkxLp40lgfx8Y7Vm0V25H23m7gIiXX8LRLVNo2Pm451oSw==", "signatures": [{"sig": "MEUCIBCHjlNMdNAW62o40vyPqa+4kC588eTXapFnuSf2Y3hWAiEAzpfWMHSeB3eKPqWSW1yG7I23+xhEe9g3bgWDDcaIbLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73563}, "engines": {"node": ">=6.0.0"}}, "4.0.0-alpha.1": {"name": "espree", "version": "4.0.0-alpha.1", "dependencies": {"acorn": "^5.5.1", "acorn-jsx": "^4.1.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.11.1", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "88c738bcdd5ca8bbcd638bab27999da9a292d69c", "tarball": "https://registry.npmjs.org/espree/-/espree-4.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-JT1p42RDNYRO25tTCjb8XmV+coviPhsJcdggPrV5JW3Z0FoX5RTRNJdY5s7cW7+FNJ4LjSRFz2JvpfrA3yUeHA==", "signatures": [{"sig": "MEUCIBrT8zA+drd5rFcAqnFgwO/J02VLxbtNMIBIeBaj3l5nAiEAs5t4SdmVX0aWnLxZ909ouhM49GF/iSIMy1iBrp76cVE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDCNJCRA9TVsSAnZWagAA0LMP/1kcZq9556wYW1mMvSjR\npr/sTV6QlZng5eIgJCE3vq50AhR2Uo+R09jsv68wugsApmJtihFZB+QBpCyQ\n58lOCg3eK2fE1LbExgNOEEwf0m072vJzi0u3yDcZ3f6CIyc1ak2LV+aqM+HO\nGJuq6JLdzfl8tRkzsDU39fm9ZFnTt4UqX/5Yw9gXS+Z69mdstW7b/Of23YPG\nvi5e/UoXFtZYFbr5bPNNeIf8Fd90qQ8+OW/CU7puy4L3sMKGngUBWT9VVuUr\nro4YE67MiFAutca4c7qGAPQC4urhQmbHI1w6ccWL47nCTgUZFGoT5WAoBs/j\npb0owweCGc3UpiLSifS8yPXO808+aet6jiqWknW3tnmwX9CZ9WCjVKIceU4l\nCoVnvmVMOS3II+IP+os6HMbMCgb2ir0EtN/EVNftYeWEhGvAy2ddyVMaCbDh\nF87i3b+qVmwKUBc5VhX2AQdGnhsfzZsPBIJKdyYdiH+gNkVVBVSV7xmJPvvd\n+lMxM6BU1GcL6M4Goq6tls0HCBgbsurq4x3Rk+GnvcA2LGqrpXEzJICoo9kp\nuR/uXRpdLqJbrslPM5hDRB97dgw0Z8QqF6zgQcij8Fbc8mVo8NvFyhT5bMmt\n4c+fsrvFnEBneOXSbBPD2Cxoor/4cO0watGShxq82H1Rmx9CnP3YZaFHxOBI\njq/i\r\n=1Ga+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.0.0-rc.0": {"name": "espree", "version": "4.0.0-rc.0", "dependencies": {"acorn": "^5.6.0", "acorn-jsx": "^4.1.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.11.1", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "d2e03d00420efc91925c4dc7ad9323b03b14a1eb", "tarball": "https://registry.npmjs.org/espree/-/espree-4.0.0-rc.0.tgz", "fileCount": 10, "integrity": "sha512-7CY0pW9/YDNiOx15GPD76jIjFJd+VqOFFNczKG4aL/UblEX5xnmy0b2g29UJ0p/wmH0M/cLOaP1hxazEhpzoFA==", "signatures": [{"sig": "MEYCIQCjBKyr+tU+opZ1ywIWpGHrCav69zGrFP9UusXTrnrfGwIhAPvnBzm30rxRq+d0qw5tbC+0K+iEmP+Ra6HWLua/YzSB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbG/hgCRA9TVsSAnZWagAAIl0QAJz0NWOSR34QaznBaEGf\nYOrGBm3gpfxNBQ+bjGCjR4cHJ3DOIe+P6TH6kAAhSkHyVbFKcSimXomeetqO\no9G2RALa3qhYOenZEJ7YYIJs+SLdng4HVDgIocFSs5VZ+QoykRNUE3emASyd\nfddjfcVgqkaEwFigaUczlCPnU6cd3dQO7yyuNZCbwnXsPi1RGKRvZVlb4mW1\nhSVRNE+OpW+isr1IFskOgII6ZBVizFinyIbAoHjXDTWDps3pT6UbV8HwO8i2\nkkoBdyx7Y2j2d6MBa80sK2RAcow1CEyg0dsCRQ/NLIiT1d+EWFvje9MtGwBv\nHLDMAI4eduOqQwUr/7iEFOHrfK4eoyljnM8Dhz4bllJMoYJUm6qZ6WVUlApe\nOaFKe8pwpWQvX70ubpdYlGD+1hctau/h3NHeTA4TvX/hqrygq332txv7P/PH\nQEykiqw7QGtii1uCqIlCHA322kxZ2ZNlq/MKqPAP1A/07HhkdXrPd5lpfwTa\nVabR8U1qQV7bVUU3M/+Xnpgje6PucbaT0aHTFJ4Ek9Rp63XZTHMlTBiL0+Ij\n7X2Iwny2wzraHXh93U/9lJ6+LtdBhHeycnBaeZvKkvNkKBQ0tCs1IVpqeOO0\nFGpwNoWLdT+iIt43UiGHECJ8N58TZKyOBvDAQvYBrX7/Tamn3sLbTzHTcvd0\nWo3P\r\n=QKSy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.0.0": {"name": "espree", "version": "4.0.0", "dependencies": {"acorn": "^5.6.0", "acorn-jsx": "^4.1.1"}, "devDependencies": {"chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^0.11.1", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "253998f20a0f82db5d866385799d912a83a36634", "tarball": "https://registry.npmjs.org/espree/-/espree-4.0.0.tgz", "fileCount": 10, "integrity": "sha512-kapdTCt1bjmspxStVKX6huolXVV5ZfyZguY1lcfhVVZstce3bqxH9mcLzNn3/mlgW6wQ732+0fuG9v7h0ZQoKg==", "signatures": [{"sig": "MEUCIQC0snC//EP8ax/0rKWjZdLlNXd8DVIbn3grKA3pp30bHAIgUKJ3fL1cJmxB8Y9vIMHQ2r54JtKM87Gp62vNtEs1WG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLA56CRA9TVsSAnZWagAA6tUQAInyP872BnOxENAOTZNK\njVofP1FLXynJfr5uaSmMsY4TXgr5fCXalySGqFOyoAWKLYmApx4UxUMRBdCz\n/qe6N9ebCUV3StHp2jrEQZMzf+IJvwGJ4NSlC0kAycE29mrILM9XZnYHMlDO\nfCKlxwE86obauQr3WY+jRCG07KGx03uH0TJ2aBSK/ZeT6Pd6Pdt5tHTNDRGb\nxy46oJq/NM76OFJSJr4BXRkOPNkq9VBHLtJ0/+Zi433BmTq+rSS3Bg0zZIFm\n4wQU0sKnLQgVsY4JjKtK8Ww5r3YBG1nCnPYYCn9m9VmVGvHT3RlAGRcXDfYd\nSpaOP91wzwpsyuRXUqKT0d8zf1XqftuuOh7FqO83x0ibBTVwxPnzBPY9RAAX\n0Hl6SjX92CwAVZBMyDCvDzDIwjKO645gOdu9VmeeMdFDRtw8h8s1TrVv8L8m\n3s3rq/ynrnmP4NWtjc+NDp0ttFuP8Gx5ANv5qUFcspdvIIJJaumdT9osA2m5\nlOyWD8vdiG8zHipRur5rH7B/hESun64FSSsq1+ATLpuCZqhLb04uCPldZ+IO\nYrMOCYKp2Uva7Q8VBHr34p0dBH/lBewVslAr62lszGO/2ZytBEpQQTLdj0IO\n0bjNasZdT0cpCl6hGzN+/3HMdXPjoOT3dVM0Jv2Q6tm8AgmwUw/RxU82bDNv\nRS5N\r\n=CXFh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "4.1.0": {"name": "espree", "version": "4.1.0", "dependencies": {"acorn": "^6.0.2", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^2.13.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-config-eslint": "^3.0.0"}, "dist": {"shasum": "728d5451e0fd156c04384a7ad89ed51ff54eb25f", "tarball": "https://registry.npmjs.org/espree/-/espree-4.1.0.tgz", "fileCount": 10, "integrity": "sha512-I5<PERSON>ycZW6FCVIub93TeVY1s7vjhP9CY6cXCznIRfiig7nRviKZYdRnj/sHEWC6A7WE9RDWOFq9+7OsWSYz8qv2w==", "signatures": [{"sig": "MEQCIHdRpTap7X4cajJapyIf0d4YN1QbXLnL/DuErMnaVUeMAiBbelYUeG+VuTJ/IHv8pGrGnoEaPrMQ+/Kc4LMuQvF98A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0K4lCRA9TVsSAnZWagAAI9gP/iv9s5/fAgJxrr3bmuwK\nQpYU/mF2R+MYlXU6u3LBMlieBeLvwxLw8MlptVAv425YEwYkXHRKXCp7hcDf\nq0bdRoJl43rcDKMLH1py3y5l3vn59CtMiMzMCErHFYsFMwu5obc8iA9dyzxO\nqLlu4UmkcYXiokZsD3tTgiFNFnEFwo7wM6p8Nt/4g552Xe9gfuE2ff5ZdD3X\n2pHZDZ8vKRRYSngG66qTXzhcnvamXkLV1cGsUN5yqfQSBr/RbGcIve1/n+Nz\nN5MdzsI+U3Mltho8/0S+iPOTwCPSZAXJHKdK/73IKy46wJKSKPjdMNU6C64C\njCSLUEk2qFcAjbG+aEnzJqT0n3gFSHGbKOdwDBUbH77POTPRUHxKnGQlNdKs\nrkgRM8impU9rXMSIANg8M4JT2f8mhnM0iks8CdZ9o0ZJd8xQ0JwQ4wNdb89E\nBgh8wL977XT+vnVYwcm3sBJZs/kFsCNhPVXIago9FFlHaM9HTSlBflF8y7u1\npeVY0yXAvQNr3ffliU3ZQph8qKuwE5Al+onS7RM98WEMPccx4tP7KoAkhmeV\nfB0NqYEMWqseXkhqUt8aHPB9u61BWkWlvAJyLuRCv2Gg5RrpoyRW4D/cbu7e\nC2OyUMVhTg6xFFN1eerpt47DxA1b6jWw/a8QBTeHtaGk+09IwN+0K1J3fELv\nybp3\r\n=Wkma\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.0": {"name": "espree", "version": "5.0.0", "dependencies": {"acorn": "^6.0.2", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^5.7.0", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^8.0.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "fc7f984b62b36a0f543b13fb9cd7b9f4a7f5b65c", "tarball": "https://registry.npmjs.org/espree/-/espree-5.0.0.tgz", "fileCount": 10, "integrity": "sha512-1MpUfwsdS9MMoN7ZXqAr9e9UKdVHDcvrJpyx7mm1WuQlx/ygErEQBzgi5Nh5qBHIoYweprhtMkTCb9GhcAIcsA==", "signatures": [{"sig": "MEYCIQDP3hxQmIUkzJned5scT2xZBRSKbnfMFu7YW828Rj01iAIhAN93M7tONwF2Landhx2d/NV0r/epJ9LE1QWceEOmfsxo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcCAShCRA9TVsSAnZWagAAzsEP/0Ckt545DfMJacZe7rzP\nTvlj3jvVO2I/BQ/HgEHI1Hn8FzDEdNYNIMRoEu7K9TPlwatteP3GAfK99lZW\n3MxM8hRGVXLyb+fmQdc5fCMyFg1pte0H88EPZO4zQMmv0bCLE9fNfk0x4QUC\n0T9g4zh4rKPPhH9xJHzbSb5bUY/zF8mEcYjiHK+6resfHyLkhvt1q5MlwvFB\nHj+3+moSH+HRYHeg01LYjkIy0xuip0CYyKbLZKU0oGKCQ4ahkCt0VCFSOIn7\nPgU7bLbwwMuAZX5NIE8ex4oUMdyy8cFqrW+JVv8lbYyYV5VLpQfMCUY5mSof\nDzIpo8tmbQsO1hi/9BSF8UKogHJoZEP4UbCHJUR2L+S436k4vRAee72ica/M\nvVFC1xA+S+ipt4IB6Ve0hWO/b/Dit4tI+Jd5rT0lqimYh7XguII/oInhaMw1\nsqpbJmN+DpXKsar1E330GendV9upBDFALdJ60ndZRozsESEjGIPGanEwZGa0\nhv1LMi3brnsSQ9mvXn9iibF20ydUCHfsak/t2HJ++dqYaif4ymgGzzgAWTPj\nzzygT5ARK46BlZ/yABbWFZyv0se5IEXmGwJjfYiFiY74Us2e9g/65N95ZetI\nHtNUdJtjvUM/HP7+8Zd+AmZNMiEQjCr/Om3mIwHgJLI0ixl3Dl4uJC8SK99n\nX7O1\r\n=ECYL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "5.0.1": {"name": "espree", "version": "5.0.1", "dependencies": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^5.7.0", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^8.0.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "5d6526fa4fc7f0788a5cf75b15f30323e2f81f7a", "tarball": "https://registry.npmjs.org/espree/-/espree-5.0.1.tgz", "fileCount": 10, "integrity": "sha512-qWAZcWh4XE/RwzLJejfcofscgMc9CamR6Tn1+XRXNzrvUSSbiAjGOI/fggztjIi7y9VLPqnICMIPiGyr8JaZ0A==", "signatures": [{"sig": "MEYCIQDudDtj0OEu78G6920SnLRwCMeYIGiqEHvMF3GCP8n6rwIhAKSFj/A/bpQOlvRlRUxa9ITQbNEaGty8O0FPoLZl5Sum", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZxV6CRA9TVsSAnZWagAAUHoP/RSQ+6dq6YMm6Fso37T9\nt8UUE8COM+We34UR97F6ILMKCmbeN7TEAzDIPr7VZo4OlJiJAahqXNbKYpb0\n7v+VYIuJZXW13XHC+bhwwX26nU8KqB8mA8VTMp58v+Hp+mpSHzIrxt1xeKW6\nK8GhG/C9OZQ2a+A/VobX/s9420f2gLpaSA9MWWUA3IykAFjRzbQGBDo43muy\nn1fgpkedH7CuA2QMAwpna8pdDkgO6w/UzzgA3t19YOUPN3EviGzTDMfLubMn\nWn86TbXPPjXfH+ll35NqxiL71mG4sw+9jX0YMzfMPgXl+chnBO5ExrjCsTvS\nD4qYe/KE2AQS+rxgNnaXO4gOuC+QIsdQdDpqxb4zda6ca4fZF7pc1eRY2m2k\nZt0//w/oytimK3W7PDwYQELQWukncZa4YWZfK+9yo/lzWnot1KjzSqA2msWR\nW72dWi/4vEdcOpuHU7PdNYbIuO2CoQLt4ShWXLyA7dWyRFjQcfm9fk6lUt2l\nL8U7EMtzpPzQooG0wE/VuTklH3B4xQ1E8tV4+PC6jJwzWEdKWX4GYmbL4bEj\nI5bKGSPcH77LNAoMoFKKsoLpjfj+t9Ki5CsOEwJAtHBm758S7jIzt/NPn8ev\nSK39H4y4nBRCa2w+auUx1YEv9j9Q/ldtHQWvRGmfSHSBiRzmFxHbYzGfiniq\nknTP\r\n=is4+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.0.0-alpha.0": {"name": "espree", "version": "6.0.0-alpha.0", "dependencies": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^5.7.0", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^8.0.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "4db6aa458d63896575d48cb3498aedfc7d45f2a8", "tarball": "https://registry.npmjs.org/espree/-/espree-6.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-Y5iDF27FSV+Lbi9WGWm3hz3Zsfh7a24xRAkNwDGoLWJe82NoxtL30hqCY2EEZ5FGvsc0IAj9V3QlwNjjj7RlTg==", "signatures": [{"sig": "MEYCIQCJU5YAb/mEc9In2Nzwoyx73dvuSUi9IwwbcI8ljQyeIAIhAIrFGMFW/VOGAWyK46O1M6DM5R9ko4XBBVIwkDlGTCnF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsUI8CRA9TVsSAnZWagAAfCwP/jXGB269ErUGNtpzLrc4\nwAh2MYX6mUOuteBacerD0qlf/lcmHdNEajQbjv7JKKsh3Kf4/K48CUHPvxDY\n+X6zCyCkyQ1/eM19Rqz6RTFNX+ackeDC0KK9OChmKpIQkM0ZJzVoVqvXD7lX\nav1cn4ghRqb+vfA2ZgK+y+pmtuKvaGAMvD5glRvFlByXYh6ihu9yQ3B5jpjc\n3D4cVlW9TZ2oyPJiMkx+csHPFf2FAjrBHVNt/8F14NBriUBNWjYJnxBPXRrj\nchJj373sBuVw3FVHqY/1yXII4MRxSyDYwzBPlzyzprZvRKx8qKI+PthR/u+x\noNwiinorepkT3lR5F+45v7d20friGq5IAR1RsRo8sZYQsrvqzNotJGGW3Cfl\nILIPBSBFzTBOP4jmenyVrfMD49lkl8Lfc69aBhDzffQewVSby20qQzlySf8+\nrvppxfVmt+5MGM+Zq0/8G1EXZVWEJBvK7aJr1e7+c7n4iVlb8BNXUlU/iHbh\nwpcmTYRY0fhXjLAhz9aFk9LtarJ3Wg+7QUoAixNsqicAGSike04Jz8S38AXY\nErvY7oJsKcROw/aK6MjH6cuBti9grm4xwxInxMB8T0g76Cxhc1QpN8qBGZ+m\nwIGzrm85vae6L55AgAq5C+JlYoqJLqjwm2+eVM7h5ymQ+Tl25qwSkwLNwXQw\nsfMP\r\n=j57C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.0.0": {"name": "espree", "version": "6.0.0", "dependencies": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^5.7.0", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^8.0.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "716fc1f5a245ef5b9a7fdb1d7b0d3f02322e75f6", "tarball": "https://registry.npmjs.org/espree/-/espree-6.0.0.tgz", "fileCount": 10, "integrity": "sha512-lJvCS6YbCn3ImT3yKkPe0+tJ+mH6ljhGNjHQH9mRtiO6gjhVAOhVXW1yjnwqGwTkK3bGbye+hb00nFNmu0l/1Q==", "signatures": [{"sig": "MEUCIGQb8hdI+QWjZKqImSbdkYE2Jy75fpWHQc82aOhDYtf7AiEA1OANzPmZ8ztUyol0QuF1gu94V8b845Wol5i1Q8C+XrM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64841, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDZUUCRA9TVsSAnZWagAAa3kQAIJpAT6TYC4RBgrQafPr\nPQjF4ZNvFA2K+epvFuyhaikXgnhcMVp6WGnuxgSL+O6sK50q1epEBqo0Nh8M\nn1iXJFfGM3RRgmKXaBSEgeEgsOI6kwSXB3Kcx2dAgZwGvZdcyl+ULVKZ2365\n9d4oMiN6O8dqjqqb3z/JawM9c/e3SOo+HmOOo5kfqK1EawA4npYd/RTAbOrz\n7CI1KFp0UkN1UfOPurDEA/4BfKHAZelGb1ne+qT95/snrc+r35L/6khnfEUb\nDdd7u9Lda+2z/9YLRgapkFpqto2iT0sjqjS0yO4EAO+W58DSlwIQSfRjgshv\nM0tdgbwZeXSGZHq8B5sg2IjeXftnbLodO49ROTKADHmPU67+fjULAZTcj7b/\nAaIaVEDo15p2c1E/SqF/KgLkHTEKCI4g4LwCHrnZqUGGvW4cWMfXPiha/v0Y\nRA79haMca1nN4VS09GcrJ83K2VD1lcjBhWfvYo01XF5lGRNykgWTPsoYbtwp\njzv4XAyC0vDS5wD1ZwLFIT/CV+sNZqLEmI2eX2eMsJ3lnpuA5PMVLWnatj4f\na9Kmb6uD1EPKIKK3CjKw00vevnA43+zTry3Wo74vJ5vFGahG4dVOGHGc9JID\nI0j0iItRcMw+3EMaCVRvJMsAq6/tf/2ec+ajDN3YfQMtJ6uS73zjzwPcLUuz\nzEjw\r\n=ZBBZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.1.0": {"name": "espree", "version": "6.1.0", "dependencies": {"acorn": "^7.0.0", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^13.0.1", "chai": "^1.10.0", "leche": "^1.0.1", "mocha": "^2.0.1", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "~0.3.1", "browserify": "^7.0.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "~0.5.4", "unicode-6.3.0": "~0.1.0", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^8.0.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "a1e8aa65bf29a331d70351ed814a80e7534e0884", "tarball": "https://registry.npmjs.org/espree/-/espree-6.1.0.tgz", "fileCount": 10, "integrity": "sha512-boA7CHRLlVWUSg3iL5Kmlt/xT3Q+sXnKoRYYzj1YeM10A76TEJBbotV5pKbnK42hEUIr121zTv+QLRM5LsCPXQ==", "signatures": [{"sig": "MEUCIQDgA/4ueO8LwtwJaEoak/V2yIP0TJ+Wx3BF9Ap7zw+rSAIgAioP9l7p/WLcsPSCMCa9FSG93y1RL13CSfgY5kH4D+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWaReCRA9TVsSAnZWagAAC9wP/R+VC9eR9RLJBzgOAH0W\nzoeVYZCzfD5vFcyvynROFXo/kZV96CVDkXPV4F/3LfbQdN7Fb6TCSIQuoEzp\nTf8feLiWmKs5X/1n2VgjnHrxjR81Rsp8+l3uoJhye6PrnWx1V56mBycoKkfC\nd70U31dZKtQqFiHakjlI4fG8UF6F8zYrfJoFSDpeMGj4LcN+dlmpdTNFIql4\nPbxWM29QyRcIAtkDLqccVUJDUvjE4V0My1XfSY8DJgGA8Q4vrBA6HYeNCy7l\nv/rA71bl1k0R46v3DaMrkb5gpqVrsdnKN+fEmH7vkCqYEMLaTVO44gKL4dEj\nvXBhjPD6K9mRzHDzyYPKbSJ9by/D+EZVGi6mQkgMT17GnSGXrkk0iHipSqRl\nc88QkkS3HegO47NpdR7gUn9X1l2Ec0Vwb3IVL1y9VWm8+Or+l/ENeHRrAXgr\nLUwfopx4H/x2fjdSZlGEXAL4LHP0PwVJieS2fqxvu45pPhNK/gAA1DgPjAt9\nUw/l9Vqkfv82uOUKrAYAPH8DmJmPa2bCFd4R5EaUexOMvEMdkU5DxMsEZLFP\nAYEzcl/lxdRszdQJ6TfQLZ4iEv+s2yLfUMyYuwjsuBfUAcXktxQfeRs2vwW7\nYS9phOu/k3GaBqriU+vnqeh/orSL6yN+GMjtwcqNGRweLsRn4O3VUxaTI26N\n3b34\r\n=y6wf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.1.1": {"name": "espree", "version": "6.1.1", "dependencies": {"acorn": "^7.0.0", "acorn-jsx": "^5.0.2", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "7f80e5f7257fc47db450022d723e356daeb1e5de", "tarball": "https://registry.npmjs.org/espree/-/espree-6.1.1.tgz", "fileCount": 10, "integrity": "sha512-EYbr8XZUhWbYCqQRW0duU5LxzL5bETN6AjKBGy1302qqzPaCH10QbRg3Wvco79Z8x9WbiE8HYB4e75xl6qUYvQ==", "signatures": [{"sig": "MEUCIDj/A5BOBvMz3jMUPGktvD1Dbbff9+QigJs13JpFbs88AiEAsdrQKfArVp42vFVyV/g1lneF8VP5YaOrXGr+UvGCdpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYJqrCRA9TVsSAnZWagAAnH8P/irlyQ2w/tjfmg0duoq9\np53YD10w15xkjZXtxJ5rV3L6pgLoRBG9DAHbPb39s52rm5kZXQqD3p0HR26y\nL6FZai33g1Y2UX3EGoN/SFoDbQvpwg+CNIgtEBZOn5NBE3Bu3w9MzsRqx/Z1\nf5kVSOTxh8IK9LyqKhWbYq7fESA5xwbbwzyYGk9GGiJngI8oX1LILy5l38NA\nQu44yk7VoOQfi6tIZ/9uWH6gcTVKjlgmco3H4l3J7SnWPh+8Ll2o3P7zD81E\n96E1BNtiW6O2E9DCbrfLztqBA+8ZVzRnxdAqnIOepkg1QFj/zEJu4YgtHkcO\nTLHrfk+BMdcjdittq5CgGNzqTJffW2dgpCWz+TfReNHbHGUFuWPvwV5vJyUT\nv7+jkVuvg6K5mut3j7/spjYHHPTOMH0MwYjxrmIqP76cLxP3V+Q5QWgnR9vv\n9TpNvjEuG9nGvVMW37x+7luMRUFPaW7XDpflq6xuL9ouWp3q4yuix9JG3aMt\nNgl/aneQl80X8BuPEJInxNOQZ7ZniXT05zrXLhn8iwnU59zjxCJUwHihNyoD\nqWi4mh4LV+cDzPLvQa/oYuRwWjynQ/wrBTjWXPjituPBZJzUZRpYbBZpbaDN\nsoZfUDhmhJrP1WXTH3mBTPtsKsiF8aZZcRXU6GaSG5QNYG43RjhT7oQdPLFM\nNLJP\r\n=XqkI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.1.2": {"name": "espree", "version": "6.1.2", "dependencies": {"acorn": "^7.1.0", "acorn-jsx": "^5.1.0", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "6c272650932b4f91c3714e5e7b5f5e2ecf47262d", "tarball": "https://registry.npmjs.org/espree/-/espree-6.1.2.tgz", "fileCount": 10, "integrity": "sha512-2iUPuuPP+yW1PZaMSDM9eyVf8D5P0Hi8h83YtZ5bPc/zHYjII5khoixIUTMO794NOY8F/ThF1Bo8ncZILarUTA==", "signatures": [{"sig": "MEUCIQC0jKe94+gDpS1u7V9wAatCnP7sFTDfv0VrWDo9qyV9FwIgH0MoBgfjtRATo2/dhb9Q+2zbzkyPa6MkzQphbrck5JE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrRV4CRA9TVsSAnZWagAA3MEQAJno7ww9C3rlXd1DGgKL\n7VJYUX5QKg+vcutuW3CCTIDkLBoUZfjX1OXuZMFuvrA5Uag536xocYM5FEgt\nOQWOV4hlhQi6+uMMPDCgeMEVf/okNGF2rptj3dWNWrXYxNf6LlZPg/u9TUD4\ndqDdWLZMOhrbFQFd12DkDVDebcO2qc3n7ZgM3I6e5VcOkDeByUiWLngNXPub\n+zHQkSAT4lRVWpx6wdPlCE9askoM6v5Ri+vGnCumohk9n4/MfT2e8/R9KosS\nsVqeDDWsSIOrwg6MsF4eq2vPa7zAA1HG+QadfdyW7cX9tKGo3MWBKUyd0o+5\nDdauXMY3KzAEan+02Lw/iovHuHm4/lu47LW4lVqS9dX+Jmob9qZZFjWRzjXL\nFFZkKxseCaDYqCWfegoNr5O1Klt3RFfm9AI3cksjl+3hPWRPuoJF6SnGTPqm\nN9UDV7swUy2PVANgB2H5rsxhPD0PEQDM1L3p8dPBThbY/CdkfPZAcLZing8n\ndiQPnjeXORj7zgny7JNXMl5VTees4+TyT64fPKQDTZeeaAb6r6iJE+QO1zbP\nz2HNsI7hozZfuv9ETBJhGyrhGzCApwphN+J4Nk9K/vJguhpL/iOkkbeyVX7A\n0q8wBJ4elejh6CT4m8awjOU8+FpvKvz67wKiNGfuaqZTgiGteNfEJK3K3OqF\n9riz\r\n=z3tP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.2.0": {"name": "espree", "version": "6.2.0", "dependencies": {"acorn": "^7.1.0", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "349fef01a202bbab047748300deb37fa44da79d7", "tarball": "https://registry.npmjs.org/espree/-/espree-6.2.0.tgz", "fileCount": 11, "integrity": "sha512-Xs8airJ7RQolnDIbLtRutmfvSsAe0xqMMAantCN/GMoqf81TFbeI1T7Jpd56qYu1uuh32dOG5W/X9uO+ghPXzA==", "signatures": [{"sig": "MEQCIBZrFJS+WzUsZGFol2xrnurZI3QVFoQySWXcLrpC94rMAiBHBaIhnEyCXwDspi92rjlov6x+mEBaBWWmYE7Xaq7Wjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeXTyrCRA9TVsSAnZWagAAfP0QAJETTJBOJUarnfEzLJ+5\nRp4Y3KvEQhr6SkPHc7Kb6mOtoHPJKC+QncBkIQzRWzOZfxkeS4lNIGQSE5MC\n0U1BG1k+3x78RGmrW8H4uZqSrmi4mY+PeMgyxM/4TCqJfWsjEFbrlar3AYPx\n1daE+UgwrY3GOuEA+wn5jlY445nTNiRdMBBDNGRGHNPWtfPkm7ZGMxZwLcdU\nsiv5eMw4y7mfmqV4bDB6wGQ9s44Gab2rOX/39HWczy1t9MGL3gn1+6+mRBgL\nrs1cuvMAMHX0GySUJXBKa4fYT7WwWMVvbZM84jhZqubgzNiXsi/V+4nzinve\nBapEOOxO+6LyS711iSGsXA2Y1m4bi9KLzSk8CXhPShQ3Pp3eXPZ+uT111uY6\ntyTW77BBQPk0vmNe2WjKcv1KdDlh6RpM97CPCyDEBzzxAB3ZArS64BnemRPa\narF8SZA7bHxQEbPMF1p3NuJxbtDB71Ax4Xnxp5HREW5OQkCfzse3HBeQFYT0\ndAg7H+i55ICEsn9Hf+BvAtGzDz3FkNH+aMc0rUO0i3+y9RWF1sMDG+hRxMuw\nCRBmfhOXS3wlHgArZ528XtjdcPhgxqISeoix3hy1fDUyK4gOZFVqHUD62Tzl\nZa1k5UOsjIMJAen8aTg2wbneNtpSgeExrqlMfgZxCok50nPYZRS3W9G6d1Qv\nXelC\r\n=Lx4u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "6.2.1": {"name": "espree", "version": "6.2.1", "dependencies": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "77fc72e1fd744a2052c20f38a5b575832e82734a", "tarball": "https://registry.npmjs.org/espree/-/espree-6.2.1.tgz", "fileCount": 11, "integrity": "sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==", "signatures": [{"sig": "MEQCIDJMAQZKxf/CxBeeKSZ445LpTUx8aBk/0IR0iweB9UXNAiAMgFwv7tFKfBM5w1J/LqVvnZitmCrpEZORnCoOhpCxxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZ++6CRA9TVsSAnZWagAAnO4P/2Yd2vyQYzAD6G3+ncEi\necYJPj1euDX9U5IBcR0PsImnC7j3IT2BHjWi/fWHJ8WgK+fHyYsGjzgSvt22\nDO+1F8hNupJY8F9cEtapewc4nRjZRyxEHpcKVjqjzXD18y9tc/kxIaajvas/\na/r/3hTcvlEoMScGJL3tx3hARw/9Y5ON9HLbfVgXanPLE2RmDL5JhkHO3isN\nL0WIXfHKUBuVllBpe5nW/aij4bhGEDt8/SDJe4mzXdQIvfhpngDKTvAdRW34\nFzBGJPPD5Fpflw7QvvXVyF5nhRWj83G5fS9zDQ0EnshEt/yxNAnlhZe2B/pB\nXw6pKV6NGMqUKPyBPB6gMG9RbD++7CD1kuglnV2Ey+b5/5bR6t6qqoCC3WQj\n/WDN9B7vTMWT8JDh7Us5o9ZbkouUTEXlYjIJzJqiXIly1GUrQFz1KIF+0sno\n4WFOoLhlCfj+SkofK/RfPN+TcLGF/yiIIYQDY3cFv683lfAbUiSGv9xAEMg8\nJu73afN96JefS7FjtSeYEFqjhGpWKIauawY7pTLTtWwmsJbx8zu/uhzs6wan\nS32cqS4UQ62fa1OL4CINBPbM9oq4HLL7YGC1pf30CjKY9BEIBGS0BnPI+hy1\nfRfscoh+UlYvqNxL/x7z9o6O+oxc8hKJdtwgcokShXRurwGdqYm02kMiu1C8\n8D/1\r\n=yK6G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.0": {"name": "espree", "version": "7.0.0", "dependencies": {"acorn": "^7.1.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "8a7a60f218e69f120a842dc24c5a88aa7748a74e", "tarball": "https://registry.npmjs.org/espree/-/espree-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-/r2XEx5Mw4pgKdyb7GNLQNsu++asx/dltf/CI8RFi9oGHxmQFgvLbc5Op4U6i8Oaj+kdslhJtVlEZeAqH5qOTw==", "signatures": [{"sig": "MEYCIQC4PdLaiKARGX+EdA+ghx/iq1r8GbeVaUcsUQRwMPk+QgIhAKnwAsBr2Q3cnvDM55mTJEa/8SGFAA0RIpWJlp+8Z9f7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJetHZiCRA9TVsSAnZWagAAM8IP/3L0kNs/2x+9zRBNrf97\n0a2MTzzHUllmtQjTje7WL5m5fN7gMsogSQEEqCiUTcp29alNQ5V70uEPxcvX\nG1O+xqZenj/SYSAFIb53nVZcjTTYrCH8S5UXYNGBhMGvSwhluBfh18bQeqw9\noXHwH7DJPX+4+2NE2qKgTPvXYz7xO5yzkpExxZ8wO8cxhUuQniHDp/bQc61H\nqE6chVML7Lx+it9L1AVgZZgAu4vnTELbpNoQvOe6JR52UgGLktalKLMLPTyM\nvOY1FRgkteQ7qz4CHm8sqx+OSeriv1SwG5iF1O1Hr1zGHZOsxTMdUBgT1SmE\nlBTjC84ZelIkyum5To3QbsePDZ9WUMmtY538cKRwPklW7jM0H1n6j7PGK8k6\nN5hidsRVF3BW3qI0pGAHsQRWB4F06dXc2SeTbtqf77t1wmpFEEvvcaHY0Pqs\n955gaWBPUWUuzCCVY/yfNrczVnWLdB6DeTCTIIqsQGjuZVydqrZCteosXy5G\nXjHFzFuKnt0IyIb3abL9F0NhWF2PSPaFsZAj5y7dUusAdtHLiBeJMgMSSXjS\nmkmUMZnbomGXWWlT41J+D4Sa6fvag76vaCIJpjDQhKHDb7jnFtsOyXrHFIVg\nAmoR+fHw3hfGU+waYZW9JYho9j1BLOiJZHlPxlwfE/2o+/EFSZmt9BPQiP0V\njrul\r\n=vQ05\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "7.1.0": {"name": "espree", "version": "7.1.0", "dependencies": {"acorn": "^7.2.0", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.2.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "a9c7f18a752056735bf1ba14cb1b70adc3a5ce1c", "tarball": "https://registry.npmjs.org/espree/-/espree-7.1.0.tgz", "fileCount": 11, "integrity": "sha512-dcorZSyfmm4WTuTnE5Y7MEN1DyoPYy1ZR783QW1FJoenn7RailyWFsq/UL6ZAAA7uXurN9FIpYyUs3OfiIW+Qw==", "signatures": [{"sig": "MEUCIQDNDuKXCFNbaLcWUkrIvzUiKzDNpUT0WC088+qy0K9/RAIgHBb+n11ixGwor/cQSjFs7U3P+1ymEB5GKSYeSLkbuXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2NfyCRA9TVsSAnZWagAAfrkP/jLqKHoVP9wpX4o0e92W\nQMEvAyfpFbNthXD9E821qe0UI+171iDSsrjXS7HlRx2GPZyPfOE6mENMt1R3\nr9sVulBr05jVbGqavFMGzQis29Sb3IHXV1clqq0aBaXvmtJvuH6B1PBbmg6o\nSzWAh1l/VPH/yprmEF8teMtI8BXdxR7u/8k14kYMpkgS5Arb96M1F6SCyMY9\ngUSFXVzfCFE/9W6UjQedbEZoo1UxCfs84nAJS5e0iaUqupK4bVceyn2pxJMN\nY6PWpHw7WzNdir7XzLeMsXISP4bj2N5EaVVMbKFJ0X7o8LoXgBPGJFfCfSxq\ned8/vc1Ky0ZcW+POhNJ+IsKHcH6WXk3qRv54IwZvAsaVHu9MNjCxJ8SLq78q\nG31xsRXC+BorvopNCFJNrWymSZ+9OBLYsJvqJ/SZuZ0r3pii2bh1Q1bozP1J\nusWMW+bu7OGP0Aa+hnM8IbUGuOHgPMVSumw+/hV36uN0FT7DiM05KCftgkuj\nPSY+xBITV6nzL8LLL6UNdyxZkFDWYGslrWLfEMnI4CaemwmeJkagziJ/pk6r\ndzd5lKA5QuYbV898g8hgLY3Q7XKQ5mZTZj4rfp7tU0l0HqznUFUZ+FrMzlJH\nUY4jni/OZPhAkJACAT3TuIy0unGDYltmt/ZM8TAwvZiZ15ANcQkHX/6G/EYb\n55xF\r\n=ttxk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "7.2.0": {"name": "espree", "version": "7.2.0", "dependencies": {"acorn": "^7.3.1", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.3.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "1c263d5b513dbad0ac30c4991b93ac354e948d69", "tarball": "https://registry.npmjs.org/espree/-/espree-7.2.0.tgz", "fileCount": 11, "integrity": "sha512-H+cQ3+3JYRMEIOl87e7QdHX70ocly5iW4+dttuR8iYSPr/hXKFb+7dBsZ7+u1adC4VrnPlTkv0+OwuPnDop19g==", "signatures": [{"sig": "MEUCIGMIZY7hNO+oIIm1iLbv5Lx4zDwLb3hElHn4R4m8JWV7AiEA7wUgd3Q+K7yBkxUh6Wgk+qrulZ09ImUIMV+6L/K+KEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEkgYCRA9TVsSAnZWagAAc4sP/jrO8EW63yDlyuwSRtIA\nNgISy2mWQXATKb9KC926X2Mnn8TZBl06ZEERg4Nlt86EGscv6KlR6rm02hrH\nMiTK+ZSlSSFvJJLO+N6wbFCgtiq8BL5fG5MZmmplrWjlKKlRJ5HS5Wx5K943\nLtLBKyQGs3rWiyjLQM0hA9xRWiGlQsr+rL15HG6ZckgtNScW16nI088rp2I3\nsEm4LPJcVC8QX/Ss3nYsPpeG0CawDXn/4sRA6+nDGM9qCAAP/fdl1aUgTcg5\n97xhkW1zwWL+BnUx+RaIZiTGN5oTck0KhCOGlCCPtOU3dmgJQcYMoOdPvA/G\nyFH8myVIXTqAA1nDtRW4eDMLHEKm9Pv7bO8geYteQgToC0B2TRgo7M9iKYc/\nLt/agcDEHutr730LmTTIY0uiyUcgmJTt87B3sJj7Vhz2BuSDZoZC4oGSphWQ\nhW8csSY0xtlZBjLQ41YMcoHjQkw2l5dDz6tksQOnIYsozGYN2esSAFHZb8q9\nlbXzCG8pI5geNy9Wnpynf0Y1VJA1XC3pg0f0pmWgWi7c9UsBb9DggmAlMSLz\nME7wiTi+zPnPat4HzhiUp7z/ovsYbKXaCFEGC7tLxh0WqYth8FH7YHN8cZW/\niEkJdQhBJ7z9quWGrqQwX5QprQhFhEqPcrJ97QYZDZ/kwE87FWfYu1D8y323\nN4+u\r\n=Tdnn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "7.3.0": {"name": "espree", "version": "7.3.0", "dependencies": {"acorn": "^7.4.0", "acorn-jsx": "^5.2.0", "eslint-visitor-keys": "^1.3.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "dc30437cf67947cf576121ebd780f15eeac72348", "tarball": "https://registry.npmjs.org/espree/-/espree-7.3.0.tgz", "fileCount": 11, "integrity": "sha512-dksIWsvKCixn1yrEXO8UosNSxaDoSYpq9reEjZSbHLpT5hpaCAKTLBwq0RHtLrIr+c0ByiYzWT8KTMRzoRCNlw==", "signatures": [{"sig": "MEUCIQCXWdaSfuB7dh23DlengtyplolZfPrEJbRMK8+ttbCWygIgHHTEKu5Jx3MSM0IGG0x2tqnAA7B2ilBkk2nyOFcBhJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQXwBCRA9TVsSAnZWagAAf4oP/ApCCo5K3V5ZIjzSHHzB\nAsRQr4hVPwci9+qFvX7RAm8MAaSLRe3rVw5qbMqBLUifOGYxPReNMmWAKf0T\n2EQ0D1rg/qfgTuBPdmdUBurH8eQHgcD5Ic3KBTBPIWED/f6Xl5JaHxe0WSRD\nAF6cBwYNeesePlpMAe9i/hk/J4ZtJs+nAYdUTWvaPlXslNHe42FQKFL1lByr\nYCqNbwjduUEIfUiVuX78tCw1MK7zKGsScvJAUFT15R62PbnOphxMrPuMOP/2\nQ2A1d8+KdeqIfIdLN9pt+i6ti5kOnv520VPmgviAvaFa2TIy2vUyGx3B3CBJ\ngeJlVY2WHBmtRKILVnkpcvIQ88RvWFT19kWU5bVV3Okvs06vgFbRIfY1UZpn\n14gvps8igo7Y5iaiHIDdgFmqARBjnXsiwS5R0PrWI3iSYEMXD97z18zvUhO/\nwBq0uU1Y19dT6Rfp4pwfEwmr1vPzujWQdFBXpOvGzlLL2yWft6z6j9E04Dec\n0U7GgfJIS+F3wVg+6QMlk7lyo3K/PvZdzigmI2yImr5YlC1Qgl7Ca+V3eNm8\nOCrc3mLbkJd1DQEPxq8vVMDtVLn/TYfetotNrb19lhtBcL09QAf0bCReNSzN\nJ7RXOrsoObJ85POezSxjzLaBc/uedxKw7moacS40ItSH1rFv+kacbPVyVF5L\nPs5P\r\n=hMY7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "7.3.1": {"name": "espree", "version": "7.3.1", "dependencies": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.2.0", "leche": "^2.3.0", "mocha": "^6.2.0", "eslint": "^6.0.1", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "browserify": "^16.5.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "unicode-6.3.0": "^0.7.5", "eslint-release": "^1.0.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^9.1.0", "eslint-config-eslint": "^5.0.1"}, "dist": {"shasum": "f2df330b752c6f55019f8bd89b7660039c1bbbb6", "tarball": "https://registry.npmjs.org/espree/-/espree-7.3.1.tgz", "fileCount": 11, "integrity": "sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==", "signatures": [{"sig": "MEUCIQDsH3KFYBgxzikXJ27anrMQfRZF5wRQambmNEF2UYGjKQIgHulJmbjcEyuokV3buMmNJAKrmmXQMdAX07hLyCnjd3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyxleCRA9TVsSAnZWagAAKRMP/RX4MdwNaAVgBFPMoafp\nNmw07oXHfQOjXFPfkK9MQFiz/gjPEgEXXxCoCSV/RN/n9nRtXGuNWg0It/iO\n+A5QZ+NoP6wnNtXl2pH+Ui8xD5Y4UWJBHiDuhEMVREyVj737y2LM5Syj/92x\nQZ8EJLFnC31o0D7dCli/gfu5v01gTeUKFurkn4NdNV4gE3u1QZaoqK3M0qLK\nz6hc2EPlHKw+iLKJ285ZGePuQZ8HvlTlvJPHQcdHaeYe/0PXE7SyOCj45JN9\npsrAjoxlqS0XpQIfqdn1bV6Q6Xo1WHHKXib+VHqQ1GA2U8vu7yyYB73U9oEq\n2Oh5/ytNx4Z7FKjdxqab+pGBjGcT9ozhwFKwUD1N2QC+T0AuR4kCvMYw7S0Q\n3R+0zkgEiqHAHGms50/XWfniTY2lH78ndr61Nf8Fr8HbBNHFckFH2PW1HJ+L\nHIhDlD2WqAzuW3fxiLoUil3oWqpZFMKyE9wVIbV5rYC1WMsOW3XwDjO+g3Dg\ndCUpPnwitKCbBUtlOPHH4Iq8CR/wmD3528+Bukz5kYJtRM/znXVrzw0Dcj/M\nQY/uoO1bMVYXEkcNnlMYWj52oe9hN1wk1ATyvQrmtWceaSEfVuaRlIY5SyUp\nPoLcVjpBw6osq0dX28LetNjvT3mczEI0QVoYO0pKapFCb3wmHEeG24VchQKx\nnNKv\r\n=Cv+o\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "8.0.0-beta.0": {"name": "espree", "version": "8.0.0-beta.0", "dependencies": {"acorn": "^8.2.2", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^2.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.1.2", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "40f187789294616b5551816134d69b056824b0e7", "tarball": "https://registry.npmjs.org/espree/-/espree-8.0.0-beta.0.tgz", "fileCount": 13, "integrity": "sha512-9/B3N0Kt3N+HiGTTIsg2FYqZsanYtea/qbFoftYtD2PI533Fxstld6jTH+U7JGB/0FCPqxPy3oPEoYQzCiA1KQ==", "signatures": [{"sig": "MEUCIDO2/ZzWFwjJ7Y1Jwra9fYCDz70GxoWsV+5YPv2MUKrnAiEA5AlYhbMyrgYETHQ+mjnlaIcoT81YvGKtn10DE9H6BPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgw6rhCRA9TVsSAnZWagAAhloQAI/Pmf1pvoVuxq+dyN/D\nEcnxGwO/4s7McJtw6ncb3WiYIDXQ5oc5Ja9cOaNeAObo2zUVB0lmpdNyeDAn\n3G8tNfqRKL6xThpA5Z+G7qsBBVUZmlPFIoKVZLFbIKsJnXUCMFSuRf1AQ+c4\nJJqEWU7OpKcmv0GHKBYfuvb27UCv17l+DgNeLfpD2pIllNaPoGacuo7LFx5z\nBIuS6W5DjUt2kVuJZER3dcFj2QrSIi4GV/6WXgO9z4x0RvX5NCUX/DLshpaS\ntUtiGXqZ6Wx4oKj+o4IHoU2ItSH9rKKwJjGTCjTNuaanlsFQhqp+V79UtWqe\np7IZxxwEj8uq4n1RMFfPeeOI9NWl7GstWc1RyOEdlSbFjBUkzZjoo/cfggFg\n1+1qFKcOZMjUAMcCj+OETpyfOYN5tp96xBhVaY3INOFQ2Og5GEA8BZwW6kig\nw2V+WL5uK5J/jaXD4rj289s25fGOVXb7l7kT02ep4EYr3HH0GPaD/lmXBeuM\n6osvUwxcdtNvOAnydZw1I/6D2NqVHm/mvipHS6w6OlUYsCctgUkKgckTbiiB\ndribBo2LC1OByfRuYC5ZNr/vkJz+UfpKC1KTfkRSy1DwEdWd8lbHhsOn07PV\nYuCUkq8s3GH0Er500IvwFvy5yHoS2zu9QQXHPs1o8WxZ+CUlU4eRi40Ukv4/\nqrS8\r\n=1WM8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "8.0.0-beta.1": {"name": "espree", "version": "8.0.0-beta.1", "dependencies": {"acorn": "^8.4.1", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^2.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.1.2", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "697476d19f2380c921bc884b10e560694f42d26a", "tarball": "https://registry.npmjs.org/espree/-/espree-8.0.0-beta.1.tgz", "fileCount": 13, "integrity": "sha512-hPbLpRhIWg92MkSsH8Y5jgfbyfERr+V5nOy9NWnuWBfBJNSVN1hlFFwcfiJyUbHlkW26BnRxb8m6SxD7zIXdNw==", "signatures": [{"sig": "MEUCICBXosXHjg9nJkjX0AJk+GyZMp6wGve0RFOtBtn36ztRAiEA7cc7xMP3J2YLmV3f9bRQPCcIztk6vJdt6l8ksvG3cYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1NGdCRA9TVsSAnZWagAAmRgP/346UsEad7JSUbaQNN8B\nJv95NaTndet3CI05OA60RcVShaN4K0HzvmCR0F/6wW8zYrBC4b9N6kykqV3j\nfG4AbMeZr65Q464kcWIK0aWP3YJcrfvzEO6NdrZTMikzIzil01k6P4ZIfMmT\n8vjxZyaZbVigBcErXP0ydCvrU+Y/96D/IjaIpkaokO+FhOsoP3bJBjK/C6uq\njjOWks4ePN4v40OJdUqG/YInde00Bmo6gjBa3pa/nAYItw1hKuFol3jZx5+6\nTU3wRZPl5qTcPuK80cFJWHA7frQUJnpAAJSbjraE4qFM+Tr1KGAO2wYOeWW7\n75bC2b9fFZ/jvC8Hg+EYSxrnVNlM4xpH3BNcLDE1uR7JhCQKyQPT4Po2RXMh\nGRS+Iyu/f4AdibUqT1+mXR9z+mPG8V7NS4b8ZBsojWn+m3xJXp1oYN+f5w0k\nbGWid5MMVhHo+UQYj7m9V+CxwlsyEsuWuilc/SgKabu1VwHP0Ms6A3Y9k1Sv\nB3s0Jismvsu3Tu376razoH6b644epiYXdA03iAweTSP++RVlq8S/u6qb1R44\nZAMIy29bmlrES4rr8SUBIGsoHwaBDcB4kgCKo4uea743rLqat4DB8wJ6Hla+\nANdXJ7tl0TNumKcr6rvA55TIzvKEELHdN+1lX1/JEi3Ff2Dbp/XOj4wPlmo6\njMU6\r\n=KyF8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "8.0.0": {"name": "espree", "version": "8.0.0", "dependencies": {"acorn": "^8.4.1", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.0.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.1.2", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "08c92c31814c96c96c54d3a35cc80f1cdb420275", "tarball": "https://registry.npmjs.org/espree/-/espree-8.0.0.tgz", "fileCount": 13, "integrity": "sha512-y/+i23dwTjIDJrYCcjcAMr3c3UGbPIjC6THMQKjWmhP97fW0FPiI89kmpKfmgV/5jrkIi6toQP+CMm3qBE1Hig==", "signatures": [{"sig": "MEUCIDeCVr9l/ipozgUCr+5k6YW6Uyi00+e2Nj6LoU3LSPxUAiEAobL+xMPz56yWO5Q5ESd9/HtJ1hYs2wolzu9XmGP97po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg29pQCRA9TVsSAnZWagAACvoP/2KeiZewS2Hr/82MXPE1\nIXTvPrBaEObHtiKsY+LJvGayFDJJyhmmq/PLI05qL8stoT5XzeLZorn6unUI\nDVvBiBROCYTG3/7VJzbUO9G7t9hzXktx5CkaES8g0m30Z8Di1dIiCC6BvNvT\nvgM04lLnAiCDgCjZTwhO0LaL1ExAKdRGTuR5DG+k4YjtOMsV7vXlwGPO7pjB\nLNPpTzaeH6tyAp1Z7kIV/kljCXtmOc6xdIf8ApRuFfAa2wuwz0uyUIdti16R\nUMncCbovwAYf1uZjF4Zjq0D6w79xFEpIMFCZQy/TQy96yvMIYG6enQxf8xJZ\nNU3grdsd491R2ezhjGuLVDHy5zQOaHfDUPAUaGhsFVXjJsSSHRLYz2oQNIJX\nXcPXeYxghpPZd8j092s79Nc7K3iFluBvBkCMJBOr6iIPKEEllf0A7rvdbkim\nQK3wLy5NwMIIdx+iXSxvUNWMEpYsZWrUwvde+j+bXUz8Y5iuHsUu652n9j0r\nVOqeAz+uv8yrIMqd2DaW9rW+G4ySPeuUELHvETNXIuGm4lGu+dcHl2l7xZ1W\neTgeGPI0c70MqR8+eTqDmoMoiS1LJbcsNq97gsfgZWcxM3XMCIQKFEahC/ae\nvkEIs9F+oDtTYzeg3I52ELte6sjLwWKDhjyG0C1I7rCBkAzS6l879TXCSvbb\nw4U5\r\n=HZYO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.0.0": {"name": "espree", "version": "9.0.0", "dependencies": {"acorn": "^8.5.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.0.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.1.2", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "e90a2965698228502e771c7a58489b1a9d107090", "tarball": "https://registry.npmjs.org/espree/-/espree-9.0.0.tgz", "fileCount": 11, "integrity": "sha512-r5EQJcYZ2oaGbeR0jR0fFVijGOcwai07/690YRXLINuhmVeRY4UKSAsQPe/0BNuDgwP7Ophoc1PRsr2E3tkbdQ==", "signatures": [{"sig": "MEUCIHGNodF94cZ3BZT/joG5uu4GouFXqSZeTicx3e2GUdkbAiEAv5LZv+8E363p8BPGFtVCt/aXRvfYxEb7hIYKyqtXTCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 80528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhO8V6CRA9TVsSAnZWagAAhy8P/jPm4YOaqrBWr0pMvIwU\nC9XLSbisFIGP8YH+exgL8dt3iCgRbDfDWZSL+thNmAvKbrlKTyZvZXkaxJSs\n+TxYmxglo+arpev8+Wtc1XkJg0do6Rm3tP3hJEJQSowtJskwZxNNUsI8ZTZO\n8Tg5hMH6okQfYnGI3mnE4DSobXVQiWEr4AerBOEbZ4uXUODh7JNF9iyD3RnQ\nenYaenoEVYNKpTv1UwUMuvFCrLwvXprCobdUyUv0tfv7XO2n6Dohrjji72/M\n38Wk6ADE4qblsZELtsV6vEF6SUsbeJNhd/hZd4UL3n/oH3256/9F0P1Q6+24\nJxQAZwZARJ2nNAmFNwj7lvneTiej7Ahlu59bql9M7P8bTMwDZs8Ng33M5u8q\nGGMzu3FMxr64AxyY2FJQewDiGszI546pBAQYUcVtP3ac+caEtWGShqeW/f1x\n5vMHVJFPClbStlR+NnwfGHeSDIOQnhRVw+M4MahTaqRzgVWN/xhQyyrsJqan\n/3Vecvg6lOZ4D3zufdLEd6VUGKceIuVPEdDvX/lFJBHrGbvEKYngj1QnjJf7\ngbdmC1g6hAzX7PdGZLj3D+U7xdl/y4KH0AeGqoOU9imTm5lsC4bJdDDbvlmw\nV92BhGgncKofS1oqroK/qMe54A2SYHHO7Nhza0BMTlyvgnK0VDv3cLbcdCbM\nUk/g\r\n=HIgx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.1.0": {"name": "espree", "version": "9.1.0", "dependencies": {"acorn": "^8.6.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.2.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "ba9d3c9b34eeae205724124e31de4543d59fbf74", "tarball": "https://registry.npmjs.org/espree/-/espree-9.1.0.tgz", "fileCount": 11, "integrity": "sha512-ZgYLvCS1wxOczBYGcQT9DDWgicXwJ4dbocr9uYN+/eresBAUuBu+O4WzB21ufQ/JqQT8gyp7hJ3z8SHii32mTQ==", "signatures": [{"sig": "MEUCIQCcsMS/WW4xZrIYa2XoFWwPWgePyeo9qUEN4NrNCvs1dwIgXVVH7d0xWX4yev1am3IodQP6LEo4A+hwNfuSMsLeVz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhmcFJCRA9TVsSAnZWagAA390P/R7uWYcEANm59Hnzy9Cc\n6TzkC2w1gVQ1XE3x+XQQ8ISVaehP2fhguBXzOlNZZdsrB/sGZfMRwF3NtpMF\nI6IEwMvGBhOfxWraIBo8DZCYd1/BSu5x0z1WicMGzCR5mD95YOQm9i2nZeKU\nLYvTsuyUiNM77RcWCjvQMycrzRLAylHsiQg3nMGQinOTL+ZdVQj6se8iawcM\n5bI6/iKHkBdj/eJ/eePYX38zrFUDZIxdmSbAtOzhXOryRPsIrxGTU8EW10RT\nc/QcACmJ1j+Z0hoqyQjPD576Hsp6lKxrwGH6kQBMttBCxAiq4VWFlqLtqNBI\nfhfLs5Ne4wi8fy3WAPxesgDDH3udpviqkcl7c9Bv2cB2d2Blv7XlTA1rZ5+w\nKK+YdLSqkHL/1uNHtdjFM6lEMmO8F9NImcEtlyAS9Fah2L3G0vUXU5gDRYcR\nqspdDEZRFyoF0tSMao3TAy7VVFYmOLUUhOApeLzX8NCY6398ao1tvc966POA\nHej2JIu9eTUVY1v+C8oXACC3Jyffjt8f+qSdgh6dNhKlBu7i7XwH8POlRYkr\nuy57agbIJpv9tnJ7bKkZnekS4LTcaERwzpKMvTOSWsec6Y/FulcEMlstXho9\n+rvrf3ipOHutWgnTxgr0CyzHIXlLeiK5uFf2PfbynUsm6Yjet/hiI5eFUHZg\nClxO\r\n=51xm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.2.0": {"name": "espree", "version": "9.2.0", "dependencies": {"acorn": "^8.6.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.2.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "c50814e01611c2d0f8bd4daa83c369eabba80dbc", "tarball": "https://registry.npmjs.org/espree/-/espree-9.2.0.tgz", "fileCount": 11, "integrity": "sha512-oP3utRkynpZWF/F2x/HZJ+AGtnIclaR7z1pYPxy7NYM2fSO6LgK/Rkny8anRSPK/VwEA1eqm2squui0T7ZMOBg==", "signatures": [{"sig": "MEUCIQCi1n+uCKhcM2i0nEDaZq7hZfz+umd6wTBuPb1a2/tUxgIgGO3MhlY2Jgx7IWXMQZQgO69w1X8P8RB8hjx3WClKktk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqrqQCRA9TVsSAnZWagAACu0P/iAVeMChUBYxlcbjHC9j\nVs4rWbdbQUtzrzUnsNpcBjh1bNfXCoA07L3N1MnXxMnaYjboX2yn2RHF44Vc\nwHdCDTifGDyvfT/wAd8nPbgzRswsFsgJPD/JzoZuo5cpVXoNbj8fkiSpozhz\nLsUbeM3TwpnaTPut3/PZy/bFsTt49ku5b40U+C4wC84n3y43P30lmrhGKT9f\nFNmAmCpwLRm/XhLvAHcfaW5nzOv4ldqEyKKmIuRRbZrUEd8/30Aw6sD0kvy5\nSAwVyobiExvFQZWr7sQ7bZB90fCpCFTMWBIuRyZfarhcNIfjq9rN7yOiyENz\n/Eismxl0cFv2ExNu55PO2PvI13Po+R3fbmT25Z2gHoe6AkQBuP4BUJU4hoXD\nwN7SiZGxxhK7jz4lBxCuY0lwetNR2rjr/Z025kkAUn3ZR2ftVDrz1eZpdJR4\njkUnID2n73PEDtlvpmYnAZPATV45KksuR2XFJO70KmI0LIhzHk3TsuFHF7Nt\nJfl5mpIF3tnjzk52I0ctEvN6OWAQ9o6dNbxf/keb8NV4LblLKumLHlzv3XQ3\nPY1nddknFtA89ci+d4qZ1CY44loH6CTEeiGCQD883EjcORYMV190jRjoFP4O\nUHT2F1nRoVMecLapAjHZFv5oIA3UeQakzGLXimVwBQlWV3svwvaTHgYXWTYK\nUtMD\r\n=k5Qh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.3.0": {"name": "espree", "version": "9.3.0", "dependencies": {"acorn": "^8.7.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.1.0"}, "devDependencies": {"nyc": "^14.1.1", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.2.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "c1240d79183b72aaee6ccfa5a90bc9111df085a8", "tarball": "https://registry.npmjs.org/espree/-/espree-9.3.0.tgz", "fileCount": 11, "integrity": "sha512-d/5nCsb0JcqsSEeQzFZ8DH1RmxPcglRWh24EFTlUEmCKoehXGdpsx0RkHDubqUI8LSAIKMQp4r9SzQ3n+sm4HQ==", "signatures": [{"sig": "MEUCIFIFp+0KOiDboaMtej8/Wy7f65DC7I3HWs1cK0o9fWEPAiEAgwpo2O6AdNCczn3wlKLwutiiJGSNeOSSaqkb58bkH9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhz4zECRA9TVsSAnZWagAA2XEP/2+Pgh5h2hfsX3UOkL5/\nm6iW5hFrAEWQLdQrPfZRfADytZ4/IsDp3yEE8vRUBWPSkzXHR5S5HmKQOjUV\nM07fbudv3M2Q2uQieTa2vgf0QSWDKSbfKYtnYhRv8wwbbRFZdmlFQdhCite/\n+NECLg/rS6kwqtkD8JNSqYCtWzIH142nhrQdcd+moTyWhz9nHI1pmr13/h7C\nLorAbbjEL1wHlBxXdMNL6QHyssLK/DseKWRaorEcArRglBafmR18S1gIWVKr\nZoJAK4BQromO8NhP4Upgq2SRxB47pqyqAs08hEbt/+sn0iH3JwdNiYdOo3li\n47Ef58kE5T9Mp2WZcw/P/Ga9gmTMa1pTrX3F6h9SVlgnz1VHlpbm1nSjdLhK\nJ72+Nw7Yu6+vVwMc30qZMHvvZAfe+Naq9jbnopmKrin/Q9gn2p7L7jTxsCoP\ncV5/OMuQy5WDAlbnr4gECtwoHSuKQc7DBcWvZTlsaIiktklXA6w6S04Gt5vh\ntX0w9u+by+alGNXYhDTur3v4cQb1iz5/NF4RkKJU9B4mUh01Zh3o8uXowl/C\nKY5rzrr6eDe/ILez94+CA9w2OFgFR8OUxlGZ1YGKjHaGgKCPBXQBwfGhsrAj\nayrj5AFMeVU+nCoP4Yt6XuaMTSOyUV+OGFqQw0+vxNjdk799wnw1Uon7kzM8\nkWXv\r\n=UymH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.3.1": {"name": "espree", "version": "9.3.1", "dependencies": {"acorn": "^8.7.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.4", "mocha": "^8.3.1", "eslint": "^7.22.0", "rollup": "^2.41.2", "esprima": "latest", "shelljs": "^0.3.0", "json-diff": "^0.5.4", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "regenerate": "^1.4.0", "npm-run-all": "^4.1.5", "unicode-6.3.0": "^0.7.5", "eslint-release": "^3.2.0", "shelljs-nodecli": "^0.1.1", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^32.2.0", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "8793b4bc27ea4c778c19908e0719e7b8f4115bcd", "tarball": "https://registry.npmjs.org/espree/-/espree-9.3.1.tgz", "fileCount": 10, "integrity": "sha512-bvdyLmJMfwkV3NCRl5ZhJf22zBFo1y8bYh3VYb+bfzqNB4Je68P2sSuXyuFquzWLebHpNd2/d5uv7yoP9ISnGQ==", "signatures": [{"sig": "MEYCIQCi4vIVNLUqj3VApGUFSalfU9K4hf1ED0zvGjL3NZ1yTwIhALjd1/NdZRg3jGurHY5YX1icYqcn77Yqipk3bc2YN5e3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75938, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBuh+CRA9TVsSAnZWagAA4rgP/0YrNOaOWtdbGaVJK1Mx\nfFnMWvfuyZ5vhnipPAKQIw5Ur3YUWXT/8I1HTtA8k3GEdhr51FS6zrj97Xsf\nIXLITsW6CopMTYqbBPJnVbOgHtBFhI+Kcwh63D1NFH1eCI69d58A1dbB+9bp\n6JX/Z2sWfQSZEpJuAsAWfCBS9M/yX1dIQz/6AnCxmzoAEcNB2bagwRWmVEDk\n4Y2F1QDcidv5jEauFwYF+XbZlcxqxWVP0ytyArt/Ou8HpV19ZqEbOIhGElzl\npLKbBNrBcrCgDlMihhXNbqJQS4F+SJRjMSCXEy1x57WvrR5btT/xcR06HXqZ\nN+b4yYSujT9uqYBHhGGwojoFmESuJDu0lgNY2V6eWhHT48wMz1uoxbcY3Q0E\nm+aK+KJAAhtNQuRlfmtoNrM3XMy43PxtuefegoevqAuq2TMFVM1xykUEyDHH\nZkuzpJf41Wu1bpa79cYJlNReECMFw0keKQjTfblwZOw/+D9k6M4zY1dj5Nd8\nev0npdBuw75bQBvJJ4wAqcYecMBwZup8i6jJaSBMBCPPxzBidfur6E45Rx48\nBIwfWTvQKFL2cLa4wsrEZhB9Tyr9DcxJQMhEXpRUTTkgZ5EmgLNcFUI9Qwsj\nJpRdRVESIpIb/wK5pvG+x+KBAbO4wpStxigF9y9YKLrbVfEnLf+7Vrnxc9Zn\n8IL/\r\n=SIdM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.3.2": {"name": "espree", "version": "9.3.2", "dependencies": {"acorn": "^8.7.1", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "f58f77bd334731182801ced3380a8cc859091596", "tarball": "https://registry.npmjs.org/espree/-/espree-9.3.2.tgz", "fileCount": 10, "integrity": "sha512-D211tC7ZwouTIuY5x9XnS0E9sWNChB7IYKX/Xp5eQj3nFXhqmiUDB9q27y76oFl8jTg3pXcQx/bpxMfs3CIZbA==", "signatures": [{"sig": "MEQCIFWZSLKR/r+Wnumd9OAmusBNx/6PO91snJ6EGtJRUvhQAiBmTdDBGddtKqB1EtuMwv7eNZ05JwO+9+XNWDnV72ocDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidX91ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDDg//fE1ghyaofyh6tGNCJsx5oi6Ydm+4bjtgXDYWZZjvPcbJJH0o\r\nfOdPzmW30MnxMW25NO0RTbcgUiJ88OWDi5uWDZWuGvDFHElsMTsLECFvVApt\r\nXQZzyfOA1Mou21LdxmAFBVuWOJleTjuiOyfLPZa/0tsyJq597JItF296QCFh\r\ndAO653b+dWEansiyrc/qWJK8kBIjbWylnUXrF7FkF2GF6Oa3o2old5mygVlU\r\nvTltf+G0cf7J665CdvkF0Vuctm2338Ce58Y1Z6L7sholOmTOpyLmsz9Oz8bT\r\nFheg9Zfz9JwaAry+4qvOfc9SYNkgCSlGCZenJUkwcIFv5Mmyfxvz9VcZO+Ci\r\nnYgQtikvz7+TYPjagQjAjUKNOavk4wTQbzUO7qRCup0QGvjHKVN2SPbF2to5\r\nvYfv+qQwjXs42XEoG+0NEQloLKk9lcvmjTP8OmudGiLO0NI+qbSbzPOk0ZmL\r\nse2ffUv1DRX+99eZwZF2PVhWEAPRTvO7u21J76c8IaxfQzJ7NMJqLjWjCPba\r\nYhMh0tmw3uEO/RdERzjemxwNzYjXTgqDBpNcvRu34ZqhNZFfKiNCmi7O2Kct\r\n13UO1+kHLA+KBNV7kAaaqAeeMIIsyV8vvnp2hxobXkUesgzqQei3PMvPUKy5\r\nLo3Fm4zRcolV2sW+nSxBLZcXINHm8fOIRlI=\r\n=QMXz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "9.3.3": {"name": "espree", "version": "9.3.3", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "2dd37c4162bb05f433ad3c1a52ddf8a49dc08e9d", "tarball": "https://registry.npmjs.org/espree/-/espree-9.3.3.tgz", "fileCount": 10, "integrity": "sha512-ORs1Rt/uQTqUKjDdGCyrtYxbazf5umATSf/K4qxjmZHORR6HJk+2s/2Pqe+Kk49HHINC/xNIrGfgh8sZcll0ng==", "signatures": [{"sig": "MEUCIQCPveHVOR/WsVpLeI2PDUpA6bJzkQMcT+HypG8outA89wIgdH9gb9aNO6UX7520Jcc+o/KxTBGTxACJnYNo1bwSM3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi5uDZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptoQ/+NUkjk7trQ5iVvHdNecZJIKqozo4IkrbfJWNqHwIuAbhOKA1G\r\nQ3pOiWbdiGqGnSOdki5yWfnY7bGIUiw1vif+EvfkVAsGtXW1RhXnB45cwX6E\r\n9NHAw//O7ZBOuBCwdP6iHsOgaG13ijOnytHy9uLwtoixKLsrKRYfViff9JWM\r\n5P9Dl3jjNTLzSuYbrAxppsdJIi6Wmu8UzREbJtYfXUxVBgB5KkPbLI4Fy0+V\r\nASr4Jwu9k0hvbGfmf5ajt8y+KGRpRUa5isHFHokHUcOdOyXEhFE8GRGCf/8A\r\nHl9YCB8fPDxw0jAkkgo71omPzU0UIiDW1jNQ8C6d6CgckUXXN7ZgiEiDEQ01\r\ndUjdjVe+eTcn2Tn6dhxc7AV7tybklQIzpOBONFMrVJwECI73PUKosfIxsiv/\r\nNhGYPKn2wpfk4GGZ7A2MSwlp1W01Gz4n4brpYmtjTDSBTMCpSOUEluhg9aFh\r\ne6mVcP7sgBVXAVnzqUwgEGdVNCfhHy1i6mHg2GNquA5fEGE4RzY1tbG+05+0\r\nsmZ6g6trA/bwUN70FWhoxrkeyR33JG9PFHcNkefBEi1byaL+zccSvynpsVUQ\r\nIBQuzCh1cpuW8f1PclvAz2Bt7qiqNPcVJk2EVFVSd3Xpy7/6QYQHjLaF2HUy\r\nkO773JOU2uVXf0X+E5LLmR5PGS9ZUUegApc=\r\n=ydmi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.4.0": {"name": "espree", "version": "9.4.0", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "cd4bc3d6e9336c433265fc0aa016fc1aaf182f8a", "tarball": "https://registry.npmjs.org/espree/-/espree-9.4.0.tgz", "fileCount": 10, "integrity": "sha512-DQmnRpLj7f6TgN/NYb0MTzJXL+vJF9h3pHy4JhCIs3zwcgez8xmGg3sXHcEO97BrmO2OSvCwMdfdlyl+E9KjOw==", "signatures": [{"sig": "MEUCIHLTVrBX2PeN55du585l+im0vUjg8S3JKWpHkvSIR4buAiEAimzC6uBFshvvYPHj9Y/yiWm4BY2xrCaXq60ISyw9YmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCT0AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpebA//VLXeuBstEawmuUTIb8CUAZ4N1Hbo58+o3gILLXbV/Ixm6V2z\r\n/uIEjU7ChDksP8ZOQ9hUzhElpB3uUTDEFxNQtUlmjgoOz9bqloRMzXjsjM4U\r\nYrxi040h1qddOD2ckwIrBVxgUb/xz6uTGNDLguZEiQnQkEGpuy+lVtbLqJa2\r\ngQT3mNWCr8QGLfxI4fctqgfRyaM4hNF0XbpJei02SVWKhuTkoEA16jCm0aO6\r\ndq8rmI1i0BDn6P8h10wRqXgISJpljOMZveXpf7TDqPh7o5kpjp0UNE4ml+RS\r\nTcNhMyLq0EqwkJcpGqEH9iqd/go37JgyXYQE4KihJWm+7gNQI1QDx5iK1ZSe\r\nYB1TZopNgRHthDVCOsUr2IV5EMCe33dWNlbk5EfFnHdargducllccciENRwq\r\njVeT737dpBFSKFLH7nft8Non2nQjFPE46QV/29p0lRrbb+yhIy7y47e9MNth\r\n9b4/DX1RnY5UPaxDxC4fKmlKl86mtHrwUP6nEaPDpKFxC1BU4Jr0/e9/Lut6\r\nIPlHnCMwv/sebK1tYhRslBAb3Ciqbnduqg7Mk2Vci4jUpRHHEeLmLLArT8p5\r\nw5YVu1aLw6wfd5bM73T4BvAxNHF/FxA7nt6Y5GfsylFBgLT1DBLSSUJRse+o\r\nhva3zhm/cRvA8MvswAdk1I8NLa9DzatNAog=\r\n=VxQs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.4.1": {"name": "espree", "version": "9.4.1", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "51d6092615567a2c2cff7833445e37c28c0065bd", "tarball": "https://registry.npmjs.org/espree/-/espree-9.4.1.tgz", "fileCount": 10, "integrity": "sha512-XwctdmTO6SIvCzd9810yyNzIrOrqNYV9Koizx4C/mRhf9uq0o4yHoCEU/670pOxOL/MSraektvSAji79kX90Vg==", "signatures": [{"sig": "MEYCIQCSl5h5wmJrGCmjZew1L9+geKrHYW+PNtJQ0OHQpqaTSwIhAJ9LYhN0HVc4l77wd5ZyXEipxkGXr0Wlq6yxyNGv98Pl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZ0z8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW1hAAjrdoXLC8ojhhS9EpaMTW3VgJ2SSarRxDbQ1Z4NshrfC7phJI\r\nsLYHJTP7tIIoJqLGjaT38qv4rM4J5SIWWDZBwPZfWMfdOCz+B53NIwdwSVX2\r\nWuWIIJt1uMq4IUdhXu5d6BbzW65EBkdqSidAhBTrjeavMdo7w1OvEAcA7Zen\r\naoAtsCe5aNSjcvd/vzjpgE2HO6p9ze2iWq7h83/Vav12/8nAIAIbiJVr3FKo\r\nyJSskohkVBb7pC4q9ZvpAzDpTMfEfXm8FZho4GiHukH7jXmESL5bhRWu+JU7\r\nzGr8SeQHEhRN+7Vm8pD4kOsnvaLGs+N0sgJtD79Kbf3wp3HK+RoQWTKRFmvL\r\nuN2Nl56yFYMGkS++DDwknfW9s9QELD5HKr+xWzaCIgKyeqBmp/i8nBDj2LkQ\r\nbACIolHXUDIXot4FhB0aEnLQ/xRQxjD5vkXP/lOfBxpLnfE9EHMTteVspw/5\r\n1A+FGjhPFti6+VHX080dHPdsBcq80VjaZ9tKecz3DKZRxTii0oO4Qmc1418V\r\nNxETnfq/BL+9A9TGjppag9Q82DA3KHIyMPdxB051z0mzhs+vrOjebKyWzn8N\r\nxQrbHn1+qxiEYPGZg1l0d4DBRktRCrYXco9FG8FEu1jVD48MNy9T8BPTDr/b\r\nlOb6zJxgyhlP48i48ErudiE7RzYnRaal8Vg=\r\n=bNXm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.5.0": {"name": "espree", "version": "9.5.0", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "3646d4e3f58907464edba852fa047e6a27bdf113", "tarball": "https://registry.npmjs.org/espree/-/espree-9.5.0.tgz", "fileCount": 10, "integrity": "sha512-JPbJGhKc47++oo4JkEoTe2wjy4fmMwvFpgJT9cQzmfXKp22Dr6Hf1tdCteLz1h0P3t+mGvWZ+4Uankvh8+c6zw==", "signatures": [{"sig": "MEUCIQCYUnFHJZiV+R8nNWWELao6hoRnmgQVtktW60nCEiY+rQIgBB7kfPIpULbfZLVUkwaaN0DnXwHLRfahckx0zUdg0l0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC5TYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+aw/9GBSri0u0y5YKctiYYrvQo5bUBEAii+DUghCxU8upw/KJjIjJ\r\npMRLcncTgMfrj0UvMgiQUxgYvWUPjJYC9JIvB5koY9kq8VRrl3DNy8tR3Dmu\r\nbAHi6wskJaHbgSn44uwIvzv2kL2NlIVf7BzC0VLEe8fG9keR8a3+/B+JGkaV\r\nojxGmkQTCtgy5NGENJQkWkLN4WFUKrs8RC9EiKPi/jMQ+SH/30htK5jJTaxI\r\nBDbVGrt2y6FxBoB/kiNO48fRowkVLZQXkCdsKUblhZkYUsyhAvH+lu8YnKVp\r\na7nubHWcqMg8VZhtmPv/RHpdLuA854Et5zH61cjzOi5a5ncKHx6zHhtesbrJ\r\nKBUfGOVAu8hKs8t7PCcvGxFHouuG68ZUmhgDJfqByYIW3EkoT+RXtzH+/7xJ\r\npcGG0Bty8z6G+P2GbGIzeQJI3iIO6liyx8XY4DeHy9nFMfHjvjOcy4y0CynH\r\nPl+Ifnbt0pTw8effPAxrdeiKvLTul9WErQ7o0K0B8EoTSN1cURLYG0K/atZt\r\nOHYr7v6gsbZRVnpk1pyO8E9Oi2QbmBT7hukNOj9pkMiUT+QG9AdK0o/1uUfx\r\nt4rdRnXCRdcpVbbkfdANV+mzI7X5DVMBbNaWW1j1x8Xusuxv2+C0aeGs6aV5\r\nz+OmRIdF/CHJbpFzWdFRmJtqGeBwOStUvZk=\r\n=fXtT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.5.1": {"name": "espree", "version": "9.5.1", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "4f26a4d5f18905bf4f2e0bd99002aab807e96dd4", "tarball": "https://registry.npmjs.org/espree/-/espree-9.5.1.tgz", "fileCount": 10, "integrity": "sha512-5yxtHSZXRSW5pvv3hAlXM5+/Oswi1AUFqBmbibKb5s6bp3rGIDkyXU6xCoyuuLhijr4SFwPrXRoZjz0AZDN9tg==", "signatures": [{"sig": "MEYCIQDVnj9kFiHG+62ZtovrVaIpIfugQkEHiTOVr35pTMTi5gIhAOVNzj0H2Tmwe9Ke+iB8yidqaFS27dE2Gl9zjukN/SWf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkI1gpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonGQ//eVN2gcjadypUGoNZQdxAg9vcySBYVmsICCqXXRsocVGCxyyw\r\nSkHTUbxaHiCCUErjo5cPVJW+yLGR0s5aMh0FMCON27MZo2F3PnmrTPs4r5XE\r\nN75M7e5o2N4cGi00rEJqTQs+HJI7/O63gnrAAbJGAx+1+b5MpS8WIQAwXnmR\r\n9tcJmPjTPF9AUXvMJdHfbhV3CHhw0qOs7bKpPukIeygmQk88ioCKmgzWl9Jp\r\nGywv2pBkLmRPTbZ2yEsI6CHWPnVIgBHwaWEFGqj43WqWaEOdICI3cfVm/4+l\r\nvxiB6TzkHgT44m2yzzR5+95waxOrWYbhpNSVEeSKKxRhgBPyMZP1ExcseaSL\r\nHZPXU+CmtOszGbwMevpNNbRs4z1jqo0qSO+poat/CUgybeEJC3V4ctyxd+j8\r\n827+YQOj1LbNFL/u3cqnvuEJal+xEEU1r1LBgKDZsdgfyjzt9nbVLndF6Ix3\r\nntX5YWlhcFhPlGFXYh5TrJ9ikmV7UqlzM/+EWna5FeJW9LquzmH/hP8ckIFo\r\nWqcdmD8BnLMBo1BXcRTsAR8rbariKVdZfSxDk2D18XKv8tcb3aptiBAizwEa\r\nVAfCC00cszi1wyfNz1a0EyIdhneTql8LmyTjXk5Qv0IHf5th45aEO0KTrS5K\r\nCqj79nur5aMq8HSJN5pTGTsm+MivI+b+cCg=\r\n=n4km\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.5.2": {"name": "espree", "version": "9.5.2", "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "e994e7dc33a082a7a82dceaf12883a829353215b", "tarball": "https://registry.npmjs.org/espree/-/espree-9.5.2.tgz", "fileCount": 10, "integrity": "sha512-7OASN1Wma5fum5SrNhFMAMJxOUAbhyfQ8dQ//PJaJbNw0URTPWqIghHWt1MmAANKhHZIYOHruW4Kw4ruUWOdGw==", "signatures": [{"sig": "MEYCIQD0gbkqfGlV8aIVnvum1KTmRK4jZ6hfLO586vMk+LXvTgIhAMX2uTuPaguo9e1TZUlB8RfmtDeJ4018vtm9sUVNaJ2e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76539}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.6.0": {"name": "espree", "version": "9.6.0", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.13.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-node": "^11.1.0", "@rollup/plugin-json": "^4.1.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-config-eslint": "^7.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "80869754b1c6560f32e3b6929194a3fe07c5b82f", "tarball": "https://registry.npmjs.org/espree/-/espree-9.6.0.tgz", "fileCount": 10, "integrity": "sha512-1FH/IiruXZ84tpUlm0aCUEwMl2Ho5ilqVh0VvQXw+byAz/4SAciyHLlfmL5WYqsvD38oymdUwBss0LtK8m4s/A==", "signatures": [{"sig": "MEYCIQCmCDl06mxp+hi7s1BBXRnBBfQbyNuKSQtWOeADvCNJkwIhAKnHwmTru+GVTxlznFy9cSFCuiPP0vI35KlmXTYA2qzU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76638}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "9.6.1": {"name": "espree", "version": "9.6.1", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.44.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "globals": "^13.20.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "eslint-plugin-n": "^16.0.0", "@rollup/plugin-json": "^4.1.0", "eslint-config-eslint": "^8.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "a2a17b8e434690a5432f2f8018ce71d331a48c6f", "tarball": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "fileCount": 10, "integrity": "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==", "signatures": [{"sig": "MEUCIHoFsX/QehBa6wJX1N2vUvd6hfkWiLhZHXKklvp5kqsAAiEAkCW5WkTHD2B+2SubGRuJUtv0uPXWAcahgNZbV+qJyQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@9.6.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 73642}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": "https://opencollective.com/eslint"}, "10.0.0": {"name": "espree", "version": "10.0.0", "dependencies": {"acorn": "^8.11.3", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.44.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "globals": "^13.20.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "eslint-config-eslint": "^9.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "ce1411bb31a514797dbd29ee360e4f3404643096", "tarball": "https://registry.npmjs.org/espree/-/espree-10.0.0.tgz", "fileCount": 10, "integrity": "sha512-gdlKrfXQWv/3vubKqeQIiBUoWeknNQVEDpKD7OD3bC53g5EKISTuhcIoA1H1e+zqIuosdKrKuTDMmj8eFfhOnA==", "signatures": [{"sig": "MEQCIFc/H/u+fSd9tGWndouUAKBJzHwrbzGboU19rzYcBTPZAiBioFn38ACslW2ApfkLHGpQD1UAQ/GaSLUILRQ9CAqFnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@10.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 73589}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "10.0.1": {"name": "espree", "version": "10.0.1", "dependencies": {"acorn": "^8.11.3", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.0.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^8.44.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "globals": "^13.20.0", "shelljs": "^0.3.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "eslint-config-eslint": "^9.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "600e60404157412751ba4a6f3a2ee1a42433139f", "tarball": "https://registry.npmjs.org/espree/-/espree-10.0.1.tgz", "fileCount": 10, "integrity": "sha512-MWkrWZbJsL2UwnjxTX3gG8FneachS/Mwg7tdGXce011sJd5b0JG54vat5KHnfSBODZ3Wvzd2WnjxyzsRoVv+ww==", "signatures": [{"sig": "MEYCIQDDzbz+sRPhteaT0ta4ttHfsJg6LUgLDr2TRhXGxI6dqwIhAPK7xwbbnYezbcJAcsNHJtjCrH6CMkF7BXnMB49NFrqq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@10.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 73589}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "10.1.0": {"name": "espree", "version": "10.1.0", "dependencies": {"acorn": "^8.12.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.0.0"}, "devDependencies": {"c8": "^7.11.0", "chai": "^4.3.6", "mocha": "^9.2.2", "eslint": "^9.4.0", "rollup": "^2.41.2", "yorkie": "^2.0.0", "globals": "^15.1.0", "shelljs": "^0.8.5", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "eslint-release": "^3.2.0", "@rollup/plugin-json": "^4.1.0", "eslint-config-eslint": "^11.0.0", "@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.2.0"}, "dist": {"shasum": "8788dae611574c0f070691f522e4116c5a11fc56", "tarball": "https://registry.npmjs.org/espree/-/espree-10.1.0.tgz", "fileCount": 10, "integrity": "sha512-M1M6CpiE6ffoigIOWYO9UDP8TMUw9kqb21tf+08IgDYjCsOvCuDt4jQcZmoYxx+w7zlKw9/N0KXfto+I8/FrXA==", "signatures": [{"sig": "MEYCIQDKSZQegGInBR47O3/ecchFSej5hmtiWtv5i5YjVZ5EUQIhAIy1zbPqYsSOJanzJgKJk2mfK4B6m2VBJdsaiRFFErCn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@10.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76457}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "10.2.0": {"name": "espree", "version": "10.2.0", "dependencies": {"acorn": "^8.12.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.1.0"}, "devDependencies": {"c8": "^7.11.0", "mocha": "^9.2.2", "rollup": "^2.79.1", "shelljs": "^0.8.5", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all2": "^6.2.2", "eslint-release": "^3.2.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "dist": {"shasum": "f4bcead9e05b0615c968e85f83816bc386a45df6", "tarball": "https://registry.npmjs.org/espree/-/espree-10.2.0.tgz", "fileCount": 10, "integrity": "sha512-upbkBJbckcCNBDBDXEbuhjbP68n+scUd3k/U2EkyM9nw+I/jPiL4cLF/Al06CF96wRltFda16sxDFrxsI1v0/g==", "signatures": [{"sig": "MEUCIFrWVKbzTgVCej5JCWRxkM1cTPucSeQrJSdi/ZqtmQxhAiEAmIwF0GM3FklmGD6tqkrNX6+DSpRqe5dR8VZ8q6nV104=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@10.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 79225}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}, "10.3.0": {"name": "espree", "version": "10.3.0", "dependencies": {"acorn": "^8.14.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.0"}, "devDependencies": {"c8": "^7.11.0", "mocha": "^9.2.2", "rollup": "^2.79.1", "shelljs": "^0.8.5", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "npm-run-all2": "^6.2.2", "eslint-release": "^3.2.0", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.3.0"}, "dist": {"shasum": "29267cf5b0cb98735b65e64ba07e0ed49d1eed8a", "tarball": "https://registry.npmjs.org/espree/-/espree-10.3.0.tgz", "fileCount": 10, "integrity": "sha512-0QYC8b24HWY8zjRnDTL6RiHfDbAWn63qb4LMj1Z4b076A4une81+z03Kg7l7mn/48PUTqoLptSXez8oknU8Clg==", "signatures": [{"sig": "MEUCIQDF/BhD1H5PJYtwo3rDN239Slh3NsU/1nvfITZQGKZWpAIgVZVgVNfAgF4e3/6DFsFZkNpeXZ80I4av0s9ZPk5s8ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/espree@10.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 79348}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": "https://opencollective.com/eslint"}}, "modified": "2024-12-10T23:44:48.405Z", "cachedAt": 1747660588975}