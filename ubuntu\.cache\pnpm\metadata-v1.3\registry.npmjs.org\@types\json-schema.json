{"name": "@types/json-schema", "dist-tags": {"ts2.2": "7.0.3", "ts2.3": "7.0.3", "ts2.4": "7.0.3", "ts2.5": "7.0.3", "ts2.6": "7.0.3", "ts2.7": "7.0.3", "ts2.8": "7.0.4", "ts2.9": "7.0.4", "ts3.0": "7.0.5", "ts3.1": "7.0.6", "ts3.2": "7.0.6", "ts3.3": "7.0.6", "ts3.4": "7.0.7", "ts3.5": "7.0.7", "ts3.6": "7.0.9", "ts3.7": "7.0.9", "ts3.8": "7.0.9", "ts3.9": "7.0.11", "ts4.0": "7.0.11", "ts4.1": "7.0.11", "ts4.2": "7.0.11", "ts4.3": "7.0.12", "ts4.4": "7.0.12", "ts5.8": "7.0.15", "ts5.7": "7.0.15", "latest": "7.0.15", "ts4.5": "7.0.15", "ts4.6": "7.0.15", "ts4.7": "7.0.15", "ts4.8": "7.0.15", "ts4.9": "7.0.15", "ts5.0": "7.0.15", "ts5.1": "7.0.15", "ts5.2": "7.0.15", "ts5.3": "7.0.15", "ts5.4": "7.0.15", "ts5.5": "7.0.15", "ts5.6": "7.0.15", "ts5.9": "7.0.15"}, "versions": {"4.0.0": {"name": "@types/json-schema", "version": "4.0.0", "dependencies": {"@types/node": "*"}, "dist": {"shasum": "fbf6c078369546bd15ffa78ba7f3f2e170480769", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-4.0.0.tgz", "integrity": "sha512-hFDnJnmgLvSYyx3eoOiZFSpME64YDzAdA0rYVNhzh0XewPDp1no5AfnvAtvRf05aK1MR5UkRUqy9ng3FnwNxZg==", "signatures": [{"sig": "MEUCIFvH2ytlMgLXFACIJVwEA73Dp6hMsveS4L5w1WgZDjFrAiEAvo3jczAmqsUlNRkIFxPXJx7nz/iEbIPU5c0RRaCTJ3E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0": {"name": "@types/json-schema", "version": "6.0.0", "dist": {"shasum": "5842b43db9d199a2dabf6fe52b6d09200e8203bc", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-6.0.0.tgz", "integrity": "sha512-pPrN0ECtb1iCz5M47uYefeECFzsSn84pRe9qnYpV9PcIurPlPieJiwhbCito+YiUsX38XydNvIDy0jixSUFlYg==", "signatures": [{"sig": "MEUCIH2uaM2iKNuKk0xnlpY+igOtm6/6efCZsG1zBsqWfsO0AiEA3pL9ZtfICp4Yv8e9xabbwf06p2O/pdlhFda4zn5C1vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.1": {"name": "@types/json-schema", "version": "6.0.1", "dist": {"shasum": "a761975746f1c1b2579c62e3a4b5e88f986f7e2e", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-6.0.1.tgz", "fileCount": 4, "integrity": "sha512-vuL/tG01yKO//gmCmnV3OZhx2hs538t+7FpQq//sUV1sF6xiKi5V8F60dvAxe/HkC4+QaMCHqrm/akqlppTAkQ==", "signatures": [{"sig": "MEUCIBR3SSTeBCzmZ2vq7iiFUk2LrrYTvSavhQmI714NEh2cAiEAlfP3IUitxDcrxsAuJp/skNrM2KrSdCB6ua5TS+PywEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21699}}, "7.0.0": {"name": "@types/json-schema", "version": "7.0.0", "dist": {"shasum": "5a2b780fec9f2cf851e1b5e0a29a4cac6c9686c5", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.0.tgz", "fileCount": 4, "integrity": "sha512-vKFTUFiUOLIoe80UE1l3ZvnlDYxgNCrpa/KEesJoTMIvWuI4zA6pxNlOBHR81ghVpyx3M4CRf0LPTd9HivK7Mg==", "signatures": [{"sig": "MEQCIFo84XdQsjAnn3UWxTs0zAb6zTe9riTiTHxXoxewj3O3AiAv5k+74uYmA0r+KCyq7ilzH4uK5wEcHDVUisb/xJZ4HQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbV9H+CRA9TVsSAnZWagAAlwgP/RgxBCXbw7UX2gc7rwL3\no9OyFtrd4gzvQOnSQ2XL5VOnyRXOl8UkSeo8tyma/LCXgYCb7FUEiuQow04x\n/GWkcM11wGJc+LfcFJLAxnVRtp29210sR4mSzC75cr0QvKp+3dRX1qaFcSMq\nKsABKZ61x1f19KivbwvtVVYenPDWiXnECWLOeSzGs5RamslkJ4C3QsrQSpga\noVa7bn5W/O1MGJKPHvCDWqmm+pyHFnVecRy6KZEWo/oyASOl5IXNwhyf8RdA\ngeBdQJkIgs08gpIp/2qwmh2W/4+SepBLn/UIA54IUP/TBdQirVX6GftGtUo8\nAfcBbUwG8iVXxl5XPiSdHKbO5ZSdMPkGy0axt65vRJ57MgTr/xY/mUq3cFZb\nRDS8UmiYGDVQDpRBZW74Wkq5AL1JMzM0fJqSXFkzEy5RluGghB1yjB65P5Qg\nAZ95tcRkUyBQEvR8k9mEeMjxxuWwBnk/GaDMRQs1RvTKeWv8yP1g+e5Tne9m\nhaY1mkFVR5LCCRZa5axbX6bG3WMUx5RO55gSFTpKGgV05nER/sdNbCxfDVMP\nJeuta8JJjwyou0uPWwj77jaKarUNHWAqfMOjCQP0p7fJzOkrBvRwZyc3pmu/\nKyesRkE0iUvX4I6cRuE4F52Lpb++TjogIik93W+5RBO60bXWVys75u9wHXGr\nCpSc\r\n=eJZi\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "@types/json-schema", "version": "7.0.1", "dist": {"shasum": "fcaa655260285b8061850789f8268c51a4ec8ee1", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.1.tgz", "fileCount": 4, "integrity": "sha512-NVQEMviDWjuen3UW+mU1J6fZ0WhOfG1yRce/2OTcbaz+fgmTw2cahx6N2wh0Yl+a+hg2UZj/oElZmtULWyGIsA==", "signatures": [{"sig": "MEQCIEHiA4bexXQ1PDfxzkW4LnrfkkJPLFCA/tcQ0Khnuda7AiA8Dis602ovzHqJeWhn6HThXInJQBuCyPiPxzFV5Tl9rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpBn2CRA9TVsSAnZWagAATfcP/0tOGlB65t+jEbEMHxJD\nc/F+EZfRsTvXHS1CypnFL7I/v/qLOLJTmVhwY0YoV+CNoCEAB0B0r8Q3QtLa\nIP5E5eCuh5oeHutTN7wMkCQ9uVspSTyjor5lIVCVarTB5hENZnzRhXftnqLc\nUok+j7TRHjce2kxgTEgzIxipj/il/psMMcPYNKiDMAAicPJOF6G0wkVh7DG4\nzWWsLekvHCgsOs+cbGQh6y5Vp11ir4Z4kYQqQ1jDez2eGnxi2bgk3Tr2A8Eu\n+Ac/R5oYEEdznlnyS1cp7SJOZgfxk6roVQsoxF8RHG3YZh02RQIu4PF+BK0J\nAu/qtjQqIvFNvvyOT9acGRIZrPlDpM5k+CSbglyTRDAy23G7XTEQd8mZB3Xu\nOaDwkeNg0qqD7CGFTIe+oTKhzHD3IccIMrp8G1QN2gWJ/hL39HqCFS76BceR\nktvbLiGqQcm+7uNyRL46cBibSXtDoXC7AsENpbC38kuCpkD/xYLRwiF/LJ3x\nCS/Zbxq80AIvfqoepjDRedOFcNO9DzvcOyhF0xQRSG4lgG3jlXczaV7H36EX\nZ0FJ9uGoeAYmqtwv2DOrRsIQNHVG3BAny//AJENh53HwXA25aKMtRq4tPJ15\njwk3GehScee+i19xFlwKMjBQxdOe58wJvCljcVM3PItnmTaBn0CUC9d+MzX8\nQRBV\r\n=fJ/z\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "@types/json-schema", "version": "7.0.2", "dist": {"shasum": "39e4bb68007c8cfa56393d867d192917a59b0723", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.2.tgz", "fileCount": 4, "integrity": "sha512-XawGUgMoPEBwyN+P8FzzH8RfoJcmkyNPdMdX/Ejeit8y5M/VllcBjyGS72a3SCAQedsSnVn2pnf3lQ0OVR7f5g==", "signatures": [{"sig": "MEUCIDfUGR8pvSR3rm8biuQU6GZPSyZHQ7Zl6mijXzJdiUFuAiEAgKY2UtXsJZGstFHjBGyZ4HjWIgqMBUmljGuOC69ANcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVK7UCRA9TVsSAnZWagAArNQQAIB/4Vk+FcWKUqxdfEFw\nkDaxstf0lE9WtLVOlM/0zVRI8EMrJkwlahnpmxe6ILn4cWzzRn4uv0KPp0XK\nHl0wvCfqXLCtJAaFJjC5WwK/RF8erA6ZRXUCNHGkeShzDDNlQMWNdSUkPNEO\nXfoifbgB+cTWjoNiFD1+7oiGE8LB9atLopPlWCmH0d0C66hgJ7HAInVVMlV9\nRNkNS8tU+5Tq8ap/woF0lXbRXfoXf/9qqUYeIVb81nEa6/Vr2M5lamfK4Ixc\nOV4zVykwI5tXQVHTjsw16VlO8HsnBHbAD2d2x8X6phj5NAZttx6RevRNm/yO\nwhmQgRlBjLE382c3khLZFIiBg7c1WvSkh6k61Vq3bTLFTMPX359Nf3Vc3Sw6\nIndn4i3+Ej5jLqHXpdnNXSS7NQe03E5iKx9efVuZ2pO7GkE+MUNJrmFTRJaB\nycSlvz5KOdhlHuAqALYbUAzWvkcp+F2+oBDxDOPOoLH3DIjtqv7jVNRFB7Rh\nm1bBCD+/mR4CliUSx7xP6EfUJzsPp/+ZTWggTBkjzX0Kn0PaMPvxzFMgXL9x\nsqbFPAAg4irFC/qlDrDXn1hqXkA79WkppMmEGxlncQOfzIKBbp+XJ6WoOHQz\nY2epkjuS9Kaa9FSebdKw7/TAAwbUH3UDG9Qtu7qmbHVjmLHJWpGDVDc27THo\nrVOW\r\n=c2xE\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.3": {"name": "@types/json-schema", "version": "7.0.3", "dist": {"shasum": "bdfd69d61e464dcc81b25159c270d75a73c1a636", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.3.tgz", "fileCount": 4, "integrity": "sha512-Il2DtDVRGDcqjDtE+rF8iqg1CArehSK84HZJCT7AMITlyXRBpuPhqGLDQMowraqqu1coEaimg4ZOqggt6L6L+A==", "signatures": [{"sig": "MEUCIQCC4IbPC5meOzwpaqm2yiQRwBpIzFldL3gpW5ydoVw1kQIgboqmj495B5hPvzPkZ5qQKCNx1lo3J0MBRYkKqsHM4Mg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZG0uCRA9TVsSAnZWagAAF9UP/1djLVcClBIV/T545nmZ\nc7b4V59ZGbwTeKbXoqbFaUcqnsxYsSw1qpCqYK2zy42vmeXAyxBZqhjZmzlX\nDasp0JwEkPkoUpupQDNjpuSD5IhvTppdKIk2auTaiIMMlDCak8PB/dLWSByM\n3TMwmSJVenlGfIybN8+tHKG357twTr3DosuINPV2uxlk76NKgoiKssA0hJVD\nn7vOzYcuYi3KbNhAHDV2/JSKelA1L153+XQvptnId5M3mE+hcnjcgGIo5lh4\nkHyeYYhR8VHtCxIDmNxCRr1HYkTpDeQi18lLwv62vvIj3c6eg1nAvpQOXz1V\nyCQsKRY6EZqoOLgrKjAvHaCzfA9DvvLY8Yrfnxh4ZlhDhGh13kbRPOj59H1T\nfO2XTUP1vZIWWCO8qn4hhHhuDF/O5H5R7v1Rxx10xAueBcIQSBpVJWSsp4mQ\nfI5ak7Jv2ykaWWzzge2qAXzWYcpEU6/awz/4SwpO+RDDgScTGzBgw6bac6TB\naJsbIOuDp1hFhpGmoExBcyFjgvC6dl9O5QAzpTO0gbAQ1d4JpxB7vNXe2fEL\nfbCE9j1vkAzraFhci/bbm1vt42l38nqFQjwGEQ/qqEuNxgaFjEJ7THDyTH3k\nYg2kDa+szYhDoWui+geh+2xiGRsAv54i2/x4a9X5fBsrEnSlyd0A03qJXVj9\nK0Pn\r\n=dY7y\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.4": {"name": "@types/json-schema", "version": "7.0.4", "dist": {"shasum": "38fd73ddfd9b55abb1e1b2ed578cb55bd7b7d339", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.4.tgz", "fileCount": 4, "integrity": "sha512-8+KAKzEvSUdeo+kmqnKrqgeE+LcA0tjYWFY7RPProVYwnqDjukzO+3b6dLD56rYX5TdWejnEOLJYOIeh4CXKuA==", "signatures": [{"sig": "MEUCIFU3+z8hZfMKp/UCAwPDqZaw2wPCXhCYrYI23po8lLtUAiEAnVx6WmLF62PHfHGAMcDfxhe7xebJa5q1aIC6VT7sksQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAR7+CRA9TVsSAnZWagAATlgP/3XPajRjXVADEoUMews6\nJ9ameDIDuJGFOtJtaCFJrcf4rdj/iCMmRBXTDSmbE8dPJ9sf4ZtIPd+6njCf\njDiEFTR/XgNSt/3FlPaFXgEOVn3/cGo/mPXRLUGIldY73nngJf+hZAPgXimq\niBvMazOckKzLi/pB4ruDkEeD171VfD22SD9YOrsMus056pTRSHxhQxPLtvIX\nELuFDP7dtOtUZlinypbDzQcPQJ8quiBHaTgVYKD+e4NecGBkzcFbzpxdbx2S\nkEH9rbqUsCxOKX7wJl1qBWTI4FeDXE/ir6Ro68H5DMQnrUHHfcpLTpcBWmuA\nHh9Q8/TpINQrcO66QA1eWzAgsM3gBKuyO21AaPAqie66Qc60TZeyaXQOK8dg\nz9KBcJQUVK38fHYfyQrEWeqD8Q/xtqsRwPS4ZQOI89Z2W//7Nw19PvqSgzlY\n4bu0h+etGwLmjtFxSNLUa17ve5hN1CvODN2TfaCaFjeSfMSTlC2pnrgAXN0V\nj4KxiIfN7IeyszCCZy/6OvHWWZh+H1SBIOAn5bpA3KuBNwURzHIZuxBKAxy0\nO28iR8o9oz8RnTKOz4M+rnUPfF5yA31zgrbskYm/GulzY2nyFamPUvxO5dUl\nJG8PABrzZIKYjh6TVrI1zI76qJ68V6pqF9svD9C7+AlD2AZhjIQJlDK5KI1l\nM+TO\r\n=EqR3\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.5": {"name": "@types/json-schema", "version": "7.0.5", "dist": {"shasum": "dcce4430e64b443ba8945f0290fb564ad5bac6dd", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.5.tgz", "fileCount": 4, "integrity": "sha512-7+2BITlgjgDhH0vvwZU/HZJVyk+2XUlvxXe8dFMedNX/aMkaOq++rMAFXc0tM7ij15QaWlbdQASBR9dihi+bDQ==", "signatures": [{"sig": "MEUCIQCSFggPg6PAAtCwn6HEfduENaIbk1QF+2jYTkXeQ9hqOAIgc3M6q8P80rPDUfGb0l7ixm1CDLmZpeRlz+/1iK6Iv8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe38FSCRA9TVsSAnZWagAAZN8P/jRirXBy7yEvJzBk3rIR\n54N3S0QojJ5tGKKJSwkFRI2MaJ1/f4LL/NPsMkGeUhxBjmqeI/CIXVc/bn38\nOmxPw16kzjSfDpRXRuOrnD8xmegR94gS65jrI0vICrYly14ifb+1lx3BWmgP\ncwlG4uNj5Iw3i+JIMZkerLc7ppNUTpbiHqpy1X4o6GwJMFIumzRCcxcPoOUZ\nSa5odIXAspZPEA97H4xQ94j5Y1gdC5clFOdhAIpLdl5HMF2ljF06yfKXx/A9\nwKGh4fc+8IzAUpbkn0ZswoiUhjbeQujmSw10UXC1gdSfYK1GIskGSTAj2D9w\nvkiJxb1+41WakS3N38TAEa27pGhe1ZJ6xmQaCDYxIfkkUIm0ZTUB8BhaVTMl\nnki6vpmLtfKUhSFPnTIai9rv75u2oWaUx3Uw+aPKCU705BCAyEuCFs9VZq1C\nUdqxR4jAzmcCcnCWNWCNT2WmlhFSxY2RD57Z/L35vSDOtLz/qyJyhhzPxZVs\nHGtfNfehMEc23qVAgI4Lu+zdg+9GFf5YgEypy1rPj9roKFVtPwRmjZ4K0ODs\nKC6ZmXEGUCAODUlkmmXcRD4FFRJFdx4a0QelrB+xmcU6jjsJwLH7Uhok5lq9\nFNyFvvCDPf2YXvNMWcT8Tc1TAdxu/1ObTlkjBZ8qcMij7fNP2BZkzyUKGiB/\nK/TB\r\n=2FKp\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.6": {"name": "@types/json-schema", "version": "7.0.6", "dist": {"shasum": "f4c7ec43e81b319a9815115031709f26987891f0", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.6.tgz", "fileCount": 4, "integrity": "sha512-3c+yGKvVP5Y9TYBEibGNR+kLtijnj7mYrXRg+WpFb2X9xm04g/DXYkfg4hmzJQosc9snFNUPkbYIhu+KAm6jJw==", "signatures": [{"sig": "MEQCIF1cCZJIznknZB8LzkVs/vSxvTqHn1BhGvkXyLZCxvgzAiAkstLHRXmRJdxzbrDqtCUG5E/nHORssWlVePkhyirw3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTW7iCRA9TVsSAnZWagAAjCoQAKGSM75Ki2DMgtzSIZAc\nVG2c2KRPjxMRx3a2p0L2oHcNAxeVYwcvURx27uo2P+t2Pd+ZXYpmbwy8zRcx\nWsXZtF+7cE00EI7CYll3Jdmg8RbG9T+sl5Cyvg5yAHx3J3QGRp+irtYXsDff\nPfNrOQk/9AY7df4DClE36rr4HTdEebIUdrzflMq3Ann47roujaHRbI7/Z4VY\nAnHz5i/AYvOglPFyyZCUy4QBoe3S1dlnB5jHJQYfMpm5d0ILK0cLFhw/iizE\n<PERSON>wkK5wBKforrX3tx7HPpZ18d2Y6urYhczAlZ1+diyPpKZkoWonOPvmmZF\nthFCaVndTQ1t5PrGslxiVCfvqKXMjquc5viPnGa6azrbffdcj4IrzSGWYDun\nhvRJ8uF67DtPMpXv9mANeonjXVF8i6AIa2/lqSeyOoIGwpu68GqxNLQ9Ph2R\ndVNUBg8hE+52F3ShjWIrO7Ye/vrHOii7NfJZao7kQWv/GY4bkjmgRSmzdZCq\n0Lw/jEj3hizL8A0Zlu+s+cqL7S2kBRhL4kCZ5fx2oKf1SStSzSECePl8dXY5\nas8q8L5gFBiDCv/GJ1ZID9vWsFPSydwBC2AiLgw1ET4ZtQV0Z0gEpHBRWGfp\nhvuZX51FUR3w2z4d8jfm6lVP+fFNwAozdba1yDQwXvdXnaLm+MnuZeHPHe7t\njNUd\r\n=vyv3\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.7": {"name": "@types/json-schema", "version": "7.0.7", "dist": {"shasum": "98a993516c859eb0d5c4c8f098317a9ea68db9ad", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.7.tgz", "fileCount": 4, "integrity": "sha512-cxWFQVseBm6O9Gbw1IWb8r6OS4OhSt3hPZLkFApLjM8TEXROBuQGLAH2i2gZpcXdLBIrpXuTDhH7Vbm1iXmNGA==", "signatures": [{"sig": "MEYCIQCU9RQIJOeR752KS2U1DeiSsStlPVFpAaQKOYLclgTZlQIhAKceZlC+pOwbPRGkH/5HxxzZxS35WTG1Pu74YmVCRyCb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgB2cjCRA9TVsSAnZWagAAvqIP/0uKlfVQIQZmcb7tfc2B\nEeqTH0ff5gMvo+uEamQGSF5oRRg1wrq6bKIsfcr2znpHYezNA7Ma2qormphf\nY9CiVZfFFW9bzfK+jNzPn0rwp14lc3akA6UAPCPsNI2vPM1tGWPiVHaWaPll\niMAm7FlFeZxooj0ON4UR13vKO2sVuTHea0+hidYq0n0nBuOAazKbqplRMZhe\nRqBSem0GlhGD3Zn8BUukycSprfQM1N+DVG2dVgcjiVjkRDgUiP/9+GNkSmk4\nho3ILE6+2iFNf1zj2t5r1D8L1o0VPXgqpeXS2No18ubovvNReZC9LgIyAJXt\n2ZodDZ2MDAbPe3947lPn+MHbj0RYgeI260m/27tDzjrlZfdqHabnz3NJNT2N\n2VRIiCg08HjS8GapQSv/YR2SrLqYi8qAaR4j4g7cv3cdwHFJqnxZZ4awZvoA\nvgAyVUmlpQsP26g7UaZf+QNLu7RWcHxW9X5F8o20LRw0PSdGRchYXEaqNuWU\nTc7wkDSkjNaC3sIE/DFwQnTjtBvRWvRDNHd3oJV+vj+zzOVIZwNdmWU/lh4E\nQqWNdSFJGtSUjJsatLHzmrGZw/ng24P/Vj4IoJ6TXkYu6WhRd5Z17ZCHSDeL\nL/2Gi8B8oZnmJNsA9qDq50TXoYLJLuk45Y40RJpnKWsJmoHKkDj5w5gBC/TL\nRX7V\r\n=ae7j\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.8": {"name": "@types/json-schema", "version": "7.0.8", "dist": {"shasum": "edf1bf1dbf4e04413ca8e5b17b3b7d7d54b59818", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.8.tgz", "fileCount": 4, "integrity": "sha512-YSBPTLTVm2e2OoQIDYx8HaeWJ5tTToLH67kXR7zYNGupXMEHa2++G8k+DczX2cFVgalypqtyZIcU19AFcmOpmg==", "signatures": [{"sig": "MEUCIFeLdQVX5OK+ee0WYIGGxDiY6XeTsoE+PeHn2W3QNoomAiEA7oz67dAzzoXeB5KLGKLH68IYVzsBoZdlnpGHWgVsp/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5M9HCRA9TVsSAnZWagAAa0kP/j3gDeHJuTG7OWOovyZI\nH6g/MMtjeiwOtRoR+70ZPOmGHfbn5HK69wA3pL185uZCSU9YcQE0l+MFw3+x\nIq61/gdRf1ScgytWznkCEnPDzjdasgX8aeAOCjqAc14U+VSRMUKlA9GVMiX2\nV/4hpogsXKwnFACRocclgFmyDyN3KfMMRxOBR6nUMXmdfEMhzNnZ1WWc2bR9\nKd8o6XXJI8M34ZOY9SnNHvPBAaft8G26GtAk5WNJnsXJy4Lv35/TNKJWrJqp\nokz0S1mnfIUOpAz8z4iX2fpmS0J0StU6BGARVkQHrvw/KV12/NGxRSryWLWC\nskJaj7NPQD5zP++fFs8utZCj1w4M2/SSr+ucJxXTyFUHWEJ6UbY2dRsvXjLo\nCo9cUJ27mYllJXcwvXWnPIRBJtgWHYTWzSiJhzpy5IXUrGlPyN0BOEeWmbdO\nZBHNoCU6WVH0N1nk/lT6XHN9dHa1hCB7Yi7u1R06ZSPYXmmrb7EFTwIyVjee\n7mjSOLk0LUzS8snZ+dSwuME5cZ35rKm9tHUg2sYJc0T8lriYjJavLUv3CPm3\nUbGkVDnoFsXguNwlYIPN1jWyobMDxjMl2P3BWTNaQS0DkONtUlPBm6xPwLAw\nqayOzoDxxvRB2U8h214Akay+CeNcfn03IM4kBRiLnXtzHJJckT2HFT9B4dkc\npb8H\r\n=Q7Hj\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.9": {"name": "@types/json-schema", "version": "7.0.9", "dist": {"shasum": "97edc9037ea0c38585320b28964dde3b39e4660d", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.9.tgz", "fileCount": 4, "integrity": "sha512-qcUXuemtEu+E5wZSJHNxUXeCZhAfXKQ41D+duX+VYPde7xyEVZci+/oXKJL13tnRs9lR2pr4fod59GT6/X1/yQ==", "signatures": [{"sig": "MEUCIQCRQOoAcKHpZspvaAXkKXOeZhruCf+BB2VRfBqk2FyP1QIgKdFgi7D8WhKHW5pSugt+XgeEKy0BXLYUtH2dUynKSno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCUxdCRA9TVsSAnZWagAA9tUP/R0Ru9v1cycSFA5TAKWk\nHjA2bONhXu5ub1ihkMig2ABThCuLv9VtNCTm9NDRGb5I71c7TuGaINClVIfV\njkQSgxamILcGVDHoiG8BwX2tX+s1QyWBCCjmxOSqCwwZw9LZiISlaQPtJutK\n7gVtXlDblevPt2+iARJ3Q0Da1XjX2Yv7Ix9GfsJiqjS59IVdLT9IzjrROySF\nCrzPe9veGejchxl2yX1y1GdltdsAeAAHADgQljWNGHU2iljq+alPyFde4Vya\ntrKfLQkzMzMWjlC57XmyYz4sIAy0r1mh7deE+eGEAA0c8wAbKyOfEah/RTTz\nVZjttvP/5VG0t9WnXIGRT8Owx8K9UuPpo58hG7xAoo26B/cBGmIt4YOkknW3\nR9FWD+A2EBll21/zv+z9pNoZmXBoljyOfAidcevb2ASAi3HThoAhP5+9H1bV\nIg9hFHXz9gh/NVQcFqswroH4w6lhOFY7KFbPOCy9e4tZH18eEmHdBkxctxT1\nM2juqnInQgk0oJSlhToDy3078PXtoYIKL6LyAzw8mEoXBWjcPnC72fcwHOLy\nEPaWGij5zGKFYWGDW2tl7hTrKCxsQCpofRGWWiOt1s1b3sgMcc6ornfIVA/Z\nRck86bWr1g8MG5gKM8Lu7oTzLhgacw2MYJ2RYiaHj5eCnrtC741S24qJv+H6\nNvmP\r\n=muKT\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.10": {"name": "@types/json-schema", "version": "7.0.10", "dist": {"shasum": "9b05b7896166cd00e9cbd59864853abf65d9ac23", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.10.tgz", "fileCount": 4, "integrity": "sha512-BLO9bBq59vW3fxCpD4o0N4U+DXsvwvIcl+jofw0frQo/GrBFC+/jRZj1E7kgp6dvTyNmA4y6JCV5Id/r3mNP5A==", "signatures": [{"sig": "MEYCIQCDcSpG+kVF61vw1gdOhBreQy0kWeTB6xncudAWdOK+AAIhANU4Rk+L8uM+pYuM/XRa2Vcj1AX4mpxcLwLe0dSwWuqF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMdGwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqySg/9GXs+d6uwgq59icoP/80ImGDa074rthlNXH8CWNk8aUlF239q\r\nlRbFA1eljl1XmEq2y+hWRlDQADwIpshwg8GWCypHl3Nr4vNzFsOTEEFH1z4P\r\noRIoEGfu0GNdLwttV9DAPOBILCgpcsPVe7YjGE/YWZmrxd7ExdmTPOLMCAbi\r\nHSQB3CWvHDoI7eiFBWbTSSsMoD1yGCiGVkQWl/Sg23c8hgL9jgS1/azaszEN\r\nFsbwkh7O7zs8tZHYyHM651X90xJMdq45gmCDmjebqQU6giUWgaA9Ie/sUUCJ\r\nR2hcwssPPFmnx3yL+qlqxHJ1KMN4FMoPKy6Cy6h72/rhwg2t3Dn04KA1ZkUK\r\n8F/Vqx1losD2Hl+1IIMk2LaHU8YgX/9MdjjWd+urQZGX+TOt1FbszZ88MGa0\r\nN4eOJSQeVPYz5CVHO1l64UStXudq6TNB9zROEUgkNKRPLnYUR0ypxb5Wf8lV\r\nRYvcz7NSivk/cJM0z8dHx/HfPgGgX4LEBkh3FJWwZkZm/XaseF38bkkUuUK8\r\nHJW5zFMjM57u3T19S/ML4JYEkILGyNOGSCw/j+c++dmvZhZ4kO0CuINpNyKf\r\naOgcVgHkJWZe1BEAQqVrhMWLwCYXRahq4psjjaL8lb20rIkikZSRog0iPGDy\r\nyaGeCuXVVxmA34BoqA2xtz6Nw1uvo7ajCdQ=\r\n=KK3K\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.11": {"name": "@types/json-schema", "version": "7.0.11", "dist": {"shasum": "d421b6c527a3037f7c84433fd2c4229e016863d3", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.11.tgz", "fileCount": 4, "integrity": "sha512-wOuvG1SN4Us4rez+tylwwwCV1psiNVOkJeM3AUWUNWg/jDQY2+HE/444y5gc+jBmRqASOm2Oeh5c1axHobwRKQ==", "signatures": [{"sig": "MEYCIQDAyzrTpzJJFEA/Aw/lqJ3HOspVq0xd0gNMMiTkNj+3ygIhAMahmYglUxztVQhSyNVU5rNEV6rx2ftNeGDUT6cnbiAA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPctQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooYQ//XNxZmUsPF3s/7Fs3XOZnAIjxy3mDBKQB/YsSl14PpuonuH0J\r\nXGd5G/Yyb2kjtlmyTt/O20swx6JU1DF/nU2N/QhVVAgo/9NA4J7tOpVCBoYa\r\nJevanpLMXnVwFlNzO8I9DDoNRHeXfJxh+RvFRzTRwOLAkUntlimjQg/uhZzE\r\nN2mF1KfoAwKg6P7CTliYDwDDQaYjWTN+w6v4ktRFctgqr7g2K3tVZvCDiRtw\r\nQ8rXUyVtHA4vNSBo4PciQFLnJetvL5EyRL8qGXvJSUz5h6HpbI2qeoosQq2o\r\n3S5t0mR8AS1v1tOSN7MrAb8BD4BoP5gRzfr2f9Vs9aYWE0QyVv2Bh/ibM484\r\njLMTMkuh+w6PtQomMSA8oJe8fRFysgl46fq0VdGd8NzVhofonzZoc4MuHz6J\r\nYszRLOI0H7ARrzHrXm+QvQcClvsh2TQa01lqYvXPrT9+SLnBgD+bYsWkDYnN\r\nWhugZu9b6aaKx9tGI/Q3nQs1vR2pn7g61hwi1cIFKV93PVRO2IeE5Db86gAV\r\nfvviJHVUVTfU0oGCY+jQxjawgeIhreyRkTB5xLxyuqfVJSHFCr+8EcbNsQYF\r\nVdnRELkEEr9RA5+eR5BHeC6ThVka4V60vxeR7XsWzVnau9wIVnKmXg79TO8z\r\nD22DEblUUYzbuXhDsbrp0JIfh0gnA2fnmMA=\r\n=OI9r\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.12": {"name": "@types/json-schema", "version": "7.0.12", "dist": {"shasum": "d70faba7039d5fca54c83c7dbab41051d2b6f6cb", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.12.tgz", "fileCount": 5, "integrity": "sha512-Hr5Jfhc9eYOQNPYO5WLDq/n4jqijdHNlDXjuAQkkt+mWdQR+XJToOHrsD4cPaMXpn6KO7y2+wM8AZEs8VpBLVA==", "signatures": [{"sig": "MEUCIH5bmo9zsNy+FHWcY75F0J6x8IgJ7Sm16NBMobXBdNcJAiEA8blNyCU17dET/vh2919lUS75JlsJoUCxUhYjh8lrogg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32246}}, "7.0.13": {"name": "@types/json-schema", "version": "7.0.13", "dist": {"shasum": "02c24f4363176d2d18fc8b70b9f3c54aba178a85", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.13.tgz", "fileCount": 5, "integrity": "sha512-RbSSoHliUbnXj3ny0CNFOoxrIDV6SUGyStHsvDqosw6CkdPV8TtWGlfecuK4ToyMEAql6pzNxgCFKanovUzlgQ==", "signatures": [{"sig": "MEYCIQC8Hxj/4aKlailme9LINpwVIAYzv+aVbVLUb/uu/UZw1AIhAI7fOk7cLUBG+ENNRB4ry6jTv/nCrhn9jdOOWfcHyYf9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32247}}, "7.0.14": {"name": "@types/json-schema", "version": "7.0.14", "dist": {"shasum": "74a97a5573980802f32c8e47b663530ab3b6b7d1", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.14.tgz", "fileCount": 5, "integrity": "sha512-U3PUjAudAdJBeC2pgN8uTIKgxrb4nlDF3SF0++EldXQvQBGkpFZMSnwQiIoDU77tv45VgNkl/L4ouD+rEomujw==", "signatures": [{"sig": "MEUCICctx/h5Xj/lOc7II9mNBhT4m8fnKobjunzvD+8AsyVZAiEAn2EptjrIKYNtPBQ/IjUccTO2Jjj400VTR0W5vJ0XV80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31748}}, "7.0.15": {"name": "@types/json-schema", "version": "7.0.15", "dist": {"shasum": "596a1747233694d50f6ad8a7869fcb6f56cf5841", "tarball": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "fileCount": 5, "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "signatures": [{"sig": "MEQCIFc0f2/H6JU9C7JfRgHVkCAC+OOTz3xyR9EvaK8uT14pAiAqK9zDzN7/9Y3FlqfiAd/0qDeyNjOMdZ5B9r0g023zDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31748}}}, "modified": "2025-02-23T07:07:15.116Z", "cachedAt": 1747660588924}