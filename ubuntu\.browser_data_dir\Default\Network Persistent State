{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": [], "server": "https://5174-ivezwj4mi8cfqj5srehln-fa821dfd.manusvm.computer", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 3436}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 5059}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 2462}, "server": "https://api.openai.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 2552}, "server": "https://dns.google", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13395320732166340", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 6779}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}