{"name": "prop-types", "dist-tags": {"latest": "15.8.1", "dev": "15.5.0-alpha.0", "next": "15.5.7-alpha.1"}, "versions": {"0.1.0": {"name": "prop-types", "version": "0.1.0", "devDependencies": {"babel-core": "^4.0.1", "babel-loader": "^4.0.0", "jasmine-core": "^2.1.3", "karma": "^0.12.30", "karma-chrome-launcher": "^0.1.7", "karma-cli": "0.0.4", "karma-jasmine": "^0.3.3", "karma-phantomjs2-launcher": "^0.1.4", "karma-webpack": "^1.3.1", "webpack": "^1.4.15", "webpack-dev-server": "^1.7.0"}, "directories": {"test": "test"}, "dist": {"shasum": "e664bbaa7672482cb155617dd4d7b222afef57ab", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-0.1.0.tgz", "integrity": "sha512-HUUd3IvlMAr78AYu2zhklldnjrOlwEoAfZ/gmYlfU4XHsiYjZL7keGCaAnxlRB2gonq5DXzVAqL5aaoLWl5W/g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGd7y8gOLaiCmBXf1faRjCKuDx14y4x2Qywd0D/JCej5AiEA/N3QLUfnSXBIhvuhORdI3wb4cl4JKdQ2YWCvkHiiBVA="}]}}, "0.2.0": {"name": "prop-types", "version": "0.2.0", "dependencies": {"invariant": "^2.2.0"}, "devDependencies": {"babel-core": "^4.0.1", "babel-loader": "^4.0.0", "eslint": "^0.14.1", "jasmine-core": "^2.1.3", "karma": "^0.12.30", "karma-chrome-launcher": "^0.1.7", "karma-cli": "0.0.4", "karma-jasmine": "^0.3.3", "karma-phantomjs2-launcher": "^0.1.4", "karma-webpack": "^1.3.1", "webpack": "^1.4.15", "webpack-dev-server": "^1.7.0"}, "directories": {"test": "test"}, "dist": {"shasum": "b52108434bd826e1083570548d143d57f8474c31", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-0.2.0.tgz", "integrity": "sha512-ntUV/+435MCg1F3LeOJmAfWS68/ZuzJ/9Fhc8Qjj01xGXqEgPfx45rb6cyoujNQODmUmaqt0WcvH77/xVXRLEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDVu4GBkvWrjh6YQda3kPVouxSNctbVrloATEuVmNX/9QIhAJ9zbAKhdA8GK1jt4yYlkrffP0Cn0ezs8+aXwY0Dg56I"}]}}, "15.5.0-alpha.0": {"name": "prop-types", "version": "15.5.0-alpha.0", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"jest": "^19.0.2", "react": "^15.4.2", "react-dom": "^15.4.2"}, "dist": {"shasum": "a342108678256db125eee3d1ae2f889af3531bd7", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.0-alpha.0.tgz", "integrity": "sha512-bQenbem+kFZzN7CMGaIBaRlJWQvy9XtCgbm/ELg25wdnl/52B/6/OQI6Ha3vbnvy+quKpJJtAHYvitYtj0s6PA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDwxRNsdFv/4/MzindUaQYdj9yNTN6NjI1coUKiovIr2wIhANmSIcua77dANIglWn1UQu80kHPhpaxVOc3E3brjb781"}]}}, "15.5.0": {"name": "prop-types", "version": "15.5.0", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"jest": "^19.0.2", "react": "^15.4.2", "react-dom": "^15.4.2"}, "dist": {"shasum": "489d10598b03121cd11e7e656cb22de72421be2c", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.0.tgz", "integrity": "sha512-rxV5S/6ANvyi/ti5z50iPaDKk/5sbUuxs91mcnR9zToGRC5upa8Emu5HVCp0kxacLU26Zj1GDtnD9adb5VBJOQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZNy2R7e08YT98PUStF8RRqsYEnx9D4xCDTbWcParG3gIhAJd7XW8YyWTe8BjRb1OY3pvT7Kk+4pUKqmmWOOhM+9Sd"}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.1": {"name": "prop-types", "version": "15.5.1", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"jest": "^19.0.2", "react": "^15.4.2", "react-dom": "^15.4.2"}, "dist": {"shasum": "8cc590cc55b8afcd05dd66f18481890f8fe64281", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.1.tgz", "integrity": "sha512-P<PERSON>7fzpi20gAZRCfuIZZe7J79lnSRcD692cgi0MyYx1VJ9Iu28B3gWdcKoeNmnG+bH0wgu36lxRDSnPoiTVHkg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+UNRA072N6Ks2NfK6lJ3xT3/JstBZe+zZuxEjpAxUCgIhALRgCYpymLUTWbq1v3whvVoRHZCVW/E4Z8p6CGx6i+jx"}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.2": {"name": "prop-types", "version": "15.5.2", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "d89cfc2b595906cf79511103466cbb845fa21b66", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.2.tgz", "integrity": "sha512-rr0Xq3R/QiRO+H0hpnEtApdoKBz/x6iZeRjraYaOWVh0stA49zmJECKtnjHLc7hzZfzPd3elc8GDss+SVUKatg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXTC7Hn6oSy6Yckwy0viNdnzxv+CCrwUq7/merLofmPQIgFypNZT0p9HqQS8MGptXvRpa4TbhdnSjkTtuUNY4RNOg="}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.3": {"name": "prop-types", "version": "15.5.3", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "91e5d35f6d8878ed957fd1fb4c8491155a2c63e8", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.3.tgz", "integrity": "sha512-U7b2Y13ZBQc/95JO6AYbdbRzMS5EV4hXYqALaBBsfh/4hAAZwXWD1aLIJtLulOt6TVDspPAauhECP22bdE0O6g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCH9zrVXoDhcMie6XHnAZbO9BHrbqh4d+iYW+A5DZMDjQIhAPWsIYBHKTP9dpWJPIsRcVs+1MCFjRT/flQIc4H9e2CT"}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.4": {"name": "prop-types", "version": "15.5.4", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "2ed3692716a5060f8cc020946d8238e7419d92c0", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.4.tgz", "integrity": "sha512-+1SpAs2dp+fsZT+OvC6BV8ymEgDrXp+n0wjSBXiajvoBXum4ayGbUgMh2ageYV2N3fHk3ZBqv8rCcy69rCzjyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDADioSCKy+HmH7Za/eTatRJ+NoArHTN6bBYZKja0xhbQIgKyOLn8nR4ngc64pEbFih/+Zh6xELK+0CT/iljuDdDo8="}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.5": {"name": "prop-types", "version": "15.5.5", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "6f9f99347da15e5dc5260632f28a84f7537ca02d", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.5.tgz", "integrity": "sha512-ihX8EJ3mEVLcYuODUI8euFVzN5uCxWkrjJ/i7wY+a/uWxxynE9WFD+NBntE0K9DAxFw6X2pdjyj5ghs/BXb5Mw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyS38GosnkEU6FCgm4bJJcFbfu/6uWbCv/v0MlUdlhrgIgIIfdSj/9s7iMmUUmwziK96h9vdZyYHLpvGM9AXygTp4="}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.6": {"name": "prop-types", "version": "15.5.6", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "797a915b1714b645ebb7c5d6cc690346205bd2aa", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.6.tgz", "integrity": "sha512-vSvfaLNN4yOzO5f4L6urk+QX86kOBPZTdi3X6wCn+t9BkdPJo7jLuBLSYkc4wWR8jnnOu00CGmg3gkALv4QazQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC6PkrB6cQHs98HITnjfvfhJwQ6odINToSwC7Og2Wyg2AIgQiXQdB2GSuXj+hlZDa6PmH5hYkO8gW0YjIDlz9mkp/o="}]}, "deprecated": "There are known issues in prop-types < 15.5.7. Please update your prop-types dependency to use ^15.5.7 or higher."}, "15.5.7-alpha": {"name": "prop-types", "version": "15.5.7-alpha", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "030803566311a6c8772fa7f79da04feb913dd79a", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.7-alpha.tgz", "integrity": "sha512-bYH6lMN/57NFyV4R19LffZkJibS1cvqGOdYREv3g6/GifYPtiakHaHSqDBhboKR3lGXtaF5n/T0Q4wqjB5VyWw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICfSgnNoNh3WKCz2SF/catO2IPL/HwPpp3CtcBxBp1ERAiBejnkYv3YeEzvNaV8XBZlFGKnOVWgmwWA7sKwXI11T4A=="}]}}, "15.5.7-alpha.1": {"name": "prop-types", "version": "15.5.7-alpha.1", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "3403122b39c1aa21f6e5dd1b944e06773f46cb01", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.7-alpha.1.tgz", "integrity": "sha512-R<PERSON><PERSON>m5q9sTU0IbxWBpVsKEgOpFpxpGSI685PLPEaYoonHjLyLrRzVtTlV7Dh8898GJ4Wn2gDEj+/c8AE4n7z1Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyGcb6rHUlgGlLb6i3HP2ymPza9K72E/NFSUCfdES/9QIhAODk00AfOx9CEI9lf4u1ugfUeqA6zvb4PMz8SO5GsjLN"}]}}, "15.5.7": {"name": "prop-types", "version": "15.5.7", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "231c4f29cdd82e355011d4889386ca9059544dd1", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.7.tgz", "integrity": "sha512-U7peFF3mVC2Wt8M/Opu4xvTE33sA9R6D3i39li3jOIbSRQTCKek0BN9JJN7WJAKKyhM6l4Ey3PzIIYU4kHy+Kg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDYWY2ZyJXfIShZZTDVGRld6/OjzWHgmxm7ClFPiHQOAiEA4ARq1dd8fXtyrdgbQQcvchxZunCQZ0BdSbwcbYmSaH0="}]}}, "15.5.8": {"name": "prop-types", "version": "15.5.8", "dependencies": {"fbjs": "^0.8.9"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "6b7b2e141083be38c8595aa51fc55775c7199394", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.8.tgz", "integrity": "sha512-QiDx7s0lWoAVxmEmOYnn3rIZGduup2PZgj3rta5O5y0NfPKu3ApWi+GdMfTto7PmO/5+p4yamSLMZkj0jaTL4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE7kU2Xpko2kkDwg7vSFg5s52ZzsHgg8BT0TG77usWEKAiEAkTeB+y/tbVJ1PgxbJ6Q+FRc/6zu4CkaHLFFAePaNU60="}]}}, "15.5.9": {"name": "prop-types", "version": "15.5.9", "dependencies": {"fbjs": "^0.8.9", "loose-envify": "^1.3.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "d478eef0e761396942f70c78e772f76e8be747c9", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.9.tgz", "integrity": "sha512-Jc<PERSON>UWSZPwWAwHi5Pm3lOxGrgf1bxjYa3mvk6vo0oMRCUiP7z694tRFx6cLBpHpPDu8dU3v35LYmYiY+RWKXGWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCtpa6+dePqDw0tyHdV6hT5sSw/atoyzPjewuf0YnwMKAIgYW8IIl6cgOkfAkGII5uBBXDZ6HUPtMFVxXiP9ssD3vQ="}]}}, "15.5.10": {"name": "prop-types", "version": "15.5.10", "dependencies": {"fbjs": "^0.8.9", "loose-envify": "^1.3.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "2797dfc3126182e3a95e3dfbb2e893ddd7456154", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.5.10.tgz", "integrity": "sha512-vCFzoUFaZkVNeFkhK1KbSq4cn97GDrpfBt9K2qLkGnPAEFhEv3M61Lk5t+B7c0QfMLWo0fPkowk/4SuXerh26Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8tKXhrXPNvGgFIoXChhKHJAZ1vulOcdyFIXj0LPeoRwIgfbK2IZ34MobakhD/RqkdObh42mYM23ZXPba30DlNY/M="}]}}, "15.6.0": {"name": "prop-types", "version": "15.6.0", "dependencies": {"fbjs": "^0.8.16", "loose-envify": "^1.3.1", "object-assign": "^4.1.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "ceaf083022fc46b4a35f69e13ef75aed0d639856", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.6.0.tgz", "integrity": "sha512-H16<PERSON><PERSON>diZ8szYSKNkCpmKmS8BCogxyABjJ1AqQknIY2iTpy1xC04egoBAzjKm+WU2pbuNxFonw921dnxR0QYAdw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4D+Vgqemn/fRo0666v+mA65omkj5c4yRHoC886SRolAIgAIgY2p80BFSKBZV9Vtzr634t+4I5vwPyIUu5xYn0nIU="}]}}, "15.6.1": {"name": "prop-types", "version": "15.6.1", "dependencies": {"fbjs": "^0.8.16", "loose-envify": "^1.3.1", "object-assign": "^4.1.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"integrity": "sha512-4ec7bY1Y66LymSUOH/zARVYObB23AT2h8cf6e/O6ZALB/N0sqZFEx7rq6EYPX2MkOdKORuooI/H5k9TlR4q7kQ==", "shasum": "36644453564255ddda391191fb3a125cbdf654ca", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.6.1.tgz", "fileCount": 12, "unpackedSize": 79410, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDr96V7Tj5oofRgsMIFbchfBilabnLgiF3BNw/qLR2jZQIgFXLn23Bta776Fl3adf39mtfWiht/R1LQpE33ZYKJnFQ="}]}}, "15.6.2": {"name": "prop-types", "version": "15.6.2", "dependencies": {"loose-envify": "^1.3.1", "object-assign": "^4.1.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "envify": "^4.0.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"integrity": "sha512-3pboPvLiWD7dkI3qf3KbUe6hKFKa52w+AE0VCqECtf+QHAKgOL37tTaNCnuX1nAAQ4ZhyP+kYVKf8rLmJ/feDQ==", "shasum": "05d5ca77b4453e985d60fc7ff8c859094a497102", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.6.2.tgz", "fileCount": 12, "unpackedSize": 76276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKSc0CRA9TVsSAnZWagAAzrUP/1ZOEi3zvsfdPvaafERO\nSUERb8RXz95I2o2NPmPcWea67oLIzZryc2f3zEyU7P5c8qoBnaEfOI3Lmg/G\nXH8OKim4dVkLlEAfe44YfI/3MPQg6tzjZJxiuelZWZKi7Lgxt+jhpkJ9Pi00\nPHtwxge1WhkQPgFzOK/v6h9cgN/SQsV0UXhiii1ItVEZ4/vQ7CpU4KACop+Q\nrlg9yGCA3XH2sUNuoEsrH8zOrzToN/nuP1I6absyei/TJWWq81ODex22+nmv\nrWYs8UnJUEnm2SSXeiQvmay0WEmXYyv7B4kcK/L9Ja152OT/IJxYNx8Iunfi\nRuLBu6joFRAroHSKMQMaVYO7fM3zuDtWhNRo31baHR+0jOkYyfwZiZBw04Ap\nZpD6yJcrKhG82+ajV/Jexrni2LsEWvqP9twMTPbAi0noNC8JzcD6WwYCc15W\nNpRjBCVY0qwD7VZV7GQODI4yaxrR8EwoNCB+wuJeli0D6nRc+G9L9XMYwhQ8\nG887dJaU4sTYiW52JNDo041OmO2ICe5gLS6H9pVpzk2voz7Eivuqg4OLHA29\np9ZyGxSz/bYOQtA33RfuMeOJaV3jQs8jpg0nHJ8PLtZGh6zbAlM7BRtUMoNj\nrLZ12RKH894fu+1LpKdd319g8yAu4pAtt0Qyjx6V6zP3wwzd8KpKI4eIgBP1\nSYuz\r\n=ZY2h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDi751JFjBFnJccu9Ec3M088Lwu9DTpdMYzq+zf/RF8bAiEA1LAtMQ3z0FcCZX6LFbIU3AGzdaVmxutOxszLoGh/7Zw="}]}}, "15.7.0": {"name": "prop-types", "version": "15.7.0", "dependencies": {"object-assign": "^4.1.1", "react-is": "^16.8.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "jest": "^19.0.2", "loose-envify": "^1.4.0", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "cc45233a8ccef80f4a2e3a6bf575bee4e19b4a50", "integrity": "sha512-rlfizAIb1WrkMkbFm3uk7cZGqeVPOhHHMj3gy9FUHE+Ha5lKvbIzj9ygvLdG8Cv2CQEhQnP5djXS7pbmTOpBqw==", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.7.0.tgz", "fileCount": 14, "unpackedSize": 96907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYKwqCRA9TVsSAnZWagAARfoP/0aZI3rmQKPh2rZQjPYm\nP8OQmxp6PBkwOermg0ZiFubPAu8qN2tIFszpSrP6keJhmYRvU5GtmC44Ztmt\niybEiE79z7uHWxCsOM+qdta6V4NCQMxnOmrYgSeFbdC9h3O2c8VJ8cCLydlM\ncJd6MrUuJPbllMhaKUachMaJ2oaGo4pUTEtiHRKTuFI8CMdKXhK1qRI8aCnW\nF6LU26mVZbW0VCAOvPTpypZSVvs8JKy8i8Eq/umr/8udmDvZKadaKz0ZFg8a\nSZ/Acg9S8f756n96Lqlsaj0ftooBH9Ib2GCpReWWcdc1f1rXF65Gj3daFPeP\nuFOL/Fe4KuLpqr+V3J+t9/DCE5In57EuClTYiBgr4Yd/zR7ZCDqbiQwF22CX\n9+7lQgLoVzbRUs7gg9Xb00J+a1pnrHDQOJs8FL/9SeMuwDrv5zvsTbBCIk4L\nDhi0Fd/Clbd1W8tZGqKcy9h19RBq3MSg3FvrHqzmfO5Mzz8q0mFHCQ5osfsr\nxWXTYqi1ct03jSpnuJCqksZF0iVZsecbRw9dlXDhw9l6R9lCb0R2DrhHUd5f\n+4I2tgWE8WF3ppXBkEuRfMnCGnzHbGFwcs5LrpLT3YflXoPsCmfq42d1kprT\n0s9F5oOYvNhK+zepKv5vLEaayt4IYKC5+z636j8GpZsXCYz4ENt/p1CdeIc0\neXag\r\n=y+dw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdvj9r5iKtjj8hWRZ6F8buz8kxHop+peuk7luhCeSJZQIgNi82/FCtMYcYgsvs/HQ6xsiE3sqb0CdzfTmvCLVsW1E="}]}, "deprecated": "Contains template literal syntax; update to v15.7.1"}, "15.7.1": {"name": "prop-types", "version": "15.7.1", "dependencies": {"object-assign": "^4.1.1", "react-is": "^16.8.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^14.3.0", "bundle-collapser": "^1.2.1", "jest": "^19.0.2", "loose-envify": "^1.4.0", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "2fa61e0a699d428b40320127733ee2931f05d9d1", "integrity": "sha512-f8Lku2z9kERjOCcnDOPm68EBJAO2K00Q5mSgPAUE/gJuBgsYLbVy6owSrtcHj90zt8PvW+z0qaIIgsIhHOa1Qw==", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.7.1.tgz", "fileCount": 14, "unpackedSize": 97085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYPJ4CRA9TVsSAnZWagAACw0P/135v8Sw0MMEZgEONtEN\nVxowq1kLluUE6LhpysNfgZ09dMY4ehsTI+yxIB00UCjhCBqiDBJty/C6YQyR\nXvcNb8isCTKYg2NyNHAahrqGqOU+J4pHJEphds5hziG/WOZamvtAfkj+y5Nl\nTrN8s3zfRD2tyGnOVcm7iSpkOif3XPiYl4VhGMTu3SQcyMX/O0Xo+zCxZGKd\nAFvUZhon/GyvvBDvoFyCjRYl4Hkm5trpyJDeP9ZH/Ry/xbArGxw+lOTFgMbV\nT7NnJNK3/FWXSDJr1/IZF+m0BSFIiXfaDHkx/7epZjaZXCGS73vw3YiWBFcf\n8oSxKE+DVcKEW6gyIjVWIQfQhI6bA7JCLCAM6BB0eZWmoHF6hTO4eYkzYLpT\nyT+XjifAEb9dz8w0bKSe3ThHaH/pfsW3nM6h75r4IAuDZQcg22W1ZAh1qrOH\nVYIMKKGFfjIiqtmooOuf7IMrmHrSk8A7KZwt1g6wR3m3wXKyP3gYzHo2XoOz\nowK8Z4N65+87ecUIb3yBHTGNHoO4TCno5KLKbouXrwQ4jFOo7xQdJDqIPVPG\nlCWuTPwzQeDRGoV2LAKybut7niKueHvF3nv4RzKN/c/xA9yamnX06MwWXDWN\nVRHdDGxNEHIgAHdHer0Tg3AUH9TflzcoQ+4z+sYMjJvvmoTZHmNwp0d2cOJM\nAPTz\r\n=UKkZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCz63EQUeHxAXyooT5cWGjo37H6J9wEdrqTHotozGRVtQIhAOnLaVpPnwRZ7yz042I9dGjn8wrXctpkBSOqOMqUO6PS"}]}}, "15.7.2": {"name": "prop-types", "version": "15.7.2", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.8.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.2.3", "bundle-collapser": "^1.2.1", "eslint": "^5.13.0", "jest": "^19.0.2", "react": "^15.5.1", "uglifyify": "^3.0.4", "uglifyjs": "^2.4.10"}, "dist": {"shasum": "52c41e75b8c87e72b9d9360e0206b99dcbffa6c5", "integrity": "sha512-8QQikdH7//R2vurIJSutZ1smHYTcLpRWEOlHnzcWHmBYrOGUysKwSsrC89BCiFj3CbrfJ/nXFdJepOVrY1GCHQ==", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.7.2.tgz", "fileCount": 14, "unpackedSize": 97691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZHoECRA9TVsSAnZWagAAwR0P/jzBTBvTfkPZZMPkiAHt\n3dntlc5T9aX658uOmR619BTjO7F6vT1LdLrmv4c0IOrGXxW4TARIhEmzH5KS\n8PL0i1qep6LkpnvApKL9CiRJprFlLUoA3tILAdlZBJJryblLIZiyy4iw4OBu\n2mMvJwDLEa9vsTVlsNfqh0k6Jc9bZ/2GMcn9fLZgC/Fv4gWHtfCPw/QLor2H\nDp1bRsaIYQENYfIbavYbS/7zzHTi1ZZhpBqVVfC9akbHR/GWlwyuEa2SrcZW\nC4YNZXu4FLPHN1eMZBG8wSe1qGVpQwg8DHoCGqPlJllw5ugqJ6aRzZgUP5ni\n419C8lyQlVDUWU/dsCHroJ3JBjGgxiRA55Vig33pEyB6yaBAKhof0pVJJBdv\ngAhclLf5+jun4UijB3lTmwf9+bwZXDzeVs4SeAX0aw4wdUFLxLtHiQ+qaIQF\nBcK1enGUNEGcRQVrlPlvzaytpYdStKPOAUh1a4rWRQtLEvqOK99f3RmgoqCH\nQWa4jNJUcycIs2iglNZXlI12GMDL/FfZ2hNdQAV3YzGHuVlAaeVfz3cc4yit\nAMz6s8W6+KuU0ZxGTyC+4MPlVL2hKhksFDlb/loY+i/kdC5MkmLa60XNbF0v\nbHEuBU/H8+XGs5UIkiZMAiLU34Q2+/KFZstmJWe2o0OIyTDkN++BA6LeHzkP\ni4sp\r\n=AYwV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBx+meGXTNXD/NqPOLrRWSK940lDf4+oR4BQxpNGN6PAIgfwATJYPAiLo7w5Tdz1v2/yH5wx3bXiGLI1cEpGjLc0c="}]}}, "15.8.0": {"name": "prop-types", "version": "15.8.0", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.5.0", "bundle-collapser": "^1.4.0", "eslint": "^8.5.0", "in-publish": "^2.0.1", "jest": "^19.0.2", "react": "^15.7.0", "uglifyify": "^5.0.2", "uglifyjs": "^2.4.11"}, "dist": {"integrity": "sha512-fDGekdaHh65eI3lMi5OnErU6a8Ighg2KjcjQxO7m8VHyWjcPyj5kiOgV1LQDOOOgVy3+5FgjXvdSSX7B8/5/4g==", "shasum": "d237e624c45a9846e469f5f31117f970017ff588", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.0.tgz", "fileCount": 12, "unpackedSize": 94525, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhw6IGCRA9TVsSAnZWagAAfSgP/Rgwfm9D5qJJvWzhPI9s\nYc0XhsRZ4tSI2f/aNi57+gOwo1mj7wGNIeETPTeJ0Aqawx/AjInZGx1uqnaP\nTdiJ8hOVeAoeDJxJKEZ6zsDXC4A0eV7pvc3P+5a2gKycuy+GOoJDy0eY6QmW\nZC2Tsyc7SiL5Q2jZob9UZvBN5bX1JMwLR3ZTrZXwHTHzH6g6YRGATeZ355S/\nHFTL+FGDCI/HFhX9PCqfQkRABmigEv/wGxxAgXD+RKnPbrNooV8ZHcuNhmtI\nm/fIpk2CaF8xMz4q3nvnalSDyaHlbV0eB6qk/m3Ydn3Wf9UpInMLPE37+96N\n0MLn62hEMwzL7c1jZDB6/+Q6w4icXRoS8tPH0RfDEjynud+uluP+OZ4HYAk/\nY8P3xXulK7bgU9QB5bvWha/eMhW0I0POo3rTrfXIgH5Tiq/A84OEcs2oMC5a\ndsMkLMGB1s6H8UsPlmLOKihh7Dbrb9LKKNnbZq0NCHQLTUj0Wa4E2Qp3SW73\nSFgkpJ2BE1c/QKnQlW5zvCw4nU/SfF6O6ZFP5O13cf/s7BN3ei7ZvgVSN8YZ\nsxOwkmgFF7eFH7LFXP0PbTLh/CxumwyrWPpKODb5qLtCs9UNa7j1Ws2H3YRM\nXGYyedy046mquV+hCixibXTqrggpGKJVy+ktU4S4xr9f4UfDrYjJKBnhU6XR\nOvxc\r\n=oHcL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDW9b+xqQTSufWIJodR4eGUzeSbNqeilbevuj0W8CxTRAiAX3/qzILj99N6meHrklpTAhshp0yL04zDIJ7OKND2T+A=="}]}}, "15.8.1": {"name": "prop-types", "version": "15.8.1", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}, "devDependencies": {"babel-jest": "^19.0.0", "babel-preset-react": "^6.24.1", "browserify": "^16.5.0", "bundle-collapser": "^1.4.0", "eslint": "^8.6.0", "in-publish": "^2.0.1", "jest": "^19.0.2", "react": "^15.7.0", "uglifyify": "^5.0.2", "uglifyjs": "^2.4.11"}, "dist": {"integrity": "sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==", "shasum": "67d87bf1a694f48435cf332c24af10214a3140b5", "tarball": "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz", "fileCount": 12, "unpackedSize": 94537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1OGBCRA9TVsSAnZWagAAQOUP/jQkSH1VR711WunvFORR\nAF7hDK2MBglQeh3JOrLm68UNvVxU5Wo01M9kQxE2tixSX+aIv8KGxXMyQBNn\nOwoNPpvy2h8MMNG16vUrFnu5HkO/jnTbq7tbkmE+tojdXrCOgj48fQdhNcIf\nSzODODlgEXbE1m/f4xa2nl1W5IG2p8Y0hH8oP81XG67HKMCG03NpLWpnEWOx\nUOfd2D6g9lO8NhbFaEs4+fafL53OJehdNBJgZVJhLhlKPdEkNKaXDtd5CwGF\n5DFba7cIdSzbhYlT5abOE//VWoGOr9OjtLl5iSegqNY9rjECZhcvWFbPNVl5\nr2YoJMEL+R0vvufiiMO6nIiY6OkN6CqjHIOHgEiP5xYB8cXhMdI1yEF+OiNj\n7uhRoKZv1G+gExruy+1Ftm3AZU+bCXbKGb6IMdBob992OXMWDpvEvh2NNKLh\nYi6avrTFInnNdDFAPZknwQ7SAOn759oSyq7lmOW1deqAa7n6uixoT87tGtot\nLK+Onk+N3pVQBfigYPXAc/4RgA6YZh16eT+yhd/EX9lc0O6m9Yrd8ax88JtZ\nFyyngeQKjWW/MjtaaTNasahFdiw964cedQU/KtUg5h7cztWDq/B5FVkj4sHS\nvyBne7zBPhEqnSA/8txAdpEN/CgtRSAQ26HJtspJoENXKLVQFLKMEzqOX157\nOZxK\r\n=q+dC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDFKTxcQ7L//I6VGZgOwfotCO21ylQ/hnFk47qypS0+wAiEAuQUR5mg5GsHTR1eiNZ8Wsa4cmR2eSxYJJIdUM+kwQp0="}]}}}, "modified": "2023-08-24T18:09:31.990Z", "cachedAt": 1747660590860}