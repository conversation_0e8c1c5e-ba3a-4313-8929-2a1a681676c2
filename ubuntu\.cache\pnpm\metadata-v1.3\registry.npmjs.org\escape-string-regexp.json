{"name": "escape-string-regexp", "dist-tags": {"latest": "5.0.0"}, "versions": {"1.0.0": {"name": "escape-string-regexp", "version": "1.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "0ca42ef5f3d8499fbc239fa0409ea849857d74c4", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.0.tgz", "integrity": "sha512-XTVnVdVq6UQDfLUzWJC36BLvIdQigTIaDu0PK2kMJF+HM+hpEdI/etmCu3/tfJgOaP0qIcJcppO936K4CWZhlw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCibB3/7KrMUB66sDs7Tm92DuSAWGKWOoKYlJU1RvJ3TAIgMhJoPJ2p1JOoIyxCqAkJDIsVgYBdgWzNCFDQ811dxvc="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.1": {"name": "escape-string-regexp", "version": "1.0.1", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "78c76393afb84f102230d949a6125c7f3cf65904", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.1.tgz", "integrity": "sha512-UlEKXCzxdhMPTv50tzcbMWdd48Arcnno04VVAHT+w8+CdIB5Ios9xm/WGCx4O4gfqOyygdbAzjmhzBx5so0G/Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGRo7s961uJBIzcUtuUNY5AXiO6iZ0G+4vYcCCAh7UogAiEApRPU+4KjiY1wGzaM5x0JqobnYEdZ+gp7xXADf+awMjQ="}]}, "engines": {"node": ">=0.10.0"}}, "1.0.2": {"name": "escape-string-regexp", "version": "1.0.2", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "4dbc2fe674e71949caf3fb2695ce7f2dc1d9a8d1", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.2.tgz", "integrity": "sha512-cQpUid7bdTUnFin8S7BnNdOk+/eDqQmKgCANSyd/jAhrKEvxUvr9VQ8XZzXiOtest8NLfk3FSBZzwvemZNQ6Vg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCJ7cVQaIaIjmjKU4VFUVGK2aGnYRYsm9tKi6umvye+QQIgIwKLXy2wLHqmwZmtibtLaP660U4yxdnwAbjZBaLBsqk="}]}, "engines": {"node": ">=0.8.0"}}, "1.0.3": {"name": "escape-string-regexp", "version": "1.0.3", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "9e2d8b25bc2555c3336723750e03f099c2735bb5", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.3.tgz", "integrity": "sha512-Moms4LX81Yz8bfx4CR+r2LQodaAV0SSUHKacBi9bylry3/+ipQX9uOah6/zGXRfBR553xSf1wuOskE22/gAryw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2u4eKlbV5cXTRBK2NwKa2yWD6emYHZ9as/BlJdrEVWwIhAPoNI4Oe1KuhmRnB/YEMQSE5OGzla0XGQu2ukLOX659+"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.4": {"name": "escape-string-regexp", "version": "1.0.4", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "b85e679b46f72d03fbbe8a3bf7259d535c21b62f", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.4.tgz", "integrity": "sha512-/tdc8o79uSd6C+QB4iQOEfGAFT8Eek6QsO/xFgeULeij8OPp4/Xa4RRphv5lQoC0Frbxa3s9QFswlmBUFgs6mQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJyssTVXYBz+GEoxlVZmruzeGCRjpaFQTTJeefHZ/EkAiEA3oRJx/cFTonvCtcpQOZKmCLhkuFEFcaYKhNC2okZQZA="}]}, "engines": {"node": ">=0.8.0"}}, "1.0.5": {"name": "escape-string-regexp", "version": "1.0.5", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "1b61c0562190a8dff6ae3bb2cf0200ca130b86d4", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDBnyETIz0cV9oONwgyi0fSb01KrkXtb2t0H+Y3vhoK+AiEA+w+HBfUrCb6fhyO2rhjg0KB4dX7Ro9aj8Fn9XqUsXl8="}]}, "engines": {"node": ">=0.8.0"}}, "2.0.0": {"name": "escape-string-regexp", "version": "2.0.0", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==", "shasum": "a30304e99daa32e23b2fd20f51babd07cffca344", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "fileCount": 5, "unpackedSize": 3259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcttp2CRA9TVsSAnZWagAAH+gP/0jUFye+8JcZ4PlzibdD\nCYz15d9ULMk4brKKIDF8WvOSx+qrkGmQ9ryeyc5pIviaAUmU/gVim8G5vRiB\n8Osea8MHVebWkZNctNpYqS7zqb79RVUVbWDOP/VSr7Se0qdg+4VMo4LwQ1QN\nJ6QhswG5gON4uyWjR6Bpak5LGylVZf4WZLpaFho3LuScSOhnzvCEgFLwtKCf\nMJEnyZEiKB+ZJpJQzm0r3kGw8m8/4v7PjvVGg+lHCn+z5xEZlW0gNrUlrUnB\nMNkGsLDUv2gV3x3AnOeN2JtAOI0M4XWrp5XyiMF+KXTmYmItrx3PpRSOoVKs\nNwrTYpZK3LeqEKf6fsr0wTw/GW1BocBe4qAf+zVJ/se1txnM1YY2OkA+Mk3i\nBUvUCZN1GYkwuqbrhr1iPNv5ln9NbUHS9igHAuo49RlJ3khB0+MvNq4+7Hl2\nd4LdE9D+NW5qRyUfBGwbOdCkz/nFRGB/Ty38IqSPDYTta0gvnFVF0OiCFd6s\n6gF94/kgrIs18+wIpn1qmQOFXRyQAtaZF+T53+PG3XCQ4niBmakXjCl2QLpS\nQSjXAB+dNyYPl2pK1aVmtfclRBmFHXKHbfF6cr7eMJdte2LsTt9DL9l+HXL+\nGMf3gm9sURzxTxJp3udwyD5/c+P782djgu3X/gXTcMneRHnFjL59wgY4KVDN\n4OBO\r\n=x82n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGYWRahVhWUXx02aRAxhJx5D+PJcDJXaIN6CC3xTF2vOAiEA97QlXedNdAUaxK2nGdKbtKD/p2eQcziB0SyQqeAULtE="}]}, "engines": {"node": ">=8"}}, "3.0.0": {"name": "escape-string-regexp", "version": "3.0.0", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "dist": {"integrity": "sha512-11dXIUC3umvzEViLP117d0KN6LJzZxh5+9F4E/7WLAAw7GrHk8NpUR+g9iJi/pe9C0py4F8rs0hreyRCwlAuZg==", "shasum": "1dad9cc28aed682be0de197280f79911a5fccd61", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejKp7CRA9TVsSAnZWagAAvH8P/jdWe7j3n/V9b70XfrTf\n5eVtdcAp5mWuj+kY1J+6OT/HmZfaQGWCwbtykn9mc5hE9sSlDqeIj1FiyPGW\nSVpbUrzAsPrLw6zGTdld4KWMS+s4e1P03sml/dAGrVOsbglUVoDA6mOEJPeC\nioQL7gPDBEj6hwne5Ti7d9g0gvj1fWl40D9O+BXotOoDlXiiRkWD5BfJXFPg\nz39wiMUCjPQUcqGMPATyKoZDEEKlfNd8f/gGG10zQfM6v6mVWVO5v9tGCTc2\n4Ol/3idL1TcO6WriQzgeoAOoAxz1kCpueKGP1YxP42LK6h6sp1WnCw43dEBx\n4ztsb+uaeXRoK7AMnvNyOtwdAuwl33dwTjGfSPht1Gt8Zt+C/eqDixbRGom1\nJrojLMlFjxHTZ6K+CM1mvbEQQvsveVID8sByQKGYF/x9IwGk7jcvABDn4Im0\nLP1xBWQ4UxdnNxGoxPO25QXra5kpR9hT736xCjT+TCvlSevhVw2OkoJo2Wi2\nFNjP3Kz/StoQS8NqXNvAEY9KCQwvGCYZ8hMXSPH4DvFFHRu9QNm0xvNIjjTC\n9VPoiaizlb8Fe6UI6X7YC5x5SNxYnUe0nTlShmii2y7wFF6XrMYqjLQo09Rj\naIeZkxYudMEsAERDwntwq5HJbt1FZG3C+y3GqhsYTqtEkWUFJjQlY5ZtCyss\naPht\r\n=jjje\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIASA5LFIqwLHTo1ntXocNCA+nJUZBJpfiQU97OnJlETLAiEAxxH4/ksEsvNS26d6cEMstDBuShUEVVy0oPXD8g0dFTo="}]}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus"}, "4.0.0": {"name": "escape-string-regexp", "version": "4.0.0", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "dist": {"integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "shasum": "14ba83a5d373e3d311e5afca29cf5bfad965bf34", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoURNCRA9TVsSAnZWagAARCoQAIDrgan6KgXDUu7sPzbz\n9oiZFBPzKrp+vRltzovOSs/js5mGWY2aXUf0H2a8flCfE2kpMboKUOOTcUJ3\nWVDsgIhSr8KM5MP2oM7uWXV9ZamRp+ksHjQbumBU2a8OxakiBLQv6o6eF3DB\nIzZXYD1xb7BoUpHzMOgClabNeXqxgRsOPp3xkb04Q8Y5GotUZKPwdLhnf+io\n2SpW5xUEVzQSQSCqzpzqjyGQcwAlesqqUWc/INud8/suDEIpHSeeOv+dhhmo\nwo5v7x/Nw3uvGmDycwJ/UoTXVh9GXmy3EtY9NyRmdtThRl3MqQ6xi3z9FQ0b\nicl744RN+cexmiiXuaVkMccklpMAbNMX62VpRNMhPUG99HpUe/aAbRqGidDr\n2GTW+4D1JtCZq2yqyH91CTN0gqMZQOEeS5jS0qH0HGedKLZFynQAjSyNWevu\nra4MwFwC4jLbT/iNHE8/PWImYL8dMxKzXs7roPETZPOQuQhab3eAbOylS2dH\n2MY0HdtX0nhLyoM6jdBgCmGKwSuzVqAPeAUoke7ft75ez2WAwcPLK4yaobKO\n/4FYnpTTs5BAH403TGhuyl9mAmk7VbrCiNLUf8d5uHzMifO7bmgeZ0185M02\nGoqJxnF04MCN7cJ4wvtfJh+eAyQJXrXikEcOhJrMFth+NkwhqlFx62cZbcrE\nycp7\r\n=uG6w\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCtnB6D8NP2XpnmL7e2IvMJPdzEG2W8Z4U66+e2oVsQXQIhAPL8R6cRFx2F7KiRUjaUIdU7VF6vZsJjx21isqRe7RX+"}]}, "engines": {"node": ">=10"}, "funding": "https://github.com/sponsors/sindresorhus"}, "5.0.0": {"name": "escape-string-regexp", "version": "5.0.0", "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "dist": {"integrity": "sha512-/veY75JbMK4j1yjvuUxuVsiS/hr/4iHs9FTT6cgTexxdE0Ly/glccBAkloH/DofkjRbZU3bnoj38mOmhkZ0lHw==", "shasum": "4683126b500b61762f2dbebace1806e8be31b1c8", "tarball": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "fileCount": 5, "unpackedSize": 3659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgewKuCRA9TVsSAnZWagAA/R8QAKIjcAQrtpAGFWhms0M2\nudKffrknPQbQXcgJkJJrZEvxGcUk9Hvcka+R34RHwhm3jYtd3vubQk/AY4qR\n6IgtVJ6yuqq1Ev9NoeniZs6Mn6iS0nqluTK8YoZnupTJHAk/idO+SrW9F1B3\nEOzaRyhPCGaImNRCwNVy1RKXG67kzccqUGdqufz5sui1L0kt7kCvhUYRBQ44\n7iOxLRwTE02PpxyCN59jMEkxP26wAmVmyMMXuu5zOhUHZLX9hgL4AeTdeBrc\n8rP/VDYcck1tDYRHJnhB5VKi+xKqaV0TTF5s0dMJ/AgPoNeYpn9YNEEFZBt7\n3OqzJFm5tt2GwQ6NTh0O9bsGzxyco1wBLGWqUTWBf0mEwtZayLL3OKTNxZFl\nMC5hk8T1NrrVAOmj+Fdwk/eXJ8afhbiPVM8EDMgHFhgVFkjCfH7EN8MR4rs+\nJb2iVZX4F0RPFgmLUhu8cpdrB3+p27tBkeyoSHF35tV4W7LGuVhEMeYLy+HO\nwYTIa5ifCGckRcOVaxSyONM5Dtkv8RCGU5YYz9KXqwiFqtEc+arD43n3vro4\nnq7ZxBHrX0F220uhaxMFqNr1E66UAb9Dfoh25hjdt5U7zNdKQ7kQE/vEfz3U\ngcuO4H7PfM++Y1ZJrENaFM/an9+2xs0m8pmM61EWXM5WCA98/ALZ+PDCLDwK\n8+1q\r\n=iLSV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDriPVVBNO+MIoPnK6z99gUKJJG33qsCk8gF+RolC058wIgA6LiFjLoZbITiGeW3IjuuZtERJD9VzL2kRYgNwF08Ac="}]}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2023-04-25T16:42:41.472Z", "cachedAt": 1747660588962}