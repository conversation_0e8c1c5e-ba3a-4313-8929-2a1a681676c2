{"name": "@radix-ui/react-navigation-menu", "dist-tags": {"next": "1.2.13-rc.1746560904918", "latest": "1.2.12"}, "versions": {"0.0.1-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.14", "@radix-ui/react-use-size": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-collection": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.22", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4-rc.22", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4-rc.22", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "24748e79e02a9b84499ae3a5b0609d6fc422b961", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ZSLt54QdY/08s5B3FcTvUm+Sm1Xih56K87861OSIuWLfY7QG58tX0Y0CwN8n8Ei67o+Y/QhPPOZIoQ2OTPSLEw==", "signatures": [{"sig": "MEUCIFcv8zoLRHHUIgVQVsy310+DoMOmKhZizNJW6aFaNAqLAiEApAsyExb855vM3zzlhypxa8n/c8/cx8BuEZFxl1k1Qlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5cQ//ak/NEBRO3tD/f/shcKtFIO69FFxgtbj3xUaAGKNnDhMWiPqd\r\nfzW0xfBL707aRe0b4KqVXLLTnsrA1KyJBC7QFeHn7bnK9uJ1R/FzcZhkoAzu\r\n6C9TEwC873q5O8PjWkOFLl2wQZWqaGAhaUDw/WDzI4J1b+v8yOpbKd1zG5yS\r\nVymWYJVq3Eu8W/Cqw2B50ypOxg6flm9YksnGsodhkahtLvtiyuHunGSg4miU\r\nN9sLDnl4kA9cMJWch6hDKQ5dchXhslOlPqYjZMGpxZx3h4W96qqu4oydX0zF\r\nsVcKXzRpdo8MQD6Uy0dkk/DDAbxX87qc03nsfuIVI1zz8dRlC4zEqbDxqpoa\r\nvfeLYEtP02D5oLenELPNuXtiDNZguMiljjKaof4eFA0hWExfl9mAYTTuxWPO\r\nG1QxOxjzB2CKC8yjDiQIJGwygGB7tuyv980IPZtrXsX7UgAS6Oo+qiPE3gmE\r\n1JsFPfNHxdg2FtU/dFoZdkOSsw24941yKBZcvOxEpU4BSLCjs9/xQNieepuM\r\nZh4HE1KOnoL+d51I6wcHK2r3UIARvnmPg2Q7YB0nN21BGtWwSD9BUnAZplgq\r\nwSEdG8rdoAot7Z4ckrbAVsgOOgQssZ8mBD2hZ4nzTJgWvkWE1RtfxeKmuAfS\r\nDDeX/XqiReIJt7PuOCs5PzBVv06PeImJ2ZE=\r\n=GPQs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.15", "@radix-ui/react-use-size": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-collection": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.23", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4-rc.23", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4-rc.23", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "fe30daf2f559cd32a2f4e30f5e709bce5ecdc011", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-VRNfhZ78K2Xkpct++DSxVuvnGHfUe+FEvA3Nu6eRUsQMfapLchYs0FACC6Y/7I9lrmfoEEOUbQf4rIyMVXjRtQ==", "signatures": [{"sig": "MEUCIQCz5ccts65OeuBEUJ4LQ/RtiuJQ1nFF8zEPwog4EypvCwIgSkQyQw+gwezdaZcqRft/UJff190UG6uOK3JrTTQWD3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 171408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxtQ//WIPAIJxOr+2DN0VYmeEUYyn6cn1lWUrtcUFuUaet7Wf9MXsg\r\nIoN2FKdzEJXg2vCCjLrmoHaeHyp3cM4x6VlMAEG7aTkfrlZCvowruAcvSKvS\r\nKbL3CkzpqdGz/W9gXB3tO5QCqKHqYTorYtsZ804YZsEf+wn0++IGpNnYTSL7\r\n1bULZ8oj0Rmtk80Rl/2iiEm+8Z8nNaUztCo7ZvKA7yqopR/LIdgz5MAenxbF\r\n0aOHiaU7D0zFjG2JpEGowaHAfe8eefqggJCeEMrELtKoC0Bzg2zNHt0V4OJq\r\ne0qXwfUHfjWq4vSsvs3Im4tExICW0boCi9D/32mAhh/BJNIobltq0aDD6XaO\r\neduI5mGrqNFdbOpejVEA32aCD63g0+39Rsxmhk3Pn3CBHgrgMnJHyQoM07qj\r\nGdiDQThT163XV2vp78IvKESO+Wwn3CMO/w9YJnhgsMuMhmcHl27BHfsBmekD\r\niTPL5hKIAYfRvD1tE27kuzIxLka0V6S8V0QrOiwUgPzcXqH0gbAGTyftHhN7\r\n33UZD79BpAKQ6RZuVfeAgrECao3D+wWxNj+XJtsPmiGvnC9wxkYZrOqG4eQq\r\nVAcN0MLxfudrCtuguZR2psA+9Xs1kIw/mQmvvzEAQOaHntLgA2/K95/PZbDW\r\nZhon6Fx1lxfzpKHnlQ/P9E3VZoiEKrx+zVg=\r\n=4flV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-collection": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.24", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4-rc.24", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4-rc.24", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b3b89ab558eef8b243d8bd6eaeab0a4392c7688a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UFNOTyGD3TtIxI5y94wUlO2FrT4th39TG5+U/e36XUJQMiehkaEvPvJh5R+7EtgZAvJsI4yWQdk/iFsKoWCdyg==", "signatures": [{"sig": "MEYCIQC5zymlJrxSyCVWpcvBMHPAFJ+LIxcVwege8gZuFzwD9AIhAIfzLCXtHkQMOMT5KB9nuno1OQpRuAiCpSyKNptgiYnk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHug/+O/9IBHlR4Sc7BSzCVpkDjyImg4HXAKlC+VNX8aKlWLcpntfz\r\nTU1GfqJfaGbgLmuFcwZC45h7UMRl3UMPea9i0X/HpczDWoug+20vx2wTSUsZ\r\nTzNzx2GYMY2LnA95GAZSsmXtY4X0xgd+nv/ATy7DmoR7MEBdukr3kCgH4Pny\r\n3mZTq1qFuEfQ8aUJWBwCVRnYBRHqCidQhsM5dJNbdJmGnNKvsesY1qNiubkS\r\nU8Y7raTpy4ttdjDHAU7Yujng8SRpEv5QNQGyR6+sC71BpgebxKgvDRl3jtWf\r\nJJlZfqaGB4WJ7vZCk6jU1A7ggKgovHHRQTti8MuyREcmHdvehBkp4jO2zy9F\r\nWMJQXdEAYXw+HhS7ktsBReWXV0E8ZmzwIIGcuFtHj02S5cQOcvzxidcB97ra\r\nuiO8699gZMsiBzeZ61rn8ATlmBe+I0Db/Sdy0dflCw5GfGig6j+0lY0LQL5r\r\ns3SASi+gj/bzgBFRUhXcPYZ81pE3mBNvTZP+1U6S0NaXgbOkai+MeI0uMT9b\r\np0rLiGmFUX++LxJFZ/QKj1sE6qPcF11myyTu4hJdLS1shh3NJdaWafNQruiN\r\ncelUlPtQb3LC8aNfypGxKAykppoRV23cNXfkaObjoan0N5cZJS+t/1bsZ1X6\r\nDa1yQ/S2LYBGkPc/VY8H+cR3eCWicUEkDkk=\r\n=rclR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-collection": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.25", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4-rc.25", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4-rc.25", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "bd54c81cfa7cc8954a3e19bc3e654a2a96cf6451", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-yPOIE/BdsD+DJ8jvmeWt+AslAtMMFteL6oKgthgeH+116cAoKq9EYbm9x0teFz0p/iaT6b3lUJsY1G3p28YxZg==", "signatures": [{"sig": "MEUCIQCAdxvtUSj2UpjjK5hRRuunZe7b+0GVpXSwFd+M1XJziQIgF7gOcKq4pmM0Yb2iKhln7HD1t50Fxah/agq560AkCVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPuA/+PnJXJ8MjQab2DxqDd9eHzTlJbvByuPMdH+6ypmNSwYEi5Ews\r\n1IZlzhs+H1QTwGoqcKQOa6tRkx+h19IfmYixqqvIeXXvz8ymDBfK28erWj0w\r\nevQqoMc9EYzi6oGUqowN+BmCxLLvAyg5iBkJGNAAELzag2b23sfHPbL2DDfS\r\ncTRlSJUtKrFnV8bSWveRFBDPLjWz4dSjAg8AfuxfCnOzjrSLKxtmPYnka31R\r\nqbGiXCWXyzX4RxOBoQHM7ZCtinNvRcVGqIfb5gOhftod1+1E9SfBZodZsKc4\r\nLKEnOif48fC18utHQOIhY2r+FCx9dDakiIPnstHPVDnlK8wQ7e2Pxz2qvFI5\r\n8Fkotv53ou9eq5O5iTd46/ka4yEL+8271zu2+BvD2R3KVQNYajqU1qrtQKbC\r\nwO5DrIvr4xhvQKdahhADn3m1xeBx1kZ0O26Eko2p+clVvo49WRWm1pOfrPLk\r\nt5T21vrmUsmSElOofMXMkGeZfVhAe6uzk3Ve4rFkIv9NBY5gREDLiEs0Anjd\r\n2eIlCXgyZj1P0ALb50lvwURp7FhOqFhNgRou6Df1EBwYzalnI8MlrtNHFxRA\r\nn+yHw2MyvbPDVGhKmIWem+mCujDFYaZG3ZcuF/7xvNO3cv/AhJKrUtL5ojSV\r\njjFSd1hgtSi4kWZqPNeSVA2VN9/GTqv0kSA=\r\n=wiP8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.1-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-collection": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.26", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4-rc.26", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4-rc.26", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d7d97822aa06002f97e8aa6936cce404e34b6755", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-pYW2iqF/J4iqiVZJpWfv6ZhLMsF4dhFt1FEzcTLK0xLQu0mSNS5P97j2DwPuXc11o+lCLBiJOZhVezRmtLAg8w==", "signatures": [{"sig": "MEYCIQCQd9rqq/SU5+kxE615i9JTJav1Kn8G1etw+dsrcqOmtwIhAKt0ZIHESEcCHIcXsM+l2Bn4FT7la9ILMlWPxQcwJUlD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169260, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwlBAAkR00Q556GY+lt7zeRiCafQ5oSbVCf3uWiuy/IuDCVlfE7Wpk\r\n7/KOuJIcTlxWWR/h0hFQSd/OVNdfJ/mM0oKt1HYFVwlkx3YM+iI7XnkJyJGL\r\nGwpkmrbpm9beYI+2VSsqkbH8viGRBzdte/3EiwdU2YGUhhoX3J1yMf3ZBdXZ\r\ns3AFmCApkuvuszixYz9yrMxmSMb3Um28J8+rpub5o0eKCB28oQv9NZXU/f74\r\nMJFq9YG+Lz8kKCIkL4zGL6RblUjS6kd56y9EelemyP30DL3rRUX5cyK5D/KA\r\nOqEKK4IWeIFvzmPXThB9YMHAaTORbTdoFUqcZIzj3jM3JgnsHlHlGiIUCCiO\r\n8vq/WjAHIeVsJV0gawsjHmgzjheXrN6xp5f/i3o+M906ChONcGQgDpr47JKZ\r\nJeVu+NOqjIKeo/G9JBB80rlH5UxAqtUOxDHRjE/yqy5OpWtFz9vWYpDZRdSl\r\nqiwHPuOs9xJM1e5NSq/9K4vEl7PUZXJoZ/nGpWJdrS33zW+twZrguhfAcPmm\r\noVeBBVvURM/re4qfYNJgQAyktA1i0Q08mbUBG9wl2yh5KD5whEpZLBs74qCe\r\nKuYaxNDlI993t6vj8dgm6oDuFL9uXVJA/A+ZxfE6oX1RRMEEDjnjy7du0jVG\r\nOKL+ODD6IaDHKjID0O/vQkYNMIGJuaP3D84=\r\n=xHaH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0d779ac4f3431e88451fdb26ce1b708a3ac26979", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-O/i/tMigEjT4MYbgFG+Jiibh3arU5leAGtsb5qctJOMv8DZBHNO95opXv4wd8ObUqB8LnHvXd4GsgLrQaOIfIw==", "signatures": [{"sig": "MEQCIFAug4FMZY2IT4swlg+rc+Ps6a9dhyRrYKWpSmiyKcAoAiA9i48UbsfcPMl0+NQsKdM5PF67MOxUaae2sksIF3dgBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeXhAAl+Gsf0JLLJHE3Tj3IeWq+Yx7UuWcIqJUb4yjNH+GYm8MG1UV\r\nfRF+NklxS/bM0IIYzC2RddJHS3wxTBAWQX054v4YWOEuCpJtap3TZxvnNtwH\r\n7yCq4AlE5fA14Ke2XYB/rxVnWQzo9HO+69YOs0pUqRWo+mxxMg5yvXb9gXcm\r\nH/Elc8m7Pl/WrQYevWOVh99XqtQYRIv+weH177D/3R1Sf9yqY7sudQ7AQ1g4\r\n1SVehRDOiXVwQpE8uwmuOpjDIX+PfCTkzvfjA1ZQtU349bcpC9NIfsDBRJdm\r\nQrhTgVV+sIH7LvFrgcsm9BVcsbyui79YJdrqCcrYd8krC9zBY7pormRg1NiH\r\n96mFfPIY1WXSelA7zPuwqtO1Rb/JouIIqmgGFZmCeteLZ/E0LV4OOky2ICHP\r\nMUfuwTEW1wNFFc/6hvzvfAf1D535c+syjMyJjQKf3+D3F0Ys1451umTXQHQO\r\ny3GWqYoXtNFvZjPlDBK20Fr+4L22ceY5wa9Jl9HYaiEV6G7DXtGugzulnTaU\r\nQrMuS9+ZsoT6NCmZOgdcVzGlmnaFUMQtWx8jNRNuVAvLHPZ94bJHBSaHlmh7\r\nmY/sMJIhRgqXA1W37cQIAHPC5pNvgakgsUNSxjQid2GVB1ZcbfqxuGzkT1RW\r\no+riedHKYBUJdo9CaZdOefPWoSmt/EVtF5Y=\r\n=B3Nb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.4", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0a1de9084dad02f7a91bb80bd06fb74db8d6616d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-PrDy48X/OjKeD/SH9ZrUvij/d86zQ/WJgUHlOMtNyM9z9FmMAwYIMEBgWidaVGSemTC9XccsBmNXuEOpbVJz7A==", "signatures": [{"sig": "MEQCIFFG1X13ILaFK8DBdSLGHDUQXV6aQiZcq/5+xk3E1q8QAiB3eLjRjbtc4SmBv6fJVHv7nzz4xF0nzFYhy8tHwPIdbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF83hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUehAAjbaYND8E+NOFLl+JS1zmfHa5B083E7Js5zxWi/pCrjkktxA2\r\nLz4r0CzBfaGMsb/rze+rUJu6G71Bm9aLkUlxPd3IDkKB5ebIeVw0nI5V4F0b\r\ny0UlM9yGoXI252Y6tOb1Df4k2I439CBYxJTNpIVEBsPoAQDT6dcvGLYO1FS4\r\nnL52wgXOQa5rrT54tz8r+XXyRbO3jZL3fOoNm8SHhCPdZ/TmACtjEaxOwsK9\r\nOMu2x0mSW/z8VKfk6/E9JgVJ6/+NUi5+MU4WOjRH+7SMY9pgTLB2TmMcUEQ+\r\ndg9gdtiT+oOooOgRd3QylsBtJGawEjMKk/lA1gTRhVugttQpFywq5ecEPoxx\r\nhwwEN2kP3Ppe1nvxwMTKT9oPXHcv8JC/XcvT43ryrN/Ln4tzXJIYN7qRV0NF\r\nMUkoL6j34+DAIDtx3KOlmy2f/7TwYa6NYSlm/EAbcpicdUHJQyOg8N+JHn3T\r\nVHvN6TdRbAfjyZ2y/PCczZ9BCrBlyt5rpmZKrNiv6Pe0ekLhtv//UvfrofFk\r\n7+jymoJli3Hr2J8Qrf6HGLcNvWLrkvSCI3wFlv8AJ2H9bOoHffR/lR2oSGnU\r\nyDVmefd9NZ+5coIErD0BVsJIBrqcWZbDK7EZJ8koLELidb/gL8lieXPco4+b\r\npl+oEJV8SY4JOrKyArCJ/ORc60O48lxWZ6k=\r\n=7PoK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.5-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c4605b131b3d6aff6b9493234f5a56f3876b65b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-CsJFnCs9f3G1M6NhX8/2yN2Sgg+HxAj1/DymdOmvzJuPZGDcMmLAyCIO6fNXLOvKtSxXoCEC1F0GSKLGGCp6QA==", "signatures": [{"sig": "MEQCIElNjLHrjtfh/ZnmZUtg0JJhui8JbrETe6f6OrJ/YP7GAiBkcHbyWeeSh4l6JjUfZQwMeuF+/+/w55mUWP8Dk+b/bA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHOthACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWDw//dO3xsksu+6ASPzP6Wf2MbljWnTynOyOaD9GiDWjQsBTF1jCy\r\nOx0NwST2uDAWKATkwHv8GNcoBcBlwiLmHuGnL8n0GRIpPtpkT8+ZLtJUnUwb\r\nHlECaAX5J82DXBSeN9xw4eC1SrHV8OTcvj+RoOLr7xgf1ZioT1wcJmHAthwS\r\nfa1ed4nHbwtzbMBDXOM7rX0fM8ly/AxoJEd1Ww3Xd0px4T3ccct6LfjOwBQk\r\ny+HCyjUNglPbj1+P45wcKNgQZqwVSnocjjopfgnearwMEcIrCwHaOQAoWJQv\r\ndptInVd2cH9JZ47KwGwCIOK0d1dIRwvdoAsGui3kCqkzu73EiIrIT/AqFfah\r\nVaw0DYkv0CIpyAPueRiVDh8ccZn0oBnKDG74y3e4rcJQWosYCbmcuuEHI+/0\r\nbXofa7BQEiX/FDyrlEXKhg6bHHLOeLFRoU/WJ43suCTpygRuuMdEm+3g3awv\r\nuUtf/XHzDzRRR7iMqEaIay6NK/mJDpirjmp5UpeQbSTYftp1Y36xcWTC1oFe\r\nxJsVyQuv8RZHoXrxjG8NzNBB9GwSIT+i/QXSkG8is0Eujf6n/Orw2yh9/K9f\r\nw44PK8hwRlU+NNuq9APqDcD0H5M29mwHYOXWA2ofjh3EXgytKkHHEeWNmX8W\r\nqzZ8zlsASiCL4rs0M9z6sYvhmNpdGC18nes=\r\n=ZS42\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.5", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b473607e1ae62368600e1439778c2411048d4a8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-b9+2ambunxMNw4pgOt9xqzVN/A5NPajpiKvR45zI/BS/uOWfV1A3N5Kn+OIbRMln9GyBnuoxXV0tUBLC2rAzpw==", "signatures": [{"sig": "MEUCIQCDNrXx8JH2SjUKiQbMRqTYeJ7DtMcBpROtN0/hM0lLrgIgW2ts3u6Qlw1Ebi+pXnP4YWugW67pQxo5EQ1+6dhJIPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHOwiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeGxAAoqdiVAm2CnG8lufDkdGGaztKiw/M3eG73p2mEwPx1QBW1Hau\r\nvct2a+Pnn722hfskR4CO5JJW0zF4CuqDK8H6Su9+5sHD8R6HId5dj69Nrg3s\r\nwOQf7Arcps8WWBrzsAC3Pt7e7qjVUCEEVl8cSDXqg0LOGUJrDvy3E2XaeI72\r\n8nXDC5eiAK0ZZzR5SxTPeu9el5Zr4+UqsBtbaRnkCejJkb3BpreUgY39Gu3P\r\nAj26G61DJxd12G87ZtvBbFM2Mt34hlVCWIHgA8NNoFRKTi1nGHIoNEU7Bk0P\r\n69OHhv/oMynzciP3bWanZN32rYqNKKUvOMsGnFK8IsidlKdfgKgI0wz7ly3j\r\n3SMbPIs5KFTUKXANqsCEjlJlmY66HsNaKYh9WExX/Vv/IW38lHgzpiCGLvf0\r\nnIvFNDE2FUUg/2XQKnRJtZLrExWLSKOVBzwXpjNnWjSED9AyG+KCVETlb8Fn\r\nkv5Ct0aOEj4ddb5PqzQjBW1unajqO+O4X3pYg1LqYHhAW6xwdLsCu1m1/Pnt\r\nqE9WQnSZto4IkDgh67fMw/Lt10GPTL3xZZ3hXAe4H5sn9gClnqSMiyFEsjn7\r\nBW7jFOKamG8fLkk6/SF5QEqFFAKJ1hIuHG0u79RJXFuQ942FQUn9TwBaLS5H\r\nUZmeN/QG16JZ2nuC7ZxTvGMt8kBxmumKTOw=\r\n=NsF0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.5", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7751366db163473d1d4855b6dc1fdbd525875e22", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PMI6/uh57B/jCGxBpgnLo5Zj9+LhYx6g5BuJoSbK0QXoFBmHR030vefCoi0xtZRJzrCl0m+i1gOz6sO+wxI2sw==", "signatures": [{"sig": "MEUCIBAVb0JnftgIzazrMGkMMPznk8yXUWMHTkcD5yk8x2urAiEA28TG1BoPwRSLcRtgXzdPLCmU0dExcUluhLPfb4YUOKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD62ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpacQ//RXOxD2qTv7NbxjXEQtCDTRWe1L1CoYZdrskz0yUmncU6vd1T\r\n42FbCbqqhP5OyY+v+6rFKuqjvtdeHxEDmrAF+KHYZ54FwPfxGtmnDVepO/A/\r\nV4F0jWZp9llZen6/+N++8okYGZcab4vx1LEyZluGAa4NcAiUiOc0GZm3MoAu\r\n4hKdybGGGyWIXTpeRBXplFxrqc7M5wbZ2b9jzRkCQ6Hzzgq4aXz9b1NZojnb\r\n4SuzGAWKp+fQmwWdta9KnkzS+jTQX/6DqUuDus1MhFzbY10CSWiE1QUyCB88\r\nLgJ0BjABX9nDEYqIP4VTTTKeIrpIEwhNh/nvBx0XkC5OEdJZvkyRuiiSeP9+\r\njULiuyQJgXnlSYMe/m/zJ//NwpUbzWWksFyvp63PoiO13tKrufJ3RmkWR8Cu\r\nJ/YDrZUckhYXuKWeQ7Patsyk1qMcU68ngihByXhHfnNmPChLKyz1mJLObNNd\r\nco1A9OuZbzh/weYBhvOCX0bdD413D6kfJJv2AF9miqkv9CWPFan31Uu7MLap\r\n73HsZEXysLZ/RqsK1Yq1Wm7bdWI2Fq8BQVgiwhKaXFcg3W5PJP9jrWvpRsLQ\r\n7JgoVerICJkYevU6mSYbLVX6CWBksTfH+Ug/mkqlXLz+pg6fnLrc82IyVz8u\r\nJlSrQpAyCTTBUGpTZTOiqt8I8M0dP95aQnM=\r\n=CN/+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.5", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b98431560d758bdcab4d89c0e0cc6cf5ba2539a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-qOWOdTv2kkmVYriuaKKuksBTpmZFxYgIYNQQqVzUodt6tPUOHAtjxZbjZsPSc/DfxpKdtHDnk+ZMKYxFqwEvMA==", "signatures": [{"sig": "MEUCIAfPuPCC5c+JeuOo+BtRBBj3lkIGaDvKEicsiu20VycqAiEA+XOXqsWXLlB4YnOfRa+qDWOazEnNseaPrPWGA5QlSNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEH9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqedxAAkdj31mA/6YjNXgD+LyjOb4ZFBNtQggYQ65qhx8ODXFQaZNtb\r\nNE5xsosfCuriIdUsIQ8tRhlH8UAt03XWa1eW6PZhdG/8BSapRoCX7jnFmj0T\r\nriJfzkGJ/6EKYvRZJzH4mapGtOHwEmdwuk4Hy97wEU+zcxuJ14GHvepoCZT6\r\nSsO9Nk4J4Q5hf75KHMBEOp0IrKzZnt6N7I0AnhH1yAJUAgVnkpmcj8d9lTLl\r\ne+SI4mz1cqOm4DnIkvWnyPg6tmAJObT9DXi8qcZK+qJ8JaQsz6lEM1CJgnqv\r\n/P3b4o9rbdoKypwbWURMdbj6R8YFingzcuCWtQYw+UpeO0BZspYmqcMWSxg9\r\nPAzKyK4jq+y6A5VIResZ/Xs64fV02Bg6dDu9jgGsCLh1a45EOE2Rsihyd1pc\r\n67fgRh9GSE57lNFC8yrgEfDoYSsv2FlWnpjPxrlpi40gxIRLq6K/648el/aC\r\nhR+BXRGS/nyv8daXEKkSWrfWCMIPvLuirF5VRPie6zx8WKzGSGQElu+YOPDo\r\nwfq+TEmP6UwJ56XcsxrviIGYKoM/W5og0kIVt/dzMl/Yhafef9XhJCxuXG6x\r\nGCQXdR/pFcZgBxNeK5yJXFiqD42BxdV0emkhgbh0bkN9gvyBjy4jhFp7KwON\r\nWb2MT247qDFpmsOrOFS5puW8ksEpLDZk6f0=\r\n=7sRg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.3", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-visually-hidden": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-dismissable-layer": "0.1.5", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "57a8c89b3754576f92b6797c1d9dd32a2aa961b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-EsNcRBPUEkVKw2d82zGP0VMe3Z5eDuoYb0hCUDgYoolAgSSdvBwKUR1Kfx5OjZCKDWRFwcsvIPjoHptdmOXtxg==", "signatures": [{"sig": "MEQCIAVzkOrv832tH9YdPmC8ZE/dzhIWWIKyc+SqSMSpeKEiAiBkkZHSKwSEVT496aowG1K772wVMJDf3Jjakj6wTzHeQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169551, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUTcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu9g//Za5AWc1oVOCCI7y6FuTaew14D2id7a+TuPsHBQ5LtOsm/+Wn\r\nj8zwLUkKBG/RayemRPzS7Aw8oayzI6VlV4mOz8QP5mnBxkJoK3Bxpdw2nOb1\r\nDynLO1VHhznXm1hKHwsbIflbuSvES89DHX7QHvEbM+SxRdU5FPqX1X8b318N\r\nEIU73VsjN0cMdFFHsUluguShvzbujjYKxHapo7I+u9U25nTxoZA67qg5HdwJ\r\nwHV/iOkaEXqwwm1FU4Swdb+DXZvoLW505fHX3v1YMV+AnRtPq16LdBfxfu0a\r\ntnozlg8Qi0Q6D6pvq2aJKOeq7G6ghEyBUTwk0UV2353anY0h4dY+IqKqMxJo\r\nWuybo+mGTxYyZ8DliPTAWIv+hK7B9azsQD/wCjjX+aT742+l/MVGUDVSYaVv\r\ndEoNDKFIpDKnOkd4Z0FLKsAGPgVaxhojKIDlYMCjYTSdYQsulMWADQflG3jU\r\nQ5Mu79bb+/io9mXHih5ZOoYcIU82G3FYWmsoLl3HMe+76MdLh47zLkOVU8y6\r\nUQzdwSn83SeF63m0zEUtzUOtcc7zdJtljhOTH1QjJww3Lo9BA+TWiRXeyf09\r\nCzUmXQodk7097DC/UWYSEceooyK1eFfDYbLk68GgkH6iOpzSxBFSePE0rJmK\r\nkkMy/R46GVQiuk2vORXssg0fRvps4PcBYro=\r\n=RotL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-presence": "0.1.3-rc.1", "@radix-ui/react-direction": "0.1.0-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-collection": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-previous": "0.1.2-rc.1", "@radix-ui/react-visually-hidden": "0.1.5-rc.1", "@radix-ui/react-use-callback-ref": "0.1.1-rc.1", "@radix-ui/react-dismissable-layer": "0.1.6-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d88390256ad877dce63663cf86b046f9dc763053", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-P7BnY0SdWGWfjNvgdfpEQIS6l4juIyrCloqnvUgUO9Al9/9X0KDcgaYhRhYa34I31QRbvq4rxIhoYrv1PVY6eQ==", "signatures": [{"sig": "MEUCIAjsOmes7o631zUfUtYLbaAQHvuoW+StiGuY12gNgRPGAiEArNNnmIlfgM94j3sgW6OcesU+6f9BtBWrIJeysWeKjB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAQiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJkg//QSJHuBH/kEO1CXoyS4b/HsJiid1GJHAlTesOreayM2xZ/ub8\r\nr/tmbj3PS7B0x6qit+l7L/Fm3ct8zwKcOy7IeMV/3zuFuI411yGafq6dUTHb\r\n6zWqPg5tObgh8f7Sko3oapNEfJWcalh7Gg8WtWR+2M1APHLsk5SZDl9Las8A\r\nAVEUcU0mpVqWly1pKzdKdHpsgVp0yj5pbmJ/pFpUZ5fYxxSalVHWLsNhpE8I\r\naiNAvvdcFMVCqTQ1CtJKK6iAolL2re4bJcmKlHgNyuFEdWY3+nldtt4F+7c9\r\nYvLqDC4B873F0L0njoDAo64q8wZ5Byrw3cMl1V7W32hk3dYuLzYGQ9XHYjNv\r\n7GY8pA2UCLW2vv4q+fcz9J+gjvIPll6Vb7yVCvkr6TqVW+m2EZW6R+IIqxAT\r\neW6JyJLxE+kMbA0blvw2/VM686h5UTIbOilrQ+ZAJpcU5m9y5xj47J6RwmR4\r\nVwlM6vG9+VKxcnp6jRig22hm+Fiz0U0nYCM3dyI01qkTWrQl4O/PGiGiNdc8\r\nnOsOm2PFJ3To6QGlJ1Y7JhvVLdsgGRIOLOSBwAHLjE8ObkANhAXOmRnREbFc\r\nR1lVpLGijRoCmwFvViTj6FSje2qIYY0W5Q2gXMoAj5iIzehn+xfeQ+y3f36L\r\nj8SFQJoIRrALZTpqWmwW0ZvceNyTRIvmsBc=\r\n=V2B8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-presence": "0.1.3-rc.2", "@radix-ui/react-direction": "0.1.0-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-collection": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-previous": "0.1.2-rc.2", "@radix-ui/react-visually-hidden": "0.1.5-rc.2", "@radix-ui/react-use-callback-ref": "0.1.1-rc.2", "@radix-ui/react-dismissable-layer": "0.1.6-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "587831a1164788f8b3f8591497a19937446d1867", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-IPSnI/0Gr2GEQTHaqT5FG2kalQmEk3uuvlzZBtEf/PrtjPPe8ux01kemEorWYg8+pZtfkYs3xy2EGQEIh3rPwA==", "signatures": [{"sig": "MEYCIQDjZdSbzg2TMSxdK0CUqL2hnmbpj8lQ4lBinJdl7XMlDQIhAPk1NKTJ3tIQUI/svmN4VgT5+nEaWBHXEh5SsnfT8DQF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Mg//YnrYormqaE1BuFGfeIpyrkGVXXF4CJKJ/y4dQBTpPkj2wfAb\r\nLtm9dXmv5GKNoB5hFaRKBhPoOjIcG6L1Hzto+L26FmWTLIvI33BCxC8BY7Ao\r\nLV/5ob0WNMOq0M2R0eDKUmwfl4PKk/Rsi1IDeBOnGIsoPdYzpXsEI0psf/zy\r\n40Rb6NIu2yV7/uTw7bgJ/ny+W7YkrSdLpA2+Dxl4160us7FEMzW0mlyhHHZs\r\nKZJwwRMxXVGdRTaNlNtGxZs9oPbRCaSrHY2JHCreAb4eIWbC0AUpbjuYoo+m\r\nuEdhXW3dnoTzJT0UpIS/cZ3ksChUAxAD8E4o3mNjxDHAAMzZmFzwSFgknUP6\r\no1vA9NnGuexMepH3XGrE7fVDQJNoOCwYyTQb5PwBDcQXUESDzKKzAIv7qoBi\r\nE5d4B9KqRZGGheUlJj+XjvhcZa5QbSNYhodeP0jNgRWv3iH6Q4Ls/7AdfmbL\r\nHxJyeoZHACup/KlUWTk9rxY7vtIsnUh8M1iuCvcGIZ7K7EhkV0uowS1vXnSV\r\nL+H7OEoj3sUiDpS8+e1baaw5vFAiBYE5gyXwS8NJrSWNIDNLtH+iqP/+DGRS\r\nEVhcu3Jn9QtZhyGmUrCZho06hzrKVQTThwtI5414irPJOd9VlJRUKFEOgJhS\r\n0IEZ6vgP8gwP5OOctV3+3P1ZIZY8WTl84WQ=\r\n=QN1P\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-presence": "0.1.3-rc.3", "@radix-ui/react-direction": "0.1.0-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-collection": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-previous": "0.1.2-rc.3", "@radix-ui/react-visually-hidden": "0.1.5-rc.3", "@radix-ui/react-use-callback-ref": "0.1.1-rc.3", "@radix-ui/react-dismissable-layer": "0.1.6-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bdaf8b9ba54987687aea5fb82cb5e6ffe64b768d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-hO2WaW9p9Nig9rLPMI1BcV/R1QTfXXI/Cwr31J5NN1QXE/K4BjS+DXyLYfKZq7WQ7cJTGbetzTb6zE1v63bZWA==", "signatures": [{"sig": "MEUCIClyMHuJstDkHvqxCPOQuMjIMQ3Kc06HT/xZAvWEnRX+AiEA5TK5/5mrdl0AksBNxbhbxPU9s//8RJsTJyx32O2DIaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgsQ/9EILQEh59IFd0Xt/kkWKwrtf82LSj71svvh7da/x9t1U1n7hl\r\nW8Fn0DMrNt35wiqHRQr3IDwKCqwwDCg01iC5JNYXhTKTMyb4o+B+j4A+s40s\r\nDUVEp8iI5wUrxA4egBx8+IT+ygLwnr01FpbHSYLU+3/dTLPCxhKespkNzYev\r\nK9PAsEQ8Vr/nsDvAGTPlwAjHqB56o5bGggbzP02kVZFGYRLjUXRTHo/TwWEt\r\nrkQToupEcrse3SkDduLQBfUnRdXIGNyIm3MIldGfvl51OOoedvRDIIJ4p5CN\r\nw4HbRwjDN/+w+Xo2rhr0bt77UWA9q8HPynSyin5h8DDe5k30vZnJBt3JCcQc\r\nuD2/nbpVaQwrIvXV7JkfyVvWLFf8SEXfA0iG6TbUxHrZ2DCvBxXHvBIkHrqX\r\nNKGIMbxHELFMHxxEgRxpv8y+SpGymMxC/BpWm36fOW+AtgKlCtF5ECkEqclc\r\nMhiWoLOSNzsRweWMZivVotyvrVUKmupZvFvIxCx4IyisQor4ngWoaD1PnG3i\r\nmeM9fdbo+zjdQDLnwSXoOdc3Q3Xv6B7Jsg42g2DYyoKpmJ1VHQWG3cCt+vDx\r\nmBbJTGaf5DzOdcYCT92dlCtJ0OFD1TtoB4CZ1iJaVyqWcMPD8vtfQ6qTCoo8\r\n0+X/2JJGzibDQ9pN4+mmNz97AlcbMpgnlXM=\r\n=Z7n3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-presence": "0.1.3-rc.4", "@radix-ui/react-direction": "0.1.0-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-collection": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-previous": "0.1.2-rc.4", "@radix-ui/react-visually-hidden": "0.1.5-rc.4", "@radix-ui/react-use-callback-ref": "0.1.1-rc.4", "@radix-ui/react-dismissable-layer": "0.1.6-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3f229d550166b0a7f01a8afe9f63aef0fce3ebf4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-h8Syp35X/wJ2ypPAvVSLgYT8hEqRIsjbzlhHdSSE4M7jhyRyTV+vT9vIwwtiBKW4x5NGbMnPBpWd1VmcvdQU4Q==", "signatures": [{"sig": "MEUCIQDt9NbEeSnBEHGKAgVLHX2Vc8/MgLKupHjuvyCiez8z4QIgOMU5nAxs9Q6wdZ6FNGfpads6mkQIHXoAAPc7VuklRgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 269316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3Dw/8CiTly0HOJsLcw0uBOhQ8jlN0lxIRkcZ8oqL+jGC8H/YLJbqd\r\nvKFqg/wEPZb7hAMhXvFM0txuelXMGvoxjJnIgLo43MXNyMt5NXkv6Q/ax21D\r\nLKQz4sqea46b8jOyl3DWGYj3vUFL1LnuyxPfh4lnbiT+UPdg7betjwAedXqO\r\n3RmN7VTyzWWYMzsmIJt1gO5EeoT9v21rpKM24t0BWZ1W55PxfUPsrH0ErlZe\r\nYZZrg/4Xv3qfh76cW5aOLyqTseoIdqi455ivGj3okrtgUpyiCVpmqqq6NusF\r\nWX1bQogBZkLiWzh4CG/E6OAZ++0gupKbqtxQ6rsoHdY5X/yQ343dqhP1fvj+\r\nz+Swoe09PaaOTnIUWRrpRk7IfNI6gl2A6sV/3r1OSZedQKPIlTTx64JxkTCo\r\nLQvtxkTtA0V9cI39lafreh8Js3Usv3+fWUUX5uOdZDuw44DfsbC+95Z6s72I\r\ntyToaVi/7dRejaibHpIMexJFHILu01BySU+d6rEqGZWE7gUuH8A8A1NNQ3pa\r\ntXyPFjTDAV/6Zi9owpUjRy4TNffO9HGok/RDWWfUqn4K9CDScrm/DckompU6\r\nu3nNgB3BktrQKseRuXsDXFvuLYh28vJ8QOjlapY7BcQHGb+JoW1lE1lAH581\r\nDGyb3oQxWhpDXGDcWCGChwTuCyuPfw05MKc=\r\n=/Vu2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-presence": "0.1.3-rc.5", "@radix-ui/react-direction": "0.1.0-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-collection": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-previous": "0.1.2-rc.5", "@radix-ui/react-visually-hidden": "0.1.5-rc.5", "@radix-ui/react-use-callback-ref": "0.1.1-rc.5", "@radix-ui/react-dismissable-layer": "0.1.6-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4d9344af77e4e57e6c72810d90e76a7e6ed1294d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-GdATDuB4zaterRk5E7rDfux0Kcf2IqVymrZmrYafQc5vDHVvY2a4IstL3EZ4Xtk9sJCb2GhFgQIdDc7Wzpj92g==", "signatures": [{"sig": "MEUCIF0xvLX+l3+GCfMHFNHNKXhNLMUYM46Hltzj9/YqbW1mAiEA5bAh74EyNa2WtQr2A60Frg7/ghQhbsJkCm+pnCmqGdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzLQ/9GolC1u+QRC99vNrM12IVAI3rbg+z5Ot+jkfahTVMxrFd2xtV\r\nkg3ivC9BSZFsNTgrmsMyAziZgfIJdp/IXSlUaCgn+0Khx0K5XEFSKtF8j7R0\r\nofOJxtuG6YcSfPw9cuUCXgVd6UhEfvHUfaoPGYeAXweCmeYIQZpzaRT0Q9lh\r\naSqfTmUoP0rB8Wp7eM0LugdtBycKNadoyToNhnJoNTjnZzbFpiIzLeE8yX0w\r\nPeV7k1E3ASfGBWrXoOG94Vs0mEuJJYWTQHzSinGbQeEMOO2KJMcmZu/5VTFl\r\nQHXkm+ci1Y+iY/TQy/VArWjvrxYrCRT3WJPgy4emhFe8Sqntf8SB1Vtx3hUF\r\ngmJo6c20u44AHxvcv0pV9UNcP8rlsLszcHeTp5GBGGH8K0JoPzI2RS/lFD0S\r\nrSvoOY7KcKoq+H/Xxs3jPz0qj4gAMQliXQ/kIvOl8QDRmR/4SZ4U75TCAw30\r\noTAnetNlnONL9Y29S9kAA+LMJ8411Sf64eQZtYjxsqxUFS0LH+HAN5VEIwQb\r\nRThbPTKY3tYByfvd7UIX9phIAphhlz7+j5Kjhtow2IkVyK6S+eDc3Im8+qT2\r\nw9DbY4vMD5pbaUAmzeP4HKakGha2AExAkgnMyPOoA9Ur0PNi2sQMjaxZ1jEL\r\n+CwKMkW8hSFzQXOvMFaHkFJjZbSadVRFZl8=\r\n=HKBe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-presence": "0.1.3-rc.6", "@radix-ui/react-direction": "0.1.0-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-collection": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-previous": "0.1.2-rc.6", "@radix-ui/react-visually-hidden": "0.1.5-rc.6", "@radix-ui/react-use-callback-ref": "0.1.1-rc.6", "@radix-ui/react-dismissable-layer": "0.1.6-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d499d3b169fa40f6440c3283bb8177e6aea5f82e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-yWDocp8WkzDdwm2LZvDfiOs3TmoUz0U9fpCKC3Ou6pi03EiJHeDqAawdQrGKDnEuZEkAeRXtJNGaaPoMA1/xYg==", "signatures": [{"sig": "MEYCIQD4zEmpcubycqxFO+uhU7t4UW2VFGhhdi4PxYyfDn/5/QIhANyBOzrlXULIjzUgWPFA3cq36xKW2SxjEEwz/5+R+pHO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8x2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry9A/8Cz0eV21Iw+k06PBEw/eD8MEYCIXusclJqYYB+yGewUSn6abR\r\np4d/lhrH1ZRurMDCtJomCkAJAESXYhNruiDTpgvZlWjM+Mi3zjMxfwV+F6HN\r\n1gzdIt2Zax5fSxB3tlRfBi5rrlQUBLU1LqOT6UQ2y/yqTWmTDFqoN5Q2ZndB\r\ndQosHr2C2mb6bymvftQhmvCT4W5kYvL23GYBk5vUnKZOOtcXvh1GireQvrvH\r\nhYIk+6ZElzSorkt7PFjmBF+kHJV+4o6g2njMhKLER4Etk4rORibG3VrgbQGi\r\n/rEl/Rf4Uzt6n/SLrH996BBK2YOAe3L0cDsynQ1mN8PAG4r2Z30iS8KTY0Sv\r\nC0b6IEQiXSR/WUaU7SLS7iqhKy4q+Iz2eNvQ007EyWHei70a02P6ysDuF6kg\r\ndRPwblCkp6JZHLmSeqP4Vc+nMZmmudeJb0LDzeqCJwLeeUpgySItAG2SRawT\r\nirAkWVADfJZgAMOVHfb51YcevnXVPxhshhItalD4TY1Odhvpxm44NRzFGVjO\r\nS9zA6ChTqx2CNlwGEEiGR94Xmdobc7RMZDnql1peYoh3efGmYAZSbhMRJYrZ\r\n9bayBBvJz7/dc8DysAvV3AVq7y5XF6i72sW438XN91poZF7l799fAzeZR0oK\r\nQvAg7IFGxGFkeMQvEV/2z5CSKBlTleFvASQ=\r\n=143R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-presence": "0.1.3-rc.7", "@radix-ui/react-direction": "0.1.0-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-collection": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-previous": "0.1.2-rc.7", "@radix-ui/react-visually-hidden": "0.1.5-rc.7", "@radix-ui/react-use-callback-ref": "0.1.1-rc.7", "@radix-ui/react-dismissable-layer": "0.1.6-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4aec30b794496c582d86827470e7d763c59544d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.10.tgz", "fileCount": 8, "integrity": "sha512-WFwElk9JOVT6w3S5i481aNR/avv9wCauxVjpJZYqOXXsGlXuW9NlGW86aPmiXwHoF3A5smim3It2/edR8A0cEg==", "signatures": [{"sig": "MEUCIGU2iAWcQ6lpvO5bZMBlibttNEjtoemPsZvRUyKDbx3eAiEAiK/g68KtnJQOKq6jJQRkA6JrTYLbmQlQuaAu8uBxODY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5HA/6AulhToyZqDWhBXi+Ufqf08sjBplCYhl1gzT88wEHzl2ubJDE\r\nzmz97UcfdBE0vJDib0Jaw6nw+mdTZDUF5FpmjMcvBjaEEUDmW9paqCtpwUts\r\ngP2O8sxjKMt7WNS7VGLz3NT4Zgklyv8p+x8yFrmTvUSHQ5Z1O0dKvBYyJNkh\r\niD6FOb08iUnUURzS7uzcQXmxmyAvW3T1VlXqYMdPfULnu76ztGjCTTA4Yyg8\r\n4/UQY+k7QLJLUvsktXv2N8alNfZnBXBs+yUUMeJKjx8HX04Lo4Nxu+mTpJju\r\n9WA5IdWHWZRVyc95LapM8V/cWa9i1k+vohnTTIWJecTfNqf6CdmedrTlQcxo\r\nqcmRJlA7/jxEBjvVcwtqqL0lSKPArrMyRmUxyaunoO8eg72jfxWtdgXHMZ4p\r\nT6UvrbJmh3+Hags9gW4pZrzDDBbeR7qkiz2amXbwzHxKDLLfF1YygCVMNRYO\r\n7p48ttmpab4L7X9LQWFC94KxsN7m9Acyg6poGW4ExsW5MVrkusEqUhuMbs/S\r\nGIwUAaPUHM6/T5GutL4hPyVYE8gN1tjX5rNny2ZwqFg/OD9rSkuzz0+7c/e0\r\nN/J56KfdnHdUjP74fjv50NQ95jnYQteuZ50xhlQNUXp+SUYEIBgk1VFLdj3P\r\nqQtzopKmgR6JoAxG+CbZ8A2yMHDA76vNpVY=\r\n=lj7O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.11": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-presence": "0.1.3-rc.8", "@radix-ui/react-direction": "0.1.0-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-collection": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-previous": "0.1.2-rc.8", "@radix-ui/react-visually-hidden": "0.1.5-rc.8", "@radix-ui/react-use-callback-ref": "0.1.1-rc.8", "@radix-ui/react-dismissable-layer": "0.1.6-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7afa3ba5d3a2ae20a313803147fde44ef8eaa9d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.11.tgz", "fileCount": 8, "integrity": "sha512-k9p4JIo2Z1qh4Ov3PQffTkLG6bWXEcO6uvW9h6Faqi7G6xXmnXV9ZnuXhDPLmb62tI6gmM2ifFT6XUjEwbx6oQ==", "signatures": [{"sig": "MEYCIQCvos63Qg+MdCeNNK4P7hhryEAw0AWQpuuiuIYLFW13XAIhAJl1UaI9sV45oHK6te35l8bxB15o0osFJqapGDouOEFE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgrRAAmyc0/AfWEg6CKTZOc9glbkRs8WNwR/BNsDtOSgQ4t+HW83cm\r\nCkQUtJLoSpVQP6Z4x3dTcpPAlh6G94LVbDeN3M2RlQzWlZGsFlUXKH5KlHQ9\r\n3/NK2xGSH4KKiSUITiH0FeYqk1GYTxvIc2IfdlxqhvGDpD8U/7oBv/rgIycz\r\nh3h1MOqcCW095GJrGsMdFAa/CdKihpcNzuu2kgzQCbAqqbGArTIzbewei259\r\n4St+W76OOPRTp07LOXF8J68/E5nnapqXkTb+Cd7wA6U+xJSsQJmyfWyZgTIX\r\n1b7WaRwS8J7b03/VFDSgINMg70i91LZu5aqJdk6WmoC51MvHFaB7NFyaXHaT\r\nI2Tl/ksV/zbf7CTyu0dq5TRVSk2v6nX2SYg1KLOY3GxXhd85hXnZoccffSXG\r\nGNNd/ZZNUjY3m5EFwfHzTFK1e0QaAVy85n8Nl0xM7WKU3FbLqFDED3IL9EHV\r\nNSyDF9ctUy304vBrXoDAf675DgOP3uL0S8P0NQ9lF/wmBIxolWyJYXglD4/5\r\n0XT5cNHn0K2htsriX4IG4iZuGzfvGHWwE3WfCkU3ACAQLxhNK/S3WohDAaeN\r\nbSFpjVBhP7EQ0nc2gCbK6s1l3Yy0gdroVD6/KcL+SclbsSwChSl1kXbCjfQr\r\nWFGQXOfh4zJQWLymm1onPWIj8RRdgbqtQvI=\r\n=c+jJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.12": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-presence": "0.1.3-rc.9", "@radix-ui/react-direction": "0.1.0-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-collection": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-previous": "0.1.2-rc.9", "@radix-ui/react-visually-hidden": "0.1.5-rc.9", "@radix-ui/react-use-callback-ref": "0.1.1-rc.9", "@radix-ui/react-dismissable-layer": "0.1.6-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7144a0ff3c2a2527593b36b14e2c7d52ce23fa3c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.12.tgz", "fileCount": 8, "integrity": "sha512-stvXTL+to2hFyQ0ksd76MGiJuBm06Ah8VuBNDcEQMywh9PI38fn7nn5pzuZ1D8tUk+evw6A+YCAFxuFWVpVb6Q==", "signatures": [{"sig": "MEYCIQCtS3gGnx+mCxri61ARW6PsJmbY0qaWlXGxntJ6GFGubAIhAMP4A2zQbup3i6PmhlHD92CiOTwXVkv31DyXU8JA263a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOCxAAgaeDrsjNbQkhSW/XfGMH0NE449y0PCV+2Uu4Ni5IwLjjmBhO\r\njCC/O8MLqg0axP6G+ZavfVZ/q2xPSjzbdpR1kOGgp+MhzXNPssrRCGbdbzuh\r\nCRp9xIYMm0gNuH6zCoZNkbe+Ykr8Ng8g17PzExHb4tsS7VPgmFUwWCIg4Nzg\r\nrgAy6NW3IcWUmmubDR1UOgkp8+RI3GkTBFeTz0rIdkGh5Q+q5spxvPW/W7G1\r\n1ZUpqSBP2B0yhUe9zeNElwTjgsCACd5iWiFtWg7uPdWK3JgOIgQ6DUaqSNh8\r\nDj+RpMFXKJP9qT+DvPHe2dpf/6+GFza3SbJoeypK3RufBzSgKHo3D2zJ6lhi\r\n9U+mKD8WStmBESRoqgIfBID+4m8VDdGdvj18VyXKJpX4kKwU1v4U81gWUJss\r\na2ar+7vfGTSJf2F0h01Obu6VZ5CKk0JbGh4z9uv5a6PgKWDLYivcCOYx5L0I\r\n4MGKIK+JCepgY2o9B1gKDoensqm+UYdxgSouWdZRgXkFSFxKLF5/CzKBGQl5\r\nW2nIQd4MV4YfRJ+NL2RmDPX6rMZsOFk/yzjZVcErZcVAePCOX2y6hGgGCL/9\r\nFW4uDpe6C6Ge0tJzRcalL4ec1pk4VB5tr29Kq1tpPAx4oFU0reCO4JHVPP4u\r\nHvTvL+i/QiZmmzu2vvWQ1vXZLUyNvIsRGi0=\r\n=GWM6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.13": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-presence": "0.1.3-rc.10", "@radix-ui/react-direction": "0.1.0-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-collection": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-previous": "0.1.2-rc.10", "@radix-ui/react-visually-hidden": "0.1.5-rc.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.10", "@radix-ui/react-dismissable-layer": "0.1.6-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e2898af93f61d8f1e21efef5d17c3630cb3eba46", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.13.tgz", "fileCount": 8, "integrity": "sha512-kELNqR57hmPJaSQ28me5MgkUE12Z+YX8HxPhyPxK0nfzaUIHHtiJsX5qjtkoHLmzkrG7IYwb+zHhuvbzpmJrig==", "signatures": [{"sig": "MEQCICttkEHXlgrLvGSmUKlN+WXQ/RiLkcjvedzafM4oZB9tAiA+IMw095bTxJAEmp4qizwLfYSoelx3R08mrjA0acPt5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0xRAAhU+KwnvuiUDAVdQlJS652KYQ+3YkdCVOeloezMks+4tCm9td\r\nBWkx+ODghKmxKEtOtgWf++W8OE6JhGHMkxrdUPLmZJgqAOqcnI3A8CxzXXuG\r\n31wt7A/rp5rW44lMQUTdzS5HbXdKKowRwrvyhyruIxnvazyFKF7k2pDyrfXz\r\nrAvL8zsVYwNontYIn7ZFzYmpTo/iB9rCMPUfImbQ/YNwcFQJ0q4bVsqIVaUa\r\nXn2UlcT8FhgM90y1iIME1NpTmjFTjRg3z7gO24oBJBQwKfkXWZ1iJ1BCFjX3\r\nTOi1Y6lCTenhDFI7O6EuC5lDAo2q96tujWyE45/uqD8WHldi+O8tVo5c0Jv8\r\nFpxtIf/kTlSjzNFBGT931rgNNpCTtVxgIuzd369l5NDVyT3vTXYBgUsLEdjy\r\nZujgH5AF8a39+SgHxe3QxFDwk/hJt6UFgctAp+bclBdhTQLhNsepDG6hB1/+\r\nRD+Hl3Lki+zVVrCTFYMzF4RGS5IC/U5dUWjT9UVIIqqnSu8N5QoMptrTdCTQ\r\nip3Or3oVmOPsGVbdIjsb8Mvkp7sMB+Us63ArUmnX5pIjbw3PlqaR/UJtEyyk\r\nsI1JtnoJYYH7fvoUF3sYSOmJx7FXTcS76jX0yA+UQasaD+MpIqLfT3y4X2os\r\nLhoCBpf1vJM1XHvpVluB2nvQlFOsN6kn9AI=\r\n=KvAO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.14": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-presence": "0.1.3-rc.11", "@radix-ui/react-direction": "0.1.0-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-collection": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-previous": "0.1.2-rc.11", "@radix-ui/react-visually-hidden": "0.1.5-rc.11", "@radix-ui/react-use-callback-ref": "0.1.1-rc.11", "@radix-ui/react-dismissable-layer": "0.1.6-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34fcf06586f107b7fa2c823286ffd288af798f16", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.14.tgz", "fileCount": 8, "integrity": "sha512-HQGF0hkdAukZhcq9R+bHpuhTnJ5upBDKnnWECh/+zvA5KbeL60MfhNZI18+GYDQqVPGoQAIADKGdkJC+r9dRgg==", "signatures": [{"sig": "MEUCIQDf7Fptc0+/YYSri+AIhpqYUfLFsGj8+6OYRymwgg1XugIgM0wPxxeySU14n4yQb/6d5otzSVleSWuka9SCLn3BoiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275292, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMHBAAi0Ccu/V5jp4bz3LYFzBYVtbGCZNSeWsS2VD934q8NKxgPYfo\r\nj5mNbZOCqX0cxj9tIlnY1Ky0XFsqjjfBP330si6W/YW9oKIMH7otjYww70BU\r\nldBv1YOEV6G1VmQsOf5OQ6QfF7r85oWHXKwJROKKwWu1weB0tdoVr4gUSVmg\r\nAm1EC5ls2vR4wt7KOyD9OOAKQl7PN/LXRU7b+5frb2R+rMv2B/S14JwxvFsR\r\nkMeIEn0k1k5su7ZX1Vmw1DoOP0sBLCZWDSEKXh/f/28zmjrXm62mlho51IdE\r\nvWb7N+vhG8QcdB5IHx3ILcdoXA4dyYAU+3ceW/VSmzqfHovKU9zjvTsV1KLa\r\nc5WhYHqVmlYroD4KrVXjN+1+DC6c0h51qUbrsSIbVlfd9FBrFzSLgygeh4bn\r\nzDs+FgRqKrikN80hN9n/FedMMkVOVUtTxq6lBgD65ckPIv3PWuiPS0AOci6Y\r\nHcg+udvulSd1RP82PiltFahf888u+GvE9aQtGFgbOCqkkh0CgStBDXllz7vY\r\n1Hy40gUO1baGlLGkFdfDzpiCFWkB3I5rqLgJqXMDymgnCYlWcFH05TeNIw5o\r\nmbOJ24iMJ25Pi2rugjlhs/iTXLpS5Ww2r+Rp7GS2pJLbCWmfEP/4mwTKaZMS\r\nxcfIp6l8v6A9jTp5PYDzugAbzTOSNXFCpMU=\r\n=9C1M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.15": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-presence": "0.1.3-rc.12", "@radix-ui/react-direction": "0.1.0-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-collection": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-previous": "0.1.2-rc.12", "@radix-ui/react-visually-hidden": "0.1.5-rc.12", "@radix-ui/react-use-callback-ref": "0.1.1-rc.12", "@radix-ui/react-dismissable-layer": "0.1.6-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d0adfe9e36a4e8e6a798ada52ad3c3a5b6f57c77", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.15.tgz", "fileCount": 8, "integrity": "sha512-CG+NvL+mXCK2spKYkWaci+ShxtL/7TSPoBb1kk/DL999+O9qlbcE1y1f0N5QT3D3jw+HAcvXhMQfmC00oe6jqQ==", "signatures": [{"sig": "MEYCIQDW2qSXOYK30qke1AAUx6pYGwtoU/JLcP3vPO4QhkfVnwIhANvV5Qo4t2oSc8yfrq8FfhI1IzHEJqkQ6E7ZgvXyBrpY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJzQ//fx6qnmVtV5nGJZVKJ5MHlzUHKRYvK8aUr6aiuo5JY6kYiQwO\r\nvETSNvUhuKCge9HGL3oH0agmQoLedyef4k+B3cWYfO1BQdplghZMZxP/zarr\r\nCtzcX9Q4bAOY90BdzbGA/u/Gi5iMqUcFe9ylnTna9kKA9DlUKWIkQVb1/tUE\r\nemkd6t8TPNAROfUbOm07FhY0I945CJDzaeGofj3dSIOi7akJiba0g2Q/lDuC\r\nZMspxfM+7aZKdSv3h+qT8KKQfVqpwPUtCrrZ04JszxVjy9F4hopEYmkvBQY7\r\n5hTz4wT/ltD2qlyq9hJVC7Z5VdB950v5Ljeqo+j5Q/bhL3YX6nU/8aIVJcvP\r\nJI1tepccuBdL3WChgtGI84Il/Ap2UZWg5DSNKYNzPMOYHYNpiMIHU6tp8zuN\r\nliKRewY2uW8r2oT/cLvUNM5rBM5ngI3eI89X6hEpbxNxrsqlCQ0hmceeGOhu\r\n2yDGS7fispUEMZkA6H8jtGlwjH3u9rRvdoLMfKZ7kUGv7PMKA0WgErtNMFaM\r\nn8ze7P1y7BUuTZVH6LUY+dGaAxJjdWb59EHILeOLufHgb0IcUZqobz+9RJMz\r\noNLHOidEh13ydmWlEN8lLMtESlWFmE0EzzWXW7gIm0Zla9Hn05ly6hD7Mpln\r\nPIF1PRcnJ134SzUS0F8CFrrVI0iEiAIxlYE=\r\n=0vJf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.16": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-presence": "0.1.3-rc.13", "@radix-ui/react-direction": "0.1.0-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-collection": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-previous": "0.1.2-rc.13", "@radix-ui/react-visually-hidden": "0.1.5-rc.13", "@radix-ui/react-use-callback-ref": "0.1.1-rc.13", "@radix-ui/react-dismissable-layer": "0.1.6-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "04035fef424bf5016280587172f03d89e4215910", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.16.tgz", "fileCount": 8, "integrity": "sha512-naRxEz5hgkh73RD177NGBMDlRr1oP0fvTkV/VVTle6Fn8L+1YEwMTqLri8lLMUJmbruobfBR5+hcI226t3OZkw==", "signatures": [{"sig": "MEYCIQCtrjyMjL25FhfITJQA//QK4aG300P+joFnpWfaIlt4BgIhAO3k2yCoyXnhhQqJl3A6JrdRf5UJLE6Pb8XzRwAbuUHC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ4w//WZOL1HNOtCyt2q8PWZjsagB7jwn4vFtq6BT8+yv1AkzCeFez\r\nFzI6PtyGOR09QQ+HRwgOLFCvhtM6myr7uMySwhReNW55irJmNIO5HT1X9n03\r\nrsiBbYreE7sbMWH2zGYM5jtR+qUBXooHC6ZEs+mLki4uXigczZ8i0r82Nbyv\r\neucLCBT9zGbCqMc2V+8qvnsHJmfEjy3uKqX5Xggt+VT0hZXr7nweaHRjSMq5\r\norwgoFuwAqH4+zcxp+JBc+KeO+zQ7EWaR7XCOOLqDJ5odORDRPS5upxhaHzY\r\ncAKJ8VIB6uU78O/Yi/teOXyx9nG0n1RmJY5bG2jl8YVzX8nKMFAMuARc0d+F\r\nNB76ljFFlhzqDJdYWrSsm8WNo2+8o5UPujoeEU9gYScKLeaZIpepcmPPsdhM\r\nnWr/g1nONSPDoRmLi982qEnO97qbCIYcFvPidu5wes3+980B1a4vEP16ot/j\r\nhSe7YLKoZDFvsr3Fsv5WKxot3DsnIBzmcaDXnfSzOrhf9EBmVncg3jzM/3Z4\r\ndNF4mAgxTHX5vNh8pHqa8oWe6f8lIVib/PI33Phn7afN7xRpZaYq2Hvk5A9F\r\nMMu1o1bBZHIUXez+FRpg8J90P9Ho1rjwon2qDL0fjWZR7zsY8bsF8zpr2qcm\r\nSpGn/9yTOvOg8WGPodcJyj7VGUnuLUE7NsA=\r\n=Wem+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.17": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-presence": "0.1.3-rc.14", "@radix-ui/react-direction": "0.1.0-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-collection": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-previous": "0.1.2-rc.14", "@radix-ui/react-visually-hidden": "0.1.5-rc.14", "@radix-ui/react-use-callback-ref": "0.1.1-rc.14", "@radix-ui/react-dismissable-layer": "0.1.6-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0abe0b29e07c4cda39760023ff99a376a8b813a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Gmc83YCtEg+tWkDB2JUdNXKVxaCFwYVKkWwxBpcRQWvQ/Rypno32A5LuZ9nwaHa3cSGYn3a3GHOKIk9T2ydzyw==", "signatures": [{"sig": "MEUCIQCTFhzsAxGLrMOjR73ZiHpEOeolexaYGMNWpTpKvYXzTwIgakVHDrtyrnAaQxpf1z2THJSsyX7t/LPxkByuQmgG+uA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrang/7Bb3dg+93j0Q6nROqUQndW0Eet6LZU7JmvChrGQltoLe9BkpN\r\n8zg92BgFigDhza6xz7sq/Biekjibmgnm6/9peOjh9JQ4+gjZq6X+vT57R0LZ\r\nqm80OUz0Ccc8U2rwvW1qMqTxPsp2rpsKH/fs/qTclNNS83ReZp8Ytiq8esPj\r\n7M+53GLBcdcl+8oxhur5LxSDgbb9H4OHBTzN2f7tPvCh7oAHbDtDh2fe0v9K\r\nOn4+bWG74So/lglsA4zJkw8VT6DLxUgLVOtiDZ4WbSVY7QgacXEW7l7mWIWI\r\npGRKqbfzRlDE1StSKgMRsQYd6+YAglZK50TrGAjEGAlaiMjW6XIp7kgiiqKk\r\nBFHJKJy6pMAS1KRSO9RPD1AU75061FRxe9vhsBjBNghLg1/QDrtgu7D1UnNx\r\nlw41Kzr0GA9UovPnPw9cEj1+h+mI3/ZaQ/trqp+3ZT2fR0QedjajNmrWnX95\r\n5I1hNIFmDmQivBrgpsYB8n6dQhBHIEb3RqhPCspnxk+noI2eOM8xS0qKs1+8\r\nhIRVAx3ifjYy7TiJTvlrG+QDPIuXgblH+q76iX02e36lPZWKz0iwzi/1SVLe\r\nlaNuOSuYxVoz6u+zox8xgSOjPpTYeHM5uvXR3F6ARAHSHW8mfYK/xHw4LYgy\r\nZipxHRORnFsg8Kmubkst1mSC4QzniFrocRk=\r\n=NY8R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.18": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-presence": "0.1.3-rc.15", "@radix-ui/react-direction": "0.1.0-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-collection": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-previous": "0.1.2-rc.15", "@radix-ui/react-visually-hidden": "0.1.5-rc.15", "@radix-ui/react-use-callback-ref": "0.1.1-rc.15", "@radix-ui/react-dismissable-layer": "0.1.6-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd6cf7fba7ce861956dc467950f962723c256806", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.18.tgz", "fileCount": 8, "integrity": "sha512-2wxKouJKpQuxONYx/FG/iKo2ROkzdILIC9J0Tf7kKGT0+bDOfHKyohXcOcsiPTk1QQF+yiUPpwSgXnMJoDQY9Q==", "signatures": [{"sig": "MEUCIQCNl0fOhc61G+5B+tB8kh+gaVPSLrOI3Xy9UNmkyKyBzwIgLQl5AwhiKuowgypJvg/su1IL2qk6myjXU7ahBzizUgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLwA/8CvEWHJI/ptmAZDLypsQ3rNbiMCTKLId6XWfdfvykIxuV59/y\r\nLlNvVFZXA/fvXBvY38KsLSWq45dbCH8Bt559vxnKnUmMouX+ymE2HluNSbih\r\nGNBGpUKBMYNIkD9DJs6EyV9nrRy5hlqoYvuE497TbLsXktXW/MYXjBNMAvWS\r\nlvnXp6iCVZUa0NUHmlCE28ew9Mw1po4UPbG0j8wtfOra4nubXsNftS0+OT8n\r\nfJkbO8MnQL867jUoeluYUZP7cDQFkkCxftyeSdOVCX9eNA/OLpjMm75LN5wN\r\n1egSv07MeTBV2xea6Kj2driHhUHAJPinN8jtI01nCIH9xCh5tLfQ494qpHmx\r\nRLJeFvxl3AwCjc62vsI1UPjmYevwd+KsXr/HlovzpMUVUVk2s6b3BmPl3VeS\r\nv6KSzgJY04IBROT7DtjjuFNrUx5hYIVXekmcr9uBxVuAFlonI/EYauyr5oc+\r\n/gQ2MWHCVZjkZSvhGWeoVEWOpfvS4DmZdHm5HIztWvjOIc8v39hVPtOI6rVt\r\nExOVB6GfyEUpDPw1JrqGn8/H/RbSvsC8gX4AnA8ACZZf9vB2OpXB7d9MefHd\r\nk4ZKmeUolMSM4AJqHXGUD9RiOBsKs86gS3n4aVRwTrzniwyo4rLsl7ZSPvG9\r\ngVVFegwQwQEP5WXyZeHuErwWQr9QZn3+6Jc=\r\n=m8Xh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.19": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-presence": "0.1.3-rc.16", "@radix-ui/react-direction": "0.1.0-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-collection": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-previous": "0.1.2-rc.16", "@radix-ui/react-visually-hidden": "0.1.5-rc.16", "@radix-ui/react-use-callback-ref": "0.1.1-rc.16", "@radix-ui/react-dismissable-layer": "0.1.6-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "97cba557a8906fbfa737c138be65dab13164a983", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.19.tgz", "fileCount": 8, "integrity": "sha512-L6g14u79wgSE9GfSVMwK+3y9GaC5EVBHSg+ClB7BfXXg3A2PNSAoItKcSO2w+8HrzSq9bqDO2dfgtniPA4AxQg==", "signatures": [{"sig": "MEQCIDjAq/Fv201KgL6sY6fdwxMalQv86b+GJFkwwIvGaxQDAiA3Iotfj+9iCEiKFgde8JsBimw2eGJfrvr3PHZi3ytG3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTr3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6+w/7BrgaIkXXZ2o/maFWULtd+Kl1YQezLTUOpMCuxdmRa96GwBdc\r\nmSxgLSumJCJETLF9riz3xAb+0JoS3d9sS733QlI2tvtWnV0ge1hVCLgT70ju\r\nG5srDzjxdJu8ARq8AQN4kIA+2XDzilTh0TKWv7fYx8ccRMar6pbAgQM7ETcg\r\n86o4a2dNknLWwwLmw1gEgzHEFBtMxfyfoWkhPovGj2Jv9oe5Gso85V3PUJ7W\r\n2JZhM9J4EkhfzEFqyXYQrb9f0jMRx5wmC0gPXugW8rtz4jP8jfclpaTb0ICM\r\n7Pi5Np6tOqAHCkTXsyVPfwM0JoX1RUDihq66G81gdi4mBT3LR90LM0uvSP4W\r\nKLajlYdDt4qwfycNEuqQCKNk1iZ5PMgTZCZDZOo2+ExlyBvrb6QUOmP1KCnD\r\nVvZT8ltpuPDubqovsJi1ipR+8fsaTJ8PLt/nC1QXfpj0cfRPW+97yuHWzhVH\r\nxpx359tT3p3iucxYGj8EKeWyjNBip6IhmWhogbSEeVo3hqsxAeTXinZYul5X\r\n8KJRNAC24M6pjqQEsr7XXAgT6ZLiWEyzlt6z2NGIC9rBwzLKIaqe7oicS3wj\r\nbnEOOmwXitYw93Oq6okP6mWuDeuj7/NgZKUobeUoeKj9CTlPciIiVsA+O+Zh\r\nkAW+VL6rvzGZUnrPy9eFjHtEBfnZZXO5q5Q=\r\n=3j05\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.20": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-presence": "0.1.3-rc.17", "@radix-ui/react-direction": "0.1.0-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-collection": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-previous": "0.1.2-rc.17", "@radix-ui/react-visually-hidden": "0.1.5-rc.17", "@radix-ui/react-use-callback-ref": "0.1.1-rc.17", "@radix-ui/react-dismissable-layer": "0.1.6-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4c2b783dbdc690f8f7aa6cf49c63490639ee338c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.20.tgz", "fileCount": 8, "integrity": "sha512-HxTEy8lSxOIhprdfTbW3W6k7Jl5qpABchayPGPjBhPNN3PD5I6bn9bos8kwCzmhO6V5HPLqi761uI2m/8UImaQ==", "signatures": [{"sig": "MEUCICYVsNUFqvbcelwYJwTXAmbgiAELb3tTag3L5dwo/gYOAiEAm37OlGsSzftUs+nYT+Lm0hksX5acZmMtIju2+iILu/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdPg//ezcGlPWfF3ZXidmRx1bwRa0JH1QQgSwbOF9fdUiIqkW5EVcC\r\nko5nBeAYGuE6KSkqXh5Px7yPuxrv/rdaj2b3nQuh7ijXoc4lpvtCOopUvrCC\r\nCfxjZ8uKxVetNV149CJoUEUZw0vOngXvbnmmd63vSfdaEsfTK3AHMgIamE+N\r\nnJ8wUKN+ivEf4KxrWCjYbZW1PYq67Z54pM0qX6VQmax9CrN1mml4VzgzJ/JY\r\nHkD8P163WCqRNxZOE3q0h2uWQHQ1WDMy6ZqNdAAyjp8t8CuV3qFviq+nxfIG\r\nDjvN7uJImFykz3RHulSnk/gx2hS0y9jalq7odeIMhclmK3IRtgeszM0VCInQ\r\nAi30YPTmmYKLX7WhODq+6yZgJJTp9maKssw+SNgyTfcOfbJGi1UmAPRWBPLK\r\n0kJ/w0FL/TFL4j7NpEllGT5lWx4XLdfCUWaalvJxm7QCTPDO1P7KG3obETrF\r\nxoUe8ruJzxlpUk5tQqLPSZTifPvwna3qjOcXIp5jSNm7DbszC9OqA9ipvFsF\r\nVBErNb/nF/JMaMJl5cQUgNUWxgo78x0I+BUNEHZDQUwBYc9DPxQkfmgKb8Th\r\n9M/I+CsNDBtYpPF4xcJyOO+jToTUEwrVijFlV8/UHcarZNoTKG4Ro4GxmrP7\r\ntbDrd0Gw8BYi+UvKZXhkJALf9Y/nY1dcpkk=\r\n=O8gd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.21": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-presence": "0.1.3-rc.18", "@radix-ui/react-direction": "0.1.0-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-collection": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-previous": "0.1.2-rc.18", "@radix-ui/react-visually-hidden": "0.1.5-rc.18", "@radix-ui/react-use-callback-ref": "0.1.1-rc.18", "@radix-ui/react-dismissable-layer": "0.1.6-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a593ac48f59c03d5bb15b2e5230383eb7b9a6884", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.21.tgz", "fileCount": 8, "integrity": "sha512-HApMmJe5UYb8WC+wXs0d2TCUUQDyL+Z6uPQIT9/UfjiobKd3Y7RtDz1FaqLgjDxRLsw8BUEsOjTqez/Lj0DVzA==", "signatures": [{"sig": "MEUCIF5BSAgXAGmDl8HxAVOBEOQyDzp9qM3UzWTVaHZFJTkiAiEArUNABcVdDN0E7uMdfeH3NsKJNVfUu8pDwvQIsqi4zQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDMRAAgeq5yKzbWZN0DSMWHPvAJPn0qOLeTh4I55Ohnee7yYJyc3TA\r\nOkzEFgs3Jvg1uQJUWJpgHjkHqPLlLLtQVcgp/1VfqgZl01iJ0IDSJOn3c63z\r\n8P9+sCnZSNBTAZ1zl1GtzgBdxj1EFbUf0jZSa6X9KDrLpkuXLVetT2KbbXNU\r\nSpiIpuX+PJkiawPFm8mnSKiNVZWM9+9ivplQpQswz1XjTKTQsfqZUgM7kDat\r\nzxWotuoB1S592pB3B5rByb46wUcwfEfANLT5N9AFIpYy6P9PqXRa/CXais//\r\nfbdEjkBT6/XvgwGO6k1OwS0yMXfWkRUeaplZeedvzXAX2Agf8Hqcn5KHspE1\r\nvLLEbo57QFgIB5AnxAOEZ3B3ofhcoAmJwrvnudOdOGSTxfJfCn02sY4iCfxR\r\nLGsGeQAgnsKSVLlg7doW2iIXWWn6rG0dOSjGClTwposrh91MEP9yWaCujcac\r\nQlw9RgE4RyxIZKcg/Dvo406Gzw8Ao5LYDpSaSIHZLOe6iZPR+nc2dZAdxoCI\r\nYes0m2wk6AQiNgd4AIZ5aaWOmdEdddalftdqt24pV4LiwK+lq/LzGrslamWw\r\n4pMQe5zCcDKReDdqImGtd4gVC+fbuGkOpxU2dfV6OnE80Ukh8gYonmtMOgMZ\r\nxRHloinfBr1KKgGKeuXvVyEQYAofto0oUPM=\r\n=D+m0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.22": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-presence": "0.1.3-rc.19", "@radix-ui/react-direction": "0.1.0-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-collection": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-previous": "0.1.2-rc.19", "@radix-ui/react-visually-hidden": "0.1.5-rc.19", "@radix-ui/react-use-callback-ref": "0.1.1-rc.19", "@radix-ui/react-dismissable-layer": "0.1.6-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d0c6bdc964102e674f7106576267ed69887cf02", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.22.tgz", "fileCount": 8, "integrity": "sha512-k7cxlWDar0MTU/tiFXxCbfIVC+Q4ILTF7FopZxU7WXZzpq+tU53+27Pts7GnhgY4nXAc4VrtUtppCbkF1B1boA==", "signatures": [{"sig": "MEUCIQCaJNh0oAzQD/s7L0wjx+sqd7+FAzWTDUSD+Ms1cbV3AQIgNmUPQgAv2tvCc7DKsMOyyKXFYNCbhcktJv5GPnTwJFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPLRAAhJP17hSb9z37Ohy1bNujIree+SKHk4ElzrzcrGCUJObr8T+U\r\nd1qO7n3+LwMm3qAjFJpLOIVUJ8aEuboB4blCiDaReJLrWpCNpPY0HI+KmS08\r\nZ0nXs5LtliRfIjgNaDOywIQVoMC+vSnq8jcvnSuIwHuXygWsZuYuvgTrbyC2\r\nDNZ/Jfhhd9SfgGAk+vNXJjBCf9nYTV/rBeVEL3gBNAC6mGYCW6GBwWBg/zDO\r\nQqtPZfwMz/v/isApCKGrwoY6jkoD43//m+Ny0toL2vxlIIl3zkUiLZQCWHSV\r\nc2fYBJTRSuNCeke11UaSVfPnNIc6YohGjJSBZEjLjGWAOjO3AtrRfb3HGQjd\r\nq36A/6bOYmkpbPuMjsUBL/pDIpVrJEbo2cU3Ipqpy6R6Sx7jv8ohG0YgJQQD\r\nXBlF7Wf6zAECwOxdkbjWkjxRvrMvXZHSfzwy0N0hVCEf31UsQNse+rkheUcV\r\nICr+qgOpeB0AcxnmfVWRtH10R2JGeJ9LhixwL+dZms9a6eajSzSm5rq2/i/J\r\n+YM+NUCYZmQhnt24PKlA1o0nxYz7eRAsh01Hl3qsgUEv7LJEt6nOl8m92u2/\r\njUzePWmWlpd8mJ0H9WyU4NufoWiwzBMsN7tsZQm1I2PKZSeflaNn4ITJFsTX\r\n2Wa6NeYUL0ZfE3Vwx0SBBvlNN/Q0xkeBMlM=\r\n=tWNJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.23": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-presence": "0.1.3-rc.20", "@radix-ui/react-direction": "0.1.0-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-collection": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-previous": "0.1.2-rc.20", "@radix-ui/react-visually-hidden": "0.1.5-rc.20", "@radix-ui/react-use-callback-ref": "0.1.1-rc.20", "@radix-ui/react-dismissable-layer": "0.1.6-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7e0bd3d37af61d20133a50ad21acc1a596838dc", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.23.tgz", "fileCount": 8, "integrity": "sha512-Ww46i+xfZD0Zq/jqzrsa/suxH3xAW43t58z/MnV+k/bPxOL8lJQ7NX3r8mtpo7xVvuQklsPzvx7F//o9qqyUmw==", "signatures": [{"sig": "MEYCIQDQOA5IX6a8X22W4Wt7Ii4/dj1v1I2E+WMfU7kVmRIrOwIhAKXevP3ffs2N2AvSMjY9rmYWMJ34n3Hss4dIwmhpIEIc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBog//ctE2khAFHqNVqveZb8JKZbiZWkdvohsH+F1DEhZwRhFofnaH\r\n6kfbgNG8B+vZ3cnOeAEVtEq4W9tAPct4f42oQNU4PObEYOogX1ss0XxDE7Ev\r\nm8qh89iTjWFGPUHRXPAUZiw4H5oSEqmZLHFSS8wSuThHoV6HJOl2qDYASCW2\r\nN3yFTBacaDx4CkJ10ypSxN82ya9K46Ft8jbBSQC/m+RVjYse2nWsHo6itzm0\r\nQNuVqK0mN5OUB5LDarMDeT2ZF6FsXrBTKZ5P9FZrIRj9i2uFL+cCtEIb9bif\r\nDXHZb5iDhnowSGq4SmjEzsVcbKszUYeH2FWxK0sQHSz4jSPc2HuelbKYFRco\r\n3ndZ68McKh1pszpYJdN7mttLR8N6pVJyh00Hbdl3Uy/he75X4uB1gXl/HXeT\r\n1VzqpQvKJMxadwmByi/xaMqnygGzclmFiKZZIa2+kFrWsbEmCdDC/w3QGtBC\r\nmXjEvu5WPMyB1U9EJ5ZP/8/owiCfIoDO3fSZ4MT6lHHb+aiK8VwQLA8PWAP5\r\nlUdxyMObse+Ab4nzUqNFLmFcWTVfQgmO/KkrnGlwQnanliC+KH9inx7uZo+a\r\nqSHAF6TiGstOkd4+talXRCwyUnDrerhsCSheGpI+SBpbCHnAiA+0JKQdffwE\r\nMfJdc0JsDhpv/gMAmD8xcNyaOLrqDjL2QqE=\r\n=BuGn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.24": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-presence": "0.1.3-rc.21", "@radix-ui/react-direction": "0.1.0-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-collection": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-previous": "0.1.2-rc.21", "@radix-ui/react-visually-hidden": "0.1.5-rc.21", "@radix-ui/react-use-callback-ref": "0.1.1-rc.21", "@radix-ui/react-dismissable-layer": "0.1.6-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f12d4d0f8153035f112a6e8efcc17cbbb63a9f13", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.24.tgz", "fileCount": 8, "integrity": "sha512-kI1x1yBAFUNGBqKDWa5W9xJ4ExyPEm1wxW8PE5N6fgCvGCYodlpcmxldeGBgrD9fKpl/QW1bI9/Kq+sZf0k8xQ==", "signatures": [{"sig": "MEYCIQCG+48xOoeaiuIsEx7Vg6HJMSyQmWkFVF3Bl/fVjMpnqAIhAJ9eXgyusU7HoB1FGloRRHdqUJ62F+/lBxK0oKkvHwIs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih598ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGBRAAjkcAXo+hsfXJZqTwy5BFWtxcy10SB+2h3PRTQ7FJ0Zcqef1o\r\nKXyBICCiWLo7CRNDbqyvDCkdJ0lk1NTZHNkQ+7fQIvrls2cdVly6AQN/dsPo\r\nx7DLmXXXglg+UaD5AG3JeNvccW70PxoraM3HtoENcFwFudSrjmlVPI0GVHUK\r\nLuaOm4Q2bg7LFl/LbNpDP20jrH6AP0bfadgVFF1rwfYb1QMQbLFdv4JuoVaB\r\nEZfNNzZhcSj0iti6jkG3eafF+tP6DVhPpFskkrQxQjXqMoRBD6iFIZ+gsGg3\r\naSqjUcWLE7+fdXiwW10A/GVvfRtgGvcHGOGmBYRss1lsbpCIwXDWzsmimXA0\r\njj3xHdHxp3yg4rC+DK0ojmwm7SooZ2lKqbsHzClXlar/7o40+WoeLchzemvf\r\nMa802SxAs8zX7d1sonC8fCYTaqPNjJQNwpQAVIpSB4q12quiwKjJvkttB8g7\r\n+FkTAW4eie5ecFNxqrYXuxohfHTL6wimyxn4bIQIgv88bxrVS0IX4KD+EVNl\r\nctK0ueA+vbnb1k9PxMvAd47TKFvQr0giduRgVafJbefjGGliD41G/DAx0JoA\r\nvVSSyrFikyIHQL4nVwZKefDTA4PVj/olmXXcqLYypv/umXvl2uP+uu1DLl/0\r\nljVar2DfFdpP2VF/wjEm20A48ROu0qvAqxQ=\r\n=y6DY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.25": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-presence": "0.1.3-rc.22", "@radix-ui/react-direction": "0.1.0-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-collection": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-previous": "0.1.2-rc.22", "@radix-ui/react-visually-hidden": "0.1.5-rc.22", "@radix-ui/react-use-callback-ref": "0.1.1-rc.22", "@radix-ui/react-dismissable-layer": "0.1.6-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ab41719d13213f192b0c94855a3b2d858d91cf60", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.25.tgz", "fileCount": 8, "integrity": "sha512-oQ1rPP3LVx8QSXweT+60Opz8Ozk4VrifJO162iHlItEtQrWLGCB4TxESbjDX1e9YXoGBpxhKTZKlan9dZ5194A==", "signatures": [{"sig": "MEUCIFStjgJhYhzgMV4BS7QVeDoZQ+i/shKM7HOEU571rGW+AiEA46L1bIj/StXddTMLinZBNBVK7hY9Ig4Xbv8aCPc1cGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqivg//VfI8bldTnm8V/r5G7wGqDSolW261yithizF7gwnrtXLCWcu3\r\nDLuAmHuCrlVcW+Q65T89ivnTHndZ35JcTzhy/6lfBvzgt2BXCuF2DdfSF94Z\r\nnvvg1N4vl72NdoLO0o00vTCcA/g2MN8EZtTqWXwvEkX3sBltckk26n44D+4x\r\nMi4RO6zQf2kT6Fugrq7/wszOIxrzIksGEYvKzXesOZ1nwZzO7IVOkHe+7n1C\r\n832q+wH46FRiAJybw0KK+LEWpbwVwm0T5yVd0bofAOVqpCtYeQ80Rcgl8Vt7\r\nFtDY9FCeRSf7Vi2j0zY8cQQtKFvZs19jaojYGFckp/cMqSR+bLQNtfgUFmtz\r\nM9DLnKyVZlR8iwokuSLk6EIfx0WWxp0yuyStGOWBVd9oFDIK9Z5QdP3UodCW\r\npXEt/C7J0cvjXmMECBHK9U7ZT+L+h2w13jB4j56z+nL0ZbeN2XoHhKdZ2atg\r\nhv4X7z/vhy6pK9UbbOmoWyFDJ+eGfSfQfbl5BYOt6mQ77MbwY5c7Hqh3fshc\r\n3HpvA614jZzoV7MyURulrkmk7mMqMnjaHD6jvt4Z0lIjCUFezvyxTwD6NGgm\r\niT9SkqR5dG4FUMLDXEZ6YB/eNEtJ+R9MaI+fyg/s5VLEq3N26S+2c1MVpkRX\r\nYub5FeKWFJuXh5gSi2A6PaCsK50+56nvamY=\r\n=Vdjg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.26": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-presence": "0.1.3-rc.23", "@radix-ui/react-direction": "0.1.0-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-collection": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-previous": "0.1.2-rc.23", "@radix-ui/react-visually-hidden": "0.1.5-rc.23", "@radix-ui/react-use-callback-ref": "0.1.1-rc.23", "@radix-ui/react-dismissable-layer": "0.1.6-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "94f701e1755b747c5a82805e63ffe5108d48c918", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.26.tgz", "fileCount": 8, "integrity": "sha512-+6H/Nnro1Y3z15bwzlUAmFGBgpHViwDbr+PLUaqNz+rdprd+CTybY8EGNSe7FhzzduCnuTHktasq8F4TFgtT6w==", "signatures": [{"sig": "MEQCIFVnDEb4Epjk7doycmibtEEk+j7Cj4nrMdAgnHmcQ9M7AiB/zhFPBtWpWm0FXg7eB2ZMAV90MlCDJXrUBRdClBm8aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsiQ//Vjw465JxPf1KX5vrrvyO1dmeiUFAD/TRAVtNNz3oNtOXPSEe\r\nYl3L9y0B908ajyhpcL3QIDzlz/FvjryD7sg61eV2ASpJx9tiDMkukIjx6Tk+\r\nmu+ZouiCoSJLNM2IJfweog42NXz68hI+NIOoS2VS/EyLw/AyBksmJsC25Dbt\r\n9FydXn+zTHpW8gpQybp3Xj/qvoQe/492dCrGqFgCGb38SCTE46vt7vyMaRQc\r\n5aVCaz5Zf3hemXlMJgbdxN1p4AGmHxlwVdl8oB4ELRm5SFEVVf/UP2wdnLn+\r\na4iE+QKcyrFHzk2agAV1Zsp4d/HQEIO15td7xo0GVgMs+p1ZblMVGAfEYCli\r\nIOZ7Z88kainAASforJp0U21/Nlt4lyZG7fG8SSvcbWRzFHOFF7KQsWLnxCmn\r\nYjZEbCT1Pmy0OIC819S1q6c4NVHOdeQMRT5mVgVC5ZZKr5iviVJYCIMNBZpv\r\n6uGDGUhBvUWfdzFTA9oRYkXC7zfAKMuhX9OE44ufDwEKN6Oxwb+lwTioUf1f\r\nv5pqajKzR6Ca+HSGoyWnRF2nZOajqPYq7xUqfsyV00MHYttN02PuIXBgPEuZ\r\n5BTDl+TvIY/kks6osQpGivIVLg2HiNr5aVpYyveA9q0Mp+JJOzjSmTSBdtDR\r\n2VfsWcovfIgY8D0ux5z7M2kxHE1Gn0Gvg5E=\r\n=T1hi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.27": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-presence": "0.1.3-rc.24", "@radix-ui/react-direction": "0.1.0-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-collection": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-previous": "0.1.2-rc.24", "@radix-ui/react-visually-hidden": "0.1.5-rc.24", "@radix-ui/react-use-callback-ref": "0.1.1-rc.24", "@radix-ui/react-dismissable-layer": "0.1.6-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "293e3d4c4b6ee78c768477dedea8e6f7562cf5e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.27.tgz", "fileCount": 8, "integrity": "sha512-4xuvJsD/k2u85Q92unRFIxSxZ9GQkDWQOe9sb5yt+nJM0nazuqZocPvdA+ltm8FQqYwXBToHALFX/VVZbW6x1g==", "signatures": [{"sig": "MEYCIQDku1o1RHiqEdXMu6a2ABaDvuPIDgM2tnH5zl8E6yz7AAIhAIMVmJZUsuRpmf6smVwXvNimtZ/rkpGcl9gCZfjcaV6d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKew/+LaSZ60dRKu2V962ZMSr0tZSCKINWtB2g4tfsrM/ct54FtUiG\r\nu0wb3ptCA6uYWrKC7ZI4r2/RpoAXqy7Sm5WdVHVWNwe8iZ9ZRPYBTcHiP0ke\r\n8n1x5EX0Q5dObjZRJJOvtBgkP5I9zIKlj9Bwmnsv2fYOLZrvWE+iehxpzxWN\r\nhP67X2DACsntiPglGmqr73BTQIgcbXYjdCWi9OfEI6Q+/zwJC9baJErJq8Bb\r\nL0rpSkrhX2Lafe0W9NtehBKHISHbW/cisjMSqbzmr3jJ4Cw+nP1h6ysdsu+R\r\n81MzXdzSKDgE5Mh+s1sv4DOWqmCiOnJiAaAz94g1j/hJrvpnfcHRR9gWBoBh\r\noNKOIEQ4OZqvxzRVxRjLeVkNUezmIFGco37ZS7IVKo10jaqzqRTueBAvo9Sl\r\nDJI4RtlBT0h5OQpPfqJ5kutt3rmGHz2M2Orb+On9HT4q5AJjH/Gt8h6jKJhV\r\nXpstohGOZLaoOL0ioOLRP9dRFkK+lhW0SuK8XR15zJ2poFQgGZvhUmKlvild\r\nEC24xLPYQoBe2ACA37vNvBvJmBZbWB5krATiLI/NrYzHzqZLeDo4+Rye8mqn\r\nh+N+yPGf4iQ7YQLnGfMVozHAperv2+KWv5T9bMXY3sP+1yJqEXRGE5lpUioC\r\nLl5tsElDNEIRWGtAZ8+XTC0JYqMQODQ4o5o=\r\n=7LIg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.28": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-presence": "0.1.3-rc.25", "@radix-ui/react-direction": "0.1.0-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-collection": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-previous": "0.1.2-rc.25", "@radix-ui/react-visually-hidden": "0.1.5-rc.25", "@radix-ui/react-use-callback-ref": "0.1.1-rc.25", "@radix-ui/react-dismissable-layer": "0.1.6-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "993b54a9cb5efdb2ed4ca757d8d6a557e4e4a2b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.28.tgz", "fileCount": 8, "integrity": "sha512-VxxiM2b9bHBVf2ba0brncxih05LWNEN7sxXpKexV/CH1ymSfHv2p3tMmayo1KMiO5RQCvH1ujg2YdmoHGC76VQ==", "signatures": [{"sig": "MEUCIEOoMFU8dO4t8iSz0ymA2Dbr2MpamM1Si2ung2pnWhQDAiEA1xIoV0RrG2dqhjQ/SMQRivpDrls+koxrNrdnPaQ1zgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmry7w//TpXoPgM7ZqijMsPa1UiAmWRH7Xq/FyWsXUs+O9PmR8EOoD5+\r\nLfgTd7oZ6NvCAyarpU2O9DBVW6K8moLPQZPh8EHYx4G3kvBnqjIw/56KyN6P\r\nAt0yn9q4rot+pTpM6DNV83rVjOuN0PB2Y9E32UmaXIrAjfmbJbnkQE8ZcFid\r\nhkcH33X+ayX1zYwi6DiBaHT5xUd0RiaNzo5WcN8KYv5RcPig3UYJNyFqHJFh\r\n+cXdhycGcd88gz23RVW5XXKsuymYQguelBhN4CpvYI0/FR5WRbn9yLAJUQZx\r\nzsKacZcNs5MxxCcl53qxfErJXkSLf3J5OnpKZjKPrthvFjZ9IrHWmpaVCcJb\r\nmf2kYyd27FMowJe5vy3Uk01Bk/ooe/ZQ/HBJfJxgUz6w7Az2opUXeCM+uLk0\r\nvvknurw9uBo18xLNjwb2v+aHp/NCcP2tZrEvi4z5EOCeoLKKvbzKcNRjwKKl\r\nbiudoNLmQHzwsEDF+XAhuwTke77sMCSIAQZYDD6G7p5n2lgHGgvyJnFpE+2P\r\n04B8ihnbZpqgNnHMp6W87+mz4J9K95W3rbtvFJiklh2WHyvjTc3h275ZCCZ7\r\nJQL7gFMvILjNR4tb7E5+1A5cIJs7aXlfpzCZs9trAkJC1dcoJ9oc9huMhqyG\r\nAOT8JayC3+esv53HmldsfKGApSD4h41eDxw=\r\n=Jdru\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.29": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-presence": "0.1.3-rc.26", "@radix-ui/react-direction": "0.1.0-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-collection": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-previous": "0.1.2-rc.26", "@radix-ui/react-visually-hidden": "0.1.5-rc.26", "@radix-ui/react-use-callback-ref": "0.1.1-rc.26", "@radix-ui/react-dismissable-layer": "0.1.6-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e48bcde01b6f93159a1c4a5d893d27bf069a8a7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.29.tgz", "fileCount": 8, "integrity": "sha512-Ibohhd7Sxp3maD74yx7Njt5xFmrgDshc8uGKUUi+2c4dppeZ1IEU6Wmlgy8+Z6hZFttBziY9V6U3g8ouBgho7g==", "signatures": [{"sig": "MEYCIQDtlLjoB4FHlWdceUGvocxmnnvVMg2mhyvL7uUEG0msSwIhAI9JZGgHlW4kv6dhsKFJUsir7QuZ8egZ3Xxvl1RAJ5rS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrK1w//aWc1ctJCpQj16lZCxOMNHZZNOFI3rUUnareWGsxaSRiWdGox\r\ngcQ61SDe9gaobilWggtn1ypJFArW2fbePVGip+EfRvFWkyGYQ8NBcemhWnVa\r\nBEUVoccTZxk7BcIVdtJLnX1gki544giMcJ3ubWeFcXwnWS35GUFLuB+Gf3oF\r\npAml4Q6280KIem69CPEJrL1XcjRmUQR+pBtX6DKJgnng0+f7B2HCgHRfRfEa\r\noLpydpr7c6V7AZBmBpBEjCD/mdTSYqcK/OXUpCjliCyweZyymm988Bfz7nGD\r\nek8v0/Qc0XieixVooOZbc5rDg5fYCIOtzEkLLkwM38ZiWFj+fyrP/PXt0GfR\r\n38m2RgadgLhbNh8ADaqdR/g7cFmCjzd0weL/xj+cMTrvZSN7x5iKEJoAfwQ2\r\nHCH8Rd1yA28L1W0BvvppJd128VjUY28hWoIEkZSnHekIf/fg9LiBPUZ/15Qb\r\nPHWVPd6trbrhCMg0w3uGzgdq2dVymP7NEcynbdMuy02alIQe+eLqGp9wslIZ\r\nB9yy+0561qOaoUThc7CxaQQCqdVddHQyOYfl7q1Rk2M2/WoZbQJqPabvhqyY\r\nO0K374yUcFPRMiLtr1TuNmS+op6YNoDnDzUI+R16qP9dUuK1r1z2ZoQYI5Yk\r\nEln1ODk6pDfSR9ddS802jceVRlOXHa95pPo=\r\n=9aLr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.30": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.27", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-presence": "0.1.3-rc.27", "@radix-ui/react-direction": "0.1.0-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-collection": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-previous": "0.1.2-rc.27", "@radix-ui/react-visually-hidden": "0.1.5-rc.27", "@radix-ui/react-use-callback-ref": "0.1.1-rc.27", "@radix-ui/react-dismissable-layer": "0.1.6-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d8a94716eb6d1054cca6acc4c34ccaec3cfe6e28", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.30.tgz", "fileCount": 8, "integrity": "sha512-WilQdc66p4TNLkAUyd0QzsEqg7R4xV5lDSyllXRq5a83IOgf5IXljvZqe2Us12ATfWqcOSXSTYQQMwfCSOL8sg==", "signatures": [{"sig": "MEYCIQCtD01QZTw56KamWcSVU+oLUzOPfskP6fk/MPvF9rBgXwIhAOQrzly50pwJm4W/A8+2zklCGqP9q+o2TD0VzRVZSSC1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRAQ/+KkIzYZ9T/vFmL6p/4et6iSeoPCSL5fww2WYsmgc0Yq0JjG0e\r\nrD7eeY7s/AZy+nYRIU7wgJ2RBui91jRYMRtLNlAhK9w9x7buY8vo/f/ECKoM\r\nfCLoHDDbti23+lWQC+Q4W4Ncp0CuwNcUkNQKbt9crzt93M2WTnC/wMVGXM++\r\nA/OZd0nF1CmZoy6YjH+yCRfN2WpkxStV8XOpcPyKexFAv067k5JgtDdt6BYo\r\nlgOYD5h3ILY6Itynk6qCuDZreL8LdYuy3HmX4pw3o3dr5UFFw2x4FDoVe0DH\r\ngNBrBc5H2lsLZrKSH/yjtwaTwsgnM9VSQHMu0Rls3fULe225HuJnez0/1GT/\r\n3pPapJosmL7RUhDbQjhCHPAQEGGOE1U7GhRIWlpKKsI0pPR3Ukbfji2ucx22\r\nBPM3NlnvS0CanvUl27k0MlgoSGmzu69cEp62GAUx0ZtaLfAazLlcugTXfu6K\r\nkDpJa14SM58XZHms0nY4sEV3vJG5C4n4iCsGjqjYPca7H1I0PKbvALxqhJz3\r\nUsPrjZc/kzl32UqbVvrbSACvJpmGL7AH3cUlJbJuG2bhL+g588y2Kh4MT1U1\r\nYiIkrsKwrFB19kn6LZAIQ//BE8eSkxRkI62mQSqAG57PxOyraDbpQdRtHdkH\r\nqiLAMURh+xT7Hvq4wipmIcTFs3ADtcoyh9Y=\r\n=TrVq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.31": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.28", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-presence": "0.1.3-rc.28", "@radix-ui/react-direction": "0.1.0-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-collection": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-previous": "0.1.2-rc.28", "@radix-ui/react-visually-hidden": "0.1.5-rc.28", "@radix-ui/react-use-callback-ref": "0.1.1-rc.28", "@radix-ui/react-dismissable-layer": "0.1.6-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "daae8e03f76391dfbbb814b3ba5f274aae15396d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.31.tgz", "fileCount": 8, "integrity": "sha512-Uz+lEILJmBQuUR9g7uRDGWjQFPxtdKUaBfjQJRL+9q+xTL4o+RGeIyRRqsRBCRY7egOwq4wQAJajUACT+y2vNg==", "signatures": [{"sig": "MEQCIAxEOJxUG+gEuGhz0LN54BUMC6gBoo/jbPmP93wxyg2UAiAgNIqSePbu/XMAhHJfwjvAE9pCyEHI9JNPD5o1Qpz97g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0uBAAizY7V1/BV+Y/ysSbu19WUt21qHqM6oPCu84iG/zk9YR6jgAN\r\ndTv7X1jvy1MCSQbyWiMsAidzwEVkWp9/ltpNo17Z10Lerq46ufU39T97KZ3s\r\nvmAC1mZ9pHA/TGOMA35frR54dgl7EN9QIAnFFqlRSmliokuZFHsJtKDm2cTz\r\nNg/vnGB7c7bgMKByY+8BwwQaHT7tqtNDpISMRGBt9ztnv54E/hRzoVns6PO/\r\ngqyAZCDFMexy23gYoVABEQclM9th3Ln0ezZ3KJ/Lk6novyBbmLvyh9QiJIj9\r\ny7rgan9W1pOnb0AdIJGwGPc/4kWiYaDtoDTDYePEhNnRkrgpUqkd7C+KlDMQ\r\n7Th6iqEUH9n2+KrKos3ffoKtyexZZ07gyosiu5aJjxPgm+kVZkmwRITfCJ+g\r\npD/se4JDIvwdyFp8xnKv9Ul+KKICqWQOup2dVinv/8sRf8653IDr2clDGvTz\r\nSOnQrmToPMjY8HTH2++nCATS8X/YNr48jUS+uaAudspWKsZtujMnu7QkQX/I\r\n1I871S0xNFb1hDGl8xK/MwDTUj7nJ15jTaDo7cnAFv99cgC3IABPdtFvxscl\r\ndGM2kXWPm2lFcPca/zsC81yjszQMEMBuIcGhOg2pTpXzY+ShnTswtgw62+Pq\r\n9BEx521Mb2SgkvOQiUxA7AJ1QLhDRz2nM7Q=\r\n=kgwq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.32": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.29", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-presence": "0.1.3-rc.29", "@radix-ui/react-direction": "0.1.0-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-collection": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-previous": "0.1.2-rc.29", "@radix-ui/react-visually-hidden": "0.1.5-rc.29", "@radix-ui/react-use-callback-ref": "0.1.1-rc.29", "@radix-ui/react-dismissable-layer": "0.1.6-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "03cf5493575b48cc2d52536a47de643691443aab", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.32.tgz", "fileCount": 8, "integrity": "sha512-CtZZ4sAAY0UcSiuQQmxpQ/XO7k7YTQ/HGm/d4yCmXGpwg/AezPpF/PIU3eDMcu/zEuMcl81E1MpsXl5PTSOg1w==", "signatures": [{"sig": "MEUCIC6PQL0reeastd5frgTTccOkF0zgJ7qm1dE96/7TXIJ4AiEAgeNYpk3/DiKlQM/gef8zU8mmN/l+S6dskszNBYhonzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRhQ/7Bfu+/30958oI+KdSIN956oxuuaG68XjrqoyrvYNscpbcbOZW\r\n97N4T5wewWjSwJ44R6TkGRp5hYAGIu7gh0jZ7Lg05/mCBFqivzYfFFiacHW0\r\nN/m18uApawzNmGHteBPPhK4h19m2D+yLD/87XmsWuPZ6eUWmZgCBk95bqTb2\r\nXsf6i6rougeujnbNyCbk7sc9OjFZVHfMNTzF6wqtdTuyNFoUhn/9FJnEOrSb\r\nMqEe58gcPak4WX+/SN3K8MdGD7OUS47iDeSeJBi7t7V5y6xm0UGI9k2DO05r\r\n0/zGjI/K6tLtsK4s4kY8G7kOMeBpDCMUptdQkOYAilzLTrPWCZWD1GQH+yoR\r\nYA8SX75iN1fUsFFayYWY/KKui8cxr+GntmyDVqBjUzLCdGZ8EPRCVcw9xAMK\r\n7q2c7wKGLMAU7Cgb8Zl0czFOrs3cDqJb0ekCs1J0UUca/HSEvVASuZBIbNXj\r\nHisn3odBJOLirH7EjeR8rpj3wSnQmNmbHqRh7BFnhNCJt3wn9KiWR5BZrgi5\r\nRID8rZq/xiYM75eZlbwW2feigWyeb3O4pJQA18q1nvM4FLXrV8woc9pqXNaT\r\nWwD9Kv+hbvhlDMocmlXSz67UiATbfvqcT1K21VvJ7oVJVK+NXhiwT6/DfRZv\r\nGCOLJ4KDTbUu6NGQ0TJV2vB17w7usp9MjF4=\r\n=8QmK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.33": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.30", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-presence": "0.1.3-rc.30", "@radix-ui/react-direction": "0.1.0-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-collection": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-previous": "0.1.2-rc.30", "@radix-ui/react-visually-hidden": "0.1.5-rc.30", "@radix-ui/react-use-callback-ref": "0.1.1-rc.30", "@radix-ui/react-dismissable-layer": "0.1.6-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e817ac3feca6264019786f5b7092f116fdf09dd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.33.tgz", "fileCount": 8, "integrity": "sha512-aqUEwFHXV12wBgVWGZYJ8uZhcRopg7mmsj4tBRYD24VJ8spirQdnYqzmiZnNQj+vJCFqHFgTJ2D99ZHZl0DPWg==", "signatures": [{"sig": "MEUCIQCRvSPfWeOGtadrxzM4m9wC89j2yJhxBJj2qvhQPo+68AIgNhD0K8v/gTWl+eIAAk9zCY32oTC7znvCrK+XVVQ51vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/qRAAhXSaY4R96rQDc6BWHriLVpP1MBupvFqRvQz/xN0C4BEyvKu4\r\nGu2DYPdFRYKrW4kbvwVsWB5M90zJMdssfylj8+6Gs3wfSs1a1VGrvsp+xz3R\r\nwa+KmMhMDkc/OVGTzXvQZwrkqZWTzKsjhP03GrnTD6Li9+HgYGcJPk+0aZuL\r\njPOCQjhFefp0NsJCz/ect35Hu+2ouvORxKLkEnsW73gtvJt6cNBlgEf6obfC\r\n2ky8tYsAncmVirlyLq7nYE/YB9O65egEnDJxWwsW6HRIBXyu3p2LuIOAod99\r\nqzXZuN63+zQjpM8TW78LU0oebPIzM6NJseBKyD+6X4lBDF3bKiVIa4NfjrMg\r\n2WGnEShW/lnd5wqAIZ3iEFXpG7RgxemxSaJE3/yrth6GuiMcx1td7tRadQ8c\r\nsPWf8Lv8I4ellyqYjLYw15bRWCayDjhIByvob+z9S+zPB3runFn2crkWqCyZ\r\nGKipJlD8JxCIqGAbT06z+JmdTkPpsBThblwOvK8bXwa3xAh6Q1cF/S+HcRQL\r\n3mX0hz8LqeqffFmWe8M2+J/MFGMPNuVSL1o7pABoRroSW6yEkuje1u5jO2fM\r\njHfnWy4307ylYRTR+J/50u2ppORCg/JpcM8M4XQnuIP8OllzFpZtV0koiUUx\r\n8+on1IRF+FRijiUo/rB7dRwgHmP2w5qtEiQ=\r\n=SBg3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.34": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.31", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-presence": "0.1.3-rc.31", "@radix-ui/react-direction": "0.1.0-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-collection": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-previous": "0.1.2-rc.31", "@radix-ui/react-visually-hidden": "0.1.5-rc.31", "@radix-ui/react-use-callback-ref": "0.1.1-rc.31", "@radix-ui/react-dismissable-layer": "0.1.6-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "935a8b3a8f2219e442481cc765a7bef8878bf5e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.34.tgz", "fileCount": 8, "integrity": "sha512-IaQO6wZ3pdjY4CAinakrNBaOuEZkeiJflwNUKLM3+GCvR4xluKjJhZ0dnLX70fsyy1AQa2qKjmIgYZ1fT5j+MA==", "signatures": [{"sig": "MEUCICiznHPGrgD8VH6bEMk+V4+wYsnI9TVf6vUCGbbH/puTAiEA2XpWVBH2u3c8FdRwlJ9vkxf/hfCRePdq7s2bFfNTmRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmXA/+L9WKfQwTeBBhYfAzES25S1FVc2B4YXYOu/8DLRV5l7LRZ6EL\r\nofkz3/ouIAs+2iH7JyCCgOZ/VQJBBjdI/vslHw6VNqDTJLb1Tuev95mJQ7jn\r\ncwrDgvQJMEiYAFrGyLnaoKRPTEyJOYhR6JSX/rQcczlinH0ERWDPG8sNRt/1\r\nATqheN+ZEbzYFjVWCFGfurulduOmmfWU8LrN/CT2GXln7N0RLJNBjPZmDntY\r\nm/x5aKN+0yxRdNScTqTMCYTdqgvAvkjYB285qk6bTjog1cFQoI+OT9DSbTei\r\nW8L+MkVcwtAsBZnb/Q6qrqm6mgURtpySRnCwxIFDsDaX/pzGI0XUFM33y1KB\r\nEQkIiwB/mFmoGWpA7lK1yY/8Ax+hAGOJ1+yO7vlBoxKLrPl+IM5zMB75eCGn\r\nC/wqzfcjI8ptkfMF+0bkbE77SMtUR7wWeaGXw3TPhmoIj1jhQ9j6/v/m81J6\r\nf5fHC7uUzPYUy0U3Gl6fmeAx6IxH4mwTzvEqhoO8xRhquIaJGWqYdr3SkiJV\r\n57LpVmHEe7HBbTG+FIL3tAp5E1PX4GUHSSNwdYkfCJl1eNizzhNG4t+DVrq2\r\nq+z6esRdC6OXE76BNxTOfkjjIYDR1zxIQB1XYKDRE5BCqZO5Q0MvT9186Poo\r\nTqzXcpylmbvOkvKj6vuDMvWZcKc8g86w9eE=\r\n=c2UF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.35": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.32", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-presence": "0.1.3-rc.32", "@radix-ui/react-direction": "0.1.0-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-collection": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-previous": "0.1.2-rc.32", "@radix-ui/react-visually-hidden": "0.1.5-rc.32", "@radix-ui/react-use-callback-ref": "0.1.1-rc.32", "@radix-ui/react-dismissable-layer": "0.1.6-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "650aea1e74718fe71b4a3789a158d376116fda46", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.35.tgz", "fileCount": 8, "integrity": "sha512-kH3LZxxnS0JmKkzsMfVJlRa6E9gRIf6S3e2X4kNIW8QJ4A2vqdEvAFLWsSsbYSbxyrs/urD77C2T+Y7Fy0SCyQ==", "signatures": [{"sig": "MEQCIBD/RxjID6jU0YmCqjm9qbJEAhhmlYA5qiYj/4ntTfnOAiBf7k20BCC0j0CYD0Tk6Z/RNr2apaltgxnehsLwuRjY+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpdrw/9EDRE9vv7qYAvRAbBEWdoMdjCuFItnYtwSWfc4MHWnvdP/C/e\r\nOgC5VDIFtpdYDmCajrLnlsqoMyS8tXE5VnEiJJ57tJ5Jk9gbc4Kzi8+F38Vm\r\njamPo+2HAPoYzzb7QrxQwNex9/XqOJeR0uZ9LSYDltXOhwMYyFN4nHraps+V\r\nkeCnKpcxzOancvQQsC56u3gWmQlCdHPYYgjR48PUto2S1BcdKYDPI6w/qIzW\r\nTZUPyasVTjKk2dSWfLGljXNwvhvh8PpK4AvO6hPqktzRv2HUvOVtRxVoII2h\r\nNJI3l2E5TMi+EPJ0oPTEj66ivmfObKADvyusevQ+D86Zp/gnon0EYJYF7c3B\r\nsKQIdXGmfjTQ2QAngOkLaLpjNKmHGF1ouuZzP9ujlkwvVed9A/ojIhe7xomJ\r\nat+NY1GPLHz3sWurj9UaWcPjd5xUnWFyMJnBz2ZoT7o2BF0+sbesyOGyAuHa\r\nj3wuMNAuIHUJrMzdC7yNiuGEDJi09WKCZ2Omv/zYCGyfO7WAZ+JpPnxlU4ym\r\nbyRroWGXdZUf6/F64XkIYIUgCHa8bQ6x7A47e/evP563sbyCtyJb+bIqX/rX\r\n7I3+w8tboc+18SFeAE0Ku2kQ5HgNG0NUBnkpTCAUma4/lcZ+2xHya93WPrwd\r\nybw9nYztzDNDXo/ilaJyTAJPqR6OP+6B310=\r\n=V7zn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.36": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.33", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-presence": "0.1.3-rc.33", "@radix-ui/react-direction": "0.1.0-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-collection": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-previous": "0.1.2-rc.33", "@radix-ui/react-visually-hidden": "0.1.5-rc.33", "@radix-ui/react-use-callback-ref": "0.1.1-rc.33", "@radix-ui/react-dismissable-layer": "0.1.6-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cb641c7274215df9d1193bdd115ea1f4b25f454d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.36.tgz", "fileCount": 8, "integrity": "sha512-koRUbjyPF0pfbE5xsmjPjDSLfkP+UJHAUUX4p2pxPi54RwvrIljDgOwkyuvLXJ68lDpic+H2hPac3mNcPa70VA==", "signatures": [{"sig": "MEUCIQCRRATWAedj7pgT/JUrlcyDvDj/9p+BJtDpGgLIF/ouRAIgcHVo3nqZ/B4nwOCyksfpE6CgHHhFTf9dxjpEr91Aie8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTLg//Xvj5adkw8HBAuGlpDPTprFcZpu8XoWSrYP8Q2abruFA39tSQ\r\nSyn1jKLsTZwEqAw2FzpIoMo97zpxRSAOti2DBmkVvb53RhaXS0/m+EhjQj5E\r\nQ77V+EwDdXBGxGqRpwTtKAwPR6C+q+sUKeqp+49YiUA/c9K/UHqRSvnUuAnp\r\nRvL4slqWGFrAWckCIX1piHBDDpXI97sHc6WW3EVVZQIC/wvPV449Lz3dU+uh\r\nEBl9wem7sopPqFfJsIpsn+vlu7FFsrMCHCL1kotcafz4P1GwAsPYB3BT0upy\r\nUw9EXED/ZXLMan2j0cozc5ug9aIe89QL1CVsC9vUoh0bzkmLBGqCI9bRBUKa\r\nOCupfWAz1phUrar8A7qqiGwzSPgbJqK2bjmlx7fIaSScE/m3t1hmFEWsuSg2\r\n+57I6sHvsjtmIl3RnFTwEXwZsfSl1cNsUL486W0wjOyhWKcJG4wSJR7+kz9S\r\nGclQjdfC2C3eTqTN8ZlrzAq3VTXO44j6mTy2ifqyUiulh9wSCMVwwI8nmlbD\r\n8zAdGhS15Y39M4/0BUYMgHA73TS1kM2bpVj6oGg2zLrH+i4znniTgnJ0Hvqh\r\nseQOys+Hawc6aOzz3WA8hQhg4eOhLgZsP7/DV3+bJJz6BXjpLgrJ9VIimIcz\r\nPuMtvPPB65sazCzXz/E1yYMXQW38WOYNJBU=\r\n=QUqg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.37": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.34", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-presence": "0.1.3-rc.34", "@radix-ui/react-direction": "0.1.0-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-collection": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-previous": "0.1.2-rc.34", "@radix-ui/react-visually-hidden": "0.1.5-rc.34", "@radix-ui/react-use-callback-ref": "0.1.1-rc.34", "@radix-ui/react-dismissable-layer": "0.1.6-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bcebbd123c45440756a725bea974958d4904ed7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.37.tgz", "fileCount": 8, "integrity": "sha512-lWWzsOFFkm1XWcfb1LkLtsD3tTkE1IoCEA4y/sWycGszRO18+FDowHv4ZhO4XTRZYg0zMTzt4EkNNgtGwaKKhQ==", "signatures": [{"sig": "MEYCIQDcxvsoobC7Uxj4HIiaxDBMHKCrcXKe/hRCgT0UGZsZiQIhAMntNy0foVmrNhvjeDPjg/xMP0pQtjo0ygr/lc/wobr1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAjg//TkyXn2dUyyVeyYGfx0YNFd8fX2JQeqtL3CHQ/kzzYhQ2XpEp\r\noBBDjsQX2Fogb+jXVhnlMy64IduZIeo3Hlz65zrF1LS5YWxq0CFSC6eFS8Kl\r\n3DOuydGvFWwRNu47PSTNtaeVyq18q8lsceRy+wmkmBSFBoo8plt27S4wQiD8\r\nR0/q6NtexRNm2pPS8hzGo2QUgdjB/4/Uuab3YTcVvGa8JbSLIrZXrePl8J6v\r\npr7TNEH1wRqs/Krn4HqmbtaSzbC8oEQSlB4eUEzv3UV6eKfN2pIxM3stEwrj\r\n8/WQMeglNrVjeYhKrZIjwQDaoWdsHI6JqOrG/iTEzHjmsry4CYe4y4+XiDim\r\nqVYj2qfQyWnmzYoZFsbiE05BnvWzjmtqx0rXkgE0m1nZm7nDAVzTSPKeE/+j\r\nW+tjdlywYTVRjzgi/Z6nzwVEtmpYjPz3CnzxjHSrcgxxoqIyKdTR7rSGFEDj\r\n1gJvav8rTr6I/pIIjLd3cYTzeRUvTUHVmO2HyPBcviZ5YTBnrKRodR0phPTw\r\nSmjGP+84j9Kni+9Jw/aeBHqiFZN2A0CWpWXQ4K6lKC2/Nsi2VJYcZTO+ECkS\r\nm2wCL1w3odqJGLUCP4Sf5lP7x2HN6K1v1v7GfA+3yvzvDuvzZpv0hT5IJxib\r\nn7SyD4Fbl64ccgRFgI8fV5HIbRg5//ZRACo=\r\n=EoNS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.38": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.35", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-presence": "0.1.3-rc.35", "@radix-ui/react-direction": "0.1.0-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-collection": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-previous": "0.1.2-rc.35", "@radix-ui/react-visually-hidden": "0.1.5-rc.35", "@radix-ui/react-use-callback-ref": "0.1.1-rc.35", "@radix-ui/react-dismissable-layer": "0.1.6-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6da91df56bd32216a5542dc04b3c588be4ba8f59", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.38.tgz", "fileCount": 8, "integrity": "sha512-EElF1arMtzp5Yr7nWbsjQBOVtBOVs94dKF4kjOBD+W3gt/ogaJTFIlEuPuatHTjBfJ9qRosi47f3ncz3e5nBKg==", "signatures": [{"sig": "MEUCIQDsTULjcGZ4SalYGX58a6QvT68LVaeZ3Cs7tOSh7r32fwIgMYUeYD31A8jVqXQQJtxoXPzNGYBY6w3NS118B8MT6Ds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsDA//Tvqm7quKWH2MBdVj+LQmD8zx2b3NnOs6X1icxqzdjL1kocyb\r\n3dmBAFWdE3ZggHEq8ERMoCJaK62VIcFLx0pHAgpsH8xkwaCJhwfR659YJ5gM\r\nITqRlYvNk3r+R5acwjOQAh3g3Iv7GMVY4nIPjCFiirNT5ItxJNkAPxQCicgo\r\nkBiyQ60o0SgXRY5l65bnjJ5XvQqtX5tF5WC4EswzUOdAzFD/mqQ+rO09Ryl6\r\nU+Uq/NBtjFmy/hEy7Sj8LpPpwdA960d7XXZXBbZhmWLYsliriRx03oacgqD/\r\nPMI0CUzHYrPp/742B/g2JFMk8lpna6QftfLcZpFnIANrFowipEZcrETGAaAu\r\nZ+u+w4jX8y0MJIA3JFTHsZiCwEYfD5I1OgtHjBAHRLIV8vPgoK4z8yaF2h2h\r\nmnz/8SnSv7qE7+AqA8j1apsWuNRAK0rlQRfK+GWs5HRUBuTeHYzJ7Wnwk6/x\r\nP/7V0wPgf9tqfQovoqoYNhqfndEvs8CaknSUNOh5nqDVAPqj5dcrCeNGP6RY\r\nMcJMlP+ytyjNiON2saIIV5YtIR+Y1rP246Muos5hAKKL1wNJLRymizVJ3E+U\r\nwnAJVTVHmj7COfbX7g60FjoHY1kxO8J6xMy4kx/NZEPnMKbxq+NStZztJV1w\r\n2c5B3u1yx7lJFUrnEH0VSpjgK5caczLFVHs=\r\n=Rq27\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.39": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.36", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-presence": "0.1.3-rc.36", "@radix-ui/react-direction": "0.1.0-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-collection": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-previous": "0.1.2-rc.36", "@radix-ui/react-visually-hidden": "0.1.5-rc.36", "@radix-ui/react-use-callback-ref": "0.1.1-rc.36", "@radix-ui/react-dismissable-layer": "0.1.6-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f467da93e322c0c4377a4789372e085fb3075e18", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.39.tgz", "fileCount": 8, "integrity": "sha512-Gn/dIItatMGrPWcuAffW0UL8XtSfNEYVJ8azDHxlrPjafX6NPut3QyJfx+qRX3TqsfzZVoEOi9U85zbsndvK2Q==", "signatures": [{"sig": "MEUCIGWbITDsDPGgHOnSrF4IoKzwifalLOmOM3KYyp7ULbR2AiEAu3ETxy2Zxaej98dBgSisa04K8CDsPfx2nt2yW4osIl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx+Q//e6/Ap8nDbBhdUAqJnEP16KfvVhHfJZhX32WvJDBUyJ7yBew8\r\nObLtV8Ohy0BSLdKmjiFiPV0BwL0Emlr2ChwrJ6DJfwPjSRU5X63RbkI4GBmD\r\nNO69YRiCKsApcjwoyC2ONW5YxjWA9RLo5BcUynekRKhiJzjTxXOco5FbrK7I\r\nnk/1hb3YiW2hCqXuAJ7bCL+3TIMwd7Kw+AYTX7COnE2aLQ/U1Hn0XYIRchow\r\n50GljOFIVixyr2TAsXW8N6rTZMJ1LrE7IqeOVv6JdwS5UGR08regKLsrerM7\r\n+PaM/EdBcM/ISrFhAJvHPY2kDMfqw6+6GLCAhfwltpBHycpo9CwqHWVbvnC5\r\n3/jiZWIW92kINFUQ0xhs84hDCCwxwumZcv+uSX3uKGR68koFyIlv6mhirM2b\r\nfLXo4AkIId8Uc0U+7rj+q5t4LIje6A/IU4UUw9aK3H8fhLm95tVIA7gyYORD\r\nrVNC5PZWE8soE5nojuS4j/Vo4tkpXZB5OAZnLPRZZMvzlkXBKeyVYits2Kr1\r\n827TFwZkXcJlDtmPSdRYi/1n3alahFV1XhjeFunaJ4r/VyCCvJL4wo74csCx\r\ntywaVNV2PDzOnVF/trBhp1RksMmH65MVLnc9yjlhnycpklHpVfkqbaS2etTE\r\nH5GayVEoZmK/StEQyWAyMdaw/Rv3CzTxuek=\r\n=3QJ0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.40": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.37", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-presence": "0.1.3-rc.37", "@radix-ui/react-direction": "0.1.0-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-collection": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-previous": "0.1.2-rc.37", "@radix-ui/react-visually-hidden": "0.1.5-rc.37", "@radix-ui/react-use-callback-ref": "0.1.1-rc.37", "@radix-ui/react-dismissable-layer": "0.1.6-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3c6416849d7784fab1c37244df98de3f763b8951", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.40.tgz", "fileCount": 8, "integrity": "sha512-gPe/+Vkws0uM5TX0dyRhqOxyglgV5SIa38NdH5mtCicN20u3rKxAFXGvraKzkpoteteQUeWlLY8ByiEqW1gYRQ==", "signatures": [{"sig": "MEYCIQC9sNB9Zc/ScwSokjK6HsIDsoZCuPdl+N7k3bHfbU1MWwIhAMKdPb4K2MuSu8sYzIire3C06v+DoRq+KiJCjww3Lqrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0n3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ+g/+McGy22ReBGJR8fMX3lRIcC2a+g1Dzpb8fHcMe/0tCTfQWBRZ\r\nXz6h+7XcguEQNUmwYKNyMqRzIWq7RTVsP3yVu6HUJvH3B6wt7yogaODhkPGD\r\n42ei09ttKn/Y6VeYkPUv6FbXJNjD2rz1jBk4IdT8bKl45I1EYoG58ndA8o0S\r\nHaFbp1GCUFju2ZPt7d0LHShFbj5mHEYjjmIfreg4esLjHIUKH7Gq+yQWiyEf\r\nc3phdlm7Cp/Ui8pNiHWfAkasv5KV/tJQnXyyB/CUYsQNW3ddF6GTA3pQ5sQv\r\nR1Vf9EK+ZRRexzwnlPfvPnc9dG6M76Y3tcu1jnoJxvNCv0mG5EoV7P2JihYi\r\nsenx1a7Eoxfi52ssUO7Tz2qh7QgISha+Bn9V0UxBG4+nsg2YtDi3mYviD7L0\r\nGxrgDiHYEucM7++0BTAnBranHq6ZieLstYyGwyfINQqUoRcn0jW7CWgaEvMk\r\nMXf3KCm4KyWnG8t1wCz917Qi3iaWC8uYe/OequN3VSSzTn4y0ibl3zWhlWas\r\n+RI/sn2B4bBpetJ72Uk2dpdE7tnpzP7z2mVq/VaLRdT+aiERiENhg0m6/nfw\r\niWDWwFSrZRFCg1Qv/5KkBHayF/FQZc4xgp+BGAvb5pFu9lSStswzF4iAJWMV\r\nzWrTp5fDgDjZJe46/8pPoeRJxRyVF44xvEo=\r\n=Ti6S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.41": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.38", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-presence": "0.1.3-rc.38", "@radix-ui/react-direction": "0.1.0-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-collection": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-previous": "0.1.2-rc.38", "@radix-ui/react-visually-hidden": "0.1.5-rc.38", "@radix-ui/react-use-callback-ref": "0.1.1-rc.38", "@radix-ui/react-dismissable-layer": "0.1.6-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9e79c5a6cfb5b976e10ab3dec52203558ff80d30", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.41.tgz", "fileCount": 8, "integrity": "sha512-lJ76gbkk43gfdDYO12aGhjMIFCCKnAJ08Z8mIvMA/yuCcKJpYA3YA3cYp+QcTlq4QO6k4b4YgbBKDshqizOigg==", "signatures": [{"sig": "MEUCIQCPTltxTpm3Atso1GcHPDughoZAYE8lvlc7rIABuS2jKAIgJw6FYdKCUq52fIKnqVoxy4btm/BymzoSms7zLuDTbN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzp4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoG2Q//fYClnnPagExdaiCmXAzpqrqvyReih9yF15szqAiPgBoOoV+u\r\nq+OouFiwczHe3qxpv+iTXh5u5G5ERXPNPL8nd0lChGFnA5Qwnkm1swxZ3Zt2\r\nISPsYZT3BBlznhdE17/5bV2YKz2/8MsvcD65e79FPaH9Rf0havBcgrWnqoaJ\r\nmPzKyDhNGhv7x9Zg1KpBluFE65EL/08kDBc6uu7YAbW48we5VKzjL+FrOFq1\r\niOG15Q/tRVLWdJRbVBh5IXO8wyLRfTdzM3pXq+Gu4bZ0j6SLJQhAMQPW+LWH\r\nTafVVAUXvUlMvyvCLuv7HwwSAKeN6AbsHKY7ATIIc8qdyCpOatJHWJTz4oxD\r\nMjcr+9jDn7iG29SEdTfeFUAr0F8Zy8cNJL0jm70uOi3/FNRZ1gNb5fQk5gzu\r\nDfOuhvdNO574abNIOsJAbt6WuJPYZxfyZVq3yYX0Z5Ah5PYXsDu0tySybEbR\r\nIK/r67F3khAu9wQOMxdxZEwxDRQ1VXEfyi7SrfAqIcV41tRAsnxBKM+3BaAA\r\nwKsgNxrCB9HEzK5qGOx+QRlizAhh6elsLLA8k5FUvSVeYxc+ibGEc280sJOk\r\nrM+VlY0/dogP54icFjkjVTmgcyy9JypOH80FK20N/aYZhxxgMu6Y9fQAl4Of\r\nMq1rBtvQqexzo3zZWoTtC1aI8wivFQvuC5s=\r\n=IYnV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.42": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.39", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-presence": "0.1.3-rc.39", "@radix-ui/react-direction": "0.1.0-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-collection": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-previous": "0.1.2-rc.39", "@radix-ui/react-visually-hidden": "0.1.5-rc.39", "@radix-ui/react-use-callback-ref": "0.1.1-rc.39", "@radix-ui/react-dismissable-layer": "0.1.6-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a3aa3e377a88c192aed7cc3a45b4f29be64eb3f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.42.tgz", "fileCount": 8, "integrity": "sha512-NaFBlytrTqMUKzwYipTAiImq0dPqPPus8t4egUVwjmmipAwegXJq0V4krwlsdSK1r79easbrZEZdRkI9ZnqQRQ==", "signatures": [{"sig": "MEYCIQDoJ4/JWM+CnNfU/edkjXlp0RZBlM3L6vFeoORjuZ2fgAIhAOkE5rzYcXnPJA8vwrXIgbclrp6sC73ETGGnhPMdic4a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDoA//cRLurFAs9LUNyArTulnrcibf13iyiqSyCH2m1nxjhEpWO4LY\r\nSF08Nw4bW7z0LraaqRx0H3SN393hM8nHD6WwA/gw2E6LHRxpEGmzh5BCmb+8\r\nQEfaymo63Xh6RWc8tZZsISKurgj3Msw70ByxHYO/EfUe7qH3gZXgU3Ds9c06\r\nc6bIMAXWKwFNaSEoCY9QTjo9oj6AjM3m7XiC771/oWMwbZOTzlilBpJ0IU8T\r\nQYWLg3zwVlJLEN0OTHGvaGflmpEC+aB7Widb8NMM8WYj6ZFHt/rBQ2dKkI+f\r\n8PE8fSX1ibJqW8w1p90WEKYlC31DBHMVExCqShoHak1COLU2p7pUI91Qe9u+\r\nIkC6yizGIdw6sDu6/FCgTqo6YoJdCRXt0kPE837uqTmbN/nfkPT6ur5Gnvhe\r\nZtHYiYGzk3CN814wOAPxpQ/0i4XmIcnmcQWoYr6ADj7mVD0y9msJK2W+M68a\r\nwFCUN4lXAdzkqMzuodY5zLsafdahaY3XHIa+y4lWn6IXsfDcm5FjiSw7HDgT\r\nZyEn2ZCWUXflboMGMRdRzw/pE/9hx3fq0VIfCU6FbPKDjRi+k15TEcZFxZlX\r\nJCQVUh6LKEDt6PCRO7X5+qMq3mpUEBRhXuhYgc2Q7HfNbw3qBcyNDODFY0bO\r\neW/PtHd/f81+GAu21Mh3mz1yjVCBGPqEhzU=\r\n=+dJT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.43": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.40", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-presence": "0.1.3-rc.40", "@radix-ui/react-direction": "0.1.0-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-collection": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-previous": "0.1.2-rc.40", "@radix-ui/react-visually-hidden": "0.1.5-rc.40", "@radix-ui/react-use-callback-ref": "0.1.1-rc.40", "@radix-ui/react-dismissable-layer": "0.1.6-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "961759409bec7507762d8eac04c1f041f9ccdcda", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.43.tgz", "fileCount": 8, "integrity": "sha512-yo1zQHlV5ZOJKuDPkdpODr+JUWoq06SMzQGLd9fe01qI3JN5f7ov0h6xbmw9IqOFV8F4Eq9Vn+ACb2ZYZKUpNQ==", "signatures": [{"sig": "MEUCIQD1HZmuyCaIlZ8Z5670KN3r78LEFHZeS5fN4DPkAUQrRgIgbTgeASdwGcPOO/7F9DpCPffGbmDRsyfSV7ExqBYjYGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0V/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMUQ//YEY5CtyrQAmzppBplf3XzfeiXwzO43R6mO7qA1TfwJULtp6e\r\n1dZg9AORHwEchayaAQ9yPLLRYtuJNyDDIsRpSHfDTBJnpUCDI9+11t6uDBRX\r\nb6KL5zPXXu7meuU83dXClHT+KetF4hBUKlAn7+tJTrUumIgH8h6+YZMUyItB\r\nidXxVyNxKuJzPLcMKJPWwLuWg8XD7Mf5hHJSlrKIYpnPpWIAaFnpVUkZP8tN\r\nax4cpt1o0cAK9aCSLyHf4CFMccFUXo9w1CkkTDHTSnH2GfrVLKd+y2WhRSEb\r\ns84ROlgckYwJ/5U1icAekFuWVw1G9SY5QKl0c+8+8ZVnD3+cIbWTVfWzAtwy\r\nLfL2FggURz/nbVV8BnMr8/EgDtLtaFZI11fw+lyxqcAJrOD5E3xAyYd51LU4\r\n6nSZjZgSDZFK7je+pMaF46Cdeoyf2/QWeXBWkFZUo0vCf8RfUgA5SrNEiafp\r\nh7MsPkBN6f2c5Xpe4D3WBvoXcgUYt6LZgro0wjzglBPlDCc5XDiR5NZBlYAE\r\nq1zjC1gPplwNjkFPavi3jqj4CpAAAiu2RkuhrXQD5FV70X3++/HAfT9Usj6h\r\nZe6GG63pHjmLoVpackN0e1WMfXdXRn++9D740w+yKiQ08g6oGgG6m3Iu7R5N\r\n/R6lbkqQ4KHVdVzvSt7WSU+l9qGZcp3p7o0=\r\n=zEx7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.44": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.41", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-presence": "0.1.3-rc.41", "@radix-ui/react-direction": "0.1.0-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-collection": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-previous": "0.1.2-rc.41", "@radix-ui/react-visually-hidden": "0.1.5-rc.41", "@radix-ui/react-use-callback-ref": "0.1.1-rc.41", "@radix-ui/react-dismissable-layer": "0.1.6-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8edc6061ec33e306a832151051e8da7417f7bc39", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.44.tgz", "fileCount": 8, "integrity": "sha512-fESMyBuJzZrkwuXLLjr8xc9zxDyHtK2dZPm1WpxkOEaCNpfbf4csC/ABXU9NPiYRGjovT9uTdoRDKX1qcjchYw==", "signatures": [{"sig": "MEYCIQCtgyOoZ1JFryXfn6FgJZNTQ3ROxQtHZeR/NK8UcncpOwIhAIyhq4SQ7Ng7Rooj1jWl84Gqv8Pu0atSby/DxiqPI0lE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNQhAAk5odkuTPlBWN5VZV4mkbNXmnObBPHsrpNwnH1jsy/+5CmxKo\r\nEMLopwyE7jVPC7oiFpKL7jXpQxDNZfIR6oSRcs5qAZTLyWChr/vVi2crEX1L\r\nXZUVIccIXWthW6JSSrEafc4N2ZKGE9itnMSrfYrtSTuXdGJrcHkAk6xlP8u1\r\nJISmXYnLgZyTNKcdcf6gJR9jWpick1GAJoiSfdvn2na2sihOvnnWcQTqNZIs\r\n7nuUl3N9RJ8J2I28SJ5TVmFhHk0HNeMLlmhQxYP/RQGSt7P2ZuZCn3kNTT6B\r\nGvxywhnyaoy5XhxqWK4Ab8chVemDz5taTbsC5kZSaTFf3FpCO4GRm7M/I/e8\r\nT3PgiPeKEthjALTB/VII+J0Rr2n8itv7Gi053UQQ/05FFN88VA5u03HTNs9c\r\nWJaFKoGaA0XPncgDvvBcNJ6MqiRvIzDrDp5nKwEkhilyPsBF5/e6EVPJAe9X\r\nOaJBC+Pi7vyygTH38D67xaZZ308c0D1zp71MrPii8Lhm3VJgbYxGLqJ2u+mN\r\nSdpzLbVeFB4nGgDz4du33T2OD5XDcPfLMmZJHOeTs599TEwHpIJaZp+iq8lW\r\nSV+1ntm+u1O7RkFHw9fp9N2kcgUGCxianqYaP4BrApSt3h5i/Kn++fHER00P\r\n6tDAA2I/XpKhJJo3dzrEEFzOKr0CEMkgts4=\r\n=UCFr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.45": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.42", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-presence": "0.1.3-rc.42", "@radix-ui/react-direction": "0.1.0-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-collection": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-previous": "0.1.2-rc.42", "@radix-ui/react-visually-hidden": "0.1.5-rc.42", "@radix-ui/react-use-callback-ref": "0.1.1-rc.42", "@radix-ui/react-dismissable-layer": "0.1.6-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2c4a365beab4a26af538f6234c45feab15a52402", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.45.tgz", "fileCount": 8, "integrity": "sha512-B45iyFxQeEjfO/sH9/w8chtyFj6WY+3k0zfNytn1ECJ9EkFfWjj86fAjmjSYYmYyLow3Qphw41sZylE364IjIg==", "signatures": [{"sig": "MEUCIQCpn11TByOgOQI8/DsDAl+mFF9kgsppeULuOGUNVQsAZAIgd1ohMz91SSucVo/JA97p4qvnBMEimuZ9VNRQVWGLkRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvd0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4kxAAlkghRrYeknVfGII/i/vopCAcGwTRhjIVOTVCBAummq6QHR/k\r\ncszt30qlbklmmjeWn6KRor0RzbXovF+6rKx6IxPuxc76mkZsRc3CMlMjBMFJ\r\nxq4jc1zbOpwrYo6LfYiSN340uUF/7kl9Wxmx3bFKBHHUBwKuJiCZWwEk4eWb\r\nnSlmnqkZAR14PAoBzhhiGgKfre3dtswzfZauBTr8fOlDi87v+7593uhryyik\r\ni/67nvvresBcbaFvghryfZkg7NqygdwYAvJ1sE4tO2bUwbCA3y8ERzqDuE9+\r\n96e9TvSXHy09L9HC0Fj7G3QOfywoRzNF4g8fH75jnRo4kK215nrelaA9BHJQ\r\nB3EgXvM8mTUogTxeu98i2LmB4v2rDaaYBvPyYY7hQ8VQ3BBIZRWfwhn04gaU\r\nUU+pEkcOMANOjckD7y31xyBoIT4zG3qTbTHb3wWQ+knoopOSjfuFvReDwZiq\r\ne89W8aO24KyH8nzYue1QI7IG/6Fez7kVy/hY83NQvZRdXio2SsbxHhP3anBR\r\n54ZHy6QJ8/ln28WGJ7ymNljkOku+K0xEcP4Hzq3740qY0EH3jeqndQ+DJw1S\r\nHBgDHDyg4P7a0Zxt0K3GsDsj4Xz0D0k46WKAaeJmXr/JZ3/sFeEGC+nnArdi\r\noYU88btyGkOG9coJ6Zt4i8VwftRYEmtVcco=\r\n=Qygw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.46": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.43", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-presence": "0.1.3-rc.43", "@radix-ui/react-direction": "0.1.0-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-collection": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-previous": "0.1.2-rc.43", "@radix-ui/react-visually-hidden": "0.1.5-rc.43", "@radix-ui/react-use-callback-ref": "0.1.1-rc.43", "@radix-ui/react-dismissable-layer": "0.1.6-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af02ede0443ec05a976b917fc8cb76b829c709a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.46.tgz", "fileCount": 8, "integrity": "sha512-/TaVUe8FxQg2ALpyNbnGAU4n2qFikoh/Tl3kIeEIbqmmu5s1Hi3w7o76oWTk0rL396rnLrwS77Th+GUaCB38wg==", "signatures": [{"sig": "MEQCIDlHpMwOIIwCUdw1C2YwxuyCJFapbujfBXI+gII1G7iiAiAxd5xLXvvuPv0M2HqAk37Jh1t974Zo/VP2RoKJggcjcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZaw//UrShtoV1S+XAJwAYFJFElfBij8ZHcQcryTlJ6qRwcJPBkSBd\r\niVKRvB+vMAfn0cPiP67RUEpz6AUfQcXsXSPMvSh542tYmWog2eabH2XhaWNh\r\nYAnHBLZVfxQ/epDeDMm2Yiyz1cgB5yxG82ZhvOpFsWCP06xug+zH5JvMTvAG\r\njSlNKgwHYi2PFi0bkjeOVW/Hivv1ri0+eCyq7qnyNDorwScYPL+KNi0cUem7\r\nEJk5zMB6+spwQUAV/FpeqOH3BHeiyRvLM23JVQBIgsVTYMXr/HAdBbMRKpT3\r\nzjN1TDjxnU9dSELZBPbTFRLlPL28/Iq5k7ylyYEnp5eWTZcWg9e6jZ0t/oea\r\nJZPj2f/uQA2cQMGNbZ32Qbk3vQuJ3426QV57uJveG+2bnQK7TMUe2V23N/L2\r\np+hFbTJ6lYSm1fij4uaHA6VTS2Wti7LvWUUMPjKHLQLpk00qJLXHil2VxP5g\r\nQWly22Lr+JdMPOgE1iXrrrSX/3h0sWg8PkoyG3XoffE8lpDla+MQvDYb6e5c\r\nakc1bXPMosoFGZqpO73UwYXuMLisnTEAyHdRbqQowCfc2hvHjixeYA9YZU8H\r\nli3VPycT4eot1dUjKl0KaLqmd2aCn5jBdBy7qwlHFyIzaMLGcs9zTPeWaxUb\r\ngsx/RsSiQf78uSOMxqT499Q1fTP6uFG6AO4=\r\n=3REK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.47": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.44", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-presence": "0.1.3-rc.44", "@radix-ui/react-direction": "0.1.0-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-collection": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-previous": "0.1.2-rc.44", "@radix-ui/react-visually-hidden": "0.1.5-rc.44", "@radix-ui/react-use-callback-ref": "0.1.1-rc.44", "@radix-ui/react-dismissable-layer": "0.1.6-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fbc97c4fb5c647eab269c53e08d03513741dab20", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.47.tgz", "fileCount": 8, "integrity": "sha512-xODBAVGfUQPJzzwPRuj0t1ijgEtbhyXV8a5JpMo66x82K99TNAKyS3SDNKVzeFWDEyDoL7cuw5jO+j6qDjoqoA==", "signatures": [{"sig": "MEUCIC3O9/7uPjkPh7XGS8/BjXT22rUbnPXLMGn1ow/GyyFVAiEA9JLj+B4o+QUelrM9DvRgr5oXCfwj93boYfmSeyupst8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0BA//dznp1tjgld0J17PUHbx52pDJjTPtUuNyNiNeJdR33AwHuXBt\r\nL/0NMROe9BYIuSP2MNHW7luXUSdfnIi7SGhTiuQfEz50Q514WVeFXW0sPqGy\r\nVqObSrpTmmkJ8rn3ABCZEX8n4UCmIpLhFVwnvFz+UYfF/Ov2/7f5amXweyKV\r\nyCQNkpfljh3XYezMR/o7fo361hzWpujnC4i1Jdk69S1Maxz8Cw4OeeB+FL4I\r\nZL9dDpIPYZ0jxVl+sllGyh47xDM9GBkKtlagg9SYRsE87t+S9WqbssqeUfg/\r\nkeohJZ4Nv5zHnjGJpEe2VKM4XExjz2z5tvVk5V9SnAeEsL37axIQcAMKC4OK\r\nifY2ZyhfEIH45xAsSnxwxBayDmu/g8PQZ58QwdDsqM6VG0KPlJwcHVH+5LRy\r\nUF7w0DVgAD7pHzTSxk/Wdhf7hFuZfvh+ePsr1PQxjUug3TkfjeOhbUmAe1+i\r\nsSV6ZE9v2u0bXjb16GOc5tNYfmgxpr+KYrgTDJ5cgKwzSiHYUpzfSf7G4Y9s\r\nahtl2c45YxUSQ0Ygb3G1iviUEx11tqiZ+Q3xeeQ6Uz8e0qQDDFIxnVjpdLgb\r\nFcXAh2N67gF6vVloIewYjKuD4UIrS0A1aSM4bYXfPaHVLcwaGgYYMqpYWDce\r\nFMD4Hg5BLHBapfAlJRHTLe/PWUYJHb95LXg=\r\n=j24n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.48": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.48", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.45", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-presence": "0.1.3-rc.45", "@radix-ui/react-direction": "0.1.0-rc.48", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-collection": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-previous": "0.1.2-rc.45", "@radix-ui/react-visually-hidden": "0.1.5-rc.45", "@radix-ui/react-use-callback-ref": "0.1.1-rc.45", "@radix-ui/react-dismissable-layer": "0.1.6-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "02075f6c211c49b1c870340f5f3c67f51a5b762d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.48.tgz", "fileCount": 8, "integrity": "sha512-j//BfDYJB8iRGSG03AXAuigt0EUgKbo4IZXUprmuFjMSyUU+4rahnfpPAU4d+sAswd32nECLbVwRhLB3NdWwtA==", "signatures": [{"sig": "MEUCIQCVuA3Cv6MOigxdBIL6AkHfMnGvETONZg9v34MEOenpvgIgQgOH2FPhSpCdAzKSunpBpL+JAFLdDbSX1PVTIUNQMiY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLTg/9GJMdbJ7zUl2XL/5SAp05TtsZn6qWPfZNoumh7jZyQEG1Qftu\r\nCGFNP2wNs3JCbrIe9pUOwnPtXGa0totDDkWcYH1v8Z9bX+gLI5iA7ykNXrpA\r\nD3QV+zxOyFpNR+D/b/C/E3Gu0VZ9uxxDnnDr2Dsn/F5pWvy73N5ag0Pd9oDn\r\njjANs73n/iK4XiGXnqfSJrK+tivv7SWMOYAV6FZEPoiyOWf+Adf4E1D+vzu<PERSON>\r\nYnletuT9N3NzNv3poLKMKFZVrdD6iV0I2Ku7qnDgj/mxLtG/sHJ6n0VKID9J\r\nPyUrixtmQJp2DU5KdFjZ6NIkwOx8a1eBo+10yuyxtGloUGxQwAqNGgiHpxcv\r\n/CnQLARdbidjRz6TM+LVB7kC0qrEoUQNG+rNjmvQ6Sj4wrvmTHCHZpk56yQc\r\nfch0VjdAGIKX4qbqjQcnqToFIA3Pp+P33FPWBm0f48IfpXiltWgHy75M2vzx\r\n9+ETqSHKVGi7ygXZ2oxBV8hy3AVieIGkYJYXu4U/wqvIJkRiNOzJar0EadXb\r\nhiTecfQiGc2Yt712PUZ8RcXpwphevCHGvjnKhmp7tb6ivAe7uALwMlEnEKRg\r\nu3PyVFrGDcNFpwF3W02Z4YVPeQ3qju1qXfn9CXPIQWuQNtGSKUy+DVii5Fe+\r\nDlxuqwVOpWk/Y+0Epe2VzCZcJ1qg8+tVa6k=\r\n=6er8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.49": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.49", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.46", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-presence": "0.1.3-rc.46", "@radix-ui/react-direction": "0.1.0-rc.49", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-collection": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-previous": "0.1.2-rc.46", "@radix-ui/react-visually-hidden": "0.1.5-rc.46", "@radix-ui/react-use-callback-ref": "0.1.1-rc.46", "@radix-ui/react-dismissable-layer": "0.1.6-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8e7e521a5733937e785a334d50faa2458ee7ae91", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.49.tgz", "fileCount": 8, "integrity": "sha512-7NDqMKHk1cz88s412VpQtdWvkWV7yQS/ZhnaFIG2TgroJuQRkSuDM48KF3ckanAs36vIbwyHoiPl3P+x90Rh8w==", "signatures": [{"sig": "MEUCIHhsvoRmr0Q9uktZ4+rW8s8d1+DIQc265Fn2A6VTWecwAiEAwpHujxcbxSN4q5Rl1juKn/epwdOWHe3GJ2yjTjF2wFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIRBAAgNhHFYMMDNM1jHygGLOjnuztQ0ao1wJt23QOPg6QX1PQxDe+\r\nzEYO3131gxCfgvlMyYkgsc1QCC+tuegqWaZeBVO2BD3Ld5OQ3yoY4wcKTX0x\r\n/aTjrwTd4smkjo5Et+x0MC7+ZGdKZOuaBCkqK/Mjme5Odu89V0WPO9NITi7k\r\n1OMud3Bj9sfzd+nvWkk+rwimHCdY7Zo1sFD/ayZqr4QO+Rx0dnCV1UsbEown\r\nnogFCE6tBNOPbOd+lzxOYyL3rxv+lYxE5sNOlt2MqUepVpW82YeM0apCYUDb\r\n7Qj4cI6jyP2hVqm6KSJxHVQbNM52RgmIUkUmhHMtMi1pGyuNCmSEfFlRkkBY\r\nDTvA0SfokY22CY1DNYewBixDtew8+ajT+BM1XX4oGHA9yVPzSW4VS8H42Umj\r\n0AKK7uv22D3k7nmDAQK1/bylB/e5RY8eOo7zP7URQ9vKXbmuoNqa5MO9xWf+\r\nyx00nJ4QaT7ciCp6+KZiM0HpNp/hbad2ziFxo//EeQ1dMCnrDqa3ZZQtFFRH\r\niy0Z4lpHf02IvmdByFfpzeqh1Reft3TCxwPcihIw2bcbKhbjB4r7zaD0bbeZ\r\nSVNjRfQKH+/Vdgo6sjwVK1VX6c/XUdMG9ys37hIv56eJBwdELs6i1IplcGgw\r\nO5/LmiPF2LFrXnu41rSr0Ez6DBEHAl3GXeo=\r\n=/c+4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.50": {"name": "@radix-ui/react-navigation-menu", "version": "0.1.3-rc.50", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.47", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-presence": "0.1.3-rc.47", "@radix-ui/react-direction": "0.1.0-rc.50", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-collection": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-previous": "0.1.2-rc.47", "@radix-ui/react-visually-hidden": "0.1.5-rc.47", "@radix-ui/react-use-callback-ref": "0.1.1-rc.47", "@radix-ui/react-dismissable-layer": "0.1.6-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0087d57ca5f0cf36c20a8e41dcfa56afb960d200", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.1.3-rc.50.tgz", "fileCount": 8, "integrity": "sha512-9vYIdVv/OT/3zbcQqnNQbALJtf4ABLwnwNl83Ap1HNkGuGZTBvgFoPPsidc89iXKUMuFbWARDWP9IJzCAMnr7Q==", "signatures": [{"sig": "MEUCIQCJon/a4r41ZsGNNrL6RpEqZGpZXHPKIrD21MEGi6oTgwIgPdfkVtVJccVwGjix5+OcyOUnvWBL6dvbwRZ27YaH95A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CEAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpusg//Yr0aBUAEpndMwC1YA55njXq3x28FypehaPNTXywNUZle2SWd\r\nwXpQFaXDAIdZku26Lqs+OBXbctT8UQ/cr6i04nEOZUWLlDgOdGGI8yyaqZx3\r\nmmJ1VMxc/11fjJKIHsRFAX9wQosAq7nfbOz/tcF9IpaWSIam3AG4YjR4A8Ed\r\nX+z/NN8vkl1JCCMkDUcs+2sQBJ/5hFrm+c8lcOdqgmlBK9iNEIjDWa7hHv5g\r\nwDuMzlqa+TY2u6ZVdpZbYaOXszTP6a4ScFGfHVhHysXnZIgSnXS4Q8xHL2rc\r\nnI5DrIjl30WtQUv9B85IfNgyGzK1nRpAmJnLlBj2LVOMVUWVMbrMb8sqSvT9\r\n8sYALt1O7KnJXA9qQDmdeDYy35CYYofzci7a/pelg+U00uJedbCCm1f2uYag\r\nTHkogCxUK8hkbT8oYyBYqSMwvQzoZg534nkCZeLosZ4zOamlsHKWso3WwX42\r\nqpYdv61Gq3AL1G8WI9yswr01UsK26Rg4nZ0/sr83U8DSRyw4lmMc1sZRVgkj\r\ndpCFsbEA03UPF+ipAyNO8liYI0/wQolF/qlNka9ySRU9AUiW3ZZLi/0UnGi6\r\nW17BEGOADsqdiRJud/rvrMxIh2GSzhoZpZ6z0VEAX/edWdYDtoMC7/4J6eeG\r\nVi4EBZ5egLJr8sjZCX3BY6Lt9LufP0OZ+Ts=\r\n=311Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-presence": "1.0.0-rc.1", "@radix-ui/react-direction": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-collection": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-previous": "1.0.0-rc.1", "@radix-ui/react-visually-hidden": "1.0.0-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0-rc.1", "@radix-ui/react-dismissable-layer": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2a9da1670b7733da28970e7b5c3eeeaff309aaa5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-c3JcfvbiHrMrbs61lYaKxTAQDG5ra6igyJZ97Ad37ejUyEqab5vTCFNay22dlcN1XGw6vmmt4htgiFT+k1AnRw==", "signatures": [{"sig": "MEUCIGzHVh40fBBramGKOlXK6znyQeuLa75gNk11APX88nohAiEA9aN5BEwxLhtZDE1DIE9RQhf00pv/hRBCwInuQ+Ggj+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRfw//YDX0Uk++z2DxF+FX7abYEhqBE+eRkDH1ZM/HzmCvuapHZ0dh\r\nqrqdv5vRJxiELbL5bO0Dc/4WYnBUlIsFwe8QTf3nD73lXz42e6yASvzlUqiJ\r\nefDFD7Zb3fMjJvXo72nVTVvobZm3cVRUPjRkx5FCjggptZueWoMVw//rcKHF\r\nFp5/+zLDkttoTrNF1ixzM//L62NcptbRhjszsFD/u9pJGLiTFQDiPYgdrsQ7\r\nfavP5a6lZQlouWx3U8j+o5pK0y52iTOv8V8CaGL8byY/5Sc0SC2R/xWyPp3I\r\n2x5oP5Yphv/tBpdhjf2jFzC0IayRtLiRjjgi2UEx/v0+Sf1Icq3FPfzZuQzi\r\nWCjtkYr3n1ElKC56J1hSwBSiWk32NWeiZuvUhoyJZxq4IcR8LA2+qORaxjup\r\nzqGI0g/sEFMLupNNc4suiOjmJJJ2k0PyddHVTg5wOJVyycxOn9QREah2GM9b\r\n1ysaM2wTpuakFWJEu3tBwIqNZmfJLMckTOiOikGOpWcXoPRYmr9NXX/SkhGC\r\nhFJ6VIY+OnUKE+we6MkwzJBa7iOVUy0FyrVXx4MpxlLt6HTPjjhgPPRPycST\r\niT0yfnWzlQUkNdyaw4FwWKwJUTYislM3CP76jljwy2xD+tcxly/zjheZHc3E\r\nnjzuhH9rAsciKuHWtC3pdvkXIRUNxCxWNMw=\r\n=A3R2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5aaf65967794147eee52e87930191c55d08bd213", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-HbwG3A9z9zdcan0076EbTXqMvmQeesC3nOMEL7XtWqBnxPo8MLStk0vMs0hjIQ+9O9/KTcEJq+TeRX2pUuvuSg==", "signatures": [{"sig": "MEUCIQDMq1eygM3+uSPSFxkVIPXOS2IOyeErSaWjHUq7c3HzUgIgNjULoX55XW3qQMIPLoOs/n8lajlFZDdCPyvfOtDUjS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcWg/9GTMWsHNxJqy8Cn5EDKvhUSjq8OYWb60xNipjOvVowYSLDsZd\r\n0/aLh4ZMaxMKsBbQvHNmzE2PkVWW7nB9oOEA3vsnJ3S3RedHmur0RUERt/9a\r\nBNT4E4dDwJ/9SZxwUhs6jevnbtMmjYAiauqbpdeYAqXA/h2tlpTb09uHbcx2\r\nuyULr2O8ioWOT4X6a82nqH9J6r2M14z6F2emhgEgyiQntWqj947glGWsBVYh\r\nm0k7CZ3rdMG704OIcDXg3ep0MlxfG0v8biRLAeX85oNqzvq5xA1cdCi3oYfK\r\nEyBX47E8Bx33hfuFiWq8uvNYV6EJ5QTKgVUfNWhx68yU7og/Wk5KM80srPF0\r\nI055u4pGHO911Ja9M/JfOREAihgYDz47g+nws6z6Na+DXeNhh99dzDpSAv2i\r\nA+qldl66i5kw3U/nSs+JJR0mwc8V4NBXLLdREKUSXGyumq7aTkSNY+R6rg/i\r\nE5f3GOOWl/44MDQYmQaeuUA9Jc0euEqvVT4G43p7ZzA9M90GlAcxT74959RJ\r\n3gNQHiDicLWVDP6oMPzKZFcnU8Gr4olWm3y063kGGXRTXSuNY4RmBWpfRL0S\r\nBpCnTENXEXgeJDSoO3DDeEyxdxWD50vCwEo3MVRjAqWF/QKP22Iv+M+8Zel7\r\nOcsf26bFF00MS2lqXV8K/KgWpdojF4JyPRs=\r\n=fHLJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "757b5d9aa7d4c11f10799c3fb8138eb18b3f1a41", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-+5t9oBSIGeCM4cJjon7h6ZmfGqDLuVusHHBRtA9OmBDfGr5ruZnmr38/g39eyVB2ntHUVWa0jViGjuKBfoeGTg==", "signatures": [{"sig": "MEUCIFoiM2H5SxNW2kAf6nFQrYPZxyKE58w4Bp0lWAmvtYoHAiEArNpUurWo/L+zK8Khl7bYSlueWUjI5h7lWgXi5FnFF+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2W5oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQFRAAk5HV569uSAiFFVOWRI9ZxrJk2LSdQtuNq9HRWYQg6m+G3Pox\r\npIJ9tneypiXIpakh/lp0JyVKpyth4D/fh5w0PcbuPuGgPjukgOsyYtWDLR7I\r\nGZmv6J4dXTqpdVT4lxHBiWmlAKPGlii4jgoLZgLtrzq8VtNHarYVZJxsPG3B\r\nlfFOL/zC7i45n9XUPOBkUI9G2g+h7la2OArzl7sUzxGddod29XSfkame/0mG\r\nH6kxgn6VuvAdyCXlBoLNL8n4zF9LcPvxarLo/wAij0QqKhAM5lq4Q9Rc1oh9\r\nWWHE+WPx+pDRxUZXq87koVf2cY8qzwBV0NnR/WExb3+sXYi05hCOGvyNBi2f\r\n/7unXWRH7QgowBFkVhqaoVkl69/p61pfZpbHSkVWgXmsUD1O8DT4uwgfkfaH\r\nPDb8kxTp129i++l1t4yDFeh0YjVYr+Qt+IaU+ApxEr892DX1v2D2h7IpUCOZ\r\nPp1jU7Z7I8nN8RDU0cKdGHp+LM0njCKphDzrU9/BolvUlwmwrmYUtKgUHpRv\r\nkX3AMSuQFpnCEWHP+TW5aL9FN7akA096bbXEeGIIQvKyMFhpU1X2T6DCrxoU\r\ntDaebeEVUv0Gz3rjhTEcQl2yrmiHMcfnbTpkJ/o/VooD0a5Z6BDTppgZ78Z/\r\nTgadBSpf7gvicGhVKSwEHb6XQZLIeTGO6rc=\r\n=iwKX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa42519c100361961b44a0298a60fdf427543a96", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-j3el3hhjC7zgzJXTeCUWUWBIpeZSMHKX9KfqMdSwD5GmWmciZKMSLwcqIlUoofomRE6rCu35O0P9PVnx2AIO5g==", "signatures": [{"sig": "MEYCIQCBQNZn7lKDbd1r496e+NXw0m9FCzfOlSC1RLLBAcDvdQIhAJb1GiOObmUuLii/gsKK+UJUK+jObej+qBcDSShjyOKU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2r19ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptaA/7B6S+0FM8G4fVsJbVtYjKmDeHb2+UfAYfA1HUjkqSERLh4IT+\r\n7GJhrndx0gMJ0oLalC5NkJWIkVVz/0fdoDvTCNEbwNhpyTJDpTwNePATL+bx\r\nPJyBkPXOQCxQwVO4PYWoBv3Bquh4QeG8QhyAVOQLsxOTHF7mJF0I09N4kOvC\r\nkqPeD2eWxJtUx7uwE1ia8t8ET4CVV/6MtHKVw1BkP67zJ9eIGrVbGNbmT+i1\r\nCxhXCvN7R4duhVLqarY/C3xqqaiNVb8GoZnpO3LeZmKeuICtq6nVaOtbeo0L\r\n5+iPCsKBff68BF+oJO5OEAnMGUZLt5lRRJWVpGN8seFjD/hAgwGdH5DBDNHI\r\nOLX8KnZIwGQt+slXmOgrbMELrZ9UiZ0XNu5j6+q+FTFzTIZhvg6D7lWTozN0\r\nCbSPgBQb8BKrTlKKCsgP6bzZmlc4V5ScBVwZUeoJgLgAic9IWDjTMnghc5GC\r\nvu8kUPTpibStYuwGScoPjA6ZRQACh+3PUNuDEsIqGIHk+nW24XokDum/Wxsh\r\ncOx9d84PxGv8534nj5VVXfhyeYbHA9J41fAzk7yXq9pNeHoqfAX6zCogv0Pa\r\nQKTmUTyi4Z5ZOyBXaEb7vDIyGCyZiEqqcC0F6N396WulZgt6vgBAdb2+DH3B\r\nypYdYUu8cxEqdS32G+MAgnzo0i6yK7XN/gk=\r\n=l/qv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "813afb6887079a030f4c561ba0ce18d2d92a2c95", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-NbZRo+SE42dEozy9jmyoRfZGzyUbnAHGhPhp7Dy7rWoxYDAy6outljHMkShzX35hoXks6oThJFHpBBVZD89vfA==", "signatures": [{"sig": "MEYCIQC26GIWq9IDoeOZYIDnM4iACYq1rotboptPBO1yDEgD/AIhAMmFPK6KuM05f6pwlVGZXyFPQLCikygVh3O1ylm0finR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFyZmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoe1Q//fuAx+I4d5cY7QnwIWME7scSUwCqUdKDsF4La+1vgeIc5DqtE\r\nWY77fqseiaI9L3/OprMjRJ4e7Z6A2tcvQufIvfCXo3S9OfmDv4oOgHOo0HRj\r\nv8evFX0/XURpjs5JBsguhMm9DzcbmOSqW6z6RVLmnRvO0ALZTy2m0stHUYIF\r\nfzCZMLSlSLRSYJOrGa9HtesGW33677GkT+Ld6tt1tEzrZ1smlG64V5uel8ME\r\n8yOPFCpfwbcXS2NVcxWwf9z5uJCt+pTfQyuzrh4ZX9W/rjaHhDvkT45bK8YL\r\nIr20piT5UpcCZWLL32IkYK4ANzeA36vbYhdUCFNTOZRgNeP5FNtdxSidjamD\r\nvWn5R7GsRNdJoWss7QNLkSWdGPKb77GqMXPKAPKL/GaRaxZbeViPE9nVaaVG\r\ndcTlx7dkYAz+B1BMK1WwvuPm2FLmGWcfxY1DGrPD7TO54qb1l9Vs7YoVrIo9\r\nzw/JDKG3U3DqnytDW46H9NG2CmQspylmBi0cXWbnIRyV02200z5B/yHCDgQj\r\ntsPdEYawH0U/lUhZWvcUMYGwTcXsyc6bairp8tBSHGWPMdYg4fROdHsDw7w7\r\nvzIFIuM5eu7kmsRyWGcdNgiiDIERBnO4lZPhXqSELWlqT89QAWMMIUnTBM2W\r\naaACkPcoeRrOCwePasHIW9iM365Og1Rhaj8=\r\n=IKke\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dd94b4d63692e77f602bb19344defb92b992cfe2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-R9kqAddf5lYq65Scs/0i7kt3zA6G2PQy5Aarir4e/rN+ecFFSSHgBmr1FZooMxX0KIfkafq8f8sVzUPdDAjp7w==", "signatures": [{"sig": "MEUCIQCmlLncxB1x43ulTPZ7xnDYieEwMk3NiA0aKv62a7WNJwIgelakJvBes56jszMHgIm2XuKTxM3o+c4CMfz/nDxkvog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKbasACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRKw//XuVtEtjW79J9cdAa1BquSFgnTmAMNz8IYS7ouOEjTOguon8t\r\nPClhR/Ge6CxQI7TXSMhHUsFW36uHs8YcLwU8H0N6XPf1p3hxmcD8634MMGko\r\n2DIsmVV9EF6DwCN4qRz4m4E0Mv4PPKkc0p9Uc75Zyrknq7tWd+52i5cVhzDi\r\nASflBIg4I9cgmr9wmFxjULCLK0yGOJhsfQgxOm0lBfoLsiZUAUWM4T502C0u\r\nLNF36fccK+Iogis2rt64GurMj6+KGTPbulIlChJliWeADhR5E4EnJ1BPF28t\r\ncFFk0LSWorl8zEGx3RRwYJnlIuoSkmFHYve3mYWDaW4XjP/4E3BeafAuaMzY\r\nRt7QkVEBbeFJyx2Vfcqd+TUuZbconNXJuLU4ZSZVpd+tn4MCG/TV2R/MtyUl\r\nrT1VV6FLeS3MRCytojkx60YvIvbGIsw35xGJJ/1opcCsKq9HxRJOHvQ8K/ot\r\nroBItPSqOBXyKJPic0VOWfwhC/6XpTtd1NRsi/dzEB0SS4ktohM6VSgiQ4ap\r\nVUItqLOeogK8NRA/D29G63hr+qPJQ/4TKtl6IKn+WJfr6ZgeObVs6WdH4OEC\r\nprA5uG9G/tgzNGY2xcJoM4FeK3Sxqr5FrqLHThZD/SwLH6giBm3NMmSFa1su\r\nywEXli7LKzoMWO3txlswSGbOF/u00gOtQ8Q=\r\n=GheD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "25330d13d94a63d0f4c0ee9053df46c58986cedd", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-98H8ukeRBbleFHNjEdJEgjAa5K3BhXMIFeImv8zCvuCXFxON7XXgWsSWMBhFslNzg+kPlQ3kdqh4Ubt+UEqGYA==", "signatures": [{"sig": "MEUCIAl+X7hZgXfOSAyLfeZJQlBTgEWx8ppxcwpMxsqEfOLSAiEAjmGkeXcw//3/iRggIK+tx6zOvQFb99c+aoJp+oTNyWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKbv6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonCA//T3NWFfZB4GAtfC4rJmKKPI7IZ/Y2QHzO419u0/56tFVP5ru7\r\nTmx+vjW/ZHrM9sR/5+Uu03S+ehiQJmpu/1C7safFTI4G+FV8ZYa8N1i59AOt\r\nwGq/y4bD9IpPzUrNsdROIgjsRMpAO6GLSIiwjcyMmK9otul0EA1yO/tPHWlW\r\nQQzgpE9iCYOKLWezMQ61WLfr3wHKWI73LVAeix9A7IyLO4RMXg7ixze65O1t\r\n/cGLJAvrpMk3Hhs5PjxD9L6qSmYhGXbS3rbXWpW2z0TRq82y9K15907dIdI3\r\nKNUxC9pgaJtRHjzs3QMCpVqHS3xqqj+R1ha3fG/Y6ScQ/94oLfMSTDvFlOWV\r\nhjzgNveoByUIMRbhWE09JylQerqF1JOOEfDZWXiMIilyn8HloTxVEdcUfJTQ\r\n38AZRfA+2cpR0rdSoc2TPKhBG4TtobeHfPUQ8UV88pxXAUfHFUpKCe9jOSTE\r\nZ+Lq9XZfsd6eFzaTYG0dEap03y/hKjdrkxvbUB9ukAwZdv+ShgdHXvfuvpXO\r\ngxKSUAteRJGJU4+p/6zS6GMZy9sXyVWzrt5YGKcxdLgonbhsh/8G7gAKudXg\r\noY44O0SVxzd73R6cEe0muIeOFIBQy6CBGpnUYD7u7b/7+96ka2QluD7kyKbd\r\ndI3ZERwUHulsYv2jAnLOTA+BTLJHdIN43ok=\r\n=fByE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ff9adf7b3a9ae91e7d646aa2e9c822548a69f49", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-J4Zp8mRWTDY7VyqFTZbTOLDY4zSUKIcUfYMwSX6e6298RaKhZ6n/ZpA7xFStyASCnLyfwZ6IIMD3dGQpy75gAg==", "signatures": [{"sig": "MEUCIDz61AqKrt2ECwJQ5xOVM1DRQIUuFnX2X+KfDRWoPQ7lAiEA4wHw/3gsFj1jL1/NJ1JrsgTpZH+NImqPIy6kbxuEkEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKu2GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoesRAAniPb+websFT+Ze7MeIv47Tilm9w0rGQixq/BA2XeXvGIP9KK\r\nEaH3sXEcWorsEefFWcMnv96yM1BbDYrPA1FNtSjtWLhdDfmGdrA8i6+Z5vBD\r\ni7xUEybuGozTT182dgcngrRJ7KhovhS98YCZTktLQxQ3dtFPBoF6fnYDP0N0\r\nPlCW6b+nEikNv3G7ikM/+PeT4KbU4YELvoN7UgQMO4r91vMD1dqtTPxWknWL\r\nu+gP77nZfBr3VV6UTv/9GfoQG0YgvOl6K8UbMrGELMOqmFsrm9dgQBiXK9ik\r\noBETNhsxTCel/ZdUsf+E+DZyJCaK14wtBD2hAZ+d4NT9/wf07+Gl7Fvyl/gz\r\nztNR0lvG8pgYzI2mnnZrLbw+ga6KShbSwd0k00GGoCD6dPmSD7LhsEvmF5Ut\r\nD3ryy80yZ5re3Jyob8OPiSqTGUQOgUEbhP9pflMRmBggpXMNW9KvDjrIoy28\r\nBNtP0ySmge/v/zWzTqnRW7vEc5GfKxsPHj1myaYsU9xa4clhdYlZFhBypz3u\r\n+xhiVh2Ogrzp9vGJ1APLbIx/MQD7+Ow+pwvvS2xN+BB+1IsZNQMvIcrKjIL6\r\nWsPf9T8vPyvFib19b2r25vjjccy09ouuJr5vpa2S/fL5rn1/+Qm+bXoBl5mu\r\nV3NYfk9K4XSM3BmG/lYEKgJg/SgFymwB4A0=\r\n=aKau\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.7", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eadde7427afb2855fc36081b57e7e0f3c2d505f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FFQuo6PasO+vw4tQ0EfqwYV+kih4kJIl5dJHE8lj3j2qgJO9+JealKar+H04CSxjYNo5lvNqvBMx3sTIEpusog==", "signatures": [{"sig": "MEUCIQD/m+RjmWcRb2bWv6QTaD5MJJ9b/zIlfhfPgBtkp3wQngIgA7AG/3XgExeNhIeEfpwARpfuqvDBEwFj6BQ66upHTo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMaxgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWBg//dlOFg7igcF3OO0ThgFPff6+xTyUbt1Cez6TV0ZS47/vuiw7M\r\nC3YBrBRCpGySZOjMagd0pFbD3IxRSN53Ir/w1HmxowshURXEgBInHbAz+QJ8\r\n/Yb92dxAGxKtXTWtgu+npFV7z1c5Nws5FAj/SyDM+v77+K4VR6euMCENz1RO\r\n7xXPqq+ZjMoeh7z7tyahT+AZ72xIEpd280uxzAIhm8FTeTqCY6o5+HzTNuPH\r\nPcgr1PAnGtLV6LjryUaB+VOdE65aKY+it25sLEHiKx3ttOM5OIytr0ds69oT\r\n0yQftTanT+gOGfXjU9rr0FPgWGCuXEaTljkr7XcrXWD9POksJ/A21cwjaAcq\r\ngVIa+ja3ZWJKVXMxeRI3e7SeQiD+Di352jTMBQToJk2rybhwn+55MxKvDf2v\r\ndS1gJ9wSaOibdDp8gqXZmkL8LL28TqWWvFy9JT0TN6/XiBZdYBOywgcH1hcR\r\nPsIoefqsbu5IlSxmfUiY4SqTOfHhX7y+uUEv2RqxdoO17vU3XaTXZfDtD5TM\r\nR3YXWIVEBDAw8c5sSN9O3p4yETtFY64iMWGVbWDfUgg9ftJwofDbAz4q32CB\r\nAAWJ1aIBwdvLGSrW1gE60nFTLlhEf/QYWfqbJsV/TDZzI9stCR3K2vtsuY35\r\nA5le8UWvdadQuNexG8eJHxZDea69d1GNPVU=\r\n=fkPD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-collection": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.8", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "599c2482455710954b9a7a8544800c1d1332ed4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-bzsCwlXFQWPqQssSTrYVLfodXPPaqH0ytmc9gdql21KMJopF1q/Q8BX4kn3cdmultHkuhXrDnZ2FU6d1YHZsvQ==", "signatures": [{"sig": "MEUCIBcaJs4OnHkJHQsGKqJyMWYUKB15LMITqtlBM4xr9gsVAiEAxKUt5g5quVnjWx9FHU2rijP2W6l5fecMrtxNuS03lHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKhA/+Ph3IekCJywrNZD3dap+XrQNtvw/waC2t3IWHH8l2UXLetbcR\r\n5jWpzNS28TRr+kfYcwbDhBD0Ezfrk+6afCyxI1e22y52gKYZbaiBZW2vv7Ml\r\nilUYYYSZzlMphhajMJHYdQqD7A+zPzAUztP6JTCP180gt0XIcdi6I6Rd1fPy\r\ntvObmBVbScFTAatKy3t5OLLHknEpNMZfm+HF0+o8TJ5L8Dighe8W27R/fWIN\r\nKolU3sLrx4uKB7WgbreRCYdfpYLo0vx4n1ZO8uNMI6H5milT69utb1jg87/b\r\nWsmPz8vK+HrPretxWXKL04qr9JFjSOHFoQy5ZMvIVxDWYRJ6TqMrX67Mdc4L\r\n0TQlcnZMfxQTSy+X3as7c1AEGbvs0pKTcvYzLvb+4YIWM1+oSZeheOIBNtpU\r\nvftPJFPPB0nD3cBB3aKhnZ7ralN/6i41Kn4YD8SPaDNyiGZnTztU5YXm+9Z1\r\n06ehtbc7dYRj2MfgNkNhMA0JQJ4MIqn49YD9NzVWcbC7SIoxbxhMuYukU8Ls\r\nezPQjfu1PNex2e+4DtCzUNzZdMKoAskdijlXNTIkJRkF7/ysxdQMQeUMGHrD\r\nkWViYtXf5PxKSCVEhJBltvOD76ptGez38GTMF3KAXFycNfAcOMjXNoijJ2j5\r\n6tVT2k0QDp0D7ZafFaWVyBfIr1cJDHhDYMo=\r\n=pveS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-collection": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.9", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3366e5cbbcbb3ee0d333839dc8ed2b4ed02059e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-nq4gGelFJ+VEP4ankUXVRSiODQj5qk6VuIoQUCGTr26zoebSbq6b+2mL2rZLHJf48B32vQhSc4ryMHUUIPJuig==", "signatures": [{"sig": "MEQCIFxAibDTOE64qNIhBNgO+FZluw27YtzIp0j9JKAhXhuPAiBKcXeUVyTRN/P9eKMLTS92g8pawoKI3Ug2D/lLAVj6UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdNg/9HaVjcMlOEZBJwvNElRG/TyFBYGH/A8nOj5Q8y2T1aTwAu15Y\r\nymCQOaBhFlIojIVWPT/Pz8fTxug73Rrm4DCtsxTJ49ITnWEkcInxJTkLXT7M\r\nTbDIpE5CLIV7HShgLAEbPVM/cK3NjA4BM/EHcio0P5YU6/K+K8OMqpDDva9F\r\nDqrcnY8U9r6gGkSziTgeUw3NBgyExCMD7MAun6jmvdUFeQrH13oXp1AR06wa\r\ng/XDzKTgUqPO3twCvVsLYXYU/1/LOSG+2FehdLcNsHedhThZ5XyC6TqzhL7a\r\nkmoA2wED/TeHgWtnJSWMvMpGUc/+0yty1KiUJ8NfmF7aQqa70r2OLqRu4Rvw\r\ngY34TOEW6ywndu0EsONuYelBzTqF+hirYrB6BIOZuObKVV5pk60hF4E81NNf\r\ns2bt1UOufLXGBS6Rq3+Hm1yRzhLt72gGKyT7VSnpqflxCxg2fcc3S5VEMnz/\r\nxeLI/axUdvLpkzPTPSMfDh22bRXyeQZT9Hjki1o6vTZ2G2Wfu2M4oUWd1w05\r\nXLnWp0ZvXO5SgR28Ap23e71IPfEGhLSezi4RdjASAgNnylgYyNvY6x5tKScn\r\nlNAognZOCx9VpPqqjawYaJfoXReLYBkblTm369cG1kPN+yywxcmXsnBH+iRL\r\nRLy4ld7wyE9DoYT3qnJT1rRetTuso2OQlzg=\r\n=EKnH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-collection": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.3", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.10", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ea5c64bbc23004bd048d009d90540fffa5a8ea4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-Ppv8qhY2wk01YFhGNccHrc0GB1HdZX19jafYFPz+ykVMQakeDNzwTmdYqC9QfbgQco0Zsyn6LfFS9+8EjItPow==", "signatures": [{"sig": "MEQCIFrz8bXmM2XgWz/CF4Zj17vBKoyZoke/4K3G1H1l/XsQAiB4ru2Fc0Zq6LuEltzR4pxQ5ooSH2kL6PoIXEOazFp+wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFHw//dti0I/DcuhT/mwDxoXyLaAxqBDLd2y4FDljsj9Ly5DDryMME\r\nhTEm7NuRkK7p7+Jk4o4qKtQPSAu0LQE+14GR+w3IGPcWnwyRbcqTrKHYBtqb\r\niYvraeXL4peq+HdTOn/mAMm30lm23M8O6N3KUwHCYiIqgVCRc845rFovR06f\r\nl02ZiaNHP3UhTvO8+v0/BN/d5AbJttk09w7a8Pyd7wI8GLxmo8C0j2/P2DKd\r\nA5X0GDlsVkj+SZPmOXenjI5LS/+Yko9OyiKBF3XvXUUO3N4tQNDCzVjBpygx\r\nlbYb+KWIoEDDZJ79UqoproEsEVwUm5+/EQI0rLJOtEGXk0ZkJZQQMQyTOQka\r\nd/vJ2SUrNwQbJoFc1AbhOAdEc0Cf4rE5YiGvSa72pbTrJFenNvrEwDy3jHpk\r\nQXscOZgIUVhe9WGKmiCKUbQt2uYVEnJqj0UMsZYR45RMiMvF59GZ5qt/TaFp\r\nZgUPCUvniffre29Pwy/eJykTldWQL/0P538vxeyZkldUqnpJSNNBab8JaIKh\r\nCteuNSKeiUjkGOL0PprW6+qacvVhMnA1JWteMW3cM8cN41KNSjek7CIHzF7c\r\nB2C9lBhQS/HGfK1xemX4lYbg2OMTgdKxXM5ZR7rGKsNkIoe1aSk0l3OBuhhH\r\narZeq5ItUrLIBaO9fsifUAOkxCznstyifMY=\r\n=lh0A\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-collection": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.4", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.11", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c31f17eefa8f6719bcc7a9e165fd3a1f481ad75d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ARvTVgWKmsCyIHUiCitmNnJI/tJWEeG1BP03LBnxDbP/IbPa12bhgNLsRYkLd3CBo8tXM6OMn9+2jwvy7erDhg==", "signatures": [{"sig": "MEUCIHDXzc7Jv0+3jSQ/gtKR3RGexuzLNsRwXzEANZvPzN4iAiEA6+KgnvFlqTKWVoP7fxIhMPuR1343NvSeAUAl2vrelnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 274853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj8w/+JLK89ki+/77x5S6wJcXgkULRBApuz8qB/ScZAl8x0V8IfrFb\r\njCsRmaeCvIhQhbZy8Ts/Jm4j9L2TtT5KLpfZRxaK7LJt0CaG+qoXdRv6GIaH\r\nOAhhVmAxwg+0pqNS0j8Vp/IxMCp823OIQfcW9YmW3Yen30cvzMDLcCNBR+DX\r\ngYRM2wC0fcmKl5t2+gmFuM7efeZV/xdrkeB/6fL+9GMYNLxbGjdR1Zyt6HGT\r\np9BfG73v3LipgJ1sPNqfpat6p4+m18g8LVt2DQXfQFtuXlrGWF1TNXgYMA5B\r\nFSZICpWeawMh943TGyHDxKDPgRtyqytRuQ18Gepgd2jQKxWj4BKzYTxnZXOc\r\nQfzIEvardm/9QA0I581uq++9RKh5ffbNUttnAuq8JgIDIu3PtjGdWPWindX1\r\n7pmiNByvrIaaIvUViBQK9Kv0vYOpinvD+srtcEcwOSBEU7F7QjnuS6D/WcTu\r\nDwaYbF7F2fmhsx5DIAanivOkoLfWCmEb7CwjKtTWj3uisW4XSQcZxlacfqLi\r\nLDl1nLRhbFd1gW+C9zjupIAjJL9lLZox8/ff4HT4DZXzLJGHWeMf0MQS+VKt\r\nhFKX6yUZUTAPFSwoNneceAPBVaNFZG/ArmzMYQamTysedE20ZaxvCvSi4RlI\r\nNohWwVuRrK2Gpk6Q4EF26BvBYDOZ2caWuFA=\r\n=ehy1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-collection": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.5", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.12", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8c09961b190664432587bacc9b74e65c31944f48", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-C5aDLgoZ98htf7arNC5zh2MWnI5Mvx7PPxRkHeV/C5Q2e5vM1ZTywM6Akga26cXtKCs+QS5BuS6zPNVyYEbCxw==", "signatures": [{"sig": "MEUCIQDbjSgx4Jr//tJ+YIEeMLfjyEmsd5IjBZty+6aW1+NsbwIgT3FrPKweVcKrhQ4YnEn2tuu5c2yd8w45/81zIxz/KvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6jg//SutCemupIxNQz8+L3BvmHEBanFXsnXN2L4pNB12HuAuMfGFm\r\n/eS+st8JICO7Es1hHpI+v1g6xLfVbFiy3wggt/9aqkWvOJcVmkwmiCIWCmxB\r\nD8uzTZ5YwimUjirQccCrZWXZ54D1+spDAplfBBSMKvYDDEGxPq/9RYjsV5ly\r\nXE91F922hxoShxhHHvTc6MJdnsN2vCIsrLXQ+yfy8f+u74BAg+saG9iI5JHY\r\nT3w3vz9GqJj2F3mBUnVJAwE+u+Ek82VH5+3wQk1hgQBGDliPx3jVoq0H1CWQ\r\nYTW01I3XACzyAWrh87RVCrBk4wdjvouWb3mQ101JE511AZUEuw5VzTLVKhJK\r\nfBVN9Kd6+xEDiTPc6OvmpuAL3JkEZvcVfLLcYiCbqIKMPSFikU4laTylnnVU\r\nVjXhGMr+ryL/D7SL1amolUQlL/ImJ2G2mvAg72ztoqLpYI48wUchXM1Z+13L\r\n1rPAN5+Fn/mqN2oKeElI8gkJnzvOwfs5Cg5i/9dwI1UjgR51g3HX8gpdDrvn\r\nPEHtyiAXytpo3R+5Yo8uEb+Fr0uGs7rL84LOK6JX4IQKF8KThPl//A+LKSqN\r\ncul/G/C+E5kg/pMj7FdDTrqoNV9kN6qpKmBpj10njee1v5PeakJYUS+rOBS6\r\nOwrTDl2h/GpbBP/YgWW6jp/pqSc2He7XhW8=\r\n=N/NK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-collection": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.6", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.13", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a5d57a4f9f33a0ad14afbb6e36502317fdf96f14", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Rk+lTI+V8AVwseoju5ZAotK81CZ9lTWWbNr03ediT6M2Ec6RyD+NRdMHAmoAjqjx3N8kl3AsgPilGVYoyw5NCQ==", "signatures": [{"sig": "MEUCIQDzsY3K6ly+qBWgstQK3bwbBfGc3GBjr6wCBDyAZWDZOQIgBEsoa+krDGCyc6nD0LYIv8K62vL2vLP20dy8sk17SkM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrtQ/+P4+CdiNzCMFIYraCgBNdvfzzZJkUu06964GNy1YosJZVRexY\r\n8kcRAPVK6RRJpfS6JJB7j0LoiC/fM7F+PtzM+c7GPvMoIdUvVJwe1dqxtFoA\r\nVAQa++y6w3f3wpH/k9cMa4g9wqPwsPldQx8rQL0VFxIcHltvNjLq3YcZcMHW\r\nOEodL7bTdgu0beKpW19ySu878Jxy4Z3ShNoOGzc7vO4jzVnfBqn5FZBu3tO4\r\nfnR3kV7p8oaPcaJbEPM0TkdlJWTFjm93F7JjSnpDtp7XpZI+3zMj8fN+qDez\r\nlPdTDGpemjIyC2XCO0vmE8GJGT09LAaICwe8jUsQiACC1WWJiYw4jTro20VX\r\n3xpGRI9bOe5Hyya+u+JaFWST6ZA/j9Mzonewm+icCM/jxzb9b75xyIIBc2Hy\r\n1G56wvjPMitLhqjwH7IePuBxuctDvOzZX5HnWiXT+IVsgiIJEv5xMz2IZCSi\r\nB3lQrIklPtFu7NfGmnTpll7RvoyptDT16eO5qsTw7LR9ptokfyZrru31GnhO\r\nX/eQaxMIkv/5KFmj4wr7vy8ml5wugrf1xvgym/gLFQipOsiXJPZZ6G07Xx3S\r\noW1kHruyezAlhOUWHkvW0ok8sLA+ln3RBwodeplzVpFHLjtWB0k2nFzSIfWq\r\nsOvthknNLINLBR/YRG1yMIjAtIxvyRS/t4E=\r\n=SwWZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-collection": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.7", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.14", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6588e9968abac67f3f1d1087c9524f0134d438a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-r92EobWONkT9lnv6tsfeCp05OEKeBlf6Y9OmCiM76tBqx8j233EXUS3l3K+sSTEbiOOqXAr44qt01Y42Ajp3Gg==", "signatures": [{"sig": "MEYCIQDqyWTOpnH/L2Gr7G67Z05FdwzuZDc9wfA8figmX1ofbwIhAPkAdkvIf1mP9RN1rRYIUHoO+xoWmQiHz5ffPHo6rzoT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJyxAAjcqmQ+X1TuaaubqsYIb8iYKEaNK7Wev+JrFjvYv+dUXd5XB8\r\nKe/n6XyRYNFln2+x2+7NOBeflgk3680enzgwFWvVKWVZkKj+5MkcwCePKHCS\r\n2FEG7k64uRy3mWLUre9DKVsZiwNjQ1IopB/6sQIiQLbPTzS03+pACscm3jOU\r\nSZJ6vTm1+JTgXmNlJNZbpWtpXiaIneI5t6cCLEbfs17pvYKXYlcs4uah3dnc\r\nNZW9rbQKmnqq0WP54NVoLnu6taFpbMajjJ5SP3sN0GiL0K+V77OLqaajAKMM\r\nkqyZ9YtoDNHkEwLwKqxzYNrtXDFrlhYeI+TCnkPt/OdbBOcm6XCDVV+cOKMB\r\nALKPlX869XjnCI7M0pW8GhW21pmKsXS1/FKEjRVWo1zXzqG0QSlbBz+SyXka\r\n0Agk7Akylfsy4Ii4Z/BrW5tkZLLE45SwbZbwhxqcGAoo2Qo3ehQ1EoX86S0K\r\nC+HE/EmX8K+nZqjQuGnK76JgGJEhEVODjoDVFEP9891CbC+2YQsUdkrGkQQi\r\nBN9GXZEyIyFskH6FoLtyu7zRZpFy22ehKSdE9W1xB5qgVhnikG5j7rIgi2ak\r\nD8SezheLbjEgHka6sABFEJUSXX267glVe4k8I080qKW9NkGX/OJsJGr4jixS\r\nhB9MBVoB4zzSAM9fH33+4XCOoSlz96/yJ0A=\r\n=E0+2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-collection": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.8", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.15", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "81290f96b1289fd8ee501ec8ce5bb943f288add7", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-JQIICc/y4ZJ5vkpdaFcK6+XRx8e/OmNs7XZ+p/zGIbLNpfSbK1vQ1imu7gzPTj089m2+zG/eUKL6cwWB2gEWtQ==", "signatures": [{"sig": "MEQCIC764gFcf9CqxstU9q/wWrDlyitcN61JlT+dXlxKhw0vAiBFoKLbXeuHlSqhREh9zII36Vop0AK1rgF3nEO/4VTqyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+giACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXrg//YRXBG0YvVpvJeRDBJqkqk0ocOViZ055obgCWInxb3oI4yQ1K\r\nEMxrFurVamwhHBoNkaRkftBGoYEIEqtqloPy4o3weGghHUT6nysU9YauI13x\r\n1X6aVzrzDVScjYWD2klwbDTWEeeiKin3sJdJlgKN6nr5pQHeCvK7IXvBYzJS\r\nUXKg0qruAst/Fh57u/WdyfK/iGq5/M5nJ09fkfyFijCiM+KC4mD4/ez4pJPf\r\n30J+ZT49KSPU0A+3hxWg9LgzcbzsdJVsa4J8dO3FHFzGVjm1E32kOAsn+utu\r\nWjARf3lDqNWlkShH6NB9jagTF9TDRjXKnOquuiu2ESx7DIt+ssrz2f6Z8DGy\r\ns85U3nAIF85hWsCPKoMH5FrErPX1qByj8GFAOXriojNvUehMtFTK8Zx6Eydn\r\nRVpk6Zl1WtFtuOrd1bbY3rSXMkj9VDFu74WlEkHVJU1LtvMCX/ZL5xkVMMze\r\nAxJuBFNONHs1M7AvxFj6IAjed+90vQ3MRKMR6H9hROFiEAofsjBtcE0oqZEF\r\nnKalN4I9mJ8PPJQf51leGrvWxUVoveHGzrDBTadvDZk0Z6W9O3voO2RJw29/\r\nm1/TWzV1jCn4nnQ9QPc6LtjRZYlf6N/HZst/WRMuo4AAmXZADMRB49X8LDEL\r\nfIsD/aOe58eOLPXmkIJ0Up/A3fGigAuyfzE=\r\n=UH2k\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-collection": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.9", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.16", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "477caff8600c6228cd8f73564b3fdf43d936b18b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-6t2y7KUAvHETkbJ1OwI8U6nxjw3GLMXOrQJx5yKyLTAvg/lqdbDoGdjvOFhCpoPs5lESOjB2GvlNRgSUkAuiiA==", "signatures": [{"sig": "MEUCIQCrP1vBSxWABKMK1f/u+tSq7Ep728kAd4Ancga+HTDMkwIgWL0SlIMN9MbvD6LsbIRdo5o7V+65EXipTvfuzWcxirI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278219, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUjA/+LKRyw/t0MU9ywFM5jAANBv7dNIJrdGHwcOD8cT27FBwGDRds\r\njKcNQiu8lj9tHK+P2iLd/LX9g3WIE+USXRUsRZVXdJCPMh1DCtrdRth1pHvz\r\ndlxNnFOhbs3VU/QZ9YOrHtA4D4AhQIIrnhK092igA7SzrHjkXh6yz4BNryqk\r\n/l1Q7q9O/ojHsc52TAaa8hb7golV132BRued8lI/Gauh16jWd5YGDuHUeBwS\r\n070HFgySpi7xA/8JGDV7YUjLQi+vBLdXqDZgbhWC9xdzZknK7CG6fUIRRwPq\r\nMv0iyCextaIf0q0NR3TZvEe5MmrXX84xu1VZv3/GMKvD7BtpOAdcQvB2Qd6z\r\njhYW5sspIgpUidC1IQH4cpiy3zSYVQkarQXE+ioTWb8luEZVo8rihaJZVTHj\r\njSY24UGXWs/geAN5gCYigbiVSCntTyevGaFiuAS/Ibpq+pHxL2Kb3T4B/FU6\r\nQe5IAkUe99f4wPl9lHLfZLLjd/2to1VAN3k1O91rawuWfK/qD3hKaCbQMrG5\r\naxCIYEWLujlq+rU0/SHwitpfCIqVOPB+EXMPKn5eeG+Zf5PHKnGpKGYRHhAd\r\nKwlNFI1HPuEAhQOaxjS/YxCCRg30WP/Mep5ebagG/17AXI1CB2/OPFkekQH5\r\nxX3DSlzm5yBofVVjpLrgvg84Yy78/1A8vvg=\r\n=LeAX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.17": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-collection": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.10", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.17", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "68b36ffd254579df9ce8afae5518cad4ba3e4af4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-aV0NoSTQKGPx5SDcapJMtSrBSJP1bhyi80uk6CZ4N+sOsSjys8No3RJskK9vd62dj0wZFqSUfmA0tfqA6f5haA==", "signatures": [{"sig": "MEUCIQDBgdZu9p5rRMp7yoaNhgbNJOj4erRksdxwp1JyNWUnoQIgYJnSxtWKMeqmy8jVmEBoGADvOnT/3ZNAY9er9YLyBDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRAB1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrE4A/+MQkdgaMU7LIsy+Xrve7I8aV8Tk9TqT5CdF9bROZBEA0vDj//\r\n47N0X1fMHMsO9v7mvVqD8vcfMfbDVXocod5UzOw0ti4cnSXEb1U3nqvWGkV9\r\nNQ6DAl3zOzNHAFnYmpLJb4YAKTUechvAqhniveIroOKMrUmgLSgd1y0qTllN\r\nlbr7BY9kUwIee/7IA2JkZbHI1TOOZpj6aoIS4WDRVlTKP2BDVDODtBWwIFCc\r\nHT/mWLyCSVie8Vt5Qr6/K6KmpvHm3WsBP/M08m2RmPPC78MU9UhkCbcNbLJw\r\nPmzkPqdLlMXWPpToHIchNjXeYfoId0XyyzuEurH37bxWzmCSi4zheXgb79je\r\nSTL35U/NYmZufr/Z3XhJC35KM8JmQ5n4MwQmoc+Rsk2JTKsxfquheftB/ePr\r\n6tps9fAREOCLwpquAARvQ3MUhK+Tlzx4Iq7hLnYtHoRKNOwSVGNEjEg+YbDs\r\nMtDFLLPRRo/jZ7EO0wpn5t8q7z2y9R2C5Hd0fM/3n/yRNFrYo7JN9IjeBhri\r\nYiYvy89BWDBRuyJ9cgVITTq3Pleu5ReN+r2+zVEUztFzzZKDfzAtwdHKGFJf\r\nZluPKd1tzLtow0FGaqoGMApYHxBHE+1TnV4cxfKB7/mi+lstyCBPUMy52WW0\r\nCSanwgTOmsxd9iZ7UlfDIgFX3FpW6YLekjY=\r\n=v/+y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.18": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-collection": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.11", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.18", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6fc604cb650c5b04d0c4adcd692bc6f4f676e4b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-0WSduxu4SLBiKdsEyeCKV7979K0J5K107h9B2rdP77DKC7CAeegz6n27DpQvyRF27z3KdyIZg6I23qoZqrk9EA==", "signatures": [{"sig": "MEYCIQCwL8hedBmP8gub6h6/nN4uwR8z0lXHgCRqnZRirMsQtwIhAMncklkz8CfaFj9K0yv8PUpUt4E5mg3/9hHVubkJSTZ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkOw/+LoUVawG/fmhPDPZXr7JDNahBJqL8sJ0AW1nmGVkwiZ5ebFdY\r\nm+Z6uJR5Codq9oqtm893ok85tP5jvGBYGqCK3semoqPijRsvtGaptSpwiV4M\r\n2qGjK8IDURpzqr4Mu7r+Nwku+rFRU0jfBGNLTus5lMLK/96YDF7FOUGRZbX6\r\nvRor0A6XA+lTcqtUCN8vBPuzaGTiMMjIV6ScUFYEcka4nWaTJV2KnAKrrVa0\r\n1uKJOMMxiMx/vDUtYXogIhiZb3GIWLXJw6B+G63UKV2w8xlcLSudUbhSSW9l\r\nylGOD40UacoZ50TCUpJmCDQfpz8y5TdGCuxovUvL5QLgcOca5CazW0k37pWM\r\nptDpIX6gq5aOC9Ej9tvdvttm8Wvxy91hDo4zil7TSdeT7TFOSPVl34YPEJb1\r\n81nD7mnEahdRSGztcYWgrAllZb8XunxPceNUIml/hlOTa7QnK2V+EQ0FuFS1\r\nD9zH7rdNKnSD+F3XyPM0TMDpXuo+DBpJ0noCD8Mk/IlVSEGeRElG85Le0+AM\r\nu65JZhm6tFqQyWaIvIOfAeYYIxytqftqbUIe3v7HHRg7eG8ppp1jxAV3LCT+\r\nIU7Re6+rAvzp29prIX4lQd0cOQekwlOTZKY4r4J1p5efzIcqO86NwxjYqSB+\r\nFXc2vRhJv9mUm4dansVhSz7XL31scdMBb88=\r\n=7qRj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.19": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-collection": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.12", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.19", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "12fead48f10c12a345e74754340d53f293c40aed", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Iy+z9PQB/g2rfJyCixA7+Qb3c/WY0TzvpzQswxNFDCBRF6Aq9au+GuQRliprsPpRpjtBTzIxrDWclLyye9xauQ==", "signatures": [{"sig": "MEQCIGwqN2DJ2/xYtjH+AHduZEtnlixgg1WFU0Jjo7sxSCeXAiAyQuSXN5HfcSjKiKsCCGe8JmT3FaYuPn7sUztdVB8SwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFrg/+PU2+K19rDiUSsY7O7Xkzf6AXRhBPmPpupATS1zJHe2k56b8c\r\nm193BaamzIkZYWt9UmvX1Bin0D96aFcimad58FAgam/5KbmO6NiFKL4ECsXQ\r\n6Xvv7+mmspjANJec0doO6Xkp4dPhQBH0x/oaPecxW0CH1RDBSd/ERaJALmlQ\r\nxkOP4QTiTB6caZCng5M8NwigMOfYWntBTRkeP9dP1OPFpz2I2QItHhGcADIy\r\n13r+Z2/iyCDkqbzB5IjOp3IE9aYCTBFoldgZQ+55ySeHpEGksQkj4qz1I0if\r\nmiJqWH2ykeJOUAVhs8E3O9elFobfLfdsaFsPtzoSb09gmT4PI6RJBb7HPZIL\r\n8IKhVQMOJTTFielQEKO7n5vRxm6cXE/63oojB8vnjUhxJ7WXswJxj1Vx768k\r\nfAlVLoJxgXng0nrsHncoCN/SV2s250W27HdYS5mOUxItvM3MqtMntZSC0tax\r\nqL9nRXEt03j/iAmTTPqZ+xCV5SC7BujKKF5E8ugaLYi7g+3J4D4+CfJKDpft\r\n17iPFQCW+9iHqqPWLABLvJx1sM4lrbvN41m2oKIkN9kKu1h+Hd1nPvOsD+Qw\r\nuljFm5tw2zWFKq2JVK5UM0zO5ztPaqTonx8BX7NYntuEDx/mf+TkAWsv0o4w\r\niYMylctPV1RoDaxX0yOujfJ4Ghya75SwxTc=\r\n=FOMi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.20": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-collection": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.13", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.20", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d35b2eb0d711f8b9a43d3ba12b0e12eb4b0bdf5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.20.tgz", "fileCount": 8, "integrity": "sha512-Z2eJ2TMgPxu3FuYxayuzS/13QrK/RAEH5lVwV44ZLXToR35HyDcAm/6g9rG3PiF2GMdc/6pshjQp2qbJWOqAKQ==", "signatures": [{"sig": "MEYCIQCuLgrP51Fi3ZMBNrgMt9PkDqaSsHdGaXWWxAkg1xSw6wIhANMV2i8lpUI2y2Azk73DpMnvA4exk10u3aCcF8TqaMJn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM3w/+JnJ32KjxfiYDmcg13wXk0JrbTvModWbdnEKqwyJCqTAGoNru\r\nqwhvt/ZdWpHKqpBTX0f9UAueEQo9tyhTN1a2SdxJgFHXgm8XWVT99g9PU+Dn\r\nwc9dbF277+VzLg3vdFj0ZFmR5+1H1TKSIkXti2IiN1PuBkmc75RVVFFUAZ3V\r\nlZS+FmSU+BdwuohQAlujqr+2Oq6fbH8ElFpI8CjmZXIYwNxqNIIxQDz3wXGf\r\niCTCXN4OKzTU5bBYjOpULzedQxJ16e0WVt+ATo/I/nNI36QGxhky0b0xieZX\r\ntfG54Y+wOgCOd/q7GRZljBZnD+V50ihisNZa4IguMgKD/VQKNpiIsD6YIj2a\r\nC0pU9QiRVcBOZ+IyyUyKso7y7kMLyG9m0tNARnpP5Ub3WD6IxkbYaUTobFoE\r\nEw6Xxxl/20YI1Q8ZqboyLYffwl/HJqwXQiAXYstsnzZGNtSFZwGkWQpD2WBV\r\nirJHB93C1PcJDNj393n1MgfgtPoRfw0mHqV7P6PzpjkHVGkDBq8VCNt/+Ssl\r\nYg5PIQVqV12+nSunjGgcpodIllkgiYaseauw2ZiDGwkmRjFJpp6v039uJGQF\r\ne0wmMZUjs5OmUeTmggMUAAOIXDzj7mAd3zGtRD2ZBV/nD2RXbudhXe0YcKGM\r\n0ao+s9+6nDA0rjj1hBnt1+ix8/s/6pBQdS4=\r\n=0XnQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.21": {"name": "@radix-ui/react-navigation-menu", "version": "1.0.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-collection": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.14", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.21", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6cd2838d06eaf2ec74c1fb089499d58eadfb6cf3", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.0.1-rc.21.tgz", "fileCount": 8, "integrity": "sha512-D4xKgfPJOB2p7H4ry7Ufo5siclNcNHwEZ/Ekt6Y/J/JNvvQ2F4WhMWLoSphWaLyJEYpp/OYDoPFTxCawgTVHQQ==", "signatures": [{"sig": "MEQCICjjHkRippZDCoYsSWZeaoc4BzENLeR/DSD0+MtXjZP/AiAF+/9kgtnIf0kb+gOTc0OwDeuyA6YXxDXlEyOPyHwdsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqw9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz4w//VqCwSkKDSer85E0wvO30XsJhizcQJRo77LWiJpWAwgqiWMlB\r\nKgz3YitK6w7wRiL5dvdeMIiEjpuIwdOke0ezCtf7pW5CZKKBzjftq+j8FArC\r\noJlnfEJA2JpYV/PCrHzOayX6TkF9L4bJqWDn4jduszi7pdLZLrK1m6u+XDTw\r\nKbPXMQ1++jGvPtvUyEf7Xgb1w7KJjkBEhIbsWcR65sngfDeUX7U/NVE6PcdT\r\nWMj5okCfyJY2DV622jglCz7KUR0HoSDtKIlzvDgO3wbPrxdKPqI9WBEbiRNM\r\n7e+/a2ns+K9+L0oQIgCpoT7U3RMTXkXMxnOQvU78Ayf1hp2NUQmVmGkLNiLA\r\n4tfimDamtufO+KuWktHw8B7aSP5wMg4lomwOKLDZws5IX63xZbx/zKaOeAGB\r\nc5j0OESPfVfb7HD4z7yc6+lQ9MgZ7uExFVmxfksylEgULdXF8przDB8taVet\r\nfdC2k/UN9UA6/hltAn3zrpJW8EiWAVZeOzfsqKID766fJiaFDuXQ3aKKYyS+\r\n9JanDEhYvEHVwTkoU6KqUfsuQyxHAA3imF3tkIaNDQNXbxlZ3pescNMg/B4r\r\nxoV+0/QhiN6WjJGjQK6Ri6OGX2AHsoMGjtI629ER0ByyU2+httlcPFYeBTVt\r\nTWMHM+3SiiKvRtvnwtmKlor57m1gpsCXwqc=\r\n=d8PN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-collection": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.15", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.22", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8053d706cda5a5d52aa9548913de07d493925a7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KVgA6oG3bVhF2wLrJ7X0KGlcK+RnhqKw0UTc4SByrpKbH3qXIEaeGm4SJcWwxrF2X9jaFSKTi4fl/PYSIgIlOg==", "signatures": [{"sig": "MEUCIQD5L/Tn3PQTx3y9zm2WUkPbsv0ealX5lrUMvbq1NJudqQIgE9Go0YghLAXHDznJBOcsXC5rIZHSu+49A4rw67rK2Ys=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUnQ//fmO81CAe/EEvW5bl9kv9M+63Z1MktHd+XdtgoeyH9gQ6q3Jx\r\nLtgaWi7OGmUPv4SWN75VmmmfgRll2FvDRKwpvVV5QTlWroFUZ4DMHJJ6uqsU\r\nfGwak6x0Uoouz8cNwomhyzaM2hVFYCoRnDZvfLaRdCoTlyU/C6EyJC+NFHWf\r\nf3udwRTlagv3920fyrBAyP06LCaD3J8HPwCFnLPl/NWVjQDEl5WRsepJVvjD\r\ng0plhq2WLPa3LitX8un/brGQZ79SyMFpcCZIKzeujPftjHI/7UBKws8HopRj\r\nutWTOVvOXcuPPBFnP2HepyMiKBaqAAK1wkXXZHV9o7w8xnLihqOy314QF4B3\r\ntrr6yqW8BYJIMaca6/oaXvnwXhHtNPnkEdQ9isf9YWTsntELJ65y/xCgYe1I\r\nWS7pDoF6kSjnqQpwArckugXDAd+Pj3cVo9ABDkMchKAynqPGhD2jy2u8AXYQ\r\nhM3dSxpJJ1dXBT+N+WX4XByjRJpNF0VtZ75/SOfIXUSrlut2S8RrB5+LG8Hi\r\nURok8cKegJfeXvbTuy5yWNSBk5+dVInHacLr+JbtzqfqjvkvHKXo5L9li4qK\r\npdMT4HC9iWEXuH6DN4bKizK1bb72xGK1Rxgr0bcXLEz7p0ezdgU6Q3h5Sr4Q\r\nVna4OR4hBdgQTsHLQLHaj4fmtfi/GE2Wcf0=\r\n=hvWI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-collection": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1-rc.16", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1-rc.23", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "efdcec6d4ef18255a644572f0a5b9d8db70ca2c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-+IeME1SLREXstkJwvm9EmAHU53+lYc+AuJHe/tLJ1q83Og8qPXQbSE3iWU1jfSFZDCvPscr1Tt2iw3FsLutcvw==", "signatures": [{"sig": "MEQCIA3sSTRCoyVQehafxsDlDEoE1DxPaxfYBhm55W2PRB2sAiARMFrBZkzc707g/VPO8XR33MRBYZUPnB1GTQJq6lB/bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295732, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRe6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt3w/8CgfAJYHy46rI4y/QCd9UalC8gjGUmZ6L19/DfJ/r4eEumJZW\r\n8ZZtC3q++FTEdW7OK6RGQ21497y6HQQuevjRoWgTn8veWDsZTc73ivkPVlX4\r\nYtb/iwD+G8JbDZZQmAJwzJz3yF1qZZsBi4k+NzoycB4tYPA2LV9PZsdrKfF8\r\n+W9+GWsNgRt2VEf/OET0QkXxtWVs66ic7eqf+/EG4bTHEkT0zFgGAGoojDfw\r\n6cWCHFteugSToRcX494Ocj0K4ZpUFRXWNMGGWweu4nsWtfFR3ptN6JU5pY9G\r\neAUxzz4o6FHeBa/SHgVABbsvf/bEVnQDX951BO2/HeAYf+pwa0qo4gcBz900\r\ny/hO1E2TS/S+LgNUgeNY5pFDAgDPFlC7CtoDHFDJsHMlEnvfZPK4/cewU15q\r\nM2JNSevVAl/B8S+ejKwReSbt+h6DsYYXJ3kFUXMZsnHeKdWfzrfHjS5Sjgcf\r\n2ZF+ClGDgUanYB0C2Dz1Cqr63+d2AtRfB20qNx4NqxkwvD7u/8r/MqCGGr5p\r\nP/PMrmpudZnlKTbiQjs+uRuBEvkQs5uAkzsE9aprdhU+j7FZghqCC8Xp93Ph\r\nQPFuHvGxvSpti7o/osiw2BQxPycAdignZwPPQE44uZX8v68OYqYs0YIJ6wXu\r\nasX9/Fak+zasp9XmeJAXhku7uM6sHv1eHKU=\r\n=CXyq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2af067255fab966642853e063a2cd27c00fed42d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-qdrzeKp5Z/iQIJAfTQucV3Zjs4QzDtL9M9HT9fg6BX0KF65DcUtyNwfg9C28AOIKS/RPYg3eSyvp+wQT5HP9BA==", "signatures": [{"sig": "MEUCIF5Lu/7HpZa4/EPqhKakmWLrZwETT+kAjBJaSsrbrqYvAiEA+b8NZwWyCkZzhKwTPg3rH3KDU85wKzQoB3eXGqasAEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRuxAAogom+Phv97WuYoOVT11BRX9/T7R+3XPRXrSr01Z8gvbvAgCh\r\njUpDyOT1xIRNMI8+Rw+jubswigk27P3YScvqwoMvml9jNN10A2mZEtTE0lCc\r\nMWyHeeRiE8MSUho/z7xlS+y8M1Da2jukgJ10DxPcwtHQR8BbuGeB9cLuBLsA\r\nQWiHBLWOyYXWNZdISlZ13vZ+SpAHE8Gj676dWeQTIdRhbe/FjnF4GIXJCKOh\r\nRAuBszXcJ7Egnk/8d8iQMbEIWHeBdQRxZPqRB1HZreH3CjssNIGm7IsIv2lq\r\nIjcRdCmGgoQpE0IITyjgF/0xvgzr+iedQDhngcwYPGNOGvaD83X3l00qOt2S\r\nd1Zsf8GeeiABVma3W2YqgjX0t/jLjDh9pNdeEl1L6k88Tn0zoEkpl9ZDvgrf\r\njxL8cLH6ZKYRk8ir5ufqt4oOaeWpMqcICzusCSkonPCPOPFoAKoULa/kxnBD\r\nzOrnPUrItqjqDzEpHw1Eky6bk8Ew+gMbglCnKg3RmnKtZDuj1jU3eWLw0gZF\r\n/12pIBzhNtc8l2+sqqHWuKZxFDpgEeOY6Y0NESGbkQavSMg7XOUJPK9uLYsx\r\nuWznO/elWJ1AorR5lMLWUxefjJnvJDpuNZPyMdOIZUuXdK5d0FMD0Kwe8M5b\r\n6TqpVNE0oETelHfwhmlnkbcAwUrwlfS1H58=\r\n=bN8J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.2-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "367ff2a8235cfc23a74113a09d0cf317a230303d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-gi9cZxtYK5FDqAO00QRRdcBGZJRXo/GUrgqZHSpklLfjG+FrN7jHsq2IY9GfemO0/BHRT6zZriLBBosxuixLMA==", "signatures": [{"sig": "MEQCIBr5Es4mD+ftmPUD3v5vsYgO0wVOf2RUfN4JbCHKEzM8AiA0IQQWzhD9wRGlu+PEsc7a6BaK4MtIqz7KLha0eTxmKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS9BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoRQ/9EYwYR/ZDIlFxIXmWmC5Vw7BtWYrTDx8EodZmsRDXHMrZw9R3\r\nfBE145ydBT4fGN9bN9NazzOe6119UHvRAvHtimFntBoyJO/MuOJqc9UlnSIP\r\nh19fwZpzjDDEsCOa248l24cC7ATGOgDFg/qaKHSFh4YtJLW8lL/TwnRi5Z7i\r\nPdEhNybpLNHriIvBz4pucA2aOiCyDUAGhsCwK7WVy9EX4ImNXxob47YWAAAl\r\nBbN1EBU9qx86/i2edv5sfXrew84YwX0CcbQWmNRc2QHalgOeI5OCTxpZDuMH\r\nut3xwHBAtZg5Z11O4tlVz3dieaPc8lpDgE45pMYD+qqt/gIuFx27K3sTaGeL\r\n0GUAlQez+MNbwzh0ssJpSYjTFDF6ocVrDSxFvbgvQ+tpB8Ne4RLb6eYC8dO1\r\nIsvw0tTn9eqsOkGRIZvUZ2LkYU8KxjUKfNRSsqcrl583M+OpLcQFW1odVYjQ\r\ns700C6uTUH2dsm0srErhde0l51XeqYROJ7tadQi7XgQcs4JpvfjS8B1tuBZ3\r\nuIpb8TLe7RjlOTGPZ86efc663AiaHmSeFAL/cfPuMNgjnfXIKn0B9kTJ9sj6\r\nWf7YSZIelvC4fzcHMJ9OpFs2RZXXT7hthG9pjhNEEs8fhzERKYGUsiSGoJNl\r\npmC0VCCLCoqZR+S/xV+MU4PzGM7cAZOHIpQ=\r\n=4ayT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.2", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84f24d90e6448a0c83d3431c6eefbea73dc7522e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-Khgf+LwqYfUpbFAHcFPDMj6ZrWxnwCgC96liLYwE48x9YJbXGlutOWzZaSzrgl82xS+PwoPLQunfDe/i4ZITRA==", "signatures": [{"sig": "MEUCIBHTHg50PIfszJTmO//0ol5xT8rK63E/iM/p0N6wWPF+AiEA9770QbE6HhSFbyZ7Rw5YcFiVbFby5cscDt2QGI+ToqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS/KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6+xAAiKcdRITfgwh9a7gkOLfRZUOMvo+zbvQtl5D3fBbRcEK4JXl1\r\nqewzO+rS0Y4/Hx+AIYycVfI7ZcUjxIcoVvAF46Zum7ASeG20fRjqqzh9E1GM\r\nLHtS2ryQeqkLDhgDAv8wjxxWfVPT/9j9z+5Lpd4lcQiE4z1pBGxSYOD266EG\r\n5zp8gEsZtcZYocL6NzG3GNoLNxS6n+0E6j8+c5e6Vkyb0Jqr+l1S7Aqk73w6\r\nSrWp1kJJmMgD632ec3hj1MIPalX8b2SvwpwJ0Vlpg5IZS471aX5hMrog2X4K\r\nFcPZcpF/b2X5kU+RXf44F8eP2jx6BIQAvkcxteYLuE92ii483Gyo3JeSakK2\r\nCj6s7kM3tJYh1liWNj1JqMe2IJWCVW9okAFJ9n2ophv3Zdp9BocIX64i3eIo\r\nF+drECFP4El11p+QPUSEHKYkTtCcVKPLdcfYt5dxK5YtEDcbfoOLSMAnHseq\r\n1dErkzp8bFeT4j39JUkjfQCHD0SPdpED49C/NO3CIFKuLxflCMz0cNLeukq0\r\nMG4uCEY/HnsKt7XD8LhvbyRVN1C6+pGVzpL3mrB/8TKAj55uDhNr2kmjNGWR\r\nNqKS+Aec4Q9QXsDfn9E5yugcAsx8GBP6SO1fsWU7xXm0pHfT0dlY4QzPZ4HC\r\nKw6pegj9kMb7Um38mE+hDvgcfjCCNAxDoY8=\r\n=Ai/J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-collection": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.2-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.3-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64388a5f66d944ba1f4083ecd311b7bb239a65ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xN7DNl55SZPL7fL87OeCYPrLa2zF9/lsNxuUtj0VNmz9DhSxJZyT1sCabSgsjKECPwe8QXUOpph7wS0csrRcwQ==", "signatures": [{"sig": "MEUCIAsZmpTsWDa5n2YTiohKY8w4nNqPUwu74ukukBhWBE/WAiEA4NkwGnMi1qJMuido+ME84rLWfA/Z/4vS/lX/6eYUDnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAmA/+J2GqyyiIvb63rgx/bHUiGawK2eTEkvxmq3bj4Y41ezzpMuzO\r\nYxhx8eIzTs+pVJz43JybSmuIoiGwf27vsS+7KnAcG9wROkopFCVPRDmq20SW\r\n6FJSihbmE5QD+1BLoyJr4OSk7ZzH7rT5allMKURKf1kJWfSDbM6935RIDEWi\r\nxA3gRhtFp11Ugzza45H2AteL7tgJFOkk2/S/g2mskpMj1qBfXgIAyX3B7ngd\r\n3vCg8Y2Y7NWu/ulfRmvcSxGalA5/q0MQqTXRYWE5fWxE9xjpjIIotTmseTH7\r\nk90AnwhuUiUIROmIeyMgWtNihEK1Xjeh3TzDT6UPvojdSxM3gmdTkJJ56VpE\r\not994ocEeFMG9tmFPZcyVrIkI/2OChAPdnj/I4WazW3n9WZK2QMyadzZbNem\r\ndsq7NEOkWGlXn/fQZoykD6SI5uMLhsRZL2/LjDRpPrfiRPYS/Hp/SkBbXfjj\r\nTU4wtOV9Sp/zJcOJ77AXSPGGCRvHwIsgB5Qi0zyyuYqkxFVDlpVpwM1JhJns\r\nwLS9e+WYBxRBBw3E534Fv5tAIHXDJofQlwVfRaHf9asoc+idrUwiFrqWVQHE\r\nKTlXMA9xQ20ETWGwYSRIIRbWEtKs3AzJMwideaFyEMtjS1mePOOOaLxIli8Z\r\n5wKrBhw3IbyVl3t5H9O9qB64sVz6KaGTTG4=\r\n=AXtz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.3", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ca5ee35ddbc4e57a9403be92f9573435173e35c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-1fSjOAGTThYSgJ5pENG2V8we7+6KDbfbiyt66HmLTeo0W3PAmmciclm0o97VlcVZW7q5Vg2hN7Cbj4XKo5u2sw==", "signatures": [{"sig": "MEQCIFdBhvG0UvMWBVz5XxSVoxzjU/CmoziU0oew97K3lRAHAiBuUDkDyAA1D8BadVAyb6k5ZffbtMwh2XJW5Ig8cS0aYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJaxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/Kw/6A7neHziKymXJD26Hvar+F7a3llQucLMGowae86TsPe4ETnwB\r\nMtnHT8UZoKOnep/7Zj1NtgBa5FiKRvsgNOi0ubblsOLlTJnzgSkwX2Ul309B\r\nbLDb3WspjtXKbVLt09ayKLY2LrCa5xnmB/UwqgMGdqgWEMRWsp91hIJiEZ6B\r\nZVCLd8GqSYblibUSsOcKT0IVq+yq1Ox7wp9eHkdXTnJJPRWP5fbdQHjfDloT\r\nT5qh7W+1VaElkQe06fSRmVPG9jgVklnuqKYHA0YO1sv6/2KyN9aAm9uUXGee\r\nTdachfJqLYwivCjWRthNgR5KOFi1+l9D+Bvb5V5tB2CMs7hNTwr/QTel8Pb6\r\n5EUZ7lEh7V9k9xR0hrudouovC1XRaKgsjaKsvjnvhHOVPJiPeZgbF59QH9FL\r\na3ikyGfYsj8JyeD6JVgyxrJBYTPA++db4q7Bnt3m7QYj3876KbsCV/L/RtMk\r\nibBiV/YyZUCA8v5v9A3yvMTAR5W5qqihTsA1QmqoU3nXCl8hgkBXVb3bzyHv\r\nJaIsW3uGF16nVtLgZStR5G0SeqDJRy1LlxnRJNHa6q/+WoO+yXUGy8i4TbYD\r\n4Ks+DA2scJzwZAhwiGYsVdjSgQxzgYppiUrys1GUW+tusfIyxkAdY09U1u5y\r\njbcb16d0Dygey5KMOWHJkHIs7bmxMY99u1M=\r\n=pRSa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.3-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.3", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84a4e04b118a174c75c5fbab78f57f8d04035383", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LKRxfbtMWF5/N8OXMTycWACwig5DNk1h7lHp1nUWdKX+vMuM6BoYxMJAKsVHYazhkehTw6pKEquB7hdQaNuyFQ==", "signatures": [{"sig": "MEUCIDeerQTuXJlqVz53V7ysaR2H72hEs/01RXv24boVk175AiEAw53rh1eH108U3QdIPHqG7zfDYDgkzESogSOF6kHVQGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnnQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooCg//ROy6UGMcN4hvXG0+E2lhqnU4qqxmFX4gNW99BmLvYPP2yCUi\r\nFf+9dl50dqishmoyS6tO44dsV3+AB61jgp02+QyLRnGrg3551ZCIdr2zgGW3\r\ndFDfD4wCHxgAsw/GyAEvLfe1HmJutOFBVjg8EmZDXpnTJ8ujeuxMQZC959je\r\nrdIqqSXbfyxklUAZh3fpIq1HAgeZaxByxzJ+E1G4IUNJCfxwphUR10L7XzKW\r\n0gTCPeGNumBiqXCya2LMS017qpmzj6F8nIrC9zTcwAMhGt/UfDju1wX+f6yL\r\n67m4ryd2vsyhJPIu2zy+SunFtgPLdLcQbHDkzOFX40X1dt2Qw0Sr9u5JfZWM\r\nTbY4hybaJGOaKYYxKEn5n6aBpXqXDzyh7G+cl4zhpivtGccW/p29wIJh5mYb\r\nkNMDwjQ9uFFgj6WbjJUln7vyYOaCJmJCRMoaC/nfUpn7qSBFd08w/M+OJQ84\r\ndLW8qHdaBrPml6AAcmwwQmhrFo5n6pi/R+syJuVHHmtu9chZEWb0O8ICqArF\r\nERo/4UNOx/QgszgkFMElURC9ktUf44zGgCwy6O8VcLzo9fi9PYatuFLwhXuM\r\nOZ88aBy4lAM0YrZPOuWx3SZkfEGKM53QJvbGtOWipc/qXd+Tv3qfZVsCzCBt\r\nuNYSOcm7sqE2VziZ9e8aDfFBtAToAchdBQ4=\r\n=aKq7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.3-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.3", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e3f6c071a613438ad08f985a1b8dfcb4c966c04", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-fCfasbpwCHQWu6hznVVnxLJArZrXl2EV/5UydmhhR3vvExcaa+lQBAFQhSdQVUPBsUZWRYz2D4BECUcipzz9+w==", "signatures": [{"sig": "MEYCIQC3cOHuNLxNH0idnE40ZpUjhfT8NnEkUk+oCdnssd1YfwIhAPHnc8Yls/t825jEqwIGKlPKgduEc1+ydCTC4YKT8dHW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/kMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP9g/9G3MveoBC4qyQSwVtfkM+Y8a/M61huhuR3CfrWiP97AG/fxU8\r\n23CNIEt9+UW6v1Pf7ZHypBRF4h80U7ZD55oRikc3dw1B65FqsScKFyrPy0Ea\r\nCG8LRoisMR5ol5Pfv1JU1J5RIBccLfR5w/L3vLECE5gZrhn/v9AM6hv7SA++\r\nWi9o1Jow/BlpHoj93qpUqebhMIigJAE6lY046V0wYD4cbs0b+43wCjMQJ/ul\r\n6h67Z5E1kKnnI5F0LLKJ5bbe7Av/L3ikELtLe5WorHMGvaa74CIaHq6mW0q3\r\nlC10BpRo7xmsvhfMoSepTufDsmG6pBpHvCcHge4pQTe9bsoLir8XhpSqUg5F\r\npxs87uPaS3PNZwx4bW7zkGR/tl43LN3tRI9l3BWpNo9DDT4W2yuGb/qpnemC\r\nrKhJ8hmSfGTf/e1UVsfr9OzIukZuvkPJBNBqdz+jSZRciM/lG2AZXPGiPuay\r\nwxM23SEHzH34o8zxDIpsQ0NazdWaLtCm36Fg1J7ikqWuSncxm1DHYRMTPgJX\r\nJBHE8wn6X8sNzlUib74MIe0de4vhp1oY5tJjNnBMXD5hZSLbcFqpGpl1EpB/\r\nW9TDrJY+a7gb0jOD6IcxHkhjbcEeSiHavDUgCkqVs52aBNBTzkhdIyCbXdlc\r\nAgUJvKe/lHuivllMfn1EyMMuALyB8lea5dQ=\r\n=/VNg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.3-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-collection": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.3-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.4-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2a98de7469f9ab1acca259088890ce0505da4eae", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ggC5qrtA3ZOglc2u929OY1uI4KciTCe0Qza3VfnPNO/3SeBExlQp78eOGIr/dRKUUYyfaQ+UcO12LFA75K8eOg==", "signatures": [{"sig": "MEYCIQCRecgGxg91knS1nVwQc6qbUNKUupdXgL1wnt0XGLGFWgIhAJ99/nQtfk4t/ABwPWvgevkP7WbO3UfvjmAI31ljh9Vk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobaA//SHbaZfcW8mu2/myUo5t4QtQpVD7ZFTcg6J+uou/R9T75/332\r\ndKiaCNS02BdsE8jmeZRJWjzdVDuSTXb8+dSkTxrWvSp15yygV7vYVUpy3t/J\r\nKKikuIiXz3NHLDRqpsXKhdPAk5XwM9ULVIM5rV9Ld10DCbSaerql66rAKpHd\r\npXV0qXPF9VLjSizJlI3aCktNay63Ghz9ublyY5F/a+2whvWxaAH/UR60r5Nb\r\n8LtOjNkWnlnNM3UemuDgKoHtnkAFV4pZoO9VCa503TH0s/WzOenE4dQVeBWA\r\nE23P2FTrzMMYoRWo4B3PXdsqESnBd8P4vutN2TKCDKAY9lMn/Q1x3SOiTwQk\r\nQHg3pINEdslaZgiUDN1J21JcqxiiREBWzd2lay7kRqXHzAbVMGGbBKO4mxjS\r\nqNJ8Z2uniEa4Du+iuFbNAe6xUx9qGgBVbxywgwM/P89lkQ977tl4dlsQM4W6\r\nLRZ5VlfBV09Dyo0wJt39KrYfJIEaK5cz7i+Yl5ZCMjiyXwD1+osmOJrH2IDF\r\nsMcVK3j2SAV12IpCypOHy6LQxGlQDhIW1pUVuzvLKl/jDSdMYt/n/xiV90pb\r\nhfDOjW8Xlh46igD3L5xFHa23pXBG36/xnr+fLIvPNgr3Oi6GSna/SO5rK7BO\r\nJ1olihf7C40RAt0rtyGYuW3bzNV4ibWZgBU=\r\n=1ElU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.3-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-collection": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.3-rc.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.4-rc.2", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b2c9e689e7a27f0d05e39ce4b38bcbf11825b8d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-HZzh4cckb6VE1yfHHlOY6W/zamHZIvv4iE86JZMhN+16yutFrUdjnJ2pKd1j5W4yI1MjAY68SEAf6I4egOK0Vg==", "signatures": [{"sig": "MEYCIQDNkgfZVoVDY+QaD/8k+A2YcakSlvaVYjtB7bjn83uVlAIhAOLwHhGZK6JzRp6Uxk5ljpLsSF+PkN7wPYQvAkVgs2Xv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295866}}, "1.1.3-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-collection": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.3-rc.3", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.4-rc.3", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ecbe06dc2a2ecad9ec4c9aa094e1a0d8d2627796", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-XpUycK7V47W9w1rB5WgrebRFgo14UkI0WQTi8FcwyDJrO/NyfXkYXOyinZW5ySD8X1bjVfxr7dATjJvlVfI/lw==", "signatures": [{"sig": "MEUCIAt8jHLKhpGrxjdym3UMscEe15N88ldt9LjLOQgNGXJ+AiEAhf1OpWcsOXvaoHSDKlUlIvcgbRLXDHXK9GozJyFr9G0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295866}}, "1.1.3-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-collection": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.3-rc.4", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.4-rc.4", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "797f133c2479345211134a39661b330b597d090c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-he69cR6I0MxbEv89hd71kLwxPVNBprzwyVEus20eRS/FP8Hb/VP2zH9+D94wk0P8h6BEPgUJttJfRisIPKAAhQ==", "signatures": [{"sig": "MEYCIQCcXb5uK+CjCwh53EBchB7+NdkjlmEkDePZfgwaCVeXVQIhAMknSVGz8rB/D7TV+HeQqnuXcAN0FlDYxEVKn652HEzU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295866}}, "1.1.3-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-collection": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-visually-hidden": "1.0.3-rc.5", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-dismissable-layer": "1.0.4-rc.5", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3c0876fbf52dae0b7a2d7c64367e54001118d0d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-VCeapyJRXyB8nduGI1rZm6QcJAwZcEttbqK3m33kmgr6PGMtWSdAHxZvausvgfSQTiQ3o08YBSLOD7a96iNyig==", "signatures": [{"sig": "MEQCICLpatCSMdJfn13snN/wVySG1r8BaxrmmnFf0/6uK+OUAiBliz6GERuA+EN82GtmfuIG1AOV0Dn+/H+P49fV3WBOnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 295866}}, "1.1.3-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-presence": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-collection": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-previous": "1.0.1-rc.1", "@radix-ui/react-visually-hidden": "1.0.3-rc.6", "@radix-ui/react-use-callback-ref": "1.0.1-rc.1", "@radix-ui/react-dismissable-layer": "1.0.4-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8d8fe1c79ef10854a82d7fcff2c8a8ca542e9676", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-wskBhmtVp83H2pAnvTP2QzKnNV98J/qgwsn2DKhoH2GSMKxraEEbTxg+BzLGATL8xCYjtFoADiTj6IKB7ZYKuQ==", "signatures": [{"sig": "MEQCIDWGqgoSWeruT4HilD/mmTFgEaZq+YCSKZZndbOmwmt+AiB5NlRyELWR3BQKi9azUqsm24OALlv6qe3fG9gL94EfAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303021}}, "1.1.3-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-presence": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-collection": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-previous": "1.0.1-rc.2", "@radix-ui/react-visually-hidden": "1.0.3-rc.7", "@radix-ui/react-use-callback-ref": "1.0.1-rc.2", "@radix-ui/react-dismissable-layer": "1.0.4-rc.7", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "decff82379620510994f2109bccd83bb602890e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-GN4VT+wguElrydp9vdfxHfdNmyz9H3uwCFAvuutc5NLnsv/bRi4x7APFWt3IQLin3YH35nCgRvO9o+FPxcJFXQ==", "signatures": [{"sig": "MEUCIQDV0mQdQPNcn+CwDZ3x2E2zJCqNB1hZS2SnVG+6kKPG+wIgML+Jbj3d3pFX4nx1qMduUewUzy/QLJ6E4RzMrOSofGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303021}}, "1.1.3-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-presence": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-collection": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-previous": "1.0.1-rc.3", "@radix-ui/react-visually-hidden": "1.0.3-rc.8", "@radix-ui/react-use-callback-ref": "1.0.1-rc.3", "@radix-ui/react-dismissable-layer": "1.0.4-rc.8", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "48cee8e77fb97cb3038c57e03b9e2ce04ed22b88", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-SAnQJMA/NLtYMZcjg2tDma3hn02xJ2Tq2pdA4yNUVBQJe4VTxWRbnvTu6FPgM/dS3T0BC218uR9yk3za9yDMnw==", "signatures": [{"sig": "MEUCIEpfsjNVJGqsJ7H/Foct1r+br8ZAbA8rMFYmNIaDMyzqAiEAgky2eL8uj7s9ww7O4K3BDjHIQ5SeLEZLpzWBqt2Q3gA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303216}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.11": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-presence": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-collection": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-previous": "1.0.1-rc.4", "@radix-ui/react-visually-hidden": "1.0.3-rc.9", "@radix-ui/react-use-callback-ref": "1.0.1-rc.4", "@radix-ui/react-dismissable-layer": "1.0.4-rc.9", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8947a2af204e9e172de91e68ad3da9d3284b38d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-fHy0gx16Osz95lLzP2bQYbW5W23V95FTV5fyQ1BBmZXaCFYuIC4OlwWdrJWbE4xKtzA5ofROYRnhDnl70YHPOg==", "signatures": [{"sig": "MEQCIEREIj/+G6aFCV3jDM6fjTQBjdD57NmKhKixSbDvfwwrAiANE29fF/hSTK3PyOQrfKKqxEc3Ins6qQIIYYvmcqBicQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303216}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.12": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-presence": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-collection": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-previous": "1.0.1-rc.5", "@radix-ui/react-visually-hidden": "1.0.3-rc.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.5", "@radix-ui/react-dismissable-layer": "1.0.4-rc.10", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b27b6b1f1ccb8f9155c43e2ddd32c19baa897f7b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.12.tgz", "fileCount": 9, "integrity": "sha512-zBcdQ40Dr5LVz62IWvFteP8Y/GPp+5KGNTz2wf9w5JHXOowVWNdaCVsxN/XyA8b7yAELPTsAvKcGFYY+rE/LjQ==", "signatures": [{"sig": "MEQCID/45TuMXuyYpv6a3imvbdwsRKKIZIz+qDXIbqGivHOgAiAbStrQAcRBmaWhk25YhFGzP6tyXr5c5Mp0nodlCQ13LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303220}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.13": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-presence": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-collection": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-previous": "1.0.1-rc.6", "@radix-ui/react-visually-hidden": "1.0.3-rc.11", "@radix-ui/react-use-callback-ref": "1.0.1-rc.6", "@radix-ui/react-dismissable-layer": "1.0.4-rc.11", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a57929b32fcd23513cc8782d1a23b11eb7ebc5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3-rc.13.tgz", "fileCount": 9, "integrity": "sha512-xYh8uwEloYkmbkrptLxCAk9ZxYz0/IZg7d5QccMB5g1EtIulBrmsVpPs5oTdRwg4fvgw7+Qx7mknUo4KXrr1sQ==", "signatures": [{"sig": "MEQCIFtNulj5n/01K6dWH/nYpvfjk60qqc4J5IueNfxDFlvsAiBuPVCv/7+TlSGZ6i2UbwEoGSLG1owp/w7D9IGeerjglg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303220}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.4", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b0f078226971b56b0fe2a9e4dde3cc2abfa731f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.3.tgz", "fileCount": 9, "integrity": "sha512-x4Uv0N47ABx3/frJazYXxvMpZeKJe0qmRIgQ2o3lhTqnTVg+CaZfVVO4nQLn3QJcDkTz8icElKffhFng47XIBA==", "signatures": [{"sig": "MEYCIQD1+92extiugo2oMy2HIr/ejl788mLSx+hG0Ar2KT8y2gIhAIL9DfL1HZ28RP21Q35GIKVH1bLBVXwmLr6Nu8hFQGfn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303112}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "675c3911d89471486cb0ec7b123df34230a2fd4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-JG6VQ4/GjMeKEwR8W8Jp5+dtlAmyGkVIvavT/upra/5b54xr54GQ21GOO48uvfHpq0RhOBHK2ZfOBXhvLnDJgQ==", "signatures": [{"sig": "MEQCIE+1Vd/N4Z8uN4lIqULII0ppJz9s+0hLGuXHF9DAqy84AiAzCFqAftgDFbcAcPQhCvLPPhK4wmDA3ciYVbXPcqXFEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81cfeda79434909da516e87e92698655540b5e84", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-i2CvyW7dVWzLB3EvPU+SZihNG0QX7Jk2eqOVG+tYlMtkk8xsXMBBIsuMV3F7UTrSxVJ2vQbN7tMRPIKKU0ceSA==", "signatures": [{"sig": "MEYCIQCWcYfjFR24+GCrUhKdswaoMC7IE/2q8UslCBurjpUr+AIhAOZwxinTyoWSX8G0M9a0/9tyqEFt/9lN4C0JeiEAbnOD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "127ebb5e57669e0dfbe1b69bff4ec05138e97888", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-MHQTpu8OiuJPU5UVk+iz1Q6XP6Xh3e9q3DBb69+yfWwXBRIu8ar39IZc6oEsld45PUVn+TFCW8WLhm23a0z4og==", "signatures": [{"sig": "MEYCIQCfzrzCnZf31lF2EqiHOwnAaII9PXeaKC59zZrj0wsc5gIhAL3Mmes+gkrhbsjMxZX6MoX2TVTUIySvqZKhLCQpZ2Z1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b251ee7a3189ae254ea71249d80f5f83beac69ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.4.tgz", "fileCount": 9, "integrity": "sha512-sFBGZUxXqd9F4j26Y6TJN3NPsY59QXvB5HxSv3w6CnidxD+SpcxmlOc10I2J6eztKI23XYNAnj61G7ep5nPumg==", "signatures": [{"sig": "MEYCIQCSonKY9zA5q6pHdF6ii//soRz82AuL8DEal8E4wHAFXgIhAIy+bxz6N67R19QC7NoI7GUV/Zx0ueLl+8vry+kk6tUT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8b8754c61efaf3347dadeaf150ac389e35cd8208", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.5.tgz", "fileCount": 9, "integrity": "sha512-ecJn09GnBdDWvM3rgx85acnM3NJ0bkWTlkWkd629FRXFzinCp1Oq8SEiVQspnfKbJYk3o+TqY6SqlZVls8ddrg==", "signatures": [{"sig": "MEQCIHioxJfACWUllOdHG7ijoD3+6GxTsXVey0+JhLmVbT3yAiBboKDQNrXGNXv8XVDl2POyefI9BNlrV4ypgoryY+q/XQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f26e223a066ee0a61abf172fc2fb731f2b94228", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-vMDZ1i8xYeb/S3OjuXraNDwiNS+OqZcsXC8sAYAiJ4B8RJo8XYXlIglzXesoKSG3jZCw971rldtD4KCA5PQWMA==", "signatures": [{"sig": "MEUCIQDmKN2xsOPZXwhFfcz3yFEJeVIbXLMO7mDTJuwPZs7u+gIgYdUi2sO1osr3u9qXhbY8vx2XqncEbwPcyfdYeEbWq/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.7", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d880374e5501f45b1e86b43cd44cda533bd277d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-bCdoG7+HuncsiGGw3X+aV8tnI9517qmCUT/+4LacPJ7zI252snCl98v+79w4CYpPQYjDwna3a13xL58DczkbXw==", "signatures": [{"sig": "MEYCIQCxlHZL6EFmiBaDMDt6gkZH9NF/MybQVaDu2fwAp71lAwIhANY4XxmjnP3Bw64Fq1F58zp5XGJbCUwLuSXzASfjwAeF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.8", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7989d1f8bb0951c868d6a56fe91ff57944c22a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-Bf/rseqohcPIXRwGLhI3k9t+6hQXuG7snYjJlO4xyDNZbAlvzZWSCdVCaYD/GgRC7G+sKGASf06ENWbz8sTOmA==", "signatures": [{"sig": "MEYCIQCba4jN+EudlDuIam/5hgnuWB4f7xUpLK8ul2zFmyvUXwIhAO7otEniWMd4SRpq3xN6zdzvLrcfDH5+nvEWV8bMYf9O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5-rc.9", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2b33dda55aa02a687822de4d8a855513043d811a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-aP7fCALhESsoBgbVhiirkjZVkmlEsNRTodm5Jx3vGAVGpwP2nrnoAhcQEzxV6eefi57E074EDzz/6AZsh7p0iw==", "signatures": [{"sig": "MEYCIQCOLTFK5EAGjvUP5cnVYFN6wpmEATknkqKmsEpnIP1yaAIhAJCncN1GD1R9DwLs2lSWtQQc1VjuMFMDpo7jblv0bfMi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303150}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "654151310c3f9a29afd19fb60ddc7977e54b8a3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.4.tgz", "fileCount": 9, "integrity": "sha512-Cc+seCS3PmWmjI51ufGG7zp1cAAIRqHVw7C9LOA2TZ+R4hG6rDvHcTqIsEEFLmZO3zNVH72jOOE7kKNy8W+RtA==", "signatures": [{"sig": "MEYCIQD/y5bB92dbBqfgCbbEBwBjj339F4kaIJdE24quih0pwAIhAJQTP9H80afZZR9aL48Z+QrYqekKSOHaUPRsQ4AXsWNM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303112}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1450c49863a903b4d7854a90c1422df6ecc3c29e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.1.tgz", "fileCount": 9, "integrity": "sha512-u5bup+nl6k1EHmf+OicwoS7qI5uC5TvCrxpg/ZT2ia41VinGU3gsk5e1TY3e4z5YBMtLNMe3jdHbyo95vrEXEg==", "signatures": [{"sig": "MEQCIFcBAoPlo9u/gyhEKQftDp62eGS75hN47GscI6m8IUwFAiAzixKyT7T435FWEVYaltbhY9CbnRRkcHWlPD4i9mVy+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303509}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e40274fd4302c299288f0c9bc0d51541e3798c8e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.2.tgz", "fileCount": 9, "integrity": "sha512-wZ97OzbL/CHebic58Ci33U9t/y09KkcDQoWj2mCd203ZjVge08/5SSescy302iuZpKJyURTjprRtzf8cgPv1KQ==", "signatures": [{"sig": "MEUCIBmGWqvddiY5cQfwv5wIMs6tSFAtrFqAm1PlNjKXQZqnAiEA7v/3y1+j+Up0nu2nNLLPjD/eD1GEgfTLxHxXYnFsWdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303509}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "89af2b7dc3726c68f97e1ffd824d957607142171", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.3.tgz", "fileCount": 9, "integrity": "sha512-jVl1kqc8ExpVc/XxaxPH3tszU8yB7Bd1rZOleWc/NLbjHM+X71jKXPpsgXohx4Uo8+38HgJcB2qWRpdPM9oJbA==", "signatures": [{"sig": "MEYCIQCS1gHaYxLFzvTd4g2jqu5Dv/cVNddk75iIRR8CGPoLsAIhAKMpB3TRdEqY9MrZFrhqCtzT4PRb3I3dH3YNhyyJ1Nwu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303509}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "808cef4f4f63e75383f51dc71d71267ec80f8178", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.4.tgz", "fileCount": 9, "integrity": "sha512-LzugkBU4JTB5GtzybL5ecvAHWw5QhBu1tH3ne3BHL+PEE0qFLbYEjuqbiTRBxwM7M83hHKZZ0P2xL2iRCk2Dwg==", "signatures": [{"sig": "MEUCIQDjpqUpF8hNIhvNn7HObNVE+Qh2iCKAaBwpVJU4dcVWkQIgIE7VI1VJdaPbIki3TDGEtXYgex0W/QEroOwQl654nTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303509}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aaad296dd4315b19daab51fff1f4b46e1966d72c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.5.tgz", "fileCount": 9, "integrity": "sha512-dLfv/bmWdQDtbT0LHmxsJlXk+1MHTiCl3qDlTWY/CxlpQDt+AHOer5IzAsS5C5OrgI3M/lN/s1pHhRt+hCp3xQ==", "signatures": [{"sig": "MEQCIEWFGI3WiHCyywvjlsF6TG4IpLnTwGYJ0F/vVrsb9B3vAiAcNtsVFMfBkf0W3n/GXLP80MxlVvOTqr8K//4/R+pD/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303514}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8246dbe78213fd34382011ab005ec87b791f9afe", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.6.tgz", "fileCount": 9, "integrity": "sha512-RHSS0RBHQwcI0bga5zKRwvfk2i5X/E8iYk2YeQ4K/iIKZrZFJWPcA0r9k9aeZyC7MzEhAzG5l6sRMVIht7reKQ==", "signatures": [{"sig": "MEYCIQCRFmMIUApK7qtN5QLDSEy/tcOBPpZX5NAeoegk1JZj0AIhANnnIWgozrPXPLwdzf+yBqhxdnZ3wjVPGpqnnvYOOjgh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303514}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5250ba696ff282815a7155de0c148bfc0ad3aaf", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.7.tgz", "fileCount": 9, "integrity": "sha512-qjDpR1PNxrCnO0L6PZX+mlEfWk93B1RbujyHIFfHHVxTjQrZLmYwKtQjkcXrZuesOxS7831z8U15Z5GyeAnb3w==", "signatures": [{"sig": "MEUCIAqgy75inOerrdhbxK6oMghmewtyL+WCaAl+zkPsUna6AiEAuHvRxSOmyQzia/s4+e16bBHGZ8NKJ180+6QEwGI9QF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303514}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a90ee019486ec926ac4dcda61b85291772240cc9", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.8.tgz", "fileCount": 9, "integrity": "sha512-L/j1cNPceBgnRyb7ZeFFQ7pFgpnNUJt9XLx5BR5Kycyfv1uKGF2K+DvHPeB+i3B/v4B5q2dl6gNgJH5zCkqo3g==", "signatures": [{"sig": "MEUCIQCO0q0Ai2gkte8d+AiOoxSfbFAfd1N+ab49iNG6bqEgbQIgcgHX28Dr1oek3T0AdvOlqpw/dYW1eeVAhsQJB+HarKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303514}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "40479bb09face0ed818c780e5f4f6f8e035beba0", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.9.tgz", "fileCount": 9, "integrity": "sha512-CQB+sTsCzDnkvMOWqm+wP44aMD8iTsOwZBd8Ujdbf3j5Chg+JSjdxqFyc/grtQuiXf9d2gUuQMmZNpGMGLO6bw==", "signatures": [{"sig": "MEQCIDO5uMQE4r5aQU/+/QavBsIb0kUaP0ho/Cbch0dfWdy2AiAt6wDP8gI5Vmr+6+oTm+YqBF335NyzBneelTal1EQn1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303514}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-visually-hidden": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-dismissable-layer": "1.0.6-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f53b5a2dd3857037adb2141467bace4d09e0157", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.1.5-rc.10.tgz", "fileCount": 9, "integrity": "sha512-e9G49UZCgU6CZihuk1Tn3rmQLGBdqTSChu+IwNgaq03Y5zEYrGLSl43xefO/HA7XQxPwJekEDTRcpl29NbQJ1w==", "signatures": [{"sig": "MEUCIQC3nE0inKbGPXdTKS1EkjRtzgPbr7jpx7FMI+nRk4PN3QIgWQXuazBnEXnrwBpb0vK+v26vUQGb8ZN5zdClBqFs57I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303515}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-presence": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-previous": "1.1.0-rc.1", "@radix-ui/react-visually-hidden": "1.1.0-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0-rc.1", "@radix-ui/react-dismissable-layer": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1dff0519522af8e064c2b4e4a63963bfc4eabe63", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KVphT9Mv8XuDURHLjJniXq0k9XtQWlwC7t8RMhMh/YgzqojstvJRYUNFEv0qrrW9L1hLTZirSBPyww2014Jy+A==", "signatures": [{"sig": "MEUCIQDADxI4u4Dmyl1n821hiDmmcLQpZdDW3qs/jWjrp1b4HgIgJIPPKOQT0caeFCADuS57150gojUdvklAoxgnqxmnp5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225769}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-presence": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-previous": "1.1.0-rc.2", "@radix-ui/react-visually-hidden": "1.1.0-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0-rc.2", "@radix-ui/react-dismissable-layer": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "737e1d5bb4962c4d0188c0d891860a298e0beb61", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-a5YXrI47Rzm9aRZyyD+kdCRwZkLYGn+wX19cvox2Cm7ewsfoJA3vVTHVpAGRvyhCkrJkkCknENvq5Sp4j7/SSg==", "signatures": [{"sig": "MEYCIQDCSy2AHPGoaR5xfhipjEi7tu0ev4voYk3cYxP9zb4ZkwIhALIWTPoqglou6qAC1WNdSh9oN4zpBZ5fF19dMbiKylIX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225801}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-presence": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-previous": "1.1.0-rc.3", "@radix-ui/react-visually-hidden": "1.1.0-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0-rc.3", "@radix-ui/react-dismissable-layer": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4098e4c97e418a29c163919e20eee4a91d846b63", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-1rBRqc43NS8CooN/MPfOmXDi2b9xe9s/M+U67c756r1QQa6HEQkPDT49JlbfL/oLsUdHbxsAUJiSVPlJje8fyw==", "signatures": [{"sig": "MEUCIHHvlvfcTk5b63ZqH0QdskKcTwvvzbczHdTWKLHqCoedAiEAxrBQfYEMxq1wxzryfRtOxe2DgsWzAvFqjSnD7tWwexw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225563}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-presence": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-previous": "1.1.0-rc.4", "@radix-ui/react-visually-hidden": "1.1.0-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0-rc.4", "@radix-ui/react-dismissable-layer": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9f9645ba8c68fee41e6404310539f28e00acf629", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-rXFiiCZRPeWZt9r6UM1LuUCsuB82DHm2nEGPX7pIJb9DGEUa3YK5zXUp8MGm4VmiNG3Ver/Ws9pnd3PensNMFw==", "signatures": [{"sig": "MEQCIGLR2ORHCUMAfNtdn5y2EtHALX2kcfKh5I6HlUvBNbOjAiAImEeqJ7BHiZKCbVUFJPjmIWETVdnlMpMQ7taXyQnmUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225137}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-presence": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.5", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-previous": "1.1.0-rc.5", "@radix-ui/react-visually-hidden": "1.1.0-rc.5", "@radix-ui/react-use-callback-ref": "1.1.0-rc.5", "@radix-ui/react-dismissable-layer": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d8cdc60bc31f58ef1b00c3be2a1d4625b2a63d63", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-e+IHyyRrtorp8LZwHEKN0A6pTVMtMop6bvQDp/iVz6d3Kf8kB8uoqMdPBgz1tjpq3MeUUdeQDxePY3vLPipnfw==", "signatures": [{"sig": "MEUCIQCqYX1jHsda0OLxxg6bek7V5U8kiUzSf8WzFIg3/p7R9wIgYOpiktLwW23NdsnnWGE+1QoTbqGT8PxVy0cjcTb1u7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225137}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-presence": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.6", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-previous": "1.1.0-rc.6", "@radix-ui/react-visually-hidden": "1.1.0-rc.6", "@radix-ui/react-use-callback-ref": "1.1.0-rc.6", "@radix-ui/react-dismissable-layer": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "91134f3a90dbe544da996b84b12a9d2de71e805b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Cd6CgK8EigCtMQ6J0MQSZOg1rgUPD2MSW5tn6tuSqyCA6mTO5er7PTeoovxhl9myfUMgFUMnfBuU0pSdy6cXAg==", "signatures": [{"sig": "MEUCIQCdbRW25i0rvioXf17Z8v7SpTjLCXZD7zR6Y1CNTZmxywIgC3Venf/6+wr77PRMe+TpTW/8JEL2Q1PWs7JHzpp6DZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225137}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-presence": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-collection": "1.1.0-rc.7", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-previous": "1.1.0-rc.7", "@radix-ui/react-visually-hidden": "1.1.0-rc.7", "@radix-ui/react-use-callback-ref": "1.1.0-rc.7", "@radix-ui/react-dismissable-layer": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "87c900d90901a82bc1e5338ab3da04ec87fb4a17", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-uT3vSLV94plU69vSbC665swQjHhqti2q/HtXyngqBBYCS1xJ2FMlvzJeif8H3VPwhSnH2z6bOE8RyxB5DuzIaw==", "signatures": [{"sig": "MEUCIGgT2onLqTFvxGE2f3qg+/OSbkVnd15XSh73EhISBXmzAiEAm9zHBeMmeJDPGtEGZLBauNESgrNRkQlbRPLqHFoHzoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225165}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.0", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "884c9b9fd141cc5db257bd3f6bf3b84e349c6617", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-OQ8tcwAOR0DhPlSY3e4VMXeHiol7la4PPdJWhhwJiJA+NLX0SaCaonOkRnI3gCDHoZ7Fo7bb/G6q25fRM2Y+3Q==", "signatures": [{"sig": "MEUCIQCpUGF7eG+clf1yCKjrlmXFOqb+kVPY0u1Wu4XzYS85JwIgfQq4AXUcKopufayFRE+81SFKozRDZt08pahLU+FrjJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225062}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "31961569ccf689d5dd24f3578eaf0f9b747086ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Pc1WAT7aOOUd9ob3g9MXvXby+Ix6M6SMpVl7wj/DSj5O0lMzSlKv0+t9sOjUhoLQ7JSZo9Y5TBdiMW2BTPhJog==", "signatures": [{"sig": "MEQCICcvpniKC3DiVcEKkg9BfpD15b5xGfY3NNml+Hg975pIAiAx5OfgiPIFZznJc5Wu724A4GYzj7Q3uCB+DJUiseNMJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0581d548a1a8400e5ad054958738da6d101b836f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jH6DSXO3UmRH8uZpvnd3Quk9NgxrU33ajIg6rzLHl5aDGmdXcwAwQfAhvQ6GoU9SJ62KGOuxQO+uZAbf2uJDHg==", "signatures": [{"sig": "MEUCIDDObbq0NTKPVz8kZTuvwq/fdFkV42UgiN8grGc2zWYjAiEAq2evArKIb8e/SmetlM6GHJBw5WpzH1WPznljJH1WKKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "304666b7a9f582670177ea4024559670df2f458f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UCv985A70vUZu2XksizQKGpg2TQLdU60z93V27y6SxrQiRl7QvPhKx0/eyc2VLDBkBQwBFAczDsk1WZ4K3rNyQ==", "signatures": [{"sig": "MEUCIQDaN1qB8aF5dJbdfJTAtN0qRT0FNm+REKIxguO1Eu0kogIgMzeb55pj4lHclqF4qBKnRv6bD+MLOgRHGg683CLkLc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fb9fbc99ee13f71502af9d586e8e89457ef73f59", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VUjImY6kmQMRQEIfZQxSsyj23N/RZSY+q8StwdGdNAFtXsVX4IvkZLLzgwdepWgq+G9SV0boOM/G89nJkrSegw==", "signatures": [{"sig": "MEYCIQDXTW7PvT7d3wJlaUtB6EmL/f+9jqQz7/wgpsFDMp1JpwIhAPzlUJcU3Nledlqs6lwwB11R8E5MnRFFrZCytJ6BRCLr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7127ef92fec5e06a60b70c5a7912a6a42f3df13d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-BRU/gFCy9OzBs3vApSsnQMJSOsqEM4AJf+SLylMYo4terqDIrTEilrBDtH4fd8sEwMMrsr1OSY2llY/VpgpkNg==", "signatures": [{"sig": "MEUCIDQNoBuVWTNh8+GtfsNXLZErJ96ums9iWrBFQ1m/i547AiEAwdq+YZCTCNX7IeRrIxkTdPl8EERqy5jM0cbttRSggGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4acf795dbbf9562a4fbbb6d142e808d9fde61fae", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xCvK0az50HoqQHqGxw4j+dGW7KG9BaIdTLnd3sqSsIYFg6val9+aPkcnAJbOw/l1M19H6P9Cv+N3ywqp9tzCXg==", "signatures": [{"sig": "MEUCIQDpoMlUkfr2vNqxIKSQSi2mEzzSpuCO4qkHz/+ZVv81ewIgW9eZGhWHnMtpbg4hzJqpruKhReoVj3U7Udxh43SPpW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "58a92d144e280a5f650512448c9da916ea8bdd96", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-IUWIxWkQ9cEh39MYS/RUE17BCFippyeKJVkrsfedSlPx/VTlYhE6jODqjmy3bEBfjOrVipo0Q50z6Bhs7TR17w==", "signatures": [{"sig": "MEQCIFiHwde7bSyv4kmjc+wtymRmz4acziWEtKu+pC26e2igAiBpvBY90ZGvTJAwVQuY7LtXNQgJpS/Wqsp939mstNRvWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.8", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "955df2051a4868af56b1962168b4f3d31cb80871", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-6ua/R6Uy4cHEZD2V2EaKczbYwxyjyAFwKXtzJFc5tZuS7ipS9SUXEp7HFgFQKWUvlMZP2h43d8yYS98ykkgAjg==", "signatures": [{"sig": "MEQCIAs2gdYkjFuiHtejfHXNHK8zRjz4Kb9Rl+u99uaFfioDAiAUXiD0I45MGmvgC1aPurUszRGf7BEKllXT0Rv4i5lwpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225048}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-presence": "1.1.1-rc.9", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a9fba2249f6d065d033155049e839091974add3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-1sj8d2ZHvSnmE6DkivMr/4Btp8ju5gGLD6F1nup6CqmDC5vByF6Z76Uxr7TrO+yT1kcVmHfs1itp80zh5g7G/Q==", "signatures": [{"sig": "MEUCIQCnlWHEbHA74vQ6SB1HTVTvF+ziOXXqS5RHZpKqnutilgIgeJDyNJZMu+suUqNgM26eUGZbJovMt1LtnfzyoP7TCUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225053}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-presence": "1.1.1-rc.10", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f6d17bd677e3d3db92269eebdcaea2662625d805", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-wOP9QnG6IueHOBdOX0rolH2dgohqLhBMW/v7Zn4cM8Af3SuVfAbGbtGKgs80liMq/4tRY72qVYVxFNlztVI1cA==", "signatures": [{"sig": "MEUCIQC1sufKQHEBypdW2865NGsz68BFqdMQZc+oJH3sPEH5dgIgJC8aXM26bf6cVnfiUaw3c8bU7Ur1uKRTB5KUMdbuPFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225055}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.11": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.11", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-presence": "1.1.1-rc.11", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b1ab92babd9c3d9c509fc9f85079b4a010928150", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-NLSaCHl3IUKjxzu8z6iAeBZSwQR3Ii8Zhq6QFDjILfSaCe2D+Btgx8YNmBGW8obsv8bYT5SQKfcoMl0N+TB86Q==", "signatures": [{"sig": "MEUCICjtE4N91HppV3HrexgrZ4mNakT1buuqsnDzhGOEaGS3AiEApGAXXz/MqV8Ar/gU3VdQdoykEgHb1Ew8tkdMnO9ystU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225060}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.12": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.12", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-presence": "1.1.1-rc.12", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7bf286e5d04576d8adad272fe5ffe608c44d78e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-qQHYf8fKEVpjbRxnVEEojNL65M3r5vrHBxyAZ05d4uRRYr9p5LFOyt5OW32j3ram8v3HpBhyVotKPPDuYeEzwQ==", "signatures": [{"sig": "MEQCIGaRnrxky840qugZeU5Z+ie0oqUNR77Geg0oQhwi6kOEAiBSxtKF0Kfyx0pHBHXe9o0gn8tEvnODoCrAIxSDTPWMlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225060}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.13": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.13", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-presence": "1.1.1-rc.13", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6205079d984d683a1b2a4d5731d53d1ef96bd2f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-6/nNlAL1CDb9dBgwGXdDPx4RTFQDZxOzQ9MwHwlKkDXcE8gJtDzboL3dRopv5uwzaCxcK7o1cPnR2u/VWYy7og==", "signatures": [{"sig": "MEUCIH7tGu6DoP6m9a1Gnez5x6IxMBez7yF/0DUUpJL3jqNfAiEA6mx/9dJfAcUqmG517WYhKKNr7LsEcV0Bjr3DtXNbnxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225060}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.14": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1-rc.14", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-presence": "1.1.1-rc.14", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "315fd0d7b15a48e38fafb329d46cb4f7af8722b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-tgZUScXAWO3bvzMQy0CExTMSvaw2Gv9peltNLi9KBEcE/+zP49pM4G3Hw5gCKFtjHno+cKWxi2rF5HAIU1VqPQ==", "signatures": [{"sig": "MEUCIQC9RjZ1FO4fAup+gnRJupDVhqL9hUJz/c0V7Y5KliqNvgIgBVaRHzrLUTHcSQLyF/kzSJZSeTkRfC4skDdcsM4KTBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225060}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "31989e026adecfbb2f7bd1108ee6fffb830b2ec1", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-egDo0yJD2IK8L17gC82vptkvW1jLeni1VuqCyzY727dSJdk5cDjINomouLoNk8RVF7g2aNIfENKWL4UzeU9c8Q==", "signatures": [{"sig": "MEUCIQDwPzz+DMHjqEUdz9HUXHjHy2KAdYCjz8vnCmniKhno0wIgZq53eV0Ha4D+2Q3qYzc+9PqKJejJfECO3YZLexXyLZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225010}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.2-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-collection": "1.1.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.2-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8a3b7ce5562f85ef9b4b264d149b2c6c37b8473", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-TV8GXs8TZnPOWZ91UW/NaLfRpOni5+ketQDPOIgIxrVT3bTRPGduvlDh0+pzHEkQiQfEMO6HjGa6f8UKNGh1aw==", "signatures": [{"sig": "MEUCIFIgZhp+5sLD/LJfMC2WI7f/Y/iBvvRfMHGskGOvZKdMAiEAvQeOlRtAevIlrrfbpd/NRakiY4rCKigfVdtPxIdnRwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224880}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.2-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-collection": "1.1.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.2-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f4c3868eaf12106fed5808f8fb814f852f291457", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ApQftB68s5LPnEeK5gS6O77tSKRXxAwprZ8O45Q0nNeDwZjXV2AHeaxcCg47eb3atJbVwQ9vXoLU1THCPxRiRg==", "signatures": [{"sig": "MEUCIQC46Kh8j6dirFDBDkzKQZqLamqNbtLIQFL2f5W/MavuZwIgdzZhVNSJfsaHmlHliQSjvdkAkD+njSx3ttI5xV9jWl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224880}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.2-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-collection": "1.1.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.2-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9bf5afcf036271a9c1b54dfec252bc2fc9915bea", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-lw7O/6FaLm+bwKed9gp0+vx7TgRZTG2f3a8tUOao51Dcf/J2VDb8qNgTAIujdilOkPS8GEcJaXPPspkn1Q2ieQ==", "signatures": [{"sig": "MEUCIDRYrK2KluHaJaOg3ucez1UrTU9jKfmsP7kr5DSolP/VAiEAoW+PX5ZiLkLK1Q0eoCX/2/ehY8IsUG/VCSHEcgd5As4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224880}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3731d42dd2425178814c21ada8c59eb51c5e1a60", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-7wHxgyNzOjsexOHFTXGJK/RDhKgrqj0siWJpm5i+sb7h+A6auY7efph6eMg0kOU4sVCLcbhHK7ZVueAXxOzvZA==", "signatures": [{"sig": "MEQCIA3msnF/mR6hpyLH6SCfNOBuG0SSVk0PHAGod09Gh3DSAiBCPLFYEoW+YrmP0y1ooWbC1kCyAGFNfMCFrxHaDSRVwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224812}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b76b243235acd229b4e00fa61619547d3edf7c99", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-IQWAsQ7dsLIYDrn0WqPU+cdM7MONTv9nqrLVYoie3BPiabSfUVDe6Fr+oEt0Cofsr9ONDcDe9xhmJbL1Uq1yKg==", "signatures": [{"sig": "MEUCIQD2LH1J0/wyvZCRsYuAuOWR/NvJRDnJyxwMf9Tkq2jwZwIgY8HpYE1/9mZ8EJuXmYMBnd9ZO8vOtQ2ZZdhiZFWc0t4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224812}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-visually-hidden": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-dismissable-layer": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "65a9af0d8e15779fb13d0d1ee049036f272d8b97", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-JAzwiS94MFufEWLVKlc1nT9qXfcMN70mKvbr7WCuNV+/IRcmzflUDIDO5P9oP4+Ax5J/4j288/jM4TYaVJlkmA==", "signatures": [{"sig": "MEYCIQCbqmmzTPHzym4OL8AZZX+HsBcJakJTBDUhaanZ142CCAIhAJ3Ds1GL4DPgQe0TnVvE25U6uKv0ScGo/tuZvNhwxMn3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224881}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-visually-hidden": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-dismissable-layer": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7e0d91f11fad6c019b372d3c6b2ae1dda3f79fa8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-bRrTuf5ReNRKDQPFyc0up1FZzIvu3NgSJL07b59P8zbw4qzWyzDA86gGeOEr53fMSat7+ITzCcF9ut1esE1cxw==", "signatures": [{"sig": "MEUCIQCK9u5+E06TE/4jDcI5a3pPa7SyCi+9FE+mjr6mhk+xiAIgCEKP6aG6nBj3HbNWecbD7jV24hAlSE2c/mmSlW/hv2M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224881}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "0.0.0-20250116193558", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "76c737c82e42e43d8377be6bcf447dfa410ea6fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-<PERSON><PERSON><PERSON>j<PERSON>CbCEUsv5qwkvZ80I14vIX9JVn+zDBYDmpCa/a8UWHAKw3sdKfROAkxV1iEa9PPEi8fg6knmq0SORcstg==", "signatures": [{"sig": "MEQCIFzxppFdS7nKDqvj3+7UTRXMpRrXnBqLrh/lD437vU19AiAaGj689p7lJW4oCHzSY1yc0he44y8JW4rMJcPX6j7w0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225042}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-navigation-menu", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "0.0.0-20250116194335", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "48ef52ba608836a9781e6107dee51645221d7ce4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-vWxrbBWB/K7k2kAm/YcgxCQQxlKEkSVxgrguhM041KB1bKga0dMpy+/6tvZXe2GqGcv9P1uah1rtsbDSN+7QhQ==", "signatures": [{"sig": "MEUCIHN7UrbdXgHoQYZLaIY8Y+LVmcm6UJKQ/nUHMZwUEbMQAiEA2RVg8HgQivAYLmB6kUTOdoiFTHKZkkCL0rMsztCtNnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225042}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b992268a8eef006af2d38b6effbe3b6141783cc5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-jhIAUP6veo8PbOquHZoKJEzP3YtGnWMcAtlN76wN+xyELnmWUCBFb6GGZWW0AehMB9Fei5L/7jdi4nz+QhsxAw==", "signatures": [{"sig": "MEQCIDCmWTU1O0nAkxliI4cwKwOIT0qpq2OW99xSrWZ3jPQKAiBeIqWQy72Tyu6mmfGE2Ve/mXJd+X9L3YsH6hKUVdXRWA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "297fee0e786a8b648b362e3209704d9dfef910a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-XtqqL9Uuu7yc7Q4d2Vo8Xtrd+gmjRgvF0IHmjkuaKTw43WMDMpIgxfP9+ZdtRd678NmdVFxnOf8G4bbfsYTuBw==", "signatures": [{"sig": "MEQCIDwMcL++GJY+MmuqLbBrrIU9oQUphti/qVWCh6DJZWksAiB7vI9OqWyUBRHJgR3eI0VvDJY4y6icAZD2JvKAeyQ6jg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8ba4432b6ea9432c31499fd1cef6a9bd7c1c1c46", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-b/p8kS+/uB2e37grEEhAFGUF6Yvf8kirezrkqXToiIDgB8n/aufFkkxntdi0H1uByUWhEfQk9A6AhSQU+/vk9g==", "signatures": [{"sig": "MEUCIAdnNO3jy0C93Oa21k+GsQYTQZ/Zq7p7iGqvYt9cs9u6AiEAzOsUWSWbMyRD8la8xCogkyoPsR3X4Yl2wNGa+7H5+mU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f99bf2877f85dad439c4a71bd7b8ef19a4f4cefe", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-htFNcutUVXEZFy44+c8jY5UQuHwFeusi1yS9guT5RRa5OESAssKfVQdNtAcpJpSOvcMm7lu3uy8Wj0Dv2hdJGQ==", "signatures": [{"sig": "MEUCIQCs1YePRP/Hb+N30HIOd8DF3p69TMbt78siUHCczaWZRgIgMs5tPM8w5lyD30HZOOEnNmNX7rofM1/Iz9ssZSPuUJs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6626dd86e9dc4a6e0f7357b6774e322d6b79ae77", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-AXuoBiRTLBIZcLAO4dWN4xFXWWO126nraCvmr8lKveSmzNRmXKSXLylLGKfchZ/bMHMr8zIU5/SGiZzGHV/Q5g==", "signatures": [{"sig": "MEUCIQCVgutmR7Ct4R27++1VFiKylXLTbmaDGW6PUaa0IYNy8QIgI0pt73H7pEeEyxavPqFKEJpYrHMT5fuS1f99AhRP5FY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "80c1e649c8ddad7bc6717c4017e459e359652275", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-3b0ft0gI9iuCdDpMww5rggo4hKg+b1r+/gfjs9qfdvG/e2mgwRQtVfxFg5VombTc1UsKmtn0J1nWzuH1mGW6HQ==", "signatures": [{"sig": "MEQCIATDT7INJRxzRNvq0v2FiOR8FWLVHs9gXDBJ+PsKUPU1AiAlyXGjG/J0rwukUIG9FBweJ4K8kjnDZ+GcjD8T2RzsNQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d9acfc52c3d58ec0d04a6cfef6a7e5bc157af32e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-JwSnzD7h+q0+uBPjmLFRK+11l4IUopUhBoKADYNhLgGeONeLcXmJ3SBADZqF/3Qj9ihWrEDTB5m1vQRRA6PjRg==", "signatures": [{"sig": "MEUCIQDuAEetUL2flVgmLi1AQN9BTrJCT64zkTVdflBy9jNWbAIgNY3HJ3q6Of0I5AezCR6itPw4GnB1CTGwPePqsINOY3I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.8", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cf8a72f9d590d50dd74290cf63315f60bd63f65d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Mral8YUg3yuK7GjI4NeU0S5aWo7ZBWiMdkvu4oAlukzasT7BBDSTZrhiw9lIJYuSYB44JCNXLpYxd/xvs5skgw==", "signatures": [{"sig": "MEUCIDGvYQX6NdENQcu4vlJhYSztfmSyXkOfFkJoRBItt/RCAiEAh55XjnNhW91P5FxqJJxWmux/xLGdujZ6CH8lHceERoc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4-rc.9", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "764f8abbcb1dea747b6b655a4f0ceb9bd80f9b50", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-AMefBnbFRD0gEMpJjmUDKSKY0GQUnTTEe1MXaf5z7Xl1SLVwUhPyXIrcmU/LCiPRpZdSpHAU3pBVMNxhSS5nsA==", "signatures": [{"sig": "MEQCIBEgcqDPe/oyCytfmB4PSU5f42KqZkWI8gRplwCZCFcGAiAgDiQdTePOg0Z3sIVfzvmWh6uEOTgvlo2JFLSiullkaQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.4", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aba3ac0d343b1842924fdaf403d8922240fcfef6", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-wUi01RrTDTOoGtjEPHsxlzPtVzVc3R/AZ5wfh0dyqMAqolhHAHvG5iQjBCTi2AjQqa77FWWbA3kE3RkD+bDMgQ==", "signatures": [{"sig": "MEYCIQDZtzEqCYVi5J7rWBJXYLdZ1g08jBw+JscIruay4Njq4gIhAO+CBS7SPjH7mEPUZigVubReJAC0jaIrnhJqpiPwDJ68", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225029}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8e1427c3b6e114264fbf63424ecd54340ee79dcc", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-bVDOOVpMMaBj/nYUA2xOR4tZxNrOTmXn+lFx5mRM3zEt/ngWTI7/udsQ0K3Mrut+OyJZJxKFSWt+Tk8pMiYw4g==", "signatures": [{"sig": "MEUCIQClRrsChMgkVZlGzB3pYyAjHGuTi7Q69fZoToSUlX4y4AIgXtqq51PlFtrQl9jVlS/RXPPTbLIV7O4YIoZMD1ytlb4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225067}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-collection": "1.1.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fb565c7bc68ec639e0070b340b581ae3d56cbab5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AtpR4l5ff7TWPyQ+JkzjH9C7wyKNEaICWf531Oj0rL48zYGQLi85a6kplcRCY+uXZBhmvsvzD6KPXG7auHxqUg==", "signatures": [{"sig": "MEQCIBEn9FlitvLEtIMGyinZfyRfNB8c2+vp/QzoWbH7qyXsAiBf3eUfdEVOlVpgBcYlg/kEevVHUJZ0NRkdZkDTskHuSg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225082}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-collection": "1.1.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e7041ec53f5ce5e938b2580bd50543822adaa57b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-i8a0Du2r5dYB62WCg+LI9W2S4sRUXQqE3tSebXA7QzuSfN30xDvzLDZpiPe5kW70BOLTmDvBPA4S8EqNlMc/+g==", "signatures": [{"sig": "MEQCIGrU6wkZxOkZ2PY8oeZ/HYtHztyF4SPrNkheB+XHGT6kAiACD9QH6rycpB/yZUFmRkg5DHy+X3KOpVVOngSVExIkDA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225082}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-collection": "1.1.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "79fcd53e5d62fcdf835b3ebc132a713ef8f5ac40", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-PrJMMaMVzxrOADPmova/tqGDRViN6ieruwtfen7yTf238B+VrYRK2VZQb4Maqsyv70NlV6rxRkgqgT9OM+myFg==", "signatures": [{"sig": "MEQCIHBEn8+BMxhxwHEYK9zkXxUWhOywbTWypQG9W3JjiMd0AiBJniesS+5kyaULAd1OCu4nbMU22EvEo6PNJTk2Jez2iw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225186}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-collection": "1.1.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3c172f8c1f3bf1eec58b1b8fd0b80f078f4dce7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-lXrf2Ts9cuRIOgGMFRheXx8ipk/klgkzsrRDeBYnTnC0GYdy1dWc2H50HBa6eO8L9i2+lWNC8qacoOYR7TrKLQ==", "signatures": [{"sig": "MEQCICN1AzYjy6nZZ7wnSzx5/QU64w5DQt36RcCwQK8ljOLCAiAYBktAACVYr7RCNuYQ2HFg4Arajfv/BkPnPFspDStifw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c882e2067f1101a9a5f18e05b73b68b2e158a272", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.5.tgz", "fileCount": 8, "integrity": "sha512-myMHHQUZ3ZLTi8W381/Vu43Ia0NqakkQZ2vzynMmTUtQQ9kNkjzhOwkZC9TAM5R07OZUVIQyHC06f/9JZJpvvA==", "signatures": [{"sig": "MEUCIQC52ITIlEY6LDPMjtXijsUGWeDhLda1PQ4JgtnTYJGrhwIgHPpDe/wq8uIHU0H7dEFrxgiHlymaNxHPr3juenRiybU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.1": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.3-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-visually-hidden": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-dismissable-layer": "1.1.5", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb39fb2bcd238cb88cedc39a8505985a36600d2d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ymCqRn7JntgpW573elGPuX/tJVVU/5DJUPn0yDYlTXVlJsuW1dAAh3ReVLKXF7rO1g+4hO/Rvh3gLmN0IoRDDg==", "signatures": [{"sig": "MEYCIQCM7LlZ6qdjhfq0uXhkgGdfeZFcvq6Wm2uYVSCgflxWyQIhANciXx+RRo98NcDnThbU7IlSShRPXLBYJVu4vKmtTrTW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225177}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.2": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-presence": "1.1.3-rc.2", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-collection": "1.1.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-previous": "1.1.1-rc.1", "@radix-ui/react-visually-hidden": "1.1.3-rc.1", "@radix-ui/react-use-callback-ref": "1.1.1-rc.1", "@radix-ui/react-dismissable-layer": "1.1.6-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "76ed7d123fc542938361705efc0e1a48f0b3da6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Si5tImLWAVGfU2H37Pi9HQt0HwQqRrxWYdl6PKh8VthSPJkRvKFmY0gdUEvkLMjznFpbYfN2A3G7mIfHDhQClQ==", "signatures": [{"sig": "MEUCIQCwZksOQEhRlqED/8abUiynchraN2Fl/l8J2FRYNSBdEAIgDnOilTdhSga7s1bEeWq3gsRts147ZigMTTLe191pCD8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.3": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-presence": "1.1.3-rc.3", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-collection": "1.1.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-previous": "1.1.1-rc.2", "@radix-ui/react-visually-hidden": "1.1.3-rc.2", "@radix-ui/react-use-callback-ref": "1.1.1-rc.2", "@radix-ui/react-dismissable-layer": "1.1.6-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "633dc390e15d2562ad3445029677c55c43b2ff1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-6jVSfgdHetPEdnGtJLpNkc+2oAS8qFRypLq768QgI+cVWwGDkETsc9FwQtMU+xKC0j9y5/u0CCNtfDse9BVA+A==", "signatures": [{"sig": "MEYCIQDAzFcWaoZ/kc2a0KoaRsWOepvNLiotLmXRdr5vJ6Z4QQIhALYDOrCg70f6EBoZcKhUJxfMvziuBbE7a1oB+wLFnQWv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.4": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-presence": "1.1.3-rc.4", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-collection": "1.1.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-previous": "1.1.1-rc.3", "@radix-ui/react-visually-hidden": "1.1.3-rc.3", "@radix-ui/react-use-callback-ref": "1.1.1-rc.3", "@radix-ui/react-dismissable-layer": "1.1.6-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f0b68361442e00c6d1532bf262514c8d0fdf58e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-wlwoiMyIrkTXv5rM+OPzYHQBujA2sUDr/p19M4sHZo3kIiphqgqsfYA3/i4H/B9GFeOkgMV8ouUxEIBqY6hXsA==", "signatures": [{"sig": "MEUCIFM3eEtKWcGZueoCawwJGAzEgNjb3abd3pQrCyNOc+PYAiEAsjcxe3lQE+JzdcUvcOU3xk0722rBCupPvOh65ZpR1f8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.5": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-presence": "1.1.3-rc.5", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-collection": "1.1.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-previous": "1.1.1-rc.4", "@radix-ui/react-visually-hidden": "1.1.3-rc.4", "@radix-ui/react-use-callback-ref": "1.1.1-rc.4", "@radix-ui/react-dismissable-layer": "1.1.6-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f7b0941907b47a17aa8b53dc626d5a5929f974d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-sw1w7v3RTVwzqgrgB90U7nuMoysHFBPIlKmMUd1sYuYwdqws22hTStTy7iZaPMZ4qiiXLLas/jKsY2c75agJYA==", "signatures": [{"sig": "MEUCIEYitaxrJnW66fcVjI7fHZkhdOLwSqjvoBRJYgJJZI9DAiEAyou01PoxJi7HgjompYAguqkl99qi+HgajIoYfnOFbTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-presence": "1.1.3-rc.6", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-collection": "1.1.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-previous": "1.1.1-rc.5", "@radix-ui/react-visually-hidden": "1.1.3-rc.5", "@radix-ui/react-use-callback-ref": "1.1.1-rc.5", "@radix-ui/react-dismissable-layer": "1.1.6-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "823092cd89a28e0fee40c65280d7383055478533", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-9uUCStfa62rMzgzjFte3FqxkSZVzbPn2EmMo+dHtsw/R+zXYHQpqmbniWaLjnK+5i+zt5DYvRSRnqp2h4mZa6w==", "signatures": [{"sig": "MEUCIFkmLpmL9zH8nFG/ks7k9znyPyZdoL/i5ePji+9VUoCiAiEAjhxmDVCry/MzTUHhiCS/CH2o/wBdhon6Hxu1n7r8GIw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-presence": "1.1.3-rc.7", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-collection": "1.1.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-previous": "1.1.1-rc.6", "@radix-ui/react-visually-hidden": "1.1.3-rc.6", "@radix-ui/react-use-callback-ref": "1.1.1-rc.6", "@radix-ui/react-dismissable-layer": "1.1.6-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e2c64d63a69578e74c14b6a59811665c1b37ada0", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-LRjfI2WiKy1BJlGPkJ9bxInaZUDQWqXl8ocqBkh7bcK82lkdufBz6KSIjPdJbRz22algs2zGWINu6m3eki5cAg==", "signatures": [{"sig": "MEUCIGfCsz/C5mKDWWClBzfcNatlGhapI+Zmo6ueKfcNLil4AiEAw7qDIwjWlMEB2BDG6LzlOnNgvOeh3pa6qEX7wj6HenA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-presence": "1.1.3-rc.8", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-collection": "1.1.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-previous": "1.1.1-rc.7", "@radix-ui/react-visually-hidden": "1.1.3-rc.7", "@radix-ui/react-use-callback-ref": "1.1.1-rc.7", "@radix-ui/react-dismissable-layer": "1.1.6-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "90874c34c6053a7488b6e8a9f29b83c504fd3fbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-8jvN57PaKcqAKX+/OPN1at+UyxL/4k54w3Pvm/DXOxFgSv36ry3RdmBxcdG67Chp/eSvNd9xcLloKCAh7+COow==", "signatures": [{"sig": "MEUCIAb02d3xSwtFcdjgP16gpJnfCBcT+Fx4j6gp3rrwnzpBAiEAzkAx7fq3bOP3IZ7LFW8rz5rEw7f+FIn2WV3TF+y2Vvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225248}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-presence": "1.1.3-rc.9", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-collection": "1.1.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-previous": "1.1.1-rc.8", "@radix-ui/react-visually-hidden": "1.1.3-rc.8", "@radix-ui/react-use-callback-ref": "1.1.1-rc.8", "@radix-ui/react-dismissable-layer": "1.1.6-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b39e1e5a6b6609582eea6897a77d2641a7a751f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-aESHu6v/9dzaykZPDzPKM+6kbnMmrzN2ggnk44SNC5+56HXVMeEsCeGxXu8fgLXKRDvSHbGf4wo1Fa1NbcQbBQ==", "signatures": [{"sig": "MEQCIG9QR+9C0zzQjYfOj5j6b4Oibw6NP3cFhP8Spy9q48/wAiBuInSMnpEb91JDWZ8p/096wrUS5gGjKQzXTSRuUpfyBA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225639}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-presence": "1.1.3-rc.10", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-collection": "1.1.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-previous": "1.1.1-rc.9", "@radix-ui/react-visually-hidden": "1.1.3-rc.9", "@radix-ui/react-use-callback-ref": "1.1.1-rc.9", "@radix-ui/react-dismissable-layer": "1.1.6-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f31d7a0ea6a080a2c8554486b98cf26d1124985a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-exVaTzC7pbYnL031764ehJCosiIMcGPq7yIC8Qh84vo2ckdacVI9lD1+EgT53CZ9ikWTeQrDKDGEqovEYyAElw==", "signatures": [{"sig": "MEYCIQDjaTnLdTJtK+WyNoucEK2v3InW0JIqDpHQPbNRGlBnQwIhAJsIrOf8hCLy5VHPZCC5LNqNGYcEinCs0hJCREA6T/Iy", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225641}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.6", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.6", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4a333d4a7800fa1111343609460c713eb68d6bb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.6.tgz", "fileCount": 8, "integrity": "sha512-HJqyzqG74Lj7KV58rk73i/B1nnopVyCfUmKgeGWWrZZiCuMNcY0KKugTrmqMbIeMliUnkBUDKCy9J6Mzl6xeWw==", "signatures": [{"sig": "MEUCIEgg1UrNUkK9x+PXUZng98DYLn3tpR5EShavSB5jh2q0AiEA73M1lSWGo1SsFDkEmjdLIuYKoLI4Q8pZg/vbs7eEFJA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 225536}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744259191780": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744259191780", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.6", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7c41c730c109fa3fcac84b3cb17fc8733ab7c75b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-NlJM05KLiwc9/bFrI+I94ZQJh6lWD5qfRxA4C7qymoK1ALjcyjLzHg7T4TVNz48ec29k+H/3oISwsuqkx44Plw==", "signatures": [{"sig": "MEUCIQD3FkuJDeCBhdJ3QPs7jzsXAmCVHi1spZByU6iTAM9fEgIgRQrWRuOvrnWf6M10gB5KKu+Ha1+LxXCB5E7gBerIgj8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744259481941": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744259481941", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.1.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.6", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d3bf4cc41d6ee1cfb2d73e3d5569036108bf6af3", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-3e7IOfGUJlzejcSveJEAER2hPCldjp7SVKkErmD4JVx2urBcmq/1oEgqhUsgxGBZ/V4MF2ytGUf8KFiuwdfa6Q==", "signatures": [{"sig": "MEQCIFfRgi33Oe1wlKntP4RYGG0aWpWKVidEeDxgblE2ZQcmAiBuuvyzbNDwZ6LYAIQ9AIkEzwn9rriR7PKWbxLvW/Pk5Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744311029001": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744311029001", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-collection": "1.1.4-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744311029001", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744311029001", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac09f47e419c0aac10794ccb0f7063b4c6359616", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-ruJV7vcml6gGVe1o8REpB/YKMyU3xtnIeiYQj10BeVZvOwU5COpi+rV4E/5f5TC2AA24s9G2LXiZG/TfUor+Cw==", "signatures": [{"sig": "MEQCIGpcKm59OE6p/1ATDeQ8qPHcTu9ak3V1xWUtrlxu11tlAiAjr0a/MHhk2iI3n7mF9Hfxe1you+CdlWsWRGZ7C/yOVA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744416976900": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744416976900", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-collection": "1.1.4-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744416976900", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744416976900", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "46abac4de533ad3be8e91651a85a9a5fb81c216c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-cn+5mxAKv/8h2fbwS3PG1X2t8g26Plupt2bX2WO+01QF38Ot/AcSiGL59UruASff2Vr6SYHnNr7XVyk394k6pQ==", "signatures": [{"sig": "MEUCIQDM1tK0XMs/Vr5C9HTyXgLRLXou4G36hE+YG90aNPhQ7AIgKrLiDN8ZmFAHcsQ0KXR6Ggd8pGabfZCJ8jogsqUNogk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744502104733": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744502104733", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-collection": "1.1.4-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744502104733", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744502104733", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "863b34e006a713e36cf75e401d6eb2d6e1f531fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-rT8TZuWlKr51KRpg7v97dKWaBNR5Ejyy+tqpb88DcOnrMVCz+shmBEqR2jyuJbSNzgqQE7Y7CLm2SCxJ9AoixA==", "signatures": [{"sig": "MEUCIQCn/xeG4xgVOWfY0tydOZme+z8BiThTj0FAiaiyaeDQbgIgTDnvDFNuxVC/iUyCN0lXcCWv5F+y2seAR6Kd0464Iqg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744518250005": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744518250005", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-collection": "1.1.4-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744518250005", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744518250005", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "afc839e9cf618f58b40ca9698fcf63e8e320fe0f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-V/PUH5dZJTkAu0jFdaYpC9XvPC8ZuiFwdCONmdmiyvyF8pEjxfgs0jI3Gn1ag9SrAYh1geHOFTjzbW4Mljwmnw==", "signatures": [{"sig": "MEUCIALpuho6BkiT98ZFnVcC4zDIrfh9hVilNoctMiQv3Jh9AiEAp+//cSWqqVwHSEAVdjo54+OZDckIKM2xQsIITgF0t5Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744519235198": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744519235198", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-collection": "1.1.4-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744519235198", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744519235198", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a464655637ff775bd781405b98e5c718224b5a6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-r1BbfO1wLlZrS3109NSipnft9D1Ce/3jZPamUn7W80fzEY/IvORjWgU15U/tRr06FNBBmUZyf+/uIVuU39tIlA==", "signatures": [{"sig": "MEYCIQDFDUdWr/9qneMv8Vz04QRHQ0y0e+Fe3PzOi28ZlMnNSgIhALhAlnN/vue0pGN0ezcVKAloCBP4LU4KN8g9WIeil9RM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744574857111": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744574857111", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-collection": "1.1.4-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744574857111", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744574857111", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d2f6d72c9fd75aa4a8a2a7ec393a3e469a884f41", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-nly<PERSON>6cqaWQ0o4o/TJ6CbIqLOy8535/FWyqQO5RkiIrqMefv5Fpw8jHG4F9LUjt5PfLmh0mI2TU3RPA3eEgxC/g==", "signatures": [{"sig": "MEYCIQCHQMCZcIETBZgGk1fKj6tK+7Zrwh2EAYI98Dte8SWK+wIhALXNUGXJSxTJMzYMuYJG0+mGBdsuINFReQ5Rw8eLTH2L", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744660991666": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744660991666", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-collection": "1.1.4-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744660991666", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744660991666", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "497dfc103ddcb186aa77d0f7709b700f7d8d286c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-lLVzc6S2HSY5TuuFzzMb/mxybgnNO22dAHqxmgbP1T2bxcMe2i0KdqxQxWRevG/PxHBQksUviEw8TlNbAT9sPA==", "signatures": [{"sig": "MEYCIQCSL1Y3GEoRETiC4mK3BNUnr9mRCtkGZD+oe3AfYAX42gIhAIWnKAYfTJxcgcXdnxkWSXmZliTAdIBl747bGHESPrPS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226035}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744661316162": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744661316162", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-collection": "1.1.4-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744661316162", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744661316162", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cfe8a019f2c47587394cbbc24cbd7986f1efb92c", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-UHGWEGhcl5McQwj0pGvV6v0TmFEz6PhxyGswkxdeghbpmN6+EJHjlEl/WtXwif0EjrpiqYqMw2ObHaqgfSSBTQ==", "signatures": [{"sig": "MEYCIQDeEvhC9lduUw+Z10lOL7v6FgMiZTnvedHgmJ43aP9/7wIhAP1uAQ7UBS7fxrNxHsu6R1hIQMiUchr2AHcH0KEegXnD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744830756566": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744830756566", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-collection": "1.1.4-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744830756566", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744830756566", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3ea6edaa82146e18c61ad8ec6fd6337621a0b267", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-YbukRhsbmrez96lGREPApdAw+ccXFAFsc6yDWV1PdNflrhTaSO/BgJvPbOPXfHxTu6ltKdPHoWolgAY/XkSB9g==", "signatures": [{"sig": "MEUCIQDgX0JJ4cvaj9IlzVUIZ1yV6Lklonf/n7e3WjFAtV/WpQIgMD1gHjKVLIZ2LlMjPPOO0zQWerpmVddUxLIU8pSfpRo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744831331200": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744831331200", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-collection": "1.1.4-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744831331200", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744831331200", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "689000f7dbdaf74735f0f60c54469781747db0c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-j+zDtsgkLd4/QaqS/edc4kSYI6wvECuJDU8HZcseJv+czuGnbZKCZ3B17/8xYcSyn394+qQ4lY2I8DggfDAiwg==", "signatures": [{"sig": "MEUCIFkSVZjuNlAbT0mgLRW0RvVlX66KDuu1fwQj9cDRVPNiAiEA7B2UG4PCnuPqwIFtpVoHN4LyMZED2ulkOXKMh7SSiyk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744836032308": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744836032308", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-collection": "1.1.4-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744836032308", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744836032308", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "74a05601ea1cc1557355b71138dbefc1990fc9ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-kbNlm6QupRricVorX6owQpgoBQyrUf0/tD1N4EK66Mr2Cddn1TFkbNePBRL0oXrdFaHcInd27hkYG4ifdEE8Ag==", "signatures": [{"sig": "MEUCIBrm/P9t0wXiLobiPlMWJGAqQByprvevVll/rfCcTjueAiEA8iMUyF7maO6fiDfueZMvgDaKG8mFiH0KWMFGT+jfTBo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744897529216": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744897529216", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-collection": "1.1.4-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744897529216", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744897529216", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "12c7b0338743f94c224050325e04090c814bb88f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-rLH1PSfjdS1p0X6vHnHeqjrnDouq6gJH0ataV3FOYf84rFBQ53CxKI+06X7rypZubnjsUNTLC1r6se6kutGmSQ==", "signatures": [{"sig": "MEYCIQCFctlU5Rax0+vg+FPlVtS+eRGWqA6+243YmlcUOFBL6wIhAOz7lvmGfdB6uQFoVClSf+2paFct89F5eydy+LWV8DdF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744898528774": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744898528774", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-collection": "1.1.4-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744898528774", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744898528774", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c58698624bbadbf297da4ed45ed81dbfbaf8dbb", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-K5VdRDBkV7NasGQecxv/ezROTqzLDvF5OFJ6uGYLbCaTFjsHdbsBQqMlIeOse2dSelTflyC8/OrmYJdYOHOdmA==", "signatures": [{"sig": "MEYCIQCvQmKN9hRj1KFXnaZJqH9+g/gnoBtytLtec/QUfTsBCQIhAPUcP7UnnuSMhCC9VvEWAzlkXgBjQAEW3XJjf0jubS4V", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744905634543": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744905634543", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-collection": "1.1.4-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744905634543", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744905634543", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1c24d5f8f94b6137bc52c18ae44c488c20ef9a98", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-cgno54zs3SDkLNcmDBhHJ8761OvA8wFI0V6LCOFe0ywsSNHlZVghTDw4NclQLXEwNLkCypJ0gB64Bhj3TBG/RA==", "signatures": [{"sig": "MEUCIAvx8ic6zZnAq4QH5YrjwCcPcCY+EufKTmcWdWsRL54eAiEA6k23+oJ1EQ5NAnIJzQF9pvTtXGSeWmELRela21LF4HQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1744910682821": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7-rc.1744910682821", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-collection": "1.1.4-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0-rc.1744910682821", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7-rc.1744910682821", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d79c1e80c90ad2e2613acd2db7fc77cf6d556340", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-1JdvzZbKnJFaUYKEtq9UFWVELKBJ5YJ53dLH+AkgO4xXWMpotNmNhuxF0156ImlbH+or+UeFs1TZ5Qo+nbWJ6w==", "signatures": [{"sig": "MEUCIQDzqUUsJgkOuAoNjL42cFjwh2dktRpdJ6j1YlWDZzUiBQIgOWFOkRJrrIzDQTN6wlZACMpTAylnTcMKJSDASqsvOxs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226359}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.7", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2f8c24c0f9f8bdf739c0d5750b4a85919e2b071d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.7.tgz", "fileCount": 9, "integrity": "sha512-503inulOK7nQwNq+2bCPI8eWcFLDKj+HFXVpuNqDLOXHGr7gwhsQd3PSYfpQIriLssydn9S3wL3/m2ZAD860gg==", "signatures": [{"sig": "MEQCIAjydIQpNekL1LyBg7XL2kgfkU2DdSp+omD7xUZw5U9lAiA3wabBobp6M+8z3p5gRojxrprVnEA46CP63oNp0uXMPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8-rc.1744998730501": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.8-rc.1744998730501", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "80ebc40a5e41e48ce4b5192b3027b88d293b9c61", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.8-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-TDdTDEMeu1S9owvbw//qL7fph6E1kNgJEQdQqaewkJaDr/lHBYqR/T7Q6ql47x2IZfFD/Twv3kf+Qb3IO7Sz+A==", "signatures": [{"sig": "MEQCIEkL3Tco14gHSayP1PhH/RpeSz4oZyYtbnUFB2b3eo0AAiA3Zcgm2uA0lviYq3MgRwxx0BYHUOUwcqL/x4G+t0TxhQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8-rc.1744998943107": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.8-rc.1744998943107", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e4400189c9d64ad88056776c765d5eae462cdea8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.8-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-63Suo7/d0lpoHsgkWcS3fgY1YZDG+e2lq9NUJ1iyToqYhcl5/4mNs2jtg6DfSbvYDo59UpeY9c/HmQyxYyelrQ==", "signatures": [{"sig": "MEUCIQCiB2zb9xv3IfE3nI8OrwSxdp8zSdLP1T3lLN6APoWEAgIgf8yut3PXJcw9/AaOM6FzJYZZLQCnpXVF7vo44SPOwio=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8-rc.1744999865452": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.8-rc.1744999865452", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53b773b986fa8c77d6ac10592c837e4da756828b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.8-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-uyzu7JkqWVHkdJTWqBbbCqIUL21blkQCOL4+yPgb5J9VOUM+HreGYPl4yIN42puw3+TTddLwuiREQ2mShdhCPw==", "signatures": [{"sig": "MEUCIQDmHSCUcWEHU0dLSQzDiCkAJkTjcxnruhmU8Qqf2oR+IQIgAkwyEgaS1ij+W6fOJjPrSmNI1DmulB5o8yRfHrgVutw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.8", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81a0b01e8ca50a72b09ce48837ab63d9c18c175f", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.8.tgz", "fileCount": 9, "integrity": "sha512-zZ/s2kmkvDB7X0N28WUwxLCv+U7TAMtqmgtZC9GkNxS4S2KijUZTA4tyiY3ApNgWdBKMAkphK0CU9L6Ivq9ITQ==", "signatures": [{"sig": "MEQCIF4pt9zgVUjO9KB/eH+Hjg9txAOVMuEL39ZOl0NrEu0fAiB/nlbXv18tHbf+PoHUPlSv7zSWqk46IogGYu2bLL4UyQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1745001912396": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.9-rc.1745001912396", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b10747cf65e4cf91137ec155052976f3735dc2ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.9-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-zBF1f6rZKHp7pAE5OWxwVbRSmnYoC2f9mx1Z0VTMfsc+rJoLIcEERyjwGFuml1+7DloyQbBSwlePe6Omq414+w==", "signatures": [{"sig": "MEYCIQCN2Rm0CfT2w+rgRbH/wPBDKSZsbPMmD5Li5YC9Y3rz0gIhAKUflu5EZFaf03P4d3VrV8haqLkJwQLvfFJtiC4amWVB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1745002236885": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.9-rc.1745002236885", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "04cd31ac2d4736327752fb33dacab6a541fc69f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.9-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-YiYYTmYuEHgLnmhbpr/3qzn+0w1b4elIQbY1Y8cB8lBihN8L2BxiVriW4NuKoPLy6QXtBj5eFWT1DLtjeTMI9Q==", "signatures": [{"sig": "MEYCIQDAMMrI9yUGwehhZmhjUoHr9GcAjxi9JmGhtDl667JvHAIhAMR6kYvnJ1qtbbFDEUO83tSE70KZJ2v3wuVSQyX1CJpE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.9", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "de652193e047da30f91ed0f2b1c294ca812341c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.9.tgz", "fileCount": 9, "integrity": "sha512-Z7lefjA5VAmEB5ZClxeHGWGQAqhGWgEc6u0MYviUmIVrgGCVLv5mv/jsfUY3tJWI71cVhpQ7dnf/Q6RtM3ylVA==", "signatures": [{"sig": "MEQCIHxLNI5vPTidI3tocejj6rbReV3yHTS51u41z9Uc1x9hAiATNwTUludtSgkWCgaBVQQSIhzHKPN5aSfgUIeMwGEFlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.10-rc.1745097595920": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.10-rc.1745097595920", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745097595920", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2e04c6fd06a4d5c816790b45b5f35d22ac0478b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.10-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-WbUtaq04SdNAF12zXQxdlL229zsKVinuyzAyilq68ySG14eDdaR9/Wuz/u05m/k60DTAlhJepB9a3KkYAstVnA==", "signatures": [{"sig": "MEQCIAV/LZkJ73ybLYosV5jhVCRVTlPjTCLod+wvMorobLQqAiAq6v5kcig71v2qd7yQnVy3cZsksfksyJDppBVfUo7XuQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226292}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.10-rc.1745339201309": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.10-rc.1745339201309", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745339201309", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "04186413dbfd9507761b47e553c6744f380c6163", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.10-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-INzG5hj9vksZn8uYgTYViiVYWEbhz8JgcnsNm5RdlHlJPuIQ+3WTo/AW7+0WPiwhfWyvpZeUvZ+x4Jb0/XImVg==", "signatures": [{"sig": "MEYCIQCaLyIolFdD5Vzl1TjAGr8VssILNlQ1HTpWXafhfx3AaQIhAPeRXQxIL9oONbTuVypE6ntFpLpuJV/KVupHg6Jep2cI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226292}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.10": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.10", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.7", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3677d37f8205719b03ec2c67585ebbb42142d90d", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.10.tgz", "fileCount": 9, "integrity": "sha512-kGDqMVPj2SRB1vJmXN/jnhC66REAXNyDmDRubbbmJ+360zSIJUDmWGMKIJOf72PHMwPENrbtJVb3CMAUJDjEIA==", "signatures": [{"sig": "MEUCIQC12hn8Ul1Afg4Xbo3hjsa13bXdhM2dDODM04CBdveWuAIgDiWbe5SYlcePBmNo/g2MmF3wL2tuVtuQRsHkZDajyRg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226258}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1745345395380": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1745345395380", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-collection": "1.1.5-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1745345395380", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1745345395380", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7dcb43890a2c9140c823885d1e553297bab9edb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-qsKa8KH9iRCuuNB2b/L0nwQbK5uUBKQ8Q7W6RWnA+/CvDoIFFm/XfJYvmIJAVVgmU/JtUNSRVoPBZnQEE+K6Jg==", "signatures": [{"sig": "MEYCIQDxXGv8MY4y5x5TYs59j4ih8ntDdFWlK6xhx85WkJ4l4wIhAMT78CajHnblGgTlEDI+ug5GK2iq+8tSBSpilLPzsrsC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1745439717073": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1745439717073", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-collection": "1.1.5-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1745439717073", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1745439717073", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d873a7b917d1d2e320366f7ab2bd8723a8bf04f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-d4n+eDVylIXvY1DEErOxWzhbPKM4B8kstvbi9fLOTwpAvDhXCT2AMY7zeaEZznhmfbH6T6WvI4R+jjPDx7ozHQ==", "signatures": [{"sig": "MEQCIEui3wzZjmW05pYfQ5hxr0qVwwE+CfT9SQ/KiK+iEDf2AiA3ZottHcgdIjAOSqihdXciEKbnovTrHxdfJzS7FCiF2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1745972185559": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1745972185559", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-collection": "1.1.5-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1745972185559", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1745972185559", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3afc163e81556cd915e588fa59094aaab6f8fc27", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-c37UyJ<PERSON>+jfQmfAQq/DmRfHdJFRDaEKPVYw7FApJYvUhgXyJ7RNFwBwKFBRnaUm8K1KZy669iUWeO/5VhsoPNWA==", "signatures": [{"sig": "MEUCIAxhgMRrPLGnkcdn6zy516YQk4NO+cHzxMkn/mS4g6OpAiEAn3l7f2JoB13dhagW6OAx9aD30LeJCe2Hu0w0zZvjoYg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1746044551800": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1746044551800", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-collection": "1.1.5-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1746044551800", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1746044551800", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d3c623fef5ff10a97fd334e909e1271d4b6c9fce", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-E2s2CVmhgfQraI3LLh0EHJvxtRT2LhiNrY4umqVbDc3TWIhAwAihPvxcibi+k9LTZq/6RgnJ2p+s25m7fC2e6A==", "signatures": [{"sig": "MEYCIQCyd1aInQK1zW5swHl7b66Sqp3u5ujXNcewmQiaFjkTEwIhANU9d2z3yAcHXmQQLTEWBUU+aqJzFSI/TDCwRZyHnub5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1746053194630": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1746053194630", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-collection": "1.1.5-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1746053194630", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1746053194630", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c90ea98c115151ae11d80003e90154d44d8d8dbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-mjHXWI7+mylSc93ahQj74g3Yjbh/HgNxSmr9elwhXhs5tIiXVJxhxEz/Rgx058GReZXDbatNbZ0BL2cPPaYsJQ==", "signatures": [{"sig": "MEYCIQDFEOfzLvEkBVGugqT5WcALEBhkfpmlExVVa19HGOBKJAIhAK93N4/17KyqwQkrJxh7nBbVzwFGFWTqmiix3/NcchT/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1746075822931": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1746075822931", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-collection": "1.1.5-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1746075822931", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1746075822931", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b01f685632f031b2116ff7be6656f019355d8c0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-tngFvVxEP37ALSoDXQhqQiIj/tOH9HUFMZ8jHjn0eN2YodNM5UHIvVLUXwcm1vGTYFkpXZ+7RTqdpbbrTik8cA==", "signatures": [{"sig": "MEUCIQCLTfJZZ5EgYXCPSM3jy4r2sMQNW6li1hwD0d+FjRYZrAIgLbI34H0aqWPD08jiZMVRpIIrxdS+nUk8dhnccQf9LTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1746466567086": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11-rc.1746466567086", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-collection": "1.1.5-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1-rc.1746466567086", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8-rc.1746466567086", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6ae6fac34d1b36b0c6dca2c0db49d6672fb5095e", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-jSUEiKgtFmtlnojzRn1DDArSyjDKH83dC4Z3POk2Vui6/J1x5VDvgEltNm9qJJ+SwGH3wycE7SgEsFqz05k5nQ==", "signatures": [{"sig": "MEQCIDtop/VQW36FWs9ZgFmsCWTVMzBZvMCPz73TkXjAnvhUAiAtIo2qdJ9xEg839VSbyJq8WHC6P8gAi1xbrIMyyhWgGA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.11", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-collection": "1.1.5", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.1", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.8", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c172f1cbbf0ea7588cbc28e94b2a8262f230608a", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.11.tgz", "fileCount": 9, "integrity": "sha512-gEY8aNXGEFdLgx/o8LlDnBrSFkHbvpx9JqRUIYE874ltwDAxnbRDZYYCLD1yQ/KJZJX37pE92QlAZihmuL+y5Q==", "signatures": [{"sig": "MEQCIENWr8rx9vEexKy53WEMYSNQ6vgxCKyoazjDl0UpyY93AiBKr/7ycbOAeJc3DZZq90q8j4QOPBshJug0ZGHakgD2rQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226258}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.12": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.12", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-visually-hidden": "1.2.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.9", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d86b4ea4e12617186f912a0ceefb81379cd60a82", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.12.tgz", "fileCount": 9, "integrity": "sha512-iExvawdu7n6DidDJRU5pMTdi+Z3DaVPN4UZbAGuTs7nJA8P4RvvkEz+XYI2UJjb/Hh23RrH19DakgZNLdaq9Bw==", "signatures": [{"sig": "MEUCIQDjKeYfl9dUiUEKBMZD/zCAHXmo8XH7vANOipdhb04ZOgIgYbp7HLwnSm+6S+6pYWjOBK+2MYTN9cA8M4MKlpYFvKY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226258}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.13-rc.1746560904918": {"name": "@radix-ui/react-navigation-menu", "version": "1.2.13-rc.1746560904918", "dependencies": {"@radix-ui/react-collection": "1.1.7-rc.1746560904918", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-dismissable-layer": "1.1.10-rc.1746560904918", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-visually-hidden": "1.2.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-VMBK1MZmR9vYkeMPuJsY5UwVNIMfIh+GRccC511SHzJNxktTh5A3B0yzsjQTt1//T2GKZCKiDfzfEHf4sEcVvw==", "shasum": "ec91867c9dd365ad9f816c8ca2b0779eae7bb1e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.13-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 226404, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDCkwQls6h5+sQCmvNnEGtDrru1/+haYId+2thgdn/pVAiA7thzP88YHVLPMUmqVlyBApPhOY+AORTSobmEqRucByg=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:25.741Z", "cachedAt": 1747660587793}