{"name": "@humanwhocodes/retry", "dist-tags": {"latest": "0.4.3"}, "versions": {"0.1.0": {"name": "@humanwhocodes/retry", "version": "0.1.0", "devDependencies": {"chai": "4.3.10", "mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/chai": "^4.3.9", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "f5442671107e06b8d67a85f3cf0064b9f70fa5f0", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-xI2dTCQ2MgnemVbWPq+jz4EWBUjCKo4GK6fwmTiHRsv0bNLrAmpq65J1VA1GFvPH/BLioXwG8C0Tg8SpTYOSMQ==", "signatures": [{"sig": "MEQCIHkqbe2pMubwjppP+QuAM/V3PNTDjEH/6lGLhAM+uS2iAiAbD+LqaoacyXaWUmaBJgnaogPXjm6JBVgOB84MWuPmyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39832}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.1.1": {"name": "@humanwhocodes/retry", "version": "0.1.1", "devDependencies": {"mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "89e9c7851884280b6912b674f84a0e90262f337d", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.1.tgz", "fileCount": 9, "integrity": "sha512-FIvUfTwPKMXUEtM/rTtjrDTpD06vU8KKxllGMkh8kgYQh6QLuBjyFy56MQJhyz/iwpBuhpQ1vNkYssedf48HcQ==", "signatures": [{"sig": "MEUCIQDAuHDgH/le+yNZDTbSyNKGXpgUTW7UyIvhpIaA8pMKhgIgEvAyHEHfrwrPq3R51oZDH5cUWQuaAKcFcWZoXVD3OQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40232}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.1.2": {"name": "@humanwhocodes/retry", "version": "0.1.2", "devDependencies": {"mocha": "10.2.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.2.2", "@types/node": "20.8.9", "lint-staged": "15.2.0", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "40a27617c44b56d2bd620b5b8cc31dc052ffa595", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-JNWGHkYfWI0+YgRHOwkFKjOuelfypQtp0GSx0lsOP9jU1Tj4f8k0x4dcaJSEZTp61THZJ+f9PJRh1GzYlQqHOQ==", "signatures": [{"sig": "MEYCIQC2+dZ4jgWh2aB5vgks5Eed73NTkQQGlwxkLLzrKEJSMwIhAKAP9pbqLYPk34/GnRyJNkIivj5lsqgdxzhdNQaMl7xH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39760}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.1.3": {"name": "@humanwhocodes/retry", "version": "0.1.3", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "14a3688e03f3fce7ec5c93e4269b3b1d3af3e918", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-2IPnKL9lUTR23r/xrjoMZ3k+Z8sWNNH4UvTePeOZ0q84VeqgRoMN/+c2WigHWSQxZZEDXUm1CH08cPT6/CoxYQ==", "signatures": [{"sig": "MEUCIQCcn5UNzuAxl5H3JO9sZdZHcYhUf52uiKw2rx3c4rWWKQIgS9wU7uWQMfe6ZUSwzPLN7aSqRsCAvwqswd7zhAsxF4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40189}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.2.0": {"name": "@humanwhocodes/retry", "version": "0.2.0", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "d1af43b33c27d1eeda9132176e1da51f5e4afa28", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-j1nt8hWsWRXzUPTl9rpoMBE/2GWLBhZPEWiHoLKz+RNsOAZ1CmVHgmcDK15uLe96JSjfyiLhsnvt190Ph8R0Hg==", "signatures": [{"sig": "MEYCIQD2UxAUE7QkIM2hqdu41dccr+KEEM4t/nVBxL5upv92/AIhANNPkL0gCdZQDpfVnGpn9Wtkv3SLsABCr1YYOrn7gXrY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.2.1": {"name": "@humanwhocodes/retry", "version": "0.2.1", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "b2d28f1b8e4f24f4313fb6684ce534f3f25b5ed4", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.1.tgz", "fileCount": 8, "integrity": "sha512-9x0wUY3wAq5qnniIlic/70FTYUKfRCkCcb49CPzcE/kf1sUQ+2bk195mGQ5Nc8vuvFSW0cbx5TFue9B/if1poA==", "signatures": [{"sig": "MEYCIQDduORGB27/W2pZtFCZiXgpq11qDBYtvaUPBGhEXKnRlQIhAN5qi/nuNr08yHmsqGRXaRjYphj8JGnEkZbGoYOVxicA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.2.2": {"name": "@humanwhocodes/retry", "version": "0.2.2", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.3.3", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "f43dc06e4c9d6407a9c6a86699565a730cfb8a62", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.2.tgz", "fileCount": 8, "integrity": "sha512-tFbq5+l0BruPg4JvjrwEBcRLEHe8fC+UcI8tWJgmKrJlKtY8/JQjmRuECSOZxFnnzTBHgCbLvJ58jRyzvZ5uaQ==", "signatures": [{"sig": "MEUCIQCAg42G+8jrSdNqn6/pPPuAC/dbZ5JjTtxbPKIyqt4k1AIgU2MdhKGE1VSvCx3Cm1XsmqvdFDYPA2SVl2AYeJppANE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.2.3": {"name": "@humanwhocodes/retry", "version": "0.2.3", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.2", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "c9aa036d1afa643f1250e83150f39efb3a15a631", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.3.tgz", "fileCount": 8, "integrity": "sha512-X38nUbachlb01YMlvPFojKoiXq+LzZvuSce70KPMPdeM1Rj03k4dR7lDslhbqXn3Ang4EU3+EAmwEAsbrjHW3g==", "signatures": [{"sig": "MEQCIG6IYS7ov5dqgBmXVTVKg6/nxnPWdtyJ/jqanRqPz3L1AiBqlcOvczLzNUFpO/U4rpLYNTxtCuuzaDgSfC7MdHJgJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40104}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.2.4": {"name": "@humanwhocodes/retry", "version": "0.2.4", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.2", "@types/node": "20.11.14", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "4f3059423823bd8176132ceea9447dee101dfac1", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.2.4.tgz", "fileCount": 8, "integrity": "sha512-Ttl/jHpxfS3st5sxwICYfk4pOH0WrLI1SpW283GgQL7sCWU7EHIOhX4b4fkIxr3tkfzwg8+FNojtzsIEE7Ecgg==", "signatures": [{"sig": "MEQCIECVZdP+Uu8rMexWJxAaqgzch9Cg5CdPctGxWOYBTBI8AiAmiw+0dbfqARatUY0b2OHigpshRL7ZR3hRgt33dOb2RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40551}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.3.0": {"name": "@humanwhocodes/retry", "version": "0.3.0", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "6d86b8cb322660f03d3f0aa94b99bdd8e172d570", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.0.tgz", "fileCount": 9, "integrity": "sha512-d2CGZR2o7fS6sWB7DG/3a95bGKQyHMACZ5aW8qGkkqQpUoZV6C0X7Pc7l4ZNMZkfNBf4VWNe9E1jRsf0G146Ew==", "signatures": [{"sig": "MEUCIAm7gkfCgiieJD6KeNUyTBIxl5ahCZ3Ko22oWe6IMHakAiEAiR7zU2twKZpzpPINd8hTDpArIfuO76QYkNH9BskS4wo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44273}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.3.1": {"name": "@humanwhocodes/retry", "version": "0.3.1", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "c72a5c76a9fbaf3488e231b13dc52c0da7bab42a", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz", "fileCount": 9, "integrity": "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==", "signatures": [{"sig": "MEQCIBdUz35OOPbuYuBD5p5FiLorvp9uEs2rPT8CBbE2ey7dAiAILjgIu3jtD3duONO2LR0oj9vvLHqgg7U4/Sc9IPW2CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45429}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.4.0": {"name": "@humanwhocodes/retry", "version": "0.4.0", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "b57438cab2a2381b4b597b0ab17339be381bd755", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.0.tgz", "fileCount": 9, "integrity": "sha512-xnRgu9DxZbkWak/te3fcytNyp8MTbuiZIaueg2rgEvBuN55n04nwLYLU9TX/VVlusc9L2ZNXi99nUFNkHXtr5g==", "signatures": [{"sig": "MEQCID28OOR4M8uf1Eekc88/HPYPIJwLEPuhrG0qRXSv+k7PAiB/W+2SbawR60hFFild4DUxnJJovxqBXclophIqX89MkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64678}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.4.1": {"name": "@humanwhocodes/retry", "version": "0.4.1", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "9a96ce501bc62df46c4031fbd970e3cc6b10f07b", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.1.tgz", "fileCount": 9, "integrity": "sha512-c7h<PERSON>llBlenFTHBky65mhq8WD2kbN9Q6gk0bTk8lSBvc554jpXSkST1iePudpt7+A/AQvuHs9EMqjHDXMY1lrA==", "signatures": [{"sig": "MEQCIH4awP2Lto0z+1vPEZAz0IoT96rmosVV1EIpX0IOlo2GAiB+EX0U3NVIp8ohhPQ27R8m7UFlQcg1CJ21Tq3ay+4T/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64873}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.4.2": {"name": "@humanwhocodes/retry", "version": "0.4.2", "devDependencies": {"mocha": "^10.3.0", "eslint": "^8.21.0", "rollup": "3.29.4", "yorkie": "2.0.0", "@eslint/js": "^8.49.0", "typescript": "5.4.4", "@types/node": "20.12.6", "lint-staged": "15.2.1", "@types/mocha": "^10.0.3", "@tsconfig/node16": "^16.1.1", "@rollup/plugin-terser": "0.4.4"}, "dist": {"shasum": "1860473de7dfa1546767448f333db80cb0ff2161", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.2.tgz", "fileCount": 9, "integrity": "sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==", "signatures": [{"sig": "MEUCIQCtJZpyelUkS6bWH19of8/9kAADuKl8p4sm1dn7W8sA2QIgSpVn4yC/Sgv/EMPsqpsRQ25k/IZ/yyRpi6zpOwY24Kw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 64979}, "engines": {"node": ">=18.18"}, "funding": {"url": "https://github.com/sponsors/nzakas", "type": "github"}}, "0.4.3": {"name": "@humanwhocodes/retry", "version": "0.4.3", "devDependencies": {"@eslint/js": "^8.49.0", "@rollup/plugin-terser": "0.4.4", "@tsconfig/node16": "^16.1.1", "@types/mocha": "^10.0.3", "@types/node": "20.12.6", "eslint": "^8.21.0", "lint-staged": "15.2.1", "mocha": "^10.3.0", "rollup": "3.29.4", "typescript": "5.4.4", "yorkie": "2.0.0"}, "dist": {"integrity": "sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ==", "shasum": "c2b9d2e374ee62c586d3adbea87199b1d7a7a6ba", "tarball": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz", "fileCount": 9, "unpackedSize": 65711, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDMCRthHuR66AlmhYiZ+rKkXT3JnOBdKjwGKorPTvCdnAIhAN3JlApaHn2fVTLplXStspkNNpyDBbUw0kQTkm05GLkX"}]}, "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}}, "modified": "2025-05-07T14:25:57.572Z", "cachedAt": 1747660588935}