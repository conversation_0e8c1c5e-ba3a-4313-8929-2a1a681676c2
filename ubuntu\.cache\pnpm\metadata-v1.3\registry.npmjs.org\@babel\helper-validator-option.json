{"name": "@babel/helper-validator-option", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-alpha.17"}, "versions": {"7.12.0": {"name": "@babel/helper-validator-option", "version": "7.12.0", "dist": {"shasum": "1d1fc48a9b69763da61b892774b0df89aee1c969", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.0.tgz", "fileCount": 6, "integrity": "sha512-NRfKaAQw/JCMsTFUdJI6cp4MoJGGVBRQTRSiW1nwlGldNqzjB9jqWI0SZqQksC724dJoKqwG+QqfS9ib7SoVsw==", "signatures": [{"sig": "MEUCIQCM9L2CB9tqoUl5Axjwo0Sffm1ZraunDG79gYi9o6+J4AIgPBz3Kd1Hqs6oLHpW1n+woFYea8jUmQhzMWFb9WnCRhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1lbCRA9TVsSAnZWagAA06kP/22/azOIbd0m4z3r7Ky1\nbyUz6BvnRc/TnnQvwk5xopeyel+PI6vcuBCOsjdS9ll3TSmuiHBt0lwLULbJ\nEHl6kNEJFLSta6GW2TOidZA4CYxUzsgKpCABJ43AXsSop8WE5YaJrEtzXNoI\nan56eli25kfFBreki9vYIm7vHbDF9FqS/Gf91G5T3dk+eX1T/RLv6kTbQp7b\nv6jyaxnaXj2WPSmjE/0hLySxeHY+7dGcVQzT3/L2rQTdG31gend41HXWtLPb\nJvBg9mmeCg3n7eA52FBkOncymyqB5SRNZQi8sj8FJMUnA+hnqYlEhKnhtCBE\n1XcGzj+bgPzWMCTCxCUvz/Z8iHsf4ulMEeZUFl2hMXUuC6ALfuEZcEyqkwFk\nQryF4keNz08GUBP3+OwgT1Ow5KdN6gV9j4AcY8M2720PWMwz1hSfW+F/HTgc\nbpywUrNxcaqdf3UKE++foKr75tsintEBo2DRwVLdD7WyPZ1TqHz+VKAKpuqI\nhB5IpyaxnlJvkwhkLBstcs+qg2mePZ7948oiB6ULmhDK/Q7xgLO2c2TSWnNq\nTl6n562H3EesgDNxfC5a4IolyVEcQFUB/dNufTdvXaEMBeSepKw6pSH9KX29\nu81DlTUFzz0f91laCunnzOHccoxD4IepdQJ2PKYwEVnPoOIiJfYK59HPVp+M\n7T3P\r\n=UcKU\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.1": {"name": "@babel/helper-validator-option", "version": "7.12.1", "dist": {"shasum": "175567380c3e77d60ff98a54bb015fe78f2178d9", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.1.tgz", "fileCount": 6, "integrity": "sha512-<PERSON>p<PERSON>absXlJVWP0USHjnC/AQDTLlZERbON577YUVO/wLpqyj6HAtVYnWaQaN0iUN+1/tWn3c+uKKXjRut5115Y2A==", "signatures": [{"sig": "MEUCIQCYoe8YR+AdpikCr8owLDzOU9AAI511rUkjzUk8YJHArgIgHac1xRA95iHMLlBLBFaw6M/9ShcJYVLw3jQMDEIeUh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM+XCRA9TVsSAnZWagAARMIP/1oElgEAzk6y9So/GDQ1\n6UITFoSIfzzQaEJ7yTI3voGGIPUv2QsUL8IeIM0Wm3QPAyGZ2a9PmDW7GgIx\nwyA0Qn44mxwsdCJ3BkNP/yHO3/YkIWT8n14+w8Dv+SIS/uhqagL8fj5HHGBo\n0esfhYOQL00rmjDebXx+Ya13JI4w12l8PkXyrisIViregPdk5dfPKrtd7hZq\nLv1mdjc+JxyBRDv3QvLDnZ6ASZVaOSNhYTzv4PftnziIQ9X7KHaS9ExqGCnK\ne5w545l80vPH8B4GLyhnF48edW5n8moA3qeg6JkYmgpBZAQrSQeSR5tDqaof\ns7KQUe6l3hmsiNVuHPz2503EShCG8uZhiWtefMLqtEYCTh9g5wB9phNyO00L\nWWpxR2+chBRvKKJGyhXasQSnr5qLXU0hms2M2iGxV2VLnczavt55rhTjGczA\n1QBamGBsCYcsn+VgW/wPv8LDBl08yaR75/+SfbYb8wlhfYk892h16p8Mj8df\nYUocY6iEkFB+crj6USjGpqIomyczaMvfsXojtUR+z64ZUn/chkCM2E7ArCaY\n85mIUTyFccjtyFxKqC8xzf0VbiqAHrCCPw/UJ+vw1LrN2SMAe0Ay4Bh1hfax\nDhOl96HlDsMftaojzGSwSBzGZ8Dx/UpLnaAfGACi2Yd2uGVOPQ6pJhQKBMLv\nq2f6\r\n=BUf+\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.11": {"name": "@babel/helper-validator-option", "version": "7.12.11", "dist": {"shasum": "d66cb8b7a3e7fe4c6962b32020a131ecf0847f4f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.11.tgz", "fileCount": 6, "integrity": "sha512-TBFCyj939mFSdeX7U7DDj32WtzYY7fDcalgq8v3fBZMNOJQNn7nOYzMaUCiPxPYfCup69mtIpqlKgMZLvQ8Xhw==", "signatures": [{"sig": "MEYCIQC64g0wPwTIVnB0oOTHy57xu1P/UqrJUCH6OdkR9GqbRgIhAPA2sP011ajDxXpi/nHp69grcE1Gcw3dgB1EhOK8iVTl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3TCRA9TVsSAnZWagAA4TgP/Ah5lskn+zF8YreOscqK\nOxbfAENOsEDEKBZ42mwNUEf/D+Ug9nDFNF/Vn6DPZTRJiwR9/z08He10ZEel\nLg9y58A5zH/y0CweI3UY3sGBtz7CFtV4gHBxXv8vcGIzdHUhbbmW7QOyiE9E\nGtvQ3iEwVjvbSxbCRSKggDGxx/Xwiui+snpgQIZVrABA9BX/KTgVftb5apHi\n5ezeJwXcULwwQmeJWFWIE4Gg0CwaqA2U6S73xkWNrQQcmTjpib3Sg8viorP3\nXYHPND4dUUxoHbRYGKXO6aUguVyJtaw1Ocsxv+cQQuf7g7xkxYFo3gW2tgfT\nX8CVBbM6u6n+uTbZh2M0m6z0/dDkbF+ygxmPBae8lK/PfXpoBwvye0Z8DiG4\nlOZFKgp5ghUX+EyoO/Hv/lo5ux9t4xrkQakpcZ9oYmPcKmthuLyZ577jqCuk\njH0MlCG8SqPMEw0saXTGb6DePSL7akYRJNY7fl7QXjTI6ufBKQlVgW3p58+g\niOJ7rf7X5UZi817IEddleAXpDSGgME5lna0fmJuirsDyH90n+FqfbnMXSxvr\np5OYWtjhTJqLZSyEfgPEk4bFVsl/qmllqeRlyj2j8694/udN6/Td8Wiw4313\nCBDY+7TdgQ9fG5682wWLwxsPkzx7YLPNg2901tE5ASWOdl2pHaK9QGW7vQIt\nHEya\r\n=Px/z\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.16": {"name": "@babel/helper-validator-option", "version": "7.12.16", "dist": {"shasum": "f73cbd3bbba51915216c5dea908e9b206bb10051", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.16.tgz", "fileCount": 5, "integrity": "sha512-uCgsDBPUQDvzr11ePPo4TVEocxj8RXjUVSC/Y8N1YpVAI/XDdUwGJu78xmlGhTxj2ntaWM7n9LQdRtyhOzT2YQ==", "signatures": [{"sig": "MEYCIQDhz13sZ8LrSHob/El5LEKCrbm9CaMXqy3g2FKEdqOEpAIhAOG2PwlPoqa6aTQWQSucIdkWYZrVILfgH0N6cnwgkVgF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJbPeCRA9TVsSAnZWagAAsVgP/ibDuwzopgfKSGs0bFqG\nljac+g6+yaOLfwgcOmADOd5W9aMvFYqE0Vw7jtBkhUnyJTXwhSGKHqf8VQbr\nEEjBzL68gKTbtNkuNZ5Q9WtqqRBlgCQpHquyneOdSeliM79cbTzdYd40r3Bk\nffZbw/p9AYb6qdckeor2HZ8fs4fx5Y5nBuvY04YIV/7D1JQElYDJJoqckB/m\nyWFHNnmEyO4QGLuKmKRjtbVNE4u+l4L2JXge+bz8mtUe5gvgRamgqub4XMB7\nzMPVedfpFmbOvglRBqhclWxMv5wuaT598r2Nv+KnbNJvZApcLiMnBfozkM1u\nrwMkhOAXQQpkv3cd6Nsa/iZUdhoW/A0ziNnaA7gUNenN4WrrJq/6wU3F6Rbx\nFIw9tEY5uSOyW3vKCPCiXyOGIKV2Po4GU/j6j7th/bC5Wh2aG1i0O1uvKM9b\n0nUBSIpkJ9UmhI3bbpV+3FzXW6j/NPF61UfiIhLhQVlrd0szoZB28OpMBj71\nZL/bYWVfwAb1D1Dd08/GLF5OdkOLjvt+wUSaBAMkezcVbMnHAyjyngGSDb5W\nSXXuVYsOe5nERzTgu9zt2u0awmCjbRacx3ogs84NE0mJ5Zrt2Onpzp4ht67f\n9djSgx1qw8NOYtKXPjI5PqhfVgoXhuQvi62WEtdFDhwkgMbgzgzHseWmIghH\n9L5A\r\n=0Ib+\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.17": {"name": "@babel/helper-validator-option", "version": "7.12.17", "dist": {"shasum": "d1fbf012e1a79b7eebbfdc6d270baaf8d9eb9831", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.12.17.tgz", "fileCount": 6, "integrity": "sha512-TopkMDmLzq8ngChwRlyjR6raKD6gMSae4JdYDB8bByKreQgG0RBTuKe9LRxW3wFtUnjxOPRKBDwEH6Mg5KeDfw==", "signatures": [{"sig": "MEYCIQCWFgVjOVXkw+hHNk34OcZrfxEjbz8lHYZvO5b9kkqgTAIhAI4MPq6tnLnIOBQ+V1AqR6lmD1CnWyc8q2HJlQV2+/cj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoPWCRA9TVsSAnZWagAA2N4P/jv8/BwkbUB7Nt0bDc/4\njdvkG5oBqOmKBkmgATi+2RVmVmaK3QIK1m1DYi5eDurwFgY2MuII0CxpYTn7\nRCakHGfmqpur8UMT0xkyj3ZEwrHa7UvOCJPUn99z9QO2D5QxtHdjVH4IF6R8\nVhA0dB/Ofs9ox74k37TyEjb9JnC1uDrBhF7tACZUaIxNoywGr0+RX7wUDkcs\ngWz8C+3XIJyBuX2WJTNZbDIosjoN7CSt+FfPgLmROYKHPQ3TEfouKAzGFj/0\npmwCpr+CiLlROeYO+Q4HyUBfNAFYlVYibVgIPp+xFxc5Qyl69Is8CaCi7D7p\ntFzkoGPeyJb5j0h/nyUvgzqf+jdrCOiXVo7k2kd4AqX8mcaQj7yge3t2FLNv\ndg+gLNl+wuQPh1foH8y3ihUkR3gYs22JGx042WH/6euKaHgKidHtsfXMlRHG\n3aDRCWZ1h2ClEqx+IB7Xt4ob54QuXMCpufaCXUWH7yyTqr4gpiri8wVzqL/j\nTycPzE+//ehTAnjyKLykcMzHG5nQl1eLqRHcHeqVBLzF01nqsgJguBfrmGIi\n9bNTASsUCoR0CCXWNRDfG8LGcuOJ27lu4b2Pe20ycIRnUrgFxROCLSbifUBu\nl4Y/z5QhS7qsxs95xnZzLNM5sl0aK2+0D4q+E6hU8bFwV61k8V91Icjb2Ahb\nurpd\r\n=Awxn\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.14.5": {"name": "@babel/helper-validator-option", "version": "7.14.5", "dist": {"shasum": "6e72a1fff18d5dfcb878e1e62f1a021c4b72d5a3", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.14.5.tgz", "fileCount": 6, "integrity": "sha512-OX8D5eeX4XwcroVW45NMvoYaIuFI+GQpA2a8Gi+X/U/cDUIRsV37qQfF905F0htTRCREQIB4KqPeaveRJUl3Ow==", "signatures": [{"sig": "MEUCIHmRjmhV18Ag/d8XgbJ1YDgqWSYlexOogMZ27UpMjjohAiEAmEzcrCy9Jr1BuGKexzBUK8YiL7yIEV4FNvU/uTfBFnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUqXCRA9TVsSAnZWagAALToP/AxEH+RbN4pQYphACnMP\npYKkG3jm59dVZss5uf83cahU4lEvEXiW2ALxGbGWNvO2VEHrl/UbB2BQsSP5\naIRBbQhKC0dXKe2K7Zamy7KDuVqhns56OiIistrKZaGVN2cw3heNlsDUx0yd\n+pOAlkbU1jgX3SjIVCeAQ4pEQ708TfSH8/YD4VZXXlZqOJXyyYpv32CgM27/\nzScns2gPfB5R0WMeuqjJ3HzG6uVDebaAipMc3QccX/Qk+s4XpOfie3H0QVho\nr5Y4rwdiPAtgQzO9Ob8dPHK3ZEOq1DdQ9FpGMFMDtLHDJBeeXaSwCV7Veeaf\nRV7JGJxqUijz0Cm20NAWbxJzo6woVEESCYKiHpEPGnWSL0fdd2d3BzlzIX3u\naCCef0peCMSiLaRImxwaJE3zavIRbrQtak9uZpExE/GhTQ0YiF3peo04Wu2z\n9JQgErGErI5XKqyaMDeZYu+gnVDcwCk+vSpdnaYSkCr65UBbvt8lD0bVLiI+\nDHZbWd/PmvBmuguzFIL2L+4WBALDPTWL2Xs/d+VTIzhwVDsUabul7lZ85MKV\nwhL2ibzc0YkYykvF572ycDtXpIvFFxRkM/NunEhuSRH39jULPnswu+/dTYu0\n1CAlEHVOrEgZCo7Zx7YTpyaDc/OGiOygQILGO1uudaHKtOWwNeYybwVu+fAD\nWGPb\r\n=UnGT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.16.7": {"name": "@babel/helper-validator-option", "version": "7.16.7", "dist": {"shasum": "b203ce62ce5fe153899b617c08957de860de4d23", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.16.7.tgz", "fileCount": 6, "integrity": "sha512-TRtenOuRUVo9oIQGPC5G9DgK4743cdxvtOw0weQNpZXaS16SCBi5MNjZF8vba3ETURjZpTbVn7Vvcf2eAwFozQ==", "signatures": [{"sig": "MEYCIQD5FDUjiszOD12IdwRsTKTKeYlr1JDt4wTsXAlDJ5ZUbwIhAKiBulfxeCJJVOl7LJQcr3z1V8NYc84G7BBBCk/jPNIF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0ACRA9TVsSAnZWagAAVHwP/AnQ4Enhpu363RRYqPmw\n84oo07LLwj0xfaeEPKJo0ZZEPrEx+pNjqkffzoMu+R5yCXT4//maNtkHO+OL\neOfJEJmFGtQ0SEBbyiifEK9ZrTMKfalgg5v+LoiBTUZ47t3qrQeAg4U8x9ME\nBSv9P9liF0M5MpZwb0CcxFqG2zingquCQoYZM0szqShK1YJjiDyE2HTo5JFg\nOKGKFPN9tDMlz5ZyqyRSAdpDH+cShMZryr/6JsGggtpJVPdugzcGo2ecSA5n\nb6SZBDZ31ktNqM+/TiT7CZGEQdXBszFRZjRwSR6A3gHqLK263udEJ6djdir1\nc0tQB4YOUZEoi7ufltowZJ4zBCDqfoSkPLmqpuDmqAWk86aUB0ByPaaw5jvn\nri7W0fqNuzdtrzb3GZQQCbbN9wFWyvV/5ny5BAgwNOFq6qmtLoUHpVUElRKr\nsfxYrO3E9IPutTr/xDD0X4pclyA54RFf8gAOqtT+7cLW09oiZS7zyCbtiXap\nRwkSFZX4gqkWkk7v5/ODlw7pFpKyuqx09w0Ed9AX8iYjwvqFgjUTBlPgfgR6\nJ8oNl1BpLqiNX0RAgeZ3Evqh4KvVoOTZiHndjR/ZmX6Zeek9U8SFmYB86yW+\nEp+FTOIFT5Q5TCqE6EhuMhWJd4UXqeWQCElWs46NbLQFDCQpWlp6fbwutn3f\nIKPs\r\n=DS2+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.18.6": {"name": "@babel/helper-validator-option", "version": "7.18.6", "dist": {"shasum": "bf0d2b5a509b1f336099e4ff36e1a63aa5db4db8", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "fileCount": 6, "integrity": "sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==", "signatures": [{"sig": "MEUCIQDefE//ozIumGIlaeKIddw5E9wmG0y4adFFmSFH/gG9UAIgUcDmE8giYwQULmnDl9pV8SqNT+E6xS/zCUA2dqZl6vc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruLQ/+OSm5l4M5xSR4qvr9uLYF3wieB/L36XqiaawOkP1Zr75K8aF9\r\nN8vkRfvo431j9zJz+ScOWCM/HITT8imbeuEzcw7PoDbyk8bCqi2y5bo6ARRy\r\nE+lUp6Gn8wu/5qnLh1LWZTcTbGn5GIjD7i6g9rrwSPpz2iF96BWFSRNFyy/T\r\nhpwteSyzsGCn2s+vKj9paHvCkKRXY1L+0VVKKI4WAPj+8x+FekmCnQZONhy6\r\nZmtvj2G1I5mlnsHMkwkTiAcKL6V7tSh5IVD0G+XHM8epWRWBrsuVzcuxoc8P\r\nmLNnx8xYjC2Rbffrvu7Y26Tr6rG1Ybo15CU9G5FktLacMrMP1IP12m6nG2mC\r\nNGwZeUxN0q2F6wVW3au2Jq/a+awBuUBStm1ddPhOO/GY5CIEgSUlHCm+rENZ\r\nUERU8AUoYIEKsgHjpYvzbeWp+j/2ZiMAiMxzgGEo1q+HTWQxZZKTW55c+bpV\r\nUIfU3aSQlFa9GQXFzQhDM1UnwRYtMPzuUYqI8GQicWNOjtcuczgJO56dA7xi\r\nCTlf2vN/hioCFinU/jOQfeIpwtxF9yN1eKf20+uLUrG80XBbLqsbztivW4Go\r\n+EANOdly8P+TFqgFaRy+nDwUAmA/sIwvUZSVnvfOx5pZF5aj+gu+Odmc2Ek9\r\nA0iXkj8YWRArpJSKGUmEAkQ1ILyzdB51cSM=\r\n=kP+R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.0": {"name": "@babel/helper-validator-option", "version": "7.21.0", "dist": {"shasum": "8224c7e13ace4bafdc4004da2cf064ef42673180", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.0.tgz", "fileCount": 9, "integrity": "sha512-rmL/B8/f0mKS2baE9ZpyTcTavvEuWhTTW8amjzXNvYG4AwBsqTLikfXsEofsJEfKHf+HQVQbFOHy6o+4cnC/fQ==", "signatures": [{"sig": "MEUCIHhAlDR4tptb6lv7tw9DusdYSz54z665g7KGpd8qTzPlAiEA8buds9rkO29QwZuyxsEmnnu/CvgEPcaY4UyixHyRGCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85IuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWDA//YZyjCDPMgBVUIv+U7ikhe0K/ugVVfQ8y4IC7Q1TTcpa3JPae\r\nzPZWXZ63aKjd8kO76oNoDcx7uqj7Hr82ZiEDcpOXFpj+eAPnCkuc9vU93Blc\r\nkBOrN2UBLFxOlNt/G4qjBSil6B/IuY5GNnFxUMtPoVRhbIoa02YoySjdjNzu\r\nRfCTA74+pHcdXXmmoZGdpxb3KxejqIV0FKvhW5SQzdkZM5OXTI+2upnhahVU\r\n0wajuLRyDinfp/9u7nuWXwxzzisjGkUpddKGbu6w6G+jYuZI+RHxh5rCZUEX\r\nuSMt/K7oJ8lmWq2GJHg+QtvCXepZpbxMyQeaAk/aeGPRxEXrxB0aq3t5ajjq\r\nRlwCjG5+HCOn8dICpZoDsGb+3lGlWnSm9rv42nGtK1EBEjCFgzYWaA2qrqEH\r\noGxv5rm4DAJ2uJsyG61Gz7By3qoLlKbqPx/1fUTa91vGf4iluGhw+OunSSz2\r\nXUDs0FKW8U2Y6oTp2ZSq48da8BeLYqYa76a3N2bBIM5ht/4tDULGBnyla8ZA\r\nHlHMqyhnUTpUYXvkBByMlCGpnaK85ykcNZYgzPMBdb7PqAir7JTE7x3Q31l9\r\nt/FluoaGvPx43DYhnz3vPQoA0KwSOGZA03ScBLedqPp6KuPaQXO83LctmyoD\r\n4FTy5Mr7aT5LhVDbqppCnie5HJJOaDKwAhY=\r\n=bNRV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm", "dist": {"shasum": "24a17e53b12d3cc66853e47343d06ba4ae4d1487", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.tgz", "fileCount": 10, "integrity": "sha512-9/eDaDbhGCfqg8QCW+FujS+5PthZu6eUf6PJJpJgt0e2FLhjKjy/lStXofozfPZsINhkaSRwBx8Ztq+LRqqHhQ==", "signatures": [{"sig": "MEUCIQDJ8DUSQrWgduNtBmp64lITtzHRaSkhi9LBeBtXMrJYAQIgfxQrvcUK5gkNE8Bz27V3mmICDfayzsZ/WhB8NGTUKxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC9+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIag//T2wSFtuFHcG8+JhE0iWtBHD+uq2WSxU2uLkg8IX6cyGz5PL4\r\nNT0YFuDJy7Es7UX2E4YldclUqfh+tiTcE4Ts8l29p1zd1WrZ8aNqansiZSdO\r\nFv4DMZBLS4jusWq54uwC10ZAzd7Olm+/VzrcGHeQ+94GnagJpfI8L4Yzj+Hf\r\n4S8tB2w7UWzLhrEann+mEUhQHcxCqleKgk2Tgey8Fx89jzZSxhNQ8X4V2rXm\r\nP1GGnWXhPwUsmCxl1uUEp00dWgtQLIthW+vr1Vo3PTUV18NDgQxzXNBT90JV\r\nxk2PRrGBJ0R1Z620ZaT0CChTDnm5fYTnnI/I2Z9hlWLSGrd38zBSjH9Od/u9\r\n5M5dKQcfLo3qez0LR7b9uN+oA10S2CwMP5/YL6YMqKZ0s71jRgEheaR+ep4c\r\nmg8ZyqEpLY6+JE9Gk0YlwOWQZBE7XNEadMjL0MupBAGZxmTeGQ56j7kNpWdc\r\nWy8g8Q7rB0sYaYB30H+EPoJ+pUa+LdNJn6HMQTuBHKQdRDx08zhY456OA6Vj\r\nWN2lhlVVkasHWKU7Gcay7WYlMTij9JeZbR38Bk0T2lzMJRj/cXyG0ao7nqMr\r\nIwik1VjKp+Ooc9XUGMJgCMn3DG6OeY4ZNrRYytWjugYy6XSf5KvphpwUbemj\r\n+wkSB6/4eTOrWbCRDTDOJTuQejqFSarZWqY=\r\n=2Bur\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.1": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.1", "dist": {"shasum": "db7ab06f2efe92d282aa6104a28740d8b04ebc46", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.1.tgz", "fileCount": 10, "integrity": "sha512-W7kE6yVpGxVVsws5ewPz6zntV6UVrz5wx6Czkt6SXD3+eLokDVk4bt6CotYY8ogCfZ7z+zCAFuPqZi8K3Ay5Iw==", "signatures": [{"sig": "MEQCIDxQ+iqm6mqBNtRrGN+d1y56HT0aNbeEzgeM1tc/Jp8mAiBW3yOIcLPWSGK2j4vmjzodtG99VmCfBVMFqs+iCkJ60Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6lg//RTEMI70V1fb4+fmsLHoEB77g7E62XV97uV4CI0wpfIMZ+Uko\r\nf5U7S0N6bMosYcGeO1P2Bl2eMf+xfDnUdtgBI3dMltYQvmZMbHNOHDfsTVC0\r\nmlHYSg9ioufquamRxYaVFMPEqDtQOfOS6WDK6ooxrQCqDamabE2T8Zx7lp2Q\r\ng0l0AM9t1CS6tlPIDcujXgxE7M4u8dtn8DqOl11DE41WcSbowyP4ASk8VROS\r\nMX1zUj6yAXenrhviARgoFoWrU/kRBQDKv8ZScbQbsYXEcLaPVgDVxuXOG2VT\r\neY5Cpnvcd8yiALnsTK8JoYqiRAy91KsYgD3Vyyu6SrqddjNQP3U8E6Oh3M5z\r\nmtYZ55JlghVy9DSa5hvsEfWJoG5ED/2gKjqfrwGMJKAB/h/Yw4uOdnB4EJRE\r\niFoUO9nwk9zDUy3B1qBL5rbIoju7pqxtsvBVNFmDpmNDlwpAbJd2OxzaNtxi\r\nSOutBC8ePiaM37sZsotK2rlhg5yXNJqspI2LfSJUNnU4cCu8W0ea3SSBvVgx\r\nY/Kbpo/30S3qLueD0+RQzxNZBrVOWyLq7C1jhKrjbjrhfKc/U0AAB89bQ/b2\r\nYFHSvQpnI0cDoGqLrN7XBdukHe7TwVRMoV9l3RrhKM8O5hdg7kENQRia5mKl\r\nMVi+bIL150QhZvyIbexLEPiiQuLXBn85vJ4=\r\n=xPpr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.2": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.2", "dist": {"shasum": "21d9131d1f7e60b74f5849fd5cebff9dc6d0cd4d", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.2.tgz", "fileCount": 9, "integrity": "sha512-X2MBKvoZOZe76KWqKEjUQmDMhboHWSBLyfdcgg+0TN5x6BnGwYl8m+XZd6iAwOgNfQ/YMuzJmvwJJvQTL9YIfg==", "signatures": [{"sig": "MEUCIDDXS0Ku9VC17yjeQv3uoGPHi0Tpwl7GKco3EPYgt1NUAiEAlIefaJbQrgymdigve8WnM1ilHtTZI47WDUplUWvmuW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDZrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIDw//UQLATWNf5/dEDf3+hhnWy0fQW7ZLFluFcUe7pTDdcYsfwC1J\r\nWWrSI80RO6RInVG4rwXUDt8dD6P7g5+tumOM2tUVvHXMm4ba+TU1o/1+7vJe\r\nQdyUD98A1iCyGs9FEZdbriUgzrRiVBcYL8qigGbD/oBgpxo3Ce9a6cMVbqLx\r\nW6JJc6HbSUoz26AVlyZeVzXiWNpkeH9tatS2MABccbRX3gbIHOQX2sIQw/qN\r\npn5642h05gW3p46SHbGqqPbnxMIBeLBhZfX7yLqX8tF7Bua4nkWMu4T0fZh6\r\nRXr+jZjknacoB8gDOGN4SKml0MqbAQnZulLMMZ84Zg4/Lq/nwMAFP9lW89OL\r\nDfRYoXZkFIJUm8WIexgTeXlbhN37Pbr9O0QDGbIs+uxRG03fkHfv3yw0Wxo2\r\nWEptKoujsGWlJG3QVxh3swd4eYS1h8yj6zUr5iVavD2CcmD+QKjwEqpAsgBi\r\n0kfkCXwWm9L3lCUwq9nS+rWxrKpvDRxmwFezbCN0eIyJhsMJU3dha3OCkf4L\r\nL4ntd/CP/LuIs4K00r4g8GEsrkNyeCYTdf7IsnNYNx+APm2Nv9s/9gr/M/Fn\r\nRFh45ArdYMuTEd0DG4vh7ee/+w2rxv5YZqvrT+y2kO450/iBH84fUqg9XoFd\r\n4oXxH0ePlazPMoF0a9ho0N2Hbq65PHNJkRA=\r\n=d+Sr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.3": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.3", "dist": {"shasum": "12e4605d38187494cc2256dee192e8a540c80812", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.3.tgz", "fileCount": 9, "integrity": "sha512-pRVDfVa/bo5KNkvnnIgVBO5TLCUaxlNR43wQJ6YStbMrw7mDyF3Hu572cQAVH9k4OAMo3TyGjNjbGtxqHQlMwg==", "signatures": [{"sig": "MEQCIDgurt/b0K6O2czr03ZFNv4fVYzuPr86otyXWymUzKSfAiA458c6PA3vuU62+3pDpy8/P7VnxgM36AgGbIwFEvf0Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDp0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqurg/8CpwtLb9YV9M4680YGWHUR2C4nVKfjkeRdPNqptcBF2fAxR6a\r\nP/BFkCft8QHdpOVVPDFPNrq4j+4Y9C+g9WQFVFQJVo0sGGqeEP+vTZDDs4Gh\r\nud/dmW9ViG639+lxc2V6EdY8YYrayi8t44jExIE9jyX+nc1JPEXP1tABWdCW\r\npQghM5MgmRsRqXpk80TwOvjo8KhS4yqdDyHn8mbjTGTOm4iLO/N9KKEd3ZTQ\r\n2+26wfunwxH+O8kt8LX1KFkTQ0wc+snaWLRDuNSyP4pp5j4xwGVn03mAwkLP\r\nyrFOLwGLZdDLeSZRixzonufqH06g4U6LTYR9wVdaGDSzMcN4BYXURgrYw/S5\r\n15u/3Gksy/5p5z15Za69LA8tFgQouxU7daqytG/1MZiikHsWuDBaUWtUjirr\r\nZHmukGXNtVRcaiC9eYLZfGaCIN4NjnWXgPTaAhkX6maVGUWWJijVp0zsY10R\r\nUKCuAa/0rXQKrtUo+i+6/ikvpG498H0ctQH/YNDh98YnGUETKBzr/XwS1u4u\r\nRCC3juBnKThFgRe0bQOWiWjIRa4g6LWh5aqAV9ifjQ61QikLVCUIWX71HAMp\r\nuO2alVY+/F3djuam7NYTVMI4cbPOL12ttBzYeGBZuMbubgfj4bYLz8AJF+dg\r\nruXzTpvOMs2Hn+4OhSjPXY20qyQxzI1w9Hc=\r\n=WFiY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.4": {"name": "@babel/helper-validator-option", "version": "7.21.4-esm.4", "dist": {"shasum": "5094e3818aa4429e0da6428abf0fed02dc9b125f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.21.4-esm.4.tgz", "fileCount": 10, "integrity": "sha512-dsPtf5S85fq0zJRIWXupOE1bqDKMq/5H3GbcI1kE55nrSW6U+pkeXmT8rFv3LHmjMjhe1RD38UaW2sqDsdHGZg==", "signatures": [{"sig": "MEUCIQCnmjkxtVxr9hpCng9Q2TVfe3OVuhg3oEzn80Z5lE3m4wIgdx4fQVHThsSGG1cMhF4bHd6uvKDRXraHTkMZFuJAfU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD5+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohQhAAh7sz0AOeYcr58o9FB9aSXaxCopKWWhd4bcK8LtzRWD7SoBEB\r\nTGztqeRL74JXal+EadvBNKgBdAQzs700cm1wwBA7+g5ezy+16XSEw5QR9Er+\r\n8eIq2fiSYbhHNI7OK0HI8/UPAskgt6S+DEO53ND/ADEMyW/oL6Wz+R+wyqXE\r\nCNI78BzHHnUCOLbx2KdEBszGFN0XZ7DpRtigAYblyT0+Gy/gq49H08CkaVIz\r\n+Gmzs6x5ma56wnCELn2QcDvpuJEcJGu7c9xt/qEAC4YuuNzJmYxciba5SReX\r\nd5L/wD+n81vclN5IK87bZVl/fJa5EsSSQlBdB0cPWfcHNlLjQPYNaZ93kqDi\r\n+nsOE6IqJelIF1r7R21WddSHHMHJChknAXChbC+aenVsGCgS7MbqzIHhEwRK\r\nz6nRSmyhGAdXXxhiPxxkkWmXK6Ib7/AoSszdTcs7poNpTXrogIMja/LyyZIT\r\no3djQkP1HFD/BmG2U021w9RE/a1Nzz1XKZTIVKvsXFf2sCXUpnFcMuxOq4Et\r\n9NMH3RJ/Hbh8ZLAH3XH3TmS1RsS4pNd42RJ54f0hwEZqi7epUvA/jv3BnQej\r\nJQRmhySCbdHAy12RBY8iii9OKc23g/LaGsZH/r/rwZH7YhSuJwxcT3dI7koC\r\nYIw0IsZNZIj18+vMyvYGngWHZGxIcAX3ctc=\r\n=tIf5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.22.5": {"name": "@babel/helper-validator-option", "version": "7.22.5", "dist": {"shasum": "de52000a15a177413c8234fa3a8af4ee8102d0ac", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "fileCount": 9, "integrity": "sha512-R3oB6xlIVKUnxNUxbmgq7pKjxpru24zlimpE8WK47fACIlM0II/Hm1RS8IaOI7NgCr6LNS+jl5l75m20npAziw==", "signatures": [{"sig": "MEUCICS8rj5BZLEUzlpk+2IW7Nt7wPdxUjHidL+M231gP4tVAiEAtLXyIi/4iIPU7K6Tv/BH6v4xJjB+rWWjI7azUnxgIdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11621}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.0": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.0", "dist": {"shasum": "ea9e38129b52ad69ea6c3cb444206017467da1d3", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.0.tgz", "fileCount": 9, "integrity": "sha512-Mo<PERSON>tKypiB0SYbdndBIXlfwvJnxSX3VZQK3OJvS7G98KnEMM8jzFOzq0EroJ4hezocVGg3HKuoZm7nMaMq5xtkw==", "signatures": [{"sig": "MEUCIBOeLKDqGoNlygqHzr0mpcpjz/HjX7atZq0D9obt92BuAiEAjRPMmchtiPXTiYRSeSWEbpetWjEbovyw8sQ3b3k3gE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.1": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.1", "dist": {"shasum": "3b6387be63e248fee77bffdaae3e81188023d5a7", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.1.tgz", "fileCount": 9, "integrity": "sha512-E5<PERSON>kcIWQE5CnSCow8pNw21OOh0ow5kJPGAAFoEjSgtCZmSh2GPwlkMVbqbt3bDctrAQLZTiIjQrKua1bLlUnTw==", "signatures": [{"sig": "MEUCIQCMXGi7EA+4zOmaAqmFrLdz55oIpB3Hy6ju2pk7NsGJkQIgcoatQ/mr+db+fCDvfZMcFmitRBfE9lnr99DvGewFeBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.2": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.2", "dist": {"shasum": "2fc8ca5eb91b1729390ce71e776d5c533f33201f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-Hy4K/dCtwnRNssFDySSR8hQgcCVGdWJ38ECXu2lSbIo+omHKhqynD8tpOfvRxPe0A90FAeq/jAVrOzilUtw/9w==", "signatures": [{"sig": "MEUCIQC0NqIN5NHGl6SBaaaAj8RJ5bGLp2JKC0DGcobSk/j1lgIgEp3nZBelYNjHJZr0tdnYiCx60GOPW7x/uo/+9W/Wub8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19151}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.22.15": {"name": "@babel/helper-validator-option", "version": "7.22.15", "dist": {"shasum": "694c30dfa1d09a6534cdfcafbe56789d36aba040", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.22.15.tgz", "fileCount": 9, "integrity": "sha512-bMn7RmyFjY/mdECUbgn9eoSY4vqvacUnS9i9vGAGttgFWesO6B4CYWA7XlpbWgBt71iv/hfbPlynohStqnu5hA==", "signatures": [{"sig": "MEUCIDFiCATG0okeKwsF4qz6LarhDzRVHE3druSXn11Q694UAiEApD5y/wkmIXHURmILbjFoxt+NX1/rWSWNyRivq5fcwDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11637}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.3": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.3", "dist": {"shasum": "65dd6f5c80448548a1290ba8f412423faaaf5ca6", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-VVh9QCblSPzAmKJL7i38CCuFZJsUOjzAKNS5187cO7/4HSIOhuNqIzWfuDlZVJwe07M/X8VG984q9RHah6aWAQ==", "signatures": [{"sig": "MEYCIQCjx19IlS7A3b+7HZ1pcuwKCbm8nNs3zp/RL6wVDfh2gwIhAJb2AkarJC9HYqr3GBeU/w3R0nWFCzQc88n6HQC51TXu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.4": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.4", "dist": {"shasum": "6c7c81999cab944b1cda8997112f66fb24504017", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-wYTWCCERu+CsHoF6hERujZx4Lvzsn9SlXaY0vs37Um78RxKkAWVuDbD7ZVGn8fS8UcTByILX09jm/HnoPUkdmA==", "signatures": [{"sig": "MEUCICUp162Pq62RD2O8vXycrNvGbKE/J0yeLOezvv6h6go8AiEAnC3eZ0aRBS/3k07lEWr1TovIhndUP+U9Nvvj/iqyDz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10715}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.23.5": {"name": "@babel/helper-validator-option", "version": "7.23.5", "dist": {"shasum": "907a3fbd4523426285365d1206c423c4c5520307", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz", "fileCount": 9, "integrity": "sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==", "signatures": [{"sig": "MEYCIQCnkSW7ZrABPzrG0Esux3VI7CVPxTF90qz4804TJkaTbAIhANN73QdJ3j8NXCNKX/H2gq8gjZMe6ybPE3u0SOb07Vkl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11667}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.5": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.5", "dist": {"shasum": "6455cc901fbcb1a4fc89a5765e33b842857a379f", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-KGmJG37KmlK5SHVQ4P1NHwEWM27PU2xYQ2mVY+4EJ2gkcdSADSbIC7c3cPsxyeUvTM6hXYwlk5xEv6peDpiddg==", "signatures": [{"sig": "MEYCIQCPpexcDR/kluV85zRG6wxOD6OwUWqIm1PJqCdIywJwvAIhAKQDB0cmZ96hVDrO3QVFFFrVe1L2I+qWzKk66whBWVLq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.6": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.6", "dist": {"shasum": "de6c868fb5fd7c5e914dd944a04581c6efa7d468", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-jHHD6msYmhaMHHLGLbUVyy9UPyAOSvjBTF3qXuBnTg1ZRyuh5z+srbpfagh1tv8igfq15h+PPexA0kGqOnzi8Q==", "signatures": [{"sig": "MEUCIG3Ff+N3nnqdJcu5ptHbEQKrWaxqcowp7a3w9fYMmOKbAiEAnJed980EZfw9m9TWgJkRyrLzIYIEDywLXNPGjk3QpOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.7": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.7", "dist": {"shasum": "2c06780521a18e7fb1be31fb570e5065cd0369b2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-yimhDdcX3wtG1ea3pNxYJrrS00jQJTfe5Cxeu9szJlcnGhtgqw1iJqz+gVUa/aWzaaUIXibXQnYJzEWHpEvQ2g==", "signatures": [{"sig": "MEUCIQDWqk6R1YuLIQovRUdJLTVL8kIHiPXw389YsmpBnakrngIgBlVf4uIeBYdNdZ4lUWZssaHanzLHd+A8f5MkqHkDMB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.8": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.8", "dist": {"shasum": "8fea1c6b102be95b0f0979d838f8aa592ffd38f2", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-AFwOpQwBzkiXk9vCoI9aAQu4CvydxgbJ2odZokWGBpFjbpBuLLS/iZOWKTJEmKg/Pz4UFVVEXjNiNXUJQ1fX8Q==", "signatures": [{"sig": "MEUCIQDVNcA1xxPSwcQhJy2XnLxcXH56l/Rb59L0sHltto8obwIgBNw+kY4I0U2S/ypUDnBSLvXXG53/trW4UYgY5V5Lpfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10746}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.6": {"name": "@babel/helper-validator-option", "version": "7.24.6", "dist": {"shasum": "59d8e81c40b7d9109ab7e74457393442177f460a", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.6.tgz", "fileCount": 9, "integrity": "sha512-Jktc8KkF3zIkePb48QO+IapbXlSapOW9S+ogZZkcO6bABgYAxtZcjZ/O005111YLf+j4M84uEgwYoidDkXbCkQ==", "signatures": [{"sig": "MEQCICnMsqfPDheSWRiNAUoDiwuuWkIVyLdLlR8rYg1hA/yKAiA6myVOw09DX4t5lh/YiDOSoAIeMnHlvpSKgJ1QqNNMoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.9": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.9", "dist": {"shasum": "01bc58ddba42473660f6ee3f5262cc5be6b81920", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-sIhiBJ3c1m+EUHMCpFda8MlD0KqiBcqXQkwCrkdjvQTLulVIEueCLzSk6n2xs4IHqwASPEWKu/5irsbZqsy9UQ==", "signatures": [{"sig": "MEQCIGsBkCQDo1prmlmaW/JffzrzUnIKs7zzaaBtfCryb3trAiBAdoYXAH8eTrJunEPuwmwAXgteKhlilX4XpnIt+XruzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12304}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.10": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.10", "dist": {"shasum": "deef0d4736d6985d6eb5c224e330fc4581e82f32", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-FpZ/2O7zovTj92sP9/xBAH702lodvrnZS9JHBVJrYL/EPRl+arrTQNYfC8BthNSBZtZIf+3bvyubIJdWWNIrZw==", "signatures": [{"sig": "MEUCIQDNKpbtwx40UNpFMCmNtC8mtGHjw7VYVlXWv6FL0a9EIwIgJUgVwa87qIamUa2RTHNfzhmU0wD64I5iBKkwLTklkK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.7": {"name": "@babel/helper-validator-option", "version": "7.24.7", "dist": {"shasum": "24c3bb77c7a425d1742eec8fb433b5a1b38e62f6", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.7.tgz", "fileCount": 9, "integrity": "sha512-yy1/KvjhV/ZCL+SM7hBrvnZJ3ZuT9OuZgIJAGpPEToANvc3iM6iDvBnRjtElWibHU6n8/LPR/EjX9EtIEYO3pw==", "signatures": [{"sig": "MEUCIAFKu8+JxxV2HHyJ75m4QAyzEPBclaredGvvsp+JYuGIAiEA5c13Fy2/TQQof6pfSuCzRBBDt4XMF4ot34GyNj2Rr3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.11": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.11", "dist": {"shasum": "6359db8c8e6d1624a3142cd8cdeb89a5efbd1cac", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-Wmd/J81AWA5ZP93jU4JL1hYK16kg1f9atdWKYycEWh+aZ+1p9HLm17sjiaTwQ+wp+bG9+ftXEuuQWa0XmQQcxA==", "signatures": [{"sig": "MEUCIQCzf8Mv1GMnmOHuL7e7d3oO1lFarA4ZyiUgvHmP2a56lgIge3MtoT+5xleHxzVoVtV5Ee3K0te05UdcsObOrKlQsqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.8": {"name": "@babel/helper-validator-option", "version": "7.24.8", "dist": {"shasum": "3725cdeea8b480e86d34df15304806a06975e33d", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.24.8.tgz", "fileCount": 9, "integrity": "sha512-xb8t9tD1MHLungh/AIoWYN+gVHaB9kwlu8gffXGSt3FFEIT7RjS+xWbc2vUD1UTZdIpKj/ab3rdqJ7ufngyi2Q==", "signatures": [{"sig": "MEYCIQCPOFo/yfVZ5g6EEsfuKSlycbv3zZiOpfSDgAa0fbUa5AIhAN3Wm6IrbXblEyE0ccisdOcnhSMCOWUITMH4T+VsYBkV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.12": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.12", "dist": {"shasum": "f06cf04bf810bdd49930a9c08406164578ca5632", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-4Tn2waWYgkuyM5t2QP6Qz9tL54HCWGrtwW9cwpuMtk5VG0yu6vj9PVDyLT1FsQqEtc5jjI+fC84as0E36JqhOg==", "signatures": [{"sig": "MEUCIQDnFC5Z6/jzllzGHp8huIyzDIwckyPKyJ6De/8YE/YCaAIga/04er/SpGwlwE5tOmBYCcXDS6NkmKUKPxGba9BzEQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.25.7": {"name": "@babel/helper-validator-option", "version": "7.25.7", "dist": {"shasum": "97d1d684448228b30b506d90cace495d6f492729", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.7.tgz", "fileCount": 11, "integrity": "sha512-ytbPLsm+GjArDYXJ8Ydr1c/KJuutjF2besPNbIZnZ6MKUxi/uTA22t2ymmA4WFjZFpjiAMO0xuuJPqK2nvDVfQ==", "signatures": [{"sig": "MEQCICUFO/oKvTMd6gJYdKSoF9mcJAgTDFi28fTfKCTeqYJ+AiBlEuRMksV1DcJChPyrwG/dy1qrMe8JvuQgMcrqyHw60Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54681}, "engines": {"node": ">=6.9.0"}}, "7.25.9": {"name": "@babel/helper-validator-option", "version": "7.25.9", "dist": {"shasum": "86e45bd8a49ab7e03f276577f96179653d41da72", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.25.9.tgz", "fileCount": 9, "integrity": "sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==", "signatures": [{"sig": "MEQCIF9kMznYhGzNuxgNYXWhks9OLc/9b4F3eHMoVhiKv822AiBKjX7E3KBUF9fFfdyJnHcFXJu++MUYnJ8/qPU2a5dHMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11775}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.13": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.13", "dist": {"shasum": "73661256805d19b3808206eb76dbefedcdac2ce4", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-qjrC/A2ee/XFnq+h+3mTyKuTzgGY3yPNPZoZWJ+zu0r9qz7UgJgDmJsRkz/voo8wRp/CoSRulX5fkP6puzD2ew==", "signatures": [{"sig": "MEUCICt4NFwZwyPmIPX6okejFWpoEQcY9EzAISTFSgGkvvCGAiEA/EXwVzAxPhszB/e7BiPC4oRA2s2uhyPa8Nke6nIF0uk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.14": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.14", "dist": {"shasum": "239725f75b598e6abd223ed982a736e168865371", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-15I9Xplev7++FYC5wC1Hp7MlwzhhAusdclqPqQHvGZ1/KETbL0TrKI989NoANzbvfmTsZi20z3vl2F4vYCWcuQ==", "signatures": [{"sig": "MEUCIQCq6ss8eoF84PJvKh/sQKxv9PuYVtuwhOlJmYAmNhguZwIgUReWs3USNPzlqG7pL1lvh4VpA0kg8TgtuhxQ9OxwHBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.15": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.15", "dist": {"shasum": "14cce959f2b58ac87c6a1cfe8c7290ca51888641", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-S+G+1QY7zQb0Alr4socYr6nR0uKZsZRKphAuiq1eVS/dVRv4Mc35Femsa1RfMZfb4I03dEzPA0tOaF3Oxpc5vQ==", "signatures": [{"sig": "MEYCIQDvCSQ838Qz1lWyeQoDa0EHCU6B0HjaCdGCoHauvS2U+QIhANwKoJPnj7DNEZ8ZEVXZofFFMNx3KPD0PbmW2LbCQNlK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.16": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.16", "dist": {"shasum": "435aa662884ebe76bceeb9160d5c46d8a418b1ad", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-iicLQ529SexR0UnW8ZL8RK80aD30WGnc/DRg2IrXj4m0qJx0Kyi2Ifx+Mly0xtBUzsWX8z1YSQnZ/sKmVIZo8Q==", "signatures": [{"sig": "MEUCIGXANiJ+ngHw0ANSgKfsG8SFj+ii4VFLC/Mv67L6ie8cAiEAwV48JXlS3mfnVO7em4Nx11LNKtQVGSincisJ50RNNNU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.17": {"name": "@babel/helper-validator-option", "version": "8.0.0-alpha.17", "dist": {"shasum": "6404bbb1482280d78243c3f71fd2b8790fe01343", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-VqaLxWOsdD/XA3MwZPPmqCDkMRJo8ckA8svQIIJ9At49gGXQXbQ1O6vAYhQT/32c+nYie9KuZ4I/+gH2cyTMew==", "signatures": [{"sig": "MEUCIQCXqUjUNHybS75EnMKsqzmykWXG3fdt74oxaDVp8Q5i5wIgHjVu1albGigbnK3Dg/C7CcQU/TsonMBgCZ7RKoZBdcE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12305}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.27.1": {"name": "@babel/helper-validator-option", "version": "7.27.1", "dist": {"shasum": "fa52f5b1e7db1ab049445b421c4471303897702f", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "tarball": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "fileCount": 9, "unpackedSize": 11775, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDOWF2w0fibyKxW6i4fhkrN25B7gyO6GSTK8+OBRNskTwIgJYvokDKH+OQj2TosdArObqpEfVHUF57HwFTxE6l0EKs="}]}, "engines": {"node": ">=6.9.0"}}}, "modified": "2025-04-30T15:08:28.099Z", "cachedAt": 1747660591282}