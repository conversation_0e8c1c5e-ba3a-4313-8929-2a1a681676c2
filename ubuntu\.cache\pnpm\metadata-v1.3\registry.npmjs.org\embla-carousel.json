{"name": "embla-carousel", "dist-tags": {"latest": "8.6.0"}, "versions": {"0.0.5": {"name": "embla-carousel", "version": "0.0.5", "devDependencies": {"jest": "^24.0.0", "tslint": "^5.12.1", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.0", "webpack-cli": "^3.2.3", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.3.1", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "070f4d62b3017b75cb76dde3a36ecf8b42bb6610", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.5.tgz", "fileCount": 25, "integrity": "sha512-HU3ER0KEMI73raZww4wS1gAd3pT7icxJ5KQAIvnuZtmFxr3TyGTbNDg+YG5QHQnZ28Ut2TldkGXFyqUG98Ccbw==", "signatures": [{"sig": "MEUCIBlVinV65ymBem0IcDyNiJzNPyMbyKlJjAVpy6vy6KQLAiEApqcuUkx5w9NzhbeTTAN0Y6D/UmX5772DhW20JTEZBc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckpKACRA9TVsSAnZWagAAiREP/RBfJhxCu+hJsN5ZHU/8\nxUC9cySV3Un5fwZNP6L6og6Ey2kag56MBAr3ik5hAs8O0YiDyHRei5zLbOAd\nD/xMboopsXujJOXjKQjW6Tan12Esg62jeBVWOiNQm/Kv5GHGLRQRhV+eov2i\n2gm8/eqhh2p96u4Ki4auPUq1x9ebJYYtdyAB0WE2VWsdNY7hBNFVlHZ+ZvBL\nosaQZXbejFar3V1WqVHSljtLTgD0umYv9IcfhOquiu7o9gDhe7f9lDok4POS\n/cwoVyIIOTh4LqzLglbxzXlweWSnPjsR79G0IoY3uFK0AdffOh2D7o+4LKP9\npxxd0Oef/cRYBE7TMnS4NoPOK3DgZhXLAcB87MAdsamD6RcMDT9dBaYcOnkK\nwZIIge452MTKvC3zBgZf1N8oeMvcCJbzyS79i7GC6OVRhmCm9IqDza30mIlf\n2w3dhCyvZk266u1+Sj60uXh1GPBCXjhCj3ibgQrPae5NL5hZzP7ukDRQ5woo\nSEFasUtPUucacfCOapEeyY7OIHSJ4GpVuvqy205WTyI/SG63KHTGnQoPflIY\nKBydxlyqKlDyX5bzEetfVca4fvSVpHfmUnYXB2Mivs/9l50liqiX+AHg3oEO\n7AaYCaAC8pBhnYCsZUQADFoX8khBr5huqoaHi9XE2qkqNr2q82rU7MZQzpFB\np+nw\r\n=sMO0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "embla-carousel", "version": "0.0.6", "devDependencies": {"jest": "^24.0.0", "tslint": "^5.12.1", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.0", "webpack-cli": "^3.2.3", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.3.1", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "ff5084f8246280f81ae0c82fe9766a325619eb26", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.6.tgz", "fileCount": 25, "integrity": "sha512-yG4TTcbxKHdPuES7RDLG8sEJu0Q6i6pWAhFKtlbv4TIRZVCmX+Z4WIQ5rl2hJUFQjlQ0UNq8xxZBblIteZI9yw==", "signatures": [{"sig": "MEUCIQChmbOdQCu1mVFNs3CcPAP1n6DGJSw0Q7Jb2jqxuRP2wgIgLyoCcEUyBWDoC+q+O7GYXGwHDSSvIikuoWW0xECTPeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmpg4CRA9TVsSAnZWagAAVxoP/1ns4cCs9KJ3xabaAD6U\ne1Znia8sdniwDgCM8zgL1AdgrdPOrts5L4EuVTbKz2ZluwCD40bX3Q9bOOF/\ngiiERzwyAj/BX2Kc6z3+Y+DQur13oJJxJijH9heTfcH7t1qVnGBXCcWZY2ji\nbwls/mvvGp/DM2CJxFVNcMTnWLQZsy+3NUA9/hIdYXYP8h8lcvmZQ6LgShAl\nz0Et009RNr31aWZsre1QOG/5qPZFo0eIfHy3G+UPoSPn/0oJ/dw0evuBVMLv\nmCjPkYcixEoYMdp//hodCy/W8FU8PtlLMIySDI9braHWwPx8y5Vgff3CcuTA\nvMDFpnLGAtxbixHg8cFc83XRA0MsMSd6+YhXdlmjSXZv7+jpdEX9FDhYDf+e\nrO7R01Gh+LW5I4l32EIdRIzTKzZNYYYOmkkfS0TlmaGBOePqvRH9vkhfzE1p\n/eCgBG52+R6O+OU0isIXid8M8l8fNGnF8AAnZGewNKN+3U57/cMlvjYNTbp6\nP+Vx7G2nDqQulV8P86oVOtqc8ctCdd/Ho/6Kkl8kTI8M2JQVtQjtc4SfOdcB\nIZ1hHfdoqsHC39nUuNWC7/1uSPj2oUlkl+ogWvMTVWm8EFJmFmyuhVQ9YWrl\naJ3u8qkI4n0o1UzPJnoAJnbC7CWheP8+QPadK6amjkvxzjjytk+kKSuX1HT6\nhrCB\r\n=aGan\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "embla-carousel", "version": "0.0.7", "devDependencies": {"jest": "^24.0.0", "tslint": "^5.12.1", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.2.3", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.3.1", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "20976943449662e84dbd746e18849e476ade33c6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.7.tgz", "fileCount": 25, "integrity": "sha512-/FcbW/x0pgr70cXPMrHy6ixd0Igrt9Ml4v3K1UWdtF3x1ynjTRUXyoat8N29ah+9qhUQ7oCS4A3kperGk0B7uw==", "signatures": [{"sig": "MEYCIQCAx9SnahC+NPROUm/QrY6RIVDD1nwkJaoMz94la5HgKAIhAMIVON0gd2z6Rf/x1gc3vOloF+hl+1Fh0ovW3Q33PoFJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcnoAwCRA9TVsSAnZWagAAbGMQAJYZGfHS2bnHUULSQYE5\nl9zbGRTFJC5flaOFgpE8WGH3hYoRLkGindFuHLHisKIbq7jdnQDYmFodwvgr\nyxBUlP6DnejSu0xsACv+c0Ap3iNtzrmY3qEFUqTli3q64QirNSz5bLOrhJLD\n6Dbew91JmIOxrQC/AjfHXZZQH3w73uh3bXi4iZrELxQaZsjV1v2CdIg90GcM\nQQMZGgUINLtaPemcVRRe/bty9ueUMlZC+c6NkVH4u0ECLK/eMlJ2BdW1rQCw\nTsonEeOyM5GYn5YJ+vNhuc8BCAhTdBp+aPpXPkQ6eJT/RcgLmxjX9zBO+IBE\nIFyVDXiGplhDpzdHFEgNsVJKafjP+AJh7BSKyG19gX6nglnfvIl2y83Mpccx\n9dAFETsmdWojQcxDiwn2u8hvaadKLZrTNPKrVgjaimvb2sh8l0PKV8plhPTA\njfy5d2NaKfaxfC6siwvq7Fj8yWvtkTBAnEVhYzl0YBObRiZtAdnV3pd6wpiI\nbwIf6GJRW89JjGpZVEQPzFTf/d1Ict6Z77Cbtea4KTMTEt7NpfVPO7aMjSdS\ndZKJsnj0t9gefyBtlV3Inz/PA8ro29xQbKMVDTQGtMeD90IbJ2aLIZVJxXVL\n/o8tFljQR4o/vClVvPQfrBk9knvZJwH32ghYxuO6Rx9yYIK4ze3HhnUffJN4\n9PCq\r\n=ciRS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "embla-carousel", "version": "0.0.8", "devDependencies": {"jest": "^24.0.0", "tslint": "^5.12.1", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.2.3", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.3.1", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "be5baeaffc999292e3df9da8d776af20dcd912f0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.8.tgz", "fileCount": 25, "integrity": "sha512-LWh/0fzcyZf5MPGRMu6YYYpQ9DZmxz1XsabeEYuvvbc7imFympTOzJXZCCf1NocODRuGWqruphvObZMMD5URDw==", "signatures": [{"sig": "MEYCIQDWyk6k+XDSLESc7VFAzYf2h+xIdRpNcuHPJwwA/yd+8AIhAOus94zufBvVVjUldkSo3VdlhSw0EUn1IZCHhuQioFu6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcomhGCRA9TVsSAnZWagAAbroP/1F4fGT+V7Ks1JT8txsR\n/1nHrQl5k0Lkd49IV4JVcvib/tDe2uGi5Zxb6h0ZeLt0edfq1nKUC4zNaybD\n1j8czpYkZPlz2RC2mjddvOXRVpHztfWNjlGp5fkSG4CnTizU66cMsDFXkTYF\nZJCobBKVutjumq1E7LwrSmDLQw1uV8wxa4S8X8GM75yTlFAhXLImnEvVnfJH\ngUprMF4migWAP0N5WFVpcRo9iw63OAfZ+iz1bV4oF5MmSE9vwrJlCJwbjnGr\nxpKCNAvEjdijIFRSJ47+C2CFV5rY07zmftVXGYNZwl6fYOzj8usWGfnFgG7/\nA9T6jztytMIcdFcwrvRzhJs6tl9myE2OMTSM/FESWhkI3hVExbYmZPG1wM1K\nRJhEBkih3bbi+VIjvordI+mP3T2GQhRqCHIB1DffOAIphSKBIhXNeVmiuojQ\nH959srcbkU5Oih9BVCxrmIvAqnDlcoGUDRhX70I4TH/cuSXvFt8FkIuWaKGY\nFyQqPoRG7IW2L3OPE/+j6uAqpPFg5+38wqxZUIwX8qYFVTHztREEdn0VW4D3\n+89I5cbZlvfqLz4mgYeYomf1RfeVx3eS5XBdX++zuJsjzGJHesFEYTbpOCoM\nDQqwy5NMUh6d/P1LyxA4fjyc/CwM3m7CXS9empGisjZtitbOulm2hUM5a9iv\nGqmu\r\n=nyQT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "embla-carousel", "version": "0.0.9", "devDependencies": {"jest": "^24.0.0", "tslint": "^5.12.1", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.3.3", "@babel/core": "^7.2.2", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.2.3", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.3.1", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "8b72d4cac0d6db9a49bc223732df7d4a8eeaee1d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.9.tgz", "fileCount": 25, "integrity": "sha512-85eeMtDeP6wH2wtWKnhBAIwi3BfNBq1Z9de8m1aLfk0kwXzN18D7J0NcAdfjwltSWBQW3HIEshISPJi2MMMI/w==", "signatures": [{"sig": "MEUCIAzl2+q5xmRJbRlgPZ3wnQTDB5zEetTp38r0x/4UaHdeAiEA6KiY1QzhlGQlLVy9p0YRA6iKl7T0ykBBVWrVQ3Z4uyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco6+ICRA9TVsSAnZWagAAHOIP/3Dhqa+zRn53Gk/6UsHx\nCEO83td8scDbiNIVzCZ9fGuhhy3sszB5ar+rrHsMN93W5nFvfs9gYhIlXV1S\n8rdVvHqtYpONVU42kpio4YraVPqy3bRJ1wCCMnkiQIB0iUGH9ZnXlBs0HTYN\nWtlI99euSc+OCo49/VxLbiosXkOxPa2T1vLrW9LaJqmkBf9BufJwLHpmHyNC\nQCCWJqkOF2+34DddQCbciGzt7z0bNikHvQX25N0YCNiaqRfU8qOjElsULSXh\nLWaz3ywC5typlQjL9rnxLInMnuXlEg9WU+fxwOSQFCUQfk55bd7fKCu5VedK\nrHu9wxeXElGSP7BoHBxs90DeF1xsrwk1IdZ4gxHKOqH7/AYcIlV/VGhRQsuF\nVEPFE3ltXdpjhYZGwgX/UFa9nZnmi2o3ak0qJVUeqBU+hVo1xn5z2HZi1fJg\noBUNqWyT2VdAtXHAgmJRVohPH2/i9ckihIVnqRSUJC43YsSrqmX8uPEh0FY7\n61iMZR5QAQpDYNr8zkhlbtVuzZ/nSGDMy8BSvxpMGC0iK16o3JHapQeqYr35\nZDKHg8UZue8Bo7IwGhqcLQgPZ6nN4sGwLKgYU25Dwmi5cIE5WTTKA/ayzoBc\nsZd2ttuSTPTiD340f33ioE7mrAE1EdEBUy2yI4H9H/Vk+6tkjuU96VleBivP\nGSya\r\n=ndeO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "embla-carousel", "version": "0.0.10", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "71e6bdea1eaaa3cd9aead8cc21ba17a593903d64", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.0.10.tgz", "fileCount": 25, "integrity": "sha512-xFY19xy+FMQ9OljwFd4vlmjNSQGkCES45P4+9L9k34qcQ9+2PELAsoItHQqptD5+Df5tWc8uPLhD9d36ddj5tg==", "signatures": [{"sig": "MEQCIFQgmaHtlsnE0Jz45x5YBeeOKSiD+OYoeRUrtvu+mSzZAiBYzhR6zr+nVAxm8pfyNSGtUh2THWEVN9lfrNBLd0u4hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJco77tCRA9TVsSAnZWagAASLUP/iCuumh7ttsF6MzDlXA7\nJRAJaqXifO+GGE4kTejWdqz3wmXOvav96fAVvM7X+C+4AOd1uyIWS76WV0In\nItvH85mtxwodkeMhoodFBR8rkW8I9tomKQJHQGDDAxLXWEqsz0YJGa8hoZbg\n0pzsV0RMqfpyjHpvn2p4/3zBM4fgysoBEZnNKQk3HxwXyWVY7Haq6sxpwEEh\npJXGlEvYjusBHRmNreh68TCZanxfsDaFdbwFqzeDN0joNTGD568qkWQM7a+C\ngM5lFvBoHSsZmdtsOsMAEZGbzdcfCb1bYH8oEWxxf3Fkw5PvciRtzNMxa8S8\nogfMr0sgP3rn7XqMKRS5ioVW97LCa+Jv5WRwgBs5UG7UXXPY+ecQ9UxjY31/\n/PtSA/ryiy8csDg56dCy6AI7PSdkZrR03xpklSFZ9gbJP06weLmxxyq7ZZLi\nOwC2Cs0RFvmHrI4XARgDigB1MqeTpQGxXNr1zjXCpeEmCT5edbG/8Ece119f\nyntQ7eYvCyx7bwUmbuI+Tk1p9I9D+zsFFdnT7VU9LU13SpNwjKGMu5GtGAwf\nWI5Iqd63HV2p72ppuD1YdIGLSNwa9mYMfRkpa0NtTOF3lPtrzU1JIJ9gec60\nEx6NT5BkdtNdtH36Lit3HhBnzvqP05vyA9vh7CcgnEbhjknRxHlI6D89KvLR\n+pG6\r\n=Ae+E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "embla-carousel", "version": "0.1.0", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "9b6d0442a7b83494873097145e1c2bc34bad6b43", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.1.0.tgz", "fileCount": 26, "integrity": "sha512-+n7StSu8rXHpXMvN+N6yzhOd6H6xbi458rDn/MTlrtlUDijIzyjWjKXN9QLCtb7+/Abu6sOwPjZuyEj/EhbSWw==", "signatures": [{"sig": "MEUCIQCgZ5xDVQwkikh9KQcQksMCG4ful4heBudAi19kgrRx5QIgcrvPMqizbAiTz6mZCun1FqbkJ8FX9XMeuySPpEKDa0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp5csCRA9TVsSAnZWagAAUtgQAIT8Xo3uRx/44t/UgBzl\nqdk2Wcr77QAYvlRGm+UfRLAsmEZhXsuTGp+cI3TmSWiacoYg51OBIYSeSaZR\n6AEMJ/eKJ4hmXLCqQN449qAUiAFr9laCsNhmfkdVyNw/nACrk2N5/ZWdXJaq\n4hew/vGGyH0921UmjcRxZc2XSg+RvkpKyr0CYiCaqNmoHr5PNGo/qq4xL3Wk\nQ9cvtbG2nfU7wS/XAAPq9qeLbgUyiwohGABfxfEko+O4gHwP26w7R1br46mI\nKF+x8JoUUwiI6smIZSTZaKUNvKBaXk7il7p8gexW8eJaNBK1M5ltaa8Gyffh\nOJGOticQEpIfavdQSEhoiWApOYd1ApSwY6YlTtQx7rY3HuRTgmTQ5gb8RNCv\nVXm8rQvFpxvWK/Wtsnewg1DDzaP1AwwE5GskcTKqG3bwPqT8JEgVK0j9e8DL\nQPsNmurMbrTT4J0/kCk3oWP9PBZj8DCX13fRUp0kz/ku1dO3MZNDhk19KD3d\nSwALWlHQz99eLitplrAHnDd4/IL4AYNUi/64OUxuqPS48LcJ5LMbpW7ObU4J\n98zRaQ9Dae96YitLoEHGrKXKIa36p3RsyGPAX/Cl1nL/e944JDHZ7yB/Mmea\nRVuzJMEhR6rhpQClFjMDHRiokQac7K9GAGJI5GxCmNIaaKprDP7QUNrW654a\nLrhT\r\n=usiO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "embla-carousel", "version": "0.1.1", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "dfaf2383ea95a2a792fd8ec6ad0d3e634897d551", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.1.1.tgz", "fileCount": 26, "integrity": "sha512-oaYd66X5hsjslHaY2qoFVc7nkL1fL4FFK0uRf6rR8Ilueiya+FZWg9skmPmMObfeDF9rzmq0Pdq0StIEX0e+jw==", "signatures": [{"sig": "MEYCIQC1nMhonsk8BwHRR//pPpefRzAbw7RGnfTRBnoJdvr5XgIhAMNL5v4s8eDEehNikIf7YTRqBiTi4bXy2dchH++VEam8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp6r4CRA9TVsSAnZWagAA+hUP/3KQ/MadZgChVvclBKzQ\n7ODCGDEpf8KslOQEXIp4w0DNbzU/BYUN3ttMRur7t97QspGmdUKdGwVJuGw+\nd5JLfE1iCPyI95S9CXGE+VzaZNne49W8OrQkHRnoVpkFEkkNXFuPUYAESFK0\n2SpgGDneZ2r0WKCo0vfpDDi8YSzbwzBIgGK+qnE5x9eJikL+VVB+K+mErsWM\nG4KbjfBV+X7ISBROqUEbZW3oRTFjWpRqCy2MZz79NH4uSBgzG+MToVLs8HCw\nPZ74HQCKiZszEcv0Geg7cFpAQVG3jZVgZ6WxfFOyaX/Am+rgIW0WqnMQhsgh\nAutT0nZJ5b0Fw1Q5PxBPjNAMibnsTKejmbN4BAfh2Rb+K5GaQRvn8t9DXbrP\n5YYbs7H7M2IUAGuT+a3CS9O5W409bMN3HbrahjTQ4tLUpOaTe+gLJI3AuHkD\n8TuQUzaNwBozazaUt8go3ghm6l/XgSS5xQuzgaeCxh6oysM9ns6NoknKl0ud\ny9wU2PKLh0ghU5PTbxAP2TU7EbyRRdLuJ+BmgBvCJ8C/MECLDnDp3wiBNxbO\nC7V1emweCCYxWmFxlw4+rXLwiOxSGXoDXDENf526XzLka1mzb+ctvP8MvNEN\nDxXiTmnQQ3QItDhx5soemIBP5XtV3rN7+ESRGocaZ9vU7yCDHTW2wMrm2vMN\niuqd\r\n=y69q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "embla-carousel", "version": "0.1.2", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "39ba1475594c0ae6d31b6f5a1a9937acc258fe15", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.1.2.tgz", "fileCount": 26, "integrity": "sha512-TYNB36Wk90mIt0q5eX3BoOIsZM1JCUKCwTN1wErBWRIVJTQiHWC8QTg+RsPFQ9S8IHsTFZ2ft9+IyXqJ49hOhg==", "signatures": [{"sig": "MEQCID+JpX/r168fNdyikz4r6eojQwB6s9hneHnOwqPkOQ74AiApLWVg9EIlU8nBh10wngsK38wGNeDrJnAeSnXy0/OcxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcp9ZNCRA9TVsSAnZWagAADzoQAJarx8R9CLXgBhdw6u5z\n65mrP+QsOvneQhawCxlSE7b3Y+gIjSg49svwGE9XnTCqwIZ5qUlFI27bj50K\nzv5iBT/7RvyyXMNS3WWonk2iMiFU1eG9SGYeLhqUPBWpKgqkjxpuQRgVWBZ8\nqjCthTW9j90u25NJ1CpPoppDI8RM/SlXEJtZ5y16GlH5FAGfdh7Vok8ND8fG\nYMivUPwXZuh5hMyJ4SUi8yxFPHIz5R+nu1IRjs+D3lDg/O425nRqQM21NDmN\noqYpqIGpR0vN2z8zvtvJoesnsdwVFxUF4jLiAAa6PEs+i68nbboQIgGxJk+6\ne26YZ6nrVf0JhIVXpEp8HKavRidBZR2LkL46DQYJbygfsxXRXZR7Wd99/dOA\nc3b9YprrdESd2ZXE6IIATEYIcf+vAC6jK3v9S72sBE4JICodYNCJe8pLT5OY\nxF25dCkR3+9OA2YrVJGICidyAzki1sQrtLhwUX2XIYaCbBBz+hsmEP2XiHIJ\nXp+uuEYpSL6RLz2bt3mDOJXxMQHaUNKL4SkRI0VHMXf3MLNC/nkzPrxrIn3I\nmKS0oGl1F4RzGy6+twGWT08WOkcM3cGg2ITh4/xL116XN/9++qTqilAIhEUx\nKSuhzpt9sngMK7+OpMTt7FN+22BHVbwTnMiQFiNDEzBKCR7gvnDxqJ41MNca\nHXoy\r\n=4FDu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "embla-carousel", "version": "0.1.3", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a6ff1b1ebd8690b507ec52dcda14e092dbe25644", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.1.3.tgz", "fileCount": 26, "integrity": "sha512-NjspyGQC0lCo7RblyEa2mCDshi+X3dgnMlvuE1CD1Q7jgwG0RBZpmmRJpq6oMk30PRK3LkX4RcDQKFfZzxapgg==", "signatures": [{"sig": "MEUCIQCkh2INjhhxMZC2Bgde3cB3ivIs6sYUNoX6tK8AI2lvXwIgU7kW4Z90Mq3TqN0bHXYpBDz0CQCvD/ng45zeb1e0kp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqPioCRA9TVsSAnZWagAAMjsP/1/C+9ICDx6OzWJZZF2f\nNdqxCllmfWIlJiymGy9bOtnAvXPNsmuY2iNLrMDPwwuq2tUqepCKOXKElNEt\nz1t63/NSbiE5pat9DP9pNPOWDmIDT58njCbaGwpO4mS1iWSXc2dK22S0vwOf\nSMBpQUDieAcrYHsOD24e3dZDGH+NhcmvOmEBQEDqAb8W2gRYQrWti8KYf6T+\nodnULJiIDjcozxTlYxU8I0j5W/9DS1vg/uGH3HlM3thAx4iWamq3U+7NuRUs\ngm3K3P7meNQxnJ7TmX5SlVF0n3NRUStIFYc2oNROzSyCWf5I6ExrA3zNv9NH\nuWwWS1BnpqJ6tKJ5j/oWvtaik+usW3zlaYdXSgaqrBBM6pQC+APXvjkBb4Yp\nsVpaoFwRtwqxIQUV1aJ6BfoocUEjz50knVHNKrZ2SV23PrC6XG6vVk19muUG\nAUrx51y8z0mrK5Eg6w9OV2Bi4U9D0N680BiIj3E7sPaaXBwFjSUaXW/mkBS6\ndGg6hiDGpRl4ykJ5d+oNoMiRqsuU0C1wQZfQBt27/bAlQrd2IpipDajSZXKV\n0dM1Iu8caDRm2p5rikisKMCpNHDr+v50+0b2QtmZMk02XZyKZXW/d7Ue2tjM\no+u9MUdUO6EqYcM8Un8XH06HpIf1/7f0gWdYrdgsW/0cHYdxMDHJgAHWGPiR\nmWy2\r\n=sbQT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "embla-carousel", "version": "0.1.4", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "fac2065c886ab9ea639837415526befc47a190f4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.1.4.tgz", "fileCount": 26, "integrity": "sha512-XKodyGnTfcBAz9fYmDGnVwdFrEA0AT8LjKwU3zkAcF85gqj/THC18QreSqLlvEOyQNkuwyERvXQLMcbtJWD+DQ==", "signatures": [{"sig": "MEYCIQDoMJH7/DgVyv2e0AT8T8+RxQCAISSXLn2P/A8Qi7d4TQIhAL4Ng02fF2VTzY10k5Y0XR8oGSSN1Fw6k7eBSz7pndCQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqRD0CRA9TVsSAnZWagAAn6cP/RG7JApJh4WVqa+G0Hee\nX8kBBjNG4WJ0nziheYxM/Wq8s5hdPpVpZGL5jr7EfDwvIee2M/3rIdks/8Uj\nf4mhh2YjSQQIP8Bt5HbwAbee0JKlGFftwt8hAPb5LXCgbWLuv/8TtPWXCmCp\nlmK0116cyONjG8gHHzAJcQZE8S7240FSAtImgt+auJaKlodhSd/snQCY0LVw\n8P0afOA63tqh+VQb//Tj/b6BW16hIiVdC96g+LPRGjI2cQxwaB/s3dC/Zbsp\n4j9IVRrCi08xYrm5KpIeuwnEIrMt/21yNPcJGe9SOiqrYFHHe4ZFck2d8GK5\n59NZxfuQ3/mcRVskqqyfpQpIRnQs5Yrr49wEtFFuD1WM6epjmSOFQpWDuxs0\nuu4LW+nTZFkNIKV05Eo2+QihBXSVnMCgcTb4+8ARCmKO/7dRC+fYPRF2OBvQ\nUNZflZw1tn122fQLtsLroCGK6HB8823j2kQFLAtwjJr6aSOig5OG/qsYWM1y\nLCNse/A/GSmKTfgaWn/2lyz2kQtZjlnxAQcdKGvaSAaNfSCElTRtICzxw0qO\nauO7NAJy8j3eI7Nccf0Pjn1u1GAocHv3i89ZDRhK167ZNHHxEHe90c0QVBuh\n9YAw1gDdumjM7XEQBWQoBA04Ux3wMa+vHm5YDtTekbQreuZDc9YnNgHDd74X\n69i8\r\n=7Nm0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "embla-carousel", "version": "0.2.0", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "21f1878cf07029a194a34db3b4a720e947be2ec2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.2.0.tgz", "fileCount": 26, "integrity": "sha512-+q6fuoigYAY1foQLjfdWrwZ53fYcfU5/SqrTY13joq63EqBDqoWd7RaMGXP4lDGNMHZj3Fu0GWep68UWJ3xWqQ==", "signatures": [{"sig": "MEYCIQC0wrtn+XTgRGjjjx7wgzR3ylGsx84Z8isHfiiJWZSu1gIhALwBwl3H2jIXU9cfaCaVtMD6olG47k+z1kii5UHj6+fd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrOZsCRA9TVsSAnZWagAAGrYP+gL7Ecmg5Rdr9Nqhg+Ms\ngddYhCm5wdOFRM2cyNa9lTyNr8lU4dLy4L36o2tzEWTQ1r+PZXeA6ZNB5tlg\n4jY4K+TLNzfa7bFtYg0kx9ahRiVbVVUH0kmsFeFaIq6BHVyEySQjRvkQ91UD\nHRlZb+HyyDUmN0IT8xfpVIv4JEaiAxzpDnh4UjA9Z/snYT9GZk0D9jG9STDj\n0YRKENkbMg7auEApsuAvPoCxn1gmmHAfssBufEGCVOgfP2RZkWoGI8x9BP1e\nb5ud8nRPvLTJriM+QIKei86DBHq/eOI9OFGc7XTscXkleLkYd0tnSreJSexY\n29/y6jZ1WFOGvkR3KGo8jQXH5rTE9/J1LBycFV6yDVl/UXxF3WuJYeOZqced\nIvK/EbT+QQDBe3NGF+7Kx9QA0tCRaeHG253v32BW5h68l1UdhMDWnoBSqF9A\nYhaCX8kZho0x8+XW/kDmjJALaODK1wpQricQEEly2xkXtkjPQc78ysUj4eDY\nSNgF5wOyDM/o6GWHIc0BgcPSocr8q13TFHWEZL+BX3vi8oDVx5LqKuNqPYkZ\n4jmRi2nKs/LoHW7nyC8t/4wAOC7nZ6is+qtaPGWYcJHOe7St/rima6EFJNfs\n88zKqxSEedd8wDbZubCU1ITaV5DwRK2RNByi/nPLWj13L0Q2PJLqAU+QWCgs\nm23s\r\n=sOZg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "embla-carousel", "version": "0.3.0", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f822df41c042bf002d00994403f3fdbe8dd0832c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.3.0.tgz", "fileCount": 26, "integrity": "sha512-iWInTkKwF7tiN79WyqMdWdkzDRTtP8hkHneuvLXHc15luKIHCPRX22tJkMcED7kiNMXTtTTw705kfDa/EZZJmA==", "signatures": [{"sig": "MEQCIHpejBU26P0ilSJzkjS0rrShvXh0y31OxyzMQjs1p6SGAiA//4/aS8wl5ehelou/gL9EJI4EGlt5H1noa5wdCuDiRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrjZ5CRA9TVsSAnZWagAAZ9UP/jR182UPF0GgJdECfH1U\nwjOQJc4F2MV2t3nHxNBu2W45tt/ffqNv/qJNxP5Sd590GspPctdccFyp0oTR\nkHE4xutyvMzJheTrcckE6nMTq1YGlSRn5fl/F7VLXxoXye+Jym1R2XjZLqlF\ndZrahZEQdZ1MNjBemjqxaQub7kNGtUHKGPYh4KsOoXxQyzNaIEX0ACE7YBeW\n7gq5/XZyIk2Xfhg86Ez4jrUX7NDlFEZ4d4iyUCJYM93rOZNRfFQpeaDvqa6y\n+lQUCxVb0w/8A6ylL5mLyDW9S02oRDxC/O76lUVi2wNcqVTIpZbLZMTtzLpG\nGbapqMXiDjBRWnYif2wovHd7eizvAPHfEqYT2Yt1NxtRstlz9QLX0iwTwgpV\nA/NIpPLgXwyOYteMHuoerMu+q1/ECpGs8qqeyLbGRgGOlW2Uiad5bm4q4J1n\n3isX7gXnCRACyYmALCDpSCzjoRVOqVtAGsOVPOV6YDv0VAjYRTBWJkzc1CKd\nklFKMT4L/pIdZVnGSQzbUtcAJOBY/gP1ir19OGFPaRRVy09iksnFrxOBpwFJ\nmFL7KRxlhBcI4X8EIxJRpEX7PBzWgQ2QofCJKowYxfowNeKUBS5EFHKwW0Lx\naAjE+PkSOd/tIYUw7DGBn9pYCJPu6uySbu0Fi/+L8W+Dozw5FzJo+a8GPPKP\naN/V\r\n=K9Ro\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "embla-carousel", "version": "0.3.1", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "fae33b856c03612029931090b2aea735b4492f41", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.3.1.tgz", "fileCount": 26, "integrity": "sha512-J1GFkNs97MWxbZtpPYxR+piWIeUOUsGD36ff4XONwRoz7enJt9+1NRvxnBGDjNZAJtdkEUclnXK0XRpVjVPjrw==", "signatures": [{"sig": "MEYCIQCPWk+GLCAx2uXRz/mrnXokEIAze0cgrfiRX8VxN4XruwIhAPDV2ZJwcD5LLJU/Mr3D+px7PiUjA5Wlf5eiO7VhrgO7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcujncCRA9TVsSAnZWagAAIhQP/RZaYKSbWxRsI2eZzw9P\n4g7O6ahZhN2ogPTBmBrIssLEB96h8j+jrnIJoIaNcpGcjasrLNW0YYC8Ti+J\nAO3IqsHVUiq6BWWGiZo7lhsMBlfWGl5bcF+lvR1QqMuP5y2zgHc689MjkIbV\nvWCN4CbltHMRKPSS3k7P/oSenEJgpSQwBNLsLGSEGBbV9LjCaujqG/IiA9em\n6ruvYWPbRwemrjcr5H9IxOMpQZwVG9jo3N0uaKv7bS2g/e8vBHIrk2Szu3IP\nev5Cr7dK4DjYiTT1Lx6wPH35zKMzvXDPC1mA3m8Xr7riHojmMU1VcGU58Ebz\nrkBK0V7hdpsDVia3CGdjh897BNXecVN2VE70A/aZe3zA6R7wmHv+6qZ6LAVX\n32Owhk/OccZZQfZyHz9kEkYEMbcAAgF4mqPyXL3UeO8KlqpwHEpthPe3YXFj\nCeH9jEeFk2plNrZl4fQrN7/1Jh/2v2DvgLhaxwPYcJ+J+BtMWLzeWnwxeVSf\nGTDKrPBhUPQG7MPIKaV0rPaf8jXbc7ATc0kPfnC8UK85Mv3NiThSjE7Kef1D\n295SWRsO7DvvhhFfRvmmD+wf8G1GboogvTWRcniUXDxRY7eOZgQzztNVpPfy\nbBuLB9vPe0L+PyL4k3SzafCR8UG5qys8sqyhgmeNihT6Fdc7VXdK2IJ6JqeJ\nrGgZ\r\n=Udhn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "embla-carousel", "version": "0.3.2", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "13e78c84bb1c07a98e63d863198eb0ebd86be183", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.3.2.tgz", "fileCount": 26, "integrity": "sha512-tfP6TtEBs0w8u9eNn+mGg0sFjQQtWZfIo5BFZFmfwIMKYEbrhaZm8zAY8/Va1C4j58UtAbEWUBGqyDNRWBJsHw==", "signatures": [{"sig": "MEUCIQC4cImhfJpbIj0JWVKBUSE/BwIQKz/lEEeYG8ixWk6lqQIgLZ/ldC5AcDnvFWF9M0JGxfYfVbctkhkYi4x8v2eyTac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuj0tCRA9TVsSAnZWagAAV70P/29c4aohwhy/bTuAxvmD\n5+kqNtuv3YpFDs2CXsl2nXfq7jQnf4CwiXwy5u5y40ncnAj6FB27JLJ83g15\nw1RQum7kzbijg8YcFKYDeOrON99nD3rIX2XJrKFvAlL/MTqGKhOioSNkk1Q9\nqtiXY38CoADNOeEdVGiCYkX6CMSjrPGrb512Sc8Fc4YUTugaBOk9S2SB20PT\nlPmMJv7HkovtWWwSWRHBEm+ZgyBIEcMqsoZgMMdpYrFlWmn+Y53z9/6IZjJv\nL3EV+aYsvq+2MqrG5zc6SqqrxZaLCiygjl+/EeTe51ZVei7pcDm7+i0RsWd8\na83k5DSGh/sfkAkn59qGM9gybuJ5c61+cEQ3Bd2ro7PB6AAXDrAnNv3UAaJn\n8crZZEDtcOZ6eFa90GB2tFHIEhuO6b3iqc30xIAMaQCoLHAW7AvIIorxTeGC\ngL5KnG4UNJxAKP8SYbl0Q2JFrkuC8nn2TjJzqq9DfPamx/l+vTr4Zrm/LKIW\nsY3z/zPbbT1xjTNvuKFiNT25XoBxgPha8D0NXFN1bAFk+zNa+XI2GO1fPa2N\nbiERuG/OjFOEt9HiWGNIKXOelmKQhg/mHBQntMnzmHHt+XAuc8A/VaOCrCR1\nD9wr1jubneESE68rZHjGwgrg9/rlBoBbLd4EdhOV5xnadFPQaEKAv2v39mv3\nV0Iy\r\n=rlnz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "embla-carousel", "version": "0.3.3", "devDependencies": {"jest": "^24.6.0", "tslint": "^5.15.0", "ts-jest": "^23.10.5", "webpack": "^4.29.3", "prettier": "^1.16.4", "node-sass": "^4.11.0", "typescript": "^3.4.1", "@babel/core": "^7.4.0", "@types/jest": "^24.0.0", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.0", "autoprefixer": "^9.5.0", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.2", "webpack-dev-server": "^3.1.14", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "3b779e7f59b0ac0dcb7d93344da9918513511055", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.3.3.tgz", "fileCount": 26, "integrity": "sha512-HLhvifmp1F5b1I3/YIhwvprjiXUtBhgM8afxpKugRVUvMsHBV3tEh242xJH4W5yBt1QZZ6fJYpePNVIb609P1g==", "signatures": [{"sig": "MEYCIQCKqD8tuLbH6Mv2XOvZEBOw7rwNTREaKrC6UFoLZfk+rwIhAN8jmNxaicE4/UwD5BgyC9zkl9kCmLe+IORTLdpjFS8s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcv2yrCRA9TVsSAnZWagAAzPIP+QAdxi71D7Qo53a8AKaB\nTTRtjyAi+aC9VzkC2eEsCpZWYa8of9r+usbQEDOUjb1stDyMGFz5t6q049rU\nOebK8CXKgCF1z06YRN5k50krDt1X5oZfVVrHwF0EvKw1Z8UJsQRK0VJD2/8T\n4ao7kF46OB2Mlk+PKUvp5lIhfs64xDz7eYtZHDuoZiuPn6JwLMKycymSq+mf\ngXTZoz38qMOdmwCksPHqkMTghxKOQm7H3tmWsXti74q8A1SLcxlJsm9tFmLT\nReXSpMmclaKN0ysvdpYbLzxuhmnXviuMx5XwX8sQNDRxpTeKEWcKIKFDynIl\n72Xz1gsLcjeIYIGQq1temoLhNQuEbjoqYZ4xQ1Lc0SmotT9qcTT5G1kZtmIY\n32MOQ1foWaefG2uLFJqoDMrDs6H08UzroOtKjxthHX+//FqGXqu8mCJoanF0\nbpk9fg9rq/wVKlv0iSX7fRcNgB47v67UTC/GHVp5/ouM7dIwFkcxgM4VkWgA\nW9zThK5rfx/TSaoZDpXIMiY08b7VnYbW+B974afLid7MIsZ6W7O0kOcZoJLz\nH19CdIG/s9wkvPnjrPGojFfmWBgl5KUqrL5b9CRCM/agcuTUhI7iQ+msimMo\ng2PHHIE0hxXbqpbXzeQJpY0UzVclVX07o/gyt6gciwzEXEmQO1OY4qgVrA4O\nPrNM\r\n=dpRP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.4": {"name": "embla-carousel", "version": "0.3.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4a7eecd9d3f29a1658daadeaef841b39a889bdac", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.3.4.tgz", "fileCount": 26, "integrity": "sha512-ev+U+Cn7cHezIoMQQydWVWrpqhDaVlA3OY8oLOhdb2WoD6olvBsUVYIkij2Rxc4+P9jOqRhgfzooJg/7U7MHgg==", "signatures": [{"sig": "MEUCIQDX3c4uxLX12/mOv7TGTUPem4ju3Zwi9bTN4g2cEwdq+wIgXBYA7C7R5LqrJocLKDpS5H1+9qjImHGGvdbsC/2pM1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczJooCRA9TVsSAnZWagAAtysQAJP9waGygPpTzSU+fsI2\nhdqedo0M9EBDR3Fdw627D9aiBJg3VYcDtx6dr2OjSy0yMJjfMWi0cZlSiaY5\nBV+nuyGbpjdeqJc3rZ9pQ38xNas0wP7w1NYM871fXUZNJF/k7UsPEITTACcF\nr5jw7LVG3RzlG0V0fl7yogvOO0DdA5kM3e7MUDwVeUxtdxl9jqyk0ygeuUUo\nSHYvoEx7M88XpcteFBJyWE96JxDvm5QEeTFsADo53fUsXJX0rRrxIqeYSZ79\nJKyFDeBThloxxbQlLUTUj2l3jB0EG5zyKhsg9QNKgi6nCBkoufB+T4GFtA0M\n8+4Vot7eiM/SJrlqAdbWFxjTNZ9IAe4JmCiN2eDq0XcgQveAQwAo2I0nUkjj\ndjm5WzOkyuGXUT+YQgjO3tN4nd2gw4rHDc5AylHlJXa/ZQWaSLzpT79LU7Uq\nWiv5MTAu1N6vNnOKGr2zuCp4maDCYXmPeo8qflHD3Se4f++SS+fIPt4VV4rp\n19NvOsyptFvIFGQ84odRjxkLb24iOQ/Y9J6oI6mn0mCyr5MVT6GE8nhvWWR6\n3tm7M05hQass6CfqBl8peygtkfcqtHIVApHtJyGMtrQicn6BQcrsaAd3O6zb\nefSO3mP6IvIfvb2hE/+7oVkjFAnxXEBgn1tQdearETJo33ZMloHR0L+ums62\nMQo2\r\n=Ld3L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "embla-carousel", "version": "0.4.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "8ae2611cc631bce3fe4ba081163c45323b6ba4b4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.4.0.tgz", "fileCount": 26, "integrity": "sha512-JnWE+3jO9ErwfnbqCvVL/EOuKSIyKnpjVjx4Hj7hU5wVKrNsRTR4VuZi36dNFZxDqINRvt5v/MndAUmNVVy9VQ==", "signatures": [{"sig": "MEYCIQCHVkOHqY/f2ucsCSlfOxK0QcC8khjeKxiPQJy0dHeZfwIhALKWsr1Co+EpafM5g4R/jmjxXd4t3z9FBKq5BFAE+eoR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2dDpCRA9TVsSAnZWagAAPd0P/3LBCBfootKahioCAA6z\nwEyNbQnVieHnGEqUJISPXVmnABt/kobOJCHTbMmBnEGO/0nlYR9dWEvQRNBX\n1VGQFjEwa7w1abpbaQQBLyCTP9yUqy8v8Ao5Nle5ubJuCgOwfbRceA3bAXUA\nB3sWkXVwwkGmBndVJbe8SOTarhqDS1vHAlmQZAiCbIzy13PWxlM5c/teeSLx\nrqwL9/EVnRDHqJvpbQ0ovdTvR9mjKRzdws3tivyfnLQU8txkHLBMzYXchxQB\n1mtGPTPXU4BSpS4IVSpzvs27RnXHOt6qrjcpzMxVZ5Ufy4RWYolstJM5D9eJ\np0fLxcMAsQMBq50B7DF/0QQHU9wFP/SzMoXyq0iFglsK/KWHjByXhV1xyUBU\neG42tLLqN1XZcJ9aJNB2Y+3vaUX9MmNQjeMqdQXppwokcUnsOGeIrHisxyfx\nu/3uxcs4LPlggig55rQNgFxRaGOHmdXEmD1yplf9t3hbzIQo2Fx0ec2tiCMl\nSR99xg4tbWLv3ESnLBLyunQONWciMmCW4+j5AgOm9ADLtwOtRlXj4+8d32FM\n8r3d+kboSQBCR83j3DYeSklgGwZ+usk4oT1XDq591V3SwPdjZryxi2Wrp+SA\nl+GYsn82ajicdwqZidy9CONXzSK05e34eHUZ3EdSchWH0EXT01PoGtUBDiyV\nz8iV\r\n=m2r4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.0": {"name": "embla-carousel", "version": "0.5.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "2aa78164521fdac99e39ca013bb1ca35e2b73e1f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.5.0.tgz", "fileCount": 26, "integrity": "sha512-us7q+Lj7JoqHOuaniXLSUuHNvnBHPIS9Gn3zAce64weMMQx2JCQpSpp6gntU6a4hCnGFioRtKSfkViiLqmIGfw==", "signatures": [{"sig": "MEUCICEuHUuh+Zk4W3HSm2cA9bAYakH+i3lkpCP0lK8z2R9BAiEAhDnX5pUbiPR1Q0czxRkZ5EtC6ArJLk1Swgb62RXHuAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3FCrCRA9TVsSAnZWagAA6/cP/A9hfukDLTzbrFC40JrV\nF7eSXAnezi9W8BqIjHae9UKEvR8M/+ezFpq+1KmknYm31oRzTCTxmxktJlcJ\nfRj67D/zNjNQ8ZeTPMKHSzpStnA7Rqf4TsODSFWpoIAAejhdo/hOyknykK4J\nWbHwo1bc1rYITxxbRNATJSgcnYbe66z0xVzw1ck3BoFEnkU/aBPG9AwaPSPv\n5DfcN4XcFEo7QnIZw2JRaSclxvjAjCiapR6E28fzEj8dO5hAfga5S8syUZNh\noWi81wx/DoAY/MAd3SxZXYgWDRQ046oROWVgFFFf128n7viRLBJZ4EMDamX5\ndNaacZhxVTkwatHLcBcw4zLyEZEep0FPbvw7GCttTNBE8q1FZ59nsgKKfJxj\neCjqH7EbFY0ZtwYr0he5SmRP0nlNmOPlsVzRkc+qcf9ZghaQqHNhZFkTieZz\nIqPuF4CIZG1XvqrD2HgjmOtyLXUFpjZ97kAU4/kkOcL1djILOjH5EGSeYVWR\nM53kBscjq/7e3/hxDMk2y+Agu43BgVyY6Shj0sGNaP8RPazPnz763rZrILxz\nAeJi0t0Pw94bdIH9+sGWDnuyH+G+ABYE0JX+O7Eak3HddXJ9LYa31FZKB2fI\nj0IJQyp78lopzYPpf3XyydLDQ+8wrfSwP5nrJMZ/g2NMHVjmUoU0topzm3sF\nvWP+\r\n=Fkwg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.5.1": {"name": "embla-carousel", "version": "0.5.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "efbd7ecbc35aa216b4519714c5d74b24579305d7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.5.1.tgz", "fileCount": 26, "integrity": "sha512-auNfMANIx3+EOFfok5DWUhcdWNTdBTX4o/szsmXVTuNEulIYTJXbH7amXOU41OVebLllAVe21vKATNbY5tmZNg==", "signatures": [{"sig": "MEUCIBCYz+T2CWpt7XJ2E1+NFxvZI1w8AHImhltEZ1Y+E7ltAiEAmCGJQ7on39JulFn+2ifnIAUVDfc5PimLGzXQj3qfIAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3GU6CRA9TVsSAnZWagAAV4gP/0o0TCtcl77qL5NLyl4K\nqRlLLOSht8j5HdgaycUMD8W4yAG2bUQ7jgmLOWVAUBsAO37x72NP5i3gTu0y\n3SChnGFgxBXP0O6yIQkQviHdAxg43mUUIcspniAsJoj9sqFEPqbKvTv+vBD8\nEV3Eq1bJ6pJmuhEZ4LJ9E7BrLWYhHVf3bV9/fkpc3aoVs9N2IVWq5RUeqHO2\n2RG+oJWu6nIylnbZQtjc5Ih4dI/+pDnViz2lAqasUVLsWA4qoaZXdzB7AzSe\nHoR+0tWVrmfDXMIdIN01oeyRJ7YtOOd8Op2+o9nen2b7S6boHVJbLHrGCTrQ\nYQhEbunnoy8MQPOmeIb+0rPP6ZB0jop97RWoDd01CUr7Ls2DjKrxvDdyJUwA\nQBq/zoHYm4sKtT0acBQLfsqMEaa8YNyFndM+ol5l6LRvluh4HyWoBGzH+ky8\n6AVfieIRI9IywFY3cv/7KCX0lA5c/7Lzq5BlND3urw20v7rTBQbn50u/8g5X\nrWq5O93cGxGnN5cSK+ohAd10CPAxM3NM5qr/iQ8SXpsL2ISPZ4tv34KEuRaT\no7jdjsYOUOpcWfeyTRw1a/GMuCWYsJgR+Ia+NmY2Jtm4ArXOwivz1DcBZAKO\n5i3l6jAjkcQIr/79CDP/SH3BIjBIDWOxUdwyo8uwbwmBzrWgi95IUiCOlN2E\n3YaN\r\n=cXr2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.0": {"name": "embla-carousel", "version": "0.6.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "188022ee2f1ca07ffcdf29a6c542e3bbaebbcd18", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.6.0.tgz", "fileCount": 26, "integrity": "sha512-nxzMUBAPaCnnfXB4ziLbiFJUGpv3id8w8LpzHVbQYvL46LRtrDLeGB2uIbvTE9Xlpm132zqnsJsszkU1odzEcw==", "signatures": [{"sig": "MEUCIQDOaYepUEneWVGYB15lg6B1JdHYQzhHwQHOGijaN7GEMQIgHN8/ez4oT0bpnR9uaL7YL0Zn5etDls6u2FL7z+rHssI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5ErYCRA9TVsSAnZWagAAzeoQAJAx0GLtec5g9MqJZvAc\nn8fmLdxW0O+QRHDwCn6GB0IhzOUwCY9qRavWl7jvrVPshWd7iBw/1Hi/bkUw\nwqSmKKhQFJXlDq4bFoHnSEFWuyYaYZf4VJLzlD0BWYJXvtlvBkbRz0hY+pGQ\n+qCkAZU27uFAfyPM54+jUaVkDLyV3jdYYHgoC/DxyNi9RD3TnDJdVuUjuUfM\nzzCP9x7wDwdR2eKTxV8vnTFacsgnsuDVabS5ew3j+bd8t34FLnWdEz3GN+OJ\n/NreAX/QgdRjkdRaqfuSLUwISO5ypuUg0uURuOEGy8/SlcMNhYJeToMGIPNz\n2+Axbu2O5hNacL8AEMnNde/Qdfeu4czjEVRngyqb9WRF/MrIsXNM+t/eQ2xP\n/gI2m0TCm9DqDsmGPbypk3tG7jBEDKlVGWu92uXEc1++8BehKxx9LC3KVLB8\nAH9rLhF/vXbIclJSRRLQebUXQFxuMcWraRmz5BQl7xdRn5ngAiWdp1D2+d48\nEM+FKZVFZbr8qFnz3uPethktbo3xTRwXZsaPqhJfJKakOoeKBdJPCocUYA11\ngGVoJuInSKHmhFDwzISDj8Xv4aW7xroD3d8f7g5Jr2zh1BI8DgwcXn93gb5q\nVDnNaOLGfqJuuXBD6xhAeLXsolUTX2lEWexf8fPtswdbY5z9ThzNaPZvX0Cr\nMJPg\r\n=U+i4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.1": {"name": "embla-carousel", "version": "0.6.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "5fadc8bb9cbfb0c96d3184fb1d3b66e4cd912c32", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.6.1.tgz", "fileCount": 26, "integrity": "sha512-7Vqv3kZDGZbqljWVPjnXXkn/YQhHp3WCOy7FBzyT+d0R5+8i0adlEX/jeFk/PwiABfiw6mCpmb37wxSjEWgY/A==", "signatures": [{"sig": "MEUCIE2ID7NaQIJh++ArSrcF7xl5Eh71cIleNyHWLFXdWa/MAiEA3srlC8IdL5LhmXM2hVpb1nIWjBTu6IGTEnv21Dy1ZRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5E6MCRA9TVsSAnZWagAAQ6kP+QE1bwch+w3uwa0GNQqU\nbshFSJCBV4NohUcaloaLLib83VSuGpLR8yVgR2Xy3UlAml28YOpuk+juDMgK\n9Jmp3Gcav8WYgbp4mhThjDqkLJ2PHattzZfMbGmzhVyW8ck9t5vao+I1cfK7\nOek3uEa+if2SCAtc1+dIY5lHnIcfFaK085fnbjXEiYszK7jc2YHG9/iQXxC3\n0T2XK58F0obcvOY1n2ptnwGO2iRbBFBUQx8ZRLAF7BRXG5wsLymIqBLbvlvB\nxRahRwIHddlFTeQFwrPjdOHRkDpEpuzjVtk6CFZM/0CtYoc1495KqFzMpU4R\nhWc65jO+J6VR36nN6bhKTMkcExwUi6+0FF0UjzJGSRXMW0ddck++gprA7Kpl\nynXsHjiyDCwLs2jFNJwZ4ucYETGQH9rXN2wUkcXWmqMxEL/A9O1t8R0k+JYZ\nVIIy79Blx6WNfGxvabRwSq0mk8FSga8eoh4JzBwMQ1Ss9zh/loViEDEF99Tt\n6PX2tuA34sKybfMAwjpI4XdedDa72Q+/2wyRm03AEdw9QhD+zSSzU6Yquzum\n1Q/XHNhWFHaSII7Yut/IlhSY8agOQH3gwPO8uFEANjPFBeFuq7+uJ2mS2mvw\nXag2tqhI1S0EHd/lYkjCA6U8dcI1G3k0hfpFCBUQzT9xLb+r1dh7jpCAD+bn\n8scU\r\n=tB99\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.2": {"name": "embla-carousel", "version": "0.6.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "ae97bcd5ae2727704fe36403eebd39a42129cdb4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.6.2.tgz", "fileCount": 26, "integrity": "sha512-7SG4VSnNm/BVNA5yCrYb/rzSb+xfCXv0HTce31m7lvEk8/3jItlXJOeNfDI7TPhlN+D8Lxcs0HdkGhNnJJss9Q==", "signatures": [{"sig": "MEUCIQCN1hSnbjITl36b3fmRO/Zc5impdgNKxEWVjLg4mdY9vQIgFzncDHIMI7lC5IlYjK/S5JIHFLDUTObFArMfBZChd6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5GDiCRA9TVsSAnZWagAAAuAP/ihL8Iefg5SNh9AyybJp\nCt/NpSRmkVOXDFMVunWRVJLItSRnR0crsFxaVKeUZ9IDzxEdX68kiWtHUXmk\nLB1C0mp9Fy7OW4oPoBOSqfs+y2CUB8gLaVwWiiOYP09SqsgPh/wqeAcp5Yoj\ncBeIQp5THdDzDUL7Xq0ae5mfTMki604D2JsQuSUC0z/l72JA1b0DQi5JxXKz\nQ2+L7e4mZdSVa0fb5HUeVVTQK7WdShyMF5FDhrsQgoi0XyAIVK8hNJ9KiMxg\nDG/+Oq9pkndtGPYjWVh60WUoBms/VtrUOR8/j0gqma9/6rLTN7AdNWGBIwYL\n3v+TqZDbX9FVzkjIQjCONnTMl9gtzo/VBwGHb2OW5nlT5IyG8BMrlnFuoFZc\nVmD7iqBA/EN2knWixL6vaSI6n8guQ58ovS3+vBbuHDcRUkMWde7XFquKPDUb\njodo8WBY/HXWcF9SHZk2qfou22KRBQub5Th4a+N4fbW6Oihm+M7BFhPV6WGl\nsvVh0KHhmURDQGZRIfvsyjsFrR9nDvJsh23yS/rlqd3Qr2IhGM+XFQtsUDxG\nYYG3fySaLV4Jh5bhndZJ+nfI0qHxn0wt39sxJTvFXt8jcoMfypGRvYwDpgtP\n3FYOMsJyI6tjblViYiVX6jRdEB+IMrKebfbCVmC84CZRijj0pERjzRF0yF4s\nzAAk\r\n=eHqO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.3": {"name": "embla-carousel", "version": "0.6.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "6f291d853b010c1ee53f3ca53e22d8a076fc878b", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.6.3.tgz", "fileCount": 26, "integrity": "sha512-yqGFUvrvUxy8ABKlNUiOjqTF8NXN26NIoHdoHQ6m1b36WMkZJ8r8wTK8vcbrQ80JsS38CWLtF2uwgs/J8/uSrg==", "signatures": [{"sig": "MEUCIAO9jbmj4TW16sb/raSLAzPn4iWVUdAz9nSoF3CbYz8LAiEAgJIusDhxALrOZvZt+CLVAPnkcshCQ/bi2OB3QjQzxjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63928, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc5bc8CRA9TVsSAnZWagAAeUQP/A7ACkk/SS6IPouDjmec\nxHlXk77aKQEJUg3zBRWHG5u8hpV0h9xiFBrH87uZ6DPaX1uL2xdeQf/LdqXH\nrQ7ysp+e3RuMCINl0WH01zEmtKLinCB22Cd7ZC1BtNslnIX49+Duczsg8Ywl\nik6quLygdWS7RRpeAOFlWMbWT1pBCculwImFKVc8ksP9YIraqnficNP2o3U5\nBEEIA6AJoM+suM3sNZPWgJ0rqT5a6I5Cle71b9NwVktYXd4hJxycEHYyr7SZ\nfPy0xHXh4B0iBS3tDo+pO2eqYaLwY6ZKnQtytFmGQXPD4szsNyN0BoDAszQ2\nfvQxzk5LATIpEr0x2yfKVcKkhX0PuVLLUkAcgyAEizS7j3j3vHdI+HhsWPgt\nLdTZeRiup0d4jwtwcXmIB7SnUDCfdtsXo575oqsbC5I9u08zLZIQVpN2oo0A\niWBS7AgIdTATvej0tlm+m7IK2R0bNnmYAwuC4CZvsNZuU13wxDAn7oaH+42c\n4e6/HpH1DjbeRTINAN52VUTvq/xWl2aKWxO5IOEXnMvStKMfo3Od7de7cF+i\n9zlMO0vPeIvcfrov9hm9tRJPD+oZhHJjzCcWYP1OlYtOjD3RMzjlrRi3VcT2\nt9IqKf0fFNbRoSBwdhAtCRP2uWzR61fV/DDJlyeHUvSHZZvfnz5Int9x7SZy\nap36\r\n=Glw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.6.4": {"name": "embla-carousel", "version": "0.6.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b51332e832481bc97d515019aac5f498ff21899b", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.6.4.tgz", "fileCount": 26, "integrity": "sha512-ovpqpeVvrwLD6rCRyR5RMTPGvF8UGkYx4ns3uh+dmYwoALiRqZOoww2WsXFS9JGnnpLfPeb/xdNxr23Pg17rVA==", "signatures": [{"sig": "MEQCIGe4VUTqDDRpkWfJm6s9OMZbOg1HY3xbdDWg/Ebnx2SJAiBmRFBpWxDq6P8BwuXVa21mMTIMUoZBH3WVfcY1JgaT3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc6G+/CRA9TVsSAnZWagAALekP/3dn1O2VBuB7+59QrO1Y\n7KCksGKQszDSAYR1+46SYicVMKnRHilafH6DdqPm/Nj6neKyqCN2KLFPgoL7\nHH0Cy1JWyasM/UjkkjJ6RZr7eGeVKHC4fd+pdMoOl+u4MF4UXy1aejBBSjGQ\nErtBSWDnnf6mERv+EAW8IAeOOD1kpfCgeyH/MT9uTqVKGlO1duC+e+GEF7Ks\nRaDIF4/u6FowvQrqcbZUW9jKIYhoY1/8YaKCOdu+KW8nM3EPspeBE2vintr7\nYcGTtAie6atMondGZ9HY6IGb0BimLcNjce7R+qW+yipEtfccp8DCeCcT6ixp\nCS+9OWevlOwb1O641fTYdv2NEhgRLfO8Np+MRvugwRWtHTJMDwZisb+KmFkY\nOnxWFfqNHtctu9CR7X/1BGrT/0kuMMoGVlm+ih13gV6o1hJadNFuucIytSp4\nqL0K9Vmu8pCb2GgZUcjFLWJ7Ze6pIw9Ytqmz2cmGFOPZJE4t63vCQUjcTJle\nzpOgl0tARamh8lspNKlMhHSWs4WpCkORYNCIGuTeWOyQ/5P8L8X2qWQQgUFn\n+84EfuPmW1g/4eYzsnfwcEk/4Rq5dDA1idor2njsF0TuDyFN8p5y7G0PiM3s\nHLdnCmt982JnizzMk5dU4BMYfgo1hVMr/AfCMUM2BYx9GDOzyu2D11SwFoia\nKGSc\r\n=sOHc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.0": {"name": "embla-carousel", "version": "0.7.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "98abb4fb8073d4324ea4d9905af1fd8f79727111", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.0.tgz", "fileCount": 26, "integrity": "sha512-ihJ1tvNLRtushPMn+DN/OpqI4idRHrqHROOCabbBKC81WqFZr0byLUE8z0yNcAlIkVh9O2r8XNzLG5MzsK6/Qw==", "signatures": [{"sig": "MEYCIQDDtUOFSvkB1ZsUjAgCNK6fEe1knx++WZxXXzN0CjyUSwIhALb5uHn1GsQizZZzymNDm9Jbxj6euvTpqd3QRAEFgbjn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7sELCRA9TVsSAnZWagAAqTUP+wW2KHaK6LXbb8o5fBoC\nH4TlVXAmvJRrh4CKSGLZ2q02wkIqGNztR9TpXD67Wq9Tm8npHi2m6YV5ilq8\n3DoFx/yBNFZLXw8Tml6U4PtWPMKZaWeSNze6UVUX6irtK3BG6U+iUTExH/nQ\nNA4VUAd/N7c4GiubwJa0PEvLbd82xpUUS9C+vvfGZ4D06tVKv/rXUnutAvA5\nr/vuWk7pkqLlXzdJTJpFLoE8Z8NrbyMbiDam7+glA2YYo6ygXN+LW3b7Qgkx\nj0Fl4x9WevYCJdnjGzBxPZo+3S15grqJcN29K6tYembUiH9hOXfW/Qehk2zx\n6Xg9p18yhA27u6ljsRHj/l2L11rTQADGRMhX+iUy+ItNiPEqSu6pCGkKKe6Y\n3NijEE5OopjtnBQM4G6Hm64kYHR0+skIHedsqLpRvAH+4JYW4i7zlBiiw0Jw\nqTBO4h38n9wktVpv7lqJyyn3dt/BLB8MaZ9+5Zrnaqm4/7wNYB/Ik6gCT0YH\nLaYrb/F0Je1M3WcY+am7RejU1cxEkD40xwD9TMv3RGXLVlIfP6itp01qa04u\n7NxDj/lRZYYl6RsujT7ZZA7yMPBJ57+cLQH+mq92DpgJ+gdHqa7kSTgRw+zQ\nNFQD6eiKNv4lalq0U3LKBMUrJR8JVy29kBQZzqnEzTC5TF3GD6b3X4saF9xI\nQRY8\r\n=QbKM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.1": {"name": "embla-carousel", "version": "0.7.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "c9c99036c2830b51d2651d1c17bab27711aa1b6f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.1.tgz", "fileCount": 26, "integrity": "sha512-GXEFVUGS2xx8gk1VKrr3Zskdll6nelvYlDn0czlpr0EYZXQzFJ1zZKtHfQfOHPNtj7SVNx3VD5JiiMAF6P2GAQ==", "signatures": [{"sig": "MEQCIC6W9H/e6/R6tkMiyQIb4tJKUlZotoA/Znfp9/KX37Y7AiA/zc/PnSMTpoGMIDRQGUXg2IwYMimcusmZuI6BshC6Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7xljCRA9TVsSAnZWagAAyJkP/iwxQk6Qi3NpDCJohwiE\nRx1LADELajEsC0HeC/MS9KgJ+TVmz+WvJX7EkPEdcFr1iSVjaR1vtgWg+5qN\nkKVKGBLOtYN7Rg1mWLsVpjgnkSZCa6PdO6s/gcO6EKM/ttIIKtt5X7nYRDPr\nW89Lw080akTKs2DCT9f5YXRjObWiRg9siGt+Ib59RW2V5nxVwQRO0l9OSybO\nfuze5e5rkiKuPuFTxyv8J/3reAdnE8AFlEzbYSnhY6Mc7VnIlvk/srvvm8tJ\nJDah3LZrnsRSIOimT1JQ9I7fj4jlSYvIsRnL1NhzGeoFHG4CDFTQWuK9hyre\nfM/BHBylYHI+bw0GlEirQ8cIF0W+0OlvW8Hg9kyc7i1DnDZQyTCtMI3BV5iD\nMBx21+wDyz81FbA0J5pU34I1H1naKjQIHy9XnPE+4+TxVOzuYrg5o6fBfJCB\ndEjsdXxOZLW89vulOagJUIrUPdND/nDA023//J8flF89wuEK2/v7Ga1nrkcC\nB6ufN3FOxQlO92btgH8E3iPLk1r/u2DEW4aMo2LyYPmMt/XWL7V9y6udpY9P\nLStAVp47o+UD10qHaBxK7a86IZP+/dhgf8Wt/O8Tj8fY24fyePcrofkRsDBs\nj0CfsEWm9369Q07UbBQoc06ioxdNCdLVMB3w4uj9NpwdRLNG5GMDw2Hbgweu\nOUkd\r\n=NMj5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.2": {"name": "embla-carousel", "version": "0.7.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "27059c7d17a76e75c3b19d6d0309679ae0f94310", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.2.tgz", "fileCount": 26, "integrity": "sha512-KDNUEIJ7yWoW1ACiBCWt9gQFdsgYTfu5SB+HsQqrU0/ZJ4JPRkT3WDI2DCfjIay8u+tOKHLztBD4IrmE9ynl5g==", "signatures": [{"sig": "MEUCIQDUSnyAYE8tDkBMxR5hY1vzABWhujXA6rhniIVKz8g9hAIgKoct/hnUCGil2TVvoJmnNAIacre7+GnjX1/ohzm4wdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+X05CRA9TVsSAnZWagAAqBMP/jlDqZ1ZmEBpF/OWYCdT\neHwCNRWcfsqNbHj+LVYd/p34audyHlgSLuoiAbQyQuUYoiZszQ6djU9ANJVk\nCM90vgZb349YSrkTaDDssyiUh2+FIQUGlysY4dotDtSDwkeACip+fr4b/VDl\nVmjfjqisL8wKDGgTh7d8398y7RkeUrWyPBr9DezCcSYwqTCi+SouSvaOtIaE\nFaORpYN+YaKen3LbMS+mUcRGLICAsCkAVLL2+uREzIP744St+rMbV0nRe84O\n5+mlw6FkiD6mTxrsCZ7Tn2KGcksQGI77SMG0F28s+E85jMmC1DBCvUhetWfE\nZwhoAYemEI18pFJysd8HS108u/0/JptN2brpMINwzUGjjd0vQAwNRU4TxHDr\nJzCrEp4w/pqtsVLBEGojG4Lo2PlfIUVhxbkIq5Aopden8ssGuIJPLqtjeCdo\nWfKuXL2mzpdA5/6uF2zarwZhkE7GRCHAhoYteywsWettagXGOE3c3LypHPHG\nI30cJIxhqxcVHhaxMA2M/zVOm00CE5ydzaUmx57aP2F3OWtr98hcWUFua/KY\nETXaUtkhF2BWmXycazQBJq+F7ErxthYJ6kf//NM3J4ZRkJdXpqIrPNAZetvR\nlJ8t8782QNYABId8YYdB2lUD+16qsE9JRroNPCCWCXOA8YGrjlIpUprwLHR/\nq5T9\r\n=uwX0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.3": {"name": "embla-carousel", "version": "0.7.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a6ca95c32b15a901e15a97451d2ccea3ea16f5bb", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.3.tgz", "fileCount": 26, "integrity": "sha512-P/gWqoBlh/rDJdnfZznYfahkN9aPIHAylP7kcBxhWZCMal3hwKv44Adc8jLwPEkkkNgDR/nyc59ABC6dk9RtHg==", "signatures": [{"sig": "MEUCIQC93zNvaqSmb8L2tq9PbzBGxHHaPL3IutlusYuO5cYZkQIgNngMSKiACZPGc+X/64BCBFSjstXD6HhDVKcr1EMTZh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/PiyCRA9TVsSAnZWagAAx28P/0CTIcA1XlaHaxce9F9/\njzzw+SXJjlv0KS0jYk6YvJVKjmMgaAF768kDYdOG+BBdt8m1S27HTJ3VUWxX\njsqeZIXLA5nlGV10VVg1Dm9EKYULhdjQd6ZVRIhRok5enqY6XpYPsJif1xcm\nO9RWN81o/gfGg2KnHIur2a6Sr6y3rKxyLdU04Ee0LlNjUbxApc8R6SnwXsk7\n4Y1yqhJTKeYS5Tl1cZH5Bex9kwVTrFk+YPt91Mz+8lXmc76w25HHNyfqvIAW\niHF77jAa+yNHTed0SLs+XOO2p/FAUsAR2EXeKaITYpk9pRw78MJ9MnbGh5O2\nEQCw4w5U6u7yZqZ/cKhTK9DH2YfSo7laegR15ElDkKt3zKNzCthm+FTs5Q6a\nx10WYsdXSNT/TJ3/t5iK5yuZf6H4ce6txPOcO1PwcwhoMFEVAo5QXmNIwv6i\nsgKjtsJk5kpaDkb0CFz51ON6ZU0WwteElkKz4COvhHNgkvlUzlcqoVcL3ySW\nVYJaIN6QltksLbPVN9VnM3WFS6BmY3uQkgLtZV9JEq8qrBsNlBTs1E9rSBDl\nMh5BNH9FgyE+I30xEQd7MRmhWpd0xLq0HT6owKgNG1jmuxIodI8bwP+sOChn\nS37rK1ijY+BzEE3DAzt05e9L0fAw5N8f49H6Tups+vrfnIR7Hels94wriqUg\n//ET\r\n=LBBw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.4": {"name": "embla-carousel", "version": "0.7.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b633aa89d30f0e4e1e2d297ec37a2c5c81643c8d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.4.tgz", "fileCount": 26, "integrity": "sha512-Z9DhknaHbIIyUIYO1FyE+Lyt5NR3GeEmHqrqI1f05TocgQDJMv+aHizr4T8xT91vLJJs9LIjqmzkzlGEulrwWA==", "signatures": [{"sig": "MEUCIQDimVfIQfyM3LAUz5tbL6S90xxO4K9MokEjeI4bAX6sgQIgHhzTlfkZ17y/Or3PldzEogA4PctG59VJlFEXwKyAIek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/P11CRA9TVsSAnZWagAAp3QP/1fF3motWBA44njNpf+7\ntDJttwslgsUj3Ww4hs3SXF6uEFoc3eX83j4ePym/rSHJO3ExmEnNDyRlvJWO\nigNbDewCNX6nycJ1jNWawuyLQIfwEOynri3h6OQIQkrnag5XcFi0lyLScM3d\nj8pKeKjcdrBxnUg3Ts+0oRY9tc9gxhYuPEoKabYj0xNT6/s+aOlFlY32RjpY\ns+H2HKMXzOck8ayoveGBZOi5xJRsYNyRRZqcPBKfCJYCf2P7OGlC9xO+ZE8d\n9K4T3jP7YiHts9GrbdGij6NofbLsQKWyb0OHisxYUi2LMzboQ5e8ZcvCapxK\nEs1sHTIBGPtAUrR/MrVPkNTMaSe4HOtFgpF4qOw8kKbMHD+XVTPc3rk/CGaS\nXs3Hz0kZeTktV3NgShJVMUwHBPLP8OBv+MBnZkJZf7NoOW/4PeexXAn0X/Oo\nxGEaogyFF8HeOWlasq7oSMzvR0+zVXRXQFf/NWYfETSq1sdVmaJoKMNK9yr6\nfS+Ksq0vAG2R0UGdXAnuoTqgP3BOZ0Oy4RDAo4av/nvsv0GAqRe+2ImpQTI8\nwYdMUPdiCAIUn4jZaQz63zCIw/JQ5TrhNBDKzwD5HPNhlOAlLnSMHsx2rgDt\nV3cbId/W2REOD100PVMsPcVlOCrECEieQz4Vja6sRBFCklqQOxlAH553Z/U3\nhdvu\r\n=jG7U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.5": {"name": "embla-carousel", "version": "0.7.5", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "645b664d7dfc9d71ea5818fde2307fdf066a4ac1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.5.tgz", "fileCount": 26, "integrity": "sha512-IMqcg3WPHtisEoAUaL38zXODYBAOe0+Q9hhMRgzElRd65oeSbx6wFvNN6Ocahzvm+pbgiP9S1q+6OuSY0wdBHg==", "signatures": [{"sig": "MEQCID35v0G5IaEhGG7Y9kV2vXPMwxOKzCP6nSg6TKtr5QEgAiBcxlUbQ5iWXGU7qrUs1Cfo5vkvFhDFiAjS7QOb84TWdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/QAUCRA9TVsSAnZWagAAp/YQAIthpOVKGgEjFfvo7W0P\nl+rS4L0MBotWdpqA/dcO/KdvK9dIwqF4ARTqZusthPRdAhpc7615naLVB449\nGXA2KpNnANlYPZIyL0o0n2RoSewTvCOmlpNAIcCVyFseGgLXC4HfDyyfhRg3\nRy6DL9JDcVN1ooUmHjFfTfbUPebgrmC5yOpwIRWulEno+CRI4uEinJCKQnH5\nxHa7L/rPVVW2EK4dTameizdygxhdvVKad1sYWdTE4UyuuerzKYsiE3jhUZW8\nSZpgc7WbDu3v8JBfRszeI68OYbtCtfXU2BzbnaxQ6QKqAfFok9ah6VYzwACp\nFSZAxDivTPqlspDIXaosSpjiVkxdibL4r0sue5BdmnHEGYhJsTlOpR63q/eg\nLa8nVrw8MkivaXIZ39qBjvInFwDA+1xpY4lk5Lut8QeNfz70cRbSabwVa/nU\ne4Ou72GkPAEafycwwBFTjKDRf384nNGQA/N7eoavLb5oUnBXotAdSXJ5LFSa\nGK5wRQTF1is+3I9t5SUJXZMw+KOY8xpLfz1sWBgmDewvzNaRgsouH8mh2H1L\nvKPapQaNHVrvL7VQvRZevNlAxnweyJSw3A4JzxCKXPjVHLXvqaE04BMpths8\ng4LG52t+3exdWCpjwaTMpd/0kAx5k6XqWcO4xc4P4rItFOuFI5pwmw5XIKIa\nsEgm\r\n=XVpt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.6": {"name": "embla-carousel", "version": "0.7.6", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "98d6287361f6d1dc21c8a0b905768fa4f207f0dd", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.6.tgz", "fileCount": 26, "integrity": "sha512-eNcS8fklvwfi7ei7pTpmlBaxzsdbPbtzv4+/VBbNId7ICm5PTD6FBExHbgSkvKpgkjedX4rHy9GC8JZY+36NyQ==", "signatures": [{"sig": "MEYCIQDEsWHDkz/627wobVFw/Hhl4VboqwrgAgqJiqGc0oq7HgIhAKk9w/bPgm7KykqOyX+lbn5AAlbyFhDzxkAAioN+fdrp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/pZiCRA9TVsSAnZWagAA1R8P/2RtNsUd2yyKJa84ty+U\nLdVwaZ5Z6IS1bqkcAAsTlh543mLgBREi8zpTzCaeS8eGlZ38O4IovzXf7426\naS6tThEQs2pxNy94P48S0Y9eVAUhXIda+eIInTFWD/fWBjQXOxFBHiTUqRro\nVd56RFwRp5fBGO+VqSB/SZoARILjVdNK6Pvb9YIKf0X82Iyvzk0sdZp4ySbG\nhl/GFytT+1sDM3Ubm/YUAA2qy1HtwflimVPlRtn6V2JhgQt+1YvaH5UXpTK2\n4ELvNPh3JCVEeaTyYWNobXdqu6yWtGqJaWWJwWlwJQLbVqK6LFAKA/KKbYuP\nPTt2XxW/IRpI1WEfa1jk/+sTze5NyH9MHO6yr1WPpbRVx0iskbGmI198OTq3\nz4qVFZZho2n8tpx7E1cOIIk0pI+6ePBMDpFzBqfGQX/MhEhoAU6ob99OPpWV\n0SnLpQlu7nc6p/9WvWzeLCgh34y2ctzBcSfrnQVmxbOSHTQpMEYVVJf8F7sz\ngCK1Hf693ax5pnLhknyvQicFDmNu4HzV5kK0EiAHTqAD/4Wx2acP1nVbP0Qi\nwkAeIj+uLFPhG4aC85ZAcgjfZDIBRgTihavXYFiLuP+sayUKhbfbMWoXvIg4\nevR7CuRrtokxeWukrCbnvLJaXItXu6OoO7F/QGrzmS/cZeq9+QRBB5+gNsqG\nb/Gu\r\n=Arun\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.7": {"name": "embla-carousel", "version": "0.7.7", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b6dadb85550f2723b577b668139d16108ed6d6f8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.7.tgz", "fileCount": 26, "integrity": "sha512-B9dWgf6rPf8hOeYA2zyU2MI7OHkKbuCNql/igTCXwlj9p2HwdDUA9JgagSG+hwzexwMjiFlGebt1m1aHDfVRUw==", "signatures": [{"sig": "MEUCIQCCza945/vdIJGziL6CdAn+23aw4tCnXO5BtQXjWT5NMAIgffdrZ4J/uDMcp2z+k7ck+RMn3ZO8dCpbpm+7COJ78ic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/pgcCRA9TVsSAnZWagAAmoAP/AyOGl3G8tQibPG/r0i1\ncEpBM9c+HWOgFCG4K8QDxrxBpMiU1fd4G3nTbgJix0yzuLFwuaIo59ytj70j\n6ADb3mg3xCWckqngdgajp0XA2ULW2WbahjvEO+2PXPGWMa8jBEO9y6DWTkep\nT8V6uwaEB0QQe7+LTrh06N1HakAutpF5wjb6TXvFNOUs6Nr42beTpDmYHtx1\n20Dc+rTetx8B77NR6uJsHilk9OWCANL1vGhwL4xFbDygfzr0a6XV5uGMyIBc\nkiWnh62MQ7hMM5gtWmTFQv7PJK4QhoDaUj4eng8nIkjPJsPF/8tugWUZRiqs\nleHZuTwNhMD+aCAHUDo/uOM+izTeWLWWnypHm7ypPy579pinXhIdXY7EXG3m\ntCR/PKfPtqvUnIxTkveOi8Nlqbxb8Ls+26CX5lFMysyJ7IGifqNPHVGOXJDs\nMd+cG+nE+5z39wzyrobJntrkwMbjYSQIVOfE76mz3k+tDakuBaQVgneNXyW3\nF/eT/3f5etq+yfePdWMBInBYOIV56vLGtJCHyHRReQmzjMzyknObRJ8kfP2e\nbhgjb2GY13SFh3dUwKdmhcLWNtpCPnjFqqLoZknJ6TeNPx3tg4EF12MI6nlw\n/jPggT+jI2YMw39quGn2mmO9/RL0cGkq+Ev13MwFIzNeGJBFDYwTB8x+JjAq\nZ9Zy\r\n=uPi1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.7.8": {"name": "embla-carousel", "version": "0.7.8", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "e7959cbdd8ecba362d17d40025c9d2b5f7ce5f88", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.7.8.tgz", "fileCount": 26, "integrity": "sha512-4hRPg3OG4cyJt7yOUqPtRgccjjOA5kHTcBSNCwmye4KmIncHvJLGX1YxtYcrKxWXT0IjB6WfJawNlQoGjUDXDQ==", "signatures": [{"sig": "MEYCIQCbCmWX6RUtOsNOPFcraH4m/ngLlt+jYrlq2D3LylXqvAIhAPJlxwVzR42IYgeLbbJHEpUAyKdqkQDVfoGyoO24xP/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66453, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAB+bCRA9TVsSAnZWagAA7WMP/AvsREwck9+UjIVXOcH9\nV8dtwFTMplPSQa5LYUiU1GK4femfzdzKBezIq3xvzGjzutnVJC9faD1wKsJB\ndXmQAkB4Dg05xZkD8c25jUZu6ecNxrYa+xgqffzhMp/G6fNThI5Rwv4CQ6qY\npK4TvSXMumIVLUA+Ib2aSMpgB0/AK7VCrr/ClJck0Q9QE1Jb0YOLd/FUqu+V\ncoNmZXCEO+82+zQi5KVVESjwnxlVuHWtFlSbP+IIDDhAMS4iTZcMeKe9kDub\nmCQLT+KLnY+TBCFXF3Ypr3N1JriyMIf2yQjMlEg7JDn8qK8dfHNKAA037dnm\nZ6kGXEaepYQtMtlx0MbLe4IBhqQnJuPVJPio/n+q3eWoOEHGwqyfLKoIQehW\nkH+D3KH4YkzbUYGI2Cvh3ehPu/dZPyK3/sHhw2RmG649rDlUBgJjr1/W4Wjd\nrEUv3/fWU8YN/DPW3HrpfTGHZTPmZb9E+hQSAJUvN3HYwwahFV9V0kCMOlj6\nKJl/4kQR4MrCZ+WMeXA7DRCUn28xyWyzRtOhKbtcM2EfIOjn7Dn8ik5gpFfQ\nlWEuR4OvoWiJTmpsgEIcmVOfAoBAtrGjiTqOKebz1Z29i/x7vbBnWQjir3oL\nit6+u57+6uLWRofcjydj8DO2WpMuqrLkT1COPfWRKRRg57rTruURJwYzj9XX\nPqlE\r\n=EbEE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.0": {"name": "embla-carousel", "version": "0.8.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4e59298393bc4e43aa9ed7ea1ed6364261f3f950", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.0.tgz", "fileCount": 26, "integrity": "sha512-KwMOyTa/2HXDMd3k+yopK+2HMT7crxJqE5vjRJDM5BOEs/RF7XM0bumVcrYEOVgG26iKo9mD4uh8VMYQamxZNg==", "signatures": [{"sig": "MEUCIQCBmzrjy/QCI2Ejk591aK6ed+wezJyTUGJvxFq9Z+TMXAIgd7gvpCiAJKGPi2NRrWa834AhEr2qdgTmlo9+i/qGMJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAoO1CRA9TVsSAnZWagAAJMUP/i7celNLT1FbPN+qbKmv\nxHbGsXky6Yq6bcDE6B+k0s5SOI8aIacuGgX/oiRTIQtnOieCPaW6RF4fMk/7\nVUF5D/2PX/CjdgchZF3dmUW0bgVmpGn0FYLqZMLwumY/aW5jKPRirRKj68It\n7g8mi+LswfMTdZ37TNCwJLX6X31E/MWs5XwT6PbrZQ9skCGREYtXGpn3kpPz\nMLgRV54ZM0ya7xfu8iWzQYBQAEMzubCUQM+n281QO9WezmOWY5D9iudo9rx+\n5xYYrPVVU6UEEITr1WFmKFLOWadB/i617RtE3fayRrGGyMjGSYsMMXDAZjl0\nWyrPr2DrCdDoOXeiMUqPACtiKm7XRnaADT4oYciAfphGKBUAsl7v11Ufptl4\nbcfdWwg0LE4Rlj61cLem7oZQutVWwZre2J6IXMiVNUNkDrwAVaVcj/JsFR9T\n41jfUqeh+Uli0oV28X6lvAdiMWn+ML9mw+4IM+7rI5Wy/MPmTgJQOUVAssQF\nfKtMWfiPtcthqzot55BeLnmHjWssc4sZHrTCQIvHNGHO7kze/x3kLZDLfy8E\n0XLic8NMTIqCzx2eu6vkSn6Oc5nF6hVHeOrxevUAmqT9jaNiVkqbXdAesJjZ\nEgit36ezQbJnM4NbHVtU+Ns8Z0KsuHUawohFvgaNga6oBf+OhoKENXUwkNz4\nR6gq\r\n=gJqw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.1": {"name": "embla-carousel", "version": "0.8.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "e15e0dd304ebc026399f72037be1c5472b9ed999", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.1.tgz", "fileCount": 26, "integrity": "sha512-LEKxZj11YttjGHh3eWCn/8lgSz4Z3fbfcgcxKcl/khjpAPUgn5j3UesLW3KMlQbFUwgXlvTRlWMytPWNXFBrAA==", "signatures": [{"sig": "MEQCIHx4s97d9/OqwEQ9eSOZNER/76Q6bVM1Ku6xLVHWahjmAiBwwhrExZO3JMsWWAEzJOEBrpDKJcAcOns+mwzGoFKGSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdArGTCRA9TVsSAnZWagAAy/wP+weE+XJxWrlYFKWgWZWq\nzjOXJ7w4KRg8Acd7ynBVfyRJWH+FMXIsod5ydmxo1a5dZ+1/EwZ3MqrqU1Vw\nYcsjg1SIa+Pi2g5RzqK8RS9SKHN8+z9+Stmk33D3myE3BSUZPt5aEMYtvCfL\ngY70n3JrQfWkIhuykQieBCEJSwyeX7sMUnpKRN7QY57Z7AzlJLGO2l0rMEkY\n6iZTjVTVaPr1QbHIds0ZzJQ9WMuAcFh9wPi1VgppUPc/pZhjjObVlHJ5WTdW\nwrkSp9g7mNRlw9ptgCLglCv0SpAgCIe5rpODNhDqcX8vEER063b0moMoVNX7\nxd3zssLXE4CkstKHq3A1BVE35Q5gHR+gX1CYnWN4Vx3myFvwXbBYmPi/Ipir\ndnWNCWGsXfX4Kc7wB7nFtk962e9TIX566pj32Aog2pSEFuT+YBC/f2f1d4je\nD7Wt8wj/VqYmie9eKAndv1hs8MdrYEd2ivbwOCAed93joTMMXHMdQQdHU7kQ\npjSjGUnND9htWpKQFoEvIJqxxRjagh/zftgFFYEuupTGxkLY71lUTQueVduX\nprYS+MYZ+A5nF3jMBbbxxrYqZX2YtB7nowT+ags/ZiOIwx/FD/o2ijOIEhdv\ngKncBcmyu/WDY2/a2RAU+nJNR0+ruy2zEC/HcRZ/I5cJZbMnCucrwAR03FBl\njhko\r\n=vSA9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.2": {"name": "embla-carousel", "version": "0.8.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "49848a4376435026e0bb8066a475ac791300e482", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.2.tgz", "fileCount": 26, "integrity": "sha512-a+NyqNgKFw/VJvuchtaZUj3QWiGHY9ibLImcZuyQuDWfmMoWYiA4nggju8uT1VKnt1S/4EaPifp8ZZj3dzy63Q==", "signatures": [{"sig": "MEQCIGnlt8+Kn/ieB7XoD+BC0caVRMt81h7ZH0iEbvo5uweMAiAhkZopb87kzbdlfIciEmLjL2NScZcY7P0EMFEs7j/lpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdArcSCRA9TVsSAnZWagAA48gP/0kRWcToFe6FII0mPkF1\nemT5ghzx5eBb0hd9xZ5L47q9j7ZTyrTuN+vCzmY4o4vmVdeKnW8ekCIOQ4p/\n9ZgWBVklSAl3hI9KoPgo3JwZyFZCxuNNcENdvvjugMZfTimtFkzMvlvK4Dfz\nr2vhxi90CJFrB4QIwoOOATNH6F/FQqGxjY+jWK8t7l98tlALtjlFvtdaMaMo\nhUu6E57MzIYiGW80QjnT7bMdRlHPx75DOZFZYA6hIKPiqxOxNf2Y3adNa5ql\nGptk6NnK+eV+m6JuR3nTjL+yMvVasBmcHRiH2AdDxtDDxgM2rfa9KGyUEnLT\nFo55Uk/1YClcnuZwXmIbRspDYwt8m30IEAxBQFGMB74R/BhIOr9+qsUJW6RP\nbFPTHfnhRDiDXVOAQ0uB/7YCEs/Y0/PaQ0xtUkfxBbxNmNE0AUeA6ziF9hCm\nwzVtD9wLVZVAqlnXaipFWq5bEKkYLMNG29S4JGwXjmCpsQZ4+C9L/k5Xv8GQ\ntWxmmK8eb+AO3HwO0BGm+RqP1BohaAhkYSCndcTliNHJv2y6woZ57Q7Jq/IP\nHSO3d4LVkegRJ7PCEdrWH3uXyddub86pZJKh4/zLkgKtSrzqRqta76z8DM1U\nUf0Vy0p6IVhXHAYiNpfn8wiNBdx+5UUW4hrQ7Y1pX23VVhIDgDYw/5OrebXP\nYGMl\r\n=sDcW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.3": {"name": "embla-carousel", "version": "0.8.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b7a5f3105edc0e9f512911b3c8c0112efd4a5b55", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.3.tgz", "fileCount": 26, "integrity": "sha512-hmd++Oi1X6thBnCy9tI5msw8A4T9kUxC5kJQqYOy4IAozBC1I+jovM/QGeFVXpoaMmnmuiwe6aSris6rvvSLEA==", "signatures": [{"sig": "MEUCIADqpQA6cXygF99PMm3TjTL0Eh4qm1thQPkjeohSRIHNAiEAzUBfs4vzv6ni+hO51/1wpqomJP7BWGDOHCPLGdwvhrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdArljCRA9TVsSAnZWagAAv94P/jz2lJtNLFK1h/I1rPu7\nIY1dxGa1xu5n20jBDz/kHchSmEHJ059y4g6AECIhp1IcVP//UA5oTwZNSjhD\ncaIju2ZkIypjqbTCPDbVKel0fXCWoxiPt4jaMGHHG09a5RWvGCtOqBZ7ZS4e\nuQ3L8074XJVSapv3jxBnmpa8m/Q/8RtuP4oUYeNETOF7SRSqfcvu/bC3noly\njRGi5PfK5HlnINdn4YT+exbCfY+jyLfM6H3X/b6XnjF/zGm60Ilvpul0Pvh2\nlxe52u6jCMPlTNP099WA7/US8VxbaZmNtb79ETVmMPsi2JQXPDdydW069Qlc\nhcg26ie2j4B1STCLW4/8PZFoDkxf5LiEzAFbxKt/kxlrHr7TQfSc1MlTwStK\nPUGFyMfF2vjQApve/CGRUULxjbfKsHMpYsJLr9ov+ELOmKqYWEFIphtip1nH\nVExgPYc0/9PsgpJph2z4sovqt9e5VnlX9bMcmKKiuDFI3R6wU+7+jkKehYy5\nCfcPTQNOvy2NrjhPe+truKVOVMERFoXvYYoWBeYjIHmTaSPbW5BdpqpZvSPr\nVlBxGHVZqhhRk8c3VMeb7WWCN+DynldtREz+zLbdvzbg9i7JGtlJ7CPqSZqC\nJFmfbFiqdZgQ/BLlBn9nKFoRCHwvKHPOYfSlzzoaGtYrdv4BRSRCE47qQ4w5\ntuV9\r\n=aPWt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.4": {"name": "embla-carousel", "version": "0.8.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f18aeec61007f7c6de2c58246f3b18b0a1d9e81d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.4.tgz", "fileCount": 26, "integrity": "sha512-MTJAhwPsegNYvi1cHUYoxP8F8P4IGvih/SLF5eARipTyH6p+MqPmilxiW4tAKWfvWMg10bYcthWzZmyTU27EKg==", "signatures": [{"sig": "MEQCIHAj6tr7v8lm7ccSYCBmef2fZL6akd2wOPKdXncNbihOAiBKEYZfhXbq1jfWXtlN/3NtBr2vq+MQQoREamGJWrzJ6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdArygCRA9TVsSAnZWagAAbCYP/R6ZzF46s6KmCtAesPv6\nbLSPkYw3m8Fx99j78n49H9pCYN5hz6eXWGNzuTLVeOTt67A/30ENtg4kLwwP\n9w/s46A1a/7y2AdlTRzQZyXrHUt8VRW/t3eTx4pNhqP8JOKV21eZLKTK2OXS\nF6lCKB/AeEZ0QqNf4xz157JrPt+FcQlOheix7zyLzmVG2d4s5Z1av72AM5vc\n+91OgiM1euI3v6AtuN41aybSvMGq7G2Pr4pG3egvMp+y0I3XlnhozUyAuD4Q\niY2m4xDfCCbJyFAPpf12crudKhFat1jRehRjH5OHGoikD+JfB4Z9ZiDMV0Dz\n6CMM6zw71UhaCkEsScqvwqOBOcD7eoGApo7ARQmfJ4PRyjRp7EpwPS3Sy7ne\nUVZdWoYtxH0NTnwxqeoZRFcW75JeWJZpPsKbM2lXgS53gaY10/097m/TSWsp\ndpntYF40Rxar6KCJ1ve2GjMwUN4uJpGNavE+5+NzxVvqSMhx57BssUCNri+a\nmah4/LzrZiShlxSXeB+jwmWTTpWoIsekjTp9fOpTpgXO/Oc5qaiZVLW1VSCl\n8NloimOCFQRAzI6vA1c/EkTY7H65eGD+/xQlIHpR1IIyZQd2ti5nQ+0Dqn4c\nCd5af25lf67bTffpYrZXtSXLJGvJfLmnlBakp+Ea71QprYQwZNnlNLELPfqD\nqZ7w\r\n=fZ+W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.5": {"name": "embla-carousel", "version": "0.8.5", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4a7ed731b6eaf3b347ec5f44d30ddabc3b81b3d6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.5.tgz", "fileCount": 26, "integrity": "sha512-ti+6PFb+hBRqVhWdeQKfg0TtPDKw8dVUN0LP0tUs0RuvlRpDcy6xrr43ktF/M6FWOp2xPtHUuTjdqIFDrwNQjg==", "signatures": [{"sig": "MEQCIHMbfeWcsBEHQSj10G+pBqITz2M7ubU5J+f+Huq4XLH/AiBi9nZAFPx35ojsd35KY10xHXU9SL+dziM+/NdDbDMbuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66914, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBC5ECRA9TVsSAnZWagAAbFcP/1myvg+56WKUAOcIEXTL\nKBmhgE1kymyHOoSUuQ21EI3xi+5dnGRVOa09dSsm7NUT9+ovQ9l+HTSz1q7P\nSfNgP1uZTpzsMOcmvx0t6IfNb+NVzXCmN2bzchUSUrhgJUMGiME1lC7dyWvL\n0E2LB3cQDKeKB4NuV5twxDwQcoA9YF6TEbzUC0+5LldZd7P/JjJl/ADBIPZN\nKGq7comGRT2gL6WM31i02mXNlyHOedRpkz/YxzbCzz3CJU9z4vuHyfxC0Vh2\nIl7af2AucxmXNb5pSQAn21a7C64IGUdmP9GkK3fsrDotpWMs6p/d8lTDXwwl\nOJtauRBPndoVlVummydUVRG/7gNremLEveAEhj/t/R6IzHBjECd8AP9sGJ88\nepAVBX8SYNP0+gXwyEqYUl3+lataRl/noV3Fv+GA2LpWg1YvuCT0UEhShdPZ\n+ZXZSZ1vq0uoiSssz3natBdSvjHurv14wFyoYFPgt7BxL+6JdvxiRPVfzyRy\nGxeIloPrxoYr5tIzrXTjP64gpBNSwJ3EvR7q3723XbFTVYhZgslzu6Vcxxk1\nw5FVDcCOQWmVbSXfMfBaEUmiQm3Dfu5LnCklhIyh5huvRBJ3u6/c3gu3c6sS\nI0okzD06bHS+SbiKQavX0YVEogFDv3V3URh7HfY0LZlSvLhir6R7XbROyXQ+\n6Dti\r\n=n4h1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.8.6": {"name": "embla-carousel", "version": "0.8.6", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "3dd7ecd4dbc6835f90c063e95c3aa06826744a36", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.8.6.tgz", "fileCount": 26, "integrity": "sha512-a1XrGphi/jbRnLEYK027ClnT/psMUkoqmMLQLsbP25glFCylKCoFGDIfbFzkMf/CRaWRVy8eYdoClTHVar3oVg==", "signatures": [{"sig": "MEUCIQCuckEnRns/8QqLWV6qX3IhcyK19mkWbTLdVeDVFqZ16gIgMcoCYSSHeEaDHzhNEpeOkICC+/w1aWsy/LDoKqZlBTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBDPkCRA9TVsSAnZWagAA/l4QAI1Bu1fdBB0m9EKubpUf\nAa301fd0D9zhYVCoGAVH9B+wD2Ovd5x2mCN5/uCQAaCVO3aI/r7Yj8aEXAMf\n2TvDxXR5Tg76p9XPAhWo+QlOpSiC+sXuPh/j8Eq8W/fAA0BJAhocANV7sL4D\nIkkAoD3uJxcHeR+1Iqq0itvv+2GhNFi+JpbiEwyo8XGKMAVhrGanieLokeKq\nJ3RSMbiRPsWslRGBqR58FLyt6znTw/9XTwe1trGyjZM+vKfdTCSQV7Iros3d\nR7FP6EYjI/p5DolHxQREX2cbfv2hyAJHvhILZjYSse2ivOZ9SFZtsYfWOaG9\n8Fovyl+bAEgXDzM4F1Y5wnOkz82PmDpJ8jPG+q3H3vEIMwVbXQeqIJUM0PhL\nscDGunCNLtBalTssnPn77R9rQBN2rp3Li92uBNlEjwtFU31NBD2LiUd+zkbb\nZgHkjJ588CkJNNMeHOYY3pHUcYYiLZtmNAcbeGQqcjPKhfFbEBraaYrjXj/T\n/6rW4vT6E27AU0Ym4P6cFLh7hkL0KFz1iSbSIim/4FOcodTkFCWFoEdS7gI7\nLaipsxw5lqugq6Gc8KKtrci+PUtp/qUKMn1EpL22S/4hVfuh/Rkj47p8Ri8i\niqWXxt4XLoRoN/Dj1GGzPdIFXQg+T7VT5LZS768rtNgD919vfsTTeK0SHldK\ntfRq\r\n=3dRs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.0": {"name": "embla-carousel", "version": "0.9.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "40cd8e95631736df6ca33f0e095c847d85389e44", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.9.0.tgz", "fileCount": 26, "integrity": "sha512-chfzIQhGGY7+tFmZE+B7gWTwnkQ8CUcuHLsT+3FNrneWuf/eABOAa/+2qR7ftdGrmJCWZP1tP5PXTpsCSWiWVA==", "signatures": [{"sig": "MEUCIBFevp714kdowrNldFqKKGgpqRr6oCh/SYTuAbsG7vrRAiEAi7zvID5KgTSzCdRlOMFPKnsRL4QFfvoJ7XVF5J7p3Ss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68241, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBkniCRA9TVsSAnZWagAAsxMP/1zIlNMMvh9Um/Gif3HD\nlZpqgJ0TeZiDK8Am+gnzRkX4vLxEYpR/+pOSSN+fJUVQ18wvu1LMsfq2d4DZ\nnJEqYI1abPmAoxbS5bIsdmluzuiAtfViH76vOVpTUuSw1YDnRUc9U3MB1NlS\nwN9AeTs0zPepmcKczCsppcksL09DvlnFd07gA75FZWFAqjGGaM3uijjVyAyn\nBc3OsAkP0XD/m0HWowG8cdkSPrzS7UYrOvynj/c2R6IBYF5x0tq/K1xs02YQ\n/aZNo0T9MLPsJG31zm60vJMAQndD4IISB/85HggW55vgXOU7Jr8VOK0Gr8o5\n+IpolzrfLx+xCGUqjjG9BmRKAmvQadPSjrC5OSEwBoVdILKzSSPBkwD0JWSX\nob0a4CPr1LaRVceyey0mt8pvq89Z5tLzdPh64yXRxZFuJfOjRJ+K0S1myTP1\nMJPpIVGIcqx0s3fHU24J1WuksU/KTaSQ3GewGZBtwfbmAuxiuybRTYDZscCF\ncPJXAhJ4GNFb3oPduux/Vj1+abtS8JrkiQujC7+0/FBtl4r3dUqyMruR3JEQ\nhyoU4yShAf4CX7DkyZwlzsm/0HUxKXckXOZ/DRh3L/mDvHTjCR0hZInDcUTk\ntFBP6udsOTIT+BEsdt34KuFTolAfl3SW23yXBARmQ2OyCzSMA8oUG+y+MTKT\neLdg\r\n=b8dv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.1": {"name": "embla-carousel", "version": "0.9.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "465402cc26946bb4beada43bb0b1fdaa80359b39", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.9.1.tgz", "fileCount": 26, "integrity": "sha512-+Hug0mC8kA1s31y980ihnvM+TJ01vxwVdwYlRe6vc0E6siHz0O+CGoCZrUGY5lucsFUVAYbxlQ3VysRTctxT4w==", "signatures": [{"sig": "MEQCIEZHrZ3jX/1zwxcE9m8WMfe/SRBUP1fPveeNOy9qUq3QAiBAAPzT6MnM2dHBwXsE4kYSp0PoBpkTH7ksTsB06WDJNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68318, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBp7GCRA9TVsSAnZWagAA074P/3BLJrI9w7h9WuHrDG5u\nw/6woYyu+bZ+U2bngU8+z58Qt1FfExMtw0hkPmA0iw97Mu72eSvyBhukLQrh\nvtDyUaIYaG7MAHmB9PTtg7HPDj9b4MPSW6oAVzwX5r0SPw3/UBwwBekfYXH9\nLA2fA54jUVwuXyLGlId3vquQaeLyqfgpiDv9JZlhIlhh9n2bk5c+BC4DdMaM\nxOEE5fxBGWlDcU3mbTiL59u9UpbvEqoQxA815gLX4PlGM+hDUVdik7SSxpFQ\nihwsPV16mlPTKo21St0Nyf6ysZw8/DQDel5I1yNJ6DoTRmMD2PORzE/RiD+2\n6m0A0e+baRJtgMg3o9r+uRNXOSmZ6BJvgBTmfF3GSQxQAQaK+niFNPLx0pVv\n9VfsaF30npk4OPbUO4uKS/lghG5Xt+SxQnRlOBzWtnG2jG7IBTKSqkPymVwk\nu6bJM8jJqJbwJV6cSj5DRNM4AouNGWIL4jT+qMB74LYM+ogKFpUxxf2JT5HW\nice5s7y99VtBaAj9IUx21UlmIoi7A0E6C6n4V3okUPVGjlPF3RCQtiFF3XRC\nLAJb8wfEkYmLDjxOkfLiDJ9DMNnPgmBIfS2omK4btl6BywOJUQi2FqCiYCAj\nNjfWhHMmOjui/x1ki2weF8d+zh3wD9Ywra3N4uqi0EdPeDFS2njSsctROlsc\nrwf0\r\n=0z5Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.2": {"name": "embla-carousel", "version": "0.9.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "ca73b9022a54692c89915e3eb50405741efec9e2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.9.2.tgz", "fileCount": 26, "integrity": "sha512-YanEDyghJWFcGA6KLl5mC9Q1aIx7fIKGgGzNrYgn9pOOMjfSiXgAeHyIXibNjZQxqDGJaVN2u7WNhKrCm2dB3A==", "signatures": [{"sig": "MEYCIQC+RmY5KJuL12lxUj01TYMBzxnscFcEYqfLsV+HEhsi2AIhAPS6LGVTTaIxYMyJKslTWIEPzHgOQVe/gB96wXuQNfW1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBp8cCRA9TVsSAnZWagAAyYMP/jgSkfTHHCxO4Om1/WhT\nxguZPRfYxDTTIINjwWlRLWChnVSWe4QVrpy41wghNHUhIjThDHocLGrqilBw\nytL7nzZ4fGHSX5rjSWKmUbnclvWIsCMZ/5bZ8kOFSgyTZWuCMsyJLujHfKXb\neodJyZgbegL+RgzPynROR+iyIGkazssi8Lp5zIxJtcQb9OcvDb2HRv9K1vsk\nH2N3mN00JapQlr6yH5vHNnqIrfgr50M1fOcggdZl7ayN+bOTJhQ68hUd1g/B\nEE3ClYyY0OZ5YF7CYlqHxZH8/GWN6p6Jr3w268cRxehuQY8OA+aaFGgYwyAf\n/l+0EZGBDS4MvqR0MPQsDCLFVw0JfcH3KTHDp976YRFMPgDRV6WGhUZgBLPi\ne7+bdlJEU/GNO5OfK9WZAN3OYhiQZZ1FKX3ncR8ZEtTcffTx/q0mC5mhjfXX\nMhFlVQT03SIV4I0CxNKeHbcKdclvPy+utIg7abh+AMlcLOqxbKm04LrQtbB1\nUk46bUpzR3aRZYhhv9vCLEoViH7HqbbnbLy+/ujCF5yp3mMR38xJqYHNc961\netRQknYTd7dXuVcSl5jSR9k+50mwCldjn4PNskSuDBTVq/BVAND8LZfOiAtE\nYVWceIyUx1aF0rQiwLRXp24c9hOsyRJt+kl8jwLkLn1pGFpWdcuaf5+NKsMh\nyYvW\r\n=kehA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.3": {"name": "embla-carousel", "version": "0.9.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "1eed3d105a29b87c33eb9582863dae9dfd0f7b40", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.9.3.tgz", "fileCount": 26, "integrity": "sha512-4gYmFyEpYdzz3xlShZWjx2shXIjX9r4wdrx4sTSLaFXAWMypiXlKOaGsqmBTOTukG5K/iLKbwjhder3yHzopXQ==", "signatures": [{"sig": "MEUCIQCTmbVn6JfesTW1Ervg3qu89o9jtGLT2htJHDtlYuAQiAIgQM+OK//NRjm5SXYgDBunO8Fh5uxYDDCZO6CLHkOFkZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdBqb8CRA9TVsSAnZWagAAuOYP/3vCFswtO2ifv4RO5sG9\nxn2My/uGqLy1pCC2YaLaHiDuIAqSgLXEecTYlIbKDPQi7ycH/bE11tJqMXiY\n11yokvCW+lnMtllqFfByRcDv1kdhJJpngCrqg4jajdYR9tQRdTLziRsK3dUe\nNJbfe/xitkGl82KykSZM62tEZ7cnSGvHbRoHrLBpc7BLG2cIRFvBIUJCSftK\nFhnIgSS2sFEFff+uifeKh3n68d5GKNlx7Yf1VolirEfo9cZUmUEs1samsQbh\nvesXFPC0V096yfopWvkEJQAgpuPTdIEujZIuij1Uc/iRaEA7YWbyy0UAhiok\nZyA63xIxrUWOatUAtbo03GOMtbY+Q5zQXD7UvUskpDgsAiXmDyBcPsEDV+jx\nBk58sQ2QlywOIPhsXVel9qdWpR3kshEzpMLQEX38UAvJII2uDHU90gM62PHm\nFEDqkLyYK0hT0lA1ooxAxYczUlhJMbk0J84jJYt7yWy3gE07ZSvbspZT/eND\nc2KfBKmZpQ2bnowGXO4R8RmEJRq+48/3hmHMVMa4Z9wiPFJbfzZzvIBPmPlt\nQiD6qbLbzlQlexxfcRUpXyd4kIH09W4CLG5r7k3K/EtlM5+upjGyWQoDurbl\nHum1Zbl/mZkfxzNaF7yxRmhE0sPr+/oSkXLVkDfxQud80/NYnobTWMucPXau\nA/D+\r\n=nJcd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.9.4": {"name": "embla-carousel", "version": "0.9.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a45b3b1f8d659ef14c8cf6dd7fa3795d5e2f412a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-0.9.4.tgz", "fileCount": 26, "integrity": "sha512-mX8QJ8JcC9NsYa3oN5pOAc4RIJCyQ4v+i6leAg+NOzUxk1Nob0vOXeot2EXdwPNZI0E95/X4shjzozOSHPoyRQ==", "signatures": [{"sig": "MEYCIQCF44BLOcwqleVs8N5wohzjF/hXjpq+1d+fMdTJx9Lh6gIhAIZGz5sGpE4X8IXn/bSe03M7FRRd205oB8tBS6/Okqqn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCk01CRA9TVsSAnZWagAA2kUQAJmjBsHG9l94NpTtGfLm\nUAN3APA+9toJu8bXoKyHuNJXIMa1cXSwhm9cZlwiuQ31qa24cLBblLqMBvp5\nQmmS1+FKZIECVXBWohtm8ITJTt3sYZ8PmuEBcR6LJ6CALQFKHkHmLSZ0nAcB\nW+VArlGNUPFB9vcPv3nz8eHieJoUBbTz1w49y4bRIXNOM5jdaOLmQHueQ50a\n+SG6mdUa1swo/OgVIg045LVpnDFXL9vBBQQ57Cmj7HG+gPrZcPRseo89YLk3\n+7PO1ti53PrlS2WwxxciWOMvMnkAvXPuMPcUloNcnc58JmKiCF1+DIWoSJ2y\nfvTPN+1FgJToTQVurqfla9wBMIJT8iBs5VF1bQbVUtOIWkiB68KYcNAHSIj4\nsh+oqM/NI4tOf/N8sKdJCHN2NtkoLE8ze33ncbXWXCRW5cQJljIx9DXHwns0\nmAVtD/Ym+Nh/h2IxmQFWOR4MkLAebCGEpDIQbh7o2em1z7ic6R1PuWzsHHBZ\nU+uiTk9KdDL6JbqQHdLkex/LBZVopp4Oc1JvAHdr5pe4sUzfbV4snUJyZNsp\nNsNXJlwMbBcqU1sd11EVa6syjojTc8E/oCbOPYx8GS8hWRHeZ5jceI4bMKK9\nISDYY+ZM7kEClxSascTPdCYGUis/dS1GJZJWDae/FGNegzVA9M32w42Wbx2z\nq6mG\r\n=gRCz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "embla-carousel", "version": "1.0.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "995be45b04f4d8bed1ecf57714a4fd866deca115", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.0.tgz", "fileCount": 26, "integrity": "sha512-IFzDZVoCLEnItsYukKvcOU1Sspie3gkvVq55GIpoMuPVlLkOneiIiZILAq27g3NZdVUzA1nSRv+wc33BB3wS2A==", "signatures": [{"sig": "MEUCIQDplnJ6YD6pqh/FXxl5ny1dViqnSylS7QaZrS26x1yLWgIgXLkgbdTnpB2tP1eFv0M8GwaGccQ8l66Y6mQbVkbidVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCm2CCRA9TVsSAnZWagAA2l8P/jZY685F4e+D06m5rY7K\noYBDLAAkoveTx9bfKMT0FIeCiQGpOMofwUQJr5pyydQh3EZRrEPGskpxG/yU\nxjy/NVtwaiKOvfnE1lJlH0kKCZhWOkMoqjKi8kpIOItCOjzwKpAoHCxKEG39\nBdiJRyNaJZH13XG16Ix4y0aBNiQ4QYpFbLb+JRbgdZa77ZtRXOuacKa0PsQI\nuk2kDaN/nBF+0yYzrn+JsuCvg0jet4xx369EaWWPfv9vH39suXliPOSsHsxN\nQRUu7ELOluLIpThwUAiIDiqlsbJ0EZcVt7N553tqbnXTW6b+NYvlnaItbvkP\nYDCLgNy9zGklOEV7P7+KiXubt+tO0YOK+ddU/D5VyOTJ/uyw/9JVBUgOjRmO\n9LnEP1GvvFd/73kJEAbfwdieeuW5eGrOlmWhpWZ0wwu4Dj9pBcAEmdzT4rl2\nr8QzBqHAYiTRdrKDzs09dTA9tTqB5wezwlsU6OdY0K/f22lCBlielmt2HG3/\ny4epT45Bj/0/PlnkNO2cQrHpChcjYDVO3WiAApoLoGC8dadQWfHBMvkZw/TU\no5hJl0na0E68GJhNd7+LtKRvBL02toLKzrORQ2h8NsZhRH5aic8joIeIlhv7\ngUf5pR8eNHC1LA8UJDUp1kJ0xwZMl1S4zJwIPCYDChXpEL5TvqmOwpx8QuYL\n5TLK\r\n=63yQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "embla-carousel", "version": "1.0.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "fbf656147ac19c415f9c8bd812223dc84a84f4af", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.1.tgz", "fileCount": 26, "integrity": "sha512-ZATxqvzY3HHssGvY8GTTxeHyIk13r6nWmmED7IbKn8udKJA26rHtAG4qGRzFzUPx1qi3gRdfUFY5OSavcHX41g==", "signatures": [{"sig": "MEUCIDYwEFAppFUjZzXM6Fn9PRVIji6VJuSy8AYPXwrTdzMRAiEAkrqJ3axZqc6WoGLKjdmmwcemrDfWXmaYxdVC7TRpxHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnBFCRA9TVsSAnZWagAAsSsQAJrmwKkd9Sh7QURd74lK\nNwTVaPC4ZeKSS4RbmlmCWUcdEFyyD5aPNvgkmoF+Y+rHL0ZMAvRyImt8ugek\n8yWRdXhB/y9P5UBTVWNdQEaThRgfyJjLfY7XQK3PfyKD/hocgi12iJsiNdti\np+gfNXLnKTtE4Ed7iGUBUCypfugp/zQtFZjVgHOrTKjEzZmwmAmgOxNJS8QB\nfv2Q7fD2HNxzGou1lj9QdghHNr1kw3GtpqRyN0vZ9TdZbGzWu7R++mEigiZv\n8qH2TdJMsH3vYVNwgtvKRwcU/3/DPEwpYI1sfFfDMTNfcQWzZl+HsY2ZaIIV\n6pLDiCrf4wTZ6XzhDlWC4CNHvYAeJLo8/EK19wkmuwC56lpHi1N0qkOW49jW\n8VBYhM6k/rU+OzVJ/4yUUNwlsxGowbA/j3y4XIf3i8jaX31x4sIEJoHe/qc1\ntbLyUFh8iHR1fQ+kpQ0fPU+3zBW6M0av8VhLCno55qJJIdVipZJQWGBhE9UL\nZHpWNXE0sTxeO9HJA8F7OOADcZZHZr9IKoAYXcRVgXclQz0xSEZCgczIUWPY\nHHOWNDDI7B0tCQJd+5ITk4qPSHso+3DPHx92QVToeUoW7vQr4ffMMpxc04yo\nUZ/6HPT6tn6VQlNe3VAnG1BzOMDPTJHHYY5Zu6RbM3F8gMGK4NK9hrzPZ37R\nY7To\r\n=2i4l\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "embla-carousel", "version": "1.0.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a8dc9cf94cba7365b532c06520493d444312ff9e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.2.tgz", "fileCount": 26, "integrity": "sha512-sBpbFYDIDNpzawMywsVjKo+q9EuTUCBTj+IMFHl5owfbaZNzH6Ii1h3P3hFdpq3kHKcm4nswfY26OOOyY4LCUA==", "signatures": [{"sig": "MEUCIQDpO3+a<PERSON><PERSON><PERSON>tzHimT0GrwI7KHS64dKqW4BLLyQZUdPmQIgKp0QffEDQ67eGeetKb52S05JqYtCYs2cuyzsgRL6uLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnH0CRA9TVsSAnZWagAAIAcP/iazBqyj14fZ+1W1goEi\nmUztcpTIGT7w0x3JhBE81PXuv6jMtr5xjSlcdWsSxG3uAfDfyDObqwev5Pwa\nPu9Rt8R2y138CLuRmuYNIjCTYVI01uATeUolwaPsCSX8HEmWf/0CW/H29QFa\n77j9980qRVEdUhdZAURd04Ylg3wzizwOfVFQwC3WoyPdc91T6pRCF2PQ+17g\nIcq6pOtmD683OnQtGGhbzfHMSfogjuO/b+zJyxCseXrv82tsR6+u0GbbsQ3a\nvfQj1G2dEFvp+Dc4RlHNq7W/G43IfzG2iWkGsh2BWz6K4v8yRhwLVJoIXnTJ\nySc6LnBwjXEb9CSSbMaGP2tW+LFay7l3/wJh9zB/ITlTTfhj8QM+YtjDLi01\nWgMB7tEB6DWJ26+zU0bt3e4HFInNlNwsoHGesQD05BomyiXWHaiGrmEuiMd7\n+I0M6BcwaoWdR1GkIkoF25LVQFBYhQlv9othgmTYAfuTkt0tLldyCRDa10J2\nSxyW4lqfO1DhSlGedkiX57PoGuZZZhtSJaXJXogyxBrNDq+pdf4PW7ouQ9hO\nrJcutKbxPfDJFXb2ImLdHup4bGZe7E5rEcSgKaFsYMCwdOmWReg94uT4PyVc\n7YSBuRYF9XeYc0cerMeQAMhVD/P2Fm+w4Ymmxs4wBKxF+FVcNWe+gvczULQg\nnGdU\r\n=RrPV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "embla-carousel", "version": "1.0.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "5f591e8410b686430ac93ff8ea276c9c85bda4a3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.3.tgz", "fileCount": 26, "integrity": "sha512-AYZ4qihJXxQ+YW1cY/j7nDaWwvsqipnvfXPBr4qzGxw7Lj9MSTFm8MvD+AkjovqV93u3DB3Yp+mwpOxrC/D2iw==", "signatures": [{"sig": "MEYCIQC5jz2Hle+K0zAjXIbT/ocYSJER7jqu0vHPl1vgO8/AaAIhAO9JT+vOyQ2bUcgdK/WDvm6juiy8czbBZQtGb23frwDQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69259, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnNPCRA9TVsSAnZWagAAIREQAJJ4D4F6CAkhGGrHpAyX\n9HXZ8cL/lbPLYAKklD7ww2to8WgmGx5gG5AMHNQcERoO0SVFm63mfVsXiMYa\n/V8MgJMTenwbw3h19Fc9jMdqzNlISYUrbovrePu8USU61nyGyo+5hm3FSwb0\nwEZ7XfZiLwYlIKF0scF5nQM6t256DQLErjXZ2Ca9ObHwZavDcbIP3Jh/zIDl\nk3MIMxm0JLoJEyMO19q3x3rQvQM61ApWhKzQC+XzsDUEvLwRdnQk7Vfumw8K\nCAFSRZnZ3nHwwpPMqjo5N2YkOyqcKKZOYfabt6Sku92Drsd0CqOlwSIVhQVQ\ng3IUn7OZ4WdkFxll+cG7t10NO/Mu65grRaydFOXOdYuYxh+jZAWHECOjsKzl\nSbYvaY4k0brOk0ul3oJZfcEvN7lHbQRBI8swCsWnD/KoyWtR3NpLVWVn4rPH\niicOxU4xrZU5T4VuNm6XqcQ/PZ7/zJo6zqXxjKRFg9NBPmqQ8iZNm4DT5wXr\nCSOUgRd6DewQwI4M/JeKBbiU9OIkfriCA7zZdA0tyKWL9pBhjWvtGlSQbis6\nMoRbxmPy8X/mIjmwwzgH7F34lMOwQAuzvazfJ5UrrOCSNaCtPkppyBURp746\nxuWhb1bSQ8MZtEK43hduTCgzR/eYGMBc5S35NxUMTeEd0h63tssaLnX6rAtL\nciyL\r\n=3N2E\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4": {"name": "embla-carousel", "version": "1.0.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4cdcad630c73f03e18e611075598587801370e4c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.4.tgz", "fileCount": 26, "integrity": "sha512-ZN8SnB+hN2mSDVuaIMnGdjVXHo+49/PBNx+Hd8TYfLkc1jSmk5bD3LS5IoYid0s1xa3nMcU3gg0jLHdwwSWGVw==", "signatures": [{"sig": "MEYCIQDjPwOIy6O4dZ9KK7R2SAhrOvY/e/op2qgmgWTNjY3j2QIhAMEcMgzM9Et3O9zxQ/8gqp5frqwuq5Vmg5gd8D3Rz7w4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnezCRA9TVsSAnZWagAA5QoP/1JDqZv8s4OecjfIA8Dy\nXedRutXBGcWszXVnf8Hy7fm4GOkIBOZMeVIZYJ1Yxm2jJHn/qjyNzcLa9HpK\nLMzYpyy0fybDPUFijX2HdcGINLCeZjrD8M6ZIkjrCwzOGHAiAd7eq4i3kbZ/\ndFEAclwNL51tOFrDhowkxXQ23Gpa1ibHV1gnbSsmGd2IdF+xHLjuVN+P2XoK\nuAQKyNuM3vpt88Wil8zgi6Bb3JcR3XgkdkMeRc0iKcCdkLJEIXBwXOoJtCr3\nSrXlVRjI6FymVdryWRGuERB1yVHaa8rBV4jwX0d4/vDqBJKLR/KcNe+8/0Kn\nnmpTmrOFnVQfDc811FvQ8IQ36nywTokG2gPa695LLtk1u6JLOe3syRDu8BZm\nIUCbiFkxAuWv03CWCC3+szlOObKrCZGV+mt140e7xa5DiOn4FqydAKqpqz5V\noKlYY2cOR6qsSL+/p2BH0CjUgzu5ScyHz+54lIB4uGUV3u+uKH3j68bHvNxp\naiXZGemyo3cnOtUPPp7KDolfKa+j3ZyBRvUbyUp0GZltcNTKEQSvU5nZCVTZ\nGI5wR4HtodSlkRl4lj3WMLsLNQebXvZvgTt7ZGHrkACri0ayp1o4iaiUnu+E\n1XKz6Mv4YUF4eNkQo7IScE/AjFsRXJF2B0FteL4b+sUi2OHLBjXf5WkD1JPF\nl/KT\r\n=oQRY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.5": {"name": "embla-carousel", "version": "1.0.5", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "3e8ceb569fd879dac7c575d132c7d4d2f7f41943", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.5.tgz", "fileCount": 26, "integrity": "sha512-/6/W3Ldnz1OvHffFia+HgCgnMb6xayshOhZsJ+fGl3bynCL/yq/yuBR8OYvVM2Lsu1gR9Wt4c7wDlZGIJyAcOQ==", "signatures": [{"sig": "MEQCIH3J3749iFm+ipe2qu1NP7iijeqzgLPTV7xDMYZTUw4QAiASHIYD/1dEg+2+8St+pgeqoLAyHDGhAnCSpn/wSlAYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnjmCRA9TVsSAnZWagAAeZwP/0+UEx24qGk68x9P3YWd\nu4M6bU9ArBNmMpyRlFSEpjI/u/LB46iHcYSwvST1I/CohbIkrp54t9VvTdlQ\n+JT3BIvao5K4fYPQLy1s37D27wJ+5YOFzSl2LC8F+YX+zYdOz+wf5l0GVo6Y\nUunNHcM+eHjW5rsCGH/H2hpX1BLdUtxtU3CXzDzdbzb45fQlLLeGDfrQOo0Z\nI5WPBUAf4v6fyGQVsuiPHwN19VxnykL9wLK/QPkhyeaa3FykwS8ZZLj0hk0k\npOJWIii9+/xOucc+GvjViW6+ayFpPvhR6Y8CCNadE81S5pifqtwEnEJRh9io\n0jb5mWcfEKBgqi8GOsZxU9oMvdCvke8B3jZTuORu31OooSRi0OrmjF17W1p9\n0EFYQwvZM5oCmXof8ukn8PeYmGKp0M+d3V4eiVUporllHMTOMz0jKqtHNWOc\nnklCuYKJezDm/RxwmwwNc2FM6cNCzmcUC35Xfl2oxMJO30iLt+XvgufGuYeS\n2iu2sJmW4CmwwjwyQciY0nmUj0LpFj6t3UE1Auqc6kHJdc7mH6BvJUB9sS+f\nuUGaE+BX5sJXOvDjwy4wD0t/7n47BeQRkoMA5tk3rX0vZUmtHUJ5nj8X/swt\nI1Hnl6ZRp+5pV63TT8trD2jDQo733AHaFAaVp5iwhNMzEAmVZjgM+D8BqdCL\nlOpG\r\n=6I6v\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.6": {"name": "embla-carousel", "version": "1.0.6", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "23b3f4a54d103a3552da9e6b1abbc6a10199bef8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.6.tgz", "fileCount": 26, "integrity": "sha512-gk5HrmAcHrj362SKJarmAhgQf1VNTWDyzgnmFbC0gJoRBbr5nXsiDHczjPcxY2k/auAbJu2ShgQu9eWxOUZ7ew==", "signatures": [{"sig": "MEYCIQDBLTgSm1vtsrHzKiMgHJ/w2ps5HdOqU39DWSe96Z4D2AIhAPUWLKTdlkCoed74tHfnmZf0dzpwOX6t0cvU4b/lR0Op", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnl7CRA9TVsSAnZWagAAGIAP/jPFTaJcshzMYJ/3S8iS\n56JKPhoPdLMQK2OoYwLdam99YyTTcpPj6J2V/lnKPfP6nHpD89r3K1JeCOJF\nmwoSzzlq6X22tccSivcBW8Yw8KyrvIgRrSPVPxFOqk6GoQ+ZUNVpF2Fu9/7y\ndMw/aGBm1/gc+26cHkSgk3Lp5702NE2wNJPCiEPaxEw75IEPM79Ou1vsaMyX\nRfrZwkz4xxZZXKbpPCPtQc7hUMy2e5zBKDKezr5xVFxZT5/ssfmCew1APMsq\nd40J/PXFaqdwMvLW4WQ27VIP7oNSfDcjUNEHvwNcoxVHLRfDdxgP4kSA6vsV\nRKVejgKI7XF8Y8CudnQY4cQpAs5mg5wjUsFJMUJF06L5AShWxzMGFHcdytVT\nno2ne6SwhWsB0iOcbSvqdtixGYnoHj6+6L4yVzJRHADvePIK8iKk3jIW5rbF\nY8JxnEX1pb/ipeW4BeSK2zwR2Vigttw6KnqM8CLZfUaUrNOJA7EJAxFsheWW\nwjckAZKviDFdvDEN/AfPK1Gu9dJ92fjhOTMcAcCtuLAgvXwHM77I7374eEzd\nc5gwdL4VxkFCUC/txq2Byi8m8rB8On4G53UdQgR+STeSJvfu4UcaifrY8TfW\nNpxdMGJdWMw1x0bYlLPEJP6Ux57DSBuksTRMoO9ONlyllRgx4MBQnTFEZGVz\nt9PP\r\n=TDG4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.7": {"name": "embla-carousel", "version": "1.0.7", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "feb9fcfefc660151ace7b292ca542dae724af7c7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.7.tgz", "fileCount": 26, "integrity": "sha512-zCxubYfwS9GSTYcPjmt/hQoteKxzffHr8ETxk3Ec99oRmpJt0hejcRgcsDh/4EnOiGMQWtaesVuHdnxQm4/MYQ==", "signatures": [{"sig": "MEYCIQCUUsN+x1c9eBnRS9Cq5uR3TJ+nDyOIuZR3a55e0vXwrQIhAM4Vd5sS49fEsk95wvGb2mhZwR2wP0Q9WYpCttYXGS3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCnnpCRA9TVsSAnZWagAAwEsP/0FjZZGdiqxjW4AuK0Ia\nBR/X04QxCame9rYRs90WLqAFMplznI3q4AWmZGsYx4T3JRUecz79ieGCrUoR\nalrMCS87AsXynS9TkZcDeNo99c+QoCPX5c0rNlRv046nwEwVHVnJ5p1wYYUE\nZprNEXkfwQ0zwN8OZsZdK8FvhHvD7PVqojRzMY+ygU5jaSlR1RFN9MC17PQz\nAjBEXn1DkVkqDd80ggy2ifrroXW1/B+U1KTNIuEOsuFzTevGHsnYZ/wgSk2f\nxK7C/Tn2kEoexAllU2uiA+M6GmG/+zZl2vSpdxlZEW6Bg+nwHMCLYpXfta1a\nyiTSEfuaIzdC/C8i46LQTF+iL/Qso6l2CY4d4nSoTfqLgk8LIm8ngX5nH52A\nmB4StJqbArmY/Ajl9CbxKl3w151pJilVVV7F+D3n5qUz31bvZxgvruIAOFD2\nMNrh9Rt1JLzx5vmUl1Yh2MOmA8HubLro98nM8/SYDh20zxFqdLxjlkgz2mPr\nAgwwsm+Tg2QLwIQlzjB0KBicaKD30mdpT/VjNJGksbyor43kvImSqujlJMx7\nLxwWC6knlgVl6XHhgy57Hli8iuu2SO2BA2IW0jisfirN/4O2dHnBS52VhsY2\nYPtL4R8ot1wJDVBgYuLp2VvCPfEyuamCYcE9tpLbYp5a1fT2U6eqzfMGrJK2\ngd7a\r\n=8Ac1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.8": {"name": "embla-carousel", "version": "1.0.8", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "d917d5b47585237267d5df9e332efe684c47d664", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.8.tgz", "fileCount": 26, "integrity": "sha512-AN7Zp5wHWxzoBx1jxC3vLf0PZUU/FMBF6/xTF/giRKCOUjAG1r2nLFzTl+9X3KGZT6yzNCXmgMRatm8PSvVgfw==", "signatures": [{"sig": "MEUCIE6Kn7olBa7G0lPYLictOXTjh6Ql3S4UMAcgZSAC08AgAiEA9xX37LN81Ec7+vDsFfPXsXIYeahYcbQ7rwqKaYBT42s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCn+rCRA9TVsSAnZWagAAFr4P+QDH4TRr9Shg9Q3IUt1L\nfBh3R7YxgEB/E6RNu9gT00fmlVBaPLxztRaoLsgTD12ViP1wgubLxhs+4XVx\nrtSYPrBpl+a0EO4yUj38AZubpfn2wi1fDelZCkYYXpiyLJve9tpKD0P5r0Yy\n/Jb0gr1CSpKGNmLfZyilqsjcw0dKSueYQ/bw2ywhdWCoh4Txc4Go5Vwu1rkG\nGlrpfRuU8GkSSshck+LlZmu8z4Uiha7FGr5l0bHJ2n/VChdUNdr2LRuC4tH/\nMPGI2MeHH80FHGg28gWSzNtvSfSmlSPfZlkOdJuIzXX51wriHgsxz9u+l5D0\n75GlEMA6Y/v0VRZCMcwULcsp5HGD6/Cs4h1hRYUvgWJ6YsWLpAQhK5O1InBh\nfUQ3AGHLM1ubNCjkpF4sHMOB68w5GDbY+kOZi9AZxTGDjrEt2J58vwzSqhdw\nggq5eLRAV5QjW2xRpUijQHRJAB0DpEk/q90R4Xju/vwNHvGTbp+W3YPHAT98\nUwHUWkVCrzaUJ2eqEg84Tf7loHU141AOVuGlRO7bT8gPC/azzHdEeWPzTooT\n3QO+0AaCkTwdEYVtcmEBao7Sjl0Vhsxfyz1aQN/7VgCJ00Um990BcKlAvtQ7\ni2F5IPG0F7YT+B/1K4c6X1IR0VIIIEdtwj75YT15H3tMvW2IVOqgH2kdK4a0\n9M1f\r\n=oMXj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.9": {"name": "embla-carousel", "version": "1.0.9", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "62b7b943346688958694f496a5390975a775ba20", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-1.0.9.tgz", "fileCount": 26, "integrity": "sha512-Ki/VU64x+KWq4h7ZkTNZo4qW2K8SP4uRMF4Ax0WhT3bGsWPUhP0J15go8vmOmZa43t/sh2eFWBlfVhfubJs9Ew==", "signatures": [{"sig": "MEUCIFHdhYWAajECOfF4Q5k8LSbCeWC0muN2WGRkMeIJFPdoAiEAjRqVC1Zp3JWQA3ddankDrUSabC9IfBxVPpoLjXBiUnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCrraCRA9TVsSAnZWagAA1tYP/RrwkmSH3WmeJSn5fKM+\nEmAQ5vKZtFz64O/dDyR8BAMG5JdpT2n2DHeoDu6EwzYYrFPWdOHo6My63C8b\nfS3WYdmEcxQws7Wy++gykITJKLt9wpVDo03HKW185G/264VwRXiKoQ2apiaC\nbm1l47DcHBWq4/SdS4173UE9UHF0pl9DtVDQczn7zTKLFFpEVuikHbqEGNFY\nNdQl2HHM06ZFHg/+SGbQMarjwXwdLwXTg7Kxlf3s9XOMawPor4YvuX0aLEoG\nkNmuP1GzUXGKqlKC2VBOAoxvqFdVVjjQaO9FoH6LQ/5fcksVdtclZ2M1aPq2\nyJn+38Bc4bT0u/7u1NmsEQkcYsvmwiMJB6rPoTjBnNPWOhcUWAzrbkhwNYEh\nCqEweNbqatoC4fKXojnPRWLtH24YPDz9Dwd2b71ScJmS85Fuj3NDwvzRLrA7\nNs/qjo3QobwsJOj18Bn3YKzjbaUcTA+ENYkLof6/9LrBbLjZR5TPJPfKtVTN\nJJFKMmqr8H9K4bEviyztKVpcB/K7Gs47e0+7Tt7T8WGVMVoiktgkxtQYCCHn\nTb3SXarQUhYmzwqbAMNqFWL8ArdrNxHz5A3iOq+eAGf1SsTq68ZOes3z+3x/\nQ3lDhj1a/I/JpglJr1DSd08QpT9oZv4cQ1GhPYn1U/SXvaxQgGdjUMAEvN5u\nCZoj\r\n=VnBu\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "embla-carousel", "version": "2.0.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "701965a41e944f1db8b0c31b4a5c938dc6c18cd4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.0.0.tgz", "fileCount": 26, "integrity": "sha512-+BvcX+6gi6avHZ8r6yqs1rMFdxmjrqi1N8xfqBORJ1Bn66h1F8I0AzhXJYclN5dWpG1g8n4Q2FmH9CdFKtXNVw==", "signatures": [{"sig": "MEQCIBvxdU0MqtsL4kmE88+huhIXZG157VsFHfaZgEjSO+P8AiBrtYDFA1TxTg/zNzM1LFFrr2SvrtdmEq5xCmh/WeGrWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEURvCRA9TVsSAnZWagAA1EgP/3GUwg1Ae/6qaeAPtzac\nPC6fDhxJIxpVXn7rEHWUkI55LMaQAPWwAKj3Nqrb0DNLV4C8tCn0Kg64sYl/\nUgLEIIw5knfyTd0+wOrLSNRBEQh7KYKJK/1QbDouV/cyac8SkqM2RLcObSfV\nkLhSF+C76l+I1ic6oXLvL49Vh3zIP9jgDAstwVQTd2C7pTdK0IhlHBVwUyc/\nLhECaclWk+2Kh2TbhVTccGFAfPzXnFJEo552CE78OQ4Z7IPkvu4m3ZmZbD5R\nmGpcqH8jNypAmH+yGdFEUcS4uWjY2ENc8KgCxzXGhpcsCoKBGMJSMWmVLXrB\n2IxXcSUOld6wEoxv/YoetkWrVYXBZBEXnrmRTff+3OBXlLHdm8874MKy1xWy\nwYe+Wcfj/1nzKkFgB9LMtchaGtLnwM0DbnjNG5asJfpih0Nu85CITGI7PukB\no6sBNsA7PUp++gJpStN+67N3KMuI70QwmY58zn6wvLEZ6SyYpn5is8irZSV3\nTS7GHMFwLM0DbFNuK47PKGfM87upNJZRjnoEpBBAbfeehMi7z3W/JCWUJ3Vw\nujGe5FGjVVbHj44xryxaHuRPRiVvoZiHiq9W3cfNGmrHo4WqtkCzZhYuSriV\nuhVHop+kQMrcHPc6CIPzMvu0uZRnSemf2uBUKSDD+XHEqR4+GvG1BVGR/QiB\nro/Z\r\n=dC1C\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "embla-carousel", "version": "2.0.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "bf2962122c35746fc85435412cedb3f4f1671f21", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.0.1.tgz", "fileCount": 26, "integrity": "sha512-rmMnjhUJtS+89/1VPeDX2soAk/eT65BTFz0IKyu+v/Unw2eGEkJGzKe9lj4KhH9oT5abZhtOr3V4rdf81U5/8A==", "signatures": [{"sig": "MEQCIDw1+qS4VNGRy+Jsp/dppPE9uxRYAzlm+EKCfhoUWgpUAiAiWvVKvUbUUjmZsXX092Xf/ssi0xTOxcrRre0Ikx6m6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70271, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEUfcCRA9TVsSAnZWagAAtZUP/0BoITzgiIEEI/D/Pcpd\nFNDRwI+5+WdbsplCLQGrIUs2twfAAMJqD1GIrkU+HTAZgxJMFfvTSNRaxy4N\nQYM+1wNeSrra93NrhpMzPJvqkTJ/f0d2xPUJjWqgZmxjJaJaBjq1LIN/5PJ6\n01CUo+KSxsJHKbkiZBIu4ZgUxuq8Sy18bPSnGy1R2xF1qtszuEUBHL/Bx1SG\nR/x6kCaGD87NMPHgy40j4kaA/uHfPqfiq3vNBjoy2PzXT9o5P0Jx8OT78/YM\nLt5ZgR0dPOUmGJ5fNr41BRgzYdVQzIQwXvH0S8lxvt75ZhhHBr/sY1iP+3Gf\nKCmPFgPgWfI3ZBwYArtivx4w2rPU9/lIRGxRxxpCTvsXWw2tRglULn2dOjGH\nflE9niFePAxmzzQZlp7bE2vBpuSPz6bAyXX7zHljn5Ludufelx+KTO4fqxH3\nZPa0pApZchg5yWCidCPXHo/QSNyL5lG4EykpRGY0NZ2XLO26rNPnrcdWhBim\nEjQvihrVkXlj2p5W89CwX/2dIJzsVp6TBmCxBVNeISQjj7r9P2oyaBORnZVJ\nri2tL3+r8VFtIICiW4yHKXf1gYjfpHU6Bkkr2/bTOA5/3x/gobZVrLjLL7zC\n8Eo4sw4Kb6Vl549WkkPfviO8J9PpJvdjNNkY2Fdy60fI3H81j0zjzfXqKJlT\n27fP\r\n=7M/N\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "embla-carousel", "version": "2.0.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "0ad32beb309bfe0ae1d6904070b0813ed2829949", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.0.2.tgz", "fileCount": 26, "integrity": "sha512-VgBsPhOlcUHG0nvl/VYzoY32Bg+mjcgGrdur+0TFaCqCb8neXAWk54hNECEOWStUlmz0W6CWyvxz6FnlDzASug==", "signatures": [{"sig": "MEUCICvinlYg/uOFaY+zXwC9iG8T5oSx8I1+AS+pg88trnG/AiEA2HZ8ENweGDrwzXWOzLsH523cYi6tw5Y05X4qNvEU9uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFQ7mCRA9TVsSAnZWagAAjGcP/RozUjwaFvJU+8awbokw\niQverc0Amv/a0e/03tmpysRJ5GwFKnK3w+Ynw8OVPynCCOyyIZG9bUe550eA\nRYWpRE2XR4G+dT2WU2+6OBQd1/NcCYQsDmPIa8/a6SXWRcAfWIipy+1LyJ1z\nxwr1dpj1OOaq5/flSEZQW5N/7evYMVOXQcMV1g02RH7/Ok5Tl/xach94ssGZ\n2j0Tdqgh7Qr662eHZAw28sscvp0nBBZ36mspV2Hnr7rsBbo0sMpLLr2ev0wO\nT5dNMCjMwXBf972j5KyaQjVYULJR8ImiixJD3yEl250IdSvVc0FfiJ4ng40K\nXH/O40rTR49QE1tw2DrgMq/3u+sKB2uEVQVjBLztDB21FrhZsONIbxDFwNh9\nHx6R0OXeP/+WgCkp76iAe1I8jMSfLlHr2SPzdQ1cZiX0sOxyqU7fOUJlUfEr\nEbc5bNt3Nt9yJJVOR4qobMbFI1OOjJL1Iuhl4YatklpxopS28q0SyJkqdMFU\nEkKAOZ+kfNoN3aYXKeImzizM1P2IyhWa/cFABQmagI02MlJ9HGmQTwKWKX62\ncoBG4MyZ0myHuOXZ6AjD0VYMPk5Coa5xqoByizmm19jbY8Zg5FUxka0LAgNE\nY4yAbOWMyLEvgdsyTf7fyT7lERGIJImuB9C0kw2bYByWTyoWqJ59xSk3SyFV\nRy9l\r\n=ugHf\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "embla-carousel", "version": "2.0.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "ce2f51c1b8995244a0c9cb025c4d9c6f03363778", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.0.3.tgz", "fileCount": 26, "integrity": "sha512-BK15H/qpKw3LEXfaaQU/K3UMxQ0ciDdkkHXMfLZtTppginC0JP6JKjDu1IYfqX9RefaxNRxZjONEVICnISzBuA==", "signatures": [{"sig": "MEQCIDMeWzppVMDW0jIMw0RrZc0CL7GkfPXH3DbSG5QWa0DcAiALiYk3IL1mDT0vn06xeDheD6STsOUw+oH9cADYozTAwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHRgECRA9TVsSAnZWagAA3uMP/3FlioEDooC1FRA+wM5u\np6bMD+gJUQRLM7MlSYoJAc3L7Oxeo20W049lLBrAz/0Oi4E6kWFDIQapRrFy\nLSv81x/AX95z9p8UMPSPvrnqHCDysz8iy1BM4MYNOKhgyn4GbtKWKJGUNoF3\nCRN2qyExc2H3hXeiE48TjOESgO5Psl8nVogu0PYV2v289kKAjYB2GnKW20qP\nPUHxGnDXp/PZ2l3f0imG+sUwMBkRMqyiGMH9ePv2OLCtp/Vb3PiLpor/903n\nTnFQ6moJyFH0HSCjfRukoT9HgnwZnEdGxXyAS21dc5HzXr2eH3NYcoLFLdCR\netZWeCarapq/Gbnm6rb2D99bioLQBwwcAeZ79I++t+BuHsiHSrW7p4i48KPS\nhl/uC9lTgBC/A1mky7ZI7V5AsijWLL655KFXecoTrdiEwBmIQx+W2X8AXf38\nasOL2C0IF7HBJ6SVwZgG3tXUgVNjY3jYfyVtGVyzYZrJRcsi4BnvhIApCbnz\ncqOJedPbfDybUQaZXjAToIlLuPd8YAKJULjtxX5AxpLcR4w1oxVW9XM/Fhq0\nFesThOgladXpXZuKVKDjzrq5/Ib6Kre92CVc26qolZgrwfiuVsaX04EkKhVQ\nDS0ThKpdKUIMzrZCZQPMC/+t6XGn0HBneL+rNSZOi9OXCot3yf8oxBke2GQS\n1wsH\r\n=ODGi\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "embla-carousel", "version": "2.1.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "fd36446e079a972464faca15ec5593089793ed58", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.0.tgz", "fileCount": 27, "integrity": "sha512-KkKKb4b9iaFHgtrEuP3fWIaNjpOhp/s5PQPfiUPPdlF4W7+NCo+A9QfUDSywdKri7aX+gmITFQff0zt/DzVc2g==", "signatures": [{"sig": "MEUCIQCOI2heT1GTRT0cRigtIUyUIzO+havsAGY2JAtoUY5kaQIgXvJKXNRaZLsL9xafsxyt7evIlO4NjLu7soAfx4emGPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH9dFCRA9TVsSAnZWagAASncP/Ax7oRQt0VMzHKdjQNoA\n/mhsrmu/fV2bdjB6xVtlLQb3riQx4n0SAUSaXkqgccZVDLVhbCQXS4/g1lUq\n81j+7zE9ySo1ojOHkiCRIcLSxRHVoH3y/D09VaGv8Bzgm/nrtmAYOs3Newz4\nkq5r1q6qiIosOqCoC9OXuzhM/Bnu73K4oKo5zPDSeA5uA6y4fp8O4A7uFPOF\nXUKUWpLBVwBrjuY9B+xluNrhG4YOct5kGO4JSJml0GBvN+0G/A4vzyQd2w+9\ngsKVMXyekoZ0oa90hlJRveqJ9e1oZIaz874WORT9NAKmVihTnnXdcT+z5Wwj\n28jXWy/QOinU7WIY87FS0VjR4HPXG/YVbAAOP0SmdN8Y3uJYROjF5S8G8WBk\nt/HdcdIVmKTTRts8fv/SLzQ0xFkrqnOqZ4fvHGvkfIJZaF/TUOO9zT4RtBoQ\nVGdhf6sMZpfpeN7iod+aUegDVbRdEfRPeo2MvOhrOlB2PKsnF4vM7+1SQFQH\nYpBkp0+3QPH++udrpQIz/Y9tRhqin2Dy34C/bK2wRNSSKqu7zVUTwysDBzX+\n1/lMXGSQaIwPKHp7pDooIi0jvZW7iEyrzK82WmndhslJisFbdHcNbKVkWEug\ndszYw9SBZY4UOfF7PAlgL+IWGo32EgFTYOkgbYtXg4N2xrO/U+mzVCG0Y/64\nogMh\r\n=qlUT\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.1": {"name": "embla-carousel", "version": "2.1.1", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "29f440e372a62fe39f52636896bf07c6047336ae", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.1.tgz", "fileCount": 27, "integrity": "sha512-IGsZ713a+uYDByr0mKCtEDbMc1MCz5ZGlfGPng6KmmHeQZfo5bbmUF06674i/ciUav2kihnv1TLwTfecGKW2fw==", "signatures": [{"sig": "MEQCIHyadVTdH85cWNv08eJMCPg5tGAUJLo6u8L3Ni1Q/pEJAiAEJleX543GMguGY5yxHRjY2YkvhliijMilDscXLJdSoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH9wRCRA9TVsSAnZWagAAflsP/jUGbmjJV5QSD65qJRBN\nA+e8l0FrjXgGI6TH4uF7PupJjBtaqeE2wOIOufK8IsUeePnL5kqAOc1jiXSc\nDhb0gAFtOZa0x6itkOD3pG/vPLc+PQ66eJo29NiM+xAEcQ5HHdGOJ4r76pDg\nvkqEeDR1vyQYO7TD2M1xbuNtHq7DKDb3ZM3VpoJwgVvw5WyGYQuj33Bx4FB9\nyUzEkK89M8UyhtGtvvGNd8SUBoKnIzEVGVQmODYKryu/7gExEee56s+f+m0K\nXor+1liZqk98VvWvMVafjNsyMSmJIWrmB44KgeXS2aM6czcnO+jx6/OAb0fB\nYog106UCovev1oxZCVDDZgaKw+9M2wDwZiRLXRZYiK5lrnEaGXBT6lFtjxa0\n8NuqzBR+GzuovAq1aT8+WbGR+0RmkWTfXwzy6+eL06SRhMHtna1qU7+eJrvB\nI+vwDZeTmVTDHiZIaWbNZGiamUWrtCkf7MuoeJGQcdU33u2liXAEANwleRS+\n9lk+dvipCzRyut5mjgnWF1SojpTpVAyUPAFHwmlaHpI9bKNDAQvGQQijC75o\nJ85FKvbisjUy99ZiH+0TPqehXOAbIKdzMq01fqWwaW1VN/PMQ5q9Pdceu+A6\n69N3IqVf8+eqi2WEL4v1X4+4PdA50vP1z2Qr+hCoeV/0doZBdoRK3dBn8ssR\nbCqE\r\n=yanc\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.2": {"name": "embla-carousel", "version": "2.1.2", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "d9f066ddeeda3c9144c11c56d1a150accecb049c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.2.tgz", "fileCount": 27, "integrity": "sha512-fe1mn7MPLeegfnJsICaEaIYZ0G8BXAurBiD3Ztfx/D3VKVFQD0lCCkOe9xNuW5/i0iqYR2LSrYe21ndya8rwOA==", "signatures": [{"sig": "MEYCIQDzi4ApygfwznVVsOTVqivY95sE1YvDD1i8lR+04r6kZQIhAMQZOqCzQEMBCSfhDI0fltiC/mrvuahT7T1ZGXES7LvQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH97fCRA9TVsSAnZWagAAqcwP/08nPfJpCF8eMK5nXHLI\n5RvURqkPXYbNsf8ZVMNUy1f+RlCipIMi/0zsT0WKxaNxq1gRS58cHvW9H6GH\nZT3Hk6AK3cTR07BJx1SxCo6Qf1M831boSRV+2ZzN5miaaW0T8/DnpW22ljr5\nLqMpleT7hvxgQrU/UU/Vn2OJvqpF4w9fwyzI603fLudTwz4lsmnhqL8wVUPx\n/kDJZqvtehG3GHo3j8s/DBU0XNFfgUr8VxEyW3TtmKEpN79irhQTrs4XSUlE\n1by54qst18yXTPQNkl/CVoGM5YfPu4YBh8w+16w945Z7dlhhMISJmZ02ayrB\nMJ6L2Z3DKZ6wVQ6Mh8n9ysVYzrUMoTNvUxT/2G727ZuJBfbqzQ2yzcoOif2m\nECoQO8JjfGaMJH/ScmbQWRY9RvENNPLpYjAd4DMN0ikPf9RGQ99jQa9+nYUO\nvrguLaFMA2O3uAVjWg+I5gDylioE2ABeRXm7NP2KffHZwkezi2qM+vNbjgdM\noYNb+xO/NobSL/Fgd4AZMroPWRqegsq6QYeMm9dwJ1d3u4GKYXB8yHa9o1CK\n/Jy0OqcQmkF9buKFzkjIfwJK0SpSQEjCw+ftxEmB+RVsAB61Nlaa1N5Z+iRV\nNCsqsI/e3Fr1AQfjtqScGbMDJ2lbNaUPI+rc0a4NlKAQPEoy5VimBh/1esiP\nUppr\r\n=7JIZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.3": {"name": "embla-carousel", "version": "2.1.3", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "05a28b5970f4e9697686df014baac5ef922571dc", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.3.tgz", "fileCount": 27, "integrity": "sha512-oQ+LmbRhorKuNq/CpBz+LgrDLKiLJ7Xc27aFr/gZ+CVsnjAZaq6xpw6HBCnY2EWfsQaKpdDVd+xEJ7COvqnlIw==", "signatures": [{"sig": "MEUCIQCI8bzLFf/6SjU2P/Cg2Op13c3S0Y+sHFe3Whqt48EK1wIgaWzMMKYsj8SFNavVUFE5rfBFvMtRmTdSFP4e53JoBnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+BiCRA9TVsSAnZWagAA9NEP/1Bh/XBxLnCVnmmW8bp3\nzn9y24cuwDwx/s/c9H28LQkFVPK6MT8DvBc4tn5N4mX1sYREro1Ra77MWPtK\ngFXM3NLZ+rCxLsNPR2qk0/00YO+vhDxEc655IPfOZvFT4eEdMaUZbEXLMSKl\njiIXv7sTV4yh+yk6pJFWkEsOtU6ibXFFQ6AHv+Jc/qUDADL4nPpt8Snx1xU3\no2nGDqJfZRCU4B/WRA/uEB9b0GsPJ3hMG9/loOcD9MtvVWBh8LM0UGEbI6/G\nHiujcRXIX2BtlCZWUgFEDB2kIdRQCjg4gYj/XFgziAEBN6n5CafYs/SvwqYV\nLNRo4tv/090NyCAId8gOBy4rGJV5UHB0rUcZmMgUiu/AfIaC0BIwylYtQTwm\nBdTpkpifAdwrIqIDq2n1EnOYi1jHljt/42S9wCpj1I6wJi+bkVplkH7Wpf70\nusz6zvSToTuIYEyKM2kM/10frU1FY/n7WuSg/+PI/qWgL85FmoapFbP+o5YH\nq66f6TfiuyaDIFMColdS81ta+enrh2ETtLqUZ95uBABMXqxiICowCIsWB8ys\ndeEStVLrUjh+mFy1aNWHgfgVK7GjTI1OmLPZy4jb/OaGE/HPau8YdXmphTRJ\naLioT8tJ9lZH4a9cry/J2Bm13PaRQHYO7UFEZQ82n9oS9s9yJ+/X6PrP9yhR\nl5Oi\r\n=k9T8\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.4": {"name": "embla-carousel", "version": "2.1.4", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "5d5bd84ed138c4e6ab7144a289f2bf34f03af83f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.4.tgz", "fileCount": 27, "integrity": "sha512-humJdV+6y+kGDI/Lqrq5MMuAH6fkM7rzUbYcEMobFEKxXwyJytEdzcesG9haSrWXtUqno5mLJnmSc6RvsIvDmg==", "signatures": [{"sig": "MEUCIEr9rj7TE0cPL93IBP+teKkxvkrl0DYoDMn5WTKc77XBAiEAuE8ezLWPwtfIIZVCDzZgOZykvxvmqdYVDQPk4Ac/Epc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+MKCRA9TVsSAnZWagAA6NYQAJHDnwv3at2sGWsTzpGs\nkxcLMVjirXssLu69tnVpYlo4ec4uLWix+5SwcEZKVG+EGHpZj3h3MkAO/Yps\n8tZMbwdduS44DNJlpXycxczdxxGXzJtX28Q5zAt6euE7+024piZxPyykJSeC\nfaqepRSNwrmHIIRxs3s9CSrL1F1TA+T/TXj1loakQs5HnLZC5MMuAvLW8D8K\nkzP4d2gO3ErSrIpc+lqsCaQvf24PW4LNPDGdCD+Jf2ikkbPqqSTTnir9yMz0\nREOsM0+g/B8OVWVJehUKlN9pzdDXZ70bhjkwjCkDdsvg8yIQsvmGtbS9JkA/\nAxTYi5ZITWcJE3ouy0XMf9adANkM9VLvIpAeWFUIJ1NmaqyN/7H1NbtmGo3m\n6cY2NkOUfJPiZ+CC3sujwmEV1OoBNt9M93mPEDiCch6nJb0/UCyhet5Nv3N0\ns4/BtWXxRs9nIZFqKyJ+wMcS940LRlUNuXGy9gktyN0gHgE/5EpfhAe/tcu8\nTbSjWiyJ/3ywPyQo328q9hwHxcLvLmfR705accOeL6C0sTerg8fYKxjAwj/E\njeOaR902DjAXFrEp/OXhkzSf7ABDzekYdxxadsZ4VIk3rWKp5+iwH5MQmdie\np2asG75AexxEDf5EfTZb5DD2zC2GzIqI6Kyw9yB59PgpyIue0vdM1LHei7dt\nlnCQ\r\n=bNm7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.5": {"name": "embla-carousel", "version": "2.1.5", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "40b8e643c85166abbe77e901e13d7f53587d6789", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.5.tgz", "fileCount": 27, "integrity": "sha512-o5AQm4+Tz2M/7OfAcFTSdQjhlcEgXzq+zmvtX0fnK0McG5VlOzSb4fMjoivdDvsLOgPbUrf3SG6X6RDZclJwCQ==", "signatures": [{"sig": "MEYCIQCrbFdi4KWPesLPZDEIfEWuQ5ZPuuS2xaldYrBdwIAIzgIhAJ30/cGd/UybFYj+2qRaIPYlLJQg0Gb88WUgXgaOUl6z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+QbCRA9TVsSAnZWagAA0SYQAIV1vk+8RC9hiFbcewJk\nRiWQ82WVGOC6+unRX0/0w2W6s/pfXzp24VnQASdj/+0nl17ypiY2hK+JKfoK\nsLYrv2MIal4Vn6yMY3vME1KFL2/EBMVeVcumJwXgvOPIktcQiepasrVlOSi+\nRFQCJjs5HyCWzWgXK8prSPxUHl+eew6Q1y8GeBWqHYLuedliMvDgrCq2Mnqp\ni8Ku/N/C1khNnYqWFzVGz+KnY49QK0TcyVG7QOS9ktXOnSMRnJdgETTjbqT0\nfAZdVSzX88GfMHFZ4TpdhB1ITGOnJvAeOI70vQehISiPD8fOUprUZp39Whh9\nIveAVNwF4jl7lNXqP1w97/HQQcOaTP32hD4YlxITDbOb1erUr0co/rp75Z8z\nsWVFCE93MvC7RNuahZGmcAo3OH7Ivm7wrsVBDTNcOn5cbU8nxm5fKvmN4lN3\nVmBvcEVFC9A+ggPxN8SZIdIhbGVz184T08iwhbwIrMeugeAKOQ0fhVfh9/k0\nsPAUBLm8pX5jNaTvCr404jG2BpZQn1EZFfZxDrYh0wl1AdgJEH0VvpV1bEkN\nVp51A2vV2bom3Y0/cLYz7aA+czcREKq3YJFXx6RvyDnJPIle7GrcAVocTwNZ\nbo9v1rLilN6I8RLsBk5d0+XMUFiFsC8i6WFCNR2Q0OXDpTvLSqWBwRGyyTLq\nqDaX\r\n=mt2d\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.6": {"name": "embla-carousel", "version": "2.1.6", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "896865084b0de70d0905d0989e4b2b6485d27e6f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.6.tgz", "fileCount": 27, "integrity": "sha512-kQZBmma5n9EIbNR/yuU1B0oMtnqYueBMGjE2l8G53lpyj7IQB2o5KdZC8pdt4rib/7PV/WB1xaE6TpJVGPSMRw==", "signatures": [{"sig": "MEUCIEoyH88wQMcnSEPB4d2+k0PptyWhCdGxXRQx//CAw64vAiEA5uh0fVZklBDumKA6CQ48doywcDntHGMyiuRwySw5yyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+SxCRA9TVsSAnZWagAAGx0P/jZIlXhBgBzClhJzhEXH\nNJW0TImWENeSJpTYtNPeU196cuC3mzG3JFQKBa3FxiXdHIZa/JrHzkYGUNs0\nobSa3j7nnDr/VKbdiwzLGLdn86SZTaet8lesrqX8Vjx4wQ9PztdCzYRx/dQz\njr+dDqEHqX46yEDXgUrx45qBCxSoKVtqNIZ7ljFFZUlxRlkfHNe/DnctOD7i\naWyvvHzez7hv6NKr18AQRQ7ypTzVJ+39wvXu8o78nm5wOXw1pKL0J4dVeTUD\nUdIBwuqczM+i64DNSCBqLzu3Z0u78BbTgHMriyouFoHyqsegIPlipo7bVlax\nsvQIYcr22TD3kWiXFFZ/1MbcIiFu2gagj9/gAvnuN62rAp9ietexVL03FNbQ\nQOnEYdhZuFv13Kh5LW1DKuOmBr7eBYef46fsm/juoluwo0brPvz8p/JaYjAs\n1cpUGrFGjED3Ba5YgpqN61USMaq30sYYh0CQG2G3jsnffiMi493CuZDTIFrL\nsTwoEu1Hwm62ZH9afWTLXP8FJUIR9UDZT5IdMQ41o7dbGyTrLe03pc2LoEnA\nKFwaOMc9OlLcj9P1x317I0SIyyLDKvXvuCz5rJEFdkFg4h2yUrEHc6Azyd2s\npyBEhw5szKRns8+/X6TOMa7B3wMKMQ3wJ3FfZ/zrTQ6Q+UPx85oT3c5A5AYj\nO/f3\r\n=7NOQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.7": {"name": "embla-carousel", "version": "2.1.7", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "e7fb2e4a5207fa63d4aeccb0ae99d1c8823bc871", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.7.tgz", "fileCount": 27, "integrity": "sha512-d47KUpH0R8VL6yBMQFsP5BC4EdkQP/bNhlK/DlEOD8dU6MzXNKGX+iJ+kIg3vdARr1Mpbq3aWF7O1CmRQXV+8A==", "signatures": [{"sig": "MEYCIQCanPjSKErAwRj2Kb2xVHdz+zvMk4yb6lEgxoWoWB7/GQIhANi8PhDVmhrcPy9Wj1cWXMrK2u/XSO/SG1ZZCS4y4Pmc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIQ9xCRA9TVsSAnZWagAAN30P/iM73qvGtjcdlpev1gWe\nKlbOOJN/wRJhWWgBinm1ACholpHPyeINfflL/tmGL50NMlY39FkyU9XS4Uqr\naUrytzHbHfqLzCx3El0/2WzycinyDHrCGxV4Iq5P8kiOJWKuEhdd/20yPYVr\nCgxWiWR0wR1f++YtbeOHXAn6c1Q9EaEKwsNi2qR0aOK1RrVKV9Lh3ZXlei8+\nvIB6L6T4Y+PysJlK8nLkVA6TQ633dghbLoPJIdb1djOyoJARDIOl365tNect\nPIJEaqApMEXWeXDwxCeZVAZAidBsyJW3E+eL4+wQPl3U1lGnIpgFzBM9wVVQ\nG7tzSpXQJ6GP7+cZlU3ZNOx1nReEH7+nG2ynqQbjZoVUj3zDiNjgNN5MpNwf\nHkvZ5aHbT8ozvPdmm1Ovr1OKrWswfIjFfxccliFbwpMorAE1AivsNOva6AIZ\nnp3eCUHpXz+wq/YCVioO/xi62becMUoh4r+25G8qFY6QZyirWQ++is5qKlBy\nb58gL90kKtCXPeTJtW3h2S4BQsjEhX1Dk9d9orUxrKd7p269PWoHjkqqxpaA\nlyEMAj24lebnh1dsLTiQWryfWtszuz9dQp2mF90eFUHUW6mEeHozWlnswuD8\nwk6bDpxfJbN5E1Xel4ZDUL2Z+BbHTbx78pZJgVbRxbvvNEbsomaApk0PajU0\nsX8Q\r\n=GtCI\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.8": {"name": "embla-carousel", "version": "2.1.8", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "3043f23e7d0b538dbda41e04f478cc6b30cf6b4f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.8.tgz", "fileCount": 27, "integrity": "sha512-8GOM1Rqtj3HGx1N9hMgHaEoT0tzFJsOsn41QGCSJQZRgEAzir8SMPaHbDVm5fSqENd2dfT2RZL/x1atWRUs/ww==", "signatures": [{"sig": "MEUCIQDHbazzTr8RWVGR4ei9fsAXTbBFn5Vhm/F9MROq7PaYpwIgJxE4JSJjmbxG37w0sx5AXjFiB5oNQwDU9SW1+wVg0XU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73331, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIRAPCRA9TVsSAnZWagAATlYP/iFB2463POuzpBHbL4GE\nsvE/r/FTZbFWm2q3VDWGm34uOYGaWs+zy8MFEzSpV8QjV/vA7oqgGvYEjBH2\n7JUJvyHnrwJ1hqD9kzECn5sTz3CFrCsvBnwZ3rWVdK9Cx/6eIry7WMkCkrAV\nBybBeEbs3wFLhve9OAi8SF6htVtAdNj7Pd5nMyK+eHxvdQbFxPy7rEiqAMkX\ntPDS9c/qUJoW7cwakEge76ns9S2f39KgKBzWGJkq6ZcvVRsJdVYIFT+o+7PI\nvsDBlG6dmqGkPibZhvWbi9cI8HswFYgJ36gtidczWkW/4FJcdBEZOW+iL9tu\n9JSmBEwcA2ygNLXc9ii1rsvd+bretvcqOjmKdU7eW6D9Vb39qvgYtfwEdzl3\njBfUWMZIPMzT4QVf8BwR38loYcOJClHawgyJW/lqcnIhrdWVIlkI9xAau04k\nL53ZDP2FedPIg964yI0wvKZMy44rOCZHSqSH6qb9foHG5XgbRAcDECMRcBpk\nvrOElDFCJkOlCVE7mXDwTtHhALWx0Vw12AjQNCCwRVr/r3Z+SCDia5lxBdNb\naL7In21S3LcfzAOwyEEiuRGKJqqFujI48G2zaltKY7M4Ie65Q9VtjKLznscF\nbcVNNGa4hHw1E4jbIWbvo9PqFi6w7FG/ewkircOV5svWQTs2JxYgDTQ8HI7c\ncHNU\r\n=uEvw\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.9": {"name": "embla-carousel", "version": "2.1.9", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "0447de5b6f8e3ca96eae6f4376ceefb48c4531b3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.9.tgz", "fileCount": 27, "integrity": "sha512-OoofcFhfve/hhmUSbDV4Bib/phOG4we+tZ7Ehuq+8o7dUEhxtbLtFlRTUM4Drw08W0C9tgB85RNAm2CDXux2/Q==", "signatures": [{"sig": "MEUCIATfDdPI3LA37sSRjiJiMBiMbiNgDpra4uLq66QxSx2RAiEAlaU6js4PscUdciBtR6tGQ7vHKJZnDUs0nPx6m3yDnDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIRJDCRA9TVsSAnZWagAAE+4P/0Gz9E8+D/5rhkw/FlgI\n1YiGEaOOB1A9vmw0saNclrZ2y2qT7cZ9gsfPrNzLWjf1X8vLs5fq8Q8cydQF\nyVtR+kz9Jz4ruQFiYVU05vpQhXhgMfa1GevisHqQGXquA8aP4D2bpwHxHbRL\n92085UNDk7mwyVSZAluIeEpH3bhBbhBaERZEyOL62ICNdfPMRs2G9FZzpq5e\ncxIiURC1PMRdKm1WoxhdM30dfqumMfM/mtACcUIhuSAX3KbeMCd5sBFJms87\ngV6BJ3H9DnYIwgziwXtYFxy72ywx815YRs0Yanwcdj9eY7enT5rvAjGGRx0b\neDszwVZRyIVOBE8y6yY5pluRsh0hqoI1D/TYtCh2viNEfZMQhDykDF5TD7qm\n7IFaxkNWUIm+e5D1aWpNgSQ2zA4ljmRemdfhurWKT+9/1o98U6MeP4xd3HRw\n4LCYcRzLGKo9z4V2RBTqFLFYTZ3ZkuHA94vq4o/YvXIf8jAMje5+JCMGCZRy\n48sc4ZXblM5Zu48+zOEpVYBc/6nWxyjW43C0WiSfFD5i8gX7kqXj8O9Ndm/4\njTaKmEm6TcWaenD8lNkNnWXjqrwsAolFW/jYxpkPMjHudVrNbdgtX5ALtrWI\nQoLVlcKSlaok6dxsXfCxSxcJqI+wfN8TQUlJyh7mZjKSBUmZXEwUY61ExLuK\nVbay\r\n=i0dj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.10": {"name": "embla-carousel", "version": "2.1.10", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "02020c47cb95074385d0c69e661dd91c86a1e899", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.10.tgz", "fileCount": 27, "integrity": "sha512-Nj8/GPMUWVtDWiYgMGCXyZ3nXwJSk5rlD7JfdrjUwHpk3CL9syiJtO3r/klsXnxnXUadxrdADFozB7J0P/ZK4A==", "signatures": [{"sig": "MEUCIC0kAkH1s0OwDsI+iZVhlkb8svwCgIhAUarg5GAfLmb9AiEAh1cgnkixRLXoqhS5jzdB3kbaFwd80iWosEMDWYCSJqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIRywCRA9TVsSAnZWagAA+xIP/29oXi97laFgsskLuo62\nm2GLhbtjd9C2Roc89/5kWV5BkP9d36zgex81hPHadQXFUPXWHJnl0bgm/wuR\nHNsa3cKME0SWG72Ts4QTVzd2OmyNjUj7r62bXQ+gKmrU2hByZhpB3hSxPy/H\nRlJ2u08ni8ItOSiNU67SJxE+8vptLGPnN62FUGk8keOkp9KH9vpdOTGpYuik\nC0sJHaCJ2hOVkxYEjiabzirf1DG1aY9yA6IyhuS7Uy+/CYNSeRPAnkdYF1Hw\nytDxI/H33Zn0l80Ifzc7YBZ8NhghjWxJdmXKQ8fpdv1PpBuTTyvsJcHuN15e\nRKAC91mS5JTk7hpVMdmLdSuNpxVXGcNVWlZCUDXt8MYTmyShH5s84mzuphUR\nMxGY7TPavswQQtT4J3NxBLVPaUfL25S2KOMIR0ofvb20DhirIPFngzL8pYjK\nbTuqGy9DBY3vSuybmA5yMzS2IvgZpkBMp7yk1aYRJgEN2YXyoVGXnd/4tpPg\nIGo7IV0iCr6yWbib7TP4gLxuTVSeJ9eJfSw84HlGguW1x5ziq/5R2MI5FFw3\nOscjDilTebV0CHknNMxooX9gW9Ud+XH8uADwG8lNaey96k8EUXtDjgloDbBL\npBbYHds7RwWSvqOjwf5kavXPTwXzpatQi2/9W428maqcsN7+VTaUDN83ZFXA\n7fc+\r\n=VHn6\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.11": {"name": "embla-carousel", "version": "2.1.11", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f4c285eb8ed4d29761a1f19f6ae2114b8561d687", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.11.tgz", "fileCount": 27, "integrity": "sha512-YUBkS07WcPIgFj5O7dTETHvrasxClKOyXtiP+ZctH3c/8UvYovPoA3jvfFM+1WAeLaM680I92IdVbWUHV56VSw==", "signatures": [{"sig": "MEQCIHPPoYgtFIuNnnb0hARBuQLz0CmqjwnZfu359dATZzwzAiAiCro3u36I749czaHY77gARtxj+jMbdbEkRIXViDIvwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIejMCRA9TVsSAnZWagAA8h0P/jHrj/k5aIGd7DHjTL8R\nsS67TKYQsq8hcXwKqXJ+31Q4p7SHTD2FlKvFrStJuAV7/wLggnwCAWRD2nfb\nVN7/UVN3Gd8hVkVRBPo3EIP309pXXQ7UD9IeFKvthk3FxClRQJ9Vf/KdhUv7\nPydd/Oc3IqoUGjmPQVKTD0Ek6KdXkMEYScNcZvyOWthIUrB16xBNNcTeHs0F\n3uAMbtk11ZepR1HX1A97upZGZZ7rh0TE4P85ZocYF3/RkvxHExsQFOkVREdS\nAOA1R7NuWodZWR2vGtvMBBIHAY5tlyGiHgkpRvzgc0JmzHTf+B16yChjfvXr\nHQQLfPXn/98pcDkBFLmmbyQ8mdzhw49t0GrLifsdjpJqWymYlMBp24JaUaB3\nd2unIkspo2WMXrH7XfBmY4cKk+ngpjuJD5JTubj/IP1Sgwt/5jwosS6suIAi\n11eknxg96DsUbJap8dQMhcQTuZ3IQAVsjQkoT1nMq2X7FIRA4nJDujEFT4Yk\nBxQyJTeiEadfvGQOUWS0w5Yk9m4dsP7/WQVzgy8Mmyr25u2RCwytXRE6dWDP\nhaHQ2LtJNU05BVRa/By8KYTAkDZbC3xPy2fHg7Cn3b5OtDBULUnKHZUAX12u\nx/7w3DgJpzEXGGZDcEEAXvtvtAtk4UhsHflKNN+zYcIzbo2JqRCPQvIhJyRT\nfrRr\r\n=pefQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.12": {"name": "embla-carousel", "version": "2.1.12", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "524882b54cc5b05ce3f4a37850626560bfe1bd30", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.12.tgz", "fileCount": 27, "integrity": "sha512-tmvlz7ZCi+DUCC2ocPJotiZXw7FV0hdJlwbf8YKcfOYDYarnGVpGClpmehtPgaS9G2S1VeE9Ma8ydqz+GRS6Ow==", "signatures": [{"sig": "MEQCIFdCSXgBq0wF3o7L/FTUqxpUeit7wfmlm+aG93A+lukkAiBQsesw3vDHPUBSefNWBPe4GKf5TyzMKqbc8rTFFcsimw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74763, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI8C+CRA9TVsSAnZWagAA2SYP/jS7SJ3peIDEcxtXARZg\n0OGvzP2nonZHLJXtOvShmDZN3jgeEY+IuOhUMmWdn/a5BjwWKrrJgHdXTe/9\nq5GBBeLhTouhb7r1ZfaJGEAL6A7IIR3j1/dhbtFv5S/L3Y+WN23zF4uYd37y\ngkMnVGFj5xHGjRwP+aIakHNzjjbVlIMoSVFbTij+yjY7Gf0wSGISbrnYIgcp\nP0wgzh9CGzWZA0o4X/EWa6YkIeQ0lfTvECmOdnWzNR6RMnlVRPd8U0EFXzUo\nynbOapl/lFZgOYjqm7hVDvALAtqqTunbjMs7HmstN4hCZxKkUqezvWliqgO2\nRCwy5IcUwMH1AL/PEEYcr4Q5kYBg0Mm+H+DRJNm8MERZNwvl5P4aMDE9T6fD\nAWkZuU/CIBbEtDtFBO/soGt+1uL+gB20jiC1EMUXJmg84DQobmBScP8v0Jf/\n/TwTLKDtqPi5PJGkw8uSP9ZTAzufGsW+QlQxZG4sPzR5uOsqrMBvNyv3HTYn\nTEP32UdlSAv9zJTcedFJgZqTq3fEjmIfcSzqfOlcTKyqxBB1PjvpQpjcDVxR\nEt39GHZI8SQkySlwtPt4/0yKj9aKfs3KuDgnhHsifiaPU1kwM4o15w2Lq8og\nhIzcUXysgcT0JJ/grwBcMmRyM0eMT9yNazqg4Jva0RTKvWsFLifhwAhTvPSt\nLy2+\r\n=K0SC\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.13": {"name": "embla-carousel", "version": "2.1.13", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f2a33a2481df9fddf9b2eff01145b2f62509a1dd", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.13.tgz", "fileCount": 27, "integrity": "sha512-aHZZFA19EbtEMssUSBel9t8UDPwy0sFD2NjMLTSItGMm/lEAN081z4uUe1JFGJRIwkgVVubYk/XDywc79cERQA==", "signatures": [{"sig": "MEUCIQDT/Aq2+ZQxe18mmfLd/pn+yy7VG++cI+jfVff8OBDjLgIgXRmTkfiTO4ixpmRMrfmCPnHCezSzeLRVyjl1xFqps2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76767, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJRY/CRA9TVsSAnZWagAAW/kP/RX7zFLh59yFAI7h7scX\n/NVW7JnrroV/yLSF0/pMu7IDELskFMuvk9Acfi6IGWiPqY62XtVplJS32yL5\nn5qoPwD2JHFMCuthVSl++VQu+7kXPLi4SVLdfHd5xE3sM6g0DtmCm3F7EsNx\nvGJc7r7Z9fUM9WLhbJYK2JFpjkMm7WdsThE//BlbWV1Lw931uBJnmysy/cpG\nY/dBKyN8bWdmqt5wGk9O55eyOIaqCkIAFrUaMPBUQsWjwXq9odDkWSxLJwxh\ngpx+HMPWL3VX+m4BLuGQzDCLkt3zCkCxgSnHw7ypURbSSDIPdzcX5rae3X9p\nFwk3h5Rzkpxzop/d+vvipsTB+HrcN+H4vEmWV7DKTqgPgkYen2pLjmBSyXo2\nqs7z4GwFqOPXAcZH68iZZ9bQH43U9gWVxaQobn7+Uw/45tUS1G39d76XvM97\nQcu47P+eaSHRytDnOJc9xxbfsEn1S337GGsaA0ExXH2xymZiE8Piqb7tOeuf\nYb1wHRobCvBqhhmQvJ9N2nSbvZKyMfQGfZhM2GQ+67I8Dh6icIBGG4C/jOQ9\ncbA4ud29jHZ4xnZJ2wJ89kTj0I8EuV3Eiuk/DpZ+4Pj5B0EdpZYgjcMySxJL\nSP201kZ1dUAKWQMH+3NobDYRKg97tMdoppqo+G/cED/RgMpQqwloEG1eqhZK\nfGhw\r\n=3MX7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.14": {"name": "embla-carousel", "version": "2.1.14", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a32d62b880cc0dfc1b6c21e485c8a5042199eb3c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.1.14.tgz", "fileCount": 27, "integrity": "sha512-tWcxFzSDKhRAdgM4E64vhoEDmmQa9J1xl27klK02mAk37mlUpC5EGXrCaBQkZpgo+PTkN6/3qPvhmpMPxgnqLg==", "signatures": [{"sig": "MEUCIFxXdcObXqIaZMP6R7GaiSJ9u0HNbj873g7XX1GlIH7bAiEAmvHAUYAh1PDEMlbg7vvR83Iv1hySGcTyNGHL7+Aw/dY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJlGmCRA9TVsSAnZWagAAq6QQAIDK+d8532zlSTALtd+d\nLzY4t7MqHbci7UcI2I5WgBiGvORzj6vZXpnRS+llxhq2K4GjoJJnfQCHyzcf\nBozc1bQvCjsX93kJ2r2/2m4w7ELSp4LZ56mRVF7yRfJ8PFTbalgWiJ2IMhzG\nub56Ph5dW3stmUM0aj8US4PciOGSMK+OhXvPeKTSHz22oi3FIX6lc/8g8Q/K\nqogs94GU66McdGoWAYls8CHkra8LvovEm2YrSL0rDpgLS/CUpz0LdzP2XXs0\naoCfgcQ0bqXpfM/jYPqjiufnccuzAhJzntY9oP5dScY+1GOMJwzXVmFgD7Av\nPRzPsCwSdtYdKHHQZ3z3ICxeLEsEzOc1diOVX+N3h+8rD11K0/a/nReGqNUp\nn+Ken//ilw8jMq88baytS1w041YWtvDZsG5i87czyX0+Sc+dqIg/hqNbcmLt\n8rAa37T6M1QXfusEbJ6PmJD0UX8O01wMP7WkUizNJZCANp1UFMoj7Dbrv/Rh\nt6cQstLyY6+t3aHZOA/+Bg581p+alKbofWPmvco5fLLm+x3VXEJPZkV9ERXS\nxAXXRvaAOdpMRFxnjcsXwln75ZMv9IK7CfttCmhCFWV3eK3HNBK3VGcbsKBj\nmJBFhAZaaONWVLi3IxAAHq3l89lQ9CT/v0iFFipRdFc3M9Rq7flGXmyQ7QFn\n5q67\r\n=9cia\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "embla-carousel", "version": "2.2.0", "devDependencies": {"jest": "^24.7.1", "tslint": "^5.16.0", "ts-jest": "^23.10.5", "webpack": "^4.30.0", "prettier": "^1.17.0", "node-sass": "^4.12.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.12", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.2", "webpack-cli": "^3.3.1", "autoprefixer": "^9.5.1", "babel-loader": "^8.0.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "webpack-dev-server": "^3.3.1", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4f5d99f166d40524991083a80c196e2488e102d0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.2.0.tgz", "fileCount": 27, "integrity": "sha512-WQ4nl28RMGoJtTFKRSrUlarOw0jtDJYIHAzC38Xf0NwSZxC8mcziVwrzAtQEaTwR+ny5sluJngYnCrzqDfKhuw==", "signatures": [{"sig": "MEUCIQCyX83asPgfWtK3ewpBmT+6iUOee63l9ROkIqkqJ23HvwIgNVNao4deVYegBE35IrqmXuWNrDN+xgOV23XJfcrgTP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJyB5CRA9TVsSAnZWagAAsJkQAJTkCnQUiY79hjln7RvU\nsXqYy5GOuWZtfOJbnPCTzsleZeKn49VF4HUqcVd3Ck14e/ithFXjJYTGUxV9\nnjyLlvaCgAflM4iJINFCbvYPDcTsRiNpf+7aHBTnw9KnPV/sTRq9y3TIxgS+\nZYhnQiMoEVmbyHPv5gwWArt+CX0D6pBFhX/q6cedoCwdBlMQ874LnyhyTdfT\nHmgUhikKpxeXM/tZ9dFEnPh5ZeXTHQMq2wJO37qDZRQU+0aYZvtC+wLCE9Cf\nbLZPV/mzvmY9zhyOSY5+wn3bGpxbRircw6UA6u36OX4zml1Toz09RhAuxDpv\ntbMebguEVT9rPxFLWpczgcgyeD5lOElwXQ0maXb5jzB/syOcBkh4psb6xTcs\nbJxZaIeqzZhT1RpobCPwauKSeKnI02T9eHXWk3+Z4WkJQv+dT+sAn+o/dDvm\n53tClsu0UN+oErq5DHlROdU7invlkcQZfo0Q0y/NkiM/YDttNJ5u6GD0UML4\nIoTmsIvownjXivfm3D2x2yk+E3f1yTeJrZwYGTN0KHLGeUZP90ppLi3sDes8\nLPVF+4zV9/QbW4coU0Z5zdnQhhgAg81zHErfGwKIqHrQi+0IRBT056DVECRs\nXTri8SGeTV7Jn+z9Z8BzrNrJO2mHKQa/GHT3/eUrexQ+7k1RE79iNa6L/tl7\njWsz\r\n=+YBk\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.1": {"name": "embla-carousel", "version": "2.2.1", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "c6371211c5df34d517510bb2a8fca4de1f1623ef", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.2.1.tgz", "fileCount": 27, "integrity": "sha512-bZyuunQ4lG/ms+CDpWnqflLteKgXtK4ik7HmBXZPlExugrCifJ+2+uOG3FkCltf8sERodWzSr8qCjs91dmnf6g==", "signatures": [{"sig": "MEUCIQCrGxZC3/w6r9DUtORuDIN8/+XU0E14GYUmKVfnUOqWJQIgGu28Tc2+eOfeKM961IcO+mcohV5cTKhTWCErOfT39K0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLEn9CRA9TVsSAnZWagAAEH0QAIQ4UcXK8mAma8C2pGq8\n/LP1Hlo04TdBOvoYnMBmr3D5ijxbnSisk4kL7PK6rz5WG2qk/FEyy//jpBgN\nmWp3JnYyzrhGrQcgBcRstTEw/7hCscVFcW9KbjY+4h82EOR5Y0G31aOzu+69\ncXAo3ZEwpkp3AgwqbFtsaQCCQyvPMSwzmobrOd4lmRCBi1bQlnFJWTVakBhS\nYOLzv/ppYOeGGQuLOY2ojC+PkVYhKFh1XSF2k29spp+JMxfdAITa2/noLgR1\nvqVatBgjN7NCuf46CA+meA0SBrWTgJoTwEp75FyREWl8C5C4g0HpQELQLPR1\neOl4Em6WNoMHUrFLgBz1Xp2wUVtEAUKS3CWH54FrvVv3zfNTdggJBEmd06f/\n8m33nlEe7Dg2ek3QlQFwSC2cv2IdoNIjXG4XNLME5AxvsJ4tjkrkHzp4eC3l\nEa1s3g9/2pMHHFGpCJaB901vM+piH6GeEGJbmd9ie134LBlB0evR0j/J4X6X\nuOmbYeUBWv6uAu9CzlF0y8UMEox4ThzZbHCQEfMRFWrtrSXSwCYTugY5exdV\n+p8Q5RAwT36IB9aSd90YHhxfZDDsOAdMgT1RB2rvhGQXcPQ+iZvccePo6e9f\nWKM9mI30vufgu1W8akw8upJtIRvuq00LSSpDG1vSz2tSwUUwLnF1emG1ts1Y\nbWGZ\r\n=BzPP\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.2": {"name": "embla-carousel", "version": "2.2.2", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "1e264ae8041e61bc5a564700335fd1e4b67434b2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.2.2.tgz", "fileCount": 27, "integrity": "sha512-ULJ/epqWLOa20M2ofYSYOj8/Bjra1D9IfGPg+JtQnYega1PY9YwyUYUmUBT3RA+53zfKrkmamyeGJX03EuHWYA==", "signatures": [{"sig": "MEUCIFFcYvM5oKN+thWrccJeOf0rnXey+DrTGiOvE3nEZ3hLAiEAiLI3tdVqHe7JPTb/GHvhIH02WrD5qnVbWQTR0/4ZBdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLdn1CRA9TVsSAnZWagAAQ04P/1k+DZW0W1/oaUSWmiGX\nlco+JqsmBq6RvNn7wozA+t/bWb5lmT5gybyVJFIzO2g/Tm+BwBFfbbvnwzl2\nKv8bSUGdrkexwfffSjo4H5wejY4M3jkewe7DLy7YzlLr0ky6hYoOte6+zMIk\npnk6UL5iP+0UeFLTFwIo1WOgau/+fiD9Xz5abu998YgZu3pQMMlJgdCHHQEL\n89V4JYMlk7dMsoyaxCmOS0p+hPhYxbL/LoTT2qZY0P4SKFiHOFpfbgu9ddXV\nd+Ai5ukftCAH66Znrxo/ObrEaVukuOUUSJAgBA3S0ZPDfKZWFyhK9MY6y+m4\n3u0nC5j5DVsWD5OB8IYXx1L9qUszCGMV8VXbhNiCVtzWKb5zU9KTo10f1vfY\n5szoMMZL3T0OPP568AXCWuz6IoWPoz4sxwge/jfMO3m5mQKPrFcpQtWrAF3x\nBhLaSeuQrVxix2fJaiYf84WBOvoXeF3hu+Ror+TYGKFTTlzwAjw8wYJU60UK\nH8A8k6dZwQn12y+YCbzENwbhTS4GUpSnG3HgMlXjSFGulT1DQ4u2PTxLf/JT\nhDy8D6Fci640VAP+FXrlamHCfIgtVPrSb56zP/fWUIkGnVzFtQchLtHERljE\nX6aj25gNTNhXN8va/a8aZpnq3JLM0kJHNQhnlRSpGrUo1liWSRnbrIE3S/fV\ngOTx\r\n=wMSH\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "embla-carousel", "version": "2.3.0", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "7b666600758142dbc4fea45454cd7d1e2888ba8f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.0.tgz", "fileCount": 27, "integrity": "sha512-smUp5HjbxvcvmTnFqDnO9Sp7EHfrwtZCm6T9Fjc2FI+4oLBjdMUoZv0HnUBjplu9/V5bJGNq607TQ0oTgTltjg==", "signatures": [{"sig": "MEUCIFV1/0DKxMYeGs6Jj0Qcs0yU0mm9sBkI7NAqKQ8wHYSPAiEAnSUcfI6EIviWHuYOB1pkwdmVxCmmdjc5f7cHj0BNZR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76021, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLdqACRA9TVsSAnZWagAALpMQAI7kUQXNnGVcAeDI9271\naeCXGOcJYOnrSgFAZw+IZ+2utLQXCY62pBFLjQ4QH2PYYj2+anzj83s5NvqV\n8dmXsLxYoZn+jJfL1kMw42sy4me669Kgyy6DedH2Ra64AVoQfX4opZYq0TZF\nBBeUeiV73Q0A/SVGPex7+trbSWK4uATlmMPvtWMe8fJOCwx4S5HebxtatNEV\nudSxh3GsEX7/hB2pYluhhGjyZ6y2bJFhP9AN9S84SKPwBfOUO9j+GcfORMDn\nx4N7nCUesR4DFM/u3KEk8dQk/0mCFbFE/g35gIV/qfQWT4iF5Qc3T5WCvbn7\nOrdtBa1AEveWB08ELD+nnIS1S8ltGyVFJ/7tPFonXD/2zrsCL1L376hjvXOb\ng5mXa50dZeMr/Nxguf8gzwaZANF6pNfwBatFnrNVXD0L3Z1QPYb5YTCkqVey\n93xeKfb+2DYVXtsriaQMnZODD63KirCWIMaCOwQPt26bq0tdE1jMR4qjNqC9\nDrmr1jeLm4jP9BrqOcbyA3I/Cplk0vbFFTfDTDkB9/nA3NGIWYyfPVoHGBP5\nEbBOAxjhKe/f30TVZYpRyu/2h6hR7MAtH4qa+JcBVOVwjmIWp7UY7P1qbiM5\nfgJVqLyosed8pJ2hTva+vJIgxHoRxFNAxS97TI2RCgUBUN8iD0Tv+I/JBDl6\n4Axp\r\n=rV8n\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.1": {"name": "embla-carousel", "version": "2.3.1", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f3f8e6f7e02e1af2a33bbdf88de9c639011be524", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.1.tgz", "fileCount": 27, "integrity": "sha512-DlP4ggUjl7ZxnoevT24i3bEY598o8IEYgdC38CTqh9XPraPMS57Ke4Rdomuspz14VfTOsv2jt4c08eV8M118vg==", "signatures": [{"sig": "MEUCIAQx1dPNfOe6Txn6w1v0j6HOpRCcSLlcS665wBOycyRLAiEAlcMXMhXxOsCe7TX711OtzXUrg8e0vs70ZVjvKzjt7ug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76001, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLvceCRA9TVsSAnZWagAA2aAP/3+U8nWNuPU22XOcYMF/\nT/2oTBQE2hir5d/2UrnuXdYIFShR2/IrHhSKPX67UdDII1BKG5yalVPulciV\nJ0MR8Idzu3QwJ2tBZBWmDhvUaz0k7BU99O/tse1J0yIuX3qW9nWnCCDwRo0y\nwE+huwRi0Qc8jBtTHGSw0JQLBg3XHkP3gr9o4/LwtmQkPPnBE5zWJcsMX3H2\n5wFtEejWiNOicOZwbr3BIjqciVYWpK8GtEssxRoPQ+KDZIWHywHr6tyh5JSa\n651FFnBVLIwCuHGRyEK1dQGHd24I2TnOW2cR77p+VkMzCyDu3GqBU7Td7Fc1\nV63FaAvsdF2SHAIv1PxvjR+MxDK/T85A+KhiL5+XHZp+1w/PvSdk+Iwblp1w\n1N3Qb6IhxkzlaiKHN5xyd83EXVy53WpgcRKWZz6fpK3Sz8EBQcWb4dh0axUk\nQKZ8UvMYdXDBHmv/LIju+BXRqMgOyk6Df636nmsSvwcG6wfHWRJOs8HJlSly\nzQSXk8WYwQAo5zJQnow7ayTrU5U8u2EL1OURlQhYUqFPmpDaBX2riCmcG+L8\n5MmJbYUocjUU+58i3kPTKl+xrESJDgM0K8kNNqlSymMI5USudYEaxIMb4dns\ndIwtY7Z3AMyb+TYzLBJVShoo/J2zC50FXVqghZBj9ovr3qQcbfrjxxNGOxDx\nUowN\r\n=w99i\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.2": {"name": "embla-carousel", "version": "2.3.2", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "969a08d7b51b4ee8334d5b39f0040def06a0e040", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.2.tgz", "fileCount": 27, "integrity": "sha512-YTzO0nEXOjlATcD5DcCdKdN1Snb+4X+Gc8Bh4wZEG/CGyGigd0cUXolLjnay0ajgz5ZnMIapsjlz3+1Bw63fUA==", "signatures": [{"sig": "MEYCIQDGQCW+im5zc1WwdXTNF7sfjSvT9L9HYkj1RAfhkqrATQIhAKPAdtSlebkwVn/ThSNE5db+JbcoY1RDIVDpHZhWqwUs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNhARCRA9TVsSAnZWagAAtBsP/AiTo0PBbpFkCrEBEuM7\neWpqinN07E9Y2g8LLXUeRqmnYkFlIJdiPfLk5OWjjSHapUL1JhBzhqtS9ecy\n27AFYM0wMC77WTMO51mPWzpNS13gVRZO68CZUADUaHB+enhpa4hrus45XKoy\ni+WBzzXAeyCkr0Sk4Wq4rEqN0ZEtHjwoqn1CkLoV6W90UR/srE4A0FwlrOOh\ntwNf1xbdx66TM7aA4MnAEkyOrCKyfDqHeI0r40N4Qf2zSx+FUkdKYgcgesoZ\n0BZekDgs1ijVYRuAplHZj+eVoA8d0Z8nAj+wZXGlX5EIjMxMy/gCcKEdmg/5\nMawe6+SrzVYDAzsxk0O7nY4lkjvYUIY1K6iisOZin2wu8Y90ZQDstfoN0qxm\nApo/v0/eUVHl4sbpKHHXx48XddSoXHxbaacPgPpgvW7Ms7LmgXO2vG9qA71c\nc/6H4UPaqqAGVw1uXZfQ5F6ApicZrogmJO5NwCBJsH+5WRH6QjmYiONiCYYJ\nAI+6qNEIN6JH1+2mDgmzbgWwJmk5CFXqkT0ocHptkZOEWPTj7v7AvYY5gdfl\nwBhd0BCEAhjRvYD8zy27yEclQKw+KTKc0TQHJbpX+FVzT5ZZaCLgwyboMC6D\nmKDep/dLMM6oeGY57RzgLo+yvwy4f1q9M40Im3KH1/vIXies1whyPsZ/g5jK\nPPrf\r\n=SWCG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.3": {"name": "embla-carousel", "version": "2.3.3", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "47e5069b7c681b031f1842ab63a04ad270bebfe0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.3.tgz", "fileCount": 27, "integrity": "sha512-8eGDEtpCiS9r/rE4LGmR7nPLpQXwr+sYi/GPPdmQAvor3EHXe9P2JHFda9zH9YA3DB+qOnB03n/siCmqtL2ANA==", "signatures": [{"sig": "MEQCIDwnuv1cnQzePFh1qnA0SX3CqalFFLAGi+Fzm5yr4BddAiAzGCpi+8jZZNIcd8LOv2O3mcJygvJVboGqOC3q273noA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdPKN2CRA9TVsSAnZWagAAEtoP/if0zB0Y2nPIdIKnk+RG\nOOPxcbBOn7cx/xNlij0rrLSVsoy400qnxka8PVMiNJ+YpmpyEVQxuB8RQZlW\nmYroCUZzSoS2IdD9v5JJenFmYKNlEtbo1JlHP7v4RcFDq6J+38n3+6kWP08z\nd826YzdlNFro4zcD1ywKnrLLgmVMXERUADpl4m1+MZmsG+Sw5Wwk5jRtA+kp\nmht44rYBRCp+kqRfiVAqzkqiEYOM5lXlKdfLLCKFVtgh5R2PFoXPu10TzQx0\nfc00kzyOJ03HuEy+Fily/RkuISIGa9tt35zwwXn4XmhPWL1oh7E2uwVGKIcq\nupnMaX+W8k41Z1CeLy4aKpYY8t+IBKl2kKhcz2lWGZABWmEm+QOPGzHFp0/A\nK3F3NSbCBztjH9fENEpPgL3ovBq2r1WUAHd6hxGHK2TU0sE7Cwm7ZMGSTM6L\nVT7mHOKnkKFkYAIZEs8Reld4JClpsRGtf+1P+PHrdf17W4GqwI2rbZmqV2g1\nzDvYoWsOZeiywZD2Y2iCCNmDk/NkvFSbKPCLJ6AhO2LCqjZrx2q3vSI3En5x\nTbMJOVan9NYXvjwURR+S6tx5ELT30QyGPAc/9jTvLTDV4+32jmUOHcQUo2rp\nIxRedPVS/YNWPL34HThw67FhtkA/hh9fIa4lpU2Ta2/XB5J5Zf0DfgYUoH/U\nRbUZ\r\n=oD2w\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.4": {"name": "embla-carousel", "version": "2.3.4", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b779693dd25320110046f2638887272461b8d7bf", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.4.tgz", "fileCount": 27, "integrity": "sha512-ix4ncNLaYrGMlpsbYstFvMwqqo6S94xd+H91hUcyhwIvdEuk/l8YGhmlo29eLnG53LN1+r0eOkZnKaBXG+6MSg==", "signatures": [{"sig": "MEUCIQD206Qj8K5VrB0/c6j2a9/85Px7Ud+pG8kHlkd2zwJqnAIgGVaOa+UlbtbrrVrQX/cVs/YdBYqEbMUy/C6VLcVxB04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVW6GCRA9TVsSAnZWagAAsp8P+wSfz4vmTs8el/ILJgA5\ni/KWrFhgfwNehwIApid1B5BpGA2ZL4Z0xAl12ygqcypguDs7I1OB8vpcarag\ncpob1Y2hdEAaaNMqmGnrMo3UEpjCfidrAQYgphTE3jj39MXReIfy1H9bzNxU\n5StcIHYZB8i9SyqzNNl/Uf+c2r7LNkqhQ/Rizu2IvivOlKME1lW5aymflLSX\nCwEVfHpK1TgU5i4/6Tqdh6LE/xRxhQs06h2GNlAW1cSzBmFj7xryoUbhNOex\nLK1tcQL5l6NBt4On7lKEvaBTtKBJKZstJfmbMb7+lo5Mk1H6e3b9p77YXfzp\nYqXIDRuiAR16W98M2TFZrlbT9x8ZPXaq96VeOMJd1nN2K+V1FROITQsADajq\nmYH8CjCebvDyTGt/ZpY/UIcKN2sQ37ofDjwU7D/IL+DXF3aFoSnRFnUvK3Kt\ng9QuvH12ulmUCz2eL8Or3j8DwRhOP6VJ7fRpWn+S99MqRhBWcGX8BLUIyzv8\nrBqbfAJ0Or9lYghHn4MJrrxmkdUnQYFyx5UkdJKdbBq3c4rXxRu69BkBvAQ0\np7mD/+uVYqatVW0K+3enmu6k5CVMiwpAltAJUizKSMhdT7deTi0gP3CWfhVJ\nFt3lL3IxEDdy7ysxOsix75GiEq7hGAxbCKMeHd7RnAtHzH7YWm3+HG3A5Hr2\n2fJP\r\n=pIHQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.5": {"name": "embla-carousel", "version": "2.3.5", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "cc4913ed1743eb9a299699f29063e3cc0f095305", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.3.5.tgz", "fileCount": 27, "integrity": "sha512-DGtHP9NAticXqug8jIxPSDizr2FeSAAlmEZ56PSKQtPUJKe17k1/jHOpSwkIYmyzfrdeRe59fPygZ/mp4XsPnQ==", "signatures": [{"sig": "MEUCIQD+5+QoOTae39P3+WvSVlZoNJwOU1TnBKTBrNVaBWUyRAIgLPcWi+Jqxg/WxqDUM0gNosLe094g6xfcjPEoZO+mpVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVawpCRA9TVsSAnZWagAA6woP/i+iyR6vy1yjAlksMcF+\nxuJgrC5eh2Dn8uGJQ7wbotZbxki1Hdtwm96heAZdqcwxZzrwrF98NKi3KgAT\nIGamiy0xjRy5KzlofmlUlgm8I5Gk4PXeIrNu2S15I5IVVZjbhHTRfshWp9S+\njTrqKyT6h/rLbPQlpNUpaIO95N9wgqwLA28vGHdNH0SCnUV3q4FHEWpN9lmb\nzasnVXbio0EyJSEMIRIRnsclhqt960kwQeyNt0SlJ3UWKh06GAIqgK41Q90w\nsHyaaytfK7OlkkTofLQUwYzDE24/lVU3YHvcthiqQxgJ37Qv38qr37J473LK\nhFpP31U/E3k4LRNQ7DG+v4vaNMbDLamyJ3Qw+tg9toOO0V3O57dFj5xOFB1F\nBdHevsEK3QPqLhjkUXVNVhUy3HhT6ABBjubz28iUlmDEkOSD3UaqkjbNcEb5\n6qtL7XzP6oD9UhJeCdhiEzG23sQn4mWzMGDHc/M6IugT4xAeMO3nGT4oHwPT\nUrxrsJTaL5oldHR7xk52E6V+t0uP25l50W1IFKZ036BegZSvwSjn2so22bJr\nSwOxrrtDVnDYLjaIi3vH/pW8YO2Zg+UKHwtflqxC9x63yS7HVhRaBXBH0mbs\n3VUVx1jBMH5lvf/wT4tqp8tiOdxyrqQgDjqKJXpzDuylL53dKjpRB7NGQxqt\nQtZ+\r\n=UcaN\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "embla-carousel", "version": "2.4.0", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b310621c03d3cccf5665cd628612624819301516", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.0.tgz", "fileCount": 28, "integrity": "sha512-E+I0imPWmUrs0InDKj7fKqXYYaBGvikPirSzTkqry83zx5hHe7Ixu3nn831ODVsbjgWL0q0iE2NFm+NKSn5VVg==", "signatures": [{"sig": "MEQCIEy4GnIWjXI+aK2SRfcsZCWjXVMpHBrl0Ng4pNqFx5TcAiArW72nGOI8830+Vp7LDlTUmPSxmTOowiYOq/XRzBIWTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78815, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaV21CRA9TVsSAnZWagAA6AkP/1j//1px6LHKRLctVlQM\nJCRRgO47hZfY6jJ/FxffcGG6h1rafSt6n6GEDK6BtI6IpjPf0XtoVdp70muR\nXXd6ufOb3ItRJPq3LuaA5jqXOGwOnp/6WzzKc3LIZo3qKtQ5OgWjQ6AByKKw\nQxc31TMi+jnHckKe1e7JklObp+mhCE/Lrpaqar4OWOEJmVvOV4HZfHjdn8uF\nKDuhEcUYFOLKYxsTQaSSC3LFjuqW1JnQByEOiQXy4SC2UbWhBJyjsEY/Fy8Y\nydHL7tNr5CMEP6PS8VakQ1ts1Yt9YlM9yv/NuUKgXJwTkdjbRQ3cbLUzguQr\njWUy1CQ6Ch4erJZY11TcGW1kClYTbx2/mfN53sIZoDBQ+Va8q4JlrUHHTgPF\nX2d1glcINEArZ98Y0G5ruNeWDwlmwHT2cx/qOZQV5RKcoKGPIgBXnLpgKcNQ\nyWz96P+DAoNZ3xeRrX0Vkrj9FX0UckJTQTdWtIRAzE+ZxK6JGaaBElesYRx3\n0VIZZVwcZlpV+oAIj6uH+zm2xm01u+DpCsquZN0IY2P6mi7gq8JHLLGZ7uWw\nxqplMRgKawgED5SUr91DtpINwOCWStm6LWKayTxCvQeA4Slq9OSBSoJNBHzm\nbzrQ9Yilo0TRHmmZ4e6Yk1+LEYvwxFLQsKEE/cIfhinQjrG7Di/LywZtjKtQ\n10dZ\r\n=m8x7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.1": {"name": "embla-carousel", "version": "2.4.1", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "8c0771f951a0ee2a7abdb2119969bff78f619808", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.1.tgz", "fileCount": 28, "integrity": "sha512-nAw1dLzXnA7yXlkss2hfYkOc+PxZV/icP+fj5jxL2zCnkc7X70MRD9d0ondX0YjEz1s/t9i4d8nj7OeEwIGeig==", "signatures": [{"sig": "MEQCIC09uK105pX1hKj78my4Ix31ZK3Yg1MJb0PRGO8EjfdJAiA/Tz5645ho8x3xMfp+p0vieY6+omO+E2E8Q2pPYq+vNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaV8HCRA9TVsSAnZWagAAiOgP/jKeQjuickpxxG6JMvmr\n+LhPoN7dG6f/z/p7EAHjbinhACRaqiObVb1a4ooTbJBvlo/XhnUvU1gkClak\njJGOTy2x9woqZdZvuqqFaMy5iwhXf3oIWPNbIikNPTYN5ASAkuos+rQ4AHqW\nn6jnKSZNLOFFs4fvruRKez4EgaAzd3Ygq7ar6pME1oRcPieNwEEZT6xEiiA3\nV125lFe33kpflf6fgTog6ajh3Yw79Fmg4JAqemaQPH2y5w48po97/PCEC180\n1sd5E2yQ6oNABh0KRFsWiEUhRiUvL1LzdaKTYmMAtUgqJ43AveE5jjcBG0df\nTGuQjreseP3PMovyry1ZpPLdXZR0uITJV+/VlMzg4g1X3G+rADX9jnIvrZcB\nbiLruEgPpXBMu9GxT8PoZKjmn1pWJm/9BuNQezQREWpA0R7zK6UjR9rkmqlh\nnrESqat8C0K8xnLHW8zufLygTCXIp2f8ConL2VLDZcwjDn3qkfXsKkohtV9W\nemra1V8yO3KLIJN7qIn6QbzW35o8ib2EIRhAWrve0JvZTQY+F4lDXk3ciaSq\n/Pvu9L9XTq95oyw6Xsn8HZ6RIaR7EZOyXGNml86JcgftRJmd//b8dG9SCyhw\nADAY7/zjv2y8NIiOTVKO6L+FnMeKHPaWIXoy/b/GZSS8MBec1dvrX95bsriQ\nfAlm\r\n=79gG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.2": {"name": "embla-carousel", "version": "2.4.2", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "0c21d9fcc45d05ed7436e775dfea6bfe6ad91e3c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.2.tgz", "fileCount": 28, "integrity": "sha512-KvqXj7G6kbfTmL18Rggbl9s3tG0CNJ4yXRzu1qTfAwUQRZlVPNeqyx2pLbBzfbccXh6mTg1SYNkWFQ2FJnYiUA==", "signatures": [{"sig": "MEUCIDlj4a5V4Pju9lvNyJKhIqT6rDhbqq0A9x8JBt5vXjadAiEAtKc/8MWor68lL7sToTcpLIe6BfOSJpER2ugJJDpRlgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79887, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbRm3CRA9TVsSAnZWagAAX98P/A2XGxnjSeMmU6MA+yzj\nep/bMf37sxnqGvWCpDM8iRqi1iQiYDeQZiJdQnUu9gVwijqjelZoZKnnq/dS\nz+2pkXYmez9tWI71lN5xGP0wjVZQ2G4p/ozuU+b9A8mFUuMv+PRyvKMxC1NM\n9TWTJAxqXni6OvDI9v9xJNH6FDSCmp+/oJ/SHglYKAvw1oip/YfhqOhVuZNC\n/rYDnxVAoeIyKrc+jQ/fk6yD76/l/nLPsD/gakxcFMzlyfR4ohcL+jCcTpj/\nSgwk9+8uD1Hvq7aNDrvFSVvmtZGyQIUW6H7JR0DSoVvizLT1vvgleureQXu1\nXEdyqNoznN1G0XCsWvFEnSgqjK7wGlcAjsEdTCXF+2BsVbfdHY3XHGdXHtmN\n0pwgxolTPbkewaoeBQmRjwLKHiUmsZLEOmM5zQWtPHmG2obU3wznzC6s76Na\n4HH4TfUpCtqBgfhIwcwj8WIgCEGcDclM1n1PsMhy2pLwaNlo3Dt+pOXEuBC2\nEDkz3DleeSFxF4N7+YlMXw+Wm6kSQpSz1zgFsaQ5gG+lrrd+Hkt58YRTd7Wb\nR260p2oLKW6hmH0dHGC8KfFgye1OMEWS2hly7Sy1SHje/Zbjprdj3jT9ct1W\nQZd3SpyLHNFu2ghTpCe1mv3kWTGvI7U61EnSLDdFofLAIEANijVyrL/W68LR\n7sVl\r\n=/Ivw\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.3": {"name": "embla-carousel", "version": "2.4.3", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "8c8fc99e54f2429a8cf1134f79b7ad564ea35e45", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.3.tgz", "fileCount": 28, "integrity": "sha512-+aTWJqFuEN6Vo6p2vp3vMoS2osKW78oaB+nehmZxS5YP6osM0uYGmkBiYJcg1eO2AdhAENDlrN7VlFWJ7TbtSg==", "signatures": [{"sig": "MEYCIQCxlpE2ksC7TVDMzQk8m4Lk5uwHePXFq7t8WjFuCCUDjgIhAITnwWqGL76HvD9xGEe1N1WKxElUnksTvbQstwVVuLmN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbRuVCRA9TVsSAnZWagAAmXMP/2x1Dl7whPEzOspVGV08\nlZmsOXBDAt2XzL91bMLaI1N+NLQqU32pqiWjM/A9Wh0h8O7fWtngKf/hcOb0\nab90YsGOCXOYbkeB2oggk8E2sri4qUA7qaULJS711U2HIrbBLDOvBESxOvIc\ngOFNq3quNaAOjVuTErfARuLwfUJX99YF3mRTJcxyZ8qZK648tT78pC1OiTeZ\nwmk4B8UMKwWUqJY5QGtvhwpzsIVUiGMyRoUBsgZJrlf0tCOawaVG6xSjzNga\n/gQV6gYYE46g3YLQ5oPZZmdI7H64X+lGjgJXhh+mTXdZE7Twi5tJAizJALU5\njg1t8fP0+k/t7iF3RaPot96HL+fg07NQblRYwS9flZJrU3A5FOf4alW3kl67\n1E/8TSq8khaLQKO06pJjWuRrhoGHS8wQEFTyuCPF4SO8Litda0wE9tbQHZy3\nIlALkhW/J06quXXrjoF7hjZqXlCsu3UqeGBoN5Cl+hBoSiY0GjlnyzrMhyJj\n+qlzZ7P6O1Nxy7Hc4CR2j0s6PhRomGfrIKsyrVUQJdu+HYb1L94fyq//IHg2\nXkTHkvbEaM3UXDlId2Or5I579hRBIn4Se53p1RHsbgXwt/Bl0kb80KRYl+lb\nFINWm6PYQKlUmLLxiEDhJW32p0U6KLULRomUqkVbsSqIg2gVRxr4rL2zu4hi\nJZM+\r\n=MVVB\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.4": {"name": "embla-carousel", "version": "2.4.4", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "05e31aa043e5d0b7849f9278c4abdd5cd8aec220", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.4.tgz", "fileCount": 28, "integrity": "sha512-/74LIgYTseRHCmLAjw66k768kDJIlC0kynZ/GxR0qS9KcUEGEnZMGtBo0Rr+5wB8SBaivpUHGXaDYFJ5N5pL9Q==", "signatures": [{"sig": "MEQCIEO3GVmzu1ckiV65Ee3kvEx03iFv8QMp4/ULtVmIZYjpAiB59/u44jPy635u26pFuHx0ThE4LT6maqbp5YeUVsUB6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdbVhUCRA9TVsSAnZWagAApzwQAJROzqn47g46+8Vw8xJv\nXccmNDSWlHUV2Ea5v8/W+3qJIp16HLjkQ54NcMUhvWpdxANwQFvOSQKH/3v/\nlTjXv9obRZKxn7SIFU7gOqiOg5QVq3ufDjIi+0tFIz011tDCs5tNdauUzvKE\nvXe9ZyUFJ5VoGhW/TOJPNRhcaKCKADk6yC/A3eV0SSmrkizbA15c0UcYk2xo\nge/otjvZccdYyq92OtKBwrWUoxgwusi7uuM6VKGUQFy3OL+wBGLWfjCjhydA\nXpmuaWC0rIS4msMSkK2jpzChb07Yrdo2fpHfTEtrjs58mibMhIFEuvH8eh3y\n9fMTMcTUWY++jCYmqxLjosLFe6AKnnwJ9MPGa7jLPeD6TxvEerSvGb5p8YNZ\nWdAkS45WbsbS68JZ/82PjdMf/u//332WYUCAurtxIwXFgn1/C71ByiAYEkp5\nNZ4JQ4f4AI7UbsQEIWhDF07OYO9S5dy3TQphBTogJZNpQOmzxON+VMhBAcdd\ns6CFsyYWzeyFqInesD9HxLdrdAAhbmZ3HP+qYunklh6grpH3eWmSdXL4iWxx\n4l6dKAy42Po2BDfN+KsipRYXUlfqTzB10xEJI+XhYLOguzMLWqysWD1xX3xN\nS2fqHX+BoJ1FJfJZv8CLFZGae/buuEEjFfT1yMmwRxa7J221iWuP7UmzfG1Y\nItu4\r\n=+Pfu\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.5": {"name": "embla-carousel", "version": "2.4.5", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "0a2d6a261cf24f25550bf7a29bf3b232cae9c508", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.5.tgz", "fileCount": 29, "integrity": "sha512-yCPsJ5+zgYsRleiN5aHgf0KOJpQf1ioLluYTyN3FnWGbfftgVM3rn2+V4RlH8J6piqn1zISfa3yqNjUKC2J/ww==", "signatures": [{"sig": "MEUCID3qQzW+GHQPjQSBQGW4RVekgJc7/gSKZav2ypzWLKfpAiEA+6CYzKrnfYYs+yn/yNOqDhXyflnxq/NtkePx0ttlUCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81110, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcXAMCRA9TVsSAnZWagAASOQP/3gQtnYw3taN5cnBbM2/\n/9DiqVoViyxlZlNzODAV2YrV5gM7dXf4l4SZ5x3FXr3Z8sOp7I/45R4ghc+w\ngGybr6G1tlmEL/tKLYC/RQjydwiZbZ44935OPSH2nNbD09Z0fWfVFd6wNplq\nRsJnljIsBz0t2SGGuHoN83mFG7dz3V+gux+uoPFPhtDiN2vsYvTAW47B4CQZ\n2nx70sT9LGDxrWJsZaXgTF785j1zPzd5YvereDYMCUxsV3SrhHArERHX6lrZ\n+fHJXsH3Rvayf7qvuEXPxsd/hHdpus9gyGGjsK7akpercZqv4b0UcsjoNrIg\nIRpDgekrinM21gCMg0GkzTdWwF2wPEPvObDsaVHYj7TqOaSFoi9c6DMox/sv\nmrgKXkMsvBK/ZHLczYMya6x7SL/7IxTkw8zZH8/PB1IkXk0bvD1dS/F16Los\ntwGGdgrwgODsUcTaqALpoGq6qaxKFchgPzFnmI60lZIGoD6jckMLYAOBHTGm\nWg5R3NBt24m+F2NmWfPKgI3d4163zMEFeinkw8zwi5z7Z9skf+lIwBFNXN/5\nMmR7WMZ+MyQzPw7knLH2CTPLiPCycR1nEdtZtPbrxWlts4MPf76R/OFcBgOI\nF3GgsDynpydRxgyYD5otAvkDZFkho5CP+bJGtExpgOR7fZYs6E8fDvIF4iGK\nRkAX\r\n=EewE\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.6": {"name": "embla-carousel", "version": "2.4.6", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "24b568e59784100ddff63c3e7361ebad6ec4651c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.4.6.tgz", "fileCount": 29, "integrity": "sha512-22KH3JG0yxyS0WIwKHxLW0GfMXBc0V6agmxFW8jkhK4gukLf+DZ7dVP7EORr9GqiB5jrOAOhsxgxWCZKG8q6iQ==", "signatures": [{"sig": "MEYCIQCi2J8jJnW1/SaC0lo16+2VWQg107p0rml3J/ZdSOtsHAIhAOv6MrH/4X292gp5QIvZ2/c55mDn+2KB+420bBS9mEbd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfSJ3CRA9TVsSAnZWagAAKPcP/iBJzBRrneDUAkNwtzWQ\nRg/cCknu4q7bOpYYWkokqAQ9N9+Nf7s8rx+utAIL1Lcc7ffXvIeDXJEDIzDK\ndAsYy9pgDJX/gIhNwUcX99OfiM16GMQcQF/x6Xj3qzx5+IHDSgu+kxliNQxl\n1BifCUjG/6LdDweA7wis+PPt7N9GQJZ9BKajTAIlYTelOalLSZnW4XK/cv+g\nTwiAONAPVENfSUay8Hmr4Npc14rnb8G9CudjUFQQMn4hJh0y9QE6lrKFn9zY\nwBJ/5gWQxC98zYT7KG7LiS/Y9v+nweMOvQWwdME2EDSyxLIL6gMTIqfRPRkS\n2vLq7Mr7G0WPSqSrXslNbSgqPGMzi9tX+mfh8Q45eQubm2yZph/DXLJfd3/S\netdcRIgTKuqKQNNvcFv4oQxoobw5rCwpg+FIORInIJI8s7ujLmh7VrWH+KXv\nJfSqAC6x8Rgj68xgf2861lntMMqlXlovRT/v5jzuJaE1ppILbVX7pQgFlNJv\nEmMyfo95ZzTM7sOCjd/tt2IsC7xPsSHzS6qR254Q9+HVFN3ifE7Qzvnv75Ps\nHlU97oy7HsYG2F0u8/AOKpgXwWrU5jSLen/MGH25rdBUwoHNkHeivNEprsv8\n9IPlSigY5llJxlfrgmMaSX230GQTIeaW2gDFgZILtP8Pd+T1ra1Z0GTkhMpQ\nzJbD\r\n=ab0l\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.0": {"name": "embla-carousel", "version": "2.5.0", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "34263158173133dff0d233ff6f44068c9d15d584", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.0.tgz", "fileCount": 30, "integrity": "sha512-mQGZagNQW0+flBiJ2SVQBesNHVmzxCyYKgDbu8VAmNxcQmobeLaZXceuk2nHErGqJ5ZkGvbqdAshXYgjfEf8GA==", "signatures": [{"sig": "MEQCIHIc1hfZjJ7EBbBoflmnCBLpAITPJDNZwlSTfADk7eyQAiAZwZHLEHA00bCb2ygZ1/J5tW2eCQNXzl/qUm6QjV/zkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmgzvCRA9TVsSAnZWagAAQHsP/3kzh5ozPszfq6h3D6vm\ngYO49HeViri2yEDQ74Q1fzQsA5Df3rI0/ZPKGZUNmHRAY+NCZUuidPXrYL2H\n15wyAo2+KpY/GavpevYpkSvYJfUFmYRNf37sOlLg99oWYdobyYJel7HqTroI\nW/VX8bvI/kqm/G3OAv6E8tFJJWYkBlFe89atxBz5foaAQ4TcIG/hdj13RdQ3\nQiFVa9WDcipeEO3jlMI1bjCiclseMpmSeVcrXR0tIMF9M3rx2cW4qhVNTzk8\nubl2M014j3pFeBZli2dAgF7qi897TZ11D6yyE87WlAQMqpP0hZtbnOrNwzmV\nQsMbgncf6x+R1/pB7du3qAkuQLOx/7c2n9JnOLpA2Hf2iWItMljxhhUwfBLr\nNtyPl7H07qim96/T8A3qR6fsMyXUdwuMBOlgzFJZX4Jyaqa70wgXrkuduuRu\nuxMfqb2a/9/1A0C/YaCFuv/c6gTHpzUwjw6BsPHHrgu8E/F2xQm1qK4+x2En\nD0QbOXiFx+mh8mWn6ywSXrkDPl22GAjDX9ekJUcSI+50hOZLdOQAvwsfGsi2\nqFp5KgYVcI9JS9ZBZO33x7Kex8FP/k+qXh/DKYRJq0ygTJ7AxzK3o5vg5JKm\nDAGfxwYmYE+qfv5MI3yLYofH9fxmDn6XVHaO2Ejkovd/WziPZ2n/4knRUjR+\nIk4G\r\n=NEBp\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1": {"name": "embla-carousel", "version": "2.5.1", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "54e5096fbf419960f71bcec9cc3157c3e9d1f0b7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.1.tgz", "fileCount": 30, "integrity": "sha512-zA+vMZRrfjUZnyZJEd1uJsg5rtrh/9hXWwRYELDDsYNHcMnxXZ5/RVgtS72pYQowDz2J/nHCf74R9TamIzvUZg==", "signatures": [{"sig": "MEUCIAkZgyqPehaJ7M26Rsrbg04h7+FB9ysrNRjIzGiuA0qaAiEA1S+2QFB+m/Lq0tg0YO+AlLaJUCKhGtA2284z/lT2+zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmhDWCRA9TVsSAnZWagAAfwwP/3AvQ6VU0SBi9HCjJSFV\nNToQd8kXhJkp0bEs6fa2CGkKp++T0y25gl0VfN8DO9fxaAXrmsXcCawHOEqb\nixah9AG0qWoIArI72l++3GBy6Gz/b8hrD6+tMEX3XehXsyyjWBuoOzABROIS\nF47y9GhfWtV2srMGCPI9VmtDc+vHBFXe6Z9ZOeCbSX0vpB0c0+bCNN8WrXRc\n+Y6q363dF0emyvK0qsv/HAzM/sv7T+IrTqlwCh1DJSiGW0gZFntgCSHtI8Wy\nRgZcxJT4yjF9C841DEKkjO8pj012uk0sod9t3PUlGqWSFkxViFA4kJ9NDLsP\nGpkeKbXO3sVpG61sGWO10mCUF7x4EXNrHqPzz9cQwMLgCU9YFBsU1Wt5JO7K\ndVckcpFxuPn5En0Ffn7hydBQyDZAuLvkU6UBhK2IAUr7MtNUiMYijRhmj/CI\n5p/polPh0o1cncefB9j5xtXH6YWtmuxC8NWnh/T/YrAeOJzzHQHiCmTC7y7n\nIgz8h+91122He6B4jnEeZt42TCgrTXID+6PEeUkb9mPiHMK9jKsml13zGUGz\n9HEx2fvLi3u3r/KHj6js+Rn3vSzKdpIut/lnyhjooVnOnHrImmiwAbLSwbWb\n/EY9mpRXczPlbCkqiWMk3AS1OBug6YiJRptfaMR6hInL0ioTReN2eZdwUez2\nJxSx\r\n=JGsE\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.2": {"name": "embla-carousel", "version": "2.5.2", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b00d10bbde17df9c06b6da7d4d8fcce1d5b01726", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.2.tgz", "fileCount": 30, "integrity": "sha512-elsjm9m5FDBunokj5JWyCiRJMypaNMfpHnV8QG+aF10gtTYe3HeSOgRtpLg2NMBcAyoMsnQVGdo/I9Xp2q2MtQ==", "signatures": [{"sig": "MEYCIQDUFGsGNPzv+5MAT/LBaQe3Dwo8JQSohb9l8DKOjeIErwIhAL7++voSSmypjqnrcvVarUyrl9wXb46RAkvL4onX7MCA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmhQnCRA9TVsSAnZWagAAONwP/jYPNn/2cf0WYS64ZFPB\niWcPeVu73idofqo7RTooKYu8TjTSGJ84udHDITch4FDRXxkr4mKhs9qTSjEm\nu9Dqud86zHa8ZseRTYdXfIfbsdQU/g9PF1eC2UJWcSaLGp56w1psi0GiQK9b\nuBw5+WVVR+0hbHfwfblOFlLz66tQb0Yi/wLm8RKCowPVyLcYto+wilVt+LKW\nR6n0DZQhqlSozmrSg/No/fpVW6im5DSrMCsmr1zx3+q1hJ77zwrEXJPIrD4a\nmJA2X5cnz4EDn94DgKkOVVLucdB2qY69JkeRhhizSLHAjYLe+a8aVGEa8Ip0\nqSKqLejFMlVecIk7N4LMJ2zvr+DlzZlSmlOaFWhZMFwMP8RQ79FIutSKMN+7\nVQjwGn6P8tN7uHdEs2WALgdtpjt1sWQ1GvHnmWirsxZ3fN7W8gYlbh9U/oQz\nKgOlRDzChY6/jp47pZRK7BdB+yAkIrzqUej4n4pSncFzmjXeT5P9pLcg5qv6\nHFedyfYlTKZpnANlQA0ec2/KlN244iKeRlFi8zw4mwnrtoIx8uUcTwmnJygi\nna1rNSsL5KtXPyDxNWxbDZl01c71rKEmSqh6g/4rIRthL8z2quHWGMmd8DTQ\nOJtF3mrQUCGHE69+6lZpFsWMraoictijvKtGjX4LuxPty3PlvLl2XCKRhSkw\nwBb9\r\n=bSFO\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.3": {"name": "embla-carousel", "version": "2.5.3", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "3ee34ab42eb8e3ac0ea8ae6763dbab4471b1cd6f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.3.tgz", "fileCount": 30, "integrity": "sha512-Y1VBUkp02kQANEc+MFTfy+uOOeN24ZraJpePDu7dE+ffawt6DXyAzjYVwQy9H7avdMrah2tc0PIslATyXU1iIQ==", "signatures": [{"sig": "MEUCIFgztAxWArEHEZCZl/H5ZU3dz6O+HW1QzfZAkxlGgYhWAiEA1h7MXfhhXM3Wlj2Gz9iSB2XW0t/eHk2FydIw/UNfSJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrqfmCRA9TVsSAnZWagAAESkQAJ3cpCldCferJRnqnGL4\nnX6/MXyMGr7KgkFGpZrETpvyEBYmbYtyXORZTRut3LGxpNapHiGXvKd/gn1C\npxDvuE+BlVWIFQjkPSZsZ5ZZnxpWRqVHDISuh58vPMAU/2kRXOQYJb8nXoNX\nLrcuJWkQvkLE9lt5kwsXOkOhohA7wsQNnPMdk5CodzB6qysTPFk6YIANnvNw\nVb9a5/i972MuUH9w3/eaBuiBJwHnJsW27OC4sxm5WUAL5cGdvI83f4a7sCsN\nUt9K8U+4JFdchVQ5Jsy6Az5biQWTbHB853ZCNFQZk4Z1Ssn6hUsLOq/FBjAy\nMrNKL8vxW6Z4hnfp24Ra8TziigU7u2S7/vq6HHO9DaL/iR0Xml6lPN5qURHM\nyDRKWL8QKfrRdSTUJQLCbl6m5ZrA3QAGbTSaB3UpX6JZ+XyCoiFhxW9rXTRI\n0X4vmpk0UVDVUvZnDBmu7ZjJtH+ZCC5/3HZgUQq7H21jCRYswowfsylQeDV3\njf4IcSKorVdShQiOSK9YmgLXy90QFfqopielAZGXA00XsZhdNn5Pfdu+2LHU\nm1gkAgegsd1xRG7xflhrM08+l7QhVIcz8GLBoUiTvM7V0E0ftMREJsHub3FB\no1hn2e+80v/PFWyqOkAxxebjgqrviSu0XO//1GdRiUYXZDFSYPr7+PC210ue\nF77j\r\n=Rjk/\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.4": {"name": "embla-carousel", "version": "2.5.4", "devDependencies": {"jest": "^24.8.0", "tslint": "^5.18.0", "ts-jest": "^23.10.5", "webpack": "^4.35.3", "prettier": "^1.18.2", "node-sass": "^4.12.0", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.6", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "webpack-dev-server": "^3.7.2", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "ac41c73b2e69d7ec5065b71ad5a13c60ae0d961a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.4.tgz", "fileCount": 30, "integrity": "sha512-HfDMMi9HqCRgWvgPjdJD6g32dQzBGWgMLwSr6gWaUCSSV6Stt4gxxVJx6wPatc/T3l2h7L4FMMZz2d0B/YP6IQ==", "signatures": [{"sig": "MEYCIQDHP/RRcL50meIYhtQaz3IyGwI7KNhwbMt7at6djorXwwIhANZCA/PHxR4XF1cnUzpJIRPBKzx6Qo8iZYtloCg0m4D+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84458, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduqBFCRA9TVsSAnZWagAAPRkQAKM6Yzu6UC9QS3eT8cmu\nrQhAixxliS7mND2e70KXa0S+lQ2UnttWrAKQik+1dG1Ll1G0eX/jcsnS8Qp1\nZfiAcStOWHhZxqnQ13Df8YCyPHEyAPDZ5ItTD0Eu54/yU+HSDjg968qLBAOC\n812v/1AdgS3wNN6Rag6knLssO2aOVFipOZrpU2Hz6z3u+V5AQRQyucz6MGqc\noP2eCsufd2bxMd5eVkN9EmI2h76GG31QYbvTYTzw3MUW/HJ2CkyxU4nOV2Gw\nIEE/+y2oY5ChudywaPQ3slD1Vd0d7PF7qsAiZ9mvgEcleZdK3Fda4d7/2pBp\nUFJuR88vzqjKMsUslm6P9xS3iG/PSlVOjxtownSqdtlL0fPBVhHbi2t3gbWG\njbl7HuVNN7Ovi4ZdH35ahjOGHsA0GC4mbgFNURfx50A0e9Dg9DP+J7D9oV+l\nFkdD4J8iq/uVYbSGIM89iOZBXsfa5V2m+7Z3JWdgTW+x2E/afktVlssqneUh\nfRH0dGZazRuxB7RoqjR8OBM+/xJMeyd71ZEnKkwyWsRwLMsxkrhFxk9yLoHl\n39lFsHqN2a+LLXi1flWV+2etNPWMeJ0oS1UG8Lil969jUQl2mEqLiYlTTjT+\nr6qiy24SeEoMtgsrghCBiXgB394CxV0wHx89s0AdAb7WFS6KU+zRiKfBZHwC\n7SY6\r\n=0s91\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.5": {"name": "embla-carousel", "version": "2.5.5", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "69c83de7b7d7b22648d78a52e42f7e3a52afb3be", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.5.5.tgz", "fileCount": 30, "integrity": "sha512-DSkPXz7to1avH7KSbRT8lIf3GhtpQfVQp7Hhe9KWzF9DT8DcE9Aq40Z9CQMLBLJg7LdgBun8nkuHSdfNuI2aCQ==", "signatures": [{"sig": "MEYCIQCEfFDWhoxtUdBkPhX+VxLAtpfBTnd/OhIieesJjoxMlwIhAMnuuKz15FkUNpn707HyLZWK8o79Qeg+4t3IPsu15QKE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84459, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxrHLCRA9TVsSAnZWagAAxucP/idiOHkmBS3+jZxOV48t\nhteUiaA1YuzLWbvOEB+H0YXER81uqX9w3kMVKsmZeT7HbUkmGKtc/83bfZOH\nOc273cHDDOuTgECwGR9ygP310sZAOdzk+CBTd37dmLnYfN1/ZvS713qAU118\nAeT/Lnxc+yJBUKIkRY+mkAVFie22So7kc9PiOUOKlZGbGYn1pW//cBYvgvoQ\nBER943rKuhkEcEhcvEQGreGdMntYDWxl6oMSPbCD1GWdZfbsEPcf4+IRNfKO\nTvY53vQCNym9gpEGymMuXrW3EBen8X41LMV8hsAy3OKX0XdP/F8CAFoHb6D4\nHep1+3w9NsFph8y6SvueD0uEiho2FbDoGGKU6QCzHPCE381iFz/i4EHKa3LR\nAaDgivkvZMjoLEo8VIdPDonvFjLCMLQ9dUlxtIbHrw9RuB+geATj2VN59pE1\nDe5L+I28yJjAvZMzRjkGWqcep4KD2iGUaSHm9ZizNzRdKxgR869lsN7UBZ7w\n3CmQu30NR/9LoEjSOMqfaPy7WxUsKXukeZjp0L5Kbhi+rgd/yzYrZrJFEbq8\n8cd/zkrNOn4YOVmK53Bpe68mkraolDmecGYyrmOl+ShY1cLg7alsf8Lo2Kld\nKbEqfdkaW/lhFCg5AzVWXRW+Xf6Ro6zANMRwM21GcMfoqftJTjlmpUjO7ajx\nJ8Xu\r\n=tO79\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "embla-carousel", "version": "2.6.0", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "4c2caf879e9227d95a3cc8f0074285913f224a0f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.0.tgz", "fileCount": 30, "integrity": "sha512-QUoR1RBUfrM7aaKFcHDLa+TMyRWnXUzhEjZ8FPsoh90AarZ17k2vMm/rDQDg7LuNgaXlmFOrnbKFwzSLyjW/Dw==", "signatures": [{"sig": "MEUCIQDDdQDaqRXHFuudfs4ZRq3piMOYFCiy+P7VMJC8xLUv2QIgL1SbG3bVOXNiO+zJxPe5i+xOv01J8N4YzE0g0W4yRL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzWq4CRA9TVsSAnZWagAA210P/2HhSe/QaS+FM2kZBoD4\nz593nw346TKyUc25fQRFrkZZ9qLBmdHuEEg4IUO2XvSumB0db7ElLKVMdyqb\nuezQl5IqQELPfCjJVVgNRTApygTlQVCcea61UHjyenJ0uTcx8UtwMxYfWPGc\nYZEBM0s3LDVQ2NMmZHqHVCwJkkvFUc/TmgMlosmLUBG7tFxfmBtv0eSk3E2I\ne9Q4TWOT+OtRueU9oqn+xbXwdGbfdrCAXmZGNuqDx3ghRnKbd+EHWy5Hs7SY\nrFlfbclaSHHSvg8Ag8UWexOuC8D9gp97n2Gl4rPxxGkSRAOtDwIMTyJw5Y77\nyRBhugo6iZw9u/OKDOxcB8aAe+zWBOZ17/V8RtnI5F+f6raj/1qS4B9HKNqx\nxm/OQ+8WbwwIg33JHogfROHeK76t3QGWaK2jGqPcXA5+F6EPz9IO0QskVOJh\nUuOAZ1fHrZBjExLfZZeHo+FbonjPbjJ6+xB1gh3XPqt7apBPjLRGrMmEbXWd\nxTymj570SyWEAXHzPxgY2AtXUekM7GgXJhhCf/YrnaXlWspRGx1gUHJp4p2k\n9LaVbFLv334e9ZVCYlfF8lirwrfC4Opmxk8r/Fofn5Rj+12SibXC9f6aghUx\nbVgU+4AxTb5qu7jzQCGwxX1M0YB/3COWEYnWD+0wAnxSs2p3guqT67j8CcNq\nM8p5\r\n=UovD\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.1": {"name": "embla-carousel", "version": "2.6.1", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "a9fa61cc5bf399f2068bdb28f178991e823dbf87", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.1.tgz", "fileCount": 30, "integrity": "sha512-+5X+N78DmWSH2n8bsYXHshai7xtivw9aMaQ8qNsVm7KeLLOOirvwANFM63z6clWLVlt2rPrn935S+sFrUc72VA==", "signatures": [{"sig": "MEQCIQC5ACTthDmtEWffyWNnSXIssR3JV8gDrp+J3AF3Sh87awIfJOqYmzySjFvdFuaPXKcCz+tcmPXEwvNxgq+eSS+mHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0B+9CRA9TVsSAnZWagAAPYMP/0g7Z7CRwHEDzjV6dmTA\nH+IHKywCuEPFy/01VVdCM+SCCSXn/tF/gdJddi+Uo4FCsviN+BshDP9nl2X1\nRb/5b4N9pH+wzgkPt0Ct/6nxBVvdJhopryuLvKnDOcxBUiXZ2NkbvmLt6JRx\nYeboCrsgWXpK5T52lWhgpOfDRm+bG1tLrfE6VvDU2MeNERLqmYlcue1+9EZH\nOkwmPRDG+2qM29s130sq6mHnrWDspyYjlRVDp9xAwoEABqtlmCrEUZIDvuLa\niPE3Zfn12ctFCsI8rasj/1Xo0GlySfOm7Rn3CudW+3lLMnlsPDbvVp5qTIn2\n8B9qa9kXav06Xi0vaSwYHPXnb6yHzpbCsrqb2WdyIdUJOgDDvBaXvY3fW4C2\nOoiquZsUVvOgQns7pe0uwUX8p1WbyhGSe2J3txJARVBTSYs3G9AlNtdbqDqE\nIrNFFxSPv+x21DeO+Q941P/MeBULYrIaqLVA41vqVvpxVQ1RpVDMx1brCrqS\nu7WCeL+h/boDHqHNcM3O3ZrlmNAuy5Wi8Q718dIBS17HjRkTEhyLVPOrTM6V\nar0EwUVUDpYgl0VTTQovjfcn7EknRWFysj1EEqDKBRI3J7ix0oxG07qGlQWl\n68YeABYRFLVvWCmgNOrY+YtGjPKTV+LYDcS7pENIEYexHlndZiSKUMwnOJ/3\naPC+\r\n=Z8wj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.2": {"name": "embla-carousel", "version": "2.6.2", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b0a75ca2f677eff5348049b0de2646c53c4e12d1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.2.tgz", "fileCount": 30, "integrity": "sha512-hNZPqWcT7k2Zd2wCsSsGbI/qjoqAxwQ+frrBWeGGnIq4Dk4KVjseMaIUA+wJlnKwiVnCX3e0s6V7kjhcf6xNWQ==", "signatures": [{"sig": "MEUCIQDMXn4YPru34oVWm+x3pPOB1qssV5V+j0DcBm8deq79uQIgDorZ5rVSC+AqC+DJJmLmQEd/wRZv6waQ8UUiqeH1Ijo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd07LgCRA9TVsSAnZWagAAlB8P/izOjfcbHxBvEdKj/Zau\nblyeZtXRtSGnLDpOXEvX2noM7EE9FogPL7vOqYhELMYgtKIebcjs1rkM3hDb\n5LN6amHwuEeaEJJ3iFLZispxfiUvReWNo4Mr32ii0DSSFFSdaazdLSXfJae0\n9fJkjqyIjHt4YFgjLrUWtuYO/OLRd6/0PZ9ANYxz1QeryXZjobm9LTPPmG3j\nBVLw3r+Diabb1/uS2ESX4ua1Vuxac9bwRzZoiM+9IDsUsMCdwxYQGTzqmQzG\nfaNVha6SRtijP2VVmUoWu/9nnA2KGDVBCzFJhRRyGIZbfLJO2qfOGiFKNvCW\nX3eYGSYHYSelY2MfIcPl5fZLpq+bC+jBXZ92oimadxfO5c5DLfuNkTeSThnL\n4AluwPVqc3rYghAheS6Kw2ynnj83UyhdvJmgYtoDHnL1+QWqKKtJVVcky2H8\nBZ3ECyuo1m40WI0y+AQy7RNObKouMcytht4XT3w2bCKbxS/D0aMx7/RtYuRW\n4suZoc0N4xebjxMbpBlcKjwUQURhORS4Y3f1gjeiUF8VQXBMlcRtnCMJi2aM\n2D98728kZR9sygNj8W8rKjP5eXXrSTHHWjrhV5+62xwn5j53HQ32rRtWMuj3\n7GihUeefgE/Xa+iEcXztrHU70kkejhjk8R3RJZ38mOi5Y2wOPslnCTzPqbnk\nlePh\r\n=q2Fz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.3": {"name": "embla-carousel", "version": "2.6.3", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "9371a43bb6aec31bc82ef93708597b791ba9e0a7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.3.tgz", "fileCount": 30, "integrity": "sha512-6hqMHecohXttG39Viz3T6oxoTPvMSQ0SeTRnh0581ZZ5zZ7dHvJB0rhqFgyr8exPFLL5BXVpBWsLlLVjZ2jK2w==", "signatures": [{"sig": "MEUCIQDZckEicYPZj/W7NuLYf8Rq1Fx9W8gSl86ueQDwBgj+yAIga3VX9EDf9qQJ8ZkPXm3AriShcxeNBq8pl3vzIw9YbhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1EvaCRA9TVsSAnZWagAAIucP/0buobM957awlk8ob8iS\n4xxk/kL2GYQbMivUKUOeXC+YWDyRGwMt/yHl391MBbjn2qmk0v+/sU49SAFu\nUoNgCF1gEpeGVxo+Ihs+kk3tI8VywBLGBRTsACIsIGRTmcydJPcbWSw8NLRr\nk8+DfkfcniI+pFf24hqNJr+eRUgTr9OOZUYmwoa4PlTO8cidpF/6hkdyOYaK\nF2jNVCmftzZawY2WwPg++sy9qIv5AcEx2smK+avHm9r3K5mCPcZkFS6ENnSM\nI5dCc1heZHgdLpfVKFoL+P1trK2zfhOCzTQalnH5psOdFg5wEsBiSNCUsUB9\nPe+IJZQAFlYT2/9DApnltCQsIj9Ce53jT/SVdYBvokNkyy8trdWX84HNZGsh\n++Rom8wh2WFqonIM3fu70qlDUrTPBuXqn4gweTA5vSUqaQ2SdQnrJZt8ygMJ\npd0vJtVh3W2F/tqtzue56TCEvYsWH3RvV96nQiu1oNQ9jpx/OkKtNo4xLHYL\nxtbbaV2/EUr0lqEjLYkoeBk0VINsJZcDjQDZlGgAslE45/0Shhj80uBf9r2t\nfncrvqpOFlbyuqiceMwn2tF19u8wqWCR4PIAII3moSHDQzGRpbgisCKQAJO+\niWliRTZqp2wnILgQxb4RDxjphBgVbJeAMkCrs36w5LXyy9gvCrJf9ItfAFyr\nfmYd\r\n=zSPX\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.4": {"name": "embla-carousel", "version": "2.6.4", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "feaecd4f1e9b02bab42044e3a5685114f8454a0b", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.4.tgz", "fileCount": 30, "integrity": "sha512-jQiEFSxSey6Djin61Ugxmgc31EGYAyvXAYcShVoZiSCQL/nvHERQrwrPgYxsDwM6qiW0f1kBwXU6tpHpR0qLEg==", "signatures": [{"sig": "MEUCIB7uooerurAuC3wPGfENkr6Q4Uus/bIrn6jdYwJwhjVgAiEAvhGbYu4EmuBGNYz9xpqWm19PnZRqK0HTPwo6mN0V2Gc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6Os8CRA9TVsSAnZWagAA89oP/1bmZI+JAZbQQSAKOlVF\nPm/TdSJ2Jfud3046TaPqk/JYP/TydPSj7+IQq51sV7MaGdkKgxyVCGsacyGQ\n3ZinN7F16z/G+PFOlLjyxRwlwi9kALTqx1CTAR6zvVWirBUfBA57SlUBn0c8\n1VtyhjBOm47qRJgFOAMD6PJZYYRfc8+YZZESWicCURwEPoMqwBD2LRIACFR+\ne7l6sRsrB/lAJPUgZsZsnvM/y01xlQar+TfC+AbWXM4f6MxJBpTKVW1MilE9\n4DpIcqfDD2BU4r3aue2FjTiLxtqe0pg7h6M4ESOB7NAzOLZl//n3OIU0uE/g\nsPe4ppB9bE8BHVWsZyrT7BI7mvXULRqvWWIpaYQFrL57gHf/ylAwmBq19rPE\n57Im6uaC+jiYaVKzmfDpErf2I7R9X8k2aPstErsHsO1l9i1xBU2EUrkEsGfg\nG9Iuqh1woVuQSyVZCD7ill4pu3/EFoNOq3d5OUajkfRgj/oPRal1NveYXKTa\nWmVmh2kp3UeugE+kykOPQaew2C+UeIKip1DTWR8/ZI38zuwhP/x2JLouZVP1\ndNCuopSIZ3Z7YPs+N6jrEVVHjQEsWR2DbmnaRP4FS9oKqiuvTeWhLxOPXi7Q\nXslKIBL1AZt24ozGE5pQQG1Sqj7QcMf+Bg1Q9KNpNxN3g9N1imOs+kJmX3aG\nTCeo\r\n=Tzp7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.5": {"name": "embla-carousel", "version": "2.6.5", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "b5d5db6a896988feb0243ee8cbe122bade2f6f3e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.5.tgz", "fileCount": 30, "integrity": "sha512-R0c0qBGrEv4x3gnsAC4zq4//FhchRLCpM5FHPNEQgPmH0tmcacbIe1iC3mNq7r8f1+me43vIilPJyX+xMl2now==", "signatures": [{"sig": "MEUCID9xz2PmrNH+mqaqiIxLS18X4f2KcivF0QktHW7fW0y1AiEA44vFaMDOOlGrBE5osk0n7Yc/9iZUaK/iL4p7HLXnKYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeB3r+CRA9TVsSAnZWagAAm5gP/js9cy+oqduF2JEypppN\nnmFeVRsm1rXl21bCpZNrjg17bXF+kW5JeHs/S2TUFCImrc76IGEu8hAaRjf6\nscyrevr7z/nJrQXFfcf1Rri5n6JPmJJvb8NSmuQIELO5clMQCAOkUFBXg0BA\nX3lykE0A2mmvFZpX373LA6wFnPzgyWK9iBCktY0ydwKH8qG76tsENkEiZ6dL\nBCAq20XoVtlbizSrxnPwfGHFxV5AwTlW4NIqa5pRlBu0TR2Sh66P8hKcsClm\ntmlVwv3sGaQFyUYJJcQ/EeuRT7vmZ4yyPet1l/KMXCS0lNkhl1PP4EWCkqK7\nIjjetMyxE7L5qJpL9/uFfD+mXoIqsONNgOwYBZKfGOp+t2KcYIsU72wtY/H0\nyrWf0GVl+rFyqNW0WR6M0yCgYZRFeHF6fq3WCT3pYZqFEJ9Hnl1zb1wbxe4F\nGatOsdgbqWq4ZS0iqQpo4jXKdntXMfzIBjP8Q0WvHgK8z7zzBj6NXJm7FuPo\n19f4DN3p131I4GFfEh4XzY486rqcJhU4ARVpKiA691F7NnLHjo6i9ccPlS8Z\nQSzVdjWg2k4P3dWfrCg6PWyG49Ko1GEu9e8bhn4QlhdBAu7nlKBqOre0Kv/E\nue7XDIEzTw0FCAMnyuCbNaOcU3k9Fbjhk31fpTfxsZMFUKPqx48kQhCdakU5\nYdOc\r\n=r5wU\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.6": {"name": "embla-carousel", "version": "2.6.6", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "430b83e26eee4c76a66a5c6bddc76b3c0cdeacb8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.6.tgz", "fileCount": 30, "integrity": "sha512-kEH8Azg0F4llHD01HkMZnfxsJyX6yI0DwXgqUZY1PJ0ofKv08bYUL4kEIvhxg1z73QrScBYBlN3OzKXQhnDS5w==", "signatures": [{"sig": "MEQCIDMf+9/oy05zqly8PL2V8VgNlshb4d7GZbNfLcAFFHvVAiB5ziYEYt+TpPLceTLnq1IxPEfIUaLwcqTP/gyyFuHcmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDPI+CRA9TVsSAnZWagAAOpsP/j4XNvzpOnLDTBZc7sTq\njI7bLKQ2t0QvP5IsIyxYGb0gYT1HWevwPf61+y2u35zORzzfVELt/6YtnoDZ\nFr6g/YpaWyyV0ll0i8V4sGa2t3KiZjubDST9nllzJ8wNEerbl7f7o/BWehTk\ndG1ahfQvvaT7dbrzOnkzYtTsWgY9lJF4BRmUmfktB5ByY24RI65wN4TLZoFf\nK3sXQDQhMDAY3vCyjlEb+N/79Q2+eYzCLs7v2VikBeY2gtfUSIlWW0zJHUba\nTR6Vb6cCREy3zTN1e1ao5cF3mwT0OZCxX6Uv/VJdQ8aQdKTb/WY5W1LWrx9+\nQ4rUXQJz8pEwawps8+PcbWg+unXIYrh18zHjhTju8O2SZDIDYnzZsWHRom+X\n98vU8v5+vejuLwh9l3Z19giSVOAsGumG3SOMQk1ASAnLpuYl++H1RAOZt7m7\n4O072Gwllf7hKAMBBoXBIxQghevvEqCv7xcRtUo4iLeAH85/egBXJu+s035a\nc2pBQTxi92iKebk8ol5fpnDKMPpbGKC3isJvBNpI4CvVTBwNeOoCisyC9IWG\nGDJ1dBGX2Bh5xFH8YY46t6INnteUpmO0i/E6wsk6SLreBHHl7qief/2v/alw\nmZXCzOvuRJjwoUrz92JDpxWLkSvzD8wXsonJbfpaDY1Ku378Y7UW409I0M1Z\ne6bW\r\n=Xz+e\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.7": {"name": "embla-carousel", "version": "2.6.7", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "efa88de8df13d74963e63c661961baebbcd318ab", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.7.tgz", "fileCount": 30, "integrity": "sha512-UPP6jSHu+ShIBzRSo1UPt5LhUnScKVkN2sZ0n8BuqUgvLi663OBjvu/oCUcEEp5loYP4SEkYsvDbNHa1uVI+8g==", "signatures": [{"sig": "MEQCIHFi/gQd8U+cPDA7IhcnYQ0ZI1E/Uk3I0BgOCadv7rvlAiA/xwMyRFhXZ7lKP49NqmjZcevudjYlb6J8z9OLUlztjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103291, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeD0RoCRA9TVsSAnZWagAAE5gP/3z2KfD7wa+fNtD965r1\nq506sMMpd+BRBgY2++X47aqvmGpSkkkkEyHjW6eew57x/qMkZCIKay/6t2Kq\nem8fQn67Us5Dmw243DRZOhJqp4OgdtXtqkaTkIkSBFad2kEayuRZUdclwDgB\np7pxdNy3TuEo3z8EUSqH3n00cChlws5q1FEO9fSwH4gW8RKvjvFygPipRPwM\n3QEuk8qHty1tkgTAwJxyLdmSQnxM2qe/GXQdhPgYUiTpeHayr7DMlLtg41yp\nwACJ9xHcGCTc+Rfmo/htLcmsPk30a/sSmPvKiLRRuA+pvRSYCjCWsoKNEd52\nwsZOhXG33I01gZAOWLZosHTaRugToI0Mdf+0xACA6jFD/hOwWzqrK1kv+dBg\nwIkub87Ttfx4bgB6Hchm9GjzXnuYPjksKA6MuoL0gqrpvvCa8xDctHfbu6EE\n/5oYInsYX/NjnPaLNUWj196wS7Ajq3vY2nU4sxAcGQM8rnMAMW2xfRXxCZbD\n6t2GVv5y4WWAiHOnmfwcwyuJLEsq1ufv2NAwMPtxiNnrLWFhwm0v6M9By5HR\nay99ZEnoA7TTKu5QkMPG0P8DvVxUa39EQGxERX3ORmRZfOcFvSvmzL75odhR\nG+LDnV13g5ElJQMWJt0VSI7UruMaxmtmJEFtt41WKgJI2wyJ4ihkADo6a3yA\n1kuD\r\n=EPU8\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.12": {"name": "embla-carousel", "version": "2.6.12", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "973c028f4e75f6464eec03f0c7d2490ead8801c2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.6.12.tgz", "fileCount": 30, "integrity": "sha512-vgEkA/VmQshsY7ZVUDCQtGXS9B8mKrWsba1sdliM6IhZm/hzBh8ZjIj8QsXknyNearY17HEeR8bcrBBPXQPH3A==", "signatures": [{"sig": "MEUCIHCABzae9SSFYvDl5KMnRCI7FL7bdNDIy4ZdG+EiZf3PAiEA0DOLNoc+rvzfWAYXNs8KFtF7Ziqe68jDUUhLSijRbto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJJKOCRA9TVsSAnZWagAAaxMP/iSOmFbwA/ebvz2/kcVR\ndVuKK9BBWkvvsOALUjQhsGKa3gWOhiNL+aH+cePMkBNlqio9r7w1M+XM9V6w\n2Fe04PzoVSUJgPTwcJB3oVFkaIH+cHBmS6jcQcCH35iFLDXwLe7fd3gYyAt/\nrLEKUtKDkAa8JDRWMuY4/v84hfiLd0r50hUYiuf/DBiN7sZELHqqAKI1P1wz\nW94xPUPqe7QsHwjzJLdXMu8RS4Yi4RZzCY2lfnq2Hy8hDVbN3n3/JUQG9vKO\nywa/QEVgQrPpqLHrsA4O6TDsIXxFG3rKaFboWbXJqLnMDhpBAy9KfQpp4Ccw\n/xZvqSSMKBqi6VOFLgu3+yJ7DNNl913Co0ysWFhoyOwnMWGijbbbGqj9zxDt\nlTMaSGECxHt1ZLEhNGrU/MFKv7/fgUfzOO5UiIjdD8v3K75moJP5QLP0QFHU\nq9b+Dr6v4pEeWlIpk0gyPb8kvUs34GRzxJD1g+2v5dRydFQKCZzZBI6fKGfM\n6RXftWGQCzjo/Hmk6izdI65dHCYTzMOySzRGgfFCGFVfIDz/ROkrdeHqQTqv\nJkk0APUBqaSU2hiwaeRwq5dLoH5y5Iro2VrpHVqBRheG8/qiJCsi9N2bowKk\nnnlxiLh4bE4W3ZIuPVyLCHNZSLbN9gs+g99wdMr5ofB85attnEoY9tYqnsmx\nQEBx\r\n=q5Zr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.0": {"name": "embla-carousel", "version": "2.7.0", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f26fb1dedd8f0a13fb7b30528a9d5ebfaa8c1b11", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.7.0.tgz", "fileCount": 31, "integrity": "sha512-a3g0p4OA5tR97XXZdwXWwb5kC5YHtxMX62tNzAXHlPKdJoe1dcoCc/G7z5dIJja3iMVi/ReZN7GNiEkbtGBpFw==", "signatures": [{"sig": "MEUCIAmP/qM2wpZI++A70oeSKqo67w2ES1OfVeo4scDygjjiAiEAvjb5nHuW0jOn8SgHJH4zRqAbIKyWzUjffhvapnuSEfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNBRwCRA9TVsSAnZWagAAy7wQAJVZ6TqjnxAGPnWskltV\n8f0jZJDNFH20MziWKzY4kIp4rGW0YsEUQmjb6ofdPHaWUxBNvgBL1PU0NA8E\n6X4Jab79vWjeLuU8LsxGElp4P/x1v3a+mwitA5T2jj+zQUnwumqlZc7YM1vL\nUBRX79YwpjcV6TcNEKn9SjenvCtxzcHVBAQseB9VSW7Np5WpWv0B5jkELk98\ntOjKaQRgWn5WXV695Os3B/b6pXXZ4J6g/jk+1yJSn6+qwstPuAH0q2aFcdrc\nAQa9huUK9/EWMjlMjRkRS3rhCHL1PRWtfDcO9MfRcSPPwOjLmn6zVrs5Zwa9\nWe4QcgOw+net6DmKiq55sA+J6GNgqHIXZFC4zNqUu5FIrUTuKiZ9X9feQCk+\nuXjl/sYfY4N8icc2/NRCbPguhSdkVX6H0Qj89RmXtxhM326Xhdi9ZlUsy8qp\nYNAPonImjBzhy+3IwM97LnpAAN7tZ/duM7+dIPkvOSd0CN1LBRp527gOE++/\nOoJOkWrnV2V+hqqiqI5VBHaSIweJm0Ci+bv4ashK2xpkeZWrMcm3TqBFF98A\nPZdaR0T1Vj5IYDrlwOOvWBCqu0YK/3HgTgdz9x0XhZrAOZcVmWVKoQ58bAqL\n6CRyNYVMLd6lpezRBz2xOikUbdmF5Nm/qf7YvrldJGGrJDtZ7W2DkO5ACnZI\nLKvF\r\n=R62I\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.1": {"name": "embla-carousel", "version": "2.7.1", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "0382af194e6ec6b0e8e4eed6fddcd24a9e130c1e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.7.1.tgz", "fileCount": 31, "integrity": "sha512-bxS8WctrXHW4/LtpsNOFqAWbet5CI7g1ZtSsUQUsW5cr/NCPnwqLq/ahZQ9X/clsdQJ6HMffi4t0sNt3r9Y2QA==", "signatures": [{"sig": "MEUCICmB4YuOAZlikxrZHlZvT65+f94Udm94N0rwu0MZkXVaAiEA1PTE1FZaMHM62HyhVrT8CwFhYLmSzCbNSypt3Nx6ITk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNC0lCRA9TVsSAnZWagAAnOQP/3Jmz0goxhYusCxCJcPH\n1A5JJUKjsc+zhQXZvb1x8sfQOLh6HjSXErrnxZbjpgUdYVRPTZghzhP4E7H0\n39Qjj+7LqdjLnUc5CNUfQghgasFd56ZblqMYle64C2IZG/4BlHIHs2R+WiGE\nEUtvOLcSB4weT3UoLb6sxQ2W85ROwDPFWeLmdVFG755fu+XHJ3j+0zgJRJmi\nqVmnTEgiRQiMwk6I2oznO7KkihOqO7wOWfRoNT7hGB0jjQKf0HXUxBUiX6+k\no8NORBUUfCXo7/niK/Td6R8Y027UvI10zMN0RR/8o1j2QcVm3w0EEW3j9UhP\nilPuLT2qUsSeDADqlxM/g1pzBObyG/p3zXFhXQ7mdMZMGu/3iOuwpi1KilRG\nbLsToiW3v3RZ9Xeh4FA35SBOaJIy2ADI8ZNYnkqMo6sd3KIjiqxDj/5Qa5nj\nKN2xHaXWSyM9gwPf2QiMM6syDjdt+5XHWAES7paCK6t9PROCR82UHeJOLt0r\nnadRU0vqSgiV2f2nECyDjcL9PAs+rVZzc56p5bJuANwzE2yhBj8wHDDg1VkI\nf2PuoxprlEPcQP5SJD1Xp0XqiwZKYKdEakEsT2O+4cOtlr3fO635U+P3kbXK\nwkjLAZN2Y+/S3B+lk1ZTUGV0lYFXWTdeB/KFEnLBhdT8kQN6y9TTgjQVWggV\nhhJQ\r\n=fup8\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.2": {"name": "embla-carousel", "version": "2.7.2", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "7a86eacf03c31dbefad2f11d936fcdb55ce2252e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.7.2.tgz", "fileCount": 31, "integrity": "sha512-hufVrXnEes/9+dJScP4Pv7zEfX1Px9NOxNMZ92QL/2Llm6zXPOzoiF8AQZYlZy4zIMtkkUcxfNQettjYbxusUQ==", "signatures": [{"sig": "MEYCIQC6yqndJ9d0CpmxatS6O6JbXREAtVqxsLblHhKqHMvWawIhAPn3VoWsHTxlO6H9d/uiOA2JMabFEidivr5/O7/CjOai", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNEpWCRA9TVsSAnZWagAA9QgP/ifpJT2rcKoDnrBCYBlu\nY5x7WDVUhK5Hi+Fac0S8EgTHGLFmRSM8jgu4sx86vEbjMQ6um8JvsDRK4fAh\nzkc0sUNhjtdA7JOr5v6l1YwtEipY/qIhzQKUkV20PewCLLybBo2CmIV6sHR4\nOcBSDyXiqYn++h2qTIOwETcl+Y6zNOcT6nlsZTfAKEuyuNmUxvutssk9leJd\n5JdwCRJklRheJe+/DdujBhE1umGNmbprHrNmeocl/b2nD+vXjr+c3NE/DeBN\nQcTsMVg/AmMlYIvGdfRnLyTXBaCEqmYLitVdfWGmKw0csxSkeMUY1KXE+EBP\n71ms0Mlc3kzMqU06zChfXxIDSRFGrSZ1FPZFUNNLKRR2WVPR5epGr/jQr7CL\nXfIQrOIRB3Ve3kh3Hq+kRoCKncdjkuojeXCB8DILCYCmnbIQ2TFiDbbaC778\nIKruA79EWjIUcnKVSkXCASWfNjZMbURnKDuaFEGU5fnch5myL6h4D7hVvnLt\nWNlCw0DHuSBTWNBSdZDYMVAMNTzUa7pR6kofdMfqdEra9veE/OzHEW4ET3vw\nPg970DxG92v4C/ZiJaEo9saUh6NNupvuc5TACJZes7XMSklxu81SzUq+1o/h\niTKSsvaPcvoeiJzoObFl4Qsa7dppyVa1veVYTfs9TP0nkVh6R4NAKYTeNxzH\nZRwr\r\n=Dt0/\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.3": {"name": "embla-carousel", "version": "2.7.3", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "af04114a651dfdca6d3ec8206a9c4e136a978f6d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.7.3.tgz", "fileCount": 31, "integrity": "sha512-peRqj6hMATmzOJ90VV/Hus8mtTTPyi0VuHOTz15RrmUsEV/R3Kea2u0WoLtxAP6PFWbUTBcemIgiRZDXmksz2A==", "signatures": [{"sig": "MEQCIAgOwO0HMjPHUR1l2ht2QPOoSLmzB9wvMPoqUXdPwaPYAiAlSGI1cUx4PMglXRIWptFpnTLUXTW6ouWOoCyM86PVWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNVNyCRA9TVsSAnZWagAAlc4P/A/ys5R5kc1H/m02rbe1\nrTOsVbtGX7rhQ84Fg3XN4/VTsKQp9KhDliUjpceRNILvR9n4KLANmT5u7rut\nNQvZmZmLsnhBDxNbLTIRvB0P7i95jEAVcU4FIFJNL8+KAGrIKdHU16PX5zKX\n6X9QzLiBcQa1F2Ok3vL2kcSWPu9rkMa3be41MGDnKtUECaJZ0Sq8+fAPP1qp\nMXYBFnQ2hyGkH9Z1eGjqOhn/PMekhi3Fp/GPW7MGL/dd8eAAtaVYbhfFRDoG\nOKnkAjH6B2fC7jNZvXjC97gbNgNCwBCGsP4GDeE2Esh+bVHJ0lIyF/0K7Hlu\nbRvegHUd4TYNgBvEbGY4J1mLUWBlLuzx7irmpkPi5I8R1dvQV2wWQSHNa4yH\nJ6ZWMZLjvVlEshVq7K8qhvjNH8QhpTGEJRPJ+Gu3B2lcrCj5AhwlsrUb3xcs\nMJ+afUpr9jrlNe9WMpSR8D8D1jNtoaeNHdN3EYQO2SJW9+lCQdAleiOnM52c\n0kZwdeYqQPeWb3DbABkK+bcWEtORszqmXrnx1CHYw3pvWug7l8oqIedGheW5\nl1HENzRNRQK6ViZE/b2FxZdy8kfmRT8UbpDz4mgpmTqAz8iBmMGfNOk1mGnL\ntFP3+ug21fd7UXugw8RAU+sIfqMUItcoz3zvdyHNvscEJnWx7MI4mhHyxPGB\nSOgA\r\n=xZQH\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.4": {"name": "embla-carousel", "version": "2.7.4", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "1f69cb654b9ee60cd9b2e77919e12379a221fd05", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.7.4.tgz", "fileCount": 31, "integrity": "sha512-AdZUFVt3deWjli0HrLONsTyvUeQ8gW4T/dezcCK9KEP8dn/TNG3RtHTiqEuUe9dHa5HnPFjx0dt4sfU3i6sLxA==", "signatures": [{"sig": "MEQCIA9hdkYLGn3Snjf/DxrLNLbcQ8xKSplTMyYeiw2s0ZnbAiBU+MdXDMVXW3BsbMi0ey3YEnw6Ak7aotMEcfeZSj7zFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121295, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRAJiCRA9TVsSAnZWagAAaTYQAILzdPRsQ1cjC1xX3vHq\njLYAGJdTTr/TlNkD9d++ZZ8ozYUReF3FbV4j5jzyG8iLgm6xHafKmq87u6NB\nh7zUwkDTO6qEcNucNoZlQM9qah1xuShun8TrAP6tg8cm0p5pfOjavMTmHJP6\nvDzjUUAzWhwlZf8n8rdxpwRK12bjuheaiDhYJWA79IGro0+gkqTfxuALHIpj\n8Z6oJXseqof2gc7e2owx12cap68ae3PNHIVKcDIO4stKj49l81xjJfmuGQba\n5z4uyh/UkED5dUHhtnb0e0ZuQ09Pb84lgEf3xEeqz9okPKcmdAT5C+i2J6HG\n8jM7v75sZ/jAF8fj5yzD6iXCOHQK6LxRakyAB9ARz4m6VFIkZc7uTQCg2q7f\n7ZUkOtZ8cpfjsu9EA1h8hlQHp57mBB9B1yUWj/qKEO18WkAlvSf25uRR0P4m\nSFzLIdMueVMp0E69bppSReIy/eTSbE9aqb4TKm2E3XH6UemhJ9yjnAiXTDke\nu2hYorgX79PNyWPJ8CnmwRc9OQxRYJ9CIOdy/+zkH9z7DF4ANRyIF8fKFcRS\n4osUsrYgyz4/auc4PxcqrCa//U3C2IDTGVyVCJvn9zN1Jn5xK+ugfv8hnr5Q\ngV6l46fbRTToypz1YzSh6fD1IYNX0/zevsKK5l1Uv0NQ0nida9tYXpRZPvD9\nK9BX\r\n=PTYS\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.0": {"name": "embla-carousel", "version": "2.8.0", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.0", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "44696681fc1cc4d1c73cf14d2529f37367fbd7b7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.8.0.tgz", "fileCount": 31, "integrity": "sha512-SGRJauALwdv+dIrEudCctDJRyWG09B+ZzHjKqrjl/sBXpmpjLrwUdFoTeNdGK4IrdKo9ZVgzDOR2Smt2s+qmeQ==", "signatures": [{"sig": "MEUCIFy1GrdrxQQBfXpviDsumWnF6bBfRAt+/Gyr/jJ+cK2wAiEApG84iMw9R9CAf1Si60NQNYjgZHxn/pTLpG+R9Ln4guQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVjYHCRA9TVsSAnZWagAAC7IP/R3vpLly8OaFqtNm/3y7\nS43+QzOwxXys6bVrnJ9yvvqdCGxIN5IVSxssiYUsb7Ue62H9Ak9vGK/z9i5p\n07dN0gETriZeq8w+pHCP7zRidvwNnbeYKpxWKL/miCEzeAqOdonoYOfCpq/c\n3xE6YGI4R14A1NruFfj6s2OVVHtbLzMZqALPWvjaLREJOWJSfWBqEKHE8RgK\njHCqZ9iHFN50guwFq+LWD7wV7eyC8ohc/e3j9KY0s3cWxlXz5+r/vcpPlVCv\nIN41dv9+vnP/KMaW0sly3joSR2vSMCKI2dYUnNbM29G0uHX+QEwbR3pwZxkq\nHOZVeMy2ghaB8Fx+PvmV+XI4IJQdUuOJ4G2sAVIpecZEikzm/rLk7CoYSBf/\nD4roM49u1nHg9yZ/DqqlMzYBCWZ3aj4j/GFoSMVA4xS4mdjGiPEFwIEhYH6n\nTjXWS+ugrHRb4COC2ASS38FR7Ihs+n1aqUMBcgrpMJhc6N9cIfaNEvauc/hp\n2QnLVEQFSoFPd3AnfVuuQFsXPhWeGWH1H/V+YYDoJi/KFy69bnfI7codq0I8\nPRts/mgx5kSiLKgHWe8AX558vqlpo9TG8mD0irHErcf/Vq0JbwXeUy3/AyUG\npbDwxMGBIlmJoVnXzBlMFM1VhyIU6oGIWkVMr0CwJED5iQ02shrI/rh6bYuf\nMTfg\r\n=x07q\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.1": {"name": "embla-carousel", "version": "2.8.1", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.41.2", "prettier": "^1.19.0", "node-sass": "^4.13.1", "typescript": "^3.7.2", "@babel/core": "^7.7.2", "@types/jest": "^24.0.22", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.10", "autoprefixer": "^9.7.1", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.7.1", "webpack-dev-server": "^3.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.2.0"}, "dist": {"shasum": "f0d363cfa8789acbe1a1028bc55ac8a880803978", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.8.1.tgz", "fileCount": 31, "integrity": "sha512-dRnRKYiJg/24xkFdX9AKA0BQJ3+ZlFU6uzhYzZbiQA6nSEdUz5yjE4K6cCA7EbGp2WQvq7qdOjIwFfSbzX2iag==", "signatures": [{"sig": "MEYCIQD8033c6sFyVaFoI1uSTMlcu9Qi2N/WUU+KZbEx3vnzTQIhAK6h/KFjEDfnjmZETneL5Ed1Echo1n8aYZdR/z5I0xJN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebfSpCRA9TVsSAnZWagAAcEoP/jXIQWtjF/Ws9rY7mpm5\nTkV7uFQjRXMYuLTv7EhdnsBEdWBj3esPgLxxGC3hS+bAwRyhG0Y3KwIMboqp\nlcRcgIvzSG1tXamTqMjF9mxAJDtdxs5gHs7mhRjTyqXYsO7CzfkEmyHkR3wx\nwmBUMSjCN4R8n2Rf0MStUC6yQ7fjDy8HIZ4EEkMTEIZz7GNhZB8hvjPALysl\nrOqXSYPKZIQUZR95+U4dFQG+v2qW0X0B653x1dOJMMVYVUk4PYss+hsNLIEb\n7T4hZj3w26f++8f+xsH8D5irghagzVlm20aV0vb2Nh51+q0bO4eVOLwuj2hN\nBWwJ8YreicGg0d7xu/hAL/WyUFP1MIYgaWqe0HkwjQN6phVEOvuR4M4w2+qP\n9YAfDjK8JmodjKdeIVm3/zXBQWbH1LBT2Ju90HjGmfLWu/xX8vrmuh2aV+rm\n+wO7jWRxaWrxx1+LnrZZVFtjSCGLIM7TP6sf8QEv8g9UbCdZ2bf1nWci9elN\n1lPHRWzMNd6/fP8DVpEeNhoKU6dpOpg1MKQCoy/vvIWcsq4q/bKnqM+PX1n8\nxWhkcz6C45cfb0WD8lwIyw4Dk8+66imtifN6KMDUwLHNSqncmex9TD+Efbpn\n5iLsRLXBsHNR5ExvItuklLT4J0ELMXaKxCZD0KPoIiB0y7wNdZT7d/gJZzVF\nNg6h\r\n=pi7y\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.0": {"name": "embla-carousel", "version": "2.9.0", "devDependencies": {"jest": "^24.9.0", "tslint": "^5.20.1", "ts-jest": "^23.10.5", "webpack": "^4.42.0", "prettier": "^1.19.1", "node-sass": "^4.13.1", "typescript": "^3.8.3", "@babel/core": "^7.8.7", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.11", "autoprefixer": "^9.7.4", "babel-loader": "^8.0.6", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.8.7", "webpack-dev-server": "^3.10.3", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "23e12b57573ce8f353e2f2e54c5185eb6a1808e6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.9.0.tgz", "fileCount": 31, "integrity": "sha512-0CUjyTkzznhCm18A2LI8LufN0b81felK/pealqHEk62SOQbcKzd3rHcvNdyx5Hwnbmb3mkNszDSen5UVAr9+8A==", "signatures": [{"sig": "MEUCIQDNGXEmVFydCsO4lsKLSukHnm0BxmANwH/YbRmD0Z9okAIgFnFG4yx78sJi+t8axWnUvhU8Stgr54617smD/f7vFw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefbAACRA9TVsSAnZWagAARO0P/ArmckQkVEArV34HqLoH\nZ1QE0JGAZwWJgUZM4O4Ia3YIvJ6lSqEKaPGWU8aYzZ8vK16uNTn9Lsb/bQNc\n+PSNTfoe0RR9JdgSCamLBskXuqZ/lpAn03kUwNueyfTeX6covoQjBT7Wdyqi\nFACjd5jSWIwWcRGrZ0K+Zz5VvFvKTPRQ1h2BvjHDZ3yHGFHpxM+9UgRjsK4X\n6Odpb0qJzo8E5K5VqX3vCWHTHHMFsSe+Uq+UVk7mCCsfL3lyKesNRanHw+fJ\nUFoB8v1t1l0ajjmceke0u/X6dK9V+mfVXj6lQKOeF7cCIzjBWOFVAiOZAQKb\nhQUXTQ7kmPoAND+bMFzHHrMWDYSbL/xlcGG5OBC+D4iinG+uXz2GRo9X7yxp\nxTwAIcpOzKkHhgo4GmRNvtQ2LOc4JwSQP2eX58b2vZttMCJmRywkxi7k0jc6\nrfuLLJWyyGDmoJ5pV5Pfm3uGWrwmgocvv67RL0qyoLywCvxvArAnSE9Qr1Pd\no3jlzw17FUsrcVDSYKT+ZS0rgk6PN3d+q/COR1dNyqH43et01rmmK302gx/v\njypOyDz77D0es7jPL5BX6j2/mmrmukzejRG58MDOJGD59M3770WXgD52/PqL\nNyjU12LUt/VHuMWltu7wHlD0NaRQzrNDTJk9Ehfaeanbcjbd8fEdmeItz9wE\nYsQH\r\n=268v\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.1": {"name": "embla-carousel", "version": "2.9.1", "devDependencies": {"jest": "^24.9.0", "tslint": "^6.1.1", "ts-jest": "^23.10.5", "webpack": "^4.42.1", "prettier": "^1.19.1", "node-sass": "^4.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.11", "autoprefixer": "^9.7.6", "babel-loader": "^8.1.0", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.0", "webpack-dev-server": "^3.10.3", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "be68ac01b34779a437d7ab902a368d1913401a35", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-2.9.1.tgz", "fileCount": 30, "integrity": "sha512-aAyCpbPwcR+NssMJ5tfYUMEavCOhSUfk9k/g9Qd98cAZ1d/8hju0cplEs85DtfwU6Bd+Oki1le/zMH53IVOs9w==", "signatures": [{"sig": "MEYCIQCDYMfi9QmLez2tTZ6AADb8ubFEI62knDobZtarsyjXcAIhAMickjVzF23cQglhig12EzWBokU8/tNbqCCbqNSJ9BtV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenryECRA9TVsSAnZWagAAXYIQAJ94Y1EMkstZu3m+qKJ0\nng0EB4sE+/cnpsri38JPiqvvbn24GbpYlJi9TMHchn7p9pq86LQ0Q4r9H1+C\n8gIO9iXlJxKW+i7G3jnv/Q23a4klBx0QdYX9jBX4ROg5gWd/WLeZgmxqzYCZ\nefGUXjGtj4zbp8FxnjQZI7ViAj0+lB8KYoo0ucdykPXDzAL1RJsWIZSKwKbF\nRJ7T5sWNC3mGS36lmYvuFvfqpK9Psw7MAUp0bEUQcja0NsHLygxmLsVcSrUR\nHN5tKq+lccK7dtzQ7WwEAgv2Jguoz8QD7q1llmINqzJmfUvoy8YdJMQLknuI\nwHO+3u7M/VgdnrBi01Cla34DoiaRcyTr5kb2sGLgYZ1oJeC50AADC5qt00wY\nc/UmLa6oaV4ttwpYtSO4UQ2WnN95L/ipyPqNDmwBZfTPyIF7rifMNPmVrwR5\nVTUUtc7TZtFwpZLGJOITYMldKzFktfgKvETaOev4Wphqk8XUgLqIjtQ816Y7\nKHJjPl5VhDPKT/hkdGkqYjsTSVnn5sBhiU2QKnE7jaZVE14inBxRoW87tqDh\nSz5w843eyW1QNJapwKnL4zKJtpZ50l2WuElpcbcbI8NXDOcJeU0H/axSVGWP\n4WHMdyJqYue5HtcUHWIL75u8z4lF5vgbA48P0c6ptVFrJXlR4g82bQR9esO1\nvKB5\r\n=58hr\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "embla-carousel", "version": "3.0.0", "devDependencies": {"jest": "^24.9.0", "tslint": "^6.1.2", "ts-jest": "^23.10.5", "webpack": "^4.43.0", "prettier": "^1.19.1", "node-sass": "^4.14.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.11", "autoprefixer": "^9.8.0", "babel-loader": "^8.1.0", "webpack-merge": "^4.2.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "webpack-dev-server": "^3.11.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "f57e1be6436076edfb5e2b219dd77ad6d4f77155", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.0.tgz", "fileCount": 32, "integrity": "sha512-ULNXzPPHJtzVyzaV6oFvv8qKLU1idwwQ1frPm5+WIcNjguDhCh6BqLEcSs5lZktOZ04vUtkqL6dgASCLamHGew==", "signatures": [{"sig": "MEYCIQCX2nzqEVacagh8uzUwwbdS6/KyXJRlT/cbfMgTaVSSFAIhAIKuNDxezKui8DqCT+yGdeZTQP7fb8atd9ZKrFYoEhI4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe44d4CRA9TVsSAnZWagAArDwP/1VsdRCj9HwcCT2Hb7H9\n24DtJI0ElEcRci28khKnwpod/JY+KGny29eZaRYi9cktI5UamY0dKm1+B6FS\n9fOjVAU6dulg0gHSqe7VrSU1ypONd//vMm59kmLvZUTJY2o3MNTCYfmNf06j\nAO0jF2dCfLdZqXgfmEB260nlmfVJ86IxkpANDtrycMJWoBQhjiSaAoZqvcb/\ns+JN0aGRcVUM0in/MvzEl7piywbKXpzHiqcFkY5iFh3FCHaVLVh8ZWD889im\nOJFyR7ejwAaCCY5s+nc3Gb5Q8q9m+mM/ViNRnzGxYum56O4l61RGTTwThKGI\npp2QCoosgTx383VTXtxhSwwh8RMkfRglRQcM3UfmqHneXCn3Qhv6CIVmj7KA\nN75mrwP93Ld5sFEJ/tVIGEDEf6LSV2IfbRK50dFK4/BRzPGm2DcJlTanIaYg\n+VhD0jnnnX0KW0eq9uyPAfwtSFURBV4chgtVHUMR/m623vg/E0HqjtD3DM4e\n+5aY5wxJ1332on+g3HVfoLG34f4DzpVskSa9ZapKDpuDl02rOhi5qSIdqEyE\nwhdg1vRj1IKfLURko9MsWeNIqQ6n6yzPMtdJcDhPjDR/zU4C+IcBF2fpTgSQ\n62FNpgIun/U+b0OoIpG04qpQ0ZmPmaaASs6cXJDy79kA8tAIeXQ2BjWbX/mf\nHCGw\r\n=p/f9\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "embla-carousel", "version": "3.0.1", "devDependencies": {"jest": "^24.9.0", "tslint": "^6.1.2", "ts-jest": "^23.10.5", "webpack": "^4.43.0", "prettier": "^1.19.1", "node-sass": "^4.14.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.11", "autoprefixer": "^9.8.0", "babel-loader": "^8.1.0", "webpack-merge": "^4.2.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "webpack-dev-server": "^3.11.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "c35258b4834109824e1e81c70a3dce5aec2b67b0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.1.tgz", "fileCount": 32, "integrity": "sha512-9TDZir32nCZIfYhG9F+XkXZxc3x00ueTeNn5AKRxOM1dxIGmNYRHep5l0dhkDgukCipQCz0FKVDii1omhFeJUQ==", "signatures": [{"sig": "MEQCIGqb8tOl7IxMbFKoZyYcFbRye2uW4wo/LT1dU8BM5ARrAiBWOgcSa2Z3qALRbi2nEHWJswJv+scw9k/1z+1GUk/K2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6eZBCRA9TVsSAnZWagAABWMP/REWyrMaSnVz7jzAHJQ6\ng3iRtCo+PM7pY9w5FKfuD85WlGAZCTBT/Oge8S4RLd6mi0xG6+5QYj6DiUKf\nJGtndezkkAdFAnwzLEIL1r5YdvlcgLNPzF8DEQHppfDMFJ8fHqJ/Io9rJKp7\njEos9P6N6AmeUg/A2ixiDGhVP6XzL60h3xU91my9Td+uyfxxC3xWEyAeIMH8\nBzDc2BgPzdfQOCKA/DI1ImE89RH0RGDuPxC/5bMxI1vkQ2XSNEai8T7e+qcg\nS9JeHR8g9HB1QuRBWNNfTTZnWyj7fXjh6ksrETHswfxyaeHkaalnf0OauM+y\nGif9iVa7WVhqNNBxtbutLlq/bXq2V+l3gaDU+rEHoht3Ft8NCVg2oxjfgEqZ\nr5mQFZTrWRo7yMuCCI0ZROcWYkV1v0Ys7obGNoJn4gsuUju1ns5P5xYxTZ8u\ndguGPw2DmZ5VzFAhfSlb4Imow1hGviN/S5rTlH2TbELWa2pURavI4D5hl/b1\nt5iwTb7KzKVdonUlKUCgKlBxtzSZ98OaeYlf2P8mrbDLV8HiM9tigQKzh1Ta\n8TTgBzq9xCiyN+a8gdBEk9cajsdnc07l2yTBrzhHAoEhF6Q/k+ziMNaH3fpA\naJ0FZbLNm6OmI8CsYnHcKqKSumNEQQnCZP5iwWkgk38ogINGhzEIgXwzmolG\n8vpt\r\n=2e8K\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.2": {"name": "embla-carousel", "version": "3.0.2", "devDependencies": {"jest": "^24.9.0", "tslint": "^6.1.2", "ts-jest": "^23.10.5", "webpack": "^4.43.0", "prettier": "^1.19.1", "node-sass": "^4.14.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "postcss-cli": "^6.1.3", "webpack-cli": "^3.3.11", "autoprefixer": "^9.8.0", "babel-loader": "^8.1.0", "webpack-merge": "^4.2.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "webpack-dev-server": "^3.11.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "awesome-typescript-loader": "^5.2.1", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "3aa4946c6a8f9a1de5882834618d243c2cee58d3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.2.tgz", "fileCount": 32, "integrity": "sha512-xub4RpG08xWdhVMecLfAQZTkwpjTVP3zpGOffY8qjuP2u7e6K6mZp6r1Iks1AGFzD9s2/K+T5jP8Uj86FZLvLQ==", "signatures": [{"sig": "MEYCIQCP+YxFiQ5Vwe1cAboLOfngrOR2vZUoO5nSnl+HD++x0wIhAN+m+E/xK21NwWJQbOF0sqQmyK1689j3uCIlf5SM4MgK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6hI4CRA9TVsSAnZWagAA0hIP/jn40K4zbfPF6t2ExgkI\npunJ6Ga+md5e/Sl516zVlhA83wFiDc0vg7hXQtypVuWlZ/5zfhTrX1rDYLlt\nzYrxB73XqkSKEMpp3HZhNJaqW8GJDcx+0tRF1dcQU565qf94pGuSHwsSYF0n\nS4vV8C7NTJl4TF7yl+5S4He5fTu1OvIhpMSGbTuERhQ/g3551j2ni+Ko+yTg\nX7QV2eXX7Vx/WLltCYegH2fQzJzwxGkJAi1L9AU4MZNw39bNAABavUFb+Y+T\nI5roKhigulTbKoztCEgN7bzgaj/qK1h6BhrTxk3cwIFywUwH2/WkHS8zIEjm\n9sFxXJ+1MBxa6NXF9G6i26tZVQqM63X/iWQgDkUly1MVN2KiYQMh9LI39Kri\nhFnRlHCC9uaXP9mqVquVZZFuGsDFG5kpcy/keuHXFmfCVD2xpSfJfsDKgmxs\nu1DsFiebMQCaUFdCSP0IhGwQ1n8MgnHx8MRbqmHrCBWvtMagpAgfO7VYCAod\n1BFoVEEMZ090c61/5HLZ1r6JynMW73S7r3JzJpib51VM2WNKvW7qN67zUo0m\nWn1A+YzqhYK5YKFGocDbTHZsrFByWEGaI3cFCF80V1ozWJXaf0PjR6ZS0hMV\n553VbguGRtlTQ2RUTUQvSZ4I8TQkxoYl0U8ZjUfsRuJcHLBrGPt0XTlkiuwy\n6hhD\r\n=3kkw\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.3": {"name": "embla-carousel", "version": "3.0.3", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "f060efa931904098bec4d169e85c0801546e993d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.3.tgz", "fileCount": 41, "integrity": "sha512-5fBF7p8LlNeAQonJCeBuy9ibVUequKOGXbfIx3pyr0D3L/MoratIYowr0ZAbh57d9hLvhQ9S/v+4+fZ5OBtrBQ==", "signatures": [{"sig": "MEUCIQC7m4SWUfYYpC+VciMgRT6VsixHyCla2NohMfeuMVJO/wIgGohOO6PN7LxMQ74gj28CINB6N5OSVRkQo0ERx8SRCeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9gAWCRA9TVsSAnZWagAAoNEP/RMhusmNMfLvBF7kAtwy\nu8j48pNQ9N0E+YQJ7OAviIC/GRoQKF4E2/OOcv1A28GdRWqfMpXKDscz18lN\n0fhHa6ZF19NZsZX7NTWYqNx0sZ7j2vGhGswiqTCiySYn8SidrSdoQucTpwh8\noVV+wAhLn+P83bqd/7kaWfcndvPYMPO/tIdKGsV8mfJhBoG27DYpxPNQFH+F\nMU3chlNMhRShvJ/3VXhxIipiM+/jNT2dN/4c4MpfiXnkBL4Zke04RmcB/GKE\nVdiLxV5BWDH1+pD6zco+F2hkcL38smFLBANZyJvyON6M2ZCcGyA0TIoPc3/z\nD2kpRlY5FfolNwCXB0TzONSFAxgLH/jg1MHsjHt+LSSCoD8GkAUHQBpYCCFW\n0ZQ4hzdiKdzXYf8ePgeLP/p8tphG+HLr/1tvFtQd5Q5abp676FeXLv9ZbvbF\nMx5ThFlNmdv+NTsjTtZb2cUStllefakT5EylK9B8x6CKXf54eYmrT/gfUDIm\nNetIyqJzIZDHQeOV8wNANpq40JfoFb+eMkQDzu3STJHg8rbVX89AU4Kx4DAd\n730ShjZPHsEOjcvch9h9P2hWsj4pgcIjvkbTeZ3FiU1i9KTNRNyerv/dq9yT\nW5F8FDb68CpxZmRiL3C01emkJUHQuZg9Q9Ye2wyneerG1ZJ8EGA8dWJVPx+C\nUxZq\r\n=EqBJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.5": {"name": "embla-carousel", "version": "3.0.5", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "d533502513de518f2206b7a8efef53a65de75cd1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.5.tgz", "fileCount": 41, "integrity": "sha512-eA0dvudI+kJv+jgmP1e7NT5IIeha5mmoFuAzrjQfc7a5PP/KDoR4WhllhWAyoJAEW4X15+t1CeC+hxWOYiZaUQ==", "signatures": [{"sig": "MEUCIQCAJ8iHCpr1supDK2bQrVWLWjkYQ+rap3dMUgSwoBfYTQIgBSqGdEFiwFhfG5dHQDbW8mcYc2uA1KEG1f6/gUvTOH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9gd2CRA9TVsSAnZWagAAI+wQAJK2EbG1LPGOMoWk11HE\nlZRfhLR7AAHzkPWlFJjwE8pYiEJjnL3vm7ihb1Z3i0gDrG//54uivDUvZ8/Z\n2Qfy97e3/luRRed6Xi/4OnxIcN61A2U0deoRX/gnbMuO2tAjlzzLh4bjEisp\ngpRBiC9idyFM07b1VdiuhmaZ2wIm7+Md8yC9RYIoVig2OWviF8r2p0ZPUsuO\noRnFQy35pGm8yqlKd0NtaKd2zb6+3Q4hsciL1nbQLCVpnsZNzazZ5Xn3O9ng\nagGI6ARN0/K6sECWCBpuSLL2hBO2HiDdvbjkqc/AW4El1MroRj6nRh1q7M7Y\ntwGc9DEfBL0UL5XZgwfl/+7QvHIiQYSxh0mGyyuY/OgxbC9rWIqnVYJeccdU\n71bNTeyfWtJkYlIwmV/l3b+u1w1P4QqGrgDnlJf26TWvHgAAv5rqJmFQql7F\npHyys9ZLcldUvwqBKk3bBKb1er4eJ4JpDiFuhq9pzJBugaBmKr55sQxkh1D6\neWUHBEYkaQI4BfKD9sAdYl4V+w0s6+sfTf24+o1RBQEgOGPVyjAJKVcRmrJ2\nXKaUGggW6wVreGj36pUdMhAScx7AZJNZ8L8Ujs8cv9FklktykIoLTg1YYsyJ\nfdgHcCW7j51DgiIEoJnJ7z2DYty4bsT1+o1tdPOIHlMqBy1PEZIWvFkhbcQv\nqiDX\r\n=9+yG\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.4": {"name": "embla-carousel", "version": "3.0.4", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "4079d4c726d0199f51cdf8d2965a55fa19c8e87c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.4.tgz", "fileCount": 39, "integrity": "sha512-gkBkp7cQiegOUszlYrhJwvAe7x5d4Xb2b5Tj/w58vamti0/Qs/LKaGLxaL4Q1dKvcgLY+32t+V6hubUiPbZoxw==", "signatures": [{"sig": "MEUCIQCINPITSkWKKrvTXx5cSDAPBztP/KUPMDI052I/nzC0rQIgRQckCQ/HjBeRKXTGfx/Xy6Md16enWdRXKLWIK4B4i10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9geRCRA9TVsSAnZWagAAjyYP/A1JFDJAFuP5VFX1q/Nq\nZWCSmDnWViMjD6CVxZ2qcAk8dnKQvlFMOMYkfdePGqRJQ8EmEmGD4WLNkEq0\nrKAkfklIB20fBUkQEWoqcTT9uJDfwE7OSLRJ+g2gbVpHpsd4L/YgGsMXD12k\nJ9mGBM3J9IiEmSeXy27PE9y2lvgEVCzk14YlwZP1tVaHFaKeLC/tiFVwrFdv\nq7Xns/L05eN+A/s4M3fbvFx+JiQvV0FW5n/mp1SggjJ9TLlaCpyMwq7nYzMd\nH1tCRo85sVhrfa88N9xdv5nEG8xFWuLblJxr3EcLBDcnFHjt8l2vnTbpJFxg\n/yfWoCcC4Pay4R+vJMTpdrgbb9LdVeSZIa1OMxTm5E//c5/9PI+M4JlpOClC\nrHQ63hV7/kJ9DTEoduW8BsNC9zO9pXCgnj1ygXefKLpi0z/adIT69j4oelIw\nQtz54CCvApvUmU3JOPIw/XSb7tS4uqDkubIRksBOG8m6oHhs+qAlRx5EV0sc\n3G2SEFOtLR+ZHeFoxWhXq/rrUhaZwxX2h5/pSAQf/SnOw9b8jUrztmwqrcFo\nZGVyYZfw6RWek34nNz9X/CWo2T2v2ejwK6Sn4eB+o283onHBbvvs3MAqNdXK\nOXTXD/mgsAEfCvriCmJHb29+nggI0s1zG0TUc57Ku7lbMhYPj3fpP0dHUZM9\no4yz\r\n=SGhF\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.6": {"name": "embla-carousel", "version": "3.0.6", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "7a9b002056cd420eebb4088686a5743235525ec7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.6.tgz", "fileCount": 41, "integrity": "sha512-bKlWoDMwGrg7dZIAb2YGxcgLD3natcdLrUEGVG8iZvlaDYYpUmSpl12g37G/ejQ3xeh8tEFkTYLy42KDT9VrTQ==", "signatures": [{"sig": "MEUCIDfcHhIUfgKgb8VOsBnGV2LRLMbAyuTOTbdRwnzjJ2FfAiEAheC988yPH6ufC96PZrGNa838S9lCXK/w2ka0B/Juwbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323642, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9gyOCRA9TVsSAnZWagAACA0P/0POmK4uDTE3vMMe6SVH\naF9/aAk574pMOkVhCUA/EypHkAPFHHP9YE641NOrM5+au9csHeztRgn0VH0Z\nsB3YlrHYRLjJLzh54TVxHbrWlTjiZ8m50f1P7twpA4HS7OKMXpJAR99kiTII\nakU/HlDQx6CnapDKXrCL+xOCV1C3pnVCVmhRObXyHH6h3a9v48lLnHHkcL0+\no2H9czGxjU8nB2TQdhl9xNIzFtkWhZccG/kBeF4x0eb9Xijewtwg2ikxug0x\nYGYZB2zhD8wwM1UvweH1qjjd33f1tFogQ2iDtP1HExTM/Kxo/7OARyTJdfRC\nl8sWReaQptWmj79zacpQDeqYTgTwHyqHAOR9HvulqJ6TKaX89iR8YeBZAoh6\n6e1LhUC9lxBOCEpvb5pfREHQ+50PZZglmscLPLZstv/q184X3v0UA8bd6oI/\nsOZS7HwNkpDStOq8j4xHtu91NNmQOJKrPbqAszRgb8YytoZX9H6gSnWpZsEb\n7Njlf7f6+KT9nqpvFXGT/x/uHC5VmhqmPbtzu8UaGVxw4oLmi0UogAEuoCqh\nzAnT22asBqlVkoWXdi+0wYA/SzZ7iUq0QtWrxumydMRgT54CqbuZamMK2EHw\nblCgQvQOJdqu6oPwqfGiIzokgxRIm29ssk1yz+XR506186aH+N/CYwmysigQ\n3dZD\r\n=pE9J\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.7": {"name": "embla-carousel", "version": "3.0.7", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "f7428b9bf3957842ce8c7799b94f569cc7079787", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.7.tgz", "fileCount": 41, "integrity": "sha512-NYv8L2zJioxGYszGk0b1oc7JgnbZKtVmN0/mw7jCYQoHwvVnTFybkRzPuJy+5OaEv8gxumRIj8vwi8voiozsoA==", "signatures": [{"sig": "MEQCIG7BFpT4OirrBTlF3tX/sqt5mTcphMM1s0gwGFGNQudyAiB5G5uzHXkHXfueSKhqepdF7wFvTHfFCs8dwbMhXTN1CA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323942, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9g+bCRA9TVsSAnZWagAALgwQAIxa5pAHbkgaxrhAUmu9\n5rQi7XswWIZUpnkXmF6jLyal02CY6FR2q4TbTpW9Up2j+XulzLojIROpldkH\nWuWCb8Mh3SKnkuMZtYzG0tBGAGxpQZS1MVK6Z8gp2b07gf6l0S1BPQmFEp79\n2DoWRn8scrpWKYHfFS90vG4rQ8OHwJDQtwbO0AzT0GNgJ99G2UD85jQECdlX\nmGpQtM5Km4Owk2fg7+wPtU20xFZmAApBBTcFTh/kOiXfXJ5VEJn77M65j/V3\nNY1DR8PJQ1rvqYa0mlb1sQ4yFeLMlTO/iIGv8Qd+IjB5jjGswYA12SgfmvHg\n9zAd65M1OV7kyoe22UlcFl9LifgpuFpjBYGBX5Ux0K5u+MhY8LRGyDcDeo2i\ndyxbMAboP82BF3Fi4VWdY1qsPPlFUXTl8e2B67Vmrg5wad5yhHkPBqJ4ot75\nRcC93BJtq1mHK2/mII3XLddR0YFx3uUeOB2W+/0rF07yCIzOKfwVvNMPMlpF\nv/sbNRll7Aik6tgnldckO1qpDjsksjOnrVgdfReypTnlz58TzMEgqxKmfhaR\n0JnYc7+Iq06Hdubq7QjKfrljp75xuphvwkZ6B/GCjtssIPmz/geQofkPjlR7\nVKZlyME0c4xZNHaWUlwlDBCQMnwc1aQ/GRl1kO8Pm3jDykUWAlkFQFbevcLi\nw35b\r\n=TPxs\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.8": {"name": "embla-carousel", "version": "3.0.8", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "f6987e183735ae2d63375ec1910535a044f5e468", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.8.tgz", "fileCount": 47, "integrity": "sha512-SnCcOw+5raM4ReGNGa7Tdty4cvjqcXIAfhh+01AHGGT72kT3ryvAN4nvSkoJv1QkdqBY0L6oggkmaaYujn1vDw==", "signatures": [{"sig": "MEYCIQDc/rYpPiPjZWsWfQX7zsTDcRnZ29qmuq5GsxG/Gla75AIhAKaHWzDz3h0ef+7Tx5CP9rgFSb81HqhXqeuU9iWPeeuj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe9ygeCRA9TVsSAnZWagAAdCcP/jKI+hA8eNPGxhxZtVIr\nRPBJtb5TRf9oRlVcQMYXmjmQYzRSwSj9JAKu6O7MaoPYEu8+pYs9GZYBle71\nC3NqmzlNEdX+itbe60ELLfJjrsQJL20R7bTTU8VBGAltyWG5LmErYs5ryv8z\nJ6p1ArSlSyW9KxuZ8su3yOmAh3cFcMG9ArhoN5q/W1IiN3BT/l9KNX7tStsi\nSTu0LbxzNtyHezfcB8g3QKyp/fzN5n5pquvVzk1HzfW1DIsjVcJfcfESvUAS\njU8su/Re0UPu4zDtCuDDTcMIwleg0d7WzigSWw8MX8DuUNYIFFKGBIn/TOz0\nUk/xpXe9gmPs8oi4CxfnlFT7pfrS+/5s+tgcW1yXFUQXHQ6D9zE7VYuHdz+E\n4J3VKv5rjHVzhgk0PEKbOvbqRTgGcPVlNyrPTKn3ULnXGXytMpFh5AxG2gK0\nDmBVy/a7dO969HKn3z4uytIo6e975A/X+jtoJPFIOceYsAbAePOChl9EbZGQ\nhK23MR59GMKQGEo02Prre4+CLIaq0MbQ7Hw1Ahwbuf8B2aDdkmn2pCS5ybGb\nfHh9Y5g7RJCX4UpX2uB8OlN+8swSlq8IclX7q8jvc4aMQ0+qU1vhDOAbpOJI\njJkiePMwBvQkBefesaIx+C51HFGvKjIOawEC5kYxMLEcoQDmr0LRCevmK+xU\n/jLP\r\n=So5z\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.9": {"name": "embla-carousel", "version": "3.0.9", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "e2b90edf795867ebd1a912af721cacd5a1bfe3e9", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.9.tgz", "fileCount": 47, "integrity": "sha512-A8w9Zad57bG7YRfW+OG+Svg3d+PhILBlvwi9LlzSg5GLDbkRBwwezSf+9ScqSts/XAlTAmWP6gR+DjE1qTMW6g==", "signatures": [{"sig": "MEQCICInak6s7lFvQCE7gsY+WifBVnVIFdaJiXPRe9Ws9wNdAiAQeM4u3m9mVmXo7WnnFcbfVKDwToV7hxkQGit0n6+HAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe90DRCRA9TVsSAnZWagAAvZcP/2XmO+IDktwPuhOdqfCh\nIQjboNcNuZR33feZEYTUZaSN1W99cHYIMvT0ImqVab0Ho3dxX9cqhlwEtuhR\niV0ShDMvKnbzAuYX/VgQaSK6HJSBAgkEGz5aiEa8O+d5FRxygKDkb9+f0DDq\nZxsQcyMX15GEzLMDFH3N1c7otvAphhkVgUGpR8f4cvOhrFz81bp7tHEeOcO/\nyg6Zt9DeywPTDhFthCB8q2Krjb5V8Nj+aM72uB1sJvIhUwJg2J1NB9k7r0Sa\ngGMj5L44xcj9FpeSvESvqowD/9sokGDIjjlF3uLsPyLA0At5C5GFGmTwlhGb\nimL3KGQkmTPnXshdMAm+oMOFpBWEUROVUgfvwIehHVXa7jQwFSJQbAAzkspm\nB7U8+zRfDSv6Zhev3Mu4A33holC+FhhwhTpq0GZ/ezrcEsLdyAbSSFXZYYRq\nPRPAPYaMYMTXGUuyh9cA8lraK6WGoSWe7Dqm9dzf0zqbvoKafaWJy/4Kfum7\nsjaxj1Kk6ca8VeuVzt9oFiY+Jx1ilEpu9fvYlxTAb7E4YnYNEAHs7uq0K8xo\nbGBOnUsZ4/tONi0ZaIgcUfLp4j7vGsiC3m+77EfdMxeofTgxFl46bC73apUB\nFYJwj2FqCxRUnEf/8oQSibFwSCyh3dot2I7iogntEIn1ydR5XAdUJOHZ0BCs\nyKtd\r\n=3lZr\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.10": {"name": "embla-carousel", "version": "3.0.10", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "2823d33069d689e368d963226fbfda160d476c0a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.10.tgz", "fileCount": 47, "integrity": "sha512-I4MW4pt3moGcRdJSmAmkfJmKtPNw/n7qZNMjfbvydkPCcZC5u6TEHTVuuuOc52tl5ZKw3ne753lVKOTZrhenlg==", "signatures": [{"sig": "MEQCIChSqU/ndkHuPkNjsV96DJUvXzYxhhT4iWtQPMCk9fhjAiBVTXYpXA5rJOTJxIMo+2fxh042qn3eRYPj544jyGHp6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe90DXCRA9TVsSAnZWagAAu+cP/3SPPHtWMOTyb3fC32Tn\nZ/nO64hUetK6+cFv7QKFSbmuMvkiquiv8j6eTd1SphCPtOx9dhnvC5RDN9s1\nsdHRbFcHH7A2l3kJT70gwfhcOQ+0gOY5fNGw1VkmqDuMrh4iIA3kbwK6iJHj\na/rzmPGH/rL8yeKzkIhfIpS5FEWCi9LhrDQNJE9dd5yqRO3jn0WJT1+ETEnB\n7ZH+IaMneSPlGoFJNqDGQ4ozSamCX2EOOI10DKY5M3Qh8DaIiPidRQd5TBAf\n5bgseyi7onVMrR4z63X2AW/+RA51JXFoW27SUj+QR0uFhMh+/CaoyZxx4dw0\nQbnvFIPKCSGfJY0qgnUJdyk0LB8vo7BqCOJHjWdh5dSxyZ4mktaRmvBA1qob\nE3ZC3rkwKSIsIE/UQa84JThKSGqrdmzxjjHBsitM2zJmUK0t9/zVNbCOlbM8\nSoJOXq6IIfzUqDVGVPhzVlSvMedjXiwb5CBupa6bDh6YDNmzEKw/7/qZ65nm\n0OWBy8zpFc+q5kvKu1G8lrRqrud4etQP4LQ1biJ+vK0UeaWrmCL4GS7IQQcJ\nmq0orgIsB0iMB6ZhYN59GwtSC9y1cNZjLJK//IqHYyttTFFi6/fCxqHeO2Mn\n69R5jhnfSop0M5gSIfxlzhJQ1S96R0ihCNjK86DKS+/agNAVrSaxlelOKyPx\njsRH\r\n=vFRU\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.11": {"name": "embla-carousel", "version": "3.0.11", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "4cf8706940bd6dd4087f2f5e8e77043a2fbd86aa", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.11.tgz", "fileCount": 39, "integrity": "sha512-egKyM79+ISv58fTt+pebz7OL0nwy1Ckr+xULoTNwZIDrvXndNSD9AyfYtg2MncGjWq35k/a9IusLJCqgFS5AhA==", "signatures": [{"sig": "MEQCIHz1VZtIFIJDtmGn2SyGhipP9/bmIkgMJKnpOHvVBnHuAiB0iBZXfOR43ZP9xu5UrSMGus240uDdFu97y2zCroj29w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 339408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+C8UCRA9TVsSAnZWagAAMmkP/RAz0Paew0rlTZiv23ad\nqmBeGARXSIJrEQqzZOFSHOo/pubmlQEb9QOeY8sTA6gLag506rnriTWZd4id\niOPfJhnnRd9/wtzJ96kHYwuVX1HuNDCOOFsRaYP6fDu4bnG20lE/7Nh+4HAO\n+m73LlHwTb0G9CnXsg3wQYIRvtEsfuoTCvkXlbsvBIfH7/6ve1Q7oeqCweM3\nFeVzc1YB4plXGNw0jty1EeEaz6jy+ik+1tV38P23rkB3AYzC7y44F+e/G8CO\n8hxNKvxn+VS43YAk/1I/v9EkwMarSAt3mHhT5PdqMWLDTMwy6DYWna5ILd5s\nmqnVPjYVTcJcfo2dUiKPyhhEoDHJ78NikN8xMXVMDVTU17WoHvdp4QO+1vnd\n8n87i8kdrGvqQ2eC09epO36AXb1OTWpv5gaXyVkxdXswvES7r7Fc6QQKM0sF\nbvQaGR0KM6vK1NPIWSnWgqYOXrGOifwIhKIjtFdUjCyeu9Xfg3KFSti8jZFT\nbPV6jrZBEJ6rs3uwA6UhDaR6G8H9/7Lolihn3M2l74QdSSw7CHysK15hhUJY\n3nzN+UUoqf8tnXAWoOfDMqKEYkb0J0cSUbyJmEE5CcD20CRr/Q1M4DhVZwSu\nzHMXcrqIGkpfdZXuHe1zBltG+sgyumJOnm11zNtllonMF5acRuenD8mIi271\n0SWq\r\n=3thf\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.12": {"name": "embla-carousel", "version": "3.0.12", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "rollup-plugin-copy": "^3.3.0", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "31c8c0ae11bca6a09c6e21986e13b0c5f528cdc7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.12.tgz", "fileCount": 45, "integrity": "sha512-4GdfV0+NrdzUVhlvJjhDjvTmn0jnjk1mhO5AEYkW5oqHnK+WtdjnNPXg2Qo6YHL0riD3HD9qFpfboYHhR2R7FQ==", "signatures": [{"sig": "MEUCIHrPfviwLa6tm1uF3Qy5KBWf0zLNPL0e9xiyAweklycNAiEA8NqXseuTFzFZyqDrVs5kl1dRi7h6NkSpjTfvOHPNRTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 777255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+FljCRA9TVsSAnZWagAAlUcP/294i13uVDb0K5lWFZW/\npwdDwxNXHDcA18JyvZr3TlZXJG6ib4wmG9uEJv6j1/0sgRfF4JQyU2cfnwlN\nfAzXT4NBSEMDC6IFQRZeqy2WR6cPRyaHd34EjznYPil8RKIfSI2N/Q3RU6m6\n48Tgd1OLvCkzjGW3Q7YMWhLaPXrNcFJZ2aJTPF8+iZ1G/Qjd7pMRAY9lQgtn\nUVFn8WTtZIgPnsIluDNusmUHk4IYSR3PYg/W/mXVwNO41AuzeysNa10Dovi4\nIOQBpe9OnBsftV7QW6/Ns4D4/KN50joc2Hk8dYru18yuyR1hKVTmcH2Ac0Ag\n9q1t+nlN1BBViuUmQC76pbr2UCngF5y9kkSBpj081WX3gzu4R6zi3y5JyiB7\nUGn8nCGhiudvh5T4Y7u+kpBa6r7Bje3VM9LvyAnxLvYMNhdbEgv3/n2UESnE\nlmW+TrnSLm5h4PZiqiWcUT/E+o8F/RSmoYGPJszsrrGlTvDhlY4VvpoObWwG\nJeIqwJ1w6KuonybfJraVYbb2Cgw796Xct4pAhPRqhAISMLLKZX7B3QceVTQR\nlfTz7MPIX3dY+cceBQiADXUNMeKdVgTvm3P9BDx5+6d2RRVznC5wrHaCEvbr\nF/2NgHUmJNAv9NYr1kCLSx0f3/MwLEu39CSoyeXQ2Ol1eUqaB56bmKz1H8g9\niHcW\r\n=nT3a\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.13": {"name": "embla-carousel", "version": "3.0.13", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "71585ff840d186110e2128bd89706f6ad5b9f8d8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.13.tgz", "fileCount": 43, "integrity": "sha512-41kIl3ukJP/AuKF09bEqjKieuAKueVMGhS7yVacNg+NH20Tfjst/rgW1VFUDmOv+W2Wb8IqIOvr4+Pn+2ZD0xQ==", "signatures": [{"sig": "MEYCIQDbm4xFPlMYYZwrccwafIJrxzpOTQiiVwSbctM5lrFMRgIhAIK5zaiopxZcEbPnTFAk9GeO4SDzoI4MVUevtOBxJ5HE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 776836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+GFGCRA9TVsSAnZWagAAk+4P/A6I9tt71kqr90WnIITD\nJ/Kv4LrXNyT+GnnCZsxsBg1mLxhTlpDQwfoCYj/tT0qNaghqpHwl1V2A+J5p\nO4H1WeW9ipKDtfT1mkXaj7n5jpMFSFLbyDlFemJEoQj2uhhxIFhNPxNLuYj9\nR8iTJhDYlSjMS8kAbqHDkdFLDa1NAWXKB8jjhgUo9kH2cmE5cp8HlAsZKhL8\nm0Lce863m5qydwvr4QayDHXwqZ1oCweJ/40nLGb9duFMFRuk/kaRv4+PACGx\nlwg3z+BFcFM+y47/LCZp/h5jCSQ4Ks2Si+d7GVxulunylFEHUNcmNWjUh4ln\nKsV7asf3CkGgPF04yOMmLg5e2BzRQhfyxwK5WOvyfwfXELJ0n5v3VT2DpWPP\nDg3nXk3PmuLFuP6IwoBXbClJofSKLbWUgpyAqEA0EHwU7SN7orH6Fx9owAUF\n9OATIUeQcNDz5PnpWcAvGoLNXQ6sK0R5PUpE8lI4y0ttKkM8W7UqjBM1mahX\nXYg8n03733e48uNJ2nKmDYlmG35SgeJhFEaQidd+XCXXscFJ03SHOGJgAO3N\nxdjZR4lf+7n4XRI/7Ud4L380tCgVCkj+C1foU6rY+gTdXbFfEVWKoq5lk5a+\nPJRkQbIsVOay+h3vo3BvyWZvpa1eqQeG7YTL3jGcXB3msIj6pSZlrnCPZAUF\nxc/+\r\n=99LS\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.14": {"name": "embla-carousel", "version": "3.0.14", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "3f5b3144eb6d397146c2a8b20a1624bdcb8d00d2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.14.tgz", "fileCount": 15, "integrity": "sha512-3Do/YbdesG0hFpuCmyRn+GaCA+wvwy45LWmjHrv3ONkTNCH/AIS1/OOs+mgC/UKDr+gsnEhBifyRE4qwVix2VQ==", "signatures": [{"sig": "MEUCIGGOfOha4qKEmx8yy7oKXipzGxVQ+7qn1u1Nf6Oc+fVsAiEA29nCgf82Gox//AJRk3IMt0LlDAekluRKWyO8OO5Ryw8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 761925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+ZQACRA9TVsSAnZWagAAjKYQAIwfqn55QcxBbK9iID2k\nppPbY/VmOIzEiEGvy6RPYbt4uSdRnVKnIxxU8zxgUq6R5/n74kgxLZNSpdsw\nv24Ppe1lAHXJ2gnOBHC5qb290kgrjUioOtX4weuV0avE0oP02ceMzgtHjYyK\nTF8mIRYUZLw4GudxRDuTDl1aUPZs4sOPLF6hXASzF5/LRQyoIzayUID73/CR\ndtGCONUe6ksXBlWwtb8vGYEeIG0rcIGszKNfu6fhfNLUF86XaIwmptDFo34u\nEu60q35zBlEU6YoRGBxjrbcX2feLCT3Dn2DlhEMVBamJyf+lVT8t94Zdzj12\nJHWUurbjHQuz9LFHaoWP7y7E1kDQeaWH9oBVNoWYC13wThk1rk2GF5GBAP2q\nd8J0ZhUsVZQmHYUZdnC1LIcwjZ4II/crWUQ1ckYgITsewSNAwvjb/wlIgMCv\nIQ4+e6os7P5eIFCOzTOyb0I3dBq41hthLFEAtrCvMC/01v1pbCSo96ZsiUum\nKeBIv3Gkw7EG8jBLDa1ebMF4RoaCYYTtm8sJSaLo4N5mf8qbY7eJ/FnZC/Sn\n+GoPG97qH4USx4pvkg2kpuEH6pFTsUSbKoWmCMF7k+4H161QVuf1VscBo5Lr\nhURqjFj7fhEFsYjJa3iWG7OXXKmIqxZvGOMPGTQlJYkITtZ0POfupexzsUZp\n+c58\r\n=Ye6p\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.15": {"name": "embla-carousel", "version": "3.0.15", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "5173ba910ea83dda3c06c64ef4774d2912215d19", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.15.tgz", "fileCount": 15, "integrity": "sha512-9Q/wgnCE6AbC6r6x51zHhKKViIvrwOpdT7YqdYIcJc44VJkFon9bh+laYU+WxL0kwXl2W3yEsmi4T8JZxb9XwQ==", "signatures": [{"sig": "MEUCIQChC/456qp3qGnAdEsTZZ7QScA5Qb5kxdkJCc9zbe2ShwIgIBX2BZSoAlcd+CHFvEuaOBxMFCZbWwoj0tkC0jld+Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 761925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+ZsCCRA9TVsSAnZWagAAD6oP/R69NpSkG/dJZSj0MuuA\n5vqkbcCABvYznBp4HxU/26WJOfIWIIl7YGBg3AZUZjd4MYP054rngYHca3VY\nKx2+/ikk7ITLvlT/jtWTVzd0i9PeUCpOnd9rDZch3aEP9+ydUw37k5bjYIQB\nbemBc60VJzGODN+E/ricVglfS2wwHRr9QhXHQkDbaAp4KrtiapQB6cq4UeZm\nNtLhMUXA3k83WSxKe2jcK3tE9s6rXUj1lyS5eFdPbB3GO1seO0xhBjHNi2ZT\nuN+1HHBcI7s8txHXxQWBXFRvC8Sr5MMk64tAvn2owQDYJy2+rW8P9LCP6lYP\nViY/0b1/IA0h3rDPor81ElreyZ7t0KbcjQTRRaPQ0Ict3zyDFMHfANLsHEEZ\nqy7I9jjXYHBukKgdnu7RfjF/iLBsvE5gy5mKyxBS+xkQD+tEEhhLCsLSbZFh\nVRCI3LA4hRSj1r7DRN9iOeDjl8+4826e7XYaVM38rtiBrOzyUjbGkCMKbCBG\nhL4YBe8h6Y6Bpnsoa9BvaL3DB296F3jJAmYoJdrqz3vDhBMMLvAziL7PhCfj\nucPrNQNGq4IBHzP0NzT/2XiiunK20rmDSfxs1XHmpkKvZYKk+cSkpnB4qyGC\nQfx3IlCKOeLATpIaoM3uNnnginE26X/Oc/EgtTGVSADt5qRA0UFtAp/Rftm6\nVKe8\r\n=oMjv\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.16": {"name": "embla-carousel", "version": "3.0.16", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "47e8aaaec9d8a8e0613d972e132f14060df89e3f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.16.tgz", "fileCount": 15, "integrity": "sha512-U4H6pasKMmTzmJtk0erD1Myx069PfAjo5MLbJ3m+eG97vEI3ZOdUAB12rCozoNO2grHpuHwtWg/rm1zUjx4c8w==", "signatures": [{"sig": "MEQCIHF8oo1ySFmVdEemNvkWiw9GwmNnxb+lfRel8i0fPnGuAiAJt6Ua+zXznoUBQ+EVgK+TM/+R0ZFbCZyYN7CRFfwEGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBsgECRA9TVsSAnZWagAAb3UP/0EpOv+9M73JPKymSmfI\ncv6iR09sr8tVyVVTscjui49TOCVYhBldz6h5fHBeFTpMxVvEs/cUenfrM8Mb\nONp3YxFHefNsZKEIJEyPEbaZgJm14Dg01QjwzD9BptaHyEZezF34R9/MkFEg\nk1gQVsQT3I1oYLIxbPl89rXYI07uJReFccvUa5eAYRsCUp7pcPdVCT9cKTVd\nfoJvwRs20EBSv1YNBzyrBFrstAVag4RLUmlKSJ1o8YQ/ZuO6rj2Ynw2wlWV4\nXlslUK9ojVp5t3MdwCSv2Auu5poO4gKjK3tISHNknEE9FmZaO0nrT0T4EiZf\ntlDCO20xEFjtbH071nzwC5hID90SP1se0fVPhndW4UzuaUqxWKZNfCEngBBp\nZmauAC+oWKE5RdVa6bZ7Au6wekrbTaWI8rh04WEcIaKcSvjT/hyBvgwtihYL\nmNq8zTdWRuXGePQvC9AWwSimMYf+/PkQ1uycssrebOkN7c15ZBfNNrRqafQS\noFQ+CnjGNMftgLAKHPCwcxWayDyNh92+rrJi3+yJrkbsJ2r0evw3zI/+JGs5\nEFJhfChJN7tZgndYtb0RI4KAQVJG3xF/n98NB3ZYWsa/+dYAoTVSp0M201uE\nHRF0hLzA9mSBU+gPieByYVsbl7OeibC7ZrSWcaYWe9HzU8klemZTYhPi/r9C\nbM0c\r\n=4R4E\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.17": {"name": "embla-carousel", "version": "3.0.17", "dependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "devDependencies": {"jest": "^24.9.0", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "058af4ce26afcef8076ada09d1df0e89182c73a2", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.17.tgz", "fileCount": 43, "integrity": "sha512-0neXvhZfZvHiJKfWSSE0dE7369rLHMVo91e3Jv2xWR4hGqZFN84+BkqXYzs0KD6bQ34quhlhP9cWJRpuItNhYw==", "signatures": [{"sig": "MEYCIQCgkUTsh0NUDbZkBDypjmEBiFqstXLwnFB6tPUguDL77gIhAJ9dCPRGMH+CTuydRMUuMoXvjUnv0A0RpY2yoh/qX3uv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 738197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCBq+CRA9TVsSAnZWagAAMbcP/j75tbLN6i/ctz/b4z1W\nhSHV78wLBRvARJBub6OIAdGV6h/mxeSBlqr4Ej5PqW044Hk/23DxeQLTEGU6\nmOtMQXhVfn1cK4wU8XRjdHTmSaTYNI4LqNVDek/ahTHiuv9EWCSRCw5wXBgS\nX1Si5Jshhyvyv/IUnVvYbUC6ROUrErJ5rFDClTUXg+OzfAxwswTl5SOf6enM\nmZooXpKkkOquI3CZuyI4hE3rbyA0mFUt6BOXcK6sPrz1gyofqR100+gzjpSr\nGZ5RWGOddzRrkEu1XZmGps9x4P+42L8OPnUH2Vi87VMKMFCEv5KnY2t0UWbm\nus0m/EkZGIgTSAonjYOxVP1N2pUBGyoQTJAahr1w4pBsJk2mSBXDWI0/dTwC\nJFH63M5EaDcErp/t9jYHZhHOIskxn4PbxJDbNznND2Kux9qEEDUH62GcIa2j\n1DrMSvrxj6SWku+W73th2pk+C6qJH/cowaVgGo1Cskr49HtQA2GzglGsJ3i8\nci2DOzI2tDYtBSpIAzVXQwy+DERRpvcdkln+AxppmzJb0Fw4sjxiVcOnY2gN\nY5IjRYBkz+k84dqCsS2r3JsXJBM2OUB8YnHz9Pz7fRyFCfZFowbSUHEroOrA\nIA634RdwUx0Cv4a0iqIVL5ihPEdnXYC8x2cONXKC7CTYYkUMvYqblM/fsgpv\nc74S\r\n=EQg+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.18": {"name": "embla-carousel", "version": "3.0.18", "dependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "devDependencies": {"jest": "^24.9.0", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "73097e5544fe4a112f805e2516618de328f2dd63", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.18.tgz", "fileCount": 42, "integrity": "sha512-jL5cqd6J33QWwPwoAWvBlcwR7Hr6iyMMMGQgLQ5YI5zw0BaQfEuDnfKcOT26kXpA2/3kMZwO52bTmr4GZgEHmQ==", "signatures": [{"sig": "MEUCIQD3XRGsqFO/zyLxKNyXC4KP4xETtU1dikIprYUCsMd+OwIgOmZoMhFOmoyv0rMS+qgAW592DGVPUiOhftrE4xqWOi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfE1U4CRA9TVsSAnZWagAAOPcP/3lllRH4nqK9FvzwGpnc\n7oLuEyNKtpnck78qHYLzVvKe58JwS83IsMMf2yZ7gDUTGWKvonsBV0IKjZrV\nRIt4GJ3lAmGGHVLOSYKSnzMN1k5W06dGDf3OI9+aBImmnt2gaAYHdk07YNr/\nwGTxP1TmeZbecXzGuz+2WLRmEyDOBSANDZDlC+nlyvhzHs0mrJ5/LMEorMgT\nbnAPz7Jo/auhtsuJ4Ky7uRWOq189ARHnzEy7mBuPLUcwyHNL9B/A7C8ZdHBl\nlnlKFfGx/e2CAAZJK6G/X3Eyv2mRoU5aAzWSip0yckq3Q2DuIO+x3gHwzd6M\n2iY1+aU+47fcT7k68d1BmIomE6UBecO7RmzUzZWMVdknEvUfAwxSWifB985s\n98Y0cbnaQ+Cr7DKl3FleO0BV1KHVZ37xBCsecAQZ8yVieiTPNQqt8NCpVpQR\nAmu9Q7NiiekwXPcz6o9UJn3HV2PQwRTM3VF38TaCk1xv1KvYhKYiX06lL0tx\nzkw2Cq7JV7jZft+kMWMHCOvJxwoEL/+fMO3MJNIf4w9Vd4sZ/YI7dzZ8xByP\nhC3G01/0nl5b/W8CehPbglvQ86o6b0thHSqCpE7dcGSY6dplQM6nDuHOEpRT\norE+QajgpjeQpz9YCiCveI5XWNfknL5wwNRuA6YO+k+c+FO1v10QZ4vvxhQ6\nZ4Th\r\n=I9s1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.19": {"name": "embla-carousel", "version": "3.0.19", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "d1358dce465b6d7aa786a4d546eddd6ff2fe0c1f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.19.tgz", "fileCount": 42, "integrity": "sha512-+04bAVFP6U7g/S4ppAB8mgarby+dHjg1af6Up+Jil+OvsvHItPdkSqWkbLWMYAicGcJgdpFSzVLa+mAuy17gFw==", "signatures": [{"sig": "MEQCIGTESL0qWZ/eWgqK+jO24ckaiHZvmOptgT72BiVO0azqAiBqeWg7zViWVohiaYwXxqUuhgsBIbghyLH5zbBo3BMbOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFJ3cCRA9TVsSAnZWagAA+xUP/1G1Lk7JeKhrpszKI0MW\n+EPtii1TQlq2aclHJKYY5XbxULRr2hLNzQnWd3rWaNeIuFuxYrjMvzsPcLRS\nx8Qf9QI7ml6ApSvABOt2Oxf312I8TPgNQMo1GAOXiHqWMbWp+x2+gJsUwIdR\nsqv+AWhQA1nT8/8XYxp+iZznMScs0iZ1746dla//xKnaQFek41gy01+zIXWb\nYT3CWgg/U8Nt0yy1aIFhkS6h3lRz2CuYVGJbenpS8o1E+DzE7HpOf7ov4dpO\nvUEiUi1FvaSFFoZXUycEgxzEvoN2yZlDH9AXR+qRaNZZFpwJzcvsOgGS8yOy\nS+LabptniugpAEbqoYHTOhIwvyaaMbFnest5qBSqDp4l+Ssj608h2mqGNZ8j\n4nU72rPffXiP9F8l08bdGxfvfq/Z+PSLqmOU90P2/WnLHHd5onBwxDgd6LA+\nntt6ypu92b37CoyZa/M2AdcokbUbLWAsY4CviM6d4I81P/iRDv3oV7/ROJci\nCP+6YtJol3k0Dx78iCQEHtXbgJOD3Jp9hBeOHC5STGfyNuuO5OotcUDEr0qt\nHoIMpswlA75nsru/Y5UgK9iWeFOFWLfkIbZX/H74vrbmzZh1twNxEfQUJ+u4\n7Vl9gwEVJyEUy7syoUXbZexQzNUAt8qF8MPYiPPpxq9BDSBhSfkbjDhIwc2s\ng/6/\r\n=S67K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.20": {"name": "embla-carousel", "version": "3.0.20", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "63617297fe484ade01bf4ca5a767020509ad651a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.20.tgz", "fileCount": 42, "integrity": "sha512-hQ96Qjts6lBL0GXj7g7jNdLOHzJ3Xl5TUoQbI4gX2iwR3OpX7R3GQqSs5W58wy/KTVzKEDMhyiin6J8Wk26r/g==", "signatures": [{"sig": "MEQCIEic7ffXdbt06yA8ztgtB64Uc2jmkfealtAiy8lOjYoaAiAWSLbZPmRc/nyeNTYBf/K9m1eGxjxt/g6VXcx66a32Ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfFzR4CRA9TVsSAnZWagAAAyUP/3LcQ+2FjMpz0wOErguZ\ngZsTg6PM8or6SjBcyu43bIytONu7JBELJlkwCHDePDUanDtLfj2/xHlQBe3A\nb9hfPZnKAwFFUzQF4rT0yvfGbkEQvrsvtNFphHzzTlAMoDLiGP2ulNrI1pqz\nXtQy0X973YOyH8NerML3CIAtt1fLhGnmICMsOB9Zle5bP7ahgYTKemsb3EUw\niwYONuKiqAj7r6ZdGYpv2s5UVHtF2HDu064SsgsC0p5qG3WzH/vGZAiUCqkZ\nZrehN5KEbQZnr/D1rrS5rZiflIp3N9f4tYaHZi+9p0HOPJpIexkXwfUsocrD\n8JO92qomQQpO+tmPlDdAhoWnn2ZbtCF6kkDoZoOV1jWuD9GYMdLGU9eBa5zF\nACXWCZoRH2pwqqx9t1Ehj2aVfTxLPDuMC1jtQAdl9+NPKqthEXxFJNsANa/g\nvppkLT7y1Kbnap8I9KzueDunFhiLWOvUAu11C5Mepk70Vlu3aC/S9VKNKYGn\nx+IB24SY6UNSGWGWTEDfQQrF0RNr0CvuUsUdmSmTDEYcZ8PJRqYwWFynTOvq\nEoHgQQh1Vv6bUz7R57eYtXfZHhgRo9Y4jkWQsD/e0gs5qgovahb7BYjkm5aO\nBtuz9b2z0qqmX/stBy2aebMctVoBcrlTXYTLw7fQQ3GEojDRy6JwvaXSl9w8\nfto1\r\n=VKWt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.21": {"name": "embla-carousel", "version": "3.0.21", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "da104512d030a4d55b985243ac767d1f7b3b5200", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.21.tgz", "fileCount": 42, "integrity": "sha512-ajR4HrU2LrkE/3eP3eE2CKP+nCVDF7omFcjQeqpd1bHAlT3Y1PCRhGrURD/4iqWQE7/wd1LKtRkkoDDwwf1zwQ==", "signatures": [{"sig": "MEQCIAxVvjV9tZzQt4GMxYy7irkHDbjLe4UT53yEtC80YTcHAiACZYOVgmb7DNPHzDvLvfe3cyvMiL4vaLj1jM/Gstd/kQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGJs0CRA9TVsSAnZWagAAi/QQAKHgLeqfLXUE0/lM230A\nz/GKoTTQ7zkZ9QHWkTlhT9bOyuDzaEGpXRHT9bIj8H2XJqG33pjHBBqhukl7\ndb0jvaKT3xRGKWwPSsvhaZ7+nXqJ/pkLDvBDQCD8Xe15phqRWCFiRsS42Af8\nUH9oaz2ofkXQWqKfsKN2LojC6tIguo389QfKF5MgL7IBZFEi4Ir0RtlXcGwc\n4R56QpTWYzuSKoGDWh+C1iCOSVH4jOurqiXZlaIvqNbBYAnN1qj+8IQrIx+O\nsQZSOQDSljDzF80WBH6O7h8NLkK5A+hW8auq2m+Nmo/EgFwOAJ+RZ1SY8AuK\nD6VVLGgYKpEBmxsuJwZK0UUQq1lZPKJvlb8lIgx/uWWHAtmRpuSBsg4bEla3\n1OeZIClJkEi2pTArXOLjYQcWTSb036LD2lXojimr7OfeylFAbG0PhjLUDASN\n0SgZRldEh/G7rKaxBgdCBriACP8tEOiTgjejd9ZZIkYjsg0HOeA8NesO34A9\nd8tdIXAxVE+G/ws7eyN3LtaCRrWxUiusb9wMPRnPVFg4Gk1Ga1UIwhGIGCSb\nRZa+q67tKDBSHGH0FuZo/mHFMLtIz2ECzDFRuHNtYKx8L4mDuOKwI9uPUdw3\nfJzSnf0ARw4KwBQ4IipUaqrulUk5s6c49WfjocGY7ryiYM/UTmF/dZBItEs3\nApDW\r\n=BNwx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.22": {"name": "embla-carousel", "version": "3.0.22", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "0b4ce9966f0fa720c2c28898cde8b9a0291174ee", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.22.tgz", "fileCount": 42, "integrity": "sha512-zbqlDAFGdDAt/mSE//UfBRpf0wY/h2fnUqB6K/8qSaMP13LZm2SbSSjC/hyd6o4BnOz0ky2M1sCabvo5q4IvMw==", "signatures": [{"sig": "MEUCICrh1rCdxE/nBD5n+4lwSoBoHrQ953rnSZPRdi/+1vKdAiEA/SwRELRejlxbTZTOUethKs480zI77o6n0ciyJ5EpOuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 652974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfImyVCRA9TVsSAnZWagAAwq0QAIW/wSUnzwHNdGS1yBZD\nzSxlUrVkfg6KGba6Es9aOaIh+7Pzr+r0hoBZBkPoTFHpkmo8VZpmwpd9/IaH\n6r6sWrZr7bi+kRaH5vtUKUwyBInqa91A0xHwBIDHqyxuFy7fI82RL/+rjor7\n77KRtUofRFDMk8HMOUdASlrtgjkMwMAyhrnqYjn08ECWXhaJ3RZ+1yRZDiqg\nSvvTSQ2JuG1RU8zv/GaSkDx5+SHTR3gulwDvY78kqWQc9KYxH0Hny9AqWvy4\n6HUGZyEvyNldwjR3xBY5XKi3bA9BQ/0oEBci42HRw4PihxlDOwMI0g3Hi0GC\nfCIJIA1lJeNIXgNUcM78PPUbciIWgFqiuCKXdqVg40M6lR4Kas0HoYihzKvz\nyoCeIYUFtRRNFHVKedUFjMFAnKZhE4qGTopnwt5H08HNIFCUAEK75Hv5aejR\nuRVonaCjls6VRO81xMf83IMpCaq9gaoVi2ea0ljksu78Yy/7dF9j2ugtAS14\ne5MkRg4ukrkynC7ucJJNQGdEd/w8wZmqyXq99pki2JNDoqSRlxOMfOIaxZdq\ns3A7OJe5pz1NtUacnYIu1JssRNdinLqOTAPjX7kttc5760cDBPM0gHrk1aYS\n4bQW/ZuwYDXf2OdBRd62Zo3u6lX9CgXFlD9i4ry5Kj3mgJ+RsqD6wig1OqV/\n1jf2\r\n=hZXT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.23": {"name": "embla-carousel", "version": "3.0.23", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "5619be7ef2f663c59c236ad662be9dab28cf6893", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.23.tgz", "fileCount": 42, "integrity": "sha512-CMi4aFamNxnIO1tU5yUSYfuA3omU1aUc20HlZitGKp1stvp2mk3Pbt1r3zETQWZQcUhRs6nnua8hiNRrgJw4fg==", "signatures": [{"sig": "MEQCIE1Uh49NBXRqxDgkb7F9BlPMbAGQ9TSMEZdIYaprLgOqAiAzZox3J8izhzuss4KTknYLHvNXOZqpfTS0Stxtv6+VGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 653721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfJnBeCRA9TVsSAnZWagAA9rUQAIaaYxfd9pI4/hdy2uld\n7PEIGE5rsVCPch9AUXF/sSWga+8aXGed8FXvjmavr2PBubtf1xD4lSqWjjQX\nfr0XeJ0X1zBH1WYqnyZBK1kdyih/TIt99QHjgCv0f8dgXMa/JszD0vRlGD1v\nlYTklnTY/EsnYibM4uNY3UjnwZe3I8W8vJVCY5P6f6FanVWUY8Yvtt4LxuQt\nWrvFzPewkShFWLC12atgAW6mf4yH/q6f5gKVGMh3uVa4Q2hPtc0qYXyZ3xt3\nJBn9aS6ZVxyeEq8D8aPwe9oPnhZkGySAdZUMcvQHeS56XJ/ifIsO1UOxS5/k\nDQrIytD/T4Hz8xOCm0uPfjmwlRG/IovqXtf1jxQ2VMpJOtClSa/ExulU8mne\ntA252VepLGe2qjwUu8jGnqxDUvb4PUdpee7jxY/iKnmoWv23tzxIH0rMhJ9M\nG+4RYprvIcmPw3nrfSWt9Y+FYKtYdl3Fmcd9S49/xi/wjSK19+hNcYv1+O9d\nAS/1c+gbhLUx75da7f1aBrZtlvQx5tHT5EN2eHCp87YTI88O4VLtIsPVOwib\nv5SoAy+8NqpGZQsXgNS0Wu566yA8kv7hEuKZJsF4EFn44MI2M2HJs5vR3L/3\ny3HwOYLhWsS2NmGqsJY4N3caGraU4Dyhi7bE0fse+VZpH0X7Wvizwa/MCsir\n1wY5\r\n=AxUn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.24": {"name": "embla-carousel", "version": "3.0.24", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "dce2e18355638beeedc0892fd366a5d0fe3c2966", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.24.tgz", "fileCount": 42, "integrity": "sha512-3gTVdHR9W9Ru1BWfbvXHwNIAer7+YJwO9iUNhMBr0dyumaXbKA/UNCB57paJ83FcdJF8/W2u3SaJ+91uA33bkA==", "signatures": [{"sig": "MEUCIBjPMLSAbwRvclOpBYHc3hH0E01nSQ6QaMJ+tRK5lYZZAiEAz5MaAv0VfuWuVO3zcF+WmH3aN8MkHUtgDbiJ9lsL8hY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfK7sRCRA9TVsSAnZWagAACUoP+QHYLNkrF+CrBZG6SpD7\naZ+j3so1bwgAxi48XI3Uc7vE+ZcJPXU3BsbCG/hpc2ka5iIt5V/UahlhXH4I\noCxL0Uo4cQ8ZW1zSt6fJ8Ahl0MW2804IE7Bc1+GFpev/uU7dnXAtWNVcFt3P\nBCjfTMJj0QCMiGKVGPevVRGXQSbI10eF4Dds67tG8Yj6AK/YJNdi4wrBVthw\nnS9+bE/yT6hjqv2S+nT3yhBQDZzJNE9ZumTDhm4L4E0sEO2NMgpCi7+69GXq\nMPRmgrIMhcTuFJPGN/QvH2nPP8tYB2Y0xPouXO9pDqi+3pLSD95hoBMD1MRp\nvWWN/yNjKMe1sY+mN6rgevSg4tbhBb1/FOeMaUgr1s8n60KvWC3SsKesrB6C\njDKwfWbwgR2AT0yOZ9/11UDLJM34XxTHXFKsCXWS1+XAsYwiIOw+yDEb4iG1\nW0ird7+hBQONU81V2YqrU74KlMIeTf8CK1ye+Lw21no82932hF6S4xT0ZYWm\n4WSXdYiASorkO8ZBWN+JXNJiuYBJ3qdUjvQ2X+k/ln8XZBQZPQF7vNlhbN34\nUFbEXu/ThQK7hzJ5bOwCjX+wq/JsXtBE/mwp3QzhuxEXnObpBJIOzsXDEoYf\nqoMtEDYSVypIGZcmL50zNFzS+LId65ui6Zo0SVigxI+7/79guYfnk1zOp2In\n5l4K\r\n=ADjI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.25": {"name": "embla-carousel", "version": "3.0.25", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "c1cac53a769a38f31dffd876188c615145770ac4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.25.tgz", "fileCount": 42, "integrity": "sha512-9Z1GPPnhm+C8GvRphL9M1URRz7C486t083TQ9s+gk0Xeux6/iK7uX3HYUeaJanHxsVjj2o8m5Bs2P35tFHpGkQ==", "signatures": [{"sig": "MEQCIGNevSG/eP3hPftwLJngQ0TeDYJ3f85dvOXbzCLIHsAfAiAF9DcmilgqVXlPmaHSrtQI/BsW82wilYUI2DT3AvycQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMaJ7CRA9TVsSAnZWagAASmMQAIenmlXNolesHiyUSChz\ngMujwtuNhjS1iAn7kjj+04T7s7p4Vio9cuenVIX6ofiKowbl6gRwyAnWke3Z\nGI5l4KstNEZ1EAq/ihXBuKiiElrHgi+nw2dZggP/+OXuPLLTPR5bBqX/z7+/\nstnhvdk33TlAapoAW+FdRn9Zq9AGRUmRcL/Z1d9RsujP39piOu0/MrqO+cAM\nrFWS8xP5bbIxzqDJm9h6AZ6jTFfMolfsfllr3VNPLVkY4Pu3YqaVuv1tXE2m\nfBsickMZkiW/roSmdjI8PSkhpowumnN4ummEOTqFgBqoOSxCwK99GHRsMnR7\nQhUFyzPdI8tgT5Z9RWsksRYG837Tlsx56LF8BKwgBCCG8XysHt2xGFqYREAk\nGeh6jrIFh/xO7kr1SHWP49ezCNP/qv83e8JZa90aevxfPqhre0Wbtwc1aoH7\nHwcfJi74mAv9Rqc2qMQQSTpwhaNgYgbeui/oJ2ysHv2nTynjnLZYRp1vBbtH\nf7zzd3gL3Rxr4uUjjYdJWoTLESsC+S6one7mFK/EX3K/efbpWk3vma5wl1oh\nBuzLlnnZ5KPAqG5W762aK2IJYSFjGKqIFnwSv5wU38M+RqHnOftYCQ9cG0/9\nixT84TxYwRp4lRtzfer099kk+B0Xm/IvbFqXsQPY3KYM3B94Y+JmI8mGSS1S\nRQAK\r\n=Hi8h\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 12"}}, "3.0.26": {"name": "embla-carousel", "version": "3.0.26", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "02e8ccfc8bc2dc6286cbbd8a71b9f90cfb309dad", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.26.tgz", "fileCount": 42, "integrity": "sha512-4O7aE7muB28D38Lz9tLZaT2VRI0mC7plv5ne6r30lHwHwsqEs4PfKLGIX18YZBG50QKyMPMDST4vBK5+GN5nUQ==", "signatures": [{"sig": "MEYCIQComh2OolR7ErlblItqb5FlvmsYL3/sfP0yensA4fswsAIhALaOZJukZwM4U50p/ORhf5wmy0F6A056ikgcAfvo8tNZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNTpsCRA9TVsSAnZWagAABQMP/2/KF1V+5SQ09R0dE9IG\n15EqWcqB5k5Ue7IwGB/i/kIiPb5WgDJAhUKbqv6xCj4j9rdXzWqyLMaehSLO\nG0K6gEVJvnGAKEAeqy2hzBLFLlFwdutjoedfynS703+soJ6fFdLOjH4sj6AA\nKStR9Kuz5+2f8TVwZ9eCUDPZZkdSP+tz3Ie017nU/sNk1ZT9QC2HAh6oDrZI\nVQU0HtMCm4YiEr60ep9Xe3xyVohqa2zhFpDCmCijGhaVyxvB0gK0mlQjpw3l\nqpjAx7ia92vMxMXHQLglGFLgDeJL6hvAuo/xLKH3uMVj4lV7QQXjRZm/VeER\nOQ7XwYCDDbRirBldhIoJP4Agcc0MChFV/YUcyR709fph30YKLZ2+57+WSEEF\nUEn3VZ7A5US/vK8+Mt8JQG/4oCHdwM5iX0OPBlB3Spyc2P5x+xFrLmx29peo\n6IVyEr4XeGJGaLpUjjZ+kOkTMxIoeyCRVmhViA3iWVXnLUxn9TNmkeOkhsoy\nJCxR4m0VPnmjrImNC8j+A4UJgPxNPNF/jTjTc7N0gkF0yblrQFcb6/K7FH+E\njGhhc+IWMVA4cb8Y8VsZ4GX5qk4pAgqceCfdRIdSKJ7QV7lAZxXD5ZuuypyE\nYMrdHGxbLvOUwoSAi8UT/gOrnZ6BKAx4I4dtUCtDXy7RNSKH5JRSXrkGr61v\nwZun\r\n=zfPL\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.27": {"name": "embla-carousel", "version": "3.0.27", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "7e1a11c753f2d120a72c2e9112a61c5facae71d3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.27.tgz", "fileCount": 42, "integrity": "sha512-7JAas/WSx7YFDW67I/Fnto0N5VMp4FnkLKB1H4FC4p0hPzjX3vQiAsBQjKwg6CIg1SIpoCiRvxlle8pv3F9qOg==", "signatures": [{"sig": "MEUCIF18os70VlIJpplAz6IQZVP9GRvGoiaEeMBpItzeYZxdAiEA5OtvTEOaIKkknhCT2H1CXj0GP4Ruet2EiSHQrfN7mJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655171, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPX2+CRA9TVsSAnZWagAA9dEP/33wTsV3u4SMW1zWO/Qr\nf+HKBvQqSUffM63WrCV4WtWyUEcqwVL2HL6STpwKlzp2TilTnBTCoZS/DuPk\nmBDP64/V3wRHyG7erbLsOT7WUPb6LRKw5SEAgGRCV2fKoPclr51VlrYhUPoo\nEhYLz+Kti9GM7oEqRPEFZ9+vlF1VCou6GPqvzNm3IS8ZoDLal8Xuz9HAKpQR\n70LJGR0yCiv8grLxhEhJONZ9xzdnrNfCjSily0Ahxq53bkln0zDFITLD36W3\n1eakrK5SwNag28lz8YtCWajbvmXEGLc2WlCrlvWK0cajHj+TuldCOwiNxoWd\ng/oN5ng1E3sp+BeTTrcvuoJJhdsn2WZmxj8CzgbCgENE1qEd4L50dN/80F3S\nsX30fYTy1IG20fdvFB3DksNu6ZUfhQrWhIMFCK9GAt6jkOE5mzaH8liqWAIs\n2k0war6UYpfBNnOiIOpfZaC1jaeSjlG3syOOEyv5yPrrdsnYm7fL7CDOhPp+\nDUFwm7gltMY0wkI50vyXmOJFi8SOp32Tpf5nkYGe8ttcnQpc4m6Cz737lOCL\nplAl1xEAUDA/6p47JLT51wZv6cLQSicbHg4xIwBb+qHxY/lfJrX+RXDhvOh4\n5QiZTbLpberrCoLADzQDUkiT/r9StrgU1X0F2II/kY2NRD+wc45Uv8yaB2po\nPIbq\r\n=LJny\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.28": {"name": "embla-carousel", "version": "3.0.28", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "enzyme-adapter-react-16": "^1.15.2", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@types/enzyme-adapter-react-16": "^1.0.6", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "9396113695d52e6fe58597dd214ff09c93562e21", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-3.0.28.tgz", "fileCount": 42, "integrity": "sha512-39LFEYNkmAhPx0lg0bbporWjBic6KbcPzFV3DHd4lT3TGPTh9aCieDvERjfIIQyS4/82dpiZNpqGLUvLBSM8ZQ==", "signatures": [{"sig": "MEUCIQD/PtCWVnM2pehizQhKm1abTW+G4dWYKeGRSg8EpjpnEQIgDWyNaqiH/imJMgbyqOcuqFpHHLPbmPS2iZ/sIbzCZ8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQWWLCRA9TVsSAnZWagAAbMsQAI3tcIUtVV/1b/dkScqU\nqt7nOcxS8F5MpdEo/OCWal+MC6Cen2VA9WsPYwn4EkkMdkf1FYhAyoSXSxpH\nkwR/8bNWnbUKaHE7NtcxH/CKwnw72cFiwKUjkhTmUAKSWam067EGj0XD7nkm\nJXacqMugZZq9DzbtvMpv+2ouvX4lQvXmBH9At/oBNul9vVl9F98N2YBHTotM\nMmjsTgoNSeb4yEIHZt0ayn7xofQvgzf/ffsFAZNghpFM11e78jkE18rTAbj7\nqyJogOnJGpncHZF5BmBosB1G2MMbnwR3WUP1NUb/UMA3mfGl0ewyHy/X8u9u\nLFpChzTOfQoLhVvgWUmX4wRSPebqXeREfRpqDPAKXVCQzB33hDQPgjM+fumO\nhehxKs4blLS+REl2O8D1l5r/sW1wWzSKZTAqCDOD74FZFeZWtueu/xFs2uUV\nrYE5+mgWbxBBc7/3BQNY7PBpW2xPfjMZqyx83fUUPqH+8JxOKkLgvBFIxvIX\n3sH+d2y7En50UYlqQyaz6XAqhwzOUFjk4kNGhPtawbrBYSDc2h4/0KcAGFf7\nEgODrR1sbLkRBTncQF3gUra1/CnNOmhjWKPf72exGU1A1tFw1sMoZd7QZQmF\nL8dKWm+yi162PKXPmpgpCqMOQtq5JlJYFOTG6ggW8kpj4NLDECTZ1zM7kuD3\n7W0j\r\n=P9oj\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0": {"name": "embla-carousel", "version": "4.0.0", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "2cb4f2a7da53d045a8d71b7c7b814f4b0434da95", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.0.tgz", "fileCount": 43, "integrity": "sha512-8ShBlrySt5FYcrymH1vqyJQgAgg65NWSNu+ZIQ8a5GfFkywxqxzPpBT3fEaIdrW4Ru2b3TU2mMX5hq06qlaX8w==", "signatures": [{"sig": "MEYCIQDTS+JCvPXKhh9Os9XWcj54QjNUlXeTdlN8dDl7iALADAIhAI2R1TwJzUStiUK3j9XslH8I+gH+FgGJKbUil1Df2v50", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 656422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRrEhCRA9TVsSAnZWagAAWyUP/Axe0T5RPVPo8W6cPwPj\n0897h836sioi71DRlXuF5wy3PAbiQjBXnSeBCorEkWkUKAX3DjE1NcppSh+j\n5Gt0420VW5t696CuPoqHdJYPHxywG5WcpVDaHuOEaXXi5CZY271DQU4FbQ+j\ne8x/WvYcuNajVwXl5522swjVzQSM6hpR26eksnez4hiBWyf3TL05iYtLckTX\nn3zdQCcqGwwidkzZ0WbrZLIGqoLKoLVykD+7tFkIhwg/QJPy2Byhhl1H4u0c\n0w575G+jEzlRIojw8KQ2+7r1dqFqMNRyRA389d0q9wW1HOXEV0B9YVnKCsmI\nLw0dhrsuICxByBRJipGPjT+wnfRLBN+BETlMoExFNXR8CELsh4ks3B9R70VP\n39VNxyeVb9TpwUtbX0gzHsvYrBklcW75LgRmT8/hEWaiwey/rv7bYfc6pEvN\n/JRpN+GLsdwGYEJDMbfzIueOaTxwhi4qZMhN9xH3EOYrPLYj+SaMImBZvLD9\nVb9YOt8GvXjEjG0KFY4cECbeu7Md8Mv5PWh5p7B5ZpHS7wTd2ov/IrG3qmah\n+4o6anpnncle+DI63jk64Phh6L9RyhfV9yjLplMSyS/MomaWBsuC399qZZHW\neSQdy00fjowlUDNPf0hQsMDEotoGNrRkWD2mwJuJPkT5CFuRdoq9x22hlUf5\nCo3H\r\n=6jbq\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.1": {"name": "embla-carousel", "version": "4.0.1", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "d4abf64cd62cf0f8aa120c21daf4d61951c95052", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.1.tgz", "fileCount": 43, "integrity": "sha512-2T78w4ebbUbsdu1g9rw/Ps+kqkFCfHkLfszC8wXblPi4IH5h2YGajJtHqq5Q6dMVOh3LL9Hp/u3NhLEqfQplYA==", "signatures": [{"sig": "MEYCIQDDvss08TdU1qyCZDaVmKClIscHU2OQDCH59lrDn9fzZQIhAJjSHqZ/m4D7dSCtROPd+lgdKOdUbRFPzedsf1mpJSji", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 656422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRrc9CRA9TVsSAnZWagAAjowQAJyxrzRGEb0bM9hLIJ7J\nujf8/X3vAbMR4AmyuRUzI9b0uQ9T4PZDfi+BN2bx1OWR6UTHc6neO3gW5DGC\ncmLkF2WTjAGaav6vKEL5naDPj4C9h/4ZRsxf9iq6Q2wxmeL62cbIX7GZpHFS\nAe7KwStvGuKT7nS0f4kUUFgZ3oeXV5kZrB88SjGTDVhVYJ70E11kbaSEMRBg\nOCGV3wGImoDuksLRpam/z8JjgCDD3/In40nlhPrZEJppRTPzOawCZCKUf8wM\nrsc6eKKo2ZjdEc8ipkcw9HX0p+78YHVa20kIduVN8wSrCcw1gVwmpg6Op+w3\n/ymNfqk2bQC8jFiDydcZC3uzGaAW21sfHVlF5MFJMxkTf4Kv81ZBeEzv0LAT\nfRM9JO1umDWVpCOEJPy1h5lDAYWpHC/qa/E4RsS2dNs/4ixGJykgvF0AFdFD\nqw5detY+5Z74sBvd/3RHhT5JkZbmWxQQg5TNVtfTBkdcmz+Ui7cQyA1D+vBj\n9SwSSle/+0bD1bdQeDPfJuSN5pj7bNIGcCHYo9XvZ+GnE5J74q1a/nKUjvO1\nZz7YNvoicbZjFsI9hyzfA+i/WkUcysdbO3l8HPBGO8Je6OV/4s55DKXCj/Wu\nqoxfZEW3R/Ia4LQp+XcnGQ/cwIPVNGv4qggnSmHoDuhazCtuJlYaV+Y+YTR0\nCo/7\r\n=bhvf\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.2": {"name": "embla-carousel", "version": "4.0.2", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "96d292f168c12df37ebeeecd9780255cc4b58539", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.2.tgz", "fileCount": 43, "integrity": "sha512-FfaCfst5U/UwXS4fNveEnctk/0cu18EIq0y2+7jlCguoBuzObMLPQllUhrW7njDN6/ezFVSNp2qT9ETxD4Tdxw==", "signatures": [{"sig": "MEYCIQD/ky73JiogJ7RA/z0c3KprIGW4vAqtHr60B2Z02HXGOwIhAI/rJQanofuZvuKkLPc0hhKRevI94qwJhdQQ4Bcdm9Ov", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 651793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWNjeCRA9TVsSAnZWagAAWswP/jwpDa0c/w+iAVxO6jK9\nH+Se2/dKXyRQc/yHHpbGAXDaUCyV6Z8Jo9trPKfn4ulWup1H/+76FEZXJdH8\n1yLiuGu7WnTYQj528EXGiboNfZWIteHEUxDjpeEl5KYuiu1x9X0AYOtxekC/\n9/+vDHKPJaZBPrd+KyYiAupmb2SYZCKGhDRABzwIeNr7VWnwI1v+a3P9c/j0\nWat+IVMog0/Qfs3ktTt6VJn+e1BejqkwACCdQgxtxJM2+7o1u77xSWP8/7eU\nRgEODkkO3JX9jEu046yW+eI3XG5uO3vIPYd90hDx5dxpojVNwXNos4Wyw1NR\njYY7fXQyeA7Yp1lL3LU/TAWbtWQWv9eUqA61HaKgbIO7ayjg78lzuC9EpVCm\nOQO3athqY1mOtxLfoyNTUMvwpEbwKvPny69z3IfdNT2qEHPbbvUXrk23S4ZC\nWRdyUxsLbwf+Zu+PnZqAxaMu6P28owu6NNIwBGsiSaxmxja5PDG93BJgIvWt\nUPzEPytWn4xM1NIDFbqHCUUhYB9cVfmv0fDlyib516fC/cLi9xic7rwEJAy1\nZIo4M9LIeiAy8Z21DNz0vkKtdPrTthiab0fHVCa9ktwWUfJsEdBOvwcddk/O\nz0hFw+8ZomAkRuBvV8aSLPPKwXr8BV1rM4xdqcu77j1bKJwpTHQRQi63Nt7T\n2xbI\r\n=R2M7\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.3": {"name": "embla-carousel", "version": "4.0.3", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "7fff8837528be45103f3bf27321d5467a570c13f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.3.tgz", "fileCount": 44, "integrity": "sha512-1NBqDVQQcA19lVlrPCdyyNhcQRqg9mS6Y3c3ba10JUtw4DCYHAfbCFydir/rhcOmuXuJ2ApsGrkPEEYN50UaQg==", "signatures": [{"sig": "MEUCIQC8TjGVdATiQUj/HW+H0K/tDxbIBdvVX/DXpulp1o8ZPgIgMXRFB1nVlIypv1900r3uzo0ffEwp8aWRDNSuYBCw2WU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 661163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXl2tCRA9TVsSAnZWagAAAq0P/2p5grpUZsRO0erZ7vyi\nudSlpr66XTlXatfqSJPo0hdPXwWuv9yaEeEs0wuEfm23OVqN4EJM1QjcjPn6\nOklKf+jqDDpEFW47C0inqWSW+hwrVY/TMYvbuT8UpVSANsS7sbXieO0HL+Bm\nS2+S9v1F01bKkrSIZY3udHFujQxE9ZkUl8zanYsvLeOHa9jie7ghisLUvG17\nrZiw+7a44o2K77IsVVAIosLKc5GUIOw23kSS/o1MjZdz51EuBAvYFy1jtjSG\nM/QZaTZA+wu6v/BlEiM4Xq75LlwwUWVFEyh10gur+pgFdCozIzixjIubSGMT\n7dcTjT+9EEzL4wS/+EwpVY096T+dhFmzykdfHVBss+A3+5zcCyspKZMif7Ux\nSISYD0bul9G/xAp9iSIayGLUwjY2xO250sMbu3grPjixzTGhKrLZ2s3GSwvF\n+D0zbdKkDQL5rNHZeTCD7bGSfCBYFUksoIFmrRzYTlTw8tc1YRJdxiyhDn69\nnNeaj25JmIGaD5HsnCgCAT9vDI3E9z28AWtXTQfNqwKoU8USoQQ7+rf4f1zz\nJX3cG0QS+h5p1ShE6t7ULkPcKkZnpLOmqeIOfnq5zCLiRO7PvBpRb/MVA47H\nDKgzrofcVQxvwN7xpdBIut8PUmMtfbEIVxCgFonr0FHvhprn6ZoYS0sl/ttM\nMcn2\r\n=HCFL\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.4": {"name": "embla-carousel", "version": "4.0.4", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "0f4a52ba929580bc303a4a7df1cb1340acf79add", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.4.tgz", "fileCount": 44, "integrity": "sha512-XZw96wJErV1lydf342AOQ8vKh1dkKRobBYp5rGXXf4WcW5QbYCtzJIlqoId5prQ8/XrjLmV5zHJm6p+QTGb/ag==", "signatures": [{"sig": "MEUCIGCj5bhITCvQLZnMEFMsiqP53idSzE3x+prkMsjt7iAiAiEAxF/Zvy7KsgCw5XLjMIGgCPtxvXF2PXhYO9eEwjrcVUM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZyfFCRA9TVsSAnZWagAAUcoQAJKuY+46OhI1DENiNyei\n8N3zreUhcGfDrTKHpGHGdVkAhu95V2+czCgaks9EB5OEwEMC1W9ZLEJorBW5\ndtDJ2p9Sj0Rpcs9MugzlB0e9Jq0Gdo6msEEtR385Xg64frXUGVdvd9UIrRaX\niWys2jN8VF45/m5NL25weYZbqwX6RJ1Q/D9UXMAfo+TCYj47X/MsjDn4dGI/\nCl5O+1M4BAQOYazqYLUDQMmFEhpOuIdf/rMc967HEg+hJ3M/7YyQSg5Te9aA\n9mM2P1NQzOP7R2SIwbpZQzNxVoHrrN2SW/XATuXQebcRSz3+D1Y0EenDhsQ5\nLYsNVwLdjtvP/NByFdRn0bjArydTYgkYj/cmO63JHtxWDWS0bZJo3TDys3Ql\n2b0pvfRTr8q5pqklnh36RSh7afSGLtDdyR0Mp24lhyGJmH53282eIPqrX6jH\nX1SwAfUGzaypgRTFw8hWMhtU6AieYAAAbL8IVqdOzA+rG/ePA0mPYxX6hRxd\n1KxcHUWIFT5ye/5IZGBhVz/wN3YEDNlw2zb8qF5JR/fLDZK8UwRrZjdcWcMZ\nuRvanTCZXsr/gmYAi5fuRHtFKxXSzmRn6DV2bbm3xeM/bk2Cahnjhq4LRhvD\nEchoUiafZ+nd9HnIPmly4d1qr2ZoTN+LBGpI6wq38vw3D+EqOrpy5ejdDB1D\nDqt/\r\n=G8Sp\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.5": {"name": "embla-carousel", "version": "4.0.5", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "270070ce0f8fa258d26615128c2d3e8b073fe26f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.5.tgz", "fileCount": 44, "integrity": "sha512-+iUYbtjbGJZGMNJGrbumBVcU0fH2+Wmdvzvfbd+TabE9rGJACf+3jrFZ1c0PBEXBNuaqB9xEknxoM0xTSzv+/Q==", "signatures": [{"sig": "MEUCIASkT76QdMphCnJNLAWOdYGOtv5jDSn6CjfK10ByDPKjAiEAoQMKCCUul5zdJm2nrUVqtaSH+avIylH7+ZEyj+2ZFq8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 655475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfdEBvCRA9TVsSAnZWagAA0GwQAILFi1Y8Nff40VSW6g+9\nFtbBW5JSNMwdIXnihoZ0dSxIzQWFOAr2ypwRBB5QEdNRDmSlZbrYO60RtRTy\nZRwuuZct3N3eIXHEefROH8x3Ry6B72t/cM58USvfeued0WXJUI+noPd9BM7K\noRaIz307c4VglsiIHdKtqhry5p2G/x7ZkOu52MgvjXFLipXyuKVYYqTteDZ0\nyt+oYcvie8jv/yvaUujt+TrhHYVmdTWYLDhzsAzPV+wncskzW3k7Xl+xX13r\nzlTslLP9uF26zMB74gZXWQS6u5jBPoUCQj9n9pgCRyOKc4StA04hGkFGg62+\nu6e0b3lYt1kpn9x9LVhAVKUDjvQTEgnWiMAvDjCUZBIpmU5ZwfKa4B9Icr3b\nC2CZSaYHC6ohui3jrFMvKRwwvcO/nOqP7GvWLRuI1vuxo9uigIalzIxPPPjt\nA1aX0OHAneocDkmZp9QQDh6Eb4mcT0/qKJuSy0YX5Ia1dMRHfomPjZlWA9Sp\nChC5U9Rn+x96XeBtblWBjQ4cGFxLwuU3VeB5NDj1XI5g0K/swTV/jIUgksCm\n/viLojFDZhD7zol4AjMzejy6NoS2YtgMlYw1KJ8Xil0+lMk7K++i+SMm59Uf\np6drZ4ik/OO2skVyZRPZEy917ijj4IXYvhKKWCMxegJS9N9eQD0u193FWvkd\nPOBX\r\n=ViQQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.6": {"name": "embla-carousel", "version": "4.0.6", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "52dae0c0d2d47fa7cc055c9c0dbdd710d9f15d47", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.0.6.tgz", "fileCount": 44, "integrity": "sha512-AJpyxQHom4gRwLFAcjf880OxbeshNd+K1neALdMJsyljUXadTPdTZaRdko6CwD+Z6XT/MzWZuF74bJh8sXM/Yw==", "signatures": [{"sig": "MEYCIQCaDKy7NHXJF+Juyka0IXHdThSleOs/mHRfmhCm0NyPsAIhAOfWCkdyenItxCABGBOLO4qhi2o4UJNXrGlyA7jOwfKg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 653878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjdozCRA9TVsSAnZWagAA7i8QAI9VTNto8+bKF7VkJUDD\namywodx/P9bNefrfZCT3s6cjsKjaMXiQXfxeQ1ELCzTTwdKWPp2EHuF68MZx\nRBU5WhNufOBDx8QUR7HGX2Hu8lAGPNBdlFE7SUX12/lu4ALa3D9iybFeErZm\nTW0q1QlOrgHq5+up5MSj+eB2tldLMHrOD/IbFo9q0SqFD/o1ELUIZ8++dn7L\nr/2G2XDJW/nkjia9jsTS3TgQpU0xWe7NvIiJ+z6oqze85pwssL6EDDnRNFRl\nWq5tYy+jo74Mx0jc1bPiM+MuLErX8zIKWzF5+d6zc3eaq7APcTEHDk/GYzGJ\n2jtdUR8Iev840j66PqI9/QFJ7hocZ9vntB8qHMeLQW1Oyb9HJbjHq4b1Jf3t\n/j7BtqUYrrLOSqf4yUGdQ2NkxMhaKK5KIce0NJqwPANLFPjU6acHAh3+DDur\nMlY9RR2x7WvJb1FGrSRHkGIkfjY+NuVDQiQReaiRliZeKbjQYvwcTvC+/NQO\n7Zh/CBSqNVLHSdGmvrCpjyqAW40M0jdzbomHB/My1hAeuAufj0buCdoHdo9/\ntYgOFY2w2T6DAYdaeUMmq9rNvZWOeXl1V2l4Et4M+gcTihb2n0ijxeNI38iH\nVamoFhQ+ZEQgEEcZMEtyNEJRNkD/0mx2g3p0buJ1B6h9F/+O9sKiuGxU/Ouc\narXD\r\n=3Zy0\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.0": {"name": "embla-carousel", "version": "4.1.0", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "5838b01d474d8321b820362f557aa5b0c49e7fad", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.0.tgz", "fileCount": 44, "integrity": "sha512-DO251gLGuOE5WQP4Rs5kkJ6MqcpUS1AfZo921JCfWPtTn2Hp6n38bcZSu+dlhsfNck5x/VoYASkqPkDaeCgLug==", "signatures": [{"sig": "MEYCIQDiv/2F5rZdsuJCL7CHsiW7LnvconXU9c10MUrhGBNpbQIhAK1swvvi6kU9XQKIBFnjyAfLkFz6hCa4BeHc+GAearKc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfq9vpCRA9TVsSAnZWagAA0/MP/jRHVT9c1oQhKj6VE411\nKyba5Htz2G579CwkmxGb487AzPYDF2qN8b2XsAS/6asE312AZMdqouk54PUm\nlNwfvdlz3+A3mUdCZ1WyC9HbEsmMGOq4wJm4fSDvkD1ylziRF6pCfscAEz7V\nNCkcM8JIrr0w9x3wPsvZfSUUBTavWI6GdKWo1Y0fOU+qk1qw8gETUPGRkmRv\n0CbCSGtf4TQhXVz5M9WDbCqbdJYPbh4TwhZmy7EMvogV5EfhjwEaecYKTjVq\nBhnduguQ92z8S49EQ9ZjJNq2JyDwKZdK46XKDZJrCgBEuTBPs1EGOBOPSACJ\nrSNbGtbC622xNiE7/yxIyrIL4jnTuOtEgmi1JG5FzjeSjWfV1PA4tu3Y7qZ+\nJr04L4TtIb7/XaLrpSTf3dgzsNNjpAGSDlxB1u/W6yuXFzgPYrq4pqJ9vf53\nOL3UBCmo3WMFZR37mJAWtyFWqT18mkEW4RdUonVfQpUwi42WNkZeoOCUz1a8\na5bD+j7QTqmsrkRkI56AZIKQnF+Cb8ZJhG87pqNoRlgGpuHzSqjoZdp1z10q\nUUmieNKwDS/FMUF/aSOa39M041NLHgse+sXuEVn+7uV2mXtyuqCPHbTgRRND\nvoJ2YmknVb21UpH52wSh/iFJ5tWbMXslkOvsFmoHVNRSKIEBWDrQEkpPN6D/\nZCM0\r\n=UVqW\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.1": {"name": "embla-carousel", "version": "4.1.1", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "e69f8ebcc2d2f35d3389e587d2bbf06d62e90c30", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.1.tgz", "fileCount": 44, "integrity": "sha512-SFejGhzBgN6+BrDGOMDwlj1xYTdB92aNhT4nZJjjkbu8MmJUQ9Qrc9KeMucWF2OyIgNjBrca/yxgvcX4NQakRg==", "signatures": [{"sig": "MEUCIGPA3PZnsBRl4fr2UHDBfG68EQ7gDQXjp7aFKOo0RBplAiEA5B7+ymis3qZA078rPOOscNFoAvejsLs0w9am/PlHH70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 654445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvkaaCRA9TVsSAnZWagAAoygQAJICN5eq/XOzogIXmkn/\ntq000CoOZ1DJuPD0RnQnS/j1H4urDekfJTDW0aXpZ3knbmRAXKTpcYyc6EzL\nJby1Ro3BTx7su5Jo1jZEL5FKqWV7D26AuHbjLABzR8j8RoBmH3TovJNxjzaa\nSaZSKIptTgYkdyHohVuHFHl1jWJVrVo0lKDgw4QaI5oQgEy/Y4ZTiPzuQw5V\nI6pQezlOBvjQ1kJ3PQDZfv1BefE0YyVFW8fXR8Mumf3Hg+eUgJLy6Qb77alL\nA3XDgsQ3MM2S+Nb2WfI7Ytfxfh28qelo7zlTVY02CZefBOKLeWX6thI8loLA\n8SPTCcRT+e9K3px7euxLovjBa6TOIDTHuf/XptZoNiHiSi1WhtcnDWkj3nwN\nWikqnoZn+drWF7gAfTDXq1g8E/LcWySz9L+xmY60ZKaPk57z9anc2N/fLZ6l\nkIq4e5Zz2/hQAchX2wGzmvEnk2aGOAbViAOJTGsQvvIgGEZagn5vhjOR5C4D\nf+fIRCzW4QhgUGAaBgR6MZr+qc9A3ipHrQsOV+jOjK+9lbBAPkkCyewgp1oE\n6JoYSdrxXtm8sN0Cg8Ovm2W+bjosiNMCQ3fsGxc8LEK8HD5c7mZ7nr6oQh/s\niYrwZu3x7dM9KXEXSCriVf3Fb3uGqcZxX6iQYGq5Zp9XGEtvfFZJIZWUPCHk\nAh1m\r\n=IL4X\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.2": {"name": "embla-carousel", "version": "4.1.2", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "4b75d23faf5fc2b25635b8ef6de80661746939eb", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.2.tgz", "fileCount": 44, "integrity": "sha512-Gg/k++bJMexaR64sLWTFonRZSYsD57DwJqd0sXcisqzrGYp1TqlDSsM5aM6o9bwUgpqc+t5dBtztZ0kzX1eQ+A==", "signatures": [{"sig": "MEUCIHqsYynWjri7rDFg9udaQNhd+fzEPGKSxxOAVqnG+ruEAiEAtoZVD/V8UN9diXsglI9ouo9mu4E2rqvdi5biQAkkK/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 650306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziOzCRA9TVsSAnZWagAAtu0QAIm5t2+ifzjpAqUdgQXQ\njs3v1oMvpa3qMbncAnhXfBzc2yF3jJ7yQu74vyooA7PKDNC75U+D2JInZ39N\nWE4igU6KL7tW551I07H3vnKWC20PzOHCiWxMCgj6kJspCmm9LMoWMayLXi22\nQbo2FJnTlf1rqcYsSyTgltDGDIZevY+21Hgubi4WRlUteYKxurF/DvSu+SLy\nkn6NSJqFYiUm/7CvX81ILAJ2BodAx7FbcBSptSFq51AVkvmmpCHWTtOO2Oyn\nojqJyoPTOOmiSjxVFz3MIbK5Q2zzYx1c8XvKPmmWyAAdZ8oPAw7QBBt/+av7\nYlsdd05jQxCg/b9e+PhVrbikA2ldgvicy3jyiSuXBDuclmhar45IGbPN6JcI\nu7R6hc92E4WQ2gQLreE+S8PYTGYEwJrz2xQYFthYbnJrpFevUx53zIHOUH/r\nC23g5rDWzZwEmcGubCuD6MAFvo9Ve7SeHLYvDxc0D/HavJ6kfp2fsGTQ0P6e\nEAwBz49MkPD4edIVPNuAe2TKs1IT1zeV6jyk7wDTOtzceq6v0ZZ7wAH1+9yD\nkup68c7TGMVOWzKoYwfD6ovIslxGD6o4mjKIFTDUZMVPaFcyPAXiyMNwq2Jg\nD0DYx/jnf9upmXORg5zF3VovFh7q7TzN6WNRbaJgQFC3AcF6GbhnDZq/z2RW\n+vnj\r\n=ai+0\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.3": {"name": "embla-carousel", "version": "4.1.3", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "526be0266b838b0c7bed809fa7b52c93e8648cf3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.3.tgz", "fileCount": 44, "integrity": "sha512-9mX8h8SVRi9szOB6RIcSZuAPl1NgWa1+2KRRXFDWUy0ER+6BOgYvVHmunNkjLM/yg9VElAxL5PNmc+YccKeFtQ==", "signatures": [{"sig": "MEYCIQCnAc1p7gPt+TU2KQ+96LwAxa7RqGe4zaJfkbzQ7cnvPwIhAL4NJywsZQRPf23tbCJ75+HMC6V8BFg822XKAtvJjrHT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9gZSCRA9TVsSAnZWagAAtEwP/0xAosy8GzwGkpc1VbQ9\n0IZW7HJEv/u+sDiWx4zzX8TLxwjaVSgEx7S6LaLrCRit5U26dHtsvfTy4Emf\n4zSaXJ4XVPK1RPpdotNylslnh98e/mymoQ/rs8zKnJ1Bozjqrdd8JpGLVdEX\nSSCCq434fi9HzXiNNDdq5laT+j+VFyLHhGhNSVaFLjj1okLXuz5hb33PiRQq\nVsDfH/GApBfPdH1pMHdHp3abL3RZw1o1fgN9Ecxx9y/dGA7pnOEwiwVPWQdY\nvL3sS8Ic2eSjFq58Z00aFJNkQnNW8n3wf265I3aF+QgSmYnAgnOu8YAGUw5q\nb+tIHtLvh4SoElDsYfJxOLiFZc2pdEUfrkranxTFqtIi85vtSaa4nz4w3gYm\nNvDkZp1SFpnduzGETVEhQu59m3OBV+HHPc+aGB35nVNBwlSEyRRX6Q8XlHiB\n12Ulxgn7B0AzthqYmx+Fjh7Xnn5iHm8YRDwZ0jRHtN2gQiZIRtnXdJmIDOoE\n21zkf5jBnqXa8BQe9yTRzJhZi9S2lVw9xCHNcV4Atjyq9LhPynMSqAik2eat\n7qFCVwIoP3veRCLMyLywKoAjyX38s/KiFDM+74eVwl800TWLWgwUQEdqJqfH\njXI4eifX9zMEBkbg8t0Z7Q3XChSGjW/agfSZauumlw7rJz0ZIh2FP00iGdNZ\n9n0Q\r\n=HDNa\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.4": {"name": "embla-carousel", "version": "4.1.4", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "14ad14db1c65b1213f8f828474368e58d5718534", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.4.tgz", "fileCount": 44, "integrity": "sha512-V9pJLwrpc7olgWA0JXzc7Jk12eIw7T7wp7+0pa3BUrFZbKj1OjzqPQ51JwPUzrpwnWjDmg6haoCLDKtbJLD0fA==", "signatures": [{"sig": "MEQCIFg+0IjWJ8sGWwdh1DXuzV4nEy9iThZyqMEuTAOf16FSAiBIn5tgICyfKcAOEoqtPSfH2dW+V9kdFXJR6hE+zlpUEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPNNxCRA9TVsSAnZWagAAstYP+gO1V3XPqO5VxjZzsMm/\nbtRJRxbp2aZkyzDcaODGJeFJn2YWf4nuEIo863w6JBpXYMkYrew1rJu9NTg7\n4fM0EkjzlrjlptR3l05ilDfx04B1sBIElJjfVaqvMCSlUdSpEmdglVBx5DWG\nIYboMcGr8pfzj+/PXYgrESXcilAWPFuoSls3w31T0vORwHG5QVAsXNQcWrdv\najM2eWc086UIVZREzrrWdk2GBrIs+EtPCCCEh1Dn356KJR03WfD2UG+WSPwe\n6M70LCY40bblI3SiMNGJB/QKfmBYdCVoDWBVHvRRwYMDiPhEa27CuYnD8/yW\n5okLK57nyBXu96nFCqhAYzgrpzWQQlL8TcfJt3OK0qX7EsDtUAK7ZavoYb3s\nb5sGQU6NDaT9ZcekQkGQCn+4qSKh6QiAtr9J+RmPXKhBGVIVO5QFsRR9C4zE\njRLQamoFkl93DrUdX75Q0fQWOoqSn82S6P6EURklNCWUgMDJlxF8ypqDSK0f\n6RTKimmTj9TjAeV4ZnQSTQNQ6u6Q9/Fod1WQxGK0w+Qez8hm2VYZkmdAMPwj\n63AdHkW7L8qPcsOiMtjRfmUbafhDhue2OydCqCtKFn/atd8UkcX1WnhS7FTG\nh6cWrKoKT388lR9H0ZqG5pHHw0QkVwPFqgPrNJ1Ljl/aySR/bJYxnUELOrr7\nC5DT\r\n=Kme8\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.1.5": {"name": "embla-carousel", "version": "4.1.5", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "b194b75b7fe984c7d53273eebf431f8b9db9f713", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.1.5.tgz", "fileCount": 44, "integrity": "sha512-iohN/kibGHUqO+4JbfFKm+9CEA6DEBwIK1RaxDhspoX3Ypm27HQDWbJ4Cbj9FEcQhSlbJoN5v7AUxtUYd9BvbQ==", "signatures": [{"sig": "MEQCIAbRfiZDksXi98HBF5+apVb9fzpbNXudKfYcUUZkCCjRAiBxoycZvg5K/CZfbmfl+IOfUqdHKcEaZ4kOYhofTDot7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 649430, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPjFOCRA9TVsSAnZWagAAbFoP/ioGJ8b5rncu6hM66rGc\ndoX3txV/u7gVwPssYTGxPCSNjKoYh22+ZNdqb7gE4HpAD4YRZMUriQBx3UHo\nwHTBl0K62Gxfg++F1bvu6EyYC79QVXpuHv2NO2pt0XH87RDyfDTIlGD3ECSh\ndNawdG+/gHNZ7IOxBXcGoKbmLEZO3SZhdhPfSU7K0mwcSVQ9kWO2vuG5VmAr\nxDq7DKMa+OEDpHhGassAKLmpdhMhQJrqrlkPuhMtmU3x1yd7gPQzS/NJF9Q2\nWaLHfn62QSSFsLxDgfbkX9gcLmRnfqsr3tJqDrycyhwPEknLUaGu1wMPn2vB\n9D2iMlseGjJxotlilFJz7GvR9Xxdo6pUtn1tmSEkox2i3UpguTmOGSc9Vvko\n4IKFoTvyu9Ki43bDLwjEJD8edLpNnbMT/6iAD9EpjjYOkk7P1oA9UeYVgBEp\nHSveX1SpRxS37nD9X68oNXpf7FDgMEVaSxQ9svnD8t9t1c6/0edwAQVLBW6R\nOz6lHLC7rdSjBejOlzG13UvKjfi5bpv1UUf5E88USbI3c0mEen8wcv94RwqH\nGmP16O6N71+OhZqrTUPNFzrtiVIlg7G7gYDyYeLIG4AtlJ+lPVpVwyljSXqd\nQED1U2Ezzt3bKcgNLEkgWSEU+AJ3yU5u5Sjli9XHgkhMsuTcA3yd1cyNp6Vg\ncuNf\r\n=az3p\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.0": {"name": "embla-carousel", "version": "4.2.0", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "b78319583126855ea398a45eddbf8388b49fab83", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.2.0.tgz", "fileCount": 45, "integrity": "sha512-WMYkRcRRwTVI7E41NRcnT/bUOKaGeYokoT86n71ONH1DeJqICI8+zItr7O2p8+QMlNtm/ThP8saLYh8yT497xA==", "signatures": [{"sig": "MEQCICbgoF/iRXDRNrFveWz12/eSFo94N0KVPRJ2ehUs6h42AiAa9JaXMjkKltZiOGg/eBYplHNEtkfs0DkPJloekNWysA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 641129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSlewCRA9TVsSAnZWagAAij4P/23f6xi126c6XcCGiG0g\npjIOFnxI4OZON2EfjzCVAh2dvDmeA6xr0TuPORspE/XSw5nxSMrZnz1Ot90J\ncm/pR0dxgLjUT2Go1hSWpq2BKG3AAqU8mKG+KanJXket6q/kUuDjOFYefUPi\ncIIf/gdBPoENGd1+BSv+QnwkAyEJyPif6QB+gCBcvWla8YWtd1COlLl+VgTy\nbLfY319zdW9EFTW+23rHQev9xtVfTLTfqrYFssimb3xRT4Qc90tw5jIxtskP\nwcF5jCyWHKNkrHumxk+iOpVTry+2ce33B+v/+zLqQRWFGaF4HsEw2169ZfLe\ncaUxlhBAbZMlheiB5vzIgIw18rgW6TJkpfhc0r/Dm4sF01GlKMp3pg87HvQ8\nrJv10sRrtKKsXzqDJdCnnV5nx8CLWqrCYPWz/L8qd/IvWneftTOBGkTO9Sod\nPHYO+5wg3FcVwqkTbqp3sxZeKMzjAkkymPmkwq/ARN4cEohICdUPBOR91vf8\ngNz5RlUm4eStY89Yd15EwkxNsamomalWp7AZVRcpoIse+1oQvZuR8XoKYXSx\niafdih0Jv0N3g8lKDkRBxrIoyxuV4EkYh/Qv2Su3tkh6TvZUVKoTAyKlulMv\n7fI1aEZ+Rafg4jIsSEfaL0MlUWSGUkUeE97R5spiaN6EU8sK+7nSeqIJqzZh\niT6o\r\n=wwNy\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.2.1": {"name": "embla-carousel", "version": "4.2.1", "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "eslint": "^7.3.1", "rollup": "^2.18.0", "ts-jest": "^23.10.5", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.9.38", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "@rollup/plugin-babel": "^5.0.3", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@babel/preset-typescript": "^7.10.1", "@typescript-eslint/parser": "^3.3.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.3.0", "@babel/plugin-transform-object-assign": "^7.8.3"}, "dist": {"shasum": "183db4f95f058375165ab9b2bcbe94ed810c55a0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.2.1.tgz", "fileCount": 45, "integrity": "sha512-m9llJaWWvC2bumfsGqYkwFhsto2A9pwRGNoF4uQ8PDmtb2Q1i5WspA+zFXqmsDrF9i1TmYSDoZvrbr3YYpJ56w==", "signatures": [{"sig": "MEYCIQDKg99I7kVfAV+j6uaLzWYc1BAGDP+q8F8NnuaeRltuWwIhAKazVBma2C79EoDEfJNADL0QEisOPYh5vFu8fkTiIFih", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 638154, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUe1UCRA9TVsSAnZWagAAsMMP/2/+XUIfBDD8MR7RHehu\n/0gW/WVGuCvFW3a7G95svbyjfVho5XZqBDIcyENOhiFBjkWs0TSisdGoED1v\nS2BysPdaKxzExwqq2T187kIZ7apv6X1cZSrHCzRnOOJtRDGoO+5Bl2O+/YcH\nRNfX7RHzQfPDvwbAh5GKkF6sehb4moinjUDfw6tHFMy8Ky2HJgH7Ix/tLRpg\nlkjnFXPFnG0rcYmWMNLIQbTmlSUu1q4BSKtS8yxJbWfdM3QlsAU6gocIVQXU\nAzb+nAKTTzFJ4Jq02kFm5RM8oAkTHuknnSqBnBvEJctKyKxgvJ8P5c5raili\nHDi85ng8cS6ZVG891CLfj4f1cCKognBOBZCD969Au9sPjM1BKkp0vy7kbg4v\nUbZRvkyt1WEfh1UHJ67dAagOj88mLTxFEWaCp2CkS+DAXSnIMIf9IT9HACos\nAeAinfhZGF6NzJhqnHBSHRuR1LGffAzx0vnxqTbwhvzA1KZ/aCSucuNKHqTl\nhv/2Ggo7CwL5z6odxMh6X6EZOa5OTuirzmyDrQqLugR4GgjrfGxZLQXXjcZs\nj2CyBqSEb0BOMFYUZGFQSw9MSS0lv1B4rh7p3BjpXaOdOv6Skl/lx0nJ6yQu\nFlXmaTjMEYuCxwa8dmc2AD8l0HC3lT8oL5FdEM32qbp639AEjZF0bEMT2D7Y\nxJQY\r\n=xTUb\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.0": {"name": "embla-carousel", "version": "4.3.0", "devDependencies": {"jest": "^24.9.0", "react": "^16.14.0", "eslint": "^7.22.0", "rollup": "^2.42.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "typescript": "^3.9.9", "@babel/core": "^7.13.10", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.14.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.13.10", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.18.0", "rollup-plugin-typescript2": "^0.27.3", "@rollup/plugin-node-resolve": "^8.4.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "24ae9dd99db328318ed6203cf27e5b5dd07381d6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.3.0.tgz", "fileCount": 46, "integrity": "sha512-BLqjl3b9wyRywE9nfFgdfxr6N/DS5Cr6NKkA7oIU5s1XdxAXYTgYZ/gc9cN1KzbPtYZKkQOBpeLOOqZ8TK0IPA==", "signatures": [{"sig": "MEQCIA6BZQp0XoiL+3NuCDJb5n2jNbxHs5gKicI+9ypgQgaPAiAuuijrSxsJMysND8wuF4NmARzupuH/tvHUYtdPkwLsAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 644993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV7rFCRA9TVsSAnZWagAAI00P/34GrbTiggsN6l6c3FO7\nOqA21OEcpcrQThsVwgmqooVev9tLNqC+pLb6wSc9OZqYK4yRslHRClfscJ43\ncl4jaen04kWBZtbfuRIiOdBu/yfZV1Jb/IrFljJh3yURZc0v6/1izPTvGGKT\nS+2rRIxKP0s30BJ2iEvwlY5l4Xvo+lA5rMnkKbU9gZTwKnToLb8CkH+9odzC\nzlnrCwPljYe4LcoNaAMITesMUDGv1F20zYGuIpABKKcgGLP1j0+Y+AhuSAyr\nJeCnSsfT8awKbl5f3c8TouVTWVHxVMHlir0kGie4uExwdbyXrc5d3485Nt6/\nOVDr49zXbD60yC++SEo4DxCtuYAW5JwOtvUnbu2ueaYkGHPYZLnAfbDpp2NQ\nad4hAGo0D1U+aX8zDOz85ssJ5mgLVCOyRYHH2fqRdrY8IEu2JI2JfegLlx42\nyq3UAkXHWGbL1Y1gsXuaMxH5T5eVD1vNzrj6m85hNzuB9lMpuIfvxUIdlLtF\nHM+Xx/RHqmHaOwQbEPKMeVaNYbkg2j4DQod9Oz1OzSJG2EDCJg8UlWX5lqh5\nUcfQaPasMGZ+7jh7UVBDWvxq3KEFMn6NGzLCtAG5IOF5Unw6W54DYGFEpjer\nCvoaYy5eAZfiA+G06Nj39fw5eMrLnqE1gzrxzvvJRDxyO7mSGytgyDzWsTIR\n88Nj\r\n=qusw\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.1": {"name": "embla-carousel", "version": "4.3.1", "devDependencies": {"jest": "^24.9.0", "react": "^16.14.0", "eslint": "^7.22.0", "rollup": "^2.42.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "typescript": "^3.9.9", "@babel/core": "^7.13.10", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.14.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.13.10", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.18.0", "rollup-plugin-typescript2": "^0.27.3", "@rollup/plugin-node-resolve": "^8.4.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "13c67c9fc149ffe00bfaee13a4d4565a5dbe96c1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.3.1.tgz", "fileCount": 46, "integrity": "sha512-6N+2gFwzHnLU3dV4aVqAfeDK+D8+9+bf1pQ8ceeLl6A048gAZ8lPINphbTpCuIhI4yrjU1CGEbQesWMzR+nQOQ==", "signatures": [{"sig": "MEUCIBM/OhhDojMOUQfTR7wjR+77qOcWg0hVymL7/bVBy/iIAiEA3w2/kQRMc3Z+MT5BLDjMFPf1JEJIoxJel+iLbbB4xIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 645249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZhtoCRA9TVsSAnZWagAASYcQAJjxLCvN4rLxgmlqffmS\nYxxNgZo+eF0dV4wK1Zo43kDu1HBsPkCrNVK7nMvug7yIZJa2kzziq3ui2Q+l\nzLO+BglfR17MZezIDHdFS2BK2+HrreBcrXQ8Hd93WqMiHFTbwWrtu7AgEMcp\n3oMZ931pzLQCoO/8X940wpx4boaJ3zt1ltzk1Zyahe1YQn1HrsEEFl4fmIwI\nSDVTtcK1vyzISxUwjaRczAz7PIr0xbKuDv3LVoohPhszw2OydPcNFu8IjtKG\niXOEZe5lV4feCjX8zP9WDFHqdrc1iCjsFmWJoTXGWLgNq+iyqRoOiOtIpygf\nJQfitUjPO/5YTmvkAbiIOiqlDpHT8UA4XhU2QdTWpSq5rsddK2xaAPZWuLmL\nbEG0XqiIUR0s03+edDiAvftHfO5Haz6kW8tDLhBupw1fdg0VmWD/zX+my+by\n26WHH6eM1P2Ta9pLy+kcnBXcAH/WA18ZUu1H/rBzU5l8TY/2zKSlcMXMktFN\npB/Gh8Ut/U4Vsl6vP4rsHmNmFOqQK4z9IwNGUfJ0/pb7dxlR70wZ0tq58JM8\n0FLoF4e3YfxZ+8jRTX2BZSsK3O4yno2+EUgyoo3+rU9oQD5SJVVwaPI4qFnq\nq3ywHuLYM5Ad9lICTDQ6Hcz8BYJiZuM9YrcgGEDK/kyI9E9PL3PQx0/OvaFy\n2BiN\r\n=708v\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.3.2": {"name": "embla-carousel", "version": "4.3.2", "devDependencies": {"jest": "^24.9.0", "react": "^16.14.0", "eslint": "^7.22.0", "rollup": "^2.42.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "typescript": "^3.9.9", "@babel/core": "^7.13.10", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.14.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.13.10", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.18.0", "rollup-plugin-typescript2": "^0.27.3", "@rollup/plugin-node-resolve": "^8.4.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "ac558034fcadd932ec115f44c0e175162b26fe0e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.3.2.tgz", "fileCount": 46, "integrity": "sha512-lVIajaZNaeIq3DP+qwrja8FMmXSjCfEduzmlhKgXIZplNdGNmF/XOy5f/CSN90h8TlRdwNTan2J8JAWAPb3PLA==", "signatures": [{"sig": "MEUCIQDRLQjvvINRAK11nEqmvTIoDDfBXh/InHn0cS84nHnG9wIgDZ9iVFJKaqyzeu7aOFTSFe581loLc/07RXOShha/tW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 641536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgau/KCRA9TVsSAnZWagAA6kUP/RzdkpGsLWShffRKPDql\nKgM1NmsGWl6wZvsfGFcu+OzN3oq14tFu27GjLxDLda67PN8Hbr/jfZ7QjSDo\nEF2zOtfQyFk0Op9qlosLy+2YbqHcUb4D5HCU7hFCt5egl6KSe9VMS/afoTe7\ntR/QqVTCSlc88ZZzu+KtM1jvfTEPbBzpQRtzfnDRQp3U9qHkJxGJpOJ2yl9I\noolm4ovvqav4pKdzZ0yjNlSTDT+lgjBVcpo/aQKiHEe5fXAEQ+iAQH5rR7hC\ns4ss2kh9hMq2wZx0YUe6//SWWrThsefIIUKR0uyNVTOx622SD8XRADYJz2Eq\nmwAP/rud120Ec9ChEcOZcp4kbnpGlGjK5nmmloIUUtoHyuztkhfQ7M/gf5rq\nFm6sE36qJS+5vHQYx8oOaMa6zGUtBmm237VLGj9PAWWpSkK4g5xecHhoguFq\nKAzdChmdRn7MHFh6fzy6razPvg0RLdiZgYso1yexERwBnloQ0MVy/q9bH05J\nZeyqzg1vsic08CJaXy/BJGBo4q0LxyTxm/yrZ636Gn6fjjfpO3kvld2EOSbc\nFwzfGSGmi0CEZHZcAh0Xs82BF0//DruNymJd+wkgMGSLQxdKMCxEhCRffUUQ\ny8UeSSExkygaztIbo/NbzCkmTeNX2eidYIv2fySUY1fJVf05ZtTLB+rNNR0R\nJc5u\r\n=F+C7\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.0": {"name": "embla-carousel", "version": "4.4.0", "devDependencies": {"jest": "^24.9.0", "react": "^16.14.0", "eslint": "^7.22.0", "rollup": "^2.42.0", "ts-jest": "^26.5.4", "prettier": "^2.2.1", "typescript": "^3.9.9", "@babel/core": "^7.13.10", "@types/jest": "^24.9.1", "npm-run-all": "^4.1.5", "@types/react": "^16.14.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.13.10", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.18.0", "rollup-plugin-typescript2": "^0.27.3", "@rollup/plugin-node-resolve": "^8.4.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "238afafdb7d86e978637aa0a1d50bc742b994253", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.4.0.tgz", "fileCount": 46, "integrity": "sha512-yyqopAhUcMeappOrcaX0J7A8Rp5LNanVN7IvDQFkuOe1R4ThNzBLvJtfiQjmk1HdP/P+45EEuUqBMH79wprnwA==", "signatures": [{"sig": "MEUCIQC2mQlPMcn2XYGSMX+pYhSnluz6BXOyEw3u2jI7yqwrZAIgRi4VtvQbUgpvKF82540Etx7z34y3GRkyBFEuOqocZPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 637776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc0F1CRA9TVsSAnZWagAAr6MP/1M8hf5QEdMgZwtPGWEd\nkPdCWDyJHx9zI4fZKRlo32FguWHlT/YTbuPZoqsEyCkP9SdT0EHRiwrvg4Ow\nf0zHEZ09w1sKjGeaeT8an6nljsypZb1a+liPwLGESbyaD9Rmzp8jDfNMFAg0\n/VO0aZc7SC43jALqN39HmZpMMRWgyIvvfybtK4zBOkEB+xdWu7ertkm1c0xq\n7Wqq71nq7so5yR/PpjTcvdTjk2V14OHZV/huHR2c5qockWevBk/Y09hNGirV\npRWv05H0RSNGHJyt2iGuQPQUPib//fcUG0HfXI9DS/WLjNQseR6fXFiu96RZ\nYXO/ToHR9JmK7mtsgF78f5zwNft5o/y9m66kp8sd25UZOOfEs2mlu7reA65N\nppccNA0Us4qUDcwpI1vLcashM/DoEsU8tVKYprQPf9ogkFDxsG1Dg5vOYZm0\n9dIO/FVX5gK2TPj7/GjCVr865IUsvEsUBZ4CKxZwXpZjODiOGw6OFcfz/ywx\nZwzIOacw1DFC+O5+KaCkxkrTL2pq7r5cPDmR7dRTKDvs2vBQNtSJyeTZ680T\n0pkNkPbs/b5EZN9qiDhOk98EgOuo7ZYOEX6C8sAJsdTxsIKyz9hldlTUwDsS\nwBQ3BCVxxO5ZqHuqsxPrav7HvYvqnh2QRfAh6CbPPK5eApea4Vy67/Vmtc2x\ngWaJ\r\n=28LQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.4.1": {"name": "embla-carousel", "version": "4.4.1", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "npm-run-all": "^4.1.5", "@types/react": "^17.0.3", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "be52e5a9c54ab5995bffe46e2633515bde3d1210", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.4.1.tgz", "fileCount": 46, "integrity": "sha512-o3lQBIzDNKFU+walF8vJvCffuAzAcJUhvR6Req3imsKTK24DHLkjAZnJWhLOOSnwX6agGgANf6P5r/jDucBm1A==", "signatures": [{"sig": "MEYCIQCYmJyTOnQN7W5glRTFKeNq9AQwIVef5CUrGs0SRZMVhgIhAK3zGsrLBuXpLx9OQnbJIkEVYR44s5mC1nuO/hExOZlq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 635929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf/3ACRA9TVsSAnZWagAAXCsP/3TuWDhgU8v20acbNRyF\neLrjFd1nAsQHe11LFIHj+WguxFKgmpOEmp8O0SxloYk/vrsFFLItElfwftef\nUjnEenhePjbc+Z0RuKkjOt6v+y6CVc833mtbMnK1DndC6PTcRLknk6Z08Ame\nKo/Tlfzp1wwgxIUkQ7XPmqqCDnpvrbKO0+DblRPt1B00z7cSC3u1dVYeYN/+\nHzib1KcUqYml6VVNZ7gZnZxdB5+xyrEWsBa/eNZBr6MqMmv7cSV1gekI1mNs\nFeBnIhbod8EyE3MXL5IjM7bQkxqckY6ofVWXdJrA9Cj2JapQ975cbwzrSVJC\nqC2/gkgmCC6ZZWZn9P52MwTPC67vmbA4rPjkxKWijZSl4kAXrZ660bcI8vPP\njeWENSaUT6mwYMTLhwxJ1FF4VRA+Ox93tg81t83v7TTZwzMMV7oEJkgI9uOa\nOrDSclqeqgw/MxCcDi6X23AbSms6b5NVDPdkmFBR4p5/OWNwg9S54SslvtSG\nfzFuVFcax0P6sH9aqxzk8WZgBcq1lcHi6vGH0vrswVDIoms9vMIcOMZZwJ4W\neqxtgPqXPnrL5XTAD7LFUVKeRvkhf4uSqEn0kfPPGdQJMnv6t1O5qORs43kC\niTGCxWey2wOGQNLtu1rYiXkKRUtbRAZNj9XYE6U0QwTYeDFys+X3HdFHq4KV\nuhSA\r\n=Fvan\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.0": {"name": "embla-carousel", "version": "4.5.0", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "npm-run-all": "^4.1.5", "@types/react": "^17.0.3", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "ac8a4cbf34f97a8ad5f7ae9caf58400fe92f1d7f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.5.0.tgz", "fileCount": 46, "integrity": "sha512-Y154DYESgzau+eIUH1hu4FNYRQmSFdzIr0dVE6Dhqv3IILCTLVcMmDosz5D0GpAd1jJDI1r0WbOBGBonA6wirw==", "signatures": [{"sig": "MEQCIFa48FKwN3uYcfLwRLvzT3kdeCvqUxo+95Yo5xkNvpQ3AiAiD5UPLsX868te6YZ8D7uBW794w4khsjx/rbNqxlDrFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 642790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggXThCRA9TVsSAnZWagAAO5AP/1UECF0O9A32iYIQiglA\ndnat7XO2lN83vE5BSqI1/i8iIl+Mg5Nyybb3tCSjbIObp1zhpTxADZ/dCYBf\nTDVs/DDzs3Ko3uNsMpcRdxvrrOygDsuhvSaUuDN6OnlVLNu1hCF056wHNDLL\n5r7Z18AjyWiJDP8idxdx62sLjKszgUH7hNTxlq2en6u3e2ERUzoQ/wBBwGoO\ncSzYNf8iGCHWsxiJGUIU/7R7MKjLPoF1+Ud0Be1R7CnnwodQ+9bSE7oeg3k9\noASM7N2YnxB5xkQG5iT69qHsuow1ncePzcqJPDJYaB1VyhFDGY4i2UFdPuAV\nGL4NqA2kMA8emfUVgA498FFcrdIFDrGMJbL/aF1Xz+4zVVMnhqyRdpmGVpag\nTFbUTBrsOdY4PjeJLY8wxH42BOTyc0gjPowCULhyi6HsLoF1j9gmONabWBjC\nZuq67y3HYnX/dOoCnPvSZZqTJdaqKv9fIXKjJSxUyTF8VvO6vT0sh8b+7fWX\nKePudT1ULkofMcggNgfMkeZolvk9Bib69Hz/OIxi+mUPpLny6KuqP39Cj/5X\nMX7nhZ2CKYHBrUNo9OMPXIcy7ve+wEpQW5tAU6hMxFx7MJIBJ0vVI0goOc+x\naLDgbI2b876N4KTeVJKgZiy0BPlClSmf54Nth+vzL288okfTVmVRfS4/VH0F\nFXta\r\n=lX2D\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.1": {"name": "embla-carousel", "version": "4.5.1", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "npm-run-all": "^4.1.5", "@types/react": "^17.0.3", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "db4b44c8f7e96ab4af1ee4afaa1bb22c06b51065", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.5.1.tgz", "fileCount": 46, "integrity": "sha512-E55z44S/gduwUNq8APVYAvzFUrIuzgBckx0Dl/j6QFdwWSkRbPkqmEI9WSMvtpAeGEz5xFBdnYeb5eXWsmCGoA==", "signatures": [{"sig": "MEQCIFwfFRO9/1yWtRUHP+tsyUlbU1hdplLlSZsGI83C7fYYAiB0GRjbo2jg4pt6Zf+BjKwBbXWZtfxRASFofHETlFaQEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 642790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgibbtCRA9TVsSAnZWagAAxIEP/jOzvvIvqMeOK1RvvFGa\nIxh/U4s1plrVNlFJVsZ4/mRnFQnfCf2Cc+TU+4bwCOFGcbYEiQCz7II3Bdrb\nVJj+c63JmhKaJP4bVsTGo28wQuiluBVSPKvyhq8/EvmlOnQXA/mDM8VK+sZ4\nhdMyctu0p0EHiW+bdFAYZuAY82BXrucNVmk6iEPMwoIbViJjAmjM6ZjajmWb\nT8wFRdiW5DSh1c1EpVXJld17fmDBS8Kv5begjlSweU3TTZd+oYLW5fJjPI0h\n1ilfjkXR48MbMF4Mt4oimhlGjSTi+QqMuUWczYlRfMzszEcREwumdZk14uqT\nHT09/zdTT8gLJ+IQBgoDN2O+3qwcTFiiq1RfF335VZaNIGFaZsyC9sPKxYko\n/5bE7zbNiWeWRe0xSOeNKZHmHxlhytxn6svuUmpmbMujeCD7hxsXuYifz5DI\nVHZrliJkcNK5xfZ/EGMMV2ACJbWdcZKwupMbQQfCnr49xoT50KV8eIuJqIeh\nLwvblMD5GEushVVkIcl6fE5d8BC7NEfhuOLrsf5NgeFojbP0ckAU9O8FT1Ri\nsg1tHV7y/9BgVbbMmbjMBGjdtpq2ycu5G95uCZkJ+MuWTO+sLxIPw7U2hcmU\n/tyJNcg8nNjkLyYyhTGs+YMnYwyta0iSbRehhEw3GAXkikrqxnihcmlaR9+z\nyoAe\r\n=QJUb\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.2": {"name": "embla-carousel", "version": "4.5.2", "devDependencies": {"jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "npm-run-all": "^4.1.5", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "4ebe7e8b047de7459409f6427348c6af93dc9572", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.5.2.tgz", "fileCount": 48, "integrity": "sha512-amqIhxe7LLeVJruIt/my5hmT/JrUUiwiVtIq8z0/pgeNaXk3yGiNlbHfq/CU0qaCpM4SGXJuC2Nr+lc4Tu3E9g==", "signatures": [{"sig": "MEUCIQCQtR7FfRFGTpxnU+7IMXH5NI+QI7iqeOaELm2exBL0awIgSE8tc85Vjax+HEo8ll5ANv+2I2bP/EsSH4ikkPkmOFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 643301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgqjYyCRA9TVsSAnZWagAAkCcQAIn/f3nSG1y/GJ7oizY2\nyv+aRm2Qp/cWrPfd9u7n4uwMp/2Woj4a7qwF4dVZj2NtB8wcghF3OT3ntmH9\nR0kf3UFLiKzxpF5X/giyhKc0Q29FFke2/4N6JCvYTZ29eaDCZZTHEkwchlXq\nqCgAxImsdb+9W4xyrTb0/gVPi7cqz8XDE1vPxdjQGcECDFp3CTUCwxRez6FM\nKp+OF61DlSz9niSqEZJH7GitJ1TVrzEfleIIzM/W2TJu1R7EsWwXMMwWEcRO\nKBeQT7/g+iXmw2qc5C2ldxOAUCxyTlURwUDFn4PjjlFMRGabXu0+1KlfX9al\nP0EhZeGzuVHBOB56SUsUlJHSlaVoctcU0Yn66hlLpkgwv56hbQ+RFvQ82/mn\nGUs1QXrN4ZrMLadCTtgH7WYs/7vBfFOyaS7iwX6d5D9O+y+pOhfucR+a3VOg\nnkIldlsjN4QQdkLbzOA+C9m0MlYQblHuU9vi6NWXZlowOyYEO+o7NfHhk9ML\nANojb21x5Yabrjdi1ujsiTdLeM7AMPrK7WJDE9qDvWQrRHiMJwAJk57l/l00\nAQhjLAHbC8RF972JgDqpgP3urdJdtmimcGTtnXZX34kfM+cltp+qx2LFJ9/v\nyxJ8WCO4EM7vvj5VbKaskqZqLCHz7rFXJpxVKikZ831T8JJe62yQGTduwwuw\n0a94\r\n=/Pqg\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.5.3": {"name": "embla-carousel", "version": "4.5.3", "devDependencies": {"shx": "^0.3.3", "jest": "^26.6.3", "react": "^17.0.2", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "npm-run-all": "^4.1.5", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "3e1b471fd9765b910c5dd9acddf129b07626782d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-4.5.3.tgz", "fileCount": 51, "integrity": "sha512-S7fPpoV9AsPRII8ytsDQavW4JbU3xWBAO+i158eVahVZYVb04vC+913QfO3FGzsVK/4sb58NJ42LBDh143+YhA==", "signatures": [{"sig": "MEUCIHVM9HXz9eLDHNSyObnOFbz8j0Ny7Hf1vMwLqk6NLcQ8AiEAj0aEVLcuqSnX1z51UojH1GqefIkgopjh6q8/RCrswk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 644084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrBLoCRA9TVsSAnZWagAAJ4oP/0N9WyRPof7LUuZ1T1Ch\n7O5lVi4muqj4tWhPshtetyUppG+YFdvcmDqTHmS/t7pK+dhed9+SJn9xNC5X\nKjJtAGl7jJ22VFd/Et4qWaGRn17VezE0JrkHC7OYoKKm4/05h0OHT9tklnra\nFuQ5JMEFpniG0ELyWEOxzFY3KbgLsv2cIiRR/LzTA9AJ807OJ5J0BV5LDQ57\nbFc+V/Vz76VxHIJ2CpaY3bA5M6FOnEvkvU1nCBBY0gItz18wkeT8jRnJX5Vh\njstWnu6OI9e3loctLs4h6K+YNcah2HBSOcRARTa1uGAtvsUUhfCYxJvemJgK\nbVagRrO9kYMVxm/AvtUVKFvONwqhXH2M0GOXzWSRlKCq3GYvI+CB6AdArKQv\nqdbjuEpoxf4rsw+xFLyErtJn1sJnPON920TOR1CKDTBpufc5Pm5PhzKItpcL\n3C1gaVsDXY58/SKNcIUf3qiQ80gVm5UqARFovBbfVDyk5VJKyoUp/XeFd4gW\ntLpdVI4gwBK1fhpvnrnep5fcO3U1r2MV8VMlZRLEizCs8LuAFWpnNgOBjY8V\n1uVYeu5vTw6E8DTujXr5WY3YRDvEIY8hV3kDW9Uqb3gjN6arNw6uyscEaIyN\n2WYispZDHTAahGgJRVTiCT4E1RRR985z7zo+jfGq9aYUVYbEj0H7nm5s+Xr9\naqSY\r\n=8/64\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0": {"name": "embla-carousel", "version": "5.0.0", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "d01b8d5cf731b2c8c8dd78fd7e66e48fc215dbf7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-5.0.0.tgz", "fileCount": 39, "integrity": "sha512-HIWqSMJnrzRRUiOgcqKPcNoM8kap1jy1ApuJWzB1SfnaVfJvrRjqgF9mUvjFEunKnGfh+H1sTzMLS1F2pKRgkA==", "signatures": [{"sig": "MEUCICsdgW7WZz3iRBn6lHI1K49VDIsDHXilqUoXtFv/e3LcAiEApHeAG97wqosLSMQoG8Vb34KcQF8q8orSVlvWjPAVldg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335729}}, "5.0.1": {"name": "embla-carousel", "version": "5.0.1", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "dist": {"shasum": "6c25d889e7287a032b6b31c458481bb648f098d8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-5.0.1.tgz", "fileCount": 39, "integrity": "sha512-pFvUI9mI/pxU92+4VDkPx0yP4Bs3VqJuRX/aw6ESYJdRBtzLx+6X2kXMu9aXK+SwO2zYsD2WURb1SeBaAv6zQA==", "signatures": [{"sig": "MEYCIQCIuvGLeWy6f4AoFzWctW9ucbSKHv2vpaEiuOanK5xfEgIhALYqiZRLobkriXSIUGTUT/5nxZ/PtOnjpVIYU8HV8hJn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335729}}, "6.0.0": {"name": "embla-carousel", "version": "6.0.0", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "433cf050babe5e689af61da756ba6b4b698e8390", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.0.0.tgz", "fileCount": 38, "integrity": "sha512-8Tudvu/y4mcblYzIyswfFo23FnavaJirODhf7moHw4DNWPV3jjlBH+pKvxXjgvUXJmi4A5xipN/azRmWmmvUYQ==", "signatures": [{"sig": "MEUCIFZmho0ktUi8WXbEU+nC1sC5sBF9tgzZwi0zn7ZbCO0bAiEAundmr3/tiy5DTxApuXV/cOyKaJKPFs+IrARwkREJiLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317738}}, "6.0.1": {"name": "embla-carousel", "version": "6.0.1", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "1974d64ad18a93963ba4c3532ae24fc18ec6562d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.0.1.tgz", "fileCount": 38, "integrity": "sha512-jz+7AqMOt+v6Ryi8jWVG4rK0rkqr5VunuNd/boGQb2z6lebtJSVR2VmKit3uQV9DkIPp92I9ZydiTYRquk9QUQ==", "signatures": [{"sig": "MEQCIFx3rNVWnqiHMtXjD2Noj9TEpF5Cm0BG4vc5tYoUpw69AiANCWX+heIw5CnA4c+Bxej2HQVGUle2KZUmrhbrBk3zkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317771}}, "6.0.2": {"name": "embla-carousel", "version": "6.0.2", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "de6503eaf7538eaac5a71d25cce32a073e51529a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.0.2.tgz", "fileCount": 38, "integrity": "sha512-6unBWN0u6ryxwHxTafQs2z9VnvOT3J7FGzLlmISFWw+kPEi8k8rlReRcqvotg6BTYRjsPmpH909xhPe/7DsUYw==", "signatures": [{"sig": "MEUCIDzfi67+w4AKyvlnsS05bh5LZg3avb4sxFp15SFZm1PuAiEAsbSjVL2D6/COZdaSzzCDYXnmjd09/HdhdsDQHeqizZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 317771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm6RrCRA9TVsSAnZWagAATFIP/i0kOiVRjRrfmEVfGo3U\nfZ7F+NTovbgJqTZP2en33KtR/f2gRFw7k1oJDiuDv4Gq049XqKX0cLguUxVW\n5TmxvHcFbqr6q9ABwcE101Gl5Rd6N8gfw63mmaOkvKHz5MAHM7zAzXCUGmsD\nrB+N/42m+AnzsCBMTy7Wpi88yTWvygbCINToQuMuRtux+tixnSHI2a5LB4ww\nOamAdJN1N9fMsNVc/tc17tBQugOv6o0aIMbMC7tDihoXC2FXCdBfDYoJS1s0\nIn/3NE+2Pv8lcRx6MkdBHR/trNXr4yqJ695XHQCXA8iDzAe2Z+ubh2SuF+Hb\nNnc2C3ZA6EZVfHYOv1hJrrlFdvSG/XRRfqjyg/4SoUcfouC5Shk/jjIwUkxm\ngTaQxqGPDuvjkq1qQuCahmsBjiN5zGDLuI4r+BLMppsIlSM2X/MCZzmVyeza\nH1gMs8UXYK51t6FIhJhcKw9t7OLb5JXk4+XzU6ToiiOh7iNrglch4TjLrkZb\njx+0Z9tfqy1xsfTi/lYgCeje+jaTN6YG0DF1TgJXohWPehUEiDUChtb9XnDw\nGQoLIXefFewTrqhnnfhbDozOZPdKnoGzavrT3v6ETmiaUN7C0pDB3gVSlqsq\nUX9ZtS9PgBfgPGJw0m2yS9uhMcXu1kkh4WVIhbF5yNfO/V5mwPQ1mI6TRziw\no/Uh\r\n=B67G\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.0": {"name": "embla-carousel", "version": "6.1.0", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "5552b011a76a050206be0f9f124eb76dbdfeaeb6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.1.0.tgz", "fileCount": 38, "integrity": "sha512-Q1AxMS3wYR8g+lSdwwSspRLTCwkg2iN9Ew4lSraboj2XXh97/esY56JOe/u2R4l+tFhJ0/fOUqCYq4dz6NJS9g==", "signatures": [{"sig": "MEUCIBgrPxwZmxfGHtYGCokuP1Njao8YxdsjYNfA77iIqobNAiEAwc95iNmvMyHEr1Y5gi96IOvLj+f9KN5XUxagB0Xa9y0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh40ZoCRA9TVsSAnZWagAAttYP/36npWgJJMANYQup2X8z\n/SE64eYZ7aCZgtOpY31LqUOLdflBosWZ/poMeF7BzwsQYpfPL1KNV8NRc+TK\n+F3qctSjjk68sxrDlRIKNbkB2qlrXJbHKkTR+LwMnddGlhnH7bz1GxHUXAel\nHaHcWDXU2Qf/FXEDxOOQ8kQuT9R2zyVDnd5JKSbwAeIGUnof3kyl3Wbhlf+s\nCs+hya/DlvnjuYb0l6JNTqywb9cO2BQ7+ypAf+c6MVAaj3KqKKo8jh4DQrFw\n+WRgM6YgWR3P3M7Wrtj0XrHVnqUP0mfBzwPHGgVe3Ow02elWdLsVdiK/6zgz\n9n9ps801z4/uFQlFRFbsJtau1895OSFkSyuVp3zsUCpqfFvN6nfj1wUo4YVk\nGYBbAbVvy3fSr9RzclAHRaj6C93U5+j1GTohEA7xgKM2gjHS21N/SxTuGMUT\ndfd5uTOjzbOa2EdEhp5NeTuQHo32LQuVJNZu7h+JTOtrXhku+lsvsF5rrkLi\nwhp8jhW0xP5W7NbOYPj4xb4RXqqPupIDtkRWQOaRETabFuS53lgkMP9LSxHG\nb2uKUKzqUKZ6dCnm315s67jakWiX7tHro05BCUVkt79bZzJWr8tCwE6Q0dro\n09cdWR+7qWva5foMUKxvNUtjTs808sF5LVOVs86q4hDOsshKTo1hV4uR8qKH\nr6Iu\r\n=0zFg\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.1": {"name": "embla-carousel", "version": "6.1.1", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "ad4e24d57650a078097ffa5818e2282199f239cd", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.1.1.tgz", "fileCount": 38, "integrity": "sha512-viRoyGYrUALxX/2g6P4bU+Q3lQPeeUt2StcMq3Ketn6lmNOVc7q7cKwiBVqXV+aEIqe/EgCLQZNajewrRKR+Pg==", "signatures": [{"sig": "MEUCIQCmlZV8b0OsT85htiBYlONsR+Ah1s0Rx6bZjWiUkGdZhAIgBeK1dX3/eiSzObOjx7o0OpvYxh4lvkPobXee6xFNLuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6IpFCRA9TVsSAnZWagAAZqEP/Rbj/nQdf2WRjxvciuDO\nhPRBzWFluwjRAB18OhKpBd3Ja0hhG0S1IySiCiMctK1bgZ8fGDG5YB9XxYnK\nx+CaFlSZ/W7SnZWFJbKL3X3kVJfavZYA6dplqQYDBTZbZOb3bNNlWpVIdkIL\nsxI3hxqjG2D54GDZzdfDMRnPMLR0J+VYMT/nAmet+XKvuQZrhnVvNr7gBQRE\nVtCMK7kPUlDT0flR5L2LyalKfdWIxU0QQGbA9XRqdBWH98GYCA1X3D1yvllW\nga/6oguFbM8EZvI7hsGWSc+SeMpfeXGNJmCDLUOLzkb6C0/vK+egxZ6hbUFv\n4RBBknQwRc2K6kWfGFrPONjsmvqa9PpZNYc8zuAE5J4RopwWv8UfU4tCjjuB\nSMZ9efJcX09ChzQtmQ/ye9+4f6FK2jS5ANiFhhRbF+z7kCPlk4qq/1DZasGE\nwzi2hyKkTCBogQpuia/F9EmFfqNuQIMzT2NfZ2xIYhQ6GDHw9Vsa8XhMxmJs\nRnOj0jdEGAmVyT9TNRtU+TxXRKIZXSDeHO50Tty5jtPfx4uTnwS5B5YfacoB\nYBVmn+Gl0yBRkpxrPJa4k5fcDTXpRWaS20cKlGBd9qVNdexsrk2kZRtwbhA+\njTkhYZkt80O8C3MrJGoU2uEX+RU9nOHEZ0v4+vueWmDGsg7czgiuDgtRX6yn\njCpZ\r\n=yZu2\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.2.0": {"name": "embla-carousel", "version": "6.2.0", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "dist": {"shasum": "c16b18abe50e05ccd03d0b8d0b738f6a87aea1e0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-6.2.0.tgz", "fileCount": 38, "integrity": "sha512-dSNsiQ7nmSQJZgbYfZCLdzrnznHwpaAcdJFcMRKgm/pjH1doOgxmfsvlMy4VfO4J11hLz8jm/W8WxSSDqfuu4w==", "signatures": [{"sig": "MEUCIDccIRGvqggChXTWmkVoZpz5yfn7RThq/jOuuo2OImkeAiEAgrBG4RztyrnewUfDo1WxpjPrC1GFmHcoTGCcwryqpCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAEbLCRA9TVsSAnZWagAA0bQQAJpJXK515/OyOCS4e+bT\n/iseUN3SIuqrEx7zH+NMGBkItbZFgsWQfu/wFayo6uWD26gq6gvjFPVjbRHP\n57ITnCGpOCwUze4m2QsofNVhfJQLm3tVkBVPe3/kXwhOcHKCXixHrlscja5f\n79KcGRe/D9UA++2qfQ7JKZrXSqtv5tiudsRlZBTNEhYxNzj5O5PcFWGgRWMC\nD+hgwl344uxG9zAhvfKjJSixC/ajjZT58/ePQWLHDoart0tSqZBrVUR+uSvs\nh2hZ5o7zfMZ54BzvCsgvHDzNaWgkxTXhd7oFCWPSuQ5Yrp09GIQe3i7QBns3\nPxd7BjkqRbmgEPAxEOIDxrbvscM3ItlLwcN8jW0cNhHCfxbz+Pk9qeZhPrCb\nS5AdncOfp3LSKzurAirZ2xSdq1+sH8VBbU11kwU7WKWoXJWPmHptitrE6jMg\n2TxLhLNX/34rnZJWkjsPFSbDBYTm7aangM4OkhiOwnUicwqQlT/iHSRu758M\n/9F7jroVRVGREUJGrTu0KQcSxOyj80UnEFPQY7R3iElAXHpjRqnfOmoewBrF\nA23EKhS0YqYS+0dcxIclrtRCk1kThT/W7rRb+zOn2kwyFkmpGb2ZVgSG9sgD\nvVo1d1dVzorJHk8CDkAHn3p/4WOL2q8tKHnfU5/uMlgw1v1VlUPfTVApjg1i\nUHKG\r\n=HuCk\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc01": {"name": "embla-carousel", "version": "7.0.0-rc01", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "embla-carousel-autoplay": "7.0.0-rc01", "@typescript-eslint/parser": "^5.10.2", "embla-carousel-auto-height": "7.0.0-rc01", "embla-carousel-class-names": "7.0.0-rc01", "embla-carousel-wheel-gestures": "2.2.0", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "7c9adfd7302b85c2de9354b7ef6343f16a696eb7", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.0-rc01.tgz", "fileCount": 41, "integrity": "sha512-IBTSKcPw7u9K0zoLvsnWYsijKsI0msFqzNp6ASIthTXiMZNJNGQt8k0ax7KqUVinDZeNM07WPWqYsjrvUM/Epw==", "signatures": [{"sig": "MEQCIFVPAzC+IQdDKA5B6iRzM+nCcObtphlvL8vhZZrXYbK3AiBsmAW3cFqC+b7npsegkbdQolcG21S1KKaRTF+AFiMzpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivVklACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogRQ//dqv2GY2+iYUKUbXL4iXVapXTifJ1yMUosdII6us+4hSai5S8\r\naP7QzaJgicqH4AgpihPMXnbo4tDh/d3heIP+i2ApDQZJxIPxgmWzGFleXgLz\r\n/S6FdD77oftL2Zjfx69fiD30lL3XnX+xonfnKoAJvX/ZdGFgDTE+6JGmJqzX\r\nAiaqjxezY+pZSvNXAaeAE+wXkJ8nmQDo/zvJa8SJxvkilh8bdRrSbUVe4Igg\r\n4OCfC3YN9HPiNumyuPEMrHhAE4K2prjgh5dTCQ4EIiYFy8OOFyYcnlSZqp0G\r\nGzDloNCSOwl8qSKeG4zz1wVQyeQbONq0qMiH3McJZ4wOePceYFixza0nnXSN\r\na4WsRsiF/14B67hjRAvrbHCnpBmeANy92hJ4xSoxQuC5lSJyvT2bSq7SQJED\r\neyi3g8xwz66rCmrvdm/fUWMb4F6swPbeWMbeo7DTPxbXeib9Jtk+vNnAXNe7\r\nBzsY4PLQHErZ0zLx28a21LnpTgsfUZ5P/IPtY044D4n/HP9+R4KcEvBdi60i\r\nWhMXi/iBF0svOomrVpKlTnmDsehVBADwcVWAwpO715rBaPqDTXWKvKdpHytA\r\n3rV3w1RA44cXM5Z8ErBr18o6uVTro3ri2PKCSpxQ4Ryyw0KJ23JIPiAiK382\r\nchts7zaGlsx0jZrFdofHkGkhTDg4g+1yNLE=\r\n=qRtC\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc03": {"name": "embla-carousel", "version": "7.0.0-rc03", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "embla-carousel-autoplay": "7.0.0-rc03", "@typescript-eslint/parser": "^5.10.2", "embla-carousel-auto-height": "7.0.0-rc03", "embla-carousel-class-names": "7.0.0-rc03", "embla-carousel-wheel-gestures": "2.2.0", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "7af5d4af2292228c005f91979f7d34ba0d94f709", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.0-rc03.tgz", "fileCount": 41, "integrity": "sha512-2BQ58/E7hJBmnjxBTOrjcrWcXGN1qQBgwCf2uMIer6/r5OlyFXSqLPd1lq3c/RJA9V5qp3qnfm8mU4hSJS8UWA==", "signatures": [{"sig": "MEYCIQD0fxXhNVgYXbKRUlTQKM90k2sLTmXUHseKGEPUlD1JIgIhAL/dnnT+YnPcPkuDcSTlugAMBN4o3GL2Qts6DXT4+mnC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixF+0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2rRAAjZuM9kQhtc9ZdCMY6YUYLsHeJJZb+REQWdXbGBRcfn7SFxx5\r\nYtwSfEs1G6DH62eoY1fGdGRVQD3o/y/7o2AuzEXL0GWFfeb0F8rKAZHIUAOM\r\ny1LLaIa5wE3k8NEn7d7TUGi9o2N5NJRYChxE9P4OAAJYTsoh3MhivbVngebD\r\n2b3krCKoZhxnWKELU8ngZm51F4h2m4vwWGZvtOW/P8b5MfC7YASGu/djzimg\r\nnKJ+625yyKPAbqEBWDJ4pSUeJOVV5IgoCRT4Ym+DnDVSQDLSCD5OPZeCV1K5\r\n636ZQtVgGsIWk50reAK1M9WTCq0jLytN0b9iFEjZe4ZX+Dxm1byINjR2pSV8\r\nvH86o6NHoOCurH8wZysDnV/rzEH7dfutyAHUfWStexSOlKLqyr7+WjmkPEHF\r\nI6uHs+w+iqpjFVSflZ0Ghd/lfLGsE1vDvWw8KJBWq+W3L1/5+Pplln2t5UUF\r\nMvKRLmRl4a2OCEKAPwCxed3/uclw/RJTI9Y6ilg2OcnWpuTURcIQcocM72M5\r\nvzQwZrFqgrgr9so66ZziwzkispUVxZLrdZMnBfF3QVT3v5QjtgQgIXwz0NyV\r\nBEdS03bW/b14EQabXGdqlV2NCdOj/1mfolUkwI1TB+zQxaR/Du5NdfWdbtPa\r\nfTu3pH7LPwxoUB5Qn4tgqgN1BL/E/gN1oWA=\r\n=/MKB\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc04": {"name": "embla-carousel", "version": "7.0.0-rc04", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "bd9c15da7740660b46232fba720b0c38043b667f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.0-rc04.tgz", "fileCount": 41, "integrity": "sha512-vhzwCEdEqwS5c6jlfPHy/X1uUkwf9AvMma8KWNbmTgB3NUNN9hoqiyTdrpraMtPU5MzlxpZipln74ZwDK4v63g==", "signatures": [{"sig": "MEYCIQDXC3kJgv5iFRZutdoB/MmVvx/mWGTAvv+dadEbA1GOkQIhAJZ0OAWDPTZOXfEvs6XuXqzy3oOOMeN7/zENi4z3ZeHu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix0Y+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKFA//YgwT3Ywdy861tkaCH5bi++s0ZndCSwF4bJ64BWgLn/oIxpWz\r\nfNp0rkU0Mlr34JQNwfWsxe7mll8sh/TJGCchLu2/dAvsC+jQjfZNM7WQ9Z9s\r\n6Yair+QNZwjHX939Z+DDnEUign6yhQyC0HbrZ5HDxVRtkT5H8ssFG7viGkL/\r\nh9kmArq04tP/i86Dj4X/1YHIELGRC0hUZEcTQAY5BK2717h6w7jxEkRib4Ox\r\nhU0O/iv4UvYFp+fBtxHiFfVXV6HLmMjhZvOpdWl7K9rk3IlpKvoP1KnKMU2G\r\nUeFpMVp+TtTS3R/2s2id7CSR0mCqwCsylBdoETRO/DUq0x0Vu5HdtvGgFiNZ\r\nJvUkZN2PJMdhz27DSlnlXF+AZ0CRO1uwdEq1n8GLUicvcFyYdhYht9qj+mZg\r\nA21SC0UpiJzHZ1Jv8mAOUq0PIyxq/3wW4vY4G0+LZ1fI05CYKgjr6iat0+Nl\r\nSdqMBnh7A6YptN5MMYAXcjU3tt/vgfYUs/V+s8oNCCNBeV6v0LZNcGy9yABi\r\n6BKhZhAe9CXEzNR8BAxmxc+HZnUDOhz0eaGTCOHVtlZLCDxIbNJbltrORSsQ\r\nmnajBeVLo+c5fdaLohsQ4B0vSlrtdQTjCOW0lhUQk9UXRDVbLoc/XXrcEgOK\r\nxF902l+K3ex8Gfgam3rX/1h3PGcLOmBu/QQ=\r\n=4Ncg\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc05": {"name": "embla-carousel", "version": "7.0.0-rc05", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "0c70393cb9284435c8242d9ec9e5e754ef81749d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.0-rc05.tgz", "fileCount": 41, "integrity": "sha512-zRPQniDxj3t3Q/okKzP8XsMRtANItAK7nhQ3Smpqfbtw3OMoZRebojUDCMD02sb5CY7ghYqL/XFEuXxS/7vJ5w==", "signatures": [{"sig": "MEYCIQDvQ21ggTiXvQi8hapzKX784NX80W/Yesig7NZOBCRyjQIhAOXWsOt/PAbnMXpZovo8Wo3JXY+l3Ad7Vy7rwWucpr1A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0ytdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpipg/6A5GejJll34HtcFhF+H0VpylelUCivBuw6OODLK+PbYm1fd4P\r\nTqTIdty+axoX7nmzw9Kv3juKKv3IEQALQmm7M1az4o23EnIfX9g8Nqh1D8v8\r\nw7eabpIPXJP2WyD9ov4LzKqfyDWBVMqmj0Wpp1V6fZRApsNfGZjKtyws355V\r\n4DY/LNngBrsecb/Bc26VSswauIU8aN3x8exw5XqQ6Tq4xkGl1TpKrOPqy/VK\r\nDSypsLWSi4agpPFr3+Ja+gvXmMmNeB2ZjbhSZy0HFNo38Z5bWAElImgQB87b\r\n2eyPixBKvNTkrUOCDYIAolWQJ2edJq4w8C0/NLRV2MdHCwD8HDSO2ScQYbGC\r\nlaNjJshBeKdCwR8LN/onb6/g+n+eny4XX6fPoPh6cyd5JTOYCUdSA2agaY+j\r\nNi9imhWas6oq6E/HrNrmL8Q1Rp2hT95TxwzzWcujlEyjUsFdv5c1nq10Sbat\r\nNBG38DGvx1aGPe0s5jP7D3aADwHB0eshhCcnSeZvuihi9zqx5yv9iwW6Wjsq\r\nBPDrn70l7hE07xNrZzkh+RUvSdLvX9rjkC4eq4ejpu0pnDjHlwmP/1j6gSEi\r\n9aCGaI28gMtSr3/Xm+W5xktIG3f0jHd71J8KAfpdobfK2r20PPfJO7j6igV5\r\nke6NzWxqEqLEA45XGWv5iGMuT/I1ns689lY=\r\n=VhAT\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "embla-carousel", "version": "7.0.0", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "a65d0b97866ff83135568eb7a92fb0bd6b99368a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.0.tgz", "fileCount": 41, "integrity": "sha512-vgyElJaBRTtzWROQuO9Qx/VlibzKdhwnuQ2Ldh5/7/jddrB4XTI6IQlgR5ZglRaXjH4nXjVtUSwWWZMEIZQLFQ==", "signatures": [{"sig": "MEYCIQDlbv+GqG9Ejot+SwU6UWLCmOxZ4uA+mGUHJuyMQO4LKAIhAPbkP493i4JuBpye9ZV7CGzt9qQFIqxJuWWxuPO4LUid", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi55/3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprtQ/9HvgYcJf5sYm7vdOicVcvQ4aq9DVb8qi/q0lK6u76I/Kjexrk\r\ntWJs/w5HLhQvzbq6SqBmkspmthfrDIT51V9z0eQC04sWzope7JrUH9T3vfm+\r\nlZtkq4YzgT/5jAa1far3v2PCLY/seo4kxnbBoGtjlxSmDvbBZExWnP2xZ7CE\r\nAW9iI37lzEpBQb5YS/oiNTKw9ZODR4k1AHpUYV3f6QJsF5OcjeNASqZQeYHP\r\ntY3XhE45yODSgM4xkTbbpPjMeSN/ocRRrd0PkSCbIdeaZ4UMAZOMxgLxTmcP\r\nhaMnjK8nBQPq2FE4nPR+pU5bOsqrIEyh6IlpNLF6/Z6tNbbTPQDz0ErKbvpF\r\nFPV3xRFFU/SK6mJ2wsxapr0qob9szj5ncdzHqX2pJ+axWWZr75y6tz+cn/XB\r\n5BWU94GsjIeBdbrDI3S5/XGQzbWpILF0Q1MwFvnqgoM+j2XI/6UINOMtpf8E\r\n0qQqSt/Zat/REHBcdrcXCIkjnODXlMI3moK8iyH8Y+McVtuCeHKGvKumuVAU\r\ntsURN8H1Ky0BqWBpTl479f5eYYXyBttcXoElemDFgh9YfBdQvaE7KxerL2IQ\r\na0W+Dpn+N2RHIsmyzCsFUMn+alw0HQQz1WHva7wA5lwz1UzqYhq1GkCLlJV8\r\nL0jFFF6JwbQhVjbvhOTBUcqZhr1z0G+kJvw=\r\n=0MeJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "embla-carousel", "version": "7.0.1", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "51bb13f6a12cd455c9435c770efc85322d73a976", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.1.tgz", "fileCount": 41, "integrity": "sha512-+beEwrdj+FGyiz9a/prfj4IAG6CWDSRYUB3c+FKf17WUq5OxxKg5BVDF5FWcvC3EX6n8wFKQGXjkB3obnWZM8Q==", "signatures": [{"sig": "MEYCIQCaXvy2djjiU45kRXwNUQrdI2PIAgdIoeXbRal+YXbkKQIhAKIx+/MtWJBzWQdjMGvc91xvmvMj4qIlnsZcfL2YS1My", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/jmfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg1Q//XYWTzFr7AvEvb3QGLQccqnJ//zaZMWenQoyJdJAVTeAf5iMg\r\ncskpO0Kq/IKfkz5HUD8I6HxPAInUV+q8D/iMFAVCbXiGM2OZr/UnKSlWr0yQ\r\nmBUsZ3MSoJeuPJFTBKXBv3lWnt14Lz7mkZ/2l74DWToJ/NZSPfiQYLEu0s6+\r\nCbvBOa6tQKT+zR+zjuU+5IA+eKEBnFxXq6wv/yw6obkIZEbDng3xPfvLENeR\r\npyFg7cDs/fYBlKJM9qi22vBbnP5RA2Z0gVqPkAOBNFa9sXVTtrRhPoSLMNsw\r\nEFb6WdENr9/HcwYsSM0ARNcpXnRMEZT86ZVqIwAe+QPsLr82dI+bdBK5VEUs\r\nPohgDhwU6BLN97ay5gZk/6K1U/wL/rvVgXQa8RVh17sErGN1qkn5oSX7lwBi\r\nUJ2e380pE09uPCModYbE+7Q/iKNYONu9O6LFUIVZsP6vY5GTqhmACSA7KhXK\r\nc6dDiZgeixpg1ZiUQhAz+TTyC6EjrszLbEjxSIUXv1suNPQx9UAOiOGRos2c\r\nsU5atx1o0LU3N7w4CDcXfvfOuK6c2lBHVmQZb/Xwmclpd5QtQ9z0MlLwtPxr\r\n0Z3r4MhJhIjK2OaX/p3/qbAkCsUmBfXupUW5PN7XaiWg2NB7FDlvWGqgAoPW\r\nMzxLtSGX1P7Rz6/CFDhneVezoobpZb9cg6U=\r\n=KHTd\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "embla-carousel", "version": "7.0.2", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "49660d75d26604de1ca1bb0c0be13e7cae65b0ff", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.2.tgz", "fileCount": 41, "integrity": "sha512-6SHKbLJRvcjunb0VT1Aw7YwK0Dw8ecqEa+ppT1j+kHGEg5I4WvUd8SjRkc1NAuQK0PTBD0d567UCmzEAngeAxw==", "signatures": [{"sig": "MEYCIQCYCCZDuvzDmxHdlSpwl1Ze8B8yaO+Gh5uloBeU1MdIMwIhAKN7h8W5IxsPDb1rFgW+HuXsUabjhOQF3jL9FhIv7R5j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384101, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDzuJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDzA/9EOoV/QE0wsSN9nHpNaOiL+MX92r3pJ2YBUJd/Y+so2WczJVz\r\noKxAs97Syj7h8wYAMV9M1qvVtVRqBHtlHyEwpU7cDqmAs7uDfnI6SjGRAUNm\r\ne1luTUdhDAtrVSjjTRGabkhN0UlEXHCF3hbJ03iURCkmWRP/yh0kxgWYWzn7\r\nJJXi/+76OWoXFHWqoJE/TbU7BIXKzb3APM194sRAwyj8ljbqm2wX3qxB/dNa\r\nj7+Tg9vXKhS4lI578eGdgzC2SOg/qVPsV+OYConrPZ7UGmS3pFppy9G9HzNa\r\nSKD8Wl9jT4cdDI21+fKlS1sTsqXQ3L3m9OViC511oN5Rgvh4TPb4+oxmqUFV\r\nNqR4tDgLdkSpkB8W0UiGldSW9xcIK+0Fzu1krgFDY10YnKfINI2C5u3PGMp/\r\nzsECrY5HhAiAvajklWodz3IvYC/ewgzo6hFAMw+5ikqj5VGnsGtPjw4sP84O\r\npX8uHlxHzJVMCMBdTG/72rgSmkAc6QtJgWfbzSpnHryHTcG1OtonJJCe32Jf\r\nGMU4AQe+q+kp2CYK97MnSl0N17lILwXbAjQmRpvyTJzR7rnExjCtsVz2WvJN\r\ntzTyHkUYzhPpTNINKVPDFKy5Om/Er8+KUMOcyw+Ou0fLFFBx1NYVcU4b8dEv\r\nWnnQ0J2EyfsO+Ax/SOaEVWxlA0djx4Y1PmM=\r\n=FO4z\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.3": {"name": "embla-carousel", "version": "7.0.3", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "7173a0f27b697a42266d34764c657eeba85bd7b5", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.3.tgz", "fileCount": 41, "integrity": "sha512-Zo8E/qNb8J7n/jcfQxPrfdARlTgsMvmcXshn3J/2oxWVLwL/3N4cAhuqLQiud/oCjZzl+A8maCJDe2PDVlc/cA==", "signatures": [{"sig": "MEYCIQD1X21oqlxujFDbKZ74A4d2nIdXSaZWgOd06GROk1jn/QIhAIEWeAiKttK1eAD/UFkBTJ+B5UYwcQvBbOdju5yU3W6u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH4asACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5yw//XRnhAyTwLx9TFecPtSRziOuu4ZJe9elLJ7mR7gvfog+h8bZC\r\n/znf87Ew5Cf/elTmPQ6cEg6mQiR5pqdnJGoSq9iEsD5l3+tLBczLUKewtIhw\r\ndpuKHmQvwfvLdO1JPL5l1x+1Y6gjXnQLvH7iT1p2cWwhZaVRVHgtTAOx4/aU\r\nuNKOgDRJcYQpyi+xPYg8e+4IbvmBX9ycmnU5jXL1onB4eR06YuzB1LnnJd7Z\r\nqjaYcmmpKOU8qpGyi9MJLIUmnY9En2+LEtfwhtnzmMwkXnAvIaSgV2rLIZDr\r\nufM66cCdng69Z+q7yy8KoK6zzZFBz/3NKtYmvStp8Fj3eaApq4GInpfGQ+aI\r\nwCzbQRqBOn/DIZGi9sLiRHyJFhQ0Qf24tWM8A1ICD7xpyhpSkvdaBqbDUu8I\r\nXjUOVbFKAos+/mkAPcZzg7odBoTK12hhNSQMwrFbQDgwfK+gkNdRd4Nm4UAc\r\nO/ZNrX0DAcBerxNOfgV/y1X0SrQFmCQGI1lDaiNlb1zz/Nil2lh7HsJ8A5P3\r\nvJZZjLt4bSdj1459kjR+abiiYlhkEiv1BJg2h3YUG7MFfr55ox7N5OGRsuLC\r\na37YnxFTfFCIs2oe/oIHseLkDzn1fJTRFAlOZzCj6FonMoW4KX1NWlDJqkPO\r\n7bU8P+qXJrJGWtlo9BBgjwbTGiBA0GgVV+Y=\r\n=4tLR\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.4": {"name": "embla-carousel", "version": "7.0.4", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "2ca87d51da3a45d1ba6d0caee576a05340d42242", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.4.tgz", "fileCount": 41, "integrity": "sha512-2/EO9Zh6yT1EiTNkCUhbYfAqe6PVODSYFZQxTeuLGFYYNTKTkHvalfuFVIdEDmtxbbGepuYtLqCmk6yzJfJeyw==", "signatures": [{"sig": "MEQCIFUT5Xfsy6tjpRY6TmcfMw7FL3Mm8oWJi7+/+XAKRJsQAiAJGQ3BQzyFwlZa7r8qQhg/lIT9GhlJ3prwuwRjKyBoPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXSzEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9Mw/9Gs6LMKEcnz8wn1Mu5fzhyYkOHmmVGt34Ye3lI7fIju3DuXuY\r\nRnqmEsfxgTJDIWCWJFRsLSuVmGkjkT2aiEAq+aUPbogg2lYJz2lWJlcvhjXx\r\nZgs3b6HRe7Eif/tpYfj/dftK0KlKFaLLgC0A0Tkf5NTzP4Tl2aUGRaldBa2R\r\nxe04Hj1M87MLQgDT9ESa+goLv1byX0OFi1VCyXfcH67I5OoKhJqkOKr/kuFE\r\n0fzt9RUAox6AZG2x7dbnbxgYXuAQkXjKmW6ifeyVV9Wyx5FdfrRAKsZMHVOy\r\nGknaIKpooCsPDFtHai50sHxpv2kbuCJUnMvBpKnqEAIRkJ5XjXI+KdSDvcxF\r\nZvU90c8extOt2fmwSSxM9ADJBBGPQcavjnYf1c8LuN63evh3EgXuuWWXkrfj\r\ng7biVfdTCwkY3xOkSySr+ipX9TtrOrHD1nnCyGTBDdGDG83XUo6SxQiQJAzG\r\nXL+ZNeVOk2Uf9XjQP/od6AuUtUKbYjbHsvholMBTGoiHC8PCA6nR6Ls94b/B\r\nSFbxHuu2aiLHHvDiJ59a+wRDa/6VSnQWP7FrOaBV9l5Zn0w11LEpXUIMX35d\r\nu9WgVMaZVocOYbUQxQVEimrgxor2B88aQbFPCF7++6u4PLVeQXHwdBxpoGi9\r\njGZqXNUw7jLLQt8NXv35aJqecbYg/yKd6D8=\r\n=EtBW\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.5": {"name": "embla-carousel", "version": "7.0.5", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "b43e590c072d06a5b11d4ce19d024f5f07a62862", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.5.tgz", "fileCount": 41, "integrity": "sha512-loRcu0La0F7bhEMwbvgpi14iaaIEdwacH1xoplnaYfwN4KMgECkiuLgu4lx+v6MYd6lSpg/bw7ZxEuLUwYjavg==", "signatures": [{"sig": "MEYCIQCxDH3Xeb7DuM9ZLMt9zcLIjKw9mYVtWnM1VHVgKp00tAIhAM6maxD93+glciww1v5cdzs7t2OScIigr2Uxp8+oRwI6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384063, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjah0xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg+Q/+NDJB/im7Gt/LtDTcVIpVk9fgrnpHfzDUFSBZBobaOtgc/PwK\r\nJIuS2t5DAZ2FPj8b2d03PB4vDu5F4H3ivLxtotaiYPGfUsCx7E/NjA5gEIVM\r\n+COD416ZJoZPM3tRwCcsJqmxGuJA0yVnrBWGytkT4HhGb7V2VUvgsbR8it/4\r\nHcyI0pPHUriwLUaREUAYsXt+A1nvJFtL/evPWUSXMPgkz8U/CIkMq4EJ2m67\r\n84psWM0EAg/iYtkjytRyu3iMZKj2v4bi2/VPB2xSa6+ZT+VLhgAQUNCnm0YK\r\nBjTXOWl5v/2aDavTbaB4Thhlu2uLvs6lhW40Dn9jK8DFccb4zUHfuZLMUFRU\r\n7LRclhj+A87b4QM41WSSYAs+SRMBAlSHMrTK2MGUjb9KVdKQZogjB5LxI98t\r\nd5zSiGmvIK+WY7gzXnghEX3037Xyzq22yGMVRqFi2umd8BTww4sMbJRDyNis\r\nOePSyxKvJ3sn5MvuMqu/pCJKeSwJy1cGpHhp/b8QbeJaDi8XP9+x+ZSeJCvX\r\nFtM3uKb7reQtIVLy0a0yFSAMR+lSgXgqtlgWfwjotF+N2uxnPfA4KaLIIiLg\r\nhQZp5O02L38+JnoaQHoZRVPXG+h/eDeHkK0SGUIhoqegdLDjsYd9zPwDK9+0\r\nj8OL3DuqRCrxvsVUvbrtdUizMjJjS9hnfCM=\r\n=LsGA\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.6": {"name": "embla-carousel", "version": "7.0.6", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "5dcc35d42c283255f6d8f0e99db37359e34c4b2b", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.6.tgz", "fileCount": 41, "integrity": "sha512-0YrByGAaXxl968KSljUl+28rV0PrpR25aU/Af1Sw/1F3nMco391ZkMV9U2SZu9glEqicM8knuqnOrvLJaNxTWw==", "signatures": [{"sig": "MEUCIQDZQPJKoo7HPACJx9TRURAZ92EG9Cij03Z+8bOeRclaRQIgfrptawIhpt5Lds3JAb4tB0rLwsccHyFtLy/3u2YGigo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxvL6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUBA//dWkR9DJi9ElMErFPhkxkRmdj18YSrLIfwNevDzgioEYftWMs\r\n1J0ADI15rbbOF0bz14lW8Zqmwk29il+ZLZ6RaFSiu7d3j20gSvoM/aOM04Sa\r\n0cXjxH1TFhwYvCa7VuqnjEla8QfBbrXVvfJm+m4PZyaO3iI/Y4L3hPAsPifn\r\nb/KitaLyTm3LfhaIZ8vmYRTMPppiu5h89pv6mVb1j/iOIOqFmBV2TXiqq5pD\r\nIZi7m/ovKd5yPalZdtW8vg0wZ97tYTdkTUbpz2SbfBTNrYNIXe22rhPyx7j4\r\n+EOrQvliUsqAeq5mahfHlGu6mvaMZmtonxIIiK41nYD1xr1ljuKh3qL0DCqn\r\nNDghHRVMuwoeRZt4NwavjUVi2DcvwOki66o7EFOQEKWs1kdfxjru8myyYe2m\r\nzvOPoHxOYOiK3VGUlzIwYfrvoTbE4hIn259nwAh5OmTWYqxs0CgMbzYmOOpT\r\ntopwnMcUFKFXmkv/vK+POrqXN+XYjromygdNISVSrmLs015sxXr33XykebwI\r\ndAcLUT4zn1XyE0SZP42qX3wQOJklizzp0tFa00UTxnciYlWOE82DyQsz9JTM\r\nsk9vQeq4SUwtKBakAC6X7hmWk7A94lQTPanAk0sW9DjC7pE1v/Ld+h47+UMf\r\nw3dM/6/Bsi/Ge7JayW1RcEBTaQxCXcY72fk=\r\n=Oovc\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.7": {"name": "embla-carousel", "version": "7.0.7", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "0fcfd356d36f2e2a00311db406bcb90dad64422e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.7.tgz", "fileCount": 41, "integrity": "sha512-eoTCIUWkMQJlm5VTtSJweEwzG3kgnI2lb9omEvK6sDdPFaEaj5I+X505o78F526P+s+NCkvOaX57o7vERXYbxw==", "signatures": [{"sig": "MEUCIQDsdAoA3aeLO0/sV8KcvaAm1UD3Z7aq5zIRvTHDg0Vd3QIgFk1a/9i5P6yPcxkd4wIdAUC0piho/97nG6+b2iKjO1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyuqrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqotA/9HiNG1TBixoGWkKHugzo3CZJifDm3/aG1OJzLd0KLRx7ctz22\r\nAOBfnQnq7myg9yQdDrXncdvhq2YFuJ99uGg66SR40a4on4KnlZxlyQopYEf2\r\nerEAiVlyirdLuVxv7/bomfxs6hq8g17DrM/+pLdhsoLShL/FLgByOqx7Pj3O\r\nX6H26VrgS5zAeqJXqBQfuzSTfgP7LDFE9xPhEwhD6qsrZkDJprrWA54MGPLs\r\n75XEa0METZzOJuPf7jEzrFU8/KO3jkxPPGA3dsv3wvqiKHnebgaOUY/n0nAz\r\nvIw/J7rhL4bxCETVRH40d26rpxK1bQ75ASs3dwxS0LieNshz8z7PG+fveexc\r\nlT8gIUmW2QlMC8Jk06s5rYHm+Cc+QBSouqprJwfWJpvwxahyNVPN7I6JEKdU\r\nY10zrs4v9aYOaLqNxzEm7bytrHobx3VAzWnLlCJaeVZFecaWVJqtPPZDPFUX\r\noLBde46sGA+e/pRB28W+/5HwY5U1DM5oDvUaqmmOmxmAm6VB2J21R2544UHj\r\n1x9qB1k78OspRJwwGrD/cRRfyoBMyTW0jZ9iuyGpQFsRwlZoAwmp/DK0lZAj\r\nJmyPY0h8uykNNLXVUe0rD187XDs1kOp76pnMc1B0BsTG1851rnpUC+2b+LkL\r\nvJdWqOqMOAn0jbqkCCiX8Q35uN01wOvNTIY=\r\n=/EnF\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.8": {"name": "embla-carousel", "version": "7.0.8", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "cf31bb06a5023e97ad59b2aff525c988c9e6528f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.8.tgz", "fileCount": 41, "integrity": "sha512-ImYLiRORMOsx4KgJlaGbg/h8EEKu44asQ9c8R24sJrrFkERO6EJNlfN1h7TUooGeLGoj8+F4Jnr6Dj8gqFQWkw==", "signatures": [{"sig": "MEQCIHp/OLmTeejuBotL5XLiF+XFfBh5A8mj1tjDUxkfLtXJAiB0K36VzFDJbK3aRL9zhs/pq/uHyxWVqkGFWK2EueLCwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 385679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzEEsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLLw//Xt+8B28beg9QHhNA7jvUoIE6PUSV/tgx2iq6jSsQvI7PTUPJ\r\nOVN8w+Byjm5MXw/U1hdS1dI40vOesJFmTM5vPqPfEEivE70qwQ9VAExfdNur\r\nqkfemOR478K99cddh4tV/eAlps8oYqSFZIpOexc/9bAl18B+rL5fCB3Xmg9g\r\nniCUk9F73hJS0GJfI/Wlv2IdfGU41Oq8q/m3YvGOB+qEUXRp4jcQjbzujbdY\r\nrM+PWpg951SzpJYMdceHeLWnA5/J7pyMmuXrRuRtpzzUK4GXhWy85sChBaoH\r\npzuObVOqxxJAd/X+py12KgKLnTx++TPHnYTsWtqdaJGyf56v6RfWmm1hJBsN\r\nvOdbcEpYpoRxSNjYyUaxKKWuRTaRJ+dinLGn6VhbQGYg/85Se6tLZMMxRODF\r\nPkEDxMefE3KsMYgAwBgd44sYmlYcjbkc++IT1kv4MPRrl1m+Kkh8CZGkZAQZ\r\nHZOckOvISiIYgZoX7iHzqt7D/Wz1vjMXxM8F1x1Rw7qSb6N7A+0g5R7jlL8q\r\nIRQm5w3VqszWlVxOjkeyGoTvVSMRBsWsZFRgASECaUgd2V2jaoQ7o8U789RJ\r\nmkcOweBnMSIq/KinXNjjj1fF793KRJ6j9ouI2pCXbvgZTEFYM6kfi+LqkIty\r\nhi7cCHhJu/dWZRD8kYH9vLNCoFIvDmWXtXo=\r\n=GIZS\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.9": {"name": "embla-carousel", "version": "7.0.9", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "51dc885e62b7208c8fa9ea9c470d5ae20012c39f", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.0.9.tgz", "fileCount": 41, "integrity": "sha512-g8pGPd1BxexiZgw8F1aNM1KL3x3eEcz6TGTz20AuqCCynASBV7/h1+aS2rySDc/hHkXqbU8dk7TDhW71LCqVkQ==", "signatures": [{"sig": "MEQCIHoYJ6PoONPWoq8ZX+ab4/wBo04xV+LZg6Pirx7gAx5PAiBEtQ/JFsUT+IQPSlk3X1NmYULtLSEfOBO+qVcrnib0ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzuo4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMSg/8DnrHvCoq/5vyqMsKxumIg/l8oAS9Y4t4wWnK1RZhnb27mU0a\r\nq1eOdmj7BFZVg3ROv12eVD+1Eq+QLnlOCpM5S6LegIF5UYMVnbrZYNPN5I6G\r\nt0rnY+SMtEpus13EwqpX+tiMxnnDm2FGVE5u/mhFo/au2/j3Fx0Jq9l0rnt9\r\n29TxWQK4pXFqm9x3w6fYf3C3RX4pVdFvMw1aU2wgnWaa957keOuW0X/TOdi2\r\nzKbwO9+GySP5qlzijeeb9903HsC3kcW2ohCNTAc8okRQrZVOdLRyRQ061nX3\r\nh6KKSy63/Q9X2GiuYxcW5TuWxQgM5sLshEtw7xvKgUDIaC0gaoZgJIqmkaHx\r\n+IouKQtUB1kGEkrVjjwLQFlvQlsm9EA8ZdvvgW7VDCWCr+ZB/sryZ3IX4Nvx\r\nMsggRNIHZ98ET48a0Vd02AWqAkYgV/Rzdq6kkYUWfMJdvknKCzSVo7i2LF70\r\nU32JM3BMLzP/2oobDDVSxxC5a+dxQSohKSQBJX3aEkcjmpo3C6tCKmfwuVqO\r\nYS0c+8nJb/CLOKv8kAglEKt0enfYuiwAkIXkArjQhLIRoledpz27NYH+KW6f\r\nuxNtwarXbLV0C0jJqbGbAptm4aX0MQ/5YrSMtSnNSbPymf/0ptPea/WoI0nu\r\ngLD+dEhTi1YpgkHOX1J93aYMYC3ijlwulx8=\r\n=/GS3\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0": {"name": "embla-carousel", "version": "7.1.0", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "c770dd3ade9285a97f5e3b6bbf705bc35ec0f9b5", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-7.1.0.tgz", "fileCount": 41, "integrity": "sha512-Bh8Pa8NWzgugLkf8sAGexQlBCNDFaej5BXiKgQdRJ1mUC9NWBrw9Z23YVPVGkguWoz5LMjZXXFVGCobl3UPt/Q==", "signatures": [{"sig": "MEUCIQCP5BwGqSguw/01gs/VCGbeZCRSZS7f8qIqFOOim9wLnQIgFmtRXVECQ8rzzWkI5H7aygwkxsAC9zyP7OM4D+Ek9Vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 384904, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCOjHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+qw/9FCmnmOZ9TWjLlIeb+LINtLf3eHngp3gOYMEkuuUgiLI/WbHV\r\nUzHJFMCeoDu6wH43MoVWPr9VcgSxQSYjasub/pQncj7dYMiMzk3Hs/oye/u5\r\nDJ1VYt9NRfoZ4Gs3E3GEifSEvtxHh40kE3SS+94+ZAE/yZVEWn6GKpNP+oix\r\nCiq2lCP9KEplkm6WkBZeBK8KcIavy6SFqGwvm0eKaNz6DUqRkzBx4PSii12V\r\nIxZN3iGZp1OxthRAVcTrmu/uRrC7RQXX+DAmzvJZo2JQ5LBFOqWUmvZjW7xe\r\nG7c7UFwV24S83oTmlJWNCyOy5GDwhz9hBWDZOAlgavRcxxVlRCc72tgIr2Kd\r\nhL7XVDRKmdaebYUJB2p//24kohL7IiFL2nda85p72xSwKvzgQEhb6wRdZDxl\r\ndL6i+Gxu0EYHctHCAUM9DddoZcodMQDeiWfUBwXHayhMKgYmiOs+w6pT5Cuu\r\nQbCoSiDKoDLBVh14fIk76XAqj1b6J66xw/aUXv/ghojSsbM6D9i9BQp/uRnr\r\neG9tgQftKDRFW37MYtPsGNKhx9VI2XTLjEj1IPEWOMfHvqaB+87IeIEEXG+/\r\nghEwPMawZna6/MIKoGdpUT9V8OUK3qKPbxWTFgBiZWMba8zp7zLYaCrRQdR/\r\nUCy6/Ix9CwpFfkbmvjPFuofm1eegdZ7u82I=\r\n=EsWE\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc01": {"name": "embla-carousel", "version": "8.0.0-rc01", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "11b8a52266f15f737469326c0807878ad36d9b46", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc01.tgz", "fileCount": 43, "integrity": "sha512-kxP/W5TsDPw974RSAa8Ad39KtiCgOvpqNikuY0ASmC5D5XTVwzcwJpgtlnwq/kxtxLNq5G0s9PdjexTb/xdsqA==", "signatures": [{"sig": "MEQCIH4ktVfvMR2b/CXrkk0u8We7AxtaPEWY9A94YcPS/LUoAiAD6blN0EqY5kIHR80AOU0jywbexVl3rh2Wy099Z0UgiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSDZMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSmhAAhS4/BGQfDr6hHMG/RYnd05x5Egh+7GTpFE5aHmQdMgXD97eC\r\nMkS/pGS4FHFzWr1XG6hX4y8LryPW6xHDdQbkdtBE0EburCsX39Ck8LeKi0o/\r\ntoYkTZZwDTSJNdo0HhIxy6sPVTYkQ2/ucDAKUfo3b9T7eZ2CHxS0s65RoS8Q\r\n0mXeeFwbB4v5+CY2WUCeMVPxKXmMbk+g9dUziKbWihDv3fvuimW0El6KvCwZ\r\nSxCPQ95ofR5MrVgYgmgHyZVdLsihfaL3b03X84S8yp9YbszIZsz+IO0qVaCx\r\nM22WX4tg83G5PBYSMken20VFL/1gLb2zc5t7XzP8siWFexCEra/144Q9ybKq\r\nuKn2l0a/4YiU4ZdJfgbR6GE5DdQnKy6gKXkwnLsCgzGyd1Wi4pJRns1YDpcK\r\nc8u/EEn8uT0MmEt26COoUStMU566kIYntAmj68T4j4kgUJNEG691N24iFkWW\r\nNYenEq893BSS+CvzzCMu6ACvAgERcU/NbgqS98mmyCo35I8N465WYmKSh0ho\r\nRD47mWr74KtCRkOrWTyHJH9taCEqMwGxb052iwkM5kMMgz4m6Vz99wobLWKP\r\nFjdI1TqknWyy6B4C9esvxrGYu5uVLBzO6rQxUa4IkrBdk/5kWe1yT+SoyMGb\r\n06STDiV8lVhY2oZ4i2Iz/wudN/1Eb+fPn7U=\r\n=hxYJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc02": {"name": "embla-carousel", "version": "8.0.0-rc02", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "74e9f7e5d2bfe20e013285a50ae165530ce8b436", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc02.tgz", "fileCount": 43, "integrity": "sha512-ZD8zkPQqp56qOhNjdT2MmzeJmPMaCwOQW0oH/8dXWy7krIzJkCkaUMDYuD3VXAHs5729WeH4d0znLu6x9RadNg==", "signatures": [{"sig": "MEYCIQDOMHPuL3hDqE+q1t4q3SfNlcoqRriGYz1iv75a6/dh8wIhAPsmIVEpN7upWbE5xZAx/FH3SSUUcQ+wMCuUdLnPv69N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkS6niACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4Qw//VIMoR1jexIxR7Z10lVrymhH9Rf517IcdvxuppNj7FXiL31k9\r\nTdo92YAfQQx86SFZAcAYYN1rXPaaIsKszd+TbiF2fj/ZykvQaBa2UE1A2G7J\r\nYWsTkpvZpCLEZjmkSpL8LzCv5RjmyDyqkIm+p6i1s3bUPwzGlaq7AU/8yMuw\r\nSG+aQBgbb0cZEYgbs0VYARugJwUs8UvG8n32qOlWPtpucLkbaJSw5w6RmlXF\r\n80CF2WUqB/6Jx9mb8wU0dCFl4ImANFehNKL6vxHNimD2zmaSzjxU46fE53KS\r\nSMBt6+nxI5wkftbC8NUYIh2fKfZhefciLJEvA7m2SSfy9j+TvKs/wTvWqmDh\r\nyCjk8IoIaAUGqGNEJw9m9mNLEX2q6X7FRF1mleT0TndeCcuaeLCPKDNbfsLj\r\n48rjxHPBB4e0dV2gsmBp1LD/wHeJwFSkv51QLRYzWGgklG6i2fsHBAFdpD2N\r\nYhGKkpB+5FFbTeqo+I9U0FhWm8+DxUbLXuvDVx/LOyMScAkRsS8MgmEFqsZL\r\n+TQZdu15GfFpr1Anof7fIR5zbXl6I/J8/HC/Hbxm9rCBxAoH1kjBiKKWOxrO\r\nPb6ZN2WaZ8avLa7Bt+bCPuO2OXVAgzZfaIAdaqJtt7W84mmYZYn+Jgo745m7\r\nkMF1BTCSIy//B34b3RCwTf+zZPU7RoQiTok=\r\n=G5R4\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc03": {"name": "embla-carousel", "version": "8.0.0-rc03", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "fadbea5a699a1a791f3ba1b424f2ac0ca170c622", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc03.tgz", "fileCount": 43, "integrity": "sha512-2goVwmTYZdGFcRejbYJlaC/iWDPv/VAsASE60uLQYLSKpuQRTxq/huu548OozLsH/2uSelVszvmzfinZnnk/iw==", "signatures": [{"sig": "MEUCIAyv2dUFd/jEvxC8uFc1j8w6p5s8g/tZXdn94G9L3HUhAiEAy+WgO29kMXPk1PyvV+2EjVmwaBNXmTPxT1aam9zYVME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 131126, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUj7jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMCA//TOP9bmOZPq/AI4lOmUnlcqzVa5PzE3y+9L3zpB1Fg4EozcjO\r\n+dJgWm4dGIFp/eE9LBmua6i8FhCdeEqA6urd1ZVhDlrPkxD+kRebrFB1TOea\r\noJhRMs0QQfvYX5V7znMOYt5ep5UtKsjKFF/A/baEl+pTvtt2YECQsNS6E+ZX\r\nZQIfiZ8l2J2H4QUmcNo/fe8UAfyPVqwE9YWeLRkfRmgdfXCrDsiuyx7X6Jnr\r\nh6+ZadozAf+l/umPgSg1SvNYjcqs+7t2feqioO7NptpSTZHgZCj2eSFrqCNu\r\nuSZZ+sLZxslKzN1MaAKRh71ou4DeBx89oFeWlz/rjk0kR/gAVHCVMAb0GqGj\r\np9OX39+EbLTdLHqvsM/B4+3cBT0Hvhpg/bwfnrH76fa9ykzcg0XPFinrGxOp\r\nCY/EPWJTEfPBAqp0QI4FhTPR5MeJFuFCAxQQlwfcarfiUuwZ/jutim167WTw\r\nWAZaL/iVAW02ien8+qFBJTfWmIgt99rPq9wP/DK5DVvvClXr2JWolOztM5nL\r\nSFPokj5277nYN1yptej/LWZdUcYHWbf5tow4hlsTgS5lFQMBuao28S+XyEKp\r\nYBfUI+J3c9LS05WVLlETmOaQfs+/4Gh3bEefguLIdjDF76QFeqyXxeypFHvK\r\nU3tz2c2m0ncX9I5llQlAAx6qMv8meicU60k=\r\n=oA42\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc04": {"name": "embla-carousel", "version": "8.0.0-rc04", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "128be74829d82dfbbf9ac0d269f2ed42a8a1f3a6", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc04.tgz", "fileCount": 43, "integrity": "sha512-+yIKQPSiS+2WcjIjXknCSQ3SPtyRvLjwU0+HJBi7ZAxvg+i405PSVGi0nD3c9XzI3X0s4Y1yuRgthDzusJ8pFw==", "signatures": [{"sig": "MEYCIQCAcTr6CC7NXhyHX8Y/CoOh1HoIqiS8MPimRPArQ4zjQwIhAKw07d+5xfVcK3QxU+eq2Ge2QDDeVDqufUStvMNQRcjM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133867}}, "8.0.0-rc05": {"name": "embla-carousel", "version": "8.0.0-rc05", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "4dfe3d766bd48323f37e3fa2aea4de66be034f79", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc05.tgz", "fileCount": 43, "integrity": "sha512-Ujl1Bp+zIEn0WY9QHiBmhNyC+y7DZMEOy02WnINvJUxwXmuE5DiTb6UyG5b7PG/yLjlCgaboAsUdFOOIS+T7uQ==", "signatures": [{"sig": "MEQCIDp2kWANq3g9GRaFZYqs5dCZGuy+6fM9Qog5/8sUzHnMAiB6wJgSd04Y5o4VRQJsIZ1UD3hksUywkyd2qQ3MaKPSYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134144}}, "8.0.0-rc06": {"name": "embla-carousel", "version": "8.0.0-rc06", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "fef395d6aedc5db2797ec49c004960cea10398f3", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc06.tgz", "fileCount": 43, "integrity": "sha512-Dq27CG9OgZjKxK+JJS3uyEW4dwQ3oVkvlTHEP17idVnP8vmuhi4WtGe4UVGj8m0O2WASR4kNu4efFXkF7u6A+g==", "signatures": [{"sig": "MEQCICpA/F85BOuTGlnhgXyjsEAalWiJm+rLduhM3PnmRuTLAiBXCAVbF/ju6BSg+e8OTgu+PxzbYUdSEaomAX5qvc3yNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134108}}, "8.0.0-rc07": {"name": "embla-carousel", "version": "8.0.0-rc07", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "3d0efd24a45d4439f29299bf6b6c3e5cdedc40ee", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc07.tgz", "fileCount": 43, "integrity": "sha512-IdGbZH042gRT0/3dOCbiv45jOt5E7jpM4h25VzyJujule/3GitJ1/Ib3U/X83auiI3aojKoBe13e1g5g7bwnjg==", "signatures": [{"sig": "MEUCIQCDoKZEmt8gNlRHQwyX6B9MkV+R1FNVUcWWZXdfnRZICQIgUEK8lRfdTtnfHkiBCadOa1j4cYBl66c5+kJMFxUcJ1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134405}}, "8.0.0-rc08": {"name": "embla-carousel", "version": "8.0.0-rc08", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "36cae041a741405940b39ce62b3f193ef6bef01d", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc08.tgz", "fileCount": 43, "integrity": "sha512-0XY/QuclHU2aStnxUHLfv8wecVb36oLhFTP0WRc+4Ob/vKCXnJFIE2O6XceuuHqxcu7lGRt/SgqzAysufokPug==", "signatures": [{"sig": "MEUCIQCm4cZvDSpfQD5ML3Mp9pwEZLPSDcY+PwZ7DTPdvLqZygIgYbOhOeYPC0CGGv/UUG7u2lSsjdWCaZgBi1PbCBCi/ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133915}}, "8.0.0-rc09": {"name": "embla-carousel", "version": "8.0.0-rc09", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "bf4568c521caa5efc39ff81361d6e3de47567599", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc09.tgz", "fileCount": 43, "integrity": "sha512-MEYeidPlyEUCYRc403tVFo1yJUHEBtuGLbFRbpNpIoCyTeBohaMRylr1JBOK4DXdsanQvPBgVPm/f2f2R6lNng==", "signatures": [{"sig": "MEUCIG/t7P/mL5bPMcjJpgr8QTcBjcLT8DVsxVQA1WXm6jZ3AiEA7PmXTuQTKM9Uy/kw/t1vx7vZGLBEf6/ArLi8ctXyFlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134157}}, "8.0.0-rc10": {"name": "embla-carousel", "version": "8.0.0-rc10", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "73c312d95408b8913a3a67e7c59489f8e04baaaf", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc10.tgz", "fileCount": 43, "integrity": "sha512-vDlarCNnUW9pg6a3gOT2pujUBou7fMAJ9/JuUjg4+aV+VcKlo50RHomKk0tp2vzs3+ANc0QXb+ee4GArn57C7A==", "signatures": [{"sig": "MEUCIQCRPAUoE6nCXlg21n7q/wX+QurOjmLW6c5GjOygxrYT4AIgDk8xA0yBNImfVHqL53Y6uRfl+XIRGvoRcoFe1VoZcaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136281}}, "8.0.0-rc11": {"name": "embla-carousel", "version": "8.0.0-rc11", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "700ab6b3e4825ef9e6ac83238b81e3e1a316c3f4", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc11.tgz", "fileCount": 43, "integrity": "sha512-Toeaug98PGYzSY56p/xsa+u4zbQbAXgGymwEDUc2wqT+1XCnnUsH42MClglhABJQbobwDYxOabhJrfXyJKUMig==", "signatures": [{"sig": "MEUCIG0F4+6t4NJBcF5g59Gx0gSKzylq4DOCysxESJAp5SVuAiEAwU1l8yaNrG24RAAEzcTlNBfWxqUhktaqtCqE4l+NDS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 136294}}, "8.0.0-rc12": {"name": "embla-carousel", "version": "8.0.0-rc12", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "8a5ed3ea359bcc820a0baabf557a5d2586881238", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc12.tgz", "fileCount": 45, "integrity": "sha512-/Xkf5zp9gs9Y45lSAT1Witn+r+o+EtoIIZg4V2lYTCaaqdDTxyO0Ddn+z00ya38JGNZrGH9U8wLGK5Hi76CRxw==", "signatures": [{"sig": "MEYCIQDM/Ca0DW6+XhfjHLQzGoP/GxTXwbk0gmiMnskgDdr6yQIhAMG4BhBAtsetnCGclrH7ELngG/qztMdSKD/43aatnXFh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 145769}}, "8.0.0-rc13": {"name": "embla-carousel", "version": "8.0.0-rc13", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "2b3e7100f86492f3eae1409ee8bbb343de448931", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc13.tgz", "fileCount": 45, "integrity": "sha512-77U2nJRl5YtKCXiVuUqD4U8FQSqDjwVdNPO3KoOgi5WfVHnKgMn0bVOLOd3ehCPGyB/r1Mqd1Gbcwhip7bedlw==", "signatures": [{"sig": "MEUCIH0zCsvArrudHAbNrIUIAqSgdmuz0IQRlIdVTCTXd07QAiEA/KiHejSK0qbgZeTBz7LJDojLJMdjXGh1qAGe07NRxAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146556}}, "8.0.0-rc14": {"name": "embla-carousel", "version": "8.0.0-rc14", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "dist": {"shasum": "039372f97b57e95c9b839c6e27b8ff2f63b23053", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc14.tgz", "fileCount": 45, "integrity": "sha512-/NLkMFZ7xKryRVYeUjmhbfV63Vr07saPBDwAX2TPMbcaiWwfQfU5Xsc2AiCMZANtwmzsjRK6gSBa7hOy/VXu6g==", "signatures": [{"sig": "MEUCICwGwINN+qNk7E7kUXBfEPVQ7naUxltsApWxD9/l3Y2SAiEAzbfPq6gYwH7ZEsC13y/wsTz9pj1X1FUfOJGVzJbBKVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146663}}, "8.0.0-rc15": {"name": "embla-carousel", "version": "8.0.0-rc15", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "68e269bb1ead1422aa3078401947f0d85c6e1671", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc15.tgz", "fileCount": 126, "integrity": "sha512-s7VPexK2h8VEYjEVQFnJAPcRnY5YqJYicFxKVVyWXP3Hk9FFDkT0kqVxMM1PcL187qHOUgmGVHOrfC8xWy3OKQ==", "signatures": [{"sig": "MEUCIQCGkMt98Xv+nEumBXlPWHyELikcNONkII5W7q81BSPS1AIgY6LURrThyogiimSByK5yDklWj3Td+TqJDY5++3raLYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485192}}, "8.0.0-rc16": {"name": "embla-carousel", "version": "8.0.0-rc16", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "1ae50286bc93d8f2883bd7de45eb3e295b5b2f37", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc16.tgz", "fileCount": 126, "integrity": "sha512-3OD76lES8/X1G6iecJbceVonYW5ZvK5qUVRsWcbvcyvofDdvomxB6EmyTRLa/2PCRSw8HUUcqBtrzUzz12iqmw==", "signatures": [{"sig": "MEUCIEfI1WFuAKKzvh+cF2JbV08e+UzFrfXkuas1p07OhGG2AiEA08wWz/Ms3w5PX/VWDVfYGA94bPABthCFUeU5SIxN46I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486177}}, "8.0.0-rc17": {"name": "embla-carousel", "version": "8.0.0-rc17", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "146dc802d8742b88834ac7ea34416b0f0e983e93", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc17.tgz", "fileCount": 126, "integrity": "sha512-evF49b88VOitvqFtlvhvKVSu96Y8A+QSFdhok87Bfm8R7OYuk95FT+o8+M1GQLi/EhGDUlT193HTVAR0Wt2neQ==", "signatures": [{"sig": "MEYCIQCzBZDSDh7Bitajpliv0+OE0l6eLPhKWpzRQpcgd6mthQIhAKWPKmMjd/Oyktpl8tu8Ly1ACFp1D4S49oouznT+V2Kq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486177}}, "8.0.0-rc18": {"name": "embla-carousel", "version": "8.0.0-rc18", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "2b4babb3097aebbe2123d3158a029d474a442b01", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc18.tgz", "fileCount": 126, "integrity": "sha512-MtiatQCt+R/lEKl2D4TyAx2Ba4/gfosQIY+Y/ooZu1yahxTbFLyhGW8aodn0GW2WZ6jO3Qpfx7VuqCPdRV5moQ==", "signatures": [{"sig": "MEYCIQCeKUBVpKQRDn++Ql4yUvN3qx4IWgVdZ0pJ78ejvb77IgIhANecZrQm4kWe0jeqkW0oOx1sQ6ok4YNQFcDUXMCMr6K4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 481550}}, "8.0.0-rc19": {"name": "embla-carousel", "version": "8.0.0-rc19", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "f0d8dbf54f2f8f2cdfe0697f371fe5d5fd4e2dac", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc19.tgz", "fileCount": 126, "integrity": "sha512-PAChVyYoVZo8subkBN8LjZ7+0vk4CmVvMnxH0Y2ux76VUEUBl1wk5xDo8+MUhH5MXU6ZrgkBpMe++bKob1Z+2g==", "signatures": [{"sig": "MEYCIQC3xOKZc1tkO3/f/AtEucnM12isUyEe8BJWw5aPkNnPQAIhALqQR/+vYR2jitepxAj4eyeJxJwzrl1YJtjh3ajT5AYb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 483789}}, "8.0.0-rc20": {"name": "embla-carousel", "version": "8.0.0-rc20", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "6763e9db8db11a64d49f42ecdb15936a694fed7e", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc20.tgz", "fileCount": 126, "integrity": "sha512-fhzhbIAcsjSpUsg5jWsg0+zVyJhY5x2SPXtuS4MPAWQWoVQpvkcbX9r0FvPBn6emTbgNFRtAcWczstJy2msdUw==", "signatures": [{"sig": "MEQCIELOo2f+XD/j/t9v5ioF/X7dXdYnsOGZx6yWpyA68v2bAiAckK7SvaHNwmpGgzxBDN4FOcOY0yeySZ+mF2FnFwmpuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486832}}, "8.0.0-rc21": {"name": "embla-carousel", "version": "8.0.0-rc21", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "c12f05528a0b6d0dc17e9f78f3e1da622dcc6d68", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc21.tgz", "fileCount": 126, "integrity": "sha512-rK//vyPIhmD/5QUDtjk9A5RxPoDZ5LOATYMVSFECAzwcAe7yJmqXQbdYzEZf4ASOR+ivod5msqXsKgZXypA35Q==", "signatures": [{"sig": "MEYCIQD4xCDyEO0doXJhfup83jIpg1XX0fXEWZJSEcGWn2xa8gIhANDXlivH30byMMXg/S40XHtWTXz7mtKKkCHkU+uHIZXN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486832}}, "8.0.0-rc22": {"name": "embla-carousel", "version": "8.0.0-rc22", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "f5e4447636dce317ae066138a45abe77e08bae4a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc22.tgz", "fileCount": 126, "integrity": "sha512-MeXnPT1LShfgAu8qXj3CskayV0R6OkHx7w3cPTx+Q5ZWKyShKpIuu7qVQJ5BoFegalE4n6yxqoQaRuGFbK9pYw==", "signatures": [{"sig": "MEUCIHMOOxDxGhtL0rkRGS3AEUxDeeN4ktBev9bX1FJOkTI6AiEAvB7T+7GE/p7gWTaW+mQSqv8r1es56/TmB5W1NZ6Zq/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486832}}, "8.0.0-rc23": {"name": "embla-carousel", "version": "8.0.0-rc23", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "07052fd046d4cfa5cc9809a8003d3e210dd05103", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0-rc23.tgz", "fileCount": 126, "integrity": "sha512-ybuDHm+udElyH+XpuemS/W+x8ZhB3a/4UzeTBvsoZUxDSty12ch1f2T0CZxGqIs2FfdaofEOmpLMSvuEPVTMCg==", "signatures": [{"sig": "MEYCIQCMmsfa0wuKit4Zeo2Wgf4DE5ojniwQhKvAVfXt3zp/mQIhAOIdIbu2bEtH6bXhF7EMeVDw8AIOwfcuqxxzIjOPuLnA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487040}}, "8.0.0": {"name": "embla-carousel", "version": "8.0.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "164d0faafe3220c2584c08d45a738c9fb4efc344", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.0.tgz", "fileCount": 126, "integrity": "sha512-ecixcyqS6oKD2nh5Nj5MObcgoSILWNI/GtBxkidn5ytFaCCmwVHo2SecksaQZHcARMMpIR2dWOlSIdA1LkZFUA==", "signatures": [{"sig": "MEUCIQC2YKft74nJIbbtubt7gY8ZX9MpKxa7Qg0Yu1yoy+fXkwIgYHuMSUrtNxqWUd9UqA4yOFLIhXdAWfLPvBgHI+hXLEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487116}}, "8.0.1": {"name": "embla-carousel", "version": "8.0.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "a9dd052d91a97b15c362723611ba7687193dba99", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.1.tgz", "fileCount": 123, "integrity": "sha512-RsaMRyBCd144N95gb3XoI+H9zj3RI4y0qcfvKYEh2tIAIEenL9CW9vwzltCeoYkWYipGdkvup+HGT9ewG1YTEw==", "signatures": [{"sig": "MEYCIQC+vKb0tiKHtz8QQdN9E2Jkv3unrgWVtI9hbMhF0Om9SwIhANE3DshrD/LU8lbf9DqUT56d3lO/zEVOLpPCctXAA28c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 482756}}, "8.0.2": {"name": "embla-carousel", "version": "8.0.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "31546bcdff7971d44d29cf6bb824ebe923317233", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.2.tgz", "fileCount": 123, "integrity": "sha512-bogsDO8xosuh/l3PxIvA5AMl3+BnRVAse9sDW/60amzj4MbGS5re4WH5eVEXiuH8G1/3G7QUAX2QNr3Yx8z5rA==", "signatures": [{"sig": "MEUCIQCF5gZayB+80ouCvyedOl4anqQhjAox4mWH6dMxUupMgQIgB1UacRXuHOeqWsfSCjlyuYe1wJOTfbtNkIDtfJVll4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 483830}}, "8.0.3": {"name": "embla-carousel", "version": "8.0.3", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "220ad25a32f04f91f800b04125e6444802f9c886", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.3.tgz", "fileCount": 123, "integrity": "sha512-ThQxBJeAQ8tS1LjBKS5ZoMfRCNXfuK93VrslWnNFmCKG45ir2AniCIMx+bYhaNA8lxwelfPXj1cEiZlHXk7IpQ==", "signatures": [{"sig": "MEUCIQCZvCpsbgNwUulFJbA5ZlhkJFCplg2BRbYtE9LmBYsnbQIgfkZy1hJKOy20ZD1i42VYG1jBxxD1VpsXwciTSVoR0b0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484214}}, "8.0.4": {"name": "embla-carousel", "version": "8.0.4", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "eff9c7e27071911dd4810317348a70aded1619a8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.0.4.tgz", "fileCount": 123, "integrity": "sha512-INndmilrV9KY4Wnb4F2tI55DuQnFjf3GPOaPDT2LGYiKhIWVNUhv1nz/RI7CZ6WoIZ8MYHP4t6Qm/cqpcGHknA==", "signatures": [{"sig": "MEQCIFkrNyjl8xprK+iupVG8qeCx5PLGRv3sMW3BnU27NnxQAiAKIeWkYhzimXH5kxzCy1zIxRRqgTfJTiiYx/JReDa4zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485120}}, "8.1.0": {"name": "embla-carousel", "version": "8.1.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "6cc6b2ab5f3e7b7a6ea55a5bcc727d32551643f0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.0.tgz", "fileCount": 123, "integrity": "sha512-s6i450b1cppstIQB0RQ/eHzBGS57twsM09GiAE04w4QyLYzZkuosxpiAGxK6Cez6BIp0Vd/SCmD7yWliEP6XEw==", "signatures": [{"sig": "MEYCIQDG46AiPAZlwNU3zxwLL4W3yekL/G7ik8mqjK7SYC7cSgIhAPtbHyZd4/4lnJNFywcKVRibQG/lD/QuwFvnsr3kBfrS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486769}}, "8.1.1": {"name": "embla-carousel", "version": "8.1.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "82bd2e07e10fb8c303bf6f1065997b8570913ee9", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.1.tgz", "fileCount": 123, "integrity": "sha512-PfdhNbPoNl7V5YnwuKe2zmKd0+/HY+Q4O59xQcVvinsgv2LkpAVQ1NB/q5STqAde8nZ56U2uQOe6X+9u4IZlhA==", "signatures": [{"sig": "MEUCIQDJ0KCu8GgKqYN5/xsKRIKuzYe8taAQGGC9r4TuXGSY7gIgK6Y0IebQyra3U/JLJn669vf4y2vq7JYPLXT3HzBXsXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486769}}, "8.1.2": {"name": "embla-carousel", "version": "8.1.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "268e96b8acc0937c1fb35da9309b9e8546fac6f1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.2.tgz", "fileCount": 123, "integrity": "sha512-UCjqg223H6xZ0xxosmyV5CDfzdbw31yIgkm3xGGLeRRD5yIbtR2AhWzkk2ntjHK09/v3dKcY8G09OawDV3gbew==", "signatures": [{"sig": "MEQCIEqaY9Dm4nD4oerSv+zp3rDC9H3kDhPzPEuB8BxOzqAOAiBlS8GNGTKS87vA6/QYcTjX/nIrdoopLhVvETR1iCkQDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486769}}, "8.1.3": {"name": "embla-carousel", "version": "8.1.3", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "d086738ee529e59815dae4e2ecd9ee37c47ffc1a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.3.tgz", "fileCount": 123, "integrity": "sha512-GiRpKtzidV3v50oVMly8S+D7iE1r96ttt7fSlvtyKHoSkzrAnVcu8fX3c4j8Ol2hZSQlVfDqDIqdrFPs0u5TWQ==", "signatures": [{"sig": "MEUCIQCtBF16JbIM/97GsS9BVimaFpfnosJ/W8sT4LuYBByzwwIgZG0SvUc69WAJxOGO3q7zioXwxXvIyGWCbIYypVlB94A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 486769}}, "8.1.4": {"name": "embla-carousel", "version": "8.1.4", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "17a38a2dc20dfff40f2b949fa7e7f61312aea949", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.4.tgz", "fileCount": 123, "integrity": "sha512-AMe40ZpyLpztfNnGQtMWVIgYdcBoY2MuQttRwQ/xqSPC3UvDG2QF17CR5Efz4c8qEkno7pxdZFDRO2R11y6hRQ==", "signatures": [{"sig": "MEYCIQCOAN+dVoqRay3qBvEs4E7JHDoMELw2n+6QdUg2xaWNbQIhALj8OO1yhkE6n1NmlbHjge2rcTXqzJsMdTSXSG9/qXEM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 489242}}, "8.1.5": {"name": "embla-carousel", "version": "8.1.5", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "d958083cb16f19e7412bf8f93fa125b21311ac93", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.5.tgz", "fileCount": 123, "integrity": "sha512-R6xTf7cNdR2UTNM6/yUPZlJFRmZSogMiRjJ5vXHO65II5MoUlrVYUAP0fHQei/py82Vf15lj+WI+QdhnzBxA2g==", "signatures": [{"sig": "MEQCH1jbfTzYXXA7B5ktWodcTYbW3ty2QLl03rqeINRiLz4CIQCado8iPyU1aZHFgtw9WW/eEWm0uyZb3BaYzmBc60rLNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 489647}}, "8.1.6": {"name": "embla-carousel", "version": "8.1.6", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "a67f0b51f0cb51131299c47dff18e2d08165d44a", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.6.tgz", "fileCount": 123, "integrity": "sha512-9n7FVsbPAs1KD+JmO84DnEDOZMXPBQbLujjMQqvsBRN2CDWwgZ9hRSNapztdPnyJfzOIxowGmj0BUQ8ACYAPkA==", "signatures": [{"sig": "MEQCIBY16OFMis2aE/7yBMZvUEA5yvVCzh5rq8LD8kFL2XaoAiB2GDGdF1uYmiWsqsDvGi2fbmslKQeOj9H+dLn1SQg/hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 482602}}, "8.1.7": {"name": "embla-carousel", "version": "8.1.7", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "cc98a86f80b5e3add2d254b1a84903607425a6e9", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.7.tgz", "fileCount": 123, "integrity": "sha512-b3kBr2H+S1gx4neki0P+aqN6cA5Ibjqy4CR3Ufi3X+Q3JpoNXJgOmJMSPkoP9DKcDREwADN6UWZzRwF2oo0y9Q==", "signatures": [{"sig": "MEQCIB3UjjZnkbkhc4j3f9n4XJmY1uH0ELegNQmabPDhonxvAiANo1Hk4hcKCOGSspthQKTnJ7nPGKdvpGvW4DhNuAnTQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492895}}, "8.1.8": {"name": "embla-carousel", "version": "8.1.8", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "cb4e02a1467909d8d59aba2063ab8e2e6262b68c", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.1.8.tgz", "fileCount": 123, "integrity": "sha512-KuHPA8qcAts6YE6ELtt38XYAb26hnKw8Ga0lSXmrhm1oI97t6oACFkqSsy33dfeZQEhaZB6VwWvaWQJRJVgSgA==", "signatures": [{"sig": "MEQCIFMABJz+5N0pQh/Srcn95eepRYWVEqFVMtZ4LW2mmOd/AiBIMhAwe4STncyfVUGlidc8giZo3Vp11Bs5RyCrp66PTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493039}}, "8.2.0": {"name": "embla-carousel", "version": "8.2.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "b6cee975d4f43f127a77787673477bf618df7610", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.2.0.tgz", "fileCount": 123, "integrity": "sha512-rf2GIX8rab9E6ZZN0Uhz05746qu2KrDje9IfFyHzjwxLwhvGjUt6y9+uaY1Sf+B0OPSa3sgas7BE2hWZCtopTA==", "signatures": [{"sig": "MEUCIB4GMQuIClIZ4P3X/1z8QpsjxEFy0pLaHXswUX/vZoHkAiEAxFeCH1Sxxwx1Pzw/Wc6rOjsl0SfsLX86u8HB8DyMg4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497044}}, "8.2.1": {"name": "embla-carousel", "version": "8.2.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "d156be420f47d9f61f444eb789c9901cce43f7f8", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.2.1.tgz", "fileCount": 123, "integrity": "sha512-9mTDtyMZJhFuuW5pixhTT4iLiJB1l3dH3IpXUKCsgLlRlHCiySf/wLKy5xIAzmxIsokcQ50xea8wi7BCt0+Rxg==", "signatures": [{"sig": "MEUCIQCFd9QmSB9wV9fwO9DIlihFx8Ao0YZ9ihPAnczsVT5AMwIgaPyIfp7VMb2CF/8t5VWwMvUGopRfqO7Uyni3FD5MIeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497347}}, "8.3.0": {"name": "embla-carousel", "version": "8.3.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "dc27c63c405aa98320cb36893e4be2fbdc787ee1", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.3.0.tgz", "fileCount": 123, "integrity": "sha512-Ve8dhI4w28qBqR8J+aMtv7rLK89r1ZA5HocwFz6uMB/i5EiC7bGI7y+AM80yAVUJw3qqaZYK7clmZMUR8kM3UA==", "signatures": [{"sig": "MEUCIAlUShub+8wDz8HsWF98+TVp0R0ftz8Sv6njPXK4dlbSAiEAjzvfy52T0Ur9xfR/8ByMQBJE7XR4Ksnv6YkqdspWmLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 497555}}, "8.3.1": {"name": "embla-carousel", "version": "8.3.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "8f5dcd0dc001977efd2bca54d620b6e6736a1a85", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.3.1.tgz", "fileCount": 123, "integrity": "sha512-DutFjtEO586XptDn4cwvBJwsR/8fMa4jUk5Jk2g+/elKgu8mdn0Z2sx33g4JskvbLc1/6P8Xg4QlfELGJFcP5A==", "signatures": [{"sig": "MEYCIQCLP962GTQHUy6WRZd0/i8vtxvQDwRBKVwlQtVo8OT3PwIhAMyjoT061rE9IRLn2hFP6mojWBTxEHGXuUArOHp8sCFU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487285}}, "8.4.0": {"name": "embla-carousel", "version": "8.4.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "18bb23d2815e12e5c9602f1f56658931eef68d82", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.4.0.tgz", "fileCount": 123, "integrity": "sha512-sUzm4DGGsdZCom7LEO38Uu6C7oQoFfPorKDf/f7j2EeRCMhHSOt3CvF+pHCaI6N+x5Y8/tfLueJ0WZlgUREnew==", "signatures": [{"sig": "MEUCIQDUYv5xtt9bpRZGboiB4QjrTsZ+ztywwRFwjXsG4eKnmgIgNfbrOv1xX8ppn4WZg4aFztcDfIu4o4h+tXDvatplL8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 490672}}, "8.5.0": {"name": "embla-carousel", "version": "8.5.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "1775a72c4ab79dea392e6783f6daec6bb876ddc0", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.0.tgz", "fileCount": 123, "integrity": "sha512-4cZ+50mAfrQySxp1C/0GSRZYSIHB0Osr+1S0SugY6Io2rfSsZizhj+A/HdUUJ1ZQroMNFqRIKw8ZRIe+0MFlXg==", "signatures": [{"sig": "MEQCIFX8UptihdcpwcIwz+EEeqaVmIArEtH0Sg4N76aWzpqIAiBdfZDKEn6s0AdJMh3sFotmacSv+fZ8VCMK7pqs4TmIwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 490672}}, "8.5.1": {"name": "embla-carousel", "version": "8.5.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "8d83217e831666f6df573b0d3727ff0ae9208002", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.1.tgz", "fileCount": 123, "integrity": "sha512-JUb5+FOHobSiWQ2EJNaueCNT/cQU9L6XWBbWmorWPQT9bkbk+fhsuLr8wWrzXKagO3oWszBO7MSx+GfaRk4E6A==", "signatures": [{"sig": "MEYCIQDcb/TA8RDHejkuL7OXUvK/ApvNxv6Q5AaGav5UC4UBuwIhAK38oWm7JTe+xb9tk19BpqXS6XO3Wj+VRRJ2MZtA/Lvj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488639}}, "8.5.2": {"name": "embla-carousel", "version": "8.5.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "dist": {"shasum": "95eb936d14a1b9a67b9207a0fde1f25259a5d692", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.5.2.tgz", "fileCount": 123, "integrity": "sha512-xQ9oVLrun/eCG/7ru3R+I5bJ7shsD8fFwLEY7yPe27/+fDHCNj0OT5EoG5ZbFyOxOcG6yTwW8oTz/dWyFnyGpg==", "signatures": [{"sig": "MEUCIETID/N2tQKsLGT1CeF2/yhLy2B1FX0M7bN5prP3Y52IAiEAlMUSAwvDTc4RLfsbJKrPGgaMBSj82Ikh4PArQrmmecU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 489161}}, "8.6.0": {"name": "embla-carousel", "version": "8.6.0", "devDependencies": {"@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "2.8.8", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "dist": {"shasum": "abcedff2bff36992ea8ac27cd30080ca5b6a3f58", "integrity": "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA==", "tarball": "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.6.0.tgz", "fileCount": 123, "unpackedSize": 489743, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICWr2oM14f0T5nVEeHCucRR7PAIgr9Cb4VkLDNUwGHbeAiEAqQRVY1GRgRF0/FQLj8lFpmMs48P2GU7BDY4IAEYA43U="}]}}}, "modified": "2025-04-04T17:37:39.164Z", "cachedAt": 1747660589684}