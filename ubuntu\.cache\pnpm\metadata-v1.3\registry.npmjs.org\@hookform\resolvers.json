{"name": "@hookform/resolvers", "dist-tags": {"next": "1.0.0-rc.2", "beta": "2.0.0-beta.17", "release-1.x": "1.3.8", "latest": "5.0.1"}, "versions": {"0.0.1": {"name": "@hookform/resolvers", "version": "0.0.1", "dependencies": {"react-hook-form": "6.0.0-beta.2"}, "devDependencies": {"yup": "^0.28.5", "jest": "^25.1.0", "react": "^16.13.1", "eslint": "^6.8.0", "rollup": "^1.31.0", "ts-jest": "^25.2.0", "prettier": "^1.19.1", "@hapi/joi": "^17.1.1", "@types/yup": "^0.28.0", "typescript": "^3.7.5", "@types/jest": "^25.1.2", "superstruct": "^0.8.3", "@types/hapi__joi": "^17.1.0", "@typescript-eslint/parser": "^2.18.0", "rollup-plugin-typescript2": "^0.25.3", "@typescript-eslint/eslint-plugin": "^2.18.0"}, "dist": {"shasum": "e7a31bd8e06514de6f061b8a1587770201bc07f0", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.1.tgz", "fileCount": 13, "integrity": "sha512-+RI0k4S5Kzx2QdP82lbglYDVCjpVEqepAafGkqeP2R1+3J/5fTPLLw6u+r9dSvqhpQMY4tQd/40MR9a/dlMtxg==", "signatures": [{"sig": "MEUCIQDgCsmHMycGxR4WOb32avdFCPBTC09tm7nroV5f5zy9AAIgU/dpT7tXDm+D3xpTaXVAVYlNGDtJXK8vb/D7Tyx+asI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexRWQCRA9TVsSAnZWagAAtdoP/Rqk5leY4ytBxChhGAb4\nPqzMgxwaHrOMYq2Y6eRsBwQEvdj6fcpczXFbR3b+SXOHmCw9pQSOC8t4g+Zt\n8cnybEqUN99i7fjy9CzGbjB3Cn4ZheZ8qbQAYc22ZCE0oqoz4dztjq/uoA4s\nLjZiD7IfV/URl3MntqYnG0xYwkqhZszZKUPtbH9fCk4qg4PdW140Tpt270wV\nQzIfrojlV89SiDksK19pF+OOjyJ+q9AdcpicjBMU2sOIS3/A288gJI61Oh9f\nn1QM1BkwiLwx3IupckGoVgEEegq7P31jDofAI7fHBou6J4iwYds34+s8YW/K\nGT9DviHmPRHdjRNQhIsHeXRUV7Hisl/LUpNIPxknyC/rao2hBv1vG3bx/16g\nE+b113/PUyKiWLWz+uKvXRZIH10SsDD3s+Q4BYB9kKJLUL7i/kc3DaJjhbO1\n8/fMOIzMQszy9k3mHFUQHvf+qkUt/xTDKUUKRM9/anGLx1fHMISGRv7X+04f\nlWmzMSexfgENPab4L5zvJwp1UMGhEuoPlYnKaZES9OzYKVxabVLgXD4xF6uP\n6hoXo+QTC1tOOEp0KATnhynVxngE12m3uh/SvxpgAG3aA8/YmX08C2LZcazI\ntTmyILFd66pC0OVGh6yO7wz4dsSTHsPmMadlwm4a6Kchqyfjpd33YIJywNPT\nm2h/\r\n=pqrb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@hookform/resolvers", "version": "0.0.2", "dependencies": {"react-hook-form": "6.0.0-beta.2"}, "devDependencies": {"yup": "^0.28.5", "jest": "^25.1.0", "react": "^16.13.1", "eslint": "^6.8.0", "rollup": "^1.31.0", "ts-jest": "^25.2.0", "prettier": "^1.19.1", "@hapi/joi": "^17.1.1", "@types/yup": "^0.28.0", "typescript": "^3.7.5", "@types/jest": "^25.1.2", "superstruct": "^0.8.3", "@types/hapi__joi": "^17.1.0", "@typescript-eslint/parser": "^2.18.0", "rollup-plugin-typescript2": "^0.25.3", "@typescript-eslint/eslint-plugin": "^2.18.0"}, "dist": {"shasum": "3e6730f6e84d8d6ec9113524d9ad926ae152ca65", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.2.tgz", "fileCount": 13, "integrity": "sha512-YYN8F8H4GpBQOiDljO80dKUyQz6g8Uhkk7rxZgoOL3nSaehgqeXdeJy3Kvu59Qvz5SlQxz+qvlResPdWswAc4w==", "signatures": [{"sig": "MEUCIAI1a9Ji19FS/rYQ8tZzr92c6nZ6ivePTbrDioUq5GiiAiEA1pb3EGu84zBY869Sp1cdBiFVZ7rPXKOW63x9pO/daNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexktNCRA9TVsSAnZWagAABc4QAIpfDbVcrKsJFqbzPt/M\nKkMD6NP+5VLRd3WTciwdwZH2oe3R0x8q9VT6zftpPaD+g2q6olT44HyD1ws/\nUnvoJFI2K2v/MZftF8Lb53oqvRZc2RIW1qwD9oEUaoSy8T2u/ZG/Bi478fln\nZrYhzx1Zut+yjKNtIGEB+qrnvXfiq1EN2V4/UoP+/5HeqzhK3RSfigBrdNdP\nkWNwGNkS0iVo29WvP2FmCD7xhxB//40krOxSLgk11mPPUWJzQ+NhCsjf2CHd\nzsQz6p64wl5T753CA86AZp1BNL1WjjBwenMrM4GvpEZ8w4iSG1ljOXFE5dh8\nA402NBN6bR+nThOfu9HTBq688Uy+BE1PQoYppB2vMuwxH9Nbf+u0vK/5tFth\nW51VLI2mCLyzxWZKHTKhdZ2LEhuToOoEu1/hSGrKMTkMGpZdAjVOA/zWs50E\nRwzh5nCGpdvvL9+4200P8KXeGbphu8CR0WJV/Fs/Wfw4n2BWhKi8Mu3TKTpn\nB5c2+hQ5mv11zPkE1SrZgKjEqzHtusPM58ms4yGNXh5vGfdfQzPgx8s82ylR\nA6OghCg/X5b81VJJgrSK/oabDmHSME9OkaVCi6avxuwEqzMXOXhi+5gHDA27\ncjIOte8qgIBdFpCKnmAeJN0ggGH8rDx40C8i4wsNnKQ0aaWIFkMGAIJqxB01\nW4Zk\r\n=k6ov\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@hookform/resolvers", "version": "0.0.3", "dependencies": {"react-hook-form": "6.0.0-beta.2"}, "devDependencies": {"yup": "^0.28.5", "jest": "^25.1.0", "react": "^16.13.1", "eslint": "^6.8.0", "rollup": "^1.31.0", "ts-jest": "^25.2.0", "prettier": "^1.19.1", "@hapi/joi": "^17.1.1", "@types/yup": "^0.28.0", "typescript": "^3.7.5", "@types/jest": "^25.1.2", "superstruct": "^0.8.3", "@types/hapi__joi": "^17.1.0", "@typescript-eslint/parser": "^2.18.0", "rollup-plugin-typescript2": "^0.25.3", "@typescript-eslint/eslint-plugin": "^2.18.0"}, "dist": {"shasum": "d2a0b093296c2173a1aab4e3ade0ef5d1d756077", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.3.tgz", "fileCount": 13, "integrity": "sha512-+mnEJanqZje7/rxlYcHwSQkG/fAeJSpSnNE2aLFY8CVTY3PgOK0sNhwsTggyGly25o9HPPF0rW/NcZEEhbaScg==", "signatures": [{"sig": "MEUCIQDpGIeuRnzaz1R0ZIehu0zlWdbXhhsqsmH2GLLtwNP4ZQIgD5EuyBXuPBDttNF06YS80cHvQ/UjEAGz3pxHUykq86k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJexkzOCRA9TVsSAnZWagAABkwP/2tfOC3QXkgdPgGalXlb\n5pns2wBNOHvMn+OCjZ1ncwUdFruTwbS/Om0Mq2RqasAWwLhri7lTOPp7uK8J\naJRWO/yZlF6nbbLRav0ryVUHOK65QsyTUW7IJo2E/KMMezsvW04K0KHX/1f1\nF4sbSwXDX6NHywHRc43GaZqjGlgFMxfYbI7gekc6P+2G6wD4DQ2TF8MHyMkD\ntdz7XwozxdXed07+Ihwxmk2xNaHXLcIN+wHQBPjwt6mTSWiTUWLK7FDqAaIa\nPl+IsL6V1PzozNJsFfRmNpdguSMnb8/Td/84dD0poEUkhyW5Zo9BDOTNrQUA\nAFSnsJcK/2QgUzu3Mo2KhdvJ/FP2+OUBAXLkbp26RXCahQWhXZjPC7Cn/wCC\nVWA60GiYezdfmaNeQOa0klxfRjEryr39cx34MHAGcZleiP0O1h81MRzdobnX\nkc2yl1H9q22iV0yZAegyDPszPVSLY1kVUbm7xeyvmIfr5TAskixl1TliG+3k\nWyUQ1RwFFDi7WHxyFNW92/E1SJwRyzIorHgq913yN+nCuiZXWMmkifuLzuCX\ngOcg6/4v1l0osStkvX6FMvRLWTIVFtaqHJO0HMWtPCfE0oYsOPud4aUai2fe\nvGckTghiazPklpdzghS4lz4rGiQ+WsG0RHdt2UsyRCNGHS0OXsb8nOJyW2Ki\nKbeG\r\n=mKtn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@hookform/resolvers", "version": "0.0.4", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "f25ae9c21d5f6ecdb228ca697d9c18e6d837fa04", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.4.tgz", "fileCount": 17, "integrity": "sha512-ApManpSvy37gblANkb1vAukdQWvEu0uN3+8KUGq77KG7DZjKq4ttfpBja30ABJ2gZ1QxipPZMd91abnquckPMg==", "signatures": [{"sig": "MEUCIAMnTcGnMPjU9nuwBzolvWx5OjUZP5M2QTEPPWmheMFEAiEAgdpMlun9h65auKReuD4CpWZPVKCyZze86zr33VhS5dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe5zXhCRA9TVsSAnZWagAA/z4P/RYp4gAvdlnkZCP2O7a/\nzw+Z7qgGhMDPnhOCttBcqUZZt2/i1a6Y84+pggp5Pyutouw2qX7yaoNV2D0B\nReyo3AALElNtlTcbLr5k9SApTm0Kf74yLBX5MQmOFm8c1t5OU8JBIJTpBZHX\nMHaxzZ4F5q1V6j4JyJfAzfhJ/MO8UzTcPYBRwp6VnARZr+0ALs9cnpCC6DE/\ncrfSzhHA3/lHBWmlKzEfCMUI8VBeikyCE7os3jW9wg1GLxtJo2qO+bNVmkAi\nQbwP4cGoLBWUdLptkuM0eIakb2jls1vh6aOkvi7R3Gbfbu0r06Bv+5qvISNU\ni8s7J+ye/EZQhodN5kGYHkiGRLNny0Sq9NedaDRnUEpE4P4Gh7PMY4WXT0Oy\nhrj/+wN44UJljbOcmMyfdqsz4eL+/uOyRJPHvjIs8ApF1HjzPa+lMVeOGWdj\nwiujc9sgkkRGS2CHZr15hJEBFWObCpgC72F+m6c+VCv/RB2wO+TOJgZhuqoL\nfv8Jnb/93DBp4DOB2pGFju1U/QJOJ3Slv5hFQj9AHpTVDFZOMMLA3s5/ig1V\nV6Mpua11lbKUSXt8A0Vw0cIUG2CxgMZ/nAEFocm29CP1P4Vz6Y/v2U8JIppe\n4V5y1VMsucYmKK2GQsY6msbk2d8EkeSwg51deZGa/B8LextbR6BNCtmJh7D5\nIIzr\r\n=vr6u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@hookform/resolvers", "version": "0.0.5", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "eddf423734c35b1b02a048528be80a64f67781ab", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.5.tgz", "fileCount": 17, "integrity": "sha512-EDp4nclPiE4slWS/v9Q/Ad9ETdqC4zLivDOkj+PCvsXc2mpe9YlkXYM4N3iR9cdcSKA34EG1bTq4+5JG8GaYAQ==", "signatures": [{"sig": "MEYCIQDHrOSJ8O8I14yTlDKl/C1Pgceq74Ku8nGUE2379z5JzAIhAMQa4IcepChGjVa4VVbeNS0cWmtUbpfA2NRQ1V7aPyxX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6DnDCRA9TVsSAnZWagAADX4P/RNFE+auALZJiWHqZW48\nAh5JxJ6InYfjZKQy7hb851gsWMR13oSDet8UjqV5A4gaoGegjajP/AdaZ3tI\nwSU5xidQ0vira9uJGfo3oUl8ugRNi9kJyRZBY5IxTcjQCXdttJF7xoWE7Vs+\nzXyDLg8YIYABEklC5B/6THR90x1BdiF4c6xgphL1WzziJ0nTyL5EEhNKLuFp\nc2ThYGzKZgBz41spe2JvzZDFac7mK/KmkU2lSoWUoykSq0SO5+Knr53NtmuW\nhGlkTJSwaRs3V9p3CqM8YJnqi0poZnH5j66Cr1QiEta++Et+VmY+pUZADbTf\nZs4+PnlKqFm8EF7ftHoC4doF597WsESwC0jHLbGUaBWqrVpiGmE6Acq3YdAw\nlEJ1fIPknLIVCQjLA5ltR748epFQofXng9dRZMESPG7vMdwNBNQn6ZvWnoMe\npDQVnU+bbgLB7mh58Yom9NegxeTCAYy9S2CTQ3uj0uFN6we/HUZT0Jn+tmhB\n33TfFdiLChv/G4dgWJO/JufYfef0gvhG/PFm1S4RH9je1pZna1xxEFicMcs+\n13nQ0yK8N3VqzE09Vg+rmeDvCuDmKQqYr8iXNcVqmYhD4olT7ICkNEZNZlIQ\nFimQBTZG+yrGt31Zynut7BKhDB6t7hc0PVgHCK0ZSd7lC1rpy4hFzeKGeyOA\nc9jl\r\n=IhLI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@hookform/resolvers", "version": "0.0.6", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "f3e1d3a53861e82d5405ae08dd1879fba9ac9706", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.6.tgz", "fileCount": 13, "integrity": "sha512-20ebJH7iHoGFxgQZkSepe3cx9UXMOwp8WHjyq2VbvPx+GfNktuCCH4g9WO/KQ0RZCGYHgTdbHZERfCfNNp5aow==", "signatures": [{"sig": "MEUCIQCP4f8rM41vE1a8JychpIGAM/0ICLaji/Lm89keCcM91wIgPCxn33IsiN1AjL9/Q4jqk38vqarZGGtkxKTSyrXFD80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBxB+CRA9TVsSAnZWagAAQ4gQAKGygSLteYUtg7WXUaM3\nc0iYRX/y+Yo44Ssmssgge0UH93W6VLMlKbCV/5pgHwLvtdO8TsIDAF1dAd5W\ncIx4yw9KqAuu7fJ7YK8yEbUvJWiZYTrJTyFBknLVr5AbnNlg8Pz0P05x6wCp\nhspngDBN8NqYmei8+OKJroysxn56avfQODnjyykkUY4NLiPYKptmYH1cYTms\nwsSy/i16On2tr7vkyLFBvfCdShrF4pauTEEPA3Hgp6uDe09onN1N7YoBZo5F\nPyWBGo6jIP3gL85I8s2UVaZxyTvc/xlj07hN0Ebc8KHsZOSsJYHaTzV8Jukj\ntmbks4hrgFinhrBjSyrHsijyV9HgwRGgN3WCe6+WD/P6zM++tJOQ1zMQxElT\n3BQTBq3P6qMWY4y8ZRriCPRn32k0tF2FZs31Xopx1lChNmMB+MVEGUDuAMJ8\nZRfQneWEL+td8DUT3rymU3Cqb3BWQkUwt8BZTGQhvMmrnKqaOOttfq4pgwdl\nSWbmHS0rXNd+nOnIgHBv9FzFwurPNyO2bR4UMMnpNEqcFukxgV8GhtMJHCbL\nHODS8+2sSh4Pop2lCYsuqsl3Jtlo+B5dCyWTUqAaKz9bsPmRxJoFgyEHuBhe\nbEZHpHfFPpsNwnIWdHdZo/qz8Ii/1GAViTrc2Y2XEpUEup7O32Qusy6ZrZJD\n8ti2\r\n=1CMm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@hookform/resolvers", "version": "0.1.0", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "30f5cf17a3a5590ca90dcfa834492e515e17d0a5", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.1.0.tgz", "fileCount": 18, "integrity": "sha512-/6cnDycgWhmvUpRmYY7506qJlym+hirk4tbt2waY3C2rOjFn+4/Gt4CuVLzjrsNJvOJkN36bx+OSlccecBgsrg==", "signatures": [{"sig": "MEUCIHTSW3xkPPWTn7G+9m/YXh+bI3UN99s/FZOXUShKYHjkAiEAtv+Yezm6Upe8o2aOIqIVg2vqURKtOUh9AVA8POJhwVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGhPpCRA9TVsSAnZWagAApuQP/icMBO7tOAna3OCtu3VT\nxG3B1Y7iSdxrOnOTiJtxXgq8i3pqMOxldlws/lempa7lsutt1iPajjCYNHSM\nticTEP9VPiOIFryXwnYHbziBqF0YUtS50ZQjjKVkJfpRpYZqvGa76JjPnLhY\n2NyH4o4x17GvEzRG1P/DoF9nkkJa4hOW0ngWSZyERObB0xcj4kH+4Y3kRKgR\nNS1m0ZoSo05KoxntpiuPYXZzD/YUWOmgC4gMKq4bSfIA4kPc+8ZabgqrjVju\nEW9jMy8PGZjMQtm53sTGl/itVGBkiKiu+VplS0CdReoS5In7oFCIqlf1+Z/Y\n2PGXF8CRy0Gvwp0xtRPW1MhXRk27O5vnWkM+UfVL9ILOVT33eyxpj97q82YH\nCYfJy3dFfp2hkdGM8FvOgF4Zk//6CZWTv1LHzL8VKDwDH1BJhImqr3db4xx4\nyjLHNgphaHc29aZm9wKdFD4Oo6EJlmMP+IKYRxbuJ/hhxwnix2o7mlH9VWqX\n7BxAFRrG/XiAe2xtBy2WDd6zJdzfmpYVDtJGULkzOjfphPT75qwN0tpkBS2a\nY4Z9Ps6I1mLxaP1JDfX26csHu8JehiDEJzAOWimxp38gdEGGudTu+1F3uqnx\npB4gVEuS9zw/RqUEOcCiBL7T7Y6rKXHHRvmmF293gkMDDGIC2PPau8YNScHc\n2pyL\r\n=bFWH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-beta.1": {"name": "@hookform/resolvers", "version": "0.0.7-beta.1", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "a5d66381c2ca471f5c82ab96347ab899e53cee2e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.7-beta.1.tgz", "fileCount": 18, "integrity": "sha512-2Gb7i3Hnpa6OL0KFZz6yfahIWfnLCvXI1TGknT3iPFYJIY+XIRs7bsK2yGhqXI0oEiz6+dLjah6CXwXZsbVKjQ==", "signatures": [{"sig": "MEUCIQD+xXsY1dpMVv5TS/k/gnED/txGkBUAR0xbjVi1dJiC5wIgDjc7yjTzeAWujzG4GrK0Q9T8h5aUzPDoYyvoavtl/vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM2KgCRA9TVsSAnZWagAAz1wP/RwfqT/08RDh1QPTfNoe\nLEMxl8TFR8emZGm7GqQ5afFA2jLUvs2Ns+dxPyvDf/Cvqu89AqKnra+Jb+Go\nQBy39KKyvM4Qp0rsw2ER+wMtRK24s2QlRpFTSAUCsgQ0Cqp6FpGVZe2XawVM\ntp2vQL7J58MDF0BGVqbX43xoNhNRaKYusi26rM6b1K6VfjIlE60K8Qg0HPho\nfRskNgnY11BSdF0tRGuqlzzTAlyVq6WiXSc8AQni+IUL0my0UsC+F/mKxsim\nT9DMVvPWz01ctR6H2cHZlvruESLqsYQua2m+AVSVOskkzRAKd96rqzjZmIJi\nr7Xs9Nzjcxwf5LEmiWd/ZUStpJiM5VyCaW6tH1yyJAaf7DGz/4lYHaW2PIZz\nXSOQQylbq0SsAw2yVvG6dDF7SoX+nnLtaA9MmqhEnfYbtH9mPbJI1/cMvikh\ncDM6WRynYN0y1qMD8g3Qcza6PAUcpj/Fkl+kSWoSEx9BmlEtBan4xHTfgK9T\nIaL+cm74Dg7hIbsUCIDUvg+xZSpant/UtG+w2DGXwQ0VhL7TfiS4AQ4DIuoU\nz+7EoJFvV+FrtXOidEo7DR1b7cEm+Hv0onCwmoHg8jS/XmQsCNtNYl5tcKSc\neu1nzeRvQdnZDkkwyEDZRgDWovFCDyPv3ZfyZCe2NZ6bOeozBG8jHI8TO7+8\nTmDi\r\n=skVf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-beta.2": {"name": "@hookform/resolvers", "version": "0.0.7-beta.2", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "0c60b281658481bd7f38fff59cbe7af811020f38", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.7-beta.2.tgz", "fileCount": 18, "integrity": "sha512-Ei0CkbyzENINfGjPxykL7c++8ci1F8jL4uklTIOxtx3ZH3IlGqGQgjHCvToyC8JLVBISS2NPODynrCtep25SFA==", "signatures": [{"sig": "MEYCIQCnfZFoepGmPEmPt+yN/ctghbEDAB/jQATxd3ZnPXoz7QIhAK9UkZjZ17zOnshX1IOPi0tpJw9CF8yTcJhWiqptEkiz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPblICRA9TVsSAnZWagAASvgQAIMpgOU6Ee7EN2hAjkG2\nVOmdpDpSdbSUvPm0mvp4d0NK1m+8YliV/9W9kj0TAWt83p6cKu35Pmg8tX7C\nne/ITL23S/GymZXhhYJ9bIv0o4sDb3Wud45/rHkf62Wq8KFkO6DAxnOQO/dX\novm41KY5HHoINhXMDIYM+fEa7I9BezKRzNF8S0MFO5gQhyXes9hHvGc2Nsum\nmmJYlt3S9yNnHg2Z1Qlta3IIUyHhLGMz76aEJcj4cmmSDIM+Ug8gWh6s1ITG\n0VgkDggyI5FTwgeeXywU2b5g81KwS2hTlPQQ7X+41BFiPQZVA0kqH6lNlcRS\nzp5xVy/S7tjFUhtEaNKuLU0YbWYIlRWeEkTaxnMnObcf95EcoXabgZiUPk8C\nH23We3gLozA7hA+vIBXp26BB0KR1kPhTqy8Dan3vlukQZ5dDGG2ay4nFcM4o\neAix7Y1JoT5cOprYL+9Yi5qchrQyZjIp6xEEKJopZFTL8dL6xxwAGR4v3TAx\n+rfPItXdzTNbZIKuhBSzuCUawutZiUluIUtBbwqnQEX03xPgnUiix1vbVsd2\n+n81Ot8yf3WUyHCUw+pWrz3ZKz+Oumzxu2vzWciOWS9PDOqp6C7A9UUpapDq\njzZC7BQvgcuoyevS10W1IKSysoXhbyLxTU7wnbIn201MvcYYsfahQ5jyXtL/\n0gSe\r\n=fgV/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-beta.3": {"name": "@hookform/resolvers", "version": "0.0.7-beta.3", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/joi": "^14.3.4", "@types/yup": "^0.29.6", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@types/superstruct": "^0.8.2", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "8a2d9458002a8497d7f7f7ec34f57cfd9acce3ed", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.7-beta.3.tgz", "fileCount": 18, "integrity": "sha512-abMDZAjcO6N0g/Ojirj1nZNjN5ciN2cbNEPwogK2rd2n35m+lLSJdANbOgXPBr384DX3opAyfmM5LfmmHluBzw==", "signatures": [{"sig": "MEUCIDnzRBmk0LUNgTr09EFW59grrnLZoANBShul+A8GiTOCAiEA8HMInoXiwIUf6nG/cEaTbSP/25ypHRFUhcj75NkiBFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77593, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfP7xICRA9TVsSAnZWagAAJ0gQAJQYfe8nTbHk34OIiwos\nToJbjoiwW7+IdMecdplw1mfTKuWPMpPnDp4akK2OKoo5S3XLVviiMNoap2Mi\nY2do52chZAQb/jyhFD4UnMOKGXJTSuhxTqByT971MeNEgExi/PhFfk1ABvkp\na24WWb6y4dqjNUY+0OycCowaI4Mb1OLkrnDP1VkIaNK3vjSbtEfWJ+Eakgp1\nxVY3p/tkDKPYWDnQYRvseAWWQBXE30IPdHgqp/73LXO02oUEQAkzKosIp+vZ\nCdwzUXMZMHHly7eq82MuDyveMAMdJQOsmVIwuqsH0Mu8XthnW6Pg8DX1iC81\nDBtbsMAvb3TjmMavLbr6Lesd8kK3Eq16U+kJx83I4EGijrUGu2C8DO8yIwT9\nS0oBrZc68F+K88zvps+5cymwDqEX8n4qKiWIhQ9cDdXTRpHzCYHfBqGpv0ie\nhtdjIsrQbfwZeNzKBETugU/t3g1JXrKR6K97uIcTK3/+DgqRy2nS6sxuO6Yb\nULFGX3XKWb/yPTaMyMAjgwnuc4QmruPbd54BqZaZMekvWEePcDV661/smSCF\nEYMkHdQxKlCfmTIR8OKLYvJEg1nfGyyULiYMn2ql0v1Nh5ooCSxszV2jjqvc\nMdlgfXmBNQV4RNVcECwxkGCC2hXjsSgb6TF7seTcvKXGtHZ5VrNASNWW611N\nh5Y1\r\n=CAoe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-beta.4": {"name": "@hookform/resolvers", "version": "0.0.7-beta.4", "dependencies": {"@types/joi": "^14.3.4", "@types/yup": "^0.29.6", "@types/superstruct": "^0.8.2"}, "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "66019abda86052fc200b6c469e770eb57bf3ce8e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.7-beta.4.tgz", "fileCount": 18, "integrity": "sha512-DTC8PRJ6vRTQr+DHn37BoyoINsh7UlADVtO8Zdnje8B/Wnmg3r2ByfEO5a5J2K7HltXSZ/Idl2JXK+azAUOgyg==", "signatures": [{"sig": "MEYCIQCFr46a2nziMmbn88Qtqhf506zvslLO4CxYGLjnf8wXVQIhAN/LO0oZt/qmsWUzHj6QRcPu4iBvSDNakQ4ye88fhH71", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfP7yfCRA9TVsSAnZWagAAbloP/Rf9XkLLWCQJE5YKbOGU\nG/TQDa3ScWdAW8YxMdjp3vSdPaWatdptZ4TF59IFh4wQef8KIIh4NoVQKJwY\npS/SWyJ5mVgI1eKeJ/v/K32/GxXYzxozN2hNKsExFDtA2ZHT/0zCH/d3mu3L\nrj3usB5qnKSaKW+TGoGIDRd+grCE0E6/KmBAN4CckK2kiHsnv0hOfifDSlg9\nAa0w4ZiC4czK61lAQ3h/llavdnq5lnNt5UybvVvs1S3un1bSGc2Icp/qONzF\nhsP4BF/QhTrT+cYH8HoPBK4aYuDrFllv2LyflBPbH9xdrx+dL1MZFXLFbikZ\nMYBf1sJZ7iIyPolgcKIn0jgh1NufmMgFAhqMpLp4+IiB52V1zs88f2vwcm6/\nrf+QL3CltbO255AP/qDUyZh0XS6GRxyXMoCNgDOqd+mhH/sLlXReRUnrWGD9\n/PZgb8qce+UHphfolttEjxATd+b5IVo0002B7V0Dre0aG9cDLB8lya1jMJQ5\nG8u0Nfj9ISs8lHpE2Q/1JqEbMIdAnqSNgSK0KCqa52JTEkACJpPhEoITlh98\nQH8BV1ksj+pqA32EoavTAoGsENCJl+d1eDEQvwOAOWnDTpeqvzFSxycydCsN\nAI6w9x2x+y7RzG2x2G1zMBI2WMSEHBBwT2DDB3VSJTGhnjSxJUetGIVFl2pB\nq0gj\r\n=xFkI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7-beta.5": {"name": "@hookform/resolvers", "version": "0.0.7-beta.5", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.0.0-rc.4", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.0.0"}, "dist": {"shasum": "eb41ac48014215a1e62da8c79d590f5c4ee2b736", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.0.7-beta.5.tgz", "fileCount": 20, "integrity": "sha512-/HPqS570LTgrJd4D688nu2QanidiTfRMABg0S6DwpeiAEXhyuRTOu5FqCCcvcNjE6gD/O1Q3jB/VubZSV9pt0Q==", "signatures": [{"sig": "MEYCIQDqpni2tGypjjHwbGXaYBHtQJlSFaZRZ2F79BpT5gPq3wIhAKJ1Jvf4W84emeLwdfms4u84yEdVTGVAYqccxVCLzWfu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSKE4CRA9TVsSAnZWagAAydQP/26G2y4GpKwcubTRLrdj\nRMuHGJqR+USLDl5M7/DlkNEV2ZKTYkuh8Hk9yLhdEW8l9W+l2I9pQoYwDlf/\nxe4X9cXkZAXocC5q8EZqL+4SxOR8C9nGZ73K+GrFTAO9ezkCcTk/Z/ZvJ2/O\nosYbGr1DVRam/LhJtri7EaFI7kL2VxuOVrORugoM650d6MsYWUUsyPThktoZ\nx10s7GQc4iZ3lf+55vIeb9DQL1iJtjKTpfhIOlZlSIOjPg1jjJVbtxMZ0NF4\npmTmDFJoYDtygFhUszBs1DP8Jd24qv6eIfJY1OX8FafbJ21hoOz+WOkLs57g\nV0w0wElpEpqahUFtAEMbQxacWwa44F3oDM1ov+AZKkoO8RAusYYsirj6BjGD\neJ4/jnaxd2XJhgtvU7G2EdFKE1/BYzuODGs2EB2oWnuAkihqii10gui9L8f/\nA0oK3MYhp2EgQmfFSclULx6OLsL2UDxa6JgZo2MHgdIv61nSwsS0KeinVb2w\n87l3OGn/+yTYJUW3IRMCpgBVrp7EljCcP02xQ3oeNXx/o77oDFM06jvs/hln\nHe1CzACWj4hI7nOTa36I4WkMOiP59XTcfbPlolFOF0vSWkttqVhlm7gJL90j\nAUXkSI2wkB19QVsa1KfsZ+oi8HEn/tMYHqhOKGgjfbuv49GIP0eeUZeYOVsj\ndJDB\r\n=IMGW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-beta.1": {"name": "@hookform/resolvers", "version": "0.1.1-beta.1", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "258ce277ced326901c9cb8f161da792e4d730bf9", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.1.1-beta.1.tgz", "fileCount": 20, "integrity": "sha512-7zWbM9SiBK5IMKAB161IUV+V1sfFlSOV4RblB/FIosCMuUpYaeT2iT6bv/NkAWZ2aOOWB+b50IB24T1fMtsBQA==", "signatures": [{"sig": "MEUCIQCoSDlzNjUasDRvMkyuU3MRyaUjKlhqR/m8d4OneVSFvgIgPKdI9aeD+ZlPAtHLGOZFTgGvmitGgDKWbcjm8RHsgrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWtpqCRA9TVsSAnZWagAAUcwP/15ctHDCR0GETgpPB3fa\nvwZIcf8Eia4rQ8yXu6eOZjAdE8c8lwT6ioUD7aNmmhymWsf9651i8t4nb4Yj\nlS61c0CiBmsMyFcESoj6n1/GuQuZAMstLjPhXWlYfqign/mZz6ZKHW0DKyN4\n+lhOSYx3WCdx4stlROE+RE7+wXANZbb29CmLey4YEpZOAy3jzPGIDna2XCCK\nCksJ9sePtfCpZo9tF/rDDdS6hvr6TsCcXXXZLbeZD+YVYdplLz7sXk7fB9Wy\nYREFW7T66QAKqJpoTtAjVWdXw/LkpD7wQvXjYW1Ze5woiK34ck8PhfoePOeC\ndi1svcPwHGY06uRnOY7K1Evqwmziwz417iyEjFUiQdkHUPFwSEO589vSWwnc\nRegt85w8fNuCBdXwIrD+g8FIG0eULCmQQIeROq80Rg9e8ySAh1ExgtaifT6A\nRxIl8c22D5bU++EPQm0zy7u6n7LDVYk3315SxIkCkPRPvIdCnnTuZPU18pWV\nlmwLW0SMulXucpDzpnD+Y1IzkVAL+HI3g8lzQIo+Vx9xVJ0qZtSSFLZvMsf8\nZCs5jDCvXuOmdfYBolmvnw9K8y61Ev7MTiXxBvYDXlWewju/KKz2reCky8NI\nw4siTzjkS1joRxfmS2qHpO9bee6jJkK5tm0RyyyW6Ou8tmryMav5+4XUjo7j\nTPJ8\r\n=L0DM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "@hookform/resolvers", "version": "0.1.1", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "285fa7aa1b7b94fab526c4d39713ed3470558497", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.1.1.tgz", "fileCount": 20, "integrity": "sha512-IKWcvDG82D0N+3ZjMSbdHul6TgL2Dd68aXyijLvc1mUCzBChvQwS6YDsXRrc9WuIjUPb9nluaVIIrmg3V1JMGQ==", "signatures": [{"sig": "MEQCIChI7PveJacCuDq2UZ8ZZbaWXaXeumGp7wzSIVmhnXFcAiBg3ZWwABghgUNl3bMU6AqHGZZgqMZJATc4jPKylxB+ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99516, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfWtuVCRA9TVsSAnZWagAAPcUP/AiVbMtoPKn3iSVgPmS6\nLuA55DDGIB+G3LxraUGvy8cRcHut7CCubjID5P0JPnYsoLqVMNAsoop2IsRl\n17ucXbe0jBtrZsjyC+v79rMQrSYNFM5tOgI/wcjz0nExt752u4jvaj397ziJ\na8joUaA30Dm5DA3q8cV3iv5Q93sVR8Fyv84y0Nb8lXnHp2ty84jQKgdh2fSG\nKEws7zpu6xCNgepPqtx2pDiETVtPd3gw9Ixd1rcpUllmgXxl6kfERTnuOEL/\neCu4EkL/a1s40M9W1Gtpi5daQjtjxbfLVZyjCFRh3Q1Wi08sCCn2NDpsCTSN\nAVgTh2Zbqe2H6nOZdHf0gJu995bu9u/a78sQzgVMnKWS0gYAdZFQDEe+o+6K\nYyIQ359HeqTTx+nusTfhSxywzJ+iSFWdKqrc8K0gzgvCAciIe9ZZFL/Yo0G5\nZ4nt1cHVdVSqSHDq3ByIQm/ZntSSB5d6EYAXqUzu66XPLby+rBTsfugEYyeD\ngpc7tchq0hLWfHL4FtrYl7hIqSXg7ZSRaRGrX29M9+Hrz40Jfy0KF9WXeV+b\nQ+Ve+zqfk4VfQv8CYjUdHUK+9x9cLMbpFEn9CHY0hJiW6iabGWzeRlcj6PmN\nhgUcRaa1nzzK4d4e5zi9gCYcMhRw/VrRqwy/4lv++77AhF8epszn9AJsb7l+\nw9oC\r\n=dUAF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.1": {"name": "@hookform/resolvers", "version": "0.2.0-beta.1", "devDependencies": {"ajv": "^6.12.4", "yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@types/json-schema": "^7.0.6", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "8080b8cc266f16b2428b5bd5311e0596dd1600ab", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.1.tgz", "fileCount": 22, "integrity": "sha512-Ww0omPBzYqVpfgdxiT6pm9WLQJ1NKvoD8oLRGFRbqYNZhg6LUS3Sar1GjniQesmOlwFtwn/6JJ0wEVWQzpgKiA==", "signatures": [{"sig": "MEUCIQDTh7fH0zNGjJXLXQflCcqhgFm3+assnutk1hdDnT51+QIgCORLV9ZXgbkI3BbJ1K614ImWuwDfPNl7SqbTHiaDT+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYU29CRA9TVsSAnZWagAAohAP/jA1LYmXZLdcI8gQvl92\nd0PenpJMOnWpfQqugaURkI0WTUbvtNTl8O/6QUsuNd0Yt0iRahFP4RLMILV2\nC+mNWBp7wETxH4GElIIbvt3OSxIgFy2NHCXRiRymjefauc1PC4n2gNERYpez\n5J8iXIIF8GA/d/sJiF/zGSi1ibmBTIF+/su/cGsmcknkLQwNFVsK7SzzPJiB\nVfKg2CbUwSlcyTr7ykt+tDWy6h8EsGfCZmBd/YtwUBjSQMxpBIXEPDz8DdrE\nCmfUY2LBAM2xoSuFOTeegPflQjZtX0xDqYqRqsD6I7EP2UcZvcDhcA4/JNYa\nwQdMzFAWyvuAur2WrBNyW3SjmcLW01G6xAPZ0ojv7VCz7314N2tZpqSWjJIK\n95Y/WoLFeBf01BexgkUp4V2kmROsoBbizHGsilJPTCiW1J1RHKBzFuq1Wgmg\nlSFQZa9+XzOWxoOmano2Juqcim1HNINchEXbp/vY2TGhfXUJTnMqSHi9xgTb\nfwdHBUv2dy6vXBg53UgmQ2wObVVvZoDXCwkbSI+t/CsnH6Vf7pG9kJ5Zb5Cw\nsbgT1hYv+K/TlCUXuSV/ZVlFw37e1/3MmqqVEFEHjn5nsCzH/U9a1euG2CA+\nNfkm+KJJkEybp0wFG6la3aXsKJ+yT1JKbX7WfYHDtpgjglDyZhup0VIJm5Mg\ndyuX\r\n=r/7q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.2": {"name": "@hookform/resolvers", "version": "0.2.0-beta.2", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "88cbcbb0182ca7d219b184bfaa7ee2889ea37d57", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.2.tgz", "fileCount": 21, "integrity": "sha512-PdQp896Wnf+StS074pG9P3PKvoHNzr5WHo2Md0V3BmhCJPa1lZTw3se7D/8cCjj4Y2K6QxlrUc3wQvr/ntIkYw==", "signatures": [{"sig": "MEQCIHN5Ujm5UT2NxuqMvLH5LNM27oBrx70zak0SVKCTcNpuAiAnvSV5ealLgHW7Gi9yIgwaG0fOgbiA4QmFsYQl5COYqA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZEgzCRA9TVsSAnZWagAAjMMP/Aj1piHE3y9rvgMxP7I7\nWvh95GK1gmbnU+mgg4pJIupqLDP32hPxOhPod3VCQhFh9VNUaz7xK6vl7okC\nnvMKwCzoXFhlzXH6p+oM5w9mrU3eYYD7qP1853hVzGiWVLfWe6J3am2AeJm5\n8oHkR8HaaEf9aQeep2iyeTu5xagLboy9OZvHgUIi1PR5140cxaJZjXdThNMr\n+zkcV6IcE6E8+ciy2ODa9/xCMAS91rOjs/RKiruW6SBjTbhHiBrZEaRhmibL\nNXv8lWFFt5bqV2dTaGwEz/pwHW9naWwhIJvbHPzikDlY+6d0iklzAuz1IMlM\nwVr4B/ZhZpzmPVMZLsxiL8s/J0Xmr2jEmIUmO5oPRlH4sjvMum1MBRj4a4d6\nztKTX4eYhBeoEK3ald4/fLRQv50qJgF9Cva7kFx5HdAVcDePqH/uK9+bObH1\ng7+Ip2X4tXW54kBFuaTEbyS/8Aj6XFTPlKDqS9FybpMDo1Vahh5HE2jNDg2v\nuZGNYTBze19buzEZK/kxD5/cJntg8gPVorNn1rK0Qu5zByutCY+WUK5aG7r6\n6jaMgzsQntBmKacc3lhd6n0sjQ72u63+XIMM6QpjbUoOCIboiA2l1em2L6Sa\nhSdMc4cegCLLgR3zD07wSkCxKtXKWRD0znmY75XGbIgek5t4/onrBTxa5MqU\nBAEw\r\n=XJe6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.3": {"name": "@hookform/resolvers", "version": "0.2.0-beta.3", "devDependencies": {"ajv": "^6.12.4", "yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@types/json-schema": "^7.0.6", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "88b818b3a6be833b9cf1bda88bcfb382af3d27ce", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.3.tgz", "fileCount": 21, "integrity": "sha512-ka+asb/dF41Vt/CsjaFAhGEjiUy/VNjYPijsXv9VlSOPsTyK5mkhXpv0fOQaa/ejgE/fds0Bex46mGqrzztB8w==", "signatures": [{"sig": "MEYCIQDnBnL5tXsUMyVkC4acN3jGmppQvod1Ehl+FZfaGcZhOAIhAL6uDv0fXgC6ytNGll6slxc28XgNdUTHHseUj+RoLaKl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZEjqCRA9TVsSAnZWagAAm4AQAIQUIoWxFS7y+aTDmZoW\n9Dk1rYvO6hbZuSUZfugrFw4u4MF2gcWhG1iBNU+Yw9TkJneSVfCVf1dSEFOj\nejUizxlGTygfBXUT2GLD6Y8wPWekOOFDVWCHT6Ltb4XAupTI6QPRvIlhJRaF\nwp3LfziOOhcZth2Uw7Dk7jzj77jrMJlzIu/felDftYQynkbRR5WoecFNy2XH\nLcIaCCDNaidyBJLvkcEGDPpevke6TphSsfUlU4LB13mZJ1Gib7hADaDgE1l/\naqE3ecmsfHNCo7+Tx2oZo+/5twL24AShKU6lbprQ40A+UeLRH/aQzvNxlO/4\nONm4qX6PSKDzc6+4V5G58BPr5USot6Mnkr+7tTrcBZSsUTBnGxtfDTvL4pGe\nOj9km5u4x5z/3qTcIgWoIQsOwa1Kn06pkAtK6XifMNdswE5grQ/29+wsvV7S\nhBHJ4QmqtSIdz7SwehcC+cDXlSl9VTsxiG2EoRCSXyQQDUe5W9/Zh+TrENOP\n73PtHY3ME6Dl/QjLdajxua44WBf5CZL+jIR64Q315q6OywoyDHRFn7yVzwLi\nIfSos14LJFpZFPcO0Eo7+UpCjR2hyvkfI0LeJ03U7eaodh9c30qTLPs3XZuJ\n86PLhoT5o3IlNRuEdCNy5vOavScMJGJKUIMzQWJbDt/VnTslAvUJ0GvRoCmZ\nXEzC\r\n=WzMa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.4": {"name": "@hookform/resolvers", "version": "0.2.0-beta.4", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "42be54105e7e9fe64053fd24cfc22ad70964eafb", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.4.tgz", "fileCount": 20, "integrity": "sha512-/u72YpWpaI6tQS0jC3X7zSfTOZJDA3O22ihVzGG4QHAqOCZaUx0zZdGhz9PID8AJlsQGEl8APKvMCJtmAK63Zw==", "signatures": [{"sig": "MEUCIQChMUhN9MiRTfHYTQZPQ//oD4GlqCgyVKGDzGRG7y+sCQIgNuiMQxSZjxua2Irh2w1rWwe00GX2tJgomjN20p9rbig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZEmRCRA9TVsSAnZWagAAEG4QAKA037wCJO5/aXyf33xE\nNVc3x1pHHHsivbkRXJY/bq/urHeyr8funqkrJkdYj7bLV6Im/GfPMSexHY3Q\nqYOMfMI0MHRlg95eCmrVTnqdSla56PyUbfLtSAPLGlkpBcCoeZtSK5HBM9RX\nE6lBnhbuSQbA3l67YNDke5R6SPHKiKhfBUCa4bSLPcO7P07RovM28jP0Mj+9\n9Drs2fYMNLKrENNkmiTf+JjTx12Yo12ZY88uGTZVS+9Ri/DQqH1jLU4kVi9R\ncmC6CACfPafFr9spdiIMZaG8OFlDfdrWPoi+v4AzWVkHi4vZlxlWSEjYEcQZ\nntaTD5oRSGSNh9vsTWd2m/LejUT3LHHyq2jOxvOcN/xaUz5c8f6rOgYGvq3e\nUAOu2027NhvEeycPbkA4T/Tg68pSyAUNUhcYvHnCTGg3zSETWfXkRR/b2Yys\nTyQ+G/r8VXYuqq+MCv4CigonngVC6+xQPEWUqYQEwOobWqkRe4tCxDLQqD2d\nfJpA8Ts4HMi+O4PLDMy/6CoJ8UphI6anpzqcHjUV8SIFEmsdVGH0ZvnEM3HE\nA7+KWD1VLfRbUks0U6gtLPD3oEDNqdDE0vpFOrZVXU0QPMF16yhlLfh3e97e\nyoytMDbvocXdRndesCwIo80nAVzR/nuwPBommSsPuzfaLwZb8gy7V74x468D\nKCXC\r\n=HXMP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.5": {"name": "@hookform/resolvers", "version": "0.2.0-beta.5", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "304f125681ecbfdf0cf88caa2519a689c22fdefc", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.5.tgz", "fileCount": 21, "integrity": "sha512-RmJYbS9BBBrctPoMsLM1j9Max0cFPYyMPBUcwDM0xv1FRC5GFRKb2BuijFf4YyqddZmhnhXkDAlSbwzMEELqHw==", "signatures": [{"sig": "MEYCIQDuu/lwKEOM4TXBGPd2I/VZ7FJHiyEUecgKBpXpUX4nEgIhAKDVSy9aLIdySizIrgkjdZueq+UQ/7oQWA3xpP/57cR9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99543, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZEnyCRA9TVsSAnZWagAAwd4P/0zPiu0G/qaFthTiy4O4\n5E5PbkMqmtXX3MGEqiNiWMVi2ekX+sdVA4cuaA2LMaAMz0MGT9+lfL0iXj+n\nulcMduogMCgahfnLMoFOSzhuRxi9J9wGXxjSLLz5+DRSpndE9gxrD/YBhPpv\nt/uNeX1Qs9tykgj5U0cmahExUDuOcZu7h7mkfCvCSf+OAGsAT9eNnVSD9K5w\nNqCI1uXwjtxeezChczRbGNHLYV1oq+vh+77ze3FMcU68RFYjfvVcj9L11oh<PERSON>+yZ3HZAyIul8Z1+J7H1CO9sgbsuYfpPlHATOHEgYCSgQ6Kd7QZoyjf\nrLJEyEj2CaX8V/cEFKCEEtk9yqMEfJh1f6qI20N/lO+6bh+Fh3Xd1ULk7Z3B\nkjosssVPwet+JAosl28QzwQJuwwhly6AAHrHi6pWKgF1XYYF0QwSJdfZBiKg\nAq2k6aNhdqmJBjBufsWnyv4eIPvycG3Cxs3Jv8PWQPdDSeWFQsK2CDciJoeV\nEAN3RTRpVaLXvqwKF+NBhrSzNDoMnB85eNl0dUS8vP4vtdpPJMihLQkEznT/\nf6twx3YUC4Xy0+Usxs8EH9enRWbBKAuFWilwvJYpCLZllXiojQ6v7tjAxYzg\nyHi/31VJLMj/+qqW/q9cFYQg6Lr+GdE8/Vi9x9IT8jG2tndDBTqAWecVJF0p\nJ+JF\r\n=kys1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.6": {"name": "@hookform/resolvers", "version": "0.2.0-beta.6", "devDependencies": {"ajv": "^6.12.4", "yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@types/json-schema": "^7.0.6", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "5b779664272605f0b12c221344df66f31c36f748", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.6.tgz", "fileCount": 22, "integrity": "sha512-6MFVQgrgG9ECUumOkNjCRGqsFeOMv0JDvOjE7JGTVnuJKMenC0f1JA1nzxWxrpGXVkR7NQahzNGU8eDK12xsCA==", "signatures": [{"sig": "MEQCICooZ+dIYWmKeioh4EnhkmJONa7smrBfkpGACcfaBY/oAiBZ2hwWv+obc1JJgiyL/iy7fwaM5bEfmyv95iuYrMWBlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZEygCRA9TVsSAnZWagAAPtkQAKPIel7mhFl38NH4++un\nsaL3hNMgNXTs9dsPm9gq92dBGv9ycPObk3S5LZ7VUMSrzlM/9nog4WHmzJUv\n/WZTTi0ZxLdhKNLUHtwgtR36GgjpU81Fo/vbPKofIkjm4WS1Tc482fdbO2Vr\n888i4WFBdh8KnsxNePqxpDq6UTpsJWNAt3S2LVNacaIJZg6P3XbM3p7LZPWd\nA+UbXFxl7UV4ckOmYDzQKsmUxMfU+OTrLSrrQ94d2BXQlMFJIDUMs/hkwRqW\nAVlzwxeDHrBUZSnoJOe7NR2klX1MHFpBVQvJEaERb9ZPiurA1AHk46QKZABN\ns+mux1pzVHjm36ANdWcFoyvIY+fapK5tr8peC2UwmnmH69iMarIUwS0RiUuw\nDlv276TksuaWbJxICq/QUMGMppYJnw+kyuZl6N/I8YBBSfKcgID7zknJQ2BU\ndL+i+pG/Cv/hp/Fh5E8N5V3tW4YNu1Tt7uOMaFY7nV19z0y8qABev2HWrn0r\nrUqKW1S/QzlQ2JgTP78AZQosmlPlx8tLu2lL9VmIcSC3sgJ9w6GFnehDHvNm\nMGVkeULK4cTOVHIOrpAGJdbfBxjqjIStZH2+MB+EM5XhivszmFs5FuMWBkKW\nZBJv0eJHuujIa5qQ0obDqjXxp1ELgA+fG/ZuwKLTUvUsIivhF1gqZtjpE6c4\nQjxQ\r\n=kLyQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.7": {"name": "@hookform/resolvers", "version": "0.2.0-beta.7", "devDependencies": {"ajv": "^6.12.4", "yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@types/json-schema": "^7.0.6", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "5e4298a22f5f4c6ba711eedc3b9a86280afafd54", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.7.tgz", "fileCount": 22, "integrity": "sha512-+zsWeLTy4Om/saVXa2Xz0eq52JHXU6hccSjnU2HU+soSsEmLcucCz2nC1MYsWkzRbZ3GH3OnBY+3aBZUIfuJZA==", "signatures": [{"sig": "MEUCIQCXxGlYlCfPeQFRciUo0Dc4Ipv29SYA9nW68/x4XZ658wIgDpEnkcF5r6J56sCp6zr3uqfbFxBqs4+VIBa4nzbSBmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1634505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZE5ECRA9TVsSAnZWagAAMz4P/jgM6m9H8hePS457xCr/\n5GGrQKiCKNowRI0YK3UGP2nHi6NK/XZS5lBdcj/7eHkx2LffcV0VczBgwG9A\nUyJIMsqDKZDDluWDToi16eoGUJm7YXSGSzzETaNUWmsB0FWegtcv4VBwckma\nW03wmtb0O6FfeQnluJS0kH6BFhjEIE5+EC3P0defC5eD704gL5frm6wRj0MK\n1jw6gdsOa6hQF5UsTOiRgJsIH6Z9nrX1qFaTj2PqNcH3ruKoKNk82ZlZzas+\nyRgDuc7XfrCpMFTlqlYzgfb3Aty5+z0UBYedlab8qgi9sIaHLPXAva458vd+\nzjIOakoUsJ1qvSNvP4zbS35kM8pQd09A64l2FuxpNr3jy8rf/lbq7+cuIqxs\nXBvRdbi8YCg+n9vt74cnajS6KLZh91aQI3ahUBvF5o6hxTJaQ9Qj5iuNS0w9\ntKiIYJemlzq5S61CNPUrvCDI3/YEJWvbKm3caj/yiR0+L+AskxrAe4UMyi3H\nMCPyF9mCM/HkcK0QFNLnKhrPeJTEZWfK1AXI9nY9EAGKZPbSAg6JmWs+J0CH\nHF+1cXcNTLf4Wv0yxpFFfx1KaHQOk8orUmWQTXMAgEj9RYwqotwEdS+f8+SA\nFhilYbi+AnFoyJhr7e3oi/x7B1ko/OkCtiyJbv5dDeTRl50YcxOm668ZJQmw\n3zqd\r\n=O7Xy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.9": {"name": "@hookform/resolvers", "version": "0.2.0-beta.9", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "chalk": "^4.1.0", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "337987f8ae23d26ec9f25175209d190e1c0e0735", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.9.tgz", "fileCount": 55, "integrity": "sha512-nEHtjR6wl/T5srFNn2C9dpznJZ2ZFBTSVVw2ttMEp3oOvoNcYYRGxZyedlUXgAi79JRbJP4QpTOcZCd6eHryMQ==", "signatures": [{"sig": "MEYCIQDf5QrWlINlsQlqiuEB1r1zAFQ78sGUKhzA9PHXbp3prgIhALIXbsA7MrESKW5gEfP+h65PL9nkZx7U1idqnKfQpsgg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZT3ACRA9TVsSAnZWagAA7uwP/j19ity5dF7VuRpdzp9f\n9U44JLvt2POr3czGDv8rTF9QnH1xv8xWjT4n0HAi46SHcwj6mOsy7RDLBMuC\nz+N0t1b+0uVYwn0sLjSZHryASgOxQFSPT84rlVTqPdvscLqSGIrz+vfDHniE\n7QLGFgRxQFxY7ZZPaNd6f0DgakHY1Wd3w+R7sHNSY1sXs7QRpES2GJuajTFI\nA9DZvlGf+5d7HRK0KJ8ZUjMBCBaQUxh4FapF0AyjyPWlBBQSxHIidNuKjdeb\nwIv1GUN5O6ZmiGkqgE5v3KXrWpZ+mmGsUpfucpaQ4NmwlylS9Or5siHsNg/c\n2oiMBWZDjt+/cxSpRU0QKOXnwTJGp6DUq6KuynFq1v0MZhz/2H/2mTt5C7Tr\n0VWTckzbboCTaxlrMPXLzXHv/Ab0O7sDxoLaO++eNCB0QYaL2h7XJ2zq6dPG\npcTayQ8Mwud1R5nsirJVy40JwpHtExcjQ+iB3oSD4M8HBbx7Wv3KrIZlpcQM\nBVcSKw7AgK/AZP0VQ1iWL0rrUffzBZQNy8ajLFr+AB/RKIDrJugkBPWmAjxh\no/yEYNXC8RH3K4C/sFi9BU0PZjLAMEtfDO3uic4rWEhQK/7u1BLkZBe8KvAW\n4M4wEPbY90dI7czSxZ72MVopKnj8hI3+LkHZugjlW1lFJr+JU4FXLoEIvWpQ\niTPV\r\n=X2Wx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.10": {"name": "@hookform/resolvers", "version": "0.2.0-beta.10", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "chalk": "^4.1.0", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "d682dc89a2658a7d95199f94bb8859dd7bfab4b7", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.10.tgz", "fileCount": 55, "integrity": "sha512-rsUFrqbufkDsS4QE6QokteWN3EQ0bE0bbI0ReS0XU79AfMmTYElg8Xqn1jXjdBNeKdN5EdyjULtkPj2rhhGX1A==", "signatures": [{"sig": "MEUCIQCkyB0B2peH9v9P5ypLQKXaz0PgNbW29azKplfdgZsO9wIgQLPEe9DzEsbFU6Ro+GIXDEv82Gmti3o+3hqj+8QnZXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZqZ1CRA9TVsSAnZWagAAQcsP/Rg1YIMwzgKc0OHFZ6gv\nauUA1fpEYHywTE1pO8pAH2p7ylFYc/+zicSIRZwkbA9nXBas7wSQaktLUZLI\nFsADjMJeZMwS6wcCBcPd34ug8Lmoa5waU2hnoOpvzBwcBDpJzWs9xL+kOqHB\nxd36OgVoi79uAb9QRcJkFnwNCtDOSOesrXe/dESrqaD6oidFvvbPHw1O7kjS\nCxSyPu8U0H5VxJ/PUrvHktlsjfYk/aGFTG/l8qcrcr5Db9NySwIgNBnhSLS3\nu2CgiMrriJWQNQtWN0/mtuTtSxlnUQht+iBFZsUudTKoEaquEMzR1BdR8a2h\nYyaiu9p4qZSX1V5zooDVCmiTOnUoFbj0xI1cVAVEcL/xUUzN9aGrFDr1DPog\nyJjnu/atoXDDFxxQ8aHQm1FeFg6QvwzpE6dRcJq/5SJ1YI/mOTVqMzdZHs+z\n3fOC5+5mqgCNu7Oq/VLMFKB7MWX8wn8/1aug2942asTYiO+1vBZhiAttXVoG\nmv7dx3SQzEf0rSrvVoA4FA9F4QrhTQ8TdxSTCOlI15eFR+71bmmvFy9SZqCn\n6yNEodzdAyqxxx2oW7e4zSkqlZIMTDwno9KQ+jkiElan1It1oYcDyrHghIsJ\n2fgROhhw6RDfA5Z7i9s/y3STu7rA79gXQsnbnXIBWOXOXS8NGSwG+no0yt7B\nxnJG\r\n=iOGq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.11": {"name": "@hookform/resolvers", "version": "0.2.0-beta.11", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "cpy-cli": "^3.1.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "8444c63d0471cb0b32bb13291a46cb83986a1c15", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.11.tgz", "fileCount": 54, "integrity": "sha512-M/mjC0jDibwaMUVdjNZOOfx8nSqoy6qmJmWM0Nlq2QxsINDe2rLLlOnDtfdHqJtZPo2XpLgqzs6zaiIDrlam0Q==", "signatures": [{"sig": "MEUCIQC8rOtJtArwMw1DOyeR4PEo9eqrGRdP5pT76y2FuQKzgQIgJYD1tSbF2NlPKTeNC753TIYmTGGb7c59tOW1Yr1duYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaHFJCRA9TVsSAnZWagAAUIkP/0iGD7abk2Slm7YdBKIH\ngi+p6Ym2t8TvVvQmARokVzEjuAzsODC0xHZyzRWV9xaFus7HFZi43sm+Nies\npU9GdMnZZP7vn6nOWKPXPAZHcIdz0gJB5g3NIjtgbXrmdhaUlI3bQ9AJZorm\nJB44YAdclTuebZZpkmZkez0HN2fQ69VkytrEeHT6QE2x1TStVmhx0W83SlvQ\nSYHYbJqBA2XaORWJFqLx3GdXNxQis2HrqufliMrMAhQxZZVNZu30eE1rVUMq\nviC8ZNB1YvZ9U414g74bioU7vACdyDJWn0rC9cgOZcUtHsHkjwTvgQ1dVke4\nXRIq+rks/NMcM3U6RSWUUuhvYcxJ5M0+Jnb5FCh/UVFne3OZQardcM8MBdc3\nylBsR4jlEXfDrSMr/PIfMWQyO08qrzpF9GRGEaVf96xnlIrBkqe5ox5hXV3F\n/eeZ7rvWqJe+9dzfdaz/OoKwyW6Xz+o3i0hzVLF63fTcqheGSQ5/iQBL6bcQ\nRIrxIg+hlBRgUMyW1oADYRSGD8MIcu1x3sHJ9gXh4n/xRd5A8r/aWN5ZwjZs\njF1iAlF+W3ShpDPCLKukxuRgHpJketww99H4jOK1AP/GBeFcsgdPiZQfQakB\nLPexTJJ7vUOL16WWpSZflR7q8/nIE1AviP0rRIuDlb2RUGOlPMWVT/ut59qG\nKtNZ\r\n=DKPt\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.1": {"name": "@hookform/resolvers", "version": "2.0.0-rc.1", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "3234315d27bbd058083b17bd615d57d4d59d81f3", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-rc.1.tgz", "fileCount": 33, "integrity": "sha512-p6YLQbnPwG6VrMZGusdyAuvIPB3VCJLc/XG/cuaDQNm1xyijDAyU1Og86phOGOmMD4QbItJKcmoaxbO9JKYQCQ==", "signatures": [{"sig": "MEYCIQCH5o3WRbQ6viILhf9AyfCvOGZvA1JMdSJx/BmzqZqe6AIhAKu+LtzJhO6LXDjRpCtK5xrPMgg52e8qNP0KxcC5Ribj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfats7CRA9TVsSAnZWagAAOckQAKOTIxJtXI37cdJzX2XV\nl6UnNQy5BkV4ODBj5FNcO0jUFSFMMGC6++6l55bvQF/uKL+FInvns37QHMx9\nlLdVK72u+bjqwiVz3BxWHQ5nTx6yhpT/gVGTBbDTs97zTbyYMxFT9/rkaNBc\nsJZ3B9NhXsPJiRJdFBF1OJH5du9Wddi8jPkKCdNb8u9Kx6Y7SYskJQApJjbe\n/Xqkg+f4D3Tk+uuzxPJyb/qt98KJPWE3CVpfjvxOkIPGoO5+yQNm29woeOA/\nkzrvjO4WsjsxNyG5nQT7H7gMaWRz6e5WAl1H8KGb2GAOxHGRvtl3yn+eQ1Kn\nUYzVI7REcqzY4IpF0+f/HxAN+wTASxpQLs2EI4CNyDAR3DsXFOUPYennd89v\nyQGiVSL1PqNVVoouWQrW1pTPTkFACluRws/UKn4TaJtbLm4fyqZWH/WeUllk\nEhmaeVLSpaKHYMrj49Ak+6gBni6iQCT0d/P16AUFS7EqM4Nk04NZTllvV7mm\nzWeP/pUw+0MexZT5NRL18nVVFB6PPtmHfKunIzL+FWf1mowAGQG1UGmmcbct\n0MOdcjrb6i1tkA3rlkX+HPgsG6LREwnxDPFdN1l14ThCqDOlqzwTBNAWKD4a\nluvlHzEqMHtBb1vUpLwg7UhpnaSwubJkzle6jxUSw45npEsFI0eOjRrkvF1b\nsBB/\r\n=dPJL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@hookform/resolvers", "version": "1.0.0-rc.1", "devDependencies": {"yup": "^0.29.1", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "0037d39c747c0514648c283519d1d35cf9db4a5f", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.0.0-rc.1.tgz", "fileCount": 33, "integrity": "sha512-PzD0V0tsknAKQlUrlT8F8t1mZN/dDbOGR5Qd3QWI3ZHd1NlusXH1lwsaXnhZCFiAru9n59eDc5vfDutUUKbmCw==", "signatures": [{"sig": "MEYCIQCTpXeMYWFeJjR5OWdCrikySlCQ5MCJZ3TdRM50aVCL0wIhAPXNaGUD62REAEFgM1tW4axtqgJPGxsLVDAh4sgDFQJi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfatt0CRA9TVsSAnZWagAATGsP/R/o8qxYJ51hSN3sG8/O\neP95AO9hQgNgbGkJUJHaCzHkj/LNiqvXnUlPKRLaG01aOKZERVZ8a1Crllmq\n2Jz5hG3kx8gosSGpNPz+NL3s9ARq9hhdKoIrwtk4pUsqUXiz4bL502eOndwG\nMgmQcrCXwxR+kOiRNFeCpodXIf0l45VaW6d6T/l1lYGViokiAs88b+C5vgSb\nb1cG0oZO633BG33RDAnlmyW5KTPdqcACKgP5dJ+iQNAUNmsiVCuIdBuD/qUM\njYzKZY+QOmwmW5z11pn3sESjVUVPsThpOEau4LTMyuz1DcZPENlV4FcbQK/x\nlXm/zrz6hJIaZwMgXuLnhsQysceDdB/1QMylW5uh/oKfg7KNTKqcIxL21slq\nqw0JtVNJAYGmoLo8KoMt0+uAK3h1g6Ju8acbTfyAI0LtXQo/Ew5cxx4zdVvd\n8pWNorBzaBkGsv/DjJlIdYOizi7p8XLhQiTMc9HTvKt4XU/1YMqsm9s/+Taw\nqsDN4fkko+YYT41MWtTS8WWJ28POqbejeT6OCUx2yP6DM+5rSA4KISYJPwGV\nW7y5Mt739gNEbR/aexGlBe3L35H/n/E4dd6x8F2hDeO33yBzid5kBks75Ksu\nL95CrYsmXa/f5z1YMVszed1OIyR8+m8QiNaQkv+rN/Ku7syr80nj8UPc0tt0\nGDWE\r\n=lOLi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.2": {"name": "@hookform/resolvers", "version": "1.0.0-rc.2", "devDependencies": {"yup": "^0.29.1", "zod": "^1.11.5", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "bffafc9a0b0fee427a058d8f17ae16dc23b09488", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.0.0-rc.2.tgz", "fileCount": 41, "integrity": "sha512-D+O3cB24IwsIQBi68nK4A0Dm3AYS7YwhA68WmknjYpdBSsHe+7veQwA9vs6cdNIuIaZshr+6uPUtYaKmlvDzVQ==", "signatures": [{"sig": "MEUCIQCgYZ/51JChkYjQtivEimyYaKqJu2g5+JpCgVmUkuiYVAIgSG8/znwex73wqpldgvz/ZfK29iftB1suT3gQ7Xp5hbA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66598, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfbCw7CRA9TVsSAnZWagAAiY0P/0DcrLbuX6MeD2whePkv\n32RemqkjsLINGL8Nw18hr2eZAw9FMx/yAnHOTjZZSLYkAukmsXFUg1vPyyMT\nfuNrzk1xBheAYdoNttrrhcWkAsl/rIdQeShY51hrWb/SKhZi+OFsYC/ctPIg\nA/mnj2BqyzvTR8fWrlwRBhhwKkwoOUcZdhttF6rj9aWGT7P+NibkmIaMQYa7\nH4C7P79DtgPQ1h3VXgMtne0eutDnk/k2wN4PrUANL4OyACiabMjf2qAHYSPS\nu2jAKpbNGflcx2BGklXd5QsCcY59eiA4ZVAoyOHpvFyMYMuFyKd0XvB+TqHF\ndHNvrdh/Qi2v3pmtIkXB6PZSLx7sVEg1CICA9kyKuiSjwOxrBGJq16e6rXSM\nuPoeALGmVfeIaOBmDBlqX5RYfKCM78+IQbaz//GwBjpoy0/DmDJ20F2PQoNe\n9ggRqGLSF7+V0VFJJWc2rr/ZKNX/9hYkWUDVgRfZ6kg+OKESedaVNFLeJZ8R\ngFpkpM7+XXgvNINOjLGkvX1LvMzgAsas+OQJhLp6uEfPjCmu7acJZc11GYXG\nqIFsncu+GbRApbg68rHdy4pC72iL6QORJVdqmEMJv0ZEh762lf2QMzecj4ke\n6+AUdSb+nCHgxGkGMih9szl850nVP3DWr1wOdS8uea3POdbUZ8zZDtm9C0//\nE+M7\r\n=uXpw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@hookform/resolvers", "version": "1.0.0", "devDependencies": {"yup": "^0.29.1", "zod": "^1.11.5", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "ad924cb846343fe0a42addcf0b055c4f9aa2dcb9", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.0.0.tgz", "fileCount": 41, "integrity": "sha512-YzBq6ZFw/uWGa3rXBNSHqnsE4hDXLrzdboDxPRKGjYHVzs1dBxjvELftP8iTmRPqP32VjnbVfUktX1CQ6Y7sog==", "signatures": [{"sig": "MEUCIQDgEHE1Eih0nTjC3+hbiI0OkFcySAGo7PKKACcji3HPswIgakKXTSfHlRePLVxilOkN+VH4i9l3Gs427q3kROrKhIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeQiTCRA9TVsSAnZWagAAVMAP/izkTsFZN1WDRWPfDnYs\nncmBk5vML+zj+0rG88B6oJs4xLsVD1A0FHwtpkWdCqnyAvMqmtCOLQ55QDFd\ndEZQJpr3lcOfbnK39NwgP2KTcy/zIDvyxTs1dJEEPlERAF4MPuMMppgGGoKW\nP8125bhZ3aWHVRrJRwyXB9oGqPibv0BDJJtalm+bthTUZ7e7VZhbR3EkZW8I\n/foawoLY9J9Gs4KRMX5wSG2byF9enCEjOJMdqNb2XbU7EJLWCz75rOhpESzf\n1ZrMwK1gBLY/gikrZC+ZMpCgiW6BK+HKRrM0elB2DF+cb36XRBaCrjgX4wyo\nukl6G6M4eEU4HqF4jBhhkz3bu6HTHRe2mOZJMASw2oB1eO9yvg+AburzbekW\nw9JMzot6D4CMc2arj2pwNVIgWUQymMxqGL8G31vRnMMi9oBpVSBaUE+86yNr\nwFPHNLJQ02K9olnDQ7KbdGSmmok1HZTiITC+N0KDSDNrRIz3b9ZWg70HYQ8C\nzl0fOMYt8mZ31lloCpbrSSUWS+Rfv/uZSWj3fgIm5jPUKD4PjPUiuUnQsaf7\nhOXgmnSKZ7cYasldLqHKt34OMf+/ByDlZlSfeIovGSVirN2rORscLN6P0184\nyP4zD5+JWboK3L78JePkdk9GoFVxuToB+1RAQJBOmW/L1o2HvzBTHsS/sMw6\nE6E9\r\n=7tbl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-beta.1": {"name": "@hookform/resolvers", "version": "1.0.1-beta.1", "devDependencies": {"yup": "^0.29.1", "zod": "^1.11.5", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "matched": "^5.0.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "0d22dfeb3b80317a82ce728896246b99aca58008", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.0.1-beta.1.tgz", "fileCount": 38, "integrity": "sha512-4nMxtiW8bS9sAKflC5gYuvvViWMWoFJpT8kT3G4gaH+0gP6MeoQOgVis2tHQdQCNdgpSa1CxSZNL6xlOJE6c0A==", "signatures": [{"sig": "MEUCIQC+um6ITS2jYTGtDnDgIvYupeSpOFDve7RCJH2O2JbiMwIgVnNytPyPPXwteg0Kwm3Stir10jfaJDe95e4ZeeTAn9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk28UCRA9TVsSAnZWagAA+FkP+wU6roq4WHrckm9pYnmo\nWo5S944TXvjkE7gjEIOBSScP+UAidgL5sK9ZdRV29ZzEbVGkEMMtQhh3CjGD\n+jkKEv9OzusSKFuNUC+kwhxeFcH+47iweFGPuU8y235H6JVLy6d5q8+ljwHJ\nb48XoIPzkpG1kZBkQ/9QQMCdcJF68sk6h+Grr1VTVO4guDiM4MEViNdy05TR\nxBQBDVjYjIoRuAOoo/SNR8eZtW4y1peCT7fzfNrwL184iA6w+nGcEUMKwB8W\n1UetRxA+FMPmuOIw6b16/vPsc3rUNDmnaCi3BKZvI7GWSrcjg5S/GNCKsV7s\naMbtQyeGlhw1zuh7dQQO6N26fmOvniXtNSe7GQYLBjG6maGm4+s1odqHjxQp\ns0DT0yfDSjmFFUmDSQQKhnUlV5fEQaypBp0omjuO9v8Cd2CdoHIRZiyJYf+x\nM0Neg+laNe0vaNiLreSICHA+Mer/Nsikyg/je51PaFRWyKVZ4P9MpkItv7cr\nLlC+wivq0P+3bJfppiLTwDhBB8ZNGawJ20r9fkxq/9foVQN67F9G8MtkpyLo\npMRtrZw8y8jg1pI7Rdx5eOpiVlYE1SvoQTDF7r3T+q0ipwaVUUyuzuGTJykW\nnW+f5imS6ADds7ZCGKKOkm1F6N06UgyzcrNfKFGxXCH/wA1ttQLhoLaMXq+p\nnbvG\r\n=ttR5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-beta.12": {"name": "@hookform/resolvers", "version": "0.2.0-beta.12", "dependencies": {"ajv": "^6.12.5", "yup": "^0.29.3", "@hapi/joi": "^17.1.1", "superstruct": "^0.8.3"}, "devDependencies": {"ajv": "^6.12.4", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "@types/json-schema": "^7.0.6", "@rollup/plugin-json": "^4.1.0", "rollup-plugin-terser": "^6.1.0", "@rollup/plugin-replace": "^2.3.3", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "rollup-plugin-sourcemaps": "^0.6.2", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0", "rollup-plugin-peer-deps-external": "^2.2.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "f268e03d719bcfe39b9d6e0ee8a996f293d6bf19", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-0.2.0-beta.12.tgz", "fileCount": 22, "integrity": "sha512-NXr6Au+tpTLu+EFCKQhwpm2+UKFXqygYLSGDtex/QDaKJNPeYY1w2Q4X5ULWR7GcFjdJeCx/Fs5mu7CyLlxLlQ==", "signatures": [{"sig": "MEUCIC4v1UdjFan+ebq4cVrh8e7rlxNDC5mCSUT4ZM5tKrevAiEA5b0EbpCq97L3tM6vzzQxOFs0aNMwuD1iBGRmfvSo5c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1456898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk2+vCRA9TVsSAnZWagAAhjcP/iNu3dYfpL1YUEZAP0hV\nOHTezzXFMK4iHbsQN7tYyzt+kZr55IbkJEaMv/HxZ+hYtTWRL0LV7eTUqcH3\no7UBZePuowuqqsYD2uexv282P9zM+FoDlYe7MDs1sdX7pTZZJfbI6LrdHQE7\nZohPuMUWIcP8ZKQTiRJsZvvFu9AgD9AOU3kN90U16TDD0sWN4yGqxpzU6hSE\nto3u4Y+YqaPtixJYyXVxAS6EHRORReIosINHAhl1jPdkSO14Edk5A0rLQtsM\nExlzPEYyF9rK9pzJIJNmJLXouQv6uKhdW1kML4opC7so8MW+k5xhph6uxgtu\ncNqFDf5j9IQD62iFx7KL4fOLCjouWxUvYzwoG670pj+/HN7D2KnqkASYhbPS\nRyzy+GlG7Eotdm3/PEC2df8fH7hffULZIyyKZASEdaLNqNtc4PIUxqJrlUUe\nYDotYuahJiadyAqqm186K0Rjp2FcsqLAkeLcjNcD4+P/csnDXIVAjavLMiFY\nhnSJJ2QGwfI2ayHX7g8c7H4P+h6yPR0thvwb8ZAKkVSbD7A7Rr9IiVyIsZ7z\nx6sRA+aWZ/tlw/MV0MfiDy6mZQcC5UAXkPowETvyxYUG9N0fSrFkz35yCVXz\nIe4+bBoA+a+jwWuxIQkFW1ypZKam+IDM5w3foAS5+GpMkOP/HTEvg+yMMSog\nhsSW\r\n=6A55\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@hookform/resolvers", "version": "1.0.1", "devDependencies": {"yup": "^0.29.1", "zod": "^1.11.5", "jest": "^26.0.1", "husky": "^4.2.5", "react": "^16.13.1", "eslint": "^7.2.0", "rimraf": "^3.0.2", "rollup": "^2.16.1", "matched": "^5.0.0", "ts-jest": "^26.1.0", "prettier": "^2.0.5", "@hapi/joi": "^17.1.1", "@types/yup": "^0.29.3", "typescript": "^3.7.5", "@types/jest": "^26.0.0", "lint-staged": "^10.2.10", "npm-run-all": "^4.1.5", "superstruct": "^0.8.3", "react-hook-form": "^6.7.0", "@types/hapi__joi": "^17.1.0", "rollup-plugin-terser": "^6.1.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^13.0.0", "@typescript-eslint/parser": "^3.2.0", "rollup-plugin-typescript2": "^0.27.1", "@rollup/plugin-node-resolve": "^8.0.1", "@typescript-eslint/eslint-plugin": "^3.2.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "a80029fb8e1fb8e17960731010d48b4cdbad1941", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.0.1.tgz", "fileCount": 43, "integrity": "sha512-hhixh7KHFr88kH+jwoQW3Y1h/77Q54RykO3vrrwnqkSW3aGjpJVzGjr5dvkJrguGljO62zryqHvQdR9Fhf1kig==", "signatures": [{"sig": "MEUCIQDwnhzFCZWrTls7t5sEwgDrFMpt5iqltTlg+UhLsm32YwIgUWDuu7BC7jwUkPc+Lv5mHxkvyZDaJ6h2N9BA57+Eyf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56818, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpdBmCRA9TVsSAnZWagAAaR0QAJ8Oz2VsJDQgn4la3WlB\nveTP0zcgkn1sNsCy3ACBU7qUnb7HI7nhz2keTuJvat9BJ6FeaXuCVsvvehRJ\n3g+OEHD5RjJDWE7H1UG4UKMc1dteSgZ1EP95P2EOyqeyZdTGVjK1mEHVJVBy\nWFImapFBDSpZPAa0ewLEBWMti2Fe4oGyQnClnAUf+1fkO3Vl22SEQAqEp/kW\ndPXt5FX6iotfWB/Hp1dL47Ej//o5Up8cuxXL0qGKN6ksFkzW4FS6tMdK0eSD\n7yyU52QTXbD0JT9eovrKC9RlNfQ7mabUVIJ9uMOu4Bj31maUUI5cH8ao3aeF\ndlEF4E34pQi0oULm0+TSV8W/OliCPUJew+8I+5ggeqKWivplrf1NUUGha0UI\nlzk5979eUkOWjUnfTICshtn0hGuVMe5jaIq7AC0J5SEtUwwK/Hwovqx/Sl9d\nH8zOpUCw8MFAb2WiHAicqfXowcDQWAtUrxPLihKYF235MHwqusm1EhZj0JDs\n61byZHbgn5IVj+sy5cT7aOSAgpcNMj5NPmkI3+gdUPJmEaOvn0Z8KMcpYurR\nQvQS1gWUA6RcDkLQ6kzIwKRM7HBX6b/2pgZSw8tqhLUW5aOYB2PsbtRrAEd7\nI/e2x0v4vyd9Cf0c04kmp4aQGqGa22xA1jsdSnhdyuSpZWhZ3n7t27aSMNPA\nEvxX\r\n=4mWU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@hookform/resolvers", "version": "1.1.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.31.0", "zod": "^1.11.10", "jest": "^26.6.3", "husky": "^4.3.0", "react": "^17.0.1", "eslint": "^7.14.0", "rimraf": "^3.0.2", "rollup": "^2.34.0", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.9", "typescript": "^4.1.2", "@types/jest": "^26.0.15", "lint-staged": "^10.5.2", "npm-run-all": "^4.1.5", "superstruct": "^0.12.1", "react-hook-form": "^6.12.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^16.0.0", "@typescript-eslint/parser": "^4.8.2", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.8.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "ed4446be33cd7c419ab0416451682051ae1e3a6e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.1.0.tgz", "fileCount": 46, "integrity": "sha512-rzhWRu6s6CEpWXuxWsbJJmuJCpc10AAK+NpQ4LeVJbl7ShE9r0ODMv043KZ7Dvg4PmdTgQg6v6C2rKKhv9wm+g==", "signatures": [{"sig": "MEUCIBKAiYXQCPHx5Q+tgSmlP84nDtFfMgHXkvueNLYTrMolAiEAlON3VMfe7VhMtJYF/zIyGBOC4Xoh1to4U9q9o0hxOfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxKDGCRA9TVsSAnZWagAAdMEP/Ao4JnX8v9AqX9LOzJOg\nQIbLWsrybSVzrRoPp8boQlYwBHfSLbiYv7TWQ2ae+0pOZFHmkI4TF+3HQFpU\n0d8tBRHIo8XNje5Jvf8JU3Ku3KtS0NAvqiEZEMdRQ9LUH+DszR9vlewWTc5h\ncqfNC8OH7OstZVWVJV14ESfmTkZ9ScHEZSIkxH+E/gtX3nbucRoyYr0KI+95\nPQHP6NkzE20Bi3Tw+L/PtI19qlgV6zy8hB9Pm0UCPReZcdgqezittM9AeNx6\nPPJVb2o9WqVTF8fdMmG1w2sznjlXmFTwtBqUIxcm8WZnoRTNawRAVhaETUHt\neNgLOoCdX9r4gnMqK728Bnk3FJIOS9bmiYe1CS2+OuWZn50W1j8CPKsqlzvg\nDR6eaRxFVDbux3iA7MP///GJGUjFh5+HOBkdIA769QP88Nl/kZeARjTUKwDY\n4ccdlpRVHOABCwDhP+XO5pjiW5uu4ZKvMq3TFSlwwF/33bc6UeNp4ur4mbO4\nFvNrAnNBDQs+BbMS1lpesEDpWVhb52NsMH1SduU2yAhEQq4d4ubzrWPcoLML\nNI/FaGdL1PCILmAuiBHUG2H36zM7ufU9yottiNt+Gdxk+QX9aqsrTNDpi4ps\nm87pWECKIBNfTG7YPTYxWrqz39H3ep1ZXmpOxtvj3MN6kvqpWV3MdAeDoetI\nA42x\r\n=YbCd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@hookform/resolvers", "version": "1.1.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.31.0", "zod": "^1.11.10", "jest": "^26.6.3", "husky": "^4.3.0", "react": "^17.0.1", "eslint": "^7.14.0", "rimraf": "^3.0.2", "rollup": "^2.34.0", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.9", "typescript": "^4.1.2", "@types/jest": "^26.0.15", "lint-staged": "^10.5.2", "npm-run-all": "^4.1.5", "superstruct": "^0.12.1", "react-hook-form": "^6.12.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^16.0.0", "@typescript-eslint/parser": "^4.8.2", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.8.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "20baf66cfd21f0feb65eea4f150ae8693be795f5", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.1.1.tgz", "fileCount": 44, "integrity": "sha512-X6OWofkjr7U4Yjb0+OIgOKWttsy2UB8NeLejegGuWgcVad0kHQGezo7S1lhJmGS4icva13gfmGGcW3EOeEEQ5Q==", "signatures": [{"sig": "MEUCIQC+bA6N36RIt13S7NBIuM3HSOY5s9B+bruFhQ6Q6rDzoQIgT8WBrEwi4OPAmV5353gaDzG/qUvNxPDg2SFRbg/cDn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfxaypCRA9TVsSAnZWagAAZxYP/R91PIpGWWJ2w82JXhcY\naNQtj4BHcp4VQRimWElTBxLUs8PZD32kxrnYwkcOZQ5Gzg0kbpJjq9JNdPHv\nkO16Eu0L8YeML3pRQ1exRbMJMc4IDvw+p/X3mY7gSjQlkmH6zD4SftstfwWS\nCDoI5Jklbg9hVGZNMhqlZkqwYqJXde24h9z3dBu1tXdJDTyC6mN2hxpT0yP0\nCDZBhjt+jrtRj6bh00iBLYRkT4u8At+OR8f2kEaZAl/nydd62qniX1mAE+wq\nwWj2kvLO8iJ8BkyuaftAvK8uxQLGM6l+zDOUoj8JSxmlvtwIKdaL5QDZ0vaG\nrn6zgWh+MJpqqyozU5O3LYjaY1yhrK6uvcoZqlCBBNhJqvtmLkt0pDD81dLG\ng76qcdXvLfM2RxhDkWNqSppJKIJwn4trPmcEeWUyYUPIPG8ASiOWjIamAoa6\noekHLEOv1Wige/LH+HQIiqWLbuGevr/OLlRZRxAhtx81sD3cvvlTe3u7DXWn\nzFRpJqwJOpDcGbQftVKnjJZ/VKXD/PDkeRcEYDBWu5c6jFNT1dBrbOUPm9o/\nxNfpOKwtYt8jla7GMqsuxL+x+Kj0DXSCcHWzuOgIpUqxpAKqovK3h9Y2jWUq\nOVmA3WZKrqcxX9StX3xsaFbFyn0ya0iktT0GTS0PrdOzCIs8O7nSgaBGIyzB\nwU8B\r\n=EUi1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2": {"name": "@hookform/resolvers", "version": "1.1.2", "devDependencies": {"joi": "^17.3.0", "yup": "^0.31.0", "zod": "^1.11.10", "jest": "^26.6.3", "husky": "^4.3.0", "react": "^17.0.1", "eslint": "^7.14.0", "rimraf": "^3.0.2", "rollup": "^2.34.0", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.9", "typescript": "^4.1.2", "@types/jest": "^26.0.15", "lint-staged": "^10.5.2", "npm-run-all": "^4.1.5", "superstruct": "^0.12.1", "react-hook-form": "^6.12.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^16.0.0", "@typescript-eslint/parser": "^4.8.2", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.8.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "3e3521a71e701ae133f02d53a6994a1037b0ef22", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.1.2.tgz", "fileCount": 45, "integrity": "sha512-I3kYtdj1QX9VkNq4GtC2ZNfoBV13GpoxZCsoA9F3CyAmkQmiznJt6HG8Oc9/R3PLqPrw98FiIzyVs/rG0FnGAA==", "signatures": [{"sig": "MEQCIA66dDmVHcH6poa7eZsqBSTH+lHzZoGCOjUOU94K3v9OAiAPpALDBYES5MAMPbwsLrB7X2WbXFO40LsbWKoVqo+LOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 85847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0XlbCRA9TVsSAnZWagAA6PkP/jXpHUKPRJSG3lcNQala\nceMMARiLYyB+K7YyY73a5sI2x2VXALSG9UehV+p9FSxqDHJFPZRJTLtx9qCl\nYplXnpnWhuRSrrfumX6qQ2uJDhGaR+t+ofFM+uu+1Xn7Q4Y1m72DrYeaP1fl\ny8UI5m0B5btxkvKuk/i7inL/XM5QYfAmqdRIzZaydKZAadhZ9Rv0kdukZICM\nic+Jrfadsi3rSrOYFh200TM3hnLMeprVGobD+gFlNULz7F4WMB0FUlZvE4L7\nGoM8d8LBOzTVKNFlt0qwHL0AChuw3bZhQsE9ykbeXqhlVIz/IdpqeoKIBAHt\nz89e8AC2KavPIzOF1DmnDqSOnJAL8fji7y4aHba541qV6NkU/xF60gvlmvqb\n2QCtgA6PzNwMWJF1ZTvED0PtNcoaqPn9D0vxpbdGoqW0ecy+DKZUQktc4Xay\nZL089JRjTgbIjkm13d+j0kRnynggnmY1Ad+JRBuRBfWnsxbHlgKQITZNJfsj\nDGnefsWsZ9F8MBMLdCfBMXKeXHzdc3/q4+Ao9gcezqMfkIW+wxEdAhBMePbg\nCsXvUpGa8UBp5m3cHszy3wauEHp3fayu++tBDPsnazdbr6A/U4ZaQc3RI3oX\nSk8OwOOpXUEePWOD8sHIh9vCD41LPvPwOsIlhEen8Q4WPICgujZD9NhsZU/0\nnQHH\r\n=mNvj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "@hookform/resolvers", "version": "1.2.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.31.0", "zod": "^1.11.10", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.0", "react": "^17.0.1", "eslint": "^7.14.0", "rimraf": "^3.0.2", "rollup": "^2.34.0", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.9", "typescript": "^4.1.2", "@types/jest": "^26.0.15", "lint-staged": "^10.5.2", "npm-run-all": "^4.1.5", "superstruct": "^0.12.1", "react-hook-form": "^6.12.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.1.4", "@rollup/plugin-commonjs": "^16.0.0", "@typescript-eslint/parser": "^4.8.2", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.8.2"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "3a429b4bd3ec9981764fcdcc2491202f9f72d847", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.2.0.tgz", "fileCount": 53, "integrity": "sha512-YCKEj/3Kdo3uNt+zrWKV8txaiuATtvgHyz+KYmun3n5JDjxdI0HcVQgfcmJabmkBXBzKuIIrYfxaV8sRuAPZ8w==", "signatures": [{"sig": "MEQCIHaTst4HS50ZLDZYD8++bbelxMNUJ50rHgoQTKOuE9foAiBDd4BMESjwO7DRXDuHzH05y9Kvlm81af3wnpsfMTNQxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf1+WWCRA9TVsSAnZWagAAKZsQAJGFGalcCEfWRPqDJfWm\npu2hkhrwsWYsmjnVCorfDB/saR7O/JCIBN1KElpcLS8kK3mhA+eSG3RxRjRx\nzkNEJAHgnHZzzueQecPVr+2p6r/PbMOKHp2Lti0SSlkCPW5NEkrSw5f/QTcF\nTsRHTMcykxGqMjshDSB/AjQBfOKsmHvQtuT6TeGYGjpyLINKo5Jfmpsd62UY\nPeHiwu/SjAWyJmxANsb23hPt7DupgISKZfQIVk67730H+o+ahJdDMPRbiLhv\nKpRk10lSizcnKiMJDoUGYxt95UYEz5B+cqTVjBbIZqBhgf4IOkQfUWPQZvJi\niYJkJna4QnB0HxVQ0e3MPXePo4YnzCpjLEAIXnkvxXdgUt2xSgxQ4PWbjPZK\nOUi48NMya/Q93Wi03tRgIO1msxGgWULsWTu4L9t1nE//oX9El9MktO+G75rL\n90Vhf4lIG9LmoefdhB7DzX2t+PSRLRTQ90rD2o0jOUc8m6arkDBhMFcivs5t\n6eD0fjxOyuEsQxwdwQCQDo0uKY74tQYvPB3yJcJ33laqO3mzbuogTXJ65kGN\ncJ/rfghLfGjobPP1iY9qwtj1CTOjSDgfqXvmPb9SBIq2EtsNVFONUW3o2R7q\nLzDaP2e7+5uFwLGt8Ap5xEWSG5qPbPz34aITc0Tjiu05PaB+4CiOUukuH7W0\nwYe8\r\n=7BlH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@hookform/resolvers", "version": "1.3.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "b2b0ff3b4b6b104e0df1954e58098e0a9cb6195d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.0.tgz", "fileCount": 53, "integrity": "sha512-NF+KJYDiFjnGAqhM92vG5kFJFCZFFjO5VxcByf06o6FrG7BEta5NDBhqVsIAMSfMNsn1kWfSKX1TkDT21xq9Hg==", "signatures": [{"sig": "MEQCICj2K5bUYzSQj++nFt0xiUj9yow6JC5hBPmVOJntOxGEAiAqvw9zBKkUnaJlZJue2eIMua/LovywQzo2UG1nc+Ixtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6uATCRA9TVsSAnZWagAAjV8P/0bzKqLb1NjS0tKeqK5s\n+WaCWaHAk/WqBOtWWZQoHwHnHp4V2qtra19f6+YmMVP/vHIc0Cjlop5yoKEi\n5EsZQ+t8geRpVgAQ9SfvIylACXr7nE6BwBjrSFm5wnzxFC/JR527KbNL7XQL\nLIk7aZTAHiVNTEV9+s6hv9WqqfblsmgUB2eP57w7430w27EOQCM+8IAWT2wB\ndYCaAmaFf49NTpCSSpLszuic2F/TYzy/5P6mFxABB4D28YNB1mr6bXq7uh45\nTnT46f/G0ROeVev9rjVq65tQCzwM8NX/lp83HQElC3bbmiI4i7zwlG08ol+x\nguqVUI0sjReq98rkgNVVj+bYxFsMFkr3Cv1Xg0DlZCNGs5bS2IjQwrwLfbNm\n8xP7YOdSQI8vbjn72UOFMf/4HCBX+4i7uIYC9KUiJS76qFl/pX4fEsnqZ3UE\n/IFCmk5lvPeFW8ifeQ4anD/6mQKIeh7akgsWSW+4WJIoT7lp6d0iPomgZt6e\noguHM4huut7NEoeuQfXZqit/L+XXpgXHH695gK99vajxXRv/7ltuRJWo1M9V\nLRsNDjfo0Ch44rgc4SedmpizmunFYndZIrC4r5fAZ/MxHp3AhXlcb5krEJ1P\n7JQF9c8EgSBJ0pVVlQ05bF0UsAL5FylihBMDrO32U7HC+yBXHvvDJ64uTKqA\nr/e6\r\n=tI1w\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@hookform/resolvers", "version": "1.3.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "@changesets/cli": "^2.12.0", "react-hook-form": "^6.13.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@changesets/changelog-github": "^0.2.7", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "129533db26f7d25d10145fe95e17367c9127e042", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.1.tgz", "fileCount": 54, "integrity": "sha512-RrTZHRVSL1UvFWf8/HxGXzfkbYtgdauVBo4gCZ61euQZU3hk5jbWIk05/vQmEctUeUggxDcnUnVqd9yAf5z0zg==", "signatures": [{"sig": "MEQCIFNLfKR6aQMGNEaacZgXz0ShNl6lsmAAQmFjhry+aD/DAiBWhg7KWEDLakYDCn5V7gd6QlTn0yHP9EKje/WScotnbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7PUrCRA9TVsSAnZWagAAKR4P/0H1gBrBWLGlQ5aaSvaB\n1EFJjGy/yOtosZ9Og8xWIajsl0h1V01kBc/qqMR12ZRFFtTpEpuvwUq5lyjy\nR17/MVnQdyNXnZ3EmdOkU6E/zMUhlyCvFUqXi9fyEts7u0A27w1+UVlspeKt\nCSif4t5iWn8lTXXRiyJiLjPbkSM/qFn2M0AZfvWZcMVigeDKaa2pIS7m4ooW\n4emKmWuXLlGusRKMVd6plI8XNQnOxtRx2vyXCfKHEQHgW3X3a6/RiKlOf2hL\nKvGovYVrn3lKX5FBBCNSeOtKGcfxItr5H3zvI74+eWtRAebYXPVTUY/JqgRU\nvxSctjoU8BpmuT15ehRn91qdnGRtlLtXNKbmnkxQxsiV/E2JmL7P9ye3L115\nF0o1wo7w31xFqI7WtGBKxOicLVyTzy0C9nfCbD4vcPDeyfOpZobu2C68CCGD\nh4p4xge/ibdxX23QNEhlCBJ8vq3u4YZ+8kh0tAFpGvTlP22m9oTh1RuoYy4i\nSn9B0Qqn8HFXLzPu1Q+FMd4PrCPWFZEj12BjoEojh79apwla0/m55cP8FTvp\nOVpw4tEDtCus1QLQhYEE7wnSri1cwkvdT++HAPQvaEVLA4hUD4oKkgmf7uxh\nYRbFQammIl5m2ltVYiNWxwWCwuFP49qr96Xa2K/+nEAc2oY3EVoL+hkRCctR\nnl3Y\r\n=pz6l\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "@hookform/resolvers", "version": "1.3.2", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "semantic-release": "^17.3.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "9d747784c6d647da22bcf43d0be17f470d412182", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.2.tgz", "fileCount": 54, "integrity": "sha512-OumtsPsGqBcsMXGUkM7IGDO55+ZmruF3u3D7eDYDK4HpYQcp4Q5QeqQtQr/BLD1wgTiB4YLvcboRXrITuJOwdA==", "signatures": [{"sig": "MEUCIETob5BM6Qu7UMTio4ncnLvx9QkJ56Qu1s/rAOgv3se8AiEAxarQNVXJcS8sE6YuYn9Sp7gxOEDGYjU6S4rxDy6ebK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 101104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf84/wCRA9TVsSAnZWagAA3PkP/A7pX3uyLoKBf3Zmf1zi\nR7vZyXAIrStRAYsqGtP+zNKsndf2P2Ogkg31mqGRbfAtvJoatYu8R0nMHzbv\nPNV8v8+kM4sfmMKDOA8nzX0FDr6Te5j5edvE2pYnqLAgMY9VKGUtYkgcB07i\nDGmbPVFUjQlD50rUbHRXhmCkOBE5Zu1hZMlwAz+AhgIsjH2CjhwGpZXuYYuD\nh3YmXIWMHfB5N3maTmRlSLcz+ui3HUGGfyLJIrkFusVF2gt04ocLPKxL/7F1\nutbXXlQOPSxAcKFJzRIGe9sxP1YNs/yZK2O8WI1fmHRZmD440l5GbDpVaCvz\nbmLaHnefbmvm3wRjX2nK+6HIjgcmjlgFh5KcAQrG3+P6XIqVITO3i2DRXBJm\nGA20+QjJg2He5ump3xdPDbgtl6dOXAc8GQkSUOR8l7vEnrgDoro1VC8VSQAQ\nYaFlAMURpzsUy5aixC74pct93Wf1MyWeH/OukJepfIwv6djpN36qHd3XI4p/\nUkLK8D6CXZK5JK4fQNK069VdCeUayTo5kgWc25svbUZEgwwary2F0FGdpxwD\nUdun0MCFCMRMnfsIXZ3ASCCEDOK6aLD1MGLyhQZa2lFDko941Cv/cI6mTRGK\nD+9KXxR3pYxedqhhXoaMIpiI+5CoGBceplE9ZjM9eUfJ91KNVBczGJFdbIWm\n9UT+\r\n=hB8E\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2-beta.1": {"name": "@hookform/resolvers", "version": "1.3.2-beta.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.11.1", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "d249e8fb6d29f344ba167b868e3b17bc250e6a08", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.2-beta.1.tgz", "fileCount": 93, "integrity": "sha512-Ye<PERSON>+ArzIp4waJQxPKoMmuKVG9C/b3KZsVUUfMZeSEZTsajDFkwTi9EJHql3yHxVKpKddopo+2Xc42sVUhy9Juw==", "signatures": [{"sig": "MEQCIDKxAcLKQhxJmMYtA/RkbkD8e4FWC+EP7vZUsnWDtm6nAiAUK/ZwEC1mqiM3HJlnsb4vyBe0sFuIm8woT9GLCb7Clw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf85QyCRA9TVsSAnZWagAAquIQAJYe7qeMGYDtGRpw5sWM\nSkLZM8w6oomnLjFlNSpJKy+VV8gXG6dFvCDjVpSO/GoOOiIMhUmYlrpinBOm\nthsQEW55l8d760ZSW6mq971SsXxznZodrrTZAI8LvJfGwMbHRXCLP8ZCBh1v\nrFiRXlCsuokr6kS1JdJTMYD8+cv1uWMTT1HPqTb7uu7Xeu7wQ7fLuKOJgP97\nn+sQK5jgjJMMM2WapwvTENPhh1rgrOYwObQgpQ6bhjqt92Zb+uYivwyMW2Ch\n+EmBmCp38fPpcwiO8bCs31JYyYmodINJ+1CjN/QC0doQxzg2rwtHpiQTsHtw\nLGC7F8ARfnsrerwVqFBRHJXtMaOaD6qfvFS1uiERAgWRBOUPehVIgz3PCEc6\nRrhCxGYFDD7ZuMB/gRvzqC1XGQtGJY3Pr92560oj7BWD+EJBXPuqSUpW5Td4\n4Y3+S2ugF5eegHw3DODjPCPangtCAzDdiZKFTrx3lqGPIEEGa7xOBmyv2AFI\nMqbX1D9eJqsQ3Sktp52jatYbUvXmPD+puGWBmohUUWUj6R7Hx7zxOibLRT++\nG3gPFBsA7DDHb0IY3ilNlkIYmw5SCnI6Pn6d7ynWJNMwAksZF0U8VrSFyoCd\nhV2JIA8qEstjbJM2neCNtlMM01+YR63pZCJ6MBmDhfIZb9m0Pu7Dq4Miwb/O\nCvaP\r\n=z/F0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0-beta.1": {"name": "@hookform/resolvers", "version": "1.4.0-beta.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.11.1", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "387e71c20e70f02dcb7cc1b983fd3df42f3bb6ae", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.4.0-beta.1.tgz", "fileCount": 93, "integrity": "sha512-nQAPxuXPtiQ7+6hNzmZz0e4VRrFnO2TKJDl3/ckBnUgD2C8nd/s5NUqzR7SToxT0qQaYGaRZ40L8hDg79qsrLQ==", "signatures": [{"sig": "MEQCIDTJe8pJphQycmwEgH4H+zdHJ7pjrF9SDD2MMzKvuYGgAiArPZQifb83JdyBnqGfEZQhbai5mC5Mxuc6KL/2+bvlew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf85isCRA9TVsSAnZWagAAeeIP/2NJGnX5A4yUaE/OSw1T\nEhdzREJJIR7njd07tL/poWvyuDlGfGLb+qMFVSSf0/9r/vflRLWe4oAkB6GD\n1znC1fisNvN9un0JLxDw6V7+mf4JEVJvHpqHJ4KhVkkScRyosAlAvbNLrnlE\nZRdxs5HO7Rv3LIrtinxa/4rvn2bzY1bHjPEjYaTFhqwiUt30N54nkSQpTJ98\nYGYUSEYGqDetgJw45aSPhOzmFUzJAYbNt41F9un5QgG3w9NsR0NjKH8QOBCS\nyJda9YMsS0JKht+0c6rbFcm0BZFkAb7yX2fTptF6/xs6Sv2Z1kFi5M+0bSvQ\n1pepJfuiG2e18Hb3gjFsZvFbDNTTNO+aDmIO5ynJvXQSgJ86AkFvOW9QkPxB\nUxCgFlDDmyjegP540AjFSnuZ6wkaRYImBPlmC6LOR6WzukxKiYrKw5JDvVZK\nLMOT3YLgg8mnVh6+cURBDffv3TpqhapYjWJu6MsJD/hS262wvwCkehxZv0KK\nGV6P76dAoY2qNjbNX8a4EOocD+WTHOBInKts6hLGXAPEa63M+fvp7emaM18g\nG72GjSuwN7AhMHF1NfhTqaN3DLO8Xw4TfPQy9l/oGQwgIP8vj/T8y50sXb1k\nr96U/OgRhjEYNS+5JBA8DZjRIC0pxe8Q1mvgXFtF0faXwvu7M6hsBq5IYvaH\nOy6m\r\n=aIfM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.1": {"name": "@hookform/resolvers", "version": "2.0.0-beta.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "semantic-release": "^17.3.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "8be24350a5b882e2f098ba69671887a679a4813b", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.1.tgz", "fileCount": 53, "integrity": "sha512-ZSFoRZOJpsJ5yjFZghZbyG2TOEper4u49vnhDpkNxAn3a19X8VAC7kpcLbkV7421HH1cacp9z60I3hIoJJ62KA==", "signatures": [{"sig": "MEQCIG8X+GzS6lb5GDIsQ6sE+9ahNjhJylFhjSTerYPY/3fHAiBAtwZapLa3QbZGsIMsiCiPnPzurKmFtMFLuzblelZevw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9FrSCRA9TVsSAnZWagAA0oIP/27XzXsTIJva3JHPtnRe\ncd5YRV3iliDqJM9Ws3vXKl7V6HBWLwOU5Blzuey7R4eagJcjmVA5iViA8K+5\nWaFf2InfjtgwH1nzUY0xLtDb9yku0lK3mehsmMmju5+vIqTjrpLIOnfk0iRV\n0AS+ZodoIZrnnpCLtqPxETNVM4VO7iC8oCRqryacycx7rIwuWv2KnacJwKGa\nGxvDVaUt6uLf9l51gcFpdKx0XFQaGCfVEBqUaoahcTmvIAe11cZ17sx5AEDe\nZoe0ScnD7E834Iqhz1VO2FhVR1dGfk3MqZa96J46iDykdjPVksa+F5S5SPLq\nG4fBCm/3ls0xwAd8ztK3chjEdu4Pe9IhCkEFsVNeymTmBZikbkj/H2xKiPNx\nVUUXow0I9US5Vjn09aPwu57n8FW9xiexFB524GXOY8WspjXCbFvj6LhpAKTl\n7YfkbKrJi/w0dKw3etiWx+kPMzoOltEaQUnJauo7gL203UstUsKrxGYazUdT\nSwrx3xynDUIFSDD+Ho9L9iMMtJrDp7E2+WFjIb6KJxD2XA5iAu3wBQb40CKg\nge1YYR6HkzOcc1jRGTLnBjtAaPf9BgcDRBzH337qgC1pxCd1vwYYdwKxy8Ms\nk1EeE4ccS/8duNA6cZeFuTYPlOGPxYRFIFC4EA1wnD5j7eVEtlgc+lNFbklv\nEplh\r\n=KKJQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.2": {"name": "@hookform/resolvers", "version": "2.0.0-beta.2", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.11.1", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "6d95bebcca728cff17c1582a1d4556ae5c25f127", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.2.tgz", "fileCount": 93, "integrity": "sha512-B3Xo5fIGwuh0zf0yFGoC3JNac5QqmvwB5TvDtjRN7l1rUCeUyv+TKKkMfIxRTiM70XVQAPNUSxtRcRnBpsZfnA==", "signatures": [{"sig": "MEUCIH1Cupx1cUjhfzKbfMUbhSSpPZVKaOxZZNmlychSncL+AiEA3raKX3ItTNWRVnr4JyjHgFBhnjYR6P9yJj7UTjWki5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9F5hCRA9TVsSAnZWagAAoKkP/ibsWhkQ6XVzr5JA5eno\nEHzz3A8Pj6H9gKbz7qdvXqs0qbCqISTV3svcslwIUBD9oIf8XzXx3TnHw98A\np2gxGoc4onsI9D/qiQoNGWHEtyZ/+lllRGqZ9Ft2GtlrUEuQTFNTdoLY1Gkp\nwKIjMQ0bAPYYCx5NQfEj9smt+HWiQwjwzHOX5aKbixi2noMr9ZReCN8r1lAN\nQuEB049pwORw3SVpKCuaqWR2kaGAE2/rYCohL71a6h7d0f1RxJL3U1Rdv3W5\nYPUZu67/VP1cMwiRSu55wQktEhfUIEeQ/qyIq/nEtogQ/WGVCPO3P8VS2NpG\nQa3eQndwarS0NLdSk7KuVNgtXdbC4/eLLBj+I0fN0mpz26PP3NKRKUNKIDHj\nls4Wv38mZ9aFngzeznUVeLYuHcm4NH55hjX1LoR433k/RiVyFT9vTuTVG3E/\n+hVGQVmUN49Dytsn679m2cA3CGl9DuZ6vKFxgxpAT26VEaCJo0B3DD5MdgJx\nfNgoCzoUeeM4yTEjIyeljZoAwhUb9dhRP7BoXR6yKh/5VSeX9Zbg3d3yMvpE\nnCga9TfewUhbTfMjZ6LUuOxzpqpHtqoEWQulA/9PlygLJrkK9zeF3zu/t6Ez\nLk47iYa6LlN7Ztn3K7N9YybwNY3VV+Gld/pFuvK4LGHKmsu81HF4jKO8O4/E\nFkmK\r\n=O+kq\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.3": {"name": "@hookform/resolvers", "version": "2.0.0-beta.3", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.11.1", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "6a13cecb8d261fee9fc53564e3e4ef6212148f16", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.3.tgz", "fileCount": 93, "integrity": "sha512-sOP+IX7TglN34WbMVt8eRqWgnXAQcvFc4XUU6x3bIiIjTkjV5L3N7sBjff2Ln4QPTMYnmdXaZV2Yf5WxOiC5YQ==", "signatures": [{"sig": "MEQCIAYo0gnyLmIJfQwtJKH+SqdvqxnX4fMLSMDp9f+WZ7xPAiAioR7DWrwionSAdqrb2tePtwWWHwm0tMsD8nGheSHjGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9GJQCRA9TVsSAnZWagAAjg0P/0WE9cNc4TNFL0EElDWp\n5xw1LxCzYBLtqtCtC1BMdXg9gSuRYIKS1vDyQbTP+B7cT8AFc/3fxq90+ceA\nHbWuDjUJbNrB3efuyRJsE2kQ/94nMXgxwnEgVNT2aFL3MNNE3uv+QuapIQDb\nuTqwIGxXOSuAcNTQo/Fjz9BEF6X1edp1uL6Hlap4mb4WEV9IcjvNOCniYUqu\nxz0NqEHij3hfPwRduFHkZbXHHDN/LHoSZX7cICE4hMamCsdVdwAoiJINLeMG\nL89M/mUBHI4M/+GkDhUuVIgZaWx5TBFzKgFTGAy0wXrc59Q+JN++S3Qic3qr\nxoZcw+d4EHLfZs2konXy8MxIESyI16+hdn5y2FvHulJLJRlbNz+iFzk0mCl3\nfWqzarmNTSoOnMkkhs9qHS/R6PWn4SS60oRsHJoHD114eWTwwSp4jtjb9hwJ\n5YFw0Lf4tr8Rbpma8yoEzrBdVnOqtVtjm2F7jdb2th9X8MwP+BqCzvOWVCzw\n/OpOmmbpdTZfutwLRFVDbHYpuZQrIG1u+ubvGKIuasnLLMalBI0ozBaOCDXL\nWlo88qf96Ut5C8MgsFEJjsldXdpbxZq1mge4v+ppdrWKpHRJVEGnzHRCXM/2\n5KrCVPgmqTs0UqdKldcYEVgRazq5NiTzc6r6ezP6c1YMpA+Lp78TtqLmrMnG\nDypl\r\n=X6ES\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.4": {"name": "@hookform/resolvers", "version": "2.0.0-beta.4", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "@types/react": "^17.0.0", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "@testing-library/react": "^11.2.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@testing-library/jest-dom": "^5.11.8", "@typescript-eslint/parser": "^4.11.1", "@testing-library/user-event": "^12.6.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "8a60d3195818106e2162c26326b4b35ef6ca1053", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.4.tgz", "fileCount": 107, "integrity": "sha512-+j9Ill0C5ntLYs2D2eTe7sFtiMtL2uCox7aSWPJOAOFmZkljXEce3D/fn+MPM/ktzS8c3z1L6xF8a/9W9UPNqg==", "signatures": [{"sig": "MEUCIQDpGL4ZhTMKLa77UBcT2lFR+8YfBvIgswjae5fHO4tS4wIgNXS3Iii+L049puoBgrSuQOpAALIXA4DlvVC2dzCq4gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 271011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCUN9CRA9TVsSAnZWagAAhfkQAIk2NMteRKuRXv/j0pTA\nIbKKPEIauXcDkIDkrnucSf9OqaAvaacASppXlOpW7g81aBGmJMuOt2qn6SFN\nIR8dObX2fhvHcquK4Pf+qHitoOv1qGENs7RLfSv8fcEf8QAtOm/Qpe3pOsf1\nMgk5Z2OSHxwhpXOHY7Dp9NQ0gFhV4rFGowEz/8pHvhUK4kQ7a+Nt8xwhfVUs\nSTg6+iyQiveOgPCMOSDSJl9+c5zlQA2NF2RXwybfx+2xAPTa1QGNSI4FIgV2\nfnRyEbeVMRDfZBsdi+s7s30i8oUK+R5pOAryCjioX1upAMYFa8FMPIlsiAf2\nVek7ilnXrbjlJn8q4iHm546cMcQuSTdD+fd2OwLkA9NW198FOxkZxW35VPqY\nS6KiqFBzC54tdq9J9MgimtIi93q3Lcy0/KB1tIn0VtsNlUBixwO/GTyrO3oN\ndyq4evVZn7DVZVAtJL+iTh8pR3c/OK/uDJZQViHVQ3V3Ha1CJ3LwKVWv+lqU\nL4feoVj0tGOFMMrB2TbdxqXJl/63Fz5NREBhX8s6a0yeTB18GAUBZUeJUmz6\nr3WmZcxX1Z+efX5LUo0IKjCBTqgHWBvNAvJQKe2EWFMKIBI8H21MAMKq5TPQ\n3Oa40h47hMhRcqtfG/62Bt8VP7CNi88KyC6F8kStN5O6HnQfklkL6j867Z2Y\nJp5o\r\n=/nQj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.5": {"name": "@hookform/resolvers", "version": "2.0.0-beta.5", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.17.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "@types/react": "^17.0.0", "react-hook-form": "^6.14.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "@testing-library/react": "^11.2.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@testing-library/jest-dom": "^5.11.8", "@typescript-eslint/parser": "^4.11.1", "@testing-library/user-event": "^12.6.0", "@typescript-eslint/eslint-plugin": "^4.11.1"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "35b8c605973d201281649e5504a2e164185b4fe4", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.5.tgz", "fileCount": 107, "integrity": "sha512-q3DqKeYJyFvqkWUo3eQ2SJenI2lECuTeBZvp8WebjS80SaRY3+npC4Vx9ILk8hm3jinT95tN7mkl9hJrjyPkuA==", "signatures": [{"sig": "MEYCIQDATsfJ8m0MRqDr4HdoYRUot5ds9sdh6LtqA3lNeTkrCgIhANkIWBE17su4Br69djAx0rxzLPariSdQZO87AUnuz+eH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287393, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCoblCRA9TVsSAnZWagAACBoP/2lBeM7Ta3hblWGAGoFS\nnivGWjyQfN0TASBYuZGoQina2rXyvV1XMD8B/9lpXNOPmy8BerPWA1U7ErwI\n+eZzSmmWCgMhesffwfvos+mhiFlkJOkxc2CvcOEYU1D/JaDX00LbfF314xD+\nDmVJipIpys+2pgyv1qrlPPhYhr/h+/oah8yXvh4ZYdf58VX+B6L5Dnz7rk7S\nGdIV9dW/wiXN+IJ6j1kSgneBBXKv2M6d9duOfjR2DcW57TeTObU8lxr5WuBf\nPMYCKR1m2Ss0ZX61hRZWoZH3WavfZJiuQipA58PSpMUVVyIHDLvwpfuV2ZlS\nd3dM7guIQGFyHwOcLO8TkBqXBggnqRUxWxkGkF1qYe0f3CcpTN/j3ps0mA4G\nyjV4bTsLbJQHYGgPat/6wbRqXmRhvO+0lo51DvXxBBgfOJ9lHVPEce0IWTMz\nEMOn3gB8LoClZ8WbXdyzo+A48v5uA3qtkOD4swJByJLMrUopvdSVd32nnjiL\ntjPxLjxIQ10YsFCsf7hM3mGH9+b1t3CGNlCTqA0NL4UqBCWkH+aeNEikWsKJ\nJO/V/q4LsOYE1yQaQO4dfExNJ0PwR8W/oI+EQ1buryZR36LI7dnvZEhTu65R\n33tBRkrcgJFFp6q10lq5axagLTuzclq5egDDr/hHbIGZVWBYpEt3t3SaTG5P\nuNLn\r\n=dFcS\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.6": {"name": "@hookform/resolvers", "version": "2.0.0-beta.6", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.1", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.18.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.3", "@types/react": "^17.0.0", "react-hook-form": "^6.14.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.6", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.0", "@testing-library/user-event": "^12.6.2", "@typescript-eslint/eslint-plugin": "^4.14.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "3ba071e0e4933dc2d57727d6d49e612e496a4fbb", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.6.tgz", "fileCount": 108, "integrity": "sha512-sqqtvbbuQFAlQ9UtYHZXOvQ4MYj6gwJHEdQNZIPQDACKstARbc5e5maNV30IRQR1HQ60Ycf3keMqefkwdFStYA==", "signatures": [{"sig": "MEQCICRUh8IOt+DAS/zZrEdrJNdeH0+zHIRZ5U8hImwm/6GtAiAzDSjlUxTUPn18Rlmcl9wN05izOSAkGw5wXMLZ8xsIxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 291583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDG43CRA9TVsSAnZWagAAS/sQAJhRRzXVurSFRuf8BqCU\nX5GS+X2OzkYxC7Rha/wF4tTiHXy4sKQ/xkLtVrDDzvUsXkGEwArFXsJO4IER\nlx87nIhQD5kVjkF2iMW4OjyNNYFLeIUTQacFksWYuUtRxXXQfzUnxo/SWXOu\nnwFAu8law1VN6He9NMDKgxuThjtPMkkzSNmCR6EuFSWyl+fQft1OeLqWs7M6\n70k/Swf7Uh4E6S+FkE13lQ0YS9YVeuGEVTOAyGA8IwAA1VOvRrWIdG5czcdr\nPHq0bL0bA+p+ii/2Nmvu5sghNuoyeCxxPHhMheNknl1/Zr65OG0hydoBO/M7\nCiXknqRwlAztvnRwo4jfd3HZHUZBOjKjB/bCdMR9WLyWFlTlrjU91gYKcQIj\naPkpAk8exwfnBfkPzHBIao9ZgT/7PCdOgO1JYWa4Tdb87kQgx+N11pNJ+fuO\nLvBp8sXHymmn0kAn9qR4/BXZ0j7vmbS+gTrZ589RVbcTA8WDdoaOB5yuzZ8E\nIaiIRQ/osGPPTK54FkkbxRtFsCu4nt6T3BITPFMDUWLw5BzOWRP7KqG9rm6J\nQYwFnroWnqLTjCKH9+s8G5F7KU/JUhdjxubkLTXiHXAG471z7SuPLdjWciK7\n7jDrgnls6j/F0UFGpWImzzeQ3sNY9yARATJOrBby2CKkFJ814+aS/LyWZuNR\nMWCl\r\n=0Ans\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.7": {"name": "@hookform/resolvers", "version": "2.0.0-beta.7", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.1", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.18.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.3", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.0", "check-export-map": "^1.0.1", "semantic-release": "^17.3.6", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.0", "@testing-library/user-event": "^12.6.2", "@typescript-eslint/eslint-plugin": "^4.14.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "ca6359185f1681f994599d0c47a2e39d6f0a28d8", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.7.tgz", "fileCount": 108, "integrity": "sha512-Am0TVvFF1SsKjIig/ePYWxzLDiaWSxoHasNL3LCSP+usc6t8xUmPrKkKpTEc2AVic7xPkfNJ8PZVPx+yKRkXQQ==", "signatures": [{"sig": "MEUCICpntZkAgGGO4UvQG51qAv0oT4qar7GhjRDROkLjg9GbAiEA5nVLEIuKtYhqlh7+6kJf1u24K1A+fghQFfRR6krLp+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 292777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEm6FCRA9TVsSAnZWagAAA4cP/2vxhpLkwmXqgyPY6mqg\n06gjdvv1SQE/twVhwuVrPq5dSJIVXMmNI4mxUO7zupKV70XKhAcbcVphlcz5\n20GUtjACAxtmlupFdU8jDtqV9F7yYoZR8/EqbSnwh8oTWeXFsL5ILM66ivsN\nA4JLS2ucqbP7nfcE5jvwH4JcTfDCqiIQqYfWBF3C0mmXwu1uJaiYL5jbnhvy\n0AdMcvV1OuJNgSTNUrIMdurYjMGfIMNETKKyLXNaVTEGCXjRp3KDmz1pfs9W\nJvajoAeGJeFo4lWD+Bv+U8865iQgzMpNSnRtIS4Ku9K5nh1V3S4kf44J0nqN\nff0rwidOIBon9KDmS40GSKpQYpFU61814F4uAJfAMB78LfxvbRWlrCeey+mc\nU3eoN2g16jUk9IcN4caIFzC5IOa8nWcN03nwvd9tkFBLMypMrRa3feJhl2iY\nBgyE367T0iw0kmN8PRYiCXuBJEJQrutRGoXqneR1+vhE74YSqFiQFzbxgOSN\nIsUq3eqgEWKOVBb56x71pwjbqvNdZuh4eJmMCoDYdELPSV5T0dakCyEfixgk\n7Q+WI0yWm+JMyVjlfTIQer+RcsmZ8Hh+OtneWui4DVB4gI5IyhyOt4eN7RRI\nGBtn5JYBIXpd18gsyjxP6u+XRzpQMn5+TQicePY1csbuCJjgYtrBd8+AQwBa\nftOE\r\n=6l4e\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.3": {"name": "@hookform/resolvers", "version": "1.3.3", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "semantic-release": "^17.3.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "db61c3e7155c52a09c62ec62078fba2d65e0ee99", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.3.tgz", "fileCount": 54, "integrity": "sha512-L4UYT8HRr9GzIHVMMgi1NOwCOZqFIcCAfGHrsP13j2dp3BcfrH9bhU+XWQTHg9/M/Qt2t7PZBXTW4UZilJCJrA==", "signatures": [{"sig": "MEUCIQDonLO5MMmR2WXLsBmU5yc6wvY3z6gsgZNJ9t+zvkk9RgIgGQL81rZ++p5HvXYFM9H9Yc4kIiJV0zCYNH8qqPjRjI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFzC5CRA9TVsSAnZWagAArv0P/36o9NhGzWjwx0qhjbP7\nJX5DJP6Itdfk5NoC/Fm+kN1fmwH6qWl52TTWkyjsjXzb4LsH0B/3O0duJE8D\nlAr8aXsCwh0MTeyraEDNbyU+0NDMSfVS1Sn7XF8ct33DChOGHoJ+fYJdOGh8\nmYh8rOp1Jy6F9RID554d0m286y/X9OMUutUN7p2McAyqfh9o9Sdrq70sN4U1\nhjVwIVdIXZPNKJirsRrL0aPypayLDgcW+6w1eI1SGf3wFE0fstLQc8bYfqqg\nomGkKudPlOJWdbxpLLWX7Ud0hGB+6GVi2Da4LS2Gywbw2DYBHKWMUtsRhDTb\nPxCEOzL4Mcf64pxDpiNRkKFAM/5BRVHVaKY5iUz+BBvWGY/Vj6UesvAIy2Px\no2JWh62yJ1MCdowVlNbHQayyrOI0Bnf5UWWYd1WIXHRZ+U1koF2BxNKf13a0\ng8m5cIyOQl68vPn2N3FESIXa5Oo508wO31uEF2/miwi8ILT/VKEs9XSqsMxX\neo1BSTfAAtSzOYI6h491yfgH7T+7BAstrl+5KMCHsVwFvJ4BhzNOYoBCDAgE\nVqjRqmqtv9OFUeWoPscKPe06vB7FrRWINbFOzbHtGBp8R+wycUU7tGYv3xcf\nsVCcBGWs6kq4LRRvPf6Ca2XL7F6ILmrcSYWmXiMEB+SLifNoNk3vLAiBXi2w\n1fT9\r\n=JjMV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.4": {"name": "@hookform/resolvers", "version": "1.3.4", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "semantic-release": "^17.3.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "3129243180a5ecc4347b9e66ad693148215c2986", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.4.tgz", "fileCount": 54, "integrity": "sha512-K56VLSInXNIT/r14pkzRn1FJclqzGOWqpe3Bf0kz2Hf98ZOmRRFh4fhB7F3ofqCQ03CEQQkV44CTg7ql6nEvEg==", "signatures": [{"sig": "MEUCIQCH70P1IhbrhWEUe2umTlf3ifh6cKSNMEZUGY4ElABz+QIgAbE+xl4wRGEN9OJrlwM7d351D1VcKLfhmkFzhyk2Slg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGQ8rCRA9TVsSAnZWagAAj0UP/1kpy86mAUyTug+Vxzmk\nLw7mBiwGoD6wbsdqVFqA0lJDEMHRAN2gz1YBH5CabGd6D4GcZUYWbGw6vmVH\n23sHT1TbJ2B/PPzQivMBBAb1HkiZuEY03iwoIBsLuv5eCfsfVXjO4unLMtuV\n9/snZdbHsKM/R4+KVWsPFBY9hWA9mFqICnQQesNXmYB4efdbnHypYQYJlpzX\nsxSBe8NfvipoEY0SeQheEOLj5tuifTbd8499/4oRVzaHAz63OTrNb/htOpqh\nt1Y5aSWawJbCJLRTlckuFTco/FlKZ8O7VS8aFVvXWw0NcAEmofSRedxt+eBT\nVTFveP2WrBMDx8QocyvfSBo29VI+b5ZfWC33gBed7dxYJLVG48Ol7rSe3PLf\nS+1Q/WC1iNGMVNOp4Uq3hGeNkm1Lp3wHzWwX+lVF6LrrAaOb/fMCSozpp9Ee\n5viPy8JiZGAHRzkjiMaJskBpJWGz2m01zxOwM464+yLScuYm6zvP0ex9mNWo\nDQCFvmPx0jABRMiILKfJUqTzz+uronI9DVEMle1KXozDFdOdk2OK25TXCgAL\nd+kjXDfE76xERDbmLg0nwTn9E37mGQTyDsC56ksSSuPVPHU3JVzpBtEXuZ5y\n0vH3mb4tbM9/DWdjoE7VI+eKmStSVwQOrPNoizgOR9c7W1fwjWjo5qyI+jaN\nvlnI\r\n=zn80\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.8": {"name": "@hookform/resolvers", "version": "2.0.0-beta.8", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "58d09a0d84800198b916160b159d16808874dd8e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.8.tgz", "fileCount": 109, "integrity": "sha512-pyZBJ6zd+Ehhuh34NhsmZnUVdsso5yt08sTHGalwuAUMsiLPPOfK6qLj2ZepSjOcTqj5oka2qXCIy/yejpztmg==", "signatures": [{"sig": "MEQCIFb2EgxC9cxAaIdW5rF1cBT6dOscc2MWFr+qlkStpwaDAiBZVZ3sjNUyh1qw2XTDRCUM5iwFmZQzfR2ir9Dn/DSWOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGcmdCRA9TVsSAnZWagAAvUIP/jhTR25BrFVekIwP41qo\nCINwTBH63RrKzsyyK7ot8K+F+tFwJDcq/f1yNZCYtumah4DR152Do46Iq1rM\nePQ5CQ+WQ42etDpKjXW9aXP4hHtlKRLHsas6n4d84CoN0JUtqZE8nLtGTFeB\nHLUcdX5iZIdxzGoNdgkoAx+R+JDvpPWzEx1Oh2ABczzKDeB/1SwNovJf2L8I\nk0CogKHbgvDa9eJ4FbJ5ybGBYBsDoj6OLWpqBMdrD5+8CZPRfFXijsIWLjAr\nZLNx+UaiNTJaAAGDgrkU0swEJ7tc4fJbiW8muY3HeUXEoBw1oFXXY3cfaEoc\nuV0c3dJ3acTpkTs/TmxxZWwIs9wGeKDM/ygkY0G9ApBfWPLCoKB5MLfLOQAF\nWircsaQW0S6ul8sJ2JJiS3/tYTzsdJQz2RzeTVafNx8YrIb+vYr8Uw8pVPmC\nG3nBFgTIE5jYceGlDiZd/mSjXy8QgHPEbTOaBJQCZbVIayxcaaN0y+tiTt5R\nZJy9hn2bqobkXFV4JWBTMenQaptqNM0Pf7XCm3cLMmpg6IajTOSMR02CtuJr\nWrvQPOiP77h0RIugVXLbvCvsKwmHLNDRt8bLFfSuyT4wzvnDOQ3SISfIxESW\nRTwcKEv0MkS9qjGr5bBSLX+RAr3YxJv4bRgsWGK8O1jvfwYi7pdaGJxioaa5\n/9cz\r\n=7/jA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.9": {"name": "@hookform/resolvers", "version": "2.0.0-beta.9", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "6c714dc2a86004acb498aaf0d20cf33eb93c92ec", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.9.tgz", "fileCount": 110, "integrity": "sha512-aJOBMHJAU7An69KddkwkR75ZdwSNNvC5h2p6PBiTuKbeqN/ztuMwWwvfNj54IrdL/9KJ1zh495Iitbjha0j1JQ==", "signatures": [{"sig": "MEYCIQCTGBCt5edPMFBniThKs72QHDvJxEPD8p7iq6U4RLDhyAIhAOz6at8OQ+tMkT81cGra19/Xu2amsxSx4cmNXU3FwzcC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGl4kCRA9TVsSAnZWagAA7wUQAJwQFJEyl4EkzYMcgVtu\nH2bZmSipTG5jxcWUKXTexQgOJd2tt7qQuz37cTkKdLjQfwxwrGu/naoQzfaU\ntHfnqeCZmwpG2EqddwK+Oxay2iYyCI6K29n9BQ8O259+53lmxVnn8U7r9Zws\nZmW+IH9JvZoDm9T8TE8IMYIgc/go9C9h2vcK6kDZPFdXDhaKZSVbKN+rXGUa\nmXOKi/8QhS7uzbQe0K+tszM8BET8TJIPd6/fxrhOsghEOB4pHf94++QWI2Sb\n0ihMpCHvvuNOHqXjUUad1SNtzyU98a27CdDdNSe2ipxl2RPRIqlVsTP5l8An\nf6xVFJfE/l65tJ0KT+/jhmuGgyt3dwSOxozpsLogyvZ7kSVH1JdU7HFpVdHv\nYNPiD7LTfttWBQJPHHEFKFNwZ6OCP8g/KuAKE8hvdRp/t34cv4PMX7ttlCjH\nmli54x/XFh5MVhlcjCmaIf/y2pbBh/iKwnZ/rDaokgsAD8NGNQAATTEvMa2S\nqqsO4Wo/fEw7tXARF8GuPp8RkOBUK3pStlmirUFWgQEV/XfLLkIep/yLzeT3\niGRDIZ1ibebbG1A4t9Vf2asi/ZToEVRcLDrSYOypfmnXQM0ISCIZckF08zn5\nDl+F0pt+zKWRwpr3bE9dvoU1QlyOlfk51zgGaKuOrB9Yl/9TcS+lo5X8i47s\n79Tx\r\n=dWA9\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.10": {"name": "@hookform/resolvers", "version": "2.0.0-beta.10", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "9ff880398a64553ab7af05114b61020a2916a3d8", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.10.tgz", "fileCount": 114, "integrity": "sha512-BoVmsYsQy2NSyuC3Ibj+jWttzxiC5DxHnseTxrmaZXsWneiNI2rOV70BrRo5fh2Y0dkwXZzNWJmTfaZlETcCaQ==", "signatures": [{"sig": "MEQCIE+5dZxGRxgPD2hDfgBfEvmDus7CYa2+GtN9BlhajU7pAiAhM8Huay4TfuHdCCmYfksBUolf5wsmtRnMHoYgMO+tjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 313998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGxvGCRA9TVsSAnZWagAACuQP/0x7QumW4Jd578HXwWxO\nXJjku6oVxwLHpbKbl/84cgt631ih3fpI4JGMhdf1crtzEtakAEfZ2RPAt1Sp\nx9IIT3GSssEU7PGLkCKIHrYhb7465txJl4Bry8oKmLndhRTeQez6RjtSUP6p\ntwHnhpC5rgvIsjZIFbsDXDW8wQuIiet8VxSpVDZ50XLZfsvPZTQvSNknVL5Z\n6lTclCLZOz5NL4QRAGwwH8DXB6hrDNccSCAegz05NYuy7HhRHZ1QQDR2S3nH\nIG0vlXw511zYn6WBnk2V6MQCVaP5RykizV8BFOGLgkd+eB5+BjD6GObGuHMr\nhXUHhL01vcbVnBBq/NFkhI52EwyZid0913fnFxhvJSXqoNHmC7FK/tZeyuFa\n6boTCwZJ34qFX9QDAr15QQrB9yu3h+Qkl4lnHOnAJNblxgUpcpzittFjzVXk\nMu0znOGClEu2P0NEr0Ivs8MsQvKPiKHNjP4Sg7OUTzFKekWzO3VCNh64LPdC\nrslF7hG5G1IsMZ7eT6cBC+B+GBLRmy4HHYx6shRCghZ4oK1Ue8eITe7c7wSQ\nEa7c/INogCAzM7cmWl7nWn10GbwPX04bJYWfBXCtBkIr33+CEc11bduhnEF/\nPg22L1Ii+Wo5edmU7stKFxSjnwQFltnBAIgAppaElnFz8ZK2kINsyj4v+CXN\nCc+x\r\n=gOqF\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.11": {"name": "@hookform/resolvers", "version": "2.0.0-beta.11", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "f42b0c0601e477a31b6d5bbf45325b408c56e6a4", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.11.tgz", "fileCount": 114, "integrity": "sha512-uXnymG9qdDxyeP8UrLk2dV3RY/3c2xDLd5WV2k0bHJv+puCekHWZpg3Zob/vxEjzWPe4xXdDsNKqoBDBJDtgOA==", "signatures": [{"sig": "MEUCIB2pmKBI1a5J5MeqhB6rHVtdvdaj+f2eatXC5BA5jssaAiEAt5W1uL2FsKe2GC4k43wYBr5ehlaF/MZdXGTq4Cllwu8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGyHTCRA9TVsSAnZWagAAhnEP/0hQgNbhwQrCTWQl4XoL\nMP47gNf/quBD53q4vWLspdTue4oLdxrmgE0P6HX4TKoCTvHzBBL/2Td3lL46\nQPNWjKQD5DWGeqByDxMXzyzrKuCziOyoxfDN9RPD8eifdzgwXai5OeyfwQEY\n7pDnZDZZIPy2D3dnveoDzZeSGGZP29WiRwbPn3/WXX0g8rVWpB9Gu5jHkyXX\nROBqkSso1Oy5hJ5KI/9oxrAX45LnVdYxtuGVK3lOR8i/ZrKEJsXFMBcXKiDl\nMZvz/s0CKl1JUsaFdQY+qrCmoMfvZronAIyxL/V59UQExQTlaQSn7vBVvZLs\nBJJw4ujWyJDwzHDa6/DVVov5w4RYZOzN2KTD1gTbNSjlfWObYQOLEIxq7hfn\nhQ/mAeTiTBsaDOAjQ6/mvB7NcgHRSmd7X16L2OXy7UjzVNia5TBbxbxuhkkr\nvP29u5IdNqFdiCUcVAOmq9xAHKUAx6p7rGV3455Dx1chkWPO2O4i6CqyuI+p\nWX2eC7jdRtLxaf+zB02KEQBLHZaTW5j/o7wc+W50yFZOeOiUnGHkVx9SZJbl\nPgp3jeWIThgCLlZaX75TE+gAFIRIgbBVW+MGtyC80fWn3EMePlFKEMZZkuIA\n/AIUK1UbIpU+TBEMaikXCwVMnWPbMAFjscU2PTZT17MEKjwLV/3BksazLAqH\n343m\r\n=TR6Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.12": {"name": "@hookform/resolvers", "version": "2.0.0-beta.12", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "dbfdfd24e8747896727711da310cf952fb934c1e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.12.tgz", "fileCount": 114, "integrity": "sha512-Wczk97z3gBxmX4AKYibrj5qllQrI60AR9pdHMYMUlGs7TXMiQpgWSBGprHjO6GIN3gGO7FaaPfGGJbxN866OyQ==", "signatures": [{"sig": "MEUCIQC9nvB+6tOdQUgUAG3l7lIlf2iTIG5BFA+dapFOMxZTOgIgV4HheK8aGnQq/OCx5LXHWCzAwUN4i+w5NKglUPXvSy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 161932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHlFgCRA9TVsSAnZWagAAmRwQAIHoPBMz3HR5nMVxl4GA\nKvzSOm7O1Do7qqpKECU5kQ3IYUAwIVveybG/TRSl131S9BGVwfupVdkdz8SQ\nL9TSk3dHdyEYjP5u+m5vAulQfGpUPVDDid4bosCYOnb8JwY2jPEy4MkE8TF0\nN/l0TKeMsOHVazkIxIe1W/YVlUeRrOeMthlf+0b9+OBqaxWxBY8gfknM3WtF\noSIN3X97A4DmYCYSx65Jyt/gq53tXoinUBHbXY3cp6pqfveNAkO/5OtE1PCl\nWVJXQvsXHC8dJtcx0DiK0Z4QWxCJS0xHkJGxqdJTI2p8xU4Avc2AWhX71UnC\nHEFml6eNjHsfzJ45eN6iwm/zjeS/1kOyC5QuwslK8ALKfupJUAr0/PvuNFsr\nU4xoTIWkOoafOZemmd0LEupgASFfOqzmLaqGDM5YaAEBdfqLdpRil8FkRQk1\ns+3z4EM/y7avlLbr/awRMFh2IC5nAnp/3spw5/YCKWn76AsNxbxVFwNkypxt\nwJvYJsyvuT3hJyiBHeHYiA5eoMgB4DUqyiz5TvJ5H+jr3BqH/4wJmM9ZxnmY\nfR/OvTpN5rGGh32zXfCatJtm3nZBsFEdY7WxI0cbkFG9iV4tgvKzJUxUDtpl\nUXRaE0S3ig+6clRTFcmnx53OSkqg90ArVaLdxPULno7C2BigBub49CM+HfTH\n1rqU\r\n=h+u3\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.13": {"name": "@hookform/resolvers", "version": "2.0.0-beta.13", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "d92bbd4505af21a4947ad7bf6da4ec229afba902", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.13.tgz", "fileCount": 113, "integrity": "sha512-7fkqvN8L19AqQqwxAdd/zPU2Sk1d//ff+9XR/sp+H8hJMfVXvDWHy0fshbArucFO2+La2T8euck8hE4qAzfIkg==", "signatures": [{"sig": "MEYCIQCrMfgFN/wVDEDoskzWxWrFR3fLDV3JgZaIPrGV+T4aqwIhAMFymmQCaUQijc3QcvKnEhIxICe6D6Gh5gCGQhEHUT9P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKjIdCRA9TVsSAnZWagAA+XwP/0G+KXFidKGoddj9HPIr\nXibAyoRl85a16pQy3ZfdKnuhPmBnKNmq+6jyj4W62gWVRjcEBiD5zk8KRHqn\nwocjOj+jxXJ9Wnr2TrttBlDeHYrnWCo9cTKg5Q7e9rkUw+id9Rx5KSjb/VG5\n7gL4mrYx2nzy0Fg2kXRENdiK8q4t1hC3dcByA4OfCSrGa6XWhYzMYQJp5wrF\n+J/MfYigO1CChmQG0PBBlmpjHz7Ev6Iay+TK+vbkWz9SqMV0kBCcLbgBUZY+\n2<PERSON>les<PERSON>+ddpxrQCrdqs9wXo01eRDSNgksr2EPP4MXobzQDhMkuTYSQD1mVTL\nQbordxXClKD6zIlbp+DHvmp1k5zmc1wxHybrzsbqRU/v3TG4t/sA0pZ6+r3D\n3KgOePd41/suCLrk3QQmddH2WcVSpSXDz4xsTwIk8FxXofnB9JUQevJFGOa7\nPt6AQO8OWvujVl6NJIbsa7FFtwWkd9qmDDi6r+tTZ9snwgxblJJE0uClTadr\nNcg6vWEXpJYY4NtQEuWwYKXZ2cnqxr2o+M6Kk2jTEiVd0dQb6xaazDMX8Vzs\nfgGEsOGXcSJm6BdzQfdYQRszunIgz/kz6cfg4JaBcfHF76wpzZYmZd1zqvZF\nPjCO/cHY7Whw6IBSRkFwrBuZ98WzA2oGok4a8Db6AJg0iMcE37wFwavw7AW7\nCvuE\r\n=ZCdX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.5": {"name": "@hookform/resolvers", "version": "1.3.5", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "rimraf": "^3.0.2", "rollup": "^2.35.1", "matched": "^5.0.1", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "semantic-release": "^17.3.1", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@rollup/plugin-commonjs": "^17.0.0", "@typescript-eslint/parser": "^4.10.0", "rollup-plugin-typescript2": "^0.29.0", "@rollup/plugin-node-resolve": "^11.0.1", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "554a4b1d5a3ff89551419b1eb963302f8021d0c9", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.5.tgz", "fileCount": 54, "integrity": "sha512-R+bX8y2uk9buIhFrngmRRQMtpGjqZNDnCbANIbV1kIgAu5D6o+frFK<PERSON>rewmicYKB2kowGyRJEx50Mu/nUdAeaA==", "signatures": [{"sig": "MEUCIQCUwOcNw/kcUOhAJsKmjzogMRERqnIS2poz010qN9gkrAIgBDnaID4IPqTRWyF0NtweAmjB8hMCFenhGN3/hxa1kHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgONcPCRA9TVsSAnZWagAA7qAP/RwczV8ONXNpNtDPvs4K\njgxmWhzOwFoIX1UvH2gipURn6JyCK6hgBRHHHjKTlcOH0PS1Cmb/hJhRgeaF\n58JNty5rlJXdeNND7bepqT0GIcVsBg9vUP2zA3t8UWoKd0Tf7UJSweEYBjPm\n18mpIvY7c67fWCBcyhe5XwEf30eMuHZqcfm/ZnEz8nSNXp2qpW/MQoh+4FEZ\nFwPnNq1n5Am+0mfOVNkAe4UfFL1bv2YOZ0Rr9S3NCyXSIxsdTiDxxjbOXMCk\n+y7ecHtlaznAnvNTCL3RQsVlrI8Th3IosmxAduFTF9VxmVevchUvl3vbXzdA\nyZcz/pLghA5tdFKfzw2nvYR4Pcp0IxTBun24PUQQLhOadwnxwGGCh6HnaNYw\nD2M5PD6M4UEjxn+DBaFubY0Wej+n8EoSWJ/PPnvWYlptzvx5BOnmvzq33A+a\nhVcq4uJaygPmyjXKM7EnD22rY853xfimDE9mT/MvBaRLc4Msn5n+Z9/eKzgZ\npOro7ARpQl99JA9s+I9K0UUEpD7Mz2KuyPxE0WcFIgd/GiwpA7m+kvFoTp3g\nxhZgGClGw4CwWu+4gRPdujhlBi+lpb1rGAjZ6oAlOsgmsfKG8MkYw2uDpOe5\n+Qjf4oAkPjWxqTq+Ew9ga7jgLQuxsZdRfPkxBbGfaJjQpFn6whJjJA4oSFW1\nWJ5F\r\n=Xgl8\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.14": {"name": "@hookform/resolvers", "version": "2.0.0-beta.14", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "fe1bd045caa05b1e928ce2d27185c8e3d63da313", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.14.tgz", "fileCount": 113, "integrity": "sha512-Msy7DmoTrf/f5lAdppgCSF1Oe44O6XmXVG19I9F3HgWNEjo2wRP0CCbMrSvuC0u08A9SqpqigilAGIh8mm1pTw==", "signatures": [{"sig": "MEQCIFrNQbDd+HA9oD2JSaXgkUC0U+USd1cMWElomlVHbjU+AiBS3T4nl9gHzxP7DFwlwbNT+cWkvnTNqdg3HcB/Fa6I/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgORwoCRA9TVsSAnZWagAAVlgP/iyhzchk5rgmEmpuSXHT\ncohkBHYnwkYXYqf/6BylMtQyl31LYhoe9wpEYNuWfK7LsAtnzc8URln0fJzq\n9JGhAQMGDLCwKlkd+NOqaWyl/dcYPcLdi7I/UpZfqYRn7nno4eANeSja8t23\nUpz3S0HSDhmOeNLbhMpJJ8dWdlMxcIthh70o+549Op76T8M15S6L3w9scuUJ\nN/GNtMOTgqWgG2chrBqElr/kqvskPIJ3z3+L01xIF7TNAjEEUKcZuoTqvvor\nOI+cMT8pkkK7jUrgdEcsfySUnwBFPcgWdPGKb+14TbCqFCuD/0IFOFpB/E7a\n1+TYr8YUk6YNRogcMMUZ8y5ugQgA0WaRj1OqobcxJtCIrQdrdhYR5yjGION4\nHyj/wDRv798e27N13+uP14sfDn7ba8g1tYXg6QkN/JIxkUGJiY7D6qesUoWN\nUkFu7o5ycfffi50xyKCe2meWtupFmxkefgSCwyNSdHh44qVGcNZ5b94csZ9J\nENMDRcRwSTw/ciosWLdNPVw4qXGck8Zp7abq0hNudmFd2kcBHCP6XgAIqTGm\nx40JiHDClRoEd6Adhg6ajInbCZqHw9g28oCm2neUzC45qIDAUEOhzlVg00IQ\nJH/LYjEYDlL09vzywpmgIqe3l7VFZS3ju+jQ4EXvzXFbU54cSHBWsVTv50Iq\n1DSU\r\n=/GXM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.15": {"name": "@hookform/resolvers", "version": "2.0.0-beta.15", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "00ead278be1b5e8ead7fd732f0e6328e3d27681d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.15.tgz", "fileCount": 113, "integrity": "sha512-T09hk26rodv00MSRWGEb2UnP+Dnc+JxqubVqUX3VCmnfg5spEe8heQ99OfkU2D/idLxIgkc3pMB2VAmXmpK3fg==", "signatures": [{"sig": "MEYCIQCIYYu6Zvl6uU6FfoLLh31lMCAhHxUmKHYPP+Aq1vXCPAIhAJzvxYwrWSpjox7yX4+FE5HVBzASE/jmGu6y8YoO7lKH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgOSAFCRA9TVsSAnZWagAABQQQAIRBjGRRm/Y4Qg4FMvZx\nSsSWl+vlb5VZhslrTHOhfFLNFoYRODg7zY96ydt9IHmSpjImFRSWGsnufaan\noSGNZGx1uUmQ2kY6NJ67asyFvAy46cWU5LJdS4kJ2u1IcNbUNDdOQp9IKd9T\nwRsz9w6fJn7RRO1BoAnW4MaYi+4iKmsZ+zhc17jhIKtJhvnuyV1awIowL92e\nf91cZqbRykgFSfitbMnrc546RPljsp8VRWuVCuQmHuufqTXTZZ3jDsgBdsXT\nbAQ8SENrGMSkDsGQv9uK3F0sAJY6enBxI9iE473k/EL27Yhk7754rX3pyUiA\n3q5Li4aOQtBf2kE4gSYf9ySrlv8mrdICMaA2GKVgAy6ZQADXFqge2rg5g9X7\nafZTFw3KWFruXBxQscfL2UfJ5b3RTABLEIfJBkwyHk6zOLTFNEmRhzlXqYg9\nbmBZZyLn+OtLkQIxmLuT8EG7oIqkzDGHRJna6oZY6wl/V8ekfm+rax6R6Pue\nk1RFuiIOFNQ+0Urn2EFVFQl77qNG4EMJKN0roXnL+asH7P30rsYC6XVu1o/q\nk+wwvi/VHmpqkQgA1TgMWh/RYv0wsdAH0zWtqwktd990vZLSdSkTSQT5VvKO\nfN95DodjYNHBKrugQU1XkCM0ydSPUQpfrCFaNjQdP+bGWZcAKLYDXswfNJCW\nnATB\r\n=Hbxu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.6": {"name": "@hookform/resolvers", "version": "1.3.6", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "ts-jest": "^26.4.4", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.10.0", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "e563616f946a3907315ddfef5e8d0f27b27621d7", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.6.tgz", "fileCount": 93, "integrity": "sha512-cXVEEk2tQbNZ+NVBjh5etuLNgHB0Cy39SbnAIl81GgDXpSuMPfDHmDyOp/58Y2Rh7dMR+j9fG+6sFAKbhRdVnA==", "signatures": [{"sig": "MEUCIQD/ErZKqLoPkUsWb/OD72dY2QOVex22PGjVWKZol3uNawIgL9qdc34vSvXOB/vuyQC+wrwLMVjANXRmXOOqeCC/Dho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 267723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbw7CRA9TVsSAnZWagAAEaAQAI9J85HD2/21NVh+3p1e\ndQmROM4xKysRziRD35iLkgqWYG/BqKPDwErGc/zLHYNPt8pTNwgZa5jX/BSc\nR5gCjkEaYXMyVjiycYnIFw7VwFUa3o0yrjZaDUfIeIdXuxMZEH76g9C7g3UM\nVuBf9bSTgEl1wcBtJDe0/VabHy1zZN61pJVPdIzDOZCBPA22UwizBDD5tRaU\nH5RYVxuv312xzTv0dp2JyOikt9Cv8EBsOA3SSPuYZ/1AZmy8n0iUQCmD32ta\nNllP+ieFLw26VwOZxyzp1G22XIFUovqtTdW04uq0pwt0V+CfxSv96ylggqHt\ne1sE4nWeImHYaH7T2UhaVXwmvy8oS59ORzWVVjJb6TokbQbkP9D1vr/wOFAD\nPpq1YWOZifJEFxJXcG/f1w1FrXAuurwFtn0EfAyRSR2CBUWptBz2ytJ2rZye\nsXWrUw7AJa7H83sdd8yfVGZZv11phx2EqEM0hDVcfemO6qXaqQe31vxQ5z1N\nW6/byuFB0XB0bGYWg2ZmXVxh0zy5mfAXQsMWbBecwXeN6bsGEOV6p5N6WKjU\nMVMrrsODGMB8GawsP6Z9qaAGDeqMw5kAdNp9aZSclOMXZNw2R16jVSwtKB6G\n9TKbNg3YjhoHk8/gdBKUJSdR/ESM8z2UX+bW9XtJEJ2NDkoIXwPUPWQ2BO0H\njZQ4\r\n=HeMe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.7": {"name": "@hookform/resolvers", "version": "1.3.7", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "ts-jest": "^26.4.4", "fs-extra": "^9.1.0", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "check-export-map": "^1.0.1", "semantic-release": "^17.3.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.10.0", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "7c164d438b788cc625e46c22885ca6edc254350b", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.7.tgz", "fileCount": 113, "integrity": "sha512-gC06h1hky7gM31UO4Y7DrUQjgZyNIKA3s0/TxXbdeurwhOj07l8Zx8Pomjt93ANOKoZOU2rcHKaZtrEJx1w7tg==", "signatures": [{"sig": "MEUCIQCtMTs36I07TvDMvkbxd6HfuslLOWskZ45HkAWEJ552UwIgCH9aTiQSQgTqKbxYTGYcinujtypNzaFoPlXVliJEQB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYuHPCRA9TVsSAnZWagAAIeEQAJhGexHO1AbsIE9opNe1\nuTks892sy9C/LpcnXFagUUY4FE1S+bjVDgwTIM2gOhp19thYCb8cMR9fPjYZ\njd9amjnncnnqqoPehMiLeVrbVK2ahSPHW/Iaba/8A4Cw3OddA7bWlxpGGf9Z\niJ6R/XIpi5JcnL3rL24vvScnr1uMTsBZRbCiqDxDV4jLRfVP7kK3QBNTjdtF\n2xz5kgzAW/msDqM/Dbiad8F5fLIWEZPkEYU5ya4jOyQO1cOPLaUaMtUv1BHV\nFLSm8V+BMnKfzXl78i0kQ9WdShHtqPVdejhwBAmmLUW20UGQnwuntXVY2ZmB\ndRUmYCwyGRj8FYDKaCeFOkVs4noRtoVKymVA7V7CL6AVv0PPBhmY/MS7hQU+\nPmuZqIkJ6vtUFOOFy/OH17ZJTwOxAqNE1Jt1axieQP6WPTZQkFV0nRJxw5wj\niuYa03aHL2buNFuxAm9wUAIYRFPRELAgpJRq+1/az2vLIJVQncpDFfK5i44a\nylmDYxa8uEzZKgyhlxywpxd7SbcZ+GcoXIGxu1a7rZnEvGtS8KHH8NUyynMA\n0/anJiNhJ/GCmXh3GC55NwCxS5eLRRfFsqZNvcv4F3hBNCflpMU561sGFHCp\nyjjiFWx0/V1WzcQ8/W1t9ZGXtdu83F1hLf3bdMtKgB+HRQU8fkPbrABZaVwF\ntdvU\r\n=wRqZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.17": {"name": "@hookform/resolvers", "version": "2.0.0-beta.17", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "4436d4cc0a3d668ca1c9f4c231618f406ade9555", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0-beta.17.tgz", "fileCount": 113, "integrity": "sha512-lB8VLjetmoA05GZFm555kl7wolU9e0SlNTsppq23wPnkUrUpsdEomjE5MbfaYsHT3qVP49edPcI0UifSq/O5LQ==", "signatures": [{"sig": "MEQCIAEcGAauuehHO7NC/rO06NbiVdk8NHFGIk/XBFx59Xu6AiATev/vQ4aYNt1vdzVJ19JcvFxqSAA88uJQctIV8zp2BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZNbCCRA9TVsSAnZWagAAvmsP/RreUfxVllFZsxuFsqWp\nT3m9CFUXNI6CBJEK2OEqfUupRiRL1f2UkYIWPLvT72uukIA3y1NIl4fSiS8l\nfFAT0V7dqzy8n0UydBtzEwXpbbwkkf1358eTcsLzCZ3iLmLHlxLDfNe6I2X2\nuPA2A29JJAEBBcsxFyuA4ebf/CSdx4wu0tfiXSkayM8ow4W0fP+vonvG9OnN\nsvFtqaW0O7JS00BzaKkNv9nXjZW1NUq8wzjcvIFhIcUivKwrKwXHqtCWjr1E\nmJ0BJCWdIxiPOEoXttDQrupHM5YOBf1lYml8XuTcGTJQ1kptHhveqYjmu+7K\nQn+lBY2ElBYfLIh746leHrXhl6fivYQxwzLjC8OTwpOf8AHZlzjxqlkIQ4a7\nhMbBJtQspzJax26u9PXlrOPs6bI3u/uLPUjYBdA5gW1Jya18StITZ2RbHdJL\niVCGlxzV3IRZZFzsqpjHcAyvnxps3muWdla6LA87nMlSc6gagJ0NsbSLDR0G\niRcVnWxvF7GR3x9yp6WjnvpysZz9ekzxS57GEqdtH3q/uTlDIy595G83kLky\nkxcKxKKem3W25Um+uuUgBCDTD+3l1LtRq2z+whMZH3JKW3PO7lRWcJuTaWoJ\nxyWsAHCKEWiwKSKyQJ28mJjqhhpEWdVtF6SLb4fzqDDpDoIKWCHryMxrNLoF\nK1aV\r\n=7GyL\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@hookform/resolvers", "version": "2.0.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "256414308d4584f2037d5c4349c119dbd66d11d6", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.0.tgz", "fileCount": 113, "integrity": "sha512-picG6qjP516JNblvg/wuDc8JPfdm3aNbtPbfu+PXQWFXp6ED6dyBL2IoB/tDvPFaNwwqUEwb6UYVKMIFAH+aqQ==", "signatures": [{"sig": "MEYCIQCMrar99jJu4bni5/S41/rdnU8/xEoBFfC2N7dJ+T0o8AIhANC70AAHSpIp2ybZQqwyxKoZsmWSLlNTGIaghblHWOH3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZsUMCRA9TVsSAnZWagAAkvYP/0V+PiPDsPKjjWdcvtoH\ncYFSpAJheLt4Ecfude3jTo98TbyrJBedF1zyayoplIiaeIyFIwJnup2ydCD9\npJVV7m54qBJQxIk+C/OC8helV1824qVuZCLlRT+PWdMVT6fop0K44hrYOWJn\np4L4aMYF51OXpnS1XfxgZmDsLYxbtJkTscQ/tSPh17ZY0e5RQBpm78/IFmij\nqf85kwGlKaIfkgmIXVbD/9JkNXyNkI6h4LooDQ5fSuVEnYqz9EiZD8bUelai\nr6RskGTt0hwjAPjbMgT/a4RRsTAh1AduOidvrHTN4Widubh6NOOsPZdC5uvR\n+oGeuxIugYo0WiSjUdPBVHwyMGWC4T6V+c9MyviFskD5veIlrpjsH0PbPlVH\nF2fRniwSphxTFtagQVJvQKk/0G7rfuShBNx81zEgsW7V5BlFE8pImUK8wpq4\n9VZICNIni4uoFVV4mteeIMx6CW7cckP3Nw/w+cEX7/mFlEoZn4L/xp8GaPfZ\nALGfgahVzVqE9tfDRAwF8dZUXZzCih60RkNTgwQPtWdzqXa7xxAy2Hj7PTnG\n34Y9b6oSsCMcEbARyqaeVI2bD4/JC2kvmIHeVhNdgMS0SZFfS1+VE64nHtfc\nYMYV4Dw0+uVJJs0OMVfPjbHbb+PEwI79NwRcGAWYVUY4VtSOW3402Vq5bY6r\nsFt+\r\n=9kWd\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "@hookform/resolvers", "version": "2.0.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "semantic-release": "^17.3.7", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "4dda765e4a5350e0eec34c95ab499e72fe09f84c", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.0.1.tgz", "fileCount": 113, "integrity": "sha512-iBkzeqKU+AgVETtsKHoVxBwUtWIjSnlBiFxduiOAfVAccGoCKUEqHQ2TuZZfAO14spGqUPBaESMNBl2FWs61Kg==", "signatures": [{"sig": "MEQCIGKSagO4NIe5U+/BX1deTcDj+Gl9Fkh+ukVEQKM+vFw4AiAdDIiLE4rT+XQR9Yco6Xy5ndrDqR5i4NtrLt1WTVT+AQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbrRGCRA9TVsSAnZWagAAdmEP/2MFCapWv8uESWYfmc1J\niNSkiY4RC5fa5/pHFVdEeLjZJ4q+rjRGSJ56jfIA6QcGOJvkkHaeuFzPg4eL\nkEnvd2A9Z7oa7KKO+qxo8uPCBa19knh8GkUkQAqrEPcW8IkkU2j6Qhi6u0Ht\n/K4kPE7kFvarj1N8wM5JJx/uNYdv6YstrLYbkSVdjYYPDbjDe9LNOej2r1Lz\nm8I6gfUCMP2LjsHjvg521pFnlGspxLY4wxy+PR3FXxqX8I9wciPc1h4PQks6\ncqZvLcdkiH8qqcSSBVXyI4h4ydapEpyRaeSv0ySzmJOHhoCsOsb9N66TmNn/\nf0KzZg9HPcCXEOYPuLCWyVhXS+QqthN9aKlhpS746H+BSo05Z2MhARGoqhDE\nzAaYjQVTESX8oFuxh04tbtmRYWHPFbtiTqyUcCYtQvGYUmy2Kz+isDYpmnp7\noVagumihjez3DKMj5GlbRdMHLfaECqXlCrqxba9pXLl6A/36jzHfn+Hb9fPd\nqsBG/caqzTrf3hz8BIfTuGPZ4YbB1avOlktsgbC2Jet55RDh86+0ig1UN5zd\nzPoCNxzIij9EBjRG1hDTnvUP/IHM6NTGH8R6dLE3XxsQ/vnjS/LVQncVuYom\niA5X0AXfY7y58WVSvyxoiAyOPB5oslYzwQz/7CDAR7Ga2jW99B18lnaEgO/L\neyYs\r\n=+2sO\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "@hookform/resolvers", "version": "2.1.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "54e022b0264a75423800b088dac2ebb28a20a43e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.1.0.tgz", "fileCount": 133, "integrity": "sha512-62K7val4LIwy4+hfRy39ZwzrtmpFcXMWfi3okSfvEKBexoM7kI3rHD7XTLY1WWmdbv6IoSQ3up2Qgja5ki5isw==", "signatures": [{"sig": "MEYCIQCJ001euIm7e171w5H8GLD3jaOvcCmk8kXn1NwsYv8BhAIhAL1Pv6ODO1Ex2iPV9lyi3poRzpggWlvt9EvydS4jM0SD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 824757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcHLBCRA9TVsSAnZWagAATrIP/3wv1+peGIppcENqVq/8\nqotIx0NsoSK6HRSX3nIGYbiB+ba5PfqZ/91h1Ujmv1IB37ocCHthbbwNZ1SG\nWWjKgEzbyTONxBrE+hZgUxoYPdie5MolqcXHURHWBOWdZh35cgBufvlyfJVp\nK+NdeZsZepMuyAbU5wwaUbEZWnabwixJfkb6AnBI4mzrGgjn/h6xMYJYTX+3\n+Sxv3uKlPxLD0jggDqbCES4SAVcvqRZeFmfsbEeRaEGW0VqIarw8sDotYNzw\nf7QQUekYGV02BnOjCFc90l3cMD9Y3UlfFX/MYKWKs2AEzjGhvZZSe7p12z3y\nnDttgmlxxQIfAgG8bWEmJHexpgatdj3ubb5qrFJDLMXIpA9Zks2Un/sKx4aR\n65svo0KxHTEmL9WOdo39dTY+TWcd0wiX9+AoOPOh9xJDlhbSllxcOTnmSqn4\n440D62Y2Mb+mLoJ6dCeeH6+suujXnRL350Q411CDaq6Dl3UsZTFmR8kFHU21\n1+pa46AH1jZ+L3+h8btHbXoGQ0hGxGpeSpfSQ67MBCPzYim4F16Fx6GFCofv\nLJKrYJwapag5MZ/asEX6j9VxiDHqevU7L3iPKI4NMNsa7JvEK+fYTYW4Oc+q\nihUwNHkdgx3IOFF0RpyIo+p9VVk73CnAD8VN3gEGAjR2mqD0T4uyfADuK9Sq\nwFLZ\r\n=TXB9\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "@hookform/resolvers", "version": "2.2.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "husky": "^4.3.8", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "7337e2a942412749f1dc1187385b8ba0c3b20506", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.2.0.tgz", "fileCount": 133, "integrity": "sha512-L9wExG8ap5rRfA1v1o1J9s1FuBgFzx6wG8/dEqLBvOUfdB1AteqXgCuCi8E/W6pGieuwVWH01PwbuVngtGgohA==", "signatures": [{"sig": "MEYCIQDm3g8iwNVoKDTk/RRBeNNgb7xp2t59B7tfIqdUBkZW6QIhAIbe5qU5prd+anc48rCgfTMGZd2vt9ax2Q4WtC13RcC8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcYEiCRA9TVsSAnZWagAAKiUP/j6dMowgP1c7p9HN6z1m\nKneD08MmLKldwOucERiepae3cVM3SE/CL8rOu1vtbW2rw9+4Zk82ENEfCFgY\nv+jzqPYueIGF56/exbNuTAVM9/ooZumvZtHaaypYy0IudGTlmd/kpN669F0T\nrmc+RXc3wj+7lHe2n/4IEWzc74/AEorWythni+BtmJ1cob/6CVbg5b25qF1M\n/QwDUaEYnQX+FooosYf1TcHvGWIytIEW8/TsK1DfonNbOcv4Ou+/A44ZlHYt\npWiZzLHOMtU7CXpAx6YnZI+tIW1QoFJSIXUnDMt2m35PyNk/G2+W8V0/TrnM\nXUpgIw+6l7CuRhTiezWynWZVZYL2SJLwxreGBH0/5frSA75eNR+xzUOu8EaH\nfKDshShO+SfiqixXrlRrS9dbjamu8KZC93S9eeGAmhE9B/UrsWBzJSjB5ROg\n5mTwwmuUyand5hpApUnfVf4BZbCuL5h43zr8zDgU1E4R7Vy9nN1cBp7Is7Ho\nrNEItMQ8yLwpa1BPIhdNRPYyomWjaXc0GQIoOUI9q2sHobDmJfIBm8GiUIDZ\nGmev+wjsdtUicdGlBdl+OjQG/Qy0dTW/QXPwkKTKoItn8D9pl6nctaOGCON1\nHVtXUXotXvzVPXJe0QH/SUp9po4xDYUVY3pfTfsECnlUajPDC3/0CHdqKr7m\nWrdK\r\n=QsuV\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "@hookform/resolvers", "version": "2.3.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "fp-ts": "^2.7.0", "husky": "^4.3.8", "io-ts": "^2.0.0", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "monocle-ts": "^2.3.9", "newtype-ts": "^0.3.4", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "io-ts-types": "^0.5.16", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "9412ab8644150b4056708fc129faa13979654674", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.3.0.tgz", "fileCount": 153, "integrity": "sha512-G2NbU6EynBRQ4OFbePm6oYjU7KAOs9ym1sxl84Km7CPCFERfe+O350+xDYicCFGaPE3BuLZapuE/dgD853/5mg==", "signatures": [{"sig": "MEUCIDUH0kHFgfxSuUNFB0G5uAvUllr0HNvlDOW45jbEKM1kAiEAtAPr5/JrqSaqXq9PcszJe1VobOEzrKLFQtleTbbNrwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 241856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdU0eCRA9TVsSAnZWagAA2pgP/jvHhLMNy1Vzzoa5R25b\nB6wEPHju3vG0V85NWPb0Tn7M9FJN9oZThRef3OFw/kJ4GUNpafRJx6oouxBn\nYiOxlCx8yLVKar2V+xaWNLs0ZcsLa6SGlRvE/OFUeyLKzNVwH0J+qU62JptJ\nGFMQcts3DiDcoACrm6a7a7va4pCEcWBlioe18rM1QmXaOfb0PlUOA6AhAwa+\nbJR0Z6f5sWdsEyvJayaRUW7O3HE6naNZ3iJKqlxwm7rdxxcNCZcUisqcsNrf\nuNv/29yW5XU+9frE55RseWZByk5uUD01cX/ItekLRf86ONyWSYlRCwI5keaU\nOToRgHLf5lWzQfFGM2Icfi79grwzsa8dzuza3zbkwIQ/ZxEnqouSNsu4XZPu\nLB0J1KTjgVJHhYypGKWws9NAZA37d/dFgZqPINEvjUMBGUt/c5Mb1Pii+k96\nIalR/hdPFiFQ1p+H4AeMbvfazHnmdqzxL2LMcJU6VB+Ys9zkECX6tgUt7mer\nxDkyKYMBSmQCXFZDpB1zHGaPYz5nOKVZQm69npuDNbGOQzPUBaS/zJUr7/3T\nEIjnb932jT6DBo44iy0CsN0YMsMUdVHOnlIeimzffT+RlGn+2ySdbZo4WzNl\nUhBhKGgNDpP01XwFg+pNdvPkbsMogkW0vEb89FjQHHbRyvwwnTD59JiuhsBP\nVVqU\r\n=zszG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.1": {"name": "@hookform/resolvers", "version": "2.3.1", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "fp-ts": "^2.7.0", "husky": "^4.3.8", "io-ts": "^2.0.0", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "monocle-ts": "^2.3.9", "newtype-ts": "^0.3.4", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "io-ts-types": "^0.5.16", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "010a65544cfe366cfac6d42d997db2234e2f5fad", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.3.1.tgz", "fileCount": 153, "integrity": "sha512-0S9NdY2dgP8/mp8h15PyX1VtX4GVktZjADjwB1Aa3tPAHCdhG4o3lcaUMD6a5WROuuy715F+4wOgnkNxcztgOg==", "signatures": [{"sig": "MEUCIQDJlCA0BzgJfMJ/xO2PdSnGfaQ9DPD/OB1dATl8pf3UGwIge5s2SCqttt/HpMCZ6FU+m3v82VmBoerHBKCUpGBblAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 244182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgd/mXCRA9TVsSAnZWagAAv64P/1FDIQbNy0ex/AipagFF\ndbe78QoGlVR/EY+utDqX03LKeS8SRTW/KOnMIDV0Uv2cZ2ceQAlrZwzSAkCE\nKSq9DQQKpzrA1X6QP2F/WTTct6guU4td051jpMhzah0H+jQO9ZMjsCAzbPw9\nFZImXLq9x0F/DtuIS94GyZyYxft2HfJE4E/vODvSRTfDggGbX91rSPteBTm2\nwFTI63MEJo+iLtCZB+DXDCtXidJ4DZOOOTfznfIqk35SGHp9t413y+NRgFKB\nWQ212ccXe2RMi3rKaEhgwenuISwwyUUJHxRNZ639QAH7pXno5QMk3RVGICXN\nwjM9QrT49nDI+lG/9vOhEaLIzMpNVfbKHq9pNrI+pXDV3Ch0ETrVge7PhrGT\nwGW/Sl1+WXNRoho7VEYbF/8dUim2KeC6oYElTZl+uzdjX3cfZzB00iHp5e5W\n6VELT8LOLnPZ3Gxnh3t273Zl6gIL8geGd5BztyEZcc9YE2C9oUECMSAGiPxd\n0U0fRyRhqJuDX6PvTRMie9aD62GI/tzAjZUnlOZSMwMHv2Cw+vLjcvAngtfI\n2DeTOEytoemozQgimOijfLu1zinc+c+DGs8ZTzlMLoYfeGg2QOZS6ArugY66\nB4KUtmCplkENNvLKoX4EZOJupcdUYqLF/43Mxf54AmMbLN826VlkoatAQUA5\nV4AM\r\n=Osvy\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.2": {"name": "@hookform/resolvers", "version": "2.3.2", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "fp-ts": "^2.7.0", "husky": "^4.3.8", "io-ts": "^2.0.0", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "monocle-ts": "^2.3.9", "newtype-ts": "^0.3.4", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "io-ts-types": "^0.5.16", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "803cf9cb272f2d3793bba825f9ac2c1c29fe2b92", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.3.2.tgz", "fileCount": 157, "integrity": "sha512-7CtfqVPbETA3me/qvy212GIAjOfzGpVOxfHMrykBjm6lg4nS15lbNpU/8KKNK3y3JOj+TFw1b2Gin2kMClcHCw==", "signatures": [{"sig": "MEYCIQCZl77lXdab26pwPTVzDhUvEuJMjMbZb46MdqQLhjmX/wIhAPtT1zqx9rNs4gxkzBhhJBOWqh+ZRr7ppO31yny5OOTs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257360, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgeW7PCRA9TVsSAnZWagAAHRoP/jO9WjrrxJRiZuDvWpnu\n+LerAzPpiNsgVdhRw5t0C9Y9oWonJcHNf5/yILqNXx5RBaBjZOeU1H5SJdp1\nlfkqr9kAGqmfxsUE2onePQKxB8+zEZTWSK0+hP8oBOko58VMpDe72T5mnwi8\nIRQrRjO2+z5BnDHh+Ezd/MzNUTOHlY+YdD8exx0SRtNH5SPXJQ567GZvHN0N\nI+hdeIeUcFKQJPPsQuOCws+/R3s1UGm0ZHcKFyvi0LUGgX+/XUJT175CaiF/\nUhIzp+9gb81AriWqftWparyvpzaRJufT5A4JzWB7JcWP98XkZM9dtnd4IYEm\nvEBgUkO8+z1iXqbTOI8GEJbBJcGc/NPOBFJ/9iHq+jtsk2o3pjbrLpLWMzZ1\nAcL/0W2LPYD+s9bHPEGTEBZT0g64f5Cqkm2ASCuR+gtinuZMARfUEq71a4NS\nqxpTRghu2ZizRSp3U65qpAHjDP6LQKA8HI+h36uou+jEMkt1TKPEWk4LHhvg\nNmD/qUTxqpHzk3XlpDjv9z/H9jlJ3tP08XBxLGR7pwktPp9wafy9gDFrqI7x\nqOiEF7EVSubPiAANXGFsH1+r5tHDZ/dsMAdwj1ZLYDviajSEw0YCfc2jJMVt\nv3GxpIaGxqA91p0rPvIbkc/3N1H0AdOG1nwV+yXKT6zUhCpljURHy+ThOkMu\nKOKI\r\n=ctlk\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "@hookform/resolvers", "version": "2.4.0", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^3.1.2", "fp-ts": "^2.7.0", "husky": "^4.3.8", "io-ts": "^2.0.0", "react": "^17.0.1", "eslint": "^7.19.0", "ts-jest": "^26.5.0", "prettier": "^2.2.1", "react-dom": "^17.0.1", "monocle-ts": "^2.3.9", "newtype-ts": "^0.3.4", "typescript": "^4.1.3", "@types/jest": "^26.0.20", "io-ts-types": "^0.5.16", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.14.0", "@types/react": "^17.0.0", "nope-validator": "^0.12.2", "class-validator": "^0.13.1", "react-hook-form": "^7.0.0-alpha.2", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.3", "eslint-config-prettier": "^7.2.0", "eslint-plugin-prettier": "^3.3.1", "@testing-library/jest-dom": "^5.11.9", "@typescript-eslint/parser": "^4.14.2", "@testing-library/user-event": "^12.6.3", "@typescript-eslint/eslint-plugin": "^4.14.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "3b454463fbb5e7a0de4da029fa34e677f32d8d8d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.4.0.tgz", "fileCount": 177, "integrity": "sha512-KiHc7Uwd2IJMvPTMQ9vQxfss2ulq2gRYL/HYZ90qiTs+07UgGWCikiIvE2pKjjGVltEYjq5eR8x0ITmoyEjGxQ==", "signatures": [{"sig": "MEYCIQCFMTaw7yewq6djnyMED1HFX2GYyeopgSzV+Vom0CSrQgIhAN8HuoxwXhX2vqKnd0qAHH8F9oAJShF8YnT+MG+DYoDd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 276475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgep3vCRA9TVsSAnZWagAAYTYQAIQdwPuj0i6LH+Z6hXir\na7iH/qvprNmjS03pN8Z7b3b8hraDH6gDe2cHq8dGsIAG+qVUh0IFniE4xaWG\nnA6cA/euV3+gMr124DZioz8T3VIdQ2bzQpAg7XeMki3fqlamTpuq8C88j6zE\n9E6yzN3r+FY+V1b+/PGEjKTVZ7YJfNhQyA2YzXSNnSrIBovdRFAMFu44lDOg\npQz5nADlvyGWjh8SGjp7RrrtLZVcTCQ9SRHaibFCDoKKVAXkv4G05+GVc/SY\ncMMu+djTq4oFWr7ZFfsvfVcliKWIyca/2588gMKR1yOlvnL+DFLItTMSMa+A\n74BeE5DdSlMA6/tWRikiJLwp+0teH1jutlDD/Xje9Z7ISGweHXJmR5cwsMHj\niuYnLCPCiFjlN67ObgPBOFX6IDsjr1ZuJAk5iIw5CLj7Tl005kjuuvxAIyN0\ne0974vYgd/Vl+HH8Gs2PO+Lhvm5lWMS5Z1msnESqeRLEyldRyVP3dZK/67NX\nV2p0D5TFWM858flH5tg4pnYNmimkiqV2FKPou5sHG+tUul+WLMGSiLOxRiYW\nQn2ugLG8oiBvaVH1yDqful3q3Rk96QR8fpA5Of+LZNCL1lgsgc62slJB09Ot\nmXlMUxExH8RWCuz2WL3SVN5OT4dHroBuHUyre4aAAL2Ec9km3lSuqyLwgSRL\nAbuc\r\n=W5nV\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.0": {"name": "@hookform/resolvers", "version": "2.5.0", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^26.6.3", "vest": "^3.1.2", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.26.0", "ts-jest": "^26.5.6", "prettier": "^2.2.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.9", "newtype-ts": "^0.3.4", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.5", "computed-types": "^1.6.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.0.0", "check-export-map": "^1.0.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.6", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.22.1", "@testing-library/user-event": "^13.1.8", "@typescript-eslint/eslint-plugin": "^4.22.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "ce6bac219339f2c0877c517b2632a163a2abdd53", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.5.0.tgz", "fileCount": 197, "integrity": "sha512-hQpocA/SAodTeR4EJWB+Setx95FwGU7yd5vux7GnlnqFwc0Uk89G5XPrsPoGyLYzolCoI3ojDswDCWl/sTTQXw==", "signatures": [{"sig": "MEUCIQCj7zW+ChJFE4x+3MWWDYhoezGolLW9OBxUa+Xmzr/mQgIgQ5w2aVKmfkZfBreVABCUAMektPCNPuPMKm3zzmVkmL0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 296979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnUZaCRA9TVsSAnZWagAADoQQAKT/R2f6at+U6FM1dTQ6\nXhuqqFDKq7zsYlJ0YywyI5+TymyygWitgFuTnB3lFlv4sLgKTyve5YMa3sXw\ncxZtVNGWf+Z1EOe2nvhOSABdbe7XC68MLD7as2z1tH29Ia+qO5sHMNLCgThi\n4wOZtsbr063DmbhofzHQEWZDzGf7LWwXRpHz1XvT1gEUY8rNhj0/hLfFB9HV\nM+wzgbfxrgW8/SRMzyrgu+1zzKbzrl/M4SVDg0boW72aw397ZFLMPFSs5NGT\nRtw+y+tCnRfHHpCnHsVEGOPJz5EQWqp9YFznXPt8mB/28Vd3iUBmorkHjMVA\ndaetZZzTiIuLihJqcpDXfUYmlvW+wI1b3B9tpjw2XyoPAaLkXukOmaFAFEgi\nxNvqWHZ0O/DdrRxSqUofmF5aw7enA/cEafE/FDzjysJcTtUM3RVw3l61LHIL\n0wfxSeAtIEPLd22giEsnqAQBFd5dCM1dXngrOS7gJi23k61aLdLg7ndcOm92\nbWzdFDllFiPk7yX9RKznrII/D5aiw2XUjLZat/RLLf+CVxzHTJoCp8QY5+Un\nrM13ZQmAFy3pvfBtfHFmw0X/rYdQqwvgkWpjollulWa218Q4ukyRjNhBKKev\nsAGpeyxSJk0eG3xhcEI1mUkxklOkM4g8RMpT9jmFfokmY0U5PQX9e+Ee4S0c\npkzx\r\n=6O1u\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1": {"name": "@hookform/resolvers", "version": "2.5.1", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^26.6.3", "vest": "^3.2.1", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.26.0", "ts-jest": "^26.5.6", "prettier": "^2.3.0", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.2.4", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.5", "computed-types": "^1.6.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.6.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.23.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.23.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "09f2228c9061c350819881dc11e4f65af90c5a1a", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.5.1.tgz", "fileCount": 197, "integrity": "sha512-d<PERSON>huOJ7ker2ZALGhXO6KjeycwnBGcSslGsREgqTDSiBOgbEysupfGTJKzQsb0sXpNhrZLCGvQcT+qPmCdBEMw==", "signatures": [{"sig": "MEQCIGzhWG56UrNkvugwZMWs/HXH0u31uHWMsgN7N6lt42YrAiB+hI7lB/1/FOF9ssmG7FMswqdqcJ0jgZ0CzIeQAzoXUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgoVNMCRA9TVsSAnZWagAAuYIP/0RX5vSDCZMIPa9lxcdq\nscYHYVg+W8MtxB0FFpJULCtH0pqMkd5uUtKG9tnTuGwr1i4gfI7mP9sUWH8G\nfE9BJQkkjxpeaUFZDMNcyliAWcd3mln2xcfcLjW0tP7ztTHLZPauObuG+hx4\n7Ii8u+rYVPd6EXuHqijKdQFFlZ0nwx/PLkkRf6xqrr+ckAkbWWsaj/2CmIw3\nepmZ1Eu6stEyRTxPQ/AvEqCTmrvO/Qk+rBdZnzuoQCuNM42YBS876lbqG7Au\nRCu7XrupRF2P2UzJHoc3xgcKX01c/EK1Tr47jjuq2OTohgEa+CICLLfa5BnG\nJCOyJUPJ7/QQIGOkvSY3LG3ngprL/Q9e/upkoL434oEK0MPsJOb+gv2ldJdg\nCA9u9bOYaglDdYeBmXnJoT5eXHu/OGKp4oHQrV+onBgqJFlsvQo1h93Ryzsk\nBFLbBYlpcX0YKx5thKZCnGFUoFRXoDT9gnF+iZw+uCQvjv7rgM8/PlIYsEce\n28xFILrWjf8aRFppWpMYNgVpo32/0Gjh6Q4qyELkHfeuRyzOOewOmi9q/4Cw\nfl1YPyKxloNwwNjfw80GKJcbbx5nVEc5Ifp5Pme3juZHe3NlRONe3giCoqEL\n5UpJGC13s+hnNZSNcdPsQO1nkJwRF2bkSyhwzuHDfpWMsD7zXr3KgexyRCYa\nG9dS\r\n=c+WP\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.2": {"name": "@hookform/resolvers", "version": "2.5.2", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^27.0.3", "vest": "^3.2.3", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "prettier": "^2.3.0", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.8", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.7.1", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "2b1134835170fcc6c8f8eb1d0273b27cf4f14270", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.5.2.tgz", "fileCount": 197, "integrity": "sha512-IS9Ycbezdzim5vRxytPcYVUUo8Jhtc9GR35SDXsCELwDCM63RUpjWnAaZLQnBO1XY1PMTBwEnBBBOBgGr9CzVA==", "signatures": [{"sig": "MEUCIFE67TzRJt0a2hg6DImVRzgnP7PN6fRGV+mc/kEk2sz2AiEAlbSM9dBWGQY8ToGrJm+tIxdQdxR1649gyqYRal5G8lQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 302861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgtJF5CRA9TVsSAnZWagAAGMQP/0oqhdnnhZwGDDsXCrAp\nT/UOFmD2ASbC/eLlHuJpU3j2Ka16rhbzmjI2CenrY76SxQnuwEy0Qn53Z3QH\nPpXjMm+CZSbfGFbT0AM9rfE37gVT/6b7E4mAsVEICu7qsv2v2x1pbvxRascG\n2oUMN/uW/7pBzs66fU6CrFlD8vq5ZaPcdI94P+G/sJiUm83Z4tpRzPPfwGj9\nLJ9R6W7OMsLClpPTx02bs0vKiTO4Yu+bTIf48rUhPjGjc48nbPIGMI9uPy9y\nMUeDfL+UrqFlzQRALWTi7T/Qr8hr8V6jZwetCCM0NgwhmdGrhN9H9tt+DV8L\nahWCypj6QczdmMJ6sUOlQLXNfdirgSmt0UfYsLVjgQw0gO0xUmBUvjZLVEXI\nS6CBcPFUIRdd5jEapbu29Ev/iwbCX2pfzHP9KkkMBDpcv5JzYWAtjpJ4D6RU\nnYS30syOUHL6HOjRCK5i2pWNfnPi3IJr3oIm7vs6yLmbqikDLIn7FGc5Lj6o\nWs46vG5YnPjsKxMORMOZaSD34AQjpMTsOQzvYXOlc4UKDjisTWFwRoPTkREq\n48WkfylDyGddIbwSLAeTqBkfigxOK+pBxFnomrE75cta51twM5ssjHLuN2kj\nY7Z6S2sPjkhbjdvSD14I2OgAbYPWIhOMvNgvhT+OQlWgxgKK7pYfTNbk4Ai/\n/csc\r\n=7EG3\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "@hookform/resolvers", "version": "2.6.0", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^27.0.3", "vest": "^3.2.3", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "prettier": "^2.3.0", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.8", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.8.4-beta.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "a4497fe33807d4ba09c34f41549f87a642550886", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.6.0.tgz", "fileCount": 206, "integrity": "sha512-1Pbxjc4HEzATEcRsyDfkEQR/3OWaQwbemW9HTTYH+ZiLF2mzafl5Pn++65gwNnJSGzXUp5LIq3Nz2+Cz4m2U8A==", "signatures": [{"sig": "MEUCIQDs4aTtl6gyFzq0Si52SyM+qq//fWpH32DYvUC/IIPElwIgOfl0IV5saxxlyId2vzkBYCDd5L+lTsrDA5gbqtOowg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzbENCRA9TVsSAnZWagAAG9wP/0KKZn90sXDpAy5hmkL7\nLFtweTs7Dkjgjx9Qaa7492c8ac9hiNAPND92P6kky1q2Nhnxm1Yrnv8Bix5Z\nOEyIh+je+DJ/DuAdKjaOZB+QEYi5R0X/VxpoL/3wemECKmZi45IYtfngvWRr\nnbKKzxB1vKADAwEJdaYR4JMf97OPQZa0FLCRgw/0TE4NObvxBpme1HycuS0E\n737ig4hkV61Cgul5hdcKGQ5rrI3StRCgGjMHVIRxT9A+suPsxkYRWI5oJxN6\nG+jh+JqUrKR9dTgjohB03ZCxCNJ/rmy9zeLfotWFXx63hpkyWLP6Ld+6snT7\nbEviQNH6BpptZGJV4a5pR4ZnkGXQjXRWtqmiFl/MBF0erfjZNFpOeaPJLu+a\nPkBhkNV2L9NwVyI2f3zEqOmxup2eBnRyogG96IXaW+wmkIToYUm3LlskznsO\nLeD+llnCFUP1KlD5gYgfjhGQPv55GgxaCP14VdPSy6M9wcZhYgJ8ry7gWPlK\n2DBFQT13buP7YekmBhXmGFYoYbgO7H5A+/WC5jhTDc4kXvgZiRv7eaBKZfKl\nTWEGNM5dqSVuFnFjHwm5uZNJIyEM8X1XsjgP+n6FDV1e+tGimKloYl7v1WqI\nrawqlMikNtooGvfDjn4ufP682qmO3R1EFOUHECaEBI590rMF/AcUkIqUvokH\nWlMh\r\n=g0Bb\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.1": {"name": "@hookform/resolvers", "version": "2.6.1", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^27.0.3", "vest": "^3.2.3", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "prettier": "^2.3.0", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.8", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.8.4-beta.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "d41296521ccebba71f671e105b75cf36071fa293", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.6.1.tgz", "fileCount": 207, "integrity": "sha512-7tml6bUUzdkk7tYb5LBk4i6ctV/1Rs4U5qVxrNBXZd8ZhxvsELI0TyYrnCK0XSlCo8Htlat8TOzxSZcKWOH5KQ==", "signatures": [{"sig": "MEQCIEA89PCEDsTwrmFBwLp8PVdyeLbvfbfFw8ZyXBdH3hfVAiA2/rh8LB5K+qnfMZf9GZ95vH/xO4CbAfqpHVaLI9K3Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7YZ8CRA9TVsSAnZWagAAQS4P/jyvhX6+V2qoJAalB2MJ\nFO+/WRmTUQ1uM13KwGB3zUaGsllHRtmYmcw8ADapjDvZVC7YyzRaOlrjD0Va\nqqcre4BnTN6EjPyMhRuN7zUbFK1ECJ47A7NTlAc8pc4EhkAthA/5SlytKEjG\nwWC28zW9T1/qtIPyMmUicPOqmVgcz7mGxCHoeb93p2WDnTqmk1BhM1HqlQDI\nUIx8ZgbxodrpJ5z3K4b3GIxxpvtx4Kgl4Km9TiUQKl9a20SpawC1Q+nsVjw4\nHrjB8OuRyQhGdNQeNY5EMRvJzTGYOkA40KNX65flEGVA3jYOFfL8XiKQHcEA\n7NGaDMBMdI0PAcIN0amCFjIIMLNbomaKHtUYLuEUb8+quvMkq66Q18vv+G+3\nuOe8zwoNU9hPex3TDKh2SgODWvt/t48i6b2jerVtUTXUHHrbWqBfMWy7S+ih\n2c2HtZiwM7v2NGI2efbC51dEKTKSRr6bKA78qz2YwLbbGTVl3YCZo65+VJeI\n0XY0KZcLwi8KapgeOr+6BicfR66LCx+WLpitDggP5MBGrPZ/Qgt+Ie9MTdhs\nAxYVpyyY3prvWVCpnHU0APSSAxvBPdOD2T97lgRQZtz9cBE0uEtqFpC4qoM7\n2kPljIZTvSaPPRIq195uU9jj2zneFwxu9DkoA79HF+nbr8LQgANGhb0CXfXa\naJrn\r\n=+Xri\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.0": {"name": "@hookform/resolvers", "version": "2.7.0", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^27.0.3", "vest": "^3.2.3", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "prettier": "^2.3.0", "typanion": "^3.3.2", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.8", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.8.4-beta.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "f34577899294738541ef0905a50eb23b3ff43bb1", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.7.0.tgz", "fileCount": 228, "integrity": "sha512-uWKw0hSFiljLmNe4FR/k6bZ/dp7xvZG9CAXFUM24E7CunGhU5Ltvu6KUrkNDOKsoIrpc01pQsQ1Sjim1P6FaAg==", "signatures": [{"sig": "MEUCIE8hhpZmrTuVAkQVIYwPa7cHRcCZ7RyWLJsEZx4YVkTwAiEAhUahzYQmBIg3rZ+Jvc18t7yPI1/1zfztamqs6LK+J5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 375450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBdY5CRA9TVsSAnZWagAA5Q8P+wfpLwWFkay4aGw8EdM0\n6uBmOE7RcmDW5PLUmx5imnDMmypQxxGqn6TP3rTeFJk7Zx5PtSrwWbD8kU7W\nY2iKVlU1Jg+FsLW9whicZ+kjhwGn4s4F69tSbtifZjTiyFH7pcr6vq8eC1KS\nSH5zWWp3pAMAHTkENq/fb7AVFg081KawqYli49T1FPJOAmlhPpbwQXlVl9cf\nMzyYFBBXmVQECX9XmKLCSRiIcF4EBVipC2b3hQjWi3LN/sSELSz+Wwjfzqj4\nfqe97FZJ3XuLo3sNuxeBQx7OXjwQzXgjxKX+vC1mORiVP9g+B9m3LpK6eZSR\n/MtVz2qfQnh4/ll9ntfGHko2O+az1/nld8QAZum+3M326ga939CR1VHQ0q1J\nMhNESCyhztEBOClgSysx6DROwp8GPqH6ZhXfkvn1D1yms+DCqaA48oKdoHFS\ndNsUc44+eooOlgoTsAA0qfkKRLkBcxXXTZj6gyY2RXSWvQMtq9k2NCUx2Asg\nXwVCVQLP4GXE/8036Dxkqpxk02SAzrxSXdOvwrfj/IknGzv0P9y2ZEvJUpTj\nWPNSX+oLJeu/XQSlDRIWNhXNjKKNRN6v6rg/StUewmm+o2JN5xiFZvihhYeg\nc0IKW485+55uW41BCGbXGVI6BZqcHjnwi65murePDNRoD9pCCRd2mAiiCOY1\n9slO\r\n=XtGN\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.1": {"name": "@hookform/resolvers", "version": "2.7.1", "devDependencies": {"joi": "^17.4.0", "yup": "^0.32.9", "zod": "^1.11.17", "jest": "^27.0.3", "vest": "^3.2.3", "fp-ts": "^2.10.5", "husky": "^6.0.0", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.1", "prettier": "^2.3.0", "typanion": "^3.3.2", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "io-ts-types": "^0.5.16", "lint-staged": "^11.0.0", "microbundle": "^0.13.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.8", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.8.4-beta.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.1.9", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "691f02a82b44317dc30df44765463f2eb73abf09", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.7.1.tgz", "fileCount": 228, "integrity": "sha512-dX//pnHJK9CJSz0bURQ7B84vSy1OnqJmB+ziVY4Jvc5VyL2qe0l3ol8QicCkjKxFdlOLlAds+eF0H0u8HELk5A==", "signatures": [{"sig": "MEQCICDBYsMRljHj5bEV9TxjExd2g9AT08jptPd9Oq/r9j3cAiB4KN+yZUzb4h4BZAksRQrqoBnK3538YwPFxFamB6g7dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCkr4CRA9TVsSAnZWagAAzgkP/0/5V6YxCc7MQUsKceTe\noFhxDRbmFAncQlfOj0eZBlc1In3JjhmaL93L7w0LuJn9wpDR4MVbgmWDlLMm\nQvODff0ZAfqwcaPpmzi0GPTYz9ZgIZBMy25YOHpva29IBV+4qCFEr1f1OnQV\np29rj4ZZKKq1WmBgUHUg/Hm7KA9wmwEhZ80ZG/djaAyAEazZ3ZOa8asxsOm2\nf8uaywaT5GTi3Yz+4IkYhCmLGifFm13U2fGem2UwjoAWBtBVK9wJhU+j4qE8\nMQZ1npUNnf9Yt9LpHVLChtLCNJgOjmCZdPo5TdUzUrM9yzKHqxB1OLi2b33l\n2mZSBiHOv1Zg0/R1Q6FrTM3nptW76FnivzUsBDDQpGzrep0UKGfEjnH1jPfV\nimapncRPlFJTZsRJaZ5m+EbbgpcSwfvVvZyOyRG4BD3SZoO60xZ6m2d3t6/M\nyOlcLd3PMada9Fq91cxoK23M18X6v1DBNVJrB23EMV+TujgFkBU4kqbGInAn\nj+KHE6Xqh3coJvBvSoR0qc36NuFqbJ/oN9oV/EpQ6HPwIJPE68rcgpQa5k9S\n/R85XPNC8IT3HJbQ7vCVN4YWI8/ds9bUYuRVdJxlQrHpzJHdZEZHrnxjIMKw\nefQqf0eO1OMuM9orB9W/LKv76SORnjCtj/GL1IuUad6m5vDkwk9C75rEjmo0\nP2V1\r\n=HT0u\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.0": {"name": "@hookform/resolvers", "version": "2.8.0", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.9", "zod": "^3.7.1", "jest": "^27.0.6", "vest": "^3.2.5", "fp-ts": "^2.11.1", "husky": "^7.0.1", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.4", "prettier": "^2.3.2", "typanion": "^3.3.2", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^27.0.0", "io-ts-types": "^0.5.16", "lint-staged": "^11.1.2", "microbundle": "^0.13.3", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.17", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.12.2", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.0.0", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.2.1", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "32e82b072ff71c71e62fb2901b129642a0c5897e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.0.tgz", "fileCount": 227, "integrity": "sha512-ALlr0Bg6zDHdRsdqkwfWGPMnIxP4hqMRPvFDHp7lWXQeA7rKykto3cWqQQh1s0PzX043RHwMB6OHVPMwFJqwxg==", "signatures": [{"sig": "MEUCIQCmgZgNBoNsb+IJ/vmWoPaqHXJ9XJc/V1j1ZyRmtL2dVQIgC9xue2R7bgCZWIk+R3UkEdHSNPIgVLzD7EN7sP5hZJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372470, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHg9ICRA9TVsSAnZWagAAjWQP/0RKR9qbJ5ycLet3gPg5\nHBeFFMIy6JtmbulYuJ3WXSN2ONXfg6TP4xy5jtTyKtAsPmzixNlTNMWn4Nfp\nB+gS09o8W2WzM8eElr13aLwYT+g/TOFXpqKbb68bCE5JrU6xQr7hC01ejAGQ\nu4IE890YAF9oBIHEnRMDgq81qSs6XsKWPAqI9Edze9j8kMIfzeh6nu2fOGV7\nLcnqnwm1XmWHKO9zca3A+2GLZzSutWMyXXZnb6fycjw/MAmsV1vqi5uK/3WJ\nryaXMcpV1OznLPC4B/qeYBSwx/jS4UPRKwxCAc1FBd43FmRv75H8BCe+LCch\nfJ6XfXPhsUwb6rrVoM4IPLbv3zcXTEf/WwFNKCiuofGBj+pYj+DznULOhMze\nc+ExTOyfOCwH4zANzHjMZpcD93SYdtCufmwjRTXy+WhOC3fws+5dBhiySXIh\navmPy99vqIjvUXO1uMJ0pynDUZIEXToCJ08t44K2j6KoKgt5vJ9k93A3gpR2\n0tsN776gMOxQVaei1U4ioDZqxPlzM2Pjj+dgeHMAnlWnirYMtA2JbkBiLdLa\ncG5tcmIENZliCAU1nFuzNVJsfIC4Uuiv13BlkUzQElBuBsfwIhL2S+dqCVy8\n5IotzfO1ZYR1OYCodNxCGCXiYayI1qMz6ZzvplA54JS420/C4Rqo/n4Z5aBp\nT7Sg\r\n=nUX9\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.1": {"name": "@hookform/resolvers", "version": "2.8.1", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.9", "zod": "^3.7.1", "jest": "^27.0.6", "vest": "^3.2.5", "fp-ts": "^2.11.1", "husky": "^7.0.1", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.4", "prettier": "^2.3.2", "typanion": "^3.3.2", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.3.2", "@types/jest": "^27.0.0", "io-ts-types": "^0.5.16", "lint-staged": "^11.1.2", "microbundle": "^0.13.3", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.17", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.15.0", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.0.0", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.25.0", "@testing-library/user-event": "^13.2.1", "@typescript-eslint/eslint-plugin": "^4.25.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "0d4fdd25bdeb4b98bf4e177c63fc4efa173454dd", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.1.tgz", "fileCount": 227, "integrity": "sha512-U5lgaCkvD+0e5X8iQmCHiF+jOqjTX6OHUA7zPdeIHI6xdAOoi3rH9MKNuwMwv5Hly2LL6XtDgDkS/k+YG9hOew==", "signatures": [{"sig": "MEYCIQD/ZWommdcMnMvu12VKE1ao2+HVgEGXwCvOtOOF8B6TkAIhAKNYqEqFqMZoud3iHd0y+1MTDPduJQGauBxPoEoCUF9x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 374839}}, "2.8.2": {"name": "@hookform/resolvers", "version": "2.8.2", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.9", "zod": "^3.9.8", "jest": "^27.2.4", "vest": "^3.2.5", "fp-ts": "^2.11.1", "husky": "^7.0.1", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.4", "prettier": "^2.3.2", "typanion": "^3.3.2", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.4.3", "@types/jest": "^27.0.2", "io-ts-types": "^0.5.16", "lint-staged": "^11.2.0", "microbundle": "^0.13.3", "npm-run-all": "^4.1.5", "superstruct": "^0.15.2", "@types/react": "^17.0.27", "computed-types": "^1.7.0", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.17.1", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.12.0", "@typescript-eslint/parser": "^4.33.0", "@testing-library/user-event": "^13.2.1", "@typescript-eslint/eslint-plugin": "^4.33.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "590dcc23212a659bad08212138261c3950ef09f1", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.2.tgz", "fileCount": 227, "integrity": "sha512-oDTGrm7yHxT4VIv4kJcfjvWvozCbdcL0/jrpbAkeo39FYn78XnKOrrGTNmrXdfu+EQVkNIdZ47IYRFManZ4jUA==", "signatures": [{"sig": "MEYCIQDh/Ek3XYbPvGpMQ3U/lKSWFxWSGwL07lYZLxGMRvn4vAIhALcQ8hCY58uk0iO80IXAXNrsEw2qea7NjSZX3H/OeBuB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372806}}, "2.8.3": {"name": "@hookform/resolvers", "version": "2.8.3", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.3.1", "vest": "^3.2.7", "fp-ts": "^2.11.5", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.7", "prettier": "^2.3.2", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "io-ts-types": "^0.5.16", "lint-staged": "^11.2.6", "microbundle": "^0.14.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.33", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.18.1", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.15.0", "@typescript-eslint/parser": "^5.3.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "d362360f6057bcca7318ad22d49e5f0c47290e27", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.3.tgz", "fileCount": 227, "integrity": "sha512-9G6/vCUNvg3O4MeAK4l749syOz/2r1DF/Pq3Njuk3sI4ObHoRyV0nxio5NrNLGC+7N1Ixu9vJ/loNhaUNXTX6A==", "signatures": [{"sig": "MEUCIQCZv26xJm7GjsAW6Wot7WFgo8Nx6QUnnOTx3Xf6bdGy/AIgWWEiXWCuEmj6Zve/yztXnQ0U8C4FcPZJHnYAhoilups=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373005}}, "2.8.4": {"name": "@hookform/resolvers", "version": "2.8.4", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.3.1", "vest": "^3.2.8", "fp-ts": "^2.11.5", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.7", "prettier": "^2.3.2", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "io-ts-types": "^0.5.16", "lint-staged": "^11.2.6", "microbundle": "^0.14.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.33", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.20.4", "check-export-map": "^1.1.1", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.15.0", "@typescript-eslint/parser": "^5.3.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "bfda18ddf2e63043550bfe1d9af3fa239a06f99f", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.4.tgz", "fileCount": 227, "integrity": "sha512-2Uf/aqtfzpEpCsB2ao4GOqmKhjOAxeaaq4CUMGBu+sNz1XQane1Dj2cUfhDII/rj/EWUTObIuUwqljLBbXjxeQ==", "signatures": [{"sig": "MEYCIQCYtUEhgm3Tpzv6+SaReETBQRVgxTHQ4EwDdX44u6Z64AIhAOYgbTkagMUpw9OrPwRe2su4SNiH30ftJzNIeZnYYJIY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373355, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqcw4CRA9TVsSAnZWagAAoDQP/3AbKJTswqm7IUVRFzVr\n+QJBz1wpZ+PA5lWEr5XIxIWFAQ9BCdbf3j3Kbyxt41eAQAwpqVohLvCjQVa2\n6OwXFvMcVJMAAnlHJDlViRQl+uC0a4G+DHQwoTZV+D+V5hdVt1FWKfpOQvQ5\nQER6IEFO0Ged2WnTx8SMYAHEqGVoKdInqMKJR2J4LDwpjZDfS4uBrpCLzqHP\nDZCOhVel5bYFfFEYvBuT2xKgz/SXH4phNZVGUmBdvO7IaaP+xbOPE3FTn/FL\naWEgvld+zLFSGTHoS+xTbTMIer4Bznw95hlohlCwQIxX4otBJT3+A/+6t8kc\nQSnGTo4UdUM12G8nftb7WDJpc1EmSnPn9JuAAQZkiN9aGwcBHqnKHbODiEpt\nQjmEdvF88mgjsEVnBI46/VVr9/ZDv/pXA5mRl1Rxhe9zREnB+zDAzgmCKk/G\n7fak/AKV0x1Ai+xiKIdK9xh+JBNK/uu9jC1Kfnizfvz8nm618x88E67kMd+n\nJPFBE02mBM0XPSpJ88GSB0WqK6rHvFJURVSpiq4YqJeq6be5LLZLEMMXNzWn\nv/l/KjSLqeEFWKh/1X3NvUHOq1hZ8GLCh4nZ/hI87zNG2+ynDIhsyCuux3Iw\nVWIJp3Mfmo4uwPaM7yLy08dmh6QXtTn+lbBmJFsxIdd1R/bKEFccEMwzh7y0\nWxGl\r\n=yern\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.5": {"name": "@hookform/resolvers", "version": "2.8.5", "devDependencies": {"joi": "^17.4.2", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.3.1", "vest": "^3.2.8", "fp-ts": "^2.11.5", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.0.7", "prettier": "^2.3.2", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.10", "newtype-ts": "^0.3.4", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "io-ts-types": "^0.5.16", "lint-staged": "^11.2.6", "microbundle": "^0.14.1", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.33", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.1", "react-hook-form": "7.20.4", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.4.0", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.15.0", "@typescript-eslint/parser": "^5.3.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "25b902bd40516c1755bc857753283de740560aff", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.5.tgz", "fileCount": 227, "integrity": "sha512-n7eFm4snsejpfZVH8q2NU7YJbsGhF61IHB0TxgK11nmg08RymEr3KscnbTAZaPd9RaXa3vUPoULgBRN2nVDtMg==", "signatures": [{"sig": "MEUCIQDvXv5OwMfm+1QXx7ICbvAk9nTNHgf7Ty8YUF6mts26QwIgU8wBWhVmkkMV17q1Qe5545BEq6vbi9b+3tng8dbP2Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 373357, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtmRyCRA9TVsSAnZWagAA2ccP/31pG6LX5g26GyRzwqOj\nF7Lr/ta2yDfXxhJ6G309/rzd8F/Xn42pqwgEi1DubLac7VVWpuvXvHYHSUJw\ncPdo47YFrJYrq2UaynIGYyujOBwPxDLZs3FkD/CqIALKe2Ah1QitoEb/x8+o\nkp8gkn09p7xYnvwmXw0zzrV/oreKCMvSqmpYt3TD4HDXxsEkbspQM4lCqTDR\n1nj8VF5vjV64MdD9V7bNfIMDHrRdYkTRNXu5oFuqde1aQRbu0YhLAlkjfTC6\nWbgXORMh8Pv9YPSLI0mVE50BhYTo77TEd8z6rQxGkyCKXly3IOlb8HWvVbg7\nKYtmhvEpQLF6+XY2SyYV8yrejFSDHVEnZ+7i0kFKBLQyYnm1JiDqGTMkr5zy\nexMdqa97are+Pa0dJ+086qf6GJdCScowxiuf4UOKQPNpuUP1+hBR0xiHBCJy\nFL6w0G7sW7gmmQGVKvI1FtWQ/h3L+cngZHp2hgeDiBoU63go3RcaKJXVgYXf\nxp+mGWjrRTlr+ud9uNr3YDRtg4JQRWXxSNZfrvnCI+ZMKfSS+LDFdDO6G9t2\nVQ2wkAkhR/iBP3pTn3TAJPvehIyc0E4+w42hMFkR4lNqDCv7DtT2kHJugTK9\nbZJea3Ne/lGukSuxMuj2bQQuSPHY26XHW8Zdnj6p17BBXFank4slguEGt8V1\nX0oM\r\n=UouP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.8": {"name": "@hookform/resolvers", "version": "1.3.8", "devDependencies": {"joi": "^17.3.0", "yup": "^0.32.8", "zod": "^1.11.11", "jest": "^26.6.3", "vest": "^2.2.3", "husky": "^4.3.6", "react": "^17.0.1", "eslint": "^7.16.0", "ts-jest": "^26.4.4", "fs-extra": "^9.1.0", "prettier": "^2.2.1", "@types/yup": "^0.29.11", "typescript": "^4.1.3", "@types/jest": "^26.0.19", "lint-staged": "^10.5.3", "microbundle": "^0.13.0", "npm-run-all": "^4.1.5", "superstruct": "^0.13.1", "react-hook-form": "^6.13.1", "check-export-map": "^1.0.1", "eslint-config-prettier": "^7.1.0", "eslint-plugin-prettier": "^3.3.0", "@typescript-eslint/parser": "^4.10.0", "@typescript-eslint/eslint-plugin": "^4.10.0"}, "peerDependencies": {"react-hook-form": ">=6.6.0"}, "dist": {"shasum": "32e733f92b25eaddf53d72a6246a233cd53ea387", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-1.3.8.tgz", "fileCount": 113, "integrity": "sha512-mEUqsqbx5S78+uN1tVS1vrgaFEeh4cbFzBs0tuzvWmJFGSSDaqf0Z9SoKvrDFXl8eBIQvADA6H4vsbUYnlXuxw==", "signatures": [{"sig": "MEYCIQCDb9/u1uV74yEnA30e4mTkLIqVNZtfYDUJiFNO9g3XfwIhANeO+wnEk+5l3d3KboyPFz4QE+dW+36a0n7mdG6PmRuJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5oFNCRA9TVsSAnZWagAAdEgP+waOlGEl5u07I0hjEwCM\nhh6sb253sTEiHRNflBjEW6X//V4JIQqSLe3SBGHTystgWTW2PLKkMY9UGiq0\nrcyR8wa6hKqFRAX+o97npxXpstJstlpMGIG0iKT9QcMqE60+dBW7izVhZVHY\ne4nspEXel3J/bZjiiXbg3ogUmGgM+7+cGyd8IQKZGid/uhlKGMHu8pyrba+b\nJdqHg2rZxQGrh2yG0xzgDPE64lZ96IIXNtBGKsN4F3XI3rSWJJ6PQ21GbAOH\nGZktfGkjp0rpwIcqpV93BL2GCZ+mR30GXenQLdxbwuzEFaZMKkz3MuxZFBGy\n8XC8DrnOhurZdeuiW+u1A+djXSBluGwjKaD2MZfISs1K86F2YnJtfyuHbrHD\nqw6oro2yopNRocWGIWQcSkuzo7VdqrmBWzZlanEsKP+vAkaxJQ8YGZPyUjZ8\ncgRQSFU7XJbC2YV7vpM0U0v1fSkOgEGl+hZ6b+Q742VjVEUXu4hmvM5EgmSG\nboE8Quscpr1Po2cFPpz6/6azC6FWMqlLOJwHqCqQPnCyIK8qqRuWtwmxg9l5\npbD92dvhHeH/208Feuq3IYhZgUA17gv3dtSzTWxqnHudGQpBAN6X1cC9/2eM\nYe6ZRA4V2CDdQAIVLf1ppdLVkmOOAmnk0TVvLqku+s9l2h2/7QSNHk+xBQb0\n7Abf\r\n=VE63\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.6": {"name": "@hookform/resolvers", "version": "2.8.6", "devDependencies": {"joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.24.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "6adf3c8b381a533406134d22f29d4e63c32d9615", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.6.tgz", "fileCount": 227, "integrity": "sha512-Lpi69VWPgKrQDYlmh3n3MDkZTDnCJ2rvwJth7WYRX8v9p7+c1272jvyLsJNvjcUn5Bm73JaVOp5v+swoP6PRmg==", "signatures": [{"sig": "MEQCIEY234jrcl5kd1fdIvplIU0505ijMIp5CB7aKw+61A3SAiBQoJ3UlLlkef0IYd3QzCaEKuqQuLUjuDfudY8bWwyqtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 383753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5oHlCRA9TVsSAnZWagAAG0sP/15GOqR8Q7iQnXFi1TAx\n8PWyfD9q7Ada1q+m4tKvrUyUx5R59bkcUo7ktcuMC/WTqQ4utU4mKIfmxk1w\nN6FdrRamYU/qqbNJB81PGEP7qWmA3mMnvI4eoQkm6ubr/WDY76V66mKX4fdI\n6XISpeb0TjrP0pQiI4z/ARW7DTByCwVvrHk4kcADZQrMxoXW9x0Rrreno55y\nHMdSlZgJwb0+Vo9WAbhpb7dVjqtkWjbtRpSrW36mHO8xruWrlxYbRBdiXkrY\nDKtZY4UStOKB578Sq67bQrBX5kbuCQC0V6BGl6K5XeOCY7Ty5VtLr8+bobql\nkKydf+lwJO81HnSDU/ltiz9pWNnTOtlDks7+jgrwMaYwYJFxjKzoqXeBOt8g\nz2fJVFDvjpZ1DX98KKxbC1DGIbeAR53ia12vvL49vaP4jTnv0onMYS7Q/1pz\n5Fxv7oe8/24mi/oXsn72zdkZqq9TMDVCOq6dbOzPn7od5kqV0ZgZrokLtk1c\nX8K8mZBrutg+HEkiNAC30AxVyMD4Vk0z1XjEM03piXMy4w1mexX+5cMhFW9F\nnfcscnGhf7b1wLQBKpvm1m79oG9NBxfdaCicJb+gqaRjpcZn1M2Lw6jKFRGd\n1EvXHbWerF3mXi6eFlZp9KE8D4RYunsxXgKhQsJEUnI6v2JLv9PSeV5iqYeD\n4ENv\r\n=bSUA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.7": {"name": "@hookform/resolvers", "version": "2.8.7", "devDependencies": {"joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.24.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "71d61a1c3d4bb16861d9fac5e74845f836cdc498", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.7.tgz", "fileCount": 227, "integrity": "sha512-TQ4hh/PM7HpJKYuWiQTxwHUcP8+W2cs0oEIUmRdqtQI4NoHI0OgcRmg8ipt2DXOkP9tcpXqdadOwGFeykNQPSQ==", "signatures": [{"sig": "MEQCIGrbVGzNcwziklJQJRtzAclqyhZWlJsYBArqzLUMi36yAiA3qaCK1BRRQywkNwLj/HUIJXWhZQ347rkmnx78FsACnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 386364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5qcjCRA9TVsSAnZWagAA4GoP/2ncFC/NmCQ9mdm+rCTw\ncOFwA07DWdcZIUyRCdi9Of1Jv/bq1G3/O9gem+iB33TMpti+vpUcCoJdmS8j\nfHi4IM+IgmIWF0I6mdp3H/x1DZy0zqcY4DJi/2dyV13iFeirQR3XrCPLlSsh\nb/fh6ISTHYfIynmbg6EckDMjtEdMO4OaynejZoZvFhCZfhL4GXjLPsqrtJvI\nYJJqvAEoo1Kxa9feQjqIBJJAvt5XjT0MiLVhoSuGWdo+wNywZj9ysyO99W7X\nTtdqXjVJn2k9pz++BNVeAM79HVfFBHykQdxgY4Z+938u/A5JOEKyJ6b5j6OY\nXu+R875TiFsuoQfTXqsO1kRJjW6Fl4y1IgrBG8HyRcB9zHHPF7VRtd7KVLu2\nN2gxyl6nCA7d/fF+9QKdWz1xdr0fYqXny/nSFaYHtRlD0t4ytvljgal6eUKn\ntihKKxeIneZvCf5YXVPefBmAQvgO0i8eeYyEiMwY1iUL55A41afNdtF/MgPS\nCxUr6sFZGXk7EvAyB0yf+aIQ6tFbaZ2iWxLfMdwChbTu6pTvQWdSch5Kjtmy\nyhZUEoVeEKO7WTGxHBkVWYN2U5NXTJ+iSz7/XI7bm36zHJwOQHbrIm3eDzYO\noKYACCkCoe3GgnYFEtPX7pdWkD7RTV9AT0bpmz8XJIVCT9jFd5zARbUFoAP1\nJ4vL\r\n=ZVia\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.8": {"name": "@hookform/resolvers", "version": "2.8.8", "devDependencies": {"joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.24.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "17cf806485435877fdafce9f3bee6ff68f7f87b6", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.8.tgz", "fileCount": 227, "integrity": "sha512-meAEDur1IJBfKyTo9yPYAuzjIfrxA7m9Ov+1nxaW/YupsqMeseWifoUjWK03+hz/RJizsVQAaUjVxFEkyu0GWg==", "signatures": [{"sig": "MEUCIQDmnM54M+ZWrqAgV0Y8QR73a0Jw8xtYyH7lEi113PEmMAIgMbQ/HQ0/Vu+NefJwr5/qUP/xBnHILjEx8skpExteXYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 377043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zsLCRA9TVsSAnZWagAAG54P/1kMbwhzsMQXAceYZO7S\nDyMMj1vUQN3Hz9so6gfvEfMze5Ncj4j9gy0KgU2i5IQ4bMfceAoUmibPIMOW\nzg4m+vF3Y0T5i5FZc0CwuC4tYTWzeZCkQP8B0bjwkCnvOqwQ0mDZrle/c5kY\nuzbPcMOfHVbGFvBLv7m2LENM3vZTjQ/OLGzfCEV8DQ5Xj13jhGKn6y8FBxfQ\nTnf0GNeNjGvJwnxeGnKFvVJtkrjc6tDblmKPhL8IiNHyYIkb19+/s5ULY/qU\ndp3PT1dD4ZwcX26+EpO41yGzMiXC0yu9miNUeINLyt85udB89jiY0qzcf4v4\n6KjCYXBpMN3NMpmKV3VGxI/Vr9jU7wZiUoncYtYe2CKA5mhH0VBTi4mY9Yl+\nq/MjhubAckdbA2CfJnB+iZ1r1vgA1+1h2R3NNaqUTzrACT2lMcLwqFBEgKnY\nhdpLqZBP3zgrPxscEH06elgR9YahQznIpqOlz9Eykiwa1iPMQedzqlTKQiwO\nWANTqTMK2N8unkC6lVBmCV3aVlC1mmksLq+AhuMTA+ECcaC9J0z+4pAxlJZX\nwh2u4AJrZ2nW+5tAe/s9JzSEjb3Xv6I/ZLjhdtYh8mEvjKcembsr+MgT9XVU\nadCFSiCPxcilBUZ+O/ixVm0Z8ePgXNV0JrSNiYkYGXTFvsLQVPRUL5StIdvQ\nA61q\r\n=SKC1\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.9": {"name": "@hookform/resolvers", "version": "2.8.9", "devDependencies": {"joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.29.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "0177a6b2b5b0dfa7860625f9a1b71803d467e78a", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.9.tgz", "fileCount": 227, "integrity": "sha512-IXwGpjewxScF4N2kuyYDip6ABqH4lCg9n1f1mp0vbmKik+u+nestpbtdEs6U1WQZxwaoK/2APv1+MEr4czX7XA==", "signatures": [{"sig": "MEUCIQD+kG+B8p+jQWw1NVmUjBEk/u03TiSl8LVIvdKoim81/AIgTRtQpFxHxqhgZI5ojkrVO/dp6/yBKYswcfzO2kZ25OM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 379254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigMD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqooA//QLMhwvvlar5Ed1Eamn6spLZTWv8OcP7lnFcNKLzJKtWmJWG3\r\nPTDNNMfhZKt/ADpeRX4BHZz4xnTy8/ODEbkBEH4n74JDyiHdE+01KuV3+LyY\r\nSzqsykReRrgA8L86GXpQ18X+32Xbs/UKaXg8fuFLVpSobP1TeCctn477OR0/\r\nUnItdxeiLm4tvYcZ5SfmioXnMjN7dGVLQ0mAyc0dGpld949KluZDZeAe5Ftu\r\n1i5mKc1VBdEsHl0AFzyLzt2cPI8gfDKKiwvzdICqhPlcrWYM0Go5r120hSGV\r\nBIugHaZmLs1Z91+CLhKBNdbmXUc+Ljj1a5yLGQCLREKfxhEXDD6Ze/tKvJDw\r\nI6dFO8VGEM7D6IkuQPKV3D0VLJphWut1wJ4hM3mHJUusFvhxnRjsnXRzTGW8\r\nQJ7plR0GZ4l5K5r6mYvRjqtKKZyydwcoBMk+kO9gH9sWj7MD/bqROWc1JbRY\r\nG+YIXASJbB7w7Af6w0WG24tR2BmKGO8YXa81IZNpX/ZBoFc0mlj2SEj+Rcuu\r\nY31s1pA2BgYTCH3uObix/w5trv9Oa0pXlee9+jkzMTk2J1eU0EH4mi0ENJiS\r\n5WmOgjWdY+MqdS1fZyGNuTSgz2VJqloTGdjfjktZ2hcwjYET7Le5SIj0NNVm\r\n06iHT9iryvOtyg/wHgoeeC2PLt3yQqjYhqM=\r\n=begc\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.10": {"name": "@hookform/resolvers", "version": "2.8.10", "devDependencies": {"joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.29.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "b66d7a7848b1b1dd5b976a73fff36bb366666e7d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.8.10.tgz", "fileCount": 227, "integrity": "sha512-DDFtNlugsbwAhCJHYp3NcN5LvJrwSsCLPi41Wo5O8UAIbUFnBfY/jW+zKnlX57BZ4jE0j/g6R9rB3JlO89ad0g==", "signatures": [{"sig": "MEUCIQCRhLM0S+KhSVK7hlrtWuufT77rfo1YcCtFoUm+8T+itgIgb+CPRGoYXHlpCJGVEVIEFU6IQoyoW+0pNiLO9i1LCe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 380124, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJig0upACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnihAAm9jhbPQFD/RBXQ+jlC+kTxy2ZY2/HS+1NORa1V+3ScV2SO1I\r\nAeQ2XJhmdAklWlniSpuzidf+8WYaZKJzk4r8Wx2Jw5RjB8umxXiTnHHlS2uc\r\nozTGIlAbQ4daUG3yvizpiDqtplube3YFxhQpDWiVgg/ewfhEq0kEqKfVbWNe\r\ns8cP8ChyNMzS8L7awYXezuU3EV+tsF/tJhTtJsZ3gpnWdFJ7x/5LcFVQ7OY4\r\nfVLXHF0ZO51dWMMptmI7sBKIfjM4oH6ACTZAu5b2L49PrhrKs69DjATSuYgV\r\nvjwVVOGwYqqd3okEX/lPf9UKdGyjbGnVVQBoS1xAjKWy/ezOJhkq3n+mp4Xc\r\n41IJ+cW2x8LeOq9i31/TTvKPA6+nfgpqYhM4jY1iJ3o0lMSYkKKQePCCppfS\r\nmQ7yAZC/0W267LQhz85yvshgC5UK7WnAOVdchsZ0hH47rrVkYjKJfc5BEgbD\r\ndv8/Qs5EUXxnX1g1h2sMvhAhgkUYeeBnxXlZ2tcTdC2qr2f40Q5cRXVT0seu\r\nthjvcHQHYZ5/oq9ZkuKCsUnvUM2PJ3Gm/Xb1Xw46ImZ7Uot0uJ7lzdFmJE2u\r\nfIV8338xIb9P5nBakaizgaJrVfDaES1IyUelYl1mdvhhy7Gssc/z8ZYkcLgM\r\neApawAU3Ap/CzTUdiP63udS4/qU0cmzQiH0=\r\n=P8za\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.0": {"name": "@hookform/resolvers", "version": "2.9.0", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.29.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "0e8c5188fb030f1c21764892db0d04058d5413f0", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.0.tgz", "fileCount": 248, "integrity": "sha512-fW/buf863gDiU60RxCTKooJRf1YRvcMy0MTXGsyv7y0sutx0iYeZwoFNQRLBtS+hi+lo60ZV3Kf8AdqGY+sZVw==", "signatures": [{"sig": "MEYCIQCn8GConLODRAE2P7E28k5gC17tJeJ+kGZ+b+mczscpRQIhAKVS/ETUJ7xuN8V92kIIFfMPInGE3eVULP5uC2XO2FkX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2865199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJimcdzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrW+g/+J4lmDGPCKBqo8jK7Qg2r/w4QZEr/IqHoWzP5tXGXm1gpI6mL\r\nfpy9mDeQstHaH/FqWtR7yz/7Np64s2uKs/eAPFqOTCfpB6VaS4NGH2rB8+qj\r\n7kB7GVjaVsivzIWePODtYbgVuPahgiLlPMKxJl5PIFB9FNvDtvh01L9tZy4z\r\nIYd+SKcthIy9qqVuw3mmPYiQqORo8BgRUA+lYxZbeTHx+TK6PQBIcyQ+YaRi\r\nb9yKBzyZhnoej9szELECEB0JMZvsj1D2zHEqsuyJH5MHlXBsj3zVdIBSIo7S\r\neENxiA2052XdAottPNKeQmpx6bc0sNRyN6K3C+J3gE5BmsSXEAqJ8DmDFbwm\r\nXPTfE+WEbNSyz36E/hfSqa4rfPZpI4lHURwbqcR1AfUcFXktSzpY0w4Zecrr\r\naaxhZ6tgDyleHC949vTnwURJqUY1wPhKSwksYLNDbwPNo+/TUTmPpW5IJb2r\r\nu61VAyl7z+sy87i6gM7iePGpbkRULBwCOlSDx3H7PBtQM4j56RmWNW9n/Xpk\r\n/J8Qx8n16+JPvoTQKv6PoaxeuND1R16VcD/WA8QQ0m4G9/6YjkK1wpC12dNU\r\n/YDZloVGQPr3bT2A8SLf4dWhPFh9pYkx4fJ3JgepHqfLyGWO+32pEXgQYL91\r\n4J5PwvaIsAcD7dJIzZ/lCz/6JKG8vR5kPcY=\r\n=CZyd\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.1": {"name": "@hookform/resolvers", "version": "2.9.1", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.29.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "59121e38d8fc95d2fd1f41c9631393cd21e10b65", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.1.tgz", "fileCount": 248, "integrity": "sha512-80lyFFcExEB7A09PFnl8k7A3obQyDF6MyO/FThtwetk+MTedYMs08Aqf7mgWnOawFGyz5QF+TZXJSYiIZW2tEg==", "signatures": [{"sig": "MEUCIAMWqiRRqKdn3EiPOuRB2FxWaqDUwfmheDQh5ot1YONmAiEAunOoCOPfXBQBoAfslHWmgfXCOW7RsMDA9xDZ4DM59vE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2866377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiovktACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTkw/9EvodeATLdH6UQQTlmFUAw85+Y39Xx38ccPiD2VMUPGQyvyv3\r\nhGSJ4lxERkeNyLXwHS9DIC72Nz430z3MqPMj06+MsP1qPpUOjbpjsfnU2oZs\r\nNRMiIO5pVukHVhHl2m1iq4/jIMgvrzhRhivTjPNWNdd3jbujwJg/IDz4gTOs\r\nIkZIdbHJ1cgDxvibXYjr6TqorGSxEFEwr4UEUXSgWS3wtP98qpvcqvuU3Whh\r\nxdC9Nld6+ap6Roib<PERSON>ZtdQZqG6uJWwrq/WN0teihValBO2tl/5jtVuLTOYWW1\r\nuvKmk/La7S4jms5y/zjyjUMwnGdIsp8X+ynGvcvnC20b0+xz9HcPFRtbLdAU\r\njG3yBGKEPmIvVAIj+Y3X/HX7tOCHakfgYHXe32aPVQJ+YUd7BYx0/wfBaRel\r\n58t0w+GcsCPutgopx+0kGfDV4zHJN/SE3Lp2dWeVLiXkC2hZkmmWxYQOvLRl\r\nuuIYqFvYfAaVhBsWsDlJMeQY78RQ1yVSNUIEyZA3ijVtt6YSssFZfWH2txsN\r\nM6+YKm0p8FdS7Ez7NgnPG71zEAm87HZLt0Qxb/HPCnPw+3CclmRpxD/3ZJQH\r\n4BQGVwtveiQBIZvBf9XyCEP1ZNXnYyxquz+3BpF4AEREaNUSIzchfnT7rwTr\r\n1/9bO+7lZEoWCwXUXeLN5anrtUvU817JA2c=\r\n=J43e\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.2": {"name": "@hookform/resolvers", "version": "2.9.2", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "7.29.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "d21f145ea7331779e10269b4c1ccf599ead5c5ae", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.2.tgz", "fileCount": 248, "integrity": "sha512-GzAgrqeCQ3o8qfB3ojX/S5mgySrMLCwoXhxIPQBGp0OCGlD/uqbzJYMolPAMHG4Hu91fdjIAbqZeFOJXbBDMBw==", "signatures": [{"sig": "MEUCIQDaWfKM2B0NU+BxqLLLEuQC9lo3CZqnjsWEupcUUSiTzgIgMq8xR4AnY/FYoUkU2sbnoEAF0OO1aWnCqG4WZMHnyDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2866122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitOY6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+AhAAlfGbvAlvasUng/r2LEm1GSkP2qQOW32X6970mqOr5VxsbbmY\r\n463AAK9FWKqZdKL2qtpavAZM4MmYDyRLX97jHItTT+dtS5k9yt2Ef1Lu8t2K\r\nnzcXAewM1uwWEAAmkk/zVuNoWm/GCMjoEmlVynm6QtsNKWP8C5Is1N43M1dj\r\nXmde8iL02LIwdzxVEhkBQOObMiGH3p1vEHE7iCb4VE/TanYE/aaqQIEPbLWA\r\neBI0UbvZ1OCRMVXJvLjAfX3IpcD5Qvme6syVi4paUphKmTDj2u8eKL2Aml60\r\nFjzTZmqwkSzN86AS1/Trkk7EQ8IaE/MC29QCL0FjaCcOSJkCa8mbNYFvQq3D\r\np3ATArwwxT6mkDRFRPWzKoinN/XMeac9Ypa8mFtmB0HXrSB95ApXUI1YLLJl\r\n19xj2jzoU/RJL4aRBrMV0PGgzq3oL4TfZHxuFltIlM0HVeRKKM22Qe7dOD0I\r\nMPJKLj9ksd3nRTFhAKMNvACN6+ceWlxu/JpPRu12eRqkhybC4ok13PTd/viA\r\n0b/0ofY4scNSgjor0Yckl5Tdc8e8s3qffjyjTu7jmd+TOkZnSI6O+sAgLTZZ\r\nV9ZvwOkXt6lX+7f0800pcT6COvdGwpAzx9Hi6qdC1EtiulhaVHA789kMYAYN\r\nDD6MXSSU1GiEQRBmqagYMGJuwbiIEohKRuY=\r\n=WO10\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.3": {"name": "@hookform/resolvers", "version": "2.9.3", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "13f6934cfe705e24fac094da377e0621adcfc424", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.3.tgz", "fileCount": 248, "integrity": "sha512-Eqc/qgjq0VX/TU0a5D2O+yR/kAKflnjaVlYFC1wI2qBm/sgjKTXxv27ijLwHUoHPIF+MUkB/VuQqHJ5DcmbCww==", "signatures": [{"sig": "MEUCIHANwLQYW3shjbSsrgQvEQuZ1Q4B0HuIlWFPhJeL0VK8AiEAo/x/myG6cDTJL9zc/cZVBRBGOrSo/IX1F4JLkPO+c7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2865230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitkriACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBcw/+JdIo8nVUT+T1SesjM1t8XHikBY9vlmnhi4v8vyTqrnQ+wYU5\r\nPxdS2RX7yii6xcrMJ9NWUciPHB2nIEyASjpM1r1HyIbQiNvY2llgIQI4w6Oc\r\np+qflpvLvYeHfq9kd5kz98o4XC1R7dx+PnT6JuWsI5NTOeMXmtMsjjBQGcDw\r\nHVmsNgDM70i6ihnGkJ6yFS3dPKTrSYqg0FIkQ10q+HZdHzo3MbMq6Telbi4Z\r\nKGJby5HJQVAeXBEbkysjHL4YyX0ZCi5l3vgybohgp4Gldikh6q9c07ezgtH1\r\nc3J5fSjaoBBQoVvBmgO6ISek6NiUpyjSsWuQctYRbuuXoBe2HNV2KT/iO0Bb\r\nT6NRCPFG8o0b7UmMX7+6pVfKZBRPntokdPHHZ29iIvUPXT9tvRAyNEkfrWEb\r\nHUxwF3AkoXJj5O13QfdtW6ruGRBoJ4G87vOSBpzfo4OS5WcIFZ19ZABx3LA2\r\nrBCY8vLL/p5200c1+zlelQP1Y0FXZiHcL0j9V6glob4PDHqH2gXY56dTYUej\r\nAjSrTE/3YvrIx+WJyX0xENgxKFOrUD/JTE/sUqvBtJw3uxbwdhe3RYPtlV64\r\nZNUhPsFelH3azJnU1UaiXkuKfSrA9+qjJxJvFmxBbw3TVRhJTvatDosUkTXl\r\nyF91U68jeuacbGDXNtYALV0R9nOJE05DapQ=\r\n=8ZPv\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.4": {"name": "@hookform/resolvers", "version": "2.9.4", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "e06d15649c5b8d7ec5fb9cd53ede1ac7b117aaff", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.4.tgz", "fileCount": 248, "integrity": "sha512-rVqwNB5Z5J67QGsJWp9a46z4+MJq8YDUXX8mMIr6hEsGD4bs6c7Jtel+ZNvk0aFfywGRwIEMfmQUrpxjWQck3w==", "signatures": [{"sig": "MEUCIQCa9rxcELLTcJjvix1NxSgQTQYcrvSECXu6BjvFbANTXwIgQCq64EeJ1EikaATwHO45dSXcc8us2IPnWqvhzeMyu3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixoOiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmobqw/+M/ik4S/u8QcwWF+Ys90f7VC7Bb7CWy6jT4XzBHcIzNJvtCYm\r\nGd/FJMFb/B7IvSm6hI1uYUC+Kt97wbKSPg4sIACHLYbKEA9+P/UmUFjZvkbS\r\nG4h5WD/MYwXO7oDMwPhewLjU343tDLmGsc23SyeuRvIkI5k+7EsUSQAEFQJV\r\n7LXcjOvGhEiLxZxekm22krlRFoEWgkhRBWXP8HiqDrgnjqZULfy8SS2wTrGe\r\nPLtH1IuQ7q4vP/aFw+UhfB5ZDmmEnAdXKLshku8buyy9Nw1uYy1Ok90qhKQX\r\nKOlDl3aLqfJsxwpkidnu6tcDGM1GntDmXEI4opfH//NZMXuiPkKCBwTNJIFW\r\nbUyQeszaOP0EDflCOINNc2bEllQR2g/IEKvkmciSdpsMcXJHP0oeHWdSP8Xu\r\nV60vmxbbemYpVtexdDCo6UsEGr7StxTZPEY3UcduXsntBDikzUxdZUFDWX9D\r\ngHXcYW5sfGxrfg6RonoFBfojiN1280BLP/s2ofzKeGmklekHwBZ8ZWZo2Bgc\r\n6CN/vxF24SoDTJ0tpcZvD6BWkJAemCxRXci1YUhpYy8UkRY7gdGV8ODpbN2S\r\nli7UMkDDfPxRgvcPjhhAR9DNdGwH69XYs6ApexjDVh/lkJyoMRS8JmJcI0GK\r\nr50d1AsbxV9xGPCV5CPKOvZ4E8IJYw7hV7s=\r\n=O6yQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.5": {"name": "@hookform/resolvers", "version": "2.9.5", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "adfa2d240a9315f4ab6ff4c63621d22d1b574c07", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.5.tgz", "fileCount": 248, "integrity": "sha512-4XABrKdE4GpX6v2RZ+Ij1Wrl9qPEl5ZvVhFxROY+SZhP/6TU8gPw0LO4ZS9/GgIbTx3Pw1U8HmcXVAoGOq+7iA==", "signatures": [{"sig": "MEUCIQDsjjGLuOlQAyJUrxCVkSt8B5x954eAnAEVrs4Tlkr19gIgESe63dL+QK5qkxzPTroxGqeiduT7Hh36nj3IdTpHpEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix+EJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjmQ/+IchvA5dBplmO+GoXyyiQJ2tqRMiDpqomplvntH3oaKbR9//t\r\nIfI6V47Dac29szBhVOz5RtO9aLR/VJX+8AWMVMr434c4Zn5qt37WEeIBtDyt\r\nXSBuFT55hVdoXu7VZ6jpRXAOmaz1P90FTgilKafBFJCbznd5HAHGvh5wploE\r\nn221+sTytjFR7QjT08lneuOsqWRWwTNHyv7+i8GW03/oX6pywjQ+3X6gixVC\r\nElLuinhrI+G3alvrRrOMPQzEVxq7Rq8ZiDbth0Y8UspIHS+fU9z1Z3V+Uj8U\r\nxvEIYzAQBh0S4fYaLYJ+A48ZAde5zGY4mP00TsRgeTLqZec/wfKwXQ6Wk4Sa\r\nFi2Ax/mxy4EolZyQdAsRUmOqSprxiNfmprFYpeC2Mho7dLzbSA2etS57HSC9\r\nEAo/pZGLL0pKOtyQcpQrK1OzdOQCv5/1jIQiAcSjFVuiDG4zUCGC69gsjvuq\r\nnGdsdH9aG7tb1ZeUoca81W/pMVRB0FNQk0hcbRfmNxpjQmS6gioA4mYHsHQe\r\npqINhg0CQSWA5YEs88+ez4Jc0XtalsA3QKVxNhVxjKK+UzeoF4x8LPz/RJNA\r\nClb/bdRTUs222NitP2hHmp3MvRJJq0e+DcbYbLx3FADiOgT15NVb3Snjq+XK\r\n7KN5lxpB/SG5TeJHmQEtkGV1wilaa+PfBDc=\r\n=ZUxL\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.6": {"name": "@hookform/resolvers", "version": "2.9.6", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "db4277a96d1817d94169108167014926d8a10398", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.6.tgz", "fileCount": 248, "integrity": "sha512-f4VxF8w9rdX0WsDCk3FW1dGPj/Sj8+1Ulcgtm5ymgWEpbA/fjY+NUDh+L9hftmxDgP8+EJFtF+qFK4gPEXVrVQ==", "signatures": [{"sig": "MEYCIQDg+PpMranhi5Cvs/TfevqdkGdu8isxSzWx1DBhHe5EUgIhANzWbYusx5SzouNAIVkI6u/VmvS1RDapcyKo0xcaL5eo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0RphACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsXg/9FPwdGZRlBYh61/vI6VTIC8ZzMu6vuACRKOMP+70IQZG5gkw7\r\naxPdMnR3hsaXIU0UzYI9Dqqnrj/WIpngFHAp24AWtRDJvutTTrwUqirhPXAP\r\nuL/hyaAEaf+92lHhaxtylfJtYfDymkQ5HgBSwOFjrIS74DG/7OOD0TPFiHjO\r\neuJ9TTBmGCGRrHqw5+Oz8w/hEgtMCEgp2fyUZIM8pZROnNmJpjAp9ZJQVyhE\r\nIaWFGkQpOSkFuo821JWmhLsHqRtofim5YBuMi98zvQAqqe/at1TwCFU3Zdx7\r\nBRZoUYaLyRIYNtTouMbO2rJW8v/PEZePRvCsCbDX5grrzqxdRAdXic33bjly\r\n0jxC/nTTHllNBrfsauusmqmbC0t5g0+yVMJX8h5otupILpeb+ThDbyVJYgR0\r\n/+bdkhp0ODxO4DLSN06cTz+hf1APTAo5yUVfTPE2QIgaTvUXaSC3G6oPLb00\r\ntU2Wc7mJCxxAAnlscsqmyDMAPFGk1fY4W6jljIQkUpqDSxBW9jIHHKPvfKjT\r\nVnxCFOVhHHLFwN5GdserYFI9jh2cRJZQ+PaE7H8G+tbobcjMlEMnZ7xY9roC\r\n4MUoamuEI8x4UVZpGkgpEH8lxekeIE+DJr18xiNXgVh3TqPEq09zLAiS1yWM\r\nzQ4qd9BOhoNzl8Zyv9iBmAxc9IGLgjFeMVQ=\r\n=MRww\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.7": {"name": "@hookform/resolvers", "version": "2.9.7", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "8b257ae67234ce0270e6b044c1a61fb98ec02b4b", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.7.tgz", "fileCount": 248, "integrity": "sha512-BloehX3MOLwuFEwT4yZnmolPjVmqyn8VsSuodLfazbCIqxBHsQ4qUZsi+bvNNCduRli1AGWFrkDLGD5QoNzsoA==", "signatures": [{"sig": "MEUCIGwFIg1ZzVW2ujEL5uAJgUad+OKGTTmN8oPHscJNSO1DAiEAudnMEqEQDQ2bfgkjGGLP8AqO/+uDuc+h8Ltaelepjc8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2863898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4HqYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5Uw//cL/dsmf/D/oYTPoorCsu25dwXAPoPfJBlnYchq9vozVfBVSQ\r\nEnqTZwmOGW198zJh6srKWgcUgYEuPHnh2jjQqLuqtLtEq9n1c8VMLQ+omnoN\r\nSNTyE4miMdLUWQSqwh2dtoIz7ikWy3KdXI+qzv9NijdJpFlyDEyijc0r4tyN\r\n+A6FfwowRZCJErY1rW4owcd/BSSab1BNpWn+3KKJJA/rQmLtHFU0jxpTVkiV\r\nmM6Mb2OxnuabDMzKwncz43nXLIInmosl9rPSZMBn5yosxkTE7nT585m9TZR3\r\nDUNzqP9dIAOiys9Cy+YkGZQ4h8Duy7PgxnKXdeV+fwDq/GVikled+i6mIOWr\r\nQPkemfYXjOvi5Cj0z9eegl3VO/RDQWOty0cWWfKMBzd5uft9eX0+gdssAbQV\r\nfFwZKU5+PzpmdCmM/23SRzla3QViXbu2HfD73nIiiwybqA9/lqY61Kp23uhw\r\no9q54rRfEbqGEGX9UI6oAjoxTjfFgyECjqqpT3xWG69R9hAdGHv/JeREIZa9\r\nKPBQlAkOEw+PTlLNoCg6uQ7XrdwEEx/1FazzrVkVoITx47BjkeKtn9IIxaBM\r\nVEmGZyhvncih1446txck2EcGmZS7eYZlEI4b+krFSU+OO3q4Ml11qzuHhJxT\r\nPqFBK0CTEffEddniWhRodGuo3WUOWEl27kI=\r\n=K/Ac\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.8": {"name": "@hookform/resolvers", "version": "2.9.8", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "9af51d15602f3c6e3249714712d8275564e65b72", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.8.tgz", "fileCount": 248, "integrity": "sha512-iVVjH0USq+1TqDdGkWe2M1x7Wn5OFPgVRo7CbWFsXTqqXqCaZtZcnzJu+UhljCWbthFWxWGXKLGYUDPZ04oVvQ==", "signatures": [{"sig": "MEYCIQDYoYTqk0sOZxz94aiWyaNnDeepq64c6XRy3cDQxNdgJwIhAMDfxo5rp2D1xRNOH3EzKLWQOVPrtHCQUGm18M6gksed", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHjJiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4pw//YOuBcqLvViZdzregh0+2ZHh4vxBvv1fOyGSb/UkhSkKIxMpE\r\nIY9kTrDR06aKPoSdw72+/4J7V8ZpzRHrOszlXHEvgaK7sSuPMItsvTQasgnY\r\nVc67PT90xIkNHlj6gTBmosgABjGVh3yViH/V6qI7avxhm0GeyMn+H5f0vo6y\r\nszQfWXUsfMjM6GErc2RkoNwPepGSdDrc3MRj4TTtxk6O+/9wEPkhQtGFrWTu\r\n89L4J0NauOfAvWV3vpEqCPGr5nTVn6iAlCBr2K/aszyO7e6KF8uDxWflWhJL\r\njjPJi8gl8aIsaTaod1tVFeL/TFhAeDaQgL3Yyl1vhbFjuhg2/jIeVyqvqgD/\r\nKLkQ5ChyEmkWS37YAn5rA5w0Mc+9ccgV8JaqwNqzaEeyYKRHyLJr71oQgc73\r\n1ze7LlsRrG8J0qN/MGmF/Q3bNDFQXh/XugRKrEQkah8HTdieinqztBL/BMdD\r\nQFQ/VyKJjN6BdYhaMJWnJ7oy7uJp89CQWnxZiRs6JqsjW6LZ+xYu7fWUeXQz\r\ngKWVcA+iixpMltfoYSZNLDBL34TfJgSag71cK1nHFZr820+nS+1IKB9vAM3r\r\nDVhW7UL7q5TZeEDmOry+KhoMshUpNnvusgl0ODfFckNXvwSB3KbxUhwiAshV\r\nsTz2/iXatGG/2e0aDIG7637qSw+sYTFIP8g=\r\n=XCxL\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.9": {"name": "@hookform/resolvers", "version": "2.9.9", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "db87ae0dec1ac006b2f01caf8d53422d7bc341ff", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.9.tgz", "fileCount": 248, "integrity": "sha512-WMKO5HbNoGMdQNmf6kcjHmBKPd3voKvDdzr+IQGn1Rtb7dIQbTE33zf5dptZnv8dF4K0NvnzpTgz3bLdeAPc9w==", "signatures": [{"sig": "MEYCIQCnhDlooeB1O4vCU2siuTnytjTUMwHjXvB907xrkbvkjQIhAMvZAnscwgPagvxM10j5BZv7Sl1iG9U6CnJqGqXF7rV2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2864286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSVTjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZ9g/9Hko4C2jWy+OxQrYhyevkZ0Sbi3znS5Tw8Vv2nJ1taitIi/YU\r\nc8f4ZX0y0GP7n4+pOX/rK3WEMiSeuHQKl6BSMHLHJSV/KtcSi2MII9QjK/Zs\r\n3wo4Q7Tthn6ta/rEXfECJePRdKYV+NADDYxlwLKMyYCQBwQYG+Yuygao4J9Z\r\ndiVSticw8qx24CoHGlZIliHEG5+Jb6OFKIYjDnEUHtHK+Ixd9qNQmzdIe19u\r\nOE7Q4PAZVe8aMXMVb59D/mcp0VL4N5UjGvOp7QcJr/YjDAgXapjYoncS2nM0\r\nB9BLK6hXT+6OnetfLFC7fefUEn3fXAK3kcNgOKWZhDj8m88ByA1JM9mWs376\r\n6E9IZjEEz2jgHtogGyeV879/AI93BtNNSTJv3uFFfwd90h8PrGcCAmAtwVs1\r\ni0gGwlHtq94bccFaCfYlWTUg/S0XMpfxjnl3Yuh56OL2iJ/5BaZYH3hgEmV1\r\ndasSfXWeNXnslvkoJ/i+rh/bbvMaqTGxLfgjp9Lp2JVzB4f+dxGa7Rqdm+xL\r\nUlRyAY3STcDm7j5kHf39wTHCeMpAvcJ1mwt1ayG3Vj9IFYVTQCCqOMCTPKdJ\r\nS1ivyPm0JDkjPDaEJCYxVXludIODPTicVOkqnkk3KajICjtIi9SMLldG6nof\r\ngcWN59Zu/YY6g5VV5qXliBotuyiiffi8zJs=\r\n=3ES1\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.10": {"name": "@hookform/resolvers", "version": "2.9.10", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "e7e88942ee34e97b7bc937b0be4694194c9cd420", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.10.tgz", "fileCount": 248, "integrity": "sha512-JIL1DgJIlH9yuxcNGtyhsWX/PgNltz+5Gr6+8SX9fhXc/hPbEIk6wPI82nhgvp3uUb6ZfAM5mqg/x7KR7NAb+A==", "signatures": [{"sig": "MEUCIQCE888GOIOxtS5F56DVpBQhaBXLqo+pFPKEq43DSewTjgIgSD6ZyMcXNqD8HFyj0jQwnrBnX7nACTepxhowJVxGy1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2865485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUTiwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcFw/+LnIhZhXpKVEwWFPyv7QZWPGN7zsZJYlVRbqm2ddqpXCaxWqE\r\nOLwald7aXrYYGmR1XDsjWqEnAurngvTNnsropGJIRyarm466N0KV7R15JArC\r\nkEwuBdzWvQsU3kxd+Jz0JlZPFvVC4i2Wxz1JXXXw6TDO/CeHbUvQ8QXsdvsz\r\nYyVZwmNqRvTHqy1kLs1mqjGiEJbAYcrOE9MRQ2FJ3xIbyprBmWTuzAamn2eF\r\nWfb<PERSON>b34r4bEJBU1ihRUHokFkyJxqTohiJK1g/0KUrAZ5lcOLvjBOTwRmuor\r\nyErWggpT44U6BBt+exggMen2cXdj0hWKWXf+W8vpTCGh7OSE/B76qI9ZKQ3C\r\nhf5GQ5ZHt3nBJk43MbOc0wQo4fzwaDtqGGQAQrmLamHT9IFuupHG530x8UVf\r\ncYZWGlL3ojwVruFR8qbGC0SqthCx+cQZCyPboF/uov3/bGwCwf995qJaFifv\r\n/jvaJOtN47Ki6Afh8yABXUOB8MCKQ8FoQs/dyaYe6iPuDqIPefLRh+6d51Ym\r\nrvRk7YMWAtixa3EwK/ZiUGYoIUpAEVQ+EtX2Fba7H+cJjbnOjvY5auHKx/PK\r\nbuy7Q9/J2vU7+2n3KTmJFbos65I1xNzCgYVhzfg3cvE1C4tN2yUYwAsxYBgJ\r\nH65HKhWb45dGwuAr9bMALuiCsIkSsjV0gSM=\r\n=F/WB\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.11": {"name": "@hookform/resolvers", "version": "2.9.11", "devDependencies": {"ajv": "^8.11.0", "joi": "^17.5.0", "yup": "^0.32.11", "zod": "^3.11.6", "jest": "^27.4.7", "vest": "^4.0.3", "fp-ts": "^2.11.8", "husky": "^7.0.4", "io-ts": "^2.0.0", "react": "^17.0.2", "eslint": "^7.27.0", "ts-jest": "^27.1.3", "prettier": "^2.5.1", "typanion": "^3.7.1", "react-dom": "^17.0.2", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.12", "newtype-ts": "^0.3.5", "typescript": "^4.5.4", "@types/jest": "^27.4.0", "io-ts-types": "^0.5.16", "lint-staged": "^12.1.7", "microbundle": "^0.14.2", "npm-run-all": "^4.1.5", "superstruct": "^0.15.3", "@types/react": "^17.0.38", "computed-types": "^1.11.1", "nope-validator": "^1.0.0", "class-validator": "^0.13.2", "react-hook-form": "^7.33.0", "check-export-map": "^1.2.0", "reflect-metadata": "^0.1.13", "class-transformer": "^0.5.1", "@testing-library/react": "^12.1.2", "eslint-config-prettier": "^8.3.0", "@testing-library/jest-dom": "^5.16.1", "@typescript-eslint/parser": "^5.10.0", "@testing-library/user-event": "^13.5.0", "@typescript-eslint/eslint-plugin": "^5.10.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "9ce96e7746625a89239f68ca57c4f654264c17ef", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-2.9.11.tgz", "fileCount": 250, "integrity": "sha512-bA3aZ79UgcHj7tFV7RlgThzwSSHZgvfbt2wprldRkYBcMopdMvHyO17Wwp/twcJasNFischFfS7oz8Katz8DdQ==", "signatures": [{"sig": "MEUCIAq0CPtsV6uK4TMNPMBDHPxX9MKtXgCdWI7fTNwS2COSAiEAkZnZvbmqB1n56kpAnkn5W5Pr/x1F5YJByNUfhKZA8+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2869131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj41qcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0AQ/+Nhmf8817WSPbJIU6ZTxtEvSA7bRO6r4Ug0j+Fc1Fj2r929Zb\r\n++/TIVpkNwRHRi5/+yhV+Qz8/gOVovKtswkktKyW1U0x4Y78kT/z3iyV26dU\r\n/N3ngoKqBhqmDj7fLHnmcIKo+2M0s1hdNZeLtBTrChY+Ki4yLEJAkcRMKKRx\r\nxKJUJLT+NZLGPm6+t/fC/6/Fkq0RssvhD7Zy5bXGf8VjE/5Ti+AIiDYCgI+M\r\n39l9lPmk+arWm3ssZ2jwjKFtD0PmbVLiYBm4UW6nV/z5+PJIQUN1i0/e3rQy\r\nBCfw5mcOcAo5KrTuG2NVpeKjIgJSuNBE/Q2bM2oSyeEA3cqCmE6No+pDd4tr\r\nyWOkzidm2mBbqIbMlp5oCDayn+Fv2kxZMLU0X7VjQy5LhW+qDu/ZROZdvVrk\r\nYkaXcxJCSS+QowpPBQp1Gccx/VscNgJXf0ZlXxbx/xgZjKfUew3aOTNSnmnW\r\noWX5CXGx3GQ14WAUIAvNXjzRyg+ALMvdBcgaJWkd55vzSj/daiuXCCvDe3Nk\r\nLJloHoowVciZK0Nr2BZRtnlHRYjQliLmmkIuIitjeOXJpVyRJAQ4YBriAcEd\r\nWohdVG3LcLMFdGJ4Y64Fon047Vqe9fLeuZkDTkvVd9PxPWI2h18pgp2hnsIi\r\nR1eNcCgbUhht7aGaTh7AaODPtwASPy1n6SQ=\r\n=yNrT\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.0": {"name": "@hookform/resolvers", "version": "3.0.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.8.4", "yup": "^1.0.2", "zod": "^3.21.4", "vest": "^4.6.9", "vite": "^4.2.0", "fp-ts": "^2.13.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^21.1.1", "react": "^18.2.0", "eslint": "^8.36.0", "vitest": "^0.29.3", "prettier": "^2.8.4", "typanion": "^3.12.1", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.0.2", "@types/node": "^18.15.3", "io-ts-types": "^0.5.19", "lint-staged": "^13.2.0", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.0.28", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.43.6", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.25.24", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.0.7", "@testing-library/dom": "^9.0.1", "@vitejs/plugin-react": "^3.1.0", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^8.7.0", "@testing-library/jest-dom": "^5.16.5", "@typescript-eslint/parser": "^5.55.0", "@testing-library/user-event": "^14.4.3", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.55.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "bb9346f1550a472a980af5b08e549366c788ff76", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.0.0.tgz", "fileCount": 271, "integrity": "sha512-SQPefakODpyq25b/phHXDKCdRrEfPcCXV7B4nAa139wE1DxufYbbNAjeo0T04ZXBokRxZ+wD8iA1kkVMa3QwjQ==", "signatures": [{"sig": "MEQCIGPVzkzxNVSa+mdemeglVSS58+LBg9wtwwg9l4Urw+2GAiAg1mGMunTdnLdVhwNsnATE8qZ3jNLnvuJ/N4vTu/4C2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470066, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGEO0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowPg//VZ6kwTf1y3pMU0ZzDmWQA68qx/pp1J/6DYTAV/lA6bdwcHHN\r\ngStmDU8vygYJBv6pbZQ22F3jU17Yd1wJ8zllM2/+naJ5dVQp5CRxuBEGTyZW\r\n71K5+bUtIVhxoBev5G4p83Es6EsICOA6HG/y+c7sxkEaAIYzBpaJqC6jkMy9\r\nNjK3fEVp3P93ArM72An7O7z3SMEsTREWf95pVHDAnD2kww6FYG0n1RGkjVhD\r\n1CK1Er0sWiL76vpwLyL8AgpOqCWqOwjqmGJDp8wjEn2jsuqaMAdPJ3nRRc1J\r\n4UOJRqmnv5tx44ZaUJHc81r30jrzCUY7KNjEVyGnJLQ4ic4qsEmmQAs/qQfR\r\nucwh8uY2ZCQG3gIcXxfPAOIeE0fEkczjk2ESQwy/IJmeQnHsNLLq6vfjyrGS\r\n5wjkgrqqx37uPi4uKnnAsdoWBbyNrOEjkKwFh34q/1VpIZI3QTCcZHpijzzF\r\nKk0mtOslTw0fKJLT+EONEwb3oET/tPhP/RouwdmrdlnpPOos/IfIhdJdmsIl\r\nEooqxTeJjJZhIp2dY8BnEud645BExGAMqPe14BNtLhhE+Dl1NDTt+IYgqMN4\r\ntcQ0aIFjnYCx8+q6JwKTYf9DMjY4nq11R2pwmBMzWPIhnwteEHftmIOq982g\r\n5H6IlBq3zeaA/6mVNSytuaBcm/857vViwlI=\r\n=ZiBS\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "@hookform/resolvers", "version": "3.0.1", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.1", "yup": "^1.0.2", "zod": "^3.21.4", "vest": "^4.6.9", "vite": "^4.2.1", "fp-ts": "^2.13.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^21.1.1", "react": "^18.2.0", "eslint": "^8.36.0", "vitest": "^0.29.7", "prettier": "^2.8.7", "typanion": "^3.12.1", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.0.2", "@types/node": "^18.15.7", "io-ts-types": "^0.5.19", "lint-staged": "^13.2.0", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.0.28", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.43.7", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.26.3", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.0.7", "@testing-library/dom": "^9.0.1", "@vitejs/plugin-react": "^3.1.0", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^8.8.0", "@testing-library/jest-dom": "^5.16.5", "@typescript-eslint/parser": "^5.56.0", "@testing-library/user-event": "^14.4.3", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.56.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "e030f8ef5c407beb5be9bbafd68059cbdd02e0cb", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.0.1.tgz", "fileCount": 271, "integrity": "sha512-n5oOt0cLw9mQNW3/k9zWaPsNWQcc0k6Jpc7XUrg2Q/AqqsHp3IVa1juqHCxczXI6uXHBa69ILc4pdtsRGyuzsw==", "signatures": [{"sig": "MEQCIDWRttvtn/z3lT9mZXSdQ0MI68wyayqb0izDXz2yTLq4AiBHoh4l1cTabdCaKjwCa6UmowvNil+I7Ee3TynAyN6oWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470616, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLSXTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4/RAApOKSgaXXgtJJxEEJndcWj5ca+Ss4YPtlLOX97LUs7G9ZxEAq\r\naGBkmTNlrkNAC1jdMwy8UWAtkqbSp2CPjERw8bPVB6Ihpeb/m0lKgrJl9gi4\r\n2uHK8MaSspeKK+SQ2nL9c6OqgvBum8/0BsL9Ny2sbsnoWFy87ZJgfjk7AoKG\r\nrRU8OGY32XBIw4PorroNwdrfmPAc1rYsdzIB2eC6aTExEYDVG/y617XGB6+z\r\nkK/TNgzVVH/whvZhUcJJA8diikG3Zyufo4VF1dOB0K12/akvr1lTYsgpWsCI\r\nJAJkFPyxpfdXIIomkNOLTnkv+Ku0Gw8nXJoNVlZtUq0Jja4eIh1tQJLLNF7L\r\nrwDp7vNLDN4Ynqt7/3/tFIpZZfrgBoXrt0RWM2xuGnpxKlDDW0AXwXvRdCDW\r\nkjQ9QB8z/lt6NyehDxS3NN6hKI3N5elg6NpqceyTS8SF3IIROowDv1TpuhDf\r\nDakNqVRJ8hMpwwscZvcnVK0nzu6jygfZdN5GkQdQuiNdUX1vIu65xrPAn0qF\r\ngmi5hcWDAEPc+7Wsof0Zj8gZEECMRmwR2UCdDxnGY61KMjaE3oYI0ZEoinFc\r\n7Gk9lV4QKAAjW5KGzIfFYWgR2I1Oqk00Lp2mGuvowuTli9DfB9/08Icb3kzS\r\nRCxpy/GxMt2ilXKSNp6WR399Zpb9iGbtES0=\r\n=8hJt\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.1.0": {"name": "@hookform/resolvers", "version": "3.1.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.1", "yup": "^1.1.0", "zod": "^3.21.4", "vest": "^4.6.9", "vite": "^4.2.1", "fp-ts": "^2.13.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^21.1.1", "react": "^18.2.0", "eslint": "^8.38.0", "vitest": "^0.30.1", "arktype": "1.0.13-alpha", "prettier": "^2.8.7", "typanion": "^3.12.1", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.0.4", "@types/node": "^18.15.11", "io-ts-types": "^0.5.19", "lint-staged": "^13.2.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.0.35", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.43.9", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.27.6", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.2.0", "@vitejs/plugin-react": "^3.1.0", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^8.8.0", "@testing-library/jest-dom": "^5.16.5", "@typescript-eslint/parser": "^5.58.0", "@testing-library/user-event": "^14.4.3", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.58.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "ff83ef4aa6078173201da131ceea4c3583b67034", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.1.0.tgz", "fileCount": 292, "integrity": "sha512-z0A8K+Nxq+f83Whm/ajlwE6VtQlp/yPHZnXw7XWVPIGm1Vx0QV8KThU3BpbBRfAZ7/dYqCKKBNnQh85BkmBKkA==", "signatures": [{"sig": "MEUCIQCidxVFAMtfsUW/tLn+UYJDC/Dg7NYHqxIrwSzGD0RUMQIgdzGF1smwQGuxYz58AyeqMT0ob1pXYdmbLX1ImnvtCnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493979, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOtJrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHDw/+KT7nxvOGBtZ1+RdlKtIK48AjZhj0D6JhT91lefCJvUTj/knX\r\njiQwSvakOJjfK8k1PTwRlWNmyWZbK7rP3evCyTGhJvOoqwbEMhFAZo2Y53p5\r\nXUfohQukwKRLmVF4cToVI1UcSts6OfdigBzCf0bkIMXxkHwpJ2s5MzwobFEb\r\ngRg9QYSABup6CTHpiGg5TJQ9KQEQ4DPOGCjkBvIRPcbJ4UkbIXc8m9/PMT03\r\njewC0coafTE+NuT2kkpnYlP4IoEZcp1AvS+64z5lhaoU7G72esnpnOe7Vm4f\r\n0tf1kgBC9pG+6KQtQ1MfqnuLWngltFWM54ginnW0k2vKA+KKZ6TQ0w0zDzQY\r\nqnXN3fZ7JSzHyvlU7ufBCXRyDIptIBEfJ1Tce5AFq6VS2QGJN1EjKOeOxmHA\r\nj2ZiYS1c2SCR5b85h7J6b9Qw7siHLtG6rMrAIX6IV9ja7pTlKUtRB/v42OsV\r\naTNeyBJGrTlo3gc5yUGMVs3lCVCuhis120LiS20WjKTKzCTZxpjgWLKwlTf7\r\nl5xBaTKLqyHDMVCXdGKFVS6NmfecLRqjF+hyYQTSwbaNT88QjBevrLrIH35j\r\nsE4zQW2yHR9eUNNV2zIxdu89LVnhYc4AQvm1GRitYDiRt60jJLv0hd61QQE1\r\nWa982oLq7CFHpLOBDi5TNgDQQ9vuBoVH/uA=\r\n=pz2s\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.1.1": {"name": "@hookform/resolvers", "version": "3.1.1", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.1", "yup": "^1.1.0", "zod": "^3.21.4", "vest": "^4.6.9", "vite": "^4.2.1", "fp-ts": "^2.13.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^21.1.1", "react": "^18.2.0", "eslint": "^8.38.0", "vitest": "^0.30.1", "arktype": "1.0.13-alpha", "prettier": "^2.8.7", "typanion": "^3.12.1", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.0.4", "@types/node": "^18.15.11", "io-ts-types": "^0.5.19", "lint-staged": "^13.2.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.0.35", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.43.9", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.27.6", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.2.0", "@vitejs/plugin-react": "^3.1.0", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^8.8.0", "@testing-library/jest-dom": "^5.16.5", "@typescript-eslint/parser": "^5.58.0", "@testing-library/user-event": "^14.4.3", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.58.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "b374d33e356428fff9c6ef3c933441fe15e40784", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.1.1.tgz", "fileCount": 290, "integrity": "sha512-tS16bAUkqjITNSvbJuO1x7MXbn7Oe8ZziDTJdA9mMvsoYthnOOiznOTGBYwbdlYBgU+tgpI/BtTU3paRbCuSlg==", "signatures": [{"sig": "MEQCIAoBqOP5VvwTydaMQc8GxjC053TpXB3GgA/49dQLBzb3AiAeWB8ZX+xCvtHakJ0trbrVUFDHHlsY5R3XD/7XCtttxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 494751}}, "3.2.0": {"name": "@hookform/resolvers", "version": "3.2.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.1", "yup": "^1.1.0", "zod": "^3.21.4", "vest": "^4.6.9", "vite": "^4.2.1", "fp-ts": "^2.13.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^21.1.1", "react": "^18.2.0", "eslint": "^8.38.0", "vitest": "^0.30.1", "arktype": "1.0.13-alpha", "valibot": "^0.8.0", "prettier": "^2.8.7", "typanion": "^3.12.1", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.0.4", "@types/node": "^18.15.11", "io-ts-types": "^0.5.19", "lint-staged": "^13.2.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.0.35", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.43.9", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.27.6", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.2.0", "@vitejs/plugin-react": "^3.1.0", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^8.8.0", "@testing-library/jest-dom": "^5.16.5", "@typescript-eslint/parser": "^5.58.0", "@testing-library/user-event": "^14.4.3", "@types/testing-library__jest-dom": "^5.14.5", "@typescript-eslint/eslint-plugin": "^5.58.0"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "0f48f57d464a216fa4a2d76b0f1317b91f91e99b", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.2.0.tgz", "fileCount": 309, "integrity": "sha512-skXQHhLxq0Sz2xDwCyv5dygBCtXJe1GmWwxDzfdtl0X6agD6qcyTG8HrZWkjJoy8AkiLARqYvSYJ8z7+Nwmi7w==", "signatures": [{"sig": "MEYCIQDDAHBqYivVNHtsMNpwFAEWX2NYIOyRmcL6zIMB6uT0BgIhAK9VJpcMJDmfstAZF4kJuN7m/JXeHuq8GO98dQNNrNJ0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 523733}}, "3.3.0": {"name": "@hookform/resolvers", "version": "3.3.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.2", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.12.0", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "5e37c2926b827d2577c1bd2676a2a705984b334d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.0.tgz", "fileCount": 311, "integrity": "sha512-tgK3nWlfFLlqhqpXZmFMP3RN5E7mlbGfnM2h2ILVsW1TNGuFSod0ePW0grlIY2GAbL4pJdtmOT4HQSZsTwOiKg==", "signatures": [{"sig": "MEUCIAeKvx7EKk6uiuay8oU6jq0eBw8OltkJKiREZpSZfUC6AiEA4owJKD0ETys8OS6BNaBvGcPmYYpshV6PoZcrz8WL0wU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 549901}}, "3.3.1": {"name": "@hookform/resolvers", "version": "3.3.1", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.2", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.12.0", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "b7cbfe767434f52cba6b99b0a9a0b73eb8895188", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.1.tgz", "fileCount": 311, "integrity": "sha512-K7KCKRKjymxIB90nHDQ7b9nli474ru99ZbqxiqDAWYsYhOsU3/4qLxW91y+1n04ic13ajjZ66L3aXbNef8PELQ==", "signatures": [{"sig": "MEUCIQDzJ7lkeDtBX7P64WWYLY/OVx1b76HGiRayDI40V6B0tgIgW8FUBP5/Kr98/ZTpt/MxWCW1QnQHne6nTebO2f9S6JM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 550247}}, "3.3.2": {"name": "@hookform/resolvers", "version": "3.3.2", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.12.0", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "5c40f06fe8137390b071d961c66d27ee8f76f3bc", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.2.tgz", "fileCount": 311, "integrity": "sha512-Tw+GGPnBp+5DOsSg4ek3LCPgkBOuOgS5DsDV7qsWNH9LZc433kgsWICjlsh2J9p04H2K66hsXPPb9qn9ILdUtA==", "signatures": [{"sig": "MEYCIQDd9coP/TF5UQg0qc7TP4OR73ZCRk6CU+xa8Diz9/LkIQIhALkLbx+O7jypM8WNuNDlWDM255J9Het5gkBKEZYh/xWE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 551009}}, "3.3.3": {"name": "@hookform/resolvers", "version": "3.3.3", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "a90c786e5564e58a029d157347e1f2965d02e64f", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.3.tgz", "fileCount": 311, "integrity": "sha512-bOMxKkSD3zWcS11TKoUQ8O0ZqKslFohvUsPKSrdCHiuEuMjRo/u3cq9YRJD/+xtNGYup++XD2LkjhegP5XENiw==", "signatures": [{"sig": "MEYCIQD1ue2vD9UfZlQY5vV2uqRT/9CVFR0zBn/rDGPuAHq5jAIhAOzKEuGFF5KzE+HYfnNFprJATdIHSfN2oqtd2Z9ayyOZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 555104}}, "3.3.4": {"name": "@hookform/resolvers", "version": "3.3.4", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "de9b668c2835eb06892290192de6e2a5c906229b", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.3.4.tgz", "fileCount": 311, "integrity": "sha512-o5cgpGOuJYrd+iMKvkttOclgwRW86EsWJZZRC23prf0uU2i48Htq4PuT73AVb9ionFyZrwYEITuOFGF+BydEtQ==", "signatures": [{"sig": "MEQCIEmLhhIfX9D6MvnU7OWYpZT4pZUpMQQILnzjbPR0ujjaAiBH5kBqH/cIYsqmJOiVqNRwSfbu+g0wVSy4RVcm464Rqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 553912}}, "3.4.0": {"name": "@hookform/resolvers", "version": "3.4.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "4ca2c08a26118fc8b6a572da94fdd57750a79b62", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.4.0.tgz", "fileCount": 332, "integrity": "sha512-+oAqK3okmoEDnvUkJ3N/mvNMeeMv5Apgy1jkoRmlaaAF4vBgcJs9tHvtXU7VE4DvPosvAUUkPOaNFunzt1dbgA==", "signatures": [{"sig": "MEUCIEDvteb76lZjvHbLAdTJShEODm+kJx/ObRmaIZI7L4w8AiEAw3fxx0HYiCkXw5FfVJ4lmmaQt5KFdUn9OE/+eQ4OTWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 582977}}, "3.4.1": {"name": "@hookform/resolvers", "version": "3.4.1", "dependencies": {"lodash.set": "^4.3.2", "@types/lodash.set": "^4.3.9"}, "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "ccc3f23a4ebebd0d74815f2256e0a518814b44d0", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.4.1.tgz", "fileCount": 332, "integrity": "sha512-C4WADO2txSnn24Iozefy++fq/nzedcjPKhEF5BsFsVPqL/z2ttT596jhlszlpVwUW9oMTogvkcqlmKi4W5ofew==", "signatures": [{"sig": "MEQCIE6onK65CcPUjnyK6dTkntHgJVoakne0ZbR1nbEQW/XIAiAhr/BTLRDMMx9mGgE3hQ0RUI7JH1FgeHBId7NS7l4PNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 583476}}, "3.4.2": {"name": "@hookform/resolvers", "version": "3.4.2", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "1.0.19-alpha", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "b69525248c2a9a1b2546411251ea25029915841a", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.4.2.tgz", "fileCount": 332, "integrity": "sha512-1m9uAVIO8wVf7VCDAGsuGA0t6Z3m6jVGAN50HkV9vYLl0yixKK/Z1lr01vaRvYCkIKGoy1noVRxMzQYb4y/j1Q==", "signatures": [{"sig": "MEQCIH2QbF8KdF0WgldfhQQqJMusOaEZQ6Jun378W6SGvzMGAiBW5w5YTD6i8F3BYzIXFGatt3t8l1xlRpvgA18scw8Yvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 595311}}, "3.5.0": {"name": "@hookform/resolvers", "version": "3.5.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "2.0.0-dev.14", "valibot": "^0.24.1", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "649ddf4fb10bb5a52ef4b6e5a157da1f1bad7b9e", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.5.0.tgz", "fileCount": 332, "integrity": "sha512-bgL8iT0/F+2Q7wB7vYSFu3H1c70C3yI2f9evVw0Td3yf6Fa9eBp6nw5L8mzVJV01EGN1Ky+FhlzGSIoZBOeHDA==", "signatures": [{"sig": "MEUCIGHaqd+d+YZyx2TG5cKqIW7EvwTFxczjfPdq37XVtqz4AiEAzeboYdZUk+N0JwRTnWwNLHcIsUYd3aoi5CEmIemqoi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2641593}}, "3.6.0": {"name": "@hookform/resolvers", "version": "3.6.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "2.0.0-dev.14", "valibot": "0.31.0-rc.12", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "71ae08acf7f7624fb24ea0505de00b9001a63687", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.6.0.tgz", "fileCount": 332, "integrity": "sha512-UBcpyOX3+RR+dNnqBd0lchXpoL8p4xC21XP8H6Meb8uve5Br1GCnmg0PcBoKKqPKgGu9GHQ/oygcmPrQhetwqw==", "signatures": [{"sig": "MEYCIQDP/iixAu/pHOzD1DtWpJGjviBYMZqXmVYJstAuxw1fjQIhAIW0SG8j2hW6mZojQhxO04klNAxImaC1dbWpqAnOLAje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2640401}}, "3.7.0": {"name": "@hookform/resolvers", "version": "3.7.0", "devDependencies": {"ajv": "^8.12.0", "joi": "^17.9.2", "yup": "^1.2.0", "zod": "^3.22.3", "vest": "^4.6.11", "vite": "^4.4.9", "fp-ts": "^2.16.1", "husky": "^8.0.3", "io-ts": "^2.2.20", "jsdom": "^22.1.0", "react": "^18.2.0", "effect": "^3.1.2", "eslint": "^8.47.0", "vitest": "^0.34.2", "arktype": "2.0.0-dev.14", "valibot": "0.35.0", "prettier": "^3.0.2", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.1.6", "@types/node": "^20.5.2", "io-ts-types": "^0.5.19", "lint-staged": "^14.0.1", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.3", "@types/react": "^18.2.20", "@vinejs/vine": "^2.0.0", "@effect/schema": "^0.66.14", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "class-validator": "^0.14.0", "react-hook-form": "^7.45.4", "check-export-map": "^1.3.0", "reflect-metadata": "^0.1.13", "@sinclair/typebox": "^0.31.1", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.2.0", "@testing-library/dom": "^9.3.1", "@vitejs/plugin-react": "^4.0.4", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^9.0.0", "@testing-library/jest-dom": "^6.0.1", "@typescript-eslint/parser": "^6.4.1", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "8d758bba68b2832ddb975019a7ec455282b28f71", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.7.0.tgz", "fileCount": 353, "integrity": "sha512-42p5X18noBV3xqOpTlf2V5qJZwzNgO4eLzHzmKGh/w7z4+4XqRw5AsESVkqE+qwAuRRlg2QG12EVEjPkrRIbeg==", "signatures": [{"sig": "MEUCIQCxPFezyixXwc3OpauWKbBeOnZEWCf5AT+6rPciSSbKDQIgdb7uU159X1Y4Uo3H0ujZ++Negil3Qh2GYAYdZB0h6FA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2678258}}, "3.8.0": {"name": "@hookform/resolvers", "version": "3.8.0", "devDependencies": {"ajv": "^8.16.0", "joi": "^17.13.3", "yup": "^1.4.0", "zod": "^3.23.8", "vest": "^5.3.0", "vite": "^5.3.3", "fp-ts": "^2.16.7", "io-ts": "^2.2.21", "jsdom": "^24.1.0", "react": "^18.3.1", "effect": "^3.4.6", "vitest": "^1.6.0", "arktype": "2.0.0-dev.26", "valibot": "0.35.0", "lefthook": "^1.6.18", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.3.1", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.5.3", "@types/node": "^20.14.9", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.4", "@types/react": "^18.3.3", "@vinejs/vine": "^2.1.0", "@effect/schema": "^0.68.15", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.13.3", "class-validator": "^0.14.1", "react-hook-form": "^7.52.1", "@typeschema/core": "^0.13.2", "@typeschema/main": "^0.13.10", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.32.34", "class-transformer": "^0.5.1", "vite-tsconfig-paths": "^4.3.2", "@testing-library/dom": "^10.3.0", "@vitejs/plugin-react": "^4.3.1", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/user-event": "^14.5.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "6c0a43d9e0ab24748337cb293f5be30b74da5207", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.8.0.tgz", "fileCount": 378, "integrity": "sha512-8BY7nM+QhMlGgYOb5Mxa6ZuHRYRvAZJ5LQ7+SxMv1CFGrqvNZajoa1ipEvdnIPllEcQ9R90oBNiRUUi8rj6KPQ==", "signatures": [{"sig": "MEUCIQDWQibTlUBAyLNSPsuyl5mQOc9ybTbZcguCG96fJF6AIgIgfGWbXJMwmzZC1yH7z0YU4u0R+QMkwsTRBB7vPkrMJUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 668422}}, "3.9.0": {"name": "@hookform/resolvers", "version": "3.9.0", "devDependencies": {"ajv": "^8.16.0", "joi": "^17.13.3", "yup": "^1.4.0", "zod": "^3.23.8", "vest": "^5.3.0", "vite": "^5.3.3", "fp-ts": "^2.16.7", "io-ts": "^2.2.21", "jsdom": "^24.1.0", "react": "^18.3.1", "effect": "^3.4.7", "vitest": "^1.6.0", "arktype": "2.0.0-dev.26", "valibot": "0.35.0", "lefthook": "^1.6.18", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.3.1", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.5.3", "@types/node": "^20.14.9", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.4", "@types/react": "^18.3.3", "@vinejs/vine": "^2.1.0", "@effect/schema": "^0.68.17", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.13.3", "class-validator": "^0.14.1", "react-hook-form": "^7.52.1", "@typeschema/core": "^0.13.2", "@typeschema/main": "^0.13.10", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.32.34", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^4.3.2", "@testing-library/dom": "^10.3.1", "@vitejs/plugin-react": "^4.3.1", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/user-event": "^14.5.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "cf540ac21c6c0cd24a40cf53d8e6d64391fb753d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.9.0.tgz", "fileCount": 397, "integrity": "sha512-bU0Gr4EepJ/EQsH/IwEzYLsT/PEj5C0ynLQ4m+GSHS+xKH4TfSelhluTgOaoc4kA5s7eCsQbM4wvZLzELmWzUg==", "signatures": [{"sig": "MEYCIQD/SAPJjeBNWpww9a/t6Bj1Pm1zlGc0sCE9uEDxNPou2wIhANdbeuekmaRFPn9Rk9v3Sy+/J9J0XdL46JP2dz4NHI7v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 712195}}, "3.9.1": {"name": "@hookform/resolvers", "version": "3.9.1", "devDependencies": {"ajv": "^8.16.0", "joi": "^17.13.3", "yup": "^1.4.0", "zod": "^3.23.8", "vest": "^5.3.0", "vite": "^5.3.3", "fp-ts": "^2.16.7", "io-ts": "^2.2.21", "jsdom": "^24.1.0", "react": "^18.3.1", "effect": "^3.4.7", "vitest": "^1.6.0", "arktype": "2.0.0-dev.26", "valibot": "^1.0.0-beta.0", "lefthook": "^1.6.18", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.3.1", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.5.3", "@types/node": "^20.14.9", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.4", "@types/react": "^18.3.3", "@vinejs/vine": "^2.1.0", "@effect/schema": "^0.68.17", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.13.3", "class-validator": "^0.14.1", "react-hook-form": "^7.52.1", "@typeschema/core": "^0.13.2", "@typeschema/main": "^0.13.10", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.32.34", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^4.3.2", "@testing-library/dom": "^10.3.1", "@vitejs/plugin-react": "^4.3.1", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/user-event": "^14.5.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "a23883c40bfd449cb6c6ab5a0fa0729184c950ff", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.9.1.tgz", "fileCount": 397, "integrity": "sha512-ud2HqmGBM0P0IABqoskKWI6PEf6ZDDBZkFqe2Vnl+mTHCEHzr3ISjjZyCwTjC/qpL25JC9aIDkloQejvMeq0ug==", "signatures": [{"sig": "MEUCIQD+QY5mN3SW3LSlSqeZKe9ZEKsBUrSRUtwkEvkJ4zvwDgIgS3GvZzBOyoCHYQGV/aFzOdEGN4PTiEUEu4+cBFRxxHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 712225}}, "3.10.0": {"name": "@hookform/resolvers", "version": "3.10.0", "devDependencies": {"ajv": "^8.16.0", "joi": "^17.13.3", "yup": "^1.4.0", "zod": "^3.23.8", "vest": "^5.3.0", "vite": "^5.3.3", "fp-ts": "^2.16.7", "io-ts": "^2.2.21", "jsdom": "^24.1.0", "react": "^18.3.1", "effect": "^3.10.3", "vitest": "^1.6.0", "arktype": "2.0.0-dev.26", "valibot": "^1.0.0-beta.0", "lefthook": "^1.6.18", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^18.3.1", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.5.3", "@types/node": "^20.14.9", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^1.0.4", "@types/react": "^18.3.3", "@vinejs/vine": "^2.1.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.13.3", "class-validator": "^0.14.1", "react-hook-form": "^7.52.1", "@typeschema/core": "^0.13.2", "@typeschema/main": "^0.13.10", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.32.34", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^4.3.2", "@testing-library/dom": "^10.3.1", "@vitejs/plugin-react": "^4.3.1", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/user-event": "^14.5.2"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "7bfd18113daca4e57e27e1205b7d5a2d371aa59a", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-3.10.0.tgz", "fileCount": 397, "integrity": "sha512-79Dv+3mDF7i+2ajj7SkypSKHhl1cbln1OGavqrsF7p6mbUv11xpqpacPsGDCTRvCSjEEIez2ef1NveSVL3b0Ag==", "signatures": [{"sig": "MEUCIQDs3MtP06OzUl+Um++ocF+2WRrWrchu8OSndBepj04cgwIgZccPaL8loLJoMZLA47H+MMtdb42AVGOQosSwZXv/sJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711293}}, "4.0.0": {"name": "@hookform/resolvers", "version": "4.0.0", "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.1", "vest": "^5.4.6", "vite": "^6.0.11", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.12.7", "vitest": "^3.0.4", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.10.10", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.7.3", "@types/node": "^22.12.0", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.8", "@vinejs/vine": "^3.0.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.54.2", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.15", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "730cb3a32bc236059264ec9fb7563f4eb3f4fb9d", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-4.0.0.tgz", "fileCount": 421, "integrity": "sha512-93ZueVlTaeMF0pRbrLbcnzrxeb2mGA/xyO3RgfrsKs5OCtcfjoWcdjBJm+N7096Jfg/JYlGPjuyQCgqVEodSTg==", "signatures": [{"sig": "MEUCIQC5lr3TrLGubWQTlTHMo+uT42+jxSKr70FRJSIkSUSb1gIgXNBa8jdAKani1ZcwP7vnAMBOuVLY1VfixX4DEZ9bbBM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 767679}}, "4.1.0": {"name": "@hookform/resolvers", "version": "4.1.0", "dependencies": {"caniuse-lite": "^1.0.30001698"}, "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.1", "vest": "^5.4.6", "vite": "^6.0.11", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.12.7", "vitest": "^3.0.4", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.10.10", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.7.3", "@types/node": "^22.12.0", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.8", "@vinejs/vine": "^3.0.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.54.2", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.15", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "17ba2b68d2883be6a06af1c7507be8e7c2f684b9", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-4.1.0.tgz", "fileCount": 397, "integrity": "sha512-fX/uHKb+OOCpACLc6enuTQsf0ZpRrKbeBBPETg5PCPLCIYV6osP2Bw6ezuclM61lH+wBF9eXcuC0+BFh9XOEnQ==", "signatures": [{"sig": "MEUCICR0YknSx7yuzBAlmPCQ3VBlPoE5BBWy6GR+nX0seJVqAiEAjnX2wvIWJBp4C5/QJaMLSjSTiriVvlHYgDxSKNUqT7Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 865443}}, "4.1.1": {"name": "@hookform/resolvers", "version": "4.1.1", "dependencies": {"caniuse-lite": "^1.0.30001698"}, "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.1", "vest": "^5.4.6", "vite": "^6.0.11", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.12.7", "vitest": "^3.0.4", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.10.10", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.7.3", "@types/node": "^22.12.0", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.8", "@vinejs/vine": "^3.0.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.54.2", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.15", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@standard-schema/utils": "^0.3.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "5d4b73174adba2ab1d8dab2dcb5a271d1a21022c", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-4.1.1.tgz", "fileCount": 397, "integrity": "sha512-S9YN1RgNWG+klUz5uQaV6rjE4pr6Py2tamj7ekshzLcMyg+/Pal1KZAYgGszV0+doiy41dUiQgXL3uRS9stndQ==", "signatures": [{"sig": "MEUCIQChaQCNieE8Zv6QsuDn2MOuNvNdCwXvJVZaoCRdFjI18wIgckWWb2lOK1j2rVKWZPEt3s58FEUNTnb7LneOvq3PXIc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 866432}}, "4.1.2": {"name": "@hookform/resolvers", "version": "4.1.2", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.1", "vest": "^5.4.6", "vite": "^6.0.11", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.12.7", "vitest": "^3.0.4", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.10.10", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.7.3", "@types/node": "^22.12.0", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.8", "@vinejs/vine": "^3.0.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.54.2", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.15", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "f2fe5ddc2341430a8559b53a8d5557b87e8636d4", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-4.1.2.tgz", "fileCount": 397, "integrity": "sha512-wl6H9c9wLOZMJAqGLEVKzbCkxJuV+BYuLFZFCQtCwMe0b3qQk4kUBd/ZAj13SwcSqcx86rCgSCyngQfmA6DOWg==", "signatures": [{"sig": "MEUCIQDL8Z0UtDa19BEoOWKy/wTAQlMUwVyXEkdo2xnZvh0JVwIgP42VrwOCNZf6wjiXX2RDVnUaJSJR/ZX6WqXJhgic7uM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 866395}}, "4.1.3": {"name": "@hookform/resolvers", "version": "4.1.3", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.1", "vest": "^5.4.6", "vite": "^6.0.11", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.12.7", "vitest": "^3.0.4", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.10.10", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.7.3", "@types/node": "^22.12.0", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.8", "@vinejs/vine": "^3.0.0", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.54.2", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.15", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "^7.0.0"}, "dist": {"shasum": "97a20cc0e88d3b66c50e1e9fd6e089a94c689e07", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-4.1.3.tgz", "fileCount": 397, "integrity": "sha512-Jsv6UOWYTrEFJ/01ZrnwVXs7KDvP8XIo115i++5PWvNkNvkrsTfGiLS6w+eJ57CYtUtDQalUWovCZDHFJ8u1VQ==", "signatures": [{"sig": "MEUCIFVwZUAWA7BZ/O5Be13rszXnIBv96wCoHyt+j+730BEbAiEApoz5Few7aoBDJttP94hps6tMYYmU3nvFB6gIOlue1Yc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 868740}}, "5.0.0": {"name": "@hookform/resolvers", "version": "5.0.0", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "devDependencies": {"ajv": "^8.17.1", "joi": "^17.13.3", "yup": "^1.6.1", "zod": "^3.24.2", "vest": "^5.4.6", "vite": "^6.2.2", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "jsdom": "^26.0.0", "react": "^19.0.0", "effect": "^3.13.12", "vitest": "^3.0.9", "arktype": "2.0.4", "valibot": "1.0.0-beta.12", "lefthook": "^1.11.3", "typanion": "^3.14.0", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "ajv-errors": "^3.0.0", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "typescript": "^5.8.2", "@types/node": "^22.13.10", "io-ts-types": "^0.5.19", "microbundle": "^0.15.1", "npm-run-all": "^4.1.5", "superstruct": "^2.0.2", "@types/react": "^19.0.11", "@vinejs/vine": "^3.0.1", "computed-types": "^1.11.2", "nope-validator": "^1.0.4", "@typeschema/zod": "^0.14.0", "class-validator": "^0.14.1", "react-hook-form": "^7.55.0", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "check-export-map": "^1.3.1", "reflect-metadata": "^0.2.2", "@sinclair/typebox": "^0.34.30", "class-transformer": "^0.5.1", "fluentvalidation-ts": "^3.2.0", "vite-tsconfig-paths": "^5.1.4", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@standard-schema/spec": "^1.0.0", "@testing-library/react": "^16.2.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1"}, "peerDependencies": {"react-hook-form": "7.55.0"}, "dist": {"shasum": "7ed8e5f3d01115a7c0ac330b855d2231f180f0c6", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.0.0.tgz", "fileCount": 395, "integrity": "sha512-3w/nL/GcafcBnehyvOGzrhFACAQoLAEVzJjRJrBpedEiD+dPvu/K+UL51wVOB4toZq8p6UkTfkd4aoAW+LOpRQ==", "signatures": [{"sig": "MEQCIG+4ycOHW/L/ezyGESqMcl1lqrcVrh0Yjq28+KUr3/VSAiAJgNr47j9FuXbUj92phO0GEI7od4w4idREnlTIApQq0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 934402}}, "5.0.1": {"name": "@hookform/resolvers", "version": "5.0.1", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "devDependencies": {"@sinclair/typebox": "^0.34.30", "@standard-schema/spec": "^1.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.10", "@types/react": "^19.0.11", "@typeschema/core": "^0.14.0", "@typeschema/main": "^0.14.1", "@typeschema/zod": "^0.14.0", "@vinejs/vine": "^3.0.1", "@vitejs/plugin-react": "^4.3.4", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "arktype": "2.0.4", "check-export-map": "^1.3.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "computed-types": "^1.11.2", "cross-env": "^7.0.3", "effect": "^3.13.12", "fluentvalidation-ts": "^3.2.0", "fp-ts": "^2.16.9", "io-ts": "^2.2.22", "io-ts-types": "^0.5.19", "joi": "^17.13.3", "jsdom": "^26.0.0", "lefthook": "^1.11.3", "microbundle": "^0.15.1", "monocle-ts": "^2.3.13", "newtype-ts": "^0.3.5", "nope-validator": "^1.0.4", "npm-run-all": "^4.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "reflect-metadata": "^0.2.2", "superstruct": "^2.0.2", "typanion": "^3.14.0", "typescript": "^5.8.2", "valibot": "1.0.0-beta.12", "vest": "^5.4.6", "vite": "^6.2.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.0.9", "yup": "^1.6.1", "zod": "^3.24.2"}, "peerDependencies": {"react-hook-form": "^7.55.0"}, "dist": {"integrity": "sha512-u/+Jp83luQNx9AdyW2fIPGY6Y7NG68eN2ZW8FOJYL+M0i4s49+refdJdOp/A9n9HFQtQs3HIDHQvX3ZET2o7YA==", "shasum": "0a5e90310149e3ac5b017efcb5beb9bdbb711f38", "tarball": "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.0.1.tgz", "fileCount": 395, "unpackedSize": 934421, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD6AYjJcX3gJgyr+0zU7HyqbgFtazshdvyZFeUdbwLP7gIhAJ+EcY1WukfS/dyT5CBe1m+EXoVZrMUSjgYll6F8kfA6"}]}}}, "modified": "2025-04-02T05:50:33.526Z", "cachedAt": 1747660587389}