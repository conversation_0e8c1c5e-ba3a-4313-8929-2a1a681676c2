{"name": "postcss", "dist-tags": {"latest": "8.5.3"}, "versions": {"0.1.0": {"name": "postcss", "version": "0.1.0", "devDependencies": {"mocha": "1.14.0", "should": "2.0.2", "fs-extra": "0.8.1", "coffee-script": "1.6.3"}, "dist": {"shasum": "408f9c3bbff4bbcdcb79cc5bc1fad7a0b761f124", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.1.0.tgz", "integrity": "sha512-552BH9GjsA1pcZLWcjMnNVoohDjhuLJYQCChhT8t8BsZ71Cf4q/DYIyNXBUrnej4jrUKXioRjXh/4pRd7/+aBg==", "signatures": [{"sig": "MEUCIEOau4kOW0KUcKoWLMssPcTob6COPqYESBkgf3lgOZS+AiEA0ZClI4mH/JyLH0CQ0w6H0B1OGmVmdcFPapppyLIU6g8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "postcss", "version": "0.2.0", "dependencies": {"source-map": "*"}, "devDependencies": {"mocha": "1.15.1", "should": "2.1.1", "fs-extra": "0.8.1", "coffee-script": "1.6.3"}, "dist": {"shasum": "1615c6c5c5f9e19ca230022c4f93b5b1909e75c8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.2.0.tgz", "integrity": "sha512-9ksGbrT/bpISj00ycmw/sG8MUglHeROQCLeQHGwOrIzs6tQsxYmzACxumhOkAvei7mxZqkuucy3AUXS04xjOMA==", "signatures": [{"sig": "MEUCIQDbMnMxnpG3uYK9ogE8bm8o4xCchgBv4p3JzbqyAc8ncgIgPuFa5VgDHaXdM+sblzMnkBSLg9nv+0bpXBsEYyfo7oI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "postcss", "version": "0.3.0", "dependencies": {"base64-js": "0.0.6", "source-map": "~0.1.31"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.17.1", "rework": "0.20.2", "should": "3.1.2", "fs-extra": "0.8.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "e740bbd89ef153c4d4fa7665f532d90e42e6f149", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.0.tgz", "integrity": "sha512-c6bnjJH0Y3yIZPgmMLNWELrK58b6+Q9ejbQTB3eKUyXH9Jps3Hc/q3gNaJreYHhoWZTYShs2XreWC0Rc24mqVA==", "signatures": [{"sig": "MEQCIDEjcUyzphZpbYgZlBDL9kYaVcVzVSLt0Lq1BY8QgIRdAiA6Eq1gjkx890iHRS4IodyH4LyH8L7CgfnVWcRGRnYVVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.1": {"name": "postcss", "version": "0.3.1", "dependencies": {"base64-js": "0.0.6", "source-map": "~0.1.32"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.17.1", "rework": "0.20.2", "should": "3.1.2", "fs-extra": "0.8.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "f4eeed25fac31b00a9d95402d13389091f466e0a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.1.tgz", "integrity": "sha512-yxjFhQ0Jr4zPDjODgTsPLGDZBMzDK9VyZKX/qLH0wLp0gjw1NwrC28PbZi8jJ4xye9LVguNJ6lWJYpkSu1nbSA==", "signatures": [{"sig": "MEYCIQCk4rcIqF5Yk0kBGTohAuyGG6Y5BdnnvW8E8DSS1S0PTwIhAOs7MYACb/cg8WpoToW+zISw/mddx/TKq5AKhbOVPjgP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.2": {"name": "postcss", "version": "0.3.2", "dependencies": {"base64-js": "0.0.6", "source-map": "~0.1.32"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.17.1", "rework": "0.20.2", "should": "3.1.2", "fs-extra": "0.8.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "94ba51dccbe5bf2d85a9ed416512a96f1b75a43f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.2.tgz", "integrity": "sha512-KZLiVrBun3HekruTYzjc/PCh8fnXeilloU9J9dvUd+2K/6uA3jFplWHHpAiSSRoxO4bRwWIbSp8gYzuOTiqK1g==", "signatures": [{"sig": "MEUCIC9JHB9u/6kplIPHSc9npbQ0DGaYwr9OT4+JVJwU1TBBAiEAt02ycKGPPvzMiAQ+o3bAJzTjofOl7+OymEClm8pStNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.3": {"name": "postcss", "version": "0.3.3", "dependencies": {"base64-js": "0.0.6", "source-map": "~0.1.33"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.17.1", "rework": "0.20.2", "should": "3.1.3", "fs-extra": "0.8.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "9377e5c46949dee78aac75a6a04e57fd10e06e9d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.3.tgz", "integrity": "sha512-NyDsLE8QBEQ/UMg0dlwU/2PzYC71LDgjbuCgQ6b357MIDdb6JPXTTbBRUQTm0CNzrhoGCJOTTDlf6cZS4EorIg==", "signatures": [{"sig": "MEYCIQCXJYFVQRXtKcXHfEE1ZjF5/0USJleT+wr99rSTtFteTAIhALUCtiGBU+47j+Vzj1QuEyGIysFKAZMCAOuY9mxwuTB2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.4": {"name": "postcss", "version": "0.3.4", "dependencies": {"base64-js": "~0.0.6", "source-map": "~0.1.33"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.17.1", "rework": "0.20.2", "should": "3.1.3", "fs-extra": "0.8.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "783190bbe815aa791ed67e5d2bdcd4a7f43f3071", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.4.tgz", "integrity": "sha512-+nKKyBQBvyWn6eJdlPfoPSxK+wnr1PfKXFrW4PHSCC+iq225mM/XtpbwW0y5sJXQyrGZ6nDXoZSt4W1/nMZQkQ==", "signatures": [{"sig": "MEUCIQCRylbGQReaCBxf4Kkuu0Vv833X99ikTqp5xM8XATF8MgIgAReXR2z6ez5CzV7nRWUHQY6zqkYWHAXlLEoxEzgbGsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.5": {"name": "postcss", "version": "0.3.5", "dependencies": {"base64-js": "~0.0.6", "source-map": "~0.1.33"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.20.0", "rework": "0.20.3", "should": "4.0.0", "fs-extra": "0.9.1", "gonzales": "1.0.7", "coffee-script": "1.7.1"}, "dist": {"shasum": "5073a3d062ef3ce592ac4a5fe6b8c2862ab83ceb", "tarball": "https://registry.npmjs.org/postcss/-/postcss-0.3.5.tgz", "integrity": "sha512-TyJE5vZ9RqE8t4OLPET+0IVCucd5yuwKRQZj0oddvIW+7/IjfkUizQkn/zlUgC5xelbzMmspDkrslmwFT/QGMQ==", "signatures": [{"sig": "MEUCIQDTqW34LAi1QKoPSDJ3vdU6Roblry3LUApQ0RMiAlqsjAIgFVHb7Fr8daIWZV0AiDf1YCE8+uRiO6NkpntdAmY68ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "postcss", "version": "1.0.0", "dependencies": {"base64-js": "~0.0.7", "source-map": "~0.1.34"}, "devDependencies": {"cssom": "0.3.0", "mocha": "1.20.1", "rework": "1.0.0", "should": "4.0.4", "fs-extra": "0.9.1", "gonzales": "1.0.7"}, "dist": {"shasum": "fae1baba117e3811f4a7f0a97658fd9db57db1da", "tarball": "https://registry.npmjs.org/postcss/-/postcss-1.0.0.tgz", "integrity": "sha512-vyU4yBp7kGWcZ19G3oHciINhnwzC9tli9YQV1lGND8QUI7YbekAbTh0zuwGpjStu1yA3CPleFAe+yWUrckjF1A==", "signatures": [{"sig": "MEYCIQD2gN0xkHK354+BDutAgwJvtK+3KPYz46c9tDg+apc1hQIhAOnv1qIJV5IMpZeovdSMkwZjO/ztc/W2FC9R/1LlxuDY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "postcss", "version": "2.0.0", "dependencies": {"traceur": "~0.0.51", "base64-js": "~0.0.7", "source-map": "~0.1.37"}, "devDependencies": {"gulp": "3.8.6", "cssom": "0.3.0", "mocha": "1.20.1", "rework": "1.0.0", "should": "4.0.4", "request": "2.37.0", "fs-extra": "0.10.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "0.5.1", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.7.1", "gulp-traceur": "0.8.0", "jshint-stylish": "0.4.0"}, "dist": {"shasum": "eb11193ecdf6acdf3183918158b0073ff8fe65ec", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.0.0.tgz", "integrity": "sha512-nIJuRfA8QBrLIsPUnOcD8a0uTQ0yQyonIgKUrVPSHoJKBMcwrVXN1hso1BWsdBeQJzEajkr4qrUr92tW/T3Cng==", "signatures": [{"sig": "MEUCIQDNdlz0gbf9Iip5vxfeYCTtIi919Ft0FA+pBTzBOmSFlgIgRbhw3lpPXO6/tSwOrsCvw56Gr9kWvf3R+IzPQbMB7nQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "postcss", "version": "2.1.0", "dependencies": {"base64-js": "~0.0.7", "source-map": "~0.1.37"}, "devDependencies": {"gulp": "3.8.6", "cssom": "0.3.0", "mocha": "1.21.1", "rework": "1.0.0", "should": "4.0.4", "request": "2.39.0", "fs-extra": "0.10.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "0.5.2", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.0", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "0.2.0"}, "dist": {"shasum": "2a33d0e7d3626fd9e2e3f2d160595f6ab8486d36", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.1.0.tgz", "integrity": "sha512-JCngE5DGtPb+G8Aa4NAyH8N+D7zvecWfqZ6LaGK3HuuZlx3+gZvLWPkiwjV/MmFpAmJwn0gFSZohhVDNsii3zg==", "signatures": [{"sig": "MEUCIQC+9pnU0/3ZBrLkbEuZG2Vo5RFuD0ie9YqxLRcf5gOzywIgec3ndUezxF5FbqJGA4NFuX9g8mwBq7IH8K9ZGKcgiIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "postcss", "version": "2.1.1", "dependencies": {"base64-js": "~0.0.7", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.10.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "0.5.2", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.3", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "0.2.0"}, "dist": {"shasum": "aada62b82ac23d61490533b9c036bd6b9e65de07", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.1.1.tgz", "integrity": "sha512-9YE<PERSON>b8jQOCbHdMupCaF5MKQ9FdDwNizqI8Y1CE+xlrI9GPFffjvUEv7KnvdHi5k3Z4m0IP0QP2Bh1hJPoYe+6Q==", "signatures": [{"sig": "MEUCIQDsiGYjvUR1ulPeYpLoKqEqKhC/QXEp+pzGE0zxv2nuewIgJC+CYmF3v/n5kmx4VhrcPIIijLVJZ+yUAt5L6ef5jz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.2": {"name": "postcss", "version": "2.1.2", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.10.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "0.5.2", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "0.2.0"}, "dist": {"shasum": "0ef11faf545185ab88042da4cc120afdd565233a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.1.2.tgz", "integrity": "sha512-PZAScb8MITyMvFYIbWwzRgF9oUfXW3E1+piXdsZZiym/3DwNbagxONMsVADmHFsTmVARGImYULw4g2PjvQQFIg==", "signatures": [{"sig": "MEUCIGJ/zVc8+ywRtJvIdLlHilqpahc8rdv21lEwccWfLpMVAiEA1pVKt3w3bQsrifu1v7hW38KJcAgHOTiAmI68HqYAoN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "postcss", "version": "2.2.0", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.11.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "1.0.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "281ea9ab77c3c5d80fcdbbaa4c94a2065bca2e11", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.0.tgz", "integrity": "sha512-+HEWiGxLdiFVMfY9fF0QtINRPR2BH/4ywKBctJ7q7DJLZyA+JswqAJP9mk7HR6ztGGyxffFRG8ELxNQeMJOQFw==", "signatures": [{"sig": "MEUCIQDTtTDlncDPXtEELbq/SRcwXiRTvkT3Z51Uyssyn1P0oQIgQWlYadN8I0TLwaj29H29Im8zmIBKHYOjnRjUEGWTEzE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.1": {"name": "postcss", "version": "2.2.1", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.11.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "1.0.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "9141516f74f3b7a7ece22de18f6937e03a38e894", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.1.tgz", "integrity": "sha512-yemIwpd9Hm4e0OD7iljhrOJTcyU0memh9iBzNzV6T2VWXNE9kTmYcFHPtbiJ1D8z64FpfTV/9s6Pu0BziXrv9g==", "signatures": [{"sig": "MEUCIQC1M11/3frh3pCBioqCsKeUwYXq1ePDA8Y9FHwfyDR7xQIgNx7nVUxhtGQjLMnma2gyv5LlGSfXLax+0G6Kx9zXv0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.2": {"name": "postcss", "version": "2.2.2", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.11.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "1.0.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "63655197032aa120e0a5e9a8bc4a4ee131a925e0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.2.tgz", "integrity": "sha512-hnPAWUg3+T27BBqjssYQ6rd0w0lfBWQseFOlsCHhdgu4nuwgqO2FCD4XzUwgl0lU/+1SxsA7JFo9m1q26C40Pw==", "signatures": [{"sig": "MEQCIAc5FrV7OwoA1/g+LyXPrXxE6BOWNG/QzJKdOekbaMPXAiB7/s56d4LY84admwtQnWoMFNhS2RPmJXpmakYkX+4hiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.3": {"name": "postcss", "version": "2.2.3", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.0", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.11.0", "gonzales": "1.0.7", "gulp-util": "3.0.0", "gulp-mocha": "1.0.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "a60f28b302d40555298ada49c76de4833eea0e30", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.3.tgz", "integrity": "sha512-A8gTPXoWe3oVvDgElmetU7G1ZY+R+WxVzI/BpMLqzwgBDomhJaYNI42Zxbve6yuxj0H2mr1qEc5wakxUbCGeZg==", "signatures": [{"sig": "MEQCIDs3C7sBGBnFPa+lw7/YGLvd7JD/RT75bzuzDlGDWQRqAiA/rmtRay0fcXNXRz1XtP6UwuRif9WeyrtaBwQx9cvKOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.4": {"name": "postcss", "version": "2.2.4", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.38"}, "devDependencies": {"gulp": "3.8.7", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.1", "should": "4.0.4", "request": "2.40.0", "fs-extra": "0.11.0", "gonzales": "1.0.7", "gulp-util": "3.0.1", "gulp-mocha": "1.0.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "~0.7.15", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "0ee6d1adaae3cab15418fc670968ec1a1e2e58b6", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.4.tgz", "integrity": "sha512-prbBmh40AsfJN5YkwXhwttUewcgm/yfb8fri4gKqmQJ+GIwFYYRpSzbAuJZ1W/EHi7gmJxGK9q6tp6k1xAIgDw==", "signatures": [{"sig": "MEYCIQCokXzwyFfrLPpekW4q2cEvtudfJot5ZxBV4k1YdsLgOAIhAMKWiZmzQk7FwjaQS9tcSTdbImUy5zf75jI5jn9RDI7I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.5": {"name": "postcss", "version": "2.2.5", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.39"}, "devDependencies": {"gulp": "3.8.8", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.1", "should": "4.0.4", "request": "2.44.0", "fs-extra": "0.12.0", "gonzales": "1.0.7", "gulp-util": "3.0.1", "gulp-mocha": "1.1.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "0.7.16", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "9aaae7ee1601002233673cfb4af58280a6b7abd9", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.5.tgz", "integrity": "sha512-lFych3cMKho2lvxPTFPYvV4Qr30xJqQEiKTn7Qw83xjewqA4xFPcrWd26V/H/awjEHEL0XJ+JiX2/2L8dTDgYA==", "signatures": [{"sig": "MEUCIE+13eagEvF31H+cdldQ5JGScZuZn+xVBF5Aup9ba9tkAiEA8uSpgE2znH6ZRhJUzTtiBwh0mJwAre4Z3wJ3E9GX4jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.6": {"name": "postcss", "version": "2.2.6", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"gulp": "3.8.8", "cssom": "0.3.0", "mocha": "1.21.4", "rework": "1.0.1", "should": "4.0.4", "request": "2.44.0", "fs-extra": "0.12.0", "gonzales": "1.0.7", "gulp-util": "3.0.1", "gulp-mocha": "1.1.0", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.8.4", "es6-transpiler": "0.7.16", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "dist": {"shasum": "c04344e2449e4586b955fbe4a74f77080d84571f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-2.2.6.tgz", "integrity": "sha512-8zk4tZ73/ddVQ+Cg2Ca/U4CGT1SAzDYriJDYrRp9ZvNXJPWwYlJaSjHSUJLFKlmuaUjCJvtDnKTwwDpoie55Yw==", "signatures": [{"sig": "MEYCIQDnQJyWDshGZaPEYIqPL5xXbiDHR242EPh1HteJAzbgKAIhALkeUwBRiImCmAKS4EbTVoXzSWIBeljYuxaTZHfRBi2U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "postcss", "version": "3.0.0", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.12.0", "gulp": "3.8.10", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "request": "2.47.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.0.2", "through2": "0.6.3", "gulp-6to5": "1.0.1", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "1.1.1", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.1.1", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "c5ff2b632af123fe8842844aaa0b75155c3e1694", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.0.tgz", "integrity": "sha512-wmyQyRpMN0WAVNhf9HO5CBVkxmBKHPuce0uIVaW4oDrluCGZufmisuL3ETdB+sD3In/aO0G2tNVvB5d3q1Eknw==", "signatures": [{"sig": "MEUCIDNQ0d2cCxzP+QuKS1vD/WX9meCe3YcMtgtkQwOfTKsFAiEAnSU2+yVhfvKS+AUq+rHqhyjvVq8oLy/eQHwc9fgZK6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.1": {"name": "postcss", "version": "3.0.1", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.12.9", "gulp": "3.8.10", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "request": "2.48.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.0.2", "through2": "0.6.3", "gulp-6to5": "1.0.1", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "1.1.1", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "02b8f5a17e5138c614dd9ba63a8e15437ee0502b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.1.tgz", "integrity": "sha512-n59mooAqKfLoKUA4FzpI5sjvN2VDRlZ8tYHykgC+2QCiWbbo7lDhm9RZyRAO/tdg87OUmI7U+08LMLjBMuwt2g==", "signatures": [{"sig": "MEUCICx8JcVW9QydDeqUfvIiJKXcdpvJtHJWWFIljai2ukuuAiEA1XKDs9XuwJwydO8SK7Em0qRiG3uXTxhZhMk1CvbSUVY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.2": {"name": "postcss", "version": "3.0.2", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.12.12", "gulp": "3.8.10", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "request": "2.48.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.0.2", "through2": "0.6.3", "gulp-6to5": "1.0.1", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "1.1.1", "gonzales-pe": "3.0.0-10", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "8a12cf2bf3c9cac3e11cad321d91e9a94e22d8ae", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.2.tgz", "integrity": "sha512-rfo/nQJfpI/3ocRzuiGK/0zpZnlgv5CYV8J8Jb76nYgUENMPVlZwCtOo3G0BNErP5RKB6lt+eLzOw+O/5EjLAg==", "signatures": [{"sig": "MEQCIDMNBLwQI80DGCeN8Shv06dIK/WLWaM+foKzhgJlNvgnAiALTQ/t0ie5hY+KC3bk/HAXF6cwM9diRV+OkausyGBhaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.3": {"name": "postcss", "version": "3.0.3", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.12.26", "gulp": "3.8.10", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "request": "2.48.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.1.0", "through2": "0.6.3", "gulp-6to5": "1.0.2", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-11", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "b4a2fb66fd264b9da8091e4d8f47a22aa1d81590", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.3.tgz", "integrity": "sha512-fp9YfhOc1iSjoYXHg7k/JkVnQuzX+nmQEMO31CEjZ6pGXLcnibrA3X6PpBXy0Rpp2vTwpqmaiQzGx+TSmSv21g==", "signatures": [{"sig": "MEUCIQD8yYJW+2D9DCAgG864BlPALNIrxnpOSabbYbL+6tAnRQIgEvgUg5M1bC36Cy8OLXPOXIDID6jLzxlyxRpg8ELJK4E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.4": {"name": "postcss", "version": "3.0.4", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.12.26", "gulp": "3.8.10", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "request": "2.48.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.1.0", "through2": "0.6.3", "gulp-6to5": "1.0.2", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-11", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "7e4ec88cb102be153e0ce551390a3f010bfdf35f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.4.tgz", "integrity": "sha512-U7+q21JwI1lR3hou64QbIkFPKCC1840kwnixeBMWy8x6PKXPYWM2auT/3/p6WOxJdHan/7U3U4m71k1U6teOtA==", "signatures": [{"sig": "MEYCIQCSXrHqVZjBZfKE2yTfsWhFzbE5HovyVI1v185baWQI/AIhALfi3qdAlVDUl5HwLXzVXdEoqaHvhTGWY6spdr1h4kP7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.5": {"name": "postcss", "version": "3.0.5", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.14.4", "gulp": "3.8.10", "less": "2.1.1", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "stylus": "0.49.3", "cssnext": "0.6.0", "request": "2.48.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.1.0", "through2": "0.6.3", "gulp-6to5": "1.0.2", "gulp-util": "3.0.1", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-12", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "a87607753c22f62a48ed8a1286e87506853f789c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.5.tgz", "integrity": "sha512-QUJdH0o6rK6BB2kWyFLnZsqZFUArGwMuwstUoljth72yXPN9W2IwW8drM4NESM+4lTKmE1lCyhBnIoUmY6oukA==", "signatures": [{"sig": "MEUCIA988YutWLKPL8Xf/XSG25YZLPLmfFUsA2W4ggwNleK4AiEA6Gq8fXWYir0JwnbOvKgrDVz950RaSkcrlwHtMfhB2HM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.6": {"name": "postcss", "version": "3.0.6", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.14.7", "gulp": "3.8.10", "less": "2.1.1", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.0", "stylus": "0.49.3", "cssnext": "0.6.2", "request": "2.49.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.1.1", "through2": "0.6.3", "gulp-6to5": "1.0.2", "gulp-util": "3.0.1", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-12", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "550d9c483f4ecc14a0e68f0ca2597ac8be278d77", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.6.tgz", "integrity": "sha512-4Z2QX/eO3CX67Bf/GOXzYVv79DUtlfj7WAxwX2ZNh1dnIh1MUBlmmxsnIMsiEX8wYMVcVY6HsGbubGfxn1yvAg==", "signatures": [{"sig": "MEYCIQCEjAqFpsqbolBdBktj8kEVhBZgNTYng7OgqpBpzgPo2wIhAN5zET3+AwG5yvSi4H3IGQvK8wX8UsJFEatN5Gc1Eku+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.7": {"name": "postcss", "version": "3.0.7", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.40"}, "devDependencies": {"6to5": "1.14.17", "gulp": "3.8.10", "less": "2.1.1", "cssom": "0.3.0", "mocha": "2.0.1", "mensch": "0.3.1", "rework": "1.0.1", "should": "4.3.1", "stylus": "0.49.3", "cssnext": "0.6.2", "request": "2.49.0", "execSync": "1.0.2", "fs-extra": "0.12.0", "gonzales": "1.0.7", "stylecow": "3.1.1", "through2": "0.6.3", "gulp-6to5": "1.0.2", "gulp-util": "3.0.1", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-12", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "0.1.6"}, "dist": {"shasum": "cba8b0721f02aca042cc2fee84741898ad200772", "tarball": "https://registry.npmjs.org/postcss/-/postcss-3.0.7.tgz", "integrity": "sha512-PB15uXXaq0CFNHXb6MvPDqkkdVRYrPkNz+VP9cpiqXxmmLC7neMT72QLRlbYo0Qo/E1qV6aauFJeMjFU2tAhmg==", "signatures": [{"sig": "MEQCIGok6X15ikWw4jmwzO9zJeMSRd3wZGUpRwDGElIkrhdkAiBuIZTxUwcICKWsblOKk71rG/1jIBPDwX/OrshK/HamYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "postcss", "version": "4.0.0", "dependencies": {"js-base64": "~2.1.5", "source-map": "~0.1.41"}, "devDependencies": {"6to5": "2.2.0", "chai": "1.10.0", "gulp": "3.8.10", "less": "2.1.2", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.49.3", "cssnext": "0.6.6", "request": "2.51.0", "execSync": "1.0.2", "fs-extra": "0.13.0", "gonzales": "1.0.7", "stylecow": "3.1.1", "through2": "0.6.3", "gulp-6to5": "2.0.0", "gulp-util": "3.0.1", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-12", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "15ce1cc081bb3f073133ef463ffa67ca3d096f9b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.0.tgz", "integrity": "sha512-Td6j8O4vE/PzTUWx2+svi5j2GK4UFK7KlINOKq1wSaMThj/wUR9BBNTd+WgRnoIoEMsc3G5KMg3jCVSRGGbPCA==", "signatures": [{"sig": "MEYCIQD2x/TCEbCyxp6uYPyyJLp5torIAco5HSEtSFyzVYYGvwIhAL1Qx1Lszde7zQ0M9dLU9yYz9KIEu07MB6FzLMiI7x/D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.1": {"name": "postcss", "version": "4.0.1", "dependencies": {"js-base64": "~2.1.6", "source-map": "~0.1.43"}, "devDependencies": {"6to5": "2.9.4", "chai": "1.10.0", "gulp": "3.8.10", "less": "2.2.0", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.49.3", "cssnext": "0.6.6", "request": "2.51.0", "execSync": "1.0.2", "fs-extra": "0.14.0", "gonzales": "1.0.7", "stylecow": "4.0.1", "through2": "0.6.3", "gulp-6to5": "2.0.0", "gulp-util": "3.0.2", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-12", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "5281f5798413ad4489681a0ef76f586aeae7bb20", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.1.tgz", "integrity": "sha512-F2s72mSHmS5O/kR7xn6FPjGtcfyKNo4ivUkBRuBF4obWJRqxeNKGoUB4pjOTmkDmWhECZ4TWyEN4eB9h7xvSFw==", "signatures": [{"sig": "MEUCIQCc7ByNWT+QeNI8QB5vlcBum3LmCZ1kSsBjrz9bzuPE3QIgDMI026d/ZILdX0o9C3sNM24jUFJos58qZG6kyWTbC6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.2": {"name": "postcss", "version": "4.0.2", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.1.43"}, "devDependencies": {"6to5": "2.13.7", "chai": "1.10.0", "gulp": "3.8.10", "less": "2.2.0", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.49.3", "cssnext": "0.6.6", "request": "2.51.0", "execSync": "1.0.2", "fs-extra": "0.15.0", "gonzales": "1.0.7", "stylecow": "4.0.1", "through2": "0.6.3", "gulp-6to5": "2.0.2", "gulp-util": "3.0.2", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-13", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "98485b1f6611f0342edd36792ade146769ece562", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.2.tgz", "integrity": "sha512-l7jToqVaUsJg6pGqguWztj3VWDrz89MA+zfNqCdwRJMBPZ6mxZr5W8nmgoWs2+5SgzxAJw/wVi/eL3gk1NnZTA==", "signatures": [{"sig": "MEYCIQC7sFSiu7eICfAvmEnw820z6aTOl/8pjQbiA2dNeYr4iQIhAN9GF4qSuHYqlLDmGDWPMBmfR9RNtSOmnnHu227PZkMD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.3": {"name": "postcss", "version": "4.0.3", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.2.0"}, "devDependencies": {"6to5": "3.0.9", "chai": "1.10.0", "gulp": "3.8.10", "less": "2.3.0", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.49.3", "cssnext": "0.6.6", "request": "2.51.0", "execSync": "1.0.2", "fs-extra": "0.16.0", "gonzales": "1.0.7", "stylecow": "4.0.1", "through2": "0.6.3", "gulp-6to5": "3.0.0", "gulp-util": "3.0.2", "node-sass": "1.2.3", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-13", "gulp-jshint": "1.9.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "254c542e91699d599f6ef8de2520dc069800add8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.3.tgz", "integrity": "sha512-KURaBhLHCnVSi0cP73sWsn9OiYCzhqjJ6TxWx1G76ConeFcnNu0GtJOxqVjCebtpDNjlWJIDmeC7BLNivH1qZA==", "signatures": [{"sig": "MEUCIQDJruE7neTRCAjWiVyPnQG3uO/v11k/lncHULyNdOUBggIgNi9TpyKfxtg/fXzVUmLSGmcvW3BBhhsgCT5jzvxEeZo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.4": {"name": "postcss", "version": "4.0.4", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.2.0"}, "devDependencies": {"6to5": "3.6.4", "chai": "2.0.0", "gulp": "3.8.11", "less": "2.4.0", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.0.0", "request": "2.53.0", "execSync": "1.0.2", "fs-extra": "0.16.3", "gonzales": "1.0.7", "stylecow": "4.2.4", "through2": "0.6.3", "gulp-6to5": "3.0.0", "gulp-util": "3.0.3", "node-sass": "2.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-26", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "a4d26ab33c894602418783f36c2bc8f3de4f3cc3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.4.tgz", "integrity": "sha512-B<PERSON>hgKJ4daNzdLtEwR1VSIe05RIMXwnN/zdtbOgxHJDfaBjXHWx5jGo4eUeLumal7MJZB5OQp3i8jtTdb9wexrw==", "signatures": [{"sig": "MEQCIFMmAakeFXiOIsXUsiL5iO8ykTnOrg1VCBx6n9d2N+MmAiAQd+2DHEoSlnjnEpAJVhT1LbzAYqM2TuBGjPBocjaguw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.5": {"name": "postcss", "version": "4.0.5", "dependencies": {"babel": "4.4.5", "js-base64": "~2.1.7", "source-map": "~0.2.0"}, "devDependencies": {"chai": "2.0.0", "gulp": "3.8.11", "less": "2.4.0", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.0.1", "request": "2.53.0", "execSync": "1.0.2", "fs-extra": "0.16.3", "gonzales": "1.0.7", "stylecow": "4.2.4", "through2": "0.6.3", "gulp-util": "3.0.3", "node-sass": "2.0.1", "browserify": "9.0.3", "gulp-babel": "4.0.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-26", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "70659b12bf6d79ec0aee89cd7c347e92d9346f14", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.5.tgz", "integrity": "sha512-ks5QGq+gX6y7jJjgBxFoBKksOLUOI7fjU7o291fczcULyxtBelrD4dUM1k6TVzK5HO73NUXBn5d4jbU1Yrn9zQ==", "signatures": [{"sig": "MEUCIH1KsgKua5fKr/A1Oeg7TeILqOlgwdnotW7VTYDRL4Y/AiEAj54C/gbCmjxantmpXiATqvpOCsVgpFrheMe7wx11k7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.6": {"name": "postcss", "version": "4.0.6", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.2.0"}, "devDependencies": {"chai": "2.1.0", "gulp": "3.8.11", "less": "2.4.0", "babel": "4.4.6", "cssom": "0.3.0", "mocha": "2.1.0", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.0.1", "request": "2.53.0", "execSync": "1.0.2", "fs-extra": "0.16.3", "gonzales": "1.0.7", "stylecow": "4.2.4", "through2": "0.6.3", "gulp-util": "3.0.3", "node-sass": "2.0.1", "browserify": "9.0.3", "gulp-babel": "4.0.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gonzales-pe": "3.0.0-26", "gulp-jshint": "1.9.2", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.0"}, "dist": {"shasum": "1bd1e8a99f73efdb46d11bf5c206079e2d306538", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.0.6.tgz", "integrity": "sha512-vi0DkoNKHmuMMFzHX6s31D3yms/jWQ1/kVQRcA39hRZVhSmscmpXDVW6dpGIxVy5wqDVkFUpWpOh/wpeCmHGCA==", "signatures": [{"sig": "MEUCIATr21PVwKiRpy46rWSxAn7dnPwg+lsH5mnxyerSygVGAiEArK/8RemgdpHDewTsJZuN8kp4gk6Bs94tsGSslgAREW0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.0": {"name": "postcss", "version": "4.1.0", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.4.2"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.4.0", "babel": "4.7.16", "cssom": "0.3.0", "mocha": "2.2.1", "sinon": "1.14.1", "eslint": "0.16.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.1.0", "request": "2.54.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.0", "through2": "0.6.3", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "1.1.0", "browserify": "9.0.4", "gulp-babel": "4.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.6.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "921450c1108813f7becc9247839c860b76804668", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.0.tgz", "integrity": "sha512-E9a25YjxKfSg1EJs4H/0gvyO7FI3CfFMfWWYhcV70vW6tiQudL3hbMxKPeyVlONd77iP3r6THm/Xu0TNx18log==", "signatures": [{"sig": "MEQCIENBsl/hkwms9z2K94eDUHVpwUaWLczt19HR8zulHmJWAiAeemIusQY1PBDdf4fV4qQeLCBRp86IZbKlKsHdAPzYeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.1": {"name": "postcss", "version": "4.1.1", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.4.2"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.4.0", "babel": "4.7.16", "cssom": "0.3.0", "mocha": "2.2.1", "sinon": "1.14.1", "eslint": "0.16.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.2.1", "request": "2.54.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.0", "through2": "0.6.3", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "1.1.0", "browserify": "9.0.4", "gulp-babel": "4.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.6.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "f4ad2354dd3c9105ed31cdf55f015303956f566d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.1.tgz", "integrity": "sha512-ECWX/wHEGB9VvmTwB4+Q9nsMV53iEmFLOD8Sg8F5xHpyqgZpwxiLc/jBIO+C0Elx4YiZZNlGmE2kWtYEMTsUgQ==", "signatures": [{"sig": "MEUCIQDEKHhw/zOMRmiR+4xla43ysmLfRYkiIoU8faRX7mxBmwIgeSTsn77ZekBXm7xWWhVCSMtXh6hxxmbzX8OmKy1k1wU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.2": {"name": "postcss", "version": "4.1.2", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.4.2"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.4.0", "babel": "4.7.16", "cssom": "0.3.0", "mocha": "2.2.1", "sinon": "1.14.1", "eslint": "0.16.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.2.1", "request": "2.54.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.0", "through2": "0.6.3", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "1.1.0", "browserify": "9.0.4", "gulp-babel": "4.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.6.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "efc66ce5ec01cb9a1e715702cebbe0cc55cf87ea", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.2.tgz", "integrity": "sha512-xrw7PO5xPOa5OOwA/ZHZznYovK+zASBzN16F7VT21JWHu1MwsL+M85f2/2oD43LHQls2TV53GcXtqPJd6LWiiQ==", "signatures": [{"sig": "MEUCIQDtFKuODhSQ+N2Kddg3bnqKhj8yH3tRcCcN9pP1C7HngAIgPINsGDnqkoPcQouUdI2v9V7nZHC8iP2NKpiGPOEJDDw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.3": {"name": "postcss", "version": "4.1.3", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.4.2"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.5.0", "babel": "5.0.8", "cssom": "0.3.0", "mocha": "2.2.1", "sinon": "1.14.1", "eslint": "0.16.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.2.1", "request": "2.55.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.0", "through2": "0.6.3", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "1.1.0", "browserify": "9.0.7", "gulp-babel": "5.0.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "es6-promise": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.6.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "c174f98564103748f3355cfe1a12144dd182c826", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.3.tgz", "integrity": "sha512-wZSw8mYQ3sTiiWuToJVSUYMPWk1hdTFnLjFvYdq70YmRUrczmRzYs1BXN1UptnmIkHMCsgJ1luelqxMZyGbneg==", "signatures": [{"sig": "MEYCIQDR3OMRrvpGamTY0xzgzXJZk9YuaUIXIyMrijMiPkM0LgIhAJqNTIQ0e3r1eweqhWxCRk2Sb16Qz/+C4IyzNJqJ2lpP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.4": {"name": "postcss", "version": "4.1.4", "dependencies": {"js-base64": "~2.1.7", "source-map": "~0.4.2", "es6-promise": "~2.0.1"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.5.0", "cssom": "0.3.0", "mocha": "2.2.1", "sinon": "1.14.1", "eslint": "0.16.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.2.1", "request": "2.55.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.0", "through2": "0.6.3", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "1.1.0", "babel-core": "5.0.8", "browserify": "9.0.7", "gulp-babel": "5.0.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.6.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "24b3bf57071b838c126636a348453e101cb8a74b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.4.tgz", "integrity": "sha512-P5SZ1/IxI/92b7uKA+e+Ru9srzScgO2tKpPqxVe262dBvmlLzqdlA/bk42OrxEqLMbQgTGnPXnw3r5BvXFQN9w==", "signatures": [{"sig": "MEUCIQCMNqdUvKDRWOmOzSbGXF7m5Qwk2B3CKWn+g8ol/534DwIgIuYEF+cXm+hqlcHHG/f79KQQiEMMkzcGSikRwLeRenc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.5": {"name": "postcss", "version": "4.1.5", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.0.1"}, "devDependencies": {"chai": "2.2.0", "gulp": "3.8.11", "less": "2.5.0", "cssom": "0.3.0", "mocha": "2.2.4", "sinon": "1.14.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.50.0", "cssnext": "1.2.3", "request": "2.55.0", "fs-extra": "0.18.0", "gonzales": "1.0.7", "stylecow": "5.0.1", "through2": "0.6.5", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "2.0.0", "babel-core": "5.1.6", "browserify": "9.0.8", "gulp-babel": "5.1.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.9.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "bee80d22209e937f56e2e6fff2515517de4477b4", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.5.tgz", "integrity": "sha512-9Q1wy4nvpKcpMCz+kkg+zWbk8J2KGlFox3WDuQzEKgg7H0VtcSeDTt4j9URyXASu1tyNbE0QsYxrhtp+FOi9CA==", "signatures": [{"sig": "MEQCIEreQIo/viXjDOdh4UFhcQx8NMtzKD/1IdYgFlIb6v/qAiB4R5RIAUYXDYx5LEBraQklHk7hXmkAVllDooa4P0GAfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.6": {"name": "postcss", "version": "4.1.6", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "less": "2.5.0", "cssom": "0.3.0", "mocha": "2.2.4", "sinon": "1.14.1", "mensch": "0.3.1", "rework": "1.0.1", "stylus": "0.51.0", "cssnext": "1.3.0", "request": "2.55.0", "fs-extra": "0.18.2", "gonzales": "1.0.7", "through2": "0.6.5", "gulp-util": "3.0.4", "node-sass": "2.1.1", "yaspeller": "2.1.0", "babel-core": "5.1.13", "browserify": "9.0.8", "gulp-babel": "5.1.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gonzales-pe": "3.0.0-26", "gulp-eslint": "0.11.0", "stylecow-parser": "2.1.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "9fc9d74ffad1edb0058df4bc406a4fd1f2ad81ef", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.6.tgz", "integrity": "sha512-BErt/X+Rcc/E3ZP7W1lrIUl+0I/i8lwLhNuGu9Es1wBbzXBcjzHn1J6Tm3J9Swr38L/+SlPAOxx3wHmQOcO/hg==", "signatures": [{"sig": "MEQCIGGCMJ1p21O1ltv5DwPyxAadTadr3c8zRJH9rOFSlRaDAiAUnD6Kqu3iFAaCpC16jbq+cCLfrssZCUAsIZJReVgwvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.7": {"name": "postcss", "version": "4.1.7", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "sinon": "1.14.1", "request": "2.55.0", "fs-extra": "0.18.2", "through2": "0.6.5", "gulp-util": "3.0.4", "yaspeller": "2.1.0", "babel-core": "5.1.13", "gulp-babel": "5.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "203da3191ccfcd132172b454c7b37150d2ca8d58", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.7.tgz", "integrity": "sha512-qUmHkTB6TdCr/L0V9PKZIMbTFOPesb15E+L0IB8hy8cAMOg/XAu/wep37Jp5wFOgzXi036Z4TnOs+URDEnZfww==", "signatures": [{"sig": "MEUCIQCQSS9D+iBlJpPxcdE/awoEfLoWTd+9byrqWgVj4dKFMAIgSZbMG7HqDsAthx6bHEQGIPVu55C2riuCBdFft1K2RpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.8": {"name": "postcss", "version": "4.1.8", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "sinon": "1.14.1", "request": "2.55.0", "fs-extra": "0.18.2", "through2": "0.6.5", "gulp-util": "3.0.4", "yaspeller": "2.1.0", "babel-core": "5.2.2", "gulp-babel": "5.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "146f2d46f4a68675914bc743ccce64ebf21d4c7a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.8.tgz", "integrity": "sha512-d+fIXD3xR9L411xTMCjr6nFjB6o3diw+2UQhWINHtvP7p2WpTwBejMAyFwznWDq4aV+QByibG7Lh0AhTBIEZtQ==", "signatures": [{"sig": "MEUCIQC1TmrOrj0524uYrnKNr3B3g5ZiwDi9rmZAwR2mwFEFtQIgBs8WvbTTi4aiVyvbz7FPp/bH9JL7NMILVHd55Dayc5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.9": {"name": "postcss", "version": "4.1.9", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "sinon": "1.14.1", "request": "2.55.0", "fs-extra": "0.18.2", "gulp-run": "1.6.8", "through2": "0.6.5", "gulp-util": "3.0.4", "yaspeller": "2.1.0", "babel-core": "5.2.13", "gulp-babel": "5.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.11.1", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "4d3f395254ab420292f693f8657ea72c1a273787", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.9.tgz", "integrity": "sha512-Dh47WhZrZ7lk6QNbDs8bTrNVcO6Ir2Got7G0Rt7BwOOPPEfijobLCwtj625jkE6/SKEJF6uvPGLzHGOqIlojew==", "signatures": [{"sig": "MEQCIH1ziVrStqkyiVl6N6MEep7P0oZmmEv4TwFHJAMLf16PAiBgJCKODkLTlL77WoHXAIzC2LTt4kT++mATl/zdkdPrhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.10": {"name": "postcss", "version": "4.1.10", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.4", "sinon": "1.14.1", "request": "2.55.0", "fs-extra": "0.18.3", "gulp-run": "1.6.8", "through2": "0.6.5", "gulp-util": "3.0.4", "yaspeller": "2.2.0", "babel-core": "5.2.17", "gulp-babel": "5.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.12.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "64acb9745b44529da4d1d498b70c2f3bfde6782c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.10.tgz", "integrity": "sha512-DRINwsf8ZK4EYR+0YfR8TBrI8kXkAnGm2kNr2FYW1hZ8hCn9sr0A3QaqD7E3F3wGTb/aDJQJvfprqZka1K6fpw==", "signatures": [{"sig": "MEUCIBL07NId3JDwxmJF9npnQ3ySVHnsiQ+G+Sd1gY5Fc21FAiEA8MIzfmN8AYLFayPf757st0RcQjDuV8a8SHullt8diQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.11": {"name": "postcss", "version": "4.1.11", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.1.1"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.5", "sinon": "1.14.1", "eslint": "0.21.0", "fs-extra": "0.18.3", "gulp-run": "1.6.8", "gulp-util": "3.0.4", "yaspeller": "2.2.0", "babel-core": "5.4.3", "gulp-babel": "5.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.12.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "c2d9dbd632008d65d982d58726dea0c782f707b7", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.11.tgz", "integrity": "sha512-CRFZPWb+4uFF4uXDvk1vNdHDWvNJ27NWOIZtlFqkYJrvlVdlnOBlq6pvgMrY2v/eMdqfIdAE1YS9FSDc/32ncA==", "signatures": [{"sig": "MEYCIQCk5YwJmzILdRNgCBfhwQsjEvudxaOmyvnMLW8sj71XKwIhAOX32Hnp8p4A7RZbsD7Q2U97J2QqfbVIMzchvFPyXSH2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.12": {"name": "postcss", "version": "4.1.12", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.3.0"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "babel": "5.6.2", "mocha": "2.2.5", "sinon": "1.15.3", "fs-extra": "0.20.0", "gulp-run": "1.6.8", "gulp-util": "3.0.5", "yaspeller": "2.4.0", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.2", "gulp-eslint": "0.14.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "3523309f7d159b10bf77f1ac1e47d14add2253dc", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.12.tgz", "integrity": "sha512-FSYZCLF8MPLy2qX/P/fTH/OLLzIwjdnt4PV3ZO6CEf90widL4J9evgbGYF2oJF+4KNmb5c76Imxm8j8ILXNiyw==", "signatures": [{"sig": "MEYCIQDeVcqNBCHLQaBK0L39GG/RKmhyNComUwiBTofGRvXO0wIhAItYiGlUX4IygCgEmG1P91ScGuINaN4qQ9uC2xJRom/P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.13": {"name": "postcss", "version": "4.1.13", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.3.0"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "babel": "5.6.4", "mocha": "2.2.5", "sinon": "1.15.3", "fs-extra": "0.20.1", "gulp-run": "1.6.8", "gulp-util": "3.0.6", "yaspeller": "2.4.0", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.2", "gulp-eslint": "0.14.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "1839dcfc32f6599c73fa84189cb197937aa4abba", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.13.tgz", "integrity": "sha512-laxiaKq1pmfDFlr139d1Exx93t64IkJiyCeqN1Et59IogxWCnS4lYWQHWM6twwkalX3oO/yGQGIXAgtTp2f7vw==", "signatures": [{"sig": "MEQCIFK4XZ1EInKOkmfxjNSB2dtxWlnYIJ7GMw64oSiyM2bVAiB7oTF9Kfx5UIfwBLSnL4B3yIkfyFVfcM3s8aGQVL+HMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.14": {"name": "postcss", "version": "4.1.14", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.3.0"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "babel": "5.6.14", "mocha": "2.2.5", "sinon": "1.15.4", "fs-extra": "0.20.1", "gulp-run": "1.6.8", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.2", "gulp-eslint": "0.15.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "2ffe51587d961f97030eaca6784f3acfd8631a2e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.14.tgz", "integrity": "sha512-v8VmQLymBV70pujMa/S5jnU5ZSWCaUCae2uiFiitXweYwsQAew3qtjnD5jw1DGEJ4RbE2sa/DB9Qd/0njwSw4g==", "signatures": [{"sig": "MEQCIDMVGVrjGXCqaU5c6VepgyM7ku/q69vs2aJCGokR/yVhAiAspHtVWG1a0+hI0/tA+lDxuSPkHhqKqcQKsns1Rm1cvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.15": {"name": "postcss", "version": "4.1.15", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.3.0"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "babel": "5.6.14", "mocha": "2.2.5", "sinon": "1.15.4", "fs-extra": "0.21.0", "gulp-run": "1.6.8", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.2", "gulp-eslint": "0.15.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "50a4976d19e93f8498365c34b5161e920114ead0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.15.tgz", "integrity": "sha512-UMcXDnUPNFWhO/6q6P5xQbAwQguRrnv3ygsDFWz6SLiWskSVcwnjBknEaVKqL5EtqbG5TqYXcyrdTPEdhzlBYw==", "signatures": [{"sig": "MEYCIQCf3LqbCnmNMNSyDBjx6GYOoQEARfGWFbqKqplXA9qHCAIhAN0VxOH99oID/TExRNS7McdvQsK77AhnG9hnr8nXfid/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.16": {"name": "postcss", "version": "4.1.16", "dependencies": {"js-base64": "~2.1.8", "source-map": "~0.4.2", "es6-promise": "~2.3.0"}, "devDependencies": {"chai": "3.0.0", "gulp": "3.9.0", "babel": "5.6.14", "mocha": "2.2.5", "sinon": "1.15.4", "fs-extra": "0.21.0", "gulp-run": "1.6.8", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.2", "gulp-eslint": "0.15.0", "load-resources": "0.1.0", "gulp-json-editor": "2.2.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "4c449b4c8af9df3caf6d37f8e1e575d0361758dc", "tarball": "https://registry.npmjs.org/postcss/-/postcss-4.1.16.tgz", "integrity": "sha512-aAutxE8MvL1bHylFMYb2c2nniFax8XDztHzZ+x5DVsNJnoW6VHvGSNSqdW3+ip255HCWfPjayVVFzMmyiL7opA==", "signatures": [{"sig": "MEQCIEYMGZH4boALbMda95Zk66KxLq4+PZH7DXBXEvO1mcviAiAgfTkaZfWPgDOzd6ofWiubM+fXW7yADO+sCi/ZGlKaOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "postcss", "version": "5.0.0", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.4.4", "supports-color": "^3.1.0"}, "devDependencies": {"del": "1.2.1", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.2.5", "sinon": "1.15.4", "eslint": "1.1.0", "fs-extra": "0.23.1", "gulp-run": "1.6.10", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "babel-core": "5.8.22", "gulp-babel": "5.2.0", "gulp-mocha": "2.1.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.0.10", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "98a2c3f832d559b73a3d99f8067eefadbd52b183", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.0.tgz", "integrity": "sha512-rW0vttST1hFyerV3ht7Pl5Z6wjf5Mi5JXRvfaOik1UIeDIGR7F3K+TjBqbXQ6isC19C16L1oahFlLZUCOO8RKg==", "signatures": [{"sig": "MEUCIQDqnyq7ebIpvYJN5QY2EuiyPgolj4rK1m1NtUAMdAGFNQIgQeYP8SP5faGK4FKgkLV2Y9QEyJkM4CbXs2xPdsEG/eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.1": {"name": "postcss", "version": "5.0.1", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.4.4", "supports-color": "^3.1.0"}, "devDependencies": {"del": "1.2.1", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.2.5", "sinon": "1.16.1", "eslint": "1.2.1", "fs-extra": "0.23.1", "gulp-run": "1.6.10", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "babel-core": "5.8.22", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.0.10", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "8f6386fc373316f691a4ee9105f6660a9b33e358", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.1.tgz", "integrity": "sha512-DRhaXDATdJ2qMBwhitfCpJFZ/W3U2KsCTc03EpCucRBiF8/uC30Lk4SZ+ydFkyj7n/oltXe5JV/zLSMJZHk7mw==", "signatures": [{"sig": "MEQCIEbgi3CSaX789quoRe/ndAGl5RvqnZYGkHR8SjsInvv5AiA1RJpjr6j44lsy5SLqSW2vWJTs5SBT0fy+FsJ5xybgCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.2": {"name": "postcss", "version": "5.0.2", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.4.4", "supports-color": "^3.1.0"}, "devDependencies": {"del": "1.2.1", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.2.5", "sinon": "1.16.1", "eslint": "1.2.1", "isparta": "3.0.3", "fs-extra": "0.23.1", "gulp-run": "1.6.10", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "babel-core": "5.8.22", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.0.10", "run-sequence": "1.1.2", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.0", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "c353405f9a0bf119531dd869e315b349c025f570", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.2.tgz", "integrity": "sha512-JRm18uMT4xq+OLlg2XeBxx3cKl2uvcxI9gha4173QCYVaDDAgn4LxdrGPy8I4+ZPsBXy9t9UZ8BEuOOT2/7RqQ==", "signatures": [{"sig": "MEQCIFx1f8qn9sJhhJQY8P96UQ50GD7HuFowkRXY1rNfSTLxAiBssWyQTrsasrkvPUzBBO173p5+7s1LuXLNsZetWozIOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.3": {"name": "postcss", "version": "5.0.3", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.4.4", "supports-color": "^3.1.0"}, "devDependencies": {"del": "1.2.1", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.2.5", "sinon": "1.16.1", "eslint": "1.3.0", "isparta": "3.0.3", "fs-extra": "0.24.0", "gulp-run": "1.6.10", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "babel-core": "5.8.23", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.0", "run-sequence": "1.1.2", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.1", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "e18e3641e691df6800c74af0a606c719ec903e3b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.3.tgz", "integrity": "sha512-bfHCpFWsw4sbozkeUsk7ShN9MEymjiVZo2BZeOeeMPnvYUIxgvrx1lGr9D2uORrr/0p/U+Xdjy/0vSnooahjzw==", "signatures": [{"sig": "MEQCIFLqZQgOdAD8O8HAfjSrfQGyswkIP0hIP8ENQCUp1RgtAiBHQ6VoiHfjwMsG551OAeaprTXvCFcVNuGDasAVMS17gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.4": {"name": "postcss", "version": "5.0.4", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.4.4", "supports-color": "^3.1.0"}, "devDependencies": {"del": "2.0.0", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.3.0", "sinon": "1.16.1", "eslint": "1.3.1", "isparta": "3.0.4", "fs-extra": "0.24.0", "gulp-run": "1.6.10", "gulp-util": "3.0.6", "yaspeller": "2.5.0", "babel-core": "5.8.23", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.1", "run-sequence": "1.1.2", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.2", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "4e3ec2f5f0fc90210f13959b06fe17a7d1403d09", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.4.tgz", "integrity": "sha512-GdYjAI8zH++NVVjCs4M5xzgh2I+S8MrFdBpaY8FXCoo0/0O4OxKiYK0XdMG0YoeS1CtaSrLQ/J8/0OpnKuTpXw==", "signatures": [{"sig": "MEUCIQCkL4EuOv+LKD1kffuHn9IZrfC7xcs/ab443uJijjyoWQIgU9YMa+HghnpJg5xvnwABWvvljDqw/kUdfUrux13qnSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.5": {"name": "postcss", "version": "5.0.5", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.0", "supports-color": "^3.1.1"}, "devDependencies": {"del": "2.0.2", "chai": "3.2.0", "gulp": "3.9.0", "mocha": "2.3.2", "sinon": "1.16.1", "eslint": "1.4.1", "isparta": "3.0.4", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "babel-core": "5.8.24", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.4.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.1", "run-sequence": "1.1.2", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.3", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "80d0c2446591621f5c678962de3dac14d7ca64b0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.5.tgz", "integrity": "sha512-a3j3Z2sOojhJ1Y9IlmhXZ8G8X0qmCVnNg04bLpYsZV5tyHQN1+M81/kamZJ2sHMZ6RpaHLd9iujdiCmOL9J7KA==", "signatures": [{"sig": "MEUCIGrJxL9X+e7BEsDKTg/m3/ZBr+fg48c889iOllnbXedgAiEAuf1k8itux+rK9WoFGtvW4t0ohT0SaKpQcjedd234OBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.6": {"name": "postcss", "version": "5.0.6", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.0", "supports-color": "^3.1.1"}, "devDependencies": {"del": "2.0.2", "chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.16.1", "eslint": "1.5.0", "isparta": "3.0.4", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "babel-core": "5.8.25", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.4.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.3", "run-sequence": "1.1.3", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "876b7474f41aae658c1874514b663db4f251a1d1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.6.tgz", "integrity": "sha512-bx3y3PF4PU9KcaF4wGfun2lhJBqEBcUiBGLdjImlEWLt9GZD3UjgQ3aZZah9LXbCq5o0utZRmKzipMQX+uMiXg==", "signatures": [{"sig": "MEYCIQCJ1BKO8nwdJxi6/q812wURGzFJioGMRcL5sR9G6JWeJgIhAMMvhbCG0AYAyw8BwZlFXxGmb3s6MWZgS61KJ9pH9hM0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.7": {"name": "postcss", "version": "5.0.7", "dependencies": {"js-base64": "^2.1.9", "babel-core": "5.8.25", "source-map": "^0.5.0", "supports-color": "^3.1.1"}, "devDependencies": {"del": "2.0.2", "chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.16.1", "eslint": "1.5.0", "isparta": "3.0.4", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.4.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.3", "run-sequence": "1.1.3", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "55ad02dcb7da54c4d85dce43b874217a1379864b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.7.tgz", "integrity": "sha512-52BN9ofeC5rvLuBY9bylrZ5Ps+q80TK0s6E0LeMquZIqy5bJ8H6N4gPwhGzCcSemT4KFH5/QGU0FqZs9rJNiuw==", "signatures": [{"sig": "MEQCICKwNdK+XW4j6K30skYkWIlKC/kpp3opDRZRsKTCrB2oAiAEIoZ5xr712ckPE6weYktwKEepZgKZVXMjdSOc0zMwWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.8": {"name": "postcss", "version": "5.0.8", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.0", "supports-color": "^3.1.1"}, "devDependencies": {"del": "2.0.2", "chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.16.1", "eslint": "1.5.0", "isparta": "3.0.4", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "babel-core": "5.8.25", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.4.3", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.3", "run-sequence": "1.1.3", "gulp-istanbul": "0.10.0", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.2"}, "dist": {"shasum": "70783031886234d7885d3035f44d9ff36a538602", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.8.tgz", "integrity": "sha512-7dsJKANau88IzlDLLdv6LWNpvA+hleZihOgiB+bc9SD6ZLG3q1yk+1N02se193R7KyOQg7ov6Zc0obIWH1E/+Q==", "signatures": [{"sig": "MEUCIQDJ20GY6zpQcElaNi0Dc7DQJ7B5Mg0Z+fobKDEtkv2StgIgHnWoLNLkY4HtES+vjdeyQu66kGOEfIiljqDGe363Hs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.9": {"name": "postcss", "version": "5.0.9", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.1"}, "devDependencies": {"del": "2.0.2", "chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.17.1", "eslint": "1.6.0", "isparta": "3.1.0", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "babel-core": "5.8.25", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.5.0", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.3", "run-sequence": "1.1.4", "gulp-istanbul": "0.10.1", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.4"}, "dist": {"shasum": "36888118cce2dfb09b6825dcbb3f36f712b6101e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.9.tgz", "integrity": "sha512-JmQcDZuA6RYdhvVC+bJXnuazhjbVpxOvsABiFvflXM6z+EfhbsInIemGxkkoXqGH9AA9370iuhpWByDsj48nhw==", "signatures": [{"sig": "MEYCIQDv4xGd2rtp9T2Q8RSekdtL3yNDxZfllMADID1KELnodQIhAKO2YBkv0FnCFx605otdZi0EBcE6xj/gs4CVmZ5J6RVI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.10": {"name": "postcss", "version": "5.0.10", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.0.2", "chai": "3.3.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.17.1", "eslint": "1.6.0", "isparta": "3.1.0", "fs-extra": "0.24.0", "yaspeller": "2.5.0", "babel-core": "5.8.25", "gulp-babel": "5.2.1", "gulp-mocha": "2.1.3", "gulp-shell": "0.5.0", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.3", "run-sequence": "1.1.4", "gulp-istanbul": "0.10.1", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.4"}, "dist": {"shasum": "86eacc9036c5c063e27138bf9503e1de26ab69fe", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.10.tgz", "integrity": "sha512-XTOui5V3OddaF5ZloIiwIvlExfL0o1mJZIaM2nrS7g6I66KdTQ7xMZpHomBb99wy/CVv0P5ytvCC3l3/AbmmpA==", "signatures": [{"sig": "MEYCIQCdi/87i0kFnBcL8WutcXABOizCFU+Lvk9DnyzgQkfTeAIhAL66zMI9YMlbg2CyGq5falOwLd1pEFqDhwA4SWzJOHK5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.11": {"name": "postcss", "version": "5.0.11", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.0.2", "chai": "3.4.0", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.17.2", "eslint": "1.9.0", "isparta": "3.5.3", "fs-extra": "0.26.2", "yaspeller": "2.5.1", "babel-core": "5.8.25", "gulp-babel": "5.3.0", "gulp-mocha": "2.1.3", "gulp-shell": "0.5.1", "strip-ansi": "3.0.0", "gulp-eslint": "1.0.0", "babel-eslint": "4.1.4", "run-sequence": "1.1.4", "gulp-istanbul": "0.10.2", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.4"}, "dist": {"shasum": "b9475ef3ac54abb8049deb7ded3bbbf97689e6ef", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.11.tgz", "integrity": "sha512-gTlwKgukggx6HOchJdwJNgVa6aXcnLfoP0sCsW9V63i6LERf4Msi6QJN0IJmrs7qaO1BxTvobANpItnNtJw0qQ==", "signatures": [{"sig": "MEQCIHcnPiRPAlwEVqrL1XTGgUtTInvOK3EaT8/crWAOf6TnAiAeA+MAhoATjH4vkdwrsrKiegTV5Nf5jXuEFhJLL1Nh1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.12": {"name": "postcss", "version": "5.0.12", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.0.2", "chai": "3.4.1", "gulp": "3.9.0", "mocha": "2.3.3", "sinon": "1.17.2", "eslint": "1.9.0", "isparta": "3.5.3", "fs-extra": "0.26.2", "yaspeller": "2.5.1", "babel-core": "5.8.25", "gulp-babel": "5.3.0", "gulp-mocha": "2.1.3", "gulp-shell": "0.5.1", "strip-ansi": "3.0.0", "gulp-eslint": "1.1.0", "babel-eslint": "4.1.5", "run-sequence": "1.1.4", "gulp-istanbul": "0.10.2", "gulp-json-editor": "2.2.1", "postcss-parser-tests": "5.0.4", "concat-with-sourcemaps": "1.0.4"}, "dist": {"shasum": "61297ec594d17705071ad888f5ed408905b9575a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.12.tgz", "integrity": "sha512-rKUDJwroKMhEqmBg3YKtDNtJQ6hKttSXT4BgnmhcGfNHpwzD2n/lP1uOpmovPMFdp5B3fFkqe6LTy2dNcZvdeg==", "signatures": [{"sig": "MEUCIQCrr6UaCkIhfgd1j7G6EyNDxrg81vn/Oin86TEM+l2RbwIgMSpgcUUnYVWqPq7obJ3b41P/htpjetJUysT2mjDXe2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.13": {"name": "postcss", "version": "5.0.13", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.4.1", "gulp": "3.9.0", "mocha": "2.3.4", "sinon": "1.17.2", "eslint": "1.10.3", "isparta": "4.0.0", "fs-extra": "0.26.2", "yaspeller": "2.6.0", "babel-core": "6.3.17", "gulp-babel": "6.1.1", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.1", "strip-ansi": "3.0.0", "gulp-eslint": "1.1.1", "babel-eslint": "5.0.0-beta6", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-stage-0": "6.3.13", "postcss-parser-tests": "5.0.5", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "6.1.3", "babel-plugin-add-module-exports": "0.1.1"}, "dist": {"shasum": "90731ea7cbc3e786e714fe6bb6b79adf3e189a86", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.13.tgz", "integrity": "sha512-dGCVKc1Y0WIpMwIDeSMB7JxUAXe/jryBfIq0ycI3agsJllWp8dOHGK/8DNgFXIu/tvqE6AwT8QZdPbFnk4Xhnw==", "signatures": [{"sig": "MEUCIGoL9rBSUDaGBPokt+5bPSETzK2QofRZVrLrmGIc+DeBAiEAnMfF52Lp5emBsDabDM8Q21dcntWx6XKNWtAfcqxlgfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.14": {"name": "postcss", "version": "5.0.14", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.4.1", "gulp": "3.9.0", "mocha": "2.3.4", "sinon": "1.17.2", "eslint": "1.10.3", "isparta": "4.0.0", "fs-extra": "0.26.3", "yaspeller": "2.6.0", "babel-core": "6.3.26", "gulp-babel": "6.1.1", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.1", "strip-ansi": "3.0.0", "gulp-eslint": "1.1.1", "babel-eslint": "5.0.0-beta6", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-stage-0": "6.3.13", "postcss-parser-tests": "5.0.5", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "6.1.4", "babel-plugin-add-module-exports": "0.1.2"}, "dist": {"shasum": "164dafa9f3c6775ee599919cda610adeb495fcec", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.14.tgz", "integrity": "sha512-bW23S0TQ0+BafL6DhJYQi9k7QmX7CvRsxjdaJPlxmg8G/lal+zwp2RzPn/0WkHVZc5R+0gB7o2ce9NXsr9khuA==", "signatures": [{"sig": "MEQCIHS3mCRCu6p4NykU5Wi8eE59OIcRu9ABurcA56MDrp6vAiB7nm383Ugoa6shLzMfl1jIxkWTyiq3JrPkog2m8mMeGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.15": {"name": "postcss", "version": "5.0.15", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "1.10.3", "isparta": "4.0.0", "fs-extra": "0.26.5", "yaspeller": "2.6.0", "babel-core": "6.5.1", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.0", "gulp-eslint": "1.1.1", "babel-eslint": "5.0.0-beta9", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.5.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.5", "eslint-config-postcss": "1.0.0", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2"}, "dist": {"shasum": "ac9d6b520ddfbf9dfe21f46bce45d161fa087522", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.15.tgz", "integrity": "sha512-ZPapmzMPEgV45c9Fv7NQ5ebz8yzG1BaN3OVnyiYYUW/+teF1rTs+Fc1NgcXx/JK+33ua/7LAIaRSdhZPyMseRg==", "signatures": [{"sig": "MEYCIQDQ5ytas0rRXA5mcYhqDCD62Pi0PG5B4yHCvVYtgo+WRAIhAOQiRaxgYdz/b2XYR9wu3fO4LVXql4xf2lmNpxjVR016", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.16": {"name": "postcss", "version": "5.0.16", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.0.0", "isparta": "4.0.0", "fs-extra": "0.26.5", "yaspeller": "2.6.0", "babel-core": "6.5.2", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.0", "gulp-eslint": "2.0.0", "babel-eslint": "5.0.0-beta10", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.5.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.5", "eslint-config-postcss": "2.0.0", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2"}, "dist": {"shasum": "b14b9fdef1151d8ca32422e51d8c95b5d409004c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.16.tgz", "integrity": "sha512-3ZsDTw4bkq76hEt48OyxmZjcW8Kb7M7txfTaks8DdLVjq7ajToUeDoBloipLIqvVKiDg18lyyvsNisILtsSfWA==", "signatures": [{"sig": "MEUCIQCFSARfE3c3X4JKzU1/Na/6+DcNHiWxm/vV5KOlx3okXgIgeKkcFM4Kzbq7N9aRGdy1OCT9dPqkdA/JGra/eXGlS88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.17": {"name": "postcss", "version": "5.0.17", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.2.0", "isparta": "4.0.0", "fs-extra": "0.26.5", "yaspeller": "2.6.0", "babel-core": "6.5.2", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.1", "gulp-eslint": "2.0.0", "babel-eslint": "5.0.0", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.5.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.5", "eslint-config-postcss": "2.0.0", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "1784874fff56c224157f1c9613d7771bd506ebc8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.17.tgz", "integrity": "sha512-9vealROLoUMmYy/ZuK9GfFpT82fKmkHnxExnIo/pN7nIl93sGGKq0kRdp1Yuhy8PTCHKV7YE0S2fngnvoTYJ4w==", "signatures": [{"sig": "MEQCIE2jdSr28C5S7CPKRR6HNjxjKyv/BfEXdR3ZwiWj1XKIAiBlR1h6RXLaKxAY8aDj/HPSZkHzeuPQwphF6/JXGUttXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.18": {"name": "postcss", "version": "5.0.18", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.2.0", "isparta": "4.0.0", "fs-extra": "0.26.5", "yaspeller": "2.6.0", "babel-core": "6.5.2", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.1", "gulp-eslint": "2.0.0", "babel-eslint": "5.0.0", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.5.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.5", "eslint-config-postcss": "2.0.0", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "592fc5983931fba57e4c92ba141aa2c17fb1f21a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.18.tgz", "integrity": "sha512-XsNfCYqEJtpqflPc8pUsaEzYNBw7gbUOJVr5iiA0eDX0I3NVXr1lYCGaW5MThBjvJoHWlJDdQ+pUQuLuaRcJnw==", "signatures": [{"sig": "MEUCIQDpdeUbwBBD3lea6LwN5Uw9g/Tjqs0GOgseWJ37RgVbWAIgKemhjYMvquArID8gR5RRECjQkR94ZKcOxz1uHigYea4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.19": {"name": "postcss", "version": "5.0.19", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.1", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.2.0", "isparta": "4.0.0", "fs-extra": "0.26.5", "yaspeller": "2.6.0", "babel-core": "6.6.0", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.1", "gulp-eslint": "2.0.0", "babel-eslint": "5.0.0", "run-sequence": "1.1.5", "gulp-istanbul": "0.10.3", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.6.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.6", "eslint-config-postcss": "2.0.0", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "b6342a01dc75b8cab7e968afda96aefc67f888af", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.19.tgz", "integrity": "sha512-O5mZHjclPKM1qwIQEBXxVF1iqKB+8JE7Z7YWj0MJoOXfQ3EdIzHf8zEVuocvRXCsh8zX77sQPVcFSpj6Zs1zKg==", "signatures": [{"sig": "MEUCIQCvlxngWYt7THUKdUSp+rB6S4je7xy6yiH6+0NJcDB5MgIgeHVsO/Ns42leKscG7bDDSn/8x9TDp2Ofaru3e0BU+iw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.20": {"name": "postcss", "version": "5.0.20", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.5", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.9.0", "fs-extra": "0.30.0", "yaspeller": "2.6.0", "babel-core": "6.7.7", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.1", "gulp-eslint": "2.0.0", "babel-eslint": "6.0.4", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.6.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.7", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.4", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "530e1c0786d5738e7557df5c1201f1aed1e4e818", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.20.tgz", "integrity": "sha512-08KpowWvhfkV+04F7/sBSkiSEFTdBb6E5Bdg7t08M27WU0ECOwOkyOh3VulXUj4F9hk0t/HqnIJv03VS/saPnQ==", "signatures": [{"sig": "MEUCIEUR+vg9vhusChV/o62IuZej8VNcl/VDvnIk2qu+ESghAiEAsbBPh9MvG5x71jP5PEkadtwT4QLBotJGUtAq4MR3aZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.0.21": {"name": "postcss", "version": "5.0.21", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.5", "supports-color": "^3.1.2"}, "devDependencies": {"del": "2.2.0", "chai": "3.5.0", "gulp": "3.9.1", "mocha": "2.4.5", "sinon": "1.17.3", "eslint": "2.9.0", "fs-extra": "0.30.0", "yaspeller": "2.6.0", "babel-core": "6.7.7", "gulp-babel": "6.1.2", "gulp-mocha": "2.2.0", "gulp-shell": "0.5.2", "strip-ansi": "3.0.1", "gulp-eslint": "2.0.0", "babel-eslint": "6.0.4", "gulp-json-editor": "2.2.1", "babel-preset-es2015": "6.6.0", "babel-preset-stage-0": "6.5.0", "postcss-parser-tests": "5.0.7", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.1.4", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "d4cf6f19774648c492ac57c298f6afb3c04caefe", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.0.21.tgz", "integrity": "sha512-/UdnZhOe5WC0Kvts13bNLPREqhaU0ntLQ1v29S5ofLx38zP+WhM0sjhVzrPrIQwKwXhtf8byfH+BROc3t2YQRg==", "signatures": [{"sig": "MEUCIQCDAiABf5nCBShwWyJVRGrYejzMBtDEbObd9qFLw1njGwIgHiJcEm2DHfdBMkr5qgSvrEc0K4CFXIPRRTsGOtefa4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.1.0": {"name": "postcss", "version": "5.1.0", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.15.2", "del": "2.2.1", "gulp": "3.9.1", "sinon": "1.17.4", "eslint": "3.0.1", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.12.1", "gulp-run": "1.7.1", "yaspeller": "2.8.1", "babel-core": "6.10.4", "gulp-babel": "6.1.2", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "gulp-jsdoc3": "0.3.0", "babel-eslint": "6.1.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.9.0", "postcss-parser-tests": "5.0.9", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "7860e3903c547b50c7e52edb3dbca30477cd1e5f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.1.0.tgz", "integrity": "sha512-i3wj6DiNlg6FggMq7gOyK1Y1nS//m4ggYHbsZB3rd9SidKGhgvGIa851bh5WjHcaCkoq25fCRHOqb2QbQlgCxg==", "signatures": [{"sig": "MEYCIQC3bRcdo38SikVlqRL/kL+KSRxTcLNbSVUYGhoGRq/TvAIhAP5Mi+xTvlIehCsqCU44Im9kgZTtHdUbOcmYzgROd/lW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.1.1": {"name": "postcss", "version": "5.1.1", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.15.2", "del": "2.2.1", "gulp": "3.9.1", "sinon": "1.17.4", "eslint": "3.1.1", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.12.1", "gulp-run": "1.7.1", "yaspeller": "2.8.2", "babel-core": "6.11.4", "gulp-babel": "6.1.2", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "gulp-jsdoc3": "0.3.0", "babel-eslint": "6.1.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.9.0", "postcss-parser-tests": "5.0.9", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-preset-es2015-loose": "7.0.0", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "c7947993b76d8f3e069b2c223b185581a7e54164", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.1.1.tgz", "integrity": "sha512-jkAPKvj7tWk/H+xREBZk7MVqkdG4lQtg3V3M/5WzvDXmYSEW0IvaLiaieEmRdSVs5h9zTMSV+8F/hV1zydopqw==", "signatures": [{"sig": "MEYCIQDttBMoPon7jUi7fm9/3zXdcbgCcHKRdbv5XnCHVoYu2wIhAPwB98QsQg2h3HVXfSWktcKiEs5daIUk6dOMnMjgGLtj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.1.2": {"name": "postcss", "version": "5.1.2", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.1", "gulp": "3.9.1", "sinon": "1.17.5", "eslint": "3.2.2", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.13.0", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.13.2", "gulp-babel": "6.1.2", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "gulp-jsdoc3": "0.3.0", "babel-eslint": "6.1.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.13.2", "postcss-parser-tests": "5.0.9", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "bd84886a66bcad489afaf7c673eed5ef639551e2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.1.2.tgz", "integrity": "sha512-V1DkLYJdbIbSVnsFe2OdMMNoxnwSub0NqMo2U451WeJYB56ZrnbRsAYyNTu7GD5W92pQTe9+TRDezAr7+NRuhg==", "signatures": [{"sig": "MEQCIHKfx/eBgfxOX6QXyukd9bJqDtHSGDdy2ZyEvpNiBOpJAiB7lBxaWIy1JotLzqbvLBTFaaCGMreZZ4CLD6tcn4voDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.0": {"name": "postcss", "version": "5.2.0", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "sinon": "1.17.5", "eslint": "3.4.0", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.14.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "gulp-jsdoc3": "0.3.0", "lint-staged": "2.0.3", "babel-eslint": "6.1.2", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.14.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "cdbee46b45abec245ed7ee8e905fc336c1dce2c9", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.0.tgz", "integrity": "sha512-RwsHGyfj+YyGvDry6CBvJjUuqJckpd/jur6EbdJAu9qs+ZFL3TwmtcgG9R8cqqI3MGeR2p2ynkjS9bN8FZtvew==", "signatures": [{"sig": "MEUCIQDxobSh0N1r+RfcijlhoXvkAKfW13TONpDnMadd35Y/HwIgItBLWBiBocsTj2oddtEx+XnEhTamEC8xsQ2jHtCVLoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.1": {"name": "postcss", "version": "5.2.1", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.1", "sinon": "1.17.6", "eslint": "3.6.0", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.14.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.0.3", "babel-eslint": "6.1.2", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.14.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "02c23730ad4a2efc7abdbda64c32e09eeaf9ee3c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.1.tgz", "integrity": "sha512-R<PERSON><PERSON><PERSON><PERSON>lHtiKLkeHuNqQBUWOBiWmoQA/cZXQ7yiCLQVXJKtcdLtcc69LO6rcfLEp47pZY+xVfBv0TgPnxydHkVg==", "signatures": [{"sig": "MEUCIH+2MWiaIt7inSv29B+AP/DzZ+Zniy79dElICJns+qOFAiEAp8UixwCZIpBgQCmYPxVhMb+b9dIcd3nsfTcAUxHHYfE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.2": {"name": "postcss", "version": "5.2.2", "dependencies": {"js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.1", "sinon": "1.17.6", "eslint": "3.6.0", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.14.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.0.3", "babel-eslint": "6.1.2", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.14.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "7e1790ed055d62af15164aba7ffcb0a7ba56815f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.2.tgz", "integrity": "sha512-7tN8z1T/y5OADL0t0PjxEn0pFhDbs+4DPDtYbdaDzbFxy3iymdnAsjf+BKylzH3WZNQs05e2G8f2r0m3zB1RqQ==", "signatures": [{"sig": "MEUCIQDrd6qTlFI/BTk4D0dLeRscJhfWfCLsQyAdAtH8EKygjgIgEcp3kHJKC9A5BdU5TS65Qyw/TI41mbam96/vJF45Q7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.3": {"name": "postcss", "version": "5.2.3", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.1", "sinon": "1.17.6", "eslint": "3.6.1", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.16.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.0.3", "babel-eslint": "7.0.0", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.16.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "79eede69d334f7b292a2dff996eec76e25344465", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.3.tgz", "integrity": "sha512-qeE6KVtJm5VLlDOOxtl9ecsymQVjtBmEOfEXobvpfPeVJvN/fQe3dWpoP+Jz+xT4xYmnKu5rtCxW5hhK1HSVWw==", "signatures": [{"sig": "MEYCIQCcCKMFdsPQ2OMUBxsAmih3FFR/xafLsuG9NAIomzyt8AIhAMew0hHXQ/Ey7HeWTQqCAwbebOEVSHCtVPxBh4bGgwd7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.4": {"name": "postcss", "version": "5.2.4", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.1", "sinon": "1.17.6", "eslint": "3.6.1", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "2.9.1", "babel-core": "6.16.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.0.3", "babel-eslint": "7.0.0", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "1.6.0", "babel-preset-es2015": "6.16.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "8eb4bee3e5c4e091585b116df32d8db24a535f21", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.4.tgz", "integrity": "sha512-JGSj757SOrfyBwGax8Xf+jRxFkfYnHsLR45yuHtPOAWv4YzvF3ZpIJ4wIym4kxTbsv3BlNYUlmw/sYZKM9ntKw==", "signatures": [{"sig": "MEUCIEPXtNLNlLWAlNjsBuBKZXDkPwW75I8cwa3DMWWghEORAiEA3tCKbhhKfMEaAVk143PtMDLn2tH8Jqogx3KTjQDuybA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.5": {"name": "postcss", "version": "5.2.5", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.16.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.2", "sinon": "1.17.6", "eslint": "3.8.1", "docdash": "0.4.0", "fs-extra": "0.30.0", "gulp-ava": "0.14.1", "gulp-run": "1.7.1", "yaspeller": "3.0.0", "babel-core": "6.17.0", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.2.0", "babel-eslint": "7.0.0", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "2.1.1", "babel-preset-es2015": "6.16.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "ec428c27dffc7fac65961340a9b022fa4af5f056", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.5.tgz", "integrity": "sha512-QUj3yzINEmaPoflBo3foVrnzXKj36GwjPe4LoEX7T/nKyLNu+cy/GKyxDoRMEIQM3L/PP4lVD1kEGLWZdWVmZA==", "signatures": [{"sig": "MEUCIQClLB0aDp6R03abOB+ummH/afccW+FxQS6HglFk0Tpc+AIgIIT/Ypc8ds0IJq4cqb10/KjFsYYvrf+wzFDv+0ZahHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.6": {"name": "postcss", "version": "5.2.6", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "0.17.0", "del": "2.2.2", "gulp": "3.9.1", "chalk": "1.1.3", "jsdoc": "3.4.3", "sinon": "1.17.6", "eslint": "3.10.2", "docdash": "0.4.0", "fs-extra": "1.0.0", "gulp-ava": "0.15.0", "gulp-run": "1.7.1", "yaspeller": "3.0.0", "babel-core": "6.18.2", "gulp-babel": "6.1.2", "pre-commit": "1.1.3", "strip-ansi": "3.0.1", "gulp-eslint": "3.0.1", "lint-staged": "3.2.1", "babel-eslint": "7.1.1", "gulp-changed": "1.3.2", "run-sequence": "1.2.2", "gulp-sourcemaps": "2.2.0", "babel-preset-es2015": "6.18.0", "postcss-parser-tests": "5.0.10", "eslint-config-postcss": "2.0.2", "concat-with-sourcemaps": "1.0.4", "babel-plugin-add-module-exports": "0.2.1", "babel-plugin-precompile-charcodes": "1.0.0"}, "dist": {"shasum": "a252cd67cd52585035f17e9ad12b35137a7bdd9e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.6.tgz", "integrity": "sha512-lDaT31ddZkkoYlQ1I5IGCuNUOjerH0aR5Id4tbbC/GbHSvHLeXz+fkxOGu9bnSW++6tOjcvatH1MIqa1ng+rlw==", "signatures": [{"sig": "MEUCIBFLZNBcZhkGQjz32iIxK7VrU2YPkoTAyNoya1NMg8ZwAiEAl+pEn005o1N2h4Fa6yMiCBNGfkeD3QUxD9wwoQlKjGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.7": {"name": "postcss", "version": "5.2.7", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.6", "eslint": "^3.12.2", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.21.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.2.4", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.2.1", "babel-preset-es2015": "^6.18.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "e38fb8370b7c0703922ecefdf21256c5f4b8d369", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.7.tgz", "integrity": "sha512-KKVyVkG0o91mZEfPFZ3BOEuzB7+DvJ7Y+0PC5fq5/2lYb94N/1Z+Xfaq9Z9yYy2QtY5xifkOGThGF3CYjoHWEw==", "signatures": [{"sig": "MEUCICwqXpM2i5rSlmZRh+96Pk7U5UBpXV6fXE/Ju7G5KjUIAiEA6gUyO49yCBs3wnVC8enukkiiqyvuXkvavM6g0Jc1kbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.8": {"name": "postcss", "version": "5.2.8", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.6", "eslint": "^3.12.2", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.21.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.2.4", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.2.1", "babel-preset-es2015": "^6.18.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "05720c49df23c79bda51fd01daeb1e9222e94390", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.8.tgz", "integrity": "sha512-s3/Ir2HuvXMaOg1alnhHQeLyJ7s4hOPg1tme+aN4MQA3OxV0HfkGAzl8d6dWJI5XVV5a5d0jx1EjDL/BrCSS9A==", "signatures": [{"sig": "MEYCIQCQRBi5Hw0GH8qTSZ8xNRVyx8ih0E+90UoU33kMKRNm1AIhANI6yDQpzbDg1BsUSJQtLhYXDHv97Cw0IFMjZaObQX43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.9": {"name": "postcss", "version": "5.2.9", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.13.0", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.21.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.2.5", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.2.3", "babel-preset-es2015": "^6.18.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "282a644f92d4b871ade2d3ce8bd0ea46f18317b6", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.9.tgz", "integrity": "sha512-M23TQqV+hH6nb/oCR7u+iV4DRUe//HblWntjgTDu8go+dJEHSRM7A+Y38+33OTkP53lTFigkH/PmkgjcUTWwKQ==", "signatures": [{"sig": "MEYCIQCivMurCPPMlc3/w2+v39FMgzCTYUSHJfv4lrH1IowLBwIhAOUsTj3Qzq3dsMF7bRmEv/Z8p4WAibJVvSp9iul+JbVK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.10": {"name": "postcss", "version": "5.2.10", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.1.2"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.13.1", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.21.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.2.6", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.3.1", "babel-preset-es2015": "^6.18.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "b58b64e04f66f838b7bc7cb41f7dac168568a945", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.10.tgz", "integrity": "sha512-d7P+NmoWSv3jVT7cMMTWy7W9QzhdX5wQ28x4v4vc930uYW65YEXfBoqMUAy0EYCB9wY64m1wzCiZWsJvutJTDw==", "signatures": [{"sig": "MEUCIQDdg6qkc3Qpp48gmJuRcdM/tO2LKQmCLE0mYzKez94T1gIgYSNuwpfkx/ckA130MkOEb6y3cip5EE77oHpUS8uwQVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.11": {"name": "postcss", "version": "5.2.11", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.13.1", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.22.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.2.7", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.4.0", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "ff29bcd6d2efb98bfe08a022055ec599bbe7b761", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.11.tgz", "integrity": "sha512-0HO8KHoqxId0/3uiLy8pKiY+fzizUWVuZurZ5YYBsm5Vc0MyYz3qGCPTmICF8P6ialNHAfv94I7yjQDMXxZcWw==", "signatures": [{"sig": "MEUCIQDTdoVPwmGdbaK0Z0QkdjNS6o+goRTEjuC0+wQI1aCVbAIgD//yMA6VlyPQKcvY67hhwq5n36DZl5oOL8G+5Rxuio8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.12": {"name": "postcss", "version": "5.2.12", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.15.0", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.22.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.3.0", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.10", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "6a2b15e35dd65634441bb0961fa796904c7890e0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.12.tgz", "integrity": "sha512-YfnfphNMKQ7Tl73ihzYrf4r9cRkhWrApqV1uoul1rS26iJHdpRUOqnvj6a3/n2SMKSA8mlm6OSxSroAl2O1vrA==", "signatures": [{"sig": "MEUCIE9X55Xnwh5y+j07coNVLZ5Hmh3Fr7mD8wImYnVuV+KBAiEA7SWWunDD7jEcN0g6a92CbnzZM7E5qLVgLswm8XZULGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.13": {"name": "postcss", "version": "5.2.13", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.15.0", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.23.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.3.0", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "1be52a32cf2ef58c0d75f1aedb3beabcf257cef3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.13.tgz", "integrity": "sha512-1pIRgtrwPuv0PVBgrJhbemEa6XPoz5kxmMjQaYC1Rj1TCVS2OcQBwQ8KItYEVMsshGfHLYBIWzJUvwy9DqkcwA==", "signatures": [{"sig": "MEQCIGcYdIy/ogGc0oz105YnWe+6VqPWaCE+5xKlhJ8nvkC2AiB81nyRnMiScPep6Goyjvhe7/zgm7/lUO0VlL69xA4seA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.14": {"name": "postcss", "version": "5.2.14", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.15.0", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.23.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.3.0", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "47b4fbde363fd4f81e547f7e0e43d6d300267330", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.14.tgz", "integrity": "sha512-qwY8ND+CGoUlJwndrBKq7vIIk9ko9lkHzikwVUMfKT8B+qhN97jrHt6R5ZgiXkry4/yjYbxYGIGfvPRijwJRoA==", "signatures": [{"sig": "MEUCIQDxhYJgw7lj56Bzh/CNbi7qU8qraYpirfTLrTBkLo9WHwIgHvfyA8+ZEpfcLK/4QFbGN2MDT0if1W/n6anzb466lx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.15": {"name": "postcss", "version": "5.2.15", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.16.0", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "yaspeller": "^3.0.0", "babel-core": "^6.23.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.3.1", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "a9e8685e50e06cc5b3fdea5297273246c26f5b30", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.15.tgz", "integrity": "sha512-XYMyCZhgvdUNwPunHabt8MhSYOSFoB/aKPBj8UVH1NcQT+HYv63yzcdZvfdUYpJjTctRUs6rjClWKW/qSOFOLw==", "signatures": [{"sig": "MEYCIQCP6rUOe7hnTHrY1rCNctN+IsdsMQVGhBNoD3mR8RoJfgIhAMd6foyb5ErEPuB28UaHpiL8bxLvehFAlvV2zPC5ZDD8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.16": {"name": "postcss", "version": "5.2.16", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^1.17.7", "eslint": "^3.17.1", "docdash": "^0.4.0", "fs-extra": "^2.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "babel-core": "^6.23.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.3.1", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.3.0", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.22.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "732b3100000f9ff8379a48a53839ed097376ad57", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.16.tgz", "integrity": "sha512-elFUff0WfmN4OTGB6gQG2dZgRkIyq3f9n+LNymGxFVS3+39QsFnxIXgHGE1yAWDKpTpfnSvmfFIrZq6ijhZgyQ==", "signatures": [{"sig": "MEUCIQDGEDdetNyWpAkk4s/+Dz54+Oj0DreorloH3EELhHc+zAIgKZHJCjxw91fmt1h2KoW36HxnjYpaYRCB1bCb598sa0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "5.2.17": {"name": "postcss", "version": "5.2.17", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^2.0.0", "eslint": "^3.18.0", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "babel-core": "^6.24.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.0", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.3.0", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.24.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "cf4f597b864d65c8a492b2eabe9d706c879c388b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.17.tgz", "integrity": "sha512-0n4/a5Exgod60L1TjXAYJYqb8kwohUsXqUxYKOUqpRL8h2xL+vhQEuxxkvnWz8PtQiX1Qon3Xfzkx8yeJBBLLw==", "signatures": [{"sig": "MEQCIEiozqKNBfVxjL1MZ7cfiZ4m3BTOOuZhX2oi4C3ACO6fAiBFlcDEbn8UrGkFoINDjL2mrWhi7r6Z8iQudmito45pHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "6.0.0": {"name": "postcss", "version": "6.0.0", "dependencies": {"chalk": "^1.1.3", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"del": "^2.2.2", "gulp": "^3.9.1", "jest": "^19.0.2", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "eslint": "^3.19.0", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^1.0.0", "babel-core": "^6.24.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.4.0", "postcss-parser-tests": "^6.0.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "ef75a67e75fe2bfff53971045697202c018b2e81", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.0.tgz", "integrity": "sha512-HwOuQBqmFDBNQMQsikeTHnJYvDIvtQms6bwUJQ/jBtwQSTRmBcxpq9uXsgsJOwVi9TQXGNdM5VmIIst1h/X8Wg==", "signatures": [{"sig": "MEUCIBvX6qVsGmxGtBw4djGfXKzlgnX+P7+Nh8bQjP/vKOG1AiEAq0Lq2cRQtF3/NKG02KaxZGF+CURX7XM6B15b20fSR5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.1": {"name": "postcss", "version": "6.0.1", "dependencies": {"chalk": "^1.1.3", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"del": "^2.2.2", "gulp": "^3.9.1", "jest": "^19.0.2", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "eslint": "^3.19.0", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^1.0.0", "babel-core": "^6.24.1", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.4.0", "postcss-parser-tests": "^6.0.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "000dbd1f8eef217aa368b9a212c5fc40b2a8f3f2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.1.tgz", "integrity": "sha512-VbGX1LQgQbf9l3cZ3qbUuC3hGqIEOGQFHAEHQ/Diaeo0yLgpgK5Rb8J+OcamIfQ9PbAU/fzBjVtQX3AhJHUvZw==", "signatures": [{"sig": "MEQCIBVtRbb3VlQNHOExvl0iY5EFVGjbiHn7fTuF80KXXZRuAiBkE1QhslFODAhY6PB/l6Qm5+uLM7HCxPzg3YiTk5f/Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.2": {"name": "postcss", "version": "6.0.2", "dependencies": {"chalk": "^1.1.3", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "eslint": "^4.0.0", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^4.0.0", "lint-staged": "^3.6.1", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.5.2", "postcss-parser-tests": "^6.0.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "5c4fea589f0ac3b00caa75b1cbc3a284195b7e5d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.2.tgz", "integrity": "sha512-jwJuCAYUzMHVTcyzDUiNzumbRpYeZtSFj1JIgdKIlxyIuOgLSGZyajK2a/YHQr7goESFArIW2UjpPFt+utMQLg==", "signatures": [{"sig": "MEUCIQDR1q/z8URTJvX3vHrBR2dKKk4kTPV5eqtZ28CUZ1PrAwIgFiZp9vkhSZvBjNnKfGYiTAFrUBg7V8EB3jCY2dJolls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.3": {"name": "postcss", "version": "6.0.3", "dependencies": {"chalk": "^1.1.3", "source-map": "^0.5.6", "supports-color": "^4.0.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "eslint": "^4.0.0", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.0", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.5.2", "postcss-parser-tests": "^6.0.1", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "b7f565b3d956fbb8565ca7c1e239d0506e427d8b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.3.tgz", "integrity": "sha512-kk/Fjo+78R0z72OkvPRNq4u4zsM3kTRb15aQQpHu81L6G/19zFTibdWHsSEpN7p4MLjbF0yCWZId5B/NKjcEOw==", "signatures": [{"sig": "MEUCIQCpeDn9UnVA+Snu+UqzeunLQwuenY2Ckx4AoimTBQH8MQIge5mBgLxovcEygppNxPSzF1kxNC7XezOJypk+02Mymoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.4": {"name": "postcss", "version": "6.0.4", "dependencies": {"chalk": "^2.0.1", "source-map": "^0.5.6", "supports-color": "^4.0.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.4.3", "eslint": "^4.1.1", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.0", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.5.2", "postcss-parser-tests": "^6.0.1", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "573acddf73f42ecb24aa618d40ee3d5a7c04a654", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.4.tgz", "integrity": "sha512-8jzpwu8bQhWyCdDwqpyJDAWbUFQvqCjSW2x3Y2kPiu22Cu+PaDGCEvRuJcZZbvE1GXwwYq6x9XXk+VeoM4/HvQ==", "signatures": [{"sig": "MEQCIBGuGU95QLy2fFZjAINrJFUUzY3ruzT6039iiRc4FtvtAiAO5c33Mlz3iORcxQ6srzVdW+vWvGkLVV4TdxdRROiw8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.5": {"name": "postcss", "version": "6.0.5", "dependencies": {"chalk": "^2.0.1", "source-map": "^0.5.6", "supports-color": "^4.1.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.4.3", "eslint": "^4.1.1", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "size-limit": "^0.3.0", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.0", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.0.0", "yaspeller-ci": "^0.4.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.5.2", "postcss-parser-tests": "^6.0.1", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "efa34f745ca25bd3b3cbd15aa4e03112b8237f3c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.5.tgz", "integrity": "sha512-dEnscuPJbyyPg0Jn5bT/bXeD4ZfoA5Ir09OixcQoQ6MDdscOT7W8cl75VhUnvmEH5lVj4VPY+OUzfUn5LGq2Tw==", "signatures": [{"sig": "MEUCIQCD1IQReXCGHzqa1z5qRgcPsV3PDhd8x38wNevuAMRL6gIgZKKEfpBHqSKo4qe7ng29L777cWuFpnCaLI1hhh/YT38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.6": {"name": "postcss", "version": "6.0.6", "dependencies": {"chalk": "^2.0.1", "source-map": "^0.5.6", "supports-color": "^4.1.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.4.3", "eslint": "^4.1.1", "docdash": "^0.4.0", "fs-extra": "^3.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "size-limit": "^0.3.2", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.0", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.0.0", "yaspeller-ci": "^0.4.1", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.0.2", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "bba4d58e884fc78c840d1539e10eddaabb8f73bd", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.6.tgz", "integrity": "sha512-KkYn44ZVVsqOheFhNxu1XVxmBu6egkdxZdqfxsA1AJGSqpJiCVsC3SWiUq+xn6m/G70V1WDNC/ZH8ccZJTupTQ==", "signatures": [{"sig": "MEQCIGjUzi6c8kify7vTKFEkxBhytSZdcTHuf0aS+eYSvX1PAiA1KbluyAlUswOQf7lr1evXQz7Lcp5/YdgQGG2JC7SKwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.7": {"name": "postcss", "version": "6.0.7", "dependencies": {"chalk": "^2.0.1", "source-map": "^0.5.6", "supports-color": "^4.2.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.5.3", "eslint": "^4.2.0", "docdash": "^0.4.0", "fs-extra": "^4.0.0", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "size-limit": "^0.7.0", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.2", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.0.0", "yaspeller-ci": "^0.6.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.0.2", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "6a097477c46d13d0560a817d69abc0bae549d0a0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.7.tgz", "integrity": "sha512-8h/GrGLLyxM5ZvzpCH2XTYPysaSL3Ku5kWD7tKXeKRj8NVg1tyldHFCQGF4NTvRUDvjQfmcCRuowHHFFlAURUg==", "signatures": [{"sig": "MEYCIQDeHG9QNitVBOkLSejW14z/ZNevPwJuOYGaKdlHJkZY3QIhAO247LXOZa6Y/RwBPLzKJzjqkrHk4mV5vNMFB6a6l2VD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.8": {"name": "postcss", "version": "6.0.8", "dependencies": {"chalk": "^2.0.1", "source-map": "^0.5.6", "supports-color": "^4.2.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.5.3", "eslint": "^4.2.0", "docdash": "^0.4.0", "fs-extra": "^4.0.0", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "size-limit": "^0.7.0", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.2", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.0.0", "yaspeller-ci": "^0.6.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.0.2", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "89067a9ce8b11f8a84cbc5117efc30419a0857b3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.8.tgz", "integrity": "sha512-G6WnRmdTt2jvJvY+aY+M0AO4YlbxE+slKPZb+jG2P2U9Tyxi3h1fYZ/DgiFU6DC6bv3XIEJoZt+f/kNh8BrWFw==", "signatures": [{"sig": "MEYCIQDrcDb+MXoelp3ycONg5rmPg1v0epFQrl0l7r5Bm6VVrAIhANxGYfArZuvNZX5rtmx/YfAbqIXUvQOg5j2wzwLDoWRb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.9": {"name": "postcss", "version": "6.0.9", "dependencies": {"chalk": "^2.1.0", "source-map": "^0.5.6", "supports-color": "^4.2.1"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.5.4", "eslint": "^4.4.1", "docdash": "^0.4.0", "fs-extra": "^4.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.25.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.8.4", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.3", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.1.0", "yaspeller-ci": "^0.6.0", "gulp-sourcemaps": "^2.6.0", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.0.2", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "54819766784a51c65b1ec4d54c2f93765438c35a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.9.tgz", "integrity": "sha512-bBE2AHNEBhF23TfET6AA/lFP8ah+qHOZoFJEflFG+HgvVLdTmMOrocx/4LVVDIn3w6jUssw1q2Exk1cc9UOI8w==", "signatures": [{"sig": "MEQCIBsbqI2I0PPTqDY+qjy8XPak/qrBgI2gJ0PRCotMn6B2AiBTjcH3cxpbenKv8saRF36nRO5uEjy0ZNeOf4C3PDHkUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.10": {"name": "postcss", "version": "6.0.10", "dependencies": {"chalk": "^2.1.0", "source-map": "^0.5.7", "supports-color": "^4.2.1"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^20.0.4", "jsdoc": "^3.5.4", "eslint": "^4.5.0", "docdash": "^0.4.0", "fs-extra": "^4.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.26.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.10.0", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.0.4", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.1.0", "yaspeller-ci": "^0.6.0", "gulp-sourcemaps": "^2.6.1", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.0.2", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "c311b89734483d87a91a56dc9e53f15f4e6e84e4", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.10.tgz", "integrity": "sha512-7WOpqea/cQHH1XUXdN1mqoFFmhigW3KAXJ+ssMOk/f6mKmwqFgqqdwsnjLGH+wuY+kwaJvT4whHcfKt5kWga0A==", "signatures": [{"sig": "MEUCIQCzBgqV1ZwQpSV1hBDUcOlALYxj+Wmi2qt1E8kbU9yCMgIge9OIM1bHYW7x/FmB/1s8eMpdOlARmiwzfM9YyDR1vFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.11": {"name": "postcss", "version": "6.0.11", "dependencies": {"chalk": "^2.1.0", "source-map": "^0.5.7", "supports-color": "^4.4.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^21.0.1", "jsdoc": "^3.5.4", "eslint": "^4.6.1", "docdash": "^0.4.0", "fs-extra": "^4.0.1", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.26.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.0", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.1.0", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.1.0", "yaspeller-ci": "^0.6.0", "gulp-sourcemaps": "^2.6.1", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.1.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "f48db210b1d37a7f7ab6499b7a54982997ab6f72", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.11.tgz", "integrity": "sha512-DsnIzznNRQprsGTALpkC0xjDygo+QcOd+qVjP9+RjyzrPiyYOXBGOwoJ4rAiiE4lu6JggQ/jW4niY24WLxuncg==", "signatures": [{"sig": "MEUCIGs99d9bSEUoPgNK2bMboSg0UukXP0xu5AARQ9nfZyPQAiEA9NFM4O4HSyPGHBXImzZrzTvjBcasDozU7ZhK8Qe7EWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.12": {"name": "postcss", "version": "6.0.12", "dependencies": {"chalk": "^2.1.0", "source-map": "^0.5.7", "supports-color": "^4.4.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^21.1.0", "jsdoc": "^3.5.5", "eslint": "^4.7.2", "docdash": "^0.4.0", "fs-extra": "^4.0.2", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.26.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.4", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.2.3", "babel-eslint": "^7.2.3", "gulp-changed": "^3.1.0", "run-sequence": "^2.2.0", "yaspeller-ci": "^0.7.0", "gulp-sourcemaps": "^2.6.1", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.1.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "6b0155089d2d212f7bd6a0cecd4c58c007403535", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.12.tgz", "integrity": "sha512-<PERSON>6SLofXEK43FBSyZ6/ExQV7ji24OEw4tEY6x1CAf7+tcoMWJoO24Rf3rVFVpk+5IQL1e1Cy3sTKfg7hXuLzafg==", "signatures": [{"sig": "MEUCIFvWYup+JsuJZaS7cFsRS/6SAtmzu7wfIcnHVJsVyvDSAiEAnUVrYrqPWH6u1U4VGsvM7GVnCEVtGBkGrEgqkddXQL4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "5.2.18": {"name": "postcss", "version": "5.2.18", "dependencies": {"chalk": "^1.1.3", "js-base64": "^2.1.9", "source-map": "^0.5.6", "supports-color": "^3.2.3"}, "devDependencies": {"ava": "^0.17.0", "del": "^2.2.2", "gulp": "^3.9.1", "chalk": "^1.1.3", "jsdoc": "^3.4.3", "sinon": "^2.0.0", "eslint": "^3.18.0", "docdash": "^0.4.0", "fs-extra": "^1.0.0", "gulp-ava": "^0.15.0", "gulp-run": "^1.7.1", "babel-core": "^6.24.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "strip-ansi": "^3.0.1", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.0", "babel-eslint": "^7.1.1", "gulp-changed": "^1.3.2", "run-sequence": "^1.2.2", "yaspeller-ci": "^0.3.0", "gulp-sourcemaps": "^2.4.1", "babel-preset-es2015": "^6.24.0", "postcss-parser-tests": "^5.0.11", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.0.0"}, "dist": {"shasum": "badfa1497d46244f6390f58b319830d9107853c5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-5.2.18.tgz", "integrity": "sha512-zrUjRRe1bpXKsX1qAJNJjqZViErVuyEkMTRrwu4ud4sbTtIBRmtaYDrHmcGgmrbsW3MHfmtIf+vJumgQn+PrXg==", "signatures": [{"sig": "MEQCIEFIsXkTlqDeaUnKZmfITJXQk5/q5sgTK4t/cYkrPalSAiBmThpqpDsJnWc1yONDNfsD7ya6mEWZJfcxqIyd5v5VRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.12"}}, "6.0.13": {"name": "postcss", "version": "6.0.13", "dependencies": {"chalk": "^2.1.0", "source-map": "^0.6.1", "supports-color": "^4.4.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^21.2.1", "jsdoc": "^3.5.5", "eslint": "^4.8.0", "docdash": "^0.4.0", "fs-extra": "^4.0.2", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.26.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.6", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.2.3", "babel-eslint": "^8.0.1", "gulp-changed": "^3.1.0", "run-sequence": "^2.2.0", "yaspeller-ci": "^0.7.0", "gulp-sourcemaps": "^2.6.1", "babel-preset-env": "^1.6.0", "postcss-parser-tests": "^6.1.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "b9ecab4ee00c89db3ec931145bd9590bbf3f125f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.13.tgz", "integrity": "sha512-nHsrD1PPTMSJDfU+osVsLtPkSP9YGeoOz4FDLN4r1DW4N5vqL1J+gACzTQHsfwIiWG/0/nV4yCzjTMo1zD8U1g==", "signatures": [{"sig": "MEYCIQDhlD/Btj0QTFZtmzVhZe1jDAdM6vi5gvYiD6cpjkWd1wIhAJEcYnZbgPCWnDavSTsuC/lx3Pokb0rnJcfBRdvw/bcf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.14": {"name": "postcss", "version": "6.0.14", "dependencies": {"chalk": "^2.3.0", "source-map": "^0.6.1", "supports-color": "^4.4.0"}, "devDependencies": {"del": "^3.0.0", "gulp": "^3.9.1", "jest": "^21.2.1", "jsdoc": "^3.5.5", "eslint": "^4.10.0", "docdash": "^0.4.0", "fs-extra": "^4.0.2", "gulp-run": "^1.7.1", "gulp-jest": "^2.0.0", "babel-core": "^6.26.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.12.1", "strip-ansi": "^4.0.0", "gulp-eslint": "^4.0.0", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.1", "gulp-changed": "^3.1.1", "run-sequence": "^2.2.0", "yaspeller-ci": "^0.7.0", "gulp-sourcemaps": "^2.6.1", "babel-preset-env": "^1.6.1", "postcss-parser-tests": "^6.1.0", "eslint-config-postcss": "^2.0.2", "concat-with-sourcemaps": "^1.0.4", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-precompile-charcodes": "^1.1.0"}, "dist": {"shasum": "5534c72114739e75d0afcf017db853099f562885", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.14.tgz", "integrity": "sha512-NJ1z0f+1offCgadPhz+DvGm5Mkci+mmV5BqD13S992o0Xk9eElxUfPPF+t2ksH5R/17gz4xVK8KWocUQ5o3Rog==", "signatures": [{"sig": "MEQCIQCxdouvPcPORxckLfxDcMURsW54wT1QQWnnJHIKiMvT7wIfeyIhvYRTQsRIepeA1qIfMtFbjRXD4SzsOc2suCo90Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.15": {"name": "postcss", "version": "6.0.15", "dependencies": {"chalk": "^2.3.0", "source-map": "^0.6.1", "supports-color": "^5.1.0"}, "dist": {"shasum": "f460cd6269fede0d1bf6defff0b934a9845d974d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.15.tgz", "integrity": "sha512-v/SpyMzLbtkmh45zUdaqLAaqXqzPdSrw8p4cQVO0/w6YiYfpj4k+Wkzhn68qk9br+H+0qfddhdPEVnbmBPfXVQ==", "signatures": [{"sig": "MEUCIQDjK+mQ/LDV/2A3O46dwYDdC0fMdt2fdu3tbXiOeVC6uQIgK9ofSycNkJqZ72dF7j91dA0Xjbex9MQhVjyvonaw2eo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.16": {"name": "postcss", "version": "6.0.16", "dependencies": {"chalk": "^2.3.0", "source-map": "^0.6.1", "supports-color": "^5.1.0"}, "dist": {"shasum": "112e2fe2a6d2109be0957687243170ea5589e146", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.16.tgz", "integrity": "sha512-m758RWPmSjFH/2MyyG3UOW1fgYbR9rtdzz5UNJnlm7OLtu4B2h9C6gi+bE4qFKghsBRFfZT8NzoQBs6JhLotoA==", "signatures": [{"sig": "MEQCIBs7c5CFfAeUQPDcFAMVtEnHNJ/ONDiM26TLbW2ZNgIIAiAgIYsOkgYlEI1hz/Mdio7lpQ0BVCgmzNw2udMZrt20jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.17": {"name": "postcss", "version": "6.0.17", "dependencies": {"chalk": "^2.3.0", "source-map": "^0.6.1", "supports-color": "^5.1.0"}, "dist": {"shasum": "e259a051ca513f81e9afd0c21f7f82eda50c65c5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.17.tgz", "integrity": "sha512-Bl1nybsSzWYbP8O4gAVD8JIjZIul9hLNOPTGBIlVmZNUnNAGL+W0cpYWzVwfImZOwumct4c1SDvSbncVWKtXUw==", "signatures": [{"sig": "MEUCIQCy+9PxsE+phaHDhOAT9CsPeinawsFq3xe0LDQcY2GeggIgEADsmvucLY/vwtQPcLte7R0Fc3j/B5Vm8AaJHQPy2jk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4.0.0"}}, "6.0.18": {"name": "postcss", "version": "6.0.18", "dependencies": {"chalk": "^2.3.1", "source-map": "^0.6.1", "supports-color": "^5.2.0"}, "dist": {"shasum": "370f5f44d47f3a205f0eb2f6262bbf202df2a80e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.18.tgz", "fileCount": 36, "integrity": "sha512-X8MyLi3OYI1o71u0SsefWLpGBo5xnGiK1Pn+nrZFplc671Ts7L8aPwEbPIO8AWpulK5wuaVzyM9Rw6R8o7hYBw==", "signatures": [{"sig": "MEYCIQDCHqBTYka5fS8L4+GE3yo37r2meMhZVBARJ5a+PxY06gIhAN7SLIheAcLZzt1CsTIOXIsmBLe6qZ+PYQZdEIxaXFEk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 643909}, "engines": {"node": ">=4.0.0"}}, "6.0.19": {"name": "postcss", "version": "6.0.19", "dependencies": {"chalk": "^2.3.1", "source-map": "^0.6.1", "supports-color": "^5.2.0"}, "dist": {"shasum": "76a78386f670b9d9494a655bf23ac012effd1555", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.19.tgz", "fileCount": 36, "integrity": "sha512-f13HRz0HtVwVaEuW6J6cOUCBLFtymhgyLPV7t4QEk2UD3twRI9IluDcQNdzQdBpiixkXj2OmzejhhTbSbDxNTg==", "signatures": [{"sig": "MEQCIB4l/yWuURA0L3EHhuhz8m3Jeu5bflfPyamCqqWd7NDqAiAQp9gWXCnnfqGLpDdnjUnS5wPYYutMWsfVlM6bWrbpiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 644037}, "engines": {"node": ">=4.0.0"}}, "6.0.20": {"name": "postcss", "version": "6.0.20", "dependencies": {"chalk": "^2.3.2", "source-map": "^0.6.1", "supports-color": "^5.3.0"}, "dist": {"shasum": "686107e743a12d5530cb68438c590d5b2bf72c3c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.20.tgz", "fileCount": 38, "integrity": "sha512-Opr6usW30Iy0xEDrJywDckRxtylfO7gTGs3Kfb2LdLQlGsUg89fTy0R3Vm1Dub2YHO7MK58avr0p70+uFFHb7A==", "signatures": [{"sig": "MEQCIFFG/YNUeDhcyzoCzPoaKKBxesvxPBzyC//bquUNSuWMAiBSA6TNd5AMX3YuANkvp18ZjNBJCJ3Duhwm6lFGH+OEDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 656004}, "engines": {"node": ">=4.0.0"}}, "6.0.21": {"name": "postcss", "version": "6.0.21", "dependencies": {"chalk": "^2.3.2", "source-map": "^0.6.1", "supports-color": "^5.3.0"}, "dist": {"shasum": "8265662694eddf9e9a5960db6da33c39e4cd069d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.21.tgz", "fileCount": 38, "integrity": "sha512-y/bKfbQz2Nn/QBC08bwvYUxEFOVGfPIUOTsJ2CK5inzlXW9SdYR1x4pEsG9blRAF/PX+wRNdOah+gx/hv4q7dw==", "signatures": [{"sig": "MEUCIQDRNuEm7/qjaMRUQgjgFY/Nhixn68gU+MdJ4Bdx3f/4lgIgb++IHnm8CTDYnmgM0FqKICYMfibZ1WKMJHWPHAFaQS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 656065}, "engines": {"node": ">=4.0.0"}}, "6.0.22": {"name": "postcss", "version": "6.0.22", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dist": {"shasum": "e23b78314905c3b90cbd61702121e7a78848f2a3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.22.tgz", "fileCount": 38, "integrity": "sha512-Toc9lLoUASwGqxBSJGTVcOQiDqjK+Z2XlWBg+IgYwQMY9vA2f7iMpXVc1GpPcfTSyM5lkxNo0oDwDRO+wm7XHA==", "signatures": [{"sig": "MEQCIEv/wCHjmPfpUkwF8y7Nt7m26O4aONm2OCWacSc/oF/EAiAlo2Qoe6uhsozVh720XV5yNTWuKz5Y/p7CnfbTjChzBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 657640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5ATSCRA9TVsSAnZWagAAS3AQAJmJHq+deUlLQa7ixmh+\nXMS6uM/ab20xx0FieHcDTp9DMIbivVuts1pF9kM10hq1d9gMh/bfMtglYjYK\nK5XckXNNqs+igtXW95ZFjpOYl7/Zobkx3Nk+kC4U9kFpuY6Q1ytO2m5nS8Pe\nFfvWerNniYPpRkrvlMUXmN1SPW6NUb+hXWR+lWbYn5a0w/sgr+zE7PGd9/xM\nW9B5THonikumND+YyQ8QuxNzLHPbCpMCdFrkOdjw8Y1Wxo4k5foh6VyLyDQp\nwqt0g9GPAm/AAVR6jsNcwu7Uvd+RHBK7puRdOCZsPDig1wORQDqd3ayJHPbJ\nvs/1zRhlf6gVAJ3/u0oGjoyDZizyTJPXSYnzEB0PySZOlwkjtAS6LBnSkyx3\nUoNYK6u/JLpsawcdeF/qq4oLKvpKsAG8ftZJYLk50k+gtnVjEQMFvrHw9QQD\nRdOXZxCG7DRsVcmJ54vrUqEvyYoHThxeKwnha6fMDTwjtrKJJhwTM4r7RAoY\np2vaNRxaZ0dAP/CrrGokvP3XZg9HkZiOy4/vnoi6DC7LBQFSNM/Qlh0YlkqT\n+WMOQmlqGR81NNU5mAPmLZyfxKuYmSO4TgFZPG5SZW5ynABlKGoDljmMXYw5\nx7iK2sBehrz5HlmGF4orexWyhL3UTz4mgUNuKGbLidKSCI5Ik5rtt6UJSLcG\nQy/p\r\n=Ak8A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "6.0.23": {"name": "postcss", "version": "6.0.23", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dist": {"shasum": "61c82cc328ac60e677645f979054eb98bc0e3324", "tarball": "https://registry.npmjs.org/postcss/-/postcss-6.0.23.tgz", "fileCount": 38, "integrity": "sha512-soOk1h6J3VMTZtVeVpv15/Hpdl2cBLX3CAw4TAbkpTJiNPk9YP/zWcD1ND+xEtvyuuvKzbxliTOIyvkSeSJ6ag==", "signatures": [{"sig": "MEUCIQCB4Yy+KIVutVnNp/rzx6eFyyrOswwGeeac4iK9raciqwIgRK5JcvNvg1tfmpCZnzSijR8nFEnAL2PwpOYWv9OTwRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 658430, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbK/HOCRA9TVsSAnZWagAAPzYQAJhAPB3anXHqUf6HSCAX\n7JKqzwOY3yOu9JNtHUOz81+a22qshkQEaBkwQ9z8Raq3lXhUaCEbdF0Uf1qf\n7L0Ekk0Xe3jsGMQPqeb0kuZjEwWhP2AJmw8klvhyhF+iIV1b85x0hvKyG47v\nspPZ1hxODPVxb/RSOZIam2d7orZrtrFvOI57pioudlod8G1DDwXE5nWvc3H3\nFYrWgQEVePhqfgNlDqHiC429Ducr6kgdNUdvJMM0CBRmPFWqgNHyoBnVqyD0\n5SoPmkgM+RGs1X1+b6AT5293/BslIdPtXkeWTdPMzRIb73s2PezjCNgt76/1\nycT27OGnjuhUKtLOJQdZIevdXNX6bMcuCqPrLpNyHo+Nc2X+0cJFslRNmuqR\n27tT1vPqndF9ASeXdx1tQZtPjwT70KvjE0TJN2d+SDp1R8jVm5hTVf4Rl+sg\nagwTRNexaaLfiIkV/mky+zHxm5mGP57jkzbbcr/JkuNUbBGJHaSbQgG2czf3\nABinbEkats9kssoIQpBkF+7d/Gp7YFBNEgfXjCMqlrx1LP9kuspxyS+BiCre\nAx4qx+qJPApDuddSfs5akfcFApf27iE4tV8mYcYXn2AVbIe8xlbWdDzYEu68\n7lE2hSxGYUOEqEfuQQMPMzm3PidC8+kj9u/T3VUKAqd8Vtf2ts4XFXeHRa+P\n4hMH\r\n=R4OJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4.0.0"}}, "7.0.0": {"name": "postcss", "version": "7.0.0", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dist": {"shasum": "163f70d2fe2715c6d4a4b3d300051c1ea9594aa9", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.0.tgz", "fileCount": 37, "integrity": "sha512-ACgL/mXREjfCothsspfbxdiXIQowQeEyW7TJgsvAgCK8igWa2ZNCsbGNGnasbEF1++L9xb7qYpo23Aa3rGmiCg==", "signatures": [{"sig": "MEYCIQD867MOGmEBqL8TQ9NsFCAGEywfOD+M4mqkPNUca5DriAIhAMCYqPYnbu02uf+8ujGb0c/KPl/jndBoFsSyztNhe9WA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 606560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTOwJCRA9TVsSAnZWagAATcAP/AirJ7Y63zm+z//UU9qg\nz+CBnLxSbdYURuuvFHPZsC9mVH/kbPHwZ/sci3yLO1+Ub55ZCWTIUqARAzFk\nvqthKmj8IL3KJ1EbFqKirby4Se6H85aNJCuYyZO+blToFBKjRJNwCKTeI4DQ\nsAVsQayyC4YjwCVbp+rOZMeCn7VssF8ceuVzM1UXAItWzkTgF79iQ8OPNmz9\nPV/kMBQCiMCmysMx5xvxEvmFR0o3YKUzHv29MUZGQzAOWODOeE4n2siG1kda\ny3AOwIitNrgBL1InS814IfGC6AgS/D109t3HoLzK96aNcLCgmGyIIpZKMJ7X\nSzYChe3HpVLZLop5YLlutjYIV0szzEig1VswkRmHie7YBVCg6J4i2fZ129pF\n7UF1hEBwa0MbNq2s3L3rgMkXidGkBu0hoeK1zV51ABIjfW8u7cKTu6e0ybJZ\nqCAFRcegSC5qndFZOyi9KmUGKyBGkbPM8mFPAsKVxnZgf6EH6KI92XfuJbRV\ne7YedfCeN9fo4GK5D880bWy3L1nIAfdU0MUTHHS/6UnUU2v2WgmFHbawYGDw\nhvGVKyh0ioOapAQjku9XcO9jOBV3ysIo186g+ACmZvOISfyFqX1lLXn4teLX\nHAj1JmZZ8VNj1poSjS6CNd0eEhNPXsFnSmihUVD8kDOVUpHq27CJPvKdGfuV\nBnrh\r\n=M+UA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.1": {"name": "postcss", "version": "7.0.1", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dist": {"shasum": "db20ca4fc90aa56809674eea75864148c66b67fa", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.1.tgz", "fileCount": 37, "integrity": "sha512-c6M68yZX0bWnZ0GcX8duWcfweGeGQvYgw6w4xksRePDmrpCrLMqneN07xwce17ACWBAr0S+DoI0T31axZ21TKg==", "signatures": [{"sig": "MEUCIQDV2iBkQNw3/tIYE16ZBcF0fChZ8ggl/nLiwNtEjRrj+wIgLvdxAIABnhSKp9LOdOn2I3Lpml3BLsy9W+p1sSST1NU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 607305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUU4mCRA9TVsSAnZWagAAX+YP/iKf1bOq4GXVSKPUONbJ\nusR8QA2shvG1DYkBvK0C2HdgOu3tD0W7Ibs3wVA5kljvKVIhSN0DKype4kv/\njyJ+KTkIJEO2XXna3/vZSu+au75HIYMcJE5uWHo01wuqxYN4JKF7C3h2PNsk\nHTNQzJAPD5OitEbNfcebTtJN+dWjjlZBXpSLi0XyD+0+YpY/iGsEp8KPiE6s\nUNQcITk/hdq18CiNCv46bsKzmMhZkhA1V6Y2Ys+UYmAUq9QXoxrvNbJdO72y\nQryyo4q4deAalx4FmrcXu7co6OfEsYnaCxtNcdExV9MBTRJ4/wJn2GLuV+nz\nzGW+SU/7GWQ0jezuoWbF83BRnMllGJkgS4zNv88mOb2yDXkhEceps7cIxsmZ\nsMyciKT8FBur2tzSx7RINaoRhwPVubrb7duYje4r1KjGdhEdXjRITo4BAdGu\nT+NutlzhyG/7/AVUIJIOOcPio3FHzTMQ/sE0bqBx+W24g5hCSNjWl7qpGB+M\n+ITOH7WUfnsufF051SXOsqz1FwUNbKabb1oWRYqjN9Q4AD6u14IAO7gXiNf8\nuptZPWV7Z7TRINCmnQglEhzUGcISGPZFZWFqYPixcxy1Og2X80zOEk4alcDL\n6g+aTi4HE96uSROzCZJG5sc5eZKTq96pzXS0aUNdLAtnAxU5KwnZ29nU/x/r\nfD7B\r\n=0Vfb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.2": {"name": "postcss", "version": "7.0.2", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "dist": {"shasum": "7b5a109de356804e27f95a960bef0e4d5bc9bb18", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.2.tgz", "fileCount": 37, "integrity": "sha512-fmaUY5370keLUTx+CnwRxtGiuFTcNBLQBqr1oE3WZ/euIYmGAo0OAgOhVJ3ByDnVmOR3PK+0V9VebzfjRIUcqw==", "signatures": [{"sig": "MEUCIQCnbXOu/vJPeDmi6ouYSGDnPAQCg4R5l9hBJnjCMRXW6gIgYp4HBh/Hvgyg39brsVolZhPAwEUhM68VsYWJreBXgIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 606775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX1X6CRA9TVsSAnZWagAAKeYP/1O+D93nco296sQzJxS1\npXVSBcHxtdA2HBnEF6LZloFJE3HAOYSeqhmduu8XIoKAF7pcWefgAYcleQkq\nywm7cDt+Pp5TUrKEs+OeskVJAugtD+D6YW6xyPkx2YmIA3ILWESxwTX9ptGz\nM2mnE3bGNDcVwhRQJXItCSVnGdrE9npC1x4BsZEXwHek+efg/ng1d6Utviqi\nc/TVvol3eafzkR7uEIrcJNNwihamAKRepp+rK9OUK6ky6Bkm7h4goSSKFqNu\nUAxszYKQWLBtQ4sjzjNBhOxaGBlHjzZz9YzcM5Zmu3FPUYUYFqVFmMRIxh3G\nP6gYTOiybhrdTtI5/DmAVSnNrxZg3ahafH4HqpXAsFpZCw8TZdR36yFMFlA+\n/V2Iq2iEoLxby+aOn7cIzel2D9Ripof64JwPXwhAC7kPtJScsjHPMaAwnQhW\naeTJgTGelBv/H7SOPsGFLNZDUVNfzeIYDFHZpZVO9tLToDRQfuQVhg98oAhp\nAhHXidXfQ2MfYTsNgrkZo5CXGiZSUUxqCbDjTaPw6skHJXTPu4SCrw1+/2ZP\nd7LZWS5J8YRsVOmD/2JoDFKJx+03OHjUmXPnE4ESUREPzgPpmmW/E7z25E75\nVbIyi+TNI/1s0rBwq4g9a+TvFRjgOSrUcaxlOhA3zqr8PSuiQDx+CnOZlbK9\ngVqp\r\n=IGOp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.3": {"name": "postcss", "version": "7.0.3", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.5.0"}, "dist": {"shasum": "449779466c944c9ebbfc7ed7c871c16c78a0ae46", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.3.tgz", "fileCount": 36, "integrity": "sha512-9ZZVMgSCRc9K00MEIeCRhC6xsZC4TeZqLybCbvZuFcdj1XdjW7q4OG8sP+Mp71JTLBB3srMjUqcgTMFlWjIGaA==", "signatures": [{"sig": "MEQCIG3YItb1wagKKg9ytJQy+/nHBZys9dRwxXnZJF9ehmsgAiBclIizA4hqvqlFLg6oAiDpHxmkqMn/+zuo65BRLJr58A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 593601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbqrS1CRA9TVsSAnZWagAA4eoQAJtIvlURbv5qVpMh4QDR\njQprjtkTEtPqhsQNjPIAjxZfJC9MzQjuBINUyUXYmCkfB1mHiKauX7iBTI0t\nCOIeNmAeNccJZeirWs1pTQEK9AwqOgJNHokJfpTChqcEobB72/PeZV8X7oUh\nWr4VRE2KLvp3i7uJ8ryPodyRaYJUzT05+gCnrzK7UyAnZxU72B8G1CgWV6Gd\npyr9RvDYw35W54lSsQlWKXnM9BTseoSfv5GlafESJm7VsRDahrLYDJFC6hUJ\neIkXrJ7/rUAOAMTt2zszx+lID9/PIcigI3uYfqqMWEYh9KNOefVXBVmtmB0b\no1MWD0o56mDB0LJ9R2DKQCp/9QV6z35WGpzJ9SRyeFdJtscWcp+Ax8jD4XMm\nCAcDHzyRSr2mcXHs/1tMaHp5Xy5rigcqLV8Tt2b6Kk9BVPOWI9Gdoi7fERrl\nSyX5exNngJf3+F5kK7LkRXkiyqaB17WV4ogzWtnzZluiGIFNv6EqbkD3bEWw\nugVeI/X+pfOP9uhcI1+qZ5J0Q6NmgHe5JYlbADEIalSw9ThuFd9+UWisU9pA\nqA73uSMfLcKEdGKkyq9D4oZsBjQUthzKlz531tTVXy9JpLwS6RiikGyCTCIg\nwDCNk9bsBE907/hKknmKeDo//bU/Ux2webcqghB8HkhS5RDIk7YfG9SaC3NS\nWMy3\r\n=5RoN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.4": {"name": "postcss", "version": "7.0.4", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.5.0"}, "dist": {"shasum": "b5a059597d2c1a8a9916cb6efb0b294f70b4f309", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.4.tgz", "fileCount": 36, "integrity": "sha512-Bg1BrMZGKNOI0mkn8NtjJrOrZKgoHrl+geKJ45mTOkeY4WCsYq/mjd1BUWRgRvydHP/lA07Ys2n9m6Va5FsEsw==", "signatures": [{"sig": "MEUCIA8fcA2e7LGGs9Dz/FPOtUpOnhRHIaCmmAlR2vhLOn66AiEArM912QxnuRGw2R+1bR50VgYOegUSb+JrvLyyE3/5reQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 593703, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrHYBCRA9TVsSAnZWagAAHGQP/i1+pOMW6EQkUINOmqfa\nxZ21A3r1HEtYVgbcUcIjPbcbSQpaXSAQ1DDq2ifXYUSYVNhjnLwa9cUIkhcO\nfbcxkNJwCJXYgKaEu3xYug8gQUSYwdxaEbdni1EUkOiq2ghtiJOSs+mJphKo\n+n07JsGZcDTIP1qYvCGiHMGdcogi64JemqvZD93RIaH8raMZqx1VixgVvAwz\n+z8VO5LmQYq1mK1e+Ct6kiETUajFPZcHAWYuS8L5S23K+EEa5jxPm5ZKx5q1\ns5BR0wnoByQBW7NQq0j2UI5G9CsTlcic4lSxqY81TpgwWA5735sSzbypnuvR\nqwNfEXZEOfs7zfgNAr06aSVDfFac+y6VwxvGqjC7SxMhn/DjVVaRSw4jmJqC\nlcxyQSJ8lqhKJsa/cKl7x4cjFyHVUi3D9xZbLK9RlTaENJfxbZncPVr7gW8I\nhwyUawhmaCt8XtxpUaiAivvmUYDFBPvGIphBuWDc3uF4/mVHoOOWXjWSWn2z\nW/SgoBJqMkkeXJmGmrcN6Fvb75nI7lAz1WpWeBNpzYy0O0TCrQXINKh6txrD\nK3CZLK/WEhrK1c6S+c4Gac8G2u/n7+vLSJfW5BLPgKOlw+Co28AEh4uFFo/x\npeL9buqiVr9nJenK8M1mCxmLWoMXMCHaBAIMVfHfkOrlWE9GU5UQIInOJjGG\n3bwn\r\n=Vv4j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.5": {"name": "postcss", "version": "7.0.5", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.5.0"}, "dist": {"shasum": "70e6443e36a6d520b0fd4e7593fcca3635ee9f55", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.5.tgz", "fileCount": 35, "integrity": "sha512-HBNpviAUFCKvEh7NZhw1e8MBPivRszIiUnhrJ+sBFVSYSqubrzwX3KG51mYgcRHX8j/cAgZJedONZcm5jTBdgQ==", "signatures": [{"sig": "MEQCICsdlAggslj8P8TLQCqIAEltVqDkD65rjLO+B82YPZMvAiApuJC3yszT49BqLg/wCtfRo4U0EpKsg31eqb0zTIzPQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs29TCRA9TVsSAnZWagAAW8IP/A7oNAY+dxvL3vLKqVDN\n3eiRKKTd6U66+HySLcRxe2snHxumjOcZgaUZghugLizxfqKOcCVSBosbaFEY\nNadfHzkVb6YxyGGx7G8YHWWk486x8U+TTfIeXQuMOqhl17f1ENj9BkIsY9vy\nzHvXuCSstco2koFAoPuiRvxlojUPWLHrclIwzpsjlqhFTQyP5SomBMWOSsNG\nppUBwDgPgtrA84jeGVTnJw/Sb/iBJ0luAKBRtcNiKjfWNVcRPPOOcTtDtmfg\nQPHqmiqNIVpr50Wd0/kbQwo0vaBAR/zMVOy1A4JpmYGIJxnXaoiNJl1jf9Lr\nTDDnS87mupt3GWcG4XqATWmGjyRSTi0MeTkv0uitjNmyDVtKgtCP64sPSBmM\nrD+uwj0akylcaiDR0xwFmx4IWhJGHciBdrmcW4/s9ClPDHTis1/uwtEKIhaz\nW7UXnzLlmHjD8gvLO3m0UZSPeN906HoTatIb4Ns3f0C9oBnv25WcqXa4GL0/\nlCWXD2Fx1ug4AW1Ssvhh10XipiwcT3JCvlE3YGCarnkcTfckODtBRgzaGGbU\nRvBZrNoWn4Ss5XAbTea74lRGBgGL7PFiDEa+RL9VcUxidk/O6SF+6CD+Gnse\nvBvUzuPgHxkz66rXQTk+6iUvBBjiC4kJ+rlVxLEhhkP1eoFce+ZuyVOyYA9q\nukao\r\n=9/Q5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.6": {"name": "postcss", "version": "7.0.6", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.5.0"}, "dist": {"shasum": "6dcaa1e999cdd4a255dcd7d4d9547f4ca010cdc2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.6.tgz", "fileCount": 35, "integrity": "sha512-Nq/rNjnHFcKgCDDZYO0lNsl6YWe6U7tTy+ESN+PnLxebL8uBtYX59HZqvrj7YLK5UCyll2hqDsJOo3ndzEW8Ug==", "signatures": [{"sig": "MEYCIQC2wK5giM6hJisdcd/imR7nXzQJOJZwe4tQ0YUlPADSwAIhAMsSI+bzY3sgvGnr5Jsz5/5r+w3vxu4bVSJv/0cFa+oM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 592637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8MG6CRA9TVsSAnZWagAAwrcP/3+YDOY1E5JzFe9cnRaV\nMzbTe6qRiWn1cXGO0V6768RBOOqt9Rpjn4ldSez0XBHnrKYFYsaakJLl82YI\nqJQUJnCqrWWPCb2T1ws2z8zuYqVWPWK2mzd+nnpkGPdkDqLkTuSnUvbM/vxn\ncSwFt0fWNcrgUKuHOFBlYNU/ogGSswXCjA1wHNhQl7SjTF75dMXbgOupMKGs\nLv9oy/m+A3nLFQTeBoa1H8iFyXQCX8ZSF3n44Nvf9ehxpBMc6n80XUxWCwyn\nOIlTtLYZCILR3mwv4143XCA5uCo+i3ej3GOZ26k4dGVFP4iC+16VwskSuK72\nk4yB/eVDTMp8shzOtKSGfExw9/eQAFZlJpTyGPxJ1cjLg2w70+mA9LIV2UBj\nZTvl+7YOyacWbA84SNrlGbJI9AA//YnSJAhCee8OtP00Z6ZRGgy7TDC6XyZc\no7MqxYn53+1oudj0UCZ9fceVfohqltM7go/NByqPEFuTT3YV0X5LykzjQh9T\n1nkH1xNDn4tmeNB5GJR9Zdojx0imyKkuKNGUYiSsxbdnPwr+s7m0T0ki2p6e\nlWkqXZQ/ifFhHVaPAWdCfoiQOyNa1+bOXw7HBmsyiK9gEYeJkymh/rYzHSMD\nnY5M4L5jeS13CqWjRLBhbszoxBKWcLDcTo6XpzJ87KkBJk/Y9XEhmiRCGimf\nVdYv\r\n=lSte\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.7": {"name": "postcss", "version": "7.0.7", "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.5.0"}, "dist": {"shasum": "2754d073f77acb4ef08f1235c36c5721a7201614", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.7.tgz", "fileCount": 35, "integrity": "sha512-HThWSJEPkupqew2fnuQMEI2YcTj/8gMV3n80cMdJsKxfIh5tHf7nM5JigNX6LxVMqo6zkgQNAI88hyFvBk41Pg==", "signatures": [{"sig": "MEUCIQCeJgGZY4X8Tq1JqMWofPm0KRsh7npJ68pquWfvrMGLwAIgLh0PA0HOY7acdCD+YMPjY+sxbpONXwe5fto0H3HH3KA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 597437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFwQjCRA9TVsSAnZWagAAVE4P/2OA262Jg/OODNtDl5Gt\niK50km4TxwiPOsxcTVNprYp/vcA0uzYdHMlruW5kk6dnMQDttPssOZEYBd93\nQ47wBBY52GyEeN8sTaMnIbSnNCf7z6FEObaPtxcRIo9E3fhTBqbIk0uGghDy\nTdu50RuwcZlr19Lya8SisJFBWzY1zVJ3Qo/iRE1Ts1tZ/q9duO9M8HPs3PGL\nTPD8Ka8Ras3OrGEISQDlhV5Plh1PMB+NX2XCrirPUJC2mA6LnNwyD2CaEMYt\n+6zrALQm7gNnSZ2EklJFYbpJPOTvF9kzGsdUb9VR58NOaLJbT90D+asR3qTv\nkKj9FogUvE4tPuThX7yv8txZG3mDYkTNU7beacUJJfRDRICck25TZ03Sf1i+\nu/9PhV8w7Re5rsNBG9iHwLOvgFpl6fH4EtVv6ggk3h42mT/VjVtb3ALUFPGd\nX7YqqFnQ+BCtdROr7yTju2tsQi0yVUOAppHSRBVr+OIDUaw5gY6+T/NFTY5m\nOzBkGtGp4TEKonMBuVJrifGTQPi2eTgY/eqLd5ZiNVarBGGIdOssJ0hgUBAv\nD8I5IVX9cV90bTrjmpV+vrhBhA0KPNFsIIcrpVx7RFVMIyeguP/agTiiJyl+\nVrFkdGA8p8A5LOpuWeAFHFt6BWEw+pe2W2N2rFK0zeE5C/+++kPXindfbGJr\nA+An\r\n=YPMk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.8": {"name": "postcss", "version": "7.0.8", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.0.0"}, "dist": {"shasum": "2a3c5f2bdd00240cd0d0901fd998347c93d36696", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.8.tgz", "fileCount": 35, "integrity": "sha512-WudsIzuTKRw9IInRTPBgVXJ7DKR26HT09Rxp0g3w0Fqh3TUtYICcUmvC0xURj04o3vdcDtnjCAUCECg/p341iQ==", "signatures": [{"sig": "MEUCIHRhP9rmQp24NarbvGCrFpiZ3AsS5twVrmUZeI+ySTolAiEApecjTZ7NWcKIEyGGBLN8uFnMJBV0yNSiIEm5zViSUQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 597684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNJHqCRA9TVsSAnZWagAAisAP/isOgRuc/ICkM5s8fGWj\nRzMAso9JCg9r64DyWZcS1JOsD6akKIAUQTZgXvL3r+4TlEcWRkGZSIvMpiF0\nQ79/wpSk9TZKvasTgshOXGRE3zUFJkxTSBnYU+yA9xv/GuwB/G/U3oCiiBgu\n0mmNrO8Y4gLXlwjZ10bSSq+9OCaEqWGh2AWMpiQfAYvu+wIuCKgecbh8nX6R\ngX9yfMYH/c0ns2AdmviK+NU9TzCHyhBT9ejJn/29x0zEHtxSxhDP58XFQMVl\nLX94mTPmzpF22dTeFZAO7vNNFWOsKw9hfOExlXdNw+wUnwhUPlAncMgyhmLV\nW0NIIjrtXeih5jVsCGJNBUryYR/3Rv8BSFry6o27vgvnBWKD6mTPk00vwdy2\nWgsmpR/lV4VQy0oZdWKx8E10aKZsLcBPPI5CyMjuehBIX2eV1FV50R8kKB+c\nQ52DP/nwPmv2tmlGM//V1NJfgefhy7E16m1HBZkOEDCqVyv1Okf452sp1OhI\n7qbo31GtV3tcKwdKjtIBRwFZZF0bnmAQ/J2rQ/QkDjh7OVF3DX02o5BDa34L\nuKqbG4X/zotHajQ7uOqYeNZGZtuVLxGJ76akjtU3xSrohwxadF/54P4agfwY\nw8PiyZCJrK7op4bH2IauEoetr27XyKFeEqoaBf4nSySiSB20oD+F10jlVvm+\n5MR9\r\n=0S8E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.9": {"name": "postcss", "version": "7.0.9", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "e35e450ce112810a5435ffe49313a67186a6fe1c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.9.tgz", "fileCount": 35, "integrity": "sha512-eXB2Fm8/BtSABq7ia1HyvbkoD9zFqq2BWjHUAyRSgbK8qdyKrA6yMCX06l05Onc8bHemeXLB8hzJ8tM0ABc0Zw==", "signatures": [{"sig": "MEUCIQCtd9eRK8N7IShZRR+GPoAvsHsav4m02e5KSZJehzs++QIgZG40mszuQt1cTFZ9BhmCq7XBYQcDNb8Y4ZEUdQBqbuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 597835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOKXRCRA9TVsSAnZWagAAYjMQAJTD8d/HFqvaluk9R0ad\nQPjBq4uHbUp52lPHxkD7ElepEFcmK3i3T8SwG2L8/CDrvhbhlNiCRJu8aoR9\nwx4c5i2oaqLolpTAW5DKGMKCijKFnNV8vTu7CZoOBHGQjb4jXup/h/IhuHB1\naTpZFuE4dAiEcosiKGVl8rQomzkoc2GxXKp86n2BJ4JJZoJ0Met1S1C/IvuU\nTF6AJt0XyZJRapdoH/GAGTnhVbvi2NKkdxaq4VtOz26fYjaOSTNWThpAV88h\nn0QicEStnJdgdwIdpX9DDlht4KXonBXeqpxmlIPnIs3T3WNWnCXcjx/NrHbm\nX11gNNFsiEFq7CVKD8butRA7XJns6mPXW/sUg/5dzNFxtWD01ca7pqyari/0\nt68VJ4m4kmX1ikHsQ6XRvw6j9GsNmXiazWB6OMWafNlumk+KqVuElLHlCkWa\nypwfSqJuaZpLPSt7E8ZY39tMOZqyuNgTMrMxLaFhOY7mIaTIoniomeikd16/\nyStTV0yhDwiCrUf2hVqvK8W4+1RocHQq7LDNn7x6RmAQR5tvTnIGLnfr0cKW\nHmBF6A0yay9MhFHNqvCe6GEB6bXf4ibrjlUc54oWlyVGVSWSXpMSiH5IOjhG\nzN2xbTqgCArDCdVSlBq+eLD0IGp9ctEOfwwy86dUGv082Io7jRDdXZQlan0i\nlMgF\r\n=TNXD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.10": {"name": "postcss", "version": "7.0.10", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "a1f44cfe3711649190989a283024921e33dbc49f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.10.tgz", "fileCount": 35, "integrity": "sha512-wuaQVkYI+mgMud6UwID/XX9w0Zf1Rh/ZHK0rIz0o08q3rUaPrezdl/oJD9aWma4Pw6q7mgRBJQS4xA1yZAHptA==", "signatures": [{"sig": "MEUCIC46xJ0/XS2OMOrIH3ljCles+vDJ9xqglW7I8JmRlLpoAiEAgp6M1TVcki4uB0PEbT+iN0bgvpIN5xGuTFfilMUbiBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 597915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOPlRCRA9TVsSAnZWagAA5NoP/0o1yKoOaAcb6Uwdywzf\nKoaIW1GMq6rFzlgZhlUomrr9hxeI0Rw+LB6IbMShyFYfN3ZgWDHILX3saSAt\nn7bcbUIH8ag0WapktgMRIA4KU3iAUpHmNhpxTDSqWA4CfB4dSmYLlV742jr8\nJTUB9Fj20mdWeh11YpOR1uBj89p1X/5atyxcB8faNJTIbTmxTynt9f2PlTNN\nGogndsgvCrtOLz03249qoyPJ4x5k7zu7jI0T7lKUeZqDDkgNoqiBST2/fHWq\ncGs7bJmOe9qz8NGfnnKYeDi5W8szaACFMoYnZjFaMxWMqMsOJrah9JJd4L6g\n+3sIijos9nmR/3oYctOKiXuXHRb/PjTarI6Fq3NV4CG64NyMyxoJ8S+T9U4H\nN4U3xjG+SfTL8MRBq5oSctYH/xigMWBHElFq7Xrj6ohdulN+BWQ/Z9kwbR4D\n3VAzivbecD2PCCkxcvPofmzh6K/+VBjcFXZvJkJDE/Cop23MUGpZT8q+LKrd\n2JWZYkh0tkSLBJUVwt/YrtqcDEwTd3wxLGNFbT0RvVXVgV9TnA+A5d+y3LlG\n1OFaYMrciPhVkw+yi5YN2lx9n7mhFiuKQljutcX7N4LMX40WMdxruPR6X1lE\nbNh2t8xTbWHeA1JoSE/M3NyWML06/1BM0Qfan0CCK/6O3ore8bhqYK2ab6jF\nVlM/\r\n=1A/6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.11": {"name": "postcss", "version": "7.0.11", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "f63c513b78026d66263bb2ca995bf02e3d1a697d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.11.tgz", "fileCount": 35, "integrity": "sha512-9AXb//5UcjeOEof9T+yPw3XTa5SL207ZOIC/lHYP4mbUTEh4M0rDAQekQpVANCZdwQwKhBtFZCk3i3h3h2hdWg==", "signatures": [{"sig": "MEYCIQCbpZ6l10O4ouC4Hl6MymzxW1AFHxDNy/DCz8sOBWD+9AIhAOIqa6xdvZXNYvkOFlDKurp6K+H7plYm/XLcvo0RND0T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 598737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOhJdCRA9TVsSAnZWagAAtKIP/0rz+VyLhW2v6sAHVP2I\nmTPMAjLetuxWqThDIuNwo6+aY5CPEzruDIQ28NamAqT4mYdEb4lTALJtkvLD\nK3qRnkZalAQaqfcBfr0+8yLzaRr3oBlLqi4hw8/tvwMltDr8qedK1r/pgWL/\nzyJXVrKIoaMTKlieUcbrih8Sxv3Tep4qHounA3RvzEv76MvEP+BdrhDKTxRQ\nHi5vkUGGEMnoIbnsHu4JtJfx9Y5ydA5rkSbWfMvcehU5lX23GLdn5OdMuKu2\nLfgajLM/BDOsjo3v6SvoIinmgGO3l38TC6sjH+TtYDXEhtpzBRCrXPESeHnQ\n4ZWwTcv05Bd7EGrcdIft6M/W9+Y1x78V+0fJRHKGYr9AbMn1ZvNmgpajMgzE\nJUl+/2jlQYi1VK3KGZGjUtEKAvepPSQPEJAwa7l3Ag8ehu7S0JAQJ6n+horg\nS8NIDdKcqfKJ7aOgrYGOn+ogYuKM5y9ytJolTcXv+RcR4xe34gPRnLfw4V7i\nmHX3CcUFUq+1QMsYukQs3j2fWeSSNePioUgkvORmX+V11gHV2fsdW+1rIutI\nK6zC1rhGlVqQyRaV2mHzLO1msDDksNrzL++rcqk9PjUUBHq9LT8sZiPRG/PQ\nv4k1GsRtdqsbWbeH55mg5Br0xqLG2nSyvLLvBh/QO1UB95VUyzzCXsBSLdef\n+qiQ\r\n=Zp/k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.12": {"name": "postcss", "version": "7.0.12", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "8cd5cd5aef92c1cae654de52d160d8d2b06a9952", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.12.tgz", "fileCount": 35, "integrity": "sha512-VTxN8+cqf0uRJ/4ngbDTdFgEaNeuF24mV17tmZrTnjOWoS4TNT3BkX0OKaASYU8itH+j0EHlCcxaNp+QmKMuvQ==", "signatures": [{"sig": "MEQCIEpaJIrPJoihC0ZFdJPRc73VUYIofDMtpjm5pMmjVHuJAiB/CiU2K/OvclOlnb/Cp3RZkKphz5+LmfljNJQ/IbbjHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 599152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPgtjCRA9TVsSAnZWagAAsccP/jjBZAQD1BsvrRpeMsvp\nLJVQVJCkWzHfHzmHLpzly40xWtIe4+CHJMpRpCiUlnzE8XmsiyWlck58xwDW\nd+lEAN5Z0gXFehxvNWknYkLJmF7UR47a8BocagRfl5+nX1c2+3GQN8Gu6KoI\nhtiYvMCpUtFIie8c4HMdAUqf+eP3PZ0DIMrN+wxdqIJvrfyIhXOQs+4+64et\nrEVWgnS43pH4YQxF8HFf6+6MuSOZC6F6zhNQKO7EbcSKja9U3fj43WB3LIDU\naQk75HBnrWXT9ZSc9KpjjNgOOC3kxF+7xdkbiSUf7LDFxhC9HpI1nqJjs0ZF\nxGS/hNfbJlJ/hRknbWVi4dZ/SAwvO6djHrn4K6VtFTvKVu+hgRD9lH3rTP/Q\nhLJVYwRZ2HHqkSnpaXVa+Z6MHoqshUJdFm2/ynTfusfpG6TCO2DZyWT6qSG6\nGs7Wc4TQER8FkpCck0JVi/TWojcBI7AvoyqB9ADtRP8YdIH050vv4bGYhSM/\nnPeeZed3nb7RpX/qLurgJ2AhEf0d/mcLP1tOSA+XERQX90nwOyvawFIkpm50\nC1Ei+TtDA4D/Qq4vPwg250Y7jQR3Uysor6h24/PBnAiG9VnkiR4Hzapn0R94\nCsS3ERpPh3G7/311aRyHWWI1FWysEmh+PBrN5ZHM/6ldKNfJcBqnRcPzV9Qd\nhzYI\r\n=T+we\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.13": {"name": "postcss", "version": "7.0.13", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "42bf716413e8f1c786ab71dc6e722b3671b16708", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.13.tgz", "fileCount": 35, "integrity": "sha512-h8SY6kQTd1wISHWjz+E6cswdhMuyBZRb16pSTv3W4zYZ3/YbyWeJdNUeOXB5IdZqE1U76OUEjjjqsC3z2f3hVg==", "signatures": [{"sig": "MEUCIFhVliNf4HGrE8o33qNbV5ZwkLCVLx+YzYt6/01fIWPOAiEA88QGDaZ1TjnZAMOQ6sJQK1q9+vd/Pi6upj/UBc9ZDuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 599342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPiHnCRA9TVsSAnZWagAA9bsQAJFstIDttUpoRAqwUr+u\ncajoiGgbyjKYognidXVnuhZftecNsPKoWtWjt5aPgmAm/tATAJ8/Js2oyF+x\nfdV5gVUZWQRynWHlph22s+G3f96iW48FBQAqxlQcIBWtXAbFINSt59QlmB8u\nTEt+wki0syhfAfCXiw+VFasDE2wPH4VLd/x84LGCaoDx5oRo6GfZvB3aM+1p\nEpQLlg+Kn9dmVAZVW+yxQgOMPbFOKsmiiIblD0Nk5aMvCWCiWdWfY8tuztYM\nQzPZfvt4q/1Bu9uTH21ryFYQxVf+R8Fet7OZQHVsDoErXBIZ4ZJXUvI8DLMF\n995DbJ5lFO6HlGx8i+16ZSgAifYIl7yunwdczcUUaJl9M9lBGFKFqtGOTaxo\n+LK2RGoOsQt+HWjhzamIpZuxNexNRD3gZarEsVm8MUVkNaaqrbcPYjm9XsjJ\nHScqsoe1wqdfrj8PxAl2Axp0/CGSns1EEm66BQ/yOKOrPR0INBlmhrHhg48F\nbi1zsPDInri075IHQCMKNFtN4M8A6dbIUUSGF2lfMjRpUUXfZAS3ldFqgyPW\nynmN/u0n/yB43yOOsgFEESJ9w0krhP4hVg686B1WFk+lMzScnnR9Oinvami1\nBtutUARn3ZpSz/fjh7ztArEG4WBYaVSCe/0fuyX6WUd1Tc7dNoXhErKsFoXT\n2jM4\r\n=UiFJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.14": {"name": "postcss", "version": "7.0.14", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "4527ed6b1ca0d82c53ce5ec1a2041c2346bbd6e5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.14.tgz", "fileCount": 35, "integrity": "sha512-NsbD6XUUMZvBxtQAJuWDJeeC4QFsmWsfozWxCJPWf3M55K9iu2iMDaKqyoOdTJ1R4usBXuxlVFAIo8rZPQD4Bg==", "signatures": [{"sig": "MEYCIQC/JiuIsIjQ4LRKzHRvlcylpcWnZ2uGYcgTmFq6RUuahgIhAOqOc66zIB/ofmP4hvS3pbhrhmbxOmOStnD20MfU5zPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 599736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRyXOCRA9TVsSAnZWagAAwSgP/jvNvckD/rACbignLu9p\nviD2/3Vow6z4lJO2pw7ks0cQJ9R7kI8GT695hiIPOATvseWKKhQ9Q6VT1U2f\nWZNH9BRj2I1P9IqncS5K5vORtOujHdzGGcufI+duqlOQhdtc2Pcbz16U/FOC\nSArKXYQ+ZypQs7CoMZvVPWU+EX+PNVV1FXxiLIhRYMaybD/W6bhb79R8tTri\nNMThdW9mCi4nqXe/oJEF52dgP1z3zf4tWalITC9Q4YmoRAVGnh5rjNZ+zl8P\nKOcAUFsG/9uLDQyVROD+ZJ5Hql13fXQmVcb7CKp3msDA2sNAWtjruAw9yT3w\npZyKm+x8cLGei6d9ho1t6eFZqNzkRKgei/psCm4ysqEudljXAMkat3vGCUnF\nwLN19OiD3CY/WNKS1cfJY6jWzpVvncFzANRIM8SzSS5TWBlU7lmEh8P4echS\nPpvtTXnvi//ybFb6CIpSdO7Lc3GwdNMhRrzcnSWQgmd5Ih9s4SbbfUlmePaC\nIZoCLcoyzZ3vUGxU0k+w0s+JY1AADwB/WLwVLTaa8mcxgSbqrsPfaLw6n9m5\niYeeodw4C36QLnhZ8qL9UG9QmHaSzFVhLbrMxPyLhAawTMu+1+pwgQCgdwY4\nAdgqqkTOrrDZjsuOL7YWA5cvDrVDT6SF64IRdC8fT11nvQslz30YgqGm6fSS\nvazT\r\n=IGfb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.15": {"name": "postcss", "version": "7.0.15", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "7f62bfff3437a8d358850792e1daee828e67e959", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.15.tgz", "fileCount": 35, "integrity": "sha512-+avadY713SyQf0m5np7byFzAFZyhPhXyxwp8OVmdd5mKOxm0VzM2AJkKIgBro7gGVk4kYlCDvBVrSqhU5m8E+w==", "signatures": [{"sig": "MEUCID/LVRG44aqUaxafjRY4ELhaX2wwayvUpdx2FCyR6EiUAiEAhFs02Dh8rrC0SWgCf61oDrlWsCn+mdwUk0pVQ+TNW7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 602035, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczSY7CRA9TVsSAnZWagAARfkQAJAbP3JYjVYijObVTrai\nesuvr3f0wEMUYVsrRBJv6iTfUonhrXGUNa9XyaKAsZ10GGtWjgAIMSCG2EzU\nVlg7N2kKMhY2Gi5ByqXZvg4DMBSs3jzRThp0VCTWOm/AQsyWIGN8pQNwD1WZ\n60+4PmzNEQtg6UY5vAxgkuvvB320F2ca9ss0ny+6BZfIl/B0uHWhLVFJj8ki\nQNWxWPipFGaeYrXkR/n9oF6qEesp8Hkv4uIqNjwc7xTAhYohBDP/r19wlTpC\nrgBrkZF33sySW6AoQoTcbJ9qw4U9LsJypgYDO+Hl2jTcLbEuOfEo93SJr2SQ\nv/J8RGhvfiWfOwNpi4ucmYgSdONYWal8Y032YuWYxaMnkI+hja7agwV9wBAL\nVVQ6gdMn/n+XSjunQB388GQbhBdZmtWqThMWXV3rjyKtGX/FP+zV+npkY62J\n5NSAJeBzDXNjkFRhgmAfwIu3j+QLkx6X381FUcNdS8ad2OzTMGNCFHkatQCs\nBP7kGsoqfr+krzowsYLYp11cMM/yuOUaLI9DNFDLl1Rw+H1rbWQYFP/d+Q/I\nP5zkZ23ww8lkyAHR6yeyPMF7tlUN/0MI+ZyjuPMR49o11ftrtK5aPNPkzA73\n6Sz5q9hE9sz/hRxO7wDEMJ7GORCA4voVe2hQ0cka3mXi5wbX/uh6lhnIc2Tl\nbSll\r\n=YZGC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.16": {"name": "postcss", "version": "7.0.16", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "48f64f1b4b558cb8b52c88987724359acb010da2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.16.tgz", "fileCount": 35, "integrity": "sha512-MOo8zNSlIqh22Uaa3drkdIAgUGEL+AD1ESiSdmElLUmE2uVDo1QloiT/IfW9qRw8Gw+Y/w69UVMGwbufMSftxA==", "signatures": [{"sig": "MEQCIDJhEzFxp0mEne3CfDFFQzbvEapWwakqk2doxjF2AtG8AiBYue/CvAyhlMLRYnbxFHi8ob/nYusfPk58x6NalDsM4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczeECCRA9TVsSAnZWagAALv8P/1gIMY+oTvOdzd4311CU\neJd/iOMF97XCGcYLnQmfOtNjjpQwUHWuChxwEJXOakgXFJTjI0bve1BSb40W\nk0RONK5WdlU+uxwy5h/9JTOjtb/UyxjVD2wqGWwcFlJF7hXLlflgzx0n7jZY\nGye/d82+1J88rt/T7UlFbTraEe4bQmMmsSMpR+OtY7BFjIsG9OvdL34wkcMx\n1jonNt4pYgmkS+WaFLLf5mXb4f6b4SjWpeK9beCJ6FcJAWXvvbcizMrywjZ3\net8CFKV5WnBLtE1+viMgE19qZYEn9Fmw2ciumyPEvz1ifIcZyVKfaLhPQ+fR\nKqOngCigkIZP9IZiy8Ic+LSA6PSaStIarCsbY3Ho7Go7uOWxV6FqGEv4GIOp\nWI5I2xQMiToLsc/E0sYXT4VEA2K6Vs9XfXV45WAdmTanrKgqEt/1u7yHGBim\njVEV9tsef41NGap03uO1BJv43H3c/EycuVPc+QUZ9AWwptpkuNvnLf3CQ3P2\nkutiQH3LOMmhl9BWdegwJtpdI5zKxHGOCOuWluHpF2A3QP6taLPAN3HcUqPe\n9wzlLC9YHZn8OuGFJeXAK8wg+NO6OlaBQU3R2qasY5ucNYH+aa3uZNhyFPbn\nSiifE3H9cpJsS7sCXx0m03FrFOSYcWrQJ/26s/N0O++zPg9HMXaX5RmRJKhP\neL33\r\n=eSrT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.17": {"name": "postcss", "version": "7.0.17", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "4da1bdff5322d4a0acaab4d87f3e782436bad31f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.17.tgz", "fileCount": 35, "integrity": "sha512-546ZowA+KZ3OasvQZHsbuEpysvwTZNGJv9EfyCQdsIDltPSWHAeTQ5fQy/Npi2ZDtLI3zs7Ps/p6wThErhm9fQ==", "signatures": [{"sig": "MEYCIQC9AsHWpjMkuqWJuIWwR9M3UDk11fhzJLrH7G2mxK2egQIhAK5DEsIc4RkoR7REqEXcqfEBnQwCfvb7ybhhHgad8vBl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 599329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9/AKCRA9TVsSAnZWagAA1QwP/iCMourfRgxmyF/OoGQ/\n//ZJmdE7miQXtmHe867aZ8+rfQKFceXVbUHJFnhAcnn6E9vwuPGzo9gmsP4T\nN/gafw6vjJLsSE+AdAlnsmThGBj3Bq8v3YNzAHg3lOTlXlp0YlihsC8NVN0B\ndUOSyusRz9IukSW5Mtxuy5qodfd25irlilCTwppagIA3Q5HPg5rUjgAo+cCM\nxdIpjNQJNiux78oBE2ed5Nquau51iigCFXG6gDJV5VRMRW+8Y+qjlX4PEtRz\n8PrQpxbORUn4mcPbrLr3nnHWjD/udgy9hRee8uhPsPesPZxQzlpjFff5w+My\nZFoJVtYEkjOk4N2bcClEmi1jrxj7ISBPODAu8nm3BFmm4Q++e8ZBXpWo03yx\nWWLmJ8EDwrkvBAxSVthxdIMiE8Yk574xjOWsVIkdEiKJ7WmIfY4KKnf8dsOu\nSxMx4Ag/Ou6KZempgkzeSCEZAZdLQA8150FsQT1NdDX2bF4qXxisT27P9owS\n6Mul/WyOwk17WfnZBiJUPvW7ppZu+wqT1RMxr2ydL91HN81B70mw1R7idCN0\nC2nnH6yFfjbtB6JG1DL15aXScK5Kr2O54fGZqeZ8i/XDFpp669LNBcoRf3oE\nifst/ewv/1Lvw4ccPs7Fv0prRC3yYXIRKBSQn5Ht/+XzjlXXVoIcnlFulupg\n4puT\r\n=Vm8A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.18": {"name": "postcss", "version": "7.0.18", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "4b9cda95ae6c069c67a4d933029eddd4838ac233", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.18.tgz", "fileCount": 38, "integrity": "sha512-/7g1QXXgegpF+9GJj4iN7ChGF40sYuGYJ8WZu8DZWnmhQ/G36hfdk3q9LBJmoK+lZ+yzZ5KYpOoxq7LF1BxE8g==", "signatures": [{"sig": "MEQCIGl6Url2dwT0mzrJDVyQoUd5H1kKuJNbUIZLgQZALX0ZAiANxDe+CTynzmlEYdrYVCJ9YrgAFLcN6WFzCiGxedmj3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1083080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcZhPCRA9TVsSAnZWagAA0p8P/39iQI8ixMYLnmqcROJ4\nXgYRLdx4ps3tG/eNlgpP+nMOFpz4Q8o3igoUi5EYd6CzBwjGsh0gjMguGF8s\nviYMGOrJ0BiyOiIsLpJSscptEAMmsCXgvb/qPpJ7d4ibwSmVDaCMH9yQMotn\nyKK056+aNW5r1YCYFvGFx7mfrU+DHr/dBMMX1v8o2dCTdaUzJmiMXk6vmJTk\n/vXRkKbr3iTSekvaUr6a6BgBvvEg8c8qnRwuPtwPpsQKjUsgkTnQJUPNkGPx\nskXzXwmX7a0JH/nCs+bTB4qQ3XAD4pqLIdt+q+IV40VO1MokSWXcwITsq+ly\n11AZeYZ2FNvapXw/jj5KyrY+MQjHmbiKPaqG7/pDXyoeiKlchWFHuHKajsPH\nzBw9rUJth+v5lnSEjG7ltVR8y2XjGrJI9PYh9yEwCjfpziiIYSJDaDcNOStu\nEwk8GbUIqZwGQ/8ZcNPDDXS2ZZlaJrOdeB5GG2BojYj4ZskNqQX2fl5xwpU7\ng/xKJN+Fp10SSG+SyeosLTjSPXq+/RfxN13oJQwS6EBC6Y4499yYXWOwflpl\nIsmSkmEObCGr5sP5eN2MDG5ceAepUUp80bbYHkYnk46WtYU73hpmu0w8F2BQ\nnKlHSWbRBAV8TB+H3hyWnmacpcSMk69AbhLuoBHQjVs276walvya3bteI6JF\naMyK\r\n=m9X7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.19": {"name": "postcss", "version": "7.0.19", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "a59e1c8fdad9f6b31fb192937361cbae1562399e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.19.tgz", "fileCount": 38, "integrity": "sha512-0InetJoW1WQMoiJ3E5oF1m37ZAJX0d0XLgR50bVXCC4cF8HCYRpT6hH2nNYHVCV7QAx1hWj81/2CRT0elHAy5g==", "signatures": [{"sig": "MEQCIG6KNmxV4j+DOzAXMpbPZuMsLi/X26TMnD5nnuOlcETzAiBImVLWZQxInh2qQlD4P9rg30bdRsCnCphGM/GKnyEWgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1085982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsZydCRA9TVsSAnZWagAASs0P+webTuSI9ypWSc8H9K1F\n+iHHSYHPOr33L1mygbdtcBeU+a130TAnpN7uxX+UaUOCYW6gaIqk1AX+CVmA\nQ5QsvofHG4O9zoNJiZAS1RvnR8EsYR5AULwDoC0dXDzKmMy1W2Ii8/fMPh6M\niAaJ7/y0Sn6VWNuuRF11Ev9RbLMLdLkzFZMPvKYWPOzpoBLBjVXZOqcDRSxW\nSWhvksR0KncuiAS3K7ftUzilSF9AevprfK8HmMzGhTX4lcC13PX/D3JLMZuV\n/XqB5CM3m7CW7/66dtGAdnnCni3sLf+vPqeCt5oX1dEgQbz8bIWTWhwVHjp/\nZrqfYasbO7BT1Dg9sQ39j4NJb8zM1MNhSk9Hr0DmGTSllSDeWB0YvLynX6Xp\nEy/C9nr1f6nTgvhLvf57M2/nQem/rRk9bYjnZLBi2Hks1RWkiSXsvAYRrQ5T\nTe6CWt4eoygwWxqBur/KMXRDVmyyCNczPvx6NoqCxjXnEzuzGWRlVPGErQzc\nvSVhFN2YX4I592smnkLB4awKSIoMqPcCYIRWXottZUdBjNc4U1/QLXM7XNxA\nRL75aZ8WN4HFU1XjY+1sm/TD6f6x8LH6LXeJX/FcVeRI5R6mVCB0bLQQP++2\n1WUDSbBdvrcPQ1rgX9QxZZwzogUCtNQG0m7HmOixDiFofnh6eN9Ay6aFsKne\n/kQ/\r\n=TNBQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.20": {"name": "postcss", "version": "7.0.20", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "a107b68ef1ad1c5e6e214ebb3c5ede2799322837", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.20.tgz", "fileCount": 38, "integrity": "sha512-VOdO3a5nHVftPSEbG1zaG320b4mH5KAflH+pIeVAF5/hlw6YumELSgHZQBekjg29Oj4qw7XAyp9tIEBpeNWcyg==", "signatures": [{"sig": "MEUCIEZvnrlBox54fAUDwOvG4CUoS+zsrO5JsYurmIX22AQUAiEAxukoJ4S67wB2PogSfA5DN+D/nBuJ2v+EccpCpfo4yuc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1086573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsb9aCRA9TVsSAnZWagAAToIQAJBu06qem8s4PP40T033\nRKQcIkJA+/KzVISbl3oWyj85PGTXZb9F2lnuDFSybzt3dTpNRgVZRnR4B8Gf\n2Oxb79uTAIxyQtW+5Wr3l7SFxDk3INTInj9EgyXEj8L/+1LkPmFqyR3laR0J\naBj/XxZNoE1JHmw6xObxlr0ovhPqHYvkjqZ7imS3NqLEgZ1LJwxFcS7KrUXA\nZ6woXBoFEv/dPb5Pry+7M6L/JUcvD5ubpggHVe9iWaO7RCrlHeU1fTyvqo2z\nFleGGMp6X+i7rseMfoNSf+udFx+xlurHy16nP302phKX3Rt2Gug6dF7wim0f\n9tSo7Nw6LEms0xrMNWsJGLh3KgwkF/M87qpE4ByzQ0c8duuwvj9AhJ6dQGUT\nUQGq7MpWJInydA7mkTWK7Eg/kjxAT/CF7QexrlgBWDSbn+BZSozBS8EbEIxC\nVoeHc4jUP/b5Hl/AMWHeZ8j4BYNavJljV0h78n5XfguzjCj+uNUCOrQiQMIB\nFVCVki5E4z2Uxins0Yvc+RXkgPo3V9TL5cKvBKNLjop3VlCjHvB7+kOGXCZD\nz1tMxiKGb98BBCM11dinsnuopJTvgraT9tKJeDCXT+N08zLQ9aSvUzo+Pm/i\n0QarcacmBi92j60KCNcf89PCvIx7CbDWO626EgT0Cji3lbAgOStr75wdjO8q\nuifA\r\n=N0na\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.21": {"name": "postcss", "version": "7.0.21", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "06bb07824c19c2021c5d056d5b10c35b989f7e17", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.21.tgz", "fileCount": 38, "integrity": "sha512-uIFtJElxJo29QC753JzhidoAhvp/e/Exezkdhfmt8AymWT6/5B7W1WmponYWkHk2eg6sONyTch0A3nkMPun3SQ==", "signatures": [{"sig": "MEUCIQCSPVjJhkQLCYVn9IERBgkSSuIOTdnZd0IuNFc4RVH3CQIgfj98Vf57rGEYD8ddRkN+LN4kP9+xWFrGGs1GVKiS8AI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1085084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsxr7CRA9TVsSAnZWagAAfZ0P/3EoOtKnNZUJKhE9bUKC\ngzri9NUqd3Vw3OlTNNEHUMYUBPnfUijQgXJD6f2AGhj9Fg5NV08VyV96N9Xy\nkvgVcck8aDFW4bNsMYNP75SFXo7auI7HAitRQMwM277PhqpXJh2mP3+QLG1t\n6HgPZlGOm06fb26zlN95cdEIsYv+5UXUyesqJ6JMlKzOq9T1JRHYQ7WO5Vvs\nF/EiJHVEtDyJnNtIcJ99hawcSl0Y33ycaj0s/5mufRztlYUwF9vCXkUJCX68\nqlA6gAuHrogCYdv6DQRlcsaV5//hAzzCuuBa+H9qd9rR/U4iVoirvYyqvOdF\nL5B5XzdXxeGjx7JwliP46YxxiexJGkrVYwRn3gJHlkLwYkheL90BK33zwNEA\np5X471d6eqCjeO6FcZWx7LqwE38EJFc8SNsPPR2O6jsYkPzlWfANr0VMPBqJ\nufW1tJRaO7W/3E7nVKpbzYK/qVwvW1R/6L3cNYScQZmfohfDwQ8exUPqZhV+\nZz+8ZlmT2JS7q8PhGgmatyrnMLF3qvdrV4f9WeszCAlrZu7xsxm39B6l+mtF\njS6pxAItEbI81gCbnAYCXIkm2bb+M+MpxNbKR3vPtepmY7iC+tT6pcLbgXnq\n2uYxkR5CgxZAZq3pH7O2GJghhVMy3i4jRAxHbdogYLOqzHjSYxWZ+/A4hdUU\nVdB4\r\n=DEAy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "7.0.22": {"name": "postcss", "version": "7.0.22", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "661442eb04aa50db5331fa58f46ff8117cbacec6", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.22.tgz", "fileCount": 35, "integrity": "sha512-LSC+iSy7u2O/6DB54nBNHjuZYR7BwcOjFN8nbD/9K/l4TJBfeyGkK52zwuqqEN2/QnsM6SDDFQQDd2AjjqgFXg==", "signatures": [{"sig": "MEUCIAxp83z288n6Q7/w28L0axCS+Vz7o5W1+IUb5LwlbFTeAiEA7NXUEj3GcXkx0LFqfWcMDSm0XIrC0hZt82Ar+AztT7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600232, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0xtGCRA9TVsSAnZWagAAygMQAJHOka7AMqJuFqVPheYf\n6PHm25jmCLw2sx7bwcDLyjSk6zhQbK32FLL4DadbzgUSoheNX3WaMrOct06K\n4ulSUBvJdVSI0pQfMY1Ffg7WjTLx5cOz5e3ZwtpODgxxuJikrh+ICJRJr+FE\nSjCei8XMCFrtCkBABTDEaSOJ67G4wzMTnSUXwuRDszHpi7Bn6IzSZD5kNe5T\nIKFOD2CXr5t2hmVYODkv6ryt2qywsc0++yufBf0YHpophU1z6R4HdDouR5fD\nX9sjpKeEqNdo3RhUHuIdy+ZkBh/AroFLQPX/xrkSodKXVDqWdOVSX2yZIIkE\nSBWNOS8tJ50GZGaOekBl1tvr7p1cC1Rsq3OG70Khs1nFxnIvTaRpH7Mk5dr1\na1KH1bRUijeeAm5keZAkWbdIN/p/IVWq3XOmZDJJw9zhxJNr89XUtL/aJCKt\nemOb7MN3pAIpNwXLrd9Fqle900Ag/igM+jab8v5bMoT7gHl4hM5/x0IS4tpf\nVVlvcuS4+byBFz9zpDlkCVI842wvpkJBwWkp8+1QYKBXn2mETiZ6sWRt0w9Z\naLpY1ysYxG3MEH4aPeJRoYPxCDellUlaB2MCjxJ/c3I84tyU+VaRuqxYzQ6R\nqJBjs3H9Y08edKy0Hu06jgqy09KiBPSYHV/2vSff9MqY08cM6IRF47ekHFvx\nVRSp\r\n=9mVx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.23": {"name": "postcss", "version": "7.0.23", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "9f9759fad661b15964f3cfc3140f66f1e05eadc1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.23.tgz", "fileCount": 35, "integrity": "sha512-hOlMf3ouRIFXD+j2VJecwssTwbvsPGJVMzupptg+85WA+i7MwyrydmQAgY3R+m0Bc0exunhbJmijy8u8+vufuQ==", "signatures": [{"sig": "MEUCIEZRdOXfpln2AuUogSoWgrJ2tdNEhrFBTi8WcKbMSlpTAiEApm8Z+nXSnwiTT7JNMyKWRQ3HL6vrCgDBPamSM4u6dRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0xz1CRA9TVsSAnZWagAAJVEP/1mwwNHca2C7iawufxhv\nXWdEbHMICPttcg52PRFQ4SVstdSub0I6w8qPvEByfwb1KxHtWgKSviXPqzDO\nWt95qWgRqttDSRhsHbxDUb6srWxIB3a0c86PH5+5ZSJKxYsWZlJB6cYMXHBz\nh0QhPigY5M4LlvRGX9qfOTdjPeVzbrzIP/1ms98dqmFjanSqoOUl+Q5mXGbS\nWhWJYS+vpLvVOvq7pq0pfJHGEC6q5a0xH3VDpN70w/mOdqrvCcBTfoiGEY22\nsoqkXYjKv3VqTOFQc5ajINLNEhrhKznaKxQXPmqLOJfr1jyb4HYNVqmspPdw\nJWWT0O/j0uSMc1SDT/7m8mNyri1uy8qQG1GSv1V7JlVgbdUPLVifHTZbtesh\n/pQOwj3EfOjNZUdjhCEojbHNOIln+/UgsLJgmWaRrZswRici/tPCUw354Q4X\nRpAIcSLLURKd3hNxglC+D7nU2AVWFJ/VT7LJAP9SmBv50RLzvy/WpSkHO1wo\ncyKbmuVRVirTRuIxkEHuTkox1vkYDWkMsza3w/r0qpvZsz0WiOvYgnbDV2bz\nqQn8VijkzrvxUroRV2/dEl9H4QjksLrK/IyUZw34v5NSA3i+M2PxrzIgZ2A0\nWu1tvncWRKea05N2PKGj7T4KneayirzgjAcUqn3lDN/gC4eg7aI0gPrGnOUr\nWr3d\r\n=W4mH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.24": {"name": "postcss", "version": "7.0.24", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "972c3c5be431b32e40caefe6c81b5a19117704c2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.24.tgz", "fileCount": 35, "integrity": "sha512-Xl0XvdNWg+CblAXzNvbSOUvgJXwSjmbAKORqyw9V2AlHrm1js2gFw9y3jibBAhpKZi8b5JzJCVh/FyzPsTtgTA==", "signatures": [{"sig": "MEYCIQDKJGpdq4u+2HsK3xU0UKTvLksnB++FK8PBUl6eE2jnJgIhAIdiJuCEoH7eyniNwLQV/8wIjKfQ0IyJGYjrFj2YpnMz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6qPlCRA9TVsSAnZWagAAww8P/imzsdag70MbyxwTYocY\nTYdURNISNp1oHhlWFaZRf9kc4lGfxd6S+ZEtsofrwtRseSCMf2pdI5Tqp58E\nnsj3LiVdzAxLMQOdfm3+TDmpwI5FIHjA8yhdqz+ArnKhaTAwzOLsiQMfkNhy\n6L2pWItfR4CEYtYSebxsKJ51JzUfgcNcZFSLrwjtXnVBFZY5n4gqGDARliji\nzL1m4e86QVzbSVksltrGlhOYlA3D0a72vfcRmOwN2WklfW2gRAX7aGr1DjVd\nrX2SpiXb74g07gJHDXhX8tcp5erj+Fpc+K2NymicQUI+rU5RvgQfb2HFIkT3\n3MNxYtpsJogEg/NHhIg1/4JMHpiOtwxSD2viwtwVOvKamUfzlefEp1vuk3+w\nbpx+wUQL5vOb4eQjZBbhhUsB3v5Ft3WFCpFMkYevBuFeOFx1/qcRiMNeWtGN\nM03z5PKOBFGG2E+o0S1/m3oTDJ4jAyT2lK0GYcca2Sv0m5gslVLg9OB4AVr7\na+sZGyMMHQUNyksFNhQ1A5i+oa7opfW7zeOZKHu9I8T10GLSMq+0LyIOFSVN\n20zO6RrcU4vd8LkAxxJq/kyq/SU/DUAtmENiQqmOLoKqzoYcvdJFxfDT3feH\nZFmX0BMuFr0xIfht6cF/R2VfwcUR+a00WWB5g8mrOF6azixibldLZg7ojj+X\n4D7D\r\n=GKqX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.25": {"name": "postcss", "version": "7.0.25", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "dd2a2a753d50b13bed7a2009b4a18ac14d9db21e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.25.tgz", "fileCount": 35, "integrity": "sha512-NXXVvWq9icrm/TgQC0O6YVFi4StfJz46M1iNd/h6B26Nvh/HKI+q4YZtFN/EjcInZliEscO/WL10BXnc1E5nwg==", "signatures": [{"sig": "MEUCIQCYWCoBlEOp1cAYfQjqe5YB6mPGdfHtAJ3SdhfOMHwfwgIgPKKo71Uk85bGtA+96aknOjq9lROQfmQZdguCK8aJhXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600768, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd99gNCRA9TVsSAnZWagAAufgQAI++tLmv+T1tpaJxCMwC\nFC+rvIdfts8cHURnlkF3Kfk32ehopwaEBn+2Jeouv5vqIIGGApObHFBjlCNp\n3VsbY8q+qoy6djq2Vut5dojaGA+FJfvZJjKdorDq2OwYacVctbPyvhWakHZ+\n8gipoQvWXAW78MzGW/hAD0hk/rr5wW6JX1AIyeeI8ZZt2u7UG0nwMqt5YdAw\ng99m72q0OjL1Cro8qcGM8UK7aC62onXs9hNoEMzWU4p2cEZPA9bgsja7NWdS\nq6Soj8FEfplnmJE1p3JpXVCn5UFqxlfEOTPf9CWzMT+CEpJnGG+XhvGcjbz7\nebRBHa9SKKweRH3TlO1XF6ZG4d8iiOUUi1Qn5Ii5WKh8pAURDd6jmlW6rO4K\n85KE/CbmesQ8XIWjn/LaTb3ixe3P/lnz92tGi3nBpQNH/u8SgktKdgpBGYC/\nZOZCSK7IN3BABGdvGETQa7vBcStw1vBdgz4o1e+YnjBR7Q0GJlfJKFWHxdrm\nPXEUW/uwsg8tzfkfp3tzQKN/PicfbGNe7z1fYdjw/uGSWj/xHSKGSmIFelGV\nHt13Q+v25ew20NhpmOGlgOUkOCL/zjEmhxarq8Zmo577zU5EhEAuCRDJvSNF\nDcbVgGtk4cIFEG/n9Gd8anwogAbUxKehIJdMeWaEb2cjRskWRNsqBDCPDV/W\ngoCl\r\n=B6+y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.26": {"name": "postcss", "version": "7.0.26", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "5ed615cfcab35ba9bbb82414a4fa88ea10429587", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.26.tgz", "fileCount": 35, "integrity": "sha512-IY4oRjpXWYshuTDFxMVkJDtWIk2LhsTlu8bZnbEJA4+bYT16Lvpo8Qv6EvDumhYRgzjZl489pmsY3qVgJQ08nA==", "signatures": [{"sig": "MEQCIEZ+3myk7LYoMS/58ZXpqtjoVDdcLIcjcp4DFK23ynqxAiBJ5Yq4c0O9rIbE/7jJor3BnaW2Sk1DvC+Wh/fpOIOF5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 600866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeCpIkCRA9TVsSAnZWagAAUWAP/1hk5dh/2tXg0YkP2nJD\nRuqjXkz+dbqP5Gid0Z69mRBc97CLDjwUurWeMNloQpJkPM3pOur8g6wB5dRQ\nNg+la4ZVuPrYtvc/hGonCtnLJ+HYiUvb6RSDeNSjz+TSLHVJjl17zYmLrkoE\nQUcbPtaSxPysY7aRTTO4wdoysXqH1LKDEzxmzT+JSAmavbFIMMGnuBGk7TBy\noj+5UYz0NUivVFOiMZQ0umRhb6qzmJBSJQtHNnv8KE3HFrZwmD3PbQXV1cee\n0kElfYZCOh22Nq7ugSPiBwtO5piYYglex2WaNvjo3Wz+Sr2S3ok8otleb7xM\nPqgFNXkBGnecOeLIKQ4pIHoG8lIiv0SGdcNfEzzW0tmqMguciWXRihcHKiHg\nt9jOkDuct1adJZ81YzP4uO3OC0YClsWtyQBCdpKRA5FYGA7/0YMB62rJs7qI\n7RLV4aGL/G4W2bQivBWCUtS3XYYNVCPRU+OEDeCqLZ/+xM2ELnbSOvmpxQD3\nra+ZONiK6uVZnCH2ElW8C6J1s7xYT0xJRZH1YZblJsBw4KHuDQWl0BcOK3UW\nTaTmmH0rvIkXtqBcFwra6Adyq7ibbEzGnCNvU3a8lW2nFUTmpR7OZZIdALSk\n8e5kpIb41Latl8+OOIAqNwRDrcyr/7HWeI4bkLJvimIeZyxHdKEP+ZL4z4Tp\nn6QJ\r\n=HL6v\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.27": {"name": "postcss", "version": "7.0.27", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "cc67cdc6b0daa375105b7c424a85567345fc54d9", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.27.tgz", "fileCount": 38, "integrity": "sha512-WuQETPMcW9Uf1/22HWUWP9lgsIC+KEHg2kozMflKjbeUtw9ujvFX6QmIfozaErDkmLWS9WEnEdEe6Uo9/BNTdQ==", "signatures": [{"sig": "MEUCIQCsxynLf491F8fNTj9F4sYVb/ZzFycordNoicPDQixA7QIgDe8hvm3a9lFGuxyx6Z0la6O1SbF3gXDneOgbEL1L4yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1086215, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeS1MfCRA9TVsSAnZWagAA4XYP/0nSbZe1hBS/Zb5RwBR2\nqaFkbzd3cjdleIUyWKH3ABErm1w4wUfprMQ7rMhB3tV9vXGNCfggjz+MZWr0\nM2k0+1ZNQraQHbNPtYgth7LG3PiGsjouLcyKs/R5BWYJxZRyyGnYZeTSlEg1\n+kl4cnbGRVqrpwQEuD6UxudK8CKs/T7evgMDX5i8u053OrJqExBznOUyo9J1\n4QtJZC5G29GoljIcoq28rdpV+3CLssDZfM3cHBLzDJ0qavdVZmX/cve0kg5k\naGHJOjWExOfDwc9+U+tUYfeF87vXe2b2SpBW9DI0uU4j2Eutf8EOo1XtRq6Y\nD/MYJRtNXiKGi7W2o6eVXjtGc4u2iVNAh78bAywtaiWu7w91F0ZlHlCxJlwa\nxsZQwGfmhzAVcogU+I9a8DVQ56mgkXoi1EuitE/Uc4U50GODpv7yrgkzHuI6\n66AP/zgaKcdc1dAhoukppEHmeky3M/FLiEGDS1Q848BXbg5D2tdGcm2asiP3\n6vriO2AyU7IB8ysx2LyKXPbfyBCulWiwcWvHqqKu0w4nvNbvTretfOjKQlrA\nG+4lNUXezESjM5VD6iiFk+MedR+7jw4X0ewnjeAyCr1/us4GhoqefU1tWA/P\nDFnxDKlFbtSo9E4h2rXksyY+RzNpXc/WFi5+DyWkZc8DZ+t3Bpi/vW1neZFC\nXWE+\r\n=H7Bq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.28": {"name": "postcss", "version": "7.0.28", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "d349ced7743475717ba91f6810efb58c51fb5dbb", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.28.tgz", "fileCount": 38, "integrity": "sha512-YU6nVhyWIsVtlNlnAj1fHTsUKW5qxm3KEgzq2Jj6KTEFOTK8QWR12eIDvrlWhiSTK8WIBFTBhOJV4DY6dUuEbw==", "signatures": [{"sig": "MEUCIEY0JmT91M/iyGhRafw6I60LlUy9CueFGq7q3zoEOWYVAiEAssQ/B7p//6vsYwWMeczRmIRR9EJSUrWmDEY8XLr8uhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerYZ0CRA9TVsSAnZWagAAmbcP/RTTxp3Cy5Hrumn/VLoC\niAvhBlsNPvSi0P0/9uP9lOZz0GMiJ839Mmz667zfuP89vgBPw/Sx2g8SdyTM\n/QcbnUo8jr8cAix7+YguN3j/f5ClXmro8yy/dpq6v/92pHciJS2UtlwW6OkU\n4Sqvxt92wsf5PEzCC+zVJsXlguEhigBqe5q35huV2+keWGi9/HJ+wp1FPQqH\n1b4XECAIPSzAFqa+xo9544I+IZCfieVUoaljpS4ZurZKJCnB8brTPlAFHS6W\nAIB3QB/IM0RRJzJtpJOZUu3nSqwBXQqIt2zBOLuuyx9ADRaB6BljuAttrAYg\nPQvi61291f8gJ37Mn0OPKenE5PdGL+cLeCiKeQ1s/62MHxXwwlRjXYcFIDLw\nWL2fHOLxZ0v30fpdjkDbCdlxG7InmqEZnZDHW+XD3nCXBwaf8oj3AY4oGZNq\n0RUwdHJUfXbhRC8lcGX7NcYJi+cPe6JdzRYD7t534I3aAbeoyD6Q5Hbmgq85\nzRCzUKfXmd2+VlMlLUvkouQZTHuMdAIfPzhbVbb5JvoqQ63QbMkl95XPqxoH\nFbKM/3VmG622FP8ck9/LBs/SL4Gf3FJiFKVxX4F5aT4VBra5Oh3vFXctNDX+\n033nMcARNjhoBtjcfq+wz+0sx+/xonhT/ldtErkzLpV3l+SVHbsZ3Fboiqcf\nD/M+\r\n=1Y3G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.29": {"name": "postcss", "version": "7.0.29", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "d3a903872bd52280b83bce38cdc83ce55c06129e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.29.tgz", "fileCount": 38, "integrity": "sha512-ba0ApvR3LxGvRMMiUa9n0WR4HjzcYm7tS+ht4/2Nd0NLtHpPIH77fuB9Xh1/yJVz9O/E/95Y/dn8ygWsyffXtw==", "signatures": [{"sig": "MEYCIQDtnuaVBfa14IPBAwtgN+7tfFNruVdSzLCkcs0nhEgJawIhAJBVS/JuI7T2uFoT7xWQFtP3Ve3HrkQdcLy6ADAwp1JD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesCOxCRA9TVsSAnZWagAAieMP/2eO/8ZXQNQF8ixc71Od\n+reCs4/FPGE3gQO0+UMw/Wu249CpfEMYzlA3dKSMnBZT2Ws0qd6Rb23HfFsX\nTTVIxs24hKoGeAzXIEx0E7UkrP6XutfG/bcbA6KxXn9nzwr0X3HPhMD8h6rg\nqHWey5hPEsutKB7/PXA9IaURHrGwfXqnZwxfWWuUPdxPm6I/GQFm4ck9j7Ul\nx6s6rvChNbhKGTuC971hDq8fI8DGSeGHhd+Z8rMLPzO9IZrJMucXpi0i/6Z3\nGFJPtTR7ijfLak6VJl6htURpVWpnRjYMGtNRS5zOPbrghDKxzojVhtIuUGYH\nsGfm09A1/5LAYClZCKTjRs2nc3zfn7N0N7jAbWU8WJm5Pwtb4YqYy8H5FX8B\nzG6mQ5362vB25QJJqUVF4mBcfYWpz8fQmdaLxrcJbejLiwNObT9WWpd1R2qs\nwVLURMN48PHPueb16U2qOqfDzWcRki9fuXHQ3jUNU6lzPqIYKXlsTxjRrh+a\n68/gxj+/mt+9JDs7z8+GyepAoedjFLhiQr7FRkeXzTNzrAcdV0+49MPwnD61\n+eoc8sil/OAFaz/V0ySf7VNKQLzDLN7Sl+iJO41TpLFa0Q8qcXncP9NhSZE3\nLU0Yxc1KBNBmxzGZg8hzqWXOpNfs3ST4JdVwCXHB5RxMXf25gCFhZbeswkP3\n13Ki\r\n=Cx+J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.30": {"name": "postcss", "version": "7.0.30", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "cc9378beffe46a02cbc4506a0477d05fcea9a8e2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.30.tgz", "fileCount": 38, "integrity": "sha512-nu/0m+NtIzoubO+xdAlwZl/u5S5vi/y6BCsoL8D+8IxsD3XvBS8X4YEADNIVXKVuQvduiucnRv+vPIqj56EGMQ==", "signatures": [{"sig": "MEUCIQCYxOKhf0RTifBNwxI7CPt8gOWyFq+FF2EyQoBPxEnQ9wIgLeuaRGqmdgPQvbQQQZ8WDO00RGA4nz5Z7OesXhS1ndg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1087705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuVppCRA9TVsSAnZWagAAX+QQAKDBnb9hPIpPu88vS6Ip\novyT8axBmPHecbcy/4F1RJMD4xv6eWY3/8JnVF77J1WIsaPni4/jT7LtcvVy\nMHJrYwaS49VWsGLzf0yuUfV7w8YbypA5um8WLoOM3wy4J2M2ZiSu463klWVW\nK24EpdUuXrXBguwSuxqGTC1XyVCb9rd2117qTwzYbhwqOrH11pDaQ/f0rIWO\nNC7//z42xOgStl5qztW1ze62nuYeE/McUFdlOdX0rJtVaTmoeFlgVZNXDOoE\n2Pr3WAzhCzMGlwwP0wRXOq6WF/U2GQ0DK8msytHe38GBwjVEEnUJ8u1AgKTj\nXgvdrQ6xQf1BJCCAiS5n0zwya4usU5ZTRBq6QBD2rvbIhcLQ+B10aHLx4mZo\nCspmrWmrTK50Ic88LaEjx3UrxlOK+Nwd6c4UuFzYuDdKHlyCf/FUOM4Dfmxy\nallmUUuZSP5leMrigc6AxDvW+NBgobyLNRwfIL44+xA3ErZuZr8fkJRQkZlB\nO21R4lGhyGW+SobRArbcL7yPTIfC4kZ4bJUdRE99s21ZNNCRTUAKBNul/OZc\n1BUJJEAodwwzLxXX5V2bYTp69nTDMIuPI7+U7JK6E+BPUOSmxkl6fPGJMZE9\nJgHyvI+L+rVO0re7SS7mBInbeg4ZpMhjMxoUXS5vcPYfVfrTX9tPUPQBPZyJ\nt3ES\r\n=yAIa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.31": {"name": "postcss", "version": "7.0.31", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "332af45cb73e26c0ee2614d7c7fb02dfcc2bd6dd", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.31.tgz", "fileCount": 38, "integrity": "sha512-a937VDHE1ftkjk+8/7nj/mrjtmkn69xxzJgRETXdAUU+IgOYPQNJF17haGWbeDxSyk++HA14UA98FurvPyBJOA==", "signatures": [{"sig": "MEUCIQCPSFm4E45ZFQq3p/B7E8BFGaGdc8DqtyEH0z56csd/XAIgAzooCQUe4OoG5zVrHLcxLBKrDWSNvcLpYsYTJIEYE5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1089354, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezHtXCRA9TVsSAnZWagAAyU0P/0EGTHoH6J290ma8nYR5\nMD1FtAHPTSlVk30yj+AMEG4dR7wOhPn8S1sSRractc0mhINwdiY3lUO+jjB3\nf9jElK8rOH2gj2i5HXpsI3uaEQ02HQUYR/ta9ODx5Tr5vbbxUIUxQUCAJ05h\nUOItFawGIqbSGGj3bHn2E2ck+UctCUI2OKufw7oCB5kmr2gdxYJApo2JXSvi\n+dun7inpYY1Dyf168TU5fH/P1es2HF9wv/1T0F7/xoa5pcv2ks3bENvNLYVn\nfYS6siD1TlluCjdJZphOIxPaYgyP2t0kdghwmkD+O0SVBuy6HbaZqp4yYws6\nO6CHccS4c+Sa7BeX9D0v1SbVfDLEMgc7tU0GKYlDoEUguBKi6emOzkdTN4dk\nxk/4BF1YlOdvbh2x3Cns8gOviO7ldswp3aWfSPxGq2QQwOqbygFrlIuvedRl\n63f7jVtjZGKlKr8JFtFSZKPpZhkeTlpKblJR6sFUp1FbhMkDizOZjEVdk9uE\nh+kQ5g4LIGCYMSYZqiKtkZ5mek3Q8BQNZxWebuaIf7o7KUU33J+wVqs/FdfY\nNtgcIUGIyjwRZJNSwmbuECiqrPbZ79bEf7nDcB4z1WlGQk3asdgHVprVZV38\nV1NpiCOliLnKYj+WgRaToEHB1r75KV/XybcCB7VVhaXuL9jI+/yYjMDhuIrg\nFehy\r\n=1h0P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.32": {"name": "postcss", "version": "7.0.32", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "4310d6ee347053da3433db2be492883d62cec59d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.32.tgz", "fileCount": 38, "integrity": "sha512-03eXong5NLnNCD05xscnGKGDZ98CyzoqPSMjOe6SuoQY7Z2hIj0Ld1g/O/UQRuOle2aRtiIRDg9tDcTGAkLfKw==", "signatures": [{"sig": "MEQCIEW1GvBd/BWxcTmbnJSq4/FreogI9OH5VV2QYiitt2VOAiAh1cqWkBNKbV7osYfcs41sv5LYKuh+ZWMr419X+bNPeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1089539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe1ksNCRA9TVsSAnZWagAAgUUP/RQrZ25FxIS4P8SbjQQh\njj9t0MBpzENGb7xeP90KEtHHwwaDEbqh8BEan6JLfN32NYM7FK8GFyH1N39V\nUgzHQk1j+ttEMkgmB+r71GSE0Nt5roaQoJHCdyL5W+81XbU0Tag/CATcC9aZ\nnTyPN16oE6D6IWdcaXLUAYrL15lua6cdrb4nRS8xexk0z2rNZEvoa/TXj9TR\neV0rkfwdXhgU+eddBXJeKCYmEAw7k0pPLP9tUsa+QE6M5E7Tj7ki3femEJ1G\nWqFDT50aWDgbxo9JC8twTrMMURJKrOcIfiwLUfi4IA3OozF6QobjSgqVj6zl\nx0d7SsUueUyMwXJZL11lmRlpGkeW6WGn3pV2sC49+RfgolACDS+U9zeV3Piz\nTW1c0saC7RGnEDwomlqFYjiE8uhXrp6Ocg0qII994MJv/jbCIo023cQUp2jg\n71a3cCRzevUt9PTvcBh9Cd6qwn6g/OcSg/rW4eBCVstR/tuhisIO7WU6yjRS\n/FcExwEMzBCHKnX8a3lYlDRKTs2yFT4FYez+svXn9RXilwoUc9zndGp5nPe+\n59ISlRmsEYCAPkhSSB6mj1soY1RLyzXzXJzGxJN0pYN/skyTCB01e3bvXkdq\nYEDT7wp1HeZMbCiSL5xFVNCGVMlXn3iZtqNiBvUTzS/vgbi2v/bFfdzrHDLJ\nhaBJ\r\n=5RN9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.0": {"name": "postcss", "version": "8.0.0", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "53bc930da438d795811f18c7e3a1933579b2bc9a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.0.tgz", "fileCount": 48, "integrity": "sha512-BriaW5AeZHfyuuKhK3Z6yRDKI6NR2TdRWyZcj3+Pk2nczQsMBqavggAzTledsbyexPthW3nFA6XfgCWjZqmVPA==", "signatures": [{"sig": "MEUCIQDXiwr8Jh9k3W4i/ddWtcPwszEaCcs3nAk543aMGgKhcAIgfCFuGuFM2/pYzkvpAoUd7/tnecg3HYV1WdvnDcD+YtE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193830, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYNu2CRA9TVsSAnZWagAA0kMQAJaVmfXda5twlcZvFo08\nni5DNQ983mgehhojhiOhbtowUQYk8AE38HgboAG4eWiUzWqY7T3apP35+Z6p\nAVsPn2AzjQ7ytRS78yIvvzHikC7WDIJcDkWdidgcoUzDiG9WCq/Fb7nTWCh8\np4MIXcTxZVo9Q8leyabIUVBWsf9Mur+quWwXZgpWGR0g54y4lpi1Cb+yBe2q\n/DTgNGsTEhFG05EKI9DWqgK3wNVZ75nbx0+JNsoGH86385rRWPBrQ4URZHYh\n/x3yB2g57E76fVzKDc6FY0fPYQpmsMjRTSh0flXw8NOFQmi5A60P9wVuqXOj\nacmL2FNl14Iuj5XIxizOvQxqT4M/GB96kPQxcXMaJqjgJdJH4fmZUfPd3Xn5\n1GvN/OXtLcidFQil1NOYTHrirfvT+SPgEvevyW5mlPUmQ13TW6xopJpW4RoC\nMGXQCY/QeExqew6SghkWzeebxnlT7JYuEm9tB+//VEr+s698LmCFaf74nmO3\nBiRGBNMVRkfEFaE63E0kDVFfDMOMv2Ot3FLiofG34gtebtwD3z9+y8D4R+PE\n5jOGXjtFMFYPZfGT0l/OpsrjQED1Fm67JW5WEMCEoVyu5UJHm13hzjKKft9w\npw4TxgQ5pwjwExSMkp3VTlj/QT8m6HKk4C6lNc8N6S01uzrfDVfCIo7CZX23\naFxh\r\n=rxat\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.1": {"name": "postcss", "version": "8.0.1", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "6263a2f9dafd7889ee40ea5bff9a86c0cf9ddbd7", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.1.tgz", "fileCount": 48, "integrity": "sha512-meEhwpdhLneP5AcfXDf1guY63DavkuWUBaHck9SNYrsobFKynrbAzdkFIevxAuQkQ9ZTrX7edt6iPJX6P37ZYA==", "signatures": [{"sig": "MEUCIQDTwP/zEowN4uGq/cDH8dP6gmbK7fHAccOBIuJhMDonsQIgL5z9JV9U5dqpqVU+8jl8jthAw+VphrLPBTv3Bh4AVDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYN8WCRA9TVsSAnZWagAARiMQAJaWuVOTamO5+FVxey1a\n42v53tjlCMlsw4YfFPOHJzmZ2ZP25Zm+nPXDIGNF/CKBDlkzotiusPXzOZaA\nugSqDbSW5N2pBJCQ3oPdILBDJphVOGDeOt2uIxyvzQhgCHDxo+MMGTCriHkk\ncIVOz0lUltyJMkgcccQvRmrBfRymAGyRL6ZkmWbMMjQ1WPXD4OhTbyK6BocV\n5uRayWLBl5/P7lC/+m399XBVIOw2JeLyfp9Ygn+qbFTZW+ydPT2v2IC4DjOx\nSTxjsAT8CRL/gYGLLahTdeHDQ+oORaZJD386Oj3ouoWYECRY8R2hLRd0bf0v\nt4eqlrbJ1OCwtD0gm0D7xNexyiPl3ld4Ei2OSAMII05Me5B1xlW+jUwgdIR3\n/T6wUf6YsS5zXogCYrbrFXaKcSaT3BR8cspLv7ZXl/Z3AuTR295+8FABZFdm\n2WUtdvMo8KCOR6jxag78eOt4W6m9a7fvMwK6PgJDmaWx4+ySIh/zMWL1bFXu\nK0Z+Q6FfUb5eLG7VM8cNFpE7o/xWC/UKKk3GQ/57hd2hO6u7FxBOLjxmnMcn\n8t8vO/mp19A9knZvUDyA9KVl9P3xPCHAt98XdiHbuYbWv/ZGXmcIk5gauklL\nN4nhurah5x3ZHgerbYLb5frDVYkQqpFjBbem5awExemD0iNvDvF7qOpR9zW2\nd2ZF\r\n=EDz+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.2": {"name": "postcss", "version": "8.0.2", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "77c96c22a78192424f11050d81c53b4ce3d79a9e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.2.tgz", "fileCount": 48, "integrity": "sha512-6huADw2K8nEmIqFudqrSntnp/J3PvwNn26cRypSJahI2kZHNLLxMxLR6r2jlPc42ZXbPI606rkuzEHKGYU1nKA==", "signatures": [{"sig": "MEUCIDJbDO/LSIa//G5l+F2aLM+GnY5VLlsuhPv/l2DUlp1XAiEA0EoLyRO6Sbciag1HzBiyh/56k7fijjLdbHbNViMtvRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYO/dCRA9TVsSAnZWagAANc4P/imdylojyYpQEanyf6hx\nlyTukQ7jynnvMLJWvSjSEub5o/WmKrxxKyW4v7rNqCI16wsSYoHp3lK2V7SX\nSml5fzUT/9GI6NRoGReaodSxw2JpqsDTCH7Js885+JXrW6HJk3w2SrhOPk3T\nV8bLDFSpt7C/Dr4cygQEt6tIA0VqQxXXwAU2Gpa5LPm2racGXA+ndb9UthQK\nyXezzucewHnBf10zlPZyO2Cjnn/Q/BLvy3vuCHXunjqHR1/LoHfsJGViKMw0\n0rSeFOozNDBsAZ+H3RQmB5j0EEzuul6aUy/cA5OFMjSVKTHKDDcsRhD0FITx\nvOv3wtde/EEkyezs3KSt+FRh+yZzoJp0giKXbhKm7L03gA2ZcnnUKHrehfju\n/o+hYvYD5MFOO2ifDtlLziZn38NUaYhKXVTO+hsgLU6wWGqfQM8EuPUmM4cK\ng4ijguJk0YU7OOdKOmdo2vb8DZxpgfJolIiQKZj3sJZlQQYoJLQWFpVYPl3K\nsZDPUDZ3mCcf6a7VjrmrK7Vc0UOFgMWoNDY8L/2Re/i/xeOD3k7vqS7AWAd9\nu73uGZ+YrQ9+bgwopY2LyWZmcGBJL3MwN0WijUdIzuUCWqyXHyl79ShT3FFv\n3ZPbDy1LSYpBNpttmISQ0/dU9cjO0ABUkScJp2ViJr5bqCEMn1RaLVjEtH4u\nfsep\r\n=nQjd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.3": {"name": "postcss", "version": "8.0.3", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "f1d4658528b778e1c5c67a31a3912faed1600a66", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.3.tgz", "fileCount": 48, "integrity": "sha512-nJR5mHJuiizWhF1DqVDy6RdeFbyA1Pakcl4wp4VwppKoAZLafPhS7X/rjeUgk3LdQWYQNxyixzbCbcIAbhsCTA==", "signatures": [{"sig": "MEQCIGsnA82lFsmrDffCEk5sgWvQUFNdzSPV+PHHQmTdLKTgAiAb/DKq44xyvpdSWWFbtVdKdjU/T89YDKLyqVg0ndob7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYRJpCRA9TVsSAnZWagAAslYP/iaQwBspMTi9LOS74ZYn\nkvo6w+rkraaZHeBFZ6Xhp8i+H+wwyAhWfoAyDzMz/ngY++mfl153SzRJr+B4\n4TLc3Mh3EnxgEzYD6oUhg0D+TbWoZeTrqhkT56XxXtRNkAP0+avGE168wXGP\npB3fPZKHwx2ZeSvWUpcsNY/eiQ2/6k2SvCEltPngV7jWpQf2wDze07pLjJrJ\ngq6+coEtGu2WfIi7fkktPfIYlig9NSA6OYrObb4G61hRg+LlNPU2rUWaUlyp\nCg9hOPsgFUgklu4rgH6vgdBtXRwDUzhqUldq3acsx4NbX4+TjuwBJc6rmTm0\n8Xka0vhBg4Ozxq+I4GyigTzYqY9vBA2cT0fd7Zbfx6z2WEnOI+9bw6f0Z1Xm\nKvhXm9Ywk0hcQLpVeMh5grUTdVU8MXpadf++X0mrxk0gUEoWK06pxYnuQY8S\n81X9matneXahQShMCFEbuqxht+3gGTpbti2cA4afkhDSab9qgw+qwngHwOyl\n4GLARWNE8+IuycsM6jcezByW0OrnCzMg0Xz4fgci+T/dk/BeV152VGQlJKSn\nu9TLwycHt6VhEvfFQRSrhkbmkdJIBXb/eZDRLIZ7nzRKQykjZMgZIt5fMWP1\nIURAayrl6TahvMFWH3zsWk9RKPABwtyHoaBKcEVB5FyW/UbER2jmAFNDgdq1\nR8j5\r\n=8Drs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.33": {"name": "postcss", "version": "7.0.33", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "8aa22158ba133e4b4dd49a35031b75a68215b760", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.33.tgz", "fileCount": 35, "integrity": "sha512-L3CKJMGkaTmz+/1AvXyOKpD7wPoUV0rBa2AdUXrsPBp8KLl5X99rY6gr4wQyMaeTo4wV+JdY7RaxlA5JsLaeig==", "signatures": [{"sig": "MEUCIQDj4uk24tTFWG4MfYeW5qmE7/dFucQYwTklMfcBvOV58gIgRPcWaCe9Zvv38RQhEHyW/XQheH+gnM7Dv8+lCh+JV9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 582390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYo2+CRA9TVsSAnZWagAAIGgQAJPcX6vKAikk/T7RCRug\nfzjhrvbqfMOBZSNjAAHxIxCKzGAfnTeSyRY4tVYOfCczq/zu/9+pYKRMDZn2\niB4TTD9MuOU71G8epyz3QPsdaKE/ovCQRa4BBl++7lKatDtg9338THZ3UIgf\nAdbNyULu5kduvXY2njQ10JwrjnUEpIvKgHFwCr6l1DFFrdbFe53Zxr2jetUj\necWt+lee7DiCRw3NOOgu+TzH48C1Wq3TAiXIFwWWcF1b7z44K0DqM38cabnd\nZz2CByiZafx5JlWIbvxG3IyOewy2dQfMvx2ATBW9m21LmPO8JhTH9oeRF3vh\nny5EWV1zZmyYQR43DJMvEOMYxu25E9Xc8mTqUTRjY5DkhQPE/tFTYS+2Taba\ndv+cAWLv0kmy9SIC4gabq+di85ZBEYKugnFBu8PJWD+AdDaTLd/8WZUJNzRD\nvnmKfi8sKQ1dscKzqzAn5Cg09avzT17KqIHUdRFE4vFB3aJatxP0hB85XXrH\niZlk1zkG/Vo/QA3exFsCMMAtH65Z1H1aefvDF5wRBzz6HbMftflIv6QvOA5j\nondFlKh/uDJsghZF6WCL2Pfjr/D5ehLsfBQZi4Gef/GQY3Zq5EsPyu99wokn\nfnQguW9Ns4P6YMaNgpxrD5oX0UOGhXo8aBmAmLd8Je7vdXtfts79O7MkcMqL\nS5r4\r\n=Vpuz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.4": {"name": "postcss", "version": "8.0.4", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "c2ce10272c83cb86083c59725ba673e445f6bc88", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.4.tgz", "fileCount": 48, "integrity": "sha512-1XnscHU1hafSVykema78TOeBf1my7WThfK2jaSDtOQW90CAny63oEsWVC+EN/WSMSBpghq5PxUdPgozK693tJw==", "signatures": [{"sig": "MEUCIFc7F33opCGb2YA3X7j5EwAyJ0kZPJmJuFlVKuJFmEuYAiEAmf9Du8PfeheEA7sCZjg8LZ5rhoVqaDdWiLTa1PRO3ws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYo+oCRA9TVsSAnZWagAAVx0P/3ns3sBcqtqQ89atsWZu\nFMohdRSuJ6Y+nzNR8AOOlBBvzw1VAXsqNMOGkE0ysjWkNeXj/VxofkbLVX54\nSwx6LyKvsbLGx85bkBVTA4hFEdVGwWxCf7yeCJa4wPPl+d1+eN8DOm0u/1Jn\n2KoaiyC5Y719BMUX5CQ4GyTGp8YIegE/tIrAAyLaSVz2Jx9USSjIae7vbieM\nEewt0KZR0e+wmgwlccGqhkb6gPEB7BIAYO+NPyGKNO0hiWz6b9lUt7HLKZSs\nKiHy9BJnuA0Vp7sysp58htfWr3QdcTD0rGk5PxMLI2H5xP+Mrf4KqM0X6TIM\nxFALVQkrwZLNlVNtqtooMZ3NU2TSR/77LuL1clojTdRYrqrm6drQi2tMES32\nCfatfzNM5Q2C/mzsdI8QJhH69YnwH1MwCeLG+tqZkqBv8Fipi+7/j3X83UU1\nDaYWEnWYC3Nyr24/g379uDv0uiSoG3ioEk7fNzjS/w7RN/jIlmZPAoKM7BSg\npYSE54hLXUrfeAT1OosbyaUsF1zcKYKpllobKe63vtVgsX0C40HlHYiEUjTP\nODM+SZvIj6m028gG+re8fD39ij4WPbX8Or0osHwvRDLIUsNPaQQ+r78pnEwN\n4b3hzpv49NQLkJZ9f+13DiO8RGVYXKDnfyU1qSotRww0kG+4IcZtMfuYMoTO\nweLM\r\n=u4Vr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.34": {"name": "postcss", "version": "7.0.34", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "f2baf57c36010df7de4009940f21532c16d65c20", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.34.tgz", "fileCount": 35, "integrity": "sha512-H/7V2VeNScX9KE83GDrDZNiGT1m2H+UTnlinIzhjlLX9hfMUn1mHNnGeX81a1c8JSBdBvqk7c2ZOG6ZPn5itGw==", "signatures": [{"sig": "MEUCIAsEAMvqWRaCSVQSBoU5Aj1cHnSPlz8UBAQoRVx8qfAvAiEA3Gw4/0Y+fKAIbzrjrTMDS/bLLJNfXrXPDi0LZ8UEHCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 607789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYsI1CRA9TVsSAnZWagAAJ7QP/0EPXT2v0jTJ/Gkoe3oE\n6LNG1vDmZ/wrAgwueSFKhg1FFvj8cEgms5z1w9ArW4T6LYVwzmV9lAt5s3BZ\ncRTBsjnbklaidkEl7FxeRgjQ+4NbutGiYc0aL8QBXOk4m66xmPYCozXPKOBV\nmtqsMckFFPp1nvXm6+MqPw9GcU0uc7oxT0Qe6WHeeeEopsRRTJJKFU3EgxTV\nmjIRSVdT3EQsJ3EUboufBEhpPDic+Fs22WQesIPWsi41wD2sc9PD3xKCB4uE\n23Ov3Of6zslhQUdIqAmsxwJjxUj9dX/GQyq3M26W8dfqwvoBJLBTLxyUwWDM\nqG2jhuaaDo/rS3BmjiOmICGVRvZibHODAtyjSuIPAcvU29aWfRh1dktuGtK3\nwO7UsyI3ZFgjjoQ6tX4P8qGdMtEANFfvPPBJibr4IYjEzCdnofc8FlLCOCHL\nW/IWUEeOJz53IRtSMOy3CrvgI5uksZnb2jRsNiMmuBpGI2SHTxjYPGamByuI\nE10eL2VtX8//LaGYhNrXaAVPKyxvQG0oAzG+9AGfQocq5fmUcqTpUqMjxqQi\n7NJ7uaUrtA1SpRKsptNx0JXwvn7C/m20RSUdv0UIr1tJs1L2w7T+WF9JtvV6\nul3VxEULxCEpDwgkuNKCTOxeW4wkwPI5zb+yu50n017a5s4CrNDNaE2Qrrti\n2PKb\r\n=7CPM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.5": {"name": "postcss", "version": "8.0.5", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "8210e2363f85c35a88a188ce7a0828e93b1088d4", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.5.tgz", "fileCount": 48, "integrity": "sha512-3rDm6KR0jHstte3aL3ugrCyFA1UXY90SWNwRZ2WTmRf/QpOqM35mm0FrRR+HHZQ5fY9+nXFat1nl2ekYJf0P4w==", "signatures": [{"sig": "MEUCIAbAuLa6p5MGUvDwObmOBfFAj90YG1mtk9IdWHXWVnoqAiEAqoBUZYW9DNqi1YRrXfwMv3JkMilsgkU4BMJEBcpQfOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193398, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYs0ZCRA9TVsSAnZWagAAj/0QAJ02+bHGcIV5+NorcfdI\nXoAgSxXMuIqmoScOUHl5MobJp/vmciw+U+e1ptBY4d4pN7Mxpa2L6Ii1oxus\nL2bfih2c0+X4a+oxOU0Tf2JBOGNY6sO+rVWIZBQTRHHxy7TM6oH0JzoweGE1\n7FpRaOhgLf6DwJ8pXBn0PbYR3LIwKOntR3+37qNYWrgzTRjMicVBODLndWOZ\nWGGziwoQwI5rZmMZtyXNaGtb989n+9Jb8woN0u1OgSW8G7K5WPkth5trO5wU\nBMBSZeGAN2VOaPixcgESPr8McmCvcYs7/btjGN/hTdgIdugsY483csyI0F0b\nszJwCY3kuXrXRXZY4ZcuIxWfu1xYEaDPkFtTTmXsoZsw204QctdeqOhpp+1v\ntBA4xcsSBsJh93XRyf8WsaUfclHb6SbtT12eRzUC6Jks+4D/KzU01nwo3wQC\naUgafhDWgmCM/Z4BZOp7P87UcLck+NgknI4L4rlG8rBKPbxrAyZ97Xwn+kM5\nnLc7cHseu6KMNcbxdE2ZAK8OCJCVl+MtugGTzLxqnIePTbYkisYikyCCy0Wr\n8Ncp/gfP4VLLIXgH2dvNmC2bB0Zajyw14YJMlKMiC0T0vA6YR/b19cPjKFQg\no4yqza9pUnknPwuWZ4Moy+3vkDtzIYxGyoMIFbKhRsgzZ+uSeJch+1/Row8k\nw7Jg\r\n=pu0N\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.6": {"name": "postcss", "version": "8.0.6", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "1507160d7df320c77f8f4819cf52491f4748b672", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.6.tgz", "fileCount": 48, "integrity": "sha512-P4lvqkhoiba/w0JE1t432xlQyeJnnf1elrviZOX0GybserV79IN4skB2nQg+NoOmkq30MNqVv9ggdqVmYjaolA==", "signatures": [{"sig": "MEYCIQC8215axnYCxjHwz4MojQuxSr4iywSDYtf3Bi4bxTN1XQIhAK6gK8EaAEB6qrQTZVJm1zLQ/5B1fHlZMQfnSbDV8U2Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZ4klCRA9TVsSAnZWagAANCsP/AkIUNxlxGbYArTQUrUA\nkuKqwU3nOf0UzGhpeT6vD80SEGVfJsRk9m1q4qTGJojMhtknk4dy4+vOGoVs\nZfJsfQy6ogEKaAcd/b+Vh1iG7TsN+2g3YRVK1gdr5Z+GJv0hn+mI/7L0JaXY\nfr63aaZXvHc67YEsn5ScYiKq6Rre/sWK0HwI6c1HHVdo+3DcXjrAMec4zUwj\n9lEI2sln4N2BcMd0v77y7a/vI0nphI/EwYReCi0Msef27nRcZcltys2/jjy6\nBrHOGjeR/CnxYadXpvTqJa4pZRjsptaa6P6j9S1aCer1LegUTV3o/q0FYBWM\n5I2Qy/i8oEI9rZRSKl4KIrnwDHC9oktg3+4q2n4ZzkCoyiipNEyOrKSl+1v/\ndSeli/n9SKpcqqY3JEilPnGnGh7eBB0DE9v2EOGHt1WJJ4pB8ZjTZpnwb75j\n47uOY0YqwNflREizsjbYfdSMvmZ3SFZacYFlcZIikR2Jqh7RT2K5dNFYZ6O1\nvXbS1uQN/57ucWU3lDjmZIASkOCqP+GXNiv+pPXkyxeRvJ84/5str9tQkLAR\nH+1A27wpZB1FaZvOyzKO32RYKGHK8bExLohnohCz7POsHEiUhBdGW4JWCznF\nf7cMbuDn8sP9Yn3vGN3xwBLamt1MjFgLR+TsNXUKHZjV4lFbVbhkdGxMBTQJ\ns4vi\r\n=Xkb7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.7": {"name": "postcss", "version": "8.0.7", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "764d26d8bc64a87af6d945238ae6ef36bf6fc32d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.7.tgz", "fileCount": 48, "integrity": "sha512-LTCMGOjmC/CGWV/azk3h34u6TNj1s9p4XleEiW8yA3j+8k+z3mnv5V7yyREvWDKlkel8GxqhjEZJ+JXWTzKPWw==", "signatures": [{"sig": "MEUCID8KbE/C63Es/d0tYv/ZqLWAxNWD/CLx0kez1PSVQHloAiEAvq5sAPCzC3aAHAthzgdS4nhP5qwjf/YWn3xKxiOfsUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194745, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaUufCRA9TVsSAnZWagAALPEP/2HZbYT3fviIRoiqsHzD\nEnnvqWT/uFzKzBTpJNp5QzU93Sgswxxpm1NuOX/0WNvrQ7jJBbQpwvOcM00/\nf8+EAbhQxfL97azxbjYj0AE1+ZgRzgEE+TaBSvidgx/FQ0RsoZBra6FjVp5F\npDc34Xk0wdWmuMr2DMR+9PTfVx1UEkhBGXPhyfeWgbaanajzkx8eqfBQpmBv\n8+cjyT0d0SE5wFcFa+qABlTTFarvXMXdkrGmRQ+SPTrZCgo8xGtvOiZzt+MY\n6wNoZAbT/B4SuQhC/MJeHS6nZerRPTR++f2PvXtAD/muvTJLfrab3L8aiZ34\ntXnN3AnhhDIbFm/cGkn3oGtOrseXb1OP78kipGZiCT+DALR6xmtdcJ3ONOv/\nCuuiZXpsgrFYjHPXo5aVldAzK69qpzQpDG+Jkk6FZVY+WWLEtlyuyIODwzRp\no3zpnaHEAmfUwbO9VddZDeyIKeZUbdFzlM3XeeI+gtubtcVuzYic7oibAoki\nnWrq/iXFYVdcmDw5IzgPiaYguMT3kh+Ihbu0ibq9CDp1QXUOahn81W8rG+MJ\nDlCdrB1Qlaz9W7FDU23PItgmONsw1HcnH9cCSXX5PPFLNgTaMCc2sNb7w4Bm\nYLYqDk4Smn/94COyc4OiGKQitn+ink4wzs7cNGbTsUoNhs2Xd0nbNjUU4tDl\n3Kp4\r\n=bvjv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.8": {"name": "postcss", "version": "8.0.8", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "5a80323a5107c0411451d1513f17984cfcfcc830", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.8.tgz", "fileCount": 48, "integrity": "sha512-SJpf3Yrghve9ygpOcVhloo6e7q/mkp0YU3FLr3grdZpHmSjgwxTYEIrzkz7cFx27IMLFvIfLBnuw+JrHio14WQ==", "signatures": [{"sig": "MEQCIDKbC/CEqbdQnbQNS2M12LAyaRrGdH8azhnh39KihmdaAiA8YxBgesuhbapPaP398oS3a3AiWnBTdOVQoTMCPfYefw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfaq7XCRA9TVsSAnZWagAA4kQP/jusLJ+FQumrZgFURc3M\nqpzInTteqyX6vIUN/hZDE47jw7oSZ0olQ249uo3J2lyDC3legyP+1/bDWzwj\nLwuFap5XNhwG54BWhTph/0s5ovEEMMBwrvMgbdlvkIZRknsgL+hBLlXZUsey\nEHAwfwUv74BLpbFVI9V3Txoo8gGxUgT2yxUhVZmMoQtcy/5W9pnOVUTkqE5W\n2YVDozjTwO1q/DVpl3oTbCC6fahi8KWJ2LLXVoUubKexr2iZxrHSlwWF2QHc\nK/uv6LnQ5pP8J/M1MUpLxeE7Yx/MfySgpJUKo2bO4kJVnl2Iq3n2Cf2kh/mM\nPH3vbeRXoc99bUqB7EVeEIOlJ65JXpGu/xG3+VQnyNmYYiDdo2OwN8u4QIIp\nMFKlw10OJnuPlFNcJdYfuDgQjeBaoblOYmmB5VCpFcfOyh73T3OJ/V0eDC/y\nOQUVRrcOt9cBbPQaB/N2nF7c5RbtLLnalKR2cmIAR1m/wWml0Eb7z8ToFDC5\nY0kfBD5bAL2QAYt3dJAbjc4vGDlZvJtqqntE+lmjUHWwHkWLh3Hq9CGZKN7s\ne5crd2sVh2PnB7Ah8hQ/d+j1FpmMVoASlJaCSGyia8L5TZpTxdDqOUOXzEl3\n0FoLVBCI+BgCatfiadGD3+DLcsoA2gvH7JEWWEZikOukdy5llIqqvQzM90qd\nOYGd\r\n=rPgi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.0.9": {"name": "postcss", "version": "8.0.9", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "d112fc1e8bbed550657901550fa736ae3dd25ec5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.0.9.tgz", "fileCount": 48, "integrity": "sha512-9Ikq03Hvb/L6dgnOtNOUbcgg9Rsff5uKrI1TyNTQ2ALpa6psZk1Ar3/Hhxv2Q0rECRGDxtcMUTZIQglXozlrDQ==", "signatures": [{"sig": "MEUCIQC96jlvm7Ev9KUUXTzj8sW1zlNt/N2qZq8X0QFcg+zihgIgYzllaJH1GWX2LtQkFQyOXyW67NwqWTDt5/woaes1nHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfa4AiCRA9TVsSAnZWagAAPYUQAIpNAxT6Q+pQep5ijfsz\nhyYqIj7YpQKA4iyjKjYpfeMbuRZcWEwUmk1YJojKwvrZayscf+UmON1pPbWi\nXsHPh80ixwFaSEDMZhALquYI0EJTc46NzEzhEu/suUZMoW9Pw8SezojhqOd9\nZ9ePPGjrFcADNTDLP2IgODOqWo8pQ7EAXNyDsiR9wNBF+FrxxXKP4jEa820/\niMsBmNUSW3omgEDt+184DE1PC+1JI3IfVNW9AqFJH3kRUeadAD7AvRNobUzU\nlT6epsuZM8886ryRr2/6UqwKX6jeJMi+/ju5eZNolXg4WMsZvJWz00WFRph1\nNEKLkDOnmTxsFd/3rVdcchGW4UT5ywWAvqFQh+YvBOuw6FmVu+UJD8dgmnTy\nnE9BZZXHhla+pdA535wA9mYdo0cUVpZczuqalHw5+nc5FNikgJU/s7t4I9c2\npF/reHXGcYkJImlZtuW4/Wrkm0wPTFM0WUM6BCpH2SDfIA6++cvvxxnBT0vZ\n2h1BVXpLJTtx9yDsdAvVP2/nPyJceyyG9Jwgfygmr34bISM0CG/d4iLEjZDU\nBc9dGn7XfpQjNFAtnYxx+EMIdZzStGhbKR6rQyOp9ryAwvuxOGL5os6xlV6C\n12a7gF6DaS00NCe8+BgaB5yDUBKNHebfUfsAUVNx0cjv5M/LLusBkL6giMJ6\nscjl\r\n=d98E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "8.1.0": {"name": "postcss", "version": "8.1.0", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "1be330c7f6971d49726059b9f51785f45273fa70", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.0.tgz", "fileCount": 48, "integrity": "sha512-d3RppIo1DI66oHxA1vdckr5qciQbMIrHvyzuvp2cLJHOLwJHg7X9ncrfw2Ri6Sgiwv/GoXtOwEHJ9E9VSRxXWQ==", "signatures": [{"sig": "MEQCIGLT+jHnUPQlWYcp8Tk1ecd9hffYgWTh7gsxk+dg3t4tAiAL6+4f32SMKMEPVT0Jn0TuVF3vFY17ZQAi6oLI3QrAQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb9AnCRA9TVsSAnZWagAAJLsP+gN2gtML9Qkdhd5bJ5ov\nTCVsgg6Kp7YCPkjDDDVmZyLiGuOB0rOAdXRK5m6tcuANy4se7+mVi61K+tZv\nogJaLSTtqMu9inEEMpFtZ3gu4KjQJNIYGNNEkO2Q6jh41MmWlFFpMdnY8yGl\nPBAUD4KTC0CsmFyWJb2RY7JGqsDstN1gPEGgIV5OTEE+Ij0yFsDK2P7Z7ur3\nunOqrdcOk6alAfJE00rx1kv+zWe6lMxyAfOxY+aiYBEFHn+rR9B41a0DMquD\n9n3sQqTMQyMYDRrpNl1mNqLRxQzBndITZhdYM7lpEwBBmmkL29N1QqawCAJG\nSSWKqM5YoKKL2kyUgWvQs/O0StH+ZeGF/rbTUNCMJnp+s+N/YoPDyjbdMUc3\n4gNnnhNqqDxu4tEqqgaXZUobWJEkSp3/FwEsJKcKejxOa4zPHpXgP/+ZV/7k\nlBugHg/tYBvqpJj4Y8uC10MSLRlaBNQfYaMzUiMi4VRlWTT1qyDKa7fMSTl2\nsfCVZYsiY7CmEtaPyzHVnSDVYFtKzjgyzw6CMTS2scUtyYU2QAcQeAlJybbp\nRqfT975PsfcPcO99p4fMI4WUwtZjqnbetaBjFYIUXiVC0OK1qhX+EDprIlG3\ncUm2XL3RFTyKixg7mC8xSWzwZ1z2W34Xu+HKq0NFEKtZHDOiJg/YxhOpoq/Q\nsmbA\r\n=4y8R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}}, "7.0.35": {"name": "postcss", "version": "7.0.35", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "d2be00b998f7f211d8a276974079f2e92b970e24", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.35.tgz", "fileCount": 35, "integrity": "sha512-3QT8bBJeX/S5zKTTjTCIjRF3If4avAT6kqxcASlTWEtAFCb9NH0OUxNDfgZSWdP5fJnBYCMEWkIFfWeugjzYMg==", "signatures": [{"sig": "MEUCICjB9W9uamAGQ2o2+1uXKO+d05yA4CkQ0O99QZBQBpgoAiEAxke8N5y8dLvUlgXYhCnaypkHU8rB6ieR7bABDF3n8c4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfcljRCRA9TVsSAnZWagAAnRwP/iXPEBZgdWvbUp/Wc6xG\nlfi1DxqlmSfecr8Yw/GLnrLajA1cibfwwnxY1q00gYxzWoBBNslhK/WNdAS2\nsbi3zfXuavP1wNMJkuFCPELM7KR+aZ8qntzZKOdaEqFsrpiwK0Dv4CMoHuFg\nFddCfigz0E1LAeRgtFBTrMZXrKb7TipY3VKULgz3RloaubL1UDI6cAJcQrPI\n0M7XP3kIoBZzqipQK5fogWdYvB1rEaFtMEk8nqzdOKypJX2R+Uukje0vjHGA\nkw1gTqeJPwkQcd1EyV87Chz6WmUZoYdotBWLCvjjzFKSfk3rS1sTo0jVY54h\niGF91Lsy0iL1i54GbLgOzMd7ERR+dhJnlNDwxHIwvEG5usy2SXIqLINCq1Oz\nO+aimp6EHQL6piDbc4jYiRfjyaHUmJAZtmz6lg+auO9UXc38Nzkob7jBzPoR\nWG7MGwwcSsKXMcUz6QTDZhVs60z6rsX0SjB+pVXbpXy6CmHc3fDuc+0e+55P\nf011pnV4yXe4F4OED2YGN7rCksztbs0c1XdpIHNQu8u7Gj1L9CZbMP8gq4JA\nIO7O+VsSeHqhcMyASm3wS5iHKM51mip6io/+1yZfv9p00nOsvndPON+FcF4g\ncb5+dhjniflU8iXuGiVnVFXXxZN5T2FXWnXxeITRShGhLZYnNrUkSRoHqh31\niWGb\r\n=rK86\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.1": {"name": "postcss", "version": "8.1.1", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "c3a287dd10e4f6c84cb3791052b96a5d859c9389", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.1.tgz", "fileCount": 48, "integrity": "sha512-9D<PERSON><PERSON>sjooH3kSNjTZUOt2eIj2ZTW0VI2PZ/3My+8TC7KIbH2OKwUlISfDsf63EP4aiRUt3XkEWMWvyJHvJelEg==", "signatures": [{"sig": "MEUCIQCk90d0aRRMRKjsdMG1mk9W/S/9vbXQRAcmaEZGdSxXlQIgBPX/uMk+npVk7kOR5rbrfvjI3O9ButveaXqyuLj0S3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfclnfCRA9TVsSAnZWagAAwEQP+wVIxcGzDroY362pIpGj\nRGgqAUr6cTfdn6N03VB/h7sJatyYx6AHz5VyHa676B4rcuvDk8SHL+Mrc1ly\nH3fLSUmROIyMWwVAsSpPZfD2w2el/S+n9MlvXwkmo+D7lVvwAftyrC33FuIP\nHK83uadDV7jcSivMUBFpczdOTGA327hJjAqLlgMlJt3SkFQYyJ509EPvk5mV\ncDyGHUclOJn9TPZE6bSmLkqd7JiWlWDKxq0PnVtdHfUW64BxW1v5cWSTZ5Ss\nk2fq3d+KjTi2Xv7oQMwG6PODCLQhX/BxFUG68LhhD2exylIHXGDGx1r1FFuu\nzYT1/SCqqfVs1UliUbCPD4j9DOP02DkbWAnWitPDZdJqHr5HoB6GUmAVGDjz\nucnrrwLlMCV/093D2vJHs5jfY5G4LHRz11kcW2X/RZ6KKh81u6kem6rIpuB8\nCZdOhf8W7QqTM7ocmY/ithPhYTg2O5cwYfz9ZFmMaBG139hw3d4OH+y7xXNe\nOIfF62HmVwJoyEjttENpg5RxYX9BcUculq8XtgMuyijXHEtbICR47Yrk/WOA\nOR+ZrOvPiwyegJEF8tSFIBO5JT/S79Athg6bB1MPv2swe7NvO/wUyDPb4ynt\nOl+YMmgz456nJAlHqAnetr4UdDPiAEl7OgKHe+IRyp2OGXffnlsXmxtS+Ckc\nHKWa\r\n=hJAc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.2": {"name": "postcss", "version": "8.1.2", "dependencies": {"nanoid": "^3.1.12", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "9731fcaa4f7b0bef47121821bdae9eeb609a324c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.2.tgz", "fileCount": 48, "integrity": "sha512-mToqEVFq8jF9TFhlIK4HhE34zknFJuNTgqtsr60vUvrWn+9TIYugCwiV1JZRxCuOrej2jjstun1bn4Bc7/1HkA==", "signatures": [{"sig": "MEQCIDEWY+iBY4cZQjX0ecjE+ynvX9ilr5yBQwVTH3BgBCBoAiBoxUfJ4P3x5iwyv4PergGhxz+aKbONoJL7upoNkphBuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjNeTCRA9TVsSAnZWagAA7CMP/jDky+/5n8IieRZ0gC8l\nujrBD0nB5eYWfUDX+n2yntqt2/fkIAKFFIivQSXU5BcpUeXOsbYf8TpmCjc4\nx+PwrR2/uz0EVb33ywjhqQ/80yQr/npJG27z8+IIW0tme0FVHFtIhsdrK1He\ndCJP/cub29ihUM/q9RS3KPGKkL5nN2wasxRIl/gexyUu6rBir9EoRCI0zZtI\ny3aBfRZRU/cwNV3gkX104SjBEsUMqu8yPbeZrPIN6ph+1YFbZtggDraA2ZLK\nTgNiWMCyZM4YX0tPbruVZ4zfh9JBYLB/5BhEOtBmJ+8OsyQuCtA1QwTIw981\n3N2Ft3GPghKAuQHjLMU7n3PbqP4j5R9WheFivVCEi1spJCrIGmF/9rMI9ZFC\nllyFoPi/bkQGr9cgdxkvqgIg3wnMHgwH6dFoLBfom3wLxCaFIRS9t/bDy27N\nJgM1ZCnNKIhnypzrolUq7elvLeiel8XitQmc2hs2PT0Z7IPN6EjGVYd+PhJQ\naiAwr/OkSA0rCf/nHZ5an0W6Zzv4vMONZvgEzN6BkyCGNPqFpJ9CydwqGb41\nqEBgcMoJchisninx9J1NwvAUXygDo2Hvly8mLu0ByffZv/lY0Dxcog78EDpR\n5eCUMUajxMve7xaZgrURmbgE/ynrGpia2rcAJOQwQOZFSBf+p1n97JrehPG8\nP85N\r\n=b0Js\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.3": {"name": "postcss", "version": "8.1.3", "dependencies": {"nanoid": "^3.1.15", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "b25138b872ca9f9512c218d9d57ebb59015a9c39", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.3.tgz", "fileCount": 48, "integrity": "sha512-AKsHGqd7HmXmL/EgyAjI4Gx719A5yQdt9HzyXrI8M/hzxfumecYS95kfvIt40UZqPVNoEt0Va1M3PG54XtNPbg==", "signatures": [{"sig": "MEUCIQCbsBoMNgXgGBXKxUKGqPAnfCWuErfeYfvWBpjWpF4UWgIgaGH1acgKmpscQAHoDTzGtZTR6bfApAl/uD9sP0BGD34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkj4BCRA9TVsSAnZWagAAtOkP/1Ek6+KK00UNXb3cmy7M\nuyGHvZ/34j5vADhM+xAw75c/+edq6m3NxNM5TyWfXCnXhaAcrQbYsw6cFlmb\nRxMMM5lOKIIxrqRPhs9WeLwGtGN3hMXcwZR7MqfHCARWbeWfAjPi+J4H43wW\n2BsywSDFfCxTNJljoXIIFIM5xyh4aCKe+WXwiUf11iGA52SlM/G0wXimsU4L\ncAFbwhr6KVvfjLfunHTa92HtYuqNFLaLUGLpAsdHNCWM9SizuMiYdgsv2gUU\nacLEVM6mrB4/qlQJX/Sl0XzH9RP8C4eS3rcdb3fIypZlPK4BCr4yKwSYv4vQ\nZdN6Rm9LP+1NxbyT821CfCMO/axFmoxS89R6e0OeNBBb2GbHTdMLlUwpLaNc\njcRcCZ0hprLVB0mSDPVOKnQIk8KyzPPsEArgEstety+9bV2jBbZ0b0NheSts\nK/llW7PfHBWakXBiWt75Lma53UznQxqcRaJmcahoPTRZjSX7crdrgMlc0xg7\nlOQCk5xOw0Jdla4/2MF3icXmGrlWSsnSx8DiCmENe6C/0Nw+OUNZB4XnebLJ\nxcqkffddLQW7ht3+n/UrLd+trOSFPlQqA+VbIsVTdBYVAFCmZbWPU4CJSQWp\nazrZCPltQIuV9wBEimF9EhiNvXiNEDWeaE1UVgVvXslgzQyNsCPRTF1uiQh2\n0Gn3\r\n=fWKn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.4": {"name": "postcss", "version": "8.1.4", "dependencies": {"nanoid": "^3.1.15", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "356dfef367a70f3d04347f74560c85846e20e4c1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.4.tgz", "fileCount": 48, "integrity": "sha512-LfqcwgMq9LOd8pX7K2+r2HPitlIGC5p6PoZhVELlqhh2YGDVcXKpkCseqan73Hrdik6nBd2OvoDPUaP/oMj9hQ==", "signatures": [{"sig": "MEUCIQDAOVDELDOglC3/nbr7H65s6ISyarX2PpL8lRWGp4BrwAIgRf4tSyYEk3I8mV97icZv9fLmHz4R2J2oW34f/0O3ixA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfk29TCRA9TVsSAnZWagAAMOwP/jsnnZXtoIrEk1jHiK87\n/RSTvh1xdTalGyhpBYENUSQVi3frCpwuoFPgkuoOEH2zlZ2cvSeuV4jFxV37\nLNExf2LtKzBd2AXdTXadB5pqB9KyHoYlF5AL9RTmAOUmEzgeTQLjIo0jIhZo\nKCsF8i4krK44Y/U7AdphW7avV9mkyO3hYY945mgvl0WYSXFTP/GnZTUlTKgb\nTUCQy5q6ZuysnXU1LGP9KeKJcy+2J/d4pQx284554CFeN83kGDJWF+yjMY06\nsjYLAaKmsUkhHgsvUyaxl5+1a50/iV+e94BuWrf5as6uyyFazSs9IH7licSd\noBaIt4yKSAz3/+7KVQFPAm+oDSwkYOMusa+r9X8YRRnv8pBUQ2tW0E3fU6/u\njR0SRNNe7G81f+Geo/2qcbIMx+e4G6YOa6QCj+zoP7XJ5yW3UsqB02DHjGb8\nGCYSYii1mUDOJN1AdKe4BpH0D08ktxJ2d4d8mn73sdQKdYBiLToIuCdX4KIR\nVD8aviJ0BEiPSXCxS2tltiqUVL5f4mXSltLsHcP+wrKQRli5dj7nHTFlwvpM\nziYaHM3jf09If0S9TcB/IUjUV1+3uoEghGCtLnIaecK/l+FXqAQzZxVk7Klu\n+PJAXyIu/gyt+J2TBPJMmSW2gIRqj4Z4GMjS9rN4BpPnZ5yLFvn8ay5Tt52z\ndW3p\r\n=nkR7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.5": {"name": "postcss", "version": "8.1.5", "dependencies": {"nanoid": "^3.1.16", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "09ef688ee97fae9b0914bff01ffe5a3dba48bd68", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.5.tgz", "fileCount": 48, "integrity": "sha512-sRcAWfjft1LGYx086un0I7XRnkhtKsNuau08mvrCC2ntuahZrLXe4iQ4PiAtQ+lhFWnTt+0gtA7omE4InCobMg==", "signatures": [{"sig": "MEQCIGSX3VgrQxXkn8L3OjV2uHMaQxVtcsSIOcHQ01tifn4XAiB1KIaGaNJaB9PUF2dWGbU7UAjDWzD8e6+6TDNfjb0lIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpB87CRA9TVsSAnZWagAA+5YP/jaABLi+9711KflgmEPY\n4iijR+CO1vWWqTs5SxFTph9JvlOcJFdLznoguSPaBkNGjCAu52CHYegW2MHO\nBQrvZlQRTm/UO3JmBiBsi+wokEMw2Bed3NlROxMvez+FSXY9zgTkvbGCF92P\nSES3K1HQuETr1DfBpj2Xj9EMuYg+a/oauZFBMFfWD0cAHWzEfVWpvpKaZYz+\nOEDoRBq3Lg84Ard/ORAR0g7CPfxrphOzND/YmyqF86KPcoyurngoXXHx5/S4\npDb6oLduSbKnEbpzcxYW2HxJeOOAUMxtSXCPJ+6TJsvCsGEFSIbcpw3UN61/\nLFhJTkIZawTzQzQeB1R+DRwyRiXESwvznqjnu/ppt/YslNo4Mv60ARZ74gAW\ndS9kyeE8Ou8gqHYYpMylIHGMc7lykjqF0sJcQNfU6Lv0ECr8AL9HHnw6dgjM\ncXXbjhTvNufQleqXFzTmX5JluetPjXQIRUA6qYffvsyL2IN7PgSQvIJ28XEt\nZwZnvZTxP9Vp5lEtUnKCtgtrSPt04aeiWP6BPhbUhXmaAnrGmm2W+LtD1FPh\ncO9Dd2VBpZouV7BtNK05vjCBfFeV1O1uQXV/jc18fkdwfX78zE8utEeD6LZv\nvMSR68hWMrA/bLmZWwNl6Iz3Lc3YR85BcRxMN3t0PiQ7ChEJy7nQYL1QiJLx\nL1a6\r\n=HrM2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.6": {"name": "postcss", "version": "8.1.6", "dependencies": {"nanoid": "^3.1.16", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "b022ba2cfb8701da234d073ed3128c5a384c35ff", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.6.tgz", "fileCount": 48, "integrity": "sha512-JuifSl4h8dJ70SiMXKjzCxhalE6p2TnMHuq9G8ftyXj2jg6SXzqCsEuxMj9RkmJoO5D+Z9YrWunNkxqpRT02qg==", "signatures": [{"sig": "MEYCIQD0hp4AhCCd0+MURSjGm+nysSJbzu8HXym3x9y8J3NDzgIhAP+a5+YEtpvKXLF3kms6RbbVb0sgNMtYBmZovR5X+cLE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfpChJCRA9TVsSAnZWagAApB4QAJXZaQoJT3N/vsGsj5WO\n6MNuRULEfXLFQDyZXtU8xjwtrI8LjlYQUSfVOnC2sAQIfiSIh8YbJIv8mR8k\nQaB9xtQinTsQXSjIvHZI1dgxMgrk8UgzxhJW6wjEfk9qFsfZc7wri+pd4VMF\n033DUpvmY0Rxox4U+l9EDkeXChUInNXbytwW0FoaHwYdlWHPLxdMuJP9zDX0\nnApWI8Qk93YXy8Ixy30y4frutOJgQ9NEvhtsTJ9BXOC2sqHELvLzXpW6oVs+\nvUiMvAxTfAAN3K+3+PfhTJ7BSrmrUFqZU9GVYry0ReFrI2GLRGdRqx2UpdNT\nfKtC7AqpL1CSWzMHSbv7i9xeYO0VBe9R83UEcuC8BWQfzWm9I+7rYhwLaRcS\nsyd/aRl/fSmr1yMahW4aiV976vdbQCOtESCm4WKoi8BsdzVn501cVEmzkPQs\nqs6CK0CPRyCLTlx55tpqQuX7tC0qtaaZhXthLl5h8luNXticbWe+//E6g7H1\ndaX4uGO86tONIur9Znz0s6AbQx+ZM+jOYcXAyY3M0NKz2rR3pyy+QyISr6Ts\nHAutkmqoXUMBil2wHtP2gJu/xFUiDz4CNFvfEz1KZojxwsq3kuqsRJh+Qy2s\n3s/6kMramPFKThO4oPeSRPhsJNX/PT6byhHIf0h7/qNfbmqE2XnvrKiyDBGA\nJAPY\r\n=r5Yl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.7": {"name": "postcss", "version": "8.1.7", "dependencies": {"nanoid": "^3.1.16", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "ff6a82691bd861f3354fd9b17b2332f88171233f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.7.tgz", "fileCount": 48, "integrity": "sha512-llCQW1Pz4MOPwbZLmOddGM9eIJ8Bh7SZ2Oj5sxZva77uVaotYDsYTch1WBTNu7fUY0fpWp0fdt7uW40D4sRiiQ==", "signatures": [{"sig": "MEQCICPp73HHxouyQV8u6fExubPc6k067tPDjsICPUaW0shvAiA/seV4faYVC7+K5FkTPzIbe0sHIRaiDZebc1i1REj8Gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqriWCRA9TVsSAnZWagAAL0gQAIGpDciVcLzE5ibXkYPC\niot6J+h5oG+vQSr+qim0N9NORTKKahpLFg/t1035CfwK1dgo8Q97Ond67Sdm\nuP80lnqP4OSAZ8aPF1rDAm68U2HtWapGZGpo8sFFkghUVAZOqfNzFs1Dn7+a\nTpvh6F2QwemKZxIVSQvaElmIXJ2sC7VeTievCJ1AimFaXOGQ0wfDZ/tECw5+\nASHswj2g0Ca+6xoGCi+7r89kJQBn40Hqc1sWO5B6Nm4HiX9rkSptJ7hABnUh\n//rFz9AleThQKrdhxDgBJb6cQ2HnYpwskyxSrdX4G0ZRysmfCebJmuCYSGR6\nzO6ABWw19+/st8TfLdo1EHJ8VehFmXR0x7DxH6PL1L0cP11hyPENdm3/HWOd\nJ97h5IWFkwRdLoblYDVhOj77nSmQJXvUUfidcIW7NregNxpcJqKUp/yBe9L6\nn56Dsl0arNysVigx4D7xhAYZpofUXCDSbWwNNQkqP67jDwqUwk067MrpXvfP\n7NkQ/2FO5ZejNBUs0TTaQH6qsUf+7vkhih81le5Xvf1cuDscUBFvpxdHex3E\nU1mD29YJ6+4ZZy7M50qWUrZCbZA/Yugsb8eDYbGAu0RRnvv4hmGFK2c0Ey5M\nyHl4nheRraLfEbtLmHJXpIgdQaLkXzHqLYqnqzkQbdqJMlizI+MH/dlrCcAn\nnEF3\r\n=tGQy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.8": {"name": "postcss", "version": "8.1.8", "dependencies": {"nanoid": "^3.1.16", "colorette": "^1.2.1", "source-map": "^0.6.1", "line-column": "^1.0.2"}, "dist": {"shasum": "697439e7276735ecdd2893d2cf8efb2236693ac3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.8.tgz", "fileCount": 48, "integrity": "sha512-hO6jFWBy0QnBBRaw+s0F4hVPKGDICec/nLNEG1D4qqw9/LBzWMkTjckqqELXAo0J42jN8GFZXtgQfezEaoG9gQ==", "signatures": [{"sig": "MEUCIEo+SKry7iAIWzQF0HZ3kHMEidk9uxPLyi4/FwknEty9AiEAjHHhIWyJH+6eAi+VvpgsN8Pbq5tAvaJqEencNm8gVeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJftpYuCRA9TVsSAnZWagAAJgIP+gPz9WJNpNJcocYbkboI\ng5Dj/gsOe5inRwmtS/F5t8Tcqh5F9X5x6upjogQNlad3wNFrt8Ce0k7XSimd\n0K1n7zLWRjD60YM4nOGJZcF8QoGC4JXAFnPDURsAPsf39AzHL7m+vZMl5J+C\njCH2ntFpWWvvPypKkPVjU1C80sd+cxmkiRwiOokBJAkeF3DyLoHQHLdOplyz\nEPn761kJ1vdxAhSOE00NIBcm3xe0+IzNu9CVWasKxsF+RsSHL4mSMBFDQz7g\n92zFon2u8YTRG/D0i5bqsH9ttaL/K3/sRjIxqsEY3FHjQ9q21DacbPa0ZvRX\nI2fvCBvmcAeeNBkzFPTlhExmo/LUS6oiUlj3YW6Z5PVMdSzKFgsxHMdLuoVu\ne0a/wZso/2ya3hjFK8Zjo/FgJ82A6ydugZDZpVEYdlqL4GqH2jKER1dY4yAy\nneD2mETNfgLSrhDvyYbqh2gFzqtCIQuOK8V+/QfqNQlUnGpSDfbNx5nWHaWF\n5r6OQhNXsUkWSBBuBpCbTuj43gNwBgZ+udmopHrfpW9yyP+5Kpkxjc+L2trK\n/vpadYsfnLF+jhNZ/FWK9nYeGHz53Z6GgYBu4lv+xE06ADE6CUjcEf217DHV\nsTLLAa09eIueSHkxMoxpjzV7ErAPq8rh1Y4mUsdAau9Voai4UvCb2gfcQh77\noK2b\r\n=z98w\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.9": {"name": "postcss", "version": "8.1.9", "dependencies": {"nanoid": "^3.1.18", "colorette": "^1.2.1", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "dist": {"shasum": "20ff4b598a6f5015c5f7fe524b8ed5313d7ecade", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.9.tgz", "fileCount": 48, "integrity": "sha512-oWuBpEl1meaMKkQXn0ic78TUrgsMvrAZLE/6ZY0H3LTteq2O3L8PGWwMbPLctpksTJIHjQeossMUMNQW7qRIHQ==", "signatures": [{"sig": "MEQCIGeFtIGEowoZnJ7eaF3tWJ0x/lEkWEuzHHt0NEQEVYc8AiAr5tk4kKoYZgBlwImSo7cBepHu7j1K2sasNtXW/IOiBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198343, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuV5zCRA9TVsSAnZWagAAgKsP/iYChZPQSkR2matukDqp\nZb14MCcN7xuFWPUPrnWVoy6c4Sjmk7xCpe7t5YWsyGNOBKreqyPhdqkmG4lC\nZ8jpAk+LDyBudigrifleFFRqmx2cS37oD94ikI5U/ECRx89E5gvd/CU0sDYj\nhiKU94DjMDQoAC+zjuD7wNX/Hy+9n/Q8bhgO+DO74CAPzaQSosEfwwMabVYj\n2/nACkgfJ/XH0sDAGOSxy5Jl62spPjjIkhSIIevPuzhlwlmZW7fvvTxWf/OY\neo7eKpyblPXFAE5qGQmvHw6b/TTFAQ1C++TyWwWogZRi8IARQqABGYgt/0K9\nV5n1gY1ByxIdyvkNjjntIswFHyhXAI2ng6H7hfq6bx3ArOXlI/H6DXSBP/dj\nmiOIWzaq11uXQTkU/DF4Y3nLyh8OEGIJKc2Nrv4eW4peQx98H6grIxdGttp7\n0UwV4JqhxHczqKfFekDMjt6ZMfqNgCVuJ9Wg/7sQDIN8zylJfm3k/WfGNsfa\nhGRgm79uwEnhxkphtZzvEJKHX+7VX9p/kmlNfsrEjpmzafVt1H7P9Ck0bE80\nVgLMO4DiSmKGjasBRG6fZCWU7+/JWtoWjaLDN+T8F5RP3QI8cSBN3nRwqUKI\nG/EWnlchwAN27m4JfGLjwMyHAvOIm2aM9CGEopvh/+G1DRrsYvqSjRGgl8Ld\nEk5M\r\n=3Ek6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.10": {"name": "postcss", "version": "8.1.10", "dependencies": {"nanoid": "^3.1.18", "colorette": "^1.2.1", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "dist": {"shasum": "129834f94c720554d2cfdaeb27d5542ac4a026ea", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.10.tgz", "fileCount": 48, "integrity": "sha512-iBXEV5VTTYaRRdxiFYzTtuv2lGMQBExqkZKSzkJe+Fl6rvQrA/49UVGKqB+LG54hpW/TtDBMGds8j33GFNW7pg==", "signatures": [{"sig": "MEYCIQCZSJkctH76RnkXXqheafktIpm3YbABsDFRAeZ/ewz/fAIhAOvGvTAiDRvEELTjr+vQlAvIVoiVq691GPOzc9gLtcbq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfvDUMCRA9TVsSAnZWagAAIxIQAJnjN0ZI0ALjzcsFcONk\npmxgQq9VE1fvz/h0JeAUIISRN4M3/o0VH1Zuu8SNC0g+zFE6uUx7QiLgtk86\nM5/THHc/mF9eGFVDcGO+tb1r5/FvyBIa3iTVat4zVlI5+2uT4ts2ctxbCDrX\npLnDoxz43Ja/k7HBgVaTfs84mQsCBTfzLwPeCkB+IbOpawDq2QtarGB2wTZA\nz/9TG5a5zX+JHVzlEuld2rm8CtHu0qIB8FKfimfXMLqlljL1vsy/m+Wn1HWs\npURoIrN4gyhXvUcUqe63IHwzsA3MYLaknT+jK+dGN+MOZr+GBxfv5AZ3AjVo\ntbNOLgmZL1wZ8rb9DV6vI82tKWME8UbXj3k+AUVFt/95MbnZnmAz+owTBe7r\nVIPiWPszU0fnPfXhLmc5659hCIRXp/6wxh1DtTTX2j8NWOCK35xD5tehg4Gq\nFynu6+TkjA5iRxKdJk2wfR6QhOmhvMyYX6IA/WVyvsRlI3//uY2U6dLVRo3w\ntwOv0PikbZ7NJBSpR13BozGFpcgpfDnDVRqSQiv8aEAAhLqZqXizVDpnjHZD\nRO0zwzPPO0Q9HdZbNkTfM5mx/aENs9K+rWeVTaGdiLaRlckR00h1gNElqiNk\ncb0EthKKzSGbZ79v7CZDNZDZeF9evjcsWHIVNEyRP1K6zu+CGZa3pGJLfHeJ\nMemO\r\n=v4Fj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.11": {"name": "postcss", "version": "8.1.11", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "dist": {"shasum": "e3bc521cc268107e7be768c76a2f479a08284914", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.11.tgz", "fileCount": 48, "integrity": "sha512-KxaNGWQHUQLtgobmCKOsKoMX0x2nyQXAH2KYhL0xi4Ygl3y1ETq3exmsyCK84HHhEfvtNk1k3Pq32G3sVtYdTQ==", "signatures": [{"sig": "MEYCIQCtJPKFYro89Uc/qsnE54aojoRihzFdrDo035pcues5+AIhAK59evBAZn3Kpzud8T40sqnH3II5JgZL+Sh4es40CSvM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyELyCRA9TVsSAnZWagAAyXEP/i0OhHXcBXYaZ0x54bpF\npUSJOTFQYfjZLn19QPPd7UnMs4+RqhNCjhAnkwm1KhptumTqVKz8nFADQzQE\nRW04W816PtB2obwVWCOVZcVHpCt/RSWcjLuLhccZI7cj727HR+O2yhIa+5X3\nClVJtcQYdGNPFBusaBc2RjQ79DJJVs+W2d7Ka/Hc9l55wzVp7SSt2u/TBKBN\nbJIW4dLx1LFAz4serxhs/bJbfygnlqXORgRSyifLR1M5ZJcISpJA6C3KJN3g\nJELOtnXiRkanJPRi4XQKUTad7Q/Cf3wyYgtLT5HHT5o78z8q8tXjxKtHvkLn\npGiQPdpguX3dx7gDNpo7gmaxhx2d0i1zKsz3Ue8oQgBr4qej4fVIyBRAOoHa\n/oHSqQctQbv0cDLCos56AaOtglHKLc/hTdBkL0oLgg3tfnoLEUI8T1eI479w\nRP92U258f3iQLgjquKHBo4dsgF79mIjFMyq9/33keZPf5xz6//dVmdZ0iyUs\nvmByhIsGR7dHz6gsAP4nxBWLUpAsnjnqUWX0r/557otwbFVx2NHBQR9zJMvo\niF7BGaMlS9o1RVxCm+CCUaxRx6pMQX/qC8zoYiNWGSTC/hih5HLohRE2jv9q\n9wMUo9XNKtrARGZ4xAEj7rlqBjhnDfI+jumzcPvmG7EMlj3CmSzFuB0EyjmR\nZydw\r\n=JvX3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.12": {"name": "postcss", "version": "8.1.12", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "dist": {"shasum": "4d234d588ed6444e7b4e76236ab4169638ab89af", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.12.tgz", "fileCount": 48, "integrity": "sha512-FHjbAbrVLVxizeQhr3yiGnmtj1O2dhUIZSJWhJN2/ZJrcKlAbc5ysDjB/nVIhcfaj0eUlx0knDCrN/gkOe4xTA==", "signatures": [{"sig": "MEQCIGLVsvMc3q6Mv40zJZFwdXyK8z88uhRXb1mPd8N/y7iRAiBmEKWzV1Ft4e/xFmZYJxa8a6GuNW66DQXonZ2WcK/R2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyFBECRA9TVsSAnZWagAAwigP/inVYVBkRwl+2b3C3E8t\njyJoYb29bbjcgBW7MRpUjtNs5S9KVJ3H9kBCyVpBNORMq9KE6sTWGB8uASJG\nlH7PndYBMrrMNHieMeqzZiXR/iG9atsQsRdvz/QBGQH07w3sEsrAL2zHgnfH\nFxV8cdEGWd9+EbSZeXBvJmGQY+f5j7i+mTYevNCAJNo77v/mYbZaFu48PkcA\nV66uppq4DCc8Lo0mKISE69CgUJ31binGJ40wLHEjXP8Y2VhingZvzRPSeuSp\nNuk7WTlzCgcSqcrzR9z8nU0HM9Hd06jGwicH7b07JO37AxKxAzsKUeHgbNvj\nFPfmd2Fek63j+rt256Lf6b0JbLnT5omAYcF0NJE9gEAo6CpRweEP5GBf9dXx\n6dSYivQ2JfxZ5URroje7GYPj60V8gEDXuH9VF4TRMCtlJSBmWhwvVc2URR0F\nqAKvqxdZbSq16ltMwz7r8qKs6JOsNvHtuzNGm0MFAn8OZVPxDTghIVyBu+CM\nQgLl1LteY8ZmC48Ax2T/IJW9K6KrVtNCGSA/mOKepugEXJ8CVqibDxhcT389\njjAn4O8fVYsRH1o+MQ8//CdUJKjVg91Kw0Om55Vz4MmEjyNe7qTUo70Eopsg\nHq1GEZOc/7YcE0ymXqYs1VQoy3aMho/I9DujTWckbdJJeoemi7xX/zN4lf5M\n4WOf\r\n=YtGT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.13": {"name": "postcss", "version": "8.1.13", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1", "vfile-location": "^3.2.0"}, "dist": {"shasum": "5d2aa2f96b3eaab5edc2ef488b4bd636c680f712", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.13.tgz", "fileCount": 48, "integrity": "sha512-D++wck3XhQkICYfysZRx90JCDgaTTdq8YxLswB9cjBzfxwcVcQjn3LsjXon0PQ6TIOof2IGWLRHhSgXZuJct4w==", "signatures": [{"sig": "MEQCIEi14YVla+qE4yKIQ2YtPYwnJ8MD7nn1kes2fLtsVqHUAiAEXOjFwLSnO/pecoPFFSYAIJB+nSdBrJYNuI9JNkUKOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyFghCRA9TVsSAnZWagAAwY4QAIjMych4wjG1xnmzKWXo\nsk4rmbzs8PBeM3y5ly5FQ+Cnp1EbxfMfDNs3Y4B5NItN1oUrqSSyuF11U70U\nHbrNaS0flaOS9ahj08L03ZhVbTZoSoQshBRlSVVmdYehAQYF6A0z5fMN1n9P\ni2yythaG+U+KSrmjx+ZrlK3xx+0sSSXTKRn3R9eh11xaYbcfTSoBMcQd3+YC\no7CK47Fp6Jpde4I0M4Cu80oPcm7hK7Marag3IqoV5sWbKtk9n6QBhl7Kh1HY\nBOpesP2ViQv4lqCeE6PnUfvxPZACnO7zg9dj3ImC4jz1wI8kKdVgWFeS0j7K\nWAGHhT1ALUc7gk+5HofiS2FEhufaCFW6BVTFN6GAmcVjNYSuOHgeZQSkDqcY\neZyI35DrcCjnm2v2NyBcguwPxKcd/fZSXD3FMgFrdAZ5UvnAVVaaA2cEgW4G\n3cL5s51Goj5N0wWYKf/NORKj5p9Fp5vynglMsJNDARKTBWy6nHxpWaSSx74v\nyaNdoQM+qpXuSLdHBQqc9kt6ufPL1uGzIuj9YKuNsEHCYIuEUPpffgMfMQQf\nzI9xIgFjXWmA/rQ3IALMnydi26w8lucnGNLmw7iJQMKNVdQN9SHctfWfvByv\nBaBbhX81X6DNSTgLLUdcAtkaOYB1F+XhzmXc9gZmy0ZM5ZYae0KjwerSIx0q\nP/rA\r\n=+d+I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.1.14": {"name": "postcss", "version": "8.1.14", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "77d6a5db2fdc0afa918e24af5323a53fb8727f2e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.1.14.tgz", "fileCount": 48, "integrity": "sha512-KatkyVPBKfENS+c3dpXJoDXnDD5UZs5exAnDksLqaRJPKwYphEPZt4N0m0i049v2/BtWVQibAhxW4ilXXcolpA==", "signatures": [{"sig": "MEUCIQCDUCo6qiJ+kH7H1DsKsTkig1tLkiHHQoKXUmqcxl8/XAIgCCJ4Aj3sWBWSVDiPpTVp1KY0sFczpJ1Dog8ElQXeWVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyYDzCRA9TVsSAnZWagAAN+wP/AriS+cggBgtgcHt/ixx\nRxkdQBWgT/u0kDtRn0rtM9Dg+ErbkM/beSjt6y+Eu6hCuQv2jVMtd6vjrATK\ntpj+5C/p0ubWsbTSarEVOaa02AqmvncYCN3N3aX+R63zV9Jg2/MGaaaEqlad\nDWy79rIN+nw2qzAW4qWzS4JB/X2orwsgh5F4kx+f3Y3uH3/dJ/+cdkpErWeC\nre1K2NgWT7It/Qu6xCSHmYI/F1RLv13c/lBBCvU9p7mc87X0oRPJe+zOEaR/\nvRqwuJpLSaccSEX+UC+B3adW2Vo4Vff1IqPHzghnjjhdXxJxVQs7gZcad+9s\n8iDM4ow5HMoeti92PNG5ehQjhbwK23JZTnLtf6zuqhYiiXJnoPjTjZ/Ax5Hk\nJ19UuQg12xEGO3LvXUUorqZLcWJCj4yY39PHj2lI7x+MSfNWrAuvYMZsAZdp\n9sFe1kCom5uM3P7/kqnR9AuNeddijrXtlSmzyy+2ALps/CvpJSQeQKkuwncL\nKytFHvdjHgPGb2LRh8gNSKcTOZ2Xzt0N0wd8GVDp8+ECsqvEUlFQP2xotTZB\n1f3wt8lx5SJNXIpjwy+xk6a6aEMS5z3ejXMagTvDV5CakH2GSAvUw8lgvaFb\nJuWEzDbgoPdpyxwXq/DjhqD3vY4DmVF2mVOvW1Mj2dPAjHsg1CKskUJsU8Cx\nk3BB\r\n=8eiu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.0": {"name": "postcss", "version": "8.2.0", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "214be4eda36db762eb8a89d7c7362b9341156eb5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.0.tgz", "fileCount": 50, "integrity": "sha512-vZ8cb6AlN53hHlnPvR+oj7fA46LU05Ysv7O7sNh1ixQrCbtx+d8/h+5tDER9XAccVhkf3aYskAiWmh0DdjNLbw==", "signatures": [{"sig": "MEUCIH1eEXLXRzdB0U/thYdrPAnf3jzCktQR9EjDNbbWx5znAiEAyp5S9LpaEAZnejUr6kceWxZdO4UL127TSZJMlgj+YwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 202405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzyg6CRA9TVsSAnZWagAAhxQP+wSRm5MPDc7CLGXijGhy\nkdMRJVH6dk94g4Gng9oB8Xl6oq6ymUoi0UHIJ/lsUddaXnWObjoWZup7xvbC\nJQ4f0Q9eIk7bbr5pkQWYBBxK0W2UW1GFBiD0XzzcU7gc7aTV9ta4mXaY+Di3\nNntwRPGdh3IFtMKjtYrttN7VnfRca5NK6XPsy7rYTqrVq6B5bKWv2Quy+boK\nsQDemgZ2kZgQeLs4/odt2ApiJsu1W53eGt6b2p7VB/7OLdcLPsrj1MF+Qs9u\nwKZwl+FUOZDNeUhYOaG8g5oYvemIj5aWXzgo6fLNbyKn5i9c8NSkI4FwE6LF\nCFrJHH8sFkQAE0FvrDvWuok1JYif+MgH1jqvuoxduebGGABotBUkDL7nFdaS\n/yW0pzwnNIQZCtlbaE5QrEIdKUS3/RoFzWvAiwfqM8j7lk4ocP4bbr7tHVBp\ndnPWgRoOr8xAku5AkC7gV2zitz+Psyz633pxuKM6+KuqKu/L564d3oWtN3Nr\njWN5Uj5od8EM0IqNwx/QcHULgamX/VTO4TsH61mCvWAL8M2hYqArDcjbj3R0\naozMNEosL/zu3FR0A9LyasTXvjhnAHNHulNGDvd4LmJlHJkWACmGTa3y4sh7\nRsEm1l+Chk+Zh41cR/V6HeLTf/d1cSM2ytZvYWdFV8iqAzKNVk+DE0pyCeS9\nOQga\r\n=iE+l\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.1": {"name": "postcss", "version": "8.2.1", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "eabc5557c4558059b9d9e5b15bce7ffa9089c2a8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.1.tgz", "fileCount": 50, "integrity": "sha512-RhsqOOAQzTgh1UB/IZdca7F9WDb7SUCR2Vnv1x7DbvuuggQIpoDwjK+q0rzoPffhYvWNKX5JSwS4so4K3UC6vA==", "signatures": [{"sig": "MEUCIEPuOASAL5K/JuO/EDUyB6R3HJnYN6pZ5jCg2javCXBRAiEA9lZyOxKtPQXxFWjeGct+7KD1A7km6mrh6nhtnrfZbJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203037, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0Lp1CRA9TVsSAnZWagAAEx4P/2yiQ3/8MPnVHFVmItFV\n7zCrq3ph1LFKBFvZcfPojGfLRfU0tuNAMY+/e7uUIORwjs0VwWAG5+EEkVnN\nCf0gplieVAEOWKmVi4auPAeIfjMrtB8/2Ahjl66IUTWdu6ngTFRaks9d6/fJ\n+rf9XKfnVeObYfhVr7IuI4wOyFu94jh8rP4A6HVIU1Jc3Hss1L9FXCZZBxWU\n/EKlbQI/mKHCFQIPSHrVOFp8UB9P+7kHosniQnH8YCtGKyJc76kXucf12gQ7\n4RKr0XMBDGJlAsfppUZP3449Yh+GJoNGIfkkhegZwGM1JZ1jLzmpCDxm1vK7\nU7VlJS23x7vQKsILRx4thGVPt1S8lKvk3mXdMmpKJoifE+SIqRbF8HKRAzfk\nr9yfE0QX0vhS3nGrrglozWcQH/TjlX+znkfxtq+ExbuHY+XzAKElsmbqcxFN\nX1bfwiCzw9Tt2QP1tDWaWue9TCVtf4MqGVmQMhZ3QV6bz93dXoGYTWQeEBEr\nvgpTJY+Nvkdn/8i6W66OHuzYe+qbffNH9Ru3NpeMqW5uSg/YklvivPjP+Wds\naPFvdTgnUq3XN7s5IgMrF1QPEXkCBwlc664w6JqVhKy/of7jQk2tON1gETLN\nuqEUVkoOthp3Xh4v3RzQ5aSoAnuoYaBM+JekcM7VQo6WwIBVjAjfKCZrFGqj\nDpHO\r\n=G2w7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.2": {"name": "postcss", "version": "8.2.2", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "60613b62297005084fd21024a68637798864fe26", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.2.tgz", "fileCount": 50, "integrity": "sha512-HM1NDNWLgglJPQQMNwvLxgH2KcrKZklKLi/xXYIOaqQB57p/pDWEJNS83PVICYsn1Dg/9C26TiejNr422/ePaQ==", "signatures": [{"sig": "MEQCICpJKxKfAYgH4qKUgvxpPhJLqW0YZKKpvF+Orks5UcQqAiAJDHHF8KBI+zmHLIzyHIOJzPlL/2zGLvKIOjueW9NRvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf65gaCRA9TVsSAnZWagAAxXcP/29wjAdBmGFfl39jpEKq\n+b/O4egzzJLYoVCkOdW+yZaV8gjXxbdi4bkZIn3CUnXzDxlOc5LSd3awnNkY\nLkJjykFHToqQ8nhqh+58znXU+V27dxcA4OM+XstQ1ZhUs+010RP3cTosKgjO\nL2yEbyaB033jIrKRT7Q5sg8SAVMYWy5kvJBg9N6Q0es+zEo0FM2GDsiyXLRG\nNyK9TgqvsKB+Hv/jRsUk7NQNpkeBUlHiat6tsM6tOpoKCyDCCoFh688Ye7Mw\nEVlKK66vDDtouqP10RbSwKRsVKDT3fPM9EQw9T3oT/GVsr+pB3IQD5gj2ppB\nKgg0O36umUC7lZxmfC9HEcN1imugoxAFrONQ9YkFO+J/81pz0ody8SzUTUWZ\nOGXCuf2m1xNdGUXMhyLJ0a2BGt6BiCArHmpp38Ex8uA09a0pCQbe2F5gfHEe\nAk69xMyA95w+oAbmoMjcnTK2jJ6kaDpqH0Ckf7i6UbyuAOZMDvkhhwaEMuMJ\n95duxcNogxefjNtR5WjyMf6we2Mlod077urfrGsKZFbTSKKJDbDhpQ1MfVmz\n28ayRKdt7EiL24Dni5wKlKb3QRZn37H9rw6/hKoKCiTukhZXGIwoX8ymbotD\n/Rs66F1Y/qpS6nMfXFGd8rPiCodfATxZu7XplcW85DQ99iAaM9v7Ja7XbpVs\nfp/T\r\n=Japl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.3": {"name": "postcss", "version": "8.2.3", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "14ed1294850c99661761d9cb68863718eb75690d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.3.tgz", "fileCount": 50, "integrity": "sha512-tdmNCCmxJEsLZNj810qlj8QbvnUNKFL9A5doV+uHrGGK/YNKWEslrytnHDWr9M/GgGjfUFwXCRbxd/b6IoRBXQ==", "signatures": [{"sig": "MEQCIGagGe/tsP2AS8jXH/orueOyxDkBZwK9BOLGlZvm3PmIAiB4AWYUSEkWGX12MqO11pR+6WMeXIOf7WDWAIbVqoCDig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9vlNCRA9TVsSAnZWagAAu3YQAIfx0rEYnBKZ/mXg7/1e\n/brrpw+yk1VKb6mABHvMqa7giPMJGnY5M6oyovLZDiNkLwthmlDu3TGR1mOz\npzCMpNih39E5qXQRM6FvyIltMA1IUCu7IPhmQThv9vcV91xf+lragD6kNx8+\nYsbeK41Ijqb/hV7j4xSDmpi03NS5AJ3HrFiFV7lMDC/5yJXhz+XLkZ76aew8\nakFprl1YHRO0QTt5ljGOm5EyBn1vLq+Cu5cbCXBRSTf7EWBtVwjO3vqE1uka\nOeNnwPOznMLoQqFFY8r4oat1Iw6AEbkWoRGrojBX5WmYi/jhBuek4N8WnGRk\nrtNOolxqR/u1ctaDjCJXUkN15NMmJ2exTyZexd/JPlk8ObsHhfRsJNOp/9H+\nQRa2C5c6ZMG7WAVWcSLy6pC0nSJIFsTG8PBjzQxrb9Kw12jgXyNbQcOnk53b\nT92f8bU5zM5JhAxmwUijL5CbLuJ+qWLFseAW2Up8Epl0xebsf56rnG1Dq26A\neTRj2HqTGhkxJakPkmKBXBNJ/cshHFXpORiF3B0qWa002jIIvtV/6eg2NITw\nadNa9XgcVhARREiZ+b2LV7zbmdYqxdd3QC7rUEVfDnaEG33XhcHe1zEjMkiZ\ns/utAd5oNz1ST685UrCcWr8YOMktvLhi/SaPQ/bpF2FuBKVu6M2EaNdw1Zhn\n86Cd\r\n=Ebnl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.4": {"name": "postcss", "version": "8.2.4", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "20a98a39cf303d15129c2865a9ec37eda0031d04", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.4.tgz", "fileCount": 50, "integrity": "sha512-kRFftRoExRVXZlwUuay9iC824qmXPcQQVzAjbCCgjpXnkdMCJYBu2gTwAaFBzv8ewND6O8xFb3aELmEkh9zTzg==", "signatures": [{"sig": "MEQCICgmOtaXIxOlPCCNJquBRPk2Qg1e3mVcuLSteVSf86+AAiAUBuCyQ/lo9oIZKj2DVSCR+qQVt3A92MjUcFbr5oZVZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+YVLCRA9TVsSAnZWagAAgcYQAJfqDMRcrCvLJhA6C+aj\nk0DwsK/hy0WfrFduCGPudY2OBHDIbrj342hROhqVP5y9V24ytlsGlZsN3+s4\ngn/qp9dM5G39WDZ873thOMHO5X41L0TMGf540AHYSyLapOBDSE1wbl2X2LJ8\nGfBQ7FyXWuooRxigJ61tIZ5CDTcautlXCzQGaKoTcNfcu8mSrsSVGIqAlIWh\n/GMDHdE1/ZK53m0gvV8ZdNKjciTJehelJZ73mgKK91NuBW/XViPMlLg+EV4K\nW1JQePkRrrcDCXGMrdZOT46bsfrrhQaoBFhsKgKFDI6Jq0BiB31XFLpRtdzo\ncX/DKgWlOFKKPlaKujtcAJtFtqTuWI+e+2IOsP3GjQEAFL1K9F9/TH2hBHr9\nTWwXaCTFx+hf4qNZsBYYq4LP6X0tfj+zzHuBsjtgMjf5XTxQU6alXg0d+XjV\nz7zAa57a5RN4GksP0rEnFAlUBOThaQ3HcoUOgKE1tIfl6vvLQWElatAaH/i/\nYpIyq3Zo/4xM8mlBJe+zowse8YXu/+VNfOPUjfBZmJoQxpYaWEYBDN1yj0H5\n/ZFRiIpccSCYP22kk1X8021tt12hzct8M/l3ZdsawHByatcg6dFHDWKIHd0I\ncv1H0ohSfTI7H5pEjtGT+zkbjdN4s3me4/Ugi20uwxlP6J+wDAlUBLsfkgb6\nE+Jo\r\n=Lk7p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.5": {"name": "postcss", "version": "8.2.5", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "3c75149ada4e93db9521913654c0144517f77c9a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.5.tgz", "fileCount": 50, "integrity": "sha512-wMcb7BpDcm3gxQOQx46NDNT36Kk0Ao6PJLLI2ed5vehbbbxCEuslSQzbQ2sfSKy+gkYxhWcGWSeaK+gwm4KIZg==", "signatures": [{"sig": "MEQCIGLUJKxAoSsmQjFvEE19FQwfy5ZBbaM/keJ6WdO3sbaMAiAuNgXSjp0nlsgnja8sCnCrAE0CJgBmx0gSQMWr531m3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHuaiCRA9TVsSAnZWagAAK6cP/1zAi2dAkpgTcU01Hr+I\nTAz8PrsR+Eo4aUvTEdyflsFMVU2Ys10VyQGUYSpa1QLDIKxtbZuILUjc1A2a\nfmhWOau3g/7sCb6V6IKpAMkmx+7GenVGcJpIt+0/42HR/D384V2xX5DkTOsL\nWwCTXrPRarfqHyJOJht2DAXb1RFXnLcOn13KkPvpDjqOcpDvJqKmTBVSsb7v\nR3USjGP+ci4okm8iNAQ+YAFgGVPxLwIl/acqPmgPaP1oI2PAOos/136m65k7\nL6kMQ/YrQ2vPvsAgU2MLqo3cB7qespO2WgStQ548yfPepXrkd9mRWlOBzPzd\nTY35rhz+Md9r/AbzpZxluFEkKQGameQft6IU8EU1IY411si5Xbzp5TKgrH1C\nG06oUfpz38zBoFFJovTh8Cc8hhaEyxuKZMU4v73OM+YApwgY1nmKxegwMm1c\nZ0BRqwt/yGXE6awOMXQ25RQ9tGXuzdrTmtPbjPDAhHFua6Y/22VCn7VFn20e\nvRT9/DMeSYuQzzdfeVnwkVyM6lkfWk81op0XBeKh7/+nVA6YNgSgmUJIhDoe\nuXVZCr2NhnSrSwLJNb5MlMQquoNOGU4SwKWjh5skuUfV0j6qyshy/SxBARtM\ntwSTgl2TI8nWloepbLStMmEx5Ihity7PB7g+4gXxPVj3XhRuCuF/F6O1os+F\n8cPe\r\n=fEJ3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.6": {"name": "postcss", "version": "8.2.6", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "5d69a974543b45f87e464bc4c3e392a97d6be9fe", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.6.tgz", "fileCount": 50, "integrity": "sha512-xpB8qYxgPuly166AGlpRjUdEYtmOWx2iCwGmrv4vqZL9YPVviDVPZPRXxnXr6xPZOdxQ9lp3ZBFCRgWJ7LE3Sg==", "signatures": [{"sig": "MEUCIBk5qVUnxiroMnZZifRf7RNEJUXY9wDk8bHFjNqtHgVCAiEAx2AUf7Bwm3PtBJpRaiErW3xqbX7KgXJZmid//EY8ay4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJCgUCRA9TVsSAnZWagAAMT4P/1JjK336a2Fdq2M/TD2F\n9dEF/WicwysJ2krmiQIonIsySH4D0AuB1OVNwJBgzLN68rCeh6LgJA6eShgc\n6ARmC1Y0I7xJimOBaEmQOj5r1uQknpd5mBxhdPB3x7zk1dogOg5PdGC2LWM1\nj3UuuY92KwGxs+OictPxuj7dJgLJOTG40obm9qVG/8jodO5uXtS+80U3f/ts\n5JBSltld3xKlw+VExfOEJavl8e7MwAh/Flyde7atEjbK4x51SaucnIugfOSM\nDSILTxuyj1s9TXfviwq4FhyLb6H2YCkpbFkOlw5rDNZSe3q6xdS3BqIwbrYT\n6x8U/7I+oUakUcOsOqRSmr9A0suatBnwwPFjdL6uefEBZ52tcIPsc0eh5jGJ\nndUngGVcmxlM1rYD0hgdgLQHrXaqN+iG5fTLscrjl918mzx70MnsydhsBPaY\nbSjj0XTKBJefxpL6YPL4TOZYsz+QM+6wDrqdW4C+xsCTDRETbfLp2P+OwVMi\nxNJPUjHUrCP9E9FeMAxSbbWURKa/ZfEEVDP0cI84pnQ9A0XlO9aCbLB9GRdQ\nY8aePBMx1aaQmFXK9IZCMVyOEnCZMEMLoxmyDHMay7wumMImMFunBPo3ZIpO\nYQjiCr7ogSWhLz3IJbP/cxixmrInfeXRPjMaIrsEW0CnbvA4J53qXo/XrC/a\nK0Km\r\n=yywl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.7": {"name": "postcss", "version": "8.2.7", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "48ed8d88b4de10afa0dfd1c3f840aa57b55c4d47", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.7.tgz", "fileCount": 50, "integrity": "sha512-DsVLH3xJzut+VT+rYr0mtvOtpTjSyqDwPf5EZWXcb0uAKfitGpTY9Ec+afi2+TgdN8rWS9Cs88UDYehKo/RvOw==", "signatures": [{"sig": "MEUCIQDXX9fLeKsgGA3odYQklU6/j3poERfh+pW+dxr1CWPMxgIgO+Y7pG8a8m1W3FIW9DbBip8wSRTGc3mghGzDZjf7Iz8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQBriCRA9TVsSAnZWagAA89YP+QFMS0N53O3pIKtZj1+i\n+ToDL37RSbI6BlIWhXFpxaRUYFA7Ho/gZKA+vdhyFjVSHZYCbPnTzFinAE+g\nTVe9vYsH/8yCK28hAPHVOvyyBlw6fVEKaMC7OJaHRtJZoSDVKlQ0w064Otvl\n1CWnUO2uOWYC8ZVcjGBYE5r3+jPP39/AMD+88VlzFGNFtdN7bIfi+sTTicKO\nCdaXw4aoh9agG8R3K2f7napauVWu6J61Z0UDsanwTJE2FfPZNGKsu4bgjBKf\nWRwLp5V47BO2LjJxel5uEAXAeOuVd1OYH+y+CKgR42FPlKrdtyeLrNjijSNR\nWMC4rxqyVs7MQ0HcK+5Z/XtRTy23EH8QbAhI8iNpWD/CEfuc4e040AR4KpfK\nkhgt7DQFz7XsHSzsSJVJQusuwUJrKVevyPRdqWSJJgBMpVT2xsfXL7VRux3e\nLNVLctw5BbIGmIfIVqGI1t+jHuuBQAV+I9njv259W/itkvZzjCoQzLmaBWaD\nu7vlOduFSxe0oiD9eMPqkzi2YX34OfO4yL0xP1a3r99ygTRc51Rm+EdjDhu/\n3vqKIdgEURkIz3gHmHyNqmu9OwAlIZgzA6hkGFUtTH3WqRwHWMrndaAopgvc\np0NoEwzRDiCwiWNaSpjPDxf/wQViWcaxq/P4TBV3U53mL8KyMceN+PvFRoib\nPcjz\r\n=Rq0q\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.8": {"name": "postcss", "version": "8.2.8", "dependencies": {"nanoid": "^3.1.20", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "0b90f9382efda424c4f0f69a2ead6f6830d08ece", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.8.tgz", "fileCount": 50, "integrity": "sha512-1F0Xb2T21xET7oQV9eKuctbM9S7BC0fetoHCc4H13z0PT6haiRLP4T0ZY4XWh7iLP0usgqykT6p9B2RtOf4FPw==", "signatures": [{"sig": "MEUCIQDC6PuTFoy2HvNtE1iKScdAe2EbR3dyk0P9eswZbf6/kAIgBB2c4BdaJWZC3mT/0QcEKDACNNXgd4i9l35dMwwHV2s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204424, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgR/EBCRA9TVsSAnZWagAAZu0QAIytAD5uPN6yAzMImYWr\nmFNLnKxc4U4477ouFDMOA6hhFMZ78m3sU+byrRC0k1mb7yuxR64sKRkTSxls\nkJRt9kvfwLky8DKy7NDk5nz+l5oDthpbJm2GrewazdJjXNw3RzKRmJXU941N\ncRGXndkui4V1PCKMP412fAJlAwRiBtcLK20/64zD5W1Wyt6yTpwjFi+TuqXP\ndaHoSSaQnsMjRQKTKo8OK2cSOven+NuEoSssy0OREfdSfEfagI6u7+8F7D6e\ntgfMf9Z5yafw0bjwjgSOtnB+v/f6+qMm0BatVemsUyxGbNcVWWe9642M1pXB\n7kvmKePKKSx+R2btRBMRTL6dT2ovFqpqOUs3Qe/5a+A1/jGTzO84Cp03RmIe\nYKRHdH7tiQodozCFwGn9V8Vdi3BVO/QTNqcHzZ2nZQw24Ykv4hKtaw+jfd89\nHUnDasCYhgBiihY3GHcGkwOEfNndfs+T53QOpNABFXsKDuaUBV0cS66px2ey\nwEpJB6ON1+Re5XDB7xQIO2JYDL3Xx9RWUrwaDOq1VIATyjLujR1PZk+Vzts+\n8Fc9PJE+SGsjhFfOXL5PQ+wkSWAb6M3OuFKIS7OtL3dYOfEIw9txzBTUhuYu\negDAihsfM2w94tiQ3NB8xgEz6VYcSaZDbaPYs8uWcNYqBSDP7fJmrnza1Op5\nsCme\r\n=/aeC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.9": {"name": "postcss", "version": "8.2.9", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "fd95ff37b5cee55c409b3fdd237296ab4096fba3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.9.tgz", "fileCount": 49, "integrity": "sha512-b+TmuIL4jGtCHtoLi+G/PisuIl9avxs8IZMSmlABRwNz5RLUUACrC+ws81dcomz1nRezm5YPdXiMEzBEKgYn+Q==", "signatures": [{"sig": "MEYCIQCM2igfv623ZjPjtkE/+iTc2yvkVUYSSPVsEafFnFiAWAIhAJOdXGX9CFdKeWZr9kjoRsUadPeTxkM486PuMDw25zDg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgY42CCRA9TVsSAnZWagAAdNwP+wS5u3uQ/YkG35N+KsXa\nLUScse0UQCNFWKRWMqt4nbOcbZ98w80GJhsPKxJeinNoTCbbQZFuHMCh1eoi\n2xLr6emGMIxbhnpuvFITUtfFN+cbatqA5S7BOc2B58QTMq1kwuemM2OtdNXQ\nnRA9u9Bo54R+Tku11RsgeeFA82Xzfi1ssFBBeMEgjGhnwZRWIrQakJn9whfS\nzX5+waSHHiFgWKDOWFWLjvyp5oYiGzb86R5wyrkvx2lyEIj1Zm8sltgtX73D\nXnaC2YG718rlITmwp+oW1EcJZOBhA08o4g3KvrkcX7tBbD6ufZqq9Ggr2Fdz\nvwdkBFi+PwoQujtv4B1rFtoO+TKUMhxqV/JHN+fmguV6v+9WJqxOuJgVjWuY\ngFGSwkojvRbK99LclEatRxMphjNMvJ1HQ4JTv+EsF1lRMYrCsq9BPWY1llmr\n8iqN32VHIXdvnQ07IebzbbgcQCjqK/voHSs1cPD6ICZBPGeaY0J0uUXXhrGY\ni7Ctnmfv31Y65Td4qhpIyfH9HdVL6BLPoJCWopXE6j0S4CSH4qky/uTXealn\nQJfyI3zK8rQdZM7LdMHdJAJzs9YoAK1o1BqGg7YRdFtClClDKz3ESUujXRyH\nH5yQVaqoe6Uzgvqm37dKhjMS+7rVDr14J9f0JI7m/W/Uxt2fh83JsRcfAtrv\n7Ncs\r\n=QzZE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.10": {"name": "postcss", "version": "8.2.10", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "ca7a042aa8aff494b334d0ff3e9e77079f6f702b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.10.tgz", "fileCount": 49, "integrity": "sha512-b/h7CPV7QEdrqIxtAf2j31U5ef05uBDuvoXv6L51Q4rcS1jdlXAVKJv+atCFdUXYl9dyTHGyoMzIepwowRJjFw==", "signatures": [{"sig": "MEUCIAEMtwAWZfT0yW0ztAQnsWGrY9RRScr/D4uebnw7grL0AiEA/NaYBslQYYJcWRqZTFtvff88cU5iVduKNxpcQV1UICg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcyXECRA9TVsSAnZWagAAZBMQAKEmR6FPfBZB3qxs5X+A\nUC0b9Ka2cY3mFluRMtEWbQiP+ZrTo0K/KQ61IkwgJ+HPIHSS1hLW2DvjPChI\n9DQmlFW3b/s+gvEUp3rRrEVGmwIxPyEHN+aMNLWc+El2oYeQQzVSbQ4Ib2vw\nysCvOTCKfQ0JnZE9vqIaey6KE5djl5j3p0mAstGrHldXhAAAQWf/f6f0NZNb\ncnTz3oKozIIqyNwdMx9G1Up5oprGflhRiZvR4kqiNRjxyXD8LJ0kvOs+wY8n\nup5K2ljPl9yMOmJravzFKb6XWVxtgilx1AtfNqm52m7PQLqklwmXufiA5Uqq\nAyXkVWUpZXyZj+C8L1c7d3RSKE4wkCfB1lTDmFiAvV11tC6y13FUgaDhlLlE\ncXGNCR/IoxqqpAn8ljnTyfhfYiBANMOcuyBAONyXTepJ1rD8f7K6sfyEeuWC\nmxNmr6qvQWq1kUPVUTMtC3fW9P9EGz7tAQyHYutuV9I7arTEEiBHrEaZKqjj\nc0VWUCwf+vPS0UTR3879G3t8y9xthjA/Jp3YdP4nbUT4V7F3pCGXFj/5zcop\nNn4lpmsSv3945PWn2RSnRDSWGjGFGnLmu5cvr5oGeRjPNhGDV2G3gyNh4dla\nynAgd6fzbcb+PD/hZPeew6XKHUdtSqfXWGdA0I48XT81S/JtsZ6RG0GTPKj5\nHPIs\r\n=Jtv7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.11": {"name": "postcss", "version": "8.2.11", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "22c80ececdb6f263d1144399afeb9ed8fcb52701", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.11.tgz", "fileCount": 49, "integrity": "sha512-8nXGYWAEzBGsGtPkwpbh576KY6Ya5aSi19VZcYiaomCBqkjvLG/bJx2UjULrH4dNKv540pq/qGvSFu0Yfuf6ZA==", "signatures": [{"sig": "MEQCIDRjNW/vMavt1j5P5wDkUgMgHh+miNDqBy5dHxwa7jE4AiA1w0TxdWpiiv0md/xDLTdOL8Y4nawkhIO+02VqYpgq9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggZcfCRA9TVsSAnZWagAAm7gP/j9PQxbG6KM2lLr2jhPq\nHarIBmuw1FivfpKoRsmxzC23cWv4h2L/8qctk7z2ELtCDLznEsmYUnzuyyfS\nmLyGhwI3WTSz3qJkfu2Tl7F5akwsiboze9NW0k6FmL8n5rGqaDm3qBLzpClG\nXUgMrX1MUtN+FgzK96su7QQg7lD8QSiS5RgDAXpRvJ/qlJeI2cqULU+j9NdR\n6NHaEmuCKzFFBQhsEyoJGl6u/KEkePECPl2+x69c9ZfPOqw/s6KCXuw849pe\nQQnAcKOc5Irbh6UV2e6weD8pgRlha2NogGpiWtoB4sBamLM5dia23asqfjQJ\nvcc5wdUACD+TFBtdqqnwnohPI+lquUgu+cPqEseB0OTMqbqODboN5kr8wBrr\n28QTV2MQTFi+9clY0ilJ08Nt/RCm7Cp+xrX4OZv9mJXCanLmRwK3DDqFJTJA\n5Y6OZTzXmevc/6446x+l3sbK4OrQ/0txucvTa7uSB77lr8/fv8vOm8/k4xSS\nsuD0R1pyZXbMHKwToN7Q9dP7OecvBpL0IKAnnKu6nz16XVkk0PaWAvSY7u0f\nauvPuZycZk0Oy/za27Q6aB4r+rc+V6t6qiwTaRvjk6a6PlfbteUrhcw/OEe8\nnAQK97gRMOK8poiSWzn5PY6T4MPsEyTn8S8NaSs1HI58CZ2hJjqhxpvrPz0M\nAwkh\r\n=Db/s\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.12": {"name": "postcss", "version": "8.2.12", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "81248a1a87e0f575cc594a99a08207fd1c4addc4", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.12.tgz", "fileCount": 49, "integrity": "sha512-BJnGT5+0q2tzvs6oQfnY2NpEJ7rIXNfBnZtQOKCIsweeWXBXeDd5k31UgTdS3d/c02ouspufn37mTaHWkJyzMQ==", "signatures": [{"sig": "MEUCIQCZ0CG9cQ0IJ92sSfePn2oMblMAbdUmpfj8KIsEDwHL1gIgOIIrA+gk0ruj7tbtPsBdbrDnlGFxfEwgwtrYVa1HXno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180919, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggZtZCRA9TVsSAnZWagAADO4QAJ9RyJAbkSGM2vqPxe0c\nMosxABDe6NN+pxX1x9xiqr+X8KqAm5nPrnRQoNdbxymp1yC9tBaEETmlYAqM\n+7nbttscoz5PzfVNeMev8O71IcgaLH9ZqsgtVeZfsZjQE8nhPYku1/dlIDrS\n6QYjdbP/54btPghANussvWOn/9tKwJyhcmoSobbQpOWyRBMZOHRW6R0oOyTW\n5vdCo2XqG9Wn3T8WyHjFqvno1NXqOcqf6M36HMHRm2kQttWcYD1InRgYnBCo\nf+Ina7KwPVewMskNX3ml/+8Ou0Bu+6mm5kOzwtTfBzWt6skOfh3LiNvydHJz\nKR+7PGBP9qdXJe8M5az9CRFyjItdFMNVyYmBp0hI/gU/CmFmyear3pwocqql\n3d6t7JY/02BlwPy2pFyzMDwpI70FpSrdPzNs0Ak8p5buFfqKNGI5Kixe7m+V\noHkD0CpCpE4NUZS7vRorQTq11yElUWlmoNIQ/2pdWepc03NGeOMBFyiWsABa\nfWWCvrtCOxPGyHdDwb/wIiOCl5ZnF1+ZpRFci+UWljJ15FrEkn1omJle0SCJ\n9lGe8Ai+pzLIwGlKRQGvdWTYkqMgwL75WWoPzNLr5tSYmWxfNu8ietYh1syQ\nycIB0RqKdFol36syz134VCg3D51vtfwC6ihp2WttthL3DBMOeHYUqtmL+aGz\neMIA\r\n=i8Ig\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.13": {"name": "postcss", "version": "8.2.13", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "dbe043e26e3c068e45113b1ed6375d2d37e2129f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.13.tgz", "fileCount": 49, "integrity": "sha512-FCE5xLH+hjbzRdpbRb1IMCvPv9yZx2QnDarBEYSN0N0HYk+TcXsEhwdFcFb+SRWOKzKGErhIEbBK2ogyLdTtfQ==", "signatures": [{"sig": "MEYCIQDvuZKtS55TIWKQk1SkGphcXKc+nMw4sh3CHhHiIugMgQIhAOTszSlBdoKdm6tKuZUM79wHjzLiXO1RXT+Wuk0WRYn0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghrFtCRA9TVsSAnZWagAA/n8P/1dFTVqBIYwNOHDDxooP\nphKO+5+mGp9bCfx9zE+WXq/kABSKbBuX+oe7M6dnyLs5gUD030v9/yHSS6QN\nrAyes1K3PGYDPGragjmYHB+asGRwQijgqM6vsXSHuvFahGBzu0sczVRovHM2\n8s8hrnnjDDwAcfpgG0ZXaIHUXgaKDYl1+zeC/u7sw2DNeVu53mJmhZdhepWu\n8PjHcWpyy0O/BBNNtlPlOVIFPxKaDMUMdnhdn3C/S1kOOErUDbi7LKWZAIqp\n7pisCTDym7WCsv9j4+G<PERSON><PERSON>hgAmomab5AIAMafIqyyP845ToKyjvfsMBnfA\nhkWpqur9a2JvvENi3zMb+1zE01ymWLtX69hk3pp8syBXBqEi2vMohF1/TAm3\nLK3pSiF24IZHQY0Qce5JrczTiqJsqRxuezFcedjDuYCa1Crm8E/7hRgepCMt\nSEhCZBN15RDoTnZcdEAMcJsQBkyIfZ0uCtIVXTxnOsRg9RMJ9krPmmOZvvYl\nRNLC2+sEp7Vei71zNDVOqy+LCW8E/CedyxqiexTkhxkrWVW5wPV8jiVbKrcH\nf3WXqDXzdk7CpmqkAkMFnhdSl6tb94dErCBrPkt25pw0KJC0V6roDOqci/pU\n6QiHTFxeeV20FEUqyFelC3CQ5uUu/CiznqOndGf0/QhkGx/pk2StHOINbGUe\nWLoO\r\n=3rrg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.14": {"name": "postcss", "version": "8.2.14", "dependencies": {"nanoid": "^3.1.22", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "dcf313eb8247b3ce8078d048c0e8262ca565ad2b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.14.tgz", "fileCount": 49, "integrity": "sha512-+jD0ZijcvyCqPQo/m/CW0UcARpdFylq04of+Q7RKX6f/Tu+dvpUI/9Sp81+i6/vJThnOBX09Quw0ZLOVwpzX3w==", "signatures": [{"sig": "MEUCIQDlvkBVmux0zcpZpy6/FurT6OepogK3WWwAC2ikpo6OngIgXzPAkCUYcKknjfENsJ7BdBKXp2PWFktfhpunxf3GQzc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkvWWCRA9TVsSAnZWagAARVQP/iOwUDMRwEsqULQhVqFS\nYST+liVpUu/pVqso4Q/Jhr3ooTPA2VnR2VJg+sj7Zxxclc+QVfBeDrdzB5JB\nRQEEkGK76e7AQerBXf5HpjNpMl7BWuKvCdpGUYImNd7LzyZ8Y4JfEeN+icNl\nfekv3eJh9j5xpQw/GX7IIThnWM9+IF+Q/2wepDRtG6fWqKJhEV3tB+5e7N/y\nXFGuJhBWbUbf77htCKfvp0js2theeFPni5Pp8oAsxkTWG8s68dC4qC91kWXT\nVli7Ye9EF5Xszxi4EgT4ytrljw8Pix04BxTzifVk98pPvqsf0X9GhuEIDOS/\nVdsX2ywUetIx1E8KSQtDnMXTK+3XkrKqqSwOel26T56EMQzN+iaJ3CuWhffk\n0R+i6GC6mah7nDSQVYc74S3DDXcQP7RVcA9512hLTplFFOSiqNTmmGpf78n4\nGaw7UZYFQgUCYqVTlkkheQWk2oPDKr1VPxIOlD8Py9d5HNbBIMtLOXGh+MEe\nRuhRM6+TgiEgicf1KaRnLDuDEIXz2BD6YrgZXUqOv0q2KUzDP0QA9dAfpOHB\nG3BY/gHCqA5Vyzh2k0nQcLxMiESevCg6NSTY9g/1LPVdNy34cOoWioIqfSXs\nolfGv42SIMIB8h7On2eJMqq6tX0fvVR6EsJARfmVB+YoibcJX2joagN+C+kk\n4cPX\r\n=vyXG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.2.15": {"name": "postcss", "version": "8.2.15", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "9e66ccf07292817d226fc315cbbf9bc148fbca65", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.2.15.tgz", "fileCount": 49, "integrity": "sha512-2zO3b26eJD/8rb106Qu2o7Qgg52ND5HPjcyQiK2B98O388h43A448LCslC0dI2P97wCAQRJsFvwTRcXxTKds+Q==", "signatures": [{"sig": "MEUCIB/Egfnmefz78bjTX/bxYmydgmqE/aQ67fJEuKxhEpyBAiEA8CRiofImY/AVvX1PIkkkXheVN3FjyoeIBj+881C+uu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmY/xCRA9TVsSAnZWagAArLsP/iMWGnPJ5p6TXDVin+yL\nzCpmxgmwNG36slfALrVfrBXjCmliC77hJH5k4EP9rnRMTi5klTbtjSiNYdJo\nsRn/tKqTTEVTgJc7sXETszoi1d77eU3eM9tS/3A1FfgBYAiW1ZmeJ6cDwKgy\nEKIYlrooV3tbGY2Ee8c049x3S9MECenCIGc35CfDAUvZmu5KAzgl8iJ71mW0\nmMo8FbHZJmyGws+ZfEsDaPJ5uPqch98I2f5eldRio+KZnZTVgMM2npvCaSzX\n7Z1KkemLTweWpCzTpi5GoMa1u6huzFRA3IShOBpnkHRBUDr96XjdsX0+Fclv\nw9Kvse14ZnJ/Z6GejyG6hvc8iPiS1tLCK24Np7jAX/JvF28YYEn/MH+pfLX4\nzkKlxHOrcFHoEyx3nL6S7leXd8GmB4xUW95biIzIL7fEFpBLKLJu1zveACKy\nTcPsGmj7K4jCw6ontSUaU6GAITsVGEZEzg51kJ/9SY14IU3qDDvwL+hkgJ9e\nDFo2BlXJHBK+Mjpf9afJUyyICf14JDuFvVEqSciCmxzOl2oJK0PsLqoty1Es\n1+o6uHAH1WcHqCw4O6FxJmtoIrN8ei11dSa5cCuhtJC6urqY1S3vYsRSUo36\n6AxN1MJohGC6OwRlK6BEc0z1yaxvvPZxuOKXABLFP5kZOtDldLO4MtKj78+7\n8hf/\r\n=GlYe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.0": {"name": "postcss", "version": "8.3.0", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "b1a713f6172ca427e3f05ef1303de8b65683325f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.0.tgz", "fileCount": 51, "integrity": "sha512-+ogXpdAjWGa+fdYY5BQ96V/6tAo+TdSSIMP5huJBIygdWwKtVoB5JWZ7yUd4xZ8r+8Kvvx4nyg/PQ071H4UtcQ==", "signatures": [{"sig": "MEUCIHANly+3B2x6wIF818uxC1xMYDp1g9wfI4qmCZtwzVSSAiEA9FG37v7hcy++bnoqIy+T2rjCMO3jE0kLmx8iutBSnrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpywFCRA9TVsSAnZWagAAfswP/2xIVjSz4Tt6zKOxNhlO\n7qcpRsYiVO3TNgDYF6MxM8K/ge/ci+4I+V+Lf62O3FFo72qTffD/TGsL45gC\n11tcYESAXxxrkrbPv8NRsVP8irrzI0skz2UWXBmF1PrtyLMFGWTQjCVNLip4\nD4LaoJasA3A7kDlAUS7eOv79LwIJkFSckjIiuGkIBjZ/1XOqAC8hXDo6tbca\n/ZEgGEudBYP5kifb20nlt07jtKi2gHH9LQEPzw/c3G5YLOtLAD3lsiO3sZmW\ndXCs9Vw0WSdpHePONLSrJFJlkeB566Ult8+J+XwP+LmGrvs18PRco1GzBIqL\nW6Xag52a8wSYDgErpfjovfzFryReW2iTdEftm++KVRv6VzTbLqHRAlwq5N7W\nmFgrWD1xU7gxIhBerxKp4rJQJuvJJx4C11aNs0hFKmu4MoIp070ljPpmd9qp\n51HKJLHSbspDcYIeVr7jxj1a+8uNxbdDh+aTDMw1QHFx+BqDi1LDKxQDYOmM\nsdgabXlxjclGTpWVUWKKZqgyhTjYiRzQ3sVD8q6j0EarlW7a7HbZVNR9XWXF\nde+sUpYDFwpqJik/n11qF27Q0oByW1TACxgGQyUFF8MlYQaLygs3gd6eRIcL\nu/LHtzxvbvHoUtYWACqBHjQH42Ssx7ZzesxjWXbwcDwXbocIQdLJt1+1ITcc\nsKYe\r\n=hJkQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.1": {"name": "postcss", "version": "8.3.1", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "71f380151c227f83b898294a46481f689f86b70a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.1.tgz", "fileCount": 51, "integrity": "sha512-9qH0MGjsSm+fjxOi3GnwViL1otfi7qkj+l/WX5gcRGmZNGsIcqc+A5fBkE6PUobEQK4APqYVaES+B3Uti98TCw==", "signatures": [{"sig": "MEUCIQCRr189hFMDfmhxzvIeDWkVV0rxBH3QS7xmsODq3gtN1wIgJ9dtv+F/6oGh6UNVCNbXryTWLpz90Y59Au8G17BYZn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwVEJCRA9TVsSAnZWagAAJDoP/10iHFBds9TpX+Ibb7i7\nGKeml3LyPGPbVRmWLQLSmdlsRmPreidNEvtLzGTtFt108FmJMEoCMNf0frG3\nw1Cnq7sNp0M9sldwab1teYiFWT7BwDVX2kZqX/Vz5j3tPHLT9vubPgKqJivv\n1larK13yOtdjGHavTZohbj0kQEnz0rOszXwkIKbbyoqQPWSaN0udMq8PeSnH\nHrYbycu0aRUqYTAziJulalir1Cmpyh2/YkFRJWB5B+8ShhFlpKEClLSx/Hcr\n1dS3Er9t3Dag7JODT1ViO5KEgMnzlSOokyv1Qq83qZt3+eJeWdXvKzitB28U\nBy37wsa2nSH3/desVopfLFQ59otrsj3SoORxDkqXR1N+RvtlMCj1QpBqql6O\n4e8bFpdOdA2JAiZ0CuHM1v8DmUrJeIAvV5WV3114CMsmxxurfax1R4wD1Vly\nSfiv0XW8V8VslNPMo1I+IGDqBnNFgH1uvPdDr4aJvHzOMl43oGFgrPasDuM6\n3pysT2WPY5iuf+pvpEkyT7iIyZnQNnNbR44ylRHBEUWVlzOsVGGB4zcsxYuz\nGjG1vR1TPD2+wXTsfWTOQIs88uBBm9TDZtFshm0lf7OtQkXszD7+tWtswGW6\nKtTw4u4NqL3o6gGkLI4bXoymGPb4me+chAmRnEwBGTD8qRI/skUc/yTpbOYE\nhsAa\r\n=dEjF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "7.0.36": {"name": "postcss", "version": "7.0.36", "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "dist": {"shasum": "056f8cffa939662a8f5905950c07d5285644dfcb", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.36.tgz", "fileCount": 35, "integrity": "sha512-BebJSIUMwJHRH0HAQoxN4u1CN86glsrwsW0q7T+/m44eXOUAxSNdHRkNZPYz5vVUbg17hFgOQDE7fZk7li3pZw==", "signatures": [{"sig": "MEUCIHfloQHF6ouAeM++9UG/HElv3n3UvKDXCM/GSMZaHGRtAiEAqLvYHDXS199V1FxFhG46Q4IdTmOOGvfdgmi9jA4gAuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgws5mCRA9TVsSAnZWagAARAEP/2tn4CVE21e3qpxaVThR\nzbHuv5A0dU+KoJkE1o4p7S949Vk78K19W9MR4IC05qmvSqUYZo7Yr/uEQSaF\nln6Hq8LTnBY6I/11GD5X0+E6qmexfPAmhchnNaRdDb92uPzduKvAuWIN7Qr6\ngj9DGt9cYwbHoRHRzgkew2N9CNDv1LmqiEtXTu9TdEbvCIGyiC0CE6UgVJD1\nm3vMKhk1niCMC2+ign1mcaJVcBwA8SpKbRT2ruToDN+9NF/ewS6pS00c3L52\nl1pADN0KhSMtDGef+1bc36uvQtKT0ySrq05pRHg+5r9JxLN9efBM7z+cOouT\nPgpZoqWBGk1q6cPXQg6WKdZUuIuH6aJm+t1SrwLNf1K0lYeeTAcI2RQbXuzC\nL7Z1s8cfHL2dp5BGIZzyIRLch7qmEH4+xb9xTOsASYAghT5H+WnCONaNDlTN\n4qDr+V5d7U2owfjslx1gpht64iH/Mj/Czq1lVxtYcA+YTYVxABWgWEvVvSGQ\nMRKrI9Ws9Nsj/Eqa+drQvWPLTjeY5wxMBL3/ZRsl3imGhttck06SVqLD9wVQ\noROrfBrvI/gl3VGG0/3PpqywkVnIT4FYkzfqp6MqViiXtUWLB+7XG7Ar4NP6\nl4p44TG9NzVGUMAFNvK8+rkhVjN+MxOPoxhwuS+3W/ZXS12NwMfqFBsIDFjN\n58qp\r\n=eAEz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.2": {"name": "postcss", "version": "8.3.2", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "ed3ec489f5428af5740cd6effcc216b4d455ee64", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.2.tgz", "fileCount": 51, "integrity": "sha512-y1FK/AWdZlBF5lusS5j5l4/vF67+vQZt1SXPVJ32y1kRGDQyrs1zk32hG1cInRTu14P0V+orPz+ifwW/7rR4bg==", "signatures": [{"sig": "MEQCIEcVFxXqd9z1ZY9ChjA0expCXBgLcxiiGeLeE4hfqJU1AiBsqNXpY8jo68mTSUI8rsJJxLDNOdGhhMl5dH6NaDEqdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwtEKCRA9TVsSAnZWagAA3E4P/iFFmtvy8OKxX1pDB3hA\nowOzNirwO1R6T6I8bYFyLeMVb5XvVQBHmSxlS9njbET9OpdMqSoyuc443BbM\nLyD3r7IiMpRgRzsFzaHIQMwhsjSs2u8mwMdKuVysFbcymnrcXrI1LyDMXfii\nW2YKFfJa42Ywqy7DJlNHrR+b9a/zX1uy/tsORcT0zF5laX3Lm38m0Z3RfxLG\nx65XgPk+9gRwvipW881noGXKp1CmfHV82Nd4n3akMKiB2PxD7+fgzHmbvFi8\nqQgmjEJUyhd+o8tyvIB2dcFgYW13MXKvAyM0WRN1SFIpROggjJaDzWJoSRbi\nmElRVEIIsW/zOcqRqiclCBmmODZjxBU3NOJrB9jae0BXSKVXO/QqDQnxTrYx\nF7e4nCGOVmBxz5mknE45GomKd+Qvy8aVhtKupCRHZh0avj+MTxw8krSfSkr1\nBWK5Hs/DE7pch1CWpEttt4pGsAurv8MTiW1GfoVsEfvopEkS3jddQ+edZbkr\nILbRxMTsgHAxKZC5bk/QFSHgGJ6unbWrr/UNsu7N9frRrJXTHEdcInyrWghI\nzawuXlbrFle/oqgrqGHPbAjytE/t5F47HttV1cwWbaRfdeL3uTrBKva8vMJV\nU6x3RCYB4wOb2QyHgYYXQtUEuqHOHrWUtfqwUIOkInzEOQku2doxk7dAoQ3s\nnWQF\r\n=wdn3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.3": {"name": "postcss", "version": "8.3.3", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "ef412a7a67e85c5b2c9f0ab3c4d9e8a3814d55cc", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.3.tgz", "fileCount": 51, "integrity": "sha512-gnXd9C4bGKevvlNFd80I8WfxHX+g6MR+W2h19PlDNHUuT9248rHTvCIDeZI3Hvs5mB3gzXiNDwVK3S153WJbZA==", "signatures": [{"sig": "MEYCIQCAuRcyyxaooRTqNYpHnW2gIpGx9YMJ3ys2RYaleiJiOwIhAMUgJwODJM+/osAe35chGqdTe4rm9ncnUvDUVGCPApdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgxupzCRA9TVsSAnZWagAAp9QP+wYeCCOAz1JCalp3qmMQ\nll9JPvKdK7FQUtFVAqL7bRtBmgvuEFrgGGpuriLOTtZnibU02WJBxs6mMYwx\nMX2zCPHhCftl0ezeRc6RAGpl4QrC5sAdJFbTw6RrnGr9VMi+kj0YScFKtnAF\nY5sPM2HcimiQMVTo+wo/nDV6ZZahz5/ItAZsNafWiG/TQFiVyl8vMkGYWVVh\nOU7fL4dK2lGyKLpqXbIJgvCdxFEGiINFM51QcwGRUARUt39LNfJuEba0jAMt\nsE9qiZVY2qWa87QkytNeO/dLInCvdJPUvHH6wmVG3KV48XqwMHxI+jm//EEt\nBCentJnVgSHYaGz7zt9d9CKgjW10BWYtYbOdZscPo2P/aLp5kGj7v84Najvy\np1cchnK0T1Vbg9h3Sy3Z2yr0HHqrfc+oj7GzSEYJ03291E3okFfmofgEAF3O\nCuCI17rvuU3ZujJEpc+ZIOnhu2onH7UJeEnbHmweqqimInoTKHdpPFpbEeQd\nnubFwX69g2Jd6a2WIFH9f8ngbXL/tThRPI3FdMt0lHw7CP9AXRQedkPzJjK6\nM8hfy0UYPfdX/kCRogh1JCNlZtphGn6r+SGWsCyUpGon1jHzP0AJW0u0982R\nhaJkdFi7i7BAjrBh++4/thX5JoQQjrgiXk/51FtwzSc7FuI+h7yoOhW9SYio\nPxwj\r\n=5s8E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.4": {"name": "postcss", "version": "8.3.4", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "41ece1c43f2f7c74dc7d90144047ce052757b822", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.4.tgz", "fileCount": 51, "integrity": "sha512-/tZY0PXExXXnNhKv3TOvZAOUYRyuqcCbBm2c17YMDK0PlVII3K7/LKdt3ScHL+hhouddjUWi+1sKDf9xXW+8YA==", "signatures": [{"sig": "MEUCIQCBK/9V9faRz75YOuUFNlg0BnHwLtSJph0ZwwwttjW7ywIgG2XPDDre+7m9DJ5HaNJxKwl2C6YWht2t1f6mS+3n5W4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgx1lWCRA9TVsSAnZWagAALccP/0Ww5v6MzAUQdbEnmW3Q\nF2svDCvo06m4xOiIhJ4rZH4nDqOkSBzJ5DadVa/SgkZzifHFPUUGl43S6MFS\n6Z8AvIg+vvgZj9EH6FuTMpgW0c8yyTEFdefuGpcPpqDfh2ckjaD6vRU/WChx\nMHnRHgOP8edfhVew6FAOM4+oJ/gl2F5fs8qAgGU9FdItt6IYov5/JnFkixwJ\nEreq/LfkNVCjhBs+A1QmfFBPxbYFuHFlzrtG12Ge43guBcUi/jYaTCUuNe5c\ntU8vkSDdzyVG4IlSh5ZMEfcqGI3AErr3TXZD2tjDkqPdhAyhpybsjJ7kNcHa\n/bCDDnQIskxZA1Enp25YIYamDehrGUN+dY1/5euup5gBxGQ5nn5/Cdz+0vRk\nE33c4YBB8jtaikbTxE5f5j4h1p79zwdKs9gwYZrhC8ftqFk6ulabkCnRy2gT\no9zkEZg00M1G8daWXaZQ/w+8vEpI7zMj0ihSCzunY68D0zo4ZyKYi/yaRWGa\ntqhmRtc9LmiZcKvsHMoVpqjSxu9rtOwBpAoiRnSJjtARGU/yrDhBZuOlFzs7\n5muaHOOcLkuPzQHsxWFa4UlrdmQOPggQPmK60loSu6gvOvlwBZXyHNj6ihaj\nJFOwze5Ar9dqLkhvpyrJcHM7CSzCKfR80ft7rNg05pvpeaIjZ66WuGZu8PUH\nmHB5\r\n=IHn4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.5": {"name": "postcss", "version": "8.3.5", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "982216b113412bc20a86289e91eb994952a5b709", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.5.tgz", "fileCount": 51, "integrity": "sha512-NxTuJocUhYGsMiMFHDUkmjSKT3EdH4/WbGF6GCi1NDGk+vbcUTun4fpbOqaPtD8IIsztA2ilZm2DhYCuyN58gA==", "signatures": [{"sig": "MEQCIAnwGOwiLGBQMP2ZdfCB2EDConPpcgN1aCRzp9XIEx+dAiBaOSKTR5vIO/gx3YKYM/rkobv67cHCUK4myDaXpIvr3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgysCmCRA9TVsSAnZWagAAsOoP/1MyBpFBOr+5E7MKLx6r\n96yDwZe4TNeJsRl5ojO12dxgMWm6nkDXghvSU89VTILUWEOWM/SPseYz0zEr\nnYkn87gDTHzXjcFmddhx8XpJ6Bmli2Sx4ugNUmotOBkkKPb9LK+PeymrqIfE\nx5NixN6CZL+Sx0I68Q9iTctWziUjgjJ+C5AJGTatxhm3eCBv4aJy51/aavEQ\n3j1tC/P37Z6I1Hjnpj0yvQUIytVnm2+mTVH+Lcldr/6lEUvycbTpr7spVReb\nt6r5tW4vupMR32iEVz4YW1ZGY1vYZhaBzhHHpGP6rGNy5EoL1QoEzS53obeC\nsivNViYmUuQ9M5pyEUSdDIR3vb4qChZEmXUVxZgRSGjN9Jb13SsBaSFQlXGA\nswL6DzCORaR1+BetroUbQ7d3dd6NcBipRmpD32KYT313c8ccvQxAD3hbx+OH\nLWbLHrd9h5QitVWFFH8oYXNY953TfipUbzaIDWLFWEbcw4lavLiCJa3Riub+\n5rAQxh6d1DVzkwUrgy5tzhNRMOICP4hvw674sYpO9g6q0AuLf7eCLey4td1s\nE5cfYlc7N3CsH8Qs6aPt9pnkiN9lieQ7+peQpAXAxTek6vkSs233y9PhnhR8\n1Xe7kVWTClSF+9B4tVNbah49dGMkzA/p7FdUeIOAZIJiR41IiLyxB859gECB\nnWdF\r\n=kxpd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.6": {"name": "postcss", "version": "8.3.6", "dependencies": {"nanoid": "^3.1.23", "colorette": "^1.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "2730dd76a97969f37f53b9a6096197be311cc4ea", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.6.tgz", "fileCount": 51, "integrity": "sha512-wG1cc/JhRgdqB6WHEuyLTedf3KIRuD0hG6ldkFEZNCjRxiC+3i6kkWUUbiJQayP28iwG35cEmAbe98585BYV0A==", "signatures": [{"sig": "MEUCIQCKUi1iPujY77uoxhRv9NbA5hgcIchpd/yvH+6HxA5cVgIgB7Tou/Yo7Ng2Y50TtwI2Quq9dqWKmTrm/UAIA+MYuik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+EMPCRA9TVsSAnZWagAAkZQP/3bzxOzBDKSicZ4jIQiv\nlDFYDPuYZUGAMsIWy6fphnQ1ckLWXE7M2Tu1MT4oxU5gdPhBlHOUOm4qve6T\nx016iOz/Hc5tDb1wRwd1e4C3eA9MvK7Jr8QNi0+CgDOM9HQU9AdqZY/XwjAz\n1cCcjNOu5TYtP/iy8may4I+5XzLj/BFRRYtFSIoun8d1KVQFX55YqHsMRTBR\nuDb4t9Ik1Az7Vwm3xlYVXxLxRimmtMykStgLhyZ6Gq3MAfvvMM+ppKNc/cqK\nzD9LExPBuYdlBmQoytj2JieV5sjAJX5nPuBcTraeEKlSsaIVqL27R1rvoQQg\nUKIKY0Z0Q9S7Veyv9inseEhCirHAVpn0bJH4cEhNrYbsf6jko1F9IPQGJF8g\n/XE5wpRdME8j/9bW6jz/QcrM7L08EdH8Fmldr131PX6m92jDB8xYxN1rtn5G\n8FEx5rqKdgKNmb2zx6k0n8GLQzulKs5Og8xXZ+OWsP3gWsv3582CzcHcbQ7t\nJF6l+lHrLPP087hMVQXMgN3LHkk3JxKZ7WhiCq+1U4vkcndUdrLJDiV25OzP\nqr365DmhZObbwVmlDvhlgdzUv6NX6Xa9mpq7xkV0LxclbKHU+ihxdZd4V+Ne\npuGKY7BJfyUeTnu7gpsUH+BtKKzfefZID3BEurXI0UBrRXSz9DLoq18D6sm5\n6C6B\r\n=7+HL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.7": {"name": "postcss", "version": "8.3.7", "dependencies": {"nanoid": "^3.1.25", "nanocolors": "^0.1.5", "source-map-js": "^0.6.2"}, "dist": {"shasum": "ec88563588c8da8e58e7226f7633b51ae221eeda", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.7.tgz", "fileCount": 51, "integrity": "sha512-9SaY7nnyQ63/WittqZYAvkkYPyKxchMKH71UDzeTmWuLSvxTRpeEeABZAzlCi55cuGcoFyoV/amX2BdsafQidQ==", "signatures": [{"sig": "MEYCIQCcfCldZp+O+ucsgAnp0dHvrJG8Ns04Q92B4tHt15F6PQIhAIjRrWqjikGnn4ZEAtuesYHGRIpv8epHXa4LQa3obhlE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189181}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "7.0.37": {"name": "postcss", "version": "7.0.37", "dependencies": {"nanocolors": "^0.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "e728e007e2ba87d87b759ca85f1be957a9dda3b0", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.37.tgz", "fileCount": 35, "integrity": "sha512-mPz1Qn6ngZSUzBqHht//GoGbZG2CRihrsYi4gGv75UBBisqsxyDAHsUqgsjAED+TsXj34B/97GzFkNVJoFnaoA==", "signatures": [{"sig": "MEUCIQDgH+7zO5c70eS8JafPeZgD/tkmXZWs+9Gs/whzseyUDAIgaG5NAqInhvYAjZy9svxwe9BoqQj4sFJo7TcsRYJSJxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608424}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "7.0.38": {"name": "postcss", "version": "7.0.38", "dependencies": {"nanocolors": "^0.2.2", "source-map": "^0.6.1"}, "dist": {"shasum": "5365a9c5126643d977046ad239f60eadda2491d6", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.38.tgz", "fileCount": 35, "integrity": "sha512-wNrSHWjHDQJR/IZL5IKGxRtFgrYNaAA/UrkW2WqbtZO6uxSLMxMN+s2iqUMwnAWm3fMROlDYZB41dr0Mt7vBwQ==", "signatures": [{"sig": "MEYCIQCgf3Ty328NnBMZPZZ9ycArTQt9S5BBoy5S0y/99az8dwIhAIi1hMAouuB1oYm9Juz2JxdsTMdexmBGRSwaerV15Yzz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608465}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.8": {"name": "postcss", "version": "8.3.8", "dependencies": {"nanoid": "^3.1.25", "nanocolors": "^0.2.2", "source-map-js": "^0.6.2"}, "dist": {"shasum": "9ebe2a127396b4b4570ae9f7770e7fb83db2bac1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.8.tgz", "fileCount": 51, "integrity": "sha512-GT5bTjjZnwDifajzczOC+r3FI3Cu+PgPvrsjhQdRqa2kTJ4968/X9CUce9xttIB0xOs5c6xf0TCWZo/y9lF6bA==", "signatures": [{"sig": "MEQCIDL2E8zDOGiKciVdqHfsgtRUXV5iYaAmFiMdFKUO/xhIAiBtuvazAoYQPI7+ygwtRZfWxpT7+bjRBXcZk3VArFdjlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189181}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "7.0.39": {"name": "postcss", "version": "7.0.39", "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "dist": {"shasum": "9624375d965630e2e1f2c02a935c82a59cb48309", "tarball": "https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz", "fileCount": 29, "integrity": "sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==", "signatures": [{"sig": "MEUCIQCvmb/C8drezROnj7HS5hqy8TjNiOlSKquaCkqahLsCdAIgNVSbMDoxZVLq6m4W4nmhNc8SeJQ+aWjpK/xBg9lBbdg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 541344}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.9": {"name": "postcss", "version": "8.3.9", "dependencies": {"nanoid": "^3.1.28", "picocolors": "^0.2.1", "source-map-js": "^0.6.2"}, "dist": {"shasum": "98754caa06c4ee9eb59cc48bd073bb6bd3437c31", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.9.tgz", "fileCount": 51, "integrity": "sha512-f/ZFyAKh9Dnqytx5X62jgjhhzttjZS7hMsohcI7HEI5tjELX/HxCy3EFhsRxyzGvrzFF+82XPvCS8T9TFleVJw==", "signatures": [{"sig": "MEUCIQCJa1xV5HvX/4lMi8C/2TUMsuP5myBzOVlzUqUleaHC+wIgYsqyDZJEPtc8yEmWRxnG62HyT6eL1UTqxnrE9kOzcRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172714}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.10": {"name": "postcss", "version": "8.3.10", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^0.6.2"}, "dist": {"shasum": "4d614e108ccc69c65c2f6dc6cec23dd5c85b73af", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.10.tgz", "fileCount": 51, "integrity": "sha512-YYfvfUdWx+ECpr5Hgc6XRfsaux8LksL5ey8qTtWiuRXOpOF1YYMwAySdh0nSmwhZAFvvJ6rgiIkKVShu4x2T1Q==", "signatures": [{"sig": "MEUCIB00L3vUj4KUKhPuGmBWn7ELiWGe1TTcRBDXHP9EbqrbAiEA5N/3ja7c2lUy2Tgcv/WynSFuo9Bk7sheyyNqF3UODYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172663}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.3.11": {"name": "postcss", "version": "8.3.11", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^0.6.2"}, "dist": {"shasum": "c3beca7ea811cd5e1c4a3ec6d2e7599ef1f8f858", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.3.11.tgz", "fileCount": 51, "integrity": "sha512-hCmlUAIlUiav8Xdqw3Io4LcpA1DOt7h3LSTAC4G6JGHFFaWzI6qvFt9oilvl8BmkbBRX1IhM90ZAmpk68zccQA==", "signatures": [{"sig": "MEQCIGzCsOc+rltff60ntGWQ6lBTg9xfUmF9T0lFbEFbcIqDAiBuNe/Zj9kuO7Vfhu/FNYCzOZ4pI3bSO/+64D17MOEUVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 172630}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.0": {"name": "postcss", "version": "8.4.0", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "cd4c33af00a00ba93ccf6ae1219f57c5e5ab5234", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.0.tgz", "fileCount": 54, "integrity": "sha512-BRMNx3Wy7UI89jN8H4ZVS5lQMPM2OSMkOkvDCSjwXa7PWTs24k7Lm55NXLbMbs070LvraXaxN5l1npSOS6wMVw==", "signatures": [{"sig": "MEYCIQCsq+3V7MDgdHQ5NpfAKMsstMt71tHYPuZ9IecOU1X1PAIhALxeQlSELgBUh9hMdDT4m1N2kF5URKFDatCWnwuyrvXy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnji6CRA9TVsSAnZWagAAY+wP/08IMJ/eLBlQItYpGfsY\nTus+tsUrdbo8OTAQkLUeqPYNBabPgiFcKZ4dLAf16Ya1GWrYOl28AVwDOA+s\nhnNGSJ+Po4wU7ELv+UBajmZTC+Z2EZU3ytKEmWZdKeN4o6ZxFROVU+afccVf\n3oNWVT6mX8TTxyoFRJYXKg+jVQKP9rpK3PVqTzQuTwE2ELESfaX8g4Gve5G/\np32fBKTaCLwCthVBIR8SkYnWNYjR7TS0rsiiA+GE9ZDHvbsRrSHx2I/alPcs\nf3VzQWQkRhsBeLLTSLxy5NSj+kEYPnDGXadsmDf6DV5w/WLTtzIOOUQvtjJ3\nyqVOneRjHiem/VKCmDh0rlmZl7r1A1fHj4j5FQCl0MHwv930yko9h/PDEnTK\nl6PXv2KZO9uH+Jc3SY+wnndzWG0TSAHmtJayyehaCipZBtHyn5K/SOTKHNAF\niJwbX24TjhAgSTmeHltCLo8E4xAhzIZMaLNsxoSu1cinByo0xF5rNw/iC+gR\n4hnuprCiA4HKowEe8INK3qp2K1gFOVx4Wwb28m/NuQUNlfwmAjQ98zAYiZgL\nRPsRnF16k12PwH1OSj8AHj0BKyRQzixCg4ADJzYahvwAGV47aIM8KCUzXoFQ\n/YavtsKK4RacpU5/6H7VxF4fsVxjTCoQuU2ZcIP4XoSTiQPJOcEHZhrOS5YJ\n716J\r\n=tWA3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.1": {"name": "postcss", "version": "8.4.1", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "73051f825509ad1a716ef500108001bf3d1fa8f7", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.1.tgz", "fileCount": 54, "integrity": "sha512-WqLs/TTzXdG+/A4ZOOK9WDZiikrRaiA+eoEb/jz2DT9KUhMNHgP7yKPO8vwi62ZCsb703Gwb7BMZwDzI54Y2Ag==", "signatures": [{"sig": "MEUCIFmYyY7Ac0RF8OUfLe2X0U6ThjEKm2jRyOMsKCzA/NJUAiEAkQTjNOCzY2MpYUuo+8rkSyqYVkEKfQu3Csn7rNa0tGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhnrdfCRA9TVsSAnZWagAAJYoQAKUpOraPEA23vqM+cg5m\niqvr2M/4mM9h9KYycegtItNAJqihq9eh1WKkaQQhhsUCtlSPNEu6W1SCrwVG\nvyvKZWIf4Vz3g7ujL9cHd8chI4tVWQTRiwic3nJfqj7cJey4dafzQX0ZUwqV\nPRwdYkW2sViGq5aytAQDGWIqcwT+jxXUS0MIY8NTloX05nV8KQS9XM/LCdFz\nQDJ0oHTB7oGMjEVMvKZlQRVX01VN01E1zErMaMfIzj4YsytqFNpAYszHos3B\njM0nn7TcApDSINVVvXaCLgIW/b6DXQelNt5iXHc10y+pFwlYh3wdblc+D2ru\nm6jUvF0pZMpS+PiLfOEnhEm1HCZ5RPg91vXBW6beIlxmOkaZkNmhXFqcbB6L\naU1GpaubZlln0PzeyiK6J40BwEzcKcbIO59ql22fokLfZ1uGVhwjjZRhA1My\nWCWRghB9M0VGykPY+LK14CujOhL4BMR/GS80GL4nkPzI3+iqQhy1b3J6x54O\nG6iN6o4NkA4Ary9kFZr9FHywZvZmzxaV6tqCOSmBM6AktaDiybDCJB4YkS6g\nT6xg5iJdiuZ+9JaX1yA3GgeqIdTcby8n653BbG6X67fGeKK7xzc9xEQw8zsj\nJz20JGruvru/D3TdSlGf39oXx88mGQkNGYeq+DDAKmNOJA746OQ00Zpi5+vr\nm9sy\r\n=hRJS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.2": {"name": "postcss", "version": "8.4.2", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "39920b20748b323bf6f331d1128256392489aa3e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.2.tgz", "fileCount": 54, "integrity": "sha512-zGwUzDSwfRLJABcYXjROVVnjPn8mdxl88kR8SAA7LOkSY/Jnm8GzV21tm6wjQxsQISnwVIggwbxDZI3cIq8mJg==", "signatures": [{"sig": "MEYCIQC8VEgDCGVbk7RpcjnZC/PCr04JnmTOiIiDtH10W7ucSwIhAMts7urDx+2+zG60ToJiECpcwXyNnk06YwbhR2kEhIDO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoSs2CRA9TVsSAnZWagAA3JUQAKAzJAeCwKpmyBSmMjmL\nTt7+OpEJN+xvg16HYi7l7/VEjTB7LhHdd3TZ6j8fC2zsHjfbLjrC+r81obhe\nF2BNCf1zDhVoHts+MVT1TDq5xqn8WcsllefYoTOw80Zgr3aJKt+S43idCLid\nu7PDt39/yW7nAdbP+8jakGsIC9ptQoBjl6+Vei5QUMOgsKBHjSZ71ArUf2cA\n5GFO+2ZlhpLLQ2WUJgKtrwhGcSzkFgFBXHrs2qewwVLE7R6Xw3WcWkZRA6ju\n3ExpsjfE22aZdVfWQ8b7mO62nis8ic+DxoUw4rBZUXzFnjBNdAZGERdK+jxm\nbnLBOQDV9y7+eUbKw3/n+Y67p5GKzmT+igu9mdM2+JIBopAYQ6rPFUaOajof\n67Ml4jRHe0vF1T9cxFizvZrRm8EZq7jc0bTTm2xujVpXGqEPb7sG4AIvc8ol\n3bCpsdu7qRWdzf2LRoZtlcb7MGOq6FhgnFR1fS5sMNZ05Jf7m5aLMu4fHMJd\n+V9Pkp8FOGtkEmu2pFvUiH13uIb65Mvyc5JnazAT9Ot4Nnwx6WXpipYKrNpe\narXKD2pJ+PFZdGGtp5ngqov/JAdj0SZocYl5wUtw4IVDiKUFs7x1OzAxoT+S\nMXq31xpNHHeZsUOe8JeYaxXY4/o7QnlBPS8r0VhKTMstNWioyFpOdgaszO3A\nUxg4\r\n=XeVO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.3": {"name": "postcss", "version": "8.4.3", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "adc55db82e868dea54e9e0e7ecdb0368b5adf65a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.3.tgz", "fileCount": 54, "integrity": "sha512-d5g<PERSON>Ko8ekrircqHOQNvSQk8dhGOuAfu1iP6dzfAlnAmcu7EBJ9cFuZG8B1KZK362aaEO3L1H+WM3ny9xQ29tFw==", "signatures": [{"sig": "MEYCIQDNHYG3oiw0bX5ObO4kypL74kKRjzlm6G+GK98rfV6w1gIhAL1iO+eDUs/6Eb5TrrRF+JsLT/Cej9nQZNnRAiMvW0wD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoTOLCRA9TVsSAnZWagAA0usQAInNKXA2umZCbeQ7YbGk\n55ZPRb39Cdzt9hDmS4HWwIe2R6pkZ9g/L+UwuQmKLNtO/HZPr1Rf5wC0NoVV\nJxFE5yNMMzNlfv4FXyeKDmn4KNVjRG3lpFhEKdkQN49jkTy6EIzn9DJTFtqM\nBlrHqf6mG8p0GMltLi0rX8T1+dBQ+K3NNIArLmkwQHET34+ltq/y78YULP26\nr38+1E7Xd4komtCCxLqgPg/Eg+xtuRPADaRRbTuO55X6sskArhYgf0wXJgby\n1gt+Boj6FxjCPuIwk8fwh1okg9/M8NYhY8oGtbz1+yPnz7hyxwysM0vN1CoM\n6tNr+MatTU/DrksLziHG+ud1rCPCdpPEEPLwfnG8AbdEVZ61F9n3Rkk9Rx70\nWIuAuQs5fLLlPKLUfVyeZ3+SYohCC6Bnwy6MRj/g8IE5vPY9994KfxVt0uYm\nNWOq/HEsWqf3aJq5Iq5wzclmxsVcUBf7bglzpIL1jqhcYmVJM7rPSGMA4eFx\nGq4SzYNIruBw7GyeVmeozp6VG9wYazO8cge2SIXaSoIwuGXVwDNY/iEMNz8R\nQu4gdZq1MCNOJvgv8ktdT7ytbyfLKd2nI8ZMt9XkRlGoisG5Uronez1KhuAL\ns5VJkHvzTyXeCTsk+xe0uIKHCW+//ELdB5l1muXPvcZEpLl0GlA8dG8VSEf4\naAjv\r\n=Zks1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.4": {"name": "postcss", "version": "8.4.4", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "d53d4ec6a75fd62557a66bb41978bf47ff0c2869", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.4.tgz", "fileCount": 54, "integrity": "sha512-joU6fBsN6EIer28Lj6GDFoC/5yOZzLCfn0zHAn/MYXI7aPt4m4hK5KC5ovEZXy+lnCjmYIbQWngvju2ddyEr8Q==", "signatures": [{"sig": "MEQCIGG0cYQ3Z5coCQ8n10SGeQR/JwsG+4OXbRHgPCncBHNsAiBtt8FsrcYdVL7Ih8NmYcbiZlhpV6ML/C/1uJ8ApkM5Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhokegCRA9TVsSAnZWagAAiQgP/1xkJHMgrC9ao5PO4vq1\nTHjs+XC/Wu9ktiVMuS7duPb4So1L0jL6H23klI0k9bSELix0XPXHs0a1m6I8\nXm5TfqO3LRVzSZKoW/OqYNCymjRlF/m69/hQf34IRSb9jClCO11gx6GXBkW9\nfu9SxmsLDUpgaPQepHoh5RV9/K7vEdgPfNWmYJN4L8Gz4IMP8kNOfDn6bh+P\na6cSQydXLpxDb0SWg1NbvImldaTD6WuZonV4TsUOr/7MGtrxQzg4bCe0PsRz\n2d0/gzaomxDDHL6BWKM4QOx6WRnGRBbVV13krmraIKVC6yK4E8ZEG9sNAAk3\nW8ELenBZVRu0cnhaPpRdm2kBTvmgJIuuPRV5BwqL5c3+e1vnL1Wjv1EKcHtz\n4ccUixhKfWyQ4dLfiOeJSKnZmEqEJ7N8rUCMHKiwGfpxuzifxoIKMhD/BuhQ\n0FoTcvXSCarpJLn/PF6DKc3qcWfhiV0QffC7e+k2pVCsXBKkxS1Zbq7R/V3R\nnQUtFmLXsxXLz8t8/cv0b+QgtrsMfXRVdTuHrk8z3dcGRdECwiZxqcI5dlVr\niE00GcfikhDf7dfF2r/PoZZzG8TavucFmeBUX/Z7dAU1/CpO8UuSzE8WrAzp\nUtXPHCf6o8EtP7HSZfNjbtjsPwWEUOWl8zo9SM1NZwYxL/PU9ILJOK3MXWlU\ntNgL\r\n=L7CM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.5": {"name": "postcss", "version": "8.4.5", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "dist": {"shasum": "bae665764dfd4c6fcc24dc0fdf7e7aa00cc77f95", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.5.tgz", "fileCount": 54, "integrity": "sha512-jBDboWM8qpaqwkMwItqTQTiFikhs/67OYVvblFFTM7MrZjt6yMKd6r2kgXizEbTTljacm4NldIlZnhbjr84QYg==", "signatures": [{"sig": "MEUCIQCiJoQ/211BzAd03xNIqqX07TCo65Dec30xi/4RU2rtxQIgIUklxPSNG44MKCwSrSZs2iDwmKy+lcGvGoJpRG+fvTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhto3BCRA9TVsSAnZWagAAzTkP/0i0tKs4wLPkIXANV+Om\nA6VcQ9HssFDBkJ3/1GiLjAfuTKm/93W8tTeXd1Oq+zeJz4DnJg402dBoogKk\nlRkHzzEFemE/NFnqBnm5YNRms7BwBW5OvN0LozuMtRju/PXmWr9enDtj49gR\nL/RbV6s2GE5c78BYL1f5AUWvqWW9RRs8OaUUsuD1PBbKYNEzqMuzfY6Fje0Z\nmex1bZa9XjMCerDTO9XjdZzqxrGGxE5/JY/DRmO5eTION4BJHrU0eNVQWNkQ\ny4GWRJvax9QNa4MEQm5qsE4K2RiGmrPdzSRrbXtvkNmFkqMZmmjccwhqtmW5\nmfImWGUTkIr4KK42Wbdsf0C5DgaphqjHPlpqjUeqoeULpO29EtM5NSCyhL3Q\nfiDMwjXPePRTYGXi+CC7XqnSjd1+SaGTJT7K4SJXYoBd4vyPclShDR4i1cqu\nh2yc/HT0ymNml2MA4XS/Jlg/nQmi9NHWua0E8HoQR5ogsVdkwH6Z9Nr5QClu\ncXIpqGttUGO3C1dM6G8riD6dhnCpQ2uqWcTTm+Zgrd1LvSqOlAtWhExk2BpF\nIOjruwyNtvLjXaKafUymGwp36V+f1PXooKrEhWRid+Ngk6OdBxdkUYsMB/a4\nid8uW8RbgpegopLAmXyik3cXkjOJ0JoW1e0Tct7wk5UKNB34r2NTp4qKnxs4\nIz9V\r\n=8Gx1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.6": {"name": "postcss", "version": "8.4.6", "dependencies": {"nanoid": "^3.2.0", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "c5ff3c3c457a23864f32cb45ac9b741498a09ae1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.6.tgz", "fileCount": 54, "integrity": "sha512-OovjwIzs9Te46vlEx7+uXB0PLijpwjXGKXjVGGPIGubGpq7uh5Xgf6D6FiJ/SzJMBosHDp6a2hiXOS97iBXcaA==", "signatures": [{"sig": "MEQCIHkfq2QrMFM4R5+6wv4jHtBBMef24hfX33Pvopbp675YAiBKzrP1ezafsJecTV6eyUtMuvRgKfeskNmS5IbGbM+Ccg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+RuPCRA9TVsSAnZWagAApR4QAJ94oIMS57OS7V3bXDrw\njz7C2GpmgcpAqAGu9S/sBhl2VPEFDRIp1A6ptow+Gt8CgQUqUS6Sk5etXOca\nhFAdGv4H8+JDD19i4gc9J/I1JT7G9x11qtiaCNEO674ihpSkH2OQ8SW2hPkH\nCoSExxFwYGLYx5LZL26dyCPsq/WvgBEx1/FbXKUA1qS21jXmcxbQAEo3bU31\nxeW0FQLhXe5xZKse5Pj3ORnxk31/LfSISME/uJt537JGe7C/c9q6tZLnX1GA\noi73pVx8TCJphEvriM19XGVdjPwn+NTRB5EFpcUO7AN7w6Yi4cE/lCYhLoVt\nEu4Oq9ppGnY3KYr4nGUK/dDt1lrfhSyY77p9zFGR46WC8B46DlDRPmzbJXXT\nCSo1KhDkaHy1/mp+lTaZZAVYQ33kF3Gv+jMSLh28Kyd2ATkyku3MQBaxQMgD\nJVKqm77aGTv39mkE+RhV3V8O9G6oxiXqH1mytoRVI/ZWCmvhe1wikSvaWqOm\nIREXAdclsUF8T2kGxi3rCQQSwZJoqllwrQgj5auzT3k6IT8WKDvY1QLQP7tB\n+fTUy8x2q8f+VFEtoQPPFFW31+/po+aGgDGGhadwjTvYnJ9rxaWuuh8rxGiL\nnpzIU4xwRrg/zCUR6YuQZO9wPCjbjdqnzeDmqIVdgTfq6wAWp4o9iRZSAQJL\nxMKx\r\n=p6A2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.7": {"name": "postcss", "version": "8.4.7", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "f99862069ec4541de386bf57f5660a6c7a0875a8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.7.tgz", "fileCount": 54, "integrity": "sha512-L9Ye3r6hkkCeOETQX6iOaWZgjp3LL6Lpqm6EtgbKrgqGGteRMNb9vzBfRL96YOSu8o7x3MfIH9Mo5cPJFGrW6A==", "signatures": [{"sig": "MEQCIEw4dAS03xDDJylgRz27eITTcSrK2A5OXIPy06YPom2CAiAVVxGzYQmKo7Gx2vZimjkSsKXoc4c54A3nvyiOaslUMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186094, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiGAGyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnRQ/+PdDW8rslNqCCu/d5TxRGK9Xo9f+q9MtQYvF+0+p2NvX/m0zb\r\nsno2euwJ/pDCPPbJgD3ermuOM6TxWdskMOGo1J4z6zHYemN6rHb2JrxgvowH\r\n3F6YEK2KfOlguhEqsoZfAkpeD7a24vNximr7A0JYZltL1/7sYCgQNmwfl7Ed\r\ny25GJtAXvf+YRa46EsrW76vQ5WDMFjRVaua1TuTobJtWB6kj6hUWBsKvc2Zl\r\nv286AKTRQXdb6CS6O+i1N/qO2F1A0F8/744KKxBLG1vICcnmX4J8EV3JUkER\r\niKl22e9mx1VAOrH26Qah4iA7nWL3qxzmoS9irP+apWJXLRdLRtKu+tInu9rb\r\nLfwOfXK2Tcp8QWR1ovH7G3bsZ8E23lQ48dTsr5obVzbcl5IrkJBnqjgmyKpX\r\n5dcRWx8AUKVv2oOf8mVMeV6fncIziNWP37TQavBrJBNV423b06VSJmcjPud8\r\nMFGE6tyZtqyQyNKKhCptuhUUxNnJbl1tZUMO7yHLrhdj2KbS65koCwOPBkq1\r\nH878eaB07t/7Ld+7xlBsnoTgDn/3cTODY0RmzfNzKMmZyDU/KZUm6/X9XPGL\r\nABTy1HcPFjTtXXFDtAEaQuoRabn9kWHcOHnJZ9+4hlLAc/9jdQhj2QX+quNA\r\njkEBx7V0/wtH/0l3LVRedso0nhFUNoDwN9k=\r\n=DLzq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.8": {"name": "postcss", "version": "8.4.8", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "dad963a76e82c081a0657d3a2f3602ce10c2e032", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.8.tgz", "fileCount": 54, "integrity": "sha512-2tXEqGxrjvAO6U+CJzDL2Fk2kPHTv1jQsYkSoMeOis2SsYaXRO2COxTdQp99cYvif9JTXaAk9lYGc3VhJt7JPQ==", "signatures": [{"sig": "MEUCIElZyvoXyypnJuEyL6hXY8xxxVU/XTrr9Fck+KFlgl+CAiEArUvWM645jocxlJIExg1MmDNuaS1TjPG05DFZb9tCv7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJWmBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8Xg//dpLSOvS+FylbbprMoyOMYSG8hXQ+Pj6+LAI5xjFs56S25ErL\r\nCiVaR6Q+kNl+dGP53SVt3lffmEmzkadEx1D9EMMCOora8PeduzKlOS+4cRJi\r\nIVufm0KeTmafNOR1H8dMKPibhS+aTe7C5CnpMmw50uczm3tKidTv5XYCecXM\r\nfVr6mgRJ+oARLycqkZdrNBPPyORmCCYbBdLXcF01aBr9v3p4BhfH2lgwKSq+\r\nT4Kx/b//ocxkTVYt7V+utDTz7xy7rClj/vqBiqwSyg0NEqwkSahq1kZcTJBD\r\nt3bEBChrw9mYPAbwOMM2njzsMW3uPOK3ODVZkbv/R18Q4Ev1j8WRzR5dJZkN\r\nu1QpgJ6JcfOcd8DgHCEfSlelslqO3UFuCxgprIRTH5fCt29NEQ9f2Qc62+6m\r\nRpungucaKB7tfYVKQG+uPt3ycK0/uj1ou5wWwaW3BTBx4mCHEkoC8op9gA+f\r\no2T8vHyHjdhicCMkm1TgDtbdwKtYAQFwjxfDrVSZbQM/Z1Y8TKHx1GIoKaPz\r\n/s8gVhaeoq+9T0EQV0WLe5jBaAMCrbvV99nmIfxl1a47qGtGo3a2sAlORBXh\r\nUA5DHPChuhSKuoY5sYLmGHJoMTYrplFrht18rIPC4xGWItsSph0FOod5mdNF\r\nqFTWArSFK811wT3WUSNRq5Ke67tc9tVs8E4=\r\n=glpV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "8.4.9": {"name": "postcss", "version": "8.4.9", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "3767ac887bcd0ffe6ca118f93f72b031537998bd", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.9.tgz", "fileCount": 54, "integrity": "sha512-IxN4AJS45+nsjfQGR8qfPedL90Ibx5knHbtsXo5GHiF+K00xgN33rpUS2c0O22Z39xCMtUh1LNmw5VnZdl4YKQ==", "signatures": [{"sig": "MEQCIDX4qzwtr2cg00wbv2VIOZgsHR7ULya/QooGGawSoFiwAiASXtOz874+vYvtlqeKzBkE3AeewwtVk1yXsFeQknc5MQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNYRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3cRAAkIWkiLxjpHB+z9crRxMRwcvvnB/i1HgeJ/BFYO++tePhT6e+\r\nasV91tDqmRpL4URDvCBVB+vQW1Ea3rbs0NJATUvbLmB4/4tYfx5h8PF+SFbA\r\nC8tIpTLPS0JrDbFQlNB/tBE3H5N57RNcbYtRtwXRiNWPVSphvK9MOtWn6p9E\r\nEI4AYHuE4qOvXH22yKkDeJ3rJhN7H3I1ZnKaG8uFxKK5/qL5wu1K4mLsbYb2\r\n5MfqhCpW0MGNRZQAmNCNkyl1a552S5mt64JRiVaMG2xNCNxdoXlC1Tqx5agz\r\n9u4gSFOp/FcJdrSLOrnvAQ3BRlPxMEkl7+5DvA+pOQY8+eLtRlTLfblXOJ60\r\nRGpCtI1NnLUGyhShKOL0n220nnkNyD9phayubmMzgM2GMYdZzLFEBozKc2oK\r\nyAyDa/9Qha/n+Z8MoXXQKVu6aY8C7gVOpbmzJYqWwRjEpD9A42Z7xgYamBVg\r\nBbzwsNND/L2K4GHnMsn23EyBIRbtm3LCKDOJ5rxX4Y684TkIjv9IA/kr7DM0\r\nCs5EkaFtVf3oNrvMNnErDFLemwU05Cxss+W4dmIqlPs3B1NzMK0kkY2fBlk+\r\npDl72M8aL/dD53zwo9lgNCvw9V8uKewbL4OIo+yktlkQCpYd20+7snXP2BjF\r\n6hcZLWwcxYAGsdLybYvmlutyQRefS3G47zk=\r\n=iLkS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.10": {"name": "postcss", "version": "8.4.10", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "0b9c326e3ab3689dbd9bb4c26039ce96be71c0eb", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.10.tgz", "fileCount": 54, "integrity": "sha512-84ERZDkObgU3JLeBzqB0DK3+kFHhhxAngTIeOL4cAykcVYaY4ie3iAzaroXpYeHJNZcPIVvbGAyRxDsqJUUSJg==", "signatures": [{"sig": "MEQCIG1zwYJJ0yVu3imtgvmqZUbcBmP4cmeoX7iv4w8zVdlpAiBrZ0nxWYanHflIUWG4VSQsh/rBhpMRlYPKHISOJbqUsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNh5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpuqw/+I6vrkjFrMC4Kq+uuWmH0NKsZHfekJL1CCZGn0Oc9j6LmjpEs\r\nXlhluejsAXR7UzKDkiK1x4DjPHJ4cNduSVPSIex/H4x/PFQsgLMwNmQ1Q8+v\r\nPPSa7rX0Q8FEp+UR1EQg3uEPRd0+OQUnGSMZMqn0OOS6YNB4klQAj9JDwB8h\r\nU4yIbg3+t7YwGzDTMjfOGOIs6L+tWYVpgtRJRZk6BpVmj0DBAlM0K/YNuJlT\r\nWsQfDiZ4EwFqDiTHHfE5HIW/5UyqS1M/sbe4b1+Gj2kPwQSJHSXC7Mk7dAYc\r\nOcIYGzGv8QjiyhJl98j8VjaWY8dPTqvSmZc/nBdSrjJ2Vvu2RK2gL7OD46Rp\r\nVmgV45Mqi/NFju+Z/K5//ggoNm6Q4eHwBTUKtjQ8SEbs7pEBGT71CwUnI/XQ\r\nS+bJAlNrKu5gHnSDtAFvGz3FdCEqtJa+F4skdAi0ksc5k+YWxUKrHPQsU5y+\r\nj2IdTPkVtX38PbNXJ+9jr0r+JNTrSsBlLShy7JvGA4eZCwyF8Vl3OYSVwFXd\r\nDRbLTZB3vMJvXEOMPtS2x8NeA1+DqOkdvXlOFS+rPK0DkqYPm07LD3bS77c3\r\nK5hBBQ41xzgg/NEuphp/xYEUsZ5yvOeScRkbATic+0+niWsoNu5Xf+iszjXR\r\nDMi+C8DQW0KvwRoTfDjlsZPecHoaKyxhifM=\r\n=Sph5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.11": {"name": "postcss", "version": "8.4.11", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "a06229f23820b4ddd46500a3e38dbca1598a8e8d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.11.tgz", "fileCount": 54, "integrity": "sha512-D+jFLnT0ilGfy4CVBGbC+XE68HkVpT8+CUkDrcSpgxmo4RKco2uaZ4kIoyVGEm+m8KN/+Vwgs8MtpNbQ3/ma9w==", "signatures": [{"sig": "MEUCIQCQrOuQvw19HAG3OY/Jqh4qyt77912FeCc+PngeTmhiIQIgLj915X3C0uR6LaYOeWCXlHa44DRTt0hS3s4qd7Ph2xo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNq8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPLA//Y5VHqZWqoAl1x06YXg3ue16bnRafiuumMEqNei2Vd6W8ffc2\r\nef66oLAWZKkrw/GQziS/Jr4ZmNYEotWvkV0o+A7VWIEijCGdF0R+gGY2fCFD\r\nPK4H52XMOMu3cBiM27m9rBO9S4eGymx5BDRwgTFP5l+xrqDZhecXq7wcqMoU\r\np4KkRYhB9d0e9Erb5vXnOyfhwHA63zQhAZLRK6Lmk1gtV6oDpPWen05r/O8r\r\nHwRsYCC8axY4F62iPoJ/elOF1P+DucNfmjjtCciQUHHgpGwXuXOkYYNoit5d\r\nzduu4HNhzftug8ESMQkWKYKCRE0c2QEmSr03L3+EFwDXEPuoKJHkIPYEeUSW\r\nDjOxXGp3cb67cFcRmRbdV+CL+OeLV2pS5YqenjqzS7bBPoMGeNhXfYStdrkV\r\nzGHFq6RhxT681z0nX0rRCbC90Trf5V6DGQkyadNsG9C8+8yaERBmdyLFgh6P\r\nK8x7AceZf9H/FZJhrzYcu4iAz+m16LsWu3eNr5Zj+9iofFCNfN2Uq0p4yqgy\r\n+KHjjgXZYiDbHWGZHNLp5VKYu2LzVDL9TG4CYmE0NQLcsWSgswjQSkfxw2gE\r\ncw7XVr3pS4QG2y5e/GnCZHGEI3g7mWUAezxEkOmUvcf2vPYolAaE1wcnPXoX\r\ngol9iSvL0h9wGWBaHyRiRGDLG/M/IJLI8Sw=\r\n=TDg6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.12": {"name": "postcss", "version": "8.4.12", "dependencies": {"nanoid": "^3.3.1", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "1e7de78733b28970fa4743f7da6f3763648b1905", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.12.tgz", "fileCount": 54, "integrity": "sha512-lg6eITwYe9v6Hr5CncVbK70SoioNQIq81nsaG86ev5hAidQvmOeETBqs7jm43K2F5/Ley3ytDtriImV6TpNiSg==", "signatures": [{"sig": "MEUCIQDsY5FzlDxPix++hlmNNz/ffbUfJMc1IKcbDrxQ7tAgvAIgLIB6wfkhbRs51JMAPO5B3ICTsZRSeRGMHIQ0NFBMl30=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186441, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMaIIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ1g//dyVyxWkHRUNAPAszUK9FvTc0ihJXmWdRe9WLvx+dpw6CZ1qZ\r\nO+snWWe34pjSi7NhTJXScFNtr1lZd0+BArJQ7uEaBTST17C++itGunbNSDpu\r\nHZJTH6yhwkSRUfh9bi0g6IhEegTFj5q/+9jIm9n+QuOhvkEg3VPNtODgYION\r\n+tD16UIt8X1YRZfKeIL0SLFiBAsQ2rfqVdnbwllJCnIYNcaEnD7Yd31Ze0Tu\r\n5fyj2ElmP6CIJZrl7pbBcw5bVkjj1gazcZYFjjnV/PEH+pEB+fmQ+wdvzSxu\r\nGe0TkIhvKw5A+0EiKjb/Nd0YiHhNdI+1rPC0MB4CcRgL7e4KC8YAPjtJIvGg\r\n08It7UEkAhr5liWihBUl7DD0ut9tK4zpn02GoaUxfPx/tAKHoF4dMxN0/HMk\r\nIs4npfpkhm0bVgvPDnJPR4RP+VXzQR4nUiY8AzF9mP4wefRuDEOTO2JlU94o\r\nOcZR6ZvpCTa1bKuPGLqJOZ4m+cbiltOAyFGuhQt+3kJQ3o1X58pU6zm0Kuw9\r\nzIdkd9Y/0+rJEjSTfHAXJ5cshCXH5+h81Zm/n/Phz0HzEc9SiwAgM1DuQqMs\r\nJlvbezhZoTI1s9ebpgzKYMuaO9HG5wms0zgUucUrGyulgZi+HTGDDqoyxdbm\r\nM+qg21mhu13XUg7pLNPfmZPG2lG/a7aAoio=\r\n=XTs/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.13": {"name": "postcss", "version": "8.4.13", "dependencies": {"nanoid": "^3.3.3", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "7c87bc268e79f7f86524235821dfdf9f73e5d575", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.13.tgz", "fileCount": 54, "integrity": "sha512-jtL6eTBrza5MPzy8oJLFuUscHDXTV5KcLlqAWHl5q5WYRfnNRGSmOZmOZ1T6Gy7A99mOZfqungmZMpMmCVJ8ZA==", "signatures": [{"sig": "MEQCICSkg17yMn5qwGfn0IZ9b79FP6GkNViYTlRsvQOwTxGyAiBpsGU+aroeDZq6HvsDSF9GJ9EIE7FdGQ5IKposI39QHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibIe6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRdRAAiKIJCz4XVB2UtdWJFCHatCSyL4/KxRpvov4h/wWdTriP386N\r\njfdXMnWQG+Rhk6a9OtO5gv+1XjSjWcLPT7A+qH1w37tLvh5xmT991tCdGB6S\r\n/Qs4mVggPxq0ETPU0wQ4ZfNMPLe3F+750iYK/bbjMj9OgJmoMYTfsyv0cr3Z\r\nkLFv8Cmlj1fkdF0KZiX6TV2yPI8rPldAy50VphldbhvIpy2cPGY/PDNsHl3x\r\nf1mlNz11fB7eLLIwT9G0p+Jlj4nPzOEW5rPo1Dr2Zp1Lemla4TVwGy1NTZ5W\r\n7TxJfikT4HLHrjRLutfEnDv2hoBORZlvAz9VimRkrxFyPDtlTYC5FXAP9XZ5\r\nXGcW5i1LkLII8dOMFLF8G1aqz9xFTQ9N6Frk/NhdnfX2JW+azlqdUitNjg2q\r\na9mFTgLMKIc4LYqQnN7erHaV1lMm/bdS+2eilVhMAY2U/AbxxzC2nlQZDDFa\r\nyJlkyEdz5CHh1FdlbpfHL4AiRBVL0HOLKzDtpYkmwy4SpP1ChtlpPck8ziul\r\n3KZplqIHpv0JbGoWX/sk95RvOP4dEllHXZAz2kVB6YZ7Yrfruh42M4RIxWLY\r\nQLBsFCdFw9iSvLYQLKRF9Qy3cC/Mu3CcgR2PL3eV2cNjP+o93UYBcC5SaO0R\r\nzGxIf05dfEwY43RLCKSspP460RfUbCuqnro=\r\n=yrgF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.14": {"name": "postcss", "version": "8.4.14", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "ee9274d5622b4858c1007a74d76e42e56fd21caf", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.14.tgz", "fileCount": 54, "integrity": "sha512-E398TUmfAYFPBSdzgeieK2Y1+1cpdxJx8yXbK/m57nRhKSmk1GB2tO4lbLBtlkfPQTDKfe4Xqv1ASWPpayPEig==", "signatures": [{"sig": "MEQCID/s7VIyl24XXeKas9CD+lBHVUzZmPaB9+lBo+6B2GyYAiBZISmpmonWvE5zLRBNaxgsAntJP+BmSvsAHHhIH0J8+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186549, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihRuDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/rg//VeuA04r2dsZjKsg45pHOEQI5w9kNtvlA3i6nPcdNSg47F8a2\r\n37FvK4kqF0MGijbPcVQVAivQLGSriqIRBMifhzQlciUbz6GRhPkhrEc3qgYL\r\n/i25kHJVs/ngYDaWzgkXZIyJXALj3cDlR31YHMheCDiwNsKT+LfVmP4EEa0T\r\nPpRP2CJKu5USz/Xp8CBCXFIE9YFdYTReuieAe3PvhL7Ih8csYuFXvuRoR8Pg\r\nQ8ZlfPdDcdJR58dDRMOGQ2PmXMybUJ5ipbgN2Tb25OwVodoGCxUbM4oG0h9A\r\nRCLIKfx2xk2Iy1M0y8nuFnz+rzMremevabpFI335HeYoQYM4NO8GFJjK5GPm\r\nqXn5U7LqeyGeQL10oES4ebDduGZoGx8KLSdoVjn+QGlEeTv4Nz0+9axPKOX5\r\n4PE1MPbkima3r93DZaxeABXldIOreYA3nY41Py3tf+IGAN5HPA9YFnWDjV0r\r\nIgGR5Jh3Cpku/s9VWnYlB0813Cj5mpw2hJSoLptD1FVQ0ND52yDjuc0botQa\r\n5IoJR1UoAIn1+dE33Vl/Bnm9wWaXgxq5gy/Tsg4O1eTwY4+n09rR9OpAfOEz\r\nIUVMNjX1aROzZzccCHKB4cl6/xf2cirT5iEQV44Ve1w6bSQR5pjMtodsEHzb\r\nKnvqcQ16sy7FSgSovHEr7vU2O2vBFuxsIao=\r\n=4jBR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.15": {"name": "postcss", "version": "8.4.15", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "486a38dc5f2e841312c67440e1a2a69be70e9a12", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.15.tgz", "fileCount": 54, "integrity": "sha512-q3lixwKdXOi8OYMNTMTYDNuBEWIOf4P5OlGi5OEqcp+XGiDICNZexuHJz7xttkLmpRz7R0G1I5lGFSi9F0C17w==", "signatures": [{"sig": "MEUCIDK1YktPmhKR9wHfBoMoszioGE9194i55Zqbi3RYMEb7AiEAqEoAXiEkmmYUXJwLqGYWkqMGGaz7NMX5i3SUEBXVZ8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7rIQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYRxAAhKVEFWQ4DGPj7c823LIQLh6Il71GOSfMvjDfnMl1ftN42cqs\r\n2lvemkYt8nJyETSkNaoZdlYbNdpR4MpOysuzzkK1gFsGcT7atbR6pwmVWzHn\r\nQLN1axbWZPtt+jvGTGlxXyurYosFgBjLbSrlFlgpIiK0AOJhjvGEUII7fZvd\r\n3KITPbmCcgexSfukE00XTirI1YaG+uqV3WHY74mq+F207Gqcu3s+t0J+1ArG\r\nmyLUfT0Tk4VM0V6cLl6UEhH5YHqR6JgH94qFeZfLBIsUFI9vajLMPIxRl5Cb\r\nFP9PwIJC1c/al4eWFh7xHkiZTpGbjYETnp9JMlw+eryfXpQj5uPaIvbT1pDE\r\naxh/ywCT/9Qo5fCusUu1X/gLRo3RfqSe6jkfj3piaDVECxiVjMTNqxFj+nFh\r\nQ00Q0TcotQ1x6QXivjhWltt9ytyb2DWawwHuno31Qav6lDeWEWnydxK1FCeW\r\nt+VCp5JUAvuKJThn1MV1+/2SPEp9w+TWoxujNzys+wxt8nJvqr7eVz97GzNg\r\nR9Pe5s2+U1hiJSBc+x9RBGAiP9IICGxRsVQiBvtTx4kp9vOdcxpZS4lzFOkb\r\niqD28359f0A2IG54nUHXLmoATNH0M+kYn0dtNEuNCiQcZWBFWD5ZGs4nRBmm\r\nkXGELmap6sbnDOXT0aZxeDPCW5prAwHiXck=\r\n=zIjG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.16": {"name": "postcss", "version": "8.4.16", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "33a1d675fac39941f5f445db0de4db2b6e01d43c", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.16.tgz", "fileCount": 54, "integrity": "sha512-ipHE1XBvKzm5xI7hiHCZJCSugxvsdq2mPnsq5+UF+VHCjiBvtDrlxJfMBToWaP9D5XlgNmcFGqoHmUn0EYEaRQ==", "signatures": [{"sig": "MEUCIFH+UIT24wbZev1em33zx3GPflt/CQpILSoMyWVjE9kAAiEAm+rmAT1EpBpKUBSHt5thUpbQx2yrjT2lx551OlFR6+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186792, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7roqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp51g/5ANdhuKbUu/vTRgvT0z3wuujoja1OBzpykXOk25AILOFI9jdd\r\nu/SJrsHzbYqnpLJmgYDlTTSVidJfwaTOcAGY18/rVXsU+5ivxPGqjzTs51e2\r\n3OoQOKlLj9lLNyua0DJkK7FY2Jx05Z51Q67MO8iDpUt7RMtg2RMPv+GHMcP5\r\nJb1YuU18kCNa54wryaENApQFLjiNiqh05acJob2hXT6rUTTS0wdxZwm6RRUC\r\nDFTGe/yb7ECqEt9dKCwfrjay/GklAi3WLT2Bv2xGICK6eemnp2kLeNfzz95q\r\nbAmYSZxOwRBXagoaDZ7PXKZ6ZWkTYbaj7yyuhIRb3801nzDodPEd+zGnoxso\r\ne4lgIoXEpsvRfp+aF9DnoxZygiuOylb12Dkbpl78ENH3xrliZEIzUYDJhFYn\r\nzBRLAo6OhvRcGHh+BTSzkuMStzlsIgnPupsCoGrq2zSg3LDoRNFwRDXtmZHa\r\n2U8SjyN8WSPzT+ZU3PkU2miDpWs3JHvkoiBVbtYydR9FBfuwxxrbUJqzpIAW\r\ngtYUeAiSiUl+Qtb3XKzLNgInQSDV522Bs7rFG2NqlyZ2rKFp/cfr1/fXnNQ8\r\nNU6F04FDq/e92bohVExgnJiqb1kHy7iU46WA0Xl/Dt6V+eTLQ7WZ0gqnFzwU\r\nxjyltxUByKuT/EtQeqbK7iLUevog7Nt/LKk=\r\n=MS1+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.17": {"name": "postcss", "version": "8.4.17", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "f87863ec7cd353f81f7ab2dec5d67d861bbb1be5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.17.tgz", "fileCount": 54, "integrity": "sha512-UNxNOLQydcOFi41yHNMcKRZ39NeXlr8AxGuZJsdub8vIb12fHzcq37DTU/QtbI6WLxNg2gF9Z+8qtRwTj1UI1Q==", "signatures": [{"sig": "MEUCIQCK0Y2/VcLxs/yeDq9QF1A+TlLs6K+dyqDl77USV56eqAIgczRwM+H/km36TvWhzZgOiSDn8QPooS8MoOX8ULd+iHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 186908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNtMjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBBA/+JojafbNj2ceyPXN/yul74iCsRfbOZPhAUT9bp+OaSHCz4gW9\r\nu+NeguMZWpq+MYpwE7pxwhEnNIZN6dPskNHnQ+S8dyXKiLmy4W7O+jd4MOR5\r\n8PpEBLHYMgHk5OwejlhAXWkkeGjrHnYuUtnjkUTykMdt1swus5F/JdWJGVDq\r\n6599xP39wzxS0en4ID+wcbgT2B7duYribB85V6HAUUsRYkBEcL6tGsBo/4i3\r\n31V+5ZGqUxeva/wSqjG8KWuMEiGR4FUphzjlVaDkfZR64jt0755sEll97P61\r\nL5PoFD+dX5mmu9BhHcQMMQJkoqPTodlqYLf7RK09Yw2EOou4+HbiXDjbUb0S\r\nRwuifU7HE9rB9MTrue18qk9oJAd4bONGbuCEWmAUburNEYTeLs0XTVQIGZtZ\r\nGZHp77v+wYkZXbvhixnmNujO9W++n0WNtGaPkw1HeP/B/u2sb1/35AbH5yVJ\r\nQNqEIvDsnllQXVxGPKtN66+mGTFCG+ONhJ22MDg/N21qm92eweGnWnucbUAE\r\no9k2wCI//+ruInhZtXrKV6P4tWr14b6bLcGQsHxSOQZBwvFDn65eSpgAG5l4\r\n9cpvDUgShep/b0l8FpMvplz+SA/hO/nJXivT5qmOzHbnOy3mqtan5nv/THcu\r\nUDecZJM3U+3vLpOQ6doU6r4OKa8Y1nKh2WY=\r\n=Idme\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.18": {"name": "postcss", "version": "8.4.18", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "6d50046ea7d3d66a85e0e782074e7203bc7fbca2", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.18.tgz", "fileCount": 54, "integrity": "sha512-Wi8mWhncLJm11GATDaQKobXSNEYGUHeQLiQqDFG1qQ5UTDPTEvKw0Xt5NsTpktGTwLps3ByrWsBrG0rB8YQ9oA==", "signatures": [{"sig": "MEYCIQDFmRcU4MFpZlh4L8mrsM53OcdMMSDy3X3/9IQsuHImIQIhAOgNhAk1lpbskITULsXEcb1qIeu5YoavV4NzfqpOQmPD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRxV6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHSA//Q68NoCBgYQFudrdBawdJPDzwi6Q5bvHuRsfXHgL8SdeBDpWf\r\n2WsBOVXfEX3OCtq3Jf8OX30l9XXaDxDehMJiHveNvuYu5aNAjQxiseGip5LN\r\nYXLbIjLrco/0BRbpNloN+8zlo4h+kOGzUe2MB9IVzG7K4DjGAO1dgi0nlLXt\r\nDUABBt7k3uw5AF+F9mf2jASqj4TwdfGS0fy7vTofLbP5rQG5erk/LvMe9nwA\r\nkZB/3a+NDh7qXOHS3OH3injadZBHrFKWC8dNL6IuuJtV9KyZbI0xSHhS6r/f\r\nQNSMpX1+/wJ2DEwaLPV7K9GrjcPOyITld5zG48AnRjX0iWtYB8yfa0B2mqKy\r\n4qXJ/Slgimn826n1n456voKalZXfmb83CUPOH8SWmMIYre2m1lqc9+VTnaVp\r\n3NHsxIJJ+/wJ6MqePqnD6adtkj33e0X75vL+vMGvQOshaBUpGqa9yeF4FLqh\r\nU82Ci0hHS5+ZK0JQPFeCRRcNQsuoG3xJ09z/2bXkRgavH6aRpcdCr2cKZ1lg\r\nWb3MfGSyyuZVL6l9nV4hYeZClDNNS2/CBJSXRKmgFctj/L4PFq0T3+wUT8Jm\r\nwZmwz+y5Vh1qEgEwr8hQt2UsginDTETFWmNbGzfCCq8C4IPc9JJQvTZSmj+W\r\nuBO46eacd4etdTmg9qDca1BQ6Te1Ygw1EoM=\r\n=RmLv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.19": {"name": "postcss", "version": "8.4.19", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "61178e2add236b17351897c8bcc0b4c8ecab56fc", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.19.tgz", "fileCount": 54, "integrity": "sha512-h+pbPsyhlYj6N2ozBmHhHrs9DzGmbaarbLvWipMRO7RLS+v4onj26MPFXA5OBYFxyqYhUJK456SwDcY9H2/zsA==", "signatures": [{"sig": "MEYCIQD1JFNnJF2y+gsH3qfRifLlaEiynKqEeeS1bQiZ1CMzqwIhAOf55xahqBfPYJH++iiMUlzZGlzKtgqiEkPnuXArtvAB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjbYHOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9LBAAjSuVM/SBSJmtfedVQzW/k4LDMpj61tVe6IeCJ5Qv4bHA4c7R\r\nEXFy67V3VkfUvSCnGHP8cNrrBwxqfY9zBjAqhUlnfuwWV+clOdLkCgU2ZfWk\r\nl1a1nzExnQJgds5QlN/0fXBq/z5YZND/eTFMzPQNwZLHq2pERY5ieynJ8IAt\r\nu9ZO9mCrzZGuHnFIGGEMOyMDgoTQ2PMkSNck3HbFrGXq+lC4QFx+lDcxLKO0\r\nU+ziQzVhHOE441/xdkpmv+j6SxgibsSAr9IGoTPxDunvLSQeb/SVybfeEhbG\r\nRhlyKFAeKPMioyjlGknibiS59LXM2WKp6cSXqzr5et2pgHBwYJDPszy2mG2x\r\nu8Z6+snY6HBobfZhOG9ZoREDBWGJUB5GUIEB+BuumQe5ig4wf6rc1TV3nJMR\r\nuvXIkEck3CT94EboT0/e9akYAbHXuBTOnzSErTJhIPmkPVGtVO6tyHWQaSmP\r\ny3bP/kxuwUE4NjR2smredmgT9vngOFmxomN3JTIaDYWX8DrvGsq5E1XzUXkz\r\ntw6cJv8oVynHniAMXynqh0w/5DdToTFtR0Kd6pPjezKfvPK3w3/5J+xCINJd\r\n0tG3jOoiXFYBPZcORBHA0nX6U2LCsNolUtrb6eKxOazQRPFuet2ynYNraESP\r\noK0hhnhQ3NXl39ib4SB55R70uLfeeNJkmM4=\r\n=TFoA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.20": {"name": "postcss", "version": "8.4.20", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "64c52f509644cecad8567e949f4081d98349dc56", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.20.tgz", "fileCount": 54, "integrity": "sha512-6Q04AXR1212bXr5fh03u8aAwbLxAQNGQ/Q1LNa0VfOI06ZAlhPHtQvE4OIdpj4kLThXilalPnmDSOD65DcHt+g==", "signatures": [{"sig": "MEUCIQC9ufIY+r9QrckVm2zlxGxvmHu+8lpMTE92hz7wlzSSkwIgdyMFWaBjElFBi2J1G4ZOa6ZtoPZXf1payNaCaXdMMGU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlhTWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXqg//XcGJYDypDEZ2sZXpm7gZVs3/4wwTgrQdNsoClIoaFYIMAyAT\r\nQvwfzoN7CZeJYQX2Z/WoaA85vVX06BmwG9w1tgNU34mIUwzoxywlHSj64THM\r\nbKSMxnzi6W/PhyL35wIprm153ZEuUbmPIpDYRVSOBYzQ+h1gJUbOkLwEbOA1\r\n8LOILsHpY2T4YHI3HQMeGKmD5lknlliozXhP9N94ZnQNrTpaXVqGyLoOHfsy\r\ndK7mFIJiSs+jTu6zZaMs/6JMA9b/sqGpDbgXoMzuRsSYBI40eCi6gg1XfDcE\r\nuXjszpSD7nDGos4jAqoHF8fUr24jpFNUUXI2LUT99rjyLZqxk831PQsdKSBS\r\n/hHpMummeJFS2Xv45p34+5kMaa6zxMsfsKBaIm91Lp/XZantgSPxDuDJSr2G\r\n/4eNA8w1MyDoq8GZ4TTAiY0IzcMHM/FcbhAMjGnnjAjbd+JzuuJaUTflkhmt\r\nP78q/4U/oTTLb/zu0qRCFXKpIIqd8BZNDYf+ZQ3sxUk3ua7zvJ3+cfieHr9Z\r\n9+fOn+3HX6GOYI4tyoAw4QpmBYRkGPJK22vdHhbbW6f6k7jyFequrpeG/jY8\r\n87vsTZyW3aZFAMmplt0ymHMo8HtdvmT7c8gQvftRlpfOV6UZKPLryUKd16Lh\r\nbvNLAga/A64IoXgW7jLu+sdiW5xEMfohxf0=\r\n=m3cm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.21": {"name": "postcss", "version": "8.4.21", "dependencies": {"nanoid": "^3.3.4", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "c639b719a57efc3187b13a1d765675485f4134f4", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.21.tgz", "fileCount": 54, "integrity": "sha512-tP7u/Sn/dVxK2NnruI4H9BG+x+Wxz6oeZ1cJ8P6G/PZY0IKk4k/63TDsQf2kQq3+qoJeLm2kIBUNlZe3zgb4Zg==", "signatures": [{"sig": "MEQCICqPJlp1CLS2b0HueeRoaUmAig2lQ4tV1RbhAAQQL+GGAiA4ScAcu5d+N7+xV7hlNvfffakcGKc4/aGQMYWsrhdMyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 187861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjuISrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKTQ/+NFBJ9z5UAft4to5JB0M/Jn8bTBTyPfVuqP7rG7VxK3aQbRY7\r\nu+IX3p0jS4zkVi/dBoZKv+v5xbHZ1Y1KoIlnxumd9VFSj19sK+HwZUfb6LtA\r\nHMy1/Df0D+S5XUM46FpLAgIhkXrKnAql5halrnYcYqLEEDuiA5lnu/ksF6f6\r\ne3ejNA47b7TMtOkgdVrFzYFM+O/86btOqCcpLjRd00PiM9pjh8f38LDclZMd\r\nCgxnukktw8ID19EMR5+TuyFqXIRs193sjKMS2Z1WaLDyn+C+2LOzNH+p+Ihg\r\nP7iVWBi1Ab0ouHj3VPMroI+O2c8erzuGkK08Kd0rrVppT0SfG18jmP8c96u2\r\ngaldeNTYzgEPjFODrmHzkk8TpQzyf1PW4x8BBe6KFgzDwp5ruPJehLt61F8E\r\nNx6cwrEA7PCp2vUTwUI/27JBDZcgyFMMY9qi+QI6g+Rn95FtWtAAsB+QsJv0\r\n6yN9QFkLyoGnnKdNI86Qx6tb9osWJQKLShzBpmK/m9puIa7b2ouo/DO02maX\r\nRzeC4OjNQZp16sE4daTMQVIRIHeOgYMnj7Nq/xOx4BQ+Mre4JfyJiuaGJEgL\r\n0EZ0AUJfF6SCpVTuLVGkJFgFfLGXXHkOgk2hJi3hFHWzUhynd7y5lsQivWYV\r\n3OjxRVtIDGNYYD0itnV8VZMergG/DbJRzH8=\r\n=j0BH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}]}, "8.4.22": {"name": "postcss", "version": "8.4.22", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "c29e6776b60ab3af602d4b513d5bd2ff9aa85dc1", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.22.tgz", "fileCount": 55, "integrity": "sha512-XseknLAfRHzVWjCEtdviapiBtfLdgyzExD50Rg2ePaucEesyh8Wv4VPdW0nbyDa1ydbrAxV19jvMT4+LFmcNUA==", "signatures": [{"sig": "MEYCIQC8OOfeubuUh2smPNWEVCuEi/sT1L2tOxx6Ci+VDkSZvwIhAITqyzQR05v2rb4s1mBb6YEzwvUdQ/0ZqEEWU64w9cSg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkO/HvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmri1Q/9FC31iW5J5Td9b4mx/s7uyavBzm/1Emlun1d01y0HAkdudxnW\r\nz9s3TS6udxNO1baFq92zx0dBW6E1DLKNnaZA++gYhKKWKqBc0HBGwD5LewCL\r\nrD45B2wwXI0jN9qi5NgSvXrfwzd7iimp7Fd3rfd/z+La33aVl2Jkvg/rq3Tl\r\nHTCcV4w/xWJBQHXWU8hkA7ENv05xIv6AbYLvCZWSgZs/tLy+qA6qz35Iqf7E\r\nJQC/d+E0Fw3XcKfxabxwCz8PTX5ip9V86mbNocJ58iJmOEUUbXvf5aZIdzyD\r\nayd7nws2uCxgl6nA1lTzdpyBIKB/G+sHkkY9RgjRvZMXsAYB8kZTrn6ePaCD\r\nmR6jmlBs+bMGw6abCcL8juLnBerEtXDa+k57pvQgdqq4oeFK5+9JXo8/+Kre\r\nETvlA3ynZmjBuEWakmGoNDQWaIx0N5ivenIooBlDwIWklxkESqywD8MxSElK\r\no8aMqqsywrGBdIynq83RJu9nxEOrmu9yPh60ZpOoHpUReh5PC53tFoMsc2SB\r\nrR7nSBPyXp0o8R7UATxtbFEd38VEwG5HM2n9//jaxL1erjLkf+Iz+9erQTEG\r\nfr/bi7l4nVzJN1ob66irliXQVJoWZLub+PPCLAU9ewH4ZgfLyGRRu+YSlfJ2\r\nJ4rpsh6wuglPW8do9qNjqyEExV8QkCb/uqk=\r\n=phFV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.23": {"name": "postcss", "version": "8.4.23", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "df0aee9ac7c5e53e1075c24a3613496f9e6552ab", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.23.tgz", "fileCount": 55, "integrity": "sha512-bQ3qMcpF6A/YjR55xtoTr0jGOlnPOKAIMdOWiv0EIT6HVPEaJiJB4NLljSbiHoC2RX7DN5Uvjtpbg1NPdwv1oA==", "signatures": [{"sig": "MEYCIQCdxuioCHVj8O2zcxO9iizHKNc6o5WB+m24zFNi3vNl0AIhALOnYOVxv4XRw3hE4q8IrtSknLiZq3vITihVv8YKQ6Df", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkQDsHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoIxAAkuPq8PpABFUNqysOnZODSvvBOxWqvNBpYyK/OWZQFQ2AK0/B\r\nTLl5uJBhsmAFqEC7eCXn0Z42kcuPjroTK5EBCPaYLs1w9NAR5ybJaaukhrwo\r\nROFx18Gn0xFLonJ2A/Ahf9/SmFA8QXtPqkg6GJDKu/CZnZjQI7pr3jnaGkSz\r\nJsQ+QSpdXwM3cWMX5GPwqRbMXeACvXhQcgmUiWBhUrjOBm4DJiL0jMQu2WUD\r\nEQ/jDIivvpd3KwqI7NuXQuQPfI3+ezaRzsAFYqjTfVmxX0i0sN4xxNMRhlCR\r\nprqhfPIbcKYnsY/GNoLlOSCNMhsowRBjIIT+E3eQIfrqFSmDMcVJjWShN8XV\r\nN9AbMBuPd6VMEgS43VhsPLHHLYptTQ51cbJe+5RtxU+SI7eTkuFNtoQenrAt\r\n4KWDNxbqKzVGGrAAYvbLi6gf57JfwMxQ4ATMHzZqgj9Xi6/VuhFGdvfHwOh+\r\n1HYOTfDgXeAXTuo4CO7mxr7FOK2M+Jl6dMxc+Pf+E3aM7Adx5v0amKk633VK\r\n9I7SvSfCYqztJkcbLQZBmTRIlV5MOE19j9D51xb2VMo/Mci+OBk0M9pP7XLA\r\n0Z9YIVlxiJ5BvYUEUt/rYgo8n6NLOowktFj28KkmIOiTLou6DkCmkFdxKiBx\r\ncRVCHfUBcjMxAOSXYFM1KhzExCPUIXQYd9o=\r\n=WRF3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.24": {"name": "postcss", "version": "8.4.24", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "f714dba9b2284be3cc07dbd2fc57ee4dc972d2df", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.24.tgz", "fileCount": 55, "integrity": "sha512-M0RzbcI0sO/XJNucsGjvWU9ERWxb/ytp1w6dKtxTKgixdtQDq4rmx/g8W1hnaheq9jgwL/oyEdH5Bc4WwJKMqg==", "signatures": [{"sig": "MEUCIHLVqvplVbJYu+gSdvtgtKD7VUP5pyElggqoULHVUrmNAiEA3Bc1vexaRQoKZH6VZQExIbqZArS1DjumSw5EFZNAVvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193541}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.25": {"name": "postcss", "version": "8.4.25", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "4a133f5e379eda7f61e906c3b1aaa9b81292726f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.25.tgz", "fileCount": 55, "integrity": "sha512-7taJ/8t2av0Z+sQEvNzCkpDynl0tX3uJMCODi6nT3PfASC7dYCWV9aQ+uiCf+KBD4SEFcu+GvJdGdwzQ6OSjCw==", "signatures": [{"sig": "MEUCICMLMu0rcPIotNJ35q7EHWrOrL+t/EvqVlSWpG78bt6fAiEA/uLzBRbvluRax+OlpQ5pSjaNBnTfDuO3GhGYIHoaJEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195349}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.26": {"name": "postcss", "version": "8.4.26", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "1bc62ab19f8e1e5463d98cf74af39702a00a9e94", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.26.tgz", "fileCount": 55, "integrity": "sha512-jrXHFF8iTloAenySjM/ob3gSj7pCu0Ji49hnjqzsgSRa50hkWCKD0HQ+gMNJkW38jBI68MpAAg7ZWwHwX8NMMw==", "signatures": [{"sig": "MEQCIH25h5VC42KNnFi70ox3BiDheJgy4iOwTpt3SCxV2djJAiAgq5+utsTOLEhBcS3ORqUryP30TeympECBSqXMPdTEOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195300}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.27": {"name": "postcss", "version": "8.4.27", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "234d7e4b72e34ba5a92c29636734349e0d9c3057", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.27.tgz", "fileCount": 55, "integrity": "sha512-gY/ACJtJPSmUFPDCHtX78+01fHa64FaU4zaaWfuh1MhGJISufJAH4cun6k/8fwsHYeK4UQmENQK+tRLCFJE8JQ==", "signatures": [{"sig": "MEQCIC+aFr19Z5nXW6ptC/QG9POPcJ08XcIyJPmfzEqOdljkAiBgw1GyRhi6XKXj1uh4nhgI51Rj6/0b2EvwdnKBQECdRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195586}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.28": {"name": "postcss", "version": "8.4.28", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "c6cc681ed00109072816e1557f889ef51cf950a5", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.28.tgz", "fileCount": 55, "integrity": "sha512-Z7V5j0cq8oEKyejIKfpD8b4eBy9cwW2JWPk0+fB1HOAMsfHbnAXLLS+PfVWlzMSLQaWttKDt607I0XHmpE67Vw==", "signatures": [{"sig": "MEUCIQDpbCRxeVaykKIUupbCChQ3ZhvXKUhN80AmhPsYYZpEKgIgDxKeocdrLZg6CpkgtskcnjFtcaifq5/T0HnamumiPU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196218}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.29": {"name": "postcss", "version": "8.4.29", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "33bc121cf3b3688d4ddef50be869b2a54185a1dd", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.29.tgz", "fileCount": 55, "integrity": "sha512-cbI+jaqIeu/VGqXEarWkRCCffhjgXc0qjBtXpqJhTBohMUjUQnbBr0xqX3vEKudc4iviTewcJo5ajcec5+wdJw==", "signatures": [{"sig": "MEYCIQC2qDARTIHmjDijWV3odt/gTHWNQvwcOfRbrSp8sE2ApwIhAK8dZLvvwtdSo8vQ5rExHMW71r9oveXGaMUYcYBF/NvU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196422}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.30": {"name": "postcss", "version": "8.4.30", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "0e0648d551a606ef2192a26da4cabafcc09c1aa7", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.30.tgz", "fileCount": 55, "integrity": "sha512-7ZEao1g4kd68l97aWG/etQKPKq07us0ieSZ2TnFDk11i0ZfDW2AwKHYU8qv4MZKqN2fdBfg+7q0ES06UA73C1g==", "signatures": [{"sig": "MEYCIQCMUDIaY5EGIZ52BqpuiV6qbssDqtOYFeNSm5peqc2RyAIhALL2YaB7GVCiXQB0r5kr3U2/l3HHD8SSFHv0mfBydhIJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196515}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.31": {"name": "postcss", "version": "8.4.31", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "92b451050a9f914da6755af352bdc0192508656d", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.31.tgz", "fileCount": 55, "integrity": "sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==", "signatures": [{"sig": "MEUCIHXHVyuNjd4w80o0ILSXYQZoNeBt2CZuf1V6ma2mHYwZAiEA/7oCmeWRz3yJy5Ska/05QU6nGEaojnKcnKjauJtjDXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196517}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.32": {"name": "postcss", "version": "8.4.32", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "1dac6ac51ab19adb21b8b34fd2d93a86440ef6c9", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.32.tgz", "fileCount": 55, "integrity": "sha512-D/kj5JNu6oo2EIy+XL/26JEDTlIbB8hw85G8StOE6L74RQAVVP5rej6wxCNqyMbR4RkPfqvezVbPw81Ngd6Kcw==", "signatures": [{"sig": "MEUCICHpc+9DOzrSpgAoW47aLOk2mr/HyGiXhbVOd7/kxGR5AiEA1Ee1Yd1f9H+1Lac0UaAX31B+qEHQ1nmitbKVgMqzwYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196537}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.33": {"name": "postcss", "version": "8.4.33", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "1378e859c9f69bf6f638b990a0212f43e2aaa742", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.33.tgz", "fileCount": 55, "integrity": "sha512-Kkpbhhdjw2qQs2O2DGX+8m5OVqEcbB9HRBvuYM9pgrjEFUg30A9LmXNlTAUj4S9kgtGyrMbTzVjH7E+s5Re2yg==", "signatures": [{"sig": "MEUCIQCkfYr+UbpVSwSgw3cxEmxseDKQWCN9HuPZ/smOT7peLAIgMaPQnz6neAkGv+Hry+WrYWxLsmnJINMk8A0aq4KalXk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196581}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.34": {"name": "postcss", "version": "8.4.34", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "563276e86b4ff20dfa5eed0d394d4c53853b2051", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.34.tgz", "fileCount": 55, "integrity": "sha512-4eLTO36woPSocqZ1zIrFD2K1v6wH7pY1uBh0JIM2KKfrVtGvPFiAku6aNOP0W1Wr9qwnaCsF0Z+CrVnryB2A8Q==", "signatures": [{"sig": "MEQCIFCpdZ0I4bFl67w7kqdzALQrPmrGZbQU5DvrgdcRMhZYAiB2Q3cIldIrJzPmw/yxkt7xYR0ipTyNDOVlfNzaIgjgHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197124}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.35": {"name": "postcss", "version": "8.4.35", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "dist": {"shasum": "60997775689ce09011edf083a549cea44aabe2f7", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.35.tgz", "fileCount": 55, "integrity": "sha512-u5U8qYpBCpN13BsiEB0CbR1Hhh4Gc0zLFuedrHJKMctHCHAGrMdG0PRM/KErzAL3CU6/eckEtmHNB3x6e3c0vA==", "signatures": [{"sig": "MEQCICICdcjgN8ENVsiBVmwplxkP3kZOqCEWDR54SOdECtCCAiBrvuheaE3G+Aj9BJ701lIqD5OdsOD2qGLPmIOR62/zHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197686}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.36": {"name": "postcss", "version": "8.4.36", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.1.0"}, "dist": {"shasum": "dba513c3c3733c44e0288a712894f8910bbaabc6", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.36.tgz", "fileCount": 55, "integrity": "sha512-/n7eumA6ZjFHAsbX30yhHup/IMkOmlmvtEi7P+6RMYf+bGJSUHc3geH4a0NSZxAz/RJfiS9tooCTs9LAVYUZKw==", "signatures": [{"sig": "MEQCIDfvLcJFfScQM10NZShOnO+W8z6bumAVLWnqWhVF54tzAiBS9tcZ9YvrkQLmHwgSRswYbcz8BSTMGtA9mrh3U0+VHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197780}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.37": {"name": "postcss", "version": "8.4.37", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.2.0"}, "dist": {"shasum": "4505f992cd0c20e03d25f13b31901640b2db731a", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.37.tgz", "fileCount": 55, "integrity": "sha512-7iB/v/r7Woof0glKLH8b1SPHrsX7uhdO+Geb41QpF/+mWZHU3uxxSlN+UXGVit1PawOYDToO+AbZzhBzWRDwbQ==", "signatures": [{"sig": "MEUCIEdIQ2rUtIsZfqvjWeMMKJO9kvdnOAJCPwKqOzUOdQhrAiEAmW9T9NXhI9icSmJIo/7XoZ8DeA5JSlOebbM+vsgyzpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197826}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.38": {"name": "postcss", "version": "8.4.38", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.0", "source-map-js": "^1.2.0"}, "dist": {"shasum": "b387d533baf2054288e337066d81c6bee9db9e0e", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.38.tgz", "fileCount": 55, "integrity": "sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==", "signatures": [{"sig": "MEYCIQDy3OZwjEFof35pA2DEoREmW7X3y4SwvJ5CNz8RJHGW4QIhANoTgj248QRCjcfD6x4l/zcWRo89wgzdmxcpuDQkJwi3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197846}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.39": {"name": "postcss", "version": "8.4.39", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "aa3c94998b61d3a9c259efa51db4b392e1bde0e3", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.39.tgz", "fileCount": 55, "integrity": "sha512-0vzE+lAiG7hZl1/9I8yzKLx3aR9Xbof3fBHKunvMfOCYAtMhrsnccJY2iTURb9EZd5+pLuiNV9/c/GZJOHsgIw==", "signatures": [{"sig": "MEYCIQDnBjAicS8QU/DHg9QpPSrL2zMeCiqgSc4mJhchzu3YfgIhALKDUy4bOScup4slcEtSwsY1IozMDejcbtc+oBBeS/GV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197860}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.40": {"name": "postcss", "version": "8.4.40", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "eb81f2a4dd7668ed869a6db25999e02e9ad909d8", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.40.tgz", "fileCount": 55, "integrity": "sha512-YF2kKIUzAofPMpfH6hOi2cGnv/HrUlfucspc7pDyvv7kGdqXrfj8SCl/t8owkEgKEuu8ZcRjSOxFxVLqwChZ2Q==", "signatures": [{"sig": "MEUCIBV2qBXCcWSfLezl9TCFej5hwtlxK2x40o4ZRwm/qA2kAiEAi0KpO2uxTOy+5UAw9p3fBVdecF6ApM1O2a++nbGjUcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198206}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.41": {"name": "postcss", "version": "8.4.41", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "d6104d3ba272d882fe18fc07d15dc2da62fa2681", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.41.tgz", "fileCount": 55, "integrity": "sha512-TesUflQ0WKZqAvg52PWL6kHgLKP6xB6heTOdoYM0Wt2UHyxNa4K25EZZMgKns3BH1RLVbZCREPpLY0rhnNoHVQ==", "signatures": [{"sig": "MEUCIFkh2PNqnsc+2TZO5aiQH3qkPrYRxlqDg5H0fICKAy4cAiEAseFe5NZrXJFnH4TKj+6bSP33E1YopwE+/O5G5PMne9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198296}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.42": {"name": "postcss", "version": "8.4.42", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "0954e9b075f961fb2790d6b807b1f24e7334dbea", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.42.tgz", "fileCount": 55, "integrity": "sha512-hywKUQB9Ra4dR1mGhldy5Aj1X3MWDSIA1cEi+Uy0CjheLvP6Ual5RlwMCh8i/X121yEDLDIKBsrCQ8ba3FDMfQ==", "signatures": [{"sig": "MEUCIQDz4p1V5SaBAjjFktV+VNuUuACmVaxaEBh6iUIbpCSp+AIgJpCpl1e7OLxrGmtjKin0n5t/ESA9wBIPPEtkZh42AQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200123}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.43": {"name": "postcss", "version": "8.4.43", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "a5ddf22f4cc38e64c6ae030182b43e539d316419", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.43.tgz", "fileCount": 55, "integrity": "sha512-gJAQVYbh5R3gYm33FijzCZj7CHyQ3hWMgJMprLUlIYqCwTeZhBQ19wp0e9mA25BUbEvY5+EXuuaAjqQsrBxQBQ==", "signatures": [{"sig": "MEQCIFvLpJYa5uNTeEgozud/HiSOv4JXT+aX4xW3tSpwfvzYAiBXkf47jAk6AzND4Fr2jhUByfK5C5YqKdXdZ8YGXPiOtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200139}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.44": {"name": "postcss", "version": "8.4.44", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "d56834ef6508610ba224bb22b2457b2169ed0480", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.44.tgz", "fileCount": 55, "integrity": "sha512-<PERSON><PERSON>b9unOEpQ3ezu4Q00DPvvM2ZTUitJdNKeP/+uQgr1IBIqu574IaZoURId7BKtWMREwzKa9OgzPzezWGPWFQw==", "signatures": [{"sig": "MEQCIDBATnQ12l2qJNuaiBcg+CmCJb2LE+vdWhchL6ExgxG/AiBi9X2kzZrTqp8t3n24O3lkdlnZPgE7ZROSfa2u5zZqlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200159}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.45": {"name": "postcss", "version": "8.4.45", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.0.1", "source-map-js": "^1.2.0"}, "dist": {"shasum": "538d13d89a16ef71edbf75d895284ae06b79e603", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.45.tgz", "fileCount": 55, "integrity": "sha512-7KTLTdzdZZYscUc65XmjFiB73vBhBfbPztCYdUNvlaso9PrzjzcmjqBPR0lNGkcVlcO4BjiO5rK/qNz+XAen1Q==", "signatures": [{"sig": "MEYCIQCODF+wBo2/D1zGZEsJdhvraUuja4Qt6J3D2cMVDftgygIhAPZLP0XyaAQkDbhjSG9NQFE25ZtTvt+qipoLKFTcRcm9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200209}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.46": {"name": "postcss", "version": "8.4.46", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.0", "source-map-js": "^1.2.1"}, "dist": {"shasum": "d526f2126b6ace463e9b3c1cbdad040fb040ea22", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.46.tgz", "fileCount": 55, "integrity": "sha512-73x4XLhY0QNN+87/u6F7TRq+yl3xPAjlbRRvhly1mAKJgNO4q5fiqegez/Yi3u+ez8wbBXXqY9N1+RAJAVCzEw==", "signatures": [{"sig": "MEQCIE2c+1UKKYz+1XNM5SO7e8PXOHM08d/aFcmVWsac6+o6AiA1gewa0JH/sNbsm9OvkPVTTbXJlFntf9/RmpkJ+I2EJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200244}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.47": {"name": "postcss", "version": "8.4.47", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.0", "source-map-js": "^1.2.1"}, "dist": {"shasum": "5bf6c9a010f3e724c503bf03ef7947dcb0fea365", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.47.tgz", "fileCount": 55, "integrity": "sha512-56rxCq7G/XfB4EkXq9Egn5GCqugWvDFjafDOThIdMBsI15iqPqR5r15TfSr1YPYeEI19YeaXMCbY6u88Y76GLQ==", "signatures": [{"sig": "MEQCIF0JSh0E7SCS+lnVW2zkfARLtyJ3+u6TDTLOfc7jW8WXAiB3tib8i91Hg9wWIsAEectO085GZO8hrlAoDEV7BUSNMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200241}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.48": {"name": "postcss", "version": "8.4.48", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "765f3f8abaa2a2b065cdddbc57ad4cb5a76e515f", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.48.tgz", "fileCount": 55, "integrity": "sha512-GCRK8F6+Dl7xYniR5a4FYbpBzU8XnZVeowqsQFYdcXuSbChgiks7qybSkbvnaeqv0G0B+dd9/jJgH8kkLDQeEA==", "signatures": [{"sig": "MEUCIAEL2XNfOtyPxj/7A2/nUC52etIJcSwKwgTOTQfPmAOWAiEAzF5vncS5fexP/GFkjjTavDlIP3HB+F8joYk3xc0BhS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 200331}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.4.49": {"name": "postcss", "version": "8.4.49", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "4ea479048ab059ab3ae61d082190fabfd994fe19", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz", "fileCount": 55, "integrity": "sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==", "signatures": [{"sig": "MEUCIQChRq7R6q2lB/h2/2YgyP9c6JsUwlwuIHqkgKiYFT+lgAIgBF2YqjG7aVkHdO+e+WsjIFm2XP6yWqPBg69xsQxgUhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201010}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.5.0": {"name": "postcss", "version": "8.5.0", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "15244b9fd65f809b2819682456f0e7e1e30c145b", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.5.0.tgz", "fileCount": 55, "integrity": "sha512-27VKOqrYfPncKA2NrFOVhP5MGAfHKLYn/Q0mz9cNQyRAKYi3VNHwYU2qKKqPCqgBmeeJ0uAFB56NumXZ5ZReXg==", "signatures": [{"sig": "MEUCIEMdrphQEWml1oqiltR5fVtEIOxMyl949kxRhOks0pZEAiEA4c11yQF01gj+zZAkSheRXZxD8NS/h44xWo6yCdZUk7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201574}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.5.1": {"name": "postcss", "version": "8.5.1", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "e2272a1f8a807fafa413218245630b5db10a3214", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.5.1.tgz", "fileCount": 55, "integrity": "sha512-6oz2beyjc5VMn/KV1pPw8fliQkhBXrVn1Z3TVyqZxU8kZpzEKhBdmCFqI6ZbmGtamQvQGuU1sgPTk8ZrXDD7jQ==", "signatures": [{"sig": "MEUCIEmUUE/t6M/7x+tSEzB4iMLogZbJQmvmI8/G1gzv9LriAiEAg08lGQFTM30K3Oc+utf7anOAmgIF/WwvNWabvXXX9Wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201829}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.5.2": {"name": "postcss", "version": "8.5.2", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "e7b99cb9d2ec3e8dd424002e7c16517cb2b846bd", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.5.2.tgz", "fileCount": 55, "integrity": "sha512-MjOadfU3Ys9KYoX0AdkBlFEF1Vx37uCCeN4ZHnmwm9FfpbsGWMZeBLMmmpY+6Ocqod7mkdZ0DT31OlbsFrLlkA==", "signatures": [{"sig": "MEQCIBHwDhf6nC446Xm7pzRYoEwLRCVCdxMhvGDGZCFXtq+PAiAmkMkQw+/wsJLPGUWajWyTZvwe7h2lwYHdVJZWppmIJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 201946}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "8.5.3": {"name": "postcss", "version": "8.5.3", "dependencies": {"nanoid": "^3.3.8", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "dist": {"shasum": "1463b6f1c7fb16fe258736cba29a2de35237eafb", "tarball": "https://registry.npmjs.org/postcss/-/postcss-8.5.3.tgz", "fileCount": 55, "integrity": "sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==", "signatures": [{"sig": "MEYCIQDMVtnoMLgWadbvDS8A/OLcvAmRjr8Zzjp1H6wIfX3UPwIhAKhfK4jlLwymvvzdufJdagkpwffiWgLfecMJzDyTU3pj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 201969}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/postcss", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}}, "modified": "2025-05-07T01:51:08.156Z", "cachedAt": 1747660587445}