{"name": "@radix-ui/react-avatar", "dist-tags": {"next": "1.1.10-rc.1746560904918", "latest": "1.1.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-avatar", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f8c02b91655050378f275b11ed5002fa72245964", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-BIPxJXV5JvyYOr3Fg6rvL4kCnETPBl94NK+3ads2RJW0gLJRwjfWBNdFGR1BoJNcGAOFUxzey0DEXDx4LavPaQ==", "signatures": [{"sig": "MEYCIQDJaviaInvLCAv6dj4ogHIfkdEq7GRz/xMJ5jxU+47BrAIhALqyGOR/7tZaffWTsORR41BeJDboDzPeLbAA9aHx+3ck", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24941, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbECRA9TVsSAnZWagAA7OcP/3yX2OyCrCDd+DzFilNE\nlS1D2EzJ7+vJIJ7zqmym2N2wX3dQ5TisEvGKx6htnHo9YuKbKMtG2iSzQESh\nL7bRSBUqEbYMw+PzXHKNCsV9tgOmpfnwwU+VGdcsxcfg8RG5y/hYb+UeaS+9\nCvthnAGtiJeaDvUHJyKtI7ueAGCpwSdJgM8XwBw2ksf+6J7J7t0RxrwgSEEz\nwp6RgZxxOIlRBQezdGs39SxZL2HL9VuGr95E9QLtBdvHUsgrE8E9sDfeo6K/\nv23gOiiLePbBDutKF8rWTYAyjo8BcHPomNvVCyds52z17ZU3kIq6aNRQsDi4\neImVqlun7bVaDSRNK86XUtJnLEMyMNTUCMU8x6jx1vT/rIlOBG0XnZxMCVuq\nR0L4yxNxPfY2tSMXsj4L/v3yA1YeT8ehNJkF/SHAv6iwYvGSNY8aw4ExO3jF\n4gfLPMb0obmGy2/C6un4vm8x3GP507xCjMq1WuJGEtt297bwn3vpvRLw+nyA\nPqQLc3RDS1HBcgX9Rz0L7PmMabUma5EfTmLHQusRVhBfpES8V1klohrKlB9u\ntrPusOr1uN8QZdZhzPxz5KdvyBjmwrgeEBoC1UBnm2qaFvaHhhSKsqi+72vq\nQ0NymIl3jljvwoNkqREs1A1oj0bZHv3NqqYUAvHGm5I3iLd8mwA3S5tzaCSa\nAe3+\r\n=oy76\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-avatar", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f26a803e2ba8e02c94805d671315b81cc5c9011", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-gjXKpwJ8Nx+upR0qEs1b4pbN+b2bZkzpIsL+u8BUyr1VO9p1Qd8CW4FPDPk3pbzOdifw0FYNfofa8zsc6VI68g==", "signatures": [{"sig": "MEQCIH/EG56MdEcSLMMfHSIDfsclEbI6Z+4I08rXmmfItN+QAiAyQyzk7qNy89HnyZ2Eg7dNoDVuwpapQ/ofwmpyGDoI0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvBCRA9TVsSAnZWagAAFIEP/izL843bqHhdfBOVo5Uy\nptFpbVGOE5Qn87yXjGmtZkqxMAb933MuG2Oku60F5MdNCMvnDJctZyI5j2++\nIcF99ipsCZbvzAlkAex3MEMcri0pVzBL6pai3oV61rMrnpRn2xzTzCzEVk92\n9tQfWEaBYmhcTlzutY2C3nHjI0XGlRLoeT0nE7JFjkjfdlP5AjG28Jrabrbd\ne0xMh+9GkgZZs3SVVhWwOFVvQ3BqELB8Y+jYH4SQMP18eiBULLkJe9EadVk7\nXUXCTpiuwUkgvcHiBnvZERnVkOM7yuBRfj0uZuSUGdIqB9eRu3mDO+JAIFgX\n3fOyIDipNC15tCFn79EH1/OAxlC+TVEeb7s3mW7wS0PyY7B0cnq4UysMHJ6w\nx2c7T8Jo5rj7yvsXeulmhgdr5RGSfLZndzHvx7ZIlZQnQDCPD0q8KLT4ed76\nDzxZL9C2fjGuPRE5RHXpZXKpRs3jjZQNmMH+G/OKBiOcmoJgfreIhNZkPLmM\n9Xtfrd8SFSNjoj0tQQ+0vkvv7tqscyIqT43NmwsHP2Um6Rg3sUZeTiCVLWli\n7NECZV6Z2qtoHr56VcSXjUbVFiZQyEzZAHHW42x8q0hzELLSBJ0jqs6z4EC7\n96QPOjC5M/BhY0gVjnXHRt1R3CW5Rsr+cEQ/ZxNI0qPdqpMUIf/an9pyElkN\n5VUl\r\n=WhGF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-avatar", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "794ec17fd87fbde2ca292fba1fe8a9759a33917d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-JiYzzSRPd78Pisc4SFklQK3I8crjGxMTc4bEMGXdoUZcMwNbirNbVHmaaBBrcatwerx2sSNAUJGxpjb20u62rQ==", "signatures": [{"sig": "MEQCIFXuZmYjM/dqmS0w48u5sbn6tAQJY4tmM2oTZMcd4TFpAiBDcukqTi430/McxN0PyZY8DOD0WFcrXxvdHzQAXi2R1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27595, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETs/CRA9TVsSAnZWagAA9P4QAIXt8brMofuBKIkBaxN3\nFJe4O5umyndvCJFcLtTZx92cfZCtSM2OOy+MqiJXIrgfYee2YQMfbrZLrCSF\nCRvBJhR7oi0E2K/12CN623qFJeW5CxxesoKHvVe8WJP2xJAru/La6mZk4KMG\nXNy4+XrBj9+oy+xr1DFYFIXGm4AY9nqTgaMLTCxrEvP26Ws19gsHP5vRjvRe\nGoeevZrX6KVIi04xKMqbIqmaiUQUcWLiRXsWlsSdxByaz6gk/OD69uHsRgi<PERSON>\nte1U8MRutiTsff5kpm8HtkD5M1PS6Whggld2b6rX6PbDIkvbB4tuwTLFHVgD\nRBGJH9eGsExSeBA7LyE5FLEA7XQFy16+tEfoQago3RhRHMT2+LCRpgD+OUBe\ndqWJ3P0MpLtHMhzIQtNzZVqUHKHb5R8Wg7fQ/lyc/eP6G2wUJtBAcJMbSA11\nrxr9+BsrpLc1CWfOyqCyZl6Q2fXXHK/e5Qkm98A/dBibf/o0jVGl34pcJ6qm\nBqGfby9Fx1L0xBuYv4ZSblnSJAQGzkdoCxIYCN1o6YNoCZ6wuBOsH3znVjUJ\nTuWEaVm5qVfdDU6933mGbd7cIP/HG5t5szqvDdosnEzbpt5XL8vVhQTYsa6f\nT6vVk03Y/CIIidTBOQg4OrjuYbh8Qlyy6hdzUs3UM+G1r8hTnL9Pk1TA8v+3\nG+rT\r\n=SSv9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-avatar", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d89311b6ed4fe8b880e4ced0b59e72a89782d172", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-2WMBYublbjhcIJG//MSrIQonTbgZnZcMOCvteXYQ+YKFO/04CijxqlUYrFDFPknVlb7Vw2MeOsbzTRF8arkiwg==", "signatures": [{"sig": "MEQCIBfWWQbw801w4tPBHeYP9ImYNcj57KZuspRJCDpjlrktAiBTImpakOdpGuje0GtZ/1ac9fYH9hall686HsdO3ccSRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/1CRA9TVsSAnZWagAA108P/ilpH+BiLeF2cxTXDZ4Y\nsagZaZMi2YtkrfSF90qB+c3x64Jb7eDgr9pz9F4p+vNqzXrW/uwtLAd5f9td\n6XI5e2shZlsiQ7XBvZD2qywn9IxBp9iusutPghPVrdNMzLDYmzCwOfNwjikb\nfzEP8z+DzHyflqwICwUURRPYAZ0TtxlniRj3syPzopdSa/Xm7jBRoWo4eHvV\n7Q16rrFqSw5T5hy29Ul99ldyg8E+ofVwmpDeF7KBX8JE5CzyoECiPvqS4Xwk\ns2XwGSAXeI6Mh5iLSY2VpK6hKALqu4T/a5s3TBGoyhKSxl9pcWxcFN2rp8Pp\nup6hjADk0mh7tYwpu0S5JVNBHXrRGTcQIAyG2YC4uByP1OozWfqtjzDAE4yJ\njTe8y6w+aFvPxcRWoFfjXXJapGxUkKZFvRriZbUF8x3hlfJO58C5MmrjO4y3\nsGGE9kmwBSMWO94OSFAYQasLbVdFTo8bLYFkQK+C5Swx1Akyp1cvGTBd4lRI\nho6zc0L2CdOaF/0ag5cvyeZZDBjogw1vsUWWEgA7YtBCZI8Hf5yHUEXiB9t6\nr0eVfLJxZqMTXV9iPuuV3v56ny8U3egSq2lJ3E0Qg0FTCb4s+7wSqEM5Vc/Q\nuqVz0lYPD6h7U5q1JNomwAaSYxiH9jJZZv2rInqvS7lGnWEJHF4B/6Ica2Kn\nUEpa\r\n=Zinu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-avatar", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f298027874b293ef2b18cddc7a218825e13b3574", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-t3nt/YYi2q/u2qZe4hpmVEjNBhVJSp2V0TmCT5iIFiyCNZOZUdEFDRBOqGXvNF/xjj+1v55H5hZBdn5Of/32Aw==", "signatures": [{"sig": "MEYCIQCZoWZ17CsBQFW29ZvmKUgkIkXKwiZ5uRckUUjW2ZZe/AIhAObJ3L4Qpqsy/CLVCgz9D581X+6rf3fUYQnAE+CwOClV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VoCRA9TVsSAnZWagAACP8P/31wx1ITyhasWyqg+YpP\nU5CypekWglldF5agXdTH3TSqQw17L6+Y9PLJTpgfBVtlJq7wkG824gtb2sz2\nKjWZte6MTvfEYJkvx2wrOdzo3fBlyg3Rpa55bkuTuouOAllVlCqdX/7Tgbv8\nPjYkaOjgoR4Kq3mZdTgggYsrKdP4sdoBxKmxDqEvGlNiKrzYnX68ncTpxIJ1\nsfT8fYJdu6d+bQJHY5J112Doik71SqUXLx5TmJPf/SjsV9zhXq4ttfq+yrCc\njyQDjY2j3UQl8lHgsSSrgvHwB5wocH7TvIHNN0RureS/n7/tUjeBiRu3MlmN\nc2NWkYCMmqT4mZ0yazrIwpn8Mq2A5vhJU/SgN5BLNt8nOQvwbhGYHlfIWGXj\nMNh+Q1Yt80fYc4tVEL8cgGZI1pgAcxlXgrhedb/OKB2bu9xc8qObX+4CRFDz\nB65m1q58mfuhLeUM99CxO8hz3Z+rt+eZjJgv4oiwb+OIEK4XTrVyudTP1H6D\nFBJ0NuHVHeWejjB1BuwpTLMuhBpCzHnXjXAgVYH3vZm8dUF7GWEGZxHjKmH2\nR1T+S4oIA3XWSZIydtQMRoeVMu7CJ5IP31KnfhODB+4a3gAJ8wRbiRNpUNbu\n3CxEmSyyggaeWFIAdPIh68QkFnS1/LT22pm9G3Vh4KHmd7z9AqVcLkiUsG/C\nzkjo\r\n=O4cU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-avatar", "version": "0.0.6", "dependencies": {"@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-use-callback-ref": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17ccc456ae10db815e93e8634204a17b8faceb34", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-lcwmRGhp8GR14VtGmv+qTsN4NbfnWpqVsOocNbvk3ykUj08W9XkvJTaIdy7RqmGvOFIBeImjcPds0k0VnaYvvA==", "signatures": [{"sig": "MEQCIDvMJO5124S74CNn6joN22XaGyRBapbjxMUBYTzgtKXTAiAxbJncbFh7v8I0q6E9JKapxUu+rRbCYFPWgPI96JHWow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VBCRA9TVsSAnZWagAAyQ4P/ikeXpvHaFgz/Sp7K50l\nvcyPllrxPEog93WX+dzpQWUt2P5G92Y+upyJgA1GkzceiyzM9TBg9dFt6ZXJ\n4umDyaYW5CqCR4NgbS/48fmvBSquSZLPMOthdgiTnPg2n3ici9Tjjo97gJWp\nd6w/L8KEKtCI1BpGhqywfC0xT6Pj7XLpnoqXbSWM1zz4pIeSlpx1bJL93qQe\nzEguCE78rG0Kia00JA+E0oZCrJmyYxTzGJKcnzbpJCofCM80CVCxMgQcfa4V\nhC4IFiKQzInmNxJ2it9SNJAyTRfRcuvVDXVIpKDN3sspTEa7K9TYoFkoMnDn\ns1pBDlDSkGPuy0Isw/Tny06n8XgwynyJbvce1pC15UbobQm7Y92VUuNDjomv\nbR/4Igg6B2yM2dMB62Vi774KLHP+xMQ6lkh0enb1pS6IqkkW5MccTpruQI8h\nUDp6j3BSXPCaLP0VP5n8H/Ddd6R4XZqW3V23gbgD6x8ZscLoJAKvkHnfqMg5\nGBvEhi9voZ3zmsBh4jjIL8A9dqSZzURsMhsHWSpIVq+cpwhywxyN6yX+jsa7\nZ4TPnVchh6OB+KpjUGYTwbBd8PcmyN7+bB4vAVUSj6dM9p2Md86G57Gz1aVu\nT0b6Gknxa2vcielPzXw9S7nC2uq5mPOHjdTERPpNOhwAgnY0AEkjIxCfums5\nS4ov\r\n=M/1i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-avatar", "version": "0.0.7", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "53795905495bca1f88a55d399f02aa6f7e068b93", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-V0Gs97fUWMedAhfwh3WROJs06kMo7ievfIePL7DWORIbeZ+vYZ8Pxek9/KttZ6rs3/3Ds5DRQj3GPvblCuaxoA==", "signatures": [{"sig": "MEYCIQD2HsIVj/yNvlTbOBN9PC1gZbtMFQi0HhWLZ8n3oZqwuwIhAJoJxAoZkwrgGDBBFDWGGpY2BoVg3yuT5H/kTzGsaZxb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOpCRA9TVsSAnZWagAAX0kP/RSunZZAiMNklZhBS7KW\naVuKxA19vvxl8mCVCjBZ6cLo8AWMcoeCN7guLhekoA2vY6dngINKL/v/rjfn\ngm9bVX/xEofQAOOpzjCHPUBNDyYSb30qrHE9CSwt44iEF0WliXMbUGDOuIi0\nXz47OzIYhY43cbMQKG/1SI4hKUtHIKwnnGVfidwtvmjM3sMVS8VVv8vc5uTe\nAsMP4MutsR1eQrTB775elVvS0DkyECDJfyHZj7a2kFLjky0FePG4o2twdVby\n2GFg17c+rXrr5JUPkRZQw+fy7kPCx/6uzj74jvlmyS97B1LlHjxk0VOqEASc\nAdw9Am1dsA2yLzcykeIFMH2h9/LCE62KdL7hU9GHrkLz2ZdeVoTef+ARelZN\ne86YXLOY61mimAtUkx04WUFMK4C8W6nlC31ntQ40XGnCmJWT817o8w7fxQKI\nEljSx40GHFJgOA6TA7oKH3RtxsLWnpx4eigLLgS7mue4fS/n7XBKhWo+7yPU\nzoa4xUQdzMlS2rdh+bykEsHPFJtruTeTAuoBqJQMfk9ksLy5S23sWHS8lNVx\nDk9b+E1KkDbolYbiKPWokK0KhwrejvOI8fNGa8xVP/RDTWmI22ZZtCrd7lkg\nr3dMQr3aQYEcbYMZn/aoMDp8/0ORKXKghO5VjKK2SggP4DcASK5jOmRGbZTk\nyMfv\r\n=GdiS\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-avatar", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e62f5880c3ac46528f1564b1efafb58d3579b28a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-cxq484H/U/00/TbtrcEcQ0aoz3J6Tw5uVRNaGghzgu1CuTLvjw0a/Bg0lq94tbSdklRjl2f72K7uLc7/q3lJ+w==", "signatures": [{"sig": "MEYCIQC22qoI7zMB61vLzG9dvSREOZafKbC7UeS/YFjgBbyzewIhANmxm1pKWRKclUYa3AuO7U4yaJw97O+txrDWRq4PtdHm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0geCRA9TVsSAnZWagAABfYP/Rm0Vl/ObTbgii3WZ2SY\nTcK39fXa4SwZotYdYafTp7XmQy4z/a98y5WskzkYfItiw3Il65FUi5ya8CyE\nd7/3cZ2VcBc//+H1Ub3uEaHotqjACKqS930YSoStRegPcNvH/4+iJ6f9LMju\nIahZqVqiDyEcO2Q2ZWgL+KiWhj92+Wnb8saRTDmvJSlZ+bBtJloxF5UE1TBN\nvFs1TDaoLwZO8LdB8r/00XHEd3xOaPc5gFh5CJQiBqBe+6yCMtldJ2kqnhlf\n/PZ26uPkj95C<PERSON>C9uJNQ7H/gTL93xH3/xkMQbgTGNX5BX2ktLbPdVmtIBRnY\n5PoHH8s8t4s87VRCagBXZHOj4CX2fzc6S+umMChCfYpI3lbMGAzrJVYXssBs\nkNyklaraPpAhfdasO4DPYZw2x4/4cdlSO1wBr0Q0aQob84DhstiYcMTFJkhn\n8ufaODApGs3OWDzkCfPkoI6wgGnvhlLLOxabTyMdOWjNRHbExiDigi4LUhqD\nHf2Jp7A3DT/0vVSLEVt5mUHM4CNCpaZMPJm7lD+MKf/3SiVTu9+x+LlEKJZ1\nUFQoluqEQ6E6Id6kdVOQ6vOsGOFyq9JCxUIgPMqIsZ8oK4WGr2svoNFt6pys\nGbwcYopcectl1yan1Fgdll1J7TQHmR0C2dMYE9HfkiaZazY9qNi17VTSSuvm\ntU8i\r\n=LaAZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-avatar", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-use-callback-ref": "0.0.3", "@radix-ui/react-use-layout-effect": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0547a8828bfdc25e9a33fccb85b409233e23c2c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-axGgmupK6Q7c53SCfKg6aFWijDo73STXrd+uMmAj93D9ckv5BN0iDVT4wEuccBZZLVsmx1S/aNwhxUU8Zl/aIA==", "signatures": [{"sig": "MEQCIAn4jH50FzBLM3tQnI/3GvdZigITKbr77SbCYxifds4JAiAwHkelYG79l/pnz+tG1n9zDV/B1QK7gUI4EZ93mH2Whg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HoCRA9TVsSAnZWagAAPoUQAIY4LoHTUtktUGgzJ7x8\nn3nZtQfUiNtfbGbXtDVFAFliSUFq9VlDAuEfEEc4zcgSizs1vWQYkX4a+Knt\nRBz3hld2415+fg8IeY8RBH7pxWe16NCRFIJYzaG4AEfS5TnsixiMiFqT/hdS\nrj/zbjeQPAm3AxLrFGzUH993o1yf6lR93pxUwwGFGh+GqAiXNuSovhBDjADX\nh25wZgSnI9sYf2XAuV2XvW5feGQOXbsZks4QpBmHNRn17N6N/F0eSpEUNhmU\nlsuHvg6HnmYYbJZkwS2A6yLR5X8KNXjw3diWeWIXyXYvGLTxhbXRZwB0KBiv\nLhgf0RbyOUwW9pbclOLPSZ0h+hVSJtZzQIMtdbLJ0xdDenPld4Zm9nBBamd7\nvZtvjBspBtwmFZb1kpEutciDs3TOU11Z/nFh1Y6g7bdyHZlZL57o8vgQrHUC\nxidrDaJLdqWrBmUiPJW5Ta4+wg9Fd9M/+FN50TLW4B3vbH8K1l6qyE71T29f\n1BCh1A4yMj/VJMUkA3L8tgGB+bAEA97aAovGO5mjZTwj4fv23CDMEZc4MYsY\novST1lPuzJTSTRraTb/ebSdROH9XPFZjeMm0jGAtk6RJM6PeyGIl1jsDgQj6\n7/bZac2uz7p1I4M+vTxplg671+USR73ik4v7phompz5m6Lqg/PyacnsWNRiA\n76yQ\r\n=ii2f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-avatar", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-use-callback-ref": "0.0.4", "@radix-ui/react-use-layout-effect": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c59810219bea31b2fdee3d06dbe13e9297eaab71", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-byVjKFSO942RDhlJIs/B8vIy7ta4AyoX9znf6qTxPmQmlL+7YlgMxlxXQdZuognoYblB0eoW7C9Y+nXJqoG6Pg==", "signatures": [{"sig": "MEQCIA1M7XGZKMihPBXFRP8GBLSh43IuhEXvS5pnmcuWkOekAiB6No76/fFh8lrFIvpdtA1zv6RGhCbeUBCoBsiqVESIdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vmCRA9TVsSAnZWagAAStsP/3IRsD/ytPt10WWMzQaq\n4jEREK69cwMPXv2/FC9FuLc62BqVeAh/bKG/Pxox2XHyGV0xZqsOmm4mY6+u\nW9uZzGrPXaJEDFs9MCvocMCYKZhjsHRoYiX1vrJUj0UqDby0FqO26nplloEF\nlfh8ph5DuCcDH4zWFLkvRY0Wj8zqrAf0nSVH/FdvoiQaDngUknQIZQih4H4V\nfPb1YYLyzgD8rc/TuSeiHAK4g85GoawAWvNq3o+vE72XnxLLTqQUQHkqEcTl\nDFFEmTqNJttR2WjPZeZs802I3/xabnVm8m0jHwjRS45kq0dFQRQ06aNDKpdM\nFyzm3lvhcAS1CMYK1W4A3vG5wjCt8SAaVzrdM+hfCprWqkJrM0tPwNJrPQTx\nOTOjjhuvcgGAS6YVo5ANFztkfVBkliUEidaTxcc8Z+bUtTPXDZ0FAPgrY+1V\nTbYrWqzVZLh7919THqGG+7f1orqOQVh7BsVXi8P8QleYulEF4TM9sQ9M2GrD\nQgndwNlchzwSYqnk3Q9arV8j+DUWBDuXT5LF3zEHli7vD8bo7m3I5xopdiKk\nFnRtMWF/i0Eum/ULJzrTwidWYk+y3so3UMfkekGbe2WyC7jOJr4eCljixiZV\nOXWrnv+r4Fj1YpAutUDJKPusMk3ddsVIRNI6HIeXBQZ95JDjuucFe5B3qcC3\nECtR\r\n=3b8I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-avatar", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f1bd805057d191f91cb1551189d7dcc74c4e252e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-ZBMgm+ftwq/SxQQb9afoAgC3eT9TTXcYqExQG8IRpSBM9rjkq1PQflV+sq/7hV3nDOb5pApsGadSi6tLXe6c3A==", "signatures": [{"sig": "MEYCIQCCJHWJ0vOmjbAlMtTzQOxRnuSksY7neFAn9e6ys29sWgIhALRUc9T7b8+xPc1pQumMPuslD2YD1W6xNY9iF1XORvZx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmaCRA9TVsSAnZWagAAjgYQAKL8kupT720EUZqbf2cX\npFl8VbvNw8/vO920UfhXbbRnFOxY7JVCjf6QUNQ6wShfIUPGcf4+xQ9h34L0\nZNBj74mCVRPakgOEbpVnZXdncQiUNV1TQlDYVyqrn31km6X05MsvNvZXtSYm\nWCxC7aJhvtPUEby3OP4efOSgs183VVSV8vTBeAVVvHwbB6c7e1wF/I7wp8Y+\n5wVR1QZEqXQxqboPdtFQe5o2idmCtO/hIoEmYbiRZ+0eiAJurso9QuuDYPn7\nvDcxY5nsfs0wBnHflunng1RdNUX6WG1voLG55Pcjv8ndMIgZPrU99QzvRLPX\nCCB5HAjNy6RFKTxxnlzfJoNQVvkUkH+6emwqXbz+BQBQjq/vzmjxLbrD0DaM\n5ihnIXwmNSADbWwQG+Qn7e3ON9/46h5tHY17tEWUQqoMEXzFtXRyZGZ1dTJp\nyPHtQHL0Atl+Xl/Rsnq3ceJcG8oN0KaEofbvchyZ5Qiz0seYpO1YCx//YQyi\noJLsSenCofmIsbIjreImzalOJcnYv4BDfj+YgXSNXt73jlsBEcdUfeMqh03P\n45dmxY/nP7HzuJg9VfNWQHihm/xKEhHCN8tWAAX0nxBgJI7NTVqsmUTDwjqL\nWUw0aM/5sJ+3ofk+uMYXUAwF6Dn5cf81SBAOt4HDJqSJR+PY0vo0yuRoL21I\nM8rl\r\n=lTur\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-avatar", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4b0c3d2fec6271edd3cd4240f3ced5a181e004bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-dqogUzZ2gBrUFS4ICg/5QGQXUMV2VwCqjNv1TiA+1xwgg/n8beoZbuN/ZzmGCXA+PRiSrxegmTcH8axRRUSx4g==", "signatures": [{"sig": "MEUCIAcFKutoaWgo33lHjrupUC6R2f9ldb9LvJsi6Lx9/7XqAiEAz+3e2Den0m3GCLev1bOnVTsT9B7IIdKtdJ3MHSQfoX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7HCRA9TVsSAnZWagAA9IwP/14MD+iGrZyMqSE4gNEY\nV/lrlUk1z3efu5m/Xd5r5ayimmv9Bmec3GyckKnwDuon/TaIfTKpq2KHHpjJ\n7h/s1HAx4Ck2qXO+C14C4nFVMdd2ClUtIlpmBCq/sPpUnNwauhbWGgaZvenm\nMUQRWpUxAFCayeJB1A6rUyHouaxM45JGQApiYFLnaRIyNGj/zmqoELlrtTHt\n2jmWG9nXwqQIaLVm20JPIp/+tdzwjS5WalwzJ+D4mqCgfhQ72b5WJXaMQqRh\n+Yne4CIJxYH+OhxbEm2QLI9sIAebqTSpot4GzD3DEVZYT2vA4nfJrC8QFumc\nDWL73anYC+9xTltnqsEuzpxY7QkUP1xCaKRNZxKrTbdSHPGZDy20bT9l8/t8\n/OfRUu7TsBVOElz0nUdAQMka/YmmX5zjoYdqpjpDfzWXjPsW8j9GYz9QcPFX\nrJ4nSJ9DXiYeqps8pB1r9KsKs6w3DVBmLKFOZuEXA/wbXnecvNFbcfA9WcrT\nzp3Vc1ME8x47Ngp5gGInKjghw/TrpFkhC6YQExjKLrvPgNgs75auJfXR5Zkh\nujfBH3gJct6xR3n5AFwCkbnjE3eIE/zij0XrEJM7E1trt8kRd/CbPhfXrMW9\nzRCNyZ3bXrOESfUkuj6Y5t+GjfR3byTqiJy5wrQdIoF3UAxXMYet3/dfPb5x\nNfUs\r\n=zE0p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-avatar", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9133e25573440320034343091fcb8e5a0ad866bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-Bj06mjMspDOla8HojtkGMMf7XbIRB12XvLl2bwyxhkaIbf7w1qgS31cOVQM8owQvlPiK/Y8MGwrzM8EGE9WJTA==", "signatures": [{"sig": "MEQCIExMlnmeIY7zyB28S2Srd4srndDOIxfGWgET6T1jC9I7AiA2+YwmeyrZJH33mEFjP2K0dQ4jMlpwZ8o/lIsAQhraqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlXyCRA9TVsSAnZWagAA4PsQAJLplILpumBBxlKpIdTq\noV3ItMhhgCzwZURSGH055dpDlMwbNcHwC2wyypetqCJQnQjJpychUAOnlm7J\nyIc9C+3VqcicorumzVYvCo5pTlajpO0aC5aalgMwQNOQYDaT4Z9eZHyxc7kt\ncUG0HGzXvVOVPeCGxnOT0SD7MgKU2iF6fIRSJrrsX6siawjqR0b63w6xPJOT\n14M5VYXLO80qN7MXx6WWb0ujvYReBeL1IcRZ8O7urqB0w73chhquqSHJ6GpB\n+G87nK0bTaYh6lBcH3tnbiSScFAroA5JmlKuKmdCD1hR6ej+MZJnedot9wbm\nVCFDA2jMcvWtJhsTo8cg3GORvinY+eFW6s5v8ZfAl5QR/AqzRyQlydIgdTIA\nLMvHIrz45z1vOiHdEFHUb0Cfg24gbgletHBom2A84HGSXVgjzjQlNyhMyOQb\n2zG97H/xcs44nxvwk33MgGrwIvVRQBcH6BsNediA7wlzaxxGYIQat/MN39Co\nlU67HCLshWHJEmnN8B8Yi91QPU/0HFvjgIs0iuIy2bGuUCcWSo2+6Ea36ZLc\nePkGzYqPt8AFKKnkgBDju1/T2Ig1h7L/b37qgSkZ5CwUkhzO+eywDQbBRAvr\nMnhZT7n8nECXDXqX9vb/6uxzr/OlgWSYUVDfY3HV41vgTsG1ZRsyRqYeThES\nl0od\r\n=6w9I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-avatar", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "45d1ee9b938452ffc5e261c7002a0df001655eb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-nm92ZlG51XdcV3WmYy4oXlaPMXs+VG+GFvPFfEmWw5uiUrbljcxAkpud10kBSXH94n5RcYPUZt2lfmTY+tKN9A==", "signatures": [{"sig": "MEUCIQCowYCHSIfTlmEdAGw8DBqn196g45p+MQbIi6i7UBLGLQIgRbSVHnCSAi8Gb4TveNVBANKZ0JOk1yxD/RHa3/zVzx0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9iCRA9TVsSAnZWagAAqq8P/jhWwai0bH1bki8rVsqm\nd6Iv9ZBE0cGCUEiZcpoEo7Jq1zq6cKYC1u4hlvEqgMPH/tQAcqnl2nIFkWvv\nxfr88us4nUPIaNYItx25pwhRSkn/n959e4i9YttAUkOWQJa5+8JDKt7aDT+l\nKoC3cFC0375rmd1G77SNdxSz+iWMMUmrL/zV2B3YDtjwsfFIarAYTAYloLcD\n44mEOKNsxQcXGcmy6vcJC4Xy2QVduzZEjzWcxe0HHL8OwqilmuxqQjxmXr/X\nXyCZZ4Drp/JdWl4oxHsTmr9OnnhDH9pzRHi5WkS6MSt1jDkk71TJA/1tMi6R\n1dqqK5EcOeNsrp0WhEqYmWmkPZQ3BI1PyaaXRSCnNRMm+jqfXStgBzm8lp2Q\n3vAwq9jWoGTTa7dhCC0Z9B79yON+aImT7ZIXHySi7X8hmesd6C6Li1SxZOsX\nYzKZFX15xx0LQWo8qg+LpB+VEyHMESEM6XQpUF1uKj6/Z9n8fH/PpKPrCF+f\n82Fygkq4KZtAzbsllF/ytfQdlnwHDtr9+L3RL9AlKo0l9R+qRNSyXwHrZCcW\nx+BdJIkyzjeElJe14ddGjv7XYSp8tTmDH7YptKON/EAdSAM/Umh4oM9Jr3pg\ngXUQh0eoD63ZbtEw2cupeG5TtvNwPXo/TS6vLZLFuyNs/7dLQdG4U3dwg8gR\nqTLl\r\n=NmQx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-avatar", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5cb9280bde8cc1bca27a816c4a0b631222c9308b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-mZHHCMU7CODOpisqiimrvVCUtcFcQLtuaqQUBwR9dYXoMxjBN1XtQXUGdTvFpPFXd11C9b6i6Gjfvti6yD8Jcg==", "signatures": [{"sig": "MEUCIQCU+aOO13BnLwBj7XTUtupj0s62kcHFndwi5j3QtwwHyQIgMd6E1lornvGtX7wDNlm9eCFxER8hrVGL3fNZlYvPAaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTkCRA9TVsSAnZWagAASh4P/RQPbGfJpm/cFE1yQB9i\nFZTaKeec+YvLdImGAqJcYunxEFaDvPjq8nQnTLVC9ODDfE/r/1Za56aD7aQ7\nNU9ZsRVt5b/fOvpRv3undb+6Q30ULQJU31wnXtFUVv7Mo3jSjbHyZLoENjo0\nYdSFi6o9kpPDfebqb/iJL2Aq2RPgxKagHSyhy8dA6MbqKeK2qCnCfg6mM0ZL\nzl/JqVikT3h+OPCfU/zj11FpR0QWjkXsHSZoQVJUIMh0ToSLrvxcizxiJPE3\n4ztWxF61sGTmX43Lqb1ZRE3dC1hVDDQSkhMfkvmOs/NkGQ7ihxneumZsMIY5\npBYPY6ekvVEyjBVGwYGXtSC/GqM+VMRpqQ/rkR2TzsUghMGZZ+io1yRyCrym\nFQggERq75h7oiqMvTZbHiCZPTW+TvwQ5gVJww/l64kFFPpQRwL8p9cbttyuZ\nQIY9bWVeErY8IT5Be941paC4fFRIZD4H9d2cHPWNFrE3Iko53O5rRLf6J65l\nKv1KdHGmWeH7fnUPuYGGO00s22HQvMIc660lDzGecLByOeeOhqna1j2RSsBx\nDFuwJB+Wck5A/ZKY9kVeUfECvMws5c1fjyJRYuCOso5EYN4p1MomQtCYkIZy\nq2cdnYksoWgZ0obPu60IgmALAZ7AIHZ+hiCLlzvKkUug+5InUJWXbEOa89WH\n3AA1\r\n=Yfxw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d93ee4e98be8970484d674abbdf2102890d82ac2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-NQr2+yfZI6ZgSMsv+Rs4xlibz01rdF4sqw+16EGgCNRWjQfyrUuIEZq4/cMczyovIN5Iu3M7h5FFdmQmV50Vmg==", "signatures": [{"sig": "MEQCIBOXIaD/VdgLpQAy29qP8O0trEvlgISZKamsZOBiTsFTAiB+MD2v7a3dLt2htlNLU4e4FSvt9qPJb2nkkNte3wklGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpSCRA9TVsSAnZWagAAirsP/igKG3xFp5cBX1fijNCX\nfJRhRAV8kHBABOb0B3rJt8Ar2g0AhdRbQfcLLfSurPqSa+PzD9UixDPoDkay\n/RG1YJX97Par81b+oZ7haFXr0Pf6nTdEUdk4CiRbKFI33oZ+REficAdoUHGW\nqnbMXOKwwqS78PddK1uCdLPCrwOti8yiZhXcupVXb89QFdNxtaCcROaNYt5e\nSDJXqg9pST4c6uEbKtBgD+yH2FEjZhN2XVZF5NiFGKsBFUoAjElwEPLCzH16\nDqrWikN5ec2Ia+Vzm53Do2zq5ClLa7TiRA8nRNoZuAIBzcOStizfTcZTXVVv\n2WiowFV/j/P++dbXX//BOmN3NWMIocaYBseXLCVD0DCvYy3VbRe7wd5jItSh\n+1raal/TtemqOyYnQU6AjtX0SV3DilR1klP3N3VDC/FI+jIwUNHzF+qU0tdM\nz5K0id+itqvs/saQsPts5x7Aevznli2TqhOu5+opaurCzXcPsb12pH6/YBVk\nH9W9tiREpoHv//eGgXPpDOgVozgy//2I49L1uoCe6g3N1Z6BIQBAOkE7qSY8\nBH5y2SX1TLuYsaQl1k1CU+3LB+IV8Q7OqSBuad8Emcb7nfrspvQ1s+uzfCFT\nvcFgMIVQ5m/SiDvVDloOmXS/WwsWil9SEz6BrmOWD8y6eu+SAXwIfzBn8Nyc\n66PU\r\n=TXVv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-avatar", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30ad395cc0af21f4228c194f71ef826294641b19", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-exISy5SuEPxfOmN4FMW2iokTJcyYmZp1cQ9rOFFLpzB7IIRsajA82pinerjP6Hoy+aP1hoOkNjxMb0JeSTxOrQ==", "signatures": [{"sig": "MEYCIQCcmJf/5NoSau40EknkGIxSKMmi1r8jNFs3OnCLjTpUBwIhAKUOF9tXpnHnEZ01lLnFpRlAgSJzZk0sgUcsinvpZUOY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyJCRA9TVsSAnZWagAAOUUP/2lIofFSAMA6FBEh2bsZ\nl9EhWEtnhNxwNumZiIgH4eiQQor405EpMR7LqqlSmT0glryTdys1/eZlgw/H\nEeIWp8BV8u3IJ2TX66ceVlnCUozO6VCjeItpXRFbdsqM3qq6VUl4kWcH7Std\nUKtsPRptCI65pVeDgiA2OZpfMyptvxbkuq7R1sJ/7n7SobcLTY5nfH+kNaWT\n28idmAvIyyUUCWjax7qRdeNqfgvddAnboucgjNPuL+TGX6TwklzmtZ08X1PW\npT98xs20Ub7NP9OjuesjN1O0HnOdSAP+ZkUdGzoaDeYkyuZMXq7gfataq3sJ\n19g+YEW/kAq7HeSdjvh8RTUrFEUY8dV472o43B1ZUlyHj8IcUtE2ubYxIbhv\nmlAJEQLmtqmV9lYmSa1Tj96PcU4xXZ67AlYZyp+d/gh05vSgNhI8V+VaCFOW\nI/sHE6nDqgvHhSvOnAnIFAB/+Yzl6ovEeUx/hZogt/b4YDy2DOj4iV1FZv3V\nB7A1t6XU4s2bwnWrZ1YjvZ4vJWbHQiekX1MSEja34ReiIf8RMEyw/eVOQkn6\n6gR3EvGyv815UOuQ8qOYKdzrF/0TRokujQcGuQGe8Qs93Of7kBh62ZqIjEhi\n9DzSxRHaEfaDPhi97SIb+gGomtYIcOYhDfxI8Y4vl7OtCqLn0HBMzfAMze/o\nhMtb\r\n=FL/W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-avatar", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4c467396170bb46a9b15676901155f28b7ee824e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-wJtWsLvaTRISFyLIVTtp1yNgIxSCLV5IuTwmLf9IR6wQ3wbf3WxmIjaMvkZ4cGhPw/LnNYyk1OejhdOnQibNEw==", "signatures": [{"sig": "MEQCIGVcTAa5RDSB089xLl4R8yugMqF4ldSd9L6XzBqVm2WqAiAS1ekA2TsU5V17LvBX0pBvyU4WcV3OtCMmtwp4twYzwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmWCRA9TVsSAnZWagAAVxsP/A3N9Y2GMr+/FCzBOVSv\nAWIc1yoIpwMauAdq1XA7JQo7GxB/UGGGiPs+KQVczuWSqNDw8RaP96Ov2N2F\nXckxE1Bf2L5AfFxxvqaWOBPAU4hDkYvZSJZFCXW06qj8E13c0kHsl8wRGZ56\nk+KYYqXMoJtsQyDjIxoMxZx/AH+XMtO8jQqvdR9Impy2DorJ6K9h3EgzYX36\nHOBBlr3wAMkckBgeCStLsvosRg2dSUMME3UnAY9wluR58Jm7NDfVh6uVuJF3\n0YwNt/YnXr7wPgyesJ0VZKuDZHsecjo5MBFTUWCxa0vCJvVT26k4C8llHjYF\nT6fgCym+20M6XQeujTZM/StuJvDN4JnZNYiafutMOgPWhAfP6pJDC/AacFcg\ngRhTfGeyewHOuW+1JcfrFoD/Xrh8ivmHe8AlDVhF06b62dj2W1qUmo6GJLFP\n5am+/pk7xu0AgDQHIE5rTxuTowiWACQv9J1m0PqvNVKcSkV8uQSft7LwQnxX\nEQSBmEHP9J2sbO5DXO3NL9wdg1j+zQYDp1yoSjq8gOkIPUxZH07CtpIjD8bM\naV0s+U5wCdLIPsc1nit9WbvAqZGo4WksHSgwan91Tu3u7N2hYHSgPxxWk1Wl\nsapCH1zOWNjfWy0RM3TVNg7/3sfUsoInZIJpYD8R0v9poJ8X1sp4pSJuHgAz\nV0ut\r\n=pNQ2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "811b2f5a2781108ca3b22fae32d45d05c1455db8", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8bal/Cd4kBeHwM85Q3y2AYFO0lTnIjsDPHAk1tSa3VQRF/QSIvFLm1JwI7cbefxawUQnn5hvIDysVhK4Se7X4A==", "signatures": [{"sig": "MEQCIAUMn0RKB3j2VufPAPjKEZUNBKLysWHKoMSTofPlWduDAiB/tXlRqEyizodvKfDSWsy/3rmmw1lR7sl76zp/CJWjnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImPCRA9TVsSAnZWagAAbKoP/jpLvbl4lSOs7csLqmVb\nOysXVQkOM2uPnugXajQNi0UVAPLpIeXY4MnKi0cNMITNRffTkugFvA7xEm/H\nFag+r9c5ASaKs9e+aXAv9Od5Q49OywXEqQ0YVOvu83xrRPJ32A7Z8a9AcFg/\nERdNTjTD7njmZPVTVi6KSIXoSwOiopw8GPoW8IbuLbcf3OBGSEBP/OotDJsm\nfsnfsd87z1axvW8f0pT4+ZkdERoFdtfp0FFEnSKKCntC/vtqS+ELV57n/SRE\nX5MO/FGmFdd45m5fp7n+JEHffHDChqLQ74/54brYvIkp4vu4DlfyjyUbjH/o\nHHhX+F1v6J0EICh7QhBhPB4D38i8m4uG6Yol2b2yvvc09InpebOjnwkCEUax\nFThNvnziHya3mmQtnKvwrp4mVWhBOlNeZ/22qH021AVTff0SH92swDQ9sxKu\nMreQOHAt1LqcuTy1cRGlcebkEiQZ4FtWzM5p0iLmDt6S7VYH8n3uEolifpFW\n1Q1XsaNWv/xg6vSiu/ljvnjuVapg6uK29yMQsX4ht3iJcndBuad8ADMhA+2W\nRg4pv9OubYX1U45PtVedByEE9AaXwc9uY0Ex/8hj6V9kOWp96TOh5FsiGSp5\nwf41x0KbDeTfFeIeFgNCm9u8BBEs0xHCYv2O9HX4aXwZ2bNqdQgnbHzQAfY2\nUsV2\r\n=KiiD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a482bc04bd99f8ed2cda45d16abdcf694a15caad", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-7EDzV56ClKaq/bSNJTI7jBEy7JwyFN9xvBt6b4x/tdkpYFqPpc+CA8VTEK8dkb+WsZ833uQP9wUq5qv9EpfIBQ==", "signatures": [{"sig": "MEUCIQCpY29UVkkzPAn1HBL6gPaaYDPn/3zH8x4lVOqVkr/J/QIgNxtW5Dvj8r2KDepsVzNsG8o+SU4xrFJ+1kJ8ricyyIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdvACRA9TVsSAnZWagAAnzIQAJcR8i+FJBVwxl43Zf9n\nHy9GZTFfvJXm820+/CSxbbLBOvSr/RygM5vZdQlO7ucP+xEDyE/qJEI9/tTy\ng1Jx9fxhbQolj7Cbcei8QIiGSxO64tuFkEzINg8WXI8V8c8J3b40KXHEZq0W\nk3VxIpfDX8noYpwg+QJWczv0On7HeyaN/V3K80JZyMcYmcYuE5rlWn2JvV3R\n2W2OeKYHcR4NCYR6Y2CGs/2nPTPPOmMA4desj+bpjF33KGB8zL284bCIuUON\nD9j7fdd5GzEIPAomrbvlQx4nLYy3P79Nbi4ihe798lkBrTAtePf8njCrcpF0\nv/8/AA+MMaH+ErI88MbXbWcCIDR1VDdDyTOtxRpUGJHzoTW54YooPmPPmzAp\ngJYWqdb371G/cfc7QvHsatNdGsyCn2KBGnHja6Aeeeurnlwpn4HJ78PaMg4h\nWylS9rsBMY39+/szuHGfDQFn6GePCn72WtEgPvtP7bTI4M9OsOlFRLV4EesW\nuMyK1GZWMXcdYYLo/v6BRGp57nrfoyAipzeekhDFONS6dGZk25rpSLgrEn4d\nMmWkQ2o6wLHciPrc2aCwEp6Ms918cKxnVPvucb6V2tbswEBBXpFKS+D+DUwu\npPbz6ELjWILEy4zsjgofdEabn+w+kw/bMhXCVwc1+F0PuDj46lKlKkZhgjYP\n7ljQ\r\n=F5gX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f9a9f3c88f9ac32c6df4152c956a574e60a85f1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-b/qxSRbMQsI951uaREMAWoAuhpFz/FKvqzoFp8vIaUKOJ8O2TzXPe0MqXrW1xCHDZkmF1HUu9BNWxahDSP94sQ==", "signatures": [{"sig": "MEQCIETrbUyVWwcUrkzJl9ketTPaLeIVUbzrp/JBU1DaRueEAiAC4f97yohkd5PsrrtNth10lA7YqA9Wo3diUpRKbCprSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TJCRA9TVsSAnZWagAAfhoQAID9PfytrxzhPjkEthRb\nxZjFpag2vWybKv8KWnA3v2WpLtv4EzzTJ9ukZSwlYnf5XWOH+GYP7e8c5z1T\n2hBd4o2D+OrgeTgyw7JbhoVfcd/WqFUP4+plBXQnVYfaMtAPXZhCzwBK/hGu\nZQ0w52kPtYn44NLXGEi1qji8y8oIpJfWVZwlXKp4TwEJsmDfwO/PrW4Ze7hy\nCoauOuUtgblQn7Vs+4ipysOmz2rFakjuo1rdEea/E1AqTbrv7YwH9+ElmJhI\n2ry4XY0tRieZyioSm5hS/8dTdGUeJ2Rbxdblt6Ra37d0tMYm2ebvnlOp9LQB\nmwRNXwUktL70Oxv58sOcaGamzesKhBVRCuWNC8+lT5LkqvFjBV7MMczzlDLa\n7EJ/+xSMZz99p7QxDK4TV9CaLRnVps25BvCT1bZpNShD5zbCUh+n+2SNexDe\n2ue2y2hTqru3Q55F+/zZIIJJGTiyEhiq++OrZo0NPdDriEIAXJLoGlE103d/\ni9+SKd4bqFc2CsfuUx4ccxuTnNy0gNDj9zO7RDVBWINqeqCgiGLSAqO1Ocbe\nib8Q+pMxEB6uI8HblMzwaUf2FHD1Mg82345PuwB8+O0XZ+INEJigbT7/FDKl\nRL5cNKHZsV2k9w/24YW4Q4t5m8ObHRoLwoGeO8LFXpAmmeDiHHJhZrBoxzTw\nADa5\r\n=TAvU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "55ee717feb29cc71e1e337d37c8cf46f68468c5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-jCxu3tBtavymhmHfuo0L9Z7eY5b9wb+Q04lwCxsda+MeI7KnksBVtxM7cc+dAHEG2owadDXtERr657e5/gPAIA==", "signatures": [{"sig": "MEUCIF0Q99tQn8MvLBsK0D0XTa6QC62RE64OehzT7uOHVncdAiEAycLQtG0e9JNvtkU6e/+F2ztLMft7H4A+g348cVxwtjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1ywCRA9TVsSAnZWagAAvsEP/iGfMAFjXoXdzSj9wTHC\npgLyhdB0ee7fLeFvm8TNHoH4NlwJqo25g7iH380RjEiiL6BL5IDkkiL4jSHi\n2UzW8Cvh01rrXXaDIRNRCUwgxxln89OSPpKAn6DGgtScf6jEi+UpWfRhaEMW\ncDFEHk2o3/sp8laDFeNyhdU4kFFflr3Cmcpa/UTePZuiklfSkG+ygr1lEaX1\nrrOrImputIUgAxaPqFpTmIU8m815zUyY8AmO9otnXlpspPjl2FKC5YY1ans3\ne/B1akPlPDwS18T1svimvehrMAloytEBqmvVfRYLVTrHhwlKwEwNXlE0bCHw\n0NR7Aujk24uvJuu8sEyDio7vPTUELDbtd7kd487tXgZZtG8TQHwRfayLwHZi\nOQY5rxggJl+FOVmcLWiS5J/SFF/3U6XciImKvzWxRqCWdH5G3Io3QynEyF3L\noTT11m3burPQRKAPX91q7f293FEo1OgjMm3QK6IMMkSsd7nqrrKpY8RRUbm/\n/Lm36oQ9x0101hGYgDeKQNBfB62pvqzrYK/KoWbo1qnq9mabTZUIX/NnADuq\nG9tI4vI3Yq5QOGSYOYuRmXTzS23pZQhFC+Ql7RP6CGWgE+kWKQa9Sz31FT8P\nnouxjwgduIK1spNekhqhd2pxArQ9MS/tT0UWI/1yi0GGXaq+nW9pfoa1JtTc\nFMdT\r\n=dz04\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e3d693c41e4a0e783051a74692077e1414d2eb17", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-U6JmA8f69FWrBZ4oYbsOjYYqUtQsaQ0/sFaL30g0/Oam/MFpBamF7ALGy5GwGsr0IbTPiVNHhy2gOB2zLNthLg==", "signatures": [{"sig": "MEYCIQDIZQxGY9Ppe/Vhi/OrETPRyDEkR9R2w3oOKBe4QO3F4QIhAP1xkG0klBOPiT+RXaKXuopfpBfJ7ZKVElP0F5Qj8OSj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFh1CRA9TVsSAnZWagAA9VEP/2bQ0iEaFSOAs9HorjN+\n7WkMalBwifDlIFhsSkXaraLrSMiA9rLUktfyxXNz8dB93Pmp9n0fewBKaGok\nR7i3GnrBKMKmOlKIGwqVhE7R8zXyM2508PPBYqv9leVT7zVVD/gs7sSumvgO\noEzSw+sfRvp7QkHW6U0p00IOogByQA4AH0iOnJ6BxHrzRRO/MS+Jfw1sZ0Am\nniIVCt551/DZZ7Zt2C2ffeElO3P2D4wNR4injl270nnydZWfSPRPnDEiGgrM\nGYJrZ4HeUE7ocUptoBEQBfW29GoZyEZlVEbsgA2b4L2dRgbQRDqTI70KWaue\nBG9nwrAV2/Kt+hDHsJ+VkAFpoR0sqbUfGcEXY0a6EUqQsKc54QwGk0dhC+sH\nldg96vLv3oHqjrBkbk0wcwpnxYB0v82ohXfA1FXAKH0Fzwhe0vmCz/1N+/Ou\nu1TZ6Rj13oRdi1DeGM6Ql18hgU0nI3ZY/Z+yBk9XuJCzV/J/qtaveWV/qhSR\ni+O8dotfTNQXWWhqcE/6ScTkpWoRWOijclN/QcnhCqI5BMpRdHQ3SF3pqyBM\nKmrwwcmImIpRSYinpnflEwDbV/T1QimhFweSc5NmL6IZH7TMyDz6WcT/nypv\nQ4gj9f7graheD78cpuDET85qsREVnO5tLG/HpzHN0v7absfkNsZ07q0f1lWy\n0OFl\r\n=IFtZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "109310483e918c2bdec1bbabd0e52648ed5b1c22", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-bW5etfbolfcSHsxn1BdUgFGF5CtabIAbv/EMy4m1qoaTHEdLU7ptspXFjAW4SeISTavrmxP3g/x25I1mHAJdkw==", "signatures": [{"sig": "MEUCIQD1odRaAddODZbVg/RvGNGvzypd82UlkWay3hPzPs2yVQIgGW0CY/zYd7FVQkwb8XuCNMrZluihcJQ7J9DgNuN7O8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835}}, "0.1.1-rc.7": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8a0fd27a3211e5b4434b29ed7f3dc68e81784213", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-XnkyTwBWtXlRtgTlPL50VHZ08/2dJPixgKqwZ60ajbjCJsbpi33usVaQJpFLFzuwLw8XKBqYXz+xk3LxPJtQVQ==", "signatures": [{"sig": "MEUCIDJ1rUG6wqY8figw/xq1MhyBGloTHcljnUUXxeNo58JZAiEAx7qdsdsnEL/8zo6Gex/bmI7HxdIkbDDce//Mqzds2Kw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835}}, "0.1.1-rc.8": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61d69fa8ead1a489c4195f2306621d1debbf4cc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-8ma2sQ4oKzhcOuTJ/aHKIGowqNpKGgp4cjRfHjegauiMawl3xvImq7fzKKb/1y58wMPIi1c5c2R45/FrCAsZxg==", "signatures": [{"sig": "MEUCIQDoGfV51iJ6Is8Ljpaink4TKpHPZ2orSwS44nLySXkKiAIgMdjwehciXhK2xCC6ubL8cin/yN5DuIT2AGCgtfvvit4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835}}, "0.1.1-rc.9": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bb6974119108af02c92205063340cb564f17e26a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-jh6ulACbXfr6xkjyCWG6yE0yclWBsHGHaBDFBTFxzHhRSKzw+jzvu326ErwIByUmr2nqxG0fxkc6bs0v2iz7zg==", "signatures": [{"sig": "MEUCICqyUfrZM+O8hYCePUv4WcriJKVRQcIn8y8diu9mb3uMAiEA9nMuTr/SD86iuKUmytkXtNkdN4s8y7prkUyTi0iw+3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22835}}, "0.1.1-rc.10": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1c742a1a861f149fbbc52528ebcdca8c05ad5a37", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-DH3Bl53BekHdBtEFU3mX58CWkyh2T/CuwYge/JK3XJvNdzB6zsJ4SzRFHkKjaKYxp6whFO8CWE7M7pfGeGyHTA==", "signatures": [{"sig": "MEYCIQCexKq514qs/+qIFxph6qdP3YT6Rbqt9b8orzZPsw2QMAIhAN3qEkrIcnQpH8fCBMvhv6e+ht5gW/f/f/x+Ozh93kU3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.11": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "446cbddf0bb8e4f48f66df7b444b2b28eb913d4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-KsPVebYPlXETTXgFOr2GL3jCZ0NfUnLG3iPZ72X9E/SY/69tDMp9aO1xyMO+B3RwtQd3fSfTAWRjP1xHlS+qvg==", "signatures": [{"sig": "MEYCIQD58S75Yi4cVShBW3N5pleq67VwXPQN8HYLrTUYUDDPPwIhAP5E2tcel5VfhHcxrcpl7lONPUSz+EytJCItMIUzVePt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.12": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5e6715b7b2746d8c6acd5661c0a5adf1c5be7239", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-IloRugO7USKzByO0R9N04gxtrGCZlfaSd5gjvZ4Ey36NtrrMfESYOpWGhN1GCOTwBAGzake9NA7E302EdutTTQ==", "signatures": [{"sig": "MEYCIQCf6aITDDLtxn83Vf1pzzAUQVBMD+0Asso6Mcx6DaEzsQIhAOVGnqOV3nfFm3ElbEvSi0Wy8uwthLgU57fHyH9IxGCU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.13": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8e6c16fcc862128ce1ee24b62772243eb0826a6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-kwM83qN3/f/TLSQ+mQwvvXXYGpLDvrL11RJfmsJAOcIrPHaMHSdAO3CEPn78KjZSq2WgMQqGuqVGBVO6nJsvmA==", "signatures": [{"sig": "MEQCIC4eLPhFlhBdn/xE4LB/SMGrsFOsp+uvRMo7jRSPaA+PAiAhwUiC9XOkyG4LI/hWBsjyWXJoaWuiDJvs0nuXWJDvKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.14": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "80e57ca737f07be034f1b58e85dbb4b7354e8a3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-A149woHQWqEj2aBB4AlgPXD3a3xbdHSPAS/m6fbLmmqkxmkMXthTSIXTJUoyOCmeMV1ZHlEcsVmlJVmM7qk7zg==", "signatures": [{"sig": "MEQCIGcvsNFizuvkqiE4kjnT25Dz4N7z6T6iPtRErfRqQP7ZAiBxOcMtkBNNGBzQ+KALjfgC9wu8ez6txJ/EGNCF3Ucb3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.15": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a19fdccd3ef5f06be455a59ddffa08b692c31c59", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-2cemPbW1GSLhWdSIqeHsaRFnwOGkj2YQiXgIdgK6oAqGhtkMpdoPz26SYEQwddNh/O8seRtyURy/4EQZEDz4ww==", "signatures": [{"sig": "MEUCIA4nRcpez464woPXoMdAaVzKIGHUNyuqP2kSapjYcorBAiEAmcOU2g1/0srVlp977RJCBLF6+DARPY2Pb6Ce4pJGEAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.16": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9b57289def95548bca080372c84d6cb403149f4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-YMwyYhzpIkWb4Sr8Wok8FwXPE2OyIZB124MktmKLRwSjFfcleTez49txOPGdg5KDDs/VODQTQZGaSQ6OFL7ZbA==", "signatures": [{"sig": "MEUCIQCe1Xs1Tq6nnlkkZBf691Cb12/fOEDUs3n8F5DyKRGPcgIgWRNxqyvKZnqONdtOYZCd0wns6xnOjGySD149Kb2pOxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22837}}, "0.1.1-rc.17": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "16feee51697a60b2ebb234b59abc6ce1abdf637b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-c97MZirpQvnBTPs5z4Mjv4TfDly4XfJds2fk+vZ+lH6VyMHAEh6FdPm7FGjoa+xitoH6dZZR9Y6Zdhe+xLuavw==", "signatures": [{"sig": "MEUCIQCRizn6uLHPC+N2NH3sC7H6pB3KLdmrselEWSIZOZKaRgIgKlHpEcmgKqPSRqvitw16KX2c1sUacTfIxbLGMaLyd0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24620}}, "0.1.1-rc.18": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d62d4667f47ab2e7fe62f11f987ec05dc17fb2eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-f5f7p50RRsQUdpIkfeil+7XOy0kn+lYDv4GeFFj+1AQjNJwyBI1VxYYaPafnpeV/CFDCpQEvSgBOxm49gaZ+jA==", "signatures": [{"sig": "MEUCICp717Sns4rxe0p1VmQoo3sebCZfSK4/MO5PTtfh/p5kAiEAguE9g+p6c1utof7aut/jHeRBasy3Zq6QbQ2w552LrwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24620}}, "0.1.1-rc.19": {"name": "@radix-ui/react-avatar", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b77e4d5686173952dd1583303f51c63ab97bee91", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-H/JoLzFaz9F6rnZkPKXMiGgz9+Yf7DfTmNz9wgHlKsFXf+W6jR7vz8+Dgm3P3MrtCGcFoKUe42zhSlyRIu9SdQ==", "signatures": [{"sig": "MEQCICUKwCYYIRQLpS8U62Cr5OnPgzsAD9sq4xRNUxSfN2JZAiA57vPFoJLbXATDbWMrpJbTB1EbisA0LXJ2jeEWaywWUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24620}}, "0.1.1": {"name": "@radix-ui/react-avatar", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a5e2c660f5e2564ad75fb30587bfdbba64137411", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-yFWO2VZNmO6EkwwdKSIf5Wabd97iGFzKNrLI8aIHFYuZe+WBOnfF99SeU52wm2a18k/sdIiwTZlsxUCiSBadEw==", "signatures": [{"sig": "MEYCIQD2QAz3Yk0OoehMLKngffpTVkWbPCWIBYUg5JUrXuecgAIhAM6wM5hkNP8CmksanUPTErpHBwjC9myCt6YQ0a9c6jjV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24575}}, "0.1.2-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6a7b25e89fe5778d29e7f9308da2bce0a8cf39ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-65Xq9WmYTmnhZaJ5AO01lKmPOuQp0HFE+T3Mq+lG9A2R0bMNi+hZ/0AeWHoFtUtw5B/jHl+vBO44IFl+WMlWVA==", "signatures": [{"sig": "MEYCIQDrIxk0wkYyVoLh0vlEvmS0HX+r7dKjs0c+kTY4s3GzPwIhAKlAQHcjRL1dmyBjk+et3CV9Ss77Q82V13OsPFOu3hRu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/gCRA9TVsSAnZWagAA+yMP/28ZSsUsNDuFskzwMa5E\nob/fdzYGGEfnuj6yjwpDavFz/dk7PGjUQrQNUEnEeF/yiWyb5GBs80Hhun6U\njc25wgbwqY7RT1mT5rJZV2H/4LIegkPnwopLp8SlDBDrMQya4lAZN99PjUnk\nGn1y7Fdsd/NuW1/cuxQpGLVSNDEzq+172ufh5Azgz1L06AjmzZ4gvTuvJz4B\nJNnB46Z9Xre6RzYPwrNcTOTnhLipgn0yrB8UHmrZimJCK8uEnB3hF4CnsDmi\n5aDRfwUbtUyiI2ARZtAmBYWHYfEvn5qXChlQ0QbMKkR8+C7XdFirkWVunubl\nBemWtSskudPwKKef9DsiHfIcxAeB5L0zDm+MNiP2VgjdkGvX6QUPsOEZjJnn\nHRgvPOhJTjq1Tt+O0EpwqJDYBnI2lo9xPRKCtHAizqGfRzfnPdAP35rQkoa9\nqEcmnld3PD63vbf1Jv2Bwy+u0g1iS2CaghOcUVHA+wRtRC3koba06GLmPLHF\neXih6pD3n+Ne5Y33SL50FgtbmDRGcW0pgenKrJOAejW+5arWSdS898oeiWit\nW8x+eMylhgsjdv0u+BonZDrREPdpWa1EX2TxriEIHfvyF4VXCznd8Xf7uU1j\nZm1o9eOIPLz+vDkylpzYm7m2yvwavH15ueSAGjRISyacJnOp5IJvXWYsuEVv\noVdR\r\n=JfpX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-avatar", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b17323cf548d236bdcff15426f4619bd0ed87dd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-BL53mUjwWvrtd8Nm/BKiy00fMAkcLl0W5pjXYJzk6d4l/xyFck6XNy0pMaqZazGlMXlz6fFIt/K7ilnWFZiUAQ==", "signatures": [{"sig": "MEUCIQCha11YIQGv3RjOObPp/CTnsAb69RRInWaXSarRgJAdiQIgR2IF0579HYWDQ8VIcjhRNYS2DfoIXmtcaCNSu5XI1og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiM+CRA9TVsSAnZWagAA5/cP/1rAzo8JEcDk3WizpjG/\nYMBTFhF/ldybO0LhJ+vZzf02EaM18wf1/9EzuK82Yh5CYrGAlTBODt0GoLQg\nWARY211nnvnrPJouPZgrnjpfMpvu0CkfasYVHyiIsOWsuFYK02xqP/5mkwgx\n9KojdWw55rOeHbWhIN5OEn7upVXU5TSmWXhuYcOKbOEJSjBcATcqPih5Zx0G\n3k1lZVrwi29FtVubQbBJuO7rbb62gj9vjmD02WtKL1scAvjtekLdGFZe7duT\nTmC5ah41H45wQRUYZuiCZzPDYKNrdgk4e+cjmsYCbT8domtcWOUJibObSTkJ\n53nTqolZYLzrZPyXBP11BteKy/JEOJgO+Y9oxvD7aroz0pfCnA4OFmvlEddO\noAu7ja5oV8f+fZ1rRNEzIriS67F8h19zJqupmXllcPyZNJPNNVsHAfOmxCGh\nmh7iO9v3EzxEny1MotYkaA/lEFqnyOiqQ4/Ag6D93mhKuH/v07Y0cGOjBSEw\n2cCaEPRX44d1ugO9RUmZK0gAK8xDvKk+OrjWx0xCOQli6lqEXy7/29rIZtTp\nqe0EsEMgwOfG0hFKYwy5vWtZDdSraBd8KiOEfYxnbeg8Wtg5OQ/X5/38DZFR\neFeZJHGghefCA8j+RCRBbHZPlMXWt4wKK0O3Er7jOQos1Qr0HbS3e7odDLhY\nuvR0\r\n=XJjQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-avatar", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9d7e9809ac8b67e5f686e4bdd628207240f7bfb1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-5M+MRP0RDtlmNuWjYDetc27cUMtjq9y1wSQBsPSI5QjozvKTpq2uKkI19WZbyb7Fnh/GuzoxnbaRa002K9K7/Q==", "signatures": [{"sig": "MEUCIBDX4p+QMgwpJsVxBH5++WLX76xRytyEPnCEjF0+***************************************************=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryiMCRA9TVsSAnZWagAA6l4QAI7Nj1ZKpADcWLXBHRwL\nm4+zS58KdRVzxvrdzOERmJg1kYKl3kB5nWlvW9JK662BrEzXbEwUpGk4NKzl\nY2dEbWmQ0Xyzesjb2TjtcVUNBwQA2obVlpOQV56cbjGCems+pc2iJeAwnJuc\n5Pcen4zlUznjfFaSa6OS9pZHb0/nsDtBgyagjMuHychck5XrhrghPeJouGWv\nDXDvk4nEMTY+RAxqyxlp9wIyKhRDv4cAlgoIVvbY5e+bd6+/n26byi64dChV\nEZuE99ZXsurKqn7xT3iW5AMi4LMVG9FIfMuBU2ZCstvc8wSNSXd6tlv6U2bo\n57q0AVLSsozTyhYTd6nZYvPejHUxekIsfJ7F7DeRO2isGHsixkM9Sz6ViyOp\nBxUMOXOV/JgtWB3DUfGQjlWCF1FKnNIVgOiq48cINfgz6PgQf/zkesDAuBwd\new0U3kDriTZEbdbw5IfwxS3JOe6RB+QCwtWhh+2EvKeTPTD8K6sAwwsiC+fZ\nH1gInL/4SqvaAvmWkNE8zPQdyXp8UnUlVzXbjycQLrJ++X6EPRhjVrv7ymq0\nj3rppceuTimaLD+I25L+JPmDzTj/+FCXjrBITLi52P1npSDG6Jp7WAwv/2VW\nPgi3822Fs58OHrqR57HNxGMcaNc2QmMUJRM0Vc4cf67Q3mIf4HON74/kpjIG\nGfag\r\n=2jLN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-avatar", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a3336054d9e1c76b95c3d4da18e3304eaa11927d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-CxMuJ2IFtTqZ42rT796a5hNvDXbsJqxHQKedIoyBi7W528rdVI/hJxsTExoDebJqwheVdmquz2+p0k+vXJygOQ==", "signatures": [{"sig": "MEQCIFFr2DYfloq+TtFbo0278W2nkpSFhMHHaBXsYlw8UbAqAiBNvREKq8D98k9MEpUJq4IBMotpsOln8LVU4so0faaYAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzQNCRA9TVsSAnZWagAA9UwP/izPvg7Lid2+Q4kh0pIE\nmZdjluBZfXlqtuQQEKaYso0oejE7r0Ckj7RRAreSe501VL2Q/SrXx0v/tXSG\nLj7CL0A8o3yh7uuvAs6o8LzlH013PpzAtLOuZacntN3Q6CvA8g/ghgWcCvk3\nzKslytfqpnSRxDp2NYvJ7gCQmTrtlfgRl+Cpg5zBaSBrfEdCCfhPSFNRIGxT\nPa3pBjhEpfWlO3Zq0zBvxvZ0jqtJW+/QtEv+Qqn4PPoAP4ba/rKpHmXWn759\nLltwRUMzQLQaePI+jgr+6LgazwAgHBzZLfSFJwrX/NpKhHIqrjhv3t/gSN6a\nfOLJRQx/C3ws9mDaFOHwm0gTQnpR6t/bArw0ArA56WRiRxriuDAp/fuNHO7T\nZ2xzSLjSwJJ/irmSfg0edycDkwA1zUY8Wnk1hxm8WljLp4yEeFLsynKGooZx\nVnUeb/5T3Exv2k9/PtoNkeT6+xXMGmY3uAZ5BGzLZSXm1F1vFlZMj1kPJvDf\nvxMoW5w1X9uELkCbMElG6oTTeIy/oPuFJlyrbeCLT0rYuXUJZneksOsZRcIO\nz8zgRICrweVvscupyXZv8GgyhgcytMJy6/M18HTH4OZUTPhIoELjICw95Rw1\naQemcewQql3LFyj8J3oljVM9BVRVRAdHOCjQiQ+95x0z6mOsBybptPsT0N0Y\nY6Q3\r\n=9suR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-avatar", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d96523950de25e42b9766d32ab92ded8efd97c0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-fgDYHu7bPQAP4zCFcM5bWEJdVH2aYPt79dHewqTKEdz5HfdJKOHjPAO1T04DbJ1xzG0I+LZt/BTL2Cm/itIJEw==", "signatures": [{"sig": "MEUCIEFf6xPkN5XafIYxxe+57maTB8GosXcdU0jg7YzUugjxAiEAioydYnsxvaf5s/gVA8B9m1Go23ltUyL/gv5pQYSW2Fo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41mCRA9TVsSAnZWagAAESgP/2T1R5kv0UhC7pqoF9Cg\nX13ZZT6d5xwvDrtFJ2IgGxLjddfl18GuR4Qyp1kcTkTnZo9+ZqsMoKA02V2f\ntwC8vcabhjsKnIw8SFNCj7hy57WwEp++Wh2E2O7RPZsIyDn53tguULqF5uKx\n8TjJe0LHNsC71s/BS0AykAN/4rAHBWQrA2OX/QbuD9axdvvuMacEF7iDCGuG\nd6AhmxTPw9lZowg8s5PaguxAkqsujtt/JJcoXff/zRuIrIGYLoLYBjKJa3y+\n0UZzFNauLD6yvbOQ6Kyo01g3GJrpiLfRF1TznSp8ozKNFjTu9bVwQpJNAAmb\nfzxPK1muwlAw5X2k9yT9o7PkJOIes+fZ1JE+o5J+vri9tvON/KvKyEWHSbSo\nfxbKuUDslX8RPmG/H/NC/I61kemw5gbEvng5VaEDl3Rip87HhpmyHi5LBeCx\nEugyXXdxfV9F6wQmsYd2vy1JCB0RCcD4hvnCmG7mrp9xA+uMPfh/iH//nBrm\nR7A+zRocTHebLKPDsYJk9nHoEvuTwgAGnEwDUlS1X/T+icRGssqvsp83clPb\nq0UpUq27miKxGqnxxN3oaHxee4K8wb+93iwywoUSK2QwsRG6C6BCFVILBDIK\nEULAuFX/ID2rvigBS43aGRMJ7Zq7NF5MhzNvCktoJquRWBA26ab5wwTJeRZ0\nigcT\r\n=fMB5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-avatar", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "31ddc80bcaabf1fb3869490de4a3a9e07da5ddec", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-aHfzDe/QvDOTlQ98CMK0fgYZo8H/cPoFh9FV0wsCNppIYbUxoeXl3ebdLYpLf9mpZX9OTRW7B8ksmVFbCFWtrw==", "signatures": [{"sig": "MEYCIQD/KrYHFa2MVEf7yeKUoICIMiKtLvnwHA01XC0pqwLnTAIhAMwlhCSzPHC58hUiu+t+408xhnHyVsHsC8uZLg45nTW5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDkCRA9TVsSAnZWagAAXNMP/1X68cAbBpvo2KpcmFdi\nNiXXGBir/JQNDFJGD/o+u5croum0hgd/lW4oLZ2rlV+D1Ar9fFAx3fYwiBHu\nC6JdVc94xk66L5zEBGvAKKL74CEQMNQkLKxQsby45N7eaTtENcNBvrc/kOh3\nCyAIdKuf7+bAuMcyx7vR/1pyeS5LWUvtu5SKWeo0+6AontRyGiyzBIu56XJ6\nhtIhmq1J5huwFimQxH2edJzCaXur0VdRo+qIZN5PSo2suJ0z6MRpZxmb5+oM\nhQvo/2MobkEW5PnCx99WJR+qyWkPfAgDalQomOVdW/F6m4Gg9jF3xv3cw9n2\npS6pgh5UASxoXDFHG0Lp4xKxgcH/qvir5/fE0isnYc9MvuzD1VTzaHtN2Jju\nPIJZbEJ3BQv9cP9PBsXe0YbeC7nvMdcwr1oNp/1j2EArjPdCIH+mrBPFdd63\ngjBCLy5u8uVGguFBxV2mHAQkVQ7CBNpsfzRb2eCDQL+0VcKBrrBWbvzU1/WS\ndSj+1HZ8Cicwm3xiKvi3oLWBfAYejE0tD82ra2iMoEva4F7ZotAQGkaleWDX\n9jQOKioysr8Rgqvd9kQyTBTKqvqL7c4Chn83o5F+J7hpzFIiSBvRPmCYAa6r\nXKuCGI0mbLJZFmc73cwzEdDnnubwssfJW9eDEs+GmJmU11bd8plr7J7nnnkh\n5Mkl\r\n=Atlo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-avatar", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "202192e55b4e0d0b98f88ad386066ca0f032f756", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-hzf8PCV4L34fekjg1Peos+csz3nNtwV2hnZSifmnug1TLBPI/tHXFlr43qp3G4rCB2YOJVW2ZpGBuKpomRyzGw==", "signatures": [{"sig": "MEQCIEtJmRO7gsvt1ujl+vQxPeQOeWrAIgL/8dAlKVWoZR4WAiBKCcg/62Z7zRnaF2Tn/KlDUG0inygGPeQCCfYxufhHvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiZCRA9TVsSAnZWagAAFXoP/2DqVcy3lIYuI9he5uvD\nw6ehNh47wTB+YiwnTfYaFbQU5qdbeRRH4KG0l6rfA9ltDzOKnqgdSC5f2FpQ\n+J4omW/K2zpJGNZHgwk939s5istabcPRbZcb7syyaDpR7zLfiNM6s1449+U+\nsoz8ueLAX+5FDT6YNfkVdxMSXf42PwW1MtqLH1zi8ZNXcyf3JieBHHtLkZX9\nWr9hVR2osqdqqN8MKh2C8x0ILkusB/gZYYmyXpHI2lBc0gDr0DI/QcDX7+c4\nbMmmXRvoF0IXDV07kSC67gvGmeofuSN2r25vPwv6egpascwzfjFIZZvdF0Ja\nWOmKxd67Hs6MP/nWgBgdUpFYY0pxG6XN/ot77nzkLv1LYkg9kZasAQzpEukz\nsRMA4P8VjLS70Bl6Bqr7vkoADHn+7kr9a3V2hnmCF0WntsSHluFfQDpUEWHQ\nuLovUW6TXAIS7EjRkYEBDQmo7sOB8BDEv4qMKtD3INMLE3Lk3umassMrwJC7\neJ18+UXTkMpwHlCntFyzfCEePx+TUP0CexPMNUXWeOuFOAdAZ0cJJSLsTylV\nxijwf9ANm9MdkmXT9+BVawxKRhXXHtdwv4XdMSkpvhAtHvYcXsDbh9GRfBvo\nFVLAgYwcC62f8P8kgzDXGcaQiHycmcvF4S8YSJtG1ARckRHdVJ05ZEfoDsv5\npDcO\r\n=Tw4k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0b662666cba507d844320ef0321135b42b96b680", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-bFl/QhkXBY/GeaYtJjnjO7K4IMB8VR4CuqnB6LcmA9oj7yyYeO9ByJP7F2mwJAxU4M37PHyf8147iTQXGO1Log==", "signatures": [{"sig": "MEUCIAPYJsPSLJhs8K6U2FNfUPQGzFTHCm26pADHxnIkEuXCAiEAq/kYUqD/pvBY8ZquEwb2PcH0oEXyUL1kHCXK7IZvo7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLieCRA9TVsSAnZWagAAmGsP/3n+1ked9n7W/jtm4YsE\no4uchwzxcuD4DVTqayqpQVx5EhFVgyUBDcHVbt4kzeGzXmsuZHP/wsg30C49\nneloQJSzFGZlMKF6Bc4wOC+82Mf4Tgmn0UTtlo2T4ykDTh62ZJ3OJwmdAWYx\n5K15L0vWOTCt7f/nXOkXYUWV3seB1YX+Utbzn1w4xIKfAc96rmtGScsKsu6E\niURbzZTUyFCSABp5LkU/4C1VCDixgjctuQ4nLttRvUrP1cPOemfbm4C3gY2I\nqCY7Mf1acf3Fh2XUEeCQCiUyBuIWlQP71oj3/tnlMZN+NUYGC66Cb/FvI3Z0\nCXEZ8tmRxsH7l5Vlzjj8n5DTQYEE4cFotoI3BG2zHq8X6vwbMeFXXW8KrsjQ\nYJrXF95I0DtnUcAo+nVaiLKhvpx9dptkxQRP0bnQnWJc4hxC8Cr1SWv+9Me0\nHn0Q1USb58/jAJKmlC6UY0YwvpttBeleLW9fRfgGDZW2AGrrqRvN+5XspSnt\nWBswgkJEde8bDN5wbStDsn+SFBr7JNWq0tu6R+wGT0OSPwL2ToJVH1RNIze4\nmonWmw9pfaBam0Y4ijhJSnCU1CzMzdLC+q+DtMun0llHBx9B7k/xTGMo2be0\nZm69D5zgD5wNBHNwZzoXE5U4r60Hi96BsAui5uVD10s2uResRE5viYoP5AL6\n5m6G\r\n=FV9v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8415ae9932672d9a37d10e02eb1d46f260d22dc2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8S6Rgia87feu1TO6hNS0meHsOhIXVND0ZZ24yKKMsfdjMYM2rujH7y7WXkAnBWKVnvMHmQB2/SqgmyC+WDok6A==", "signatures": [{"sig": "MEUCIGUYhIu/e+pC291s514nR0KIdUR69EBEE9ePmUGilFG7AiEA3/WlmfOWe0IuALGMDi65sKfyNYq8aNkYrVKeEUkBFZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31qICRA9TVsSAnZWagAAuZsP/iH4eP9hvIEShUwsCCCt\nWWxcRi8fduyx4j1JkpZSmUQ1XUCu2NDh05fQVU/y9qgkaeT/YEGxQ1I5gTcw\niNrufD4p2EB1PnBjKUDBU3XQYYgO0OakZZUDVvC5QNyB4ApbGp9zOc9nr/QC\nN+f7FnSCLNSIy6IhsLllT6YIMnYspKmcze7kNx6LtOIagKu8ml1EAPw0ZW0T\nfSrV1xKimPMNTGwcdqy/Id/SzD5dZTlBOT8TR4Nm5P1ypkc<PERSON>ze2bKEc6jCw2\nH31z/b3xppSHTHtrJPkYbP4wDVFUVnuSFKg/OzKXwBdzwk1O5obvQWPddNW2\nrOuFuGaY0k12RGTA5LWfpbtP13330pPlsEspaz8xOTp7LFNfo7fRPKiNxU6v\nbOptpGnh6YBhnHYZgOVAhNVHQqFfJbYisx9a+9Fp9Xm9eR7ecaFefDy1M5Wl\n54cvqlX5tegtzD6gDx/A1CKCMdJDpzC3ZjB9R1GQbTcQAuSRKw27U+hRHyoN\nFfITZNb5OiofoJoCQTKXHn9yqDapayOY0H4laIwhRBGo+cdudvR1IDGpL98w\nnpsueeLw0JQ6uy6/6KwLAGs3xzdzO7ic1gLDBK8XWL45bs9KcGWcC/E40Qvs\nP4YWoia78RM96IgCGWw1gJoyIsBdjyzxGVqEPROo6zcp7xcqox5ILUu3/yaJ\nTZqt\r\n=HDKt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e39fef9d76c9e50aced9566e6944dbff1fe4334c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-56ZB56Rifux8tDMnnWuRZVXoNboCtbHmcQNanf8lPdPD4Zm3dlsvSs/L0B5c/qA0QrQ6zNdKTDi5XxTxAhFXGg==", "signatures": [{"sig": "MEUCIA9AaAkhjksTwwyihtwMHgXE2bQ9Hk7ld2rLORI6GJyhAiEA0gMGZyBKsIJgZ4X/MN7k7RqEh4QX5ArCGDsHzX52iN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BDACRA9TVsSAnZWagAA+l4P/jLoqiki78leCxfyQ1bK\nXzcsYCvw9LuO50DDy9J9kRfakFkmLNSL32Cbc9GVblkqRkwXHB9Ou5/kGnEu\nwxgxyV+Y4PLIAbHLI39Tawby3CaIezcH0GFc3/h4mDyE5OJ+0tf7pq8E4vf/\nvBhO+yle7gnRg2GyBmT81Un7rNOZrmp/TW3aTSyAgaB0covtymu9YuKth01X\naqjGkFO0x+Rtz839/o3L64qHK3TZqlLT4H9RGdZBxvqHbBkXV+7c61neY+oC\nKrguoEEv8NT6K8rtdOiFPP2yKV94DKPbJ2WD99OV7jK66s0NS09fF8xm88tC\nITvBYRV2QRd1bU4YmJlIIjIpIaiwn/0Nixw59fPdsVWI0fq3BRSikT1us91l\nIUqQ7j80C/4nqesDeGXQNRpLgH9KmYlB/485MWeVWSr723e16Z0lsjC1NwBN\ny6V8NUrL3uE3sDxSNYxpNF3vouJ4atq48p4pvvUeLSPoDCRlAVUzCO3pUABg\nhuxK7IrzO/dVRiPu+j5XH1gX0sS9K4MSJtPyuzZQ8YtgiXCAIo87+d4EmG5L\nYsrGboEP0xATkw3gi0KupdsPoRS/L601BjtPd7hIibMGS0isxJ9vDkw1TOT7\n+Zo34fWGuMOBzx0DZ4lC2AYYFQ//AaYk4dVVIBF52HNtvfOCDSjk3QnXeMLa\nbkTs\r\n=ko2+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c64d7ed80cf9933358eb28af86b8112a9936994b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-r9MqVwT1nVXTH2HJ4ngxzA29YV84Jl6WM4Uxb5wgLZ+T8j6PxZHQfI/zoSjvCCLAYUHlGcBTQ6yInp/OQpm2yQ==", "signatures": [{"sig": "MEYCIQCwtce7F1SVFhERGgtCzboZdO1Blqqbq3sqEatHWZyUJwIhAMJQgjr+O48xo1aUWEkfni5kKzsm3d6WWK7KIm2h6Hra", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4ClkCRA9TVsSAnZWagAAyH0P/jE4ikwOqW5L7i9WzP5X\nW17SoGrGDVbrSzctzTrQ6eUY77+S7VUloo/HJSTacmaqzqgjnrFJ0q/6a5XS\nQYMVbiUngdZw0jgl6bVIFf/4sFOTFj3+SRzxd4lNvNUB4rhcrxmXWPhh4cpK\n8tUzc1MzXAZSbYdUtoIreuRQbSdtypeDk4M3MQNC7FSDE+sAYW6eIaTMrPiL\nOB75Cb8f3w7VkEyq4hOQSyjHmX4DiMSUCsi5De6n0nHCoZWFEIrgszB09F2e\ny90QqGdSiV7zN7MDhxHgIq3BIKeYAoHV3xIYx0pfBv+FzE8HZDXU6jDGo9aX\nzsRgCfjfFT+u7l32droeRt12wbckg82ZX20We+aLQRbT4ZPVvG8guJiPXae+\nt2l7jkb6X/uKSFm3gtmZMsVpDrl1ii6y4QHrOYR2QAYl+jdHvhnQwhfgX6ml\nqPQRLMvqYsCc8PNos0AdRv6iMf2qIaAmNveB7vU2ywEYkiJQWG7SOmEadOBg\nFY30mntgpJFnJO8OZZtDjfqUIdbq1wdtMnkMOfpw6pr7VXVoqm1vesyztf2O\nptmIJ77cTwWcySp6ZPd4ujGm2g1rsqwTTkeNPq7Nd5XUQoMxhkNYyeWqPHXI\nNsdPw5NzacbGrvFBdyO67WKJfSZ8VcNSO/5JaOvpdQR+9JGu0G80sOjYRKCg\nqsGv\r\n=7CAb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c740b430fbca6a059e9bcc18bd65be7d09319b89", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-g213omVAHGlsdy7+uLMNqT5o7aCyPFpHurP62rhl0KLuTsO1Wz8J/L/eMyrAm78jUYYgc5m5010Ad8ETiUeDFQ==", "signatures": [{"sig": "MEUCIQDHQ5FfvmKWInhFl3K7j8Vv4TuWeZ2uLBN7tzIK2omusQIgKsMlmOehwbC+JAHcSOfskWnKXjPWZs9m9/nsuyOj4M0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GpPCRA9TVsSAnZWagAAlaoQAIXvfjZncUsufODBMa4M\nauzd5PRPE0yt5ylcYGnsTNQtVzBTqUumADbW9N+S1XNbx4Dwj4Lmd+uMVzWr\nsDQnsYTMXX0DOmoIB3yermd5QFMfPIujBU1Y86ZjrAqhDCkffBczfMb5kBuU\nJ1qYw7GvLTUycw+fYcNZK5X7RKnmHVr3NUBqf/QG4JJvUTvj3BdpP9QX7gdL\nDI1fdby+4CDa2dEkCqN+SivqYnyUe1x1w8XQo1SUAPIPIMEgqSumYX/n8ho4\nTJcvU8bBmnyuvntpLj4DQcynh1aBI6kmz8RcsDew3/wxXqJ1Jk2Ux7hIeFOz\nv2erN1DItvlu21+XPbdbE8C/9FB/LiagtuHiXG8lrqiIR4xDgq64L9+BKALb\nnHWzto2fqXRqIDr137RWtxVff4zfGDyaO8wreW5/bE1PP3VRyLRlXpfbaKwO\nhGqxONq8jPkMCAlj108hBxrWRsnkrZ2HhTbap7bXBEwAMAjowDXpsTixezxT\nPSAjd+ohbcwiEotQYST61jTtZJhJnSK0GTJxWnOB32cowGE7bdyfzWd9KcaO\njuyhY29qU3zp/x4wHuFCYI0mcP3ef0oTU95vb9EeO330SPg52qwAfhhDa8ie\ndQiHj6mJBbLE7Inir6Om5qJy/wnE7qK1LNxzE41q2dzDxPVVx5kS/lb8tLp4\nfLsC\r\n=g4UZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5e27d48f0c362c1d0d5f3f8fff93df8290c2bfd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-vSjTY2qFZ0POH83B22KCCzm612Ejqe6EuhcDIzszu6zhrnK8x12gAu+VWTGwAlMf02+ZyF6BKPDALSAVjRppng==", "signatures": [{"sig": "MEUCIFiz/vh8XUsKmMif7YeCogj98CH03sHnwzWyYk5Unc0BAiEAwIOAM+q01PY4lSolULWqw46BY5F3OaN7JRrCZk9kIXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbVCRA9TVsSAnZWagAArv4P/j5Z/t7zp2HHxsaG0aAn\nyJd5P+CDsd6KdpRA+yDyHfR4R5PH/kVmv00m/xnNs+XfgU6NERuTDd4jdDqt\nlr6qDIc6XYSTtmJiulU0r1GmJMD73237MNT3ktNiRffEBPYiSCiwEWGvsoDL\n1LH3Mi/2NJCtzT6KF/9c42YQIo7Q/C84+O1OAh0w/JZmMq+I3lT/yWXbe+K3\n8EKw0i4M+7FyD1Lf+tFu7tkS5n7cRTBLX6s2oVeiFvatydTKjDEqSg7BYIBr\n2H/aq0KvuCQ34MWmmdnL8ZR32ot2b4oLkjIrbqbJ1aATQJrtGzliMum8va1y\nVta9Q/NOnkg1Hx4DT0kMsIVI5rdUhoC4SiNa4V9BPjoFzTnCBjHFv2TKBIfX\n3ldb8WWCR6hluYCCTfUrQvSmKSQKKtZKa8Dsl7XUFXMbeOC3IZLxKgvn53Zt\n8Mmx0Wj73Hisqiqc4rfQDPqr0LKu1DNKd7J3aq9gVUfe+327cJ9MVrSTJYqG\nT98CoOlpu7HoJNQXD/TLSGcfa1Lutw34c2iCgqf6t9NiyHF3L2AHCqsRjL66\nG+pVi/ZSXLzDni/IQ4S5IrkDIhvbxd62mnVPovRqzKeq3xMzeurYlt68kt5Y\n/pCBatIYI3uv1kJmcfQP0J9tcBsMvZPje8jJNnur/sHlpjIzUX6Cy9PGwX/u\n/ufk\r\n=R5yK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "23be4b997ae770edbf0c6e499634d1e28c073e50", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-qGkCH9JdCFP5l98M4vNXQ/RRmiapW810x9tuypg0x3ruyYLrR8oBbJJt4yGLuMfleJcoMALyzdsi5JSIXpaxpA==", "signatures": [{"sig": "MEYCIQD911XZpiuA4zETxCffM6+arhtn0YNiBN2xenYtL79o4wIhAKhhToIgfYzhA4nNhai0byu4hNFbguRkCNrrd3SDzSgg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YruCRA9TVsSAnZWagAA1PwQAJ1ZsOIjRzeTGEaxnir/\n05k1mfFH0dY5Bgw0oO2QX6FcmPy/TLE4HGLrcy42SNFC2NJgetfuqmF8lO3j\nm/FdStSQT3BbygXAuMGc6QYjooI7EcG2j00iDfnyHxj9QAhC0wqXtGIYNbuv\nAQye+krGTiKSQ4EA5K6/nq5IQfrICXKU1I5e/0govSHsI1jQ5kE6IDnTcN/C\n33ctk4MmlxGRfa5fTGwuaA0mAnsTx0mPJg70kk+WVGUz9/miTd2u8ayDVsWG\nHkPs/QX4VLqzFA5RixBt0vouRNU6REwAGoNcOVWsfnXwKiKjRBppXlyCyRjS\nImCx22XPpYm8H613GVvODOAJ5bLEY7xy84vIsgdTRgKXAiO1sEGdaF6nxuLE\nDBgYhlqQcwBCnOOfjWrJ+ldIOJLxDuvW+rVKo4TCRoY4FKVyOBk/X3C4yqf5\nz19LlLqPd7wtjoCZ16KNBpV7djaw6r3P/+0qM+UHmC4jrQCnR79e9SbQ0vT8\nnzZznqMdF7H+RWZp4sDZvmCmtKc0f/k2ihuAYjjum53p3GEA5trXGelW2HUD\ndtBBHP1aA+CbNAdEgrz8h7vpefZnErpc1Ush4v9joqceHme8jYhbvdx8PXN1\nbK66m4emyYWZLkwtSFYMNqOpJ/2IDJxwMv2djw8rzjRYWmgNgN3XazSL6iea\nSPfv\r\n=SZ1Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8ec2201c4c7504a7a7911c9044a085834219b076", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-+42wNYLPNF6YnLClb1W/a5YuElNi/GSFZL7CKSNSPTZqmG83i93w7C4UVx1v7b8M0JozobkDtCmAUkIVUzJaGw==", "signatures": [{"sig": "MEQCIHl6FZP/8f3X4RpPp5NrAyC1HMLi5KfAH2ciElUQ8okyAiBBOTk9xqHQJYmocdco2dPNUOrHi///x/OOiiOqWqUSlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scSCRA9TVsSAnZWagAAX0IQAJvtZwFgktKbaTd4pgvH\nRlWWirLU9zzipjgSAvlQCg0KEEv5ey2/tQNndnBuN3D3n49+LJJY5CWR599r\nqFESDc/Jl7R0nlMhNOSoxUmS+xCCconySRUJVGI37550yebGH+MxaH6W++8R\nFzR53MlbH5jClHEKtGxRhM8d8XmcYozRmsL4DjWrL2ths4TfT6+p5t3Us/ed\nl6W98+t1Py50Ez4XSf98FlNbivKKSgcPu4QKpvOki2llddyy5qjBssx+esPl\nE5ld0u47KeFZLOpJ1Jt1jsZTl+qdPRfUzqtIPliyrsPiCbD5ZE1JzqN6K+B3\nduJ2v2zBQ2gQk6JFNrD/qOvAI7MbIYSUuUEX5ooExfHUheGG9f6k8aXQX1vx\nN6ZS8WMQJOHGgfj8mzYcRVP9RijXmbbZYXDMolYFi5HQch4NdaRmBz+CJDvr\nERL2h01H+98qVEF0vV0MgHBDGy4wJm5bBz08NbiqX65REO7ar3hNcLDELStr\ndqRJQ6tgcMo/+GRX3LNC0KaH0wRc/lLfn4OqzbbPSDQZjLWkpboQM9RA7QOq\nMGIui2l9g01hLmCqWyOiYeDE28VwckJqeMY7ZJbfBKCdSwaJFhaxT25lMcOB\n3c5d51zfSzJ3yQn9VECW101IqoHs/AZLwz4Xemrh4KbP64uvFzWC+FSDukth\nplDX\r\n=uYyn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81ccf73ed9ca041abc1f940dcfd47fccbbbdf637", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Yqs8Xql/XlaEnfH6iL7YdA267PJ2Nk2jQPJ5pMbPBYx2388IGqSgmCfXkcHq0tqGTtd+Qsqq+qgydmzOy92esw==", "signatures": [{"sig": "MEYCIQCY8W4etm13X6m61UiXdnAaWZlPsTeTxrDEEAz18JyEJQIhAOeKFRjwLgaEMylOjtZu7nIp59W3qq8i4SvG6Omx4Wlf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCVCRA9TVsSAnZWagAACMwP/RXCIaUK+408SridGOXw\nt8xuAz7Epqow83vnAemP/69TtqbhsxcQUCAVwSRS+chrTnkiV74bZiJZ2NhL\nvpegrkWxB1SHhA9dHlKwFEQzS+caVxCnf85NaROIZCwXwGyaDPVt10sQJqZp\nyl/sByJlV2y5V5AnZJHnLEprK0rvpEIbgK4qf/vGKt63DM/RkhMpyriyMZpJ\n1Bm3qkauX5+KXO2BXH+s3ceYRz1SBEPgnoez2ybeV7ukYZUVAn9QNs1kjeOk\nV/fvHRX+JwIXk+1uVtjCdK72tpFD8hRWyTDhrBW0ZjjzIcebXRYK1f+K1Bh5\nnYcRROWGGOuASo5gIQD1lQssLSqRIX3ZUpuFNCHYguxDxK+uBOrTrzvbudaf\n6UuRnMDzLpuUIuAdXK7ajBVhfv/rBmoXVXHbrT+STnBNpdjs1CreeHItvDrW\nh93DsjoWx3GzfvCQ2W6LYWnSzFKNl9cui79/JoM/uyVrXUCq/w34NSIEOQIs\nMMbdhkoUfG/DiO+KPf5Vd+0PhBaLukVuXhGoErJW2ZVAzp0gWvrIK5TMrFsr\nrksPNW4MM3lv2VGn5Q6EvbcPgyzxP+/dAJQVNMkThO8v7d/MWasF3oQqlTR4\nw9snQTjvSW5Q24ot6I7n7TYjtvNmyudUoLwZl1QgB0FQVD681L5RjZb30xav\n9YWR\r\n=sN20\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17f94b3ff8b3d25fb4ca152353faa53e57292ac8", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-7gDsuAfp+VCO3Fm7Mt5pKhOtr97NlXhyoSTHCypGDLWDY3V/l+d/QY0JHVZvXAdaTDHa4nEniO7ruvpz+KUjEw==", "signatures": [{"sig": "MEUCIQCVXsUJKzzqrEeralap9Ck+8wR0wup1aEccGv171Ur1tAIgICoKU/P9HwNYf6jDZ7xrLvgpeXoWgQUdOSzXgVB0/WE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xJ4CRA9TVsSAnZWagAAIr4QAIUE37x8ByKHZtTgPOAU\nr/JH2InPKDbZRcb9eYo6Vv9qi3nqWdFB9KZr/Le6qYDgqk4CsxvnXenaehvG\niP0tWqiiRru+X6Rfg5DO2tihjbI9rioa62EkKNRfABJ8BDCdvtzKrTtgLMEw\nNVqruY6mWueV20y2uxqtpDYFiPPKfFm0qxDpw+6VRvmBQ88BJ5/rZPKBk2Pz\n8GBLuFArotpWa/3oXk/9+HYWiPC2iqjJ0x5WN5vn5EKsSonVlXrMfuPiFjFp\nGwpbhjcmuV1pj7ERYy+AdDaGQuv3nxfWpF8km5M/CYHZdfzuURvw93QScKuo\nMajYom8l3VehGv8C7R4C8pMDYJgE5aV4hsWBv9sDGzBkKc/ueW1VW5I8W2yJ\nkFA56LLwSL76Dennyn/G62f4uCJFr69nDLPgSRM9Tupw/H3cgK2Kvs5UCoM4\nIcl13KtJNR1cQ9rkPXHB5X17tGObMVahfHSzBJxR91mgBTqyI0057/R+H/0d\nyZ/c4yoKfDoF1ezhzAiIw/iRmdERzWNiSP5LPguh021RRd7Wqjq1gf9loT11\nqCguO5rUQzIrOKjsHl2XWRi3xmMmRHuUOoftdFlPcewNsjcYcjFKVy6/2hht\nNBTjV5jnuKX7OM6tlhaSQ2laVt37rbgZBrICodRam4TtWBr5vCXQbZxuS5tL\nadOp\r\n=CgJF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d7770b872593e634007f1c43c466d4f442c6c13e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-X0lyWLNdi1hpnrpcjtQlgk708JSxB8b0sTeJ9dgV4pttZ9XA+gZPGdytVyWDjtN7hRSv1HJGc/aSBFR0cI6YmA==", "signatures": [{"sig": "MEUCIBEZlUTQaYKLgDeG/4D2ga3N4H5g0WnKKDdZBfqOkmKGAiEA27yCic9Bwrafin2VF0ZhQYgzzGSOmy948BKODVWn/5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8Dy5CRA9TVsSAnZWagAAzpsQAJtY2BpeSpxG0WgwsiBK\nQnbnpm9KDoGNVR7dzKYAyhsWPdljxnBDNSJxnHMkMRcKy2/G+voin595Hf3F\nHIg5N1ApodHSFKSmAgpJE4n0VLeY17ShtHDz7mZbJUbCHf+jd3RwCOQn+D2t\n15MuT7MKEIhSN9GRqf5nbjIsXZwywgF1OSpjreHebySV3bZ8g2ZlvArwWhZ2\nAAAhaXsX6QNiehub8TkIu0QIYt9aaC1grMsOfzYdrJsd+LaeyH4BMhEMwFl8\n+zB3A1jd4flrcjztolNSr/UuZwBoDUjxTh/+sZkNuLMQcNyBSNK/PDGsqb44\nbCDGimOo9olAw9A+8PuQpM94yPOvvhy/sCbqc8mCt8yoQqg6En8njbEo+ENk\nIC3xH+NC7y6kpRlq4WAE/cl5nNsHkDCGlOB2vZPHJF+tlVWn2vHfF6oxNN4e\n4t807e23ZHxpPAMFVddooiX/rD3Nmreaj44PyqeNdTqj+gDSyr1GCMsyFOqo\nkRubWI9lpJWAYSRyQy6/ENhkXSI4nuTZbSTVG3ZDJN6X7xaqse9Pu3+CIhnw\nvhQO+2Wglxck1EOIcIc3fimIIo2uU6t5JdFeMkk2HgDyWwQrMREq0R1IXB78\nutyipsowCEUO+bHUS6mkMtqOHnBsy+wY6hojbD9WKzZf389W6evTQ6MxWzTm\niHGF\r\n=pMLH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ff7d36ba4688e5d84b4eeee57d71ba171a35d480", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-+1q0N/XQNyDJojylb1Fwprr8CYhh0I4TrwJAn+CnSgVcC0zU9oDB1K32uS1J1Fv8upe0swJ+3PVRsn0fnqNMsg==", "signatures": [{"sig": "MEYCIQDj2EjpYQ9dmlMuagoEjwrrJtdU3++hc+gLuNa2ypbxzgIhAPoQLkA5nqH2Oxmbuob42D2DpqkXRkAI2C5dWy9UjMVR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SR5CRA9TVsSAnZWagAA2wsP/15AeEpKz9z6yngDILTN\nXVHyHFo5jmAqmgOX5kIvyyGP25svXupD8x3Fg6jEYmJBwzFRl6n9ZmO+nOAa\nf6lJQLoeg7wxH7JFS7GbwEenxFSchHiPUdHLYjS4m9lxdjPK3FNGyfXAWuZl\nFVckBe0jWiGe7T6iUrIgOJISnrB9ClzUWKeleMRtsg6Uf98QV61fB0mNDk8m\n/duqP1FSwwt91Js1phvfJpO5CBerOuOcYHQJpuc0MJqHCDVt1KMSM5ZHmPmY\ntbb6oe5ghVqF/X9igKlu90s6nd/XHF76g8u55oDtBoaZVD7ESWWMp6mqIv4a\nAOtotaAOt/9L8Vb/Wd7c/XMEXgza1fXzuLfmc+R1NbpP82lnR3AO7AeNcA6Q\nqu2PJEcPBelLEA6/oyS7to+pIOu0QtgO6kkEMIFchVbj9XXP7fc5SP44E/Zh\n8FuI54zmx65tztZdEo0f4/4TAztMgoqqcfkdRPlkNC9N38xh54m13rYj17aS\n8OTSt+0MpOEJopeOCTRax9VdfgJ4Km8U4TdoI08xxNIkMFgfrPQeFlN3So9v\nlkHtygFOmRqzYzAFy96z0kD53lAK8CqX9Csz5Ni224YP2F8lhcuv3BbHGq5d\n/1/BnBIYtXuFhfqGig0ml8b1zt6aqcHUpm+H+WweP39EFO55XLFxAMvkvwIR\n1FxO\r\n=6o6M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "55df3f97e140ebda1488407bb8d2a262b6cc15aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-TnYzId0jyPB8dWK6jg0LgD+GUSVl544bkMnkH6iPBEJh5gJveIHwDNWaWDMPlb3PRSE5wYGxlouAe/0Vp+0NZg==", "signatures": [{"sig": "MEUCIE3Bj26Y3B0YBCRWuh+i53xaMLqouVXUc27KqQCsZMc7AiEAp0IuYlZ0ApUtPkuEFaHcWdv6+jTE1VWg0/ll7nEfCU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DY1CRA9TVsSAnZWagAAkaAP/Rt/SVVRjM9Z8doWRuwH\nqDc8vJql146xeewuNsdokBltOjy14ccbIPaFSUxp4pu5SbMI1w41DSs6l7lZ\n/e9Iv7j4ewpPIaI4JnLdPY2kePfcGj7DLIaRR3bFeAlJE/pufreFGm/rMeGw\n4ilTZ+eKdm8TUKaEFTvWBk234zloCKKDAv+dXeY2hvAoazscgocmG3IvSvu3\nqBvXtg9W7drY2GSTZuQ3hKn3tuvWJFJP6uV9iu+FY36xkdXq8zMtwI8ZjI/O\n3E3B5I6HHW9NUg1UnzX+tOFUQ19zES1rvCtPa9zagxsRRvj21lfBO7rhvFXG\nv779NFx6oEU5PxWYY2ugCOEM0jwA+SoXCntZgSbHrWooB92KY6pAkJTEF2of\nIkQTbrlXkzO+pptNbJwk/oDd1fAZ0DxWo3in9+AQ25UG53cAKV3B14sXAXy9\nWq3LZ1V/D79fOQAaIxs/0Qro8DUu4LauZyDcV44nVNnjAcHKvulMxaGqEoZo\nxTqzzaJjog5Tjn/I14okifaBUqSEGOiL5q9wdOrwwzm5oNth6r3ltjH2w8Wd\n1FM8Bg3rB5H+n/piemggP4asf5c+9KBXfOfDf300hMBeLCC6zGrqaXfWSZrn\nc88/6MH83m+2pe5kml5QflJHPg10l5yoKyNXYiSb1rotmr0cPopD9FBfC5ju\nDFfR\r\n=e2RV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2a20791392ddba35e8995cdad2d559dae20740d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-8d5OOZu3fsk2F5KDlIUXkcYDflu5FpJ+BGEm1LPD87ydF6yQZrvUR9WfzUrQhcwrXQ+X0Fva3PKbX7aTJj/CzA==", "signatures": [{"sig": "MEUCIQDSNT+JW35Qqu4OHBHyU1Zki1oT6jp8W671mxvnLjwjoQIgI63Zcq94n7jRtxdcwrS94vTbEO5DQAMhTX8zRHmJl5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wm2CRA9TVsSAnZWagAA4dEP/2gE2SWlsZvTpPqJIG4x\nUtkdI4j3f/0cU4GADrl1hlL0y3AOJRJW7/Y5YWdFBvF/dNHjh/8aRSq6RGro\n6ThXkB8pny16y/MhNdG58kecWLOrEo9pTthRVouABmZarx5rZG+KQCnTsdN6\nEnTcVLdPeGRuX3EYRE3PZPnt+yuy/+NGYQ321xA0GXR7oC8aGQHuF4DMSxYC\nFVcxd+MIdKHe5fEFCe9ti+CM4O1u7KyQ5My2a6MBmKqecmJjIxNompT/I9GS\nd+vtaW+1PLX5phszCiK7E7Mu+cRIBBmJ7E5faj21zf8OTucHZy1itVO/Kcbw\nMUjZ91b/auYxl2dDzsQY5pwTGUghpqBDp7CeTCZ0VdLlf4bQdHbPct1moi+n\nuBCgLgVc2rozq/fM3RNHYPk4RvqIvBJ9A8/KdDxQ5hs2l/3u5mij7AacME3n\n7b1hD7AZAi4DFnZYQpaaFj4MBZrXOOHi3a7m+bK4KGod/jRJpGHFuqZcWbxM\nK5AnNECFbXivLAHjCNVU315QEos1wB0S+NQ0EV+7+ebPgO5u70rfRdT/AcZM\nJ/tI3fME2G3t2fwBCTfAxyfsxkL1LV2OfC4sTAJnwA4CrxuX9WUnKWwAGuih\nO9K5wXV7bPFlLeEkBaAfYf0domx8xfk0PT5Xx0yhucjD/cYFycu5L0MG8/4V\niETS\r\n=rB7D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4547463bbe573d65460144a14d7f3800f905f6ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-rE7UwsjM+CzuUDBart6TTLSCfJIyiPaZCQkWv+SD0s7cDqndH1hzCB1qqYMUmpWT/f5PDMZ89oH+nCM1W4fl8Q==", "signatures": [{"sig": "MEUCIHe6xtLf6AfMAvIJYry7L9O1W3b/dVcU5p0KQ/mho2YzAiEAgvq4uKWC+ShQ0AUoapx5yGbrcrPTIF/i4mlr1StsfhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTSCRA9TVsSAnZWagAAI+wQAJB4/DJEKd9Ad29n9MAS\n7KkDWwTL2BuDRjnyuh5DvJVNLBabbxgRKtjd5c7VDngTsdtZCyYUzYSg+++z\nCYlPTSEQwgxX4Cn0LL3qds8uQM+a4+EVq2mh/UtdTlpphw7qd7P+YBb8UE0t\ndUDP3t8K0JmIFvAZeiic6DITbEmWlD1CsXuk+GCdNy7NfjB9UqH5VnIbeBM8\n/82Xbzo/hzm5MfA5dRnAAw/99v7TB+png+gQl1F0xKYoUhRpkSG+0wYGRRyS\n1PYMlVb9Z6vjgW3X6ntLKSmVykYWLB15ixnvlk89xtSu2Ox7oJWHES10ejb3\nbZ5GT1NDdFfXtF+JYoIY1GI3Q6j3NKXJmSCI3teHDYQ2IJZsl1TRpMfgZnv4\nBrMFZSIOeYe6gyn6Cad2JPWj1cqiZ49jPKloQmmEsd9pDXA6iuHS0LQxWvY0\ncX7c470z86xSR7liZBeH2EZT5du7B7P6xS5w9+tiTziUfGOwlL6kv6LqNnP3\n1WsBaMd10ZexNujflYdIpUDiNJgmE+5SwlnWqhn3U3ZVRstpzIqbsSJ3DA2O\nI6CRF0q4cTdE2jYbRh1wvKC21T2Lp/pIXLzkKrKq0iqPaoMlZJJ0l+wt/0yL\nOEj+juNW5HgqAixnSqpT35yOiCeRqBGvT4SsUK1aIMEDBcrCgwUY+d2rxceQ\ntXrK\r\n=L45t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1e12adc9066cfa5eee225bb6d25de3ebc852d821", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-OmXXWk+RbuYMzJf9CJi/juyIoCPPOirqE/3cyMATY80/DMtIqPpFAlzok1ldYQiJwBB8xQ2ze8H2Z2tqe4fvWA==", "signatures": [{"sig": "MEUCIAWpIPA2Vdj7xzNxm70lLvRfUplXaxwbc+goo/vWcFVbAiEA07Z5kdWJ7cwYaYtiEkderVCNOuUlvODqS1M0/F38rX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/mZCRA9TVsSAnZWagAAIhoP/RMe++O0mtTs2Z0bT5d2\n6PFGxl7JONNTZ0OPNb3RNDM9opwmlFIc1utLIG3WBfrVsk2BDgDTddc+UNFc\nG9DkfcCCQ3ZaaX5SkvPkYH2MKyfFyGNJ1WKyJWua9ayaPZKDdsNNxCXdnd4I\nxrMoOtxKI7GR1H68Eap9DHyGTToWbIl+jwoJIF8JyFqXWqtwzUSzFRSuAVnn\n8jxg7PnqjI0/hiLO2zaiHcUGWRFE/vNHa8X6bFIPHGv8FrMKdEbeA3Y/rIlF\nFmYswrR61V6CCjr6IX+cm/qDt692fs764vZ8yzI30OMGEfW8cJQdEdtbTBv0\nny8HZfuJQn0Z4ff86hFHX6o5b2LPsWWsbEjihK3Q8qOdnzdCdq0Ghf329ukA\nYzMYoQtd3Ii19CuD9OaspcPyIyHe+8gmL/fdurWt9pdSiNw0NFVye+SsEXwL\nAzsIX97RryfYMFIHWxseRCa1DtCnyayX/CK6bHoHY74PNs0Uy92UG5p/K82t\n9Ue3f0GpvLWXu2iLdijI4zKQ+IUSqhQHrPm8mtks6+EGGr+XH+FMnvjj3BKr\ni/T1jKjYcOV/YJrTD8UTlnCL6aFgI+7+c26Axr+FdJGDcATinlWGXpfa00/C\nXFr30R7gskzUiGy5d3ejH2T5vHn6wXzW2yozoXRUBG5EtEVFK1rKbccXV+qJ\nkNbD\r\n=nafm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "362cb078d95ce1b7ae996a8809d0a1d5d7d6e712", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-I7PV7KgrVQgsq/hmvy3f0osUcSattR4TP0QBVVUn0W7k/b6H9qa9KLeW6imlTnOakz2dvXHDMdq9DRZcJgE1fg==", "signatures": [{"sig": "MEUCIDGNdN/YE8g7cc8c3ou8+e5kB5pTzTgURfBkb5ZRsnoyAiEAnwNtWrxNLhvlPc6EbRZ/MkiICS94opm8W7pyk7alozM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBGzCRA9TVsSAnZWagAAV2UQAJVqmkZsrljVoaAazDOS\nkdxBlGxpTH7IHRgMKc7ce35NSPptX6QZRHIk8TML8jM9Klc3T5CHv4kpGxJa\ndKObaM1aS4rGzDjpzaSt7nr32rxsiz4Ed1+k/Ej8K6JV/GyFdpZ2+9rI2Yhz\nbCliHGwsAxUAVL4bGdmSOFisNrg67BxU+NjxsRiT9au1IeLlP96tp2zJTDs1\n02pB8IdCp+SjScRm2sqd0HZCVc3q9U7yOduIgHLrXF7NcmwsMY/sKaqIyHPh\nseHFCkN7f2lvUgVCzuuOw5YEBds2AAhZ1MK6eXiWHEj4E5z4kIi4cco1sP3H\nu5xsqzxc1M4NzAYF0BKpUufvy4Z1EbTZSBTSjAy8VolPw6+6x1wJHv7bXolR\noCUCuWa08luHWDp/q8d/QFKIuyMrB5wD3RTZKYuruee034Yvi571hYvvuQrd\nM8ok3haNxQ9r6lOwi3Ha9vRnIpGNg4WSfv1rSWBwGSMrUYi/R8VtZ4e8OiDE\nrIf03jbP2fk7w3uqVuluKcmme2QtbgLbQTFgiQ8rCPQbgz6iOUESJDjV4Mey\n1uLhW6VSibliSZzbQTfIPZGsmEDsQhHHrps8IdeXiMwyNQsXGyGjLV0uiOYk\nbnzBhw4PlSKoU91nFxhqdksmU4+hQvLUeqznCU2VNImdeYtnpHxsTxpWAuBy\npMYI\r\n=BDJP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "35a8b1da2f617d271698e3bf34e5b34f08e21cf7", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-wxqSrWvkk3uQ0KZSzaJ5DQBytBOwvw4/HIaSv26FCVK2BPUWik0yJnHlh/MkYsiK9DZB3rAXkI+ySF+S+Hw3mg==", "signatures": [{"sig": "MEUCIEMJkhCPyeQ2UuaYJvyxcSqmaiD+Ga5aH9eRwrJdC+2cAiEAzrr58vdLC/Rx+2FO4VXifXWMtmgzX5SPwPSQncVhTRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBW6CRA9TVsSAnZWagAAxuEP/1MHx0+oKmFoe5Bqp55m\nzTG5JdHbhHtSgGWeFzQ9fk2oArTlkB76sLUn52sco6K6rWGunA3xO9VXDYgT\nV8WlGbI8rpgi7/220rRqBy+B3pbA20XgxkF8p4rN5zIRMPoCfGe2q9MZvHrp\n4xneJWR71c9x57X67aQt3pT0Ynuuc4gF9pyFNKLQE5Du/upRayic0UeTyQ/w\nWbFSIYVsdA16FHRb0c2bYlP+UNfDGE/IkkasG+CQgSG/UbuDEo3SkSEYBfvD\nPsuF3kPiWQZwlPomtzhAZkoo3cgUmL5mhyB+03XCPd6WSOcZ2a18G+Id8Pvn\nuKQBGj5JZA26B9Qbbj2kIRBYM3rYPtjdgOVd/1vPUw4Zf6lOmvG1UXCDhTxQ\n3eY/5qCfvakCwx1AuZJsHT4+hS4r20l40W5zFpLhDpqXm0NTN7qUyIGI8vN8\nK+gBcJc2HH9U2g+6A4zKeFal0yu15PlFr6DLfKtKpPTwDbULPC02h4+aPzNb\nLLXMK/R3FbxnJIv8GZsV7u6Jes9ZkFqYCfAdXCX/Vzw5nIxSHluf4l6OjDZr\nlE9YDXbF1B7WZBE8p5RfXFg1VzCGbd2b9gO+weUzAYJH5Wu+B/IzFE26BnFL\nFZH22S7b5sb+v1FhvK5I/p4saHZmpqWogBQdzUHQUKvC7tuTfvuy5VgLolST\na6wr\r\n=RUDx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "089fd736286099eedd29babeb7b6bf08647cbbd3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-/6r+w3n2ZPR+5tJPdP0KJCWxKZzp4g8C01/IOdUw7obaf1NBuBizyuRuyqKg8kH/1RohtYgPASHXmNoykCLNMw==", "signatures": [{"sig": "MEUCIQD9uOFZy77OzLYn4HD5zaapbL3DNtrRztTGdePGFTBDmwIgaYbq8E25QlB7+G6xn2MKVz2uPSMU0U7rA1J4oGs3VZs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlkuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoULQ/9Hj9Twiq0D8w3fwxnyWPX+PNPRryRTq9Duva6OQl7PUPds1SP\r\nwiurKV5yAzxNjeuk09b3zsXQZ5IPaPQJ9H+m4iDDvGnusItgzPZWFQBjrz86\r\nvvvmWVDIPcHICKh4FE5I20VHhZ5bx4A53jou3j+Y4c/6rIXGu9shmFRGr2lF\r\nWrL09nX9X7BrJCyGnwRhGiIyTil/EGoBQdyMC66HZEbOj85kyZ2W6ZlEtfT8\r\nqrPb49h5KxuB7LKJHHHWzxmfrixDrIaPLQoNkFiDnOGVjcfeAJt2/PPsE97X\r\nLwiAN9iLaL4yqJ/+ACtB1dQ0oinMDX6jpB7NqP0d7SLLTQzFgZXbLu7GAT+C\r\nDT6KWM2WcHt+mH8P60ve53n+H3Xl9D5o6CRQc9uxABy5HPP96LK2FzsYSh3J\r\n+RoaOj45BITZgNuvNMTOOR73nkuzvjZf0j+gBxG2t4UEHfZccI2iLCRbgDQi\r\nVJckYhehoBeJcP+KqRhOATJtBifkYR3OEkBQq1rSKW09WIo1WFg9ZQxDOAkd\r\n1f8P9EPMdEl/4yWxJgmoYYMJSgMM5rjgEljVApwcngn6fQIiLYAFYeeo3Dfd\r\n0lexHzzEU90ZuafOAO7d8AwHuAOHjLE4+/A+SjJY8JJyqnco/jZygyQ+bAim\r\nQkKetfrUElMc+cBJxjp+FYOCNz/UMrUMoyk=\r\n=1ONU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "082debaba4ac86bd096965929202f9f6e1fd0d16", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-2Xifh6TY5l0SzQdT04nKNoAENm6vombpvaup/EdKDmx3/4PpQ3keP8q7rFQXo8y26Rbg+3OekP+KpUrIG0n6FA==", "signatures": [{"sig": "MEUCIQCuU/arnIF43QT4GUsendF6sFMPDP74l7/JB/uT1lry1gIgUXTSITe8VVtHKn8UT5/b/4xpwHWnEOhuoTjPQgHF5+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRUQ//bJtpCbG1Zi9WcqSGRqGcUQzLeYHA7gQRHW5OncOGDrogT4s1\r\neAgK40QtSKLWH/p5jGMo+44UudNfNVj6LoCBEzWKV0ICZnQSipgCvZKrGwNS\r\nkub1Ty4bSeUXigwob1pFF5niJb2LBKIgXqJFtqH0xylBhV+hBYRYXC0CxxIE\r\nUE5oLn5JnEEI/A+aB65N9d5bhX9VTO3JDXZGy7i2ilLX46oOn8M0DPtEHYoc\r\nDvZO7Z1tdlmX7nFM4ZTguym39rPn8748HL8sxMfN9mbkSKLtWWyxG8xdbLI/\r\nemviPXvenRYa1oN6sfVUOb4RQid34Eawiw1A7lgP6wLvlY0wt54ZhU1799zK\r\neYmWgEfGezU29A8ztIDpICLi9Loret51/XiX0mR6zBPt9nhqR5PZTylByM7M\r\nv6C4I/nyRD2IBi7pqmOo0NmsV/9Y/x5s1XrZtdOdP/tmcoTYCGDamthhPCUQ\r\nzHpJ10Hcw7e2o1bbJF2LSjzrkTKSH9t1rUz83aUbs8manaz6u7hxMd56lqes\r\nTH/8SfCac2zfYe3V7DID6hJoSajtxOh4uOLNtiyApCrpGExQVNiAaVwlQK7x\r\nMLvJsUWyJxMncm83EN5kqDMjokAX9uaYJrmMooWh2iKs5gnTBw7RwbsJ32ua\r\nBoVoxun3IB4vHAH2+sF5mF+8M8U0pfsw1sM=\r\n=NbFl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "00ee6a8643e62e19f68ab53d18390e8dbc78ccf1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-+K0HbqfI6hyyX7y38YIhThWzmFSJ/CrnmaxYzhifHVMPUeC+I350n/9uT4wKQSBolP9x1GHvNpjy1Al6i5yMsg==", "signatures": [{"sig": "MEUCIQDARaRE3RqRnmmwAclvuxJ/v/EGHsz7Qb56Wh5gqsokqAIgeCJZdvqQolhO/nShT7LefwKen/PTZ9wbKO5oezHuoGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo82g/+M936AckkpRIn0nesOtQjOZYtS55HS8p2uBsfsrxlNRhX6nXK\r\nN6upp36eXV4DRGiaku4opwFBZbHuxfOXQKNRUAiShcnNMZjVoEOJZ6XkUCt8\r\nexSnSaPgww7T2rQ/Wy/ylY5SwTnYnGf+y/tOz6LW/knomVPn7ZHfsvVG4vxM\r\nYFNyao60jawaJRvMyL+O+VlkgvlcSvzAaFEWbVyiLMiIxZGzWvFAPKR13+t1\r\nb8qXMKu/i9v/311nkszZoMOiYdFw2ud8/JIa1I2sWbQRlmBrJ91uQNnClzsy\r\n+q/XQIhhnOoe99FU1j8y8pd1GPXPMEtlOAlO5El/vqJv/j2o7kXpmKsNcjkq\r\nYJtHqqy/CggAl9T/ENKHRPMRma5IvlOkD30r2SP/DzaP15LkyzDc+OisQN4f\r\nPA2nNzq9fsKIBtOQf5IpkGg6Yj7cdeC9+h21m0/vUfUtSW8eoB6lTJSS3aGH\r\nJ07gyaDseOoXyV4Z2mCFXGfMRb6iQMxe1z6kEIuYCJRdSeRYc9uswmXRrCDu\r\nd6+AEWi65k3ugKep20W/11UW8lIQEDcBpwmNqEO71kMHtOWScpLbocKGYpkw\r\nDzi1wjT4ZcMv1MBftSbeq868PC+DSbmOvzV5dgjwONf+UhSBtWmOnqT6sm/G\r\nPRMYEn9csVTRZx46DPlWeT36sv99N4V/g9U=\r\n=F1Dm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad382efb6605504f2cbfa95a6c873d8bc380f0ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-CWS+Cp+QitjfmML+UtYgSEuuXgAasFkzccWoV0BY1JKYZeGu95yH3aj7tDAa/F2YYdtko0gK8KUfnZGTg/u9CQ==", "signatures": [{"sig": "MEUCIAYiN/S1vsh14f78777Im+IEQA+WZu3MZfD4WvHGTwlbAiEA97tgfsIVQP7M6ncPfsoTjFW1lwElpjuQuzv7zGQIu38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkypACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoiiw//fZz65Q1cAiUS91xOcGxHIPSwOY6pdx4WCfC/7ZPAqPBj9NOp\r\nsRwguKc6t1tXgkV0s3SpTHEcwVZXwfAbzM2KJQ2ARcrnI6k9DvxU62gB6k3p\r\nfNoTVeJlOdKA3LPiZPz4Dlseh/CPR8jpI7lrjvWzbwTUR0deYtY9cOieQAt7\r\nZkoSkvqzf/W5vTqpIDlrAatdUPXX8dGNKP4hZ4EaCr2duxN+eYwBl2dlB1eG\r\njTEP/JS7ax170Lj/tatTg8Lx0JLgSwSx+7SY5IMUQFFtig0KNDDYQ6HOqD0j\r\n3AGlxPLklgRkdC5UIdrO5dgu48qubVbvrGY7gQcFZwiMPFb+AH0kvsoUQshh\r\nrzylTNarLGndIgE84tR9wSBD0QY11q/2lEqzaJgUhx9KsSGFphFeub5saTw7\r\nRXFbog40gyWnq44r9RjUUVkbjoEVM02HyiudCBIJb5gti1eClUK5AEGpIB7s\r\nb8ztkWKhOMPBvpKB1xQYcH7cgvQrqxem9goOb628QT/6qLF11C08u+iXb/uQ\r\nCdEhtPjx0+oyLPcKOzJStOiae13Xy9wBmXf7HfizaRBPiK1H9DsVIWamdPcN\r\nD3lRE0gtQ86R/wm68ckxYG0bIQuQ49qiSOEZgDUFgOIqoIUeCwTznee6Xu4C\r\n7D8ufblkVooGbRur4gnio5gxwJloZjYfKI8=\r\n=P879\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1f6be4e27eac74a92d75ac70770d48e804d229ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-z1BgQCv7NWTY0vFnRtX/L96ED3cWH9o5LQNpC5pgnGOo3XFImpgOyiF4QbtZZ33FSp3WXhmE1rWiihBJgUSHdA==", "signatures": [{"sig": "MEUCIQDAIgXo/TS97QcDU+bF731vs9qpWGGl+IEHDzvUmSVxyQIgQEMY6mOMQt/8Igg0tmr8jyQRJCyEqpPTwx6xOpvt5WY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH4w/+PpSUw4GZULePsZY/JF4Ld8jFy8qdbuXyQNHxAB7GARHAxcZz\r\nSvpRffNJbSFSDHXptqk4rVr3GsjGXm1UyxmdB4Dr4x1W+pheSHRreyartICk\r\nbTs5VSBX/zP4aR52HaYFbWAQrpzBwrNq9ZSYB27X0rRRdkvLTYVumTDQpHM0\r\n2IbU5OaHG9UlVe6PHA3HJ5MvUQkU+HyKqUvWNhR8wgy4sRQZjqTQa+6l/RhS\r\neiqNHtKZzPFBECZ6OrXgZXieLUcTwIFJxPUnEIflbZMnP+yxy2w1iJBO67bP\r\nQlzVA7AFi8U1DrL0Uu/mwyATtAdNXMGK5wA+tmrghQEPkNXerljJxbWB5DUo\r\npwA51aMMh98ATq2CXCoo7S52tkFvZTaw5B4iXJngCHqfHw7OgeMMH75K2PbC\r\nxZdyvND1ekBOvI/9SHTgvatYV8RPR3wozpaf8ojFOqvltIz1mW0L9yhAgXnN\r\nYGDOXFBAbqz8j9s6Gsy0iV6Jy9cD7IciXbLGPwLMkhNea6FN/hBPHw0JksCA\r\nw0TcYDbkJwBn5A5oLu2tME5UL+tJ+1KHW0ahaUC6HBVoNCJkUV2MCgZa1uY8\r\nqkOGIbXrUL6OwlFvd8j4ef96M1DBSV5HTP7Xzt+tG+me9YxsfOf0PZSe6Qi0\r\nohX95aQc7tLK/7dbxbiTGEwDcx74bHc9Tl8=\r\n=ntn9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "888c4ad4ea1cdf1404af74cfab54f46839b9887a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-lxd5J/L6ieXKQH6mFhx6HUEkpxQ3KBDa3JZMZ9hcJzseNrVBjP76iq90u+BleOOACedAjilSoB+pvtsDr7VP1Q==", "signatures": [{"sig": "MEUCIQD+1oKbSIXnx6tbI45BMsy8R86JB/hnNmSoOne5k1PznwIgKaOswWq/rojWdtqLtJgHE2FYiN53TnOtBMOfVXjSmP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpC/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrA8w/8CfL/1HsPWAOiuUr+ajlByiYQel3Yj9IB5LibVXjODbOvFW5o\r\nsvnUU4vDVXf3K/C0BpuSOFDTYCMGsFNug7Z0lVWBoPZYZwUHu5ihb/Q2uJUa\r\n9yYnbe4PS5WhqUiPwUo+ygwmGL/6Hu59RI5xhopi9/GJikP0OAL+Sy+avMo6\r\n94CIGi56KkKyQlmjuFqO96UC7D8RSykDC3CGoKVtQVUmc44midAkSHNdjrdp\r\nGroroy6rielkGhO8QJG06k0KNzM1k6cFSitnfmtn3DI+eyhqiWV2qqveB6Mk\r\nQyiGgfNkH/4w7wJ/3+wLZxqn6VFtsYstZBJVHP/11QaxMVb8rI0afdeAaatc\r\nhQk7mKAF69thYnpsN8CMZiLWcc20yNF3s2o2jdB4zrLpFd/0zpwRCZh9gjSq\r\nl+kblb8rGzm+KJtgozOTmcUSiF/9NQfniuFUDzUGAcCVFONjX9JognnaRqSZ\r\nJx5T5+J5cNTFIv7RVx3NSPF3IwmixGSGeA40qgejf2rz5ZLACVzU0VFQdH5P\r\nwjxxxZ/WYwPr5IgBiLDo/Dt/HBGsYcnRC/hHlB9auBMO0OG6D+2f3Bv7AdrE\r\nOBpoIC8LUFBHuqevzFNd6YctRrG8b/QhNTBKxCrFIkRnjPTklv3rHS7fVxXK\r\nm4drYJ+YncDfqCl4mbDLJldZcVDaMPXJhxk=\r\n=nqsO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0982ad67b20da23de399a1540bf093fee6db0aac", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-lN9mVk5wg7EUDA6PamNMB8vbAb435SQVvGM6A3Y5qgQhOBRzHh+oa6SQcrLBiDrzVTFwgQLREzRvSFKaZd3tLQ==", "signatures": [{"sig": "MEQCIGesM9BuYNMgFFEziSoAYeDm1L5HArFXAtENyZC7C9oJAiA6BlHNSALpPnS6bCKVVm9w/ZhU0feDW+0Tv0pXTqgE3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC7Q//WxLcywPY6W75gJIi7t1t8BtyO76Ae4ogNZzCy6+32na4MRpN\r\nrDbfF1T2pqoiUUs+Eme7xMx9fmpTUS7Fm3qfR4IPPvZvflvhGLJA4lpqic7E\r\nBPug2hQ0+UxD8zFPuIteozpGPDnlCsnjLCNZ1RW2ll+smFHNbF5DHbrFExYj\r\no4QEDmxfsvhbzPsm2vNVKAJDMpxQDkXbL8j4lnBGMKPF0BKeo7Hw3KAfQ97A\r\ngvDURq6RnUqkGkWyeZg/4+/lDtMqsnecu+KU6m1ZW5XrxASbP3xZeESBiPNn\r\nwPVhUv7rjZ4RTP+trb/yOMBmMmEYGYcSb4tsEq/S9mVQmU96FDXLRuNihY3J\r\nEceOEJJnOeJRMaLITAPsTIavOda628TGunXtG5XxdOEINlQjgnHmgKMm8AiL\r\nQF78t3R1K/MWJW3AgTcWvqmRM3k5trMpryZ8+RNwf2AvaltUBUvFTJ4o+H21\r\n6Y38jIqIjmjPCViBMAo/hcq6ri4u3IrsuA/CXyJR40He1JcGhGH+7RxQOVV/\r\nYN/G6UC6iBS1HUQHQSMuXaGeKfb8NVJRT8rdpoQxj0MjRgOSN7MYB3TRecXQ\r\nMCZfLDMUMmqXZpbs6eU6kaKvpaJ1D6WWoMUGfjI4W85nZmeyI+Y4Il3Yattv\r\n15VbnqZMa+2E7u8JecD865Z26YPA6X9FDrM=\r\n=/Anm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "224100d706ca8f705e9e42d5e46132e2b481df32", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-+7HgmoPNAVdZB0h7pWhDpjHOFjw2wqMFfU2RvPPKARyj3UhSphoApjFzuWEd3pSUGEXDILPqEkSH/YfaJTs5wg==", "signatures": [{"sig": "MEQCICWXTbU8kDohl+Xt574k30JIQlPlkqLHAphU3OAE9PstAiAIh1xkbhO2BNKGY1/SnKn6Su6baOqhJ364yH9RpFgOJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjvBAAgvhgySYv1NTkZA2izzqH4jw8MOai/jbLwPUU4vb13BTCI+I2\r\n/siQPbeiuNvkZBlfcyOA3r/2Lt41HYDPtHfrGWiTgTvF9Z6Uria2+7K7DBSq\r\neXHCRVtiFAgaL5P1HMHOYIkxw/JPPcjOg3T5PuyGKDH1WjQUimmgumZ2ZC3P\r\ni9IyThkd8punhA/hb6RGzjIPzZqHgkL/KTPttmpmue7sNaZyg31tX9v/vz0T\r\nL2Ux+sNTR6GsxmjDWSaJM2PwTCQmAhovo6FtwiSDGq0XNGgknXk3p3H3VxgM\r\n5tsF9SlHbwn52yaPqUOkBz5DM1/rK7+N16HoHkfhxptXyKsGlc5+sAPCo6o/\r\n5gBmmVmpBjFZb3/pTNRHQd+3vIPoNrcpenEiZ0/9yihA8puzENzYGN/T0gXC\r\nJ9p67ruAlyFY1kTSlUvZ+aJ3EghfLQAZhwfOJpwFOXvcqpKrp5lTzEmX8u9V\r\n01rrKGhQxe+PjmwBqGOGcS0i1fHQpIjgIhFkNtOh4nEHcfRUWmsNwNN+vi7f\r\nGli3G+eBzk8OL66Nb4bGZebr09mCOhQB09jQsbkbOoLvX1EXV0rhZdi7IDcd\r\nim/ch85fIPraFQyeQir7u8GV+bcACqRGzx/g/map38IQ32d0AVCP27kdx/m4\r\n35KKtTlJfiUPBxJcxQJQ/txfB/wiwCI5bVw=\r\n=YVjr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-avatar", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c9fc63a04d4f60ec738c34135fc1c30e940c120b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-jb/gv9dbcxLR0lbdIinAvu7Oju0L1a6Uh9OEJ88VGVLfhXgbyi3wG6B0PJc5pbz8wN8YEOxzLCQRmy5mIRlLiQ==", "signatures": [{"sig": "MEUCIDXVfUGs3mboeSXb4tucQjF4meW49YrMt/2McPdh+KGvAiEA5DQtSXugPUa2hiwGbeKMaF/p1KLhUilQEG54MT690vs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQsw//eHUjmZdeP3xc0Kl3Vw5+arlaQor/8OUGW44EhmASoS1IShRF\r\nX2o0Rue/03Cfk3hn6OtcdQNt9vlF/qu89/R0jjfDwziSO1q7ClhbAYtzLM5y\r\n8TBLUUXUQ/bkN9dFfIigf6U/gJu+VqJAZfTskgd8/4EKOdWkl8lyVTItFcgD\r\nKUhR1ZtH8SFHWedOQKAeDHIuPXldFY36k4OhJJU5H0D9MFIcoNlPX8HcVLqa\r\n8w49acTn3UKTnJHycm/h0ghS6cVSaLYFIR9pW6ykwSV7TqblIXDF+xzHpw0h\r\nm8PHteAsVf5oCuYDcwxF9IbOmK+j/X2oEWPCwRpUfzfeJVz7hfHDscl3/RHh\r\nTsYUC77aMK9AIhyNP1HSnmH5ZsJT3SBGtjXiA3zkXpMQryTvpcYRawzFi+f2\r\nJUWFeN65Qbw4mnUyv1TpDLhT/lW0ODjsshuQl9liT4cNE8/c9QYF+Tv9kevy\r\n++Q35ROuIUPhsqIkJCSU/kuduWKiadN/KK9rwgG3U+OMndpHtZpCrt0vI9tY\r\nTrBsvziPRe2+NJdSGe0Oh8gToYEUDVfwv3vQ+hDJHxYSXPYG3SfIVxNfkqiM\r\nl+oelYHRu9df1jJaPBwz3HaNANceaorMUx8QfTEQiD8g+OsoYN/9j3/Xhfuh\r\nFPCClq7JMHV797wcVzrj1srxVOs2MUgB3uI=\r\n=xeN8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-avatar", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "36210e629e0eecde44027195c82d2a4101cbd7e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-RTs3KQTToVKJHHsn/E+vC2MyP0LnA6uMdcD7thwaCJEbI6NAvf56Y+RNJPSsGXz1o2LkD7Iy5SogsrzBwXQk6A==", "signatures": [{"sig": "MEUCIErfhq46EBO0cmRJnJx1F3RJx6IsSYp1xE37ifEDualfAiEA6/v9izeUn5/u5bvXaUgvTrGU/5ScCAL/N9/g7apSoIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24575, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8j8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYxQ/9GXP1MOsBx+QTWfKbcpstO9T+De5DKc6q5seIOmRl6kTl3GjQ\r\ntzGiRQUYq7W75QVBtkef42WINgL1gZWwHhN8k8djBtx2BqmHgkYsxsQVbeA0\r\nH1jbJSSeYAHlSZUdtzZqld8YLQFlFcEQvK8eHS5eqot5IAXJMu2fb9JveOpl\r\nj0RvNV3T7V9JX1tOrR+SlHcR0g+vI5C4mTWlkqcnT3a75kOibEiwsW+A7Xgx\r\npNkO8mhCZhWi56HJ1L4zllDlQHhY+CKC3qzmPSSQLyPCBKsMOz7zEjArAL1W\r\nyABE/35/LUllg6zisUh+REm4EjjzN0fGOSLO/0rIwFwRK/LQYUkfDdOdjZ2g\r\nDzuExK9fIgnVXtbAx9bJXRDdsLZyoGGYJUOt1szYgh19AT4DYJ2AdSrWMCnV\r\n9N2IiARJmEraYPKEnIXSgm3DkfU+IzWBEnzqTD2gqdjYWWx5RcYwk/0S21HC\r\nECQPP3kpf1LvUoueBfEKScqiZeepq9EMLUSdGXiYBT7yij7ilEqela6fqLie\r\nKHTcxlq0yKmAFTYWrJuWrk1T/HrPgvQcN16KTyheF6KLs9V5idS3+bxiEtiS\r\naA6xPYIuCMbjK43lIDe7KPZLH4WOCsu3RslzWNh3rEBlnY4AmR8NJH4kyLQE\r\nw6D2mguD8cQCIwTC6v2Xg1mQBgYqkeTc49A=\r\n=E9HF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-use-callback-ref": "0.1.1-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3488dd89b0dc54ab35faacc298d1352460a15b1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4QB5xYCeYe8VGoZVCb40Z4Z3T04Im++qBXoqOkFFulqC9evnruvGt+wy4X+smXLtrYJOXwAc4NVnAIJK9HsXmA==", "signatures": [{"sig": "MEUCIQD7aQIdziOLEzoQ22o0xCBtYGqv+C8B1zeCg7xI3KCuJAIgWvnrzwEOSyGxy3L3Ui9gytNoXB9U3doqzLAYfXVlsjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6CxAAjdC1j9AqKZp9ZWjH6YgSizxgsw9nrTDneBRAEu8A2ZaL5WC9\r\nTGshLW+lMS6WbcrALTUeTyqINcaI253U/f3Tsz9AchEpnC1pXm9XdlEL6HEW\r\nKrZ1l+QENB+oNO/KlNHXlNcNhr4As4EmYpyHTe8GenMFCqHa1sIStj1rzwBe\r\nUl/sZUm7h2v8aFKu52UHJUSEo6Cx5NSOoEK0dKcEarY6h1Uw0tuCOBFiGNOL\r\nHTozrMazYmxJIvstTy18FOrVeEfUHGCm4IdQhuQsqc9X44ltkrjrqqLCN4Ia\r\nPb+NiMESDyIp4PA8vd1Pu0GPU4yc9nNTjIdTc0LPw6WE4MGTzdNrohq2EMVw\r\npysLOd4zblITqOTxBAcBFGChNyEWbTZaAij0M23SanphKody/6GNqTSziziM\r\nQAOzrVl3FYDGqT2UCBZ9mn228Y/oa9jGK+muFeYxX4TxJFQ9gsL5KjwreDYX\r\nrgQcTYvfPaJCTEi37u91UaEQYCLfdEP9nopGgOfuaUkXZg4dIAtB3EpMoGBe\r\nXTeV2O/D4TOS4IJEUVs6sSTB35h7jWGRa8V8n4xWrcyAXNtvF1sQ/zUIsc/6\r\nPuvtfZnr0Pm08Kfgz7HlbkqQic4/wIFZC5ACgD1Np270KA/RQSbZdBtLMX49\r\n4e6ozn28WkDOTFxx5KUZUZSqUBkKZl3KxuM=\r\n=KuQr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-use-callback-ref": "0.1.1-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1ff2dc32883aa01adba4f13d0e05a9efba019f47", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-epFtfaAHKz83JipZw8Hzecl/lsqwxm44FcdLI7DMbRMauYr6kxdJdmLvxy6QYvSJNYNpjd9FvJBRG6JdlStEvg==", "signatures": [{"sig": "MEYCIQD9Is6gJfWFbro5ToDZDnokUBqvJp/GmprXsbxMuarscgIhAMyMxYWAFHOKo/jwMwTCKiaeKR0rIyae8/YcLmWg+n8l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLeQ//eZ483ffqwaTq7tO/LZu3y4dZo8aYP7eNDmbobcCsj8PSr5+6\r\neiEQ8ZSz+BGFBSfMLWHFGpvkgL6osmbdN/3WwIbiG3bZWO89MWsPZN3L4w28\r\nVmVvElbc5S45FP6jn44zgeh6OY/riPGttXyLdteIG8OWfPcrnDnUfEtH4sqF\r\nVFmejtDruilp+fuBOQmVJpvinfXf7HWVNN+cuNJAEvJKFV3zdcMKWLR1h6MN\r\nlDCN8vX7g7urGt46cwQXonkYbieW1AqTClHi+Iq5xvXtRVkPoyLcftDNSrSY\r\n8QPkLpSd8qfIXUar8zOBJvIDR1mlcSyja05dZN0GnBkJC8WxPRRUWaJYAdMb\r\n3LS/r2vaKnxKXs3Wyn+A+Ld06KHnrXN/1mRdXP57LFeBBpIUBnQYCY62L3Xu\r\nLs1rlOegqwFHQj/MtfDs60I63Q2+8JIX+JzU4i1sBfbeazzP/ugr69NdpOhO\r\ngSRG1AfuXXxQP+oNkDq08o32199/kR56eKjqEdAMWgQDYIUezr1lTEEQpfvQ\r\nf75VIeY35yl0skg8KjTDAwQSaHt0RAiv+Td8kselmx/JcWSywS6YLgBobGcH\r\nFnipHWBD9oTnFeWGKORE3bBerfSnqwldC5VyZGe/2ofUcbeEEdvIciTg/yCx\r\n4WxjaZgDdrMRWgs/6xW7l4VUiPEhaeC60us=\r\n=E1zP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-use-callback-ref": "0.1.1-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4c12322e530c6bbc1605944df91f4b7cd62c0f6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-TP8BHnmg3P+x5VCAh45CNle5OMfDqXAg6lCtYQeZbzdnjMfQDlzE9CXFDqQE21zE9EgYjCPPaG8vqQA/fdOdmQ==", "signatures": [{"sig": "MEQCIHeBhsoJC6i4WN6eDCUqQJ3cE8GHxZeAfBGmZ4b4ACJMAiA+pwymABF4YsxE6J/hb1mVsMaaH82EpKvqgu3uUot03A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8dg/+KsboHVqqsbERIuLHAARtdl7DFumqM+BZNVR1cE7y1eL7p6Nv\r\nvhqUEmHYF/unO4cRfAaNvfHaLmCfRnXiQPhhc4i0OmQ2ohNemr2G8kut5C2S\r\nw8NiIRYgnDNUU6Mm5Z4UZCMjRWNFNOu5oO6FiC9Fzq2brWALdQaph0Ioh6l+\r\neT/6ygkkyuIiPGWVW/qsPhbGxrQ3QPP6DtEZOyf/uoZtCospzbnZEfuVkksS\r\n/McE161Q1Ba0d39Jl+bJ9FmmF7vLHfvFdR43RPdgWdzrn4H40SVQ/LZI4IoV\r\nQBlWah/l5OpDiqNoRTS23yAyEj2Zj7mJPOHvdEp/Rogx5dm5yetmk+DYd0AB\r\nxOCl4oARCciZ6lHIpW1zrJR/AProoXOFr4OKMudhmipwow3rTuDiPKYWS3GF\r\nqj68hLJSG4kjJkFr8Sx0kPIXs176ZBTCoWFpTu2/NbHM7/huyvC6/rsZm6eI\r\nDf6ti4NmNl1W56j3fdwPTCc5hlSkDSZy0iiCbwRK11USqLuA9P4qG0iieB67\r\njNVtuuhctmOHWZilMs0u8OKuWo24EQqN30St6oc+M4G1mAQNYw3Jv/87wdqv\r\nhlxWkyJJlKf980EIdTCoTE4gQc12Pdi/eDY567mltfW6OeeOLqCldOSnNeJy\r\noaqh65XHwQls0fLRb7oYEQLc7MtmvCTMkJw=\r\n=RmFC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-use-callback-ref": "0.1.1-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "395cd7838926308973bf704243722344bf75666c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VOqj5TUIn8TYJffVHGPu5HvpnlK7+UmDnqZw9ksb5NHkFm+NOnt9kQ2oJESzVTRgmS3Fqfrtfm+M5kZtEZHyAg==", "signatures": [{"sig": "MEUCICa/LFa+Zk0CykT0FX1en8nma6IgWcIrC5vdvBIS4QRhAiEA5QriZbqwZIJReOx4MAgTUobGfwJTUbBu9rmL+elOcBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdhA//VGeVQB5lontkdr8peXz8ckQXbetbjZg9bzt3cAi+wNGN3y4f\r\nXji/UxylU6y2ZpxaJ8TN7fOh9SLkMhSj/uduXAia6L8TO2Ahc6LNjvIUU6ZQ\r\nqAdMAdTZaI5BfyaryT5tt8+YEuEvkhari/gbOuNOIYSzAZtP9s51GC+T0MDA\r\nQLBxPNF9q7WfuruIauTC1doKDm1u1RhCulCqqiM5df2KPXAO+eUw9vyUO01S\r\ngQHD5Jvuzmn3JGWahN5wJetLOlpVI9pjDuVKozmXSdC6zWlpsMkZPE3uFbhI\r\noMYgG43RSWF8anuq1jtHVIGiXzjikAFSnEduD+vxC5950NmQ2PxkqVgoLM29\r\nwUQwCFRh9Il/2g0V9Q6tzIj/Hd9LXpaJsjVGYTCKPrqtp7LC9R/iGoc3FWx/\r\nRtsDFx2TiENYL22+RHFES9588Ep2cU7g31j6irhiXQ3Qg/rZKdViAACOMX01\r\nloYkjeoAZwChw3NxNvYzuT22828UHLBt6lgpLhdPuKYrgTl8OmyOv6Dh+gCb\r\ndtPH4dv70yTOPzGjgaK/WU1IzFKHfu+i4jrf9Bnh5YkaO1zhLIip/eruoVBe\r\nyDY93SBDNrdTAX1qjxZ2FU1Gi2QZVkwM+JYBBvVuqgHLysGrgHcAb3ON++En\r\ndxYmatA0G/pV09kXRxTbnDbGabAix5DBxfA=\r\n=HWZ0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-use-callback-ref": "0.1.1-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "35b472f7c0997520323c7c4464cc639ac258c406", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-/Z9Lo5nqlwC+tCIuIRhunOIwJp0evCg8UgpOHSPGH91ThQpOLjx4zGyujIz4+YMx6Rm/bk2YKowV9j8Ny4Ci5A==", "signatures": [{"sig": "MEQCIBfUa24y0VrEUBCHuhpE7U1LJvgTuJXDmngcSSPpOA17AiB/ltSqe/56xO+ki3nrrKGsLjPsegVSlvne4RWIAmvnlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCjg//RNpZVuH8nrTYCItENs0BfliJbZll8G8Gmp55w3Kea+fbWl1b\r\n3w0rg7BVXl58MIr7XaAfWs/uOHAJNaoOPtMM99eJDyN0GLw8q7hSAbT4eyIN\r\nvtlYkGTNZJUx/oWn4p57Ghg8rhp0rJMdAMLfb3XNbijlKlP1EgWP/6e6JyFS\r\nA2OLGbvwCE5XKv4P4K5/yQUvPFO/4UE/61Q94OtvaKK8HvyH4Ea7UdQk3I9K\r\ndEitAYIccQoTzC5AYwVDom8xtQMPazMlUuOxj9t+Ax+nLmYTq7wWipt4Xr/1\r\nA4/HxB8qelS6Qtc3hqnj5roptnB6km8Ic6QVj3ShgS8IwCdbXg0LqPFgnx1N\r\neK9B2MDUPlVQoVQCH+XSIFNKLBDsGuTTvqBZYwUY3D9cNgtIi52GB2r0no+y\r\nPAG0YVOrXcwjbz6kd1EDvJzYXjR3ocFXBqjPT0p3KaQGk71YP23hEOBoiOxU\r\npdXlQyT0PXafHj8dxemippFvT6LdzrrkuNZgsyeNQdHBmFsg6bW2TcvQ/Qgf\r\nxuEKRDWivnuuhvp8YLIxlBBoIk/mEWtRvnibCuSATX1hty4OmrJ7bWFpSk0q\r\nzu/HeAryR787+PklzWrUFmfjpxAHEXvGsrD8cXQhLAVyXglJ1w63pFZbJhXa\r\n57yHwg+muml2110bP0w3fmvywQq/PNvGJ0Y=\r\n=3k8y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-use-callback-ref": "0.1.1-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e8885e5b93fcf7b0a4a73ab5608d84778710adb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-kDDyK9m26Mogn0351yeQBmK+AAE6DLPHCZ5Iw9t0laMWmhjnhy2IbFzijtOeCqjpwKTao6xbO0ews30BNvYMcQ==", "signatures": [{"sig": "MEUCIQDXYf93RMI7G+6L3qzXM6oOnrk9RnjIquKWun+BbQvP2wIgSQVcmu2X311ywDRV0q9FAQXqPsAT6fo0GBIll34RqNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyARAAotNJIQXySUMwSMg+M4+/7AtSXx9JXps3PHvTymTMoHZQZwOW\r\nzRoJILgj+ltzlpY0A2ALp/QxGBkQYZzOr9GI0gR/psG/mlH3MDFwtDpBl9Jm\r\nkzg9Khepd/o4R6a2dQ4MQxmCjXmAlLC/Ktd791gSx6URAQZpcxsnS9zhzzXQ\r\nOYLm/prtHMBbJpAQTub9ldgVZXZyte7f+dZ67kNcQFHYKa5sxunfFp01rNdM\r\nJwsqfQNYyNokAqabIlClhfAbsDtNPlJi970b7G5HYuiHTO5zTxgqYTsvUmJQ\r\nOtqi+8ogz353eJaASvhgtWQUOMl44Tk+2tYLUOkm5q0HhgY5K4sV93CszH5X\r\nUQqsET5MYam10TJAJ+VPOG10rcIULjJqDYMGrjtkxqN8FiGlHgT6KBvKao0Q\r\nN+CO7QH7wT1qYwYDFAzxNRvGFDktFQWm52DKc3ffyhqEG/qrS8txHp2QNbsS\r\n2jWJl3GyYUVcMCfwN3rkKr6EH66fmj7OR+RqVCedK01cYMmKV4Xf/rmeLWxQ\r\nGsBXNSSOtumn2Cwv/zIkkhpFAtQMY2puXbna6BpTiG2bJswctPlLBEp/TOhb\r\nbiQL1ijTYIfaSIHpO+PMzdLtUolc7PyCpV2HJbfaQEYxoow54r3AOTthr/cv\r\nt7HAi/ilTVS9LG8Y2KJZxhZ64I8OcwTmdQ4=\r\n=qM48\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-use-callback-ref": "0.1.1-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1f698b6d4e1870ae32d04c2c772ba63ff961e422", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Hzfz7DY3jhapGOHqIy8A5h2GuR5Cu39nD2F0fd8GxEG3AJXqhDFUPRj1nwthpA2cskjmbSEAdyHbK2fucKLXWA==", "signatures": [{"sig": "MEYCIQDyzpI/xnjJj9cvmRLFZA7CFAfv8qQEche6dMc4LbsyyAIhAJ6mtSoEqI2xClPRrNJSyCKib5RfC5ZWvjZ9JlTGdnZX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8zg/+Mw2yscn8OZv1rDZH9upt4d4EvEJH+nQHlsM++ACKUFpB/S0L\r\nt6p4jnyhxt4iXvEQDehuxZwkZgONgonLkL9MPtbplVNc93dD2Fl79/g04qyc\r\nRaZz0xGe8iCaUnJkSUfAvg/J1jS8fOSzYJg25t2k9uHmVREBW2nh3Z7R6LOX\r\nsTSKAsa+iZyrHw5rYdSHnIGiCM6Rfed0109153Y7TngI2AIe80PKDvErxG4W\r\nubft28/m1HyDvBBJONzNfR/GB3oInNO3/Bxhfpw3XfbRQlMNKqOrJDOoLWec\r\nFTjc5AkWOFkzya+0mpj4H6CeVp4ldK5nH1H2aWAd/6YasG0jv2TCLcrJGOd/\r\n/SllVss6dx+u/6BBGoczG3J71sUVR+2/gV01GbElLQr04KveRa3mfPO/D1Jt\r\nY4jAAOMtZl4tDp+iXUJN+4L3UM9rNOzBcTHrfpM2mI6emm4VP1LLUM6iGRMx\r\nB2tuEM7jddLapD+oy9TK1ZxjQWYzl8F/g8cl1O8pf8xPpB1gWtgDM0AxsqBY\r\nTt1/2bMMJj3XbhbpyzYO7/XeVTSEa4pufyn+1qDX3u+/3S3+q191qpZb0q5f\r\nkcqlSaOhBi7y8riPAq4fcHsNC6nV7SHZwAujRmWeSnE+5KFrjeAYLz3zmwQA\r\nFwVcOL6DPFEZWnCBQLzcTjC4gZ7qMrKx1HQ=\r\n=7hJQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-use-callback-ref": "0.1.1-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "368860d4aa2bcb0faf94661a111002b6e94cfe2d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ydga3PHts0y78tBY+Xy0IuWfI2f1p/IkAwRHqfaeY74TJruaQYeG0kuzkcm1sdkI0llDNGU3eQUoCx5lGV+YwA==", "signatures": [{"sig": "MEYCIQCZQEYrtiJitbjYgwnRBvUgNfD5v5EJ1BGvZ6oDOcmWowIhAIJhZuXOAoO9JeT1eFpDLg0zYOiGE4l1Bp9W5ogXKZiU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovcw//SETkYdlZaoMGIsky4eDqwvBkaBxhnDrUh9K1oZY5dUFHIMWy\r\n626JZYxKbDbNwURMnxzXjxo53ytovTPoUxpPQ9K+nw1Rj34ESFrlHVxGw8Mo\r\nTCFvmU190hLtN0VIB2JbpxEbRluFe4Sbju2JgPXR+/kiVeq8+IvvNvEbcDmV\r\nE140v4Db4o7SLtKTvDM+q3uf82sNrZxA5SjXkeoYxumx0Cyv8fS2ZwKyPxRo\r\nOW18aFtX3I9IxUNReBC54gR0E4r1RHBLCrF7KsOQmzuyBjFXrpBWKFm75bmX\r\nZcafVBxp7Mtlo/TPY2ASNBIPTkl6n8D2nnwQgx5CsoWw3NLRYRcy8B7DqFzz\r\nhYslPyFY1bivaVYdD9Btal1VNp8vNp6xEdEX9TJOtWOsB4DHJoEU5IjPsAMY\r\nB3vb77Hb2eLaON7GZRlkmt5bTTyWzNvrn5PcccMUJiXc7Biqt5qhRBmqyrxF\r\nadnohIH/+bp55MAHe6FoIU5wHzOWrXQhVyLvuwwQl3yu2eqeZ8mW7CEZRAEN\r\nO9Bh8ejSqcFDOt+2ohdp2thnfqwFNdaTatXA2nUYsx7rxtozWd1pUeI0Ax0/\r\nF8AyTnWh/RXYRTmOAE3kv+Nzj/ZK6GUsCGm3ow40kD9KZQv1kpe+DoAxwRkE\r\nbtskxPnqPlSU7FpP/rQjrKhsZ1ffgF2t/RU=\r\n=HaGA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-use-callback-ref": "0.1.1-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "922e6c3b7f113a3b8949fee127b5c3ffeba09659", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-RJftxWtdj9xEJaWrDonpZPFRh7aG9ClG8t59ezmo6r7C6E8l3V3PVrhCVIttrN9onux/87/9xqy8BPWbcK0nIA==", "signatures": [{"sig": "MEUCIQDghtk2xqYD36bFtPQoE9BZOLkzw3tegXyOyXouSg6iAAIgedscjLSqCESYNS9gmgypfZv5le24qyCUbXeb5fDfVk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofpA/+IyvcWagSjHCstACgRRzbo25SPliM2Y3EXaMiV3bgkh/G9gFU\r\napKhPQnRuzvBCCa1iFPNk1nS8TewYyHKyDjmSR2qG2LrWh+geRQ/Z1jeYlQF\r\n7egOFX3U9xJ7Y7Aiw+fNJqlO28gtezCs5h0Vunzwt//U7OrmSUbDjbKUtjSD\r\n9ty6oWHt/PqICm4nqkkQXuODY68Z8hfMFBFlOvSHHI1naaY9WO0DX+SuaFxL\r\nCOdVtymLmFnyhlIyQKIn1Vm/OktH7F12g2CU+ufseGjtGs2pfH8n9zsqxEX1\r\nQ6QMOLeXWAUS8BlHA9o1SaH7UZUtTZW5Mc62P7ILDbBLQRmpCZuLEX+nRYCM\r\nl3cLB6IyQKyuqm2gLNY0rdO8393LwzVfXehaSmcfXwiq0FZeMvvUMhsElwl3\r\n+pWOnazTB/K113qZ5w9BkInAYChTSAyWEjv7UPnBfIXal7aC0fZZBPusOj5N\r\nt7lBFdn0YBXURY6EShvhDVNjEakwgKhQf+5CLCMSUL2zAw4Zu10sPCZ5OWN2\r\nAGtXdDxkkk1KdevHLklVHO1/REaOu1PfZWI1gs9xI5Nw1/ItOerw75p89XIn\r\nyYj9nKNFKIPk1xamzfFpq/isMEi/0w5BoZGkTvU1yLiJwO9eCltayoMok2fx\r\nurgT+oG0e20MKxtbxBWPwt+Mo0liJu8crUE=\r\n=MowN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42d98b8ab0d2cd5084b594eb8121a2732ca629e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-tq9Py4bulve06nczsEeDqrWiknjZpQDIQVqJprTPEQaT9c1v6uBYV/XZc5hmBuVdKqpjhiOzo77SwdPPPriReA==", "signatures": [{"sig": "MEUCIQDztSluA5YMd9nfHW+CPJCDkAxWzvZBg+OImYDpCnmfDwIgFo4eWNY4+qG6Ye7kUPoGmOjoSXMoBcgxtc2SLhcKFMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5FQ/9FOHUgu+EAMcyMuntPFfCCG6qTTpMvOFKhrzvLzQ2/f1LudTl\r\nkng1kVTX4iZSIcjSoSfglQWpQH0ARyU38kQTQ6N2wXLm3WDWhVZrzy79Y7XX\r\nMGWvdre5jLPLz0dG3Gt6c7GwtFHqzqiebKFV53X2kS0/Gcg10ODvMf9KVfDG\r\n28TqUhZekxmZUiejw2sLYxw4M6F+h0F/HF2JwOYfhWaLhmsA6aE1JPkFkVAM\r\ngNL2N6QLZTnwTlMRQjnxbRVz6U10tTjj5LKio7ySR1RTRLqT8w/ysWDp2ZV+\r\nZs0rw2bQvNoSTL8qUC9PEBh/uiZ6og+2w0VlbIatE4FGj77QtR4q+XIirGNc\r\nnu7PdG2XDif2WGZ87m5kKwrbwmP7ciu3qdWh79z5xYdRCZv+MrnM7z6TqrCd\r\nigfvDicE8YMiAFBAxzwOhPwb6qRshGer4vSBJQnoH+zRnfAUvkbLRsqt29fD\r\nxzB5g0e6QCvrxVmpKWkcIsOA/yHbO7Nfq7MxHSoyNQaCrJ0imA7HU5F+UnC1\r\nQ4l5NQF3HCD/dbT6XMPrLgQdRT2AZLA/mamTUWLOW+o2xkdRbOwRrhnvuXjr\r\ncavaKW+cBXaDN/AALdblWgeaiGrVIy/DdvJK6qKX1D+hvH2kTU0pzpqaT/EM\r\nbHOWNiXTbu/sCSFPeXN0sipXgx5T4m2cugU=\r\n=34pV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-use-callback-ref": "0.1.1-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b82ce3d02534f60977e69981f6490bcd889137b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-c/g9sihdDxAFB1idsDDTnrsHs7sQmPqVkFD5BJr//GW75II0deHga3+ayA/11clqjzKAfJGDM/dsCCmHtimvhA==", "signatures": [{"sig": "MEUCIFZMAMYmM7f4aFwR4vYd7yEej0IdqwIDIvK/XJVJbj3CAiEAmEDyHxDjY2XfjC7Fjwc+CY4SWBfodMpT7tNL+/WESNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSktACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSWQ/+OqjZU+v3lO2Hip/ia+mCBHQoWhEv9ZcW+/w46ikGeRHWOIRD\r\npnBY99cz1IlNP3IMffXBJgPMDbhElayZQh4wAftnDQK58znlPMETnj5y74nI\r\nnYoKH3Vxb5zIJu3Gb98rt1X2lveyxj0yUURAu4GYTrjjhQtgB325X8ijwAR/\r\ntGPQR/afVLW5LXJvY7Ka+Nc3IjM07clwAvN3rCbE91sqxxMX7k4H7ybkxyxA\r\nFP/CHcB9ngQQqweLdL0WqcgBHvByVE8vbxmJ72ECOZ/AyV0P+PjCypaiuEjQ\r\n75N0nJQAF57fNDGKcpgVluJ0hUJvsYs95ZWlsTopmJxZ4wDrX/n57VTr5tfX\r\nDqOL8AXiybmKxf12hjzvpflhRekgDqKTc0wlEianvKqASxfV2J66f08NgVdg\r\nLgXz/5C3Pv+bVmioR1yh53qZAp2QeKWduMZnmJFUTxQJII0jJYLUH9E9sg22\r\nqvrg+acyr7C7/koPdCt/G0tybZXCHdccurW1urhFqsVDYWBW1j1XxwCaXHVl\r\nqY06TmiP6U0qJuHejWGXYeHpAvcBWgT0cioBVkJQTmQ3+WTRWoCX+Kp1wsak\r\ndBXeQ6B/IqRRp4b9qXtit39/vE5RhIHLLaqJ+UOJNu8clpYXHbRkrVqotKZB\r\nD1HX4hdiLVFdMrP/PWFRnnN6NeClDuZFBHo=\r\n=X+Mj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-use-callback-ref": "0.1.1-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6201a897939f9da7f4518ed8883a0ce78f9a264d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-96AIyDi68n04/FltEmWGVrkXswOVBFktpG/0mRrCWRttrE5j3iBaUi65yWuy9HNBkatKrpHyheXR7XR+PvonEQ==", "signatures": [{"sig": "MEQCIGUPknwWfV1ZQeCCrspXJeRVrKx4JYdXDm/gLVNFn4X4AiBfGdxnRXrv3egHrtTtxNMQRBiUdV6tASlvXGQheChcWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38218, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieoflACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo5A//cjQn4CBn0ao8WRGybbiFrKlNWiHQQEY50yrlFbwrvlACbZWa\r\nqp9gEjROAwZC8j0A4g4Z/J2JETSWe8+n7oaYXHerFMGXAceVIhkraRJCf4aB\r\n+iu3dRP9+xH11PsTc82JB3UE7UxvabzNjX6ECH7Yh1+MT5MliG7E4mT6lFvT\r\nkMkngBr1X80br9/fM9FmEjXp0NPw5Z6O1FSb3vTsdjpJyl2zq8v/jGOXBXOC\r\nXbENGzTCHKKytuDp3mLSUUsqArqALcRLif/Oe2714C9KUCs+PYtbseiZYsI3\r\nq8d8Cve0s2wdJDiN0jl73+uyPizAS0q/vx5v96r7gknTNfVHs3gebDYnCck+\r\n2DoVKjGriWRnqPtUxO3/1KUABv+QmyZU465q7VGLkFAQWUZ0zS4usy9rMeZu\r\niwQN8j3NzMqlZtRex98Cjo1Q6DR4lfDdhkqpxbes51vJEeCBcG3XINbiUzHI\r\n/O/FydaxBABBDh/sCjPOUpddk/3y+PEmfwCJ6R9OlpKDqji7pOJwNUhT/R+e\r\nslzHaxe4/WBoAYpjX5MAGnRScz7m0cvqjnuoFOy4hpqg6uBbY4Dx6rBVVs+n\r\nuQELpGj0EE9AkUcCpxIWaRwNLsv9PNsOOOcM1IE1JYiDoFZD3GA88fJW76o/\r\nxaqoFiTc9fy7OYp08uWx35jGc/ml64xfB3k=\r\n=PtBb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-use-callback-ref": "0.1.1-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e24bb45afb589eb0894e1cf88409218ad7d0cf1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-vzCq6GZaM2TmyQIBHXrZrtE9F5nit9CmICzf3fcO4mSdeXbaMRZdce58/vZk42q6qNTBrh0Uj2FZG1609LUv6Q==", "signatures": [{"sig": "MEUCIBbnuyDIGTeETbNQuq0VsxBLMfmFgBvN96JOlVXoPS+jAiEAplF9zkX46mWkG2z7HzDieexT9Pud5m9m9IEILiYBrNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrE2g//TlCoS2ueWrUo993jyDaRRCyimd4A54oSPzd0B1EfIUyrANUx\r\nAodoHfreeZemJh/ReJII7aZdgDa46lHbdyHQeG90oz45VZFo1XNanQuCodtB\r\n0tMHhAUyJf7GCBbnjS5QuJM5utx4SrihkdWNuRP5Y0BX8mLeNh+m9+WLV9+8\r\nKqpzxAnkobaKUCeg7ihLt+RoHhptmx6fBNNxO0rz1L6cDQb/9B9W3zN1lmMC\r\n3pQ41Hk0B3Qvzbn3wY7FA/CvigmJsVo+97Jmu7lSRUf5WkRdIRjfgfeGfr3o\r\n0wxd7iO6qR77jRPdTVVejYwA0jC7N1egT4ar9TYUHyXIQy8PMx78GzbL2mN3\r\nXQ9GmPglB6CREhVw38JCdbimzurYjm+8ZFbQKHSl+Vfu3BWUMwfr7QDtLVvB\r\n3YBV33yBeCi7TVBL5t6heoLX30ZyosD2vHrVIx0Vh0Rh0PUkQB/I5BbxPC9o\r\nQqqhZ41vlzK6Jzn38zf+dEXkkwo/62dESR2S2V/9czNVxDMOOeoBqbkPJp7w\r\nYnv1GWgppMOPfTQMq1Tpvrdr4eMtyPF4O3NPLK3Uhh5Ybz3yQOXoGcn28ZPu\r\njt5bwux0kAqZgHT4AMVBbyPjfnmagOgp/inzcj8hcDFcuM6D/TklFgpXCyX+\r\nun3YycGHRoO5xiZOCpL2yozPkBU8/TZXL+g=\r\n=KOPd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-use-callback-ref": "0.1.1-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cae57a3381ae2e61f893a3609bd4bfd26056f1c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-aBlXpuF5QrfdkPOJ4hgPom5Y1F9mPWPXmCbg2rEirGORjKESde0yosmv1mizBYVJjnNwIQQJ+y8B7XFbJFLFow==", "signatures": [{"sig": "MEUCICGXyOyOXIX3HVCVDXUjhgEiQGCWW3xVnr7gT6xD0o6hAiEAp5Ffe1uAN/eSg1rojq9FynNzrzXCkG5RGJ73YysHEKA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/cQ//UFnN99dA+zGKpv+1JJXeoz9kuaQdg+/Y4WmuNkRaZmNKL3z0\r\nYnh3GpD1LKgBaIbd6ecCNJJEG+giRziEnmOObQUpeVoDYVcbOyK46BgM67PE\r\nXnbW7QEqk1y0Km5xO8Pd98yrFOmvwk67/PU23y9EhjXGLBqQmAQRA7CbPs2F\r\nCmEGEWfwoL01oLQ0ZEqJvTRJRLMHGRWTK2VTdvOfFm6qy/MwYmridYoVzYE0\r\nFU4T0eypcQyOIDEfhbOeEPRUClHIx2xX545DP+R5f+XpQNDo6Rzzf4cqMASy\r\nxzI6hMpqnlj6bD3POCuXOJTWYRQWz6hfJvZDJJWZaNsydfwDk/M0Q6RuFJ5F\r\nWClZOrhwH63LeBLZK99OqH4yjqHKzX2CV/joc9TlIFYtrrRT2WrnQbnwLbhV\r\nRJ5p5Uz0T1aOzCJvio4GOOJpYqk+P7zPtjooEKGwk2evfoMkRKb2tkLOx8A7\r\nEXyKJGZfHDJPIUovfRwCNnbY95D+lYPb6g+zIMZdcrgAgYvGbcbNpkM4yaIT\r\nsOHU9evJwrDT5IMy5uHIWOy2JWPe+H2vvid2+RXQEQxD9fXiXMCrg+MViZdx\r\n8FyXw9buZFw5NVQPimzQ52r9jeBb+Yl42clV7uVhSHG/sdIV/AJlXQHBGAmv\r\nTteR7hmN23daX0akWnvRZ/xjwxufmLKL2Ss=\r\n=wFnX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-use-callback-ref": "0.1.1-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f74d074184c65661550bab3ec25c4816a00feaf4", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-P/bxEV3Rp5hm2AgKuH0W3HDbKNu8eESjm51krFKmuRixXfQcVMMGSmQdHZVS/sFO0W0uNenUaLAK4m7TisMsTg==", "signatures": [{"sig": "MEUCIADam1RJsTXwquDaUQfgWgt5Nctf7GNDPkQNTZe9J/BaAiEAtSW5rAwHEFJzI9Dranhwm0nToH40PJxO1Lv237slC60=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4GxAAkRm2GauKOViL4puL7tfYDv/v4Dnd4XZqFXUFIJrxtUAjil3K\r\nVgPaxogRukph503w3Xv61Wt5+EZW+XyyB+qmsbnfzuIjz16GgKhMMpUmzlno\r\nRFuh17PVNwlRq3gtDT45GzC/aZopf8B0rKGlIsSS7VG/O4FWuXThCD+k65lG\r\nKnycnTZWoBi/PzQ9nWYdBN3XgbwUkchwgV2SnVoaoQRWpkILNRMQ4h/wF6B0\r\nQLwkWBVOOT9LpffS76rYiapW1jMXLVumwg/1uMLL9qCPFUAaqaDK80t1kpJa\r\nfhi9vO6OJc91dvLusenU+PTtf1dwKeYqxn3zixcL9SWGWcKRFZqkkjsGPov7\r\nAM7XckYLnKGT6u0gE84r3Rg2NDl6Cpjppji9O8elGF9P0L2+ZbAXtKN+yMC9\r\nDsK3i5VtFHsdYe7WuCGW5QgUTsmlkvrOg1hv42U5CpO89LOUSb+oEbuZGV3g\r\nRd99odtLIpKT/eOpiRtB/PhHNKtcoHm63u4WzWrVOx18PNOacq08rLPtpdWG\r\nxdchcZhbPeI0p7UQ2q+ntdHmFNdU8nXYtP7YxrFnHwzS4YqOpmFV8dhyYWdN\r\n8/QuoKY/6uu+XQIoyVvLirH4S8f37wF0Vq4CKfWvzRfPn656sPw9DcKsFMFf\r\nBW+bOhqoY7+PNST+PMQxIfdSzErC8YQdRhI=\r\n=hPZv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-use-callback-ref": "0.1.1-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6e5312e7a56927bdc0fba48324e5f343f983e637", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-iggXqdmPiG/SVpxMCgkVwYZEQ+PJ9KAmjmBlAqEWrMm0/htHj5NnX60MDwz0wobDYqX73SVCowqv3NvTEUUM+A==", "signatures": [{"sig": "MEYCIQC0pv8HlcK8zVeliK6El+bFxuLP2+rvXukXXL4GWMrE9AIhAPNu/lAqT2gMIbw8caX0rR7HL2sZqjlo+EKksTqQuLBa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpv1w//TTdBjl7PXeMoV47ZmfC7If9b5T8ysD1i9ctFg9X/L4RvG5R0\r\n3WxqdDh3r619UZgsJxya8mqepj1CDX+ksFqrY6LmOmTcm7Z8uQep+bhNHLuZ\r\ngWhq7zpuXiMAqhD8AaVeqBIGIij4DnDOyuYA1XPCmv8pE/cjSQW0xz9HaBzJ\r\nWT4y+3kPSWwcpNp9mKq5F45UQOP2ie5hMWyP7KUg1mL7o/tmvYwg2Y8<PERSON>ayR\r\neEm9qQ2YA8T8fBWBI2ZrFCvAGyOBKDGaSsZ92WK1vTgDDh8FrBS0wIs+4St7\r\n9ilzaWx7Xiq1M4mzrt0/xDk5kF0Qv8YWu+CRjINMp5O8Fm9ePO5zNuog4rit\r\njDR4PetBrhugn5c1WxE+q6M9TuKxA69ETJLnRHRQLJi4HO4RME++yPEg9PyE\r\nBtW5Eea5AqChCCjTqO2179XuU0CaWkP1tL8FJ/Mk0s8IZ7CqvfYdLzr7D97v\r\nzlkGbNeAn+kEKBN/pa1cEkDvbFNA0PRNrDXP4HCh0XbfdecGVGjcVe9+mIZo\r\nh5kzHj+p0ryg4+J+Tz5cJ0pHa7CkPdUlBmyJU/+7zRCWhTWsmGfsfifR3ZwN\r\nZFFfwfAEooGm4LMeXYYq2sqplTGOv4DXIJyWWfqwkq40PPNg7F23aRA8jCBO\r\nyWZRpCzQRFD4LV5w3htbS464eYEO7noWxGU=\r\n=i6KG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-use-callback-ref": "0.1.1-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "327579695556177a6963c065ae6ae1c553d8119f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-XMsO2PlLQVFYpvXD9VmEde+EOh8iu2ibfTLxhmmSTwO9YR+uXmoJVTMA1fGcbtwURldo15HMQSsuzWtXWM7KVw==", "signatures": [{"sig": "MEQCIChCnMWuBnsJkR8hYf/Ia4uaXh+d4GvQUGDcmtQhG7y1AiAe5v4tlrDZNAyIslUZRd/bimLAHnyxcag5RlyE6SwmlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifhz9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqgQ/9GcNHKnWgGCga0qvZIkTWDNo/oNDjeT3O1V5Y1wJRpqGduOwt\r\nnQ4PlvWTFtzsJmoCxNzvbCA6MWdJRof3LeejIK7D7L84bXs2XzvyeJaEGhQ+\r\nmKjKzFVep064M5liWYwgt/eWavTqn/hsdcBmOuPBuRw0oYmxDTDirWuQjeJY\r\nGKtWnia/WzavxhJ4JAcQzFjOzt+G89laxrZfQZ9G18iBl1S9OXRGCjL4VJh6\r\nBo8KDSw104jHyxGpkriycVRroEUbpTPXp1ydERt9rN+KOw5GGVG+YC8nm6Pb\r\nb7k+MhWp7evzknr96PpZJMhG+KkC4KVbrS6Sfdr0BUNIiDmfHh8ENk2jdilH\r\n/yrY1RlTnOh830enmDR+rYYRYFoAuwxHY9DM7l1S0Di8Tc1MyqSkBsl1nGGS\r\nfuR32sZ+gd66N+b78Dac8pNy6BZPlrMQVTfVlULJmO8AxOPHQ54Pwch68lT5\r\n1dM8WTDLqYufj9si6HuR7MAQIziEd6ZdBYdcNzEGxpTuQq70bkzp0KL8853X\r\n50nejfYw2jOIsSfkqWz4iH9hAN/npOLilxtPOSXzEMwzuS6sduu5dN1zaMgz\r\nrkGaB0kST7zWC1+wyNj08fBA106l+KBUHep76YvjIXhpYp6IoenQy5Fbthx4\r\nWL9dUdHt59lcpszTPLQMetcGKT9ldOr65F8=\r\n=zfvp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-use-callback-ref": "0.1.1-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07c536e7357b246377785bf15c0a99717484ad3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-hwb5K2En2BN6x0hPwg7pgTpeHzZ3dZUOFCIznn+RvmG71Q0IG/otLDHEHJ8K/WcTRAMRebAgY7QjF+sf/1QXRQ==", "signatures": [{"sig": "MEUCIQDPaIgZCQx9B6eJXDQb6eXzyWb+d4PQq4D93dO6pclu0wIgGHrsWfY06vaZFSTc2ta/9xWai7eBJgm9MG2KPvLNsRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMPw/+NyJr3wOdP9AjbsycPMFfyMe8ndQWi2secnBLB+Jo9+Xc+I7l\r\ndx6ewoKcsio9c7nxGVpAHjqyn4IqHTvyw+Oi49+IyugkCYuBFtlvvD08Jzv9\r\naH5pATQW3+BqBQYm02hTfoFptBkJPX6Oobn6khtcMKabYOgavcK3szFBcZKE\r\nUpBj7iWPKg8fzuc+dziYo8Px66y/bbhOk58EZgYE5PFqT6Cp/FW/x56Cvu8D\r\nBaSolMuNdkwK65I3KTOadt2xW+KcME59nQ6hg+B6RrClIG7RShOFsb4Kakp3\r\n+RXeoMaAnPxMuF1zp6U2O/lDkNp69QkcbmwH/RY/ZhWEwnDeDj2d4M5B3Suu\r\n1V3znMX8xvb3QUPcLMDW9AuT74iHivyVeE81CCuJbQhxTo4wajO3fMU/X3tO\r\nWzoQp2vKqZWzSPPCz2m+bq4xY2FDwX1Og1UdmuOVROwuNaWkU6MVAAQ56Lvw\r\naSGH3AXBrXEvvTwITQOrPUuu+twC4zMDeDv/LOKIDoNpYQGQoYtUyVV8tzi0\r\nd44pGI7nyEPUdvGzQ3Bk18P+PQwHa9V2iTqXWsmag0mGdp3dbo0yp567ym8h\r\n5hj9xG7RtXv8XwNuxOoNIE6y33JY2DgOuMT8lowmhMYHcC6euHQY13ChyjWA\r\n85qBeb4UUO2wZImwweupwMavKqko4Sgyz88=\r\n=2LKd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-use-callback-ref": "0.1.1-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b279f239bc3ba625d395b013660df2e9b2f99db0", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-WQB/6bgxSnZ5x6rpIDCH0S+zYtT8JGMCjtctNx2BZ+WqyLHQ8v6ei19fl9E0KV2bY7xzRcgJd8jtf9XaKQnMnw==", "signatures": [{"sig": "MEQCIGDVtRWgBK5jJ9ecl0j8dZSADBB+NXnf3C5HTa4kJmeaAiAKedD0aN41+fOAxp39cvZ1jW3pAYCueuJqAS79f0fo2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWiRAAmSg24deY0nZRB+r+7SS2KqGpb19GyebLfG/CJXMVL/2aclVo\r\nlNu9XUyyUQ2RG1yUjrS16CNM/gTtDPI8Wccr0/yetb+u7yrZ+qXnFzhDwr9f\r\ns676BRvpnl7GTx48Y8+B88jpHknWyzrL6Pgpz0adhE3xZ/6gbPr8rAbx4tZe\r\nP0wMWqlTjZaNcW5pwM48rHXTkZ+wuYyS8zUfBMB+8CNtn2L0i1LED3FQFoQ6\r\nkupDxdFP6ZIAg/tyLzGKKiJOiozhtza6SXhKo7QWC47q5oCwgAfLtnWMFXWq\r\nn2gVXOFGZJKYllAjlpHnbiaO+fK2dX/SapNdZWT8TN5MDPYWLrL3X5PBI6+n\r\nvQTMeqfe4nahhGW92IpQLpPP3I56sOyrY471iE81sWcTXNuD/2sv/M+Zr+FP\r\nKmuzsuGTNNre8NxP+nysHlSvd+tLIEf9Xh6M4X/f3uPPRsSb4wAJRiEADD0e\r\ngyMVZXrvGLOvF5MOq4AEkYZ2JnC9ac+4GD0tLoskDKEKZNp7s4iAJkBQ8uSx\r\nguJrqXhqiEDPfp2gU3gEf9m8MU2UMv+qAaS1onW7vj2e0trqDkQbTSemujcy\r\nCbg4R5tuunKZhukZcPFjcKQycn3PKMOCAzZmYmJ9xQylmEHEliqyGGhHQq6H\r\nXygaT7C8TLWfEEQ2QY2uIoF0TLvSYkVIdSM=\r\n=tNx4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-use-callback-ref": "0.1.1-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff8be14aecf89bcbc2b30491576f69ef954149e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-GR1aufZLuL+Q9KL7A+/pvlGFvxWYKwzmZX/ZDqs2c2E2wPgf8ttYnPCIEuGvEuwuWT04krzraw9VcROndT+JXA==", "signatures": [{"sig": "MEQCIHI8mi2mt8SLveF0HTF9h1kFxaKRRmOn4Ao2nuK10UCOAiAO6JgbfAarWv2MB1Mg7MScc4+281rRQN7UmbGsaOp1ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptGQ//fCXHAJ+eUUrjOBaQ9xb3TOXK0ZTYeV4BEU65bQcEq/tn28m1\r\niSIH7KN0jmpVaHr+sYkjaukmqW8ylumy6lx+3NzCdCP5i9Xb0oqAFVZFgSA4\r\nD9JrdXrVtWxw6HfPAR4gGshOXJ3vSy971tLRNjZjtxzSFj1E9vccF4VUOoIw\r\n5weuQm/wkFXZ4Mr6rvvayN7d47lhevjoIVn+IW5laXiFSquK7r6hK4NAAlku\r\nzkx/wytOYbFMprtOR1SHoTZI6YHJqUN12xsv0pAu2pGwnsR4E2iZfcv5gOrj\r\n26+qK+qkz63DGd9PY1Pzs+geHy6/6ltHLCXXJTu9S9jEYuh36RLDIO1GXEfe\r\nQmEpjEW6BM7tiAvp/zAX0Jl7J5K2xUKUxdG18+7dRha0h8z5bdduXPmzynFE\r\ny0xAQbiIqxSoigpkzwVbrlKey1Vm7emco+EGcfs7dqwnaXX7DUHYvjb+SiEQ\r\nuCFxHdE8dKp7qPL35yVQnI6Hb41evsUqmjbQJNQFffafBIT1wuUZFCWiR0KO\r\nE5UezBgDn8KXk8ngVdMzVZMccrWRfwjvR45bCboMcPsSIHSAsa4WvwZESFDa\r\n/ZIOdcN7r2pRYVyGXWKxZgOwBAeJB28GrJaQA2tEnIr9gWtbVsREQTKkglY0\r\n2BLdB+R0LTLhwxHmbRyAMQ2U7iqpFGCVhd0=\r\n=YBdC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-use-callback-ref": "0.1.1-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "342c8b2b12105d4556ec618c7ff299301e39d892", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-JDMiY7LCemvr7sszdt5gixdegF87U5ZbKbi9EJubsyyCHCdnM3fs/1+JuBRaqgLgt1uTBcOzIkvhhmrjWxdRyg==", "signatures": [{"sig": "MEYCIQCubHDeCHCWhCwcISB+9MNKb+A4Wecb30tbWGtP5SipvwIhANKgy0De1idw4QTz27ZbSwI3++CLxZth9arV9+ER3loG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo70RAAoMWDR6SwPOA4+kiwEHj2rmA5+nFD31a+UV4WovIrR/ob+gGc\r\nu1mZ0h+pZJYSjVG383pdyXfRbM/NwgbTASxURXlmnBbgtFzShWeAPWvqQljW\r\ny16MKXl8uCN5TKwUAMX1kno1u4T3mOl7MRuq6PKEXcgQs5F9wLsjyoMeTliK\r\n3f07sLhx7qmBFS5zJaMQJewZvETdi3tYKCjDULhIu2aaM+n2UvM/dMzjkX1P\r\np/yuUg/JBSKdKOI+6+IyY5pz/L5qI77BlOz9hHblWC8oRoZSqf/vrz3topyu\r\nv3WRDWR6VDKHbOdgfJR3QAfbKfE1juESspyhxr8N5Kus17qD5cUJU6oSZVRZ\r\nrrZbWSJc9A+YfGopHerpcyAUNPVJ8cFprNhaQUbPMDNz2P5Cen5edz4Wx+au\r\n3H2PCWPYQTMK54lumN2SBumLXLfkx7SOmB9N97vCm60BJyrFHfZ/nrjvMFFC\r\nc5S913s4/JeLzdQI1iLmMPJGDcZQTd3qPHTPeFlcDUgkDbyidqC3J+6LlPEz\r\nXB2bdO8HO8ZE66cEMALW5l9aSbigh7nkUrkzVrWG/nH/IuaRoHoYWBeOCxgJ\r\nczVEh8h6JHb6aeljCHXV2nEzr7Ni+Qh5inXqGFb2bS0TXXjHqhX1kkHRc22Q\r\nJrpwv8zNKwjnrlr7RsCqa6A+UySihP3sdDw=\r\n=ywDh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-use-callback-ref": "0.1.1-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "02f12a8a3a6f20daa292b74255676dcf04d268d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-DeDEdG9aAiGyH//nIhEgXE1+cwariBNYYJG042c+cpP6tCGiz3hwVM7+eKRlLgP5dssP9i1aN/Z8WMnrHrvZng==", "signatures": [{"sig": "MEUCIFXx+7QgTmX2bKdgubah47la7bBck2hl5EsSjlveq3/2AiEAmeRmOmQnpg04m0T4IWYGUS/hzjF37BYsyBa1gEepSmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXhw/6AsqLgAtY3PrK/2umDdA3ihq7vFcZexEPXIFnciXRvr5/mHcS\r\nc4DGz6pjhlTbUF5yk5JPZOXniFMwdMVkxTjanhBEnSDOj2uO0h3RFgxZfkIn\r\nm4/zkmA2s+lwsNrksg6y+DpXDbSSUzk3OM3DFEcgMQUDPnZ65n9uhkVu0VvT\r\n9hKhFRPlwHncIEhBa7vk90pNOwnXoN1heD5/iBMtubrmHkm35w1gp6cZuASY\r\nLa21dU3z3U3nP1CzoS1TkmYUlzGPKW1C2onO/fJwb0MrFPQJpNmVEm3komOu\r\nw7iOrbq8vz16gldLw8EUQialOWVD4dfwY6oUseBcXzxHPZvwN+WXF8Dbx7m1\r\n/fzVSd2PugzCmPV1E9/1+bUFcXUNQIufnpG3bWrjjml9ouU1o/N2xroL3GnC\r\naV1Ruv/TIKo35WqRjFcjzDu6kD2WKX1sCBAXsSEwa6lPEHZEsMvtypNlboaE\r\nTstsG8vpB6xhlOVnJ5ARtF0cIcze5zFUKJkUpXJ78+XhBlFbGSFsIuYfmd5V\r\nQHaQHapJun6GSblzIuU+cGPkKAEHBTfM0jDk4tC+rPRdv6gpAiyPNfo7R+IB\r\nE4JCCFC2KmnPTlLBYIekXkxMacLa7afrFgr572tmbmwq966EyH2JczpVyyhj\r\nlhlacifcGg5MeT/Ig43dpR0F/OdQD3bW0PE=\r\n=DtK+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-use-callback-ref": "0.1.1-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74979bb47f514f2dce68263865e28016ee94f0ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-sWnX9f7kTn83pQ0Qv59dhQgMJEFXxoX7GYozloCPFSkn0dAGuQGe1Ul391vH7xZAimw4WXBZdyGryeOfvnJnGA==", "signatures": [{"sig": "MEYCIQDd7HP7maMzQnaoiucHZ2zTC9wlyHy7CNRoXvMbS/awYQIhAJdJT+vUhHdYkl96TFmZzR3d6wn62eqMYCaJIO85oKPN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0Bg/9FL0ODZluPZwsqGjcDAqvlx2oSZhtsgA/DN2K1iL00mN097Mt\r\nIvqtqNWYOWMY8ia4Wc9Vq4cZL1iJNKrilcN2sEetcfa9uRVBghBiJMbEqGRd\r\nfNViOlJZY5J+6WiXrrKw9DSjXvWOOrhFHb+QyBnZFFvlCek+EK6mQNZP/zhc\r\ngmhjbvAn0YPeUGYolDQaJ0qx4ryslNmAXwjoaodpH8EzD/xOG8SMjid3r+Wr\r\nnTInoVx79n9I+EDIH9PV/K6ODuqUuxqX9Ikxk36A2XTQP251IbFd65juXXjV\r\nTXnosGyZgit/HYM5u59mVGmCGD/5AH1myomzg1Wthq6+sL3Rezjz2/Nd9nub\r\nGvCVl3/0lhy8KduXva2TsCNQNgs6z8xBdjg3cdA5FB3bIpQYecbCa3NJ+/Da\r\neogBA3YDWwQw1pZ9Lsvp/axLb9i63N5bEQc460KHjX4QPOY3YglzFiaQmvmb\r\nWpVmc2Osp7kSOKr+4GeRIiKw03qtdXpqew+lcUeZzkcw6J8fBJ4sWDO59LpU\r\n7ej3zT6TeY8Krl7s7z0mZIfnPueolG/QYgPdgj1LL1dEFg/emfcD49tuMKuw\r\nLHtVN1qss9WHzF14imcX1u6FDsiH/KRjmHQ0ucizgrbmL3blugv7Dh0TLlcN\r\n0m8+Jg/JWS+bHwE4I1vO5Oerqox/7ksF6s8=\r\n=A2Oi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-use-callback-ref": "0.1.1-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42c4bc76fd0c654a3f80f7e6f2af575b175e1ff2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-haV+QIUFLO/v6QLR1D1BB8ExmfO/t05PJLtusxCR1A7T+zKO8y/qOowZ72QDXsGPcj6Dj7bTnXmy3jgl0buIUA==", "signatures": [{"sig": "MEQCIBdGmHaLpwUahubQN+OPsn4KcRnpi1CEE6NzR8EKvQ9LAiA519KxxH52ZPgvIf1gV6htBJhFbbQpv9OD/rwAww/MGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLg/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvGRAAkyuAqk+ypdKJHFOda3MXkNzvlN8KlPx3+TP00+AQukmtJHHz\r\nr3/mmJ9D/2vdSttYHgcss9gVkh8irViPYx+qWUKY7c/jr7ff17kotFhlss2A\r\nopTVdLnqZLxfPQ3uDIopHs+ZkZrAwWw4RFvNWmltWutaOX3TayHD9LeYEBax\r\nmx7Gu7j0JGBmTNHGnX/Sw0miAu4oi53gxVct8rDPRxNqNjcg6mZa0d7GnwqZ\r\nuITw+NsnUs6Of47e5D0jz6RHLUJ+vSO0XirJ76U4ZJY/kuxO7g7y1znRtrAo\r\nJ9tdEH8e1sa3BNQWB49rWW+aJMvGz2pfWok/rPjNvU8lR3B6/x08+wzoyUiC\r\nkdgLk/zV7DEVhxSXTSu++QceqNa9dAenrgQMTXtBFFjvSsIajG8IvylTGF2A\r\nKBcMNUTHwxFLy/UPZi5lcZxJcfeH4GJq3QOPPK6sVX1Sqfs0anu8YuqmXbkl\r\neiuFZkG5RncrFniEOcBPatdE60mvEYw9fiXyxWpsXe7LvVNb7cZiuRnFpBZr\r\nseSJtHG42mvcKqSoWvkVulEqGNBsWLuq+0JR9DljGtwdS5eKxZqW2PAv1sZj\r\nJl8Sch1C/fQERkxRLFZuOKSQRgcZLh52xlO3kywHYgzuZf9FoDdSZjqdJyX9\r\npNUP4SPpmR9zJIlqMEDMSPUrCwjQ8QxSbkA=\r\n=tS4/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-use-callback-ref": "0.1.1-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b0f0bfd7362553a426cfc849cfe3e576c1f7f845", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-SesR+4zhaaXU7D9QAIRVXWZ+NroOC6+eYaS0cv9gjTeHVr80evkVAqClqDyLVO/19Zj/Re42NtfjpwoualJyHw==", "signatures": [{"sig": "MEYCIQC+E7NV7Aikrs5Nv+na1WcgXXr8oO524bWHqXsFMW7nYwIhAL/hTRKwDFtthCr3df6GtQXdtgwJAzBZyZyXvjMNQcl2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ0A/+LLEcXFG3L8vZpAqPT7Uth5l+BxLcOXtZJdG9qbKcsabuMqM5\r\nceUUR2DUAq+0dSpvv04MgUHdSozrraGZZdHmaSFC2bODsBRp/owsT+PllqFi\r\nFHXdGugBHn1DE8xWdp8n9KznMZWfiDuxh4w04qOMg6z5sPApKg1KYcaxmRtE\r\nvbmoGpc1RcnP0uzlAcpbVhL+SWl8XXJDWqusYfv1GgaDK2eNQvEd8IH9c66w\r\nr8L4/Xs3dmSdbtbAtfHWIvO7+balO16Ib2V8BBpHiOoHly/QGEy6xnNfz2Po\r\n6tOGiqPKmo7BMRPy7Yb9daj0pKUdBNDPItxDcrPFrK98T5JOP827dC7+5vUg\r\n5P8IGg2GkXG9Zi6QLfLrIurWs2HvtwFlmMQ2y9Xxqk0v3sZeqyAzwSHahdqR\r\n+Vy83suzKMA6Kes16O4s3f1C9e9ogYk5k/xJepCh3QCv/RRJHnc/tT7bhE+6\r\nwbaqi41QaQeu5vS0IusvY5oW0+gj2GnoIcvqN7idv84QhFZRfkRMF9u47+O8\r\n97IpS4r2heq/x10lSx2u15LhMhcy/ERose/JYXFjIdzmXMkkc0PlfU+iNMxN\r\nOBEQ2jR9IQLgwOaikEZbzm22OrLOD/NW3VIvqM9IhQ3ga/NncrLdInl4tskk\r\nnPHOYZoo12DNdrLOtVpRrncXqF73gPRz1u0=\r\n=Hk4q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-use-callback-ref": "0.1.1-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd8b6d98ec7a1c7684ce5c567a525f63b4b25dda", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-9t+aJKWZsrHBB7InLR5zmT+gnDHfOfoF2x4YiJl9TZAsZJ6fv3Z2+EqGwsI6oG76Yl2U3cFP24N4XjRC0Ybcag==", "signatures": [{"sig": "MEQCIFHk+gY65+5kZWOryS4R12k0BS0tMFkcHV8CIGVAOOqQAiBlu3RPR9eEGaX2KP9JfLPp5uGIDxXKXe9ww/0lrIgn9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0og//QXCMd1FUvayQhCfX90fxDeCk80QlGnaiqr40uhsEYqQnya+s\r\nKTlZw4qFMOfkXT8Zr20ZafKb43onDVKJwZ4kGHFwyGzbAWKrqJc4Ih47uf+s\r\njdB+v7zlmeeb/wrKlKb3gR7GQFVtfF13hl1821qSAzh1n3wTJHOft8mWNRxl\r\nMSpK6nHTsLg7lvFCMGjEUsD8CSlPvhsXsYoG7+rCfUrEw0N3JfgrgCRqSwPl\r\ndwzH0PH/ShYq3zvjv1fnniZDviZRJs34nQkD05wywkO9K4o2hFYXuWhR8yGk\r\niPE3LEWrzARE5Rufdcj4x9NKIE5O+MaoI/TsT62XUmbOnNJcLUmaVOhoQcmv\r\nwF5ZoSpUM5j/yyQzcg4HdSQKIIJ+91NoF0aysyENWeXhMbbINrRmzi58uP2c\r\nprpOJP516n/FUUNfM3v4vrgBcAsTBdeCPuWDgIGESz4WjwTATFY64t2XAano\r\n1DTiPIK4tTMZIjGJBxpH1FXp67CrpN4PneMpkR6PT/JTx8khe01I105Rqhw7\r\nEktstt3uxjccaIn5VDooPaZSItibGsmhju7vJVWCpSCKAYcRmqXAiY+y4vZf\r\nKvO7k/TNNMbMPCj7er5Q39NxY7Y6j9mKWfxLLkV/fEKQnqmkWV49pZVka8Sf\r\npN6AmWP4/ZxL9ScOCzjYPuE7wFHGPdNSvos=\r\n=5fGN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-use-callback-ref": "0.1.1-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "733961aa3106f7b30ba7169963f8d96bd5edc4a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-HeDrMVK5/mDnXEPy8N2xIAtS3blqGrTAknYBvmjHBYgjPicebUGjk1omA69+0UY/Dx05qn+b19n3RbMLwA/bpg==", "signatures": [{"sig": "MEQCICxv08+sH/wxFF49ar2hZA55I4fTpj1+vN9mP7q5ZCC5AiBz19G1IAWE6kkj1O1qU+UxMO4+e2mX/Lm8E0wmrWjwKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ06ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplPQ/8CQcm+gpDHpEYgtXyJWGuXpOOpS1dcDkfjeMeBRBktRpBI7uj\r\nWjoF46+aF+Ook7EmIwTRuT/ktrh64XzYQgYgCyGN+2TbHCOGhwAMkYeGjtDN\r\n4xgyLCo7iGHlMP4a5Q1/NKj2p8bQ8Gc5b+PB+hdKvUY6e2aBOGYy8UB9rooW\r\nVOj1l9QHpMsH0UgMDLyVvx7I1w1hSAg5qyaL4Rsm0ihXxNltwoAKuuS8KmkV\r\n1Y0TUGLIWez7YjnXHtNSGHNEva4YQajOWrTwK2lBlcg0sIUHSRQSF9a+TfaV\r\n7NRSNHSPqGocqHaY9ABGTZfPY/DkidMQxLQivmVMcOx/ruw8LszYI3Mx32D0\r\nWQXoZMx4GDVkP9LALpyAxrW2DNUKakwfZGZZqVB1CS6RUb5yP47qFTlcgB0x\r\nzOUT+cDBXh3s6mfOjhYozTeoW2ABPD0dv8QlA1I/or8aF27wDmO3qPbrTLg/\r\nlBEjsxshS7McJvuSm8959JDrnCUMvo48szd8YGnMhLveFMR/FlNk8jA5ur0R\r\n0R0xPhRUwSYuWF+mRvqbVdEnSuKGRhgUBOn4io7zKqP7/xPBNdRdTcTN9sif\r\nI1EfyDSX02AlQKPzpWwUsaXHN7btLBZP9ukjC4mIaEJtpt24/AI2IaZmiAWa\r\ndKdgDEuBZMM6oOGGBEiDEtLEzvSxCOhxTds=\r\n=fgjn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-use-callback-ref": "0.1.1-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "94a382bf2521afc850aeb255888c71a05e2fdf93", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-q6aJrn7n/xvXqOCa4pDUFRX7D5G6Ce51Fn8A3+wdR0xSBSzzqd7IgXvQ/p+cB5LnMRIzJV5NZZchXVAcRqgjnA==", "signatures": [{"sig": "MEYCIQCIIXVK2sH3XgwnKkSNKoH6sZpMEBOR15lJt3jqVQCkwAIhAPfRRSG39oinFVzwFQfAUvdumrOpCr1U10KFm4SY7SQA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildM5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomeA//ems+6yhXExO6LBPnAqyHv8rpVgQD6KgwicRXIQa60H05W0Qh\r\nCwujcYxbVNBfPJ+M7Kgfhl+ZpdE68jQWG6uyLFw8JfP2LL/JD2ksK5CN+k5S\r\nfHWDnO//nGHZ+f1+YpRvX5rqfItkYvfImr/IQgGIW4+idRfJBE9GMzdO5rqr\r\nwn8Dx4vCfy0T1/rOYLb9Y3aZ2b63Gy4S06qrXTQQZglUf5oYOgn3pbmkYdQK\r\njDDNrseNfOjnkeEZEkVHLGsZFRk4BbBTtaJ828ah13/LxxJ6VfsWyKnetwir\r\nhAbQXXV2oH9J7G1q7L8fHYNTc98MbO3Pj8x+hmpJkAueJhP1R/o+FEt0V1p2\r\nL9rAjwz5kcapK1xFNzeOfbkDX8dzVM+X6PPGWI25Yun4xKob2pgiJQxDJCE1\r\nBS2gzo0TbgprQJsM+t0WWcZ9adcRpYCtx1e2MYJ4usZ61ZIQoWVUdYiroovu\r\n2CHVclLLPCFr6xVi3tktRyZVQHDSo/Cp/3phYpGQp8tMZuMYkfJY2V5iyJK0\r\nLuJz4fBnXfQJ8KJtq4kTnIjZzVVswoRtnpRcOpr43k6/ZsYcbJr9EW6e+dpH\r\nxQihlPze79/p4icHpR8JYR5xjsiswZxKlNL7hbsPn9VEzkPBmAPyYZq6qlu1\r\nlq2aTzagLc/GSJN1uLRBPEvVRCr8Aus5mBk=\r\n=eRuJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-use-callback-ref": "0.1.1-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a074125f77dd741ff198ffc1c476a9fe152b445b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-xr0ONqHc7UfdPaBLc+DFXRZQ+rMh8zrCM4NUAVTGdEQ2znz743HkNW/zm2jRnXCrqkQVzZJ/Rykb+C82EJY/HQ==", "signatures": [{"sig": "MEUCICkUWEQuYmodbjb/MG8r7/qn/2P87rT8AXdFmh9UHZ85AiEAlxBL7FR/eFYP8++vRsn8oaRjR4x6QWmfJEiMdlRszFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEthAAgOHjxfciwvk4iyxLfAMVvjn7rzaSvuJrN9h4RuIwmtT0XGh5\r\nXw4L8YNn0GZlWq5mzzyJMIHgBLeRlqIdBmlkhhKb/Zqf1aYt3EBQ9IEzSBVL\r\nbi2hotDIpiTvaTqWK5LQqrXHS32winwMpDhCWpuwzn6kGFWUqTeuyDsBgbec\r\nnb+5TkPquvmY0GVS58SnYXxv6a6qQFqlPC1weere2B+8BnKvpd4xbMHOzNzU\r\nfPrfxf0eok/VuxCTluCaPUVd13bfJWGpPeW0JkHETyOQkakP4j4E0qQ2wYlQ\r\n6vIi9/bYXzZn0/awNnk+F7ljg6sOft7XTusTSErw1lVqh5mSIeWhpR7wjaET\r\nwZKSzTo7dvMB6QzuKyxYYHBNeBQQba6J+7yK/fR8M+9j3m2d+eAYEX6mRej6\r\nEX/gk4ZY6SFqpQif8mzEPVgxo8j6l0zCKYcR+TvaTPmTF9YgBtSkxAgbdAHF\r\nBHzyCkjKyC4sRc5g286ZfWa2CISyruDRqG6uOo4i/2VZmBBptWXvnlXKF4Mc\r\nN8dw6HX76f2wFGFtoeedEZSHjqQKJB8b3azE0d4VdRfIU78KetQpkT4JPuiV\r\nPquwISPuP+UgM27HnWeN2MXO6B966BQLmakUDBe6KjgH1K5kLEp1mvFYK68Z\r\n0a1TqW8mbG/OdTz2w7+UA7Mykin0oon9eDc=\r\n=HYGH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-use-callback-ref": "0.1.1-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2321a26e91a92181f01e2a06cd277a6d436d9e91", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-9iMu23lfpHjKKDjXXTUdhjXk5SFTsMyzRcX6vitX1W3NhXiesyeeUwBwZw7fwip7Bv7zKuLxlAw+hSBQfw/C2w==", "signatures": [{"sig": "MEQCICSsnv2iISgVuB8P+crfaO1++MrVMsQvUB3O5eBHseL7AiAtGEfj279xTOY9bGBLF+1tbDPTEgCl6fsqmbzmM11h/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi4hAAlia1O91TuwyEBcS8BwtRxr8f0qNFFwpFoNjZ85sLaSoQokfi\r\na+McAmt6+drDivPQ2YQP8lWh0TGuU0//mb1V6rHjDVUqtFGTmsPNJrda7UbE\r\nC+ohxJ0NbTQAUv29VsyQZ6xrNux1wmfmoa89BuwhLmpQmiIjMsU+s+B97Nl5\r\nPx6QkAq2edRwmQYCPpTQGrvFDh05Gdi9MsUii41SChpyNCVJyJC84+uix+2K\r\n2rl8eAia70JnpSRphRg7LaqLeFKT/HBWI1DURjjRMA6uJRNFXBmPFV7fGPpR\r\nF9wscpz7+6lEdzmjj1yuM8lSysFYImFl5P2Y1MEb8YoUCZxfDWOmDSmnqfsq\r\nXzN0zcnMcOBQOZYNxhf4sQx+6U7q7m9Hn5pTeIh6AQqjveCDLRvJb6a+YNXu\r\nmVGgJ1kQYkeouytZHz3Pl66PX3/VvTjCj3aiKsW2oPyYqHJYTKQB61nZtjSZ\r\nSDudTq0YG11jdMrXfmKtXn9yDxUxD/QNySClGT75G9j3BXuwYqZhpDp7efkN\r\nZ56Bgqu81HiUedJ+AosZjihYT+BgkpFxNEoCI4BogETDFb80mfN0Bf0TBl7d\r\nh7Znm8CaNjdsMIYjvk6YWx9MaC+VhsN/0orwDKCKwIfaSqIhQdFMNqx3/t40\r\nHq49vH6mQU6p7QqWNZThHTnMR90wEJIn2Gs=\r\n=c5kY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-use-callback-ref": "0.1.1-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b6577436ac3b9b9986959ba824526cad3a6bc276", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-mFDHi0aVYYJcM724h92jEwkYOmXhtCiMEqrCkyKV4QdSL/oVobeUugNrLCsxRVg2kz6ENpx0j7qzQYnqTKAzzw==", "signatures": [{"sig": "MEUCICjkCZokTUvgddRBpwlGPIPCfd9s+qSYrCGOSf0VdctXAiEAuCwzqh5qG9Y2GjhlcOmJzsUh6Uoy3yMlJUbshka+hoM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqb7g//SazCN40Ue9oN+bguQfSWSDmEU2u+je27v3Genoj/VsqpQkSj\r\ncixMo8/tlNeEE37lBSgBIjhcoJI4ajXFIngbHZTcMWW+00IsUgFULhHa5f2i\r\ncdrWkqbtiE6+za2gy2XYhyKNSnkm+duztvUixcpkTbuey0Hwb/2/N4T9kNz8\r\niQLQPX+gXVH5UnJmvf2+vkrSP1hkU/EjOm1yyCuY/By80JVJQAg5PFSH2gXt\r\nKyOBKBwB6gXLRG1Dy1TQ6JiaDMS2uJZ3zJ5CBAX1GxHrdo6nObzneK722efR\r\nzAWKRO2EnBs7tbJOdsCqItLvJu1iHPSF/kgOxMzYOYeNBwzltmar5KfexJvF\r\nXmNTCfq2Z4iVdN/Z6tuqEtSuqurgy5a5u9VXgEZWriscRY7yKfv1l+1iBeaG\r\nQe8HnwfW5gmZA4vOAgRsIPm87z6J07Q7oVDsz/itLXU7VTzaa7K8Z22JGq4x\r\nyaQW4OdNZU9pBw8M0pez+GszcUymz5FW3PPAUtn8vzEmBW6X52GzK/phLv9s\r\n+iV3JWz7GDfHofvY1XhUpEe4+GqBIfP4qxunj+6JVJwr0uqJUdsPsg9eXeCy\r\n/x6ZOC0MdDd4N9oHhpyStDzgvHCtlJdlAFadPimcH7r0DK1dlVRzxxRhqlDz\r\nLj6zdpCUss9WAVPDnnmBPJiEP0N3Bkw72nQ=\r\n=GfZs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-use-callback-ref": "0.1.1-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "377dadf3b682f448c481de7ae0748ad2ed772bea", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-8fGFbyFIVLWLOvRozncJfgHHnFfV+4F/gQsz3gwConojI5DXLxr3KqXKu+yNgTBCzcFuckVF0jmqcCtGuryoaQ==", "signatures": [{"sig": "MEUCIQDr0i35mHCibLUroA/YZaZGXJuoKMzXUVtEDzC5fiHuBAIga8QiYpGCZPPysm05lylIpV79xOpLHi5AlXhPvuRZMkA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWRw/9FtgqX/iEz8dQi2JNLs67i3PIUdBkGQkPgbd9KxqUZXFHigAO\r\n+Ts80H5fXwiy98XMYVQkkIOCdpjbsNAZ5vZ9rnJqjdSBuaAhiEwCgdPQ/zf9\r\nKwCW7AeVVOfZ+Z+ok156xbUSq/GiWiBGNTYTV/JbBsPc0fo8q6xfZV0IhtWy\r\nSqmxsAW0wuTsf4puk0i5ScSDMYQqndKT+wGK61ZJjLYgjaCqUAyKRQY4difF\r\nfZI3hCmqDsm+x9QTeyAVSCmJ/VnwmrJLa5OdW2trxubO4UPytlgUHkhCeGck\r\n8DdQd0u7rmFH+9jF0uZrXmbobMJSkHkjtt93oIl0uVcc3TXLFO2lCgcR9zPT\r\nRKT/2kbgH8D05lKSbB1tnz/DtcfSH8OmGfsIUoaOWlOdmTHheRoN9AIFTwC7\r\nx3xd9zNUPXqsC75lIXlkCNeAP+UZEMjaRi+r1WeyUnS33wP9JJX9rgTHIdJT\r\nGan9C+lPdyoUxkRrw+WFl0lHWq/yHN0bTj6akuBLWOs7NTyj+YvQRD2aiWEZ\r\nOWB5qBRCfVPQOWvr6OhQKnIzTvOiiEZyPhYWk4J29c0qi5vboKDWoEofleZ7\r\nMoU7Co3rJZ8h5ISiYug6CwuA0KjUOjMO4rb6QBwBiX6i4XcTvH1cdvLpbiFc\r\nCyXr716BHzu6meKWJpjGHZEUxCCPuFIYt4A=\r\n=cwI9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-use-callback-ref": "0.1.1-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9f219dc7ad4a4792b8aed422f48edd9d8e8343d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-//8dUhEMWAIae0QnAusI08eetzs8FFgI/kjOdjClWJd4ShGLMestRpNZ2XiITC4W3UQQoga/LyZAyIvB17J02A==", "signatures": [{"sig": "MEQCIAOqDZx12wdHMNJvmI47tqR5U4wgKc1IYmdA3bP8UbNjAiAg0bII/h7uijOR37NUom4KuNHdh14sPwRwz6t5lXixsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHbzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCjg//YHNvtJg7FFUG5UKNW4jC29msSoTjeUAS0kzTTkHch6izpo/N\r\ntaBLld4OpBHiNoydywu3yQO3XQZizbYTszK5RpYXb2afPAA2hjzx3rTQ8q+H\r\n5UA2LzfmrcYqbQNEwbGChhzDBdJWF3V4WbXrkZyGm/EB8tcyr4h7KRsIiSL6\r\n/9L01NNJzjlpa+QXd/CzSM1SFL9jAMgJ56LOF43iU7iLhiJKzLccXL7ArF5J\r\ngm+5aG+hOuwseBo1gyWUVOJucM6sBZOI95yrhjQeigEKZ8J3aPEt/IRG0uJ1\r\nqhN7BkaQszrl58SkD60tXUUsowasZ6Zyhhxn243cVvYQHyRgKbUtKefSewVg\r\nAnikZP5Y/HWOvoa9DE0EqZg3E9tXidF+Jy9aDWdSOOssldZ9PXYD6DudTOQ6\r\nRtiwNO6jPuoMX+rwdzRvuDtW2b5dqx35zf3AKU9j4EK5gWP361ip8u98YCMU\r\nhm6Mrl5YjkEYMRmNpfqag8BSWoBr8fjbHdlHndNsEcQJv/0kzY+gAU2nBKYj\r\nX9WAFI7vkdmLzO/CBe38ruZZitheBQgHDaqkZZrcTi09FegyhtM4gDgbwmXC\r\nu7TFsC60TJDWl16xZbUpXf4KQjcB8RYbsH7Smj0FfX5ki45ilkafpMfA6lK9\r\n8oD/IcOgTZkCvRY+m/atKqxJpMPmfwPwZds=\r\n=Yadm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-use-callback-ref": "0.1.1-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "65581a339a92dce7b16887b624067b280cf8f9fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-F4prRfypqtBWQ0cNM3xEar2ZyqXfzNpO/VQnIm8V1TVuTWO/MY1nYp6LOkgFlyUD6ZBD6NJXO8DOAuNhe0S4vg==", "signatures": [{"sig": "MEUCIDPFvd9Xon0PnMsLb+UGk2kkrVkYNs9HxZ8DhklwgfXDAiEAwBNKObHxuWkfHqqfujlPafYZJujZX7BFeEM50Rt5e+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrcuw//Zn/bKQ5FBeIfwF8+MyDLTLZQZ0rsv2SpBjfYST/uF+2oSKLv\r\n9dtb+Qf8WOtwf4H1hfFA36joozxWy/Dy65NYvkwqr6WutqL6VUPMAl7xNrl0\r\n8P8dmHkmx1vS2ZtkFTj2tO+PWwMEfommVuxmkbhQ3UvO9mxpBJ45jDNd9R53\r\nOnF8TWiDEbRdTrfb15GtSn7SQaOpROFoBaIFNdzP2iwUE5h2+EJUHDMnDQDr\r\nbi2dB31IMnThHdT0XO+NYPv+9oSrCvxwGorPKobuYAcRlh2o7a0uWQnsy6Ek\r\nGAC35y8oMfKR/yk65LKELcsKpWHiXgwwg3RnQooIbkL2+Eoh7SODTTXHLaoX\r\nswexRtDpYInU56gyEAfutrT2PXojA1vB6HRUwKhLYKnuJQ73faLr/SUEcCMM\r\ngrCNI6EOLEL3AWjD7fRcVaYszY2VgsVjHgMjY6vaMwhcPCXt+b194NHgyU4w\r\nTI7LYaBjB9/S6tCxixe7gBHkkhEXfrYRogsZmc5+EZ3BWeytdckfO8V0i4Q9\r\np/9/fhBMVMCd9bteAwg4OI0c5qXYOks8NZZCf1t6BMW7NykIZ8U66sfY933y\r\nXagHr7DT2bwPnIPw+c97AoRoVBl6G1VgEfeDCwy3LWS/7OjHw5cHl6X0vge6\r\nWF6nAxRxYkkm9QWiZKUDwqZAGLfEFfZAQm0=\r\n=mk3/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-use-callback-ref": "0.1.1-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4b27b0edf2ba73c919973dba3e97b229007ca11", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-ELZEOPYXXtCh8KK9wlurk9kGGJC0rqFeP+aNDglixBg+OV0MpMGlUauMxLSaRFcfvUiKQkNwSpsFRO/TNScUSw==", "signatures": [{"sig": "MEUCIG4clnkv2JLkj6B8mQSyiWmHg9/byAKXAxdkRUfWK+jGAiEAqZBmnr2n6MNKDGuseXDxSSLSW58sj+Tv7RnwjKmH92k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDaw//U2g4DWALd5p+E4cUQZmbEzr3xbSvEP7xBiV23nyPkV7wUPRO\r\n/w4qslStL2GNt8QvCengmfFhU4UZmFy3ghFj/oSvrbxmXpfHKmivI/lYF8UK\r\n2g4KOUu9E9Eg0J9nDXZGN0MGKUEQX0/lcumuIlHad22MxVA51lYIWQ8ZJSEQ\r\nFzWxb1ZmuB+bbDei09qiguds9w1G3LSArl9/7RuDikuKQNs15gUMrDWJbTca\r\n7k1RhJ8QhBjtCPWwMFFIRstPdHQ3KUR7b6LI+37PWvO/ab7zysDjSlqgvt2/\r\nQ0jidcnEWhydsG5SaFMb4VVdzJ8BMyNwCCqHbuBgsmDoIziekcVbgwfZvJPu\r\njOSKlwpkVbTue9s8OlJiqQ22E0hNI4inPomn8rrTSAhX5T03oI/7JKOz3a2Y\r\nuqy7wzgMzba/4pcz1WsFKdl+FjecimXQf3EUXds6VeZh9ElmBkC5SDIU1J+A\r\nORi4eS1XAsdUMWKMlElf0bdrWq/012VGoavggRP0g2GwtcfKIGrBjEMqlidc\r\na8lSuUoaZvcXMo+lPttU1Yqv2HvS1E+Q1KSMM5kt2e0wBC0ZuBQtPJSWOtd+\r\nkIA97opYggcxET5OLDCoLQ6hKNxxUNbRPqDlPCNHAjBnHprDcX3vf4gUFFLI\r\nSGVTS+5EZeUUmXPUyl3ZwPJBEuGcNLc8yWQ=\r\n=xGvh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-use-callback-ref": "0.1.1-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f7ff3e1b946ee291f32d9a419369673b9d032682", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>L5+BdWUPtH6fzp6FwdsGu7Ks9Qi+QccMLSAaDanvXk3PtVBaYA25JrUjFKV4JiNZA1/ii6FRaxYPYgO4o5Ng==", "signatures": [{"sig": "MEYCIQCme2qfzQ+zIy/fB50b0XhozBKXjmUCb1K+b3MXfSGp9gIhAMztoVTF17VLhIzHefvMkO0mW5BFUQoZuz9QIprXIhWx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0H/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/7Q/9HF5nr4yjb6Taqct2HRpVLRaz+qf0n6TN8/K19dOzFB2t2RAa\r\nWiK52VWn7Q6S6SwlU6oV7w+CVkWY1/TA61l5rX4c7TFuGf5oAC2xTRiL7GJy\r\nVlPxyVPAVMzaTgwaYxojULkbgJsk31fvhoNFFji5yMsqcwkeoWuCyCPdsBL2\r\no/h/kGRvtz0X4xD2OGWD9JbLj8nAUWRc0CKvcsrLh72B8Sn/IVOjd1RyWPr9\r\nz1kcST21ml+W7qVFPhKzW8lhYA+zn1Nnqd9YmAzldv5BDAQ6RAjEoRRrJfhM\r\nj74H4szyl8xaLdke+pK+sCfm5SSK+fwSNX9z3wZPpxld9kPUOfua2gibA/Dv\r\nSX7v56FnLalI066le5CCVL1iiV8O1O/kLDhnsc44d4YcTwyQyOVADw02xNUQ\r\nhTRr+VSyCMEgNXiB7iFtijt8CEY3UPhhvT/QrTgvrKnVNLiB5IECXqmGLY16\r\nibFm8L/QLrMN3WQQ7X3ik9HJk/awfbMYyH4lCVZWWieHOVzXzvZqADITABvE\r\nUp2/GEf8s2jBVqv9DH5o7sXLidrSzvT+f4SsGF3OewqyoPeQ5jpYph+b7nOS\r\nBLQoVehHcxPNWDiLwXA68nQ6OhzFbltgEGbXaaxDNjE+KbD1pVoremWBPNGK\r\nPMaF/S0AmDCIuagW8E5Di3d1F0adUcoek/s=\r\n=pYsR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-use-callback-ref": "0.1.1-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6ceb8ffdddfe1046275172d6af5037a1719b25e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-zf3a8yU/jrdekL43Tx8lA4q9OM9my0jLXpw6V52beAJEqzsDk29ktcIfS1Wm1V4dM3ekn0EDzdQZp4nPIEzvRQ==", "signatures": [{"sig": "MEUCIQCogABdbxcfQ8RKf+waLElJFg8CJHV8RJ9gmpwZTKwk/QIgGVv3NjSsFeCYr/s33gDBw4xU3k68JYjl9Cm/sI6o0Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt4A//U4H6e+1ei6f6QjG69hbivK/kuKrOkEawn2UzSClqA81rX57q\r\ndJd3HuOUjo5blSQYETaTRXuApXUUQ7IFOxbv+oEFk5mS98fFi+1dlIfO2YHM\r\nvOVfq7DN/GlXh2FSe3urTwexJaqOoP2zDYGPS7TksOodkiMziFTy3LYetQAb\r\nh7cyu8tHW3zvsNYyyNZEJCb++o4YssgbUx4Lq6EZbOlZN9gBujOtoUoCqxIv\r\nKkAjeBRdiJ7UwFyEggmD32uBWZZ9plLMLVwxIEXp4b4Ny/x3eb29FBysGTnk\r\nQu7QnR5SXrn2VaBSyiBQgyNVs0A1RpsSnLusiAWivTMhQwgQvegWBWx2RSAX\r\nv4E47t2fZDb/UR1a4HzrK3Vv08jCXT5gT3lonAQNlORQvTwFY8mrTD7AMkBg\r\nnwYHItBJSSXnky15qQhkXPi0Kz5vnnWJREuS0f+5m6XRjTJDHLe/skwP94Il\r\nzSS9P+SqVqy0bvBBRIcE4bxJ+qWxU75PIqXxRe/SI11km1E46I7Vg58fE3am\r\nAYobQ/NoNYZBfjK3knatE7Ccku4aRsqi3j33JqkMasM2A/Lhm1row/POkmBT\r\n+IzC0092qgNKWRi+2oHa2MwDvvbRGb4MhdV4XUZxucS3pwyhCsXVXy6rc/Dg\r\nj44OCrNxJPl8k3k2/yQg3E8ME3KouHBRdvs=\r\n=Af9e\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-use-callback-ref": "0.1.1-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "08d1fcc2c98506a7e2097bf739d1b0b4ffb359a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-lOXBqK7/vwldNa5M9Fwi2OAoyvAGDtNohfeWo76oQErHUtlR85g6bX5Z37Cn75kWDfg3HWUDOEF0R8oNNz/pXw==", "signatures": [{"sig": "MEUCIAZyHMcwC0iK3bVboTczaBDkKlzG90OLtVA44+ljsosAAiEA6Wy/nFJzyFWqvTDWHuv8LKpLMF6VuOr991Z1oLzQv5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj7w/+MeZYh5PS2B1VV8IEwsNvz1wtW9Y3sYEqpDoP2CS4jipBdsxK\r\nPBn6H+L+pDoI9gJIWiy1dtGEdNEXBhyUrxFfhQGbXfICfoZhUP1xq8vx7SG0\r\nF2S7po263MPBTHbn1n0JmANztZejEpk3W4ZroPQYa42orimzm9yXB9p26ZY3\r\nMEwGXyoVhyUliCfC/EaDJTP9qNOlp7NNrAqmizQkQSpEAvw6ShheSVWJUjoA\r\n+GxXzqvvqd3LGNDAZ5gccCmyen8vNZdK9ruMGDry7qsv+XVUQ7uBJsy4Qj5e\r\nlFhjHyYdyvgI74blQvyWkeTrhH6Ys8uaELdnXgNK4H4HLEZuTu72br6ihAlF\r\nw+oqmhDYssuDOBuBwGknd6bWQB/nwNndZRkBxfOSK96XIW3mYUeNrGM3PySH\r\n9aeayLoiqe/9iGBzspItLdqCIfxYFmrK1kbZ09yfYqSCZxzImHUxiSyGPqu4\r\nVpOB7Jsbqcv4BMJwz3fKbsmfrx7W7SzwfmyVUg0DYWWkXhu/bViTQOn5mp/F\r\nt23w6wNUX3kxTpbOyXiw3ZaZLheZaBsd9tfO6+PobP2Gh7+J/+F/s5aIuthA\r\nU6JZWNdlrYgTy2X6aKMsH0KbRqiWG+4vmTXtGtnnkk2E8YbmuMxBwZhBqXpF\r\nf3/KTl/9jPuK7nPuYJcaYzUwMF2oXu8AHuc=\r\n=nhHm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-use-callback-ref": "0.1.1-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0a449fdbd4dc8dfb23fc60c43889606c5b4e6e00", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-FdncJ/E7ppp3U3eGJfW76KKFnqOFelcfHq4RDvwhRqYBWmiE9y2zzMIJ4fVg7UyuGhHoZUOusQggpfcKgBK3Sw==", "signatures": [{"sig": "MEUCIQDdSJQMT/Y6Qb5nNlwNfp2FAx8B1/jq+SfrJodGIU2izwIgU+KlslNXY/F6ViGHtiFc4W5VFTyP3H/H3TQiXmXpWTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQaQ/9H7mvTVUcXMyqM1gG4GbxPnrLin7woY4adUWNO5auW87NJydg\r\nF0Le8spljtGe+aX5YlihE3ETa50Zd/197ubwMFlXs21sGt0mGo64Vg5RtTlG\r\nXBhzYFv2k1BrbZRUe+kbSiYX/amxmlw5gTLjYFoJDTGaoTYs3keyGc2y3tsE\r\ns9Di1SftJ7uvNO0vpAz3p6RCjSnnxTAuuAvH9gyVs6ZUoWCyzbCVh4s1/fkg\r\nw3Qs3s29RQx5yHc7JxRjHSmG3CqH3TJxy0e/KL9ywd1yxYuwLotGiY5jR5sO\r\nKdkFwMczcXKtjg7Rwkn5vo9llJcyNISNZGK8zlaXBzVTiFrscgzhOHVWtLwL\r\n09Bqb4ehlNuxF1FJciV9nQ6kNZyDq3V4i1yRYjcYI7YfGDQkT24DgkpKlw6j\r\n/Sfbb5tR5X3hJIl3AHlePDZJnz6gxPypeYmOWc6IR3QgLNAyA7a+QttcU+WK\r\nGM9pOxc1ldTjK8MSY1Y4R3BwhNNv9UJEA6LiF9r1qUZgsn1BFAgg1IYhODNR\r\nUHCvSzSaPvpCz/LOsrzMmDnBdBaHA0XPlGLoK894R/PJV7Yzljq9gZVfZLVV\r\n7270tQN5raz6HxNBx421wimFOBKUizH/9/iDsFJ+TZTHWLNRUnhND4p53JKT\r\nt7RgQ7IpIzpQUtkTXSFN/85ka+4kX3Sd5YE=\r\n=wE6J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-use-callback-ref": "0.1.1-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "de2fdfda36d7f21456964213e98b491a2619f5f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-5PE76OmQu8tJXJ8dBICCcu8Iik4gxxo3K6bhofq5usEIkwvQ5NtvwPVdk/CG4nQACHbCHcUu4ycolrir1olN4A==", "signatures": [{"sig": "MEYCIQC0uWJQeiAWH7/Y9VCXOpO8bHWXyBNpj19AVwziIAPxewIhAM/oe/6QXdCUI1zensk10EsEoqHchGKf58J6ncbJ3egw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTtBAAjsgxeZre23wR1WtqR/lfotaWOIPFGL3bnoAiucva3WrgRmjJ\r\nDBtdEWZYBMpwosjVLS2NuzjMulasPYvboh+ChZHM5q1TV6OpUH1Q3JDXs6Wb\r\nFg4Miu+PXfrrT0wWPpWr7/eqr+K0vpcVNp71S6fUcKn3igsSyHTysSJJcqfM\r\nn++dMqtdimPWcbxsG6QGH/68CRdTuB1DHSFgLyS6BZeVNV9Eoe2D83x+M6r7\r\nGMI4H8BErIfzLYVFgkZg9TZWmUJXCt58kxW+ZpQ/1ZmjfNu7YyGSbARccluP\r\nFYj7nWtDq4q5vV2wmUauz/pPspVAkN9ghpm9YTMHKa27p8KxDaLBXQeRCw0L\r\nxkFdonE40UiADCJ5RivbaeefKTWnV6hBp39Ton76pmF+lkqFEXRQT0jB5U9t\r\n8JU5Vk17fN37zt5Sdf6aTuL7NHnDiR4tt06Xnckq5LOJ6FCSnV+H24YRFXUL\r\nrNPk0GCt5bdVrsKBvCwdqkxlXZ+XMZmNZGEEUmdI4UwwXPiW2b5lb41fFpoE\r\nglS6RVgMdwMpwJs04cX08Jklucr/CDK7AKd9EffJc7OGYyqisf1KrvWU2MWg\r\npcnJ13sM+iBzzi/MEt/MfegNfgW0AbLJpQBeHSfNPSoa46Iz8JOyZ4Kr7X8/\r\nbkMJ+6OZl/41SBw+qJ97LZg8Z/6u4zJg0PA=\r\n=XWDO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-use-callback-ref": "0.1.1-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf74ba116433a3568dad5eb0368e13887fb63b05", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-KyS/Rm0DXnWQJsYVsEPU3NZljZIRRxfoyrDfUycB0uPmiUvPxmT5AlQkUnPtszjaQPULD2twvWVnz4oXhOHLkQ==", "signatures": [{"sig": "MEUCIHbAsGghrM448sy8CLjS3Rh2HXmhW4EHfrdxww3EAtoAAiEAnuLYJ87Z0P1z22pqudkT+WZo/7MbXWE2ujtSlprqTw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6HhAAi9umIh/SM+ykiBgwKUHsIG2Vd32ySSE4IqcJU2w7ZJysdP6+\r\na5QVZvD0HnoU2nrY9E4jBu/3lxiaRdGLBH/NSMoIlRaX/AMS8G2VWG+AQI+X\r\nXtP/Dl6l9CpnXycNpAie92PaGhXdfXxv+/OFD7YZJ2g+oObu2FoCyJmEcLJ5\r\nxztEF+hMh/YMoH7TxhBHvv32cYJDL0tWTlzd78blZ0GSw6XbwtGPcoGQlsLG\r\n7u1LXySlBbgnnH3x3gcABR0F6YNSI6WMBLi6vIOZ0AvQrNhayAWWzpvfKL91\r\nvqIo0BFzcu1J6zg7DxqokRKEUsbikJ2+czFVuiif2kL+6eLNrymr5hqD0/lg\r\n/cdwMFqGkzVEc7QwvyivF6R9VAhLGkIm1YT3/THbKXwhHuwGWglCZxYdCqiG\r\n8L/OZ4p7gT49FB29yVj4OQpeDww3jFg1/Meo2o/xE+0V4W5EtfO9qkMdu6Pu\r\ngLzMzOb+iCZv6CpkAEsbxcND6C+PB/HUyb3d7x/wkRwdUn9lQe95M3+7x5PJ\r\nRA1pL82QGG0KBx4++onTQt8dtQgcmVgMMHIHNmUjfB7olNymLUtvkXruNoXs\r\nYqqMjE+f8DMEsCtkbCP6HuJI/HCnznTUopkVWKzy8w8rGHXazuJRhFY/aiAw\r\nZSmX83Z3RT5NRY5DJ5DtZrVnoIO4R71G1yI=\r\n=Zf9Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-use-callback-ref": "0.1.1-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9fd002373bad08e6325b5c41016e8ef3a893095e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-5I97CpXKUzJpgTSTNDiFRoNRAs9YwfI3ygVvnx7/El8nKB2OK4eUvEvwZHZujLxBgcMx64iNFblZTbmWWHYCMw==", "signatures": [{"sig": "MEYCIQDOE/M/Ldnx4Q56HxmthCdRNu4Pf4mGGkp+wP/CrD/0cgIhANrzkJS0PBmkCVureMb7sdfc36loMwO6ANkzDQf0h100", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6tw//SWT8nEhadtmXl0KgibMYMebNKA2TzZFoj+2JBOMSFI2KkBwU\r\nUIehm9/umpPr61NYe+Wa7wdcNR8UJq0e3X2hWX/g/jWmQq1ptP6djKpnCGoi\r\nHNfaWLq+3zRECwiUNKNgIQu+CKkOB+OQF42/TQJ8L5kN8yiAEuC8WRuwHCMT\r\nYTFk7bWOQYQIdZL3aUHLgcq8xRC2+OsnobLmtXYAp90T6WYhGQIs5V50IZPI\r\n5dJYi/++WwFeg<PERSON>+1xVAvAD9BBqyWfJdFXCcW/dx9O8xQu3E+WvqxP2Tk2Y8i\r\nZrwgv6ABFiKCka9u+XN0UA9gh6Q3vtn9VqigUsntY3T8N78phPCFNP/fGiUU\r\nAWP+ot6d47qqr6QmUOKnd+YCFJyC5QbvkkRzdSw55mTs9+HnOlwQ4vMQnxdk\r\nnKtkwo0rNzGcCa49slhCdTvYVGJXA0m0olFzsySwMkGatDixrVBllRyNAHqZ\r\nWcAkkkt7q3w5CbPSH+PKJuXNScjbvRr6Ib8IhJABFa5IS8Vq0+ycHSCgyja2\r\neqWHBdMr/p0NBqxlKshz0d1WIzH+8EjjD0lsr8o4J+Iceg5zlRpKRxjza+jd\r\nnm8QQ7mXd+JhmAyI7l84q0xQyyuUZbQlAxEm4lfbmbXgygZpZk1OqGFwaVjr\r\nofHgCTKRz3T/mcAunzb5S0QIXOt1MtBAUEE=\r\n=i9AX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-use-callback-ref": "0.1.1-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c300e6111d4fc38fce5dead7d5384406dbe00e01", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-3PR56J/bn4QR9xOn0qyhhtrfeSlAOu5fWakZc4zW+ahk232m0coUpZlTzY1h15VKFvwjNv0gPpKoopOJLwZTSA==", "signatures": [{"sig": "MEQCICfX2EvV43zhwzzKtV4EuzaUDE7INkrMeSj2R7YUvO7AAiBszVkM5WX9z6jkOxm1PaDyYcu3BhYGgJ8kGOdqxo3qKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK+A//U4qnXDBXLp4E0Fao4Wzj85VdhEhPSrnUGmUq8LtTAxHWxUtq\r\nM9ivDZv8L6cDEikVASSL/gjFK1SgUaT1p6gq6/gRXSsrilpcViiEUHY/C4pa\r\n9tZsNo3F1xZ5RQHjsQ9PqgAuIriinrT+sFgRk5IOviLIHa2fWyOhhQR+dYe0\r\nERPfhuQ2H3qedY0aPvKoQPruYoj+JbsolM0Jc0TUvEzq1Klpli3cUuPVblZe\r\nL4wY81TDltws7tNzBwpSO189hvPEZHKH0C5Es6JcuSITcf9K1/8eIerj/1kV\r\nPM4PsTHiBX8V+dwaqRy1o/W1dMXFU8qCo1tUIcE8pDsoQRTRwgnLq2pTWxD9\r\ndY3kyQobwJ7Mlgv4SE4CsJi/c1rJk/6yl233e5dpusykHCSK0d2hpMpjl6zA\r\nJ9IupHAULogJnxzYM7mUc3qnwB6c6JAY30byFMTU42X4zSU4gQ7QLip1rcrN\r\nfxDjdrDbe73Yc8I/iZVg63slkmceM+C8fXdMw7idDJPn6kTx3uuKZ0ZHXd2O\r\nNI2U3uZ9+TzZQOxNeeo0glf1YBQMvMWakPgzfV9ZDUr3D6ApV0bqruf9f3sr\r\nI1ZNY2p2PheO+ivkvwWvL1IjcTr4hkkA5dUBknoJbXycN24MvdGi1aDVimdd\r\nQg/+00f+/gwFD1e8pkYRDhGQp2re3oYv0zg=\r\n=FN2M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-use-callback-ref": "0.1.1-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "39b6c3caa38572c2561cfb46bd65236cde23bbab", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-UXGqpABR5LMJCojzNWbOe9tDc249hL0u1x1nDmDXDPnNW+01vI58PrJWJkFcWm35nl73tg8gUfriJtKx4bkjdw==", "signatures": [{"sig": "MEQCIA6A9xdm5kD+b4Yll2i11Hlp/7cic3SqwSRBS3Am+mPNAiBzzYSfQ6nLwfaBq1iUag6yTT/XkorZW3UsffPERYBDCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XF7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHwhAAouyG2NV0J3cmpKmgj8cFhYdejCSErUWx+bzcKwT+veBmmaEu\r\nMRFW73Uf5gMoGjsDJhM1T5+W6N23Q/K2bCFEz/GxW2Wug5ckueXHVvfiOS7o\r\nPR4P5WRNDS1VDkC2lT1ktfvwQYhq5QyqdqGFD7WUbX9gRMcviZRByZNNg8KW\r\nSKdpuTlqEhavW1OPEVr5z4bmjIkIf2EQr5EJ+ZOiL//My9vn3KQVrJdopCmQ\r\n8ZGnSj2IPevr/z3ILCY7sLcbc31rS6LnUd/bg4Ku4tuDcGBqpNRWZ7PJ2ckG\r\nsGwRLFJ+CBXhLDW4tqT+NLWgS+CHWG0mgHPhcpKGkG3N2mn/no2CD0qrpGZH\r\nvewjtWgeatGnoN2H01pHwShVW+MXA+U+bcC8Iu+0raGDXQZsYaH2s1aSSkF9\r\nlDMsCezlLo3ImcPEB7qILdRlgllup0hySL5ozwf2seeiWqDlOMX9Z6LgsSX9\r\niatTbtagihTml81DOkJ4LIV36AdOextnZjpj/Flqt4uZQczBXs8BT013X4D7\r\n+W43gjLA7q/sCox0+r5sjNQRI0VMK6QW0cBv/97CSjoknJ3ZjAY2+b38P1k5\r\n563Yfpw0rjhDvwWyUwhm0FonYx8J9mqEarAjGGc1biW6mn5hCOWtWBKCUyP9\r\nKEXqInQnn0FlFMm9Xnb7yCQ+/VD1YQaK9tw=\r\n=pqw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-use-callback-ref": "0.1.1-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22987330d23cee397734c38a3a0601f104a0819f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-uzkgNTV+OEd72BK2SgoTym5weWuIEufjjGBfSjfbJCEPNz/SSSdDrkjmn78jc84oteQ7DywmMI/a90EXalSLoQ==", "signatures": [{"sig": "MEUCIQDolIPOuALSzqFJ56VB9rzV37dys1K1YUaizDgTf0pTIAIgHcUMEZVGHfAXD0O913xPWVHndJmjRjXxoDKFwZVgMJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGdQ//cPHOoHCq3AFJscKGoH6AUR9Zp2I1X2l8IRrZWsCwWElTfv7q\r\n+HDZB6EsTFGpA0y77xGwB5wh2tVjNcfkWAPj+emfFhCfRaRgQa4K+VElf2q0\r\n0HZZ0SbvIQ9SBluDHMWJ1PlztRrCWBE8ZySEa/I4kUDe5QaTYBCV4Y4IPK6N\r\nJGeZAPBYFmSSzgtbTHsVW5RqGZdASX8hDyuvkQD9NjZkiiziWZ6FVtcDJ5mK\r\n3eaOtuUa8/Y7W4ew1AvI9ppPfrNyKWFby1PD76+U91a8JBm57sMOdhAgzn6f\r\nZyTwO9bMkwNrmTVTiQNQKL8NhQdZD7M+UehO/3f3vfMsz7imdPTKim3HI10r\r\nDjl0eUtQrSkyraDyYtk8R1jNYoAxbgLysf2w+PaPRh7Io+mvQamJpzv1+dxB\r\nxJ7yY2QXZPCOcWz2Hksd4ASo4mC9sThMBYdyBsbENyqKQg6R+YF7NzWyNJDk\r\njYteco/TFVGJ7amyZwDj/WK7C/7x9rgdH5iw0uZjslhYZr/Yk2B/7LQzEc9h\r\nOZaLVFp8TlcVH2P1ZWJ5FJRiPr0GotR05UTy9RaiF4XxQakb8rchijtE4eDm\r\nn5kyfGe/MjXy0cNXbWZ3AHKign8MJ2r54JnnZq83brvSuxBUYxLGhkDtRY0S\r\n+fxK3LRf9lIH0qwt5llEYXjktRGFKkzl0H8=\r\n=B4UW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-use-callback-ref": "0.1.1-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3941a6d00ef6d323e7dc57d39fda4b5bebf2375f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-V1SUkrIRLZ8XKhgKE50u77X8W8EndKNXtFwXuzSGTBXNvHdTgAMOJdYrs4RUoJxNtjq83pfPO/Ad81ZBfTXI9w==", "signatures": [{"sig": "MEUCIFoIj+D1wDMws5EUYWQJFIPaBX1Fbcs9Wir3rmIIJFltAiEAo0IKxn2sQWH95yS/Q3Rrl5RoxeitFKZoClcL4wU3bx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmov8Q/7B08lGRZDIWZ59z0zkJL8dT/kkTgc/st7hMhwAVjoUOxd3Omb\r\nWJS/v9EG3R06PS5Gi8oqoBLvR3Kr0NcBmmbU3FE4x/ds+Be+kUj6xRh/2a+h\r\nQvfn9tfV/ppfzWb9g5SGWkvWoYrvIEGs/P4g2XSqYbw6JdkHXCG/QzyZkinP\r\nAaf/2TqGJqZ81SGXNxlc9lWlKxDfIqYu/SgoeOxo5o74mOkPhxLFYkuKemJX\r\nJi7yqar3sxr5MQCtRW9uRn8qLad0M2259rl66gImKg/EEYVbC9M8md1zDxSn\r\nq+g2InB+nVC7sO6zoFzVXEwerxyfJJdWaQFL1Tkgc7kdwrOgR3W29NwtFSPJ\r\nG1cu3GYGKktHnNhvrEaqL01JO+9OqpJrYS+jz3ED8Ironvumr1BDoic6dBOB\r\nJaghKGC/EIDYGlng8SmjkcFDCyrymL18w1PFSyER5l1yjg0g4u1z82nx8kwN\r\nSAzXB+OOL2LiDv/Cz5zrFzLUzZdLcRJg4VK5Fyf4r4M/sgVsB57yKdBYpJ4J\r\n4TkyQ5Kzrq/sqpKhWfJuKRwBqF0gba/5qGBfhnOyyDNFGibaLFFiKC7pyCYv\r\nieHvmdqiGbR7+Zt3aZL1pXaI0s5f3DBu8BuHbvnG21HZXY4RwpJnpdShnkkM\r\nWCk4FNrx/hnZ9eE5PKHx7t/cHfHNcbXxMGk=\r\n=v+4B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-avatar", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-use-callback-ref": "0.1.1-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "21f3f7a8ddf374c7c31bacc50aae4368d83da6db", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-JFDwBZJN15XQfsxiitDJ25LZipta5rJYFZL8R28Bk3y2+Nxdd6SmIgDLwGGqJyl7N1j3WS0Zho0qhaRuAKaS8w==", "signatures": [{"sig": "MEYCIQCPhNsQQmAIAMyWF+WHH+Fi9nC5RAAE1NJDwlRfrLcMKAIhAJTZqF1FJNYfrWxZSFJidS15RynexUgQO8YWkO+in6Ms", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoufg//aHND0nqhuc0R5RBNtSN1NgXeduVYbLU2JjuPJbb33FpJ4LVg\r\n3yN5lWplIHLIg4F5sQBgrtIeIwLRP3iFb9SRuHE1asfFMf46IT6ZIisAIR+3\r\nn+/NHWSdtLD6MzgjnKd83z03lwXutyEBvEIpL4kxQZLsh+s3ZTuO4VAErBf/\r\n3R/2/iyv1pTA9hkgbnYlqMnTGae0o0/QIpHgONOAZtkRc2tAqectLYgO1k35\r\nsGtdivB5HBGzIqqOGw/9ibWE7glIenu/wkpBjJdWQgVfKhZhwHgxe8FkUdQ5\r\n+UB3ViWwaQ50LDKm6Gcs4BV1igrLQXzlVCjcmWUmNHCsyKpbpqRmmGYt/2Hx\r\nuF5b2cLUw0v8OzZkJWOr87JI2I1wxB8bHslmy8USLUDE83T0JOQSBb51nYzY\r\nUmOAG+WnjD/kVfUR86lc0QgZjxdBYgq8b/mn/ItvuxVlPr3cyxdKInIfyFW6\r\nYbIMg6/ylIUoJEk35jAbhcwQ4xa7XeZiNpUzKNnZ/CQOuvSy/ZwNqo9IHj9l\r\nyddPNBvPXAVdCXs8LR+JCUu0J3L4U9WrRfq35BmtJzYDaPHTvMB3ISGdszgz\r\nR1I7ZGA7hOkpBRbhablto0Y0irIEvXkh7LOLqFwrjb53bmi5DGaHHR+pBgG1\r\n7UpPYngIQ6lZ4Oux0XHFGO2PCGci/+StOOE=\r\n=zxoX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d073385bce1b5609180e2aead618ae4883624209", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-K6jxfDAYnv7ccS1UC2u1EsyCc4m0+YiX5awbqtZ4Hbl8QqKnWGvyXnT05z8QFcaMANclgv8Oyzn6Wag8kPSIBg==", "signatures": [{"sig": "MEUCIQDq+E+sS+jKj66J/I/9xnu+cZkbDjMiTc+UDDdqBtbPvgIgEzAb5PrG0SD7UCsEMHuN4OCNnsfO/DkO5Xq1PuovL8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37380, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EuyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSFxAAj02HA/7wg9C0VQDZgXY5y8bKPhQAmlyhB0cR9+ow+b9/IDda\r\nctSOB/58VzZlZA1d3RhozHZfl7cDx9Rra18GHi9EM46szLuGhBMY3bVqm9wH\r\nJhk2Jkl1jt1bJEhg15a0xCG+bZtV9FKC1p9aMzstUDxGmFPUX2AbEk1FxZ8F\r\nxxIXn0NF8vdS0GqITaeUP7Px/IuO/iskivlzAIDZtZdbHwVKH3zYJeKJG636\r\nbEjNlgqIxfjtPpaoj6Gec8YbW/CdUmzJibGj2UNmrtAsKOVoPn4ZIRLMaGZJ\r\n7VzIcWdbpj4E8GB/EknszLofwYi0eiDGYn/ZqlpZi/9QiwfmMeshKQV+Svxi\r\nytTOJM67T7RqdeWthcuwrxzbLaMtPMaxgXXm4ERlSUc7QO3jbGQdHQbdL8b+\r\nP4HGwSON/4YRKyQv8GqdhKZjwz5Ci6nNMTXt38dnhtatuqS3EhOT7tWeYYv5\r\nO7haFGDEXcJACXZn8lxt6gQOIi1sh/dprZ02tw8zmqMNhlxoGtpa8hUQiy8Q\r\nohJH7s/EwAKBAP7qyOxvZ0AOk+49RPou9T/o7BFcBYN+7yryzWI4VQlH9dwf\r\nM96YfKSlz2BNx+jw0c+6UCxbzs65iPlhs4ZTmODC31B6wCilEJfpOHJiXFBb\r\n5Wrn5wMNnvZElFpM7Hh76LVKsfPcqfUxh6A=\r\n=xNvS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-avatar", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74ff3c0b39a74e07a5b2f1a1718ee480f6ed4e2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-pCN+bXAjhuGt7xkDh8MgdB2wN09XhLKpzPMJR9aVVS0tz0oTCy8wkbFOAmLotjBwZBHMJ9acpsVh7/JIuDSCtw==", "signatures": [{"sig": "MEQCIEMIG/perQo1sAWTTBFk/uOfQKY4CWccWpYC4UxHi+YtAiAZnLV0J3gwYXSqWOtl5646VPEI/42qcHrzq3mM7nSaOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXmQ/+PjyT/S49voYTXS9jrbLTQG6/16tnemQMFyxlCbY2NQmUONiq\r\nw0QyRsU5EkiKm929sngHH6L4a7AXgznumDwCL9qABcBYFMOoB9iCN5i10INR\r\n0ILOW+kJckc+lr6N/S4IueL80wB9RYaCImMEHqy2STjy56EzpDltRyYMHoWZ\r\nv3QFnzqmhixQ33uvudCj5hSfQ2nqhFqX+PYgex0D4eK85mDdm38SU0926qe0\r\n4VWWVDY5rS9YrSKd/y514JAAP/na/ahzXywbL6az0ghZEV19kf/oxvXu5fPG\r\ni+XDUR2VIzEjcHoHWRNRwjTwCRb5OC1xELFsJUEnkcpuYw05CotpV2E0s8Ul\r\n8d52L8gRE0ahzDkeAa+fxtwTTBpolD0g0f33xmaAbZa1O9IKm7S6moxhiMw+\r\nvo0YFylg9uxgRJYMEKieE1MEZpaolGjzKjlK2obvYXkl5ADUJDFaeE72g4Wr\r\nxl5nXw7HBA6yszt9llixPAM3KUMVuyPrcUUYPWcZTsbOBU4mqL/sEbtejdi3\r\ng0gsPEWVIqnnnoM3LrHn/nHZX3PphLnhB7+9DkCcraRjoJh6bA/1kN7tpkga\r\npVFrNQHV01FxJvvphruL9Q1fpeWBCEy92VVTYzFtPhiHVxBt/VUDp/9CgUZ1\r\nlQvc5L9z2MDCT0VIlrP/TXbqOv8NUfk/AsI=\r\n=ZmK8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96f344adc72f207d95d4e9bcdcf50c64c123de97", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yVxLb8PPW6TsCqN5yMl1vpVZAzUYGpgl83CBPIaMTfZvQwmrM+pse3oQmMawyubyh0TywDFQg+s+kSj6nkHP/g==", "signatures": [{"sig": "MEYCIQC/+p672XGqCS0d4vf+s72+x6FOTj0j8uIs5a8KHsAG6QIhANCIKIyMLUmMxmeneqOp1mWDky+/JrFML/maK4Eo3wdY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVCA/9HSjANEXLs1Ews3Sa6gIjn8NU3l4nwn49HskFYQPvBYe1DYrt\r\n+QWYsOxDWIXkN4m5gqHQWzgaGsfDuZUobwlHDbGP3AXCQ1to7lX4rEISffD1\r\n1hY7P4GcSSeLqjbbvVjgTehhl2G/qUjy49UlClI5MGKTs0qS8xeBJ+qsN/J0\r\nnzH9gfqH37KaiLPeMgcTsCj1OhtK7Xio4jqKYtXPfZt+1VxA1C2Y8/Pe+9Vh\r\n0lZB7zX1kXk8xiq9POof1XQw7bnZvAxw3UYMddnl1ui9Dv0a6EvJ8WgWRHfq\r\nr0Xmzc37RkwT8N8LJDRly0pHzj7nzgyp2rWwnkWjXVCD1WY9ahPqcfSqVohC\r\nknE+gNsg6V8crC6XicA8WHldBeiiG6n99Kg8+J+fGdECRIO3LpLIuf2hhfj0\r\n2jECKxtgyqQXihoDjLIyhGLM6o6Sh/wcEAjauQcrHOCWPg9cPfOE6YtSzUxE\r\nI6/UuyyvKVq0E37z8UgxQSJp19ZY/FJGbdMZyV9OwyDIXFO/1oKSCDwDhZzP\r\nDWwBymJLmOugwEzoWGz0lviMs7TDAFG9qGNcP9kNHzRQqsQNt+tk8+V2aCFr\r\nuLQN1b3T8a/qsl27Mb14BXlehd0lZBg/gVBFKfgZygr7usMce6gOOlR4CTfk\r\nC0ZIy+66VcnAfb7d+MNTexySMgt9gc75Ow0=\r\n=ll/J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ffface56d6513a4739b26c0f478e8b0b16cc496a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-251w1fnYSGjpUaIxMhB1KhDevH1NnTBk7HG6pDAT5IsyTFFLEBy/uW8A+8fcfAJwpgR6VUcl1ubhqu/Z9HFn8Q==", "signatures": [{"sig": "MEYCIQDQVS7wtzq7swD1hw+He+JAEGuIj8pL6rsDWHKZSlYPogIhAIs8gWh5OGxizqRVqXMgXdyMGOMXD7R1ctwq289qblk7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKy0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGbA//SeVv1NojjrM54K+JEhlWIxoIhHSuNYI+rkD6BHviur6sTj8y\r\niSyYunA5pKxms2Wxk9CarlWD+xo48i5lSn/E089qqJqYad2pz4B2IJzLdbC6\r\n4Wvsi8FmksFMqtp2BIpPWhMD+sosOKSbR0bQL+6BjSn6YgbR279GksV9MQwx\r\nTXCmpVgAXmuc8qz/KResKF1twvmCkarIyh290F97sMycBYLoMChQDgeW4VFH\r\nBo4hVvMA+d2qXQRUlYXfk9v2EUbCeqkpc5VC8p6oNlnFwSxM0BVSLzfeqdI4\r\nNkU7lM7gRybIVTKuU8R3K1P1u1hNiUfK5S+Upg1O9M6OG2XosoL082twn8uN\r\n2FaFsYs24MCXRFSDse8Vf9PBlGFxmOXFLcJrG0im1DMRZriGQCWCopB3RvcK\r\nIcL0hwJ2cB1+6g+zsMNboBzfB4p6XYYWc51pQbSVLJufudu5bY0C+Ml2T/c/\r\n4iTygMoENkJ05sklDYJs7Jtw3Xz4BDNRLpS2sntUqlz7OhFRJqG75xR7D/kL\r\nXstvEwqLQ6cxjw5mZvJ4t5lcySmxsLJn1Q5IWYHTqOQF4g0BKC1baDNNIuKT\r\nKBMj6oqO/pNv8cwXYuskPd8wBlCFUJ5W0MXzvlSx/AqVgo/nRbXAjtVQL+Hr\r\nGkjMcQq2qmQoLjZhKEjypcaAE/aSe+JNi3E=\r\n=4KyG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2833e021b9589cee18279becae3d75f81bd6e847", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-786nKUjDALpeuEkvSmmcd7fcctVtUYwv1bZ+FFCV2EHhR84Yte0FKJAi8+ink7HSEeTlH521pqnF1IK59i1hLg==", "signatures": [{"sig": "MEUCIQD8jYX4we4vGR352Hl6Bk5E7y1He1vjAYh9sShZmyUz0gIgFv8pbG0X5M+Rno6b+gzxoDE1QwohTfhx1Fpk2tc/m/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdbzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxKQ/9FhkLW0Ko0wm45lnGd7nlYKBAm8d22VWQ7MXUCQns/1X6nH3w\r\nO5kPBULjBoS3wz8o3lr49/7woMRYJ7VW0nEwO23UTXmOaMeDIZALOSG0nbRr\r\nGY2iPh/1YkLp+UBaCdTTeKpNbiQhGDfrDHEJbeNUFpQNdi5PZi1tnra84+jJ\r\n+bQM4fKin9QWlge+2rJj4FZTkT0GeAFMzfKMWjfn3JeWvfw18CjKjqcUq8r/\r\nCfFm6koJAwf9iZ+b7RvgQzWGIp+/6bfji8VDJO6Bu36TQggFLvqKCRyj1GXI\r\nyULahFXRrUq9WDXnAH39BQI/8F+azo7w2HFaj1TMNRuDyg5iIpkdWjbTEJd2\r\nGTLSue85xIWfDrroHpMAMb+aKSTKb2qnTUZGlU69R5iO+MUntNbJLoDZgF5g\r\nOs3gFOcU3nVhCdE7XCfebK6raM7MbRm3mg9Z06ePr3n5+LE0ED6kfUoKZ2Di\r\nG9pEQ4HivfFfWh81K2xzCYQW312MZFpqzP3kViZgc1N4ms9IsMv8i65d+v3O\r\nFf5Cxzo9IxvZXcXzal+s4pq+hZy5g76KQHV8B7N4uVrwfQU7b5sHo6tn5sD3\r\nnpHxxmfufrKHgPuWxRYZeznnaB1HhoAIol30ib1cW49EDaJrkhOLLa008A8/\r\n/zlESVCesdHppGSeKG4kFC9QbyZUS57J/PQ=\r\n=oX2Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9023c4140cb14dd63eb4979278c39b0400046d83", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-P4RA9x6Tg+ie+5SgkyDj5G5oc0UuRkpEF93K6CrNg7ZYTWKjmNyp/mJqzG/Xq7rHmbZqGcgwoST+T4+RdzXtDg==", "signatures": [{"sig": "MEYCIQCCG5f/7aHQ82w8/ipmyyCITaLkxdPM5FtpDhvuuSSowAIhAKa9dNKkfjpTB4tnziF4t3kruste++Rz+OFnQ1t3Qrs/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogJA//TW2VjsWqq8WdgAUA+WeujQRtG4V17xeUsqfmjGTmVDKs3Nwl\r\n42g/duchp4hHA98gL+KHc5mOHcGN65By4q5t73ZAqky5qdLXjgONob96QUR9\r\nMc6pq1y01FmT3wwfrYVA+te+vhtb73RKwXXb3gWoMfh6cvIquVMo4zShz/O+\r\nlfOUQi/fGeY0phyd12IOwyhSvj8gzLl7JAXSDzFabgZGgaEsc9FXSG+LeBdG\r\nSxLOZG/x/a+NLmOyrCBROpqn+Shju4K3cChY1X3DZXf5HHfH033Yp86K05kU\r\nG8WVTCmZmKkFnpaefJzeMVjAvr2fiKLjMt08gMioPNaudxfVh8U9WDJMirK8\r\n7ffFyuNg3NsCsGdvj8XHpr4KERZkSYwFb5OP+UuLPCNxsWWMWkuuavoONn70\r\np8ohZurMUDNKWOh1ZFYg5a03JC/f8AzQNvvQ9S4Asea8+B3BL5sIjPAT3KH9\r\nG2lzF1Ta8KaxRGJqfryBLAJmxSzbbRZMZK1Oy+LBZ/vTRDpxXf6Bm9DxUx/9\r\nid2yTGXw+loWOES4MnP64isKikPa14gIttRIPu/kWDb5EeNGzu7i0N4seEsj\r\n7kVaoVD+AckglB8zCCj2gQuEnWoJLNnG2YtpFewzPV1opbmNUXLR8HLmiZrn\r\n63sD5Jn15ob1+NHSLcmAUrktzEVYTF7GGrg=\r\n=xHNH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4956974aaead738dbc58230da4629122ece44fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-UQvpNLv/z3tNEL8NoM+2mLmKImd4ZpnDCAGN2m+vemkvczqNGr2RQZdP0NkLkA5yTqCO8YDLcZaNF/vfa73hnw==", "signatures": [{"sig": "MEQCIAhLgHHRRm9imqdwYLrEbO/9PpFuMJPftj5qsWFZtWfzAiBkfAgaC/wY3J/z2Q2DJSKSNg9f1PsvRkSCcIFv9u07aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3vQ//XZG5O3xu70QXKdBrAygHYYMyoo+JqFmhcx15pBWGpCWb+bE/\r\nW3bez23V8W0r2nEAyV3oVnT0klKIVNRm2V4NZ/N2TxXtgpwTH84sove9FrXk\r\ncmqSD/kz+S0vudcWNpcwgvEjbA50E266BqD0jW4TsaNMIdmGFGknW5k9QLuY\r\nWrjPYkHT+cZ/ZGnt2rvQKHaIJquLhNl/ynL3L3xFbWyjhzDW7VT92v++e/nK\r\nFoiMoGM+QbFhJJMYtii+mdN3aA0IB3T0ZmhTA2iEMRuQ6lQY+SvVMffTB7Ty\r\nX+w8bo1M+yLUys1zEDzIAzg6HAGztdh2drfBpG7dhv7sbaRWeojgyh+sFh4P\r\nJgx/0pqByOIvoHYD0Pc5dI8UjPayMD7et1TaaL1O0JEOeHN2HQBht4SKPaG3\r\na9Y8ipDt+MuTRVUOQ5KYphnbZeJnYhMSmMM16ipf09ANlpiArqhj7aigQoAR\r\nM/wOZpgS0vothWbUa7RUZF136PBk2VzufIYTbb96yfnKfoCdYwBeLNlsHvm7\r\nlUnXsReWc1g0z4t9xO9w5xi2LdwvCrgab9kOrGDDbHIOrA88xUhy3o2eTS/M\r\nHoo+6av1m+hiEPNJGZC2qyrItQbPhs5IT3aa+xZX95HTaZ+wOu9jnOOhJJp9\r\n1BpmHnUHYXgIpu3ETgDSX4Nrwprr3MKKwZY=\r\n=E0jm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a550237ea8335eb5f9ccf01e16fd52a9ba181a1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-hCbrBtCGUJT35eLZXbz4uR5VNxxbvTvu/iW9rEM8xhjxwKoTZie2n9ZTIW5za8JfSlGSH6OFpagGGr8xs4YF4Q==", "signatures": [{"sig": "MEYCIQCkYOjICyHeLRyWqmbk3zl3F4qG7xcmM7K5ODlnAduOUwIhAKV4subLDXAo00WvPh/AeVo9bvN9wQQDz2SYwXzXsrBw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwOwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozpA/+OZkQUOeLZ6eRxe4sEC7aAQQ99OJwo9n6QE80sbXUuZmyaX/p\r\nlGAWD0KcNrY5FmyNlnzCoL57qlY/QT6ltBgER7J1+fwxOyBHM1io81jtU93h\r\nSRVpBAbJNturCVa82HjOzUEgr9i/IZSvBcZJrqs2D42tIMKKyHByL4X282cs\r\nRXB5VamjlaCXmoPDsfSQo4Fq6PREFM0O43S3O8ALyXMs5LeN8ZhKU7TYxjqB\r\naODap4OMoFm9tPJeCAeiZD0A/wAzi33kDtjzmWO7vON1Ul+P3KURk1mb7FeR\r\npefSv3gZcgZ+pJmZs/McihzGreP2GjtRRCIVMrR8ABBz+rzWRYPahuG4xmfG\r\nNpXuxzt1Drl+Wj4SsxzrOBli3zF2R9umqz+zLa626OkCrm2TvOnViaetVCQr\r\nnECh2Ytn6sJXxgdh+0Tx1KAW51rsr+GqcMgypmzx1//jLsdNdETFfvEblNig\r\na0tzvPU1Y6qkKg+nR+23zowo74X1lKWx7bgeN2X6qXGsmdb7lvbZWmwYscHa\r\nPgaG6oj+z7GrMnzsngPuNMSQYg+5hn7DLiXPcuFAjPgLRFmL5r0ia+JQUm9z\r\nCKfuO1pySnP5mMNEpqiQzGGwotL4D+lxWiCu3o6RRQ/V58OJjQOJFyIIxHBf\r\nB+LrPJQIX5+gYdWyIeqHrBTD4HvHObgB4us=\r\n=xTpK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6b82c35830e74f5e5ce7b8734378c4fcaf412838", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-RE+IPFgaJLZyBdc0JOYhjxjqqDOgzCkMjzSJdn0FJbBVtMOCau2GieSD5wyGAlLPOSKXEwegzy5P+fHJBsFD2g==", "signatures": [{"sig": "MEUCIQC+KSMLaqkpBDXxEiTxJ6pNaZL5l1FM6kJNTnS5X/m9tAIgbmUMMqav7MIIrWQNHST9uIy3pVGjt+eaUHCWWvwxkBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr86w//URydRc+FwaJcr0M9jno/8zShg3B4lCbzoX9ATHIvY6CCASt3\r\npyaBbzroVsUkdGuu6V76NkqtrEhSHrgJQsVFNaPmZZgNIO5Qj8fa+3s3poio\r\nRfgXM7JAbnIDmbZIGosSrnQWRjMUQKCpzTMR9fqqzAXGbSyB3gyeMHi33nPo\r\nyl3h3s5iYChjt3yb4IUknxcmrjGa8QacoOge3jTh2iyMHei6CkYCrEJF+6JL\r\nSfHxaYbmqFDBVsNtfiRq8zXJ+qu0BI0etMv8DVyfmIsut+3xMF96sqhuppHi\r\n7kPqMWtT9ylra2c0HlynNA5AB36MPki/xq695fkQe9aapLvb/kWeRYhT8vaQ\r\naFZuUEqWZ6H13GqD8mZEmd2czaQsiu/KI4lWzfc7OAEh0NGW0iSztSVwqHZx\r\nadJWQEiwpX3XXJovNPb7wd9vRfVl1FDMrfhjf1dEILOO14jLs32PzetXMf4a\r\nlLBkH8wnExlCMvynMJt2AJXdqGU3fJI0hTf5fizmfBp28ROOjQLAyFN/Fh58\r\ntcTLBvqJ2+eScWaY1CtI57N7RFdYhgBL3pDXdsaZVqAZr+vzd7T60bhuvoie\r\nQ0YHZ9xTJKLFTJp2O5DkknqKw4H6c/SQF7ia5zFjn9tGRcYLt+ztSt6yTXRj\r\ncbAUSj3T1kohEHMwVs7SPQs+9AdyTtYuGw0=\r\n=VJ5H\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5bcff3a324e4d4fce8e512d857de8207901347e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-mfy4idOfTBSV53m9EltxIzymnU5mobxaIqRF49MHKjQlpvOal+5mub3LZZN7qAmpZMQIsScgmtByTWWtPjmOvQ==", "signatures": [{"sig": "MEUCIQD9IRDTWbUj8cBvF47Okbo8JOH/0wlgazCBl/ij8EqX4QIgQKYXSTQ7WLNHApjEFPQLv3OEK4/MoJ+i5vgwJbwjoSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpypBAAjLQerueJ6fllV5sQKA/XgGcDcnbekd4nKNUA0ipujd7uP62C\r\nIuxJa3N/F+NB5YG3G0+MY2YsJs3Ximkg4Zs9OZp1BKo9FYyzIERoMMYssvAZ\r\n/lKZkWPz+mBacpleiY58+M32fudTCvUaxtYna3fywEB//ANykGXz7oKGRIzU\r\nVmQc5gKc5N1q5dSp0upFRawVf96zqxz5u9y1/+JHA+XKp+Cy42gTLWuo7xP/\r\noxaB/1VqWXXguAx+hHyEJRLOBsMLX9N+t+ry1xuAwFCLA3qedO8GFKGYD+L2\r\nnugTJA1wOsiX5SniKCx0yjfbRvzybpcISLV8BHb1pT7Xeg7gllkwOxsxPu+a\r\nxVVOgs6WYJxD6h0ZUNxj5BQjLcB/h7r68TL3z+IRZPXRI20uOW16aP3PsgQC\r\na7Vd8Fff/Qa4+NSYzFQearaeFtSPKFR2sJGiGvA6C52k1EhqdqOi5jFbVRMc\r\ndkJBcz4MB3fj08ayvZ+zyqu5HT2HlZbQB+lKwMQuIO1HmLqecv0r8hVCC1xg\r\nB5Bym+VXWylRbAs6vrGqxE0yKfvk1CtnxzYaoaDLO1/8pMlWJvOw0KE+nw7S\r\nhdH/ELwUx3xbCjE3irh1LsUOWSCbke5OG6cj+lZdlfrpE6B7Qv+Zm7BMnsGh\r\nRzV8OJrF1IEgQ2cDP38DtLPM4b3uXvpZ1Y8=\r\n=bbAi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0195fcfeee325dd19d2ce6813cb65e3c1d9e97e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-eS3Anpa2crOIKhtrY0zdrd2TEBsmHUj2DepGr56Fd2CEnYAEJtwmlAakDx/i3PaLkaxXJQ1DQ4C+m0LA4RDIYg==", "signatures": [{"sig": "MEUCIAieXKDpTMFAouEjDPuZSIvgcqHJIjNeipx06Ctctkp9AiEAyuWhOWCSzOZtzSD9g9tv4Yjq9rc4dIBqYwwm1GeZVWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/a0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmof6Q/+KdsOygwQLiULbBDUyGflg+5jLUXGXQ+XshzgCrCWTDcIKta2\r\nbAmq02NmsoXWZnovJEmfZ3vh4+i2ME6NRso2fCYStZNNtAOaL1OZWrDVZDqW\r\nKswUoFqbGLK2Fu0CYryYFT1ZSRaPz7P/dVVvTC4MkrVTrP4qdUDfRkh3pxlA\r\nsQyDD4Td4Dx+YMMyQYHOIGBMvvXtmxdwwiwfOVAYzH6GplqZQzEFTpGgxZh5\r\naVfwUjkI7RU+iOCWhJ2X1yX8+IFLZyuq2YbN9ee5bgaBIBq0YCkdk54seXo7\r\nSlgZZT5GEci18bIwJO/FvCeFEaA/wa7/6y5L4WmNuAAxoFosnFffTgDsaZeV\r\n3Bf6ZH+/SnVdhLmVsbhWvuMlWFgfMRIVlwvcN1wNIWRLBK20CSMB29TASFvZ\r\nssW5CdYU8houhfckH8vorgruuhhzuq4422XIn60ed947SvBGWC5iGgm1A3zJ\r\n/SGqn8tmtccT6iv0Uutl+c3Y+7Rjp+713EkAN+YpAlRlyBiILlP1FAnfj8fO\r\nYFVWHW3/9g+MjTKJu7HeFtC8p0VgjvsFOirw101UnQnt1VL/SADy0gQtvgHj\r\nC3VBqwhyZMDztpJS5x5puNPy6DWnHZTwaLGVN/TLMpoDRQukOs1J38uLBaLb\r\nx4R6Th35pDp4mEPt8JogutGzdkarXHHfFM4=\r\n=REbm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a8782c1e865d5ae516428c62eb9cdb73c5537f4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-CeyqJQITuRpmvBkSd6bdjBQ5VP+URs9RudrNrO43hOnmijWZqSbrFLMSIvXt291Pjj7pkqZCKd9JguSFat3VOg==", "signatures": [{"sig": "MEYCIQDwIBsG9ydpIzzxY5UaqMmizoKRYp77VA0q0GIb6Vzd+QIhALF5RsebgTJIGg65pw8OwOVTAcaRIZ4yscrrVmPLPJZ0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0hw//Qib9fuxM6npKOi1zsKS/kLXbHnfbiP6o2HDvoQEpKp0Jn3dd\r\n5X2Gp+MhHqUQsxaCp5+C/FU46v/e2IKkTrrHmBzaDIu0GZ0WMNDfUItxScFq\r\np9eoGDcv43Q8EEpE0NKS1mQH6jvX1xiLkRhxoZp7ZNdYEndRRIaktW9CPdsr\r\nG3e7JHATxSV/1v7CCn3JOGlLT9ptyqB7yfdXdFcgc3cU+tOn/cf4D/tLxLT4\r\nStlBa5L0M6yNd0sRcus40k8KQ51YQtTw30L/fLzTlXdYkM1LkQKzE9gTKLdl\r\nbvpHoYk4NMTG2IVXqWJ7zstMDGk76dmHhV+VUe2WjGEPIF+TPHMZb8sqAoH+\r\nHxhAjv+wtrplqYE1wamgMs4KH5ZWH8RKG2HnLQeLb3+XZpwiHu5vzc05jxLg\r\n/Y+NFOTY3qwfLYK+G4YxqODCiHQxo1Yi0fYGIWd7iISGUQXg8hc50thb/OcA\r\nLM+XMA1feNa9eSqfPvdjrJNbsbnsuno2XYWlc7RKbU79LwDpctwZQt5eKSAo\r\nXYEnX29k/0Y3TZ5K2PWn1fOjYStHzmNugW4MHSXiTJhq/sOJkk+Ng4mEPrmc\r\nJPB2xvC5qY4aEeJLGKb6A9KpR8+LLehSgH4GME7lm3SUmeqlEZIJ0SBNvr4I\r\nHwa14CdbpX/+0x4S2rpxtaxh2Gaf7/6pCK8=\r\n=WgKp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ed479d185635b07fab76421eb9a77d2ee8b023e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-OnrW0BP7S/4JrSb39SycvY44XCxcPFhg/6NtnFRqrzvYhifkcsxnHxJ0dqcml6OjsZM/xNIvirxvZ/UPHsal1w==", "signatures": [{"sig": "MEYCIQDb7djxGPMr6QXnbIV5H/FMU02r3sa6/za/vgxvLevR+wIhALyyrkXkRYRhvi0Gh2Yl8VSJPdV4IQI0mz+mRRTdSPt5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKvw//efRwTFK0WU0IvUTCIQjhkaQILnGOlkNoN9xm6scxfCwzX/3X\r\nK1AvQvLt8WK7vRVm3OJLCwFd/jzVm1MDpmcu/Pi/L99OUo3mN8UsjHR3WtuJ\r\nxUC4LtfCzPY+jcZfe/a2S8xgoYOL2anZ6dk92N0MnXIEmYroJ0yafaDrWN0w\r\ncj+/tCqqlXFMm3WKnZNeJTwKY6Tb8sGW4rNp8fwvb8QF9+eBCcZ9D3AzVRL+\r\nSxMEBy6vxUQ5h2M2VqVzbALcSjESvli2OXdYiM1MolwhCc0QnS/j3l33RiFa\r\n5/mjPp6WjpvbPUwRNcRGcDiKRS/KbJpfubpSVKJpZaZASZYzAfXbRN5vtVKv\r\naCEFY/JGjR/69ucCuD15iaMIF7asRhs8b8tLirT5MNSViH/LGXPPJTu41hKM\r\n2b/8HuXjKPPqNFF5fgKTMcB/wW6y/xbYSvgbwCGnfUJsDOPE7mUVqqIUglUV\r\nETFV4gC2kVwyE488UlNnB8xJKE87G0ay9i1CU71m1BF9vRw6eEgmX1JAef0U\r\niYKkecap3YCHIJa07jQOcOTUnKSStGLRsQ7twRPdOhns0lDHi+AZ9mDZ3NIe\r\nnNeaEje9fbIqTjUXALmtP4wfVmqpvWw2weL6H1AkAOBy/VUGFGmdBnnSot8i\r\nWEHkiN02NTGRXP0T+Q319MJ1G8n1oCwURpg=\r\n=3+EX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "44efd2ebc6a12b72034d3bbfde2dd05392eead6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-rbnAY5H0xZS5qT4oWUytBE0zDuJaVvbTAAJzEWnpoOMpmlz5u5qb2NSItR1ZVuxQZISZCg4xoakxWvLfZE4rbA==", "signatures": [{"sig": "MEQCIBeEWc3loeFyov16Wo2C/oaWfg3nXbD/rri5IWZXl82ZAiByd29TAl4FnuGrt8O4F+TEML6H10ZLivXb0GHvWLOCMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVLyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrR/A/+JBZ29HAwApXVSJWrgx9MLECs7MCrH6MU1v6AjGzca7n0BQct\r\ng2aHSJ4Ud4+Bs7Vrr95hjIQlkngOp9FClrQUVSC9aAY+BO5IJUPRTSDIjCEY\r\nDr+MtGWyfDvKXuKomGYwGSZd9dn9JkFpKy8rJPzf3Oqi/dCqAxR94JT9J13j\r\nsdHGREFRj4CP75ys0+mTsK0EsCRY0jFe2NqiZigUfLktRyEPqFBaFkliVp80\r\neGdx0/jvvSXoxNguI8veCv3LHbVevaQGhuHaDYx5jMNuA3O5BaVWZ0KPnZ1W\r\nEepkblFi6ebe4zIiDoSgwSFIuQaPUcTKrOrQP1CudFLz9ZtOKbAEKYMiN2Vo\r\nmGZ1IrZCs9b23CX8R7Ueib51K87O1iZ3kwtc17n7CoMYjBgOLVSZdP87al28\r\nOPt9GrCiRPOioYWpdJzpB3+XxFdgo6q3kENGGzTiHETMWLetRHSLnfNwGqMp\r\nems2KrYIOrIWw23epb4TavkzrAP2gOe682++FFsrsHoU4+zjOf3nLygEA6u8\r\nQ5adZkqWVdnGfM6QicHe9qb8+fXdn86vCIHcA1LBQXLAFHNEU2xTIqme4fyQ\r\nUWHbWyUbuSmb8ya6KBsAfirtywsZy2QsgoYTHG4FCFcAlvzzse4tgDQe74Ah\r\nSwcqc6yOhG+LRJq03OinUgzm3mUocwFp5HE=\r\n=kzdC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "807d545fcae4faf53e67eb221740968d583e2cd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-SHWxZjPYfITwCWPK1bBzv0ZvaKaG/a0iJeLYHWmxDvp1IbUHfsjTBP+CGsNaWglPrEhfMpxBb4C2nuhvddtwew==", "signatures": [{"sig": "MEYCIQCD6f4QE0/ytanZMIihr8+6ElAwJhw5dOecWCekLFaygQIhALZ0mp17Nuqlmc9eurvnggKlgMu6o0nOrEwJ5tSx4iqv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnJ+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Qw//XI6xqHAKGdi+DOom4zK0g5Q0ytlFalwFEfxaDeiF/s26RnYm\r\nOxyx5n3o5222E1x7fxreFCoSrDZi3Zo6FwhFRSeE/LKjSp7rzXvn//U3+J0/\r\nSDszzDNa0VDM2ZZx34Ecb+oZ8EDByVdmZrAKKtFTF1z1+fuNUqI5J83Ra74h\r\nKp6SDU78QcvTTYUBRdfZMdGiBOqRL9Njw8QLIKUrnoo4z8axCE9Gt4z3XNs8\r\nOX7cZY4G1Fbc+589E5rpkEdPKma4sgcqidMfpxHBqPIUrRilHH/Wb6Rou3kv\r\n3nwxI9X87V18aMvvFkrBSNqRXdEyQ2Pp0homLRy1gnX3v8z4rLpoZltZ2I3A\r\nGHXOwF7/DFNGG4lEIkzvGjBp+XQV+9NpYwDJUQjjYTgBwBcINhwuM+J6NNkM\r\nstFSEhILyQnd9t6KbBHk9wZhvlLaxryj1GiIEz4VZLrcD8WX2jKTcnhRJeXK\r\nBk71yZbnmph/NWtjaqGphjzlLyaxBxwPxoKaAr5rGlZ4PdAFyDL60BPTF5pB\r\n52KLLUMVUpPJznkF0UEZtokGuXpZDjjNbA6IF75K8uMSVf+u3JVr1iZODoU6\r\n3AQRb0ny60UIk1ZpK6sX/w1vY+tYq1cF37QQKz6d40flZ5XfQRMhlSgklprp\r\n9be0q6urLbUTZTzio6kND/U/dV2KWW/5KZo=\r\n=MSi0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd267dbf2848e45aedc66cc77babb7a681e696ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-rrZvDHCq/DycmPS/jTiEY3WTlHXFCpoNhZjbYS5nYF30WxnIz6c+2/lXn8+zfKPOvogv5PQKR0wurQQwWicmFQ==", "signatures": [{"sig": "MEUCID4ln7UgIT0kL1PplGXxCphHniCFatOyHa/Z/+4Km4dmAiEA1gAW6lC0LC4SbRtn1ZlUY4U99HA5QFZ74PslZ4vdxZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqweACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHuw/9E2HmTAwDxKbDuW3tzZOwPMZUbAsbbUWcO2n1i8IZQQgZ24W9\r\nDZewGhXgCJ3RTKVoclX7irlHPtN7a33ykimw97eaOalwfvzlh94pGed5Xb4i\r\n6kiJVr/rSrt3rWdyxvoS53ajohQ/9aivAtDN8gvcp2ScixotI2qNqrnKYUDi\r\nagbwz/0Z43a/q6rcaViECzfKE4Okpijr8LvWWmiDNlKikUOt7ZU4ErTGyvC1\r\n4z22S2T0q3G8qgaf62xJUOz5Sc1MxUPmdaovHr1qLDq3xOUwblgNCjuSvkr6\r\ndVibcU4+Yi9T6jq3sky28jSfGR/SjCNCHw3J+R01ZaMY3gQ48Z4XDaxaf/2H\r\nH3dBb0KTAy3gbLoK2YSIm6CQd+1oUHmXKP7GYlt27xjdloKV0zFReWY9J4mR\r\nqub6J4gbHT0aWA0K1ozbI+X3gqkwB35ul0BjcJDRt0FUXmQBRddFJA/6tPqf\r\nCGDXfuL+HzaNevf6WmQLc9M46Y8RJatPfdUIIugVheedHfmEGuSUSZrjkvZf\r\nAcrIQ40gl5AHLNmgLoNgm0xa28JUAKtlHdMtX6CpykS1IKRUKc9lbXKAYNTu\r\neD3HOukVKG60HOKp8r+7wcdea9YwYTcxbsO8in7RTYzxkaPqtWEyUMT5rn7Y\r\nisJlA5C4LidJucE6CUNMbYQy1X6daZBuRBA=\r\n=Jp/E\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b01003ea2b4cab1a61f05fcd56078fe066453021", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-rJWuMmNnL7LNnPWoqU2N0SOHP8CI283P3UvFlDdd/GGn7+6xN7GteteP8cnEcFotCvViUoHjHBxObQHjS9Rwzg==", "signatures": [{"sig": "MEQCIGslnkpluUFvnigbV0FVdHwYNDt5wXlqwGysraIddpdGAiAwXBKiVdfWKHtsi3LMW9xvxhe0mXzlpIPoViB+g9mlBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJ1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphoA/9EubRIZxTdBqW1ECUrWf9nFDGXeCItH5eHCJ9Sib+L2Eouu/T\r\nLg0GDaB+/3QPzSq1rSKPCLSKCceAwuPnIJkhCkXEOx4WwlrZcQaziUE/XAEX\r\n5TeU3GA/BfmuriQtM7i8Lm71Nrjov1qQUHIukQGlsUMtHiNlj3zaDM5imDvD\r\nb7XAo8abwvG+Sj25/oAsEbDXzAejyfdEUty+QvwDMOWJz18uBQHrnfwGNifs\r\npljGYGbxTN4l85/4Thgl/GouiYBtDnKjGw2fHrH2RagD8MDxFXSNYlbpPQZj\r\nnoa2b1Qj5m3RqBhrDm+aFZDUY60H4IfHVXFe7lDfTpjRxJQ73hnWGemAbbvN\r\nOdBrwxT9nfsu+ji4y1wXUHaCbzdoAsSmTQNZrUHotGqy20UnCpdWLogtEMXh\r\nzuoeQZ9CHb20/E6OaZESZvpaXFaUaNfBzSup+E/j9Ddaj6mLqMSQa+pyNCOQ\r\nMzjdEWOBhyYXH8l5p9h6oGlsUO1FVb8WOL/GutwH1YTqPXO3nPr54rG5xyL5\r\nGP3ELTEDEP3DV05k4LLZHOpH+a1MVDuuHCRsm4x1QnAnl/htXfYyMh5693bD\r\nb6K45dpvv6XLmKFgttw8pNs/BmcHJaCbZlnt3QF+e061KVa7ZCIdtp/GREqh\r\nd9hpBw44XfPqzPGGRWf9h6MwI/qv1+B/mLA=\r\n=aqas\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-avatar", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7c19121b9daa716dcae510fe29203a52b70042b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-UxFkqTBvJ4irxIi0kpya/kTHZDHTixE/JS7sOtxAl4bZ5J6I+HvtSb5ktbDKTeBMmpbFoFxz52z4Gbe9QV04Dw==", "signatures": [{"sig": "MEUCIQCTT35mPaw1/CzEP7o2wx8zcHxPG0tURpw1sTG4dU3tmAIgb8imy/UlMRqxtlo8pUvarrbn3KZF1gFXKmRx7J92G7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRegACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAkw/9Gm0MQ8I6Yv6DSvuvGN5UO0U+ELNkVEkBnSQ/sW3sufvl1fov\r\njbDeuFvG1U86affLRouixDoh45HQcPi0wLF/H2bQzu4/ebkE355rADZzUWi7\r\nC2bFOfgELLZpp7axOuLoc1YEcukaOZ9Eckt8HrEbO6LyZvuxd2Pw63GWCpLo\r\nQuUnqr4cXzARHrWQO8lbyrF2cf3X8wEsHvQL0x1I/eRB+e+hZZRsBT0DyG4K\r\npIf6PXSC2gTmfqVMfFgjIgCmpT9aUd05qxqG1QSSyIKARaAE2j+dqaMPE1Io\r\n1Idzh0knN8YpIB72oxEKOumKvm+cNF0SqdWv+SWZH+wizL2hDrkfCfy0sRox\r\nABxkrLrcPXkPu9u9N82j0L1nNqeZDOz8+p6cCiURwZm5k734JwifxfXUsuqO\r\nLpZUzO4Cuzw9tUWahUdEkUEj8/X1znLI6ADi6YrImfcl7LmKKrJSspu5MikL\r\nPQZwBISsvyMjjUpe0PVJYqXMX1iguBxZ8px3OVUgwNxygYiMu0+HC4iNKmP5\r\nkavOm+KBuV63C7rJKRYFd2PWeeMTUSCn9DY+cDzD0abPGuKoq8G91sbMONZF\r\nS0e799HQmQtzp9aMHI2eZO6t0QqVmWoOdl2gswPYwvkAWYhKF/N6/IyoEgfc\r\nTlvyEDE+hTPFvHCbMBo4HEuX8CB+I4S8hXo=\r\n=mAJn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-avatar", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d25ef10b56210039c152e45209dd41a1afdc192e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-GfUgw4i/OWmb76bmM9qLnedYOsXhPvRXL6xaxyZzhiIVEwo2KbmxTaSQv5r1Oh8nNqBs1vfYPGuVmhEfpxpnvw==", "signatures": [{"sig": "MEUCIBkadIuCuImCclMaCbEUncQcrOkJ3xjHPqmSe688bjsjAiEAhF0gCYRm6M1oIca0pQefRgQTjDTFsEudjqw+6s/2Ttc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryEhAAnunzsa737zij44hVp5pQt5Q20G8dZbZF2ERLEkQpY0Yfh7v1\r\nSSusb0VMnxLYHjY5KIUrYdOUWJepYuoX52NRA5Ng0YVNEFK8jVTXlvHpYRU4\r\nEatDUSEhMkWSCTOVjWT+gc8LCan7cS1eABQhQeqRmxTdD66f5/XTHf72iRfF\r\ngwQiI3XaOb62/v86OrFfqw1zrSN1JLavj3ALmpWkQcJuvZDDS4Nsh9qq21pw\r\np42kpZHsRba5q2AcoWNR6DkdZ1iqcUT6lJGqQ7Ah5y0BT53v5Td/GYA0xssF\r\nMv4CsLAzl4HirU/DeJdYFpuGiPjf8GFUhnfj7NZ/PvIePHFYEvvzzrcd73Rw\r\nHVitpqUX/8MmAzsOAUyFRrWdaumltg59se2l5+8SDgN7sooWwQLdw/hEY6Il\r\nA5zwXV8WrcWTUixqihLLB2q5WWL8HcPzd5t0I7ynd9puRM5ujf4Besqy5n6Y\r\nYeE42JCVG9tRpqVpYMWRZ+oN1Tn4zAJMiwVmUkI2AVLyxuX7kCV1NQOkK169\r\n0tEsniy+JJAeTUYs5qesIhhJ+K6j37UQL5uwT4CHjc2L0YPLckAzw7Jlm+ZY\r\nJwdaE/cVuzai4kEF7qJ2b3SFh87KPhEwYwu5qW5G0O/fPQ7vsiGdNR3QHu8o\r\nFvsUi+SY8xR+3XnUn7VmGUNEM/XQu1gV/hY=\r\n=jw4U\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d42b94dcede01d411f2b9baa96b5061c0227d32c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-EfG+hPrF8QqI0OzKaNN5l9/+hqiFRpRyX3lpgC2gswgmgL7Pzzv3v+1UELqfyfNbbvJXDOxjy651RUgW5zYXkQ==", "signatures": [{"sig": "MEQCIA0ECUIUMfZn5Y/quzt0ffdu+NHTPccKNhxBdap8WYveAiAyhd8r5dslcISYEb574hxsaFjmFWAeJpSGFuwBmCt8BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBze/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl+g//Ub7vBKEF8+koPO8oVqVJ60jHv3cydSLqoHxnrM+mQeUzyrJt\r\n8FEdii382qF+aAUkC5+L7yNzQVZzPui5pAeyEVcYrkvK8QPOe70m6SQhxEWp\r\nl7Ewi2PqtN7b/mS1o6rG0NgQU6QQIRABoCrvxxpMBjzmdr8HK4Nv7sHtucpG\r\nTqJVQSLGhV50cSxR8VOv/e5bQ+Y4kK6rxUPcaWS6SciNACvI4V8p0FSU+RGh\r\nFzq9aWMz8OKdUKAf7YvuFS8exxf29cZMAQOhRUmgFKsCUdNP55uY8+DWZ4lN\r\n12zYaslJXoaagOaDEJB4PWUfAXoFm1bc+K8CIP7fHAhWaJ5APrRomVcy0Ljm\r\nlltFlbLwCmBxy2bOa4MGw5VRsPMybTqiUyRS7UIhVAiSz13i/RoEDnpVeZVg\r\nBuk+TVm1uc76yISQ0JAKOFMet1BE/Zv8i5fY+DJlOc3S92UFvv0+0sVQD50Y\r\n+eP3EueV1ZqIT++L0arjNKo74eQPZfhMyAVVMz3Z4u1oxKzt1ik95yKdcU0m\r\niIydmcojXTm7wf4LI6X5+Ky1IywklrGMJ3fRT6psaw2CGb+UM4xUKwdNbv88\r\nQUueziFfv0rPLMCCWzvQ3fwiBpvkYQSsZcR4hbD1boXw3j6YKmiOpo3hHx+Q\r\npjmD7iYT2ji1KzpNaYDLcatFSqkaYfEZ5rw=\r\n=Ui8T\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-avatar", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c72209882e191db00ac0bfa16a9d9c025f0958f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-XRL8z2l9V7hRLCPjHWg/34RBPZUGpmOjmsRSNvIh2DI28GyIWDChbcsDUVc63MzOItk6Q83Ob2KK8k2FUlXlGA==", "signatures": [{"sig": "MEUCIQCGekW5D6ZF/s5fUIl4eXs2HHguKgjvcyAvqfQtZe0l1QIgSSH03ixXj0cpqrb2Gw4TNfs2U3usSNNnGDZLPcKAn2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJahACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6sg/+PYDo+hqSXxyS2liG0eoPcUGV14LlAh8v4efB9vFHcRB+gikl\r\n6Gr0JNKPhCqD6vcL45gtVCIy7pgRm1DGlB2woUjKpX9vpY4UjpsSNjIT0WWE\r\nBt3wlxr1slZQnUUSzqt2igKsa7UtT6lzug9EyQl5DAzyrx69EisRnMvIfFJL\r\nHCdUs5uOvnxgMyBM8kaswGbzQgpHL98Q+qQiwneLOTOP/2pbHDEExkenemuY\r\nZY/TEyIH9aX0Y+dew3yQAsTS70hOJ+FGbaWZO3LjGp5U9zgWjb7KxL6va1xM\r\nTaHSqiRz5ckRd8HCDDKoWnjI2mmYlfvycwAL3ZNMgXgtCP/6PoURe6T44+6l\r\n503NbwHzVWoGPmELhacsg7VFyeiyTki11fnbwMchREPAWEzvJ3m5tEpAU/2W\r\ncPQswY5DTJnPkWae9kUFLheoLkmvh7bypLdDGDyWRRea0K+fuPOoqI4+za15\r\n5zHd3PWKJ1rRdEX603kN8EruX8l5WjMQUZhqeRYZaNHqhP10P7uqTXpfR7a3\r\n3PTvf7/8ftumV+oZZWLlOmURLKi7g2tS5S2SVAqEjj8XJUDbx25eIdBiXyU2\r\nN28d6QMk7oG6YAwpR6igbzBgZz3HFG3RRLBAtcVDQc1/7Mp/HHMA54Vo/MiX\r\n2iL6jh2Y2mYiB8Edeivul+4jKtalmFB0cAA=\r\n=xY+1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "88f8b88e7bb50861bf690a1d64955483967c261e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-SCX+R4/H6ktSompPGke6Pg/na6Mt8D1sQx+aOJiozw4D08PpuKXsu07k9z/kOhwSeVijH5L+REPeCwUSqdSdzQ==", "signatures": [{"sig": "MEYCIQD5azDc+G5ZQc/fIQeCB3JYHL+2mU1NRM8wiJpUYZ57ngIhAKCmiKnltvbK7RCPT7ygWs28MmoWtbbfRudcmEPoDiTH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8woACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrrrw//RZ76XaGX497Dc1W+Q6JYbC0NXtOZ5h5EZ3QpGmfJxoA/zY7H\r\n9xyUWUOB0lD7Uv5ANO5+V8TCpPk0Q78orozwmVbr2ZmINlFLsNe4rMIv504f\r\nDfg+UxCOBeOqyB5L0fRpKTVAjzRiv3kzcmxrGyJSD6CrI58szSEDGn62SnHL\r\nbAR7sbaULbexskv5tlV5D8VOsaWW0AMhCG1lneUes2inxSiTIELGatUkxbxf\r\nJfq+boLAXAnX5vrZ9ctREmOr/EpxkWmcxy9cR7I+TJT/f2JBcAlZoGIov/zB\r\n5AiGUcyt+/GlcZxqp6sSB+R2IYf6JkTXsihV2gBBj861h7r7itdiKQHe2xps\r\nZlQL1vFIo3zPrS+Bz7boqmrjUUWDns+nR9fOD9/Bz6+3Fl9g6HcbuOOjVhfj\r\ndnTqpeXFcbSdB+tiB5UWvPECBrI4B4wOKmM1ScqACGyChMYmu6EgN71VyanH\r\n3Nqhx1NNC4HJiRT18eOC8k2iAge/XT7iXCmokTRqsTWRMFFhGiU7GBKV+6mW\r\nNYjF+ua8yJy7xFPBJrksJa+f8O8BPzu9yreBxrR0M4atrD+6aXX6iRfBaHTN\r\neiF0aQTKCu1vCYGg7AUJgkExcy9+WyIl7YJShSPZ2iRznr04d9NJCM03CAn9\r\nUn95fqTHm1jyNqCJzfmiXSOcWvvwQwcDVBs=\r\n=u10e\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "327366d2814d9a73468bc5409d5c9f001e2d9ac6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-klkrJVuCtAJBteId/kxSFWzi3dRxJUajYa3cj8diYx3GhnbRk4voJguTD4EH9Z5g8JovHquchLi0P7P7PF/7rA==", "signatures": [{"sig": "MEUCIFMeCjczT9W2XJG/mbA6Ex/bOF/fYkz6Ij/iGTXY1zO6AiEA0SLMhQHrjpMmINCO2e9h/Oy0dOFI6Gt37B/7qNNaS1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365}}, "1.0.3-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ce7dc6c61c315e4713783dc8f373333569e9e0f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-VdXSK/oktlLu24jEG7UjC/vY4TwX1ljJlMxSmbnuj3E7Vd2qIi/QUTBCOf/Q3KE2rTyso08Dy5L8lFiFFsp8Ig==", "signatures": [{"sig": "MEYCIQC213aqf8GD2DsTV4jHsoWoMe1FdXFUg89C4OYsoOqJ4gIhAOyy/vqVjaJWWCsHaPPcoAflNAYxc632Rf12cSjRs3bJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365}}, "1.0.3-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd79a105c190b587e1b8d667d20b702bcd7751f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-T0tUP95siIMdulE8CYDms31o5Sb5CIRWjxlYm2qjlMLXIMqXpo/J86s4pXTa+r957DukRPYteq1esjWZlV2lGw==", "signatures": [{"sig": "MEUCIQDeXeh8OcX+PL+fuGWW7gOeWN8FBsCU3D3TmnDb7v+FuAIgcVaXyE+Z1m47wcXwwgcxZ2HIJoLtrz+m9DKlRtNZVFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365}}, "1.0.3-rc.5": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b46586997227f1ad399a3c6940231ac227944619", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Esi5CJRYr40IG7mdt3mcdFMJDfh/FSgNHm8Zqf4wGJIv6iZPKYkC66Xlexg/k7vBE+215HY3mb/Ak+76ujtSyQ==", "signatures": [{"sig": "MEQCIHFH4lPC1PVQoJWKZTvsny0Jv26lHKqpxqAhd4nLdk09AiAbljiUYp9awAO6tfLboiTZ37l2cfNejAQdBqWFuhPvNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37365}}, "1.0.3-rc.6": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-use-callback-ref": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8ef0bc4b63079fa8bab04d8c946bb31ae0f60f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-QbS95ePw3JHg/z5f9412WRKKIfL4/osJO7LEhX9pDRFF9c0NJot6y5HF7rSdrPlou6VbUt9v32xCxnHMYdVDcw==", "signatures": [{"sig": "MEQCIAp4+QJNrXvTqS4Chxuoc+xM74gSUUZFdNTMmUmBDsCMAiACA7wjCsWNiKkbWMda8OOGNm9vyV5X63e32Qc5jgGNLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39055}}, "1.0.3-rc.7": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-use-callback-ref": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22f45bd55a33f429127a2de2ff51e69d8bf7ba60", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-2kGoBbtjcpuH4LxSToJWE61ZDCa5Z/a7ydpe1LogL6h97qJmeLdqfDCz6yBLBTAv/ZqjndLSSxiJPknDCNbBKQ==", "signatures": [{"sig": "MEUCIBtA9RWohq7WbXLpIWQqe2V9Oh37lG+Ufdl7xFLyKie9AiEA5g2h4yCec2KKSR2o1i5vKL1Mm0UPtu9Mmq88k2uhxJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39055}}, "1.0.3-rc.8": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-use-callback-ref": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "017f3a35ffe15d2570f7083cc4a3467e7b4bf81e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-nFG+3NzeqOcbccelJCoFh/YUyyrigsCwJLKuDjJbZUJxFf9oL6sLpGhnBuoIn6QRewiyt2grmfl0eOWeropTmg==", "signatures": [{"sig": "MEUCIQDmDEhgZ6RnA2X2BOKTidBfnD1Fmw2YfOuFQQXqxxaOFgIgNJgHUm1vXigi1IBOYMHJ/EnEMWbcLBG4QCjKasLfMvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39249}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-use-callback-ref": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac2e16eb0c470abbe23adab49aa5e8a89c9ddc0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-S2wKPSdY6kK0UJ4ksdzzibdJIvHexTlwX6ro14ycfp7aUj2KzoKJ3ugk9t38eB0k6X4zcr0w1PBAOJxmPfnqKQ==", "signatures": [{"sig": "MEYCIQDZPUbAI6qVSHoSarVbR6RM2QkwANZDwiXQ3tHEWTpDRwIhAPIe5HRLsu0cN7en/6rpG6VMRmabqJf4ZQDPVW8yx5sg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39249}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-use-callback-ref": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "892fa1b002e99f48743b966557d94d8e261f9dda", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-3u2Kg4tza2Ta8MrHyzjz7M3jinewE5Qe6W/LWKHwu0gBy7v4rF7CEsRdkb9soITYVgwH5YgJ8RHvUf2W0yA54A==", "signatures": [{"sig": "MEQCIFX5XyU6v1t8DuizTQ7iozF6mqWKwSVBEXKqptGC/6djAiBaMK5qqWdnA/YfOFfL3fxzc0Qhuz2PS3NuxCxWqNT8mA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39251}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-avatar", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-use-callback-ref": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28550b1339b54805e913631f9b0423741d497a50", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-ZsTjy2EPgCc+0AIyenOXfNfzv1s97RJdncxVubCm5AR9SnCgAvdByxh35CT7O9UApvEpIHQt97rorWGZuNahSA==", "signatures": [{"sig": "MEQCIBMedkJL7sRc/wGPrh4TPFxdbw7ZNawX2JbprtpUcb8HAiA1cMkauLW4iWajfBKOnTG68cRZm8pJKE45AIPYNASoKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39251}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-avatar", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "06a31d06bfb03be5e0d156a5af26a437dce96e7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-9ToF7YNex3Ste45LrAeTlKtONI9yVRt/zOS158iilIkW5K/Apeyb/TUQlcEFTEFvWr8Kzdi2ZYrm1/suiXPajQ==", "signatures": [{"sig": "MEUCIQD1wGopuxIZn3TEqFe65Siovj8lmOegOcS3bjQFS7J/JAIgIWtedkUfyoevM4nCPr/54G6oPT6+qZYA55x1sPhwYVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39196}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5305f8fbba1d8c4d24d27f364524e04a4ebc6163", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-ij/oL+4qphHFlwp/IUqU8sGGUQ2O5Ps6nrG9URXBR+sSAOFTMoqlifGoiKqAZMLanHYPp6ieUPM0cFWes2IFjw==", "signatures": [{"sig": "MEUCIFTu9S0dYhZauldhMXzZLqdkOIrKOwJxBzKrBkdLSJ24AiEAxPcd6TILAsm7l/LW8UcIe5hnrtA+eNaHPVhVyIRNKqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39259}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0c5efff36a2965488089bf66b700646329ba67d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-HWmhB/tsTkE5sWKLYrESja88nYna+Ll4yFM5VQTUmp/pl7vftDf4u+MyDLxAv/a321ndCBTcP10OCc+dZQZfCw==", "signatures": [{"sig": "MEUCIBlYT7tzgLtvyyedGgwloblisfeiYvXvW89XbpKDBQlyAiEA1edIDq6uYSXIUt4Ok7nRveYqCnoeUS2ftoSJWhtjyqE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39259}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2cd045fe42673cea17d51f2c686c32db7b0ae0ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-l+rxr/K3AVRwJND7s9j3d1i59ycVTorYsvo5W1Bj+vSTC9XtE7pVWIpm/YGwPQLhLHEt7bwG7EwtCIZAqlzsMw==", "signatures": [{"sig": "MEYCIQCwGNhsDPLD3Sil50rjEiAfudnaqYu8i9TtWGNO7HoNywIhANCdwOHigslR9k50TVrCIo8xWqQmtjUCZLVNHzNt79o5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39259}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-avatar", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "de9a5349d9e3de7bbe990334c4d2011acbbb9623", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-kVK2K7ZD3wwj3qhle0ElXhOjbezIgyl2hVvgwfIdexL3rN6zJmy5AqqIf+D31lxVppdzV8CjAfZ6PklkmInZLw==", "signatures": [{"sig": "MEUCIHckVLTy/QONoxBtg1YUVTvJ5lc1KZi4sBkXiNc6H0POAiEA79LvCSh7n8E6AVmzCLoTYFnefrLKqdqgVXRwDDi9OEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39226}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8dec098abebb876645cc35c59b23299b1a1be7da", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-254YUVQsoOLga4OPekLMdAe/J/M4uMz07H7SyMMgv0Mmqi0OtRT+648GuFu4oeLHNGeLFCGjv7684v5vTgykPw==", "signatures": [{"sig": "MEUCIAZ1k7BHH8JMG6yq19IkqI/JeM75dzF/uxc2iCfW1yc7AiEA60ktEIqP5GgP3LS/OHiB9VbA2Yrm916HzMtL/EjfGHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29644}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4894998d058ae4f009ee5b126c9d040901ed943d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-1FmTc5p6mIVjFc/nejpOWeVLtEG6I2FAPW5W8FMdJSrIpDoYTONsClOX6OSSj5MGl3E6UBYBPZmQ6qXgL5Mj5A==", "signatures": [{"sig": "MEQCIF939fQHhyvPm4fRBaWLZkK4+Cool3im6xiDJXbLewYnAiBw0uaIckPqbDUeWu8Do7viXf0PQVbINFFlxqhiNsFo1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29676}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "132a641aff4ac6fa61583095d5516597a92adc94", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-dZI4nqVHd01nVXnuNwuBsN0+n521faOlFQCOwW/fXEOCnELaZM9uLj5AYv0ztBsa66x71fDkL5LcTfAQkBFtuw==", "signatures": [{"sig": "MEQCIApirfoF95ypKQsGiKMQ/mmC+hptbdKLWuIcdYYXlnynAiBgL2Z6zSlqOmeAZPWfkP/cHm2lqeTkVy4MTyWXfMwxIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29914}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53609f78f2028ed4dd558e2bbb1e23273eb5806c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-UXVQSmRYJo1s8Vxkb0L60muImNPTCpxh4nWw+oOhgkjPjJ9BLP0ch7ymV52BGqTg5uPzH3TGKciC8c+z5UY9xw==", "signatures": [{"sig": "MEUCIB+d76ptAZpR1SpeA5b5wyZUAffuKVMFSlX6iyc39lkxAiEAuho4UPPGsD5lpz9skIzpT8ZT9Ea8M/jZY99EDvHsTYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3b94de705f6dfd01d7e3214f2bbd1470d7596607", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-/wjyavTFkRrcL1/8Rz1/TQnfTKAnQIgKX4E25y1WH3fDhkF0lyqvJLjZ1QJ4fbEb+DEudzdrWmPkEsXPWzQfjA==", "signatures": [{"sig": "MEYCIQCjMKgRnurVqc0T4I4qOyfsI2jvnAPOD4Du6Jcz6PUYXQIhANNzeuF+B9Mdrbu7JGv4OVHbpXfHM0cvm90c0jiOqVkt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df675edb7c70f77abc78c61e3c732af1da655ec1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-FDRsFeav4oD7ez0LMrZdHpxDj22B0QDMFaNvbasmz0zUyl8RNrzPzGU43CXM7aqGbPwrF072lDB7RtOPTW9QqA==", "signatures": [{"sig": "MEUCIQDfx0qKttGm707qfHpbvEGoOpeHOzXDgPP8SjC877QctgIgIHn6U6wAEgUdg01ZiHNCQuWLvrOeop8FGScTV1+N0sw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-avatar", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "087451a4a8dea51b69a697d20757cd1a42d69855", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-jlsJSYP7j5Gm2rs8+X6qTwOo+D5e3FA5WmKoTVoYO5SO8eegj1Y4e87dKvjeTD7Ll/mzPMetu4unmliQbtbBUg==", "signatures": [{"sig": "MEQCIFThOpYhFL7Co2u5TdgHXG9AuyHlneNosK7kcQU43wC7AiAP3Ud3jePAsw8lmbIwjWueKmW3HBUQotBSHtjFpQhwAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29660}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-avatar", "version": "1.1.0", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "457c81334c93f4608df15f081e7baa286558d6a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-Q/PbuSMk/vyAd/UoIShVGZ7StHHeRFYU7wXmi5GV+8cLXflZAEpHL/F697H1klrzxKXNtZ97vWiC0q3RKUH8UA==", "signatures": [{"sig": "MEYCIQDN8R8gIR83DI4L1n3vXEQvjwbeUK1tnVxYrRGxeyz8gQIhAOKAYTb0lg3p2dn1GIHRcqYvaaaCy9/pPF35vTqNzLvG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ece56056c94d4abf69c93364c4c21fe6d255ad9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PPfyRxjSnuXvr0yn6cyqFfYbZTDM3MSBRiMF5NjhbPx34TmZM58/Vz9ebpMoNoFJd4HHDO7ok781T7Td9wwFkw==", "signatures": [{"sig": "MEQCIEyCL8VTy0y1TJPz5GU/iyB9Cw/JPSWccD6FbAlpCSr+AiBUmmz3ehSKFyr94nNG4iRehqOj97KNxRcjRJYt8AzHzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f7410b478fdf4fe73e30a71d96e62730666ec22f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-dKtsk2Qhi9HYG4gnFqxYPPzut2iZlzwnECf2RnSfLcsGGvHg9jsbTzwDq9v2FiAWPdZdWqadNQv97CHWrLHaBg==", "signatures": [{"sig": "MEYCIQCPgfQsOoV88Bo9AvrEAt/xb8rZQSS5XXyW5dT3j9E+3QIhAKFozLq+OkvzFB013lwjQCD/WR3UXRTx6w4IXN3EPZJd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ffac10135fb143273a0499217ae0c4b8c9138cde", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-2n72YDu/dxKIkbJtPBLXPGx4PBMjSH8NV4GrQvtZ3r1U1hohEHlXvYW2/pr225LWcW7nuYf2snXOybMDJQ31zQ==", "signatures": [{"sig": "MEYCIQCpch4Tpx9f2K3U2EmWpmTcUNTvIu1oSQRxmZU3jRpIuQIhAMwcNz0SuPHHcIvSbPGQZKaIox1Ves9xIyuu8/ZwYH0U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d8dcb98451eff12f83d3adbd639396371d46f76c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-TFlMkNrhykLCEQ7Oxcfkm4l7HE7l25YEoQIBeVxLiskxZ+ees+/rL1DrhFZhVXlpiopiVXsmQCYu0BTuSju5rw==", "signatures": [{"sig": "MEUCIQD8f62kpzWs7manzGP1qb5pumackLVHn6t1ag28XdEenQIgSR6K2uftQvH1UyPTLec2YbBslOFG9mHlUpXp9jU0Fok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5f7754227cfac146ef4cedb7bafb37460c389119", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-uriL+fnzw7MpeYO1UtPO23r7ifkMaz/zAyxmTTtxhFNyR7YUhntXXsd00bM+6LX7IiBh+QwoD5+Yq4pgWb+bDA==", "signatures": [{"sig": "MEYCIQDqoeHsfuf5je3asiv8dq8R0izblWDZPxD4jpDS40oLCgIhAOhrjmUWeRw9+wmy40VzyQBdcH01+ZAB9ZuhOp1EDNec", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "978a6ce8a48e50de30343d8f41909c07c1d5c3a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-7zOMMR3UzAarcODVdpL3xIP3uT17W9zLx2VZFS0NXCkq0JsviaQo2UVLR4wAWsrUIMqHIF0StxA9CFvN/OpbQQ==", "signatures": [{"sig": "MEUCIQCbpZxLsVBD+WTTPSp5VU37hJMZf90dJFtQcW2vS8OkHQIgalV+1zHeJ6c228Cs32M9FikXQ6ciZSF3RU0aFTJIXYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bce4dc4022dc44ae564a666dce3146c99dca945d", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-UcvzlrCsSOEDclllJDB0wcUIs0uxrXJhx49jdRmK2bh8I/NBOsBdI31yzs+VN1WO5MfQpQQDUsZqwqo4eTlPuA==", "signatures": [{"sig": "MEUCIQDji4MEr1sAP2RIIpFZVYaxqTnTQDC8/BwpObVYf3XUwAIgZ4w3emeyDeqYQDH5ZsBh9AmAOeqyCC2sO/iVHym5n+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "af0a238d2794c870874034b390a3b66d598b63a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-tm8WwJANflCfVGcbJIwgjNP3y+kD03BZRLUbOq90X4jYWB21Ul4USDubsnZqRfC/6CswtAncL4OWoqW6arWOZQ==", "signatures": [{"sig": "MEUCIQDGBIBxTLkTsSAo7ApCKY/sl5u4PpKr2QhHMzsOPXnw+gIgaf702xzktOprme3+y6J4wwezVTNfWDZZ+cs/oCxtuP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "093cc130516f8f7fe2d4f1bf215714555c10b123", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-oUd5zLttaAmTZaDINL3PtYmUxig2kEqBoUDgaZVN13v8ibDDxP1P4ft1MAWQ2EwKCYKT8ZUKeYbRS6PjtD2gqQ==", "signatures": [{"sig": "MEYCIQDC8uHlPdcyp5TWkBWAClFYIibEp7N87TczL20RdVjsSwIhAK82nK4VHlbcK96GlUTxPAhkbHko4adYYjUDmJ2LEIIM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30400}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.10": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.10", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "281623ca2196e40e81edf89bf78a72bf44f74cda", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-cyUQN23mLwz8jxlux7wdXFyfvFjMkDlFU1aZHYTSEMsCVcQcRLo8a/YjZScr7HALwuVLg+tcb4dybZvrGTDmTg==", "signatures": [{"sig": "MEYCIQCLbExHDoJoz5AhQufMwuJ7bPQqQ2wTDlmmyO3OKqJL6wIhAPBFDX6nKFI6ARWUXPXI3myQ7kYQQuXM8+QJychORcEv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30401}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.11": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.11", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "792cdbd31490a8ee14c62e64af508e8d4c1c182c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-y45g+QusYpC9ZNb+mZPEjnOWzNyHrWpcJsDGLOmAiEZbtpnuZ2BoaMUutPeZyYHSFVK9FqOJY3c03aBu6pzPRw==", "signatures": [{"sig": "MEQCIDC4uKeALCZuk08DyjyfYxBB1VqESqicESALZ2vC+xqfAiAMlUjqA+WNGPbAjgAsj3ETnVA2kZzQ5kdAVaIcQrvPlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30401}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.12": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.12", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "95be571128edbba357d8ba7f9e84e5cfd8508246", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-uSyT7bQTBjhtN3q4EBZa2agJ/U+CuMb0nQ1XxgK67oBsgZ2dTLlFUatyqeBFYIzaLarFFl9luKSP1k9y3gYalg==", "signatures": [{"sig": "MEUCIDpzDx4UP36b0nmKHY3wAxw+TvA8JGmvEwsgnjc2YPvtAiEAiKAxdi4sCCB4JfF9fCqSaHQ9WUEb3Zq4VpbF5JuoEgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30401}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.13": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.13", "dependencies": {"@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a99c92438531e4e0e44709046f787bcf33743e0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-swEsHUNIsiYhukqq4IL6J7grG0kabWtVN0Fig3oXZXZRv6QQZhjIssbPayTGnHH6L6KpTFYtGaqQZStpUK81NQ==", "signatures": [{"sig": "MEUCIGslJ7VbRptllMXPFMHWJ12D45qg5NC6C4cYRTwpRWbfAiEAn11XhwJ8G/YGN9Ss0qVkbMQS7t1AmmGlanH3len6aq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30401}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.14": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.14", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f51d386569a0ad84a335b005f703c1f17a98e1c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ADYeWqjJ0L1OPRcLgh6Axm86oSOGF5JFiVMFMerOOjHFOabbzzz+uX25zdfqmQIGGhets1kwpxUhavneKBxPcA==", "signatures": [{"sig": "MEUCICPyAio42vHAzeoPgw2yBjY1hC26E02dmbr5C45i2UMcAiEA6SaLbto1ZcBJayMeEuAAgcxALkPa2Web285/P8v7Vlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.15": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.15", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "04fbe61f150e825174cfb3e9198f865266a6e978", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-ZOcCG3kd4M+lrd/7z7QxVs7SS3Pg4LYOjY+MZn+deatp5dAMrhwp+RbUofUFyNgXPZXvlBf8Nz3INkfcv/H2cg==", "signatures": [{"sig": "MEQCIC8mHEXJh7YpGzaupKv+wELn0nmKfLMgxM+X2mdTNVwIAiBBajSQVSH2JwgiQRLiF0ur8eOPkhpwFgzO/nFItEJPWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.16": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.16", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fcc401bd0092dfcd7cf40356daceaf4ab0f15870", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-LAEiXQIcuA2gY/IjlWMdPPBZf3jLTymp2soXf5A/6w+IPofhoETR0yDd8ta+OiPcbwWZPid+KqorGXwJlTWWZg==", "signatures": [{"sig": "MEUCIQCfnWpuYWEpma/SkCIPtIGHljo0YJdWYr4lTItln40mQgIgH3r4CNawy+kgrWEdDMvVfF63UClTny7vwfUCOJ7gb+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.17": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.17", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c8c2adde9119bbb85772d5cc0d43b82e4b8cce66", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-L5k1UIvhozMTbM7i2F9UfzFWEW6iy+zro026WoYIkbS6Jic7vjfYUKaz/IYSwyyQmLPLIiBXU0tC2bbnVi2HFg==", "signatures": [{"sig": "MEUCIE+0fXLWe/K8oh8xwovYisSWfGuLrTfxeBhjzFCrotVcAiEAjsBpFR7XvR0uCwy7I4uSfR2zgd4cOGbLMKStUU9j018=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.18": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.18", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "79eec7743ebde972b819d48026cafc50c60caef2", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-NprTPur2AAEMBNwSG1qlGF5pJEvKFkDpDMHCtv809Tzt7LZcsOXCsA5JkJft5AyMLe3OfX8bwp8biQxIoEVgTg==", "signatures": [{"sig": "MEQCIDR/qrsinVO9aPFadnndVjxo1TyIeP8OB8RyoMDuM831AiBY7LdaiJgXqQv8EIxvkbfjLaS9A8THCabpBcYCvFX0ng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.19": {"name": "@radix-ui/react-avatar", "version": "1.1.1-rc.19", "dependencies": {"@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a208af27db9fa606eed32bf66c299daabae37080", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-EFTPlAQYBY0sw0EHyEzV8mHfx6u9X7LI4lbkrl2eovrEDF07zDvO/jWBa19TzwhECjR2xgjLebrm5yblRnODLQ==", "signatures": [{"sig": "MEQCIHxJ+KBsZHwM4KsvQcYeoOWUrTf1FYTBHvqtOJq53Eq0AiBPNVTqZ7hjavpMVA/KT7/LVwaiN8OE+rHrfOHMVn7EsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30406}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-avatar", "version": "1.1.1", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5848d2ed5f34d18b36fc7e2d227c41fca8600ea1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-eoOtThOmxeoizxpX6RiEsQZ2wj5r4+zoeqAwO0cBaFQGjJwIH3dIX0OCxNrCyrrdxG+vBweMETh3VziQG7c1kw==", "signatures": [{"sig": "MEUCIFAAVBT2yRVo8pvXDKwOuoBIx32DyzjC62GpQKzIOepHAiEA3ZXAwOcCJkjCwoQq/jDDz4MU7fMcVGMaWYos9L6m2u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30367}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d20be7f0ec98e4edbc062487a02c82e54579783", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4hjShjur+jz3NAQs4WuTWPwG5YqSfSBkviLzICLtgRiCVuUib+/kzGX5n8986aLQB26AqLJJVxiH4cXHGCB9cg==", "signatures": [{"sig": "MEYCIQCfM0QbjQMuncQ1oHNJ2qdFcIdPg01k2eM7tDDA0EeEYwIhAPyh8+fXF7q2/moaBJz/6v4kuIhiVKc0Gx+e6kroGCtb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30135}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "29cb7861bc113f4e1c0a7ad487345addec3f0dca", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Y2LP7+wwJm5uxxWkEXCZ7VMWaWFh/42uzEWFaB97ZMkqwSOEeh/v9wfllZ1nbnOEFv7X1RhOS72v0DclgHy+LA==", "signatures": [{"sig": "MEUCIQC2ZPf95Ll+q2Hb9VE/M54hD0QfZe5JgJN2Be0KnXIjDwIgFPrS1DL162cnbWmM0wROOKwaW6fkcx7qP7+9n6gayjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30135}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8f5bf26f7f2afc11829b01d9fa71d78655ed038a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-0d<PERSON>Yoxey8wXYy4cLh9CpiWEvQpYFur/JrnWyjP3pMtvup9krVJ2vcNEEhJnhQwNyXUW85S6gU/wd5GUr/LpB2w==", "signatures": [{"sig": "MEQCIANhprNF7/BVPNuABMGdCaUem+F1TPBiSP0njd6FPxipAiAhoAs3xTtUxWYkara95G6X5y8X+QmF/xd1j66IkDLm1w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30135}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-avatar", "version": "1.1.2", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "24af4c66bb5271460a4a6b74c4f4f9d4789d3d90", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-GaC7bXQZ5VgZvVvsJ5mu/AEbjYLnhhkoidOboC50Z6FFlLA03wG2ianUoH+zgDQ31/9gCF59bE4+2bBgTyMiig==", "signatures": [{"sig": "MEYCIQDwGSFKfM4TYa93cDohsNlC+3xVp7NyOsTopMFHHnaDggIhAIDSjU5MP+gDvyJBttHe5RuUckL4pzs4Vi4PIXtIkGpu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30097}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-avatar", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-context": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f730b019ea208053ead989ee17115ec3ee058f21", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-PL4La20Y8MYOe/vPlK8rn1hBIlaSrPY4o0Wzh/3aRt35gOUmYYhhVzP1UKNMOJ2RvdT6BMTbnvzM/l1tImtQLg==", "signatures": [{"sig": "MEUCIQCtRDxlW4PFlv+39cKoy6cmEQKNtXPQ1Sf/JggEO48mawIgT0gpOcsmSpWiXDJKtlR3pFk1eUkrZJ8vuoo7fAlyQI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30106}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1c3cc63c451a1264f836368e8eb124facca3557", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-K/U1dAJfspHpJxNAHW4geLulC66fYGkixImQa9O0Ul5v9+SNG+MQIuQpr16bD6xiAxA+DOGPfvMCEOtWD+2ITQ==", "signatures": [{"sig": "MEUCIE4OgBznR5S9alzsSIS1ChSsEd/+jtGR6ArAC17Vw9voAiEA/20yefOZ2CLE7xACuQY6Jjo6po0bQwq8VfYAgGlXZu8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30348}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0c1f0381e0cdee8f17236d0004159d45e42c096b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-vBhtDyzoBEaLfwsgiIqeZbcbaoiGD/CkqVR7iqoos+qVr8g/FlsVEMa0zI2GO3i3MWPqSqJq0n1L12bo1FmOAg==", "signatures": [{"sig": "MEYCIQCqtNA+T4UZSCfefpR/STgS4r3jPtUIZk+q3b1cR7FsjgIhANT/FKTU8Q7e0+7wN0D4i9DEck1Yzfq11b+5TacXa8X2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30348}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d2a422fdb86c734acdd684d0a78a9877efccc915", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-CHXF7RAlmoFhX5Nx1WRPZo1NxebHRLZvQIeUKA9th2D47nLJrTCkksVUojk+d/iXbUCMHywxWIAy/+Pz76ytpg==", "signatures": [{"sig": "MEUCIQCtKU+qH+NW+UyYq0aAYzb/+cri5aSBzUsarHJjLoaZkAIgNnDog1I6qMNRvRjcgT+Dm6CvCwnxkWtY5hPzhoaK57k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "07580748157e49f52bef0216877fb97896b860f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-m0NhXtz6SOtbP/eVIIL/MX1jJBBfgts1eoEh/1htHMLxk6857ztI82JJ9NCa1B8/7R8vmSzulU4DdtXP0lDLhA==", "signatures": [{"sig": "MEQCIBOxAWOgYrkj2QDlgxzrGagHFVvaFxGTLmwJr9rwJyciAiALYMrYmjDg75KfmGvbz9dSpP1JZ3aBb3MC/QqqlYmdjA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30452}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-avatar", "version": "1.1.3", "dependencies": {"@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8de2bcebdc38fbe14e952ccacf05ba2cb426bd83", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-Paen00T4P8L8gd9bNsRMw7Cbaz85oxiv+hzomsRZgFm2byltPFDtfcoqlWJ8GyZlIBWgLssJlzLCnKU0G0302g==", "signatures": [{"sig": "MEUCIQCwXHR7jAEqMVhgmak3/WbHQp9oDyiClv+8WLNxlyGHhwIgbjghmgRXIB74U8fA8lDyEavuaQaNDoHHoLy1ivXj+UY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30414}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-use-callback-ref": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0191649a4e4b3bff1236b98917f95c9225a70c1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lIVhn7TZ9q5JkgxDYVRAFZ4mmhqeXI/hW2cf7gsYXYqRumzM/6bKtNqgyklq5iyUA6xDXe+pIxJzL8EbY+92aQ==", "signatures": [{"sig": "MEUCIEea+BV5T887pyi58vegUZRkj0kVowFP5AcfCloLcRu6AiEAuuLrsJZn9XD04wnJFbtjiXH1uOqrQDADQQ+XE3BGBMc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30473}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-use-callback-ref": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a71cd7362d8fa9c4a81925157cd1bf05fb01b8cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/NZ8Lgkg652hB/bIO3GKKSvcf8WgtH6fHqoiEiq+/sqonpBEaxbILpo/aqV6Vs9quBvqU2fpZLVEfToz/2C6mA==", "signatures": [{"sig": "MEUCIQChVEBCCw31cMqj+IoM48EBkm+wwb0JaSJWCjp3+H6NgwIgMZ6PABzVf+DO/LVdmpFKFwy4AaGpCzvS7bwVNrZQgVk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30473}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-use-callback-ref": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb5e8dcf74169bb8178907746b3bf468ac005181", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-mupN159XwoVisCgWXcTHbXX2dhwY0HrzOhrnWEQ4MH1kQVoV3UsU4S8UmfVvGLR2wMrT8npLhROfUC6mHzfXZQ==", "signatures": [{"sig": "MEUCIAeQC5iUNGsLT96IUqBieuzgj8QZ5feX/Xi2d1xvhYAuAiEAj2mhCY/WWZjUi3rB/D4rmGw4Yz74pa4ojZw+/Nf8+vw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 30473}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-use-callback-ref": "1.1.1-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88ae866a347eb5f792f8aa77979803af89f4962a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-JT4VN6zmG+ue/GGUTz14UVGqcX8RMnK1OFJZP2A7YTi2OFWp+4/rnjeisSjFLd/KZwmyigJDyDN73YscKCXQ9Q==", "signatures": [{"sig": "MEQCIE3vQxEX5mYy3OVgArTHN4HHWJk8hrozkkPwchk3FFBNAiA2/e/1Z0dDFTduh0TxbDQngcL/VBJj/A27ExaxERwJKg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.5", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-use-callback-ref": "1.1.1-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28c5ab40e44f535f508ea7cc6c49a7630df3ff12", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-gitVzOIOb2VpVF8rVJ5blU735orri1XIpiIuxgYUMUKzz4l7GDhQL0S8OSgZLyoJocvYJK2Y6nqvCncS59Q2tA==", "signatures": [{"sig": "MEQCIGbEOAMR5aS/uBmlqsCmDZrQ6XAKUobVAsBRLL11j4B6AiBM/8xiIWlu+55mzTJ7W0gw3BE7L85j5q/Gqi0MYByKgg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.6", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-use-callback-ref": "1.1.1-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e9328f1c2f5d399f7d60c31915ce7d6862e8399e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xdl0DezMPLcwTvAST/LifRBfdPGsieenmcBGQ4ECf0iu7Rx9VnkWzCm+UN3crkXq6XtOZ+zFMdk7m6wmieIfzQ==", "signatures": [{"sig": "MEUCIDC8Nm21u7Mo51ULX5qE+vBP2eYDi0o2184e3RcpqxU8AiEA72Dc+JKp0nJZJCId4c5Ius4KHV5yexCfeEhVI1gaMN8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.7", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-use-callback-ref": "1.1.1-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "954c12f06d2a8d056818432d286f97aa89a39b31", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-uX/KivcB37ernVW0rYjR8dSX1b+9snkg9c+UQ4S9NqOXMzXOx2woVw0dxdJtBDaimUjVJbeZn172UbRZrhfi7g==", "signatures": [{"sig": "MEYCIQDQNL24e/naN2zAxPsqT0HzVLSnwJePNNQulBSdP4mwUwIhAKiq1VWsEYRcRRLF1tgDt8A37XjAfOwaGkgmxdZc2tLp", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.8", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-use-callback-ref": "1.1.1-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bf4a7dd56f2ab0cd0e5e666d9a147057fdf16206", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-QhkT0EyA/ALONnhK4mU5yfuEnX6gDWj6hdr1UqVFdLQ0w9uAESBa+p80ZyP2zqNB0QwZ5uI8tv86b+yiNERqZw==", "signatures": [{"sig": "MEYCIQCJQoXregYpTDTOlBbiYLB+HqTsKzj4pyvVu+WdcFmbTQIhAIVMC4I++1pQnF8m0KsFuAQzXUiM0uPU3UDRFpiyBDDd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 31422}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-avatar", "version": "1.1.4-rc.9", "dependencies": {"@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-use-callback-ref": "1.1.1-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bb5dc98ccdc851c67bdff84b776d2e06b7da48bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-BSJcphw8D+q7mat3nrvTQphMGKDMQJL9KYUAvTJWgha7d5tfPkIzt4tbCGl3luBsp4g1Wg2ByfaI2vaUlhvVSw==", "signatures": [{"sig": "MEUCICToPUj1UISZl4xboyCDmrfZLAyCgcKia97r0TSe9Nk3AiEAtlkObUrgQoq+pMy7jZppqqZemOzJncAqiHljqc/cn5U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36156}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-avatar", "version": "1.1.4", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "55dd879cd57b08265cf313b92ba1021f7f4d5abe", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-+kBesLBzwqyDiYCtYFK+6Ktf+N7+Y6QOTUueLGLIbLZ/YeyFW6bsBGDsN+5HxHpM55C90u5fxsg0ErxzXTcwKA==", "signatures": [{"sig": "MEYCIQDrvmPMrKMMIYxnGa93pxu47f4am5TEtFA5c0fjtT3iuAIhAO4+Nncg8j/UICzy9ecdmur28hbhjO4ytAh26POAP9Mh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36103}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744311029001": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744311029001", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9aa8b32b534e08a8729b4706c9d62acb26afe397", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-avGOdD1t8jZmIpdjslOWhng593HzPf6Mc1F2qMoFwiFB+eeYvnCWyxmHovZm5AW25NcsrccfZvk0rmqmGq742A==", "signatures": [{"sig": "MEQCIB6jaFyPmuoQNmScwpurlv98i6NIz/FVnDb2kanh+BLeAiA+ZOTyieNdJPmLWDBr4BqSBezFLjaC/5VGmXqVUdjq2g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744416976900": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744416976900", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "439728d2b95682f731cd9db3d21f85db2492956b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-zdSj6p2vOCcd6W3siopbsUlsemA2+XckjdkPdcmMPhzkVSpsAqq0k+8NZkrCrF/vvAHM5He/ajJO50OzdUWeKA==", "signatures": [{"sig": "MEUCIQCKD4tw93ZoeOSl+KFfSeT9b8VDfPO0EPaTheJApAhKNQIgPgdcrNb8+cm7yCyJFMI0HfKPasOwmQ0c4FpZEJoBgQo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744502104733": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744502104733", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "61c4b6f0629dc7b17c4458c4beba00ed636ba94a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-wzhEYp58jafP9rfdQMHwl9c4e/D7B3SkU0Zy9/+eI7hzIdk+ieVXy3X6qaKRG2R8BPafiGj2torCHbSHaMk46A==", "signatures": [{"sig": "MEQCIBB3RTfFGuwlzDU6zX1Al7iQY8uA0RSyrEJm+/gf0R7pAiAVJTPUTrKvZ8L/ShNzXaeHKkq50xOQ4FuffBPArFmTxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744518250005": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744518250005", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1d75d5aca23411984cde55edcada2be40cf18b85", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-clv5he6yCUQoAJxbQ7Q0Ap2HtMrivAidHvSfgebI5jJqiwsJk+Zx5ieVTfPu8B068l12KWp/zF465DeTv3BzSA==", "signatures": [{"sig": "MEUCIQDSmuq0EicdV8HRgxHU8aT2nmjNq5EamB83hS9dT21s9AIgB85qXM2V4OFcvcggr4D2x+xx2eExsh+hwgWzo9hYIPY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744519235198": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744519235198", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "80d7eea9acaf829d4f15a8ba7924a99beda28093", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-O+TTX8hmfMj529XwEPoRyr3JFq3Z2pimqT1bz6smeiBn7Jbs75NmQLzz+T7zO8iW1PwEZby0hmZPO6wicVkTrg==", "signatures": [{"sig": "MEUCIQCzqgeDiVza1utqKqGsvvaKjDUNjAL4IezgOAh/f9/09QIgHFgr9ZYtOaHQrRVmftDwhR1NzgYDWu2v0FUj20nFXHo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744574857111": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744574857111", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "56c5f8a5b08546fa97eee7431cc16298183c8a1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-dm5p6UvxwlBTegTyGbTP7GtHCFjjytEigcqyN23sEFAFlNbkd64nEabwf7mI0GNIZxv5NQ87ajjUyWUqhGD/Bg==", "signatures": [{"sig": "MEQCIH/db3RMUgbND0UuRccdCXvsGX6vujAqaFNChsADSBvbAiBMRkyPgoyu/f4pd5Oz+1bYTNkeZZrQG5mWpL37Rt9DXQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744660991666": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744660991666", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0b081b9baba167020525545210e9d09fb5c81e73", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-w706V6SVdeUTVJvNzFoQBQ5wFcGBZvtkkheRwbNOr+1B2N2V2W50+v3GmmfkPesfEZv7jdnH0Rrozhj5SdlUzg==", "signatures": [{"sig": "MEQCICRE23/rsFFqEtR0NQylF5EXShxy2HufP0dnELTp5MDyAiBFUBKN47x2IaQFOqA76eXou95SHQCN0RmEwHEKvwRaXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744661316162": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744661316162", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "353b8520e0605aa4d25bfded5ea77d63e421b66b", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-ZyvOq7ymaRdJB1oTZ+ekxLtuWFQhF8RCviTrjo9F+MwJGhSm3LBQ4UUndLlzwdg/GYnj0K66DBpn8GTCWiIb4w==", "signatures": [{"sig": "MEQCIBwss3takS1OUSFry1HqkMkBvXSwW5opKJH1t2YiD8MeAiAU0odp4SQOMZ81zX7zZ5FSwXU5bFEHQ0OAaLoQgZMkeQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744830756566": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744830756566", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac77b899518ee43a95a3107e6354db27b9619db6", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-fzGq8FFfKqegj7wlsKAnFWI/y56vLQkmwomS2RtVo0a00obYMVff/G7rIvp4RE5GBp1J8VOhwX5H0n3bn/n0LA==", "signatures": [{"sig": "MEUCIF5PCdgMMqDG0ZBftgxaDwAeiZY+lWzQeWt3HMFdhEZSAiEA8w0qdMCLtjOybHl6ydYeIUFFt4/CmyjnqLq2jCFLEqU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744831331200": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744831331200", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43aec2445de16c72f7aab4f3f68bdad5273f8896", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-+SCV+ATVQyuP9YLDJ+QMIC5HiA+eCYpVBnWsfwJK/BHmpuN9o0kMun55gnZIIW6gsRtgjOWMClZ9wqx7Z7wQLg==", "signatures": [{"sig": "MEUCICAVuuRunNploqKIGLMEQFWlkEpvXIfReWsxvVm+u+Q9AiEAs1rB67D9+CZj19yc3ksoZox8N3GUQrxYVqxJFRi48iw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744836032308": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744836032308", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3d7366f6a4b7048b28accc6612ffa1b44f86d1b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-VVO0pvFI9tF+uBqqp1WowcMmTex4zG/r1zmkfMPPDPnRvBjhBUXEXUBGCuh9qoUOfy0X3ZpT6NFe5uOQKh/xUg==", "signatures": [{"sig": "MEQCIFyYrBSw4QsWm790nWIA7Z0GE1D/fTnneNBTqMW5kDz/AiAH5PU/JGD26s3KxSjdKl4KlT26t2as/PG5Frv6oUQhlw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744897529216": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744897529216", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "89d841b852cba002433e8e8386d0456173b1ad17", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-LMVHIIjuq9d2UyVuRECGRtmlw18+vJxZGRKMIMPtgBPknoTMHpLV0Ur8ePndOnQwWZRjruV7901mgoAw+065cA==", "signatures": [{"sig": "MEUCIQCZL9TRqJWvff76W71eaHaefLwOP0mNDFwBLLmGvwZLzgIgG2QKiNH1aDQ2DekzEELGhLHE/ddzMfytBirlEmQEnkM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744898528774": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744898528774", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "33da93868ff34642c7413dff02e40b07f5c522cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-1oaymAgq21KEl7gmu63hcFUniezLQhODix5LQ9gOzMRiA16axha5Kysu3pid7efcml9g8wxW+XXQYThijrnMbw==", "signatures": [{"sig": "MEQCIBQ9A2AIr0c08vdiROhoFg8xy08YK90YgK/gORFMaOMzAiBrrDqDlntFEfcb6MVPWoGcR4nd/IRg32xx876YZ8i0HA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744905634543": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744905634543", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "701422439aa9696a626affef593745e9dc9a0d95", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-4BbIjzv/LrlhvpTDKqQKn90pVJi9zcnKWV7UKCR/iHWv1e6o2706wXftpvBbYeIbVZO3TbfAXSEhR2qrpVVGng==", "signatures": [{"sig": "MEUCIQDB3pMCE8QRwBk6Ka6O6PQvCMHtdU2e/1aiPKb7TNt7OwIgSx0FvjaYLYX3p23ixaRkvV4HphfM11y1b2+biCUMCDI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744910682821": {"name": "@radix-ui/react-avatar", "version": "1.1.5-rc.1744910682821", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "14a1884ff116e798044fa19e9de4296bcb2f477a", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-X6adr+eMH8UJSO2exYieblxxY3YC7oOIa4EgIqRoe2OdQC+a4OsIiCMzIVjtafOOuWgKccKRzOslSPLKyZ5WLQ==", "signatures": [{"sig": "MEUCIQC6tATR0KYbJ9veTklB5SFUT1tt7XiOLxQSa0PN7J7qFAIgXCSL8prppsSbwE0HVQkeLmv3ISstutUb5RRSdhXkw60=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36689}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-avatar", "version": "1.1.5", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "67c8841159b5e5d079fe61afa59bde0d57d33953", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-bC1RzfRN8jWayFTGirqgnlmjJ9IHl3K6GT4Z4vDFrU7DdPVc4P8XSxUe3sRicjL6423WIWY6p5ys5NndNoaeAw==", "signatures": [{"sig": "MEUCIFOp7mR0BgWJKBjKqV5Pk33e43i5UOBpPVxRHftpyqTUAiEA3XzG+tBW6tNy4sSCzzULPHT6AughSR3on9Y2XYQYVWI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36655}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1745002236885": {"name": "@radix-ui/react-avatar", "version": "1.1.6-rc.1745002236885", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-is-hydrated": "0.0.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "96a8464573de04462e483a0ba8ebea6cb3a038c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.6-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-NXShk1TwTTTbr5z2F+wqO2tmEfE30CAEmAIVtZwIwgvCYn8KOs0UND1q7fWy8RtgkRk1eyY8lk9fdZuN32BRzQ==", "signatures": [{"sig": "MEYCIQCW5LluBGQWkZO0hZPhNx5XmCMXr9kAHg9KjDHSW8/9kgIhANodE8ZwtPTV5YmJhqt+o6+AwMpTAcZiW9WgXfRKZ7GF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36121}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-avatar", "version": "1.1.6", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-is-hydrated": "0.0.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1b8b59318713e055cd2d40b48536f361be3f22c", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-YDduxvqNMHzTQWNqja7Z/XTyFc8UOP98/ePjJTFa1vqILPlTPcQaVa1YyQMiQl4SFQPA9Y/zj1dHBgMlE5G/ow==", "signatures": [{"sig": "MEUCIQCuGxCL4ZXipIcOll4oA3JX6EcRcJspO+TFRY4fuXh3vQIgA11EdzgazNmYNltHWfYY5QRHQ19YiRfrJC7+oXUE5gQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36104}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745339201309": {"name": "@radix-ui/react-avatar", "version": "1.1.7-rc.1745339201309", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-is-hydrated": "0.1.0-rc.1745339201309", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a05250a6ae660bb41d6a38752421c7f93e329463", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.7-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-KrFYYf439XzFeg/M9qzBIkYioVXGmiKGVHIlHY+da8ahd0Tf0ywfO0b2/JUY0qrS1jtOzOfs7zRAdTlTtaabPg==", "signatures": [{"sig": "MEUCIAUgeIHTe83P5k79ysSvsG9psGLrKvmWo0tWteqzojqIAiEAsSjkZeEPZRmgKRZEoM188wiM65jSK3ugW225evHzdGQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-avatar", "version": "1.1.7", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6a1334db6292f7e7a977a831dca6cbbb10a3f938", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-V7ODUt4mUoJTe3VUxZw6nfURxaPALVqmDQh501YmaQsk3D8AZQrOPRnfKn4H7JGDLBc0KqLhT94H79nV88ppNg==", "signatures": [{"sig": "MEUCIQCV9evUsHLfczHlt2jygYaM1AZOk3I2oSl+k2PMXRj2/QIgbOwKbJbLqonqb+imYTmC27DCwUPZLKHFpdUj+3MerQg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36104}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745345395380": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1745345395380", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "afa76a1f5d5dc5324ec28a9ba5f5ac454b2d0d83", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-805s8OsVh2qyZelu7qCeKjM/l1JOd6J8hzwSCKwvNb17LyOKq/RpYg+vLHi39ms/2HmtktSyPSEdpFeS6ylqkw==", "signatures": [{"sig": "MEUCIQCNg5JTZShG63moq0Y/XN7xUeePkYFQAEDRDHbVswZytgIgCL9CAIcUbkqBIKAA7IJ70OR/Od0NtKDUGE6uXVdP02g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745439717073": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1745439717073", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3aa7fff8053232fcfa2629942ac6ca6863e7b45", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-YaJEJGCZZDfLKv/B2ZbjlKKI+2WAzCMr3GuV3hC3ufepGJXhcQPKK91FXbUV99xaThfIXrNx+CgQvYFGvVzZ6Q==", "signatures": [{"sig": "MEQCIFj26ChyJzxoqGge+U9+U+Z2e4HGuStelK4qsba+pY+KAiAn93d9Cf4tTGQlc1ixaG9xMdmshW98U8mU/ZdeYll3Fw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745972185559": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1745972185559", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e9069176bc20fddd22a74e339c93fb14530328c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-8fqyLkcCHGS6cHthkS+dOhkGeWzFV/GbE50BClKAiqECn8gwYibYrDB1anFhymPK+5UnyKO396eDxea2TnAunQ==", "signatures": [{"sig": "MEUCICzi+oL8GrgnhRgZXEWvlfERtIG8NMWGjGOA+0r2M2EVAiEA/ajL1sc5ergrUphMH6UM60/JBn9ypC0oBbUwaqZzGu8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746044551800": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1746044551800", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aa085790978db7bd87969151ecf9dda0922b9c51", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-OjhwnNdk8h3Tqr/zMpALMShJT/I4OlcnZLtDk1jn8fn9Qonlm5Bs07Xsy0AlL/G9KtgnFTgF43lCEDKbwUWEaA==", "signatures": [{"sig": "MEUCIQDdxQ0EkCBiXyOFaubuLTUQmpEe1i7ANzK2Lb1hIe7BvgIgYc49WHMGBiwwdfkpfymVKuh70xQ3qjE6+Db9F4uur9A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746053194630": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1746053194630", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "55ab8cccc2330ad7dd82eb37451cfa05555f8ca7", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-fi7b8bkb/sFDRWgKSmNkIpCBuqs7EeM+Y+cPTAvCMOHVzOzhopDVkZwZxExC+CIfJsIMUj8vRt7sh1DTyiZk3w==", "signatures": [{"sig": "MEUCIFo1RouBv7DWnHepIzycDyOHbyHwwsIns2TzQ7uC5zBEAiEA/DJjNHXxmEDYw/YsNZqdxLipLGWhY2cmEMZONK8anxE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746075822931": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1746075822931", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d1b34ceec7e03ba80cc22956231a0780b77f1717", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-o35HUoPo0K5j7qrQ1PB38Ci2bLA7BZFoutbFK7P6sPDRoDrhdQrjwFlsBik+QSe2wVSNF7VyAMwOGFvKaKUPkg==", "signatures": [{"sig": "MEQCIHZwk/XS86CoOfqZim3EcHC5FjYCcBJ5vkyTq6la9u65AiBztG6ftt0hh2mHdm2RbgBjX+/ovs8rB7N+xYpY4mYyGg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1746466567086": {"name": "@radix-ui/react-avatar", "version": "1.1.8-rc.1746466567086", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1094aa171fc9db2451d878c459dd329cf5536762", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-HvXPyFyz6sAuQfIWMIO9xH7ZLVfd1eAnaT9NPFXHnJHTqz/emam+ROIrU1J1nMp6K55dryQaK/ATBmncwxCqGw==", "signatures": [{"sig": "MEQCIFwVfCK9ZpCyU1QAMEw1zN7O+I7iovDroM/SVTzOeHYqAiAHgmToY3G0kplOEAV1y14mRmNyeC+gIW7lF3hXSv2mJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36138}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-avatar", "version": "1.1.8", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6b6b1c3c1ec90c0548528d5e29df1775c6e6dab9", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-TxnjlV5BQCY3a/RdbJCXbCW1CWWr2W8Owtzr5Fm2i57crbNIjiq85iZ8/iIE8OgkBdOPTO1Jd7Mc+3JNG+QpQQ==", "signatures": [{"sig": "MEYCIQDitnLnYFtutXub0/KXscRNfn8H7G97JS7omVe9yj2XNAIhAK+GYGyomNaEUDA5do2b962grI3urP2cNQD7oGkOFTW+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36104}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-avatar", "version": "1.1.9", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "592b1ff78a5ad90bd8bff54935d88a6b6960520f", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.9.tgz", "fileCount": 9, "integrity": "sha512-10tQokfvZdFvnvDkcOJPjm2pWiP8A0R4T83MoD7tb15bC/k2GU7B1YBuzJi8lNQ8V1QqhP8ocNqp27ByZaNagQ==", "signatures": [{"sig": "MEUCIDOEZLL0hBbTfhmCIkuEmBX2oWhq8QkiEe0HzwpBby3dAiEAtiqqWfF3C5UAxnEAt98l8s2zUTKJ/sxmFCPTTSTjdKE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 36104}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746560904918": {"name": "@radix-ui/react-avatar", "version": "1.1.10-rc.1746560904918", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-is-hydrated": "0.1.0", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-0ysWjUlNQzV2pKHG57x+GB67KReJd3lS3CWrKs+q0px4s+GHtrsATTEpM5v1drZDX0hvGNjiTcUeDVDO+WqT6g==", "shasum": "685bd8998810fb286c7d31d5f35b073904989a7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-avatar/-/react-avatar-1.1.10-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 36151, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDUz5dtjfufd5gPSJua7pfvdUltpwHJcABZ6bd6bOlyMwIgK2gopST1MHlAv0gpJv+opDbPXD5LLD/LcRZ2YQJzszw="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:43.614Z", "cachedAt": 1747660587619}