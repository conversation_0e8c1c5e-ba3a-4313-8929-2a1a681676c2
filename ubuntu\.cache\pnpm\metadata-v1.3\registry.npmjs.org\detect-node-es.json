{"name": "detect-node-es", "dist-tags": {"latest": "1.1.0"}, "versions": {"1.0.0": {"name": "detect-node-es", "version": "1.0.0", "dist": {"integrity": "sha512-S4<PERSON><PERSON>riUkTX9FoFvL4G8hXDcx6t3gp2HpfCza3Q0v6S78gul2hKWifLQbeW+ZF89+hSm2ZIc/uF3J97ZgytgTRg==", "shasum": "c0318b9e539a5256ca780dd9575c9345af05b8ed", "tarball": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.0.0.tgz", "fileCount": 8, "unpackedSize": 3210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHq9HCRA9TVsSAnZWagAASUgQAIhtIh4DnUWHLd1lvwKs\naIx/d2SdaRNwnO7m5+uFxyeN8hHd4hUCr1qsDWhNAguew2Bp8recj3qqrela\nlLbjxjD54bicntQ+hj7ciwVvczByUGWViAlrKB+CKhzxAx2KhDIjiXEBfUcv\nGwiiRYSNAybqftEbl6BEZ+QNBeUGctlloFYJxwE582iCH96LJPVJP7cthvLN\nPcrt6G9leZIT/zJOy092BDXqUokCYmjytixn7E/mMTOHn2XeyvD9Wxq1Uwsv\nR9NDI+glsBQkTbN/dmbWGid/amxfBCZb0UzRg9V9uchBGcSqGaCjK0enbtmb\nWiT/+/oKDzESPj5l3BS/QWbSYqOX4gqobouPGWlamsBuX/JCf92iK9tZ0uHG\nEcjlhoBHFQpDld4quVqg7vbC6JF6dXGGGl5Fvf1+5SxUd1YXce5dl9S9xWT6\nUTb0EXsZf6cSmLKGJaebEReOMN2PMmRKKT2PKoZEp0ixPD8r8HMAdW6OWafL\nCWnwKaa3kqXACFwASUi++m9co5PSYiliZgHis7DMWRJZEkMRysf/U5+KUlOD\nMkJ0Pv9908rMfHTVabzlc3j1w7mfIBQM+df+TK2Snlz/qRVa2/JAAiKloW2E\nthp60zWHwnXYvcM+AflZxi0fn/6VRCETekgQqYYl114IXLEKfUqQqKibam/A\n3fgX\r\n=WJ6a\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE2zRgNsR3Jg3yUGMG1YIUJ9COsajiFa/l5X6YtfDnOzAiEA+5lnGyewi4OFVbYwQPIMNpbORSUe1r2g0eQPeyRZf3s="}]}}, "1.1.0": {"name": "detect-node-es", "version": "1.1.0", "dist": {"integrity": "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==", "shasum": "163acdf643330caa0b4cd7c21e7ee7755d6fa493", "tarball": "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz", "fileCount": 8, "unpackedSize": 3210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUqX0CRA9TVsSAnZWagAA/8EP/0HnaCQqvq8gsJlt8/ky\nSoza+i6zkHxBeDI5h5wNiMMKxull18tFh7ok7jkC3NLnWaNNEVAjVvzcRto5\nIoxaXpzdv4NODZ3CiDD31sflHSxM3oJv0rdUDFqI29g5Z6B4J2U0EDh9Zenx\nnazPrhxzXyZP7UYmfahO3hxwHSI/TqX5CC33y8jaIFTpQnDIlUxNteg7JnfQ\nXLG5yCpnB6jENZJ0Opw4cX73aw2+3o616WxhHmVaqIXOkhh0UlczzztvS0+r\nzJ+/H/HfFKpYLdKPFUQT5TuYpGW49pef56DIfL6DWzX3auH+UAYIOTiS4gRE\naDsoAg4oKByzL9AqjTj/LBeRfwj9e3oUJaHSkNange+s56RWZ1lG1142tEzw\nikWBMBsW3v2+/BFDg8MGKvYBHeW/1Svl8lMDTsAWTC/L+ga9Z/nKbnqIprJ1\ncRSIqzmkJQ18RX4b5su93BDTImzMxNQIYljbkmgvRfBToYmm6IQvBHNUgt+v\n0cqUaOdHCaSIAdRuwalwHSpMeQSMXMZZWXuIFks957EXST3k/61WmbQodSQ8\nr0Yh9P5id1aJyO4vbeeKC981Ee/gpXfY0KNvjKZ2d6uaYvbODD2dditdfZ0R\nyBsEm5u7yAX3ePUk0GMyr89dFIUHmK6wono3G2KyRvSH0pXZ5ZFBjY/YygvG\nA8Q9\r\n=lKry\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDoKga0ej7CgI0SEp2NfSbdoyBzHvxqFwjDCGjDVB4/JAiEA3tr+uVagFhydM1E2tTFXGmlRcImwez/GWq7r1uXkUKI="}]}}}, "modified": "2022-04-28T10:58:36.865Z", "cachedAt": 1747660591729}