{"name": "input-otp", "dist-tags": {"beta": "1.2.0-beta.1", "latest": "1.4.2"}, "versions": {"0.2.1": {"name": "input-otp", "version": "0.2.1", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aab8a1de937d472961097a28574bbbe040655fa6", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.2.1.tgz", "fileCount": 7, "integrity": "sha512-pc4NXuN/gr/RX+syzjxy+eOlB3ZpPc2i5aDfMLhbxZXu2gska/GvOR6/C3MhK0J+fYNXrZQmy85K33DRiV1CJg==", "signatures": [{"sig": "MEUCICV5BqLHNuVQuD3A4tdE0WAl3csVZAsAZgvJAi53YnP5AiEAsUYVpPZhoj+dZ53T60s/HrYA14snreP6X/0Y4tAW51E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13226}, "deprecated": "please update to input-otp v1.1.0 or latest"}, "0.2.3": {"name": "input-otp", "version": "0.2.3", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76ffad1cc4825ee6c1e6238e39a82bd6236320a2", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.2.3.tgz", "fileCount": 15, "integrity": "sha512-2ru0E1ONGK6kSb0lWHQKhAcG/mtaD4NnnFCy4izNSfgRUmYCPN//jLeWQBuG6+lKSH2ZRkdZ8nHIkBUuRFEbsA==", "signatures": [{"sig": "MEUCIQCsX0V3y15ltuoamax5lgrpOMxINvlmwep9PLqBUEp0RgIgX+eqSWrctboF45qhlNV+WjKwmcCusxuATWqDVRPQRCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129235}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.2.4": {"name": "input-otp", "version": "0.2.4", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8953759fd747370aa8aeba4ea8a9aecd24c8342e", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.2.4.tgz", "fileCount": 16, "integrity": "sha512-FHgLzR1vGZWwhVZ+hgOtopB13L59F+84YO5V9y3jKbFOzgLe+wUSJ9E/H84UGfBNjG2b4pUnCtMx9wxAZifBDg==", "signatures": [{"sig": "MEQCIB0gOpBpm9Rjj8/b1NGLz8Jmiz+/1d3PV+uBYLBNKmLeAiA13m4bxQsPFKV/qCbr0YY66NICh1PGr4txxSH53O5/Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 133611}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.3.0-beta.1": {"name": "input-otp", "version": "0.3.0-beta.1", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2b6543c3d5e929b13053e8260a8f8ad1bf24574d", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.3.0-beta.1.tgz", "fileCount": 8, "integrity": "sha512-5+2a/uj+clnlM3isFFSS8FVhnhW7Pny6lYpQjbMy1jPVbPiSdloS/flH7XAzQoHQ65NbiSxdZwb6XDkiQup7WA==", "signatures": [{"sig": "MEUCIC+nqV+X3HoN79deJFbe8ij/Cjpimwe5jTX6zuSCuLSpAiEA0hDWU6di2Zx5fv5cFPDOlFKhHFAdSZJ2np/hmOB/juM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40019}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.3.1-beta": {"name": "input-otp", "version": "0.3.1-beta", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "563be3aa58ffc8e6a513c050d2733ca512952fa5", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.3.1-beta.tgz", "fileCount": 8, "integrity": "sha512-uohfLD9xTegnPyQnxAUlXO2jim6wLwetkawZzaFEpqN5OK+7C42yDUqR9/PlYzIy9Q1YeBoarX83LDV7vtjIXg==", "signatures": [{"sig": "MEQCIAVtrQEKPD2P3CyPVJb2Hpcy3UfEpux+/54sllTPIx+5AiAdQJQvctG1DBgcrBM8Miv1jKI+yotcUStHsWnSt7OBQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19802}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.3.12-beta": {"name": "input-otp", "version": "0.3.12-beta", "devDependencies": {"react": "^18.2.0", "turbo": "^1.12.4", "bunchee": "^4.4.6", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@playwright/test": "^1.41.2", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb544df89ed582e3a532b72bc216e1547adab5b5", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.3.12-beta.tgz", "fileCount": 8, "integrity": "sha512-ZvAKF+Ta4555BAwvWz77pjChNsmNBrMBbZM6oJfsSJgZgaLaTALg9A+IbQrvsf69Jke0la/izwr4VSMdzbzAIA==", "signatures": [{"sig": "MEQCIEttN362GXKVgnXkuH8xhWJ2Keuv/bGP1xRZ3axKHLMYAiB4b/XBMLU/vo5XtNOoKMnHjRp485PuqZLIrCtiVgOI8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19798}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.3.2-beta": {"name": "input-otp", "version": "0.3.2-beta", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9854df2849a94a24929cb595cdad9c535303a2ce", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.3.2-beta.tgz", "fileCount": 7, "integrity": "sha512-sUnAxAnA9OBa2Dufos8sjrPx6RgornayP8w8hJn2YWmesvet0PI5p8XSdsfN7d5iKRPM+1/KkLynHHn+NUQ1/g==", "signatures": [{"sig": "MEUCIHruIh4narJKQUtlHfkE3Te+3qtwV3YiKPtOkGcJCHJBAiEAhF+uaIlcWHYH+ukBnfmxBoVt06EvShB/WE7TMrq61tY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69128}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "0.3.31-beta": {"name": "input-otp", "version": "0.3.31-beta", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e3d5aa397fea8dfe1879400891f51fda80bb9b31", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-0.3.31-beta.tgz", "fileCount": 8, "integrity": "sha512-PyOlHQ3qfZEPFTu08A8aDQhv4vi16RS6LWGPMN6wY0kA3zIX3dexWYnAb8NXHL3ET2QjcYgfyycCDvy3az2pwQ==", "signatures": [{"sig": "MEUCIQCI9Jvs3ZrPcOCDDKtuhAFrpbjo1NRlvN1QvYhMbrJ2GAIgC3/IsqhoryPY75osjf4j1GugedMIKUyAh/xEPhqvY2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72180}, "deprecated": "please run: npm install input-otp@latest (or 1.1.0)"}, "1.0.0": {"name": "input-otp", "version": "1.0.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5401ff54137aad8c6209a79dfaa338475fae6152", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-sJJJM0Gul6bHz/Hjewm0Ebxoj6GCcX0ZD2pEhxT85VWzkOrK0iGtysXhtMrovRJYBz5RVGlHBsLl1VE07J83EQ==", "signatures": [{"sig": "MEQCIETPLoczsfLiJ0NyT5pgDKu2DQBthIG78HYrfr436yo9AiAGEPx2hJb1bltwb5/crcUB4x4pfsbqXIVHIMJqeci0zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79563}, "deprecated": "please run: npm install input-otp@latest"}, "1.0.1": {"name": "input-otp", "version": "1.0.1", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dbbde8cc95aa1278956d2bec337d4a4f33cc716", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-AFMGRsOwXcH7koO+8nnVcJFYEe92tNmRlb2TUKbj9Bpdyc44GaS3LfJam3MdoXQv1jejpMS0+fxJFSCsEDHd9A==", "signatures": [{"sig": "MEYCIQCRG03wOFkHTqQWm7/GMHHAMRKwSyBYsPrpCCjz1iVgPAIhAO4V9dhfegGSBeET4rNEhUJ2ma2d+Ibm+oQDywGetVHq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81703}, "deprecated": "please run: npm install input-otp@latest"}, "1.1.0": {"name": "input-otp", "version": "1.1.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d6c017659b782c51e843a840e041a1a52dac462b", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-bHM7u1nIbhtupHrCO7hxBksbAp5SNuYYLG9pmMeL9ghJPDDmyJRgJ8dcAylHT9JHyB9tLB+U8tjM8xa+DTpacA==", "signatures": [{"sig": "MEYCIQDkhnVrWh0EPv2x8GG5NAnV0r44tcZ0B/+pdIatrZWYHAIhAMAPMPyDct1o7Cp4Ht5YXPaP9Pwlp6o+OzPBMcROlcSn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107049}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.0-beta.0": {"name": "input-otp", "version": "1.2.0-beta.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "477e3c6691ba417728782152f002054f6b6dc762", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.0-beta.0.tgz", "fileCount": 8, "integrity": "sha512-o9Ulvhffk04LfYVN9hxaSi3J3+yZMYNlX74rW390cLeUfhS1iTos4PUfgqG3OrXxbOwJsxnhwhhxrK5CRSYc4Q==", "signatures": [{"sig": "MEUCIFuaxJgZ/cX2xcV/RiAEFRVmrrCG2I/jzIauvT/bE5QMAiEAkLU0JV/4rIi17ZzvoQO7y6OuqTAL3XGXglUXxY3DG5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107132}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.0-beta.1": {"name": "input-otp", "version": "1.2.0-beta.1", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e74db6f7f1b93370187868301551277d3f6300e2", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.0-beta.1.tgz", "fileCount": 8, "integrity": "sha512-fDZGF5/wb/OaWiumkwwH1W5XUwC3ctTTDnRlPJTOvIs9OluQ8YhRMRjUa2VSsRdkUzaRnKqs52DZBFHUYcrERQ==", "signatures": [{"sig": "MEQCIAr+/EixRDpOxk/7Hf2Fq6RYQGEZN7mmSrbcxAhy2UagAiBanzjN63QA5Iz9dXUq5O8mvZcHDZ8f/da3fKr4xHW2Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108904}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.0": {"name": "input-otp", "version": "1.2.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13a8b4d2d62760d9fb1e0d89bbe398a74024076e", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-54bYU7SqAYdAKNRpHXHGJZtuiSqSIHEs4iSr7Rte/Yot2IiovUtc3TxEcF7Ak5yeuhtJ0pvr99z5nHpPne5S7w==", "signatures": [{"sig": "MEUCIQClK+lWXrjES5Mzhy8qrKc1g4OoENW3RM+32N192JubBgIgVP5tUhYH7jSVdb0THS/s2yLubRC1q0g8wMYrU2qQteQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108929}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.1": {"name": "input-otp", "version": "1.2.1", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1e80a9425e54f89684aa19867f92d51c43672835", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-KxJCO9v0DjpyBKbvB6z4D48c36aiiWDDF0zq8GCtbT9FdCzG+gCtUfqy0nJlJKSBmdf68t0xcNZMZUEolSg4bw==", "signatures": [{"sig": "MEYCIQDMD3EeXH6O9UCpFrmH39R+k6/6Ew5oIDKTWpFPv/b17gIhAIY0vF19USRCnrXGMu410h/l4uOZbZvo+L6vAYXl9DWU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108312}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.2": {"name": "input-otp", "version": "1.2.2", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0a92e80591c4c64d30684509ebba1fcf59ef2d5e", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-9x6UurPuc9Tb+ywWFcFrG4ryvScSmfLyj8D35dl/HNpSr9jZNtWiXufU65kaDHD/KYUop7hDFH+caZCUKdYNsg==", "signatures": [{"sig": "MEUCIEvGf0rASDk9ocVM06DNQWAeW+84olV0jXzXe8a7b/GIAiEAsFQN8MZ5PFbTBr5M47XAOXHJPb4OwHi0TpXPCyqyt+c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107750}, "deprecated": "please run: npm install input-otp@latest"}, "1.2.3": {"name": "input-otp", "version": "1.2.3", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0841dbfd01d59e5183e734d74b0f9e81e7eb1a9a", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-pxYvgnihL9KAdpcShX2+iKctdMRbDs36bIqd8uIsN3e5vv9VjMv2bhO3S5Bl1PjcDPsA/OXZe5R71n8oVtucfQ==", "signatures": [{"sig": "MEQCIETAESAAXycQ4nkZtr1du/p4PVG/eodZZBoI6CYDnB2QAiA8X+4XakYUUFALDH0a2h7WO+yV02kYS0168KltId+4ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108004}}, "1.2.4": {"name": "input-otp", "version": "1.2.4", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9834af8675ac72c7f1b7c010f181b3b4ffdd0f72", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-md6rhmD+zmMnUh5crQNSQxq3keBRYvE3odbr4Qb9g2NWzQv9azi+t1a3X4TBTbh98fsGHgEEJlzbe1q860uGCA==", "signatures": [{"sig": "MEQCIAHdjiI979j1OGqz95qG9e1h40970ku5qmmVqQKxUDCIAiBRg4hIL735thEdWg6hrm1lBMlIAAnNKXmMyak+DUJPQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109578}}, "1.2.5": {"name": "input-otp", "version": "1.2.5", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0"}, "dist": {"shasum": "7a9ad66085547cc54dbeb44ec66c0930365fe998", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.2.5.tgz", "fileCount": 8, "integrity": "sha512-XoRSZyr5NaK++/Ml/0bsTycKiSq9M5u6fWgG5Cqjldj6+ontrKnPRqn+6Bo4fzUwoMXRenUdacVfR7Y68gldKw==", "signatures": [{"sig": "MEYCIQC/piTF8FuwRpIdhsfZE95MM4RU3t59OXJPSf7Lnq+5awIhAJGKzhMQKQqkBtzCown3Jc+3kcud4JUUsNksGBi5vhFC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109597}}, "1.3.0": {"name": "input-otp", "version": "1.3.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0"}, "dist": {"shasum": "94567f7dc1f6006a6ce117970a019133f7954854", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.3.0.tgz", "fileCount": 9, "integrity": "sha512-lK8UIatyHedrxtBybAbmZQx6e8BeGJVNGP2AB5cBFFRNzvKtLC29+kFpDK50rf1dpN41ciQbdrSVK6Mka1Xv7w==", "signatures": [{"sig": "MEUCIQD1OM1NuDlrxfXAfI7x88O/5O6TmoE60ZC4mJzdvBOeTwIgBll6mbfrEre445ptMzJxvtHBOXarZKVldPbts71EFs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146165}, "deprecated": "Please update to input-otp@1.4.0 as 1.3.0 is mistakenly redundant to 1.2.5 due to an NPM publishing error"}, "1.4.0": {"name": "input-otp", "version": "1.4.0", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0"}, "dist": {"shasum": "09c4e903b9f3a4626d22847335872dc599cee8bf", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.4.0.tgz", "fileCount": 8, "integrity": "sha512-whngTwnXhSQijjxNGUaU/O94LNugnuliZoWktQd5nVYESNlb01guoo+1T/1zppJ8fbo2J2/GVe20x3M6qpgdFg==", "signatures": [{"sig": "MEUCIQCinlE6aEvOCzhtaMN4AtX2hb3L+98f6e4YwySOXZY6jAIgEhnkkFsA3+BFzYi+MFtF/pwGXwTHdh9dRLim0yiEsRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110851}, "deprecated": "Please update to input-otp@latest as it supports react@19-rc too"}, "1.4.1": {"name": "input-otp", "version": "1.4.1", "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "bc22e68b14b1667219d54adf74243e37ea79cf84", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.4.1.tgz", "fileCount": 8, "integrity": "sha512-+yvpmKYKHi9jIGngxagY9oWiiblPB7+nEO75F2l2o4vs+6vpPZZmUl4tBNYuTCvQjhvEIbdNeJu70bhfYP2nbw==", "signatures": [{"sig": "MEUCICMyFu3mJetkOv93LGVvvWE3PzOfAogA2ljunA15b3U0AiEA3lJVto6kxjiVeL+O2D7JLWo0BKwRIrOwP2+X6F+BqUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110883}}, "1.4.2": {"name": "input-otp", "version": "1.4.2", "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-l3jWwYNvrEa6NTCt7BECfCm48GvwuZzkoeG3gBL2w4CHeOXW3eKFmf9UNYkNfYc3mxMrthMnxjIE07MT0zLBQA==", "shasum": "f4d3d587d0f641729e55029b3b8c4870847f4f07", "tarball": "https://registry.npmjs.org/input-otp/-/input-otp-1.4.2.tgz", "fileCount": 8, "unpackedSize": 110633, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC65/ZXRtT7qwSS/DaqMLO+6QlO5YTD5MQUU8+4KmzxcQIhAKFgmjNksYEkrW3md2gq84uTJ85wAGWfqnFl+qrMvVKX"}]}}}, "modified": "2025-01-06T11:48:28.956Z", "cachedAt": 1747660587988}