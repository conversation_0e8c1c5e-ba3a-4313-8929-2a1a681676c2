{"name": "@radix-ui/react-use-callback-ref", "dist-tags": {"latest": "1.1.1", "next": "1.1.1-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.1", "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70c8fd7d6a146bf65860ae7d4915985aacc53acc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-WNqLyGlHuxfghIPuKH1/1q/NNc+W2ve2S823syefD78btShmR6LJqpUXinn9X7uK7ih/pB3UySMmbTv9CWrb9A==", "signatures": [{"sig": "MEUCIQCp5P5AkvI3quHWOhu1FonCMwyhvGjQyngDvD9pPqNlRgIgQ1hABliDIJE5sWc1RcO1d23jBxshrXT2T/k6/ROtpe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VfCRA9TVsSAnZWagAAlHYP/0a6akWsoGts6PhxwHjT\n9AdoMJnFPSMu0EkT6tCRTpsdvQ986eWuMo5CxHxwNxCDOVBrhznZuexU8AUh\nNqHCxUMaOlbabDmJeDvQRZltdWxIFSgdYfkfg/gFXYHUycluuoFYL3FdmDxu\nZMATunvm1N0GcsWRKvYjg8IJ739ATHHNgXq/73J/eX1f6Y+Kz2KoJEMdhN9z\nC5EwyHLZdRV8vTFQ73S8RcoehhEEK8zngI08dos3Pj1BJ3bMG3sBbi4qNxOp\nN6QEbhztoEtJxc+nqFVP9DPmt3YTiPYeZ45lVI6G9CCLFJD6vPCnTldDAif5\nO869Sf5R6oYNG9LOfg3ocXHq2iQGEj2D0lqC+YtECu2yoPPT5VZTjTWzHFQW\nn0Iv6shV+/c1SNyTdnO7OixVodeqZcVfUeVwi4HspVj9dWYsE9gWKUQ5Zqgq\ngpki5mtM5hJyAP3Cn7nxpsNK5al2MZCP3Ecrui5A9GORoGuWj8LJQp9fst+1\nB8Efwe9aZvoz8FSs6B3jCMHcWN1v6Qvkce0zJhYAGeDPMRARHh/TdgAF+6Fj\nIgtOngqBHipZl+qIF4wXzd7pmZkMafv8eH+dnSE9R40lskznPPPh8IDVs1XK\nCK+haNscZcZOZs4yQ++998XHXFHwCeHniBss2WF6S3y4O2GAmTCabEf+m0GA\nh3ed\r\n=l6NH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "08ba74e7c8b0c550a2930c26b894ee7f3ba0fc49", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-aaIQEbucBqLMjXEpJa0EeC90eJo1qDHQeqHUipXIJowxCQKNVdqMWTqbySm6RN2e3QCoGF16OxE2q5cD1Eslkw==", "signatures": [{"sig": "MEUCIA4dptTbbmkzSzH+PP2dAdw8VJfu4Sz84Rr+2UQ+sUyNAiEAzXHkImIS/xy1a1RzoGwEFSAW+x+kyhVKB5ks25/Fxp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPICRA9TVsSAnZWagAA9+YP+QAQFW8YHNzSGL/I4xdW\n+kFNURGPBvER8Q9tM4rxuNZCaHDzj2s/7x149dHcnhfLf20v09jlR0quicSU\n45QnghMGXfNhXOHTaFEVC4w/2SxQ0BITDSIPboalzkAOg4S2+R/ZHP0WhEjy\nmoAbUqojAFVD2CCv3zNu+dvvzg0qGL+yhpPV1NPThufEcvgYfSc1LcMqXCe6\nJ35/lI5slBcA/bZPWx4x5Y/W9DCjtn2i5lx5VxvGnm1eK2CN+fXdzSK2RySO\nyh+Pf2rd0onhnKN9dcnSc1O6hmcEyShE3j4GACHeA6usIfqjSHJTaAbJQBO6\n41xMQgnWw03fl+OyvK+exkspTMC1EE6XaUKkXycwIwC9ajK27j/NQGfoBDNX\ncEUywTFIRcr3iuIIVIGkp5K3tNhZwrDg0NLSWBaysD0lnpe3LrqYB3v6Wsu1\nEl59aoPY3o1N6OGgsuim/vVsWSTKrrK0Gkf7TX6NleNp+83Y4Y7m6dXB07VD\nrqXx4iECYP0vH4i5IzZZ+frJIFXaBZmhuK/FXCrRoo4oPYbduhzmp2FXMQS6\nAqXrSdq732DtxpOnlD+RGCqQE7NHDVLBvusHFdg4kav+HUjC9Ey2eGe0c8ul\nMcpC7fu3iiP/lYB5xHW5oaffwWIH4Die3UdAw/uHNd25dX6S+h+FhlnzUVDD\nm9YS\r\n=yr5k\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a1f68edc4936e9e216dac27f93c6dea8f14b0431", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-8MckTYRKSkWj8EW5Op9OglVYPoYeGbOxxn+2VnmeLltJmL5sz/kRuD1t4SwW4GC14hy226IOoN6oiRdLTSg03Q==", "signatures": [{"sig": "MEUCIH6Bmld4xo57Ujd+qwEx6Jx9LpyFsU56By6/DaxfywMrAiEAwWHwRttuYt3Vs/7xyQ5I7dHErbyEzjS4cxY/hvBauZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IQCRA9TVsSAnZWagAAwXIP/iNU8vyLGSIdDecMfo4z\ntv4AhDkOR6F1H1M3MEFZWgQZg0fsbDgjo6qsas9fCyH5Z/q1e9bL3kL298Jr\nfQq20cajOnG5I4w1EUIn9bXwKMPcLLrIMUIogaEIpFcSDLDXM+JZyH7a3K1s\nuTqe5qV95zaQ5NlL2UcPJFCFg5Hup1IVfQzNVUrurOf1BFmLytWMSl70mRw4\nC9yaVPZ0Y8U5hAReEZfXQL+VvnR58x98wDDcdOg0dtF+d+5J0+z0uWW9d80w\nStkBdqOBsfK94dZ+Hui8qxcx964GcW7SLHwsmeJ/PGfudKnnrZmMbjAMsNLo\nR4S2xm1vy83atGIUkeSvysIaUMROgbF7uf+/j/Ko3xcdB7uS/fbFvodpOdrr\nBVbCDAQSLZloybslu+2sQfOke2UeNromIyTFapoTtFHkDfvF4jKx+Y1RHnRk\nWd6iShH/keVtryBr0LaveKBknvhaLb8VcZCcVoH0so4wz6blY9LeIC6gOgTd\nZgAIrbS5zb3Esmj9VThb26fIOzHSjeFVHw8mOD+Ce5DrAZbmAbYSVkApzDgv\nT4zWPEzXiUdWDSrZBO223rifHb3vMwUWNHomxzYpWQWLdny27uhf97msXCIh\n6NRtwg351xWQGqrAWeoBglyWlvvwH9tf9W0HyZ8ATkFp4rK8hFU7kK+/tJob\nw9tW\r\n=atTG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4f2bc1196a22ea6c4bb5d9e6dcb4b7326083bcdf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-ZJ8oPoPgxFzOKtu+ME64uEzz4BIuA9Tfh1AHKmXVM2r/h3GF1gOZr32QlLoOSpbJEf0MqzjZWs9DltjnYbgbzw==", "signatures": [{"sig": "MEUCIQD80P0/1vmpRz6SPxG7hJqzqKWe6YZjgHjbTpw5oaV8RwIgATrzMpsREBJzXl5dYHHmWfrk8Z8hjn1tZIU/Am+4Qao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wFCRA9TVsSAnZWagAAnssP/RmW8hnU/L8ogqUvrD3n\nU13v7SK3zhGvvfoBsyfdqkY/mLaPSAILt8iGz9phSd8ny7AatOXO8Vjc7WQy\nLBO+tOHcX/kc7kiYOPil18XTClvgAHx0NNd7hRh9Q8xdl9juClAX/47Lf+w5\naVactJloHco+dyMO5VozhPWhZTTuZyYzT9T1+yOKON28uR+SrSMSUCUwFF6C\n45QdH5A9o5+o/eA7evrXb+BFwy6Ni9I3nr1lPgCXDEACA1N88b3I5BqBepem\nIyw49Zo2FY+65LLJIVqcD9laiTv7f9DFlckJpC+QUanoZ5GF/Ut4AlxTBGh9\nCpL7cFOc9uC8HsKJ/IYBXouhhntpS6KCWuBOk68ep2iderNCrtx6AtFgRkA5\nOuJBGjia2T4gh9q5QxvTXcqBQ3zaQkfbCdh9RRCjM6PJGev8+ZprNDQEfDde\nwq8it9syrFZWQxrL2Mn+XbmyyatC5R3l9zVcHYKlqBn8OXra1PaUei2CLxZA\nL9mv6AuPeSpFZN6QwcJV1bYD5C+j4jf1v80KxbF4hBtda6xSrCbtah7nc54+\nTJXIBRZX2LFKCoqL+8vcV8MHHIG9r2Ginx9+uCC2+ted0MsadZ7QVok/HDhz\nTPg1Kx6xk6ODUl6NIhXogr/bwfDUqpH6ciNM7ditim4jlUEnumc1xzoi/88d\njtoF\r\n=44F6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fa8db050229cda573dfeeae213d74ef06f6130db", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-z1AI221vmq9f3vsDyrCsGLCatKagbM1YeCGdRMhMsUBzFFCaJ+Axyoe/ndVqW8wwsraGWr1zYVAhIEdlC0GvPg==", "signatures": [{"sig": "MEQCIFmSYJV9YdvQrIQp2VgR3iTRnCIFuUKEpKPqfnCSCkvpAiBGy3uAHrOj7WFsqHyju0PeXfxBLRfzmN3T9VbXZJmesg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm2CRA9TVsSAnZWagAAXDYP/1c/76aAcLO4UKkG+Hs0\nLxl7PP/cjOxwfDazuQx0Tmr7u2yQVPofceGtxLj5mqNGVN3+sb6HwKt8hGJ4\n4ElDgGCxj/OWhw1gILEPTltf7HQSubT6j1TEPAliGuFP9cWH4hq7NtVy1L4X\nNHZhUlhBa8cU8sAkBx5N5zODEkeJqAjrvJeYwnBuVYVaDILcI0M7RL576PIi\ncJZx+jeKQkI7i0dFHwZXHXP8aFbok27b3ijZ6/bzNYMIjLaxUf3Kl9rortOZ\n2rewuJQQklfbVOGObi9N0bGNGPPGC3xbNARRNTbUyg/20gcksSk45qgNeOVn\nchVHljYcGljzf+4y4GRx/jn57/AbmoJKNa01c0h8wjE1as/c2wGFBzBHANCW\nbc61w+XLVXwnsLQfoa+Y9HwNyUuLMPnMK8J5KzDrztdFrb+UJGVTWgsavs2m\nPqpfuBrkG3Y4EDvHZ2xmHgYCNdJ/3oj38cx11Qx9BZkgonEOlVMbqoEYgql0\nxPYF4lQlIxpfIVDi0gn+NAvejNkPUmpoqJWpC98vp0JaT5N6IesIuYw8OuzE\nO5CFfjVF+BvemwAVcklkqQjR5ML8PcO1qBDkM6okzjHd7kwRkX0r8SA+GTy5\nEj69iWiVj3SL7zZOaz4BmYHgDX+YFUkPlNIL+Wth15vINTd9G5wqR6AgkNki\n5Sby\r\n=5yVf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7f583066d7218c3b407b93cb257204963b487db3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-ZYxi416xxE7f2UuM9HBLLS8qZ88U9zuJ6cafhaL7LL1SPCcBtNK12M/zxQnZqhvjtMtBgEFihVgFEQYzPzh9aQ==", "signatures": [{"sig": "MEUCIDbwn/HVgU9qy4HR7MU2oFmnLch7H/Th31hbtqZ40QxBAiEA5jJG/RhGqR6RCUtQkLK/tBV25e7bTePjA9CvMvLfqds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpvCRA9TVsSAnZWagAAzskP/3OtqY9UJMLD98R2Ib1s\nPY6QJjzX35q1zoyB4wdg+4Mz36jeE6pqCvIwAjtMJPee3rAC9mNSmW/CdnuD\n8NAEb+uB9rxy5OISVWoVO0jNodCi9USKSkAI0ZwA11F0g/44PnQ6hbu9t5A3\n0ybe1ZyBZq0P+2ML7WWt84bQvJ/d0amNGlgiS1BDzbI0r1P331kLKTqpZ9t0\niEI0tglAz+CH0KiKqU8O4tDfVigBYlpjfCqo9ckeQ+Ml0SqM4+9Y/weXI//b\nWwBlp6rWEVwKKF1GBxe3XEPnDQxy85NXHkMyvk1oGZEdChmBCgJfAJHHhZ3o\nXkmtjgLgg4bfRdbRrsWQXQ0zsOBsoJdOdS04kccswCHN0d1HMOBhYiIuL9r5\nDRnW/st2+ekJ/M/8jAql1Z3K41gz6cvFxTAsVRklgKiRpE81n++cqqgSS1cz\niU2FP79SRflVXUfVqw7SobVYuY8mVGn0CKshm6azjv4sgK18r00eWq+9306s\nw3MsnXiDhuW/X6mV9o/3SK61dujDHqUKUOj3Vjc0kIK4rNMOI/dRQUTjgJpL\nwLzP8OTIBDOKzcalc8enprcJzEJj5IxoJU0TpMLaygVsURY4Kw0G5oj+OvG/\nda8CuSXdAxqQmi5DL1AO+DgX29rb7R/1FSiwSc2WrBG9Alf27jgijg+4bzFK\nM8u4\r\n=xBWA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8bc1bcf465fd42298ff97660193a5da389e40ac3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-glm4pfBEmnr6Lp+0xqWzZTNA2NAAcgfMy7i7P31jMO49kvhf2wIRCfEkeUqTGKqyKPGAtltzoffsD98dS3yItw==", "signatures": [{"sig": "MEYCIQDOlGSBkw+MiBDI+TOaMkPnCGgbDEOZYMChPp69iyqBjwIhANuoV0RPhAOHMwHTG+wUNd5xrpFZl4Nr5s0Y2UFkkkT/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyiCRA9TVsSAnZWagAA53cP/R3gQ93fzliJYE8KEEZZ\nrYKZuZXCvb+DIMr2YlK8TaBdpKiqsSgTZIv5UcmNN4Q0fzooOdbvlaforAry\nznZe154Ww1WY0t6P8Y+vv8sioY5Pt8Fy0GfhNu1olPTKVQgTgnPnP+5xzgXE\nPbp9SBbyqk4h0GiNJ9TX0GdA2Aqnbglau5vcc3+ckH1XnA3/rVulSzJFS+Nn\nLIvx0xa870Sf0IILm9OztIUynzIAz2CwmKUgGgmv2OfVFZGBocnhi7vtXNXs\nf0ULFlrOOlYdGwxJKfeN1n3cNSfAMVemepfhFaStnocmWdD6JgtbhvKNT7q+\nP9YkV0/NEyI7TAM0znW/Dnjg8Zrp7SAf2GLU59xxS0SEaY6J0mQjrDecFxMR\nwJ3cMb2+frCdUjhAQuGJ8SHhBK2UUkyC08alskv70oflNo2J/54bfr94uwFq\nSKud2nW8bDPVsA82raobAdp4NOyaKteJMumryurIWaNVg4ZD3oLCnA+FgF3i\nuHNipbdzC5BZDZF8s39GzIXk7rUUAIq+4kqp9+T/SezZ7yR2YC7YwSUUdQ1p\naeBHQsP7a/D3J3Ip746wAsB9RNMppaAvKGSH207WYxcB+vYGtn1nWAJpE9kf\nH4JmQ7vd91rsCGR6zR7QGHqHQcdOI0DicBGUW1d+smtObz0hvnkqGxG6hL8d\ndv7f\r\n=ybqT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "934b6e123330f5b3a6b116460e6662cbc663493f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-Va041McOFFl+aV+sejvl0BS2aeHx86ND9X/rVFmEFQKTXCp6xgUK0NGUAGcgBlIjnJSbMYPGEk1xKSSlVcN2Aw==", "signatures": [{"sig": "MEQCICRxguDU8WBtZDvje4yGnGIipSmZrv9MfYh3PUY4M9HUAiB/A19mN2jbgccO0COqmdPFnjt4f93ij6wzN9LlAm1qDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmuCRA9TVsSAnZWagAAkpoP/RJgzz6HcUaWmxP181xr\nCZoBpXnpAvXdGqhQ/JAHvD8cYtjXB5UdmssTwzA2bDxGGDbroyxqiAuk9ktC\nH6m4OAd+PuaJHRkgKiBNguD/i2TO7m8YT3b0wVXtEQiUIsom733z3EkHClVj\nfpKtPVRZaubMU7dn78KNJMSIJacNUvKAxNzP+JN0VMDk7qW4ltVxWruDP4Cs\n3q5wULS+PtV4JnIYl61vkQs3eiv02aXIhmgImmHwpbgz2huGCowJ2K+FldCc\nHs1Z1/QqF3aGQODvPTb3+Ys8eCTsmDgZdWFh3zWMchzKO7hzYgoNMS0EzOXu\nXp6ctgihB6Dv61DDTmh0RyMLtZq58k8jceMCf4UlMUQfacrjE2caKqEwlERU\nMK8XEifoI41j6l8ZZy53TYVnR9eipT8QCxDj4pr66fgUo06Mss7iv4b5E3G7\nbGeM6X7T/LE0C99ZJU8dWeLZdT57pPKRh2oMuFabM/4kowJXyVNN9A6jeOhw\nxUqAidyuxWSTzvuEbF+6glaKl0M4T8bMn7fa1eHL3CPE/66zu2w1QvBBEDcD\nmpD/Lf8IFzbHirEsSN6uRV09vgF26WHLm42lgyXA9CZJWVn7lew6QV2vgCI2\nZIXd77Zz73rU1uRF5RzoulH6FrOzhOry/gAVDR3NaZfJT7ND+fRz/Lm/Iv+I\nlmGi\r\n=haez\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0724b4557f98abea290caa27b94320bdc0e3ac69", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-pjAzJFCHn5xFpu1NJm9UgM0YDZBUqUSrwLrE457h839MTRO4XeSLVWiisyeXopSfqe58xnUTMtQKZL26bbpRxw==", "signatures": [{"sig": "MEQCIED44M4jW9fch8/uJfb3cBayjYDAL7vwaZLNYgiROTwEAiAUvU3Ap8Dzr9sEoS5MJCD/P1uvYWqJWyBedqQJdWlP/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWASLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbSw//RPlDnC1l0gYJ6HCjesysX/4G1b8rOo+KKYwh09xSOZ9MTBE5\r\n3xbIBe7KF3Zz1ud1qexN+/jVmpbaU6elxz8+NlQhVtleFlKgLjBw9B7W5fDy\r\nyw0jgMMPFpHrgrGpSNoPzjNznZN95bVhpRIZh33HmL2evRNqbg1K2D7YvQ4G\r\nStqkdiqopPR7kSDWAJvd3aW2pX9it65CTlUwljbLPVHAivqpT91V81Or59pP\r\nhAofuXv3IR0YsXxMOB/9/a3yqNpfMFeqhuK98BfDCB4s28aiMt3iYMz08bPr\r\nP82xV8iRbjxrwR5vRh0nQQKVyFF6MVwwidKYiLhya/ovck9FqWpnm5e0FDsV\r\n+6tXP+qL5Y79mvCdB8N4BPSl229Rhv2+armzbJGcC6hN0NvSpm9mHBwtf6TU\r\nvyaR1bSrAsjNvfg+TrDt5PWpC4eObY0FgcNH4fkmnkTksvxHdxRJ5iajB4g5\r\nk0tN8KxtZNwOreVVOaBSvcYq490tIy2WP+mqpoYnW+Jsd/uG/vw3G8BglNNU\r\nr21/RLjyjfNA7Vw1d4f1/qi43c/FUmwMrbImJefxHCfdAGtHLsN1kLaO9dUp\r\nRduuYRfqsePP2jX2DAq/Rx3mgaXO3TZII+UnexjgOWqS9zbPLFsEavgUz1ol\r\nGeZvl8dsquNRLocmOgUjsIxm4P3Vh2x0pu8=\r\n=Cwjh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a1caca14f64625f907db034236d2a9771d9dc628", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-TkCNHKkU5lUc2Fg8D/nruMeM+O7mlICWX4zR7EqEcR3U+i/IxqEnaaOEh8NJsu1aTvKrEqBMs1Ws7FB0fbPgXw==", "signatures": [{"sig": "MEUCIGyoU8V09hQI8RGQjo2D43WUKhuAisUi+E5DGipwCCeiAiEA+WYLivdTw5YlHWB2E6UNUVOUF69GcgVKemRUBqYQ9/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbXw//eC2gA9KdtE+/ae5aWVH0sBORraRG3DNEMkyaVujqH2kgmrjV\r\nCHv7FrymDjQWhLU4iZwjBPyNxxXR5G6YGWwHotg1EYld8gMXDDgSs5RawmRe\r\n+3/l8cvMR0XfHYCY8pLSVnreefWo4DypnWxBVteFdLWa9czzL+OD68y3FLkJ\r\na1pFi1mQGq2dqHeiJVmNddSQjPDJuF449Oakp6YQItIY6tNxawdsOyePhqWP\r\n8/4/YAUT35iZMmkuYKBxZk8lpuAog769p2/LS4tcCkl0KhCksEFoPbv13nHV\r\nkPlv6Q0WPaQfnHo477bLVBOyFbvaYYYwhP8IlPKeWVTkFwIZ+bMalviNijXr\r\nRnk8tIZMxdh8B/jMEuZxV6OKOVM8kYGr4cBl180fAwU4kr3Rmw8q0MSKRFqh\r\n0A1KCX5Olazf0SrwXc8j8MVAXTw9O5UjUsglAEQohOZruBOYPnp3QyQjZ/Zy\r\n5Que8etuuuw8GJpdMT24a2yO0RJwWcmsqiDLbcCYr8yOdrlf1zcC3igPkZuD\r\noMOdzK/UVV1UUE452sJg37jvzAUfgCPAc4H4Ox/NK+DIE4aLo+M8wmXakTw3\r\nwu1b/xrHcx6TUKkXw61lSLdgB4Ah2qYIH/eg48kuhSE6+FC5bSSngYkPdmMB\r\nhQJ+RaDPaVPyvPBhH8bcAV0nR0X0lIt1HnY=\r\n=Q2ft\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "45797a93a0a346476a50815c8979aee590dfbd13", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-o0GShHN6AWbfKBkul3cnEUOuyXYkHWZVURUgVkNwj0hJqdYXT4tzBNIntyQMd4NVXY3/XHytiES554vtIbZUlA==", "signatures": [{"sig": "MEQCIA8w2yW/5ihodkCMzZSL3hylyu6niNSlPvUodDDPZYJNAiATOSBKjqAMvD7tRziJH+vLIDrKghLWnesgnF/T6aO4YQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWhRAAkAyUZVmpMQBoQdcjjWl9EKj3HbgNHywvG2KWzqMX9FpYIq3a\r\nQ4KHA5vb7T4JrzjrUQeN6/hmVJQzKWsBflLoWDzq6KkvGBEzfnAht9harDDW\r\nK1MPc6OrDxUbZeGnRVt6U/K2N5cRWiYMZy2pMhefQ2m2+bOvHxsovUk7RxCm\r\nDONxQOMkWVkTv9fvsPV37BDuKgODlDGZJmJ3vXGARBnn6O2PdnVOLzcewCfj\r\nKoVhOYGgCcCUEU165Dl+9AIsqY+2j8xZTyfMXQWXc+Siwjmg/BIil2mYZKOG\r\nsnegLyZLF3TfMKaBtRad5ec4+2mdu7i1SK684uIEjX7Vve4bNYJYWpOCY/z9\r\nVUnPWEr8aNta3T9DiUEmfZ4k6Fs36lLddh7LhVisKgdc2jwG7B1rX943zmcF\r\nrd3vw7Nn/X8ZU2UIeoLceghtkmFY3GSR++zdURLmukAwLw/Gcdw98L8/zU5e\r\naxkXtVkeb2qb16jcUR46Os4w8mLxAc8K4hEA3UA1fFlt9/7TLs4cA2vlA0LU\r\nQjemG8E7cvb4mCa1zaKfpKeKFGbgXa9o1oLTWEMuwkT0tk6EyLC36kAmP5wN\r\nQG0mT732dNUHwuqpMXe0ZkAcukNVl4GEfQfoN+vulAw6O3L5Kn/hO1IK9qy+\r\nFZI2XjGMkRf8H+il1CPU4j0yMpaWfTyhFfM=\r\n=+h+A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "effdb0b4bd68ba1fec76f28f1810bf7d83e445f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-izWpgjISO7pPjs1aR0YuDaKAPjC1558rk/K1ztJpiu3Y2fAC3h1tAKbX2FbQZ6k0LV54Tn3IDykaWO6VipkMXQ==", "signatures": [{"sig": "MEUCIQDXmQrr4qsNCmbFLIPJ7CacxHvUqq6+MK0S+8vu2pUdlwIgYJR18vdEXYynvXYBpEbWSvjTAeS/4IbR0PUAz8xBP/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoDQ/+Lur5cyCOK5ZQ4S0S2CcSuuDTqNLtjhsZD5eGC/Lmrv23yzmO\r\n7i9kelAFZGwhEw1uehD6FxrwfJgSUOjbpbR6xbGJkPIF/o2rt3o4B8NsTnNe\r\nx6MeBP57cA5W9D8wcLkd1cKbJoRNajFgtXOpaH1JjCEeMR49NA7cY95utxFB\r\n8rq66whJJFjJEc9BsVx7Q3k1/75I5//jaCt94787bnuuxl+jRSMG3jr0mj5f\r\nNjRd2lt/W2U6wC/69wdfoVSdgOh8gXcltS0dPTdZlEifFPA9X6gSLIJcVyph\r\n8JMZt0vznVJr9WVT+OOM3yEfEoJIoYNZlLW1f7Z9QDqXBTIYZgrXWY67QH1L\r\nLAcrcCywR8Mndyfwcf6VT0kMRTITU7Q1dXQdN2UkSKps4laE+MYSVZ9RARrY\r\nnBHPdaRbsKoCwqHWCLWdLQmGitFRgVEfIk7SgHjHKcalPqKcmUIloPoZ9dGD\r\nsZcVKKQuh8PzOlbrKAAxYHFnqbeMDOdWhrRUQeOFX+7kh+jI0kEP1vgRN8OS\r\ngba2HoIitRAQH8+kbpyIda9wuor2UQd7e04E9idIJQK+y63oBdWCPDPAWtg1\r\nwfO5tV14qJhjjCUx8L/578Du23aWelDjqVQr1XuJB8WdlovsakynlUOMuJdY\r\nED30Jc7RjO0xugUaRZuDZnspHBO8YtEBPbw=\r\n=pcia\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b76d2b69006f89f63e6d293a94bb4e8fbe823ae0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-4TXL7DlddKsgneEBtol2g7hYIJEw7fqvsRrhgWPa7hzo0jQZwf19bkrqF/EYHdFnbaxX44+S9pwv4BjbTjJ0QQ==", "signatures": [{"sig": "MEUCIQC0qNpBAGhm8vIIphuoUTAV0lQkJtT7oFr3m59Uy7xORAIgUwo9m7PdKz6KCADT6DPj3VApzfXuXlprcoYmvAXcHZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgQg//X9y6ito66Ca8MpcRaubOCMkG0PCiTTT+L/SpUBTL2fxWfoAk\r\n4Z/HEi+WaChj4oyq5QQA6RhoHYMAY1t+9uF7uxq9FNNvEsdW6D0+e7SINHht\r\noDLERwj6fFfYpUnb7XYTQHGFlybdAf+5Dn43LfOhUDh43CnXuZYJysmk8cPp\r\ne2HngRcMRYbp8+bQZyDIZI0X70YYkOp9f+TOQXTYKOL0BRazQYzDHzYEEahn\r\n+jPQ5hPtisTpVjRY+ZafcdzYbPIhg4+El7SBI2el/oEPKtJsPQd1OGa5pkZq\r\n9kAmuSIYCYiBBiLPtz8Of+d+PEo2PkYZQLLr/fXpTdSX+fARnzOKhlzn2WHi\r\nyqH3Zbbg1Kh3N+oCkan0xDpGrKKpKWp7YQxsehlwpU5pyQ+sJ/YZiKpsK97C\r\nLUt0KfmmfS3eh8pN3utZrnEXbSFMCqIPTepJJApSLVZ1ZL8nk15T/0jb2mMz\r\nwL/rJBwf4hQTyPsa1qQSN85Z9F/LjSB2OelpXtu79UrZCeoMho+mrmYgC63l\r\n6Zf8910oKg0rUS1lqHEnXNUMtm3D7BEc/zR6faw715NJG52q4DacIWN0FRVd\r\npAujknn/IesUH0NNeEEqP84meperhEJzW++Y5Bt2FzwBs6Mq7XumxLDpJbJ1\r\nEZ/vAb9b5Y+hVsQNA6ARBTY25oVreb9QyNw=\r\n=94sz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5b280ec1ba9540fd2e520570203b7f4f4c142ffa", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-KHYl3zmULTpwvF18IuZbumx3Mm12knWVJ5tGRFqNztR7EZPWtGa3czBoGm/IuejEQYNIh7mUDmJd4S7GelFZ6Q==", "signatures": [{"sig": "MEUCICl6buQfPibWlcuKYBX5/qcwUstiNlWnwjFvR89KiL+kAiEAkbhvtVKLJ7fBS727rP+GJmVobMkZzFb74qFSruVCSik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOxA/6A/eppLq0SQC+JhfkeF3DxALhtIdhuwPjTCKuj4tfhaeGuDGT\r\nqicofQnz+Ti+kFrn6anvmuQdHAjSU2BLzMKMUU6ANbWRyIsbQ0pQx+HpShfQ\r\nTAbiN20D67nNRuJGRRXlqJ8Ykg782Y36D+vLtX3nEw0JqjnaY2d3pn33F6hV\r\nEn9pdfudr4Mt868w6F736ViqH4ynlyGfW++BCzCgvqL580d1pKF0VCE2YNqA\r\nJbP8oO7SQoZtFKrA4/S0EeMioQNQObFQzU5JIjFtf0d21PeiIsYMxb0zWamN\r\n3hgVKAoiSedFf5/wkOacJrb3xIw2VRV82nqLMJ/QAsh7enESfHTdsjz/XUrm\r\nOzm6p4bIRoFSVzwfxu4/gYmwoa+rLKVrPDWvndVtFnxjWhkGxJBgDKSvgpAY\r\nqAnou+UgB52j/h4tZ7qaUWSaHyXg7o1l/eoW1YjRmLnImzfN/4dsSF/OYrVD\r\nKIdOaqAyPhOT9D69wLLuE85dzysq9utN+HYOapw3TD72XyGTraxNj1h5jEbQ\r\nl7Q6EB8W+jk8eIePqUuRrqD1PnvyXBExz8ZfV1zrvKDejs3bVZ2JELglefS/\r\nRp/qj0Pz3Gkk7fbhm2dPrhVt5lLDcAtAxBDM9x/+yiJjAGXagphyZI4Xi2Pp\r\n8wlQ6cS1SzOhKMLyi9tKSFf0/TgaKDILVVk=\r\n=LTgj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5ec9402224f7eb46379ab04b31915f8b44befdf", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-cDuHxty1SkiRb/O2A7ZvU1/z23wamB3o7zEYWmgr/udc7+cjoRaLbdjAAy0qw1kPQJExXAwgcAcItkfuc1UcRQ==", "signatures": [{"sig": "MEUCIGqV3j2mn46DvsSWpQ0NRsD3UVQZp5FNc2B1rUJHNVx0AiEA1ckgYhbg8YuVo54GmoJrA2jYHHLCUJlWCdcGuH2VAU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNzxAAj95J2VgFIlhBMEAkgMXsTO3fVTytf/xTzXkklK8bZLhLr0uk\r\n057hIYCkR7EeFuNbRwSTP18uhyslleuyLRGc4U91nSJVqw2yYzTIHAv8oQQF\r\nM9yLnG3ieHvydo1XSFCG9LwLKk9j/wn18p7ph9HN7Sy1v2LQoQY0FzsH2VWa\r\n8THLQDq+6je3Fww5TEZz7lvhq3CPlmwtB2eF5lLBisFq7CPvUklZoX3bT/ZW\r\nka8dz3VQcSNxfm+luy57d/YunAr2QmaIN2Ug0gNJu7kqM7iX5I0n+ETJLJC0\r\nNdQ4fzJY79i/6567u8elT/PZOPmsj8Q/pK+pZv1hh+mYywOSBhXFonADfSA5\r\nXTq9zo9jIoVCCsDhEwYXTpyG0Lq/ZbUL3viGjqhsPyun8nx0U4mg4wYNgrSk\r\n50bY273Hcdi0ZOsaaOxBPoZasQITlCpTYsH5Ts7s4nFfwhpx4AYgQTi2YwDM\r\n0oRf2OETZvzWewFvL0AgXSum/SnVLJYFyaAfZgcgeEyA/4BXqX8aWvuWVJRg\r\nCvmcQDYNLPn3JnU8iSkup1b3/lWljbAFYRar/S2NAdXrEPn59psvONthIYtv\r\ntKrvGUEGzlb84PVqwX6eN5yHaQZFVLFZpKqQnyAQaBrK6Xli2HqXTdkQZUze\r\nUSVDagwQzbtzhJYt6B/DnH94d5HVjcY2vCA=\r\n=17fB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.8": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "17c96ab3c69cb5e0a49bbcf037139760a0e8e0ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-d+8VpVKHBUeJrjEn23bSwAeLmVErHyhGU58QfNmjaJe4VVXw/ZGx4bscZEYe4xSdgjny5C1VIBxhVQyOv3lTCg==", "signatures": [{"sig": "MEUCIFhTEoz06WkNrzb1jYnsUQo+shrhiQqA4gJKNddSApg/AiEAh34xrmcJUe6tZkrvQb0f5r0bmP0xMFS3nCBAUs0BwB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozPBAAhX0ZcIzBHnVn+3WJKZwk6fMCr+7baxxe6Tghetg6t8Bt9rSq\r\nsZU0ODiYOv53ldICYkDTBdFgdb1W5b81djVBumxsqjfvmRf+FI/9CMknvZPG\r\naoZak5udrFdgHU1FYbbvMds9LzgU9YYhyEqjAOb1fB4wkdfd9ejRtVZawzsm\r\nNXDbrIhiHfqEkYia39hwLcklBt75xTB+9i6vkOrIA3nWB0n65xBa9XkmIbBe\r\nUIRoRPyvcp+aH4TkS4n+WFjmzoSKrew3OX9HGRMJfvaXr/RZPzti9pRGxHg6\r\nld8FbZANZTh/VR9BrQNvXkKdPYPuBDL/JpwZMvEzB/FE09u2/EMgMFVSbHPL\r\nuS+qEKLec2GfwJPqTQ2ES9j68W+ojyXLzSI+1H/+JuBICscKwKa0zCjKRp15\r\nxoUBeYyl0ZZn0hwgsp7Yigxbm4WvmcBlX8CPi4g07FCFfV05FCIWNBLdfw4C\r\nXY1VlrCP/Te/OlNqFIkGDDxdv4MxxZik9BYhX+r11KK5/ZXNFx/mO1LrwE6j\r\nSD7W4/MQK/T57VimM6ySylmMAXz2R21scxO9L0yuuyMD2tvDyZxeN1lC2aGF\r\neoe5iF4PDj9p86ijJZpJ1/6EE0IgyUV/meBCSAzFXAgb25/nUfQ8Z/xvy922\r\nug29RWEoJs+jsHivAuQ2aUwDq+UtV+F4C6w=\r\n=RjvD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.9": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "618d0cb4d77aa009ee42b94bbfdabf809b9f45dc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-oPgNdxU1/Wuej1Yi3a7YXx6R32EwALNmAXKPC2Fy86bP5UtY3lbGdt0qbOTksljMD5hMMjX6d13bAFd3eMk5Tg==", "signatures": [{"sig": "MEUCIQCyjqslX0MudwcPEDNHTYZDdlXV853FGC1mfq6GXucoSwIgIWAclrlwO+tl8Y5K+jZ767DhOh/o75oZbyaC8EhWsOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7249, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNihACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPoBAAm6VGBT3qsnrHDquLh6Axc/bZbDmTL2OhI9FKoyY4wljAu28g\r\n0c7RNWgOXV5SS+nXzmRWp26Y01QBXrd4fLWPBkXblmfBjIdu2/H0Cv8owBR3\r\nCzziIIc3qgJLCMWSosUfXK/jkGKIayRPcLkW36Wx/zqRFBVowXvUuCJ1ZI8q\r\nC9OXv49+mWecyJvsiVw7n9P9HnZH3PW2wbHJ79CNEH6bUCOLVErnxKKrXEm2\r\nNM+fh3U7/hrW0VOj1Uo/21ZLT//SF/qd5LdJlHM+4zXNCzvnwAVq4LHZS4ZS\r\ndfWOWkW8nbap0M6ws43gxTq0uv2YZ6uMBVWXEi+X8M4P0wTIQh78Qp/zubqE\r\nFXCB7d/DaV69FcPgWnvjWYm4mcvrfKR/T7wmEKi+sO9caO/wbBzR2akao675\r\nz5VeV01NYrXmFcdYB7wUXcQLX211+FsNxg0pCxjve9zxg/8YL/aA+Q4dW+/g\r\nifthKjtFKYwbOUV9X3cQwAiaZUAW+hZMjv1+lUjfWJFHPpwfXlEyc5iJVZSB\r\nZpEbabcYpenzxNaIRVoE61YZvZEDSJgzTb7+56IM8CTs9YzsDQxqwtIGdgSF\r\n6vO3bll7J+UTS0S/Yoyjc/T0ooKqIbB7JyJoX+mvbW7Na33T71DEaMAWoA7W\r\nnlbtVk8XutqjOuIqUmCcPBBh3nkDVjBeGSc=\r\n=vWjV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.10": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6110229268ac4c97b129c0416966a769f5f7c379", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-s0MN+tc1ZAT7qAG6SX2LhdRoBS6JDtOSTb8WhDshPLz7LH9KpYeCyPbNtAAdxuozF3x9kUyCq0OsubEeLuO2WA==", "signatures": [{"sig": "MEUCIQD3nVzooJEOjrVG9fZZsa0bFgUYyPMgPRYOVBwLzN96dwIgZ6lgNUzj+TYyCzZMv/FIJ2TWE+fyem6+AR0uvz3jCOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJPA//Rrugkg3D/g0796q8YRDbip6KOVvheKualoTxZwzqBAGTOe/q\r\nF4KPzrk3fWBZmLx3K64Tb1RbjCBZdwhGjKJ1uhNL+EhjQD1VYGNS7hnneWh7\r\nydiIJG2yHN5sj9ofeJpYDwr1pJsE02EPHa4zUXDSdUDcgK37TxQ6TUJL57iu\r\nsQiXvaIFm3/tm/83n64WhhjxUoIRUreTZxf/W05gfl1WZNXFsc2cC4BmoRhs\r\nNp6W7XoYQVWtnV2S1LORw3WfXA9/KhKEaoxFY3fAfCOy/tpclrJS3YlRlNOd\r\nb0PNq1Bcak+K86cBUDTL/lp+Vz2xSwXve1pymkJlm0J6vP9i64P4F2Fw0IVy\r\nnJ0+GbIiNmdw+aQu5uR/usC1Uu5GMOZmntsxRqg09VKFg7eIvXQHkLkzKWdW\r\nKuzCxtQGy10H7X+9uSsVJWFQG+GLNHwMYHs5DPHtc3GigIlNFce6mjc/jh5U\r\nE3RwZpje6Ws3L8px9uLc+ozYz1WThWkEqNtRH7GMUIj44oGBUERsMqE2tpls\r\nWzzVCGUUvk44K/5BctYze8xPHMMNFBfOZGJDNuT5hEOWVzyaiGbwayYXnjoj\r\noZ7b8MUMmPA43uW/AngVM89lWBirZByJiiGmoDJzSFH7/507HBpyudrVfPef\r\nxM3jnFEkhD3vJKPl05MY69lhg9t99hJugw8=\r\n=H4xL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.11": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2459c6ef88ba589ab8d70cbb4a4735063a76dab2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-KrNuedagia9N6xO2EOh0XAOdYLeKJpnkUX5/ZXWMePrSRHqu4YxOkemFMk8JnSXua1Z+n1qg0OYz/vkXho7pOw==", "signatures": [{"sig": "MEYCIQD/h91B84JnYel18mSbM8RaeaPxHEg74QOaXEOjoS1mpgIhANhKoNAgApkozULOsVN08Uc0GqBLHh+X2gIII9BTFpnZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSl8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvCg/+NBafxz/vjqwRbk5+lcxHgES/xoR0Zdbw2YBr7+3oUCKUNO19\r\nu0yG1VWUqQ3anROP0hJpI19RqK9bG1skB/SDVTvqabm/W+eewWyETcr/wcn/\r\nznzGyVMeRqoshOaHOEs7TVYd/735nkI7AjyUcDfxcP4ZndPsNI/PFmlCOYwo\r\nFsMpg7GUFdXEdxOMIR0LM+fBQeiOSMCdcvIob3WoZO+UwHi8qtvQ8jr3b0tX\r\nVk+5yBPtX+Mw0KKeAO6jjjodt1RlOTV1BydDVNeIRV+R1YnScqTOE+t7dI7f\r\nTwz6tFf72PMJ0z1X7hKhVLPw0jq1rq7QT8LVfS9jG2S3datSXuEkqAQV3Y5Z\r\n3sH3d3LrUSvpMPq5r6trO/Dx14rGLAle0zj3qFNpvgpbJQ2NxSzzuiwecdMX\r\nYxGqppLrU0OMtdj7uws2XgQuCOAemwNLxk20EvUGnetPxXBrkqogX6vG1AP5\r\nRAe2mViJvXraVJe4buXTMymewcyxQDBN2TlaX4NMQ2aog0qYX/DJT0JQ7J4m\r\njbiufJdy1Fr+2dWvHGIAu/flE5Tdxn2wKOikwHuo/Ium9THpIQHXmcyW0BQQ\r\n3NerHpxMR3+Sf9oeZGSpxfG62uECetEW+R6kW++X7n92ttqs8w/94KMLjHuQ\r\nXdxB2pN7tOnJS6f+aWTi0Xm/W+3yStD/FZA=\r\n=tVnj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.12": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6cbb43e43641231b5ef295c4b386f3cf085405be", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-TKasVWNJeWeJw2xEn6i0SNSpwqr0tyew8962kvqcChjIQ2dnhkGtMd0awm/2xZ/bz7xbJ6Wztn3XK8cWyojL9Q==", "signatures": [{"sig": "MEYCIQCElEbVz6kvCvIMTgw5Y5IoK4QfwH/PaxAUzeAFS0r8tgIhAN6jyGPlrHIai+l5XmyfYA3Ah4CXjUm7bzaTQYCjr5CP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieoglACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAEQ//UoGUdDszohJdn342li2QhrvA7TlwS/1RUvcrSSKs/yLKFR4+\r\npqGmtvzGBr4KOLyyGK+E3ahhbeAzmtLMrsKBR6J06/RKz+ZXwkwUgXeB5ZXo\r\nvZodxb03TfyPHpBZrqEvJMDD3pHL4GozJiffQ4LFGFqbl5JmvTtPodp4+OzD\r\ns7XY1xIAMfVNrNHv3BRKdSJV2O3evKOi1TJYxVbgKCCopHpG1R1dDmUIJszN\r\n/T8FMeWgp0jk99c3nTtP4i7vALd0cJXKMOSdydRsKXwzoqfRVwUqE0B5oAdF\r\ntb3el04o8wUAB8BujVmpbRXjSFinrQm1UM30fOO3+STaIGE892DEJQYnmt4W\r\n9cCQhG6zjMH0ValL++0kMeALHzIRD7swBqFh76jgUE5KzWZuIRq7GKQi+xOG\r\nXauYCkSfFb7kQap32OTOJENqbMEhzTBLT8z+PxIoY9CbXylvN7wNq8+q/FVs\r\nqKkhi4EZsNiS0FtHwBWwQBOAvW9Gkc7apPXMLh6i9KIGt0hRJexQcWhOo/2e\r\n6UZndu1UeX6BMEv+wRGMCwzAT6P+jrXGv+Tcun17BpcF9XDajNEDEG+PfI15\r\nWgyhlqFe0R6hIuZgFDmbQXCSRoT+rSHq2ZgxF5LHuGlxlLW7f8S97ZJK43Ci\r\nfmkab8XzuQvkm3x/b9uWM4gGggZT843PkUE=\r\n=shoG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.13": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b991a69bdffc4faa2dae8421262b0742e728f25b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-RrgYWYu67ADAPqK43RIhDOs8B9/XhJtmFQkRV58Z623LOKYdNlBSSHAykW/E2lStrZvvXlYCI4ZCi9t+QYQPaA==", "signatures": [{"sig": "MEUCIF0dFRqf2fbsDw+UlxbBTVA8EoYD+rXC3R/m6ea1NbOhAiEA835uA/STwSaQIbzu2+l/24baH8a0Xi9JdcQGFNG/XKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXfxAAj9w1+hi3nXS/iIdbFcdAWsz3ChXqJVXJdsFXtmzCRIIKGDbx\r\nSby1d91lCVOD7NqSiVwZKax1TtL7Lnkr6o13+ilfs4vDaf28xWgsR45lUymT\r\nSkkx2o8VpGKcyVRPD+mLItM4sEchGorJQA1We86UOkSrtJt1RFuspO04B2oB\r\nuuoNKQis6um3/8RWjxhzSa7p7j74/Y9VIur5uzMaeQalxpgqOaZG1JnYZ24h\r\nl39Dd22lXN3Dr/KR+qEuhp6eJqpGOkbV1/LTCYrRz8OIG7Sal4919M0HR7Q2\r\nSlNLEeHJGNIIc7HExwdTCTzFG6iH5+1X4birDx9/cDUqUqhBAPAE0nEVs1eZ\r\nguu2u5xNF5g0DNWysCa3SQ1Xq2yfl/hU7an7DJFNwdqPAiRzEjvPdfdTMJfs\r\n6BanDn5WTBRUNObZuQm97pcNEAA7r0spuTL5H45egYBBLZFf4CSsHFZ8Pg/8\r\nucD8qHjsEyNXNKUZ7U/2V7aG017myZrfI41yVVefrcB4DqG6o4fytEgWPevG\r\nF1d6RwEeZwH4+kNU4ASAwI4MC47A2dOqCF4bnywO5ZN8oN7ouNm3KNC6PW4m\r\neaKFvXyDhdkEjlfWHG8Z4V0MOP8JKa1+DwTGDmEe1+SRnR2m/vhusDs20dA+\r\nZ0e1XlHK9UeLkrWrO8QWOdd5IyqVEwKcnLs=\r\n=macC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.14": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "979cb4511cfb88728366056e16555938307ad8f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-cv4wZDjsWoGkSE/xmsxP0qi1BAvhAzS1kFGJd+oPVoiinsMnmuYYbN360699HmB/cSas12c0EVc/7yJcNQyQ0Q==", "signatures": [{"sig": "MEUCIQDPf7ZzT0T6oNmfV95q4W8crJ6LLgyumKvs6wAlEqai8gIge1F+p98d6Pl8eyRsozDtDxVPZEHaSU905yI322Rtu1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqp1w//VphLcoiyZPcq5Akvyb0LoM5FWtT/uDeupMzXsMYC7aaDTQRW\r\n+eq73T0B5f4b1grp5C6QNnFab1D0W6X+vt2wmqGP6Y93gd0/DZUbi4eYJ7y3\r\nr24vLGS+LNx36oBSDR2MuxxnWJ0sz5t2tBPnBplDqLbYs9O9g66CHjvj9tol\r\nHdoctO7SfIsu/7f4Zup1FyBfKAR3+VsgRrF023hUdFCBULs3devdxO1JUjzr\r\nsVY7UDsWp4ZhIinvTve80slAvssRCgCbjZTuKvSoWVm4OeGZJDG0YeU7Q6Gr\r\n64BnnvQVRAUCucTYipchNJpV0L1wkADUyN+SnN0VTpZ0r5jlE1knZaLG0KM9\r\n4e1W8yW0P+nJ4QuIOGFs/fS6Drinl8T2m2WyKpj4s6VxARuVP7Plmmya6JhP\r\ngULvNTlp7GvduDuNEI2QgiFvrMxXyT6Zs2EEK6LW4C4WaFCu0N8Sh4kuPd47\r\nCH8xQAYaGtwKvqBx/7R95C2SKz9GpCSBiIUaeCxDCPXgxuDyj5xdet5+6pO4\r\nDxDwD2XJQFqushvzO1dxXf9X8CREscEjaWQWn/wNDP2K5xCWYbaLE/Cvfemp\r\n6dlWIJe1Y96dff2V2Ga3/SEKOhkO1hb3Jt2QQWZI5cPlF0jHceY/T7gOmoz7\r\n91ZWIg7JB5UhVr6asrDRxspKITb5xOvMe/g=\r\n=4Z83\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.15": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c4511297aea845b22fc308d82a890eee8c8a3bda", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-CRUGpr875hN8ItTXbHTTATE+K7xd4h4HWYsFPxFRLhSg3qQzOQxUA6dj9PKJm5UTeuu5lURwFPdggrGkLsqhyw==", "signatures": [{"sig": "MEUCIDHI0jf0kitm9LWwfJyGxnfiFtVjWmj/0WaJjc30RkZ3AiEAgbIkq914p5p//AKgrU3lUwgGjnimtRuMwcmOcvOiNOM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA1DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRTg//S2WeNM0fx5cInxs+IK9sfD+G8zxmjR8oRvcBY6GK1q0dLcJ5\r\n1pgMHfDqovSr/QHK414sz+gPvPReKa/X97vH4N6RBEv7h4PAA+DTDaslxlhN\r\nhZ/auHDTxj8u8qwWWBgk8N16I/7R4yHpSfDatDJ3IP/0BwrnxeXPFD/zvXYq\r\nxH2VfiJRfwA3KCQq4tuTOUGTQC93pqIjGH3wA29vFD2JdV0RsbFxlEz6H5qv\r\nKe7Fmz/tH8LpmJOUdFlgCVPV2vpLNxY6z5uZUzFeeyN1rIOhtGe0275ygecC\r\n0FOhx0MJARyuEyFiwWuJh0pvcA31Jn1uQUP9AoKXkGdjxsjOIZfNA2eJOMV7\r\nyOYHrTXkP8QYiTCvrelKczd69aHQs/1DLgzvbSIa5yFUQd0jiKoBPD4BlpQG\r\nFF5bOlhJofYX6Tz8KvD3KbMSi1NuOjyJIjgE+XBBhXeVSqpL37jnOPJ7YLCj\r\nHU4sutxqNYKwBupjGtgVoN2xsgPPa4CYIgbLs9HlSyGshRIkD6AxWTmTazfZ\r\na0OtrfN1MpQ7stvlPTm0Zxnq/zml/wcYQoXy3DOtibD0WpqTCYn2A5ZPSNln\r\niWXt6UArdj49xwWRkynJAmEbr4S/RcNKP3Db9ut2G0cIMAZ8sHlXvoRA1+zf\r\nESAYbHlrdpq7jZCsYwOHJ7WpLWkzmdsRpOw=\r\n=uvTU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.16": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c39519be87fc1204b2ec7a91d66ed4bfd642426", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-WYx5XYk7QRlnfpVt0ijah2v6eeBrZ9P9P7Bdw3nDBUf9SJXYNOCPGdb3520ySdFGrdqDI7SsjjLr9IXh9BCybA==", "signatures": [{"sig": "MEYCIQClx3P//koYY1nRGFckA39Vzs5qHUlwbohnj8c0cQGxKgIhALOujyvaM+aqSYKBlk63Z/eTl+GupptwfNe/cKQtss/p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO7A//WWSb8IWOutzwUaTXST+OtCOv6FkPa3n2gW1Y2+2yPKptAJzS\r\nUiKePMj88rutJmaOJ5BC2sGZWOLja9Fn7B9Iq5hdEcAbsprEhZ5ESj3tt8X1\r\nOVwi3QIDUa1d/5EZMuzlkn6srDHGLNFMSWNxI4FF/weNXwlVtmua/QKgHT2y\r\nMD144jCy5C9aaKhr5+AavnhhfZRh9hhz72A6WBpbMU2Zs11nz4Xw/tchAUdP\r\nvs7WBIywj2fQoHNhmkCvBa/ZgI97HEjHLi8nRbKXa3znnkWzBNyGV+W90ySj\r\ngp1eJC883coyxVv3yBVpOcnlM2hAy1PSpc77al2ViRiwBZA61Ri9FZYIFv11\r\n+OPwKCQHU7LDwqGjZ859VZXedcVhF0mTKEElBLlRaxinjgUwAbRw4liUSpQh\r\ncixho0p8LpMjzLb0v/y1clRCnm/w1nfTg+skvXYfuWcy7MOaAYeoaadrTGaF\r\n6wegaoO3ZcxA1NzSVDWH8+xCExtEeTLI8kG3e9v3xEqW/RmENU+7tqaquu1s\r\nJJXGE29p/9/5vbb9wa122srG+EGwRI/sh5mwuq9OSgywtOza70nRRNUbaf0t\r\nF+1jRyN+GJJlLiiG7TrSG+j6ZKV/+xBnkaqUy1RXsshHdgllSQ6oqGgNQJXc\r\nEUbG9y+j1qD7X+FC/GycNB2Vd6qKE1+BZTM=\r\n=rooD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.17": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92a54891212ad00ecd7191aa534a2c83198d1e0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-T9P8Uh9sq9aI5Y7NwESjPsT2NNt9Sb5v1eew5q8X56od7nlvIzIqCON9wvuRiJQD+wnspneLE5quZzeZDvXeLw==", "signatures": [{"sig": "MEUCIQDngDRK/tOB7yQFfqm5hunAfVPAn/oX+ywttO98kwMh3QIgcdzOmdC3LKXTr5vt83Li/k7yAXKqq7nuspSYOhrEtVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3LA/+KxmxlaHRtvIjx6iEkiea6sf+XSZPWddEZkH3y9moecKDlOJ3\r\nov4bETvvHKwrHIUflP/2HUJVYra+M77O162+YtTdwrh0fzIUny9OxSRN8lA5\r\nHsyaHKqP5mu/nD17W2sRirXXklFKEpWi408ZbHu6Iz0PP1/SWtK6IHIibtKv\r\n5Ivb+n/U07kYbP1ew8Qz2fAA50ZA6CqCt88gHccdo6UQykJDlDRXw2hLrVrK\r\n/4OcYjdeiibB9/WnjPUvji3GANoDM8K+BQH8m9kXBpkT8QyHSA+XuilhWz8/\r\npT/Y4dFPmcI/+A5YSaisNJq969OpRyUurw/ZDJ2FSU3wZgld2tEAk1weHx2Q\r\nTBy1RcHTI6vQa14xkx8aZZnSMWQQNpG9/Yhs5/9usVVDqfHYK98UqaWLw35I\r\nun7aPrY7UV+OI0D3FfQqiyrEXjA/buAdZmwuAw0RcJLPWotdl4U6w1uyrXe5\r\nEAfhTWNTNYWtjCM71aXYX15aWhCx24YAizBbzM3IB9nnE2AORxiyYb1mH9rg\r\nUeOpgjeFKljoNzwoq9QkYMa2Ie8wVIJn4MkXILK3ss5d4TnioqDd60OzO9O3\r\ntfW4uhYafHZITy1jKTOvfRqi8MmgfuqIpNFWju4DSwWz9L/ydI1vlQGWeRUp\r\nYY+KbvjZ1ov20qsoMVbrItPBRyeAgNxTyYo=\r\n=6eQ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.18": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "86cd931b69cbf333ff3ca888ba74edf119184ede", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-Jw0jcOL66xhfOTIAaPvcDfynN5KNE+Y2lokXnDS9n/erGeOAFANLem3hV5elqxel6xOAhTj55dubxzV08nJ1vg==", "signatures": [{"sig": "MEUCIHnyKGQBgGccBCmMRkzjqXWbsibGusXTlYlfqnwRB8JPAiEAxAhyXm8VgbIQhFAZ4cyFYbbzUzS07ND8/Pn6snlv4jI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ06ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUvRAAmBaHEs6L6ssjRtOZWLmpQSrN3UeiSb1CPAwomsrrtUfCZ/lD\r\ne4hOzOH4unB5c3EUa2GFx82HQIQldNrsLZgLIrL1Ak0cAKfRc6MZF+afxhjU\r\nrUfmGC8YEM54VJxV/k40kg3a4BRMXSUokmz4o4WaeBY9ynq0OeWeu5+30Vkq\r\nSyJMYAkVtEYovypDA2EPN93/6aXxLzwvPrWH1F8zbsOVmc1oYcfmolxcFKKW\r\nlPQh7xOWLAijddn6Hbhj24+UiSyBZguU8rPzJwrFqcGN5WVBydRgN+yfU6Cu\r\nrDoZbA5FDDPWa0hPozzbwty2mwJxLJURq2IE8hpUhExP3wLS7zHeKfL1RCZY\r\nYw7RNbEWReWNvd5xERN9X70g2NwCZouqKFbDkEBHeWRyJ50QnzEYcq7MUFrv\r\nDwy1auF6wL+lYV9WRMO7eG+YbP2bb+2+/pBWqWnHU8ikJTI72chRw5eZV7Zm\r\n7XT5YgeELP517Fy8KlHThsQJ2ZIhT6sWpofKA3Lj4M9kcjK6ZDLiAgSA+Jea\r\nFWzwh49rKCF0dokormbvqqd0+oJhAGZxet/OwaqewiIoZWVjYz7dMYXqqkxj\r\nITR0JabbslIQvcZMSHEcXkuli8izx9oU2aE5U5ITBIZroQ+DrIASueOpeio4\r\ngTWWAtUkQOBYKUzHNZ+yjaqj7GL3ljaaP6g=\r\n=aysR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.19": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d85e884df3dada99636224da702fdd98d6b26660", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-7XOQG9YM13UG2k4hF9dIfZT3gSlsVXTCKaHcZ5MmI8JtxQ4PNsowU5D1LZVp/1L1TmsiU6wEzW6l1qrgewAX5w==", "signatures": [{"sig": "MEUCIAyUJqpwk6ffj3C66vBRFhhhHwwJVRlfj7NBcy09ZcYMAiEAjhQrbZkhBP6J9DmVEkddq1Uzl95HiiFbrLE1Kba29Fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDpA//YTjVrPwWzx7FnLa4/OXDT3v3SfLHWuoMj9H3Fg9ZjtfLe7lS\r\nRLdtA/iBSoUUH5wcIGhiasuOJaNj+CS9XsxkkCzGYzeF9H8db1J4+kW8iti8\r\n5no21e6ipKmEd/gUvZchCpOq9V41tWLhb7Kb3JJRonsIQcGcrWwywfrO7mif\r\n93Opps4Jnwabdy5MjhO79tYOImbQYMMyBvrd/xKGeDpTMFUtql10LLZQUW5o\r\ngfBnqiwAehsWDh16IBy0VhHwhB0484ilWYr6sncQm7RO3lIN7vLS7xFPBWVt\r\nI/WclQcT/5OT1AhmsejB0tAELsiP4zRkOS6Ckl5XpPqz0Yp1QcDxH5KxgX6O\r\nS8g0FThoJ4qHL5j5x7je8lizjWdqrsi7k8piDuMbFY5jrTTHrtUtste8QSnf\r\nWnOqKUXU9wrYccOKO/c7UrphOLFseD6n08PUTFwIiskPZ9Vs1OYBeziXPuyx\r\nwj6Mz99d/T+NOpggZBvpmXqb/LOKAc3A1t3y4WPCVFO3DYSWsy1mFfau91wf\r\nah2inulkyRasP0Gq7ldGZt8rsrlSkQK0DRs98VNHgzqyOazGfCt8ox15DpKr\r\nfgTWO7rMjLS7X6veVk4wwhj9RKI5B3pn6MNNsya7qR14GP/T2LXVcwn29fYQ\r\nWe55vy6Glp6tEIBgHPLfrNwk4up47dK6aDs=\r\n=7VGv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.20": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "98abbb224ec91d9e8258763749389982165cfdf3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.20.tgz", "fileCount": 8, "integrity": "sha512-K1hedZnG/rq1iNPj2/4MsZAwWrwIhybDuffvY06fps2Sn8Z9T9dfnISJMCzgAU/kO7sjNA0A/HINPYFT8Oy3Yg==", "signatures": [{"sig": "MEYCIQCXcXlsTXmdR/z2baIHKy/Y9eA/2FrdWibng9Hq1cjQegIhALAWIeJRnXJ7blufs1lc1OH2Z5LnXiLl+39v8JYNNP76", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCPRAAm5pPtMIMly4sjIOHhjbT9lYRqP3tGMqpM+JyXQu0iH0213V6\r\nNaj/x+Z6hbtZrbsHIefUFvlR2X2LIFDSq/vjiylbR+vSrkjfF6T/ZhmJKBe0\r\nmlWddreUbrMIeQC15D5ebmFr7i1jefAzd+jfcSmaFipIon7p3OPHaOt1WFpv\r\nly/FH96rQM2PMVhEygcetm2a+1gvSRA4oVplKTWowaLXKMwK2BhA1MUBX8TJ\r\nEwJ7iB6AsTi/RnCpiHEoxeGSwKVsotk/7vGp2kuSrQMDn9kO6UHF77iuIc1y\r\nS07SGqz6Sxs4xJFZgt5pFWdsQiDELb63Axcp7wWZ77z9msKmJvLz/szT0f/D\r\nTfMrc737pc83Ztx2mLcW5xZL+F+QsmjXE9YLKdI1txwyupnbhY8fyvNxP2A3\r\nwLwVOfYGXoW1LvHddOe5ke2gOPAWCnkLad8Qw/1QyGXTgH3oK39xu3m0P7mv\r\nYGX6jjsa0G3TvQWlF4poLBoEpgLDWABzwj2Wf5tw/YmBERE9cs9BY6601lSJ\r\nnev+m56taJlc1/DFeB6nNA4FvK5PsXYAIa7NLCyW6fvHm+8P7gM5y4gcb8m5\r\nG2tqLs8X82llLKQzVkdRtlMOcRp8/rmc3/A2FGzku0n34KDlXN4xmcCfmWu4\r\nr9/Nm2TYeNj1jEvV3f3NknaI5E8nuZB7H9A=\r\n=78vY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.21": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4be4efa83b0c2d3b183f5494d23f1d7cf08da97", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.21.tgz", "fileCount": 8, "integrity": "sha512-cS8aLTE6pndDww+tGGUbIhAaZeW+9jTivuq4iSWCOmO5VCPRXXfOaaNrxrvL8lAk6CrRNqKISxMKt2BjOtOstg==", "signatures": [{"sig": "MEQCIG70yoFXPolqyRDm1Ji15JVbyPPjjcBQv9F0CzAIatKDAiAwIOZ4Fl+IUIwPK8jXDuhbZgdmSSFkfDvIXvhTKOaCmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHqQ/+LptTU3KYgc+Gnu5odF/mvsazw9Zke8ti/M2zQmEHcQBOQKvi\r\npaY7Q2GlPv1GTHLBhdtQFz56xenTDUlX4hADCOptnq5LG6SFSeB2OoosX+sF\r\npSYaQdzi1CeVgbgutnflBbhaHdwAWaEFd85NVoY6Siu2B0L5iKpodeNMO5/i\r\ngw8VMLSrrr0skIOBR9ZHOYdykUNKKmAUoui3++trKL+y2H0OFrbHvY2CSr8S\r\nMmObwrnPBa/Ez/uktuy+jDD5FWpoPAnZGx9fgONx8KTj4agcuvDNNOu/oynJ\r\nzYSfHsH5rqHGv/GWVEJ1YGFAVGBcALlp1HaX0JKD/zLcX6UqzNnHJAor648+\r\nCSCmdFV168agsf/0ky1fIm66EfVwDKm0b4LhpsOTmXkjzay93pjOc8H6rxNg\r\nT7bkMcfGFF8UW5z9FGkRIGFbq3aWhEo6haqCU/Z8lZiaVZh9tSnfDwIhpctT\r\naK51oV2hCNuyvLE7ftJ4TECKf/rZ8b6kOpdmeIjWMTYTznaGPXVn6FYvi/Fz\r\nY7Uyw1Qov/Fkr5VmRAFQio0RpdZMf/CaIv7YkAjXD/yYU/Ag7S4QPZUFnGRi\r\nHrDApYllzK0HXNoaac3T15WK0ZyLd8GbLmIYVxzDbv5Pfh40lKixKNqK6Wmw\r\nLPEIWa/o290TlhtMo/cbWcfL4EeZBJ863CA=\r\n=jvdX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.22": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.22", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "68fa986bfea2445a2a76c41fdd07a5ba986e5098", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.22.tgz", "fileCount": 8, "integrity": "sha512-z61/Vjwxh/uLtyX0oWUlxej6PUbJUwOKOCy7KchTf1ce2xL88rTrIUvFSpkx0i+ZddLVBBxg9gBKOzJg4Xt5BQ==", "signatures": [{"sig": "MEUCIQC4cBlK7oqAouCtcSRRCIHF/2VFfLM2DYmqaf+jkThBRwIgH9O/lJnlOG1q50sGghtrcnRxG6B4ZjUhxWldXAlKyNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSnhAAhR6dC0U6Vt+CmzD+JlYf6ZPP3dXoj20offP1qQlfxlhrw2wD\r\nbSQnPubtzq/9LjyxGVvKFgCch87S78NLnBXZSvvY6HRigXq1/DjzysiNnOa2\r\nHUr8fGehUdVBd2+GXuK/22Rcy4A7jzB84nllGV2lZ5WH8k761FYbhmjWoiMc\r\nCMNPtc4eU+rYx9odngf212obpdNLN3gEjeKl9kEXWrhy8T7m9DwZ/jb6u/BA\r\nXsd5kjeqrYMiRCgq5/63BoyM7QZw44q6KDe9plPBFEqsYXSZep5JSjcF7Yuy\r\nmsRlJr4/IHS9HqlClSuR5xT878XN+F6pTy9eeWiA914xULp1/xSlrEJgUI+i\r\neCfmkJHEiW7CA9mNeTo8nxP3uuRFWD1Qu6hWhkYt7VxLynvH52EbcE70PybJ\r\n0v1AkMVYFRGgqwdmnmNm/QvrxIfLxCgsClH3QPdo5+YlXydTfX3vdwZHtLE5\r\nLIe8yLvarEywSSI1RLSNhyz2JFMIrcnB3Fnwbhx3lwQYoVzz/mGOwI2Zs4Fh\r\ndN048C3ud63DtlX8XNdTelg8v2t0Q0TH4KE9vOEOu9ynmiI3+FIA93FE2MrQ\r\nt9H46RDUssvZzOTJfsnAgyr94KYxZqQPqP8pFrWbiUzKBDAgU2NaMdzMJ7nT\r\nwuabFOCbAfuRvkzWLuCbiR8Jxp5EhzDGTiI=\r\n=C2ml\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.23": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.23", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9da609eea8a08349e1bbd2e24382c0c8bbcf9795", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.23.tgz", "fileCount": 8, "integrity": "sha512-JnA8gnZTxzwt3LWjCHbnCdGXzhEgsGPUQk5com8+VEjemqY8eMT4TBEnun5LwRzuwb4kR3HQoctCgryr3HRFEg==", "signatures": [{"sig": "MEQCIHbgpq8ESVt5783sefLbpajiKyJl088TuE7vuesI0gk7AiBgOAHkTUIs+e8MWrta5GYNc96EkJysabwm2lpCQ2bTDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKH2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopFg/+On8tBL3nui7vS6+6HRHYWzcLjpC3xuZ716Vci5Po5lR8j7/L\r\nj56xq6G9uM5zvceRYFfBCBWEralTF+OPbjBTWHN1vgFi4zOlcy5VHdq5yTod\r\npkCAoDtX5IFFOHIc8WU3R7YL1nkZUrA12tZJZ3btE0KbWBndJneLw3BLxX0A\r\nBjwTU8TPMBRNGPWhNYPLufam7on+FNlyD0gD857ncCQMfGYqnuay6/aJlR8i\r\nXMPovFAI0Ug15PerTukBjGEnh+P7zG+f2x6+TAnaFLfMorMZ751mo0RS22SL\r\nfbjWJCgpcVSfpECsBB31IULAZDwS9yVsgcR9ET3rr9X0xmvtT7JpBZM96Yxm\r\nlKKdShE0aEq1IPjantQs185cEnTgbSrXzZ/QF3XPDWmQ6z4iDo3gz5gQlLjY\r\ntJmkTAUAOXXHnlNxuOmEWscymt0XzklZRO4WI9q2TWePgrPbRsn3M9i0RESb\r\n5uSZd3ao4ko5OhEQWzX1icGH7UWBNNSBBLg1pNdOt/0Njcc99Tg/z/eIIFr5\r\nQKFHUs9fMWC3ONTnK0W9QUjoMl1TzpNSi99HxIBq/mPkUdlsJhMI6eg//3/U\r\njN+yoQqdl3KquG5vMcpLUqImzX4GOvQH8NqP760xrECJ53YnNv5Vr44WKe5Y\r\nF63lBvMAHwi1OBoBeN0SM2uwDYrt3ZW4228=\r\n=QF24\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.24": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.24", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33301a4f7fd4a8b5b1d05f12f955ac734013278d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.24.tgz", "fileCount": 8, "integrity": "sha512-HShUKOXHa4l7IBUTuqIpllypzDUobsIlKXbsqez0EXEc+clfC6PKpoleIN8NlSMEid7ZYTwz9m1sYD940LClHQ==", "signatures": [{"sig": "MEUCIHSLptai/NrjZdmThL7SZz1UcXur1gJeda3Q2NwrvN7jAiEAm6uoiLtTO7SCafuZSBGP7+6Yn4xNCSO3CCKGh+Z+/Ww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLiFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonjg//fLrE33AyW2nA9+eKi/oTyfaPyDpc66UaLFwgqD82omJv2xfW\r\nNerHEtrfCInWQDmysAmWBkwCSwBPXDyM3vPK5U+6a6EXFLaRjfNdtMBktAl9\r\no196+WxnqTZfAoemr9coWb/+y7EtGYopBiSER6EnhEvztgxXNjPtfFJCmzYC\r\nNz8ZtevgtQRkZkDgZX5HVDAabSNA3EDbI10k/+Xw49ILB/D7aPrJWur55zPW\r\nnVdzwSZ9RK8v7gMf5Y3w57Im4+6s+BWNgLNVtyuxVXw89jCEwfh+yubxcXmB\r\nol29wJn+yut808PRClzAfvaYd28w3zWFiBl/GgvU81EzJo9eyTK5nXy4DUrH\r\nA+t5rA5Id1jJq6sVVabxGn8JVbS573/lSRZwuaYdTKfR4IeoAwJAyD2I8tvx\r\nNqfEKTLbTQioNopxnLfJxQV5WhnPnXB1xNTE6G+ABSKzYUyC1ZRP/LYKvzqI\r\ndcgqqXPwvTNu2quZsUKJ50EWXALUVBRotg1wsfu4FeoRpJNrlS6xUXyv/P3l\r\nmN8WdDVbmXyxlrxx5ufvriozEyiNvPoKL36aURtBUOmz7JTm51mbPDSUYx0A\r\niMQpAGENr05JeUMEnZ4gYZ9gYiy9tIua5vrXnjASPCdJ4cKSVtpPdPP9zIql\r\n36W8kEcfPFJlrIO04cHt8HOiyio71IZjkcc=\r\n=6P11\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.25": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.25", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6246f531c976cbd662ca164c175e8b8335c43ca7", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.25.tgz", "fileCount": 8, "integrity": "sha512-+0A5yx+oCW7fOJjPNS4KHCwe/gvlRWP1YMmR1PeJoFKMKFNLfnE4u9Lnh0fT+PaO426HOfyB/jMOKbHa6mIYMw==", "signatures": [{"sig": "MEQCIATMXxGrDsliEiBhDVWXBUMYRbQBpQ9/cxQGEzoF1egGAiBmI+GvW2GcYGKA8HuT3RyY7O1rsSHFmQnFf9cv5fTvWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj48ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUBA//XU9kxeEpwOWLw2fakAuBOel3zEXbLV5xt5wvK0094hMxk+OM\r\n/t7FFec1nTPfFBo4eW4VysMcnRybcMt3UwSxJaX02nOuUtV6nDnFCnzNyVRH\r\nt2P72zupKXksAuPvWcesILvW1XsbNxNgACtH5FC+RlSmtAE+zN9SdSa9abWs\r\n6eAXO13NOLQVL2tquFGbPaUlxP1NuC4aZ4mwo5IPsXbHZ92dzUl7z/JkMg13\r\nZ79P+NCnydAzJrilAETJCDx068PnuDII0ldDNlLfff1BecYmBTL2LsLBXqao\r\n16lXCvtLx/AMGCneALLOVPtqeYyTJWvxTQZNWyGx4mORKEaGxZyvYGp4D0zD\r\nf1q6wLjY1VivKdOpI3PEYVt02lIiofGKihag2zYSJIYYfUHkvljNJ4Qv3AsE\r\nRdx3pzcOrRAChJDi9M5ZgMIYit1fjJpNh4gNR4I8xFV25hBFxNvu3IC1GH+G\r\nfnwrOhGt/nKADK0sywsNNOtv+ejtv27Vdv5XZrXJNHNswtvS6r3mleKtlHX1\r\nQ1DfdGEVUbSH8N1cX925zu7722SQIug8gPw0EZ8pnC/yPlBrve9HaDCLQIu/\r\nCklEog2k0gSxaG3TPjttGEeEbZYdMqYD4yYg9b5D4MAbH2hsvFC0k9WG7YZh\r\ni8MHWfFuboGz8+B4DbEiq8Iq28Wji73vARY=\r\n=04h5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.26": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.26", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a5955764699784d68a9780dedc218d0a8d6ec31", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.26.tgz", "fileCount": 8, "integrity": "sha512-riKjBN/LkAUHiGKFQo2fpI/iGT+hOupoqsxPs6ZXeMh5m5m8uiMrZbhQdUxGLE95TfZWaWXn74MLkwL7onhkFw==", "signatures": [{"sig": "MEQCIExIoSpFcorRWE+EO+YMUqvUwJXtfVv0e2zuNIzWyHJeAiBaqlzFa3P4yY6SOANiaA6tygvkm7CWpWt16jZgd+Rhug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl13ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7NhAAoCtcBjTXc6VA7FIXjlpbX0OAz6+FWnEq4GOadg5h8mB67JH6\r\n4Y/1NJgAKHbYDKotat29a3HYL4QJnzJw1yWEJFy51AV0c4xS8By9EESZXc5E\r\nB40/FGg22UFEVUprq//I6ob/O19NBAkfBjwsA82nTZuMWJK/ArTbD3NjWrvo\r\nQBrnD2x9nwTZL2Wc46DpIKQXjwIAPmgDsNiAu0YH3+2VNuwXNG95HRuWg97R\r\naTnSO7B48mnXGLni6/zYQyrAU6v1X+Qh6+AfnxsxU8SIx5OYYBhzolurL1pR\r\nyLXHzaLRwoyQf74+1cSqtbnQTB1AhfxMMLt0C8EwfWqfvuYGTrS8CZ3XNASn\r\nSVM1MN8fjwXyTMHmr40zB1xkYRB4vSOECi/Nd2ph+St4knfbJWKz1WFUjGxO\r\nLLvpoPnKueNMVTDCQscGAJnYtaZfQmeh7OF9npT6TdjZVEK3CbnVHzrOHCfq\r\nODfNaaXB5x1vUF6dmwn9V4BIr74buG7P0ogwglOwodj+XUDDrpszZiv2Y5ZF\r\nXfXoTHxmaxXMMwRZSvLAANIGAVniYCwWmlw3tTGrpcWSI5dhMh+OAAF644wB\r\n0Pgaw9Mp9Lf74OtetluCEJlSICVkRHDpHX4jDH66HDZA/NL4GWCAWE/lOwM9\r\niHOSCQJzGrnVGWClKrWD4Bw+cIRZ41ISkFw=\r\n=nqFB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.27": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.27", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4fd964452ff4e4d64e1f2df25d95d46510ea7c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.27.tgz", "fileCount": 8, "integrity": "sha512-w/rSaYKA5Bv18540ZYT68KjRQRwFm9CDKCl2SSZqdXK6gedg2UWz/R/3srnrnVnKcBfGRVlXHJjqAsRQIRRqyw==", "signatures": [{"sig": "MEUCIQDU9oAhZm242x2dDLhNX+MG9hxsX131xa7gt9ESkkbD4AIgasT7P92jG4k0ZzGfj3/YxUPoNYpdlUKhn/jpiAvjeN8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqx4w/8CZAdaQP4gnZzGG6dm4BIev5LCbftDFITdTe6XBfH3HF5MJ6x\r\n1HdoHhC+LtKpGAv3W7fC/gNzDg1/au6Z7wU2ku3ZS3Yo4BnCl1n+Sw1IGISL\r\n9Xx2rBj+xN+UTncLgouZVq2+HY1HN/RubyEBtoR5n1mDFsXYimvxohwOFQHV\r\n9cDXjJL/1U0e20IpugeTPfG9oOzDOi8nWAxDG2QZNTHzXg+wmertBSQEH9hC\r\nNUT/5xy6HK8gTftA3XZaq/nSVJTa2Jm2Z4Ntpy0bQSnojn79cVYvLun7+oUh\r\n9SrTHntCmOgHQ8hzdwE/Qlv9ZfkZz/nFZU0qvT1MOcftKEaYJJJmCsqRUcjP\r\n6LkG65CgLGjOpF2SIMbXTZFfr2oL/0/tZyEEPf8o3lsNBEHPonSX9xBKvwVd\r\nS9RDUxFB+diDQ5NgkK8y/5qtXfCWwBmfy/ECWj0yWPoyTcpqeOs1GdN3Rcp+\r\nYL+0YO3Sp5OcjKbUrnyWqOGHtRi9z971deMvImAEaSaQJvOasSzUadLs67tk\r\nEUh8fQ5gugZN94FOyT0lTuYBEM8cIr8LJjVYE/VQr0xTWaPL7Ff6hhMJbd4N\r\n2sX52G60RJfdOe3oemuai0U5WZQX9f4OYlpLNOhOml6hUm9rRM4jX5/yz4xq\r\nsvv+HIqQYwUxURE/kSDaaq8Yl3x/+MGwLL8=\r\n=kaqz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.28": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.28", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e6f1d0097d7cfc5069e9c0a102733742fd038389", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.28.tgz", "fileCount": 8, "integrity": "sha512-Swr6hl4dvZzQmCvQunFP9fzw05wbe7oGkc0GteN9Zi30KE6LwE0ensy4AN1+yadeApM5r4AF5Vq0nnNMjZ7H2g==", "signatures": [{"sig": "MEYCIQCtHJpEDEqn8KgkkOh0cz0T/7VotLZpy7Aj+XLE4ttnuwIhAObH5jgcdKHVsLGKkki1qRekOwlKkc1180a4QA16QQZc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPhA//bDcu9Y9Bq7Jv1mWShp8ZcRRI1wRfJMGWFrnOXuckzxRL5l8D\r\nQvAo5YXogcs7ikFdnsQsqHb4A0voX8i2TxatmBWUaVneHAUjHfUreGDJ70oF\r\nKYgpLfU5/PunQ7E6WkYi6/r6gUXmE2Hi2ESgBfzBiZqg9DWGpRC0D29JDyE7\r\nQmVGg7pJtPHF2M4RGFqUxHi34CXYeMuV819OCgbdb1rDc9ROiW73qeConhSd\r\nFMquBAjqRK4bau3OYQ23uIwLRSRHnDqwANYTmqoFr9ek8bn+GSgtR7qBfxBL\r\nVYPk+kqp3VCbeqepy5RsQwSMarIzRXPrcW7CoIlOzooa9nrLBcVvVVxBxjdY\r\n8VVvbduzEXO0R4cIEuJ0zIsL8bo93l2RKLvUD+R2XWXdnxiJF/GUBowl13xr\r\n+wuWkmsAt6d/ui2f1L7I2/UsmRiBSIHFEGFMDo2ZcreXJ69RR3Nn/deJ4Mnm\r\nga0kKW0IaWWOXMj5HIPwOb1PXqxHQpG8KIejuZiyqorcg+h87cIPYhqRbHve\r\nQuK5iOg1L6sbyhVgPgBjIhUjcH3Xc26Tayb0OhU84hDXf/UBpAIDynTupd5N\r\neqz5wH0kj0tOI42mH2KPLhyphICDzcLFV3Y6c3Rcnb+M7CRun/PQhVGg+UlK\r\n7nyqV85ANFxdf4r/tjlEMH01nQtzgjj83A0=\r\n=PToV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.29": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.29", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "99eeb9265e1f06474bc93d453bbb318c86257c0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.29.tgz", "fileCount": 8, "integrity": "sha512-juzOEoI64/lc7Ya8qcN1XyPbWbXPWJ0mCL0ye0c+J7TRsqTltyZqZVR88ra1S2NrhzS+0E2RyXZ6mOrPrjj7dg==", "signatures": [{"sig": "MEQCIAH8uXn7gcuIjgVSL+7yOFrYiuHHZUoZGF6rj/UTRa8FAiBQ9OZ09JL1fx2GYyiq/kqjGCZsoUo4SnnnNjp9c1cs1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildr7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr37hAAglg0Lq6eBtwtU6zAXccLafxz4y834lFMPYsdLUHVXyFZOWqW\r\nMM/GHvUT7Y2cI8FfxVBWHT6BSEMhvq+Y27CsM8cszizAYFoku15TDtaN0opK\r\nMxLTGTz158tg09yUrGwJa4d4BgZeiIIVFx6+KECehCDWZRjXK4gkVSQE8Kdg\r\n3mbe5BW+yHQg2/1b4XPdHoZ83tLc8+ejJ4AswGsZ7GfWc62+Ie1c9cxqxQx+\r\nlDFAJXTUPzzfFk/eqva9U3zZtg4PEQEjMCUsDqP08wXlCE+jEM+67ay1bKRB\r\naprFeTKtmLEu5Z4CneXapv4Sf73LPTyYVI7UcbTrfwmHluKDT7YU2hpLdZ+F\r\n3ZX/eohIEr7kj/OHF/76bsGM2Kx9ChiJ5exW2i0zFVMC0dyF5TqtCRjKhIeg\r\nsFa2EJsJUbwDW+eu5z9YcVBR1RwzhH+sIQnwjfbRt0yaQDr613s9DISkKKA3\r\n24PEGElaBYlgIEMPAemTKa/fA6jav1xrDo6IbiSpft9wAXFJTJKuBdN4vhXH\r\nV9JhWMnNqU0t3fou2PN6l1xRQ+vCj3/8Uvu1RVRDz35Ys2Ilolk0cHXJyyKG\r\njNrX7Aaph/K0PzB7H77iwIknVKJ9R2RgSjDLTEEJFohxNHlc64jgmMM0oIRG\r\nDGluTDxx+srPWbDze3LjwyMxkz2Ll4e/Jk4=\r\n=+Wmz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.30": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.30", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "45c9186d93b899fc8e92ce90bef5fea90eae58a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.30.tgz", "fileCount": 8, "integrity": "sha512-zruhsaGnDcAc9KIxeK2dG3hXj3xVZNyXXwiijKk2Y5AopXsjpXD4/odKDXAmvpGAUJpl7dakKjkMVzRfcAMd6g==", "signatures": [{"sig": "MEYCIQCwcqC7XsrikJhVqDCeSKSGBbINUECz7YzICA24OBb/KwIhAKItRVZua+zeizrchJz+S/OXnM8przZMaGFhXlcJgLb2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6ZhAAkroofDNhp1YKBMPJehRHjh+RNhHYp3PiFjfeoWugwQMyXfwR\r\nQw0N8icwDVmg1Vyw0vsR+8BjsktNhNRVUJyKFGVBPqlNb2f4jhO3WFD6/um8\r\nDYC1J+nqKZQCGFuK9Al/5/qvyxIdQSK6NQSZZNyUiSn1Y0hvpXtPHZNTg/pM\r\nq4NkR6OUyJ72UAaFluSk/RnBi5/6rIXqUbKeptdRrGwhhgcnlaJ/sl+XLFqr\r\nx+TW0kYNg+9Znh8p4x6iv/60YwNpQlN5/lWlySjc5UlPQHXcoA1pmplIVNDe\r\nzSU5htT5dWD2s2YnPWbfbaYuSlosRsXR2IecacoiQvJitI8DYdS7G6wvXOb+\r\nl2ayJ39avkwsQisfrIvub1nlwkh0qEIEZinQQV0g2V5nqmzM71fVursWuNQC\r\ntDJe5m1LV3m1KZf32GFlHEtPEE0T8yFEW9oDUtBoxnMr3uLpy4gRBmF+JHZ9\r\nhfPrv1nFdwHr2+5HC64PYxtATwPXmG+C/kA+uGGb3kQkiZPU3NvC5QXNUowg\r\nSi7M2bZjnfrhMfhX27+lzyBNddZZ4cQOCzhW7L9S+RhbYQOU+ZXof53uSe+S\r\nXU/8jfJDShuSg75FYlPtRx8fHl0BUZMczBvuzRyxJa7iqLDJ/YgZbPIi9TFz\r\nDA3yLhfwsnbOx02CWhkpYwdxLNYzgfSdKqY=\r\n=gidO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.31": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.31", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf235204f432f868c11437a0846a874a84f239e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.31.tgz", "fileCount": 8, "integrity": "sha512-CuQOLQOXMlThMnuaRG9fzGivav+dhMAtiqT4nhe8geJ3Ts8cEd8k4gOYWt4Fz6ypyKLanP8JaEVbradeLYR/tw==", "signatures": [{"sig": "MEYCIQCJfeaCSpAadio5H7OhVwaRBYOBmqiFQoscU6UxZPQMlwIhAO3sR37jCJNUUQxjFXLTpGgSJbHzk7FOnLHmRUzDY/v7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXQQ/9GgxqbJRvY4k8hHzvEQuYZhsWyPvHCRfbBDzjWr+iyaPgC6Mi\r\n9a720lJn1aBHOfBqWrrQS5JgYday2ufQUcLZB34XT60eHg+eplSEDPTtGk2f\r\n9NAoF+rgqip7FEdzYz1It0Ijt+j5OlVgm7PD/xLVTb1TV7Pi56l6GeobaZdR\r\nt0SLpn4HxAlrQzvzkla9OWFhqOZyK+m29+RIKQg2ZqTtlWizs466F4wg1z+k\r\nZv9Ct8gaQlxkcAuOfyJyYYaW0LSf7q6mpXaEUCaEA883QSvPDhIZDJg4r81d\r\nEafjcXkCwYtTJc7IfrFubRiJWNE5ZzueoExY9UPl6lM9uDXPWrN6uC9obp1u\r\nrUHwyF7l2EJnD7KNx4ViOKAId8vdwmTAJ2wFZTY8A1jw4gBm66Z/B1W9aStP\r\nr3qHMv0YZK8MfCv2/XmNrRSyaqdEQn+D3r1Jn8ryKBJXOqivEgXTE4uL5y7t\r\nFI2P0dv+ynwEV+B1+GSUvAyQ9VxW6lXrNHryl41srvOMN+3Hc7cBG5VkavB3\r\nb2wkyzvDfIvlj8Cje5ppxJMMzQGlWcofUj9EBAQuibeULeh2QWWI0blcABmU\r\nwDOwilezz0e4+tMs96JzyfpKJhdXAylV+mbLGsYijl7JfGSVpzlH+FI7l+gB\r\nWfGZbpcRD1opJ3Itup1k/bJNkI5t1j6bR/A=\r\n=jal1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.32": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.32", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "75759bc423000f69967d009a0f2b2b2185b51213", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.32.tgz", "fileCount": 8, "integrity": "sha512-vgw1rNYlhXvW7LwQmQFpWoxBdGMlZrLXQJnmr7FDGjS3hzbFGEWjDCh7ARGDS8y3HGgCC9juwaErjfIfEz20sQ==", "signatures": [{"sig": "MEUCIQDkScvesdb+Qc+u83Bwwu0a3r+MlcX4HCkVnw3fV7plCQIgenwbP9bA1IHg3JXqMniDewuqQ2CqhFvKAnfDh7CPW4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlWhAAnYVFAcSQwhSfNvcC07EaMCjQDvG8hYGajR4uAy1RUzmcd2U8\r\nyezkOVbfDOgx8dkCuFkxivqio4FGt4uSWxrLvDcsCsoq/6ZkW2cgamw7oqkQ\r\n7GzIS5hwgMZeRbJOrPJI9RMikJE2OAa3Ph17OkILYdGOKuYHwa66Lto/nlAB\r\nmQ3oXPNr6NS0E6jsWZMLug6XUiWK2rLkz8lSxhyHCLC160uto7wUweG5KOxs\r\nvZYxZqoXHxPZt86nPxlBwk2lYTpY7X4CLTOS5NkmvcC09ZmxglaS0chvPkYG\r\ncXOpUKts33OGgo7gS6+sEasBtLWkTgZcRhTA4nuZbhkkG78FWAWLDl61I+Uj\r\nAj/ZJZeakyf+KJcBl2c16oY1qoISsiZwfc/0OpIpIXBOC1PkFx43DYH2KROG\r\nZ0ylUC7mNcyBlVM6uEfEfyOCWZ19hxi2Fbp2GpftDhbjQHGHXw2rGH7bDQrr\r\nydcGf3RfH+U+at51ldXIxQNZValVHIU4Gig/7s7zt2IP6oRC2NLhCKDqSE38\r\nWZNRs9GzxvtJB2qyYSBk89OJDc7gMtf1dD2Te9w3npAHPx8HVB5Te99Uoj/D\r\nGcRy+RiF/OAKcu6bcf0hs7/IoprF85ChRCSn74flI6AmnXWexWZu9uELcjQc\r\n876PGpD6bEeughT9KFOJyV6McIYXgWWZzZI=\r\n=X94j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.33": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.33", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bbbc7b1e5508d9be270122318154eef90c864b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.33.tgz", "fileCount": 8, "integrity": "sha512-VsU2MIwKnUMPe4N6ooGCHsep0UhQ8IiFt97+QxMtEMVEtuWV6Xf8GTx+9oBpqcXBSciPJcCnbTgDlVaIcPMMeQ==", "signatures": [{"sig": "MEQCIC/4bI7d5Av5txcCE52XyhL/F7DNjJEL2ial8O/SuePmAiASxfOWb/QTIsG2gf5Ezco5U8laJZM6Lh9aucP8l3B7jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHc4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgYg/9E0bqPe0pPprpm58E/b1Cvm3K3IAyAPOYkLAxAOz1mVc2st9n\r\nAAcB0gGqKmkgFu1cIP28le5i5fOEVgFnLk6zFNIFctOTmoaatFiruuTU1jma\r\ndp7ENA2Ewt4WuTL7IGBUZeS85Noqyw/d8ES9WL1jL9bhoFUxuZkdqm7TdyZt\r\njGgI+CBomxq2duG2srWGR2AoicoTSb3b10KKDCSGmf6YVgIUrsL1B2mnBViy\r\nNOTS5dwRGHKbjIi8p3bsjdVcVLQD4yqRpoPVjeJPtwLTKuHNJ2yc7sEpzWPc\r\nus4KV52rrPmjtDHD9QJ+vuNdIpDu8vJst7cQDV0dK1WS/+R7jqGjzZ8vVI5j\r\nOTRQitNhReC+RW4wJsa/VCSndZCe5hii+rvn0YnR1QYZ4+l+mW+hliJ5evx7\r\npMhAT8RMY2gCRvV+lxpO2b2de4qRyUJikkV2qkTIb00GbfWFX38v8GGGf6gQ\r\nEudC4Muxi3uLf2/8PYbl9Q/F5E3oNWzmwCiWWomwSNTVkkmAtHYflnCtKN3g\r\nosActSjm2X+Jn+SwU6HUoC0aft8/ylBIr1RfjqVYu8HW82dm7dIguisOnClg\r\nNsJmMcorSn+EfiRRHDaUBMICwTqn2XLfxL0kiMNBlW1qPvdUGoZjtp8ifPRF\r\nmiPe9O8i5eV3t35tVHHtmv7e3C8dcBw+83w=\r\n=2tCR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.34": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.34", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "183c682112f45f3ceab7910a12eb957bfc275c4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.34.tgz", "fileCount": 8, "integrity": "sha512-uuR6Ly0oDxq8WaZi7CuwLnQH11IQ9tvyWaWkWHYpaILwAI8xudu1eaC7xtDTDVDbPweWswC5xxmetgaG9rFRaA==", "signatures": [{"sig": "MEUCIEaJOxBaM8MBXoReqi0HrFawYUgPuYexezTjkHW0/hfhAiEAzS25/la5lwmI4cdI3rvS8/Vu1hI0rSD6vDsyNR2EEtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPnQ/+KZcQF3AzqABz2xNShTQKWJk2ACLgmSpP3SnNaM3yu94nw7ND\r\nWx84W5JpugdmWtO/ej00SrkUwXvOIIkOZLiOYYsZJ+4eao/lvtUV0xY/nyTf\r\nf21v9hz3ZR2Uur6hRdCoiaZsCIU57ccEbipFd2RWGK20jpeBEr33cVA1ryQd\r\njvA3xW/W070jSFJTHHW+/fMKtvuyHfDm//57XGo4BZ8E9R33ulEJSawX5+Ms\r\nyEhZY8BHDrG4sxg7aEAnOlKgg0TLCPRtnrF2ivIkUUjWoP9lXk4wnZ+Z+NFF\r\nrx/ggmfG9oBlt0S32pimR0xMROpAtQ2LIPqXxjOffn0E3qLDcLe8Dh2e+0jh\r\nWBXABVQpa2xkfFO5juC7k7UpFETVRBB8yCZmKyYBQRrqD2OE4o03MCI5hxp4\r\n2fSzyBGrqbQnugM+YdLhFLfGtg363mPeax0b4uHvCPG/QHeWBKBoIlM098+e\r\nvFv+fUXSXhwhzRr2/L/MC1kCT4ZbEvKjsVVY1UMRosOpsi6+5ijBOFUFJ0Ux\r\nkRTCmhsMm5sJpvTiE5+1i2xBan2dgA/GkTjcYFN8UuTV3+Kfi0/ynQgpTQlL\r\n3wOOouIMosMUyBwzoAnTorWF38fEmoiK31Gt4UtolzFMRNgdnM2ueRJascnD\r\nHbPnlu1PyzwjOGLHP33wwzQOSEEHxiCR3BY=\r\n=mVEW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.35": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.35", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2d83fd4c8d849a24180cd63853f487c676a12d21", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.35.tgz", "fileCount": 8, "integrity": "sha512-mF8em/nVMe1aKqFlDZwgzSK2If+dVGjWPn2KyXUg+LMUKlXaoxIJC8Hm6FmpL5Vojbe8n/VLISWkj4rpVuQg/A==", "signatures": [{"sig": "MEUCIE5Sr52pePD/jjbS2ki4NC84TqaTLMSYexzM9txhBDBMAiEA+FnGK26mdDML5m7Sfo7LoDLSq4x3pGMGUNmfGLd0qus=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsBg/7Bf5jdK0uscD3QyO+ePa5TyLf+kPm902Jw715M4L+xzs8p4Mu\r\nRvYnLT+QhmrGyb6JHsAqR6FYJ1+tTSuvhcdhPzNV4kdVK3AWs8w01re0pDYF\r\n1JN12VyRGIrbMkG0cLojWUzaYK2yFHxutBbTGk2IzV0qRN0FtHghB4mWNBMV\r\nWYd4psB3mdUrOYMswQKSj8SIYF6dU87eG3j8rc//HzFA0+fVaNbBputUKafi\r\nWk6X2cDHQi+QaBaf7sHh5cxIyMLE4YcLyHII9OGdH5v/c79g8uwo7FXEPrE1\r\nbglx5mWEaLmC0JM7EbJW/I822p7+dIvpt982KD3SVpRdHIHfaXKrOE3SXiMW\r\nQNlFLEDNUnMBWGe0Im/wLs7AHUHe+3cBspqF/ype4xwoFJcGSAMDJRcyl6Tv\r\nnmbWs6UlBNdtE7/NMx3MlUwUmOcuhhON16vVScx82rZchE/qHb2tD3y4PwTI\r\n2pjhBUD0oWITBlV35gumUoAJAIDM+fZOYprcfi4w6jQnf7v8O1CfV769mjDx\r\nPGRcOOMl0i8dTgQDTks/EeuypqIzqUnC0nQNRpWiOqnD1Fo9sFOdbxCpXCYm\r\nYi2m1sVAvx/WjuofbGU+R1eOCa4dfgAmdFZ6oJLsabchy1VZmfqRdOM+XGEV\r\nclFe0D4Sr8mGLkdPJ/wzcGhxsrCy566C5Fo=\r\n=6+DY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.36": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.36", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b1b4d5f943fd9bcb492d90329d86821679478efc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.36.tgz", "fileCount": 8, "integrity": "sha512-9WHD0hrkENvhun3ZhVjB3AClTYqKXKH+5H3UPE+UoFhf0PRuKxyaDN/naSnEf2UoV1NwQGTHt8G6kE7ZV+Rplw==", "signatures": [{"sig": "MEUCIHFmo9T45AF/Z2ayvEqSqaXqI/JBzhdXN3lbrXJi8VZIAiEAw+/SalEwMMlaEtIyHWmYrg31KfIDf4ARJVqG+WFu8Os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHBBAApCmSBtgUG+GAMfojQ00Ci1xWhMZ2e/sZSMm/2lbl0bxa0I+U\r\nu5xCkJ8HofFH2+dYfruPqtvsfilJ3WkYJ/8pVItjMfdjSH545GjK9xtfuA3D\r\njh4mSBO8Y74sNWV9fFhzXhmKKcgbqh+9NTqPwFtQkY+BsCUVyQxq4g4Oj9wq\r\noLG7GevaA1snxigXzMvmFeUITTPYKP7thDjh0M1EWUMpi0/C+ccwCaxsdVwP\r\n9ehb+btlwuwd7cJdQ62eeXh7S0hl7vnGSe70DH95kCrEbM40vb3J8Z8Sa1uQ\r\n52GTcI9CfF0tT8mqdEJtVer8IWoKF1oLh4prlKjWqm9j0/5kE49Q6U7LKOl2\r\nE/T8qVLSAZFixDQHGPbUldS6VOMSK5A5nd6AzYR0Qqh+DASHoWFK9RP6RHnX\r\nFxr6UkG9XB6EYJiAMXdFwqYO3f1fIwmITdOvIbhF4LKAEY+NF0deUEGWaLFV\r\nQ101ar0FCwWBiMHJcIKctLghokp8UJbB/zTiDBLswuHH+ZHcptulh14kDTSi\r\n0tC88rteIs3B4Tf8OlrVjkaIznF4InM8U7fEtRewxEbShJ0Q/OsluVe9Kc17\r\nSmYBzoNpqw1mEwzW+ZijgjRpouc1KfXYkGugZ/ErcOSxcpKK38TGSgNG+0fw\r\nBGS5PiIpELZqrnmDW+jSHdZgldnv79jOFxg=\r\n=clfR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.37": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.37", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "619ba63ca2a684b5382d17e2bd9010a785449b30", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.37.tgz", "fileCount": 8, "integrity": "sha512-ogsNDz1Izba/j7OFwD0q9/wm3imA5qF7/R3OtX8GkE6SUq41tSl/Jjdt0C+jLj9CG0ylZbaz7iml4DaaFx5HrA==", "signatures": [{"sig": "MEUCIQCmw9pUfK8VK+Jk4r/RtQKrr9UB5/x8jt5WQrX/OQVBtgIgFCyCoATwR0ZJbpSZnTZc5XuKT76ehQVyvA/00SLYIZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvUg/8DxFeVwpYf+qlJltA/9E7u89xTqyVtpEbpdG4wEsV8XvnaWyD\r\ndkioiCvwSS6/3TH5dOLVHuRH+vyKZreyjP10qZg+7WI47+NfTvzN6X9SV0Sr\r\nA551osBA05AzrX/Vn9xnu4Qi3BR0ccZAuQGDr0gllVoYfb0VIqIR85uKqTAX\r\nU9oLNyNVyiu6SiXomH7zuOsRZiNa1LxE9pQouqiENfQ1aGFFEo/RI+Urh8rX\r\nCYg59sLRoHBh1VevbLUsJ8ZnS/4Wwaqk/oFkGIlRFC+LN3hMcbpDcCGvsI1m\r\nzaFnygfW5YRd939MEniqx7BDUTcTPxneJCgHiHkhHTanyif9CbtjY/mSL4nY\r\nx7KrCdLI7NCQq68dFoP5tny5fhtUhM2a6/mCqYMw9MUbYzRJ0zJacAxa6Nj9\r\nZaF/QwlLtVAk/yApBYBSiUzFprTeJZCDfVGv4W4ea30BdvVRgzePvU+K6E8x\r\nY8Ev6SxOwPCJG2ryLQwcz+VzRrrpzk8MrT05NwiTnyzgLDMzGQ4bOMTZ6IyS\r\nTOA+/2F/NgFUoCO9wU0cUOWO8bOdR1FP+uZtGKdJ9e8iQB0uJZNNhJA6oEsr\r\nHu9vRLFCmHnM6tN0qIdOFezsb0opeZ0NB49L4Y5rUoMsszkZaCaJjDQDKB7i\r\nRs3usfsG8GrZlXH22GqQ/BlgpXEBwqbSGIA=\r\n=r7dJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.38": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.38", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1e0678fe5209d97f56998e0e3cd81f4c3be855f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.38.tgz", "fileCount": 8, "integrity": "sha512-UoHiX/KTD+HGsjazZMFHzQSVaeHuU+BMoM6kIIDWNSSSRMJnZlYWaQ7FWAMRZnwoCfGikKxwFmFM8giFsnvVkw==", "signatures": [{"sig": "MEYCIQDxTJmU+ZwKX/TYdglmbyMJeXXr5Nmf6+98l66GQJOwrgIhAPnF3p2SFkA4ouG+GcnyttcnAgrE2vxnkB4BxY70OumA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS+g/9FMBjclxlsWGRdyDfSwCanyXqqH1OeAn+LOj88WqnEHtl3K1L\r\nTsyiKPkFag1aPISqXJXipnAe2CVxcH2sDwxpNYlNz1mQxe2+lSVZ05+8pVe2\r\n89RE8XttLJuLnBSPJSvKeTNcj4QaCCwZYAp9clZyJy4APy4MuYAPrncgU3EI\r\nurJfvsMyMppHno2e0wUyzLXbyYYAUjFcRkP6DhYP9POUpyP+loaLb5iiXgdX\r\noSsRjInK+2dv4pdMQqyddEnyWjTwCX4JZbyqQJHT/Cmju+T7cVgVuEVSG8/w\r\n7AEhRbjJrZ1goHHu7aMSVtZikylFGBQ5Pb6wRgjfsn+frOrpv2hsfHvtCezL\r\njhXCgWQaCuQCHVW0ZQAw+iCOQ0F0k5dZEclo4GxetP9mjjQsi5bYpVsr0lpb\r\n2BFb1UPGMo0El28pzdiLLzzMK8EF8n6exbpPXYM8reBz4TKKBoH1CemkHIQ6\r\noxd5T9jlcS3fJlM1KVfcWtiIxlYkh5gKfm7G2NMU4HZvK9VlupP4tygLInj3\r\nNZLswdK/8W1p0tll5vLrWz7Qg4vPURHp1YAjZ53696+E2OwOpAq8EFuvP8fw\r\n4p7z2qTBtKsyAOJ8tP2KuRveMyIp2x4QThK9vSm0SEDrAXh5gfFBaLKvt7cB\r\nMjzYI0jxdzmG6MayrgDuGlYZRxejIaElfQs=\r\n=31Aw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.39": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.39", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4eabb8652c7c1e2a5ea89d3945cb10a0911432ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.39.tgz", "fileCount": 8, "integrity": "sha512-N9OIzJ8NqCWZ2ORSHu7chKC7UPytSkLgDokKEmAAFdnaCDx/pc2KX9OWyttO1w7eFroYA0UZlj6+uwtY0lOjAw==", "signatures": [{"sig": "MEQCIHSg9bfCRBhNAMUketwslSScnhtMAp4Q4/8TnRtCb3V4AiBykqdP2T6OkUZ+cRSRZy2wl4vig2K85BFIG0VSlIf7hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDaQ/7BcCEicLvgtovPkwgeIMHDYoWj3ykjqnFcBgecQWSWIM6N/uw\r\nuLM4tjVkoC7OOqUQfg/00x3eseaCBPEOno5EXUSI5NoyXvyc4dzVosZQpu4e\r\ng8bMtOjZ+4gj85aWlV/knYIX6zCHe2f2RFWEvdULYVUx+ys31Fg1oUYFHGab\r\nZjyDVyhskcJwCoprzGbnOREmrReoIf5eKjNeOQ2kCR+GIBl5eUmDbxicbaIz\r\nFff3eW/syV6X3Xj13/fjbJAzo1NkIt1FWgoKVr3PK+qykilW0+I69LO0odnj\r\n1dTKHf8VPqccuHnpb7QTt7bqFLgFjYobQ9i0JV6QKyF4VjIpyIusLYco4sIx\r\nHxwrkwWZ9X9i8w0IkChXJtJDRslswxUJHPjSggUw9zYeLralO0vQf8Hy3Jfj\r\n3g5L5+ewabHvUj1O+9FRNGJzz5pN62IGJUNpLcG8RrP/bDY9JwNhCKxq9DtL\r\nd2Rra9LzPMDBe94kfEL4NWqbj7QvOk0va96Lc33ER/A6lCeNs0l+mBtcYRM8\r\n3/FgHX85Q5/Tx9ouSdE4PrSwSNvT6ARag9ydFx/MKSFSALjGGDGM8qhzrpbv\r\n1ldxBcfCY2t6nSfuNl/eMUkJuDnqtGzU7g6R/X3vi9SuLyg7MekUHcVu4X/R\r\nlVrRwGUGO0lD2I0C7Qjv8Ryqy/hARL4LrvU=\r\n=A7/7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.40": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.40", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e1d6d950c9dae36218968eb73c60e2b649117ecc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.40.tgz", "fileCount": 8, "integrity": "sha512-IzvrTip7gEp56VemxwDzmb7AgOMzgDAy1SDUnwBH0qpP5BKTXUKumgAGY+W8rNcZmUcA7EmE8wW9kbf+NAgSiw==", "signatures": [{"sig": "MEQCIFQuUHlGf5A5rLia7ssGCLwq3z56zCRRQuXC2Kh+np70AiAgIbqRYbZh/ncAv2CPm0SEObyO3oKdTz1AEDnM5QdkVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7250, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4JA//XDr5goSNogHNzgQR2cOX2BceC2OaAgGrIteCQmg7uvcL0R4c\r\nxJO+FCXJ4OdjnDERxc7zvbNBGNfPo7EJAhDukCK68AUeAkLWVxNVXgnVC6Vb\r\nfRsv0qaDb7cyGuR/YjT/eJFEMEHeMgEnAk9mIDfo4MSK/ira6xd4NjrZE19F\r\nCYuWf7XBe0tRD8SPEWk+zTTReMEXsPNkzo/EEIJOoFOblogjI/rdxecbGsXz\r\n5ONglxFfyasDB63kvERElltEGBF7R6lezQoPdrfH76mBVzPcdzVtO5j9apD5\r\nCdzlUL2MH3X6Q5z+xyZjwtM2aPjeO3Wuagu/oIKq+jPNmkyblsTI/8LKX2A2\r\nly6rbbDSyAY0drVoLl+Ph/8CC2QWCPxGv2Vf+J4H6FYsv51K9f/NVeTe4O5N\r\nJpkYKs1nN1iLy3jHyUs62zklyNy5jILb29z73l/2D6Kfos8Y9Lz9/6xPySi/\r\n/eQMOL5cJm/Xq0YOCk5gQ8Bz9paHGR7Lkni8/KO5t3OFdNYoq8AoF5vx1WsL\r\nWVR0dBd36iNuGFTy1y79rey0uyLwthFGcRbow7OvU0SbWYE/h2B1ClCkKUFk\r\n4aNygIQHXUlkmz0Ov0iGG+mrfn2gai7DKSBgKHU9S9kAzUEi0ywHsWGcn0M7\r\nOiK0rrHtabrShVA65wopdwmoQ3/6/jbg8G4=\r\n=TEjJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.41": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.41", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15b8378ad2b1d3b619f901837f10c1a91d20686c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.41.tgz", "fileCount": 8, "integrity": "sha512-YCArPXg0HdbopYb2VQaNHDnR0i8Nd1h7ajm6iD8nvWl1NzheAQsnMUoZxDxyCX3UFnExggOOU8fzK5pwY9l6Qw==", "signatures": [{"sig": "MEYCIQCFCAteiiKIFehs8gwhQXszhF6yUVWodYyuGYruztR7RwIhAJ1HgDCUEnwRQuQ+FRbwNpzerLsyJFxhIKtpQQeLHx4T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZ2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjjQ/+MbEkAsexJDpCChYMttV+ZX6BHaMsebGl6RocmvTYGfkHH/85\r\nb/PCkDjaSXhhLGLehy3iDQRCcyLDsOVcRmyy0znUFsAHCzCsdH2R81IiP0wa\r\nGhBFRNAHrTW4FyMB4AL8L9kAFcewgOVfe2ZbT1GKL0aOiaAZCbw1YuQOt6xN\r\nl7Pjs+CU9Q+RVNWMSj6UXjKYwzmpm7A5P7yRuZGDqefRG7cfjwy7kS9EmkfQ\r\nI1B9exmuA1Vsqjq4CwWOVyquT59s4eoTASmzvFCZ9KzM8x/hxrVz3D7j/3Mw\r\nl9v6LbWHf60OR1BnXaoDO3BF6ZsNvPqQr+dr5h7UTz9RfInV0y6Qa+FMnSMi\r\nwry6zKPEDNejBoPVejdCLtHBmIONgovJZQ31xyUDte587iBqTfJe+uwt92nd\r\nQZrI6TIjWL/jKORaPw2WbvRLvlRYqDEvaYZpGG0gZZ3ZXFZWy+e+qcyCpl97\r\nZY3z7bDTnJUG0yyW1fg1RZg9XvrLGN5QnStu8oGVh/q0xSljRmmvGrZ/p6jR\r\nyJAkbYTIyT0+75p9RMWAgI4D93Twi+nBBnTA/nF2lbn/6qdox4nXuWWi/+Lq\r\nFMdA/6ZJp2sMLkWEJbVc0SZXmf5fPaeNQ5ZAF/31XHTLumw1CKi4aZcoxHqz\r\n3JarsIb22glskbape/jvSsSr6vyO2v05fy4=\r\n=mVwA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.42": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.42", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "56298414a7ea3f9d30d4181fc2b9c237fe12b48c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.42.tgz", "fileCount": 8, "integrity": "sha512-9VzttBViW1L2mFaxENxuh6pX6Rgl2sPfSiWyJ7CU6RnzOp/HF/RJkiqKc+yjZZbbHoVnII+OZoTN+RkogJLPQQ==", "signatures": [{"sig": "MEUCIFXLUkRTvfg0kWK/yviUpVqr9s3wXsOhqKSslQUbsmf2AiEAyPOWucJSfvKJV6c6pLmxo3FS+nZZiX4HrkM9hGspDFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvefACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/5w//UkbX+l8nkKLQdkVtYRWacE1SVKr3lFYQYXIEQ6be4r94wYJV\r\nWYC3FpdIb0VDgX4sXG/s/tUpCmJePzFdBOZ7jti6LPP/gHsWILvq+0wKOidZ\r\n+uT/G7kKNmNPi1A2M8KH55uIwrYD7S82md/hiMsqNbcMhSG4PxMA46bSMpW1\r\n03xymokir2hvLjIhZHRxcJVqfaQO79+KDv2zWXYU65qs5VzdUnbeEWciUwaF\r\nVWd760v4EONbUVVlot9EDF7DXvVQvX4hD7rzg2+td0ILHC61oFbPB2rjjMQ1\r\nDJnaKllBqinx9WTOV3DxKIrtDsVOWq/BLVs+U+uZiKTKJKgVBP6PEUZ0HpUx\r\nwBAqgax39mHW5vsM2sVU/7Xro8l4mgr0OwzNuaEotzp8OSjvHrnjQIqJFWcM\r\nEGma/NdiuvXmxLz00oDJXfNvkVmW8Fa9s//TYgTmBYUBCcwc6EOIIuwKaWxH\r\nxtZgo9RDJyQid4neXFyrTwKDZQ3IxPKOJsZ5SOkg4s7NSkmdNHiW2HpAH4/S\r\ny+KBjxWUsCjzUItd2cFRzFpRq0ysSO6vQYHFgQOPowsw2hT4Np8JQeQzJQRT\r\nCvpbiuqc60Bmrw4vjVREPHFwBs3na5SWeOPmF7doeF6pfb7UBRs765G5YDBL\r\nGFHbMyhuJDmgsR7BY3IXF5IwTTArZSZBQYQ=\r\n=A7XQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.43": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.43", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e9fba6b2f8cb363ac27db762d3979ff429828703", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.43.tgz", "fileCount": 8, "integrity": "sha512-/qqv91yKoOBQ0occiZce9bXQFlm13KFyB5IvxPMBqWsz7uFaPc08Ju/JcihzBc5DXxXquT6j3Maf6+iC2/bbVQ==", "signatures": [{"sig": "MEUCIQC4hOQ9tkVXiduAya7T97efKNi5mH1v5lRvNtyqIXmWJgIgVuXGrxGQXVlnTDJmNwImCVVti4dMjx7cgidIr1+Zet0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvs3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/Jg//ZmTN+29pxT1l0ph4dyR45OL8S6rPYQDjZHN/dVuwd53+z486\r\nrLe4nHlKLlq+JR1WcUtbDlSlt9TcK/lKtCQnyV+s+ibeMxu7J8YqNGlWy9BX\r\nSxlM3Sc296xBXNL7OwjX90d9jLCWuTEq3Uq8ysxNjJcSVeIm4sLQF6evYQzW\r\nBwbdgnipLrclBU2az2PgXOSfuvCiAwv3JxdMOY9YC0JOHCEOKCAqr8XUtnMK\r\ncf4B90FDha2zhDe3rfviTIA6sPy5FPIBbyi4qEUxEn0W6DeCDKqv2X9BT2/X\r\n5DTQNPZlFM1CW3dXU69TqCcgeFPjpZ9A7aKzaDyDLQxNW0WLOjaXI8cx+F7J\r\nEUd6zaNCB7sThRUtdDfoxXfy4Pt83rnwHETBFddKeLutnh4daAw2oF3Fj17T\r\nTjQxBUyLOvqtkXBsWuHTCgiSsXv8WHwidoF+94njLIvK5yuN+61klJJsDoXq\r\nHuwdTmrkOiI7l40Xg6maLiTqEhF4FzNrD+IGZXKUfritW2oxAwySfKjNVzup\r\nPxtPxstjBiSRTPkF/LDcWR7rxTTiVSChrwbcjzmtjYGdvrkO6MK6Grd/KvdP\r\nm0kbFDmjXH3t/u1JZs8nkFZNbvlC/aBxdjEhipCCHEJ8cH1lq70hw1b4czP8\r\ngH2bkyKciNBygYyhczZNBI8YotDa2KsyUvA=\r\n=3UrG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.44": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.44", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4b2ab2b306ba86ef612d0ee3641830c9d439c415", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.44.tgz", "fileCount": 8, "integrity": "sha512-s6yu+KNvRJTlfbwrjAMzxXs29BcukN/cM0gi/67HCOpAWSgR7fqxtDAzp2LoPcYEUnGhXufFJTRYl/eKfMje0A==", "signatures": [{"sig": "MEYCIQDpop42rnId1Z4U9rxDUPAwG9FOgLnkJghv5hdzEmcfUAIhAMB2E9V8IUbJ7lNG2chlR31ss2WLtF/7EmzZcahnDXDv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhXA//err24y6HiFewE4XsNJaLtAF+UXqd959Bx1SCc2baD6TwantX\r\nuSmfbKGYuXH+VMLr5mvnmMEjHl0ydyhzdiJwLJJMZC/poQ6lUlkcYA4sTHUa\r\nKXR5XS15q2FnzZnEilDuJatLVcaxS7I1mGmmz2RrLyZyumzHrFysSc4cXxAL\r\nbkSNs8qHxJKXqy0JDkwn75hrXjnohp66pE4P44958bBNIcIgv6qqLVjo7pgG\r\nu3jukDQuR199ZQfKDrRLyaiGvbEg59zkNnc/FVslJQHeM0X/C1Q0GMcAa+Gq\r\nUEvG30dFM2jJLYHz7C4Jab4OJ73p9MmSzn3fecJBkfGYe7NRXhyy9wm+sdZn\r\nOlogTqVrhlN+yKUlLI8l45nMbmYVdI9ZCQcaxAw8/IIioCKwlgKgBAB9eJm2\r\nBZqHsj/JqoX4pxldvVo/4NSRRiWP2fVIvOX2RmyBwm9lJ73fTQ4j+uxCbyGZ\r\nfiGBEKSDXEeSunfDGm/kp2PUMM/nFrccEe39A+hI1p23ruBDKY9SrvEK7SVn\r\nNJiTbTaYGyOnOsgWdVEwUUQGoLNwUsfZpxr3BeXPiDHZqkTL841MqED4+utL\r\nHV2IfRLztBeg/hWeYZy3OdhuUPQSx8nuTKe4riOxaD+SMam78Vv1NSlkyMy4\r\nWJ1vR/XpwNMJq1rryPWWwNSKXf6xgp5uC2A=\r\n=JwmA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.45": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.45", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fcd770f12c4a9798e2d579337d85a28802cdca66", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.45.tgz", "fileCount": 8, "integrity": "sha512-o1XdyWWn5M6opGHCJs54daOI8ij+hO/CLEXXE9TsgMe5lNjG4nRvHdV7Am2eZLebuFjGi0IncOZXnQy6HSH0lQ==", "signatures": [{"sig": "MEYCIQCwdZY6vfzf7fMGSTWhb1Qs95pByKEsE8mY8pnbMLa5NwIhAPVijduwbNXm+eWl9OpQ3OlisO2oIuGek8b6jFLnSOtF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpIg//T9QVeBSySrmRbcubINBi4ynrMp0fBvbY1hR2U4xXZ3Nk67l7\r\nEfi5NSrFquGoL/+fJRCKCk/ySm/th0S3Gdlmoh18ic2HPfrb1v6SYJwmTszI\r\nBq3MnjSgKMOS+6kEAESpa/d64oFHfoAGtj52VlSNQUMkWGmCJBCnlwzPlDS4\r\nLOyv8K/mfqJVh8XuT4GczT74/aZFNA+WGzZkvTbsHglC4DSa0gOIVwEW/QR4\r\nVRBBcfLHywI94aNbKp4BNXyknPkgvwNsLQeK9r6V1VZLc0MjSkxnNk2MkQK4\r\nR23y/zOUfwg2endgHwsiIB74qLBflLaq6bKrya0mc4F2GopCaWA8/s5+6GOv\r\nUwFqSG15cySu8bTTApv0L7azyFSbKrJGFGO5TbTF3c/wymZ66DHyalWnEURa\r\n+LGZkVFIVdp2qkiJslUo5luuHXcaIcrVosSw6xJJa3+om0MkGWSE9GrCKA5A\r\nSqDZA/A50t1Gttsbcj79zarDnCT7XHVcLne4nL6cMFjlDszypMTjtUmJfWlz\r\nNNOkld8KCJTRmHnVL67caE4iCPMjqv13u8u80IXsiBversgD2GxZkhwwxvMt\r\nIXyEjUW+4WvncIQ12K9+0Mjs8W/kw93xzKo0Af+krPdgvcUk1M9keg7AyfOH\r\nvI6U6uGrA9l7PqLmUzvNjZNy/OewVY03KcY=\r\n=dP63\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.46": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.46", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3816b0f728ce9def080b2308fdc8f53373baa6c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.46.tgz", "fileCount": 8, "integrity": "sha512-pfM6NKYHtcpzj/igmfmnazsGEQqlfdbVRtS/R0eDpdXzY6RDbiBVd0mKUAO+au6L6IcN/6fSsuZYYDPDkGWutQ==", "signatures": [{"sig": "MEYCIQCQhOYU9vpLej/pmNn96rD78IWcw9cRW/WnrGaOkQo+wQIhAJNMO4Qmo/6fDh2k3Re6T/h5JZ9RSFae2S4IKuF8u40+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBJhAAmXX3SXLOq8fSdKwf9xKyKx8Myzd/LUAPvj9jMDMBkdHYME86\r\n4FQjReuslaAth+4pJa5qgEvDMUOl4ILUEW7X+ZPDEXSr3zTiXgHRDZNfDmN9\r\na/n0WmSIvxrbKwKeb8A4e8RVO9jj45H8Vcd01N1+aEreYf5IFyJwS51mqSmM\r\n3mHaAlQMfKc5Ab1fYNcF3Vt747m8rskVNdwRl3a06lCOK40tI+KiKRkoIy+C\r\nm3X9YpuZMXxeqAbsca9Wntw2vJHZZJruaFAShfDCPftmB6ghTlGcvdk0kepe\r\nYbPqC6daWaqDNzcfQAItX4qCmZjDRag9fbM/ROGmVKcVVM50UtkjaZwHYS6s\r\nqU4zqKP5RTQFdzPod/5p8pjTtw6nZJt6JrJ5A5CqYz62UJdfa5y6dLdiPDCg\r\n4FD2SxEK4DXZp61PH8BaKUu1AGkuQ1dDOrn3MoAE9hXPs1UcW2QNf0ffizHY\r\nQkG3ou4rvrqFUOxA0cNTeCHZmxoMci0yGVFGOw+0KbiXUjFWJpZudR3C+49H\r\nuFm1+KVOXKR2e6pkoIN7DPocsrxPbxHXTggkdvCijuCLDic2sZ4Gr6xWT/Oe\r\nrBP2sGiKXnqiIAnTUg8Blq95Xhpz6xNTn3HMJznsYwhNLeRnbW7anCIB2vgi\r\nigBGvu1JubycHQ0pprHMwJb7PjnQND9aIa4=\r\n=yIeQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.47": {"name": "@radix-ui/react-use-callback-ref", "version": "0.1.1-rc.47", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "960922d1d519849b3740db72e491d5c8fd562b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.1.1-rc.47.tgz", "fileCount": 8, "integrity": "sha512-a/Q1GFXBDzgnRXI1/MV+P8CRXJZtManDS3zqq34avcLJB4adTVR4+mA3jPMEPsyKaqA08wzHtynSC2hgstRXsw==", "signatures": [{"sig": "MEQCIC2ZTt0R6ZbnU87h8EKH4SUnEcj/O7/TYb7tOemR6q54AiAfMU1P254OaN7UsoX1gb5Z3B2lJCU8vH3elFxII5nQvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrkjw/9Fl7wMM8PxylMDOo5ddJZlvWhegDhf+MCow+TXZnrNIqdZp5m\r\n3FvAYd2IfwmdTf3axUd21xayv6uquXASzPd+Wwpbzii01AKWVp/mhQvSlaee\r\neF6PAADaabCUzyw6ep7E2nK4BSPf7pq/Ysbg7ucxyE9wqf1ztxMLx7jOaZOY\r\nBm0peQR3LeANJAPpfNc5SKIk31A+VmghQeeoqcvMwwPAGcF69haAff2vB5Sx\r\nXrtzPtVB2wLxGloISleXP6iGaDLK1Reo414kXqoG0eYhdrI3cmyQe2za1/SV\r\nEYIYLYq/g9tQx1Mqb3HYoSOuXzK5Kcicn1yjCY8qQrvmRlQR6WRxAv+f8xIu\r\nmQlNlFONA0BrIvv92uUJFwWFe2BRaBl4LRDnXjWGOWA10JGL/wrf44Qtg+LW\r\nvR4wdvr+qLwPWPZEVkeqwSV/ODNwwgHk9ErdbkIeYoJ/WimpzDttXzyTjNtJ\r\n+BGoh23D7nKAt4hKROUyI5DaON57qSW1qTQlCTxMA+hIn/JXh2cHGKlpTWdl\r\nUt6ITBF7PR7TxbWhSwTygrgLWFOGkn8tNgSH8LuguL11KRPqmazfS7mlm+sW\r\n9LS5eJ7/qQIYkgPave2ZixpAgw+DrQa0SF63UBEFctBFFb1CIxCmiR+KmaSq\r\nPY93AJRKrTknBtmr5cGVaeL7F0Unma22e7A=\r\n=HEeX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4dae473df60a7996dbb3fc53793e4a1db29eb99f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-59rKU8p8ShnmN7HKox80Z7DeglEUuB3Q1RMjVeOPZOoBEerEYTFT9ZkaQcuJZx/lsVunMAlyBz9s42uXOPpIDA==", "signatures": [{"sig": "MEUCIBfG4/kFRIFXzWIearxJkGE4Qy+QzDNXp5kMWjlAOTygAiEAvwAkVHmEOcMLHVD50USdL2hGJ8zTWmKn2v2h0dMIt8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Ev9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkQQ//fwd6TRb6iMtcOu0AnFKeXHLe+s/bCT8ovOf3F00fTRKkQSAQ\r\nThma2Ihyjpu+w5sK0pTZUs3b6yEkQYvfvoSHfLbddzBr9iaEi1SKTa8yOv2x\r\nu8uwhqJR/gRtWpriCAt7PIlJIXeYfleBGj2c/IsCLj8X4cBsqrhYv6eFEEoW\r\nFSjjWwynWd0fe998wYjjv9dvKkEJ9WaHuqQd/xB51UoQsVt/Gv2RYx6GINuR\r\np37UMM9eVQZ/9oMiNfy76GNnmeSH8HtvuDov2qIlf4kpUczl38DFIUPX2Kxp\r\nF2iv/MijCgkdXd5YLyOMspgdO1B1WiOGY4X1SlrG12LYfgW2kie6t2GW8Dax\r\nh2KHcwSUDp/p2JXWxRpkb2yAgpwYZLm7gbNIIoCQI3xSWD+V9JFYQAzYVfmj\r\nzfKbc31ylE/HD2+S4HhWrlxccZy0xoaQKvJwbbZHQxiqdq6JwyleP+wXkQeM\r\nGiBE8L5wVPYfCcoG9kC4N+rBQhLPjbvOr2jXfPmMCumbcAHKq3aB4S9pQQk8\r\nARnZgUFl4ebDKzqVUw/TdMAYmS5a0hokwOSDSIaz1luytUEqhq24ozOmG3x6\r\n7eESow5dSjbjt6aPwtjmY57NFWd2BC6/H2bT5dhaXYsuflm4Mopeb86mLiWr\r\nZKX3CJOSXvQb+cuGC0g/Qf9RUWoj9E1iv34=\r\n=/Hp4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9e7b8b6b4946fe3cbe8f748c82a2cce54e7b6a90", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-GZtyzoHz95Rhs6S63D2t/eqvdFCm7I+yHMLVQheKM7nBD8mbZIt+ct1jz4536MDnaOGKIxynJ8eHTkVGVVkoTg==", "signatures": [{"sig": "MEUCIQC3c9Y7J4BydK2+04n4Tm61UbgAYicUNvLt8VzDl9G6hAIgfzgbYlab9veTF8zZRiJhE1pETHkZpeXsw31b8d+oLYo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6501, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpM7BAAndYlr7qJxs+47VBCU192Goq813QQdIIGMcAVy87hQQIGXEX8\r\nAFLy7ZHVgcI9vZR9Pue6a80OOTdjvevjqDhpBOTLAo8suvxxCFjBCby2ag+2\r\nFOrZAGKkpqYbm6EQveK1kHkxVrU9IZ+DLfWe0ErScjp3PULylrucl3mDHR46\r\n08iXwF+4FIQ++yDisDpoGqOIB/S90UG37z3LlA4cQomYHQZdvY/T6XiD9leF\r\nb9ZHrL44/hyDJrItqKriBj/wDNMbM+pL471aMmHVKqPJ7sbiwEokbKDUAlQu\r\nT+/a1869DPXKW84aH0QPQsDkZC4e5HwMq3epH4IyUmhkdyhvnHYIfCE23BXp\r\neuzjjCYu7fWaAFS5f7Le8lnLjIQXyhWlqYdi0GGwL4pUL9RMy12cijdUT9oa\r\nmRDNMqm0B9e5UejF4P0N+E3EcGtpWkVm71N1slvS8BDfq1hP5/a13V6yJWBq\r\nNtAiDyvIB02L3tBgkDJPPzoGj4K6t4bKR220jpwtFPtzuCe2QRbkH1UVSI9c\r\nMcoX5EmsbEr5vAbqkMRyUOn9+ICiA2OGqE0lLgDxjpeFHPJJKqne6S50OK9N\r\nf7jJiy42LqsUTp0fCD9j8PMzClpI1qL1PS73yj/ZJbPmAkAPIEPZnlBa1BQL\r\nmmqbkD141VCQ7NLDGx2khPxLAbJkqjbotAs=\r\n=HuO1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f3198d1c7f6da90ce49f301f9c182269cde89f37", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-5lJX+QVMc/TGYtF5DYK1fmsrPmA507KZBLDSOym63cAJ+L20XwFiol18GRrlc9qtQyYSscNdBirQO7h86CiWPA==", "signatures": [{"sig": "MEUCIQDcMBLUIJMykoj1ulzrHOR7VodKWvkNeyxhbilTMdUiRQIgGncs57YBHzdg6Q22E2dCQcoR+pmbFfT/BuImeDt5Ww8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7072}}, "1.0.1-rc.2": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd04ce417a3453754472eb2cfa808a841dde2592", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-K6hc35f/IjJcadRZU+Ms/9+COMW7gjeWkhhKMOi9yvg/C9rC+Zd6IeWqdKajtk+3yoYlYb3N3geecApa+08tnQ==", "signatures": [{"sig": "MEQCIArv52M4fpv8MletBnZmwFz7C9+TMfYRRoX2axbdJJ41AiBMY0ZaVfCWE0Zn5WPnpLyouMnuUlsMGxidHhD8GIbeYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7072}}, "1.0.1-rc.3": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "7ee7f57a6c306a3c0d3a6527f2b69dc972dbc254", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-koNTLa+GBId9JRE27lNoSs7dsOeUkJ8okDj2dfkxRx+rZ273/lwkaVcHSX9O+g9zXe7ypjJCt60tL8FvMKLoDA==", "signatures": [{"sig": "MEUCIFr8CcmocZZ7hzXMRfmJ3p9SIdHm3E3O7IxRgnry+eWpAiEA+jJvZUQ7iCN+N4hv4jyUavb/DtMYWI/aNS0QIpiJFuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7181}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "1f618ae2b5d70f82ffa6959dc4dd365dc795f190", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-llI2ZhdhlkOMHu3EzylTVrQoFnbjdMq7teH+amvtDjNsRXNNSRGt1zdUEcirhLLnRZN+pb+NvYhZbqyjGbflyA==", "signatures": [{"sig": "MEQCIG06KewvveqviQkgdn8QM2Q+TFIZYXhPi7QHGuRus+TlAiAH6d7Sfuv9KX8GxhczbiBm+DJm7D9+lpFo2sYzQQweuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7181}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "7e221c52cd9d481e565972de3226420ab57c93f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-0bnSNwfIPCLusbjNAZnYomJp+ua+GfECJrhtvnJNr0RvsyT++52XyWWKv/zxb/YCqjSrlJ7Ct5s2wGDnP7p3Cw==", "signatures": [{"sig": "MEQCIB2k5EXJ+HmOn28bXMHY5bduNZuFr+KibnM+x+B5oPs5AiANLopw41tFSYf/jss0P23Q0Z1yTD3OVeaIsfNsiNMUWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7181}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "1115574eafbad986237d5240c4363a24f757bafc", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-kQ3jqqo3eMnptkWKG30mnEXc9yaxMI3fOOrZ1SP9hn3eKFC/cO52vwyfvldR5FpbxSm4cJ1iGUAmd7PelmiqXg==", "signatures": [{"sig": "MEUCIBIzUzUelf8wjpLgRWDT7gcA+9ByhI2vMxIJDJsk6gRqAiEAiG6a0ct//gX5tPSVtHr8YWhs31y81+TzYJZrT7wxemc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7181}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "f4bb1f27f2023c984e6534317ebc411fc181107a", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-D94LjX4Sp0xJFVaoQOd3OO9k7tpBYNOXdVhkltUbGv2Qb9OXdrg/CpsjlZv7ia14Sylv398LswWBVVu5nqKzAQ==", "signatures": [{"sig": "MEQCIClSYKu7eVDZizNfUJURoyNL88SIXJ0lHLEOXBZVdK/qAiA0YA7M+dq8VVBX8Z/vzW2jt0gGoUc4yOK7cEcFpdUFwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7148}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "5616002efa13479874ec945595207f9ee9520cc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-pu8nu8/KlDDRD15tALI8NLe0k96XhO3hrzw2nRXkO3u6KlN5CzjM2QKmL3HliUdd2AxzhwW2hK2JTLVuNDpYaA==", "signatures": [{"sig": "MEUCIQCcI+kXJLcDSm+AHlpxb9iOhJz1cCjFFb+dcW8mmxBUwAIgTE//MOcGu1Kt6y4G5xnOaxTkAYX+KNVVAh7rPsMmwdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6091}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "6844103bd8a3d16d206651e072bba26260008184", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-P23fCbtwX+Y89cady0pHepXtRlGJhB48Fe4fmmoQDMqgngJaQI+eSnKY0+MxrTeS7mHRSf3UPeaWOi1iUJ7yeg==", "signatures": [{"sig": "MEUCIQCltSmmZ67RdG8VpHdhAyR+CV9VIGB5os7hRhJMinZp5QIgNDmQ06ZPqfWxSRblaeJ65x4QMX/Z6wKKJgFBZ9Ps9fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6091}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "ba54140aa2838f022b86efb52d940a1809c277d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-QVOqQ2nGB591ApaKpSfyivr2qqJYIqzZ9v5atMRhCSV9I8Wrk4uvNYDyuzGoQQS29yVmV+P2cGtlr0i82PACTQ==", "signatures": [{"sig": "MEQCIHaZM3g9YlRmyLIp80AydIW+To9/5l7qQ3pmWMj8lr4JAiAEy558YhGQZOds0jnXy2flnZTeQfHJSca76ERFm/To4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6132}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.4", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "e76f14bf17d601e1f284174288c693d543e45265", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-z8f9aCDLBoBqPhObNkf7a31WFrIXEERnUr+g5oxkGDPBlkmkNT2RuDfhURoTPTge54Cxfbp+6Tpb5aQC+Co46Q==", "signatures": [{"sig": "MEQCIEmGaFO3PYZah1OHkp8ri6yI86EynpVORVRKZoiyLsNnAiBdSWAPCnfYCe1I8RTYNCs8+BYACybYLl2g9/UKHMM0Kg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6141}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.5", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "af549a7199a8b443a0d93a8932cd0e06c91479c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-3QAuFnIyDu4lkYewnJxMKuHlJlkrItkOs2F6aeWtp7x+xn6PcFFJAQAReXFiIL1MEr3kRk4M38r5oL9y8fEAOg==", "signatures": [{"sig": "MEUCIQDKbQ1cRZSkxMnp1tzjhIebvchgJ8c9ZEzwnc8wlLwsoAIgHn05qu0tYY1e5dXtKL37wNp5tNcnpz0YVkAZXo8jm9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6141}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.6", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "fc4a2057ed5708c2e87df87f676bd4b33fa3979c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-4hmlYbjJv30qxov2knVZ44D48CcE2hBM4QKg7W7Sxf/raAokMOzoGsjEpxNXbSaZ1TOHAC56iptLV1UHy/QEpA==", "signatures": [{"sig": "MEUCIQCAAOS/MFPEDfXsC5fyFd43kLW/gfEHCi+JryYHN5UGJwIgGIA3vUWu1feJF5cU598QqsgkKi8MRmDyn68OBnY8EXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6141}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0-rc.7", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "0d7c268e060daa1bc61aa429a19972a9c44b77f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-cOc2m7owmxsdpVFdxwh6awZ4rnGdfp9G59pisbs1lWjmPPlEmhDdnhRObwWdY1mB3qz3Y6L9dLf3xg7gVkcIvA==", "signatures": [{"sig": "MEYCIQDFiVIyeEFvT0+mQvaANLV4jduY7aWHw88kBWT9j70NxQIhALtug/NjNWW9T0QWQ7lrVmYZTZdWB2VZHYsjKBx3dkPe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6155}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.0", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "bce938ca413675bc937944b0d01ef6f4a6dc5bf1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-CasTfvsy+frcFkbXtSJ2Zu9JHpN8TYKxkgJGWbjiZhFivxaeW7rMeZt7QELGVLaYVfFMsKHjb7Ak0nMEe+2Vfw==", "signatures": [{"sig": "MEUCIEFh8u5uwxx/TTkxCVaegMtrasi4bFR3jE65vzHaxlKGAiEA42o9oVmSBFAhYqXUEFtOg/5aA1Vu6t2QE/xbjhjv2/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6122}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-use-callback-ref", "version": "0.0.0-20250116175529", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e0eff62114fd31f1ed9045fd10b7006d52798a1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-hKkt1k7SS0c0awvzj9E6NZD+ocnQiYSlgkryVP/DLoLaJJ15Fgq/Oe7eCAPJ1eYWv8dDLG3KGYdYS9udos/D/w==", "signatures": [{"sig": "MEUCIQDmZ8Qpn/zirSobHDMNFzjQxM3Fzfr4ouyHk1tDSGktpwIgEBTpPoEG+o0gM2mFMGLyLtU266iX8dReiANx3LwOcQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6107}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.1", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "f995fe9d7243fcc871b0eeef03ef37e92896805f", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-cP8si6SPhZ8FwiliIUG9oN0JAgdGvPvCpTUdoxYhtVAuwLnZjTdw0fTibXJibZjEeWSdgTY6Nq+aPPpeACp/ZA==", "signatures": [{"sig": "MEUCIBTyfyM2fSG8pIJh9KSUQ4uWodW48f+1JTWklPL0EkJuAiEAyLc84o8o3O8XoaP+YLd0aZkB3ixX3S2xXcieEYnxr1E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.2", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "bfa9101df6a544e5bb9e3b6d50456130e28a8a64", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ATr5AUnosRYbqu95dzWpwWRuSUoJKWhYi2eDKAe4HrKH+b+7DJvdT7lyUtI+PJ/6T2+hP5ZeZfC1ZmsoTnSipw==", "signatures": [{"sig": "MEUCIQDnEaFO9sd07842pyJXop/GY3FMZLJZeX9CUzhZmMyEXAIgfUNIZFkMfZljLndRcZwE2n2mI6btSwhjLi78R9aZvnk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.3", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "c9a41b5ff923d72a0ad0454c416faf568a40f670", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-AvaQbizQWtPQlX6LURB2aaP6cvyZoti1aJn9QCoIxRM5rTuo6nhRDhtFLzokhoWuNvAtxcnmggIbJtCINn/36w==", "signatures": [{"sig": "MEUCIQCJUslIkUmLeZ0sa3b7WKlxJBCrpohx5+FSh0RLbchiTwIgNZmod1RLom1I1jhmxjTa/2sJ7PkhNQQgsGqnm93C1v4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.4", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "bc3ed3fcfcfc911ac70fd2cf80acdd45f9b1e607", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-HcyIJQe5CnYRwjOkz+yfF1/xMoU+tCpwIkRzKA8xUProUs45AMTUMx8kzSI7tiFoBlwOcivPnndirwKkb4Sy+g==", "signatures": [{"sig": "MEYCIQCx6k8IcxriRu4tpc+TEbfM1yez3eR4dwCKvnQdpelxfgIhANetrWZcL+Wnh3pA9lfk0okNTBA6ZcNLrLH3rM20fUJQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.5", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "4b16314f493be83f41ae0d57d98a6ba88c4d7887", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-td0rkdUSdVfTGNV/pBSNYZGfo5BBsrbB7eDtWemMyyL9VJ0AaRwjvAcvyr4pRRx/HxpqlfucK9WCBKScycgb/g==", "signatures": [{"sig": "MEYCIQDV8JUmgvxVqO1y+OgX+YOkXhQz03fK/EWrLl0EDEwSVwIhAPdu8KkN6eKonJtcGk7uytqkYpHAYGSvhEIyMJkZAlKE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.6", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "5d6da7be75f2a7396324e61b7ededdb29c2e39f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-8QIHB4gjtfBV6dGX7NXcfLeP1mpEKHTXw5zZ6VVRGaX+VfoKL2D7nmqWz0KvTbN7/q8Z5DWlIX983ya1ttmsIA==", "signatures": [{"sig": "MEUCIQChYu3VBEzYBvW3BBunHwV+ftk73Q0xpCGX2UJgblT26QIgUePsInsWGnq3fHwHULkg8embuAyskyHzJvumQWp2T3g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.7", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "61c25a5b7f407065f4a064b15539ceab6301b181", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-W8bEpzZ7C11Bs174xvYgnzEp0bAoolhNllab6myIWl1bHfl8hS2P9b5/8u0vuRWZQ6x+vhWni8xHloAER5upuA==", "signatures": [{"sig": "MEQCIC520IoBR3vgwcZOeVWPiXET3YDCkzfyOYdnOP8GfrXsAiAWhAjRMgLF8HnhVwvozkwNFUNiadMSeqwXqSiCtT0PQQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6488}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.8", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "d8b499aae6027bb777b664be888e5275c309b8d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-xhrRnBovPV/9rz3go0Vo6sSY2//KujdW0TjhL3KdhYqmjPY4OmOExTZJ0dgbVWVr3WdVstBlcoo8jjP3e2+KHw==", "signatures": [{"sig": "MEQCIFelgpiF97B1A8v2t2SFS8ZG7aHQDg6osSCe8RAhjna5AiBNBx7AS+Ml8Krv1EVldfF74Y6nJejKL8B2Uiql/6Zbew==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6879}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1-rc.9", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "ab298757ee498860a9263f4779a71740d22ff6b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-IT+aYEns42HQ8FASZjPAvNKYZYkwyW1KR5Y7vHGxVRTxUEFSk7RbJIlCDgf3btcjl6O78wCLvRenB5xL/UhVXA==", "signatures": [{"sig": "MEQCIBxpsa8uuGn24cIhhOE4TALwkUq9Nh/I2xQLGb2GSP98AiBJjE5Q9TGyzEp0LgFizyvxIaW8MZIWzqXntlaGPwACMA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6879}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-use-callback-ref", "version": "1.1.1", "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "62a4dba8b3255fdc5cc7787faeac1c6e4cc58d40", "integrity": "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg==", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz", "fileCount": 8, "unpackedSize": 6846, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCb/YTUW/cgeKQaCWWRzA0b4dv2cY36jTlXRa7OfVqfIwIhAN0G8wPFISucFpd6e3Wx2dKxlGBDZlZCdRFhp5WhxggD"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:19.398Z", "cachedAt": 1747660589400}