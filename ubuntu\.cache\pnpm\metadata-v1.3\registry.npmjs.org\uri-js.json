{"name": "uri-js", "dist-tags": {"latest": "4.4.1"}, "versions": {"1.4.0": {"name": "uri-js", "version": "1.4.0", "dist": {"shasum": "b4dba71e0e4e4d2e6aed233be639938634462959", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-1.4.0.tgz", "integrity": "sha512-LXtnuJm7EYOxYIde9xoQa2TZWFdPjZHQ+uvMYPEvAaNk1Hq1nH0NqrU7Sk0sH1PBvcSZoZZFCZdIH2hLeB9bgQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFoE3+KOVQ+Da9dGk2zu7n5Y8pBe971FyFp2v+GAze/gAiANUkHq1TdDiGCP0kCHaGmArwRWZx0M34S9v9+u5hMxQA=="}]}, "engines": {"node": "*"}}, "1.4.2": {"name": "uri-js", "version": "1.4.2", "dist": {"shasum": "b5d67221edc6b4be269146ee34082876ed729bd0", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-1.4.2.tgz", "integrity": "sha512-qt3ta3U2TH9hPzzf9NacVVsz6US29SOyf8M6ovisjDM9poyWsJqbC7BnvJ1n6IGfSU7bSwH4AXZ6BbI7wOcDAg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCeczvr4DFf6D07ZAioch0GvR1rEzdAwkZAikLRqoem+AIhAKW9wgUfWWrOmvtlzLbnqyJaLRAmIubKhE8o2RFDMRkq"}]}, "engines": {"node": "*"}}, "2.0.0": {"name": "uri-js", "version": "2.0.0", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "b94dbf7bef8780722826a3049808e0e07dfd30fa", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.0.0.tgz", "integrity": "sha512-fjfo8fIlHQk77HsBnS3cF5z64ynqp4TRKFL++BxqKr6vKCJ1nahp+g0AkSqdmzPjaN+svRIl+Mrm5ebvJdaYDw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC62BZ7prvfiGkmgMx0vXUcCKxHTlXi6DEx2jIoW+AFqAIhAJrM+8FMtxO5p3OU9yG024IFdeHgMu/3skOPzx0g8kaM"}]}}, "2.1.0": {"name": "uri-js", "version": "2.1.0", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-rename": "0.0.3", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "82a70e24394061b7e4fdb4713a1d2a0fb3e65b66", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.1.0.tgz", "integrity": "sha512-RWHLpP/nEqJEfA9cb58jLnQEbG/SX4CZ6Kl8oJe7c9DHY5gK2VA0qKkPJ00Nd75E/c8CYhZojohMhFqjuhh8vA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDc6vB/kcBykjb4l35AJfeKpYfm0hrigSwXDAAAmRXFKAIhANapV2Nm0AGJ1AQtdTKHks4f9EDDt2q9SztmkpxhlPfO"}]}}, "2.1.1": {"name": "uri-js", "version": "2.1.1", "devDependencies": {"api-closure-compiler": "^1.0.5", "grunt": "^0.4.5", "grunt-closure-compiler-build": "^1.0.1", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-rename": "0.0.3", "grunt-typescript": "^0.6.2", "mocha": "^2.2.5", "mocha-qunit-ui": "^0.1.2"}, "directories": {"test": "tests"}, "dist": {"shasum": "eb3f8505f468969bf92cb79ce8ceaac2ed667661", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-2.1.1.tgz", "integrity": "sha512-Mzott4fGJhw0z8q6P+QrQzijicqmfat55MdxD48gN9yXe8gBOGhcvc2hRVyNXzFa9xzCAep6CqWS6pbyajqE1g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCYgT8dPoWy+ROhyW3nxS5AZVKOzHoF2fDQDKw2rWWAoAIhAIJ614hDpMOKk5Xfsdm+l7mokgUYWGkJBM4ww2hhju5t"}]}}, "3.0.0": {"name": "uri-js", "version": "3.0.0", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"shasum": "eb72ada63c666c863aa9b622f03fbdefa0bbd21e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.0.tgz", "integrity": "sha512-y/zj7MThqSBVhsDTz9nV3xGGsHm39Jk8oCTk4q+tKVfx+7iUfm3xHtyw91D1/oBjrmu4vUT4sdlJ14BdFxy27g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB/+ROAxyPvKh7PyoT9spuXygD2OXC6K8PJrm1WhrnUtAiB6ij6jZs7ZZUqlfSD/D4UGFn3oDpu//VcAPn71yMRpAA=="}]}}, "3.0.1": {"name": "uri-js", "version": "3.0.1", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"shasum": "cde6cccb3da47df9e4a0118f17d2a1fef4a1f69c", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.1.tgz", "integrity": "sha512-gstDj+dNkBLhfSgYms7JbQ17O/WW1rol4UK/tibpRd1v9uyvyuSQLptDU7niq3lyo+swE+hyYD2DbK6WjKIThw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC8/zEyXdEcZThe2d79cRwTC+HkrcTQ2Fwozc22VfhvjQIgEifiD+aqmv1l1FqFHZseHx98yLvYguSr4wmWZELVylI="}]}}, "3.0.2": {"name": "uri-js", "version": "3.0.2", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.2.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"shasum": "f90b858507f81dea4dcfbb3c4c3dbfa2b557faaa", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-3.0.2.tgz", "integrity": "sha512-SoboS4c924cg+wR2vxl8fospPPli3ZmVPIkRpJEWcrGIPeE8Tr3m9zNIyjYKn9YlF8EgiXQDCy3XVZxSFNjh8A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEsPhfnTZP2VgCEQ66IP+0p/bkEXZPy0TVy2x9Wkt6veAiEAqy+s+xu/oMR/0ueDTRI+p8t0So8OPo5/1+rth2nTlMw="}]}}, "4.2.0": {"name": "uri-js", "version": "4.2.0", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.0", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-WxtXcqX2yRvv66qyWxgYWcVl6hKjjrcqVnn+X2l5D98c3MfThsWmvg4j+FZGe4J1hdScE+HzcaFRmrMovAN4KA==", "shasum": "8c41301caaa13a71c5bafa74f9e7bf5a832f9f0c", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.0.tgz", "fileCount": 58, "unpackedSize": 532951, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpECWhNQy4y61HuMhbWlNX9O+qgnqmCSsThR1x1+9BdAIgcdBdCHC7cbwPV9CID4v/0uLBU/5TorkupO17i865fIU="}]}}, "4.2.1": {"name": "uri-js", "version": "4.2.1", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-jpKCA3HjsBfSDOEgxRDAxQCNyHfCPSbq57PqCkd3gAyBuPb3IWxw54EHncqESznIdqSetHfw3D7ylThu2Kcc9A==", "shasum": "4595a80a51f356164e22970df64c7abd6ade9850", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.1.tgz", "fileCount": 58, "unpackedSize": 535794, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB23tsgKaGTsvHKSsMGTo9GRYe/Fie4lZEsvfXGvFepPAiAF6DsL/8O1jKWrOAnzcYnbHnZZXSL5mvImXUWXPsNzyA=="}]}}, "4.2.2": {"name": "uri-js", "version": "4.2.2", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-KY9Frmirql91X2Qgjry0Wd4Y+YTdrdZheS8TFwvkbLWf/G5KNJDCh6pKL5OZctEW4+0Baa5idK2ZQuELRwPznQ==", "shasum": "94c540e1ff772956e2299507c010aea6c8838eb0", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.2.2.tgz", "fileCount": 58, "unpackedSize": 533198, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCIYyCRA9TVsSAnZWagAAa6oP/1XTWz8ftTU+nUe6aXcp\n4iluilj92MViWAwt5jsrv9f0DTowMdg0zKL7sG0rZJfXwgThlvKjqO+vEQRb\nLuf7bCtmHySHphXmWdFysAc0LHRb3eJiDLi8QVtDCzE8501X6F/3HGItBxw3\nzSsd31TFBl6m356HH1nRb/Eiz8W0MqWbJ1/T6ixU4PbUz9DxRx5BFxGTQlNM\nEdFiBR4JWTcCRov+nCmubeUhf+vDwosLGpcdEZePywgaDi3WzX8PLhBksZbf\n42ODIL0OlmHhcJeSmOgnZ4hJJVmu+59mOlLM8HcV46rp/LutEItDFOG90u4R\nDUds8cvcvY8HQ6/E3iNrQS9cL6bKOlUg0OYW6cxM9SzIWYp5awSqKa/ru1mJ\n0sAP17YYOA0dvY3RrkktCV+lthw8nmqYxL4vmL0vGR2hh5KEktSltGTI++nA\nc+0uEt559t2RCP6Of6CCsYeJ5JXzR5ncMadCuTyW1w2JCGKVoZq/bIQru7su\nvY1PxvjMh/fN/hXSa8kAd1uATLyIH9oVd3yJuRIj6zDEAcpimmqslwRX0PlB\nBZRaPS2xW++RfjnLRbA9z6wVqiXWwgR8WOPbgGTJgH9kj5T7MBLxtZd8Zj80\n1uD+BqG8iUFM4b+mtri7VlU3cWw0iVnfDwgGdleRhQ7ImEH4sJO3x5pBJGbv\n3jS9\r\n=55z0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDzVuHlxmX9acm216qkyptErKakM7Zqd6nYS+cRgN9CfgIgNqNKn/Fzmi6T5CPy7FM+Tr+4+2gulwIIXDSWM8kh6aM="}]}}, "4.3.0": {"name": "uri-js", "version": "4.3.0", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-Q9Q9RlMM08eWfdPPmDDrXd8Ny3R1sY/DaRDR2zTPPneJ6GYiLx3++fPiZobv49ovkYAnHl/P72Ie3HWXIRVVYA==", "shasum": "e16cb9ef7b4036d74be59dc7342258e6f1aca20e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.3.0.tgz", "fileCount": 40, "unpackedSize": 421345, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfSv7ZCRA9TVsSAnZWagAARhMP/1k3ermDhttCNsbXd4lK\nxb986+aFwxnjD7siHnQJYGLDyj6iQLuo9oZ0DTTsIt6nHC8evgDOsBsaiamV\nFv41MtC2e97KLTIAbI9kAcsvvf4npNrAwr54ZJ6H/kfyLvRibhfTTmVNDJ4r\nDuosU++iOoO2IGzTWeM4/llbUP3HSMxI+fJsMKawnKhNPFS08OR7eEzr9gGI\nuSfFetHjCziWo2VubTWi1zI2H43sS61zLseaIGHhX403OKndC/LeWXVMzu6l\nCa798t/osiqufMiJcOf0AjxJLul2LGS8j5KQtVvzg7UF5O8e5F6NEv6KuvQY\nz4jj4TD/w+L65bXwM+27b0mrILNIukCXW4g5bQsfSNuOb819e5dXCzfo/YS/\nbX7GsYcegaDnny3rDPAUQU44RV7Cxe3l2n7IMLFwEULuQeUcCVgIOMPSpjQW\njkJESCdD36ORF2k48dKMJwyjcwArW2POBsTJDO8sW0H2/GvZoDt6UhBIEGlK\nUpUMGBXhfgLnqAFyAXk+/lswTizgJGSZTt+q9xLa3M/kMjOgaZCjXeEbhfYn\n8yjzwU5DDLDkRRLIPI1k0RgDLSOi+EV6JOnAXUdCa5TZr4wA/EBfRqw3k+7e\n1ar4ATyJ7COOOvG1fJQVcAidG7FYiZGcVbHJcgUd4B26Q99qiZNoSHfNpt+L\n976P\r\n=oosy\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIC5q2Be7KKh7EbeBNH5iJYy1P0ST8jt7AqVYzCRhqlImAiB4iipCphf85P20x5O5WHCrhuiLbzN4b1r2ztDZr1eDkw=="}]}}, "4.4.0": {"name": "uri-js", "version": "4.4.0", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^3.2.0", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-B0yRTzYdUCCn9n+F4+Gh4yIDtMQcaJsmYBDsTSG8g/OejKBodLQ2IHfN3bM7jUsRXndopT7OIXWdYqc1fjmV6g==", "shasum": "aa714261de793e8a82347a7bcc9ce74e86f28602", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.0.tgz", "fileCount": 46, "unpackedSize": 435804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfS/hFCRA9TVsSAnZWagAANNsP/3GypTnT3GjWWn+gLvQU\nGekN0Eo+0cz0VPVVyCI72fFUgSN7txFow7/4hRNjkmu8A1hIA9hw2FehonOy\nkf5eKMyGWxAKc6/UcfWtAgdUYRt66rVq2qqJHoHZyocCOGXw32aQTwBtz0H1\nBKzGZpPeXWjz1Y42PFqhWXBNxOG9Zyq0RH24pG3aOoNxky2tLrygAwG1keT1\nXxN4dzPjJEhN9uN9Wig/VCy7fM9L8MFrKducFNpjYem8IGIDfs0uhllGiiYI\nM6H1VTztAJRDAKPzg41Zt/iCMiGMKIQfARP5rvhMFQen+HjFETIXdomQzB5f\ndAhHIoMJbpX9kM0V0fuKVx0lVxtiIuqzqxVaHZJ1qFNTPSQSEPZPX/KyBr2E\nwiQKG7Bn2JMZby3c0Kb2t1ocNuXt1FEC4y+pkl1KRhLQ4S0a9gdpSfS+1lST\nUFqypQ27JRepjb+N4TqUFy1kjorTm7sFkzk4FF7RKL6raFeulcYLGB832XDk\n7Wt2sQR3MyvzvqYzyk2P52MEn4OGVLR5WowCjfqU6bpvOe8uwMsfHuWcA4iJ\n0yBJMZr7lr0bcvMNN7jSiUt1L7wSBKqLjvZ9h17FUqewshlWTKPzZIQE4+KL\nlQff98Jd8A28bmrCW8IpHVfBkxpsVxuyqLxVjHMbNID8LlnByNndfEMZKQSX\nYY4+\r\n=qU9S\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUPBtWJ2wUxInOUONbxRA6+rtZq36GuYFxfTCUPvrd+AIhAPeHkvA5RZLJ17Joa24sxcHEUXM3vmn0gn9Wv55IhBgd"}]}}, "4.4.1": {"name": "uri-js", "version": "4.4.1", "dependencies": {"punycode": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-preset-latest": "^6.24.1", "mocha": "^8.2.1", "mocha-qunit-ui": "^0.1.3", "rollup": "^0.41.6", "rollup-plugin-babel": "^2.7.1", "rollup-plugin-node-resolve": "^2.0.0", "sorcery": "^0.10.0", "typescript": "^2.8.1", "uglify-js": "^2.8.14"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "shasum": "9b1a52595225859e55f669d928f88c6c57f2a77e", "tarball": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "fileCount": 46, "unpackedSize": 469879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+k2hCRA9TVsSAnZWagAA34gQAIBCHrX9EiMnQ1y6Q/lA\nnH3L0pTQb47BwM+2z4HPTX/fzIMryYGjGTlr5M1e1ljQSiaIVUygAxqqD5Tg\nTJLguj9k4nhKsmsHFKmCzTkFK1R7foE/vhS+EZGWYc/bWkE6oAQ8+AX3Q53j\nP2HnbuK/kqWtfoskY43JiuRKS9AU39e03SnmZz3uUDCV/t+lF+cdBlzNAjG3\nesW3dV2ynyOBkouW7WolPYSULti6YXquvpgz1C2QGAkyxXKOJMf7jlYHN7wh\nId1MAR92XuzBgu2sYNzIxr6Qhd/Moi/1yb++otrm90OEC1wBgoXPbNsBzJSx\ndIwWbJ0DIjDnqAhVRzZ90MfC6qxRuCImpDd8feZ+nb343oUb8ofEME/hUG4Q\nPvAwKM8n+2BjNiwX2TyoGvAfuch6GwBrOUcMZM5SMW45ocEXZgHNPD2uIek9\nBMiaC1Ew9h97NDwi1SsB7HMBhUEQhT9cbz3ojeLE3GKfOmUBs4JdwbYk1n3+\nUfMhwoYvhqRugjUbFXpoBRbFBXWYPlEIR5GLcIozABblfuPLgmndZ0kefT6A\nW+WMbezye9qJPb2ARumFOfand+nHea4Grpa87cUzM/Pe+uzs7v9JflFdXDjP\nlymzW91jAE09aIh9rKaQHZCEZA/PeV66BzGPaoDb1sPS7AlMXEpLlzy5K/3B\nGWvW\r\n=SXo/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCMs1qpyJpeN93ls+gFlrcOf7srUKNr2MjVpWe51VBgKAIhAJctn36ZpbANRujkBDQxFeKL1FoBpLuff6Dz7QOvxBh3"}]}}}, "modified": "2023-06-22T16:34:03.088Z", "cachedAt": 1747660590229}