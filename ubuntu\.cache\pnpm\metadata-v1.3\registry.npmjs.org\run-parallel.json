{"name": "run-parallel", "dist-tags": {"latest": "1.2.0"}, "versions": {"0.1.0": {"name": "run-parallel", "version": "0.1.0", "devDependencies": {"tape": "^2.12.3"}, "dist": {"shasum": "e947a17a3bffae5a088f8d299b3f4f446b63e0c4", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.1.0.tgz", "integrity": "sha512-zwyw1YEOguOQVFzto6FTx1PdeisMD2L5sEMfG5dzgcrEejaYUnNsSQ6OkaILDZ6tqzZRatc8cSnS1gC1PSwJHw==", "signatures": [{"sig": "MEYCIQDwx24jFePMAWcoWlBSNSrYyAk9973b5LWWjjMaGS5NlAIhAJZz6c0Gr5A+GUN2rLDV3Nme5EC2azNbY6EYXAqoEv+t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "run-parallel", "version": "0.1.1", "devDependencies": {"tape": "^2.12.3"}, "dist": {"shasum": "17e393e7387f0a35e187a45b527549deea7307d3", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.1.1.tgz", "integrity": "sha512-6fNlVhrwHZyJwioEaZ5GWTXNXh72qnTeDe7XLiUAr7jJ0V/c8u6KtyaoiUxhm+4kU2wgnTtjIm7Zwryn63iANw==", "signatures": [{"sig": "MEQCIC6Xofi9MbMcz3Zq2PCuRxPwAbBooXJ4XdPWaF3WF71+AiAAgiSN1A35mImcvLN5mEZ6F9wUHCAJUTceFflEeJS9gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "run-parallel", "version": "0.2.0", "devDependencies": {"tape": "^2.12.3"}, "dist": {"shasum": "5d018e89691c1af8bf150973b9a1f22e4cd04797", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.2.0.tgz", "integrity": "sha512-E7MJk7bwP3KPfq1ldjb2Jh4ULV7OO/a23IpdqegklThUEwmBDIK+gDxXqBoMBwgjNHh0uItUwUf6da51V9gpWw==", "signatures": [{"sig": "MEQCIE3drMfHFSrNdT3l9nSZQ8MT5GpS9P8N1W3JUXJ9SMGUAiBItWCDRPuZz2FED7vCsWOfYmqmfVY2hIjiGJVZDZvZ0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "run-parallel", "version": "0.3.0", "devDependencies": {"tape": "^2.12.3"}, "dist": {"shasum": "2912228d34e1e370c70b9f794036de1c3eeae715", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-0.3.0.tgz", "integrity": "sha512-fYp5n5QFbxsSmGtq+e//fM3pKw0q+axkoGh3VH7REBMAP2pLpU66VHerQz/nReJbiIjNQVpfDfI5VSO15G43fQ==", "signatures": [{"sig": "MEYCIQClKlmBdXr+KY9Hmx3deb6dZg3uUnwoBu284XsUneolSwIhALjOzgNU4VdFuz7LOvHexWsui7Omks4Vd0rr4WoNBWm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "run-parallel", "version": "1.0.0", "devDependencies": {"tape": "^2.12.3"}, "dist": {"shasum": "1dd0787f994d17f8d1bbe7e910e0cc3036800cbc", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.0.0.tgz", "integrity": "sha512-ye3bjudEvqU0nQxz/xM6Mz+RU8uo+pm8vdTD3AwuAQg+/hF1fWV76GVbXjt1wz9hFm8uysmZITNf+UTunc/m3A==", "signatures": [{"sig": "MEQCICWgZzsj7TE691oGIg2DB1aYV5w4/m4Et4RX+uRfIceRAiBslzag5q2Sh+5O2LyLeYre5KV/ZSlGxuTWTOy1i3UHZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "run-parallel", "version": "1.1.0", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^2.12.3", "standard": "^3.2.0"}, "dist": {"shasum": "6c51c3b7e06400a39f38e34aa1e76463a015e67f", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.0.tgz", "integrity": "sha512-4cDIfL6b98ijT6xlVcPFXNuqF2ZWWZ6xuPrK+dMvqSxv1/Mj+OncDXWnAl2+O9gnLmormLIsLvfhLs/c0e1v9Q==", "signatures": [{"sig": "MEQCIEZWKp4A454ergL7p2l3wCPuoneCz+QHBRFjUrogyqYzAiBneLsGr9t3x9jq5m3AeszyxKsW6xyyA/uv0ucQ8NYN9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "run-parallel", "version": "1.1.1", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "standard": "^3.2.0"}, "dist": {"shasum": "043c1f40e5ea94485f6858b79c6ca08254d0720e", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.1.tgz", "integrity": "sha512-5IdyV87k+RyYENG9GClriqfCUh6IlV2//vWpFjSYVRdUe8jB+0B4W/79znBYqlPSnYh/N0cABrRNZhgPFhr4kQ==", "signatures": [{"sig": "MEQCIHN4mtzj1cPGOyJjrejoyBNqotQFV2pPb0tr+rbOf8sjAiBtaAUAWoBd2EoWSNlkj4wBrDhOzJEVJe54O4X4AhSA/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "run-parallel", "version": "1.1.2", "dependencies": {"dezalgo": "^1.0.1"}, "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}, "dist": {"shasum": "199a63b1bbf76dbce26f3f5499e5e4f993dae8c6", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.2.tgz", "integrity": "sha512-ffdoX/IydCSRCkgctu8EYTuoaX1iY7TpQ2bXIHsADV92Vyk6JroqOJhjk8BXFMfMvj/TpjsxEzFB5h+lRMN8Qw==", "signatures": [{"sig": "MEUCIQCZ/zyFm6B79vxPcw+YtHyVcl29K/DeK6pq4A81ik2aGwIgboTLaO4nyZhVLbHYVnxkKB4dLUpoVB0niFhn8sPo/cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "run-parallel", "version": "1.1.3", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}, "dist": {"shasum": "c8ffa1a42bc05d4b526878382f6860a1baef629b", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.3.tgz", "integrity": "sha512-9+FJGKKcZFJgKct0f/1MCU6sTkNoq80G5rPfGMmRgUSZserzji4hdIrRMmFKXh8sDDtuYeNRnNf+i0o2WzrLPg==", "signatures": [{"sig": "MEUCIQDjAalHyVgIi9hp/iuAHx0nRL6j0gyOe4W6cCsRMio3zwIgVj92UkwURuzqO6+zg0qbwxm4Tvo/hOsaNNEKuBc96pM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "run-parallel", "version": "1.1.4", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^4.3.2"}, "dist": {"shasum": "b4fc05ab632a5e767bdd691b4aabe7b8acc9abb2", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.4.tgz", "integrity": "sha512-LTel3I9NB0BJuhORiQRA/K5IuA25bgW1s50hA9miB0eHjcIpKaAjEtXjCDrTBowP8a+zrfQC8Dginygkp3GEDw==", "signatures": [{"sig": "MEYCIQCLiq+SYWk96qc2GyFXey1vqcZ9c90yT3jW5fDp0/0c2AIhAMGKJVnF7G8yqvGK5aTNK0R5r1KHmVNaeLKdKXSI48QS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.5": {"name": "run-parallel", "version": "1.1.5", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^6.0.5"}, "dist": {"shasum": "feba6ee55e0941ad7925ae520af633c43ad2c52c", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.5.tgz", "integrity": "sha512-KHFkb3BoRAhWrtSyHNOMebDmzTITDgMx+CDtO4uAT/O5Omr4hHxHaJSU1XaQKim+2hhkKi2NejPCdpkJBy/Dlg==", "signatures": [{"sig": "MEYCIQCBJkKOk1B8UmSRdMy+gtW4lMHT28uMyYbJXdqJyPA7ZAIhANPk4CySlfpekTxelTLuivOzPbhJTX4qQukaHSDe33Kj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.6": {"name": "run-parallel", "version": "1.1.6", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "^6.0.5"}, "dist": {"shasum": "29003c9a2163e01e2d2dfc90575f2c6c1d61a039", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.6.tgz", "integrity": "sha512-XmkpA2hWubG33ZcSAtFujmcsg4dPy9u1nwB0AtuHNEVF6SoDpZO2ldD24PS0Z5l8/C/sdaztIqPpeLzMugG7/A==", "signatures": [{"sig": "MEUCIGpVjhe7Twf7VBNJPs6oTH5LrRrhVcOztOHSxSu7CgHeAiEA7FvglzxiTRLUy6Tlz5Y6UIKIk85YV3DIgrvBmF5NUWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.7": {"name": "run-parallel", "version": "1.1.7", "devDependencies": {"tape": "^4.0.0", "zuul": "^3.1.0", "standard": "*"}, "dist": {"shasum": "d8f40854b9e19d18c2e0e70180cc05cfc86b650f", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.7.tgz", "fileCount": 11, "integrity": "sha512-nB641a6enJOh0fdsFHR9SiVCiOlAyjMplImDdjV3kWCzJZw9rwzvGwmpGuPmfX//Yxblh0pkzPcFcxA81iwmxA==", "signatures": [{"sig": "MEYCIQDP1E5XTLEGJH9D1IUA5/F4Otg5MdbmuFzyAt8Wq+qUDAIhAJS84PhfKqDhd/hAi8n+JqHbbHEHh0Yu0d4+ILVYQG2F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11231}}, "1.1.8": {"name": "run-parallel", "version": "1.1.8", "devDependencies": {"tape": "^4.0.0", "airtap": "0.0.4", "standard": "*"}, "dist": {"shasum": "70e4e788f13a1ad9603254f6a2277f3843a5845c", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-e5t1NVhr5VWmD9V9U4KjjSGkf5w6CcTPgw11A3CfIvkkQxlAKzX3usPUp1NUQTmpOOjU+f9QRICU3tMbKwn9ZQ==", "signatures": [{"sig": "MEYCIQCcagpOgMx2LknBN20lyDERNSfdEfXwZJuWm68AwivQhgIhAO60NsJ6vghsAGP9yXUMDpF9WDoGqz2huvR2ATL7jBXn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10563}}, "1.1.9": {"name": "run-parallel", "version": "1.1.9", "devDependencies": {"tape": "^4.0.0", "airtap": "0.0.4", "standard": "*"}, "dist": {"shasum": "c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.9.tgz", "fileCount": 4, "integrity": "sha512-DEqnSRTDw/Tc3FXf49zedI638Z9onwUotBMiUFKmrO2sdFKIbXamXGQ3Axd4qgphxKB4kw/qP1w5kTxnfU1B9Q==", "signatures": [{"sig": "MEUCIQCI+sAPSevsnh6ATUcYJ2ZPks9eSkag3Tttfwo3mDqSrQIgfEyB6k3AoBiBd/bNKfk1C5IfXEIJLSq9CChMCeX4WhI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4ScfCRA9TVsSAnZWagAALFgP/1+INLnEF7wZhzCZYGgt\nAv4wmMOi+6e17OSm7MBketwTxmGs74yxTi/DGHAH7qNwy9s2lKbJMcLTG7Qz\nFfinjAr1E5LFA7fAr1qeexRxcMSEMKlOPm4V4dDx6WmW9JpJVSQ4LDUFhG1C\nJFrlpxjB6ppQ0sqE8UGq6Y2MiuLp1A9mwn3/9wkZ8rka/HxOO/nMIsaNrupE\nkchduhq33CWJwvOEXFEQlzgn+gthfQHpYvryNF0GEODIv+TI9dvXK9BZaTAN\n0cq/M4JGIoSFy4p1rCjus12mWrJSs1rRpMfSp3rjntZjGDJWS0KBdkDLtcfn\nSA59J3S2WcJ6S6JeMqa16siSj6egY3b7J8TIkuUay+TcaYT9SjDK4AmmE5wN\newLKNnNu3O+sHXFw29IDMlSVPebimBtbkc1SElfNc5U9MeE+tKroXhQ4NYcs\nUz3gJR+GHt2eU4+EWS2UlxNbJHJ+DVOFF8QmJoT3Y7hopaqHrYYdLhYDCwl6\nU6AAVIt10Us+5nGZS0WmGrw/P+QQL30I7p58UFmJipg9PZNaEIgJRBApynky\nDMkDE6ICS82Pu/jv3d7enWE42GMgdc7mh+cp8ZQHkqWAD5YxGoWRIJgt3wAx\nky+bAmtR1mV8ZoqiCaDf+EAPXazquX/8SHzad41n3RxyB2qktQbYCDE2Z3z6\naS8I\r\n=A5eY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.10": {"name": "run-parallel", "version": "1.1.10", "devDependencies": {"tape": "^5.0.1", "airtap": "^3.0.0", "standard": "*"}, "dist": {"shasum": "60a51b2ae836636c81377df16cb107351bcd13ef", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.1.10.tgz", "fileCount": 4, "integrity": "sha512-zb/1OuZ6flOlH6tQyMPUrE3x3Ulxjlo9WIVXR4yVYi4H9UXQaeIsPbLn2R3O3vQCnDKkAl2qHiuocKKX4Tz/Sw==", "signatures": [{"sig": "MEUCIQDKWqR2lnCiRDkDymfFpFnwuhQOXTC1J9/whLILvjDQSQIgQPT43vYlxB4GjhSpjQXG2AHimk8xBG7PZl4CtukkPj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfl5a2CRA9TVsSAnZWagAA5o0P/Ai6QVS0yTu5gcSlMs3O\nlOPX4izXHJkvgiLiWngcjct+m7n8fYxV631L2RPRFz2DnJgszlq5/l4/3jNe\nkLHSYMhVXD2r7HEPsVyuR6THf+X6LCssllAu7VN3buhhQGDZqO32vU91+Iuj\n22StAWkKxvCsbkwGiT4hqvdLW/COKrFIevwfoTm5PbTxjOMXYZOGI4K0NQy1\nTXGOeuAEQxf+O6S5xJpBa7rb5e4gyVOfiW+NbadMvu/v2Xx+xh7AIAOXqtFv\n89E1e+vI7YxOcsG8IOsOf4+XYAUttzd036oIUvUhvdBVq7viZrlcVEpByUP4\nnNzDcntrqrg8xz9mQECcFuH+fu7aHdXrPuPdnMoacGvk2+LuR2/PhB+x3+jG\ngw/ATk3wpk8lI0b68opRRSb7GLISCUaDhrlhWSmwbslTu7MnIta+WWX3LdSq\nMTVXJS0NGuXNnLZGBWdrvcrfL93RCBKlqolfs8COG88bmFqXoOPGrvoeRYvt\n2q6hfqG1VlPyfb8PLDjm1k09EfQlPjOfgblm5zhS8EmSZJhC+Mak/bgxNxwl\nF6n5eml5PTtst0y2nwsKSu2lZigfQkL4jo7VbSzPbZSq6KQo0fXLzETHSK4k\nLURrYjswlQ2tIcP15m2+OpOye0BhEIZdoavtbDgpF045C+r48GbOaMGkgncj\nJR6o\r\n=xfxk\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": [{"url": "https://github.com/sponsors/feross", "type": "github"}, {"url": "https://www.patreon.com/feross", "type": "patreon"}, {"url": "https://feross.org/support", "type": "consulting"}]}, "1.2.0": {"name": "run-parallel", "version": "1.2.0", "dependencies": {"queue-microtask": "^1.2.2"}, "devDependencies": {"tape": "^5.0.1", "airtap": "^3.0.0", "standard": "*"}, "dist": {"shasum": "66d1368da7bdf921eb9d95bd1a9229e7f21a43ee", "tarball": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "fileCount": 4, "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "signatures": [{"sig": "MEUCIQCgDvi2EAyyPczvNERNPxsJKEqi5qRckS+/St3IWvlt/AIgSoKR0VNW7NejbLX+LOEMP4zuiZxRhph0kvq0UrVime8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI0vACRA9TVsSAnZWagAAYFIQAJv2prhLf/SBLhAXo/8h\n4if5q/RPTJqUXPrB+Egulc3R88y1da4SYM7QmObhlb8v0uSAMDH2JNFB+tPm\nPxpruymc+bfpzXVaxM5qkNnNo5srghc5P5RyJi6E98T4cvbg8V0RlFMOMBCl\nWuVl8W6FLDsezAI4H0IibYptzph3xy7Ptq1I9iaKAQT/UIaDi+DmtfY3jvjQ\nlfdsG4zf5kY1LkmJPmzNNLMp91Q753tUBb2SOaTo8Xe0fDB9l3UjB+yno2Wj\ny+NPpxAuF34l4aXsKHKs8B3n4nehoBSuZlScrXjm14aGokJlO9vONA+9yDR4\n6SHH/4F4Fi0C0Wk3BG7gwA81NnF/FofZxjf6if8Aza+KdyfDllI47NgEvlc3\nf9N8AQVfxDYSnVggXeVRu0gLKSwysgbHKrD3eUVRU+5GL0QlRulVHb0IGq/p\n+zZN5tpErop9ghKI8BTkPWlQAtaQr3mCrQzflroH13E+GdWftMdirn3jMNmd\nL9UvChuu0no97ZtCu2Tq1ItZOtfqHRuFwWTYDb8ZzflhjFpvtm1k6Ko8sXUy\nLGDQLvesDVrNXLTpqLaTinZFSUnXhL2DAUMr50QidhvT/ePR22yb7Gq4d63x\nUasyPiePaqGErq0jGmTg5v7w4oMc5iPD76l/g2Vo82vfK+kfi8qVs1hguTAK\nNxX4\r\n=NyGQ\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": [{"url": "https://github.com/sponsors/feross", "type": "github"}, {"url": "https://www.patreon.com/feross", "type": "patreon"}, {"url": "https://feross.org/support", "type": "consulting"}]}}, "modified": "2025-01-02T08:58:07.736Z", "cachedAt": 1747660591815}