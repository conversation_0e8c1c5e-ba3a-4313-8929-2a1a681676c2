{"name": "globals", "dist-tags": {"latest": "16.1.0"}, "versions": {"0.1.0": {"name": "globals", "version": "0.1.0", "devDependencies": {"mocha": "~1.x"}, "dist": {"shasum": "78bf585dc4c9587372a6a10143f1caaaccf595e4", "tarball": "https://registry.npmjs.org/globals/-/globals-0.1.0.tgz", "integrity": "sha512-OSe8YgBdgnKJUL7cQlPPX7kdvfVEwE5Fxg8agxU63W8nMyqH8fgI6Fdw4SDVg6AqjlrjwGewo/cxGcXG4m+pqw==", "signatures": [{"sig": "MEMCIB6j5LJnuor8SMcUCu6Rhk2D3VBlfccyvVzjNRX/lZ7IAh94cJs8G3ahqNm/MV08Bz9MaGDTUikjv+7aOpUMtXyi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.1": {"name": "globals", "version": "0.1.1", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "07a636a461e2f7d7d1e6f787205c68f121bbfa81", "tarball": "https://registry.npmjs.org/globals/-/globals-0.1.1.tgz", "integrity": "sha512-cM/gJWP/Jjf8D0lTNT/jr8QvpTukoUVHeVPL5FHv6QQXzAj6Fx2VOJL+gCIP8TjRvw8V55Mnk/86VzU6LiBZcQ==", "signatures": [{"sig": "MEUCIQDU99Nwh66bDJwpyTCq7Fz1FjjfRuuam6p9av0cnu89IgIgLzOdBC+tZgpAjL4p0/zW5+9AMizIiphjGVtujJ3bwms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.0": {"name": "globals", "version": "1.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "a2809cf4079f35c1f95d5978eae4a71c1e12554f", "tarball": "https://registry.npmjs.org/globals/-/globals-1.0.0.tgz", "integrity": "sha512-0fawhO6j7nfHX04UxK1kCbA4YCx+bbDkcYfY2bj+6n4P5PpWPcH2ikNSwbXhvAvPSZRiMZF9w7nJPqun0sPoXw==", "signatures": [{"sig": "MEUCIGlJdtYiHxQdafEXE6a03EO6dRQ72E+lgqEH5My6opHSAiEAl94wfcAIiuRHZwEEOL/+DqwQ3yQnASDKaUCJ53OSEN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "globals", "version": "2.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "034d937f10c8f331948b035640174d5cd1cf3817", "tarball": "https://registry.npmjs.org/globals/-/globals-2.0.0.tgz", "integrity": "sha512-PKBAtN5NAJaLPJ6cTZnEsdbXmuFO9UjnDSS/zHJI0IjC/CIldSO50mCMDFrB102XRZYpmqkQj+iYo4+u7qvgeA==", "signatures": [{"sig": "MEUCIQCxqIL9i3SXHbraw3quyMU+bnJNqaFMCUVuuOxBjfi4EgIgb6+QUFmkfyHC1jnSGG66iVm2hYhROObWZEq2KqpzvE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "globals", "version": "3.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "f617efffa8f31d18bf6034a7bb26e282de308946", "tarball": "https://registry.npmjs.org/globals/-/globals-3.0.0.tgz", "integrity": "sha512-nVaoWC1rZRerxH7fGVDpuhM+BXpDpv3s8tgJ3gyIwYX1/Om9XB+fd4cFbFjRiqI3BYpnBj643k9idd4DFDuimg==", "signatures": [{"sig": "MEQCIAfaecknOCuvGi34ifGajScsenJ8McuEfGq+bw6exIGrAiBj2KP08wR1w0PYKYaKMH+DHkmjX0Agsbsu/WhCMj+7+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "4.0.0": {"name": "globals", "version": "4.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "9a68887ae12f70858ae9372a02e1b87827bd56e3", "tarball": "https://registry.npmjs.org/globals/-/globals-4.0.0.tgz", "integrity": "sha512-LN4WtrMRYKc249yvP3JsTKZgb3RURuEOnkAASO6/xpsZMUqu8kpdptFrHVG9+LjdZ7YcGbzxz0wybwG7NDo1TQ==", "signatures": [{"sig": "MEUCIC6IXuZP7l3JWGyqXcuVJ2ialWHoyMNo6JRP29LUlsa+AiEAlK1/CS/63d2Kg2MvA5tYEh7ae9AhSR7/ztIv5BtxPN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "5.0.0": {"name": "globals", "version": "5.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "933dfb37459c6dec908deb0db65911b73628d24f", "tarball": "https://registry.npmjs.org/globals/-/globals-5.0.0.tgz", "integrity": "sha512-EWtWMYmVMkBsu8OuMIcIPMWuh5Frh0LIFdEuGTn6S+CR/guksnS4O65jtbmbuUTpXZXUaBCMBX7KXlc7tvkw0Q==", "signatures": [{"sig": "MEUCIQDKOoidkkMBUEOJFTuJELl5KbqJM0V0EXdzPPSywDyd3gIgPqfJIvAbO7eJVtC3QGnTJh878hGjzc/1r6gDIxnwRrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "5.1.0": {"name": "globals", "version": "5.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "4dcafe4e908d666bae086234bcb34ab553e5524e", "tarball": "https://registry.npmjs.org/globals/-/globals-5.1.0.tgz", "integrity": "sha512-sRYdTTRykvopO6HycPMdrh4vGYmTfsHnOkZQLeP0GKnZPcZdLdxTxPGJN7SbaWo1mZ2BcNT7HMF1QYoHisZ6CQ==", "signatures": [{"sig": "MEQCIGpf5DBawahQNZcRjOG6EujgDmtuhp5rKoFGh6YCfjj5AiARPomLcYQEq4Jetg3GMqVt7ZRZcCTPNiuURYvquqgCGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.0.0": {"name": "globals", "version": "6.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "92f22abab2139e8de9c3b5d2644acfe4b8544f81", "tarball": "https://registry.npmjs.org/globals/-/globals-6.0.0.tgz", "integrity": "sha512-Zf2cCCjf8W2B/atdUCjZhQDb6YzA3vRrbpRNyBRb31HMZ0avH4XyLSqDvK6n3zDwLfGR2bvZFjjU0HPmd7t6Rg==", "signatures": [{"sig": "MEUCIDZrpIesf1+YnakCj8N1+b3DDRQi1b7RJ2bIv+KMhm47AiEA5CuIq5HLsFkYf9tV570822x7RuZbkxkEt5EgRQZpfuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.1.0": {"name": "globals", "version": "6.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "45d617db6fa9f0f5d7aa21f041a0ab51b9d299b1", "tarball": "https://registry.npmjs.org/globals/-/globals-6.1.0.tgz", "integrity": "sha512-e7Je1Jg0S+2rFSb1qwdSMXnQ56zN8uBUr1rhXVKTyHkgXvS0LMSoMJrFYGXvhV5u3bsRScq9VVcM2/Ia9x2ZUg==", "signatures": [{"sig": "MEQCIBh9oCdhuA0fe+5GJ09KK2CHScaCdBSCcLCSY3A0qi6SAiA0KKV7wSXYqgWnK+ByEKIbLzcvo/A3uP8u2c3DbkCGmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.2.0": {"name": "globals", "version": "6.2.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "60ced87cd448fe17ee46804643051490f5bbc19b", "tarball": "https://registry.npmjs.org/globals/-/globals-6.2.0.tgz", "integrity": "sha512-U+lzxUqcqALm8frDpWGqC2MZDdGSQL+AwNGxwcLB1A8N2p3wo5dnw5bz5t3ZbyTrgxB5MFbSbfX5nJcaaHnhPw==", "signatures": [{"sig": "MEUCIC6eoRMEbwJz7deJtO8qNWaEUoK7W0t+MqAneYB3tB1fAiEAnvKmZINBTiFgKkD5NWkUACACABX66jvK9nh4NyVlwKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.3.0": {"name": "globals", "version": "6.3.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "89faa782414d18e49ac1db2b610c7d8b2edd2761", "tarball": "https://registry.npmjs.org/globals/-/globals-6.3.0.tgz", "integrity": "sha512-Bf3dzeFZBvNxiUtnTUwr5ALd4np/XaibMVOSQ4eg+tlLKXYTXbzz5ArthtwZio79L2YvBOEhkVBslT/trX18eg==", "signatures": [{"sig": "MEYCIQCqtVtMkTH7/fTe9NTBswEESdoqsVqZ5I4HGrcrP3lx+QIhAP3f1SfkhuVY4zPmbGJn7h4l+kdwJoIpdcPvxbUXI63D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.4.0": {"name": "globals", "version": "6.4.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "1ddbfbe9202d758544cdf2cc6aafbb5cb2f12eef", "tarball": "https://registry.npmjs.org/globals/-/globals-6.4.0.tgz", "integrity": "sha512-9rNv9z9uEma01CFoMzU6ecbArgAnoLwMaSjndLfwlOPJMQF9D45bok4d3xD6+oYQNit1JpFmcQlU0nQ0m3rLww==", "signatures": [{"sig": "MEUCIQCM9fxQMfRF1K7T1BWROlrDzOgakiJGZO7mkzGcT9l7yQIgXI0phnJ7DDSsVnMYyFdylodyz0T0XIDrYbtgqrmjzj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "6.4.1": {"name": "globals", "version": "6.4.1", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "8498032b3b6d1cc81eebc5f79690d8fe29fabf4f", "tarball": "https://registry.npmjs.org/globals/-/globals-6.4.1.tgz", "integrity": "sha512-Lh7H0bYRNBMc2CapY+TYsCzcSM4HWHGFoQORuEcePk3y3IhpaZmFSJDirhNYSwq8QeHvaCqV/tHI2bdUhYryuw==", "signatures": [{"sig": "MEUCIBmcUBF8RrEI+vrORzH9mGXSNogq+SuWlKCRECKQaEgYAiEAvnfJEcrbpMFhcMefgJg5Cl6xwrHMPm7Yxr0iuHkHWpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "7.0.0": {"name": "globals", "version": "7.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "fdf7027d66fefd84ffb640dc6555059178eacea1", "tarball": "https://registry.npmjs.org/globals/-/globals-7.0.0.tgz", "integrity": "sha512-+0WxAwzw2jx2uMPA/EdTB3VXT2yvRkp/P2YSc7IQpcufyd45jaYUKS0onMBEgwL6vh6SDjY2jOwCAsIGkoadBQ==", "signatures": [{"sig": "MEQCIBV2VxZCCDx7VUosP7ot2XIJUis+IYkJEWfsANXN6hlBAiAv96yRcsEcxO4V+eNk9tCkflhplezSe/6qMzeEr7lDeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "7.1.0": {"name": "globals", "version": "7.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "3a7f4e7f5cea78d13b6c18eb35f7f29647939075", "tarball": "https://registry.npmjs.org/globals/-/globals-7.1.0.tgz", "integrity": "sha512-jjxR+yJ6hVTjDlwcbwbCp9h8hV4p1H+6Q4+euACIcTplDZ3pl3OPqxsqGM2SlT4VjciMioL1PTwggI1/yfnCMQ==", "signatures": [{"sig": "MEUCIGoh4liQ//4iWnTnakqjlF1G6OfZzhUD7p1ISH0SJTWbAiEAvbhXh1CPv+q2NvHW2IPULYLAJxxW5fjdOiZDRSXZSXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.0.0": {"name": "globals", "version": "8.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "1bde4be6e5fd4c8408b1bf8fcefdebdf53212614", "tarball": "https://registry.npmjs.org/globals/-/globals-8.0.0.tgz", "integrity": "sha512-qvKdJ2tKdiolUR8kxNU8RFzXIrhYbI+Y7LMelgWnZmHVU4oB4yVkEnsQuZqDbTrymxFgtIMOVpyUGL/9ZZdqGg==", "signatures": [{"sig": "MEUCIQCncCY0UQAEBqmxBeb1OZjNI6EJzFcvmR27bsfwc6z8iwIgcH9C9gBQaGLXs0//OBlVSFOx6p7eShs1pd9E+r5zn5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.1.0": {"name": "globals", "version": "8.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "f491df47705dfb94e06ff4ff2352babde95e10b8", "tarball": "https://registry.npmjs.org/globals/-/globals-8.1.0.tgz", "integrity": "sha512-EHleNbLeHWRval4jfb2HRvlnrM3jJ+T+YZ9AQeNySfitdA+qkK9Wrnnv55SdhjElRiVUBDgC6gkdc/mFu9mmHw==", "signatures": [{"sig": "MEUCIQCxP9PP7LVRuGxMC3ye2rDv+jnd1dreETPC3RMQEGtYgwIgeV5eyHyNSu2AcSPovIF+qMlagngwMWdA0GD2DWFmOEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.2.0": {"name": "globals", "version": "8.2.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "97d0503f6a942ffb69b107fee447f4d54a66fb4e", "tarball": "https://registry.npmjs.org/globals/-/globals-8.2.0.tgz", "integrity": "sha512-Sr8UBu95yN+2ot8DviKSJbduB+2u2ImoJoZzDEeopPjKyclGdiuU4EDg1cpjeVQL7pyLUJeLbrL19inQB1yxjA==", "signatures": [{"sig": "MEQCIDdYgumr/CQNqjFOtR0cwo0RdFAEmG1JD2BnULYPrXW7AiAhrkKwynuQqIUCGwhZIw7DBb2Uim90TeH9FqTl5HxYNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.3.0": {"name": "globals", "version": "8.3.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "5706f1ff4338e0cbcd2400504b3085fc7984c7fc", "tarball": "https://registry.npmjs.org/globals/-/globals-8.3.0.tgz", "integrity": "sha512-HzOYHk2EKAH+g13VnMbT5AC5Y+nqvyaRbwNvhLfD1Lo9h6W0FMSTTlQha3HsHFd0/D5RIOjGTuJfnea99xm08w==", "signatures": [{"sig": "MEYCIQDzTbnAs/LKOKPumuf8UitwZVsv3p382KVNBqmCRGKM5QIhAPiRT1hTbnlMaItS7cfz5SlcvneX/d/Zdc+GidHhIgLC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.4.0": {"name": "globals", "version": "8.4.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "0885574bbe4478678b4546845bea6ef54d724b22", "tarball": "https://registry.npmjs.org/globals/-/globals-8.4.0.tgz", "integrity": "sha512-waEQitqdfnxed57zjTZCd6JQkJOlz3ZCPb7M2W0jRtTsjma4C+cDZ7HHN5/AlCZEgVib179N1yqdW6xO8+R4OQ==", "signatures": [{"sig": "MEUCIC36SsujBaXhOuM4d0xJ+8HHfrrVC9e4Z1onADJH7MUbAiEAmelBDOLZDndsGjtZ+jXiV5JaSyChnnL6gyfHDetF1Ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.5.0": {"name": "globals", "version": "8.5.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "3e899a41c0c4518dda4a0503d052474aebf32097", "tarball": "https://registry.npmjs.org/globals/-/globals-8.5.0.tgz", "integrity": "sha512-DVcPbc6vqhm4zt7LJiFm+hZT9446SMW6KRcfpFa9MQU1kbC7Nft9zOniCS1lxGfFpl6HNZ7PzAYRUVgxsRNFWw==", "signatures": [{"sig": "MEUCIQCO85HcotzK4N+TBhPsKvIOI7ANfTrJHfSqE9ad8GkRiQIgJMh2y3vFLCh5Gx2LCB9Y+NsUOMdbOlWDJ4mRNis0Gy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.6.0": {"name": "globals", "version": "8.6.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "d4ca7d15d9c279bca23002317115d519698b354d", "tarball": "https://registry.npmjs.org/globals/-/globals-8.6.0.tgz", "integrity": "sha512-OOD03ss3nYn9NlUf+3ft2V45BIWWRzw1K1Qdz+OQ+m1FAd9uC6oBXPEW/SPIh2NTmg/NWSxSAq4n+T+tHhL3AA==", "signatures": [{"sig": "MEUCIQCW2+JtlCHXkyYui/MZVbgZNLVXkGY+C6c8zijtKph2XwIgF27IGFHLDeIYvTcBKXIIPH0VEWKkjqei3Rf0TXVTiE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.7.0": {"name": "globals", "version": "8.7.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "52b36039f16b824e7617657fdfd207f57231c67e", "tarball": "https://registry.npmjs.org/globals/-/globals-8.7.0.tgz", "integrity": "sha512-0+vDyjHBmOqfJeAKeItgeVKTVZld0t+tApNfoksi8ofU7B7Ox//lTS55WOeXEpmga7TiwLKFhx9MAPjCiocYuw==", "signatures": [{"sig": "MEYCIQCpSGZdyXL4teB4Xa9nUZuRlhuAdoOnayoPHDGXVun2yAIhAK8FOQY1ZhUlgCAAcyMQgekUZ+3ENunhGq9vNAEpSgCZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.8.0": {"name": "globals", "version": "8.8.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "a584643e06428650019b237e18265d4099d0862e", "tarball": "https://registry.npmjs.org/globals/-/globals-8.8.0.tgz", "integrity": "sha512-4RSE38nPzSLG/DMpCNPIPHH/nfliwxR0kSZAGET2j+DghfAs9KbtXNwvdp7z5GxL5Zsj+b9sohcNE7l6jogjDQ==", "signatures": [{"sig": "MEYCIQDQla+Y6+eI6cltQMSkEfxpTwdlYbUhjJEKEDoHmqXUPQIhAIwx8Jz10VDswJ5OIBMWuKOKUn1OKtw+w+aEIUkcXP1b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.9.0": {"name": "globals", "version": "8.9.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "13d5520e356e1b2d7f1d0a4d519daebd31ab5a0a", "tarball": "https://registry.npmjs.org/globals/-/globals-8.9.0.tgz", "integrity": "sha512-skKRbaXJVaoGmwB1KITXhy5MUCLCPsf/S1T72ZXYqjnwH7mkbrRxxtC/aw66qgILuTaEehfFY+ginE6gQt2kSg==", "signatures": [{"sig": "MEUCIGRaZxLnOJQblKBxijscwQbpypAzDlZVzR5Iyrqlnne/AiEAsOtYM1XFX+fRlKwa5c9/VbLK1jxWd7wRrGPCObNQMSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.10.0": {"name": "globals", "version": "8.10.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "6e14b51cba0c38c7303ad17e1648ddce560f2b14", "tarball": "https://registry.npmjs.org/globals/-/globals-8.10.0.tgz", "integrity": "sha512-D3QMoBGkwS+2rOYBLMBtn899hZudiMEfs9FwUYUs2wTNNG95Z10gRLlgvMDCcyiWpASqDZE20+cnA5ODHLufUw==", "signatures": [{"sig": "MEQCIEwJrfONJuRDefiwITlc0DsJa6E8tTB/bi7e5zdElkOtAiAFlyJX8CfZELIQayM7CHJok/RcYiV3YQC8ngbBBnxacg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.11.0": {"name": "globals", "version": "8.11.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "f117b03479dc2df159302d81cd08396425705d5b", "tarball": "https://registry.npmjs.org/globals/-/globals-8.11.0.tgz", "integrity": "sha512-40a2wxtDJBj7TQGWQrUfuDD6WwiN0uUDeoN53tXQIBnKDvLGwpygsK7IvO4irYbBXg386doJGy0FgET8zKWJsg==", "signatures": [{"sig": "MEQCH3ksC+BF/e9YZT4JZkdBfFtSv25o6CGpo/ZEjLzkD6wCIQD+lT7ODUo3j48QspOsjtJVJthDFM6DXMlBpjaO7F2n6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.12.0": {"name": "globals", "version": "8.12.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "e459a2e8993fe3c1ad09a3b703ab4ea38ecbbe94", "tarball": "https://registry.npmjs.org/globals/-/globals-8.12.0.tgz", "integrity": "sha512-b/sIGPLSOWJbt5JKyqh7SkdNYCbtzB/Te4pftWKhuUN0r8HXU71vmPYPN1SfUZLSPbE5NU3fqkrN5r6FBxO6mg==", "signatures": [{"sig": "MEUCIBP1rBpbE/qVW5EpU+ABjdNBWYTOX/h+x+h9xpRO+At+AiEA1EUPOb8g1oJteCziHqxZfp/s4QFDgkdK7igAC/u6i0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.12.1": {"name": "globals", "version": "8.12.1", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "566a13f7b3bbfb8779e7c05b3b89468d83b3d10f", "tarball": "https://registry.npmjs.org/globals/-/globals-8.12.1.tgz", "integrity": "sha512-vPO/lWe+fNRxNXc1N6CsLcldzvcOnZxi2EIcFs53J6zATq+Mb1pjlbvk5dz2rhF02nXZI/B4AbVABzxK8NwaYQ==", "signatures": [{"sig": "MEUCICnuM1vtZmrMAWB+HMXnmpz6QEr67hbt8s951uzG1VHWAiEA5U3SIOvAEgKGEE7bi8zQjlr0MoTno3MdIum8OzDjlp8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.13.0": {"name": "globals", "version": "8.13.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "c9c4d1dee4e12252864d9511477068a719ef9560", "tarball": "https://registry.npmjs.org/globals/-/globals-8.13.0.tgz", "integrity": "sha512-HD+OReP9y58jtYLu3bVfvx0u1fXIEKFe03df1DvqmyIFkO0CZYxKkHb/YgDph563Vt15GCx0b1/nZU+bUEuJkw==", "signatures": [{"sig": "MEUCIGyCr3GEjbPWL8mOfvhnG7P5A6Jh/qhFH9OCQpMmb/WOAiEApPW87RjY8WUlPYs2Mnk/mNkg5CmV4bxqQWDutzL90og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.14.0": {"name": "globals", "version": "8.14.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "62f567e67479d2d949c12ce52c39c24eff443c5a", "tarball": "https://registry.npmjs.org/globals/-/globals-8.14.0.tgz", "integrity": "sha512-2GuwL9lkn33Qp4+3wsalCeMiDFLhnsa+czLQfzKKHNc2CpWHAlwFplN2f2ypd7WVHHno1mklwYUEjEFxVG+zFA==", "signatures": [{"sig": "MEUCIFHJ4aKgvcOmrcF13z2KDcHHre8fOs8FIwc8qhsoTJevAiEAiQvtLzwicqiPauYWJ4SGiokK4cvmFOOVqr0y5gyoEWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.15.0": {"name": "globals", "version": "8.15.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "3cf33671186eb0619e259df5dc4ad4817e7a846b", "tarball": "https://registry.npmjs.org/globals/-/globals-8.15.0.tgz", "integrity": "sha512-IhC1yQKEw8BDwKyUFo8ojCOCAVK9XIgJy2U3hCGGVaPaBP10X/8YrcqP+ctaTJIU0colkWKCCSQ66M4z/40fFQ==", "signatures": [{"sig": "MEQCIHYB6IAp6GiUGnShfnj9ZGjOXDMFaazQLTnu/jutnf5GAiAGXZHKu2Nj1iNiIjMnL6pVpn/UAIYGnJHF2YcVaPwZ8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.16.0": {"name": "globals", "version": "8.16.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "15ddf66b4e41f8224b44d897960ddab07f7b9f65", "tarball": "https://registry.npmjs.org/globals/-/globals-8.16.0.tgz", "integrity": "sha512-EjXv+sQ76IYYT6lv12pcp2R4tJtmVVeYveRolsWll6uy8e2RuqpUjkZcTMC9JtEXk0zcqQL4FJ73BpSVxWAdXA==", "signatures": [{"sig": "MEUCIE13Xp/7cQvsVoohrcFFoxYaSZjSNe68z/gOty98aOREAiEAxW6QNkMsv0SUzEDhwsdUJ4h/h5GMXbNiegSHD8L0dD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.17.0": {"name": "globals", "version": "8.17.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "9385a461712d91063fbd62e9b88b96aa00d09b97", "tarball": "https://registry.npmjs.org/globals/-/globals-8.17.0.tgz", "integrity": "sha512-gql85dpXuywMrOYP0P9NEuQFEtgfqmnlqLjtIM4ak1FF1OBeo6Vu+S9BdbSRjkkgjxP2lGfhRQhujyTtyO5vIg==", "signatures": [{"sig": "MEYCIQDXtp8qXiolOe24n8QjA1saOzljrEHxz1v/bB7ZAUxC3QIhAJeLDOslh4b1ak/9kMO/rvjyO9sCCdXS2FJg1y5pqj0A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "8.18.0": {"name": "globals", "version": "8.18.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "93d4a62bdcac38cfafafc47d6b034768cb0ffcb4", "tarball": "https://registry.npmjs.org/globals/-/globals-8.18.0.tgz", "integrity": "sha512-IHCTKEGo42ICEkTZBADyl4HX06hVdFF4qdJdqOgaBe5X8RE1/MrvubetsEtGTcwjs46djFq0Gc3+5RgTsc3UoQ==", "signatures": [{"sig": "MEUCICfI5DC/+jocqoE+ByME7Ad757yEq29pBOm7fmDpwHdWAiEAlXivPQ5hNRN9/X/8pIy7WIp4dcTL4++BObrWk8tfRnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.0.0": {"name": "globals", "version": "9.0.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "285f64c96b38b9c2c05959149748e3ebd388a65d", "tarball": "https://registry.npmjs.org/globals/-/globals-9.0.0.tgz", "integrity": "sha512-sTgnjXeQpQ2fx3kqb3M/howdqC+puTuIQ2afqO6o/UzNGObZGRNk9mWui5MMjp/7Jf8YE/9uM4xZuA5XaeV3Ow==", "signatures": [{"sig": "MEYCIQC93DGFISuAAHT/upg4Ny2Ai5e9M/JfPX9lCymDVTiWxwIhANuYPJEPZNFaYlQxDUAVetKWlLqV4zkDvD+lPyHy+JxF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.1.0": {"name": "globals", "version": "9.1.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "2443cd6ad93c51b767324dc59562fbe71e0af98d", "tarball": "https://registry.npmjs.org/globals/-/globals-9.1.0.tgz", "integrity": "sha512-8qmktz45VTwYH3hSFCNU3tDd4jXK9Ruv9Vvc9sWmZSwO4BGU3fkA9aUNEuBTfnwBimG1TnNVuGrYnOta3yWuMw==", "signatures": [{"sig": "MEYCIQCg4O/e/HGjh6UTEnGOU917C24jEpUKHKuJcHZtMv2fXAIhAMTleTV/khV09ra269jMOhF/0ullPCHACN/gqFcwHZoZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.2.0": {"name": "globals", "version": "9.2.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "e099b0135b3c4f84a6a93e8006d93df9d87c9838", "tarball": "https://registry.npmjs.org/globals/-/globals-9.2.0.tgz", "integrity": "sha512-4Aht/n4BrXypkbqonUSVm5qAPB47eg3eRpcLvYVrd2I+5BXbxhLsPSjF+YOI4ftxLctT4ykH/wF8MV4AkXIsFw==", "signatures": [{"sig": "MEYCIQCvFze6vQzokCznwchzdCxnMfliogwOlj66DOgvxgnjdwIhAPsxf1gX7wwEMrhLv0xpZXMEE8Ejatrurtz5Ip7fcneo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.3.0": {"name": "globals", "version": "9.3.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "55a541cf8519d66055ed3c892dd7c49c1504783e", "tarball": "https://registry.npmjs.org/globals/-/globals-9.3.0.tgz", "integrity": "sha512-slFC4HGj8zm0wvisMTEfETTjovoKOdf6ecpkEqRXFKkFg+wD2r/eqOIL9QLXTn1haYL38A0+PCrDGfKEcKteAA==", "signatures": [{"sig": "MEQCIAnkFke03tlbrvcmKvliaItcIlwwBUPlHcEYA7sgZ+EmAiBa2+zlz5Qs9fDwN4Estx1fImvqqWnFa308+qZgKH+SbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.4.0": {"name": "globals", "version": "9.4.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "e89906bbd58b40305e5691ef934324e93325b35f", "tarball": "https://registry.npmjs.org/globals/-/globals-9.4.0.tgz", "integrity": "sha512-7tD7KMmfTSAk71M2YcpjPbTcEJoxtcosH8Svlo1l2MvMh4ZgLwmOuBmLQA8K/u5eBQyIBpOiA0OyXlM1mDgyiw==", "signatures": [{"sig": "MEYCIQCeh0Nulj22Rc/vGgD8EF4UQjJtW+ZKFoaayOf1jqZ9LgIhAP754tkKqNZjLImvc/UymxyY+GbxS7+4HwZEsyIlxHX9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.5.0": {"name": "globals", "version": "9.5.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "8d0244dbc03e1e71ee0290ff805eff9af37b176c", "tarball": "https://registry.npmjs.org/globals/-/globals-9.5.0.tgz", "integrity": "sha512-VYSme/JRrRmjFFjKm+zOl1l1wl/cxkAQWFOqeSOUh4AKYg4Rn1M81/yvdXw1TKzFqLxUbtu4VLZPy0Yd+odukg==", "signatures": [{"sig": "MEUCIGN7EjPv6/NM54XaSA17LJGIrs/zlsAfGouhzwvBma9WAiEA3hTGUumbq/ULuwmfUIb7eXTHFxikkacGKB2/u8jBIq4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.6.0": {"name": "globals", "version": "9.6.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "b34eef40f2583df0b7f58645e39636c601c5b79f", "tarball": "https://registry.npmjs.org/globals/-/globals-9.6.0.tgz", "integrity": "sha512-49Z5xR2eK+giaM/kVv5f4QlhChrlxbhp8WoreeM4//j6O+PPrhh+iajy09iEaP0WQa0Hjvco4jN6r5YoTA+mjw==", "signatures": [{"sig": "MEUCIFipYkTW+GEj/IgnTWhkA3Cmy64Qpw+6VXQZRxxldxjFAiEA7UAv/3zx4AjkjqOB3KWi0yWRyesOgzygpmCrnZjjTeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.7.0": {"name": "globals", "version": "9.7.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "3a8e41bd9b8ed79749ce6629568be839b789ef9d", "tarball": "https://registry.npmjs.org/globals/-/globals-9.7.0.tgz", "integrity": "sha512-LuzcdarED0dbuQ2wVWBF6/POv8aJz/bsqaB73M4aBrtGn3ExVSPut31dkA46fZBMVYEhp2J9Icv9F4zDMLKWSQ==", "signatures": [{"sig": "MEUCIQDxxoD8YS74kDjd+FvfC4AzcV4jRum4Tf3ZX5m0ZOi5pAIgLxKftfoCGa5lz879aFsgtANzeyMHfuwZ6IyiaehYKuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.8.0": {"name": "globals", "version": "9.8.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "a436ecf6214e5f73f110a400305325330a7cfd50", "tarball": "https://registry.npmjs.org/globals/-/globals-9.8.0.tgz", "integrity": "sha512-W5kSx1AI3Ze/o79X3QrQhRS2W9J11nDZNc+ZHDN7ORh6uRjXysNTnJ7WqulaEKgqDayoYIcidgkRixMt9c1XXA==", "signatures": [{"sig": "MEQCIC2KQbb/J0feGN83Sz6hv90H80GoxKaBiil66yDsplLGAiAJHGrQ/zU0tpabW1WVYNcsLKcSQU0og9YacG4AZkm7FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.9.0": {"name": "globals", "version": "9.9.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "4c5ffc359fb21edc83fedb87b1c0b414dc24d552", "tarball": "https://registry.npmjs.org/globals/-/globals-9.9.0.tgz", "integrity": "sha512-XFg4WzLYirTILqtwUQ3mCdyeBwY6I6iA3b3YvXknYLhb7+PkH9QytwVIVJsonVhfIxEqB3k+FYYNGjOhPjzLnw==", "signatures": [{"sig": "MEUCIBe5aO2mPx1J6PqIZMD+xq5HPyO2YTtkmzDyfy/3Q5bYAiEA9sUd06mMcLo/AJMEC64aS7Oo8VFTwDtrMeWmuCBJ7uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.10.0": {"name": "globals", "version": "9.10.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "d1047641c49b7b03cacf7e15fb8a42a3d33c88f7", "tarball": "https://registry.npmjs.org/globals/-/globals-9.10.0.tgz", "integrity": "sha512-4/lMING9fxEyj9QfljBnn419UgogGyfy99uhyWfDIoDjNIfAlN/9jc5yg04bzxOz6fH0Ju15tBmhQlx5MdxAbw==", "signatures": [{"sig": "MEUCIDOknuyiWi3IR86Sl5PkozU0aS2EYkgp9ezBmA6kJS+lAiEA6Z5Y1jgEMSotoLZOyBy3GNXc0iMW71Sr9WNG3vussMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.11.0": {"name": "globals", "version": "9.11.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "d46044c4945fa40d86b1f0a816ca5819a960dcbd", "tarball": "https://registry.npmjs.org/globals/-/globals-9.11.0.tgz", "integrity": "sha512-sXsjQK1gmRc00VXrOYJE58rTXQfDNkAXxdVT/20tEoXXEUbITNiifZ2bxU26fqNfutHOmcLqXdC0u0vAohMmZg==", "signatures": [{"sig": "MEUCIApFgQkcM6i1FiCHXC3F7pVblmcLYPV2ts+62YPGm5e/AiEAr0EbpUa6xc2CFTWyqdMKeWP/fecHteqQ4yS/Mf06XdA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.12.0": {"name": "globals", "version": "9.12.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "992ce90828c3a55fa8f16fada177adb64664cf9d", "tarball": "https://registry.npmjs.org/globals/-/globals-9.12.0.tgz", "integrity": "sha512-U4E+yRLRTaZ49rv+L+jpB4dnE97/2Cq48OQjHu/VXvLa2bxeddP01nZ1K0Ot6XPTOFtPLsgmoaQxo4IIhJaGSg==", "signatures": [{"sig": "MEYCIQCJBWtbDmDEZ7ltV/cztqMfmGQmblMfS3L4tTG7OrhczwIhAKJJPgCC6HJ7pcdjz39dkiv4xmjgcaA4lzNRIgR+Yb5W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.13.0": {"name": "globals", "version": "9.13.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "d97706b61600d8dbe94708c367d3fdcf48470b8f", "tarball": "https://registry.npmjs.org/globals/-/globals-9.13.0.tgz", "integrity": "sha512-lCreuVElM2zhR23Ahu/ST7eW6a/7QG2HY3NjF5JZiGcw7hhuOC/HqOXwfJRPsiDqqVb0BOGBIqiAPfCAN0hZKQ==", "signatures": [{"sig": "MEUCIC5sfqhm+17K3dBncMTda7yZAzs1W/FgCKIJG4Ax1U2dAiEAj3FHkHhbO1pBMLun0Mc/U27vVFRD1qgp+N62PS4WWOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.14.0": {"name": "globals", "version": "9.14.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "8859936af0038741263053b39d0e76ca241e4034", "tarball": "https://registry.npmjs.org/globals/-/globals-9.14.0.tgz", "integrity": "sha512-LQgqWl7MiLuoKSdfGja2TKwoXK0Xwg85Ak3AHuvzl5GserrF1xQ5uT2tFKroM5o41IqtXmQ5KJpdgcdxgtuwKQ==", "signatures": [{"sig": "MEYCIQCa8HP7hx6KI8zdghbamK1Cm1DBd9lr7rn2U9MCJo97DQIhAPX+oRUSfKKHG+r9HQDU5sYZzFYmVnfGi19UTsl0p9mH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.15.0": {"name": "globals", "version": "9.15.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "7a5d8fd865e69de910b090b15a87772f9423c5de", "tarball": "https://registry.npmjs.org/globals/-/globals-9.15.0.tgz", "integrity": "sha512-n1H2ijgtY/5mw806BpvTgrHxQlngCDj+R6YdlQwLY4IAeMhKcdTJ8P9edSVEevumKPEmH+KDUGwvRUx0lryrEQ==", "signatures": [{"sig": "MEUCIAP0uimhH91kRZ5Aoj1OgAr1dxmVEoh7W66Bs9z9yEVUAiEA0D96sMY2IjlMVi2+pmxuzSosPq5ymwKVR8flKlBjzo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.16.0": {"name": "globals", "version": "9.16.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "63e903658171ec2d9f51b1d31de5e2b8dc01fb80", "tarball": "https://registry.npmjs.org/globals/-/globals-9.16.0.tgz", "integrity": "sha512-0C8kzSk5AqhNgO/TKtlFIr2F1anvbqJlIacth0mWhuTUqANWPjIfcHkaWgL+q0XVWRpwA4cYiIorHFORv5LwnQ==", "signatures": [{"sig": "MEQCICqPi2qvnmU/hmgMAphOTsZN8yDD1S7tGgaIFRw00uyqAiBNejm4RrRnFL+A2cFYM7gmhb4Ol43+Qw8B9gc4LZQftQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.17.0": {"name": "globals", "version": "9.17.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "0c0ca696d9b9bb694d2e5470bd37777caad50286", "tarball": "https://registry.npmjs.org/globals/-/globals-9.17.0.tgz", "integrity": "sha512-oZir3ZZbSYGRu+KeFbR9nWoB8wqAciMthMMSeoy2eFcRZf3uzZOsbCOFKtW/QdnK+cz7nn7eL3q6JCAfgsb/2Q==", "signatures": [{"sig": "MEUCIQDcN5YBL0PqugUJfMFzAzmoIii+48DVz7GULKCIlTgvegIgfySZdW1HIqvWtOm5wy9dn0j4/55qKmBmREX3waMBcWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "9.18.0": {"name": "globals", "version": "9.18.0", "devDependencies": {"mocha": "*"}, "dist": {"shasum": "aa3896b3e69b487f17e31ed2143d69a8e30c2d8a", "tarball": "https://registry.npmjs.org/globals/-/globals-9.18.0.tgz", "integrity": "sha512-S0nG3CLEQiY/ILxqtztTWH/3iRRdyBLw6KMDxnKMchrtbj2OFmehVh0WUCfW3DUrIgx/qFrJPICrq4Z4sTR9UQ==", "signatures": [{"sig": "MEYCIQCO8OzX2IPITeAPm5YrBykseNlDDB1WK1rgs9/rCInzpwIhAMnRpK2BwUZdu7AsuaycyeGHCJ9GCEMQNzrSFU8J4kSo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "10.0.0": {"name": "globals", "version": "10.0.0", "devDependencies": {"mocha": "^3.4.2"}, "dist": {"shasum": "a5803a1abe923b52bc33a59cffeaf6e0748cf3f7", "tarball": "https://registry.npmjs.org/globals/-/globals-10.0.0.tgz", "integrity": "sha512-kxN6RfO/HLQcHV2yI48enVP37K7vlpTl3vcLCwYkRidzO/GjOOK7jQUD9LvhPeSvbiAsnSDGEd5SDVVa9EeimQ==", "signatures": [{"sig": "MEUCICPjloM4wRURYLGxyhy0GVESWSOwWM0xYMeJFl0h4cN3AiEAr7BKjDVLfKUUP5rcgw7yqiLeGT7RdwoCsjGNKIKDDx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "10.1.0": {"name": "globals", "version": "10.1.0", "devDependencies": {"mocha": "^3.4.2"}, "dist": {"shasum": "4425a1881be0d336b4a823a82a7be725d5dd987c", "tarball": "https://registry.npmjs.org/globals/-/globals-10.1.0.tgz", "integrity": "sha512-xK/ymsObjgXY5+QBnZSvZKzqQaMvoyG2xJyhI8tqQI//3AmX3ZGOfFQ9g7QgDGf+pBGp5TYeszuSCQfjhtQK7g==", "signatures": [{"sig": "MEYCIQCPLfrmWbrr30OBD1P098xa24u36S0AbsquCVeRod0bdAIhAIa0As1GwVWr33W21XcCKBJoszPnRuKKS4fl/XfSYZCe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "10.2.0": {"name": "globals", "version": "10.2.0", "devDependencies": {"mocha": "^3.4.2"}, "dist": {"shasum": "69490789091fcaa7f7d512c668c8eb73894a4ef2", "tarball": "https://registry.npmjs.org/globals/-/globals-10.2.0.tgz", "integrity": "sha512-Kqkw0LTOBKEwtVuBiw52QwD6qI1TkJZQKdIYUfVEitfPbSiSmclVicQV0hPS3oqVBkr+O/TPpKk7+dRn1h5Hog==", "signatures": [{"sig": "MEUCIQDfAeaWHkcEoUtz8gRfeZYVqQcFBkKDAUiybfXlRhmrnAIgXljHIPzZY16evGveV5ots+5TzltWY8UkfYkA9rySpOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "10.3.0": {"name": "globals", "version": "10.3.0", "devDependencies": {"mocha": "^3.4.2"}, "dist": {"shasum": "716aba93657b56630b5a0e77de5ea8ac6215afaa", "tarball": "https://registry.npmjs.org/globals/-/globals-10.3.0.tgz", "integrity": "sha512-1g6qO5vMbiPHbRTDtR9JVjRkAhkgH4nSANYGyx1eOfqgxcMnYMMD+7MjmjfzXjwFpVUE/7/NzF+jQxYE7P4r7A==", "signatures": [{"sig": "MEUCIQCdLn/vzSwcA+6ZwbgNFlSYrcG5hmr3aHKUSV4tZtdC+QIgDInWMu4HvApw+6wUKlrcKhZN25adWsF8aLheX1pPeHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "10.4.0": {"name": "globals", "version": "10.4.0", "devDependencies": {"mocha": "^3.4.2"}, "dist": {"shasum": "5c477388b128a9e4c5c5d01c7a2aca68c68b2da7", "tarball": "https://registry.npmjs.org/globals/-/globals-10.4.0.tgz", "integrity": "sha512-uNUtxIZpGyuaq+5BqGGQHsL4wUlJAXRqOm6g3Y48/CWNGTLONgBibI0lh6lGxjR2HljFYUfszb+mk4WkgMntsA==", "signatures": [{"sig": "MEUCIQCFS4xE/7ccnRuXJd9GqLSU1hiIvxBZaI5panbk15PfVwIgHSylKq7olJ/wIoH2ZtiKV1ovv5qZKeueljWLPsGQgZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.0.0": {"name": "globals", "version": "11.0.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "cba6598408a17822ec3495386b0ff807a89b7046", "tarball": "https://registry.npmjs.org/globals/-/globals-11.0.0.tgz", "integrity": "sha512-t+t3pk2RQprGfUEEz6c1tDBQRunf5C7NLPUDyaOPMaXQpcCdvrLibMi468hn5kS0mNzx4GLBJX9nvNMOKiAejg==", "signatures": [{"sig": "MEUCIQCVy4GtUeqnh2CCQXEjs8guu1rmfDoMWF1rjB3jWHgYzQIgdQh43E3qAmMArprTYorlbPvt9PN+AFmXtroDCCPkMzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.0.1": {"name": "globals", "version": "11.0.1", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "12a87bb010e5154396acc535e1e43fc753b0e5e8", "tarball": "https://registry.npmjs.org/globals/-/globals-11.0.1.tgz", "integrity": "sha512-FVYsa91HPGoiMPkNLRbGNcdyeRMYs3zp3QA803Ka/A/QjJX1UnGPfZQ0NxhZ8HrfbMXcn0EjEI87QMtztklbqQ==", "signatures": [{"sig": "MEYCIQCczqgVw98uH8pHEU9hdnq/YBb1Wc6a+58VRbFySVs7wQIhAM8dEtV/Fwpmb1WwwDRB0qtLepNOluomxmaXHS28yqre", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.1.0": {"name": "globals", "version": "11.1.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "632644457f5f0e3ae711807183700ebf2e4633e4", "tarball": "https://registry.npmjs.org/globals/-/globals-11.1.0.tgz", "integrity": "sha512-uEuWt9mqTlPDwSqi+sHjD4nWU/1N+q0fiWI9T1mZpD2UENqX20CFD5T/ziLZvztPaBKl7ZylUi1q6Qfm7E2CiQ==", "signatures": [{"sig": "MEYCIQDWH+5ED5hvs7TI7xfoTEiQO/qw46adTgHs5YtJBVDgWAIhALvB469UQgm/mX+zZWEA4qsErYBCGUvAXtsLYGXLddbF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.2.0": {"name": "globals", "version": "11.2.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "aa2ece052a787563ba70a3dcd9dc2eb8a9a0488c", "tarball": "https://registry.npmjs.org/globals/-/globals-11.2.0.tgz", "integrity": "sha512-RDC7Tj17I/56wpVvCVLSXtnn2Fo6CQZ9vaj+ARn+qlzm/ozbKQZe+j9fvHZCbSq+4JSGjTpKEt7p/AA1IKXRFA==", "signatures": [{"sig": "MEUCIETTrgqTv5xYsEChc2X7ITzdmOMSvGeU+PshM+vutK7GAiEA9FdDqwUncrprbJOKIO9H59MpQo/xgWXFnqWrTuXS3pY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.3.0": {"name": "globals", "version": "11.3.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "e04fdb7b9796d8adac9c8f64c14837b2313378b0", "tarball": "https://registry.npmjs.org/globals/-/globals-11.3.0.tgz", "integrity": "sha512-kkpcKNlmQan9Z5ZmgqKH/SMbSmjxQ7QjyNqfXVc8VJcoBV2UEg+sxQD15GQofGRh2hfpwUb70VC31DR7Rq5Hdw==", "signatures": [{"sig": "MEUCIGrkboVxbOjpoVVudjgQuxaAEr7aOHHT7ws+SZSHEYfDAiEA5ovji07kBkW/8xqljRNdpw0B/EukcKd2CvBjPrxD9aQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "11.4.0": {"name": "globals", "version": "11.4.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "b85c793349561c16076a3c13549238a27945f1bc", "tarball": "https://registry.npmjs.org/globals/-/globals-11.4.0.tgz", "fileCount": 5, "integrity": "sha512-Dyzmifil8n/TmSqYDEXbm+C8yitzJQqQIlJQLNRMwa+BOUJpRC19pyVeN12JAjt61xonvXjtff+hJruTRXn5HA==", "signatures": [{"sig": "MEUCIHXC1kIdjzxhL4207ig/rC8Ei3Vh5xTyEbJ6tXL+P/9AAiEA+MwQl5FbuCV8xRntIGOVS2EXFmscTqybj0oIlD3lWK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37385}, "engines": {"node": ">=4"}}, "11.5.0": {"name": "globals", "version": "11.5.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "6bc840de6771173b191f13d3a9c94d441ee92642", "tarball": "https://registry.npmjs.org/globals/-/globals-11.5.0.tgz", "fileCount": 5, "integrity": "sha512-hYyf+kI8dm3nORsiiXUQigOU62hDLfJ9G01uyGMxhc6BKsircrUhC4uJPQPUSuq2GrTmiiEt7ewxlMdBewfmKQ==", "signatures": [{"sig": "MEUCIAbxpApt84EtYstLuwfySKSkkLy42wkk2PN/hwA7MYGHAiEAtozS7OWQty465cwof/biqq+071Mus3VIkcDMUQhSmEg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4q6CCRA9TVsSAnZWagAAJM8P/jDe8kfcA8U9Afe8tWsc\nFJv5ft9Xib3fnbHLAj6bV554Eu9p82UMCX8J3cz6eb6NnevEGeEoJ3AMxIQx\nq6T0i2Y5/9J96vKc/z+k6Ox353E9bcdxPkHehOWGFDlccLWI5bMw47GCagor\nCrTnthSJE2lSUkpQJ/+FU+FjfY8qZom5cI+ddEXnD70iPwgWL9hsuZWc1bML\njb8pBPnf3COUfQCCFijJdTZpdU/6eauxJCZ5LaBezizixL6RTjjoL/V6sQlP\noMekDJ5eKKJw29pJiEBx6Dfjw4d0qZAv2Ue6BeG4fe6Tof1HTs1O5SLaCXgu\nSwrGWG0YvzpTk9W4h/6AM8hN/2cgDc7vK29sChc/pEQ9diSZMT7sASYpUW6N\niyuE8oatL7PSkPKMO9em+X9gjFgKHRHnjYA4GwgE0OvedjB+fHbVkGs4TQN5\n10ltiYIl2HIOffw18QDZA+LcQRMXWppRotjAMRhbe5FfKxvLgacjCHrbXk9f\nTMigSYHyZiL3J1u8Z2/PZggaWzk6mbnxhkpLKu3hTfaL8Sk3qyfxwXa3KZTK\n8MFPL9hklooalLP2XmaEiAPpEJ100II9GWYbH5rEA4229h/7CPsc9REW5UzV\n8oDo8IeQP3iecG6X/Dgw2bfy4eMQoA7juccsqRISbYindLBMjRtM8N3kRFkG\nPZ6Q\r\n=PuSR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.6.0": {"name": "globals", "version": "11.6.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "7e4d12ffd6b8d174851ff5a246bfb2b8b002a6c7", "tarball": "https://registry.npmjs.org/globals/-/globals-11.6.0.tgz", "fileCount": 5, "integrity": "sha512-IC8IL7f76dYfElTZ47+hXtEOtq/Cxqd48jR1dfSl7N3T2XA98XBnx8nkNUuy/O3TFmws107jq4Ty+3Tm81BoOA==", "signatures": [{"sig": "MEYCIQCPrmyDfTVnnp8RnwQ2l9jjYDY8uEjEK/7Z0v5CeLzV7gIhAPbeUfMe+f98O2oiUy9JoMqMewmeUM1rDbpgyN/jID/P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37572, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKUg/CRA9TVsSAnZWagAAoQcQAJ6rtNMd9wCOK0VkgBrq\nScr4624WB2+POPrNvUKeJr3hhLTRqGNE5GH1EH/VW3q1hKV4TkVpAY+jp4fu\ny9qhWarhcjL834I2A28ya2LnXrAVrhBCMQ1YEHfapODkTm4QYDwMpV8k9H94\ndWNe0ewaYvJMpWG7thZXcS8Nw19vGGjBtec9zePTCQoiU7WBjuehQkGptL+p\npHzlrwvNjeLQaT2VJ4cNxgy9oJCiox22nhYTyWqLdWzHKVHeyEJKGlpVANH6\nMALMWEclOMCiRjJdho75sB5XTYHWasSjcCVuZY51KcGWGph2b5LR+TB5f/J2\nF6hRvDYzZq9MnByl/tTxmf5OPG5L09hDa3I+UWPb9qVrHzd5bA4hqYQaEbax\nwsqY9NXJO4Cv/yowsyPvpqTBsfzKQ3EJG0CI9QoMsjvWjZyEKABMVNh96HIJ\n38llH3pNpjSd9zI3NXXClBadFRdbjdlaBSNrOs/sa4PKf5TlALkxzajbSJgR\nXsmZnByoQlXVd16EVvSzBC8Jb7pueZr+hjzz/4oDvqY8jDs1u9iJzmyolh4F\nC5puq/eN7HipbyXnsBrYvxaEE2hE1dSWMvT03SHNewj/DOdRUj1nA+71FTzv\nwMHOZl5lpdqMtjy7eSg2l+kcw1FCwOqFTxPPDRskd8Qh5vprQcQxeljJEqe9\nz/o0\r\n=ND+P\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.7.0": {"name": "globals", "version": "11.7.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "a583faa43055b1aca771914bf68258e2fc125673", "tarball": "https://registry.npmjs.org/globals/-/globals-11.7.0.tgz", "fileCount": 5, "integrity": "sha512-K8BNSPySfeShBQXsahYB/AbbWruVOTyVpgoIDnl8odPpeSfP2J5QO2oLFFdl2j7GfDCtZj2bMKar2T49itTPCg==", "signatures": [{"sig": "MEYCIQDRFVM9O5lP2emGiDHKfoXgmRzTqvC3vYZZWoL6aIpU+QIhAN69fMHqplp3gkuYnKoJoHcL23fuy8215257jHSS4+8I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKgHpCRA9TVsSAnZWagAA7dYP/Az2SaUNEAq2MJ6Rb5Uh\nnPvNLc6mHkg/1ha4HdMukcAiBS5Sqty/4CMVItJkjnRKKaiMiefcA8+u0D5J\nf763va1n09qdLmX94zj2CS7MFk0hj7sYt8GPUkaIzU+jlRvbnvNE1lEETVpu\n70Fyta7diCk1+Q7fNsy7ptceawZAfXDeY9ftOMMO1B23CzCha3ReTeUPyg7y\nluXnXRqg/N3TR3grgBHHbEzwN0yXPlHtuMXr7qN5qDSD06hgAOVtgbvknN6p\noSKaBfNOZ5VJpnYYtvnuxd/uqRnR6dzN0YK5yXF3A/RhgHpYIAWCB9+UaxFd\ngL5BOZYRXF0OhpDgGvN3optVSyfwNs5B/AbbQGhtzMlLKQe6ig4Ge9lE2lVX\nF4dh6gm3KncaLl473A/GASRFEmh5YlPUMz/XoJW8eXveXTu4Vb+BowyI8q/V\n4Rz9S9FoskqtqB/oPICFI8ldjxc3wk13LP6iWpjfjJE/1xdDeQxC3+s3gIm5\nLyUck60isXIh/gxenTnuNYOpkCwgQPRZJEq6J5cyVuoaVqJTcpqfV6Jnyv40\nslX2ptRgNi3FM4l4WExQUO9qWeRz3nMjQikIRpWs+cVIoD/hf6+bsAeg6UN+\nWH75o+w6qitM9umomMJjSo0McNneDVZYEyv/PCx35AOEGcuixTIke7Q6rkoD\nA6sz\r\n=TnmR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.8.0": {"name": "globals", "version": "11.8.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "c1ef45ee9bed6badf0663c5cb90e8d1adec1321d", "tarball": "https://registry.npmjs.org/globals/-/globals-11.8.0.tgz", "fileCount": 5, "integrity": "sha512-io6LkyPVuzCHBSQV9fmOwxZkUk6nIaGmxheLDgmuFv89j0fm2aqDbIXKAGfzCMHqz3HLF2Zf8WSG6VqMh2qFmA==", "signatures": [{"sig": "MEQCIAOqdL61czUTDEih1avLGaqqn5nvc1eMsLuYz87K8bXLAiBLNhnB+Y1xrkOZDGDe7jsZzzYII/5oByWC0ptkh9RkYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbswURCRA9TVsSAnZWagAAv6EQAKCiIxrcULGtBVgLnXOQ\n6v3Fh6JjppWdWQHsCLGlICVHtdbCoswp/YtvG1j79ot0ulWTuUSRBMUIyc3L\n81PsYDRxK+hnrpM3i38n3ZxaS/5M201iTWaznq4wSZqsdPZfa3rL9YXtZxYa\nhTCmdjc54/1A/yxHDyoTjHHV3i3lp11mmGhIxowObGuRyNxQd6/dsY7qOZ27\noKGDn05LOrm5VHyQBWLSPSeM6ekxLWgAZBvdIEMzLj0jeLbbkffRN9lCfsw/\n+O/cMQK+vnJNZTekdRKwHRLel3eSjSNp2A9pJgsY4D2nbv5ffyP4bgeO6vUZ\nq4Ku1gKDzuDtC/7KScUtZp1rdXxInuyUZkBi2OlPZkJ1sMPKtbjL+uuLLQ6+\nhdvHbZQRZrgO2bzfsFsfmVSY/pUQz4cs661C+nRmnrjEf8JE+Bft7V1oRrkt\np5oTTs162uvrPFTW+pRLSoaawdkQtnq2as8Wi3FPDPL0XnCANKSP5BE68aG3\nJQ5kXavAIc6TGcFUcraIcRXuaga80EQBG7DNK2IspFEbr8QJ34EiNOiCtCoB\nBHDanKKChOfKBdWke7GMQ5d7NAx91ZjSsEslYCd70yBgTuqgUb1vKtM64Nvx\nHoRpVBLggWMzVLNAsDWycTCS8PBAtMV8UMYi6pyxQJvOkDPyv3//4KZyuz2X\nlVb0\r\n=aq4z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.9.0": {"name": "globals", "version": "11.9.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "bde236808e987f290768a93d065060d78e6ab249", "tarball": "https://registry.npmjs.org/globals/-/globals-11.9.0.tgz", "fileCount": 5, "integrity": "sha512-5cJVtyXWH8PiJPVLZzzoIizXx944O4OmRro5MWKx5fT4MgcN7OfaMutPeaTdJCCURwbWdhhcCWcKIffPnmTzBg==", "signatures": [{"sig": "MEUCIQDjnc/14Ib4hFypkLFzHDSQfAHDETU3hTBVB5d3gVbP5QIgWCggXBDqncAjmyQ0flJg8vf5Xouv7Tjj9+FReMhEHYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6SX3CRA9TVsSAnZWagAAjw4P/2O6dMKlR7rlo9GPeIrH\nFRMOCmRAPtL+1kxHhdhR7S5nQ6EPuH25iPJt0PVxpc5jcOixrwnxJ0D5TDIk\nM6IiMA6i2y+3N6I7NIv9cDqeprfmTV+K5dFT4+g54sDeVIuPvo3yCPqvKD+l\nJfXsMbxQceoTrEkcvLXMXoY/0kfaNkr3qsPkzt4ddwEaG8NHY4hJBMTnrTAK\n8dTqtQkEICN19+XElRyC7g1ml65oSeEpSijluREp8kWYwsfRkdliGenDoIM/\n1sjxtQ5Kj/z0XMBjkcDuUE0eJUaqNHWNVpMuVlOKDZ9e3lmgZEEP6ufhiVsD\nPtLokqi63eJWInephekG4LCwKvedVhwfgsGr75+on6St+psxjn90qEjp7z+c\na5FTQFvy95YdMBYxsGHCP7z6IN/HW+4rvEZKhoR6sBNDkrRGv+JdCqMfYbeq\n4n4dKbedCfTg9p0MYsgstSsLNn+eYsjWiTsgChMY/sRx45mlM2HzX6FXQBeQ\nEpYRqPxAiZ78MAheIKQ/ke30IntxlcqbhFToxxOiYV04ciK/HRTCpv1vBMH3\nr+Y7jnrN+FTTRiKcB6gPcNPVM3J9VROXE+WEg+7sLkIufoN0wDu/JhPus0u5\ne/8vinJikJWvNfiCjCwPCQE6nPC/+ydghnAyVfPJdXfllrSYDV1uxv7eqser\nM5pf\r\n=xevD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.10.0": {"name": "globals", "version": "11.10.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "1e09776dffda5e01816b3bb4077c8b59c24eaa50", "tarball": "https://registry.npmjs.org/globals/-/globals-11.10.0.tgz", "fileCount": 5, "integrity": "sha512-0GZF1RiPKU97IHUO5TORo9w1PwrH/NBPl+fS7oMLdaTRiYmYbwK4NWoZWrAdd0/abG9R2BU+OiwyQpTpE6pdfQ==", "signatures": [{"sig": "MEQCHw9smNX0UILO4fF9Cw1Wvg3HjgSln3S3nle/hhzVwdkCIQDXWIA9v0P/hD2wZktj68RC7vZGBW2S4bV2n+poWgfXSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNYM0CRA9TVsSAnZWagAAMb0P/3y7alo0Ik8MopNLOhbc\nP4snL3MKYZLVFpNo7xphoSq37AyXKAzZaDx5kdDYhqHoDnHhfYBNXkm2OSjA\na2JNhTOgqMSOPZyovC5Il/G4edGfta/FBRkt1NM3lNszxPzdpjBt2AFY2YaC\n7xDFmnaR+EXsazzEDYaJqYyVaAghOKDNDnnwaQ7lgWdO+s3hfiKfxclgs+/T\nTxZ+061WrKGwoC5/3NflcTrXnep7k5ByW/m4VaAFDBnzLvUe65oyr5te1obM\nXnRZi8sHEGOtwZOiOgC9PPbTV0PIAKSoZtrEaJZphR/cEe6Rvi5KymWOK0tN\nvTmNrs7dp41IwtrCcO4ouf9y+Sosll5hBhw94gnALHxMU5O0GVSFjqja9Wu3\nJeW6gNWMJmgvDHHGiGBnN/soH8ec05dChMeqiZxN1HZWydwjH0McUzczOSw/\nUalJnyNpT/3lBkzY+cZvOvvP+fm2tTBG4IlfSzdK++Rv3ZjYxvFCu/bgNmef\nwGKEjdWQRD8ZaZdDnzWc8l6o8reRbNvEdJOMsKhF8VDFr3gLO6y2uY2pVRiB\nrkkui0r0rAuz+LOwjiircGARnnrDio9kCjE9HpYeXsf3C9Upl13ekymzm41U\n26DoIPGVjvacNozGQQaHl6oBF/Dzo7//lBPuZXrLwC1NuAMJHGmpckn4rnCH\nP+OV\r\n=RUsI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.11.0": {"name": "globals", "version": "11.11.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "dcf93757fa2de5486fbeed7118538adf789e9c2e", "tarball": "https://registry.npmjs.org/globals/-/globals-11.11.0.tgz", "fileCount": 5, "integrity": "sha512-WHq43gS+6ufNOEqlrDBxVEbb8ntfXrfAUU2ZOpCxrBdGKW3gyv8mCxAfIBD0DroPKGrJ2eSsXsLtY9MPntsyTw==", "signatures": [{"sig": "MEUCIQC7eHIn/sqqtBYBj2NQShtSksktdDhXSU3zKf47AvmdPQIgdCSzRqDA5v8mBPjUQw2JCpLFjjGfv4ppgyV5+L+GyPg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYb7cCRA9TVsSAnZWagAAr3AQAJWvrW6MxiS3dsCfaWCk\nlHkkRVAGAxgPrA6C1TpMLT8vQPS1adNUf1Cl8r5vgYT1JHKgk8R3fWPLLyAV\nBf1tDN1DStaG9nmFWuSEA0kaYjALn5CrI1htMPpNxsbLR/DuRiZ9SnIp5N9h\nD2e4bx7RbjQ4wEmBcotHnqVHASttMT9nwUDtWW8x84LKdkDRdkJz5IfSUa0X\nOSOr6EQlhhHw2e1f6aQ/A6VuJRRaKAx5Z50vlrdaMNroTUWbgB6D1guIFBNG\nZFZ0/rXJnc69X+7d0A4PpBFJ1oiZyLbhMPDavPkfaVrGvv5i88KHjFRmq2fb\nbzbVnQYzvriACP5Fm9G6n8N4g8C2T0+dnfXetlkH/lH+T1Ng2v0YXNNvfbXx\nnq9iMZpPKDwiVzNxu2S8inV4Tai/XYPzoK47HHFAThGi77kv/efcRUiIMFKN\nIS+hw5vQZ18zrpjrLyJsBgcdxSZ4EAyiyTsh06/LSuQ54Rmc6DkYFxMLg57Z\nPVuLLI1FJyQBUwgx0uO2TNbQ/9aiY5GH/qQUg1XxdprplY/yVXLlCp2tRDHk\nWlw0UrX2Ok1iUnG6wwJyWUExqcnNylsc9e5L0Be0MXRF+si0UzMltAiSrstJ\ng8sL98g6CN+k9q+1EF3Vo0AaNGVQoXXsOKZXY5VA3rOfD1ru7ayap8bTttio\nug6K\r\n=ow0C\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "11.12.0": {"name": "globals", "version": "11.12.0", "devDependencies": {"xo": "0.18.0", "ava": "0.21.0"}, "dist": {"shasum": "ab8795338868a0babd8525758018c2a7eb95c42e", "tarball": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "fileCount": 5, "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "signatures": [{"sig": "MEQCIDTc2l0JdhmUwsgHBp5nx00ZlLIi3BjZ8x+AIQkuHGY0AiBLIlplFTik4/MVECOXt2ULXg/rEyURUvRtlFFZQSH8MA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyDsKCRA9TVsSAnZWagAA+6YQAKKdMgYFu6mbTMJlwrGW\na/KzdxoLfRO+h0zznmxU/iQ4mOsXI2QaDy+mQ+5hFDczu3V3z53T+gR4XCXF\nHEpPWchwPBzvRedh7/NaGMibeHsDJiywuBClDn3Xj5m8kqa5+BqKHp9qDdZM\n33+h+SVhwyfsCPkmuCHvCqjxxSuvqArHihoVgqjoDj53s1U/de2vTP0A+1kJ\nthkaDcx9IFNff5Vd3JbQbt5JuFy2R68ZtpwFJTQmjLd+GECMiwni38JBtcXr\nQla+xFEdLTEFBjuM681MLyGXhtfjrXdWhR5hduVLmY1LjsrErSe9jdGWaF89\nknN3+AD2sllZ1Cno/DV+ud/SLpqboFKNYz/jqTyE4TjOzPlJS0uCJ8/6MsnN\nHo22YXYcI7ilpagPxiH5rJ7/gFGo9ILaPj35sc7Igp0Z9dGtXMIpkVy5P0Jd\nVFQOk02PHRRNBJ27kdrSNNlfjXq2BdTDXw1DPdrPZaL19y45UZElv4+Lohwm\naOZf+/e47Sr6JV8pFhK+uIU390chD66neyWIU7whVnu52bmICR3YJYIqmMg0\n+6UMTF9UX9F/hKxdv0wlFCiLlRGW1zYCalRuZ3JjjsIy91N/X/va/SfhdG4u\nv4jPqoiWIQYQk53raPDB0LgPv0sSNhq8VVj5gRLzmvk/FUoiTNabET8zy6FX\n7P2Q\r\n=p4UH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "12.0.0": {"name": "globals", "version": "12.0.0", "dependencies": {"type-fest": "^0.6.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "tsd": "^0.7.2"}, "dist": {"shasum": "99c258ace9f3f75a5efe7c01387e2e41890c3dba", "tarball": "https://registry.npmjs.org/globals/-/globals-12.0.0.tgz", "fileCount": 5, "integrity": "sha512-c9xoi32iDwlETiyYfO0pd3M8GcEuytJinSoqq7k3fz4H8p2p31NyfKr7JVd7Y0QvmtWcWXcwqW4L33eeDYgh1A==", "signatures": [{"sig": "MEYCIQDTSzZeZfjzPeE/WvJzg5I3E7V60OzRDglMWOrp5J92QwIhANmmraX1ZPdH2PK6pTLVwB7f1dt+6tr6rMtQ9ULci+4J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNIFqCRA9TVsSAnZWagAAhOEQAI+9MN8h2Sv6d77iQFlS\nYalWeYvZ9GbP6TVbM0kpgJYDrP1HQ4tFNKb2vSlg4yEq/dsfzoEpL8ju1nBj\nY7VYNrLAUqukdQRpJnazoCw7e1PrXkrYoipBn1kVvLjzZc+F8bqkZbsLboG1\nMHRjQ0KUFMy0g40Yafw10Yk/qVDudYkJgUEwz7PfX2ytsK68jbbuHObp7l6o\niG3ZTdOZXyTGZ3PqmQtV9s+b6QP2Q1pCF/Sd9E2TrHJlJvNZgTdOvFnddipf\nGoRNUVi/axI2ICsNjpNYemaBqNynRCvuWA4SXw8OulwQ3t9+P0hz32zYL5UO\ndg4IMJ8fGIKUem+39J2hgd9h4F6btukcwsA4tJqhbUm2rpuayN/mjXydco1j\nCGc+ddQQpLD2nMR+po7SfqbHmgK3sXt70Afbf5u+eOMaOrcm6cN+a9QPAUZ/\nhcXO2sdAB9xWML/TbsH92YpgqTuybH7w+nDk1OITyW+MNTE8yVmvAHojkJLM\npYaJE7ZBLCOV9/2Pt8v//uCSkrlbQOalq6qVwQvOKMmFagHyw/a0e3HONSOu\nIENHyVgdGf6RhA741PBAYtY7pJffJtq8eXOFNH+mIW5te4EImFyge5UYy3Lc\ntTWu833R9ILhjNmZUqdtYtx99ZcB7qUzBuPHISe2e+iPw28rFXT4H5fKor9S\nIV1y\r\n=UU0e\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "12.1.0": {"name": "globals", "version": "12.1.0", "dependencies": {"type-fest": "^0.6.0"}, "devDependencies": {"xo": "^0.24.0", "ava": "^2.2.0", "tsd": "^0.7.2"}, "dist": {"shasum": "ce672adc93cb3303513ec62b5b97bad510db028f", "tarball": "https://registry.npmjs.org/globals/-/globals-12.1.0.tgz", "fileCount": 5, "integrity": "sha512-GQ4xcAfbMWx/Lly8PUHIn8/t2o7YEoMWnQ7nhJtjEJ1gs8I4Y+koc0GiraVMaSjc9Ghz99obkMau/tSK/ACEsQ==", "signatures": [{"sig": "MEQCIDXYnS8n+SlilrVgzpL9Pe/VZjC9tRGqhraeRT+C15IQAiAwm0b6BjY14Z0e+aQaazcYLQOKAT6U/9eOpBnnl/DVSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfK4yCRA9TVsSAnZWagAATJUP/jSdy9+TzQCqHCX2QP23\nW2xrDkFGYtR0m0wOQ06kShK4B15jPHl6Pd7yRLL4TQjCAzEYjre9UuRarmsC\nc1nkNorqjkyjjnZQIT3BxsAlrAsSO5Now/vb2NamHWaI8x9QoQmR2/EbVnlL\n9J2vqWvbIQZvnjh/os+z76wLTFC6Cg6nsYw6z00p4YVLqj7HDaYUDcWRFFZU\n2r1ZPol3jZSqdD7FKTf/ThLrFp6h67bjJR7N4UOqd/+s8zLNze1assS9f/zi\nMPIJKY+bowOcyBvD5q9oj6Zswx52lOS1yHcsGdfOPPKI9vKJcBC4o2TvEXIc\nRzhNpPTlo+Pn5T41mNA9qkBgvOuUnvplcX9JWoGBLcbEikGJuGO3vnie6VzO\n4S8Q7PwYYdADeMtv8iPLShGl7agLaprldWpKGK/egtofxJf0TS+Y5DQcqvvk\n31LqO6Joy6bGLIanfp/yj+f2QOSW5px/znB5Z+K/LxEEmjTbWllZHboJ4jNe\nl2URr8hbKJ+9E/9y653IDXsDXA+7CcQBAhl++Pz2/3e8CpCZ40k1othxitBr\n2Op04T80HL/58yuvmqIkCaT9IHZ8fLU6IgAQ5hNbaGu19WUtTAQtoHbDl6Q2\n/DuyUS30V2LdIZRWl9n89BG0IGHCCTR4mmFezctLW9a7FKgmw35gRgooCQZK\nTSiB\r\n=+e0E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "12.1.1": {"name": "globals", "version": "12.1.1", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "143936147735ab224e5317482fac9669371363a9", "tarball": "https://registry.npmjs.org/globals/-/globals-12.1.1.tgz", "fileCount": 6, "integrity": "sha512-i4wvLF+QFfPq/gNA1S8dL4Z2f2Cb62ZvxDhj38fZIProAfyUidDmUQILIg1jc5iwqJr4PVJSUB5usYvFxSzg+A==", "signatures": [{"sig": "MEUCIQD27zAx45QSOaSj7RDJAHN4bkItnlsWBpB0qGeu1JRoOwIgPIyqSexd/TqtsTJFHI07C9eKgCQqmKEpln0hW9CJ4V4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnBeoCRA9TVsSAnZWagAA2eUP/1AQ2HcqCOZvw0GvhUAN\naC+2imvrekYFk7iKEWuPPzB592sV0iOeosbZpfRsBfIz+xMp9Cp7NrnvMmKY\nGTYhgIDcoUIcKkj8IQy41svHBVeXA/40IquTTYNJvPrx+U+GS1iMgChO3gcp\nKhTX9pi8RpSjB3NVwbY36HAxdv4RzttpYNFuCvejj9PboiLMDGtWOx5F9+1X\nKjEvzyNklKg3VUszyaUr/P223Y2N+UZKaEJoX4etMxIkatAixC1uFxWdEiT3\nSHMp37Jh56yheJc/Lq9E5zqmPa6xoNJNHE3P2DuGshPQQEFLGfPHNBEBpFRI\nj2uzKZWpRHphtvUAy3bPLF3//YHM5HwZa0QiWtpBsRsvsauvOB2ZZoOh+cmZ\nHPBkax36pyzGFz90qGFWf8OqJFEIbhX/bM8XcXe/ak8sS7FDm6hps33Meo2c\n8uW9HKYaMmZy1vSu7b14/N4WeOt4VUYVIho0RBBJqu5b0pbyVoGs0HgRA4wI\nCWwWRGSYqjLsWWODojyJ6ZE2Ayu3N9+9bb1A15y+ZEBU88GRFQr6yBr/5kDu\npZHvsvb6ZlrIlmLc7G/dRUOS7rnCHpTU7tfREN+IF63PSBDCdXSvlsUvrauJ\n7g5GlLLu9kwIwkvw+m4pRqwaEmYV6AWlVxxXmZFhIYME++xlG4dVlqVj1fSC\n2AXc\r\n=3Ty0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "12.2.0": {"name": "globals", "version": "12.2.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "97e51144f6654892545009d60c5d9ee12d9396f7", "tarball": "https://registry.npmjs.org/globals/-/globals-12.2.0.tgz", "fileCount": 6, "integrity": "sha512-I+vilMwxQTALVcfFOHtBGoALP/3IERMgNv5ZOjAzknz3E4AF5BBxxbZ2s3yphNfC8TgjMlyy3yGOwPqPETUVtg==", "signatures": [{"sig": "MEUCIQCG8UWI8889KqY41BXBI/f5qnfX/sZ5lZnt2XvjtoG2yQIgI3eLKS8MnNpzDlnN8Guh2zpy7ZoItCnev2sv+/mm7UU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy4tACRA9TVsSAnZWagAAou0P/2oaPrKY3iSYVN/8ZUKL\nZOBAbgF0UFk/koGYvUDmoAPo5HB8aGDnD7y2fepjSqzguMcuorwKzsxeoBOK\n4KEFnSdrJJbYYRpvnNrJbs5Kx3u0yuTIW8KVFN8schex4Q0VOOg93yekeMfD\nVAjYsfg2vQz9CHREjYcG+KFvgKpq1VSea2UFbp0kULQgioi+RnyEDiF3u0vH\nt/Bt6JSlaVWNQ+szcKtacwC4N9MAMsSwTp66gmYy677Guc1fPZlfuaW7dmNR\niKNuI7BGjFtCHfGyqsHg8ascPoJXJD1FAlktHtamqvejAVh0Rw0whwgTiuNY\nU+r3FsLVvdrPkj0oX5CChprdvb83OtTPNamfPDaiTF1WwFGkScjYhqqJY9Wv\n4h9oLLr+243UasBMBqrYWfXy/BwUDCXJFKHcufNL9a9Ptn3qBX2y3H+SF+7m\nSDQtOVp5klFw+FJ7TKWYjgCk0yoTKZj1mbE66iwseViGP7lmZPq84eAQO/0M\nacMrx6vFrYB4pOH3pG+sm6DEhmaqE2Q0O/hFQ/N7LMUuTuJJkfm6+bM2QD72\nmkKYEUBKz/8uZ811l8a9X76m/YdaYSwzZJfnKL63CBWQaorH96OeCdyk7ges\nXjQG2uvaiYW4EGe5/pGlnnWaEWX8kl2iSsi5PKTqg0Mxb9al+K4BFXgWQWWx\n29wB\r\n=clp3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "12.3.0": {"name": "globals", "version": "12.3.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "1e564ee5c4dded2ab098b0f88f24702a3c56be13", "tarball": "https://registry.npmjs.org/globals/-/globals-12.3.0.tgz", "fileCount": 6, "integrity": "sha512-wAfjdLgFsPZsklLJvOBUBmzYE8/CwhEqSBEMRXA3qxIiNtyqvjYurAtIfDh6chlEPUfmTY3MnZh5Hfh4q0UlIw==", "signatures": [{"sig": "MEQCIDQ9NZB2ER81CNgNAk1+wu4APUNbtLRx888jG8AAm99jAiBl6/4f0f0GiKM+B7vF5o+3rhBeaCU4OiD0uHkX7AXD3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd04R9CRA9TVsSAnZWagAA62AP/0g59ykkI2klJcUJHWRv\n4uTgoPouYsgNtTXY3bky3YbVcpcL4KUiWTlZiwgYGFkcvhs8LGZvLKsB4353\nUhc+IJnzrMtYqZGGcblr2QjOJ7fPW8e1Y0HfAu1ZzAV5UnuyM4HrF2G+yPyK\nl3oLjW5MsHUCZyIWBH9CERGgGJI2C71U4QzD7jDMVqbgJGMBwKrihNTeHnYx\nQGOgZM2GojDbkExguiinc6NQ5X2NfT5keqD5QxCJsWIvJ9nvYusQ9VulvJkV\nsSsoad5fKiM2KYIi+6tOmPzp5VOirzRy6F3XdCq+bx5eDAWS9QWyiM0zoiCc\n1xealODP6SMl1BOf9kfWyoqGadjoMoa34Ih9v5F4WKQ7GvPAGHwYOyJGtqls\n+mPl6V5RFgDXbFF22cLR31ojjohKL4DeHfkpiK0jPxZV8oiJfrVg9ZXP8L09\n1BjAVidmHdmSCYbMmjpjq9eBqI9rYFY9vvKqyxGO4coYBIvDv5T8ZeIwONTW\nieWC8viTcWtEwNmMrr7BE2ZWFq01hizh2byB9txDx7GEMMsgYLM0rZ8/YKcG\noDFrufTmqL94CoynN6uwNeT7Qcc6P9/jeKtYK5KxHoF2x3Hm3WxeIyRKo0/u\nJKLpBW8PWniFOFGvjTzB5tWajx3ZPDruPrdgBvMZs0jAkhZZ2FC1p1ugX5HC\npJaX\r\n=Xtfr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "12.4.0": {"name": "globals", "version": "12.4.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "a18813576a41b00a24a97e7f815918c2e19925f8", "tarball": "https://registry.npmjs.org/globals/-/globals-12.4.0.tgz", "fileCount": 6, "integrity": "sha512-BWICuzzDvDoH54NHKCseDanAhE3CeDorgDL5MT6LMXXj2WCnd9UC2szdk4AWLfjdgNBCXLUanXYcpBBKOSWGwg==", "signatures": [{"sig": "MEYCIQCk1SEDGmcZPy55iT/JJOVaXmGxeIHaLvXKP045ibuE8gIhAJPKeEAV+LeOunIC2g0q8qQO56paBJRhfOrwUldbdeJb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41711, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeZgzHCRA9TVsSAnZWagAA6DAP/2m9+whkyl2Gwcae7LNA\n2eK2HxnVth/8E8G8q9k4BS/TRGUl6ip1lk5GMY/ERyzsDMEZib5VnNqvP4Nv\ns4t0L8/FCaRhvP9+uDplOy0/ROST0go3gBfi7KmpVan9C7eEPiSm3Nvq66xV\nBLquDNCehdrsUQ9d2mjUPir0zMORj4kYN8vxpoGqmpi+dJ73ECFnwK7qlhpc\n1uJJHWEh8c59G4GboxiEj4we5pKjLAUYLT4YJkDp8W9dbLPKKTTdcreEZCL5\n1vSU2sVSY8/HjMbZ3D/A+CO0G0leyZjlKqYijqVtCdssVXk67h0oHQyj7u1K\nLKWSSMfV1MDG2ygsBb49jwBwCIfV0lOZepPJPTiYFFXUftL3d5fZsLC6kNLV\no0CDRb2K8EZw+b5YvoERqgYqJxo7Cuy9pbcnrWe0OyMN1m/BceaYq+aURv/P\nEYb/r1UPCRcDBXyTTcj1C60N2ebrjVudzQITsX8to/WpQBDCwZ80Xu20jFmD\nKlUsHsmksp8I2X/BhJ86Ep5RNdWNt0ZX16x3HasvWkgiePRMb9wqwCFpB3dV\nz9UkWJBXQ+CJUBkg/2Ah3vyNXf5XQyW2+TsyOGmJ4M1EQNqNZKmoXHDdgSxN\nSdUHYrdbTHJYkZzSU8HlRjzddelayDD2noSTek4xOQW2lYmuL8kGxpD5BHI1\nd+12\r\n=NkiT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.0.0": {"name": "globals", "version": "13.0.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "34694ea5ce49ba46fde3f110e162586ba58ff6ca", "tarball": "https://registry.npmjs.org/globals/-/globals-13.0.0.tgz", "fileCount": 6, "integrity": "sha512-TdLO3ykvHpNsJZiMr+RfkwoMI+XdDaFbH87w/UZGrSzNvZ5cUU6W50oPKSDZgb6eoMWZ6Pltf5Y3oQBpKLUSkA==", "signatures": [{"sig": "MEYCIQCBs31IuZ2BkXkJMGXB0aUHoRqyZuh8Id9LmjrDAxt4VwIhAJ9YxMXR3gpyzu0xuvnwn8PcfLl3ZxUYFaIauWkZ9Cnb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekroCCRA9TVsSAnZWagAAvsAP/A1Bav+WlOhRojqwNXoM\nw7FO+qcydDlG1z2MXey5uEwc/ikD+JK08RbGLbGYjg1d2v+6jHyRsjGN7P91\nMGAHOwbMZFEBFfW8cz6d9mLUevwOPUOaDHx/L42mS8hOvYGK5lMEQrU2Mv0d\nMOtesdW0fuHCOko8rReWoT6NY10ndBnH1LVY7rvBuW3WoVsWzqeMuPUQHS2H\nvjHn1RaVkbCpGbH7871THjEIjlJrzCF2RlyQeVgfkjfvl8Fb2kA3JRfLB2oB\nwhhB4pp5X6W+Eh7kHfTPXjPCqkJPhdKZNIfXHlEtOyGcr+2/LdoC2a9uzqgC\n8wRDbuKI5aF/pVQD+Bg1gitHRQdvQcxVGIlQ5SJ9WhNsjkpdTGyH0XDLvqOY\nEU8ABI24etibocy4tP39lhowMOS3VFjuHcI2qvUhTjJ26Flb35dY4Y2d1mAh\nWu4sj7VEfoGL1IG1mjTItW+mzwN2TqMKRB++Ro+X1mMRJG2hoEQo5neWObby\n2gki9Twzm9aOn3ntwhhF56bbJNLYj9r79kOdPQrmKPclMDyj9IYWCAiXoOka\n9Y0AIEFyUqi9lDZX3h3VfOsdkTg4/5XxQiaUiUv3vM/YD1sQkUbr87cn5KmX\nZNM+WmWxe4/ceqMsDjP5Lf3TsiN+yTNPXjfUC+cjlAp/7Za3QNsus7xvL04g\nl2mP\r\n=XnGu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.1.0": {"name": "globals", "version": "13.1.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "ad4f301f0e55e4f72767faa01a69de8fdaf50ea1", "tarball": "https://registry.npmjs.org/globals/-/globals-13.1.0.tgz", "fileCount": 6, "integrity": "sha512-4N8AdK8YMcr4nLOUsCP62jhMVAaJVdrEevrmuqHQ/TTXCXVL8ywhd/whKrufcp1zGtKBqw4DHcvsokQ60khOJA==", "signatures": [{"sig": "MEQCIF8zmfv34z1iWVtMX76iK1Hsbxax2L7YeXRwf0oucrq9AiBjagkDGQl92FlipUMclpdfhzmtFvb8gLbX/IQ1hySadQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41920, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe2zv0CRA9TVsSAnZWagAAeFwP/3iJob843vQIGsnhyDJX\nP5U16ABBRE+OZ+9LSsFYk6wursQVLgyi0of4D9CIPIp/MsidxtVnz6PlUFg+\nBIZSe7kbHgg3oHBxRk7WFGeeSDoEHwYWKnO5JtQkkXiSh4LOB5VAYjh46KNU\nxNSrRHbTi1Wb0vVVEjmXOx2EMT+9Urxlx+OGZ/sdC+dGTFVHOB2IPWM+DqnX\noqAFbRYn0mXkWDK0h9GmuAAS8kCP3e2jSF2i78qckswowJkT9g5G/PJ8lqDF\n7JWK8EJS96smPHrv9trGmMzcIalROAAK4Pn0QZyWebYhzCgZehIkBv+4tUH4\nPYUo2YJAAAiA7PbF8wAXoTde35SMQjMWwqj/YvLrHz9KUKH0Gmx5wT876PvE\nZU9vRfF/Y38sokPsmaPYzUQYfFVYeMnKvGcU7+DW4vCeprqm4XKj0d5mdQti\nZwf4XfYBrS55F5zesDZ+3IfFy6eiCvyPlj9HqGoHLEFy1eusi/HI9tDADihA\npu+7L6H0jYKLc50E11qhgXRnNB2Wne82zr9VRnrTPlGZVONCDyVG0Jvcfk+Y\n3c0718EhUTsR4O7nzdRm1k0Lktv7dpbCZ4IF5EbXlmq0J7BiwSsBXXsiUz0g\n6rrOHmc30rRPx8aATtm0NIU60ziPaWv6JreWZ8pa49kAjbXdQ2DSVF46QjCb\nVmHo\r\n=vLbW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.2.0": {"name": "globals", "version": "13.2.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "30b6814a1b7945d0b5b3795f81c275342bb17129", "tarball": "https://registry.npmjs.org/globals/-/globals-13.2.0.tgz", "fileCount": 6, "integrity": "sha512-OjvFbGDlR7aIsLqFrraxoqIP3bb+wgp+Aarel5S56lwS3se4uUrwKkChnv1MqsMIv/Opexbmu7qCfHL9T0mBEg==", "signatures": [{"sig": "MEQCIG6/eAWxzhxMGSoJOH0fQJYFkSt/LKspR7rtZV53gHYHAiAYxN2/yzTVRWKYDW566IDJTII/9DtBOKerKG12ckSN1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfK+APCRA9TVsSAnZWagAAyDsP/iVJmI0bWzTG+CnIwkw8\nBF0m1D8kxdmNA98YUScNJwbG0aMcemq/UaqYLJsX2ZUL7j+i2dVqdvP3aPzx\n204PiQL+bNIb3OnuUyRW293Gf+wbRaMzBisyt4xtoKrXMuMGfdn+Z/XCsPbu\nmVDjDqpcwnoY48jEbtuvrUDMvn4tWpbXoE5eb19dna4thJKXRtzRAoJZJRAY\nX3BAzgJ5bR0XqGz9I3fJmd4MWZcc+CwieCJpSPoinojdLENYtSIZuT7NJnvB\n6YxXwGnDhrB6TaUAGHF148JX6uu0y6ehFz7xlFJesS5hG24tSJIu+R1mF9cu\nDU0vqMqEd7fYwDbwQparHVqg4JrjNt0ItV+z1W54VMlzyXdJJnXpOvV2nQzA\nDDJzZ8nNWkzemDBaErH+Ngz3isz6vG1jcQNXqII5KaFLQqrGLrFQdYeE8mfU\n6edcGRKCc+xEY5NPVsm4qKmTG9SOZhwzbaEIOgj/DJUSMJVhStb8y7i+hn9+\nCJXqmV49Giu7SAsFx/jIFpq9mQt2v3NDxsBTK4ixZV2Vr3Aue9YE+prpxykJ\nI9V9WcAsjuZl4P0Hghc4P1lqtm6KE+RVlwFHzLHSc+nuAO0SPhqDzMNxz0gX\nHvJBQ+ea0concPK/0MxOD8MjwAbdkEhzVMkRiM7dh10OFB0ht1aL8B8iOj7F\nl24D\r\n=AI0M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.3.0": {"name": "globals", "version": "13.3.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "0d47e8bd06722d2eb90b77040e9a9af0615b51aa", "tarball": "https://registry.npmjs.org/globals/-/globals-13.3.0.tgz", "fileCount": 6, "integrity": "sha512-RjuvsMnQXQWjVGClrHIVdKOkYZcP/4UrgrZxIFdEyp+NvradqD4bNtPmtTn4mv4NMvVqdFCzaJuGGA9QpKjZmA==", "signatures": [{"sig": "MEUCIQCK+lXMjchJyl7yncFAA0t4JKn4YUR89QVu17PtMXDbiAIgU/BUlcj0+9emOpQYBjB2uE9agWKfzet0lt4yU12KCo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJflgDgCRA9TVsSAnZWagAA6wYQAISA/0HVzZut9L/qoTL8\n3axSPXRJTCdfHTHpmr5YJtGkpWasvICvCsEhh5Ppo4zQT/vGLF1AmeQ9Ant8\nL6t8oF4P2s0XEhPm1sKRyP9NinPWJDiv6WPTQPPqCR0y+aaUV+YICcKEjzmP\np5qva0gEyYgsyOg14PZu6EV6xSCGiF4XCayeQ6PEJqtQhDJ5wVGi66aI/D1C\nbA6tIwj9j3y8TkQ7TEKeman8Z2G6X3AnYsZ18kSnJMFJbijWY88MFEdl5s2l\nF9jBmfv5UvMKCVCPIR9wRgFz6OdjuCWkgsA60RjcZTsQybPYkfs8tOVEat6x\nb4zoDkmSpOQpNcjptQLVDdkTZQ7dA6mVRysuaWEzyL8j04eQtlgHG4x69M9a\nK5yklJ/wsf6EvmDTKeMhY43GxfE/kpeLWoA4+2tTaV7+i4g9PCr6sG6xxeAg\nFS5I9WBXScvU7hxSy+TRHnXyTiJaR89SZpByNJGRvGVRyyba9dUhh9kMPS58\ndh6IV94xuTJsOGqn6m3oTQrniK9p0RpYNL4vMruIuGb1YYSsWu0oefRugglY\n2A3euOtI9ug1kDPeoPofJo3ExQKy5AI/2cImltgLr8L71qoZCdA/IPgX2/8K\nn1ZO0QEmMGYO6XIGdBn4oBBr50Gmj67J98cDjlQ9bfCj3eQPSf4k7dDJvPlZ\n0nSV\r\n=Indm\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.4.0": {"name": "globals", "version": "13.4.0", "dependencies": {"type-fest": "^0.8.1"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.2.0", "tsd": "^0.9.0"}, "dist": {"shasum": "71a82fa722f250f5a0df6e67a51a34cd0864c1bc", "tarball": "https://registry.npmjs.org/globals/-/globals-13.4.0.tgz", "fileCount": 6, "integrity": "sha512-xgOaIvY1x5K+hlRg5PfmLGt9I6d3cOczsxroFNSouMsrAYnuvHnVFM3+TfmfJDOAbu3H78ihREACEAmSCHWL3g==", "signatures": [{"sig": "MEYCIQCZ7Tddz/wc0OQrK/AkpKei/rwcZHC5vO0rwnmyohTNbQIhAKdl09S2gr3T5YLMCoS7KGwqTfpYUQtGTOph2reO+EET", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzzYvCRA9TVsSAnZWagAAf+IP/jT8qM5RJgBJuucqYsIh\nqa7/MoilSN2OGyZ1SnoUohAl9hvi5m257QIWJd6n9yHk6McHWkm8fiKwlpPn\nGMTGPyRDhAy4aiHc4gogDZoy5NdCA7b9gPvbQwCENvOkWbpNjMqHf6zI/rqT\nAQMUymZpLxRXPBiPasAb8fdAMO3w2aaZCqC9mUablk7TDfR/A4w9CENALV26\n6LsnPIafijaXiW4IVvda2v68b+RHMqUTRgVHfBBwoCbWD/BSSNodETCbY82x\npmFT43bNOCFFUTUpuGygibNXdxJJmgdl2NOphizJcl/bjlfz06oJOX71UQZm\ndrqCo4zUTluSNi0uqymODGB6bQXj2C6xh4BjHM1fq6fl0hUL8MdKc5PZ6oO+\nLuYuY0Su2pnBQKdpw8ljvya7fa1naaqfTYYCtTzkzw/kipjXl/s/T2LyQmv8\n5E3/TAd7EGFNiA6iAQDgW9JKWzGV4MOiVydeEazJmxnuxnv6rNxIGXsi5yqr\n1CussPJbGQH5KFdLhtSyYo+tZ4jTdEmc5RwSLX9+55HEVZqpE0+qan4mRxY/\nzkWasM3TsT9J9zhj7NDrkeurDxYBtRorWbkT4/MCB8ehqdNn36HeOvlw7FJt\nKVefQkHedp1Arf5TP7OhO9Mtmf5kMSXFVSW4wlHvs6m3t3WpYBDbU3HVNhhQ\nKre3\r\n=czWM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.5.0": {"name": "globals", "version": "13.5.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "2295dd5088cb0ae75a0e0207386fb4b35f71b208", "tarball": "https://registry.npmjs.org/globals/-/globals-13.5.0.tgz", "fileCount": 6, "integrity": "sha512-TMJe2Iu/qCIEFnG7IQ62C9N/iKdgX5wSvmGOVuk75+UAGDW+Yv/hH5+Ky6d/8UMqo4WCzhFCy+pHsvv09zhBoQ==", "signatures": [{"sig": "MEYCIQCHEwDO9G/+Jep9wejJFtpF7ZEtvkuA/xqiFf1nGTAtLgIhANrEkYiXjHXQGWC/SrFOk3g3Qg5JH9Cwp4cBkbBljgHP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf3tnUCRA9TVsSAnZWagAAk0gP/2288cNTuWfmXip2edUI\nYNPg0L142ruYL9BwdMgTcolh3IsQIo3cIumXmQlVyG1WKBvMvcNRuyhU0ETV\nK6FIuxNqYpuDf4ods9BbvDxOcPD3oqhpR07bvT2wP/jeOXmanNvFji3OI3q6\nTYRFnaDfoDnSFAeU4qiO3slVmId9KMw3Aa4QXouWGCL6WlKEO4Dndzvf4YwO\n4e2lp947OXJJyenUzlgo35QesAr6coGcyt4RyiOlxxgiknXWREHF6JAF4pjS\n70GvXEe3BFLNEo1FaEIxlHelQ4VAK5uT29/7iOrSJTK9Yy+zMfQKZtOgvr87\ngmR2s21xO4Mu0USL8X/9ZaYMavN0yyCQrrmF66DxWgWlrsmqk3sigP08HZVQ\nYnFEXZYmPLfgomuX4ji+iKT0JsYYbXfCv9rQ9b3C8+n+EGZSPU9k6gziQ/GG\nws1Qu9gzbvoi1eL+8EFkMhpeNYxbVE9n6h7ghzy1lTuWy0NS13/4aChsvM2n\nC7xTTZobHCpuTwBvn0NpetimWTzkhHr4lNHObwBMgto8NHaf1prVsn7tf6mP\nawP9TVpakPz4YMU9mPnQxknHr7yCcI/ZA9/lQCIcVYv/5aP+cUF2v4t8Wh9f\np9stglLdF0RrTH2QUdCLTog21x0FTdm2rt0T+n6LetrQD/Z3vTwunZMhCGGc\nX0Hf\r\n=HRvG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.6.0": {"name": "globals", "version": "13.6.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "d77138e53738567bb96a3916ff6f6b487af20ef7", "tarball": "https://registry.npmjs.org/globals/-/globals-13.6.0.tgz", "fileCount": 6, "integrity": "sha512-YFKCX0SiPg7l5oKYCJ2zZGxcXprVXHcSnVuvzrT3oSENQonVLqM5pf9fN5dLGZGyCjhw8TN8Btwe/jKnZ0pjvQ==", "signatures": [{"sig": "MEQCIGt2tL/VR1oYMGN30BOLpIGkdF5UUSEEvjypxiQ/u8sKAiBP+Ex5Vpff7CPEAdkVR7jPgk/pGidfzfUz9SNfodSjlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgK1eqCRA9TVsSAnZWagAAAk8P/jXuJG2PiJSmtShBq2M+\nqAApeYtr9kPtDpgO5eiPtq5G4AvRI5lK6+S1r2y6pncN125ZOqM2bWBetPQY\nySUt9PD6HUCm28InTnY6auzzFsJ6+pD9mAwjZkTNsjI/lzzJa9fH5vQYNgn5\nwvJA7wO7d1+Nisor1ozq3uwbgSrUkHC+zbKNr97d90kpH2D2nmeXPgTBgLUC\nPLYxVFtUXiV3OH4uYYpNWthSTpmmqudPTzVhpiW1XaA8xizMzoFy8bTzd9Di\nKQsxvw52h2rLXz0kGj15Vz2TQEOio96YLPMgh3ltpH5VoOGn2SBneRT7Wdab\nojGPm9gVNe76hIE45UdWrSmQdvDdJX64u/QRIBDfxshapbrgv9AqtGV7a5xs\no5Cm8n+8ZCFfjrFNRXNNjNyC7C9yMCfI+2VH1UGgOCmaTHykru8NQ6/rk+cH\ni6EGTBF7LQgBamA81dNeQY+ldhvwj1iGxoQuUZoEfeGdYNH03tDt2gse7Dgk\nrI2DjIUcjgD9wtJr/Aw7e2fHUVY/sSHvYUAdOv+TpT7t2J1lRWjGGdzfoQKO\n6uHzEEtI807G7wof/FGIVfICbmUoczrvggEh6jHxhPWTXDipDxChJyutVcnI\nl+WxgbS5wUFa5dQmLIQTPYA/GJsQ/w57FdawV5jWx643U5v0xSkeCPCrpVgS\nhINw\r\n=F0Bk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.7.0": {"name": "globals", "version": "13.7.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "aed3bcefd80ad3ec0f0be2cf0c895110c0591795", "tarball": "https://registry.npmjs.org/globals/-/globals-13.7.0.tgz", "fileCount": 6, "integrity": "sha512-Aipsz6ZKRxa/xQkZhNg0qIWXT6x6rD46f6x/PCnBomlttdIyAPak4YD9jTmKpZ72uROSMU87qJtcgpgHaVchiA==", "signatures": [{"sig": "MEUCICzvDorknsT06+oButmBcPCX/A2cjx/g/75GGjDcZyMEAiEA7wfr/GPx/+rcR2SVWRLRnqXqR9EfPIKcmelx7v5sF9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgU1pGCRA9TVsSAnZWagAAwjIP/j5cNjICdgsbgYVuMyH9\ntZCAYBLdWfaT7p4JGJ/592mBkk+nFMVVSuWJ13l9x5hsxBls3m9GiPXFNFFZ\nJbmRkyVqYPtes3rFqC+HXh+BLtnO55AlHuUaySA0C1AL59g5lnxUGeS4vMlo\n25UGulGXfcJ/h57ELXikPFstySPVz+42jfj3N5YLY40iiaYHD7/mgUiHaLxw\nVvkPm0MfTy7wePils3YXJn/e4+wBTBpnlmB6trX8rJtUnnwh7JYZ0MLztPeu\npPlaLjVVeeojp0dN9CEN0Hf4mbHlLRvKiQHYvOgVCJbtvquNVPm0XF1vd8F4\n/ci4GJjjRM0UxF5q5bXg6NLCQiMY8dVKzNXFihghq9M3810F37yfvOqjAMQk\nj3ehvTO069HJhthb/NakA+3k48JJxSPdbxhMJ4PPVc+kwMQdMHIQCPhAJIhf\noFIzpiJypGdwts9oawBRK+gfowrj9pxdKwhBC3DKw79w1l1Usu0welnmJ9ZJ\nC+RfUGpVFYrIN/MOc/685crCx5ClQC7+Vb9ftr30t5xvoyjX9H9Bhu1luG1+\njEHkTnTUL9ddScI0hqAblen8l/CwNDTo1a7rLg0zqayNmOpG8lLJFPYHb09I\nCRonHfLZa6qAXk9TllFJfIEXb1vJ/Ur2gmvuoavnI/EO8XApLkxWevvk0BG4\nYLCV\r\n=G6aw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.8.0": {"name": "globals", "version": "13.8.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "3e20f504810ce87a8d72e55aecf8435b50f4c1b3", "tarball": "https://registry.npmjs.org/globals/-/globals-13.8.0.tgz", "fileCount": 6, "integrity": "sha512-rHtdA6+PDBIjeEvA91rpqzEvk/k3/i7EeNQiryiWuJH0Hw9cpyJMAt2jtbAwUaRdhD+573X4vWw6IcjKPasi9Q==", "signatures": [{"sig": "MEUCIQDGZV3HB7PY/ADN/fWRaijHB6dJK5YVhvOJkUKjptCU1QIgR/GQEFd5e9CAPtfcnjlkQNP/O+7l+F6nzb2ivKQddO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44754, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcBCyCRA9TVsSAnZWagAAkgAP/1CMkJDUueMxte0Rlqv6\nPUXMcyXSaEnNZNQJ4jff7/gslKagT0DHMTc25uQ/NOBhh9UfBkBqlEc0JS66\n3LgVgznNMahtA2zzEDTzkkKpAeXAiUHjGmK+87CoQyrXgH2671wFBXwHQu9h\nbjuTDqrGzA0b0EdZ3E9rR9Yb2apVvLu8DJcW72EUZyzULg1CMUhhibef9Kxj\n7snqiEeY9Mv1D80EB0+LGd6sj+vhIPydPBW8JHer/0UYwHNJCkXjex2dzm37\n7aVxujtm9ACuoKgZSUl/27tehtlR9nUspi0syOAzoFlA27mR7PWz03evuIMv\nFSBoO4UFdTcg8loxJQtvGoNREfGOueo4XRrBIeI2SEfV2WAC5r1jr9V7o37Q\n66VV/786mtbc4zjp1YOgbLdktRUD5+43paadoIGKfmE9jkVMzqcv3Ecmtuek\n1vL12kARQkMcgeT0YXgg4d6x13Ua3GieWtXbYbL/1y+ZaJtZZqXqD93M3nCP\n0Z4/eAiXIz/V7Vv42wxiPqebk+9EnZQUQl3huFVpGrlfkOSO4JyajkNmq7ku\na2Nldw1I67p+ZBtEKnY4mcBNn+QgSktTDMh23ZtFWgVTbIjovefX6JbCSbb0\nuH7Vkc0eq6Mu+p2ypex1I/XxTp72kyDEllcrwhuiyiZDd45JUVQvZdHKRLLN\nM0wf\r\n=rJUs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.9.0": {"name": "globals", "version": "13.9.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "4bf2bf635b334a173fb1daf7c5e6b218ecdc06cb", "tarball": "https://registry.npmjs.org/globals/-/globals-13.9.0.tgz", "fileCount": 6, "integrity": "sha512-74/FduwI/JaIrr1H8e71UbDE+5x7pIPs1C2rrwC52SszOo043CsWOZEMW7o2Y58xwm9b+0RBKDxY5n2sUpEFxA==", "signatures": [{"sig": "MEYCIQDAO52wyMpJDfI8wVnfsWef6MkqZ9Omsk7iUdpu3908dQIhAPHZah1e9p9heJ0+dlTkqU1IUhb5d6KtwvebKQ8G0QLp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrxjPCRA9TVsSAnZWagAAx1cP/iJuyQFFhBRcDY+tE9FG\nFrUeBFMyVyazyXcX+8U/I/2hAnmRljwTkgcpsB+15U7++9WTnoJSyaBHnmjJ\nl2/5sG98Khx7mrYmBL6//LVFHelvE/8jvuudJupFEqEyYm8CLiuo3yDOiBmU\nz8zw271VnEZYTRWjHQNuPIyGhYfdw+vfLYTcdYeCMw6lIIMftngGNv9hw4zs\ntmR9Po3OTtE5yduA892o/hGafMVZiK0/20YE5YfJJdCIR9XorBi6YVYKIwDH\nKqwxRxBAlCXfCnNKuR+MXFwoKstsvffq/R3O8uNGh5mt98Q/qQLBEjhr7o5l\nnAleHNWqCo6BXV1Cpvt2PLtJaoYpMq7a6rXyyttw/k9p+PDO2dMyYD/Plkwp\np1m9ksScHOhSXSFdaYlYJPVFxZq6G3y3UZTOShi3pZOHTVIXDZIqMcYoQPRG\nwHIig9gfIjdbNv67jI0ZCAjKz3fpRT2+w3jK6Pgj03aYzMA2f0YdflHhzugr\nSjOtEyNVYC2fXhpMjBFaJl5iCL2/b2OZaY/X8jHI00dbx/C987SwJrh5oxAY\nbAYZH8WaBUr7efTbdiECUK+rZznr/DTIBXm6tiPE4yAv55N5U0dXwQ2MjwwL\nZ3XXZabDThmML3Rg3uqn3Xhrrj3ikY59/USUw8N/chA7avF8mFOFJdVaVIxg\n2/D+\r\n=hnW5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.10.0": {"name": "globals", "version": "13.10.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "60ba56c3ac2ca845cfbf4faeca727ad9dd204676", "tarball": "https://registry.npmjs.org/globals/-/globals-13.10.0.tgz", "fileCount": 6, "integrity": "sha512-piHC3blgLGFjvOuMmWZX60f+na1lXFDhQXBf1UYp2fXPXqvEUbOhNwi6BsQ0bQishwedgnjkwv1d9zKf+MWw3g==", "signatures": [{"sig": "MEQCIEofluTh9xkzGnfBtj5MzSi5bO+TWNCZ4kvKo3RGpYQAAiB5viKnPN/jsRGse1ntFe+yQ2DebsmYtLsoteMA2V1E2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5Ym5CRA9TVsSAnZWagAAdWMQAIBz2Or0eu7+LARVbdkc\n5rGLvT5nFjeWssMnOci47+6OChFfejuuxl9878r1LMZAg+ndKvIl3MuXjj+W\nf61Fcznw2quHstEMNjak2q25MmLV30jTwr+DjKqYckdsgf2uy9HQUl7m5A4+\n3Urv0mqMSvs2FT+Nj852YeSu4SNAZmK4SWWojmQgNzFrc7hOK1OTPD+7Skfg\nDqoXF/XFx+PyIZ5nh0zeA08LTLrb8Iz+85F+zUzteBt6HXTWANdaI2wUDW5K\n8DsdMXImHEbdvdxXmkoILpeJmc5KDXrSwgrUkg5I7B6fJxlyUp9mHIo2yDHp\nV1oTKOuyZRnFeCKOOwfqd+aFJWHQIJnuEgXUFUr2uun708CxEwA/8cOVvTc7\nMKCGJvvxksrC/E6pOTRBSEvsXj3otGaBCetyJjWEWlcfQrWBCjFp1Owg7GcR\nxmyc2ofTU62bEAUqbF3dLUgbzaF0kVOQn4TLepRtlpLOFCPzRvBML5qiqxj3\nHDl9Z726gphyNx58wpEMog/ZpSz8sWDUWj2KKkCeMFp+++2XrpV3VyJQyZL7\nz1jErXy3GZt850Fil2TyNG/qHKn9LzXkZ0OwZmHPo3rgMEGAuZZj4fmg6TFj\nD79waD+4204A8He2SKWTWqi+5gc4ydAc8GfB8oxexYcVA2dXE9/4q66uuufR\nHXNR\r\n=Hks+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.11.0": {"name": "globals", "version": "13.11.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "40ef678da117fe7bd2e28f1fab24951bd0255be7", "tarball": "https://registry.npmjs.org/globals/-/globals-13.11.0.tgz", "fileCount": 6, "integrity": "sha512-08/xrJ7wQjK9kkkRoI3OFUBbLx4f+6x3SGwcPvQ0QH6goFDrOU2oyAWrmh3dJezu65buo+HBMzAMQy6rovVC3g==", "signatures": [{"sig": "MEUCIFukJYT6vcPIjmNhx2JmtFVE7QkS1rP71Gs3EuwD4EhMAiEA3BfdHEd4dV3wVpf7Ub1i66yj2n4wuthQ3iWDkuqfoh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFauWCRA9TVsSAnZWagAA0tgP/iTua/VduBMjf0BPowgP\nazdXSdleZcJGTnLT79BnKo7/L6XgmhvUiSCQgPxwD7v5ECz8NMJLSJYZ3zYg\naw31OdxxPSsWyUkARdiHJA8+kfXYoXT/zatA2pZb8GHxsYNvm1TQ85iaZ49b\nXTJ/EHIdlu7UJd+N70kqWb3fbVilKereimXoEOU76Jk+ILdR2dj0usW/rAiK\nljpD3SS9SfVBtQSQDc790vD7U0hkz0ll5rDbixlVTgSYMqizzwsYg4sMHSXO\nlYmscnWAvCkreWklptlg7265CcX6WhsGMo7n/zcmuYCDl36XGdTInxOnnO1v\nzqGyuAJ3LEbEmmBWIcRtgXJ25bUEUfoacGDgl7be+fpgaztHXq7aIrwsX8Bs\nOP82cxF+FakqU8HlaKsEm2p0oSehTpY/AHLLz8km2PU+WFXl1g4MkDS0c7wn\n+pdnPCndfLL7iZV4IaYi1i1h8Vr8KuLVslYaRKPQwNHKv/8QK1fvFY3rbMAo\nUsrUZiKcyN4triKNj/uZ3yWlqKa2qRRqgEvDtobcBIqk0B7V33EJo+JbsAxr\nhE7ts95rVKEp/SVIvyE3CrBUa/21vlrxPlCqTMDvm/mzBF2RceK8L1rUF3vI\nJJmm6og8acrBveUigZIThO4nKkB1Cdnyef0QdfZJMZLWNC/9LllkFmNTBgwC\nLxUV\r\n=PGPP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.12.0": {"name": "globals", "version": "13.12.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "4d733760304230a0082ed96e21e5c565f898089e", "tarball": "https://registry.npmjs.org/globals/-/globals-13.12.0.tgz", "fileCount": 6, "integrity": "sha512-uS8X6lSKN2JumVoXrbUz+uG4BYG+eiawqm3qFcT7ammfbUHeCBoJMlHcec/S3krSk73/AE/f0szYFmgAA3kYZg==", "signatures": [{"sig": "MEUCIQD7/sNqIqCC9zTzEj/BD6YIGb6uu26eUVMCdRgtr4Jh4QIgCj3Y3SZe0etQSQ+1QWSYq5zioKf6hMsHRHXdGnVLBNQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2saNCRA9TVsSAnZWagAAOaEQAKJlrxT5JtJdtS+9knYe\nrQDEzfdCbkCZGb8byEvWtR07zzVEIlKstMS1AG3c+Y49I5y7VM3JrQshE/rD\ndNKbXfI9kzao47R+L6jt0zDRGQncaOLDOBQbzQKCsBrx8fTz9lMkezOdG4Ow\nGFq4o8h9cWR2pEAmLg8rox6cbP1bhf4+Ja1FGuFPxZ1Fgo9vc8t/lbaHQg67\nUZQsOlatH8A/J9alFACCdnh20VtiHEzmQ0p8ig362bysa9rZMNdzo6Pg6Il+\nfBlR2DGtfFDT/O/7I8EIZzHC9cV1Xc2+k7oDQ4/nMUn3zPzSyPZ7pIsllFWz\neWEbdZ6y/RFT05fY0Cgbt1e7oHdxdphuyfGhIYoZLbR1FlfARevgBMqKrn4s\nvtDrJVPzOTSND1g6OkBU5xtXBzlWN2WgQM29eQvL9VgrofxmPVBPLrF8Kwwl\nCSriOPAJTQ0OuyFA1DZ4FyqQQ9jZAkgVxzhHtgdWF9xJ3ofuWAp+AmnLt40E\nk5yT/le9MaVuvHluN2rZGZdLutwuXmCgjYm0BtQJ7hY0c8xsP6ZXZTTQVZjV\nzctMXOMyI4VV6fPb7l7dU7yhedHSVj/esDpaghLBr4dV21Pd26FDQA0oAV8O\nqo/UbpelFYmMlStAEWBx70aHQ43FwU7GQUcFfePHEm3IPgK2IfKWgDIrlGyu\nSuNm\r\n=0YmT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.12.1": {"name": "globals", "version": "13.12.1", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "ec206be932e6c77236677127577aa8e50bf1c5cb", "tarball": "https://registry.npmjs.org/globals/-/globals-13.12.1.tgz", "fileCount": 6, "integrity": "sha512-317dFlgY2pdJZ9rspXDks7073GpDmXdfbM3vYYp0HAMKGDh1FfWPleI2ljVNLQX5M5lXcAslTcPTrOrMEFOjyw==", "signatures": [{"sig": "MEUCIFR5TRJjUCgavsX74mAM8Cv2uLKdC0V8ro/9sQwgi9JRAiEApmLt1+O5Ky1DEaprrcV/PEvcEGhknOl2+e4aII110aA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rzzCRA9TVsSAnZWagAAoo0P/ijgaLBzmkCSEUcdI0jp\nDA60AaRJfPNG4v/b1X6saMeK32J3ZBNs7InaiwJm9X1Jr1M2UN/fGnak0hTH\nVz7u/7QuumfX+YgnjNVuufWcXfHYS97HIrKjjCnaDPxBqW7JLVBHk3LzrmTs\n3XvS4yWWwBm9dM2KcdG7/KaOtLyGQaPxsEXhebNWiCfgcWCY05mUMKDUOsNO\nyIwT/NksVYTMdKFxTYvjT03GdGJPj3KE+6ZuSyZAzOzcByAtOe4+ktILBGZ2\n0MPw5H9QID9BqiFO8JVN155b1bnfFGSfXZRx334nu3V0FmwE8GdrmVqi286U\n6UGoXFx/6kU9T/QYa7R+U5puCg2d0lLRQwnDhT2mD77PWzBkmoEFadbk1/mv\nFxfak+q6n0tYbKzXdND7Xh+w8DZ2irCwg3SSUNUqobvSc686e/gPoNhcBGhJ\nFdeJvtwNk7fLcjVagtbZQU67/qii2NQ5tymKTPazRW5k2XPLt+aaZyvDYM6t\n8CDU/XVKe2Jvv2/WlVNZwq4yBAMe90XifSyRh/Lf+6NM/aprHoRABuN2mbsU\ndkTP9JzJ18hdcAH8hIUBcDr/GXmU2x9DrcQGM5smH7nZBpk39asXKEsf2Prc\nPEGtfroXJJ/6ArA4nI05Y2RWqskvLQwuMrpBjdOc9bQq7lPootNpyOCUwE8h\noMps\r\n=1tr5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.13.0": {"name": "globals", "version": "13.13.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "ac32261060d8070e2719dd6998406e27d2b5727b", "tarball": "https://registry.npmjs.org/globals/-/globals-13.13.0.tgz", "fileCount": 6, "integrity": "sha512-EQ7Q18AJlPwp3vUDL4mKA0KXrXyNIQyWon6T6XQiBQF0XHvRsiCSrWmmeATpUzdJN2HhWZU6Pdl0a9zdep5p6A==", "signatures": [{"sig": "MEUCIQDdwOizNH/+lC/ah5mCU4S3bdp3aPX4bf3kp99QLsxNDQIgdm8El9tlU9B0ZnRGt/cxTen4QM22vqNd3wbMGbhckIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNc1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDfQ//S171VbZi4BHW4ksj246CI/erbLUHy9FripAyvvPTak21FaeP\r\ny3f+Xvtu8NLRnnAkldIFzFpqZi+n8pz1PdbqIepcPT2tasKryJ3d8GHQnWaM\r\nPx42YKV2/xMSJPpxtqgdBjvykUqnbMOcU0xn6QY4gduPuGeOjgRGyZ0+QUhw\r\n+GJniBhgsuFWT0SePJY2KRpZuclZs+vLs44Y64IU5cRjeMvffr3K3p64RHqv\r\nV8HqusIjR3pARpR5B0lNEI+7Hqfb/nu3hEveGAplvEgyp2hxRbjEBHfnZuwy\r\nM5xvi/hea/9LDRZ3Xr4XrA+6mnh4fkY3aCOLfmI+a/wFpUXeyci4mwp2YL6F\r\nZJy+QpL4TULNRQUAjROowHQQ5kt0WnPhqv91FliSEUhN0EpXXOjlWxVhdi/0\r\nq+POqoN5IilbwdIe77ZYiXNu2d2hK120bR6R3ka0u0I20XsxgnHbsz4H9TYM\r\nu9pod4IvYzUtYGyCXEOP/2FKcQ1N1sPpLRH+m62O1N9+clCTSdEmuy8Ye3in\r\nf0ynAU2q96Smg+YOaEEq37+TejqL5KSQiAlrjuzcBZZzPKYa5mthVDn7xLYg\r\nIrsYLxrogX68vNWUTd07r7Mh7g8TpGZ8yCv2ry7A1iH14xqDZyt6fpUT962v\r\nhBSXYAsnhevRSmZ0gFYBzLS80KYH+L1DjSM=\r\n=ZRN+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.14.0": {"name": "globals", "version": "13.14.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "daf3ff9b4336527cf56e98330b6f64bea9aff9df", "tarball": "https://registry.npmjs.org/globals/-/globals-13.14.0.tgz", "fileCount": 6, "integrity": "sha512-ERO68sOYwm5UuLvSJTY7w7NP2c8S4UcXs3X1GBX8cwOr+ShOcDBbCY5mH4zxz0jsYCdJ8ve8Mv9n2YGJMB1aeg==", "signatures": [{"sig": "MEUCIQDN7k+ikKfHLeEs58akUUQ9FnTsrId4DYAX4MFxFuaRqwIgUW4bgjEvLTiFWnnmj0TCNNw1CL9koxhiPiaf1FgBvm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJid/sRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrBhRAAhgavA1ukvZyPtoYV7wvZ75si0rxwG0qxCUsy62yz0erKj9c2\r\ndHAYkznEUKP84RzMCgEffRdvpTzZeI13ioxLhrx7lMO9G3CJC7vteSursvMy\r\nVHrRT1YCFvaT6uDSXg4aGY4HHBh/TVIFIaw4sQ8jiDFvCZpi8auGPwoLWtVx\r\n75xDHwQ7SVOzIEQd8QpdYbWXBT49maIjTy8XO1tskv4qgYnzgxfdn8PLi96P\r\nAe+hniOUH5Eghg0gi3VMyLz9BDR7Vl3kSItXYSronFXkjkA/4/O+GD3N0fz1\r\nkMclCmfyJb8R+urT8MMgdt/WehtiOlpzNw4m1WGRpgooxfCUj/CgjB2ROeeT\r\nKqGXMOfiIx+HXFStpi1GiVVdaXdbFQW5RzMscDl6gZ6oAYIDVm+vNYbskYGO\r\nBYf+fJ2AT3zN3KIDypStX2D6glkHLsUKR8aUM7I6HejNU26xJQEvmHhAjtYc\r\nZKgsaqePZXywlEREaHgpwmF22H1VMqM/S8mIRV3kqBvB4m8fLcBkNwVNoW7V\r\ne5WtuNJMJqFBCJ1/HEfPZGlnF5TO6o4wrWASSJ/gBNO+0SV1jOrxVX1qUaLl\r\nD3hNd2n8KaaC7yi9XXzD7sENvl1Rvc4VSMz8k+G3VIzyGn3BEwaVqMxTwjPr\r\n1Z6A409oc/mtHsNXSBwT7Q3G5fD53AIKly0=\r\n=bK+U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.15.0": {"name": "globals", "version": "13.15.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "38113218c907d2f7e98658af246cef8b77e90bac", "tarball": "https://registry.npmjs.org/globals/-/globals-13.15.0.tgz", "fileCount": 6, "integrity": "sha512-bpzcOlgDhMG070Av0Vy5Owklpv1I6+j96GhUI7Rh7IzDCKLzboflLrrfqMu8NquDbiR4EOQk7XzJwqVJxicxog==", "signatures": [{"sig": "MEQCIH22A/CrsWg9a+dOrM8L2J95ImOmjLc9DbymM0+Gn0dwAiBo/FO63kPXlbprqptEqZ78wYviv6mXMzC1lAYMgLYtWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46009, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifLhvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZyQ//UeHi6W+QNJSjOU6QNg3iYbTKlRUUIZ/UfEQ67Asz9cbd9i4I\r\n0TgFD8N9gYy5lkT/k5o58YpS/Mfqa4krtBuACRa16YpVW4S5jsciMR1/72DZ\r\np/vYmiAdTVtg6EFXo4zaJZmpL4hm3JmiQlM1AKQCgDHwhgg1PdLigp7wGsPb\r\n8jOvWA8StOMKGpnj0gEp+twmqbqeRZQ3gA9MC0x9dOx4MvFzRDgA437rdvJY\r\n1VJngjyhskRWBGwGewAudedJlXtpmE7sQxeug1fCnkgVBy22vPFTJt36SqKV\r\nmGfINTrm0jkj5rSPWa4T0+Ii87rBBOyllPLHCYE6Wt5Km+aJcpsdmAwjyxyL\r\nMewq5T4CWsinrKoZiHRsgEWAjhSg0WBnmNPPxRySZ3OZE86My8Ut8M8vV7Eb\r\nPY6QPiSp6iVxTpLo1rddlBzEMgcm2/L3YXCbBvwzADKqxPHirvhh0vRGuSYF\r\ndV3a1UYac4pdw5wbQQvG2ek2sczSYil+HW8LyHcqiB68JUZLuWIX1yC+/4oX\r\nNAFqJ9OJAoCbZNc7PgjMAMbd7g0hMxGWXhYhfrnFjHxBIG2399towS8Yan+w\r\nmzsnAyI0vDFshkKzYrqotAQ5lVpAOBzEK/ZegHLxewS8hlPzmGumoXoqyTlC\r\nH2AA+fzzwPbtGwyt5Tko7kgpGpgCBfWoWOY=\r\n=lcsC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.16.0": {"name": "globals", "version": "13.16.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "9be4aca28f311aaeb974ea54978ebbb5e35ce46a", "tarball": "https://registry.npmjs.org/globals/-/globals-13.16.0.tgz", "fileCount": 6, "integrity": "sha512-A1lrQfpNF+McdPOnnFqY3kSN0AFTy485bTi1bkLk4mVPODIUEcSfhHgRqA+QdXPksrSTTztYXx37NFV+GpGk3Q==", "signatures": [{"sig": "MEYCIQCyCWDZRJsj3JSX0l9Pkk1KnyUwRfcqiT8MpZlNyN0iFQIhAPZDacko/67nwKKp0wLySZREiE0CYrdFk0XOxu3BTAru", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiw0YfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC4w/+KigpDvNq7ammQkeTcHs8kX5Fl9cf7fAp21+s1U1mPJIfDMET\r\n2pkf78Abab7oEae0BO7vaZeCDpo+XA6jMF6YlZwa14qKr4cn2EZx0MgHDC27\r\n0S/1OuoFmlLVWhmRGkNQWEbRwXTMAMgzIsh1I88e2DNoJqFnxI4w6xe1PI76\r\naiS1eD7O5FC9r4jwyH2BihuwP3CZxAzbNGxyZPOI1uqoNMEMETdnHRZxAw1A\r\nR9DIT3tE2eUQPdYnjqTqcWjx1o1XfGpCI7jcgrCTalWABA8Ijjze4Mwsnfs3\r\nhpzAjRfxGl/CCebwWbfYsTUvGaFvATBpCmxcCH2TBC63qS5gBJXzOU9bEroS\r\nOlzNX6d59vay8kTlhTxM7Im9T6lX/u3AZe/d4OkeahfbpxU1yozuQEvlvG3f\r\nRAKGGi+ZBtIR+qG3hIKHxCcIW+5bWq981nwBHVF8eJM6oh2NotW/odfcxVJX\r\nrCpAQFXU6Xocz42s/+KPRZ1ODnWbzOkNuilA0InfQR9oqcScp+1aQ7tgwWBf\r\n3HwSsj5o5Q5iLreRMDk+Fjza+TBBwZbnm7SAHpHkyclT3ZNE9OiOFIXfteo/\r\nhYhIXDSR2sbHPzGi6J1YfxYWdEdpD+sX44NRTi4HaJyZw2UZMfZoZVenQJOr\r\nDaKWbiBRUztef78nVELSch/EF6V/hVz/8b8=\r\n=NdcL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.17.0": {"name": "globals", "version": "13.17.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "902eb1e680a41da93945adbdcb5a9f361ba69bd4", "tarball": "https://registry.npmjs.org/globals/-/globals-13.17.0.tgz", "fileCount": 6, "integrity": "sha512-1C+6nQRb1GwGMKm2dH/E7enFAMxGTmGI7/dEdhy/DNelv85w9B72t3uc5frtMNXIbzrarJJ/lTCjcaZwbLJmyw==", "signatures": [{"sig": "MEUCIQCJVV2QzBgk8QZkIGiL0Tc47k02GV4sjqgvLVClSY6g+QIgdj0X+kHQpzOJcZbeTTMeuHYCtmL+ZAGIte0MO/V6JYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2QtlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDhw//fc6R4ogS7lQO8GlzVFeV1aKEwo8ZIuqm9md1EpvujtNtg3si\r\nSMAVyV94avK6zxZXbZ+CPBQZcWJ3rgTwdrldodOMXPRGcyqKN1oPyIR46Jzr\r\nPLedBVnfRyx+sGxQxz+RQRts73rr3b/GoD5fuLKx+V0cEjGUtKaJoZCsgwa8\r\nlo250LsjGJir6pQtTZMCjeEbZZrK0thL8Njru3NyhGzNsGVGyspIifF0Ph9+\r\nCEmSM73ziZrlEHYKYsFnIDqHpGQcE9IToVQJJofzQKoTmOLR1ZWL+8eaWAzn\r\n1TJvHs/nNofvYc4cvqzh4kVkrjhy52gV25sZt4EnZbTaSVCKlzz77sgUXnpb\r\nk8dYYorrMMKeV/ipLiqhAiJ9Ac18BTUh2DGs4CF10yrOryTXWQi/m5EEWf78\r\nHTObf8c+A7ltejG6EApHcToYPo3L9ogJY7GqZaVq4xfcKwBTNQIwi97OM+i5\r\nbn8VQCinnWgGHfBHDWPvQNCCGNdZO24pYzGm+ZxhDt6+CmnB8einjBR/2mzY\r\nTiTlyHsUi2aTPygwG9zTN55lRShk5Y/pcenrMC0jfe2NW0ayHZ2EbPck0fdA\r\n4TnxCY2SXrPK0q4YG0IemE7lI68n8Hf8PoK7uhG70foWGggqSUJWipOCbu6a\r\nbM4aJiiLKeybcAh/ynig4RsbK+cGX6vM5EE=\r\n=xg5I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.18.0": {"name": "globals", "version": "13.18.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "fb224daeeb2bb7d254cd2c640f003528b8d0c1dc", "tarball": "https://registry.npmjs.org/globals/-/globals-13.18.0.tgz", "fileCount": 6, "integrity": "sha512-/mR4KI8Ps2spmoc0Ulu9L7agOF0du1CZNQ3dke8yItYlyKNmGrkONemBbd6V8UTc1Wgcqn21t3WYB7dbRmh6/A==", "signatures": [{"sig": "MEQCIA4nu50ZN7Y2dp/gOcjBN9WISYi9s/js67PSXSjBVrnkAiBqvWHO4eSJPcZ6KVcVTFtf2sMkvXucUejhtzI0BUhpBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdgkxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO8w/7BlEN4/nLETFxxt3CRqHwUNmEYauM9OyvxpPgm7n5cTiiyYLE\r\ny+rItmjMuKRuUpteiw2ENcXJs6haiaAFN+K6geK3oLFGCO6wt7EVqZzP5la3\r\nnJwv6/mX9RmIr5edMnRyi525ic8IRx0EiWRJyJc9wAR11WFyttVp+dw4E2Ib\r\nGelwRljPYW7bE4oXOG4+f1XjMgesI4X6psEkwzrFKmm/weLHifZqiemYdjnS\r\nciG3Ff382rLO/b9ugqxeNOGhN3yq+stRsvARPgdZP7oVp9ykWrvbUY5iB5xV\r\niV1gYqDaPqsXaCRduxx3IKjN6UnTTkr3IIlP9U4ezhAUF7Ilxqz1WSzfz8l6\r\nrDKbt4rRCpjpO2teQHfiIaRuSyNt+J8r8+Kaq9PDB5vIPWWG9NP5QuXCyaxu\r\n/eOGDb8UTQlbD0wzsGE7V2U8tZxgwxVzOCC6iNzTT1dBO9uosmPYTGhR2g8O\r\nEjtVem3GQ/WRrQtIB99UHfSqa+YdWecnUhZH7XgcYIeSczs3+RA7thRohSGS\r\n93c/4oAHOtXPIz/4R1RXhRmwaYh1n+kxJDxfGLlNrSf1EnQoms47Snm7bOvF\r\nqCke/eBfJYsInuKz0QA8Uci1vMlbXsS2ljK+K2YqKta+twXPogBNBjQ/VLHv\r\n3fY8spzR3d5uAjzKEEcMapi0Byx4cU3nHkQ=\r\n=4Ozi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.19.0": {"name": "globals", "version": "13.19.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "7a42de8e6ad4f7242fbcca27ea5b23aca367b5c8", "tarball": "https://registry.npmjs.org/globals/-/globals-13.19.0.tgz", "fileCount": 6, "integrity": "sha512-dkQ957uSRWHw7CFXLUtUHQI3g3aWApYhfNR2O6jn/907riyTYKVBmxYVROkBcY614FSSeSJh7Xm7SrUWCxvJMQ==", "signatures": [{"sig": "MEQCIGctWccpZJuEhBUmm4DhFVOm/WXGk4KEvYZnOXMHjaqUAiBdsyx+xo4zWDsIx5OW5h3b2x4JjrVSje9yjai0PD6FQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46369, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjlu5UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmor7A/+Ly7ET87Qis+YMGierc5/QZJsUZegk/d4tSOaUcyY2KfhMRHc\r\nk8C5W3pN3PB556JJRTMoX+kgzGipTzyKVzadWgesDm1fjhnLe3XdSOW9SkOw\r\nwf2er40YXBpBKHuFxqtIQW0b0bKbhJ+EL+34m2Eh497tgncru1LC9db5RXeD\r\nu74FCetgvXxRIIb+uIa0M7ktv5C77TsVNfuDIbtaogftljZ+Jhj8/NL4RbvH\r\nc4wh6oTaWHYXqxJ55tvscTnWEt85mM1hQJesURlHc2LiuHd9e/dA+osGPgXz\r\n4AHtDpVWSEDYhqLKI6J1ow+XMuW7eoLmSQYfdc+OGKBrL/NEXBtILL1lOeq6\r\npuNYz3Jp9v2EIREDEWmgU7eLFDCDruoF0UtoqtcVkcFtfJFbJEM30vaFPOVD\r\nIBAqY8SjSLzbq9Ye4+JPzHpfjQ/Nz5APHVwA4L7I+Ay5yHuh3thSA83Hd1Hz\r\n94MXLECM1a/bY3G8uzCvq0ZtkVCQkJ+L2bjHHG/M0GEmfOvj/Iwoimbt1nNz\r\n7iItGWUTS3QtJ+6mvGUHskGA+J6gnREEW2va0ADsGTYlIML0NcyssXfgHVfJ\r\nLM5xgr8EeMh+INeJhlu4CBDhIGdFJdAeLWU/tu8OXAKtZVC0YgJhZPj7JcHt\r\nydAmNuC0jsVF4Wo0EzC5DaW3cPABqR/yGb4=\r\n=16kW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.20.0": {"name": "globals", "version": "13.20.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "ea276a1e508ffd4f1612888f9d1bad1e2717bf82", "tarball": "https://registry.npmjs.org/globals/-/globals-13.20.0.tgz", "fileCount": 6, "integrity": "sha512-Qg5QtVkCy/kv3FUSlu4ukeZDVf9ee0iXLAUYX13gbR17bnejFTzr4iS9bY7kwCf1NztRNm1t91fjOiyx4CSwPQ==", "signatures": [{"sig": "MEUCIB1+bgqeczhioM49AiPDb5LeidGDvM9bjYnCQXgLm8rXAiEA0hCgTyqFeJnU3K4LMt7HVK8xJIltDkp0wROHf2y3d5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46615, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1PdNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6KQ/+N4ZBWkoRTS4g6vjos/eCtW67EitR6OrDzkdz9xpsBzTt/DQ3\r\nCFLTK6ZIKrFclcqFoP9DgvxGAyzE4nsomKXntE0V8KBlsVzYDZlAftBiXt9w\r\ngZhunVP1BiGjti1WpAt3ZUaWzZqh8Wwgb6GJBuD7v9b50u8rSTUzPVDP+vkh\r\nE2QA38v4ZUJt69nqZ9FTHiRPJIJDNu9mRqh9bskECg/wSx+PFs1ihhLM9Nwh\r\nh9qSPnfU28e6ML/LlYyPLgg/1FXwDJ35ylMcOhowu6biOTSaXyWNRt+W97ZT\r\npqhlAvnRWrZAq68Sl8R9RNbSSEucRMmXVb6GweMrux+yt8usbS57bzfXo45V\r\nUnlnZod3JN432DJ9vvigXVLbZDzfd3qfYir6fSK8+ePO9lyg8E3AooKDDBmg\r\nFMLGTZiPPewoAg1o3KF8lMGm4VsnTDj9qr/Xepb9YrNRw1MEPOWBKnNt2eQb\r\nkreUKeAvnAgIebgAQELEF+q3n6in1g0PRAdCcaKtj857S4x2v2P7PyoN0uj/\r\nnh9+/NbNIeiC0iWNi5PG2AjjpV4DRKov54IPqXO6oCGE8y2Ri3XjyOYct9hS\r\nLUjnzrKIYM2VFogEutT0mOAZsigIkGED2kS5qKF8TUGr1HYeD5wIfkMMUp/S\r\nTfWoL5GiHUOuSKbpsKfCYMklwv0AApL+6SU=\r\n=L7iV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.21.0": {"name": "globals", "version": "13.21.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "163aae12f34ef502f5153cfbdd3600f36c63c571", "tarball": "https://registry.npmjs.org/globals/-/globals-13.21.0.tgz", "fileCount": 6, "integrity": "sha512-ybyme3s4yy/t/3s35bewwXKOf7cvzfreG2lH0lZl0JB7I4GxRP2ghxOK/Nb9EkRXdbBXZLfq/p/0W2JUONB/Gg==", "signatures": [{"sig": "MEQCIC6OnQBzDq2HJggf2mukjJJPeqQBy7KSVgGtGxk9N7h+AiA24mm+Es5wx9P4pO1AbBxMU/ReMPezSiw6YB4TFWbMtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51669}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.22.0": {"name": "globals", "version": "13.22.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "0c9fcb9c48a2494fbb5edbfee644285543eba9d8", "tarball": "https://registry.npmjs.org/globals/-/globals-13.22.0.tgz", "fileCount": 6, "integrity": "sha512-H1Ddc/PbZHTDVJSnj8kWptIRSD6AM3pK+mKytuIVF4uoBV7rshFlhhvA58ceJ5wp3Er58w6zj7bykMpYXt3ETw==", "signatures": [{"sig": "MEUCIQCWN71M/e1Vqr66OZm1c5CRX1hIaa4GR34gtBzYBweuUAIgQcZSwehmK8gBUo6hRkOVDgMKvGGUm/mmdAQUXNWL4W4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51704}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.23.0": {"name": "globals", "version": "13.23.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "ef31673c926a0976e1f61dab4dca57e0c0a8af02", "tarball": "https://registry.npmjs.org/globals/-/globals-13.23.0.tgz", "fileCount": 6, "integrity": "sha512-XAmF0RjlrjY23MA51q3HltdlGxUpXPvg0GioKiD9X6HD28iMjo2dKC8Vqwm7lne4GNr78+RHTfliktR6ZH09wA==", "signatures": [{"sig": "MEUCIQCK8eivfJ/IiUk7KthV/fVyhNMLF/RmP6AIsbU+KvP1cgIgEjDv3UD6YE2MiHiLkNWl3Utdqb5Evw78YyyXiRXUvLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51728}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "13.24.0": {"name": "globals", "version": "13.24.0", "dependencies": {"type-fest": "^0.20.2"}, "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.14.0"}, "dist": {"shasum": "8432a19d78ce0c1e833949c36adb345400bb1171", "tarball": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "fileCount": 6, "integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==", "signatures": [{"sig": "MEYCIQDekMGt60yz8A4tDJr2mlUJpokmQTZEmpCfV6tc6JXnSQIhAIrww+ORR5FWP2T3lR9RWHQBFjD3C1VGdcLIxCctqqWD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52113}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "14.0.0": {"name": "globals", "version": "14.0.0", "devDependencies": {"xo": "^0.36.1", "ava": "^2.4.0", "tsd": "^0.30.4", "cheerio": "^1.0.0-rc.12", "type-fest": "^4.10.2"}, "dist": {"shasum": "898d7413c29babcf6bafe56fcadded858ada724e", "tarball": "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz", "fileCount": 6, "integrity": "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==", "signatures": [{"sig": "MEUCIQDJiyMahVA0/iOxUymfpdtHRJHr/Ho0HWrgaBydbALe1wIgUkT+nuRz2aLCqS8wUhhiLffRQMVhvP+qh3vxNP2tcYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117263}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.0.0": {"name": "globals", "version": "15.0.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "9c6cd4e54327ceaab563b4c17ee5e9d181c03fd2", "tarball": "https://registry.npmjs.org/globals/-/globals-15.0.0.tgz", "fileCount": 6, "integrity": "sha512-m/C/yR4mjO6pXDTm9/R/SpYTAIyaUB4EOzcaaMEl7mds7Mshct9GfejiJNQGjHHbdMPey13Kpu4TMbYi9ex1pw==", "signatures": [{"sig": "MEUCIDp+4lmvCEl6tjvf7JQWBSIxIFOGPyey18M2J+9XR3OFAiEAxyIj2wmmPxzjwGvqMO9nxtvBLFiFIYJqrOWbCU+wQtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149264}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.1.0": {"name": "globals", "version": "15.1.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "4e03d200c8362201636b8cdfaa316d6cef67ff1e", "tarball": "https://registry.npmjs.org/globals/-/globals-15.1.0.tgz", "fileCount": 6, "integrity": "sha512-926gJqg+4mkxwYKiFvoomM4J0kWESfk3qfTvRL2/oc/tK/eTDBbrfcKnSa2KtfdxB5onoL7D3A3qIHQFpd4+UA==", "signatures": [{"sig": "MEQCIBCJb4IbBO6bsV5GrUn5N9l41p56TJKmkKwOdiieqKhDAiBCsmgwSEKusSQeuo8BDeYy89zXib15Cb5CMtzbHpun0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 149934}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.2.0": {"name": "globals", "version": "15.2.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "fbcea7f8964a71d8c6e6867ddadb9788ae1083d8", "tarball": "https://registry.npmjs.org/globals/-/globals-15.2.0.tgz", "fileCount": 6, "integrity": "sha512-FQ5YwCHZM3nCmtb5FzEWwdUc9K5d3V/w9mzcz8iGD1gC/aOTHc6PouYu0kkKipNJqHAT7m51sqzQjEjIP+cK0A==", "signatures": [{"sig": "MEYCIQDBcY9Q6nfqbQkscVKJBCIvTmzRy+eBxqP9w5LfEa5FlwIhAIhI0DVDc9jL4dji0HPFUuLRDLQkcxaoElHq9r9Gwq0X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150396}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.3.0": {"name": "globals", "version": "15.3.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "619db01f88d8fb91b0ed841a820cd980299575e5", "tarball": "https://registry.npmjs.org/globals/-/globals-15.3.0.tgz", "fileCount": 6, "integrity": "sha512-cCdyVjIUVTtX8ZsPkq1oCsOsLmGIswqnjZYMJJTGaNApj1yHtLSymKhwH51ttirREn75z3p4k051clwg7rvNKA==", "signatures": [{"sig": "MEYCIQCvkrouDkkP+QGeWBW29T4bVUQxf+c/Nn1zq6sE7XAPrQIhALy742JURQikJX12jpZU3o+tOPcpfrWo2N9W1Ja3yEIW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 151988}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.4.0": {"name": "globals", "version": "15.4.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "3e36ea6e4d9ddcf1cb42d92f5c4a145a8a2ddc1c", "tarball": "https://registry.npmjs.org/globals/-/globals-15.4.0.tgz", "fileCount": 6, "integrity": "sha512-unnwvMZpv0eDUyjNyh9DH/yxUaRYrEjW/qK4QcdrHg3oO11igUQrCSgODHEqxlKg8v2CD2Sd7UkqqEBoz5U7TQ==", "signatures": [{"sig": "MEUCIAIXTIwfMvNHLxcwrraxOyOIkp7qEQIqqT3WSfX3aTDVAiEAndaxILWRUW+cOXdUIl28edsQvfrchYF7ZZcC/RdHp1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169593}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.5.0": {"name": "globals", "version": "15.5.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "093c5b7677809f6cc9dd2c01518b1b0962f1f085", "tarball": "https://registry.npmjs.org/globals/-/globals-15.5.0.tgz", "fileCount": 6, "integrity": "sha512-r7/9tQj5RylGxt/BKGv0D2SvehYvRFYg4ukSNk+EuZxvWI7uK/MJFmOCLq8aKvgh3EVBYFbBlOMAtaITXZr80w==", "signatures": [{"sig": "MEYCIQDcg7IOUU17gExcJDtUyJn1TQJQQg5dyEaX8E9scDMkZAIhAPqjZcOILw14LaTDBNTJ7UGiuoXKhCWP5tJoIjGOOT0z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 169839}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.6.0": {"name": "globals", "version": "15.6.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "3872d3ab4427e1df4718efd3f7d2c721c503f65f", "tarball": "https://registry.npmjs.org/globals/-/globals-15.6.0.tgz", "fileCount": 6, "integrity": "sha512-UzcJi88Hw//CurUIRa9Jxb0vgOCcuD/MNjwmXp633cyaRKkCWACkoqHCtfZv43b1kqXGg/fpOa8bwgacCeXsVg==", "signatures": [{"sig": "MEYCIQCQYKFDi/ieMVUwCpoO2xkwOkAkblqJEV7V89AB+BOKfAIhAJ4W+fctC41BwMWuzXshmzat+56Y1n8tE1N8uLzjozz3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170040}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.7.0": {"name": "globals", "version": "15.7.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "d9ca65edad684c438b179749fc57def027557f93", "tarball": "https://registry.npmjs.org/globals/-/globals-15.7.0.tgz", "fileCount": 6, "integrity": "sha512-ivatRXWwKC6ImcdKO7dOwXuXR5XFrdwo45qFwD7D0qOkEPzzJdLXC3BHceBdyrPOD3p1suPaWi4Y4NMm2D++AQ==", "signatures": [{"sig": "MEUCIQDmhgSX4T0Q9RuRtG74gre9XZJi05XRTbzSSwNRwkAmIgIgPqBtnKo4lZ9Xq4rBc1cOAhFFz8x9Qy7f1nRhP5DVCo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 170098}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.8.0": {"name": "globals", "version": "15.8.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "e64bb47b619dd8cbf32b3c1a0a61714e33cbbb41", "tarball": "https://registry.npmjs.org/globals/-/globals-15.8.0.tgz", "fileCount": 6, "integrity": "sha512-VZA<PERSON>4cewHTExBWDHR6yptdIBlx9YSSZuwojj9Nt5mBRXQzrKakDsVKQ1J63sklLvzAJm0X5+RpO4i3Y2hcOnFw==", "signatures": [{"sig": "MEUCIFfSlJUVDVWoIeBk90x/e8m2N+bFbg8L5ee6wUZOnTUcAiEA+bhTu91hqmQmc4tubNxuy2tnVjzB5IOysDUnlPgSlw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173261}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.9.0": {"name": "globals", "version": "15.9.0", "devDependencies": {"xo": "^0.57.0", "ava": "^6.1.1", "tsd": "^0.30.4", "execa": "^8.0.1", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.0.0", "puppeteer": "^22.1.0", "type-fest": "^4.10.2", "npm-run-all2": "^6.1.2", "eslint-plugin-jest": "^27.9.0"}, "dist": {"shasum": "e9de01771091ffbc37db5714dab484f9f69ff399", "tarball": "https://registry.npmjs.org/globals/-/globals-15.9.0.tgz", "fileCount": 6, "integrity": "sha512-SmSKyLLKFbSr6rptvP8izbyxJL4ILwqO9Jg23UA0sDlGlu58V59D1//I3vlc0KJphVdUR7vMjHIplYnzBxorQA==", "signatures": [{"sig": "MEUCIFBmeqPNPMewBUqMaV96+OzYWLQ89Hclbkk1ydWP5P3qAiEAkgaZ1HDP8gZOfpCb3RTCjuDfm29V25rP2mPL72CqQSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 173575}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.10.0": {"name": "globals", "version": "15.10.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3"}, "dist": {"shasum": "a7eab3886802da248ad8b6a9ccca6573ff899c9b", "tarball": "https://registry.npmjs.org/globals/-/globals-15.10.0.tgz", "fileCount": 6, "integrity": "sha512-tqFIbz83w4Y5TCbtgjZjApohbuh7K9BxGYFm7ifwDR240tvdb7P9x+/9VvUKlmkPoiknoJtanI8UOrqxS3a7lQ==", "signatures": [{"sig": "MEUCICbaszzCJYu+0620SLs/jbnna+0hdSFkNIeE5uylTTpsAiEApxt63WF311IuHQCqm+bJFBT3335hC0Cm+ac1oCF258w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 174025}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.11.0": {"name": "globals", "version": "15.11.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3"}, "dist": {"shasum": "b96ed4c6998540c6fb824b24b5499216d2438d6e", "tarball": "https://registry.npmjs.org/globals/-/globals-15.11.0.tgz", "fileCount": 6, "integrity": "sha512-yeyNSjdbyVaWurlwCpcA6XNBrHTMIeDdj0/hnvX/OLJ9ekOXYbLsLinH/MucQyGvNnXhidTdNhTtJaffL2sMfw==", "signatures": [{"sig": "MEUCIQCP1S5JGgebvuGLlOfdSlyO1WlxFF3XmaSx8NvmFLovOwIgINz6lln0vSYpeVQYGMDabwuZItb8IDYy1RsNGPuVskQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175592}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.12.0": {"name": "globals", "version": "15.12.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3"}, "dist": {"shasum": "1811872883ad8f41055b61457a130221297de5b5", "tarball": "https://registry.npmjs.org/globals/-/globals-15.12.0.tgz", "fileCount": 6, "integrity": "sha512-1+gLErljJFhbOVyaetcwJiJ4+eLe45S2E7P5UiZ9xGfeq3ATQf5DOv9G7MH3gGbKQLkzmNh2DxfZwLdw+j6oTQ==", "signatures": [{"sig": "MEQCICLoV1+FLo9Uo5ts7CnRGYeWhfwnEAqLAzOpaDFds7H/AiBTGV8YpMyF1EIqkoog2EEntWBTMuAp8MN+/F/WMY1ytw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175742}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.13.0": {"name": "globals", "version": "15.13.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3"}, "dist": {"shasum": "bbec719d69aafef188ecd67954aae76a696010fc", "tarball": "https://registry.npmjs.org/globals/-/globals-15.13.0.tgz", "fileCount": 6, "integrity": "sha512-49TewVEz0UxZjr1WYYsWpPrhyC/B/pA8Bq0fUmet2n+eR7yn0IvNzNaoBwnK6mdkzcN+se7Ez9zUgULTz2QH4g==", "signatures": [{"sig": "MEYCIQDWch8vKwTgHcPF3QhUCovjoZIeqeFGmv4BA7Y4O8tFVgIhAKLO9EOTByq5V6NsgmslajGDyDhseNR8KQEuI7rbD7VL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176196}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.14.0": {"name": "globals", "version": "15.14.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3", "@vitest/eslint-plugin": "^1.1.18"}, "dist": {"shasum": "b8fd3a8941ff3b4d38f3319d433b61bbb482e73f", "tarball": "https://registry.npmjs.org/globals/-/globals-15.14.0.tgz", "fileCount": 6, "integrity": "sha512-OkToC372DtlQeje9/zHIo5CT8lRP/FUgEOKBEhU4e0abL7J7CD24fD9ohiLN5hagG/kWCYj4K5oaxxtj2Z0Dig==", "signatures": [{"sig": "MEUCIQCALtJ07Mf6RlSXbZrfTKJHiFtB17xTCh+GuTFY05QTiAIgWSwgVNslOcecH99dLbwvnIU7ivPUdvS1kepM5twjaDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177168}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "15.15.0": {"name": "globals", "version": "15.15.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3", "@vitest/eslint-plugin": "^1.1.30"}, "dist": {"shasum": "7c4761299d41c32b075715a4ce1ede7897ff72a8", "tarball": "https://registry.npmjs.org/globals/-/globals-15.15.0.tgz", "fileCount": 6, "integrity": "sha512-7ACyT3wmyp3I61S4fG682L0VA2RGD9otkqGJIwNUMF1SWUombIIk+af1unuDYgMm082aHYwD+mzJvv9Iu8dsgg==", "signatures": [{"sig": "MEUCIDyW2gFh7KsdNpYWM/ML/n9gZxFIa4obPYmDdNETQvntAiEA5PkDb9E4XHxx9TQeX89jS3ZIUJ1HwC3yo+1oK6hxung=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 177210}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "16.0.0": {"name": "globals", "version": "16.0.0", "devDependencies": {"xo": "^0.59.3", "ava": "^6.1.3", "tsd": "^0.31.2", "execa": "^9.4.0", "cheerio": "^1.0.0-rc.12", "outdent": "^0.8.0", "shelljs": "^0.8.5", "get-port": "^7.1.0", "puppeteer": "^23.4.1", "type-fest": "^4.26.1", "npm-run-all2": "^6.2.3", "eslint-plugin-jest": "^28.8.3", "@vitest/eslint-plugin": "^1.1.30"}, "dist": {"shasum": "3d7684652c5c4fbd086ec82f9448214da49382d8", "tarball": "https://registry.npmjs.org/globals/-/globals-16.0.0.tgz", "fileCount": 6, "integrity": "sha512-iInW14XItCXET01CQFqudPOWP2jYMl7T+QRQT+UNcR/iQncN/F0UNpgd76iFkBPgNQb4+X3LV9tLJYzwh+Gl3A==", "signatures": [{"sig": "MEUCIQDKD9T3rt1FcQ/4BCIBZKQaypyrg9pHHAIveI9lr3q69QIgX77asxPv0Gjyp5ZOU+giy1KSy0bDSiQAIk8ebWH0ovI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 187424}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}, "16.1.0": {"name": "globals", "version": "16.1.0", "devDependencies": {"@vitest/eslint-plugin": "^1.1.44", "ava": "^6.3.0", "cheerio": "^1.0.0", "eslint-plugin-jest": "^28.11.0", "get-port": "^7.1.0", "nano-spawn": "^0.2.0", "npm-run-all2": "^8.0.1", "outdent": "^0.8.0", "patch-package": "^8.0.0", "puppeteer": "^24.8.1", "shelljs": "^0.9.2", "tsd": "^0.32.0", "type-fest": "^4.41.0", "xo": "^0.60.0"}, "dist": {"integrity": "sha512-aibexHNbb/jiUSObBgpHLj+sIuUmJnYcgXBlrfsiDZ9rt4aF2TFRbyLgZ2iFQuVZ1K5Mx3FVkbKRSgKrbK3K2g==", "shasum": "ee6ab147d41c64e9f2beaaaf66572d18df8e1e60", "tarball": "https://registry.npmjs.org/globals/-/globals-16.1.0.tgz", "fileCount": 6, "unpackedSize": 191185, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIEsR3mYHsUa+JPH7gs0q8U056M4n2z+L+8F7YiItITHUAiEAsdbao/bMTzqJ2mOoGP7cB49OB0FV9VEGYz0bD1mK5Fc="}]}, "engines": {"node": ">=18"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2025-05-07T07:02:46.572Z", "cachedAt": 1747660587372}