{"name": "@radix-ui/react-accordion", "dist-tags": {"next": "1.2.11-rc.1746560904918", "latest": "1.2.10"}, "versions": {"0.0.1": {"name": "@radix-ui/react-accordion", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-collapsible": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0e44e3bd1b5c90e6b2d1ed58b807a7f8b0ca4743", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-PZSCSR0ALV61qhCUCA2u4AvxLEzpOvhKXRq25399+fqUzbXnzn319+r9JH389K639AWMQvoPZrAmFolv2cBzyg==", "signatures": [{"sig": "MEQCICViFOWMi58rk4eabStxlG6/M4c0lrcXP3IuxrK7erCeAiBBTslyxwkouXdFGB7f9t307U4zWIdIcXu4kELhLF6DYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbACRA9TVsSAnZWagAAGYEQAIw8omHaC53ws9aYlols\nNCK9zdbB/H6VZFsyCk6i+MDH846ZLjIxdKDIFRbM/PhG3jUR9iq/9ZL2doxA\nZaaqo1FXd+TtKGb2AkKnrVhBO8EU5WB6rsNoodRuVewlKV4XxAQ5pcCMzL/v\nzdFmmNTX+muqvX8fvXQWEEprLnb+o2bM2AkOYi4im1wRIu/EDDJxsv8r6npm\nZsZkkJNGYUAT2gO+Mr7k34NRnWMC08z4kqladLt5YznBIi0T8/Em3SeUBJHD\npJ9kT2j+D8LttfmjMQ3BYdMc9lMY2JZJ1hARZ6L3x7HpuXkRDGhBn1Xr1x0f\n83qVeZ28WDAAQXnURfiKO1d/et/5kvw6Vgj/SKqR6ja6k4Z/sJubQZQpEqGN\nBWspFB6VAayyFWKoBVgoJm8WrB8LKz6nYEb25lcLEImUym/WryJWzkYXUqJh\nRRbAQaRi5KlWNpiR7wDeRqfwBwRHm5lez7HjWgkXugZPpVA/UaiSvLSdIrdM\nuWBvgGS8Mv6VG3tz4sWG7qqtPH7FjkBPD3bs5lo15wn9zA0QrSg4qLoiGz+l\n/Z3Z7jUrueFWxOBQjVCe4Orfz5v6sDjdlhRcwt6GfODYPsDOhu4fLNZvKWyL\nR6EuOphciHE73qE8dzH8JiCQc+mG5H5DL0oIe7YFVwMg8q/nQnu9Veumv9xu\nY9ju\r\n=sQfg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-accordion", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-collapsible": "0.0.2", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f1bed38441c1bb370d6da73537050d3994381296", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-HzKvoOesyN7RK340DKFJJxe42y/kDwINBT03f+5wPkbcl9TekBR+SG3n6LaEpjJTAvYW6dBgGG/Ak9I8ZfsoAw==", "signatures": [{"sig": "MEYCIQCqMXIndkzwoZJE9KVOXsErnYOTNOf1eWFUIK4kueTQoAIhANk0ETTiXsFXLpqTyxJQyDApSGFbu+ZAIFJc3ZSURt8q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwu5CRA9TVsSAnZWagAAdXwQAIOnX0Q2hkN/p7FEIwUf\nTpAFx+WLfFs/6dvKTcrRXG+xPzqobhCJUDBOtRQ8kucqAaJahsf83y7DG1Tv\nxnwIA1eola/e8gEJWuTRRdZu65slgFQB+LnixGWjMWvzEduHDxH0/gPY6Y9W\nwzBzjnZaHMowz4dsNJd8Q6JXkOeXkn5irzuZ+xb6CX8eZXv3nj8G8ekKWgqE\nK0cSHNDjKWS/sWYh4nt9YMFAB2ZOmcgb/wL3/35Jkce3IJqLwZjP5FL2qGt/\n5z+gmf8CUfiL+Jn+3xnMgMmBO/6bgUfkag6kx8eP+7cTM7kHUhL2HUleRLqo\n7kpLNAaX7LSbnv9XngSfbiyUKlRMKWX+huXf4eY5w7jeSBrIRwMWW+5a3pix\nylouH3vK21sdlVT8L5acicCBqaCCMIvLzwaAfDRxJoRRX3lu1N9zI6TrNmoG\nU/56Ag14lUjVESWAnWN5JG1KWSylfu8a8qhuCZ69DOsOcVpc+CDo1I9BDFSw\niN/uyTZGeyZVg5f+o9Gka1yO9wNJJ8Pp2bSSS/RIGN4xrvAguB59YFi/Z9GE\nSnzHpwQJaFRlLKkcaJAeci0CxdNg9nNGPio3/uiTBHlXquWZGLf9lxkZy1DM\nUGwxrB9QpI5CaIqX09J8vgF7MsKnTUa2J8/IfLXW/Lkli3Tzu7ntA7ay9nWN\naZcW\r\n=PnV2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-accordion", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-collapsible": "0.0.3", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e7d50ad2ae8490103a655afdf8d87c650a58aaf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-ooQgAfbpQVeQcqwGeFE4Bb8bFpzZ1assZik3qsv/Y/fYAddgIsFM4xouGT5M4KUriwFWWURetDzZoCjxDpY/0g==", "signatures": [{"sig": "MEUCIHABEWts6MwWJoyiftWC9605QzhLfkEHkX3hhbOdlE3tAiEAyF05oslHxOjJMgCeEbTFBRGK6R0SBLYE1Vlx3beEzPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48540, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETszCRA9TVsSAnZWagAAsuAP/3WYSq/rm9QiJECUzCEa\nEdTBt4OVs9FBUxdGauubjJSSvrJx5Lbc6MXtH175M7G88qDxoVbrugQj0SEi\nrGEKFTaECeXOjfA4/WDNEYDjCF6Q/BX/r2tnaC3cUxQSpzRIxwb8SrBcIExI\n4UdMdOwrcWCHCFdXPQbqzeuvp9n7QMXmB+WtJ4mXoZ8AC2l3xCaOAG1RY6XG\nVBFo9r+5LxglH7NmN+6DK2yX9P6tmKCDqjJ3v4eXf8WycynpixnmQLxwfhj0\nzPu3z94I3dbT6DzdntqoEPZSJ3jL+o7ks+wU5SiaGu+IU6+46feyGU6L8F8/\n5I5ubpu7RxtrBWMDAy0oWa8zNMGZz4O8msv6r8uKrHr6daozy9khTfuM2JgP\nZfhpigyuJJ2wAFnWdhXubU0/fM5J175xHEVkQU0b+wzgyVhiV/Rb4/8zqJ0C\nICpouglqnaxPMn6paLTbwsGRaqoDLg5AoBdP2O6A/AllV4oX8wOH1yv12kfk\nHMXK6C2O0+xjhRa/47jSmwQ+C/DMcKM2tj9c2eDWW4dNKyZPSSpiisFn5LVE\nGFzFR8BblDrNgVWNBtA8nj/SjbQjp5lM7Zwsricw9ZZRVCRc953KpQcokpQF\nHIcWTaUt4kR5Zb/wfZ23kPNQtqJRbVE1ZXQaXpIARFrfTfvLlPhd0ZrV6iUs\nnIg1\r\n=kRji\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-accordion", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-collapsible": "0.0.4", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b5d1822135dd274597706e1c77afedabc48dc76b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-Opp86cWnAWd+CPDl6jGHuSUqLLOh77CXltyBHGuzlhijWSiCq2ducnTQ137rgvlKPssxOMVsxdghvaM0byU5BA==", "signatures": [{"sig": "MEUCIBJ3lTlkOACKds8sOYmVoCbIL7vOeCAZR/jCXtVrLp3MAiEAoYAuWfXPywuFitUcIaaIKOuSJTH+GMlw0eVkTbENuio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/uCRA9TVsSAnZWagAAXe0P/iRZK6GACplLKGJKTNzX\nKE/lx37dn4z5TmNcODE607O0CzP1z07xCkfdG4Hz31tG09YaUT+nlzdNWmSk\nI+vGyH93tbG2rnlJ8V9MPSr2g4f7rc/mvspMWce6L9+0eqhrYe/HfO3ey8LF\nGkMcIZvzK+HMSTDg2D/43rZT7RHOIaI0agE56bZcm4S6f/EdfPoC40fOFCc9\nHUSNNkwyc4nWrdid/8gnpQbtHeCoU7Ej3CWN9MUJTj5WFXNGT8eP0XWKsZ3X\nhtg+DcDslM8gTFHOM+A0fi2E43bhIPuJe2a6k5eZakmrczb9creeF2C65jLu\nObqxi9XcWLD+W7qvmx2fcFwZSdxuRF0NVeHociqPqEd/SH2rsPMShJgOQXHX\n6TCgP1x3JzlF4Yuj97vMyzpL8B5X/O2RN4S+EWMhe6HuGJKEN8aF87QM7T4d\n3zf3uQud4KvPgkH0wPX7qgtCCbNzuJ1LhUTndozeLinC9rZg19Zhv1+WQeZD\nUSe2ZQQczF8SFK71vtCUiAWT3G9eym4OCgE4K7yP/D6NrvMDRDQX1B3b2Zes\nohvh+ZLJN3pl/rkAyDhn0YkgpiSVUQB9CgtAiUhkT0ECGzGZTaiIM9pHKv7K\nA3V9NthsXOySL58axLwCSRZCwO8Gdf/+9h1frLmr+PHjurug+BEZVjucszO+\n8+Q6\r\n=UVgD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-accordion", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-collapsible": "0.0.5", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "09247128815e2e3b75adb9dd1918b848bcff0758", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-7DAKzqeZMvIhWhIVCB4BNe7pjCaVoDDZAdPbwHx0AaMTz2g9UXtVJ8nJh5jdF+4axSEvghobOXCZbTjB4apMWw==", "signatures": [{"sig": "MEYCIQC9p59bm9x0eOQpI3Yjbp3yvGAcNHQ2Wf72C+IVS5dDPQIhAJHzzQrvdeZvGXP/GWKPpljNAe7DF3FT8ZG/gaj4GYqQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VmCRA9TVsSAnZWagAAUUEP/195BAV+OLiaDgkHFlYT\nSLalGXO8DKkzjqgQXKb6fOAlQSyZocEHMQbF7lFqY2vT+uJa3yDwlRpeyiTJ\nH+KCbwX7X8sYBU5i0etIhlC26P9yAFlp+Z4wo3a81doobvXLtYl4zbIVRTd9\nl1OMtb/Ux+3JEuitfpLkcR/EPGXxgh9GKzXy260rlNY8VEJ91PMpm87QeFu6\nYqO+/8TBm669xeb9j47Z84ddOqHHuSTvvaE1lsXClv0ZNEsN6j6Pv9TISgLk\nej6eGoe8IXda82g7QSxykp1yS/x7JppxA1z1PztVAfG3uHH9U4ouB5gomOXF\nPK72/rX4QCcnZv6aFAfhoClI3229spWmh74DJRmUfik4pumV/5dWKyWzWudI\nMq6kmU+LhgaxtuFWxTK/SzxGO5h/AGbVan0/YurnaPbU5JARR9iMMxee2n6Z\npMi+I5hrfARNn555Z6OpAoQrk9FPmWGPDfo3M5kbSe4k7ZRpy7jsClV55JMV\nne75SSwxCt1k/M+ek9KooTH383vBlu/0jm/AnErXAlB9S5JurMpxeegC9aYf\nXkmc3KupxHLkpcuv95VV6QQfYLmqez5dlDAGN7whKxzu2RC6IXhXIMvZZead\nBp56I7LQf0mLCosemj10c8SKo4SwXEc2MuHhz244EZT9UH5esLiWcQ37oX+9\nnXOo\r\n=OdOl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-accordion", "version": "0.0.6", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-collapsible": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0ffb9488e874fcd03351aa0c34d875be1a997485", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-hOMLJ02Ob/I4spvXUbRMjmDjUvI9dFM9yoo+5d9IcBduGLdMCl8yUZCsSl4M8W/r9xL2kPIUGyFmHa7qG02xVw==", "signatures": [{"sig": "MEUCIQDDBus0q3yM/2/iS0+Q/cvlj4hAk0FRkNS1S0ulOWhiawIgE9JKAGU8HxkL694J1XbTt+qSwFerbK6euYTG5qTGAsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+U7CRA9TVsSAnZWagAALRkP/0vU0sn2bZKwKjnLTA9o\nC0zBKSiXEyWDHT+igAEIXOL/1B/opXIa/rrkyGDQzfw4lJTakJ92wJ92MPxi\nw1hST+lK1J9hMcYJQ2bMYGjUe1e7BXKyLMfpgYtmHxKAQTUeGG58SvfplT9s\nwmXKWi9EFciXuZ13dUv7beE01A2L8GYbL8tc2UHSFvu1ULsYSH1wFueDp6CY\nlImDSZ4o5FbnrikwXcO37CsWUScj+ZZpHkbwBRmagOehyi8dUPYKxkzLlaX3\nEMPOMWEbGhwYC0EBgMhol7B7fbqFFEf0Kyaerr7KXHCd+9IkV5okpolHwhTg\nKS+Sx6L+XRWWT9MvFhF5RPPDOU82yumKAj0xKjzBpv1Q6PN/XdzrGxZ0k+PD\nqGTiSQsNbUzYMrnLQ6i5ZhEMctRb6hETnl6vLDkZzMFaIjAddU93+bjRIQr0\nC0KS/xoZ/aR/JSYhfUhckJngDiGDUWhtCTiK1OUFI8UkTiHyXFBYjkC8i3yB\nhMA3uUR/exwB7/Vrah3GOrRyUgyLJ+1uokFQwJEXL+xsRjxwlyXXqDwEz8T5\nlk+65A14NSzvhZjVBMq2COw79jzPKsV0COnhVi64KMlYAMccahn1xT9sBTnH\npMj4tDHEIMv+3mIezID9bjE1/iMv16/yzjruIAVpEP8Qv0qGqd6fbrYlymf+\nlYnh\r\n=PC1c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-accordion", "version": "0.0.7", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-collapsible": "0.0.7", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cc2601bab4876909802ed07c3169267fab8b812e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-9HFa8wfSLc14Trx4UCOwxdKJ+0CGdLjdBx7Mwcc990PXjCK340UePY7KHUbDWVhRPtD8oxmehr20kW5V2OBDKw==", "signatures": [{"sig": "MEYCIQDld8GqBPuqCfXZNh7kScUfCrSzTWBGQWzRqhJlEsggxAIhAJcveh81LPDpWfuX7H19bsnX7r5VIEYkyMcGOAnzF+e8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmVvCRA9TVsSAnZWagAAy64P/2n/v0XnnZH9Jsltrzqx\n7Zk2vzh36nPqPMtUOhPj6CJChhE6vRNLlaoglANJXgIC6KhBHO0DNf81u849\n8GuLiZtWo75jT44pl5aCJBeJqyo9MAJAoMOzZ/OkcFUl0i887MAwrH6P94ZF\n1ASVf7NdhTU5tpDuLVKalf3FRrQYl+0RAWM0alnTkXvHyA8pkI7QoIOHHhAS\nFhxFEkxugbuQhLoJCziYCISvbOXrfFHXtBmeTB5tah1ONj1eqVbMVvau2l0G\nMS7SGoUAChE6Mhwxc8TzPId95+WW4YhhL8BpxCIm+0CZgyMlAIy845dhVPkt\nXDPZXLm+ClLZOhaW3wMXDBzSl2SgnJOHEkXZax/sncbq9BPM5BBq6rsWvI+T\nFKDBAtw3p3sb78isEscq7ldw2uynZo+fTtx1oO+2K5YKLBT8W6pyzm7L+QY7\ntOjH/IjWPu/CDocYSDDeFlWdLZUY03gRYhUR64/Iz0eLPh3Mogm6YkkoAPAm\nVPJtEWCXbk0nOOfeEO+HVnLt78rXwiwcbk7VBnYGpDvx8S45HQPXcaKOYBHn\nI11DRwROx1FroXywevuOzKNuSURxbPi52LDf73BOn6FqIiQtMXHz0UvvLkGv\nyfYbzfRJjSJLBdggU5gz8RyS36gXe5adx8jBtf4IFODg9knp+/FH01NxvaSG\nWqGY\r\n=Veph\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-accordion", "version": "0.0.8", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-collapsible": "0.0.8", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c3dbe4340559c1ac430302847e8468ba25980de2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-i79U0sK2OyBhT6Fu0y3BLExP/84ud7x8gZgrhWuPEBT33Y+3XG8L5wFJ+v12k5hA4ZXa5iFJx9T1axck0j9LbQ==", "signatures": [{"sig": "MEUCIQDY5yfum8ELGEIFgtQmwgRBL1MbiQgUvrdTzE+iS7jlDAIgJqlgevyGv7xfU5mIcKxnwVornP0tS7RbGm1LO5xGZJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9dCRA9TVsSAnZWagAAL4gP/0UVZRqAf6bxt0Mn05mg\nXMwGoH9YVvYErra4RZuYZNUwcROrM+nZMXwZ30BpCEcY35F4t25GJzRltXUO\n04NqT8oEFFv/vZfNsuwcW353Yu8n062wj3riBZm9/x69TcDdFnQ6A/BYrfKI\nMBkqHnq7H+SKLjl7Iwm7YlPtL2U+frG7aqlRGqACLrummE6DVrHFN/UxNehm\nfnbXVNQwFFCGWomPlpJxYwZrT9vajp6rnvWsOADF0QnF0vZ4oFbun8npkuEK\nFCNyfX+01EQYvz22pCkJnViZGivtvOg7IvNwdPoJl6QFspg8UnpxOoVEMpJ3\n2QleZzZiaj4p21sp6ioz+O8H0gTtPdYBRnRJDGDhKQLU3A4d/YHtw99Zu3CU\nlhxlpTZ5v8H1lryq12gPXDgOiEgbpS4Mn+bXGJyJTCFF3gntu35cV6efozrH\nvcHZfaYM2cysTj9Fn0KMgJUrwwcMaldGGXwQIpirUJNAVuoR9DdLykf3GSj6\nW/2lIqj0rgMk/UBTielumFzl0iKbomsf82DGJ99koyvEz7c2iXof94gUVwXo\n0HjoCH6gtiNTbpGHuGcQgwqqP7QFLLjdme9lUOrb3i9ucvTsrHyWl01Oxn1/\nrdUvBQKttrczkOWXy+TnWNNtD9q8OlJexkvDp7xWQRtHWdGS5hXkfTbqJJmi\nbgWz\r\n=tt1B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-accordion", "version": "0.0.9", "dependencies": {"@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-collapsible": "0.0.9", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f6070cbd058f8807ca971c18c55d88f1e51bbbe2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-QlO6qRrn9ScLXy070qcWKbSluYrFTJtI+rBGTSX96axUfi2BiBxZsHn5zobXeQ0Tjghn67moO5LXSbu925NB+A==", "signatures": [{"sig": "MEUCIQDbwuJ8wTr4SJ6HpvdAGQGzIGUu6H508HtKpcoNiPsb+gIgIrGFf3mD+0HGaA+ODPrijvh7A1ZQlhxGFqCWaShDIfw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOhCRA9TVsSAnZWagAAbwQP/jHFBVFeieSMrFS1yt0o\nOQFeDHAYnAFA4VN0Ykm1xYWcGHNj/U9O8WxeBCCbaKRM0KK+G20BbLA44yS8\nLe6NsHv98D9VwKxt2G1GnWi5x5+UyiROeb02BznvZUv4wBbXKjCeecWJwJBz\nWfvBM71/v1NE3Aq3OoLkbBwTn3ZJ9g56I318bpWhS18ICRjQYu7SNJrgx/vd\n6ABRXnyNnu5NRTqE3nzO9tTiZbn6jkXxMtiTxQIkR860HhPuZwLV2eSfuaXw\nMi2vxj3WqlxvHaIBJPtNJEuE1rSSLJ+XX0zOSuMABrRhbQySRQzXoO/hH9UR\nLVIAAzA9Djg6wF/rrFViHGPdOWjUX/5UqGH/SfW2elYN0v4AyB5uDyPOwYqM\nJkSzwvHiMFB11Vw6OvqG1sIR1OXJiQnDoeF25CRjDCBD89cblCUacmOTWSgL\n1C6Q5rkN0epXfL48eAt6QjIdMRphQbTKIObg0q2FI3aGp+ePcuPYEWiTnyeO\nEDIN6JFxXh0Jmmu0R6+9yJAJvfZtxbgYhniuB8goUNDGMcW6uLplo6/fsT4P\nof/RqOPURpqQUQJCKZA9lEMOibyvTJXlylkbb6xs04nNksl+kScpASpbnH0x\nEAj0LahlsHLdxlW3m/E1bavvKpLay0Q5zfMuN2dOqvcM5vc5j6DN4Q0Ocu8J\nCQwQ\r\n=HWG1\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.10": {"name": "@radix-ui/react-accordion", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-collapsible": "0.0.10", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cb2674864bed5b6eb6a9b7d7da75162b61c5de2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-cWyunY+s1y10/aJFAX7RpBSU4dnOQi0y0QKqWtZ1W47zDVayTfda6YoHS8XjXoNMTXKenoVR8eDfAINeYUDAFA==", "signatures": [{"sig": "MEUCIQDAzb5ZWMxu4xzaTQggCzFjz5Klf45+4f+kUlWE6UHwMwIgZbXkiAnbNJxvU0BQtWcmHSLY0eshlxrr7i3uSlu0GL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gcCRA9TVsSAnZWagAAsH0P+wci8wHDpMEodB4U88w2\nLXvrqXE8SCXd8+T4mABH/8vzTc8DTTwzVjcH+JhatHJLesNfRhM73Xfk2eHI\nJCP6zUsG8yb+nVjSXn0KnxW1m6xIQgbSyiHOHTj2SnUoqs1nBgrtLeN9CUbz\nI71hqGyrwyNS9GxyRwSb/fx9IMRjNAZjOWDxYXRnX8MX9E3ogW/TVqUF1mb3\ntxZVk939zx4SiDj0cJDU4MvnW3+q4CJIjzLdxISF2MiB5hE1NID0Gti3KDoZ\nqrVuvRY4XZ9AN8pGu2EcVsj7CJsegwpNkv0f1IeqbN2vW2Qs6HQE280gTHRK\naGK3EyFgnsCFGrlpO0sIkd64qqtrNVfi2MK6llu+oMJhqkVXyaDlx2ZdyLvO\nfIFJC0BTXhRvQDmhRCkR1sROFzPGTTFa4Vpid+CT04TuaS4ELTQVsMHdzpwd\nGsREkQ8VCDiBuyA/gNW1qHg5T0kUQGLVD+dtAguCfNeKHSdzkHOyX1aKSRE8\nEzFgv2sLCTp1jia+OWlcqthkSYfjcckRo1cgtcEfoFYH4LMZdD7FUhb5oLgS\nBYmNe8YHcP13MwSsl6HTi7I2SFYqLb7j/D33FJBFPMFm7EJT/CYLN6TVVpAQ\n3O4z8n3kqiKWpxu/vB/Cgodhh+PPAQzno36zoOC11B+FVikcFWfWT1Dzk16n\nfHO1\r\n=dSqY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-accordion", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-collapsible": "0.0.11", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f714508aa65cfc69e28ca153479ff345a2e9d88f", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-isNpBskgtPum9EjMwA72AjHY6GWzcnxEqbDUJsgWK4cGWywKW+hipIcvw69YNzKE/D6EstevqKZM3pUbFvKTUA==", "signatures": [{"sig": "MEQCICrVXAdtMHvBv4kY5ISlAuPT1cfWktJhHqa0r3yBR16KAiAIi3BowYGXlKt9vUnsR5sZYw1PpayHR0cAsXRXeiSDbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HjCRA9TVsSAnZWagAAO+sP/3XgNDsmbuiH1/shu1rs\nmx9gvnPHS+aqpevCaMq/2wH6rAS78Tgj6Z5WLP1kle9Xr8SeydG3YAleASVq\nA62wRlNE8ShPhtyWcArdMIqjMhU2TYdo9IRnnMhZS3ckfJ9UOjW5kr0MYi+W\nvuKll0bcChtaua/O9+RxzmduG5krTk+ooHMJA+/ZpNDImYS3CF4R4duH8vUG\nN3r2/aqlmWqiq+Q7YNdETejvnHpTf6eZpNUFBJVXRsCX5zvyh2w96pFg+aAL\nvnKGWGbsX8AiFG7l2l7dyYlQbpVjoiMW3XIg9cl9RsMzLhxBE27+R7RFoqjn\nwfP+zLfjQus86twjOZPASWFcE8WJagqdKNzFISWScv08h9uWWxxRpRGtnwtG\n7Mi6TcrdCHw4Vayvuvt1HcMCpTPPzZEmsnD88CcEA5UehTliZTwghPOw+Qzw\nXm+KQnI9/3H/n184Ck7S4m/XRla+Ak9PsScKWWF9o7rJf9yOvAGuf6lmAuR/\nn9a1HZ92w2Oy2DAdUqsG3TKbMD3NmY13X8gjCYnBPQGxcFBnyd9781IV+DCy\nOb6RBeC3KemDQds48Fm//9WZ1kyjNOY4Xgep4v6W7I4wj6UqpdTa73hqnU7Z\nkF7lH6v+KIssfciphBLBPEgIeOeQMcp21pj3gWZSv7Muie7bkq2Olv969IFL\nqeFD\r\n=WMoP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-accordion", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-collapsible": "0.0.12", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "83a9ce5189d2d8dec56bec857acbe260738b21df", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-XBisGpXz3O5Tp3aYczDfiZdMaM145GABARz3YJJmhTs6zBofhJBNcEqfvi2+JSrcJAeq0xHZG+ztIg/V8B842Q==", "signatures": [{"sig": "MEUCIAozzYz9/K5rAFBNGfVxnD7jbfcG+pM5MDwVAXJJPFYIAiEAgVfGVY0GIy01uQwsX20kZSGMw/7UdQJkpz+yvHZLzgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57993, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3viCRA9TVsSAnZWagAALcYQAITJZMwFLiPfK/7BCmuA\n2YFmrVWpxmtlTUz+n5fkU8qldb+Lg6ehvLPjPzhrqX7PcAK5p7bl7cTh2jYV\nlV/DRVCQhyJzryevhjQ06ix1/NvFTwaT3Px2Mvyz0XT4IHBu2nPuyAuvp8ED\nfp5Ki5yVRoXdoXD41O+ImAUKRl70Xsapa+hvmXTxUie6uhji+nJ6q7K5rbW0\nl3e6ugkvz/Qihpm9gJptBbigVp0gRJKK4W/rwzV/UCW4jL1cgk7bez3CS0jE\n4n2iDPID6ZgnYZUh8fuiMLY8KN7oYOseydvQ4zJQVHJMGINJD9HAq4M8eqfR\nDq6CQYe5gCSimpveEBYqghk6Y7B2OC1/vGv8v5Kdk9sQ5Ln4ySbfI1FkwWsU\nv7x7KYYirtx2LjlXALxVPnwNsOgAuo6e2JARldM3cqC3HOyBCsikr0r2gVCI\n30fsAaYf4G0aZsXuLnFE+i1lqTarqGkKTddtYakHVafb/rf100aSKQeJySa8\nDTVKHAVWcGCG8KoA2Uo8yrpl55gJJ/XYaNK/cy/5dF7SfDedHoDJzCDuyd84\nwFY8hdTnQ6z81v1zknEqyQ4VuohAcb+nwoVzUKcxmIo01R6XhIru84VgeBNK\nnVC+7UBcMrAVO6pA8OvjlE9K39AkkqE3Gsj67yRX9itfRgoxFEZtINh2vGrp\n7zbB\r\n=Zg4l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-accordion", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-collapsible": "0.0.13", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3a8aa18197f8df7db2f6e143d6edbfa8eb97fd8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-R5ivoiJR3Q4RIJycRleuGQGh5UHXQ01VdTvTUA6Pfdy0ppL42NLDjP//ZmGHFOEBEzfVhZAUuCl+OIQ6F8W3tQ==", "signatures": [{"sig": "MEUCIQCguxFgXpzGpXGyc8PlO13+uUtnV8bw9kFiO68GsZ2CZAIgF6WTP7rrd7mDDC1igHWBX7qOznEFjm0MFvm8gMr+2ns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmRCRA9TVsSAnZWagAAKCgP/0MDGyd+6h+sw/vmxCOs\nj+l+xZJ23AiktRKkA+1liqRUZ6bADOXvj9mhTdu3Adv6NusDNdNBYPke7vl8\nW1Ru5ceQD+yzOBzMRY9yerwSkJgBaQNzmUbJ+mW5A1nz0SXJP6tNpuDQJBqD\nzcS2gfHWVWfo/E3ii+puJw5X8ZeRDTNNAxwkf27cjajF5ohwM489o4MR/XHp\n3urKtzm/YSW2hhAzK3mSNbcfAhXanUiZ0+9A07oOj7eiCQnL3iba/XytDWlO\nz7akZYpVhEqJiyekmDTI7rc/Ypg8SwrXJtrePbcj5hnhGzSx2GfTVCsd1yrO\nvyyIGxfvBsWtudopIgS+E8nB5uCkh1PmLSAmYR25GXfvzuMicv/JE/QQJT0p\n/F5ZIDow/J9zquMLkDxklPbR5a62XQIh85s/EqvlCa4/ursTnpUSc1O4HCEx\n4wqFnafCAUxYTkTPA0/2kz0U8T4x4TJ6jiV5rBB5Tg4F6kp924HovXljIF/D\nmQJBpZHI40c+I7VO6L9Z7UgeeByq32B/hJSIiPpjJBVJPOGsDWqRyVoP8FXc\no8MYY5GlR4GAKFhp7zcTY4uIiryOPoSIowStZ3RVBCXFZ5Ux8A1SrOlHFpuD\nwnNIHb4+itAXaphf5Sq2jRXoAVZgfTFeBEMA+OOqzjE6X+bqPS0zAP81ujwL\nb5Fx\r\n=dSLl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-accordion", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-collapsible": "0.0.14", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f4a740d2694c5cedd0ee40ed31f2fbb93d46f62", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-Jfqy48uOzpFIyYVSWHLkpoDi9kUlw3X8G5DxLf/TZFqWDdOkYkcjTAOMWJelZcwikW26W2oYKV9Rvat7cLDaaw==", "signatures": [{"sig": "MEQCIFfG49iPUiOmhAaeOJavQKGBBCZ3dFiwqIvSuxPFG8r/AiAkrk9ddxypCz+TAJviBgLi40Kslm1DKZ/0alD3mILT0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7FCRA9TVsSAnZWagAANJsP/A2+SvczV1B3TLvSADwv\nIW9EMKmg31fs0HX1ZY52GtgyfFqNQGRtZuKIxYnVmpr/OPhtdtz27BOXDOSB\n8NZ4AC5EjfJ49JdCCIEi2lMnszxExxbD552K+51yIuoef0WgV3OM0exfGE/T\nJLRNDTVm4nLGmDv3TUIE1a47KY02/7DA4DlgqXlmwsCGsZLyyj0a9WJCyv3q\n9WAz/KWPdlmRxR6/wzIz4GLATwe6D54BBKPT8KxHqkkZS562N9XZHnhvIYoT\nTSAwQpT4LQX6wyKsjc35OCI6pJhG+TzJ60QeaTxYQBBs/9EUrPik30ylou2O\nzb2bnXgKE8NjbjTu7tB5deqgUvkhI9ot8Zng58b9JOl/gIqNXGzIu5A7Ut6r\nlNOa0MapfmI19xZ4STokrcCcXNd3lDJHvFm2oJg6gBCmTCis3kSorbLfH5Oj\nWMC6GjBvqrB0dkAD3rwMdGLtq8q99nL6MWnWSDONuqcf3r+vN5YvowrM0Yc2\n4so+GUkv3rDRAUwMwsfR7jV1YpwA1iZ2WNghsbPiLQl1qxQpGVMEjPs+0xNo\no5EIJ210Xavn5qDHCVK4aNq6d8LPe4KUYjIRW6xmk/phLP9tPdT0+E3jP8Ua\nQlqWH3sFaDGf/kYdIcXnIQJhJGxZnpJ2jB9X3PDhoVx+DXlDp3fES1hcg+3z\n1Syp\r\n=6EVl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-accordion", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-collapsible": "0.0.15", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "77b452f758f2b8f266847ea4b382739376d9d558", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-WLqPl/M1VL5noYMw8AmM3dEbUGlO7a1Fp3/upRl/a1BumYMuVHLSZVm67NlP+VqFVaBrbTpMzm4Ck095t80vyg==", "signatures": [{"sig": "MEQCIBqrxWOR03hzXaYClcynwjE6fhYSiZJbEBGrBcqzhf30AiAl3Ep05wHEGMVh0vKleB2yV5sxhy78GgjQgSprlVHzkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlXwCRA9TVsSAnZWagAABIQP/j1WI6WmBZ+KROZ4gVr/\nFAwZ5voC8XOpvTmyGezrx2BXnmlBFSZkOv1M4ZbMkFYNOZMITKo8unn9w01c\nRcWtHKHs5l+l7zeAa0Jx8qfM6LPG+RGciOVVUzyaO6yUTbr8qRf4imia1dsV\nu2qCtkLwDeTRo+/kwj14V7b+3ECryRpUI0tTF+LOvzSplrekMl8jAb3dhNk5\ncdC6ISdkAWNnFhOLt6BkqmVYb+klBsjsPgsndQ3tO9dS5tdL2B48jrB6WB8a\nGJFrb3zTzVJL42wKTxDvN9vGe7s/Fc6+DL939cT/i+QmRJtNzcbnPuUCTGFR\nSfY1gImgHw5TekJ/Am3Uisqw6lwia/y33OgWtJea228HFYoEqTzD3js55lSs\nCpHzU3QGd2NyaBjfcUkPkQ7Gn1qAFh/o5qpdRc77wxRp3Xituh2EY9OrlGjg\nQDUaEKwS5rxh7PpIpNPvhOmtsdCHpeOPNFF3J1EjLfi0K52MvzcAVJYJD0O/\n5S1k3VRwLXRuD/DlHFxmZL0xNmdj9OQB7y87fzpG1Z68EccxBd13stlbFFyi\n0ht6ebQYfXBI4yCJ/EAWd1iYRQSPeTGNKsAlBmFRa4JPeHTPrB7sIXZbvG95\nAiWTURZuUCFpCcrJITPbWndOSxpwTdZ/ehHhjh23KHjA1S4usshiccVBLvGg\nPPXq\r\n=JUcO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-accordion", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-collapsible": "0.0.16", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9743b9a45fdff40366680a3df799d5e8daec9ce1", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-T7x8MuPhlZbENotvaGD2LqRR8JFq1+tK61ajai7tCAsyOpmkRpc/G0piPojm4R9/tFRYG4laCb4M64FxK4liig==", "signatures": [{"sig": "MEQCICEWEbIKoK2UNyF8hab8RMaXbbqAPDxFZsJkcEkCHuuPAiBYoI5fI+Q/mVtv4oFLTLGRtwP9RWUPIskkHO5qt+nJCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9gCRA9TVsSAnZWagAAtuEP/0WGPj4kcyTfuGL2WaPr\nUL4J7qwru3WGmm/VqWM6leOcoOy0aZithy6DchjBkVjXuqhOHEg/gp3kUhVZ\n8csSqBp3kVVDm0wfSIMHRErUtWOcT48cv0zJ86W5mIr5OuzOxJ/WUATkKCZf\n3okMlHu3Ps4UuHyJ+jGoX4rWrF78pMuQiSCLEnniXH0qjGfJf5GO8SVaspcS\nnyePEpOj3iv6ZhurUbHTwFXEkgMSkMnwjtJKy7sqmyVtpc60b6QOCvfBCaEW\nktel+tPtDd17av9CIZo4fmzPF+xrfWDriQV89j4uexKwnp7r0uKbnFrZ+dSI\nl5ZRQPbV6RlUUIR7M6/tbPpikcbSCLi4GQd06h98w0YS8wk5dfFZFLFS2jPv\nLwuFHgfuxEPYe5cxiXQ8JV2nYnlFt26/OsgxWMienMY3jVzSU87ysV47En2e\n2k7k356q5M2rnACytv1Gpzy90uAqGRGJUTl9j0Qm0ZRsbVUnvTb31CswpDsA\njWKBfAWMcEuLnH4e/L+65dE8wswG/yuncprzFGEl2xSCvuQJTtfOvp7xNbp+\nSVRZztlJ7Rp2+1FfYGd6GtrpeQuZjVZk3q6jKwFKfxRwXuo9C0UlRUVpH513\ncz5GfQ6HZ/H4yeU7iLN+w5QqEb0IO/nG5FYB8wEdEaTbw6vK2cg91uQNcuhi\nCl/S\r\n=QW3V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-accordion", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-collapsible": "0.0.16", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "378b6512c7a8b029950f651ed6007f410e9b438e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.17.tgz", "fileCount": 8, "integrity": "sha512-Rf4RJBDTNCbeLP1NB1UKGdy/+vLAAv+GSbN4gEgNa8MOBkZtD0nq4qwXbYdJPcS0ljWara6OBCG0cr2f1SvSPg==", "signatures": [{"sig": "MEUCIQCR0O5ifAfiw/OFKNuwFzyeCS81xAOkxEnsymSY2e00XgIgZ2o5AECu/4zKNtfWn/YT3JRQrACjBlMDMvOW/JUkRC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GUuCRA9TVsSAnZWagAABB8P/2mOVWuMpyd5n1mE9g42\ntkwCcapV1PY480xzUPHxvkmsC/yuPg6nysq2GVQpz50KHSwuxm2d1iWDw5V9\nVxNCdJzrA+c9sv7z6Fj7Yph4kXwNg+Bh+jGBD0Yx4sylqCIadPBBk0/NmbOp\nSrnZj/FBoj6n/TRRCA/lKUJs6zBO8B9Jr6OXF0efkLzJuoOfldpoLhPmO+J9\n4+4lb/MyUaN7fX3r856qCOyUSUYvjz9vdqywn6rH1L+WwxA4FoWxYEpK2rDJ\nZBugr6ZI02i77dWcQBNo4ZMTC40dvNjwJY47sSJqgWquX60e/2RvFqRTMbTt\nUaBgO8Q8k8e7MGGFA0BPOPUF302Nas2ux0PqxFTOpP+YkcEjLLAjkar375cA\ni/lpEIWYTH/4HwJ7n1ZxvSL12IQoaaW3vrYj43w0l+JPulUTj9Rs8U7z1yv4\ntcbv9GKoyv4nwD5T0/LAgyOHA1MoVtDCyP4CJGQDBu20BaaX9kBRzoXkrD3x\nwxj6xSQ5n/FgnO+5pthaMV8IeIVi8tyQB2QeEMwSDF9gklMMEg7ACuNIlS/b\nuV7tBrIKqIPS9EyeTjBT2K7wMIDRLw4YLGJKYwlyV1cWdSzobZSBs72NmGqT\nmge8n9NJH1BgNaTgI7iffCw8qUOxLqm44br20m9qFO11xIsARE/3p3MGeUkQ\nnDkb\r\n=7yMd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18": {"name": "@radix-ui/react-accordion", "version": "0.0.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-collapsible": "0.0.17", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "63cbe44250300f9f3ed6d59e8150b8e25e4669a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.18.tgz", "fileCount": 8, "integrity": "sha512-NksTGLiRbXOpAV1SdQB7x3ykPgvY1veCRMBFzz5D6NjVMIPsXUXmCeVWjCFtB287e+yBLg6n6TJYSMbomoulmA==", "signatures": [{"sig": "MEQCIBCVi6UMX2j+OMYHTCrl9jgr3mWXxTZ9EX/7vXvIqYkMAiBbYu44HfQU8htQFSTsp807lUW0P5oUaXU7VOF3hDqKdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61449, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTjCRA9TVsSAnZWagAAU4gQAJl83eIn57NiCAH0ELjN\nwxql7eMAfUJIeOk9OzdbKuznwvUkdb/N61IV4hZ66m+ROTC62RrIgBWTB7/f\noEdRNwPFuW5MV8oNHAJuw0JdxsITby5WX5WyvcfFOs/a9ZuIEQq1yknwByiA\nk6Vd03egr7Vsp6RuZNhRlhkMdf8dpbkZhpvlzyDvAcJiaSd/bNUFo7Z01e/H\nV5PYnS9yVkzAgi9wxxwYKgjItzo4sgH8qzPj2pUt6IX0tmzOMJ3LSo4jiMGq\nSSq6zRVPZD0Y1Kp+79z6JODZwizRVpTzJi3hYZpYEwPb+k+WelUNA0xKKJXy\ndmzzXS7e68FM4TyH2GAMGv0aL32M0TdfleWyCUS5lgGChwkwoKPd37ux+GDd\neC4Sjj3giX57d5QKgQghCt5q+/CSs1XJkE7n4gt00ii663F/cqCKS39gYark\nwhcQZBh5IoiYjjf5F7wbizAhKi58nM6U8dcArak4A5o7psSCA7ezt0JkQokV\nGjw2gLaMLBxPrS8aeUEQGCMaKzVkHoLqpAs1iV0eQMuoVKwlZyRDKAXbtHpb\nQLQRsPfcrXOYI7bj6sIdPvcuGqEqew5kbwIHKC38iNefDbnapwS4GE46GTl5\nVXiZToaRRX4eUVlvATqMWNIc5jpfFN6G+sBO8/wrLpzSaaw0zwvkCvqhGx44\nqq6D\r\n=2hfT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.1", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-collapsible": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5cac17c498b065974e75aabce190471d4f8b1d53", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-p17mYAyo1BkmDlJWLBP1mfM9eeyFIKUXPjtnBu9ataGK2qYaGkA0Aj4H2zsERwsgMccHjLZOX8/tKj7GOequLA==", "signatures": [{"sig": "MEUCIQCm/3f5xkcbuPtSWyBkUCmQTkoT+H3zakPaTcJe6JVO/AIgFe17C1BgjlML7fos/ho2voTv9JcxXlc58bywA7rEQ4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpMCRA9TVsSAnZWagAA7FwP/R6JAOQeOB+6vsmkWigq\nBC177OrBWFoFpRaBkLEQJprvvfeLm5zIn9F/IXdKvcN+0N34+NEaWIcR3mhK\nnQHQbNFDsgUh1W4q5HJivvXufvGOWGdNJRupf3FNVJGS8tBx+y4DwKTMK5h/\nDe+yL3ENtX3s/nHmuO8YHqj3zEF8gNOjWvGPIIGKDpitJVQOUr0oA0AztiJb\nJykWiu/5Q5OQiBLo8wJ/OvmXVukklWQ1IMGDx2Yi3R8Uyu3aMHTu8GFoMfvI\n8L1USd9ZqCosT9JU10eYwQsVm2mCa5zIR0D0/jDKlNTezyvHuAuYqo5ToiGU\noSQHazyCPuh9qvW1oB0iQnDLnOUJ5845/pO1Bnp7XD2BniQ/DpGS5LGa6kgq\njU6Xjac47icLtWJz/OacRvPFoXsO7q+uinzBXMvR5Ho9cF8ZJYGG+QWlSOio\nGZSVVgd4FHe5CVuF58BG6QcG19nc0pIEux1ycAK6XYed7EMXuW1/5B6dGa2b\nSnXGbrenWgd9QimYv33cMZXSCVjI0LiizoJFge1HPDUzz9ilqBXfqyHU5lVt\nzdH6FYX3t6ReLPaonAYKRpLJb5WCOa2sEoHA4hRPYcLSMK7ACYqYgc6uy784\niOGh6rBimq9biOmGw5WPNYuQq/QHHMuWwIKH/dd3Q8hSItWQJEcKK2oqWL4S\nrw1n\r\n=dY9E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.2", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-collapsible": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c63342d0d64b213766a27e5951be04de6e06085d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-gRLK52aDYrhdnEsTR/UEySxxX0CeuOhritNCVoe3ePjrBB4YZKBHnRoyNzNARsd6L423VUw0nnh3QL1Opfa7yg==", "signatures": [{"sig": "MEUCIB688qK45oC9qqX0NkYcPoa3EDpuTrBroSJknmyicBEXAiEAmgmlizcABuA8HW5/B8rldDzzCP17M5m2kZh8js4QHYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59478, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyFCRA9TVsSAnZWagAAQz0P/RFKbccvv4ncyhxck2Xr\nAi080LLBEfvkKfGq3vNnq34KpodUy9pmfOkRQHEhXwqToPkmTJESnqOCqoOx\n05p9D8RQnApGo/fHdxtKQIWSx5rg4xlJtPCSYwfzBhnwTXpG9ZEnyQZgHrdQ\ncJNUQA1m/9HP+sV95IEsO1ZX7OVTxwKaPn7Wa2FErjJgk/m/umcQi5ogCSYx\n2Xci7GX6qQ9EyNbyD2ckJ7Yufdkdte2yRSYaYLxJRZRZ9/c4//+qmjE/RZRb\nh5l+MojNjklV+vXJ3pKq8+aGuBEFX4vhBfZLFzTfQnjGiLIQm5DNvFpsp1v+\nKpFTSboIRY6fR+SH0qZjGc3DJfZOpMpxUfZaoBaIO0FJOBHWFccumI54fu5W\nbC3dyCliArB0T8PINCFKu4VVZZBLIabgdx9oFoSrVCt/0YdOM1dsHKdk12Zf\nDftK8C+C06UH25MIHMLPEgXQVfbJ8ifRTQf7HMoTuPIOq/p1uAlSR1wNbA16\nQGBa2NOdma+rRtx8qTuIauuJjx532BVsKoKRupLNcm8lJ8CXgJ8TXQE4PX5m\nU/u+AyF+MTsb5Wcs8Ef/dQNrxeiNkIdCxpGkTzIyEngfjckid36+iXkzgB50\n098IKLkjkzrL9xnKFxVZVC4kWIg1kL5tixNRInb2CitA8Fr4BJwXTRJtVffh\n9Tfk\r\n=w370\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-accordion", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-collapsible": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "08e5e1283d6bd42656a80b59ace39dd0cd6841dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-hIH+kE5mIbo9owGTWUaAxAakQoSRvzoY4IHCBrh9zUAm5cAw1EIXy90pBnC0wGY1UWaUV7wzwvxEpSM5e8FDjg==", "signatures": [{"sig": "MEQCIBTT+UwjilRpPOEDRu40YRHlfI5RLTDWj24pFiAqRtkZAiBoTGWHSvCmoFwg3+jqSEc4Eyzgps8gnpt2zBHrUu7tCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmSCRA9TVsSAnZWagAAtToP/1EHm/L9Gp6ankF90yTC\nDzLtP0XoixXTpcEVb5D+OhRFut+SN4M+jLWdZ6zEpTzfKeefhhSGZmI6SXmf\nYkJiRJejUpYmHlab6yJO1ccT44j22RFnK3le0jpe5jdiuYPjaNxDeoq9qeRS\nRF0n1IFJeGNfGbwaiRY4fLvogqZHUTk/BEudrFh/RU/bBnIQ320pAltbGxc8\nroOPQx+q/9oPs/lBVcpa7sKFi2v72zUvMs7VVd6ZJq9IHYMbaMWHkhWN+YXU\nzI4x5Aw5DFC1doNK1Y0lTX6kTrAcGjI5t0eoCQajR80urgLY5py23LwHlqZO\nx/TAH01lK4XFKaeFE+ilUrSsZQKqRl07BWSeEkFlpeYZ6kvhF9JrYQCJ0cJX\nYQz857ep3DR+xv0C++VZQWdsKhFY/3sqrhfVgEZ0S96e7yUOSnFyl38fM3xt\nzTKcSrkXEKfnGurtyJjhM+ERjqE01p0Jivws+LjsxscR/gTAdpMk78etumeg\nEPLZtqiA39eA/AT5O6RwmTmZZdnNyPa8uJVqxwOlgudmcI8MZWnSh1jGjAdG\nqujHnh0FqmP10ZqOKr9kloz+4O7O4WE338qrIbNKIB2Gc2gQnOvJmkU71PwO\nAzPcjNhFxkL+9CiRvxAP/AjYg5x42FBhTcjqL/xS6w3+CpI3mH6uL7UWWdsB\nc5nb\r\n=v9Vg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-collapsible": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6d5d080ad2aea3e1ce11a5359ba2f2819343ef1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WpWd0jCkfsZ6FU0Hin4AFLQzhdm0PB22gX/FkwzTiVshZ1kmT/79gfYlCRA0i0+Usd8olkhfkr8k6nV+B8D1fQ==", "signatures": [{"sig": "MEUCIF15OgDMKQo4K5xQZhPApA/Q73THaa6Ns0nzKlJV5AoSAiEA3xJdh0bkSnxAMe6CGAqis2jDnFQWtbJ2Cnlkk+vCBGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIl4CRA9TVsSAnZWagAAFzQP/j8UgTNya1VDdsmg10Pc\nqdgO/z+h0ygvDQdhiZ+qnn9M8p1TzaNvVBnfr2m3O0F4hRSnqx6W2t2qp3Kz\nX6G+UlSSXXm6/CL2Z5mA5PlJRqOieTujHPfBOpn9Hs0wN4lOxLFNspfwKff4\niQDaXSHp8bNtcxdrGS11mmcviDgRmGnjtQOIUgE3teZ1l0HkumHjzn3ZpnUp\n15Dpr2SDxGIbcv6EUaQcxAHXgtjSQUqzw85uvFuN8+GdXSXVpBmGugFqOKfg\nCQXbGHL6Sw3uh8FrsDOhD9wWnhPA3+/Xp6OG2ddJhRhgFodamKmwzu6nSwLu\nhk8snaw2tQrTu/aYazg6d4Qav7YH3e2TpPflQECLizhKydsgiYQp0doLpqbw\nyd1aUYfDXRtdHH87LVMhPbWeHzS6F7WITzvKh6mHA1HomlYo2CXnfLQwWDlr\n1qJIDOvzsgzotEFmghM5NksXbla76wop+xmeiVkMtfgOfQp0Cyw3xZqrty1w\nq/1AD/Zr2Svsv67Q67AOsWVt0HQwQXGuIHzXBIlRA8hiGCSqdWv5hV2nYm0m\nX0nHVXjx5sT8g3lHUVu/s0gzl8qnT3W/PR+Wjz3TcTm9XXBYTdKIQ8iFsgi3\nKBZerqmYSPpRg19SiZLckyyg9tC3TfCiYc02pLOTps8/vetF4M3HqBHIc2iq\nJai1\r\n=bNrf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-collapsible": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0bb116e07309970d3f02ee2923bb4c51a9c690bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Yyh2U+PAFJTz9REYMGqoTMPfR6hXXI4Grou3j01uBmCu2A+Yu4WeHbYDzOhT8m0Vj4tmcKzKRvDb5aEiA3vW5g==", "signatures": [{"sig": "MEUCIG4xSc525kYQWKpSRilrbepjM0C+ihTw2TZkAMj6eUJtAiEA6qKUuCQ+6fikl0THACbI8d5GbRSLBWDNBHx+AvC3Ms8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdutCRA9TVsSAnZWagAAvM0P/jj0D7qaPuYjM6+9KX0c\nro1HNl5dm5zhk5HHHtUSvcqEOdkK9d8eM0S1Q+0bzUkLI04N5Ou6HVLAkseM\n0BbcmI9kD7uxl/sLgJhTR1M3ei80aPkVDRwPYoEFTKjr9yMumXBoPjsUzMIV\nLCLgTB20SGFBDsUGMoKYrOayJQQIkObp4+/6iqmHBBNKDBsoj9zS1wR6e6JW\nbCql9o9cyVyWEc7ZRPtSykluHzFCThEdB255NAbldk8BkgkxX08nCg21B7QM\nO2V/lGH+S7xHRUV4ul6ZSC8hL7nxDFqGshgcZfx9TS/EmoE42ZsRflFoyeya\nPBSCiaEeSuhi/BQZKWqSM7EVLXIS/oDPtnFF0bSg2mevDnF8angcHY1AFdyV\nLX2UZAFP2VYxo19b0qDJttWciIbM3jvGbbLUs2CtfJr+MSDBYYiqFpbuc/Os\n/ws7X8CTNMvRDqVo+jNF+5qbgSW2uwttsgzeraQvMYdHUTTzm2oMqEp7ElAJ\ntan9HnllfimCmidtu9mhb9wEeX2B91NDrh7U8sPkG4b7lf6Ip131iE7oRhln\na5ZQL1mtwRLUgsNRpluFTlGq0kUrTzISnEl46bF2OENxv3mZ5/CPgi6PbSgs\nubjHKVUGdklUCStF8VuKFnRarw1gk+ORptXrpkuNBAsZmoc1IGyQ0WR28NjX\neiLj\r\n=s8Ac\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-collapsible": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d3dc54c32128606138360580fd4f1847fdd9d93e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OJCd/+834mR3Ru19+KcmhFMbLl6xmqfJvqRLCdDQBHwhT7j21dkFDLMPETfZUqZxUL42TtV4jih7MEr/1J+gpA==", "signatures": [{"sig": "MEQCIBMjxs/wBs7MCewnHnadjA5MaSj327jWAG/x7YWwjzeFAiAlEGFCEVM9e304jZRNSfngJ4nPfot0jGo3dXmNn21XwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0S1CRA9TVsSAnZWagAA8SUQAKKJlFtbn1b5CgCB/dNw\n8J6g3lv/scrMb3ylhoE471OVIdWRiQXJ/WbWZKYDhVTFhAHzd2ZNBL3TcQie\nYuGNcNU+9JRVariyjitBZN0sUkd+ZnTcz9e2zG6RFCPVZJWQlGFmLHJB3+Uh\nsrTSCb1Htq5N/sYErkCIa/Go4pSzE66j10e92mmPZpxtEtzJX+Kfs3Pt571j\n0viMkeQOfVkDGRvORIJ5OwtaiR7Bkx4GtpX1HF1hiN4tu1i32tY56RUxzGO3\nJA0kXzO8lGVTiP+7yNUm+A8AurbZIn39e+pb+viEu9OmsO7CXfYa63+5rqgD\nrMxsK3d14+d6fabmhV3SXrmUtDbJ5wHLF63sgPBRc7nc4rnD8Zpe7AL/1MRN\ntrbNPAkDDr+FO9N8isoAZAP2runqzOHsghrndDzQ22Y0ZmIRRu0Ca3w15FBl\nLn7kCcmhx8Ju5wZ2HPUH36ki+FwHO4s/AjlYyWOwjudR+oysr7CKXWZWxCnW\nVJnNb05ANJfGyrcUnATKJ4/x2ckKNSrEAb7GlDcGxnUiJK6EPjO2uimdMgca\nFmc8YP8c5lhcJAl/F3Ysua17yIVNRjkSSC9ywIRJSAJM1NwTliSA3MNCeMio\nlLH6pVviod6zM43D1eXsakxUFOhJnGNMruE0xQinwOLAQqWy9mFWUM0mjGVn\nHfp0\r\n=Xyvu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-collapsible": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "04039f9496deea3cb9b7f7ee9b490435a9a4b6d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-m2Bs5TTwBwtvM/v6c0j5d012K6IbSt60u2HAr+2Rwp0VvNGmAa55CKDAocgs/JO6iU+Pt0bAmpfDRfzTKKtLgw==", "signatures": [{"sig": "MEUCIQCJwQr14YecE2shG7kaoxIWycLuPoZK8bWqZ73eYcQvkgIgDSGeJ+BINVLUqEFQnpFraaTdTZUyqOr2/5F2ggh9/h0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1ybCRA9TVsSAnZWagAAdQEQAJgPuiRieGaMC3MdvsIo\npZ8QjK2zOpb6j/5S7TjCeZR7AS5bTZAvPtE3FCJm4Ad6hRctBeSPtRsEvHOq\n6GRZ4YpDn5YJrFkyFYGd9rzX83UNyE3cKkDbjM0EPIXLk1iqAPagzh/hqBJk\neptGeGxKTdR/GYujxnZgV799LgkBUKWybsyLquRcEsPIo4EW4Ub0Ra26MsIE\nI6g4UJjIgXm7GM7G4WCjWe3+M7e10UplDHwts6QkS7ii2pOB8wCCXjKniMLr\ndTS8fxbpA2T8wIlbQl3AJneB0zm3ADRDurtS9OMZgYz9NiOtt62px7qsvfEv\nc+h4vXEkFzfVDM+l8cs9VBNbd8V+cTIVXGPiDyhjZ2emnid6AYAXXuSQyt4g\nk+1bvk3uZC1X59+6ecyXF9Pwsz0nwSkD+nP0UQSnBZixQJ9vEvLBhT58U/v7\ntqCgor+YGHUeJzPYZecx2Qfhf35aobOuIpdVjUlSGdmimK48mKFLpWJCe+F/\nkVmv8zdnnSryUqzsKKDzOvSuUjgg3MNltfwzD0aMRl8XQBYVzPW6CNBzaTcD\nmxP42IQhxBBGt6AMK/bso3YpJGVc8F3zGJ0uzG7suBecEe0sRqqtW6ytCX4u\nkVxR0fB7S/ok1ioGKOhq0G4G1AI8x2E0x6S1PYrhTOyyqERqAgffWd9XGrBa\n6gPp\r\n=fz2I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-collapsible": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "05e0d4ba2db7e844e335e51a9b11911b5f7f5e3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-NMF4RWMvewDj+n1sG9Xbpdmcho3VR/zaUrNrGJezTMENg3yQCFUeP9pV6rEFTc/avqKaDY3RkK6j7xsXmS+Whw==", "signatures": [{"sig": "MEQCIBhJzdmNX3alsjUe32QmnH7/pjl6nRFXKSLIdYt/j7BXAiA+xfIFdDToQBFz/xIX/qtmHQC4/uDpc8XfjrphPbmK+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFhXCRA9TVsSAnZWagAAtFQP/3P1wgneP8Nw4TrWW881\nvR//s2OOJEFPwrrCIzfp9Vu4mvOq1PR1TjE7f+KER9eRCICe+rjQxEYr6suS\nMbLXq2CGROXXS2uxPrDVJqPDwnVhRhpMXOkwPT7o9fEtqXynQJC99vnbjJOP\nyUdbuVw3YFGqhzMuu6OpbSpkNGd0sj7CgsqnDz9mrlvsrGLQ0l1IYGY0KorP\n/6t9wQyGuMD8+qU4wwxeRU//7bo5er0Wn++wUllEQWAxyIaGficVjknlB4Dv\n8i/oasdkM6b/ilBdHMYlb2b1JvoFWoshVxphq9d1cKltkET1JmE4xNg29yx2\n0aW1kM1Fwc3DrNQHrM+JNCLOwCcHaeVIlOcQBcNAXWnMQM4AQcM+c6Gc3Gkp\n2IQCiQOnxyit3lbodDtXqpy1zb4TgmWtPmwtwITPvNhA0GYB0H9WVlcfVUBN\nQEYPq58mM+Fp2c2iXjXmlCmFX8ceGl+MgmV/rnnAwu9u43Gvu8DKw76869sw\ntM8RqfcStYw39b+SQTFoug/P89HUryvx92616fRAwduJnK7MWmROw6Z2P4mL\nSv1FS8g6mlZnMumjc8b4heMSRw+rJJl1GxJkA2IuS8lZ8kUusflvj/vIdnNx\nQg9kC5Lg2jQwa7kbapeWkC/Ou591YJGGDuGD87w0nx1N8Rf+Qo2f0Mep+I3Y\nvZZD\r\n=6XPn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-collapsible": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "697956bacc3b3dae5c12b89492db3d50e2fa0c52", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-ZeR2LIVlWto7w2NO8wvfONyf0J781nle8sPQD6/MkX2f+MVGNpFF7+SnM5/La2n9qw9Mv2Zknuvb1DUgWKEclQ==", "signatures": [{"sig": "MEUCIBhOO/CgjqTPRQkvw4hOdF5s31htJJyHUEGU/TeFryIZAiEAomuxrJjX3RyBFH/LaAGfi9XOMPeT4Zyk037dE8/Qbi4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578}}, "0.1.1-rc.7": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-collapsible": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8247642e1a8b7542f7d2bde2de3a342f074cac10", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-szGf7G9ByJnTMXybcig1kaBs0dK9VSFgjq7jdhFYPCArUZSGhB6uEiTYOIORttFuzJtplgQQTyg0KbRR6sKE3A==", "signatures": [{"sig": "MEQCIHtA++QgOOOqeZmMb4hG3/ILuIsHAC7aknbuJTG9QTgKAiAcwPwWoPfw+3XUkRTip95YzCo7+7V9/5qI+eJtbGKD0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578}}, "0.1.1-rc.8": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-collapsible": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "42bbb44bb71189baaf8c9faf91faaa049dfa882a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-XWW1ARYl8RIXhFcUj3C/uFwaTWljnYgBVmeA8JS8kQ4hLP1caeLqTGWCNzvGfisiArxzJ2x3ZVDnmQbVlxn55A==", "signatures": [{"sig": "MEQCICaESf4XtWpr6xH7uQHV7KMmRraiJqnBBOyv5Ct28cDJAiB9EcHVYH5X/vXWbmJqjdE6C/4oxx2S5OLsKgOl6oWBRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578}}, "0.1.1-rc.9": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-collapsible": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b74fce90f4d8880c2b5253648ef09b7f15d1a318", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-draHGxPd/b3tQIQfSNB8IDLfo107cXv8PMZ28ppeAKo63iR7ZlB6hMlLXgREraRUP1XHgMFwobdkLwuM9ZPWMw==", "signatures": [{"sig": "MEYCIQDKfvTgbbt3uZuoSdQGIlnLK0F8mpBSv9uTcpStKkYmFAIhAL4ehqeKxeWWNnwQTmcnGTG2leon7TQJQgZ9oSl41lMW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59578}}, "0.1.1-rc.10": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-collapsible": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2ade065dd3b64d0865c06eb67d1d2c6625aa804e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-7Vb4oZbBXlDHrdWwOqKIwzefyXJyHnExDHakRyyMeRwvtdzbWs9NGJV4a2DNMCAWYBHv+ncXezoVyMUP3mwYyw==", "signatures": [{"sig": "MEYCIQD7FQ0Gb1Nh7RrrBxtEFbdIsdG0pc1Hj5iaGL9JsRey5AIhAJE0MyJtlI3ARLBlTuhzuL0RvXG0gR89IWL3aHCtxoMs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59581}}, "0.1.1-rc.11": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-collapsible": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dff422bbe8461a99d5a3e06512a3c43bd1f8c884", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-LwGOx2Pa8XKIKN3QnlZtGcqAC0XOFnTpgnN1IrDcn7M4DUqSI8Nq9Pgi4uu7oFCmWaxVVWKA//VuNHj6aJICKg==", "signatures": [{"sig": "MEUCICpjdlHyPGLiPEACI6l3ZMG/1pdlpbKO5+Vkif536y+gAiEArbDCgaH0RxlqkDESxro+l2Um5PVU079e4/f5eo3PTho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.12": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-collapsible": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "db6c4ccac490777f1f6e5268f8df3a4a08b7b1e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-t6X6oJSXYfjay6Pqcf4ZmX0gE7u87Gu32shR9S6+SJp1HzfvcjARNfepJTNeRxPF7M6QXfjqrvRIoJAD8mnqtw==", "signatures": [{"sig": "MEUCIGKpgZ7oV8+VbbyJNpZcHH1rW+K9rI8Eilf+p729cenZAiEAod+Xr4wP+QM5wsjWAj3JqGvO79Ss/lnoZle5PFTwf0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.13": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-collapsible": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b5da3a96a914636a1fe564ae2f92e771e0da3c88", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-IYeXeKbpc6gxz7fJoJzxBUDkopUO0IE+aTdqbAhyNE5jRrdPGxFeIFlBqk2gZqPp3KuCpIULiu3/LdMew9M2Lg==", "signatures": [{"sig": "MEQCIH/403gHlPNrPqQxxpIxXVyjf7TxupQrPwXYetKvZKfmAiBdHsfxhPg0fngiKvWpw7xC2C72ANHmuwifujxP749fhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.14": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-collapsible": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "53a08187414a6f55c86cbb67ddef0350565e5577", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-e9KKnsGw57UhEo9sLzXRzR6+5CjrRt4vfLbecRHy5fyEiAIJmaq5ssviBp4q8ELG4kJMxcPBvtAGwifJ4SycYA==", "signatures": [{"sig": "MEUCIBhz3ziNyvNsEVXNno7l7h80dH1qfvIWQbphHIQybtHcAiEA+bjgx77MVADDh4DpJ+FXzaLlheaSnRbl6Gkw5gxfhnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.15": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-collapsible": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6ae4f6a84defcf8c982f5dfdbc56d6d83437dc78", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-kJKcLNetKHt+hlI4821JNjfWiEp99RH32kDW8cKQtd7UVOmiwD1vu+Tn/pEvLZeDx/ONWwc0mcuibcOLLta6tQ==", "signatures": [{"sig": "MEQCICTKlw3TH8tuBOx/IRULVD11zfLaSFWAm/iTQBwRFf8CAiB9S0NAYuc09Ma+PyRoAgSgcN/CrdfdLVcMP4oDP29gwA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.16": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-collapsible": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "12c35423ed2d854b0cd8ae680ca251e5c970cce2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-lA6Bx3H2Yv+G9lCpK3jWOdZ/EaiQXaBZKWAY4FsNCrSFqXMFvm6TLWLQRulel7ixAno61toJzCdzninsPyUj9Q==", "signatures": [{"sig": "MEUCIGUkdm7tfyrFrc8cB9t0dFEV8RHgforzZm1lKjIcJ+ycAiEA8H3d3YbxHt0uz66+WCj6Mr/lBEbH3FJ8zB7w+wS4PEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59953}}, "0.1.1-rc.17": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-collapsible": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a47fdabddef678ef9709020474628e6fbe077418", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Vtg4TSMMj8JR3zt4TqMzoO4QDTAlIeQ0OvKFyGoLTIrY6TkJO5PiivLfRu/pbK0gtglYR2j5XY5nAKDslGz96Q==", "signatures": [{"sig": "MEUCIQDq8LwAwsqmZOUn2GfM3DBhjlHdpAsmaC3PmFC9EZho8QIgGNvhDfkLJf13N/ZCN0I0Gt12VDPMC5MCCmqz+H32LKM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65700}}, "0.1.1-rc.18": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-collapsible": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "35f518a1983104604a2a6c7911a5c9a8cf7b7a30", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-J9y6pee1ycTv58my41afNQrVIoHN/UEgkCUnEp8mLm5Kczn/UiTDzPmdCJXZYojQuSIADSWbfVS/Htd0MSIBDw==", "signatures": [{"sig": "MEYCIQCwCAAdjnyBiaav56+OUl+FQmM64e1VpfT+vtZPDOvJewIhAKYwSP3yHbLTOWGyyCatrTPJ2DEa4ZtmKav43XsYWbvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65700}}, "0.1.1-rc.19": {"name": "@radix-ui/react-accordion", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-collapsible": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61f32c70a169c7297783b77a12d92fa2a13cd3e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-uIeG/KRF/ygxkWwJwt5oy2FihDkYjF6VOsZ01jfwoHo+sS32JiSBoHFzYJ55hEKST/8YDRetoRDsv4tzkDWVdA==", "signatures": [{"sig": "MEYCIQDIBdY0TTUVVKpEYO9h0SSYRomIjd8EG4ttHfzqpLlJQwIhAP64dAmekjuMFuNyfDg4mWMi5Xr9XwJyxEqkorz7uEg6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65700}}, "0.1.1": {"name": "@radix-ui/react-accordion", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bc57120d37f9e08ddcefae1d9ac08e4db3029bc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-FGxV2QcCtQRBmcGle5TppSDcIzTgecLoXL7G5yM/YJVdcW+cw4LqPF2VnHcjIv2BGvvHi9087abp9jQxoJzUNA==", "signatures": [{"sig": "MEUCIEc5kAeUS2qM6qD1KagOwcnWYjEeIKYC/3Y3SbPcvsyvAiEAlpwF8rKIGdd3x0rhWbjN4BBiiNgJuw0CFctYQlqMqGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65644}}, "0.1.2-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "da604f6c66cb114361be9e493553c9540cb8f2df", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lvO9yXPASx7aiteTXMTUEZnHNjJZsYd1VFZw46xMMJR8qMjLFr95zgPo0+r00NjCRhoUFqLTq35LWx+5oRlKJA==", "signatures": [{"sig": "MEQCIHMd59F+vbvw0QecIDcIRIKQitL1/0oFlhxrt+yqdR2/AiBAsAmGzxlXZ9vw3kpZLuo2ZlqctXjAEdWSlHaEPz/AXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljGeCRA9TVsSAnZWagAAdh8P+gIA1TXfn2Fh6W9bZzd1\nvEVwI97h4jV7DjhQm6n2P3C6HuSEjTe6Sjx9mBR7Wg/CyFzGXbmAzkQKKl4s\nxDLPckG/auVHrbMfW1xlPkE1yhrDx6drzVLyBToX8LmDWtFHeHfJDWPmm7hO\ncTOpuPV774yTXW4QZa3pnN0TsmrXfoSsb6hi9ur00hrcPM2cuwcvcQWenint\nIXgfK5p8Upbst3Dbu8rmM0Uds1Dtl2GOixajIcovnsADNw+REufz2EV/syYZ\nksPF57BxBqRhdz+Pl+d70L9X6TanBaOXj7dbW/J+Eplnt0f6nKb49x47oXMD\nky+KBovbxPFT2jwr8brsD7SnTQz8ZUgaI7eyRsdfBE34FDz2kEeC4L70jFwA\nQswHDV+na4U8+cVg6ufJFOqC/7qOrHgp4Fq/vuuXXjm3fFF3m6UTRypeNDEY\n0irJ3YdZN0+TCao7lGlSC4kM3YyFrkyTFmSfIOuqdBiAqrLYbNxe1S4ePFw6\nz2aMf1uckU9ZI/S2VmMj0eBQnuo/jYd+zklugiYReKlrstN6cI342cxT5y8i\nWSi6id5BQufRwfSlPjToKpa1dGR602WzqZgy++WWjRxxlgXqk+6slsecocD7\n9GpHVsgrchyp0HChV+TUqJyhW/j6jDvEfv905eJlnQkiKLeTv60GsDY+lV27\nEcgG\r\n=TZxh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2e2c9a4203bac38edf9316aaf616a87d5cecfbb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-StHy9o9raR0M3Kem/KaUvOcMii8be4PjxmbMGPuhSAlLKs3l/ORdzhFA3A0/51aBt3sCkKhTURHnnIY314MFXg==", "signatures": [{"sig": "MEYCIQCLfU8HI+W0Xo2y62GDZqPQ8HdjKxnBCxtyi8g6x9fXBwIhAJGoCaj/ribVglcYdS5mwAz7+63+AYZJY4+Yod8Q/c0K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmbGCRA9TVsSAnZWagAAET4QAIFeDRVoQGOKd7ITMoj7\n/g+6qk+ASPrn00v/Am2+Im+WHhiq7jfjPfCFGhek5CYty125NYvBD08erXYQ\n86PgtQdFAIDYuslgZKYfCxotIwHk2SK+NHAI2+b3VCVoCSDSQk3zU60bS0b8\nKEH6FZacEaIiFBDJaSO5sPwvaZWFgX2Zr4jIsYgu4Gsks+P47x9hqN7/xkw1\nbtVQTtd6+GxtU3Ww83XOtKCt75Odnj+JCyqk6Qh7WK20O7ukWxK3nQgNyBJ9\nCYxsyIREUywbXXfSyoDSXjOLOpk4rHLHuMpvSCaYXYm/Lx4GaXE7fJAdhuu9\nCPS1Q46SCacxMMQEpS0ICELiTRReGSCF7oKQNbrkVNC9+mAADCWSqIxVGREA\nJ0ywXbk0AWV7VijIirCTl+KrC4snvjfNmIGmvuDHFApSCAU580YnkSIBH7s7\nd9brE3o0blgndOhhm3zBMImEhhsh5JRksVhh16+EA/VScL7o1kCqn1OEBiYK\ntLEggIti4qc7K7K0mlusQ03ZAcgQIWxjkJ6xeACv885JWkOUWd5OAE0Vpx3d\nwBOOnolgD9IwTM4VjPbTVvxnTBeFVRLKk/CJrEbL2zoMYO8go9c4dai4F+v3\n5q6k/o2Hf1hyVrUjrr9nAeOjK4gimfYvA8o90kczs4X2mogXRCxPqazmBIDp\nzmQ1\r\n=1GaW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "92a1b4de7baeadccec8210400ab88a7c7aa3fc95", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-9nR0HA3sh70st2uLcW2/c1Kff53NLglPyzLF3xgYBg6f+4zuhEmSnqYOiCU/xZdqIiIsLXNKYMrvaIye5YX5mA==", "signatures": [{"sig": "MEUCIQCrw+1jBa5u/vXKePinHRSHeytpmt0GN6ooQz+OaR13ZAIgAN0AL1+c4PTBN6tEyuLJW5Fm7dbz9kGhrKs+tbhQSSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5miCRA9TVsSAnZWagAA+ecQAJ3ajJrnMLiTJyRitPU7\nuPle5ttU821mBIWRekNCpbACOdKaYrQVnhnywMW/HqaaCA8JRUczdbFoxD78\n2jnfEgl5hMtNGj3YCa9ksQCrrEZRYfe9Fz7wGajNdsr09/FpHg6quDxkf4xZ\nkbAP5SxFS1VGkZh7y4nYM7Ikb0QwR6HKWgBn7phewsO7ex8qhImFJxmDOpZ1\nzjOLM3HSJtBC8d2XIlkC24227uf8KqUO7eKQ1qkVYgQ9U+0auDj7//aPeumw\nulnUD5P2FV4yDyQL2VYmCBsDDMB/mOfDABYC74jCcQcMUy6LxWxwjGmpQ/3V\nfJHm/NTOuyh9NSiSUL1c1b5BGpRe9muforRlztcvvDIdy9osQzbBLHhNty4x\nmzdKRzVZAx3DRddskatl/dL8IKlXJsyMJAfFvFzzXueCmAnzqcQjp8eEh2iF\nX+JB1nqe0Mz1bHdxLVJGJFtAISLJXI6qztInNNR7QUJ72gxUAjiiyuo/U02s\nfaFU8NmRPottBkBYZ4KKiBhfO3IWS0g+8tUEkDoInUHE3u+jPVvzC+0ZSVor\n7Yp3IclPFB7Xc/A8H4gsSIf1SZxuDjiX9IGimQvSrl6aXHLfEQEfiyxVzTiJ\nWhXNSfvOUd4+SMC61p2wruQU8iIq/g3p1+uVEO0CRzCp9QfNPWsIDR4HBa9z\n/ulp\r\n=GGDk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0cffaaa6eaa3a4aa2fce88bac874d2c084a678be", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-mlIv+ZX0OwWb6HiuDTqfXacgxoU4nHwC9EtcKkIQk5u80MBym3fntTLPPw7gaApAZr9v2MSke9pfSggYiWg/Qw==", "signatures": [{"sig": "MEYCIQD7h8q42TRVXOyLxf2t3yJSNVi+TJBKdyDZ0nVG6XJ5VwIhAPE5iSoPQGt0OznrQXpPTMIxl6jMulyLOqIVfFTdUTd6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN4LCRA9TVsSAnZWagAAuksP/juPSjLs77NCx25NCQSe\nkGP73YGK0h22IdsPwHxIVwZ5FlTOEwJa6wdgsUA7Bqkd+51ygGgYLheo38nc\ng3mX6sl96SpaRDRX71sikiwxHpoGdPVI0BwI+JjgyJ/Wf8QIW6v+5Qas5VBW\nOetRYkEdgaqw352XeE5VrfDmOCqG3OJKYrGLNA4YnjgBK8+/6NWQ0wVvrnHG\nWGoAO2PjtR0OQ2JXcc/EZ/FgE/GSZ15INu51m5588QRrpseksaatpCBSONt1\n0NqFW8CgNU6K2VhMsqlAR4ntZwD3GJiVKVUF33h7Ffq048HswLM0d8IoADQE\nbEBVUW3sD0zkD1/pf+p78GcgpZGk9wXlaK7NfEG4L3mTQfEkJC/1AjcvcwWa\nRyWeTa2JS82luRJX8Hm/Mbs9BzKCJ7Pzf63qilrb8zjgYHdNV9O1V/4uSMH7\nay5EBXq5SFwXmH4q8rnCX40YKw5Sk/XmzPMILtECl3elSeYigq2xgQXFl+Ok\nY7y+d3BBACgVLa/aLOpechc/XBzJoMfDwI6yQcffKtUJpFOb5O6zMjphm5l8\nnFBsvivGP7N2hfC1QFoLnmoy5aUa3+2XpwpKpoMB0qwPdSVYZi1h8Kdt46AV\nmZcA5wg42vNWv/UIXUKopRERgbrlT8rNap0IIh2MFtapZAT2aaLPPl7aR7s6\nXhyN\r\n=Dyie\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collapsible": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8d7c8f7aa43e7396325dc870f0804fc1b142cf9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-g29rGRAEq+YhPCXCfUbNadK3RePVpipUQRINMBBMVG2mwgbFhxE5BzwvycLlAftAJm3JF1nPSCk6VAoqG+blQA==", "signatures": [{"sig": "MEUCIQDjxjiOtfWfoK0+cuE1u2+aZoz1XSysmN2NLOsQ0MReFAIgII4EcPbb9MroZRK0Ue86/ndNTPxtUvg9R3WB4I7RsqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoS+CRA9TVsSAnZWagAANHsP/3fsetfw3Xdl7mHycOD1\n3+ynKASmKgsLUHVzxjNF4fPlNdXBLQ3Xp9bZ8XgrI75WSByBepZzXQQ5zjOw\neUvrhcqhhZoLdzSMBBCqKecdL5sRMwLliC697BS+YV8c+xaat5ER541jSLUZ\nbeWuMBnQlh2pYpuRIP9wWdhNqgBWBNhsvpDfUc65NlITVhWBMyfnOp5z77pq\nmQ0HPKM9hPqpi1lxa+A5zhUGhojTCUVTs/fohh7d8E31AaJICehO9pzXN/tA\n1zkNJydUEsJ/FzhN+guDi40hgeOvH/YeO5ob8CKbsimKjJxWGby7Wb8OK8xn\nvgADkgNBzPSiusUzllgLk7ObnxatPKMZ6R/F9rgVTCFGq50SVuFih8c6hpyV\nMn0+VFCCVxZare6rm0ph4H4DGmfe4vBS4uGCq28St11LQvk5yF1MLjo2YOu6\ngZep4UzSGQQZ78HYkzMDgoe8KTAZd+vKgjd5jhiVCx/8xAVDQ10kOmdBNhCd\nlLKI1J/s4ndrcXM/cQdVYrWjOQnTowvl3tP1L2efZmbdOBgP5fc6Qs7+/K+W\n6yrxgRJZckoQO8uymzwd9HSGKMfGFrZZMVwyZsR/fC3TenqqbIIgvzAaY21e\nVg1v5RwJosa/KInJAWc4PN/TSDZHRflRe+ugouo2qTSsDbH6h8/cZAuZWdL9\nXozm\r\n=v5Du\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-collapsible": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "20feea8d85b78c1e52ba82f2e4be24f300a9467e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-ss3VDtfHDrqXJKrsEpyP+Lt99hQ8hDy/PWvfGPHpSk8hbL1gJYErIOpbzE8mWYYIS0zIJESk2Xxce8wgj1cZag==", "signatures": [{"sig": "MEYCIQC0J268gz7aE0fqVXGI2rbOyOrTa33JE4fYOMvOgVrBFwIhANDMZHWmSUqBRuKqtovrgxpIQTnboYe1MsFSsIop07rV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/NCRA9TVsSAnZWagAArvcP/3o9lnxUOcqTFMa0/+ls\nEJd4bdm2XQzYsXPZL0D6kOP/To/oVFdYjL9hkE1wVBYVUasoN0SQtx+8m7f+\nZF2a92pv66dNSO5uDSS0OVgPgLmy5y4r76G3ZuVvW/4m2jdj36DTP6JNRg+w\ns3IlDtAzZE7PlRNSCKkJwBR5KaVbuROqGR1uNmXKS1WinHB3qBRRkSkhNtMU\nNCJ30yoC04tkj3M/RreyVyAhddYf0roKaJNrKvxPI18lhdBOpTj8SC12fgYy\nJEOcDhFVEEK+CWH6fQA1MAwD6auNGH3MYYdHxs/Kf57xkoJOzff5f4Wu9xIW\n4MVP7DYb3v0Rygu9MNvscrMW0z3G04HHHqBigpqpPQ60FTUlCDrcXT0xSdQ7\nPPtpZ9gyIn2EckXyrQcDaQnXNXbzvjrdTwXHTjKcnMrCp0YT7Ruv4/+o5wAu\nPZedkGsduEehMYbIMzc7/rqlp2OQv38iu0EFpxq4o09YJ/Pb9n5MLi3iJ14e\nCE3d93OjaKcTQ8BZON+MmiW8bC+y4IQT80p/e6oT7Yqr3H9NY1jzL4mfpq6u\nhRry2MNQ8mxiDX0fK34cQhC9A5cJznwwQ18EO4XUSxaJax2abW0w/qwiHTAD\nlli7/gi2Qii1BleD6J9tIxBGjSumOQsYr5rNgqHxAOci2o0GEmq3bj2lHwzI\npSOa\r\n=5/JU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-collapsible": "0.1.2-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fdc4c9803efa3b2864273883bd6f9fbe3bb07d1d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-XTz3Tm5/rKwDDGSLQIkrjtvZHldVXuyyyxsMqJzEgyFBHgN6j7JyqDLXapUQh2MVfpcMCIFAJz26UK33lvsR5w==", "signatures": [{"sig": "MEQCIGSk6cYMccH+IjUjaXtefKWVIZFJjy98D1iE6Mo2mSliAiAYnfnReiRjYJTcL87fbZMZqZ5XY7eWqz8DHQO2Z9sezg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiMqCRA9TVsSAnZWagAADVMP/jSf7ZKn11G+Z1DohESY\n30x4cijbqGZXJJdzBc9+GLsUFn7N521k1gnhnTYc6SApJWEwE9MKR9kdbWvr\nvRTTfxmNVWZcK+LvzGsiEF0AMMIyCoAa4mSx7yf8i124dl+eVBw95XV/s2wY\ny4smKBSuHAxSykj/OURKlW809uKICLyc7Sq9vLCoRldUPKPm+VXjYROLMi2S\nrTjnOv46NEwkR461eIOVUOs++sPftSIaY1nref+m75D7zY0Cx6O53dkON/0a\nnrBHmudXzdoxYHqpugs1BN48YtYfbWYfFbzmrUX0z9LWWp4jXl0yLqchtSOx\nYajbDBHm0jG4W84AMqvdjCEbbQ5YIpHKS/ILBDbPT4C+rB5irX/PwsZ40U1u\nSATWftPoMZYOX2vAl4CSL1sEYFxnFcRn63r8f7JCrosXQQicqPh0dJqypyjO\n7Wm64KbKVspTNi+SxceJHVv3jWCu8m0vMLfdFrajmHC1GwMJZYseONLIa10I\nUjuoOljMBoTjIvHpDfYt25S5XerGkOf5vtjlTTQPgXZd1BB/Bm+cw9ollhnk\nSzuCdSLepXhQC2QELNRykk5MntWe04N0GC48+l4JpfYJh+81k13kmBnE1HSt\nJkHLPfsJNpnQJKmmbrL4bohMqkzr5CmtrakHSPlPnCta+e3SraRwCT5FsK1K\n7Z2Q\r\n=Kl7B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-collapsible": "0.1.2-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70f00e5b1aa0d6974e53137b8ab54b3838a1c8e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-EFX0JhAjnlyUQoBWAH3F79WWf1o0FiaZUZeycXkSy1EPkFjY6Y40roCzNRPIotj+aiaEFWz4J7JwZyt8awnNDA==", "signatures": [{"sig": "MEYCIQDxauzH9/ZnJaPhdJzrapR10kf9VgyQUkxHrF/X3Mw0qwIhAL8l52C8kCyHpYc9yq/kIAsk1TKAiXHl/V5E/IyF6MZj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryh5CRA9TVsSAnZWagAAkbgQAI8/R4aPUJom7Bq1XYH4\ni4S3zjSIgG2oEsHguXGHXofhmH/uHpCTOQQimSQeLtoiDAH2+S/dyxergZ/V\n2nkEIxf3tF/bB3clu3N8FddqqzwUA10DVGe8GrT2JPodq2w4LoteZBdxznHg\n0gmRI0aNAuxS3Lu6xHEa3jTeUKENhOMoMMgMFpIkoCOwXZDv2CppHGgz/1B4\n3pZcWC2MCoBQGq18C4tBOGIt6p1FoHp6x0w2Gh8kj1Z9H8lh8uHT3kLsX/ZL\n2Z0RkaRCY6xESPpQS+HKDvwXZ5MDvOjSLGN3ln8FhxgrdZa38AIKVOvbAx+Y\nyAXVn1fKfAMVcHaltBKxSQib8e28xj0YHp1F/x3qikuBl+2+JyoB3g03curs\nZ9Ttj1wPuN0ltKFhIMa0b6cU7rA/ZHiV1pI1rC8Nlr1zEgmTK1Cbp1RS0llR\nuHBRFQcj7qQf7QJJqjXNxxjhaF0xbOBxP5Ihzzss0RXEwbgfIWRbK/CUQiBs\nvrbdeF66T6oGUoHKjD6evd2LGK5v1vaY1KCYGresYWU8BLn1R4h1kTqBRvPn\nysD6RRlmVeD6H3OoeaC5WSPOi3EMEFBZT0gEoGHD9EvQX0ZSr4KFLIPtkuFl\nukNNJ1RYbvDb9ZI8u2ej54/ztGRGuOR/9cMfYx0kVCPIlWe33zdgNCLopWRi\nPKLJ\r\n=YTnf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-collapsible": "0.1.2-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e937dc58dcefb5d740829c979061b8e884415dd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-dcOL1LsCZ0u+XQoja40/oGoDkSFxAWBOIiEcXZEGKRoh4596UqL/MMAuWCvqwDEO37k3RIAkxE1QpVcnQFkbbA==", "signatures": [{"sig": "MEUCIHnRd1CoyeESYlJ2lV3OamJD34WCB5bUQhtYAZOC1/FxAiEA6zVZrnt0Cx+8mT962idyZEeTAtNFN9We/EcZDbyYNXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzP2CRA9TVsSAnZWagAAk38P/itLSwf+maiANiEC4461\ncDBN66YESG9OwserBVlaP2onCzVenNcyLPLAg7p17ADyvEvSZYc2gHo9yxSt\nOmMJENLLZ5w4JTfVgaxP6JJvnVVmEsxd9UKWr0oZle/ndBeUSBHKVVUZAu8x\nlKDYAnIazDPzoxpyHnE1P192kyXiheA8LTtZjeYcV7jEVjddlSwF48mSf8JT\niBEOg+MBs7L4v8p7tRqCPfR/bKSs4tAiN1G12l7HMHefj6+3vjAZAxKI2GuT\nQkfkocJfU/fOFOUcaMi5MECVcvFFoAl0zswY65DBJZjchKMxMamerBFLpB96\nzldNB/diOkZQ+VFxtBuyjsus8DZVG4qu+pmjRUOvXm9vOsvbTObMomWvDpew\nlSgpnux6tle70TcPOtg2Cou/eQ4KGquMoxLMN99vpQRbamJRpViQ2461mzs9\nwdoCTnDUfknJMslgb9ra0q3abnlBY4ZFhlzrhA7y/Bs/RRwF0dsdJpACM3Q4\nzKT615WrytW3YaqYiQ3M1ozK08YMCM9Y6UrvIg0HrD/yEVgjSjc8hKbhnZLJ\nrxY9OBkTxq+iPI4Fur2B2oZEATNKPpzUorwWaSvPsYBzTSfWTh6rR8GJoR5S\n7bbM1WybZyUDw1vqpKJkHoJxnEuvk5s53n49bQ9G/m+x08J415/Z5dQrHduc\nRa0A\r\n=zEPo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-accordion", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-collapsible": "0.1.2-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "127a7ab84e58549f561ea616973cda95ac64df12", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-flVlVZdyAhlUhHsfQnJh0EyDCivpfu9h/NAwSDXsFcM1gVcrIzWi0b0ilSfYEcymy3SWXLlv1wek3m6pj2GHQw==", "signatures": [{"sig": "MEQCIDRfJpCpgE0jkYCmHRauIhiW+gwBEBc14yjPvVaRbf1NAiBiZibtFN2ykQeX3vwGkP17ejm23hiW8gAPC3SfZjA2gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41TCRA9TVsSAnZWagAAmXcP/jVnbFqQ4+3NT04yVoXI\npg87vlEQOXdbugyWH1eZhYN51hsvmOKT61rwNDSmSDOOvfUQYHw78JYexbpm\nJhWsE6vTVi3ZzxrgCrf8p0YJKNHVw0gyoxEw1VgciBk645cNmINgGvZMl/Pf\nJZzhQn8qWp4uvvVc6H+I6aIC7c3rma6mpbfmyIvhFsE6yn7oUs9AMnAAFpNm\nIDqEOwK9cSoHTwpKhzToi8E1PzemjbmwHyyAkL16Olk00daHCDErPFJZORUv\n2C+9XoZosoLMXh4wmlL4pMNyNdJJhTGnqJg5l3zbegVCVmL0LYHYKGTnYQip\nXgq02St9NER8/aM5emq3wpQRY3ZVsu++IS5bVwakqH3v5vKf2BDh6o0pdZSp\nMyLnsLJfmNOGWbbsVyEpAuFZS9nm51MONykLeZ+8ao4ZeWAyEIWbJ/7BuqY9\njfKV4DO8PeOrUMzCPeiHHmXzeVXRwp1nCLHquwmkqMeml3Hr3cCTBwr6HEtb\nFpx91p0nq7pN6jY0FdODNchBgVZ0g3WXCGaRNxOsJ/jmfl6NQxZ3Wd2VDoGQ\nBt/vf9k1MWqcnoyxR/FG7nmDu79a7ZLlNo6NHov/esNDcTN13mzb1iU/4Oy0\nYKda2EQ5HhAb04whMVNmx7aWC/Tg1HqfUfh8Vhmud8SWyDy446b2pCkoEMcC\nk56v\r\n=abrI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-accordion", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collapsible": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3e3e9136f5548b16d295979b997b610f254fdff6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-CcLKxOWtpQc0WtTy5qFAOTPmVV+vpP8eQymjJAP/3F8JSqEe6Eq/4e6gqoGCWJpfwPpYpMeYMmYgdxAfsqv5xg==", "signatures": [{"sig": "MEYCIQCdVsJG8QaG6o+foFzIhylbpTOsRC1wfUW3FCZD2N5pWgIhANM3RsTDXXET36dvGJBYxk9FFfdsj0NNLNxv15kl1Ho6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDhCRA9TVsSAnZWagAAhRgP/1GxXbcCT/Y4r9+lfzwu\nAuqQFcOUf0+OtCdQ9mNob7yogm4CSC0uMiqC970bppcsG1Fioa0dmx5GgWrY\n3pAsjLfqkYrGlAmNmGDTeKidUbspWUlxIVK6hdzgf3mohqgp6YT1YNzq0rwZ\nK3CMdD19x8SWAxNoM2UgQ7B1tJgqNFWrmuIWKLxEomvue5LlOqRCUhrRKyQp\n3JsKdKS5V5pUreV2uNIQ7XcF7N/OXtXwrSXi9hs53hpXpByb3upD7W8RlCMS\nSi/o6I8UW8c0KvQ5gj31T7hz+pHWbMadxCXwsymnfxLATO0bj63WNe12p0na\ntt/kih+gpm/4VtSmVxFlkyPV2dq97lFwzDWhxlNtnOGwhmKvUTwQHdb8hh6q\n4xCj01iGQHntm3Sf2976ss6ywvzjPGewuaGRiKAq2rnhxua123+EFk1WImn4\nP64VHTaSf8twwVqqcVHej1UVfacl1MMuUXldFGUaW5wAp9UtYNSMaDjkBNKY\nUa8ZCpldqe+fUDNW0XlC6Mfu+UO6Xe9dv1hUWWLrl4brLWvLLnkw66OzFZRG\niCFRINzJ5SAMbAcC7BcWODsl/pjj8XBMuna16qKiaqwkxl54ypgVtgVrH3wB\n7QXMIiptoytK//E3KizyTLOlh1u0oWEZ4G+zelER3UGdAMyjBGpgJ2V2KwgT\nC1fK\r\n=0gpr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collapsible": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ed6b20e0e4d2e69f15a5f96e57d95d3a7e2770fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-IjgnRxVbo9+3zK+nEn4dD1I3qIhkOFxuNPe/zgDKKHOlFOal6d5CE3lJDPYBYLdF78uWaXD8mUklEc/sR89uWw==", "signatures": [{"sig": "MEUCIQCjmWe01Fwwvns2N1euwyKhEBbTdozaaOfqel3jHeWTGgIgNArzpY9UvPbBUdtRBgA7I5x/rDH6pcs9nUY9izM/e7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsy/sCRA9TVsSAnZWagAA6OcP+wVIEq4avcojtTpsfSkN\n4byvi/nBff5aumb9IuDLVvrhkjqVBkgxgzYiLoqAYuIE2YzzMTgTAsaJ7K97\nDgcDicWSwgRHAis8LKtGG1YhGTb6owqgTqBuaZ4mpK5oxmrf/AbC/Rvd7xj/\nV/kWIBAtWY/Qp9g5g7zzQSvEng0pZN1Bm6XySkNLm7g2FvSAczBOiJZdvGI5\n6RMouJn8wC+qBGblHQYQxH1xe9jHsha949XSRF3JA9reiOatDRFrCAwBUmEM\nMvUCX2RXpZGwOOKGN3aH7vDgySG/CXkxG9n89979L5GmEjtXbo9MoHfyxOA7\nnGjOd/+cg24RME3FloTyAhLAQVzItXfSPkTgN7Zx7lVdMX2Jh0/e3aiv/JlU\nRHVaIrOz0q4OiBECAueO9py7g9kU9pU2gjwQnzm0OcaNIn+ed9e6EmM+3Bpk\nC4YL8lQlyKyQ3fWuN0/mJSjB7W2blhdEXEeEIXOGPosO3fAYlNxdC77hnG11\nZDw0Zf1aMvQKimi1Xlq240MWk6JwSgMyY05H0/+COt1bz0d91hvwT6V/AFpj\nu0APjBo85Ki774+9s0+MoTwOt5L8D0ILmqqcrdl/RL7u6CuYvY6rEhFRnRwk\nmOizu6zZuarDZI5JySwM9iorDPfoe0JMLQ/UoeixmoZEBWiKxfnBA/bkcRtu\neHzI\r\n=YC2S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-accordion", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collapsible": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1333b47cf0d1bd21c0b75f0359def06669439602", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-cI6gGrEnRZri58N483B/jKbrWkCenlm/p/+fixVOptNjOtGrwIc0d/q5RnxCaw8S3uBGLB6vGUTOG1hHCXJMeQ==", "signatures": [{"sig": "MEUCIBQ0KJtWTefCNjHjHoo6sRz2nyAJpqzL4q/If7riDZs/AiEAiQ5btPILjSjF3S01Ke6ySW1ahLldHwLCLQfx23IVPmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszrtCRA9TVsSAnZWagAA5SsP/3T6zXhojGHztu3Cjc3x\nsIdLaWChJbWzoXso408YVSuRcQTkO3DVACkMYQmNH0RKhVf7Tzj2sbm9QtNm\nGsHmkJb8esOufI6NCTGITs327iswlMd3IT4BL3v7Gw7PELSx5ISZg8p/1sTl\nkA+g3NJrAauUJWdoa61HVBk5crot5K8BQWOQ9n6ucFuTgWNXMQ0vpzTulTqc\nCGcGBQ2eBCf6VbNlvO29eMFfdc78glLt4AG0Oeu4kE4pUWLtkT827NrizwL5\n8iTrnNxzHCHggH+PdeO8HJCIngbYWAHrp7DJz+pK6E0JhSyUOnqknxgnfyEA\npxammNA/QrzXiWJHQyBJ4lPp4Eh+35OPNMOwr6rfb/ATCce2R0mryHLUN+Pm\nDQdeM4Zy1m0FFHtLWRIHnW3YgNhG6FQSODVCA9MYBCzBPjI4L7p0jslxhX4J\nSggDyb/hwJzjd+a1epTcZ3yVFPXu5Vugw6MlI3SxbAijUNTDbUomLnnmXTpF\nVuLRtwgeigI4O62Kztjj66muUqj6eZVcs41t8gvOqrvA9tHjXG4Y7goqLcjq\nccEPO5QLUv8jcqN9z3zOTJA6WxAES3i2Iy2tbHf25vNI2q/HuhZeabUee1pn\nnnpmrpLWoc0WMOHJpNhZqoLkwiACy5OPTJOc/r/39GIP3IRlpzHnLaRPggY2\n+EUQ\r\n=aOaF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-accordion", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collapsible": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7c038307c0530cdba4aa4cbe4fba3100ad59e09b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-JENbUR+xVTMbmuKqHpji9R26z5k5BTMrQ044eDs+gXw4zTwGvY68D+6+090tDnqlUuMGdJsQMIJ8rPLmor7Qrg==", "signatures": [{"sig": "MEUCIHn/1wmVwVR68DEtjRkk/HlMc609pKkPbX1NWxTk42feAiEA8UpEvdcaRrjftAep1Up4Uizai9TxAmJI9FZM6+Epm9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs3zlCRA9TVsSAnZWagAAbEkP/3bfBOblG8hApxNKo3ka\nhGwFCzvK0Cmp7xueW1rH/1P72ILicTqeVuEuWh7rI0Q8Br5MCu6vY5VIj2Pg\nxvCs3LK8dS+zg0GWs5nFIgLCRrKEjrtDdxOMBz3J4ked8r+jahV006S3kx14\n7xUSVATlW4HdN58hct/aHcouVzrPW+FPGQ+XdCn1A4AETgCAnXDt7N52POKt\n9xcKBLLKkOICNguq+hSWX8ArVHk3hnz5Pu1t15/SQrVk+Igprje2gvTn0yyZ\nswGDQYUlMcOki4A5fEK4CfuwXYRFOQ1Vjwti9FuvJ4897hG0YdR6up8msQgM\nVKUz/Q4+NEATk0RnhzbwkF0IiBP4YnewSk2tPp9IacBUbDNXUG9R3Wp4gIdW\nTbHeC5zzWumaOu0W8vCKUVGcI0P2+cfOhuB6fvH2kFWH2SjiwZfQxzJduvJK\nv1wyc/IRmil9lZuH3K0JIdcGMBRelYpX6SoWc3GJq7+8EKAqSI/MCPJ/nASk\n9IshmWheQXelIDE+td3pejvZTKDbkRwzJwz99oJwxQIqQTtYMNILCNv/n6te\npg2hRcSYmszIABNoOdQCVbHg21veo46/idEkbCa+/Uzafp2Z8qb1JtRAkmRs\nsHHOsekJwvzo/ETadvvnVMkR/DBN1H2WOh1RMF5G4Hq0KVf6BPBixUKuu2mW\nHNkd\r\n=VK49\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collapsible": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2e6baa00ea14b8d72865b717d4b5468a3d0cae84", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-i/mzF9y/38M/SOWGfeRVv3zd2wtAilSmjphjMJlCpirr/w5dRTWHT0K7Tw9N17QTH/yDRhAvm9vT5fUCPE+AlA==", "signatures": [{"sig": "MEYCIQCjMzt9Obg2k3Wh9wwYvHdt+VeO9uEQqV7u0o8z6V0MJAIhAIH405QoEcHCc3E39FVf5br56qEV+0vfySK2TYRCeDKt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs30PCRA9TVsSAnZWagAA0w8P/35GZ699SQVVpvHtsxL+\nAnbn2MflIzuxdXm7M7z8HwVUwtOPBT3POCxnK2tKJLgdESg1de9/gM2RZxKo\nH9L9Qwnl2Wyh/Tl1JeuJI+Py0rndm9uuhNFnd27afIl9vpqStSQxW17aj8xx\n4HjJQCksVhanQ3FeLF8D71gn7fB/2GNXob4baFwv8m+nVRaxiRIyXy0PPEp1\nOFzsMnpkHECLaR7TtwzrM4yOzIkC5B0AgJGMnXHCDlThD2OaosRMRhvTO0NB\nxu2GoDnwdkCEP69nUbjxCHRc4GGU9yNYhvEjtKw7RXpvZ7+1b1sBjBCoTFao\noxXn4YQtfefnb7HDrXCFyxohf3Hxz7ZG01TjHd6koqfjb/ZfkD1mtdcXxAw4\n5BJ1FUFTYtfRqHhfLn2/CRzpRAlKRz00bmUZA8AHr/wzqDoQs7WRCD4mVbCV\nAwPTli3jHdjLo5qKHpha8k0V07PkhvwTnVymheeij6XZp5w+NTIH+3jPxF0I\ng2BGoE2jX4SHvHs7mnbnpAc+KK+/wtZSYxNGfElDhesWFASxhzWYZrTiLuU2\nG8/PyO9yTkmkS983KXMQUyHyeIMhN8sJZPxSN9hJYbWCcUeLWAo4HP6yqDcT\nwO1UD6g0dHFWaAjgsDYzm+j1UbjrQBxDB2MBvwVMjlmxflqDUWfVXlPheEKN\nKX0z\r\n=usFC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collection": "0.1.2", "@radix-ui/react-collapsible": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b34859a1b2356bc94c7b441504b89ad5a1067c9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lRKXjQkMhL91VXWTko1M6DFKFz1M9R0nOxk0Zh0wmQ5SbFYm6GGTl9vD7IkXZUSkKH5Dh+fHWk8NE/D3NWfM/A==", "signatures": [{"sig": "MEQCIHERwYiesSUhDx0/VvggBm5lvrDsH/kSWSTPcVTpIQ6jAiBk5V9e2lZesHoAEZaiZnN1+YvjdKT3TvjV9seFcr1z5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuGcQCRA9TVsSAnZWagAA+AUP/3Qa81TzSM87Y71XG532\n5qHfwUYdSubNiN7yxCOwfjr+aKdR8cZDTS1VqUc2ZK4bv9o5yZSJRr3q/Zxq\nqdKpkF33/kMoPuLvEonnDldpRMuP+5qkc3ddJQ39fqlR4YIsgYNRLvAUdB0v\nMgjgPbm7WXpc8cnO8P1bqQa0EpelisN9PKgcCrE4UcS+EXD97AlgoL0PPWCo\nvvljtMJxyKi3QG9HXawveSZinqSuxHb79wi4jdu5cLMdtKbv2/A3dkzYBaiP\nOzFaUkrULYRnff/VNj8rQPJvPlxbtC8YOq9NzIcskBnQtnp2yyf/ZI7Br9HQ\nRXCxYGHyhkt03giJE4B8z1zmZg4yq0v7VERydebF2tanwMUspZl5kB3Oz2Bg\neU6nqDCaSArrOR+ZimDhJxILwCR2hRvGqHvjRRwa66BlvprykU0TvEyk00RM\n1qc3ZKJTyAwX+0I9FYDHyytb/6Ae+nqxHbKlYpHlV9Gk9ge67NykV/ookIPm\nAAG3AAr5fzNaUwrNQ6jHAQxh/LjQgiiGOsELRjYH8w2J5vYhb1N5Ko3M9F6N\n3LWaKFfnSW26p8cg16SikjaDSKpfpzUHQ+RxSadMy4vWAKU17yqUzYmvC6/e\ngVXt8FgEfFXYBpk5wnMlE13RQ9KB5clG9UgVURsAxqLBTwTZ2mL9hQTaBP/S\nPDRl\r\n=YOR/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-collection": "0.1.3-rc.1", "@radix-ui/react-collapsible": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eac139927bdec503132909366ecaf12ca8a2dc63", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-s/ZEMxWjqx7M0zZ8P1MBJN2jGkjw/2nDpW1ngVTUi0fO/Wc7t3E+RaZaPsVqLOuyDPeOQEiriAoABuijunJuPQ==", "signatures": [{"sig": "MEUCIB9ANJIaFyaVD2GiBx1pO6hmUdNTZ4epJJPnrecrgsBbAiEA1kMHOI7orV39I6VCW+jnFp+VbeqxQ4GYybiXDA77aMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiJCRA9TVsSAnZWagAA6mcP/0GxNkGFgYbnuWcuNWoT\nue1Yc2QhYx5kJDCARMEBsM5uUixKlsjQw9GUpfsHMH6HA6/AWqtAV4IEGmWe\nzYmGfQXQVCZ4o8jbulAzFWGSOa4cE33dIZJ7BVZTptf8Ls8zcsMe16qoEGCF\nS9ngGT81ggFf/kOmeo09F9WXYY3ElHEGHL3jdbHWJ88lC5SYI/ggND5aYsnY\nrSG8Q4iVwmyWD8ldsJvPVxjv1T8ScQNUAJXD396fVT/S2f0dcWR+mvBLuBod\nIw8sK5Xbc/A8NU+xVzU3FT7Hnzj07U+I0ZoEHlnJdw4+g7T0wKElwJbUWvze\nqX0dBfLBgUfe6hwTc07TWiVi+ZrPYgbT/yV0PRQHAhl3zu2n8x4Db0Ei8/v1\nVDMuGnLx9o8ywv5B5acq6Cd7mWF1KU2GVR/S1ch23QFNYqPVnzkhtjKmrqtt\nEiDLue0r3gGItzOhDNn+tEKLn1OwGhHysHqVKDkjlDrzmrPohuwmtcF+LmoY\nP4rBgEmULh+tMGeHuCJQS0FiTG9IO48t1JRpvWF77S2spkoS5TsYQJJqqPKq\nZEFuQcZo3A+37zXafnTTSC2CKfF8REXny4mRcGd1zsm0D9qFgd1f34dOaOZx\npDN/gPDAHjJH1+lWATMSAGQd3VgAU4EOpyhPKLH+ie3kqJrjENFKsgmqUAYW\nN4ca\r\n=VgLO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-accordion", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-collection": "0.1.3", "@radix-ui/react-collapsible": "0.1.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "96ebabc6f852588e8b61e5e53ea746f99935887d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-NlI12e1R56/WwOjj255R4EjdSmtmpLvTFq1vpuF63r9uB7C3uzDpBBnupi6V1uGVE3zA8b0sy1US0OaqWpdyEA==", "signatures": [{"sig": "MEYCIQDNUOPIKyKIF6e/1OpQxlP4DkWG2NCZ6q7QZHdzwVVakQIhAPpg49v1ptAUH4XtWYuuM1AJ6oxRR/QU7ta0MHBlGMke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiXCRA9TVsSAnZWagAAgxEQAIuyHkX6xGBvju9HfElh\nObIpFxMqWj/HjcKLtYrmCnCNiM5d9j+8yBUzTjigHqSRQtTFgoVceWfeDKVi\nVNTjPbUhgOo7OdCC5utYcUkrd/qzPW/Ndi0UMFjyXRZog/ABrMJ2kflpKHL6\n22CEtI+zdXckZd2baarwUkw8wz8BMIfCK9NMe+SK9t1wwB5mL3z89puT5GW3\nCXsbpM1emtqcwMvnYgPDtsKGfF02+HwUDAKcgVKm9OuicJ8UQqFcmE9I49sN\nFKBI1Y5LrOkse/7S7K5W64tTmsHqO0H82yJlfrFB+ozqh6N4aq/1oJgRaRH5\nvL03XaxMOC0tlWXIapnhdjPIv3wiFebb+YN1t5N7osm2hK5hA524MXJOFhf/\nxfdrEJ1wiCm2JAp4QARb50TrHSDoUOUIoRFHWYPYAIXs+kLC5kBZKD4HfQvk\nTDdiFaay+s48/JiFGdu/GkpYZ0xLqjxKdu4T/viGlIx2+OXErX3Td679goaX\nrIdaP2dm/YmIFWDN/HLSYvKFSxsJeHzXjHygcvHCDMjVbKY/3JvRPMlckDgV\nYB5rYxPB/sFL3TpBVoU46z9zrJYWDi3GSEmbA7H5ly16pp+vmdnJVDlEbVhn\nYdrwcqnpr7JSN2cBbsGq82GSXU2v6YsbsiqVOWJlsa/wFOw7V5tXB9Ptdss3\nyDve\r\n=1UV9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-collection": "0.1.4-rc.1", "@radix-ui/react-collapsible": "0.1.6-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1851e196043a74cf7bf8186d03ecc3124abf5d78", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-O44vQH07EBs6nasYoXBUhBRf6o1ZHPm738+Bs0Wd7GdVPka/Q42lUBvEcF6Yzf/rVKgREWOFB5GivMltzTpAwA==", "signatures": [{"sig": "MEQCICiwUI80MeTPehPC3ficXacXhkL7AEdxXoULbDr/yr47AiA47RVK6nftAJIchNM4binduekE72AC92TPiss1Urmq7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31pyCRA9TVsSAnZWagAAw2sP/3bPMAnj79kjvkfYxRFC\nN4AFbPdTkaSg2jPsESXxvGZ/5WV0P+sPX4G8TcA3N0x05bV3eGwmAm2Ac9NI\nJdowUBoFR7EdhUU4OiOhISYozkTKw5kkwfnfWv2lzUUSs15Wv82SZe4wmFMa\n1i3HGv46nn0ii5Vve3HoWH2fTmGsMRLt39aR4j77S6wBscBKu9rgDDomnqJw\ng+2e+LhovxCSSIv94Q1ryV9mR+/OP5Wa28hYS017/GxBwFwsKnJAr4rulG6A\nnhTontltZzMUEKffziL+/rFoJntfNcvCv4V4D/0bMowP5EZdioPs/9ElKoEh\nhLtBnvsiTNSCoBsi5s77dpU2j4lD0Z0eThz/VOp8mXGpPHQR2dgqA36dMg+E\nctB2yzkY9IL3XJWaxozReA19DBEFDp9Ag2syMfvTOjWjunBnAUOc0QqX3cXN\nPwUKw/f/R4GTVa+X2M6YaC+e3FzRkWvkGjLnvwK0GjHZc10wZYs49LoF1Th0\nKmHsJ2//ATQxKzLVvNy1oJrrz3SklS42US4u8Q3dTwkHrFS7stPj9NqQSo/F\nzMPoCRD8vjjVgYQoLjNdEmOb03mo/xBzVcVM9Vjq/RWhJgbpiUWW64GiW2di\nrFWNrc1b+UxK9xF3aesgB6WXRHpkpvf9hyIGuSUPm5y/WnMgNxdhTcyz6M5N\n/AW4\r\n=3K2N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-collection": "0.1.4-rc.2", "@radix-ui/react-collapsible": "0.1.6-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "83323fe5c4c17bbe8ac0684db7b48b7948dd796b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-xT6OVlo79dSsaH5W2vTbQ4OBv0m7boO4+SElZgVJR57o1hZVNNp/ZksE1FIuNAPH+zusBPwp+jBuqwazhmIqOw==", "signatures": [{"sig": "MEQCICJeZwfBi8Il+J0pzelucMgWUbTpOjNmQINA08xy0Ji/AiA99Kjde8Bg2CRNVHN9yppPI4ubliQptnA9isgzRRzZpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BCpCRA9TVsSAnZWagAAZSUQAJGebk6qiu5O2wTPVLti\nHGr0d1g2wEHvp2c5oxNxuCU8ebiMN/6NpGMFaBDDNJR2VFJp4A0q9SKQYhUs\nm7rtDSez5Y7vYxJtX0SgTUG8nOT2g5Ybe7oTL0Hw7qaSdTrb3EuH2ux2WKZO\nE88j8PuPj2ti3+ekwmiLvEsl7FMmEhgaoc4yiOZUgEE/2sonOzYJeMf/oBQS\nFVH1eFLm3M4nHaUutizq8O8XoPbBarNj0y9ybwPivd+KJJITHQxH8LWiwtFy\n6m8+sSSdFWYHIENJ33701i1Sfm80U/udZAT/zi6g76+0/2PQZQRay4ZZIEu8\nLC9+jG2iMkC7JGzNovTvJU8X3nW/r1wJe3mZT6zlZbw/XogQQtDfjnIaZYrP\nLwMGFoXOzJ7rAJm9G3QY5QzfTwzmemCo4349FZjwuk7vU+Ij1BIFm+hoYwdp\n8wx6f36u8cpW6qER5CSfWDXh6ulUxr38Ab0k9n4tZfClQcKPVE9yVWoZOLwm\nhJF7zUwzrpgXRIiUi/hNFcHh8Pnb5YosPdvuPkf6+ZY5cv3iFbnnWlTTS3y1\n6galjeGrrOk4NMoYc4/JfK4KLnPsa0EM5PbCjV1+97BZUtjmLiJQRFfg97kd\nOW1a4X16L7Rx3FWTU7g51fWcZUN7IaCSucvYe78xV5UuGMAjYK8T9QZ+WN1m\nZXjP\r\n=EmUr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-collection": "0.1.4-rc.3", "@radix-ui/react-collapsible": "0.1.6-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6bf5e3c0f77be6fdad6b227ca2f2e02530bf6a67", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-fxgxik37DAMOzg709JCJqA5ZUWXe5zbrEcgaOa1kUqQ6S2A1NrjO0rAC+me3GyRZ7VRNgOBBXtBbX7HCA5KAHA==", "signatures": [{"sig": "MEYCIQDjRQ9dtpbvgr32lKhpb2vBeGpZPsHdrmegrKfIaWLMewIhAN8Bt+z0O/J9NRFYHxoZZEij+tSYLTn1aJW1nudyhhFt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4ClRCRA9TVsSAnZWagAAS74P/1X/B/rg5e3OQt1aYWLN\nKqiXnctf7kpyJhhQRQ47AFCqHL7mAqRcHcfpibTSqbKSQHAkLIqeOS+EvC/7\nut9C6JUwLvpMb7opYRzR6yKIrVYOfz/aROx6Pf1VKjDdqR2Cz+OYUAErq0YH\nIl5wuTKQysWW1aIQl/dS6liCZNj+4YYZCvA/J+bjVUaUa/OohYIr+36vyhS1\nfr0znXBnFZYiHt/rNF08dQ1+W1Us5jEAoUFhR1PXAbB9sc9vK5HJCVrHfds9\nm3ynRsjJ2nJDUyiffQHRgNUk5ASpl2Mk9adcHQLwevammrw8kr/XGz7LWUwd\n5FWxKG8jw8+ZTkk1/XsDue1druyvaJKgeY6FlwvXq4uwnfoKYbOl3u5TbltK\nrW8lbKsXf7F/mkbo7TqFRmkieUZzZN7Hi4vIdoXeF70eZbR2iZka76rEAcWb\nKCq3Da1NTVUf5BeSShcjre2wmA2bMkhVXmovsBtr3Vbl8JpNb2kZPOtBWu4J\nfmb5CH7nA1cXkNZuugpkfH3gmOntNZZ8CqrzR8CYgcMBt+NRY8u2H361ql3R\nakFvKcq7xybzsK22f8TFC5onfWUVA58f7lIGDoG58oTrpJOAfsRR4liS59hP\nmmU55Cv+lWWup+WKJB6Tcd2TEIqrjK7rjBj1ZEPpwf+f5zcF2kGC0+9noQIq\n6eBO\r\n=9//U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-collection": "0.1.4-rc.4", "@radix-ui/react-collapsible": "0.1.6-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2560095560265df65681776cab09f31e045e8806", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-epZ1DSTEKrtQV6UApZk3XMXRNF9SwumTVMzSwP5qLGih6uzSrbUIJ7fzIJmKYsUQDC1nqAuLLEEWD8LQPTTNmQ==", "signatures": [{"sig": "MEQCIEXodFgkZzR74opSi2+8+1jxlHU1SuH5oNOcNvd/31UwAiAHpDooxBCfhZ41cEepw2NGIE9YS7gJNWKjAS/H9RP26w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Go3CRA9TVsSAnZWagAA5DMP/RKOivwUAEDhN5IpkH08\nazruDchJqtF+heYzXOo+K8IDihpmhIcVvrDizkJQpW15Hi1pGEjtrysZF099\n2C8OiSldkkKur5h+F2HkcK8NyfOLyniwvNiRV1c10VHGrCJkKTLUg/iEFUNI\nOrUzAbPsaXw2OXvCdwg313UQ89H1As+HOOHRYMSZE02eP/jT0VqNvgyLdYL9\nuYJQ04ohEu5iV1c6iboSpeHS9gl32YIG3qw5ubUgFrNwEsc4H0yW2XlQZTjQ\nmu+f2PqBEnQeLbU5urwdf7U1xdjmdNPFCBd9Z7iEG9Xay5gLRojFXe7wduTP\nuVQ7ynr4D0tlzTMX8IDHdqOhK9fA50q1+NEnG/Ojt9DXGt2JUpGtrgnWEZgX\nbwFlXaD83wf4pBAXjnzDGSBKto+Go4kWaCnLWukFFET5Goj4anqVgNYIm1Iu\n51HLPw+5LPYxhs0+gUXFdg/jO2qjNz3feCLE5nafoqi8EEQDAHqx8EL/BCZ/\n08HkWIwTiBzTVHmi7JOcXmp5p1TfJyKlChkQp14NuNjCbTscUIAOP9IRMSR5\n87elzB5Q4EyAhq4b2h1hTbMR2zUivTIlObBDzgMYi6rG9INP9nPjFSfox3WN\nd26ZDXuvHEQ1o34Xd89/YfHa/VMsnylPMg+EGx++J174/+DhsVmr9QziprcQ\nSJ28\r\n=9NMo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-collection": "0.1.4-rc.5", "@radix-ui/react-collapsible": "0.1.6-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4fabd919df900f466febeee7adf7feddd2637be6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-PN0Bmz26Jp2vpYruxW/IRDroAvDZ8yEgb+1Xr40yPEFyqvF7PQ4XtV8ulIX6veXNwF83e5nOdWlGsdsFUzpqyQ==", "signatures": [{"sig": "MEYCIQDSQZ+CtCfgzO0fY+OAzuz1BcYxN4QC40LpyH0lvZViaAIhALrjdtoy62BpRuPmIDe3vk8moh13PzVKbNmXsgCD1ou2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbACRA9TVsSAnZWagAA/v4P+QGFZqwYiwSwSvkX2XJK\nTcjD8HmXgOD9cJ2BHR26S4Xn8sct4PJ63b8sGcAg8D7wbeJ+Hb3Qm5fkWM4p\nGsuoKvRofmjx/U3ASRnuKxbEoX4XKFPz2yPM0c42l46s309GI8JdNzft81xr\nn22wF60Z7n6Lp7dsD7BgaNVTMmwi8badOJ1Nfbagubdp2k6+A0s22bPMU8Zy\nzHg3B8it1FPuasskiXPHALi7RLca53WU7ReNrftwJSO2sdzvGaEQ8P6fkbZa\nuZwxvWgDl0yfaEVspDE05PtpsK2r0Dt+dcLPRVO4nZaR1b5qRumc68A6CDg0\ngQNuvpGEXtt2iDdZLZce/A77+FGtiN1kcFq3Czy6zJD87/F0sS7P2JfVAeO/\nLs8cWDpZXqWcIogU3svoEv2fLUFB7XJojcIkWP4bNzZBgBIaripJ1MDxN3xb\n6e7fHqQkfAVIO5Z+st+a9E3OZhqKbOJtlQOKe50RceaXZbyA+X3ua1j4ftI8\n+EGa4Q3HmKUZv7+TUQwY/jkN3iT83WfWiKm9HYt7QRmpN1lrp2Ycm14MItSP\nYXLPlT0z4x1TflbgrO6T6hV/x8R1woB+73AG926iLfeify658U8WTGbF35U+\nCEhoXU3VimfConXB5ZekwEZePt7CVsD4LSW/2O06KNgUGBI4bHeawNSRHEeZ\nj17g\r\n=q8aD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-collection": "0.1.4-rc.6", "@radix-ui/react-collapsible": "0.1.6-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c283d64f4b6cfc3584c37840c329e5f5a82e71a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-JoKjXk2GVVdKzMEuh0rpY1YuQ8iI8Tl+d5ntEnh3QuQDcgQcJFHpFn28l/+HWoW87c33F+VOscFIPDuujwVLpg==", "signatures": [{"sig": "MEYCIQCwi+3LOiwhWNs/sljsWe70il3RE7Uz+INLZNRqKpDprAIhALaQ6w4+qzXPtGkv6IClxdV4J+MdYvr1tQ4B9f0lQcLa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YrXCRA9TVsSAnZWagAA8UAP/0a48xDfLmuozhjerQb0\nwnOsSjI4BU5G078DgB7cgIw7Ql//l6Kn/LJsLNCVTk79lfK1dwWqAztapibB\nyAuMxLYJcBznBwhh2RHqjr+w2SyxExKfe0rPSIGWGPwcRLh9hGIWlkXrPc3T\nWYg/v/jpDPno4ca8MXDoCxa57QeFvENjzaleWr0RM6u74k6XBO8Bjzsl06HN\n3w3VPU+IgTS6YJPsM/H7kwWQbygXBOJ3PaJ6tabnPoeyyEVezTHExC7RfARx\nJEwQlK0QcwKaQVx3kY5UTm87eNjKxHNiFQnauuQaDdjE+4IYEhe1taBxZLoV\nL6a5JIeDeF//AjIbAivc925OQXLqdS2pPS80MQjarLpOA8hnUfOCW6VHQyri\nU4+BbH6D0v/nhIpRaP3d23hpFAv6ysLayt2XvlO5el9E8s4TKC53X4JLxq52\n0CcRISQL9MUjeUMnpGIc7iVDZbRoMH7yejWHmCFDfzC46Lv5O8PHknf4SJjJ\nHnPyVgfNKDwhPNbQLD5TsUBX1yekuq3yWycEUSdkujdDOhf8/55CVFoTMzkU\nc/E8zgd2uCvQc9xXr2Uqav7zaSTc+22HWVUwK4RT1xSpUHV2OaWTn4iDZ545\nfOXa+Z7A2uNNuNyO90rZTJK978MgKYLV/hhnN63zEqr4byzPvozWrgRehU7H\nFYl5\r\n=oXoy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-collection": "0.1.4-rc.7", "@radix-ui/react-collapsible": "0.1.6-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d750d92256ef0c0d3c05aacc2180fc3c99d90818", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FS7htbw3USAbPIZRPaHjsFg0LmlKH/mBPrOHQ03kleh6R+Ndkh3X9lsfqKNQM4DJygDT11wwNVFxIHaBaWvJDg==", "signatures": [{"sig": "MEQCIEbzxbv8g06kXfWDyfepQ6sy2BtZfLPHe4kx3q+Ukx9+AiBtdoC3BJ09xSKAscfuZ9uu9XT9+c4tTc40vL+PEzpKEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sb+CRA9TVsSAnZWagAAtaQQAKEfYuiYkvk3IMABHOWx\n7+E1MUTDwaBCLCSHVgUYRgtIyv5X6VBtQcdrfH+AGKuh+YIDjAencuZhe60c\n3WAVn/c5+NB47P8p4NasrPKiJv08gY6F3sEdXjhxnI0/4FiTLfSBlGNfl6dw\ntddg279xJYm8MXmYmTIt0o8Pg4H3h4d6MxnIwejqkH87UQsMNQBz0CwneofR\nMYg6yjRucO5HUkwbwjiIXSM/FGt+LvYTHVVI9X0dwPov9b4ovl2fbR+u46OW\n5S+Xfjr5UzLIdi7yvkfxfcM3423Mmd/N+85LJnwEejEBSDQ8OjO2kEKqANxr\nNEbRrSMOl0os0mGH9ye9YaSIHjw3GgIDTAyf8JaIp6G+YdKgrxL7bOR+jT9P\naf1X3NSOu8SGZrbo2tcc50vLw0H4iO2+q51icrN9NUZLgmgJ05UiVx2iC815\nxm8ay4Q1F8RrookW1m60jYBd2Es0XWev2MprRS4j0Vjqz7UDg7XcQBPKXvA1\niuYOAWxZDcVjXKrly6ZOR/zFLAiK9Tgd18Jutm4LDIiQ3d0te6dH+f0qJye1\nWkNMuaSg89uDktgGrJdD7CSxVp+5d4LLTYrIoYx94vmFssmWmVYQb9E+ylR/\n+0Gj4JJfBLth/ONZZB5CFY1Ty6kepM9tyUABep/SS0EOEQyeD7sbKecLI5hv\nZBLY\r\n=Id4/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-collection": "0.1.4-rc.8", "@radix-ui/react-collapsible": "0.1.6-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "daf1d0db4bb6927a83e498dd6998630da329bc87", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-J9ly7MBPSaT3C0A/jPFO2/6gc1cpu99NllD7jdm6P0qKwYPwh8+F2iOKnUhCANand9Nae4AyLwPIb5z+S4Yw7Q==", "signatures": [{"sig": "MEUCIQD1D1wIJx17cqzT+1d2z1YCP2iMQjyAJCgGBw+3U3bPBwIgBu8CJOJhBG/OTvLw8b8NhSDxNQ8jeJJ1LdkRo6RuSQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xB/CRA9TVsSAnZWagAAPFYP/2Z2aM4SBzDzQ3L657Eo\nCpMeo0ixQVEcWrSmksm8LLKM9/JMYDzypgJVZkzosGgJyvC+6cplUxs8UHgj\n9Lg7EX/DDvSYAOT5dm1P33QkjACeFYi7ed4wA9OgyYPa6wEigJ3B7uP1y2wS\nST5iHE3vuv7oTIzG3Xpkzl0cnPHJcPdUI9vQTb1wa6M/rpV8KXqwCY8pmqVd\ntBI1fMClx7j8q9xq1M+zVEkG7aIs1Yg+/XrBBhdBLExWFvD5bLlvJ83wOyEy\nVnAT0H64RqzyNWudCLqTUfZEyJl3t3eYV2+Vl4Lvi9I8DCAqcywlPlfqxb/F\niSjZt7eG7WSJXsosZPVtfLqNrLkvXyuwUmnuV0L8zLd51s9ZmlE5uoJpY+Jq\nzYaWg48lcKdJtE3QsFRf0gVlXcYL2dcZZ4w2dyLoFI3dj/9XW12uST3qMNZc\n3MzobVISM8XHxgRmY1bpYmk+VvfhqjBuMxMU6M9efugfsNuVQdgpPI2DLtjZ\nSumG4Jx68c1KyE6ox6J4+Yv3xKkcP86HYjC8pHh8i9GVc1SHWwLHlYJeRp/7\nLc4jCNZv6sk+mYzZC36PbbptDEHvvjb30ze+7fCBcRqqcfTW+RiTbh6AP5lL\nQ5sYxmpWdhZe0SEz0H3hy97wrhQ5lVmZQHCeCf9RO0oR9K/dQKWC6GK7aIYr\nKBmq\r\n=7N47\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-collection": "0.1.4-rc.9", "@radix-ui/react-collapsible": "0.1.6-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8aa575fe307b4661b40fb5d005c69731115be20c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Pl4pPD8BDsk7xyRn0kKlxqRNLwXuXt494i0y1I6FCpOifj1fBj5EE3YdSbAzfNftBAEdmZjv1U1U0GltvSLeVQ==", "signatures": [{"sig": "MEYCIQDOtbwZUid6u3zfosAKUgqn7UV/EIj3agTShA8F40gvMQIhAKPMLuPrtpLjyMN/DE6RjUd6pKIODkf5OXqdLxs4jl+e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68012, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xJiCRA9TVsSAnZWagAAUBYQAJlehdrPFHRk1UyMIKlg\niynRksFutCa66W29ewVlMSS+6a2PUfk5QCUwIaCuMnUjdFxSoff9FTZO4ob4\nleHVThWUpJmIfDTR8fdQ9TTBxF/xDamGrIfGzDiwXiVeevaeQzI3H9mbHYxr\nJHHzrT3ATpbAz/kAFbXeNfaTfknVnZp2lAJMzKCO/li4Nt5+r8i+cVOSGPTH\nkzs483qZ868RyMDb1bqS7jR4g6mR3qvTXpn8NuL7e0IUu5AtQWVg/cJp7vFu\nc1c92ec5gaaLOH2lmbhoszMGfFGC3wNLN2GvaIHS+BTRUq3i/SnELy3+dB9g\n5TGJZM8fgcr1yV411TNuAxRhu7confgAey7rNuYuJRTFRT44ba09gXplSwHV\nc7gUbv3YVaM25QGRuOeq3RqLIjPFBY+vbYgxgRDGukBTsviUhKw1a0Pb3aO4\n0feBKjjrnAFYKuTOEWAwLMG0e8MSwQGVejeFJtlr4NuGs6dXc+6+jNgO7bcm\nG6uecOvBHPFy995CRb1dIeKWPBAN//P65YWrlzYFX30F5hbkmChYKQOs9mtQ\nwKKRx+OSfLJ2VvErY+xuDC2+wOvE23cmCAftK5DjFxVCoekJ/W/Djb3nmGDB\nKWzgk73NuuU5ycgVjDJRsWWx91KyVhgT8aVDHhtgGQraorHxy1jMA6Ciag7V\nBfPH\r\n=6gNU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-collection": "0.1.4-rc.10", "@radix-ui/react-collapsible": "0.1.6-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c8fb19b4968097a85cc8d1fdc23d5d0701cb8d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-3uuSDnPELfAgKgVlk10M40xFT1CrPIF6Ta7UmcimZHjF9Mun+FrC0RfomjWr5ipIQFSm+VhsL4ho2XhqgnnYlw==", "signatures": [{"sig": "MEYCIQD2i335kakrI4OQNa6xVcr8RkzjGrMGbAGejNYPkf36OgIhALVPQG0A0m13fuG1rzB7SpCpADM76qFA2yNGh1wLWQO5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DybCRA9TVsSAnZWagAAMLUQAJwQJimTy7tZNpgaT9rn\n0NuKl+w7b/xB0JrB2dlSA3b/fH7r09/vSo9KTLMm7ZQ1cWBXuZVQavnyJ4d8\nRlBKkOyrKq0+LfvZKzYIcIdjlRbznznLBgAN9EbuWxSsGugEau3nqkSq4skG\nFgDfIeu8qtExP7te6b2xyCbmmWiRvxDjsaCuKLAD+XZ7xW6njx2BkFMTT9ZT\niEol9cXjs4AY+trB69G6Ck46zTw16hvA4iH0SIc8T7z5n0N9+UnuEgCm65Ah\n7/1UX2H3osJf2MOKmtILIXy2Hvc6xnaByrpv5PA6TxOgByJ5StCLjOlKPQJB\nTedXPU642/qa7cCQOTkl8aI6PJMpPMd69LqXQ+tJyFqTIviCLsodG8UPPdVN\nM1LmMuKK7W3SxkD7CoebM/JOYStplW8o/aXITWm4FBNP5WMkHpXAPXDF4SsJ\nT6Re8haZTKczW8i2rcJ8IIgux2VRLAyi2kC91zOcCtT+TzX3VWydcjQECVEp\nvWDA4m9aYeBtRLFFXD+lfSGKBZQGk2kKUByQyaNfch0R0EOOkyNMCCtD7B+V\nNdb03nW7UbXzGZ4cE+nJwleqT0BvjHsdXIMpLJEPq5ld+b5eNDOOpM/ljzPY\nQWo0Cd1GAJOzLQVgtvtwwDR5K+JD1nJevtw9QjMM114G+lYLFfiZ3UzZFT9d\nCEjt\r\n=gcjL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-collection": "0.1.4-rc.11", "@radix-ui/react-collapsible": "0.1.6-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7481be495c3da6fbb416efd5718f18354a51f45c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-94X/D90wgPncd/OigUGEjAz+UuZZZImmyPQwkfotNpFjS5HhlubT+5UzYL5RVuYFZaq6RrYviDxnC6H+se4sGw==", "signatures": [{"sig": "MEUCIFhKWepn3jcLPngS42dcspURQmFoT/zhJ2jc0vvS5IezAiEAowxxUMjHms5NZIKk9bN8TBHBz0cD0uQ24hWkUqU5LZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SRjCRA9TVsSAnZWagAARYMP/jOapwCYbQhlJIWR7GKB\nprKB1RSVuofQRoz2GlHdhPexE7PrIfX1Fdvbd1G64Ntgdh3EG6GtNgy2PtjV\nq/eN1tjllqubrZlnj4kN/DxgF+h/9GpAqHvJ1c1qTiwcfZWBAdxLtmdjvTNT\nLfDf6R2Z95OuGJ/TCnXAtdBlj+4Fw4AvZFUtl3am+vlimeUu6Q/QnXXIvEdK\nubG3e5OulI7JdzGuV0OpDd1dUm5Df16A9eUw/TIgfc3kQty4oYKl83l58SAZ\nG1o6cctQrAR3DVhSPz5D+qpSwIqzmftrW/3sgO2InX4MREBQtcsA+mco42n3\nAKA8shlnXANUkccywwFA54bdKtUPxVVpWQ7nk9JJ2MGRbJjY1NYTeimkENIG\nHXueQSXLQcA6OwH4j7gQVC1vPElEGS3BMnOfBAxYXcD+PEu0iZbIQFkYhcEo\nA6ranMkAp0/0oTLRtipfyB6dGJ9rrMgZMovczBEyp3QuNGb8TBHceFJz4WRg\nFKgFc26nv61/z55guMFJTuOWkqZlaNCTXNZ2cXNSAOZo+9/FLGmpsySJxFI8\nU7mH7cXIRydHRcwtYt/YtxKHlGzm/vAcmmalyhSBXIfWvAlsBzTDUINdTn1O\niv4snhmuzMYEVEI/tTG5VyVowasvAO9iCKG9Tr8pqRXsBnbMl6bew8J9NX6h\n/MJ/\r\n=a/RQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-collection": "0.1.4-rc.12", "@radix-ui/react-collapsible": "0.1.6-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8d0f66512ba3d2f9ae0a2c42abe4a57f66c6299c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-p4gecBseXEEJWwVXyIkLmh1qxI6pYSoa+UV+y4TujIVA33WREIxxrkEI4yMfKbORHaG4nMuMqhI7uYqOuO9bcA==", "signatures": [{"sig": "MEUCIFyqAu/2BoxFt+SEWIfecI9G2A+5EDmw7Jpr73sgoPsHAiEApBYLbH9acaUwCVm2ohtxMpJaRLV/LnuJyyKFndalfxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DYiCRA9TVsSAnZWagAAKBgP/39HPkzU5+vG9WYWAnHG\nsGTJZZneTvpUSz7SF3zHMjsjWlbqaIdcx2oL7adblEbxZIgvkOGmcB9IsJ1S\n4hpSpPHm8OPa+ZIbTmjUafdVq5q7C/RtmbNzSuUX0t+YupIJB4FuTLSa7lkG\nlhUfZI6bn4Rb8lX9or0dnbyERvG0ZrUSaXtDSXQwv8ci7DwipKssMhGQAtOg\nsimy+jtiTqE2RvsPAyO8u5oEySJm+03RmKo7l2rC7waLo0PvGzxE/OC04wHv\nTmuZpK/8HKGfrpGnJmHZUYHG3rUJIB4/Jzng/p+wT1HCTFKRbFK4Zg6/cN8z\nhu0MI36IArbwQF+t2vsgJYNgsDeoMoFKPVaH2BZIcZPmYIIU6x0SGZ7ncnTU\npX9kBeCsjwj1Jth4Y8tnFo70NfpIkxz0tdlHznBI0zJnrDa9J2c6FlS1z3Jp\nmb7v/4eJu/nbP+KaIRQxR2Z7gnQGA/eGWqV533NbSPIFay17l6Y9XO6nmQ//\nADcUX9oXelP+eu69Uvzar2PtEYiON7WsRfGfoaW6A3mVDKdct854WWCU6BoV\nIBabezEq33pkQCTOGNWmuUt37SlriW9UcWS2vtULucRGYTsv+2aZyHaao5ai\nRVrx8NHm7KtrWDKdZsOhM3tlHMesN2ICak9wOkpxWwDq04XxWgxXYNBIAJet\nYiD8\r\n=kRYS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-collection": "0.1.4-rc.13", "@radix-ui/react-collapsible": "0.1.6-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3d26b56438099c7962dae606faa97881eb77681a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-xdjNtmELSHLPrAVs8sZpoyQbo/BYNrv8LeRiCNgTUl+dlwBsVn6KR8u2XGOl+3xnHa9RyIAHbsgnsuYdYPVjgA==", "signatures": [{"sig": "MEUCIQCwJ2+b+W47U7KYHmmRG76OPEJXWB9bLhsE9CDzpJ5H+gIgVhF0dRnBOQIWt/d+xckLVuSHq3+ixI9aRgefNJqA6Oc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WmgCRA9TVsSAnZWagAAk6sP/3fk3vCLpBinZc59fyHP\nc7ErzjY16c2oO4Dw7bbpxIYPhq103OAI+9804sy7wH9XXG4Eyc8YTmufXrJX\ncNQW8NR1HH+BJjgCSUCm+mhOd301ZyFBh+sE2b40YBRDYU/48sfdeMfqRwJ4\nYyFDic6fLmbzriynRRnbzMhuFDa1rYyz1u62iow5zbW+7GOGDQHLYd7tYqtF\nwiqe5qXZuG4lcbtezD0CYVuaeuWL7SIuQn3nkG3RRnK5FXLarXYEghMjG/tZ\njlFOD8+4y4gcvT10QFwlnSv8bVK80frAsdoZH1bZjRpKdM1Dwc/VYLpCaEVd\ngzblEYoNea+rxQTytswHKtQ98svHflgWko7ivYaTLpBMuumLwraESsfKH0ln\nmupC8SlcaWXHicHo8C2No79U52UODhSZZrGHAcvdjUzvaBJvK41LLphAlVQj\nYilkB7CmEBl7jkxrmDMLeNSNwEKucb1Cu9aIW+1A/q1AGTD4UgiKYIGyIOVi\nONOIyUMejrzrgafLv26DL5LHEsp9KsyUMBKXKLaFEahaSNJoj0CMSP0umolw\nO/s1uV6GS7B8XOLZbEBnemJ1kPD3ocrcL5ClTRjtf0syayNmakPRQ1mabjLq\niC2dvvFBx559kXJualAvDYRm3nVOESTYLB9QBUIMjlX9NpXQ5bjx2ZOVIc6i\n0mV7\r\n=/MXG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-collection": "0.1.4-rc.14", "@radix-ui/react-collapsible": "0.1.6-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "71511c42662fc1e6623ac3058a3c6ccfa4a5b494", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-w6Lo9VdboU7p9kMXhShNU5Z/f/Y1SvHSuXWTLexiZCaqnhEFHRyslGriDOL7lBPneCkcfzduZu4/Flw5HjmeeA==", "signatures": [{"sig": "MEUCIQDZJ5Orb+kRRzIK9RSlH7PVlQ5zuTjUz9kUYMN/zvgnKQIgIsrI9AxxxYo5U+4z6VhpoTpgGB4In4tOw2qgR7cicqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rS5CRA9TVsSAnZWagAAAvMP/1ksfgZahgf984/JAx5I\nRVJJA670/vC43ZJ0Fyo71OzYdD+pxu8JsHl7WywCeHLO4+O62ecwDOIQToft\ntIxhTqg0ZVLNJv7SUPrf+9LoRmEij9I3xCBcgvvwCtaUkPfN83WUpwBqW6+0\nkc0wXRJXwMsbsxuhYPY6rt8gHMdo4qQVT6wts1X7L8pgGTfLp4+Ss78Qd6JD\nQvbRYB2v3Cz8xDBJt/nM8+kKCljJbKrTcD/lIff3MCyRKuDsEX923M+iJAS0\n9RZJK<PERSON>ieFMg0mbqmQP8shlLnIxoc69FQN1HSrm0vto9xAPkGQjb6zWOfdrA2\nveb+uXMMi/HvuMHEEPjZr1FIiWL1JzO5zBI8fo4GebxkvKu8KgOMRM+dRyHF\n/8k2DAWC1yJw0Rwrgt/4Gfcc28x+a93zNNCVJa7BhD0DeRRR11Naa1E9LCVn\nV2T+hXOGoqB4Al/Mu5EH20fe1yoVgp0E7P55mgFEp75fIq7YdE4Z3ibrg2Ik\nAYGaDV5L/Tuo4b9Sx64sdriSPDHTDsEQ1oh2s5o4zL8v+eMh8VXbPfx7/rwf\nvtSUh5rTpm/qr4Noyg0DvoN6VR5SjNoRYUXP4yKNc1nrH37SnP8zEDvzY7c+\n/zfc3ikb5WwNizprRAyWWGVV+eq/zqJSVvM7s8xAW0yrh5iKq5MpyLPUnWFL\n/KAU\r\n=lyQd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-collection": "0.1.4-rc.15", "@radix-ui/react-collapsible": "0.1.6-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8925e3f4a77f2d7ee740ff978ad0199bc2f287b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-i4ahbY+wkR6bQY2HsNMByPlwMeFZqySBn89mrwFplsUG1UHZ4dc2BdujeeK/KxBzYlTCKyEqyrf1dm4w6GlJpw==", "signatures": [{"sig": "MEYCIQCKSuORyILAsmMpX5L5Mw3+QqY2TMPW5Edr73HdtKrvUQIhAMGxoiYxlpPZ6hhxlRUXJzOGszIcCQJn0QVzeU/NMGL0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/l5CRA9TVsSAnZWagAAw00QAKIdze+8FrEiwh5TPcbh\nRy+XrCu/y+7upGD3tu6//dE246470D6Ia+cnjO8TXxpjh+ldBaOC5x16ZiKi\nPf7m9Gbnw2PwALgMEbEPU4b3KZqFfWY7G0DytP+1jBO35QEHQL8sS2in1/A4\n/y7CFr+WU4tjaDIZljsPRI7UN4EclcWQOXFzfMScq9rtiWkfNZXaG9cg8qo+\n9BPDubasLGGYpJTpZO/NAx1YFLm1rZUsNT3eO/2cEj6ouuVJTQNAt5FFrnXe\nk7hjbFQXBQJjCMjEUoA+laktsQAH8ksisBh3Mjk11Td5u2GuorKs4A24L6t4\n49RGGSV7B+JL+o48NF2y4pcQwkkkOodRZlh+qH3D63s9v+CspaPOL1m7bN7H\nZnyi8h575IoyTmJecVaiaO3MQEPHTpobn1ZpE+h6Z4lBePNG0F1YOPC4m7Pw\nwiJQsku+omyEWajDVJn5+Jh/8ub0IYkhPSWFhzre+9TpfsbYSOtlFj2jkAEW\nbryeBZphlKmkTLmRgZ4ODVFgJkmCCQ0y1jIbJHSWv24WLuhciWTHIxHH0eAf\nkObs3QNIvGT4/z33EZyn5Ai96WaPnc4EWlXEP67YeFUxoEZEzrvxxI+upwC6\nCeDRSvE3kvCNWHbUCIcLAa82FZHbtQb89pfecRriQp+/Y95fkZxjltGPdBfh\nPMn+\r\n=d+WW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-collection": "0.1.4-rc.16", "@radix-ui/react-collapsible": "0.1.6-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61c82c05ce5adb5dce0b1239888589105fa85676", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-qvcrUwAd+06P73HQD82RghdlQWi9YsfnLw2EZpYpvFlmcgZUCN//1KX5oqOrurvd6Gj8kE1RHm97jUVrQ5eZjw==", "signatures": [{"sig": "MEUCIQDjuctPah4+A7N16xSoykvY2ughoJ9uWdaemJHdUToQ4wIgM1RlOAzevY92q3EKQbzBwvOGZL3RIG011F0oGYqILeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBGfCRA9TVsSAnZWagAAM9MQAJW9LKuAbw6c2IHkD+ok\nc3JpgSrsvfxn8bo8ptqi4qvDiJkuJ7E3boGa6pw1h8gm2IkyNFWLtXLe6iJk\neluTZScVax/nH1jKl047w9OZj9X9MiRRSw+hf+2kZyuHQwrsWEC9qx+K7AbC\n6FWIr/TUJls6otKPcsJReSOeQyB7irpTSuWmFJWtHdDSs1bCsvC07rxGL+X4\nvEe7Ul7OJQUf1lPVCKwwdGtdqV1Ejqe+1peVR0HMDpFh4dVx26dLI26UVcTc\n+RLCaHv4KFgMrI8YyjIJjr4HMZZZ7iOiWZe7bbpESHGBIOqLY8cPRCLM08Oa\nFveH8QaB8xoB+/v4sjukJ4hG/d72DvnUqklgOAS78uuAqVmzPBHIbeenmoyN\nEsJVB2WKOCzeBQXHz2eKpiQiEJvkBiD2sZmF/a9tlba95wklGSZWqz5jlPQK\n0LXgnqYE6pSel/UhfguaBtzZUznIrvsngPd98DdzZbYukZi60GGwZnEmyS+g\n6O8oftirzXUDZnTPIEV7gI1pe1NmiDmeb4Ub5cc8n9u6gfrCz91DfydBXbwc\nKYRhPCqe+sKPZ82U4Nc0ePI9moqIJqrkyRki0DIrYnag1LBuofrBC2Uaa2rS\ndaOW0k/mdoAWueB4IetQM1BY2UGP5hXrsKf5OnBAL0S6n1OLGUaBmH6xsfMN\nirEI\r\n=SYJC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-collection": "0.1.4-rc.17", "@radix-ui/react-collapsible": "0.1.6-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fa2279abe3f7030756ef8aedef9e7dc08fad1e0c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-JVFgez5dFcaTQOvQvVjFxv4QOWYIvWqIu+vvXcKVExxuyyU+bXqKG++GU2yfJbA/FBu8MFZKSrbbekKkBYdXAA==", "signatures": [{"sig": "MEQCIAq0zPp81VR9AwE7TRZfBPWyuuVOHAaw+Z0vZ8DOQc5dAiAgxXkA37xhR4twVnIo4YZ8QFJQIi2SEX1hSqyA+1KWaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBWmCRA9TVsSAnZWagAAWKQP/23KRq0lZwpvGz9yFpfo\nLNDLipFljnssGg2FWuq55LzGjU1XVozLuG65su/hCk1kFZakUNfrXifyfZ29\n0SI6lO89oEKpP0Blk9inwxupbQzocxhl6CSG+wyGks+4+RkkmQp2yO05bvRQ\n8mvpknZBHzDnPvFrVPz+Xuqc83lyZc7QXT4oXUwYDVzTJjFTKYOqUdoMleVU\nFpIzWgVUhpXzAyxzumCruc+SDMq85kqOMgagrlrUR+YeECX5Ke9TaGiX+eoX\n19wKnaq8aR1eYX64D2IXthSmXK6aCjV78VO5MOqmTrcs+M8qVAftzsVQE4+p\nhv3jRKZmm+0/eLJBkoZft44Y631zkcHG3jT1IxDFccnPLiMqT3MVl4MZER3S\nCYxgZ+j3M3F+8rKLPuEdcA7Tq+vd9Z+rMISEhVMzv2rAYStmHIibcb3wg6k+\ni1xvCJ5XF2D8f84nAGDDXfs1eaReFm6e00lcZMgFKXq6W9Rod2YtTW2mucoP\nTqGJiMgRm5qA+zxoIWtaqo1nnVBMoPxlsXhFn/AgFV7tfdc8j4K+Yvfc1MZb\nkccJJG4PMZi9FffDGgnBAFF573j+rd6y+gN1oDhYLuLCwxUhouPTgra07VaZ\nyOrAIBqeRBotBwpXtx1dxNS+paQ4XDqeT/WHGndafr+KjxeG5yLKGHdarwzC\nGO6Q\r\n=aQa6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-collection": "0.1.4-rc.18", "@radix-ui/react-collapsible": "0.1.6-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5122476896483b4a934ce9838cf4d8fe8ee0e376", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-/zvjtRDGRMjG3e/tt5NlgXnUJg798/xUb6GRh+oEKOWZgJBtWvGX8QfZtt3cRGtYy3V3vSsNJOHKyU4cKVkg1A==", "signatures": [{"sig": "MEUCIQC4PxNvo1RVHOGyUTL7mP4uQcn3/tRTynz5JE/sMZIoyQIgfg2f+ttg6eYJzHf0fTshFp5qat3Jn4PJx7qGpkR7WCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlkjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrawg/9FHLTru3z4JQnf/4Jik3a1fRknfzIS9fYKuxVLi9k/cbobNWi\r\ni8XumgR7E3IpxOJXI1P8+KieqtloFENn1cPkmd+AKd3KuZcLX4MSObD1185l\r\nVOl0r38APnl+MYv4RW6euw878VU6gzGug3GoGKptnj65vmQPAiItIS5bFjSV\r\nn3USBeLEgdSdLfMennATQOkNro3mEi/dd4UmX/q+10mTQ6UJ3V/cfCXPRIsJ\r\n8chye7qXW3NBQtJT4SUDrfXL5K19uQBtcJSolVZQh52+SxZbe5R0aIiUHS1I\r\n5BR+vKSbwvWy9SAVgCh7Qer5Nz03XL1ic0xhiLCOgYxwtzRuMeKsppKmxXcQ\r\ni1ahRMDFP8n7dFFjAxw47jOI/uMBvft9DWRm1JWC8wg4lKHsk0HYED5316Fc\r\n0dUgqLFXeyxVUlewQlQjVLyctmCgXLdNpmzPg36sDR0bquUHWhcbOO1apKxR\r\nf8K65cX7EBPGOAuABp5z6F1CCoWo9OlPuEnoVY7sNK/JQAk3d8MVYHsyqDw7\r\nOWw3f2C9TqjDaV/ZjRCUvdtXXDcHBJSUdJ/r5y0XyhCUwZbtpqAd0hXhzfQj\r\n4YtCqkSqNKZfbcstF/5vsXoPQWh6+9ckHB+3wpdqRYZ3rRWJENwm0bQ90meU\r\nO9m4b5QI+2v63fpWBGG8xkmtHtVsGnpOt1g=\r\n=KjH5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-collection": "0.1.4-rc.19", "@radix-ui/react-collapsible": "0.1.6-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "01966e972f9dfbf6657876ac828ef1c9a08f9c99", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-zQUWLOtenNkEBH/v8PKwUdSfAB46Daf5+ahMxylxGMkPgs7eh/Vn4cTajd2r4jchbduouGt25WbXoiJyGmjiMw==", "signatures": [{"sig": "MEQCIEHBpssWzApmvNZGK1P6PmVXG7hNArcB3y+4Fn+WoA/fAiBj+g2uEMd1CRL15fhHodKFFWnE58NhevVPs97DlFrFIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJQA//TmHenQelsuwUgZcUsxkIjLdn/o1CTDPGkRfCoKIFTd1IbGci\r\naUUfzwp8vgaqQvIJgtnOEGMkKYsXUXSywiCzwSKwaA+/7oPT1gIPFEgzw/z3\r\nWcV53NdECsn6aLFrR0DwVH1kzhQL/AOBgbQmHYDYaOMEsESRvx23cYRb45Rt\r\nnFP5BZ76YSVcMfHyWgvMkN7lKstdNrV6t6eaD/QzuNxv69QD0Zh/vAadFcJ+\r\nwiiGjmiQw9DA7ltFCLafZgjtmRlFYd7y/ASfr2CrUgbJeXe2MFvzKA2osChN\r\nXlIOnPMe1mwKMtYccsyIZ1pC1XPHPRtEpPl1DRwkrYpV5Ux9VVytdTU6tM3k\r\ntjjB8V57cYS42H/uWFPioQV1RF4Hz/rrO/DBKpe4xjB+WM5PdLdFu20F+GBa\r\n9FuD3bpdm4R00FBUQ3Y4JfoogMMLKUTgRizyquFagjRxE+z+7m9JlH6FmXvm\r\nT+RZ1QVxzXlSGaHDL5uKo6FBmpEEs4s7kCTem76gFNVfGqiulRIDSpWvEVHE\r\ntE7FxuQbSogcnSt2p0fM69W/f65WLZbCZeyfYHo4ty4D68XuHW2Z12RLzcNW\r\nfEJwFVpJUsF+9cweebyvD9US3Mml69UiwQOmQ1VGXyu9UWneYDDdJz+IevHV\r\nL1ymUy5JR3qeMh/QU/LEi73pEzV+WMUxQsY=\r\n=px0v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-collection": "0.1.4-rc.20", "@radix-ui/react-collapsible": "0.1.6-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "53f0c4e326494c173395570c28034d8a6f7333b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-/wSXmSeeBjqVYFGXNnZ8Aaz4eGR950RS+1PzvsHu0b9P11me3GaZMtMfF8hbxnmYTfUU8StNKKVj7uVtRh3UOg==", "signatures": [{"sig": "MEQCIF97U+2VL12DHukzUoUYEEwLfYIEKasb9cGRUD0pOUU2AiAybBMtc6x8k3SWMPMS2PnGGBeb65S39A4jmcSVVsLrgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8TBAAjLKLFkuSzEFYb3tVuHGy4JKMbCXq5iL8Zhh/XucnO98Llpk2\r\nvSmwv+wFmRnouN/jMff/HCzh/7b0+s+hj4aCsgubsLIh1fnQX4BQEu+3TbNW\r\nX+TiKnwHmYQZb5wmkSUVk5NZpz50CJULmXkywoyA99Copvs9MQEp1c2Lv/ne\r\nS/ar5oLXg+FWsbdbXe0rjeRmn6Uqi9ixFh4IX2/sNeRnkk/ptEGs6xoPJpHp\r\n+Vze9mdgiRmTLCmb/JjNUPKJWj6Z0dtYVe7ygOSWQFJ1JcWHWQ+KOlBcdZ5k\r\nNTHJUrZAZ+WKiSAvP6OP/7ExEu/JtrjkczKKE9SaFy47FDP/8FEouYZzIevM\r\nMfl+YaXt+s8mxcTi6xjaCMu1tKllYuDW+BdT/09xSvnwQ0Ly7BLtuND8PVBz\r\nszcviBhIewH1ZGvm6x91VrYxVxXnMSBGltyOPArgH99R06lKG5b1xDwnWr80\r\nFF8SrW62j4pJeVcPfz+hLeP69GVt6gV9QdM914+9zqWP3A/gcrQYdW6RWKXJ\r\nD+i1CUL21G66rzzAc50B2v29ItOLPv3fr0UltsSMDLaGvuLs6WZwDfhmRTkF\r\nDOxFPsMcB8mT1mDlAjjY6abXY5MhFxPu29Aj83t+bPG8M91/0MWDz70552oY\r\nOOHxgQD1mdYQa89TudHe3Nw04XmWI4+Nvn0=\r\n=Zwcn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-collection": "0.1.4-rc.21", "@radix-ui/react-collapsible": "0.1.6-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "acbc7261029ae1ef572aa80090229417a64b946a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-07esmZxH4k+3GVhF1WKfUSce2bdZQ+CTQw+Cg+Dn7AUjNV8oFD3Tw/ZVWpMI9nvreeDlJSBnmN1Kizi4RVtNUA==", "signatures": [{"sig": "MEQCIBLOAvhdSlIgRFw1tqsfDSagxl6ee2PtDbfhJ0FDAT81AiAH5/Ojmi5j8NqNjvGRpqC2+inGB4dQ896j3Op0skZTfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkynACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMPQ/+PXSfNP9/7Xz8E0aFHZ6j5QRRwaHbahHFVdjwrR31ikgteXYH\r\nlaM1hK2xyLAnHdAztKCdzVUqNWGzVgpZfrbxriGdVaGP5n2S7wZz0uC4III/\r\nF4IuKSGjN0mLl+DfrPuF96Vpqfc3OXbU3mxtp/0KB24DOdVtH5YHNB9bC22J\r\nCLz0DZKpQ8Zbm634omtpwYLhGzBT7r9z2J85bbAxPaHz3eaJCWxtGDbIpWv6\r\nSLTr5wiWu9uZgYwTT3Kr/KsrJmvV77poSPVAyPjujxfp2e8irhVm1B2yAb+J\r\nG6f+/c0nmdmJZlrUZMZ6jk2GVt8PW7DPNUsa8Bz7Dh9FoNd0qRywgx5nkqHu\r\nd0Lo4iSTCCiuv59KGh8C7bVUdh5/D3iFXB/zBB38LZ1v1nucokufhiZS8ZEB\r\noVcV6pWmmGWOwERXjvYnEYXEY/k067thLxxPqi3f3b+r8ZAJ4mw+Rx7ItIDY\r\nmNafNYVClPeAPfbs8FDphwJtGb/PbSW6xguKFzbxGJzjutsBYL/chZTDeB6a\r\n2MVZydbQSVxTA8d02l/5/2kQOSrFYUG/Bz5+mxfhpFDuL/hl/Wn72dsOiOz6\r\nKBNEhjFU4hnmfkh/mVBRerr9kNmFWwygQHeHLDJKdI+tp3TOHhP7qHIaCG7k\r\nW2c3fQLEmdRL6qXx3J0agJOyErJn9C210Sg=\r\n=YYoe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-collection": "0.1.4-rc.22", "@radix-ui/react-collapsible": "0.1.6-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1911cfdb5ca8accfcca2c4d4b5efa216768b66e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-v+bhldGg7cXVUf+kjARqfvnV4eM6KkkyaVf3cS7Z0t3rrmT711o/7MqQ2TtbW9x2U/wV9gM4bT6uh6jnUQ/hHg==", "signatures": [{"sig": "MEQCIDBj40FSjCvsFvedtuER1MnAB1uYtxRbm66S/1+v63wkAiAXm51t3pUv6tS3Cnqb9OvkLEjMPRAFSsNUrTKqagf9eA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlM7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKcQ//b/jklpDCDF3AR8NXe3QpuVmc/7oNDtXoC4UPDyGzvzlZOWHV\r\nUmV/KcQmh1jC0kHCkrjm7PCqVXNeMbe7YIpeLvwb06MzNicaUa/Pcw9ZWcZN\r\nGT2K0lhQ2vY2RZgdb7PjCDhPiyRdu7uJOTYjGHrQp8y7HMglYsjePNkAi715\r\nnBp/TSeMVH2L53uniT+Yh4srxe5ZqH7Cm1Z455TTadu4mdXObTnhFW7I0NR7\r\ngPj1gezHnJVFJfTmi7icPtoE9ljg/WHAPkkRl3kkPju5wVTUrG1gphC2HJCX\r\nZiGS72yRLVmeppfWfPB1S630YCPATRMTp4CJlwC83AXCy0Z88FWT/pk+CMYu\r\nyqNsVVrIHtkH2w4hWT1wk/ugkLRoPToq19XFbuO9tbbS2Cw6u/lgefPUY8X5\r\n6tgGK+8Q7jicIc381YRsCXQlYyNWAQOTrRRkFDT2g88vsbU11veEHygviIJV\r\nAfvOlti2LTuXgTndo9QfIw223gR2jLZA7hHqpCJO9dxMxap9R2cw7zuQtQdL\r\naQnlZxJEr4KqlcKIlSmQ4qbilqr6Y4J4b/TImFrQ4vaj3dzEFhSgG7AcoHoR\r\nfs2YSrJRGYEbIvBVLPd+rZWPPaY5/E3lVU8GKGQCYWdhEOAJTZkFwz/b90A3\r\nJ1YSswS22S6E0yID0cGB+2gTl2qrZFpBGEQ=\r\n=IGyk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-collection": "0.1.4-rc.23", "@radix-ui/react-collapsible": "0.1.6-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06fd1ee90bd387e98e4cc6802208a1649a613ee7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-2e0yNY8rGhYXukZ5snmVMNNUVmEoWzfQnvqMWp/Ye+O+ODrKC/N7BsXt//yb+3QjgjIqO6EcSsoNPMXHmUwk2A==", "signatures": [{"sig": "MEYCIQCaA16vcllYtpDdJST1SbPEp3PMmxnyWVYdzEb2pc4WAQIhAMRNFvIkkuvlRDCuNG2c0Df4s8vgGCKtsXwQIffdMXFs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpCzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj2Q/+KafiR6MHFKJfyYO/hlbmzqQtYPNf+Te81l14re/R4tPbQXXP\r\n1GlqEIdesSHwAUh+ZdaszCTB2ZOQ8E9Tve9kFw3yPL7/lPerauc+7bM4PMcg\r\nnqkw5PNfbx6QGjHLxHatM0/WSzVOxaKjHWZNaMfIYtbNfXV4ja91kjjW3v/D\r\nvnT+UxKCF70MvuFRGZudpTfTv/ykDvc9PY9haYGbT+dhKmfCdueifOfMlYoG\r\nFykxkcBNKHy4GO+SevbTf+iLVXSBS65p+1Q0pUkTCRQem+f0KuHWlSDc2EYF\r\nTe8h8Fi1Gy5ainWEv41wMMo3mVBtntqddFMp0OcKhaCLYeMvTRn9afZxR2NE\r\nvIJhTAomKxJnyw4hJBF3Vrebp2BZG3f2bIuLLj2BmHFaZlka4etv1FSjC1wQ\r\nZYSTZEDzdWV9InL4wLh72mSKwtogt+iiQdpz29tnD5qLWid8jDMFrK6wreAX\r\n+pQPZObifkXUpBWwN8pANnrMDpSFis8sQKj1uXGLrQcpxXv6y50wvUawvtaK\r\n+a8gq1QPLm407WVQvtADrEUwTECnFb7FU3XMZR5hR8/SUaSD4F9zyv4WJ4Iy\r\nT0GmO1tHjT+tckdCVMBECXksOhtPWmUM+QmNqUs6YeOTSI/3vYJv61SNNV2L\r\n4qYzbl/+YzDG7pKQH6oziaSxuQARnUPOnuo=\r\n=KimR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-collection": "0.1.4-rc.24", "@radix-ui/react-collapsible": "0.1.6-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "adf27ccf650fb251966d34e7c03d2b530e5e2882", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-kVXgS/YGsjKKSzRr0r6jWQUheK9zSD5QpE6LKXxU7LznPqibuuHBBukSccM16/Gw5P85+eo2CmO1+D5t4E16Yw==", "signatures": [{"sig": "MEQCIA5/QcgzEymeAMno7juaDm1MimfoDBsK1YQln6citMI/AiBTHF0GExmlubMtYaUetQLh8UZnDgkcv05sFPCPBAA21Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDkw/6A4a+tRVjWlw8PdtfCXv7MvBD7hjcEUg/0zm/kj+mDyiHgwLc\r\nIq7/1xLvO8vNHwuyCpmh7+xGmtegMcbMUASAaLV0Ew3683zMQosjGWzJCmFB\r\nrjLur6HQq4HoXKF+FD1e7Fn1hk5T/HHEubwT8yZU4trbSxJ1WDszGdHDR9wn\r\no7R4XJLlQbJ0NCB36dZm+ftBBxSp1euvy8OVEUbAsow2J+2ktAg91XbFWZLQ\r\nEvhY9nQqFAPFmFBXsvIPoTIft31PcgapOQ70smAKf3nSUoFgzP7X1xb+0lfL\r\nLAsSnUOMiOXEjh6Ze6LbhV2biwXKkDDnd4uH6+kzkA9RIAjDq8EEZTMvhyoW\r\nFg2YrvhBg5FjpfZutKD+VjhoK3CwHCyKgxgOpHyzKLhEGucVFj8ptDJlGZD+\r\ny6JTH+AClRGCxQH68/ySAEl8MHGEkM7Xc0kZgQLr9Ys3syKzv2Pno3ycS4Sm\r\nyHwJ2yC1YeBybKNLQzgcVVaVqCQomniveqd41iz6xGc47ZpntAtgJgU/1+m3\r\nsR48IGAY3M3CQxhwoZCpNy2SzmWmsitlQiQt12CkzTCADJ8hBL3D8qTVUhYd\r\nQA2iZHWY5EXzthLiHz9+jPz3UFHEcDLZ0Xfvrxsquf16m+6PlrUAe2stMRui\r\nh6dwOb4UNoSC0qqs/uuKK/pR1F4tPhs7Rnw=\r\n=ivO4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-collection": "0.1.4-rc.25", "@radix-ui/react-collapsible": "0.1.6-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2ae1615a5e971ac835754bdfa2f486e66de990b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-O979cPq82/s+08fPpUqdReJPBVKTdoGvzj/5WNPFu17GbUTfh/jvblZUxa4FJTJbkaenl1N+gG0JTn9jtNKbfQ==", "signatures": [{"sig": "MEUCIQCoyye0Got4mL/NKxHaPo1bv6DLCNEjVh1WijCqqN5O4QIgCc26RDlkg3g3dz7vqTbrgMMP7g7XigkDznmPaZzv0ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4W3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhKQ//ddRDqQcOWTl4qzwEqHEyzr16MHj1qp2AlG1coNmJR9qqz0a9\r\nEBq2pf6L29Kyb+AbiB+mv0n0UYasxh423xtdJqt8kN9UVt2hWmft+TRs302+\r\nKmGOVBFPuvJzCgcvXX9e6n9hBU7xlg2OWy7QbJPHGU3w3IxPNl2F2X8J5ydV\r\nnl8GPKOTFcZl+6TT9uY2ITKG21e9CGhhoGQYoPH3tlSNDZd2xD5HqsAFFtR3\r\ntG6Z+ntNPPvTyw6hqQHtgX77FjNeCIhy/QINwkjO1iq1N3HZePxWO06qi/LS\r\nDWvs1ql0t7GxR2DYu28u7sEkyIULroo+DakEZ+goERiet6PTKtifUk/zk0qi\r\nTNmo3nJPjFe/pjNOa1Cv6g9s6NC2aDnDc/nphxKHgEgkzpxXx9IFLk/TyZe1\r\nrU/k2vaFIw0HXfLD4zSRR5dZHys1/ecRQ3XP+LyzMFW1n5kGYSAVI78acnkf\r\n0Y3t2hMo4aT3md1mtRmyeNlFW6tzuY65mgVQRs+pVr/76jbTZERdtXQV6inN\r\nCzyXCZOf+ho9TQwuvNhCqf6x3M807CHQJuXouuOS0gHjU+X5eLIpk3K3glZM\r\ndO0y8Dpl/EmysOXWQbYSW4qWZCdjh4GoQMhPnNWGoTVoohUF2FSuydX6hiIJ\r\nuGXzy0i7XR938JNmpkIuCbNVqD0vBQF4E/c=\r\n=oHcG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-accordion", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-collection": "0.1.4-rc.26", "@radix-ui/react-collapsible": "0.1.6-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1f521b376aee25e5f9e3650e315c341cd2ac7080", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-9ljDnT1RKhty8LuS5x7y/u5bsQQT+FFH5Qoz83POX1XuaLCT+Ma2tmc5o9JMorPCMF8XUq5BsveTcWqtc031HA==", "signatures": [{"sig": "MEUCIQCNh247pm02IkdK+zws1xEswAwJVNMd5ysd0N0wYJS4/AIgWKQD9MknIjo3mvhhbyoAM6m1FDSVZVAOZDy7+EJ8mQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68017, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Y3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqzbw/+KuT4ZZ8butQI02vOIyOH7N6XYOFHttL5+wHcJXzahNE/ZvrY\r\ne5T8mgvMd0t8UNkZP56xie0bomnlvLLejbMfx980MpTkJvs+DPTiyNEJNwGA\r\nkrbnQNq9lRn8QPRRCUG8S9p3xGFXrsOK2JTHtTyC4GBAxzR8tp2C4uRlhuqL\r\nB26v5Ll0W97KvIJRb3K4LYky3f00eFUj6mhxrCEcVEPRxmIWU4MI6bQGxznv\r\nBWRAKLkBfa+wTrnoIpHUd4rjurju+j1zlg6Y/FBHeyqmi1maiyTpgc6MxvkH\r\n+h1iBn5K+ddf1/qct9u/NcBnzoiRIxervZuBBrhbiOOvBSB9FzaCFfNMnF4a\r\nOxkNUljzUiwWCrFsax7HqggEg6C9lXMpweaA60TVLXhF7iRHnzceEm7DfvVF\r\nfb4ZxB6e66qDz9mtxxgpsmrO6ABZbQxKjbjwj2s7lJx1d0mz99wmqWx8dxPe\r\nbGklxG1al+E6tzj7uJ5RQtAoF7qzZcFT7AnMKmeCIurF10TxF1aYp/LM6fjH\r\n9XBb56GqqVWHQMMsi34CsIPtBiXK7NcVYdyM1PMtKF8e2e5zAYy/70GSGwPC\r\n0fH8VPxz/3xFBAn1SD1UqJnSbWHUOLA8fXgV1i4BOAd7zinzpLWStBsVOzHp\r\novRZyA0dm+485nduBbziErTYrEMLQu6oRvs=\r\n=0hxe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "@radix-ui/react-accordion", "version": "0.1.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-collapsible": "0.1.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b76613d56717ed24b8cf6cb1897cbd54f04714ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.6.tgz", "fileCount": 8, "integrity": "sha512-LOXlqPU6y6EMBopdRIKCWFvMPY1wPTQ4uJiX7ZVxldrMJcM7imBzI3wlRTkPCHZ3FLHmpuw+cQi3du23pzJp1g==", "signatures": [{"sig": "MEQCIA2MIAm90WEPT3hpu697JSOgdzw/6nLRcWVy6UsINzj7AiAFEwpEU48ekeGZvNwNyOdfltmMIAhL2mwEnRMi3YK8dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8j5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouAQ/9GRVjEr6ohKVtwqDJ0Z4rjVoWbxxCf5Y0/XSZLLSPJD4aDAxR\r\n3XKvQTtCpsFwZMSFRN/wmatVHSiL/tWGev+r6FJl45W2PhYnqSkIroUNYX8s\r\n9hdh11whw8mDXJaoAfVzVsOgBQNAiY9/ARWrRtuRmAcUAiiOqZzmwpgDFb+B\r\nB3M6klKWhDBSfZ9Wutp5oj7Q8F4qr7rVny/M6qyLE7Disak6tvUysQcTTSlG\r\nuCLjwCv87CNXccDKc27yQWj4eTVBnHidTcs6PaANaF4d1KvYGVLRzKp/nRPF\r\nHjSw3O5VVfqQnj1UsIIO+4Hzas/9ukXFHH/Ql63pgs5CZ47OLxZTXmVx6w6r\r\nZ6x6oAHmHbpmioVABGa3GaIf5u0Oytbe6foMOVdQxYcWBu9+Rd4kRp9yCckt\r\npsOy+/yVDqbuXpvYB7woGhlfsXosQuj5ATctyORw8YkUijinIA+hDr6vaGn1\r\nFyw9aPM/RfZQZo/yGWcW5kU6sK2uTXSDNmamDBoT+8h1KzXnZSUhqC+1wJV2\r\nU0zxFdD6DQX1KF7/YlMVvdHnto8vh+jwQo+feQGarsV38gdDQzC89vTWt9ZZ\r\nJquP4ZWdUEOW7GaCtHZiNM5m1VnsczalefYjglLPQ186EcPK+Ez79fPAWopJ\r\nODKNnrvTiBkkyCvJ0A54LX+Jb3E09PfmsTU=\r\n=EqRA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.1": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-collection": "0.1.5-rc.1", "@radix-ui/react-collapsible": "0.1.7-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2371aad6ee18c72d3d2e52a2cffa74015e3d5765", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lX2eBXO169zOqhQo5zZrSMXFuGWIrKlJgCbtvwTKOt1SxQ980p8KGRSf+0uVCs5yA8zlGpzLsFKRdjACjQelIQ==", "signatures": [{"sig": "MEUCIHUW9lq8sGL7xR2rAQ9fxs4Hy+W1AMp0c7o/k26B+VBPAiEAqUqutt5bnPJT1zKjIJMuiFTJM7nlA7IrpzCEm95MDY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS7g/9E4H2XjaD8QwIrkO/lMNsticyNJOOO9RlqGWYKygMkZh6Pdq1\r\n5yRkfmUUk9ULqJGdScN3Rl7hNayqmFymgZ+w9K1jRyt0ACJRkCub1aGteFaL\r\n9+S2I8td02o3+HraAPM1N5NdKX4kR5ZppkySipS4orliutskiKu8Iti0Ik43\r\nAyYTzYYJ0RcKSjWeNEGKBFMTlr9FkvwcqEMbJyVtGZjwHIQsHSX75g7qzpqI\r\nxRUnZvLsHRS0vMYl/+U9og+MosCPAFUGT5SZ0xNuoKEH99d4RV7ScQdDk2jh\r\nP/BlGt8LXn3Pd8sEUhUaudybsObojJ6Dy1hSxjX8G4g67ffZlpcIEeJYbw+F\r\nZaxnXTpPr12+UecVEoBoXroyh4LgNtaj7U+CjMWxYITfXYJRpllODU/gBdSQ\r\nF0kRsmRcQwasqr+OidB3BS9ffDh8F6mjOE9kj3I+OZks0ycWR2+glFklFxaU\r\neul0osjWizcHwV38NFITq7J221W64jCaj6Hdlh4eP1cknYUSuFXI+64qDV5x\r\nZd5vzfsDmnKSs07nMM/dH6E6R/ZnK38Qv5ZCBjTvpa/++upaG3ONWTVIn/ZI\r\nsn9pZa+dzzkAwRgmsBo7JTspMZnR4P0SvfKMbE5cXtIBtkh0Ae2oFhhRTqBB\r\ntS7EliDQBkZv/UpK1OVWZ/Fi2bub7+D3lQU=\r\n=vghX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.2": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-collection": "0.1.5-rc.2", "@radix-ui/react-collapsible": "0.1.7-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "30b6529220127cdc3156ecfffcb57643dd5ba45b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.2.tgz", "fileCount": 8, "integrity": "sha512-VqgNHCYywmiDofGT2Yg33Pk3wx1A8q59R/DiWZVmjUrwBYBOQyIifCpBE2dcYcgS30o0yYRv8T8CfwEOHi27vg==", "signatures": [{"sig": "MEUCIFgb75gjgCXSXPieb2TJhz/7WPr2rXJCoryHVu1dZ9ojAiEAsfnC2i0VbXeSl9Z8CDNWQeLj/vfJAo2pbRbbym8qEEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpOg/+KagWxM7BeHYvpIcNzjYWSu6IMBDmll2jPMEz3cbhXSzNwmsI\r\nkzGbydzL1YKH51pagG7RjVohAUsMxr6R/v2e2IPRJVscIPNgVbZbFGHet1uW\r\nazrX1jqYI7r+7JcK/YOntnpKNmBLyeIohYx8+oCHGOJsyCschZtGoBQenrTg\r\n/+F//3QxBXoN3E+UEK2nGi4AdxbGOCkNZT+NP36IuP7C5ttctTT2kC82HdWV\r\n4OYkQGlq2aji74GhEVNPgtS4AQ6UTgU1E50iIBORSiObhsJhZfnDWlIjaldg\r\nUmJpk0x3/nhXg3KgylDwjyELzA8yuX3wbY5UOS754ip2WC0gxiJ/9hfuFSyk\r\nd7QWmbUd3w6IymS+inf+38WfQphGPgEFyLDUAqgsPZIoI/0Uq1zi+aZdHXwT\r\n5dQAy8jNHqbNJF5yAlF+B/3E3abt1HbvLcWHy2AmOAVuBSy251X67ki3/kuR\r\njFRTai1co7kc+WMJ0tqyQv6D8rx/L8XgUGDRXSyYTeZcbihtw9ejWzddk1+8\r\n9qbjLS6uHjx1xgkK/vF5GTht/uE2/EpHX5NggsUpSZFH2M8o7HYv3rtRMtsc\r\nOkzWJxyNI0n3YikKix4pBu3hFdVxCcYUWDlfG0RdSaPplPM1YuEPqQDxF4Xc\r\nkZE8PqFjetimHpW+PcSwfAzz5tsocrgvxBM=\r\n=ktKG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.3": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-collection": "0.1.5-rc.3", "@radix-ui/react-collapsible": "0.1.7-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a4cac25f9f31fb413ffdc1bd9ba70e4b1ff69acb", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Uey5/n0Slc/5Whllsg4meguQ4dsE51WM1bVuZZDSi7zoKsGBhFWRon2DngTMVI39+pG3B+mZLvPbFGXJerPQYw==", "signatures": [{"sig": "MEQCIAh7yDh2+hQWiepcL3s1RnMa9N8jca94HNIJxu0L1x1zAiAswjAK7GprTaL94JXx2PhReA9SjCMUnt4PMRxta6jdWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104480, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDShACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcmA//eXXb9qZWlWjqYvs3RuKdutsjwiaqKSRWt1qxEFy1cw6jZHby\r\n1yTkJMfHg88vLIhmQlFQJHE0SoqCIA9vSbDRxy/4gaCS/Swc1ipnYrCIYPNG\r\nKgc9CsH3T9N8GjPS4bYWdyej5TeCeM7s2TM2Jd+C8iJHevYIYS/RZOzuL+b9\r\nFIkBHBWsBIm9BrlTf4rD3oKcorm+vFSrZLYBZ49KQseF8B04UgLaY2NfTbWW\r\nwiCUEd4kqWhFA9L9oeD0gZjDwxcDEClo/jJDjczDA/+gWJ8kzRfAkhVK+2qT\r\nphhHfn8jNBOn0Q5VcA0FGCvTyliV/AkQCr8+ppBz8bp2yw1iu/BiBjBcUKuw\r\nnom2DnuUTh/By3hFymOa6yKAX6zHnKYF+UpBnfXO75A7jqFfvSQoIrRTg8C/\r\n5hyWjaJgG8ju3Uq8BR5iaJ4D064qdE5edOug8T73Ji1UXjR7Ie3kX4zzr6eC\r\ny0A2OD57z/3FYNiMbuJNVm505sdcFyUSIZkDnaW36cqPK9v1YqtVl8kdmh+o\r\ns+6KZasIqajv0mWjecLrll5qyLaYkQaSLBQzpqZRpqlzkrl8Wf2cBgYLB/jL\r\nqk9FlxEKas+YUwVnM4NHdby7l9ZcWkY/5dZnx7Fasken90lvKYP5puiSUt5O\r\nwU3F29sR6bH2XfvoEz9fN7re96suB+73uRM=\r\n=RfKx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.4": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-collection": "0.1.5-rc.4", "@radix-ui/react-collapsible": "0.1.7-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "17b1ff505ead40d1b4a5a1712dd714e116410c71", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.4.tgz", "fileCount": 8, "integrity": "sha512-XTazgqgFBBmfhEFHi4TOxE0NH3jLb7kQKMboVuFPwwSWdK5YVBc/EyTALQgxxP2uMiGqXQl1V0tkG/f751wGyA==", "signatures": [{"sig": "MEQCIClw+JJu22CxI8LTodPgzraSRDIJRIboiRcrcK5zQfTpAiBxFRMi0HiDPTvm9aNYttY+sAwnmyisfb4aEc/ipAY0Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6DQ//XFF/eN9EV06MFAKkdpXY6rCjEDaY+TyM6ZzXP7Edo+8pDIZo\r\nqQweTGlztItO8Et0PBQzQ+umG9fh29422ityfyNgAvLjnY89kDMRsritbn2C\r\n+mP/KtQ/jt1qqtfUimmqtnutGqkNH6zEhO587rbOk6xH01hJ0jKYIRd1jr/X\r\nP32gYuUwG5t5iSw2ZcTOz/Oqlhvh3rVkc16jaA4nlBo1i/pbTnT8f6qHV8qb\r\nqjrI6fG/8EI5jH/fmyrwL7UDg91rhAToeLuAF5Ar0r0KE3qqIaZm5X4ZCfsO\r\n4N417Aym3hslTLQNbfwWoqBp6j5uq+pHOf7c0Cj3FvrS4ZSqxsEn1X8B41uU\r\nBfzqKaTbbgdYnb6IjBI5YxgrpHw27xdjIlFiy+coK2rHgwlYdehzx3lm5Jz0\r\nHj7cP7vQpoDdBTW+fEovFYNgQ1M9ADVG5Q2RFybygyaXzIh+46/dQtDiPFOZ\r\noRFty7adoEWcyCzHUO0hJPsFc9eXDgKcwIH+NAKYJvx4iAcfn4mGQPezaddZ\r\n+kCApl3x6gVIMbLS+za9D34y5WLEPNbljAfoXOiHp+M/hfWxY7jjD+y7Z0Qy\r\nzdyAM3a3v2K31pPzegaQHJHHcHM+Vx8yaC00VMW+m3LCx8VTNPJt7iIUlGYR\r\n7ps0ZOFM8JC6gREPqpOARTF62C8uh9ikn4k=\r\n=SZgN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.5": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-collection": "0.1.5-rc.5", "@radix-ui/react-collapsible": "0.1.7-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9d66948d0610a0b8fcf8f68ab1e49d387e92ceb", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.5.tgz", "fileCount": 8, "integrity": "sha512-n1usf+ALeRb60PlQk7EIzzaIvHtHQoubi4bBlTHtcB6W02bv1Hx4m8QVlLOyMsGzn1BCa+jglCzlCXvgazqxJQ==", "signatures": [{"sig": "MEUCIQDg3DCllgwljnVPD+FPl2/P2NqWhMubPbw8aBM/rZpZBgIgHOJ7XZN0v+HDtP9hKkKHrXUXHp1MuSSoTOpxdtt0om4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapf8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6sw/9GLIR90LOFzUOQ2AYInVpq5LYye0hfQLKUXmx50Ycqjecu8Dn\r\nTJPphT+BVz4h2Ib4vNONEU4Ic4kYGZucsKvNyUxvjJIF+up1XYRugRMB3TyL\r\nkf/dyOkPJB2aay0UeJ8f2QRgAvrBStVCyLIxzJpvuQmVPt/XAmpO1jxKv1At\r\nJkh2v89b91M3aA/ffJmxyDATR/a0Ez99gR/o9HxvHuHLA6HeTL6J1JIiz2QQ\r\nMHVZ0wxEcjfNXUyyKqdobdtV0o9oY1o4/Jry5tWvMc5G0Qyt3dt3+Bx9IK8Y\r\nhCfdz8Lb/cgN5o2Q2B7KEvzvGzNkHALGDOlpJRxEmhett7p+rE0cAkI2dV6G\r\nNep+mID1o9l6B9/taFk6vQiuiXBkly7MpsBLkbs/HfCi4GqZzPeQiBcfaX62\r\nD88xMleS+Lxmj1QgCVAosV3ZAPAiLk1UgWUJf3ATKZE4jPl9lFQyLqFX5EKZ\r\ng5+/JXguoSg1gIfbNXyo/PuE5lHgKZ6sQp9rVG9zFjDf03Vtvi33Xqhf0M0/\r\nLY2EIctZG2D0YiewX02/ug/ty/pvQPkI8rMzX1184e6DEzDF7qRvfaPIb4Zw\r\nHRv9GB2vTNAg7OAS+9y8CwnmgxyW5QjFAATSMxLe0r7hLLYJpwC7wug4dw7+\r\nh/QtipeDl6pprL8PPghGx1jhOKcNWXpc8DU=\r\n=iot7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.6": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-collection": "0.1.5-rc.6", "@radix-ui/react-collapsible": "0.1.7-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c77ed08d5d8a029a2757ec6097360fd91577f19b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.6.tgz", "fileCount": 8, "integrity": "sha512-UeOrGBDldqfjbmrcCD7lFqZkTtm/SGVuvC2K5wCMRu7ulqvJKXfiJkfLQhZEP3iD0/hUYX3QpKWEeEt7mTpUxg==", "signatures": [{"sig": "MEUCIQDw/v1wEV84okExhL3VAZzR/M22jmMAglAsPuRki1HqPwIgFTs/gPt9PqqpFHhl6jKzwWoBo2cLVjrWaEHmbcrBGoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkzg//UBNxxAM5eHLYcD8VjWpQqakRqBMLhGLoenM35NfkhPX46/I6\r\nBaDE2jPk8d20NvSqEDv9LlcEStjkkG6hxat7b4g6o3NDo5JmX2GY458YMaYG\r\n6EPzLrh0D3zqXrp03a4whYA/PXjKjZB0QG0VHpQM+0pZs3gVaYtcKPbRIOh1\r\n4sK+yEqTlgv475Iw6QxWLyq418uc0bGZd7rpcH3YkA0bptkQsNKyo77pWenU\r\nVW7huly/rIksnGuGjAp+FfVzZOUJxwfHVhfZ3FoiTIAj4yen5H+d+Mxk7Rkb\r\n4B1tgP0a1h9NzKfdfT+aIDj8+UfXkmT7w4/aycQj9ljmus+kspxAieProO5y\r\nnqUybJygV0BCEx4CHHue1JUjcVH/m4EmmuKOQ+EEr0Tl1WNFE4qMIKcG372y\r\ngbef7759MRX8/HpYLE+uP6bBN1AOxi1aZ21MV0CWxnhIXbD1O2B+QXX7B5ie\r\nZq6+bykyxnk9Fc92berTnQv4ImSGe/2kld5uk8i7aJ0PVXBiVYfcHYvZZWnX\r\n95p8jB78JUAfNDYxtRrwty4IBfXNiyHEKbI3zDyeI/cmSpBI6LeXDc16500y\r\nQ9BKej3VDhsMT45XMA2pGDzSFAz2ByEQ7slWFF1GGK5r7yFO2gy+D/9HoI3p\r\nawX+d1LjaGo/1hA1Rvu/fRujFL2lTKdqGJ0=\r\n=VlOa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.7": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-collection": "0.1.5-rc.7", "@radix-ui/react-collapsible": "0.1.7-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e75f2208ffee7d907ec1d3b54ea39245a55c2370", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.7.tgz", "fileCount": 8, "integrity": "sha512-V9MebAu1sa81kUx+H/4hF+i4+OdlDm5y1/82F2yUo4q19HwWc8EkRQKKwuSjZTSEhaFvp3PCqGni+xUARZYpxQ==", "signatures": [{"sig": "MEUCIGhDqrOkDNSV7noH5ksdbu7TcOMvg4U627Zd+tzTbiHBAiEAxMpt20Lum0Pd0JKD8oN/cdG3V2MepETn17GhVMqKw/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqd/A/9Gkaav2hvEposA0mHP0toryqWQBdRyopqbM3qyJbe69k9bxON\r\nCAdCMDs/1DMeCazl/1du1pVlwA2lNNwG/ciVQhuxPUT+4ET+Hn83tPNF65NY\r\nyU6pdympZYTV/dvWrT7W0pfPO7tEMKdMI1hnkvz9ed+vifQ9ROixf73/MXtR\r\nXGQVMxiVaxcTyQ8moqlk8mxmAUhlmUffPhCPV8lxYNvpCwZnZvI56lnRF7LO\r\niFq2bZAGd1ot5gfty3DprGLey+Wv5yQYOTgtOu5lHSaSDmaojOyZunGtnCx8\r\npRedgqYtluiQ3vZD0GIZaBqamyhSM67Nse6gA81GEWf7LMjjTosd0OYOWdbU\r\n79hGPh6+bBZEuZnY0dw6qyItKIXqMFuA/rqHSsFDds5P3irEA9Pa3stxkpxv\r\niBjF5R6+yiDZzPvfHUK2E7usicz2h7niUk+3B8f4m73RTbIiEaHM9o1p+a3h\r\noROXwxAp4QPmwfxeXXmTGffEu/PmdB/rU7egTNT9KOBnm1RZRCuWQ5mLzufb\r\nZsX61Wt8iGqWka0v9N0TE4w9lBJL4GEQtHuN2vMJWcShI+jTc/P/1cI6r6Z1\r\nfGEpAv4SihzdvRHlqG2AI+qO3pp0LGVpFBXNzcmvrl4fv0pR0IDVtgrBd7DP\r\n7miEwXJzIQ1u/lExVx5kdVCpdcgVezkk7MU=\r\n=TwgG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.8": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-collection": "0.1.5-rc.8", "@radix-ui/react-collapsible": "0.1.7-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9fdc3edea36e19cdf3edefafd17a68476b8a20a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Lp16LFHS19Eg9aSHQp2ssoQmC+p30S0LVRnxMmvlqgL/LEoNM5d8tXesDbfrcqnzR8dsP96f9ibKdzSVXYCzCg==", "signatures": [{"sig": "MEUCIQCcoCa4RiXWW+iHmpxx08TG3cbNWn7g0/7jO5RcDNugvQIgB8dYgOJNenx8adLLTThF6/weWdRt0jYMy5qzjWk4dV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwiBAAiyIxQQnqxDMpAzJyeZ0wafnyoRnrMQnwxvIT5lrFfSzNDdH2\r\nIf128hQkk94efg89TWjlwMv61VJbyED1ktYcQY0FGNLcroOo7bg93N+O/FE9\r\nkgptsQlrK5NiBPkTlDH7hmuIc7v5irbcNwb6QveuYARYYYFaLAogF7yz04uv\r\n6rITq8Z77bga2z5oqenPXOm6gw7tIedclXxV9v+NrBBlW866rU7d8Patu1zm\r\nOYdHA6CcYsg6t2jT9E8/OSX99I35YA3PipIB3hIURmroTtUrhjaOAqDbHq8a\r\n2MKr7u6wmQsLB/Y5pVOyGzhmdu+K1t+fD2dRzoFmDaPBwppbiWan5AI0YY7B\r\nbbaZBPl4L7vLAbYXjpmQvud00bHvgEKjVA5lAOCu3jkuUjVwWr2ToH3oJbQ3\r\n/KY62dTe98Wmi8I8IQcNsgVDgWX881K2+ionLFNpi6XB9SCZ+TPfy+QapZSq\r\nlEzMjeu8bm+7fvAcfdP0ZQFONiZwl2MqrFlvFa7wqXcUsOWGkfraiwP6Nqvj\r\ndPohpHAkSHKqZuCg0kw2aZFYdz56HgWUHAIZw/w0cuKIRsVrMR5Cvb8JOi2G\r\netvJSo0qNIs3J+H+A4ito+ku5m7ZEiF8cZU9JuXiEEhvFx5K7nzVmu4PLX1H\r\nJ9qArhdTQJ5xGR6mXtkpwisqOZW+JtdUmxk=\r\n=t8r1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.9": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-collection": "0.1.5-rc.9", "@radix-ui/react-collapsible": "0.1.7-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "49115d6ea1f46f266f0bf6741dca73ea5a13ecd0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.9.tgz", "fileCount": 8, "integrity": "sha512-cezIDQM3sgxmIc/zWxNhKFaxAY+k/HatRrXLXvC6qDyp9B3UV9uSzYIxR0rrUy9pBAFYRXyXINbKR4CE4gWsIw==", "signatures": [{"sig": "MEUCIQCzV4aBsTc2bI0WA4hlrzGFpwyk/DoJ2CsX8kwpZIFLHgIgaI5Gg8c7Q4Bm13abduC3a8TBL8MDDO0Yvspj5OBJuSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9PRAAmjzMFw3z/0HIksAHR+KO6LSmBLQ3soNaTCcbKjAGYWsbPCS2\r\n2VXNnHigZRs2Bqyx5ySvlOEStjz0a6WTvOfBEEyoeiiCzGK9YMEPdMsMl8Jx\r\nu3BcycYcQd2tArXqUV0rbaiRT+HnR75bPnxZyvlKrndALDvvess99+xn/0yD\r\nR4Rhf2HkS6wFo26pi9ulVjAce3Mftl+jllwBqVUQqwRZoDX56H4o/5vyITpu\r\nacruNmYyznLlqDY/mqvoGn0ra7JY0+gsUer6PKSwjiQWlf7+lkyGg5we0OYg\r\n6JRcI+WqRy9cqjddhk+Oqg81q8rtqyGLB+SJW655r3+Gq0YJM/SIjFGcgxnA\r\nMs8mC8r+r7G+/jWraF51H9A6Alk4BdR4v59NNChI/e9DCmUN0Drxec780VJP\r\nsYn4WhxqXYjxuhK/3n4N+22Z+k36rv+oken9mDqYkg7HjDEV1BlTuTskU6JC\r\nVxfPlB4pArRdd89JdA8/8HdAsSn1zYRoAnGrH6ji8P56sPgOFBo/EsdxSxDQ\r\neMfl4xtU9iE7edeNuLwFojIdDSLWtOZyo72Yjtc1Wffm2drsPs+UUMIZdnlo\r\nz0RBFFKU9ypuQHnX/bXGgvijZrFdDff4icTZHc8nqO185L81U00vaOpwpx7C\r\nJ7b/P3eCUc0WDIDMPdExML+mKCo0je+WAZ0=\r\n=Ij/c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.10": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-collection": "0.1.5-rc.10", "@radix-ui/react-collapsible": "0.1.7-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "63d4e1374da29d735064c92b606e3d8e7b880b55", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.10.tgz", "fileCount": 8, "integrity": "sha512-CvDd7NcJnaoa379XEPJBC/NZZ3QfB/1VPaPsM6LrMoniXNwbv6TMDNua6o/qOHVzU4Wat0D42/8u2GGhcsecxw==", "signatures": [{"sig": "MEYCIQCwn+ktQmhGveogiPKe52ZxumNWO7JPJUBDNHJAFhgapAIhALRpfpEjf72KruCHIYdcVLq/uEzvx68KVdUSQn9jwH1e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9bg/8CjQ0M/rXTuoFKG6pGtRPiKoU8pQ+zJiCW+DCVOluSeGsrc+e\r\nZ5R1xkknCN/BD0aR9jb4RDKT4i3GRiCBlNeATzuFxhdaoFcO90miBKyx4Mm1\r\ngCW4+uIa9qW8CJ9dcmCtHFGz6knzP7clzBGzYnNt3Eht9b4gzOJda3hQh0nY\r\nR14W0tH0fXJw9GdB4TesHRSNwAhTP+Dg0YTeFFrLUfqEEqgGcvfvF2sHiRSO\r\nQEiMiif3HhtU1Iox7+Jw+5xGxHfsqwsZvrY8OZNK80XTS9zJxgPJAryJmHUC\r\nTn+Er5OCwNn1VFvPALAPDTt6qVjGnlCWHhZlEreTU7h9z+PEDslv57dvTmwX\r\nYUqN3Wz8uEfIvMEaG0BRQ8bSHHG3bhST0qq2wDB/mE+BVah1fqriYis2nsB/\r\nM/o92603WKYOWLuhl9H5ALh964DZYDZxa7omYOBp72HHMGn8SDItZCqZSBes\r\nwlzW5xKruTsf90DqpWovMkIvgRIRrsE2w/4/hy+ecTrzO+x2MO6Q5V3hKOQ7\r\n7X+5D4zmCxGJ0KD4Ru2tWf5raBjNaCEcAw+JaJX7MCV0lVApnSktzfbv/VC0\r\nY2k+GmL/Df/nqpRo+1QsFbMWPXCw9u8VZ9DHXToU4F8inMl4kLiRXhqqDNpe\r\n+0BSzc+YpkX70kpCNLaKiKXvu1SfwR+KfNM=\r\n=qmNY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.11": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-collection": "0.1.5-rc.11", "@radix-ui/react-collapsible": "0.1.7-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "611775d47e1a0ff3c62c85cb665d2c14d3a347fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.11.tgz", "fileCount": 8, "integrity": "sha512-KAy0SbLz8avAt5G5aMoMFRxUH09IxBnFyZVdUShhFq2GT+PjOdCtPh9fIbUGOPtn5HVklBP8oXO3UVFs83iN8w==", "signatures": [{"sig": "MEQCIAXxV2t5YYpXOPwfQKTudEOBKh9EHy03MXqYgOJu5HGVAiBF8s6Vfy49WyHekR2Paz7q2a8pbYiJ27jl6Y2xdt6eew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSkgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoASQ/+J7jGv5CPLoy7rUL6bLld5PkIqRhCbbsW1mnpWNDMcPANwcwD\r\nhtYeXskHZA5EOjKnJWlgb/bDjPG7R4qbs/OUYcnX6cRTHPPW550PiVOmPA7H\r\nv06fEW9mV35VBhWBk3NZMgMAWXXBb26Q8ZLoyDUJlUe8+pEhKQBKn3OxzdCP\r\ncrZItkkus/WLR7Koo/PAXRw43Rqh0BMILxNqbVe+sgKfNlypCWzn53oROVc2\r\n0TfTh43mF6O5qX1FpwNQHVmRsEXRAB4KMR05HRjLrD34mD3SE45189r6hCVE\r\nHOyAunIFV6dyjA02Zjynx1WrmSIlYj6s7ftWZU3SuDktwatn20wo0HIFBU59\r\nxwnOwuM+7OzAL+8fyaq24CRyCNBr6Nl1VrnxH6WlIZTkltcUowHxYyEBqGw+\r\njhDinOQFK4vs0meWvwrza3VwCq00Gquu9INkpJoTEtbTq9uC74JUNnrsB7tL\r\nvbxo17kKkhR05e1fsE43BT2zL+uDIbb1lHd2HlzrgGAzaAZp36TQp08ju6Vd\r\nqlz+2a6o/oKyl1tHczl/WYM3m8fzOX/nG+4pkiFsAS+pBjLmajMSeiWLlxFi\r\nqH6tqm6seFZD9E7E4TXRjqb7JyJmiYxEHmT9ou9cP2/BXn0YBBG1l5z6x/ns\r\nZ/babELWLZD0Mwu6x5FiPVSAvLY7zFTkRC8=\r\n=TxBW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.12": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-collection": "0.1.5-rc.12", "@radix-ui/react-collapsible": "0.1.7-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "de23559ca7a1be61b772bbd6dc069d38a52601e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.12.tgz", "fileCount": 8, "integrity": "sha512-nmIgXGEHZAhM/FKpm0fJAlYKUP2n1I6eZ2G//UzAX1lvnNGnyEorOnFtMJ5+IJ2OY6ue13zC/hQRCfhUvEVe7A==", "signatures": [{"sig": "MEYCIQDpkvjMXyBccn3kisynyz3hTzzjhWW08bVU9p8LCUOz2QIhAImNOJwQfG6otTM4OYeSsYLT9NVVhI8N4JkNSZ/68AWN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgvA/+M85xbGyhqT0OjnluUfUjqkVxHESZBvi/4ZdDgnFm1ODVmMRg\r\njyeB2MmE4/4sRLzr6TNnWneKy63i0J53FAKbi8x/+VO2Dme3CF7VSbSnloF1\r\n66iBpE6spWqxKRQ4zD7NlLFHX0JBYU4LJ4jsPlTbbFRmDkowpP3+e7fOgu8/\r\ndHfbzNjnH5RwmrK0VIaeDL7xt/MthsZNoPtSAlOSrLBJx1b0CuUd8r4jqZpH\r\nUX8udIyEI8o57r5fG1pH5dVDniG5bTMEFNj2JWUpHtFN+7iQSTx1/0Uz6wAb\r\nSHjBFnaLUsFws9o2+mm/Wcv+PN4MnFx6heYGWVeDngPx36+83H1b7ztHufrf\r\nm+RVa0WV7l6L+pII9vCnEQUQhZ/D8Lbh86ZYWytNj/4tuGSa0A2UqIk7+YG1\r\n3APMh0s0w6Oh1jADWC8hm7Fy8HZBJNwh3zmC48CqzqGU14+gM/BCJ74f1DbA\r\nor9qBgMoa1Ot1SYqkLpUy2IZ6EHpqTDcXMhG8AoB1Fj0OULJdwWvM8JTu32q\r\n07reSNNk4oqKKrXcIriRqnnP11Cw9IqNjCOOQsIIyoICgIJTAwMTmMaS9tTS\r\nhHJ/PMydGCzq6nHE7VCbkSNIwbaXSc4RzLJrj4m3qc/HzPDrtzfOds2+1Opx\r\nsCKhFbOurzwW8u1VwivH8u7lNwB6DEMNRl0=\r\n=FDI+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.13": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-collection": "0.1.5-rc.13", "@radix-ui/react-collapsible": "0.1.7-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ee91a4478ca5486f3a50aa74febf8989a6dbf19e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.13.tgz", "fileCount": 8, "integrity": "sha512-rrc0yPiIdzX4UV7Gne+isAg1XwBqMtWJHEGAzYFcN0dBgK3R3z3Q8ubPtVkelEb1UsuwN9SgC9RMxx9Fe8+MyQ==", "signatures": [{"sig": "MEQCIBygyDb89u+G79pW2dWzXBH0LcSWnMFPdZUkmkIyxLdrAiAlPbP2CpbpR6/BgvC9AACUMej9+TxJy4GxjMsOEaRBtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmofow/+L+CIegCsEuoL3dbi+JqxpdUhZ2UK9fLxz/KeSa9n1u91IqXK\r\n7it/eThG3uWGmfAxkjRnHucW0MxIUZhPOLxJqb9Q1IEyT380jBhbUVUbk/nG\r\nu0fcXbWcoICpFvQX5duMh4pdTzv8Ei55oaQpTdqRtlOjAE21Gc2uHL4m37Tq\r\ndPL+1WgVbspgduti9wgc4/Y1wq/7l6M7Q8Bt4aGjArRIV+LAG8CapUZtlZW0\r\nNdTZF57ATOANaGqbRoAgIhnnxhNFI4ZkszHonJoJJTLHQXBBO7C93FuEfBXq\r\nqsIuh3gHOBV7sE9uTpHcbD+sfyOKTUQdVo7k1O73hkEwyZZOEjKz9S4W2lOQ\r\nHyV683FXuWBBPDBaCX7/5wAL1WcrgVPd+ww4QMbVWgv6dF4v0KSpNtkrtOim\r\nPLDAfEQO1matPluMqrR265QJVonPnFAq9VVdus9g8rsaZRftNBoCHE+bgciQ\r\nWYQjUcBpN1alJ69gwVKComWzI3Qw7uPtPHnnv7XUP6KEPZOEIIsQTQTJbx/H\r\nkH2nqNvAj+vrMZu1tJgh+kXzHM0F/AhYU0VIbrtoFYbKTve+6WFhlV/AJdKV\r\nGN25nm2J5G4LosaTGZP9EKuavk/Zcr7bG8M4NI4pXYnx4EhETguZe3TJovhG\r\nXsdnPsimXyy2hmAY6BOIOmdAhbR7bhhgZUs=\r\n=riJO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.14": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-collection": "0.1.5-rc.14", "@radix-ui/react-collapsible": "0.1.7-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cbcd89b0e1c8b2d64203ce78f4b21e6e4d7154ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.14.tgz", "fileCount": 8, "integrity": "sha512-AbjFuCwVl0jmS1lDlzW+8PNSZLH1UdBJu6bDDppPu+JVtC5F9w5X6YYh/vvZLK6ZlhjmB1CtE3rOH3mjVUoLkg==", "signatures": [{"sig": "MEYCIQDvUEnD20QM/ZS/zfZ5dZ1LUR3KsmdGcTmfNKLdYyKwVwIhAKry7lTd53wKr/TdrEOrjbRwrY8H4181IQ0ARkOQch9H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8o4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpE4w//e6eSSdDbUEUnn5MCbXoIVg6xWbVqRZdsuRaW4118kcb+M5xm\r\nHHwNaC3HG0o0lVDEcdSEon1TqhZ2DURv0pkCW8QM2ETCflw4P44ZZB3sczhZ\r\nLDRO0q+1CvGUcq1s2EUkHvYq9Z7dlsCYYIw2ukAirA4/HJ+sbTGSF+cQnKID\r\nTpPR0Ac39tgF3at77ZUDkCzf6l1sYv+Yu256a1W2dWHzo/iNJLiwgQGmEIQV\r\nf1ifllDwoNwsf9A9EHlFsMuuuGjq8cICua291N96Ssg2/zktjsREca3X8ac0\r\nf3M9HGJaCbFNwAUK13UZs1TFI7xUJOwPs1cUcIS6cZ21hdisyLhvqwRMTNmX\r\n4KJY62DnTINPJ3rMzvuaq8QX/EZtCrzLDG7gE1jxgHGBRYOr4e0RcNJ7SDey\r\nW+foJJ/Yv40KmLl6c/DubG24jHFx4/dqn3yx/FCxitUP9lF+FJD0mDJKqo3F\r\n/eKo/1z+Sxpiqg4rAz/A3Bm4N5f2mMAuqzYMtTwh8YDHOW3bM4MBkohT/iRu\r\nNm88DoDMnPY5t9qsyQGOoUQxr3lk70USHV01KbZT4dHCkcObU3gDskZBWsjX\r\nIK5d+uReWr5VDO7fuKTVZVn0vW2mklO0/nkuv4bbcvN3KP4iaxW053ezPhYs\r\n9BvFRV4e12fboGQInK0XfUyvlAxDHZ7aaMA=\r\n=0R77\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.15": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-collection": "0.1.5-rc.15", "@radix-ui/react-collapsible": "0.1.7-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "975691d16bd1f5dd1a3337cc4de4a64b72ff453c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.15.tgz", "fileCount": 8, "integrity": "sha512-qpsI6I4fLxMKhZfD2hDU6SahhbyEBR2Ppq90vX9QxEqCIJj6h5GAaCvvQhmhonZhMPKVMqQF9yJFWX8E6CyUBQ==", "signatures": [{"sig": "MEUCIQDNKfMmSh461p5+ObBghGBo1F93RrL8bE3PJavd0hHoJwIgIWqHh2nZV7o6npQy7SlMPldbSopPA4Du89y7FiF13xk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQFRAAlg4Fsn1nWT5eIPo/Wi2SsXXhSAELPxkqueW5tC8WqGiVZZiw\r\nKs7XHI9P1+SIrgENx8Ej5KPlRkw4cF2v70pAG6g+xAcdNr/N2a3C+ffrq9Lh\r\nNKQNB98boHnKs1VD4BlU06fh85QZI1PInsrXTjm4hmSew8jPMF0PImR8MFhG\r\njJn+S3xTUfm13e9nKu3g9TOmu/9tV9rBvxvNJErDFuEYYVwaWxX1ctWnanFU\r\nK0Uwpz44OBVwbAFyEy6s/04DWimAeuRgrvQJxfgO6oyGMyck59UkMuq8aHYO\r\nv9/vU32wM4R/33RZu/ccG4rWAnHwnlNpjwU2HVOtRXs/uQoI7SsurVYMZBTw\r\nKpb6XP4ujZ78E7b4IqNfMCWUvxxPBtmrKNMQEnLoRdsuZPQc4wK/6gYnLI9e\r\n/JfVZ9mHsVtf3cZN2EDcH6aHEJjMDFygElU+C7GIdBQJFfHXTYSXIbmm94Dk\r\n9adtLWc2SlIAvgZUgwFXI7gBsipHV39jUXP3ZjfoypYKRgG9mK93J3ZXNYp+\r\nQ1X/4Xalt9Ugj9mxnrePU11te+vsU8xGAN9bam/j4tM87nnGjvy+k7Jsh/ei\r\nDC6Gr+FZ5tjQDsNwRWYcFzPlQ7ogPVaaMxOWdVo2QpXLFld06QGBQ6HxHGd6\r\nIZA05slAZpNsQQWdS5PkM392wAT5UcNbrug=\r\n=QxHG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.16": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-collection": "0.1.5-rc.16", "@radix-ui/react-collapsible": "0.1.7-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "44ccf4e2c24cbc0d73ffbb639661d6ef94f6f5d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.16.tgz", "fileCount": 8, "integrity": "sha512-Hw2zgpSiSwSaptn4lSYzx3X22EStxEj0uQJ+LQCICqNjDZJCAkXOOiONjpEAO1mq3bwPdadveIRWTpTqhnMAQw==", "signatures": [{"sig": "MEYCIQD1Yskf0V19qKeu3I2+r7lUPlHR+oX+fE/A7iajIIqZ6QIhAO+AUQD2ZnBM05MCnfd8BEULh0X0HNm7tBkfOa+gTmLI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTq5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqE/BAApCVyFHdRvZQz5eH3K/tn2RD/wbrPR/l83vwtgaSksnD3KBb6\r\nE0AMNmDoE4ohFPUJGFOqdh0T4VIu6U2Be6Azb683lhpTLkMev7I3NGZBoc+8\r\nOOjrALeiLgF553vjPp8Gr290XYo7XTyynqnYw4wDAv/OgeyFYt71iEntlsl1\r\nr3QUwXHOb/sHq0FN7l+i8dJBSTC5+kW9xDmHU6AZoiJcpfqTcHyL7nyxDdkf\r\ngX2sejjN8OEM+CO5Ll0P9CWLkyY6lyppszHSq1oLWeTaH50LwpUXk2L/zGXn\r\nlKxduhTcqMSvh6M7U1NqUDFZgLt87pxZepGnHcEd7oyhcPoKZ2alQ+uepsKI\r\nNrr31PpBwPrEnlttj7goDpbojNfWlwR9dCeWEd0G5ihEiuoviflROXEdVoRC\r\nLou3Bp88Y4Z4hMgEbmv6OOCHACOf6jOl5nfZqfiUBKPG5RfgpDS8BMdbky0B\r\nBMWClQibl8cRYTa+GPL9MwFFupuCCDqbbje3X4hsdGfmzZ+YUmiBPCTVrBvO\r\ntPExn7LwWgxh+JE1bvvKa7wNkz6x/t2qc2kpG8crcwARqI8KW6JZp4a7gY2J\r\nOrp/yuBPqfSEwLwInz9u0e42EfHx2QoMClAiz8Bvloq4mLTni7E4AeacF/KG\r\n/JyXPfqemefJX2F5uFLdQUNuR4eLYPf9W3o=\r\n=oVQg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.17": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-collection": "0.1.5-rc.17", "@radix-ui/react-collapsible": "0.1.7-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "85ec7a7eda4e0678358dbabe23fbe321b00db1a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.17.tgz", "fileCount": 8, "integrity": "sha512-VsFQX2TpPjXkobD+0Qree6aS26LTP8tN+3C/Rz+ie3Y6HlKjM2kmWvuWl/I5pnWCNFz/tn3bAwRXch7y86Zd5w==", "signatures": [{"sig": "MEUCID6jR68WNyFA2Wahmz4OyfAiPJ1439UB5U5pIKhH+BElAiEA2rH2YMdYvskzhij+mQjnydkOdYwCMO/h0qAzF6Qg58g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifhzyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpyRg/9FCaKy9ydCKfHQX63x2aCHZ4FRGKYOV+miZfaceuAHFrB4svB\r\ni/3zr5FEo3SwDYKr4OkduVsUn0L1qMQFdllXtEz7VLSJJJpMxJXjzQm5ughO\r\nH3fC6DNlborXHVMTFpxz/xY1FlJWj65St/0Dj3DWGFSwX9LUqjCuLxQzabNi\r\n18WAkkFjQVvTipg82QlkeZJGUtut8ZEmYsIIy2OYugh6nB8WP0ty+7W4Jl4j\r\nNEX/sl5Z2MxwucFxDMKh+qAClYkyuxeXiuBBhTsSai+Ezta8ytryefC4xzoC\r\n9SJy1Z3TT9c1kReKf93S7uA9fipFmdKbMEoQyQI0HzNqrjhs8fdJT4Mea8Gk\r\nTyRDzi24w/FSccQmqy67iWsozr0wMD3noWO1aRRw+EW+n0d5/BTnpf4Qn3+A\r\nAIl8FpErZCUVoqp6b230edrCsLSJYyK5bofEFeQ3cvVuOzC5AToejEixEbv/\r\nxYZUrbaMbfxgqpw4JjYEm7N85KgiT0XTER7Ih1dKFZDOL5tGhA97AvXqOfSA\r\nkxNe3TkhNbgY0dz4i1et65HvCEd9PI0jyZSw1wsoWbIEPSQLAHkPihG1kyna\r\nTwDlbIG4mz8EbuWGINSDqm/jse4tvEU+hxmp1FyrqZvLNXFrebGMt7LWtkhS\r\nXAfzS+RJ8MeHuWRqBqtvOPxxwNjw9ovoaI8=\r\n=rHgl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.18": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-collection": "0.1.5-rc.18", "@radix-ui/react-collapsible": "0.1.7-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "05e170a1d0f1cafebe36772ce6d83b4f55a1fe01", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.18.tgz", "fileCount": 8, "integrity": "sha512-Xfa3wcVeIf4uKeOMJGHumjEtgPUEOQtytFfw5WuFGAaI21Y0LfFiXWxQUMMx4Cuipp3sicsGdqF4UzGGykq7KQ==", "signatures": [{"sig": "MEUCIQCBe5Wf3TU1Em8d/kGQAYcjKsorWKn5ncz6X15GdaDZaQIgNONz52AA41YshVyC5Lw5oIKVE8HvjbZpeljOL8Ssrk4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8qRAAmLAG1Si1Wfv4vz8m5d/gqEjljkWWTEQyI9OXSa2MS234xpqO\r\n/umc4WJZUIMDVB+TPLwaQcaR9KrYy4qFEcYkeb+jv6gSJZS8vUWLAwhoBru2\r\nR26XUyQufsOyfm7/hfenomo3Dk/LyqJi30GCnhFGloRGCp4A9KbhWwV0pvPK\r\nm+ZDWo0+HDEDO6QAvP+HCodh3O68fW2exhEMu4Kn9SimFGxecwH6x8T4nsoq\r\nNKuJi7V9AL0rLTJIZWCgSYOl4ehCgS/7D8/PXiNNenB+3ZeDZSHIJEDPzmvY\r\nPa66XniFtgl44ECAALxOMHIhXe0n4kdY7gKVWwiPWEcRYC0C2ASeHruIzMGq\r\nEtl4AwnKjeYpbInMqRYbnF7x1ohsr+zcw49d+gF9WxQMc9VUvgbTuWi2O92B\r\nNSyXMGXY/Ytnt94Nok3rrWCboQDKCXuZ+I7iOU5xEPKCB5uM93Br8RlZlq0/\r\n7lJMo6HtlbKykQMW5LWdPn2pZtWXRBag+6ArQ8xotFLgrUitvNyOlbHeDTqL\r\n9XSjqme3r0i6Kv9db7CimyAy7sCrnDz1A5AQfEV96td2iJNPTeviwfk/Ehi+\r\npxcD4+mNozch4OCeCgK//7WoZvUjMlcJvnIN1PwMRX/y7yi8hVuwjbmd3mHE\r\nZQ+KgUCE7ssYBCAkhLDBVfER3Nam4m30Z4w=\r\n=mXb7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.19": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-collection": "0.1.5-rc.19", "@radix-ui/react-collapsible": "0.1.7-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55d57691cc1103b7c5404a8ce5759dc92f136036", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.19.tgz", "fileCount": 8, "integrity": "sha512-upP3BGYpwxDf7mzLeC3T2jY59O2yyn/sM2L0fCzWM4IHVaoaqiJMpgeoyqysDeQdrDQLhxm/adLEADc8ciJZxw==", "signatures": [{"sig": "MEUCIQDMJ8r2yyBTpS2P5ydDIu3uuZf27YxeVkTBcFXiOXBVGAIgB5X/9i2fIRc85hlMM8MLuCxqEMgREHhUGy4Kwy3f8pI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2V9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBLg/+IJE8qwizN+QjuuP9ZK/KRmwt8sa7fX2t0JbP2WMIFbgszdO4\r\nVpB5pGWz+Vz2L4ZeKmWWB4OuKDY1VYLpvGxZtAVXJWzpBzF2qo+lH4sVV+af\r\nUN1O8shuYA9Nk7Z6qfdIgsaJA6/Nzx96SaSmOdQr7wdonyoOTisSrUO306wE\r\nPvTypZvIG+QLBBAkI5ImssDdMAA3BZ/0AW4wFvUP324j/k6voUpIrllnJBR+\r\nSv9I14N/Cb5aVRGH/FzzaKHFDHOVKNqkoxUhHCMVROe2+eclE4HJRnCHvgF1\r\nDpG4M3xXluNBMykoLnYm9v0JxFgh4PIQ24YV6GZJnA+tpLlQ+lzzU1lJu4wL\r\nUsAak4RhcJecJufgecQQRWHS6N8rd/rBvjm4IUDPjk2EYVGDDmiqsutAc57+\r\nRInl7jlCsacbuFXgdRvmOe+yd5YirhJTFhF6ykoZADSijoe+JCa29+4Ppae+\r\nwcL4xT7rE9oaO92W1EfMTkO+FwgLeknwM5cwTstW5fDBbW9y6DfWjGsA3O0G\r\nlN7qsKBvU3BwPX3bT8Es4iQBt9VAZIKsW9zsCgvXqDfw20MpRAJVoyOPMIgf\r\nQGJ86nc0T/GWzgfxkfNkuJ668DOMy/AnnTLyf3Yv5OpI2YxJoTXIxaIYBmoS\r\nCdPfbIFjW3EQAMkKKE0nD+IFWdxHpNSxaeY=\r\n=ew5f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.20": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-collection": "0.1.5-rc.20", "@radix-ui/react-collapsible": "0.1.7-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a115209d5cc8ee52ad0ba5acdac070e5c15b8d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.20.tgz", "fileCount": 8, "integrity": "sha512-v7CH5sEY+gqnAqfj5+aWR/47j92MuUaJ8fPz4mvOH//KqOliKqgTOuBlzSwqsCR1Hxc/6NTyVEFqyU5x14W17g==", "signatures": [{"sig": "MEUCIQDNADtDqyI8KQK+/AWjr+koGm8s7F20JZcdkNuaJLuqCgIgTnDjeE3MVaQIU2Nj/z0fe3YfJY8/Gy2wWwFqApPEEcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoP9Q//VDziWYseZX1KrB0o8nLQ/eSyGJBgeWRB6bJfC0oYPmrfWaNU\r\n1Yc8M6l1XsoU82SZQkJr1vq6RMMlEVrET0LuHI8LOeOJ11Ww4CQYaQOiEZLw\r\n5UVZHxAMAtmEbM1UcXAFzbZi0+8Mpg+6eny2mz+fQ7jA64fllr6GLq2DoNJL\r\nhYZaZqR/gek3lLNCcV+k0AlH4q6/H5em/n8EKhUMsPyfaZLOOKl8TxoYOS3N\r\nbq5AaK14YLe/QGmgxrpltCOjcXzqJO18Xrinj0yNNcL8HekzENberu8AbzRs\r\nL6Mj03fLOIHTcXjQg5YockL/UByFe8byoB5rYIjvT5LwJVjh/t0HFK2WCfuq\r\nNpNcvSNcp76chPdI/4GUI8UfM57xidqy+tXem6drvxtLqq8OMCeFC6NdJdxT\r\nlsGzZqwUF9u0u6rX6HuLDKnFJ/IMzN9WCd5cbqAAHG4Zez/VJUM5o6xBG+Se\r\na+4tRfOdAO+eG9HEcOU90FUnaDEB7KkYnAiiU8jpzecnENriCbXjRvJLya1j\r\njxpyAAShKJU1Y063ARewJjnCqzU9x7Ag2hHVC8ZWBvZtekiYOgGHCevPzG7U\r\n0FUVxsPDAbICXo4zs40aZiVYwndehCGlocFaRHLWc4tHWW1NG96XMKuws15y\r\nHzo1z3jRDwqpPSpQizpOQD4E0FQNH33dyKU=\r\n=Vtoe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.21": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-collection": "0.1.5-rc.21", "@radix-ui/react-collapsible": "0.1.7-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5ad51c6c23bcc731d6400f9bce3f0a020dbf2dfc", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.21.tgz", "fileCount": 8, "integrity": "sha512-Rd5XnO9RkiDikDXwz5Qoe8wpsjQ1mWuNptEHX8wky+6VlmYRLR4wQkgoTe5Csn4hebub3uzHoGLDgHP60xKjaQ==", "signatures": [{"sig": "MEYCIQDQOFGJgwPb+Uuic220L39fCXNaiqtnbihOaslsklUA7AIhAPhuALKtl92Q2y4Xzm+jdIKoFpAJACQHbPvky6WBveVR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkJA//YbkBk7xV1pP88rCe981S0Q5YCpNigIX7J0TfCOT98/sw/EVj\r\nSy+Q2GaDmw2xpZZMJWk6ulboRNDGj0i33jUSJk9ZiV0Fkd1gAXhlbEwoRdH+\r\nnJ8B1P0m1IoO5/+5/m0PhvKLhIjl0AuDYzYTiiozt8JouuNLPAQURIGC/jeG\r\nrou1VFSojvzTn68/51HX8Vo1mqQCbncb1nZF0yGOzhSB47aiD0rWrEJ+EPok\r\nOPd6W1Z7PUO1gxlG3c9ooipryQyi33YCJoHOyqmZAUQQ+4O32ve8OrdhgBG9\r\nnnrUDQKwj8DCvNoTCzfU9N/yXLg37YsH4Ai1J9FweTTOwz9hVVtQKlC+AdeZ\r\nDciNb+5C9gmyo/LOA8xM6MGUAaKUyjM8CaIdt8f5nuUK337Wji9J2iTLBfUw\r\nYscayzkEA5ilGsZdqOp8F7Uh8HeUacgpScrUTCmTeT5juul7RmUx6nsTjBh4\r\npy2kNzc8fHUIu6btqa+oaRceYCKy7p3c3QOR/bvKUOFNdER82WigXWfiT3np\r\nvyU4/qDylW9DhcvnceZuOU+OI0rfbn2GPwNZOJoCXDDh1Jqt/QxbDHsBvVvp\r\nKRJHOwqUjhdmy18ckwuL75w4EDbWYFlLwdCD8vz1ibvZNmLV95fhUOG8sd1p\r\n7KGwoBc+l9VjbPWsogYyxU0xznhZ5vdL7QE=\r\n=/dOj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.22": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-collection": "0.1.5-rc.22", "@radix-ui/react-collapsible": "0.1.7-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9e580e2e690cd019ba9a5814cf4783ae0746c3e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.22.tgz", "fileCount": 8, "integrity": "sha512-hRHSdaKWSmBvBvUMoLYVzEaTSGIpse61J/u3OOd+ekEuwRTaf40vFtsuyeHbKpm9P6z9V0syS5hNn3t8EyeUpw==", "signatures": [{"sig": "MEQCIFEufo2yvj2hsLGFxWXMtTKRzy2BTz1lFKmJUF5Q5DXsAiAPLYcVGBqXqgiVEdAFpaX7XR6P4TnAo3PZjMEYkhVhFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5IRAAmdEhx3Zj1Yjpujbn7BRd8uEi04b3MH37GHd0ocYqJhusftFg\r\nA5fmCoJD73UvgVPPS0lAEtrg79TMMDcIrF1Pt9wWzaZvsrWxCCqezJ8wMqbp\r\niFyPaYZd4j0kjhcT3NAVh+HK8wJAMzTWYO3d8Lv1/wTlkPpfDYSl+3KynzZv\r\n7tubkbmXdIk3BXjaHzqxWBUSKSlNQ2gl8TmeltaG4gx+flEfc/48OLu/jgRE\r\nGh5lmPPXOhmBPkP0KYNUMqUtLYUs9GMFLxyptVQ4kzxVBX6q4Li4bOSdDfWB\r\nMox54eFpgyukVf/ATudzLNztV6CCcAGbQiBI15Wlobq0DqYSsqK1Bk4Me/Vv\r\nZMkm9MBFz23MB78eJx95npbPAifNYgPzlNzBySGi8jVKr4r00BGB022vGmrM\r\nJrzO5x0AcZM/GDtRKdZCCTThhfW6LmIIUIu3MtW4ngfPEHnHm/jqGqJEqBnv\r\n8to+l34n/iGHjQlJcfCV9u2dKqou03xCvb59UXevuEFnfqkmT6OH2S7fgCP3\r\n7GQgCf7TDnOKJunxxYLD/7zcbX0Xny7OqABORGf2N0jYt9zpqd8afFdFx97y\r\nmex2thAzxT6zU00BxBul94o8zssL42nEelLxyZ8s8SxWOXsxNswmruhjcnd0\r\nqI19X7B/kPq+5x+95eFSjgwbEi9T8OypFFY=\r\n=xGsr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.23": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-collection": "0.1.5-rc.23", "@radix-ui/react-collapsible": "0.1.7-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0a4312cc04e275645679c55846df50956e9549f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.23.tgz", "fileCount": 8, "integrity": "sha512-HdB5TE+NfSCGaFr1GF664+hn/Ea3DQPjArQ3o4iZf7HOgnETDzkon0LQJN+SMOk7a6Akkg2dQjrHpnlq986V/w==", "signatures": [{"sig": "MEUCIHBUwMno8lNG9h2mLhiaDvy6usM09pf7epZvNUpzlrSpAiEAlP/Q5eHe9c+ERBIEsctERo6UkyHHRUDaJL6iVlHdxYA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVVQ//fPxkM/KaCrQXM7c1HCiaHLzUlrm6A0ipRZgeH83LIxX5DTgF\r\n+3+08vRRmPPAL2SebC4GlNoPHQflCj+dDR1P5VrWz6fUEo9sAwCjEH09oMG7\r\nqbxIPz1sGYTe43HPSHw5jpkr11NpiNXVi4CWIpISWnugNwXhPgjJwWOqHVlM\r\nT7rnJo1TqyhEZmFXuItGEdt+XMyLrLIYeWDtqsrWgzHv1Dv/EFAOp38UCzrJ\r\nv2P2JEBs8QkyTH9H5PVFIJ2MJwUJvmcljLXeySD3K66VRJoOoan/1G9irojx\r\nzVrLkgxFVh/M6IT+kR8D3Uy73LzbvokM9IMwNZtiVK+SsMmx6D45HeFlQBRp\r\n+1jzkHeV/Dsmnok80L/kFD3kTTRGqFlhxmRSq2NPzWqcIFbFAiKMvjO7tRep\r\nb2sSV52oA+9k9RhZcbUFBB8D2vcdRchLSRl0r7EqMtaEvrxaOEBpl3p/mynP\r\nIh1yBDUslZgyjgS/VwbMAfYhWMB5X/iLDoBfzfrKEM4fz3+SC1PevJTIBVVy\r\ngw2ZaAPjWaPaBPfNYPwyelbO+bBQ8WvtggqUdoi8sSIhRfgThdzuPg7qqZX3\r\nyjZIp7YvIhAKipf+9jxCqioNvF8QO0Kcjwhya3FxBnm7f0Gg8Vzf9T/xyXJx\r\ndEgEpSgGSD6YUtDSOUHM0qDaZTZxaVB4eKw=\r\n=Jl9j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.24": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-collection": "0.1.5-rc.24", "@radix-ui/react-collapsible": "0.1.7-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a12fc12633bca37440b4bdbe0a648fef888f4ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.24.tgz", "fileCount": 8, "integrity": "sha512-z6H7xWr8VIIMdEhHJePSFwyUIUSczN1moMHvCVXDlX8s9JB9v/qSqk2imgMsMpZbZHLnQ2E4vphefkRvfUg1jw==", "signatures": [{"sig": "MEUCIHvUE07IHDm6HiADMqAm/TdNFyOtKhMeuYfSbqXUMA9gAiEA3DYXctXg5fY0145tf/n7hvCBr8MC3+P+fgLJtWqHaOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLg2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNhQ//bkEg4lM3VYMlwxhU1GMPr2GBU0TQ/Cs0PCAqG11nbUjc7cBg\r\nzjkZ5WbY40T2TkiSgyR/VHhSKcebif1XMUTIgItPflj+uEwSmck8vBz++3KE\r\nywNb8x3V6pGAf0k2U+Qs2mlB/RWRrEuzZuF4A4ksVSX3DiVGLhYQqHHnVF9J\r\nbwyvdEEpHuCmaURGfQDQh4iy4hglGZ8QpgJjzZlXEgozqy5Utp3wh7JUOlWA\r\nvQ7sZjwCg6xd0oLc6/PF8wV11Ht9TuXZewg7LxEO/8TFV26NmgqPpuMGqRmW\r\n4ixOWB32MF0lBOh/92QPJF6T1FmBAL7w3+ZxEDQ8jzI1odioKBSqbHu4UQ23\r\nPgRujiGHdRCt0iXcVt02dzDtej9hwV6gHYmT81MZxHFXaxOCwiAz2y9VQKYz\r\nFRrYWnOookAvBIj3PCqnn36rrGZm1DQSbfOd6GaCkpJTUBw3FwyWjVj4ccw4\r\n7GYWHACftpeILX86C/MZKFqY7OqJT7MX7p2C5a6b+ZN4o/GzMrV+kB3cY6Nn\r\nSN4Gm5o4DbLvwkdOPW7D+4/HiVDllQHaARGPmjYBwBMTug6cr6y7lAEafdG0\r\nOlEbgwvy9XbSnUbu8p7itqo+ahvbYyV90yhaC1uYkt1AluTmBYLj+vl8EwZh\r\nBA67duBN0WwUfkLcE+vsEZuoh5GNWKhP1TU=\r\n=C3Us\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.25": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-collection": "0.1.5-rc.25", "@radix-ui/react-collapsible": "0.1.7-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e6ec726738506063a7d7c99a20e1d79f8196c2cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.25.tgz", "fileCount": 8, "integrity": "sha512-PJgTeH1iN1hiSEAPABSwo6lZz5L3NvDCxTwqlrHTFFv5E+xlT0J0CA1dxC/C9WaZvPTcacuiR+HWxwfeuguFYw==", "signatures": [{"sig": "MEQCIB+9WArq+s2yGvBTIEw9cQlFF8KHBUA08wTIkd7qd4+1AiBIZQfYZcmLjIOEP0HKKCSwdNgpRcvK7P3jLiViVCNJQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpISw/9EAXsaYks3w4UvDpTFvzfWhq79U2jv+CN0IMPYnQSyjNQ1rWw\r\nEWJecxycRhsP+/4Awctay2rKCrFMg+bSxpUNoaaKZlOJw4jZGW4gyObj9AGm\r\nsRwDoWZH5n9boJcEv3wi1l+KyXA0D6pT+3YdlRbCbx8LZwnCdw9z6wp7r0e0\r\n096xWgZ9YGjBKPsyeSRFHSV+NH3JAz/aS2e/+kHJfpezzpqQuxPm69HWqCg7\r\nFd/Y2z/dz8zIk/NSMdq69Qky6O+HImyfciWwrPxmP6crPa1UR7xOXIJZ+/Od\r\nbmfdobj9wplC7fe2/EuXPNjJwI6Tv3GbiAkSV7NIvQjn4DahwAG2xkhpu2dj\r\nhu9O6uODlPf73eqS8BPT7Y3LKFqAXPZh/sY+GW74R1tj69+p53NSmySRmJzf\r\nGAEHD31nchdQt5N07lJc0hgSw/GhJvMoZyWaAUWvb7+OYWH0ekWe/gKHLMqt\r\nQPqBJ5douX2FMuvxLv0A+lWHNQtcJfMmIwDN4n7+gNDi8IL+OKfX3L538mqs\r\n0snTxahIBMqL5LcC4eAja23rrlLldL0OTrcBEammZ0y+WDgksyrFvqPXqk5S\r\nxhd1kqT7pp0hIqS8mL1hmpp0EHtfF9ix+dKATAmjMXGGMYzi2hPrsdBtquOi\r\n+c4tWk7gG9h1oY1118tIBF17RKpLB/UZv3Q=\r\n=9eYm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.26": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-collection": "0.1.5-rc.26", "@radix-ui/react-collapsible": "0.1.7-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b1fc3aebc4d5801b7bec6cd483741c03661a597b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.26.tgz", "fileCount": 8, "integrity": "sha512-hFL/VOYOkyQp9i06f5jPt2jXa7QbPUeyz7CX9phHywYyvWLHST6+0j2o6qIUwv8vHJprvsPH8Km12onh2HE0Yg==", "signatures": [{"sig": "MEYCIQCQjFRH/ta/Z3T1bJiki0uyLPViLfHtU37FrZ8JrgoW1wIhANYriuYcjSX9IlwmF3jD5Yf26J/ElzAXbjKIgyV8ZC50", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqaaw//ZkWK8/I32EexZWN0UegWEHZGqC1X0mmRWWMopkTHIawxDIWH\r\nSTcZShiJxrFqFm0bZwuu1fJ5KSHSuSB8EEb+VZfkWSS9uK4yPkzuEbxeKKuN\r\nUNgMPh3osylVTTM2H/uezR0o0Lz9Wk4O8dXKnGT6ZfxfbYe5vRhzbYqzvBmq\r\n7bb4meSNWJYxxFDKELNsnYBgu2r7hK7RLlM/FyIboiKAIIWzH1X7tTb2D4cb\r\nHtLq7W3laHFIszAaZTaNjSW/dx/9oqtNiBmxjHROujbSunMeI6aQChiLh4vc\r\n6CPDmwsqbAFnqe16pPwEhHGuBYSjsZatPbzaZcWY4qRSVUe2I03NCHDJAo6K\r\nwmH5cPWZ9FFFFDYPbauo356zpr7T27XoK1aZG+i2oaNlDEkeBsVuvsjDofai\r\nv9/mYoIqKXikyCpKuzWESlT1pBUP/R64mcrDD+nnCpYzcESShEJ8SPCaUQFh\r\n6sQVBrzH8whm957Ifjrx+7O/r8q1zIkq5QboTxMrwBtR6/h7CYem75+G2uQO\r\nAs4TsvqiF47cQH1NWUCg5G12BgXU2K87CVqo+kvcsBL/AYV3Qrl4yTNE/ZPd\r\n2lUw1GaCBAYJoGnp0R/tHPigLU3ZDP4p6s9I4FqCNxfVYSEt06cN57HI7bPV\r\nnmiriCCxeq9ORsYPf0WcapLU2GuD5yNdTwo=\r\n=2fkF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.27": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.27", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-collection": "0.1.5-rc.27", "@radix-ui/react-collapsible": "0.1.7-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dadf700a71df5b322fa826afa9770579c863feff", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.27.tgz", "fileCount": 8, "integrity": "sha512-i69ap14D5P5GDM30cXA948EZgOFMvJKVgQ8IKKm4e7Nzs6unLVdKxQBx3UUbOLw3zIo5c3Odfx3BHez0Wy+U5w==", "signatures": [{"sig": "MEUCIQCTVGKYa6RX4pLTbZOBQqjha4uD1RngLA88mR4Q5EYh4wIgdBltKJVlrli6cVl0tnvExuMjV3qullIi90swxZArzCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ0yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq46w//UEKs6H1NbLT+1ivBvdA5ABUxxPSWuF18tzZJS0rO6IaJO8VD\r\nKyH0n9m3aRKFTg8J2NJ627FRcUxjs7hRVk+HSMwFO24yuSIi1I5enqhMl9kd\r\nYEnbXNBpV9eNO42RstJkAb6aNnNqDmWgroA2YAffmJeUV9A+rUNIUYxpn/fA\r\ny6CmWVZh6DEL69+eFOAN3y2dGFhm9XZGPRBdbvHKPsVo2gW0V24oz5vpYgUg\r\nmMQcf1+f0DWAWUhCm5lbbKWiVoDSiYmm2/a8zTA/KrwaT12U83tLtLsrSP6U\r\nRDJiuFRKqgrGwAY0OhhNp4YXDIxzNz6qo3OB0QN3nGYbpwCspYuqP/eQBz8i\r\nvTI26IULts8BORTQ3o49O1PtkjTaomsSRIwmgY86MMnCEVM8lh4OJspQFTOa\r\nT8JBXzuSRQGV/pV6xpAVH1c2xW78Zz0Z9hhJGRTZiw2zEof+UnX2e14Jqt0Y\r\nVRGfD70o0hvmjxCw75KJfJfeNiKYSApL8ZN84vUAszZLThxdN0vwS55iLT3o\r\nngOO5fKfoTeZaS3iZROz85zHg9pnUPMzU6j5x780pKZrgRMIoqeVm2toP1cz\r\ne1Ar3yY5UJYoBu7QAlSYefn59bhz8tFKXWvyJVPXv/vQAttE5kM22FmnlwHR\r\n7v+v519ruBYP3NipkzMXcq/QlntzrVYBNKU=\r\n=J4H9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.28": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.28", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-collection": "0.1.5-rc.28", "@radix-ui/react-collapsible": "0.1.7-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0d52b230bcc1552a398f45d2bc1d4a251810c712", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.28.tgz", "fileCount": 8, "integrity": "sha512-9a76q9ITySKgu7miCfMUONuB6S/zRfHv8u2vjnP+GwrpHhQ37YRfflVL3XIpiSpYG+qGbnX6smGoF6vQpwe/rg==", "signatures": [{"sig": "MEUCIQCWz/M2H1KAFIEgiFIzHh/q8uGXKxzJJnX5n2nApHTycgIgOc+Knc+OwxuOuTFPNKNUs8XMJSbC5fv0a3KySK6Jsck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildMlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoi0Q/+PKpv60srSU8LvefENZ16P2d4l7Sq4DXpVXBI07E99HhBGP2c\r\nnREmx3Wdi4hb2CCXexlugGyUtZWcqStVTVBZmz6Nk71BdRj74MGJFBe1+8b4\r\nPPDV0r2M/resvGcGxuGJWLxwTA76NmQMEtNeOKGGBdGpf0SDhYgTGqL08tTS\r\nmo4B0+fVL6ubNlJ36Pj1YRcJIVqJTWBCv0/tLR6tvD2sd+YFu9oquibApe6C\r\nd/w1wzvvWVp7FQkatr+4e9toaONC1KBwveHX1V9tFuh+9r2xiHcyy4qFC+xZ\r\n4GmSIjaJgFVWqIN3G+RvrfNinToOvEcgyeAnhXqmybRIIZVTvToCfrQC0FCp\r\niBNdf3JybqMOeufEEq3fdXvnJlMh6ZWcc/aOV+DgK4EOkkSkTh2loVkxieWr\r\nthSuhnfNVMzhlCKP2rEhlatRlMu516a3eEb2zSa3PRboqKtcQnYCL2eO+/KS\r\n6ZHF+v1/o1vWHHx0IKKmbzFLWIpKq5uCTl7L56z/1n/K88+DRQSpYnXr4C6C\r\nBGKNq/uzbeXP8FI2FNEpuaWEIRTN1bfoVKJMFZ+h3sJfDaSoWvIxt5wUVQU2\r\nSXPqIx50YS1NBNrxLand/vj6TKEAhaMrJc+mfT76PzipthNOdXe4m5fhv+YC\r\njuBbl3tAJHmHUMa7WpI9DSTELnVaTvwzBoI=\r\n=UvTl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.29": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.29", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-collection": "0.1.5-rc.29", "@radix-ui/react-collapsible": "0.1.7-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bb5f79fe4add8274a53f299bf2413fd6c972e405", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.29.tgz", "fileCount": 8, "integrity": "sha512-AU3XrD4FdQrmkxk9O90B98E7tsXcnJKhvhjxPD8H4FMaSNbe2AUc3HoAIGi4uy/FUCCjlH7rSjAyW/LoLCgIAw==", "signatures": [{"sig": "MEUCIQCLlm/NzishTYq9/DzTzXw+dpz1VdRQ/C7186zgHmlpYgIgASaGDkUlmdQJq+Nc0d1mrvWCEEk7OtBAWOG/c8QXJ/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoixQ/5ANCdkPNdhh5rVQ3s4UsR6vcOmRZ/Ddhw1D3tbJxAxnoHnEdH\r\neSlsth2hrdmk3yhWlnTab7PenNnMbQ8PQ7qeVpE1apu6dJd4SJLNrGPwAxl6\r\nyDwkNOoTlnqZW1TQN/uAAGVg+H5HUtxJ9cyQhVYwMGPpgySkQP2Y81thnm7D\r\nB9AlqhY3AjipE1h1M8pc7eVVWEZoUn5+Lv59BVr3o54xI3A1w+ot2gycxmkK\r\nR3A+YltuvSrUQkJYNP87QtwtagOVaDqGkbGYLvIdXH+s/tbebJLqy0NUuqsA\r\n0pN3wawuog4oyYbj/Fswi0ANBZRDDUTp99PY2X9IEnUfVAIVDAkKGF+P0RMB\r\na+MZwWlOFmYzcN1GbDJBg7HCiB5jApLvHvRTsNPZyFLnzgQTudNeCmR26BKs\r\n16tREyLzxED3q70giwqCx8dyCEUUnS6IcBfnMH9GCZlpW3uNy2wbqDSOWzlf\r\n5xTZYYblLEeAf389Hu+gE7eh6JQlN7vo0MgNmZDorsCW/AB2dzlTRPpTahpR\r\nKjgBk5G16bHt4wvAf9znC6hBtdpSwPkY/X7jtiwTn7EzS640B5STDy+0S5z8\r\nzJdYC7la2EBVDhbbQr7vlmUViwX74CEsbQpoiSMAX24/TNourmeFvoWEo/4j\r\nhe8cK+QukPZPnxpDbwDbii35EWTwG78MUj4=\r\n=qChx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.30": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.30", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-collection": "0.1.5-rc.30", "@radix-ui/react-collapsible": "0.1.7-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2dd02525774ae4e1ccc18c80eca8aeae30ada98d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.30.tgz", "fileCount": 8, "integrity": "sha512-blWMZ4YnYNM9eiVCg0eKCFIiolyDF9nFczTx6Htsj+1rTqWYB0P8GP8rj5FF7FdzFwe9ciOjAAiqaQP8BdAfOw==", "signatures": [{"sig": "MEYCIQDqkFqGOO+rCjzNUIsqnQbBLiXnWvGxT+64aJpnXrj8ugIhALGPVONBEV2+Zp0VXjOLqhbgZGrxhK+FfZc+xObra//P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4qxAAn9aEqYzSIZh1gLMRuE64/ESjrqJK7q1inRXuigFXe6nC35Lf\r\nLwfFOr9TsxozcmqMmshng6sIrG3C5+BzgjfmSa9vkCySnEFp3eC02dEXRvlo\r\npT3uwp5n2DY8gOX9GiWR60kPxGKIQaqS7Mk7rXoYUPgHsVVPjNJp0WzvmZ6D\r\nsecXsbCiqWoYesNHEvHtazUqUT78r97BM99Jj5QxL4vz9/8BrzB4KUaWNJgA\r\npaZntES464mzeQRANW2vEa8c0hR8+aEJsrkSJYjVbY267YUPhKsGXyYIIokh\r\npyGIvG2iLbaQGMzxOpJ68SLOC7T6v+barrg4AB5nEEaduxd81qoQIjptLfZR\r\nzE7NMg9tE90TMWSYKe3FH0H/O+lZdpapjUDqbgSdM1RcZueEkJ9e24C/iXjX\r\nTe81fBhT7qrA9+REvPWTC5KJ9YNXOWHjx6l11y3ZXJ5FOSEZ23T2mA6LA72J\r\nehig2Xdl7GPF9TDrq5c/4g50skDefuD+Y7j9maR2SpLP1Lj8piyx7OXvym8m\r\nMBDnAUYPHLP8qi/cSpbeIW6ODowSK4AJn1RW15URW3FVpH5sbIdIN+5ftEOy\r\nQP454iUYs+x7jp6ZDD390+dwSvXQOrw+1raXVG7lDWkuTVSVWZTuGMIrJb+o\r\nqv9dGKO0Y9XNcMSiIf6SAsylA52msM1X57o=\r\n=lnny\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.31": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.31", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-collection": "0.1.5-rc.31", "@radix-ui/react-collapsible": "0.1.7-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cba32cd8043e63074023071877eb582055e1a09a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.31.tgz", "fileCount": 8, "integrity": "sha512-y3ax4+7s+nAxqC6UpNSayuHfPuim1+QQDxyC+hE/gxjXYP/Kcp8RdoEGMIHerue70mK7ophlkFXmANbLuTpyTQ==", "signatures": [{"sig": "MEQCIB5Q0QPycztls2URpIK4iD8CAVdUDwB09KZ9GpVjdCzFAiADZws9mTimySslQtc3D1nplfUBeVkCw4hpMeJxMI0E9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3WoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSNA/8DBj4J2zlfIOt7Rw1vLv8EtN521wCAMw07RPYVux/aZS7Xo/D\r\npYage6Oak8+O3NPdZH3eWJQtQ45cHYq7IGXU/+qbJYcz2XAUSWj9oXuQk/kJ\r\nmO7LkOAtp6lUfs9GvH9Y10zwTn3g8UW3Yb8W6VBq7PrgiP72C7mvEgLObUwX\r\nk+SrDI/ttmw299qEw3TzG+m7SFavJ3Cy/Td/eve6atKhj3oQeO3k2EZrdqvV\r\nM9HIDwR2Xi2daS+FRBdO9fZCHnarVTRzHocFd82+waaat0Cr/2n4FbCplRYn\r\nONxLw9yBhfVShwpT98/YDh+oPXq3ZIVVavK+kb3BojHI1kg47/fuo7Ulbtca\r\nXSpCo/uhs9bPpSNhmkMNKp3LkOzRJ4QCG6zxd69Eyx7v94ufmowlLYtpUOUl\r\nBocyEAoW/PGnOxesBUhTvd6o4tFu3cd9RbqQGy0p0v9409hbM9B3b7SzRnFn\r\nEToV/Wc/JufQ27k2cvcnN1g15UouLGOgIe1Dsf+7DCE5uVnD3CvP4mI1pMlb\r\nQZBG7zvKSAE7UpLdaul/sKgnsreK/FVivl9yPcm4D6PMR+/alCe+rowfgZk/\r\nInNZ0oxVU2J/O9RA7codtI5dRy8S2YkvRnCwoOfn96mdnPOwlaO1RPG8caxr\r\nXfBLOnENrehUbQmFkKjJanJVLLSMeod6VQA=\r\n=fVgm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.32": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.32", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-collection": "0.1.5-rc.32", "@radix-ui/react-collapsible": "0.1.7-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c2da212aab70560c26d55724016a7047bfa9956", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.32.tgz", "fileCount": 8, "integrity": "sha512-UlZVnPn/Lrb+XE5HKfZqJbypPxnIXTMx9IWrm1/OwZgQOvXrSO/yED9wo40bwJuRs8RH1kYRnp/AwEtDhwr2rw==", "signatures": [{"sig": "MEQCIGRtLGs4eViobJOnaUOYoHQ9iknBMvgLDaI8DqCCa1eiAiBoqVqF5BRKpFUIbXd3KyhZzew33Pgbvn2lloQlCqd77w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVTA//Z1PlXhAXDkAK4trZMAftQmWl3iuk9/H9SZgHFmoiu5NktI9n\r\nMper4k4MWo+VDBxHqzswiY+yi4ZfW6pOuXsBECoTLJ5mxK2dldkhsCK/NKGR\r\nxEWknEZUSsVaEK8DBajTMyG3HUyawyTLhYNgY/1Go2X+QtMQJh6JNhXCcAoM\r\nQnT1ANFLpl7qgkSCXqxpo94+DCNTLK7NCOd9O6JADqSbCcVs4ZWxVG7ShnHC\r\nBx7JhHSBNEkqJX/CBDFN4ikzJWzekeLHkirWjnL/gsWzEOYvUMYSmhsfnVFw\r\nIOF5S2YOAAJyKJhAspHBFG7H6TTgx2KV6nrp9dlJm/0yFmf4tylaicqAtGSV\r\n1JabR9GMA0OWvBBZYcvVmBeX1jXlRHy+8CWcueW4wYywBX7L5IBirVQaXcHn\r\n+84S8x0GwplgaYuJOrDtsCkCkIs0xVVZsg4qAs7nS3oQyprCycLJqhJtY1sj\r\nYJc4uEIitqcmN2JPipFuxgvoLcFvon2swVnEKGd1gy31RsYdQnVDqWllodsp\r\neMX75R8j8Zv37J3lUHdSShTZqpg+pGfi+dlaEAOM3hL8QBtFqw5ofwRvlM4l\r\nufbIcEe8aEj1oaG6kOpgvTpiFMCLgzbEuKPK1VaVVOoW34U4WCwZyvQ0xfya\r\nxGMv01Lih0u/VXpwMtZn8T5C/Ky9t1eghe8=\r\n=jS9o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.33": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.33", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-collection": "0.1.5-rc.33", "@radix-ui/react-collapsible": "0.1.7-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e2ce5373854a58b93b346d98b1488b3dcb5da60", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.33.tgz", "fileCount": 8, "integrity": "sha512-T2m0HiBQpUqUVHrNF2sBSSy9YH5MnsJmfDNCSwlOd8RmMeRn1kYQVbzDxil11gxSAH3Iphe3JyXSn/RCMJ5iFA==", "signatures": [{"sig": "MEUCIDdNWWD57UW7rnM7niWXjEG7Dj4XyuE0JOgu7ydw6UM1AiEA8Y4GmTd903jztmD969QI0DAm9hi92C39ET0ZrJk5Pwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpynA//UAqpI/mkR7YlhiLHcTvGtEqVzhWz5xmSPAnh9StO9V/8VVys\r\nGCQtc+cK2zjKEe63c4rFsb5ECuGxSFv9+8RN6zLISEVhkj3NNzWXVNUnnb80\r\nh/QOhFrHSFUGzQ12L1qoLOTFhfYXXXard0tPYGv/Skd/IzLyVdSwDQMRSUrQ\r\nU7FX5bkulng51Tjx5mFHpR/IgpMHRcB7vNgx185N1R8iDBjK7/Kj1+zTJ<PERSON><PERSON>\r\nkMlAMSWvLyqjpM9iAWXwydFY6kuQhhDcfLGjeuDLBZDKmyuWczFhSTICPYPS\r\nXMEFDfK9Z8P+A1KGmDisaLBRJf90M9+/PBC4XJ/Y3KbRU1UDKLIsaskwiX5l\r\nprmHDoZOuPI32Q0NQLV5pxgDBIKK1AwyE/IlnEokMks7joV5IeH7koJxqYc9\r\nqFdep4RCx+Fd3YwfrtHx5udtIcVLwU7Qu0sO6CmrAeGr4XEO8KEt3Nk73bvC\r\nYGn4r5XY+BWsne08fERKg+vOY22cC48Lax5+T+PBC+BeNJRK7OvwNuWfi2KH\r\n4W9NjFO4q7aIJDugERxWJSs962APteuqSe3PuMSPEkVJXNwMnYvXHZ2qZpD/\r\nD/hN5lv/w1CjTrN+owmB6Bditr+ZVX4pBW1TIX9axjwsbwXoYHumFhydhG1M\r\najh47UsryCrExd+JKCwX33BS8vE3RqQ0Cbw=\r\n=qm7M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.34": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.34", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-collection": "0.1.5-rc.34", "@radix-ui/react-collapsible": "0.1.7-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e1a7005dadb57a6600a9e16ebae8ddb79225b078", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.34.tgz", "fileCount": 8, "integrity": "sha512-cGWeU34K+5bKprf1qpzGBz5oibrM640xnswk4faskcFxAf8N8D4J2LKU5VMe8V59FdCSU7/TsBXYUlJNPlzzxg==", "signatures": [{"sig": "MEYCIQDeZ5d77tE3GmpUwF34oJu36b1BdoCd0Nqh41AvhIzORgIhAP8gbaEQ7AWRYHOK15Kk6pPfzSBtfJ9JUN7B6fhTnGn3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxtQ//Swr/T1q/RfJlZ1ZZs6oFx45SPVrESTaH9bSUoACLM7jrs/qA\r\nD/6IwiVJ0bFzqK+IAn1RMfIDRrkdekYhcizqOyzc8rdmu4REMb3qug/7XE3X\r\nDG1bM+Kn8J0CKhWLFl1LFiH614CgfERXgsAYePTOELuOS0d7VZBpzeUeal+J\r\nyVZazEjkBNL8DYVojaojYcRJAyDSu4L3+d2DSyFSeEQJG+jG1IaMDorOwmLh\r\nJnqBMMYgbyWu+augn6Bf26aU9PGyHoNEnLJuyS0ffJTt5sfpVBTQ/SsZhPmp\r\njqYUzyvC5P0RjcBHWogaKkkJWKFbwwsi9PRwnAjS0TO00tBjpYxpXSMf86MQ\r\nM3vGiyMgkspZWt2uNhP3Wa5haESrdBU2Tekmc380SHnKTX9DQcGR5iITEfFO\r\n99vhQ5igI/A+DT4nWGfDKlKf+POJ9u+Shj6pJBq4MnUWvEFgH661Yxm0kMDC\r\nyzd8w/IRCIFbTr8z3jQGyXZ5FnPGXKUscT7OEtISH8KWJE8NYSBK99LLucEK\r\nkwii/mu0SQvnVkw8ebDEacYdl6vwKeB+091nwoF0d7dqhcxaUrHEceCrhT++\r\nFqHZtKM1yCeUrp/2mRjMyPJ4KK6akNJXLSSJOjpcGNRf9iVTuOZeo0QZH5ov\r\nnzSf5YP7M1Q1mQNv2QCpLDhIwVkiZHYwjJo=\r\n=EPb+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.35": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.35", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-collection": "0.1.5-rc.35", "@radix-ui/react-collapsible": "0.1.7-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4250e4b78d371710e542cdaae0c10a2740341e32", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.35.tgz", "fileCount": 8, "integrity": "sha512-kO<PERSON>HPbirKuOuzYKR4PPrkNZMZkkXSQ6Fg762gyFgwnBV+V+WSp4oDcn3i7DvfYSoGF+uK7Yk4pfr4D0A/QcfmA==", "signatures": [{"sig": "MEQCIHF9XZT/qUSeJ0kUZsrQVY4adlenT1442VLwIrLln0IKAiB7QjyXwTgsRQw59NPZ70AjWwOQSH2+TYfX8zPsxOUFXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7sg//b8mvyMO+Metq724bxrxDazfHYth+RbSQM/mvlCgk98qSFGuw\r\nxepQYgWZ8QSRxd+T4LKWovmmTb0YlQubFxrwXzBgV9vFG1XfcStx7YYr0HSD\r\n7fLWvvlXdlbRGpFRr1kAlDqSmwEuzU4vWIKbF0fFRLV0NTQVxAgl4qcBCFjE\r\nd3O2hyoqYcCjli2+un3htl1LGa+7ffV0SjYzlwYKVIi3RDD2c38fPbkDp/FR\r\nB1at6R+1mcf0iYIP1jzHNwuTs1qITWOwIWuUqWANpwSTk8pFiaoYpusUGJFb\r\nQe3EgWlvHEgoLgGFArN17yf1qPMGCoQRTDh9PTzJdkLIGCvE2UE7Lg1QADKH\r\nehqWP4cxkdeoiOi9cKxDdkzRStrv5oQUdr+VmNVwUDyCMSUAh7LkOFpbhqGq\r\nJb32sJWwhntCjG3FUGCJO+ffLDHr8fHsL5wcSuPnNbcsUrbe0naTS7HWEpqA\r\n/OMluO2gC2btK5eecAIWcOHbJ2CS2ZbkehIF5SL8K3evhfu7sYlnKA/9B1oN\r\nF8WO/cc6T7em1XP+904dbMbSbcv4aCcAkE0yWUIP6C+iJMZevW+ZGn8zCCYL\r\noqcSed4iV648uts/Dl5FN3tgq8k7iTeGuP06ZxhdHp5ZJbkDLKsr6TEBYqXT\r\nCOcGqL+vnQT1rdtsqz9xJW3xBo3upwdwBc4=\r\n=QsY9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.36": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.36", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-collection": "0.1.5-rc.36", "@radix-ui/react-collapsible": "0.1.7-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e6ba852e4cb95ceb5bc634ca8871f77d43d8f60", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.36.tgz", "fileCount": 8, "integrity": "sha512-wwtK6vxYXaFdaMuGXW1eTP4r/8vV9RRhg2yIa52GxqQMh6aiuWQEFGh5gfE7G9ur1G343ad/0a7ZzHQ+8XYk4A==", "signatures": [{"sig": "MEUCIHBu8aZpuESF28Q186hlyBh2a8N4F/Y8wDo8ltwWw2mhAiEA9fngcPwQn7VDHMTiBkn8whDr024y1BvnE09EjE2Hez0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0H0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMVA/8CplnTXILPc5OUTX/NNicQmfN+gBRt2gA52nqiaR8a84hJgvR\r\nmCM1dWsAPZ7K7XvnAnYjooifppZB+ozyiqPBQyhJcK2qEd9ioQUF/URf4VuF\r\nQPaq1Wv33hADBE8+bVKFc8CJLsUV5YQXzs36QChUTFahU3fgibNmA7ilmZ1p\r\nx/SudCTyNvA0cBXtFVEsNkaTJwb57n+HZ+TjKj5uvKSQZsMkjybWW+q6gOSE\r\nS/d6j6vcOIZZhoBGlJdfzyOcBcanZzwJ57hNURisjMJDPSvpr6S8cS1xlBja\r\newPfkNH1S+lcWI0uzYtwV/CndHos5pXW2sJaNZJn2NFlTsriqjxEiaN6Z67F\r\nVfXEXuZGISggPi5EFyeMlNvjjVyuqnp5EWZgMdFieWK9YOeyslZo4Uq1PNYZ\r\npUTezaeyHAwalLxBTR2MPl0eqpvwp5bFuRy65bITKO3h3EMajpB6XpREsNpZ\r\nTis5cd+uSE3KcjziCvaHSZwDtKfmiJh52GkbeUq2NCujHRF7SRFdExDyBhAO\r\nCYS2BW9imsSTGEGUtJUV+LJnrNdcv8ohD5jg8NQ9p6MHbmvPeFLf1ToYQbuN\r\n9XCvqLAVwVM+mrvoxb2tUl6dWQeplNVs3oWVQuycZoIpILkwLWwucAclnHDk\r\nsZYwEfbEg1fQwGUEobzdQVtjBd4T1WdbY8o=\r\n=5yX8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.37": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.37", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-collection": "0.1.5-rc.37", "@radix-ui/react-collapsible": "0.1.7-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "377e63d23ce5c20668ccdacffe0d0a3d0dce09fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.37.tgz", "fileCount": 8, "integrity": "sha512-wcI9yVIPc3GSS1SozMHXwCd+eKQKdlPCSm5rc03lJg5iS9aGZrJppWPytFYc8VTcE3QmwsYRe4dHHQesAZpYsQ==", "signatures": [{"sig": "MEUCIQDbFxCFwfWEPX/7YjW2BNCI7MZ/UfM2fDr7CekJxRs7CAIgKHSRjp1jkBpRSfecRLEuCk0TgZ7ZbDp+xElPdUHGrYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiZw//Z5lHCFg46nookSww0EKFcGbtymC28RpOnZVfNGKfYk5h4Hm5\r\nwAlNqPJWmWRU7OFyebEjAnGHTJPFHw8M/OGnd5mODW9jMxfY9cTK/fdgF8Fs\r\nGN94dY/wh4B/t6/xrItTGA7dXiBTdX87N/6lYa3llsE5owLrqRzmbziJsbvI\r\nd1eDSmehkYLGHhkC5YFobowl8qdBmfvSFD1E7I7oGAFTRBR9xXO9y6LWeffn\r\nuyQM8vZ6oxVA4sbUYs7HPv05E7I3hwP5DZQRdLpsJucAM1o8kowRJWeH1t6x\r\n+VEH3YZgxmPqJ1/TOk8rf2Ne/rJESDkpUwovXhkxasVuK60TdaOQaHuTU/uX\r\nNEXTqaBEf2TVu53H5zvGzHLRABC5piupPNfqBNkeJH6WYklmlUfF5fRc62y7\r\nhBlbHq7ZqdchXM4httzjfG9wJtzty8v8+c/iJk/Za6qFTdWHvsXUUPty/6jQ\r\nIDHJVktcltsQjLO21gUsleGCyISoLNdVF/cvv4RKitaG0EphIXxs0h8wib2T\r\nc6EkVHwT+daGH1KAYhA0M8SBvke9BNVJb1Jl3tEJ+w/vxgbSnT6WcjGWf3+Z\r\nP+LZ9zg/CfgrtcEYtqVKU4ums3mXnI5SXXkC9wuJuGv25uq7m8OirqlUD4fn\r\nlAiy0svPvjjAGuW8JVnMnLrFfDGhhUS5yKY=\r\n=/RiP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.38": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.38", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-collection": "0.1.5-rc.38", "@radix-ui/react-collapsible": "0.1.7-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34de910bcc3ace3b037d00d69cbac5cdca69c704", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.38.tgz", "fileCount": 8, "integrity": "sha512-b67P+SxkSLMnHbw2kYsmEZG8ABiJoCDRQ1l2Pn1rtxyCUteFhEZs1lmaDhjmWmfYFrKBlFvwXRnFF3+7mTi8zg==", "signatures": [{"sig": "MEQCIB0r9dGrcObNGUhZCpDDbazJ8CfF856sDi9O9UuM2UlIAiADx+THxdpiUhjjUwPsCdrqpidblddYA/mSUtIqyKcnTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZGA/+IjsTHIsuRAr3XjpXAbVv+sE3c6pEjPCFga1OhprAcR4ssTp/\r\nbg0s5lONL5XeCtiWUAzDqVskNjcJkH6lM/2HutB8mz/w86kolj7c+SJ3GYAo\r\nWpG3bkzPiT75hULuuYh9L0sGVgTsr4/KMdKp5fP6jUrp0ztuE2P2WkscP4XC\r\nbJHtbZBi+lcLSSWCHoFxu2ynHvUSg8s9dW3yJJxXpicztzoIilOXWx0tAXqh\r\nCcPAnng7LCRuLMBh58pYaffHb8ZJfqq8WP0vVxa71mxDOh/nauX63zF64bgn\r\nZQ4vp13DtIRFfe23b2RljbaH+qzILl6JVzKJrOtAGPfdc9eCUYGoFYR/D8ft\r\nlZTTs7Zurjmisc6MkKYsuRSGTuB6v6vuA31qLMyEAU3ckSzKuWV476EPSdU8\r\nyF/S9IQ1IRc79AfJMIyFs6u2jGgFFg6sUS3H5+vWuNtM+ZQxXVwVNynC+Fn1\r\nNSSo/IvuAgHBBdt0HM4SXT3NJPYjMs4rR8CdXk+Cm7zWrBo/YMNTLdF2urHI\r\nWJPj9K5Z04D2syfkkay8HynKk9feuxkDuulxxNXtaD+ADnCj4q/qbtzSpR6x\r\nAfZmCD8L/LJMtAVW3qC+RtP0KLs7C4SYgIOwm1eG2XAk3g5w5HxKIALNvOvn\r\nEwQVLyM6oGfIVnqmO+u/L1yOxMPRjRare7A=\r\n=vjtt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.39": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.39", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-collection": "0.1.5-rc.39", "@radix-ui/react-collapsible": "0.1.7-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "325a54526708335eef5f70f0323c5eb7d90c75ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.39.tgz", "fileCount": 8, "integrity": "sha512-mvtTv6fngIoG4Mjm+UMo4jKn9dZLCu0dqMX7cSFgNf1IqAYBBIiP+PJEkvseEqxTVPLcGwOfxvw47a4G+1Dszg==", "signatures": [{"sig": "MEUCIQD3247LfOY4whSCPGhtmGmTVfxWyRQTgKW5z5Rc0dwYbgIgTlxmz6QprzLdwS1KEwc3egSmhtzZZEtGGXqaCGhUVEU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgYQ/8DCqTVFhIm0Qo4lR09V2zllmhbdH49aEjM70xDCt5UGRberP2\r\nH0i8fS5N6FjfWIC4SayMVCUY8Ke5Gw3frs3ww7QVTkt418cXMbGpgvhpKV2F\r\n7Hu9FfxowN6QRZrGgzicyCGmikAQ+aTcmnYdCwN+S82aet3MXNiHyqGY1vvN\r\nOpjKZAc3p8uHBSt8lIGFP58dBnNYsiRtjBMbzICOVdVhqLuqQU6Zu/A6rS+D\r\naCI8ioApxAMciVPLroDtN3Cz9uxfp4rfJoKH10bxlnHml2GGKxCQ5nUJKXIu\r\ny+B83gGhJISc11YVeJvEiyNDsY+eFRCLuujv366Vr2p5B9drzi0N5YNUJzwp\r\nqVZauUmr9isHdBh099SpTAg2r3ld2uPYJqubg12Tt6oLT0bZSlTDEkkAEeA4\r\n3xXZgxJeHbXLx38avGgZ8BTshQ1zZiXaE6zTh7xXb4KBFr195Zdgt0HtSF0L\r\nURBp3B657Jje5y4U+gAi4LKNhFvhrXLFPBtfLOrjYKCO3m45CqFvthydfZN2\r\nTimDqkwH80Nma4AblyqmpV4Lnnlr9CvpCDDqiNsZVJHL56ChckckK0693Q/6\r\np3sddTwTfttn5WDUAWJxnDWgnYJfgiMKycG4ntMqFkoWHQbojj3vKdTzf8Qb\r\nkrDbEwyfyA6GOQo6RU3IyYLPKqoWwzSLd1E=\r\n=gx/h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.40": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.40", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-collection": "0.1.5-rc.40", "@radix-ui/react-collapsible": "0.1.7-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f7b846ea8874fee59fbc61e94359144170f85a21", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.40.tgz", "fileCount": 8, "integrity": "sha512-VheHpY4JhUNcmIF7Zz1HgWNjL+buU5cl2D1RTtVJgY0aNFlfiePjLou77hxBlpnWRjswESZlDh17DZ7D4+daRg==", "signatures": [{"sig": "MEUCIGEcAcbLnML9I/CK6xwKIUTM2unLN6epHJn1jU2cyQQLAiEA79sKkM3dyRQd3WFsFURBrTbI8/6z5FSQBWReMjVze9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqG2g/+KHzxtXa80e0FaDF+BNoSxzHLVHXCAQU/LG3ps6682x1Rl4Kc\r\n0MQuychevCkMYGGZ7U8FLv4Qd8lE34WYTTM84/AeEjQfFgCKfmj9L0pnySL4\r\nriqS6s+4HmFzKsC0EQYX4bPCvjjIKs4s8nYMouMrfjk7tWUQXdHVymLw++wd\r\nI3nIkLdAQqxmkjcGKcTOoH130GVCEgWNhSUVCCfp/2bF6nd6Qx1Hqx70xQRr\r\n37pv0YkokRt3s6dQYXetOM4VnAJlPDbVTH/aUvDmHnkFXRBOGtbMb7kbueNa\r\n5k4WZzTPd+fVeWSibqKmYmRdS8hxaSylCWKRnmosbpwu81sjLkQWRa/qB8Q5\r\nw7vvMuUVhrsuaOnuCKav9SkQ30jHNX/H2Z9vYWo+nBv65DjmrNCSeX7utuIS\r\nQuizKlQCGyJRVh/vH2PNllQN+gFABpDWKN1E9ePq6prTnp6kRu9d8Dk5uS4s\r\nM+JsqlbJcW2crsgrK1fCAWFRVpEzdJrIF+qoCsya0Vw7VK9FpciqDzUUgkGr\r\ngD/PQh3e5v1wVe8TN+amLM57rySJCZ6e71Dc+mgb40tp4A71T/UBwwp4RJ8X\r\nvh818BVhZ1RXpB3RFhEkUT8w5sWt8pIu66J8ME5wPr2fZgWXuAdjNpUElx0f\r\nep0Y9BNuBc6KOmTIatculxcsLSb9+frr+BU=\r\n=Id+R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.41": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.41", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-collection": "0.1.5-rc.41", "@radix-ui/react-collapsible": "0.1.7-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7765b6ab13776394bdab6385dc0462888adea9bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.41.tgz", "fileCount": 8, "integrity": "sha512-QCeABr/eqf2SJLsTqQ22mfkYqXW7qbDe+69WkH3v5Ywr2vlOnjpFnO+kShuZWqQ9HDJ8/7ObMcx3Q5HVfUSftA==", "signatures": [{"sig": "MEYCIQD2NV7u293NelFzVQUZONDUxH5k8y1rT/IYXA9C31TuFQIhAKDhWhze/THh4kLPuG488d6hnucnzwlb+CO2Yt1BJvJg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaYwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjJxAAoT+1ypfxhmQl9fc92OUx1hsuqo1tRb3U0Le5BMQkMbox0d5L\r\n+OYmqFX+ZvmSH0EFP7hT7vR7JcuaLvmzKy4N7bO0MVhKoZwnwpfkBmfg5pR1\r\n/O5XVivwXuVphEUqNNk4wLD5fx6JPbQB3NXPRXSxgdLY+aVtj1LPeBwILWNq\r\noGwPmktU3Vi8mmuk1kepgAegXlnjj3y2z/+bpgYo4NFlbvtg9uiBVf9oR0Z5\r\nXQklWt8Bn57W3QZkuvQtFUNm1x/cCkBz3pFyLOGmPDQ2wZRY8PcOhjU6N5hX\r\ngWiCGEXEk5iPLZRmi7xDA49J5TAn1gTAO6T38BlDdFgxXb2RWi9lp8pPVRvE\r\na4wk58NOvQnp3tLrWFfxB2DsjdzZMxJqeKDRb0xgGXJUG3KTg3DATxUIu6o9\r\n37rTn5Vl0ScCzwmAPBcxzwAFDHM/Sg/f1JJ/e4vnwEgVnkNm4uDhzgLK8Xwi\r\nVURH9SQRw8sn8M5lNDP62IVXeWUscXqOhqGW+mjGv6NOBN8M4Y4l3SgGHE9M\r\n2/A263EdJKszfJQdXrlAbj3uVYBmq2JMnOwH9ML3itwAYSP2ZBLjaAUw4+KB\r\n4JbGIAnsB6PV1y+eo8J4csvThAYmVeI9ESxGYUqvgZeCWphGTvEobKu96c/Y\r\nK2h1tuvkq1/jgiFgh/RPfJ8gRpdcMx5SYv4=\r\n=MAF9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.42": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.42", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-collection": "0.1.5-rc.42", "@radix-ui/react-collapsible": "0.1.7-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aaf72a9e166320fece56216ff86a6055ee7f7ea7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.42.tgz", "fileCount": 8, "integrity": "sha512-ciU47NQLE7jBuppdBue+VM4Ur5xrZbRXPT/gKYgSzIg4R6wAwEIv/YjLaVZGtFxR5MiTMSFUCvan+b3jgYJNMw==", "signatures": [{"sig": "MEYCIQC0T0ZviARMJ/8HHPqQUP+3fMypRZYg/IuWFATG6rvloAIhAIuhMK6kY+v7TXVrxQB1/sccB6hW8VMCDGy4i8YEWRfx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvc+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPag/9GRlcDiAPDCv3r240AHrJB/um/trn1dc2Tsxj7DMDkBVBqJAg\r\nXfxEFtPTLcGM+Ns9d/ZSaIUXSEaH/v1pE6g2CoYV0Ix3vPFFZO4sDkadMEz9\r\nfpMecwydQO8e2S6VCcRGU4qOuv8u49JwCRrK3OiSR3m9maV+0iNjqJeP6i7S\r\n6VuHCUlruXF9OX2YTRDvrWS7YYePG0PtvR+XxNRlDYP/bWs762kNROOMdhHi\r\nMrKgOulPBCUynJElcC4TYO+UH4yRflbXvatus3wPJ0OroK5i2I29Bz0NY70X\r\nUw7YXqt9EKEO0MCEFvnSS8ZfJKCNziUC6WQ8cKjkKwtUlHzWJJvayr00szJS\r\nE0F6T6X13UZ5EbidSHnr3nUDIdi97N63IcwMpchNgW7ca1+GyTsZKWO+r2fh\r\n8cHDsYI/3hCQIXJztxvtkhTDPNrFAoqiCrKvIV7Cvf2IwAcB53z+CScqwiDX\r\n+xicp1m1D+15F2M73CUWqlYnPOPQxNsgdU7AFGPDTwx8a+KYuHLNVBNnQ5Gz\r\nKNNZDzIZmyiQ0JPtqWRuIvnFYdDSg9tkQVeN364s0gUTlo8gWtoiWzfSE/zS\r\nHB6ff6hDt5zmjfS5eFN3w/pyzPhOqmjnYYlkMSFnTxYbwG3Ed/09Rhv0nKjt\r\nSAoum7AcRfHvT0fgnGU8IAz2YAYyrm0xucg=\r\n=nTRR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.43": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.43", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-collection": "0.1.5-rc.43", "@radix-ui/react-collapsible": "0.1.7-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3f03252b3eda9b88c58e6e1c61b11c324653bdfe", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.43.tgz", "fileCount": 8, "integrity": "sha512-O4aLzNGqdU+U6oyoCdV+cWJnDGWf+e4EtIdSBDl30FzyCPr6qYDLx6rgwHRKDSPYEYObP+8iZi5WPUZsX9xvuA==", "signatures": [{"sig": "MEQCIEH8NmAqYOUsBZ7Yqna38sy03HHjO84AfzNpk0qHBcI4AiA8Fxqq+OQmTMv2XThviIK7JIdNQ3kCBwPEe+F+5/d+rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRQg//S5FjRGFr0EmVrykwx7UX8cD6+X8pFNjFr1U305F27dtU1se3\r\nRKb0V0imOuHu3dgyKhNo5WHLN7+8UJICYYlXfW+mYjZ8P5Wyd6dAS8PMhhdJ\r\n6Wtrs5dE+0qPp87Jy26ucSAcj2UATtpiIYPjIouBB2Ow8OD93Fbw44syzg0I\r\na94SBEdgS/bzyzO9vfMVVu8Mjnu3nLYHLZSUT4yNHJuAmOTh+SDPlytYQskP\r\nri2ts20/CR52TrdpQYU12/BVFocq76ivGxTaLuyUSw69ccD0j9Yjozt5AyGP\r\nj40ZxwTmi7L0meEx659wRx2qWASqNAqBr/j7LgJ4yHNePBqOQMijf8KjlOJm\r\nl7WG5+E5LWih/R4k2RfQWp+UeDqGWhliaTXUpbrYALyQYJKcDXSWZ+6kW8YM\r\nkWhmZut/gHZl+bd4tAWObKDLvt4Ft9t76NSpzjMzqgUzIG40aE9kaR+RRRg8\r\nGNIvIhazKuJaORVMTJfB4A2mZQhKnaJ6T6lnPe2CE4jFHUySazW6rUfVPMPw\r\n+yHyC7YyaiTAdJpblZ57LslzwsDlg72DvwlUGCLNcHqoyUVMLYCGneOjQKgt\r\nlHkQm7LZK2hdMpG3+GXmxmWaomqGfPrVg0Y/8rRy0cUK7XTY9JB1qpA6ZNCa\r\n+W0FK+x8ntpp/ceCWLOW3wGL/Ie/XPzbAQ0=\r\n=SL2X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.44": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.44", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-collection": "0.1.5-rc.44", "@radix-ui/react-collapsible": "0.1.7-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "853bb5bc32443d61ac3ae12312fe880df594bd1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.44.tgz", "fileCount": 8, "integrity": "sha512-l5avl19fHgnanPU7ndR+RWLKwHfBH10RLdFyujumr2MXwFpzpcMn7sYOPDLImwb13UAlaYcdMCB8CEcsT+3viw==", "signatures": [{"sig": "MEUCIE8w8BLrSsC7asJu4z/dAaPZKJqREleRWOhyzDfp9Tp8AiEAm3y8jEoUojyjLJ3aGke/uXW50qqOvXzRWSN9CcR+HYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XFsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrjag//bm3p0ti7S7JSf+tbtOy0KJlmPIJLQSWskl3CKdBYunIE9rQw\r\nBwoemF3RE7pgtt0scQkBJRZIiAcW31PIkfVU79/cz3rOo9hOm9Q138VHU+dE\r\nmf8tp3XsLLRYxpno5oSe9VJ0fbDafusne6b04foaeMJwGudkGaFJ2HgEgR9U\r\nRifc2HA9Dt7WOOSoZgG7xt2nW9qkh+SH7kH0nX27jww9NKwI86TCPOVfaCXD\r\nfuNqXwioeSgp5TDbSmx0vGKeVXoFAmqi71Iv8YQuComymBGB73z0nXI1TPmW\r\nWX/qcSC8G4G9DiUJhc56+yVNMCmvevS93XQk8/cSq6WSnYh/txy8qfXn4gjK\r\ngUzYA63z7dqdMCtbvf6H/1MpyGHyo2RLzwTWDRgM9xp08V90TF+Uri7BkvTu\r\nIRcPgSeBukK4EuNno+XSfNGLvrDtVh4HI3F04KYOZ95yXguUR/DaSxwJGyP3\r\nd2+Qpa3DrLNKGmVeU0ApQuW/k0wGwWriPaIe526I8HK9SLhcMOXJIhce00U7\r\nRCMDzlVa/Hq7Dw1Sn1AOjFLzZPuUzCLcHrFliRMr4LxxMyIh/365lcAFVSpN\r\nwhMEF5Q/GZ989dCoIL23TG+jRg6+DzmRN5DW8wOSfVF2R5MSV/O9lnxppfmB\r\nTzHnYQamRVD1+fWHAgj88A7t6evyFfkgSL0=\r\n=bElj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.45": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.45", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-collection": "0.1.5-rc.45", "@radix-ui/react-collapsible": "0.1.7-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "26c07f8f9b8d0bea93705b3aa4e71638925ccf4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.45.tgz", "fileCount": 8, "integrity": "sha512-bxrnqpascyDvjPDkF+5qETyh5K1ZCdhcknboLojzAB9UJUdFoqvBkskxbdMp6wqsOgpBECkJEOMTNh5WL8k5eQ==", "signatures": [{"sig": "MEUCIQDBoW0EIjj30H3Hhj6WGpmfZvS/FdTSuht10tJBy1jGGwIgNWSqH+EWDbeqnFgDQiBQloXTYrYqSBQHmlL5OCZdQHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnCA//UIzoLx/+TSI3/L7tTk65lc9HkQCNpnKaPrSshf2cEM12wa/R\r\nJPzADToSnIOjnOV8L9eqOFTG99n8MvXFohX9QmCNgl4dcpuMVacNMa25y2RJ\r\nwTQtOppzBTSo3f4mbNljhCrkl+gdbcaQzQXm/N6T/MROFG1ojNjIGrVVMlUy\r\nTW3OcvAQPmsdll8R73qA1wMLVokc6t2S3ZXPs09m0DwoZDiLcjkMMyjxLHPj\r\n2TCg5VIhgFnqtPZi+n3lmRMMfRl7d0gT/QouZyUviMv0mfudMXu+VB53xaJt\r\nwm4Iw1DvWx10ms+3STYtM6/4gBlPvbG0uL5TjywLK0Svfls3DSQ+3bqH3/0G\r\nUuJMGlxxIstaQOz2hX30151VDJjyF1qQESYq1BQjHi1ViVgx404vUW8N35SF\r\n/cQhce4LShzRO6Kol0iXsr1zIesLLmMdChYHWOmgCWyCqJO6uMbxf3DD1t/1\r\napgCcfcfH3qClWdfrV2BKw73/SjuIbBpTKRj8kJi4MDJZyX5/B5L5yTzky2o\r\ncDd/Zl9ob1naM7EKpymtaZHnuDwoO5yeDj6V+zPBPUBkZw5YUmP7lQTa7srr\r\nNBJj+AbX5WkflOjymEi4tga76wwuHkBJe6BaucrOBs25RLKWNJgH00zRcoVp\r\nzliB7r6A1Q1ehCPmgYGZOYXUM9+Zg7Ij2ZM=\r\n=PuBC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.46": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.46", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-collection": "0.1.5-rc.46", "@radix-ui/react-collapsible": "0.1.7-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "91b7522e33aa7eb17609d70b8001c6c4adf4bdd8", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.46.tgz", "fileCount": 8, "integrity": "sha512-2FDKbP/Ddj3jIxZyvdS8weI+G+1NnKbmCSzZR5PtaFunxec8ynHpKrCvGyDsoujdX3yjK+ceFC/AXlyuGyL+2g==", "signatures": [{"sig": "MEUCIQCAXD8ihi8CDvTmyT3fjmMn5UW8sSXF76etcolnkbizDwIgKZFvr2qMqbg8CzgOGrs/lnUXhyH70kAAfDuuoj6RXEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBlA//aaHKeydeL0DJXnlXmjsje+TnxRC76YDXlXiEWeAq1Fgausl2\r\nDHtG4W7bPvPYtSYtnNhkQfJYEQR4a+LXNNWEp5X6TndM3zykB6QfQgy9qAQ9\r\n5yhUvPMDwoYSHDGVuHaAYRdOM4eeHpbAipGiR4fpKohx2cEjb3HDDCyS5NL8\r\nFvPIsSTimAlHug/kd50RuQXHZ2zAv7ARZ4GIDhj3Iw9JMTN2yZVEH7Ylfc07\r\nkS+cb/YqPFeSXpRuHrMcuh7ZiY5sPX8lHKXtsN+pDFVZ+SrTTqD/O/m/6K0V\r\n/HVTFCCOBKt39O952x7LO0IHvx7wLCHUxaYH/Hx0EAH5G86Cl6busiH+BuQB\r\nYn9w/tCU6p1qhJgo0dCZxvZaVY3T6khXjoWp9TRipOTHytIyuohIWCZZDIE1\r\n+OvqDJGBK3hyTCBSSy86QOi5xdxm8rFQYh3JF+KpSbldzOXrMrfOetO+gspU\r\nKGn85EWyNopX/6RS849r0sSnE1jVGLVwOcwGbS0UyTRye0Wnum4z6BNMVkpN\r\nelrEvKSrkV/ZNDQUUjrZOI37Z8Eg+DEnrPQ4RMB1P1CUzD/c+ZtcmORsByJ+\r\nCZ7nT68ROxIIjrhm8jh/zS1N6rzeg3ydQkD+FJAJEz4iFJBR2MLDhfQkgCy/\r\nOtL/z/5NdfUQyKbW73ND6G96Q2tZgEjsbys=\r\n=+Tpn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.47": {"name": "@radix-ui/react-accordion", "version": "0.1.7-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.47", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-collection": "0.1.5-rc.47", "@radix-ui/react-collapsible": "0.1.7-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3636f350750467b558834770621ab4f78d5ed553", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.1.7-rc.47.tgz", "fileCount": 8, "integrity": "sha512-1aOvW0uw+swgq7lKTyiHvDifBlaVnSWsMJuKCrFDV+jRfnS36H8lLOABWeJhPyxTVpSwxRFaVlRFdG+0EhEM1A==", "signatures": [{"sig": "MEQCIAs4wdt9aq3u00sJQzjXzW2CAfyxNuX3ZJC+pF7HuH3EAiABbzXt1B9cNpUmsvvuibyYaB66JLuoPyZ8MMDovuhKVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CCzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXQQ/+OVsQnxVVdpCFfILtYNlfL1h0B3aGuqCHoAoBKqf1SKURVFvh\r\n6LjBv0btc5U3XUEeUIgnSQEU/XOVdGJU2+8Xiexxf8cENepb8eVnL0UtVUuJ\r\ng8e7kJHg6OmFItq1RXnVm08nB7/qBjwsWodrArprJlwmJASbrBa6rspHsAE+\r\nWjNdJ+JJvqmzTdjhKmdsMQ5QGHj3X9K5U6DQEmZ8xhTtk0Mtzs5xeTXK+JuL\r\nu9fOFQKLacYEUbkZEkZpSJxhijzSGciYi3A0H78HdXm8RRw2ZyKWMloyh4bL\r\n787XbVznn9HNG4nBJP0x03J6OUtHTE8T/xMPjrDPN+7r1YNMkTbBElIbOEEJ\r\nqR65BwFjuQMIfbmJAotNk52lFIliPfd6zdQVVCP5SwyKxTVzdM4c6SJ6ir3p\r\n4gAOkRyojauo0MMcFiG4OLY8dDR8GyDCj45HlPyABLUXb5vdWdRnjpebJpSd\r\n4krKqKWX8lbxj6cKusZK0tcYos+vVqwBahNyB+Br68Q9lpK2g5gfr5wMB7PA\r\nA64cEeCNiXHMAtHRxwMKhTqldBnhxFwnG8glujzH3iUySPlkH0vp3X5fxIwe\r\n8KDoxrW80SQliDeP4LPKN1YiCwhbbp//+jotqW5yGZE/877UvskuEcIhCFKz\r\nXzBedbZQIi9SKiZHffTIV77xzzYokLHjB2w=\r\n=kn84\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-collection": "1.0.0-rc.1", "@radix-ui/react-collapsible": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7767d914137142e658c1016c97fa1c0ead0e5064", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-cUjA3DNT7T1FNORHde3ELNyGQKMLXnCOruNf9Lskr/99+9bNgDm6apKrNHh+BB1DavHbxoEe3kwllgst/yGMGw==", "signatures": [{"sig": "MEQCIC5R/Ypq7NQO2H5aujCyi1asTDqu9Lh9ByJeqj2oALwxAiBUWrQaU0KO9NSAy6LiVLKX2F+Y+1WoKaL9Si2zl66Sfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EuoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzVQ//SV1RBI2g0wEFcwjJQAHAr72nbzK4Y5d4qVX9m6Hzrfd1MH7S\r\nqh6Kp7yECTaBlfS44NtTyAvwBF51q82bQ4tuj8xziNhDJJLoBea/1G1wmamc\r\n+l3OnPzaTAfX2vC8onMkX2qO+z45YbGmXK3Hsi81SRI7vUQ7iGEqXIiZh4Ns\r\nifoPsJIDr508JcWEc5Fe70ney2J1BqDXHuAPJuLikxK2w9fnHolAklc+dm6F\r\nqUfVhQTMZ61/8M72VIzt1ilW3MoE6htRM3ZSAg4Nj2RJgzDKok+vOXtmGSB5\r\nkNb7UYiyuldPiHwMTuSqlm8ZaU7PNuy7MJ7BXin5O6w9kaokFJjdhm05C2uf\r\nja8AMlGqgFdUfA6zhBHlwXTt/LG3XUGMPKdDCPVo7A6HySWmPMjbn1yj7wCU\r\nlQJeHWTdeHNd3CTGwiEGn32eQAPE1WEG8U27tAdhdYpjXMTrFpmVDac04UWC\r\n6UvcJb82jyzD0nMmJS8CoswDKsuHaLfpR+OxZgn6ZZ7OyH7Ta/h003vrebLW\r\nL4dqe1bm6HV904QZvfFOT4giEixq6eEPwrweJOYkHS5GXmVodzofoR+QpIiX\r\nFLzdViX3L6se9EacCmqv6WUKQE4Hsg13+ACmrr0wW0Bts/0X3JvnI9pX5Y4e\r\nwEYeLebgZORwj5/wzdGvK4jy+rLzb10rOvk=\r\n=SYwN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-accordion", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-collapsible": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf69dc1f13fce05d6d7560ff79954c49abc1b71b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-F<PERSON><PERSON><PERSON><PERSON>+f1gjrWiU+IFQIzN43fYyvQ+AN0OicHYoDddis53xnPC0DKm16Ks4/XjvmqbISAR/FscYX0vymEHxcA==", "signatures": [{"sig": "MEYCIQCUrrF+v2zucoirhSpHS6gmixGaAIlKPXAFvDj5tKM1pQIhAM7y1FVRhC2JWZDI81z4isFfCEf2DVJNyXckZrV7xfii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoU6Q//XedNX8HmqQ6Tjym2iUvbFzCmNmps0ZmqeysZnxNl981dNyR3\r\nNUkSp0Vhc4AI+biuZhYo79pM+8b+1CNxSkzgQzqYRNjjoU3WpVKq30XNlsFk\r\nrdqMOfXw56f8S/pyeMX/j+Ja6tK5HCIImYjrBA3AXOjYzbVkrJspQ6Cm8fmo\r\nZsOAA6nXb2/bRyNs+xj6teZFA5+Rdfe4XXPhsujS5yKMH21OZHGrh+2B6Rx/\r\nQgju01B8EWyxOPXplPw63f2TBFeblQHJNeQqI56YETxXIJ3P7TMu3Hgg6FXG\r\niFhqgAk4zGh/Uu+gz+EL+uOnmhtg+5wMQUzhdCm3YAAjB1pKeeUQiMcORlQ7\r\nXn2nF4tz0sPqvSt89y6xvPdlu1g5KJkHhKFeDpW3dcyto5nDbNWcPA/F7NcR\r\n4xQHFxAxopKFbaHcVIM1dWmbnTS5SJG5Yuyru9ClfqyG9Epi4EanjmG3fq4g\r\nPt9X0M1rWOSVreTIphzQF2kxzQA7873zbuxQmkBykeOsn9lvnex+GG//hA9D\r\nfDdfK5rk/8jd7IfyeZHxT9y3cMcOq4Uengay02cB9123/y6rEYhPGh2z3YGY\r\ns229h7/sJWQWY7m+9rw6D5GGpQ8M3KzQo/Hs37gJfWve719vSf8ZG4//DJJZ\r\nSL3q7xdQ1gArdyr63E9oKNAPDLQ0paDf7ck=\r\n=zT6k\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-collection": "1.0.1-rc.1", "@radix-ui/react-collapsible": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "498ed16bc22f298c8244820eb0ac462cf1234e3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-jhrc1aoawRgqPfYcBzraS308m6Pt/86f6tGP7fOp5E943y/++YcrehVfi6RJcjU9XqcA0tIxB4U/VW6P/PpqWA==", "signatures": [{"sig": "MEUCIQDW1vP7n8x1eWrRE/7IauTVETEHL7Z0fGGpFSN5RimOaQIgRBx2Yo+zb1lv7ZU1m1sBDCBL7ydjMds9OgK+HG78MMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLgA/+Lqpa+W7O6qCh6Matenv99C1TtnGlYwmxTShs3QatGGL/fFAB\r\nwNLUZuY709rz/6eCl1Gu5VAp4NWAAFfBkAjtnOwUvh0kGTE7UcbMb9AC3JjI\r\nERFK65i6Or5Gnm0SvjpD6DfWYpllaBFILObBsrc3A6WNLXWjmVaWXEbeNdbw\r\nZhja11QW37BgYCMRGDIkx7jSm6/GbMehupOFwRAzLaHwsYVHMeQXf7C4QhcR\r\nY3DG7Y8fXVf53li7oxfHsaO7MZ5/YayWqKPjQ6vzwAjUdz2SVdWr0XxgQ5IS\r\npkcMKi6wdzyWuj3I6cHAXizfGvc3EzYvmbima58+kUWuDkxgh+jBM0Hrxy7G\r\nV5XBMB9ZEZ+6YQuSHRVMdlnOlU0WJcCT8QV3JnsQ8+Xf9kR8ASd/8JDDygTH\r\nK6zWriagsyKrlV0dUmMuLjfUcRKVNj2+U2pnjmYUZR+5ZlC7mt4IU2gaPk1o\r\nzkiQTLwCcznoZAJsFllgKxNE48ydJoxJGRnSVXWcdrfItT7paZYGnBdXiw4e\r\nsOW8dxbStrW1XNgMa2uUkSAFBGa17TkKuaMUvcVdCTHeStJHbJHwmo0NtCYI\r\n2t0LWwlSNhB0unyHLwsHJdPV1fGlji+ZQeXBB90uxGwJFu/BLsE1LJKS/1+l\r\n3t1S7yN5ksbv7cJOLic3rRsDuMfAmZLOYSw=\r\n=QMJP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-collection": "1.0.1-rc.2", "@radix-ui/react-collapsible": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9206726997f9db90d58a61adcdecfdd2fccc59f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-vglac7OEIWR+kPTAuaCA6DKaXCpCCgzZunoWES/HLTakvt/32GRP3j56Holgr1CTfO18O1o2pbSPXWOduTRc1g==", "signatures": [{"sig": "MEUCIQDeW0NP/166+sU27aerQIjjgZrl0WfIxFERoAyDWcWWOgIgcOjPIe7RGVvveMqAV4xgniRMJrQKJGiRcLAqciISq+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKyeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmKg/+JZsqMF9WpXVp/gIzv4XfCUtoW8V5RBCkWPepv/9U3BKirGSK\r\ncHnZRC//XfklPDlmum/0Y+X1AW12B30s+B2ep6IiYWd8Hd86PJWoYW+5iPAr\r\ncxmYjXVli449sVvatPr0ozzURq8RZy5s/kDEKso5rQWmSY5LOwK5yyaj4G7w\r\nUzWxZZthasRANJX+M4PtoI4kaemZXQQH/ak8yODQHlCNeJyptR0sBQVraKfs\r\nRgsrM6Uzmdwx3KX3/LSxpyWi0Rneo+5vkMRtbLU3gtPGt8fyvG8SZtbuMTO6\r\nUUa/RXku7Tt+u9gpXUmOaa3Yy1dZKLpyXEs+ocL8X6dCvXTeDGTuxo6t/pNU\r\nF0JZcoBmX25D5LTYrKZArOJ787CIhVXPKF6nyPyf/EpEmrYn7oaaGaVLe6lj\r\nkSx5laA+C60wwGsgJvrlSON5HBpZiIbnegEjj71/Xr8aMwhF0ZIhPXU/8dib\r\nQhuWPEZNenYQIqxuqX6k9lclAiWCWrWK5g4rczQTtJ/m+E0Y92Hc/JHB2HZe\r\nTFAChBMOGmuD7CPtK2zGKkjFWHMRxVf6LQjBYtfanENU7kYx3VjaBN9MXHIb\r\nT8eILjS5NbbR7mUpno/clWdhncrdJgTnr0Mni2Udh8+WhZY1QwHfEhGq2+go\r\ngKbZzzqDpXt8hZvxm97bszy1Sfb2n+/jWog=\r\n=/1p6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-collection": "1.0.1-rc.3", "@radix-ui/react-collapsible": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8c7be88c2141ada92078cdf31149fe8d8ff330ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-/3JJHcGkGeJCHikQGnjOVQ8pSeR5fiysA9TAGL58DPNR6tjkfxGyzQPmSqn8vAG1Q2fyIWA1O1pIxrgd5YQeBA==", "signatures": [{"sig": "MEYCIQCM1+fcCDFjLEaurCMxuw8Z3MYezFbWQobOBKGgLJiBtAIhAM1DRI8wj1efTn3homMpglMdM96jsALro9RNZrRLZJlT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdbqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpj/w//d5iQaRcfX7NjwOPCUEvQ1cx2qaKY7G1wU7Uuag1AN9gohDD8\r\nfhLOF9SU+UxWbbrSTRD2HvC/ylmM1QknREyxSay6TJC++nEB2WSkdyaMAVvI\r\nqB/8eZ/8KdBJZtvYCgv8OpJbWSTanc+ielfs+SR+o1pupgUvj9nEdwcyoTXD\r\nHEi4Uu/cP2LuMSNXQW04FLgpJVk80m0bLUdGPMZ8LKYC0rBeCvu9b5/lDsrv\r\njvAgVrX+eL9is4S7pmqqgTGESHSFlSKexfaSfGSPBtENu/cSSTlMK9iR/KtH\r\nt5mMc6bo5xkgbALeuBOBKdhOy5vheOL03ArIbJvCAZmNuamU4y7JZokeI7Pu\r\n8vHqmv0tSXJlW4RFGU7Jx+QVgpn3TpnKREm+eJ8ni4RHJ+fxAI/0x2yzGGOm\r\n3HyevzBqoEGXLzg7vmApOiVwqjHsxO7d4ZElgEe2kJIYBgDdoj7aSxrtJLLy\r\nI/t/iJGK6hvy+6BnmnPXdEjUSOdxumCQrYg3LNiZ2Vvs1jVQ9nstPtYNplW+\r\ngZ7cI9/Nn4VSaJ2zOX5hDJCKeZBKD0YenfkPu0ki2cbXR1EoESL0WHBY8XKV\r\nZAhNS//ljRjk1lhXOjsEF3c+98ENp+BJx4UX8CaQXJGkFIc9iCsdl3bTJ+Ry\r\nxvmFUrmaJWONsi+7qqykYW7ucIRtGtKSUko=\r\n=BszF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-collection": "1.0.1-rc.4", "@radix-ui/react-collapsible": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ac2994ea7f9afbb8e74076518795b19e0cd04e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-7kuJBMIgq/MzQ2i5U3FHAHxnTTO/yacGcEspQS/vOsZXRP91BvZh8E+10tZDu1OyxzvEQeIZhLOFT6ANItwQ9Q==", "signatures": [{"sig": "MEUCID9Y+rdh3U88X9TAc5pVYTOa7gVuq6eYoPXgyteMKhjDAiEAt3gtyoDmZoprTQRn/3DU3sTAE2eEfO0Q1S2hB/uKy4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU7g/6A0fcwjXzQbgFfL9T9yb1PdSDoVv7sRHbANX3ZNYBVEP3m9eQ\r\nIn+eV7xlM1WU4FkyGbv+deJLJK88VfoA7N2CEHCzAaSQrjraBfYzxCctu0kq\r\nEx6sVnAgONjXPyREyrqJ+vu7aHlaRd8k4upPFfEIdI6835dy7xK+uCPIeQ2+\r\nPJ9dT/SLaagaV8v1Vr9UYzPvUQdTH/q4+SnGvH6ct5Falkq1/eocNNUupSbV\r\nDW8kpCC3eLU3VO9lY62SDucw345ke9/5Y3hjvM0Yv2c/gqDJe7yFYMBpiHdu\r\nJbD0lNhyXGfxmIDtiZ4gTL2zo9yQgDLN5Ky/eBY7ylp2gf3CFcK/sZN/lwmV\r\nxTW8AfaRMZOXzD5k8OhMtqtsUptow+nY1eiG2Rxk6e0ijAxVgOF9rA8uX9s7\r\nogYg9GmztdbyDOb91YDI+roA+Ix2uCCENWUIIC/Mhl78sGZSDBGkBInZWhut\r\nfW5gY3StXFJ2cR6AyHR49eAMeuWxIZdDka6tmgJX/cvl+0lTzvmfauAXdaLJ\r\nTgHowFzEc6QdIFWo6TNXQMM3Yg/JhPACyc42Ib/LiI2/Q8k8TfouoBcFtW06\r\n01YNtG+lnSkeGkOHJn2560l+tTWfKwhbUnKtNkxcjdX+2F04Wy5PpqXpes8O\r\nMo34aZRvA4ik7X41+CM0SuaK5DkZ+IKPwbo=\r\n=ef9I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-collection": "1.0.1-rc.5", "@radix-ui/react-collapsible": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1644b58799f3278da2585cfefd57af8e1f207bdb", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-GKJtoAB2dFz6xeHJUZ7+1Vd4zB4W1jGhMktuKPSYESBcI2ReVB6OK2SYk7c6JQYaD6uZAmAtj0u4xtUkqM1j2g==", "signatures": [{"sig": "MEUCIQCZeaPZYZV4RTriGl4aFc023qOs4Fp+Nkjtf0ApmJ/sLQIgfk2T7covjme5fLU7FEGGyXpgqUnD4xJZZFZgPmXVfK0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlYg//TDDwQCbHx2AQnFYiI8rxXerv1Seur77Y52ibD/CKNV3liFXK\r\nuin/ImhXvhFkxYBXVIwvQmaQWrSp9BgWF06hQfkOaC6A/a0TaDrVcoJ/GpRe\r\nDpaAj3m8410Envsr+cxsjinL75G16eYhP+NGbQ6CDGKIKfoFa5oP8Z59OH2e\r\nKqwwoF7PK6FIbi11BAJeP7pkV+iFj6OiKRJtcT9SyVRjzpoIU1bR8Yw8TW0M\r\n5WxZeLFhlGv0XDSbcb752vlcs6kotVHTnTGl1WlnGxglLqtrOtsxxhirJW1m\r\nUPo1cTb5wnG06UT1442hRqxmiPrHy0ofCTTOlR1AcYokk0bafo+1T0TZtFFM\r\n+siOffupydKIxYIE5hIlcFGk5hrEZX4m4TIlV/CpzeT4pHKwi2X1lrRqJ609\r\nop2VmqD719Hd0Cp4pdg9zQtNtlSiqCy/XcHYa2plTuNjrYHTLLGUmx61rQa7\r\ntHjFPybM6TDHpiDsu1RbuotxsppBSFv5c8gXVDwDJkQO6Os6mb1CUm20W8Xv\r\nXWC7qdzZc08NLV3wiaJuxDPfwqe4fU+GPdL/BD8MM0MUpDMl/NC007Zfn0Ov\r\nGaYBOZfARfLBbl75Bea/AO7WCajt6p7wKMIG2hmfoL27uB2YJn9L6e3AVC9Q\r\nEoWY11ABVcOfcHXrgkXYshI2dnuYwp0AOyM=\r\n=AT87\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-collection": "1.0.1-rc.6", "@radix-ui/react-collapsible": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0b601a030e8d70df1d294a6d8f1e84ca28f9af13", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-O/wDG401mK4nPo66mSyAl9XHgGJDy7P/AxXEwO8dJA0UrQYni3f/HjS0G1vYWYyJX/45Fa0Ukauv1n3zsSEttQ==", "signatures": [{"sig": "MEQCIAwSktobOnc/MaacsYTI4f8QiXYU4ra6DTakWz7s8cz4AiBfg1yaRGA2BCYNfHP4io5JOmpoFdyH+Dq6hc3u9rcfQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwOmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOSg/9ESfKOVUSU9OD+mCjIx2QzUzU7dn7PoPpEyvI+8vxtIzo7M4C\r\nH0SkX1+CHgotGiexilPIxGhxK1a9pqXLC8vK9Ist0sCTn+OXQGt0jy/ukN+X\r\nfA92Je56gG0x+pqQIJNKFtuXdlxknEZT/WcSzR6/9e2utkJ7XIRCKrONL3vb\r\nqSj2ZoyizI3b6jAHmL1QbMbSA9MZY8vVknRrQhVBic+qYtpzO5frps5e+Cad\r\nkzUEPLkU4af0i4sqSbY+PnVsoa9+L3Vv3w3OtdP9kUkhKHZLY/da5PwLcWih\r\ne0NK/TdczkyGmFG0HNT8YWrUBSeYfXGq3pK2FFng5e3U4Q6B2dtSEc3iGObQ\r\np7Y53Kgr+t7McPZSc2aXoYRKVJuYqWx10eQUnZTbpA3wbQafQkeIYDYSzjZ8\r\nJlYJmBiWh1G1BhZ0E7o8cgLNibdQTaMN1u10T4MLZ4zXDqoJBg6dyjKbQlTm\r\nKEwrqqaIyoJDTZipONjPUtq2cirb8qg9wXLc5YUGTPUHij+fbqrU6rVMbSly\r\ncleUjCGyxM/t5665oyzd9Th7fbxa6Awy8orLZH3/4cavYNdjiumu7D9g79b/\r\nn58HKgaQWeH/BQeVwSQ6JrNyXCG/xj0ICBLES34k0gvBm9UdAwe/pNQFfOCe\r\nVKQbv80UTafESxoQ7UVOkjU9UVvGnGJqu7I=\r\n=fqIL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-collection": "1.0.1-rc.7", "@radix-ui/react-collapsible": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9b933c2f533f75d202aa5b089ddf9336ef8ab344", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-l9k6Dpb845sFYvZ5xKVfvxlPdv/1AirjhTpkmlXIPLPpiZTbvFTh5ccbCGXtfeeyjhr6JMHLJipIDnCKaKXubw==", "signatures": [{"sig": "MEUCIDL7Augp5dh1HYODQvmOPSrpdBfS1iWPn6Bzd6ZZGhCjAiEAu8cIVGGmrr74EZvTPZqyaGYWY9y9s1QnUxGgB5fSKp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWcQ/9HZR/paxCqVzDSvwYqoOs0LpJrCGl6QM2/P2cCC8OW+OIEEy7\r\nfepLPz2U3rcjo3QtRYfckZEFm4MZqtLOzxckrzDcw5YjOumIhEPfXKGTdiyQ\r\nidrmVtMxEIiZFdBfOdwJdlthDUMmHnmjjDVM64IdCSoYn0/CU0gQbBblSroL\r\ntvDtEm47VGWwi5zFws8hfJdoXDKIHP3AIU9xTMp9pD1ZkOpN++7g4Ok+gnvb\r\nM+JZwQnhNdJuqukjrvpv2cB5CyU1QHp9RPFOl4hoYwNJwaaLsMs7k93TF/Nc\r\nJcdZIYQhEPNzrsnLigAg1JDRTJKEvxEAokjdTVE/FJhbIOdi1aJWEwIhNw4D\r\nnbd7zF5pC406dVVJg1/pti7/QxSauTw1G3RFjhl2rjrYLv4R1o2qrglG55i1\r\n8Kpw0klHfJy23R1x3Ft2u+6zQdLsFJrKSo+tKV1bsEcleh4p/oVUUQ9BRifm\r\ni8ORQosMt7s/BslqOX4w3HTn6P26j55Np3ygrI5+kBPAnnr1AQouJ7uJf8h6\r\nc4RxZ+DgCecC3CJRkMlO602oElw0+9VLM4JH4DBCw1iAoNhLLnttcnFkM8a6\r\n6Kd6LJvZ7t7vo2KtywptB+lfBT870JjAJqYgGnDAJsZ9DzNrnxlxXSetrTHy\r\nTdRDCZkpj+PRNNJumYRj17LJPOD949H/bS8=\r\n=XQuG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-collection": "1.0.1-rc.8", "@radix-ui/react-collapsible": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3ffd51f552520aa1a1cbd2666c92c3ffe5b25c81", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-HG6pSbH23ch7Ts4pzc4zaEHE56bEcN8mMI3Tzl/RrPP5N22kPrdRDjWbcPA3Nk17ObFydK0Is4d2gPbuBAD5HQ==", "signatures": [{"sig": "MEQCIFgI8GTiaperRnoTAIVUiBQMmHnRh9rSXDWrgyvFIIzRAiAdVEPqqX8u52AcvJy57rWVVy4dJ9GvkOhL1ZHviUVYGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+f+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVGQ//VNujY7Ji0RCLp36/8jqoCNkKlwUumtlWfcYM9226BAGn6xLE\r\nFoSo/W/1YlQDVgWcqWI9VPQ3jPiB3xYmiL9Fbi0dtQEvNnr86wpozzcAgs7K\r\n+QiSIu/iGNGsPZKdJu44ehVfp7xG4ZXggU/7b2ryvsr2JIXFrjzLeB3jOKXI\r\ncpj81eJQCaojYK1VwQElyzBOFxjNm6bjDSQ6R8JNAN/PGavG5242nDF1oAvL\r\n9DaNu6/UP4DnTs6fZtG3ClOVYIXzVdzoYPiFMrt3v1lo5d1EQ/kk1xyv41kf\r\nlkniM0pqNwSd1PuwhtIcPLE4QGF4voaNYp/5h91MK+Tip8LpUFReDfHJx2hO\r\n30ILSA8ziBQJIKaAOqL8vw+x34SQrLxytYNLeI2Sc5Z379J3Ow7Hn0w9i2dB\r\n0qSZEr9UbtpIEqs2PeWrzXTY51NBDn50hjB2J8G6CFnIGr8ISoMcP+ucKKmi\r\nYSZMLKOYwo/kayIfVSIzWEviLxxZWB2CrozVJwPKIBzKGi8LhvoaCdvr1YAN\r\nSdFgLbXMQLI1U0YZv0hjGZfLOJM0N6KW6hw4roOu9Ii5RUHK5frKLGu9738N\r\n2BG+KgSnX+/Z20/0A80WvRXYj1dfV9gfpgVvmbL17u4AdtB0ZPXeiv8V77gt\r\n2WN8Wyf9Zu7E7A90Z2JZi6dUZZRR2TfI0Z8=\r\n=o4Ea\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-collection": "1.0.1-rc.9", "@radix-ui/react-collapsible": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "52799586e3fdbbcd51d3ca3c54d00753533301bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-vs1NKV0194B8iJJtYBvEBL8b8eRgaLfZKAp24gJqQA19Lt3PQdTKSSIO41K3bWBzCUjlmbVdgwlAwJCfLNRXww==", "signatures": [{"sig": "MEYCIQCiHyrTRyuUC/QLKMBW2tNGvIpQS5VQWOkSj12aB/zIhgIhAKJbZb88tQCu35QOF18BrvqpdzCjoPtEQJ4FXRW7GPgW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/arACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS8g/+KukLwNjRq19NKSiA0CuvLpR7YXUkx/51VUaTuySLU9JD9Yr7\r\n1aPiJEnDxoV2eeNldmlWfWIroVK2NDrOeHRpkFmBCSWWqZI8iVYCWGib+q2+\r\nAdDrhurbxrFE0V8s4mGEJ3SQEfIZ2TvH4W4aUw6t9rHlz4DGBrp8J0Hvlwtg\r\nPEGTiayxTL0r7l5JDHQtulqNDfFXO5N+5+X0La+zuFUvRDNpNNBuQKTj2VtL\r\nvP6Dn1jwJyk5l3YCyi198a8FChFJHGxhz7h+Hy5fLKBiVvJOLocqXSEVSxir\r\n40kpzLH1c7mG+a47+wLnkd48+RFpaxNZ7iac1qPKTXy/as0by3SzU3Vt8KQu\r\nGKId7rLYT6ehRlhR/IGfvF93F3kpgwS42cm7B8nFmnlt/EQTC4CH13KVpn+T\r\n0SkaDAs6QzXNV/m12XjMTltdWuYEZzvi2lisDtrk59qna87vgSiFHWsECUfd\r\ndzU/mooiGsdbU3m9rdyT8LvTTd/4WrxHm1E3yV0oqKl3Nvs+hKuyAtYNs+SG\r\nMlM7YbNait3j1udyZB5OoqoX2NChXbeqi+YgfaaEe6wL58KWDoHBXXnaxP87\r\nMSHXBm0VruINWBEjVK0eYhTePFf+b3IE8N89mcBx4fvzDU+UjXcyqjXQDpHP\r\nlPhwlw8WRm8iY4sGqYMjn05tWCm3dz1JTbE=\r\n=Bagl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-collection": "1.0.1-rc.10", "@radix-ui/react-collapsible": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "32470f2eec4626ae76ed4640fb35d0637c316ae7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-92QjOLEYsNgK6vY+QCVjnmScTzlMxOFdwUP1jiY2Z943K7SxGVIqjrs0Yy6IASRuCuObC8GwdM+6EvcZ9I5KmQ==", "signatures": [{"sig": "MEQCIEBOadOpJi66Ae6hxTFsKON0YNxhuIrRGkcOra+uOmT0AiAQ3r1BK7TYOONCvU5Oxj9qRvdQ7QjbXCD9NK1mE0wNFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJcQ//Rucg0shVHjrmsncGuMwEzqQbA4LENBfanTZsThcGizwq7Tu6\r\nNHFrgVAe1HmOMOB/QQvqBxd/SqJKX4EHgQectRDD1ZV1uSg16VDaTZpt2T1G\r\n6I2iSfIuqKetUvqJ9qgHs8kU3A7Zy8xv/NHHwIkQymHbf9+ZA1shPkGKk4f9\r\nzAnD1cuHPNoEOTUKBcCZm7dEAfKzmH/IT4kVVQWF3ARaDfabJMNHV2N6Mn1J\r\n/ZpjUYC8cvKtz/QcHqy0/w8bqQUvfKo1cBj/02yCURUsT73aCq4sfPQ2kWtw\r\n10rwLDXR8nexJHjOk3Qi92ttonlyaAB2qxVDwHKYVBRSNZfZ9vZKGLvkA9Xf\r\nzgvU8W6S2PLi/lej/9vuVC6hFWr8xduSn1WtfvsZ7nHCONLNJIjKd9aj1a/z\r\nMoIfh9PEGEXrtmcUkdM8hIWj560oRV7TDZqeJF4eUHY+1c6lDBYzLJdIC9e5\r\naSNFChqNb/x4Rpl1hPlXXxR4OFQYEGuVWzA1cVo3UAQzyWSZmJwBBPh1/IUI\r\nqU/4egf3TBOAEAPN0rEvNqbOaSUaERAhu0O1NHfGNxXt6Ewy6lORvIlBuCo/\r\nvlbWcpsjJ8fDb+o2BHx8UPjA47KDCuIEw5eKJFP7SBwEfsdYmAjialnBQfK5\r\nE7w0EahKal1s/bzSQeT0EN3XTVNhLd44GXk=\r\n=UulB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-collection": "1.0.1-rc.11", "@radix-ui/react-collapsible": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9258efd1c773c6fb66296729e267fec742bca058", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-+UYvUYfOfe55arB/BocJj++smR31k3EHLTdQ2wvGIfiVIkMGGq2pi1Z1Hv+rTs9FkmUWYujH6gkNBBBFkvCMvw==", "signatures": [{"sig": "MEUCIQDnZKBxZgIYwnRYCumI+LeSs3PP7ETLOwwSK41ZHMlx/wIgSt6Pu31Evsb2VJU2w8btN85ivvI7/p968HXrecIu5BM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRw7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI5g//eYUruuuVAiUNcN5LSCXyQx23e9m3z3NK/zBGrAcveMgWPBX1\r\nSMI2XhzWl6HkTdaRB2c7V4NiEKUsq8S6iVXxDXxN+6mWNx1qSE/u3ipFBz6i\r\ngSbNdMR4u9N4QUVquY+fr9t2xuvu1RmWf4OvxK8Hqr03pQJ4Hy1ls3N4MA2b\r\nR0vtre8XWXKinIwEBKG+3BzaE76Sjlzo99B/a9ydUjFVP+4wL6+GscDZhHYS\r\nSBuio9FHjzEO/gKi+iH5Iv8crN6LlVLrq/540HoufqbbISgB3/jhnCQA7dIk\r\nD8s6KhQrhgUQF6yVLhxUnG7UHNI+1RHzf5LkrJD3SDB1Q/xzVZqW503zahE6\r\nWvCYy4yuh3Ob70yqaMuziJMETR2sLBPSqlE26wzCZjiXaSSkacPK+s+Kyi5l\r\nTROxzx6Va40SZwEg79ENMfFXLiFZLDvnmNIyw57qhQFOSpuM59+d0GF6AS41\r\nJQ5lU+AhKw9y8IvKE+Q8YGpn7V92w5Ibcq4Xp0TdskWL6t3VHB9k+U1FAacR\r\nD9tJjnvZIIhzwdU0cj6XwED6ciM5FXLkq+N2rbOUmxdHLhJTLb2ZDTU962B0\r\nRmAkUBV1lIjvC9EQ2mVSEiY28fpY7i/uDapFm7+wIyz0x39wSKsPwe+W6bvi\r\nRoDJ43XAdBd5aQtJ4XJdKmUVSIrRjfePE4I=\r\n=+etw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-collection": "1.0.1-rc.12", "@radix-ui/react-collapsible": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "01f8342c00342f0420333894883a827c798dba34", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-U3dt2eZVLw95U0Tq1ngthGrHq5DYcjvaiTYhvoCjneuRFpPT3genoWsSTy4zcDhm9xH4LfTv+//9yCgzvrdaag==", "signatures": [{"sig": "MEUCIQCM/5+mTxaQw4eV8g62bq3gITLx9OAoaeHPXrQWcQOvpAIgLTX4VV6eX5Mpf4KGM/k7C+9TqP7CGwXRVz/XsxMqzro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVLoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogAxAAgE2vS+4HMQyr8Y5T+z8EzNSmcltyTQRahYC+wLGWB3Ccmb08\r\nzbAFgTyV60dc96BniXLTV7FVvEvsWs3hMs7QpEoDDF/VffP1pYLkN1MpFty2\r\nLJDdyNerS7SoQDhEqnBllKNPqMRfhbYzmRGloQ1nLPKw59xs4Y70u2nVmNEE\r\nG1aVov+EJ28WWyb2iTX++uVn8Z08qg0aM/z+Rc0wOJIc0mSGr6K1hNhxCl6y\r\noK4JUZFKlCIYZDzQ7UrQXv0Q4hnzf+woAxfDCVw9/3F5L7Gm1IyuCQKe/Mns\r\nlgbtGOgT297mQAXR9Ztc7cDHmmST09IbwXj5uVLsrkzDox3OG91F4qUqGqVr\r\nYAmRwaWN8aeUNYFRQ3NaknonU4uCQZBChx6KD5okPX8kyzBJvLtcWCSj2/qb\r\nJJWD6lNdHoE2SYwUoTi1PhyYemy60Yf0+Ml2Seiwqi6EETiAgzyPXcmRP1l6\r\ncBf205TI5kRi3OTwbS0pKlkoVWDW1OOLVIwcCT+H0hFoFrVVdgJ04lZ9ZDBb\r\n96sOxlFrS7UxC2fBBKlUsCZ62Vosrxok/rE0d0Fpcl4exPw/A52D48siFrn2\r\nHZWQrbTwP8icAB+6zZGhK6Q3afA7w+65YOh/DKrnT7pXXdwA0kasKwLZaJeL\r\nMunUoiQMstwsgl9sahm9jL8lfIDV+e3LCAg=\r\n=athk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-collection": "1.0.1-rc.13", "@radix-ui/react-collapsible": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "188568c9feeead7a51a653de511c52e8058c484b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-X3WGSGYA3GKIQEM5je6QP4gMa6XGiLquw1F6IyTvAG5ec+UrqZ9inEzeIbRlutcPTDPvQg43QpkyYE40U3oxdg==", "signatures": [{"sig": "MEUCICT8MTW7zc4fJTRVynNyXF3YWOozoN/mterHRF7MHNg4AiEAmufrWhf308B0BNcYS7mg/Rwk0LKA3JRuekBww3lAtn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnJyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLTg/+JO7UyYWgX21n+eQDQsy5ith7aclxkWq91D3msjLmN0mBATRM\r\nrLn0lyRWjAxiD2+SNUNadHX/uyte3sWEtvpi0PwpATDMFDgpaGJFBymFragS\r\nz2CUDHq81puh8XY8NagW0iS1TpSKQrBB+DWdiDdJ7fLJGV2o9YVpNByno1lW\r\nviorsFf2QRIkGbeGytRKROBMAYsN6+rYrRS3bx/38FbPIuNwTcr75QZjCxva\r\n2FZO9QLQpOZ7GjwFQOv5Wfj2TDSxTbKamF3oKl+DT6xr8VW7lDEuMFtKaB/e\r\n87QQutILRWjvCgi0uTvzqiiFTqWSeuBWdIrW/Csf5EGucFkEeMGR06xYx2s+\r\nIoMf/pR2DWTb9+M0zZCW10ELd9TT6HpG7hAXSmkHveBfyZqjOGRLZjsxptSS\r\nYoQwg8PfqwP6kkWMrdoiPaboRSlYl9sOvHr+sarhJfW7SzgMyJpbLNSrwmWW\r\neveof6SHMeS4lnGRT+YSm3iAnSxIG17ypynmlIDvu7kFfgFAdBdB5l/qasp5\r\nItT7vFfVADo1S00WuppObzCqVUmcgLksJVnQGibfhhVNO+NaJ9oEk5iaP+Jc\r\n7TabGbJmMNJ3g37WZoUePQFujWCvoPzEBHaD0vuvvo1pG4EUY2/OW5oeMkVl\r\nL5HGhb695YmOKi4AX5AVC8E8AymVIIFy2r4=\r\n=vNss\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-collection": "1.0.1-rc.14", "@radix-ui/react-collapsible": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "29c95ed90088f622085a59baa6d62771d1da61c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-EZ2jMh9H1b0CQk/urk13+YXJOlHvC0lsaxcizgBB++OoHxFqQLnRUWk2tM0Gg0lmeQRTY+cJrXPa4V4vceLTJg==", "signatures": [{"sig": "MEYCIQCXqGoyqvtybj3cOQAo7fJst4uPbPMszMXtrcw5eMZz8wIhAIrXl1C9yfLvuERfx5dxErQqv+DY60jCuGo5ozASbE0F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoORA//dCJfUo1HYKiv/cMRsZ3yMJY6MH2Rr6agPSfxbbMki4awHZ5h\r\nVDhiQVwZJiDkakHdOdiHNUPS8YBa9iIcmztCU8dJq3H+Z/Fk9O49iXd9KCQE\r\n7/mWYmImcIoglJ3gbq7vQ20vsdQwo+nOJ2XWbzvvcutMzYWeuGYji90laOza\r\nAJEQUdRoRkSTroUxREwlDpl9QBjve5WuEtEziN8jFENzjftKHFqEKFD9JaFa\r\n4x7uNNiSi83p6sTnZ1aWwbI5ViS+0hqcE56Ayoip+jpDhQsfFPvPsvVhfjIs\r\nRcVFIDC9iQJeY/8ImJYVdBVU6VIiCXkm7nJwr6OtVjip0nkXcbfn4rdJ5fqE\r\nnsGSv9bD1QMMli8Q7/RosKJlzJq7Qc+pd527L9qsTM+tnTn2JBNajK+2Wlyn\r\np8a6zpqYd9ZCDGFSaKyhb+YXQLL/wZkyci63BvzGqu+sC1rZ/yOUIM0Iffpi\r\nuSDh+Nwc+Ocr5k0WV7v3kMLJEMdmt+FFPiEPJfSc5aftL/YJmwKAf2uMUwQx\r\nqYX8A7Is8NOlkSwZ+viRsI+a+QSrMJHOgCRKNkMc8OB30QE/wnhgu5u6vvkm\r\nSQN7Ry8e75k9ExX2DmNhehxPzBrw3Hz5Xsu8Pm/IiqXrErGOYIc5lq2aA26/\r\nENeYLCyztz7YmG7AncQkxJ3GSMkDd3US/l4=\r\n=+7mw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-collection": "1.0.1-rc.15", "@radix-ui/react-collapsible": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07d93c76863ca88d1407810ed25a267564fb9361", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-tWN7w+CHFdSpwX+FOE0Hdr6r4/FkiO/fvQth9Ta89IuzZCfCkDb/Lr4Ye4d+NHBxGNQCnRwL24hlscrSYu4Uhw==", "signatures": [{"sig": "MEYCIQDXXP/qVJ9F8YLU3/QFE19gqHtE+PxPs3j/JidzgGuWcwIhAIjVUMubwmkb8N1oyEwPUfuadGGZGdyVufIyeZ+DGm4/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY5g/7B6/kMg7YYuLPDJmKZxtLocGHt6GjuJqr5TXhV6dVZvVMaCYo\r\nolizaLrVbZNlIiC4YWJQKOqR2mRZTXs1YbW3VkQk2+eajRWGN342bOtDpFT/\r\nn3WPNhJBtdJj+bHevvBK17KQTCG+qJJIS27qXlNV7S+UugAJyT8BzYXTYIj0\r\nXJCUmbhtgAHQTH9XuP+YJwQIcYZc7FFCfkcFNG5EICfTchlHaobd3siAX/dP\r\nV6HDbE7sBDSji/AFz38Y9ET9zsosHmoK6nxYJTbdtjyhMckzMkm9MqrSdCy5\r\n7/kirAefRbN2saM+vkDkXhQoNNeevlt2l7aJ92XYw5Z+zQdp09v6ZfmU6+VB\r\nqGtGm0Wh+8XXrYoZDBGr4HO9444oDshM/kVBMD7S6Q3dvKhfhjq6Hx5CJTVO\r\ntegZSbLEPMWXTtD/tO/Y7+WbJ6K4X7MkIQG9xtN6xsYqABTubq4+d0xyEiOp\r\ngRV3QNH0L9RpCq9EKgVdv5RvLYXrjyHzCU1Ebjqshn/rdFDxv+BLaN8atnMt\r\nW3gvi9+XjmpaClE47iyu09pEWJPdmqHBwBIm0KVdPYRa4def2/sBZ0AKEcPT\r\nBPd3R0O42sJ23c5NMuUrVBqgprySr9JcjxEk3GeZFlAij+i23cTVTRs9fhRu\r\nc0BRdVDl414eI8OStRtTsrChphoBkrUDSck=\r\n=lVBt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-accordion", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-collection": "1.0.1-rc.16", "@radix-ui/react-collapsible": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "590f6264032df233b39b96a3b2287a4d6dff8c93", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-4ryPQESdfjpJKmEgiBNQfgESNx5WQbZwIoFXOZuFFqcRQwlMTW3piX5yo2fU561oGXv2+cbLzqscWb8Hk0lPwg==", "signatures": [{"sig": "MEYCIQDiSq+FW0BazQBF4kT2phdx8u4eNee4FrVLWzQYAZSXAAIhANDWwufUPRDSQXg5EtHQAzOza4nywMWYsE4mcjU98e8r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTReQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUWg/5AQkgCbVp9y+ivhfhjW9aup6JLkwn/TIhCG7o6bbaoigSWayT\r\nNxrAjws6+2IFPauQWV2mQM4fRfifRekyRZKkcpRLXMKGPyVGS2axcCLbvLjz\r\nZcPyBUj5jbIdbDcTmmLUjWefClC/MraLa9LvFHdJmVFGgz7US+tqhoyJ1chI\r\nIqro5V3wU+c9Ve07xcjK7pNw1VMxuBquWONc0RBEf8AemhYBftPIczHgSATP\r\n3cGLfkZU11j/E7dVgVIgRlgQRebXslYHRjXb0HVdYLmKxVnlFDQ+fEg9J+gK\r\n9ANTCyI7HAXcPERXtJzYfvh0+PKcZDGF1I7L/NACseuh6ZWSB3TmknUMtS0w\r\nFrIVEu/dU/XpiTVgh0hzQX455n7e5Tb7bBUFOkq4PWB/0jGJRO2+4aMLmMKx\r\ntadnkqgMDth+5LlPaiG8sG89O3QK14+cMIHC3fO839u83zN15j+V8JbSK4pE\r\nqo9b+IcCGYiWC6uHefS4ljpj0zs52sN8I7ECiFfHY3OyBDL9UgyYfU5qSL1B\r\nuepPLy//C32hY46zpV38/lv2odD8k78MUAU60iLPmZ4XO28tEfmatEJbsPIG\r\ndURugx2UNmXP+UTL3mTqWW7DA0/sw+tzM/yzVnWEbDHLeIPJ2LNxhPzN1+HX\r\ndNVhs1Jbtl0ebvNgOur4D3YhyeBYtkOwTxo=\r\n=urVp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-accordion", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-collapsible": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d956e3adb9ed32c629d0ebe4c771a80843903916", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-Ka7BQoyRRPwsOb0YEv0fQU8V8aCXCxAzNVI5BRN7WmPG2E1zQKKxmb86NVDqIj6uSnaahJ1E5S6bX5Lk9hTK+g==", "signatures": [{"sig": "MEUCIHxUNvchzgmxYdpzDedkyVt+qXUmls4pKTKoBvDiyD96AiEAo0aufgsIVXRtzLZb/QZrTx7ta4FqObBB0fmcVYaAwyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103632, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0KQ//R7Z3lvoHtgpqC2OGtYYLuEInksoAJDpAmM5bHucgbbvoJYp/\r\nmGCJWGwWtza8RDqWvW43IfpNB93rDR5Ugp6mgsKXniMR87fbfMcq6+C+6JN0\r\nKWqfdGRrpBX9g6HvyVogcmcDxHw8uUjIMtYF8pfDmSTz8UtcpwAXw5Gcuwvu\r\nID9NFZAD3s7VrwxpFbmS+jQnUaHZMB7aqBvDWw8CJmBkmJG2yKZrQrQ/YH1q\r\n0rrmkVeRQFmgXStM5QHAaap9kIg/1LdwYmQgeDaYu0Epnt+Z6l/Pxm9u8z+X\r\nm1hdw2VOyrVKChCVLsF81lQDsSsxwGc16gdT5uDniRmEPa/YyQYxSUK68MtW\r\nJ50MYJ21XDlISmtDVe99e4XmJeV6C5/JugMEAPsmM7hIMrt0vUUsrynSn049\r\n2tOmaPZxD6xzF1AtDoWUBu15BXh1AseOp6dQAn/1X666O0pJJimjiRc5LFgM\r\n0XQSMPvniuyk81+J/BiF75FrXp6TeVdfiSpaJsfRVRHh424KlRreufuTav2G\r\njp1j4LjIhhJ3Yw2WwXz2mN5AoTs35MmvItda3a9SH2Y4z89oX6HBh2YXBcCU\r\nX5PEe7Nqaut4TJPhZSWz9ddSZkhlmqygiGOw98bivNpbWRF4aCNINJ8QUyzd\r\nOMP8c4887AUpg+bDCDhPM1Ji2yEdJ2Avc2E=\r\n=MK4n\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-collapsible": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b331750d4119aa353da09b00567128acb02aa9b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ybpmWsdvAAaYwZIpJDb1zry3jFQGiY+EqymMvIF/vgVZMneceQhEZ1jLeNU0JZCbtkVXtC+dOfgj/eTJ9+AxSA==", "signatures": [{"sig": "MEUCIBVjDlOfM+iKEMcULuc2KSQ9OkACIP2hr0iIqN4X0ZsIAiEA/7IvSzfynP9PHgJN/h5Vbzu6phJ3/uojIA4M7BXzT3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxp1yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXsw/8Dj8iVfisMsge0Q2WmjCRFJFfnFaT191ArRgGCTIgaJfmAXNG\r\neatrQWdLebsfDFaZAsDDRrqLmDej+gJVHvE/AJ+fEMZGatWSbmind52kjLiL\r\n7lobxEgrTIoeKEwqutx+uCvZBxB3/V4Wh1Aq2qP9u66kJqLpwoeI/HeYjRZS\r\nq8nPfdDre+Tc93PnTaZWYLZj+y997n3RXs0P9KwjhICC8NgiStNPvQznTVa3\r\npPeS5cXVeEkCPfT79Hc6vnGzYY96uAcJZbqT981OlBS4kbLF9khaOUbrviTr\r\n5UVR06oY2h970aFksBd08pOP0U0AyUsXAHTtiJ5t5HF3fIvUlHeeC/3Ac8Jb\r\nycTAfZ4IDQDtTGON2dozqjldzlLJcRSJI+m1u7Ts8YIawFnVb50SAQLDS+kk\r\noSsD+lSb2PBzBoY4K4mQMlKDT0ul/RKpPbMeXaGXmqfVT84eQ823mAt+Buph\r\n2bOEGNhrxcrz6BXG7UV3vvNBFNPWmKt1MF8GJogDA1MX0yawMsiOElcVGJoh\r\ngXZAJUHG4pIZp3oPtRsNTtGbd1Vuuk8nDo8dxLWt1/ML+wvxxbtM+gYme8Wt\r\nk0yezdWZcRGj3FnMS2IVXGQTPNq8Vuu3ie05XbkFNQyKMypaQCb+m4W6Mc6w\r\npu+fnMWl8qU7ng+CsXuygpca5/1SGkgGpLY=\r\n=v/br\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-collapsible": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f07fc9d5529b50092d3f255b9e88447f83ddced9", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-WmpyKMbQs+wOFaBNXm37mq8NnmQAuNKBLlw2hpzmRHu08j+PWLQj02S54+HSY5TxexN7IXAvjiX2i/eG2OohGw==", "signatures": [{"sig": "MEQCIF12USftZ1rZixPQkolVtgD0FP+LtFvNt6d0zGOvn/nXAiBbjQG5q3y4UoMoUbC31EKLLODlSptnO5S03/fO9r/pTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqEFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3iQ/9H5/UibXe4vgIAya7VSo6I4ZXW+Zg6jf8wz1NU+ggS6rQle8C\r\n0eFZ6PBkOEKx9hCZZ9vXU4lzAlSTADAMNIul4Y8vNe9gC9dObQp2lbYoiiFz\r\na14atjOaQFV67ZI013bfaFkAxxFxZ1ap7XxZ2H0zgDnrE9OfofAy13i2o0pB\r\nBlL27BTrrLafBX1AVrFDoulWOMIzQKKnHO5MBqMqUVk2Cf7avxdpVVwgRGzE\r\nJoADkSGAn4utmXHo0TYAK5XLYJzWIflBC+oZP0STDcZM+IShWT37JCsicWpZ\r\np9jQkPKYPo8vNsDv2xSPhRaW1MlgeBStZc3vmbf1l7kLMYhjgBI3pyuwRneB\r\nr43BvGo/GYAweFy/NolK+l8zlHcEO+M3+Tk++qJORvXsH4lt2DY80GlsxdRu\r\nVTN0R1UviAmnmnMiDC9LM5WtpJsUho1omaHgi+zdgojj1nx2OqhZDTOh2Sqq\r\n+TeF3WPhD2yPfVRvF20NN2APydFlsFQcUgg1DLdSZlZUjAJw6eEW3l7gc1gJ\r\ngZ844qUtYQW20jEOCyEP97e20ax65TamDh7j0UWCiChz9/kPWZOSppSq79ks\r\n0CJVPrgnfYKnDsV52umMCDhSPDq9lzA4aQ+9wkB2ZuumDFsEYO1jiLMSvMBn\r\nChSBHhl4ZKHrsRVYh21+z1x1FPvZOfEfyeA=\r\n=5oP9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@radix-ui/react-accordion", "version": "1.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-collapsible": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8db063b9eaeb32ca90ffec74e190dab104b56522", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-CNN9ZBgCK4i4SX7gFk5s8095j55DUWi85vwRNfkfBLs0QdAG5Tb4ku6sBeugCAiLvsmxw481GyNl+C3stoJVBQ==", "signatures": [{"sig": "MEUCIQC/xL/cbW/lboBJ/J0TvNkF976M+aC6Pwzoc2Tw53e8jgIgFR/Nqkwm/fg6RGcLGupkVZZ6RJpRLgutF5xpDagG8+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqVPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9dA//YFewUDEt5VBDxJYTuosug5hDj7EfAT5u+6vxIuNzedqWZ5xZ\r\nzWWrXMzVRLymkSrnx8AE0nsVFJt3gAphbIO2nKtf91rKBQw8F4/Hwbug4GJ5\r\n0UVLU8N7Dwu1Zjp3ciyQoqkGfDCnN03MDySFJaBKys7aevHzNA7ZvFJCb3WY\r\n7DXcB5gZ8Ne99YGwn61UVPZHK5ftwexvdFLQQo8FYfCEJ2x6a/5NlH3Zq/rh\r\nMJ+2Drb3qmMYB8nZplxOL9W4zd/wSeyBkO14G7T6/kJwjqlej1h9H1Nf6au4\r\nmqZxCHfJJe3Qpp4k18Tk1vg4fv3Ii5UFLzFyjT35DpoR344/PL9KLtEQ2ImA\r\nMB39nXsXLhrzjNwWygpvBPdyu3xfY4DwvngIjGaDcnjiw+gUd7InBxI7kX5E\r\n/1xJ8VknU4EAE6u0yRLC4trlalpKMFPtyHwsw+TERQ4KIIq5i2y6K9GhXiLF\r\nmgKjjONmMdWERmGNiWOOmlUDGDe4UDBdH61ENuKGBqMqrtwiWMCV7EDzFtZx\r\nuTJNbxWw7DWraJjH7UCrmAKzdTq4yqWV/cqG9EivhpslvYcS1NnHM++VLVfv\r\nOUzC7wCo4zHMiz5WoNcYKYo3emMF0QRHejIKr+8JjbrucuFLqQUURScxBLV3\r\nqKRPFOuYdhWrlKU0V7UpjhiXSNiw794njl8=\r\n=ASuS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-collection": "1.0.2-rc.1", "@radix-ui/react-collapsible": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f929fc9f36584a4c525c7cebf1d42cce2339702c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yIeF5ozAc1zgtZLqiic9pI93vM5kCbkjfM5fLymvYsdXGyw0FCbnbuCj81X1yxLOZR4xJK24vHNFgOVCS8iNOg==", "signatures": [{"sig": "MEUCIQC5S4eNuAnLAJaOenaDmy08PLTzWsxgQUotWPStify9dAIgU6vr1yVN+W10KfzwtEJOR0DoGEw5Ho4StmgC9OYDUFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzeqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTlw//bik40tEVyAyR8/zAf1E9kBiwk5ebveiOVz/2fSu7rEz5mXQW\r\n8JXV0te96WnzTJmd8HnndOCyTDnqff0IlsulLDOmpuhqW8UWSmVaA4YesnvN\r\njRVEo8ETX9NghpmhG8Vswd0fn13kitLd573d+mwpGFc5pAEou2Ukza9PmGNZ\r\nLuQW0cE62CVK6Lc7lWPibFH3xggJ4vf3ZlytsCymmfwAfRk/PdrqfGTgyi0x\r\nVif56gMhNQAGvHoZ+6UCVeH3sRJyn2C0Tvdgoa4spxSd7na4a+n8YKOdSOPz\r\nDQRQ+SrMoZLZrEXAfLJhAiXxtmFsqIMQlhE9D7rPaK4Csrf+V2nkjc1+5bZq\r\nRnPXxqptEnmCic9tZgjd3IT32qRqfhGS0CYfbGJZaa/mZhzmu6G57KiwqVZo\r\ncgRafEtCvEdVz2MvCY1CxMlS8ZEaBQmyd8910IojH37L3lPlxYFjxgSjFed/\r\n+e9SVGAqaVy0C0TEnshDgnidJm031v2viIubw49B+tlsgMwp3xGREuXX6Oh7\r\n6FlFVvAwEHIsBBuzbB3TunCDhSbielD3u8W85MV0018LvONz3OqFG/ZaLuBu\r\n8WRLLPA1LoZ4Tau0Jb9t4fDJzDTFwx9ddwynASJ0LksM8AgL/dh5fxQJgeLI\r\nWbhoY997Wo0YaSvViiLfjT0wwfJDkiGM340=\r\n=2o91\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@radix-ui/react-accordion", "version": "1.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-collapsible": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa1ab1b5c6a29aa75aefaf306a9e72fe3a482dbc", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-TQtyyRubYe8DD6DYCovNLTjd2D+TFrNCpr99T5M3cYUbR7BsRxWsxfInjbQ1nHsdy2uPTcnJS5npyXPVfP0piw==", "signatures": [{"sig": "MEYCIQCt9Obb0ulXxMhb1KEAGckoRXHYdCHh0KzHKDFtNx1u9QIhAPQ9nG7lOpdUiBaSCpUS3YkH0Kkywt7+IEtQEW8fah0g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJaeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYsg//VUPuwkMPUOK7fdXoPEYsXjOsDaIdNAtfb/SyjjJB4DLjtfkN\r\n/HZWVi13F/qHZiG7TYDzrNLzIBY+VFJE2/3YBUsRiAKjma4qMKupn7gtUiep\r\nu+64OqZKlqyV3osjWxqQVmVoftORrtg0ga2g4ey/TWMXKK1wYxuQY9+vKCs4\r\nbuOOHMoMD12QTyQwtQ/liY8H3Ru8/3n34nyuniGoO4mtuP3ERHW24WCCI18Y\r\ni+nEBE42X57+6EWOJOGKmq4IRyyNdqxbgB3F9d4GmtaqBtIJb8iPTURIN2em\r\n028vAC9ODAeoFGu28sDsoVgcB09RG9SjkLpTDPjIUMmD2iKxreiJrfgpxXtL\r\nUKyv0PSsRtKmUPRxmQJmlpkKETAcoua+c/FfESGMbJTw68+RBqHinvoXYdIV\r\nymSyxjCDgtyfCBbrWCSBOH8wySRoUsYuCJxudpN8yk1QKGbGf6HaYX7KLqsT\r\nd5gzvUtIBO4DQM4kzmirT+p667EI96g0ohzzgBR1J2+0dqjZFdNOJ/Cnt56g\r\nGhwUB9w25y4uH2qXU4fAg3TO+21Rl2rC34oOlQbpEwmiQsMQVuuyubIo/heQ\r\nufjdMtqSwvwsvBvx9yqgzX268h4EsJpECfPFgjkMomqyAx2S/DnUpWJ4nwln\r\nWOYu3QMh2LD7pMpu6Icf/qw4IVDDOzWm8q8=\r\n=jvL2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-collection": "1.0.3-rc.1", "@radix-ui/react-collapsible": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7c11ef96f8e8b065624b08d4d4f9e878bb3d22c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Q2ULkOCWcLTCx4qgYBpH4ZUoRzxishpoFlJHzg9bSRNQfwrk5Vn+//MJC53+flFH1+9w5UQdkndFNwWdl6Eyew==", "signatures": [{"sig": "MEUCICyckMZOqocuJEXH9Y08EpCkVb9Vt2GK+BjKlXMwe0IgAiEAo+NLp3s7EKh8ePz9HlSwm7qhPpsHlAgR/9BBRFqHFU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrh6BAAjOg2T/08tm2IukxnVQF4UDh0p1oHEytb8uCLk9hHP81g3cRi\r\njeJIrumj5X6P05dKDN+3d9dad1L/pQzAKgs+jb6n/qyYDHc3LdEEjJUxrTj0\r\nptmhp+ryQPdXTnbnnDKEgCywPKo4L+MD1/ooECRYztqUGHwvgrO1p3PwomRH\r\nyFhH7l53B92ZWI3BSIv9dZ2pYZjJVKzNIFLFuFUBOHSjoommHEG9YakDVows\r\n8u9QFptM2keqgl8yHQhpebOcQzDBhEODebKZKHgYCrN6Sd4RS6BT7/r0bAMy\r\nl13+o5gSvKyyiOrwifQpMUbyHev8RmBjJLJQpIJrWaxMbEsztI4cXC8s2AY8\r\ndO006M+Nnv+vuzTtcCQIOCnKv1I/O2EwvoG3eOHjV2cB9MoWY3rXjEENXtwL\r\n4BTuv7BzCZVTXavg8Nmbm3mPDiDRcdTqN/6BTWQbawwa25mobgzTxDlytEaj\r\nveiR0cBFsMVZ0iLvKqCJ1qIq2yhhp4mXkNFkc6HdEVlWBHNQ8XdRnPKh0jIs\r\np5uGajqFymSXdMtS8YN6Ddmb0uLY4YiZ+Swaodoe2VgTzpGfwUXvKR1aGtCs\r\n7MI0yiu8Zf6DwrlyIliAYrx16VAU63Gz+/fWXBe7c9bW3qk6meKsDEKTtQW6\r\nwcMzb7G0Mq1XK0Uj4aERN1gs2BFsv++3Q+Q=\r\n=2zod\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-collection": "1.0.3-rc.2", "@radix-ui/react-collapsible": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "054c53b1d1d5d7fb6fd283861a92261cf30bce51", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-3MEpsMOkoZACvpdj6iUQZIxWtuBkHE6E+rQhRVa3mNJdDtkl7Ay45HhpcBCOx0yIY6e3KAZuztB7gojF/Blexw==", "signatures": [{"sig": "MEUCIQCzidTi20nC24W4ZJXgqAJGuk20op7W5qmopbicuukN3QIgZt0qjxi+4iJSzoceulsh+OH/2pJqbeH76UIYR91hnwc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114146}}, "1.1.2-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-collection": "1.0.3-rc.3", "@radix-ui/react-collapsible": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4d853cdf0067b7cd3c136d134901e00af478f990", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-7ymMHiggWGoOi59OiZkr+p1nGHryTOExJky8TppMAptuwg/7+ofZ9t47tO7wKKQTkxMk4FY1j/hB5qzrX4v7vg==", "signatures": [{"sig": "MEUCIHQpzFgpsZmySihB+cJJcxvk4pR2VBoj0q7zfQhts6ohAiEAqke/EkljsZ5209hccGLpLWhVOOehwxUvk+LYhPtvtyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114146}}, "1.1.2-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-collection": "1.0.3-rc.4", "@radix-ui/react-collapsible": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1aa62d47cef8d1e68d5a943471b69fc706270a3c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-fyELWlxYUVb7Yf94MuNk82YT/Cs+7Aeny+Yvj5WIgAnXkM6gTK6IuJwB9bqOo85s+2KrG7fT490xLkZEkv6eKA==", "signatures": [{"sig": "MEUCIEewNkfwuxEJFwsWZCmNd14Bvfy4sqcEFIrSNMfvUQdsAiEAsF/sDlMZ9XhYjbliiyBhhcDwusGqK+LRsBocOiCOjHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114146}}, "1.1.2-rc.5": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-collection": "1.0.3-rc.5", "@radix-ui/react-collapsible": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b91b5c8e224a952c6d62937a036a40678f82d562", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Es8R3zrZsY+wb1OYWMTIljajl0U2gc0TLx7cXfqA0pe50ZE96aV71X3GWR3BG9HPv6sOV3UGz0FdON2wDkO/Tg==", "signatures": [{"sig": "MEUCIBALL9knQJ11Xw7ryLy7osFP9uwjd/qF+RfHuS2F+JBGAiEAq6uYjKKYXtp423wJ0mrIYIVIY+9vYgbTtdPbUum+P80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114146}}, "1.1.2-rc.6": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-collection": "1.0.3-rc.6", "@radix-ui/react-collapsible": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe75686721056c12ca8cff13f9ad2645a972f20d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.6.tgz", "fileCount": 9, "integrity": "sha512-uTrGNP2bpQHROurfLg91eAYBeHjW2egevWx2bDNNxn2wPhDkZJdHczyo6IzzRq1gET0cUqGlMxNoCGmfYmtnWg==", "signatures": [{"sig": "MEQCICS5NQ63rbspklRRrXxLn4trHQC9qHBQD6sS+bw4cy75AiBIoDYFYRiMoeXMvBGliW/yGU0KEMxxAt0PFnYObj20ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119446}}, "1.1.2-rc.7": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-collection": "1.0.3-rc.7", "@radix-ui/react-collapsible": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "30fe7e51b7b4b7ff67df353406e22b4e6f2b701c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.7.tgz", "fileCount": 9, "integrity": "sha512-1gBlP53J2OYZoioWcJ0eZ6z7J5OUaaWV68hUYOIXXh9bniGalrVglUuQEI60KRsvW4rlCDbnRL13YdYxELHooQ==", "signatures": [{"sig": "MEQCIFg+kaQ0+G0N2CUG0qbmXdFdpD+FPstkm1kUOb/Dynz0AiAcJuz07W2SE/A1IwWNIr2jxaqWhEWHZ6LmmsnXGjTezQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119446}}, "1.1.2-rc.8": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-collection": "1.0.3-rc.8", "@radix-ui/react-collapsible": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "72740addbb429532f07ede45fcffffca42e016af", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.8.tgz", "fileCount": 9, "integrity": "sha512-I5MPXF+oDttOM88oUv0eZLAKHrinYa/aIzIjbTNRsxh1a5dh1F2cIhAmYMtwhOcNXqrf0O7i1NuHo2I7e234VQ==", "signatures": [{"sig": "MEQCIEabWKt7+RqifhP0vtrw+kCtzGf6WYVBqRmKpPOj2pIsAiBCXI9tjdOKe59WgB2YIolqECIZFjyo8odhDLNRaAhlxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119640}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-collection": "1.0.3-rc.9", "@radix-ui/react-collapsible": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bda4555a10acde124db5adeb78bf3437412d8e7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.9.tgz", "fileCount": 9, "integrity": "sha512-+vNisGEtf4UczVSAk/sKs8RGwZbtbnGQb9+zFpbuqoLa0YHJbo3hXMoNmKosQcRliMRT6swYSA6vUz0aBgR9YA==", "signatures": [{"sig": "MEYCIQCD9F11ig9dyE9YeK+bnFWiMZ2k4dQY59XLV+ldz4CkCwIhANf+jFi+yzsOG3tlYXrO2pcHMRtznr/wk4hHWeKuGDUe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119640}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.10": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-collection": "1.0.3-rc.10", "@radix-ui/react-collapsible": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f2e5dd580f6c494b5d5585b1baf1390d99fe8f8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.10.tgz", "fileCount": 9, "integrity": "sha512-H7O5hCyVLoeaMMPI4CNnoez7sLwoANlfcxKZrVQlx6rWvEgMXL9yYIAVwF1kuCDBejDybBwRch7c58G5u8CHIA==", "signatures": [{"sig": "MEUCIQD1cSMiWTfnjtVN6ufiSYnNC6kZ7p1xf7SHRGC34h5nwgIgLM3XnAYUD2V5M7w0r8K8wQuURUwGD+7Rjv8czP+bg4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119644}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.11": {"name": "@radix-ui/react-accordion", "version": "1.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-collection": "1.0.3-rc.11", "@radix-ui/react-collapsible": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eb6a92b00f2976aae9ad3591058877ca5346181d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2-rc.11.tgz", "fileCount": 9, "integrity": "sha512-ciaIZU70Q5TlCSRBkds4At75x6O7+dzKeiuSnO5BFnnQg1p4iQkW0k2/r+lY22e0wHRrvn58XHDFGp0suy8jtA==", "signatures": [{"sig": "MEYCIQDUqsyb2JJZ7o/7KFQgEDaCEYZ090apxBHt10o5UbIzSAIhAL5Rmz4/sIFSgjgNKraxcumKQmEtw0F2+l5g+Au7Odo2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119644}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-accordion", "version": "1.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-collapsible": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "738441f7343e5142273cdef94d12054c3287966f", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.1.2.tgz", "fileCount": 9, "integrity": "sha512-fDG7jcoNKVjSK6yfmuAs0EnPDro0WMXIhMtXdTBWqEioVW206ku+4Lw07e+13lUkFkpoEQ2PdeMIAGpdqEAmDg==", "signatures": [{"sig": "MEQCIBl+hQ6z059b5vGfsniBpd8xWBxpJJU5WTIovj4s0a5bAiBxT6TRhDNjghCTnsisXYQJxgx3P0uMAhFVw/Wj73SNnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119562}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.1", "@radix-ui/react-collapsible": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8e82470ae7435a014cc06e4b4083592b63f7c69d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7Ik5nGW8HCYGKtnnNRF0shJPRTqb20EUC6l1hC9N6KZeTneqyhftyW7M4WcAIWd37SybgrKVu5U+NQe+T31xhw==", "signatures": [{"sig": "MEUCIQCiQiTjKhHpJYdpheIppYQOfrG2y7DzJpHf96Fco9rHUAIgSLQKkieuHv+7hRfdaPJYLOOMPsWTxJ3RJ6ZKVaoY+0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93153}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.2", "@radix-ui/react-collapsible": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "46d5e831157ebc946b45f37ac5eec5df2c23e753", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-UD7+oZk/VNle564P9Pe7zLVyLUErSua5NyN/IWF8A4aEw62m4yhWOottIBrzgu8Wq6qJ5iKNMPxTmt744f7AjQ==", "signatures": [{"sig": "MEYCIQCj4RntnWknQZqlIcylTWZs+cWZ8p85sZXWuATqQBYBaQIhALG3WupyXm6m+JSvmaepS76fSyEznp4Gh3DTyHJWdWTD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93185}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.3", "@radix-ui/react-collapsible": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7138aaea8ddd6e87e3642325f3fc2f7fb1b306f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-keqEbzSfi1vso/e/i5WJ7ypCjpqzmTsD4OWGi7yUC2eDs70nBaSPR7jUpYvujgZ5kvNCFUiVOCKRJys5W2v2Wg==", "signatures": [{"sig": "MEUCIDQoNndo63FRtyn1riMMd/3sUp45Su9OnCePSyzT5LB/AiEAtnC30tDywMjhcAfaB6GsVQtau1PtyBTdPzKblkQiYJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93319}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.4", "@radix-ui/react-collapsible": "1.1.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7434b5ded48900c7a8283a761417502091314eae", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-aJdiByl41mZLQGLLra/9TTgeXIPuBTtuhR8J6X+m0Wdqi977Pfx1ZEtvqi7ylroHhc9j6c5IhyI3/FRKS9eCIQ==", "signatures": [{"sig": "MEUCIB9eH1T8XjJJJOZ/SfFerq5a/7Sao2oMTVxK7F5bXUOPAiEA8sCz5czmr+pIwgSXlhBkq/ur52ZWcTD0PqSsAij2vTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89195}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.5": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.5", "@radix-ui/react-collapsible": "1.1.0-rc.5", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "19fa56feee5c95eb39a80c85dd534490859fffcd", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Uyw/IknE+un3pAmqEGSNtRws5hKaSzrdpsw9209PV3uA4Hva3Q7VzrzWd+qdoC8MCj57BTY9osCMng37iIm2pg==", "signatures": [{"sig": "MEUCIEkpX/ev3Z001RnMSew8PH308W4mwb+pXl20XWsRmZUaAiEA0f+uY247uTo6VQZSQtuH+OP8cJfnz/YMsMur09/cPuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89195}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.6": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.6", "@radix-ui/react-collapsible": "1.1.0-rc.6", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab17ccf2b87aef7c1bd69aba2eafeb727c8d9fa6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-cDfDl6sl7UF/xTD5uACKFjNG4en6nThb3jtSswjS2nsFeQ17OYoXZuI1aU5zJ1qhcTG/FtRojJvaQqiHtQ8uQA==", "signatures": [{"sig": "MEUCIQC3o7ytug7QpsdgTmb3QyARlc7RX6XUfPuf4/IHi0PrQQIgUAxpLPlB0x/fDnq2OFup6ZHwePoI1Nw56HkpjQTz9BE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89195}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.7": {"name": "@radix-ui/react-accordion", "version": "1.2.0-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-collection": "1.1.0-rc.7", "@radix-ui/react-collapsible": "1.1.0-rc.7", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b189a16635a1f8508e4aa6e8ac7ad6e860e0fb32", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Am9/nzqI+H9WuO+V6PviVDYQuESSjXCkqhplZJ7LgwKoMLJyXgwDlZHhUo5nnI/h9xN8xXsOSC3x8EPAmoeafg==", "signatures": [{"sig": "MEQCIFT21O1crDXL6Rx8+poSWG5S16/ZOJGLNFWrLmfGiSLaAiBsDACKZBu1lsd6N8q1/1T5cBWr6GGvYddxXi/B+rAFqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89223}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-accordion", "version": "1.2.0", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aed0770fcb16285db992d81873ccd7a014c7f17d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-HJOzSX8dQqtsp/3jVxCU3CXEONF7/2jlGAB28oX8TTw1Dz8JYbEI1UcL8355PuLBE41/IRRMvCw7VkiK/jcUOQ==", "signatures": [{"sig": "MEUCIC0pSXwFeCri9280kgopvaGYP+Cd1oJkWhAxdndAdjPzAiEAxHsjAo4bYRdezr8aySmPvfN3RPLHIWfp8askkEQ77kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.1", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "65234119e1c8fcf418a89f81eb21de98c8bb609f", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-D5RYtGtxKfhB1rQtO3h88LKrP+su3j/xBsVS3L54ce2MBwxsXSoN/B0joa/xTYkrhxCE4c+el+RCe+zn/HqGNw==", "signatures": [{"sig": "MEUCIF308ykHoFzmB3bOduSv836YdYX7wkIcDr6vMDdnl2rRAiEAuV/zzb7hdIIn+A6QTKzzE2gjCEmZzxY/nd3AMsgxEmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.2", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1dd53fffec593107bd3996eb73747405e772462d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-IJRKXNBeYi4imbMdphZYoZTKTHfT8ZznK6s0aBwEexio/tQG+D1i8I22TfdrYcQe/gwE1JK6fXiPAJyu3mZK+w==", "signatures": [{"sig": "MEQCIAPSrUkOhYdk18pbnSdgOt6A5bR3Z6A3bexY6MJjbVpvAiBDi+3otFNbhEC7+luJcwgmoz94EqLPz1r0zHljYehBlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.3", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "34833bc3dbec5a02333d02ff835122ab807a4546", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YBJm20Evq+NzlLw6NEogbGakdf3ZzBFzg5KuBhtVpS6UvQW5aOiBNTe7n3uq0BoHYYuQ5BaBnCvwYGrqaXEgdw==", "signatures": [{"sig": "MEUCIQCE1dU1d7fZifJdzK5z2M29x4682aJ/r3jUoErpVDZcTAIgaS/SPqyuGEC7ZUPTJVrasFNrjn/nclMLQRbidxdRrGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.4", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98ccaca1f3c238fa76936ea320cf44fea8f5ad40", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-HMFmKILN7hhhwrL4rU2rZTVBjsBVmWgkDLg9VEPGyzNPAUkv5ATsmLdSvlVBIb7TRtu3j17kjiP2kWGApL3xng==", "signatures": [{"sig": "MEYCIQD/I3PsWQLFBBFzgLc4lygpN+EgjHA4IIVaLIpFlD96RAIhAOPeTIhnNEWndmC0mkHUBlTR3HpfpLJAVx58aoYgMHls", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.5": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.5", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ccf9cc999edaf5c34e9ab1b75bd78814246babe1", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-He03eDTFwQsg+F987BxugSeZrXbvMsaB/PjY7Jcw/PsWzgX4ackkWLpe51UegEy0s7Hzu+0OCW0wjz29XJV4rA==", "signatures": [{"sig": "MEQCIESYqJw1fKJssVjDtDcH7PpLwfrRTMEJj7m/gTV+rwWwAiBqVfVxe5Qx0+ZaGuh3gvhoJ9L70xKD4pShluXcUngiJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.6": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.6", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "73bced6cf0d74e41f1a3da96afd5a76fa3ba3c90", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-CsqR8uJkkfsrZXjVsbvIQ03WMjnHYDlymadTUBBl9IctmM35vbZqZLqM50a7AfnW0DJ9wXdLv/ZdNzPjImrcLQ==", "signatures": [{"sig": "MEYCIQDiQqQ3MIkwxuX9M2QS1YDm3MxPEulEcsEMN6XUFW2asAIhAONsO+IHUFNT/3LCDa9eG9GpwIM+RaPaD56FFtmY/IQ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.7": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.7", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2b82a1cb487d20852e6bcb0e5966383519a7dc8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-yzQ1UYw7nwRcqsX3EMRYQ+LEBtcMOCGt4/4ew3SICJwFUQw+3BQY93QGAQDVR0HHMJ39l7YqAC1GWbmMqyAvqQ==", "signatures": [{"sig": "MEUCIQClB0hoP1+QpKXaQX/jwQC49srqMhh3yLk0sTg22HemYQIgA0edtcWtf/iVLuQzKgAf1wXNNYbQpZTeqGDtgslJWxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.8": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.8", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3ceae529f66f228342896785dd09fb351a964ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-z9hJZeCB3yjR0juzmMhaK8HjgM0iutsCf9f5JkQdt4Pu+eD3blyYRwMwhrJVi5191xiqZDA0SXpp1dFUGyI3Fw==", "signatures": [{"sig": "MEQCIFwYizxHdQctoxbD/DLCqgT013x+HhDgX5MDIH+wOsMtAiBJPXswDzTu5f0dWyp+wO2OXIXKj4bX/XuuTPSlQs93lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89183}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.9": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.9", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ccaa6c82976e43b1663a4e3116609cb8a1aa2019", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-5Xyut47jvnUwv640Qqngbb6trzix7lBKVFMGF2s/Nt43jdl//Ez2sXjkPRz8ld6j0bhJCIxt6n/bZ9oOkoBmoQ==", "signatures": [{"sig": "MEUCIGLUetCgagyUXnDeEeLQcfpZCt/L8iuNDRFfEficq8T4AiEA8tYHb5i3k3/3CYzPZtcNihLuG/hUlsXjAlZhTr/JltE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89188}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.10": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.10", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "687317dadef47f6a41f7f1cc1a29446fbf99ae74", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-WP5wAT8DbVPMdybOStIubCI+wGAl6/iPi2RtVUIlpQcqnMQqb+oCGh+EReh9qor+OQWVMiAeN9mXhBbyJXgzUQ==", "signatures": [{"sig": "MEYCIQDPMzglz+MPRfdKox+zPnHkM41dQxfmdztGcUPRSCwk1wIhAPKiht/T6uD4LKOoQYjMW82ooJW6AXZxeieJgCfZIX2n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.11": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.11", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.11", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "17adecd5370421f5aeff004788620909d30b446a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-Rcu/UAHQuRP5zCk8VUPkekXKwMx+1rH3AZkRR+plcJsDlXKyieXKXJPTuapmPucVraru32CUTtxBWm5Flm+vFQ==", "signatures": [{"sig": "MEUCIQD4HuZIR0fIjRjy/b9+7RuzH94wwJV8IKvs8qUUiGoQ0wIgUQLQC+0raXNlMt247gYrGD+MjVHpcajd0ZP98uC/Bbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.12": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.12", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.12", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1b0ccf5aa1a910d3bd64764bd15cf04ffd81e536", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-QjLDoEbgpty6e+s0idLv3iNoPZvUB09vqlLHHa6FqHvoRNJzqB1hD5Uvajzt25ld2cJmZD0PZhZakT4RnCuvEw==", "signatures": [{"sig": "MEQCICy82ATuw77b8A0eV/jCvJrS8WwPrCngXz+qtb0G0x6EAiBu6k1jGvxtXppzSpsL2sORBV2+yu1BiXKKoPydQFJTYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.13": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.13", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.13", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6bd07be5fb93140f6f3e9dfc51fb314098bc6fc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-1MUAxPmxV3wHvM0Hd9FLgqcJ/H94Cwd6erZaCjFotydTFnoVXEidfm8so7fatfWRZSTl2v1KYG0y1Bn6zAft6w==", "signatures": [{"sig": "MEUCIQCA/WDbz+altwbWKqDhkw9l3ZkpxP8eBd6emfWG8w4YMwIgMp8MvnLLfb9/eo5hZNkSMu3iESTE9hhvc+Y0BTXL/PM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.14": {"name": "@radix-ui/react-accordion", "version": "1.2.1-rc.14", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1-rc.14", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ee5a441c601bc9ab65069abaabdf568aba53db0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-CXPfXAzQ3ZMBuyXiiR/hnuzPSaBWPLZksMEg7PCkbQ9und9HE1yUjhJWQhC2h/Ck+FdwfPEh0vwam3PI0PuMkA==", "signatures": [{"sig": "MEYCIQDi1JK9q8jqiP/AlaXvxnYWE1FunU1+U90IhMsgBYE1PQIhAJ4Y2B56RqzhnRcvcP40uUw6J8wlUpcIO133/y4dbdTO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-accordion", "version": "1.2.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-collapsible": "1.1.1", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5c942c42c24267376b26204ec6847b17d15659b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-bg/l7l5QzUjgsh8kjwDFommzAshnUsuVMV5NM56QVCm+7ZckYdd9P/ExR8xG/Oup0OajVxNLaHJ1tb8mXk+nzQ==", "signatures": [{"sig": "MEYCIQDgFvK4Dhld75j/yCE1poeb2Banzz3OPQIkx7O31wq4NQIhAIscuH8b7sgAtz6qxZ51C8nofgxjeDLWxN+vJYnppAqQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.2.2-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-collection": "1.1.1-rc.1", "@radix-ui/react-collapsible": "1.1.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c0e483f00ae13284ef6b6c1f4e8c9d713dfeb195", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-HEOFDMRvMH3wWrLgoxtVH1pVvFtAX3VBZSDcINMKFwNvP65QEI287XFtFR0G9kX8TM2L9EW9U1io6LhaFyI5nQ==", "signatures": [{"sig": "MEYCIQD87TbEb2AjyH5jvvvDK5lEkCpdKKqO9GsQUMd4cInUmQIhAKB/ZQmz0BNv9zdwbodkCYbvVh3Y6uCohlrPN9GU27MY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.2.2-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-collection": "1.1.1-rc.2", "@radix-ui/react-collapsible": "1.1.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8c1f832c0803a89ecf87cc5398d53049c6f70ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/JLX5PfE7QQ9pHCcpuV1Uoc+9Cz+8CPji5KuRYS5rG8krgxLOqKa1FhQBzyfWGx2D1ui2xjDKrlmFSsV0Zbddw==", "signatures": [{"sig": "MEUCIQC7FfF2Nnc6SxHuLDSRLgd/cDF9birlCxyAdS8qejPSdwIgR82ohiTrMibIbME5NtNTLnAhPTXn0igxykUOGTj349U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.2.2-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-collection": "1.1.1-rc.3", "@radix-ui/react-collapsible": "1.1.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "987a31532fc182299fa480d3e9abf33d4a6d4b41", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-sXEhEUG0jp3sVsnnkZ4nDKk0hp47IjYmIIOOJ6m/cf+75UlPJ4hDs4imVQ7UM1t/nwd1qdtNQVsjNuVm4ugnSw==", "signatures": [{"sig": "MEUCIHjwOdnisvZ9x8eIJd4yTzZaL1K388nCS5S/Rk6Y0NYyAiEA5Ad2mixHfHdagvSt9fve5A/UWzXV6wn4IPPojWNZ8Aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88383}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-accordion", "version": "1.2.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "96ac3de896189553219e342d5e773589eb119dce", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-b1oh54x4DMCdGsB4/7ahiSrViXxaBwRPotiZNnYXjLha9vfuURSAZErki6qjDoSIV0eXx5v57XnTGVtGwnfp2g==", "signatures": [{"sig": "MEQCIECKL13z1GXD9ldvMANESFxeVngW9++JTo6yGzHW4m9gAiB4TG8qNLbelZkP0HW3mLyWzGdtiSKz29hE8aVSuPxwlg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88325}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-accordion", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-collapsible": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c0004aca63ac0e76400a08ada700b28936c1d23c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-+UWXuHkX11AYq8Zyr5XaV4od4l1BEAPZzCdj38pgULXLqBaxMUaDXtPSRv5PN+Lw3p+ikDaQEJGBYSMEsPfwDw==", "signatures": [{"sig": "MEUCIQCgjS6tmKNztVL7ohILsKl6nHX6dL0a2Po3l6CDsTBEiQIgVC3MwscueVdrG7LA3tl3d7VffNhWxMPOAE0xqsk4mY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88364}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-accordion", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-collapsible": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "396a7f84b8cfa5f963940e7314b55f88c30ef6d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-ZuFVjllX01cauhZEFlmR4EXH8/IzLq5M2csN4X3JdkAJ/swYJpZvM3kleM2yH6w0mU07OD5x5QwzHxiaGFVYrQ==", "signatures": [{"sig": "MEQCIGwm/BSMyQcK+WGZmHVE9BdRidz/BvJdZ11GOpkAVjt4AiBJHSG1nvk+XQ5zUmmaf7Don/u2MvInPXLiY0m/rvPTkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88364}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-accordion", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-collapsible": "0.0.0-20250116193558", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8cc7740d8790cd58a112ef90db6eac4106acf42", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-E1MedPOc3C5DIJ/gSzj9ydDXdsUdch9w7wGeo2EnJrzKfK9QwlM8nlkMhEtFSnxdYYjG0Nwxt7HM/DQ/8XYrcw==", "signatures": [{"sig": "MEQCIHlxErc4s7T5fGf5H//JmTbkDFupUjbNV6nabBMnkdJUAiAezgum8zfqHyZiN+cg5XYSn2+04NSspfvM7kOw/IlPig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88543}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-accordion", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-collapsible": "0.0.0-20250116194335", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d6909ac1b7ea33ece956726b1ea10554f7c1b0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-C74gvjgeGeWNjEH35wK1emQdgkKdj2jzf/KfXxCMXOvjkvcVaFmaB0T+po6pcdS4Rk5Vi7lhlxCB1hm56wE4Ng==", "signatures": [{"sig": "MEUCIFFb8aCnPTBDgprX9zYtf69JNIrQHxZJYFroZHQn/n2lAiEAmDgIHvvFVlfg4WuHUd3CvRpsKAF4TBkjgtqbr5/9od0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88543}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.2.3-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-collection": "1.1.2-rc.1", "@radix-ui/react-collapsible": "1.1.3-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "851c0cdd54ed35c329c3c2e5eaa4886bfb5bd709", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-TX3zaGr0QirnZ+eDLnYrz/lzZ7OTv/o5yrb/Ndb8DOmZMxipjKoLJBKzj9OnoG0qlY6eQtUTPg4+fAv+qYxEUA==", "signatures": [{"sig": "MEQCIEuv2O2j4WV69Zx7ARebFMDfdJsbIGxCco+By3hYDI1TAiA2ghdUKo9NDIyLlgcMLX7/JxmkHlAo14II1yjEd9GPaw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88586}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.2.3-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-collection": "1.1.2-rc.2", "@radix-ui/react-collapsible": "1.1.3-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d79f52c0465e7cd97820e57ccaec68cd985b351b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-u6KX2Ck2bA3gy/RoBBQpvdKFj/aY4PnxszifvAKHjnUsWEvyfN+GodTUVNZY3DIgzvcsKDGZrz3f9B6Od7gtgg==", "signatures": [{"sig": "MEYCIQCXfzish/qbgh1exQtjQESOuq1HdhGJjkRaT9SMRhLwTQIhAOnsNQMTtAXy2OL3VDlU5LiFquRJ3SfGQFwknphjEVpK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88586}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.2.3-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-collection": "1.1.2-rc.3", "@radix-ui/react-collapsible": "1.1.3-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8ece7ccaa4cde16757fe50cf31eb270bc6cace17", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-jPVQDOizCgAve1rWKSzZS48Y31FrUPkIr2WJ1xaL082+9V6/Ke+x4Ryp2JvIGckuPkyT/OPw83C9XVecQTsQQQ==", "signatures": [{"sig": "MEQCIAI+Y7KpmGNcZol6FkcxqbpOhae0A89FtT1GK9rQBsi0AiAbZqVMWkL0hX/VKuVGB2K2IKvacJZwRVx+yahNvr6dAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88690}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.2.3-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-collection": "1.1.2-rc.4", "@radix-ui/react-collapsible": "1.1.3-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "04aa5c2becae0cd1a457a7afdca930d16baf33f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-B6zBA8Z2VAFylptJfZa/LOse9aPRXaoyE0iSTJg59YBJnjw4HUoDJ4q3plTCbFD0a0aTfXnVLeGPL0jrFAdRcA==", "signatures": [{"sig": "MEUCIEdroTUnq354sBOi89mIn4MNz1goIuG8iBIzZnrhHNz8AiEAo7EvkpSZ5VCpVW4HpZY2cEVajkYtAkpdkhE0G0fbdw4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88690}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-accordion", "version": "1.2.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-collapsible": "1.1.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7768a2d2daea18e5c09809f2c4b8097448ee2ff7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-RIQ15mrcvqIkDARJeERSuXSry2N8uYnxkdDetpfmalT/+0ntOXLkFOsh9iwlAsCv+qcmhZjbdJogIm6WBa6c4A==", "signatures": [{"sig": "MEYCIQDuohtDTS8mvYd/kyWbgapA6xa60Tijah2i8QoYK2IVJQIhAPTEGL2AGqAVnw5zyR4TAGE+2NjAJjjQXCROMofTTBH3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88642}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-collapsible": "1.1.4-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9b8bc41359cf4c5a3c43678ae220b8ecd2eb4f10", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-QLG5AuYsf1h8i09ZnmE2C6Q5vPvLwQ//Z8xbrO3xZmpnP9zU0JktMj2tOeByhse31v485REYdvMwvgB/qTkrqg==", "signatures": [{"sig": "MEUCIQCj/BqcVuyuBMVgz1pJphg5zCsSlAY8s76+CegvYVqffgIgIiofOe0MdfE8Ezhass6UIy9Us1Qg59o21tdvdJHwEW8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88680}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.2": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-collection": "1.1.3-rc.1", "@radix-ui/react-collapsible": "1.1.4-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fce70712f4cc256fea66ef800342a67bd068f0bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FHlviIwPzIF+khPoy+4WfhxQ4Vp30NoaNJgTRkXs5XbNEx92H0YOGFCqVuIjliPcyMN3VY2+MGewroNBQH1vrA==", "signatures": [{"sig": "MEQCIGiWnYG37KfPqAw7v/6p7IrH6+Pb6RLvCY4rTeegy7K4AiBcCHTzgWu+20+yvhoKTvBje7TUY0Iq2PU3/RviT7jfEQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.3": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-collection": "1.1.3-rc.2", "@radix-ui/react-collapsible": "1.1.4-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5126f712816e7120362624e5f556b1ee63634727", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YeGwzMPqEsJCnN5O1bSmmD/lcRL6XE880eFby9jGs5lfamSWTgyMh1vZcFlfWPNQBDILqCCClKTBBmHHr7OpVw==", "signatures": [{"sig": "MEYCIQDplo0b+sv76yGTZ1G7tzjysRmaHWrytL8xI2flANER1AIhAL6iE7Z6udATOGmM+Fh+QyYKWUVVnDXMEzofevBYQCQw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.4": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-collection": "1.1.3-rc.3", "@radix-ui/react-collapsible": "1.1.4-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "32a7080c9d887c99d670349fc91df436c34a20f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-a8vt6/SDmirRMZOWO56KEdj/5VftWqoFOglMzZosIFpRISMxr1gVw4qPxZYtw9z7nvnaQitAri+lO1ol3tH1jA==", "signatures": [{"sig": "MEQCIE/J5P1SAV5mJ9mMrZyGUO7CydatdXME/axUv4lHINgxAiA1lW9C7/GMOj2w3c3sFFTYxb9vVcVcqQ4/rNcoqGT32g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.5": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-collection": "1.1.3-rc.4", "@radix-ui/react-collapsible": "1.1.4-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7748539edee6d6be0d19cc7a4790b13b460ad6d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-3cqR38iZ0eETizjbzHngj/AnBwMsnU1+G5xPIb2XiG75V3RdLmP6Fayt4k4EmTf3i34kPh2lVHHz3gJzNpBxRw==", "signatures": [{"sig": "MEUCIQD8ogtRoes2PA4AyqCU+Dau06tEC/M3ADmH0buisxbgXwIgORJzaobrTW8fHDo5PeOLVnyoe0pjkDh9ftC3y5VwfTo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.6": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-collection": "1.1.3-rc.5", "@radix-ui/react-collapsible": "1.1.4-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bf27787bd983158cf236924ca5256e1aa73f6869", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Yr9B12IDadUDEoIvBoemKgubl18rqqVQRGhr3n9hoPq4+CnSeGvcS9aDAJt1yfbV2sJmEBNxRh6OuTy9D2KEDg==", "signatures": [{"sig": "MEYCIQDK0bzGXOhDp1LfmwBOXFvHtRK/2TTtz4UFFiCQdBDCUAIhAP04S5439HHI529p6LF37UViVWAr5IH+gJ2L/1lcOJ3h", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.7": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-collection": "1.1.3-rc.6", "@radix-ui/react-collapsible": "1.1.4-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b4d767183f4cb9de31d2ea924006e83a1f418c5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-bpHLsaWKbKspPGatDRkTJFphPKtW2/COfElcj3DMt+MkLZ3iL7lhY2CA3aST45pE57P1t5VQhRDyzGs0vRzMqg==", "signatures": [{"sig": "MEUCIQC/Jb7cPsJEFBJkZ6ILaexUxOhk7spnuBuALzbZLwzxvAIgbrRqSlTK3+9NxEq2T5flw+TXj8p0Z8LkF0g7JfVRKaA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.8": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-collection": "1.1.3-rc.7", "@radix-ui/react-collapsible": "1.1.4-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "80ceab09081b9330302ab99dce84b708ef7e3e57", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-jEWQ9CTwzNbet3xqrH9Tf4CuyR52psODhfK5a8VirP+Bs4VrsdgRzP8rzFH3fCEIAWh/JmymI8LV0KgKwty7BQ==", "signatures": [{"sig": "MEYCIQDgTFyfVlqPVUkEw7xXJpuVQoRqIcrBpZcgRsO6Dp2QbAIhAKvUZ8r8zsXOlrtYsJnmGLm8zIbq8NtZ7M/BaOL4Cw+m", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 88726}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.9": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-collection": "1.1.3-rc.8", "@radix-ui/react-collapsible": "1.1.4-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "078ed0f17aacf13f2fb2d25ca4fed03281232d45", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-FoUIpaxPbiWOE1FVe4dbdnHqtAnG6X/5kvaIZEfByPr1mMrstRP74pKI0lwd69P6ubOzY8i4PPeNOjijRfcRqw==", "signatures": [{"sig": "MEQCIA5nPs3oPB7+4ShsVCjGK6XlUZ2Y08Fp3upxgPEFxYamAiBtXqMsKB8xML4L41nCATQgjdg89eDBmU3jUbfGNrSorg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89117}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.10": {"name": "@radix-ui/react-accordion", "version": "1.2.4-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-collection": "1.1.3-rc.9", "@radix-ui/react-collapsible": "1.1.4-rc.10", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eb1c9d40dece705fe5ee73f9c5607b8abd9b8225", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-gRjf0ljdIgTgNvQwhILU6W25uQ40R29RuTXezavWHdgpU+8jySDQGt2gs53bTt9zdUNfZajWEFpPZfMIIHDDng==", "signatures": [{"sig": "MEUCIHGA3QgxQSQJkT/QgX8OmKEfjjq27IEq8Ug3pD5pLOIRAiEAy7VaA5Y0vz26i/Mn7D/AjwCpTowMk0lXe1dtS803rvw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89119}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-accordion", "version": "1.2.4", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-collapsible": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ccfceb959bbb3bac2f92e5e75b2654a488a2ff0", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-SGCxlSBaMvEzDROzyZjsVNzu9XY5E28B3k8jOENyrz6csOv/pG1eHyYfLJai1n9tRjwG61coXDhfpgtxKxUv5g==", "signatures": [{"sig": "MEUCIQDXPAcOHLW6JLdqQpPvKaCPHbnGHgJqTzYxyTTUIgDOKgIgH45LNm/WovmmYSdzO3u5bReFUYgi9uwrxNyaAFzv/Is=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89039}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744259191780": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744259191780", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-collapsible": "1.1.5-rc.1744259191780", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2a4ddc1a3efe8850666e8a9cf3182c994f076f43", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-Vlf+/BR/ckj7AphXfGEm5Qtd1jr2rfVgW/FocI9xkXSD5UIEDY64cczCIR3/aMHPt4AOTBEYMwqHif7T56vKMw==", "signatures": [{"sig": "MEUCIQDuflD06Zg7ipyHYvDEXNFURC3YC0AwUiOr7gCe6M3w8AIgL1Rs3jAVUoMdcZ06LJDGRUuEMPP8g5GsGQ39YLV8YzA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744259481941": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744259481941", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-collapsible": "1.1.5-rc.1744259481941", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bf8920e3a2a443cb1d34b37d7d3be66ff29b33ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-zYKIvtCRmGJO4/LrcqrkLnWQwJRSGZNpIjCPEIYrtiMmumqucj+JY/IMielmcgksiGhYUR1AhWYfd0qkYn/G7Q==", "signatures": [{"sig": "MEYCIQD0tt38FEItXPqx//sZU2Xc2TTzUnCJl3ChbFHP1c8nbwIhAOhvWDXH0JmGyRE3VLt47Q7W0Cz5NRkEre86bISeVLdI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744311029001": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744311029001", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-collection": "1.1.4-rc.1744311029001", "@radix-ui/react-collapsible": "1.1.5-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "18a9fd8ad793b5a0aa7c7341b23cc9ae0512683c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-ixFxT/snoYdUTkvteQ2vef7NhuLVTpEw0cxxpVColnT6JczcYFGP+wgkG/VbDVVh+0uRSITYbOcCWTCj8qm/Mg==", "signatures": [{"sig": "MEYCIQDWESSeNynZuEt4ysx9DPYEEyE3Y3HpJ8Wkj9jVVrWMaAIhAJd9WKZxiQbz56krtrFAVVuNZ7rZMrfbmIy0svENS04c", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744416976900": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744416976900", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-collection": "1.1.4-rc.1744416976900", "@radix-ui/react-collapsible": "1.1.5-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "48b093e214638a8a21960791cf66cccb35bbacb7", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-7Y0aSY14MW0RfXpWJtFcX6SixZFHEMfXSV8WMAvecuBPXgTfebDPXbf2C9HK1cIjSeQWHrS7nfoplI1Isx8UsQ==", "signatures": [{"sig": "MEYCIQDg04mPEI32gaCxrmLiSPhxKUjcfZBAr58CXeh5J3xZPgIhAJz8sshzioQ0U4n8ELhZrk5ucQbW7sMDk2oLVW0a8Iij", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744502104733": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744502104733", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-collection": "1.1.4-rc.1744502104733", "@radix-ui/react-collapsible": "1.1.5-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7940cfe2314f0310e89c67b4aeddf32b6638b304", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-ATVcRXYWHky+kJwKUd5siXtUVXfeXWZBxB8+3Grt5q5z+g7GPiYNWsgPIybnn7SeSpTpHKJqbOminnkBRAteGw==", "signatures": [{"sig": "MEUCIA1Nm3KdOY7xajHs7KxTu5VIqysKZyxGqeCjz7DTcibcAiEAqUkMTibENi25rIDnHTxcivl9nqhBPQkCAUS+54GM9O4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744518250005": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744518250005", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-collection": "1.1.4-rc.1744518250005", "@radix-ui/react-collapsible": "1.1.5-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ebcbf4fbd7aaf90767943c6e2b42fc898a1d25ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-XBjSHspEW80CMdFeZ93T2VlqobwOll/91+OELYGA6tJleFARUS+sna1uwaEBodnk8I26PQM2qDI39Do7PNnF8w==", "signatures": [{"sig": "MEUCIAr/YUkXunracIi+wz8030xY9rvE+l2yxO1XQzozpImhAiEA+m2m1Gx4+7+DJgvhpV8NyUPdFbdwD8Q61fF+7Exhm2k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744519235198": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744519235198", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-collection": "1.1.4-rc.1744519235198", "@radix-ui/react-collapsible": "1.1.5-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbab63832ddd65f995e6f34b24399a56f799fc27", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-RlVNshMkAqksnA5gz22y0driBgRdba8Vc+Tq5PUGP3MOte3FMo3ROhFaeb40772zSPMUAro66JVIRMoLshv8AQ==", "signatures": [{"sig": "MEQCIBP9bX7PRmeKTni+dKR9fcPWix406QqhWO9BREbcZJ+/AiB0rshn2HhG3Mh3lk5/sv3LHkcto0/AuczB+xwUs8xU1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744574857111": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744574857111", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-collection": "1.1.4-rc.1744574857111", "@radix-ui/react-collapsible": "1.1.5-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e710bbe0954af7f009ea1a485c838b47b5eef2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-iZnV1zOwTlQhZ2Bu9es1ql9H1VzoSY8U9kPp335zdYnJH/1q35XJl2XQdajCzoHftsZozZLpgIyzgUffGdjrHA==", "signatures": [{"sig": "MEQCIGFeua7D++J2jyj2XCs3QgMtJhWoCm/sBEQyb4WYPzswAiA7vKTQFkYCt8fAmCJlGKPHZjS8+FZIIy72CmffJ9t3Hw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744660991666": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744660991666", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-collection": "1.1.4-rc.1744660991666", "@radix-ui/react-collapsible": "1.1.5-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03a7afe31cd705ed9a507ffdd63f78f69399ed47", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-sy5vF8cfhYVEUmd4JwV3jWbdkc08Iw77J1O/mcO6YbisG5z+qRvQvf77kXk214IpHOk4JamaL4i/LL5zT8ESSg==", "signatures": [{"sig": "MEUCIGDGtq8Ga6jjnMbcy/9AdhVSlKZth8neJttyTU4YQdiHAiEAl/HT/UGK7NOCMCu5kSKEfVBqoA8BG3EFHnj8AzCzs5w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89663}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744661316162": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744661316162", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-collection": "1.1.4-rc.1744661316162", "@radix-ui/react-collapsible": "1.1.5-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "962d68c3b48ead6ac21a616d2faf74ba1731bf42", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-6Ytg5aIKbJPKMZTqh2EXeQd95qeIE9obuSCx9SPDuFYul7uOW9z24amt2rv9WVs/iLgJLz79ygD+J1KeEahUJw==", "signatures": [{"sig": "MEYCIQDLAYvLosq3DZqt/mmDS3SxfQXmLkkWkYGigxwxaYgESgIhAOdzHbnZjgPp+66qm5n4Q2cW66mU1/ESZMv+GW5FvKaU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744830756566": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744830756566", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-collection": "1.1.4-rc.1744830756566", "@radix-ui/react-collapsible": "1.1.5-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "098061ba379678838cc7d8953ce794852db27150", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-nwGu9KOHi/3zqPQZ3DLJy9o2InL7W/xsn5+Wq7rxySU2IDnFTicej8NMq28NEAwzoCPm42iEjEoJeDVAco/pkQ==", "signatures": [{"sig": "MEUCIQD94uG/lV3SeGWs9D+nBKDS7o/8lN68aNuS/2zGMRV84AIgB8qdLxI/QEgTnyO0kCP0dIDhYcYy7ZGBdx3menCCmpc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744831331200": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744831331200", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-collection": "1.1.4-rc.1744831331200", "@radix-ui/react-collapsible": "1.1.5-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e55e139edf3be3b7484e9b9f08f383701ff13be4", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-oaXnR4S98pu/95RG1wkNAFnPaOOxI4rpHOor5eH7HFIKLpmVmY6O2U6idBrgPMME531ROr/D/3OtbGZ/yyLXwA==", "signatures": [{"sig": "MEQCIDlckZhc3Fvm3WRTPK121DgyOGAcQ7kFSG5lovpFMNXHAiB3ddCvbYkP3V3QHadcQE+GPpy1OLRvy7aGUv9qxH974A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744836032308": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744836032308", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-collection": "1.1.4-rc.1744836032308", "@radix-ui/react-collapsible": "1.1.5-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "62d0acd9b77718675bfd29270fc654f39a956dfe", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-bj3GGVHlWiNF9Qyq2tIbx1OIBkkul7dzBTg4nYHerxaQHj4oGfT2rMAgjOAAgwGGIF6jaRzSrdGlHhXbhMZbBQ==", "signatures": [{"sig": "MEUCIFqpK5EURcOySAtLo8Cpt8/GtGaNRcLQoLhWIvn9hC+5AiEA2Qhw3bMKN1Wz3tgqgDUbPYccw3ZKza9vEat/NlH7ruY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744897529216": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744897529216", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-collection": "1.1.4-rc.1744897529216", "@radix-ui/react-collapsible": "1.1.5-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff9ba6d425e5e0099ad46f312f287843a2843829", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-vZYjpFcJFBxGh9l0HbvNFNBWxm/IltMNj1W45V723faPYFq6HNByXqHOCBpL/93mZWtfnFzdjWKg1srlNMvcRQ==", "signatures": [{"sig": "MEYCIQCTaCBXjBm648X02zCmJwONNynYkG5rdKItn0m17+69wgIhALQeMsiaZMiTVn6phIIDwTyvqI1AWSM341jV6RVDv7+z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744898528774": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744898528774", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-collection": "1.1.4-rc.1744898528774", "@radix-ui/react-collapsible": "1.1.5-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0a716457aa7ac6ef9b2e3aa75ce1ce96b860328", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-zTFVZApHwxXBYejKtrlmmrEnVnHMSRwuUm9qlmEzvyFAG5HGrpfOTYk319r8at52GEfCi79DPFMaq2alos9oLQ==", "signatures": [{"sig": "MEYCIQDpS8rjfZlLN70iilaOP9Nmbki5bMQeY8eLbfnHPiZQ0AIhAI8miT2iOMeS8OmIIiqp7wdzlM84nk8Soa+Mjw9VxkBJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744905634543": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744905634543", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-collection": "1.1.4-rc.1744905634543", "@radix-ui/react-collapsible": "1.1.5-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a83de3b0f786762a9dc1f21512aa1f340ee7793d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-HondfklOXUmVGZIiPQBLk3gtcvcNT4ke3w7OcBDJ4JUpE7KhUR4L7SHcnBqv/0CZwIsHo8ASnl0XHVmspKh4qg==", "signatures": [{"sig": "MEYCIQC6ZRw9l+mh0Hs0mcGtxILNYl38I5O2KtbVggMgLU873AIhAPDe9xThC7Fj2+Vzk64wDiyG75mwE0vOlPHbRUvGbhZB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744910682821": {"name": "@radix-ui/react-accordion", "version": "1.2.5-rc.1744910682821", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-collection": "1.1.4-rc.1744910682821", "@radix-ui/react-collapsible": "1.1.5-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "622ba05e183023348090e3e7cdce07468b410471", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-WdRF1wzQAKtnW/1duLn3O4c8TGfHu7UGhSVAYuWxwxB3QkLdoHfDfTnxcycZpi4tgVSo/PuZa41IZa5K2hpbhQ==", "signatures": [{"sig": "MEQCIFPiIPB0uTsSTbKqgBJ7KaFb1+NxQuRauQIDw6dMfUmUAiAu5Wbnb7+LoTKpZjUjfZPm9EQJb4EuLL9hSZrKgFa/Ow==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90224}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5": {"name": "@radix-ui/react-accordion", "version": "1.2.5", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.5", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3b9eb3242363c13240e77576adb11f693bef202b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.5.tgz", "fileCount": 9, "integrity": "sha512-YY352LCp/qfEqYG4HdC11GxIpGXKlWVnp1w4urUfABSha32drX2finb1SZHCxJexWjLrPHEubMl4Q8PwSqcezA==", "signatures": [{"sig": "MEYCIQCKden5t9ocZbrFsIOzy77zWOdFLwf4faU/a0kTtwyDNQIhAOvRwabLdOkkh0h8n6Iq6ImplRNSAxAoAdRit07jlS38", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.1744998730501": {"name": "@radix-ui/react-accordion", "version": "1.2.6-rc.1744998730501", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.6-rc.1744998730501", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "16742658253c509d939010c806fe43095a3d9981", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.6-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-4uyxVO7Xf6bzOotEZVRoerxSmciYbIfVLOW8+nphV7+pnhhcMuXMnBu/OEQ98OZbJ5lFTj9wmJDnEYgRbrIj+g==", "signatures": [{"sig": "MEQCIGDTvEv4SZQRRJtwq9NaOaRK3hfQ1OVXcSOBN3VksG6+AiAkXYGp6CS0kbl5FnwwKs27iqF2nF+a2JMo/FqkR74GUA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.1744998943107": {"name": "@radix-ui/react-accordion", "version": "1.2.6-rc.1744998943107", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.6-rc.1744998943107", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "85f13a6a95bd006818c74e8cb18153c05b567f80", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.6-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-sdMp0CO1313TBLMW3EIBeBFb26ogFhFDdefICo36ho3cA+0J29eddWGLRDrevQPOFAAfKvs8/VxbUIkd2kPdxg==", "signatures": [{"sig": "MEUCIQC6YenQ3DLq8Ee25Rn3KOvvphNTU3tHRAGutxy3zemHNwIgJyjzrsh53JBPvrJKEcbE0D5Ib4LO75HR1OCpo5LCCOg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.1744999865452": {"name": "@radix-ui/react-accordion", "version": "1.2.6-rc.1744999865452", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.6-rc.1744999865452", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "69d1b4884f3e62a3962ad3ad9031b578f04e8d5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.6-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-19V8j0kQG9RED06Jls34EUeB/tdr3b/S3zyYo5ivEgorVHA8bEwyfaw9eGjR5p7VFdWhzSxAhm0LOvtmCJTiWQ==", "signatures": [{"sig": "MEUCICsvjBOYpQCDtdoRrxuvcnJwh8VeDSBj2ZYKtpY2xiiMAiEA36Ycl9gCy3DpamyzA0gbcfhHxbfhDdVhCoDJ7LZ2kGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6": {"name": "@radix-ui/react-accordion", "version": "1.2.6", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f469745c991abefd9982f100c43c82575bb6132d", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.6.tgz", "fileCount": 9, "integrity": "sha512-FMsLy77w3XH0349mOB6TP69QwqXQ5sii49ZF9qOUD+bqrAk3Os0whM/VayIDWMSK1PWUqItvlamFvnE/RW+wPw==", "signatures": [{"sig": "MEQCIFSReYhkLf4iT2+ZTbjQrSBO6DY2OwHbi4m3n/bMe++7AiAqVhgLFYCsgmo3fzY08uNqtnJgrbgxrIJkMXfN4aDVHA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1745001912396": {"name": "@radix-ui/react-accordion", "version": "1.2.7-rc.1745001912396", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.7-rc.1745001912396", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8309925c10cdd18ccf8668a22433d621ed976737", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.7-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-eWUkUwzBfZwl5Oc5rxGmdhOlEEnTGGd6h1nFPBIdaUzJAzcge17Sj+7qJycWRrFQVLWEA5PsSZYmO9wBsKPdOw==", "signatures": [{"sig": "MEQCIEbj7J3gbDf5YU3Qfr+hcwdQElLZJ2Si1uTEmBrwX2MeAiAFQviYbvUlc7JXReR1zFxxFzQE16A4aL0HNJIqR+umMQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1745002236885": {"name": "@radix-ui/react-accordion", "version": "1.2.7-rc.1745002236885", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.7-rc.1745002236885", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f2c40c15f23912b7ca714462b38d3eff4f27f086", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.7-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-SGSrBOEND7CesN8Rk0kGpg7cd8fPCGT8eEfpJpMnxNWRSHgHHEEa2B6H78lR+c/Y+vsAhXAlJhtb7OtE8WV5Ag==", "signatures": [{"sig": "MEUCIA3Z9SCawBMnKJq/Z9AUyRCX/+O8R14i1pW1YkcfGsPhAiEAqdBRG4eg2imxZo3Qekr06jt57WcDdPJgw9VjS1bLhCI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7": {"name": "@radix-ui/react-accordion", "version": "1.2.7", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.7", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "75da1efe16daff1d9f34552ef200b2b5cb858ba3", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.7.tgz", "fileCount": 9, "integrity": "sha512-stDPylBV/3kFHBAFQK/GeyIFaN7q60zWaXthA5/p6egu8AclIN79zG+bv+Ps+exB4JE5rtW/u3Z7SDvmFuTzgA==", "signatures": [{"sig": "MEYCIQChvZmgxA7HzwqDjgCIu1QuyF/12cQD5APAs64d9HrVAAIhAOc/4M0buro1Xuzl3VKSOqG5ecFYKAHa6luFZblJCuZW", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8-rc.1745097595920": {"name": "@radix-ui/react-accordion", "version": "1.2.8-rc.1745097595920", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.8-rc.1745097595920", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "64eabf98917f6a8492f6a7b3f7c1aea0354df12b", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.8-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-AM2ad/Qfy7hgxHRWXS4chxnvwre1eH7X1XSvz930biT0TLuoFcDDekQvRHczdrUQVMC2BFIlVy4HcYeoe+dqGA==", "signatures": [{"sig": "MEQCIFwbCPYq23YdRtvesAQ+E+BNydVm/1MR3CciooTXGN4EAiBtglPGrzupCOmrgQbyQydlEX8mXRsEtnvVALuTFfsd3Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90173}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8-rc.1745339201309": {"name": "@radix-ui/react-accordion", "version": "1.2.8-rc.1745339201309", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.8-rc.1745339201309", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ca315f108aa9e0988b0bf80b5028fde60a0da83a", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.8-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-Ye/v2h8sfr/CDhcGIRG6EbWaB/35dmEECwNUqTCO3yKnzBhuO+lAkFoYsUKV/MZDICFOZiCzGYMsd+wNc5WxoQ==", "signatures": [{"sig": "MEYCIQCCxZKUoW7D5i6WiOT4yu937tNPrgDFjfc82oOLmmOk9QIhAIZ34cUpEaR977cAvrzcIlqB+Yh3CKkECZRyCUhJ7Fz/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90173}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8": {"name": "@radix-ui/react-accordion", "version": "1.2.8", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-collapsible": "1.1.8", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "338f8a11c90199831a02c0adf9faa1fe06e324f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.8.tgz", "fileCount": 9, "integrity": "sha512-c7OKBvO36PfQIUGIjj1Wko0hH937pYFU2tR5zbIJDUsmTzHoZVHHt4bmb7OOJbzTaWJtVELKWojBHa7OcnUHmQ==", "signatures": [{"sig": "MEQCIEEdX1vr6Sjvf/YXGEE3R03pfi6wgfpuMWnx/F9uj687AiBd4SFMsgq2DGKAtqnpPJvRjYJFQtbMTJ5kx/mFBkhvCg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1745345395380": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1745345395380", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-collection": "1.1.5-rc.1745345395380", "@radix-ui/react-collapsible": "1.1.9-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e199a038869f21e745ad6ca142c0b400b66b510c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-qBV8O3lzoWO6fBJFU0valfzuRqioy/4pk1bmG8mg1FdoTXCr8ehIAOy36mK1NKUATlVfsFtBxYQ1vkV18eb+EQ==", "signatures": [{"sig": "MEYCIQDLxKqKuO2wv0O6HGGqjlaepVN1vq83aq1mDUCWHeo++QIhALLtlHnYPT+uCVd5K9mu6HAmzQqMEb4qCk/qYl54SWkE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1745439717073": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1745439717073", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-collection": "1.1.5-rc.1745439717073", "@radix-ui/react-collapsible": "1.1.9-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4787fe3eff394327a2d653471b0945af538f7845", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-o5nNFVGcQ0YunIzCEEdLEiCyji9+Y/uivBusmDVDtLDdMRNtyVlSwopY8Kl55KbtxvOhJHLiMIfgC9ALiYhDEA==", "signatures": [{"sig": "MEYCIQCcJxxA5He5nOOmGRDDujWKMmXUOSbppTKekkjMhOUCdwIhAMz7AaeC5KEtit8qoyqeI2gIaZ19cIz2ssG5IGJk0PWA", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1745972185559": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1745972185559", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-collection": "1.1.5-rc.1745972185559", "@radix-ui/react-collapsible": "1.1.9-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a18d93919cb90c40c0c8c150709cd97e731854e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-ZiJ0itPrwQ4Gsd4WYE7o10rSxvJgMKrxQS7Rm9pjVU2OR+//0oADt2EGBFodUSSiSn/d/cRQMdKFX/4a0g4N8w==", "signatures": [{"sig": "MEUCIFgfu1X7ZO5rXXEM166BC+5MF30uigTAdBr4Jt4LVic+AiEAjwEzE3A2l9k0ifJq5iDBeG6uLpjfwBmlccQBAJkDt9s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1746044551800": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1746044551800", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-collection": "1.1.5-rc.1746044551800", "@radix-ui/react-collapsible": "1.1.9-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9cc3087dc7d45dece9a4a52bc68d825a590b2685", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-7SaYm7df3ZMhEb+QpMXYJtY9Qeg2RtVjel5vAw5CeH1i45kQ3Q6uNv6deZYdIFf4dwGE4XxTx/lgii9RXrTwgw==", "signatures": [{"sig": "MEUCIEgMnUeExigGixq9Jo/UPpAu743fNGdHLlFX6mLmjbl0AiEArLXz/L1eZv+g9w3e9psBbGAasor72fvoU8V82sAtPgw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1746053194630": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1746053194630", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-collection": "1.1.5-rc.1746053194630", "@radix-ui/react-collapsible": "1.1.9-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d33420d832bc9f30d9dcf3b3a1e3964b77202e25", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-Ucbs05hrp0FttLzVEhhf6+/7sYzpxgAcru8OPSYs+Yx25GXaMgwGm/DFdU/1+tSfPt74DnNP2aRCHpoxskoS5A==", "signatures": [{"sig": "MEQCIC7WbYYzYllVR+uWerS0eBSwM+a0WBz55y3BBF1FYeZgAiBV2eZhXcG1nQxjnBMm6Cv1y8NvmmYGMf3qlbALlQNYIg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1746075822931": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1746075822931", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-collection": "1.1.5-rc.1746075822931", "@radix-ui/react-collapsible": "1.1.9-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3dd49b6b67ab4917fbc32978c82f84a50b593d60", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-8vykgk/5Ah7wrZQHacjK6mI/lcPsNyQL96n+IzIlwBUats+5aNcBnTSK5Wg4Ellsio+7r8uYwN983Y6XmgXJ+Q==", "signatures": [{"sig": "MEYCIQCgsVH9HJSI1NIPLJV4K5SnOwjCftrUivuvLab3LKvbKgIhAMJHjmzYpgg2IF2G6ilf68pcLJeWKIEp8y1vpYLd9+7j", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1746466567086": {"name": "@radix-ui/react-accordion", "version": "1.2.9-rc.1746466567086", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-collection": "1.1.5-rc.1746466567086", "@radix-ui/react-collapsible": "1.1.9-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a1924ca98a708132e91791368c0dd8fa64756644", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-UH32Kr6NyB/ijLymSePUhtjAVmKnbPfdUtyO6g6/AeIgCwx6ZaNpSQFt07gBhGFAyQPQYHD7FVpDIrn0wuTBFA==", "signatures": [{"sig": "MEUCICZrA1dsHyVFcYHvMhI71tQqm6EC0YYNqMRHmgW3ouMjAiEA1uJerdjrgIaPXgQsQU82H7AWmmoy/SryhhasqxnhKd0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90207}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9": {"name": "@radix-ui/react-accordion", "version": "1.2.9", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-collection": "1.1.5", "@radix-ui/react-collapsible": "1.1.9", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0b2aae24ad74251118c7115538b9954e9cd4db06", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.9.tgz", "fileCount": 9, "integrity": "sha512-LxTdzg6zgBJpvSFAiFor+frRIBKSLArkdhGTIsDu5glaeIz8zeO7LmKr8xbqobhB+0OYWewrTatN1wqa9kDuqA==", "signatures": [{"sig": "MEUCIC0kE4+xEMoggArhnV304hW9Gv3MgfHsvoAHbsv7mT6NAiEA3IhVpNC5/OGTz6YV0vUx6GaYllTj9+5rHmLJMlzKEp0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.10": {"name": "@radix-ui/react-accordion", "version": "1.2.10", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-collapsible": "1.1.10", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a83b368c809015514f75e21316633a73ac9a30c", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.10.tgz", "fileCount": 9, "integrity": "sha512-x+URzV1siKmeXPSUIQ22L81qp2eOhjpy3tgteF+zOr4d1u0qJnFuyBF4MoQRhmKP6ivDxlvDAvqaF77gh7DOIw==", "signatures": [{"sig": "MEYCIQDD5LgYB2+MWc99+uhXai+LyEnqd4PuTRuM2/a8Va3qLAIhAJwoDx8JBo8hd3BTjj2UfUhURk7G6T7gpftSmMwXyMRf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.11-rc.1746560904918": {"name": "@radix-ui/react-accordion", "version": "1.2.11-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7-rc.1746560904918", "@radix-ui/react-collapsible": "1.1.11-rc.1746560904918", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-NdGd1K78BaKn3FWLJDD28YI5lNpt0ksqcr9U9TLBoGuX99aEHXZVgHa/fiZVLlP2cmfqIld9nIgbXSFZVz0H3Q==", "shasum": "97638172122bf11243ddad0d4731ded6b864c2db", "tarball": "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.11-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 90229, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCVlNwtA5Hf1eP//5ZiEjpYzfuam2kiDhCix7CkByo1rwIgbwk58cCOeOFTzFm9CZVbxz146D5cJfWdGOWzVFHQfqw="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:19.194Z", "cachedAt": 1747660587496}