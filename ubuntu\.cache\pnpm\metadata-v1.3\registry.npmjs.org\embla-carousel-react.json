{"name": "embla-carousel-react", "dist-tags": {"latest": "8.6.0"}, "versions": {"0.0.5": {"name": "embla-carousel-react", "version": "0.0.5", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "c47caca2b0d7789abe3eea9320cf04abcdaa5b3f", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.5.tgz", "fileCount": 5, "integrity": "sha512-QKd3qItWpiRe0o9Y39RIyHj5qtpT08GNjTHBdw+eTxVimO/reAIyybfRTyyqOhVEyajNdZ6yPvkr3699AOlnUQ==", "signatures": [{"sig": "MEYCIQCjVRzW3SM5QBCSJdRZkRMFIT5VGca/VlsYj+GFi+ugDgIhAMPZcsRYRcJUanr6u2/GjsLXNB6Gk9s3q3j8Q6mSlaW5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9tt6CRA9TVsSAnZWagAA41sP/2xI2yyQ+pSCxGImcuec\npek/xMKD8YT5YYOvNBkwvoB1xmrdhdwM3QDyRqGlmh6NugvRJwGOrxAY7yc/\nhyx6jdiTA7nVvw8R1rihek8UYbdrz07ZqPafJilyb4efvKKBnU0c87f1A70j\nUY3SYO19kv2rlSVcz5dV7T6vawKi2uMAfqGXWPz9N6Ghj0hl3ZNhQLeFtvox\nGJRpfxS9V74rlVg7hdF8sRwI3qoUA7EbjvyEthw6awHPP5WAb2vRSQBBdmwy\nY94LN026+Yv8cExc9tGDgG9Byj5t0bEGMpEfCMcxUft2hAs4K8/PAs6vrNrE\n97nYfiLP6Cm086GrrlpECZ6J825GU2G4ljlTWIvfydTcYhR2XF0d8QYRz5Ne\nAs3BhZl143w4bnW70Ib6Po7dknI7rIPmpLj6tSAF7v0YpPw+x8LpSXil4FxR\ncKyfq4CYSzfm+D5gF9jLT5VbKWNgt5rriZjwtP/ZRAmZ4ekPSEcKQOBPwbdX\nOodRjcGxwFXQpqlwQgJSPqXqHo4QzyFGQtYi07MNpxVP7w2/mps1z2M1Y+ui\nhFxfaySQ4pl1zsoZOrBqVmKJOxqUAxARIsfYJyuh2Bu4FXXPQ4ICBG4y+10P\nydNx2psHQVEJs62tylhyiMDaVxYihl3sSf3gRXjFvEOoGdOfT5Hw62iUW9nK\ngsx4\r\n=Nuqr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "embla-carousel-react", "version": "0.0.6", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "06b6690387a0f01422fd64601f0a2a62a43e97c8", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.6.tgz", "fileCount": 5, "integrity": "sha512-0FGNW1DrHigC5QA7KpGQ9vusrMzcZUtUmzhZ6CsBJbGNUsmzkr3VR8u1qKC3FGFKDLzhTQQasD8y2X48niD+nw==", "signatures": [{"sig": "MEUCIQDbWYchich+9jZLWtTSF0yz0KAKmAlJ7G8lfhWHdH1O8wIgP03eM4uiNmBkMshql29RpuU98dibjaXaOpUZjsDzPg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9t07CRA9TVsSAnZWagAANEYQAJ3396udKlGmnn6zeen1\nFJou7RkibGIdzpCOi3I5DbyHAU09IF/SEyr5yF9vEIbmF002gOVZvnA8Xm71\nQ5vf0ev4xuPiNbPc0455xe7h+lgJEIHqH4gQ3BNNUylNWKdJhIee/TVKhTn5\noIiMxyK6Sm7Eut7OctSuD2BoHIMh9kHKuS01I6pBIJ3AQcj7Ao3WK0R8QVky\n/0K74t+uJp9wCjb490EDEo9IplhVuzQAVSY9bXc0uKy8GbMSyh8Y3xLT6pvU\nyJYBSd8axihZuslnvmfjh49xn6TH+1UEzmEZOFRmZgk1vRwwtL37PowJvP4P\nKMdrkJ7m46hxNI3ThYggGkEc1eSAEj5BXxy7CiA+deM9GmvYzwnnA3n8aOfF\n/3KBK9Vyr/w5X5xyNoMTW/Ou8JFFOB3FruDK510UQUJKt2IxXZd3lMywZPZY\n25cB0aSA5kIzG1G7OV0hNRGwOIJp7EdecZ7FzCyGO5mBwL5M9k2zjgnBbYD7\nkfxVbgMSZOTNyn8XnfhXxdPtRo5uWVUkDvOGrc4VswZjqJtZ09ZdoK2adEzc\nWYiCmlFZ6xjqkrnT8zHLTemc+eMRz11weLtlDjC1Hq/iZY23YixbIet1x14z\ndAhKPXc1J6La5I8AFgh22zoA2GhiOyrLPrmg2lSzTnIYozQXfTRo0x/U/ed/\nv9uu\r\n=wfbw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "embla-carousel-react", "version": "0.0.7", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "11ceb1f5bec64b3dcb292db968b16457e19fb31d", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.7.tgz", "fileCount": 5, "integrity": "sha512-07blzBR+bYCQhxlhg7CHF0GoLjCoYPeGDlyvQYvAWLfSjLFhm7Rg2MihvPSqM0BQ0a+K6CfIilHo2Eu2Xtj9cg==", "signatures": [{"sig": "MEUCIQDYoqxjGDzINc98xrJQhupDCY95ogR+YL+f2L5xZ6E3EgIgW0NOw9sDksrF5cFh8nq/O3YZXcI14OA9ugjqtKyT1zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc/onHCRA9TVsSAnZWagAAjmAP/3E+u4q4LoDcTs0yhmwX\n8KRte1FLQmhp6DvoGVlX9eRDMaG1JNd0V0jOACNUbH61DFrMvourn1UJqVe3\ngx2kSx+55Qcz5m3nFDQqqKODkf6xquRnzY2DW5NyOWXNIWTeUyHTyWY55ZyA\nbFQXMzQlmunDOLAm4qRlVvSY0vY7jHHocf3XixuyUXp7Py+MPZyNeR/ssWjK\nQrBRnDPfjXAh+TMu7E9LWjaYXXZ9i6HO8dLNoWwn7w9YZw6Yuen0nzK/H5Bq\n0zW6f2Zo5QhygKXzyREYt8eM5A6BAWVVlEr8YyGeesgLeSUwBdUfGIIm9fHW\n33BFDJIpLHwDiu0sfj0SZJMh6ft761h+D255DVxXo90OO1rPhXUUdab4j0pk\nnknEgj07RNZPI0uGXn/pJ2e9P96dJbjmhreU9yLrGoD4lG8QDh5azt/3wK4C\npvajOyoEDonYIvLgDlQ7qg0uYdj41kB7SoeL/707XN+BbN/laAGCiN/TYtIi\n/JA+AJ1YoFdGLJn5YEuxJ/q1q6OxyrtmWKBxKV/cbM9RMPg9x1Ie9y1PEFtn\nRFo/ZP/9rfINHJBFCh5kiGjdNMkdrGKh8fEKNFMLBGLXvZ6FTruUE4uAmyNS\nt+4nA6tdM9ikwr24fWoxVb/FL6AkQf0OGZbCtEI/eMV5DjggJW35wAJayuIk\nYu6s\r\n=MtXj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "embla-carousel-react", "version": "0.0.8", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "1fccf58739d7fadd2f6ed500880030f967ebfd87", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.8.tgz", "fileCount": 5, "integrity": "sha512-ydoJcVylp6NTyISHFBscGmMxwixm/48TI6U1tU2Y9J+sQ+9FM1mrite2ClhCH3SlXpy/0P8t0dWm7W9VMKRO0A==", "signatures": [{"sig": "MEYCIQCkOAQ/SlEjJECA39AGfUP4bEydDL3PK6YVRnSGiGOOzwIhAK/yLZwkTa8iLyQZlL5HdSNOKgpk3lRVBkZNgMDMWFYm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCrQZCRA9TVsSAnZWagAA7pgQAJEepU6c4YI4WCg9uchl\n8VkTxM3kM5JOollUQfH2rsJDTCMJIooJwknn3dlb8Vsi9y/yzAJWPp9kGTkZ\nKMu7hjYM2FTzvyA+rRmMY3COx+gbWXWdTLUnZGuIRafdOck1EdgoMSwJChvx\n+hD38MBHgGJWQxn9uhQZ64DUeT7sdnjR5Gj/KX7QuhI1WTiq4SjWlK9LkmVt\nd8BFueMT2Zbr7ARpXPbnqh2e1sKZWTW8VvWIDXS5le6rbj+5Ujr02WXMbS2H\n5D5CtAfZu8RAJ9Y13AKKU/cTpedHnL06iqHzf2GHJMuan45Zl4aGEe5N+eqg\nl3u4O/AdkPDXFrt/OftQuZ90bjRxDv9DEXBgdIuPsiO28yjRozX+pEHUmyst\nCvACnFIpBY1uOTN/IN2DL25z8gLxStZucjXchjnooyjNS9mq296gn2WDZszj\nGqs4Hw+UgJodGC7VYL7CH9XKGYbkBpSI8ECYKk51EQhG9DkJqDKTv2BtldmK\ndj0AU2pO/5JCkmKmBlOujYnxs5GpPr3JWXnXHx3QyVZ8SN5VPHUad4hM10SE\nbzfEp3Pdm8ZaxWxfOUjBMXZpRgUY6sqLqdnhYsb1JIlsKo+K9eWrn9YzsGS1\nkZVEaW1qudqDb9/qY85EH3ppPZXPvYWpFoDMwPvPVjhkR/rDQsHF9mpzv0Ag\nDxKm\r\n=OsYW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "embla-carousel-react", "version": "0.0.9", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "b2ab34d3d76ff6c72102cfd6e19c9ebac10621e7", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.9.tgz", "fileCount": 5, "integrity": "sha512-J6ihlYEp98u1rz4QtLW3loSdBSqISm3xoQQhAHzluYIrY8Ag3XXOUX/wwGeuy+eSAgMTxeadQCWYMkvlF5imYQ==", "signatures": [{"sig": "MEQCIC5+E8tOOJEIU494WS/yBrXp04+u4sGBySxoy/3sq6p2AiBbHo4n5RpvYRfUwGFBo/SqPmJzjAQPoiNf7QJllEDCbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCrQhCRA9TVsSAnZWagAA2o0QAIFUNsu432upuTYtwnbj\nHoKWQR6eAxLM2K+BncNHq5JDXfi23lj1DwnWH/F2VSsifRQZ6iC/JptSyzZB\nWf7whyNrXTktW5YCHDqlmZvDVpnl51QVN/6w5BZp6GDv9GRVdm3BRUJoMVFj\n8/qGC6nDeW1sQEYF8KHVjiu/50hoIQDT7xJc++g8qQQlhMMErMyOrxXwgExd\nGQiblYCihHV4oXocDORn4IOO/9e42jay6T6RoewAs+gwPrA7h3eb2mBxvpnw\nZ/o7u7PDkqWR4QLoodVGtLd/4Seq8F1jTcTY2Tp7Z7X8S+3IR2l7/FS7Iejy\npqkqlxD/6X45FsteE3jATOh8wTaiKTIcckLaGKgM9FZ203ME4Bk0O0JF+5pJ\nUIWjVXzX6muSPogktnIoukcXJ2JGqaRFHCGlyh0guHByfnOMPDLylg/E/1qi\nT1y4qy95mxI0mAlHbXZ/dBXMbgcMdRbaoa6yuDZZJwgdrPVL5PSmO9PPjSGh\n7dMDMgalmvfzmw7c8sbifSTud0lpPhV7mS0pA245IKLz1kGpMHhEtgkhMWXJ\nOpuMrFXQ+mYf2jMHJysRi8Myrw+KlRSl8Yd4+vxdmsAnH7XP1UrbbDHSdPaa\nE6XSxJesVdP6M+3C+fhVwy7xcUYQRI0/knB3Tl5hjzN7fWMaVSFuZd4OjjQs\nrokR\r\n=HHk1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "embla-carousel-react", "version": "0.0.10", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^0.7.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "b2f3255597a247d527ba2257a841a6dc49795963", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.10.tgz", "fileCount": 5, "integrity": "sha512-QYUyqEUE6lyTXsXCTe9g1j8U7S4v0nBSCDtTyitrJOcps7X2Tqg6ObRz7RlPJqXmSGhFsk3LKxmxXlrcMwKo4Q==", "signatures": [{"sig": "MEUCIDfuOImw3YBU/mpKZbD1bCSqzUJKNqQ5F0nFYqTiFA/CAiEA0I01GRmIS0tVWKkdO521PPgiW+IxuE6qhv5464KEHfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCruWCRA9TVsSAnZWagAAfoQP/07HGctdEiEVWj2zGWxF\noJFP1FzlM94e9QGEzsp/8eZ6LvNtpZZ6DM4JlaIR1XFnPne2bisKQchc7tDd\nhGZeezD+45rfcjXq2P4pJlN/aYU9kNbmzI2etMgf8Zv9uZqQho0ETzkQZZNB\n9yTZOXD88F9rVIO3E3NE1bDBDfgV0Lg4pEbuhyBHh4jvxXRt37t74/NmgYCu\nU/1e3SoCsCN42ceeR1jB5ADq6qY3JLa0Gj4ZWDbcT8E9aA+zFKaq/leamwut\n2ke23rNY0BzMuSY0305EbX+4BPqKGvI3/ngB0ZhU9gkenDKjKPAlbhRpwBZq\nOaITUV9AHuvsG+Agw3qA/1kkiVE0rRAQJjtJ8tTSK4X6YFaOFTY/OJwA6yLh\nqjLWg2q7lvLUdcOiDi8YKyXBRudurL+GnVXeidYyMDkrZB4An+688bF2Q2C8\ns+7DQsEOvRYjBz0vmMkXl0n28pZ9e58lQEHXR1No4eCE9KzeJiaSjUZorB9H\nlY60W6BzKpTKJfXN6O6rkK8cHABnnjc9fnD0zsjCCGzl7Zu6mJQsdoxlcQaT\nru2gF1Byze2H9G0FWx33iv/HnCxmnI2m68hIuOPqTw5HFUwYXt2Rg6A2m0ek\nV//jGeNVGZrQO1qudt/ekNn1Q4vfQB7K87JyU8p13qPefULjgwytWnNbze27\nQCRf\r\n=bnvB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "embla-carousel-react", "version": "0.0.11", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^1.0.9"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ae7554db594e3f11d072e8b073a1fcf3c8f410cf", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.11.tgz", "fileCount": 5, "integrity": "sha512-U9hHusz1BqYbFjq1gvBtvdYdkZHr99E8PJLWA+0tWuUZBjafrO49+gqNEuId/GbswVDcXTuolemdJfDWsw7W1g==", "signatures": [{"sig": "MEUCIQCC1ToizaTVrzlsW1Rlzzf0Iw9rRHhVJ3ZfTUConTOCEQIgfUP2eH+hOiGDCJORr7rMMdHQgrILEQX5umhj2m0x3NM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCsArCRA9TVsSAnZWagAA29IP/Alcr8le95OkUwK0n9Fa\nQmg/bLnIdOULVc9VtJ7La1nkcNzk+RWZu/9iSwFXYSii/HUSTx2DH3ASVBGP\nf89mp68VBhi0ebCk7uKWshV9ezsal9Fr6yfcywNWW55R9L2BOL1W0bDDJb0B\n0no0zXB6oMdrX94QaGKUYcLcFjIv8JgYWY245ja7SfMM+ivsBLlfws6wys/3\nEYgEeKbu3GFe4nXFbAFlAGmJSXzXpETPbJ7Y2pXCjGt6lF9u8iUXzN1gbdeZ\nV3VAy3lrWoMF0q+wHwD2JxqS4uaqt8q46JIECavmvj9UurznZRJTGM8r7A77\njcMhypqVnhUqJxGhsXskGpfi+7hDCVWF+1NirB2P86XYxbaITcyZKGWtBbOe\nt6hNBU/dgQAoay/kQGwXRINgHfRceHwxowvPFJSU58bFj6N4Fh99/l/OxWqx\nf2MFobM+FvXxc18P4ZnEYHsVEXWnR93hzH7CsNyMBmfg4vhgBjzApCvnoiSI\n2vIzCnPQCO3Jbs9yxsUVe4Vs6QukrX9zPodLLcEcCc2vgZUw95N0Z1hVqqlW\nuipT3WtJCHPxOrxRmmPXfjmG21y0u/LsAaYVfep8I1vzAf8HAjBQNkA0rAcA\n6VJap5NdTRD3YyHDeA//D53k2wTBIk32nWpyD2KbpzvHyDgFaQjVZbkgRqd+\nBRhA\r\n=Wp6i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "embla-carousel-react", "version": "0.0.12", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^1.0.9"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "4b33f39ce7de1e6aed491c51b518402a26054561", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.0.12.tgz", "fileCount": 5, "integrity": "sha512-ITF00zWM6RTUDm1jVHn5br3/E0UNq+ZySeP32rL1N3Vv7rrAMQkG0irCeQxRH0r68JoG5hg+V7fFWTWWjpTmgA==", "signatures": [{"sig": "MEUCIQDWsctIyBnSAZXd+3DmmKrlRH2JOmOlGRi2J72C289MvAIgdF7qJhuHupGRt/mw6dtKDvJ4uaPj6QNTdCzskFOkpEw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15503, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCshECRA9TVsSAnZWagAAY2gP/0obgQHc/Cu4jcO4jyy7\nD5mSb4HDHbthSPq4a+OHuR8s3A5PWsAwdqo5tIaQB81yvgybksHO5GFOivpi\n9aGBeSaSuBTZRTYgh738J+Ucl68D4+rE6Q3ehZWQmjCAHpp9l5magtg3u9+q\nOPwZr/SG8AJrV2MxrvMr/t5ytRTimVpC5gqVXzreCiHfP3f8euVK9/9oPzfR\niM++ror+mC0siaJbVo94eeArrS7deSu+ACwG7P2FpVF+QjdW0AN+R7QCLVyW\nJHmqLz4Hz60H+Mj7za9/skplJ/gX45W9+K5t9l91PpAOL5Vx3GofLfHyTQQg\nS+t3slv5IE05hKl9OGu43l7NWNMiU0A2bndf7pPSGuKVJE56m//OP6C/htDL\n/iRC63RFY1MikBdbebQAfPUub7m4uo01H/0N9Wf/rh5ng3CUis96uDGaAAhu\naZJxGXUJgFwnPghW06pA97jaI9bzjcgDeQXRJdeD9Su3ZYQAW4arq/QrOis1\ncDKTMsYUzTFHd95e7CHfDDIUcmqPVLhvz2jqHTsZBV730IAaI7ia6/eqSJF1\nXBPx+6IZbdm8jbrJoY/KWUWqN1dGGFae7J4vWzDhsEwrmTMz8B+k+s+Yrm2v\n0siWiNLUZPBtJD48C3ZNI+oIqlr6esPjBXJYz1oXVAjTUw5+c77iaJM9BgIe\nZafA\r\n=jGMY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "embla-carousel-react", "version": "0.1.0", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.0.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "db996adf12ac7253554efec519033f52f8dfb272", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.0.tgz", "fileCount": 5, "integrity": "sha512-K<PERSON>bOVNpahrSU0HcPpQd57rxP4PMHHzo8BHRP11VtnNQwcL3ONuULuMzCM3yb3nYY6nAYn0i0loxGbRUvE1p1A==", "signatures": [{"sig": "MEYCIQDbMFqI3olJIT90aUl/oVNo33V1kS+H6UHzpRoU8sx1HgIhAKUNwnlDT1/axIl/fwa/sg++HuycqOPYVo0xQYsAoOyv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEUxDCRA9TVsSAnZWagAAKaEQAJ8ENiK3LzpwomQ38j5d\nwcFH3FDljZDh/wCDWdS+H8f7yXHsZd02gRYg5OKwrGMt0wmbeUMJg6mqj6Bs\n7Is2V5bFH1rTbL3Noqb1TglQQXGxg7zw5XSXKcOjgGLXMcJ6B4aQzK9Zos2O\nOTbmq8cQ4bYKRGeXWwA/FGJorQHcdGy2Q2WvfsyvQLUANkTb4KKKCpK/Gl8Q\nJ2aMQCfsL9d48QEVMSBnKBOXUi8IBBVkOCg7kST6U3RHis2pPVRlfyJD7lqa\nV0JAy6nvE+dOcwZeHphIw8pSkiMZcBroCULLHwprty5HHpZDubZZdkJcQYwA\n1IVIq52DHUoamwFvoEdao5suYmcC5eR+Kfgp5WJF4T1HEz1mR9HsgJwyLe57\nB5g63VHMDWnO8D+tGTXaEt7AJfj5GI59DlyepAr75ZnCceVoOw2vX4X99hQP\noB0QIc9gwpSfROHGmaCCA2BjgWCF3Vi2GBm9nScl+W/WLxKAmhjVmEgBmxC/\nrQLq4dNTu0h3M88en9sweqK1XtKVwKPWtEqgnTTeB0J8b6ySpzxK9mF8YQAW\ncdcBbgh9HLgk5UbJd5jcN412busURtZtIyHiy4oRg+f/HFiE/sutNGr1H/uE\neNwQ/ASwvRZgMlJW+wFDr8Rm+46lovR+1anA+fNpuioqFUr0imbtLS1LG0Uz\noQcl\r\n=clNh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "embla-carousel-react", "version": "0.1.1", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.0.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "1a9918739793f56434af73f714ddb7e918101e45", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.1.tgz", "fileCount": 5, "integrity": "sha512-n5dILnaKxwJoYn+P/beehWZYEn4q6aQ1u+ytTEcfgP2xw82ppg/v3xczoAbE8OYDyNdAi5w/Uhfxc/ohaO8sFA==", "signatures": [{"sig": "MEYCIQC5kW2EFm0nGOu9CylBr2mLDKjrYVAI8Y4Et8ZJn1oNzwIhAIIFFd27cOf59e3r+AMFI/O59TE/bYNJ4o4+5pkKZTy8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEVGHCRA9TVsSAnZWagAA6JsP/14AgY7YJeR0LMUurKiS\n1VNe6AEmYoiAIDkNHstZ2bhYvOn0ADCXnAV/io7RMjfFkkxtsmfCQpJEP1cp\n9ktMi5x8lhXQCiDBL468+O80f25M9xHNEMerCQBTM5oP9c3sjZSeK+AuVZza\nURqEbklk7aMwxzlDpVz487VsEhui53OcUg+dMO1DGMRjWmZXYyDSFd+mN4ZK\n+OzeSmSyARR4bV6F0Lxprs9BUZlXCZxssidvtOjsPuDU+n5CgYuUoAXzgMYO\n2cf6WP1VIQqit/JiAQTlp1p/MF6GvjbEOmigtedZF26AQSgFN8yxG+t4APH7\nzQjXftp9DUpdbaIuN97oSP8PmTec94Q9x+cZ9vNCWSWv7yp2ByGL0M+0RyN9\nZCFrGyl0rZFUzOzz4grbySj6ODxBaHgbft7s4ZZ+dLsL3C+SG+Ty9yCEORwG\njLaClu3pLbcK6HNY0wGVWX023nD4IEZTJSO7V9cakgZaBYVFaAsgwcmzdqOA\n9UNrZUoLUEKT2HznyVNShRinumfRUuJzQhPypEQNUzdTuyXURYuXYnX2ib2h\nh9hf5tbDqksvbOcylqZlqI0BU3uDVRc1j7As3MIVabgrG86E1qcMk4fcEfUO\nCjV/HKgeh2hobzlee1F4MiSB0i+Ed4HqZmtlZlC1pW+fZTrAqjVfMbefhgqC\nkPiY\r\n=Wxnr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "embla-carousel-react", "version": "0.1.2", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.0.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ae70cfeaf714bc41b3d6c430a4b83d99ca7a06c9", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.2.tgz", "fileCount": 5, "integrity": "sha512-SeWDGwdaMkuSgntDl5qmNRxzJlcCzlfqo6hpd4AFCR6R/7sUL20MfVqNGMRYTri2rQPmYXJKZ8vNSRjpYPgkIg==", "signatures": [{"sig": "MEUCIBpr3MnB5uc2kX5nmm20FZztXsFlOoagWk4FHnsU3c4mAiEAyasDkdqG7J/3CnDeDTTPIwzIxK7aLYQp21f1skqmNsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15524, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHRlFCRA9TVsSAnZWagAAtLMP/0jj3SbTtLO6o0Chb4jm\nF6OmYjrw6tU0yf4RKd/aRK8fo93RCDLk8EoLmt6wYSYQfSyViq7S+xyQipST\nHh8UiI+gmICoAt64ZsR1UqDytxK6lmxeV7w6aU3/iUQ3jn7bl8W5oTpmh/16\nwuApeTBUS52fMmU0hfHz4FferntAj1bDe6Pc1mm9TdW/eITTNelhVgz5rpWE\n0v0VSnXsAsm0CpDCJFNCZPgAgPkMgYtvLMJaKgJ8jvjF1zHmIOr2qTH9kNmR\nupDwUb0YrJeJnI7lMyaJninuZV+XhFT4Q3DEwqRS+Bbsbx2Yjv9OsVWICuEd\nqDHm5J0NFNlqlFGvZ2pIFHnRo0DzuHYMSg7Q+a5u7llRRU0DzenLB9dQspio\nmSaMgt4sTZjSd8ZMmQaVrzlIkEuIDUVnfqhA3hQeErVn1EQoPWt/sSerUoDw\n0WiRnBn+3Y0o5GHfu2S/Rpag2YOC3d+rzdBiJOPYU/sARFw7yAP0OWW1qZ9t\nsQkZlztDQ8nJlgk0F3EzTEkUiNf80s9GsWoayleQ4blCUJkARom4oDEvpAej\n3uNCzUaFq88MXAHaXXVc7/NBZVF7HHDHm1ZdJRLt+ubPsYXm/iggIE8cBw67\nqvXPp6p9Lu9h4pSh1SlzWZTGYmUjyO8ID7CmPlRhVZuJl2mPpDvuueYyTQa7\nRP3M\r\n=ksbQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "embla-carousel-react", "version": "0.1.3", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.1.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "710bc3ef4125599235c47b987599aab46be654b8", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.3.tgz", "fileCount": 5, "integrity": "sha512-6eSvzGSztvhUFPFLP7kbOBlf2ZdCPjBNEwVzb9nT/BzlNReMnKfKS8BFgUldwKDfgl1T32S6cxTN9tnimbN07A==", "signatures": [{"sig": "MEUCIQCw66mJWaSAb5k9bSl8Px3RG88ESpiQQbpy0fiuOZAEvwIgcKxNBI6bpSGvrvW1O4dSZR3Scl3LaZWXgUEKGhR4CWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH+HMCRA9TVsSAnZWagAALfEQAIZUmRZS6oYIxsiIH+K3\nOCq6qzAZKDMeHCIhalxNw1QBU1ShOyOxUjKyd41eAPSsiBAnOpNxJAFL6GW/\nbl2TxQ9ftthdypMAe7NFeQBnIUWXv1a+fN08MWparc51LkU7YEWAhIN+CdKE\no3727a/K529Stj9uxrSmrKlC4zPEApM4OIzO7mQLGenuuA3EDjv4XTyAttiy\nmbq2cBAYSsXqcmgRfpYbrUhu5gh4F8/a8yFjl1yZ1OU+/1bUMbaQfabZbw2V\nk2UwUqK3KamQBeUz8nOBezqyStKtnxNqIWZdtdNLM8dURWTEi3tG7SwbWPJi\nMlGx6sP5IpOJB6GSijs70NPnqxOaq2MY39OJaEJ/9SDGp1Jcg2tMdpWcADlj\n2EsEo1lzIVOiOy9xjjXe0mkAIESc7BgAcDhMdbPzHAHE60WUhuM7Pjd/oJ5d\nUHA4MlTErcqXzBh3z+ubUf4Gr77/jA6QFwehYP19JDXyAb9+JjOTE0yJ0Kat\ngH5cex4brP/K7noOBd74dFDhLzsRro0LsGthoVvDVpDwrA4/mi2mRDeLBQNy\nHG2i5J8vnePSRDIyhCeKqDT8/eE/TPwHWVX7PY2t1lKhEhG/DUnTcfxkn1MA\nQkZC3FI3eS4vKr9Hkcjj7bVirc+s/j1mkTeavphHrSyR72cXydEeCmkl+LAc\nfpJd\r\n=/X0o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "embla-carousel-react", "version": "0.1.4", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.1.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "a656739df827db5209e86cad8acb0de39ab3e3e7", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.4.tgz", "fileCount": 5, "integrity": "sha512-GOIRGkgFhoylTdTTFcGCNW/hcDlgX3XBvSC3Uh5/7f/tqIgEdhNAK+PpAQoc9N9sFItQ9f7y849/a6bw+O0yZg==", "signatures": [{"sig": "MEUCIQCPWWDcYrWFs1PJG/NzjiPeh3/3n6vpN1pwyksRI/0fOgIgEXxMqd7CrTD26K/CnRfpb7I//UOR+gIKdn5K+8/JDWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15696, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIRZZCRA9TVsSAnZWagAAytQP/Rh3Gt8NS+2y6okW5OFd\nJ4LkirKO08GSQhGv6hAd4m+QcLu7OsCWCIEjbPHMX7C2CO9twqG1hccpwz+h\noojD+ze4rWMeqXt8jbYcCoA3n+PMixhbSRHFjnRBjPjVHYKKuQP7FUXIsm4u\nz7g9hhmya4XnmMv9r15U45o0lMojZR1pw50DBY7RRtUWnIGCHj98s99t3xfa\nCn4gg/vI+idtGkEbVNQlrGQIzApvascrQhB7kXwKzq6AkLNG8Atofw5rIK7q\nQfEwItQsPnzIQglO93FRts/RgjqyzPMoA/825uIWglSETkox58IJAnKqx8r3\n2OYPGX7YlVoniO3/1gO+my5GTrFcCXv02DKXGqojd5PkCW4B0YvyJj4CgTLM\nT3CYoDLkGJhSUNAT9q0YZ0maLSCWsi3MZKBneeXyEWT4bgFxFBZK4S7AwHgf\nrwGbDyqyzlLqCBVcaeAWDSfe0Yp4XXVFXIa2cOpptlK8meDMzgGgJPnxTlwW\ndaLBZ8ETQfFnf5CWTovCtHrb3kzaAh+E/FkhbpuiYLxGgBew7LRtYYquGUal\n1hP09olF3nv/+xiF+uZQzSm5CV9lxdGytoPAW1fjdgWaxxLemLwF9tP8mayh\n2YSCjVQukCtt3PNIW6mebBL+3AS7rEsVcHu5UDECm4DARDET18ZrJLKZVVXn\nDIc+\r\n=blBM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "embla-carousel-react", "version": "0.1.5", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.1.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "f57fb33dadbd961f24122ee0979aba96b39fbcb0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.5.tgz", "fileCount": 5, "integrity": "sha512-Is5I+5hrrIJ6cTtufDVPmgIqMm5/qQXDtCxPPk5IDu6lZD5iR70fkFG8Fcw2airU81+ziLzcOgEHk7zhMW4wEw==", "signatures": [{"sig": "MEUCIFjOH5v+tsCeKI3px54/mTGAES4sWgogjzSA3dClb4RrAiEAsNK6NdpBzMv2yuOSpyaJ/iqW8q3nowgXbgY5PVqj5Uo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIRyaCRA9TVsSAnZWagAAGZkP/16se1LBkRqPtqs0Lfah\n/u+zg6bCf1m7ne6lJMftkMmEvJj9fxd2r6vEPsyHGLoX1K2mPZorSU6qzAKM\nuozSulFGNEkm7QUIDm+093M4b8CHhA4IrvddUgAVYJ7AK21D95o9XBpXCkJY\nDlbmPf5azR0wLRAt4+cqa95Yns5+Zj11sJhHvNoukEurNj42PMWguqyBDOC/\nSsO388vfbFaVTgvOgOBHmy7ppyWw01hlQwID2GKpuOaSs68qsGR5l+YLxnLo\n68GWI9QvZAFLT9MPZn+qvIePxlEySx+LqdNyLa191fQQndZUEfewfdgchw3K\nA4OujJrZlMjcgB5wM/weC3+vMnZmdsrjnuLQdikMgPiwb2jOz17OcbrB1QoT\nzuoQhpjN8MgSunzMyluBgxxj/VEagDaN3gRxe8wxesZpxi2/KA8rN4Xt7eLF\nc7jUiT0ZoAQPyePntIwzVPBPesdMdTg3GdzPxojy7sBp8EecqolgX0s/uEEn\n5KIZyWxcmaCtakIVtwC6VulTqudiHeAfl90GSxIl/8Q0OmTQ1gVcuEFhxUso\nrE6eEL+EWhT28wgVTTOYdiMbQctw/IZ13gGFBO10RhRQ9gB7O63awbG6Q09N\n/soI6AybAosSrba9yw3OIR0MefduGWQxk9BtdZ1eDRxKEXWUC5bRtjhZFBub\n2W3D\r\n=0Quv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "embla-carousel-react", "version": "0.1.6", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.2.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.16.0", "ts-jest": "^24.0.2", "webpack": "^4.30.0", "prettier": "^1.17.0", "typescript": "^3.4.5", "@babel/core": "^7.4.4", "@types/jest": "^24.0.13", "webpack-cli": "^3.3.1", "@types/react": "^16.8.19", "babel-loader": "^8.0.6", "@types/enzyme": "^3.9.3", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.4.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "a573e91253a6afbc1c564a9c50d95f2b7f2ded17", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.6.tgz", "fileCount": 5, "integrity": "sha512-WM9Dd/Bf8asprHg5QVR4BIq/AWKaYiA3EhFqV28C5z435XXT3Kks5Ua3rG1HPCyKs7nu4v7jlVbBvh7W62gu5Q==", "signatures": [{"sig": "MEYCIQC4X6Q8e98T7lBf4hXK57QXw3ZOjIJtxKUVSSR55PVTrQIhANaH9u+fT3bIxiuMMqyVp3EhYm4KU9qIdFCxHeZmHP1U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJyKsCRA9TVsSAnZWagAA4DIP/2CH1e+JEsvQTBPTcsVl\nlWULLC6YQs0esJyfR0eFvvmYQuS72l4pHOT0AVsfdHLriD3GYWOYsogMcNni\nRx0iJpbL95tOt1j8rLQxdsBYKgoASWg9Fkq3YvlE52941D8lq20498hvPipQ\nN4vVRL3a8pdB/qhbWo21Skz8LEV2xKHxQW/t9laaxoj1SGiKvAdR3VWdZi7s\ng8EiJwbfu1vc9R/a7MY1uBYkkwpHfreckK7HGcHeeMgtWxPzMKTc5RVBIN5P\nvXgph1eJCPEtyJGMDV9gX5Ldg7AzRvo5hJtOiB4ah9eJCOq22/CKbvfjnfEm\nCTC/T/XisBtXP1RXzO6e8712N/E12DN4YaVjE/jmCQIW2WPn8PzPLjc03YRk\n/XtJK/v7Wft20taQ7LHOumlSA+D6UdUCz8e7A7nzPixrBmSCmLYbuk/vxuQb\nKxPknbUALaN6bs854fJYRhr+IYtFRlTuLNYcm2WaRlwRewwnhsg92spq1gi2\nhrlJWQnPUjRXZ4+e1qKK9DdvMhfCIAouQSDCNKt34exyQ8loeg80whuy6vQt\nqj/oG0/+zOY5oQG++Dz3q3sYlHv12LHxkU8JdsQjzIKJDjBudwIwxyJlHbKw\nj4nP2r/ll/GxDVw3TkazkoLr7G0vqQRXpPv/6QG/pfHRoinsz73gp7qz/uFC\nXbTz\r\n=xSSv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7": {"name": "embla-carousel-react", "version": "0.1.7", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.2.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "9d332080aae6ef52e1d60ed85a6620b7cd18a0a5", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.7.tgz", "fileCount": 5, "integrity": "sha512-wop9kIfSNHuaORsxiVfdaEo5Zb1R74/Z3tcBpaOciB6cNZYqdE+ZR1NML7Nf+UHaTaOutHZXrHh4t5m98vLK7A==", "signatures": [{"sig": "MEYCIQCmvlDi9aWYkgVsz9cas0BG3tkXOe7MLbrllPaAZXQE7QIhAOclPSLCUzKkr4ojdTng+zif+nhztVNykZUbl91UpoUA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLEwYCRA9TVsSAnZWagAAv+QP/3KXjpnN+slPEBaIPGQL\nDuISs6bA5yKrrjhOrNXayzjfYE2fQsI8MhiQEcBLuSD1uTDzYEQEdMfjE+44\nFjRSgvvVi43WSTGyX/hu2nFYC0S+DcAL6KPDMzbvG+OXjFf4a33rrYCJFUmG\ng/4CHDVphx0P3dd+Ofq5GplwLKDrLP3jpmG7Ak1QigIhqCfi3l4xZwhce0if\nWjZ8QkrKHYgF6mQWykLP4+J8eUon9QF3akf1vS9aEh/zWFEJKa5UELBFjYlA\nyx/soz+G2Pmdo+gliYEU3vyI7HwBrM21lyUbtICTh779Rh9RN/rxMYlhzkWg\nOuaRJuOAYAtTvfF3CqDO9RK/ig8zrTm55mmikNSYdJhpHE/WsXSg04gJsFSc\n3WzCQ6MzNgL1reHZ841U6B8N3S1BvEVQYhnIO65Yx8IFwzLmsOh4Jiap2o70\n/qw7Xt5kSJXEpZjZzwWrsulk+i182ytQVF8+q1pnct1TeT7E1T4kkHtlWI/K\ndjk1wGDPWhpNlWxU0y7zVJW7P382wlhE6O8KcuQhF1jCG+3vnY3hr+mb07O4\n0YBvFxlQvwgYweDKDKpjnVW3gv1Dlp3OMJwgKjxro9Y3xaHbrjA2aLnqkT7R\n8Bwcug9FMaY9EAI7p8aera88vc/mPQpPvxi0kb4JUQKR/OeUZrtNQplW5mpg\ngNg9\r\n=JPDj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8": {"name": "embla-carousel-react", "version": "0.1.8", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.2.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "493f7068a2039fab862600a3c80677c2df47a7b6", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.1.8.tgz", "fileCount": 5, "integrity": "sha512-xWj8N9c+RP/rgokSsSQ9yFG1eicyh2LkWXdY1NP3UpKOH8I16gTBDx5JXAOmVicXyUEJcUjh1n43TZZy4AU2iQ==", "signatures": [{"sig": "MEUCIQDnxMnTlr/iWI6RffB6/dTtxY5chI8s32tNGmc5TNIlmgIgEuo0wGTMwL08mnZ0zCLmlfBy5xvawZLa74SJtjOtUO0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLHC8CRA9TVsSAnZWagAAUHwQAJs80BawgZpx8rM1tp+n\n+vTDgif/MlGfDoFGpvtpR4Q0pPf7K7X3LdZ+tG5jCLJPZaGyJMbtfT8QmzWM\nYAgFVQeLAq55hVklVmXrKzxJ+kJTTIIashhstCZyLLaSfNgLK3jXqCjMXOWb\nqxv+cuMHiLI+2Ix2ePbIoOA/JdffzYDIepslwY2rcvA7u0pd0LLLZGlJQO0X\ngMXxsRfmie8pC3x8ou1b2ykH7O4UM9BbI5gDBo7rlv7BKpfhfTU68ImqtZwW\nH/gzfK7HEUm61OXmF5E+LVNMK7F41aTFowuJMhzJ7agkwteFFJtVXK7OeBvx\nEcCNBDL0uwwxOYXI0MSyNwEoxTtiDWrpGwcQWn/6cAiAnSEKVdF7kXBP35bu\nCK1W/C4o9lN8qr1/c2atjxBQ9ha1eHEIaBBYuAPobRDEOLG6doQ8/cfOsZmB\nyyuhOCWa289LlWRPxnQJdYmSi0mwb8ffqUDgvfeyImH4CiVQGgzOQ7rjU+y5\nHyJomZGSlacJ66R89voYOBsRFWKkydOf9wZ711T9UVHJbwwcZT6L1a7OJS8n\nyeYkkhHOzQSnmGm9c/rUWP+ltNqShqKDkjmzctAVl5CGXaxbtipRIEhsNtVN\naJbitQM3zEj0LiWpW06uo/8t1jwx3Jug0823ChQPUtHvoaVi1OhEo243nEIg\nflfx\r\n=non1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "embla-carousel-react", "version": "0.2.0", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "dd1274baae4c34818337fd9bdf4b4ee3ce538286", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.0.tgz", "fileCount": 5, "integrity": "sha512-vfovF8a2f2gQzA/3+ToXXveNuP1JvmUFBNBaeVBDOyF/PAcKCVlTKQ32yv6hAYQPfuDxSirwllTwZRGJz4N+OA==", "signatures": [{"sig": "MEQCIAHd4/scxn1+gROfqUniX+yLe0WRabZ13JopjPulQBy5AiBu55Yln4XsS/j00qGRUBRqsn7YdUuC6q6W3eS6hBzNvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLdyxCRA9TVsSAnZWagAAU5UP/09gDGyO265fep2r1H5x\nm1KxuM1Rm5Z0ZqRkt5sK1F0zAdquDiqZgXAlhvFG7SPgZWyPKP+Lz/PZhPfa\nvFJvIbUdt6TVyn9hC63A0ZLK01la+AnKtb0H6ZKEwmrC5A8T/ctnDMFJBgwx\nzmmf/HvPvKPBO6TvjlpnJyS/NsFJwCLK+lT6rZ59TtBHDyAzK3d6+Sr+2RGd\n2ovfbOywG3gccOApcP1cQHwuBYlnjb0XPhII7omUE/wy7ZCp0CNJEbmOX+eZ\ntf6CxvxO+sJACbhXz9Gmjp7wTe5Qu02FdqTkOkWI7Q3xrgVkcTS2pgCGK5ky\nMAHZmOF54JSuxxFJebHwxUKNLd49iHehWbQ/X9Dr479ldU/gpcMaot/uvLJc\nPSEZxj6iFFAqr8DSM8MpxqFkVJZtPXMOSluFslf5OsSrouanbNOl+SA9AsXQ\nZY0qRlN02TjhZ2pcL0HLt5OGe6ATGOrlN4evUHeLfUu3a6KsUuSLzK3/TWcE\n91cs75dLII9G2Mh19WT3LlhK/yY6ClrUFM2oB1hLRq/OoyP5hexMnoJQXUv1\nLTrx1/6QIDDOKML8a/R79QwGS0rVbea4k9v1LwsX7HqO2QYH5mQEiX+vm26d\nZD5+waXDCLWuouS6WhLcjtz522F8MGBF8lxveP3BhVY9a6wibvXgwF6MhQ1z\nKelF\r\n=Llrt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.1": {"name": "embla-carousel-react", "version": "0.2.1", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "4e4a445155f0571e32e84e3cf28167a741feb8eb", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.1.tgz", "fileCount": 5, "integrity": "sha512-Ussz2IkQLVdiSZEDP4tRNUBqBgmaTCriu6Lm9KAwyArwzVf7AUeSYIzNTDo3krGU0zrjb6PNY0PbDOqcCQN5YA==", "signatures": [{"sig": "MEYCIQDnnh1/qNLvzAfRX53PQaHyHfdIsWkaQhJ/JYdlvbuS7AIhANQsCYGhSPBkmidbJ5fKnhslhVBz7BG2q7X8Vm64axQw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLdy1CRA9TVsSAnZWagAAnPUP/3wezSv7qfhLlc2lk6GT\nW+ZVB4FBSfUSubjEvXs6L0p3rDNR08+rjcZpZr7xb9EHxQ4adPb8nMPyW7PX\nztqqjZqqcRW011pyCsLcjtG9nuwAMDnIwlqu4e3shhjeh8Q49rWUzLluPP1m\ndSQZ5ufsNKCPMTa09OUKBR8j1vESS8uzjHuAIzkdkTyamgCcl5pIQPW0qR3M\nmI++ugG5gYe6q8BWGIMfIQ1TI959m10biwZJ3MFa0Ks4O91PbgfpeMuqwaNf\njlMvMHKF2UnlwsfAwjBnokut9uz+tIugS0gbYf+DAesfx7V9QjuLy0mQOoJG\nTy2JkXz6rXeR5J9iTvTxhlqH19cqTP08wlKvN9yarwUi1h2RbMAGTh9V0Ohw\nYDhTh9IVpmU/V6oOoMbDuGOhoTOehKxiAXot7xpGYm36sK+2P+92c+wv9LFM\n8wxDzRnvXrAOWcFJUQmk/jYZt7VXO3InshqkKHPxsNIU8UuORfitBKPNQ5fc\nu4N0L6BBWPy1hq8J/DSuq/5cAtBEzXdrOFQy1i+z52ojH3nZT5BNBABgTcFd\nDa/qUTvznVifepDiFYjfKz2pCcSAiECVntFiBdkM6qt1Y1HnOfsPawcszmgp\n8EJqUH1/Zzt+T/03gitwfVx+E1lhQzckiwsCzYrKe9fkiYaA8s+td1f+BU4/\nGGoK\r\n=NEmS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.2": {"name": "embla-carousel-react", "version": "0.2.2", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "71a0316189c72b1b6c49c241ab784704e72c7003", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.2.tgz", "fileCount": 5, "integrity": "sha512-yFgIOpsXewvUWaoBw4JMJDZuhWwM9ZebjeskFVlykH79xCYsE7llfsT0stM/RG8O3AU1IdPYu80DunNaRiFZVw==", "signatures": [{"sig": "MEUCIQCXN2Ev0Am7dAzKztbv++DrZp/Q90uGVB2+YVMBWY5y8gIgVOK4dlF3Lcs1C6IgFtb7+E56XpX5QC+WZhIrUcPGymA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLvdNCRA9TVsSAnZWagAA79sP/0C7wRJY9DBGDCjUMX/4\n90/iHj2+fZy91bQKUl8adp3yvngQP4fOzTKJapb2ygKK8+RdP8iXmGAVy4s9\neGpzWBzZvXbPEonLzpGxQoa6roUHN952BTqTpAgCLNc5Qo3S+HBZzQ32OLJN\nRhaE/BfI11UZWAkZxN6369v5Ib6XZd197C/I+yhOKcYP1zYdkFa9ax362iFE\n3xhXWWLfvb7YwFemJ/k2/BwnbAZEAn9g5A6y++O4iUa3YoanvLXGcpP/K1Ja\nRHQ3NgX7kLUxSa9VWqmSmcjrRonlDB927Y+OsgpUsyG+WVYsAzcavf1+rPr0\npHHjX5+zF5Q6VhOIWHH6RYlTdkuab4Du0gGvKaz8e+yS08a3BB0NzhVVnKmR\na38mL3BOWLoSlv4scVsnNlIAiC37jtUruFMVHY5RQNFHhQF7vdYJOXwg32BP\n/HJlatRh0oFMMfUc6o6dV1MO9PiJHoJ0+qy/LVIrO/chYEyDhq4ZjAToBmZI\nNxUzrCsU7H7uyQGOKQHNLcrtuQHObLxPhj9M4zFicSV9Q/erBX1shBXmtc3j\n/z2eZzxa3t76VE4WFOaCOxKAtwF6HrpMN+8j7tsaHP1UlOHURmibG7GehD7h\nUg1DQgB7a2YGpXXmOvmxx7MIao24vaLL4706lYtfthtgroZxkF28XfCpFh8v\nkh04\r\n=rmCj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.3": {"name": "embla-carousel-react", "version": "0.2.3", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.2"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "76aa32c6464afbd37e93b917a2d8dd431fc91405", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.3.tgz", "fileCount": 5, "integrity": "sha512-fxEbHSxY/kOMKwMmo57HQ6HY/G2y+eIgInCYiq+aFdrEASNMCK0k5vEsIiadLCBJGJtFejUVzwMF2l4e+ZWX/g==", "signatures": [{"sig": "MEYCIQCTTxbRKicn865f/GzhiUlI43QF67Pdzyl0XeDFk9mtTAIhAOGrfq316tLbs8KarZkoBtaB0gYw6TOReDJ3WpGC0jrK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNhGGCRA9TVsSAnZWagAAydIP/AxXTOT3Ox098qaLeyNZ\n4ifN7HxmpiSJta3tAAGirUN7qA5kpfYYd4KOvB2gbz/OdgfybVI6UDWYDZRt\nNK/B8RvqgFvfDTx+mldu5UWNQE9mzAIh5rI1480+mokZtxlpOeuNlE9WuKVB\nI7/CZkY6EU+nt2fafGeVls3B7H4B5OJ/iGSOqVeQ9XUSOAesNagDIlunmjdv\n1g7Peg3Pmo0iT28JV59IvOESDhJRT3xkeJb9zI73XehP70PemV7mjGO3ag3P\nKI7ppHQ9N3iJ7twoYLJWAVcRkfJ9tHdnoFnWAIvPTcCTp6quUh5DqBoNlYRu\ne9BrFpXYJvIFpXoGYuGHoW1LIwXZ1YlgCjZ0zGTh3sqF7E6cloPrK77Iof6Z\nKIt1b0kaHplg4zoQfVCvoAfarTNd/1Xio3aO3Nv8Ag3lMTFr22eeCCsZMTez\nUD5b6B8Nnn4ttJPQGnsLDU8bA4NHoJ1eSXCc5TjvmtG1wQnsrwR3RETSHowz\nzGiUUvLHcdoukrgEI1PTJki2wQpZFYDFbmilAM9U3uDfK2zXs+gcREz6AReh\n5c52YxL+mcaP4s3MhF24PAaliWiaFs06eOPax34gv930Io+WicIxT6GpPk0G\ng7ScFfK2ZAaxLRIt16RIzYu5BhQ0GEtaUHzOd3VV+QMt+AdkCk5lvpjJwrUY\nXfx/\r\n=i7NA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.4": {"name": "embla-carousel-react", "version": "0.2.4", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "268c985b69e6fce541fcb69b86791ed81b398f49", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.4.tgz", "fileCount": 5, "integrity": "sha512-yFg9eigQyrKwRAcGOeMoSY567vUS/p0nB/vSU5USSIIthnKNxxlgjM0mtrtrgDmqjl/p4/GLh2h7F57EMDE/Fg==", "signatures": [{"sig": "MEYCIQCWQulWfVUZHDlq0XKfPjnw4963A1tHCB5vCWQS+0LwagIhAKx6YKPB5PR/z5/GYeIYD2+jSexvDB9P/mTnp/1dDBHv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdPKUNCRA9TVsSAnZWagAAQHwP/2EDXNUkdbJZpTUtAzbe\n+Q5oa82BD01X6ehKJA65EUqP8Un3wkXm6fImI4CdLKCEsDUkms0gTRYj8PY4\n1KERI+mpZ4g3AL1masrTwJJQ2oZmAi3VnXOTRG6+DIjqb7yR4YZTaEo6eScT\n7TrEYDdTiv7hznqZE/yYZqNtDXyICoJuGJ6daFIIMz4EfEaWZ51lES4ZXR2A\neSaAjS8GGOxSvfWhSXrkVEo6c5I2y5UQsGnSjjTMeEXIygqoLp7PBtb5t5Hc\nCUPHqd2ByGJNd2fTo49fDWFiFRgmWj/wJkEJCuUPVD5afyJmeBUDld/q6svI\nYimcqewc/fBxewpGVKNru4pzgcSJDhN9LxM46T9sEO5dF6Q8tf9LKdEyW6Fs\nal/E3UjfCopGeEeUfaFASg9Rj98YQJw+adhEG4mIWe/Z0b7yJCNkQfd9IAyj\nXMm8IZ9QkRsCj5VXoBtgkGRAXOUO/E0+fgJfF6N6NwvURB1O9TkM9wY4zXXK\ncg126w7uE/a6wpCl+bWlcYw5UL3GScGS6qaQOoIXzUg9YHmi276d5ZDrjZQL\n9kL59B1bvSTlKDLY8RBreAZhp0irQqEoeLBSgQiUGo5TjDM50QjeiS9xAq7M\n3hckatyP/3AGNJOwFNlt0OwTEfW1WXqG5fviQ4+yvUN6s4g9i0xLeDHOJeOa\nHnUJ\r\n=NefV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.5": {"name": "embla-carousel-react", "version": "0.2.5", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "a55907b2c94f1cb494757afc8b167f9b2f2d1009", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-0.2.5.tgz", "fileCount": 5, "integrity": "sha512-6ew6IJdyLwLCXBBGYXdkdzFndYiu8Bf/0Ia21BWM9Qa2TIG4LnFTpami595yJS3nHWCntHz9vIR11EPUq2mTSg==", "signatures": [{"sig": "MEYCIQDkGo3bGB0YIkOk/TrnAwn6m6/T6HuxeIlekMBDGsNlEAIhANGa1c/ZGFuETU/MelwMlyUY+aYpmm/ggnsHIiPmV7br", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVX2mCRA9TVsSAnZWagAAs9EQAKPKtQPjX8zGOCgGyczX\nhgoX2dY/Lx8Hu0rktQBxBaVnQgCyXUWXN99SLBhQSVqY9cxZmFBXv2wY/ZBF\nI0+/e4DxZMBlArsMtZ81O6gPglrGYoY1XQeeiALGYd3+RC7dp9TqhyTO476o\nhEmYrqxU8zWBVSekuJVPya4/qSqg+cKQkX8Fmnhz15bkUuLli3pQtdlTbJL5\n/A6XwxgMQ1AiycBqtuIHaSj/+qosfBOQyS/3hJXO8l2vJrxHqnY3+FCyaMSQ\nn0vwVQLDB4ubDB+4w/2K6cswjPP/Zha5OG8d0cxH8RU0I/D1KVzpTzMIFScF\nWXESDbXQw9dhlGBUwyrePvfKTwhvSUut7jqWoV6EF4NUMtD2csrwTj6By5c0\nx6re+7EC23olxN8UESsRxl6uGeSeAnLCsc2JUTbXC8SEfqRBRqqn1suOsnaz\njgXm41jDEu+tCa6XUpApvG0iGT3B8JaO3w8H7ODaMLEMn8KWzh1oxrCgYL5R\ne2Vh1IqldygaQakJE+qklY15/889qOQJ700OwBteWXuOjIFTbYrzwWzGyRfV\nFTKjsiTL3FhM+1yVfx8Up4yigSuhCbTQp7fbK+yH6Ug/mXmRDdBtbXJt1ZH4\nlyJvQ+8UZNFuvXUH2F40eo/2+CCpaDV2IEHcQSe7LkpF8UhHVE1AH6bh558T\nJpNE\r\n=58K3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "embla-carousel-react", "version": "1.0.0", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "27d4edaf0fbb7262988fd382521af98568dacfae", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.0.0.tgz", "fileCount": 5, "integrity": "sha512-mVyu9QdLXt4L4y5Mvn4T/L6rKpuTPK0LbkzYge8GsQG4QKaFA7uyrKWLBVpKg9Efw/nVNCu/udJGF/krKJr0uA==", "signatures": [{"sig": "MEYCIQDZajbKNQ5hTixpStmJJb5LojXpOn8qCXfydUaDdSRpyQIhALWYC4xJeKPgMPOLh5HY47yRWMvz7/OHuFGcJ8QzkqA6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVawKCRA9TVsSAnZWagAA6mQQAJFQyZgKF65yjJ/FyoLr\nxEsZ9QoPDKUg9QGUXEZP6QdDZ6rGCE+bkEBhR6p+ufKXDtNVMHfVB+zCJ8tl\n+8jm7sN/H/R61fn8yBufap066oZ2Uo2uiSOLBEgnxK35DL6kC/3r+no7rlEm\neVlSV82TqdRXNdmJKBiHNDWZt0KmiboIKwEw0MzX3Nzew/IVPwu29m0+HoI5\nbfMJT5tg7sdGnAw9L8OG82xWM4MpdY5DIG0gNlRr9zRDu4c+XN1foKiN6dZJ\nVTEeSS3uQfLgazL9fpgw5tPCbuIIngU/raiyx6wBeHPKtOp2MKzvdnTqgKJh\nKElHo386uGuFhsleztO6sifgKi+rWdFvRstj4gH/FzENNf1U7kkx9G74jcbx\nHCsdQpFWyV+7FsmPbNS7npblJRuHkFyqfbcYhYAHl9PturgGGKXLfSwyQJQL\n5DfZuYSiK5SBEEU4ZRpwOIo6As7eg7puyLRo3QlDfSmq7C6d8dhHy3PL/0mP\nLYkIx+Z0FzhoeeT5QMCz1Qi3RNjZzJ13ss3jEDG79FSmWlI+gB2pCrSLTAze\nVZnJV/QA5rAYsPULmu+vKAIVRC40cVwWLlEvQ823EOqDPR4+FoyUzw9JBNsi\nYb2TzJxXtkcitNUQ48uFuiQIEm2fhhWLbaUOPa/1oxwqGr3+LHQ5BcKFRL5z\n8zYG\r\n=3Wl7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "embla-carousel-react", "version": "1.0.1", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "5101f996fdab67f1ffa765cc5fb7b955e09c73fd", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.0.1.tgz", "fileCount": 5, "integrity": "sha512-0M/p4zzx/fIAtLjiKfWCoKfyE/AwCtMG3z/ZoocfNksxs4+uiD/mLjovMeixKVONygASvXXXkIqbWsWLdkuAZA==", "signatures": [{"sig": "MEYCIQCtSXU1ryg7E8izYFP0h3FuMCE+7T06NA/+mggqs/9q/gIhAN/dS7RR+sLHAhIbu5KSa+VmTUX+82Pgaxcogiddmo18", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17170, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVbFOCRA9TVsSAnZWagAAaD4QAJ6khxokxmavwM7/KpPz\nIu/lnnDO1fKNrV58IwTkcWc4O8AKiybLIzxcdjH9CLlzt5ueVhrVrvSMAII6\nfL5UXD7a9bQdebph5Ermmzha5cF4+WYnIi6eGs4LQ+aQbERopvj6p+1pHdT/\nRd+kheKbWrjPTquMH1NumhWNyCMBk2mVEZpKQlulZ0o6X6THRQZRmOUSOgri\nUkatnuSbClLmR0PVQuVMt5t48ZPLpfMAeorSCLS6a3T1Yu3kUJJK33EM4Vxd\nTPcSU2NVpWFHgvVeRCQoH2vYFVc25zrqs1U3Qv0CxvuRIjxhIoWEIo5zdCBB\nJNVxuYwNTFm1mh3pojpoU+GqDuKJtTNYN+UP7kvHlzZOyDLeLvQsV9RpGOVH\nnX/peMKJjW043FmaItqMsXPzYwpFftMUsINovbxvcSnKOFNIwm4uURSnh5T8\ng4IMpQC4PWi+gA8D6NAquPtbWCO0iGEK2lwwWHPuO2KJBNLjB6ZjeefYx+oE\nsxYJZMtYyNb7hRL/oND42YGNmVWXVvICUZPLf1+Ua6dopnbI88vNiHFrik7r\n2RNrNM9s1E/AHGjpz9UiRgAX1PUcN7sA0eAH9EP5b4CTHvflX8tOS52UDCuJ\nffshHC82c6HuDKfDCqjsLHMQmNLW4tsY2kuK124U6X+AnpsPvQ9o7qUXNB/7\nPDR+\r\n=Hu56\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "embla-carousel-react", "version": "1.0.2", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.3.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ebe3784d1b014f2be3cb39e672251473c9eed203", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.0.2.tgz", "fileCount": 5, "integrity": "sha512-iAlEUehd64m6GXnmGw1BH+rsxCQQjpnzXpZ9iFFeSo/Bg8GUer7rOmFph2OsyPgjh927+Q8LPIP5U8tHwzbheA==", "signatures": [{"sig": "MEQCIGoNut2bhHlVHLVZLahjhhQsdVJYAJpYynD6WMCX3ih7AiAKfcl+V3qN/C4L+yExSh2Ju3LkNkWy+tC5mFFsXfgepg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdWQ9/CRA9TVsSAnZWagAATJEP/2DWxB+Mfw6E9Td3TqPt\ncstmGOPWV2P68xarDPQazyAg8SIuUlEld6pNmE64mZqnCGE208uog/bAgqQ9\nC4JoRKhtd2noH5I1RRNzOAt9wn/8dhg4hsGMirZ8JbUwWaJtZE5F/Sixfe6W\na2ufAJg7HIAenQTqj81SiJ+fgsToA3inWWbywLO9MH5dE2Y27dijYIdlBWGI\n2Z0mJPbDKysIJggXzMgQy6YBE5NYcmOwjqYEe3k3UUCRyu89FTTUqR6A+6Wp\n7neeSmGnBPlEmt22zu6LDqJEX6d6VzUuQyKPpuVGQifrLvF+rmXEFCjpLzgt\nbdtcXMF0vipnjyQaNFNpxddBkoAfaH+ZztBfJj23a8SPBcxD71/9u/7wkpRR\nOa6WQImGUF3+dql0Z+ntBCMzlhTNo3aV8eK3I8+kFrsk7dlI1zYasuouyT7J\nICOCc7Klpzhad0FfUSAZY1K5zRTOLZY4ZYASK93jNPDOQJvW5Blz+KXc2oFs\nW7mSF64P/jyDOfZBTlzl0V9XAiR8GJKbHYMQ8dJ1iwDR/fGXe6itDnUT15b5\njQDmdj31SIuKVqu0+fx/vYBecc3PKqErkx738q1h794rXX4CUgzODj85uKvt\ndYkl0ud1irQ2w7j0hu/qjqH33DPUBqt9Uuzoi723Dz4O9hB78NM+wKqD27M3\nYsow\r\n=iSUo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "embla-carousel-react", "version": "1.0.3", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.4.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "dc85ed10771f0735047196e7954f12313442f0cc", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.0.3.tgz", "fileCount": 5, "integrity": "sha512-V8mGwU4t9lDLuD1TRZQEs74ipcvUN70YxsMIx58THJYSpa2uJli75TmNh9CcSTDtqWlIUmuyNehTYO0f+tE2hA==", "signatures": [{"sig": "MEUCIQCy6IsHxekWGH5CUEYyke1wJyToWBO10cNFsqW9S7dftQIgGGy0ZYEW+QroODjjBHX3W0zBAaRlwTAqDk9/lT2KHvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaV9lCRA9TVsSAnZWagAANA0P+QCtyhN2O4AjsaD02S3b\n3JZV6hWggVA9qe3bCJ1DfAOgMp8tVRiMdAElIf4yi6eFcLCMZY3cx4tE0nz7\nQgdc5dhdAtH8/TBiEKQGh9KTou0NlMdT2Bd1dD2siadufUeUcA+XZ6s6J6qj\nMoYBl77EIQ1XOYiegl3lPB2EekZS0QT6k39apFRTNFdwg2/LtrzWBbrE80GA\nuJ2wPyP0EDZZHO8xGAxmZqJoa2I1LD0MogdWHpybQn36iirUqfPXXZlAqfvR\nMzcK3v70/n7QLQHnynzMxBVapxMGNSKNthlcSEV/vrGfZtlcH8rKpHNYfh68\nGrVXpwW//dXhWqmdeT/wR7ZPbZFr2RzEwkKdQZFEDu5sqd667CIDTmI+jx7j\nsPa3+lj8ydvHB/XEhNmBnEIXxGQtkhQJYEdx8UkvncP7eFeJxhmC4foitLWd\nCMZQy8QDC6cEfnM6fdaWzIkaFnk6jNJO2qAAmOgeClGIbapnSqYJ914dt23F\nZYkm+hc/S1JSOtPEgP1jMCAc+UzCSAacsRNTAqt7z21ZvKfsEAqg+7rOAmGg\nW3cp32sE/Uvbn8kemkTwjuUd3n8jfsGcjaqqNIls0/zpgzqvNWyop9qpbCi/\noTbwtgKqaJnbdzvFw06uAS2MB8mWP2eY3BrCL7S/cLrS/oRr9W4cEBFIsIO2\nxkab\r\n=NK/j\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "embla-carousel-react", "version": "1.1.0", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.4.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "e59973f9e3e71eda0fe0bb2a3493a35f41b00d86", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.1.0.tgz", "fileCount": 5, "integrity": "sha512-KDDj106i0Ss+tSQcBZkdzaMqFcw78yMDHTaCGml0Gflia8/L5sUb2gfXs67XZjIWLU8zj365pnOzpEJlt7L3Ng==", "signatures": [{"sig": "MEUCIQCbROEpGhOhFMfsNJC+LHPsNu95BmdbV6cjWK7BA24unwIgV6C2ShaXaDVGxuhEBrChYOlNFYCtGQ3AR7zaOAsnTsI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJddRMXCRA9TVsSAnZWagAAs4IP/RU1kj0FYiQFBF9XDyZm\n4XbrbUv/vtgiXTwlzsuxzzA0xVillqxPgkLoRMejIb+e6dA16O8V5FvZi87L\ngK3Lcvzr6LfVZq8ycWuf6HmVPkNBu+ScA8+a7dJJmlty+jOGJTA+P8ToCBha\nfrKoxHBGDulXxTYCBkxWP+xgw+8gTL2JUp2lQyo10Xr1+RWmju3sWoVaNnFp\nkKyBevftGP8WTAsl5o8y03XgHAn7Ye39KyHkl9AYJgunmHdIcdljei3F6wzv\ncEb1w2oVaVwU6y1Q8K1pRZXaN8FZ7vGWA+OsyyQYr1BQmrLt6Mw12juErw3b\nUgdJbikXG8nUp0iRwdlc6St13rxJtIp1fAhrGjHTv7UeQo9ieo8NS3oimO1f\n0d2GWtmJqhQkveQWQRkkedKPpqMEjLiKn/StK1CCvu51XAIfBlC4pW1rL5sb\nFVLF6++hL2QClIASPqISxdlOdoWjowozfzMbfYhlOEa3B5c8k8zR78UUGbL6\nvj8d/h3DTdL8u206A10dY8WPtuOTeiTNSg6pbg7gycgFK/99XrCk9aknQTsF\nPntEyC3MXKLYkBR7SAw2EEzDN9AKgZO2UHDgfSO/D56LNCBWx6xr1POyEOLy\nHkO8sJo+AaD9EBwfHKb/Kl/9gbUuJhkXicFOnvbirWYr1f94ZFJ9Ff2EIX8o\n3Oub\r\n=mjgl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "embla-carousel-react", "version": "1.1.1", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.4.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "fc1e5fa3de70a0361f2d78d113b0f12761d029a9", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.1.1.tgz", "fileCount": 5, "integrity": "sha512-q3ZxHShYHStCIGnlSSL/smou40TfrxfDuqtb/dpVexgyNHzN8W8mZVAfYPfjSN7pce7p7Cah+kjRVJeBFQh02A==", "signatures": [{"sig": "MEUCIQCO9fdhp32ICg1q2VIdrTIxiCUetcSwbrlR3xVl0+X3rQIgAO9LIS3pcJbfccDtgALcGl0u7XOUAE19tpAtI2yYSFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfRFkCRA9TVsSAnZWagAAB6UQAJD2es18mTfdQn0nsRMG\nCZef7CWrD8qTMUyAoYpoFcj9txiLye2iEghBpzebHwJOaRwfe/rr4mpuBKN3\nS0gZ7YV2nX2j7Tan3VG75J85ESdBwq44Np3/7b+utILD/ilqKOtoUYiIxuLy\nTfexetFlSSHbDD85Dj4pi2M7qMWChNPQn1+Vv9NXc89eNRMq1whq/ThORVan\nRT9D2OF9Re537onsduOHaVUgGdFiGPutumbxXkWzuYjSWx4M/BtGWuPFo6Or\njoSFKDp/SpzrUL6sh31ZZllX3c2dYfGp/bVBxLZKK2RrYjiOhEtOhkbpVNYF\np3y7PRF2KAFspKsHfY7qmsZZTVs0t/w4cXHgmC9XQZoTcKaIpyteNhupPUrm\nNzFjxVnvR6T4TOoJHJj9VLsxArvzNHfskIg6C1JY1WupPsx2jhqaXbl69XDF\nvtej14fUZojXe6ReXS1dvw9NhCEmq/Up1dPvQjNUlUXLpco2ip0h3ExJjeaI\nORoiXBT09SiPjvrBRYSAmtCB0HZ+hQpwHppkqpR4YTcYCQm9AH2SAOJSpsgz\nawLRnSxmKSmqePo74HbfFo7eY0n2YKNXK8CDm6xagvys2Bn7DZLNpwbre2E4\nRW5AV93wHLsOi5p1q4ADR7ju6TCSgVQaZ3mE3ur6r49uDQqlG5bkU1T8lBMz\nj35G\r\n=h6nt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "embla-carousel-react", "version": "1.2.0", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.4.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "a2d71073acac8dc4f4c20005c739b248ed2c39f0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.0.tgz", "fileCount": 5, "integrity": "sha512-KaI99OE3jx8rRpp7D481Gy2f9AjVC3WHEn2rga1fgRgHW17wFgVOpwY7J/Xbdkr2HwWX4etHstrQlaER+3xRPg==", "signatures": [{"sig": "MEUCICmFl8wPSsg4SCNjtUbTYnU5TkKKDial+bQ46j+QVTOnAiEAsn0GUIfFmah8QscHyTTJREA6gKamYWmlGvOhl8EvmAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfR0ZCRA9TVsSAnZWagAAPRUP/RPX4WBD3zKszr/urEAf\n8tkdf19qcTdSSHcGLgd1eqJIqNeZSLCfH05iy19zNxdtdKhb/PU/4+wcmcjN\nqHXGYaSB0V27jnGR9xotyCtXUUNAYrzhqozyqBpleQBS63rvrUdjrHPuFabw\n2mlA8yrmTlCOk/EOjEf0pzTtVJfAucj3tmEt2DPAI9pp+TQzhEj+cVaA/1j5\nJu3lVzdKPpChUobha1XlIo+JlqSqZER2tZvARctqJOuRrkYNYsw/qSax337Q\n+CCZbLnJqpV+er4KevhxM1LKtaETZpCRX81NxXYKANnnaogZ6p0A5snCcnhK\nBj2FUYy60F2Kj8Ab8xuPyFHPtm5TkJALCVH5jM3kgYvMbX4eJ87HVacfh56Z\nh16+FkwIIN4OWGOJ1wqQjNYefa/D19gPWshJNtfEC/wCaUMtbSSFArJr6s0G\nylc7OsI/W98dUetKD+2PoH6uRlZXGTGuUfwhX3vdTrazI6jaZa6cGrOL9Cnl\nfN77AUgo7uHb6eAzj6kPkKW6qUkdQZUGfqRqIvkOZgk4gZDyT3vC8/XnMI2O\nQ1pvUbEWNHRyvC8E0l4MSSvAWu7MUpZrT4vDTcoALfs0fKMDwco7aoTXRQj+\nvF6TY/05z7gdyhQtCnTOWlHaG2HWlakNDwC3DROMbsaVHidGrt2nRw27BvYu\nkoHq\r\n=fSYZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.1": {"name": "embla-carousel-react", "version": "1.2.1", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.4.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "e855f6db71ba07bdca3e74b9bd69f0b955d17f0e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.1.tgz", "fileCount": 5, "integrity": "sha512-wDVEaUVd6mwH/b9P+81MAsbD/pAAnFaItfCJqFFuHJnYNzpvwj1OWy8zkAbdddQtj8i2FBHbS8mPCMSSwj2QHw==", "signatures": [{"sig": "MEQCIB/SdHIN5FcBwpySVi3MReXI/MSVcgwKMq3ltwP5eysXAiB82c6SgYmYhsPhLLC6L8otiIweZoTuGSwXHmjXFqyZ0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdfSAhCRA9TVsSAnZWagAA9AkP/j+yiuYpHApkKJTwINGG\n8vnWJV+8eKyhmlCWnBPPjP7jbS64F+aJZR2STN5asQrEbSl7qTzKwzgE5yYg\nUSDDF2JPCd3U7nLLjKDw/WlN3RVhXTXzFqpZUOvbZjxc8hjmHK3NQngZ1j0D\nqPyo3YHoiFXf+lXMNj32yRqEcDMeGCtYGRASZBBBO+17lQKDFTlybL/v5bi7\nEmbgC1SQgP6uIJKe0kXdKLqcQzFbByHBVZR1NFo+Liu/2VQ8kQ6ms4daNalr\nopLcBf8kTW8fUFlmdt9Wwhbo2/Ew9LcBuKTE7Ue9FFFjG42vIUmDaDeML/WY\nL/du33hv11vWfuAF/Tt1gmWISOKjQHB9XAaMXy8xlvLB36/Vw+7jfJkBJt/o\nXK3GCEmvO6ICOZYbmWu+dcsUGvyGMtNraqgpWLAI3B0vu31rzIIsMJOFad2l\ntkNzqorQLAimJl7MiS/bw4GSRjCn7I1zIq0YmVPdGwtc0a72GS6v3+T1qtlw\nR3nGNBt344Nxj11UhDpPNDTfHsnsG92OPF5bcY9AuY4WOBALqeU84O+ZbSe9\nilF7roPD7omtVzfjHXqtInTNsbTqmNMeLNArM0nwoabCMeKfVQ9fwEub+Rus\nyKuWzxPg6rCGo1YhpuipDTnhRJQHDPZheMFc7h1fcRMaLXRgiurXuVWzZ5qI\nwpqv\r\n=p/bF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.2": {"name": "embla-carousel-react", "version": "1.2.2", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.5.2"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "8c1b57a16e5f119ca36498ec0c30bc9d82a14d0c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.2.tgz", "fileCount": 5, "integrity": "sha512-qz+TmLHhjf6fhzHYmCAQ4GsQ4r6Fx7G7o6Abm0sqXarbJXU4cv5zjB9b7qSGpZD2hvtcm5kiC5JdaOJL6T51oA==", "signatures": [{"sig": "MEQCIF0JThBgMW6cQk/lgYeG4aOeD6M0ut1QDuFUNtEAPQEPAiA0we7v+UiVWrhiR9e6EOllq8GE2fQoyW5lbIRR12Grdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmhVBCRA9TVsSAnZWagAA7JQP/jXN1dy4ILXBPdshlMt4\nautOkDyNzn7coZmupKkXJmh1+Usg/fCu7Lg0xnabkm45wCWodMr7QCZ0IgYJ\nXtuHLDMVYt646WHLhC7KSrh22UYCYNCoCViphvStmsLMHn1SEqhOaihkUTyy\nmnma319DumRr3FA0wCQQjUIfeTElnd6JGCbebu5WTe6kE7A//201SPrIgaxR\nZ2USxhFOK3xeb6oM44i6QZ0mJbDOhMqDfXT+9QtDVMryaGa/Mvx7RttBqkpr\nYyleXTugboYK4/zu5Ho/X7kY2ln65JFpOypVGrCMC3VXoWLl5t5FK9SFikgL\nkpqrd53/oJG71x9+ZUPqjBuU1u6Dxithv4eqXfjSiUUkOf1Ci9pGKKPeupER\nRNe+pEwPIqWoAp4aRXyVtU5H3Mp7rX6bthn22riePB2h3QUG+D81dp7/kqkh\nVwT/MsO+voi//5YPWCDpeciaTwcKfvW3IXNByfuExooR1BOJH2uW/OltkDxt\nwxxw8gP38aFKqrR+x/gSc4MRW1xepS89snkpxn9GnRO1wZLfmkEzQkhmjLak\n3xfIHP7KWy2DOCvJa+bb+r7Mr1S+E0Y8FvrIo/V2ccevWqNWMuNguYUyeYV+\nD8xMKlpMt/WDaoqDOh8Qtkr29I8S3da1FCq0FYeHDqHG1mDixWOCHxemqYkA\n3juq\r\n=+9Yj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.3": {"name": "embla-carousel-react", "version": "1.2.3", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.5.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "8495707c45855ada237c2d6148f9ac9b924f4aac", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.3.tgz", "fileCount": 5, "integrity": "sha512-p9kzOHI6gYE9ilMYzR4Y2TpeaQVU6jMA9ulEQrFUVr/uBqqnCBw8KXNgkoTeUMUDh9HjUqsCtK4++N+dkXnSjA==", "signatures": [{"sig": "MEUCIDowlU9juQfsID8Jl/MtSoDQiBLDMlGGLHfOIJDQGLfRAiEAqFZ8VL5LEfxSwRkO/Atnew8dd2GonkSDofMZWP5ruk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17465, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrq9JCRA9TVsSAnZWagAAxNEP/1FJYgg8nqX26N1XNXS+\nqwHdA8Oa/AA1Vc89llS9zDE4pjRLiZbloDZmqfntSYuw3l80nj7o0VSeNUaF\nnicp64mT16eVKdxjDfiu+kauTRxFthsCvC126r7u+IEHsunvB5wmqn4VwE1H\n/sr8AI0WrsJMR4vX8b3zZEN6CfvoyuHmAFZG3U8bU7I4UywPtjoJGDH6vKo0\nFy45XIt/D47FtX0K0yMGv6PTc6+SOC0U7PK4t4NXw5IP4fWmpgiUrQm3LoTF\nd2nnay+EiKpQ+XOYfotyT7Gx+sj/66a6cLe3NsPh1Ckw8lQz2iDiPOdC2bG1\nWeA1o+F5TDQLl7pxIBdvrhzQ8Z4MKcP0hjJYTuQJZOwXqlqCwxVK31Ebj1nE\n4F+NRMuxDGjwJf5tMUNVsl+p2IW5pxUGdS7fpb1moWbOKk3Fc5bPyaiuQ/EW\nypR1Jv/Uq5j2RB+Ul+EBG4jAHcE3gTJuf0oeadb/CApTmQ2sNhG3ovlSqsX/\nF6sVqzTtwv15y20GE+Xd/Mk3EveXAiF8PIcQJzy210/EGQHXwtSG1hIgvtdm\nVjZSuiBNPINQ+ZMHYDElEcOhaW5faBQJjOOW4QFKK+RzOUK81fvzIchYMpov\n29su3GqE7Wq/IAGih5/j2Jhf7NTRHDfCNfTgEMGMczAy/YDM5Xne7figIIQy\ncWVp\r\n=60Oq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.4": {"name": "embla-carousel-react", "version": "1.2.4", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.5.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "044327d79dd09fc4ea75b731fbdb19e16bb5391b", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.4.tgz", "fileCount": 5, "integrity": "sha512-vPCBfkwUvhnnCcNaldWYV43ymzlVFaZbFQ57q+kRT/TxTIrWI4U3ioQSMsnAVXKO3L3Iba6pfQx0CmGBU5VZOA==", "signatures": [{"sig": "MEYCIQDwR4hm7OtWqhMduzwSiAMcL/7a2kg4vqTffCVdhxUvLgIhAISRFVcku2dzVbN8+8ANlLzR/6QQXr5R3mzbJZgTbT+R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvc1TCRA9TVsSAnZWagAAu90QAILG3rlh2SRHaotVz42f\nBpzSqjqiq6LJefbqcZ4PM97fBKhrwgEvLocN86LsP2a3UWzXi0XzF6a+nfXZ\nAZD75HmhUnON+PCjdj4Ya5He419o8o88c3ht5i9u1r7PgUUo0fLIQiItp6RC\n+OMGx4+lFYg+DWIE9zNacV1Mczo9s4m5ZXXRqE1ej3sJ45Z64xx2oQhe0Dgd\nUxghCf7iZNdmNSuJ2a9yP76ormO+pHByV0Q7P9Z/vwolPk5SdNnWqdL2Zlfe\nCkW49ThLxoMsLS7DjxrtBYmaRl6l121Rs68kxuYaCxAD/RsfVgUK3qVJAbmt\n1vwwyrgaaa+54g4ahn7dSb81+Dm+fkLXRfxzCsRVc1iTUtCUhlp5coe8eJtT\n5mPMfbgYbnwuzx56ggDFjswUjwiqdpn9Eq3JNLbhYgul3P7Tt1hfudL90wA/\n1cfHACYP+szA9T76lqySf3vmSTQ5oP5lAofmYb7oSwBg3cCdFKicF3X+nFf1\npp2LIbQjv+LrlyhTxRy6cmfJlrUxdvp28H53GlUWucgY9uxdw0BmP1O2HOOp\nzS/hro33ThISX0EwFxnTzXiwqAMToAmsI0ls3L9CzbXXzDsenU3u0r2Wf7Bf\nx68GM2GyuIFEHsaKHGhWtYaZML6MOX0yAYa4+yl1vjc5QuB9E4VVzYB7athk\nd0hU\r\n=L2ex\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.5": {"name": "embla-carousel-react", "version": "1.2.5", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "a430a2ff0b63573e15c7b7b6636b1424f9747fdf", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.5.tgz", "fileCount": 5, "integrity": "sha512-LKY+dISGuMqdz9WWKl5Dub6LL25P9anDGM80sV0K46qrWFqdfDyqLNgnV5OEEZcE0bW/fIUS+opYfdmuZ81Blg==", "signatures": [{"sig": "MEUCIH/0yEBLqjH2XeaurlYHY+RvhuWJPx8cSe/Wm7UwlLGWAiEAlMlOfCz2fdhIljwRur5Xi7FEmQGmgT0/Xo8kKAA9N0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzW13CRA9TVsSAnZWagAAQIoP/01lcCat0qngpNZW943S\nBQkh4V2d+5SIoBRNYGZMLWrDWuF+GUtbSmed+K761mG6K71M1zJMblaT+IqQ\nO+aZ+Z21M8NLMkusM/47DqqFpZhjmPlfOXoNJqaBLXs2S5Zyqj+LhOfUm3wN\nh/EtV3pKzHYX8xeUlfPJfJntAg0xkWF9EF4nPuidONsh5h16y/ZeZD2/08pL\nnIKbELlK2TImO4JpkQIsREqIU7IX8GuHROvbiTSHqB7EzBXB0WQzQARvHN3F\nO6/W6hDdVcv48WNQsI6smtea9Y8N3DxTs7/ISb015OzT6mokxP6E02Ex9NUF\nXqkjyEHeGvVRC/fALCzBB1uXmWMXGQCszgQ+ASwUZfja20q+hDiUCEN+wgTW\n85St9zshkwQ6N9tSr2g7z1ILn3VGyEEUD9k1fha+I4oprGv1ZGu8mM96AUTB\nnWesZAI2Ltuxi3vGu2lRa3GUZXAooDIjBXOhImro9yUMuVvYzJwoAKhl3aF/\nM8Oa4j8oXb6Nr5W1SgcvvActTE24YnZYqQgbjro/R/42Sx7MeC5szYUcIKjP\nTf1rPJqnHpa/VHChGNY3Eb+yk6FIzvzsIy4+00ipkiOXuZE76KWDEnmTXIve\nWuy/FOrtHfmZRc7n4g3UQgirP5ydw5U/Ipt9jgltT2OQVQjXaLV83wYuRY1y\nf7Rg\r\n=KS5c\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.6": {"name": "embla-carousel-react", "version": "1.2.6", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ba24fd74453fcd7c96ab3894c0a4d986f8d0d582", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.6.tgz", "fileCount": 5, "integrity": "sha512-4ZVF6PSagmnTzejCBCepV2ufO4QsG+eIZhfCyCLV0TsYynUBIjTfbKMYUH4JCsWh4tCHcgeunSiBrRluey2pVg==", "signatures": [{"sig": "MEQCIGAWwvrGaXvPc7byG9+ZkMwVMj37yPTLDw+F8Uw2EaHtAiBG0eJfN64AmS4LXjtrXm/8HcuTau30bsRK86A9lkPpwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0C9hCRA9TVsSAnZWagAAoWAQAIB5fHiIwMTvlZVccIDg\ndSRAxrfWtHx74WDIBcQV6MwRoseOKmHaai+iNfXYzRzTDzEPlrjs6b7Jw7VG\n+T6yqDs3NGQP+sL6KMrpvDICcyUXythr9MjCGQT9nLQdCNyAhA96GrZkxV3P\n4ZD+Lq6WHM99338zSvr0sgUm2qd++YseLa26TZuc5FggvEPUFKp5BvZpmGAO\nDFBOv0W9hnYkeJiyPelP9DlewZKf+QSa8mUXHQ0arAFccX4oqoDJL/TmdgzU\nAnxlvfa/jY7DaxHPokOdGec84LuD1qKtZPh1eAf5Mbv/IimTg9Fy2H9o3jpX\n+34xS8LkFkD02y4JD2J0ghMfZwLRgrPahWuN+CvlR7ALVwfhTyNHeluhJ32T\ntv2XQoG0AIOLg4ht7JLMCm1vt+msJ9gyWGJRA0Z5sSG1N/Cv0Jk2Y1C1zz16\n+Mz9puWOvB8laeEjXqEoFvdQtFYEBmR+IThyb+rKKkVODuMNLPR2rKqTehFw\nz0IQuWNsMb7fGl/0jWe6mYUxsfEknaaqBx8Zsps3xnyy16SFxWOdGFurXut6\n8SOw2k7qsObs/aHHqaFxgX9HYCszYnC0fGK46d73KaZaFlTsITa6Sy18x1jJ\ntxqooyHVi0CFfb0PSehzY6epI9E7UYNgMjVtytYQWxVdcIl7Al1uyU3y2hyu\nd936\r\n=5mx0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.7": {"name": "embla-carousel-react", "version": "1.2.7", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.4"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "b8719ffbaf38c068289c6f66fdfad1744543fa51", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.7.tgz", "fileCount": 5, "integrity": "sha512-CWKZSPJP12GIALOUT5s41mPYAdgqDR+iid0/4PrI1hJ090WGS/BNQ+6u6X+5MJaTNv9Cjvk+KHkxL+2jWgsJlw==", "signatures": [{"sig": "MEQCICeIsK3t5nEjbVFh5Qi5c7dumcq6MBXdG3p0tncsjWciAiBN1Mavmwk2qSZ8AcSQZNcoA2FlaCUedbphIp3T0to4TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd6O7SCRA9TVsSAnZWagAAs7YP/A6jSbH+y3TMXvTHMQ5E\nIq1//bVfPr9hXIOD1VExs2eCISsMZOkTk8tsLXzu01bhmUXyOQdtesIFUhg8\nujYeVYfnW0OIDDkqHliPiX3p7aUNnp9Q7sxBJa/I9xCipj6oSeyG8ZkLQdlo\nqNveRpzgvL2qOVNk0lFTOqHsfRnir2/rQIAifzve7FSRYz73Hy6Oo2WMyN4i\nxouSaLAXJ/27mhFsGpxXGQLC1mr0P+0AHqnAn4CdU6dKO9lX2R5zu1JP2N2F\nmwMMJzsQa+6qofYU+azvuc32Ypj8JulwDY64+DdBJMYrJZun8UPfjVIFyLaw\nm31Tfpw+/LcTtkrtWvExZsxI4+jbFRDgbh0C4/xmgGDNPD6GvYxoMs7ag9hZ\nz5lNLg6onf9NHCVbxOxLSB+RmJXa3VGRTUQC4KH5FtL8kvxkMi0xkDoecMBy\nhSys5nVHNgySUIVHwhJ1VS/CUgSeJSd+UGL3+da1ivIVoxIfLfysqtWh7Anw\noStV+3oIeGGGfAV4OpPh69nbf/JBGqrt012/8MB0ho66benyJKcyF7o6JSxr\nyVDdP1w0JkknL/lCamFIn2aXsxaP+Hk5ZiAU0Bj+g+g+gXiPnKnihIdG4RSP\n0XU8DHIDkx4dmm3xjzPIg271Ilv3/vdBOVT+98aUhqBcKljTGkuk+MYw59hD\nAECA\r\n=slMZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.8": {"name": "embla-carousel-react", "version": "1.2.8", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "99dc3d66a5135dcc68aafe2037f943e973487254", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.8.tgz", "fileCount": 5, "integrity": "sha512-Pd8xfvGBLJzl3DVbGEnTjtL7DV/rvpSwmXACh/WxoQGbl5Sr1chERN7nGiXhSZP2YdUA6Y2Pdrne2cgKC9z0jA==", "signatures": [{"sig": "MEUCIEEVbF7751uNLpQclD50STEwRFknqsnOdmfRKGD+dUhhAiEA7VSdV2KGK2sElrKubzpVobIOcBWd059OdY87kykKhsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeB33gCRA9TVsSAnZWagAAnX4P/A0z/VkkwP7EJkM+Thr5\n5F0VbEXzoRphJtKTJ72CES7Om30ZszHVppYJuxlgXkfaAGViR2BSPdgrdqmf\nrjfHwx8N3Fksd2Pb0Vb7udZ8k7qSNODPtnsmtZRNGaKESDSbXpQUVmgAbFNs\ne7K/jq/nQshjufaAepqtg2M2n+alS5nM0LIdA8/s1pGIZAbThjUKmN9qGlyN\ngx3yzGAsJtfdiGxA0OyWpikCujFEPBN96cT4KINmthZFvkGgzmrR+qDqILQ6\nzn/e/Rr+vCuhVMgOu3ET6WJP7tV0kOIYyZMZcZvnkrmFliL+9u9uHRMAdeSX\nAj5TEaPzAfRPWTDY6bUodvxjL324zhHH/qSK2Z/sKVMxvnOErg84N1quavy4\nW6DAfrk/LcVa9ogOzioOACn3n0h3cQRNPP51WZ8sx/lVnawCUz5Yif7DhMBy\nNak4KasLeym6cb0vu1jeR7Ks4T25vglAAC0mbz/fn2N4ETnKTKgew0VWXGOA\n4qkjciZ+ezBdU7UI7fbh3VHH4nOrScxUsSXPOPTOjredkedtLAeK2bYd05he\n9H/++G7kQRL3uc6RjQway18NBXZTEIy1loVd9hHZ28RH3nku9KCvll+zXANM\nwErM+O/xdxm+r/GdzrItSElcXQVTwHl9QsgInBTfxStn8kXzfXNmWDr09HKf\nhgw6\r\n=vzT1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.9": {"name": "embla-carousel-react", "version": "1.2.9", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.5"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "729bea649d1068d12368200ac13b0eb69253e06c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.9.tgz", "fileCount": 5, "integrity": "sha512-di5zWiVQoqogEGcGda3/Mw0NqjVzFB7agLoLAZvAfx6RK76uWMFyop1J1SLVhmMoV3y/670mrmkhSsjg26XM2g==", "signatures": [{"sig": "MEYCIQC1jXBh8g+iM3BaGQlSzvjPgTD5nzH/i28aji8jvmINDgIhALbdImHvNfbpMHZmPyfA7ASdLt74UY+J6ukTCaRPvpOh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeB34ICRA9TVsSAnZWagAADUsP/Ra9dRXi+UIrzIk+eg8B\nG/3yCJSCoW/S8NASl0DIo+6Lxq5qznl4Pqnu1liIyyiyYVH+KCljg/VhQ9XX\nV8sFTRbyr8G2Jgrqsv+JszSU68pGnQGG9oOb4qowwX7+wPGBaHC55v34sD1U\nLNpHU5E0HiO4yuaEXlPADwhtjrZISplF3lnSLBiXaJL9+6SEgIz/aOj++MIc\n+AzyN1MaJg5uO6d88PEHvQiKZlVESCzGmhxTssjdCat/ZmSuD4JN1YFoakVg\nwDiirCaACbMFsMJmUkr5gMToGZDdSnI0CcazKDrNmliCykEkoLZMsfSVXxcD\nPLuLM6VJs8NOx0ioXliJyIEJ+9UBt8uIZR8BsS6okyWLqQerY6vch2GkOlwY\n0t/u53KDVXDyObefVTbbT+rzJXopLDQe8ukj7HYkG4zeX59Sah33zzeZX0xJ\n9/NiYJYxQ3bE/g1O7DjAvJqNsSzz8yHL+rZLDx1EX+4PRS45Z2S7AV0ZUVpX\nYo+JkcWNBsBoDRS2Wk/Gyc0q+lzijuquAZChysGgzKWxByUwkBAoG4Uwi1dy\n7iA2lvNirnUu5XgMn3Y+lvMVSSnP1adgeD335s3pSV4Vy5I0H3+P4XD9xkrE\nYkswq7e9WgVSd38QvdIcjR1pOi1j2DNidp1ky2iJ3SVwTFsWA1N8oIzyId0R\nGpAr\r\n=V/qp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.10": {"name": "embla-carousel-react", "version": "1.2.10", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.6"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "dd34785e7deeb7e253eb6a4e45ce08cb1a40d201", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.10.tgz", "fileCount": 5, "integrity": "sha512-BkqZfmvCrsi7O5rlrHxHUPJ9NLVcLeNPTJFK1CZkfIXQWHPX2xDczYcwy/Yc755Gv7g0hV9y8nGD897DwwWGyA==", "signatures": [{"sig": "MEQCIEguf5AnxgHQ2nUsCE6ryEi3ikq1XsKJiJtz/hD3OK+UAiB+CHAUGwDHmVVPtmx0rNKjwlfJWfd3pPh/aAI8xAUCsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDeKaCRA9TVsSAnZWagAAJlcP/1e476lIkjUY3kRr8e/l\n42NwPli/8/l8oOFpahsPdiBtYEQrldOI78QPmj/AkW76UwsxAwxuvOTaD6Za\nQAQzd7Snptqmiob+YXsRkAg0l6dc45HsMNm18Jh02NCC7SCu9kVskXHqjjli\nVPXzZ4xESqQaUr18hFfnD7d4KylSlYfuq8tcqc7e+Ebr8c8kprn7XRHGArQ7\nmxYV0dFVO+NJnVvL8mgcxqIMEleumUkkEW+4i4Z9KJNQSJ5fwH4hWjLZKlc9\noSaCXtL0ANVUyf4hNGf7pY/QIGpwTUN1gRzBd2h2EGtXhhMTmpcyIt4uwA/j\nahEPwZ66+FS5wjhqqEPQ2r63LGPWEgwdtZvpv8frZWpPGT3R8hpEj3cz4y4v\njpol8VeRJmo1xnW2oPIJzIaPabzA37p3lyLGH8pp4dZnSPDN7BrjrE1w2MRR\nbI/CFNO9SSDiU74pUAmh3aI2fld7otgM8KPn7xOphg7hGzXjmByYufh5Hv5u\nJRtblicGD7QzKyT8tXQXKDnWoAfnAcRqakTx02BOhoxqKF3b8kuuSHxKbAjY\nbHCqHJVwyDBYzyO5RTt1D0z/PooP6yIw+nWNLUnw709+DylvZ4EwkBc9F4PK\ne3vlH/dI679X/mGMbAB+fbEoepwjOKVd0g7rES/gpNrSrCKxU8NiS+dIVHmK\nBfER\r\n=CrSN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.11": {"name": "embla-carousel-react", "version": "1.2.11", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.6"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "bfd5f4ee80fe0df5bfcd60394a8faebb0a5ba6bf", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.11.tgz", "fileCount": 5, "integrity": "sha512-P4<PERSON>ZZrGUxry7FzE0VV3nQhBdoFQ18PnxLajDqRdEdpGSAghlhZt70NS68xfAn1PYnM6Ze5cRV52f3fTGeETzg==", "signatures": [{"sig": "MEYCIQCqxH8jFQy6MLtnqVhR5BS0UNarg62RjyKbB76q+3S5XwIhALZFHCwrJxYRO7u77qR6HuQ/i/0tOz6J98lJ8tM2eFN/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJJiQCRA9TVsSAnZWagAAhIIP/1XYShd3VIB3xVdtnu2B\nk7jFP0CbHAv42WpFuMrWoWp9Ydi1Sqn0icc4iUEZGKSqSixbxd+S+rUBdeuK\nRzMTe7Ql7C42OX3SJ7h3+YtPSOrXeyY2lG9BzoqxRnTlLMjlnXuMGlbS1IB5\nCXSHj1qH0xcP/3uuSQ+Sk4EN9VaL7KlxoSGyjWl9bTuIPcasXcRhl04my7+g\nHCySLdb0Z4nK9NNjG7yyJG4t383TScwgE7TLg1KaXpc1nWzuRqV69Zckyeud\nLpXecIvhqi8Si3DVt23vZWFC0xfa6wuPiyrNW4JRPPYz99EeAwmHabCOF7aR\nS5rDmr2tQfM6c+Jo+cv5BhFMEHMxt84tWJbzSboquvf3W/AadT2wVKeSMAmt\nHXs20jjnY45LUKPxDXG5TJCtxT6m9tvIRCbLjExZDfLLf9JX1GcHnJZUH8cX\nIuSPLmFAHLwU9MP7I1oATQbqhDOu+p5Hfmme/+se+bZIDPX+kpLBkKb7CKuf\nBjuphp0HBG+SQMSyUpu1vjzvlny9/x1kKKASX3sF4+0iDbNCPQygqnYnRt/w\npELlGxKmoxWbUORXX2Z5skkgM8sgthLAAxtR7gYsbJc8Zuw0pJ2GK8SA2g8W\nDwAS6gNQ7exmGD8zzOEL5oSQEo+otcR0wf3zzwO+zaNi600CQ7cj664tHNN7\n3VGJ\r\n=QfFZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.12": {"name": "embla-carousel-react", "version": "1.2.12", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.6"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "780c26c1283878daf838bf036a251e46db51032c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.12.tgz", "fileCount": 5, "integrity": "sha512-PfX4zMHciTaXOjRhZuQEoX56z4I6fiiACxfcK+dRQy76rGqPsM46Qlmh5AgmTTp0DVKM+zJKB1CQ4a0YfktVDw==", "signatures": [{"sig": "MEUCIQDaUo6rxGcRuBbTevvzjOjxuh8HdPRqUqzXBp7Ot1yb7QIgP6+rF6eDsEVjwe92OJbR14Qzf6/Z3D2+dKLB6zAtRTI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJJsrCRA9TVsSAnZWagAAYu8P/3c2OO0yhU422rR90BJb\n5B7N6fjj3dnUhuOolynxlXOUMrjB4+TEHMg4pWJpTEQowgkT3DjfPj52iScR\n2X8K1zfJ9z3vYpOaPNXy2PR5WfPtu9cctDkSJq8thqae6UMPxKWDtCkJII9n\n//Z8w4nhxE/DwrtkcER5AJSkgJGb6l9VH+1EMeXcmt0Gb1p/yDq/5WBcwDq5\n2Qpt89hYGD+lglxXbYLPenC6/SQzu2oUwSiiaKD4vmBYzA514Smo6x7uO9xx\nYpjyXmphkALWYHOKFBIRJJEFgXMMkv81Z9Rba7uFMp/f4bBKIfparKXB4NUR\nfeZCnJLY646n07yYAhYjUP7XM8MTxjQUX3aMRhVAHf85A4m5n5tYIfvxa66U\n9Esz+AGScze2LN0Ig3KY99k4zL/g3pd2UYqXQv9QNzMZ7PDGSZkpGJbvYj7Z\nRfiHpGGRxeqNlqguSNC1nZP+RKKKGU56EKNFyEXFowDYmHtrIxgxdqwGXaev\nSIOYNf+oBa+KHqNqsRxJscD/WUiA9EWrbr4tUrWq7q4S42XsLfRbjfytOdPj\nQKS1A2Cu/abLwMaSQeJlJuZ7THs2ZGwpdy20lUH/m5WIwIbaTuHY41OCGAW3\nwLhX19AMO9OI/Wt975QKS9Ecfccccvr0FyiY/aZ2/9mRVelh+LiWtvXfl0ER\nO0NY\r\n=iLXa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.13": {"name": "embla-carousel-react", "version": "1.2.13", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.6"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "d05d0b46fc81675f2668315965eea38f01dc6b0d", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.13.tgz", "fileCount": 5, "integrity": "sha512-ezrznsSEaZ0vUHisjNczPAumZwppOu4WaqAH5fUgfmmhLJh8HbyNrsP0EcxInwmttKOz1CpQ+/f+HXvEAhJbVQ==", "signatures": [{"sig": "MEQCIAqNOtIcGKDki7fX7RJhMrMWvHiTnBfRSGROfsQTA50jAiAY1HACV4WXhLgAQrdlLwvNJ72yhz3naTXmM1Qia2DqaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJJxCCRA9TVsSAnZWagAAtHMQAJDkFOOUUHlFmP9MZDdw\nTqGHgoAjUDieVdE4E48h0sj2CPuhzBXX7C59a2f2/G3d3HYQhBpACWOZbRzT\n7MFVJ2yVG6L9+5aT7wEoqEVO+fxgS7M7kCS4Aw/6YwCQ0mq/fHVu4An+Spzo\nsAb0QQwb9uWRpkfGjDesEVRWaWNjIYB7ZI1CFEafaQ+pQ5yb7xegK5bMVP/d\nV0+GW5zDLdoQr9n3Po7OiSeAzGHeY0RiRcLYDDtVKelndl6A9mwjOn6k+WMN\nD3v8qLwWMQ9LkF8Tdto2dhQKXjmlWFz8Z5d6mAZwrywMYrBmzWSSWO8C2ZtJ\n53f/9N7oJotDVuMFOnZdkMBUbp4YZOLplXti+nEnR65k1ZCpu/tQEN18CTwb\nbljs+GsN9c/jmfx8G9a297aHYUgqNgOopN/Kw4TjKujrwf8xM3ZABLs7noas\nxzDKwpCwV1ONWw874El3s4+ajPLtw8NWv0DiQcHKf4P9qSpArGKnSYmVjNYE\n5nVSQP+nULXri7pXWSIYwKRPH9J1s7YfJqNAEqF3GQ24+4ZKU75RFgg2BxEX\nYpL+y76b9+1ROa2EVBCJJlIuO5HNvek7CV8q5sUUDrf+orle8k5s40GJK70Q\nZKDK3MnoC/X+jxo9jfqwTQHRj4Omc/rt3ObXhXwnu3RSrvelThWqp2oiCEJK\n50jt\r\n=y4hA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.14": {"name": "embla-carousel-react", "version": "1.2.14", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.6.12"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "90a5e8da4aeae219535467cd10f045ac509a28d0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.14.tgz", "fileCount": 5, "integrity": "sha512-KqqzOY8ur7Vj1+uU3eRz66ouD/yfSdPnOOY/BNjwpM2hKsGpga/PK7Y+il/3wWxma84nNjS1hRuDe/h34TNnhA==", "signatures": [{"sig": "MEUCICeyRYV04qEuGqXKWifwKnnDTAkRZ6w39gWxkOXdNFlFAiEA6vQ51pIlzYY4DHQQ1o9mXhkyywhvw7wVw9Jt9ivwmA4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18288, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJX3fCRA9TVsSAnZWagAAsIkP/iDdHmn+FQpS6Q9CXOEe\nj/tnJYzTZ6WD0CHBiR/S7FQYeMgtu+RmfTyRxnFiWEjOQQH7wDLWrCucTKZA\nW+nFcHOVe/6dSz+PprcFfriDE7s3iC652CR2S2rCbrrTjXEgpYo1/ADXPg/S\n7jegdOFlgLGlYFJp5uduGnIsVm693bGEjWFhuZiNzYBBNHfygYhuldEnsOqI\nmYRsKbBCVk6JFKs2PdhTGHzvKrlreHn20CwdvQqAyamOknbmHoTj/yb8v/Yc\nreHdG6pRevP/Y7Ng77kTh+KIEfLLgV1OXUmBoh9taznzNsoFTXW1RfOFxLEa\nye0Pfht/g+x5rx3Dm41Pv7ERgAfTjWXXXH2uD00VNHQ7oyDc6cHULawL40AL\ntT7j1mID8Yd4t4PuIS9lcZtQvTQumP9CXF6A0vy1C/5M6LDghhxCHV2teHEr\nt7mdLHHjJmH/cLKPilXGEdbarvzbSuPNcVYWklkMw1BCMc6xOsh8Emj6ko57\nLHYGAu8U1109MJl+/bebFg1LorCYWRYd/juvu4ICVLDxfHwkFOIACjPgd2UM\nJg8gYX4z8CFVbxfSsNanDT6O7e6XB47CB0VQQIZAPo59SuRzCVfc6/p8xsq6\ng0BWTTiuCA9WCp9fqO3o0oN1kldUVZAaUPcfEn1wHuJS9Lhj+l4QHB7+wo9O\nANwd\r\n=+SoN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.15": {"name": "embla-carousel-react", "version": "1.2.15", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.7.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "0dc10a289ac0bc8a547b1fb406b4ff5acf2f39ee", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.15.tgz", "fileCount": 5, "integrity": "sha512-OFhQhHVN8Wcah+o8uPRuj6vuVET0Hy9cPo01OTP+ANlPbbQANXmYq1btnehKHiMUXkdZB+VLkEVKscEMydACjg==", "signatures": [{"sig": "MEQCICfEe06GsK5mFgGAOA/pE+DI0+n4XJz49XY+V1eI2MaCAiA8mVMhyjrv2Yu6Grb0QW3AStP4gAp79S06o8Jy5NX5eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNB9bCRA9TVsSAnZWagAAT0sP/25VEagk3MkjY8Rf8LMn\nbKh4uD7LzgDJ8LUXLy13dpgPru9piIW9Og2Jh/7ol8LPEGsHy8/l9QQudLMi\nrpLvEDiDJmjwJi/cuP8DmxglOzdWDOKUAit6fCPGuGnZzZ0p7/sbWRtg3Ho/\nW5EjOuDtlDJ56z/buTvNhfq5606olQu6rLpLZjdDUXR1SZadQFLGELlCLtcZ\nMw6Lg1aojw+3DtB1wlnzcx+ux+aszN5Ijw5CbmW/QUV6brsA2zSMt09EmWec\nSe3cyjmsGSzJoOqjiGbg7JNld0Omhd0Bhpzc7QjFxfQbjfpb9xRIQ8g6lc0C\nXIVq6tXEJO7abRJOI1iDg0nYvq5jBSLbw4XZvYVT/+nQ6MfOvrftEedqGCtB\nBQFTIDVfKDKnd6Xx9u2/CQFf9iWy/cXNIfTcJtPjF15aendN2Y4sOgnzW2Rd\nmZ9UG/M157vZnbtUSk43gQ4M9QzSzJTTNqfSXlQxyrPwJaM+4yhcbNvFNzhn\nxfXQvSGEofZw5nJxdUjbn9vTx5N8QoT+ZD41FG/HMMvgBG+N3vY46qOkwLY7\nGRDhVnq8rxXWztUJFZEqTqHHfu0byyIPQZ7xh0voyWfhTqepyjcP4qvmA1WJ\nZ0J+qDrn2jLt6BierITXHn9PxeEOY8yY0xJbf/2vLftw6omLrJ3yQytI4SaZ\nxRlC\r\n=UjZV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.16": {"name": "embla-carousel-react", "version": "1.2.16", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.7.3"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "46e3cb45de97128e84f84d569b17541bca67974f", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.16.tgz", "fileCount": 5, "integrity": "sha512-Q2ph3k1EhAhBoW4L5GslFbzLQlKeAzIikx7bSp33TJgUBBB8rJ7xUOASMsIHUo3FYvq6QpgrOIwkhhb2TwmNvg==", "signatures": [{"sig": "MEUCIQDJSj7wxzUJDHcUeeIxdmvmYVE1xYBn/11HKsEZGXKXRQIgO4n+UJrB6dbquuIgE7cznztGDQ/K1pJvtPOkjlHgFxw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeNVXxCRA9TVsSAnZWagAApIAP/i5xZw/pBm/wUYjY5kqv\n0tVQVqYyUaeCGFSPIgaEmCDAYddty+HH7uTkGIhHv2bPMMhIXla7RmCDqMSJ\nCXW+PRv6aT2MehQO01T4nyBexXBI6buesYN3BrMedSMzcfdJdtqrnEQ1VX4O\n13vS0pLPYl+BLcaDAQCLOV9Twy6jBhBuYgCs0ENV2a86qB8TrjFLiLbBBTFE\nYx+dUtQnROnxsOqZza9xSQTk/2BBTbHkhHnz56nUKFOIiq2T+D6PLWnahvY+\n4Mk6paHZdIPYQowz6bESGyK8z9TN34orgWNEbJPXn3Xew6d0VgBRwFHxty4V\nzarLJV4/Zt5QKsdX6V0zdDmxNcHAIKri9usoXEYn8asG+woHXv0Dnob7W7Wa\nLEAVbThTS+J18moQ5QRr3dUw2QFfKkLjC9YHg5QnUbPg1t6W2fLzysbnAUiB\niK7P3DDVVNE+KgVHIjZtCBDH2bNTysz4m3OeCZoe2F/uguVeHReZmfTG5U19\nCnQv9TDeQpw0UQMGTFUH22kdSaUmfkhzo2DOYJt4kOhnJVTJNXnzZVt8p5Ez\nJr7k4BtgL6JQx7Az/1RmThikMLsSDl1w/4qmVySiBp/pcK4ZhwjNPge9cCXt\nDoXByniI/RvA4LGWGpmzsAEgwY7iu+SN9ndS+YfdU9t6BBQ8Y7eSpAzdYlCn\njeVd\r\n=gDu8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.17": {"name": "embla-carousel-react", "version": "1.2.17", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.7.4"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ddedfd13519be4ce413144ddcbef3da1089eea8d", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.17.tgz", "fileCount": 5, "integrity": "sha512-TOnmUTE9nBksDjroJDQzz2txFj3sF2yqVZIVVxBGy7u/bYFIlz2fqhWxsoP8QOw4WM21F9zqG+IwhzRa1u/Ssw==", "signatures": [{"sig": "MEYCIQCUQiFBFliZWkJ5Cx5zABuQqsgqy42I517Ei3OO0wF1lAIhAJc7teyrZCSfvxSEdNlfJU3ad/h77dHm3KcIP2glTSl+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeRAOZCRA9TVsSAnZWagAA6kUQAJV3Dtak3Ra65XpaN1Ne\nQaXu+UPRLL+7ODI1KyrZvJGDsKhY6EAGdmB4KC20G8CsqTS2HLzOkz4yAsQE\ndo3xurS9YuHS+102BN4Yrhjra1F8JUA+TXMEjI7/m3TuGJ6ELKmGoUOrlbun\n3lBCYcOa2vhhnpWZd8nx6lom3yqrNux33LH7ydJJ+eijkYsy5VenOFaejYUt\n95DtX91/9ptysOKTAs1rQRgvmo2QQUVAl+5yv3sxdqJGM7PAuZIEzPB8IQkP\nmhwn22XIx5DULxAKdB7w8TdwStQ1Kz8e9ogtq4UzKekjCc4y9HbzcH4Ul487\nnuVLnPDhl9kXG9SN5gm7fyqQkSjMdos8/THsGuFEGsWUe3tC4GzWRFiKVwFF\nTwrJCeeNALC04WVnWS5GJ7EY965bknEmiyp+n7XfHQtwdY+pQwljDzQDQPn8\nK2ByJnGyp+xAFC0o8/tueT1qMG+RCcEHTt/PABPA8ZuhOovRFS8jSL86iu++\nBr9OoPRSa4IY6RbRV02b5htMR9NgLHHT3JXj3bLKi7zpI7eaz8HRg3PN7HOS\nKHP+bSn/PVmfkrLuBtDfL7VbPvxcUruufM7xeYQHD7le6wjAGjiNSlGUNl1B\nKCPuhbQcpqKn7gr9oy82ibUBHf/IGLOwDR09l6YDLSigz/lu/Z/CSaZSE7yz\n08rg\r\n=nYdk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.18": {"name": "embla-carousel-react", "version": "1.2.18", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.8.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "ffcccf38cbd6157b3565cd8c42eb2c1f7a6bfd99", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.18.tgz", "fileCount": 5, "integrity": "sha512-WMjbQU/6FHecqVWU8+0VKxDKzGereAYXjhNM7d95q71iZqj+fXoz0WeRGjWTHAewbmg1WD4Rkd5wIAyCeMbqHg==", "signatures": [{"sig": "MEQCIDo3pzE61hZV4Xwy9Wj58Sk/DKdiv8LsrBmFDhlHjnL7AiBbcAGm8bziY8kZbO6BwkN9YSwiOUn7Y2ONwSot4nm7Zw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVjh/CRA9TVsSAnZWagAAhpkP/RX8GZ9TM7bImQdY9MUK\nCQBBttTRE5gjMkj2zb57izmerjsi65o43RcnccZpDuM1pBLWevYakL48GFcO\ny0N6w1SOjBgkbpitpDlHjiiS8KTT8LCqkRhmQxE/8ueflViTHfqZjN4tiazW\nlhCPx1PEf6qMQN57yjVsWK0ZtZXR1hHICYDqr/Ib+Ds8yjn6bB6aMgLck3iW\nOnX2PZR5oOaNPqtNJ6xfy2k104Azudzyx4RS5aONQf3LUcdx94co+hpfHmgD\nCiRhQP4gF7hXm5QnwF5o2oTPPy/wBmUU/KVWPsjzyUnzXhMFUDpZ+9EP2V7L\nswjSxQNldOCQa8yQ2++mbusaPIxtq+bCX38WmkWTrMFyoFRsJ3XFaRBQt+r6\nYdbfSFas153Ap5a+kRalskKe3NDxyrPhR9p6JuwbbTTapESUnJrO/nIwOYRw\nuDgJHcqi2Q5xii/LVuCtm0fcQfcDZQ3KBJiZlC98hyVXIt2UXphSuVgELepp\nVQ8r1xNuTcLFOllC+mLRyv3Ig59n/LvdG7TBGE8G124XYpTqLot8QasbIqnC\nQMmyFcC9tAiG3Cw8SVn7h78mS6jBHX8j1U2yzhzeDQtfj+g17f43hPi4crWN\njqTg8aTydcyw2EMHHcMMYF48UvOYxiU5X9KfaF1hxLQMfp96FUx6OdAF8JYL\nInVq\r\n=5PQx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.19": {"name": "embla-carousel-react", "version": "1.2.19", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.8.1"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "0ac0fc544a79ab27f34a160c07e35d9d099616ff", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.19.tgz", "fileCount": 5, "integrity": "sha512-7ZfbD/Eevssx9cRxUxhQBCLLBNlILwf05PLTwh9hLbBsidVC48qaICK6xEqClAfAhTACneRxMD1gazF38tEZsg==", "signatures": [{"sig": "MEYCIQCa+ni9BU5mIPOgQmfRH2Llv94pkaUdg1tc6M1XDSNSewIhAJx0BPenCS1a3emD104ks13uQUdLraYiovUvQC4Rz+Om", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJebfdtCRA9TVsSAnZWagAAl4QP/2uNnhY+gHAG4BHTApo9\n86LNlZ+Y8FUCvD51cObtTZBiiZga7OsFXMLcI6JWDxNZ9BX3CaO8+q9MBcXX\n1zGgeiyBkDL/lEP8SOhRYDjWeDLv3t4OFe9b1mM/5GDwjhQCXt7ZmD0WeutG\n4/9D2N8aCi5B7o3d98Zx/DiDbZXMW2Aq8zRMw+lKui2G2egR2DffXbgmLD3w\npcetkQ6Pq33Ju0BH0Mzy3kZWcTnv05wrZqyXTJaDW1KQmxqanT5+AyXA5QWg\nJQidlMmdOR6POljux51V82IUQ1R/NCIYc6TV2hbnD/jTuDPzWwv9S1kJdfdf\nWB4+3bil9AKqOnNG55VYY/PUalFKFqcv6CTIUE1sByMPGS5A6/eCUTKFupPp\nGnwUTRsyd2m7O4gzvfDVIswj898GOlAsnz3IorovXPc9LmQ7R8+SYangZrb9\n8KawYVNOmvOOqcap85J10EJP4lu6mox7SzrlqDKcAtjsl7yCMPYnODz65+vo\nRfrBt8N0utF3G+zcBJ+wtw0lTt3w9NKGy2Nc+qzrcFA86f3urZwY2g4j4B/9\nLd2+BHBPen6aB6RNicmsd9wuLpWPzew2XQ5ZaJ9aX/CUSz4oxoC4EXV9+pfD\n5Mzdz3JEbaWvozJo1KWCnIe2B+H1T0zLn8ldjO7ByGFQg0GaxcMLMoe5pgI5\n6cDL\r\n=/T8h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.20": {"name": "embla-carousel-react", "version": "1.2.20", "dependencies": {"react": "^16.8.6", "react-dom": "^16.8.6", "embla-carousel": "^2.9.0"}, "devDependencies": {"jest": "^24.8.0", "enzyme": "^3.10.0", "tslint": "^5.18.0", "ts-jest": "^24.0.2", "webpack": "^4.35.3", "prettier": "^1.18.2", "typescript": "^3.5.3", "@babel/core": "^7.5.4", "@types/jest": "^24.0.15", "webpack-cli": "^3.3.6", "@types/react": "^16.8.23", "babel-loader": "^8.0.6", "@types/enzyme": "^3.10.2", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.5.4", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.14.0", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.5"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "3452020aeb72f84f4cfa36c5958357142636bc7b", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.2.20.tgz", "fileCount": 5, "integrity": "sha512-xcSQzYlMObhNV7rmDsGC28p8rY6cppTYgfMZJY1v3vDv09cPk3NAbK+tJUCHsW4lf8dnBtM5QbENJfOEnJsRIQ==", "signatures": [{"sig": "MEYCIQDlKbzD/yC9mxJqE8WBqYFSHIpBMTkTmBq3c8+N2G7XigIhAOMiGCMobvxi0nJFRJOmec+a5mVp4hUipZUwHIk/tF8A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefbInCRA9TVsSAnZWagAA3bkP/iMABpMw7wULOlFX1RJG\n6cME2HZ4KPLNK7DgbXvxOGYq1x8C7vLxKh2LlrhGpotQT/ZQPzc5fBmtmT4o\nPqzGFKJvc2I77erybZDS53s9dZ4gk3+l6QaDXKvHc2s4lPx+0wcgwnt62b/p\n4uNBp+LTntgY1RxC69IczZuWaa07c9r5dU9QiJr7lYXcxsV+yTPzjiQstll8\n2tPUDS6/pOiiak86KiocH7DVTcwDg482XFzJqRCK8vb6NYlN//1A6gg/OdD/\n5YzzJ4huqavOu9YZMoxeZxME96Be561FeRaYGEWMnyELwIZ9xBVl7Y4DugZR\ne/g2ksG1aEkbsVZ/+NGpuD6yP7/4XBqLH5RpT3RgnyR6eBArDD8uvgIkZO9h\nDYbH1qZHhKi2LyM01gFpdyNVfjB62iaLnOYzbPkH0n1a3YvzuTb1ut8TFgZC\n9dATHoGrUDpc/NM/Ot3dQEMsKJMSX09Oeo2RQAKOeCsJCw+Jl3DqNnHSlntC\neN/TcoaBBZiDBVrjfrotjQ2UplJE/J6hmkJJ7PBr897ZUwImfUTJJaUTZpGr\ntjLQHnnHSI6b6IKBSkynGXSkyLC1yaq3fUN8BsmlV7gV/6ls8yPovT2ibymT\nXCisyJ9CKorImceiknu8TqA4a9755BiG09s9TQXVacWE/1LWz/PmevK9l6xn\nybWm\r\n=guqt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "embla-carousel-react", "version": "1.3.0", "dependencies": {"react": "^16.13.1", "react-dom": "^16.13.1", "embla-carousel": "^2.9.0"}, "devDependencies": {"jest": "^24.9.0", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^24.3.0", "webpack": "^4.42.1", "prettier": "^1.19.1", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.32", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "7fcfd0c1f9996ad2bbf8d6032fb814a7af2a5910", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.3.0.tgz", "fileCount": 8, "integrity": "sha512-GhKcaliOuIU8kNlHNfYA10uTcNcm6e+nlwLAe4k7u99qBJEofFoQXfcJqsA1598GK0p3HbaPbfknBBKs3pFyZQ==", "signatures": [{"sig": "MEYCIQCl4yckwz5chGN2DfVv1P3nWuxSe5h5Ea8tEc/cPa6eoQIhANwJAyRioR/OROER6sDKPybiy0Z1BlHoKEp/OKYYqLsA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJemV2mCRA9TVsSAnZWagAAaMYP/261uNorT3pC2e5/XSos\n+hWgjKrXihddVXkTuY8CG8NW5bqGG3EuO1P/MrkKguiQkAq4eF+6OtrY4bUw\nn1vQS6Y/OFgbpcK+0dDUPAwsmr5xPJwj1DnyV+3nE8DHl/5sRNHCW4a3fiLE\n7XAlLotkFWllmmIo3cwF2Wd7Z/acLtdhhB3Tgzt+Z/Gw3dRm3Vo6k6VhRxI0\nUj5+sTPgUPiaIA65orBWSX3JrSYI0SM4IDAh4vlG6yhs3XBirrkeoYvLNtHP\nIK1uq5E7YRqSKD2iURM/PRBzcTtfEDdRruxoLMVbY1/u4klmZyJdlENAI0M9\nCo87al9zlrQzAwNQOtGqlHOuUdNdNh2KS9AV3YaXaBb0DvzPxpfh9u9REbR8\npTGGaLUrt/SolRduqg0bTSpgiK0yzchuGzo5vdUsPFbX6sm3WhPcpyqZSURF\n2/OyQtYQEtJsvC/e25YN0n604vfIWOU5ZSNLRybE6eVeL+gmrcaKjDVo9QwZ\nEbFY6mKzh+kzHf0EUmaRv+gw5NZjGsC+rLZz/4QO0UPICetdS4En3MpTZqci\nqDHPspozlM02uhCRpxn+B3qsdva2g61dZxOOhiZAJQdRuXHs7CdVf6fFxdlD\nqpq5zenRarL3T7QY7Ss1Phhe/JzPn83L7KOT6wwd24P090/g5ydE9F3T8Ju7\n6bm/\r\n=rJ/Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "embla-carousel-react", "version": "1.3.1", "dependencies": {"react": "^16.13.1", "react-dom": "^16.13.1", "embla-carousel": "^2.9.1"}, "devDependencies": {"jest": "^24.9.0", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^24.3.0", "webpack": "^4.42.1", "prettier": "^1.19.1", "typescript": "^3.8.3", "@babel/core": "^7.9.0", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.32", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.0", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.8.6", "react-dom": "^16.8.6"}, "dist": {"shasum": "f6e77853ba0cf9aa998546c06149c5436f9f0580", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.3.1.tgz", "fileCount": 8, "integrity": "sha512-eAcqhP0QqUX7J1h7LHBnTzu83R7qf4MFS3FVkHv99TFYHZVtTzT14w8ezivZ7CcoamhSXsmkQX9hwALG/9B+nA==", "signatures": [{"sig": "MEUCIQCVRndG92dYXDRKMW9QIkRci9at0OFUX/dLSOOTU6WIWwIgdOPBOI50oAWsH72AB4igWW9J3otJoyL7cClxKaTqMks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJensZDCRA9TVsSAnZWagAAlawP/A+90KGhk+k+XDtvlb+Q\nY+kcZJEf11jsbyXvlhRgOU54eldncccwkHJob7bu9Gu5TEwXMABMA0USrfOH\nusxN4wq5ypIHfLig3zpWCDiJ3S4zxUSB1Htv78JBNs4m0B7nh976cxaWRgSW\nuVbhBTQJCG6W+0U94XSiGmGW/Rhdn2FKJumx7TIj2RloBCXwLtRr24kZNGMy\nooUSk5+g6oGnnQhGLKbGNPiWIVgXHs+fHVK+we0mRvd3vZGTxH6FxaavCQ+H\nzbnrO2cdMpsAT9zUMnHNvXLRwLuEHDMuFSgYkHbWmjeF/jbq3yiHX8tSM7jo\n76K76cZwVwWvU07EGghsitRkpRYp9AwT9vsTZmokX9Qo/jWPHd6BKJv/ig+c\nBOoWEabw12OBquuKDnMaX0ENopJ08R8GFyo4z3AVa3c2w2nS5QpTCbaowUoA\n6UaeYVpUymJyJBVi3Bjtg0X5GfNVo/qYQ8+4qHnl5+NphdLA0hyIDzebLcXE\nUiBKt8aQuxfOpXAVhkLSC/HedY3myUDsEu2zNNVKcwFMA54so++ffVwAFy5z\nuibOPeRecJRgSQ7OrR03rLCUdUtJnjruofAyR/grbOidyj+EV0d4Igr3ZM6V\nOjH5Zkyiui8a2BuBROZXGq8YFz5Kg1RZIq3q2LoQHjlq7UDtHDXT8Qt3ckne\nAptj\r\n=aXSp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "embla-carousel-react", "version": "1.3.2", "dependencies": {"embla-carousel": "^2.9.1"}, "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^25.4.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.34", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "dist": {"shasum": "fc4903fc37e73392d397691a9ceea1eca5f8f373", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.3.2.tgz", "fileCount": 8, "integrity": "sha512-XNslDSooKsRGlVh/J/58Zq0P/vosGz+ZyZeZP/UiTlm75lCm9iVnxeGdVw7rq/sMyD8GQoqz5PTVfT4fNBrczw==", "signatures": [{"sig": "MEUCIEX+oRuXNQ+MI3/5GoWvKlt0SGhTm+LIZpAz7480d43pAiEA+iD06+Qx42HNWtAbpJaeMSWu0Nl+NBF4bQTBxB2mTe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24674, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeslu+CRA9TVsSAnZWagAAYe4P/jv6tKqP7Smcoiz6ei7I\n20LooHGHTlAEFWiPwTGQ9W3YRhbuoI0FZq3+6Mw5MVoPWZ2jqU5udgih8aJK\nmqVdKFwv1vFO4Glmd0EsVPPzDDjYOuv4FTjWLU116qVb/S/OQwvp7C9jt4CX\ncIpJe6e1tstHGIymQMSZ/HtlT2i8LDMc6bobaP2Dy08AECxJe4W9eY/iWw2y\nX4jvfPHJRhwFOZqS+8+DIGomsfj1kva2J9ucxmLdjL8bTP9OvLfraCOamjQY\ny8OgRRZQN8gHUAUcaFgv5PEGy2va70zncYhNwyOXtUyi7+WxPv6QtWGrxad5\n4ghY9hg09HHjv8tcClF4f09XkLlpLDFnCs6KE8yGSRQHz9Jw/OKb0fXZw9tZ\nhSTFCEsgCADKIxSS9DXrx8zOwz5tMlht2Wavbu3PNaOMxH/gyfwr1xbgdxQU\nQ1azrKVC7IRtsDD7wYcmv1e12hFAzMtWWPAKe84s9/wa4D3nBFCUGOivU4sd\naQWYbn57ygTBhwHLibR7NM4ThOUQumZxMDUEGP64HQcVa7X1YZVjTL4dOc81\n1TT+5yuMUIMJTnqLtI7AWonBtbD4FEdd0fxqyc/Urkgt32pf3GWexiU/FdX6\ncGsISBv8tIe7sbavS55BbN8k9wUn2l8toP3N+xBBXofaFeNRE03igfhDseOi\nYz7J\r\n=Cx9W\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.3": {"name": "embla-carousel-react", "version": "1.3.3", "dependencies": {"embla-carousel": "^2.9.1"}, "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^25.4.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.34", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "dist": {"shasum": "44d0eed1f7239c80ed81cf891a9397c838174655", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-1.3.3.tgz", "fileCount": 8, "integrity": "sha512-4kKnPiTua0u5jnXL9tLcZ27t7bkFAhTq6t24gbrwqG48xZwNn8NpsFeFPGWXDmjMMV7F8wBZM1dquqsuRXnM9g==", "signatures": [{"sig": "MEUCIQCwJRCdS8Ttwp9zwIY78l9uGjVVO3j1oE4E/x+ASr2AzgIgcP7Pr1yXF/4SCVe415M5kwymHjYSoavUZocOjOrFSzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeum7VCRA9TVsSAnZWagAA3K8P/A7Q2yLmYMdk7H/yU7JP\n2B4zNNdX3eFuITwJFujKjuTFttZNWXwn5H0TPnl3vhroNmeiJrd3SH2LoX2x\nQcLF0ekOYzGcDYy1tDSCrAJ0cAeWfN3inHSXgV8xGGreTEQdfyREoRXBSabu\nIoA0Niz9nx8OiDHT076spa/I1GDEVuZ5kB+CcB8VhkFizb8SIPCreGAxH66Q\n0WMewvgcxqVsbiJ65AaPJo3u3KX7N/NVd5V84gQR/KSqULAtQI76tECH/atg\n7NrfippHwuhXtu3GMGE6E6gVwXB5Hm6TfNzpEormq5IuVkRLtkg509Ub1jPA\naFdKtm+TR2RJDW/+J/RSYvHy6R0WIZ9HIvoMM+60SCfXMaldegC0rNvMMZ7+\nRFZkZDJZPJS+uDfuZGU2GwYvpuqTBmQ6ofQxvJ/7oB0UNrbIrUSR44rCEP5Z\n4kGdKqb2PjFAZgbNf+vRIOo1i8VKFzutyWE5T/Ea2WgXQLgJSZodlQKzSTK/\nz1EBCg3XI3LGnkF9JsY7XmR5UmavCTNL039EFzn1UDg9hnD5KZdN0so7/wdl\nhn9PzhkO+oAOJpQX52uQp2hrEcbpfebVNN6V5WeVY6/IPYgahX4oSWWWTPzS\nFLdTv021d8AdnYUKrhz8XfvGnHpJUH7VxFii97PV/nyweX/EzFqfinPtIzt2\nQ1iN\r\n=1a6E\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "embla-carousel-react", "version": "2.0.1", "dependencies": {"embla-carousel": "^3.0.0"}, "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^25.4.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.34", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "dist": {"shasum": "32823bd788d73a9027b4013f3a37484906118ef4", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-m942GlvaP3kGtXSgbvimM1cV3+OJTr0/ynsDDY6EMkMjZBJyiipRA13zClR2hRlNCXb3M4SSjMqPHVatM5vHcg==", "signatures": [{"sig": "MEQCICKAiRg6+2UiR7IDbM9xhzUePz/nq2+5P3IDEdXAcAVMAiBghZtaiEeH4lTUAbImGp5tBVTGWLxPjc5zqQVLoCJbdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe451ZCRA9TVsSAnZWagAA3mAP/0JQ/4H/rZ+AnpA/ESQa\nQxX1S6Wzj3iihMhCNucXpvD2ALvVXwII6bdBsKBik+8mIObcQELLgFcFoD2P\ni1ydIxA8BIhEhmE84OX3RhJIPJc4/qYeDGgxxZgrQCAXvn8dLe3TDvBGk4V0\ncTC2MibypPmz3BxJwC7XMnw0IYL7E42O3/CGQKsV1uP3kW1gcvWvpyINlerJ\nhkQ8OquINNc24kVdUnk9goSoVPUCQ50Vdx3Lw/DYOPVcTVAEBQSHWhP1a3LC\nH9/b843rg4nUEpn32+u6CWfc38HcZZ+O4iTQMnLFQiZBC3JVL+fl7NkZsHrt\nNcLebkDErfKEVFe3krWon1Ti/3hbwK5rV2j+qW7iNU7MXZovUgeP4VBgThni\ntbgVsSbzfzJ+xnicQeg5IzH+OlhxerfP0/aW9p8YJ92CET4+NCvsiRa1NxpF\nrDH1lznVCUg2m17wCJZ8DqbDbIwewAK0Vz6SGHwj31wGnxZqQdVDiLEGd8UP\nvHYRwVVHlNMByjM/DQFA3NwxtkaCzBkSzyzxCA6trvPq6F8dW3kaWMEOx2Dn\ndrupJh+oJKKpZQttT82EjLnLhx5KsVoJg6q1MwbBhJqmYS1ZRD/bXpadZRwI\ngmyi598/QCboM7RQktFGt/Ci0xloa1mZ7Z19F/vfpgIRHZ/UsOWk8D2CGKOF\nWET8\r\n=18Og\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "embla-carousel-react", "version": "2.0.2", "dependencies": {"embla-carousel": "^3.0.1"}, "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^25.4.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.34", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "dist": {"shasum": "74b15f23e6b5fa15453d17ec7926fd6154d37797", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-2.0.2.tgz", "fileCount": 8, "integrity": "sha512-Mq46Yt9v2jUF0SgFta6TSG3d3UTZMS2OftC1JTMW9/vBx/Sbz1OGO/WcMPNW5mu//8tVtbSHEKjhEp9YgyYuzw==", "signatures": [{"sig": "MEUCIAZC/VnKjarGF3d/gTUmK3m9Lx2GwV3iG4N9JbciDbFzAiEAzIB1KIvjDuSE2m01e7aYIhEYoa99MB4DKS6tdmGDYdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6e6vCRA9TVsSAnZWagAA470QAJLPVuoMFxpDscErhud7\ntEF2+rbx/CAgZacItO3G3LSt70PisyQ7QQpr2kpPLgIlobxMrn5BvqJ/JaO9\ntANyXssEmvhfIP5BPUHoelCzQ3JC5WGzk/ftkRhSnR27/eYJO4gArnwuskyF\n/vpb6g4gsr56qRzO/9A3GsYSLPC/oNLi17yczIhOCw/yq0Z8HelDPAlCKGAh\ndn16tVTpo5q7FB3qQJ4zDRAJUopUW7aqHvsJU/T41uCBZSOlpMQVqTvc9RY/\ntk8pF0KHCtYsotBsnPmaAh/OsuEkRJf8P5KMmMXHc+rZ/rRVBVlhfSM4lEpJ\nYjMDsJExklB3YEXAXZCogzSYvaZkBcFR6MqukhzvuGbPkLfCJmx00jkAWkli\nLAO4SOLXtGodCJQS3LjmDgreBVDZ7O0EwIJbnc0i7IeakzEpNWi90mU2dfwK\nRm/lt/i2SkGFE4KzB1LX/Cmn1zyHwCCaIrq+B8STPB0gjvlbM8Nhvi75qbTs\nqd/thwa4mUrTUvfSEMdTknoCql/7BddR9+8jgzdRpT3CBKNbd11jsJaqhcKC\n5OQu/UpyC9kDgnoOQjRCA1aKj75avOQfjVWEZurdLs0CrkKongjYPGwG6LTc\nGYgS28uZNJ90NPbIc/oF5VtV7A3ukJA70PXBLbFVIPZMyjGt2W7g8spgVVB0\n+gmy\r\n=D+4f\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "embla-carousel-react", "version": "2.0.3", "dependencies": {"embla-carousel": "^3.0.2"}, "devDependencies": {"jest": "^24.9.0", "react": "^16.13.1", "enzyme": "^3.11.0", "tslint": "^5.20.1", "ts-jest": "^25.4.0", "webpack": "^4.43.0", "prettier": "^1.19.1", "react-dom": "^16.13.1", "typescript": "^3.8.3", "@babel/core": "^7.9.6", "@types/jest": "^24.9.1", "webpack-cli": "^3.3.11", "@types/react": "^16.9.34", "babel-loader": "^8.1.0", "@types/enzyme": "^3.10.5", "@types/jest-diff": "^20.0.0", "@babel/preset-env": "^7.9.6", "clean-webpack-plugin": "^1.0.1", "tslint-config-prettier": "^1.18.0", "enzyme-adapter-react-16": "^1.15.2", "awesome-typescript-loader": "^5.2.1", "@types/enzyme-adapter-react-16": "^1.0.6"}, "peerDependencies": {"react": "^16.13.1", "react-dom": "^16.13.1"}, "dist": {"shasum": "6a8c1b8fa0b8ee45a65ec8907cd806f6a00a2bbd", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-2.0.3.tgz", "fileCount": 8, "integrity": "sha512-lxah4lieLB0in33AZSHSwonEFs8kHtS9ECbd+VOFm2eXDUuxUQQktHLdyVCXSkQp7vstrzXEbh4/i4bFzOj4eQ==", "signatures": [{"sig": "MEYCIQCLqiwEo5416xrtL7OmHMXQyVhkvXO6zV1q00JJs3hOYAIhAMnxK//q1iJ/DfJ2f/wFSo+c8KmGRtbUTmgb/IcULMZZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe6hbQCRA9TVsSAnZWagAAcLkP/2t3ioa8ZYTVW5R2IOkP\n7drXygUZ33XiIIfReSev60k4ed0otebw6N/HbrlqgA3ISND53SJmbNCcEWst\nMqiJTl1oLNuT3GBQFcp3KrvYERJ1uWKUDC0ELFHkgsAWSn+EEU1SJK9sq3Te\n8zpi0I10kniy5YKZw/Mc7jw0KVVdbggq9luesR+HAQM193MG6+/7TLTt/oI3\nXPoPYURAMy7SNrsgtRdFICvzSdeLsl8e9g3HJJwBmTH4BevHeC41TW8JX3/x\ncL7iq/PHkftB5NaTdQYNcu0uXasiOCqz9MDHpUpruF86zApO/jAnXqq2GMuq\nqjUqDNJlabaeqbVTpRGzt41uriqvm6XCT/XiF65NgBjC60db3QSZd5rxZFft\nHQ8UFS8BwJHLc5x4yJtm4T67MRGJNkGetjeP42MDsScmnUjX/hjl1GAFgrlr\ncDh7v/GYRi5Va3cUqaDFkWCjgKWrslPYbQL9AKmM7E08cjCS6c1INx2WWxZ6\nnJutrdXRApPGThNZ8WspBvPlehDtAI14z5a+NKKGKdeYTvXCzHXLBNh4smBl\n5wMTx9qJE8c1PO3Np15ckfQxV46HuvUDe5BM846Jga8n9JVr/ZzlVLpQQDMU\n74Fh4YtnRhzderT6NO0It/v41k2AU0HgZW9XqLazdJopkZ6aQKE/kMS4X8PC\n2aAf\r\n=C6HF\r\n-----END PGP SIGNATURE-----\r\n"}}, "5.0.0": {"name": "embla-carousel-react", "version": "5.0.0", "dependencies": {"embla-carousel": "5.0.0"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "67279be6732d05c1a76b433d1e125bc7b6b7fffc", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-5.0.0.tgz", "fileCount": 10, "integrity": "sha512-ED3d0dIUCGCbLIaFGR+BL1+DIenDVj9lfqlSEt0DMN7LZDmN8e1SYqx45iX1WaafN7YSYR+WtAkmK5otK21fGQ==", "signatures": [{"sig": "MEUCIQCmXyDTJ5zg09j2UEY4/KKb9NH1o0RHeuUtZysbsLhVxAIgfSS5Zolps4oOeiHitB1eiru2kEWvx09uyh5i3Pf533M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198776}}, "5.0.1": {"name": "embla-carousel-react", "version": "5.0.1", "dependencies": {"embla-carousel": "5.0.1"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "91fb27ad92d5cd7a4ba9626dc75f4b5e89bb1ecd", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-5.0.1.tgz", "fileCount": 10, "integrity": "sha512-zR9kmMVxh3TNza7Anj/McBQLeFD7nB0vVMmahyDl6XmH1K6o3X6T6Wwhh4fUeRH9nJhAn7yyRqT22qqGjd66oA==", "signatures": [{"sig": "MEUCIQCAx1PnIEU8mq+RZNC1TGdCuQ7K4ql7xEbQZvaJVlNTxwIgK1WeiMue5wnR/T86r2vIqrOyL/mL4eH0e8prBc5TaFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198776}}, "6.0.0": {"name": "embla-carousel-react", "version": "6.0.0", "dependencies": {"embla-carousel": "6.0.0"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "5944a8f07ded2f6becb2246cad042658ddb955c9", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.0.0.tgz", "fileCount": 10, "integrity": "sha512-ttklY5kEH+8vCKiWlZwjyGnXhNX78dyZ+4NtfWiJa67YTFG4P2FD2B4dDhpcttc2v25G4Eyyr8Sy8HG5IVq1vw==", "signatures": [{"sig": "MEQCICxqSrPoVI+U86YLNqtvfnDSRRTDH3XvCjwlc/ymcfyTAiB9kYqVJw2KF909mXYlPJDY5k0LW+e7nB4lstlplgPiSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192998}}, "6.0.1": {"name": "embla-carousel-react", "version": "6.0.1", "dependencies": {"embla-carousel": "6.0.1"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "4a7d75860dc700bf7eb688ae5daa7a901d62fbb0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.0.1.tgz", "fileCount": 10, "integrity": "sha512-pNtxD1v90E4hIeNwolmmde3YcZEfFgV4xfZjiEjsI2feVDMmWWFEJC2nczsW2wruuaKQk5kPpe3M6m31T/p14Q==", "signatures": [{"sig": "MEQCIG7hDZQZ9ehIwlDoEVvHKFpW0CWFpoE+YjJ8JufVTtbEAiAsIW6Zcr62Nu32kNr1MR+FHPxXO+MFb4lrAGfRS9ZJ3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192865}}, "6.0.2": {"name": "embla-carousel-react", "version": "6.0.2", "dependencies": {"embla-carousel": "6.0.2"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@babel/core": "^7.13.15", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "@babel/preset-env": "^7.13.15", "@rollup/plugin-babel": "^5.3.0", "rollup-plugin-terser": "^7.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.22.0", "rollup-plugin-typescript2": "^0.30.0", "@rollup/plugin-node-resolve": "^11.2.1", "@typescript-eslint/eslint-plugin": "^4.22.0", "@babel/plugin-transform-object-assign": "^7.12.13"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "86de4481f949d1bf003d2c071cac135e3e7f01b5", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.0.2.tgz", "fileCount": 10, "integrity": "sha512-oHNicBXb1V3o8KmKzgw38hf4SBy1i//7WY6dsHr7KAyrhyg9vGJck4h3S93TeIFtWJuctrxonlm6zlYzV9LYMQ==", "signatures": [{"sig": "MEUCIQD33yX8Wx07RwReqjlIlVLsyUvylrJF9Q+s5HEc8dFMdgIgM3+UHCk0r92uNNnK2ebTp6g+qisVJJSDG+wHHPEGG2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm6R3CRA9TVsSAnZWagAAp1cP/R4Rdy1Plx1pBnmuBysZ\nc/DI7rRkUhnDvyOYC+Rf41HzNpadLk0nI2KMrKbNkaUSN3GL/J0eEHFDdNcb\ns9V5xVklCqFO/XoEN1Ph60QhJwjsoykPVBGMW6ak1y84i1INNwxSgqu/1CCW\nJaMGOaXbWR0SS3aU1bE4f5ll5RCZj+awdPyf3ibMA+dHMN5tbXE4+uaO4uBv\ny8jkRUYwG+rKfY7EWygZnpYChdhzKzpC35tYC9qjLx/L9FUnSHjQ5JMoMrMs\nxqpL/W0XvuGdZP7aP3ulfi5hffH70CvIH9nWnoD4yk1XF53zY0eUGGVTQW3e\nQeRAtTUMdqC8dkNVcq1beDO5tgXCHBeZRWkHQkRkeNLG3U00O16e1SpCO4LO\nVKtYMaAGfIe/AV9mDBq+hcEsDYs5N45PsQP8IsQh1bvHPhyhe7wnKZM0qEPn\nFRHIJj36gk+dtw9+2XJE8Q8pj9iNe+wSpvzSudl/X8gitAWDt3mEQPpzfWJm\nhUXL8PO6/kRHkGhMRmD5yATQdrs7m7q70VPqWF6zHeN9dnm6gUw+KQYuyGAE\n3+XsGRP7MjtjWyim4O4PFzXa20jrT5fT28H3Fvp9H/D71eOEAQXtZ3CCdL1r\nzDS3N3tS4NceCjhuAfLGp8lgqQB/o2v5QC5sZQ29Epa1h9/2vaP671so6ctG\nUN9t\r\n=nYyF\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.0": {"name": "embla-carousel-react", "version": "6.1.0", "dependencies": {"embla-carousel": "6.1.0"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "e8002e7513560c583b76462812efb4b5fe4ca9dc", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.1.0.tgz", "fileCount": 10, "integrity": "sha512-ITGNq97UDzvEZV/FuFhlWmLQ36qdQkKFcXIJxp97wQt2K5Bs/PK1xS/AoltO789ly1g3+eHE/pDsolcF1QOmTA==", "signatures": [{"sig": "MEUCICon9xZVLNPuFTnM+tXiV1ydT2UVB1O53yfK1gqlMntbAiEAiU4gNL/IM0YZEk6oioTwNAnNlRq8S+ylpak5B7jcxD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 192947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh40ZxCRA9TVsSAnZWagAAT/IP/ip4sfhTjlFHwhXC9z5L\n2J0vEujxqQF1q1DHSZF5xkauxRPQRDmKIuZIrP/etYOiFINHjv/1ROif9LQw\nQ3bv/EYwg3HbuD7fCJOvnoruyAchYu3rvAIX7G/fxkykbNVK0IthMadKWzaQ\njbL1q0JjzJxDa102L19fJ+qq/uON1DUExmdKXe6MUp9gkRXH4bXjNZ91Aptp\nvi5Jer5juSHgG/jE8QL1oMqHZqUEI60OYQXwcVPwEfWAGysWK1mYkY89CNBv\nN79rKMz7tLYV/A8msvBtVnTsfdD/wgLsIYerDNcph2Zr5Idq87CJzW/UtmKB\n3haBqs9UWH+8FBDMg4dF0abMtjNqDpvAUAZat6c2LOggLCwPX2XLwa13l9JG\nz6SvwJLN3TAd3O6YmywwEMe0xrRZw41K8tiazWyBCZu+RqWrGafCJZmlqQ/u\nffyJPyIBHFuY9Auam7y7C5LAWnT2fyJAtm15n7JRKT6elzckzgxs6Nl0bxZa\nfp5L5hmPLWpoZUeZxoBU01jLaa9Kk195XSEz2lR1n78f1F2TEDRA9IFj3eWn\nseRoW9aZUm3sfKAFcCBvtUtsQMjIZ0xPQ8b4UkDw5MS7hXhuR6zg5SDRz4gK\ns0ID0CYZxrvhIBqQ4mU9XnCr5H0+V5/39natUierV9mKPCMWG2NSPP7tdBHa\nAFlY\r\n=aBci\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.1": {"name": "embla-carousel-react", "version": "6.1.1", "dependencies": {"embla-carousel": "6.1.1"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "9c4e1ae7e436596a461e036c99fe8f003563f0c2", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.1.1.tgz", "fileCount": 10, "integrity": "sha512-DJbHjWNwDfZJQRj9QSQtBk2Azp7jjBRmDRennGhBeWqdrgORbQ8L9jAAH2+aiFAk+CeoEyLDnGhvoAGYx4kABg==", "signatures": [{"sig": "MEQCIELMNAQdUhnZYBhLpcCdkoWaLdpsPwMkAJhLWZArVnkVAiBJjTbsdZ9K5LQyLmlsHdQ8+SSNB27l/XgsG43K3zJeYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6IpRCRA9TVsSAnZWagAA9lYQAIVng2kP4BNhQIpQn0un\nJUNTX77N+iLrRQcm0xSFmzeJgpVahFegvUZSupOnrJaklx9ItsGjiFstgGWn\nKvdzrSJwT4khwBN3DZB9gSwIcUjXaBogoL88yurCNuGKwWDC3JKloBpu/g5r\nZVwkJ6FCslAkT5TYXpEQtfry+/TA9Vn9aGutGqmg8Ea0KZDg4sp4f07Hc2+L\nr0m+XBj0NT9gCoa8uZ49otOJb0FOuYUZcc3u6TVmRrWYZAJVARVpovzZQtTE\nHUBz0VQGCz9zU3NwN9DAegVAT7h2OYy8AHD0k+UPGk4NVLlJDWC3HjWduGyD\nWZODGBUe7RrV4ln8EBLWbCAqvmJf9uH1YLLgk5FdatDCWrFcp/ps8OkDI5bW\n8+t7CFJPCH9/KqIi2tXB8f30w6yFQojSE4Z+4Eb8ODsh7w8vbsjbM73pA0XX\nAGz7zt6jyqOeq+LeRsZp92lqsfXyeKpTv3F+xdr4Vf1/1OwHZB9REAr7Gvjd\nYgO+gmHhUsWbuH1cR9JrXDVLwg1jmdE7SrstX8D52YCozPLVf134U461RKha\nGu58rWlXLmHdbkmbV759seuxfOGo+VjaQH/rBjRXOfHt+/Cp9IMJ6U6mANqL\nKbY3/F7JWFzYTsUPEVC7FnDqKEDZ0lgsMVuj5xdlBb24SY4qsHTD1wj6mXAC\nlBf8\r\n=rN3t\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.2.0": {"name": "embla-carousel-react", "version": "6.2.0", "dependencies": {"embla-carousel": "6.2.0"}, "devDependencies": {"jest": "^26.6.3", "eslint": "^7.24.0", "rollup": "^2.45.2", "ts-jest": "^26.5.5", "prettier": "^2.2.1", "typescript": "^4.2.4", "@types/jest": "^26.0.22", "@types/react": "^17.0.2", "eslint-config-prettier": "^8.2.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.22.0", "@typescript-eslint/eslint-plugin": "^4.22.0"}, "peerDependencies": {"react": "^17.0.2"}, "dist": {"shasum": "25fbba759b294b54d6241a04ff56dca83bd3c511", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-6.2.0.tgz", "fileCount": 10, "integrity": "sha512-XshB7pmmhso/fzENefflWw5/N3HqCjO8H5CkzQxuCQqV9w+bvEaBkjUZ6fxfqLUMMSKFWIQvMD6uo9f8oKS+2g==", "signatures": [{"sig": "MEUCIGjhDAOwQAzjHMEfuk5xCimc1G5Fuv3i1paMdBQjEvAuAiEAqBgfdh0VHhpjIc3TtLCl5ob77hNaaCEm/DH8nYW0LY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 194422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAEbXCRA9TVsSAnZWagAA6ScP/jYjQNGfvRzwAcXkWFXM\n7uRnkGSqiWWaTUNtqlMzaQRUf6a/Sd9EzOHqC3BSnCK3r7QME7OnZjMt7fcr\nLy9ip3rWvvTU4+1MsDKizLixfas7jg7oAcrewS5KE7BVWAAyEQu3b47KxIvN\ngQjpAmGC6NMH0gKE43XFcvZBruFi5eSAVz6hWsWDTBIBqnda5SeinC5rm3fJ\nl+2u1w2xYm5Mvo6Uu6Fug1TSPg35kUkSSFgTTjHsdQ4z7dis8OSKLinzlXg3\nVg5XO2e9C0jlnN4WK9nXfYrmYejC3h7+ViRJENsYc6mUNVrwYqzNMfm4d+x5\nW5IzjEgwFh9LAOciDongxYhIhDM+Fuitho9UOAonAH1dwWrYaHZZov2ZYWcl\nNdRG/TVlDvMBDfjZBurXk2WlNrX1m7sehm/uNrwtCg9spk9ctEGrXHMTFjtv\nUAMc7u7LZnuLOMfxd4QHlCtkLXEy6mmOP6kzN7mDmBo3LvvNliyRW4dNDSqC\nk0wE9EVXjnPWfcVIdqGT4HLfvWGjlTGNs7s9QbN8wsRYnaNruGUNGs5K0fbo\nh9fdLMu5hbBjNlP2Zw76QJs84ufHmR+Al3eQDnzAkHTm1v08su8fxwQ3NvJ2\n02M6J9GuriBXRSLoyXbIAHMHGwSsxVOliUyNX54hyDHfoQGGFKqgiaC6haGb\nI8uD\r\n=N3Oo\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc01": {"name": "embla-carousel-react", "version": "7.0.0-rc01", "dependencies": {"embla-carousel": "7.0.0-rc01"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "f4e7a8857c7e84266e6f93654a447d0d792b83ff", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.0-rc01.tgz", "fileCount": 10, "integrity": "sha512-PvgXEtMDzSIEVHOom35ZklEkbeamtEghXQ6ewlHR/qRznnK1udXJSxbZKZEDGRBZ/TMMMYgeKRwlbOxSDNmDKA==", "signatures": [{"sig": "MEYCIQCzy26bj+vEgYqkbM6JLw0d1hTauyi8ipNQksopZdbZpAIhAPKtxv5NM52sLApr0jm9SNRm/OAGXOkDz0378FK9Jes0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivfvBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLIg/9EMWvP/E6kXm9c8CPFK7ocIA9L60L59OkgaPB42CbGnBfulrN\r\nQ7kcfiqRXZCZhB9oy44VrkBk41fkasreH3HX5RTtUNTvxtXPhuutLbXtZ10d\r\nOoitiaMJpk4PeqovrNmNHTV4jajyrNGwO+Iy/uIVWybYh5P1qSJZx8JPBz3Y\r\nvfl0qrOQVXHQInSEn1mV2T2JgBAT4O2TXTEA6b9113lWkKV7e912UZQirMPE\r\nAHkTBj5tAqwdXlNPn+Q40mzsh/cvY3v7CH/zubkVkhCaMukSuSw78iuGvTBj\r\n5u0PPmdvhYWg2M+1WGQ6ddoxwQiDQkP/pl/H3l8fioYZQrZz5ENRY47QB16k\r\nGbS5arNGJyk4vMAs1jnTnPCY+CQvMRpvGoSPZ5jJAPwS4aoTBQSrdI/OlEX+\r\n5DfYUPzG6Rj6Y9scTGmb3gkCWWlmhardj5ID8orQN6kqG+pZZJDX68Ss1vUE\r\nVtjkMeU9poFJmW/85ITJMjLbL0mmlMEh8jzX8QlajI/H/Wqg3ye2qjwOlTAZ\r\nZroQjCYytRZwECu1099ldoGBJEC2CcHHguianbRhOJxdoA3IgHdreYgLpggk\r\nOMRo8zuVjMjCHukRzsYDM1CA0pokDCEMdnYP8tBFv7ftT9DrHZt0jjuVYDcJ\r\nhwQSX0sRJjcAGC7H2F3DnpjZ1ClUVISH2c4=\r\n=zpKx\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc03": {"name": "embla-carousel-react", "version": "7.0.0-rc03", "dependencies": {"embla-carousel": "7.0.0-rc03"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "6adf04a820bff5c49b72969f63b98f74bb6504dd", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.0-rc03.tgz", "fileCount": 10, "integrity": "sha512-6Vu9H/5BDqr6TJFBKc0R2z/UWrvbgueRZhIwvquuDo4EDr6xxhVCjUfOUlDsz6zNJrOSysWfl9zPMtZ7oIUFog==", "signatures": [{"sig": "MEYCIQCFWelVqXA4xEXzxSP6H9btzCfIqm5Xe6dxP8KEcryhzwIhAPYpgSAVHrhojsyaI4yaxmI6aiSAJlHfVF+i1FgVfICE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixF+5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgBg/+INxZDnbKwunUuLeCz8Zbp1Imgxo8oywmW6gla/veoTMH4B3L\r\nMomDztRZvYFDLEDT1qdyYLznBkKX72f98IrxYfB3RE78gUs7aiIBrg7v+r4V\r\ng9XA2nV8/Hj6fVaeu8Eo6ABlfNw3tidmYHkrDF5XwS+VYpJSvDm8Mhm4VWIl\r\nqVqhSeDulxmH5uSI66P/OCuWzZcHDy+FlLRGDKd7KZYdKs8XkA0xKVOMmXbR\r\n6Qm6tTFoIEMhl7Ud5bp/Izryv+sETscZ9OTdNWkheREPAxB/cSanAgT3e6LK\r\n0wRX0zmmd5BdjB3ebWawQj7e3Lku0rxH1vxjpTRaoBss6Iq+UMIzL5DxcXlw\r\ni1A0DtM/GJku9aIR3OA25DB2iPbS55fyMwnB0c+0/DtdSZ8LQIKh7qxUzSmZ\r\nxb/goekFlaggpM2MRctdK+al5xF9Plii4taJg4HsjztJM4p//z4RYx9Am0kU\r\nh0eNjPLkAgPJ/4YlXrao+LQNlQu9FAGekch5qB11TNHyxWC2DzCyF8x2x1m7\r\nYH6jq+HV4JGu3M8x1xIp/Hh6KpFAU9vv/6NC2E4diXvFuFqq1TVYKFDMJ+/p\r\nTUrNa0QKFZn6GCmQ9gqMl6E0nzwd3r0uLtHyaRh4t7R05sFyRpTx0DYCY32y\r\nQ/sEP1A3OUrMUhCKrxeyLlgPAj/DbnvSK7Y=\r\n=lMyv\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc04": {"name": "embla-carousel-react", "version": "7.0.0-rc04", "dependencies": {"embla-carousel": "7.0.0-rc04"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "ee3e954d746be509aecb78e81ada2ed094f89cfb", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.0-rc04.tgz", "fileCount": 10, "integrity": "sha512-5Ndn+EeLX9DPZnR1wXH5+y9gnrh+ML5HO0+20l6rcnuhVqpJRMv2E3ZWnAw4jw7HmHAl+5eDKZaA6CGxgWiUdQ==", "signatures": [{"sig": "MEQCIAbXFN4qZ+CGkSUKhniGdamz5N8JWYsYnOWcx87eBM/WAiAnCEZanzhxGOWLDzw0BjI7GWXwzlUzRtYqWeNAn+tNUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix0ZDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3NQ/+JkFmFz7dI5sjG470yJZMHM/81CLgi475knASsFdbD8+N7bWv\r\nVFP5WW43tguvMMFElOk3SdRqBS6cUFA5awZ/m4BMkcbpWb1SvCW7YyYIQ9Tk\r\noCsX1HxzjBU/g6Xz1HiPYSFRHH9oX5WfFmoU5tj49B3injfb69uRBix9MDDn\r\nSnzIBHXSqCoIVUGUIv0Mtk/q1VOrxjDewT4P3yV37nK4SCN3FHnDvV2TP6Yq\r\nZO9Heu/0xXVatZ34gF5fQEXu2W7U7uKcpYi88idHMcBdpX3HOO7nbx/WwlaY\r\ngVfOAxSeuoiRhnRAwB9f3cXb35cbJKjMf6WHA8uugaFEJP50U4hXTHAtz2so\r\nXqeU3cVkl1f/f0YQq7b6eBq8V6kbrOed9ctaKcmDcdFK4pb254cC7gWKMx3o\r\nSw4QPLqyzeNV+qHl6+ajp/PGo8cFRyAkFAjis8Ksgo1y/gfnClhYhsrqfKwk\r\nFpDYrk6WJKIGBargwTtEYz+pmtW6eJtDOCw9RDmhzNM3gmqFMXkmZzIminNg\r\n7eu4vd4ybc5JOF2jzFKHkJE4KUb5fxGZPEWiinakzAeVytoO1+rXLCVbKnBp\r\nv1OlkOcvt+QuXMP2VYmtzfpMMlmVhPl+BB/ODAxwzQ72hP/gKTcjZMw2y4P5\r\n1psfEDluZSyWNhwCI2um7E0WlXwBGuIse7U=\r\n=EHlZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc05": {"name": "embla-carousel-react", "version": "7.0.0-rc05", "dependencies": {"embla-carousel": "7.0.0-rc05"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "09f3e0bec80403fd6f37cef59361484494d4f782", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.0-rc05.tgz", "fileCount": 10, "integrity": "sha512-OYvx48Y/4/VeicKctqz1lsx97Z3ve6lmqmOr1SpL5N7JZyJVbegNopgPy/bi0H865JvyccaxFGxHFAr0YU7OKg==", "signatures": [{"sig": "MEQCIEOY5MNkg4GFfNd3aPZpSj69c2qVd2DzdRlTrkG2ILHhAiB6Jmw9sEqrcS0/UBWVp7Pl87u29Ny0kFlBZA7PHYjE+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24273, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0ytiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUThAApHF5+Y1uJo/XtdCYUxm/2JXhZ3QoojO1tMfioHLzy2rm2IbT\r\nppLjkhxp98YKFcv2kQWJJP+sHUwDG6B83TEVGxWjElaZLThc5VMAS7OCBmYZ\r\nzfZ9THNt/mb522oCdGedd6xIAwkN74oh+DzN+8m/1l9mOmocZSLLNOtsmgVw\r\nWPBBQOwWgsZeXv6z09c17d4/px5VOUJwop6X1UxN7H8ZcNpMNSpGdEOUK7aa\r\n4V7cRa2P8U9ElEkOjLPHV3f5LP77Hg6fTJvUgibNGzhEzSKdyh8FqYNSZAkM\r\nujluUICDK7/FDlvLVEX+s86J+Fw1zn4ylxAKGUxJNTgH8GASqa011gV2JV4c\r\n0J+78XnW3emZkTanMFzOySdIuFs1nCZ6f1agqYieRtQYy27jFXr7EiHk20/m\r\n0V4WrkXaPktCOsl8YixbZPrdOMpZ9jENlZzJdfE9NwRny3WukkGX78WM7H5R\r\nmBnfj/i7Yn2POty2eseYC4JzD8/clZwBEolUn7ghzP44T9kty1n01UUdi0N+\r\naYAO3lWeIZ5+4oUDCIsIOJMuMQe/ajZECDvRgUHnaylsJFvdIhi2rpeXsRo1\r\nlbhXcUelJ4HqoX9EBEvnxn8lDMfpsBn9RPsIL6yazUi/e8nVXKqTm2ZY6myM\r\n7irMHcJZMjSk/k0oBJEh9u+nKBsXYLbHJBk=\r\n=cdLB\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "embla-carousel-react", "version": "7.0.0", "dependencies": {"embla-carousel": "7.0.0"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "a4f352d814734d399afc02f201db6d5ea03716a1", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.0.tgz", "fileCount": 10, "integrity": "sha512-y17TYtqvvziWbDwwfX5dh8n4qU1luytz4+6WWMBnR4pJfLfkKBGsqYNZ4WhmAgUcYL1Uliti8Cjg+NJd46MPxw==", "signatures": [{"sig": "MEYCIQDxiDqwnxGEFGxSnGhjnO+GebypgiTsCxm7wcVRUhUdpQIhAK0naY2UuqNpf6MZ0kjDcqkGWo55Q+u8BUQeAis3k8g2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24287, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi55/+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrjiw//e1R+ArGZWYwUvynpY5FgDv0rHsrpnUn+QHQGXe+6hGUkfplN\r\ncAjRtgpItBD2/GTyGd90aU0B4LY2cpMEBsneJ1tzEHGss0iTvwdmXcgr+jIQ\r\nuQq+90AxwoJZOFMdOVFH9wJJze+BA3FSze3qOZYH83YaYYUqufi+ytTmPL4z\r\nXZGG06t2bF7Swt2VxpxIW0VHDBM65YRmN/RQhD/srC4zEXWtMExLVuRcFXpw\r\naJ9P/ox8Ey2AHa3LeOkPFxJ3tW+VkGYzT3iOWPnR6nSJ9rGOMbLCSARqtJRP\r\nRdaQbL/Km82t+F+JHc7aZRth2aNTAN0XEED10HPoCWZ0so/HuDlY5ANfr1o5\r\nHbEG8HxvuXGM7U5oZMqHSYusnOjTeyjzuwcN77OinH99rNt3mc0yTVBmLnPp\r\njG93+9NBQm4fh17+wSuxJSVsUnTas0vFb0gD5QaGJtgFJXWnV0xXvphGlAIj\r\nXGOSDEuASv2asoEciUo+0Ut9TQdv0wOXA07IOCbJUkKe/ouyl702PZzAaLqj\r\nwa+9p0RG48a3+gL4cPyEMM59PcurOD5bld+9i845umOXscWN6H7IPhUBAesj\r\n6G1YcomM1K9xUOt2IrWxz0W0a7sz7ABMAa9zJojqIsvSHqOg9HZwr1nAOQYt\r\ngapTNuE6Yiw9hl6u24LGqgmLJAjA/QSf8XQ=\r\n=uNfP\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "embla-carousel-react", "version": "7.0.1", "dependencies": {"embla-carousel": "7.0.1"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "357397026c86100d6902f231ee6baaf82b24921b", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.1.tgz", "fileCount": 10, "integrity": "sha512-D97sHIMNYAClp27u4N86RVBbN9cNVAIri1SC6oIEFqeIovSGSV795tTiQuRL6M4vGXvlEnau+QtrVPVbaG5mcQ==", "signatures": [{"sig": "MEQCIGjvSGOki0aZ7dty4rLY+ls/0PBwhjNMexsQP6IJkOEJAiAThafpKYXROgyp7MnAhZsjIpWES5tVXrbkZn1WxAwM3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/jmlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3MxAAjjTM8kRqCLuFQ26SUOIENRwGulvoVLGlA+vJwP+du2XQ0o76\r\nUk5WXvPCPgeEpO8oLZHsoCGKXsvWy7UBE3Nis17AP9JhI7qoKazpqBV0biJM\r\n0H9Evw8fDBvPcBXmhEVxZL9B+J6AaQryJw2yAob0k6T7DTiRj9f3DxsbVjWo\r\nTqUyGF+s2r1OvokSGoyqEvmK/24GAfSKdQYfpCjnKGIZb+vMfuKvmXn2isxB\r\nvprNJURGIsU9vADCFgDP+AAFJFStNXg5oomd5kBQNmA13JuZL3H1A6wQNwUE\r\n2+4BFFrIZOSHhE/JzSwEyUCunfMF9qCPZ3BezQ/z7mTfjeNrYCWXTTVILscK\r\nriWk3OoY+DPjaGNUKMLHF8yIffxW13D7vGPB1E9yhu8E03L24tuBOLEpLPmF\r\npUEiPi9je9wRtwRk3qse7EbDNIBToM3/eQylC1eC9aYsgjQ7eZH1ealpzh+1\r\n7mVQo2V1oNMuLL/2pYJofQa8Gzd9UUvrC8XObXUWToHbqlEF4AO8dRupem/W\r\n1KWoaAF4XAq0tQ1ModssGRmDyDNmyhAu4uqza7+eC2RjRv9KBWazxMBuDTXX\r\nlgO7jZsHdq6B4IXF+is3tx6AL+gy63e5IXNhXbjMGYxo/tgRpwTalmJdy0Xd\r\nj61JUTvZ1aGETiIOj3ky0AAbDWko4PBFqYQ=\r\n=Jx2p\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "embla-carousel-react", "version": "7.0.2", "dependencies": {"embla-carousel": "7.0.2"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "82e48d8fb14adfe6d4ea415cd1ec8a1de40129f8", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.2.tgz", "fileCount": 10, "integrity": "sha512-x+/8SbytmhY5K7TrVaZ+NWpGfkI2NaOBgdEseExedwTAOW7MQDoo1lE0yEJz0RHwvTLFBupfbbgL/GxMlhWVHA==", "signatures": [{"sig": "MEUCIQDVaQNP/KCCJQW8HYpgPXz4dU/EG6u7PC5LWyVMMJss/QIge7AHNYcqZSbEd/q+teR7i3giq9rHVdBTyefK8eQaPQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjDzuPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTbw/+JTKmL/xQ6DcpAL4C6mSD8JDFVcnWYKg7Ac5VOqz82dW+r6LU\r\noaFhpVMH32MVJGkYFsr7QGl83K2c5FntUhfxusXpJObsIO9WgOgdXc50aTyU\r\nTpYvR7phv+ZAagehXmGYQTUEbeAMlRZMv/7uwZBvuz0Khma+YL/sLt+idDzQ\r\n/wo8gTXZ5PoqdqcYNcmqvsF6bSh53U6Gd6c2c7/f3oRmugKqkx5YIZsWMMtp\r\nyO9k9BufVklyVVbMC0kDTtqICUwcHBEJ9P+jvrn+1z/P+FAYjW8O9ouJWiDU\r\ngARuVOnYxMdC3tMnhixy9zMhCQytOWooXm9Qz2Vig95juEuYIsJ6VtPO4cUy\r\nj4Gl83kq/Z+OI7wL/GiC7LCWgTEmQaRYQycm7/gkz61A9bwOyDbFPUr1qIYD\r\nmKzcVQd0wYr/V28fzuzTiajkfzl3JhPuO/pNuNIztSE9qrIzgPN6eXKbzOhc\r\ns+QzD4g3OvgDgBV0JBqH7JMKCLxqbq0UbF79ud5pqnuyz6x40SIq5DLoQB+w\r\nynnfJBzq2WM0vxY9uBCtvmMNcUBvLvpJdM9lTABE0M+W+/7d2IV53KdbABX1\r\nLEmh1HljsIqxq9/6R+Zrln7SimmEV4/l7iCusp6m1o20yD/WjSC1QHnzI/de\r\ncLFIy/jvpyNsKtgoRz16FZpAhh9IrxmqU7A=\r\n=Z6wL\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.3": {"name": "embla-carousel-react", "version": "7.0.3", "dependencies": {"embla-carousel": "7.0.3"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "343c972a1ac5869acba3d6e9c7eeb3bd27f7e335", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.3.tgz", "fileCount": 10, "integrity": "sha512-nvgAAmoFuXyCPuH2rSTnSdaTSMwFX/ilXDlfMCCDfdaj4X3u25+x8lMC1bINuk+4G2Hm6SOlrZ1eGkYdBg5HAg==", "signatures": [{"sig": "MEUCIGg2RLD0DjKtkyXM7TXck2RAk13hxL8ApMzSKFd16+ytAiEA/wqyWCiXgs9GyLYeNfeDJq6JjEMI4v2wB1YBm8g1zyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjH4azACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoauA/+MiFF060LOqaue1b2z2DgRwhVejQUnsAOxSoclftTfQgZGqJ6\r\nmCXtIG9MGztezDkh6OAmswLEjcbRjkqgreLezHQVHBID4R0/E22OFQyJ22j5\r\nDFLEozIoyQV0FLdZBA9bwQnneX1x2VpdWvzI9G4Ubl6XVUweat34pZPMRbOK\r\ngNjI1Dp+QahnqI+3Nt5m9pj4/EHSC0pQzLv7vhuKUww8rHo6KX3Dy5d36QGu\r\nM5lxiTTrNJJXOqQvDeaPSuE/ASiFC+9f9H1fqAajR86MhR462un4gSq8gTH0\r\nRG45v3rBe54qq+ODFNDFG+eR2QKTY8Diiq7vz9KZ06cDNt+X6/X0NaEDYS1y\r\noxFkD+zzWZzX49ldVjQ2zW/QhuN29qztKEOLNNbh40g2rXKVJuzQFL0ljod7\r\nwePtpe6WWAuPQ+7vab9Opo9kdFHSvLim6kNTvDUs4mScW2MTUd5umWw3vaZ3\r\nuUIswgySRAS5GHF8UkuXfRMWiu7vFZQ1KOmOAysyULeFJ31co/twKEhokfhd\r\nr46K8oxZmKcO0PLULcl3+xITGw053e4pseXJPsaY2joeDCwNupUXcoYpyrHT\r\nD59qP0ymjpZMBsRJRmmClHC9Z/3Rmyg18wlWTqJ4jxS5auHAwxrMGEdQQFKj\r\n1bj/gEdNH64uI0bSRaNa5WiFUgEICaPCntM=\r\n=yrGM\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.4": {"name": "embla-carousel-react", "version": "7.0.4", "dependencies": {"embla-carousel": "7.0.4"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^18.1.0"}, "dist": {"shasum": "e5fe99db334e33f08f753fec65507c3aabb86f63", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.4.tgz", "fileCount": 10, "integrity": "sha512-pSnxrvsLMt3TwVS8uqYMZGfJvRLy+3rcg3JOImVDgvNEtEbbzXoL8tR34Dph6Jh38e/Z9IZW7jVX0igu/haLyQ==", "signatures": [{"sig": "MEYCIQDCNqa7j7Dj++VJJfpj0ci8kRe6EE8Tx454KJMDdJfKywIhANxhxABBi5ImszczhHCElm1mCyMB3psUlEfC5VGZdLP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjXSzJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpz0g//VP2SQlr+9FZH6Pk1SrY48jY1upR8ahR20YAEv8+pqtgzjsaU\r\nld0X4gHSNMibfTReNBixYwPziSGhyIWbI90wOqvX34F6GvVZGnpuikliaXXr\r\nqGz9/BuxAe07LAaGSzQRDMpgEROEV/5zw5vc5MK/kLcMwdV4z06Fo6O80ato\r\nA1DyIVU32+73R6YPMvYRXXK0rKGIFBwzAqVxc5wURsRqgFy5mv0nP7muyoCI\r\nInvEv83rHmw90UKrIbZQt5uqOFzoYE8uB40SbTQLjVqqu9EVH91HYxyGyK78\r\nqfKogulKYULh5Tc6FspBEu500ThgY4RaCPFF3k4NIDrSZ9WyibtA54rpqn3D\r\nVSuHDYXosDS0kJSdN+b4rWsZWX6l7XmPD4+Iy/ZfbDYUicH6Nby67oKL8KaG\r\no+f0+0gtTP33higzC9ZcCJZMLlXHGtHoSCQzdoanQ1sAPY9xTMK/5KmoSzKS\r\nVeEWjgn3NJxy/xJLlpBKQ3LEzGL8D5X8U2Li9KMg4F2kewS1M5mQe9436hr0\r\ntp55EICNCbBsoS0aRJdbZPZzkJ2X8/tCDp1uKM0Jq0qDdLWa40Ywx+3xHgfB\r\nVkNUQXt8BDBIOTXsfPpPBDScYrEqMafXLANkf2tWgGCIP2A5kUeETjmsGGW4\r\nz8EIHXskpSUQVKwmAvg9iGzLGa2VOgs0wzo=\r\n=X7Ft\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.5": {"name": "embla-carousel-react", "version": "7.0.5", "dependencies": {"embla-carousel": "7.0.5"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "c6a079e382432335e34adb16bb11558df047760c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.5.tgz", "fileCount": 10, "integrity": "sha512-qhw980gKj7G+O5d3WSwj0TyhcbpGP3DqJPiAatt1t3wEEcZqn7csaIfLh4e1N/0kna9sZfZZRzz+//NSrUodSg==", "signatures": [{"sig": "MEYCIQDmx7y80byrRX/QQ4NTktdV6XvSKRl65jr5LwFIlKZeCgIhAJjaCRgyu9J0qa3ZrJM492B6mZk70aEKK7Lsamw+H+tk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24257, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjah02ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohKg/+JMSyllpHohYMT+c7cllOrk6lI98FQHVJtclVqcRO4UXXbBkA\r\nX+OXw9+thHWjumkM/31YT3N4EBE+7RzCaC5FSCgDqRz7r0CnfNFD9kOCanqo\r\nDJGSJrr9r7c4ga7GAc6QO+01xslNU9mntPOT1P/D0i3T/c64sPRlNFoQgA3b\r\n6jk1CLMAi4+Zxuoz0dYERK5T6mnqRrAahhd3byYdt6/lYJ0yyCgxJBQsuxgH\r\nwfB5WjLaPzDw/rfmTNQxM/6f5xnOxfRVsdZ44+ICUmDziSEXsFvekncq+o4l\r\n2r1MSbnaLyhP210eyMMNDYyH31FGu5qZqfSlnVl/dGD7eh+tMiIY+l958UQA\r\njL5UfPQWYEa4TwYOtmlOJ97D1Yi3EVe7kJC1ghOLuajBKcczyoVI1OLoLR7n\r\nw1bUIyfRPq7j/8wfW+jrvw5htzYJ3x7ugETe9OTiaNqRsXhjn1Tu+jcCY6Qj\r\n6oUDur+aqbbMwF8oqrBVEW1+04rz63yk3Cmhw2CUsdvfzZlNq+YQLymEEvWH\r\nLgP8RuDchSolrc6oLtXmd/vbJ71v+YYE0krzKOVd+7KU72lfmZUZ1X5g2r9h\r\nfnrEmNlH4gt7jZ+NEVvUKQbF16PujHzO9F/i/x8zdNnbhvWRqQYKdyQy13kk\r\nI+YRNUeAk7JjvQSX4XAi+CqtScNhFiSO4Qs=\r\n=qSNh\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.6": {"name": "embla-carousel-react", "version": "7.0.6", "dependencies": {"embla-carousel": "7.0.6"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "8119d9ae2800ed8c7d436ea84cdc9bef21844f98", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.6.tgz", "fileCount": 10, "integrity": "sha512-tl7dB8X9bCd7j8iiU8Nm/JZjiWRsNLofH7dmc0TjVMvMZ+eEy5NvZVq08Nv10lBuqGW87X+i98HoKojkga91Vw==", "signatures": [{"sig": "MEUCIDLqQiEeCW3NYdv6golVzZQORYwBvjyfPfQecK/GdRI3AiEA571ZfGV3JBCJWWPrC+8/SNTt4LPaWtUO1bSMVCPil5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxvL/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnyQ//R/dOZBl0df1iBQYyMj6svOrrMNJ2VVzL4+1gQI5gZ6V4NhFd\r\nOOBbzVKbn2LtBJ+4Z4yXVtznw7xjM0QBJ1sC/ckXfg/bE4b4f3w+cLoQYV6E\r\n2159yhTxCZptO770qdEgazRkhHZuWQyHyYV13kaxu9+gUm64uqG95QU/MqIw\r\ndRfjlfj/G2Oc4RuFypr5C2EDULiN/D7Fk8KgL4tuCpA2Rib9UW1YYmG7z1pr\r\nhFYyQHVRP50uyoGioE37bO1RS0fphO3zqzpSuH3EM5GOD4NNwZ0oYLkJ06pH\r\nADDlimFy9FI4sYZbhjm6ZVzhWaugQ+qYI4NL0fPnHtbJ+f0ecAHZAlpPZv9+\r\nrnwybjy4be5rHnRSZAOTucnCdaGWXHTwxwNd2tEKeI+1gAUbhL+4IE6lfCWL\r\nqFaVONEV97iBaH+yhNfmxzFS4rp/QZZtd1slhkS8izQSKuPOKYUUgncBsD0W\r\nRrzA0H5tlK5sNT1DT2HeHcUszUNLX+JZBZtH8MqADDv9OQuB28WVXmWS5CJq\r\nDYHIvE5Mzl476CItFyzYRtpmiAlsKYfAs/cxNI5oKWKojpLNc72RnDswxqqc\r\ne8S+bbm7pT31r8/Gncz7iAbobqc5adPp6x0KXlxZVoiRwiW73Kp9m2WOntg5\r\nlA5/P1Ud1+QHM0cMkIB1TCnX5y++Oquj/8o=\r\n=Oy7S\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.7": {"name": "embla-carousel-react", "version": "7.0.7", "dependencies": {"embla-carousel": "7.0.7"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "d5a03fd9642b4d711d7ae81ac35cb5acf985bc22", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.7.tgz", "fileCount": 10, "integrity": "sha512-bm3fgBabS0YDTeaghStoDw1b7flLZwzXenrM5k+fHT6C8RudFmR74fPy3ftUJT3ORHPDQXcS9EjrhNPu3Ao4HA==", "signatures": [{"sig": "MEYCIQD1G34FJpAzyu6ZOejvUGffK5NIUtBnL4htZoxTrFHDJAIhAJuKsQ2KXMrG36NCIM4s62rFkvWvTePaX9JgURoytcaE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyuqxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCHw/8D0zwoWEC59Xd13zWhl5ZmLG89nPjvf4R2IRcusUHE5FigN6p\r\nLkDtF/hxrvCbvshXaN1GLdxkjWYxJQOrzLMl0Y89Tge4hm1hYKAD5navhGXr\r\nzGV2zBNf1rmNZ1y7eye8U7nPy5rIc15n/fcY6GmsXGtMEaSO6MIi8qoxviRj\r\n06J+pxJTAcftRN/EMf/9ceOy+2kV5usLej6dcQ7+ZwjC7PLCVkpJsAQBeZBP\r\npg+C8vOH3B2kqZ8e8Bt1XrefFztN1RQF9X9ZC+rQYBHXy7Fh18tLxxzR9oTW\r\n8PtMbDutt+kqq780dOM7H6xWez/+bk7y2Euq6c20buqdH7dy6GptyH8QWUHV\r\nCraq7bjKo/7lSopC0Yf6y8GGB4mMl62oALkwsC7wGKqcz89qi9m/Awp83hVl\r\nKp7rIWbcNUFn7+5BaYxyktarVWfSVIrQvqKV1GY5uOYjtr1bSUr5KOe/7Huj\r\n3wN10JbHr/aYEYe+MQ99HWp6q31asOjDGLmwbyhcnJ7Yb+q9ANQJfcjjpNMv\r\nmkiJKj6d/kl3TOpoD2Bt3/sKOyGDeBlmyrZGQoGgfVfTRF6ZdoiZfqpMjrku\r\nCqdHOqPSnAs/Heoy/SfR2MzDew1rmmuQVORaDOP25NGrf97DAOBDmDl2pYEH\r\noT0PTMLFtvMlA3WcAb5Dzi6WnVO14IOIWe8=\r\n=TYnk\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.8": {"name": "embla-carousel-react", "version": "7.0.8", "dependencies": {"embla-carousel": "7.0.8"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "704916a3a09c32b208dd90e87e289fd51edd38bd", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.8.tgz", "fileCount": 10, "integrity": "sha512-Bkc3RtDH5HONtE+YyRTlrBQklKRaDdmiO/waT7n6XaSNzswvg3z1v/yJuNof4dv5O6U38WFOmufepvVsS58uVw==", "signatures": [{"sig": "MEUCIAOWWZk8hYSiEoD5oaYYIBoDlKcwSWmBfFCmr7kAjFbuAiEAihEBWQ+Mkf4FmwZzgKD10EBvEQYv2BgQXjfHl+sTtx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzEExACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkag//W/DtIw+GufNxkjnVkwI63Fo9gErsk9gr+hJjcsRrdTyeX+xe\r\ngliUKDSpZHTS66c5ZK+PIKlkKf8EwcNy5szg53u0H1A3uErZqVf7EDgh3Ipo\r\nYU7oyz4DLIRTapMrMCVWJL46vRvWLVAg0cV3aWUlwFZO3fhZ1B+pX1G5PEds\r\nZdRM4uCQdRo/UUp4+Tg7vrlYwigMi2rGwc1uJO7ny47j5eKmiH8T3vVhinG8\r\nLaGhrLIW3Dn120Sji3nhPY1YxHAUbeEe4ynm5jxmEKZqwflZ4KXUlMxEZ+1A\r\nlDjA2GHbkgrREbFAJMnnmykjqKb0V+PnucyllSjQ4dr4wPFUvMNCwqTmfYfO\r\nRRBoOV50Bnbmt+3IXOKoBKQ7rGn2g21uFLRxfWSMg4xXYehEvGbhfiYgv+ix\r\np0iAB+OECpvLXZFO2IxULwG2DyMUSZq/uI/O3UiiFtbsVEJhWTqjHyqNZVSN\r\n5dhtt4ivWq4nE3Pu4goXbFKZmFOBD5QOoE5LTXDLSSxp//Kh64fdNxAeuplX\r\n94xOiWzyl8vRChpKJzSbDZPf9ixqHGcDbxgbqg4/3vt7WrHavtA1oG3ScU3Q\r\nJUSFijS9PzJ4CizU7HtycGcsL2evV2mzQz7PSFuc/DStPBu+mrfVx5KKei1+\r\niVxpu4SbgrzxZtK7toorXqv/EBhKThOExmk=\r\n=alIb\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.9": {"name": "embla-carousel-react", "version": "7.0.9", "dependencies": {"embla-carousel": "7.0.9"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "7ba5664eefcf435c385ceeccbd7c722654fa5e09", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.0.9.tgz", "fileCount": 10, "integrity": "sha512-KGnnZWukN+N2H6VjQf6C9mWmInqtCIiInhkGsJ9ckxri3m3Ks6wRDz+RRlV1kcuPHtRoxyFuBBGNwftWqMO9Pw==", "signatures": [{"sig": "MEUCIAVREO8HvE6C5dqpJtNoOFW+KAFxZjThZZCTPDc7VLXTAiEAw1SkyTNySxndZNdfvY7Ughz9/WkeIzLQzNrAK/wBnMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjzuo+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrctBAAh5acOekl05TBG02nBRJc0eUbdOifzE6rELieM6YVGbZ7cZ3G\r\nRKL0MT7zrbmJ958EEbitXuhBGxpK02WvCEG+HjhO8OImzs3vFpFjoiJSFGkQ\r\n6q9fMABIfT0W7EVcmpljI6NnUGXxYF+wpymfzBeVDcIjyt4gSz6RoMR2CqCp\r\nF8gsCmNfOzkmu3c6J8drqRBXyV7vukvHO/Vi9PxuFYMfGliW+MlxRe8mmPJu\r\nELQEDSsNC3/RiA+IEKWGYWiiVQvWonLhuTXU3LazD/vK62nhiKQXn2qArJ9K\r\nM1ae/wOh9ISIy7XaA85ZUXFvm+KmK8p79u4ICklP/FnYzzDyQRhAG8O2kH2N\r\nVppyCYK6LfTEDVypTrkPvaA7erxUm4vyeUjtql+dFMpsj13plYfCKWWCEZLe\r\nH7E4P4yZZ1Thda9Ge2q/QQtY72tBy5RUUsGGgfSoTXaAp/JgpoM7rgb1b1O2\r\n/ws1qUVKQ3R+S/s6UqpfErKp9MdhWiXISa7rAchPrU9yB0gscH2LgCBypUmC\r\nBRI/eD6rmBmrfNFXfb4OCxriJeBjPkGAYpjW4tIgxYoywLcFSUoKTS5LceVW\r\nRQtx9fS1rZnBH91pp4XY3JVpWAkcaTztWi1GFZcRyyi39nDJXU0IoUQiuSU/\r\n085eVbOJ7VSBgwyPkg149az7qzm0v951Dwk=\r\n=sCEu\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0": {"name": "embla-carousel-react", "version": "7.1.0", "dependencies": {"embla-carousel": "7.1.0"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "e716f4df7fd31f7ca3e59a3e011158167bc3c468", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-7.1.0.tgz", "fileCount": 10, "integrity": "sha512-tbYRPRZSDNd2QLNqYDcArAakGIxtUbhS7tkP0dGXktXHGgcX+3ji3VrOUTOftBiujZrMV8kRxtrRUe/1soloIQ==", "signatures": [{"sig": "MEYCIQDd+SBU8XTKeluLwoAZrL9ZgMWeunkpFSnXRxBVnhEcbgIhAIy1pUxLvMsfVVf4d4Wdsi/1GXkUpG7a1uWw4u9ibSLF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24145, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCOjOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBIA/+KN2lJcRQYm4rT/WTNsi94mIG6dqaA4vQ+OKwDdrbvYzhKZOw\r\nSq69duWWeTZVexvwA4syqamBYGSIOPSrsqhsvUGof7OXpUm1iZMt9+gexTNw\r\n4ZOKdPI6NbvTtTH3M5t3Nbw7RcsWCcxggr2/FZZs7KWFpF0Xee47/MhB2J+0\r\nPGo14tGKWtMzW5zWoDDHuZ+vOv8f1EassO2Wp/gvNV7NJkgKmoNlts5J5VmQ\r\ncoypIhT52pjsMwdABmiGIXPHos6F3szI5qeIG4Swikompr4L0LtsuCGPw8GX\r\nPkE4jrSEI44wFw/Dk5zOcM4VJnBxWjbQW5XJBp5XbhAVzyz0a2WJkdZ5y21s\r\nRMUeo4Lq4NkGFKcZdO4JR74ICQDxTEdkZMGFwrYzw3AcTlVCrc2RkvsHU8vC\r\ntBgsdpKRTxnL7Jp6K372I0QHtqkTJSxjv8E7HoukWUlaNjCA2DHnd6Ucxs0C\r\npgasU2UeMqunOGSvPz9+E7/7JDu1YRH5v1O8EyI2RfX5XUft+4wb86m7RCBj\r\nYlyDbWZdGMfts/LDhZne5aSN0JcHlqJ4cYAgYmfIrvQ9t1MDBKIEyn9MyC+1\r\n6YYSxHKdUGiWM5BuANqW6V9thWOIyYB8iXjNXzyugua615E3CRcgD1STWqAK\r\ngrUGYgvaXOwMWeuOF+0G86FBK16wV4NxswU=\r\n=pYo3\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc01": {"name": "embla-carousel-react", "version": "8.0.0-rc01", "dependencies": {"embla-carousel": "8.0.0-rc01", "embla-carousel-reactive-utils": "8.0.0-rc01"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "5bcb1333c153b16b88e9a91ba4f2638f5c438ed5", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc01.tgz", "fileCount": 9, "integrity": "sha512-X0v+qqCqoMFmU7U6EDQljl2CcBvLaBMi4DZjDqLI9eH5dlTkwmzFYhNIIFpwC/dvxEv8ly45UT10oKvTecCUfw==", "signatures": [{"sig": "MEUCIQCWrEhg/A8KM1RDVZchD0GoUK8S+bt6WCRFwnvl2vdAOAIgRlL0MjG7UnUXdRuuosHafnAiV/YbNsbl9Uf1kY0/dbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSDZWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiIQ/+LWwlR243078c4RXjfMPde4x/amp1AZ6EYekjT+mPXD7UtSf7\r\nr1SOT5gsEtg02MWrRx57UVD/kn3vbc9phGYOKXwEtbB4UOSqzG5G0LjJXFQi\r\n1i9k5hHDfh+KLhFdrtOskxafnc4993l10e9/f19uIAiyzeSbLmaoCw0CE5u4\r\nm3Ke4fWqKWSySuEpYKo/oz0jSLkR73Gq5NFo8y4WlMO2lZmN9BEAnG78Bjjr\r\nug8t+y1r3wRLP0081XyXeJUCjnSKeERVDBrEI6TiGChY93aPxIBC9U7pv7On\r\n74vWBc7+tXcVhD/CMva8Gmpwb0k+5jG+IrjFHh5upCN1O+ya+JjxxNQZpeN4\r\nx2Nj7M1ct9Km1CXE7I3LUhagRfmy7A0uV5F8n4qW2J+xboyc6/UbHGsIGRVE\r\npBRNLS2kS/FkzeehA6to+beiyUaneqbzwHjQGQrb3gSpPwLRETEBHWl1UFw4\r\nRvNouMIFWahL71c6PNTtg8zzvErLLDmQsMurp4wk9jMTZ/JoZb4k3KGAoWAC\r\nRMHabDHURu8zAZXf24c8AuEuEhGl149Vbq7dl2AQRf/xa6B/Z3YshL5dppXz\r\nzGVM4XA2NOi5RaQHPGYpm+JwXOzAEomMzEA2pqaDYg7CPmDtsZ5oA44804qO\r\nLvnn73ayaHRrTLrbfB3QrRHkHoe68BTRNPs=\r\n=IsP3\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc02": {"name": "embla-carousel-react", "version": "8.0.0-rc02", "dependencies": {"embla-carousel": "8.0.0-rc02", "embla-carousel-reactive-utils": "8.0.0-rc02"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "fe6804337e218ae21a76fef100070d5c89363f2a", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc02.tgz", "fileCount": 9, "integrity": "sha512-ONcbfeM23FE6TUNEJcpPbLTha/EQdLfnMibgXXtYXYyXAHJ4WsjRsLL4WAP6pLGU/uXjr5ZJ5I/39T9YUzUaQA==", "signatures": [{"sig": "MEUCIBCjXZm7YEyeyQDcl4rIu4y/qV+QmbNy6iTUxvZfvS0rAiEAzrXTLi+RjMThhY9ROoCtgK5cmRlPv4S5V3bPiDfGY74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkS6npACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Pg/5AZjAKU2bsA+l97SlybwzgAj0PAUMsjEFRHoRar7faAU/WEBV\r\nsef8UQkI2V9C4Geiwd7ZkktMHqCI/QaSrg7W1dhPsKEBBP9b+IB6TNITPWZ/\r\ncanQJmkTV9DVZ1EpbZJNU89Fpm8QoflWrwQSxb+cmhT1+HbXlvmy1jRc0s8y\r\n4LKJ5/pkNwDJiNtzgp9149lGUgLnyfkWZ3b50fuUTT9qtINz5JoFWcxnGTK/\r\nHYVuNu6O4VaubSXTWbl6wsSkOyHnTdcNOWujKeIgDrNdNDzBoqYvmWQKsuMN\r\nQvscOBCIXKzBi8E/2LGN1loEUzWg1Jm5Oj5Sir15MKe0DcoEUAoazo3P9Y3U\r\nu4LFfNK7BJYsTaaPatIjtDsN3a3h1rZSK1lZ2W4NI29kKcOjDsjGStS/gBfs\r\nEpp3Mhs8OURF/ho15FFpF4UgILv8OcD7BxcvbADLFzcuIaRs9YLA/WwS12Vo\r\nM/8nKmFfZfOx+r6ZX3MSF/D5TkGv5I3gAgWKsNcvoCd/mohxYxyVyFGNB+ty\r\nhYwT1ZtEU2vCxVBwHw5hmlb8Ve+vWJQvAsERr3/jYybccMOT+SahvqvOr9b0\r\n37pfwfdZz8e/p2lVcKbJCM2X+Z4B+W7CTNuvqr+oQpET1p90hgsI3w8M9jTA\r\nprtquly5X22Ywt2LonBwueXECDcwA16GzX4=\r\n=d+AA\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc03": {"name": "embla-carousel-react", "version": "8.0.0-rc03", "dependencies": {"embla-carousel": "8.0.0-rc03", "embla-carousel-reactive-utils": "8.0.0-rc03"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "92a5388fe9364974a9eafe0fb9153b88a6457368", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc03.tgz", "fileCount": 9, "integrity": "sha512-PxxBdAUlUgPXqeoO7JRuG2l/axXBzSxjz6eYRw3ifzGW0maIBWkdVB5lLC8JSFmCDXI8R3ZaQuL5d7KFvJOkBw==", "signatures": [{"sig": "MEUCIQDrNoTBLAgq6pv+doS7JzJbB0A5K6z0ucB0stAv9BUBMAIgYAMzS6pQNmNerW2TOghewONBc+mGcXUs+t22ANo4w8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55766, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUj7xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1wg/9Hhb0v2M6cTlBSCcUTk47ABkIjNvRHq4zq7kESakTP5S/D+5k\r\nBxa90GsaTB14GLejMJ0GMLG/+DaLvzKOknoA9hZOqfOjporzt1rwofrGwavt\r\nNzlMR/4QQ04R8o91Lf83S6Da42R22eCN8y2nus6Bii0iWDqKPCjY/IL7ezMQ\r\nmNwmnQir6C5QCE4dKY6dr28oMnr8LQRromxWjXlKeoWamCluTQKRi8roDWhx\r\npkTTyWP1xsOlU/EGWJh5ksRh0lWEh9dwg3T38AXEpnozSSV54pnogp+jaimw\r\nNw1/r4mnss5iUJrKm3rFhgjxF/wmY7x1D2YiOoLf+tgkp01hduBckvkU3yix\r\nlN8KxGYxf7DWW9oiBBHlPCBw5n9Vs29bSnpOFGJkQj2OQiAqOk822wWDsrEi\r\nPngiAgFWbgJazsPi76etl8LESkWhMNLIoVQrIWCiW6GyZ8j1tSR3HREIIexh\r\nQP+Ph8YUbbwDk/FPosqU0LS8JZWMh6R2ibrSU45Wsma2l+jQ6zvusKrdUZtq\r\n8Xkr2mxqSChvnTjhMFQCgIRS0Ws01GEN+fCbtgcrbm/2pPSZ4PefQ+/NHroa\r\nTtzHAiM31M49T31EEg05wqfVpFBsNDaunl7TfEPEHP0HmRvTxgtQf24VBO2c\r\nPGeFdeQ+ARbrVr5UIWJTkJspzR2rIMJXaok=\r\n=Mb2V\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc04": {"name": "embla-carousel-react", "version": "8.0.0-rc04", "dependencies": {"embla-carousel": "8.0.0-rc04", "embla-carousel-reactive-utils": "8.0.0-rc04"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "1e610f7cefffddd0176ddc21bff1cd1ad308925c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc04.tgz", "fileCount": 9, "integrity": "sha512-dVr8kHkjizjRtxaN2HZU4uOEOgO32RorWPGKLHQxh641eU5e/CkYE8S5g0gkLKQIhivNtbBRJfTr4Oj9P9GupA==", "signatures": [{"sig": "MEYCIQC6RcjdpWm543EqmwUtp5HpWyj3icaF+Q5LV64bP10hNwIhAOBNAS9Mf/OwTR7oVmGAv6pcuvKXa2U731PofVtxHWMF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25877}}, "8.0.0-rc05": {"name": "embla-carousel-react", "version": "8.0.0-rc05", "dependencies": {"embla-carousel": "8.0.0-rc05", "embla-carousel-reactive-utils": "8.0.0-rc05"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "ef0e925070b5c5a95a795d9ad2610cdf93060fe1", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc05.tgz", "fileCount": 9, "integrity": "sha512-4N6RSLUdgWhS3aTUv7T50yJ8uBcVQ+MwOqWL3lv1NJiWPPZ0fJHrvQieaerHZsJrdXf73fWGYqJlwuyVzqlvHQ==", "signatures": [{"sig": "MEQCIC02t8FA1nGm0GlxBYokHFXH+rzyOiSCLuwMGJ185aKLAiAGyEhzjvh55fsKwWDJ5tJoTrbo6XHeAdERGI4qIgtong==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25912}}, "8.0.0-rc06": {"name": "embla-carousel-react", "version": "8.0.0-rc06", "dependencies": {"embla-carousel": "8.0.0-rc06", "embla-carousel-reactive-utils": "8.0.0-rc06"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "4e0b92504ba35491fc57184b2ec097d2677fdb1e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc06.tgz", "fileCount": 9, "integrity": "sha512-aZfVqTptCA6uq3VLPFXLKGSkxVdoiNjQa1sjVOJc+9FL6pbwImaVR0oL7jA0kfW9l67XoQOEOq1gRiImbYr/Ew==", "signatures": [{"sig": "MEUCIQDY+o0HXbSfxuJz3S4zypcHY2EZQ5J0O673oymCnteBUAIgU9BJGQfkTkDEoi0ccK4sypHyOaZa/AMmba42Gq7/ZHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25902}}, "8.0.0-rc07": {"name": "embla-carousel-react", "version": "8.0.0-rc07", "dependencies": {"embla-carousel": "8.0.0-rc07", "embla-carousel-reactive-utils": "8.0.0-rc07"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "526aacb736d4695dbfdeeeaeb3d87583973699f0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc07.tgz", "fileCount": 9, "integrity": "sha512-8JX2kIMTbqHvsG6O0XRUHdZJ/66kyKCwFwFVZXZ1YpdrdMlrMHBNyIQP+ETBWgOsNtpbcBeWyZx29YtzCVLgJA==", "signatures": [{"sig": "MEUCIQD4LughEWeHr+XVqSaLyl4fiPMD8TpZ0uiBwnIj5L8xsQIgDSX46fVV9dhUtFAMPmDLsWsDGnwjLjHvwk+HEGzMb+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25939}}, "8.0.0-rc08": {"name": "embla-carousel-react", "version": "8.0.0-rc08", "dependencies": {"embla-carousel": "8.0.0-rc08", "embla-carousel-reactive-utils": "8.0.0-rc08"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "494de55dc9168d31dab276c70aa0c1d01ad3d81e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc08.tgz", "fileCount": 9, "integrity": "sha512-OdqTJrus5NEPBgO2YX+ueKZ8Kbqb6EswcyaJxbEV0lbjGQusY7AKNe5kbgDj7c7nma5wuvR93NzpC98DnhmDpQ==", "signatures": [{"sig": "MEQCIHL6C5tF7uN4vBKql8nYu/OFq89o4lXv6RglGMmFm+rDAiA/pZ9dxsKysxJxn5sbF313H/EKaKb5aex18i5VYFUSjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25866}}, "8.0.0-rc09": {"name": "embla-carousel-react", "version": "8.0.0-rc09", "dependencies": {"embla-carousel": "8.0.0-rc09", "embla-carousel-reactive-utils": "8.0.0-rc09"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "fad0566e7fe69d4c0a012cec11429f4e8762cf79", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc09.tgz", "fileCount": 9, "integrity": "sha512-BmAq531HLnwZUSuEcwAT11nmrt5D3EVyuOR+wbR55vtTgNeEl+iPwvdhve6SRQaevJ3zb3/Vd2ddxHcyoSHRJA==", "signatures": [{"sig": "MEQCIFFne27FXAyoAznWxF6tpDwJ+wzD0SB0+i86bfNHPBmmAiADWEtU/CNUOfxFIrQQWzp0S1re5Y/+L0pIpIYGEQ+LXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25918}}, "8.0.0-rc10": {"name": "embla-carousel-react", "version": "8.0.0-rc10", "dependencies": {"embla-carousel": "8.0.0-rc10", "embla-carousel-reactive-utils": "8.0.0-rc10"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "2abbaa111daae2b4012172e3e52fe6fe9a1a5786", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc10.tgz", "fileCount": 9, "integrity": "sha512-zCwPa/hAOh2iq8m7x+0VgAnWINU8WHMdUjMjJg7xP1KxFU2VwgiU/4KLSneEQXczgHk1NZbqJTTWdlqKuwfyww==", "signatures": [{"sig": "MEUCIQD4ybn5jUIr3KyfYKo0QvOVp8QXefpmdz+3GSmEFGOtFAIgEpJYixLMLObDba87cf3hTWEP+A8W74glCbyllDObAEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26212}}, "8.0.0-rc11": {"name": "embla-carousel-react", "version": "8.0.0-rc11", "dependencies": {"embla-carousel": "8.0.0-rc11", "embla-carousel-reactive-utils": "8.0.0-rc11"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "0e2fde5cafa3cae9c30721e18aee648599527994", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc11.tgz", "fileCount": 9, "integrity": "sha512-hXOAUMOIa0GF5BtdTTqBuKcjgU+ipul6thTCXOZttqnu2c6VS3SIzUUT+onIIEw+AptzKJcPwGcoAByAGa9eJw==", "signatures": [{"sig": "MEUCIQCd2NcN05rrNZ6bAhLxVPMm6GANRfaaGcXVx5KeDG+HLwIgXbkNsuW8CXx/v2VP032LrjyHaPLvude/WSy67HIKs7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26175}}, "8.0.0-rc12": {"name": "embla-carousel-react", "version": "8.0.0-rc12", "dependencies": {"embla-carousel": "8.0.0-rc12", "embla-carousel-reactive-utils": "8.0.0-rc12"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "2577ddd26576507e96f8b9c558342fd1ed121f3a", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc12.tgz", "fileCount": 9, "integrity": "sha512-Tyd9TNH9i8bb/0S9/WZsmEvfZm8jlFU9sgaWNNgLzbPsUtz/L6UTYuRGOBDOt2oh6VPhaL1G8vRuOAuH81G5Cg==", "signatures": [{"sig": "MEUCIERbildd2RTCW15FPFhxIZhXuyyLDulZW0IcrpdEBjLiAiEAp/TxFKUD3UvyJ2VWDJB0BVE77Npyv3yOYrKnG8v9Sto=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33701}}, "8.0.0-rc13": {"name": "embla-carousel-react", "version": "8.0.0-rc13", "dependencies": {"embla-carousel": "8.0.0-rc13", "embla-carousel-reactive-utils": "8.0.0-rc13"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "faf388744d8261b0dda0dfe33bf3cd4b84ac891e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc13.tgz", "fileCount": 9, "integrity": "sha512-Yq8TXVtTTPIF5dmLzedamS2h+gDEY56x1kp6W4g+phL7DyLV2KJKHCUvfeRlVsbG90Q0if27IIUUNKKNLfN7mQ==", "signatures": [{"sig": "MEYCIQD4YlPLVZkqkYWhvjODnd6Z9Nk4xqgqU34NTj5CVV4RkAIhAN8qZpNJvkVXvsxoyfu7BFVXU9mxJOriEmXv1YVmeuDz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33898}}, "8.0.0-rc14": {"name": "embla-carousel-react", "version": "8.0.0-rc14", "dependencies": {"embla-carousel": "8.0.0-rc14", "embla-carousel-reactive-utils": "8.0.0-rc14"}, "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "@types/react": "^18.0.8", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "ed5d9e641134fdc7e060a9188b0ccf13f7677da1", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc14.tgz", "fileCount": 9, "integrity": "sha512-2b9vXACEcn0qja4QyaFMfCgFbFhumV3krOCGr9+jlQiuXt5z/EyfiYYziDsm70DhTtxtg/uKEGflIqZSfWRYKg==", "signatures": [{"sig": "MEYCIQDUqw4YpIzvIF1V8qZvjJRR/xW7iPULR3tjluGJIHrIQwIhALiHmpuc8cVlWClhcPL/fhnCv4JcQmtd0tP/0oyzhILY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33905}}, "8.0.0-rc15": {"name": "embla-carousel-react", "version": "8.0.0-rc15", "dependencies": {"embla-carousel": "8.0.0-rc15", "embla-carousel-reactive-utils": "8.0.0-rc15"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "1f5528256896f5b9a8a49c743d56a522519d47e7", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc15.tgz", "fileCount": 15, "integrity": "sha512-PePOkyPMWsUDNJKYRhUmGScYhex9mfpEiYiKT8OgwP/4K60plW0qk8cAXWvS9N61A/3RkUq7uz+hQsmhAtYMcA==", "signatures": [{"sig": "MEUCICGT9wyJJjzk7/bADr56ix6//SFIX/VzSTWEh06Q5spPAiEAnl/SdwNrJvk7Lovud/8aFNXZxrxOmcQPMZ2HjJYBdpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46427}}, "8.0.0-rc16": {"name": "embla-carousel-react", "version": "8.0.0-rc16", "dependencies": {"embla-carousel": "8.0.0-rc16", "embla-carousel-reactive-utils": "8.0.0-rc16"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "8576064d5c657a0ab9906c066c1d3de7f52fd7f8", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc16.tgz", "fileCount": 15, "integrity": "sha512-T9FIa10lRWXUerdgTsPPPoC2NwR/Wt3lhSsKrZ80kyEM9AbySeqgwqzzaUrYO3FjoUUVg1IQ4VANnEOUGZNBTw==", "signatures": [{"sig": "MEYCIQDprr4UbpQuqMYJgGe+iIZI76zRZkQdmDs8JqU7DpsxCwIhAM89RAz4k9Z9o6RDwRYaywCp2kpk6S+cXu3aEA5ErPoV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45841}}, "8.0.0-rc17": {"name": "embla-carousel-react", "version": "8.0.0-rc17", "dependencies": {"embla-carousel": "8.0.0-rc17", "embla-carousel-reactive-utils": "8.0.0-rc17"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "277d4212eb95b6a29898b65660efe60d2075aa3a", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc17.tgz", "fileCount": 15, "integrity": "sha512-x4aFprwFB+PQO9EsHHZsrDxARb0uYNBYn9mr5oDFdBdPez4M8G1r5yidWbUcT9pNUc8AQXC9sGzlfauBfBxVOw==", "signatures": [{"sig": "MEUCIQDFhXw9IlQ8jzz+es+napOZdcLLke0yL9yPVSccYw731AIgP+nJodg9Qq97LIiNETdy/0+35vv29zD0xsHP5dLrMFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45841}}, "8.0.0-rc18": {"name": "embla-carousel-react", "version": "8.0.0-rc18", "dependencies": {"embla-carousel": "8.0.0-rc18", "embla-carousel-reactive-utils": "8.0.0-rc18"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "feb36c3798aec936df7b83abaf30029ed0cef2a0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc18.tgz", "fileCount": 15, "integrity": "sha512-sif+zkOnQIJzZJJQdtE/kDBrlxNt7Vm6zI8i17qT6QYv3dnazPVf4EEfXQ2CZdh7J3UPwnIMS3Q55E1a8uxJ/g==", "signatures": [{"sig": "MEYCIQDDlaZJ6rND9NFIzE1CCuYWHvOBukW+/YLeXeCu8n9xEgIhAKdJ+1K3ds6kzdrp8DiXI6K/DC3iHIr/hmk89feeuJPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45673}}, "8.0.0-rc19": {"name": "embla-carousel-react", "version": "8.0.0-rc19", "dependencies": {"embla-carousel": "8.0.0-rc19", "embla-carousel-reactive-utils": "8.0.0-rc19"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "50a9c1206c996bf83c27dc9f081105bbe6ea552e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc19.tgz", "fileCount": 15, "integrity": "sha512-4BBj1HvlUqhWXFyDJOL/JbQ74jtekfdH646B1wQzM9QmWn6CEcbD/SmovKqc6B5jYTKaaGEGEEw7bpUJRajA8w==", "signatures": [{"sig": "MEUCIQCj2d7Vb9di3HLYI2SXUlqLO0fNAnXl+ctyWdpoagn5mwIgEUfzhGCXU4sKyYalbPxDiyKvRPx78nWTHxyBljtNBPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46337}}, "8.0.0-rc20": {"name": "embla-carousel-react", "version": "8.0.0-rc20", "dependencies": {"embla-carousel": "8.0.0-rc20", "embla-carousel-reactive-utils": "8.0.0-rc20"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "0756fde4791b0f727dd90383c3cf798c17cdb539", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc20.tgz", "fileCount": 15, "integrity": "sha512-02xhtl/qd5VQtzRbG3jQKVXy/YzP4J3nxQcJhz7cIY73nK3aPwxoZL+Fjk0VdS5eUIWowRBH5qIv3nVNsqeYZQ==", "signatures": [{"sig": "MEUCIGm1mGBrxcYE6+hD0wxnrguy/z/CjuIYhRxlAzqGY7vzAiEAmwSKVCU5hOB6Oa1QpdfF8z5ey/XJcPOX/2Xnz3u9l48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47469}}, "8.0.0-rc21": {"name": "embla-carousel-react", "version": "8.0.0-rc21", "dependencies": {"embla-carousel": "8.0.0-rc21", "embla-carousel-reactive-utils": "8.0.0-rc21"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "10eb8b1b0c829a8d292493693ab9be75678e0e26", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc21.tgz", "fileCount": 15, "integrity": "sha512-DOa9hgF/T1fwb8D3rZ8FFMceY3aDXtbluZwzZYMLnN2Dqn0IBLN0l97o3obkMxI9Zzog0u1WMM6HE7AGF9SjEg==", "signatures": [{"sig": "MEQCIFSjbRQNobmeiptbEf6RJnV4C8gGb1V3+g//1+Rxk5dUAiBbvRt/hKEMd3M7Ln//dnWK2der+4Qknv9dNB8lny8xrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47470}}, "8.0.0-rc22": {"name": "embla-carousel-react", "version": "8.0.0-rc22", "dependencies": {"embla-carousel": "8.0.0-rc22", "embla-carousel-reactive-utils": "8.0.0-rc22"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "cc017e5c370663a9517fa46fba55ea7ad1df5e17", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc22.tgz", "fileCount": 15, "integrity": "sha512-NwmISV0Cw9XVo76Vquz3hJaeZe7qoCRtrzStxlEY7qfZD8WR/f4JlQAso35URTs1BeYVhcuClflelioo+Zmidg==", "signatures": [{"sig": "MEYCIQDt3M/QS/U0NXTCm4tx/nfhibnB+DwYi7ptWQLcLWNsBwIhANKcQ5qWi9cqSgVXT7gogirRMTVtVCfVe5BCu/oSeegW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47470}}, "8.0.0-rc23": {"name": "embla-carousel-react", "version": "8.0.0-rc23", "dependencies": {"embla-carousel": "8.0.0-rc23", "embla-carousel-reactive-utils": "8.0.0-rc23"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "bd1760f6c0b333fd68d50bf5d9617b8b72dd0dd4", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0-rc23.tgz", "fileCount": 15, "integrity": "sha512-EmtIx4oYkBAUi9R31Tg1lh2HCw0Q01bOftXRDhIlNfB+gsDRS76AgeYU+mQc9qW6yeI5C/W5BqtPZU+ymR0E2Q==", "signatures": [{"sig": "MEUCICHl2zSkn4RJL/ng3HQISr7XDwkoBKCqrNkxlnblvfUGAiEA5h3XMC6QN0OUWIoV9NRM0g3Sj/eLY6maUU1gdpXkcb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47481}}, "8.0.0": {"name": "embla-carousel-react", "version": "8.0.0", "dependencies": {"embla-carousel": "8.0.0", "embla-carousel-reactive-utils": "8.0.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "73abd0a30c2faa37532ae3c4c0b484867e066d5f", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.0.tgz", "fileCount": 15, "integrity": "sha512-qT0dii8ZwoCtEIBE6ogjqU2+5IwnGfdt2teKjCzW88JRErflhlCpz8KjWnW8xoRZOP8g0clRtsMEFoAgS/elfA==", "signatures": [{"sig": "MEQCIGE/KAVs4MQth6VE0M9jyUnOHRVKqUd1Qgk/A0FPwfwjAiAqwjAGNk3oz3WC6XuYrGFWvf4mXhyKerI3oWI8cg7J1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47351}}, "8.0.1": {"name": "embla-carousel-react", "version": "8.0.1", "dependencies": {"embla-carousel": "8.0.1", "embla-carousel-reactive-utils": "8.0.1"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "6c0420e54079a3f47fad1b4e982be782066f5b2c", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.1.tgz", "fileCount": 15, "integrity": "sha512-cpFQ/HwCsjBjzpu9Z9IHmZ9DaCSf/wo4q+qUTcRW3SsNv+1Q8IY7Y8J2QIyTmz0vOWY7tliu3uE2gqRH7ZDwOQ==", "signatures": [{"sig": "MEUCIQDDL1mD1TX91r4gRFQ7HhRwEGJPzvuG85OO+77PMmlSEAIgKii7pfh+XsxAYdvDCuE2R/yqibAWc1hwlqWVbRQu8JE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47251}}, "8.0.2": {"name": "embla-carousel-react", "version": "8.0.2", "dependencies": {"embla-carousel": "8.0.2", "embla-carousel-reactive-utils": "8.0.2"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "8f27b34c04aa9fccdd6059727f573d9e7ed63d27", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.2.tgz", "fileCount": 15, "integrity": "sha512-RHe1GKLulOW8EDN+cJgbFbVVfRXcaLT2/89dyVw3ONGgVpZjD19wB87I1LUZ1aCzOSrTccx0PFSQanK4OOfGPA==", "signatures": [{"sig": "MEUCICxcD9fSpVHj+sRkCtslr59DWWU02eIpYf0J08eacXCqAiEAl47mrjLiUh32JCP6TV2tJPAgBPW6i5NA28r508DX8Ls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48189}}, "8.0.3": {"name": "embla-carousel-react", "version": "8.0.3", "dependencies": {"embla-carousel": "8.0.3", "embla-carousel-reactive-utils": "8.0.3"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "a3610b73ee64b98b34a4145d56c1bd5a2575d8b0", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.3.tgz", "fileCount": 15, "integrity": "sha512-kWRqu6S9FV7K1mKNBNmMiU8ZXLAOXHy352F6/3T8rF5/fV/pnKF/X5i5gRWvOoPT/tbvC3xQi3vAvJZ+SxsIDw==", "signatures": [{"sig": "MEUCIAUgTPC4ur/TbQRyupjNbpEK3sS+VVaKfXnSXuqRhI2rAiEAx5Dp84zfEhcYHrOIOtsCnIav/MRB7QzGDMi8gJJEMGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48573}}, "8.0.4": {"name": "embla-carousel-react", "version": "8.0.4", "dependencies": {"embla-carousel": "8.0.4", "embla-carousel-reactive-utils": "8.0.4"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "3c0fa1a73bacc92e1e3d84eedfeda3b1962e524a", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.0.4.tgz", "fileCount": 15, "integrity": "sha512-3NlOmBmRhaGhtI0It9243yg9fz2wPRML5gGLDIE69Oyb/CuGsJnxpcgG74j905NQBDgReTEfFJwzOtIFAjeBfg==", "signatures": [{"sig": "MEUCIFR3SEe23f+NdPhVgSa6RDI8S1CB1xRVd97x0X26q3kfAiEA/cYQ1Zfecp3rpEY8EeQGm43wnSs1GQIq8f3CnoqN5WA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48617}}, "8.1.0": {"name": "embla-carousel-react", "version": "8.1.0", "dependencies": {"embla-carousel": "8.1.0", "embla-carousel-reactive-utils": "8.1.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "26951cdde010f81181865c8f43f6b8417cae0a8f", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.0.tgz", "fileCount": 15, "integrity": "sha512-sSqTG+mz+nnzOGbbjINKlv+2UBrS213dGbUL32x/kkLRRlzqGDxe3dcJzaNBZlg3d8IyEvgcrI9trQwXBCNIug==", "signatures": [{"sig": "MEUCIQDEi9ZyzRiFkCBhA8o5yp3piKNwsjS9ryeosfH8UujhVwIgHIokupJKgJggcaFdg3QilRLRSQLdt3BZ7LZCQbJvpco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48668}}, "8.1.1": {"name": "embla-carousel-react", "version": "8.1.1", "dependencies": {"embla-carousel": "8.1.1", "embla-carousel-reactive-utils": "8.1.1"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "4b9a12ef2db5d8dd1da951055e320078e6688d34", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.1.tgz", "fileCount": 15, "integrity": "sha512-VuIrNyGneFKlDzAvkRGeXjEOw+FW2Ob76R/Y+kdSuKj68bnaAJCEf8PlHtJJWle1/rR/Sfv9NTIa00zlA8iXgw==", "signatures": [{"sig": "MEYCIQDrRnKE0pyxd/Q1n/7u3sgt1duNtbaEpNsP89S1ELhpYwIhAPvrPJeRphyGyxFZQN6PRF6gXlmOuyJtS27GXH1u0Q4e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48668}}, "8.1.2": {"name": "embla-carousel-react", "version": "8.1.2", "dependencies": {"embla-carousel": "8.1.2", "embla-carousel-reactive-utils": "8.1.2"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "f9852d7c873e2e3c107d2ddbca237b8433264218", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.2.tgz", "fileCount": 15, "integrity": "sha512-gDJ82bdP3VujDpnfX+HyDcIiq5IDArnBCu9Y5nRZoGpon3UcTeDnQdYBrj/n8yluQnkYdXWCKdfPlduJgf1U/w==", "signatures": [{"sig": "MEUCIFGMx4/ziqnjH8W+fvNvub4ymgsg/k+ye+zHhUx8KefWAiEA7eDZxgHw4UVLcT5wMrgaGiCB6F5h55jQ6j1Yhyz9cDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48668}}, "8.1.3": {"name": "embla-carousel-react", "version": "8.1.3", "dependencies": {"embla-carousel": "8.1.3", "embla-carousel-reactive-utils": "8.1.3"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "a07770eee003a56dfe2fa55304b48892cc00c013", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.3.tgz", "fileCount": 15, "integrity": "sha512-YrezDPgxPDKa+OKMhSrwuPEU2OgF5147vFW473EWT3bx9DETV3W/RyWTxq0/2pf3M4VXkjqFNbS/W1xM8lTaVg==", "signatures": [{"sig": "MEUCIQDsMwac6svxVBoADevzFGF9Z1PZ13exYknQ3QLVTnUHXwIgXvPh8SmXEAUKfzLSGn4Ex7meeh3FoUFIHjCoJ8pmwTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48668}}, "8.1.4": {"name": "embla-carousel-react", "version": "8.1.4", "dependencies": {"embla-carousel": "8.1.4", "embla-carousel-reactive-utils": "8.1.4"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "23bcb39405e03f63656eeeb7d531a6b32040a071", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.4.tgz", "fileCount": 15, "integrity": "sha512-0llfX4HlXrdgJKxlZ8q8LyI6EV+TvOI7KtZx9GkDTwdgP6/AyyuvzO8KYvDUNZOnbFBAPbVEe2qEgGMLEinlYw==", "signatures": [{"sig": "MEQCIClo+rjhHCF0Gv6g5QCtfVcH/HTeggECWDD5Ydu22CKoAiBAG2uxwOLqA1GcUEqklLxDr5DOn/sGuK9QP3jMsI443Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48764}}, "8.1.5": {"name": "embla-carousel-react", "version": "8.1.5", "dependencies": {"embla-carousel": "8.1.5", "embla-carousel-reactive-utils": "8.1.5"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "64867a2812c0c5beb7fe215651ce1f8d9f4855d5", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.5.tgz", "fileCount": 15, "integrity": "sha512-xFmfxgJd7mpWDHQ4iyK1Qs+5BTTwu4bkn+mSROKiUH9nKpPHTeilQ+rpeQDCHRrAPeshD67aBk0/p6FxWxXsng==", "signatures": [{"sig": "MEYCIQCcqmbRpUV5cfZjjLIDuESRVP33BOd9dbRDlBK4wZidNwIhAImLBAxq1N604qIpjWtcRwMT+OKVibMEBn2JzTciJTnr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48746}}, "8.1.6": {"name": "embla-carousel-react", "version": "8.1.6", "dependencies": {"embla-carousel": "8.1.6", "embla-carousel-reactive-utils": "8.1.6"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "4de0cef2888443f4203408df73af2707e5c961e9", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.6.tgz", "fileCount": 15, "integrity": "sha512-DHxwFzF63yVrU95Eo58E9Xr5b6Y9ul6TTsqb/rtwMi+jXudAmIqN1i9iBxQ73i8jKuUVxll/ziNYMmnWvrdQJQ==", "signatures": [{"sig": "MEUCIHSlGe4QvgxI8d94qdSsbMiRrOpu9jg60+GZexAyLCCPAiEAgbg3lhB3xt3tp6S/OMee0oAz5ZPP5TTfAPZ/fe1z/t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48490}}, "8.1.7": {"name": "embla-carousel-react", "version": "8.1.7", "dependencies": {"embla-carousel": "8.1.7", "embla-carousel-reactive-utils": "8.1.7"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "28b0a3036baad410a9828473656f32f1cd741266", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.7.tgz", "fileCount": 15, "integrity": "sha512-ermMKzQ46LhXE4f81VBCVGxCJWvZfsu504dkyiUDO+cnEEPW8NlC2PpKULmiOWugusYWRLhQLjmyQs3b8vvOjA==", "signatures": [{"sig": "MEUCIQC7DbjEbhM9SZiRn8lX9r+4X3aWu72TUhbSxHJlnO/8uQIgbAc6LseQ7ILy9H0zOlaSSf+oDdUyLsDSTBnQePNPr/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49011}}, "8.1.8": {"name": "embla-carousel-react", "version": "8.1.8", "dependencies": {"embla-carousel": "8.1.8", "embla-carousel-reactive-utils": "8.1.8"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "ccca22c12d97407f12c3dd8f6fafae6e82e4c578", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.1.8.tgz", "fileCount": 15, "integrity": "sha512-b8DcmC+j1vqVWSM6rU/GYGyY6Kp9LX8OoikZPBKmV6qL8s94sSPGl6jtDLLUtV8TTIQGMYOlOKUgoMAt/0TwOQ==", "signatures": [{"sig": "MEQCIEk63RFQwgVeBvaoLyhgykfDpxVCWoko9vwj1dKucpgxAiB2OU04vdJLS+O9hYyvUrSePmo2AzppBk64KLDaE+M2mQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49316}}, "8.2.0": {"name": "embla-carousel-react", "version": "8.2.0", "dependencies": {"embla-carousel": "8.2.0", "embla-carousel-reactive-utils": "8.2.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "4214ce960e2691575dff6cebf9c94ac573fdc569", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.2.0.tgz", "fileCount": 15, "integrity": "sha512-dWqbmaEBQjeAcy/EKrcAX37beVr0ubXuHPuLZkx27z58V1FIvRbbMb4/c3cLZx0PAv/ofngX2QFrwUB+62SPnw==", "signatures": [{"sig": "MEUCIQDq7fh5y7C9232/nNVIKjyAn71u8IJY924+BMyy06oj8gIgLkm+ydY4hb0DT9WnhGJoJtjBQ4Hfp7HHwXViDJGfoq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49410}}, "8.2.1": {"name": "embla-carousel-react", "version": "8.2.1", "dependencies": {"embla-carousel": "8.2.1", "embla-carousel-reactive-utils": "8.2.1"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "0202bd6b04f749cf9a56ad86f4549f75b7bb43bb", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.2.1.tgz", "fileCount": 15, "integrity": "sha512-YKtARk101mp00Zb6UAFkkvK+5XRo92LAtO9xLFeDnQ/XU9DqFhKnRy1CedRRj0/RSk6MTFDx3MqOQue3gJj9DA==", "signatures": [{"sig": "MEQCIFBvc2wFQ15pSxgrR+nHl9fBBWJ5uLiNzMFmk1CqRPrsAiAFrIdbbMDLrqLVrDRBJU7cfxGQxJwJL0zpOWvri0I1AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49709}}, "8.3.0": {"name": "embla-carousel-react", "version": "8.3.0", "dependencies": {"embla-carousel": "8.3.0", "embla-carousel-reactive-utils": "8.3.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0"}, "dist": {"shasum": "8aa6b9b77c3e900349a7215cb31b7ead6a84a715", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.3.0.tgz", "fileCount": 15, "integrity": "sha512-P1FlinFDcIvggcErRjNuVqnUR8anyo8vLMIH8Rthgofw7Nj8qTguCa2QjFAbzxAUTQTPNNjNL7yt0BGGinVdFw==", "signatures": [{"sig": "MEQCIF3l1/XefazHTjuXeVccYXqZVjeyn8FLv8tEoR3Zol/PAiBEixqrOEgtHlQurP/3AcsK4cwRMf1Gd1LS+ZuDD9kHww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49907}}, "8.3.1": {"name": "embla-carousel-react", "version": "8.3.1", "dependencies": {"embla-carousel": "8.3.1", "embla-carousel-reactive-utils": "8.3.1"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "f6d91f484b00704411524cf01feb80fdcff7dde2", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.3.1.tgz", "fileCount": 15, "integrity": "sha512-gBY0zM+2ASvKFwRpTIOn2SLifFqOKKap9R/y0iCpJWS3bc8OHVEn2gAThGYl2uq0N+hu9aBiswffL++OYZOmDQ==", "signatures": [{"sig": "MEUCIQDwRMp0HglEFMRxmV/jMQs1n6dGBMw91N+gosGhAySqnQIgROH+GAtZTLdsI9WvY3uup1KkrUoX7g0q/jJK5EVFq+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49948}}, "8.4.0": {"name": "embla-carousel-react", "version": "8.4.0", "dependencies": {"embla-carousel": "8.4.0", "embla-carousel-reactive-utils": "8.4.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "d817d16f290277d3db74e2fa3377c0d3010b9370", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.4.0.tgz", "fileCount": 15, "integrity": "sha512-r5UszqLnIIhfXfGzTlfEAYZ310H4gIiLvBsNQ9dnHY6T2ooMsZs+k5vpJAYjkFtUSaQJ1xZxSGwIV6+IoA8QEw==", "signatures": [{"sig": "MEUCIHmu05Muw5MjzYhxAW1z52Cng16XrWnLtuUNRdLdhFecAiEAoBdui3OKmOlhhOl9BszVriAo8UusuFBpGCoZ6NNcaVk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50282}}, "8.5.0": {"name": "embla-carousel-react", "version": "8.5.0", "dependencies": {"embla-carousel": "8.5.0", "embla-carousel-reactive-utils": "8.5.0"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "835ffe30c64532498fee27c5db0fcfbbc6fbaf6e", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.0.tgz", "fileCount": 15, "integrity": "sha512-Yj2FYTlaK3eKN0stNdzgwOFImksJA2u6nQwb8uQ5MWfQnf74M0Kfi/lwTBwevStECWjP4CG0bxx/XD66oaISzw==", "signatures": [{"sig": "MEUCIG99OwSzKFhoAZNYeh68mdyylMUI0406m4gx3IqEMYU6AiEAnIWkJ2w3sPZsDw9efY2w6QL9GDjJJdLnCzEd8sGDRe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50282}}, "8.5.1": {"name": "embla-carousel-react", "version": "8.5.1", "dependencies": {"embla-carousel": "8.5.1", "embla-carousel-reactive-utils": "8.5.1"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "e06ff28cb53698d453ffad89423c23d725e9b010", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.1.tgz", "fileCount": 15, "integrity": "sha512-z9Y0K84BJvhChXgqn2CFYbfEi6AwEr+FFVVKm/MqbTQ2zIzO1VQri6w67LcfpVF0AjbhwVMywDZqY4alYkjW5w==", "signatures": [{"sig": "MEUCIH57GFVT4XCj7yh//3oxHJ5NHWglrlff/h923rF/WyljAiEApDXG9V3Q7e5cr2pzVQj6z+TkKOl8ej7cDbzpVYlMl9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50211}}, "8.5.2": {"name": "embla-carousel-react", "version": "8.5.2", "dependencies": {"embla-carousel": "8.5.2", "embla-carousel-reactive-utils": "8.5.2"}, "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "@types/react": "^18.0.8", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "f79f6c36690596fe2aceec994372ab84bfbfd9cc", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.5.2.tgz", "fileCount": 15, "integrity": "sha512-Tmx+uY3MqseIGdwp0ScyUuxpBgx5jX1f7od4Cm5mDwg/dptEiTKf9xp6tw0lZN2VA9JbnVMl/aikmbc53c6QFA==", "signatures": [{"sig": "MEUCIAJsGOfDbIsy0t6YPOfBZFllOqc0TsCEgOzG2ulP7mUhAiEAukovCaKLjbVRHpu1s6mIk2TIiQkeY9t/Mwk9TLGJ5vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50429}}, "8.6.0": {"name": "embla-carousel-react", "version": "8.6.0", "dependencies": {"embla-carousel": "8.6.0", "embla-carousel-reactive-utils": "8.6.0"}, "devDependencies": {"@types/jest": "^29.5.6", "@types/react": "^18.0.8", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "2.8.8", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"shasum": "b737042a32761c38d6614593653b3ac619477bd1", "integrity": "sha512-0/PjqU7geVmo6F734pmPqpyHqiM99olvyecY7zdweCw+6tKEXnrE90pBiBbMMU8s5tICemzpQ3hi5EpxzGW+JA==", "tarball": "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.6.0.tgz", "fileCount": 15, "unpackedSize": 50629, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCUkloTIGKWWJ2NtrDoz5BKBFMjuO0c5NFSDARd+Z+JggIgA3r+zIv4hkn9HPUsRYRG+nWUzjUrz45M9aX/7hs4Hko="}]}}}, "modified": "2025-04-04T17:37:54.132Z", "cachedAt": 1747660588050}