{"name": "@eslint/core", "dist-tags": {"latest": "0.14.0"}, "versions": {"0.1.0": {"name": "@eslint/core", "version": "0.1.0", "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "directories": {"test": "tests"}, "dist": {"shasum": "cf6bf14ce7112cb0af306ba1cea3bc216fa77341", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.1.0.tgz", "fileCount": 4, "integrity": "sha512-TsGFdKFxmTWubuKObLhJJCnn/4jUecSWPnAqkOUoeeBtWv4QSFSkLo7w3GW3xQM52LyoKKHblKoN9+noM0NmTg==", "signatures": [{"sig": "MEUCIGCnHceD0oXRLRvHSoz8IocVbJNB+r19ryAJZvrtnTy9AiEAwB8hFSnXha1V3LYfvN13zESghXqA7rxrJ5cifvMq6Co=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24313}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.0": {"name": "@eslint/core", "version": "0.2.0", "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "directories": {"test": "tests"}, "dist": {"shasum": "be35e5b5e26dcae33b9bab38e7ef49b313b516f0", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.2.0.tgz", "fileCount": 4, "integrity": "sha512-b<PERSON>kucH6uxPOaUeUoGnwFvEz48ooF5QcuDKSN142iYWCocLLJ4k1kC74ftzmFNC+gRpZOpRoPsk34PAarGuYFA==", "signatures": [{"sig": "MEUCIBlb+orcLQQjr0vsFsbmzcYhgya4S7uZplvvhLLLZYngAiEAg1Zz3zzaBf2hQhvUCleyN0+yB/416Uzhs9QDgSiFhkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 25188}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.3.0": {"name": "@eslint/core", "version": "0.3.0", "devDependencies": {"mocha": "^10.4.0", "eslint": "^9.0.0", "typescript": "^5.4.5", "@types/eslint": "^8.56.10"}, "directories": {"test": "tests"}, "dist": {"shasum": "198782123d0132b420a1a52edfb0ab1a39760762", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.3.0.tgz", "fileCount": 4, "integrity": "sha512-GTNr6el9ReTPD2PL3bce6niMijnldAC/v+B0QcnFRuu3Ij3LuUp7nWg2R7kADi+PGDj6QL20FsfbkF94ePV8yA==", "signatures": [{"sig": "MEYCIQDoofpH32Q2zTq2SSs+s/L6LEmsDSp/+0KfLgtnGIJIBgIhALrjpMJ1B/7lcV1bkF/IoDPy7gIJDCU2WwX3Hx3oI18o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 25979}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.4.0": {"name": "@eslint/core", "version": "0.4.0", "devDependencies": {"typescript": "^5.4.5"}, "dist": {"shasum": "a90c2e339018621ff8b191770d74af0a49b27743", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.4.0.tgz", "fileCount": 5, "integrity": "sha512-dJzhhN5oSbpaoFWmfA1iEBTINf7gYyt3ZNjqDm0xw1UMz6QlA1YjcclWhPhGQSR1XbwAIon00y259kaBmjFgNg==", "signatures": [{"sig": "MEYCIQDfli9MkxuO868k7yKdfzmZfUwBz0UpgkAwvjHBXcOwhgIhAK85+eRt+JeYklTmW+YSXsBdgy2g9IHx4cyOWaFM8UrF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.4.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35564}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.5.0": {"name": "@eslint/core", "version": "0.5.0", "devDependencies": {"typescript": "^5.4.5"}, "dist": {"shasum": "590b2d16013a785504924e618d76e5671d6d5b0f", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.5.0.tgz", "fileCount": 5, "integrity": "sha512-d7dQItxTFa0mgeYhU+E1sN28mYYX4Z5FRw0DRszhD8OQr92Tbd00dtSVU/HWj9+Q0iKPQUMtPcExqI/QvkHUow==", "signatures": [{"sig": "MEUCIG+NOg1/az2cjlG1FJIAh1qpTemaJq4jCgr/KmXi6G6bAiEA0wF56AGs5SMZ0MJ2Agy9z6tMSQfczrzLC0fmIUYc4SY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.5.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35700}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.6.0": {"name": "@eslint/core", "version": "0.6.0", "devDependencies": {"typescript": "^5.4.5"}, "dist": {"shasum": "9930b5ba24c406d67a1760e94cdbac616a6eb674", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.6.0.tgz", "fileCount": 5, "integrity": "sha512-8I2Q8ykA4J0x0o7cg67FPVnehcqWTBehu/lmY+bolPFHGjh49YzGBMXTvpqVgEbBdvNCSxj6iFgiIyHzf03lzg==", "signatures": [{"sig": "MEQCIEZtvnbYZbX2Gf9wjj4B0TOS35luCM26AgSHLo9qZ2uwAiBuudcjkxC01P3WVRUE7S+0F7lsUgO25t20K2zgna5LDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.6.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 35970}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.7.0": {"name": "@eslint/core", "version": "0.7.0", "devDependencies": {"typescript": "^5.4.5"}, "dist": {"shasum": "a1bb4b6a4e742a5ff1894b7ee76fbf884ec72bd3", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-xp5Jirz5DyPYlPiKat8jaq0EmYvDXKKpzTbxXMpT9eqlRJkRKIz9AGMdlvYjih+im+QlhWrpvVjl8IPC/lHlUw==", "signatures": [{"sig": "MEUCIQD+dP6iMgUyup99KA3y2WknUG8cQpF/B5WSK2TW5LyY8AIgJE6Eom19T4cBNioh39MsERmLx5vgeKa+KP/33r1YcEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 36264}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.8.0": {"name": "@eslint/core", "version": "0.8.0", "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "54b6591e4799b2c0e363da5b607e3686a6d8d893", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.8.0.tgz", "fileCount": 5, "integrity": "sha512-ncQZoR8YJtXIrBuJo1vDlIIR8+uoyYj2tRXE/RbZ3KHWYXNLcPeOgNKRBzXSZ/yQbVObVS8JGbhzvpifU+eQqw==", "signatures": [{"sig": "MEYCIQDlC59zIaKYt1Kiy2tJ7Xql418T1QKkVSisyFhQ1WutNgIhAJNCjksQDXfjU85EGnvxAU7/sgXKVzoEPod9mfeMU2vs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.8.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 59372}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.9.0": {"name": "@eslint/core", "version": "0.9.0", "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "168ee076f94b152c01ca416c3e5cf82290ab4fcd", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.9.0.tgz", "fileCount": 5, "integrity": "sha512-7ATR9F0e4W85D/0w7cU0SNj7qkAexMG+bAHEZOjo9akvGuhHE2m7umzWzfnpa0XAg5Kxc1BWmtPMV67jJ+9VUg==", "signatures": [{"sig": "MEYCIQD0NsHXt5Eo6gvb9x3g8J0RYqvvlDSLWWeAAR2jWz67fQIhAIKXJo92Jtf+jZSHeblI3NthIuo54Ib+6nvhygbKVYp+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60049}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.9.1": {"name": "@eslint/core", "version": "0.9.1", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "31763847308ef6b7084a4505573ac9402c51f9d1", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.9.1.tgz", "fileCount": 5, "integrity": "sha512-GuUdqkyyzQI5RMIWkHhvTWLCyLo1jNK3vzkSyaExH5kHPDHcuL2VOpHjmMY+y3+NC69qAKToBqldTBgYeLSr9Q==", "signatures": [{"sig": "MEYCIQCoCkTAI2oVvEHGdxcJjaDeQX85TAJlkmNxGPsT1qRhRgIhAK+jM67yk3I1E0RIPOJvpAU4Jp/DLk8pl9YRsiB+V8CL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.9.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 60030}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.10.0": {"name": "@eslint/core", "version": "0.10.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "23727063c21b335f752dbb3a16450f6f9cbc9091", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.10.0.tgz", "fileCount": 5, "integrity": "sha512-gFHJ+xBOo4G3WRlR1e/3G8A6/KZAH6zcE/hkLRCZTi/B9avAG365QhFA8uOGzTMqgTghpn7/fSnscW++dpMSAw==", "signatures": [{"sig": "MEYCIQDPc0kxuRcyrGGVOlZA2l9SUuen2otCw1ZSg7WA2kLI2QIhALbvfqK1rXmTTw6twbqslQ+ySYNxgu4ATpP5FPmRH07q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 59880}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.11.0": {"name": "@eslint/core", "version": "0.11.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "7a9226e850922e42cbd2ba71361eacbe74352a12", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.11.0.tgz", "fileCount": 5, "integrity": "sha512-DWUB2pksgNEb6Bz2fggIy1wh6fGgZP4Xyy/Mt0QZPiloKKXerbqq9D3SBQTlCRYOrcRPu4vuz+CGjwdfqxnoWA==", "signatures": [{"sig": "MEUCIDAdNJKG2kBAu9M8ZAlyxLE4dk9/ikrhaK96Z7Mb4gwsAiEAms9wP1JSAjoldUh/ecnYnlvyvwsr5i9O4OC6fNDgGkU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.11.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64553}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.12.0": {"name": "@eslint/core", "version": "0.12.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "5f960c3d57728be9f6c65bd84aa6aa613078798e", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.12.0.tgz", "fileCount": 5, "integrity": "sha512-cmrR6pytBuSMTaBweKoGMwu3EiHiEC+DoyupPmlZ0HxBJBtIxwe+j/E4XPIKNx+Q74c8lXKPwYawBf5glsTkHg==", "signatures": [{"sig": "MEUCIQDyzmdp0k9wAf9AatQBAzq2fRCXYuMVcmfdy6733LVesgIgAbSxpH/2gI5WnkpSmKoaMCCaqXnAPIBf2OA3RePvnYk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.12.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 65114}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.13.0": {"name": "@eslint/core", "version": "0.13.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.4.5", "json-schema": "^0.4.0"}, "dist": {"shasum": "bf02f209846d3bf996f9e8009db62df2739b458c", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.13.0.tgz", "fileCount": 5, "integrity": "sha512-yfkgDw1KR66rkT5A8ci4irzDysN7FRpq3ttJolR88OqQikAWqwA8j5VZyas+vjyBNFIJ7MfybJ9plMILI2UrCw==", "signatures": [{"sig": "MEYCIQD+qZ7TjOGqfPamQ0ILOF+q7g5aibdFuUFBSU0c3iw3EQIhAKtdkscJnQu1/YM3+rU+GesSQVJ3b31PGvWcaQiMpGYM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.13.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 64791}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.14.0": {"name": "@eslint/core", "version": "0.14.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "devDependencies": {"typescript": "^5.8.3"}, "dist": {"integrity": "sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==", "shasum": "326289380968eaf7e96f364e1e4cf8f3adf2d003", "tarball": "https://registry.npmjs.org/@eslint/core/-/core-0.14.0.tgz", "fileCount": 5, "unpackedSize": 67087, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fcore@0.14.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDlumktcqFGXzDgMpBRkXP0Dhc800rTxFg/V7td5Rhe1gIhAL/6GwvtJy5HRxcUu0p74RnTrDNAADvKvf2ahZp6ml26"}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}}, "modified": "2025-05-01T19:20:56.011Z", "cachedAt": 1747660588847}