{"name": "lilconfig", "dist-tags": {"latest": "3.1.3"}, "versions": {"0.0.1": {"name": "lilconfig", "version": "0.0.1", "devDependencies": {"typescript": "^3.7.5", "@types/node": "^13.7.0"}, "dist": {"shasum": "b8d32a9be7925d71371979d0ba70512a92720b12", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-0.0.1.tgz", "fileCount": 5, "integrity": "sha512-KhuTkzR6smC4N/y0fUUk2HPv8WKTRuyF4r9Thy77aGQp6s+S1eRd2HkT2Yl8SdfNukWRTV2jgol0wjL1iQ/4jg==", "signatures": [{"sig": "MEQCIGVNwofUC4+yGkaJLgmVpzQ46TcVbBrZgfF+rwdBr4fBAiBsV7PkSQlZYea8iYsxu4pMfoNsFU0LzqOzxe5RLS3w7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQIdyCRA9TVsSAnZWagAAwBMP/2l/rp6ZdoBtjG20v5H+\nS0oscRZzCPD5Cf5JTABRa0PnqqrfYUZoVE/wxG04G09j28BDuJXr6bwjiSzd\n1JE87jExJIplC6m8oBx574Ga758djYa6WScRcFHHUAKkwOonMJPpvcrTrmiV\nmBghFpTMW6E6oRs8PmMZtsA+7cPrzBUDHXmV7/fOc317bg/MrwESuA0Yl47j\nqvyXVYEFm9xMCT6bq723TjdaQg3r76b4aUbNeYWDdPUghufwgES0ZrQd0Uti\nkXObkseSrP0P6lCuKidi73dokpEZYfUmY8yQ8kadhVISHCbzhOoy7EYq/D9M\n+gpkC8b82Zva2+GxnkjyWRMKlsNSEgdKhcyRndXs40wPC6gpCFcOYadWGdgt\nVgQ+xcQUDMNIh8QkDezRnxzTV3GHzKuDDP07QllTPZxoVKRsmyIq5a9DA0Fs\n9dh15gnuiZYzcBL9tizUOvfLohwsuAde35lQ7n5kUqLmvRLTQOqCahfzr98p\nEiuRiMi9Y49FLQ82yOslPtF0j/8sbM+kJA1Myd923BnRPjjowB6Oo6drCl09\n3rPbDJiD4XD2U7bFuSE23qNjwtmFabfZoFRDyjHbgd6Mq8TWHaSuCgiiIjv7\niuShCwW/q5mV9TXOpWFmD+M8rKXw14qXZXimTENRqi57Ax5HFikCa5DjWzQU\nTfKN\r\n=FvLM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "lilconfig", "version": "0.0.2", "devDependencies": {"eslint": "^6.8.0", "prettier": "^1.19.1", "typescript": "^3.7.5", "@types/node": "^13.7.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@typescript-eslint/parser": "^2.19.0", "@typescript-eslint/eslint-plugin": "^2.19.0"}, "dist": {"shasum": "68416ac78ba77b82177fe249e260e9e65cf8ea27", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-0.0.2.tgz", "fileCount": 4, "integrity": "sha512-DFSBrbylZQWHNOAL2CPUEme1J+rBnaOzxn6/amsafAup1u3bm6qDjonH7kR74Q3oKfSWaV3Or9pP/qmjop3VIg==", "signatures": [{"sig": "MEQCIDtV3Byra59UWM7+mQc29n/PjhWPZ66eJ4gLHcRUkjMCAiAl/qFSTT23wV30+4Z3NjS5xyciZGKy+6OEBwU0eJCrVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3qsvCRA9TVsSAnZWagAAnB0P/2kL22JZS+124PbWk4cd\n0oH51fLCxXXWSBoTqeYbcRgHFZfeRKSoEHEUXz+bIzCCO9VUfq8X3R2jKxLK\nRSethKb0s0Mvbu6eFqJP0E39LhIGAlmcRsVbaCcWE1vuphPlUF2uFYZvdxvU\nPaQLisTqEMlUxe7U3hpefE91Q/GPQeKPY3Cw1L2Pu6Xg/makjwhNei8uuw7/\nYhMC+DCfZCvoCszhAJ+XdO1QhQDnxmHIfv8hh9hN3YOJpPWZVeyBIm4LGTnM\n8MjbaTF8XCHnZ5fnbFJPErXT989Ce20bM8nLiWq7JITS07U1nhO1U4aQkna1\nd35rJSgXyV5pdV2WJSZ82IR6N5/lbEsLkcMBIF6c0ZYRb0RSnVwBTNSVRZgS\nykJLXlS3DsKgBcr6zFXLiv+C0RI7NQYWgLijAgOdfgVHylkANBjkHBkWQNnd\n1nCtDdxk7DZ5izkBZ60SIC0UWDfe2VnG71KfoPgvkbUcl5asOPhYerAo3RqN\nhNaOMHC1FsIJy65S9THxAmrkxemXxsZO8g52lkIiurcdY4a3EsCTaZKhA5PY\nWlS+fANIieVRWl0c4t4/X2e8+iul7nWpIMwSugijauYqux5KqJF8cSrRwL9U\n/GVYdkbbc3nGe7K2asZT+UALLE/6I+b8y+xx8dBvWBvDFQRcxOc62UQAMUry\nrioI\r\n=zpr5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "0.0.3": {"name": "lilconfig", "version": "0.0.3", "devDependencies": {"eslint": "^6.8.0", "prettier": "^1.19.1", "typescript": "^3.7.5", "@types/node": "^13.7.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-prettier": "^3.1.2", "@typescript-eslint/parser": "^2.19.0", "@typescript-eslint/eslint-plugin": "^2.19.0"}, "dist": {"shasum": "a24bb99780247c1cbf2b1da849240b49cd81e127", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-0.0.3.tgz", "fileCount": 4, "integrity": "sha512-dpf58BywaMjpW/qhbavJAoCDZvWXjf26ol3hxjWMgG8K8NabhK+KkFFDpdAXGYHpE0bJ7gSlfJpbgqw4Ra22Mg==", "signatures": [{"sig": "MEUCIEr114K0O7OO1wETqF4BKE2Ji3Wuw1X2ki9tVVfMxZkhAiEAtCwoDM9Fzuv813IIZLt6g0Vu8Wi5FVzM9gwwAJ0AtsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCXUfCRA9TVsSAnZWagAA3QQQAJjNcvAQZUrEykh7hje6\n3GgE6ueuAPeQ9UxlUUa7YqfzeYOMlBugxkTXENqdDVUK/D0OwORxREsF9OzX\n+sYvXiGYVrNnxDO86/OvH11vHKNRVjM7GfHdJC6j2RNmgTAT+zUOucM6WvE2\n0uSfz4tHuAO3O7u34lA4rX9cbP066C3YKgdgej86aocTRzosf/JpZ6UmyoFA\n5IHroCRydPUUrmgaYVKt1dCbKaP5DbxUheFN88e290KPJiEKOuhYg8QD1GlI\ny7/kiljQYLIF/baEqK7SKWIV6IqpnSOubxqmFxubiR0z9w7THV6AD3o3EmjO\nJMBj6Vl21PcvXvi6XkDB/Li4Gh3WG03UMRZXUw5ozjayq/barIiHcZZ+Hxhj\nHu8htEwl1FbCgckV1GDE4nGEnmAobsFQGE3aQL1HJl54bkv5VatKb0t3R23j\nE0Ep0X6Rq0M4Kd3cnKixoxAu6OW7Q2bJC300V3HV3k8apS4pyYA3HB5jULiU\nqKwZN36Z79t6eCpsGMyAW++YrnUAqgNdnERuMChXlHmYRI3FWWafHemBra5D\n80ZtHsDPhwIvU+8Jcob9ao3HPRYpModAUcmwNV3W6N8kxBpLZyh+TbNQa8jv\nLG/mWG0xWzNg1gEyo1Si+uj6WGFkLSwzYe7KC1xtgz2liEgvON/m/J773XNd\nZf1t\r\n=aFQC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "1.0.0": {"name": "lilconfig", "version": "1.0.0", "devDependencies": {"jest": "^26.1.0", "eslint": "^7.5.0", "ts-jest": "^26.1.3", "prettier": "^2.0.5", "typescript": "^3.9.7", "@types/jest": "^26.0.5", "@types/node": "^14.0.23", "cosmiconfig": "^6.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@typescript-eslint/parser": "^3.7.0", "@typescript-eslint/eslint-plugin": "^3.7.0"}, "dist": {"shasum": "d9cdaa97f237472f7f368ca23d8ae49b53af73a6", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-1.0.0.tgz", "fileCount": 4, "integrity": "sha512-Htbenp03fa9rAnrTTv0yg9MbsABM4/z+2XMdWCxtXNqGj5xaSJu5FpdVHKa8atVd35m71CPtlEF42UC3GYGZJw==", "signatures": [{"sig": "MEYCIQCaQX45VXCna+2j6eJ5UGHjVAZCVcW9JS5q4BL9NVOpfAIhAMUEw+ryrizyBCW8JIfcj2cMkOFhsWheAXlOi2DlZOAE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHMryCRA9TVsSAnZWagAAkN0P/10X82s8rqVjzgN8BEwQ\nCOVsQ422X0Sy1+xyVe/E/XJdVHToNv+V+VmwUzEXj47MdtDGnVBJSAtcK7PF\no+4QRuVqzmiErXrAHYgaWRARwdF/iBEbvJ63OSngo/qcuxEOEgO2/sjUB4If\nFCXW+Jbe4OiT8dYclEHHDS/HASydihI7H8d2Xn84S4KdZSY/br0UmFnwA+ye\nDfconIDJVvVCKGvBbD38xZVX2KvBZ/hLNFC0TNcvRRVlm3QrZJzjjfDSfzg8\ncOJIU4avm70oXCrBbL8KKWfXwQ3Ezo5s05a8BR86ZpsNVimvfMCiCyJMztWV\nNCB0OldCM1odZ0aChAHlgSgnH8GgqAnavjuRhREX4yGo0L38cRzjPMQbnhg5\nXgK6UmDTTztdfLTxhqM4tiOeM6LMGTI4Gn3uLblXNbG1HTvTp7Wc8xyLgLMH\ntfjhkTJ1gkhb7P5RNajyZOf3akMcqUwxNjhkvvUL0Sj/xnd3QSpa3fTaB0mQ\nOVJ5dhaMFbzlaMt/OUu3DasHe8enzs0lt9RF5zg5UmjlEcKMcgBtbx6j6PCn\nDHDv/9Mpw8bor4H8/NktjA4YB1Muvbh7gdnIJajj1aRycWf7ZTIUl7d00xOO\nZhkcl3bA5k6+g+kf2ObRCBGaCW2R3wQU+mVWJInD5xTF7mLVpIlG9WLMKN7o\nemOa\r\n=TUPM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "2.0.0": {"name": "lilconfig", "version": "2.0.0", "devDependencies": {"jest": "^26.4.2", "eslint": "^7.9.0", "ts-jest": "^26.3.0", "prettier": "^2.1.1", "typescript": "^4.0.2", "@types/jest": "^26.0.13", "@types/node": "^14.10.1", "cosmiconfig": "7.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "5997d88feebb5f51c0f9aae164f722a14dee66d1", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-g6HsB8qzh6yAsixO31LTdcSua3D6kbvEy3ql18MKovNDp4YF9bss/4LLXUqGHH1DEnx4U8ZSF8Ssr5ujRIBZjg==", "signatures": [{"sig": "MEUCIQC85fI3+GTpPN+T0af1QKRwGkKNWDWGPxqHTw0N4o75DgIgeP/xnwl2oiY6ltl1VBBTRVp+sz9Sf4RStGjW3+cf2v4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfZ2lTCRA9TVsSAnZWagAAWzIP/i424UQijjom39DnDJAR\n+2ME+oHNaqCD+v8uZsfh/BZUJrGDEQyUkB4Jx8WvrocErCtqx2MOXlfpYQNp\nsyReXO4+kEB+SnYGDr+OAv/TIjwgO7ASWDWC75FdxoS8sPY7/jshES8jeLhi\n9HxXZ+4C6PUNMlCLfLgzoX8vm+E5XYOMvkgrcVfxWkgI2zoXsSqG5iMC63IZ\ntjYQ27Nz9o53v5YJTLV0y4Qbobs2N7jDvXen0vw8bFdB2Vr04Xc/s70yrA/l\nMaCSPSs8im3vk51oirXDpS+K4kmjbLNmD1e4uNMKlSPWkngEWhg7ajTW13Hw\ng80gnE86z/5Z83fbKjorJ8tmAvAPrHdJq95FYWYbgdx1atummWzYqqK9FqP+\nicxeevJHbzQJ2ZIeLxF7kabzDshk6X/zOZYsynzchQsmaj8GQn4Hsy2lwNaW\nNR80U4TnSDiZp7DMeLIa3JMZ9c4+JVJa9Exzi2Uke8T0TYTVe3Nw+yDYCV8C\n48yGl0fIO/EnYtTg+Wh4ZmlC8Vlw5XAM5aNx600c2gzh2HnVHo5V5xrodJ50\nVQIGfV8zkCWAW1XpWt77hlauUJbrU+iLmT7wP13mkmgcPITrh6vTZWVH+t6R\nDdvR3lmmpuSAVt0ypn01d2iQvxEi3m6+xqueyxULRwZqsabRzdZ/WySQ3Bec\neYD+\r\n=P4K1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.1": {"name": "lilconfig", "version": "2.0.1", "devDependencies": {"jest": "^26.4.2", "eslint": "^7.9.0", "ts-jest": "^26.3.0", "prettier": "^2.1.1", "typescript": "^4.0.2", "@types/jest": "^26.0.13", "@types/node": "^14.10.1", "cosmiconfig": "7.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "24353e5eadfbd7fcf314bad46714161e4e085ebf", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.1.tgz", "fileCount": 4, "integrity": "sha512-fFzm+Lj/Up8OGJfygxz1VLVuihcqZQpqFMmQ3yklVwUIA8Z6LWBwiISObInLg8R8g9E9r7b9t2+kh8+N7vRS+g==", "signatures": [{"sig": "MEMCIFwItPLeMMwlOVhLdQzBj3P96zO+qEDyujfMK/OS7Fl6Ah86GZrUeulr/WW9lOk3LOVSjHGOfwS4uVqxN7tr1XmP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14565, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkfY9CRA9TVsSAnZWagAAZMYQAJaSLUc8v41TMyZsWz8E\nFaN7IZ7+DEDWJhAAjV9KaexvfDZLYHLz3LjnX+m+yAYdvYFxzzN3DOhy/0m7\ngVexD3XCtzIpm5GD20ROWQ8HkEfNGoKE4n9AmZaWaB7wkALVcnAUHw71fub5\nRTA/D2rZ8ItBp848naFpRRQHhIUBegi+At5eh8NSU32fR0sdNSfZOWkSMuGW\nBAcI7p2sjfjazvj65EGPW68MdaPD6essUYrDbg+mH1yb99HyEMBdpVMrlaF1\nprCyAg3jMNlL1MKAhpx0wvT0H6nsIexPXtbRoWJzvDAG8bYdfzqC2wPHgSlp\nfM2UvSScWttGwx3S6VBli8LKlaLKg70wfyE5EJFhQS1NG60b1weD++5bMZhV\nuN4+xAQtmpIlDUfzF33PZsi7doiDBJ72hnZZ0QwewCqeLJcxu5MM5M/0DqF6\nER57j4swYispTp1zMWdKmvTthFfy/vI854kj7DRiH+zq24bBBafi1tJObSTi\nV9nDP4MHErSOmLZWxDFctawdCkh3EZ2fddNGD3xHetLAq/Uf3DhWGMF4VWxz\nCM9v4uBMFBminuta/Xj7GS29oFHwIZrInth1wPeX4bXOYLW3VEwsm1TJMYZS\nmL0knonWuyJDIiL4cQG72maQ0K3IsMn/2KXDJYDScdetBBL4Nwufub9Bzzn4\nz8pD\r\n=Ev6k\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.2": {"name": "lilconfig", "version": "2.0.2", "devDependencies": {"jest": "^26.4.2", "eslint": "^7.9.0", "ts-jest": "^26.3.0", "prettier": "^2.1.1", "typescript": "^4.0.2", "@types/jest": "^26.0.13", "@types/node": "^14.10.1", "cosmiconfig": "7.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.4", "@typescript-eslint/parser": "^4.1.0", "@typescript-eslint/eslint-plugin": "^4.1.0"}, "dist": {"shasum": "9f802752254697d22a5c33e88d97b7329008c060", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.2.tgz", "fileCount": 4, "integrity": "sha512-4zUThttj8TQ4N7Pps92Z79jPf1OMcll4m61pivQSVk5MT78hVhNa2LrKTuNYD0AGLpmpf7zeIKOxSt6hHBfypw==", "signatures": [{"sig": "MEUCIB7EFEbVKYVjaQ+RQcGrK3n3yrBwlXbkNAcJVJvPsLySAiEA08xe/P84/4Vrsvszcf768YHe0O3Kn3wIFgJIPYB5j4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyfAoCRA9TVsSAnZWagAAaFEQAIp12V5H8HEAMNQgpoNd\nqixTuZZoPPt9PFq1t5hqk7YjVY7vDTrIMfYxaRJ5GittnAHt8jqrL3IJ26RT\nOb9jgVd7PfDjOynXnxm7yKRXVpewTgyjT1PHIOlYMSJnZXxqj7mWRHSKA5oF\nh7yl09YT+gKVQwbeXPilp935c98N9q7ClrKkKOgpnShIK4YsyHwdDGt6GM/b\nDswXkzjv3wu1+sIp/j0pGZhnL3QzKuSXfHs9aiuhu0zLer8/wKpHppdUYtwy\nyO2F8rV7qmpUA9/6SNn2V7mQ8N3qr/+o32r3dTLlsOTPQA/UMOKApZ9SrWD5\nqS38KreJxA1gRTzrvNeT3ydJBxnO64SJnKoJGlnpt/bsjemIauJHMZTUvCsx\nDARq4oguzkKzPXEm9R5VTgBMYAFKenmvs2eJQaBGpoWUaEYIidTf9oxkMaby\net9ZXa1w/lXuet7epHvYr00re2CycvqyVUg3rRrBEv/NtiVJh+ImF9xkgpMh\nqOy9eKDAkuJQh+lHdWIXQ+ORUwHscdHQN9xgj67yP0TPXQ9PbF3q4IN1PaA1\nDjkp9aG2CM1+IgitTKFqDteZWOtka2IqZHPu6W+DvYqXiH8sOoFlkW42RPFT\nHXYAFqOo3r1YW2kC8XXWry/78G+v2mABb1WwBqlFNc6FmN2DnDI8yutcjUwR\nyDfs\r\n=OqBk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.3": {"name": "lilconfig", "version": "2.0.3", "devDependencies": {"jest": "^26.6.3", "eslint": "^7.28.0", "ts-jest": "^26.5.6", "prettier": "^2.3.1", "typescript": "^4.3.2", "@types/jest": "^26.0.23", "@types/node": "^14.17.2", "cosmiconfig": "7.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "@typescript-eslint/parser": "^4.26.0", "@typescript-eslint/eslint-plugin": "^4.26.0"}, "dist": {"shasum": "68f3005e921dafbd2a2afb48379986aa6d2579fd", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.3.tgz", "fileCount": 4, "integrity": "sha512-EHKqr/+ZvdKCifpNrJCKxBTgk5XupZA3y/aCPY9mxfgBzmgh93Mt/WqjjQ38oMxXuvDokaKiM3lAgvSH2sjtHg==", "signatures": [{"sig": "MEUCIQDwOSrzaGsAILzv3Ng84RKoG6fc2OrG7UqQCb9/rJO6TAIgfxuBKtgmazV0gJkNUhSdZ4Nznnkz43G/rSGYrySn8LY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvSm7CRA9TVsSAnZWagAA1oIP/1k0DlxywzgvR7h3E9UK\nHAgpliPZ3vlo+CoChbyS/5ARI9UqTBxf/Oa91/NHs6oGWolbgRXvOAPwcQpA\n2ANFyMXzCo5p0zTiFA7Epd7fpOV+9XBLDYVDzgIO5e/RqYeuemn9d2AaMCuB\nEvUKCZhqDVe1Uz6Kf3s0ecmecqMKtRsSJgLCVk4XRu1AzM7b+KMugM/LTU+i\n6hL2Iaat7oucOS0FK/PoYCL4LT+tatX4d/jLGKYx/YJPIM5HHc+aQ0pavqTW\nlRrcpmMG3cTTt1kLKUUemyfbljvOQD22GZjw5EX4LKjwx3zT/pUQ787LuZhB\ne3dg+MmbbsYGTkV08Uw9JcImUB9dUb3QZHYc9H5kQuLv6jkYzHWLCGRPfrhC\njyoe0buU8NceuNJ7XbVPkKmWztJaRYeYMaDeBhFDQ5B14kcOCLPt6kra8YiH\nJQvHILG4R6uUlFMw4PRCjOn38zwsZv2PdQ7Ujt6SeF0/iRz2wQBz4irZOKWh\nEKF3/4cgKmRYV5vtpL/831+30jlY2RYStvTikgTXV6FxDKgUCHc4nzG6arYv\nTjDk1xxU4YH6wRF9peMeVZgIYBH97ylCrOg5QTJSldOf4qnmsRLdEbX0pppZ\n6kTDLYsGAW0MEEwUxquiDdOG8XRVaCee3qjGXGoG2AiONuz/zma2jhVzi5wo\nEft1\r\n=+2iL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.4": {"name": "lilconfig", "version": "2.0.4", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.1.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "@types/node": "^14.17.2", "cosmiconfig": "^7.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.3.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "dist": {"shasum": "f4507d043d7058b380b6a8f5cb7bcd4b34cee082", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.4.tgz", "fileCount": 4, "integrity": "sha512-bfTIN7lEsiooCocSISTWXkiWJkRqtL9wYtYy+8EK3Y41qh3mpwPU0ycTOgjdY9ErwXCc8QyrQp82bdL0Xkm9yA==", "signatures": [{"sig": "MEUCIQDdAOXVoutMrziycisX6ZX4+O5qfpngRdbrapvNtglnZgIgf0x2f5V+1YO32c/y9t2CpRzJ++GcJxd2TIhMj28O+GQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14779, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2x6TCRA9TVsSAnZWagAAmZwQAJkKWZRC8vc6jMFizQwQ\nitXL9yNC0htOOC+ug05nMZq90JDnCQWb9uKDcN9XYYhWXA/ByBUyn1rNI5pD\n3DBFyctBk9Bc3zXE7TvFD2M3Cy9TUtZg+7dkfH6+4xPx6R6d7VooNewqsHJk\nToKKQ05D833w8jRFydnwUwNGB6ULF2djdYJbrp3XK5albsAw4IxAyGU9uDZ2\n+lYn9Cez3ewdJxCagNlBVL2l12niK785Zi959fQi7oNLkMIFl+YQRoflu6o4\nMFY+jFCpgtRjnISLlahbr67ghWmbvsuRDswmp4whf1zrlzEx7+U4hFVYdCfx\nb1Sm8ogPyAnRdv/D2liyRzodiCfqL9DsYQMakiwXYJvWIW+zif1+UAU5fjOj\n1zSK3rTNIj2b4MBdrn13HYa0GpSKp0erNps7j882eHKzaQY+FzrQTQQ5lp88\nHfJvvDshaeKxRedvbSTshcwukCM+ekQ44iD1K6wjvHJ2uOSCFGez92/0QetZ\nDn50DCBGA+qjGmwyUWMhR2KbvLkCO9Y2wRybkO2rqf4gqF4WTf+qJBNxJtRW\nkdEFXBO6YkcmDtQu/N/WHsaJq2TPMk3yMswA4YVhowOyXyNcgVD/sYZlF62A\nKSJfnBfhLV+Z/GScmdGVsIRN5D7e0S/+J25yaW32DwXQGeGYpO0uEkKPbqvj\nvg63\r\n=wsSn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.5": {"name": "lilconfig", "version": "2.0.5", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.1.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "@types/node": "^14.17.2", "cosmiconfig": "^7.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.3.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "dist": {"shasum": "19e57fd06ccc3848fd1891655b5a447092225b25", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.5.tgz", "fileCount": 4, "integrity": "sha512-xaYmXZtTHPAw5m+xLN8ab9C+3a8YmV3asNSPOATITbtwrfbwaLJj8h66H1WMIpALCkqsIzK3h7oQ+PdX+LQ9Eg==", "signatures": [{"sig": "MEUCICO1qg03Dx5fqDhYWmvZr8N6At2FZ9ax129otK8fKiBQAiEAuJrqdWWr7uylcdEpd+5oxINiyqYGCkJzg2HyN1iStt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15386, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiO6GYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwzg//aKvQBUvmJ7GdXNLGyAlk4kYZHPeQv/PsJjEZ/XLbOM8orqS/\r\nrEBh60zLrDy3G9wVwihIJUGOxxj579L7BWUbtTh7lurm1+SdL3VPFImoio0m\r\n1FMx3DYzWB4o7T93YP8jLiOQMC4uzjmCXaoDWTDhK7vE59iMFoCawuHrD3we\r\n56pRS7RMwk+5j/65cvGJ8CePcuTGJZza0wSIisoHu5GYgpLDsaNrNpid2FyN\r\nm3eSnTlmZk7QUrT0DftaIMDi7KNqy/NAfZ7PBdOrimZp6Q/DcoORlvt9tI6X\r\n0Q3kgqm4+m2Vt2Z45MFpJirIM2V6DqQxo9pFqsp/G0vgIs9+64XDBW0FWFw0\r\n2swipsMpmAoJ5aryRK4j5gEMu0ZXVNyTFHFuC7wTtNLZOllkQqu8xgCfrTyz\r\nv6gS5HcTpba5nsDMCEZSRAlywRCOvXUEi99dJt50w++SbzTJ4MEHw25sVa/v\r\n7KddOQly0L8IjKc8iZIN5HilXRRLqrkTX3Ul+ydM+AMh+vi75bVNHnuDStL6\r\n5yoQ837mrglrkYaZ1uvT7r9TAsVWIxlZA9MZwrG5sEiuDBnZFY0ogie6y6qq\r\nqwn9YIGEzRi7rMHLFxdzITdJq9RFn4zEyoZ2wG865pS/tMfrP4UbVuSCrKMw\r\n0uJdFp3DSxnbevsGat1Gh/kNOFRMZ9uuarY=\r\n=Ya6+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.0.6": {"name": "lilconfig", "version": "2.0.6", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.1.0", "ts-jest": "^27.0.7", "prettier": "^2.4.1", "typescript": "^4.4.4", "@types/jest": "^27.0.2", "@types/node": "^14.17.2", "cosmiconfig": "^7.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.3.0", "@typescript-eslint/eslint-plugin": "^5.3.0"}, "dist": {"shasum": "32a384558bd58af3d4c6e077dd1ad1d397bc69d4", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.0.6.tgz", "fileCount": 5, "integrity": "sha512-9JROoBW7pobfsx+Sq2JsASvCo6Pfo6WWoUW79HuB1BCoBXD4PLWJPqDF6fNj67pqBYTbAHkE57M1kS/+L1neOg==", "signatures": [{"sig": "MEQCIFeuFMHC6pvOtVp2SbFeicnyI2BNpbnliy3Ys+m4irtdAiBfot9zR+3EuHmItf47OCCL3N1wie1Sx4Nd73g7RLpz7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiytZKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJexAAoF69fASG3AeQYU0MrAbP7LVZVPGPJo9YjCzIbR3CfsEqUdN7\r\nYgteUXbeZP2w1MpRJUndW4wJviv9kRGP8+1QuXWrlPf+kaQG80+7sgGaVORj\r\nu7waldWQBoovlMPhzkhJSMZy8c7t0kcboiNzTF7caxh/eWTomb7k4/i3qQCp\r\nnsn19hZFBZftO7qXe5jbeG1e7UG44+VBGoDzssERAG2h+Fb8jCScxHLXlz40\r\nzY4V6WYxP2hDvZTUr2OV8JcqKJDT6nFjPvfNEXmgd8qN1uYSQNphcaWSeS6S\r\n+rEF4X0HAVmi5qgovoBQ1jYSEZN7VOurB6SDGjlxKQbze10yireDPrwgemuX\r\nUp15cY5b9kSW0wqz91e5fu/5NgHDVoqZ4HS85ur9wSdm3DD7yGjZSHVpmOjB\r\nC3NBzzgL/8qx9AC6aaU40uWtdxQp5Drv8ZBhXY+UhmtXy+45hSuT77uI0IBO\r\nXq3njobFZvApljfp5K9ZbDnnfyHpjosWyeOC1ypx2kbPzEaxjL3u0CRLgL6C\r\neCRvMifjkjWugTV+pFZKGNI6nnXeU/CGEsVB6ror7oyz4pkB7H6aiF3NvqTb\r\n+gGs/BDl6yfXWdX70iwFmkb5nMFeUxUfwGMvMVVAFZowvhsNGO7jdu/r3SFO\r\nNuu5pfIAnuSGnSzPUyFDKwFSgF01k+J10pY=\r\n=Zn/j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "2.1.0": {"name": "lilconfig", "version": "2.1.0", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.35.0", "ts-jest": "27.0.7", "prettier": "^2.8.4", "typescript": "4.4.4", "@types/jest": "^27.0.2", "@types/node": "^14.18.36", "cosmiconfig": "^7.1.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.54.0", "@typescript-eslint/eslint-plugin": "^5.54.0"}, "dist": {"shasum": "78e23ac89ebb7e1bfbf25b18043de756548e7f52", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==", "signatures": [{"sig": "MEUCIQDQuTBdd3HrQhyGLZOJ2JOHKly2C5vUdsEU1FSrFkwaMAIgB6C6HOyIGBUuk+3dzUZOS74c3UVzNjlBc6MVimCzwzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkAPtXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoftA//Ys74twRTDUEw5L29+LVkszXEcmtRf3XcMvsbPS+X9momm2PA\r\nRHNzpBhLUAiNkEfsZaAOqpGMnh8o5MPGkPqX0b1AfwfZuWXywSnoltkBKVVw\r\noh/WynvZ5rB+7R64mvsW3W+n68Vu4Az6ReXTydf65qtnvEDj/xiLTrOsvFNB\r\n22L6kVyhzLfu5VDxQ767Ik7xRvMb8IuSYrrNkOvugKA6aLOEJDZMt2hukAiM\r\nY/qUJMte7BFP+ds8RXTEOQYb3qEJxSVBtcEbkFEMLObtQGJVxmOWDieM+daI\r\ntLxhAxSrRhCwU1i5ZWQIDNyr2tCN/7Du+u58E6C4DbP/vL30dkk9c2fDUWPp\r\nqr+gkrvYsWrnkXs2C2LAu+tqkvmXx1fL/h9xHb7oxWvid2i036Rz4MkLYxjp\r\nxM3gjvtHluZVIDRcAxEcH75UbKLOvpz4S3FBZ4gYXaoXkTF2Dju5C6S84xu3\r\nc8GaOuQ0JsNikfsvu/fecPrB/8U+OeaDvZaggVCZWArGjpPm39J7rI9Nhnl3\r\nsIkG/HwdAYwEicAKr9i63dfisMvzn5Oj0DbneMzKXJ/x3MoL4zBidKPUUo+6\r\ndMSQgKPzkBYubGbeW8cs/1N4NHUNSMDOFva0c49txyqe+tmjdacuQ8h4lQKF\r\nlpidZyfTdmVJ0xkuB2cjghDgU4klLtW7xV4=\r\n=fzHr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}}, "3.0.0": {"name": "lilconfig", "version": "3.0.0", "devDependencies": {"jest": "^27.3.1", "eslint": "^8.35.0", "ts-jest": "27.0.7", "prettier": "^2.8.4", "typescript": "4.4.4", "@types/jest": "^27.0.2", "@types/node": "^14.18.36", "cosmiconfig": "^7.1.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "@typescript-eslint/parser": "^5.54.0", "@typescript-eslint/eslint-plugin": "^5.54.0"}, "dist": {"shasum": "f8067feb033b5b74dab4602a5f5029420be749bc", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-K2U4W2Ff5ibV7j7ydLr+zLAkIg5JJ4lPn1Ltsdt+Tz/IjQ8buJ55pZAxoP34lqIiwtF9iAvtLv3JGv7CAyAg+g==", "signatures": [{"sig": "MEYCIQDGZGWKVUGoRmJQjQFHlSzZkquU2zap/NVQ8vk7KmKaOQIhAK9YU9J/jPUMLKygXdDSN8eYwk9ipwa7CoG/jzw/dAUb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19273}, "engines": {"node": ">=14"}}, "3.1.0": {"name": "lilconfig", "version": "3.1.0", "devDependencies": {"uvu": "^0.5.6", "jest": "^29.7.0", "eslint": "^8.56.0", "ts-jest": "29.1.2", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "cosmiconfig": "^8.3.6", "typescript-eslint": "^7.0.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3"}, "dist": {"shasum": "aabf03fd46934d0566d75b4b64ce41a2cdea1167", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-p3cz0JV5vw/XeouBU3Ldnp+ZkBjE+n8ydJ4mcwBrOiXXPqNlrzGBqWs9X4MWF7f+iKUBu794Y8Hh8yawiJbCjw==", "signatures": [{"sig": "MEUCIE4IPq/yn5+jhSdvgHx68Ougm+dAMFG65jodXdd3BXvHAiEA03cP1qotW5oGwfoBPCbYMtoo93OzkhqiWcpe8eZ0TPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19813}, "engines": {"node": ">=14"}, "deprecated": "This version contains a security issue. Please upgrade to 3.1.1 or later.", "funding": "https://github.com/sponsors/antonk52"}, "3.1.1": {"name": "lilconfig", "version": "3.1.1", "devDependencies": {"uvu": "^0.5.6", "jest": "^29.7.0", "eslint": "^8.56.0", "prettier": "^3.2.5", "typescript": "^5.3.3", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "cosmiconfig": "^8.3.6", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3"}, "dist": {"shasum": "9d8a246fa753106cfc205fd2d77042faca56e5e3", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-O18pf7nyvHTckunPWCV1XUNXU1piu01y2b7ATJ0ppkUkk8ocqVWBrYjJBCwHDjD/ZWcfyrA0P4gKhzWGi5EINQ==", "signatures": [{"sig": "MEUCIQDKm1iLnauy7r0Uk6N/ZqhQEwmSfNz89Tmeaix23yaMbgIgNPCjQGbZX20JgwjpONy7WdWlKYXdrcDY8xQ6hUeucBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17739}, "engines": {"node": ">=14"}, "funding": "https://github.com/sponsors/antonk52"}, "3.1.2": {"name": "lilconfig", "version": "3.1.2", "devDependencies": {"uvu": "^0.5.6", "jest": "^29.7.0", "typescript": "^5.3.3", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "cosmiconfig": "^8.3.6", "@biomejs/biome": "^1.6.0", "@types/webpack-env": "^1.18.5"}, "dist": {"shasum": "e4a7c3cb549e3a606c8dcc32e5ae1005e62c05cb", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==", "signatures": [{"sig": "MEUCIGnlRczLYl4KuueAANXrEby7q8D8XXyiF+B9apVxkr6ZAiEAufHHCsRvs9GWJg1qPdSX41FOQ6v5/uBWuC7iKh9xNWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17879}, "engines": {"node": ">=14"}, "funding": "https://github.com/sponsors/antonk52"}, "3.1.3": {"name": "lilconfig", "version": "3.1.3", "devDependencies": {"@biomejs/biome": "^1.6.0", "@types/jest": "^29.5.12", "@types/node": "^14.18.63", "@types/webpack-env": "^1.18.5", "cosmiconfig": "^8.3.6", "jest": "^29.7.0", "typescript": "^5.3.3", "uvu": "^0.5.6"}, "dist": {"integrity": "sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==", "shasum": "a1bcfd6257f9585bf5ae14ceeebb7b559025e4c4", "tarball": "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz", "fileCount": 5, "unpackedSize": 17959, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD8bwR048YWsbpn+/l/UM2kAZZ2EDS7c7OJgJ4FCJbnRwIhAKl6XH03p7VR9iefq/Ai5yz0vL8ttFe8ReA2iyYUIIj7"}]}, "engines": {"node": ">=14"}, "funding": "https://github.com/sponsors/antonk52"}}, "modified": "2024-12-03T11:49:17.366Z", "cachedAt": 1747660589155}