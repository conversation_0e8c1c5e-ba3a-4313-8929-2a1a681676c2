{"name": "@radix-ui/primitive", "dist-tags": {"latest": "1.1.2", "next": "1.1.2-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/primitive", "version": "0.0.1", "dist": {"shasum": "402fe0e690d5ff970737689dbb5f24705d423889", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-Z8R0kdAZui8eYTuGY5oQUA0SU4jYq43m4bZW6Dw0B35fUp+U3r+pCrkj0EADJAPv1UaKNskSv/lrfRdC7719Rg==", "signatures": [{"sig": "MEQCIBzwwjFA/IiHc8mntZ8c2sHmMjkZuIfbFRhir3CuAC3FAiBxKK0/RirTb9gIMGU21jLFxFbDSQSQ0lHi7Q1nm1iaeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+U6CRA9TVsSAnZWagAAyQgP/2rw1Z5yffMx+2NilqY6\nGpaxTnjt9WXUtK3x1HDWz1EKg61sFhFqhrdH5wM1PmLqBi8xJZrbt35HMxmJ\nfFt2hoShoJ3Mh01eQf5plbVQ/gRw9lqXcBrR9/FKicTD/w7tTcFX4q+iaq6l\nd+UqFqb5kvMqB7FyA9aiDuulGYEkNfKMJ9JUXFowffmZsLUIrtFfZ6de+QuR\nTdOnnQTGOT3zGv7TC2vZgSKtf30h0CYcpHNOUWWWhDlYzAOnrV1nl3QbcOit\nLwATOd55/z4LU2x5aM2+x7E//COanXy/5XLgCKaDOAfsFJE7heiXLnCqyvpO\nFeWBnDCOYYvaSElDEgyN1cAW+vdoko2QyDaODX0S96VIKGb7huLO/bQ9ib8o\n676Fe/QAut6pRIoYSR6GG2gxz2aSd+GywKNNO1+lPdAA6rCyKSXpe6QwH+kp\ncHEb0La/fk+inqAU+gk9akQFUH2isRSXQjrpCQiFYEXPh5RtHrKKYvZRjMB8\nAEk1QHsuLzxKz+NK/F/HFZiHedp7oAqwrtOiNTCKa4lJAsz1sWKNOvspbq+a\nzC5wQ7toWAkaOfK+0f3omXC1yJdnqgcT6y93LF9K0LOx9j7he80uf21Lb6f7\n0iYUO5yWJkTbhdJKIN6h0+kWeNwuWnjre4fs/wX0/KLrC/G+BdDSUPfXk03U\niQpl\r\n=Oo2H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/primitive", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10"}, "dist": {"shasum": "dce85a57c14fdf5e4f1d4f7b5dd7aee8a0d13fbb", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-6RaYzskCsKCBtVxtEFNEZXzhMrPxf6qd2PUa/NNlX4pSQsAcc5ylzPGeTF1lp/6i105PJ8snpMLkOAO/i9y1Rg==", "signatures": [{"sig": "MEYCIQDb8ywqT+zmKjDM3Ld/FZlfbTMtYUoVjskVR2z9D7gdTAIhANIXUrb6pu4HaKRtvnSUBaweq1E2e9jAGrTGypgcK5fD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6584, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOfCRA9TVsSAnZWagAA1JQQAIMF6ZQoZWzeKsXr4jJP\n1OARguxrOlrt54CAHMEFO/y4BDUPk6f8Kr7bDC5SF/qjcfEOkXkIuZ8A8HZ3\nDzXnEE8F/FZuEPOP3P8U7ISeeoNSk2xNEtMkTg86oRNyBtgTCFMKD7MkBrSI\nFSFb8iybwBGF3AIsAnB6CdQs2ZVo0Sunq5YgofwcNE1QUMwBx9X5SzcsZqF1\nb3YEj0iJQy+g0I9tp8T/6GauQDfHD0T515ARtffjkl6LWJ2F//3rNiQmmsGj\nENUvn/HjSx/T5/13E8EOcaNTDeCQyRWF0ujSBQ1WC+iaVid7W5juWekgFLbl\nU6ZaJC2RssYDVMBt6Q77NqlMC7+YAOpIYTUueXkauooiiLJxnkFcftqUVgld\njwakXv6no2RuUbpqfInee4YY9Ue831tY6h6dlomKZvpHjWeqvGoX8NW86hOq\nI0iJ7cOw9riLnJi0EEXmGwYk6HP/sP831JFPXu8qtbw98AEE6lkzm+I/IJqa\nNrarDgvh4RdxEmxSd06RGlfGyEZc8TYx+dNAwM1/jIiG2+dfAq1h+pv7krdQ\nTk6SfULC8EZRRW5ALfcrpsn4Gs4G8FAqKZtlXBAUWuI0fD4eIsDNvU0Voewz\nuUKJeZckha3Xla5LHMxDNn06Yq+XwlGfmqRalQhlHDmZLpMtM47m5Z4odP1e\nCfvZ\r\n=Omq5\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/primitive", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "7eb9a1572b7f581e6495182401bd16ad3f45778d", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-NxMAJKk98W+Pg6/QH7C0Nsq3tmxYA3BfotwOmbSNnmCEEsMGimm95UQPU4OCOzZFk3mbdhV4fTK2HnD71N8vCQ==", "signatures": [{"sig": "MEUCIQDcnwSJm46POWSYxtq3U76F129Cs/54DIHYW8WyYH29igIgMqU4frizhb3id8Cz8Tul1zPgFOFp66b73ChuJJRPs+U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HhCRA9TVsSAnZWagAA96sP/2cNeS4TJWVAPtWawBjY\n5YZCAv1LbaCSl/ORIrp89aRtQct6xs9uilr8RnlF/Sq4KWqZ2V+O9/md7ics\nl2UGw9mOFxAMzMu73eVVN+Rr0G5Qfm7MjVBWyGygwJHwPJkxQmdNgz5TmhVn\nGpUKUou8zia2RDIbstDRzG2WdSPfygli7gk5MJY4Fkbry3Ml7yQ3suvKICfm\nCOk2rkfgA/3CktUv2Qz7w96kSqKm2tCOBk7LxU4/FInDAiaBIdka+7EPnI/f\nYsuEfJKfRSO7cYNEeJwLcjH/PGDYJZ8QJOIfEYpHk/Wdb1Iii92c2F7y7L9C\ntJGUqkC/HqLAxuTrS3LJOAljt9F+7x6+8kLaZgxDPUkY4dJPsB1SHyWjm6bw\nh48UML87mkAXZ+blWMkl6wsdRqjuoN6+K9fOmqlJxJ+4RKdBun/oSuZHBOPy\nf+V3B/RgsUV5Ln2RkOw/kHlLVjt+EunR67Cdb81RHz9NVNqz+qvsPq0xNxAy\nVDzq/Lfl4HtoxWIqmt6yWxvZLS8d0VMqnRORZGaUp+4w57+boYcPyw1bArD8\nmPLdc+gSX4+hmqkTt8+GmIbXaa7F5lEfrr5a5zR5cnaPq58+8iycFDv9nbWA\nSQRyKAXxgGlV62gM+K8FOKTUXqbHp5ecKgQ+Bk9o1jm4fJWeNBO/crG4ne49\npmHx\r\n=HaQ8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/primitive", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "96492d91becc0f4d4b9780a8d40ff468cf3f312c", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-GOfsLhrrMs6LFJn84BdZPKVfO+I4J1pzxpwp16Swr3uU1QPZdD+3vawTg4RUaM1aV3AIxr6jPDcL0bkt4q9E0Q==", "signatures": [{"sig": "MEYCIQDLw2dimXqMoJpkr7v1BOnB/5Ua6ZKV9XcnQ7dGkLXp4wIhALsH/l/8FhJAaXJ0Fyqge2qdSGeKy00RICRZeZ1r3gY9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vbCRA9TVsSAnZWagAAPcgP/0XlfiQJllOTlBfqMkmF\nQ1S19vtvNllzO7TDD/ZSzoalwmWpSQzXl7q48klgY6NyooP8E5x4jEYNhBDD\nj3uBIIuEzqjNNQx+JJbb+ED23WjY5Iba15SqFnk4EdlbRo6KZ0hUQ0nXXNd8\npgRl57hRm11WbS+pk+Sw7Uer6xh9LSnZJlKo4rY7agFS/HRbljsLUFPR0llF\nBJFpHcrUW+Gl9SSU5LGrlD5CMaPmUKb0dnRaA17d9/Q8ZMjVAK/WWYzHrxzD\n/bZPVMvrhz8Yj4MjMZz26Wx8aS0E1PYOwutAWPhuaJZHBBtWEaIfS9FlheD4\nk7ngHj3q6LiCHHIE/YJBgWgJW3beMYANP7KYo49PstjLIyt1+uUK1LQHFpNh\n3ZWUoFgXvUKnW7HunjTQXAKIgMHDbpvEgQ0uVy6Bg8k7y0N84EqnCcZROLji\nh0pQrcGzFhjvlqPYKy2HvdVcUNB59QVHlLGTMOroK7HoKHzgmdlEOZaUSgK/\n9bDKXCfXpSfSuaExbwcTpOhTlt0wC0NMxO40LqZgVOtAoLiUTgWzA2iMAG5n\nsbmRPLpi4nWUb92yOe5CAx0fWz1kwFpGyuMyH0x48Po9VjFmcthfq5DI88bY\nFhfQemT86d3hVhL8w6Aw3ctj/zYqVT1lbnhgCvSzSjA62+1Et+RxH4vTLb42\nPXrF\r\n=v+eU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/primitive", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "8464fb4db04401bde72d36e27e05714080668d40", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-VeL6A5LpKYRJhDDj5tCTnzP3zm+FnvybsAkgBHQ4LUPPBnqRdWLoyKpZhlwFze/z22QHINaTIcE9Z/fTcrUR1g==", "signatures": [{"sig": "MEUCIEI+J38CJ8MEOaEAlD1AstnrDBkmvv8XWJrx8NkJvx/wAiEA3gu+/cSu+Y/NRH3Vf+GRP2psKHD6oCnFWyoQZBYPBBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3636, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmXCRA9TVsSAnZWagAAOtIQAJ1CKJs4hyyrz1sPmDPe\nEQkzIejxIiq3lGfiVqeeMSDaJh8TiipbmY4RNCC0sT7QBlhKHrIhmZbI0TJr\nWZ5zKZ5Wvh8zlcJeo5PKrCNMk19Ffm5lcYugHyp9LewQzUiaHGqUaIJqj9vF\nlsQ+uWLCK/Niu28kJ35u7gXgZygsdDa7Ebn8YZFfsz3tODc15pfQ41b/Bqu1\nJgcWGlTDY9ZVmm3mcqAGHLfGQxmWslJKNUeE+0Ef/gUTZeBKknFSSThRANj4\nnfxZlasIFeuOiR0+vH9L9BHbUrEgeH9u/xQybw0+QSWT7xaEfO2zdI6w7MwK\n+VMhw0u9/xsM7qxmkWum9bYQ3bYLIrXSNiF/ROtcbRhjWWNFv2Qm6GiJUpWu\nSxa2D160KqX/wOdarR+C98UgwRT9UVkuuomSCbGAF+DlFJ8/jYUf+VEOPTof\nXGvlVxBqyIrRjKp0Y7nJmvvLjkjbYn7dvzNdvXS9lIqTuv+tzHQYWr145f91\ngiF2z1ghK6rxoitm2bBfNnQ1V4X1DYezq1+U1qPHaR2lVCBjjmAQsqkGBAsr\n0oBSjks7ACKqL1tp8SjCXMP57jsOH9yMyF1NRHJtLlwIqTOVAfV59+XbRc01\neWRMZpvj3QB0pdzZDd18yecPJJ4k+j8RQvYLuSlprWUAPFJ2MWfON9Qz9d1q\nUNyS\r\n=oe6W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/primitive", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "602741f19f50f287bb6d17165533294bdc9bbdb8", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-ipJuTPmNbWlDP9/1Tb0ANIOFNQFhNZU10hMZApNqcohqOyis9zKjPUpL3obPmHbAczE0ki0FZYjfReUtfFcTlA==", "signatures": [{"sig": "MEQCIDZulNU/N94/PuoGlKpff8ENBhTxwszvuSYUZjGOMRBvAiBlQT3FioOZ/CDh+jMayIErnROWx0ppHp2A+b3HSmBcsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 879, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpMCRA9TVsSAnZWagAAK08P/iVg5dEOti6hf25mx07S\ngh/N3SPU68BhNw14ZNeqjEyYBlZpGJMnLW7dmew+dfmu1Q5PXEG7XsODPG1w\nhEjLz+bWsM3vzVGxiiYp7mNjlCEOqpBSyamxZGY4w/HSe7niVambwqXb6xDf\nCVCVXWr7FhO9bexaNnO+17q7eafANE969yE4TQWstmbI3iK8krBJedFrlCX1\nRyeUAqtc64kSL5twERyGUsKryOokwhjklUebsdnPOAxeByd1RfzvZ7lhMZde\nAhzRE0HVDsfHNNCU8Qm4bGSzl3WieaQAqgf3bTAa0vLDYLIvaiojtDvWs2KP\n/eeRp2mJ5gZCKe3gbAUO9l4v+cOZAN3V2zpF99V/K6Anvsuha4BRxqKIj0A/\nrvFH/MtH93U6tWi4goLqwaJaKsZ5maYX1DS728Jhm5Gc1OeR10PCVbFJVCgu\nFQkMbLjcXCNECOdo6EsSFXgSEJgPlcbJUNurYz3g6kw6YI/XWf9t8xsbaLPt\nVGyTpXX/LbrUBRmEoh/MUiKoWJ43dRI/DInLfUIhaemL8hXOkAjHBDcAfodp\n/9aOSigvm1PIPcrNxKDelOUIkjFjVUnoL5mHSf0t3vSmxatg7n3ioWV07Y04\nDd6tHDgL8HAtO67mHBgGPwvfGjq/0UcKqh0TRB+y7vfBhZR83tnnk2vBH3Bn\n9hMe\r\n=DS/H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/primitive", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "548a37a09eb57aab391e920bb82e630c586b310e", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Q5LTUPgsbrYCnlH0BVVx4K+ogCczvsfdm8sruMm8U6LXzZJZiuhmnoBb4cQfks4dScmEXy0YCer4Oe4qbG1Sqg==", "signatures": [{"sig": "MEYCIQCyUE4e+82I6AD9qJG48/FPyTEm2MfFLJmLnFYUCmONzQIhAInHkD2Mttj/Qbw8WbePsQnn6kdEP1kN0yDa/TCX8bpb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyFCRA9TVsSAnZWagAAh8UQAJQB7gNTyivGayWJCvK1\ne+5/I3+NTB1xk7ahtapzV4AQdSIEY/dqNNVCbaFYw04/qqeNyufFRss8tmcX\nFTwgmooYYqY4jeD9eWEms7vNemKw+eBcEfoNJvlAVKIqxBYaKpHiwy9EqXpP\n5OwsOuTldx+zc5Ybjch9Zi6B7uBEuTX1QI5+OdzTiILZa2ryh9dFXifpNils\nf+kdwJYr+QBu7MMdeKMXAGoQ3mZ/P4mMoRGpkRa6JkAU2npJokw0p99sTvAh\nfQQA77OFrheKgB/Ij+e+VwjdleVCTGWn78rT39N4+EDhBTcKtkowEtbHQJg1\nsIczqmzdow4zRfJxd4nuhVwTuuLdKtt/y9QGSuAV2gd/xBSy5WZjiNhh3I+Y\npNSNGL2tgHi6Oyi9JW9bWGyxAVhGjHg5tmMzJdjEBsSJcztuiehh7tRw8ib9\noC3IEfZu993VHiL9D6tG06as17y79TPggIXsXPOrIY6XKwWGcro2BlxSFt/t\n+YGSisyiuJrcdvnVdEZ9GB11pMnK3wG3XyIdPhm3wlzA0l0hfnBbw7Lm56NT\nPrADsA0m9Q0jN7M+1DT4WK62LzOrxxqQYv4zXlxZmjF4xOlW+6hFdRkVl3u1\nvv9GpeJhomwfsJxlM/L8MnZRVArl0EIaUf9EQflxHrESww58F/gOQjC26o7+\nm4mV\r\n=tpCd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/primitive", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "6206b97d379994f0d1929809db035733b337e543", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-tqxZKybwN5Fa3VzZry4G6mXAAb9aAqKmPtnVbZpL0vsBwvOHTBwsjHVPXylocYLwEtBY9SCe665bYnNB515uoA==", "signatures": [{"sig": "MEUCIQDrAKqgfb3g2XT99unKOCiUex6Z7XSbxa+EuFnA7MsOsAIgByt+PZz9bfPgidFwbZ1/fkf27grE/4tYieE1ekOaSRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmSCRA9TVsSAnZWagAAqRgP/316FM7cw/mfQlBA4Lct\nFhW+03KWRB7L4ELzew/fpYrDeLIBSc93r6E8UUkb1ytornh4rzjQEmuYYZAO\nepz5LD5eDxHRhfDO8gCQqCiezvyQj05LFpHz6E0DUXvzSpjNqVXS4XIK2hQ0\nQFCBd+HQaK6tX8iMxUoWym/RxOoRjC97BA2ZS0gIp3pvK7OHjFIT62dW60r5\n6QrcVXLdak2+rk4c4SENk38pmK15HzL8eU926upQhoizOGsh0u7lauI721OI\nBxEkmQ9hErEcCG9R4NpUws4hNh6DRSDPa4xAoXWw2eBs9W4ungFEufCjn4/Y\nULuLIW5I7aPzcmcSgh/PQmb7mlFgoNTiMVO+N4Q0RQjU7zcO3Ihnhc94Uio2\ncwtZOb0OKoOOltRedHpdoVfgbMXuHt1eDKyh2yLg8EeKZymKtbLhobVP10iC\nGi1vfMRz5GENE55LrgxjP2b4YF5ZMHTv/34z68DSrsTBoXEF+rXfbu5tTuRB\nVDSiGI30/7mZuNP7Y7q46kpNY+htbfat72ELKu46pD2fe3kft9xp9jT4JzK/\nFzdc4bVPvaDhpbcdF5vwAbplYxUnPo8ZsUG5DDkI8ux+Vtb0IPlt4G/ZdeTM\nolx+dr73miUVO1hTDmJfIGPEuoO1ckCKDuA9yxsMK5wG0T+KIEsx7otoWg9m\nDAXj\r\n=PAoV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "3f5c5bf2c4b4509bbf8d23095c046d51b858564c", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-eutJx1W3AXycYN2Lwbw8mrikXhKteEnEPWWn1rrZE5HekXtU0S5n0pMpKvBKDR4IoUcwS1Pu7GAr8utFDwXdCg==", "signatures": [{"sig": "MEYCIQDOYkiPvchw7T4Zm2i9O4SmLqUY0eghoWCys13ODJsM1wIhAMzsFhlWlce5Atoidi8f7hqiVcF4ZeDvE4JgcYFDx4yw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaYsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiXg/9FW7JhcrTSZpgN1JxjxMFmH7G88p+fKSnCm6VAIkkHsRRTarU\r\ntRydBGI2trtTtHZ6stFmOX+tW1aisymsRn3Wk24QWiIg9s99ImqKcMFbNbfO\r\n6LQNFBl2005NUAomsIZCfskpB8Iu7uZAkcdSu8uOWP6NCVnLelHQawjZ11BC\r\nBSnOhqlFC8OkS4MYzHHnIC7CmzFz15dgHofXh46nBVJHabnAsr84mxIoXSJs\r\nqCNTJuPVXZl32Tz/ewokiv6sqWkAWeHJe+YSvrP1gh/XcctQUjsMwYuHjTmI\r\n5otZwmY3zPlAQFul8wbZNyhe+gvROb9Tfzv19cZ7UqN7IkZEXCdWMReNIYmY\r\nnUSDROdvY7Rs3uBlN54W/EBNjCrbu7pHm9kmfqRDyUpReIrmMUx4YaV1F7Vs\r\nUx9vxdAjmdZ3gYXS78qK6QQAiOyV9PQtBa2s3OtzbQBgdnuQRb5s6F+SHzne\r\n4ZnwWcpjLJU0GIkpFwoe3w2EWdUS25mZLgcKtxPu3S45l74v5t0YOrRXUbCB\r\nbxnWq6Nbm7U4Kbdk8MG+fFHGrqeM+t9ddfEO0RfrWBctnlIRYfyO2huS5wM+\r\ngib2bMsMrHDi0XnhmuPiMS1O5NqaB/yppyPSErOSBzJskKxJ9DOVrN4kCO5a\r\n8Wp/CKFm0pirGJUiffHKBE1BJX2YiWjWOpE=\r\n=Co6h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "5fff5e631f7e249bcfd35b6979fdc2e3ee0a8e79", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-5v8Xi3eAtNFQqSUL16nCg/q9FIA7zh+dHJXtZUWRkXLhYYUbBXprBglm1/zYPNezoVqJoJC4M0ZRCzJHNl/y7g==", "signatures": [{"sig": "MEQCIBL7rxDqeHk+RnyiaXsp45G4QCQf9q/48RUHQ5STM3wuAiAJFb9vxHD1pdD9rk41QL9ZRNPbD6OXPWiC7Cq9GHMguw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvc3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqR/Q/+NoX37+KlLEKz5vB+0Wasumib2nLsVnUgyyhAX5YyEcs/yU8P\r\nFLkTGvsW82XYsRaMDmRJfHIRdarzhmGv44G2CzLLwPzM5y7c/kvw+HKh10WP\r\nv4BJ3aDMV2+jnBGyHzB9wb7O0q9wruzTyKOeE5mI/jHxWxSNA/N+qe9bs9nG\r\nrd5gfksCY1+syGWYqX04fzzMbv8EDG5mtOaooAMTjWq9LH8/+8+l/87c0tLe\r\nqfpWDxQKqB2G+kLZFjnUDR9Y/9qXnRVRzzX7YtgfnXPdXbP4CXdXzjftXBj4\r\nmpr8CuaBq1AVVtX1GgIIBE9nIAwAQj3oIwIcIxnV6AOzRLApXQjuWy6rIITZ\r\nO5Ue0H3F4d5IOF88gmkWYAs6dN1FxyVY8ypaMlOku/gbvGNWFK0Vgdu9l1xl\r\n8EiyRFajjAYDY7mWOX9CQtf+/vzs3mMyu+xTTMVtM3huEDS4hqzCe7v4YmpJ\r\n2OgZHmBwGcCevvuDDXmPvKkrPSVvq4I84uMRUpQK46Y7utMV7dnWxVCgp2Hg\r\n7zj48vv2Vetn4yWzLnDjxPsIoCvuv3gdFXF2pD8XG+16pTr0PGXK+EfItpXz\r\nM23LgdscjbTB+VllbNkonVh8+/6kN2VaM3q4OZSpz2dZ67yYoumm6SqA/NBn\r\nTFM9PHaI2TLiPJvt283WB2qRQdGcirZlEJQ=\r\n=YYZy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "f505dc5331e9a9a2711e01e856318b267ee5b550", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-IICTfFScKCxUu7SURN6jCgZPsP/7EjLFHZzmVgp13iO6FSICFzzvy3Boxi8nZoja3mTJn0RB6spRgzNdyjlxIA==", "signatures": [{"sig": "MEQCIQDobBMtjKtX7VwlbStqi55T3DAbKjVz/ZpU5JAAK2IiHwIfCn7Y03LqyjQ2dd2pVScf89q1lwRgPzTSOKg1ojVFHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp80A//amoOZBzb0Wbg2519wq4iOE7WEqqos3EuXsNqJxycHbrJbEm+\r\nUMgopsdyVzhmgsC+HCeJE91Z2YlmmNqZn1VfPN4+SEYsZsl10K56Oi+EmQRK\r\nmAxn1tdxlAI0mXUjbxhuNTyFOzBwXLCzn7KU1a8JOi2f8oNdnY8jYKLQQfYA\r\ndN+Otaannc313cBkIEu+ik8v/nB+CRqmftC+tBDVb1T7ym7B41K3tbXBGwwT\r\nCxsZeNkcKS2UDU7dW/xkg2jSt5+n2sYBK53fTQ7+2SuMvsVcKCv//nhz4w2B\r\nAmmuov7BvLR7MtEpCyclZbEM11lTjxnpdXZtzIfpSbTys5V5aqi+Sp57VqE5\r\nrhIFwBT9b5AkB2jbczI4Pz7vIgpw2waIJpkNVRzaC4tDjpJe7qfxFRFSPWF3\r\nAHk8Hy0g9dfl9UL+dvWuUHSDwV3khQ2zw33zeuMCOwi5R5pZB78vKf8yfyjo\r\nFA0i9iQB3a3V06PVfA1kSWMdkKPtwTRMXk9/0pephySuh7HmDmA8/n8Zricg\r\nn/dTQapENumWaqqc8tchhkwj20HlLZIk24tx9WQiLabha000CuE5SJFJcstI\r\n/hzpfiolc5cYt4j9nSbN14Gz6V9SIdN4VadPnD2PqdnPjroTkkNugGvbWx16\r\nd4nuieDDe4LAJBPgoeiQv5X24dYSFMSrn9I=\r\n=5YGE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "83be001554da4c441f0d03bf6bd0434521eec00e", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-/Qke7/BT/aEM+dtLl43aevyyoQTgF5DL6oxdwNffwz5LBA/UDNv70IiudJUKxnsu/+svLi1CREI4SDhv1qfCHg==", "signatures": [{"sig": "MEUCIQCpcXxDGwWoimklUlf7uKAJkCcNM8iB0JXXLqikNaej2gIgQDByl7EH//uxb9jCNJJmY1+kBIeWDv/bG78w7T5iuYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XFlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH8Q/+LeKJ2Q18CN8xd3GbQ7WNcz/GxQnwzHLm1zGAN6l7TCgQCvJy\r\np8osjVXZh94qlOBIYGWDSMgk7UBG6cmjnq6Zd+SawUVZV0mF9AH14hcwfLAJ\r\ndceXDQTabAzdWAIM1Y5N9AoQmTA0bU4oDEHr3tkSH9ztxpKHMI25nMZMtyqB\r\nTc10252RLXMMxKI71a+wIaJWChOj3M8EUBnwcagCrYf+rNPgVy1BrjoMvmHi\r\numLpXCh/nnyxdPj+8PEh6JxU34WXv2zE4QLpYIyp42WxxPp91GbjSxSfliw3\r\ny0QAKdg+PKCJGQQmY4zHa8c81goGZZnmCw6kw+NeeGzIAcG/8X9Fx3AgWAhV\r\nBsJBW8SHnwCNgjerpQEgOFylzGyfABwxgftGgpfn3wmpAD50rz/IVNgLadqW\r\nYlO1WG0z8IQanBTHnYnfvi2dq8pl+27i1m1bGLF72Cm7QS1tSUW9pbwpvhC5\r\n0yOFvHTmfG8PYRcqyjZDpYMdLpdq1ZbamlZwhj0REeRrotJ5rW83XcalQitA\r\nMnWED/RD2B/G3HxWrNa8DuStNwxkW1yZzzhvketdVxJScb9Tq+FqNWqSzRAk\r\nWnrBenRO0W2QQ2HutP6VYC4sM7PqmfCqwrSkI5AeRsl6q/rtUpw5jHPiFdsD\r\nu2uuLAyW6TRPsDe5Hw9COEQyzbgqDj/SxjY=\r\n=sY1y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "5e957773f63d0fda875e9e3c433ec1b6031f57f5", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ky7Fj+s7lgE8oSFpkvigASYOd8QAHKTQGjvU07hKGMmZXXvP0F3b6mb2TuPVrP3FJlvHSb/mcfczVtDd2TZIvw==", "signatures": [{"sig": "MEUCIQCsWEcxx0/tZ/wu7zmMegu2M9Ti2Zrg7AWnxcGGOngfKgIgDpIQa8dg4QmD5XCr6xF6KAHv0Kvh07cLqdCfXvOL9gs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8WA/8CJipkB6A1pGM59dyU+XPPCiKPepnB0PRyvSvKPtybDkIw/oZ\r\nGt/3YvUTaH9GPS6S0Fk+KOOfxb5F91hSTaA3YWxykF81mM+QqhM05P3bpcwt\r\nV8q/Du79aCuBgFIvHw4Cen6zQFMbsjddGAdggXwhyKJfAwXzC7aL+s88qxUq\r\nhT/JuBJCa0afKymuUEZ8y5xWP5F6eUcPfhY7JHqAzr4YZJPpZ9y/nVjayVzf\r\njGH6jis+s2cuCdUdM1DhBMrDno4JMCvcofdhuZdP2earrbjYtpWJ4WUFanjQ\r\nlTMnH3MrUXXnLsQarMQlSsfVwZeqpv1nM+wXOksgSRoZc+/0BvztqulAshQ2\r\n85OKv7Sb9k5FbmboEQ6Q9lR1Nl7mXZmlWZiq990R4Dd7vUAYYzZO8uSLWvai\r\n5M5QFZ0of1wBK/NMOq2lJBv0Ic9YJDDhV1KrJ/Tm9tpacaxYCW6Xi71G2psc\r\ndmME7UDfVtGoV/jn1ZqBbV4oiEx2iWO6gzpqazK1ttaCgHSALfhTb1I886jh\r\nd4LpYcl8/tlkFVj7qJqpgQPrSrt+NIMYX/obaOWLjK9/MdKQyoHrUQIYmX5e\r\n79g1vS6AGXETJ+Ld4ROXDsTo9LpQsXmVA1KMrWz3GjVZgfcZRZSPTVdGis3U\r\nddz/ZinMTVc7LcBu/RxzWsth0rrmYLOKRgQ=\r\n=SjZt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "f36d4af81d39a8a8b9e56b098fb1c3013d684078", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-LPz+fwdiVONHT0S2oiu2Vm597JK6/WsIli6ggbUC41rclQUcSE0Iv3ehAkwkxroSOU1sYtEJ/O1veFZPwyTLDQ==", "signatures": [{"sig": "MEUCIEuRUyykVu+nLAVPSmbYsIT2XDoxqB+6KyCkodDftCK7AiEAjqpVXs3mZNHxAktqvpMWir/vd/wGfkhyEegCmn4Wc24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi196+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKrw/8DTcyk8ljXw9M34rIsMivQYF24GNkXF0Zd6rG/0AtTnfGLCZ8\r\nK3DGpYlXb6NZi5OFnvIxQE/diLH9U1voLzyYzrnbIKnFNlycNbvhixiCFaSm\r\ncpQ+X/hu2obv5kwZvcOYAHUvFdtR0ZebNh5IZu3grNICW6AUgGqp+IkHoXc4\r\nqfomeH0fX5MyyOc9ZPOZDWDboW0bZFBGNL2xLkuU8ATAhgMAIXd3u89nkjry\r\nkPJLxgO63HxCEk9QPN0HQoo0N4+qSteGZ4+qjasBj7u11E6mBVwG67iTE8Ha\r\nyhBgE6P6hZu8hVf5SQivBiXAww8yO998y4MQJMGlGIz/ZxCU9tgDWPbTqP3n\r\noLB3/RVnj3raCIPCl9jzfxATOBM2lXj3RBMWNlaawyt/Z12dujxU53aD0bl5\r\n5FUjSHKvghVrIdutABN4leVHLDE/4I8VO9lFBttp7HhpGEFMG9oz8JZMbJ9m\r\n7C71tyahDzMSJJECVy+jW/ov0PMask2a7PMwOMewN8imEUASgf7vHBeAxoo2\r\nGu+iW40z8rev5xU/cMNOZhVxBWjBnxwujmvTsTVXdc9PhhZpO5QukwBrQdIj\r\nSByG3kfa7TYZD4rWbzI5oWXdNFIXsB+FlF4AK5A7guxUplwQ+r67LQie2A2r\r\nZc59RK4wZ9ZgpOL3NnzTTTWY/ZsZorUb56A=\r\n=MUlf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/primitive", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "8aed9b94d81a6dd24dd48de62f2f98aa045160d2", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-JA0hJY7t2ZACmziHmtO85b5f1haxt9ptWJN9wdVePehfui3ZS3SqpL4asA6lRvvpiLDOl+0y6i69siHpeoUtgA==", "signatures": [{"sig": "MEYCIQCkrJdlADe0P/6ub7SeQQqdaA04VjDF9/jLQumSCfhPNAIhALnTahHvU4QwYQlzErNzOGsoVmVLv7NcB1/uHw+Pwb4/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CCrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphFBAAm29BMtPrRu9p0kfYBUvTDTUmAcNgYHTsfal7OT9AbY+Sd7xC\r\nsvsxTFVUMojslpZe0CXRnvKSxpCD7/aLfJayWcahmf5PEvvwrCqhfEKqdvNu\r\nZp5SCryx7IeS1fA6XNgJm6q7HMCJACCEovLIhpIm7M3vqkxsJ3pQRY1jDuau\r\nyRZVWkw0v+00eiexXW/uRVgl3GHa4RbXc5ghbIILtT2K0AYg8uhPTkrmcuvI\r\nchxab4t7buQdxAdTCJ9iiJhSTENBZ7eTINBRd28J0hZe6zNje2PKxRZjwkZT\r\nfTsqesUvgQlpCT8iHTyJ34mpdUp01xxmXSa0Bb8L4rv0NeVGiPTyN/k3AqUp\r\nWik/eX2e++2/70C0WYqtqprZ8FD80oOCa/ulqkf526js6MTxyy2vytpci9mP\r\ngXkb+i0zyW0NuAPwDR4TMOGCesQp6fSXxaqGyewQTTyYfD0nSWsIHRCMWJNC\r\nhDE0qshUlBYm4K4zi2xQLF72i5Wp3t5tBxnhLXvnCkswqaSQFSlzkLkKGsC5\r\n9pHiO3DHdUYqMOeR/LHeamPW9z8ML1QTSOH89eHk0fVfIG/eGyZX+BvIg3p1\r\nZU0rYcAv/HtfpBXJzOg3sUNK68F5SSiQMtlpy0CtOrTO4n7ktB6GCKXweYFe\r\nfkWio/pts+pkls7QPvXIRbvekhtTJtgif+M=\r\n=ttHW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/primitive", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "1decf1ab5f4eb65f5450a37c911519323dd54a40", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-UgB3PWXblJfEryAE/tDUMIz8L54SpqIa1WoNjoaCHFT9lkd92BT76dmeKGLOVBYCq5PqzwAnIA0ALOpjnVwnJQ==", "signatures": [{"sig": "MEYCIQDupzyDYngJx9Q+vXdT9w7sTLZp2lMHYtBv2ANrXl4v8wIhAPLwYaddtIRzaPQWGoCg4uyqitBoyLAzLxk50QfIB5k9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EuhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNVA/8DEJMKIoZQbzP8VgoN/rWUexkmLRJsSnJGBcbnLBuZG6Lg0yj\r\n0ZSJcNPvKI27G+CvadrxOna6Q3jAgHkG8NvGSfEIrAPsI4eLBQ+SzHBBV6Xl\r\nVUKqdvKXjGHhceJCKoe9GXVRy2tQE2phHGjLehV6QKysFLAnFxozHBLz3YrO\r\nHTrt8maOwNAs2TSE1UUZByxlUmD9BL2PzvfNIttezgiLpOxipdwuFUqcu5vJ\r\nITYYwVhA0+tUwB3RW9X3mAk9Q8522q5ZukCU0EM9q25p0lOhL+XEt4Azcdrz\r\nWI6YfVpA6cQV3JcuRlqDb4eT5H+Ovxz9oKEKl7j/W6XYTrcOn5NykD4gt2sa\r\nA54tFGxN8QfaFd8zysJU/5W0fWrgSAQGti1WaKzZakHSWcgK/FgZNR66qsGd\r\numzAOg2FysRUKRLY3YAxQ5c9FSUQTYsu7vQ2daI3CVxts1dAM/nKfsIrp+rc\r\nKpYzHD7wD1o+jks132sAVrjSJo7JJjdYcbzNS2WCsr7jElMpao15jyqA7gWW\r\nSVOlazyA63OjbHvNROBPbNEQ5QPFwhWGsikCNEVy/2UWTJKd6ikyn1s+KiRl\r\no1mifOvxOG+WE3bPpwk/Rgpv1H8XodJ0u1fKfpE2HsPXPeqVvOe56/mW/0Mz\r\nT0bgD59tu4n+J4QV5MTqgB/pe4qtx4efv/c=\r\n=wg8a\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/primitive", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "e1d8ef30b10ea10e69c76e896f608d9276352253", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-3e7rn8FDMin4CgeL7Z/49smCA3rFYY3Ha2rUQ7HRWFadS5iCRw08ZgVT1LaNTCNqgvrUiyczLflrVrF0SRQtNA==", "signatures": [{"sig": "MEUCIQDmkMDP0ZaTsygDjEj2g/se6cu5Kh/O0uCJSSgsLlQZEwIgCW5wPbNOwxXrlO4S7zDucQqiwg1d8CpcfYl/6u7+lAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5381, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTmQ/9EKoXuxt3MkpLR7UvArozg8HzeQCV+S7p1qcRVEvPh8XEkO6m\r\nOZMizmmC3In7x3baMtJY4Tiy0cHHX+YvpGL4vnF2OCp6FEevMivVSPim1ngi\r\nEQGf37eJipkc+HIy3rjObWZnUNp7E/nXS+2WjQP1BAdCs/h1FzbgqdI2IZ7V\r\nFbSTUYl9ddJzdDPRNdscXPvApEcs+jUIdJW02fTQUu0T0lKXxiotzfU2gsA4\r\n/bRaCvWZPZRYSqks/vbLnnJnHuAqdffEbRIzARvXDPol7L3YLojvgy/Pp/Ai\r\nu5nxe/O822Q2tpogzbqrqELYvxhiZC8l8CSdZLSxhErEJ6MGntu6OSUFbaR8\r\nw+1JC9MzCdf/BcAYUj5jPuo6/Ob9GOxWsHk1Jl02e8YCNK/x5emgg1ZvkY7j\r\nuAHQxrEG9VRC14XswQhE8EDep4DY7m0rj7iULX6YjunRBw9RW1mCsFR3wMhJ\r\n/o3TVwVZ8EF7Ee3imC2xXNJYBhSq4+zTFSMioPStffqSGR5r+es4Waq032Kr\r\nO2xyq8029qA5c7g3mKrLXoH4WZQWNCvK+/GvPJVO6VKHRLlQqm6eORF9RpTD\r\nekAHLhwWYh2lOahiMBqbp/BIqXUNb+jYAGH4mcCZoaVii2vKbVzsAtX/waZo\r\niZKtaQRDHgOyVYIw37n16UIzBrxTlV3c40c=\r\n=7Ak8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "fc2dd598fb1d7c494b95de343e26c77b35fbffd9", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-zRBlMYgKNamQw/sgSB3+/IzxFyQg8udDvlRKQsLoDXeepEH9KOzSl1HEqj7fBBr9nLFySDE/2+SnwqRau1yuoA==", "signatures": [{"sig": "MEYCIQD4QCzGOML9b3D0K7veX99fPrKhQMTGfb/5my6RcerMJwIhAI4XF9mHtxSTv8Oi+lVc1EAV8a+qk9X9iTG3b7nKvS+c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1-rc.2": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "3994eb545bd5fb280d85134b69a5e236bd8164c0", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-jinsJgXqxJj22yY/qpHfHE9naCDZ3P5IVBLYYRETJcLoQY11qTuE11BrOIGEaUpFEJIfPKZTktXF6fvYVnPr2w==", "signatures": [{"sig": "MEUCIGXqvlo8sOOs1Ii66UZM5YYCQ5smM/gPst0j8yN86j4qAiEA+qj0VwQIKFF0OQ6aMeGfcGetyEKEXpDgbp1l1o7RRdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1-rc.3": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "f26e93a2b236a297f512ff4ed4be6923281a1e51", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-/gCLOUch7BND57N7lRTegmWlDr56PFKfvYs9VpElwtH1lL9lMnovXTwd9IFbKf1eznbyA2ra6Va/PkpGUtFojg==", "signatures": [{"sig": "MEUCIE3/wu98FohMHrifQ9u+Ie/IffHUMS2bsb6J/KVMFs05AiEA1xEqk0gDnqoOwqH9IS0sBldr3yYddcUo8Ec6W83oMmY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1-rc.4": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "5ef1cb76da4ce7f8762e74b6810c9d11500f71ba", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-VUNxi4gA0GBfMImYC7WMpoolSHgVTbIfFpPulMvvUpYffMQpZVZgSwrxSUQuAAqvC3b5HO+M4j5iw+tAH04Q6w==", "signatures": [{"sig": "MEUCIQD5QfXYpyQq3c/L+ydMFFq+zph3KDXDM5g7SaoMvFFaHgIgFm+dvMmZb7fn06GcOTP6/LlmsvjeKUgZOORd+7MYgCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1-rc.5": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "0530338dc71ee24075567858cc0addf546377f42", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-4YRGB3/EZwFFrcsFMiIg/oYdrN8mEeMbfs0dUOuNXW2pO4hObhAyc24ATZ1NsSppFBFPiGKGR33ISOXkeM14wQ==", "signatures": [{"sig": "MEUCIDNzKDYGZO+9Pm6XVGsS3WmwSfTkBSDGDGGh931pshNEAiEA/GrgDaddGFtykTRgydj6dIvFZ+VHHiNelBRCAw+yrS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1-rc.6": {"name": "@radix-ui/primitive", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "fbcebd8479b10aa8c8f4bf9145c3bda3002dd344", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-7QjO90mGf5qLZU+zQ3jahaU0dgfBSqJhsqDxsbV65bgjsFLN/KzauKA4pYBoF20wpv+OJZxItZw54H9vuZbMtA==", "signatures": [{"sig": "MEUCIQCqancq42iFKlzeDVC2bpxz68CkRldqu0eSrRqpEG5maQIgTRG8vV5OSUZTt/IvynSv3DkUOdsAOXit0nrPP4zTmXQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5914}}, "1.0.1": {"name": "@radix-ui/primitive", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "dist": {"shasum": "e46f9958b35d10e9f6dc71c497305c22e3e55dbd", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-yQ8oGX2GVsEYMWGxcovu1uGWPCxV5BFfeeYxqPmuAzUyLT9qmaMXSAhXpb0WrspIeqYzdJpkh2vHModJPgRIaw==", "signatures": [{"sig": "MEUCIQCzj984IU+ps6ezij6icL8JA/bCaGfojPvj4ejNKEljrQIgf74JcO8d7kPzsabwCSPFBXVZsVw3bvUGapajEgHWob0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5881}}, "1.1.0-rc.1": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.1", "dist": {"shasum": "83a98bde3944e5b7b8f75942a5e564dd35f637d7", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-MZuLsaddP4GsZhCuMlMAwIfWSF3CRmi3zPF2VaPEFcn93bwmcdfo0ETvPj8TmSfeMePSibmsShZzWaderP6ovw==", "signatures": [{"sig": "MEQCICq520dtfT4hQR8kBImdzgEwbDKd5n4An25QBE7/Q5qlAiAwPMqBqHE1dG87BMnaDYkXwanDA+n0yKxCxWmEoDGBeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3944}}, "1.1.0-rc.2": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.2", "dist": {"shasum": "5f7b477f32ddc5ee8eaf9513f1a12758affada70", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-e1VmsIoITVIx6bv4838Ywt6oO08fWZRJKC+Bf5EwNHbU+I4c2uf6w0Hm7lKZDl02ofo6Hn2jIvTFEUm08zEQxA==", "signatures": [{"sig": "MEYCIQCvcw/YvbgL9AxyXsO8D2ksTngo7I6H7sXmUAGEWSJu0gIhAMkwV9WFj5mbLTeKTri9J5uU18ET4SmD2hdKoh420WpA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3944}}, "1.1.0-rc.3": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.3", "dist": {"shasum": "0762084d8eb9e68c6a25a3856885b6318edcbbfc", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Au7IDm/Loq9yb0NWf0t6Fwn8tHI9fsj4rRy5TP+oDz0yVYZ/nGh3K3osun3TRm2O2VXW/gp8yqUkrBTXaEJ8QA==", "signatures": [{"sig": "MEUCIHWb8vlFAYef3oaLsvMQCnzM3HCmm9fttbf33FVd/wUmAiEAwBCLpOVFaQ//GhmSD7GvYel3ORCRcdWPf6ZSdclsJNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}}, "1.1.0-rc.4": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.4", "dist": {"shasum": "cc89f991e3073a06129152c8000e24c719030e43", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Kt4hUaNP2G0IDEB8tBmYZjbDsV97GMTuzcEtTS1cBSaXLNXElwcI+9APn+KZkkFUSFTtIBLrfjEFP1JtwME/cA==", "signatures": [{"sig": "MEQCIFV7tf7l5x9Slf8BXLORKRbnclTH6xwHnYk1xz4EevmlAiBd1ubousdKliJl3/0U+iJxaYDu0+tfqmbynCBwffTbFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}}, "1.1.0-rc.5": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.5", "dist": {"shasum": "44948b6f125d2a45202ffe894c7d52ef8176e36f", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-HkKmS0yp/8OLbljRUUBN2bQeWGmBuiEyd+Ms9LWnb1eFN7iKu3c4s9I7y27Pg0iHxDqc6GznHAdiv1uTHTGBEA==", "signatures": [{"sig": "MEYCIQCYe9ja0GLry4wx+Uh/L/k7X7nVIr7QKkZCwB/PG0KzwAIhAOWoeZptluE/H4MMMFzVsnQiu7t26b7MIbZS+l3lsLb4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}}, "1.1.0-rc.6": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.6", "dist": {"shasum": "f10581f6e1220d3ace5f7b6b9fd7f795c5d1fff8", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-npVN3RUkSsu6r91nv4k423oo1tYkWo759rezA3EUrpDr+kFJaQ8u7AwtDpY1yZrllcPygx72mN3O8CAnV+Rpxw==", "signatures": [{"sig": "MEUCIQDPYeFWuP5H4uIIbfx9PVEN9DweQVYZ7ye1XHRAW3B9eQIgRNBWOR7VwrZmvSVaeRaZCubzOXkgoF0CNPfifaUJEcE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}}, "1.1.0-rc.7": {"name": "@radix-ui/primitive", "version": "1.1.0-rc.7", "dist": {"shasum": "8f8e177e454e58b8821565fab4b26a8ab381de2d", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-7wY1m2XQwkpNC3bOGaVXZC+WnzHicA7Y7lfhig2VAuWjH6QZrS/Ft1gSZabgr2Y2t2vT9A3yHBMX+B3ahU3GAg==", "signatures": [{"sig": "MEYCIQCg7z5WO6Spr30K3QbFVHWgxFRRPDe5IqbfEtVM0ddeGAIhANdp1JGhViKfPfk9PgUZ322/Ex/QT2OJ2xm50Gxj5lWa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4998}}, "1.1.0": {"name": "@radix-ui/primitive", "version": "1.1.0", "dist": {"shasum": "42ef83b3b56dccad5d703ae8c42919a68798bbe2", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-4Z8dn6Upk0qk4P74xBhZ6Hd/w0mPEzOOLxy4xiPXOXqjF7jZS0VAKk7/x/H6FyY2zCkYJqePf1G5KmkmNJ4RBA==", "signatures": [{"sig": "MEUCIQD3VAfsoNvSv4xSeJdGeDgkg8HjdyGCc8sKCKq7UjaQPwIgL1qEbnAfSuPOUfx+uu18m1wUYpdyjf75dtZ5OIbUeD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4965}}, "1.1.1-rc.1": {"name": "@radix-ui/primitive", "version": "1.1.1-rc.1", "dist": {"shasum": "5619fc3b87fca5ac2b3ee2061e24d5d515f6243f", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7E5mxXestDtAQa/dnz5DKmHRRluTLZzgmZ3k+199jdOHZBPGlWPC35KzK1UDgh9nkTYCWTWmW/PoOqCiIUXvtQ==", "signatures": [{"sig": "MEUCIAw245blxlSVVNwtx+y4fLpujVR5mE5NmRGL3tp2qUYcAiEA1SANwdAQwbcXfr7nQJSPLffVUjhziJhrTnhARElNnFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4994}}, "1.1.1-rc.2": {"name": "@radix-ui/primitive", "version": "1.1.1-rc.2", "dist": {"shasum": "bb0a2409290f570fbfb5e0209d7ddaeafe4921bd", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FNKfV9GgbsnIJyhI3urTmuTwecXYGN0WrZEA7RmcEOWGP2gR51a30b0WRmffOwz7c/jEPetMiFZebbAiNHsUNA==", "signatures": [{"sig": "MEUCIEJi8SoFP99+jJP2Rx9g5bUTQW10ssxbc9OULfT4/XiuAiEAi8XJthvUZXUvAh3MGLnSbQVs9IFHRlgGb4sctAxIRUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4994}}, "1.1.1-rc.3": {"name": "@radix-ui/primitive", "version": "1.1.1-rc.3", "dist": {"shasum": "899f114745803cc35df8eeb861f708f89367b8f0", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-DjVmkEKpoTqyT+TNDwvjGZRGFN9IWa5RRIWqYHSUToDaZ2hnsUIk7fY2acMoJleKSrXBQjnt/VGjjKYqKoWgxw==", "signatures": [{"sig": "MEQCIA4Bfy5QRY/uJaKZmJJzxXM9m8EinwVxU7YL4k7F9cr8AiALrLUyW9iIcsaQHMnuffDCKE7W2Niy2EMUFabxvwfwwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4994}}, "1.1.1": {"name": "@radix-ui/primitive", "version": "1.1.1", "dist": {"shasum": "fc169732d755c7fbad33ba8d0cd7fd10c90dc8e3", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-SJ31y+Q/zAyShtXJc8x83i9TYdbAfHZ++tUZnvjJJqFjzsdUnKsxPL6IEtBlxKkU7yzer//GQtZSV4GbldL3YA==", "signatures": [{"sig": "MEUCIAKlBDs1Qs9U4rP9WfUcN6vgq4QCvoYhrYxMoS2oTeudAiEAiQk9zdBdHQlLTaXzHrgro3RgpUoJPlrThR2RTFXY9YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4961}}, "0.0.0-20250116175529": {"name": "@radix-ui/primitive", "version": "0.0.0-20250116175529", "dist": {"shasum": "d4180d0846e7c9a9a637a719adddeb7284c6b069", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-sJdi/6gxarlBsZZz+ZLg2OUoMZ4itgoKv/bFniZfQdMe2FgnuzW0ix7d0x665GPT3mR1G9xyDHTW4555TnyBlg==", "signatures": [{"sig": "MEQCIFsrO8wR/nq0AHZlGw9NKei126n1js97ZerPkMyyCdc2AiB56rKtbR1DvVz58ln2opUtIunBvyrLcHm8g0Z/AEJC3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4946}}, "0.0.0-20250116183145": {"name": "@radix-ui/primitive", "version": "0.0.0-20250116183145", "dist": {"shasum": "9618c8f51e05868dfd5ba05bc34e218250228b24", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-O1ZbqDDj8lAYEp3JD5r7o/pPecYKcPrUT0F3hPNvLNK5rT1DDpCf1Wr0frvcYOamoLlGk78LOGiCiGOeHMQjAA==", "signatures": [{"sig": "MEUCIFZa7ZUwbSqvcUaEy4HkTs1B9BFzb72QC94qk5XLQMxMAiEAwB9aQX7YYIqRRG/qzozChAoUUOO1Kqh1xE8nd1zSPEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4946}}, "0.0.0-20250116193558": {"name": "@radix-ui/primitive", "version": "0.0.0-20250116193558", "dist": {"shasum": "2084b0d76143197021e47c89e2bd10803271fb66", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-xC81aDQ5qEXBr9h5iIkZPSMPV7U+4qLfZYPCy/LPZEelnoSuqv79dk9x47st0eJN61LQ2GZ174XF5h0vK6GfMA==", "signatures": [{"sig": "MEYCIQDiov7aFe2PYJkADjt7gJY0KZgTZEAk4nxRc34+j73e0AIhAKkwl7MAEtHKjvJve8JZpxY7SRBNsVAj7ohq/WnmfOG7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5028}}, "0.0.0-20250116194335": {"name": "@radix-ui/primitive", "version": "0.0.0-20250116194335", "dist": {"shasum": "7bc459e236e837b9c130a40602ad178c729109c8", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-wci+isNsp1Ye4N5uBaaxUCIhZB7C7BrNK+3HZ0VBwjHX3CaKwyu29RG+DCkjurky5Ag3OXl/eQ1OQesH32C+4A==", "signatures": [{"sig": "MEQCIFuW/hAgZPbkR1o/MWNWMElqcGQKuJOMjI4NQHl0y9YvAiB/khp5lrW4mHrbR/zn6gCiqEGWy3ZOkXqvN4jeOgJ+Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5028}}, "1.1.2-rc.1": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.1", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-bsFChApb6XfsvPxQzDgIzj5m6BHweqglU3ZwzCdsjeYnw1eZ2FugzZyHlcXJ/l6S9fjlU5ohiDcoT8+US4kMQg==", "signatures": [{"sig": "MEQCIBw+AzruLPQpnSldfoHXXc78DzagFogbscrux+Ph/v4PAiBhyiN2jTVCpE4MHfXqm1Bf1N4Rt2eoMSJTsN9q1d612A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.2": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.2", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "f4a380ed79d3a6c5834f51b17dffaeb2ab43d3f6", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-wcpXM8VDdlwrL2DBBlbLxMTOG3fdX9bS7Bd3ay5+wIwgae+OI8r7kAMheiMrt//ftEzgnMoDVUe4lHokieEv9A==", "signatures": [{"sig": "MEUCICdIxgwFqShc3cvOdo8pFlb9nQG01Vvt3A9Ye4XobKYGAiEAjx5PTwhL9l55D0LmjbDpKFxRbkIdTjrAlQF4q4UjoOM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.3": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.3", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "c53a4ed36b061e24ebd9557709f4ead06ac3a55d", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-k3FmH8MfuS2D1vq4x2UJNi6LPZJ0k6OruXFKT6jNAW8ztYCdQGCRrjmQHE7pd+GkBV/wCZXXOOlgMc9umoaeqg==", "signatures": [{"sig": "MEUCIHJluDu58j/gwra+IdH6wqiw1jaGNUuUgOtZn8351foyAiEAiRCJ1aj72DgeHvAQ8TaT0rZuXTNv/6paJ/JvCBQ4JYs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.4": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.4", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "430aea4a2be363e2b2976edf99f5e9f7ce7f985b", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VdD6AGumchI/HXZIMVBqU6KpxPwTf2xrElCWI7E2JFGRaKhBYGDaqqwlIhY4/IlC8FN/4ilQfJV7UMwIRNdxfQ==", "signatures": [{"sig": "MEUCIQC0I4R2tVm6D1VZSiXM/AqGON5QzvxnUKaNKBjZaYzhPAIgDo4f9Fhrf0EfQNnV+XZdd3u+kwGrs23zmpNMjNpFyhk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.5": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.5", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "797921bbac11bc4e20f007c0c94064e5b28ac1c8", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-UOhNdbr5JjlhxwyxbWbQl9o3L23L+d2hSzQnBICbOs2EehHrulCggBPlNQV495kiREIh/l4AWfaOJ/Hg8QrLSQ==", "signatures": [{"sig": "MEYCIQDpOWRJdZL4FPzj1B+4yaXCt5iLQRi8hDoQUXPf4rBCJgIhAMSsm1AgnGf2QzVT9wjHo55d/UAJPxysEr+5Slb9mO5f", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.6": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.6", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "2f39205f97730c12cefc1ff177c6815b8b5b8b90", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-TDft/1lNb93JVzODE0gCim4q3wMYIh5iQpFP8UCLcpuP3XSw2zrUGw2XofWaMpNm3X28ov3Q0U52mZrGJdXtFg==", "signatures": [{"sig": "MEYCIQCHOiswGJ4IB8ME44//RNVvVAMXviQ1I1BZFNEI46qmbgIhANR2IB4H7GL7Meavjxo1p5stovHXlR93oHvGSp/CyoXD", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.7": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.7", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "a7fbc6f509c9bee8302e6239d46bab49108087a8", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-f/+i5irjfMxws4L8p6F64OmMEjEqunp72AG/Ql2AB9Ol4H3fae8YF1x3OhBB0aM0sWpE7Fixm8p+Ns/LlxKQYg==", "signatures": [{"sig": "MEUCIAE6r2i3PDzGq0P0OVyBjJNuQT6tiOV4hLz8fXAAtYm8AiEAhhnW/g/cE+HhuqvHkVnfNe9WnQ5Ji9W8if+EUfq+Qzc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5199}}, "1.1.2-rc.8": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.8", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "fe13909f0a4e38131e94a4e0af467a0df04d9a25", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-yRQ1XY0eLtRGXshMVQ0NZOUKpp7+Rc/2ha0mEcqgUe7+mHp1z6InD9nRiYP/nl+6YTQQEihs0cgjG4IVUH89iA==", "signatures": [{"sig": "MEQCIFVTmnpnM1Fcw2xne3I/+NX7QC8i1KPO9b20J5ZCm//0AiBdE1ngnHsXko0RlLx5y4HmRj0K+8eHIFCbjPx7KKDQCw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5706}}, "1.1.2-rc.9": {"name": "@radix-ui/primitive", "version": "1.1.2-rc.9", "devDependencies": {"eslint": "^9.18.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "dist": {"shasum": "3e8c1f87d85c0268303637dc6cad11423c213034", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-uyl/NWE3CeVAT/FNKvB1bUoRa7qe0nS7QKt0M10lcdnH9eAmCIUr7G31E2eGC0XM8pZWVMgje1qmtam5fQdL/g==", "signatures": [{"sig": "MEQCIBfGa/ROL1dRL3DhGBPQJmYJSx64rPBH6IOhIizSh+lXAiBGlzNojtMoPv7vMS+Bvb3RcfwiOJMKygC8723RLwAWNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5706}}, "1.1.2": {"name": "@radix-ui/primitive", "version": "1.1.2", "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "eslint": "^9.18.0", "typescript": "^5.7.3"}, "dist": {"shasum": "83f415c4425f21e3d27914c12b3272a32e3dae65", "integrity": "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA==", "tarball": "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz", "fileCount": 8, "unpackedSize": 5673, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDsHSat/9DYQPmuETf5/XBKWCmcwYW/fUFPc3wp5Lk0MQIhAMuVsodaMc1MCTU40kenqIFkU6la6udTeLmlOlE7CxVH"}]}}}, "modified": "2025-04-08T16:46:02.718Z", "cachedAt": 1747660589250}