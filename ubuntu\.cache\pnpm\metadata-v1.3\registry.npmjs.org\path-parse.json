{"name": "path-parse", "dist-tags": {"latest": "1.0.7"}, "versions": {"1.0.0": {"name": "path-parse", "version": "1.0.0", "dist": {"shasum": "365a0ae12131306628f0958afda62397c3bf0901", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.0.tgz", "integrity": "sha512-WiQ5m2YR6tbpF/8F2H6cMrMUHTN7p72NHgagdUHxkNk7poIFkZX8wH0qcKaoXWcqUtAJnDOj/Md/XQoznsc28g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAm4t8/Km4560faTqCrkY9rSnouV2znCOnnA1ckz0Y65AiBcJfvnL79KdDjPEFDSIXYhyVZ4Tfpaedvt8EtOYobXpQ=="}]}}, "1.0.1": {"name": "path-parse", "version": "1.0.1", "dist": {"shasum": "d9ea8a3571bb8260bcdbd1a516f931c81861c7f0", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.1.tgz", "integrity": "sha512-bFmTXmJyk+1ArHy+kLNOB5FkkcRBkpjaLLeLasBg8LtiQzJlucOWFFAaIKNIG/xHQgQGDItSv2XosUWJfFOxRw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDCTo7Z3xAB2ZpvlP4MXOMESfLH6v9VBKN/ofMqwTeuSwIhAPuCkw9kui4yZrSFtK0IGCaM+MP7NZ3/fG+D4FEIxPgF"}]}}, "1.0.2": {"name": "path-parse", "version": "1.0.2", "dist": {"shasum": "f1122f5c88200371ac27eff5c2643f6aea0bba56", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.2.tgz", "integrity": "sha512-CXn/si24iuT+aqW1XBNK21B2P800wHI4POQNbmIlVvVNCY9KACHZRcpVheDDUvXbXJJKaFx0HWqE7COcL4jRqw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBtDIIxuVOyqV6jMbSu82lzaBDnFmSfY7iYnBT5u/nSYAiBN9XitT1QsK5B3M1zL5/3e4sjAoTOHkewg3RwoMoFkiA=="}]}}, "1.0.3": {"name": "path-parse", "version": "1.0.3", "dist": {"shasum": "9b8e6f7675b8fb214a14c5dca48cd7ed5159d6f3", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.3.tgz", "integrity": "sha512-Jv3xjEYlAw+HwapwX6ViiGg/SWj6kanI6xguTZxOiDMm5MW8UkLVMfD4cR31nvNnSrilDwCazvddA28/G4cPmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCIRKS7ryIjMCo5uprDTHEEuMC1YzMmEZDlkQSYv/VYUgIgd/5X/1Nxvo4so8nLlpiw+dAFNgmmDLesIPEJ16xLWIE="}]}}, "1.0.4": {"name": "path-parse", "version": "1.0.4", "dist": {"shasum": "219d40738b7d249706d7d57ab71f3151695629bb", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.4.tgz", "integrity": "sha512-AsptKwPee8q8v2VL4hc0gQK4YcWBRervN7fFQjl9kQIhYt9bPLwLkjFck0YNvX65e1iJMHPKgn0Tskn+gDGSmA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDyIDHCHtZ+iDLlU/B0rqQr796SPu6Xdy/Vp9Y4dqOnUgIgbsq1rXmVq7eXZlYUxPJlbqrGJGj1Z8BxcWl9rMHFabo="}]}}, "1.0.5": {"name": "path-parse", "version": "1.0.5", "dist": {"shasum": "3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.5.tgz", "integrity": "sha512-u4e4H/UUeMbJ1UnBnePf6r4cm4fFZs57BMocUSFeea807JTYk2HJnE9GjUpWHaDZk1OQGoArnWW1yEo9nd57ww==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBa1YpYaTekp3K6LL9PrHI5p5Pr/FryHayqprAP4lfQXAiAT9ua86D1rz3zKhcl06QuYArsYPgOhZLn8RoO1S8bxjA=="}]}}, "1.0.6": {"name": "path-parse", "version": "1.0.6", "dist": {"integrity": "sha512-GSmOT2EbHrINBf9SR7CDELwlJ8AENk3Qn7OikK4nFYAu3Ote2+JYNVvkpAEQm3/TLNEJFD/xZJjzyxg3KBWOzw==", "shasum": "d62dbb5679405d72c4737ec58600e9ddcf06d24c", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.6.tgz", "fileCount": 6, "unpackedSize": 9029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZ+uFCRA9TVsSAnZWagAA5GwQAI25pLPhwQijTCAsGUSO\nMWrhq2ReoJWhubV2hGPMRIhX7A1v1ov9AmZYutpxnRggBx472fzbMJ5aiQg1\naL1fONHaK9MDDY87zSWRKOAJgvawncbiY2apXJUnBwVNaUeBPTm42RA9bOyO\nkLTbPbQBNTXm7js1U1a47+rOLlvHWusPP1A1+5gPbqr56XqIw/Yh7fz5zH1M\nzNKX9yOlWp1hTXosNQ3TQqRWGp69OqUw/a2B2ddvGre/bv+oDmyiDCFBNPj6\nyVXRkvzThxoVSjkcjspz/UEFZMaFonc9I71q04hK9Aq53USD+ZK+dsLFMaLL\nzgZeiNfMJur2fyXSEZwSccHqCbZ/5uf92egs9zbquf+ULyn2bzk7tZb1QGQp\nur0sH7fFQVoLgSLlmnXXv+uawNY3f51oL4snI56Ik5Zj70u6nKJYIGgIpBj6\nlK1FujTUFojzic9hRd3uc30LLoNbdJHF7dL7FqVrM4bW9/NKmuPsLl8Aiu9M\npH66W3tXW2WrzZuR7wAD12Rt/6n5XIrNJeEjP9l6bEx2tsTka2Z7TgaVXWCD\n2dVCyzk+Tp9OvqXOi+7IAMis+NlmZjtdVJ7js5at12DH7iJY1gaYvCH6b1+C\nAA1T9+EFrMd8di48t20EZEppXWm4nAvRa/6lHEG9VYuK4reR39V/Gn95xksb\n1Md/\r\n=QlIN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBjCHJPlBJrL3tTwOwpjfZobPqPqISEM0Fua1qvzmA/wIhANjEpM8cWY38zz4fq/IQBotTcQXk0CENuEpm06HznhmX"}]}}, "1.0.7": {"name": "path-parse", "version": "1.0.7", "dist": {"integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "shasum": "fbc114b60ca42b30d9daf5858e4bd68bbedb6735", "tarball": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "fileCount": 4, "unpackedSize": 4511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrPRBCRA9TVsSAnZWagAAUDgP/itIkfxex1Cu0wWJbeHX\n0cI52kJhy7AnU8vZiD7DI67DvTS94Vb4y1BYU/8r9JFCRJpt1N2XKqbZL6iW\nKjrewQ/xbphevDBtzwLecBWYsKBV1wrTzEkE01BXL6BJDC9l6uqw7xChhSWM\nKF6ZdFep5hdB6+X1HqxSXB80biE7BkRs/qx5Dck0DvQxf92Rz/pGiodPPa/t\n/7oNmkmWrBkMa3eqY5pmjo5lbVfqpkxoLxrE1kgaSf8DPAh4Q9X2hq5DJXtK\nOzVlztBiEKqxDSMrOyibvuE0VgLuz3IPa9WE5WM3gFUJIcXwpVYx2jZW8Gna\n0ob7D/JHS3zhGFJIUwEvpBVLWmUckRBLIffT6BLUTfGxIHmA/sk5DCKeCO5y\nP63M+AuoodwOWlDS1pJ5NYdgM3YK7LnGMcXBYwmDiyTncfNHZuYCIGAIYwMO\nu5IjfGISmZblQrkd32ifxS4iRZE8g5deGriRB0YiexwmmJSRDlTW2s/GbHXb\nLIZ4FaX1a/fm0IihxOfU4I9UNYL+QZlBtXwsaTVxHgng7y72xW/G3pKucKj6\nPwTRHAYKxuZ67xx860w+kHMMEN6Bwi73h+ebEzqLFw0HP2w/ycWcTKtqVmeg\nZfq9nsyMK+Bq2fVRZMSJ/DoSc/vCuke8YmFvRJXzDYrW9VCKgIYmIqS22api\n4wZB\r\n=kshs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF4/Iqf87RtJ6beozakaFQ8WtCYJiX/5aFTzVdKrZwSPAiEApvZ8kI585jkblcQmr4CU7U/4jDcWlJ8Y7Hn8tIOEpsk="}]}}}, "modified": "2022-06-23T14:38:12.990Z", "cachedAt": 1747660590356}