{"name": "recharts-scale", "dist-tags": {"latest": "0.4.5"}, "versions": {"0.1.0-beta": {"name": "recharts-scale", "version": "0.1.0-beta", "devDependencies": {"babel": "^5.8.21", "babel-core": "^5.8.22", "babel-eslint": "^4.0.10", "chai": "^3.2.0", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "eslint-plugin-react": "^3.2.3", "isparta": "^3.0.3", "mocha": "^2.2.5"}, "dist": {"shasum": "252a67cf51053026574a346e527d7a3803f7f713", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.0-beta.tgz", "integrity": "sha512-A+ptEE9odznuHaO4ZSVE1YqkWh0RkjrIgOUTPQH3RxcmeFUICSovH2ceCQ0Ng11cOXyln4IMkAnquAqcM55GEQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGN/hMsRFUQNzjwkE2sTw1WiUQ7w4DZhns609NcTJVVJAiBpvruRLcx7mNeuaGM4o+pjdy1K7brOQai24kobHqaofQ=="}]}}, "0.1.1-beta": {"name": "recharts-scale", "version": "0.1.1-beta", "dependencies": {"ramda": "^0.17.1"}, "devDependencies": {"babel": "^5.8.21", "babel-core": "^5.8.22", "babel-eslint": "^4.0.10", "chai": "^3.2.0", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "isparta": "^3.0.3", "mocha": "^2.2.5"}, "dist": {"shasum": "1d1cc86fc486c53c81f774338f190a4840301844", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.1-beta.tgz", "integrity": "sha512-KTfZNE34WaUj+3Z43caGJxhXoz7Q/Tiy91e08EQCL44IC43U1QlOw0FHa0fp+YUKewUwscJEBX+if2/YozirPQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgW9kcFJY8RqKWzryT5608zD1QKpCe4t7FnyaPA/AbSAIgEYJsvr18BEccZ4l6WDoR0bEpu3D/2l/zIThW2TKDCuI="}]}}, "0.1.2-beta": {"name": "recharts-scale", "version": "0.1.2-beta", "dependencies": {"ramda": "^0.17.1"}, "devDependencies": {"babel": "^5.8.21", "babel-core": "^5.8.22", "babel-eslint": "^4.0.10", "chai": "^3.2.0", "eslint": "^1.2.1", "eslint-config-airbnb": "0.0.8", "isparta": "^3.0.3", "mocha": "^2.2.5"}, "dist": {"shasum": "3e299b262ae6a61a1b1c300e9e9f4ce5cec3d307", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.2-beta.tgz", "integrity": "sha512-QELd44L2N3HNnOPe3+xh/ovsMRP0vXr9/QSSOu1Jaxb74pWqJHLEFkWEpZ9GtR2d8t0/1Ap3ivLiqQUzEOPjWg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9uZICOzZLLR3T0qBxFr86UaucXaQZbzIVpI27l9LyzAiBM9G2mEVuw5j3PhFgHuzR4tGPhxxYUIQd1uI1x3YWHTQ=="}]}}, "0.1.3-beta": {"name": "recharts-scale", "version": "0.1.3-beta", "dependencies": {"ramda": "0.19.0"}, "devDependencies": {"babel-eslint": "5.0.0-beta6", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4"}, "dist": {"shasum": "463e721520fbb543ca632c334dfc71b81e1af748", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.3-beta.tgz", "integrity": "sha512-+2eewGrPGIr5Z+xpXOPczTBssUBCzkp38cWXdCyyDxNuhfc6tovtEYqwh0bpO0v2xMjtjwvBbEDoxPUro7cffQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCo9mEgNLgrt1GNyga2UWqBGEZsZk+EepTha5TCTSg9lgIgNVL2rdGdF+StWDcOnN69A86pTYbKNcGXV3YCSjj20jQ="}]}}, "0.1.4-beta": {"name": "recharts-scale", "version": "0.1.4-beta", "dependencies": {"ramda": "0.19.0"}, "devDependencies": {"babel-eslint": "5.0.0-beta6", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4"}, "dist": {"shasum": "4ea01a2a9fa20c4d9f578aba55295e1c33d9981b", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.4-beta.tgz", "integrity": "sha512-7DJUHmUTLTGix42I5FaCtSaE0nEZR7W3FXqOkBZIm4cXBkyYwj5xLFbvbMo9YScHHPIPMs2JOZy3AkhWKzS1UA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC/dh31odL/kohJNMGM6ITmVPtIrXX32w65YvafwuXF2wIhANCnKfas4DfY0JOtHaZ1nyViAWdpBvRDai5Ktp+2slfL"}]}}, "0.1.5-beta": {"name": "recharts-scale", "version": "0.1.5-beta", "dependencies": {"ramda": "0.19.0"}, "devDependencies": {"babel-eslint": "5.0.0-beta6", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4"}, "dist": {"shasum": "9ff542cdc9d4045181c7b3cc6d0f86246768503a", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.5-beta.tgz", "integrity": "sha512-w1b01n0mqlJsR1yEGG2drwQi2PKLlWe+/m5bga2fc5s1R12HXI9wdg/NHBS2qduh2K2UABycJo8/92OD0rdG0w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCkzeFsUReL6LfV22zcR6O2dOO69bQ0TdB4w1Hpe89SoQIhAJIYs8m1uqHDGeWxZ5X5JKGfHszoXIkdC9rNn4K7Ia94"}]}}, "0.1.6-beta": {"name": "recharts-scale", "version": "0.1.6-beta", "devDependencies": {"babel-eslint": "5.0.0-beta6", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4"}, "dist": {"shasum": "de2a6114be523c564615b4682d8635b8e8fba374", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.6-beta.tgz", "integrity": "sha512-bS7udPVbaG+mtaBIXEbKaVA9ZtRHRgYxQNuQ/WJcKD/CdwWjqXnITQJ1FUbpQmXaMDax9dlpxeP1RsH4+WwJoQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBpO+YK+WQ3Gc/P6qhZWqUKtbvpyJkMusFhWPASVvwBsAiEAosoT/w03VKCbpg0w/6xIi2jYeyLIGI7zZBaYZ4wARmI="}]}}, "0.1.7": {"name": "recharts-scale", "version": "0.1.7", "devDependencies": {"babel-cli": "^6.4.5", "babel-eslint": "5.0.0-beta6", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4"}, "dist": {"shasum": "72758edd655192d94712c8ac78f5f99cf0b47864", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.7.tgz", "integrity": "sha512-O1SMlZ/9hSJ8hDiGQTH7frLhP6FJC6V5TF9g3O5CyBN24TD9x7Nuw3xRb8hIYuDka3p/87xIIeX5zZFAZ4c9Hw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIANXi+Lltt3/NJqzrW5SdA0n7vnnXJ/+MFEX1DqB0213AiEAj3+0TKOEyL6T2dLnMZIE9WiVfTFiwsMLjpivMAH5zpI="}]}}, "0.1.8": {"name": "recharts-scale", "version": "0.1.8", "devDependencies": {"babel-cli": "^6.4.5", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.2", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4", "webpack": "^1.12.12"}, "dist": {"shasum": "2ccd251016558631aac0c0e65ce3b519b85c23ce", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.8.tgz", "integrity": "sha512-ByO9o0V1r007V/NWjc5gcEponTZouBQcMpTuYsVP5++YqZivT4ZJ2yK+EL0b/EdaJHS+EBh/LOs7C/76m6D6Tw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC/Jp4OPCIo2vG40WCKSVowO/bxAwdYYOvdSO8wnP0ITAIgPk9WkG5cSbFkRjI91UIeSMWXXP3vPJgRdpq6yP3Au4M="}]}}, "0.1.9": {"name": "recharts-scale", "version": "0.1.9", "devDependencies": {"babel-cli": "^6.4.5", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.2", "babel-plugin-transform-export-extensions": "6.3.13", "babel-preset-es2015": "6.3.13", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "1.10.3", "eslint-config-airbnb": "2.1.1", "eslint-plugin-react": "3.13.0", "isparta": "4.0.0", "mocha": "2.3.4", "webpack": "^1.12.12"}, "dist": {"shasum": "f7031adc80ef14cdb0028151f0fdc0600fed39f1", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.9.tgz", "integrity": "sha512-rj0QB0i6dADulnaCh/bg/3iv5SakQNTa6VcpMcOcNhtEzlWCVqsnXIako1FpT64TCUgAfhyge8GCEmledJ2K/A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBmM/RUoIozU7+yJucA/IPltAdFty6AOzP7LVyUmIR9XAiALvH7hHVvfbKZs1OkHMaakQZZ1cUcIDPy1FWdzB0mBug=="}]}}, "0.1.10": {"name": "recharts-scale", "version": "0.1.10", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "b1ec66b1ff1272355bb636e2ff72a7846ea2be6f", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.10.tgz", "integrity": "sha512-aN5bw7wQJ33JLflk1w0IOa82QnzM3+RUAwZdRPeknhOD4VLn4h256jrOomQxr0yt+r9cXUIfpNe7ifRwlfGjyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID5FJDZKBQa9f6NpTTJt+WmkpKVXD1ApUoeEhRB6FIWTAiBN6fzCUyr7HuMHHRmADQOhsGliPxe91IChzqIuJy07VA=="}]}}, "0.1.11": {"name": "recharts-scale", "version": "0.1.11", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "1fe3eb3db1dd877431ce731ec5d5adb7f6807bd7", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.1.11.tgz", "integrity": "sha512-49huhSocmP2k801xrpU45Sk3RkRGQedl7hcDQTvh0vZQGP2IsmzDPSJAVPGkfWzy6YRzYKWOwnO9aYChNtAI4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAJG0J5poPfqKWM3YV1DFh9G3Ko0ejzhy9v6WqxBbsNEAiBHKEGODRoretNZSaLWF79T+0DbIRQECluYuiVrrOuNqQ=="}]}}, "0.2.0": {"name": "recharts-scale", "version": "0.2.0", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "3d9d1f619d87f99143904145c0ff3c3a0f17268a", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.2.0.tgz", "integrity": "sha512-YO4xme15P9Myrb3ABIAeQamQssae2bDJl91jgLiGOR29X6kQFQ1iooDnq5V5KzNjWbQJzlH0CFABVc3aG5wHMQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCm2ZRzLk/irJM02GbPfcV/wjGXfV0idKe5e+dVPHtDywIgMSv/8mWB43uDx6PvlIDL31Hld03UwHXLYDNSBTDsePA="}]}}, "0.2.1": {"name": "recharts-scale", "version": "0.2.1", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "378f543ed17c3245e4d9afb10aaf6298240d2fcb", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.2.1.tgz", "integrity": "sha512-zzcRDbUKlQEDPUAhjMbkkred3SLVV3jMS+90SYME1HzwV3ZpF0ojca58F02JKduCpJjpY1eI0irqOopEfrtQQQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF8ygAV23dPcfuEJy4bw/Y4sceS2u+T3vOUGZWlIBWJ/AiEAtmrF+KOqOOqaDf0FP6EEaI8wnLTiuJAn2mS5Dne2F2k="}]}}, "0.2.2": {"name": "recharts-scale", "version": "0.2.2", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "e1f7feacc6a96ee570001909b2655881a5e68443", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.2.2.tgz", "integrity": "sha512-q2P3JGtFU9zf5JptSR6fNlYFkz36SRBi6VkBygvjPowgGJhjhjc7UG20aDw2Cr6dcnTsVfCih1M8d8n/Cg0MCA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDyimqh6/C0fLu6cuUoLdGW6P1GNurq0f4C8qHPRDxqpAIhAIsxGFQMf2sMibSZihjHLkX+jRpfgxELATP7y+Zoe74d"}]}}, "0.2.3": {"name": "recharts-scale", "version": "0.2.3", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "20d903a00b1758db0ded3fb2dfb370e41b958287", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.2.3.tgz", "integrity": "sha512-kuEY3QyDPgdXKyTS8LlYD4RN/539IqAT0KP1ZnPfK3hZVoMaDOxpMuRATG0yJmuOlUUom/I32o6mjLM4/aDIJg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDI2ABJaHmG8k5crmiaAKGwg+Oo14oXg/cfYqYi961BlQIhAO0McZjY/Za41GwtPZxKMF/WplRJljTRxCu15QYeS6cq"}]}}, "0.3.0": {"name": "recharts-scale", "version": "0.3.0", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "d17e8c4055dbbeb0d65435c9142ee0fb9054758a", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.3.0.tgz", "integrity": "sha512-ILYVQrxszlZkbcr0OCZoXQg/oIZQDTyqXAwvphgfa9FAJn1XIMPQyBdamXyD2lPPL2czowFXQUMPcuJjxoxB2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsvZ2jaBTh2HsdPeFR1A2OgU5U001ft8+Exwh+XhXCPgIhAP1ra5xMeyzGcLQ0BC/XHbLz72E4KGIm0DXRnmx2LJNd"}]}}, "0.3.1": {"name": "recharts-scale", "version": "0.3.1", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "635f14c998b151d63f2e8fff1fcbe46faf0423a6", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.3.1.tgz", "integrity": "sha512-SyUGV1ajjMPbsCDk/ifvbY9Hqj7xxG2J65uW36HNjh69/qAynUuaHHHahRE8EEFrOPlC97CBX9EhdmOOzKKzwA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0v/hb4bbvGD2F8CjGY6yS36smp9zpGy8+vbFIASq6FQIhAO2Z2V0hKiLHUAP1s7/9eeQ1sghh7mypZVQgjcskl0ZC"}]}}, "0.3.2": {"name": "recharts-scale", "version": "0.3.2", "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"shasum": "dac7621714a4765d152cb2adbc30c73b831208c9", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.3.2.tgz", "integrity": "sha512-CuZK7cUzp50yRL3A1QOOlMRKsJyN/GmKXwJ1lukxA4gKTmJnwnyBOtMup9eWlCzj8b+wSwWvw6bMcq6MRnmITg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDkfAi/XPHnkmOchXQBCdcmWOA+9XE41rbW2221VswxigIhAM/2luzBS0aGFypqW8yya9CSQdZTss/bsc4MejGjP7Kh"}]}}, "0.4.0": {"name": "recharts-scale", "version": "0.4.0", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"babel-cli": "^6.10.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "babel-plugin-transform-export-extensions": "^6.8.0", "babel-preset-es2015": "^6.9.0", "babel-preset-stage-0": "6.3.13", "babel-register": "^6.3.13", "chai": "3.4.1", "eslint": "^2.9.0", "eslint-plugin-import": "^1.7.0", "eslint-plugin-jsx-a11y": "^1.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-react": "^5.2.0", "isparta": "^4.0.0", "mocha": "^2.5.0", "webpack": "^1.13.1", "pre-commit": "^1.1.3"}, "dist": {"integrity": "sha512-+XRGUAZsw5Fs6aiosBVcmu76JZXmb1HLUMFeAPzAfrPlO6muPNEXMuC5fXOO8Sy1bZ6QVJzAFY5w1nXXZzqB5Q==", "shasum": "a8dca2b32abc4c68ee0a214e6376b7af3e04513c", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.0.tgz", "fileCount": 18, "unpackedSize": 153789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrEeICRA9TVsSAnZWagAAraQP/26+bEpTLRKmy2LYaef7\nrwHjxb1zYz5cv9mKg6q7+YcARKufEsf6YLwJJQ/tnutwP4DBSThdQYND8TmF\nU5Z58y9YaxL2/76AfjoP8ckClk34cEzCoZVkcWDMro/+4G5pkcFWYltkVUO/\nzn69Gtq7okmMtxYVj82F6h7bCfPp1Qjosc0RpT2FASV2OXVhbvLPnc5MoUgg\ncxmuGH+eNB7ghjW6w/dCQUPBzG2dhfyInxpc+U4S2N3604IdXkpAh0lAcMMv\n5zan91CERplma1++TQ57ZXWMbEpm1AOz2m6Zj4H6HuRE/CludVpOnCOZnjyA\nuIO8yV8RzboSUAnk+ycyV/9mv0jCR8srAsdWqynruwSnqEoVNBtupw0GhIlV\n8UDMieMTWmQ8U56m17jVrJFIkSy8VxEp5RYXOuLTytpE6Gfr+j+/IwBOuwni\n7u4H4IRxf2MS51xG6cjI8GW+ZrBhyyFfEIwf7PNeik2DVEsH7M3CBQLQmnRv\nP7sUKr/mq45IvKOp/dUxTKp/pN4qVoAOFH2A0X5u/10FVB3asSKjTxVQn3n1\nlgcPUfFiXDGeVrVAy5CqjKmtF+KVD1MBbNMAfRf5LFLn9YwgezUeg2skLYpa\nhtIBQ4UPmCLBpJ3uYmYsYbxcoQ2QqRR7wV9uNXSQoAwaedPb8MtveuEUhuvP\nTcS7\r\n=Za9D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBBeA0plwt2tNflnRBUizm3gT2sTR5oUpATmT2D3yNhQIgFG76j+OlnNeV7DX99QELjs7hhpi0rXJ5JqHULGjT5Oc="}]}}, "0.4.2": {"name": "recharts-scale", "version": "0.4.2", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "ava": "^1.0.0-beta.8", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "cross-env": "^5.2.0", "eslint": "^5.6.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "nyc": "^13.0.1", "pre-commit": "^1.1.3", "webpack": "^4.20.0", "webpack-cli": "^3.1.2"}, "dist": {"integrity": "sha512-p/cKt7j17D1CImLgX2f5+6IXLbRHGUQkogIp06VUoci/XkhOQiGSzUrsD1uRmiI7jha4u8XNFOjkHkzzBPivMg==", "shasum": "b66315d985cd9b80d5f7d977a5aab9a305abc354", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.2.tgz", "fileCount": 18, "unpackedSize": 96626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvZ06CRA9TVsSAnZWagAA9hUP+gM0WlW/ujw8s+BzPxQW\n+4jxKU4ZgGvPiTylSCDfbwYIjzWzqHFua+ZKxSZ/eOXdXM8dyRC34o2ernz1\nk2nx0QYAPpkp8iG7dWYxXa7rBwIEPoITxrEmLiGk85+l4RuV+vAbJPDrwoYK\nwvtJigWG7rU7EeazfGhTDWx721db7XNJHwg4mUN/kFheiyid5vBtzRVIkpwb\n4Ji3uUb4wZJ0lgeMFjqkUB51TQXBkt1fBBthVu3Rk6RiP3ZOZitOWtp+pJRg\nhDWJn+JfkbOrguNw0cBcde6VMfs83MXYvL3oKr4GjCeo+LnXH2MZlonI6uDk\nw5Gv+nKOFlIBMVbXs8N6+1xHnop2JreXCU7tgMnrD+87ikf8XgTN5N7a6qdd\n27Mdm7CnM52412T7In6U770Ez16kORLTqov05q1dXRHRdLSS9iRLqr7VgALi\n/0raH3IjB2uwu+bzpVwrPgkKINjdzwIge5zhCMHXIg3GNhBSI0lJnR2FcG0R\nIwsVTdfdQV045rnXynGlCwSXc994v63d2a+BYkk9QJ2Xntdpav5Xkz+CiJtQ\nTaBHs2On2zURUIn7T0rnkSdvekNFpleItVMTL3ZNjvsjYlG8q9i6umeiWx3v\no5pKL4oz0vue31u+9POsAgZAWhOvfVvelluWDg/3HwZL/lEqk2e3ns+ybcn0\nmAuP\r\n=QyUj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEqTjZyys+J9EItge/ouFDdNknYb3lbRAaqQ57Cx8u1sAiEA1nVuhxDAoVVdEiIpWHQScRE2/1sq4njexVXUjr+eL0Q="}]}}, "0.4.3": {"name": "recharts-scale", "version": "0.4.3", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"@babel/cli": "^7.1.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "ava": "^1.0.0-beta.8", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "cross-env": "^5.2.0", "eslint": "^5.6.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "nyc": "^13.0.1", "pre-commit": "^1.1.3", "webpack": "^4.20.0", "webpack-cli": "^3.1.2"}, "dist": {"integrity": "sha512-t8p5sccG9Blm7c1JQK/ak9O8o95WGhNXD7TXg/BW5bYbVlr6eCeRBNpgyigD4p6pSSMehC5nSvBUPj6F68rbFA==", "shasum": "040b4f638ed687a530357292ecac880578384b59", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.3.tgz", "fileCount": 18, "unpackedSize": 96889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvAY2CRA9TVsSAnZWagAALPAQAJ4r1w1M8SvSX1aezr+9\nx15Q2v4qOxka3IJ5ZavDlcY0Yy4pVguB6z4xk+/N26SUBdUqFgTrM77GKhVZ\n8gcgwNE6Hsao8BjBkqbNiGrWXu3eCFfckArrkjHF+GL8xgdJ5M+7bimrTsIj\nDufj+gJjF9ZrwIwcvX0DyqT0X/xuJmJHLBrDJbchFkMZG5m6xRnlKkMJ/JT6\nOjXu1s3XwCz0dQfPjNqrkapQyzP6iYseWjUkdmyhOIMe9MXjkMV/Q6JhRRlz\nN8lnqfs5BlPIbrmyUlCIJJf2l9vQ2zD9qCA3fGpOh34jK9rdK+/hrNLDTIoC\nRw7L4Lzt5Tsv2amWHTKpiuOc8AF3N34H+2nYCNRPHwEJ6D0WfOryDj7Nylv2\nWsWx2YgzxYB9o2gx5KAHoWMFPR8PQIII3X1BjEhZqk0nGZZD2hh47u8S+7LY\n9IYW4FG5vdncUdXbVrhyuwx6YLWVlUIfG9qTV3TzmHjuotCg6bNuD6mOtesj\nMsEQxVsXqiP2MuEMKceS5jC4EpRbH0OZBK4IBerDz4VS9iJdhVz60NCz6/34\njLOsr+GgbONmfx9S2Shks/8mtoRij+IOFz4IVtehhHLBegh5nsMlBaT+3l5w\nhNsGz8V2Eb4k51muBuoZ2Fg9fgTl6KL+ojFVurEcPor/mJ3ACxMV+HxDY3+3\nAxYQ\r\n=fpBF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFiVaY5wdAbEqqJRDqqasQ3cVvz4Al9bps6RzO6Bw6B6AiEA9U+674snkaLbTEKLHeOYkObx048RC8KBG2gWGcykFaI="}]}}, "0.4.4": {"name": "recharts-scale", "version": "0.4.4", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"@ava/babel": "^1.0.1", "@babel/cli": "^7.1.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "ava": "^3.15.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "cross-env": "^7.0.3", "eslint": "^7.17.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "esm": "^3.2.25", "nyc": "^15.1.0", "pre-commit": "^1.1.3", "webpack": "^5.15.0", "webpack-cli": "^4.3.1"}, "dist": {"integrity": "sha512-e7MCnuD1+gtY9N7TYxzB+QXvi4s30kvNqVbI1p5m4rf47GusEQaEHxi8zvlZkdOOZ90UhpGHcnkYlyYkUJ2JoQ==", "shasum": "8d6d1fce49cb617ee047601c6b261f3e5ca7f2c7", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.4.tgz", "fileCount": 19, "unpackedSize": 161968, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgUyYqCRA9TVsSAnZWagAAlQ8P+wb93ZOhwtpBhGfnpOLj\nnMuBPdVAb2q4A6pig3/xB9sh+cjAQrkokw79NXt6d2n7w3h9fYSqqcE6k4IH\njXJjiLVLAvUnLKBjIWmFJGvo4tLX8dG3t4rNjiQmDRlWIoS/FCfb2q3nQ2Ta\nBIPW3MU35MrAm/0rwDgFXqwet16Cjgd2w6ZH3B3S8OMYS0uU1NAkLEI//Xwx\nAsMSYUpgYnaII46D2uYLK/Kg5i2+d90uRqZ2y/un8XuRD+E3NiQFbKpf+ndb\nNrTqoSOw/l8MLumUd7ws9DtJIh/0j0V3/WsjQof7UNdNZZgk5+X/JMDokTQC\n2OACE03RiI1JqM/DOydpGLjPsJRvSZCpsDYjwtC0L+qru9D4oPhnOtQF+R/O\nX9OUR1C1bhsFx9f6RzP5cCwArMg8rQLLrrHIACZiPAPkdSaYVIwZgDX+h2ZH\nkNpuyl3WXCdLVFWvy/V45B4wn6W4lFLq9Z9P6F1fsD+cvSLyBeDTlma5In3D\nKiDGNWZJwKJJaoaal2/koj7881qUXJstEoSamLeh1YUYaQ8cyyldcjTq3Xvs\nChQ38j+LgGQ9iU+XSGe3/L/DTfgZz48fdqlxX98uQ8SWQdR/JTopBUz2Ljj4\nlj3KJ8VDcxMS3wHTa66u/jASwOUH7XdcLQtVrChZdZ85AiTp6osMwt/LVl88\nqWKR\r\n=wlYo\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDuKeHuVM5s+4AwqFo56gRx1QhIa93Tid6fZ912iy9b8gIhAMnL4DGrZFGIY5t1P6UgXq94IFEzGAbA2kE6ZjADrSvz"}]}}, "0.4.5": {"name": "recharts-scale", "version": "0.4.5", "dependencies": {"decimal.js-light": "^2.4.1"}, "devDependencies": {"@ava/babel": "^1.0.1", "@babel/cli": "^7.1.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/register": "^7.0.0", "ava": "^3.15.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "cross-env": "^7.0.3", "eslint": "^7.17.0", "eslint-config-airbnb": "^18.2.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-react": "^7.11.1", "esm": "^3.2.25", "nyc": "^15.1.0", "pre-commit": "^1.1.3", "webpack": "^5.15.0", "webpack-cli": "^4.3.1"}, "dist": {"integrity": "sha512-kivNFO+0OcUNu7jQquLXAxz1FIwZj8nrj+YkOKc5694NbjCvcT6aSZiIzNzd2Kul4o4rTto8QVR9lMNtxD4G1w==", "shasum": "0969271f14e732e642fcc5bd4ab270d6e87dd1d9", "tarball": "https://registry.npmjs.org/recharts-scale/-/recharts-scale-0.4.5.tgz", "fileCount": 19, "unpackedSize": 162004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW/W7CRA9TVsSAnZWagAAePAQAIZsGrVrk3dpuIeSG1CY\nBQrigvcUmvku3pJHe1MWQvKFYnxRoskkkEu8iWB2Xuy4fDdB5rTemeFPcHbf\nt0y1otyVG+k6227V07zAC/V5jJQHvdhPftOAadCW34y+Ykm2KKtq9bAt/ZRb\np2JII6O1fBHdvaqDWq/ie1uS0YAv8mO73ngWhiC8WNXlWIBI+agLzOFynLph\n75SdpHA3AUrd1kGBR41z1iYh9ww2K1ozb7x+pePCuDkjgBhl7Sgdx1Mr0rX4\nd42WHCXuKe0NPOGYT2fLHvqYjrzE5+1ejMyxwtxjJNYp7ln3EP5vToP7Drjf\nqcwNqNfvy5RSGcVFrAZ4xjrCXKvAp1603h9MIdgkvkY0/JnabNqPIN6//gWx\nxGljTholiMD3FgMSLnbEj9f5aHiLXRjAvmqhlIakOkfR41nDLhfZjQce81lf\nJEHauTalckBqbXAqZscvS/FQLi0+q2rkkVKvJM0yNp7Cqtj9Yra48WQLhPKB\nLHgnnAxhavf63sZH9H8qkAOhpjQkUtF65W1vlgcHPSplx1q104EZEvFD9nDy\nDiV72bD3Vkw9IJ84L53jzuVKHQX1YLaWSDSg0DYq0tX1wpM49hJw+3RPdkoY\n8rfrlDfCgth5ioI9UthtpqZyfPw2RorevkZ6g2CKvRcLa9l2QN0gJyPNkPk2\nA4dZ\r\n=u+C9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6CrOW9b3I+X1gZ1eyrib4fyq8nzPG9vcznmfNYEbYmAIhAPx5M4Eum/8fTqnDrJYBTT9PZebhWP7YejLQU3oI9DwI"}]}}}, "modified": "2022-06-26T09:30:37.117Z", "cachedAt": 1747660589709}