{"name": "victory-vendor", "dist-tags": {"next": "37.0.3-next.0", "latest": "37.3.6"}, "versions": {"36.4.0": {"name": "victory-vendor", "version": "36.4.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2"}, "dist": {"shasum": "ee986392f545d268e868d2b46de2a6db93c604a7", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.4.0.tgz", "fileCount": 279, "integrity": "sha512-FbERt20G1qu5qPxZqBlkWcf9Sl/gyqYVmhHadLPj0nMcvCf+vgZUVe/NLYI45ey2JtS3jvciQ9lCWjcNXY+G9Q==", "signatures": [{"sig": "MEQCICnc/m8H/lg9kS9ZRjLNhuvFxyCPPpmmx5CYx9y5ZXVsAiAWiEsjNn/s8PvViQY6rAblonUfYC7i691mCTcN6Ru2qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 370894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJietVbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJXA/+J96Fug1OtUDV/OdTW9b8zzf1X1DWp38y027vFf+9FplhUNwz\r\nnzZuSyucvABEMYIFGB+rUz822Sl7XxJ4ACZruSwt8i8cncrsNjaKGLnmF/oo\r\nxvQFQu+VOzIZM7MphYAQYDVo3ARKssG+bCoSzx5K55VXC77+OvFIP4fVxHYp\r\ntzJIIBH3zEqCSE+0i7/y9odqqyLpXTW3+k9HwqGwfBQf+9xqpvfC7KNcndq7\r\nPhfYYtGvs2C6cgnirgjMHTyulxlq5CSqUf2HLrWhPrZtB5xbGb+Sw7p68ui/\r\nvS9/UC9a9PACC07EMXDXaXUUmi0glIbmuiZmSj7oZui+wvdiEDfXxk8TL72s\r\nOpv7dr1eY/9IGA+Rl4Q/+uYj9R+wPyPNJYg9BAPfMuovyxrRB9kqbSCdXrJK\r\nnnmsEHUwCH+xGuiQT84avzBvnSgBaVvQh8rMfPATzZubABGo/ScofDFvR+5G\r\nnFmbjM2i732HLqFUwYr6ingHKUupFXJXvx/tRkq4o5xhrr+icLIAwvqj0E8T\r\n9CkWpbUGX2mpY5KXeJ9xvwh/AqAPNlMPSdqEfLMzxtg5wKgq0nj7+LBxXjlY\r\nRc02gVZUYqhhDH9kctzd5Ax880EdeaPTIiGrvSs6x504Fz8g7aLWMrpi1/x9\r\nXUWe0oLHKedHOcSRQe8k3GaIUABEht6fwAM=\r\n=USJ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.5.0": {"name": "victory-vendor", "version": "36.5.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2"}, "dist": {"shasum": "8ccacf150b84c4d9863f714bfe8ff19804d4fded", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.5.0.tgz", "fileCount": 279, "integrity": "sha512-f2R/Fhb49SZhfhNIXU/yJRQq1GkmWGs8DphgnyI/AC9R9zJhQ+GXACh04NzV2HVSVVn5bKt/JFzcsPo8cbudzg==", "signatures": [{"sig": "MEQCIAPaPAjQ/2+bP7MDj+G+jR/FZGFSi+kF49Hlg5c2+KdzAiBR+ArnJNf7ayE+4v7hBNH9keJ91QFXlLGeSalfgDh7mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 370934, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJin9TbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8wBAAisee9/pfdXanZTJo6IekZwi5zShj4HXdOmiM/R0AzutNEe0m\r\nyv7wo9eTSuIM96SqqWAu2GtVNsyQW0qX+pw2iPpdGINkewkq4O+/YvYqIeju\r\nzk85TLDci3sGhCHCd99b8RdQ/rZnTffdgbDpwa8LY+wRbPXDpZEQpS2HPS+K\r\nAZhmR6y1w/avGPbOx5nanxwid63SdSFBLXZ9jb1x919a98Lbmdm8GT6+qn4g\r\nxB7ip6LJZem4TlAh0a5+FhPvul9e1ciIfnz9vLHwaJOMphDvRL2IQNE9xFx2\r\nzj86QOR0bQ2Zf7iDE6IoFbegXWHB1om3CoRpGA1JXPW59MFJSW+MYPNSnvOI\r\nvkq2qPPYU2au9WxMzn9B7lCApCO3UfadMOSsZF4Ga12OKElkJdXkl4/EkkjJ\r\nxt9hx+OjbDtk1wItqsDG1qITZIMvsS3VpAOEx4gxG3eK8E6YoN4BU39W/Yar\r\nMhrHUJL8HIszDKmWZQKOss1b/8merSQO3J3gWnX3ddX0HIzx3IaJ//Yv5O9K\r\nGgX5Otj8zZJSwbljsp94AngTS0QerAt6XcD/IEnULTh7EgB2SuK4OVK73fN8\r\nFxDDPpiON7irIUUJ2ZTQLN4rteOGESGHD5PqMb1H1AWjcDOAYhC4cGzhh/gG\r\n9JhM3Euk8GITwv2JzMLONnONfpAVx9ztoUc=\r\n=sT5q\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.5.1": {"name": "victory-vendor", "version": "36.5.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2"}, "dist": {"shasum": "d1bb55c768f3b3f80e92741f0c7837062c694f25", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.5.1.tgz", "fileCount": 286, "integrity": "sha512-VMGEnIOs6GWMxdkWeEyc80s9MfI3LN9kIKdUT0bJOmxqh3pRfK7NmpYxq0ARazu0ZfeTW9dYAVuehGYBwgQB/w==", "signatures": [{"sig": "MEYCIQCcTary/PRfkKiiJL7K+IS7PuECwWRfviyv4yZhkwAZFwIhAMWcWKBrHsJOBA3PNWZFxaEYQ3PNHD7mAjD2R8i0DYTr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 372103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJitKnXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjiRAAgj1roIsBEDR7URUR/e4S+mqU6vmbAUA3udMdmWj65F1UM7nC\r\ncnZnvTC4bLv5duLFiG5V22oOhI/HZ+QpXjEZ72sow+8yHs0WUFWenl8L6DPL\r\n1krDupy/8bwFvVXqGDYAHt1nyMZJW61YLLDVsQgF7wUACmZrBShrKwA4+kxk\r\n65BPHNxROWpVn2mpJwOL85rOaxWkeQBAwucmXbS1XIKw/FWoMec8/bGABIoC\r\ngZePMiy2jfFrTMk3wz4Rnki4WxdqEJX+bC5j1DSjtdED12bS28b+KmFujuYM\r\n3wU+M/2dvU2ND4DIX+Zw0XbadU0i/MDdcJJvjXq4aCkUs9jYE6IzJvN1pdLm\r\nvdiL08b8t2aKA/FJqqFnS6XlskDxWaFZOxGAZLIf+qqI21jomO6WNtLvy0QI\r\nZb5FFUASQOQN4I50r2sEA9HNQzmj7ndNW7siPMIIBqGTKDRGRXDvi1C88MFn\r\n9OTvBLL7vf8E8P2mYDevrDDASIIHsAqPTpGzYr4u55r0gkqJs3YnI9fSRFXk\r\n3/LfKnLr1UVzyzgkInrfWu6EUwBQRBuzBWl/YulhuDB8lczoF46etVktOFwb\r\nnFg8fXvMweCV9EPzAOF/I1OcdkUegGN0P88Io5bGswbBa8Ow2gs9UCD7e4Rr\r\nesMmqXoddb4nB93HmCRDhLqQS5VchcJ5aXw=\r\n=1sAy\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.0": {"name": "victory-vendor", "version": "36.6.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "cce44e790b0fac4fe615ee51577183f21ffb6e99", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.0.tgz", "fileCount": 299, "integrity": "sha512-<PERSON>ly<PERSON>cQOtTMngyfZoNeIpdHvXH6N3epqTpFKGhttKuGZNz2fA8Iz2xx7AQXTXK0mFZXQ0sBClmgtgG6vdqpoQjQ==", "signatures": [{"sig": "MEYCIQCxXTfHvcq7Y13u124BjKvO9bl+Yu7laZycPwp8tizykgIhAMOsPhiUYrVhb6VCicKsMOCsqyu3bR+7RlE5ojqkP9m8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 404998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6/QMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiBg/9FXX2jTBRKAJ7e4Zoo0pA0rbqIrvUBSrfk/WhPYTVuX3yRPtJ\r\nbptXnkCf3gJDfalpdimct9jPcXCruXZDcN1AxodUfmsvnvKDS41Z4lk8SQjL\r\n6uucBpPaZRkslyBHQYjw3FRBw7pWOtmujmm/W1QJyQZFHDgBzv1nrCk2YQaL\r\nKjc6hWePbAQZ1lJYrLDxob97OZ1cy8+nxmxI0BB+s4pHSgByBHXGHkwV6v5x\r\nVLAZtKtdWlHI7w9WhuSOUvUrPcknvXwO0Vh7XWgk+879pLnV91uZCF8bu5j5\r\nbFzcK7dt46G06aaDD4T/wtwOyHWSjhLMOoXaS3b9hjyU/d8l+/EDCI+H9Wgv\r\nUcW4IukiHyUU/xm71Ph9zXbJPWNoEQljgECXP5uZVO4nB8NsBL9ovxIFvj27\r\n3dLWL76gQa7/RcKiQhJAi1iTwLum+derpeG50M1tSThteU9HopF4c3uxui0C\r\nCvH7PPKeneFzXv4MRBcv215vcj/VfXUMDswfP2k5z0H+/Rco8ErjtEJ3DQ/k\r\nK+DB79VT3QssA4w2lVfq1kYlazpEXOj7qBd6rXCDyWGmfnlo3HXLd/fCk0w5\r\nm798Et8mx3lLR4YxkZiSk4LIqvCZYfrxft7lBAJPcpDj4108w6o/w7Hc8+sf\r\nq19rEQ2xZKFZqair57LPk/omT2R3DuR9xNA=\r\n=pZgz\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.1": {"name": "victory-vendor", "version": "36.6.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "b76a9c1ce5730ba2e315eae91e35c838be93ff56", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.1.tgz", "fileCount": 299, "integrity": "sha512-EvztJfmAsJv0dwppiNmOcFhGMcqxExoyjke1W3z3bef75npsmgxYucoFoARhKbllvKbcA+sw85yaYcI3GpDvZg==", "signatures": [{"sig": "MEUCIH1wGN7hO10TAM+Ry0AK3B2h2cME0gXQIORDvpDtpz7gAiEA0dInHU+dRdN4IzJHp+UqDx+UuBDRdn/ag+qi8Eblr7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405158, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/AlOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7Gg/+Id34J/ZYNEHRTvyzrtFGGAKlJGkHEyz8mM7lhVNkIBRlJfUx\r\n9QTzKE/8+mYD5emT7jAuC+nDQ+k1L3NerNMlHNou3aNf2Qr1KtsN1bl8EgRp\r\nS6FcJ0xxuCiNV7BGC5bbrIm45sTabLu7T7mNXFwTCNXqxO+K336twp7OND0+\r\nLlLTV103c9L52VGRWLjtOEN6McRf0Ey/S8aWsR8Qjfi6TqQPBRKNuJDtEdmZ\r\nFFWZQLwRU5cSLgWIbGvViLUJ4JpJx7vks1IKVnQVKyQ17dX1XcVN+blpuC0S\r\n6ibu7AVFvch5ywjQDnifKDhdtmDxGIYvrR/LSAshv9jLv8vCeeh63ixMDfdC\r\nZHEZdvQ8VU7MOS44SqTzc/rtOd2MfJzOyQdKTBNK+sEnGx5w8lDcLqtJNBbJ\r\nf8u/aoXmpo5ldiEtRChlei9LDDqQNqZBYH/xRYZ/4keaOQcr9EckdgtHQYbS\r\nZ88rMaRFuUFo2ssSI8wBpIjnEs9+rakTWCMbGFUQtGEAIsnYrhk2r6TAmh7L\r\nl3CV5GQ70wkI1b+WYu9Oj8VwvltXAu3LEEAfXY6NmfLlt6rN8WMimUzKNHw4\r\nh1KlQioP4ZvY4N+GZA8BBIuL+sPOtaMcN1imgEcNA1mH+mPpce45y1UY7cQC\r\niOgJFNd3eCTCuh6wfAcLG0FWfefuFJBk2XA=\r\n=MgRd\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.2": {"name": "victory-vendor", "version": "36.6.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "3f008c931511c9ab41c11a2db98fa6c3aaa2f724", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.2.tgz", "fileCount": 299, "integrity": "sha512-cjt8hflz3gBlk2gCvRPrkMZcjYak1i86OLJApxb71ZArK85i+5eQpxfiSYESTqhPRSgJj55IhJc9g0ltOCJkDQ==", "signatures": [{"sig": "MEQCIHpEoo/OAxhfERZOUxNhWZMLNM5jJTpEBE78sVGpzMzwAiAgscwPHJUMVp4q+VRER0r9JGWQeMzN2aDnmeMTGXFOGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/VWzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorpw//XWP+WCmmNrc5S1NUvFe9f+ldZVb5e/+6fXillpVWPpH2z5D4\r\ndU8EGo/9cSZJiN1uYU5N+EDVNKXJbrpEq12JPADpdolk6fs96zm14KNUBPCS\r\nUmEVqdzNtXpTp1d5AaZNyGudg1llsgLjj2Op5qrWAhzsS4utc0bPJGlZuoe0\r\nQmxDrSDWjISlyKrnfqzDypx8VCvV4onbhtxGceEemKop6c59h11y1J7yE+7g\r\njTqj6wzkCS6BLdXZOxOO/kWJMSvA031zNaUwnSD5NAGzBUpNxIGco1+z5Zj9\r\n+W6etCqXqOPtbE4lQZiHfl3l8w63g8s8dfV1BhKNbZ7fD3q682WNFvx2jaU7\r\nTkzeN5o83sANaOe+cBhCopuUYaLpqlf/vcETPytMjtPLEjsbEiOvcXBvpzRb\r\nl6Q+5Ew2ggQMcciGwZJBqyG9IOUOvqnQfOQ/GGx46zslozj4WPN0+2QgcPJb\r\n/f2YKIWHlHxdWXQgWAFUu4JXe0s9l+OdFfV9Z3Mxll5Z7w9bmeNqEILlq6QL\r\n7uZCUnh3vN4fHq+MG/KDKyQYSo98bU+Khne5/49IeoQHOw68WAeqHed+ymDF\r\nxA3RObjhosDuvyJwGZnrI+aCJQt/vvB0xbmYRaCljR5rV10wzuKPPYgidUA9\r\n8WEVs6pnccbwVRFvCq1LGW3UTunl+hY4Kng=\r\n=FX8X\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.3": {"name": "victory-vendor", "version": "36.6.3", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "f4fac1de28b4b49b5c2df3ecb9fd76d258366fb6", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.3.tgz", "fileCount": 299, "integrity": "sha512-iXr0si2zuKUqLI+AXNT8/tk8ySugek4qZCy6s02CFcoBoYgM5kb15HPda5aPNT53XRXKzWlIsoHUxMZ4u/sz+w==", "signatures": [{"sig": "MEUCIQCtfEw01loXctxT8zfVAfhaEaNZq0kgs4EJz5eW/xKGrgIgRjyV7pAYRwWhutkETg8yfu35ln5TiYWQU0haI4Nii9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/vVFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooXA//ad4/QoSVpm5+VnHOOgBPtCH6TfhRvqwRLnEMYXC+dQ/e5Bh6\r\ntjQI3xa5WPQqyt9sw9gQ6OVZwR2RAXVdRnmecU5WwSSisqjPXq4zLqI1nOlq\r\nAJf8IHcJAl8I7iaS7R/wISBcZu3H29IuP1X10Y2m2Cf0qU8Hilu2JiG1tN3T\r\n0allwMxRvGdEFhasOeaPouF/Y5J+aulDolSQjU3zOL2oARES/aF4S/gid5gZ\r\nYj0z8qaRpRo9hP/YgLiyENOs+0GOCYCQkArfsOC7Yw+RT9ekE5pYWcLr+upw\r\nNcGc1YjV9NyVB/Ud0YAyoZ9ln08JQF4HoSFW1WM1Fa/lE2GKOtECpRuyOQC8\r\n+O0PIGfMVwzosWOx7MzcIMJs+dUvO1I9dkLKgiSGNvkRNmQd23mQzpLWpC1O\r\nce40FumCvHNrDIzz8I77hRQ/bwVTLrOnUulyNAbCDF6gac1Zv/H8mwpsfxvC\r\nj/uEJXjRa3VS/N+d968YXtsgM9ApedC4llqK/lE5Ot2QZohUNMIHMAe70K/K\r\n4PW40zrClZvGonajgKDtOmKbGb5k97gvA/YXMjsHWGUJTqOrLyP3fczb9Pqj\r\niqCKc2/edHXFTEkJCpG9XPu2Rup/EAq1k+0Z3r2AoJOjKH2KhLZDUnMoPd2P\r\nBHDPSbtx1Bw4JiWMXdFnE5k6NcB1YJp5mdQ=\r\n=vVF3\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.4": {"name": "victory-vendor", "version": "36.6.4", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "848a75c776b9efa9faa55d5e5a4f6a280a5d2c63", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.4.tgz", "fileCount": 299, "integrity": "sha512-WXy7j1uCf9fXQVh4mWfYUdwLmKE5UJKpDbqRvmXlCPO/IaUsHRWZf7KLuZwHZPKKLG7zrAe9H3o2oHzmwW8+5w==", "signatures": [{"sig": "MEYCIQC6/A7bff6Qa8wB1b4d9YfQYVeyAlpNXOOBd2gsbNvmNgIhANTWqB/roWpmtJDHDmwHmXJ6VH6e+XDyJDEiYqwxrZBN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405586, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/8hfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpznw//XCzIzCLfmw9WRdy99IaYBKkXuHuXnK6TQkFQ7cJwssfrhs4m\r\noZHY5DQp15EgfkzD+2ONS9HX9cy90vc7TTJ/fzmb/euvfRB9yaM6Bq5p+D60\r\nvVybPezFuQPlnd72JFOmuayEq13BkO1m3uFHID6h7SbGXKv8bzZlcVLMiUqQ\r\nYIGHIBhgBMPAvWjvltl/E0o7NhfyWZvAmnTfNcKmlodABffbj8PqslzdSJ16\r\nZo0wXfgL8eXL2TYA1VZTzoCXtiphT/WMZDoceOw7ENkfMlavtg0pI/k7D4Yh\r\nZquLpeElTqbzfnchA3MfL0s6dWJeh9lJvI02+85OFwMAKg95P2Ed8DMIemfp\r\niEZ6P2h2u0ytJiJoq+wIjSyjXTLkvjXxx0yVgoPv/UpXt5qfSQay8Tx9G28E\r\nIxatjzYPx/dWvZ/B5E9rkMh6L3xIXEa0zWtRGfuxKfuSSzGJ7PCnNhtb/jy3\r\nnuT0agM1tf0XzdCdTNQ4P58C7rkP7MzWrieohpMxPLbHiK6hYFn4qlmoqCdJ\r\nFziK0IAI0o5a7gxrzUWP/SBqF8FjdxCwclskAXLNdx4ToUQNBw4te3c05ghY\r\n0OlZvlHyUuu90nIA/Q+PgGxaBJVxQTat4D7f2lun4cenxVaFYijUMbfYlMXa\r\nRqskRBWyY3vGSc2BLslMWXCquexs2j4OOa0=\r\n=r66w\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.5": {"name": "victory-vendor", "version": "36.6.5", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "cd09672407f8a68907a457fccbcf64ec1749ee13", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.5.tgz", "fileCount": 299, "integrity": "sha512-5KXhZxKZYMGXBgQ5LCXRcIDU6M8OJdheW3c99vLRW4gUOi40+onksx/NFA2tGUE/Z8j+pc1zW7ZZqvG6Rs0Ekw==", "signatures": [{"sig": "MEYCIQDQbKObUOJFCmwWLOQSfkAePJn3YKAygmH/+5QFI33VLAIhANyGF43zaWD92WESwWXNB36Ll3++jGFuO6UYSbZZ03tr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjBQ4mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDHQ/+M7Vz1RmMnylg7xhEcQ/qUWCDRb/zSH9jtBjc078oDy16e5p2\r\ngcxTzzhiPHcvqn26r2SVsvV153m/KxbdW1q247Sf+TwdXCI6kMooz+0YlHdT\r\nVYaVRIR4kXJi2MX8G37NX0fkHgTGjLPmYAZvgx1x5vqGhUeYyShB6cDx4nAH\r\nqUfjtvkPU3SgFhOTB9FO+AlUSl8nmV9ky3mGylBYEwXBlChJOBQ1p1b6NiU8\r\n0+ZQokxTvHebDFF/GqX8i3OC8OxnkXXZTxlNdt5dCF3Y/mZMsvCuJlh1UYd9\r\n01g2i3qBOReLJIo713ibUt+Z09vpnSswkzKHFuO7srsL5nx78paqHObEYMRg\r\nw4O71KYfpgnrOg8npNkXx94LJ7c+cwe0vfu36Um0Abw5rWSiknSr+RGHhGRG\r\nYD2hPZXIMirjVSxVSxl8Wr5kQEOaxxhl1r2SABSkyhOWBYE8FezdbbgvR8gM\r\nHL+Mp77ETm4ea8qcFy3f8vys15EZd85v0S+PaddpK4SxSaxK5dugebLnwopg\r\n/lXF4YPqu0GeF7nt4CSsf4Lcn4eGCdCkNyfVPtaC35uq7I4psPhqj7P6U6XF\r\nUSqYpJlyun/0pIZlGGEzy2/NaS4NE+p5ubNe9EByR5EdLwC4SBJqi6CHT3aF\r\n5ERJnWirWiBefpNkqxDeNqdObXE0kSaPoec=\r\n=nD5D\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.6": {"name": "victory-vendor", "version": "36.6.6", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "d01e5a5124b7287ac29b66199329c55daff49494", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.6.tgz", "fileCount": 299, "integrity": "sha512-4qTXOWB0GiBpLFKt3+Ac+WaNeA4zBYJQpy2gqEvf0GcwYGQfAULo5X1Gia51ugnmZHI0uMOBTZRW5DyKp+N3NA==", "signatures": [{"sig": "MEUCIBSVpgBS4e6pcrEvZDFISUHGaRV/XlFY/GHh7Hqa0iq6AiEAhKdiPGQK7GkP/YMKvAMsnjynk8E7DqGcoFu0OnO9GEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjD6wHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOig/+JIWd82qO/Ec10cApiy0lx23DyZ5NuNPDY0ZPhQ4P4gpC244b\r\n8DcuVqAMde8KRFFxSespLzfFooUETFNR98ybpo5pyzP/aOyNeaRGFg801puX\r\nWuJ8UfgZgoBSPC7GmQdmrPq/syvE5SBsbrX03DCA2c5fQabFUoK1GWm2c4YQ\r\nn8Xv463d2TZ6L5h/d2DNMZcAbjhJPSht+/2VywKbsUdT9sbu22N0VLL/Jcyr\r\nmur4S9vKgJBhrKkhX+yIE+Lmjl2/a5d5UHmBLRZfjoBIhkDf2nU/c4KWQknk\r\nq/6lcm5wA5TnJ7ISPOBUTle52nfeMKZezFk5omOGpM2rhyguJm6upDX8m0VH\r\nD016aXhnQdiF8qD9heI1VnWlIGCaJ82akDhZ9iNEF1EsJJO6mLdjh6ayhcgM\r\nnZDnzghemdQ87Rq69i38d90yG0tU6V+OLPB5Yh4F3bAveOiulQDgF6MtDDY/\r\nLO7CdHQeZcM6p8ihOINr8ollHGmyv/70Ehr2ImVsgSD/6E+i5/vB7LvR7aI9\r\n0x0DWXWCSfhEBfsJ6ZKhSkbZb1VDXV/iWijZ3fnXZZE/dGC4LE7TDfhW1+2l\r\nbaX3mfp3aEeiXBOD85ufVPKchML6k6kKPW05iwhb/tv9S90eZKDkJMIlltii\r\nENWitbablDIDkTqDqYVBExGhHZZIPi/T6b8=\r\n=umOW\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.7": {"name": "victory-vendor", "version": "36.6.7", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "d95e79eea080ef607570d6a280aba71f23c1f25f", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.7.tgz", "fileCount": 299, "integrity": "sha512-zCL7Pm6oyUHPjGKIimxHhybAKTcEhDRJpdfqxKGXQZX1DgOYeAwVhnag75Fgb6tBx4xm7aRg0Bg7gKGUaOFkmQ==", "signatures": [{"sig": "MEQCIDHRx93gPXjKhH4tdIIuQiYlhEu24bD0gaI6rl3TptAKAiBXEPAbzcNHifbIVoZzsSTALKSY3OMDHC/TVljXufJ1Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIfylACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqmdg//Q2UeiOe19Y3v9mEzzTrOdzDy197DsAukyJyfWb8KqGl1nzj9\r\nHwgfwjbTy34VKG6mnzhGdL/jmRlTWVLCi5T2KU8ZrU73K3I5rr7KXVtYMpKb\r\nC1fLnJ9+JojbRcTZqaiBwfcLPUXbVnSwcSDkk1oDYNaykEymTK4UaA8fsmkI\r\n5xYh9QAnQVgRrYLjPQHice9+2CGpUX/cHqLiT34UpTbA+EqOn6jvh5wiJGwo\r\nQFBTS7xgrKMI6dXqFXbGASNFhaIM40LBfv7wMd7gmpwaTNFvB5Uze8fzi+um\r\ncbxImLTB0e1Fcb6HY3LcaOq9DkX9DlaRHiI4/bhqO6aS/gUu9EXh9KC52BsJ\r\n3oJkYCryTtplfr3AUFaImVCJjYwLi77J4prV4G0IIasVVPVSITUlScewkXe6\r\nsJTRoKR/0zkC+GweAj0Dq+fnALxR+I1gIR8OyTAqiuQg5s0SUe+Rg1qTDDDd\r\nCgtKgil3Rb8mCXEcQJY1MPGFFTFsaKNe7kI92+TEkP7quvBqIx3ZbjARHD5F\r\nXDzfzKdJ5bPmGAzueG5HgvV9IaxTq0MUJnOvYQTxy+fTA9cQbjs+BtqSPBVL\r\nlQEAvJYmgf0xfWmUTEny6/fhrDNsH2LajlB2uscwX1YKJMnW3Dp4maKkzzBP\r\nGaC1x0oKi32QN3blap3VV/D5iNeoNhDShCo=\r\n=jipo\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.8": {"name": "victory-vendor", "version": "36.6.8", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "5a1c555ca99a39fdb66a6c959c8426eb834893a2", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.8.tgz", "fileCount": 299, "integrity": "sha512-H3kyQ+2zgjMPvbPqAl7Vwm2FD5dU7/4bCTQakFQnpIsfDljeOMDojRsrmJfwh4oAlNnWhpAf+mbAoLh8u7dwyQ==", "signatures": [{"sig": "MEYCIQDHRmrPnwdWug4EA3Mpc91ZWP29KDJ+fWWjiW0pbNTkUAIhALZunCQfpSN5UMlltPBZ6jxwvtTbhuLY4fE7tf6M5tpm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 405770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMh7JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpomQ/+Jw9SZVFpUwcxrYjqspUmtzFJP0fjlzZTduWEo2USZfH4TRCF\r\ndk0vqLonfEDb3DlwM4Bb6aoUfI/TBNZ+Rxx3AQb4B3Xc6DFCZdevWjG/i+eC\r\nHGzS+ZQNDYv+e054f4EZguQ2SBsxkbiuOZ6+/SIe/DRaGHTAUF2n7RJC0b79\r\nQ28GcrjRKUsisXDdkOvgTmhbfu6bu8fA3KB3ogckB2Nryxenwbnoxc+1oGWd\r\nq3zYvxeXHVwN1MtOvLwfn4hU59qKQ9RSEjcther0HEj+gsfC9uSfe3qBoOYC\r\nKrjRTjtV6xUglYQ7rgXFMxaK/MhS7/pwjGJGqSqTmbq/HTb2+eIizlCc7mx2\r\nb2FYSUpshX9Gm3UWBok2e6dWLPXUHLCRcfG6Z2Hn8rIvdd9/E3vi4WjEaWbJ\r\nLq5i8u3UiyB9CkK27AhVyF0MbU7oVCSZL1kyqZWlcbs4b//7o8jUGygyVOcD\r\n0GbbRoquC1MkcbHvb5uqm1ZbYriJzUe/bxE0f8v9SpyFfpXkTQsFfkHRlyMe\r\n4PZMRTWAOQxv0aTcHczghE/hHTKjl3sWY1ooYPcYByCXlVbTZIpH4tdozTYa\r\nCSJr7zoZN7bbtr5LdsF+IszomjorHQda2zbog3E+mg9Nf0pDgqawqSPU8cDs\r\n0XF5Uf02L7PH41InDNrbOMCcoD9fzGKrG/Y=\r\n=+M2F\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.10": {"name": "victory-vendor", "version": "36.6.10", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "e7e3646deaf0e850bc60dffdad6d7a4abee40632", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.10.tgz", "fileCount": 299, "integrity": "sha512-7YqYGtsA4mByokBhCjk+ewwPhUfzhR1I3Da6/ZsZUv/31ceT77RKoaqrxRq5Ki+9we4uzf7+A+7aG2sfYhm7nA==", "signatures": [{"sig": "MEUCIE7XatNz1gdKe9y4gWTNzyEPvUr0u1iAou9/JasWe7EKAiEAspHIQ3x1LbIht7JAbJoN9lAXSQNnU0huBYqpAOpH0IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.6.10", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 406074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkT/hQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4bA/+IIBmQfLMatHRZBlIuaoXVwaKpzd21RQKK8JrbF26mWJGRQnj\r\n1N35ub56t3v4+/BKR+x566Ce+ly2m2GPQaBHuJIQAH4FSruo816OLwqlQ2VB\r\nUpq7BHMM3HuHXWFloMmsCZ+BK5YCnppkIT+gr9ravaAFIsBAJfnk8447KeDZ\r\nfa/sojB2IX5PuEN653PO40h7LpSSH+7Y9wYf7fvF9mXAu9fgbUHgY/J5ou3s\r\nULlWdHj05bg2fGiv0ee8EbVv7jiBGIZORP5iEweGK4izkWwXCmnImHhVKIPe\r\nQ/FCvrMHGMlA1YSqbURUndbWW+RX/58TJHd+hk3oycmEafnHu72prkZA64GO\r\n8QI2hOt5RAwmpVZMIpvPkiei0RAzcAygfLtqgZMuNndwmYaroXlVz1wDAF6N\r\nS0s+uv5kignXTHTS2/aeihqWGmmXQi9GYPSwrbwfRBVFOTA+afxdQ9PUHsEY\r\ngiW1Zi9JjJ3IHjCAHf6kNjj/ZB1odTgz/E9ZUGZ6t7apKZa2lOsnkydNzSkv\r\nQ6zJeM26+HGCK1+65LzmmWXdLPIWcANGV3P6vfyjpSIf0IQIJUQsGpqTqLw/\r\nrqOl49tTfSa1WHxpWkdmkcWn8IjVt0NCfZn/+WFiKc148UzEGgoPJJx+LPqA\r\nhn3At5R0fa5/qWyuJznvMz/ql2wVAREzsO0=\r\n=9PT0\r\n-----END PGP SIGNATURE-----\r\n"}}, "36.6.11": {"name": "victory-vendor", "version": "36.6.11", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "acae770717c2dae541a54929c304ecab5ab6ac2a", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.11.tgz", "fileCount": 299, "integrity": "sha512-nT8kCiJp8dQh8g991J/R5w5eE2KnO8EAIP0xocWlh9l2okngMWglOPoMZzJvek8Q1KUc4XE/mJxTZnvOB1sTYg==", "signatures": [{"sig": "MEUCIG8qaNAwv6JrMDmqPJr/CmEGpmyPTyeHcXJFW6mbcDmHAiEA5PR2OfMqnRIMyXIKipFN3mUmXVc2uvxMccsLAt0z1uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.6.11", "provenance": {"predicateType": "https://slsa.dev/provenance/v0.2"}}, "unpackedSize": 406086}}, "36.6.12": {"name": "victory-vendor", "version": "36.6.12", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "17fa4d79d266a6e2bde0291c60c5002c55008164", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.6.12.tgz", "fileCount": 299, "integrity": "sha512-pJrTkNHln+D83vDCCSUf0ZfxBvIaVrFHmrBOsnnLAbdqfudRACAj51He2zU94/IWq9464oTADcPVkmWAfNMwgA==", "signatures": [{"sig": "MEYCIQDCGjPL9evad5JhOEZkElr42yai5e9oEJvJTeN1ZDgdegIhALdeMAKjnR32zsTbFtwmQhLDiOCckBW5hOw0jZr0QM1J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.6.12", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406098}}, "36.7.0": {"name": "victory-vendor", "version": "36.7.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "e02af33e249e74e659fa65c6d5936042c42e7aa8", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.7.0.tgz", "fileCount": 299, "integrity": "sha512-nqYuTkLSdTTeACyXcCLbL7rl0y6jpzLPtTNGOtSnajdR+xxMxBdjMxDjfNJNlhR+ZU8vbXz+QejntcbY7h9/ZA==", "signatures": [{"sig": "MEYCIQDYSfFJv+hfJhoTGvgS5O8PHEeO0gdufvFcnvIdp5uCVgIhAJLIvnp8grjBBQkIq9Wa5Tt377o/GK/F/WdnK/d0uSzQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.7.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406108}}, "36.8.1": {"name": "victory-vendor", "version": "36.8.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "c012297de51efbd26dc582f1802aa58445462dc2", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.1.tgz", "fileCount": 299, "integrity": "sha512-T8cXN8D6J9wEtDEHLiXcgrOE5gyKR39s9fCFTGmcOfqDrT8m2XQLt+2p/n007uxEMRvCDH7GYYqy4vV7GIcGhw==", "signatures": [{"sig": "MEQCIEzYOvqKn8+tZRr5j9R1hNyaXYDDFjEihjXBiVRUxuvJAiAVu1My9YRenMrGAlglYaK/rIsRf3kEH/dQIhSmFPH9Iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406130}}, "36.8.2": {"name": "victory-vendor", "version": "36.8.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "523e97a78ac8af73c526eb9fd3921d6972100600", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.2.tgz", "fileCount": 299, "integrity": "sha512-NfSQi7ISCdBbDpn3b6rg+8RpFZmWIM9mcks48BbogHE2F6h1XKdA34oiCKP5hP1OGvTotDRzsexiJKzrK4Exuw==", "signatures": [{"sig": "MEUCIQD7N2pBrN2qaDSWLTc9siXVM3f0NMsyl7r67wgtYnxjqgIgOHueCpLJJJySjsiN5ZvqWv42A/tLqsBy9wsIJOvGjxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406141}}, "36.8.3": {"name": "victory-vendor", "version": "36.8.3", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "146b560848ab8d7373db70336fc5b236c9ce3a94", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.3.tgz", "fileCount": 299, "integrity": "sha512-G2aakSDHt2fXA1FQxxLnZ/K7Fnwf1swUIUDGiKxONXeilhncgS5upVX3hm2X8kouGPpMpDUCVCrjbk6bU4uYCw==", "signatures": [{"sig": "MEUCIQDR+bn5b6tiz+rgFALw2O5dS210iamC4rHjQjE0vPCHVQIgFUIt6xcm5QCXRHWmW//sBHWTgJ8U0zmd+iSQ4KVda78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406152}}, "36.8.4": {"name": "victory-vendor", "version": "36.8.4", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "63a5bb04934cf9fecf8c5e43c04a4a1242f306c2", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.4.tgz", "fileCount": 299, "integrity": "sha512-30dOGZVjrOraxzflyZozjwYBYnIjhX2c18kuVNiiZlRHx++8zXGptlXSAm57M87Y2WLN10XGbn8kTXntqteKUw==", "signatures": [{"sig": "MEUCIQDcj+BrkPuBaQn0zR6dKE/mXguLdBVgd2qV7GSaMSMt+AIgXFHbDu3j3zSEvTZ5b00HuA4tIXHa1+Wx0w/BJsB0FhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406163}}, "36.8.5-next.0": {"name": "victory-vendor", "version": "36.8.5-next.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "0325c2fa781b313baedfae8c1122c11de7696fda", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.5-next.0.tgz", "fileCount": 299, "integrity": "sha512-vDJIEoLHh1AgUzHRWxwlhs6WpCy6z+OlKuWUHdstDX0fm69nAUMPB5BESYWk+vJzWv+pfL0ccAlb3cQeDzmpjQ==", "signatures": [{"sig": "MEQCIGqii9VD9nPa+rGi2ImqBLRkJnsQSzaBolVRb/SwRD5tAiBpc8WAgUaPgsoF12EWLtEtrzrZL5/CCJwnAZuzZBgx0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406276}}, "36.8.5": {"name": "victory-vendor", "version": "36.8.5", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "5432b52953fc9dc956a1ca310fa71839693677ed", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.5.tgz", "fileCount": 299, "integrity": "sha512-gqLYO8pqI0bEEB/MygK/A9ZyMflvhMTYr3mymH0I9dv2OFUGNgY0ZqGZ7KJ/Trmyg/3D7DG3Hx0SYvD2emQ4EQ==", "signatures": [{"sig": "MEYCIQC2T7b8dickNxVgxc/dS1kCaboGRc+2jFPhORZtoydQSgIhAIVO1wudb9N32HMEPDzjGqb2HzrrFNZZFggGttmCGnZ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406313}}, "36.8.6": {"name": "victory-vendor", "version": "36.8.6", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "44a150779119ebcd19120f30fc1f891341ca803f", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.8.6.tgz", "fileCount": 299, "integrity": "sha512-PH8Wj9b0xIZ4AVfyn0c1SJrOhtxDJ5PNxj1ZDABPg1Gw1vJr1mJVqESPhvsFj7mXLQohVdiKqp4kWZkXlPcRcA==", "signatures": [{"sig": "MEQCIBX8HhVAmFkBl0oeRpcqMdIPQ+YygaKYzGTXAkRubE1SAiBoIQYnAwQ29NVwmAnKsQsDKiE5gptNo4vLpjAAc2hYoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.8.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406324}}, "36.9.0": {"name": "victory-vendor", "version": "36.9.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "edc35c52be5500364c7c0a5ee136850822f18306", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.0.tgz", "fileCount": 299, "integrity": "sha512-n1A0J1xgwHb5nh56M0d8XlQabMCeTktvEqqr5WNAHspWEsVVGGaaaRg0TcQUtyC1akX0Cox1lMZdIv0Jl7o0ew==", "signatures": [{"sig": "MEYCIQDGa7shkN60Eq8RpI16t/X1bZedubP4HmxecIHLLv+n0wIhAO48pPzT4620A4hDJ9YyyhPHpu6eELAUuc/nUuZ9XjsW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.9.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406335}}, "36.9.1-next.0": {"name": "victory-vendor", "version": "36.9.1-next.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "24eb459a8e3178374ada9b389940f9f2599bbcad", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.1-next.0.tgz", "fileCount": 299, "integrity": "sha512-K38xyW5r6jl2VPxDgHxBo0B9PYOsCOWY05yeA9jr467LvjczHvgZeBabx4t3F3ySkWljUhskBpvO/iLeGc8jCQ==", "signatures": [{"sig": "MEYCIQCzOarLIPY7rb3JuRQYEllHZDuD/PcZeFUsrmdposZ+GAIhANryDljw870+S/7v4I4auOsF7O/xi5EBPNp3wu2djuyD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406311}}, "36.9.1": {"name": "victory-vendor", "version": "36.9.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "a7536766ca9725711c7dc1a36dd1d1d248cfa22d", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.1.tgz", "fileCount": 299, "integrity": "sha512-+pZIP+U3pEJdDCeFmsXwHzV7vNHQC/eIbHklfe2ZCZqayYRH7lQbHcVgsJ0XOOv27hWs4jH4MONgXxHMObTMSA==", "signatures": [{"sig": "MEUCIEUJ2iwtaKnB/gKjxILjL+p3334DUiwXKhqN0Nh5fUScAiEAnMpE863TR1rSOPEnpJYp4D5UpaxLeneeG+dRwrgiBcc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.9.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406346}}, "36.9.2-next.0": {"name": "victory-vendor", "version": "36.9.2-next.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "ddd93441fb991964ea0380a50d0a757a927f9828", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2-next.0.tgz", "fileCount": 299, "integrity": "sha512-rKiU53tDZc5RT8lVKKNVHzVJExn5yunv1UNTD+u/BXheTsYuG0wNEODVOptd0Iow1edoudQ1RJEDsiq8aI+HTg==", "signatures": [{"sig": "MEQCIGkH+axKonZA0exMrTlIMBYRygdWOdytKOWxs5IAgFuxAiA4yZsaLju3pW+UoKrxzJmSm/Wbp7LPkvQkX2moR12b5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393146}}, "36.9.2-next.1": {"name": "victory-vendor", "version": "36.9.2-next.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "53fb460330563a08f8d9ea9f70cfee17082f3620", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2-next.1.tgz", "fileCount": 299, "integrity": "sha512-NX4yz90XD5vfeBAYCxjEaE07EKBT+/SUgfpXRFxCvdHK+hNzJPBJD/2/9ZtQqUyT/W0rWobz5wFZktUlgD+q6A==", "signatures": [{"sig": "MEQCIFrXC2eIQeMd8tQNzwbsSc942O0DoiM81yG9BplU2jcXAiB8dqFr2U7kQG0wbUPsELjihZKKPhMEFkECFzm9/YPbeA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406322}}, "36.9.2-next.3": {"name": "victory-vendor", "version": "36.9.2-next.3", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "b149c8279b84744cd3260da59b7e10c2e7f921e3", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2-next.3.tgz", "fileCount": 299, "integrity": "sha512-80ze+1LHmRDbjfak5T5o8aY/OOZWmzXqOH6qPgd6X/vH3rZJVE8QfCboipgZ6MAuBwUY2WKOs7X2vPXQGGtUjw==", "signatures": [{"sig": "MEUCIQD9k9ZU1fsXZLG0+c9vKFr3AY9kGlq4xBsg0LC60gooXwIgHrRlWWcD8T+dBTjXolnoK08F/xYsIhQp6CsDRgJtwBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 406322}}, "36.9.2": {"name": "victory-vendor", "version": "36.9.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "668b02a448fa4ea0f788dbf4228b7e64669ff801", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-36.9.2.tgz", "fileCount": 299, "integrity": "sha512-PnpQQMuxlwYdocC8fIJqVXvkeViHYzotI+NJrCuav0ZYFoq912ZHBk3mCeuj+5/VpodOjPe1z0Fk2ihgzlXqjQ==", "signatures": [{"sig": "MEYCIQDh/VUjUKrdkjxA+ZV0MWq+5iLxPFHE+yXuwRSz3+39jAIhAJ0f/z2M8lwewNXR5Q8N5tIFbZnlmIRod6oK74xVFO66", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@36.9.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 406364}}, "37.0.0": {"name": "victory-vendor", "version": "37.0.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "63ee8bc69d89e0e163a15f0f885dac926f562441", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.0.tgz", "fileCount": 299, "integrity": "sha512-jNqpyUTCw2EbNplG8KsbVkHCBPSOL5fTQHVf/1oOBecfVoO8dwAGDm0xFCAD4w4c9llck6zgIV3eqR04jVYRHA==", "signatures": [{"sig": "MEYCIQCELNQn2KBNeyic7QUwk7bsp8rFMj65r4GsjDM6kjonFQIhAJzEMXaBjtpyQy2jxBODkrqIj+XbKJfX8dDVLweQu+6e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.0.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393347}}, "37.0.1-next.0": {"name": "victory-vendor", "version": "37.0.1-next.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "dd98221dfc54bd1f44261c1afb5d6c09bfb55438", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.1-next.0.tgz", "fileCount": 299, "integrity": "sha512-cCQCA5c1hpAcUlXDNL/wO9FSa/inflqr41F0Kiu2g1euI1DO6OG9Ec84ceZS5p0fzQCh2nKCX2ZrSpsO4x03Fw==", "signatures": [{"sig": "MEYCIQD4h9BR3P6ObgVpdFxcvus+Bly0XMnEpyrgZbdK6+vrNwIhAPdfY7RWGmv9e+zcErcPgSkPx4ZUifqRHYoWCxpzbwNW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393323}}, "37.0.1-next.1": {"name": "victory-vendor", "version": "37.0.1-next.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "84ec418ca63c94257f082ed42db42aac9b2d8518", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.1-next.1.tgz", "fileCount": 299, "integrity": "sha512-i2+JW0j0vFBdiFCsVJzO23izZI1ceYKRkvm/1O446VCOHPFzdgRR/o0q8cs8YV/W20TBgrzc2hRwSF1a+s4P/A==", "signatures": [{"sig": "MEUCIAi6oCDtYOACppRXdWi+XfN3X+dKIqOz7xSkf1AqKNouAiEAusBuqq2BSxHYNhFqFRzOLRT4S2OVEFO2JF8f5sdwVbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393323}}, "37.0.1-next.2": {"name": "victory-vendor", "version": "37.0.1-next.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "8f18c14a3bd80dee904c160608fbfe96357e909e", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.1-next.2.tgz", "fileCount": 299, "integrity": "sha512-aq1u5NPnXjbyUfrAgykS908il0sUmyhmuZiZSB8CrDnfyqb3nIoLmgyrYwc8wnsnWTpnfH7Usn1YQapFS40kew==", "signatures": [{"sig": "MEUCIEGxDG32wch6g1iw4iFXZ2ElwuF7Vf9vsr3Z0hvXwBzyAiEAipgnK2lMQ3HLB/1ZglSs9QCcWU+0rudBzCIxMf2cqh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393323}}, "37.0.1": {"name": "victory-vendor", "version": "37.0.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "d8cc96b1a81a1e0cf4c67cec596bdce2969d7688", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.1.tgz", "fileCount": 299, "integrity": "sha512-1AZGLvUO81GLtqbgfkxVoqCGzL5Cz4LnmEb4M/6jV48RfVRUBpE0mwv7NtdVDhjTVzkeyci82lPDJDaDBkTllw==", "signatures": [{"sig": "MEUCIQCK9nQGqaRZnSIWQMsbGdFh9rV81K5uvra84J6IQLmCQwIgSaEGdC0/fE/Bw43bKt+jqby73t0tEPyBVUNQcRpCI54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.0.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393358}}, "37.0.2": {"name": "victory-vendor", "version": "37.0.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "29a81f83bb5a57a2bc9da084961a4300a730410c", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.2.tgz", "fileCount": 299, "integrity": "sha512-Mjs+00QR256itUM/jVqGJkAw5OADpwjQj7sOEqLqJQfnj9uuf7cPto1KCjrS5d+E6lb0mE5kYwUfzBq0BrMa8Q==", "signatures": [{"sig": "MEYCIQCNPMVk8olSRnLYbCSOMaqCQSjK3RZxOI2rPZpAuxk2jgIhAIq0x69bflhwTWd3spsoLA2X2Awp2+6BTE2H5yJdQ7SG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.0.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393369}}, "37.0.3-next.0": {"name": "victory-vendor", "version": "37.0.3-next.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "d0d8965981d1dc9367330cea4a51a94e270c737c", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.0.3-next.0.tgz", "fileCount": 299, "integrity": "sha512-ky8nC6/JsctWNZply95X1jWINW1Nl4kwyihp0WVblRzlkgz/n4DPfaJK4eaRLT+WE2Mh/DgITz6WSlRUswPKTw==", "signatures": [{"sig": "MEYCIQDUGurM5oZb5FZN5Ud+9zDknNkvZLzP/KuEttM0KGaabAIhANHRNGhQ7k8w/UAF9llaCGdw1GYtowgxujxoxjdcV9id", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 393345}}, "37.1.0": {"name": "victory-vendor", "version": "37.1.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "3afc8c2b938f81b37bab06cd3c5683d441258c90", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.1.0.tgz", "fileCount": 299, "integrity": "sha512-ecXgkii9uNdLQvEmRQgSawdGLVLT5jAbSuyIyDeS0T5O3diTEW/UcyK+JmXJxVaNb1ZHPZ+zEW/bNg2pyGraIw==", "signatures": [{"sig": "MEUCIGJIe05dtUPU2oJTMZ1czzKaqG14re/rczEenmUwRXBjAiEA/OWdyxavpo2mWhYAWZymftR94uwbCYN4+uiENNG41U0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393380}}, "37.1.1": {"name": "victory-vendor", "version": "37.1.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "ba5651157a623f4112234c7368c920422a2b9165", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.1.1.tgz", "fileCount": 299, "integrity": "sha512-WDnoGOSqmgyFgY/+7v4i40Vc/I/iOqc9JpUniWO9TvLCWAVEmwAjKxrorBlxEv+vQxQuhxGKOf3PcJqfjZqA9g==", "signatures": [{"sig": "MEUCIQCwlp9rlmEyq90V9bkEXUWI95v5gM9LYWtrDWJsq7nptwIgM2S7H5wM1fpzAlNEyvwDQ+oqODXlMcKs5mpkgPPBGP0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.1.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393391}}, "37.1.2": {"name": "victory-vendor", "version": "37.1.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "70a10e91d58c83525db58bedfa32530495b6d4d8", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.1.2.tgz", "fileCount": 299, "integrity": "sha512-kZ2UVcoINrisEW7JDaxws2v17D4n4ShRzsPUcYnF37/avByNbjzybhvs8JrqO6+vUmoP2W1DrTEI2L/86PEQjw==", "signatures": [{"sig": "MEUCIQD9JZKkYQsQmYTvBI8fUs2c7FEr1mmEuAbylCBC/iURVgIgHpSrMXiQjK7/VylJE6K8lmx83ONRhVndGuRww3Fskvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.1.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393567}}, "37.2.0": {"name": "victory-vendor", "version": "37.2.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "b13b3de743a0a53189c482700074ee18c0bc2d86", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.2.0.tgz", "fileCount": 299, "integrity": "sha512-HQuEaWVZsRir8VH4TqK5K33EkXUz4xnrs5GLe1R1Gdky7GqHYQCStB/8V+/8p8O/mDhF4t6aluPG4XxOc1qQ2Q==", "signatures": [{"sig": "MEUCIQDPoZBoUF4tBGN8W2BuXlmObD0alwij+ZYu9xoVlkdTegIgJkhdtr5SFif5TA4ufOL6v/jpWbV/16M7DTTngJduVBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393578}}, "37.3.0": {"name": "victory-vendor", "version": "37.3.0", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "14a882d1c7d240985a30117717d8038af33200da", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.0.tgz", "fileCount": 299, "integrity": "sha512-AHk9GoSVc5RhAKB87OkFQ9a7iN6wcN1AzRH+Fm/HxkynGt4ytT6qOZKbVknHhrXTDaxwfKXtQY6YK9+iC77mlw==", "signatures": [{"sig": "MEQCIFoCEc5uucrCSNpc9ETNC+BBkS7Ukl8klKuwJyrILSwoAiBBxvYRoFtXcBPDnE2OSocuZLprxr6B/a5lQEKM/HNcMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393589}}, "37.3.1": {"name": "victory-vendor", "version": "37.3.1", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "f5796e494d48d13b5dd3e55ef48eabd06cdb8dc8", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.1.tgz", "fileCount": 299, "integrity": "sha512-ik/nlt3GgTGwyt29f2zd5Y68nNJnxtUJ2CulepE4nWTzbrbFkqhrNDTf3k+yb5ZUQBK1BLkyE79UWVNzTRCByw==", "signatures": [{"sig": "MEUCIQCZaSqyiYYc9xZMLa/kLwEC89egLKUr//loHtiFw7GHPAIgA6Gnyih6jUF+o/0KMnz/tmpqaAR4Ut01IfxE4gtoVlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393600}}, "37.3.2": {"name": "victory-vendor", "version": "37.3.2", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "75815955dc3a79b8af3022fa1e449431882b1e40", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.2.tgz", "fileCount": 299, "integrity": "sha512-2N8j0DIHocffTo2UeZbcd8fcB5+CrQq2KMOSbyTIzYuCVHP10XoS0R/ln7YOU5WoNS6/6L3GEdFWBaGYAAMErQ==", "signatures": [{"sig": "MEQCIHefHasyt9TYH/gJ3JljGKbXDd+qmNiSwOmaY6zV5Wq1AiAUfriEerZTmg0DVDkVowwgfoFpooxUiI4IZWCjQvepoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 393512}}, "37.3.3": {"name": "victory-vendor", "version": "37.3.3", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "7cab56210aac85376392ee728e5135b351a32fe8", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.3.tgz", "fileCount": 299, "integrity": "sha512-IOTdWXOFB3feUR4zEnxiBL+efah+XfyFhEvqijCTygACNQUqNn62teJeI4h7OF8kvAi3FXle4uPWUom0vlCP9Q==", "signatures": [{"sig": "MEYCIQCC3Ze4OsKdVTwNjQDZGd5T8suYJrDGSPwTJEODJIWB6QIhAKkKcUMXfk56Hvoqk2IGxECZF3TyAzYNTfLXGgEyyInu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 392910}}, "37.3.4": {"name": "victory-vendor", "version": "37.3.4", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "149c5bfa16aa37868b3f9a6f02576594e87e78e8", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.4.tgz", "fileCount": 299, "integrity": "sha512-VD0ddlxWErbl4a2KEG2ruPynLdjDskPny0FuhQkrPVz1o5SBaxkYR8/6mFHUAQBurL8w6eujqP3jJIGVNevbiQ==", "signatures": [{"sig": "MEQCICaqD4l1ED88QVkwAIE9edObVHez5qfty83SQG367/xoAiBYHFVF30SbpakItD8JLjR094gFMl+UGjrmEVmxQdTktg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 392921}}, "37.3.5": {"name": "victory-vendor", "version": "37.3.5", "dependencies": {"d3-ease": "^3.0.1", "d3-time": "^3.0.0", "d3-array": "^3.1.6", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-timer": "^3.0.1", "@types/d3-ease": "^3.0.0", "@types/d3-time": "^3.0.0", "d3-interpolate": "^3.0.1", "@types/d3-array": "^3.0.3", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-timer": "^3.0.0", "@types/d3-interpolate": "^3.0.1"}, "devDependencies": {"execa": "^6.1.0", "rimraf": "^3.0.2", "d3-path": "^3.0.1", "d3-color": "^3.1.0", "d3-format": "^3.1.0", "internmap": "^2.0.3", "d3-voronoi": "^1.1.4", "d3-time-format": "^4.1.0"}, "dist": {"shasum": "750b634b8f91c04787928760f7ed766566c1bb4b", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.5.tgz", "fileCount": 299, "integrity": "sha512-+K2VBMmB7peKG3Gjp79XjgsbfsYgD0eZRSmKz7p5a4V0NhYq43eM/b0gpSLq+Dhwag96QaWsU75/6bFVBjVE7A==", "signatures": [{"sig": "MEUCIQCWrqsy/eHBaY8bITpVpowsZRxguI8cZ8fT/8eD3f92CgIgGQyGzrM2dtZZzc8oGOxxswwBI9Otvn5ZJ9nF0VCppnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 392932}}, "37.3.6": {"name": "victory-vendor", "version": "37.3.6", "dependencies": {"@types/d3-array": "^3.0.3", "@types/d3-ease": "^3.0.0", "@types/d3-interpolate": "^3.0.1", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/d3-time": "^3.0.0", "@types/d3-timer": "^3.0.0", "d3-array": "^3.1.6", "d3-ease": "^3.0.1", "d3-interpolate": "^3.0.1", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "d3-time": "^3.0.0", "d3-timer": "^3.0.1"}, "devDependencies": {"d3-color": "^3.1.0", "d3-format": "^3.1.0", "d3-path": "^3.0.1", "d3-time-format": "^4.1.0", "d3-voronoi": "^1.1.4", "internmap": "^2.0.3", "execa": "^6.1.0", "rimraf": "^3.0.2"}, "dist": {"integrity": "sha512-SbPDPdDBYp+5MJHhBCAyI7wKM3d5ivekigc2Dk2s7pgbZ9wIgIBYGVw4zGHBml/qTFbexrofXW6Gu4noGxrOwQ==", "shasum": "401ac4b029a0b3d33e0cba8e8a1d765c487254da", "tarball": "https://registry.npmjs.org/victory-vendor/-/victory-vendor-37.3.6.tgz", "fileCount": 299, "unpackedSize": 392943, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/victory-vendor@37.3.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6RYX12bJaX3eD+w787kRKxgEqfxhIDa2+GfLAmoqX6gIhAMUziETNjWVlyO6ibirlr+i5qIVJb8kQrVXNzioFwEHn"}]}}}, "modified": "2025-01-14T17:21:52.150Z", "cachedAt": 1747660589699}