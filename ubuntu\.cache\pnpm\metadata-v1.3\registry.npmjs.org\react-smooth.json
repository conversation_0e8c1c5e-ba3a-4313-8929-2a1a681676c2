{"name": "react-smooth", "dist-tags": {"latest": "4.0.4"}, "versions": {"0.0.3": {"name": "react-smooth", "version": "0.0.3", "dependencies": {"react": "^0.14.6", "react-dom": "^0.14.6", "babel-register": "^6.4.3", "pure-render-decorator": "^0.2.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "eslint": "^1.10.3", "webpack": "^1.12.11", "phantomjs": "^1.9.18", "babel-core": "^6.4.5", "karma-mocha": "^0.2.1", "babel-eslint": "^5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-preset-react": "6.3.13", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.6", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "dist": {"shasum": "ea2fdc1065b525e94f36f89d7806862a0bcbca8c", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.3.tgz", "integrity": "sha512-MFyOCWGdlLbgTfRTLbxFEHgTyxc7oOgijf+93uhhQZEKCeRVYylMwbHcvlwft0movmLqo5kT6KeiI7FRmMQaNg==", "signatures": [{"sig": "MEUCIQC42y6ZHPJpUc4NGzVhTWm+zI7Ia4DxPtploUbFeBj/RQIgBfq7G0wJSe93GMMA23MyiT8ze4y0Rj/LPeHdhIMR2P0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.1": {"name": "react-smooth", "version": "0.0.1", "dependencies": {"react": "^0.14.6", "react-dom": "^0.14.6", "babel-register": "^6.4.3", "pure-render-decorator": "^0.2.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "eslint": "^1.10.3", "webpack": "^1.12.11", "phantomjs": "^1.9.18", "babel-core": "^6.4.5", "karma-mocha": "^0.2.1", "babel-eslint": "^5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-preset-react": "6.3.13", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.6", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "dist": {"shasum": "1846150f062f15de8243f04a92d52e50f29a2419", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.1.tgz", "integrity": "sha512-tkfpfc+3GBe2EN6KDyoKTFUsB2P6I3cYQOeuwD8m54WzTePv7CgrxRSpnz795RFwUoQ7T562XzjGMGdPXBh8zw==", "signatures": [{"sig": "MEUCIQCUJuJXoGNblpoYbt6QUqXiBFqgQDy8moM5sBwEJ93vJgIgS0M+MZHSrP+hAPco0HsfT2hg3rAZ3xwqb/Z0A3BmFeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.2": {"name": "react-smooth", "version": "0.0.2", "dependencies": {"raf": "^3.1.0", "lodash": "^4.1.0", "invariant": "^2.2.0", "babel-register": "^6.4.3", "pure-render-decorator": "^0.2.0", "react-addons-transition-group": "^0.14.6"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.6", "eslint": "^1.10.3", "webpack": "^1.12.11", "phantomjs": "^1.9.18", "react-dom": "^0.14.6", "babel-core": "^6.4.5", "karma-mocha": "^0.2.1", "babel-eslint": "^5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-preset-react": "6.3.13", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.6", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "peerDependencies": {"react": "^0.14.6", "react-dom": "^0.14.6"}, "dist": {"shasum": "c3ecdc92513e64f8dffbce2b5550e7c9adb9cf56", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.2.tgz", "integrity": "sha512-oDkpWWZ2vS3pu6+iCBcpfQQX8Vz0a3iKTOSFXRjv21bYiFoBF43C6QcEdk301cxFkgrfGyL1RiklNExSTmm+Tg==", "signatures": [{"sig": "MEUCIDAaJaQ1iUChNOk6PD00NzHNO8INoMiFc1WaEdHDcI1AAiEA2gRRk2DpSWvZoMrMhvSA49qqTAQm3RIEHyx7HfMEtFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.4": {"name": "react-smooth", "version": "0.0.4", "dependencies": {"raf": "3.1.0", "lodash": "4.1.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "0.14.6"}, "devDependencies": {"chai": "3.4.1", "karma": "0.13.19", "mocha": "2.3.4", "react": "0.14.7", "eslint": "1.10.3", "webpack": "1.12.11", "phantomjs": "1.9.18", "react-dom": "0.14.7", "babel-core": "6.4.5", "karma-mocha": "0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "6.2.1", "karma-webpack": "1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "6.4.3", "babel-preset-react": "6.3.13", "webpack-dev-server": "1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "3.1.0", "karma-sourcemap-loader": "0.3.7", "react-addons-test-utils": "0.14.6", "karma-phantomjs-launcher": "0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "peerDependencies": {"react": "0.14.6", "react-dom": "0.14.6"}, "dist": {"shasum": "aa50f451a459bf778d41749ce4ab49b6085bb7f6", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.4.tgz", "integrity": "sha512-fGBqbN9i/b49lDtseC0dOYJcdJrnJbTepdLBk/VBAOYtdVhLwQAA/TcXPGdpHxmnfUmslHPmx6FHIcDoIoW3yw==", "signatures": [{"sig": "MEUCIQCnYLs8RaYYmLEGX3SVbLpfp0uSfNMVqDZVp/8S2lnvYQIgMBo4P+5O+zSpMvDudDVFITwtWVz6pmUpbrACQewNUcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.5": {"name": "react-smooth", "version": "0.0.5", "dependencies": {"raf": "3.1.0", "lodash": "4.1.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "3.4.1", "karma": "0.13.19", "mocha": "2.3.4", "react": "^0.14.0", "eslint": "1.10.3", "webpack": "1.12.11", "phantomjs": "1.9.18", "react-dom": "^0.14.0", "babel-core": "6.4.5", "karma-mocha": "0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "6.2.1", "karma-webpack": "1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "6.4.3", "babel-preset-react": "6.3.13", "webpack-dev-server": "1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "3.1.0", "karma-sourcemap-loader": "0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "cf523221c7db51d55806c5fa4582498a609732af", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.5.tgz", "integrity": "sha512-qW4uYM6J5Y+l8jmz0igIXEeU3vXwdaDJYUqhkzmPjl3a/Vi161u/lriKWL/Z1nrJsU1nBsznm8JCMl0hKepouQ==", "signatures": [{"sig": "MEUCID2gIFDW3BfhhcHaWwWNTgQrs+mqM2BtyrdA4lU845EjAiEA4E8iyym0VyBTPZisTFr8s+GIzUvhiy7qGdhKgEvA7BM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.6": {"name": "react-smooth", "version": "0.0.6", "dependencies": {"raf": "3.1.0", "lodash": "4.1.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "3.4.1", "karma": "0.13.19", "mocha": "2.3.4", "react": "^0.14.0", "eslint": "1.10.3", "webpack": "1.12.11", "phantomjs": "1.9.18", "react-dom": "^0.14.0", "babel-core": "6.4.5", "karma-mocha": "0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "6.2.1", "karma-webpack": "1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "6.4.3", "babel-preset-react": "6.3.13", "webpack-dev-server": "1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "3.1.0", "karma-sourcemap-loader": "0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "39a021dd23570970a0718d69a95a2930712afb51", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.6.tgz", "integrity": "sha512-O3oIVMBNU+cn6BRJwxe8b65hWPtLfgVjjDAWvqni9SmnrHM0RRqJrqESk5bUd8YZPe6a0F/8A4aownk1HDNeVg==", "signatures": [{"sig": "MEUCID1TGdHfCLhTWpCRTdAMnRQ079veJFrMS9XXz0xQ5YdKAiEAjrnPW8hKvos5UD4X001TMlySjUd9i6s4eliX9GyRUMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.7": {"name": "react-smooth", "version": "0.0.7", "dependencies": {"raf": "3.1.0", "lodash": "4.1.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "3.4.1", "karma": "0.13.19", "mocha": "2.3.4", "react": "^0.14.0", "eslint": "1.10.3", "webpack": "1.12.11", "babel-cli": "^6.4.5", "phantomjs": "1.9.18", "react-dom": "^0.14.0", "babel-core": "6.4.5", "karma-mocha": "0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "6.2.1", "karma-webpack": "1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "6.4.3", "babel-preset-react": "6.3.13", "webpack-dev-server": "1.14.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "3.15.0", "babel-preset-stage-0": "6.3.13", "eslint-config-airbnb": "3.1.0", "karma-sourcemap-loader": "0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "0.2.3", "babel-plugin-transform-decorators-legacy": "1.3.4", "babel-plugin-transform-export-extensions": "6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "e11e36763263c7e24b3bc17db3ae176f753205fa", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.7.tgz", "integrity": "sha512-0RkxwDbtwZCQBG3iqgnxFKzpL30NYV5gm3yv+YzncO/US5nYDvtdSgBo2WHXrVTpR2LA2De802aKdLfhHlOHDg==", "signatures": [{"sig": "MEUCIQCOrkYEgkOlKwEK79ZU8FqmG7IXmNtbBdjga4fUBqQkLgIgQ2IlUN8lGt0yUBt76V6hyU5aWmcUrwxumVhLLEyryJk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.8": {"name": "react-smooth", "version": "0.0.8", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^2.0.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "b06be97c087d739d537d495f2422ffd5a1fd4239", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.8.tgz", "integrity": "sha512-erhrSW1LM84g8V6o/esKxSmRDEbma70ah36RrO/KUN8RXfPt8JyqQHdAG/jAW8+ICPOoELtzEigUyhIUCght4w==", "signatures": [{"sig": "MEUCIFudvngXFKlCOS2DgQsnIzr3dqJTXzmAJrp19v+JDY2dAiEA945k4RDLojC+puL61olk0NzROKCRitsH1GnjDtyysEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.9": {"name": "react-smooth", "version": "0.0.9", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^2.0.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "314559aa58e3cc43e3cc2d62aef0d52b62579193", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.9.tgz", "integrity": "sha512-S8h2DR14plQHVhO7ONbKE6xmu7np59fDMhEt5TXJN5AaWi5zExlj3iYmxWzZYAzNnm2QVlKxFLeckNCu8SCmLQ==", "signatures": [{"sig": "MEUCIDukhgw7d6MF3w15qWIfdsoXeZ02TUXhsrvgAdWfE5nZAiEAxacaDzNS8J5UbObAIYQYtUEgZ/e8FdLlp1t+wy95XBU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.10": {"name": "react-smooth", "version": "0.0.10", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^2.0.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "677072d872bf17db884d02403ea8f0bd0d368c08", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.10.tgz", "integrity": "sha512-37skFr7GzFeJAHBSQC29CVm2Nr9YzR8Cs6EQPC0d9SCTusmo7TWftfLOLFgWaBeP/uNkllqf7j3Oluu+Qyopyw==", "signatures": [{"sig": "MEQCIAq75BphKJSjF58S4NqcEI9br/yx9ECKF/u4sdrAvKB6AiAevRinY9mpNrHIvDBoA6ieFEHQSSWEmc6OUknht7mpzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.11": {"name": "react-smooth", "version": "0.0.11", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^2.0.1", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "karma-phantomjs-launcher": "^0.2.3", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "7004537d16775f3bbccbc65cfc5f9d61c0b99c02", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.11.tgz", "integrity": "sha512-AjqnY7UaF2kBIxKur7GL09DTxWHt35ZxlN1xgIThm4kwzKFNmMfFvFhGJbqsXVMziIhFLR4dv9hm8H05lSnTkA==", "signatures": [{"sig": "MEYCIQC3kZQOn0E+nDc0uvjqDDIdqo40+uo44oYFYXEv3C1M4AIhAPbWThyKJ1zARlPTODfYtBlcx1/Gm1NBXYvUkURJdamw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.12": {"name": "react-smooth", "version": "0.0.12", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.1.0", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-chrome-launcher": "^0.2.2", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "e27a86fe5c3b841f2bee9ce81f7a731cf1fdb41b", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.12.tgz", "integrity": "sha512-HrieWM+PEjzmAolu/wXy7awaNaXAjye0+t7QOQPXTwaWlW/+9O2f3XS84h0rSEVKD5QxUTqTgveiwoSMKpwAHA==", "signatures": [{"sig": "MEUCICyksTiZs/UlApU4V3zgeNILZwtwPgqwl/+tETiHlWg+AiEAtO3EDCEioaUqZCWN9RlzTiax1Vp47vBgFAC//i9SRVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.13": {"name": "react-smooth", "version": "0.0.13", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.1.0", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-chrome-launcher": "^0.2.2", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "9067b4a642a5b58bbdfdafc36e3ec8d97e85a349", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.13.tgz", "integrity": "sha512-wV3P8t8lslOn60KUsSXu4+hiaRS7Z5YOy7p8dVW+Aufh8IAe8JuoDmUtXgHszqVuD3GtU8CLvn2YX8D6gTscUA==", "signatures": [{"sig": "MEQCIARhU1nXk9Sv6MCe3MXATtaD5YrBAunB0rPtCpNe7qZfAiATskF1FVDvQPAnjYvdQnJlNYl1UHxCGTs26oG2VVTpoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.14": {"name": "react-smooth", "version": "0.0.14", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.1.0", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-chrome-launcher": "^0.2.2", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "cb42e1a93469a53be128dcc24c3d803d04e0e871", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.14.tgz", "integrity": "sha512-qe+Pv1tfRVvsYPNMY0D0wvto9vzUWQzk6FqacYYAc1uL96R2TeQFH59QaH3exyyrX2NObh37339n6VMZKeBewA==", "signatures": [{"sig": "MEYCIQDTWB+M4ylOxYMT9jovdGFK1AUsc+ctpgoAVjV8XnT+EwIhAJD7A9ojDVnB/McptYq9JSldwobTM0wNbGpdDKbyX1Da", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.15": {"name": "react-smooth", "version": "0.0.15", "dependencies": {"raf": "3.1.0", "lodash": "^4.0.0", "invariant": "2.2.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.4.1", "karma": "^0.13.19", "mocha": "^2.3.4", "react": "^0.14.0", "eslint": "^1.10.3", "webpack": "^1.12.11", "babel-cli": "^6.4.5", "react-dom": "^0.14.0", "babel-core": "^6.4.5", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.1", "babel-eslint": "5.0.0-beta6", "babel-loader": "^6.2.1", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.3.14", "babel-register": "^6.4.3", "babel-preset-react": "^6.3.13", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.1.0", "babel-preset-es2015": "6.3.13", "eslint-plugin-react": "^3.15.0", "babel-preset-stage-0": "^6.3.13", "eslint-config-airbnb": "^3.1.0", "karma-chrome-launcher": "^0.2.2", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.3.13"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "54e11e34ade26f1bc3be03f47b7e48e07129c153", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.0.15.tgz", "integrity": "sha512-Oq9YWdKPeDK9pV2Q6Ykp5N8RoMDIul6X4zw70ihniLV2fy5hzkRbax3f/5B2RWliROdEkI86vhifL717Q3xfig==", "signatures": [{"sig": "MEUCIEqTF/ltJZIu9aFjGeNnSmX6oexhE7QsxmQTbGIKGbJgAiEA8VbMd1OjwJaTsCVDbq/Vvr0i554FIUrCrF0I8C5o4ZU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.0": {"name": "react-smooth", "version": "0.1.0", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^0.14.0", "eslint": "^2.4.0", "webpack": "^2.1.0-beta.4", "babel-cli": "^6.6.5", "react-dom": "^0.14.0", "babel-core": "^6.7.2", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^5.0.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.2.2", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^6.1.0", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "9b6c926da7c254ad98741af70dbbb7e127eafcaa", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.0.tgz", "integrity": "sha512-cdJl6wAixXPfiQGgHahA+SlKPvi0Wczav/SVaua92twbf+A77qUr0UesbDpV/awRJCYHxLL/g2XzW/Yu71Xxsg==", "signatures": [{"sig": "MEQCIEclNYma6tSsDG9TC9wpQ4/yMuIL29/e8xTOg+LbBI4zAiABBJuMRtOknakHB68pm8XQBYpMN9djazx/rssMSP0d1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "react-smooth", "version": "0.1.1", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^0.14.0", "eslint": "^2.4.0", "webpack": "^1.12.14", "babel-cli": "^6.6.5", "react-dom": "^0.14.0", "babel-core": "^6.7.2", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^5.0.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.1.0", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.2.2", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^6.1.0", "karma-chrome-launcher": "^0.2.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^0.14.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0", "react-dom": "^0.14.0"}, "dist": {"shasum": "dd1a8d12d7846f54ba38f4135d146dae07c9d30a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.1.tgz", "integrity": "sha512-7nuy2vJfqYSr839yOlhebq8oxVUpBI3Utg1jZfV4cUf39lXm/r/jA1psfr7I+S4f3yI4zq2xHTxCkAOIMCrBNQ==", "signatures": [{"sig": "MEUCID13lU1gUNx7JwwvhTk7S/uhwZs6Q5PsFRQZlVoHZgHdAiEAimHMhX8xadN0IN5ZJk71/XeAH9HKFtyDXLJyj1VbST8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.2": {"name": "react-smooth", "version": "0.1.2", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "react-addons-transition-group": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "afcb836bc3b0383b804ecee306427e4b219a2206", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.2.tgz", "integrity": "sha512-SLaxpeq0W4uE3jNy8wJ83uzUlDunEKN0PKalP74B71cWqJAnknCbvi5beHtbIMo87CKw3guo3UlvWeNmHcjyfA==", "signatures": [{"sig": "MEUCIQDsLnxA/q6p1ZnKfaN6PF72hArGxILxCwWUWHZRQlWHhwIgfeDM9ZvuEIH76Zm2CzBHEe+vejZFk2inROGFmvCr4Lg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "react-smooth", "version": "0.1.3", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "pure-render-decorator": "0.2.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "react-addons-transition-group": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "519f713cee3473d282aecaa3c2f631c032f275f5", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.3.tgz", "integrity": "sha512-/ekcKNVpNib4o7+s96/gFJVhBzZRUaevDdvOoNCe+0pvPG7Io0PCLuFnpoGYCY8hTb8jSEBGHaurNx9ZPkj7Wg==", "signatures": [{"sig": "MEUCIQC53D3G6JCQSUM6FC/mb7aeWzavL4G6ADahvAmCrs8dTAIgM1mkiQxnUtqRi4dTAomI6MmD0ZU5NtqUBnsyjeXm/Jc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.4": {"name": "react-smooth", "version": "0.1.4", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "pure-render-decorator": "0.2.0", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "ad78ef9e037fff31a7f0c3e70729a6e79fc19236", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.4.tgz", "integrity": "sha512-RrOlmBdCzSF/R/8esk+/aTghSDDakvPQ7ubC6ulU+FTjgwgkoXshEaUAQEOtPh+R1MX3hAj0bdCWi/Dnm2jTMA==", "signatures": [{"sig": "MEYCIQD9U0XtKLY4ywbAk8eNwuoPFHEOF0eVioo03XNzrPcY4QIhAMu9Yrj/CbecxlRC+zpCc5/OzKvWS8qVdJp6lki/SzxB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.5": {"name": "react-smooth", "version": "0.1.5", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "82ebafbdfb587b0fd4bc73fc92b80bbb942932dc", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.5.tgz", "integrity": "sha512-irg1vgxxuy3dwXCDLZY4seRUNNvE81iPAT4G8N6QtjUMGOe1Nme9e7YwcHYVv6/OaYGVvMKaX9Kzoz5wf7CdIA==", "signatures": [{"sig": "MEUCIQCMRJzRxRnMzq4q0ByhwKFSJPLSI/uB8giX12s7OSQNMQIgPr1DTkTYrJi4bvg7TiBS8B5lWWrt+t9m6zY6EIiRkuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.6": {"name": "react-smooth", "version": "0.1.6", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "cb50cecec1af6147baa23a5cbc19333a79a8bb9d", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.6.tgz", "integrity": "sha512-Og241Db4hgbIKi6j5Bb31wdiQmFFe5CDaOPe7hGm3IUx++MiobkfoUdYA0UZwUJue2QnLP5u83A5ND/YnswCOg==", "signatures": [{"sig": "MEYCIQDgrsXc5pwRQCqVGcaRGwGq2hv/ai9Jhu6n3ZMLPs2j9gIhAKBd2Uld0Xw0pAL8niwgJUwmMN4NbEoRLnnZPY4fyZ5p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.7": {"name": "react-smooth", "version": "0.1.7", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "23845ab22a88f92bfbcb24d158759d7092305642", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.7.tgz", "integrity": "sha512-ZkbQjA0w2pSrAPMN2XWv+nkvAiFilMDHfAqZQJK1hE0XmCbty5sqXePF+oSoga6tB9asN6iwJDuEuTgT4nOlsA==", "signatures": [{"sig": "MEYCIQDy9rY8OblceCavTNs/ksB4grbYz92tomPI6oSVcN6xAgIhAPjK2RsJ8FK7Z2dJRIylTsah01NsEYbBajhYPPu/Q95x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.8": {"name": "react-smooth", "version": "0.1.8", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "aaa5c3fbe3d1b3770a3505542a90d336d2f67987", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.8.tgz", "integrity": "sha512-c00YtJWjXZouFnth7HcaccIV5mdjaZeGk2dAH7rhnrN457pZIFUfX112wx6BdBMZt2uGHW9UXLnYSikDR0jw4g==", "signatures": [{"sig": "MEUCIBlyg1wuEwNXm1Y5pi4S7IO+cey41Oy2WlXvUdh5G5ArAiEA18tYWAgE26I29L7ByS4lEz+KBYpeIfmP+DOZWYfE6i8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.9": {"name": "react-smooth", "version": "0.1.9", "dependencies": {"raf": "^3.2.0", "lodash": "^4.0.0", "invariant": "^2.2.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^0.13.22", "mocha": "^2.4.5", "react": "^15.0.0", "eslint": "^2.7.0", "webpack": "^1.12.15", "babel-cli": "^6.7.5", "react-dom": "^15.0.0", "babel-core": "^6.7.6", "chai-spies": "^0.7.1", "karma-mocha": "^0.2.2", "babel-eslint": "^6.0.2", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.7.2", "babel-register": "^6.7.2", "babel-preset-react": "^6.5.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "2.2.2", "babel-preset-es2015": "^6.6.0", "eslint-plugin-react": "^4.3.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^7.0.0", "karma-chrome-launcher": "^0.2.2", "eslint-plugin-jsx-a11y": "^0.6.2", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.5.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "8cbd79e75ee7a22837dfa22cb01dfc38aa989ada", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.9.tgz", "integrity": "sha512-FBVE+jRFY3snCj6N6vJ4NUR20/SxCX00UB86NAxHJoBQz2xSh7F/hCkMKGCBlXBdjilG5IsW3zZUBiph0V/Uyg==", "signatures": [{"sig": "MEUCIGVrmSo7bC3TyVDQHwyWGaHj2FV71u4f3ZwLz8bPXKzdAiEAku0e7o98lDGxvDVIq2yTMfZk/EW9+PrMSCHnr6/D374=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.10": {"name": "react-smooth", "version": "0.1.10", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ~15.1.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "~15.1.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "~15.1.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "~15.1.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ~15.1.0", "react-dom": "^0.14.0 || ~15.1.0"}, "dist": {"shasum": "d4535e0a022cfafbe5f0bdf3c023e606431021d1", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.10.tgz", "integrity": "sha512-kXBblB4Z9QDIlM/poWoNyTvPB9sCBTf1aPMNyqfe2BXf082b5T8zghY0wvCktpBAaNNH7uLOJJ78k65lqJ2qOA==", "signatures": [{"sig": "MEQCIGdHzeGZaICBhbMLoVlLCT1k8U1hNSxpLPwEvyAac3OzAiB0hjCDMI493SUi/u2jw4CU6VG7sNEiiQPp6Mjmb7mvgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.11": {"name": "react-smooth", "version": "0.1.11", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "f389f8e58cbb546abb4921ca0cbe7b079bc51a0d", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.11.tgz", "integrity": "sha512-1VojWVLpKE1rhBrvwVY3rS4fQvBaGqha4iq5m8IVE5/eacxvQggkb3OCRbn0R+X3NILJrFJ3S2Jsx7sp3Od1Dg==", "signatures": [{"sig": "MEQCICRIoX6GBZGcVb4EKS5uKvDSAPGHv6XWMIuuHpzoGInAAiAiGcprFZYn/1q2OsOIiAGN8eRSfX/Upv/4eF6rCx704w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.12": {"name": "react-smooth", "version": "0.1.12", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "f831a40f61ca448636e2322fd129322f68bf712e", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.12.tgz", "integrity": "sha512-aZssi2ya5OVLUejrkDGa6Qbw5BUWiyOxKng7T2lIT5tzkOLgGj4OJrM2kFzxJFyd3svmbjwK6rCDvv7O2XfnUw==", "signatures": [{"sig": "MEYCIQDAo+1dGm700vst6nc6nssLAbClLMG7oavgcdO8zBneSwIhANenV9RBRXWstbu0p9Ckm36kojdbobqVeYdieKwfdKQg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.13": {"name": "react-smooth", "version": "0.1.13", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "2517b80f65dbe32bebd02c14ada4b0842a7fab6b", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.13.tgz", "integrity": "sha512-WRAF622q4YY1W4m3LyIX1E7G8eDi/xTbBJEjnTILRB3D9p56hVbSAU4QMkBVlzo0CFRRDd7a9EQ1vNdLOduKhQ==", "signatures": [{"sig": "MEYCIQD7xcQxc27uYqO6XvsFPEatS3I+P/q3dvlPoNd8IRv2PwIhAPicLOnq2Bof6Fcth7ik2Rv3HgWYP3bdWUNc4VxBdtFK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.14": {"name": "react-smooth", "version": "0.1.14", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "7a5d379a8f899a643f81be721557e9669c044b85", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.14.tgz", "integrity": "sha512-Yg+hZ35HEkZxfnElGogX1c8ZIXFoH/h1VQnomNwn5NOxrWOGTpatgnB3DJ3ROkSZJEFTpkPTbhKHnUZcnamVSg==", "signatures": [{"sig": "MEYCIQCqrqYZB7KidUHeNHuFwpkGF+edCPDScR98UAj+aAW8IgIhAPsEJbk1eB/FVxsMqBh0/zmh0dy5qY56PB6zjmsQptR7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.15": {"name": "react-smooth", "version": "0.1.15", "dependencies": {"raf": "^3.2.0", "lodash": "~4.13.1", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "144e93ccc3d357b2f77eb0a64e5cdbddecb76333", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.15.tgz", "integrity": "sha512-oViU/2RfJkTnTBEQ/TuT8X7MyPw2mafAouLy/PhZtyfWvw5lfG9G/g96shungZaJE4gELS1rEfdBk/E685jgTw==", "signatures": [{"sig": "MEQCIGhjno3SZBKqLx+dL1T0/jKvtOVMdENSf1Eb5fDTSNkZAiBDue8E58RvkqNVridIrS/D9VRF8K9HKvx2mjjWl8n4Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.16": {"name": "react-smooth", "version": "0.1.16", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "efcb26ab51edb63ccc6849962f4ff4cfb5aae0cb", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.16.tgz", "integrity": "sha512-vqRDrRI0l8G1lQicNyPdUoLkLtAa5+nB1sjmQ98cy8caHzoJdj+TihVtW8MKbYqsu60FjvClm/C3J4g0sh8UVg==", "signatures": [{"sig": "MEYCIQDcC6oiIVvWxwCr2hxy6pLD61DW1AAuDgWUttjp5oOmMgIhANMaYx9H8JLYJswJLMua7CRWN4AmeO9SKyeyj7ptzHEM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.17": {"name": "react-smooth", "version": "0.1.17", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "0810990fdcbb5a5ceaf1fc4ebcb00f255f8e8935", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.17.tgz", "integrity": "sha512-lULz6zX/9Vy3DwK5T/MrsKSwaJklJ/5TUZzWUrsbNyi8uSSSCFoi7k/S7rwEkliphZYuOeH8jZ+VtEUm9i4l8Q==", "signatures": [{"sig": "MEUCIQC1+Auzoh2BPm0NGH270YtmKQCaeCg5uu67zXUz6xRCzwIgFVPScet5gCf8BIGY/Sx1npADdjXBXWOTmphSZuQ2HYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.18": {"name": "react-smooth", "version": "0.1.18", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "93a256c634c8079015da5ab1cd4617eed35cabe3", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.18.tgz", "integrity": "sha512-vWNe7KQs6fdlH7Kr4MMUtvSi1pxIaIBTPBThNGhclN7HVYuRUVKbK4pYlxIju+Wk6otpWGDNdydqNvzcnsRvUQ==", "signatures": [{"sig": "MEUCIQDAqdu6gyONwQqXf1+dYr9bM4lH8bPM5kFNmc4X/4jk2gIgVzqBCutK2aFwayoIrp3KTZyQdr84XKfOMIrMExUjIYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.19": {"name": "react-smooth", "version": "0.1.19", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "a4657a5a8364f28e5fdf9b0a9abeb324a3cb661a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.19.tgz", "integrity": "sha512-h26O8aGxClsCTPbQbVs5W60fukoSxeC7MVL9cI93cpNQx9i3Wru+PWc0RVH2k2yRZeCp+b0wSdsAI9LVxbsPmw==", "signatures": [{"sig": "MEQCIA/btz0MhZ4y+QVgS1DVjsC0SMmAZ0i7v4k1uFzqnDD0AiBl5PlHoF+M/tLSFpAXg/n5mlu93+RGZ+zxQ4eGBuX0Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.20": {"name": "react-smooth", "version": "0.1.20", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "react-addons-transition-group": "^0.14.0 || ^15.0.0"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "cross-env": "^3.1.4", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "0527eb60d0ee529e98720127aea5d3fbd2e59fb7", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.1.20.tgz", "integrity": "sha512-+ZA89X8yOiWCrL6CsHEjsL7hWC6DhO/2akRCn4HiwlOh31wMKR4f6p2k4XHUiA84vONjIl8zkyXUmzmssPOzRA==", "signatures": [{"sig": "MEQCIAxN6SzI7XEXAfKtEJWRohJA99ThXM+tA48KZOeAdskmAiBUtjNPjQn/jCqHjn+qDALtYbT0NyufZL6DcCB+TnetYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "react-smooth", "version": "0.2.0", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "prop-types": "^15.5.8", "react-transition-group": "^1.1.1"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "cross-env": "^3.1.4", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "3f06db3d5e642ff22f793e335fbcef76953a743a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.2.0.tgz", "integrity": "sha512-d+aTFynTKy88EtrC9ydpE3K0v+6DMX+3FPZTArcPho1CQfnGpW0Rnth8sF/yNEBJkAagMgYCkela64X7o63R2g==", "signatures": [{"sig": "MEUCIQDJvoHYBwkI+466K/D3SB9KgaaG3icNu+ouu3dbzke0SQIgCQ0ZdEbGpP2ivFJAOaR7YgPRx7Vs70dLukYxllmsC7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "react-smooth", "version": "0.3.0", "dependencies": {"raf": "^3.2.0", "lodash": "^4.16.4", "prop-types": "^15.5.8", "react-transition-group": "^1.1.1"}, "devDependencies": {"chai": "^3.5.0", "karma": "^1.1.0", "mocha": "^2.5.0", "react": "^15.0.0", "eslint": "^2.9.0", "webpack": "^1.13.1", "babel-cli": "^6.10.0", "cross-env": "^3.1.4", "react-dom": "^15.0.0", "babel-core": "^6.10.0", "chai-spies": "^0.7.1", "pre-commit": "^1.1.3", "karma-mocha": "^1.1.0", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "karma-webpack": "^1.7.0", "babel-polyfill": "^6.9.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.11.0", "webpack-dev-server": "^1.14.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.7.0", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^1.5.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.0.0", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.8.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "dist": {"shasum": "b67665d7b9820257e34279c15e02e8e5131ebe99", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-0.3.0.tgz", "integrity": "sha512-sV/M3wwANbUIsThztleHBAfAOtpXJQbFaV/wtFeROYzhiXR3+/ozBFRc0MjX06ZKMp/SbyFic+38wWDgrvqyOg==", "signatures": [{"sig": "MEUCIQD+dVAWHV//Y23MXAopvymudhTWvpJn2MWVYjrFB6ulygIgIRTw3JFyXSTfqw/n3Wf9OVdN0qG2hs6ToNAWtS2YyOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "react-smooth", "version": "1.0.0", "dependencies": {"raf": "^3.2.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.2.1"}, "devDependencies": {"chai": "^4.1.2", "karma": "^1.7.1", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^3.6.0", "babel-cli": "^6.26.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "babel-core": "^6.26.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "chai-enzyme": "^0.8.0", "karma-mocha": "^1.3.0", "babel-eslint": "^8.0.1", "babel-loader": "^7.1.2", "karma-webpack": "^2.0.4", "babel-polyfill": "^6.26.0", "babel-register": "^6.7.2", "babel-preset-react": "^6.24.1", "webpack-dev-server": "^2.9.1", "babel-plugin-lodash": "^3.2.0", "babel-preset-es2015": "^6.24.1", "eslint-plugin-react": "^7.4.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.0.1", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "babel-plugin-transform-decorators-legacy": "^1.3.4", "babel-plugin-transform-export-extensions": "^6.22.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "b29dbebddddb06d21b5b08962167fb9eac1897d8", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.0.tgz", "integrity": "sha512-dkegzflIpZjvL8gujUwFqsOU7Mv7GbsjlU6nZCiIvY3gQcRyob+K0Y4oBQT4IMo7pb0vdAWBAm0y14/EwrkzwA==", "signatures": [{"sig": "MEUCIQDoYaTaGUjBYFdHNtEQtWU5Tmdc3rZinbfNg5mp9IVsPgIgb6g6utEFBC01gF5XTOoRWiCCm17oBh9NLVYYEDTrDUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "react-smooth", "version": "1.0.1", "dependencies": {"raf": "^3.4.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.5.0"}, "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^4.20.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "@babel/cli": "^7.1.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "@babel/core": "^7.0.0", "chai-enzyme": "^0.8.0", "json-loader": "^0.5.7", "karma-mocha": "^1.3.0", "webpack-cli": "^3.1.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "karma-webpack": "^4.0.0-rc.2", "@babel/runtime": "^7.0.0", "karma-coverage": "^1.1.0", "@babel/polyfill": "^7.0.0", "karma-coveralls": "^2.1.0", "@babel/preset-env": "^7.0.0", "webpack-dev-server": "^3.1.9", "@babel/preset-react": "^7.0.0", "babel-plugin-lodash": "^3.3.0", "eslint-plugin-react": "^7.4.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "webpack-bundle-analyzer": "^2.11.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "cc7aeaaf46d552f7662e1ca5cb794cf3760c22e5", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.1.tgz", "fileCount": 36, "integrity": "sha512-4Je326q3mCQogOcNkMCNjiuf9U8F+BSFZuuS2pCEn9H/Ki8tsxJwPJQGrS/IrPq1cxxcsF3ZuhtdoFUDmCx2LA==", "signatures": [{"sig": "MEUCIDiFzZUa5S/MAneL11DM2KjZ6OZVUCUfqrgPzkH+O30GAiEA6xERoiSUaBQWDaNxx+tYkEKBK9qT3JGu8xFs31XH/sY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs3e1CRA9TVsSAnZWagAAKM0P/0birGht9mPFhHZIW7d5\nN5X2A1oiQMRwRlv8gpQa2z4exwVyZAkuPI/n4Xu80zBLplfrXYczgUSHzRYB\nhV1/5jdNkYum/eSwFzxSPcbiFN01R3ady0wjwD20eW5ivto59Ndv8d1y57vN\n5AvXW+z3+Mva9fakjJiH5iCkloYFrAXCUzGRbFKbZDaxhgOxb1vP/nrP2kWe\nm4+JOncdI6NJxhz24puQ0ykft/+sfYzXS9nOh1KVmpzYdaCUt0h5ayn63HBv\ngiwUVZcbWyZhqXFlIA7ae1po7va67UnOEZnVkW7f75f6O9j1npKcuQyWrqaB\nVjYPu8bNJ+ijV63x7QdLoku94mINdwyQgf6fRfSwkw4s9t8RyW0828w+JO+c\n0JZUTfjfkX2fjUjAdJ30IknvXtn+qFgFuI1VwjRfTh5IKd1PxcmCNkglORFq\n9p/xKlyJt4LcbClMlfXhB1LPfpFfJo8AF5/qcZpwcbzpI7Oo9GxSv/D73T7B\nzw6tOq+cnP54Loi8els6Gq+LerUpWClw3yItCUAhjrMMTdWVH6ViA1dQ+Pcq\nd1xXI1F5soDUPmYnTvXtD2zeHvy+Fj+bswy6UmfP0R0nLV1kabelxvJrYMiv\n3wRzKH0hV0Zc1OJ4Fh3B0VEX5JB3E4k4HZs1fJkyPggIlmSlmz5/3M+Df4Jg\nrufa\r\n=l2mm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "react-smooth", "version": "1.0.2", "dependencies": {"raf": "^3.4.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.5.0"}, "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^4.20.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "@babel/cli": "^7.1.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "@babel/core": "^7.0.0", "chai-enzyme": "^0.8.0", "json-loader": "^0.5.7", "karma-mocha": "^1.3.0", "webpack-cli": "^3.1.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "karma-webpack": "^4.0.0-rc.2", "@babel/runtime": "^7.0.0", "karma-coverage": "^1.1.0", "@babel/polyfill": "^7.0.0", "karma-coveralls": "^2.1.0", "@babel/preset-env": "^7.0.0", "webpack-dev-server": "^3.1.9", "@babel/preset-react": "^7.0.0", "babel-plugin-lodash": "^3.3.0", "eslint-plugin-react": "^7.4.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "webpack-bundle-analyzer": "^2.11.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "f7a2d932ece8db898646078c3c97f3e9533e0486", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.2.tgz", "fileCount": 36, "integrity": "sha512-pIGzL1g9VGAsRsdZQokIK0vrCkcdKtnOnS1gyB2rrowdLy69lNSWoIjCTWAfgbiYvria8tm5hEZqj+jwXMkV4A==", "signatures": [{"sig": "MEYCIQC/ak+HzT5NZZRG6oVA8rB9bBLAa0IOOwOxCc5FywohhAIhALl07ZzI9+hQq4Tp/kD5f60DlSXnUKqunw34USZt3aJ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 463663, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs5OkCRA9TVsSAnZWagAAFI0QAIrBuC+WxroJ6eYq32RO\n1GsFFT1ZAiwZvUTQfuOXOrjjWsnXO6x99Xgoa249cbz6VLlecZzm4OpHVhKX\nFy7B1W4odVk/R7FvSXQsAOQ6MMNc8fc99/wVWgtnDFz6F6E8UzHc7LIGqBk0\nVaVxWBTPK4BkYcqMjmEgVFnZJXfv8rWDPDf1HPRx1JLFNozwRpEUCjBUJpYb\nXuqe9LmgO+C9dYqh46RXbkNKtjFpBdymwKGRzwR2V2/tMJzRAvPCCMVd/ljD\nQDyK1LPef6KjMEh00IfZHxOs+5ymy6ua5/brwFcGJ/78+A/srcPXWgze5H12\n0/c/Xbw2nXDSh0BO2Zciy+uTz3+xwgTp6R7PO7qQopsApyq/6qXH8/GjN63R\nAUJYHodLtZBRqMNk//du5e4P1ovazCidkMjr0utN6Yga38vPHvRF/z/omZlt\nhDZ5c+Xa8mMCYFzIqpCDbWNKOHLKQbAz6zW3n/7AoK4vgX0Rn/R8WDiOv78C\n3+LpEgdWET2V6/4/RQeNjmUnXiSe8zD36o6d5abpS8k/wXVGo+abHpg30+1t\n2sYx2yI6DSKECqa5HfAunGwDDoTc+RhBj0K7Q/SeFYS3ByprFHs82lGo+Pe8\ndHwrCqraFQ9UtwN7+UulgeG8OhAbXbgjgY8bdwVDw+iNahFx7J4GqyoTH1zY\nXQsm\r\n=bKy+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4": {"name": "react-smooth", "version": "1.0.4", "dependencies": {"raf": "^3.4.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.5.0"}, "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^4.20.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "@babel/cli": "^7.1.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "@babel/core": "^7.0.0", "chai-enzyme": "^0.8.0", "json-loader": "^0.5.7", "karma-mocha": "^1.3.0", "webpack-cli": "^3.1.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "karma-webpack": "^4.0.0-rc.2", "@babel/runtime": "^7.0.0", "karma-coverage": "^1.1.0", "@babel/polyfill": "^7.0.0", "karma-coveralls": "^2.1.0", "@babel/preset-env": "^7.0.0", "webpack-dev-server": "^3.1.9", "@babel/preset-react": "^7.0.0", "babel-plugin-lodash": "^3.3.0", "eslint-plugin-react": "^7.4.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "webpack-bundle-analyzer": "^2.11.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "c8dbf15c0c399c396f23a8adb72fcdcf21939f37", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.4.tgz", "fileCount": 33, "integrity": "sha512-dYdjHN2B0aPDcj366xtNt/Ug/azHWy9kmw+DwomQUSVvbH0RWJB5hPU2Azv7ZO8L7W5ZoH5X8nMG6l3bg5BsXQ==", "signatures": [{"sig": "MEUCIQDeWJfrw31ZKCp1AlytV9LrAVFy5u/b+yq3zC3AVBei7AIgZ05GedtKlAqNsVeYjJfmwjBfGhfCngrBiUD/fCQJaYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 450099, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpoj4CRA9TVsSAnZWagAADhAP/3kej3Zl7PdBGlW4Ah3c\nmzhteFoL3HzaurNd7YI22QFvsvGqTgW9owph3qXLS9mXglq01yTmL259kT47\ndW3QBXfCOr38iR5SypsRrYPL2zb121bP2/W6AleTTQwVd7z0AuBxcQKLfcNG\nuptSNaOcqyeymPOL/AFrbiTauk27vOjg66arS+O6ckmFk7NnvvNhtLCJuC6Q\n0ki4m5jRe550SWaLwlxGqvHiOs0bBI/RlMlPf92hVfki3rhW1ldDNTGfqrmP\nDlQY3dk9e/5+PRPOpzY0f/7GppXO8ZA7OgsjFWLHr3j1rbLVqaR94jXDzKfP\nHnrMPgCKJjq3XypDxCYe9J6MsDiz4nnAbw7fe8w2jzJ2NzAKGJLivRdZ5WPR\nwBgS/gopV2WZN8tBxvGBXRFa0tFTy2Ujl58FcQYv37Wf4at0JfY1mmSXUP++\nliBR38wFqgQcfPf5HkC2MXM298tIsjwjJLDlak2laPDEYCmxy7W75J5+Xi4V\nN2w2gvKX1quOYgPuPhB1C1xrmB/Q1ci8YKnkLVLJXuubEz3oXonYjI6dw9yn\nB77bbcddbKYP0PRnjijIhTOkx/1DPoWuorsqVzR7Cbfy7uPXC2Dr3WnF/LDG\nck5sYX+3u9ohLFSCSexe7miGfWY2FuoJdqnhZtDgz8Bl2CVk6VhQoa7hnaSS\npd1i\r\n=uhOu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.5": {"name": "react-smooth", "version": "1.0.5", "dependencies": {"raf": "^3.4.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.5.0"}, "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^4.20.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "@babel/cli": "^7.1.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "@babel/core": "^7.0.0", "chai-enzyme": "^0.8.0", "json-loader": "^0.5.7", "karma-mocha": "^1.3.0", "webpack-cli": "^3.1.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "karma-webpack": "^4.0.0-rc.2", "@babel/runtime": "^7.0.0", "karma-coverage": "^1.1.0", "@babel/polyfill": "^7.0.0", "karma-coveralls": "^2.1.0", "@babel/preset-env": "^7.0.0", "webpack-dev-server": "^3.1.9", "@babel/preset-react": "^7.0.0", "babel-plugin-lodash": "^3.3.0", "eslint-plugin-react": "^7.4.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "webpack-bundle-analyzer": "^2.11.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "94ae161d7951cdd893ccb7099d031d342cb762ad", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.5.tgz", "fileCount": 33, "integrity": "sha512-eW057HT0lFgCKh8ilr0y2JaH2YbNcuEdFpxyg7Gf/qDKk9hqGMyXryZJ8iMGJEuKH0+wxS0ccSsBBB3W8yCn8w==", "signatures": [{"sig": "MEUCIFUncSOI5ap3b+N+ilwWnLjQlLRAuV6faSQPL/wbV627AiEAmj3e5xu/4A1O7pMWSoiu1lXcz/aTGyMUfUtj6KOET7k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 455723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpo7jCRA9TVsSAnZWagAABo0P/2HgwJlmp49UBr2iW7oU\nb+gO5rlcagU/qIihjKk6g4fmCaJQOR/x5gPrDXysWO1J6Tx7xKDcrEyUPEr7\nkCv1ajXiXGE7WyceQzyhkTdCAiqSY14TBeie5Y8A1XXpA2JL8EMbQxrwfTcL\nIiN1N1RH4LGtIRCt9YPchTiBOI3g9GXU9Ys952lH+HmWG9w17s16btHzNHTM\n/R25ZUgUYvMpmAlkzU5FxCq+bvIvCPR3Syl39hOp6me6rzH5X9LIOFL5dbh7\nVoaiX6lYVCJq0t8aHlWEJLwxGQUjoxuldG18GWzihEI07kLfUtroH5g3ESvC\nIkmF816PQrso18hrf+exqOEYWBQgx91qZCtgo1Q3wcGs+RnTiNuwISjWw9OL\nv5WDa0AMSZhen3eA/ohXFaQazIJIsjtAs87WUx4WpQ4SDJXVpiWs23k+TzLH\nkt8FkaSiIlXDEy7PqCaFMA5NT+ZdRAui3RKyYHgwK9PALfWESIzhJPo8HnVU\ns6YUlGuD437boeDmYHvtZB/Z/znH0Hdcry/u/KyiFXFmuVmL240b08kIoTgk\nIgxXBA3K6xTNrBhfaMV75nXj7GpvHi/n0OlcoL+0+a73LX9uye3EEjovxQHY\n3AUa1rryyqJbgDMk3wD4YQOKrp3aNjHwngCSi+TQA7H+0TcN9ZSYIzFwLzAb\nA2YQ\r\n=Ci9U\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.6": {"name": "react-smooth", "version": "1.0.6", "dependencies": {"raf": "^3.4.0", "lodash": "~4.17.4", "prop-types": "^15.6.0", "react-transition-group": "^2.5.0"}, "devDependencies": {"chai": "^4.1.2", "karma": "^3.0.0", "mocha": "^4.0.1", "react": "^16.0.0", "enzyme": "^3.1.0", "eslint": "^4.8.0", "webpack": "^4.20.0", "cross-env": "^5.0.5", "react-dom": "^16.0.0", "@babel/cli": "^7.1.0", "chai-spies": "^0.7.1", "karma-chai": "^0.1.0", "pre-commit": "^1.1.3", "@babel/core": "^7.0.0", "chai-enzyme": "^0.8.0", "json-loader": "^0.5.7", "karma-mocha": "^1.3.0", "webpack-cli": "^3.1.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "karma-webpack": "^4.0.0-rc.2", "@babel/runtime": "^7.0.0", "karma-coverage": "^1.1.0", "@babel/polyfill": "^7.0.0", "karma-coveralls": "^2.1.0", "@babel/preset-env": "^7.0.0", "webpack-dev-server": "^3.1.9", "@babel/preset-react": "^7.0.0", "babel-plugin-lodash": "^3.3.0", "eslint-plugin-react": "^7.4.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "karma-chrome-launcher": "^2.2.0", "eslint-plugin-jsx-a11y": "^6.0.2", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.0.2", "react-addons-test-utils": "^15.6.2", "webpack-bundle-analyzer": "^2.11.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-proposal-function-bind": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0", "react-dom": "^15.0.0 || ^16.0.0"}, "dist": {"shasum": "18b964f123f7bca099e078324338cd8739346d0a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-1.0.6.tgz", "fileCount": 33, "integrity": "sha512-B2vL4trGpNSMSOzFiAul9kFAsxTukL9Wyy9EXtkQy3GJr6sZqW9e1nShdVOJ3hRYamPZ94O17r3Q0bjSw3UYtg==", "signatures": [{"sig": "MEYCIQDxYcXjseEd4xNI3Cl6UFKlcBFtRcsakK7PeCWSN7arfgIhAKKe48L8aGrscdX+XLnDnC/dCqlNxlVihdl12tToyvDO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 457939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf4W63CRA9TVsSAnZWagAA9LsP/29sU06X01wPsfa40+K3\nPWV+gxm7Ipyd4vwAacy3sYfiXRWc8BJC0V9WMHUQ1Fh8W9Bk9drgzEYE0ebk\nWDQULLEnwtn4wQRHpQjLIGEodTKZrfrD0xLfV2EqMZTXxeMnkv22+Bhxn/Z2\nRyMDl9Ov/n1tWBVoYLgn9XvYFGr57CfCOEQOIVtenlMq5qroDl/kEf9mrnv5\nPe0PDxjrtEQJtcX6hUZRPWsDQ9KBq+KFkxHbrdyjfxK0XZpdAh6l7PVpKC6z\njtDQL9VoFiSyzKmXKVvV11sqRhHzTCyc4JY/bNPXXa7Ti4Potdv/ILNRTqMX\nr/lBXObchR9Cu4cfoxeN7WVkp2KAoupfvIo76FCmpfdcbN/1MSc3fpSUoPAT\n16BPIDoh5z+CYeFwntIQ2DOnu85Hc3k3H58zYBa5Vp1FxGiTwTqOVUVzUqZ6\njPqJa/+YeHHJXWPht6I6SqMZhcxrs098F80yI3fRvJuvqFpAkiRhjRXn7vk5\nv+6q2X9t4nW8/ADCh8JFa1Ufz3e1ZcSclMcsg3WuUBhBCunUVWvqquyg5IPY\ntE20BpJm697OlKJN+uDnGxqLXJrQ311i52EswnJt9YWNFKb13pKC6ok66P0I\nW8X8Tndr/dW7O1/XHvK/+9pbShtSlDHruLLh9yhb6ktGwyBwVtblTNFy6OyE\nfRwj\r\n=KZvH\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "react-smooth", "version": "2.0.0", "dependencies": {"raf": "^3.4.0", "fast-equals": "^2.0.0", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^26.6.3", "react": "^17.0.0", "eslint": "^4.8.0", "core-js": "^3.9.1", "webpack": "^5.0.0", "cross-env": "^7.0.0", "react-dom": "^17.0.0", "@babel/cli": "^7.13.0", "pre-commit": "^1.1.3", "@babel/core": "^7.13.0", "json-loader": "^0.5.7", "webpack-cli": "^4.5.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "@babel/runtime": "^7.13.8", "@babel/preset-env": "^7.13.9", "webpack-dev-server": "^3.11.0", "@babel/preset-react": "^7.12.13", "eslint-plugin-react": "^7.4.0", "@testing-library/dom": "^7.30.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "@testing-library/react": "^11.2.5", "eslint-plugin-jsx-a11y": "^6.0.2", "webpack-bundle-analyzer": "^4.4.0", "@testing-library/jest-dom": "^5.11.9", "@babel/plugin-transform-runtime": "^7.13.8", "@babel/plugin-proposal-decorators": "^7.13.5", "@babel/plugin-proposal-function-bind": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-proposal-export-default-from": "^7.12.13", "@babel/plugin-proposal-export-namespace-from": "^7.12.13"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "561647b33e498b2e25f449b3c6689b2e9111bf91", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.0.tgz", "fileCount": 33, "integrity": "sha512-wK4dBBR6P21otowgMT9toZk+GngMplGS1O5gk+2WSiHEXIrQgDvhR5IIlT74Vtu//qpTcipkgo21dD7a7AUNxw==", "signatures": [{"sig": "MEQCIH+DWS1k1dUKYBpvUrriC39MyTjpGgFRqhI1VA5y/eqGAiAG9k78qqsN7OReEiCpTlnRPUPuaF+4NW9ujsBgttRf2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV1brCRA9TVsSAnZWagAAHNAP/0lNWMDDd8To1tmVHgjL\nBWCeqosNDwQsJf681yGDId6AHLsx3lf2xSv5SOAU7LmyICp9ueKQUw/kR50Z\nV01NfSoYLByw9l1UuRkMfdk4Mj/zk07yP/d4Ze8bgNBWtrmU6cQajqNA3gGE\nv9gpnJrtP5LtG2hrhMMbMMVgCa5K5m5FcfFJ8JGQR6OIUlVX95NrfsbyyIq9\n3GlZct2f4BvMCHj8qLJrfUDhAOByYSfKnsaBfqgTFfqoFrqNpTxCKl2C8YqM\n7OYLJilY7ucH7zSpqy7TkI+4oZkdG+OXbSJnJ6KgtsuB7o1ahitJKDoCe6P4\nPgtu5iC4EGV6G6/rzqzP38SnthZ7uDgkSyhpAa85BLf25F9jfqIXpHiAoOM1\nE1U7WSwJ9U1ku/+RRG/0TqY6BZ7x5P7bWx5cuT9pw9bdtJUUoQigEHitdhW2\nNq3TpNt68aUEdhEQY3kvzKC9lSwgc4v/zU6TfKNG1Q1/wHRlVmq4UJsiNF3S\nuDayEOqFHsFm8il5PzO69ooikiS1G8aMiYzYZC28x6j+PhmEIr7Hj1xIZN7Y\n1EdowG0jDnDIu/DNRiPNyDT22nfB9uBHbSSrCvmmNw4yuH53V/O8oade0MQn\nB0PNb3+FDKZrKT5ozmNdpQ+hta5ZWq20mEjwDPm22BjKK9Fk+JJplGoc8Xu/\nFb+s\r\n=uhOQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "react-smooth", "version": "2.0.1", "dependencies": {"fast-equals": "^2.0.0", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^26.6.3", "react": "^18.1.0", "eslint": "^4.8.0", "core-js": "^3.9.1", "webpack": "^5.0.0", "cross-env": "^7.0.0", "react-dom": "^18.1.0", "@babel/cli": "^7.13.0", "pre-commit": "^1.1.3", "@babel/core": "^7.13.0", "json-loader": "^0.5.7", "webpack-cli": "^4.5.0", "babel-eslint": "^10.0.0", "babel-loader": "^8.0.0", "@babel/runtime": "^7.13.8", "@babel/preset-env": "^7.13.9", "webpack-dev-server": "^3.11.0", "@babel/preset-react": "^7.12.13", "eslint-plugin-react": "^7.4.0", "@testing-library/dom": "^7.30.0", "eslint-config-airbnb": "^16.0.0", "eslint-plugin-import": "^2.7.0", "@testing-library/react": "^13.1.1", "eslint-plugin-jsx-a11y": "^6.0.2", "webpack-bundle-analyzer": "^4.4.0", "@testing-library/jest-dom": "^5.11.9", "@babel/plugin-transform-runtime": "^7.13.8", "@babel/plugin-proposal-decorators": "^7.13.5", "@babel/plugin-proposal-function-bind": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-proposal-export-default-from": "^7.12.13", "@babel/plugin-proposal-export-namespace-from": "^7.12.13"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "74c7309916d6ccca182c4b30c8992f179e6c5a05", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.1.tgz", "fileCount": 33, "integrity": "sha512-Own9TA0GPPf3as4vSwFhDouVfXP15ie/wIHklhyKBH5AN6NFtdk0UpHBnonV11BtqDkAWlt40MOUc+5srmW7NA==", "signatures": [{"sig": "MEUCIAwjXvE8LnAWQfBnbUQoxqifaQ8CPcey9s2RBS4TRFC/AiEAk2ofeoHfZ9S6/1ul7QrkeTZ5I8nAjSUd03bOlVGTstM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 256512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuZtPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqn2A//aztr10rLU+ekkITwmkFBd/O+i1gm32dgLBgNY4WDI1EW+prP\r\nVbIv0d3lwpWldjRGePgDCPboSiWWXuwwWNSuIzRhFurVpbfFAXV/NATLghdr\r\nkHi79JKD/BbLcSUb9C7oB/9uOVkRjLwj/DM2DcnePk0tFz84T77IgGCy+1ip\r\nTUPzksUG1uPcGfRqq1MG862QiamuqpRRykKGiUv1KPUueCey9CpHBPgZhFFj\r\nPh9iiuZUBx4rwupMuac9n4pAxDch3DHLDccqAs+bpvna281soVMylbrCX+K4\r\nzamaqc0SLZhw5OIwYR574htvdcMCfYpznedeMFuk6ZU0b+EthwsLwXUwDNOI\r\n9+CSclGxGOHmzN4H3tmXlSg7PqpkT/zQIArutitCzOmlMhiO/pXVP8kGj4k+\r\ntQAh8G0UXzlNWHeELY1qFIZ/UMvptUNmpMF/Huppsn/WJucVMozX+z4srucp\r\nVwwNDWeHQfr1gvaPyNqLDvaq/96lbsyqToHZuULXbjO4LnwsOpD7OTRvnJss\r\nAqcVD7W1j8QTMU/gRj/MsYZjhGk9/uCVbFlIttvK69lXacNzZtnsR+DWA03U\r\nj25RI/JJ6C4Qd+17+j93PCo/9DFa79v2IuJvzcsSAU3GYtcTMUBeR5e2g6EI\r\nagH1PAjgEFVj06sPWhPQsXL1mz6FSRHrwkE=\r\n=qDwz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2": {"name": "react-smooth", "version": "2.0.2", "dependencies": {"fast-equals": "^4.0.3", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "core-js": "^3.28.0", "webpack": "^5.75.0", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "pre-commit": "^1.2.2", "@babel/core": "^7.21.0", "json-loader": "^0.5.7", "webpack-cli": "^5.0.1", "babel-loader": "^9.1.2", "@babel/runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "webpack-dev-server": "^4.11.1", "@babel/preset-react": "^7.18.6", "eslint-plugin-react": "^7.32.2", "@babel/eslint-parser": "^7.19.1", "@testing-library/dom": "^9.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.27.5", "@testing-library/react": "^14.0.0", "eslint-plugin-jsx-a11y": "^6.7.1", "jest-environment-jsdom": "^29.4.3", "webpack-bundle-analyzer": "^4.8.0", "@testing-library/jest-dom": "^5.16.5", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-function-bind": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "0ef24213628cb13bf4305194a050e1db4302a3a1", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.2.tgz", "fileCount": 33, "integrity": "sha512-pgqSp1q8rAGtF1bXQE0m3CHGLNfZZh5oA5o1tsPLXRHnKtkujMIJ8Ws5nO1mTySZf1c4vgwlEk+pHi3Ln6eYLw==", "signatures": [{"sig": "MEQCID9xZV0MZiGcNFd4Fv8IEjypbI9mCb5lAcrjEizD7aRfAiByOPf+GY5g6Q9lzDhst8BSLRPAgN0pX7mC8UMUl4ypoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+AbDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppeQ/8DeEa/LrwwRhVBJ+uGPt3UzpbqV4qou4Ud/xFqbs9/GygdN/1\r\n0rdvR455aL60CUHFIQvcschijl1ZNufN9n/nzKU5nEBPDOPUvwJYFWgtKedK\r\nbrJUZk/mrgDJKWSH+OACrY/OMfWVizACMuWGf2k5tN36PzAEJApLWxr9l/9R\r\ngkTnOv2EOqfI7dqosknz00/lKeliB4xXQsJFV3yrCpkTk38hIJolTBtmcF56\r\nMlq5bKIYGXbot0V5PaFvFxsTOmYHhN7W6McQ6UltoIdfCzC5iEDHlymsIJnG\r\nMQG3ojtlhIPH83nv0XAxpzuRKuATUNkaMZRXnA1ibMiwS+G0txXAOQOVRsCI\r\n2+CgxHWCguv8+N+JfN9iOgUk3N37/cNM8iKnF+zl3XDFMRPB5exuI10tvhTe\r\nwMbuOBizSWtvfkxwQy9ZHItGQxWb9SY+Rf3aLAV1cocy5+CjonSqboTSge5t\r\n8iBHeprlOFeFu0CayVT+DMida8NZ1jQRGLU+VoxFmAA46AuXY0iPKcEsPfSc\r\nLMVIZ3xfsGsXHb2cMUBd3tms5b8kp+Y7GlDG2DnpnQuOPtv6leV1fOGUzTvV\r\nzz0hm284+fYUg6D7s1biqBFv++2+rS2DUhYVK5rGD95ewYAM8aXq0/q5XOc1\r\n5f6PBxBN1/jT9/J+lDDxvdDv+rzflwOvlzQ=\r\n=v+LV\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.3": {"name": "react-smooth", "version": "2.0.3", "dependencies": {"fast-equals": "^5.0.0", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "core-js": "^3.28.0", "webpack": "^5.75.0", "prettier": "^2.8.4", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "pre-commit": "^1.2.2", "@babel/core": "^7.21.0", "json-loader": "^0.5.7", "webpack-cli": "^5.0.1", "babel-loader": "^9.1.2", "@babel/runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "webpack-dev-server": "^4.11.1", "@babel/preset-react": "^7.18.6", "eslint-plugin-react": "^7.32.2", "@babel/eslint-parser": "^7.19.1", "@testing-library/dom": "^9.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.27.5", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.1.2", "jest-environment-jsdom": "^29.4.3", "webpack-bundle-analyzer": "^4.8.0", "@testing-library/jest-dom": "^5.16.5", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-function-bind": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "2845fa8f22914f2e4445856d5688fb8a7d72f3ae", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.3.tgz", "fileCount": 33, "integrity": "sha512-yl4y3XiMorss7ayF5QnBiSprig0+qFHui8uh7Hgg46QX5O+aRMRKlfGGNGLHno35JkQSvSYY8eCWkBfHfrSHfg==", "signatures": [{"sig": "MEUCIQDFAMEBFSyaEosNgCMPJrOFbrMVand+A3YJioHTKNy1pAIgN7oYr+9Th7BfpYHfGsPdvHk05TX5ZseA2ObEZFDgtHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 278765}}, "2.0.4": {"name": "react-smooth", "version": "2.0.4", "dependencies": {"fast-equals": "^5.0.0", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "core-js": "^3.28.0", "webpack": "^5.75.0", "prettier": "^2.8.4", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "pre-commit": "^1.2.2", "@babel/core": "^7.21.0", "json-loader": "^0.5.7", "webpack-cli": "^5.0.1", "babel-loader": "^9.1.2", "@babel/runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "webpack-dev-server": "^4.11.1", "@babel/preset-react": "^7.18.6", "eslint-plugin-react": "^7.32.2", "@babel/eslint-parser": "^7.19.1", "@testing-library/dom": "^9.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.27.5", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.1.2", "jest-environment-jsdom": "^29.4.3", "webpack-bundle-analyzer": "^4.8.0", "@testing-library/jest-dom": "^5.16.5", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-function-bind": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "95187126265970a1490e2aea5690365203ee555f", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.4.tgz", "fileCount": 33, "integrity": "sha512-OkFsrrMBTvQUwEJthE1KXSOj79z57yvEWeFefeXPib+RmQEI9B1Ub1PgzlzzUyBOvl/TjXt5nF2hmD4NsgAh8A==", "signatures": [{"sig": "MEQCIGeJ5wvBk2gcRE/2d6ATSv82ZIXFE2ZS9JMQhcTWz2exAiAmZTOSP2/3Ztpg+V/eU4Dz3l0PuGonvpL9iCj1gyU7IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279256}}, "2.0.5": {"name": "react-smooth", "version": "2.0.5", "dependencies": {"fast-equals": "^5.0.0", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^29.4.3", "react": "^18.2.0", "eslint": "^8.34.0", "core-js": "^3.28.0", "webpack": "^5.75.0", "prettier": "^2.8.4", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "pre-commit": "^1.2.2", "@babel/core": "^7.21.0", "json-loader": "^0.5.7", "webpack-cli": "^5.0.1", "babel-loader": "^9.1.2", "@babel/runtime": "^7.21.0", "@babel/preset-env": "^7.20.2", "webpack-dev-server": "^4.11.1", "@babel/preset-react": "^7.18.6", "eslint-plugin-react": "^7.32.2", "@babel/eslint-parser": "^7.19.1", "@testing-library/dom": "^9.0.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.27.5", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.1.2", "jest-environment-jsdom": "^29.4.3", "webpack-bundle-analyzer": "^4.8.0", "@testing-library/jest-dom": "^5.16.5", "@babel/plugin-transform-runtime": "^7.21.0", "@babel/plugin-proposal-decorators": "^7.21.0", "@babel/plugin-proposal-function-bind": "^7.18.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "d153b7dffc7143d0c99e82db1532f8cf93f20ecd", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-2.0.5.tgz", "fileCount": 33, "integrity": "sha512-BMP2Ad42tD60h0JW6BFaib+RJuV5dsXJK9Baxiv/HlNFjvRLqA9xrNKxVWnUIZPQfzUwGXIlU/dSYLU+54YGQA==", "signatures": [{"sig": "MEYCIQD+HnQ9QhYMY17lJ8ryt+wqU8YyolAAQ1fMSB/THRk9kgIhAM/W/nfCfGROGtoc/KF2pePGa0mKymR/DQYWrfMHy7X/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 279871}}, "3.0.0": {"name": "react-smooth", "version": "3.0.0", "dependencies": {"fast-equals": "^5.0.1", "react-transition-group": "2.9.0"}, "devDependencies": {"jest": "^29.7.0", "react": "^18.2.0", "eslint": "^8.51.0", "core-js": "^3.33.0", "webpack": "^5.89.0", "prettier": "^2.8.8", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.23.0", "pre-commit": "^1.2.2", "prop-types": "^15.8.1", "@babel/core": "^7.23.2", "json-loader": "^0.5.7", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "webpack-dev-server": "^4.15.1", "@babel/preset-react": "^7.22.15", "eslint-plugin-react": "^7.33.2", "@babel/eslint-parser": "^7.22.15", "@testing-library/dom": "^9.3.3", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "jest-environment-jsdom": "^29.7.0", "webpack-bundle-analyzer": "^4.9.1", "@testing-library/jest-dom": "^5.17.0", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom": "^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0", "prop-types": "^15.6.0"}, "dist": {"shasum": "7f60df26768b72232ad92b63d1ed90d56b869f50", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-3.0.0.tgz", "fileCount": 33, "integrity": "sha512-BDQAePwq8xihRKS/SgPVxKIHASkLaQg4sfa99FQVCFqqjz2HZ51E4MbOgQKtu+sce9s4VQIMatwSReRUDiZVbQ==", "signatures": [{"sig": "MEQCIECHAwpqHodIMGAJnO8K9IxMCQYGDWPwaqthvlO9tBDsAiACSJiF7//U66p/hL9PYrioRRcDO/DC947imjoi7Xn/xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 266491}}, "4.0.0": {"name": "react-smooth", "version": "4.0.0", "dependencies": {"prop-types": "^15.8.1", "fast-equals": "^5.0.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"jest": "^29.7.0", "react": "^18.2.0", "eslint": "^8.51.0", "core-js": "^3.33.0", "webpack": "^5.89.0", "prettier": "^2.8.8", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.23.0", "pre-commit": "^1.2.2", "@babel/core": "^7.23.2", "json-loader": "^0.5.7", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "webpack-dev-server": "^4.15.1", "@babel/preset-react": "^7.22.15", "eslint-plugin-react": "^7.33.2", "@babel/eslint-parser": "^7.22.15", "@testing-library/dom": "^9.3.3", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "jest-environment-jsdom": "^29.7.0", "webpack-bundle-analyzer": "^4.9.1", "@testing-library/jest-dom": "^5.17.0", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "69e560ab69b69a066187d70cb92c1a664f7f046a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.0.tgz", "fileCount": 33, "integrity": "sha512-2NMXOBY1uVUQx1jBeENGA497HK20y6CPGYL1ZnJLeoQ8rrc3UfmOM82sRxtzpcoCkUMy4CS0RGylfuVhuFjBgg==", "signatures": [{"sig": "MEUCIQD4vIM+OZnNUaMlNARdWcHq/8FoMGI3yqHgnyz/JyjQrAIgO310wviLF4BaMPP7RHBNbuljTJUtP63It8s0nh8OvAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 308614}}, "4.0.1": {"name": "react-smooth", "version": "4.0.1", "dependencies": {"prop-types": "^15.8.1", "fast-equals": "^5.0.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"jest": "^29.7.0", "react": "^18.2.0", "eslint": "^8.51.0", "core-js": "^3.33.0", "webpack": "^5.89.0", "prettier": "^2.8.8", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.23.0", "pre-commit": "^1.2.2", "@babel/core": "^7.23.2", "json-loader": "^0.5.7", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "webpack-dev-server": "^4.15.1", "@babel/preset-react": "^7.22.15", "eslint-plugin-react": "^7.33.2", "@babel/eslint-parser": "^7.22.15", "@testing-library/dom": "^9.3.3", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "jest-environment-jsdom": "^29.7.0", "webpack-bundle-analyzer": "^4.9.1", "@testing-library/jest-dom": "^5.17.0", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "6200d8699bfe051ae40ba187988323b1449eab1a", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.1.tgz", "fileCount": 33, "integrity": "sha512-OE4hm7XqR0jNOq3Qmk9mFLyd6p2+j6bvbPJ7qlB7+oo0eNcL2l7WQzG6MBnT3EXY6xzkLMUBec3AfewJdA0J8w==", "signatures": [{"sig": "MEUCICvVwuQrHZiTehbpXIysMID4oBANuG1AnciI4NTpnXteAiEA7Eer+w9IlKoooo6fc6/Yf/RV028QpJbK8SQoTk9hgDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 308635}}, "4.0.2": {"name": "react-smooth", "version": "4.0.2", "dependencies": {"prop-types": "^15.8.1", "fast-equals": "^5.0.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"react": "^18.2.0", "eslint": "^8.51.0", "vitest": "^2.1.0-beta.6", "core-js": "^3.33.0", "webpack": "^5.89.0", "prettier": "^2.8.8", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.23.0", "pre-commit": "^1.2.2", "typescript": "^5.5.4", "@babel/core": "^7.23.2", "json-loader": "^0.5.7", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "webpack-dev-server": "^4.15.1", "@babel/preset-react": "^7.22.15", "@vitest/coverage-v8": "^2.1.0-beta.6", "eslint-plugin-react": "^7.33.2", "@babel/eslint-parser": "^7.22.15", "@vitejs/plugin-react": "^4.3.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "@testing-library/react": "^16.0.1", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "webpack-bundle-analyzer": "^4.9.1", "@babel/preset-typescript": "^7.24.7", "@testing-library/jest-dom": "^6.5.0", "@typescript-eslint/parser": "^8.0.1", "@babel/plugin-transform-runtime": "^7.23.2", "@typescript-eslint/eslint-plugin": "^8.0.1", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "eb88ad773fea958e8d76ad7a0ee0a91a6818d2e0", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.2.tgz", "fileCount": 25, "integrity": "sha512-dHYASvQV2mTcyd8+gkDXSOXNdUgNGghnYJcvoQhPRJlmvi8vOrCeS6BVvg1JhYjVFcy3P/CLzKeDS3EyDjROqg==", "signatures": [{"sig": "MEYCIQC/pC2igl+4OnO/TebhD3xLlvBQMJFAcpfxVuqQ+Hr5wAIhANozj05hHimTPz0SS4WUsvm6wfjBGLSgdSHFXLXF8s7+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 283039}, "deprecated": "Broken version, do not install"}, "4.0.3": {"name": "react-smooth", "version": "4.0.3", "dependencies": {"prop-types": "^15.8.1", "fast-equals": "^5.0.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"jest": "^29.7.0", "react": "^18.2.0", "eslint": "^8.51.0", "core-js": "^3.33.0", "webpack": "^5.89.0", "prettier": "^2.8.8", "cross-env": "^7.0.3", "react-dom": "^18.2.0", "@babel/cli": "^7.23.0", "pre-commit": "^1.2.2", "@babel/core": "^7.23.2", "json-loader": "^0.5.7", "webpack-cli": "^5.1.4", "babel-loader": "^9.1.3", "@babel/runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "webpack-dev-server": "^4.15.1", "@babel/preset-react": "^7.22.15", "eslint-plugin-react": "^7.33.2", "@babel/eslint-parser": "^7.22.15", "@testing-library/dom": "^9.3.3", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.28.1", "@testing-library/react": "^14.0.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "jest-environment-jsdom": "^29.7.0", "webpack-bundle-analyzer": "^4.9.1", "@testing-library/jest-dom": "^5.17.0", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "dist": {"shasum": "add5c7f607445766bcd871b10b2170318a444454", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.3.tgz", "fileCount": 33, "integrity": "sha512-PyxIrra8WZWrMRFcCiJsZ+JqFaxEINAt+v/w++wQKQlmO99Eh3+JTLeKApdTsLX2roBdWYXqPsaS8sO4UmdzIg==", "signatures": [{"sig": "MEQCIF1PAevsMOXAb7YQdh6ENZWB4N5qfdKuHH56eLhBmxQwAiAihkh63s+Bg6kjS4fgD38yLRIY4ECl5y4y2mHiJhcXhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 308659}}, "4.0.4": {"name": "react-smooth", "version": "4.0.4", "dependencies": {"fast-equals": "^5.0.1", "prop-types": "^15.8.1", "react-transition-group": "^4.4.5"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.22.15", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.23.2", "@babel/plugin-proposal-export-default-from": "^7.22.17", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-proposal-function-bind": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.23.2", "@babel/preset-env": "^7.23.2", "@babel/preset-react": "^7.22.15", "@babel/runtime": "^7.23.2", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "babel-loader": "^9.1.3", "core-js": "^3.33.0", "cross-env": "^7.0.3", "eslint": "^8.51.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^7.2.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.33.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "json-loader": "^0.5.7", "pre-commit": "^1.2.2", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.9.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "dist": {"integrity": "sha512-gnGKTpYwqL0Iii09gHobNolvX4Kiq4PKx6eWBCYYix+8cdw+cGo3do906l1NBPKkSWx1DghC1dlWG9L2uGd61Q==", "shasum": "a5875f8bb61963ca61b819cedc569dc2453894b4", "tarball": "https://registry.npmjs.org/react-smooth/-/react-smooth-4.0.4.tgz", "fileCount": 33, "unpackedSize": 308681, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFIF3IQ7L/yWLzlhgeCAfqm4/7vH+1d+LjNZLpFhUwR8AiEAoFkLdV1VmpoJnNaIBCfgPIeIBKc/9FmhGyQCh50gWiw="}]}}}, "modified": "2024-12-16T19:07:54.120Z", "cachedAt": 1747660589691}