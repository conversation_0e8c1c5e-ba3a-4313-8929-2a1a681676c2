{"name": "chalk", "dist-tags": {"next": "3.0.0-beta.2", "latest": "5.4.1"}, "versions": {"0.1.0": {"name": "chalk", "version": "0.1.0", "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}, "dist": {"shasum": "69afbee2ffab5e0db239450767a6125cbea50fa2", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.1.0.tgz", "integrity": "sha512-E1+My+HBCBHA6fBUZlbPnrOMrGKnc3QAXGEvCk/lpEG/ZKowZFg01dXt6RCYJMvTWYgxHWTyZQ6qkCrVPKJ2YQ==", "signatures": [{"sig": "MEQCICcizJ0CViMVIGAdOi/w9z8s5d3Zn23t9fMlbuGpzGENAiB4IAPyJ4DSwN/KaA6WIkJE7cqo3iZiTLD1ClLbpbCEiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.1": {"name": "chalk", "version": "0.1.1", "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.1.0"}, "devDependencies": {"mocha": "~1.12.0"}, "dist": {"shasum": "fe6d90ae2c270424720c87ed92d36490b7d36ea0", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.1.1.tgz", "integrity": "sha512-NJbznmWlxmS5Co0rrLJYO0U3QW6IzWw2EuojeOFn4e8nD1CYR5Ie60CEEmHrF8DXtfd83pdF0xYWVCXbRysrDQ==", "signatures": [{"sig": "MEYCIQDyRcBiBgR63thSUFHx+SV7hi4FtDRy5WNaeEfCEr6c6wIhAJbSSa3GwYNrT0RVe6AVJcvtXcjsOVg7yTdLVbxlzfK7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.2.0": {"name": "chalk", "version": "0.2.0", "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "dist": {"shasum": "47270e80edce0e219911af65479d17db525ff5db", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.2.0.tgz", "integrity": "sha512-CHq4xplBE+jhsJKGmh8AegFpEsC84kQNPMeL2mjrD5ojPc1LqNV1q5opCBU7BcRxWbpX+S8s+q4LFaqjP1rZmg==", "signatures": [{"sig": "MEUCIQDfcM6X1OG63W5oEByNa/SCZJWAU3mNjL4zY35I5yHbGwIgNPIUWRFZl6a3ByWBLR4cXyn2WHnYru0PY39fougSJzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.2.1": {"name": "chalk", "version": "0.2.1", "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "dist": {"shasum": "7613e1575145b21386483f7f485aa5ffa8cbd10c", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.2.1.tgz", "integrity": "sha512-nmVapomwGksziCuynboy7I+dtW4ytIdqXPlrfY/ySx8l8EqFRGHyA04q6NMNpOri8XliGUGwXyfScVl48zFHbw==", "signatures": [{"sig": "MEQCIBda89bi2LZLymi7dToSY9rMAFRCXyg6fqyF15yoQO0tAiAT1ZyRicgVCJku8vgl5CUqx27uRUEnRK1GZ2kowTHh4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.3.0": {"name": "chalk", "version": "0.3.0", "dependencies": {"has-color": "~0.1.0", "ansi-styles": "~0.2.0"}, "devDependencies": {"mocha": "~1.12.0"}, "dist": {"shasum": "1c98437737f1199ebcc1d4c48fd41b9f9c8e8f23", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.3.0.tgz", "integrity": "sha512-OcfgS16PHpCu2Q4TNMtk0aZNx8PyeNiiB+6AgGH91fhT9hJ3v6pIIJ3lxlaOEDHlTm8t3wDe6bDGamvtIokQTg==", "signatures": [{"sig": "MEYCIQD/TMCMMPNIkr1dvG6kRR/CWZZJtqKGoMdimY4IY6MEsgIhAJ1HbZkkcygfBmN1N4nqpYpBdAbRlLQOQoHfhOmBxYT1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.4.0": {"name": "chalk", "version": "0.4.0", "dependencies": {"has-color": "~0.1.0", "strip-ansi": "~0.1.0", "ansi-styles": "~1.0.0"}, "devDependencies": {"mocha": "~1.x"}, "dist": {"shasum": "5199a3ddcd0c1efe23bc08c1b027b06176e0c64f", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.4.0.tgz", "integrity": "sha512-sQfYDlfv2DGVtjdoQqxS0cEZDroyG8h6TamA6rvxwlrU5BaSLDx9xhatBYl2pxZ7gmpNaPFVwBtdGdu5rQ+tYQ==", "signatures": [{"sig": "MEQCIBe296G5Ckfk2TKZTU3bGX1WzY2zO0oXe5yCT2EecEajAiABeRfWovOVYu9t02fFT3Pnrbreb2qwOUoA6c16yVmoUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.5.0": {"name": "chalk", "version": "0.5.0", "dependencies": {"has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "ansi-styles": "^1.1.0", "supports-color": "^0.2.0", "escape-string-regexp": "^1.0.0"}, "devDependencies": {"mocha": "*", "matcha": "^0.5.0"}, "dist": {"shasum": "375dfccbc21c0a60a8b61bc5b78f3dc2a55c212f", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.5.0.tgz", "integrity": "sha512-rTCcbF0wrwC+kKzA/3SpBc6PrcOx/+PRQVtS3PEDw5tGzqycpB48dRS8ByxFDd8Ij5E1RtafZ34R1X9VLI/vUQ==", "signatures": [{"sig": "MEQCIEGqQOniqI9HsAKeDEvFr1KDB3AZGGcdwYu4q0ylIHtUAiA65kQmXrb1YvhBtaJjhFQBrt7C9MIZfOOBOrg8SOziBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.5.1": {"name": "chalk", "version": "0.5.1", "dependencies": {"has-ansi": "^0.1.0", "strip-ansi": "^0.3.0", "ansi-styles": "^1.1.0", "supports-color": "^0.2.0", "escape-string-regexp": "^1.0.0"}, "devDependencies": {"mocha": "*", "matcha": "^0.5.0"}, "dist": {"shasum": "663b3a648b68b55d04690d49167aa837858f2174", "tarball": "https://registry.npmjs.org/chalk/-/chalk-0.5.1.tgz", "integrity": "sha512-bIKA54hP8iZhyDT81TOsJiQvR1gW+ZYSXFaZUAvoD4wCHdbHY2actmpTE4x344ZlFqHbvoxKOaESULTZN2gstg==", "signatures": [{"sig": "MEUCIHm4uVj8/eT3R7g1Q6tzTAMZ2pdqmG9OumNXatGp4ykfAiEAqiq/PLZqnD8UaVvvHh3ElJAhsquDcnDKTEjLo6bZyNY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "chalk", "version": "1.0.0", "dependencies": {"has-ansi": "^1.0.3", "strip-ansi": "^2.0.1", "ansi-styles": "^2.0.1", "supports-color": "^1.3.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"mocha": "*", "matcha": "^0.6.0"}, "dist": {"shasum": "b3cf4ed0ff5397c99c75b8f679db2f52831f96dc", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.0.0.tgz", "integrity": "sha512-1TE3hpADga5iWinlcCpyhC7fTl9uQumLD8i2jJoJeVg7UbveY5jj7F6uCq8w0hQpSeLhaPn5QFe8e56toMVP1A==", "signatures": [{"sig": "MEQCIA2fHIK4HaMM5vT8+vgIZDSYl2yR1wBMDpQnyK2mEp73AiBv9R+hCSU6L2lKmjPk37XVGjJaJPMWSPFUT6HNBGXJJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.1.0": {"name": "chalk", "version": "1.1.0", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.1.0", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}, "dist": {"shasum": "09b453cec497a75520e4a60ae48214a8700e0921", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.0.tgz", "integrity": "sha512-pn7bzDYUIrL0KRp/KK5B+sej6uYtzQ5hYOdLU+L3MVWHCgoYi4aUYdh2/R2rsdURIoOK/ptZi5FDtLdjvKYQ7g==", "signatures": [{"sig": "MEYCIQDvFUAscqbO0W1o8ynaLsS3H/qRFyNIcBpeciwTE0L2eAIhAJLC4kNijQLiP53FxwjKtIk/yb2Mz5bSJkvQcbmO38Lh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.1.1": {"name": "chalk", "version": "1.1.1", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.1.0", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}, "dist": {"shasum": "509afb67066e7499f7eb3535c77445772ae2d019", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.1.tgz", "integrity": "sha512-W10W+QfIxJlTm3VRtg8eafwUBkDfUPFvRvPv4jCD9vF4+HzlAyXJ7P3Y5yw/r+gJ1TzFEU6oFqMgp1dIVpYr0A==", "signatures": [{"sig": "MEUCIQDJnOp0QV+THZBj97NAJIJ/7FOfH1ApR+V17cduIWef4QIgKx6kVG0zABWS4/A8EyL/AV5PuxB8aCmy9tNSXcFiXa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.1.2": {"name": "chalk", "version": "1.1.2", "dependencies": {"ansi-styles": "^2.2.1", "supports-color": "^3.1.2", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^5.2.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^5.1.0", "coveralls": "^2.11.2", "resolve-from": "^2.0.0", "require-uncached": "^1.0.2"}, "dist": {"shasum": "53e9f9e7742f7edf23065c29c0219175a7869155", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.2.tgz", "integrity": "sha512-QBKX51aavmpKcCkgrJXhjS5b3rCgH2Wn99BYqUV2H1FjTP7Mm4KTcskSxuKrfhQKt69mBn9jH4Kb2xnchvEaOw==", "signatures": [{"sig": "MEYCIQDR73JAj03sZJRAoJDQVm+RirI4Iry8ZuCvjsoI7cTA4wIhAOjIHdQPS2bXbYZucSmdnq17cnoYJ4fHFrpbk9slQEst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}, "deprecated": "chalk@1.1.2 introduces breaking changes. Please use 1.1.3 or above."}, "1.1.3": {"name": "chalk", "version": "1.1.3", "dependencies": {"has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "ansi-styles": "^2.2.1", "supports-color": "^2.0.0", "escape-string-regexp": "^1.0.2"}, "devDependencies": {"xo": "*", "nyc": "^3.0.0", "mocha": "*", "matcha": "^0.6.0", "semver": "^4.3.3", "coveralls": "^2.11.2", "resolve-from": "^1.0.0", "require-uncached": "^1.0.2"}, "dist": {"shasum": "a8115c55e4a702fe4d150abd3872822a7e09fc98", "tarball": "https://registry.npmjs.org/chalk/-/chalk-1.1.3.tgz", "integrity": "sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==", "signatures": [{"sig": "MEYCIQCYydQMZbUiHhEF1lG6Vvl8dFiZehECOS8naCRKiBaDWAIhAMB+3sTOs5gMFmQyiUE6HzXaIsahGhReBUr4OYaI+iCX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "chalk", "version": "2.0.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "nyc": "^11.0.2", "mocha": "*", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "dist": {"shasum": "c25c5b823fedff921aa5d83da3ecb5392e84e533", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.0.0.tgz", "integrity": "sha512-7jy/5E6bVCRhLlvznnsbVPjsARuVC9HDkBjUKVaOmUrhsp6P3ExUUcW09htM7/qieRH+D2lHVpNbuYh7GjVJ0g==", "signatures": [{"sig": "MEQCIHTAVf6u8jvZCfxvAN3anX5/E4q4xlYfkCEkZENhrWYbAiBLQg/CjJ1n1peDolmlJB8V892hSsvTW1L1zOl0qA1IHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.0.1": {"name": "chalk", "version": "2.0.1", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "nyc": "^11.0.2", "mocha": "*", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "dist": {"shasum": "dbec49436d2ae15f536114e76d14656cdbc0f44d", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.0.1.tgz", "integrity": "sha512-Mp+FX<PERSON>+FrwY/XYV45b2YD3E8i3HwnEAoFcM0qlZzq/RZ9RwWitt2Y/c7cqRAz70U7hfekqx6qNYthuKFO6K0g==", "signatures": [{"sig": "MEQCICfJZUefrwq1SvzsgOv2Q/7HhKkcNRqOTzI/P/PsGm7dAiAVHrhAfBt0v3Bjqycj4E95pSguOX6OdYQeIElUAHjbvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}, "deprecated": "Please upgrade to Chalk 2.1.0 - template literals in this version (2.0.1) are quite buggy."}, "2.1.0": {"name": "chalk", "version": "2.1.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.7.0", "matcha": "^0.7.0", "coveralls": "^2.11.2", "import-fresh": "^2.0.0", "resolve-from": "^3.0.0"}, "dist": {"shasum": "ac5becf14fa21b99c6c92ca7a7d7cfd5b17e743e", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.1.0.tgz", "integrity": "sha512-LUHGS/dge4ujbXMJrnihYMcL4AoOweGnw9Tp3kQuqy1Kx5c1qKjqvMJZ6nVJPMWJtKCTN72ZogH3oeSO9g9rXQ==", "signatures": [{"sig": "MEQCIAR2pBNunhcCtIpoVtR8CzUr4fkHHazxHsUmYiEOl3SyAiAj1UgZ9m1qQjHPwS0lWc7+x71FyiJ9BnT8LKzU8hZpaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.2.0": {"name": "chalk", "version": "2.2.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "477b3bf2f9b8fd5ca9e429747e37f724ee7af240", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.2.0.tgz", "integrity": "sha512-0BMM/2hG3ZaoPfR6F+h/oWpZtsh3b/s62TjSM6MGCJWEbJDN1acqCXvyhhZsDSVFklpebUoQ5O1kKC7lOzrn9g==", "signatures": [{"sig": "MEYCIQCJ26ChsXMgEr8DGfcdGhwoECmCJIQc8C/WtfNNghWXpwIhALxt4sAYIddaWoA+laUzI/bv5TtudGNeOLEa+lB3iTKA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.2.2": {"name": "chalk", "version": "2.2.2", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "4403f5cf18f35c05f51fbdf152bf588f956cf7cb", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.2.2.tgz", "integrity": "sha512-LvixLAQ4MYhbf7hgL4o5PeK32gJKvVzDRiSNIApDofQvyhl8adgG2lJVXn4+ekQoK7HL9RF8lqxwerpe0x2pCw==", "signatures": [{"sig": "MEYCIQDfa8X3hU20LXJII/tFJYOtrI+If9MXC6lVP2kVo2TeagIhAJTH6/ofItVCqYxnT7cTqmAxRF8XTpQFWlvUr/dLjfi+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.3.0": {"name": "chalk", "version": "2.3.0", "dependencies": {"ansi-styles": "^3.1.0", "supports-color": "^4.0.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.8.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "b5ea48efc9c1793dccc9b4767c93914d3f2d52ba", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.0.tgz", "integrity": "sha512-Az5zJR2CBujap2rqXGaJKaPHyJ0IrUimvYNX+ncCy8PJP4ltOGTrHUIo097ZaL2zMeKYpiCdqDvS6zdrTFok3Q==", "signatures": [{"sig": "MEYCIQCmt7XnAO8uN79Qxpb8HhB5EzqXR5F9Xz+dizDO68VggQIhAPCzW1TPRUP1vXrzImjrGiRQFFXTq6uWu8l+3dlLRyJm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "2.3.1": {"name": "chalk", "version": "2.3.1", "dependencies": {"ansi-styles": "^3.2.0", "supports-color": "^5.2.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "523fe2678aec7b04e8041909292fe8b17059b796", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.1.tgz", "fileCount": 6, "integrity": "sha512-QUU4ofkDoMIVO7hcx1iPTISs88wsO8jA92RQIm4JAwZvFGGAV2hSAA1NX7oVj2Ej2Q6NDTcRDjPTFrMCRZoJ6g==", "signatures": [{"sig": "MEUCIQDq923pbuma8n5CHp1BmZnEpfSBznm9pjEsnO89V1Fd1QIgPAoDos2IHJA/kzqVf2mD25YwAbgXJ4YOKduTvo92f94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24721}, "engines": {"node": ">=4"}}, "2.3.2": {"name": "chalk", "version": "2.3.2", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "250dc96b07491bfd601e648d66ddf5f60c7a5c65", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.3.2.tgz", "fileCount": 6, "integrity": "sha512-ZM4j2/ld/YZDc3Ma8PgN7gyAk+kHMMMyzLNryCPGhWrsfAuDVeuid5bpRFTDgMH9JBK2lA4dyyAkkZYF/WcqDQ==", "signatures": [{"sig": "MEQCIGrPWo1zy8RefMcSH+1wPT00s3HsqCjGvfnnE3kKN0BMAiA90/5NkYnkzLmda2udxxQfLxRPdxx1Gf9nafuAeaTxrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24713}, "engines": {"node": ">=4"}}, "2.4.0": {"name": "chalk", "version": "2.4.0", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "a060a297a6b57e15b61ca63ce84995daa0fe6e52", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-Wr/w0f4o9LuE7K53cD0qmbAMM+2XNLzR29vFn5hqko4sxGlUsyy363NvmyGIyk5tpe9cjTr9SJYbysEyPkRnFw==", "signatures": [{"sig": "MEQCIHWaTr0kn5hhbrdU9rauwwyPdBW6TxZnI4Lc23AJQgTCAiBJcOQo3Y7yHbfSuuL++TjazCour+dgSoT3qw/rRcCHgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1Xf3CRA9TVsSAnZWagAA1DcP/2EhxWse6mGwicTqM2U5\nQl2Xol74cFmd6b4nCGZnGycgatfJtyhb1YoH/vL3uNqGFrQGBwAr4GoZxhGd\n7kL0xKWnfhGHFeUe//fSCklj4Aff700RteXornlDFxbK5jVELyYcXfG5xJ5i\ncAIuPb9YYXltdaSfvVcg49qIPcjRfZm5Wz8WxTaUAyD5Ag4lpWKVTgWZsU+c\nEKRQHu+UmpX2OsudafT6GL3ak7GE2+ysH1b0HcYVuf1Wdf39un+E0MXDs58C\nTLCZSASN99/KCEpjh8aa4YdXVU3x0rdf50KdKDBUMF3b6HnSfWqOS+OWZRFZ\nC0jvk58j4vmXCVb2puQI8HIuZXBlNeS59GaN3hB3rz7JMgrQC/LXycOU1x+5\nuKEKupRkkVsSRyAEUdHqx6dwkcm+TVGPnXjUMdYREL9VkyY9eB7lBYTEzH9I\nZN9H3JXrjo/dGVmFL6q+L7lCxLFsl1p+UCMxubUE9XV6C/QN4mQmiwIAwn04\nhJH1RFIFTHszVEUnAJMZ6SqRRJes5iSedAMyiUYi+1S86uQenyUqtIJbHsNO\n7+G3Jnfdw9e1+YMvk53PSJcdtt5ayOx7ezc0HLS5HD9g3bXhMbbxTupHOSAv\nVCiEoaKAmjJK7nbStTqrX3xjz85K+lNHZdKkIzPWX5TkEg8KMSGK3LxfXG8B\n+CuC\r\n=orOi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.4.1": {"name": "chalk", "version": "2.4.1", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "18c49ab16a037b6eb0152cc83e3471338215b66e", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-ObN6h1v2fTJSmUXoS3nMQ92LbDK9be4TV+6G+omQlGJFdcUX5heKi1LZ1YnRMIgwTLEj3E24bT6tYni50rlCfQ==", "signatures": [{"sig": "MEUCIQCTy37ycwX7bqoO0WDu7AVubgfxDHR/7neyxoLzwx8dIwIgJxP4QEC0TbUnC9iBA3w36fy7kAAd1tHPSQZtkZ1yOrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4WCICRA9TVsSAnZWagAAhgwP/2M/ItinhR06BFhLMh91\nK/ru5t71NzSzoEvI2nh4W57Wk9cU1NOYi1cI17nUvICHCL4Vq9mjvU0hajTw\ncAYtM0Lwl+G4Hk4JtuiZITYj93QY3yLSJ8zkj95JznFbH0Zd9KkZrkoGukcG\nFY9at0cfNyhBmwi5sEDAFktcw7wThQ6Wy3iIttQ0N1M6Lf1XILg9Xyq6Id/W\nlz3TbkCt6AZCS1icmDPIiLdVQuD9SfpusIDsHm5/6FJPShwmQjUlM6Kdy7lx\n6M8uhcIknpxjfPTA6/aSBC4qgXnDhuPPi9xF657/81Mswz4Tb71KOf6UqLPi\n3zk1D5PF71ujWs3wmPll9TAVGnWuNzE+X/7GVIB4qCrib3SgvRzMhL0Wo95v\nzxTpNoD23hKYwofUyV3cTFh47YwkVoPtOStRAgdE87rx+v3VjbWSThQJc3V8\nHOsIeTjpQMwAr/d2DnasHKlps/q+gnGKqhBhcf11tAKn9C7PsAQ2l6+E4Erc\nfPKqDRC6TVG7ABdwOtyNonHhrJ2JLgYj8d4mHdtsMTtFsUTOQR/+Rx0V8HJS\n9gBLmPr3yc/yEedYW68wP5tPK2SfvFTzgMBw5v0+tgIxOjUunGxDUV4a1Bpp\npCBLN7iS77FLMiMonfcD2z/SsoB+Hb+7q5eT/gua3BIUNNZEdmgw9queXw+q\n7DFE\r\n=LSlF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "2.4.2": {"name": "chalk", "version": "2.4.2", "dependencies": {"ansi-styles": "^3.2.1", "supports-color": "^5.3.0", "escape-string-regexp": "^1.0.5"}, "devDependencies": {"xo": "*", "ava": "*", "nyc": "^11.0.2", "execa": "^0.9.0", "matcha": "^0.7.0", "flow-bin": "^0.68.0", "coveralls": "^3.0.0", "typescript": "^2.5.3", "import-fresh": "^2.0.0", "resolve-from": "^4.0.0"}, "dist": {"shasum": "cd42541677a54333cf541a49108c1432b44c9424", "tarball": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "fileCount": 7, "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "signatures": [{"sig": "MEUCIHxDV26xkYTdx1T+EBh6zvEaa602qK7hNWXvOTB1yr5UAiEAqSxYcAo+BSotxMY2GjH1e25JFKt2I+5D19gPFGbdghE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26924, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcMNEwCRA9TVsSAnZWagAAmpUQAJgCZygaBX9qniyJ7YVF\nOXq9BNycBSnHyRd5YnaoO6HB7ejh/M4CYYGPqdSQ0OXEk1teNm7iPhGhocbW\n0eEcg0gsnVTgkUKx5p3o841VKydwy72FDgO9WJjKm2QC/mwuYHB9kI7zkq3h\nkakWBNGlKxbKNYX+7x04BXx1H8Fn1CSE//133uQnUWzM6NSXrUwpiZTzwtXi\nOybESujfKq6x6DxlYsTTScThCUodQQTslxIrdeS8PZxQL1RqCwnJSMHi81nI\nPR5BNVbAEYOsZuw88mNEtc6sHellN3ZFVlZwFDu4ZDskgoMiXZVv7Qp6AXbN\nCdsz1ej/OBFdwUfjS17igoHY3sO3+7o3IuFFaCXM4lkSE2zu79M2A7H0GL0R\nUcyfM1OC/nRcLgeEytIDBSOAgeN4tstswdyagFQ36jymeKUyz+q50ziBchey\nZnxPMGYDMKTx+me3TGpf3SbjiSstyZm8GLWPhRLbkjIDajFcFnq2HZXUu/LR\npdFJIWqnJihr9dxxiPSxddqZspb/Jo2mD2+ILNxROZB5+nzmlLnV/PsnnbxM\nPRN0iYDQt6NtXce/GOFMasLwtwidfHx8B4ybmObU3btbmg7V7Og++xpVg+h1\nQfACtop8sZyVN3l65vhonCmioqpSLQPeEkMvwGN6/7wi01BRi5VI4DdEtIet\nHcNL\r\n=DerQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=4"}}, "3.0.0-beta.1": {"name": "chalk", "version": "3.0.0-beta.1", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^2.0.3", "matcha": "^0.7.0", "coveralls": "^3.0.5", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "328a0eccc051d6e50787fe112cbcf85b5d5e4d88", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0-beta.1.tgz", "fileCount": 7, "integrity": "sha512-f32K9VcIM5XJjpPHkqbrg+xN4vQVzEFNmPTgn1Ai3RBbtWT6ggFkUwZmB3/JTk0tdtjH1sl7fepaB9Vj8uVdUw==", "signatures": [{"sig": "MEYCIQCe0bs1bAdkWtb7IlPzwUC1aavccTqLOMjSpRDMQWZFjwIhAONAABWrbNk+e31MGrLVolFCW6IA03RVeQP3vX11l1fh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjZk5CRA9TVsSAnZWagAAzZ8P/iVhUy5WekDoOrbrump/\nw638F5EWWFLQNYd9SZOdZ0P7eDFJn9vfYwPitgL9jYJZ1T8fMveKbYLsJt6h\nLiNEk9to5mMgfINlj/+vOk83qEZ2yfM46jL+xTfyBLIpnKozlLkUjlm0cPXi\n6yOW26zdmyke1tp/gZafNSaJ/vJbxpjJyciccrvmfe2DWVyzwCcjP7nxpx8/\nUHdjSGvz8ul9PMwA1WieSHE/btPTqaPAPPiVtiOZWyDLhDI50+VXW7iQnvVM\nlMr1OiGjdQ2GKAxJHomvb+bjoWjWxKz3kUnk801quxABfgDHAk+Y3GIvMxLU\nVGQqiNvqYkMcqiF211xkXyFw71tiHyPqQ8pDkp4iYns4LIScr+ppoHPmJv9x\nALKBarLK5IQI1Fjw2sK5t9oOqNQ9HDZLDwGI/DTVOzUCzEMa67euv53IPRl3\nVbR8HHDDTareIWu1zpaHeOfxSoiRpx9TdV0thwWCzSuhANhUws4ebWh+NZG0\npY6Zt0lmSC+Cr3W/Oj41NgJ5MIk6uywZMjYa5wMS22qEzdhWCIDwckeuAsdz\nDi2h+9fcIHOkXxPWKn7UI91B5FCbEadThU9ASjZWSEv+BmtZ605wHRxk36Cm\noFe5WoIbblSjTpWU8ECtpyLm7o1j0aZj7nTgd0DV8ab30CzepNoECebX8AOq\nJ//W\r\n=/gvL\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.0-beta.2": {"name": "chalk", "version": "3.0.0-beta.2", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^2.0.3", "matcha": "^0.7.0", "coveralls": "^3.0.5", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "d6a422d12828314a6f2ecad6863c68b706598a22", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0-beta.2.tgz", "fileCount": 7, "integrity": "sha512-NKGY083nqioebgPmjRTGY/bRq34dgkXkmHmWgwDXbPs0YEkN7ZTcimhnv1KnHGW80QPta1QZiJDbQv7s+e+uaQ==", "signatures": [{"sig": "MEYCIQCR8QG2c2hRIQDlCKH8KjjOFNGvWAEqtmoIHPcJ0BWY/AIhANBUmWME4/25NdWUmjZrye5lgor0aOJPV3z/HEVysw6z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnFe/CRA9TVsSAnZWagAAN/YP/0i73lzDmsyO/x2VPyvz\nlL0W6F4HdeK3W/Mr7BNf3M06KVjax+BhLTq/KyAhyZlfCAI412HeZCdb34Aq\nXXucOoN5l7o6oJrs+KXN2GlxzHZT3HJjrU653bAuaw/FN0apVM/cLCLA30gq\nZZSM9cxJKy8LDZsszGT88iQtcUI/sp1yvkooKKV4A9XyjQaC05xTVBAbNan5\nYWQfgFKdr2Wo3A5KPND0hf9247GQzg8Lu8fE0EOtRhng+ZxUExp0aApaevl+\nZj+265abk5RMmOUa+VMln91NixSnE98PRguTZcVF+m4HuGyx3fR5jiJx5XU8\nlWvaA01CSHTVh2aVvDHa16/Cbcqzrl24xV7MSVRzUEAY5OudV/+JMvIFWo54\nIFnQqBYFbao9GoHQRqrFLqi5szR/yBYPayFGBcG3L3O6Tngz1N+OtnD+mGNC\nu7ZRt+zr8w0ooPIqKO97PBsgF7u495pvdKx2L5exw77kbltwTEiad2KmOTf2\nQDy1UN+KTUGzeArUa5tdqd3xl6r3V9WqEHsul065u2o4cqPAMXqvR1Xa8lv7\nxGbF1M/lKwGQihn37tHbXPkS/t1ghr+OS0kNaUgN8E1j/7//xx+j2bt3MAi2\nxmZbnCL0EufwXl85FJQ/Y0QFWFl/6LgJbvsmCD8ZqoP+Y8zljZDsKHjvtewU\nnRyz\r\n=5dvj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.0": {"name": "chalk", "version": "3.0.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.25.3", "ava": "^2.4.0", "nyc": "^14.1.1", "tsd": "^0.7.4", "execa": "^3.2.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "3f73c2bf526591f574cc492c51e2456349f844e4", "tarball": "https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz", "fileCount": 7, "integrity": "sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==", "signatures": [{"sig": "MEUCIHFevK5+lXJPZaAa9WD2j7ykLEswtElzlNvuddi6ulb+AiEAuQyuRFvzORZkufOCblOvMRtG0vc69A7rodPmDWZlp18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxmO9CRA9TVsSAnZWagAAQlUP/0PNLoVFZvoOYYt8XPh9\n6mDNBG9olRXfrrR33bcNZdkpKfil6MU7uMkLOME+L9U6h1iqMVUK/CWKfqwS\nUPH/zKprPc2xOgDVKiEgAPm+OWCxcZrGBGUJPiP2TVlo5UshIA6DheD8TSgQ\nGBW2JrOo+IXuHkza1+rPMzfrBpvulp0gthN9JjJYQNZ66SuB1wyFPMLh1b9A\nq/Q5Kg5acnnNAE5xFogzw2gAaf5qee3v3Ozda96VFG8oeCvDXYre6oC0OUzV\ng/ODyHikvvtZx6TU5hosOSRTawXjq1lGgLL2xzWt7w8oRziTBb2NKf/irsMd\nuWel64/sBbQF9C1C1/z/MkYRS2OVQWOo/qZ/BdIvfCTniaq6sekNn6rIXJj8\nPRXmBsJXdxD6mhPKxa2YEUWHFigyI+DyNRi3EkUpCqp2ZmfkD0srhakUf97z\nB4kr+0MvNpBhWt8y7Yjz1jQC9EM73yQ1POjspYj+Fh6mr7kgkfo/AFGh4AHI\nSXlXgE0b8WUn6hl3/icMZHk2xwYyIVImklNkKfI4IhxodkjL11ji+Nn5yUkI\nf9RqxQajpLAwLyWeAT2RCSTLvxfjwKnU+bWyFWHqyGQb4aS4TLSz0wmR8raS\nroGj3AXz4oUTazsBy+kGNwzOZs3tOV/Uv30MFpnHpFpOQhW1Mvp/RiQkd4O3\nxEsU\r\n=jRi9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "4.0.0": {"name": "chalk", "version": "4.0.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "6e98081ed2d17faab615eb52ac66ec1fe6209e72", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.0.0.tgz", "fileCount": 7, "integrity": "sha512-N9oWFcegS0sFr9oh1oz2d7Npos6vNoWW9HvtCg5N1KRFpUhaAhvTv5Y58g880fZaEYSNm3qDz8SU1UrGvp+n7A==", "signatures": [{"sig": "MEUCIQD7qR9gpQpWRXV3jyQzkkap03iawVdpCjW7KHMqHUCSMAIgF1VFxJCJMi2OPph8OaYzcwNwHHTXEDg3BzcAqM0J3c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehaBSCRA9TVsSAnZWagAAOr4QAJcb6PYIe6jxjYoonKMe\nsF+1AbJDL8JbXYEJME2KzFD1SXQEUIlUBM8C+hqfu0b2TxXs96rMzI+ueLx4\nvWIdw5d6By+eLQr2pfKgr4YrrPa0vniUd26/7pdbPTJbXpF/u0/BNI0TD30D\nSHBMuFbvVVUV0QfrjajTJXnA6LsD7LOcp9RlL8zyZRDYrstGJShO0yYUt6vp\n7+cTVX3oYkXFpfd2xBqRyeljFO5S4D9dhkGvq6vdVt9VUMXhmUzEJwtzpDpj\nBqfDfRBudePuthM+nNlNd9DIfCY+HopadNAKsRKv0fobzACNE2xedBUHWfvB\nAxVOBupA23Xok0z7dgXBKllLptQlI5mQ3e1cqQO9hUB22iRSUrSM4eEz6lrB\nzCbyzr+7WoxSPRWaoIu52/zPanuGAwc2Omk7+BHwEX0j7wiyHpxPWSkbZiw6\ngeiro1ZDzbWYCd2lOe2ZGRRQdIHCADozHz6IHorbHp9k4R5cHtmryiwmE59B\nZ4JYtRjn6uiWbr+RPVdSS9RySu95eApfTNO148IIek+E1lNLJ3VTobUmDWyY\nDW6HDIA1Iplbm54dSGgC5fwDOhZAnvrMOfOS6MMygblIuHP4Cm16woIUwWFi\nE4ErSEGMZZg2N0fkGP3oEgOjbnzXiSrYBPXZncfI+nnTtHf82kJ4D2Oc8nPp\nVbza\r\n=EnFl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "4.1.0": {"name": "chalk", "version": "4.1.0", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "4e14870a618d9e2edd97dd8345fd9d9dc315646a", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.0.tgz", "fileCount": 7, "integrity": "sha512-qwx12AxXe2Q5xQ43Ac//I6v5aXTipYrSESdOgzrN+9XjgEpyjpKuvSGaN4qE93f7TQTlerQQ8S+EQ0EyDoVL1A==", "signatures": [{"sig": "MEUCIQDngUfyzfUivBEHqCcMvyhegi4ut++J5IACRwl3gHTy2wIgbyjwcUbiRPFMf5J95/s1JR7DUuPwMqrOHpFuzTrnmZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33631, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe3z2uCRA9TVsSAnZWagAA0WAQAI74Vzh0XPfSSmh2CcWS\n+aaiM3I/TWlkYavizTfItEhGfuzf+Fi0RAzdW9SXjxYq9kZe1W+9zG+QJQ3S\nR5icdbgu3lXwAfesJUcX5EplLoSKEJkpTMLtOcff0lu4ug0+dYm5XHbCfign\n8e7vVdU50GPMM8mg20Hyl0SIuawkUngnlIeCiY36NjdysJtMa8X2QeMHRNat\nSg/aK3+SepkJyxAQSXqiI9QemV4PMI42L7pCb19bu79eBUYwaoKml4qKneX2\nH18ViBB6wKhxfNwX1K7M2PHxqd5e2t44hqliAgzXzz/bNIaU3qomm2OJYGKS\nZujI7mx3DEKzsYd7glloIBQKWWet22mc0eQSjH7q25w6tjPZpk68Ggs4xxIm\nRgJaAjhFURC916d4o/EhQYXTv+0WnS+TRwcJOBDpsrfZ1IuqErC2ZT+QnMya\nzEMemmRWMUFhdQcQYUQT8jDHuSpqxj9d6DUBBGJrM29W1CH3j0xt6DN2EwOx\nUj3HGHKTc652yrvP4JKSj8XAPUn7mHW1kEOT9ypk+sBISI3u5EYMdPr4xRKp\ngx2Ci/q9lI+IQWT1Rf2EBzluwjA3/6fz0IoVAkelxKRiQsVSsdGVXuGB/QdU\nBChW4WzwPn9FbBiuLb+9SYY9lKjHmNbn0nUklvZVrpiUpWAEePZVp97ohqRy\nrq+U\r\n=5TmM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "4.1.1": {"name": "chalk", "version": "4.1.1", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "c80b3fab28bf6371e6863325eee67e618b77e6ad", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.1.tgz", "fileCount": 7, "integrity": "sha512-diHzdDKxcU+bAsUboHLPEDQiw0qEe0qd7SYUn3HgcFlWgbDcfLGswOHYeGrHKzG9z6UYf01d9VFMfZxPM1xZSg==", "signatures": [{"sig": "MEQCIFRLSrIoAV5Vuz5b8oIdXGeN7vlNJCSxq1vXEDPOGrdxAiAGLyD2ukbgFSt3og6x5DALaTSFTGCTd2ZCTZIqPX/CBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf+g6CRA9TVsSAnZWagAAYJ4P/3Tow5x9NagQZ4wlfmp+\nAx2ZtnEj/nASDGzd0E66OdyF69kS658zaMCBK96P/qXRYbCDJm77wq4W3Iui\nnaC6X1ZLcsbY9ME+EKHVCVXHiCjXC7EsPskkHrSUUYvwEiSK6wOH2ljfogNr\nLXQFbMT0x5cRpZZcherkrDWgJCFnD8L6+a9aDkfS/ZQZSBN0MHGZmi1AYl3B\nh/iKYq+ZhI1qUJaDhf005Oj0Vtegjozr3C+senyN4QWpUijoTF7MgJwiF/8w\nvb8e8Lzf0KlfdSvxtXeZ38m3J+y3+cphcBmkoagxsMtrtjzbFCUFIjHS6b1r\nldlAmH/xe5oemntzjX/7mXzhK6yMA515vAhKswcJ267oJ5tsnfa90bo2wh11\nBdAAr3RjWgNUb/OV1aIlFFvTCwc7O5yYKfkY7KXnL/NUGq0gpIJ1lEdTMgPL\nNaQXm41q0UEmVV9bi/BQKIUpPblVMYSJShcbjxhiZ9Yzc2vkVbzY73UXoEwE\nn+JIKCZPbC1RJQ/E8IjEtROoUFJ7MmhPm/dqMuw+/9JITGh7c4HYh1u0xqzI\nHp4l+Fc39tIje76zkT/QzvW9R3M83xOWxTTHARTJKtC6wqDytuYO8liIOEIi\nbcZd+j6YwHU8C071Dvncg2wELx7VmQefvxyWcjnuOCLyuHFOXJAsy79z3Cc4\n1lSw\r\n=t7Q/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "4.1.2": {"name": "chalk", "version": "4.1.2", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "devDependencies": {"xo": "^0.28.2", "ava": "^2.4.0", "nyc": "^15.0.0", "tsd": "^0.7.4", "execa": "^4.0.0", "matcha": "^0.7.0", "coveralls": "^3.0.7", "import-fresh": "^3.1.0", "resolve-from": "^5.0.0"}, "dist": {"shasum": "aac4e2b7734a740867aeb16bf02aad556a1e7a01", "tarball": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "fileCount": 7, "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "signatures": [{"sig": "MEYCIQD4dG0uJ9LWDhLIbBJazgFGOYD+6u8gUvOP/yfEluvstAIhAK7GZOqN8KY0Alry2lZ2MBIvWjuEGISyqHydp9oFrw22", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhA+psCRA9TVsSAnZWagAAaj8QAIsfB8+2kMsY5OUrDEqr\nqbznQ57bFD3ArrUSvhg+03aVEQV8fwgQHZtDC4KUSFuZGw/1l+3A+GFxvK68\nMM9SOD7jMNy1BUfzrfi0M2TR+V4pNjqJl+/ePQlgQ1SYzCZ+JpMUXdwkiV6r\nYzXuyObH6lJHPvdch+ynLSIYsQHHCDEe1qKEsdZXJzEAOc51a17ymlt+HdGg\nYZKENPA80H6h1bxOwf74i6uUNOYRLucAjDDuZX+cBxzILh9HOzl1MLRSgEJR\nD8Yl212H5+02bZb2Mhlrl+iFcfaPwz/CWWFxTW3dFoaAEVpiK+g06vlVKR+4\nIagsh89hAyWLl4mc661vycKi+WdhVQ275bzjtnzmti4E2CKVevksJS6aJKKo\nrtgAVlI0XEWDfduEEH3Bh/MfazmRl2BlKtKQoWmQjnj8ydydEchk2nN0N8cd\nnshOuqhOVtRoCROOUBIZHP84cy7teK0HDNKypv1Lu/43F3pZl12fJNPuo+zA\nGSjCke6YI6GFn0NkZ403iMQnOb8bF/nyQwVNUf/0DXEP3wNJbGyXyrY3HLeY\nleZa/LuXwt/4dXs5+g8RlaMjcqIukABwEwpsqX8G/ASaW9Do6VuwRqD7fva+\nXQCIebFDLUTbtAbI4L0qIqIDPCxvXR67m/j0fzHvzjGDwQzOlL2Og10rI0ZT\nmil5\r\n=FyBn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=10"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.0.0": {"name": "chalk", "version": "5.0.0", "devDependencies": {"c8": "^7.10.0", "xo": "^0.47.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "bd96c6bb8e02b96e08c0c3ee2a9d90e050c7b832", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.0.0.tgz", "fileCount": 12, "integrity": "sha512-/duVOqst+luxCQRKEo4bNxinsOQtMP80ZYm7mMqzuh5PociNL0PvmHFvREJ9ueYL2TxlHjBcmLCdmocx9Vg+IQ==", "signatures": [{"sig": "MEMCHwQWn9IdUcKlXMudz/Dn2ckiNTICSI/byCzWG3AhrfECIAiIfvI5iXGb5Ps8mCJMHTu07kPNDH3RrsZMoyxnI6ge", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhoK+PCRA9TVsSAnZWagAAX+wP/jaVhQWZpfCaDjmJsHuN\n5JQ1XlOLFdbzRfyvuZ2XAn96myIf/1UAa96Pi9gKXjVV1cnRdFW9TPgBMIzt\n42mqC2T8/qJe0a2VFIvlRlSFBMyThbyr3WnDCHvrgjfy8pp6EN10uHwSqnWn\nYL5NemSFQSc2EZ1LxulrJC8bzj3CzcB6yJXDMKUE3BfSFL0r5YxG2FLjktDw\nlL9hm/JxylEPgUIrzKNabO0W77Lcvg2nwJzxRZpDCkLC6NSLx3UV8nDyzoXT\nlvBiR1pxzjWrWbOPvTGO1+5q1HdVTOGqLWA/XjSBBpHZ7SQDgkh9f8pwP823\nep7Q3/2j5BvgleeVzOPQqwGXOq7qbAcBFibHTLd2Zi0xXBFMHtIVaw8odDVV\nAsWqTV4qAgRrefe8Q7wIzUrO42Z5ndRfqHSI6t9bJ4AeH/ZL3vrNNmTFIoLx\nJdWEu1WXSnkrFdCWkxHIjU0wyVJAIAOtK/vQw7LPmOQhEeHLLos/t7fBP9tC\nMi5UoMNZYYE41d5MENeySPAHXZ+ZVIROdAqN7mCCwfAg8lygiNfbNLiPeo9o\nld5H9hYFw4eW31fwvlX8Q6MclVR5k9755fpZUHcyDqgN6hgLJU4j0PHSvnhI\nFrB0wwi7SNJ4DiqIlKtpeNaRSwy1cnaiML3wFeeYKf0zZEAp26bf4CNbZ9XX\ndcEG\r\n=qfhV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.0.1": {"name": "chalk", "version": "5.0.1", "devDependencies": {"c8": "^7.10.0", "xo": "^0.47.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "ca57d71e82bb534a296df63bbacc4a1c22b2a4b6", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.0.1.tgz", "fileCount": 12, "integrity": "sha512-Fo07WOYGqMfCWHOzSXOt2CxDbC6skS/jO9ynEcmpANMoPrD+W1r1K6Vx7iNm+AQmETU1Xr2t+n8nzkV9t6xh3w==", "signatures": [{"sig": "MEUCIF8zpu4flQt9Ck8M+VS0lz3b8mdY12JcKsCNaVK+2pY1AiEA4+L33unqlFikjPh1wCQQcPQ5jOpz9l5PGjjp1ERb/GA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJ6QUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR4hAAkZ1Pzo7shU7iLgAUqhpUI79vsAQoqkKYBv9iC+Z4ogWUlcd0\r\nRmauMjugcrKH6fsokqp9jDuJLiZSAbj7nwbmWDoBR9XRrxedjsB2H0eANVlC\r\nTAecFxGdN4gxEBzE6yVOfJelpNuw2qWstXKwOZmLwZkTlqE9LETYWO7xmzfs\r\nUW42alsTOP78ZYRnUWwkeZr3Z8yt7thGUwIi9q+QFsDIt0VwE86uTLeLvv+6\r\npNI04MyKzNkKRG+PF8uW+O+j5PYIr9BPYY++g+f+rayFdwSYcoQjutF0BHdj\r\nG+7DGTrk+dhzCJ5xLSZY0rljcgyY9/KMSj+3rO7fUJalPIpqbwLClmMtfJQU\r\n2MmdM+dJfsXAfoZA5rMxP3GoOC8LC6RaScliVivockrCSJgd9Tp9KzRt+66I\r\nqNiuztSypB5SRB7TC41S8jvIHDw5PNNnNP5C7E/uBjU35yc6aX/2pyxUeXYm\r\nAOPLmVWqBcroBiE4zi4OmG1fB2Izw9DJsK31i2U98lkMLfkVaUa4tIBgVwbU\r\nkB62F3BA86yYuWucYeZL3hp9H1TG5+WBhs9+lHrb0+mH3b6ab2lZu/uCWvH6\r\nSedXquHHQxyM9JcuxDrDtmELqR87B+h0iAKzBf7KYtmyrqrxpHE0FiteQsV9\r\nUJbfD3SPAaX/IBAryNeP4Q4e/oSQDy3YUuI=\r\n=ZFqY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.1.0": {"name": "chalk", "version": "5.1.0", "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "c4b4a62bfb6df0eeeb5dbc52e6a9ecaff14b9976", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.0.tgz", "fileCount": 12, "integrity": "sha512-56zD4khRTBoIyzUYAFgDDaPhUMN/fC/rySe6aZGqbj/VWiU2eI3l6ZLOtYGFZAV5v02mwPjtpzlrOveJiz5eZQ==", "signatures": [{"sig": "MEYCIQCGiB2UgkPRG76rTZOqtyvgHW3GZdkr1l1grc4mtEcDuQIhAJuhTuvzptwKcBUWBFc4+2YHWu3vqzlDzAAfqkac2pZ1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPZi8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoY0xAAiHomYe49a9lY0i07r0BEJZGVmegNRGYPJAekN+UaRE/UOx67\r\n6EzdIa7v74VYkhVD4rHBg/O7Uqe+O0U517do414JWVWVXbkB/t5RGjaGtH1s\r\n8OQ85DCyTmIhD15M107/v3qSKWLMhE8lca1KnFYKtCrGFfmEsib8hAFsqS2F\r\nWnrEM152LuQeXnUoUwCaI5yUefG0uLe/yuKhtyWcKToxjmK6HH4ORlG+1o+K\r\nsyXX6Lfv3m40LAIWayP8KE49RjZ1kM0sFK+hasPg8hoxMQgSyM2URnjcUPVN\r\n9wi8ReRUHuaWyuPEPWfRRnYpK8IQnTo24eFaSz4Jz5Z/yVLDYhmOuvzeqXbm\r\nkv74Tnn4HlD7iju3erQQjolvl5D+mKh9ObGjl92nekeyg8G/A/w2EfMpd8FR\r\nJfFBr9Ty+vtNEYbl5iKgzjrl5uLEHKmOGfXXogLsahWB0EXuX2AV8dB5OMlX\r\ngmr68f1jvWYZmxiaKHkXVsLAW/TXl1zWOotSdeYDVDpZQ3SOKtZCez+QKnBQ\r\n1GFqIj/nBmkSGtP++w9Hk9PGAXM7cW/7pLgwT8qGLEeT2SrwnEEAUkImPNJe\r\nnLHSyaS7l7nA267jR5XQQ2SP0wR1alYrE4MKL0nw5wHau8bxLMb1ZFjxE58V\r\n/LYxUMGQDJqfgNtyYmtq8CycQRUv3kbxsHg=\r\n=0Hlp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.1.1": {"name": "chalk", "version": "5.1.1", "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "546fb2b8fc5b7dad0991ef81b2a98b265fa71e02", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.1.tgz", "fileCount": 12, "integrity": "sha512-OItMegkSDU3P7OJRWBbNRsQsL8SzgwlIGXSZRVfHCLBYrDgzYDuozwDMwvEDpiZdjr50tdOTbTzuubirtEozsg==", "signatures": [{"sig": "MEYCIQCe9oT/CKTmjgzKIXm3S+DVkuwywIySphHQrSYz9xWtGAIhAJNOTCunCt5qIDhrg5pWHvdPHorZeO13auuylbrUjTq/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRoroACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotkg//SSk78lf5lQWol2k17UO4dnj+LpYx4gNYLk8uptAPbsRLyhx2\r\nhkmiejrW9Komrzx1FO58JwocXNS5Q2I3mUqG3WisGokfvTWI6kDVlx9l4M0a\r\n0lPo/ENgk9lPN1WUMVx60zCneP/NDxG/sJerE8cM/UnWdc/YHKDiHQEU/hzX\r\nkGAn5juElGo0ae4PAL1M15UsMg3Mk4lyhTRsoztWxYfAI1pNIAxI6ovTaVWg\r\nG/dT6b59DsG6U8dM94cwGxFGtOsnzaBfgupH5Y1T1GvwVQCgeFiLlWWXLAqc\r\nsqP67fUDJtMY1qAJCKhFm80g4eCr6NOvfZ8yovn87Hw8RRmglyOrA46MpQ70\r\nYdJbJBruOToWfR9C9VpcO7BzcPJP79ynfyX70ULe1uP6MZ6R4nJr5FfKanm6\r\ndde4ZTNvLr7ZDWhbW9n4Fr7cVMsT9Jr3f1R/YJLqBNpp0cQBP7UeeJfi+yRj\r\nAsoD1oaDgdDIjcjRBUVAKirU4EwNZ/tk7ejQg710KBK2ccgsiav5rm69TeDd\r\nlCLJdiE856wWK6ZRqY/aXdydoglVJb/L8rG0UK20gaEw4U/BGkLoyvWke/PG\r\nz8VUy2ih3MlSRNVBgXW5U15jT8ku5pnf0jT2yiNT9ke+Vk79Ke7TCPqUj+AU\r\n8htjOcAsRtRTJnQa15nytGTSRtQmQzGqVzg=\r\n=Tswy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.1.2": {"name": "chalk", "version": "5.1.2", "devDependencies": {"c8": "^7.10.0", "xo": "^0.52.4", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "d957f370038b75ac572471e83be4c5ca9f8e8c45", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.1.2.tgz", "fileCount": 12, "integrity": "sha512-E5CkT4jWURs1Vy5qGJye+XwCkNj7Od3Af7CP6SujMetSMkLs8Do2RWJK5yx1wamHV/op8Rz+9rltjaTQWDnEFQ==", "signatures": [{"sig": "MEUCIHW+BiaXhxpezvF8SWNxOCnLYZEqgpN7iUr+4sfRrJ1NAiEA45MmChdnALfbsbbqaI0RqSrU6koyuZjSI/FwkJrgCf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRuv3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXDg//SHD6IFmJB0Dq/i5hFI0gxlnD9aTFqVEQAatIyxOJvXvEyS7M\r\n+JD3/dOU44ePZ4zH7aYT0YrUnY1oHpijO1elbyL4mhgLxauHuNpYmvgH7dyE\r\nuSKSbvCmvtB/lzeTZR5VDDvq/CYRZ4CTL3N9oZmRMV4b3yw6JGZVa7iTUYvS\r\nz5WdJ89aGNmtH8wvmR1HOfDQOeBEV438VpGhBp2lKy6WYYJVEJgddR/FutMd\r\nwJ4Ptm810A5bNMlhfCQRmjYwEgEz9EipavaM+sS6RWKxLGps+tvTRz1Ukgt5\r\n4yRaEjDpYywtB7ujl/dA2mXKjnJo92uLmirmQkSJHHjSJ8r+M8lx9y4CYdo7\r\nlpzHovkYnDacd3F9rKMZoFZZR7a/VWGCBQQM1P7xabPqCJV88Pyqv1jsjSrE\r\ne/mNaEqSUFomZG//23pavE3UAe2aCfX9V3aZay2rYDdeAmLebCAhxaNqUscL\r\nok0aOwSJG2c0c2RxceQl/7o27CY/5F5iJqg6Dcvk5MbuF0z5GIoMoIRYx4Rs\r\nouwEKeu+7ei4Du7qs55GsjQuSUdR52L5s/UtT1u/RUEvPgoonP33LSr7UxZD\r\nOuN8BdmCdD9MMfiZjf/RUJFrApOa4nCyWB4p9wwKTQc2DZ4TErJJIWwQxyz9\r\nKAkARhOYA8iaWDtpBRlVbeEI/xnbezSuqrA=\r\n=ibOO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.2.0": {"name": "chalk", "version": "5.2.0", "devDependencies": {"c8": "^7.10.0", "xo": "^0.53.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "249623b7d66869c673699fb66d65723e54dfcfb3", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.2.0.tgz", "fileCount": 12, "integrity": "sha512-ree3Gqw/nazQAPuJJEy+avdl7QfZMcUvmHIKgEZkGL+xOBzRvup5Hxo6LHuMceSxOabuJLJm5Yp/92R9eMmMvA==", "signatures": [{"sig": "MEQCIGpkIV4DWbErlKu9VI8Xrd6zVKzVBcJP1P4uiwhcauWeAiBCaqZ8GWGM91SUt7da69Vy7lSbP1Qsfns18NVexta3Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkjEDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV7g//SkAHVpwaXi9OSemLPPViDVvDgWITSR6UOiBP2kt/IAYeuRw+\r\nBJPNm3iIrJPK55H5xFW8hOTasUdt6pltGY6lBLkNYytOGRKx6Hj4+6gMUbvI\r\nHhrRUb3IJyGAkxQeQ1yqSpjGpubi5T0Xe7q5WFgEm+0cibDPqOBBAl2/Y2mL\r\npHW5cL2zyzTo76TW6m33PjA51ima7eSlXx76ZBYE74NZ69mS30mkxGx7/KW4\r\nN83QnYpIvZi7cfFqcMQQhnD5LK9vm1bE9wbdVvwj3r6tFt+W97egZ3qiBU4g\r\nownwJUfj7tSP0Vi0XQeLhImIh9efdxqvRK5HJjaEWDQhnyAZIw9S22aesGD/\r\nwjL6ehZRawG7TAHvMh4KC/8Yv4Wubx5dJgvGuwOjgv1+45bt+r7xfKPvnBBb\r\nFt+s8YfMvGMv685020Ht9TGmH3DwypB5RltnZoOnmvBpWRfP93uTkLZiweek\r\nbRfzqfjK1kj8V4qqphCfJYU71d9s1q7A5fnRob+MTAJ1opRyK2v30du2D/P0\r\n3IBJ6fynWJUAMHyWg7BZRDfsNWN/pYkW1UP1fMy8V93dXBa2WGuMuVwpRBxK\r\nIzrXhc52uxi1jIZDaZ2RbhcdIxtWLy+5ikYx34nqdMFquh0WWkhXsus1t3A4\r\ndI5gxKg37xdA8NYh9kwQGmZ6HsjrmqMbKUs=\r\n=tvom\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.3.0": {"name": "chalk", "version": "5.3.0", "devDependencies": {"c8": "^7.10.0", "xo": "^0.53.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "67c20a7ebef70e7f3970a01f90fa210cb6860385", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "fileCount": 12, "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "signatures": [{"sig": "MEQCIC7jtsJDO883o5OSOE4ygbH48k0Q4SciRc0MhlEjWvJRAiAi1pflMpbvv+4KrVGVN3ZhppLjF45dpLmLJ3dk1VtZug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43736}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.4.0": {"name": "chalk", "version": "5.4.0", "devDependencies": {"c8": "^7.10.0", "xo": "^0.57.0", "ava": "^3.15.0", "tsd": "^0.19.0", "execa": "^6.0.0", "matcha": "^0.7.0", "log-update": "^5.0.0", "yoctodelay": "^2.0.0", "@types/node": "^16.11.10", "color-convert": "^2.0.1"}, "dist": {"shasum": "846fdb5d5d939d6fa3d565cd5545697b6f8b6923", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.4.0.tgz", "fileCount": 12, "integrity": "sha512-ZkD35Mx92acjB2yNJgziGqT9oKHEOxjTBTDRpOsRWtdecL/0jM3z5kM/CTzHWvHIen1GvkM85p6TuFfDGfc8/Q==", "signatures": [{"sig": "MEQCIF9O9rt/0qVcL415fsimTeAtarR3ClkFa2x24fPx4VzsAiAyfeEfT32AN36Dl+1Wt4T8dyAGqZc0ktD3+hgIiSNnGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44129}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}, "5.4.1": {"name": "chalk", "version": "5.4.1", "devDependencies": {"@types/node": "^16.11.10", "ava": "^3.15.0", "c8": "^7.10.0", "color-convert": "^2.0.1", "execa": "^6.0.0", "log-update": "^5.0.0", "matcha": "^0.7.0", "tsd": "^0.19.0", "xo": "^0.57.0", "yoctodelay": "^2.0.0"}, "dist": {"integrity": "sha512-zgVZuo2WcZgfUEmsn6eO3kINexW8RAE4maiQ8QNs8CtpPCSyMiYsULR3HQYkm3w8FIA3SberyMJMSldGsW+U3w==", "shasum": "1b48bf0963ec158dce2aacf69c093ae2dd2092d8", "tarball": "https://registry.npmjs.org/chalk/-/chalk-5.4.1.tgz", "fileCount": 12, "unpackedSize": 44242, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZ5wDikSytgziyeCEmOhw4VJznYEkpJZXRQgJr4o+SUQIhAK3MBAa2qCkGdIpM+MMML0rPG+R12h6M+pqFXKxYO9Yq"}]}, "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": "https://github.com/chalk/chalk?sponsor=1"}}, "modified": "2024-12-21T17:04:53.207Z", "cachedAt": 1747660588926}