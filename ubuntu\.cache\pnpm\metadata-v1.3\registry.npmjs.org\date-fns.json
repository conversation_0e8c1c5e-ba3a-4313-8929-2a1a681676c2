{"name": "date-fns", "dist-tags": {"next": "4.0.0-beta.1", "latest": "4.1.0"}, "versions": {"0.1.0": {"name": "date-fns", "version": "0.1.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "ce563bc3f6b4da722b3b1242592efc233ddf58fd", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.1.0.tgz", "integrity": "sha512-eOZs7wWJBHeI4mHT0pDZt9qgh5QsaHS79jIMiu4kZPCfUy41QrWZ2QgBB/x9c0jrE3fOvBSHJfhEmdeF9ily7g==", "signatures": [{"sig": "MEQCIDTOaLv/Oxf0pkqhJ4BXMWVajiC0YQwQQH8wdGmLeAevAiAS44h36exkLTWUVwgyZNG1aKRSzWGCQ1LzmkF/FzUZGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "date-fns", "version": "0.2.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "aefce80c1a762e606f563cb455e67fcea7ea54d0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.2.0.tgz", "integrity": "sha512-YCIF41XoxAb0Bm49+qJyOMbGx9EKJj8wnWggeYpSxk8iUSyl+vxxsBpJ+aPCNItAmIr2a0R2Bbk94SCy8OVmvw==", "signatures": [{"sig": "MEYCIQDSw64uTXLlQD9W+ciwIVzHh+5G3/8uS0Ktyw4emoC3zgIhANqyNjZCsjvtfIS67yebmCgmIayHgDBPIvlRsbgmZLsH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "date-fns", "version": "0.3.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "e66023a4bf9037950fe98292d9703b2ba5e5c7cb", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.3.0.tgz", "integrity": "sha512-4hQSiqjFqrTrd0ojNgGuSFDNC5Qc4RKvyc6yJCcCIxz6gBf5XjRIEPaGfCk4tCmlV1rPhcJXmqYQ8znRNIt5Cg==", "signatures": [{"sig": "MEQCIGblKjaqqFt7V66MZwRP37TJ1npb5ny1go79VLeY2j7zAiBelHe2eLO6PuIdjWY6G9VoRb1bZXxtzeHa1NSSjQX2gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "date-fns", "version": "0.4.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "9df9813eb52b9727cb7aa53196ebb5cde48b75eb", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.4.0.tgz", "integrity": "sha512-cYQ249DrzFlmgxMc+j4G2zuNnQFHCVz/FzU+EhX8Mgf5SHavLoeoU32Vevehmq56c24GMyNCsCDga2PON7KOUw==", "signatures": [{"sig": "MEUCIQCCA44EHOI7/KIcngE0Qa+Nj/8DdjEJ/i/OaQ7mBDndEAIgB9TwF5E2WRMt+iBwl8M9GNdqTJDvi4CleJq0mCEeOGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "date-fns", "version": "0.5.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "4ac7beee1705922179f1f95a9cde2effd62546a4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.5.0.tgz", "integrity": "sha512-EjEgh1aHOULa5Mm2iCFpvEUQdX7ZGK3ytvPQUOiXjcYI4I5M55yYE2BroI0UeS088u5fwtZgFRuzARWqaBRIsg==", "signatures": [{"sig": "MEQCIGsqFoFFC8T7Alx4TH6EshNwTmL8EaN8u7IhO21h4d+yAiBerVwUCj4sDcB4raJsetWxavjD66cxjnVt0jG/a5hHng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "date-fns", "version": "0.6.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "befc057e696884d5e32d0b72638c436b8a919abc", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.6.0.tgz", "integrity": "sha512-4KeAJ5rlwVoNpIzgrSn+7aBLMVx2g15gLJi3CAjE6H/HkbnbXfoS4alUb1rGisEw2Z2zKRdNQUJf2TZXFD0R9w==", "signatures": [{"sig": "MEUCIQDTLkQoZGEeFepOd1ZaqtmnqpIJeYmYka7rAQYJPN4z0QIgY028/WT4CndjVVbOEcO4bJLW/3Rp1v7ZRHjw6Jlr5wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.0": {"name": "date-fns", "version": "0.7.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "7c7470e17c4cdbe92093612765ae65a9db0ee0e3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.7.0.tgz", "integrity": "sha512-SyDVuoySqM7tGIDqgV7Y/QctszC8ZVYU26x00Hc7eTsmG/NfpGLea2VWAUC8qJGXDJjbX7ddWzcr8MCI3Z6KhA==", "signatures": [{"sig": "MEUCIQDD4Rqv6KW93vdKi6yv1lMPSPDZ1/k+9Vw7SOC3LzjY5wIgBuRNULm8tPphzBo+0LqnhWTDuzndT7uNtvn0OyQj2RA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.0": {"name": "date-fns", "version": "0.8.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "945623acbc2720efd7a4632deccf8acc343ac541", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.8.0.tgz", "integrity": "sha512-GxP4a3dueuvA+i8c6zhgHNgqkJkup8lJVUbTMke0A+RkTMkSLG5T8EIUDW473SS8GSwgkedP4tE2bCK+vtYiYg==", "signatures": [{"sig": "MEUCIQC32kL8tH7t/aEQe9KbsR9ZQrBIspNeVDzJJ+9H2wHfQAIgLp38ELuGeAu1KDVldI7fSaRDJEkTCn4jYkUgiXbfo48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "date-fns", "version": "0.9.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "df24ee130b505ed79e86ad48bf575c5c94d9c214", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.9.0.tgz", "integrity": "sha512-wPvyeMtx32P5fK59ffXaVaKr9p8q86hHxidcGZiuAYdP7ZPtXa7nWxTHZVBeJXvnnCzLoTBhEPMhxINeVI2UJg==", "signatures": [{"sig": "MEQCIF4mzvxBWlSqxNBjbdXO+5LUVGoJilSFAIL1Mt7wKh6yAiBjn8dLM6bm/jaEjzCkd5NnKg9lInypssSw9W6R2vnBGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.0": {"name": "date-fns", "version": "0.10.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "2491a73b98f8f1487325a2de1fc6573e4f3b33d4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.10.0.tgz", "integrity": "sha512-K5DLzQ49tcx92Pc34um8wAVeS7JVxl1HrsHSHC9dqIgaByTEA0kr3eaw723A3LDh/jZcqkLCeZSPlio/at36Og==", "signatures": [{"sig": "MEUCIFe5D8FSxHiQ+zpJnA+OoC93WkVHsPuJ93H1Wy78YzP2AiEAo2hAISelZjfR+cjYWywBKRjZTFva5b1jC1/Yj8Sh79I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.0": {"name": "date-fns", "version": "0.11.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "f88369adb528b58f339614f59f8d6f1ff6af56cc", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.11.0.tgz", "integrity": "sha512-JUZLsyAZUn2urWeRlFAEEJvZdCTa8HOVSouZEiB/0P4/HgExtappiyqxlcDpnpcxGLN2elOVkl5dbb3qSy+rKw==", "signatures": [{"sig": "MEQCIFUAvMzFFvdwbT8Qkla2sIPohgJ140Es/BPETULlBLpEAiAz125yZ0Pk+XdZCGwgnwvy9SKX2vjtsLCX7F/bohtpqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.0": {"name": "date-fns", "version": "0.12.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "35f5cda8fc4e88ecb8a5afcccbbae9955883282c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.12.0.tgz", "integrity": "sha512-HtR9uPPbfKodTfK3TVmzFb6Apy7bFT49kJ+6AMiYHw9+EvR8b2xt59Dza0KlMCwnI+rRhK3Htcw8P1uK9ieR6A==", "signatures": [{"sig": "MEUCIHvVWe/3mRu0XwVlpXKTWNzN1bwI3xUT/nuUmRFLGw3ZAiEAq+obr1UXMgeO17OtulWKOgP00NuL3WN5MP02/HqKGxk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.1": {"name": "date-fns", "version": "0.12.1", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "f7ce022036789006c2228ab118b4cc42924ec5d0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.12.1.tgz", "integrity": "sha512-t/N207L1WP5LYM7rhL5PpS3WRa21FpK+dAvYBaGxTvNDwEYwUvENqvyWgYU3GZQUNsw/13HvQQ0hM65QSOENIA==", "signatures": [{"sig": "MEUCIQCuWITDs4bGV0yVSC14xs66G5kejWl0M5RPQkOlIjS2EQIgZ/UjVfya3jOD7ISqx3lLF0iHtI1CyFoN1Ffoo3IgRMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.0": {"name": "date-fns", "version": "0.13.0", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "b3e1af75ffcff674062858c2eeb05eb23e0e7ff4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.13.0.tgz", "integrity": "sha512-qbAVvNkxk6+hdczeFhULgxEn0RF2o7PYhdBG1pujxKo76o5Lr6H5+28D71SZA69Hxtrpu06GRsmaBK05X3P4Ow==", "signatures": [{"sig": "MEUCIE9Pz8GbrN9PKFyBggevJi8muSk5I04X27QRItjgC2brAiEAnAIQWNPEvka1Ipxc3dsWVFAVEtFXurvO6Ffm18TBy1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.5": {"name": "date-fns", "version": "0.14.5", "devDependencies": {"karmak": "^0.1.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "47390ed9ef0cee3886dd86bf8010560f2a253c01", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.14.5.tgz", "integrity": "sha512-neKu0HDqXf5aYSCQwOFCREDfTmLptke/8cOLY/e61tLD+798w2Bln5Fi7WDIqukUZ87gfbwoLIJy/7jZSI8tXQ==", "signatures": [{"sig": "MEUCIQDTrwNzIv4M5Q3fAjQF50Rb6qaIZH4yRGSrNgr3pl6rLgIgMnA5bEcywgNsdJMmePifGpVQdljBQCp4/6RVCMEAOH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.8": {"name": "date-fns", "version": "0.14.8", "devDependencies": {"karmak": "^0.2.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "d32f011ef800b395487c15171692034e3f69ba35", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.14.8.tgz", "integrity": "sha512-qXUvy6xsFrY4HgvGKn0W2CGvM4kA0KGdMRsq11XdEcvIbXoRWS/ZL9Qd9YiGZ1A+uVqObavaBMrWsRmxQe29+g==", "signatures": [{"sig": "MEQCIFbs6hAOU8SpZ5bbplgk32wuzETyjlA0g5rmxeJsrloqAiBVNF7iY3Ae0jhsxGGN5QwzYsXmVP4KM7lBcxF9rcm2OA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.9": {"name": "date-fns", "version": "0.14.9", "devDependencies": {"karmak": "^0.2.1", "webpack": "^1.4.4", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "8c1657dd6a0f21fd91658251b80c6a8e17e57065", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.14.9.tgz", "integrity": "sha512-uonj7PEr/GWEp7h+aJfIiiNDzYeptTMriKWbNzMvEhQAaAqbJyBw2vrf9YfjSNEN019S4QPcVebfrX9LApu3Fg==", "signatures": [{"sig": "MEUCIBvt0jB8uB9JQNjXMc+l0nMhyTdkL3ufqVYlBYkX8a7uAiEAoRWpZ8m3Wujqylg9L2ivXx1ezzBUewwurFCKE3DXjls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.11": {"name": "date-fns", "version": "0.14.11", "devDependencies": {"karmak": "^0.2.5", "webpack": "^1.8.8", "karma-mocha": "^0.1.9", "karma-chai-sinon": "^0.1.3", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "0358e8f319bc8314d7c7657fc70622a6387fa800", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.14.11.tgz", "integrity": "sha512-2r3pFOs31aOnKz9PC/L9k92OYV/xfZIbl50YhIYPR6UhoAOp/0s4su5U5RPED0MBtpamaQHJRIYir/l0OCd+LQ==", "signatures": [{"sig": "MEYCIQCjo3Li1MTif2Ypz64taAZaVR/rR7sOWn31PfEDn9NDyQIhANgFe/cYks2LHP82EF5rFv6uKF4B/uHTEVgoRkMerxMQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.0": {"name": "date-fns", "version": "0.15.0", "devDependencies": {"karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-mocha": "^0.1.9", "karma-webpack": "^1.7.0", "karma-chai-sinon": "^0.1.3", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "4cf3457b9e12586180d664415468d8d3b61c718c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.15.0.tgz", "integrity": "sha512-6i4e+WFLQaSzNbBOVix9iJadRnjZX8YvzchDd4kc2DMQ2wsQAzjSmuWR6rI265QK6GaJVpl+ohJxvn8zmx/yWg==", "signatures": [{"sig": "MEYCIQD/qw9jArsDDrTHbj0QFcXiHMmTnwTMzeaRpqUd4wZzFAIhAMbbNrYKvtUPEXj0vmSm88RGRJsP9/dnuXC1z2OPdgHM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.16.0": {"name": "date-fns", "version": "0.16.0", "devDependencies": {"karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-mocha": "^0.1.9", "karma-webpack": "^1.7.0", "karma-chai-sinon": "^0.1.3", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "baedb55172e1fbb0383009e24cc243825f9eed36", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.16.0.tgz", "integrity": "sha512-l4/Y76tg1t9J523rjnvYMDdqUup4fe9QC/Tn5tPeVG9c2jZPtu5SUK5AjPSUbyx2v755kX3TebPW6yCc9PS0wg==", "signatures": [{"sig": "MEYCIQDx1AGcDay4RDHyoeM1qQRjtTPQHW72ovuns/rb765qFQIhALTKyW1E2ML4VuQpe2ffG8Po9AcfiyfxE6rvViN6d2rj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.17.0": {"name": "date-fns", "version": "0.17.0", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "js-beautify": "^1.5.10", "karma-mocha": "^0.1.9", "babel-eslint": "^4.1.3", "karma-webpack": "^1.7.0", "karma-chai-sinon": "^0.1.3", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-phantomjs-launcher": "^0.1.4"}, "dist": {"shasum": "26d56395d254c97db848ecd1862a4e22480672c5", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-0.17.0.tgz", "integrity": "sha512-MqPJq8f315h9kJVhIcYVLXWXdHA7n9/DmNSpVCMpIHLaIIrHLpSqCzc5iWhGlMZc9HI7917zAqAkdqGf3V5+FQ==", "signatures": [{"sig": "MEUCIG87NcpcUl/RFTB9oqrogLLeRCZKT74a61EbfQ5A0xCYAiEA9zK2EVwgmY5mzlaY/EeoVMTIrBvkutAD1J2J+UUEy9c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-alpha1": {"name": "date-fns", "version": "1.0.0-alpha1", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1", "karma-browserstack-launcher": "^0.1.5"}, "dist": {"shasum": "2d389811398f0fe914dae48f38114e439996b374", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-alpha1.tgz", "integrity": "sha512-ukuH+cQUVJ4ebhlPkPD1ALE326/K6CnZ3WJl3T6+uHcHNPuTCeprTV4isYLEEBvxnf93ptD89g/72wEU9Wnsow==", "signatures": [{"sig": "MEQCIDbVGWwwnPXaYOylhU+CWE7R8BK38wKPJFEaHeSiE5p9AiBm/6X2aNhdc+Yy99yl2dUxSfh4kB67BAUAeCJI0+hVew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-alpha2": {"name": "date-fns", "version": "1.0.0-alpha2", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1", "karma-browserstack-launcher": "^0.1.5"}, "dist": {"shasum": "11d61c708e06632f4f3dcd8ed5567d4e0fe512cf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-alpha2.tgz", "integrity": "sha512-v4eO6xasMZ2rwpvNkv8z9ECHIXfxxsR4FghKzGUuN6m8W7j24xchz64u2bypZPiR2N62TGUfJIj1/wH2OEZIjw==", "signatures": [{"sig": "MEUCIQDxilf0DBxouKncEOq4hnFedOWJudYMHsiIwuaSRPEffgIgPPSfos/f3nIUSN4BB/QQhEcpxHHbaNeSdeENdChSxCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-alpha3": {"name": "date-fns", "version": "1.0.0-alpha3", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1", "karma-browserstack-launcher": "^0.1.5"}, "dist": {"shasum": "250cd8f53d4cd3eb53b08dd2954edd554f450ca8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-alpha3.tgz", "integrity": "sha512-aKZdqv6J9dqPK8NfcDGHl2eKSWjJn/sySmqoyxb3uimFhMX00SFXu4TnivP6KoNW/L/3fhDlXa2iYyoJvwGzIA==", "signatures": [{"sig": "MEQCIEqgrwXsWa6fjeewp/ygHNpTY72+zE2yR6RclqVXSBY7AiAKeQbxMSyebj5BFUZp7eZW7sWuSSkM7Px8Xj7TdSDc1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-alpha4": {"name": "date-fns", "version": "1.0.0-alpha4", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1", "karma-browserstack-launcher": "^0.1.5"}, "dist": {"shasum": "5a5321d03414f8e1f7421842f6ab7ccb48ed9138", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-alpha4.tgz", "integrity": "sha512-BJGKhiw1IANR2KLku27bFUBMjP+epc3yI10UVwwr5QStYHmZ2Ti7zAUrTIZLjy6OVBbJa6AAuacoLOX3DerUvg==", "signatures": [{"sig": "MEUCIGDn9GCR9LbfzOAKc/tEtDjK69vST48yh09CXa29gboiAiEA1EkzcOTP0kYUkNBi00pvzgzngWT4fn0nqgb1N8S9CJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc1": {"name": "date-fns", "version": "1.0.0-rc1", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "2c121bc71a14b7ad2f8767db53fc870a119a84a3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc1.tgz", "integrity": "sha512-hc28B9ZaHYxzV64VBw9CnnwWlmoX0RLCDZwPa3V5rr/O02yVr4L5eyFaLf4AlRZXQdtWwhD/ZQLc69DY1s0NGA==", "signatures": [{"sig": "MEQCIGGIcK3NdaiB1o0n1hHRNGO6AHfnTsoM+ECvfT/dTMTOAiBwR+PwLA5hb5t/X9eDFzC1M0scaR7pF/A9IOFWa3KQQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc2": {"name": "date-fns", "version": "1.0.0-rc2", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "9a1e8b946ff2ec726b50602d787ee51098b4d6e2", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc2.tgz", "integrity": "sha512-FExcWh3KwH9STDXuvd0vZSTVHAY9WZN+dbAK/xt/AX4XJicb1KIw0c+ZCcbhSO9Bp66R0qntrAhthSKUW07kVw==", "signatures": [{"sig": "MEUCIEniAn77XyqHBui8szJiL/00atGqT/UIkVL9qS7YshcSAiEAmUDC2ma9r58fjGWOR0+JTb8sM1gXYZd07mRy+EJ3cFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc3": {"name": "date-fns", "version": "1.0.0-rc3", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "dc15ec5bd7e3f2dfdefbbf15e1844ec3c989315b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc3.tgz", "integrity": "sha512-D6OJE30WSbT4IxX+3+wZJu7FiGYH2rKnaCshwpkgcuzJBgwbmvz3dxZpFPHjd0zo8KlYVorQ8mlJKJuB2SVAxw==", "signatures": [{"sig": "MEQCIEWfPwRPPYKOpjXEfQRa5zf5jdWRKVyFLCzXMoJ/OAGOAiB5ohx8IKuWZPIAsLQ3TetDAYilz8URH+46pE3pazsuMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc4": {"name": "date-fns", "version": "1.0.0-rc4", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "4e76fda94e115b6a4642c620cf45d3438c17aa2f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc4.tgz", "integrity": "sha512-Cr90U2Xaa0yXoYm8T2vCjB5T7OflBgLUgMLMGglNhqkgTgUAQz5O5Zv+m97imOOKa7TlFwE01MDaj7EKmu4X4g==", "signatures": [{"sig": "MEUCICMtpy5Z0NmFooBnSZa0Y0+ahnyRN2UcHMlO9gbVJQNfAiEA9tljYtVw8oFkZ4HjtF2IM5P1OCED/ZiqEPNqlCPpDWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc5": {"name": "date-fns", "version": "1.0.0-rc5", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "e891450b3f590f2c44f08f211072c68ab30fc55a", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc5.tgz", "integrity": "sha512-egnZB4G6EVvYkLCLBHgFu4AtSU3ogSxuHvHZzK1JC1a3Gmj84qsceMxQ9pk4pUCM3vKbsLnpZK2OclPI6DrykA==", "signatures": [{"sig": "MEUCIQCp8jUNZLinV1Y/aFOMNw3KIkzx97dpoXliJSJ+ngBsfQIgH8ci4sqHGSz0H/tOFj5HSNjJnxWdfJYwyXJ2Xry+Tcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc6": {"name": "date-fns", "version": "1.0.0-rc6", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f6af8c8f44d6d6b48067f743b03baa37a291224f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc6.tgz", "integrity": "sha512-l7ZSxT1M3uEndGnm9e9wCyjrbUv++/6DLtmOA6KvAZNFTjaaDQgKkm2Sj/SYm4P9nlwfRsgPPqRdg2kGz0tXYQ==", "signatures": [{"sig": "MEQCIC9RSU3uF5f8JzBgXDTHvalBRVokYxtrphLghY67kzAnAiBO4cnNnTU215GeEqp6XJFRmWWi3PCHpYQ7huBor4Bmeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc8": {"name": "date-fns", "version": "1.0.0-rc8", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "ebb8c32700ecace8327c9dbd8b356f46e64212a5", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc8.tgz", "integrity": "sha512-SEO3xGw2ogiNkLBb5V4M/uSxG1zOuy6FubeHLquPOGWJzhQ9zpZlH53XkgNGF331x4PrFQK6OX5QmMLRNR7Klg==", "signatures": [{"sig": "MEYCIQCXz9XcJe+qjjdKrwewhndIxN4eUcyaCsOGVyhvqnWkjgIhAJ4NdZtGZqaxS0HkEx5DZMtzDzOuOxWs5D/vPXBr3XpN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc9": {"name": "date-fns", "version": "1.0.0-rc9", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "38e04a76cbc9d31ac8795484875bb8e245b68218", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc9.tgz", "integrity": "sha512-ZHQggPVZ9A4ot5USP8I15d685rzv89LbQPkHECm0fuBo8D5lj1wPCNwUZAWUKzJhBCmIdBCzAq21Ht8SgtLKmQ==", "signatures": [{"sig": "MEUCIE8TBd56F6UXIXuqMXbn6CyQGbq8Y/Bg0pt4Dulj1ePSAiEAmWKCU/fHIoKU/Ygn27yNxY7b5MQK/wvPMeAepMXx5Fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc10": {"name": "date-fns", "version": "1.0.0-rc10", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "f7fac7378c0bed6df75e0e8347e26fe7fa921ec3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc10.tgz", "integrity": "sha512-rdI22gRheXE7JAVXEUcVFtTtsBj37zPesTMbRwbi/NMjK0BIE6i0n4SU49dSvjph2Sxaj8mDOBmHvRlLdzNU/g==", "signatures": [{"sig": "MEQCICpfgB/IHtjVpE6UhOZP7og0qgQUtNYb4jQKf4gcxnhOAiBO0vfr4IY6dGnMbyx6eVHooOhbrFryAUuaq1wqoLIS/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc11": {"name": "date-fns", "version": "1.0.0-rc11", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "3aa16448086ea6805e19e73fb4413483f1148444", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc11.tgz", "integrity": "sha512-BsBEONFix4Ef0N27ne9lxvFS3yE0jxJy7rcfc23NyGEvthh0Ud8VdNHbUCwN2WnOKlYGIF5IaK4E8x9GpN/N+w==", "signatures": [{"sig": "MEYCIQCNypFBqTGPIQqxObFMu07NUNOs5EgpCGqsx465Z/XqFQIhAMQnlNr0w+e8V0WH2eQ05QVkF2NlyeRqyIVtPanoR4T4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc12": {"name": "date-fns", "version": "1.0.0-rc12", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "12782cf2fc6783583a0b7e0ce5199ad9deb00013", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc12.tgz", "integrity": "sha512-MaQ7m9kYP7jPxaB0eoLpxJSAhqoDgDkDWzcL9VfZqD9tk7jwgQHebOtwi9hjNZzqqE6AA86k6j65oR8Mu2lPXA==", "signatures": [{"sig": "MEYCIQCiKSXcqgF9KFd/cSi/YfYuwMuvPNSvFNbxLST1rVIFgQIhAKOQifejA3xIC1cZEwvCQirwnEfl7CgtKw837YAIit7f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc13": {"name": "date-fns", "version": "1.0.0-rc13", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "d1deff9cc372312dfa169b307006dbf23b1af706", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc13.tgz", "integrity": "sha512-vCbVYK0D7Q1ygBRCeXAkmoXhX51+V2EQS/MZ2/61Laeq40gAZHAPccRzWFo8TZtv7hGdTeYD9IArBy+wG0mxDw==", "signatures": [{"sig": "MEUCIQDhfY7QA4UefF9Gyl5B2ROIzFCqmTGUKGCXJ2WPgZmJ5wIgGSssvYcen0SwfFVek0pY7c2QQEYbTBug/9sgnWscu/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc14": {"name": "date-fns", "version": "1.0.0-rc14", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "da2fc6b8f6e25a25d55d67844bb9fa10dfd08c15", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc14.tgz", "integrity": "sha512-/8/CVv9frhkX28So29i04efXPNM1XzYa+sZ+4GIda0RvZF/k+HxT4FXRjJBqSY1wWouYmV4gJmc0w/ChRkKHQw==", "signatures": [{"sig": "MEUCIQCtYhfgHBBLmJL9Pc7NXnT1elBFzzSM46SP44OGHGN6AQIgFLvHQ7/4Ki50mB7QtE5GmXexHPOyO1bQXSj/teZ8+tY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc15": {"name": "date-fns", "version": "1.0.0-rc15", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "6765382e6f3a1249e3ed8ac9087f9abbfd5a2baf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc15.tgz", "integrity": "sha512-jpYcu1UKD0Gh1/Yz1aTHL5h9WVmRHlWSqiR+3aZRf9ANCfQpXT8h+BaOugp/GrwSr/nRXEyAkmn7pRI2zO3tAA==", "signatures": [{"sig": "MEUCIFKFHkMQjKGVu2g/rObLVU5ppohYqbqmAdVqttdNb9VKAiEAtqV0C5E3hqy9nxvFV1B22Uys+8XzpxDanvT6Qh4mHZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc16": {"name": "date-fns", "version": "1.0.0-rc16", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "3332a2fced9be0725070aa1ce17863431937c680", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc16.tgz", "integrity": "sha512-pBUWEbo+lMJ98ApDJIG6/+vSulFLG3QQCckippU6DH82RGmfa6TG50MzNszoaRtKMUFTGxHoEY1D6cGbcyPxwA==", "signatures": [{"sig": "MEUCIC7IZOZmUAZnDxUQm01osT6fdjBAY8qsyq1TFRHwvWkVAiEAvNDT/j9lJQ8+8KANgNCsliXUkd3PnyYakMDPgZdg44g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc17": {"name": "date-fns", "version": "1.0.0-rc17", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "d3ee3489f4f22fe1dcca9c9c168de57b842f0597", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc17.tgz", "integrity": "sha512-Des2phJDs69g45rG1z6Wr8TkDDwOA0ILm06hLEQtvUWmDqSsfLotUk8IqD+bxWtknyvwd/qxw2bvj868RnatkQ==", "signatures": [{"sig": "MEYCIQDAfEWO6XXI3TMz/fGwoYRl3JVXhbO88f6pclXHFspnnAIhAII8dQKstbSuAj6Nbw8RBKDo4mIkTPFl0RXeI5i5cIWq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc18": {"name": "date-fns", "version": "1.0.0-rc18", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "fs-extra": "^0.26.3", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "glob-promise": "^1.0.4", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "03f5772c523fa6a7a539270194d5efe33f540a76", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc18.tgz", "integrity": "sha512-uMwXBM9nb9dw5cFA4E57wHFJZx5D86yKk1kEBAbJlrU1lrA72zBHbF6z95yCETVE9XcnU9lmp0Yt64DsIKqGTw==", "signatures": [{"sig": "MEQCIHyTNRxIIEP8vSiFMM2/jbzzsMoYOtMNDw/okHpNtGW5AiAUN2GXon/r3bfiKM0UokQakTepSFEwbDYsF1WKL6WGGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc19": {"name": "date-fns", "version": "1.0.0-rc19", "devDependencies": {"babel": "^5.8.23", "karma": "^0.13.9", "eslint": "1.2.1", "webpack": "^1.12.0", "fs-extra": "^0.26.3", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^4.1.3", "glob-promise": "^1.0.4", "power-assert": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "eslint-config-toptal": "0.1.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^0.2.1"}, "dist": {"shasum": "c07e41a3671d51ca9dd286c0f0dd663197464cf6", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc19.tgz", "integrity": "sha512-O0D3U3XvBuiwntxDKVOlF5Mxo7a7QKdW9nwc8LRwf+URzBMZXWN63F7D/MhEx8oLwrncMLcyOQh3oByL5G/P5A==", "signatures": [{"sig": "MEUCIGFahJhPq2pL3litKImKe9ZSxsWKKzcviXmCho1kizCzAiEAyDDnUeaE6FTumxPPhz8Tm6eBLgpsixokqhe8D+houRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc21": {"name": "date-fns", "version": "1.0.0-rc21", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "54f16cf421b3defc77a21b9ceba67f272e31a1df", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc21.tgz", "integrity": "sha512-k0jPrsGFFfT2xw5yA4c9I7AwDx2RsXc/cZS8yv7wOJhjWxPrxqsmAWpaNA0n5sdoe/KBm6Wg1aHSAvQ3a+rrYg==", "signatures": [{"sig": "MEYCIQDFiAS8TqrPoTozpq+ZVhC22RGMnR9fESh+azwyUlWX/wIhAMImGZ/qVUosbbsTMt7fWBk6cgo2/Xnrm9NQ6IrJqbbj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0-rc22": {"name": "date-fns", "version": "1.0.0-rc22", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "40d2216dd4fc2bc509d2b7810dbcac45ce814aa9", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0-rc22.tgz", "integrity": "sha512-wqNhCH1m2d26Jg6luh4AqvKbYs98rk4nHcRAOSdJvZ/1ObiJ6/xp9pEHGHGHfLpiYk40kKZQyDRPwJmHmppzyQ==", "signatures": [{"sig": "MEQCIA1a57YI+MaCiXr3P9h7dDD5gu2ajTaDtBVFeqPKsTMNAiArTYJE0mdhaZ3RcgVXd0znoTSAE/2WQ8B09Z+gMc06ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "date-fns", "version": "1.0.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "cb01b485705fa89ea532f82dee0594f24c6d0110", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.0.0.tgz", "integrity": "sha512-qSvA/Y8oaxE9+Gj6hqpVozQWZTkiW5cACWxd8lC0MolVH2ahq5FnigVAFsT9egsfc18GbZOoj7Tg7qIdjyUE1g==", "signatures": [{"sig": "MEUCICSznJq1jCZJcK7Yp3Ry6Cx+JYi7whogeoFnHdcz6NcQAiEA8cQ38tEtgWGGiqjmYJ+6hSYKB+aHCo17xiW86+G3X0g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "date-fns", "version": "1.1.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.25.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4de10a767cfcc77adfaf4dab4d95571639575642", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.1.0.tgz", "integrity": "sha512-VNT2ggCOGZTsKg8dtPvGlrQCBivlLpzTpBiBpkp145KXFUoSqW1Te87nrW2PCFlf+WC/7IlqjTbAkNUX1BrtHA==", "signatures": [{"sig": "MEYCIQDS3izL4ctHYyyO9l4UgSGaWvHI9Jg6IkJdO0AWgsE3PAIhAO+0oREOiQGn53OHJK5fD6wSIxgfsWFUztVDqODCF1Al", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "date-fns", "version": "1.1.1", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.25.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "59f8b27cdbbec8d6b1503314c274a7d802ca3087", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.1.1.tgz", "integrity": "sha512-vrBvgWRXj6Ja1hIHnbOl/txbW+WZcqt50So4sl0+FpiPclgco5PJVHDGWN2pm4oSLiIdgb4QAsrOmOPxfInmDw==", "signatures": [{"sig": "MEYCIQC5aIDYq+iylNYYWU4l6vQZI2Ot/1m3xaSsW7rqobqKzAIhAIaVUDe0SRz7CQARzpvkTsY2/Gv/0IRz9DZ6L6zfg5QR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "date-fns", "version": "1.2.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.25.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "336461fa09a40d56ea32efc1375fe5127013d3a7", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.2.0.tgz", "integrity": "sha512-1gQwb4I5vpoXCXFQgby9TQht3N+EhReEAzfCqNfRNlGzTk3id05TQc4+b0D0xUpVM9mDuwCuEoTE94LDV8YjFw==", "signatures": [{"sig": "MEUCIEpaAaU2JHUAZwd0p4yV2Ys1SWBQ4VGFVi/aHWIrcQikAiEAgQV5Zwz+BhETasxgx15TMOslGcpU2YlnSNOJSBnXBfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "date-fns", "version": "1.3.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.25.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4c3e62d02d08590117111ac9b252d967b962aef4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.3.0.tgz", "integrity": "sha512-3MD0QkVM6yrAPkuTYX3N+x0/Y4CqXaZzOsjCQsnM2rbE4v2ukSW5OMXZfXtUjm8DSpuv9Bgfc6pqag3rXWsqoA==", "signatures": [{"sig": "MEUCIQCecOogIn6Yh99F4vV7AsOR6qiOJg3Fe4+m5RgSbnjv5gIgJ31LSyCUw9072ehA4vo+8Q+TsJ+Ikm/sDMy2bpRCti0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "date-fns", "version": "1.4.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.25.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "b8bf1542c6e456ba1af837d4b029707e6dae2c9f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.4.0.tgz", "integrity": "sha512-xJKmPvGP/CY5Do4MMgAYd1Sx1a7Y1Ws2xaZFOV5vFLguG4FG8+AH2RJGNyB7Xh4ZfhCKndCSjL2r9U1WHxscyA==", "signatures": [{"sig": "MEUCICX7Dt88ZG9Q83X+ucQ8GNzGITZ58WEPCDB70d2n0z8WAiEAq+tFIMmGVh/jI+pIj2dGh6tt/IdbrUpeB9pjqSpmXFk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.0": {"name": "date-fns", "version": "1.5.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5a79526f88aaf008b3f756fc20eb942fb8e75e49", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.5.0.tgz", "integrity": "sha512-uQ+jmxZ297q251C/vCi+hPN1jI5uA6t4HWzPqNrBU0hCT43IZCPuFpqfgdvJm9JRXgejB/qgtK9+wRo2QxpXlQ==", "signatures": [{"sig": "MEUCIBDl5DTPWC8mhDsd6RjjPB2MoBLjqt/JhLDVq8Kf46mOAiEAjkvwzmvW/HBlrT60+067b0lypxtx1BetByAsC7r8nlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.1": {"name": "date-fns", "version": "1.5.1", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "0cab4ef0734e01c1d0ead25381657e413ac7b123", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.5.1.tgz", "integrity": "sha512-Erk7TSN2ZAyqeTPSqdzf/G7Xkk+hMstWcs2WU16L+ujeo+7jCUyKf5y9yeenqxWQskSN6PyvdMNipobn9W7SOA==", "signatures": [{"sig": "MEUCIGeJZ+Ur+Dxr7KM6pF3lqr5AHL37k5/JzLZ6E/KhlW2mAiEAy2wnfgrACoPmungRrvutWkfqrBX1rCRKNOPR5s73tz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.5.2": {"name": "date-fns", "version": "1.5.2", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "13674d961ee05c4f3cf773370da47a42b0ae45fc", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.5.2.tgz", "integrity": "sha512-Ys4JYuFzBNiKwlsm11kL02aVnK4fD+CbTk0RliHy7oD3GSk1opQuMFuVDW7iohcHyihZh3S8PqjKuGsfDm4xPg==", "signatures": [{"sig": "MEQCIHroSngtUq/ZyBgHRtpZ4JF0WvGKwAG87a7P8NZ2Q7C4AiBUrwKYdanOc8KIItfeFxBBANb833AmcABoKaHXBksUsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.6.0": {"name": "date-fns", "version": "1.6.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "db19c95bedf0cc295506c24440ac017498f82587", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.6.0.tgz", "integrity": "sha512-YF3G3T8sA4ifgT7mL/I7TRumzTx+mgjDXy4Og2oyIabDpvkkA32hQ+rfJ3NBFk/8WVpaiyV7Pw3pBudz3EKkCQ==", "signatures": [{"sig": "MEUCIA4z3vRthMO9tXeG0Cc3wJ1pMe1/wLiLMjRmnBjKnQcGAiEAv/+VwgyF5AWrakwF7SsRjVpFlgfblkiDrqDv6AqN5Cc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.7.0": {"name": "date-fns", "version": "1.7.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "4246874fede7aa1ca33a9cf1c3eb0b82282e8e5c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.7.0.tgz", "integrity": "sha512-4k5q+JwtFEIepzb5oJVVqnBCNB3EhWJmYgMYVzqHo+bRfLUud+2Du92wQcKNQLx1g7fKNGMO9wrmVwZ+Q871FQ==", "signatures": [{"sig": "MEUCIQCgpzfT+f0vbWPDSRkkzvbVwouMbUQCAtse/MsyZIj7zwIgWSWKJtdxvtMIVtzhGB+MBhC0HvmWEpLf4c0cQwRD58Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.8.0": {"name": "date-fns", "version": "1.8.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "cae4d6c2c6d5bfc606137c05f44dfe227a51d59f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.8.0.tgz", "integrity": "sha512-0gS/cuPRyEndH+d2pT3eNi8muJ+16yj8mpbP+FHFxhWNH3zF8w8y+T/7guJFsdBIcBe4h+01B/EGVRvq/M4uvg==", "signatures": [{"sig": "MEQCIEBwhemXjbLC4eqWYSiJrdofMxT1ZXQ6BuogGH06Z/GJAiBicnSPktjiegM3NekQPEXiSVuEUi/lwTg1HQ9b7SyiJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.8.1": {"name": "date-fns", "version": "1.8.1", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "11803e8493cba7b3ba7d461707ad5ea83e2eba1e", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.8.1.tgz", "integrity": "sha512-ocWXDj/rmXgx48DBpizsMvl50hlT8W5wuAnv+ZoCWHrwrjHwtbtm0Z37/tpM8qbxb0ZGt5x25/SDXIC8qDCzeQ==", "signatures": [{"sig": "MEYCIQDtyOiAPa4L5/ic81oSYb6E+LB6bL1844RZ+a6R6by25gIhAP5jytVdjpExsraxe2CrJQRdmP3Yrj5X+Rqw+xHGmIQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.9.0": {"name": "date-fns", "version": "1.9.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "43c7c32ad6a90b00d0dc587588cc58c6ee7f6013", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.9.0.tgz", "integrity": "sha512-6I9vV7hpo14hnAl/wbNYFAMYYfnoP0fya0iFnSqBei8h615U3llkU/D7J/oNaW8cofTZsIJDC3fBhfMLDyF3rQ==", "signatures": [{"sig": "MEUCIQD1jaqP4Yh3/kCXQcKhoqqGwD4mr+ChMdGs170E1/CiSgIgYTVWWaJFvUdI1+XoTpTA8xgfhWov8XlxfB5zS/XBsaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.10.0": {"name": "date-fns", "version": "1.10.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "fdb0d980ae744b7538f430d7fc4a1ec4e1c2234b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.10.0.tgz", "integrity": "sha512-WEJOcbHUpj1i8huOK+voiTGAesRUEfqnhAERY4UMfQDawsqO2KzcCoUDMe1+5udSJknD0/skGXqztARvpqRKYA==", "signatures": [{"sig": "MEQCIG5iCBTRgp47L/hT0gJmBbO8JdLeEzRIiXGVW9mTeIoqAiBODyVVbmefwGGz0DG+rzT53yTQiDae8tqV4ts3Iufh9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.11.0": {"name": "date-fns", "version": "1.11.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "38b6ae170f587898f88fd145470964e691dc7ae6", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.11.0.tgz", "integrity": "sha512-4CDwbksv98aUBSPNFVMs6Qnav9S79Ajy2GpPNiB4XmaO9dPG2YoNUpWYWIAJNEnmL1rxbz+NIvoMNWTGl1R/6w==", "signatures": [{"sig": "MEQCIHmWZwBdMgjohZCbTNsNjbjKsphVRI4XIe/7kNbGIykaAiAJFN9XV+GsbGMWasbua6bB+WDlYkOTO9WHBqiDcI8B9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.11.1": {"name": "date-fns", "version": "1.11.1", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "62b9fc17cb698952426e3ae481d07cbba386f5a5", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.11.1.tgz", "integrity": "sha512-v/IKoJVZQE3ora6epksjzdREf1+Ax/ATnan/mh4sVq52FfdNTHaO/ExUfbgT8lKvFIO+irt1eQY5Ig/konMCEA==", "signatures": [{"sig": "MEUCIARUb9BYAR2kb+P2uHodhaA9MT85k2QqP1Hevfe8/gJ2AiEAo9gNH46d1DvSS2k8XGeWVm1cWHK6HyIeyewEn0hxVPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.11.2": {"name": "date-fns", "version": "1.11.2", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.27.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "30a8136c52633688852aa66d6d043b84ffc749de", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.11.2.tgz", "integrity": "sha512-k6oOhQl7SnHy0+eg6qRWTcW2TsWzP3JuTmA8d3syRx+lUlVoEJCbkGKfvvb0B0TQM9orhrKIy+OJCak0r7qf8Q==", "signatures": [{"sig": "MEUCIQCr4VLM9YBCIyZjqwZ2xBS2lpjGFAkV1azByRTqu7V0IwIgGWmB2X+Huzzgxz4pisd4Yfc31gj2bddZghrsgHbu9Fs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.12.0": {"name": "date-fns", "version": "1.12.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "6e44dcc1c53d40446686feaf9398f60e5fb64376", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.12.0.tgz", "integrity": "sha512-/cgxoPHUxRlmX3vzTtZoRDFjs+k/d/ex7H5oZlCfxOCWd/GiOecznLT5qYncbOxS/ZjtnP3jsMeCk6C6PGeoBA==", "signatures": [{"sig": "MEYCIQCqjlUzMl3N8QK65g0Xcfa2uWon5Lixayrbwu9zo+dp/AIhAKRBEIa2Xw+tlOgwxRPQOSXOYuxBLyG+ZABhoKkTRckF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.12.1": {"name": "date-fns", "version": "1.12.1", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "9098b6c85c91390da3bbb6ce3222a9642dff8465", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.12.1.tgz", "integrity": "sha512-WkfbbITcxB80OttNBvNafk8yEZIk0VlxIc4q7iwQg95beKRS7LkLSw6CNaBRqBMPT2hOygq+i5hvEoyPcRXR/A==", "signatures": [{"sig": "MEUCIQCg7m110ZGvhE/tAykWBJLIni8R3J0f2kJ44ApqQwTNcwIgcZWh2o2tlSuHjqNn6Kv8966xpIIDlPPBQA+ChpRyv7c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.13.0": {"name": "date-fns", "version": "1.13.0", "devDependencies": {"cloc": "^1.0.4", "karma": "^0.13.9", "mocha": "^2.4.5", "sinon": "^1.17.3", "snazzy": "^3.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "fs-extra": "^0.26.4", "standard": "^6.0.8", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "gzip-size": "^3.0.0", "karma-cli": "^0.1.1", "uglify-js": "^2.6.1", "fs-promise": "^0.3.1", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^0.1.9", "karma-sinon": "^1.0.4", "babel-eslint": "^5.0.0", "glob-promise": "^1.0.4", "power-assert": "^1.3.1", "pretty-bytes": "^3.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^1.1.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "e69a387067bdbb5a923184866ba1b699715d2b95", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.13.0.tgz", "integrity": "sha512-GagAVo6monJwOt7Q4uguYQNR62Ml9xH5ON5OCpc8wbRCt5XkLlG2nUB9XLLYs+SFRann2gRPsGM9toBF+vJcug==", "signatures": [{"sig": "MEYCIQCQOVRqP8U/01Xo8i6kpH0NHNP0eME1yp/MM7u0EgZbHgIhAPRpQMOlwSPIClmxoz+NDVUf/idsiSuwrMgRBCtDTYQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.14.0": {"name": "date-fns", "version": "1.14.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "75555737b1de353654c75e2a5492df8bc665f949", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.14.0.tgz", "integrity": "sha512-G6L/xhh6eCC34DPXfjeAYylnEhQFODxiZgZzH3N1Mx/nU7lty6HJ5af2sIPY2EiLSMUkw2Q1ZXL1+38+2YCxow==", "signatures": [{"sig": "MEYCIQCsG4TTtzUwKcypmnDAvzuOSI1BR3B1Vke4EHhj8vys0QIhALGIbsUj1pYmW7vvnF8LIrphZ7Br0sqsSfcMT9jX3Aj+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.14.1": {"name": "date-fns", "version": "1.14.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7bea2b23770b7a5278d951e718b889bfbfe77391", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.14.1.tgz", "integrity": "sha512-IQuUidy/fq25rjcTfpxOd3lTIzKvmchy0HVbKEiIf5ZFRJxz4L35kyfYXBaP59rhH4Bnw92JstJjuCdKaa3+cA==", "signatures": [{"sig": "MEUCIEQCSUJWVkbAno0iVRbpaIInx2RBxTUxTrwurnSD2sBKAiEA3Qie/mZKbKLmnqi+i7PgXqY8Htt/DtsB8pVsakdC2zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.15.0": {"name": "date-fns", "version": "1.15.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "997954738d6e3b959389174378531ed45625e3a8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.15.0.tgz", "integrity": "sha512-/dLJURu/YLJDvGWqCS5M7WZ+GELqTohvQ7WOCZ6T1X1tjjUMa/t8Sf160bkJIDtqa3i0hbKrRCRmfeqWyKDTPQ==", "signatures": [{"sig": "MEUCIHMtvlSFhqFnnr/r7qS1mxXPvZkTeA1gVYUeHxRpzxHNAiEA2aVOKZ+K7Ztp5Oj4qo+/9Od7r3y50cc/L0XVy7Z6Cs0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.15.1": {"name": "date-fns", "version": "1.15.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "26b1f3163de8c9a71447adeb23c9bc40171b3fd4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.15.1.tgz", "integrity": "sha512-XhUmKCGh9pPFMR8AMBxw+GAdjJasGQ7nTtR+TWy1MdVu69rxtD/pFtjM7erBFY3xImj111Wk0kWG5rtoJFV5+A==", "signatures": [{"sig": "MEQCIA9N5ImANnlu+N7Rc3Cbtu8jVFhA7ZltdE8s9iIaylK7AiAO+skwZ2bSPDtI8//ou7n5Xz6vMPFg0RYSXsRqzFbNIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.16.0": {"name": "date-fns", "version": "1.16.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "1e1e7457560d18d9300947587183c8cbedd8091b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.16.0.tgz", "integrity": "sha512-L+t0BUjyTQiaZXHH4jZiQwC9etcegSUUy+wELFYW/HjzUa4fPrb9XARJ7huAQCJGZ7/eEmBvd1nTsg5FOo6cLw==", "signatures": [{"sig": "MEYCIQCC3UhteTEJK98jWI2RK2kV4mzisYRw8j6VoGLz08NHrQIhAMhCtWZfwXGsYf86W9Rlm/1c6s4fb41h/mXg4c5v3XeG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.17.0": {"name": "date-fns", "version": "1.17.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "f3eadcbc4937b5c617afc83417943df708d9f7d8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.17.0.tgz", "integrity": "sha512-FScTf68XPAyKaVmgsELEHur5NG21sBhSS3c3o/jbIVASQlZZduCmjGsgiIDaX3ejptCh/qJomJBpEVrUN/R0yQ==", "signatures": [{"sig": "MEUCICAgnk2iAjBgh3S6HDEfzn/VBBj7PZTnDlqrkI1D12ndAiEAl+8geWQqZw1jJERqyc4N/vcG5meX9mGOFyM3lAYUtN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.18.0": {"name": "date-fns", "version": "1.18.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "a004be802f6dd77d1763375ba5da5a4bf7c5ba38", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.18.0.tgz", "integrity": "sha512-F2nwQ56SASZWIeSce9y+4Gt4K9TbnEoThT2Cs7Ev9LeIHJLK+kHFWM20AddeCDz2wV1cIjPOLJWCTdqfKWTKnA==", "signatures": [{"sig": "MEYCIQCoeIWXMJgNMcnKAiWTL+JMtwCYl5MAb54w2dFe7SG9VQIhAJMF48QfPQEmNejNkdMDhsBjDWEOKyNrzez8611uO4bj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.19.0": {"name": "date-fns", "version": "1.19.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8faea32778af0b90cdb8c312e15d8fbd91eb0adf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.19.0.tgz", "integrity": "sha512-7YotUujo7LuVZLLod2EcdHqpBX4CleKi2uIrGtylomDZ1efqnRi1g90XBssebslVrnIHyiieah0z0mfsVMRVOg==", "signatures": [{"sig": "MEYCIQDRsXu/86FcuqRx1i3BIY6xSNn/pdv4z9uvDkTL2kUsPgIhAJ+pImTgYr9CocD72+2MmplPNxQMa4WkMRLd0Ijp81ma", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.20.0": {"name": "date-fns", "version": "1.20.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "632eca6cfd7639e4b9dbebd903db22cc684a42d3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.20.0.tgz", "integrity": "sha512-p7lkvLHb0U8AQ6JRksW5PPItiBiWkBJI9Lr75bz+BOYVq/mTfgaMmE5VOUaOUBED8gorxxGihKd49dgoiFjpQw==", "signatures": [{"sig": "MEUCIQDC3CqHzE+Kdgwuu7agc53pZP4miTIlRYB6s/LwoJsV4gIgFuwWAA+n9aSbjyQr29pv8vSvh9gUbaJWRzUORv3WYJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.20.1": {"name": "date-fns", "version": "1.20.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "8cc3e1588354e65d145729d42b94f52a0f48d58d", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.20.1.tgz", "integrity": "sha512-ux8yaooMMqhW9HTuhztBpMP/WdgDIvZxrbA0sdAz41q0FoH3pWjNZvNtScdbxAG4sI0WKqECAErh2QTINyVefQ==", "signatures": [{"sig": "MEUCIEmWm4fpfZWK9oQfG31DQKiv+5gTUOZkmjSJY15heHRJAiEA+se7ZZ6a8CdxcMYDIef46GmtwP7limlZ8CAC0yAPKgw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.21.0": {"name": "date-fns", "version": "1.21.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "7183c75dd0762e101f5db7fdbe3598b5a47c09e4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.21.0.tgz", "integrity": "sha512-8gDMuIHRPXdaE4l+wcha2xYJ5jKtVZVDwBxCW5n42Ha0PPtx+lwpiWX2lCflrJdqWK4hc+xcj5fIcAOgVrh3ig==", "signatures": [{"sig": "MEYCIQDlRU2SgEkwuHDiemsjWWtuPKKrkykbnnnfWMc+zj75TQIhAOnXs/U1tJcNd2aYvfe2BMXgLWnnG2WeXBcPTnf/0H1t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.21.1": {"name": "date-fns", "version": "1.21.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "5f5de347a88d575c8a09958a884f91502b48d157", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.21.1.tgz", "integrity": "sha512-Il4rfLBWcIPjJB3/2HYBkr020AGoIWaiV4uPRwJ5SPEtNeHcgpqhkO5n+KHkKc8RuSR4ZbicK3HllZh7cSikPw==", "signatures": [{"sig": "MEUCIQDaZCw9o5QfThbyUomPFzdIySqIwDI9x4WD4S41cQr5EAIgcFeBvkpsShqeHzkDD5EP8Mp03dyh4HzNMIjD2xIhIz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.22.0": {"name": "date-fns", "version": "1.22.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3199427130e10e3333af9350a8701e7e1c634bc8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.22.0.tgz", "integrity": "sha512-zMiqk5H9YqMhgnJJLMcAbhzxACMb4VvfS95ggVjveX1r53jriSZ4o0qLvfpMTPVb5rtSNbpGVIMvxs1KgCVH5g==", "signatures": [{"sig": "MEUCIQDxQq+c+TFzBWqhJmmNcGxtwwhoBfka6b4/b9KL5Z90XAIgK017baT3XlEEFJRkouuyxju1x6qgoVXwAGsw22nj2IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.23.0": {"name": "date-fns", "version": "1.23.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "48eecc209c44c42ed5283435bffa625867c11025", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.23.0.tgz", "integrity": "sha512-IUplFaqirRKNxAJ2Ph+tR+jGDgJ47VgnSSqXmgawZgqUZpKKwW5Xy/R2yIhW+M2smI6oTmCzV4LK5/4TkDAkjw==", "signatures": [{"sig": "MEQCIFMron9GSGvjJ/W4bke9rfbA2zl5tLDeIbYvCT9pRKCvAiAEgL21vtvH1Pq0sbxRgbi05SKCXSQOyXwRc8aLA/fyig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.24.0": {"name": "date-fns", "version": "1.24.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "e5d5945797cc2cbc46284dcb6699d3e5a23279f9", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.24.0.tgz", "integrity": "sha512-Wl0H+3YAFDyzfFCC2l4urpuNgaFu8v8+/0FWfPJtrYzShepbaXR84s7ym8lM6YsIc/0GoHC6RPTU6moXZAYjVQ==", "signatures": [{"sig": "MEUCIDOAgTxuhTXiSr5X0eu4ohIF7Z3jAe22awkuoKZmP26QAiEAlBayJg/R4ACfZG8PwveVTSMFLTxQjW+VaU8o0s1X0vk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.25.0": {"name": "date-fns", "version": "1.25.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "9af706bccbc7b76d796f0e93190802c204ba5efa", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.25.0.tgz", "integrity": "sha512-7MvD56WJOAVAyUFN3JLkXaQ6/HQVONPEWiw0giYcH/XySGHQN7lvhlklW9jbnSertm7C2t/RRmg20ySUIV6bog==", "signatures": [{"sig": "MEQCIG+vOe0m6N2pNLLfN5M6W8HoJ8Keeol05IsPobpd3hqvAiButY5iQGHnJi/0e8pQEFIJtjW72S2wprGmtgjNbyMrlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.26.0": {"name": "date-fns", "version": "1.26.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "e096fc5c464753f8dac412416c08199f3d289eee", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.26.0.tgz", "integrity": "sha512-BBz2O+GMPYw3MejvUv/Zm6ZnOvvStAOjjSz2qfpNdV4Ct6w1s7sEMMba8cRVm/Vre+IA2ygPivN52efqWnRQrg==", "signatures": [{"sig": "MEYCIQD0tj4XU4WugGrmu8+5wnGvB4CBtsm9MMzkRVaH5tSOPAIhAI0WaG4l9HfXwdyf/fvFopKCbMoVaPMlwHCCoZaFOcXH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.27.0": {"name": "date-fns", "version": "1.27.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "527ab172c8c3a8847e7801019e26dd62166a68b3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.27.0.tgz", "integrity": "sha512-2d448Gu5JNbnKlI/t6qcAWSo0xsl7IsYj56QlVqXitLgONmeNwA8EUg2BY96bNamq/76/MZ5lMbQPeudpXLe6g==", "signatures": [{"sig": "MEUCIDSRDQVduYfxO3HBFviYxxsnWCk8FfuY72pSe1wYKvSLAiEA64H4Znx1EeCnrQI4UimUT+TYwhLsTdg0pxTaJQa6HDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.27.1": {"name": "date-fns", "version": "1.27.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "194ba3c77b42d98e280ec1e5d6f094434b77cbca", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.27.1.tgz", "integrity": "sha512-Bph2yl7FqSVsK86lm0SmCQj9NMVefI6O9ZlnxIYXTrGalpWaeDpSFreSZmPilQJSxezjxLyz7xnL3D/dDYQpCg==", "signatures": [{"sig": "MEUCIASFjnbp/dZX0r0/wiYXrUQy0gsC6QV6gOTnqEMtgkObAiEA7JQY9MdVJWqftYmGIdfaa04VcpLhJjr6RoY/HdAH+Cg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.27.2": {"name": "date-fns", "version": "1.27.2", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "ce82f420bc028356cc661fc55c0494a56a990c9c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.27.2.tgz", "integrity": "sha512-Xug7qeZ6JVImJfj9JwBjaGn/xwdC4gIUAyrDfs9NNhfnxKMQ2PhONwuvdj+KVQ8D8udzqm5/6v2pfNZUi0AvCg==", "signatures": [{"sig": "MEQCICWt6B3ezO4Ve3T+knRYFx1V5F7BuUi6ifQqnvliSXZSAiAmUORu+31YpClB4R17vLDEv0QBoMEPIhawGtadzEkLFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.0": {"name": "date-fns", "version": "1.28.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "3b12f54b66467807bb95e5930caf7bfb4170bc1a", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.0.tgz", "integrity": "sha512-jLJKw/Tp4Iayq/okFnHldUxYdeyHnPL0hf86/0fFDX1MkLOmLPMRnilbiU+JXC1P9gDJmTVoogg874Pmt4tYtg==", "signatures": [{"sig": "MEUCIEfT8RAyIkhoo0raivEV4il/2myjGpmQMl1KKPj94prsAiEAqmdgfT9jGN52tfZZcwJab+okzftGawi3uifevwG4yX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.1": {"name": "date-fns", "version": "1.28.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "9e2325c77b1cb7da3a9fd9822ba52c188a4ce91b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.1.tgz", "integrity": "sha512-wDciF65VaFPEin1V0upeDllQvV/6UlLk/1zG3zRRynN42ZapPhCCz78ULxr8KpL8973a8JtfUO5+/lfUvWJ/BQ==", "signatures": [{"sig": "MEYCIQCp89LskCYr2XS6whAgzPP6ymQXPRM/j6ICbk4G5TwAtQIhAM3pcrYo6uMET/ACdBtZDSVv2F4gaiFiDYWUxk5Z7Ec+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.2": {"name": "date-fns", "version": "1.28.2", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.0"}, "dist": {"shasum": "19e4192d68875c0bf7c9537e3f296a8ec64853ef", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.2.tgz", "integrity": "sha512-V1T2c6fiy8OmKmezx1hn4aTth2cwHkC50I2X09rhpm+EC9F+t27zQjT1ywh7wy9fSO7HpFZYsMZC/nrFk9IKxg==", "signatures": [{"sig": "MEUCIQC3ueN3xkcEdKZE1gDhY9dUkvwjysP5JFC3V7wBy0R6ZwIgRBrjM3Zo9gwPn7ovzjvewrEggExVCaljWviCrIvXmEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.3": {"name": "date-fns", "version": "1.28.3", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "145d87adc3f5a82c6bda668de97eee1132c97ea1", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.3.tgz", "integrity": "sha512-2O3HvWOFMTQD2+frBtgeMlkHogqFGo/CMV9bXIyyMAypPZT9Ks2GV10jvOGafW8p1dIIyV0wlos4FOVvydychQ==", "signatures": [{"sig": "MEYCIQDesn1VcSLiL3hj/yLCV+a42tu+FZ+9txk8NSPyg4vMZgIhAO1Egd512IhuF889rn5WW7bHdIJGZ/Q1VmC+e8D7NlOB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.4": {"name": "date-fns", "version": "1.28.4", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "7938aec34ba31fc8bd134d2344bc2e0bbfd95165", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.4.tgz", "integrity": "sha512-m/pCCUJdkr2lSTsfKbgE5f0LeYCpEL20UNVwKxJGjhD2GwPNhSU1PkrOgutkDpJv5PpvZlIMSet3eWqj7nZJoQ==", "signatures": [{"sig": "MEUCIFUTpTJHULeUcSal67KSJK3DCKeeDeMb4Oyhg9acCzaiAiEAlc7eegOEasGMwlL6yM+tklJUSLpyasnYG4kPft5G9m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.28.5": {"name": "date-fns", "version": "1.28.5", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "257cfc45d322df45ef5658665967ee841cd73faf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.28.5.tgz", "integrity": "sha512-Yga7k933zQ2qRhjXRUzkyEOo4RNsfPZAPoD+EKfDGwAkr8+q61c66C+2so7Ucpm9SZwRKkWDL4PRmhaTkHOYUQ==", "signatures": [{"sig": "MEYCIQDiiOZtgKLn0XmsCsDr/6hNJ/kzXzqE4lCaNZifcet28gIhAKUCXde7hI3L4jExEfxJ/1h9kuIaCZHpvYyaUJQnodS2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.1": {"name": "date-fns", "version": "2.0.0-alpha.1", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "f45e477cf3414269d97fb1aae899035f3b308474", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.1.tgz", "integrity": "sha512-4gYdF1rDgv9X/0ko69pt+FgpQtDU5rgqZVmckIOhDicfCSTndwHMhUhLJw+pa4DlPdzIkEBtHg6L6VlQ6ueD1g==", "signatures": [{"sig": "MEUCIDNq2ZocKqb23UrJBXCdcHIMsRhH9PntVeck+bJB562IAiEA+o7prLZrAQhREAlp0Y7L+oy7iNfCgYaXIbrTQixy/lY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.2": {"name": "date-fns", "version": "2.0.0-alpha.2", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d1ad4284d93541e8cc4ae829466e0ec87952b7fe", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.2.tgz", "integrity": "sha512-/gw3HddF8M883RbiHWFPXn3CU3f/HQhhGWsm3/Xly+W/lTT57ssIC5juymDfQnke0SOrM/8aaaaGsRgactIhOw==", "signatures": [{"sig": "MEYCIQDAtE8fTmPmUh/qhVnl3LJwM3FbG/2u8AqFWpXgYYLBqQIhAM/w4AnXDb82RpqcR2PfkKDfcL8RPNzRyepf5ZFrT0eK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.3": {"name": "date-fns", "version": "2.0.0-alpha.3", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "2050873367398289e9bf81520538a5fb4e87174f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.3.tgz", "integrity": "sha512-ViilLHzE37t19cK8wLf3ySDXh5zt9euFZS6sKkgsL7/Hfzov2s1TkBhPqEYlVyLAu2Jxpo0acQzmGAzH2Y4uvA==", "signatures": [{"sig": "MEYCIQCCeaFQwG9qHuQXJeTMvvCZ4xxLct8b4tjjSBHorxA4WwIhAKQg0zJ1UGJdfVEb2iyaNdKcMnbQ2KwFF8aF1nvlEWH0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.4": {"name": "date-fns", "version": "2.0.0-alpha.4", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d72614d8629133aa6dee4dafabf305a07635d2cb", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.4.tgz", "integrity": "sha512-CIJv+gWelsjjjdmbIEoJrfMv+3T+Da8V8IQr2U4FXE7VEXh6+70JQJCs0gEWPTNoSpF+WOu2w3YqRpKZLhTStA==", "signatures": [{"sig": "MEYCIQCVHJZIkQyO9cHfQfSxMKVEojE6Gbn/cUNN9QOdAYcxmQIhALucvr5qg6eIobCKO4Z0hD90DCy5ML7Lr2NbbrrnX1wx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.5": {"name": "date-fns", "version": "2.0.0-alpha.5", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "a1cf13de8005c0511fcbc3a8c338a4b65b4edbee", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.5.tgz", "integrity": "sha512-iA+rZPy2EFMWjroAVcq63kfehYXhi0Lwarrjsvh2jFLgs4LMVJJklGmmss/IguTn5wn7krVeltUWIUXt1Ymp+Q==", "signatures": [{"sig": "MEUCIQDMxFYgaMNDaXfk4dRaVmNAdfN5n3CZJiX8m/kDIvTUuAIgSU+sBzgpGv/5WEEroUSMMZ9OtYLkF++YuoJURxIRasM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.6": {"name": "date-fns", "version": "2.0.0-alpha.6", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "49b1f982b8cf1cb628a1cb477ea8fc61eefcc296", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.6.tgz", "integrity": "sha512-opzHsc8yIeUN+ZNSEgFkC2abHzM1wvr53YRCjFdG6T173KNJfIL0s5jAO0VcIJm3uu6N1C6ZOpICzEqtv633DA==", "signatures": [{"sig": "MEYCIQDm0DQOdd1+oQ0jLnCpQa0xE3K5CnhxMQR/Rnmy/+s/sAIhAJsSPwsyCHNrKb8C2c5gBiTkqEaYXGqb+cJ6pC9GUAoJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.7": {"name": "date-fns", "version": "2.0.0-alpha.7", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "245ad16f95764eababfb2c0a41fd5d033c20e57a", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.7.tgz", "integrity": "sha512-TrWCl5daN+rdOjg72LQE9OVtAz3FgIDUYoAoSQFHdRUAx/vggWvstDuSBeBlwqf2uMBrtWyY5ZB1NsbFOFb4fQ==", "signatures": [{"sig": "MEYCIQC2mYQ7VzOCu2ZmQRfmEz2g3/+zM4S0hWWXmZPGZvSx1wIhAIj7U40IMq8FrqlGVwlTHJwaWzI/O01gnmB+eJTPkz6D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.29.0": {"name": "date-fns", "version": "1.29.0", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "firebase": "^4.4.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "12e609cdcb935127311d04d33334e2960a2a54e6", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.29.0.tgz", "integrity": "sha512-lbTXWZ6M20cWH8N9S6afb0SBm6tMk+uUg6z3MqHPKE9atmsY3kJkTm8vKe93izJ2B2+q5MV990sM2CHgtAZaOw==", "signatures": [{"sig": "MEYCIQDqVg0+OC7opwByFFPd6W4TzRBkGteTe0B2HL+YmPEjHwIhANJJxn5nC3C/CQm3a8h2hYvJcGBDoKZ9jxx3MyRoFMFl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0-alpha.8": {"name": "date-fns", "version": "2.0.0-alpha.8", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "^0.36.0", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "a62bd76a8f38e0194b2903245e9cb962b9bbe624", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.8.tgz", "fileCount": 4015, "integrity": "sha512-htJBDGs3fe225f9jP8cYswQfiygcaFuNLlEdAHW9R+ulgPbBNdZopjob212ZI2jDkHr6Lod5e93nijVP1SZEAQ==", "signatures": [{"sig": "MEUCICc/jW3p/KvFtEBCD28/NCoGhublkKthSLg8wnQVDupTAiEAtNqQrbOZdsMDVg1WAVJsWaQvaVDu+bJ3WIWitTBEJy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6840786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+uhpCRA9TVsSAnZWagAAwjoP/Rr1Ll3lLIqqxmrqO7E3\nGCk7RMMlIm4oRs6yzJQhfqVBRvYhsnEp4+xJU09KClLfXVP50UBx82VWDGqQ\nCdtqwUtAfqYuVRcJfV34S+UuKX7OC0UTRv24nTRiGYTdCg2Vi1uX3vC1CDK3\nxk8GfkyGPCmB7XTZGnrJ9qTj0q4zK6FR871m1c2gP5GkWGJr29Z/+1NkV7pk\n2aVURHsVTTtx1KtlK/ZtGgzfTbPKkZ6iEDAsBwd2F5GCfTdiOb4nOc1J9uVt\nb49POhEJjt0KMEiKpXX7ZcfgrynWow+7pBiXKDqpTbG0nvZQgMZL7k60j30L\nd0WbnL8qqYaRcGHLtZcfTJdpoqu+ic9PF/aqFOoS31M3VV4Bc1G2oP5WK7za\ngfjT++Qczw7w6sMghpL+7nzWu0JEmO/p3zMSyua24ZOPiN5GgazC8y8F4dsf\n2Y5Gr2GcFyyeeyloNqniM10yptofm5O3lAYitMvOD9MCBzQvkW8YsrdGhjQY\nBjFJAT0Vuu8eXiuK0/Ee6+OQ/FXuMi4XXZgCWpfFJESbTsDkCpLAPuqD8Yts\naTjG4pmdQtU827NWyK+IGoHRrH7JldTzqNPXYXVAbu0PEsefDJaqSAEHIlrE\nasCT5qGjH94aGnt2h3hvy479NAbLZfpNSWMI46uodTd8hNPWNyf5ILMQhZ0I\njj5C\r\n=D5QK\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.9": {"name": "date-fns", "version": "2.0.0-alpha.9", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "1406f2b57107781cf02ad84741a9013990f198ca", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.9.tgz", "fileCount": 4037, "integrity": "sha512-3wO8K00f/3lgJF/x2TFip3KtYKePEutby1BM3aDvC+azBiYhr+RtZyWGnpysyehEYjfERLMFwTdFedh7R3F0Sw==", "signatures": [{"sig": "MEYCIQCuBAsqMYBhUEe06HSADP0LZd1QCu9GYVFxRxC+vcwbIgIhAI1uG3eE+rsDdwm6Knbr9owkKHvtIw/yzWHlbGzaUE+z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6870389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbB7VxCRA9TVsSAnZWagAAqtUP/1J4sCOiDuLkFp6mZyiW\njPcTOB28Uh+H6+v2gujWufrbGxddrCv9Lof9Mt8bZgPqppxf8lWe4jfcWSI0\n9sqg0qXXcQhkCZF0RwWIN3F88/n3CzE9DhvvoSIrxVU7soUYG3k8slGZHHRQ\niKhbzYnfza7Ltd27L2BUgjFwxE/5XgoV6aLu1rvjeeLYslE0mVr/Em7B124X\n48bbmuUyysXb0FlUrxgUCnWd0kTPKa5JGjqUnGQsWBEC0HhcFSsXoCoX2aYJ\nxE/501DvYDQ3tiZJzZarLEWOIjvIDFkW7w+S1WiVD3SvAjJaRA8Cm5SiTg4C\n5k3nc6OUzYAetM8BPioi1zvLoH6NCKlqKaMJuz+YhvyLdRK5067S/Cj+R37d\nME5scZbzEXLCbXiYARbHqbNc4ZdWN1MHnKRI50HMha6F4S3lQs0FU3bNdguv\nH6dQJwqZ6AsjyBnWViTxRgBV2cz9prmswNd8r13qzc1cq4Si55hjaiNaewRZ\nrM/pe32y0oZdoTPXHTPA/zzCoZg26vi6eu929Je9G5Wq35OHha3jhuW/jw98\nnRiwGZviMpUct5+muUJtLpfxN6gtDmM4xZo2QEr43VlLPqkg5hsuPZ4EV6qA\nio6Zsoa6G7J/K1NcSeRMSO9Cgw2fQiVr4VFkoY6uNnJHCi6U1SwVNdnWhh8K\n8ftE\r\n=wbqN\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.10": {"name": "date-fns", "version": "2.0.0-alpha.10", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d334453a4d8e0643dbee2d70436fc9262c15da80", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.10.tgz", "fileCount": 4028, "integrity": "sha512-YcZJ4TM+7hs9VZ+xsE/rKQdmv2tay6679TMwpJW+kAZ0rPSxghX4vy3ZT22rH4OTJ7MZQbSPYZU8daU1fNykJQ==", "signatures": [{"sig": "MEYCIQDhzumNvaL86Ls5wTRTHNJ4xgj3bh4XUGei//KDx0ccFgIhAOoj9i0WoYwZT+usg4ukt9HVezqj98zs0oWkce6/X7YM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3913034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGPXUCRA9TVsSAnZWagAAULQP/ikXqPXs+UkuFNIxGtMk\nVuHya9EPdBkFZi4fg3DUhDcKbUcW+Um6EPncaq2j5SEtPLjNBGalMuviW77N\nZDAiFMnBMWBVS8p2CzTqLQ5/YkM0M56Ghh6kX9IuiZnIcHK1KJE77n6aawuC\nRJfns71bS2QxeXmKk2ZGheAOh8PDZTopEx/HL6haCWTePW2s1B+LnES1+Es2\njZQrfZ+uLhCOc50zriF7URGJAuj1IsS1hFDYn+GAFbotSlAN8FDpX/fjhI2R\nju/sEOJqT620y3Eom3yN3WLAwIY8pKy0h9GE5TkBjrKPtUooxTAokhUR2EbJ\ntXHqS1XaXMriuxvuzDXXaowyIkgDLZvKTVEXSnAmXzp0l2vsIaTlswMq3VYc\nGjKpPiFwznxlQngYBy18f68buni/WzmKgAFtDE0bBlK+RuYEA80S0vyiqs6+\nJwn2S2nzJbxSZtFMKk6TkkpRmGVZT1ZuSoe5wxSQVdyw7LG6q29Nq1eJa+nu\nan9KzkL4mydMdxDYzqU7HmFN2ey0Gxd0KRsE+pSFM5jR2qbvVmb9IJY5YrFL\nfA5R/RZGVSBpDCQU6LBcWJNa/hmYFqIt4JrMCt4trJX7znydJRvJNfjL4rLV\ndgqsgj/ODBRGowdT0WFnLNxvhYwhs2HQheMueig5sG/IUwQ4MEeeWtspPMQc\nbAFg\r\n=ADpY\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.11": {"name": "date-fns", "version": "2.0.0-alpha.11", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "586e855107dc4717ba4d9f2c41cf12c3b72512c0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.11.tgz", "fileCount": 4050, "integrity": "sha512-4fYUCAZw8dFBUdKzTjUbDrOUYD5J4H4Utz0i/DwRnf9juF06hGXGCi0HBu9ETAk4ompjFWfBzn49mcHgaUh7hA==", "signatures": [{"sig": "MEYCIQDrV4liD4w8qycoajherpOgKnYMMfwjMV9V7/sXkHJh4QIhAKsj9CagEqrAlVBQNEvXAsOZl/v7elrMCj+cJwBaw9KY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3958644}}, "2.0.0-alpha.13": {"name": "date-fns", "version": "2.0.0-alpha.13", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "a0fd723c467b5c5ba98bc9d7fe1beef5e6a4d596", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.13.tgz", "fileCount": 4050, "integrity": "sha512-XeAM1EbqiFZloEyEkJP49dMTT31tb3EJYaq/WRjE8rk7vgRLhkPN7U3v8hM6OF2D/NfF2+EyTKErGj9xX7repg==", "signatures": [{"sig": "MEUCIEE1dR/Km2xr5JTosONcFN/QKo5LGiepw3sdPvHNW6ZWAiEA2ndy8jJOiLpol+ke5ryjUZbZeXAux50FDJ+Eoip6h2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3995484}}, "2.0.0-alpha.14": {"name": "date-fns", "version": "2.0.0-alpha.14", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "7a8398a4160816fc141ca8541a6d048eddc27ddf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.14.tgz", "fileCount": 4050, "integrity": "sha512-aGIHb9A20BJ1Gex/TI3D7h4xaA130QdlZjilA0k7tLwwPiWG40R57b+k7KWakPnPmodTRMeOmsIjPBpUO+sRjg==", "signatures": [{"sig": "MEUCIQDh0ko5TzxHP9Kk7IigOcGNjtvPJ7ZFKaPiQTTu5g2HFQIgCx1E9Yf3so2ZTLCTd2Vq81TH4BW2yBBN+CHjgxAC2EM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3995560}}, "2.0.0-alpha.16": {"name": "date-fns", "version": "2.0.0-alpha.16", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-benchmark": "^0.6.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d249a6c9b799252652fb9e3f25460eaf9de86ac7", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.16.tgz", "fileCount": 4050, "integrity": "sha512-369JtF3rZduKzpGvlpSKF1MOla3clPrefOAEYUajUGFxfoYxsR9wTViIPyTE0pGMvAv3eqN+sjqrLmAcGsSriA==", "signatures": [{"sig": "MEUCIBIa2x/btoa5gYPYiak8MdSLxJfDvGLxepoRz/y89ydrAiEAvbHozkhBVM5eNSCk5uqDKPE6zyu9S+IOH+dM2J3PzF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4000469}}, "2.0.0-alpha.18": {"name": "date-fns", "version": "2.0.0-alpha.18", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "7d1ddb9067821dccf6fc3e951d457363427562cf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.18.tgz", "fileCount": 4122, "integrity": "sha512-Y/y5rw7tyZgcjKa3EbFy2WHyU3PyhMYOwwD4Eo9ydfIGUuoBr5jMfzY6ZBmI3O3seukaCOeqFgZNqX0c5RvmLw==", "signatures": [{"sig": "MEQCIBFg9sR1Gf1trMDC8mV5vlm02TQ+jPeR6eZwcJI6Coz8AiAh3QNq6KrJZOPo8z/r3U6I/2B40hPzwNOgNOOBItabUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4149229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbpK5cCRA9TVsSAnZWagAA6woP/RkfTwtQe4VbbulF4mts\nWLt6WXI7l3fuiPdlFRCVheqzVJ6DrbnSB9SYT7gNPSHNky873V+30oJouTYW\nwZBas41EDWSEjSBB8MThsVKRi9b+r5f9tLSxUMd3zUQs11sVeJjIjbBLKF5a\nMlgpGzacwcd8Jft38HxILrNjgv7St9cxjRg0yQWgAAUvLWJ/gl3ph1ulS4NB\n2s7J5kA+vUC7seVJIASIMCEYS+vjyuq/2gKZrNMqq8/OdK7kWjieLDUrldWq\n+JYNiTp86pysfobUj/3uCZ1zGq6lijUlpgX0VrIn+O/Ajz9d2HrEy569VGxe\niD2VCENLFZaySRlvNjbOYThN8uhcX4emSbos7vEJ+j9FfclAG5ONRi/GE9pU\nOrV9S35KiHGn5lerIcslOxn7WZaMjijN7e0TXGPWZSJuol8zbbaNFYGzzCYf\nefEHOwQu2VSnkOT9+UOcH3lnhrB+B6O1p57JplguGCQiDGabYjalrfQFdRu7\nMtunng8bTlpcHw9KmFlABtCZoahsbxGHMgTGoBISSmmlqGvrBSf0myuxGZB9\nBqPHI6zEEECz9IGuOmBsH09ndnP/MwLO+k7NODwQB1jg8gO/yRnXG4bpjRW+\nUAXa2bVw+fD58LEyI216sxC6gnOQ0yTCeNc1LV27ECK5XNJ087F6yCxJagEe\nYob0\r\n=/vYH\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.20": {"name": "date-fns", "version": "2.0.0-alpha.20", "devDependencies": {"cloc": "^2.2.0", "babel": "^6.5.2", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "standard": "11.0.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "6506acb99869913b2fb7900c14cc76f88e0ba057", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.20.tgz", "fileCount": 4233, "integrity": "sha512-6QqnkCeMBrlnGUXKrNMvYbj0h9Nv+51hNxwg54aa4Z7JWVycssKINjoPbc56tV0qTQ27hWTLz3ixi4Pigpq07Q==", "signatures": [{"sig": "MEUCIQCo52+3RDcr8IJp9ytEBEZye4y0hblL/INhOxSEmJ2qegIgVPxJ/ddr2dsRH6fQUPDE+HMuCdYAnuFEW/Ue341cRmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4236468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrzDRCRA9TVsSAnZWagAAtZkQAJysJhpy1deQBteqx4w3\neRfoXuQG8XAL5VRa4PFChny8KRkSB9DaM49gqi7jCMPz3ziCaKvLJm8dhurt\nXouwRcq+STRTVo+ZRkgvFNUiCVpvQ5jo3MHkUISyvPupuC8zAh+r59MVVpBK\n8YrhzlaRfXUnOAOv9DhUposmc10B55arKljVFfEd6V87PA3DYePl1TBJe8o1\najT9d8p4iN5mjQ0XYSFWCYg/n3l5/KQMJq1ezj9ijXQeJf/EpBgKR4K+0mUe\nTKcne8/EbtUwyr98CHPE3kEFY4OlL6SZ7ZwPiXgEc4KuufvgKU6CcalCtZOd\nFMw4Ts7QZeQwFwW44vDT/uQtLRfHQWQWGJqFSfzUMU+V2Uh/2HqumK+UA6HD\nTN3NdPYIxO9Mzd/2dmG6lBDzLuGcNxfsS4ZypYCJpQcnVUZMMl8cmf9qNNh4\nuxd+mdOyfZooZ6pLoIQZTHGrsMUSFo9Ob5AR/8PTvHOksfb9T+fBO0uWdl15\nJ9TosOcJRCWsnXOfzPutENC0+Xprs9eALCgTQOtVgruS7G9Dm4//b9IaIx8F\nvi8TzTC78EZyuoiRGkKlqX6V/f3uZ/y1srIkhtWGnur5yyiQ9/tcj10FE34a\nqIrNvB79gjOIE3NMFBglOWmB1GDn0C+WKcn1UQ6lNMIPHXX/L7DsjEWwCtUt\nQYHg\r\n=tPHT\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.21": {"name": "date-fns", "version": "2.0.0-alpha.21", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "prettier": "^1.12.1", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d928c3d595d9e607701d2b3682bd70f03f2efec0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.21.tgz", "fileCount": 4522, "integrity": "sha512-QL6ZzDhQVAGzYElbGcMCCwDhobclw5jIJG9oD9n0kA8+PLNqtP/V76vkzUXvOiXm421aAXjLFgo3o3cq33cdew==", "signatures": [{"sig": "MEUCIQDfTQK9n6Wv2z3CLMv5MTxW4oACZLmQChIcQBeBvDTTggIgSi8QitKPPEFwNV0T2mREh8KVy7TcgSFre5X8cxxK2gA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4275020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtiwvCRA9TVsSAnZWagAATJ8P/RbIzS1eXzrwe5oK90hL\niZE1Nj9Br3u9ulcbnEoDd2ZEre2+gn7MEe2VTSmq/EykeDecMJalpWxd5MNx\nUTjf8qsfbtG9aQeyyQkGZB131/XTHA86rS48WqNk991Q2nAaPJGtNJBP4vyU\nynuLFQ9VpvRjXO0ohLsDqTd9zwORXqYc0ctgLoVXl2SbTkNC+6G9/nfU4oE+\nGqZ5vFj3dtpKSyls8g+EaRDpAfWxqhqDUujiutSPPGmzaDvnNFOeKaAnh/65\nw0/KWcDDsFba3tHjqEYZg4QYK68M+T3vCRQwCnTW8ojeHln7Me3QWmBpr0B0\nydmQk6r/A7UxNu04CWXaaoZsFoXZasUO4Fjp7pXr/MzRBfdU/vHywnxZmpmV\nKMZp5I5tatFwLoLbD6QFQauA/+CvF1eaS7SUicXmJOm8L1UGooT3LBcQu3fe\nKtRcsTaiZOV2TwLi7AsHSendmj9mwY2apbcjqGyCGIwyqU8xOp+OHJptj8ow\nkwsuLlJLQN5Ne/Q4EfhMvnt06cpS5dUR/yjGhRNPXf/Ef9FiyjBPDkq8asql\nNsdJIoX4UKNrf0Qf74+sWE807grTvveMKasPbNEGe0pLfbht0iV6QffMraoU\nAnp0D8fzVAnOJgTE3onVUPpFQ9zdv+OZv/PfL8lw/eWX0s0EaYYdb2Pp5Kzy\njr3G\r\n=5Nd2\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.22": {"name": "date-fns", "version": "2.0.0-alpha.22", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "prettier": "^1.12.1", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "c65f5dd7e959c59f8ea5591e458afc76a2a0d8d0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.22.tgz", "fileCount": 4526, "integrity": "sha512-ovlz0I0OFhyn9bgTfqFAUvJvo8VS9mUuOxZ6cwKwBjSx42lMpXPGolcINYZoTNAG2M4YJ3QoApiQQljQEuy/Uw==", "signatures": [{"sig": "MEUCIGePvHkZgoGLUnDz/eqQJVBO/sdsUEqaa9bX0aIZwihtAiEA02e0dCLLreQysHETqdY8AbWE7pEkWm/rToqyUbpwzdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4320861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvk0YCRA9TVsSAnZWagAAUYoP/12qjLGUwV+zRMQl5JI+\n1Jv8l9DpLv9fgAJ2v34mm7t9kRsPpUwYnUT51Zd75KeCpG61kozUpxz91sE0\nyJ2zvxNxaKMrJaz+YnoGk91vYhr2J4IAlNN3Re0dQk2NHc2IwHYn+mJcEC4H\n8XEdtk3E+N/M4Fek4zoV7d9KKtOrBWD6QhzJH90cT8E2P1JxuXgfy+FpBsoA\nA/VJcdMvqnflKvlZe1CUQ0v9NhYVxztmRDj0wrMPA0KP2SDhOcVwkZpU1lFk\nkepJwaofX5FrfokgZrRPJ58jzxrGxC8ImP5b6YqwZ/QSLwGjjOk7EkYrHdQR\nqCK9yHm/1WMe+suPv8eQeykBIJJEtyfzE8dgu2trP59eseRm5kYv1kEDv42U\nhMQ1S9RUntUGGwUPTi1uWnSahPwIHVFmiKkVzqK8zX4RFXMDiQw9SdcBadE+\neuA47uyHkoy3VOH7lGZimMcNbxQjVeAqEfyvzqzWXUDCOdw6RRS87PjVnwhe\niz9up0E/pRgm5flG3uPtaNx1MoD2PvVjDC73wKbn9uHSk25EkGCKJlRogqIM\nokf3b2Gb7yH0iDP+6ew7krGaISp2mqni6+MjyV+nDAfOGUUjgYT2RK1FvCet\nr8YSqmw0co4y46ofJmYw/s6eA0Xt8urOgq6QVuD4asm2/nN0PwZhCoeSDtad\nq+03\r\n=c1Ol\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.23": {"name": "date-fns", "version": "2.0.0-alpha.23", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "prettier": "^1.12.1", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "d3384f4628de78a4819fb3292286c8fa77cb175d", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.23.tgz", "fileCount": 4548, "integrity": "sha512-KeTgG5mbk+o9UbMXqxOvW+G46mWOWqJdjQDOP8t82E6Q28oH2ObcBvdRQbNFR55cun4QUg359PeE/KDBWJlk9g==", "signatures": [{"sig": "MEYCIQCMAA4oxeHE3mX5Eh23pT/ZhWOtvfFDLo8Yhid+UgflsgIhAIIA9oFUqRpa8Sq10xGcTqy+8LbXYJ80BIfOx5sbYToH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4357373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyG90CRA9TVsSAnZWagAAP/AP/AijVLVRw2vEzPshOf+q\nrw+6e1fyF3piJJDkm6m2d+qdARb+KyXqNsL4/PAIYaJfERE1wyRoQmnuQaRr\nCCeZftkxq8crAwfsaH4abObG5+E9aOAwdSYecr3L+HDn6EiG8E3Y3+yyoSrZ\nx6vyx6MuvmINUv188i7g4WAom0rVplBmG2dDQ/mC2olQzQ3Jm/zTMwGiSQRS\n70inE97fIcjAHD7NEV3TXtk+RiWcYMYZ9RAOZh0+rPFfJWTz+yrVfRCyuo2S\nHPdaYfTIkx1+MXx/RdDQ9rn9b6WDei3+giMrSju9XYUdzW5BqAuplJoBquNi\nN/r72fo9oKefSTfZ6rswjf3D1cGAmL3jnKTJR1FxXYol/52rW7PKbfKsBZWO\n+QnZe8fQ0maoNXothHWZsnzBFyycMQffuq1+vSauI5WhMW6WMfyLpkBLMUs2\nd8phN7dmfnnvXKHasmbdV1vD3D97qu7PuVwH+JFZjF2pbNP+4qGHH7rGKTDW\n7wvRjjwClF5jM+MjNXoV7C+JGKPFgefz4Hcb/Dx+0+jXXtO9ncW7sz/d1Vdt\nRhi7Zx32R+O1LAgLrrWbcB3zXlM9LhAF3SSBRrixLH61rz1L6qm3caHaujtS\nQELpq7K1lmMggtOKaTjoK1Daemf/JPHsZ1SC/FZGBUBNQ9qX1MIEtHGUsSaw\n5fKa\r\n=Xs3r\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.24": {"name": "date-fns", "version": "2.0.0-alpha.24", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.72", "prettier": "^1.12.1", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "2988c137c72275af29d7d21cc53eb52b3a8c2586", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.24.tgz", "fileCount": 4590, "integrity": "sha512-jpLzKHKSq0nTcZ3K5ZnTelxWmUwPepuoEaXkATwIUnc1tc+/rIooAvDMR+zdAGnwQ35eVWiyn5dikoLHjhEYeA==", "signatures": [{"sig": "MEYCIQCk2CuuPXrsAZU960QjiifhixaVxSps9BJEmsQfHYAPIwIhAIR8HpgXX0sO5v5DQOBtRzZzmZI/iTD5zVT4AQgHi0WT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4404276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzvGFCRA9TVsSAnZWagAAo7IP/3e8Wt8maAPWcWgtFkgQ\n7OSbmdtv62+X9jtz9jyyYysy25mIcf1EmFY2LCicYN0ZIh1eU4NFRCOYIgPj\nUajV+5FQwgX8DXXQBOJ2SjK7Vqxp/7a+7QtPwjdqtJMhLFbMjZg3HirXy5cB\n6fE2TL9wUbVy7WvlRBEcO52mcaRGdOovelM+qHaukoVv1n5D8WDN0f8fbqcV\nR67BRTQA9fCdFqVliXRgIj6Ypdd2nmAnriTJ9TCzygmiORgT5FfvAtt6AHCx\nihoU4AwWebmc/sGGq7q9YsmssoPoerHns8WPu1HbotulvePRwQ2AbbPuzt2O\nC1fnGlefN4U7DEDIbaII7SiwUglyEEaW+nKJY63c4tLf/qpvFwVPIseuCUDn\neiu+QTNculH7M+s7uyDI3ocVjd2NolN77P2SnlCJWMKPaJq3bBsqBKhNJLPG\nUK1j9STgr7k6oveJP+34NtMDySffyW5vPKq0N44dGwLJYWitVS1G+IivMBgX\nwUNWpyMAi5Dio58VQ//829bCvItvQuG6W9SLnxx7Y5LnP0hqpEowTEuPWCg1\n3b39E3Sn4m1PTcmT17K1aHier702TdrN/OzhaFM7YiytP1obyt8oMg4bxKev\ng9Q2Mx8FnSkeJLB8wvMzsLYB1bQ+A11GYJQg7GElForo0c+IDG9SNexL5Yap\nFHNf\r\n=ShuO\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.25": {"name": "date-fns", "version": "2.0.0-alpha.25", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "651a5d1f59a01af6cf0371e39b2ae29df5d521ee", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.25.tgz", "fileCount": 4662, "integrity": "sha512-iQzJkHF0L4wah9Ae9PkvwemwFz6qmRLuNZcghmvf2t+ptLs1qXzONLiGtjmPQzL6+JpC01JjlTopY2AEy4NFAg==", "signatures": [{"sig": "MEQCIEPWuoMOocwwRL////Su5iOHF1yln0YXLZDBLtYpXIr8AiBHzS9rtHuD2NzuXLk6RqZM1kARHdrYRpJab8DQuqeG1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4492601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1v0QCRA9TVsSAnZWagAAeu8P/0ePWlMmFTP7a9Y5J3yK\n499gvZ404PkCT7DXRySjCNWD9HsEhDnuroEVkk9hns3rxe3AWDd20s2GpOUi\np7auqg3RBUnW2UJJ29raVJdK6Uz6TooQY+VdJOHETjKI+VdbLPcVbbfz8D/A\nVy+2DhS4ZbFEiUYKFTvwlScYbMVS/PwSN7J5QXkHqSldXOljFXqk4PBZH6OH\nVwVD6T4yx4quBLoh1k6KhI8XL2bAoL6ADJiQ6gUM21sZzCiuWiMSgCVWl6Ne\n/z+UkF5ro7Mn+aBdzXxlWftu6wcQHBsIXkiieyGJzzWTm9p4THj9jvR4h+Th\nW15ihnXjV8VX5E9nejdwAvpAcCOkJtlRGNSqAt2pDL3bP9U+4O0vy1GcsSad\nFrqiOL2Knz50CjawM1IX5FGKu8uGAEhw4WtS3pFPoHFRP3J/jTt4zLaRCWhe\nnZJy4qFpMYGy9whA4Q9gXBILkzCyygz505+OijursCS0lAVUVYiAJaXhrZA6\nGRA37B+eGPkX6ArLLhVTtolYoNoWAg6SN4DkIjSChy8WKyPJWF795u7J4RCx\nxJbqD9fgfabDfaoE8YsI5eZ2aY7gHlMiDzNjAtJVpgfwQbCGHFVDsn8Dt1Pc\n5+/+SXljCgHx9kGzYCsYkrOTau+7DSa3VVfchl1AYKZD0Mu+3CxE+lGUqLAn\nM+2S\r\n=yHkD\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "1.30.1": {"name": "date-fns", "version": "1.30.1", "devDependencies": {"sloc": "^0.1.11", "karma": "^1.3.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "moment": "^2.17.1", "snazzy": "^5.0.0", "webpack": "^1.12.0", "flow-bin": "^0.36.0", "standard": "^8.6.0", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "uglify-js": "^2.6.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "jsdoc-parse": "^1.1.1", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "babel-eslint": "^7.1.1", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^1.7.0", "karma-es5-shim": "0.0.4", "karma-benchmark": "^0.6.0", "pretty-bytes-cli": "^2.0.0", "phantomjs-prebuilt": "^2.1.7", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.1", "karma-sauce-launcher": "^0.3.0", "karma-chrome-launcher": "^2.0.0", "karma-sourcemap-loader": "^0.3.5", "webpack-espower-loader": "^1.0.1", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4"}, "dist": {"shasum": "2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-1.30.1.tgz", "fileCount": 808, "integrity": "sha512-hBSVCvSmWC+QypYObzwGOd9wqdDpOt+0wl0KbU+R+uuZBS1jN8VsD1ss3irQDknRj5NvxiTF6oj/nDRnN/UQNw==", "signatures": [{"sig": "MEYCIQC02RyQ3yzkzmtKgp2spmXrNerLlT6xHsfX+K9wCWmREQIhAOROIJvYSAUrn6V+cRyXs6cwTWWnvWFHWk2QcumaXJsm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 728177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcDnLaCRA9TVsSAnZWagAA3XwP/jAwKd4q5ixnGyhzh6fI\n9vP6b3t556/FLCdM2NZkHs9aCEDVUgiWlBSWGDpm5a2zRts2QFHh6ckEAfTK\n+rJ3I+YGC50iXtiuiBN/4/0N8fgD/7P+eTp9BLV+qCN7SkOcxVv+SQJ3cd/n\ntehSqgUJHotVEudvvLX+oIia/pX/7db5atJm1GsRbMygFI/1WXHdvTrxIPnl\n9PrZHaZo4cUoSZLSYd8vqqSB/N+DMmH/NV0oEJvDxH3uU7q5+WfShx4eL8Jh\n+gO1+uQkMtRpRu7PmpRLnLEsgz08pbrOyYX8wc6/LMw4hsS6f5guSOy6ti6p\nHUFP4Blx8GgJlFDkoBnLWMjdkXLd/DPiuUHvzDjfxuAlqnoT2YiLlT1GPwnj\nAs+00crEaj4sv4baqeQbn2iiOKZuamkhdK/f//txAfwF+feOAGzrL/v16zKR\nPLxfd8WqQVVk76rsmND/1WEem8izLKSz3KTSsbhGForiEASiCbxQxdT9zwyF\n0288qDKCtpzQpz1LMxbUDMD4VHCv8shNcjyt7AWSwaKKLlbcE1aaxQhBvmmq\ngTV/n+autZlQ+XgvfN4FkIh+WpnWVSYxng8qN0wWrhiAqNPFw0CfEqbdpn4b\n4sPYjlElQ0Ojms9X8rR/WP39Q0H11Ya7+R/9O3+L4iY1A8S2wGZZvVzqT26S\naUW1\r\n=36kl\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.26": {"name": "date-fns", "version": "2.0.0-alpha.26", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "^1.12.0", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "js-beautify": "^1.5.10", "json-loader": "^0.5.3", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "babel-eslint": "^8.2.1", "babel-loader": "^6.2.8", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^3.0.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "e46afd73a2b50e0359446309ee7cb631d7467227", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.26.tgz", "fileCount": 4684, "integrity": "sha512-UAptCZ53MVimUFR8MXTyHED51AVGIqFlBfWgiS/KIoSYiJGrWScx4PYQVNSWfK2Js+43OlokCW1ttnexBTJ5Bg==", "signatures": [{"sig": "MEYCIQD+xVGXEvyFDy2OYtR1vyOONcIaur2Fy1SVDjsQepExVgIhANEBRXMdI686NW02s4YBuCCe8b9PYYEzfRPkCgzviJQH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4621410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD6UZCRA9TVsSAnZWagAAYrEP/RdzDa/uOMJx/EAY6I2t\nm54AGcoM0wnVWKz4ajt5XT4bMbQ9X/dvbGRLei410mc0+lKgG/laeIJkLbWZ\n1bKVRt48PbOiTnRNM6oRjUsxTa1ZVxx913nGrwntMip+nuSZ5WbE+hZbb9TL\nhXct7e5MpSLmsFvfpLOnm50GuCH1GbGwWvAOwafv/QghSrJ10h1EN3LrNnZA\n5gGwzU8WzjeWT48LNiRNpZ7mCZR/tKcTj7E7w1rlcg32uiOkmmZq+3ONMG2y\n1bDvQ4Dh1YV6IFMA+ceA891IZMtzJw3Ll1xbWjFzCqZvZfyECUjz5R4tgbVK\nhhGv24/ZeOtE4Ga2ks3ufhkWraGB9wk2R5X27plm2jms23cYSVsERoosnuhE\n80FjayZQBP7S3EhaKsHQHlOad2VP9aU7SS2+7A9QkakDU/FKGdBjGw2DghR/\nm1q2cbeU2dp0MpXic+sogI190kDnD5pMshkp1AFlk+6Jx5rocHUHixO4bM9L\nRWEtTmnak3UA3sNOTPJbJCSrQdW/FSlyUrxoLKhjgro8WVgrYEsGIOa2aAAg\nPMKaL646z1eUpxGinJqEy0E3msJ7A0BHJJu35Y1lmyG4cxXtHp8u4Za3RXSq\n/wKR9mAAI6ntUwlc1bO2R84Q+S03F66+qwmJ6PdoQMVAqsgzOxim0/Fgkpxf\nwjcx\r\n=3vVu\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.27": {"name": "date-fns", "version": "2.0.0-alpha.27", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^8.2.1", "babel-loader": "7.1.5", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "5ecd4204ef0e7064264039570f6e8afbc014481c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.27.tgz", "fileCount": 3701, "integrity": "sha512-cqfVLS+346P/Mpj2RpDrBv0P4p2zZhWWvfY5fuWrXNR/K38HaAGEkeOwb47hIpQP9Jr/TIxjZ2/sNMQwdXuGMg==", "signatures": [{"sig": "MEUCIALjpHYUMg8dJhic8/Zj9nHPhgjhcZK5RDiiV3d1amsOAiEAusHd/qZcagfo8t+WOFDuEAi77suSBvfDMEC66TQVLPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3516143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOHw7CRA9TVsSAnZWagAA/CoQAIO7Ek8G+gQTezOIRcZB\n0841Da7CKaDOlh+XzYrJcCGi4IKS9ok/LMmWRAtSAsgfdmPdDeMdsQwo2eEv\nRgqdzqf2mzk7AhS8OTpipYFLiaoLeiq9bjHx20INkV36fw4JOaIgBC3q3WlL\nt6JKBTbbhFTNudUgIQC9nHxaIHbz80RC7AkeN75xOXN9iR17MYduwzJLQMnO\nMQGiFdWamQVgJ4g8ecohLEGV5Zo7+hF9WLooR8iyjvRgUe+qKc/SHvOVjVef\nqhNMXyHDaBwNTwrQMklMfzzB2hkjFsEN0H47y/a+Noxmwzu4r3Pu2Sv8pKSE\nRMtfVTgYH4Sz8uJtnVXTraYT5B96VieamzPY9C5fhoxg8V3FiC88M37B7Uag\nbqiIrd5O9XSqnTiubJGhVt8L/cHnt/dy7e76ktaaGApJDJF7VGD4+OLHHdla\nOauShAqHCPgNRXZ4hyteHtpT9C7JQHp2qTZzio01cKGoucCXvabu6pZ4tGl8\ni3n+12zUGJr9wmOKFX5tVm79hG2R3InlJSnrrXiXSaKipChtNeNSJRNZDCoP\nZ5jLWjRdkzTRjJYsQxIMjJFyO9gq624XIS26J7uHEHHYgVwgoQPyIA0rzZSD\nkNc0kMFZQ+OZBKprZ3cTndha7nhHzWJ0ZklzuopuDkg5lLdSaft78lb4297l\n80Ma\r\n=dqTM\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.28": {"name": "date-fns", "version": "2.0.0-alpha.28", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^8.2.1", "babel-loader": "7.1.5", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "eb891590f0d2ad2960011a2f1e08c0359189c9ab", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.28.tgz", "fileCount": 3779, "integrity": "sha512-rqVTokkdoSqv2KltgxA5ifGzW74swULcEmijlV+hsixGcTQef0A7LiF2x3QmQOg7QnRdr9lRrb3fPDe+DyIrug==", "signatures": [{"sig": "MEUCIQC7zr7IYfXTdgWIF4H/mpFv/ey3Mah2nrnonRgXkfvYeAIgI3Po+apeeHjAV535XcbkiGL7pfePgw8XclNdYBTqRbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc985ACRA9TVsSAnZWagAACZYQAJfFSDhXQNKp5paJQ1v8\nT54zzrCpDT7O+4TaA3X0o5UGl+Dqq0tV5JLhoopym32o1yNBypmiJbJm2CH0\n7pvvirfOtFMsYnjI3r7niWdwSypw9b7oMEsYN1c7+L96zGGcOh4MsPtUbB/I\nVCpQ59JHrxdrxshclJdSQLwZOsjUGkof11wAUzHvy5WZGHwclsCmfOOXcj50\nAJNGYkmeT80iVzsB485O+HAErbkaWAWThisYmrDio8d02ouEJ0c7FEjupiKu\nIgq4Y/z678YuzBW1exVVbPEpEILVGI4YC4EKsD71+tLJXQCKX0OskrnfGuuC\nG7t0wrzogC8AIipouSiX1awJ/tUDkiLdl/EGLcxeLGGJXC1qQ494OF8jmqPp\ngcb638mxtGwI0F8/jUMveFRp+CctyosLBe2LAdmp9UANlfFR5V7N18HQR0ov\ng9rv6BiCIxp7S30WYjrg1R38D3MoeyQ0n6HMElotaJ1RDGEe/vrMZLvl3jgI\nUa/5m985Ju03TnScYL2felNPJkJxWcLE3le3a4849QXCnLlckfLyHYkGv+l1\n0zlMF6Odeuu9wpi8+vBGLmN1TrgNNr2nV2NAwvSD4fsW6FHUQpUhMyUtryxc\ngysG8LREb0f5oALrdEAQZUeOp+15L/eQWrQrqb/AZH+sJEiGO6oTqYeO6RpM\nfalz\r\n=dljs\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.29": {"name": "date-fns", "version": "2.0.0-alpha.29", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^8.2.1", "babel-loader": "7.1.5", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "9d4a36e3ebba63d009e957fea8fdfef7921bc6cb", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.29.tgz", "fileCount": 3779, "integrity": "sha512-AIFZ0hG/1fdb7HZHTDyiEJdNiaFyZxXcx/kF8z3I9wxbhkN678KrrLSneKcsb0Xy5KqCA4wCIxmGpdVWSNZnpA==", "signatures": [{"sig": "MEUCIEJZnPAiBr4YZ9LmOsNB7hsL0mNNOQoWtahA1lJcR6NFAiEAnK8CBL4ijLYcZwIClj8tZNeeJzBQyQQRkWfQn5uGKMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc99toCRA9TVsSAnZWagAA/CgP+QDMBp1HKU0u1WaSzl/+\nlei7c7Isz7TgEO8SLiB2i99fl4GqbYnw2U4sQpkngOsXxFmxdBp3j0vaR0f3\nSwUN0KgaSa+T5fcfL2rVKPEsq7tODIG4EZJ9L+JZfSUAQhxhs21t6dXhtxVt\nqY2EeVpG3fCs9bOQC5aJTzXwpW2KUbEEV4Wxgck5dmPHpiRw4BJ9ySuzeEt4\n7yJLtB9/noKk2/eJ4HI4fLRyXbK+6ntElN63QqVHlmUBSF43BzlpI+t8i4nq\nTiuTq4tKQb4DZ/PKt2ZTdaxf0KWlBfbgojAW9JMHBvsy2sgIk2Jtr9LWGnqt\nCa8IFgHy+tHffyEzATzZpM7xIOqgA77n/HoOqEAS6T/UDzQqe6qRCvY03Cm7\nxtPWoakhCCpUcP+rYd/6nylO+Vr9nDVBb2cgWFyDd/yiPqaMVOGhXBTGP7r6\naqMgQ+AL+Aa9mLDB2QkJ7VB8YBf0hH2fQcNbEjerLGO4z8dwrH9lHJwB4gfk\nsiDrdGXSmXQ81mSRw2AMMUZkn6y2HZ1vd5IIWxpZXI/jXBjosObkbSpcuT1i\nSonP3QYSAlYqydDZM4pCtoQZb4Vmq6habF5jD04f+p4xvRiX+3L3ye0j3llo\nl2f/1iMVHdFhle0oe08O3r2ZSzxn1QiOpgGXXxyWL1OpxSWNXzOwzFew9c+x\nFJBV\r\n=Tz5d\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.30": {"name": "date-fns", "version": "2.0.0-alpha.30", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^8.2.1", "babel-loader": "7.1.5", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "23958c6f5ae97522c2ba837b725001749e875096", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.30.tgz", "fileCount": 3779, "integrity": "sha512-qUIZPmN18/ePPvWr/D9dAASMoGPcinj2USQ2uzMUFYePSPavBh9hatumLBXBEgvlAq+IxuUbeXYl07PphKCENA==", "signatures": [{"sig": "MEQCIHIpn3HT6fJoIik38Z+o6IOGgGlG1xTp9A3K8cDKiJEgAiA0Gi2SsQpylucSeQLEGz6gJQ672EY30e+kvMHb0eT4SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3746825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+MSvCRA9TVsSAnZWagAA4aEP/j5Yn5TEfqPAEvggihUu\noteHHfbuyp5Ure/bqg78cptpy/awwHwO5PKnV0Qt+FWcuE8UwBvUYHLPV6K5\nQRj3zx1WqOwizKEHk8qkPdPMzEqz8kkOWaW2Tfcd313SatwIvNe+Kydk+QMF\nq1NwXSAmgX5QaWXaq8ygBVfZajDBWbPBa9M7l9EJD7ja3htYg0n+yh7p3xim\nCCu945H9kWiFeFWsJD6qD/uOqH+imzG65aLPwI7VHZHALfHeAsh+EuE2GgSF\nfvGnOHSowN9wsG7rJj5EaMjUEa/C0xQw6JEU+6M0fzc2edABscFYxstftCIe\ngdqvfn2awb87glHmaY0UNMrwSJd65Y+VCPqMj22pPjn+8XGGULPIH61lRcE8\nNhCUue+/4+xSTL8UfqwHByILVjPnXVo9Cs0meTn//Vfg/VE/n7VqUdMbFN6H\nZ89MBXXsXZH95vXghm6jKE5VPuBkwNFWJ3WLpizokinKmtT3OnqQlVOlO5i0\nwUZj6RWz5GfzzvFZqgpEtBl5oTpUPCwsxF/dND2fcdem/TskJ4T+HhtNWQ1g\nR7U3O+fJUvMJLioRLt4bpTXf3e7FIATn94aJJ7ChTtlK7wnG8MqYAFOkfP3f\njB1A+pG3+KR+jzj6KTm5cq9f4Jt+9jm8FLkrKL69vafvRLAHHX3ian9uCDll\nHWWm\r\n=2yMD\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.31": {"name": "date-fns", "version": "2.0.0-alpha.31", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "babel": "^6.5.2", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^1.17.3", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "babel-cli": "^6.6.5", "karma-cli": "^1.0.1", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "karma-sinon": "^1.0.4", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^8.2.1", "babel-loader": "7.1.5", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "babel-preset-es2015": "^6.6.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^1.0.0", "babel-plugin-add-module-exports": "^0.2.1"}, "dist": {"shasum": "51bcfdca25dfc9bea334a556ab33dfc0bb00421c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.31.tgz", "fileCount": 3779, "integrity": "sha512-S19PwMqnbYsqcbCg02Yj9gv4veVNZ0OX7v2+zcd+Mq0RI7LoDKJipJjnMrTZ3Cc6blDuTce5G/pHXcVIGRwJWQ==", "signatures": [{"sig": "MEQCIC/ouycaXp9Dd018H9aSe3GZA0Ftxr7xUnqahnddXDQVAiBQAWrLsKUbFtj7DgLU30t1/+s2SFRBRhASzIRsZU8obw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3745347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc+M8gCRA9TVsSAnZWagAAStQP/1XuCz/WVikuve+aSokI\nupnS6dbWntWmTSE/soEbdm1jiyZiwIaskC+QnzJb9DMsAHWa9ZmIueFE6lK9\n3r7XW1WnBP7b3JgFDqoWByjV62GLCdJLZEKKTtiFyCLWTBLI20ljqtgUmHaz\naoKt1PQY0Zsa4K4ooJbhR47QGGJQsoPxMAK7zcSeWHYIi9dAAyHFuxhwlgF2\nQ9zn7aLaWgwFbZ6AfpM4Rmgw84UUFNBLjZRhwsHyokfd8Gp3TF5DwVoDcDTp\n9tjGbtC/SOi2yd76OzQkSYhX2K0HoNZabr4RoeMP3bHqsZT0KoPPdBppFY84\nPJ+JB8+UlzyfxqlI6fQt5/b1h7wN0o4+VpHKN8UkT81OAuqR0cn7vsVwh3kJ\nLyxW+y5BA54uOgZEiQCjLYy/mBkhxxxODPQGMgeItxecuiiZTbiuO0ppMwir\nUxUsxZWJ/qePX8oq1+LQcVQZEgfg6MfMujWlQSb1oj4uNCidKNllDhzd6nl8\nBfKKwL/daQygLmk6sb5MuBiArlKXAtx8the0WFamI0T7y7WwEt7x30+eEMIY\nClX2QQk3cWSSYaD/KAWX5n4cCs/J4Zz598tMkBP4XJyVVDh3JiKl3d/6eaz2\nv7JIbIGyI4o7EAde9hf8WObgLtvG80wUcEoBmkpkL/mi19qQkR8dT3ieoT8m\ncLfa\r\n=83eg\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.32": {"name": "date-fns", "version": "2.0.0-alpha.32", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.4.5", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4"}, "dist": {"shasum": "e36472aac2ff49b199dd7b257807ca1c89bbe603", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.32.tgz", "fileCount": 3978, "integrity": "sha512-Rv9g3piGxEp10ujgPODgVDfkn1Xz1HDZASEKmGsP/RhU4DCAugdNfnEjE52mvz8G3Ha5XFlQMDEb6Lwryb9Wfg==", "signatures": [{"sig": "MEYCIQCpgTwEH7/dqoL0MeBOw32ITeQkQlEFV+OzB5HQh1mKngIhAK4JpNR2lLa1dPSUIPq3oMUfjjFjYTs6m1S2OQ+U541F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3898526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAKGWCRA9TVsSAnZWagAAU0cP/RWnNHtLgf7fdSd8YhLG\nssa+v7zRe8Ak7Ik/OhUVw8XwrkdwMx+Y3jPlLdh+S3kkAfH21EHnNSorXC+6\nFxLhljlHbTZc21wwFM/A1IT83LIYo+b2b4+F/Zihkx8gTvqz80isa+iPH7y9\n7cvXLte3un7ZOFTfe+J3F0xyV6bOPWjwjk2GRg0EpER+0g+60ctf6Jwu4m3T\nW3EUERP9zVVa9SHqimsIQMMrN0qldudhJToOeyvHAujpGuNR9/iKYvYqL0xJ\newa0CKM5B0dlkbgSbS9a2YUM/FXaV75x0BZlujRmd/5nxqHbl973YnBFDaKw\nXTcr1EprMDg1R43d0cvnW0Q6GP/Tj4EN03ZKnBwzn62eqEcFsc1f2dF+F2DX\nybyV+Lo4w0nWV2pTs+GP0yY2a2lq6rRyqkYa5sZZgCKy2Jle8R454e4N9Y3P\nVHMjO4cvFT+9muzcyUf5vh70GHjUb3xFf+S8eQAPOroa0UVygQMuYhcCT9qs\nEJ/ytdTrPSRXF3/Xcl5v155Ftr9tHZF67rGhK9Betm/BCuAmdlpVR5+T7zpv\nhhSQqJjYO9Vqr+ktdRfDce7fxE2yuX80LVNqiVqDY6aqRroFSzowHRGNu4yk\nf+UyT1DGeo3QWh/mfecNUsEqkkOaqsFkhuvSK4F9ea0sR/SDMEgWrcBdK8Sg\nVcb6\r\n=pOpV\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.33": {"name": "date-fns", "version": "2.0.0-alpha.33", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "lodash": "^4.17.4", "moment": "^2.17.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.4.5", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4"}, "dist": {"shasum": "c2f73c3cc50ac301c9217eb93603c9bc40e891bf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.33.tgz", "fileCount": 3978, "integrity": "sha512-tqUVEk3oxnJuNIvwAMKHAMo4uFRG0zXvjxZQll+BonoPt+m4NMcUgO14NDxbHuy7uYcrVErd2GdSsw02EDZQ7w==", "signatures": [{"sig": "MEYCIQC/XXQWQ3J9F9gG+kyEWsi9dAFW5hvITzurJ/kn9bUCQwIhALoa7e1c2g2PV/qFaKHZuJhCCxKetT6KXyFb+uSl5awl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3898520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAfIUCRA9TVsSAnZWagAAXN8P/2cuXZlKoL+e9KkUb592\nTG5phxriE1k07CTyNCFBayJq5StlkJAZnRZUJZy6KQJAVqaeczLk6rXgaGMt\nlGGonxqt20sEjEj/0XwM8WORbXNleFovJb+HlbffnF3E9FTPSf/VpqpxObOQ\ncaV9QUSlWm8f3968Mdkb9wakF/kPmrFbNy3pIx9QJOrWFvkcm/kGVRsWlaWJ\nbTNJCTw5eNF3vSa1fdSlPIqWgaZNh5UX4skFFaIYlhE8KhsyUqASGOK58PV6\n0lpgR9zMMd60DycN4MUc3JqE0mXxmF+g27lvHrpABFm1atdPjDYQjkzxfU85\nnotKekOILtPX6IpyMmvunB1qjMfi3reJi+srw4LmMcs3565pt6P2BHjpT7Qi\nLMSkqLvBwOWNg7VYcT7ohlf1oYrtesLuQuGyv3HrFUcAagpEdQrknvKSESpw\nJAZMTQAqMgkGgVKOtuP2Zor2G1fAkdRu2z4K0SwvRz2EClAP537ZXgrM7jQk\ncfBgstZsilPTmxwghv2vlLYgXLq9/L7F+EQVkykBytY+fEoifyY73qRRK5yc\nt1IY13J7HDTDpfa3VUIBnQ0xdzCGy+6revF4K+/ES3599IN8cIKNb6Lv9dPV\nk+bUtqHuuMRRPc6hcxWxWk5/7KgtZUIQ0f5lOM4b/GvTpS+V8pAcvSrA/oFG\nmf+N\r\n=T/te\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.34": {"name": "date-fns", "version": "2.0.0-alpha.34", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.14.3", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4"}, "dist": {"shasum": "5d3ae7ca0d08915ccfc87a20545250af4e9c3cae", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.34.tgz", "fileCount": 3866, "integrity": "sha512-yjSYUHASHvzOZl++cEms+Tw7oQOFA+7Z6/lL7L3lRO9j6pMfT48N6oEyvCGo/MVlH08XWmydgf8X9Y1eedf9sQ==", "signatures": [{"sig": "MEUCICEIqWfe/12n0LoKeT37CaJVy1p9mrmoHOhLQZMh6eCEAiEAzpyGPllN1c0ci76J9ynzFmCEhKdZK4GRZ3xvLBMzWuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3830589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdA4TNCRA9TVsSAnZWagAAicsP/1VKyweUbtLrzX4+JpRV\n9NGyodBlxoIl7YNn7ZwB/rTWZRUUoqDFx8R3PGz1K1wVMOLHv1BtBeOUUCbJ\n5hCSAxcpwdacvZF1Smf2pJhnH+XE+L6xaO6alRrjIPGWERrfH3il19nxBy5h\nSgaHCgRl51cM0mvj1L8t9M429WACkcBsWzqWbBDWgKTUNbe8YMGMbvmKjWW7\nrnHRUBpNWPG6Op/H9cH2hdieuDlNe5XmBuPX7Khrv9/e7OqpXfarOvtMDHUl\ngRUast98tActLZV9VoVksDbg9k5oVk7Y8PnNtIO1vvFRCtkZfEp+VdhTkw2G\nBQE2/3aDbwgyGwOisoEcfw1IzlhHk5S/7NbbFQwpbTeyLgG3/EabltP/4GlJ\nyZE9wzcdmw1L0R3+jAaQU6RVkkSTBoYm4QAimjkfM1b1meGqOvGK2CIO3mtf\np8MZSn39cJaRtitbRy0hQB6brPPgk32qyuoaTAQJzQPYI2mE3/2Gg8Y8bgJW\nk4w8lifNLuYsGntYFWQPQCDoLJ44nDzvNsFpWuimiZN8yyEoCzlvO+wA6/8t\nk2h8yTNUztwoo8QXOk0g9VOSZNYg9O3PQBl8w8IkcviPHiP8UE/vOu9ZRCqO\nk/7K3+pT6Z9qCbB7nKWEfRsIQO5dv46DgwUXilDpGOPH4ljyW6Nd3V4S4eyT\nkQ8X\r\n=5nxy\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.35": {"name": "date-fns", "version": "2.0.0-alpha.35", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "185813cdc51b05cc1468a95116494bb3f3440934", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.35.tgz", "fileCount": 3954, "integrity": "sha512-dAY1ujqRtyUsa9mVeupyMzUluWo1d7kAMwyXTQHFImKYSHKvxDw/dipiY6fdswQOs8CwpGoiKysGfaaRP5r3bA==", "signatures": [{"sig": "MEUCIQCChJNURrPkc5yH2eS40MUgAJuRMb1rEE2548Lr5jfU7AIgKv9v3pUt6Nm5GECVkxp3AX5gOW6coVJ3/jejO6QKlzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10034518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCzOHCRA9TVsSAnZWagAAuaIP/1Y12xXUXqEwkDbOKkc+\nVKb6QyAhD5ZAoIt+pBkKk4CQR5oMUHqbc+Qqtw/xijzgubiP6floHTzSiW1X\nBNVM7H6ANYbTUAJhRRWQZ7A1CPS8L2nHZ2OAYhUi+wlb/rRNKlR//leFInVq\nPV7WvZRT4oV7uacUM999aKBvJZuH+j61fkDQ/ol5ue342Ged6MuNXUmFVlUg\nj8bZ0Ed8Vsuq1dYMsnfQpnSd16734Yk0Mly05goK4Wglv6r2IjXD5CDufcSx\nL6f3XYgNHix/VD/z5BHbPx96epev4OPzCIe2DbQHDQEt8qRmn8S8JmknnyWd\nQYB02A1tKHB2GdwbGd5BrvZCAIodMhGrYwBbgQgWvmvUR+EPFZ7Nq1W6NwT0\nQb4JNMfs5nl5VCeYI33mnIsZL/XGSZPynT4O8f9onIbD/FGrl4PHQFlgfVNx\n0f/hSjL9sSnwRMRlAxZ1m54OxJhXahSr3bI9gMtbTlKAAXhoygULb0D0q7SJ\neoHj3lLyIuYLVT0I09XLCoejgkV0dm+VRt2RKUeT0q7GQ61q0gIV6+rfnZZ9\njv89qR89HPnPLK6BVe3WnPesmlVm0Gv5N6Pobb/4pr4L3Kz2B08hY9gFjwvP\nDDmxMZIELsPUOhMo6yg1j8qMsK/j3HxQ0PqZcWap9vtcrDK7siQq4Wmm4mJL\n/31Y\r\n=DhpR\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.36": {"name": "date-fns", "version": "2.0.0-alpha.36", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "e3d106fbc6a7247e951916229b948d47848d6102", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.36.tgz", "fileCount": 3954, "integrity": "sha512-VcO89v0bmgw1V6wVlg9iYiOfyS9/m8T/TDhLBlERkF9NxVEatVr3LcwronDtUGMwGAK6KxUzs2TrOx7TzANMkw==", "signatures": [{"sig": "MEYCIQCOisu+mqLqKoVuNPtWZ0iw5/oICxgkIURMLPx5ZerjGgIhAKlyziHtl8zLHzKo3EinoCwIEilvhjpXx7mBOzI6Ty/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10034679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdDJgMCRA9TVsSAnZWagAAZAIP/RU48CEBQBCW2vOET7vz\nTHysSBdx4wfy8KiMngoY3Ww91B0yttCnBnKele9Mvp+vWpUkayZp6k9riwIl\nux7CgkGrRJD+7Cqv5ozx9UDQ3RB2lA1FgY8zn/bPQweHB95Q8EYFp+dnHrDg\nZNzrM8l0tBnEQoHR2TzApIW9yOfRnTaaLOLl5/nlmrCALxTv4aZc5LD9WDmA\nRB3r74QKk8dNjuqD6W8+hbC9DMuKsWeuM5FsZxV2WggPF5y43Q24cyODQNjS\nPWS43rVNsSh8+ty7sLW6FFaR/SKsU0tortdA9DhiTxDr6ljOel92zutbh9he\nCbBJ/VTivxUrAtA0CyPsJm+RZqFyDZMVXdJAgnXUF9nZ5eWubLv5W3XFgVvT\n7g8QrR79olKi82xzTYc8sRudwxJKvqbN5gBXfrhoXobFyOEmDQBGdLyYMTVs\npLZj5xJqQ4zLwgJkPGJ9PLciRTvNeaN0fiEx+mnKWFE042mbV5ro+nk35l6Q\nxW5WYoXQ/q1VuDEsWSxp7NSr7TYHHaP3fQL/DTo4fz5vN8RtZQ3ui2H3OkBO\nW0BaElCIj8X5J1B42rrTxuX+nXwDdWKnlFmVtNVIKhZOqfZJ6QxlrGizGePf\nkNEJlMNcqCkaOI6FkFVkH3tQUAVc1dmyHqz3ooJ3CMUyMRUvJl9BI4t44sxK\nkHG2\r\n=OGki\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.37": {"name": "date-fns", "version": "2.0.0-alpha.37", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "c58b3e827da4f860ec8dc123e54019efb4a610e0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-alpha.37.tgz", "fileCount": 3965, "integrity": "sha512-fyIv/h6fkFd1u2NHXni5LPRPoa9FFh3hY67JSjNfa+k/u4EKvfrpGtoTM16Y/BJOqTb4W05UjcmwBna1ElyxDA==", "signatures": [{"sig": "MEUCIQDvHq+I0PIsmtVkLQBxvoI++yMwiugCKJ/Nh17WZZP4JgIgexZz7uEzPbrj5jozzrKDDkFE2nipE+BaoxzwsntvDnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10190422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEdVRCRA9TVsSAnZWagAAGCAP/0I0xMqCgj7NasbbPQht\ngdH622sH04tXdf3hAV2DyHAW2lXX6sGRIdI7xsMOH8v39wlNy9JfChiasE1D\nuxvghAAzpsbweyJZsgRk5Ji9xTyKYeYCTZMX9rzQkHb60NEqWr+9Lq/hzhey\nVwj2BeITVR33k/Yucm0o0dZc7INUnnOKO1ENdIHXmGXbm/JXA0qd6LdX05xZ\nJYOSIJ14sowhP9CqCAZ6lDMOACAwmKkDK9lDGhm90AJLSKnz08Gx/tshcOPO\nq+wODd1NygUjMSdM0ut6YpvsSx8fVMeRDC3s7zm5nAVTeCIToOJ9+a8tTfbm\nLJCmMRczxOJiHzxP47p/+0E8tKtMZL0Rmm5Ro2Xhvkt7tzHVCirtOCeOh2Ve\nmS56/NShJsWosuJ5LfPDRwxxxStTliqMUFjG90bV2YyF7d/ui0UCAK2iQ8rT\njMJSc61WTRfc+1f5IlR9uRrQOu/2eSOKhc3vY+Kd4ZBXly4WFGpCSxG1VdmC\nosxF9o4x7oZ1c7fjDulgpTjgvW7OTK/R65Ylo1IBBgUVqow/LuEIS8+MZi0G\nl6uFF/FAFNEQIkK7/W+TtiDJjUGUgf5TCORiPPtPantbMN5zRfVC7dHY3Oce\n3Gt93Udxo08boBWkf2JE3nVkDqhyXZKq65LFKDOS7ccoJhEhJJ1VCBgw4mW9\njngs\r\n=L4Dl\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-beta.1": {"name": "date-fns", "version": "2.0.0-beta.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "6f3209ea8be559211be5160e0a6379a7eade227b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-beta.1.tgz", "fileCount": 3965, "integrity": "sha512-ls5W/PUZmrtck53HD3Sd0564NlnNoQtcxNCwWcIzULJMNNgAPVKHoylVXPau7vdyu5/JTd25ljtan+iWnnUKkw==", "signatures": [{"sig": "MEUCIBy9TStaqvUDIiiqcuMiZpmn97HRrdeP7Eso/0aQY0PjAiEA4US6qz8lS/XJlXOk6MYVR+WTwGtq4tATxcz83dV6z6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7457422, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdEzuZCRA9TVsSAnZWagAAEMYP/R00x0tHRI5DOAaJwBEi\nWA3/zMRc1r751kagduQJ6Ui30NtqSzaEY90+6+K/d4xhjGLo86sNnzpItlDo\n0E2iaFs3iz1nPQataaG3hb4ebwsedA7LoCNsimSbAlkkKvyRq70vvr3HorII\niPSS05IUxlrDfgew/d6pIEgTflFVEpQf+JE+6+WMbUbNKMwu6ZIi73BXOJlj\nDNhd5DuIN3LZMt6ZWu+PhdOACCse0POEuRgxamC2mT9gRZt8ZOoRSn/EawCz\ngTAZ/mAKqj/4peVyLbUdcHm9+onluYkULwgwQSFz/w1ni7t2oXgxvVNozBQI\nVWEpv5+EMzvC7rRLOjK7N9nAAnlL6qjQzxXR66aMf64c3Ev9hG8s5bCe0ysn\nt1KDIjF8LtGjHhVIeAB8ZPouxOmfeP8x5uRuAxkgVT3mSvC8oRuPfzo51wsT\niCUms+HcYQCEsz+A3fGxHVsRjGSrg1Bo2V7cKQnzt8gZpH6jJ3xz2O+b4aJw\nDAgOv6TSYp4vjtjHQYTKqZSFXQ9ICsA04UI72MFc1JfyLKhnbDrQKz3RrSd6\nIpWxagfNpuJEK29G3OyOa0xNZGNLnUo976ISE1y0ypvTbQOwrdT84jhbh147\n2qhx6hO+yMsNC8vwbCJjdrXPf3i6f/8MFgy6JleSsWKjjt1r9Jlcm51mwXXC\nWuYl\r\n=2SMg\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-beta.2": {"name": "date-fns", "version": "2.0.0-beta.2", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "ccd556df832ef761baa88c600f53d2e829245999", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-beta.2.tgz", "fileCount": 3965, "integrity": "sha512-4cicZF707RNerr3/Q3CcdLo+3OHMCfrRXE7h5iFgn7AMvX07sqKLxSf8Yp+WJW5bvKr2cy9/PkctXLv4iFtOaA==", "signatures": [{"sig": "MEQCIAyMc7a7axgjHX1jbkq3D+qWrQseYE0ejkDmUphxdiu2AiAxBC5fimqeW6vfbirGENlrKXyHTCM4PV9HyeRdgsQsIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7457522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdFHgcCRA9TVsSAnZWagAAeDIQAJCQK69HZIPlhDlH7MT7\nZniXIBZITbm9ZB/RSI/dVoQhSWlwlhtFt5y5T5bXgVjbFbpUl59EzFT4l82v\nJvbD2nI7eoN5xMiSi6JzKYTFwsh6Ehox8NW2Fmz1U5+vAQSnBc8LYHu7WE2s\nLQiswOLphV+2wkE643dz22NoR1A1pFH3x287r/ohp7mBSj0eyOkMbqaktLS1\nMhoqEZV8UgXlTtWabwaiFk9j+QOSAFhqPSEr5HybjlQimwIMBbsFr6dLo+OO\nsXFAwfr+qjLlAvm2qaS7LmBBRsQ5X9M+LaDk7SoHtfulwgeO+05rfuFoMgAr\nwkbVn247HXSsG/SCNDw7HlkPSOScWwsbRnR+sL4Tj4doUJfSvqwapfbWUsVe\nAxE4sRkCPxdme3slBEGM85gakEeWQQIh1IxtcE/Wl6Oaeq09v8BuRplK6wxo\n1iAeRtIPzpJjN0BJSkCSMBm27h+01mRfBQh7EfndM9ucqIfNWXJ0CjkPXl8s\nCRCPm6QNay9TwfDuFyY/DfpQuhOArf+GuF6ogEPz1f//SiD3JTrBEGW5jPdF\n5rnRKgmXfastzDcV6dZIofO4Qi2fr6OV7UjtTszNlCEM+Uj9HmMM6GLnUHNC\nFUIne85THFnEx2+eICl7w2YUbUIfGWh3NPT8NLDcKZT2FloHJW638+SzRchq\nVjBD\r\n=BM9Z\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-beta.3": {"name": "date-fns", "version": "2.0.0-beta.3", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "2e28f5af945930f774ddd778e184d68227101d55", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-beta.3.tgz", "fileCount": 3996, "integrity": "sha512-z5O262BvHPhwUvA1weXH+AZodygnZUcORERw8hjwBUrRPGrAo2e/rjXfC8Ykf1OGJZGDuLnK/WXbEZBIc0exGQ==", "signatures": [{"sig": "MEUCIQDUuKkJe3N1Cuv6/pGJab0gLyx+uguU43Sf9JAcLBpdZwIgU9vGkyFUG7mhsFQeEBCjWXNwqsVNIFdseCCzCUSqRos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7673420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdNxTzCRA9TVsSAnZWagAAKSMQAJ7PMODXarQwW3DBzCu/\nFSd2wzq2cthEuQ1OTBO4VA03xrsQwLy/+nXq28KeQVFnRjVFF9LoCmxeo9jo\nktQyxqJCRRDbxuESHzraFsCP51DF29WIV5ZwPpbomEkTMzOmdWQmcqscei+N\ne0iBnPeKkP81eJNQ+OoxNdfrOXv6bHqW7xe8kY7qP4sS8KP0vkgszYUG41M9\nvxdicXLRR0/lM2gbVnV8YflqcNdrOKXtUWWJdFzDYhwlXJe1Dd2wSUJLRgDk\ndWu9gZzg+HHlrvXARxSP11tdvHQnCE2T/lVXx5dlEzGdkneRw+W0Vhqe68yN\nR1MOsq/fGCEYh9r4JRDf+vjy3JheixQn3TJ4jVnQWRKYJBBlKNtlOPJ7G99i\nLxcVM5OUYtmKa27oo60Dy5rmkQiBdgNkJG+5BYR9YJR/xYgwLyJ2xWpg6Zu2\nw1RWNmgjv6eKBRyFMrBnsQJdC/jpMM+K/lq6VA1duJp0dCQ1xJ29RnClloXB\nKTDbQue1HHwZc0phOyal9mijh4VfL6yugTgMi0G8DmK1jOj1i2zOX0AJNvvj\nEvhmldF0S4Q2Iiaq3Q9lhMgarcE819au/J2dmkgTVoODT5YZtaor28cIa0GM\nSxsN0rO3ikOI4lTtXCUyndfM+Mj50GDloyEQMy9btUooplUljWNBjP+wQjvo\njQQh\r\n=koFv\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-beta.4": {"name": "date-fns", "version": "2.0.0-beta.4", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.0-rc.7", "karma": "^3.0.0", "mocha": "^3.2.0", "sinon": "^7.3.2", "eslint": "^5.16.0", "lodash": "^4.17.4", "moment": "^2.17.1", "rimraf": "^2.6.3", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.2", "karma-cli": "^1.0.1", "@babel/cli": "^7.4.4", "fs-promise": "^1.0.0", "node-fetch": "^1.3.3", "size-limit": "^0.21.0", "@babel/core": "^7.4.5", "@babel/node": "^7.4.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.1", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.3.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.0-rc.5", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.0", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.0.4", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "3e1bf33a15da69481f81972c4a50aad762a81f2c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-beta.4.tgz", "fileCount": 4006, "integrity": "sha512-xekjYm7ZDBuzePM/GBodhi3hW3P8dd2RbuIOLBjet2E6EGFR82wHTTXCSGuDEoapqlDvsx88ymRsq85lbM7dDw==", "signatures": [{"sig": "MEYCIQDB+Bk6AnM5Jm5WJfqAJK+aQUdIhei+SEN4eBc/cMVg7wIhAMfaAj+5RsKp9z8wzsmVGoOOtjvt9+krpeXmaHXBLoVN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7771159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdSAHxCRA9TVsSAnZWagAA9QgP/jG8RErlaBOj08NOFh4X\n3t6ebEHTbxR+j3Qg5pEFpN/XrFIG6JTifOPfMT5cmtmBzt9u0+9SQayTuQmU\n/LoOvm1L2iR/9LQchN/dEMonarh21k9fSNzdY2D2JEs1/ov52YjzHJ9aKMRK\n/+4q9AFZ1KWAkiwRP5timY9hHgtULEPPTZezjmkontaSiEdqDpcP0v1DsaaC\nCobyat1LwpKWwjvmDLh8rU+Sle9czubZb0D/Kt8wL6VkPNAO0SMbZg8X437p\naSmfrAWiYOqYBhOcEKTFAoLU+tnrXnUQaHymWFl6upr5gv06mNT8cISKsFlL\n1nAFbgQPcZ2+2QFAyCM2M9rBz/Z+cGj+WydaWeAjnGQ8w/9478ecX7Hkw6hY\nH4CUdpavcp3ECLiRtEP9EJntaXUQJurBXUHIy+qe+cPIf541A0Mu1ht/7lUE\nXThNWrwzlsF3GLNL/HPP+tXIOBkUAAqkh3CxVpVqUZQsCu9S8/6W/UcR42q1\nLRl2HCxEqHOgRCzpM7Dl6/s2FTxXsLNY9vnv77vYD4aYAuiDr6umKo2+dvUs\nK2qPSLrgHJDQzq6+RtCEBT2htdzHriK5SumwPQJxWz9mnLqrLFDTZoeab0Xc\nXNpBoOkwOJk7bghB4tlU/w0q4ZAkKpaQIicMh8QUdv9WfPnAG+nwsiVA4jnA\nKL0P\r\n=KDBR\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-beta.5": {"name": "date-fns", "version": "2.0.0-beta.5", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "90885db3772802d55519cd12acd49de56aca1059", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0-beta.5.tgz", "fileCount": 4016, "integrity": "sha512-GS5yi964NDFNoja9yOdWFj9T97T67yLrUeJZgddHaVfc/6tHWtX7RXocuubmZkNzrZUZ9BqBOW7jTR5OoWjJ1w==", "signatures": [{"sig": "MEUCIDqsHHgrogV44WAMB31WwJOq1kscImbygIbWqYyxVQ1BAiEA6UVm7s7hHDEwkx84kta1Tity0M4PRVHKPq2ga9l0ew8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7873959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVqMzCRA9TVsSAnZWagAA4poP+wd93kIcq3m3rMQIW+Rm\nU2PSF3WdreDh7nK+6NkKcTNL58ObOLsRKtoaZmVkmFVPSSlNBy2ESc12on0G\nwcRM2bl6+SyTmMGbSHmeW7937ZBgCcD2793xOEEBzKa4xTXr++gylQK3wxxX\n4AhqnKQ2P9FX2vJcTR87dUzsc0l1/bZ4drLAJ80F6kTUhYLdeyjdCwZ/NgjG\n6kAKg+S0L+HVP+QFdrPMgVdvwtRqTtB5h4GhXqzElLgi2bvfcIQ4/Q++ZsqB\nfyx/lDC3A2G6lS4loPzM9+sJGAhxYSurOCqCGjJhsvupXCUUNPTkjuCeXQ2s\nAGds6518zV77UiHytX4slbhJEmyDkeuNRsA2l3fjBivuPQcqafXKEhUp2EMb\n0sMfBJ/G6Ohall+K7VDCEc9sebLqQCwM3nFifWv4eONWSmbahbfP6DN77GmE\nsBRsF20w4fP6iMpQMfHjbW3w1RzMQafb3n3dbO9VeCb4Tbgkjli2rwHIYhBk\nwcKcoTx7B5GYs4u3qqGFQcg+pM0lQiCqN9xpqhlix5HYwjUmqoj6GgI8Vytw\n234baaWCGJRMoH/xdFbuK+FChjxZiMzkPN2oVO7bFc0GtTO1Fh8nqd4aFma7\n0kcgOxfoKwud6DiR5B2MmmDbNZv6I4Q71PRKWBOi15rWTr2Jmvl+U5MiKz8p\nVyV2\r\n=zm8e\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "date-fns", "version": "2.0.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "52f05c6ae1fe0e395670082c72b690ab781682d0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.0.tgz", "fileCount": 4016, "integrity": "sha512-nGZDA64Ktq5uTWV4LEH3qX+foV4AguT5qxwRlJDzJtf57d4xLNwtwrfb7SzKCoikoae8Bvxf0zdaEG/xWssp/w==", "signatures": [{"sig": "MEQCIHZyG/q8tbTn/fOwCM8ZRNbmzo67OCEtxbOWM5vXXLjZAiAShNzhynEFJ+4HvKA/Src7lYhxlDn3Zq7YJF3aJVlhSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7878151, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdW/KMCRA9TVsSAnZWagAApuMP/04yQc0+9xl1MWIF6fe+\n5yJRdYe4DP26lwgXx768kDO76XomLu3KQytAWBrjfcEHuOhWzSh217DqFit2\nPIQQEM38SPmpAktW4ET/Uvtu5ryw9C2EviWmFoGsGGUMRlHv+3iG7P6XGkx2\nmCB/Y4UaBufV0/63CMH8WtXWMpLhCgVT12sNmm2OvAGKMA1+hCDvYgnSJYcy\n8an1XQoicanuxxjS8g+RUq84JShDklcljnLTzrHWv5qPcOyzeNssV0KG/ieH\nFXXWihmygQIvd569rStWeFdhqfkRuCTZMkMZmkmsNB/4LtyDjK2X2euY3flR\nq/YW0aDE/UtjyAPgXBUkwv/XRNVvKqysiq1BhX+fGXhyGaqEGHsiWwlb9pnX\nAnHRRanqQKq2LF9k3B70tnHQslZgrPTztLj/jBOzUQgBKZCskq3yjLpyUx8p\nAgAKrd2ZHVUOPHcU/Drp8RyN9XfWEpADq5VPomqfb7RAWsJ7wL5DldqhUHFp\nVTGk90nvmKDeyRoPk5COsceuQvYy8TpWZ5LKT4s6D4Xp3X4D/mfIuPhnPFGy\nteDDSwso6OnePMghzL3KS8oOl5hzgF9HtKTxKAiE1eNOJTu6ndZZaz2gMsvj\n1CX6RVA5jdDnxcrXd4rh7l7tHTI5b8P9G5Po4oyla7oFE+bUzHU1+rmjBhGt\nS7ex\r\n=navv\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "date-fns", "version": "2.0.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "c5f30e31d3294918e6b6a82753a4e719120e203d", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.0.1.tgz", "fileCount": 4016, "integrity": "sha512-C14oTzTZy8DH1Eq8N78owrCWvf3+cnJw88BTK/N3DYWVxDJuJzPaNdplzYxDYuuXXGvqBcO4Vy5SOrwAooXSWw==", "signatures": [{"sig": "MEQCICBMwTink+A1kCdjeLilNQZPrrQhibXAciVSEXB++sE4AiBSr9Oknm/1cKcI3oMPmk4VqXfiY9AY3DJm309WUvaFcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7879558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdYA0zCRA9TVsSAnZWagAAYAAQAJ4ymrtTcIaqjf+cH5ND\nQD8EZOF2nE8xk9KeHyRpgawN8Z2mXHXZZreXkpEaROedRP2389yMODtsA/E2\n4VL1Y4RLPhuaGuQICAkzEJtulat4IErYQz9dtJ6xEBRIFTwigu2FJ6B6z9xq\ngQcLz25i2y9bmDMH3fojTDoo9pNYomSK+L9kvxr172xn6mOwXvYeLxhujRWi\nuG3o+tuOfbLys/Trg02xVAgx2JnfsahOQcThg8ElgyHQrDxU6Ipz5O9ISY1M\nDylv1uZCpS02P6cXyvEuGqixKF7lcSHmgy7jPZjdoNrj5J3k8gZ+PX7sIqA6\nk2V+FEvc8QuRGfFSmOHz9UY4ydgN7j0YfrbRQFe8IVtTico5H/4EQxzu7+ZD\nISmdbcbFYwlXUru5NTUPDBI9ZGmcu5damKlXuF4FjRfUbjlYcQQZ3B3sT9d2\nPgqqCMUsERenFMjRJZeiyYFvyYnGCFtOTQN2p10+e1JhRAtSP/x1QhMNp27C\nuymKmkRjj8LN8g68Ay50TWs3GG1JQrs2Wozz0+H3JFGN99rBUKDSd+F1n59m\nEoPsU8u7gr1lMKHr03q7/9CiOF74NGI/9gukSW01V0lb3a60/6OSgvubZqKU\nNXnyIclh+mlRWKwOb2flNvW4p+fIYVFQ0uJYceHlEHxNHl7Pg5Qf9sJxelON\nPcTe\r\n=Gf0G\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "date-fns", "version": "2.1.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "0d7e806c3cefe14a943532dbf968995ccfd46bd9", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.1.0.tgz", "fileCount": 4036, "integrity": "sha512-eKeLk3sLCnxB/0PN4t1+zqDtSs4jb4mXRSTZ2okmx/myfWyDqeO4r5nnmA5LClJiCwpuTMeK2v5UQPuE4uMaxA==", "signatures": [{"sig": "MEUCIBOZpC1WFpDqFZMct76GCNMyCMT42/DRyKGEZeyjaRZ5AiEAla7CIRTW7ULxpMWwRMSi8yDalPcwuLFF7x6ZinEl6QA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7996413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcnG7CRA9TVsSAnZWagAAHc4QAJOoRzMiRhsMoQyQmqs8\nabE/gTEllZ3IuCC3iqVXobU4AxySDsqiXfrKKJsQqKITmXvr5FFEP9uURpGb\n6LQzAMS03fl1xD+3FMJvl8BZeKwRfb9iIf4b4Jh5uwtD2eDq7qV5AFQGlvm9\njc6732VyOkrjUSSjgChtN26cd9Z90DJrRioqK+y8L3AJ8EEz6oYaoNuwHDz/\nmmonGWQhZSHSlNa8LnJTJLbPDCBvbLGE0mPqNRSDSJ3cA4WwDBD139xLsqj1\nDf1UOqAiFBfe0o2fYuBZeMKmJRSS10Nghrv3zwBgYnFAbapUhczLIQmKFnDS\n7M/zdSkU49j1phQfcvPm183xP5ebdg3DMqRZzndaGe2MZHVyI6D0Kc1diVHC\nvb8GWhpSm1evIkuFrsX2PzIQ849uyNrkJMK5Ps6pId/E2TQOt8Bry02IfQPf\nBC1j2VNpxrBwLOqLf4hoHPCe+puHaX77TpXHxAgjisUJKP1pBjX4l9uTJc/G\n8nsfTRHHLu0UzemsqyxqnfOdNdEC7GXXqz7vZ4Pj8dh6z0vMVhDXioEfHGly\neGMBeeIUA7De8OPQar9mDQHwrkDduvURWKbKhTijYSZoS1iVSiA2DgpPr6Hv\n1mMQgCv3xXHHPzU75MgCA+fG7EnhB76K8EMfGBe+ZA7ggu0EOtJ66Z/n1+hb\nLaXV\r\n=MbTA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.1": {"name": "date-fns", "version": "2.2.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "b3f79cf56760af106050c686f4c72586a3383ee9", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.2.1.tgz", "fileCount": 4106, "integrity": "sha512-4V1i5CnTinjBvJpXTq7sDHD4NY6JPcl15112IeSNNLUWQOQ+kIuCvRGOFZMQZNvkadw8F9QTyZxz59rIRU6K+w==", "signatures": [{"sig": "MEUCIQCmAoClwlRahGnFM/bhJo2VWnROciQT07krWAddVuXLygIgbds+Jue1cAfGXV0U5d2id74UvWc4EdWv/a8ZbpUV+0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8378308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdemV3CRA9TVsSAnZWagAAp1kP/jKKqpr2AUk9pmrVmS63\n/rZHCfPi7YEkZmwNjVSZflbC/9wryr5TSWWJh/Ic2/aQPTVk5wkpxrzMRjtr\nBt4SeX37vTLvjWmJdO5l4xVPa5cBhZ208qR/LFhisBpOArj4LVQEPqzeo2i1\nZ0VR5FXjNDXWJlwaN0trhIM6sxBsabbMPeadrzgyVP+yA0XDZHeAA8yQy6Ck\nL4WXS6ugsJXuesBPB21vxs2kbHHEdC9aMtl8eGH9WOZCCOVOxl+nzaax6usj\ngiVhwE3eEJ7AsaspbkQ8OmzoVJ18wWi119sJkH3Vyt4fr7QC/lrzSWOKweN0\nNxaZvW2ts1sb8CsWknFk4DgINhyxFHDqmmabHwMDkG59aINffMb5UahFrJWb\nxTfp4fzr8kRTW+4zZJC6CiNMIZRFPiRkqq5L+MpkF7/zQAnLzV/h7tMydhEO\nyc3lGHo9Psz7B+kdr55BeUk63AYptqUq3L656fkPH8r/oF2iQZx6rfVJfNC5\nzz22Wyy1gEmUpUswopBqpfLFtKeFnfnhpyOEBuLsH/b1oChbHbO4s7Mor70M\nMszgQqfyCs46HM8LQxXlNqAxtLa0G6sWuZch4CZv1AA09jPmNYV/tKrCHBK2\nlNA3r3miO9ivyuUiqPAwsocjEfXCTxHELkCi1gJRj9ZkbO3RpK+/p+68dVMz\nCc8l\r\n=x7GN\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "date-fns", "version": "2.3.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "017eae725d0c46173b572da025fb5e4e534270fd", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.3.0.tgz", "fileCount": 4106, "integrity": "sha512-A8o+iXBVqQayl9Z39BHgb7m/zLOfhF7LK82t+n9Fq1adds1vaUn8ByVoADqWLe4OTc6BZYc/FdbdTwufNYqkJw==", "signatures": [{"sig": "MEUCIHvrbQVnjQ3fLo/eAtFlEUit0SVO3sZuVlmc2kb6HyxvAiEA/WJsKcZ+lObwrtL/ekSM4tLiO5oHvzmTSpNkIhUjM0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8379812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiigYCRA9TVsSAnZWagAA7eIQAIY4J+kTFWOA2FDq22EU\ngopUOxWH59NM6bbNv8aAJnMsvwH9VVIFEQIv/+PiMCQWzEahDyU+/rdjazZW\nNmkbZ1QEnTYPxROVfcEtX1TZKY1YoF62I/4IycRnKtlGFeuYs5wT+sWvIIFx\nF7t+FgKRaxl1MhefgQPS7GvVPKJf+Dtrx/rfBTgCzsZL/WV0kC4pcTxEgw0F\nIxF2tChy0pivevj2DwchJ0wXZ1/QoVFjl5L2fjGtdFjLsqdQaQBdzoY/1Qo1\n9nswDnYLT7/v+KLdcf3K5ipvmP4NIP7dTbNMUCQhpegYjWHMHZJs+tT7Yjwq\nOf18mMYP7JDLBgRVrNd0/jwBQOcVBceeIRDNgOJIRbg0yr1y2CAARhwoI+03\nDghPeniRssuei4UKskytr0mkkaW6aY6h9HIeKR98qqYoqwjn5u9PRirFc2E/\nv2bguVbY9MAxZWI+YEQapOUGCW5A5aK35cxTUP3NF8yJ7ptj/O3FI+s62t4J\nvoyliE26bCn1tGkATf3BbhrgWxU5LX6HKOm4CO2l7R/j2CLTzJpJIml7OsNw\nq4S5Sm8yqvcaq4uAfmjukcq60yxFI1MAWfgwK8+EDITHz50ik8AJKqIdhzQO\n8DLrLQp5dFmCnmOQ0+6yC1CgiNBiAwi/APvUZafpVOcdqu5hvqLv4OUkOZP0\n/tMy\r\n=zpF6\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "date-fns", "version": "2.4.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "e02d1d08ce80ae1db3de40a0028c9f54203d034b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.4.0.tgz", "fileCount": 4126, "integrity": "sha512-xS547fK1omgCgOGbyU0fBY2pdeXQ9/WO/PMsVgX1jtF56dXNHrV3Z+GKWIOE7IG+UEeu+fTyTlnIvBKbxXxdSw==", "signatures": [{"sig": "MEYCIQCtqh90r6zyM8/zqsjUbHgQ+LMniLK33q/V9ej1wSJoGQIhAMVewKJyYymrNXSIeOiHT9ReEEvDXl3pn37FM61D4jwf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8492307, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjhzyCRA9TVsSAnZWagAAiYwP/A6BXoNTzIGXZps0onqv\naie44I1Hf4M4mHuiuNlNY+aU6oZOOixQeUtTIfNb//R8WwS9cK4+Js+HMz62\nM0Jk5eXlRC00Xxy72LvvhecaSbcxoOB7e/h4azxKl5zzxiuWtFHHjhTEFso9\nCkRyaIuSR+sGwAIp31KYguSOPlLKyo5PRkQySOFnKPe/nCOLHNtdIaprDQmQ\nreUd/GvO0ilNEdsx6soC/21pnEOigLCr2yBvkV84dnrup6M0Oz+t8NDMwrR4\nFaaO12MHvUbYqkH9SmS3SougLPb226/lAXcLvKM+vLycguovt/tcMsBqwp6D\n35vzakdnhgCyyzDRb+lO4h9TQUdQIwJqihbLpLa9EcHmwhB6TBuKBnzKun5o\nZquZ+SdLeAErQMAX9bIIZ+02ewyoNpy+5njCfyRyup9KM4M7fdOh0nvZFDPu\nJZsvwtAwQ4wDUC5duv6qRUv6QLX5YjOCKNWqsmtt2jd2aR9xamTev9V8dOqV\n5/iOIJ9wO/0A0/sipMWHAXTEqbhvx6RuqOSWzKYQ7dosxKWLgUyEM4jV3wYT\nt/sbWpXx3LzYbgiuzd7VZXoD26agJ5JjiMHz+oDpl65rTr49gYruZM9Dlqzp\n46enYlklTc8IMiS7pN0Fkg9/6PD/D8ei61LbPxDa0zP+ppnRkBiMvEGovsUt\nYNiM\r\n=zd/m\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.1": {"name": "date-fns", "version": "2.4.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "b53f9bb65ae6bd9239437035710e01cf383b625e", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.4.1.tgz", "fileCount": 4126, "integrity": "sha512-2RhmH/sjDSCYW2F3ZQxOUx/I7PvzXpi89aQL2d3OAxSTwLx6NilATeUbe0menFE3Lu5lFkOFci36ivimwYHHxw==", "signatures": [{"sig": "MEUCIQDRLlwJqQYg17MnaEXDT/daXeTVA5HEDHy8C53wK1gVJwIgTw5Y46uNnjluWgA2Q8yCTE9C1h+XY1taRamg+G1HU4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8492645, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdj4M4CRA9TVsSAnZWagAADYUP/2VV5cl4piirTbH3Bwu6\nGFpKjoXQvrfC6qzMfhQ540Jawip2Vx/LYZW0j4MqvGqpmbDCeqDS0XPpzn2e\nueOuvRC/73FgJfeZ/K52RQBZRYGipeyL8gVCKVR8WQVgS7pwqtOzNlFM5fih\nAfJrEoflt85TG4x1+9n8TrWlyYgzeONoNhsTYFNfWZX8bXEJFvsulB/E2N2u\n3vchcgo1Wk9VlwmhY3oxWExU3C64IOEsDsFomdUlam7mFh043IUEbjnktF+a\n8x8j+rcMprAvWaDlV+ZE8ThCE5M6p8xZ3UeYu9u2cWhPtvdL6xiiQugRz4qO\nteT5/kb2biLiKsaKyETXaMlVKwDs4v4jlzCX0FaAlAqiXBF42aQFVgLPNkzE\n1MgsJRaBZ81DiIdJ3T7nFpIg8uU7DG7vSqdZKJLoAbl1ZRJoLtLNyxVSTX0P\nmwOwm1BLgojGXB17F8iu0cMmbnOkxuOa/eRl2Po3RkC/9YyCACUz1cQyNIiz\n+inJ6PNBzpsoftK4N8f8h5nal92eZwxudoCQZl3U6l+W1SwGhttqcZ1tb1uM\n+QZQh4Wu00cY1pgamXYJMCbdESMa1h9kHA0xD9GRTFUa6C3qRolz0qvBa3eG\nejSApj6mluvIgrGW0qkhZsxla94YULWxHanAkD5Y2KGE0HMHnCLc9m59+0P5\nGpBs\r\n=s5nV\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.0": {"name": "date-fns", "version": "2.5.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "b939f17c2902ce81cffe449702ba22c0781b38ec", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.5.0.tgz", "fileCount": 4190, "integrity": "sha512-I6Tkis01//nRcmvMQw/MRE1HAtcuA5Ie6jGPb8bJZJub7494LGOObqkV3ParnsSVviAjk5C8mNKDqYVBzCopWg==", "signatures": [{"sig": "MEUCIQDKFnHLMZuRvlqhDuhcZ5y41BBNmxl6UCpfiQxvxp+F2QIgVC2rNWXlIcYeeHV1u8jUmuwEQI8ruizyZ8y0ngspGtA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8927717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpr2GCRA9TVsSAnZWagAAiMEP/j6xzqzwYRGtIAIEu9ik\nJ4r7dlhXaoczSiCN393lIaT0gnpbezsVnU06HOzWmtPzyRvd4aqS1+xyXQoo\nlVzk320iqM7VX8jUQnaHCGVhaFm5rpPkPosXdpxk2MrBTkvLuexB9lPKyt+9\nMIQEtREMGkAKAS1R2SMBb1bwAF5YMWW5ITJRzb/LFZSJ428ZI29wF+Xq5LiP\nisdU/pgxasi3+hcVZOw/NoKTcM1gNrXzAJa+i10uxglmB2OcRsZLnQ0U3iKj\nsQDxngbZcNO4sx2wVzljE1nrmYt6Q/GrqGOh3f5KZTJPrO7mnDupgQh8Xzcm\nPvPdlwtLWWeTNu12I3GwYfqH4y4vNbB1NjOULU/dMiHqLdq4BQt+frEBG/rJ\nxLP6AmVmnavyrQmWNqh2s8TNv7hCc4c4yZ3Bg9OYka9K2A2UbCjvdk2HUpyI\n3bn+HSEug7bTece1d4S4YHGUTOqFndQJHzSADRULRB3SsQ4qZ+9TEA3O8Awz\nr4rIddDZ9A6eqLT81w6emdGLOapUsmw8ZQxa5ljNdYxDgkLl2VJ391vRmplV\nS6FwG1wzBmt4iuoYUUxyqXzBmdbo6XmVE8Yd7VAEhts0E9ghSRAYMfSsbfbg\nABGJR8IjEKbvzdzMPN48qZqilHgb8xyVdx/8PlMbAuYSpGOrIuJAp9INfhXE\nHXUh\r\n=/xbe\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1": {"name": "date-fns", "version": "2.5.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "6bd76f01d3a438e9c481d4c18512ddac37585b4c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.5.1.tgz", "fileCount": 4190, "integrity": "sha512-ZBrQmuaqH9YqIejbgu8f09ki7wdD2JxWsRTZ/+HnnLNmkI56ty0evnWzKY+ihLT0xX5VdUX0vDNZCxJJGKX2+Q==", "signatures": [{"sig": "MEYCIQCx7rOtu94hl5gE+PGoaSMUN8dz1kZ/KZj8NMVQQ2JYfAIhANR4r42DXb+9O8vHhVDds0XPuzTVKBL+N4YmKAuKT2eT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8928083, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdqYXDCRA9TVsSAnZWagAAlzAP/0KROrtAWp7ly434E3Kq\nDTUZEy1N7OdsN3aLctZf//VNS4CXc3bRgKY5txRuzmsrEycuy3qjY0YJGa1B\nhwKYEBr/pJyeKJXKUAQiUVzSoib2tKDQupqZBhyeiiRjkERP+2oa2FYjUO+T\nZuh09QoNBaU4Ux8RaPB/Valfpk16AloJZVOxBcnmtzeSlqBNOTYj9D/aEigB\nIn9JJpKrWedo81JOtocIdtDep+I8eE24kSfF5Nyck326nIjYtGX/vwI5nCYi\npJ6YVWY+QxQ/N5xU3+Hn2+Hh0/Yvpflz1DS6l7wvFERYPT+EsJcKSn4wBz+J\ng0Jsz8JQr0LejiU51N0RRzSUn99mw0aqC+VFr9yorsEV6dukb+fPcMZGBlT2\nev8VA2EZlAbrYHIfTW1jSz4N0a/+J9E0TnuQmA0FY+bN+aXnYOOXFqKZUX0m\nQWyN0hwjPHNorqnY7SnI6YR/WKOXSBGcO/9Nlb+Kf9Xt9Kx/60hK+ufOSbma\n5VsZpZILMS0sY9s7tY11z5Ni/YexjZqWgUCjysoTW4+svBEGZj2wwv8jnZXM\nWNGXvRIPC3aebtC5EBV3oTDzMVzs68fTP6081PGg4VXg6aJUeDNrTVBamrnH\nt8jsvaF2WfPbw/QlSEkzIUcj8PyWgN4iutoo6FzI0yFMXdf0lentFArb00Sp\nfVOb\r\n=KvPG\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "date-fns", "version": "2.6.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "github:leshakoss/jsdoc-to-markdown", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "a5bc82e6a4c3995ae124b0ba1a71aec7b8cbd666", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.6.0.tgz", "fileCount": 4252, "integrity": "sha512-F55YxqRdEfP/eYQmQjLN798v0AwLjmZ8nMBjdQvNwEE3N/zWVrlkkqT+9seBlPlsbkybG4JmWg3Ee3dIV9BcGQ==", "signatures": [{"sig": "MEYCIQDTcacbEu4m+w+bne74ZdD36ohK2GMWhxXBXWWpJEPPVgIhAJrV28xeQZX96C+cfViMAJgPlHfHSsDH95fikj39bF5W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9163058, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdrv0MCRA9TVsSAnZWagAAy18P/0jZT981FwR0Xff0UTf/\nQc568IHUx3V00FKRK3ZVC9JAoEaTTBOBUt8bJbMnQXT9JuXkhsJ4Q/gi274K\nptSijtXRBrQV0VRjGPY6pMr52Zd1ydVvSoAAXbLDK+/TgYNY2LlwO4t+Nfcs\nRCPdJe823syOZUwLFyOdeGSrAwOTmS4w3VlckTwqEDEOWvqxPR/Drcrug9Nk\nZ/X18aI8cXuUhtcAgiFT20HhuvFnvI953jLhvhxoN5uVZLnGRdq6S3aR6fqy\nfCO6eFdOyMScclbr+GYvEn7kEexF7e7K1BHXm4dtb5JW9diWYBBMSz2MvKQF\nC5q/I9I4tYbw7pxtmOzun2z5cqQ3zFH86p8UPJlkZyIe1OCh6xoPkoe5CT4j\npFIGCnRcjATcsyiBUjrTk8zua9EK6s4a1tVnrJkUmhWK7wyVYM2uRgeqMJj0\nyfQzVFkWacGrAcyiCmCO7EL6+buUJ9s6fLWZrJT375zYAx6Mn6YaPEfftQSs\nGZtWoDrqqdQ7rKpm23LnMg/fQ+ZJ95kfETY1Rfvfwls0MnwP+P0H/GLTTbby\nczAFuX2A3s5hs65Pjgotnkj7Fkz1V/mgzRUz1hb8T+kiPoaBYtLHizcbeLXl\nfix/YGBZbfN8wamNI1c3Gs2Gi+pYaOM232hLqV37B3UPz4Gcn8WT6P/0x4mt\nijjh\r\n=+kYE\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.7.0": {"name": "date-fns", "version": "2.7.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "8271d943cc4636a1f27698f1b8d6a9f1ceb74026", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.7.0.tgz", "fileCount": 4263, "integrity": "sha512-wxYp2PGoUDN5ZEACc61aOtYFvSsJUylIvCjpjDOqM1UDaKIIuMJ9fAnMYFHV3TQaDpfTVxhwNK/GiCaHKuemTA==", "signatures": [{"sig": "MEQCIFhIwg5KS4i0QraPnfUkuU4pkoD77m2rTb+K4lROCCyPAiALVNufrWPWq00mjqMLBdyuOEe3TPioZUwDZyV7KBzS0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9575258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdxAXDCRA9TVsSAnZWagAAkoUP/iTBgQeDi4j+fti/tCx3\nRubn9jNtoROprILhhdQmfDW85yvxPp9YI3jxpzpiwSx/MluycurKxSVoW5KQ\n/VbvOAdC/62UUxHItTqCr6oVbcTMoR6fVNxDvInNY2D0y0xJUhXphjf0KJRF\nmYBLpqWZbXrcBU30hfXHIaNThsqMijaJ3bnLZP/DZdadac00qWZNQiw9LUeh\n7C6zBV9MEuSLJe5j5efGSrNvqADvRDDTc3DeANbPJP452tZgoS9fVS4L24Wd\n87dEvn+a7ZPwsnFgUAthqDNJQoarOYTkwH1WkQoyYq4ImLVRcycsM1rQqMe9\nKLgd8JyWmvkndPKGVfFIlWPcn09OM1HHtnKx/biTVaNHh3B20rAyYM6xu7kB\nTAGdRcvIlab0cmYu4ywoveSOg1znYU5v7Jan/5zrzn1nsbI8MBT+T78RSf/Z\npMTjcIBBVOMMUisHoukqEV/2H3IrQP21VGSPt1p19t6uVRQtXkG8EXa3Rmaj\nNoIi2GGbHpDtKTUFDCV6T73jeRysRLlGs4/PMfC5fP5TH7CSgbUp9NMgKoyC\n/twBM56A9Fpo4e0/IB702emSYPc9pimqaJ4kGkqI0V37GMP/3MFU6ubcXVE8\n/uVWDu1zhmN0IHtbtKh8nYJX4IP4hcgvqJ+2GYHiPFzX366/hIcc/at6xQTP\n3rJr\r\n=QVF2\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.0": {"name": "date-fns", "version": "2.8.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "628d865367e30e45747ed1e8b0c1572090b04f55", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.8.0.tgz", "fileCount": 4362, "integrity": "sha512-nbZMIMsoD7QiIKipZ5+XRTCtHZad1ch8OEkLaJxjGL6ThAK2IWAdjmAUAS7Fdz5fCaVWtqc+c8pAsN/MX8eaew==", "signatures": [{"sig": "MEQCIGU4kPKklZuC/1tAG0R0eSSjIjQPN2f5JSCqe7BRoyVeAiBmZKsbl5gPcBi9iwu/3ja1/V5IOowsE7J4OJswo16Zig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9753488, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd07XNCRA9TVsSAnZWagAA9gcP/2VwloL3RNAiYS9KpHZS\nHqSp0aOY34VtesB5didSWWjoTM0cG6h8LWvjLI1EA9l0cPhXFOSB8OTyaXYv\nDPoWj40GNBoBv7c1erz2CybwRAkfWe3s0TZeK/N8cUcU3JbdxEf+hMs+cA/5\nQOpNryBHAh+CrmOZUnTPoaC+EMWvSS+TvYeCJuMNIo6Du7vUXF45vwb9E6YN\n+RANNtXYVpNDyE8FOZkN0D64UfY7XyABRygNuklHfoHDHRY3otCPgINqhNhM\n9261eh/wkGc1TCZjlO7fuxQRDLa3gSL3Yl94KC1jzk7DaWrqe0KCHsLIKey5\nQSmBbMUA8u6VQHnUfgoO8kVUiZWrFZtlMiQmQ9nWERKRVl1FoM/EHIIAm1d+\nObkG2Hh8qz+L+fJ2Xps0apFFAs/fqZ01Mm8xiE8b/EIKtjB048JayN/31FJk\nTpHEZwHDa+LFth3MhVRs9IT7WUMIejUaYQvRhOFJefdaSou+Xr5Pp56bXK/b\nOIVxFJWOENCCMP6gEQPCtzh04+c2apo71wGEXIfoGog20E0SXVEVCFlgAb3K\nWXQsbXP0ofpDd0F9SeHuhijt1QtYreJvT0bkZ64BqEYaa//3heU0oV+x6BQO\ndjLk4+gU8SKnhFH7Nz0CsMlcghTaNt29ftSJQ3FR6bcQeruMTG7PT0ty8c6a\nLmKX\r\n=YyQj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.1": {"name": "date-fns", "version": "2.8.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^1.13.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^1.13.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "2109362ccb6c87c3ca011e9e31f702bc09e4123b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.8.1.tgz", "fileCount": 4362, "integrity": "sha512-EL/C8IHvYRwAHYgFRse4MGAPSqlJVlOrhVYZ75iQBKrnv+ZedmYsgwH3t+BCDuZDXpoo07+q9j4qgSSOa7irJg==", "signatures": [{"sig": "MEUCIE5p/cw/7r9Zd2qc8pifsm4Kd7GVIygxQy0HMV1fYDhwAiEAo2yo52ff0FmlSYnAjnZTVRAlH3fCficaqPXaY76oIKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9753854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd14AfCRA9TVsSAnZWagAAx0EQAJpQ7BncVsTj1g+//2xv\nKrswSskx/Tz7Z4jU0ABpj3neis9WniDQUoeIhVSUVYdHqBOoUjOb0lmtbkc2\nn/fnL7BaQIgqLV8u50uNsq7UZIr0qyUtQPIkCI0zlfc02cyug/Wp1AmPyauc\nsjalhtbV7mKlGPxXHrPRGmDuSDA0DJBIpdfxC/DaK8ubxfjMGHbNkAJcno5k\n+jqQXW+e56hyQR9drvI4kGE/9oX6iCBfvkFzAlDZ0xos86/l47cV463pXpFL\n+q08wgR31SuL2g37Op5i1+PvLSsOD7a8DkwJoh5AhGtWZYTZiprzJEAMp8XX\nh3VcjuzJ94lbsj+1sd8Gid+URvhHrHs4Lr9Ick0mDn1LIYAMPv8gnztzP2is\n2vR2yIcmWbEwF4j6FNdAcacGETnas+bPmP9OluX4UUauPG0BPxe4tg4I94EE\nWR2CMjiN419/cbvmQ+aHkJgwnBUjDXbGD81hz/Q+4Atu3YRjHO49madyfnd5\n0ZBbxjN2ueXtsgWzzlRoU84eErpCSSQxkUkxGO3UbkNFHQfkoTD8M0FORXiB\n/jpVL+21jJOLRt39ZCSjiuZ+Lm/Yg6e0b2XcDAhkSXCsXgY5OKbeDGdeJXEH\nqwoJlWnMQPCmq6lqcPOgCReA3MP6HJCiHIuRmYXYJO7kLZ7GoW5GFz1fpufF\nO/G+\r\n=GuxL\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.9.0": {"name": "date-fns", "version": "2.9.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "d0b175a5c37ed5f17b97e2272bbc1fa5aec677d2", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.9.0.tgz", "fileCount": 4493, "integrity": "sha512-khbFLu/MlzLjEzy9Gh8oY1hNt/Dvxw3J6Rbc28cVoYWQaC1S3YI4xwkF9ZWcjDLscbZlY9hISMr66RFzZagLsA==", "signatures": [{"sig": "MEYCIQCU6lXJMFUdttaO+KgbEQE8ESzi4QFUJg9h2Ij68kTrnAIhAO5Q7n6I+Unb0X9o9gai0I6vfQl7C9qS9/bzpmLrPROB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10179870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFdPZCRA9TVsSAnZWagAA3rYP/1MxN/Ljt8hpifes31Mh\nJlG9vkycBqkkDHJx124QMuy7o1U5X03pHXboBWBC/6ut1x3o4zsVpOWuHWYO\nebuvQuX2SM5UOo++5wgxWaBDWjwff7Mo1Y4Ucwpz26F6ADD1ET3zQrknpfE+\nHmSBerxufoWv7ppFnIT0cnbeAsHZew+CbzaxABHdBa14O2bfG5GBhs9Hriuj\npTmUgFoqlwg9X4TNnuPji9IhV129BIwdvAffVy7N/lQH5WgRsDbdzy+mLqvk\nvfzOg4lXYRrgeUoMRbszdgiORM2RpsED24klIlW5vzbApid+BXFVqxJS4n1G\n05EhTYDYxLE/NrraW9MTIajkz1YnRy2pRp4dJfaMV7pzeaf7SIcjZS9efX8q\nCv07pDhCruqYPGmutHTu9nTIyPTVMzmXcBtBIOLmQ9SvqC2cPZzaP0iuCqhO\nE+VzxcGiFksi0/LppvFYm1fSpEObjElMQxlKQWxTMAi/c8Yu7h77e85lNHh8\nkrIFIGX33evezcF2EES2vZG+XeViS4Rtck41JowRo2XUp/GBG4zvUNLbYFoM\nXZcVV8V1qzQn/LE1PAiWH42aCq2IZcup8p9/wPH2dO/l4MgaCaHgG4QSHNGJ\nfimhY3X/f5Ks0mGazrTFi+n8tgbzUOeKXD7A+aQRkT5zguZBOfTi/Uxlg80D\nplXA\r\n=sz0x\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.10.0": {"name": "date-fns", "version": "2.10.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "abd10604d8bafb0bcbd2ba2e9b0563b922ae4b6b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.10.0.tgz", "fileCount": 4493, "integrity": "sha512-EhfEKevYGWhWlZbNeplfhIU/+N+x0iCIx7VzKlXma2EdQyznVlZhCptXUY+BegNpPW2kjdx15Rvq503YcXXrcA==", "signatures": [{"sig": "MEYCIQD7Z5DEVS4NwXBN6g+G97eYUQoo4um776fIbVY+FwcsGgIhAP7f8l0onxA8bQbYgH6u5r3s9zHMJCS6TX4XhfrwyIGx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10200613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeVPwaCRA9TVsSAnZWagAAOhkP/AypHRnmYul9P3F6vvlC\n04NkN6FNTe6YqVxI89+BFs0NfkwmcliMLY+TyhgTmfRAEimr3HnSB/AuHmdb\ncAmall5AEyItAc8Sil6reWrF2MXYcobDDPdue1cOJDL/Fynq60zyGDKvPQau\nbRHYiWhhwF8qvVf+w8S4xWwoiFbRstz3trgc5qePOZVBQVQecokycZ0pJy4C\ndnvbBisUiLIYXcBSGqI7vCUbJ0JClRqJdb/C/sdDqpc/UEhN4CT6ZDkDPPsQ\nBYj4MwujLTFkc/rqmveMvmABmDffwf+SNshjCod6nbEU3/HtlCu35AIafO1y\nr/SYPW6iMhPoNcANiUcGxxvIWp8oggc6Ws9UHUfT4ozUfDc78tEYaziujssK\nnfhMs3gdnnn8TzOntOfdJMsdbPBPWUBxz45k2GVNGW5iT/csbP6y0/Zw2zrA\nb1X0Xh9SRD/IEsiX2XnqKd6PHXAPejNRYUMbXwFAI4PYH5vRaMeYLovYe1Lr\nefGWaQr2h/jOWy1C0JDkO13BQyVWfpxsRqkmildh95W2djOsqLIufYaAdb8x\n5xF/d/kzm/42oroJeUIFeDtNmiQ/1sGxct2f9bpsG85/6RS2kgUsJMz/xLp5\nacOJydNom5iEcJ/ThfBmwOeTjV6dlRKRm+d6WcVF9kciGlXoo9wqjcsQXyvc\nwAir\r\n=BBJN\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.11.0": {"name": "date-fns", "version": "2.11.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "ec2b44977465b9dcb370021d5e6c019b19f36d06", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.11.0.tgz", "fileCount": 4540, "integrity": "sha512-8P1cDi8ebZyDxUyUprBXwidoEtiQAawYPGvpfb+Dg0G6JrQ+VozwOmm91xYC0vAv1+0VmLehEPb+isg4BGUFfA==", "signatures": [{"sig": "MEUCIHoEAj0F10wJPpggS5abq+6R3Uv1pmmnFIKuf8MeaFkEAiEA/8qCUAdn2frquNpm8u26B//8meBE9BRNnoR7wqB9pA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10500824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJea+7kCRA9TVsSAnZWagAAWGUP+wdB2eBzis69RZ6orUDG\noU3h0wbHUwkn0KMpTs/UgLztNDCl2g0RMBtGATq8yOPwCwt3KPQ/uXxisSFQ\n5vzHG3YVUsx0wZFTD4dcUk2R+Tmo3tuyUcI9zDadnUQ9rCB6SdnUPTt2hXgv\nJx9H2nWK8cALYG6C4gpWssZRG6HqEcQgDCG5ZU7HxLDwNNxwkHpOLSPhZNP1\neVhzzRbtxx9jcDf0jwZxjdN8A3g46Jv9aOqC+7dyYKoSPQrFgSTbyCTKHRaM\nejVZq5q7XfuPcPoo1hqyi20vdZwoKF7Uko41QwBf5HDHcLRRtIyPtI9dCu7H\nRKFSsUzMhEkvrmYMI+2U9L8iLqR6KSYRkT1mFNcFGKuMbOyF8adkIhuPMiSl\nihNrHgQ1w7+n9gAI4atIV1ycq7zH3RGP9dwRtB4v2BrX1/w2eGE4PR5kyQa/\nol91lP08pAOJyJX40tRSw66B/KxJ7fCA3FVdGNV279FXgGEzUGS93b9at6TW\nWaeMX8Jzfh/XLOrL9Mz3zW2orrceE3Aqa00FZFdB5yC6eOJpLk2+4gElFQt7\newEtsOCfVb1JqVWgvmWr6oSvDqP4Ub2aYqWUn8uygSgGvt35OTrEKxGVxCSi\niLnSXhKYOEA8GqH2cqJKZrkGCti8J3HqXHSOHHWynX+HR3PzwJNmLOslbKHT\nidaO\r\n=aHjA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.11.1": {"name": "date-fns", "version": "2.11.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "197b8be1bbf5c5e6fe8bea817f0fe111820e7a12", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.11.1.tgz", "fileCount": 4544, "integrity": "sha512-3RdUoinZ43URd2MJcquzBbDQo+J87cSzB8NkXdZiN5ia1UNyep0oCyitfiL88+R7clGTeq/RniXAc16gWyAu1w==", "signatures": [{"sig": "MEQCIAFecUL5E1zjMqQ/viJ3MUL1mKAKjk/Dde5Z7xvI9Rv2AiB78qPEMaBmVt+dCJRb/yBqKBN1S4+tAQamQtzAp+ugyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10504868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefPrxCRA9TVsSAnZWagAApLsP/iKcQowErvP6rtYwFWyT\nNCYIaix6SOAy7S86sUXHauC+OsTWmlsUNdbVUJhvuseznkdrtPKuASBeLaPu\nYwJBZ2BG7y9YOW5J/vNGhChPMY20ZchqRkgm+5ikoYbdYS6HIs/W/gjXmTFT\n8ebtT3JUfCfopFTQ/rzJXpQBWs7wqFG4Oy0s1naNW8mWPh4qRAaOatXCmjIL\nBY4i3Ji2WQ5mdAzYn8YBh0A6RlhS+YFxeMEN3TFwGgjnQSBk2yfLjYC3h/U0\n/+DQv5pbbC3VdhSfK1b3kWOjLTyqZJzTqp7gPmSIte+XCB+9MI5eWDS8a3+R\nbB4T1HMrtWAoav2NE6kouUMgiz79EeancvEMSi/yPbyqiGRWk7cgPxlUJCUy\nLCiDVyGvpXSXwcpzqRJU7e4EJ0U0BnfsRErdUaMsgm5p8nHFNhrnv9aDK17z\nvkbwk9xpN0Ml1Si/fsQrBFoQ8nJgEWINI/m4fNN6CJEyVk2FB3+mH+9Moiol\ntRZJatxHJlWQVuiDlp1s5fPCL+vgxCYHto+Tn+x496Ci4pH+Ray2MtpksTfc\njR0RYA6uhMMdD+VceYHdaQEJS7ZgLSBV5xiWXZOC8VwD1rsfivrCwujAAcWu\n4/GTAtle61UkI9V327bCb2axtLP7JdsXI0lTaIfN77RGSs/Lq45IpdJAhV9J\nVkYU\r\n=6jcR\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.12.0": {"name": "date-fns", "version": "2.12.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "01754c8a2f3368fc1119cf4625c3dad8c1845ee6", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.12.0.tgz", "fileCount": 4552, "integrity": "sha512-qJgn99xxKnFgB1qL4jpxU7Q2t0LOn1p8KMIveef3UZD7kqjT3tpFNNdXJelEHhE+rUgffriXriw/sOSU+cS1Hw==", "signatures": [{"sig": "MEYCIQCgb/3fHi7pFLpI/UKo9Z1Qe98LQL3SPkFziclRJu0vAQIhALPBl3PMJXrxXgbVwvuNJ3pshRkRNzwj/Fhde7TAM1IE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10521944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJej18zCRA9TVsSAnZWagAAO8sP/RvRq1NodYFUDhjyf66g\nWS7LFSRxxbfe0XeeUi3IIJ6O8TTPvHX27QIdmFm1CKfwmfG0llcEUgcoCGHI\nqIRglVJUY0hlTJKFVMaxxuRRbsKcUJwLXShZaE8QTAweEpCz9udWszP2DH6r\nOCZ+4qQZpemKWflmsZHYV4yzyi0B4qGr9BLxsWVmI5N0fXL7cxL9HT4p/rrb\nlbrxdHmksHYbU6pchAXDOly+RhCkqP6jAf0UfsxTi3YGMdkjT2aS5PHe82fU\nyDmqCpXBDCI5DPSGrKSdYM1ze7ikh2QD4eGU10novRW7GrNSPMbLk4Sq4W1c\n3geiRo7eQ3JKtKorCmZqmeLO9P95NabczukPbjWWEzhyZXC1k7kQEzX46mT2\nlJ88HHSKkkhbsg4rLyWegcYgGJe3lzxd3zYqVF427MqiWgH3CffQ2dSdz+8E\nBo9nGoa+uOj6r9yJ9cq0expJs4uhAzFa6DgWWkvy6SHOq9+RWtLpcua8Eaa6\nt8wlWTEIDlaFzezERopZ1BznLDEAKet9E5WpurXIhPRpNMZ8WcjjIHCWlE0g\nakhpZa4kNlsHeZdV9VDV02wFtVGXpyJ2hUu1mAxkxsETmie4NubMzab89Zu2\n/1O+CrUGQ7wPMzmcyFCdMxrCPaVQF5XgSUZd3eJTyXkXGbTxC4njOhwMayRd\nsh1W\r\n=QaLG\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.13.0": {"name": "date-fns", "version": "2.13.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "d7b8a0a2d392e8d88a8024d0a46b980bbfdbd708", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.13.0.tgz", "fileCount": 4604, "integrity": "sha512-xm0c61mevGF7f0XpCGtDTGpzEFC/1fpLXHbmFpxZZQJuvByIK2ozm6cSYuU+nxFYOPh2EuCfzUwlTEFwKG+h5w==", "signatures": [{"sig": "MEUCIQDgRgwuOdj1jlSeToqDbNAeX4eWm7owS/VBb2s/Xe6yjwIgB00srpHw8m7peo06N/KeAYiweo+bXdkqnF16GOWshHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10691980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesyf9CRA9TVsSAnZWagAARz0P/0Te5JGlXq9pO/tohy/d\n7wM0aRJzybBhE9IfpsKcFy6CTOgDBF8QeGeDnumP0QJ0jn3ITSsmU0f3K+Z7\nEgf3L+LSkggMjTe8B4YFXR+mV625fgXUSjKK6hL+HQcZ4FfsoX3N+FI4oiJE\nqRED2gF+Soc1UCjZ7I0+zlGq7cY7p0ylWcSUqnzlPuVy3YCcbRSqZnmnwYrd\nDqgzNrxPV0SPxvdkXzjglM2sUlqIpUIIJf6q2oZtJallW55GAIyXVeV3YHTN\nzVrwBgRs9+xcwlbXF/CRmqlyNVoq3ORS5NOTk+Tt1SljkCBEbXCXEFuJ/GC7\nQvQ1kmzxSldhduA6k0ZDcjfQ8qKLryK3vmXnZ184cP+8wXJAmRcmjrBoz7Pp\namLdJr6FejItCmVDs5G88JxZw93Vm5yothruVVJoCqQR9amltD93s8Jh0f+B\nATJU59LFsLOBtydSBnR63D5tg1c8SM+jgLF2JPcFzqWVBqHXoFk5NeNJjSnC\nas55Qf/0/mV/5Ww+eRJFl2FHsAZGfJEhi8pDxDsoj8gTWo7E3zPkLVxcieYx\nk9FBQesE1HWPz9mIi22X9mGTXbE7NNT4H1+tnzQK8T+VRxH5YkJIea+nw7N/\n4YijkvZNTnziFBaU2UKRCpOSx9bKykVidXgcQt1bASRvvxgBgt58HvvOb9em\n7dsJ\r\n=U6vR\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.14.0": {"name": "date-fns", "version": "2.14.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "359a87a265bb34ef2e38f93ecf63ac453f9bc7ba", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.14.0.tgz", "fileCount": 4700, "integrity": "sha512-1zD+68jhFgDIM0rF05rcwYO8cExdNqxjq4xP1QKM60Q45mnO6zaMWB4tOzrIr4M4GSLntsKeE4c9Bdl2jhL/yw==", "signatures": [{"sig": "MEUCIBzQpt5rerzPeDsZdd2qiqX8Jukvz8HCzXeuLlHw5QUqAiEA+BcSJ7clzkiyetcnx8//Ifzazyov/wWnjut2DGHWyr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10985626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewsQTCRA9TVsSAnZWagAAR3cP/jn0BeJZAyipF95MtVbn\nDZGWjFfJwI8k56xoRpee702aecJXu0E/XvT5EnIBY1nnKkZDv44P9K9EsOuY\nTu<PERSON>J8YweHYPjFxK/2ALv+ls23kCn7xAHzD0UDPMtV+toahnkF6xqAbjgQSdG\nL4SAtwzoPG4VuER7Y+X78WsTCsAw8JepDCghTwhdwpSk04w828rwfMo8eWKr\n1lSm1nLyK8ABujTrvV7+f4TwgMaz6OVx+a4HNrkuJDlUrypNgq+sBYN3OsKw\nsWwnyHCahi2pIyDWwmqYn9yM9dmxutZJgfiGKZQJlK1PIRlKKGJGhor54hmm\nZ9G2otlJHnixRihL5wLTXcmRLXKnQc2l0U651vDp806HMoz6ZSnZTLjxgoA2\nhZf+6GGYFrnVGQkTvZxDUZIEGiLVxhIpj5WahhtaAQTJNM+EIs/9ti94BoS+\nvVvBpGR0NiYvPJ3PzTMlYerpR/HLwoPcW6KeIB0akswvWSnxn8yfg8epsXs2\ncyfUh8DkU7xCAbSytY4RcUJYtehIS0/SZc8+BHU8uO+ChoMPQ74zu2BowJTL\nyuLgXyMPLbU5HTpazgehW2+00TFskzTRePh0U1p5ltAcv38MzpFEvTHYW7Kb\nZ/SQBWnO0LkLs1ZFpycA1wKWsOfTsQGC8Hieo06JOS90wU4DIwnpX8bGHEI/\nUsqo\r\n=K5J2\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.15.0": {"name": "date-fns", "version": "2.15.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "424de6b3778e4e69d3ff27046ec136af58ae5d5f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.15.0.tgz", "fileCount": 4783, "integrity": "sha512-ZCPzAMJZn3rNUvvQIMlXhDr4A+Ar07eLeGsGREoWU19a3Pqf5oYa+ccd+B3F6XVtQY6HANMFdOQ8A+ipFnvJdQ==", "signatures": [{"sig": "MEUCIHE9st0gR06OPtkYXqbEBCGSc38z/49xbfbl5h2aIOgeAiEAiqnjPal82sHH2rw9VZ2/bCkIjW0JXv0lWSm6D55VnPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11441433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEcDXCRA9TVsSAnZWagAApYkQAIL+/BtNg2861YDr4AbO\ne7iT3hII3qxpuFdMZ8CdYIyq8JXg3lq0mBEfnwuU01Sw0+yicbD31ETEIuXT\naLpd2h4pcXfvVEoRRsdPSmrLBv6AuU4d4604QCJRzLUDFaVWeqhf3xNEamcb\nf9Tagc+euYplx/B054lbngjqZOnfAwWit9wG0XEYsfy+9JPMaLIExu/OQC8E\nMOLRXFl8ERQbFu2H9v4VjrJAd/PY9czRG/0TVLU2+6NzZLz1lqD7iC9nTnoT\n52JgB1Z8SavTBuFrkmHn/c9uPhlNLXOkk/aCmOmwXh3HhPeRIU24bkFBAK3J\nIoJShUcOnO3e2614tNwAWPiwUuOwa9WgCGYFgN+i5fIR58xT5V2EsOs5ZcAi\nrOcC2q8JU0sOx19OdtfHkY8r/c+OUYn5Lg/s+pKjPJNyKDACJLA92pOZnsNn\nPB7JAqigHVLzYwXwXlJ+nllpYMJmiHtCGH7ys+mcuE7cV8pMEcnqQBmwlLVy\nUDTv9fTrlKFS2EgxnT0j6em2QANfSHYa3ZsGYEQqOC0v6gu/YM4AQQN4Qh36\nToWk7j4IP9ln6G1dO2E+PHM+rKSDoIVusb7a7k1v3cT4662RhC+I6tEiH0N1\nKaaVlPd5HJLS3kqe3fHYsxiVtX7BLaBNXeV0B7A6OWNVPb7FawtKncX8oTaF\nL/8n\r\n=g4aH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.16.0": {"name": "date-fns", "version": "2.16.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "d34f0f5f2fd498c984513042e8f7247ea86c4cb7", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.16.0.tgz", "fileCount": 4804, "integrity": "sha512-DWTRyfOA85sZ4IiXPHhiRIOs3fW5U6Msrp+gElXARa6EpoQTXPyHQmh7hr+ssw2nx9FtOQWnAMJKgL5vaJqILw==", "signatures": [{"sig": "MEYCIQCi9rYa2Acpt+LpBAEm6n0g4g2Z4J9me5KV/e4xroJNIgIhAKZ0kd7KIFrjM8awndNxGGzcnpPckAx1e4dLrC0ERJ3C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11564999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfR7sgCRA9TVsSAnZWagAAMeAP/0iBn4hkTa2ZCzLVcQ2R\ncgDyP2kXlTJEvr38UpARWo/vghoGXaybjDDmUJvu9qWbibFfj5T3lNDR6OdW\niihSw09BdURKuxgn8vZSI5xv+RvNP/zVX34A60rezKBPLs7WuS7qF7790/kc\nzKz7PTexDdMZJmJfs84q7LGg1uO7GWcGxFfGm0rfWwD13q7HQjK6Cs4W8EHQ\ndfwKSaZ7iiYpXLKq16btkO59smWO9zpqyUArD+8Q/VR8+wN9uoxWopjZmwyu\n14sGZjaS5A/Y96uo14Wlv5Ka9DxmsXOeaoHOHjRQ4UmO64ukn3RCRXeQpEcX\nDhR4EmA1CJG1oyXVlLRjUogZmDbZdL7BRlTdD54vIfk2sJs5vR2XalWOn6XP\n/mEbEJCfCofhiYlLepKsK+640QvbfnP/VO2QlKqDRKgu7uBYgRhUCkgsntTy\nyA4T5QDwCFus8XFjqpXhGwm4rgvSsQvIjSaed1symjlleSGpWt6cpbE0+fKc\nDX4TnWvKRbAKh+jTlmxRE8o0R8g8urY2MXZ3mR69VF8uwCB/klMS2TaSoUMq\nMNu2Xk9d9NdhfUvyuPKZVVndfpcspMm9uhT1DDBeB6Zd+i2w+MyYvADh9uI1\n5xrdkafoZnkiEtRPlpgV41xGGFwsx1TVb7fi8uzu3AmtICzCDhjkXwVm3BFX\nF//N\r\n=MMR1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.16.1": {"name": "date-fns", "version": "2.16.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "^1.18.2", "systemjs": "^0.19.39", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^3.7.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "js-beautify": "^1.5.10", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "jsdoc-to-markdown": "^5.0.0", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "systemjs-plugin-babel": "0.0.17", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "05775792c3f3331da812af253e1a935851d3834b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.16.1.tgz", "fileCount": 4654, "integrity": "sha512-sAJVKx/FqrLYHAQeN7VpJrPhagZc9R4ImZIWYRFZaaohR3KzmuK88touwsSwSVT8Qcbd4zoDsnGfX4GFB4imyQ==", "signatures": [{"sig": "MEQCIG0yMcZsoQXcbsVYGN/mBXPaZpXJONqBnBTD88sWaDYdAiBVYSdEsetbJZSUjUfQKGTBIG7RNFOMZ1JVNCVxfXX8uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5526531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTQkLCRA9TVsSAnZWagAApKAP/ijU05oZ7QwqURRyNuCC\nMFi+pD5TciU+x0Wiy3fGBSNxBlxcdZ55U1PSTTGTGEicIcCHrzmfZ5uQ5UeE\nnYrHMg6D0Dtli5xgr++tSzMOkYus/WoY5gh9ueEmZsFYt+G0tEDLIg09NDSX\nGllblBFSPTrtXxdFFgUM6qJIuibkjJzcNwE9bpOdGdgp1h+sZwNoMOkIcj5W\nwxamfQp2ff8+kUnIzD/24XkW/7OjaD3G4kUUt9lhYkCajOi2kAnR0kKIyGNT\nlol/Ok1l4m6zMyB1WWkCmqOZ5vSl46KZbSW6yG01DIlpDq5dAco/wgEIo/gd\nkyp2PcA2f9Npz/EzW32x4GDLgQ18dWJTkBZBC6fWXrln4mTD1GPu9fzVD6ts\nRJtU28+fRoKDmi/DcuMCCKt96dELm5iG2bCH4whlWS/YLrUPm5f+8JyeSquL\ny3wvjs2zoKR4FkrT8L8aaWiwmkvcLdb6JtbTCqqaZVtiM6CXBqtbu8PUaB7E\nfjTRdQTj/yr9JewwVrgv2tepm1bmFPwoxmqspu4O/z7wHaVaVgojOs0SgSoh\ngFFUnnFk+00tWUt9gzJgzw0+E7h8yCmVaO5yQD89468bm38dD42cCCelBRM+\nn4fhnLuaAkBU1F4qSiC7Y33gOU35OkLmaJz4kLs7S3pACsGrOXQo6hxdnXO6\nh935\r\n=Ezzd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.17.0": {"name": "date-fns", "version": "2.17.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "6", "@types/power-assert": "^1.5.3", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "^0.0.3", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "afa55daea539239db0a64e236ce716ef3d681ba1", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.17.0.tgz", "fileCount": 4676, "integrity": "sha512-ZEhqxUtEZeGgg9eHNSOAJ8O9xqSgiJdrL0lzSSfMF54x6KXWJiOH/xntSJ9YomJPrYH/p08t6gWjGWq1SDJlSA==", "signatures": [{"sig": "MEUCIE7oAFVI09yqg/lAtaHumVNv8NrvtnKtCBHihXSJz6f1AiEAgw/579CkDESptWCB0XNWIrxhOW9M5kJ3RUaGAW7xbuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5525774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHVGZCRA9TVsSAnZWagAAC4wP/iKTbmV4GlTV9GSf3l9L\n3CsRBFsiEH8Bpnd1ty5L9PKNwrAqgsd8j+H2FKosHNoZ9rQRXuiomxtAjFOd\nYei1C99wo4HB17Mk5z70KVChqE7mxWvCoH/eyiGScdumLXI1UBpRrcHgCeY7\nXg6bCiwTIoWqX20Dt+cVYcoem2Q77maFh8jhFsIerHriMx4ememWy4kNKIgh\nqvleCFym8USipFR8VM3xg/Mv/Pnfz6DOJRcsMn5JYZFu6zRmN1WrLSXf/8ID\ntmjMilLVljaqJlh3NmP/f0UM/OpDyBpZNU3WIrjGBWL9PHqn/de3Q7xj598y\nG1TtfRGS0yjJfK3H1yJF74fvt1sY6x+ZEV5g86fCwHGdv5ASoOzbpWLWE7wF\nLtxcYVbL3hHeU3mzHNEw5ehuUwu1cT9R0giFxDrtEvh5VT0vm9JZY3DhzkN/\nfZA6Zt5w3C2N5p+hdTssN3AJxiXdhGnt7kGobk7yxoisTiUXCYAbH0MdcOi4\nYXB1uai4pet7HZpTjac2A4k3Cgin8o7WUiS/6FTqUle38pK3/g7s6EKW+gfs\nW8iYVC/MvsDFKzkc+e/+2c5Qx1bLN3ky+FrSksJ82GS5aijUU1hH3MjmI1LG\n1a407hMyLlC89P+8DMBvD6xQuEy4iYZ4jyx1brALVPMUuFvZZ3LH1mDJmC6Z\nfL+C\r\n=vKFt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.18.0": {"name": "date-fns", "version": "2.18.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/mocha": "^8.2.1", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "6", "@types/power-assert": "^1.5.3", "jest-plugin-context": "^2.9.0", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "08e50aee300ad0d2c5e054e3f0d10d8f9cdfe09e", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.18.0.tgz", "fileCount": 4692, "integrity": "sha512-NYyAg4wRmGVU4miKq5ivRACOODdZRY3q5WLmOJSq8djyzftYphU7dTHLcEtLqEvfqMKQ0jVv91P4BAwIjsXIcw==", "signatures": [{"sig": "MEUCIQDGUEL1BDUqjyGcAtonR/qrKQFiUUC6tCoGDtBCbmkqZgIgPpL+zbmcWqj1OpwFaxqg24dGcPUOIVAp4k0ox1DxiJ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5552481, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPPGTCRA9TVsSAnZWagAAWO0P/3k1NiuEM7H4T1iClc75\nl2pSrUyzPgKsQZ8JxmJ3MZOn8OZQjl7mUbUCYPaJj8ATZokHfkykkHQXvtpC\n/LzaPCu5Zo9pdQ3CCWtuy+wkUNvov33Pc5haUK1Yldie7/RWh3T0ahRKZqsT\nfFwqISJZGzlICcKJ6ESYTofaH/sy8oeCgeBQhbnRJE79Y4ZnhxnQYRQ0sTZ1\nm7BPZuxeMThYWBg+YWQLbCw9x9+38uqIlP9ixSDeZYwrTSc10eGa6toe33c9\nBqPQNH1IXelv8TzIfrZ9DX9aMHPZWmUhE/Qbcsp2gn1lz56s87H26dxFfzWU\n7QMEmhTuwk3OpSaKGI/khzi7k8OR0EyoujMjbEIe8OFvfXjfyaztiO1lCYgp\nVRsDkbhDcT7oaJrGPjvRQS7BRHxDPwYKp03undlBXjhLd0Csu8XMw9g5f6LW\njPzZId0zz9D3J8Wlf0eqx6PTD36K0qwGAttVZIIrr/h3+DITXOo4CR79SjJ1\nSyqILJcLK/oxd7483PWnnHfDgSnCo7IQCaVc2czTOeZWRsIDylgL7Iz4K4yL\nkJzMjM8bjmzfxovoELO8SitHEtCGFG2T3UGS/PpK7NJzOId5MK7X5i9nN2AA\ntWMeXfthJEK+gJfsscurYSrMNc2iY1IgUWUTG8oiPpYcWVf2e7cPbOotyu+k\n4U5V\r\n=KQ8E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.19.0": {"name": "date-fns", "version": "2.19.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "6", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "^0.0.3", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "65193348635a28d5d916c43ec7ce6fbd145059e1", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.19.0.tgz", "fileCount": 4704, "integrity": "sha512-X3bf2iTPgCAQp9wvjOQytnf5vO5rESYRXlPIVcgSbtT5OTScPcsf9eZU+B/YIkKAtYr5WeCii58BgATrNitlWg==", "signatures": [{"sig": "MEYCIQD/l7RL9GXikVKqjrDt8bLNJlI1Swx8qVkQjLlXNFCvfwIhAK4UUtDCiNczC955BO42PTkQnaUHsc7GUwDUPoLQEdwj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5560550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQauMCRA9TVsSAnZWagAA00kP/1o96wJF8ebBFFV7snMm\nPiXrGxSWdWoErbQhSeC4iyCUTEk7aYszPgXpC+Xip9hRAzNF3hzgHHTUnq03\nOKNTbMrUhVnbuEQXGG8uxAhXjsDAm1vfvha4Z6gkuOnGm3yVh1hPUH5WUloH\nCbnkSoBTTioghKzKsaRlB4J+TnD2M2rBqftdPwraQitznIl4QjBizRoq0hTD\nHdKgtAJI50rhQZyvX8m6SAbeeaWg7Y5go1j9+/wgrKkKZJ00QuXdVl6d/Zq6\nQE+yv2G7vnNWF2yLctehpuPb1amcMikU+CNe2U3JjCP962Yr/k/8W29wAvBX\ni0pjKLZiypewk52Lrl3QCtrt+TZ/UoEGx0oiJok/wWZTwJuVQ6LmOvyesqYc\n61xl+TayhvufrEefnHXNc9kO3207N9jIBcbyzC3PTXrEmkE4MD6obd0NW128\nfE8X+XxNfhHVqjEGf0KkuyDR/xiKlOACCPtcfATyEx4VYKgUMdBSK0bkSWJd\ncYVsWVtJkHsVtHl99kzBrC+//rnu+KjOf1ncqbOFRpfHhZkGunfNNZfM2Jwb\nnxfViXBmZHXRCx8/f3L3Rmb66QnpYme7VHS2vq4PSXfj5pD5apIUsL7HtkS6\nwxIO4kN6PymliA+Mjk80Noa3sgtd1qzqN1GvubyQdGIMOgLDvdYmnrBHTBSt\nHel7\r\n=PKoT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.20.0": {"name": "date-fns", "version": "2.20.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "df00ba9177fbea22d88010b5844ecc91e9e03ceb", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.20.0.tgz", "fileCount": 4883, "integrity": "sha512-nmA7y6aDH5+fknfJ0G77HQzUSfTPpq4ifq+c9blP9d+X9zs3kNjxC+t3pcbBMGTp262a6PJB3RVjLlxIgoMI+Q==", "signatures": [{"sig": "MEUCIEe1QnCz1JLc6tr+trtNT6MtNNQqbH0pzqprCuev5RnUAiEA1MzIzSin8Lh8luZudu0P0xi/PFK45wkABFW6kf07vX8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5769929, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbobYCRA9TVsSAnZWagAAfj4P/0wvJe3f1UZ6lObhYGot\nYlYWTmuuwwUmvsrJpqv5y4BWBDLLPHFW6icMC1mOxHzHfvl0/MK3h9590Axy\nL6oHJ4/56L7E2mgPJZyz6FyPfwfFkf6u4CGoa7CUzEKK/BdX5oGqylrsn4Wk\ndxCRrBr5rhM9SgZGY1dkdwEy8nQ4VU6sSV4YLcRD+KOITGlFWzE8wT4shazA\nNyHCvjUGHzS3jJnkB7Gq50BLCa0/eAU++zJ8DCeEIIvj3Eh0f8ZFomEk1evy\nYlqd03bHYjA0S7Sp6ZxjaRQ6X7+nA4GKIyKi5g8IVmyseqPmHmt/XRLB5pvb\nHkgpB15Caottwbnz4UubbBhVdfS9XwCuFs2DOWSOdCIU0Zv++yDs7gCRekQ8\nLE1cf6avXM9V9xgYustnraxMtZEKv+UG0cnMzsOj4x+7OQLqsCq9d06uaXOW\nyskHZP4Jdug4sp/DflDKYtzLQ9tg1+hG6+ceB6aBigKM7c4mnV0etQVtSWnZ\nP2RAH/2a3PFhlLHDxyCBjnUCExMcsVCAV+KQnQHdCAyEFIVuGJ5mGhhVCC3O\nAUbtG3mm7E4kSxpOW2zYCo9VL1XbJGlsi8wgoj7OX+UOS7MsDnGLbElfsk8Q\nAgVaFEtDaNtCFrOj90I18CPkRChsGtwzMM6EbNQU2GlniyFwix+Cla1SWJr4\n5mxX\r\n=vhFj\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.20.1": {"name": "date-fns", "version": "2.20.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "7e60b7035284a5f83e37500376e738d9f49ecfd3", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.20.1.tgz", "fileCount": 4883, "integrity": "sha512-8P5M8Kxbnovd0zfvOs7ipkiVJ3/zZQ0F/nrBW4x5E+I0uAZVZ80h6CKd24fSXQ5TLK5hXMtI4yb2O5rEZdUt2A==", "signatures": [{"sig": "MEYCIQD5Fl4qdxzPHR8Mb1S8d3ecCNWls1uZwxX5ZB1iuiYpSgIhAO2r8lQrlpVeDniBkIMBYBhTjGc/5Ysa18lpw1CbSMad", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5770162, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcEy4CRA9TVsSAnZWagAAD5cP+wch5qqomCFJL09PIRkf\nWcnlC1RUiwWugTsplpv24Sr9yaTJ9B/xA0qPWDtAwCuLQIvUnn7L9Q5s/hUE\nfNy7V3GtCCTR7IDXOZQS3M4Bx2JzadiQlEPx5JjRdeLrFlPQMiazHP9liYtw\nU2FDywFwxzrmxR6n7GuqLSAy4Tncl+MnDPBuY4jFCwYji7KCOneOOxPDGlXG\nZ2Rk+oOErCBMiHxoK3qdk5OazeslebxU/nWNVONC784CeyT5uh0XGoFUcX6N\nsuQjoYHmftHJPpJ/1awMO30jJpYhYD768nTF0CIfLMBTvN5wAueXzVFtdVcI\nsGTi6vxjIeFV/xsNnAVIcgGwq0/nEkwIz1MwthbT/xDid04bhXsZOJbABxY1\nk71QxbIbF6yBAdoNUc61Kx2J72OVaFBSj78EhlzNK6uNKJPULIQG4L1SqJBs\nvjxnyhbAomzYpG8cy0qUk4TUMDuoL8weXweh2yJ/aavs2DqZDmBip9gYTLFH\nWOq0ZPDyCtCaQDhcHZWdGw5HVAHZNzfaRDKFHYzQY5sFn1h+scrvQIGrPQYn\n47OVkG7/IcxWPXJrECvkhv2yBsP4wpswNQecCy+fMsV14PG4ICrTlzL/BNzz\nGHihGPDWMbo2emYR60k1MYDskC3rZ07TWxZQDz4LkwGVUC8Hf1KKBVzyib45\n5dgK\r\n=Zz1j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.20.2": {"name": "date-fns", "version": "2.20.2", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "7f05d1275e1e43c3bdde5998201920098e19c6a1", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.20.2.tgz", "fileCount": 4883, "integrity": "sha512-QS0Z8SD/ALhKFvhtU4Fhz+1crsI7fPzBquXmdWay33KJPEU7btro2hnmmErpQRmt2D624B1lbjXQKDUMLnQTmQ==", "signatures": [{"sig": "MEUCIEIT31zMLTDPNt3LPYbcXmqIll+SY0Ey4AlleW81YH3lAiEAyJbCelc2ek+9yZezrtuFQJ3Z0PcR6YeXzMdUcmKcM38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5773375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdEe7CRA9TVsSAnZWagAAWjUP/3D2ajlPbvLF+tUDIqEW\nqTZsuqcnkn4gawZq5byAAmjFoGfbeHlIyryxyRBlQn/Ir0eAz4FaRjnMKsTX\n0kbd68SgOmqkbEY76NbmyZQ4kAGFeJnziWEvpB1cheKdzGcZ9CikX2seCFxT\nwPxayGpRv8xmA+cC5jpwZ+tUplBEEa/2l07aMeXDr0ISpDjiGSjIJ/SzRbNx\nCEcpxChQXrGLbXCX3DXCHVXuAy4g9gAKk+H4lMp95WGb9v4YQzrj5vMjHr3X\nqEPS3yn4x+GUojy+GS1GgzGJ/oOH0pXOuMO+kw/W3QS8Ya9r1xigtCVjvwrt\nGcX9jcBsRuE7s/skvgBh7p5DCYIpjNCQw7VXxtwL2KDqGZyglo13T7l1ZM+1\nlzk1hoPYciBp5UJhB2L4zg0R1OrlMECnc73jgrnUmDWGPTqOuHMS991Air1l\nBT3MTlhRKqbNPztpvaRO7Yw/iUuVYaAiRRDYv9h9NVDV1UFkWFhf+eYwe16O\nT5tWoj0kJx+mt8IMOZPxfQYysoJleXvkb+AsqjsH8Apfzw+4F93UmCGplGCu\nbgu0FjUhv4wv90pNOHNqQq2WfjVf0mXWpIrFMqXZyL9fsq0W4In/I+RG/iBS\niI72IAte1Wsva+s6aSHL+qFZGeue+atN5ozTJaavRwdy0788jYxVmaGoaSmX\nHWih\r\n=eXSI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.20.3": {"name": "date-fns", "version": "2.20.3", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "5a28718edb95a80db96187b25340959867d36bc8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.20.3.tgz", "fileCount": 4883, "integrity": "sha512-BbiJSlfmr1Fnfi1OHY8arklKdwtZ9n3NkjCeK8G9gtEe0ZSUwJuwHc6gYBl0uoC0Oa5RdpJV1gBBdXcZi8Efdw==", "signatures": [{"sig": "MEUCIQDcCMIFtmz7h2WiYWt10tep79ZI+IlUsk2OzFjz6jY/EwIgBUlBw854XyH1GxsLe1IRU8Lo4fhM9elcGPN/Dht1mnA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5797829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdYo4CRA9TVsSAnZWagAAUWUP/j3MP/qtvwIrEvZpXOsR\nb7g5Ar93C1n2bcZ43QlpkQsaAJpfn/qiDyheGxB1yWrwCUwxp7vysH1gY50t\n1BPU0o7nIKlQda87Nif8UNYd+MhqY1BAWgdh6FOu888lk55UeQRyLORSU+Ug\nNt63jd6ZwH6TCJPFg2WWjyDhWzMpwi8rb+mec6TTia9/XB6vHS5KJkw6SN0l\nMlxPjRkwmksDZ/L6H64XBmUgHag95fX/rswhKsCeZ3L0znXJ3o2relzvfo+q\nIRcqlK4oCdHMg5jueX8RNfFuFrzHGLgsZgF8AkoHCbXAshmIvVO9a+9olBVL\nyiEWWnlY8o6qsuK3ca+32jCyfbzQ9weBGFS9ptjaXcy2a2ALzkxxNZ1yXq+K\nYAxcsvvFHQ9OlmlWF1YCprGLfl9vbYAJDLNtjtPw4RXCk2pjDXzbVJyKQFgJ\n4aM9f/DWWm5hUh15RZDj1OhKvQF0QB3ZAKwXW1OwzncG6ENmRGN5eJEss6Ad\n+XYvzY0Hc6o2qg5LNtRO7IroSnvdPY0DDcWuGmTWvFNxv4aP2y/9BE8i41Ua\n+0kH7DrE85CZsjnNYwaZ/e+mbwzinFmH+0dA+LgqcFsMToYe8LFPjeQDlO3Y\nC7nlTx0mCTDZlP/dw+1ni11jURE+LhgNgOR85aBZl9h86PGxPNqLjEl21A5G\nnTbA\r\n=dYNs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.21.0": {"name": "date-fns", "version": "2.21.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "4672b8e926d1b37d5353dc14da44b883481ac927", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.21.0.tgz", "fileCount": 4913, "integrity": "sha512-lbAFpaKz7QuVxm6m1rmioh4BB2gmLx1r1JMYXU2A/ufT5ly4zEG7HYH4fvS/QfbdyC5rkYyiS30mYz4Q7XCO+w==", "signatures": [{"sig": "MEUCIQCSawY/xL2sdpLY/+NY7eZJtNocDGEd3ICAwftS5+E62AIgIV9g9oyc05aTt8RdhSC3aLiQyLo+8sA+YClhZTMr6aE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5835450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgduyrCRA9TVsSAnZWagAAZmsQAJyk6a2Wod0n2ARdDL9L\nifj3/+o9Gn8uelRGzgH2VNV9RXDF8hhMoSrUH4l7O6OWyCn41VvyqwPhi4o2\n00AnyQX921VhVMiezMS1JIuRD3jG+EonpKQoYXgyi0Cib9azLzuLL1loj1ww\n6YXJcKuSKKMRiCzHZkgD955viWCmcdZGM4CxMAWhoqlDnjK34/pRZGTIRvzx\nYpF3iA0i8Fk0gdlKssD53cPjK7Xt8gYyNnBKrCDIp2mwpoIrqxqWreIirbgt\npnqkKGD/0py8TqBOWizchjNR1CTFqSRzoFu9OfJi3EUCtlNZjlhBGIEI8r+h\nfzxcjBwDLRejFstTuX3OMOBuCKIK8JDMQYvM8Mjt8/al8LOkpE3xJgI7Lr/k\njz1+EnugbUEZr/X+kpOyVVYlAscqwiETLjeJo3TFZO8CcMuCn+HmXyIuvwj6\nKy0FOUIk1BoVWAZ11VJYlY0OoLYkNHYteiy5ClKWEND2CnXESbs9aK+uWi3b\nXG0O/q3lzdYErDGUnWKbeFdFkPlGls5nkwQv46Cw/VyE+75beU3MyCStmRM8\nYIfMXeR6pWowAcmlsfpe8tRq65OB1mq35FzswmCopUqJdw520AOAvlWNw7eO\n5DFzPGwZ1fp924WoP1AMG1k4p1OOaMahDyuMedX/FFdASBIP3IeHOpEKJpVY\nwirq\r\n=0Zo+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.21.1": {"name": "date-fns", "version": "2.21.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "679a4ccaa584c0706ea70b3fa92262ac3009d2b0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.21.1.tgz", "fileCount": 4913, "integrity": "sha512-m1WR0xGiC6j6jNFAyW4Nvh4WxAi4JF4w9jRJwSI8nBmNcyZXPcP9VUQG+6gHQXAmqaGEKDKhOqAtENDC941UkA==", "signatures": [{"sig": "MEYCIQCgT9FVTV+Q0E31EjqtSK2p6uKZRjf2yzN5LdYlbm7NcgIhAKgppYqnxESHqpekV2SOFmCuWPj4779yrpwIcLyswyOI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5836376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgd+xdCRA9TVsSAnZWagAASxkP/1J1n6m6pUjFX6vJkn9z\nFdd+wIsWmsfrmpgep99ESTurU0xNtkulowddXcOZTu0JrqSV/Yy3WjwyGL/S\nh1adxrOZBpStsvgn0LEOqEjufDiYYr5XkTdTODz3UNlaHhSbyrk4GKfLTBum\nim6AuWdSD1OaTVd46QsRKcEv0GajaeD7AbWvKebgpDIyZz1INho8hKXy3tEq\nmWvkbc22Izo5nCanamAyTRhjn2iQ9872nV85R9mx1+iPstYhoWvTRWxkSJQE\nK9F+BFoXBmNLvopaUG9SGcPCTqqBf+3vK7NPmn4kCjlXHiv3R0ZMJR7CjBZf\nIMs6AZjs7W/87iL52A9fPcM4PqUvNgY51LtAccqbCeZ19ZhEKsQzeBBOKrXK\nKb7gM6znwOT4jY2XmLa/CbSAhLBg3b7Io4Lq+ISnhCWswEr2u1a04hFqx9qO\nA5rddd/w6FQw88c33B1iUHkXmAiUQagAhXHwV2UkvhNfZdM7KX5Uy5aSyyyb\n/kU6UW9rkCrqcqS6o3Zds+481PMhTkIE+wsl0DDchtXNVl7LTK0qywTGyoZ1\n3ZZ/WRay5m5A2AyvQELlpDJU7pTJcJQ2ji2FRyNvlcmaqWD1QfrqFLCMz0Ci\nU6PxoZUFTeohDIjBfyuxL4tkX96diud/GWewP0CHZmMjXtlymWPuWuIFQOrs\ner5d\r\n=jAD1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.21.2": {"name": "date-fns", "version": "2.21.2", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "9db92305cf00626e9122e56c72195b17725594aa", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.21.2.tgz", "fileCount": 4913, "integrity": "sha512-FMkG7pIPx64mGIpS2LOb3Wp3O606H/hatoiz7G0oiYWai1izdM4tF1dd7QABv2NogkIDI4wxsfLLFQSuVvDHgA==", "signatures": [{"sig": "MEUCIHtFOkn5+5CsH02ZznOdUSNN3+Xhs/RQuX4fkXoXJ12WAiEAsJ2vhO3+wYo824YB4Dutfu24FpUV3YEekMlWmG3FIno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5837813, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkj4/CRA9TVsSAnZWagAAGJwP/1Xfl4GIu4ZbRyF8gO13\nlrJ50SkPEeKZzBhCym4oR231ejbiWLueba6o8ep25KRyyqECasKrRshVNebd\nKmNrLwa+r/NN8JRKjzSSdKk1mxqQQR5NmFmTZSXUibTwYsMWaq+rA9V3gbZh\naogM5lM0+3jzZF6dfJDWe1g55EUMLIlwdLM1j40GSOu51QKo/t4omwn0GqcW\nOOFSQ+sYpXyeY3yc/5/GHshkadC4bYtFrziIUUJLNPYDXf9A8SMciYZkBk9F\nCadtFt/lTIFXxWPEbrgQpLeyriuI0iU0xPxcRFKRHc1y9ZcK4EFsFMDKgquo\nRyXFuuKqtv4IC2eqmQMFBK2ZaLTPPQ+3eTcJgD3Hr36OVvFtSeZewRA4/Gf9\nC66bQVsA2vDY6qjCQ1WRSxoGzFlH/6ZX3iS7QkogBlSYmNV/7RXZNfMe1Bty\naIIHP1ZybSCPM/8jC6EQPC0A+n4biPQK+ekQ9rpMKPQXKEPmmI3er6+Euwy0\nrNoc6AR6HYrrKgXHGKAxOhWv6DJHZnMtzcUgNw48szFGy173Zg+HGm38HOnl\nH5uq9kXIw05wn7BsLQqd2cdJlJELCNmtVHW9BFQlvtfR3V0/S4GDzXp2YRBR\n1ykEpH8plPEfhDl47rL8YShE39FjNv9uIPnzDfp3znvPAg5c/YT9kfUMZpX6\njr9C\r\n=2fUs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.21.3": {"name": "date-fns", "version": "2.21.3", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "fs-promise": "^1.0.0", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "8f5f6889d7a96bbcc1f0ea50239b397a83357f9b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.21.3.tgz", "fileCount": 4913, "integrity": "sha512-HeYdzCaFflc1i4tGbj7JKMjM4cKGYoyxwcIIkHzNgCkX8xXDNJDZXgDDVchIWpN4eQc3lH37WarduXFZJOtxfw==", "signatures": [{"sig": "MEQCIAQW11TtbZWu+21rsEKx0jMVza1baafpRqRLm6t3BjtSAiATOidycMOkWPyPG6U6cYwKxORu/h8zCKFrO2enK0EPCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5838308, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglgwCCRA9TVsSAnZWagAArc8P/jVrIlMNgbDfx8tcrM3K\niimDMs+eJ06IEyfwf3uaIdWysl9LWCqW86cIRbpzWFVYubCM5++MV1hP/TFG\nh8yvO3E5DGDP7cOsJ3l6ymwF7+Wvu4NkjAnT7NmwpWeuzEmIvPLfv87wi+dH\nOX8aeJu43buNUMPgU1tfsVDeo+azU9o/5xEhBqdPgWQixVJJmHVJXSzRu9tl\n3zdaBT+zJDvM4Y5mBgV/tjC+OuEaKJXarFTZMgAR0S/ZMVCWhLIIXoUMfAFu\nNWTMtyxgGWNWhP1PfAQHPCEkjFh8U9Z7d3x/fO47C8bN5dkVKZsez628qoG/\n+6TZjVQFrYAmcvq97iKOomtW4SPQfOclfRX4Tz59IrIt4tpniKGKb/NPWNtn\n8UGQOM3ko1fdImcr/4+vU3rCJaNS5O0xVwWJ+s0Gq46fFE5nAFtoqQf8LrjQ\nxsdReuDxZ10lSoaogQaoP3GwMowGc9V6Bm0RQyz4sRdCXzzTNbLVyjfu8Svu\nGJuSX+v7jKgZH0u3UN2yH7lzaaV1B7bRiozSaMsBbG9JmNQpHffhtb/xLTSu\nUPq1ZlCMZOQZBmXM75O9T3wpvO06EqTAQHGk+G379fpEHTdtXLTWVypbVqI5\nZGpfGkWz0hHbIshjTVBDY527pKTzvX2nGO8KukTMleg/Gv/zh4WZGMApA2h1\ns/0R\r\n=p9zE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.22.0": {"name": "date-fns", "version": "2.22.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "e25d79dda0639ae9e840d02ab34446613037c392", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.22.0.tgz", "fileCount": 5233, "integrity": "sha512-1TMrlJRPYjeR6KS9TgnJz4DX1rHW5NkfgIHpe9NWL6TGTzd6qo8mLo6ibt3p1wvXAu/DOal1Yce5YloFGeexBA==", "signatures": [{"sig": "MEUCIGQGStklABoBK2v7R75OyybqcFrgFGeonhH6HpjqSGs3AiEAx9UMziQ9Gazi1xp7Y65w31/IK/215QQsqzNb2CCRW+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6090350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsIgdCRA9TVsSAnZWagAA0KwP/256ioVqd7/SU5d7YsQ/\nMJ5GNUMu1zr6RVGgHB0JakipyQj2MBsTMm4tqYIAYOD97OKuocsbMyPopcGw\nwTaR5GFSzqUzKCNd5sp87R4DKufT0PR3WSZY5NM6LxxIQ5T9DqT+ET4SU8yk\na3EdJG+OrL/hyVuS5Zuq+cGFDmJbEhkELzVg2VQoOWWHjuXbbbVlA92eDOHi\nW//AZaEO4zfdNlw8OjapvvyjvlATnb1gvqZtP4nOX/5AOdKLgYQ6LkA3wp8S\nt3ZfcdJm9GoFToDzEtJncqFnTHJSmMo316My5j2bn+1kNnmL764StOMEJ3O2\nRg6RLPWSRYYtd6QemuYQY3A7IFtPMA6SKeC58JpNhi3USCZuPvCkdOovtEiJ\ne9rVNx5Zx8+xeZEGyobV6LZYqzIj3fkgapfLxI2VY4TDi3vymjSpEJ0gP9X1\n2NABwtabZGZ70KdD6EehfzDJXA/Lz7R9rkiQJCVmQExTB9iCdjYiLNVv8Gzk\nXMz6AM4geq1SP21U2MWnXGnle15zF+NU7dkIZ3X6RLJxhapINslIsys90IZ5\nj8+XV72R4y4HeWhbHgm+vriFty4Es6P1txCSFKGEk65geLclOPzYNb4Fg/Eg\ng55ZhyPJeMShJxi1ZiF/1J3yJvOa2Y8QHZtufXrblQGyA9sz8tpidEw+MXZ8\nzPtD\r\n=Hgvk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.22.1": {"name": "date-fns", "version": "2.22.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^5.16.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.5.5", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.0.2", "@babel/core": "^7.5.5", "@babel/node": "^7.5.5", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.10.4", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^2.9.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^2.9.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "1e5af959831ebb1d82992bf67b765052d8f0efc4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.22.1.tgz", "fileCount": 5233, "integrity": "sha512-yUFPQjrxEmIsMqlHhAhmxkuH769baF21Kk+nZwZGyrMoyLA+LugaQtC0+Tqf9CBUUULWwUJt6Q5ySI3LJDDCGg==", "signatures": [{"sig": "MEQCIDyQSJqhgXGjCHAlha7RrixAazkXM5gxwMq+bd0YC7L7AiAW9uO6vBn9xtzGUzjj/vLeDLWLlBTW9nT6ZqUn5LlNPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6091188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsNw0CRA9TVsSAnZWagAA13UP/iQAf/fb3DjafmN5nedU\nkwu1JQuMh5+FM8r65vAD0n/qeMonVmqY+KzLdODbq5b1+HgaKK7UlBT9UHeX\nZiEGQuLrY/AWbrj8wixLE6L1OBobsDnSl/OTGL4G9veHhMVNvjXxtZrreQU2\njbwZBzDgnyeZkd1N+BwA5suMf8D4W3LTUZIXmRZ+P27YjpTMaPczgQ6czR1i\nFZtiXRiAjbPtu5eaOWTVLQsu6oCINR8Q+ILMy/rbRUlgU2fwkq2rFY1s8qBr\nPs0GQyp686kAnMEzj2OBt+VS2FDY3fVLM4oqdJ+8aqNXbNucUlxhItkpyodb\n3smSFuB7i5iv/jjHx3MIkT3+qTCNZTz1QcPMrKKedKLoOpEyckLtBecdGDjL\nJZQxwx2a0RvPRXQxITK//dT8iFhqSTanRM9AFfd1dqvoF50V8FWyvO5VMDqV\n/+Uc1JtN+C0wcvva/3To5OPtEAesh3WPV/Bemp/YrGGPk5MWnlnVUch1RTGb\nBh7M8yRu1l0fH+w+DgExZHRqRCoZi0eXOk8xn9isPZcl5SvSAc9C/eoogwOU\nhu9epu2hREDGLR6PJvZ5SXqpEd7cq8NAu1uxbGikVhl3SUFqP2feqHytt3WN\ndCv2zMogEN1D+NKYLCtDoy1Ft/uvZ9CLgYUENJgZfgAZHVPGBEfcUqDdcGnh\n+xQv\r\n=IG9z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.23.0": {"name": "date-fns", "version": "2.23.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^24.8.0", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.13", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.23.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "4e886c941659af0cf7b30fafdd1eaa37e88788a9", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.23.0.tgz", "fileCount": 5269, "integrity": "sha512-5ycpauovVyAk0kXNZz6ZoB9AYMZB4DObse7P3BPWmyEjXNORTI8EJ6X0uaSAq4sCHzM1uajzrkr6HnsLQpxGXA==", "signatures": [{"sig": "MEYCIQC6X5Xzj+UDDYAjx0x6TFMA/69OTie1tnstAp1n4rlCAgIhAOOB3TqIK0D4fNgTkM5FeYgxKO6LXmOrM+mrMS6ssgJs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6141274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+lvUCRA9TVsSAnZWagAA1lwP/iMnX6kI7NPD1A+CDKS8\n2/vNQzubOe83zjoChch1fxgsnK4beqoNqt6h5fgPn8ek6TBYmBW4Xjhw9dnq\notQL3Yl9leAnEPhnlrMRZnjjL4b5SPz9cvUApq5c4nvHF1sfW/iSypRtrUgy\n+7bVwvqYudNqYH7PWT53FYoZxirvu5Kl7MDL2iV+It4hjDkE2NehoD8/5nu2\ncRVOU2BSeem3VdXaYGmkxyLHkn6GEjCzjHCsDm3J5pCU9+qCYXIQ4GONUUat\ntiqYRTmia7FJcLV3QGN91UNIcFj+LRSfQ6y9AtAw+lajaqcHX0qOopQ2mBGE\nET7FcgjZDDoG3U9BzrmGT0TE7NIgK8BXBaToHxQwOqVy0gf73xftl8ILlhJp\nzfj7GqUJ/WxxdmbgYmPhbTw0lC5r5fl1krWUEyjgztTnYjiuyrpYMxHUny9l\nhIv8MxrHnGLUj8voWWgrrdb7fe7HjP9AS5i8lnp/PYuS5cTHRoKpA9MYUboc\nae6hmUXWHxx73NwpR+YwBvkpdfyXpf7VJ+M9s2dvmsWRhWm5c/CcAeX81COW\nytKyYFc0zJYL45vBB2PpffJm3HHGD+ZFhTSor20kfAa7AdLK0q5sXTkQh9a8\n0mKGaBugY4amOAbsTwhexx4fjH8QHjOGjtUaXxzjQ/75DDn8a5aLk5RbMXa1\nw6tm\r\n=fTEe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.24.0": {"name": "date-fns", "version": "2.24.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.31.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.4", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "7d86dc0d93c87b76b63d213b4413337cfd1c105d", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.24.0.tgz", "fileCount": 5471, "integrity": "sha512-6ujwvwgPID6zbI0o7UbURi2vlLDR9uP26+tW6Lg+Ji3w7dd0i3DOcjcClLjLPranT60SSEFBwdSyYwn/ZkPIuw==", "signatures": [{"sig": "MEUCIQDQb/vooV66F10cBZpYUHFln5F3b4lcw9YW0qOPu7oHhAIgYja/ZiFltHPaZ97vwxBGMzuqK/AmEGo26yBVKaHY13Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6327731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRCnYCRA9TVsSAnZWagAA728P/27dq2hjqG+nHTAxjyy9\n9BgMbiG+VH5PK5q3JRvBR1luS2VB+5xF5OPwkGFqvahCsuBrGrkhdT/BtO1d\no6KJ38UB/I76BukTa/zhIPk3VPL+lKn6wvQ4pPTiwmB8GDKFecb4muYWdn0q\nc6e12XQBN4TSufOz0oHe/EPOGbLXQCalQ0oXzgnQzP1fWzT14eMO13+s36fI\nJvhgt4vEgIaSY/V8BFsqVvkAbY4dUcg6exBTHGRpFAbyB8CQMaaHzT306Ali\nVJSLcnflRxavqSQQNt9VQHtBuB4T6dn9nBfKKTOf5qdcqGb8Wao/+pPTm/hu\nE7U4ww9lqJhWjnurWLYc303JVRgjq9uY6JAKlr2D87pJGga/pGU66hZ8GVQ+\nec7mPmeBK+hrdo+hBCYOBttjZKsgzH0GT98k64YkPEDE3pCxlRGTzNSOOQjw\nAcn8gkmmnd8LrZflyBn92/MvPedn6OR+GA/i31qEOOZ//Iuo5uGazlOF+f0y\n83K/5qdiW3KJMrexVbjLEx5sseEDlRv5GZwflb2dQamL2fl2Y1G8DV7ZsqYR\nsMZlmb4x5TuSgJXuq/7J9Wm2iiDCHlEKiTYc3cSi3dIAcg31yNslOGZUqRKN\nDiT5gJe2YX2KdDnTfOp8VF3qnDr2XreiEQaj0AJwjefLmLuFMlu+FbFiXHBp\nrCvb\r\n=lxma\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.25.0": {"name": "date-fns", "version": "2.25.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.31.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "8c5c8f1d958be3809a9a03f4b742eba894fc5680", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.25.0.tgz", "fileCount": 5491, "integrity": "sha512-ovYRFnTrbGPD4nqaEqescPEv1mNwvt+UTqI3Ay9SzNtey9NZnYu6E2qCcBBgJ6/2VF1zGGygpyTDITqpQQ5e+w==", "signatures": [{"sig": "MEQCIGmmFE93sqXzCr8fBrOFFS8mwvxisC1zbr126le/uDjcAiBrzd2tlllypCZDxhtG3Gk+d9EgCeNbamACNTdqHyM//A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6360028}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.26.0": {"name": "date-fns", "version": "2.26.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.31.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "fa45305543c392c4f914e50775fd2a4461e60fbd", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.26.0.tgz", "fileCount": 5538, "integrity": "sha512-VQI812dRi3cusdY/fhoBKvc6l2W8BPWU1FNVnFH9Nttjx4AFBRzfSVb/Eyc7jBT6e9sg1XtAGsYpBQ6c/jygbg==", "signatures": [{"sig": "MEQCIH5IwYyjMRCAZU21e7etetLARVsew9R8Bfq+Q/D5qzQgAiByMPBaNh88tBlXNHhrW6XRL9fuRme+gz6Npy/hFPvTPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6442511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhl1l3CRA9TVsSAnZWagAAE+UQAIT7yM41LOkaqCL9HjlX\nAKrxoesF4/v+8SBUzSr+/JXatH0Ff3Z7zdPsZspHUGye+lfWKllmBfeaEr/G\nDBtSz5l15BK8taq39IN/s37bSj4N+l176lXORpTLMC+rIgPFvhX1pKEBtE3T\nxe6+UOBCs/9H3GKYcV9KkwifA0ZPN1jx0joSG9VkXruXj1f15HI+6S19C8uw\nR+6q3M1xRT63+n//TPZw/zcqCLV5YENB+esJrswxVQ1PlusmtFnNv+R1avGu\nxrj5WALdc66s6eKjEm3nG90HTqp6rs2Vgy6wp9KtamPrx5vT7Lwgiz+Qmwif\n44jZbTotKQ0j7fHRYOjPo1JK8yApiIGywhzeYQ4Y+TxP/FM0BpJyo9bYcY2n\nTjRHHwgQyXZcewBSFbPkmhyKmRKNpA/ag8IETLUa77my46QSEdqX+zN5eWaH\nDGYDV1l6gpc3OqFvfDzMDGLskPy4H6SnzQGAKvUdYBeCm2ksqy+1ErTJnVIw\n8f0MB5zargCxSOY+lZBdO5NkunwVirFlwgyHu3ZtDGSaDa+oXY1OsBuHXIkI\n7YyLPS9jctL6lBFcQvk5E3VgtUgRolU7YDVsP89Uz/oMqH8rBV7AC+vdEh7G\nDLFXIUkDg6qeQNCWtxUj4vxC2sJ5UQqnf7ns5V/dBIYh72n4L5/zFhBIh991\nHdeP\r\n=3zFZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.27.0": {"name": "date-fns", "version": "2.27.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.31.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "e1ff3c3ddbbab8a2eaadbb6106be2929a5a2d92b", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.27.0.tgz", "fileCount": 5556, "integrity": "sha512-sj+J0Mo2p2X1e306MHq282WS4/A8Pz/95GIFcsPNMPMZVI3EUrAdSv90al1k+p74WGLCruMXk23bfEDZa71X9Q==", "signatures": [{"sig": "MEQCIA/sx6IVL1lV6CYmgC752JaEiK2YY0KdzIea7/7+ZElkAiAuK0OyPB9KmaPurDWcrjZJkK1fcFCToZAwBSedOoGQuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6467583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpfiyCRA9TVsSAnZWagAAiJUP/0oUQbCmrs/iYMh4ZZgh\nRmlgxeflNJu/vg3ukRvA1zaBIHXbtosmw81pm3A2Tr7/2trZsMUlrdFoWGVN\nQHaMFhhHE+SeSEVopryXktdDSb3sGvZE58DIWG3+/PViuG9VA2dur3cNdt6G\nmZhBePk6X+9kNxvIOpVWHa87nG2esGEw4trmyfZcenjKZr8uIlGPPXK2Qorr\nz+YL6qtmAopX+DTV91xY94itUy4CSYydFnTDq2tGcwBi+Dv/Ljq3g//KqWGj\nAFuRYJg728gRRTu5J+YH86l5vOFYNiHnRL65z+XurRN/vBGla4jSPdwi3GAu\nGftIdjppPQ7bqO5RzctB+M4e9bJ8qpGgnSDu345sbthMUkW12BUn3VDtz0Sw\nsss26yYCUTDVdheKlQHOOr5p0dDngeMoGV7wfwDhZ9MXkOeQdlRtS2aeRWzm\nq/VbmwK4+3o15rUyAKYGzhflHzdeFYsff2UCGPF53+ZvrfdkxrKJ8ALIrt4z\nfbMdKX5dcmFWvOVvjJjB3XNpqyd723A3y5pxStvUG/UdLTZGWLk+f7TdoJy8\nJaNHill4SSqaD52W9c4cLeBmrHYOpSv0gl8Jj1Kd9an63GNFYRI1G5YExtoU\nPNWpPVydZ2Dv0wG8mBVdtbBNbUf8/Ot8zXo2nBzg885ysJnT47oi4U/rsH7Y\n/Xqb\r\n=9MqG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.28.0": {"name": "date-fns", "version": "2.28.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "rimraf": "^2.7.1", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "flow-bin": "0.84.0", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "power-assert": "^1.6.1", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "karma-benchmark": "^1.0.4", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "@types/power-assert": "^1.5.3", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "karma-benchmark-reporter": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "@typescript-eslint/parser": "^4.31.0", "babel-preset-power-assert": "^3.0.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4"}, "dist": {"shasum": "9570d656f5fc13143e50c975a3b6bbeb46cd08b2", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.28.0.tgz", "fileCount": 5560, "integrity": "sha512-8d35hViGYx/QH0icHYCeLmsLmMUheMmTyV9Fcm6gvNwdw31yXXH+O85sOBJ+OLnLQMKZowvpKb6FgMIQjcpvQw==", "signatures": [{"sig": "MEYCIQD98R52uA8aYOcuOjFwErv9kXkL1w8+ScNkIbpT4iiH3wIhAJfDgtWtNWhy344aQBfG8G6H2zenE8nhwPrMJhBPHLfq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6470982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhyyfiCRA9TVsSAnZWagAAczEQAJLS5IZJuJvPoNgzjtw+\ndDjXWtrMF+LqhCHYYptoQuD5pDeHczG7om3RDQBB5sN8SIuyqLP0CdAnnTwx\n0i+nlM5CSSYXquqdrlfSDtYxy+lnaKD1fGpB9r6E1aBXrxNiArIldhZ852R7\nnlu5RgdLz3QLrsKNh/swd49PaW/BdiVGX65+6cfF2FPmxEA2DTQegms86cP7\nduQPW2kP0NS09/u7UrfbByl9v8eKsxcPPDNKbngUzPVsoOgEIdsIQivcP7Wp\nTTCrEGMhcecO0OZ3HJiHnZD9VLC1lGmrOERD4F+UMI7TI0XIQ7if6ooCOePw\nF2BaScbiEnX5QMoRzkeL/zoeTmvD33ZbPj8/ZMsYBDsbebprWbo+0zYSzycv\nDiQop2SOtciRytBCq+VSLQq3x98T/5Mwl3tvb+yzXHUfgcFlIYjeDM7OnnLg\nqfHsQzxj89ViQLBHRheEcCLMtMZZ+RIyQMp4l0Fd0mzYUlrlTgsFfqdCMkFg\nDGl00wZ2m9+WxfVN+Rms3+5yEFkFOtetM3h1mi2hSeLDGLDoCJ5PbbMo9mmX\nEkRndvbKqV9HI/XDj9DmN6paZchPQvpb5m4EMLJOCCFjrxq/u+SKu7XP0ZLh\n1rUbckft5q4oTGzMUE9DVr5h/0QLjdZ1OZ0ddsz+8I2RqmVvDJ+WraJeVleB\nyFNX\r\n=K4o0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.29.0": {"name": "date-fns", "version": "2.29.0", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.31.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "dist": {"shasum": "0abb17a5f224d5d91cc1fe02e847ce80ab7372a5", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.29.0.tgz", "fileCount": 5722, "integrity": "sha512-itxguwZHwBR0ats0VpYAVyY65dq1DEsDsfsGxFG11Rrho+zQJTCcjKbxQ7LiU5M2omLJ272bnn63yYFk/YGuVw==", "signatures": [{"sig": "MEQCIA33zY26OkwnaKkWp9FeLTjSMsrPQamKSZLR55kJ/653AiBYhOYQhA7O7CcUi3U2UDJOfzbEM3eQWadjXadf2DnkoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6607615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2ktTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkTg/+JhmUXAB29Oj7TmmMfbl160O+/OUAXCsxXITgptL1Wtw7ZNAt\r\nYZR9uZ5nNtgt6I6w+qPt04aw5iISFPvqEL2IdtB2t9YmawdLfgM8C+F2waFW\r\niZSXYML37j8hjdkKFReSsAFp2ci3dyNzw63jYwiGb8kbLcbsq2M947576b9h\r\nAHcXMRoHnLPPcQ2qCBC9ywxt5cW0fLMTYp3fOx2HTi/DllhForQMJ5/69qe5\r\neQZEbbr+KtoC/LObG/SlBA60jgoblnojBB/iLLtT0XBpVsuTAWycIEcnVFR1\r\nO4o3k+8JD6vQhUfmFSW97+l+ujQgm9kXSUv4pUwrbd1bK1gJD30+68KUVq70\r\nx3x1d9gwQwvrknkLsCVSzp/6qyhLtA6pQ4gg0iGgDQPxb4jxtk/m3pPZaPyk\r\n2/oDz4zZNSEjGwUMrq8FPY89EzYX0hOl/JDUUrznvob/aLkKMAzGmHKIvhV7\r\n6PPBfBfCf/fPcTHAQ5rhxHNZ2YkHZFTf1YeoqOKEymELnomoRoPEcQNaCwQm\r\nHvfOn9XsauJ/N0Nps9XbvIxBKgWitQukAClcVygjYUnS3Bp4tF0vKV1SPMxU\r\nGNGLhuUko+eKigegOMrw7j7ygFqP5IUG0QF7yJljYjFa1OiOPCChsdkpe0rS\r\nEHwvXd54FU10pWrGR9OXVM9RpmvzhTvpBXQ=\r\n=WYU9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.29.1": {"name": "date-fns", "version": "2.29.1", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.31.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "dist": {"shasum": "9667c2615525e552b5135a3116b95b1961456e60", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.29.1.tgz", "fileCount": 5722, "integrity": "sha512-dlLD5rKaKxpFdnjrs+5azHDFOPEu4ANy/LTh04A1DTzMM7qoajmKCBc8pkKRFT41CNzw+4gQh79X5C+Jq27HAw==", "signatures": [{"sig": "MEYCIQCR3V8cqJqIUPB/w62AK89TFnb1b10np6SZRa1Ide+obwIhAM22aHHPjeVEU7ZJpbonZQG1JYw5jQ6ZKYms7kYEqQqm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6607849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2pOiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMAQ//WJq+YKahnEHdJa+et6+r84/XZbD3OAI/I3x9tHETpIasr+gY\r\nPU2qbR7VgvjiQg0XQdDsvCShnEnGB9cLTQ0L4nE0pidpP1UmbhDLe8bSsMVk\r\nota4X6yRUDvFJ7DgfcPzXAc1hjAXSezRBnEF0ODvqmk/kmfD74vZBop9v8BI\r\nmxT46wZLGsw88lDx7YKnJL/FSBMxQ1W6oS82lI1nGDaXAQxOIkfwJoFNR+yT\r\nncrsiF/7YOhlFEInxd+oFZvxlvYlDwCvF19+BR2ho6CuJAyiUPWjp6C/522b\r\n1Ei6pIltlnHwoEdv2641ol/9Jng0vTIC+ASb62Gta06YHSJR88M5xfcrVWaT\r\nslEDPCJ/zWG9mtNihMwpgbpnxFAk6CDTbbvgthzSWuDA/gmJpeb0HhNCE28W\r\ndsqawhiz73WYl4rBJELAd8VekCm1ePWvd+I+pJ0ZDZvvRzVTtGQSib2x588H\r\nIEIZmrNTzbtb9IJYRA3Nw3trYKsqRa6spgW/lSp8HgpbySTLWccAPcFHA142\r\nCEVgR4Qa69iXmjyzh5zezJgrbWjeHqCh/vdCKrqKTmCDGHJDUiu+t65krMmT\r\nWIIKz6IOBPoHu8eXb9HXTxcyVrA+RZeiwSvLcwYg9Zgq3RTSsTMeJxgv3SJe\r\nv4qAiDIZeZvvWVjzdx8+gujVUQ/kF+I2c90=\r\n=5Byh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.29.2": {"name": "date-fns", "version": "2.29.2", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.11.5", "jsdoc-to-markdown": "7", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.13.0", "@typescript-eslint/parser": "^4.31.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-block-scoping": "^7.5.5", "@babel/plugin-transform-destructuring": "^7.5.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "@babel/plugin-proposal-object-rest-spread": "^7.13.8", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7"}, "dist": {"shasum": "0d4b3d0f3dff0f920820a070920f0d9662c51931", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.29.2.tgz", "fileCount": 5722, "integrity": "sha512-0VNbwmWJDS/G3ySwFSJA3ayhbURMTJLtwM2DTxf9CWondCnh6DTNlO9JgRSq6ibf4eD0lfMJNBxUdEAHHix+bA==", "signatures": [{"sig": "MEQCID2vcqTGl5KFdEp18zk9RSDYKRCiH0mJDwSAo/6D5YUVAiBfkS7712TFx5nEyuDqvdoIoQzZxVeN5oj4ywYS5qLIPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6608506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/i4fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsSQ//Tog/1RUsn7iTyR8a5R88wBGsTteue5qwANCHVg9iJ+hgET+R\r\nxu+Vd6Uio7hMjPANVC67pHqPE2wzpMYhv8trKVOJOhPfO/kxgsCotNApQ9u3\r\nAFvHuFYZjh/UYfQiETt6cPVttLuwm3uNUhT3rHOoO74LbBVbVcWfju7JQDXm\r\n+vJeiYddwQ4mQPR13zwrfuTH1cW9YN5GLfBXqQEvujn7wxkLI+IYfSyKiWuE\r\n1icqiqKa9646K5rsnPXqx2BF5ZEbRxgu1pmNm3UwwW8+nS1yYxi5IIhTsEQo\r\nELDJNsu8RB714Wf75cTGl7tjv6jZO2CwCjjEXObBz8BEbPd3UOTR0pe5lGuh\r\npw3vtNuLsReWfwN0YucpW76dMH8dsjQUMrBCC4ATAQpNlWtiym3vNkwhioz4\r\n1Hvmtvf+UEFL+r0sbzzg4THvJ62WUeheOJNe5MuY/Dvg20N8H3nRJXYm0rAk\r\nYD3IPJGzRSu+eV/l9QCdxyRmbgRBm+bMZ5WpF2rLraPlkeWWVp00qq56m2jV\r\nKCGs/h7TIV8v4VPubvTTuZ43wK7z8HenL3GGtSoeXFV0mQ1P+Ymm/rNn7B/q\r\nWYfw4UWdBblHumaf8utK2QHEhDetMPbAMZJuiySxxlnOXERdQal3iP/GCWA+\r\nE/weXAezMLV9nkWSNNuC/e9FTimCxpCuZ4o=\r\n=WY8e\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.29.3": {"name": "date-fns", "version": "2.29.3", "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^9.1.1", "webpack": "4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.13.10", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^4.2.4", "@babel/core": "^7.13.10", "@babel/node": "^7.13.10", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.18.10", "jsdoc-to-markdown": "7", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.18.6", "@typescript-eslint/parser": "^4.31.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-proposal-class-properties": "^7.16.7"}, "dist": {"shasum": "27402d2fc67eb442b511b70bbdf98e6411cd68a8", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.29.3.tgz", "fileCount": 5722, "integrity": "sha512-dDCnyH2WnnKusqvZZ6+jA1O51Ibt8ZMRNkDZdyAyK4YfbDwa/cEmuztzG5pk6hqlp9aSBPYcjOlktquahGwGeA==", "signatures": [{"sig": "MEYCIQCzg1mN+fr2z8mKNne33qmRUJqpbzUmduSLTgYKr7bKfAIhAKws0ZcU6bhmnBQtlbtIoMjZlFvn+KN4WHg0Ji4R4+dN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6869554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIHk8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7Gw//a9yvRpL/gmWbgyQav5xbSlj0OneGkgzwKD+CjVtClgsiLJ2t\r\ndlGswGXI5GZZv+sPs/aHCOnEYs6sU9HQvMCQogviI87pUT9/Q12sY9bnbft7\r\nXBBYg8fsKpysW1oMpyfA7oeGgyHJAI1+Ja5+ND0pQRHAnm943SQYKTu9c2G6\r\n5VoppG/SE63n0nNfdSvs57sain65EZXPObK/7SWQJk6Y4f6kFWHC4aDpvXfI\r\nyisoEB5E6uIddmKQQNk2ZwJAtx9WUJ8sm7ht0q1sblpFenRZ8Rj4UcXeazgf\r\n77fzrtJY/bGj9XH0SC4IqipgyYZNl486sY+oFtOsGHA0HzKQeWhvQGP42sF5\r\nM5CBBRdiUhzf6YvItJwk47Pqz9xq+1leFRzh6kPO7SMooSsg7Slft6NSOZib\r\nVzJ/5IyU7RF2diPY2xtPPp6c+BiR+KFKHaQPtVcWGXrUYyod/GjhrDgebXqb\r\nvrbsw4tV1QVSWWMidpzHFZD7fhpcyPJPyRpqzDi8bX1y4WfckfTqFVsUeeD7\r\npTv5CF0sslf+XCluDYGRC+fqc9VQNMoe8TpxOGWJxUiO0K6Z8YULQQ5yPlIz\r\nmG/pOmTfRwNYa4s5tAmhKLf2umINlNgy3EadF1RhJgE75HF3jTCDCunUS7RN\r\npfCJMoyrwhTTHv58AR9wl24eUmWxbqm5rx4=\r\n=bkT4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "2.30.0": {"name": "date-fns", "version": "2.30.0", "dependencies": {"@babel/runtime": "^7.21.0"}, "devDependencies": {"mz": "^2.7.0", "cloc": "^2.2.0", "jest": "^27.0.4", "husky": "^1.0.1", "karma": "^3.1.4", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "p-limit": "^3.1.0", "ts-node": "^10.9.1", "webpack": "4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.0.6", "karma-cli": "^1.0.1", "@babel/cli": "^7.21.5", "node-fetch": "^1.7.3", "simple-git": "^2.35.2", "size-limit": "^8.2.4", "typescript": "^4.2.4", "@babel/core": "^7.21.5", "@babel/node": "^7.20.7", "@types/jest": "^26.0.23", "@types/node": "^14.6.3", "js-beautify": "^1.5.10", "jsdoc-babel": "^0.5.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.3.0", "webpack-cli": "^3.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.21.5", "jsdoc-to-markdown": "7", "karma-mocha-reporter": "^2.2.5", "karma-sauce-launcher": "^1.2.0", "karma-chrome-launcher": "2.2", "eslint-config-prettier": "^4.3.0", "karma-sourcemap-loader": "^0.3.5", "@babel/preset-typescript": "^7.21.5", "@typescript-eslint/parser": "^4.31.0", "@date-fns/date-fns-scripts": "0.0.6", "@size-limit/preset-big-lib": "^8.2.4", "istanbul-instrumenter-loader": "^3.0.1", "@babel/plugin-transform-runtime": "^7.21.4", "babel-plugin-add-module-exports": "^1.0.2", "@typescript-eslint/eslint-plugin": "^4.23.0", "karma-coverage-istanbul-reporter": "^2.1.0", "babel-plugin-add-import-extension": "^1.4.3", "@babel/plugin-proposal-class-properties": "^7.18.6"}, "dist": {"shasum": "f367e644839ff57894ec6ac480de40cae4b0f4d0", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz", "fileCount": 5722, "integrity": "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==", "signatures": [{"sig": "MEYCIQD2df1r5aZw4bF8EiSLRgzHE8Bdvgu/HAB0HTi8qteeIgIhANit+qO66scC6/w4Mp75npZa+T69zpUQQ66ymsidVl49", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6685407, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTddLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGPxAAoGP8x3nDKlpbnpTKNKFGaFjUCoCpIriZAyvO5A75N6tyya9y\r\nwjsG4bKCjsPYx/yeSTNb7AOa4zMmb6rp8Gn5YVhOnJJhqy9kiQZGbeOf7d9f\r\nOErb8Y/zJrtuwN4RPbmuRTTJtHZtYPjhP6RMFdbjTggDVNA8RivFFZz14dx3\r\ntwP5xdm4Ujm9C1zYFXLpu4zEtou7br6SK0UeDnd4g4fRFIdNs7tufVraPPcW\r\n/+xLaH4h8pSqBZjTK+K3EM+cCtLmUF1MgcwU5YuC1D1x4Yn8mYS+toV9yM4Y\r\nCN7RyF/JRB4+/czMA4UwX+v3YV2UbEEsnUatsxKpWnLqSCHeZz4zW1hQcsqT\r\nGqq30IBKLR6KVbafph3K8d2Hdbjeffjz6L8ZjJqgpqhtbDqWp7mfFw07roiF\r\ni2/kd/RDLx1LrcWGVce9irORr4GLSUGIYKkD9HKZsi5ZHdFMYDj/W4ybONZC\r\nBQg526jl/rbjappQ1FX4BwUhtiQcNc7syFmWVIla4uTMRVIUXX/46NI53ixk\r\n793+2s6TdfbaiUxgpwuEkFUjS+Y91e2g4l305whNQytf5gFqA1eRtlMGn3jI\r\n+CPEE7DOI0HuuFeVmH4i74C7yJkYWf7vbsKPkOqrwn0/PiRhHNeEYkuVH82N\r\nhC/VoooFhBoLOcHzgh8bDQeYwfrm1moT8CY=\r\n=24sK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=0.11"}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0-alpha.1": {"name": "date-fns", "version": "3.0.0-alpha.1", "devDependencies": {"tsx": "^3.12.1", "cloc": "^2.2.0", "husky": "^1.0.1", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "vitest": "^0.34.6", "p-limit": "^3.1.0", "typedoc": "^0.23.23", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "node-fetch": "^1.7.3", "playwright": "^1.39.0", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^5.2.2", "@babel/core": "^7.22.10", "@babel/node": "^7.22.10", "@types/node": "^14.6.3", "jsdoc-babel": "^0.5.0", "lint-staged": "^7.3.0", "@js-fns/docs": "^0.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "@date-fns/docs": "0.25.0", "@vitest/browser": "^0.34.6", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.22.10", "jsdoc-to-markdown": "7", "@vitest/coverage-v8": "^0.34.6", "eslint-config-prettier": "^4.3.0", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^5.30.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "typedoc-plugin-missing-exports": "^1.0.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^5.30.0", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "11c47acd3ab3b083381afd9f087b4144591ad488", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0-alpha.1.tgz", "fileCount": 3221, "integrity": "sha512-vnXwZJRuIUykjVFiKOTzpTQEFhm0SBPU47kRcztmhKZ0tcvWkS9lisnNzRRJC/HfSodCkhPuTFQdjyHxXtrInA==", "signatures": [{"sig": "MEYCIQDWCkRapsy84cqtCOPmE0zjqfyFSCwyh4t/QNJMwLnxdQIhAPiqxM2I5IDroMgadYeHtp+nAsdb823jqoIVMNBu+1a4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4224487}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0-alpha.2": {"name": "date-fns", "version": "3.0.0-alpha.2", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "husky": "^1.0.1", "mocha": "^3.5.3", "sinon": "^7.4.1", "eslint": "^7.27.0", "globby": "^11.0.3", "js-fns": "^2.5.1", "lodash": "^4.17.15", "moment": "^2.24.0", "snazzy": "^7.0.0", "vitest": "^0.34.6", "p-limit": "^3.1.0", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "2", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "node-fetch": "^1.7.3", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@babel/node": "^7.22.10", "@types/node": "^20.10.2", "jsdoc-babel": "^0.5.0", "lint-staged": "^7.3.0", "@js-fns/docs": "^0.1.2", "@types/sinon": "^9.0.6", "babel-eslint": "^10.0.2", "babel-loader": "8.0.6", "glob-promise": "^2.0.0", "@octokit/core": "^3.2.5", "gzip-size-cli": "^1.0.0", "@date-fns/docs": "^0.26.0", "@vitest/browser": "^0.34.6", "world-countries": "^1.8.1", "lodash.clonedeep": "^4.5.0", "@babel/preset-env": "^7.22.10", "jsdoc-to-markdown": "7", "@vitest/coverage-v8": "^0.34.6", "eslint-config-prettier": "^4.3.0", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^5.30.0", "@date-fns/date-fns-scripts": "0.0.6", "istanbul-instrumenter-loader": "^3.0.1", "typedoc-plugin-missing-exports": "^2.1.0", "babel-plugin-add-module-exports": "^1.0.4", "@typescript-eslint/eslint-plugin": "^5.30.0", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "3f7beafacf1c2b57e326ee76ba9cbf30c32db37d", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0-alpha.2.tgz", "fileCount": 4421, "integrity": "sha512-iyiPe3OJNTaiSSH0tZmQuXL0EK5hWzh+eHewYZzL9KzGKSgwt0yszO7BRMxgE35f0/iC+g3NxCXDM0WwP2yB5g==", "signatures": [{"sig": "MEUCICItTrNsEUWyyZEGfaMn0MqSbfSK9+AfGh4yeSOqlamNAiEA7bOsr1lmn183HKRqI5WeV5jHEmjBYFiaNIh0xXa+CGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4285812}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0-beta.1": {"name": "date-fns", "version": "3.0.0-beta.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.26.0", "@vitest/browser": "^0.34.6", "@babel/preset-env": "^7.22.10", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "aca3517acd0420ea7c8f3678b7b53c426aa5ce19", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0-beta.1.tgz", "fileCount": 4423, "integrity": "sha512-35r9mhoSpu2DsZerpaTbcrU0y703VfZLw4HzePH6BnE2xbHONKP24Mdeoznf45LbiA/zJc5B9Z/p2Jk9Ph4EaA==", "signatures": [{"sig": "MEUCIG7MtJsHlIdLOPMo6APA+1plMVaYL+mw44jz+XZl/CgVAiEAoPbOMqVu8sEXcKvAmHA3NPgmVB8PJB/LaJsecWKNWpA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5724855}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0-rc.1": {"name": "date-fns", "version": "3.0.0-rc.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.26.0", "@vitest/browser": "^0.34.6", "@babel/preset-env": "^7.22.10", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "e016b1a36eb0a02c39cd5b8580c4459490e01eef", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0-rc.1.tgz", "fileCount": 4422, "integrity": "sha512-L+31PShqfrcAUweitR3jnDaMIA9QmwMv2OJiz3uHrwHtMDVvyLe4K/RARCKJSKSiXyBbM+tBcwbOOM47g4n3bw==", "signatures": [{"sig": "MEQCIGIPqRwX5bsYr6J9XB8YBvKEQBrZkkNbNKR7f0/GEAj7AiA438y8UfdUqOtmMiR+GU3Ksu4mMqFYjud6/fjQAqBwRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4428826}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0-rc.2": {"name": "date-fns", "version": "3.0.0-rc.2", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^0.21.0", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.26.0", "@vitest/browser": "^0.34.6", "@babel/preset-env": "^7.22.10", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "e4adb2a79845cc19a8dcf42a204725295969c89e", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0-rc.2.tgz", "fileCount": 4319, "integrity": "sha512-3MAy/Mc7i7LVIayaPnHCNhah7Zaj9/9G9v4pmdJNvMJ+mPahQtrQ4DWm6YxkQjvhRB4gBnjV5UB1ll5ZQxE31w==", "signatures": [{"sig": "MEYCIQDueXtnK9sgLv03yfrAzzAK4enDg8EYhF97wsWDreogHAIhAKqha+e9DVwHnyUD9h1x7sboIJwdR0j1tY2vPm7qdd5Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4405478}, "funding": {"url": "https://opencollective.com/date-fns", "type": "opencollective"}}, "3.0.0": {"name": "date-fns", "version": "3.0.0", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "dc46aa8c403966a588fbc3794c6b006c17ab8691", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.0.tgz", "fileCount": 4317, "integrity": "sha512-xjDz3rNN9jp+Lh3P/4MeY4E5HkaRnEnrJCcrdRZnKdn42gJlIe6hwrrwVXePRwVR2kh1UcMnz00erYBnHF8PFA==", "signatures": [{"sig": "MEQCIFvcN4KiapXkmB1dKcKDogveRm2Gfbgnl46KUoEGpYlEAiBXh2inDLaupnmOABCcEf4WFYabBcdgxQvid46cAEw72w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399429}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.1-rc.1": {"name": "date-fns", "version": "3.0.1-rc.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "56dd3ab819ef1d08924bf24989998038e598fcf7", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.1-rc.1.tgz", "fileCount": 4317, "integrity": "sha512-Wiacj44FuQ7WK7Hi7TEAK+mdhT0qiSTyzZa7AHGOtCuOFOaKobyiGHyzuq4B9l9KfhV/Dqm6Tb/5eEyMPtjEDg==", "signatures": [{"sig": "MEQCIAvx8PazkiKlsTRWV47JvbwWCVJ2LcptuDc6R+DSfLZEAiB55SuBIiTWqgHuF+brTVnIqz8Gh5gjciWw0KuYt8FO+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4393975}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.1": {"name": "date-fns", "version": "3.0.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "a95b3e8296e72cf57c99819f37679aa27ca65ec4", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.1.tgz", "fileCount": 4317, "integrity": "sha512-cr9igCUa0QSqgAMj7JOrYTY6Nh1rmyGrFDko7ADqfmaQqP/I2N4rlfrLl7AWuzDaoIpz6MNjoEcTPzgZYIrhnA==", "signatures": [{"sig": "MEUCID4LIyC52Bl6iu0GJR5TAaY2eCazY5MvTUsjxKXV1BqOAiEAonO02TC2kkBicQsqXBlHWny3Y6FBB68SD6ga/KdeEcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4394160}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.2-rc.1": {"name": "date-fns", "version": "3.0.2-rc.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "4001db8a9b071709a3816cf1840090ec457e3e28", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.2-rc.1.tgz", "fileCount": 4317, "integrity": "sha512-rtLgW/u9feeKuSjYjDZVDoe47sdA7QnGRbyEtFtmNB8SXVQnL3g3SIKlcQkHwk5X7uhmF9vP7/B1ZGXtGsF+IA==", "signatures": [{"sig": "MEQCIArxRtcDgRMpF6tO9Zxk6n+Pv22LHlaoBga0knSgtbDwAiABw+h1FfGQEvf1cVNNpfVEVg1u4fCsOsoG9AQAB7HSrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4844215}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.2": {"name": "date-fns", "version": "3.0.2", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "3e8f3d52273aadad4af02947a1f7c9fe44ab0786", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.2.tgz", "fileCount": 3242, "integrity": "sha512-HbGldFfQ9V5djjYVesX9rckgUXJJ0y3bMlaYMSyo56DTL5MtCEbCtJWEi7ah//1bc9TySX3bsaCdWO7BbvUZ2w==", "signatures": [{"sig": "MEQCIDOl1kN3dSTR2mmtUY1HhPCXm7EiKdsS1eJYMGlWPEW1AiAac7ydZqgtIyesiowywt+pBmzKOvXEkj4sqO32ab38TQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4354416}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.3": {"name": "date-fns", "version": "3.0.3", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "590d03c49d991f1f9e3d2e18dc76aa062b5d5bf1", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.3.tgz", "fileCount": 4317, "integrity": "sha512-orRh4Qc+Y5fE8RO2fG0FkggCTxgfRR71kypohBPvIlEHi+GZCy0RuaPm5i2oNVhnT96nNMj/0R+LWAjBA/3prA==", "signatures": [{"sig": "MEUCIQClRE2aiUXZhjY1blhEhlp2MaV7og2nXqzVePxS/bwB2gIgHEZQosFZZRybTXXtzwN0wB/y+P5CGnoZym1atJA7S/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845057}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.4": {"name": "date-fns", "version": "3.0.4", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "56eb7916d8d4666bf9be168ef84bed0deef1077c", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.4.tgz", "fileCount": 4317, "integrity": "sha512-+daptVi95nJ/D4TPS7/gbUqneVMA0Izr4qYAsL2ZZwtcDtEP8Zge765mbXhTqWYdTSG79/VtbBigQTluiE9DBQ==", "signatures": [{"sig": "MEUCIQDy+TOj5eVerHcCvGT0aFMSI0qGIlJI/de5ZA3mNC23ewIgYO+84I3hGTLc21TN14nrzzHDQDTogfl+pqWkyNWIxBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845297}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.5": {"name": "date-fns", "version": "3.0.5", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "ca4fca44aff67e01c337dc9c5f2ea5ac7350ad8f", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.5.tgz", "fileCount": 4317, "integrity": "sha512-Q4Tq5c5s/Zl/zbgdWf6pejn9ru7UwdIlLfvEEg1hVsQNQ7LKt76qIduagIT9OPK7+JCv1mAKherdU6bOqGYDnw==", "signatures": [{"sig": "MEQCIGIyHg1A9Fm8DJFYpepC3zyk3IfJd3a1OfQHkpAD9XPPAiA8AZDv9C83FyU3HlPnnCOd3UJ6cMmepwzt/AmQl6rUOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4845500}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.0.6": {"name": "date-fns", "version": "3.0.6", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "fe3aeb7592d359f075ffc41cb16139828810ca83", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.0.6.tgz", "fileCount": 4317, "integrity": "sha512-W+G99rycpKMMF2/YD064b2lE7jJGUe+EjOES7Q8BIGY8sbNdbgcs9XFTZwvzc9Jx1f3k7LB7gZaZa7f8Agzljg==", "signatures": [{"sig": "MEQCIFCGRdr8FqaimfG4pPwlM6sxNaVuaCkxBYVyMis2a/OcAiBoa+Zna4ZGibd67fgaIWyClDP1qVa/qtW70iV79sl/kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4846064}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.1.0": {"name": "date-fns", "version": "3.1.0", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "576df8df93202942aeebd49ecc042218feb40426", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.1.0.tgz", "fileCount": 4321, "integrity": "sha512-ZO7yefXV/wCWzd3I9haCHmfzlfA3i1a2HHO7ZXjtJrRjXt8FULKJ2Vl8wji3XYF4dQ0ZJ/tokXDZeYlFvgms9Q==", "signatures": [{"sig": "MEUCICl4MT8kLBmE/v2lRsqZqQoLqFkZ03QU3wAuGO/YIv/DAiEAkPSb9eKd6s4d/0wZzRnV40m/gmox2RZGIuuzzbp06wM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4837433}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.2.0": {"name": "date-fns", "version": "3.2.0", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "c97cf685b62c829aa4ecba554e4a51768cf0bffc", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.2.0.tgz", "fileCount": 4321, "integrity": "sha512-E4KWKavANzeuusPi0jUjpuI22SURAznGkx7eZV+4i6x2A+IZxAMcajgkvuDAU1bg40+xuhW1zRdVIIM/4khuIg==", "signatures": [{"sig": "MEUCIHR00ntWSExH3ligLgs2F2Vfxj/3BeKBF8lWZG62GUglAiEA0LamGYwC0n07voDRbdSDzmN+yrNSVhcZe9PwFJ3xLcw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4842019}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.3.0": {"name": "date-fns", "version": "3.3.0", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "c1681691cf751a1d371279099a45e71409c7c761", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.3.0.tgz", "fileCount": 4325, "integrity": "sha512-xuouT0GuI2W8yXhCMn/AXbSl1Av3wu2hJXxMnnILTY3bYY0UgNK0qOwVXqdFBrobW5qbX1TuOTgMw7c2H2OuhA==", "signatures": [{"sig": "MEYCIQCQ/ny/4/x8pFd0oPUJTPN3X0C2YUl+bJbAg5zb8RbruQIhANGn6uTJJDevdu0Gf1nUIppQZgQPLzyjfOuS910auQfK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4858154}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.3.1": {"name": "date-fns", "version": "3.3.1", "devDependencies": {"tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.2.1", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^1.2.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.2.1", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "7581daca0892d139736697717a168afbb908cfed", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.3.1.tgz", "fileCount": 4325, "integrity": "sha512-y8e109LYGgoQDveiEBD3DYXKba1jWf5BA8YU1FL5Tvm0BTdEfy54WLCwnuYWZNnzzvALy/QQ4Hov+Q9RVRv+Zw==", "signatures": [{"sig": "MEQCIEWakLFrOaqdj/8IWO/DSCLGaaddk7BqriL1BRJriKUlAiBwVbE5dEpMOkGdnPeEE8h/JC9Ltfa/beOtjx7cCE/LWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4861064}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.4.0": {"name": "date-fns", "version": "3.4.0", "devDependencies": {"bun": "^1.0.25", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^0.34.6", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.0.3", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "@types/sinon": "^9.0.6", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^0.34.6", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^0.34.6", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "19ac3c19a39af64acb21c23587cb08504dcdc3de", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.4.0.tgz", "fileCount": 4361, "integrity": "sha512-Akz4R8J9MXBsOgF1QeWeCsbv6pntT5KCPjU0Q9prBxVmWJYPLhwAIsNg3b0QAdr0ttiozYLD3L/af7Ra0jqYXw==", "signatures": [{"sig": "MEQCICGqjq9U7bWR8AXtyELsUM5R5CUqfZEMJoIIK4H/nBikAiAqge66sK6RzkKaQ3qPszn7gIMEZICHKXoiiTxt2cH0IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4904079}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.5.0": {"name": "date-fns", "version": "3.5.0", "devDependencies": {"bun": "^1.0.25", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.3.1", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.0.3", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "jscodeshift": "^0.15.2", "@types/sinon": "^9.0.6", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^1.3.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.3.1", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "d4f89de9b347ce9b14e4d05701f5d150a6591547", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.5.0.tgz", "fileCount": 4389, "integrity": "sha512-a+DwyXn7NOfdJireCzAA0B9p7jIXEu/Q9JKCyMYvH6+0vPUNbQceA0neXrdfJ/xzl3mhOh5vibQQ3936Tssm6A==", "signatures": [{"sig": "MEQCIGraOAlImQ3SxgUOfIGHaZGZQC22bP0V9XNG5fkgr1hUAiATy1u1c33WsOxXVhqLg6pgDOfMHwlIIsPhjVdMNq0KYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4934687}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "3.6.0": {"name": "date-fns", "version": "3.6.0", "devDependencies": {"bun": "^1.0.25", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.3.1", "typedoc": "^0.25.4", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.0.3", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.3.2", "@babel/core": "^7.22.10", "@types/node": "^20.10.2", "jscodeshift": "^0.15.2", "@types/sinon": "^9.0.6", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.29.0", "@vitest/browser": "^1.3.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.3.1", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^2.1.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "f20ca4fe94f8b754951b24240676e8618c0206bf", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-3.6.0.tgz", "fileCount": 4782, "integrity": "sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==", "signatures": [{"sig": "MEUCICYt4cfRvx7E4A898FEJqksUbBZJ4o3cOIns9Y26FtrWAiEA0NKCnsn2Kr10NjJ2NCJphoF7/9KM7bBM0gQzeNNYjcg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22153202}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "4.0.0-alpha.1": {"name": "date-fns", "version": "4.0.0-alpha.1", "devDependencies": {"bun": "^1.1.13", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^8.55.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.3.1", "typedoc": "^0.26.7", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.1.4", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.4.5", "@babel/core": "^7.22.10", "@types/node": "^20.14.2", "jscodeshift": "^0.15.2", "@date-fns/tz": "^0.3.3", "@types/sinon": "^9.0.6", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.31.0", "@vitest/browser": "^1.3.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.3.1", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^6.13.1", "typedoc-plugin-missing-exports": "^3.0.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "babel-plugin-add-import-extension": "^1.6.0"}, "dist": {"shasum": "ac09e476435426ca4a7801231f647ab98b8db271", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-4.0.0-alpha.1.tgz", "fileCount": 5326, "integrity": "sha512-st+1LN3p60ZFMjk83XRuNEdjSIuzNnN8e62XxeqWpC6F9f38N7QwflF6uxC32golMjcb1iTcHQ6AGs6IXAU7zw==", "signatures": [{"sig": "MEMCICl/cRJj+dV0C6SnvU1YgJOG1eWJnj3EKnxl/sLpSCbAAh8V4sEi2QK1oFYhqGlqg8qn/GpgTeaHRGSpMXOmINY9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22783959}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "4.0.0-beta.1": {"name": "date-fns", "version": "4.0.0-beta.1", "devDependencies": {"bun": "^1.1.27", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^9.10.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.3.1", "typedoc": "^0.26.7", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.1.9", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.4.5", "@babel/core": "^7.22.10", "@types/node": "^20.14.2", "jscodeshift": "^0.15.2", "@date-fns/tz": "^0.3.3", "@types/sinon": "^9.0.6", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.33.0", "@vitest/browser": "^1.3.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.3.1", "@arethetypeswrong/cli": "^0.16.2", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^8.5.0", "typedoc-plugin-missing-exports": "^3.0.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "babel-plugin-replace-import-extension": "^1.1.4"}, "dist": {"shasum": "6eae6e104f5362fdc24779eb9e8123662e12c306", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-4.0.0-beta.1.tgz", "fileCount": 5326, "integrity": "sha512-EjMXEgaR2r/Ff8aLwg8OtPnMq3caEANXuYVDz3F7UnL0f/sjeQW+mwQAS2iwziipMSi7kqc9tdfBmGAefhrY+A==", "signatures": [{"sig": "MEYCIQDJHK0KQ0JZYxurWWD48zZmZZsBb/XtbCoY5JYRnmEIJQIhALaQLdnSRU7fqczyDzQGBLZSpnWq5kRL6UOHsDgCe3w3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22641926}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "4.0.0": {"name": "date-fns", "version": "4.0.0", "devDependencies": {"bun": "^1.1.27", "tsx": "^4.6.1", "cloc": "^2.2.0", "fp-ts": "^2.16.2", "sinon": "^7.4.1", "eslint": "^9.10.0", "js-fns": "^2.5.1", "lodash": "^4.17.21", "vitest": "^1.3.1", "typedoc": "^0.26.7", "firebase": "^3.7.1", "prettier": "^3.1.0", "coveralls": "^3.1.1", "@babel/cli": "^7.22.10", "@types/bun": "^1.1.9", "playwright": "^1.40.1", "simple-git": "^2.35.2", "size-limit": "^11.0.1", "typescript": "^5.4.5", "@babel/core": "^7.22.10", "@types/node": "^20.14.2", "jscodeshift": "^0.15.2", "@date-fns/tz": "^0.3.3", "@types/sinon": "^9.0.6", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@types/lodash": "^4.14.202", "@date-fns/docs": "^0.33.0", "@vitest/browser": "^1.3.1", "@size-limit/file": "^11.0.1", "@babel/preset-env": "^7.22.10", "@size-limit/esbuild": "^11.0.1", "@vitest/coverage-v8": "^1.3.1", "@arethetypeswrong/cli": "^0.16.2", "@babel/preset-typescript": "^7.22.5", "@typescript-eslint/parser": "^8.5.0", "typedoc-plugin-missing-exports": "^3.0.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "babel-plugin-replace-import-extension": "^1.1.4"}, "dist": {"shasum": "3a2c76d061c13439660b0524300df93617ce0880", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-4.0.0.tgz", "fileCount": 5326, "integrity": "sha512-6K33+I8fQ5otvHgLIvKK1xmMbLAh0pduyrx7dwMXKiGYeoWhmk6M3Zoak9n7bXHMJQlHq1yqmdGy1QxKddJjUA==", "signatures": [{"sig": "MEQCIF+beV3LQLuOqr3nteB+g8b9QG6iiCpmLyR/h13wlehiAiB3Pux+fSprKkDzFzpYAEHo2yg+amldCPhf+MDwsxK+fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22643515}, "funding": {"url": "https://github.com/sponsors/kossnocorp", "type": "github"}}, "4.1.0": {"name": "date-fns", "version": "4.1.0", "devDependencies": {"@arethetypeswrong/cli": "^0.16.2", "@babel/cli": "^7.22.10", "@babel/core": "^7.22.10", "@babel/preset-env": "^7.22.10", "@babel/preset-typescript": "^7.22.5", "@date-fns/docs": "^0.38.0", "@date-fns/tz": "^1.0.2", "@date-fns/utc": "^1.2.0", "@octokit/core": "^3.2.5", "@size-limit/esbuild": "^11.0.1", "@size-limit/file": "^11.0.1", "@types/bun": "^1.1.9", "@types/lodash": "^4.14.202", "@types/node": "^20.14.2", "@types/sinon": "^9.0.6", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "@vitest/browser": "^1.3.1", "@vitest/coverage-v8": "^1.3.1", "babel-plugin-replace-import-extension": "^1.1.4", "bun": "^1.1.27", "cloc": "^2.2.0", "coveralls": "^3.1.1", "eslint": "^9.10.0", "firebase": "^3.7.1", "fp-ts": "^2.16.2", "js-fns": "^2.5.1", "jscodeshift": "^0.15.2", "lodash": "^4.17.21", "playwright": "^1.40.1", "prettier": "^3.1.0", "simple-git": "^2.35.2", "sinon": "^7.4.1", "size-limit": "^11.0.1", "tsx": "^4.6.1", "typedoc": "^0.26.7", "typedoc-plugin-missing-exports": "^3.0.0", "typescript": "^5.4.5", "vitest": "^1.3.1"}, "dist": {"integrity": "sha512-Ukq0owbQXxa/U3EGtsdVBkR1w7KOQ5gIBqdH2hkvknzZPYvBxb/aa6E8L7tmjFtkwZBu3UXBbjIgPo/Ez4xaNg==", "shasum": "64b3d83fff5aa80438f5b1a633c2e83b8a1c2d14", "tarball": "https://registry.npmjs.org/date-fns/-/date-fns-4.1.0.tgz", "fileCount": 5326, "unpackedSize": 22601076, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDPeErMpFrvU81tFzLw0nml/Srz2FeXOElkuWsL8+5q/QIgMSkRqEl8QapdnNiNJWxVL/dPKwvL8ASb6iE3iVeMJuM="}]}, "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}}, "modified": "2024-09-17T04:37:03.984Z", "cachedAt": 1747660587993}