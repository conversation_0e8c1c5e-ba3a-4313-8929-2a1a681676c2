{"name": "@radix-ui/react-alert-dialog", "dist-tags": {"next": "1.1.14-rc.1746560904918", "latest": "1.1.13"}, "versions": {"0.0.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-dialog": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "bb03c6d455e7b0757c1a82812dd8d4dfd67e321f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.1.tgz", "fileCount": 11, "integrity": "sha512-bLdrRuW+8aADZhC2GhmeL4iJa0PmZZ8i3LsQz//lWtSI/zELSxN47cGmqNruogZvgU4Gvd5KVi0CfJzj43ZpCA==", "signatures": [{"sig": "MEQCIED23GPBDaGmvHQal5tB+CbZ7piObLKJ8WZJmabEuf4fAiAYZFRZ8Y76jmZ1o5dsLYpSpbgI/lCN3f0EdJBmG97M6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbACRA9TVsSAnZWagAAbHMP/3wCvdHy2eO1/EZO8D3c\nmQVEXm/sNCFKOqvLK2kfGlFFKV5ff/eq24EfH5U6Ls07nQC0PNLlpBm72pAU\nM9COFnFcDh5RbJRyMQMOtJiKdXUQ9Etw2UZKgX6RxrHd1xOuX3Z+gEQCZ4jj\n06NXf+JEsrf5xddXwo//k9t/Q4x+uGy0vd7EhKIw8+aOYb6OJ0ZsS1IyASvu\ntJO8kT9n9Gr2tEmeP3/FTondxIJZ8uVyjVF3XjHOIeKh/CRPnFYjJF6+5f0j\nJx6NMtQlNzpvEonD+Xh+sWKCoFMGrVuGYoHxKU5szf9P5YSZlgnCImfPQC7Y\nW/H/kL7zT7TV5dPRcm8gpE9ea45T1/n+VrwDIZgsYYAQzU65W2TtZ74BEg5N\nA90325zLi7l4dwYoW7FQx4+TsU3wTDlcQRmYRyH3VcTSfLn8KVaN6gU2F+4J\nholvAruuuS0IrcZfaWMaNi4UvjhojO4HgzF1AQF/Yx591Tde9HnlQMclj5Ey\nrOXBWMcaLFxp1ZqPw9VtazmSsv7Aj1go4sWTb7YyCbjD7zdVvIQBd4+khLIf\nHPV1X/DVZuGjsapGC1Xmz/cYVafAfs2xGjYsivzM+/imeohhqWbI08jxtCqj\n5UU5OS2WvWk6PT4oHJoweY8bAzyGuukGUhg1qcqTgvrazsKobSAKan9+7BsU\nYbr7\r\n=9KkD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-dialog": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "425fc1e4e36a77fd2ba1650c454f9113e89355e5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.2.tgz", "fileCount": 12, "integrity": "sha512-kBPJqbSyjnlFz5RDLNe58U1E+Buu7dBgwKQQtAOO4w8QtVMvSgvyT/LGjEGRSUx2ozaN8MDzJvZ8h+3uFVRs1Q==", "signatures": [{"sig": "MEUCIBU44+5hnMeSo8tyO2ORcBpfwQiceLzNjX2vIiXWqb5aAiEAvBGjCSXly9zawjV1whIXBZWHT2qgWZsHJZvhZL8BP+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwu5CRA9TVsSAnZWagAAyTIP/1DHCFuO2f2k+XtSBAzw\n/6tBWm5Y7V6bPgR2sp5LyndMGE3NMNvuyZTPAuqvMX5X/CjeM6Ac4GKDWn0C\nmdKtEg3N3r6j0Art/8S/4Rg35u4GitU+vB7jL1+uxdK8vor7KB2ZdyS9aeoP\nKfkhwa+/GDwfXWwdxLxxP4HZLHmqKPp8iak51YXzCDA6fv97vzABORW3y71q\nJefRQZ7SAk1GhGhXw8TQlZacHGHdBtl9ptQIPT8+01LgUeAvs94R7zG2CmSs\nA13UnWucVUsGFthCCrwqr4nGpCwBEN/2Us2ZDNFA5jv0nf9+ykQa8qFvs383\noJ+1cCujHAwXCvzVi/4uSZRRWV3fXVUqv6fxW0n2GlAfBjCN4ny5UgzUFCx4\n+WYx7YFHMuBIplgmo5XKT0pkWXyROSkZEk9M0fe6pYa2pnN16R3xSTQXbXXC\nQngCQDWA2Gp89Wby6th02C3xw4GQ0JMexx3+mSLMJ6YSJAdnoJpTiuLXVWQB\nZC2OlbEZnQ0RH6UJoYidh5YQJeIjoMZAufUbYYelFeIk+Ju9dbLzdWmmAFmA\n3OoyumBQjZ0rWLyeKturbB/47yFT7DKmTV7eJXCdFzR0gcETtkoo+Z08YFLs\nYQUadsPj/lzEWqbHbhAl5yiBgejvmq2pznxvlgx7sQr9lkVCTGcEVy6t4Fp7\n1BuO\r\n=qqza\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-dialog": "0.0.3", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "52cd58e41072d0133ddcb7539bd9a1d3471bf6e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.3.tgz", "fileCount": 12, "integrity": "sha512-VQYRUNUE+Aedw/rNgqpJl6CwyvZHT+NPgvlm/e4fW1cEQ5KH/5irejVLHz3yzAYauDtL2JdN7bkQurXrr6MzKw==", "signatures": [{"sig": "MEUCIAGVloxUiyGiHXQtjOQ/jZv8oBE4h7ZmRCOxs2xhFCu3AiEA4f434B8zYt4jlijWEm0GpBjpBkm40pBcPBro3rEttuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDu28CRA9TVsSAnZWagAAlVUP/imG09tk6zEdLclS+VXN\nRhywnd8ju0t6pxr2qWVtm9YXh4cdTAT7nEWSNrQ6VEL5eMYDFaq9krUl7F9j\nYHp2Mx/IKFbVPxcnhcn3DvliebAtFzsFjLXMV1KoLFsQkLfbPv2kMlZXFMCI\n3TeIwoL4PXmJeITpyUpRHpJU4brBxM72/pueG7SVHjWYdxgjhKeYxUQFGo39\n2Tj0O1Pr9KaigSMuIkUfZwqZ1/kN1kue+v7/+KV/ATZv+n+Q3O4Um9cmyM59\nZyLFORng150MU/2OTVEkkQaM3feB4cQHxB8VwcHPbTrEULr6cy2QqGMi2vKa\nVziVTmPA1G44Pwo4RiR/xukL3kLF+2Fq+taAys2by0qJhFcw1MeVvfJ6uRWJ\nUeQQ/svN2/loWft3WPuIvasEYx0O3llPcZwlIRNi7ZkkUbiDs4gVniyAV+CO\nzZk/4cixiecxIK+PXdkYG0RfFdQDijnEMlRohG6c2ohXsJWN0nH0Zj3kq2xV\nLBPw6N7G2/BP02ng5kowUw85X4JWFABLOeqZ97HSTO5aJ/yJxuc2UQ2/xeej\n9U6P95iST3GHSxmkazdCAByvDZY2XEaB/7djT0MZVQniemWAYLXfL45uiMTP\nCLCCQHtmixN+xxqOi4tyq7HnurxlH9xW6ojRN6GFfpLAilZZMCSs8f64HJ2j\ntpKz\r\n=8tNV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-dialog": "0.0.4", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "1fa5cd5d3024836b9d9023fd1316a2499492a85e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.4.tgz", "fileCount": 12, "integrity": "sha512-RxPNbOmfsmf0KorNDoWi820Gti2KtwD/NVbkIJXLHJY4lO9W8BWdA9+FBrfzLF0UgcOfEKh/bWnTjFWlAu6nPg==", "signatures": [{"sig": "MEUCIQDT6XseT8Yz5IfCxsCX9JaQmMMMGaogCR9dakgITEb8twIgEj5gKbqNBkXANAWso9IP56pjb4PGiecPFsOKxiqMA6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68814, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETsyCRA9TVsSAnZWagAAzWYP/22Q2IHpAh+bplN5wpSf\nl6qVhU2A8Yg7tCp1AzjDYkyhbSjTusSP666GWBL2tZY+PwLVJKEkRxZTLSaQ\n4VLqZfjp9TiJBtc255A9ud729dT9+wKwXS1ESBb8Bw50kLgeJNTtsCW1fXMl\nOVUjlQpA1GY5JDo+QwZG3MAnJUDpeZVvEj/A7TjMEITioDBr3YD+RqAt4kWQ\nhd1hQywwmPsi/C/kOd9WXb7gqCu9/dhCZg22rVI3WUxAVUG94dCxQikih3WY\nIAHQiRj1CUOhMTRbDoDyqb44fOkE3ox4MjH11qKSLTg3yE59OvLlVjs4bUlZ\n8VMwx3cfGWX0haxSTStjmsp0dtRuafywvPmnIhyT3cg2BgLwB0P3Dz92vEDR\n+dxmxMYLWWAUTPK/S9+8cNKC7cYhlt/LE+X3NtHSgs62ciPOD6IN0RM3KTkh\nM0SIqafYSuExBr3Gpo4966Fk0LfwS4G2LBfSSC6JQJUrxpap+g5TystqKYji\nwBVoh8KfwYHplFHwF4jVovq/R4Hmcr3Zvkm5sF3g+kvdTDzScal4M1qq5s+W\nBuwdWWoHgcQQHa8xb8iXT77PmrsaiupgaIZEeKalxflJs0bnsZM/9ILzanOL\naK6aIzwHDkuikZHJKbVuJp+FIhlFilYQitq8oA2SjKoGrA+6yzE34KGaeZ//\nTvhf\r\n=9b7X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-dialog": "0.0.5", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "4dd39378d3b9b2b3644061d5ab53ee329436eddb", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.5.tgz", "fileCount": 12, "integrity": "sha512-tBDfNMBT2HMLEfmsNeAxihtDjftd2gtGlNgfcnp1z3cIaGlIv/kFaJRoY5E5Z7DaT/jEq4Pf8bR5wq+KAI/gog==", "signatures": [{"sig": "MEUCIAFYJy2mnJlsoPiLG3JpJh9p/ouSSc3gt8JnPs8PUP+RAiEArm8DvjfrjucX2ufMIvWkzpg9k1EuDhuC48jvGF2ShuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/uCRA9TVsSAnZWagAASjQQAIhIDfbmMa35vo03wphE\nAF8amIPRO0kLNvBVqQJqAr9R6PCQVH6uPuiCkTSP1OTpEm9LvFbhGczMV8T4\newG8j0/AWv7pXz0A4ohDOYeGVfOyGxw8yHVckYIRfQc4IKTCbM+BpNeaLkAz\nUCEyAzqZvtCfERENUJVLpJNekL6id+fNjhKAhExLFBWi0ROSE6viFoDX84tC\nYs4laU2UNP+OK7VYw/RN9ZNGr6FkEW4xv48rxI76Y8kmLI6ulx8rRJrTntMN\n7mODGFJJyEqWLGHeEkGoNkuIh9MoMWu5e73WOjRJ8LWmZIan+tFzeQIsY4t/\nnPL+obRYUTaPGnGnog6vgLRRNTdjN8tN082vJpNmHHDu0wI8RWiIPu/Nr2VV\nPQk5aHAsKAYWB65ZWib9bR7yc95How5fF3huKiOTq3GOZo5U5NbhibeTFILm\naEEQ8S9HBBX2lEySiV37k53oOFCPUMeReZK2IdENfR55PJSS2kulvHmb/Gcy\nmBDqoret6GdrRNGdgddbtXi0kf2Waz27qP7DvqrTyslsKChIyT/wZLJNk+jC\nX6deodvsc/0v2zGb8WCx6jrHJXAXf2c4mhOh7gEeXwmlEJiu03Ft+SwEF9ML\nB0DESRRbe0aFNbYUhFIzyr+PAcutTcAVG67YjYQN0AHdwhEHvP0aJ+zCoTK9\nuCpt\r\n=Pk6F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.6", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-dialog": "0.0.6", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "0ed967e821d9edf93d1c74232125cba19d4e6adf", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.6.tgz", "fileCount": 12, "integrity": "sha512-F0jSvcmCzzuMRrN7HoptYk9lDPlxNbr9FaTo7uSdvAvznoFp0Oi2pq5EKiOxv14ZyYwDAzhE+RrKIATUsQ5hjg==", "signatures": [{"sig": "MEYCIQDIvdu9X+TeMPwEUmOgsBGxkZB+2feI1k88UjvQkEeoHgIhANO5lLs8Hid4siNNlKo40zI4LHpoV7xJMjvuC5edgwig", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72079, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VmCRA9TVsSAnZWagAA7sUQAJnOcfIHSQ3GbQmGOAPD\nYwB1AODpQguKTBqIb1zElbuG5qcj88nSiIs4ikYgAjBs8MeDkvs3SOYvaKwC\n5wS7y2tvp0hVuwLohQQYYgCuQ4qHi8GW68m2A9ZU3C6fcps1siELnV+zibiH\nFL5/wYlCzRo+XxO8D5G/4HlJMbe4YwnziUwZpZe+oveL46Pd7EAlAU0NNTVX\nYUBULGCxH5P5NbPcuuEwXlrpXODwPHDHvEktjjRfB3lUKiXzs7QwrW7j2xqf\nJcPgFoOjXw1Jabc1dcff/ZNFKjvFS2KYN+jOYTmhax426X2bEj38HDQBBxxE\nvKwAOFhObD7Q0et36nQe1Mks5YBq4gZqtTDTcBcbrAEy1MsGH5LD3ydfHoP6\nTtW2scHGbxas3A7BS30MJ7V7Nm2XCvUXp9XfS8lZuRVSAkNwnbpZB8l2kdfQ\nFTb8ItakaAET2ojpDUnj/9us1OleOyCfiXiElBGCVkkkaaavYmUJ1dlJmMq+\nsTN3sHrpZwkGm2z3py/sQ02qc+Nidg0s311fB4s4jBn/ae4L9/GtzxlLjjwa\nUVw7IykanIIOfNmLHMNHwPV0A1QJrDmFhlB87drLgazcRqoB0Lxp1OuynKc3\nirmC+15JUsJqgvRXpCv0Swqbz+gkO39UOhzeX9g+CtJweEn8E5V+pUdqi619\nsAz1\r\n=Yyim\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.7", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-slot": "0.0.4", "@radix-ui/react-dialog": "0.0.7", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "56a60fced5cd517f33824e61d79219ea0294aea4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.7.tgz", "fileCount": 12, "integrity": "sha512-w2WZB7GnMK/f/froMdYtjS6CsO1Gx/Upo1bqAjIIpNyN0I1h6yi9ZwyJft6Zsv/4+4Mny+jKCE+QD3v9VLP2hw==", "signatures": [{"sig": "MEUCIQDrb1HjOmaRQgbMHI0YaH6tuOtRQVx18strdUQRwlkCiwIgToPVuhK875f9otEGY7rI8SbaP/JcDFCx1vtJIiTdeSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65890, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+U7CRA9TVsSAnZWagAAVTgP/jSF8co4nIPwFp0/o7rs\nkJ7kLkP3oeKam0Zp1W8gxNJFeTNEdkQGUrWj2RLQDfTLMeJdrH4xGOXZbMsy\nnT4HzYqgcHdSUJhhs5F/mPXT74EclMxLsOS2yRS25F5B7NxIjMssBmUsw61/\nLt/Xwe3lURyGfLbAoE/syIsTa8NNRL+44+be/qXd2ipehHNjE0VuT+m4BJLE\nbub4QTaYy75ww9OBpS0jj22EswYosfdVM/5zWU9LgzyM44wJlhNbahYiZErZ\nmOWLuGQW43Hl9WyMUf3nShTK83mjFHbrIDQpoqoRtoA6gAq2Krlw3kQjBx8A\nBRY6Hg45Yy+f49PEIhUh375xChXB8Yc/fVSmAji48bfJZbGmGDz8h9WEViRa\nAP5wP2xvisqVSBQu2rJxDHaeDPH2F8MOJK9x/gFZsgm6bMs6xfL49Vmo86o4\njIqCKzGDzSKCJXgHllV5aZqERMfMmDcepOpX/W75HaoPFyOcMNNvOsXthf6z\nogrD76RPueNsUnbBM6L2D7F30noDwvZqGMlxWdKS+pygCh9z7BqAyP2HWmav\n284FZFCjyCxRW36vXBiGR7qE0GhRKQREWhsp5sEGFc9d8EbJXQPSSeJIlgqp\nRdqKyxZzIhp3zWmgTzwI1GUUxVDBxEVtFe4uH1lmoSYs3Cnja7JyqUUiiC9b\n6mT+\r\n=+ZUK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.8", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-slot": "0.0.4", "@radix-ui/react-dialog": "0.0.8", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "1ce083dfe7a8addd8871ef5d1e52696884b47d2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.8.tgz", "fileCount": 12, "integrity": "sha512-KbwyYo9kLyrmj0zH6szn+Jhk+/j7H1A83NdpqYZNWqfwEf4GfOqH2TJJ8FoPJBPU3w72PGZb4tLHWabLKjt0Hw==", "signatures": [{"sig": "MEQCIBxLpI23LimQeDTGypgMx9FK4MnS1D8vxiJ4q7GH/EeJAiAoydJGVGoo9DnSkDpV564xBZxZXBM/4aSgrQi/kfQmfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmVvCRA9TVsSAnZWagAAOEUP/i1GxDSCqGd1UQdvSUL7\niEr6pqIXsN1n6eIZwwZrU5xy7zja0Iw4xOp2ALhWb+eL1dZJKzSeKzz1/5T0\n1gOgxi9P+MbAgWM9ZfTXsgI59Sf/hdUkMTg/hTzgKcM6GFdZs3GuWD6C+1wI\nQQN/yIJRR9e9dp811P92hvsldoD59nW4sT1vg7CMwWw5zl3YvDEDb9Y4C3KJ\nfdkJCF+vb06XfpxiGbvIqU9NGmZuu2R1OEEYyVM8osuwhN60SAKqPWD//tJ5\nEKCSzs0fgMZKFZvKN+qBuLWh3biphgwql2nNTYjCvfTkGUm/a66/APeUiQhR\nZ2kqSbaa7DCV09FeIlcDFQJWAhjeGCSkcT3QFl8ao6kk4cqAHF3Dcta1eY91\n7OKCzSjKbs6Ip+eqXoAqV0OaOZKV6IyJFYGArI8WQaITEILrK5KDDb/Mgdkf\noHUwk7+TG2z6z45gXE5eN1mfS4SW6hk0wTQIhVvOCjZNJ2O37XicJxxqNF2Y\nb3XVWLMAubVYAVXMOHnYKAdj8DG9MxKfPfy4YWBUhTzsANDrM82yeIyiMpIF\nTrqV6TLH8t84AEnsI6G5IDX1WZqRczeIBgjjYfdwi9IDsQqW4zmpLzR9QkGa\nR1ejAXtzv1NEtDskbURXsLMAb4AvCbxT80LN/d9emqnAphdAd3NJMN3dtMzL\naT9M\r\n=7IBH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.9", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-slot": "0.0.4", "@radix-ui/react-dialog": "0.0.9", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "45fbe7b719eeaa55345d426bb5f7b2dca5aaf13b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.9.tgz", "fileCount": 12, "integrity": "sha512-UyQsA/1NOZQbIJ3Sw2Byq91WC/w9tGYTgeF5erS7+/INsZYwoEGKlkfiLdAf5VkgOEb2nDpVtJE9rlUgsLnf4w==", "signatures": [{"sig": "MEYCIQCE/3NaNnBtCeYPlSgYghzbFITR6BLfz1LimgLIpbu3bwIhAO39R7GSxKOtOmRLJQyRqO6dbuavJiAilPQxquIHuqsr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62854, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9dCRA9TVsSAnZWagAAOHsP/1kl7K7nNIFNJJ+xNB5I\nfM2HNUymItKRwQ3loVC3BzSEBLO6nB1P6LiCamiqpjNqQIjb7rf9+onJ1tuv\nBPbhCB0IyB4LwXXI0ucrMVkG+/0hvtt7datAPeKiSO5ART0mc2dII9Bbsko3\nyjDA6DyDbonyaQIUcHPsT1jjPtmq9Q6BI2JvxIK3VtgvOevfREBnUkdUAxbG\nmAJEkMIDVyHnOOkwzFkBKHOIPwYl/y8cDrMEeMm9abd2FGLgamVjUv0c3W44\nRqxRC93/VQ5INEBrpAS9Dh2qHeOjltSymMUpOA4M/7G+w0JSsxr4LOWBLuG8\nckUgwo/WWxW+ihoP+SlWfnkciR8OtAeLKTcS+1dZgSSHxzqGisSc2TGfwW3I\nryoiyODYwZJiKYoW1rF6+kqI0Vhg+d7G1YMOEEaM6YqtRqL6nGWVPW3qDvAU\nq0Pk4tOjFsBhRsimHOVZcnYjNaeEDA/viFUqnSUZePmm0FleqCTtJdkbL8xQ\nnGnF9BXgShQ6u6Gwz1f2SLnIqRNxAy73H5ueJuOf419HhNR9cS+UtomWIGgu\nRKWkghMOKwXE0wMCuawr2Uq5E5/qkQaxaUTTXwXUmgtx6Y+Of2VaXXTr65DH\nJVTYYhTheOT3a/LAwki4rknsvTTpToipZoS49rLvWop4qODP9SALE+PW7CEz\nsVuf\r\n=+yVY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.10", "dependencies": {"@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-slot": "0.0.5", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-dialog": "0.0.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "46611f0e332826550b9555d08aae066d117e0ef1", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.10.tgz", "fileCount": 12, "integrity": "sha512-nU0p8Wlu3NhiCXWcfPia/owM+QVCD1N91AUGT7873O+59h4fr6Tfjrp2NWNdct+8Hpi10ydZjyH8eB+/RNANIQ==", "signatures": [{"sig": "MEQCIDRUWjBfafAk4io6vzEX94jaoNMzeN0nQ24zWq2aTGKcAiADHsaBp8rD6B7QT6EWzUUkAGY+3GVwyZbcfr2nEISgTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOgCRA9TVsSAnZWagAAvdkP/jTboQoZAHvq4aK9g0ff\nmQtrJHu0qafZWsVfASBX8Qr9KM96mH70qNBsGTtW1gKdQpbGHlSvm3BpCqVM\nlS/BBSUQWP4YiVsdyqYFncPeQ80cg4SIMLssyDI6UlJECy6TqhyMP1yxJ33Y\naMXjVZlvQKwuApQqmYidi+FOBX9aDSsWxG2qM7h6YBdlH2upXy0JG78gwqax\nUQyEcNl3/8HGUbB1Xe0NpZGyrlH1G4BUPW9SREzg4l66VSm2GpiyPk7x6mAU\ntxBtvNeKvXxjm4BbJBT9lWSa+YSkRKF55cWf1Sur2ZxJGIfPz4oDte5doAmS\nCjZhaMzLv+De6hFttp57Lpq2rAyuzamGYoJmIIXp4bx6T//zCZoOVUijud68\nPHMzeQIDppUM9dkyHMwtybNeiXFLouceIC/h24TtxInB7bq0tZ7BezItvAlX\n3d4yGuOSG5Uleij696rUbYjCOnf9//TTVe+BLDRsN5BkP2lCjiAuDuTIiDw/\nBHHYCqbdWntJHz8ELCGZJPxm0pBCTGB6NH4QahXzFmLfdVDX56L9F7+Xbqy2\ncU9iTRkHHBKjw2OHrPPc9n8g/6W16gPINPaQzs419kAhYgQ9VDSnh/BNlQYP\nVNJ1LGhC4uPBJqtI5/4DTDQFGuJB8x4hm8X3Lnn8lTf3jMVnAq4Q8MfOnM1P\nGyMY\r\n=3CvN\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.11": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-slot": "0.0.6", "@radix-ui/react-dialog": "0.0.11", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "21f0f1777c7339e61fd735f835b497f861dfde09", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.11.tgz", "fileCount": 12, "integrity": "sha512-3cgtJF+kovBd8kB4ulE+fyAZjP31xcXrdoRzqdCRTEmgA5c+ZghrdfVA6JjaUJwN//Jl0/Jrlep4XQIdad+LOA==", "signatures": [{"sig": "MEUCIQCPnzydRjcMAvQ43Aghz/vcgM9febexrwfPXQBJ1ZZqWQIgahZSRpwFe6j7U/2eNJIuvfXSr4Sclpk8IGS0k6VTetU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gbCRA9TVsSAnZWagAAamkP/i2zjKDNvWND7/s0S2ta\nz5FI5Y3YxkBn5NnscudcXZ/owQlSzE0SC51/eX+J3FLVMCI10C7wB17tdlOC\nRxa5U1jUjW44t7FbI4haXN8ceJMK4Gf71yGRUN1Q6kParAd1hsogTG1HewyN\nLu4ZDiNpiruiEkJXRyiAOQQZu5JDEcFv/KXRRB2i2Roo7pQVrbUu6+kS19+l\n+ncj5GTvFcMYs6bclB20tW7cDkMVgUNqSDCJKNahQ7cMz4GO8Mjgdt/OOMkB\nxypicuCtfGaEqeIY0CAFpEpineXh1Foa+QWGR+FkqT4+RyPnjlSp7Rj0aosD\nT8TGCDrDp1VFHZLE0KSm8JbSOeFsGlZdBLqr+muqVxdjIWcv0jLEdgcft1P2\ntX5d/zxrp+x9RTIZ64CqjW6zZ86SsbyqSlMsjWkE2asXstLJ/o+j9Fz0H33E\nZGs4IzsMkKVuUmX4ARKxwA/5KRKnOFN4CZJ96tsJe1uEr40RQaEiQ30VXMcd\n3/dHZboLdSzGGwkhotYDCvz8Uft+8mtO0u10qn0FGh1Y3B1Nf9vwNGsoHqpS\n07Y7e8+t8Me9Np51xKdY/ALDxL2XEwBLncnKK3a8ZoSoH8ourOnyUUbervZV\n9WPF+/C/DUuLyz1Cf9cSe1R/HyNuUJduCytMj8w8UEfGa2onmCr+UNyux5ms\nZmkm\r\n=ogb5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-slot": "0.0.7", "@radix-ui/react-dialog": "0.0.12", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "ea7c463dc95305b7a3d5eb33e9042624918a3141", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.12.tgz", "fileCount": 12, "integrity": "sha512-B1/InAj9pHolQE1mLN7E76PNsBjtN4XiODd5VAygCH+A4Aj9ICVUxatjmmdsfx8WR421KlpUhIYkOnU4PcCHIg==", "signatures": [{"sig": "MEQCIHUiKCCM1LNmevVZJHAc7uW87eLQdFwcgSEOUbGT4BjyAiBQt4SvzjLwEWFLZJDCY7TgIjdPYDYloFXNycbdIpy7pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HhCRA9TVsSAnZWagAAOHcP/2aFZ9X4MlOgQdSI2PK5\nscePHhEsKgnj8/C8ic3LZoVT2bxuaVZaTuebdCcNl8LdL7rLsFm39LgxK/zj\nbDzjDRxYaFg331wuif42SO09ptiHHz+1+GoS64+lrD/lz0d/QCJMsBcmM654\npqpyl4YjeOum4xYtYZ62Zf73uGYLdYi0X39jiTFPetiNuc6QrILNrmX2lMhj\nqXaOxZtvNZvg6Tmf62uANRg9PIGkKZCA/Y4+jIJaxjZ2tVZ1XwoT76m0z0Ei\n6n8v1m628vXAjrnz4TiUMpG4zsqG5yzhPuGRSWI8EAMVvY+cWXFHcyTGTx+Z\nPyMu9m/KQ0i9Vtmk8VzZ4/W7KTbwhWUBcVlLk/MI4EeqCBTdtnYSoQhzLnAo\nxHKf7eMg3R8pBBHJP8V1cG5qLQDz7wVvtOY2aJJxvi5WYIxv+e8oskORJG0F\nrPD8fNqeALshZObgLaoDBlJ74MNhqbSQsm/0U9hXEji6jxD1z/TK+LgTf6pU\n1GWAcygbHRWcTC87ZjKv0By3ecbpILFTJ0KbAvg31SgBE6ErtmHMkJZDqush\nMJRSUJO6pleioomKKiY2cP1NZJ6T+tJ1H+Wi/IwUJCUtXDU2MhMJZIl/ZWxU\nrV0YZY1MbpQ89oCuoS8d2YH0fioakvLNjQcLEb4FtO/Q/AAhUcvjLuDBSgnB\n8Fie\r\n=e3oS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-slot": "0.0.8", "@radix-ui/react-dialog": "0.0.13", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "3aa3d7518452d02babc9becaf91c2fb3f3a15996", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.13.tgz", "fileCount": 12, "integrity": "sha512-TaxoZ/3M1UPTjQG5mIfSj5K1M6ARfbEmgt/wNQBFWUoeShTRbL8bRgwmNX/OJVSIaBpJ+VJLYfp4wgUMmBolMA==", "signatures": [{"sig": "MEUCIQCGg4gSuw7hfCWyVYBHLh4shtPw/AbS1jZgcLRbuRBGkgIgbg7sboQ2BncJBU8U2/DgCSNY3V7DauJtP1CnIAURM9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vbCRA9TVsSAnZWagAAISgP/iR9Q5ntEAA82oV5Syd2\n7bWN6eXOZ84a3OsG9V7khIk2C/DAiKIji8OVu4CyxhyuDhNjHI1KabYf+bn4\nZxIrJCPXIlWuiZkpJKgJNDgT4aPKKEYVIoFGceOTNdNWJRacdtGmirkZg5X6\n1/fI58b1ZSoMXr86kVSe3oE3OaqQJDzOnzVuSyG5DDBzGC8SPTQp+U53WXY+\nqBVeEKi6PoA2n7hvW6mVHiSoGB/NVc+gr6jSucvENDUfiwDjAahdGdSC9bv7\n2afYWInvXmw+4iOMgIfAfO1WnVGMcVUNPQyMCPkZViWP06l5mfKG/3cDu2xh\nLX3CW5XltPFnZLs1xChTCrWxZ4lhpv9YgwIlS7i2vopZbcYg4BWk++gtIVTT\npEw3Lkfv90nZk6JzHWYXS1WDzf/wQg/6ShUTpLz9c69LjvKvsbJ+N/S81zhw\nAcSUgaBimQ34J8M+rh52J1/8jVHht0sU5pw21Yv5qZe2m57QS7DocZh5GH2J\nwQIkLET6bhf21ki08U+xW+qTyahrtQinr0KBIqkj3tfjh3qFohTJENN90h0e\n1sjbvogF+b+jxBNxiWKcST9SJKDYjcSiBMOaNswNIsHXch1RuapT86vW2nBw\nQTfvEtmMVyIY0mWDSH65uMh1zIYc7EhEx7V/ViCTgaXqZCr+BDM5okXc0LuN\nvYQ1\r\n=jxiU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-slot": "0.0.8", "@radix-ui/react-dialog": "0.0.14", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5e60c9e52b2cb0bd6e3ae54391b84f878aff28b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.14.tgz", "fileCount": 12, "integrity": "sha512-xaNA5Vkh/2E20adh9HfrFI/cw2zAkCCa/yflXwqmTo6L1wOYxdq5RNNi1VQ0NbgbsoVltS1JOCTdU+EgFU+EDQ==", "signatures": [{"sig": "MEUCIQD9xnpoJaQhqTPs+WqPB74ncK+pJIqjM2j+XZ8o2vFKeAIgYoM0zEGgsKvtuQkafIH852jPdVOuZ5g8Rd3AqdyE7/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW6seCRA9TVsSAnZWagAAlrIP/3XSauIzSsAZK0FdE2q1\noLe/e46jUVrMZwL8Ry5X4waCSbiOp49KPySSOWsGtEK8SsOpktU+/a2fgdZN\noSEHrDownTr6d1I1mjwL0EcBs73p6Nh6JbFGdObdNCB763QcqV27rMjHADkk\nbDWjecq3PdAlVzirrJE947sCMLy3SZ49QIbmHfwuCZLpL3nKbCi3uhUIgA1B\nR4r2ErJJwI8MDf3V7TXfn6H3YB8IR9aVxRnbF/k2wNLWEB5krIhA0ujGcxHu\nUAiTTnQMpd/FKykIzmbUaEtRzJ6mU4ldL0IURk+r7GksKUEBWiWm93HTGgHp\nEi8tfEBw7GzDnN1YnVW0omHcajSuJIe2AnKl3emXzMQF/99xSnCyUkW/+gDG\nwCIvIluFC85YtJDI0MJbQTe9BenfdQ/8d8BP2UOuTLgqyXNEmlHXkRBVxz6+\nhmF4pyc7JYQfb9WAdp6inGp5IeGkMlzcl7g0auvnJpdQyK5vZGrin20iuswL\nJfUJOVnP2CjMvjkDpuvhESG0mh3mqL80MH9zTy1MT9SnarZZhhTL8RpFs2jx\nYbSuR4HcwBQwZ3yXza2oR8IpneFH9go3tuKd1kOVVnGBtudQ+Xvxwsv62h92\ny/rkwkNNj260vEUM7l8c1pUhDffYHNLzftyCpFq30hvSG+I4PK8M84hNI5tq\nyxBO\r\n=mmcf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.9", "@radix-ui/react-dialog": "0.0.15", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "aff68a903143cbc01f7ba09e13c73aa8e866c36c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.15.tgz", "fileCount": 12, "integrity": "sha512-bvhiWNd79KZWeKwsSJ5ebJ50EY6v3WdN3YiOk2u9pUhSrBSe2cKBLveNdUGuWxnIORMAn7I6vs5i9rxexSVsWA==", "signatures": [{"sig": "MEYCIQC7xgkGVYRKlRi4IJP0U+CsXtHiPqSatElnZ8lKMD5QlAIhAPd/nX9JSIM8jCYLmU/Ecbg1MzGuU6OGgzavrWz/CRK0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61061, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmRCRA9TVsSAnZWagAAijMP/1ltAOy/LnjDFi1rBZET\nPugqFFBpLGktPoWJiBLXW8aJtnzXq6dpJrM5ZF3HtZ6y1Sw3wYLLCh/z2Y0X\nr7JpDt35B9uVtkHiTRW7yWwk5ZE6Vjljtsf/CGctv9SjkSEDXVIa5OrEAw7j\nazQARPYak0Xopwiw8Sd/UbFwZx1rkg4BGJn1DZouLENhTqBoSEqRKB0+SF8j\nMAVz1+qKwJX7CnP8wAdVJVFgrvMWUx+8K7zlIIzq8tKfeRBKl9P60RY4/iw4\nDiDiWQdW0b6SDH9Vb/2dxBA+P79LaGdHmwuAIf8vK+h5+kORXtSyLAn2NvJV\n4bKVNJWhn78gGAfPGrdXtDlqr2Iryy0siUuWVu10LmW4/XqG4wJloPj+ZR4J\nBymPdvpAjLaQHN0FpPuxLd/fYHobGp8bBD+isHoNpvOKrpBgndyf2rmzuONY\n3abqxy3sQowk87+Rkjj8pCrIGhsljkmHI6KDVqLL/fAa98qJQyy5LnWqgMMS\nfZa/eeLOSk14icNZW2ejdp4YdHkt35Y26HMekS+/ZrZUbjyarLZlm6twdPRF\nU+qJbJmzgsACdmQHYdZd9wzm9ygU2yja0+1VZnmioLCCWA2ZiitWhWqMD5hX\nB7O9Rb+221x+z4McnoHSy9OKgT+o0sLDxYDunthsoel52EKon6pQtmJOXhhE\nvHhE\r\n=OuY2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.10", "@radix-ui/react-dialog": "0.0.16", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5b7dfee5f6a56fb0527481e5537e44e8c9119951", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.16.tgz", "fileCount": 12, "integrity": "sha512-jNpyvhTeGJ8CYlHQgDTxFHVbhuGrrSifgZ/vQkgCBDXlVXARoPaD9rFLZYnRDmLeYgOGpr/11A75XBnPBigkTw==", "signatures": [{"sig": "MEUCIQC9G809xYKzvCiOC3A6nTrg902EfcV3ZcLzcGnX7LB5VwIgQrYcvPrByzv+2esJVfNnW1198p7FxnFUAswwAELVfn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7FCRA9TVsSAnZWagAAQc4P/07so8TGpIBwp7oouoC0\nkzH2KenInAUn++Xj2jD8NJ4l09zx5HsTDi5h70DCOyhHge0Xaq1r0yB1koyD\nR47IwKLJ7I9XGnGNppABsmHcoa5TH8me67f6dBBSuFkqnU8sxlDU3dPQkAej\nikz5dA6Wd0qPFGdZtBSBiqTDNyMVGGoa1yRXa1EmsmuTmEUHL4Nxkse5ZG3Y\njB7IweGdHX0C7/5/PAJF8WkWnxLcyIqdWlFOXFgyQFdtZ9v3QdHDsS2be3rY\nPszthblDRGxhqZa7tH1KNYalbq9ZIj4/WFR6TphFOBLG9P3mHf2WvPk+cFTf\nV0ddk/J4uzqZpDW8x9Bwq/9LPqHRfbas7Jo2ppq84pkkqQOrOOPhRdlwjOlj\niZAiFZn2dOSKsOdf+sjR/CORXT6z01gkVX4okmA6N/NtdIdj8L27dTR6ikIS\nNClR2RmeUSI9ZTjCAxtxEjCRd656hLJNKWs9W6HZPNjyIR2pryjkvnK+aQcj\ngex054Hrmhx1196OzwlxSLbF/e9ni3HBWIANyi9ht2AUr2lnao/d27Tukxib\nGIGTQgyuZZeDsF+NznGtzO26dg3d5OgF5PAHgJVYIsevz7I48/6htDebXKrO\nIHua2s71itjkSvPzjFGDUYR9ty0tLN/kmuYJmHA6hLqmiWOkS21lJWUVi1O5\nwG95\r\n=kLDb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.10", "@radix-ui/react-dialog": "0.0.17", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3c814e8a34cf0aa2f1b7209e4a7b019cd3a8f7b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.17.tgz", "fileCount": 12, "integrity": "sha512-LaqOY17r4Tp385dUmYdbUTjLIhOtpzy3e1X7MrLhg92wBWijwQsK4uUnZtqazfzPN9Ohk2rTLP8bGNxPqDFUhA==", "signatures": [{"sig": "MEUCIQCJeClCBbu9xkdXGzZ1u46AKyJr7l1DgEbPCYCMjVXzrQIgaRkXhm5i3393Tq83vm8Rm0zMUSSha4Q9u2ut53qLbV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlXxCRA9TVsSAnZWagAASaYP/12W/j0Bfpq1LvwRuytD\neyJ+eioalgA0oBuBAlwKh1BnfPbmdEotbH9fEz5vEsCO6GyVwWbTfAwYzCmo\n7T9/hvsGSRgGv4xMQqDiiu21hnX8EL+WBvMn09CgZ8O1NZA8ctBZs7+MgHh2\npIUJjK+mHO3x41JuV58OJg/ky+QD8F0uJj/lxszAhzbtcFAauRyA3kYLNCgN\nQXznCV70lLeto/ZDx7gbe4cbk2AZXxZZMU4zKlqhZPJ0cdi3gO2lhFrH/hON\nnW0eOGHokQai9T14nY/DvJpS2jCskRWSruHFghuB1NF3jzclZIntZFRkPItp\nnCJdPRDaoN/baqgnmwJ3/0tqHqrFwd8cixkDEBX/Rim9fVavbzqMtPvpW1Vm\n9ZpGtjSvOrAUaLo7609J2f/mvwBHe0IW7XhjscTRB+ppZvLurEr5i5hqH4Q5\nf3Y/ALzx2SAN4V3sg+ZtZmkcJ6KlHCuFqOfPN5d4WF2R089hTcspuzo4vyQY\nhq28+0wMYKFXFiDECNmyluYdrf2gwWUAWYGMsBmzRehvnhOgno/SD/221Bvs\ntGC7fbWb117HbfJlBiL6iqgu7IeI60HTrJccKPihX2hJfPUeYQFP1cbY/sfT\nS3t1NA+5DHKNu9riw0uKRXkLfdrGVbn+kza+mTzFzeV1gskWPd8qJbi3rk5V\nlycF\r\n=rMJR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.18": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.11", "@radix-ui/react-dialog": "0.0.18", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "f46e75761157e9baad96860d382c5db0835b3346", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.18.tgz", "fileCount": 12, "integrity": "sha512-J6ocNH7bf+yIZsPJlhtrXVGUEsvt5xFYKZL8P/BdXlZD9MBw5LNyyxjYCg+zeOBepLxZTPhl7Dxgt+hZMea9Fg==", "signatures": [{"sig": "MEUCIQCZJ8YlfmXw3ZM/Rrw0BJrJ7qV85qtm2jfPN5dpHoShVQIgMxyjbvBdKfUHwZ5XGjkhyNxTDY0cVFKViTkqwEfCySg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9gCRA9TVsSAnZWagAABcwP/iodq7Fq7g7yM3Hb34HN\nR3aVlylAOrEFrfbFcp16baBo4TfUo46w5+CHv0MTEVCz8ifl22dwEHI4WqL3\nmKjVsht4Iundgjp82mbO6IaPl+9okmORVGx5wtb9uz/qyqR2pFmQ7Suj2CWC\nEBSUYTBxUErBVyLcKZN3pJIVkxQ9UZoC+6vo6c8wvsbEgaQEbBo4KBjXCExt\nfGJPfV90AwGqveExLAa1CJLdcZ/mwkAmXtx6Q2uakHfEiH215lLeXgsamyS0\nCeYN6jAI+bg4IHLQt98wQdJtMniElv7mhNOVHm0rQtAUi/vSaKnggDWmk5//\ncUwbJWVRXZdfLUv7RWZBI6ilMSaWI6ix49+yEaJfS4c0y+IcAxbqNjO/yThh\nKYSmonjaizr3SCn5UnSQBgGq8AErxIUL+T2g0uJuJY+BC60T8GAe1nur+ovr\nFZAONrEgm7vuDQDmr9Rqcgfsbcd7teZJ/Ozj/+A312GbM3eUfYqCP/vDHQ4p\nc4B2xljUPp+30p8+ns+azRcF/ghOlcNVztR4+mir7bh7NowJ897UFpLaMY7Q\naOWYqFyR3G8UGPofV9K5tqfKTtVsvejhUpfZspteExY8hOXz2EMyCUatnLUT\nMBTUjOeL9NkqYlLSUd8K35Kcw/BCMYQsK9OdrZKyj4/v8DPNeF4EpsuYoUxD\n3OJF\r\n=Ruhq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.19": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-dialog": "0.0.19", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5b69bfe063cdb13f49630ad2705e71228505d147", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.19.tgz", "fileCount": 12, "integrity": "sha512-SJRUT2s0/WLCvCEbfuKL5EM6QNXjZQkX9ZgkwKvgRNYu5zYEmCmlCUWDJbPIX1Y7w/a6tuEm24f3Uywd8VcBxw==", "signatures": [{"sig": "MEUCIDyl0mk+0XLLN2cbyGgT1/pl1UWy8xi5/z7B39kAGxmJAiEAifQOiJN+ZGSXTOSk6HzHos8k2fE75oMo/LdxpbE8z18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GUuCRA9TVsSAnZWagAAreAP/3TLNH4r14EZHrH/C9Ji\n9L2cTpz0JXe9kVtGwNypo5nWkeAVxGkf0ieKaZ3VC27+63tHXXaiKMrC4OYR\nQxkgtKfQ1s1Ysl2zta+1aDkfpf1iG3JgsEyCJyZzpJhqBEyIwBCTZVgQcDdy\n1UzmhT7BtVPbIuZWlELL+TszJQIk7B1rpEQF0XFEZarTPNXMgfM7dycyUxDR\nYXYUw36iGk2qf+MDYz3kMzOI4P9G8yr9yYgUssPiCcAgJrmRcbu0MDt1KJDx\n0aFN0bULk3x7SVgkUqtH+2OsiYOlIuHloxLqDnbY6j2Z2LL8i3cOzSB+XsGS\n/jhkV73xmfyT/7k0kSi4/nAOwavGvlcDK/U/pcGPtr30F9bMN5I/QBezKKy3\nOVvVXenZ62JcYSFQUif0zljl+xpT1fsSsrNVKA+vt0Bo8hhp/1jh/9mXkJ/0\nIv+ytBOh7gfr8uxlWUoHwwR7ai7cE+LB+fAUhyqfgXUTtTOGFzcPO5oNJjc8\nn3CYfuFeMlvYFhJfDT7ImJ6++7mm15duqddhoCzzVjMzx6qB/+z8gPR3Hp3i\nw3T08vLDGibBX8cvNiWj2ltigHnRhBm8gy2vFY9FSLWm3CJ/7f9i3R6qTPaj\nwoWsyRkYprpwdwPZk0Ol5pRsQxMSpWKYKgOamQ3rFYvXnKB/pRVwyDdfrBjU\nNgjc\r\n=GHBJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.20": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-slot": "0.0.12", "@radix-ui/react-dialog": "0.0.20", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1adb997c899fd9cb6f0d13bc66b50d2166414339", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.20.tgz", "fileCount": 12, "integrity": "sha512-Vaz16wc4rDVHC7BH2At3TM+HEU56jN6fAWoeXatZyv1BAaehSabusbghC4V8Cfc0llP4ijvOY7Eznkt0+jP/fQ==", "signatures": [{"sig": "MEUCIFOyYY7VtfaZDL67r7kmzqhesVtO2EUg0RQ/eg+FmuGcAiEAhnzzi0yoQN5YkSd+2EiR8dky2OgggtTp2y+hteX7Z4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTjCRA9TVsSAnZWagAAUQ4P/iVY0XI4rcbXM7Oxu0Hr\ndD1pgVEEM4ckOxsaf1g2HE5CpIU82LbCnvDnP4VbFv3cMmHcDGqVrr95MTzQ\nBsQkWyzLHUbn8nRRpcp1rPDkxBbvufFv8zMpH+WlZaOGe558D8+7Kt0aQ7F4\niqdTcqXIAMJdZe7tURh7M8mFdeJeTZxRJOLo90RltuFYI0tWZpBMmarrKBkc\nPuzYk0SM65PxiJlLOcXbl5OvryQ/udEqfBJ2oUECnYtb+S/ax7SSTo8DMX0m\n9HXlEimv4cDPuiEGbugcXmApv/PTomcxxRoOYwalXjLLQUVJZUwmszYHzYs+\n5bJBSYLMHqyVIvyxEUkxt5LPZLgQxX2UhpgEXHG0NYXsvNfwsTaKUgzocwET\niQWlEAP29aI6HJ8/FB3zQ2AattyVdv6HlMXLPStA/8ONEYmVM4vNSNq8ZhzC\nfjDt2h5OL+8wsV62/erplAo/IJMsJRCkjuhQ87kfPFfpHD9RLgkFY9Mru/2Y\nGdPtRi3sEDvaiocewMSsdE+6o9vjWGFvS5Wmbw8lBsPA0VI5AN9afac59KE0\naMsfFJkMP9jDHoJhk26J4znJsnMQ7Np2DBESu14bqXn3nmInOA4wlCFHj1VF\nSTsGLwEdRk3Eayaxc7hiox1gS5WB01Jt9f2Cdc/PITqTTPi23g6rgllW5/YN\nHHrO\r\n=jLS5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-slot": "0.1.0-rc.1", "@radix-ui/react-dialog": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b3f144f38a81247ba0d4dac8ccc1e61514e99c43", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-W2H4IhOTlE3Ew9zO6iEkXKaKoo+ebrcPsiT+mnE97RNvFYnTLELU302t2u0DQZWX0LQxvEJv3y5VzXxYr9AYdA==", "signatures": [{"sig": "MEUCIBI0hNPjC67Zhs4D+BsCqoDTKpeoIzR8XfMGuw+ot3ThAiEAkit6226/Zqepd1x7VgO6MdihG86Iuxxt1GGpaYmHeFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpMCRA9TVsSAnZWagAAlH8P/iW8/JCxt8Gt4Pa5pQ/I\nc32HHDuUV2bKFxgStV4uz4keb+MREd3jd77vJbHXmK4ZX6e1r+sF64vqNvTg\nKyArDGm9NxyXYZd6JBRfX3LnXLoLxC1yjMVmAJyGmqmp8GzEyDQWvvvaQJd3\n2MnRYPR2XF7Dffupwz+henVf1J3PlbLclPcOyRI6dHVB9LpThASPhw2BBpWF\nwSsKZ5zvW6X0K7pJ7WOqa329I9d0rdqqQvDrnh8Z91BTPB0TzT2mN3DP0YZw\n4YFISgDiTY+y4hSKV3O45YSfPLcEsZcJrr7l/xQiIGJcwiRbTqX17oStOizJ\nldDsRJbz6pr62ywgugVpg9QM+BdqDsSGul8+/8aqKfJ8wfNCO0NYgkxrHJVD\n0BgLg3eDJeRxb1M4TnQvwjXwEK8yWSYmgwhjXahK/vJmw4sLyKGHbvsfrNLZ\neRX7/pZ7QI0B0yyS49B5dRRpnD/OcI4Ge8hA/MCrmVy50oKS/OcbUnVALFt6\n/csmAs+FxUwgOHq00d6IxXkH8CX1Z7H1NvORPqYhKr9eLyuMQVtzIMkKaVKs\nIrQhaBCfH6AjNVyql7xFvoTrZ1BVDKw6UoxSQq+KCgINv46L4UvoNMjhU17B\nYsZ8SkvBGM5NB8o3tC2M8yKfHZE04FcfK+V5GXOzuEJhkxvy8hPR9lhStB6/\n5hz6\r\n=6V4u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-slot": "0.1.0-rc.2", "@radix-ui/react-dialog": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "caaa68cc7557e0e136c98b19371d15070ab4e394", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.0-rc.2.tgz", "fileCount": 12, "integrity": "sha512-igsXVFajO7ucsL7WW9Vre5UN/H737GKCEvpht0qzV69RV3iJ1jYwcioSxP1FXdsPXLKvKnr3/ykhCE7GeWpwlg==", "signatures": [{"sig": "MEUCIH8Z6fKM7zvSEq/PfvlnJO2U+CyiC2MdZqmLzKM1CNnoAiEAmOCKHEElfweGSowqwBUEYQ6pars8mxD428ppsg7lyZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61936, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyECRA9TVsSAnZWagAA+lQP/R5oUH/tyujuuCvmVj7f\n9PnoDpOqlW3hHUu6uuS8qlKiYm5LREO/YsDs0yA8s5ZfHOX9BLQ0lTteukkI\ny5OlS5n6gZa2dw5lcPgy01DmYg7EPjq6SPjjJajgTkPZvp6crpAX6lCzC+Si\njADaTdu0ZuuKSM2w8GI8NkyYmCcUxbQwyCahjdACMNy8yXlHl45e5TfDMDVr\nEXJl+niy07+oAIFC0Bs4yk9gw1HaHqn8+IKarMqeS7ZvORGhHVyMwJ2ak3a5\nM3/arEA1rc9ifP9y5n+L8MIrSCVWhfLqHw2ofQuz55VUVmZCBCzBhAbszuOX\nSBl32gAfL79vW8lPCXB3+lnxTevfHd2fSppzGUQzglmodowJ1f6iFb6m5Hkq\n9ZX74Nt9+2mN6w3eeFPk053VMeI0raCbMJR8lQD8Dr6Dj7dG9Sh4ic8Np+bU\nIXlfH47r6mbNHJ3+V9S5CpFjlGW/msUGnteb+MAUlU3MjkSBN3+Eu4P6ZR++\nLnyghOsjbyEkhxnNur2uQDc804hZ4v9LQ2tPWXV7FrMqU1Kd3DGncqvKBaaT\n2U0N7PRvRNpLQUl56bc5bpOZwnIDjpI8v9iONA/TCPE37ROXgV7aTkWg0wwn\n/ERi8Ql2/IjCbDf1tUn7TRZvheKIp3/zmtoR/mfg5p4H/y1WtZ2jIpu4hqVG\n+9Em\r\n=TWHf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b584ce023a398fa466135c5428f4f5632de6aab7", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.0.tgz", "fileCount": 12, "integrity": "sha512-h8k0s+De9mCHz86CZ6gQGSdBHB2aTlA6T009HTA6+xrh2dBy70uLCs+lDexg15/VEIVJHFOYnV70gNJ5VLqX8Q==", "signatures": [{"sig": "MEUCIGA+3ElfSt9+U0VpvIfofjmgT59yJ1WBsDU3tMaqAnokAiEAuGjqzWcBTGNNaK2HiAS5WOFR6tl4D/v9lqP3VTvsvvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61877, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmQCRA9TVsSAnZWagAANeIP/RH6y1PUL4FrfxOmaJLk\n7u+QaZ8DQwrnceNQWB9zNKdjiV4dG6rmIMrk1mZA/zvumB8WjW7jKupzc07l\naov/CffOX7MMUloLoYW2hLfVhabGuSVaE3/yjvoEPWU/2/C5XWnz/X/zKdFG\nfORHBX8TzDj57iQtNPHX1lwPFbAUUmrandC/iYIdOKExbdFWghcbtpK2wL2S\nSr5kJNKi6RWQOO/cWyK+lH6EmQ0cAMCjBEOEkmlzQjZcrFrl1MJRpUz1xX+I\nqdMqTr6usnH1YEnQ1LTTu9llqh8fEe0iGeDv31asmcMAoFyKnBGSXqX51Xo1\nQGYj4/UhWBKXGAwfNaangQFLxRsYsJEq0b/p+ENSg52+oYNHXta5DtIEQXQy\n60fhNEsm2+tE4dhwIiz64BfCyRiKdrUtVBl7bwCqMp9u57nGuLa1qA5UMheQ\nmMN4G1Vz4/+iMzvNLmFXiEOdlsXQtittDYesYzTWsFJVI/0sd3tWw/3BYlNh\nNKynNtvsdCG+i1umvaBbwE/mO1AWq3IVX4f0glVSbezh3BJZ1Pj41Drb+f7N\nRURiaaiOqGDanQwQOPehCiaCu4iuvs9HnDtihOVFpc6C76jgZC10aO7sz33y\nGrlw77+hVDpSJhn30vRhzy5rVN6YcLtt+uumDsOz2sTHNeYHtTfIX7TudHKX\nZBbF\r\n=z/vP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9185c14a43403a59bbdbf86f8d1c89ec18096e6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.1.tgz", "fileCount": 12, "integrity": "sha512-920HmPw0LpRGp+42Y3RYVIvwIIozP780iQWBDMbroUaPCmBcR0YG6Q2EnfB5avfYGh9tRTaTeh5GO/4N38W64w==", "signatures": [{"sig": "MEUCIQDBuprCPl2iUkPEftmkUzbnsF5PtSzZlJf4DSAc7gg0xgIgRFAa9yeTLnn20PF+0N0CWbCussTIZxvJ357K63IKz10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOMNYCRA9TVsSAnZWagAAc6cP/2TaP48eWZh2/zv/Td6N\nZiBOciSev4wH1cu8MoTU76NYf4m+/5ALyzKliYQJzUGF37F8VtRYlpQNj1hE\njtQMB2HgDDZmpIGtQgy0Up+S4PX2E3Et7DeAEUn+t1YzgHzj5y7OOt8OdmA/\nT1hEcxQ8CqXglANXV2KHzfehfLh7KcirHKtXtTX3TFiSEF4IcD1PbzNS+F/Y\ndAGgSF1XSt33EpIKzSAWNTT05j6mrN0A1FsDhBADuGzYnNcXP6aGl6jeOfV5\nK/bIGhzvTqR4enQ1bOfk84vIN5xg1gbVbq786fsclAAs1NKYpuTt8vZTEpiN\nQanKGr3ko9JNoJ0UnHq1xe+DfbHpxIxOv5J+M6xD2XpUWTwtFvCSXqgrTt/E\njOp1xdj8yP00q/DSgqRLRliymuIcHP+Md4TrDr0GCnZ2hBUDx7dlGzivjGcM\nNciMgmRTFU39F9A0Nc/EDlKUj7t2KwBu/OKTQEZ1Tiia+73PQHpLdg1743FE\nfgLzHk5d4PRLVKSYGXXN7Y4VHXZhDI6aOaLI/kVljocKD/3LNgvt8K3ueNVJ\nuKTS0y3zkhjtsNQyZkoqYgomlcvQVjIi0CKsFuIUApt363jAjB5f398Vb2df\nVsi0kHb5rJxY7SZz4lRL6IPeOdDYcOGwEeBXJaRhrJgUAeF1SBIC0aY0YLuJ\nAIRM\r\n=5ZgD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "96a5ec3165bba910b86dc759a0dbee96845fb28a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.2.tgz", "fileCount": 12, "integrity": "sha512-IVOlfmnrFpV6dn/HC7YjhYI5lbJTl+/MouecioLTfPhLVkds4odGzteROAypDQALuhhFEFK1kaGB/p6/iIhWww==", "signatures": [{"sig": "MEUCIQCu9/w11ly5hpXz8KQ2RlQk+kZ15zIPPMaGXJZbQnXPhwIgGon7AVhG8XVAL2Xb+TOi7fR3UHGSbVuJnfTKfJQTEoQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImACRA9TVsSAnZWagAAy5IP/2xJGB+670hvKrOdkPKm\nUVby/+4nbwWwaH6js1kK7N8OEMQNsCl+T1ubz5Qud3flJyed4S7UdjGNBL1/\n6nBNCtBoMu+YnC3TiMudxdZY8S9CFLs9//1vza8xGoCc4KHlHkZZZyoU0S7f\nIzzuSpLdRSA7kQgnRM49I+F+5PVz3cZuZG9FaAF8lATiNiHHu/CSnEi5DatS\naHAsCScRkkQj7OQSQ7AP6/DB5HRoAcwwbfKXdwcCs7/s91Vt/HSlu9KsCwvo\ntG1KKuPJbDXmjrt8XldG8iIXAN5+CeKrU9YnTFjnQRcWXgSoWVNcsEacZleQ\nbvjPBMTyVLcTO1fFMGBnQ8QW+JrXnHsxCf+d7J1YEvuzVkHAEL34nhapo2T+\nBWAcwI1lMBwLR4XBCkv9c7ioY/9vRH0DPb6tk11VNrPRY+K1irEDGYpfpm3I\nrSAYlnKdIhtzb4/2pkpxhYxliwVVBvjCTawpR7U/LSZLQnhRzBrr5jwJ7e7w\nLE3bSq1sO2BWeCvSVWAnAJtkS6/v/wF0KVyVIgJcJe5sZ3LthCaqWvTvJJCX\nQjccwrDJp6SSlzXRx2lcpJ8OSLaGdeG4liKndfqGdtBDW8ujAiFW+1xRci/M\nNt7Pvrs11Y3cMkNAWa33urs0rnecZ5uyeNrs7jKRQbBMXoFSG8U2imSIeeCo\nMOi3\r\n=Si4w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "8d10712c89151e22e44991475e88fb75fb26ab6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.3.tgz", "fileCount": 12, "integrity": "sha512-M42Te1MYZvX70g5K3BodXoFwRDu+/2aUrToFRgoiQN+yhczcRdNj4yg/Bt7hZeTppyQZIeqBy06USiad0Mak6g==", "signatures": [{"sig": "MEYCIQC4+8QNnjnzVQ/PR5VYErT30+qOA93qF/80Wzu14f/KqgIhAMQSNasC8aY8oEdpiLxO2KayXpqZidorYvjTDIfPx06h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQduwCRA9TVsSAnZWagAAEjkP/2weyRjq7wSdHyZ5bbbP\nKbpQCRDXNB1lY6cyw+qegDvKXQQtbIbdzehjN9O7hsy7XRWb3NjduDKg8Zq8\nd2/Rxh9LMTFK3T41mv8NLHi+R2VjT09GVvAvNlxAvWMccRHf6llOFn4/lZNA\nU4sj/Bmss1F6Fzq95gCVQFTMvXSS4zynIXF1StdfsxQLM82f1Xaw09EVnvNx\nnuLSskuMQfKBkzkzsc+6AYtQURRr749QDRjlmsoSF96ANf/ocbx9KbtxF4Vw\nlc3Bh/OcB35hNv6dnIZuZrLPlGINYnWVq0xfwZSY44pEgeZerkdFN5JD1Vbl\nrnNFUDn8r+kLSNE4bxkKfoShvDQrWB4JyjV9Xk1QAhbXgtZbG0tE2pf+qVm+\nmPth1HPuAWzh6rQNq6RkQivRMw4kZLmA0Fkas5GuNs2wxUonTYgfSXNqU4y/\nEJsLVmByGE9hb3Lvu+SPvGdTbFk5+xBbGFBhNNyMyry3Rs9920D2R1f9b7ri\nWnXBqpbTKggZHj04cNa28iJd1X0E7K8jZ+cNZR0tOp2gkPF5p0/oek2OjAFD\n0ZelsERhxhZCbw5/ZK79CTim+UkOjXn8RY7wwaWkK8ocx3+JuG5bdjcoLDRd\nJco+aqW8GhtoONzSkVnctwRw47L4q7bwpieKOp7B/pkWNAjtKAwGEZg5Lc2i\nezHa\r\n=hEbN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "20e0da7dd0be0905baf590fb518e6f519a9866a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.4.tgz", "fileCount": 12, "integrity": "sha512-5qUtPKotPbajKtPh97NaNKAqK7wPlMTRMmdpvFsXseEJl9xjGb60dAadWIojRKIKco4p9svrzy136QVfWOzkHg==", "signatures": [{"sig": "MEUCICFDio1qy/DXPUFr3GOY2CPnQSv+Dac7b5DiXNVKO2xgAiEA91/bDCVBxPdfURzOA0atfi5soBmbLk2trixDyNvMB+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0S6CRA9TVsSAnZWagAAMeoP/ihzHWR+JiNPTw30pWDT\n8WHY3Eu3IhHm9tdO/XXoTgUYlPr8XmbDChObPThLrObbMPOPiIIgJF6sFz4X\nmfq6xEr0PfKIlc8zFzXjxI3ca4zHJ5SZUijUhkCpvW6AgOMIgWhfYA0fFv3n\nVeWgbIFQRI5y+xUmV1/mqIL4hcLXEiLoEYmEf2VBmVn9xTE1APClYB+EhxQN\nZpHBMSlF/nf/MwDl+6UV/PoQxbol5aosi2UTZ4bhD5qcln82LkV/CtP64WLB\nBvkTg6Ysiaw0ZVtsaFQMPnCJ9TRpR4m4Nr63E2A7dAhSNl8watxV5VCBdqp1\n2tIGbjvAFuCjrti/qBxqS9tr0DPFx4flKRHwTRdeKEapuVtGdCxLpwL33z+y\nsRWG6/Y82kq6eS1QxHsGCptM10twaHJT0MiQOXl9xQrfa4Xpo2eab2rQp32g\nKHlMaQIZ/Pu+ADYh8RC/pO4iLLMyInJMiXPcm6YmzBOw+dbOrI7mzqILfv+i\nHClVKAEG6chbK/0/8dZdYoLQSuOMFU6vbNJzmbxs6EAsMTzIaDlunyLXSA1J\n6z3v0FhE4JH26bDuBk6vf7bOKdvidVubeqKoO8r8Rusn9mdd5f3ICCPKtH75\nbF7V1DWLegNG1+KnJoD8FeiT/AmdA4OO5bpv6JznuPKhu0ydClFSbN3LeOW2\nr19j\r\n=S6cS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.0", "@radix-ui/react-dialog": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "730fd506c7fa241564ca459c7befc3aa41a601e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.5.tgz", "fileCount": 12, "integrity": "sha512-OBX9mcG33G/k1KKWRRBQ9gmp19j5eipdzDaQo8Lu+ZBPtw0aW3XddwSGEEwPxPLVLQhGhLCPRWHrrMy2uevW+Q==", "signatures": [{"sig": "MEQCIC3T43/ojk5yciUB1guoLTXTxznqJPLlVuFTouPisHWPAiA2SMAiOQv9musl+/pByWhWjPohMv/t0wpfNatGozNX9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1yfCRA9TVsSAnZWagAAb1QP/3vC6oOBEvG+JNsuSXPL\nVlHQkey+y14L71NIxZIaamXgfLF+ZncNiiHFax5vsV9PcoZgMI6XmFs/dfPt\ncDAt2BsMmlv7Lg5CSXsqXXctDN+LUyBbkFwa9374Na1j6kzljV68qz2Pssc6\nfX+d3g5o0eoTccuzYS+9im+EEqPZdtGKUFtUwbIuRnjZg9LGRjyyQhnRckmM\njhIPCeWA3Q52V6cjGkLGxg9h0yN/7NL7l+TxuijZo9FMvo2f0l//GsZ9LKM6\nTEtaes0aQBfL3rPXgIJncSaDtSVf5wPfCz6V3u5uf6ZVG6G0gQA2Kvv9aQIk\nV7UqBPBT8i3xJjfmY96MgDGQx/TIKXxsiTYiU+PRqe0wpncn2xyvRT0gbcwa\nkwImQ7xIjeEJGejs594rlIa/TZSBMfaUrxHfc8fn+l0TS8gYuln4jNac2Zqw\npdczznie6d8Lbz6UeoTV/xLWwnKO3MHn0gL9i+EQ+KX/s27CdHgH5ut9CmZ1\nLRjbv1+vfTrmpHMpWetV9H2RnZEM20B4rtEdKK6aqYng048zfb7PlncXEAPO\ntGdnHs76ZHPgtMRnDdbda272Qybj/MsWwUBahXr0+Oxkd3jTT6Ify6Dr2hH+\nKj2yEEeu0DLEY8/N9U8hdYodapNdC7QiQfmNIsXsp1TAw1wzZpxJcb0HLn7f\nZTQj\r\n=Asry\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.1", "@radix-ui/react-dialog": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "834470c575e7fbe97f142b150f80ed5b987af1c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.6.tgz", "fileCount": 12, "integrity": "sha512-WNyRBQ93kb81JeYXhIUizyRhNZf4L1hAZcZoFipc8PUG2B0LGzDDAChoRskWMh+VtZaV+6YyX52s4YdGf2KluA==", "signatures": [{"sig": "MEYCIQCSJM1XNtLeUsIWUTxkcdK8Jy1mR8Ur4efo9Jrpgf7cawIhAI5vQE2MmwduklFpSmUBzf6BFUzlzU9wUkd2Z7yD/tjI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFhdCRA9TVsSAnZWagAAiu8P/0+yF6/1y5/vqpH41dNK\nhw8HBAEiUD9KFGd/Y6vSSWOnUQuPTPzqqpm8V92E0E/pd12VgrHcJutfNeyB\nMJj83gtf/N/F9N0rybjVMUUNSSYRP045XHrSlYzJOMGdD05phBIFiy25gyO/\n/6XDLnZtyb0tAyCSNh77sQTIEUT3rpr8lqPUAB9vhqnXF0JMcL8gjlGDxEPn\ng7R+uo2W+vyQ9a0YQ5NbbHRUz6wFRXnx87SGszX+BWkZ28CFR8/QL31iaJV5\nZtUcl09yF/XbU1dhvnxyp/hrpgKKd3lEwv0NPOd03gFjlYddsyr750zLUHAk\nPMQMH3js60w6COIXIQtulI3wf5pZZDdy8cRBEl1iVCvVLIrykp2VEUaAj7XJ\nTe3FOeLj5c6SJ0zDUzyKj76kF/w0KtB9FBlpGncSWq2uq8WnGNLt+7uPdOFN\n/NzhabVQvyBWZXOl91YiiHCV12vCFxozAFXF5HqG6fvaDCnTKUgMFzH9KLxm\nyl8tiipdU4LnAXgwszf6z41q6gNBL77ZoGSbMORidOGYIYJ0GhqeWfNQ2lZY\n1TilhT8E+a/aT7ZYEuYnZ8+0hFzFaKIOY2mXoE8ZUsExcXjg6aV2abdIbPti\nlDuJOHDot5fZRsNutl+oRE8IbT23gUgcl4aJukxnGMnI2V+IC2TdhRLfbI5P\nuIVB\r\n=1nIf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.2", "@radix-ui/react-dialog": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7297b7ccecfa1def1fcad35e2ea138ee48c3f0db", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.7.tgz", "fileCount": 12, "integrity": "sha512-xJtEL2CJNy5UD9XxRZudDM+38Wj6a6dOTFqc+mQdwMY5eJYPA6z3HZZkpPwEBul8fcGkgUVvmK9ZcOA/mGsJPw==", "signatures": [{"sig": "MEQCIDjajLxy9ZirXPjIzprUZEGHz1V8aIyB+YlI2FPdFfRGAiARxNGC3ynJ3AmOSgzJ3VSMdhsfrq2yga+m0pTqeMQ6lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62405}}, "0.1.1-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.3", "@radix-ui/react-dialog": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "12f4d9cb4cee90f92fdd2970b3e23a40d2b855fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.8.tgz", "fileCount": 12, "integrity": "sha512-QjO/JGHA4YBonE44VNxTO7JOh4RdnJiPzqmtkaluVVYcHBW5/KMNBWc6TZ4gAynmBePyfwajegMoM1EWWszBXw==", "signatures": [{"sig": "MEUCIQCQeMdib/avvLlhr6giJwbismRk9Xswkdoo9Ycy0kYjlwIgLmCGF982n0Oxv5sQe+OpP9h5UUCUlMd2qvefgTiFfAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62405}}, "0.1.1-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.4", "@radix-ui/react-dialog": "0.1.1-rc.8", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4245f95250726afb607a8945e47a6704bb97bab4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.9.tgz", "fileCount": 12, "integrity": "sha512-W04X1EFgBjcUJ/x3/due4s/M6LJXw+4VVDYuKBI2ENlspJkE2hO9uNyc/9K7hW5R5MlxV9xVqh1xiWJRkHpIJw==", "signatures": [{"sig": "MEQCIC9oR4ek2sOe51oZhorEE5Pk9ce0MYk8JxDXuSGz9qEvAiBWyU39Z4aPel9VPRmq3kdFxO8N8d/jy6RQnDfWvmTMIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62405}}, "0.1.1-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.5", "@radix-ui/react-dialog": "0.1.1-rc.9", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "951888405be0ea99e3d0698357c1a056842e3a0c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.10.tgz", "fileCount": 12, "integrity": "sha512-H6WKNF/uC3TDn1KZUqagFtBGTOV2veF7CO1uWEl6vmanFPe9CSUzF5tCdWpbc6UJNWUMCribFVOXoHoWxvUJkQ==", "signatures": [{"sig": "MEYCIQCeBCHWqUEFu2z5QJgznRju2m12Egz3z3ItL7OP+mH5NwIhALxTwmDsoNbeZjT/OvwOYM7DR0WiiWLoR33bAbZm/tx/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62406}}, "0.1.1-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.6", "@radix-ui/react-dialog": "0.1.1-rc.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c5cbf13f31b443fa161cabeb8daa494bd3c330d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.11.tgz", "fileCount": 12, "integrity": "sha512-SCebQxcqHPLy0296Tot4eOrbdGF8NYhX8vT3qdd8ZwayNI9ZxRHd7TKHU9D5/SWTCbW+vnhFes7CaJxSvUqVQQ==", "signatures": [{"sig": "MEQCIHZig3S82cpJGlgS/35ExP4kKCBvS+zgXw2/K7uBdk3eAiAsdmTy89CNqX4cHFvkkHQE1iJwfOsSuUf4Y2L05e2QvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62407}}, "0.1.1-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.7", "@radix-ui/react-dialog": "0.1.1-rc.11", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5fcf054f095486f092cf8eec21504d553abd92aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.12.tgz", "fileCount": 12, "integrity": "sha512-HatRaSeqLaONuZJZzZTQWaeP01u1mNU/JhZmiZT3FpdzYbdSd3BtvV/0CvYFDqOLZTbNRmHZrCBR9kukAWN4Tg==", "signatures": [{"sig": "MEUCIEj+bAnyAQHGgFqUQvFj76pT8wlhkVY7d5IHbk7e+4WHAiEAotzcTpOR7D0Nv3zPEu0jL8LtxzPOKlJyYQUTuHAFYKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62407}}, "0.1.1-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.8", "@radix-ui/react-dialog": "0.1.1-rc.12", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "88977e7ed7c250805d18d08e583873c21fd590d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.13.tgz", "fileCount": 12, "integrity": "sha512-O+0retNYWw9jN42VahSaKL6OWtn60vgYKzyPt+cpk28XtJtgGcBK1wf4X3q74YiXMYwSQkmbjW2BGbRYRrQrrQ==", "signatures": [{"sig": "MEYCIQCkiY1pj/mwErEsEOOClXg8WbuYJixGaNnZYwFan/fafQIhAMviFxQnZo7V4vMO8tLhxIHUNpUqBabq2F1xBqQapBtl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62407}}, "0.1.1-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.9", "@radix-ui/react-dialog": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1ec7c473f06057e37a15708c90bf5c027cd4f418", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.14.tgz", "fileCount": 12, "integrity": "sha512-r5P6tLpVNFff5ZUiIGyfVAZ+beext7L01F4Hi2qkHyHFNLLixlCGCiE2SsRiNEQuB3pQzp2X0eT0MjSdEDaVJg==", "signatures": [{"sig": "MEUCIB1J5/iVMDS52avSKZwQgPb7j22Qa1U8BJsLstqMsFvdAiEA258u3lsLSqmi7XHraRXHPdjNjG+cCmcmgC3jzMCYNmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62407}}, "0.1.1-rc.15": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.10", "@radix-ui/react-dialog": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7e8bdda4b6a165369c1d0ea81d514280a7a6c960", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.15.tgz", "fileCount": 12, "integrity": "sha512-Bf7Mo5gfFmboMoVJcGmVml6T9SJkeWKqLJGO5lxwC/6xkVKR19xk5YjZcWZf3FKECJTKqyeitRYSZkcpEtXeBw==", "signatures": [{"sig": "MEUCIQD2vj1TKMC5F9oKzmsugHxjTLlMq78SWtEsjiHIkxT5aQIgSPwNpJgPLWh+wlwS9RWrtl+zZdLu3bEXZFROzh7sgJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62408}}, "0.1.1-rc.16": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.11", "@radix-ui/react-dialog": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d2f5df8ce2d74911ab7d9922af34299b432ed5c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.16.tgz", "fileCount": 12, "integrity": "sha512-wVdrbYNyG7NQ+LQBA8OWubfYyO9uJYefya+AdMADozEaDFmQRg3fQa/VRYYDMuLRaUq/68DY6XeA2xExNKKt1w==", "signatures": [{"sig": "MEQCIAWyRP/IxMFtVYwSXcjyOgZYcLwKdoEnASNTlSBcM4K0AiAccNYfnTPykocn0pY89mSXoeUlGagGKyt3kk1eapu6QQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62408}}, "0.1.1-rc.17": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.12", "@radix-ui/react-dialog": "0.1.1-rc.16", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ab932e09d632f1132246a378fac4dce3af4ade4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.17.tgz", "fileCount": 12, "integrity": "sha512-DXzS6ofsCmdMEeyZgaRt2xwUu40VClZL8f9YOW6qlgCRKwZVR9W4PabpKAGkewGaafg7d5fecy6skOZJJpim8A==", "signatures": [{"sig": "MEQCICPs2wkh3XK0Dx8pKau1DZv1Rraq/j5tAh4FHRBSW0T5AiBLsysj6o7IY7yFsah4lt+pOcGNasudTumGgOeccT6EaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62408}}, "0.1.1-rc.18": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.13", "@radix-ui/react-dialog": "0.1.1-rc.17", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0b9a181cae90e5b76e9a563295ad3acefed139d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.18.tgz", "fileCount": 12, "integrity": "sha512-O+NzG5JNsAFHsVLapCr8alYkkoXpXBOpadOtoBzaua+sELGlIfBR4sOiriDkOAY2N+++pdUA3M+Vg6PNIrLlCg==", "signatures": [{"sig": "MEQCIGyR3h366X9yHJACrsCZng8KoH4UPFxW0H081fKaOV+MAiANrByOfUfhu9IhrY5ZwJGb77sWMhXQ+tQORUv5+rCKIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70761}}, "0.1.1-rc.19": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.14", "@radix-ui/react-dialog": "0.1.1-rc.18", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e330a51167901cf82be6bb809d71f19276243790", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.19.tgz", "fileCount": 12, "integrity": "sha512-jI8OTMmZ6apHdRKh6xKy7nOx5xXcuTD4upDzv7BFYcJCSimRAZL5H5RQY3OwRMUwkNneodCY5OzN2iGkmRvnrA==", "signatures": [{"sig": "MEQCIHsS1kgMfXb6h2oxdYmbcTG/16+IGlUuzFRJ2f7VP4dZAiA/WxkU/XZrqH3EVORTrscjMWfmd4dDBT3HSNH0gdeTDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70761}}, "0.1.1-rc.20": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1-rc.15", "@radix-ui/react-dialog": "0.1.1-rc.19", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e95cbd641bd813c3df1d0a69c4c0ddc6687b4b77", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1-rc.20.tgz", "fileCount": 12, "integrity": "sha512-Q8Y4ZayuWstB72lKUYBiumvRqQ9h1wENLDSn2gTnecC4nIoG2iHsjfzH+7tFmsYB1R3KLnjUvemFCqJSPiO/+A==", "signatures": [{"sig": "MEQCIBY5OCIsDjtLh8y3QfHOYokoMBnCosPEucGuY/kWus9YAiB4LFSEt+GU6M8OPNUxFVNLSclW+MMmeIj8zRkZydvhIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70761}}, "0.1.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "979dc343bc2766b0049e4940a49867a3c4b002f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.1.tgz", "fileCount": 12, "integrity": "sha512-2oiBy38KK/hZZFpAsjUibxjbKv+HXwsGQUcKKXZH5/D+9BPScKcF192r7aAbQer3NNMHuPEOGuGeYKWegUEzTA==", "signatures": [{"sig": "MEYCIQCSnSIVaQNBNdv2cNxXWW3X8sPMpc2hN43pWruJ42TRPgIhAMbG52Myjwfi30CK2kvZwbW1JdqvBckB0rR+0xbGXzIp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70710}}, "0.1.2-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d3b70fba57a4fff6b0fb30ddc7184f7e5ac91e20", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.1.tgz", "fileCount": 12, "integrity": "sha512-6X3KrH/AwH3myP69v2aITiUICXc4wtp6YdeiEWJPm6EvSfcYYLN3nwcj3YYBHJuP5oVEkCsGuXVjmFKYmaQqPA==", "signatures": [{"sig": "MEUCIQCw2TwKXodL8jgNlOnZTtdrvLMEsFYU+ol3++adnRLS5AIgRWi0V81U5ii/+5xnbSnYyotDWy3CP8u4mheMHFsjr/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70785}}, "0.1.2-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "fe466e0ffd50cf9d9adfdd6805e2f3350733ef09", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.2.tgz", "fileCount": 12, "integrity": "sha512-VJWbScYIKFR8o2Qk2mtyedrmSY/9GsbPaQ97BTY/oTPqZQT5QJKbJ8NQOaxO13Ok8l2vTEgVcRJlk9XAeV2rdQ==", "signatures": [{"sig": "MEUCIDzN50jepsC58p8TXSD/EFcmUb5bSmrYGjP5MULd2nVkAiEA9bVoMT2PfM4uwXeHSU/Qb334MQj5kj8V4GjY5YzwhvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9b8458cde5470680cee2b15d1256c7ef3436c556", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.3.tgz", "fileCount": 12, "integrity": "sha512-Y97jIMoPBOZwajhPoslFYggXH61DaDM/wswSEomQURx1F8HSTze97YYF+pyWDtC8TBaKxeG5e/vOfpV2A5gpSQ==", "signatures": [{"sig": "MEQCIB+eK+3OhdCQccqA9LUJUy3n6l6vjCSxWlQf0GDsfHfyAiBK796URyi5un6CZ0pNcwUioICRWOspPt6gWKY/exAZFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e4a52aed93de6f8c1496b0614983ba85fcda1d65", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.4.tgz", "fileCount": 12, "integrity": "sha512-AE7tMMOPgGAJapYKdgip6j15XsF+LY+BRpYaxkrkIWmOWu637PzAqF+lBpW3lWJpGiHz5xR5i9Am6aRUcunMGA==", "signatures": [{"sig": "MEUCIG1HvCIWpF90aLGTLRdNB32fbYEI3cd8JawIMmnLu81RAiEA6eEO+dwGXN83RMfd/9IZf2yjQ0mjAfP0k1pdd8xSHRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "abbcb65b6b69e98740037dfb4346f3f21e6d6b17", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.5.tgz", "fileCount": 12, "integrity": "sha512-JubmfHH3foqJ8MRwWvueHYP7D3wxE9L+zELS0lmEN+B6/xL/sb3IsxxTvm8JyNtorVWFI5NgU0CJpSTPBnushw==", "signatures": [{"sig": "MEUCIEOciM6Glk1qg+WWi8CxJ2IOals/jJtvPxpIaSVcR71FAiEAgcrKXAaD8ouRjrsO4Z6hJWiUsqxVs/mVoO39JxOdTiw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "919fc55a00aa34c05b5da029d06e691535b81b64", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.6.tgz", "fileCount": 12, "integrity": "sha512-ZW4fUo8VcFeVJHKB8AefEjvNq5tKCwOQUTPykZGJuBhrfNphijV5jkgeJKjKX2PlCM7Adr0vog1+OCJZlPfaCw==", "signatures": [{"sig": "MEYCIQDluJe2YRLVzzgoCnjyyVeLxOi+VXkuky5O0VFKyjriUwIhAJiIIH1e0CroxZ8dhqCaDFZHHSTpnoAl2eXwnXadvstQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "818ecf8f36a98e66bd795f29df82b784e778afc3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.7.tgz", "fileCount": 12, "integrity": "sha512-wXrBTxX7sjQLQzlMUO7WSZs+F2ljDGlvVL077wU2aJBx/4/udSwvlQwY4QCE3MF6NySEvRed7SwHsh6IQJMR7A==", "signatures": [{"sig": "MEUCIQDNsWF1jxP1uM767QNjvcTr5+IIayWVb5hIG7hOCgoj6AIgJNvVSmCsO5o3ylods1zzSuWhNBMGxc8wFktWN1Cnc/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768}}, "0.1.2-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e98eee7ee9998ebd33f9d130da631477cb53e329", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.8.tgz", "fileCount": 12, "integrity": "sha512-ufV85fD22mDdU8VRW17MQNY57QYr/Cm0fCg46UOzTh0XAVRVuVgPs7ymdN+7KvduYP9mWrpoLEjTtkHlwfO+Cg==", "signatures": [{"sig": "MEUCIQCAk4kcZmlJEPuZgA9XJruMq8QFg5yvEerVRF2R5YCOmgIgf5igU0otsSWSs31zQlSQBBTX/Nh9s8scP6OsC4w5yeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQLzCRA9TVsSAnZWagAAJU4P/ibh7W+Qr9AcIq6HfJPc\njrp60IBNEUvJYDR1HwP7uNi8cRlN2SsK7iQpWWzxKpCnR86jwwlWHouRCfhp\nW54cjpl79GBihtSbcMBVSeyz+lAIoHg9EhFpO7qVpKrcolZzItJUeeV9JSaQ\nEKggj3j221KB1YgFIssnxUQi1O5ml6VTHW1Q0H4g9HxQ184ylCDRdSEvXNm4\nqe/V/cIzAYtMppEftNKKWMuZGJkDqLDSk5cjUqPw8qKX0gCjgkVYCVXFxh4l\n5IrgvzOgiMt1NBSWJccrAmfL1E9/+e3P4DbfZoAgL2/qyntIlFe3tcxwp/9G\nZrSe6yNL9fZsniExzJHBBJM8cd7hifR4qSd3fV+Fa1iMNf8DfymX4eHh6ACl\nJJzINUxTkimlB1Q6oi49sWkWLK/CtpiYcevnZZz+wvxCp3uf5RxbhruhWqdl\nV+GrI0Zx9c16jn4l4IRL3Q4LUsXs/4DCcCWZbrfTf8GfldJjAAF73MzgnzYz\neV4cRU9KlS3dMVf1FtVpy4OoQbCVqc6nWeRFvmzjUiIlTaozdfD/9oZV+VrY\n2IaJKl2igpsYVM9I3Ket1c03vd3hTqvkAOuDPQwflLRahwNhNB+yhcWrYwAY\nIqAvGCMDwmvmUG8oZcvbb+GHdlbb3SRuubd8pAcj/6xMBGHRpyOzkmo3Ko/G\nWD+G\r\n=2lHG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6a621c9ec2198a2d4aae3d9282c7221318934136", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.9.tgz", "fileCount": 12, "integrity": "sha512-7K3mNKx2SJh95lh1oRtyUPwB4jjP0+ZThpKo8ajaS3atADRf8VdAAsxJDqRR1iGfMdTFkBWHkJQkwpdayMKKow==", "signatures": [{"sig": "MEUCIByNN4yJEO3DZ/lRhy18HsoJpCm0VBWBhAYaMh3teXcHAiEA748RrBq1caROW063RtA1q+NMUqIrrtvnNSJcr2HTk/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQjxCRA9TVsSAnZWagAAizwP/jIUfVH+M5+kNEmhLxxi\nmA5MKq277xVrhf3yfy/ldcBwiVZ9v1tUrGXwRg6k6i4rhaINvcs4TyEfd6MF\nd6jpqA+cssqEjMxTEEcCMU22BjLADUoua7meh/pvPMq4eS4SrK9HF1aF1hdt\n+WMbf97mD9q94ylzDpw4xGG5DY1tJxopFvziSe4byoNd3vFS9AIbDOWTCLBN\nmC4UM6uPvxbi0OmqlY8xQh5fXvg3E+PCbMd4zN+Nj9Xwm7vHv1qjdeAM9959\nbn+7e4ojr2Qc6SFdEc40OUb2NDuDgko59IfjQQA6tED2c8zOWHVPMq2AQcFY\nl9FPLyCTTICnQUpmmIgas6JHMK8otUvvphflr3p0wjOCZvBLbls11mC6/b5U\nMVbVQD6JWoK38SP5NH5AYrbf8KkUL4LLRxyyTc28hW2IR9iG6OMyaLldzTGb\ni3FdIyJvCNG0N3Jp/p8ojIICqkB19pnQ8AcmBDi+j0XsJ/1ufv3kxAbfdjjF\nXr9nUs06iQUeQNbYrNPfMyr4Ywc5aVPOZ53RjECDhdlVHEv3BZlTgaUTBYLl\nm2JnLwfCHgoOGnyFvjnQFuIPgvi/RqFq6Tkv8DfsHCw9m+RMAJZVsSedEwk7\nAOi5JeZXjh3VTmTgF3JVkhxDBcBon3ObUzNCgh10J4RZxjLtF0EP2dZ/L/oX\nV8cU\r\n=2eIU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "bdc7f423f7527bd96d07918e7aeaebcadff42e06", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.10.tgz", "fileCount": 12, "integrity": "sha512-1oRYqg0nx9LjRn086fumRN2KjzJSsGKySBWxJVH23NkFcRz7PaWcLvqkToc2MtX6Mg7sn0lroqo5bhe4RWoycA==", "signatures": [{"sig": "MEQCIDpZ/r2Xux8cl+s3dsRRIZL4hXG0H2ibQg6siXp27YfiAiBv+r9dfjJWSqj7WYhSh8hTgb1NIsNShf92d0USQL7vbg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75769, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljGiCRA9TVsSAnZWagAAaWUP/irgskvko7IiZGbaydJk\n/dJ7pdx8yBJyratohjPb1309h6C4EqSEESSt9K05SG9qzt/lPxj8BH0TOLAu\nhD9GnmWOMyzn+mWdgjHF9LIt/IOoAUaQ1o2WbBIMhZYG1h98sK1zkfc6+E7n\nHgYOSPEx69obChkDKccnAV4fTob0n7Rt7ZbHkNP5KpRiWgzoFqEGcr02Oy43\nHMHrOzaQje7/APcfoMKc4n/dpHfUN5pG9frtPzhfCUh74hSdLYYgV7vzeWmU\nIIHwolOAYXQlW+PiY4E+u7WcWLrSGzQnuc6x2ao1nR9eoTZYw3dFEJKB21hU\n4+HDN4DMRAAukoAn8hqRyc/dnG8QjaCsySniW86sd4PklX9RY0hwmALs2548\nhDD3T8CBaqNpGZsmeDPZfjlCNxTJpsO9vZstDK77k+9V+eLIvKKHKvUe9P6+\nLT0cRT0I//vNoa7QkI1jsLzlDQEpE27v6zWgTE0UPaTN+bD/3pzvvuIfWFQr\nId6OzeiW6WjMOeBrqfv8FJdb5Q58XDbwO+0ZfltbgxQVd1Pls3JeW4s5ORUx\nvQgUTapTD1rRmMhSAtBvQ7U7Z1IvrBGjOKzFTxkSBl5AXv8QdIBWWPAvijfd\nH/tNhVB1ybpAGUpVHtD4rPZI2CmZngs3qW4o35F/d+RtkK6L/HxptbQv8DjG\nyUKm\r\n=SOJX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1e4c6ee5725503dc8dbd49d80df8e55944f8c4ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.11.tgz", "fileCount": 12, "integrity": "sha512-/wU/7MyMkJh/lFEPyQwmHXBt8DUpZspk99bTqIXnstSWltZYB8Clq/PqiMhtVCMeBE0b6+O78NjjYQoLBEx0Aw==", "signatures": [{"sig": "MEYCIQCLr8nYVTG/+ppKW9FOzgctSpwKI+xzKLwmOuIvIrFsaQIhANjG0EaPOapBmeJCZxVJkcPBa/M9+sJAJQsn8S1BgCce", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmbKCRA9TVsSAnZWagAAvgYP/0wSdBYY2m9DTUZuJC8X\nL+9y8YYqd2VGxpU/jfeDmLHrxXrbn5jailR4APdBVpF4+ZTf2rnSryEwW9PX\naMKp8pkKV9VJdnIjB6/oRJr6CpbGXmpZcUOiRFF521lKaxV/H18mP1VeNe6Q\nraWKOkXvExD0lTN1JYABzCRBLqI0oJXgNFqdKzZ0S6lotQtqEmc/mijL1pYM\nhZ2xEXLXPUrKEfWhIPaDzey5BBF8B4SveAtGsPaWNaq6ntTSV/JuYLAMQTR8\noyg6oc6JcaXhcBCIBszb2iqlHxDGwWEf/xiDNMZ60sNakDYQ9hWJ5slRWvP+\nYT3Mp6xxhLW/ozJuy+jdmitDNxSiqwNx8SwlKKXDbuuzKSoKoLUCqcd7UPvK\nS1l42JHQt4H1bzkXOEaZnWC8KVrZpYwYieg+6+bdUcDZBlfKi5HGFUJAF5QP\nTwBs+eAgnLZ5LgL0YBZnNIOvVkbwwAYyvZm1GNb8cHkIaOb1dispz/GJJIIe\najAWSkp1gAExm7q9ObTY9zw4ChAPLj9Qhg7gwXHjxYWTyh1xepiZ7y+lHnd9\nCTiBvaIQP4cwFgDJajB5NllHpKvQ5idMJ7D1p6bF9tNUEoulTALw0gdElYmS\nPur8KbkKbWWLwiFXh2ADtJtYKuIWwHfboM07/Wcul/658GrNstefa/kKBwUK\nzcUN\r\n=AeLt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "91b3245889d1753952c5b79302e278e2f5ab333d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.12.tgz", "fileCount": 12, "integrity": "sha512-+LzkIajDhBoUz7iiK2BAK330+D8ASb1fIrjeyG3diN+C/T66bywO/KZCGzsWmMjUo8k7ALj/dHQq5qxLhHK+hA==", "signatures": [{"sig": "MEUCIGVoYgJlC0wAlcWhyqF/0mKT6iRph4i/gQyj8kB0FEc8AiEApNszeH3FjYNYpNBBELNOsITqpB66lAUkxqS1jkUqMbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5mmCRA9TVsSAnZWagAAgeEP+wax+Xy4NgcPPe2gh28C\n7NCJEooORxyMaj6UENvsYO8EKuq95Nx9D5qeRUCo5HZ8IpIr8Js7+JeqiI5X\nUIjRqaXKtkWAHmi8Bu3d6gtBH7FOugB3hIdhIMhcEr/k0w4yW2ER1BEBewaq\nbcTKbHXlmuhZiLgi/8t2m1EU/Ts5wa7oIY5W5UE8zqBJ8lpVUHYu1u9zL1nF\nBBRzM5eb40LTZxgRAcRsOkwLVTgnNPOUYK6yUGidmBPo8gC2UEt2fpgMZtf5\nP0nelImj/uDssCb/108bOVGUMrR3pJxGNJ0602Y6KWANmsQkborIjM3qsMTR\nIbGTvBCHbHicZQt41bmvClQDJZdBTWIAbzQ1hSPDE/jGDM4QSIjdKZMLcYA8\nT5DRe7gzzGA6TuagQ1ryPPnhDCCZjUa4J61RUP1QA3VzApFFTcrEugGUEEC8\nh5UHKEIgv+YvC4I5m+SYerffMZiWIVljjhNBx7vZUh1jbTBq9SYpjYsYZiIr\ntI7jj2kDG45tuq71OanNx0ry1Ubqtgw9IQ0qoq9Zv9dnXImFTcyIWI/GJ6iu\n1B+JvhkxP9dNCcaCuD+ZiOj5G8LSP597ZK5Ns6+eIq5klQVpt0Tm6bhHRk1X\ntQRUHxArvQ8/lo3JJGCWndbqAv+VBfdBGW8LIcfhStBujWvPFq8DZJZ4kl74\natDz\r\n=YbkA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "546296dbcb5ed48b2c41730c0c6f7332b37be34c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.13.tgz", "fileCount": 12, "integrity": "sha512-tSVa6ng2L1DcPC/aU4kYr+PjgasR40TP8l5zuZX0TjAQtJbNPAJIT2Z0y3E7pd/beYG5hgC+954VMyFbWzjVYg==", "signatures": [{"sig": "MEQCIECig+LmrPaaotvYsWVHXAnPIyPteo3AQhu6NTBKPMsGAiB1/DgBmzxOaEefRWG8EbvsF3VLe8etCxGX4F2/buG6Xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN4PCRA9TVsSAnZWagAAD4kQAKGBVIlrq9duyydtodVq\naRHhEbYGHTRq6gZRhUQTk8CDSOeMv6bEbz0yLYgOY76r1xhQoNFc0CbwqRtm\nuapACU/XtT50EjLJ5dldFnHaEc2RUCWBWeD0H5ccRWR++eb/6kst8DQxrTxF\nW/2fNtnUzNxKo1AlAIqWC5rG6Pniqr6AWsr60/DER9zMvRa/lwpLiCftHZ+m\nXBvWKb+qPPEkI3+KoZh+d+Wl9LI/yYkpXVF+RgGrX3J/5nZZFKudL/Weff0W\nLUc2xrvdw+a9c8lF8YrVW9IBGNgd1MbTyxbchWOBGCiqdAhTC6gbpAewu5zf\ntmSH8Yz63c/poyMwY8HpywIgil+8YxqOMH0Ce3LcunQhphZOgI2+DRAT9FK7\nkMXhNPUJALivqtN6yuZqDmUSnwftyDpDi2Ojag1QdELVqmQcBvyRtZ0J5Jv0\nrNz1Poc4/gLciwSyYi/18d+CgO8lzL4KdZWeoZXTrfdZNhZzO2CkDpjcHo41\njBthwuCcjHXQRTtRdCWhE4+ejWaE5qA/Maxur5JoLM6wpuZs7AWSMsFDmowi\ns0avybGFZ7i+MhKzpkLM5a5FY0acrOzq9s3CZiMn4i6IV/YOPJv/arjT8rVR\nDmWfRCZgVv1WE9i16D5JZagipTOG5LXhlPePsrHOdxX8praEspprknXLfEaN\nYdOH\r\n=/gB6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.1", "@radix-ui/react-dialog": "0.1.2-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6416e600d2db610c5a7420713abeff2b756759ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.14.tgz", "fileCount": 12, "integrity": "sha512-S5jQO4EFdPGSYqO0tUI6SBsO5I9dUbqaW/6tujyk8NaUi8lDW3IRE2rogV4zqHY8CkokfFozwdY1At/uur0lwg==", "signatures": [{"sig": "MEUCID9Eo0wXHC1yrCvhVrTGN+CAA3HAbLA5MmVtWG3YVPKIAiEAjyutCd6AQOaFHogLGtest+aSw/uR/EVLkL7ksxxfqjc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoTDCRA9TVsSAnZWagAAE8AP/0lTLvilMjmj0vZfHhaf\nPhg2Fc6RN5fp16BCctf4BCZXh0DfqWjqeWdtu6p/y01NZhqHMoeZVrMA/1ds\nVcVSxDUfONiCUPb2lFkayTMPyLPBn6CEG1mBvEKSAKVUwHk/zcFeI0qVYKcK\n79JiFApPlMO0/l6sVMQrLwzAInVmYChEgsyWcsvYgTM94O0C82OfxkiY5nfP\nnLuCXCFCvZ2/RqCByi3ScicT+kJJOI9Yepldlwqzb50WUyT2Wogw3yO2yiGs\nYH631HvQ2O5ra6HgAI4I8S7kHuhhzGrXicZt93chxtbNioSb1kbhh7UdnoGv\nprC2qpRrS5zqnHmUJ4NhuR/pBDKqb30zugSspmN7nWTeQ1vUsmnTyQ91lckD\nbCqsmMKTC0t3yocPAuCm5BNrVTGgkM8V8ckKx9DBAWWr93cRCpolyDr5LtDB\neWgxb1fy41vuv54lI+oa0X/uOPRc6PmTnbpcap5F1tYzAB+5MYhR01tWwsts\n8vjU+xT30mWTgmejIDVBmaT2yYWf4uAww5E6BiAU/h1JVBheFbSaMpT94ylp\n+dnzz60mFOQTV/dx7d0FjpVjEiCdmod+uoufPxMsh5yvrbE8a01Cj4ukhx7a\neuKWuTy/m/4+aXmnZaRUQQU1jnTZvtPsTV1tBbiaUJ4U2mwBpzBuKeauxX+A\nYwNG\r\n=Z/ka\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2-rc.1", "@radix-ui/react-dialog": "0.1.2-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0fc397491abbf2986d9e36ce7b8038eb7cc4f07c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.15.tgz", "fileCount": 12, "integrity": "sha512-cmRBC1diLJnU9o2ZemjcBfeBMd27YWYofGGLBGrh3NDr/2N6j1EZTBHMmply2CyL2HHB8GO3fMNFUO3uDhMbxg==", "signatures": [{"sig": "MEUCIQCq+G0WBLZIyR4AeJMGzTmjq9yZpqZTILoJkmbrWuMv5QIgC0537Bd2+X7oP2bJDo9Tu3/jS5E5LvN3SAbxNhFDB/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/RCRA9TVsSAnZWagAAYdgQAKKiLiOCR0u7DXb7KiGa\nvtjSf0SUqS3n3324tZmYI0wleWStDAVyGXo3EkirXjMRVCx8qW0cm8OnAazN\nk0CRH4KZTLb1/trnkvO2TrxASsafRkUH71Pr71bjDs6M+OsC7bXm1UDoNmTf\ngShuRKJcUXTmvecmqMgY8TLblSD2gCSdWNYfdQuJeEo0YHLFTyf3VKO3TC2H\n3OL5kBmzIIJSST9nxDtrWlsF5YvVERJiWirlB1kq1GNDe7ENigaRR9mbzCvI\ntOauAUgmr7qs0W0cpmQnULlRweuBb0yLMhfniRBzcTCq3eFRwY2rcHAcm+TC\nytaq5wn3rDUzbfacheRpaIWVzp0UKTr5RrUwrnv7MYLOL3NGNE7B1SgR2Oc/\n1/Dadqb11fIvkvsUMwpdb2t3fyjAfN3CfdzAVkZmV1iGg8NLCdcn5sporda3\ntcGyI08tRqND9FGM+JDhqezlOySfGBtHu2kKBef6VVY/K734t5WfuzaVINEI\nf0OBGOf3IMnDUIA5b/4kXwHiaWsRoQsIwxbV7ROCGOeTxYL+YiSJgUOWFIcM\nBoBGdinr2x3fwvh4ZIFSHIFVsFl1srzzhuL2Jy7p0u98PIzHaZrs82tmC9EY\nnYhVfr0QZb+KKb1patHr4mkjhPuarObZmiBitMHTbGgH56Z68LWu6yWrBxT9\nXzSE\r\n=Wwwe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2-rc.2", "@radix-ui/react-dialog": "0.1.2-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "10dcd883492c1145e50dca8cabe51f398673af6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.16.tgz", "fileCount": 12, "integrity": "sha512-ddzW+2kiGJrzvU0/LnC8o6i313ME0gcHICFsfJB1PW2jOfcmZrs9td31R7PEYD5gLLPAf9G7/WWp6Lu3ivZCWw==", "signatures": [{"sig": "MEYCIQDrcuApppkXu4mqcMigKMAMoBQNSiUb/t1PD1sDE5uF8QIhAI6eGJnrAf0gG4NesNNA1N8I8lBlIy4O99tYWJRcGfox", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiMtCRA9TVsSAnZWagAAdL8P/Rx5GljJ5IzliT3NV0Rg\n/eEg4GuaNr/XsMmS7oBvT7QtDG0ZbAklbJJ6LXtrkc5oGLasGaDB6Rym4yco\nz1z52TolsXZ+R3KJULpyzEc4mmDS5/qU33km0ouCjqC5Kss2VeRUQ5HgNcYp\ncwVOk9DvVKwsehzAy15ekHiwXgbtjOaEu08TT95oVGgnSmH/cpFFa77pEjd1\n40g2qlbqOKXQ+VioBhEbNcvZCPJoxD3qpsH7iy9aBjiKHIE1BL559HjRDb2m\ndKWK+44ioG9sWgE2qBzZg+IZa5gEBIYZApfIdmeULAASeMfU83dWRZYtFX3s\nOeUJ6spzbUs2pWwV+Yd4IhhBRrjSfyQ2Ffh1EnaRPXWgptDBZU17bbhYgUJA\nI6Ihmu3bEhtPZT6jbYRS/X+LW166/YHgRr5siA3Nk7Xa6nVWZvDd5fBnr9sn\nVjxjvd7tWbPNaKDj+dLZydE/L9NJ5piDLwRvnfmEST0XyFX4m9xFoVpeNCDQ\nD0hf1ZFTkEaWZR2TsvI2l9wBPSamkUx+dsv8tPpyLUGoihaimEoptB44ZSE1\nL7ob9SRGwnFKRlL122DB40R1Dvk+TT1bUGRixcRD+FQY3Z9GLzUw/44BtpK0\nynW6vbY66hRoTIJh8G3NiXrcrBhbU4w6uOQWYDgVCMkbmpo96mzd25xHXL3v\nImug\r\n=FTcg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2-rc.3", "@radix-ui/react-dialog": "0.1.2-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d06f55f30bca9c27b78ae0e2b9ef72178432da94", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.17.tgz", "fileCount": 12, "integrity": "sha512-TB6mG7z4hNGzm+IvatTrgzB6tBoCt9Gj3DPRG7Ben+WDPvZT4ZbMBvXhhZe+m7TUCQr8fpcrggllGaBdKJV0JQ==", "signatures": [{"sig": "MEQCIA/QC/h3Hh+ITPlzx0WYLSzVRe3a5S31P1THJt7rYBVqAiBZDJRzhlB0/VYYg6fT+kxzNKaTIciic6VYTdYgkhN21Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryh9CRA9TVsSAnZWagAAfIQQAIidfF2X0NY2Ys9qeFNC\na4Pv6wT4ClGvr9nZrePB0yTLFYwlUPfxmAUEI0tUvUtrp3YowDmGIRXSgp7q\npaDTZgN5vTQwMXEKJfaLqkpytCCXNzMR/jxdmgg1c7n7/dYsYjVqioyJsOhK\n5KPiAO98NO1+Ml4/RkvNON1A2WJfVxtzwvA8/1jMt4kPod9yivtVdBoJAMWU\ny+boec1q3aJwlQxv6RqJrGTV0H52rBB3nh3o5awWRyv2j9MjPAC4AxWralfm\nnvhcekVp26GRQmRf0cmqOG7hnqupcEyP9T97qwnuBbjcI9NBe3FJR4DSKUuW\ntLrrLRWdi2TOimyJtEr6WSyyMMlQM040/Ps04hXbm3C9ZSN0lRVzN3iu02+v\nXcg/9K45hYZ5t3NBrFsTI08dSfN153zgcn4Jhwo6EeXZXFCChXadbbfYR1vJ\noW6N1Gwl3s0km7JGSPqTk40Z2mAMpQjgXvFsExmv2QzJOG7qvkMvyClKJcK7\na5oXc56eFx57/UJW0wQJdlopHRrszG0g+sTtUYLtrAolkOVBwYTlTWUaVXRs\n8AeNqEt98j+3fJ9eXM6rpSlHlDfpv3hoAWDI6wikOrujE7WfP7KrIKGB9Y7i\nYqRWLivd9mu0/vQKB4n/y3a1ze356octFg4oSwsL9NDyOigqzSJi/binj114\n59h7\r\n=bFGD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2-rc.4", "@radix-ui/react-dialog": "0.1.2-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3ad6f4a70745fbd93e0fe4bc1f07d35e5d07ecd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.18.tgz", "fileCount": 12, "integrity": "sha512-qkEsVXPj7QD93sA7G8Pg5SclhccHANW8LtbCD+Do6pd/jmi/NRWMhjRG9rGKlBVcRHPOnWLfQibpB/oAbiPNKA==", "signatures": [{"sig": "MEUCIBS2RRTEBEyOBPh0TqN78jgP5rlivs8JHZuDB561/MBYAiEAoj1LrR+z3ufSo7KMJ4rRcMWQR5HBrJpvhK1J+RvEkug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzP6CRA9TVsSAnZWagAAEpYQAJdpy+4PFgmEQopr6uSx\nhcfJw31M/lG/bEuwMCF/GINI/0A5gLaARs+RoyA3JcCxObolO4PVzrXLB0py\nu6NH2Xevm/dMlNYnmk3diYEWwZsLIkDBR06aO0Ft8m4rnTOPyTi0LWGskJJQ\nLDQV4xhJlp1AZXvQJqsJX09ZOMgE9U8OHvOyByY43FyCuPQoomzLk2q8L/0W\n3F/3+F8VTZJZlfUqCoExPq2+FZ8oIIcBAyDETuTN3R6E1KeLiEiCr3mzLM41\nkVzP4KvydNU5YTF9d0/OatrkG4EAZUgczaLJNmmD0WsCjgBc2wrN3CfInUha\nwsPTSmPg+7KtdXnmRZm5uCt2g1hTiV2WtsUUe9W1b6K/ymVuvOyW2ZTdpXsB\npXcZIkZQzAdUyqxs6M2E/BKdZrc9XMoYn2OwhavKLqyvun9Pyf7SBLrEZwP6\nbclqVeYzlFJttF1VXojezEHGEdj7rvOw3XqaRy32lVdU0xAWa1jRj2hXuXYa\nsyQcfqMlMbWpg78vJFmNV/VMA9RPS9KLIhFWt8rFR5tdyYme7K/vx4b3r7Om\nt6PVRoLDErwToUOgDoJ0W8/pMdm5kfGsqDrXPmpnk8ljmAO7lV7tWZM3hz06\nRKAWFa6YNghSjiLEGNnXuuJXZyT6yqPGNcxaKEZIw51n2VKCs/uPQkgnx0VI\nLiDH\r\n=O11m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.19": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2-rc.5", "@radix-ui/react-dialog": "0.1.2-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d0bcb61180ea747ff50a4b040c51a043fe622e1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2-rc.19.tgz", "fileCount": 12, "integrity": "sha512-PKvAMNu2GPMMxXLCrWPGT8LqtL35P8Ft9jIYrIZRyq7wawslVRxO525dZF66uqVA0JboYZlIAKrx3EhFWsym3A==", "signatures": [{"sig": "MEUCIQC9C9XKkjnoMub1YA8+hWYcIhLZKYAEPXD9Qp7kRpKq5AIgRwtMoLmwmy9gg8PiZG7QzChXD5CV0orLDMgonDieC1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41XCRA9TVsSAnZWagAAArcP/2+DIRlcgngeXZ5CjTr+\nlYfqobRb3LPVlse7GZRTKrxIQAdiohRTXm9P91dqcEnSE7QEbKzplDy5w5wF\n0yVfn35lkioamn08eojywKO6FbdYeDjFeZ6VqF/yQalgK//0pM1HNpAhcsLL\n789MELg61xD4ajCNhlh88C7Sv37Oxl1krlMpEUMLp2hZJatNBlyz84cYdNtV\njYs5ve8LLrAR0ExWUawDVh/SMLXJwDEPQrtCpEODOqm71Fy5uD6L3m4+ieYx\nPvJ5YE0watFXjNOQaDIDxoMswGUJYmcVdcren8Glmr/mvqdMkpaph6wO6ks5\nejR9vG4yP+viBj9v3by4kZYKlJuysu9IP2z4OC9kDTN0Pm9ufLWaQ/4Q8+iT\nKUowknF3kB8rMBu1FnMJO7s4Ygw+6UysjUB5QRR8NBhAL72OnmsaueFO3u2R\nYKVEXET1TsBt58OMrg2DEOt836VRfB9br5DOUNh3eiTZK0REI575xGUjkoo3\nRJzIOUUmJkckE3RrTeAs4VyZAnU1U3yQRozErj5zCKa+PlqP75EHbIOa7lbq\nzwdj/VTYZZjGKPauFf/aIUHbCuq/3zcSciAXeaF6DxVNHCp5oZkHJa/v4G4s\npsKzd4rnNgZagO5GLJcggvwVX1FQ7nxnqhC2WIKi+krqVQVYg+3+oMruuGic\nqi2m\r\n=f2Th\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7e8a4451c81ce6a5d729ccfced7142dc5a4e2190", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.2.tgz", "fileCount": 12, "integrity": "sha512-zW8sWLAMbn/lQ06l1Si9zxyZZQpiZd4PFa243ln/hYKdBetGjrmuP8o9GopbZF5AtETLOjc1Ytchu4W5YBmRtA==", "signatures": [{"sig": "MEUCIQDTWMCuzuJk8AGLpl/VfMSdWZYiPzCwUg07K9qOYGG25wIgOD6gb6lkwBJIsUYfVCbzS98xoMGCEu9Ei5mDUTQ4QxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDkCRA9TVsSAnZWagAAwFUP/jjRr/kvbwibj5Xtwl6s\nKfDncJeg4JiXGY4p2VuSsHsRNPkX63gM7vdPHTKVe8kLi5/qmGmJE0yWi/n2\nnE4T+QscrVM77s3E3JfHlXt3aEyDmMRO7+iPaJwJG3lPgEAK9vZHwbvqBShG\n+snsN7CpLx6vVmQvv7Aw5/gPin+pyNfrcvMWfdGsQvDH0cDb6Ptp2TKQGRtV\nlHjnqNIa+80nnlvQmBA/Ug5yjb3XrTz012ueE/xqRrXC9kmQNvidopDXdLkR\ns+v+X7/PIe0RN75Ao4j5PHJWre2eCzkFrvTqBqxGWnxIRNsphunHtUG5k/dP\nlqqG0ce/Ix1HmXqr+EpETrAnga3SUhwmEMzjHdUIfwDjqpPD5xiaSjFq4Li6\nL7ZONUDEylcTfRP4+2NUlfQ6dksqAtpsZ7RTLd2MweNZlK5lKoSSRixnuDnU\nrKJk1H6SQowUn7pPHJBxMO6S8EJWWOmuZov+g+H6usp/bLRua+yWFBRHMUh0\ncTNmchqfJ5grYUCNZd7IxxsFYJKV3X5dgrheGFkluYDZncTDDP4YMXEmnKf8\nNrnFnk2uavwerBc5Yz0Mm5sM5uukzw4O6uhYCONbHBs08zDs1xDwUMq1AIZL\nn156GfEDkBHKcz/0vtj/JsTeE/xhVeX5pf6CPH7rdS84t2kyTAUA84bX74R+\nw/RV\r\n=X5H3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "f5ae8a9772a537372b039805832d50180dddddde", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.3-rc.1.tgz", "fileCount": 12, "integrity": "sha512-qFyHST7brpLWApL0MUVeq9fgrx4dWWEKK8+UKCEYx4B/28QkiNmQv/DXGXoQApEVPOy+kovp21gF7kEV75rnzg==", "signatures": [{"sig": "MEUCIQCzRE5VpqYwJ3O+zTXisnzR1H+3/mseZKKdyXnLB6pAJwIgX+K8JwvzV1NFI1Kgvjg8U7w2JnwDCSF4oT82Q6hyLEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsy/cCRA9TVsSAnZWagAAVXgP/1ciw/CtrM4adtolFVaN\n6UhGOUp4OFF+b0p62YKWosWw2a0MFExQfu+MS6z0SzIm9p1Hz7Wrcly4JzWb\nskL3uGG0GtK+jfzjh9DoQMSFlX0PSgot51RGQuIEIgzgHZqPWLrrHKwISWOd\nkyZs2mHcoTRlPshTgep/+J7/EpTpa/HHTyXFXscE/YS8u6n1fxAbOV5SUcee\n19cJ70ORKh8cCDkcIchfkHWm4+iKxWyTW6PpKF/eOW2c3zTsTbI6MPsa<PERSON>zo\nYqGKPp+Jp7XTl6Bp5/Bi5tTBah8BNFuEevUTX0/uKZIPwCgWDQ5/n8H3y8Yl\nEEuHxUdwBWIFVMfwR7RBjdkLiq1keOnOywyfwsD9qLZiLL5X6HsFTeJKequT\nxgLEVXvI7PDfrVxsoQkTLHPTTuWE6STSvxp4X50OPdCuwznHRBljw5Nt3E8p\n5oraG8tQ7R5WD7kOrgKATCtxQQBShavN2GYRawGfAbn2qvnE3doXrSL+YYzF\n/vUx/tuHteWGDnWhtAwdPZBMSU26FaJSErIPB9FERd2rnHecSxjQ5U8V+SCM\neLEND6297Qx+Cj4eK19haV6vTjbeQ82LT8ywEhyWIK+p80tFta8ZTVETtgFN\nCl54Ai0S2d9UZhGGjBlN9M/ENiXhVGE/lhyzJmaPU9hsRHTWR7W5PxiGVF4w\nEd27\r\n=Lh8n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4b7600e2b51871c233a4056267d2ce5a12d750e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.3.tgz", "fileCount": 12, "integrity": "sha512-Dt<PERSON>lerCid6f7Svu8+6s9MaPs6a1n8ikT/3WNPfXpeP95oeGwXSZZTc9Pwir65/BrmTXngrpFzc7qjNBILknlnQ==", "signatures": [{"sig": "MEQCIC7r4DlXTHR+Wbarir11IkMSAKSu1gM50/zcoEj1tUEUAiAHaHJ4JBOO7KvT6z3kBLkO1h+Shf5i6cLtQdrLmaZ+4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszruCRA9TVsSAnZWagAAlVMP+QBcQIxDh6iLgswbgodu\nQcGVu2wvYW3JGR/sgSvV3dW46vYiCTitmmdRfT3ghu537lRFsONcA0ScoEfr\nVBn2hTnqN+fPRwnKZNF5k0ZHUCofPaohE8UT2NQ1ce82chTf6ScpmFkYrVaE\nXpTevyuODcIrJ7iDvHgWS/1Zrxrl3TyAVy2CbwYNJutlfZVCqkNySuv4sVWl\nx+3RxgJqgTxCnAdfCmVtX6BgaEh6ROofUMOrdGH2hzn9OYCFUrWIWGt3pSWe\nDiJybUKHj7ETBF03mPDUCS3z9mPzbWXHFdoBpvdlTWVjIBU+l/9oMjDqumqQ\nFUIiXjanjsAwISRJHHcZ4TRGtgWmNZyfG1AlVjO8EW2yjtMWYr4P6vaZwTg6\nu54EE4653csKy4vFgBR0yP1F2W/Acxz7fOQK/tHbluLovw8xcMPAzaGHbdzw\nLbbR2r6iFEo1ABl4UzcA5JcTmYN5t2denMEJqBeuhR+LpZEQCmYzUMJ6vw7A\nrHQv7/qhU/WURqLrS/fHITeI2KX2c4gubv/fZX8wYq5D3HuJc2wNXey1DB9i\nk6AXeMZ03HraiOpyMhetyEMPj1O2iPpAB304dKzDWgrxB04MPwbUKjnPo5m3\nR3X/v3youlU8MiUmioT5f2bUhZhmZxtsAtuSJHycE9bpY74IGZj07t2Cf/gv\nZ4df\r\n=NxKE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "570c56d5f8abcec4bfe0b66c35c2cc879c3359ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.4-rc.1.tgz", "fileCount": 12, "integrity": "sha512-XWjEPDn58fBh62YbMSjJIFiMjX26f4NYlN5/po8lWeX/kjlK2VzAU4IWgteEw4SjTQCvI+pyLInnpUa/QMyJwQ==", "signatures": [{"sig": "MEUCIQDKVCIDeTUQH0BF2JFAFk9XAtdqV+azVW0ZqEODSoLmqgIgN/GkMIKAfVZsPzzN2t27tMQAnkne+yODU9tjYsemhAM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs8pLCRA9TVsSAnZWagAAZN4P/2o/jnhpDVCzhZuN9pnk\now52yUdvmHEfPglUbxzpnaJxbdRKfE7uofwVk5/selomyw7TSf7ntGcEHxSb\njSkrzB2ath9g75Oo6IDxlQod7zQp4/qUhbHGU9j8W9H0h+Irm8GE7ZileEe/\nkYdM/LqKqkYTWm1vviOcTjJ90+D8kxSzFab6Ae8LlulYNJ0PM43orpznXtr+\nT27arDJo/fso8GxXPuTYrrEp26VH7KF8d89m0yYdvy8rcJpn7Ppz4PCSuy8R\n7JrP7Qo/1IpFXPs4FzCar5gXW9fUBZxYsmt7h+S+u42Z48pfJgow1EQYIn4d\nEajILNMNsGlk3rj2yw6nsAlP4HlRDXA7GT4Sf3ymgPipcRlQ/xujQS2+W5LK\nsk0yj+DgJKDe3bGJAnnbmUZwMIs/0hPy181K6Jj9On3G3EU+tEes4wy2P7KM\n5mKbMxYUIEQI1dcNHq+oJoQsm5jlwmHNmEmj/cYr4905vfI1ymlZCS6N2RTJ\nOYTUm0GoVZkvvyKc7xM9rWvmw9qFPuGULk8jFDyf7kwdnIoeFNq6JmLSmi7Z\nT0Z6NFa8O8e/p5EYpPYOt3wkxxFp3r9G/nFU19YSTtU7wvWYr0vs9Zk0vzAU\nIHYkrZT6y4fHNBUvhKSH4iddWaCoI4kFrQWz4GGcAsrVsYm31BBOuK0eh+oL\nXe4b\r\n=+7wo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "a16a387a2ae9eda2c5a1392e36a36af3125f3328", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.4.tgz", "fileCount": 12, "integrity": "sha512-PaEK93zqBTtW5rdTDeOzKNQRdoY+WHJSe6oPvYIXp6wzL9Ar3BeGml8XbKv1XT8azpWpardbaRGbAMl6uy1Clg==", "signatures": [{"sig": "MEUCIQCoHSeqxiJxRRwMsxrP/OGwbKKMQsWT0fhT7GkU2Bq2DwIgCdDIfGkFbBXRXsZQQ1DUd2ynkin13q7qHAmR12+Eje0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht4aGCRA9TVsSAnZWagAAU7oP/j+rbq82AQiYGq1aaPQ0\n5N7ibNOBovebcJ7eIH2riJkAbA+pKh77kbBUP5eGfwNou1h3DQdTm2J2HtK/\n53ffi2tFNmtdh/C5y8+3/LJIP82kc3t/NJUl541n5/UyGunsb1vjzvQwdgR1\n4s/FYlg3xQKJ209ImRD7eQFEtfqXMCnrg4jqvVvg+lHdNY6daGPOqXUagoXW\n39JLeMFjFo/YXVz7bJ/Zfj11KGUQK5M34cUdPW+QHr5P++jWBBB2X5oSnKz5\nfhPbLpnp4gjxODiGxjhSPrPStCZsVBzcNMTpO7LxLK/LZLPLGvejYnHp744S\nG8graGXx+QmctVuH3qrJMxy382OvvFeFLE9vZ5bFqWyxLlINLRLvT53RXonr\npFSOpJrl96bSaFINw7IBMqMqXOGWbX4MJ0zgTc7oue9scYYvEAzt741SkEXv\nwsWekIdZHIhC/127UMEQSF3U8acIx1vgFh9URZ7Z88P0XEcR8R3APMkU8PKN\no1JFfYXyMBV2gI5JzNBBOSf+q6tUXHbMqwJou8gVmCiNeFXAXLXuzKgCwzN+\nPIjn3VQN/u3ZakfsVbfRRr9C1FFSZuOdnuWk3aj2Tmb4s520zeZgWIsfS619\not2FwZzomVC/Gkdt2QnxpOTjIF32pdVoj3tUkH81APd3isH/iRXIE3boIcIv\njfsB\r\n=jCe5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.5-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ebb038903562f88b1020723588a871d55baedbc0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.5-rc.1.tgz", "fileCount": 12, "integrity": "sha512-Hf8XxO0MmCHp9EFuDelcMXZyjgicb2R8UG6f0B8gKpMlSLSmWaAMIJP1eJamrNAMGLg+BUcTj0bKbkpJgSckIg==", "signatures": [{"sig": "MEYCIQCOthcv7dgXhkd0ST3f7wi/6Zo3T07OUJzBrvtAjahZywIhANx5SMGDl6f03ALL3o0uy+cRf1iuZS7AjnHhCxHsEaGj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiOCRA9TVsSAnZWagAAEgMP/j0aLLC0efj3Fgv16LFQ\nVl0O2GaDaIFPMKC+UcWNqLQBbQ1qEECUS8pk0W8BerOfp/Pijt8Biuoa5JNA\nF/GdErCbDY4apTbL9Bhq7T6FEJKQGz5amxdlSZeKePZQuV4NoP75Y+GoJ8If\nHDxWdZu1jF8NAUnDqI8vbADWeSSGfOnQfeteUN3m6Aabgia4oNrzFbZImlPb\nNtI44YYEohXYBTlsI5JuXFSnC97Z+wMmyPLaqP6WZXpcgOfbn2Bx/Y8lUvyq\n3TpiM3HGStk8LG+X7CJDGo/06GRDTARyuZLZStMeqSYL1NwFPHDo5mFajAFe\nT4YfLwZtGREAZH033QpipI5p6NbFh9K35FCmKt1u6dyf2puFA4o1zQJxjSc0\n2VD5YjIasdp50NrAVPTif7WMooBoqLzeipaB9e4lXNhI93K8XiBuUE6+kZcK\nBFLxN5JYC4mNBbbM/CHMMT7DfNCYPZb/MFZciUGN2sEmwNoRdhfD2apSdtyR\noJoadPg2M7ACkdmdHy56Nfl2vyv+Rh8Z57FlVR3p2bwCS1BmHcUIluRUna0t\nYQuJGg+wZg675BbnfXghVpDwdZKPswr6yb4Tp8j+eDV4sWaXEMwBhvFsOxff\n6xZ2FU+WiQEVV0O2/KS9SOe72WuM0ZUlX2ZUtfhCqNo7tQOtlnCIrzHtomYE\n/VUF\r\n=59fq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "54968d9c5a7e65e2be77642ad3dead9e307f1d98", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.5.tgz", "fileCount": 12, "integrity": "sha512-Lq9h3GSvw752e7dFll3UWvm4uWiTlYAXLFX6wr/VQPRoa7XaQO8/1NBu4ikLHAecGEd/uDGZLY3aP7ovGPQYtg==", "signatures": [{"sig": "MEQCIDUodmmNwGDnt8j6sH1U+2X4lBFvl4bVhN2+yTWhzp5sAiBUmKjBsQzPZn28XA7XpqbEpsUjwABUeOW4QVj53FrAPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiYCRA9TVsSAnZWagAAkmEP/3VrI/Dq0xKhkniH9v/1\nyk1MKrXS08CLEqGI68C9rGuYPTG9I44QLLK6cLC3MZssF8mYes/6IO3o1RcU\nmW+mIoTP0uyXi+B47Y1VrOe/OXGWvXn3/suSD6GN0yteNKY1wVI2aHMuXY/k\nqtKytM/sVqiO04P/CrU2c6zVEC5XwQnK3hw2d46iL/e5whO4Y0QOh3OT2HKp\nHNFDtYxpRy5VBqLU8g1cpqUfM029nUWP60dCBP59xthsQybm3wgad000uvRv\n/U6SSKCwXEUS5bOoKGyHG9rkp+nzO1Mo4t/KVtAOH6dYReNs+okfEoSDs3m8\n4SXqaD+I3DEsB09U3FW06+EMhcM3ezqwAQzMetpF8+RXLcwnFkI2L2lqPgyl\nU7nCzgkXaIH9Du5l3psvOlIheoMmhpeUrJjRU718fIOzPNaxQ3vuFuVbWnM1\nhurHwxqk16V+1Km0OT6WjdHwP+TwaxtzMwiy9GWnoI19FxAOhH8OVihmDL2A\nnd75GZ5sQ7loD+0cQEIstYq3NFMeJ29kWUMg7NvpXFgppXQb7DVit5uNtGre\ndfrzYQrK77NJRlV1BUkAoncNBWezLkF48Pj+5CMxNKiVJksJU0VrOGa+dbHQ\nFO0F3Ol7+MzAN5NfVtYNpl2SPMrn7nQ5VPvO3L7Po0vX3tFF6qy666b3c62F\nXOnh\r\n=o0oT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6ffb4b5272f62ecdb924730e5bc5cee1b51f74e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.1.tgz", "fileCount": 12, "integrity": "sha512-6EipqsvEgAoj+LBZxvOqVB2aBlzmY6nCN9oat+g1ggCwZsNd4FJ/yApHr1LPM0QGDswrdlY1dchtFX/VKmeL7A==", "signatures": [{"sig": "MEUCIQDZ3BQoG7Tnw8A3OY1TusScyMuwwee+lgcA6L14Tq3AbwIge+Ah0Ap0fd+I4OW4cRsgDmBej1hR2p2NWJexE4lwJJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31p2CRA9TVsSAnZWagAABgsP+QAnLcnADTHAwvdmT3/+\nNMw7oZ0yxk0MBPLaJ1m0+WVQJO+L0/gdhu72/B8ITtKY5+xec7ov0wReKGqE\nqwhcwVALO6WKcn5k9mG3Prm30QvWRT4dDOcCVkozuJZ7mgRcPb5BdmJ9nRHa\nq9Q3cn5tfg+Iwva+dd21L/j+2ocoHj2+cxPv+IMr6sMpdKsP8eMqUIErTdEH\nERJ9CvFb7hoYks0Zf8qsHtn+eosi8h2Xbfta42IYvH0rMN82umLCO5BfHgSP\nK9ZgpTJ9PvHrxPz8SR2cR5DbNUT4FHJj99O4N+LJqY2Rg5VraWpb1XinvxKr\n53MXrsQazPcaH/RGmscJxnMHIU3jpS4b2i5JzPxFttAmi6ei2wwf9YDPlgtx\nSFDrhe+U9yHgxiIjG2qvbC3aMxF98MkJtelMV76rgs0BCSJHyLqltVlT1R/1\ncWS2bldzoAz4BNUf3RsmAhPwXz/Nnem+6HLOt/qjZQyjCVHMODbhB0Eq4cbm\nz0BgMENul00fS+uExpA2Os0hdcoKtCEI6Dhe0OtzdrwKhNpo7/XXYOowtmd0\navH5xYIUuJqHIUCXW1GHflQoVkg0H7Ibi90Du4OxhbLnQaOuZkU0CMpuEyEY\n4wSdTuwQDd/Kc6Ber6OvRFurMzrzicBYLj55F9nmfbXuy3oZF0d7cQDw5Gyy\n5OuZ\r\n=MYjo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "771e10bc2371c39ec881a9d879e134d6e975a2c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.2.tgz", "fileCount": 12, "integrity": "sha512-wCS8JHn7NPLoK5Ucg0rxZ+TGLefHXZibJyq8YK0gHwJUUFKpw+5NOzl6uE/+fjrpKi7/DZsSmmADzraustrxhQ==", "signatures": [{"sig": "MEQCICyS1CITb8rJrXzWxyF+sX2j1VLLv6eNlQ7VTXzCriTIAiBRz8XbSv3fKGkeZ/o5v0HE+hSnV98ebXha7/zJ1aFvhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BCuCRA9TVsSAnZWagAAcxEP/RKG16XyScSp0f6n84z7\nLvlt4Mkxg0NRJBKTkIA70NSB1HNFmBmvLf8Xhd9sXjI4+eEszyXbARYWXYLi\nquth5eZJMasO7sZisbj068jbqR6aChddiKYTYMjFbNE0Rzjhjw4ci2ihR8YM\n9XNpXW/IK6TWE9Ee34G0bT8/1KhOl/BTDYHa/ClJmx90LZolLxnx058Z0XxB\n5QvdyAqFAOu0nGp+4/64QHSnfOyG7ABZkEV1q+OqXFkcnwNW3HRmXQAzsH9S\nNjiZMG95SLAoFUdoFlts6IQ+lLy2W1SyzatW4jsEgU7ZH3pFChnk4vdRSqNp\nzSRIGuwzq2/5WkH/Ns+qwP0JFpmN62NGYDx49yuy1vD4T3sVt4yTPzDitKl+\nUgGMBTC+WvAKfm5NxfRAefHqb2Su6wtZHygDr0Y7oFbpsyz5Kr5SYxSdf7Hf\nHRwG0PePn4dnTte6NJpCSL73ng5vYPZERUXKE6mnqtlTWud8bGhndnHxQFn4\ngiWW1rETsN2CXLD5uL3Ahdia5412tO9MwnXnJ+XXggsry128WZ8QI5CFpwS2\nWbk6o7oB1n1yjjtmP5eZGLq+P43riftRjYUi/q2xpSI7ZnCTKyveOhsjSSO1\n6FRMxmsTyeRHicel6Wjn88PUiqG2gLa8Ti6cV5fmSCHomah8gChb0ODsnJRA\na2zv\r\n=hvb3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5c54db53a8a0a72dc66a1a346b943315a2a62a41", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.3.tgz", "fileCount": 12, "integrity": "sha512-E+ZglDnARpCDcDxg5NXEOTruR68rzdvwvcJ7JBtr1uhC4Z2mJQPnlYCW+LFM0cxIio0VkSBXuIYXVQ1LuHwdUQ==", "signatures": [{"sig": "MEQCIDhWOYPhx7oJVs2gwcx5ZEVpT7OMJDE6qefqqHn7kH1VAiBkrwyqove7S/BPLOjLSzxUE9JiPiYxmOlEg48GntufDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4ClVCRA9TVsSAnZWagAAv7MP/0omKm5MnPSR/BDrceT/\nnMJQJevO5F2wzb79x8RDJsnWfcFi2qIa7WlI6+QzJ7uLMLMeyw8Ip1USJYfi\n/rYvi4U6yEouVzD9JB1yDw2r9MOq+d6Ufo1fgazozoJJXdRi+mvGeGhLplqh\n+HDWDMO1wjzJI9jJe1EG8LqQCoFmNykjVaNy4dp6YHhE14PmeHsYPFqZr91t\nNOPgynNUFpYr4b7a78ZF8IMBUr2ZA3wgdfjnTPvdyBxArzILUoy7dps11fX2\nyPfJXUXJf+P3OK215UBDJXZQdMGEGbRr3jC8KHdiCrIdFNm2oFUegfciSUYg\nV9/17r3gOz/HNK1oox3SbYFWh69bPYowCvOqyPbWxdIeGSkvk75q4ZHVULTN\n4L1rXECw6aOnJoJAkkeO6u0TGShn/zbYf0weud7OpL8PlYUC4X9Ar40amuX1\nX+EWNO0nIChUX6EY0/xwfIng+FDJg1DB5+i8NWCdRUsNYJyJyLMPUvMEGuNF\nl8XESL1EsTiZNNABVwYbpNqKOPLCdPtaXk/LuLicbVnhkkZ8X6n6heg4ef+h\nPV3xfwQ4ZcPxEkOfAkvRZH5WUTAKfZteOKioM3+Obym8PfJrwXXBhT6ZjAVK\nrVSh7tDQWENuZI2SVubwc4oKDU3dKNjiiWM/NqwCApSHMVdEtk0c7Oo38Oxq\n+I7Q\r\n=6ABn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "2419d8e80abcb2b295bad4800ed24caa27adcc90", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.4.tgz", "fileCount": 12, "integrity": "sha512-xpiemDCzpdyTte/b1Vuy7UxF52wNwNZWLqit/p3bld/1Bvlj2U6lwSh9s7+9vvFUFCwVr2skawE3D6ZJtzBVEA==", "signatures": [{"sig": "MEUCIQDsevp3SPe2TLjOXhm2V/ddUVv0qE8KweNZ4AQLUcSXWgIgbuZSQSPCn5iuQEz6P8e0PZbu8+dQf7DhWwRp9JlR1KQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Go6CRA9TVsSAnZWagAAHVYQAJc7wdRLSKhk2UZHXFar\nOmgcDfbvB75aOt4UynIUSY5p5TdiZU2EXcm763cfz6ViwsXcOAGf/KQEfjhH\n+l/sjYOpajRGXKlZOlvqGavxZs6jNI8Adev9sMaL/Nagp7+2pncSZSI4Nb+M\nW0TJVlfadYAz8dapb2ww28tLvNqojlvtCcvzsexsphLvSzpb1kXe7Ek9l9h3\n+CS/VJVVjffaFpRQjaQNa4SYBSx63r8IDDPdCsVXjAqPbXBN5rCB+efR+4fH\nmdSeK3nse0jQDK7jt5LaPI5ZnDUUGFK2OehQw1nJaDmss4M+EmtHOtuf9x4u\nr95eelIezOlLW+Q7E8CDya4CfsRTwjA8fqhnVZElFKDP3JfPnVUvs2ljqGEB\njmFZX8ft0Mvw5TPcWTfK0Ny6oz1brkI78T8TzF3FbTLZa5RbiR4hYxGwI1F4\n7X6fCVZBTeiXyUypWxWoyYZlibAnWSF0YJyCVoPgSubFgv1hYz5gCD3VGyC/\nV4RIYExUTN+RPocvwv/oHrsKxi5HdTknHk2M9mcsSU6NVbjmymJfkkXylQ8c\nMilf+kOZT+CVOsqutRsbkFWoIrohLV/u7drL3riwGs3izrx/HLKAkBu215PF\nCA5zWh6Xv4GO0qGhdKx/ECcIqW+Ridscqoybln+TkpuisUidC5bgirVd22N8\nJcRb\r\n=yang\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0e2e58a61b88344531a352ac358417d195f75662", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.5.tgz", "fileCount": 12, "integrity": "sha512-Bwz2zzqqOJpAl5MdNeFDOPD9xyuacp+xmvijVz9hQuGo7e6fopTubudQrN41kozK63sHfkvapP4uTYSDPNsCYA==", "signatures": [{"sig": "MEQCIAkDNPluuP6Ojc8dq2ZBV9G0UWbwGujPBXFkm1e8Z1DeAiAG3UbzCVuVdn4PZdrjjiajuwNQPlSwlL6mk56g5l+g1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbECRA9TVsSAnZWagAAyrQP/jHBvE8otfzQ7HBIO4o8\nmjMYzqDTkNY9InbR1A+1UyXjKsUgZLvThkfFGXEyxVzLZKQNNNglojE+5lbJ\n4CqICPLS8aaUp5WZNiS83Of3ykyBmUsAoFwFiVUEe46JACbPFgpHag2yUu1E\nDO8t/Q3XYXRveihhnp0/LdS16/OQhFDZtPndrilzGYw/kmphM067mlHLsKEe\ngP6lk9sWGcVcjpsW2vUeEv6fUc4FI2ozwL9tWxK5sn2Y9lyEiwY74wa+OZf5\nsy82gxVh7vKLWdu9AM4oRkLv0+ZbjrJb4iWyNX5cq6gtFWRdZ4MVO0qZ5Zv3\nRxKUSy3ol5mdF9Rt9CxKITE3fwFaUrw6vpmAkUBchmWRXG4ccQTf5D68cupQ\nzA8nI2UnmFjpfEZWwlPWi++ikp3mftOYgc2v11NQpr64ez5idX5S8NlOKcI7\nCuNVD3d9PMLK0q64JsORxhxYT81tsOXKpt9w8BXCEToBFy9zYIdbkN7tzUG2\nRJpnRkevaNzKe1jLGRAz2KkCbxQvGdHuSi6gipHmNY6zhZsE78j4cVvIqqYu\ntW8pvzzaOqRbUNU3fSuoL7EXuX/TW6AQraLN4eCFAnr+x18hOCgbX3ZjTlaz\nYht9aiw+zPyeQyEelzRlQvv/MMuMc3fZ4BWuwDoHnKXZeCAORPOTREuQFGUm\n0z2b\r\n=kUCq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1e753d6860d658719b0e8558189509f817fca95c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.6.tgz", "fileCount": 12, "integrity": "sha512-UDDDhJtwG4axN5FqZP0eRmXK0qXWjSZFnB55gz8uoNJSuXNNby2Vd1JaEZKuAeBFFo9zn+eBPyr0xGyT8gBd/w==", "signatures": [{"sig": "MEUCIAv6p4wN5A4lt/gLqDnsV1J88zw/qhFcNJHC5gs+r1DeAiEA8N+LYQoUYnGzS3uDSlXdtPjbAzT+8QntgAjQe1AKyR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YrbCRA9TVsSAnZWagAArzQQAIRRgQ5jun5HGzoL446F\nIi1RFiJ/cgE0m0zsiPIs+4G6KXr0HC8CpiRXk87WsW/g06vxVWevzkRNjQul\nCRdY1zonTe0c97GRrZsadub3HyXl6HB8Wjsjyo/sp4sSz47m604AC5GuflFV\nNX6RCIN2lJ/6huJM7MTQqQ1Z/SsAaJZ4XnK+49YVKBeZDaCOvonyLT1Jl5nT\ntDjpdlbXDn5cKrzfqIzlKPsDxNot9qbPiCGf073kYrvTgD5YxqltW//vfJB9\nEpS1WoBcgadtvsJSrWInxKzq8Hf1klt71MPXDTQ8QeZ7JJ6cpDhZeTv4O9Vc\nG64EWNnz6aaelP/w3BwdkYFDuEyasNwlcNzIHe9hdDJ2CE+fB7dYVaLpMjGB\nSCRko6Mkr9LDbBYn3k0iEgBZAVnisS49oTRr7SN3Z5RHx3AXlCImfXR8YPFh\nEMbcDKDSVZzhbJWoW2CKk0D3BvUUS9BvwE6SYwE4IcXbiYEK09Qxg6e8axGb\ngtCw42Diiqp3ECFmg1gGjGZQFBx2Eq3t6sNTp9HamRtPXHlZpwpr/OeksON4\nnKfoLLKsZTVFOeZj9ZOphX3+NtjGhRiq70oTGhAaZibkGFzdupdknnZKwqmK\naKQYZ9PJwl1KNMLugQV326mB46VUBihQzUrSVSHzR7pdcAMLjPt+CZYFfrzQ\njFn9\r\n=32Bn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ea3d0dcc84dffac412e10a9948677eab693ce8f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.7.tgz", "fileCount": 12, "integrity": "sha512-w3ZbUYFQnwXA45c7jJpa3gIdpC6PapZS5CtmevxArAB8CaC004z0Bl6mNm6b5NHJwIrpIUcAmCdp9F0p72yKkQ==", "signatures": [{"sig": "MEUCIEEQfQ6DMauParvNVenWPjVF/h13n8CxO0G+il2fo7CmAiEA/52SNv/WISGYJAN4RuF8zvuu21AelgfriOcq9f2DSFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scDCRA9TVsSAnZWagAAdcUP/2Quthu/tWnOYtFXHX9O\nMGwmYtbwW/50iVE0U4yY/sjYvtfxGqyKmMxOVhhygIn1FZ1tK6Vb+nkA69f7\nis9EL1T1KL3w5DJ26JkMkDaryAAzfAyz/zwoVsFCaSeQY/x2sIHM9QGsTMbT\n2ck9/ohjNE6XJr67tnFeCfljsPsjo+EAXxG7VLiLtODu0O6oQL6T5ISbflDV\nZSDOFYVqJv8fn0EJWQoQpIe2t86dWN6tVYAFXqx+ixhw/Jb4AaOWe77bhbK6\nLu+Sd5FwHQtgxnHAAxhp4jp/NgYGcbFGo3pxiU9DvENMKNcm9uXre+ZqLQwm\ndXXW/ssZf1czwBOKyCSSdpr3hO5T+fl1d22uWgqx4uIqG1lFLnY3BB8VRsn3\nizEGRXjGu1RH2Ooz3IXzHK+RqyANE1bggwHVqXDrab4+7W6uq9KSl3OdInuv\nio5JJo+Ykcc0ke/BQYlzBdEiU5SUHPU8sfD3wmWHhWcD2OJIfXhQP8Ta0lwe\nQNO1QYrJNiiEaEXgYRWErV9ePYyGfvqZz0q9DJyiW2Jxagx/m6nYWCDHJorM\nwbRd/Kk3wF4G/oxBNduFDevlx5yac7aAOwifffFMit9B/MjfKD96rkBTUmsF\nZvZg/BHk7CCzs9KQEocLfgu2T9Fu+RNSh+tMmrikj1WzP5ueN0oUnR93a/ZN\n/W00\r\n=GB0V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "cf60cce57e1ffe3f66915bcca4a194b71b18727e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.8.tgz", "fileCount": 12, "integrity": "sha512-pF+aOk8Wyx8cyd063bdZTlBi8idtKqCziFcdgI8XgI57gc9fLFMBCU59qqcef8AlCBTb/W5yJiIdqIW9axbdvg==", "signatures": [{"sig": "MEUCIQDnAVg0VNmxGiss/MSSGvwfJoAuvLlRS5wd0Mc5cuzstwIgYzWQdCnhNMMWGFukY9Gx2Z0kpONDbRHAk0I2Z5XBfms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCECRA9TVsSAnZWagAA9eAP/Rh+kwTlmjVirhPEVB5G\nY9WtqOBxcZQYyojq4Zq9hhpmb2Fx1fRfPS8l2lGug/MRMy0zWjdsQXDrA0Ae\nitBpuqp4jNG7grkIxXCyT6c9Kv8YA3hD9IWnCbYnBNXQqTCwJl/Zk4kCZMLN\nC3SvEzP+4Ihz4QR+7Ur6rAvoTtUZcgwRXdJamQ1hVU5swPYWCZ05VvSbH9P8\nxcf8WKG/kLMOJ9W/UiR/th9McQs5w3y6qrdB5eP8R19N2Fn6DXAk8r6CY9Yl\nsjJSzQs0oFwCuB1X4QU4UglJr7j0Mh+0cfGpoi/ozAgTEmu/AAxviFUovOb+\nMFZRPkVtWUUHGv44DIGepuraYSjW6lwPrDJXS2LiyMOOtpIhQNgVUnMWaAKe\nbpyb/V7rOk00fz4hZMj2wpQyC4UQ21IaV1HeHsoC+NSP5AKfHSw5ZI2x3nF1\nubZJzlJkrj/oWibe1RIfufDpQ6t6xKfFTlUhtp0XRDeKaALPj0Zva3gF5SzF\nGvnEscGMtzJ2c3882F0TyLSLiRshdjrYFxtpo3pTcngg/c2AXuiILH4QrbHY\nve+0O5Qd9ZGnVcPvsZf5bjuOZSPnfVTbfgmgfUkPGrJscioJ2XIDp8mSdVka\nPebHTL5gU2Qufcv2IbIlGTDY4Md6dcZAsNPFU1gEaaZV3y/qZLHlUOgl3Rjl\nQrq/\r\n=a8z7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e8daca2ecb3846316fdbb602f3a1a3d4c781084c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.9.tgz", "fileCount": 12, "integrity": "sha512-jsHDBBFXEuOvBH+przdkgb1IUoeULxe8AGzwqoO9Di+p2NFwtTHeFEw46M2M6sZi6xK4v0v4I1xrxBeTrKBIzQ==", "signatures": [{"sig": "MEYCIQDz+8xD+FbhdnaqpeRPO5L5wLVIsjim6JKEjNIBRvGsFwIhANd+CKCBe6fZTp/n3+D0FF7sBSCHYS6Ttzr4fFgoK+zF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xJmCRA9TVsSAnZWagAAiQkP/AihQ0wtLWTOhySs+TNc\njbkrgjSmN2IbXN64yWF01XsrgGv9FQKyodTHN+JUY9fQofh0RTcO4yeQZKf4\nsVDgaVRex5L/O2hncwRpAEe1km0dsGvIIuM747WCfz2CisJwwRhaa//wfDIh\nzEMGp5D5ua6GobhsJs2EVz6tEQfr00hTTgwUUcYgqOKTti3gbTe3/V/Ton6c\nRen9VSscbi3TbiC8b7mKyzCJGo+0Uq6NgdHRRHWpfkq751JE2Yjc304DxhPH\nMqX3id0Hursu9R2yYt7V/5AdGhOJ2ZAg132gu+mkAB3howtKWuy/O1AlAuvJ\nWUo01+EyYF+jCEGr4CGalQaQaOS5eAUfn38+IHHk8+YekeTftHu1HsXxLpc2\n+NIAApEtTnZPNk2LBabbbdBor1CM0NdD7QoeKXsFJn7xxtkgezWw1wqPPtLv\ns3zKl3hJr4ljzHEFwLz8dHb0PiarxIGqAd09bjBhMqa5IexzMTpwaVBOBHRV\n0orqJXn2rem0FQw88PThluq8ukUGPndz4jsPM/jw/an9rWXFw+z2mq3H5dxu\nmQpWnx/+wPdK49lcSweF1pibRHPJZTZ4E5enxhKmcP3oYaXkVJk+ISHCCRkD\nwGMO4WBND9blk/4U2OWiMYg9YkbQplHKvpBrUvNdG6eCbu8UyAWEokmCBpnr\ngtPD\r\n=D2ZG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5918642ce5dfcc49a948f0fade1924a0e107f575", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.10.tgz", "fileCount": 12, "integrity": "sha512-Dcp3E1KewhnM3TCmoi+l9ORKm7eFk9zuuWgVzJJzxjO2+92UEuM5bUhFI4mGIwgb5ZTra0rmHjUU2ElHQWDH2Q==", "signatures": [{"sig": "MEQCIBerrobF01m+4gApPUGW7Ks39sVmIGIz5i6xUMHJlS1BAiBd8B6nPmo+haynL+FCto47KIvCXmdFLAC7j2s4XVznrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DyfCRA9TVsSAnZWagAAYfMP/2/NM5OiGJBQQ0oAHea3\n+TPvUQ6Pri4dghF6Imf3xwAkQY92yukmw+BsGCdGQ1gCXf+pz92gjCwbhw/M\nUQhx0SMgdzi3feCuFh9u0Dt7iyk/oszX1cjI0icxYQsmcvApBP75c8sTCtfT\nl6kX70gnDEXoQ+2p/G0kSHo5VwVKdmDtn3MR2DU1/udaZjJMNk9H9ZGpf4d8\nqRlG2sRTKPu8++jhhQ+lOR4kP1G/ZPL3f4ovNvdaN+Xkx3V0BayuRbHZadWA\nfMT+hW/Mu9DfG7zA+AzOE1/7FNhqTUrfhHX7MSDrhxTOcyJEP+lKsAjPt85N\n4pS6NHSvMy/moHFkaxKO6qUnbedSPhM2hKJDiACJKACKeJ9kCp5Nz4VZCkz8\nmaD64u7Y1KE5vCiXGnaatpDc+DInOW6IU3Py8Ub0GndfSQal7Swny30Pr12m\ncbh4khBG6KraHC5WOIU3qWHy5xszbFS5FeTtu1juxdx8+8mBkIDMK8eoc2Gb\nRZa0C7D9RhBMC42ONTKEZ7Z6vwv96htA+KjkIqZKQ7fji9LpzNkaCcV8sf8N\nNPW34blSncOP4FFTR6nj+htu7Zr0fTPU/VqMrE1NXUnw9EZ9hdbXZPgw9m/f\nT22D4vgqv09c0C1cKBVAJ2v1siPs9WplAwEweyjWtixgcs6GfcobuoxkUFD9\nM9ln\r\n=Nd3A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7d07e16bf136b618f6c284439d1ea2a1582274b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.11.tgz", "fileCount": 12, "integrity": "sha512-tKAjhmGelQr10Z6bVxKQ/rkoKt8Ob1iUUJH4zNoJcJLcyNS3RQFk2KHnE1RcPrbtFyGXL4XdUkAg3gtwuQZolw==", "signatures": [{"sig": "MEQCIH15C7K/hpX84Y35w1jVcL84F/krpiw0fG9tlK/AVPx6AiBmJBWN2WlLoTCtIBlZRmra6XV68l34vs7Acrx0iWrFrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75776, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SRpCRA9TVsSAnZWagAAkH0P/2zaWhsqIn3UW+/b6iIu\nB5+sj3fd/uxxHhtuvPYUmAxKMOUocut3qHSTEEsbUL9hD0XQ+WQR9zx/qD6R\njecuhD0lngIaktYah8kUZiR6cilCLXLmtNh1znf9qhPiAWB+p7p9AaA9U42m\nC4huoBs7/Zl1DJnU76IUXjbyPr9QcUKFRdm0Y36SRk/eVYxSPevu2VlxeqQG\nhLDyOcVJg9B9dCZ5Iv0NiTtdk2hw40PrsZe1bx4wM+hdnoa5tITnlbTdqpeI\n9L+bM/74XAswh2jsa488RCnV/nBmRFmn8TdgQ1DwHx/WsDaNunYeHGm0LPlM\nsFQrLngrd/9DzhbslhmY9UJZ1KwvvVuOP7ux+vck0zAUmRXjlP2sgibLz50v\nw77jHjKlfDmQyv4D8sDMMzZXHdEjb6vOB2nHqDwbEMVNePaCt8NRvVSTYXU7\nF74uw6EzSoh4qWA78J6pWhfnXFLVoPtB1M7yfNwc9vAxX7pQMufXpmycF1nb\n9Wuu1Cf+FBV2fFy22Fqe3ognPOABGj8KH2PIYy3LG5Fe0cTZ4jpbQDfbRKwo\n/oOBeombAJLm58kwuCmqMwaZT/onW7RaV7/i/BeJsS5sFu49Ix7tDUvn7WUU\nOHJ2zcjgfPBTDkyAXFdrdWrub+N3aj6N8iI7f5K5v1Xb9eoa7lFswck35Rkc\n4CTK\r\n=R2jm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ac9d54190e6a32115f35e32ddabdb7fc2dd0e808", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.12.tgz", "fileCount": 12, "integrity": "sha512-KX6V/dYHf2i2TzI+OksWUronutV0kVUdPKPDggVkVCpYPmw62/0bh6Eo8p+LjUVhtExE/v8UKwV7nI9KHpbgAw==", "signatures": [{"sig": "MEUCIBrBHxQ2hJKCiSEWiOuDuV96rY0jjEWibBwXr7Gx1GYWAiEA7zFxJc5taa8rTH07LALk6V7jDuWvSrS9W6MIyi2CMQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DYmCRA9TVsSAnZWagAA46MP/iPYQc7SEw0wgBuE2FKU\nL6m0SO3Uj48zg3sjxFzYcF5O3TCGMjYYOZ1PFm1c4kpNBSRQaMXYDpiOswCO\nDvOc/vFlRT0w2j9UwNKfIPeA+BjDXS+VRHmgxaD+Ec0L5JJg8jGySoGJSz1P\nE3wJ7/llpyzsVImiQ+JC/Yav9gZF8kEI78clydF6n+ZzwNbKMrWd2z+wkwQW\nfPS9EGMwBHzFh5mfvCqhgXSZKGy3pgqpxmlbzj1u9DCZ0aIKdz3b2AFVU4xl\n6Ow78TKqy9Ely0Xr5WC1iCTMYr2h5i3zy+qDh3onOR/obodca7/edSBSKW89\njRJBntdEY9frO8BXPzGwfLMFipEl8WuiioQA++r7GVqp0wUIVUqUg/PD775a\n+Wgw8UQhnK8861m3xp6RAw6vpbq8LG5arA+Dx0sHdCDfoa2rwXquih2zmKUt\nJUN88Nsl8Ez+/J9Zcrb9x4QESoh8Qpke4+00w8nmCyCpJ+l4pwbFxWU80ugB\npuhJb6agpdpu4iDApBg2HW+SfUQT5XFI4IraEG1sjfoiRd9kxOcjAgDLqlQi\n2ZBJnDmK5a8lthsjfLnhuzzLQSfCjSRqo3lRp9u6nMn14i/9vsNoCnGikgHH\nZQ2OjHA3xbJySCAMoHZQObnAbuz8XCHvFM/hczf9NljwGA7miQtedetSHSi5\nB/BQ\r\n=4aXK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d2f85ff4a18bf3df916c01f0d66073f45db057b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.13.tgz", "fileCount": 12, "integrity": "sha512-IcuoioFwnxi/FzpTompfSLUjafBNliMZiYPRigU0ri8rBiWIlgVLmuCoDgv2Tbne+pJOSW5649F4vcCAQHKr7A==", "signatures": [{"sig": "MEUCIFGdMFrlHFwqKv1QWGbMorzwG3jwuHTBMRKNxbjWKjyJAiEAo6MmEFG2WLUl9NX+F+bCWrmffPt/6c2vEUVAPebZ+oY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WmkCRA9TVsSAnZWagAAgEMP/3sfv9X27P6CTYQop8Rw\np/YJGoF0Awkln4eIvZ7N5CJbIIeh48gCBtkG0786MdZG7F+vmaJeOR7+W7DZ\n8d5d9s8bWE5nbuQSGSo/Q0wdjZsXDHuadG6p8QcvZXcO1u5x02MzRCYYpmNO\ndQWt7qg5hx9pVsOCTppuJZ8JF580QuJ0NIik5l2u56MNZF2zdMVCnwO5pwUm\nIrNyPQZLj+IEDkjS2uwhcnj1yZUIxj1mAx2roidNNNJ0IRvCR0TMiZbFHxX+\nXYbSt8miy0Xjd2VabwO8PQA6R824R3Sdf23LWiZRFIMBfKoMdgSQ8oP1anlx\nWIIRUmp+BMsk7WjShQ0eVRJ3gUmL0MCNhVwBYRyhjyY4fbHPpwUCuxh95efw\nsfTQB0+/ZnMC5QYv2daOx2N5SiJyoYE0zjzO2+HqAPkFW6/lTFkl5nxoK2fb\nt0jeqht0uRLwbYkDe4fQdFoCcN1C7mCEkhRbKw3L4fwkxqnaWq7SNO9ADZMp\njkdZ/6JYWvzEHB/K3z6lyI0W0hLhkrm0ma4yDKJ0FOoqSXTGpTY/qhdl6hR+\n/P3Q7gkd/m/mrUHaDfhOya+7si2FxkbcQl1uMC7Uh9ZFLm8yZKQQs39yZfmY\nL0alBJv7BDPn3pm4j4moPj1hc+zF6WKfoJt2KcPShNVCBCEXkLdd95uFB4jW\nVfv5\r\n=xpgO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "96dccc76ff151447853ff87b1e2caba6a5932143", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.14.tgz", "fileCount": 12, "integrity": "sha512-Fs3j75GvJVa8ZUdjAx3FKNa3JhWBx3K8AT5Nxcjc1VXaU0qgKyL60qi3DnReX0xWmqXbkYjOGYsbTMXfrZKvmw==", "signatures": [{"sig": "MEQCIEcdMNe1oyzYA9DGa89kt/KROwdYFE0LNCHWOS3rqTX9AiAUnHF6rh/L47FWKceFSdP5GHSptiEuEwghU7XX47E+6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTACRA9TVsSAnZWagAAmKAP/RXaD7iz2j9nUfnYoTsW\nVQ/29FdqDuDoA7Dk6NOGchySM+Rnvxzb3jAPtB6BBQFbWwJ+GLW6ssTDbML1\n26Wa7MywCVB4Bi/3kURqk8TsERVCeRCyl5HThzlK415eZDK8Mrk+2PGpi/UD\nG05YCpUFFiu4PYL+6LSKishkM3AxLSaV8mvwapyxrpRHtWIo0UNuwk+GCNlQ\nOMVoNJggZiSt9yp17Qcyf+SWZGReWg7KZH9yfzVQc9DiLAEOATmjm6xJqv3j\nVGqPtuvfHt2IlNwH/OVIv7NfUye9848ihDbYOMv4EQoSqrONvCdkMhcJ5Wkk\nc1UvpTLZbndTZ02WWIF+YnAolUDJBKrGENuNTTJAUhhldyct7k9J2D/2qhOs\nKakHEIb2k/qcTU106ltw8JIyT7SOYkNemCNaUD1q2Ko9oHBrOokF1X7c72gH\nqVRQHkSUkUj3JMnf1zlVJEDe8E5k3pI0FbF0KcuO/DD9yZupRK8vvIzMQNZ5\no7ZaDqXDxyBvqbJzzGvNIRZZsZCp/H0t2MhItC8AhwtxdAYzwtxGQpALlV7j\nR6+7Dh0S3XwNAH/Au1O3XvyVgqrlopyef7tRrMvrLpp7hxKODNGBkSvEokWF\nZpxjyVC472EZbP5Ry2J51V5wYP3q3bLO7SLzuUxEJuXxO6N+rc+gCBidxqAF\nGgM+\r\n=ocRO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "a4e3657e3a993fd9860f10a0a3bd8a581fbd3df6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.15.tgz", "fileCount": 12, "integrity": "sha512-30dI87ua+lJTkvLWIQsPC4ngyh5DPITiUo580xeoqcXjGClGr1nQqPsoqk1cT7mxWLE1RvnHKd21jAem1lKNxA==", "signatures": [{"sig": "MEYCIQDzIO5mQbIvfmqDMHOR9QW0wuPT7BLGg84ND7bPxcKnKgIhAMcXb/BsluKnC3IM2oBlXJSTxf7YM9Hdfz3L4uvYS/oP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/mDCRA9TVsSAnZWagAAFTQQAKH+S0AntsKjvVy5adtX\nNYaGXgLwBel4XCqvStjIVURFl4E+6eatIgWovnTVbygnHtR0uyLe+OOP+rQW\nLsl51vNeeFj0WwZ38yE+vRMaqev+ERWvwy6W3GWjLymMzbXMTdgcB2r2kVit\nO/nrKg13yg7nGFXlaGKu1xTkpEuFhlF0LvHnWODnUFvJJcEu+mlFKrueyB7o\n2h7htXihTVjLaHqyNBHBzieLNmsniBH3OXQtxZwuoMVeEjLud71WxIngo5jB\nIa7R1bYYdCIdkwmtK3Hx+OisLtO9r20UDj+kPCmggIxmXLgdLEliFLaeN/Mu\nSLTITfcEkM+BcpHeat7jXlu2egB2LXnLZxlGXI9/6xyOww8Vbx23LSXiV5Q8\neFEt56x/TstWvdQs6BPLOBgP9c8WKPBftFjKsPM0qw+cqig3IK28qtN9Ai6V\n0KGRZUjo5JBHVymtotYsL6TJSnZ6ZjlUPvR5W1/10TZQ89UlINzi4sOMbTAJ\nGryNQpXtWTScwzFx1NA/VAMbPtZJLbt455uGTVS33pn6yyn5aFrreIqrpdAI\nZe5iekWHp4gv4U09QSxNEZ94/uAUKQai3RMEjHdPzn4P4B6dWneJPr+5P+uE\nLDD+t0qWkdiCnopkn5D2AfizlSQuLwl0mjESBuTBngljhrsgL507y9vA+ZXl\nfi7W\r\n=YG/X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e7692e435778595824120ebe91c085bd03deca89", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.16.tgz", "fileCount": 12, "integrity": "sha512-JuwGdQTr2IHgSU86s+iaKBKbjjlL1WlE4W2ktL44pRN0ghAy0ZihyFUNZeSSpI8xgP23nEEioHvxRg7hizPHJw==", "signatures": [{"sig": "MEUCIQDjip6Uk9r/ZfFzbvDSQBWZjPeIsOpR7MZT72REs+YtdgIgTyazysREfoNq6+Nm9dy8YV8bSUhmRpW7z0nuzj37rc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBGkCRA9TVsSAnZWagAAcjsP/0OdsHQZ7wct+7wj3PF7\niPPDLyPJHn86ixCIXIKmA2ds05u3mouzQkUUGv9i6khXTXy2Q3156IUr/XxL\nGIWdO4eKCzyF/3p75K0RV2XLKMy6OhU0NXo7aLhzI2CDn91em42vMra6rRly\nKxyz68mCsyRRFkxuMxFYMY+DD5oG5m9ZQX1Cm4dEuUC7x0gc9xOYBUW65t4F\nxxgoOP4/ymSNVnpi6R0YP/GJSUeRqT/LqldY7eTKu1cU+Nd1KdD3zI5SpkcU\nSN9wcTXqK4P3ulIa+t63LQwLcRY+lXCsItlt6Rnzscbbee8KEAIf/mlaqwsK\naVs6gu4tM+Kmqg3iagibOHUdZpas/yifIwtkMkx6ElA2PN/w2BrpXZbvkg36\nf6ZPKu1Rvq6l58HQGRgVPRshT3O66bBwnJ0YKdYM3Pbg9CVWEoWJAwzIXree\n/fG1SQMlVyfAHOFCXcc/xXr6inNSWoQT/BSLI2b2iRr20VdBGW4kzRe0LsAU\ncV0pxnCmlhdszEzkC40BhClPNwmYgn7fpc4q7QwaadMrACxa2AooN7tmrEuC\nA+Vn4AzMriRydGlqYakR9OHc4fPG9nT+WbX4Ue7opYlx+89KgpOwU5IpcUpg\ngkkg26G01cRqGDO1MFmx1A7lVgYLd1B3ciSPJ2eVVfZ8IrfKL4jg/8O0dEkG\nsPJj\r\n=/wYv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d8ae52d94a56fd7286333372a4750a79c2a43f4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.17.tgz", "fileCount": 12, "integrity": "sha512-nInUC1e20T1ZZPGAo2e1n9DR4P3+xhCn9KW7zR5ir7lYpF9WJC4yXjm6WYeoZRRps3b/XsK+W0D1tOa5JUOlTw==", "signatures": [{"sig": "MEQCIBcWpjGZkatgfR+iNVND3TPX6DZmEyyuGocehHbi6ysMAiA8ZoBPOGhvp7xRJSvyPNV7qak8p8dW3te1Hj8PoaTCuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBWqCRA9TVsSAnZWagAA/eEQAIl2Ar5jiAeIpSxzRwMo\nXM1JIoRg7z5yr7vyZ6EM1dd+IqbWJmmkoAFXNOib0IIvJni4z7/k/E086X3T\n2isB4AIQChpnDfUxW7wNGH1kz9MhLFiLWyUVLTnn/FXf/r4551dLoeITFxyW\nPqvlIg6TM7MTTZCMagAN5IsKwqujniPwGAeB6fYPZDQQSbk5ED9Lm6JIU3MC\nNgToGtNDZ6ad4d2CSf+REIScu/YRZqhiekUh6JP18ytHnywZR+RgJ53bRpnD\nPj6S+WNH7bObpxODESbSrnEMdgxIjYFicsCHHgdCCfFe1mLTj1MjnRqtgjzz\n4ZtJAFu9PoaGi2GNG1/rb9UbGur1qt+EJp26HtpX1dx4xFVNwuvLeneiKTUm\n3YsW1owQ0uEKz0ETaqIhA+CoJd7bLqy2q/hEnn2Pp4Vndjz0JV31MwUINYGO\nncpDHk6GcWwXbno14xcSy6TxDa6VT8Z/JkvMxItvk4SCuTiAg8RZ8555+xLT\nB/V/0TlX+n4alsknExf4roW62Au7LpppRitrPcxOmto0qp89nPmJ8u3iAMtu\nHBNgz2EIzLy1UJxg1ASFHlwRNeI9rtSEoqjDRPOzpHtGb2Zu+Mh+zStLw4Jo\n3qPXADFY5/aG5PEcUep25QSaCqcFbaDwEseSZbs8Cm/ZuS2iINQzJRIkpuBU\n1h+/\r\n=SStz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b4dba3de05b72391fb7e681d7c1e81be200c748f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.18.tgz", "fileCount": 12, "integrity": "sha512-WB2hZvpdrehVjeY5jYPZBvXdShGkip3CEiI4Yx/YlJuvhQ1EBy+JgoERPrPI5mO5LtdWWHdW5QS28C3qzsbfgw==", "signatures": [{"sig": "MEUCIQCXGM2qYObCJWgh27oKysg4CAIMvTyUxSxDnR2BF5loZwIgV44vRuBklqbA4IA3F4cSgE37VUpnDApbDJZ+DpUayaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlklACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq38hAApGLfdgvyNc6DG4gROa246+tyANyvm2IVmihpZapwQVa7uKvF\r\no3C2TejLc42cQggAtLfy6dkD0keL4qc31okBPJl9QlPL9TYVmYJte0TDsFq/\r\nYCB9mQ2v9trLBqHyangyG3tDoivSOBn7t6IJD/RCVhTQqe5jSio9Swfi4HQ/\r\nqfU5jVZEIW8RhhLR9Quvwog/yxqW+8fpIo+Q0U1GLg30TfpHO3b1DvP40kf7\r\nSU2z/Ejtge8VkV86eyciNqa15GwBji4B/oowytS5ciLGWIDDQuf9mIp70w7u\r\ng4aP0f/9Oo6Ame2TaQwUfqk2lkhDmUpHkHn2va4h58Zy7bevWmbZGNJIZ943\r\n5jLZe9baJKfBmJ/zwD2fIqcflk3qsAGd2+lE8o5OukgWtgMjiXIL0ca8VmoQ\r\n267i8Gwr5ULsXFkJl2JVPTjEa8VzeFIAek2usIiY7GFBJwPd7sN+QF+em3ko\r\nu+YbyyFJ4XllwVOmUZCiC2zVtKUWzydEQVz1Ejulj0t1FavG8R32YLK6LrCO\r\n1KDQ1AGEJG7QXHO4F3mCt6Fk267BRuKShXX9M1UBxEVTdzbViAVbjfQObR3X\r\nZ7gTJixIg9VrdYG5RSzlRvvWlMFDXFMam4EbbKGRAEyzpGwjOszhyAtvpVXv\r\nGUeoJjzxu3yFNOcdh/Oo2sULNOLosYW1MPY=\r\n=VpFx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "fab1b783016812f0db7de1ba1b46c802a6ce9708", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.19.tgz", "fileCount": 12, "integrity": "sha512-QIY/6HDeJhInDslIKE0D8KbMV2rYxTvVr0YHpb5DIO6PwPX6kqb0QjLU3WMfWdm/k489ua/elCHC/v09Yx00EQ==", "signatures": [{"sig": "MEUCIQCOuRP5GRkSaJTl1GReTxB1PV4hH/fqsfgrgBKMWVGR0QIgJnoCEv5oPKhjGIqrADaalIkcEdl3+iBPiANEFeGbvGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqa6w//XBrrcnRARqrze3s63iAGyOuul5wHXSrafA4ystd3az+HUQdF\r\nWGCGWzac60wy+Qqre+uwxBn4g1jr7WuxQPOrhoyA++tXTFVudJSSfhHNGi8W\r\nPINKVdgvRkCQsujfabf/U/SkYamrsKZa1ogybxgVWq0/MKwFJI1DegeRXWqB\r\nIxC01oCRLbJy+/Mplia4B1z+yheX46NSYa2MhL/SOXU5jueomRfid2XwxpRc\r\nLubJacWX4KnMswZhYmo3mxbO+BL8+uZ28MetHCc4ZYKthOnyLecDL68KSRQi\r\nPNkY3aoa6XwKTgIxOIH++hD5e5kZiEtHrlIUYnNhdLQqlgC0Ye+k7+M8HptG\r\nFiSSz3MZpRnaoZXhEUaEFc63xS1CK9ufbalJdfVlXMWTB6O4Co2DvSAfRnKW\r\nqjsR/+fSWoWru99ziqyV6ht5IWdEgALc+VGSkh087/z/ehFvnqemh7lCHC/2\r\nSZ1Ej9IGF64M564l6/VvZsz1xnYg1yCWDUeHDqA4xS1y8vH1Pqn+CEwoinWa\r\ndZFlS9TvN7f1OxFI1L30BD7syU7tyICcwfSxab31/p0Z5+miUyz1mJG5vKmT\r\noOGdS5Xz1QIoTXoN5IrHF8EEONTThV/LIOwfYv6vF+BJ40TSjqi+3arJUQWh\r\nYFM9SgKd+J6A6PJH/vgRoZwzLkuA/PTjcDk=\r\n=+hsB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "100498f7d2cfadfd1fc23b4dfa163e457680eb72", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.20.tgz", "fileCount": 12, "integrity": "sha512-tyXWNNDuk1yK2VvzebYd3VGFmIOq1nnZI19lvEMQOucdNj8gSR0e2ighUroQa/kCRtEWMz92daZMywPXmEeGDQ==", "signatures": [{"sig": "MEUCIQDiijLf4cZx9yDNAMZBPFyEJSyX7X12R2aCUpl9mqJ2TwIgWhR1rZ622/7FlP+2I1DS3ALQLRSSVLeIEIZkGHr+XeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqM2w//SGC4ELy/WJ2I3dgJoeNMc5T9Zz9yiueHOTb6zV/9YH/NYoek\r\nyNq9KabS0FK6C8EdhFzvlx19gtTBzt3D/oYNPxloAXc3wba7RJnoR+zbjFQL\r\npbYRVxpyfcTwK1IpHNhIz8ZF2cxow+m88e/Qf+Miq5UO/wP+6yQHzY0CSUEe\r\nOPKkP2foQQYEGBoJLBnDwPNQk7vG/jTeqwUol45XnelERa905jDS13MfoGed\r\ndV3LJOPlTx+Iru378RtDzhyDEfOKZsu4EG7+6gqrNM8dqjCP5A4oe8pR1b32\r\nprt6Qa6kSjYr1D6KrsFohK7u4nLvS8gyT0BzHesNA2bjJ04fAr8v8wJcLFKJ\r\nRrmD0lYxBv32qrWQP6vc7mvOAL1sbY2B8dgNW+Jy3bLp3hJZC9wGQkTsh+T6\r\nmLJaqoSwSEc3uY+7uSqqTuJwOHT+sRo6jQ35ht5sjDPWrH6m/wHz1a1jb5Qf\r\nSfC2faWy7D3OmkLXoi1eEd2fD5CMcZUjYSkygMdbmkfiO4Nbice8C6nI93aS\r\nzUgMjcTzB3JVbBohmq9QfTI7sD3wFLqRzQpjoaLmIwVniPFR5SuqIy4WQfq5\r\n3hVBYmtb9kzpk/Dvrqa9NQgdDAgjTWLT7duIpsvdywoTrbhQnKzeTlnJr1wM\r\n6PhYs79BD4UZVZC/WsY3H13Z//o7XSEVPZQ=\r\n=JAes\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "534fb0645d70240b61ce70751bdd11c45d6bde32", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.21.tgz", "fileCount": 12, "integrity": "sha512-PmoBypZ4bTOlpRw1bdytSh28f27s/otxp1yoW8i7Zjwxz9pbH0xH2NsSe81bHsqQS3YU1jMG49LlD49kSOvW0Q==", "signatures": [{"sig": "MEQCICWAr8DGaEf17ODQ+WWwahFnrVkFylUIUPhYs7U3DD1jAiBw8qZSe8UqNn+PPW/bWx0NqBtR515BXGv6RMSOptsfoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkymACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9Xg//YVarVK5ZzNkaVpAtlYzLPkYNbZ6IN576XG1EWCpgUzC256Jj\r\njHCdOLm7q+KdP8wOuCSLQxPdYFHLI+7XV8f2If6dYBmXVrvLWBvekfj1xbZs\r\nbgVbz4NOCumbiVOIhKIiHt+ByvKOzC70d3/GOcGp8PdjoXlIniJn0ljxy0WY\r\nO6KiujsXjASFWFktnCZKly1mMrCO7ltHTtgXAw54jDxkgvUYBvjsdSI6Jdoz\r\nUd1PF+5y8HccjBOvwMqeJfmdWuzkRGFW4Eke5qgteY0zs2jG3fb1xD0ji2/k\r\ntrtl6RE9PgHN6Q6Dm2NYKw/B+qe8EbpYmrcQJLoIcx6O3UNtNHYvj5egsdG2\r\n3p/PHo4b1tHxoQp+NqBUoFWuhztMvGD3anYsoCJJy/tOmNyjVxEVRyi/p9jd\r\noiFJ+WW3Eu04dtGtHDDRV6t8kVsxC+3Zi2vsS81CrRUoQDZddltXD1vItbK9\r\ni+wPLLvj950g2ehwkGmgitnzuknrjniD9sUJRAONvx3sZVSLgRQ3rppzi6Ta\r\njkSpLFbXjY0IVe1Bg1Voqem3zlZBeyyUa894Z/uxxnEKhv//tWAaLqGZP28g\r\nDPU9xFk2mo1n+alOnjq6U3H3fpM2zovPzc159SR0Nb/ZgszPIFfkNADqvozX\r\ny/82niUXN7nJVpxgQyCwnzvfrdjjCk9fLTE=\r\n=dEEB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "f0aefafdbb5ebf40019f042d192dd10c091dcb4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.22.tgz", "fileCount": 12, "integrity": "sha512-xf18IBrzQlD2DEF4WzvBamZMh5e2/7GiK/Ebg4hlvaKiJqtRRBjgo9aaOySciKvnlg8NMILlEB3ys1F1fa4R2w==", "signatures": [{"sig": "MEQCIDwzeaiqZXLI6crkGagnhMkvAE/5VBMdibjmZiFJ0HPaAiBtPc7h3eUrm9iltMGAqEtBvZHughzg/PqPHMqhkT38Jw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlM+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGkQ/9E8S3NEgZfYe3XfeXCEnUzT00MEZhty7c9XuZPamok3f9+wgD\r\nYoVl0yVg5IrOkhZu05xXLvRGUc8vnhRhufTKSuJyXl+WZ9jdC73Qpm38cWzu\r\nlnLMHEwweZ0ZuPUPRCDK5MmYOhDzcFOtG/iFuuZsPEfvIlo19UJenc7uFfZN\r\nsYY7k7G5Kv0B8emKPI9l8vq+Wc6RkqHbCQrBFRVSs0xFQi41IQliFFowGuNm\r\nI4falsvpAnbqXdffBddP1tc8yOdFKAUti+TKijOCFRWN/OEBK69nlr8MgMc8\r\nzY61kL2nG8D2XOKH1mCb/WkyBucAsqZzV4FLagZThqUmcF4JZSIHG3dGftMr\r\nFJVmgFNwqyOKaTo2ceTqjqrcna+UWlZ7/yTGUGpVA2JjoacZzugunQdWA44C\r\n433CsgsNYPKwRHRe+VZzqCUl+vShhifuOTBmCVvSKwJu0U6z1si32n6Kj6hL\r\nSPZC9VhSBeFhgt8qGZlPTbIx1FRLhrc42oPhJcaDHrqbgr6xxoIAvH0Y3mLo\r\nVzAzTbtB+ojnGrifDvaeNHO4v/oeWOJidsf+gur41fNCWedhEaY4CnTqRU8x\r\nR9QDHa2rpGOoiDMU8aVOeTJNEAeZoCXPlSGbM7F2Tm1cNtG/rZ5nh8cE/siZ\r\nUsBmYAz+eW2ay9rdHUPH3i73q3YMFYSGYdI=\r\n=AaZ6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d559c3603fae7c3a49e9608edf73222fe307fc54", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.23.tgz", "fileCount": 12, "integrity": "sha512-t56ox9enBmZ3YV4fCLCzFG0aOIzYnPtBparjIM9QFld7RaIjcBH6zBIKrViHzXeUxB8LPFmiXOI4+mJDMskxyA==", "signatures": [{"sig": "MEQCIBef/DCMXR18m+4b1WOy8N9KE9gmaS30JyASEyqulyEWAiBcydDtxV2jYn+Zwf+srfWqy2zR3KR+1SZQ32YJa6iEBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpC2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5HQ/+IOyxS1hVBaOpqLRSsGjt6cIrRFenYqAF09/iAkQOQx/msBWw\r\ngSSLgj9BmiNIbXy9Xesz5UBzleV29QP49DqMm7vECkMzIYXbFl+MbFZ3PRGI\r\nKsZHIUEGiR7XYFQ1q4I+Tt/OXSKg17mQyaBEU/Q8Ipcvn8348hMgW8XIXV+A\r\n7facB+kXwty1x1iu2xIwLfx0OzApOciAaLtcI73Iad37IyGX0IS/xrBN3FqP\r\nO63CyxBLu1lS4srTuRIyUdO6jteLnMRi0cTnNAM/2KJtsrjEWLrdtopWtwVY\r\njJqvTHNv5VtlWeghocjc7aDiyk1zT1NlxCJHtYm64T7bOUmg0lMGDNUYqFu2\r\n3vFx7Y1fpwEflxc1kWsmmSZoezeg/EqgbzGSBtCX/Q7QLfrH3rwIGH2N/186\r\nK4hpc20k2ktsNjm6bvbqIMaxSc+B4zndIfZfhi7qzrINYkXxX424vafsYcZS\r\n+HYgLVH3SVTktHQnFatR1Ey6numL/kKygXpzYvs1j6URTUaqg6N8UzH8tlNV\r\npiTWgvsWWXzpcwFlnoWOIBDBsdtsdd3/W6L0WT98cEDe0KHCNigK9ZthyNKS\r\no4H4dP3ZG2hBnhROG/9B9lRS4emONzm1rQFen7/x1f663GPZFoaY5NfDUnDu\r\n90VHx346F8Yq1peOj5lL25Q2uCFZkmOO2hU=\r\n=Mva0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5416d5b0632c239f13057865ca07e4a563651ec8", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.24.tgz", "fileCount": 12, "integrity": "sha512-mtHpW1UQtVwlFXVk3o9KWjB+1qAfcu8tTHcvLVK82mnfz/iIWoDLGNuOIJnDJqh+lVs/s3q8Cu3J0QLrQ35Uiw==", "signatures": [{"sig": "MEUCIQCFhG7596yFa4T9hAN4CXOJWhnKzSQEKXXr8uwqAPlHmAIgdfI8+Dhjj5DRP7b1PcLtOv8PGglg4GGDRSRpmF8vIVs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoZw//YWDqeDvJWLVq2SXVSzev4u/WEi/BapMjKYkS3FkR1VT/UWuc\r\ngbG0BOvbrMrN6AQcSs02G+d0iTWtPZUWFtmeprNeusZVMC6+5AZihURYGUvS\r\nLj0p3ljhqikr9JNJs4Bc1j9xh5J90KvjGB4K/CWjBYj7J10fdBu5Uw6TPXOo\r\nuLT+0XDWojIRYuH74dUyKHbal99e0aCfwtjTPoNbjX2nZ3opPES6IeNvgrui\r\nZbYd6hq9HIRQrADY7qtElR8Q+V4a456Gf9B9j4GXEaUg3ZsBzClutIUTLd5F\r\nQqFIcJfR1lwGXxyqo/O1X/9pHPP5Ptt8Qej2aVaNpkILzm8L6lnKmjrAZ1uJ\r\nln3k/A7NWjmqf96VQV4B8D25ohjS9eCHyUafy5ypS+9i7UIaQIV9pTKnKGhw\r\n30L4JBbOsek0LEqL111VOEkeQSpkQviYloYpPtn17MrlxgeAFs65j1dvZSAV\r\nMKEaE9nOVHurvqqwQIL6XByxYCI11nOYpDhF79otgAhMys5y+Pxg2hO9IHdJ\r\n5rnc1L976+LFMhAR9CSMMK6wHrSZduE3m+djt6/lixoYcIlslxiJcq+A3YIO\r\n/kJomizrHbVjjBR3rNEWOORcJvdiXgkGfIFXSNH/aX/McSB+3obxXN7UEmF8\r\nTCfg3cYLz06PUzfvEWoeScMsWHIfGWEsyi4=\r\n=/Mne\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "87b2c11c272d9310e3cddac3ee67d5daecfe9116", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.25.tgz", "fileCount": 12, "integrity": "sha512-EccbL2Ub7C+tDC0h8Acpby2c846LW36JYwc3i6H08F5W+t2SiQkBVnZnthO35YdyFb1KmSwJgKnQ7JnLJsqx/g==", "signatures": [{"sig": "MEQCIEkcaY/p/XSl3wxKlNESLtVAEEjybqKbXaVf6Mv5ktJuAiA/2GbXVxoFk+a+Ne4bIJ0DRmZ55tU+1ZW3CwsswES4sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4W6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhaQ//eKS/KgxmCBJ4dkE9lvxI8jXdsCdE+Coqa3C1lF8fflVqi+z8\r\nepuG56zFj4WZogp8MQgVb+fikW/p40fOXY13nC9nMFsvRL/RP/hjZz4jdIYw\r\nacS1esogdpEDI3wBM/2TJ9ZlqwNd+OGtpanc8dQCDihq/o8qVGVnIu9bIOcO\r\nz7iM9SNcpuzens/OIH/6LcDnXUSGj7pjhTuAfaSdBuwnuaF69hvkGkmQUDEb\r\np2GQvaU0T9alIw+hVABC/zmt+fKpSFtgXGI6j1d1rVroH4YZWy7/ynmzuf5c\r\nrhqhfCSpfs5bJbA0Cg8HDCWr1jwNaWQOJLu5x+c+ifNPtcP3iumKovnnZBS5\r\nlMQ9s7i+xoKUSUXYFH9ctgQTnlBK00hivyc1U6HRgopMzO16cHpdZUEkihVJ\r\noNlo5uAi+am6gOgwJIxqBl7shABTC3lG1MjISLdLahCNQGWioSN0HgYVySMd\r\nWHu/AtAP6IWMoQf1EzPqOlhw53xt/RpkyDZiVpf0uCZzHhUU972OQOx2BAun\r\nv3u32sJxljLg2KOZ3WhQp6wA0nOi4A4K5lFBYw+/YUaaM8kyNfcZArUSMlTt\r\nvu9cb6p8EVFIqp0tEEN9aDmdbw0hBdoujmqyft4L4GoVtMXCmNW6xW4EOoWa\r\niLtwO/6xbWaSuu0AG3jjZqgTHxHc2aB9tKU=\r\n=zPlo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e30076d116fa54b595aa2f8a563a291b6be62bd1", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6-rc.26.tgz", "fileCount": 12, "integrity": "sha512-J/gAAO4JSyIfseJTUxQI0gZG//vl5emB1EkNvgze4dqSKtr/yIkvZ2ZqqcVG1ZPY/PgrL6NK7mOfkvH6jkL/9w==", "signatures": [{"sig": "MEUCIQC0EdQc2kmCruVhzdZ/yFFNvCD63GtO3CY6MEAm0vXFLwIgRl9WsTPIh1REhZNMuDlSLY95kpnZcTh5CpTEJF5BuT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75726, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Y5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx2A//ZCYMUfRUJTeQJD1wbj/GV5JapKLiqkNhxPYi4X3WPc2ZVyxv\r\n9farspv9q6YJ53WEyxomufWnD9czv8anSiZRuA98zhLVl8YwGY0KkCJppBqr\r\n2uUdqrbtG0J+sPp08+s+3V2LoLHHz3PlYE4I6lgtFNJhHjm/JUjpBNx/n+I+\r\nhn7aCw27IdfoF5lUwNRVSBHkQk3ZAVQpOHOLZXCYq5prJSpHFfq39ioqCOvq\r\n04q+WI25jZ/Ay5rqlL7Th+WjY2+sD3Xs4xvIp6FdIdgi8f3LqfhMbt/E5Pvd\r\nDZ6cG5YzC4RjZaKqCe+r027PZT5jMGdNR/0Ws+yCNhGS8p6fJi5PHOl5KA96\r\n0+SbPxAZVnne4RwSKKaWFPkC9XQIhyAHbTTAK0JEHg6t4cpRDixMmD5MUttP\r\nQ/fQhdh99KY+PAJ04yGTpxxmuaMFkvYrNfoA/Z1rFd5dax4mDK4zuEv3ro/P\r\no1ZBIcB+Kak1mC6zf3+JkLgfFW9xM+C1WmahsJbbtaB+jI8aEiT1QY4aIZzX\r\n299fZLwt/oKM8r/bib5Tt0ssqpljoXA34CNeEWbHEnxP+BUTUoc2n6vqrdch\r\nY719lwIHnSpH51wi/xx8xg8tHBdVYkUhLuDk76L75kSZ3mp8/UE6P30jCneZ\r\n4izBAQrBaT1uoEZwv0oSJ7ys1lLGf9fAWyk=\r\n=iGNI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4bfb9c61956e88229595031504e3472846615384", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.6.tgz", "fileCount": 12, "integrity": "sha512-6XeTHgwL/Vw2616NYRIFTUKl35HpJdqY9xcXqI6pWYohp7GhafGl13jhVEQcgKhbVQZU1eDYFh5vp4f5emmt1A==", "signatures": [{"sig": "MEUCIC3hHkf1ng4Z1PvH10dsNe+KAuFiC3S0AyuuySIxVX60AiEAl7wC1uFziAzgU4Z/FtRcg14eNNlpJQXOpW1ldeMvVm8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8j5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpESg/+OMpz2tEfDESgOjVWnXwBcjrVtOi+tuiWeIYN0JEx50sT7u24\r\nmE+dossBWzCUobN7O0KJmDsBWjpYBsDydlyf/DjkQ1gDBaXyE9EfF7WCUJGn\r\nTjII+O8KfJX1crcX3HhA5/2PpRZOHunTJLz9JZtqpn4ajWVnrltRE96amxq3\r\nLqOOnpmzZ/3FvwKkdID2MVIv6Ji8H1RbWyLg3NnXuc6KLuaFj6SrcfGuBxe3\r\nDeaAaTlAUDv/fGdsq2FfDw17I+pHFsQA7t1uHL9aZLaRB6DMscIzZkaeMMSX\r\ndSVOo8WvajACJnQJwh8NBCQze2EIf1Xiv74Ze8HpyH0cVz1bQJmJQZggkz4B\r\n55WYSxbEW8KwvIp5gjerdVmhRNt/5A6d0l1PST9q5AJnqytwfsw5T+C0H/nM\r\nOFcqvECzUAKjd09hZ1NUIucwAeD613W0408FYbNJhFmHsyVtoB1B63hH0Tv6\r\nJgq1naoFomZtW1Tilh0qUKss7fOuabQs1Uin0K8NDQRQCOBTwTUTm8o/b/hy\r\nEe3oHOw4NMJWN6+p4XCeBTng8G5sTSotrGgpHrGp4WRGaMLzX+RHM51KlDTl\r\nkMPDDUPm5JlzDaKagzHGxnwgxr7OSs0mquVk+o9bAW72nwSALuMXggdjsBN5\r\nPhRh3+kex9/nP6Qqp8lL+LWIsNRLjqgsot8=\r\n=zbmT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.7-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.7-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "00593ba43806356eb3a5c4432f9ad04076419d9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.7-rc.1.tgz", "fileCount": 12, "integrity": "sha512-UezrR4e7WO1IRHpvcKTljGCONZtT4aBa1V4g6BBreobReSi34MYOI2Htx8VuoqrLY57DpF0UKFnSDZcrM/9aUw==", "signatures": [{"sig": "MEUCIQDy1s/4ChxD8V+L6snwYvS0g7yvARkusi3GosUXOtVujQIgULuugjrQWXhGphMgWkdhtGQxhMDd7gezQbBImKFju/c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHOtAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWyRAAmecEYjeBp36bTaPBmTRmiXBiI+C+hT6ZMKxWEJl2PVzyUMGo\r\nZu+2UVUvZ8/eBvQHCkLz62DtvxD01BWbzJWBCjRHxmXRVe2kHR7RRdefIAeZ\r\npT345pvg5XBTuKa1FjUivfgpjs2q5l+pDsHzL6Lmcqp/dK4mM2Vp6DssB2BA\r\nmSSlrcOGsrQx5RU83ytLzkZfmDDmfJop+W5bHtNb5dIA3YeOmD5AKdZjGR5i\r\nEHjvGMwewG0vpGoUQfMIsLXTu40PdciNT9fz7AqtVeJJTKT9oDr/HJQON+E9\r\nFo5vczchMZ+HtKeIApyuk140oV3Y4MmnR/21ZKE0Sbsu3yGp7uqJOne9vtL3\r\nOYlC7BFbbwmb48mpynoJSGcZuY1t5/5E/gbQIWJlkAcYcJ3RpHuDqT+7i8kF\r\nCrvwm7gNn36FpOq/W/CCi75pBkREGdQZDUOlQ/At5CAbxaykKyXgJvGRPm0A\r\n2p7rx9QnAgDbgVwlr/JSGcWp9vEEGLxuqZBCkWKYjXoOBw54CLrQTq6wwG1v\r\nR73YRA4O2kRFuuJz2DmTniLYJhU3DG9E0i8pCDgB9ECewP9cAQ4R7bXPTVma\r\n8+3It7TEdytnQ3vk4q7+GNkCTiUG0Eh5Um4LejpoyVwoX5lfMs4d1rFanQLk\r\ng+Zi65WLqWjqurVQ8IY/yKkH0bsOaW9Ituw=\r\n=ovXT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "2b9379d848323f5370a8b3928dd6e5ce95f8bd34", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.7.tgz", "fileCount": 12, "integrity": "sha512-b0+TWr0VRWMWM7QcXvvcwbMGNzpTmvPBSBpYcoaD+QnVo3jdJt0k0bghwbYBuywzdyuRNUFf33xwah/57w09QA==", "signatures": [{"sig": "MEUCIHRz+B7bSXvATGo9G49c4fmwA/gXler+hEDGQgohxoFnAiEAq1819LkWAEyGd+xU3cV0RARbGttlfuulI2WNtRLh4OA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHOwZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbTA//RZhmU9j7+qJm0rEHWpoy8X35E/Xd233J/dST0jxBHhfbnzio\r\nDvzF5zb3ukJc57zQYhFYPEDxOtqe3J41lWj7u6RFHDUAD9u/XksZDJK2dN2t\r\n67h2ziXsrMK86xrLSskgg120ZFr3zkNHcw5SG4bRoriD32JQhZL9CGconLzM\r\npJ+4prwYyzcliwThsI2b6fS00Vzpzxhx84tpGusvUuLLFskJKZW+GNIwjYko\r\nUtjUF3ZEgxx7MOVE1eWiAca/fiAZSqDwKjGaQUWIEH4mQquMjz5BXrC3m0Tq\r\nTnhdsCsnd1OpMdbt+MNiUywakJify7EPw3dKQ0IJC6i99UH3oceafVfcI/oy\r\nF6x3w7qvryxePIV6RnkGSbCg08pXFu0WTOlhefLnAOdW1RA4fk6yH/roE1IP\r\nflfrwCTpOBNBD/0x1AJYeLHjqLPbyzjHbfjDNZsW3hL/t0nSCc70/t+6iI1t\r\n/xxPnM2Kdc9xDGlgBdrVxqRMzDHu8Vqq4HkGSXTpaSbOWSqLJy78VPrx1JFt\r\nhR2vj1ItnFqWV89nbfjahNjTIf9ldEBoEfXIM9HsoPw801amM6gU2c9dVpAw\r\nyWt3+biQdaPQ75CaVMlYVIqVzE9GjD3U9wWA7WBY+o463b9pzvNCibuR2Kaz\r\nzh90LXpj7JF150h3Nd6jAkgdLVHpFU3nT+4=\r\n=/cIM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b686f887ce2696d5ef21e30a15bd363645dee58b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.1.tgz", "fileCount": 12, "integrity": "sha512-gYLNr8J1Q+6PydmRZz4Vg4RHsNVb0moMY+UYHAcbus5BL8x/w8mru/CrqKyh5DmIWlG+yvzrPcU9bKm2qw5Qkg==", "signatures": [{"sig": "MEUCIBuqtv283JR3RLhGl2rnO/hstR08tSKFoOy9WEq3xVsCAiEAxKWaNA7RWxzl0HTGgMLuk18w6zIlviIpc55pOf0OjBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTtMQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiMA/+M4s3oHn+LgzNMcHI5shSNnxcBk9iMUnrl+D7E0U4MvE3tFAt\r\njcvo6iHthih3y0LBIUkNBwhljLJ0q5R/7GOG8fNAEP/UDll9gWqwnJE802vu\r\nShxM0mzEKAoemJX+5jBeQLIOBBuoZe/dzFAhDMvURpewvw55ag6jKS+hJw9C\r\n833wY7smOMXWzl4Z3aRdaniZ0dQM27e6QiLapKY+6IYpZBj8S3y/jXo4wbu+\r\nHhnnViV/rjoKRecCGT/xNe/qhGXVGKhrvoiir8f/FGECjlpqr9GNmsIX87cX\r\nZ9mHBVLHy4r3ahzWTYTdMhZillX1wciDN1FbATjfOgzb6pldIQUKw7+DzUB/\r\n9uC7K1Xx1PLBEIUmi9LvKE1ZpjxkhGh5rS1L6sW39Kp9norjqj68KR4lZTIw\r\nQ4uGmqnVmfPbynYhH0GumPTV1jz+pKmNCfjc4j13MDC2DYcG+gI8dVeGyfFx\r\nOARJnQRH9zzPwTeQX7VP7vS6q4uwozUYJLB7h4u8FcO1X/8cgk0OZXhVWYbq\r\nfxfFTnEPl1AuscMZ409cFJY2SbFMrkpG6LvUkOsL1pbWFwKFoE+uNaK/ZnIs\r\nDlbZljrmdnRL8JYYlM79uo1kcj5d5/araCe3GZnaV5VXOa7Ht6dcPxE9Bzwj\r\n5ND5dqjbdQ54NX2h8w13nEwFSMNTKdoFcSw=\r\n=rwcD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c8c201e419a1c96c4e1cc5ca7aa511d19a76143b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.2.tgz", "fileCount": 12, "integrity": "sha512-GVy9ZLZGZsO1MveIsWNKlOjl18ngKHaBnooV5EcmVXngUBCxzHILnv7HrpJu5sjpJrOa5mcjmfyM2HwuJ/xjGw==", "signatures": [{"sig": "MEQCIG9RtQ53OxY6UgAiJPEG7+xsiOdBO7zSNKdQ89baNRYEAiAwQq/f1yOcZYYUjSVm07r0ODhlZhtLViO7l0YexVKWDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiU/87ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7Tg/+JXKKUCQ0WaHbwuHVI+MLT4qkP0SpWIDrXm2eHEwRWl5vPUas\r\nm1WZi/pvlLtJJcB9IhEoDnrP5truIQRAUfLvz5PwlbSF5PvQxa19E5raWVux\r\nQ6auoVKjkuIiBZw3J4B2qVCq+Swy5DVHn+9LCj/bIdo7kDAj7SIoUuVlfY9K\r\n4CyLonA/qLhK3b0W6KhnWsSGbIaCLRqhCpr40Lw/KlYsVewiKbLfrL28wMMP\r\nnUBQkMDbXpxmi0CRWTcXi6sDFp0pndVPvbtLP00RpwdTok9VKN9AeGlqP91y\r\nUTAo25/yj78jOzZ5rRkyhOuR90lN9n925IPeWPnFVe0EAjstLyOwivC90KD+\r\n2ABnN5P2gSSQSBZBcxNGn872RsooGFW24hWGKHb14KK2xQct7AZN9GuIv5qb\r\nJPiBLmr6t0t0bao5azdOd0koeqMXXGTL39btXG+HGD42qyY0uQ4eLAI4erp+\r\nb1Hs0+OCtOlFUe8hQDPiy3v5atEyWiNF1daHIuMHk8Kg7Qj6SYteAGDQkiHl\r\nEK0FOVNMJDsVBU4qijgfqf2aHIZ5I/stHU4fDZNwu8Dz6dByrDqZK0q1AH39\r\nvzDlLxbGWRjOOd/OtfioJSdiWWubXht2COa+seHxl9LqBcYyh3ra+uCNYDkF\r\nmWhhOsJc3n7hCVSY0HOEPnOSHEB3MwujQPU=\r\n=XE97\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "90f89b872441fa87c787deca035b53ed32b9bfe4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.3.tgz", "fileCount": 12, "integrity": "sha512-S6w/qZKVY05fDZ9JWd6HgUkblzFmu7XOhsRR1zXQ1JQVju3l1MieCAV/tpx+VryC1rofIs60SCdYJSJEhQgyYQ==", "signatures": [{"sig": "MEQCICx9Ku+HnCtp+YQPXFK2oX7vqvMoqD3G9jZqBTY2R4ODAiBVEPEuXdNkSisttaIcFRix4rRqRv9nqOxP2Z8zCFC1Lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVAW1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRpA//efXn9whwvMopdn5hnDeppjy+wQoH15aubzfmxv7LRtXoCmu8\r\nY1fLp7b4p6tpXAwAYpEqo27g+ySwiykl1Ql7LkSNcqXgf9tyK5CnN+VixTqo\r\nJmMOx+ccaDU+SNUlz1KhMnvJt7p7wXvX80+8MN6SALa/PQndOV56/m8pmK9a\r\nIu8nCr6j3JFgV7EwTlx4L+HF76OlhiDXTGJ0hLNfAgA4CovudlRINFL6Z12j\r\noDcPHaqP5dWRKii0AaXYgw+mHEZ43tqyG3d48Z/gAZKcBlXNj95qiaFvTgtc\r\ngN+yiM6YNMQAc2609PTGltlkdKxsU3kSv+WTtInK2/R8NEtzxfN59O3rIt8A\r\nLartmjZFibmNvuWgGNYtFZmzCN+okq+68fXZLeIaNrgHmio6JEXygu+EqVh4\r\nldOna0OknGfkH/xu3VQARDkOBROJWr+UqoD7C2wQLnvJDQWTiWwDQdxxyZJv\r\ngWxvWaqnLN9CvRjpMtOPC58WXu7rgcGCaRGUkVOHIFWbX3FvWc3PxNahsGTv\r\nxg+JW2BQHqV4l27ud9kc3HlTWQCITZcfWKecDKIMeq2W/bJ5BVNXgofAYfEL\r\nqUd3RYnmd1Ox3qHDoNWjW11BZ+9yLOt3xJ80ejFX4VuWOGLxlapye4tal+AI\r\no3QJee6cUVBxI2oH4Ei20tX/37ttxWSf9y8=\r\n=Fiex\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "f62eb5da986cd393652e0daa8eb2e44d6a81ef61", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.4.tgz", "fileCount": 12, "integrity": "sha512-b8ZCx6ePZ0VP1FBC0peY3csMAY1bf0Sj6a/JWpKPGpFINCcrfmVfpEBegqi6t0SjraFokL2Cx8BxH+hrvFZIBw==", "signatures": [{"sig": "MEQCIEBP4+9toF9OGbDB1vHyIbM0IquhNo2cyc62NGULcnbYAiB4Roc9SCW9pT3o/7c19ZVuk7vXW5GeHpw3Iv8v+EIW9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVCwHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmojag//X5gJhl8rN0Du8QkY1S+t8GlUyhZHYpqsRsFJXZLDb93zK8TZ\r\nPwcPVl9vKtmQUbYQeP7WFohn+A3EgJ1XM3x3sWgu+R1tGgcNOD3rfpeud0bN\r\no0GY/LirEaQQ3DqC/OrmXAOpdEmKL4YSXr/xIeKf+3fGSIJDkvr7UL/TOnjJ\r\nxlpkjdHFEHxi485x3oodTCCCZEN07+sel0283R1Q1qVwQxY3FdS2PKqzGmYY\r\nyGPQaDR6vx62BbEEZjfThrQ/B0oWGEriMdOVhiehAAdL/H4F1TOCCikG2t2h\r\nL3pu5ps/9phikWtQjFhRI41ddkPLtG4ua2Sq/i9zV2OrVz258PcLDwVuVWZ4\r\nPpWiMZ24cwjioHY6EQir+d/Hf3kpScixqGFWSKrsJESaNAX8y4WO69e/YzoD\r\naW/n4Jp+DB/+2yKY1oahi5eFN/M2R/vL2JVcb+rUZxy6HWyCpuvwvrii06J8\r\nIAZ2eUfkDC2jejWEkPx9DKAttm1AM8vW/tomc+Mmjglr0k6QSuMemYeOejfz\r\nAAtiAc288Qh74g5fRA0JP/4XjVjAVOOoHTBmTlqrN45soo2okRo96NRWpcHX\r\n+K2ZaGs+w0XFh4nI1I9ZXeUc7vBb4iPqrixPCFNAdsWbeA3tgTysl4zfe4cY\r\n4WhuQXAG5FcV9Va/GZZijWSRP/QUQZuOAJk=\r\n=H9Dj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ec36792b4c9518db62e29b9fdb27a6c03f38832c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.5.tgz", "fileCount": 12, "integrity": "sha512-rMI8SQJzdly8d1atxQ8lb5P62t2CosSjQsa3dvWsO+1Ie3cIxRKpxMprnEVL6yrX4PoicZygHFeoGnpHssQX7w==", "signatures": [{"sig": "MEUCIAa5Y7eKuo7i+Fd6nW98kM4Rz5IB6IcQlqa+JUzxGvZsAiEAl0jbzQQe+IEZC6sU3OrlsnzZUE4zy+X104tgY9s5GAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD6bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9vw//cOjU7sFflBL5aDK7gLfb2I9+eDkqho88bBiHi5URPtkfXMNb\r\nAoDvTly/WYM+KZSRtmuB8kSanxlbVHmBV12HLtWdo/hBVQZqXTkf/MQ5+aeO\r\n6pI7AAOCyx+003K6mqplfW3uV7LqSbm6SqaKHiWk9WKGDIi7IqZF9JnQzoHO\r\neLY9kPMOPiW26cMnbXUwM4mgq5NdBTvTUNZ6Vej3LyYCcQgEtbAzje8pWo0w\r\n93nHNtHHB7bSxXJcoeDQ0IzLEnfSgpffBt+ljYOUhaPkfgg1qJXpw11yOBQg\r\n2cQoM5PuN2hTFZCAgU0Dz9d93rudimyvHwA7jfsSXWRr5HUnNVICELIm2Kp9\r\n9nnvfwq/86Eqyv5LxNGbp4/QcU5YC+eROZwRZJPTURnwmYKF/IQxfUg8gEdv\r\no/s/pv4HVGYV/C00edFzM/hahXeo6OLZcI0BjDZUq5K6+6Lzw0/1h5yceO7i\r\nqdxnbU6EYiRyX9eWP8Nyec9HAzLQ2bkmXp3N5OHTOkUrGerctZRoxT4eBZmO\r\naYbE6qri4W8FNQkLiPcy7+HPwK65mmhuMvNCi8hadKaaWZHKLB5nuQAYQGmC\r\no0DuvB5CTFvyx1mc1WWXTASqXwf2CoAcBEBvGOPY2obr3JwHBm1Ok4FUeI+W\r\n5wCm1kU1ivNznd4eSWIWRcnk81dm/U871oU=\r\n=McZT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "48a1790aa4dcd0f81c2954ff661b2ab759f47501", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.6.tgz", "fileCount": 12, "integrity": "sha512-zs3gw8WW8feRG6+in7Lo50pclJVi5W6379vPO8onNoc5xB6ySKHc55E9Pwm7hYMIHUqg8kw1ySQC2zv/h/npGA==", "signatures": [{"sig": "MEUCIQDXwxTKgX8xgvsaquqNgOcXC5bm8XWdip47jl6tyjtblQIgZCDxP/dzkv1spf+31suEBuW7yOHVe9St8GFffdffgYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEHhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJSA/9FZk60j4ps5s00g2w/g/m4dRtj+Mfw4UiIfCtI57pxRUjuvQW\r\n1t8IaBIFJTV7l6HXnxXNTBFW+ZsqIGgDsN9mXLyRwrGs7OTRlHd2o/3Pa3tb\r\nJYIDxmbAeCpuI8iUpIHzqI0pycdfw3a6ZZ6BMKv1SDBnpCseWFl5MHDjqj57\r\nAo317bYsU1Hu50FWmovd4Erd6zB5dJvehmAfKjVme/wPTqQJvzJUUI6L1yC/\r\n2dt693DR68O+aPJL3rJAHnOuAM4LMU9eAL//hQ327U/ZyVc+cziy7bDB4fy0\r\nKJgcrFn7anNIs10/yZqv1V3uGgcTq1fmjlhse0X6uyqO1SznWBlW5k2vLisx\r\nl665tHejXWGzh2iYOZKN3UbrpQzS7Y46yK8xLvoX1S6Q/bfQ2QSOHFEJ+Pls\r\nn/xVRI68puUJgg0QYwq0Wv2QgbD/6BGaoo9GhjLuySEnlUDsNciJwWAvxrZ/\r\nrOHswdD6d3Rs4TigXeMkXZfO7rrOOk+ZAlhEBibQwdvhoFkl7QAImrrcjuaf\r\nM9kIX4z92yfYvI8cg6sHih9ZMmDqu1YxDbgvdSI+Y3mgTFd7YJlbAQ961Grg\r\nNNAq30d3SMw25No4EZgU3OO7RdtgJUem08W9FW5NMQzzkKhqhbpHludPi2If\r\nzEhenE4QMyrh0dC+ncg2wcDnxOe84GLdf6o=\r\n=qMfZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.2", "@radix-ui/react-dialog": "0.1.8-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": ">=16.8", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6b5975583018448c22c50ff68509e7c5da4a9e64", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.7.tgz", "fileCount": 12, "integrity": "sha512-fcePAh72WdJPSpG+zGNypcQoSaIy3V745KseBk2x3Uo+l6vdS7Xqf1QEQSnb6HtxV7EQRfp0XdFIJxNNihIpMQ==", "signatures": [{"sig": "MEYCIQCTTz/5HSM2xIwSRBaaqjHDiv7NkvL7SyGhkbP8hzhqDgIhAKPXXps/2g5NmaxkfbRccnggvQrE7XSSoKO3xq8F6CfF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUTBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoYZhAAljAj2Ca23j5cFx+w2EoWpT5bfqigDUTHIOwbGBIHR0UGFND1\r\nFt4AX4yHV8Sq4VShL2AKdc051rEbEOlcTvYWnnTpFtJ2Sy0bpAm6hQxMXI0K\r\nDH1ve2O1jqxdr037FTPqWh3lpnzlI9dZEMOYc9JUAzRewKMH+anN5LA2XkcE\r\nugcWiHwZVPPmpKzsRqvOBadOc7db4WgA5Ni34XK+bZF16DwmZKAXm9GTtZym\r\n4ACkAEJGCWBA63fVrYzwxeBLi1sKR+1G3EDYXzuKz72Z6S2Ql+Lad8XcQoIb\r\nTqDKAAXQs05g04uijgDOqGnWDIM6DysyNXmWf0oIedhhvp0eZYmv7hx9AOHB\r\nqBEYwfnNXoHwK6UEFtETiz20YRnUImKIPhZ0Qay3+waKUxe3pS/k2a6DM8kz\r\nkH51eQyK4OrEjHXhec63XG8k4supQmy01KuNIyEtFi5xSmzgXVWKlO0SPjWJ\r\nhWQXrevXg2EsXfPo1RdjCeDptb5r9IuX3A+MGtb7rmjumWgqNgnPlE//1Nci\r\nnOAnRjrUWFVPAAWO0UhjZ2YtUKOABwfR+GTWo15965YDKoZQ859LVRXrJoZr\r\nOPPMdNvcj0vF2vsz68+YfjaYtpEowBwgrYTr6cJQoPq2Z0tRMEH+PVUpwYCt\r\nbcjuUuLcrRsKV7oXmVDtGz3Qk2biZtslZpk=\r\n=g5P2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.1", "@radix-ui/react-dialog": "0.1.8-rc.8", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "48005ee4568009051a2e8f4c7ed2eb56ae1844bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.8.tgz", "fileCount": 12, "integrity": "sha512-UJd7/K5hKixr94eTWLtFP6t8ZwO6VPMqBXVSzUceSUZaMkbTn8w+dRBR3Z4SZpPXxfDM7wPwWqYbZdq+rd4NBg==", "signatures": [{"sig": "MEUCIFENZR71VJf2qynE4c+XtafBANK6gW+2N6MWBMUZqgCFAiEA2dDd4ZcD9v8kg/X8WN0CYxQQYKG95hpn+hgvtT1RWxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7uQ//apfLki0VBHRLhi1lX16yCNxftcKo85aGRFluZC0Ok9mVx54z\r\n11OB+ieXwJbG2wrD8/Aq96rxUJKhyVLfaV9q1f88hmaCh0ZACHMJ2k7nBJPo\r\naBuwyGzxOTH0tpwqISvQnY7Iz6xlUzO0tKvKR1Ecz56SdppdPphiR3Byu9JR\r\n9j12Fmoe5RBti9MKLCMe8tgubwpZ9+C3rOsn9H4z6FgdARnAqT9Fz0n4SOr9\r\nm9RZn4kHNWgKeBbgJvmAb2oV4Y8Y4KYPLRuROdtHFYskBPWCDEQp10KGtb/3\r\nXD87lf05yhIb8eFOSxv6jNvoch6yEQkz+mIrCSG7oSssRXGX7zFct/zkFlQg\r\nxgKnGWBt+uu7pGzQv6kx41Um/S8D1BlIe9o9EAmYlkfMRyhNz+z9+Mocc7iP\r\nvBmnvis2SRDab/KtqBSCcqcStStjDotaKEOjWQMd5I5jgtoL5aGlt/LvqFdu\r\nAfwS8ZOvaFfoEu7skJHnX2buXaModKZJpWN83b7IIBrCfp015bESrw3CJ5sR\r\nLS7QAKGnHESNg3b4IPJuDTIGvYX4B26lB20jFEJyDSIr45Nqu8W8w9lmR/fm\r\nzn+Bqh5PPfNpZp4jAEHbhI3RZLxTk+E+H2NW0z/T0F1qWQavNZwfu4sUeAp6\r\n5lQjGNG6UkM+GK3A+OxJsepOQFcdP9sFWe4=\r\n=vJaP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.2", "@radix-ui/react-dialog": "0.1.8-rc.9", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "14b701f00de12f96a87cc4ba1d51f0bbe9033b8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.9.tgz", "fileCount": 12, "integrity": "sha512-cBJtc3xcFGDwh1pNzu5xo+sgTGU6lk4wtGd9UiEoJGndVWs5da17isdSbJwfyALTKvFXqk0dbXfAszisQTu/dw==", "signatures": [{"sig": "MEYCIQCIitI0GMDV57C49xhK976Ukbmj/ocepvkTD2Me3GaGcgIhAM/fV1dOIwZmI2TwKWKUNF6VyC/DiUM67mckwnag6vGc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75764, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoATg//XLWWh7Wf4Z5anCEC+L8GFM/pClMCvKySnTFtLfo8AGYlfBMq\r\nR4xXFe5hTDienAguWdowgNe1KjGZP9WG9crOL/RrqCPmrpiSXcNgv+W3OdF3\r\nnvnumliAFfqbVCp1d/C1IiMWlr48Zqq4UQ4h/7J1t6VBsWjzELEEWvC3+YrR\r\nGV7v7fjsY7QGSnF0/3uIGUb5w2oGCaV8mJ1P02dN91TXU2IlvJQbD8Oj/3DT\r\nUXXoO4zAxVuPODlWTsLEIpEdm2o8MuVbYaZiVIBIdPDDJmqnYk+S8tTerCr9\r\nUt48BDFZ//jy310vQK5n6dqSIg/+hz1WNXC54iKIz8qk7WGpDv88M5tocoQm\r\nN5c2geGwcQfBBNMAWJa4YGYb5F+sltSSZnB2xUgwNxDlW22UIbQ7IvsYJj/1\r\nWgi30msHTwFs0IHQ3r6wEZMZLGE0LDxhXXunQYQ4ticjBXHmYrw2Xs+rVVoa\r\nBet7Pw98EdOyTOLTRgqj4gdqX20nrOYM55xsjGABUbPAuFJdP8fNYlE6KN5p\r\n/6gT9fEavs4hh4Pqqid/RGUnHoE+AX6a1YZIGsgfzRQVaTSqEeo32rL+QsxX\r\ntjUAi6xh46iUtQqHJ9p3jzhG+umnn56LtbNhgkDOsC0fh3S1t77Zyd/xwExG\r\nZyyW7Bx2Y4V8KlkupXYoRHwG2OV5KooXdgs=\r\n=C0qT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.3", "@radix-ui/react-dialog": "0.1.8-rc.10", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bd619e4e364284b4c31ed1953a4e9a3c4c81ea53", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.10.tgz", "fileCount": 12, "integrity": "sha512-TfIrVffxdN52Z2R5Us3M0c7DHMW63HebwzaKE9D/XMB2TUf5uuXMxYASi2GGUFvswkcv4rtKB8urmduYEjsrxw==", "signatures": [{"sig": "MEUCIAMNgx6gYQJ4g1RgtQUtXFs81CTk7YqOunaup5C32sRYAiEAxqr6TB/64lTtHjne8aZ+7O75lJSrayUGAw4EzZWBD5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9Yg//dCpF+nDM760ROkQIfc25ETpwqukJS7NftJfFIpAdi1uU7imM\r\nLLIgeebPr6XWWEnlV4m+hmE92wiHVQYxTLyZDlKVZvgeT0696950gns5sRnf\r\nlCajHajHrA6ItAXmZ5e611YSksMgrsQBvkCwJVmmr98TejacBIUXVSHql1+K\r\nE4jZi213BpYMXTjkB7UfaKOwhsOnikXsLEsLvtl3Ur01CTewCrrRf9ZDCD5o\r\n/hrz9wkuzoMtUJDJ8lafw/2p7Gu8CNUlrV2Wtcu7QUr6W1KhnTiSfbCwHOIz\r\nHuHXmizn/wTrM7FdU/7Tu9cl3kfPFCCG7y+vvQYfUjYuiw5W8iM7J7wHWtD2\r\nlEojcDhyljwzjWp6WXF9V+8noAZm0E7Sz4d7vX9RRxiBx3xVbXbgF082jyyr\r\nqPNHdRZiPXvikfQB1995/tDxB8yCDb4nv8v7yk1ZYC+MC3aYwfPsjbjExZ9f\r\nb7CSNnyFJs7GmCyP6lxTpNrE9alucVKVmjQ/HFNOU74cgxzUqpwkiGUVnl/C\r\nIZV7hWllGF30BGYybMTYXnzlKDYtd6kWp6GExbGrnrF2qVbIYENEX+9iwuVE\r\nlqC+Urd2KCye9zRHtVeRdVzf9amWh9p4Br+oWoJF7BwcNdLgQ7rTFEkioH25\r\ny0IAkLa/IHk3LIwUwf6hfbuWYyj3UJDEbm4=\r\n=OkFF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.4", "@radix-ui/react-dialog": "0.1.8-rc.11", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37011e87af0f0cbbac50fac0f7441600f5443c5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.11.tgz", "fileCount": 12, "integrity": "sha512-dH6pEryGqnzLqSIPcBUkfpiDM5ZqnkSMzt0HV7knBur58pfPL/WI9MpZn0sAEaFX2IEo/C0aauYINUDyvJJmRA==", "signatures": [{"sig": "MEUCIHcZSjLa5fYuIEAc96eafJBB58DyDAno2du3SVfanvh1AiEAjktIVV3EkuLMFJJ74V1I15sX8PEy8n2ceyO+3DS2OTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq96Q/+IYAnWoODn9YtM8D34k0ObblvY5KLBYGdV/pz2RvwcuhlRHad\r\nhSc8hatIDh+n4mp/P0cJd01BKqV3eGVCpyHvvLeFEO319h3JE95ewfAJjkG6\r\nnxU+ypMW4j8mGpaAwB0rSs1MTmEJq1p81W08c5sTqfSn+TuI+RpWK8xl2MZr\r\n4y0o+zIT/c35d4B11vqCq67z0EyttFKRp9bgOHwTYvXELLTjWsO1lyteR6Ay\r\nUMVEWPBZTOOUhBvH8ERtJrGGhH73dkBbog3TqdILtKQbj5PMG2SuGLF2+RVu\r\n+UdFJAdOy5HCQclFjqUzcWtd+UcPs/jnHHR28vrZcuiXS8t2psOCfWag/Hql\r\n712bqO0feUCQjkCtIqxx+UZm4OZvzlIduv2RurcBUDgRUYDk5/+rb6i7QQEq\r\n1n87O724N9QFOTYsWLqS5PMi04G+yndl742HEOEfvKEyjs8KS03zJdGO9Hum\r\nwNLHOA6YiGNnBlhBnM2pr6lyPy0oaMiWi/Er67KdXdn/wRDrvybS0/525xBg\r\nF4Zhbqgfr5HBlAx89s5Ec2fexnl3E4mf3xZFComlE4lA0x21LQoT36jeMuId\r\ngjLqb5Bmu0I4o/V5zxEAS3TzBQBLfrRe0hzcMzRZ0ZPd038VLliNcilEjc//\r\nhuftJOgQWSv2a5k+9CAkNI7NehSrtfPQW5w=\r\n=hwWM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.5", "@radix-ui/react-dialog": "0.1.8-rc.12", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "71b79712d05711812251247a08776641e93e59bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.12.tgz", "fileCount": 12, "integrity": "sha512-yE5jwMthNJLPwzxBiJ2U7dVrPEOtD3vdegTlpQoUf8BaKCxP/06Pa8fW/QK3mRqc5LOZpnMT41UZ8Kg7b9Bq2A==", "signatures": [{"sig": "MEYCIQCHs1RmmA9rEjJJvZCS+aPDAeMrieANfYHf9qt3sg4MswIhAJpwGxyMHkYzsX8sipBsoAfPceR4aGuDdJMUX5danCq8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapf+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYNxAAhheCzofX3gRlopntlPqH8QgdRPg9KGxxPz74gb5yjyq4FXBY\r\n773kcK8ImrkKMmDMub9EAVkN3JoaiWLNjmyHGPAVi8RZPezHRNkC4yIgPi8l\r\nLitCPRtzFOqprS8PPLHEuYTViJc0r7cmke+udShd7JaNqR3p71PKGs+E46xM\r\n9WQEM5EWJbEqDuS6q2UZ58uTcd4CSw3Ty4j6oFIxhUphgVXhsZvN6og2n1v2\r\nEn7XYNDitzJwe/lU85DeXQuXCVb0L6L4zN0S0xB47Rpyc/z2wjimSPHS/2tN\r\n68gV4rbCOMDuKdNc+4fTjshKAt5rxMxxzt947z+J15IMFXGmGVlITjJgro27\r\n+3WaJecg1kRPn+8nmDdGUoRJTFVnxYhBAKpvak59Lxex+kwnZpFYgeumorlQ\r\nJcgI8j5izjzcVm7l5hFq0cDN3vMKGZpdJQpulkyCiPR6gRU1CNByTxSuR80j\r\nHRCKA8zvTregtfafwIBotim7LqhN8inyrhMgSasq4YP4TKWqgXj7QsIduzma\r\nkF8eecAV0HqNbQZtU1PtLbdvbTjTGNca6wVI4o5IMoNXDgWMU5JTB10nVVfV\r\nnsUZAlIAsIqHn8dODBHToGXIma+t5ehPg6PNWeEZfti7Sc98fmubIIM7B15E\r\ncQVi5LVSSI9YR1BLy71t6ZWmTn6OXX31yts=\r\n=Q/G/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.6", "@radix-ui/react-dialog": "0.1.8-rc.13", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "53b58dbe90d5fd05142a301b666014f840ebf20a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.13.tgz", "fileCount": 12, "integrity": "sha512-pL1A7t9gma16C8kBL+TmpW+282e7IL+G6ciPBPJa2pYqVLjCPXYzPGniylJI+kiACHh+m3nnEc0J/M5fpps0kw==", "signatures": [{"sig": "MEUCIHUe+sUcXFcDSX8iglt52YvpUw79kV+CYXFui3RKeTuPAiEAopktuo5nCODxA/8CbijUfAemZs6ZaqhLn3V8fcO08fI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqffBAAmhYV4s99apM1Y0xgQD/MpfRt7tH4rqp2xLoUKuIDYXOSzz70\r\n2OnKN78cYDsqCj5zSRZy7+QuOZwTkmqYZM7WFLNsob5UdBrj9USplXX0iI+j\r\nnqno3stwlUwObGGTGHt6ihTXEJ4+seCO1loZJIeoGqwAf67SoSNqgpuIEPyS\r\ncKoSlxcmIIlaAzRSTRlK7YjwBXo/AlLv2p2mM+vBq360Iy7AuCJfzsY9rMpz\r\njWtcFCIfsF6B6O1Y86KDT0FoGx9v0SrcNdSVIRf2rQ4w1CFnzzMthIYfcrfA\r\n+sH8mj5hnakw+f0+NCpLVZgZnPHSEY16IWZR4nsBLPhIRp43A7t8jK6JnLAN\r\nR1bqpXisTSxy1ct370hJ87vQZKMvHEa7P9VHAsxBow0IDfzhe3PdNM8Fwnnc\r\n/8u3c8b8xFur3rB/8PirwXYH7/BRr1JHdFw0Mrt/f5if6vbMd/6AKm5JhsIP\r\n/Cytm89/nWT1QWqM5QQixlqz+rF/fkznOLP1HMLqvOtUm/CDj3esGTbWo3aa\r\nnqUNTXjolSRI/gWWt9tHMedley+kIRkZzAnjGDu9O9YaEM7FiQTQQTGzGbTm\r\n9it+8oJPoC6FMxUQmVpQpaJeXrrVkn21lcAqqR8nfZX26JO7/QLhx0vaZBXG\r\nICSUCC+huVIDYUsd5EwLNOdVPYKSHUqy8Zk=\r\n=VB1O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.7", "@radix-ui/react-dialog": "0.1.8-rc.14", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e1bb0cf34e2681db13207879d6dc1f00892c0793", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.14.tgz", "fileCount": 12, "integrity": "sha512-HyMaXfHeY/UfVueoNJz9YyfoagJCjucMOljK8uDwz5sq6/kxfbNkEvh5h0/EMhAx/n38JnLxy7AxDy/fMnYtmA==", "signatures": [{"sig": "MEUCIQCmdY+Zbt+GUwTgFHN40QY7OO1G/uAXNr/2FLa1SiYj4AIgbVf1y4vTC8eaNx07Y50wLlhaEDUn3Kp7hKkPTe3DqTo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+TQ/+NzYd5aytKJiO1FaG4iFY7AhikDoxron87F7C1M9IUmkWLruS\r\nnPwn/4bjjodTl+jBmkIEykItFjFyJybT/CQeGKsxKY516lUNiR6kbfH2pPbp\r\n4J1PMw+Tfb6Qb0f8FAMfnJAiCfEkfbUFfSF67JGIZqjwx82vvmVP3d/qQQWP\r\naYImLopLmCEGkHWdFD0d5GN2fGA/ES4bHJ/WpQDDtjuT7CRLiKolyni7lC1C\r\n4Hv/4dYVZ+tCgeWGx7YCjFWieZyokNXKc+XrZLgNpEP0dtDOYjj4bjoiEC1H\r\n/4BOsczfc8O1V/lCHPAlGLwUl5/6YixqNLq7n1Rq1ZC92LbPkW1p1OVUNisM\r\nd0rQkHJ8fS/z1RufRhl8JcPcnwP+Zh7PMQDL/OCglsEqnD7KRDLQ78lhK7nW\r\nRpW0RflFrlSXXJ1f6hswnjobg7Xc8dy8yyj9ZWXQxdVkjOX6saGJOmmI8UfN\r\nDKBv9PqpCJPCuj/JjLfly0+SwGQmnIH9qxl1NlR4tE1TnV+7haNdEP3G5Tq/\r\nBX5271biSB8zEk+H1NAAG1aMpX/QnrjOKGz0w9xjSsNlClHckEhhgKHsqSsw\r\nAnQoVDuRQAGqBzOQIFsD4dDsiD5LXS7b+Dq1hZMfDJ5fPrJJV01z5nivg6Cq\r\nfAzYt5JboZwR0TCGMlufh+unngLAL4+sgNQ=\r\n=tMLT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.15": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.8", "@radix-ui/react-dialog": "0.1.8-rc.15", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64017f32953d93c2270e5a13b089df947821b76d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.15.tgz", "fileCount": 12, "integrity": "sha512-nAOorfAP4+3WwhPNJxQxf/SouTYSJeuI3PkxCZoJVDNlkaBDV2Sum/uYDeJBsnKMadVqjU1VbtlA7CagpCES0Q==", "signatures": [{"sig": "MEYCIQDVfmsq2b2+vj+p8dtePaNu0h4AhgjIlNHxT2bluMdh1QIhANPH0WCp+IWfSaKMgPTzlgUchvHkuWGRsWOoBHBL6YUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsuRAAjPYdRe/6LwInXkNPucjuEdzwgLi1xaeA/u3knhXGasRMQZOH\r\nygnrVkdJCUVddIGuNUArgQa7Tj2upmZjejHOZBwVyDGOc7FpoP7a8H0AEYqv\r\n494ASBS/xXmWzgrWUvkclE8JEPrfhIV+8mTZy9oWmdhbYgoRfSAVwQ1FhYtb\r\nZaL4lIXCy9zNc7rJF9IyOSlCw4MFEFZpYh//ENNaO29sIhYuJRx8snEiSg7W\r\nm9EbmM+YEMwwYAlhhh/rQL4aBoR7znku+nopSLmfX95gqHMIFEgLrJ+yhnI8\r\nsigOVd42Eqt0YHO/Iv4v7xhRv7vD2PGQLy56uPUkg+Ub0JShKqpwGMuVnFiF\r\nuJGg3UJ7CNwQO9OzWzGmYLgxCc1MDRczx0tIqssJycLN36Y2DwhzoJDSuuPB\r\nHybs+vHitXmuQkw5rTdKeD6b71FR/PWTfrmGuOXJe773cjsmtcsx7mvZJFRq\r\noQAK45TRcFoePF0k+2cXi8pXickvx6dY/So1nK4R6W+jrTbs260w4L/9ISbb\r\nxiPY1GAGSgzYheei0Q3OSU0wfFNgaYoNs3lcKpmrDJqoc8dLMkspo4RQLSiO\r\nj0Km3WlXEJi6a29IyXAh+qveTrB++RJAEAEXw1tzBF1uBz4hWuc7uFTi0nVi\r\nlfRjJVEKyb7k25JwVYeRQu3D1+qI9yidNow=\r\n=BaJz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.16": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.9", "@radix-ui/react-dialog": "0.1.8-rc.16", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b548184ccf89379bef0643a262cf1442c7f0700d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.16.tgz", "fileCount": 12, "integrity": "sha512-pNBOTmpBKzlRmcXJtArAjSnpxh1VwiUBD9LB+0Phd3tOmFZ+ll6R3jZhNTuZBDJgg8Ff6if5LUlVx61gE6kBsQ==", "signatures": [{"sig": "MEYCIQD2VOH+/pgRI3xR2Sg2uKsfAwOFFgJRwI8rwr2zGpNWCQIhAMzjBKdGaNIyvocv6sOCsKe+XCrKxHRv33F+jm/czKKC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5NA/+PLEvJDphilvuDU/bJ1Gs3HFcln+CU9YLsyjpnF8/4YK2v6vT\r\n0czGxeXM3EHQ8H6z1p+Ku6AoVnkvBB39lXSgjjXMuIhLuktRVxnN//IDjvtI\r\nQlkxXr3BCzd5X4hMmJfa/TsmIaVaGdRCGHWRMJjmRyWiTl5UEP3eXx5XdGAS\r\nGn+sBIbUSsagPRel8ZTY1HNHhFSK/Hd5rB64a9QWueu8avgv3iJSHyZqkZ7Y\r\njtkQhgj43f5y3efgbHLFVkyzxeZJgXTU7MWwM8kkL5UZjhWMa/oClptkMqng\r\nozsKDBdlv3uo2QfIhcCUxcyj8+srjEB7ru9wEgMrUs8m+zXESUl00JyDUDn4\r\nZE35D2ePEidR2Ejs1tPEFjUDngUjC+yX54u3KT3KqxyOv+Da6wUSz/Y7ZVyH\r\ntUGCGhOqh/tBmbht3woiNJ6T/QSGWvyStTkJRKy3ToISBPexQ+sJeQziQQjF\r\nMwj5rg67n9gk3lTatvbOA/n3+n8txLBuxgJ/0rTxTpHKaZCGBpX6evkoFqav\r\nLwU78urNZ1paWjA0Qq6m/mT+h8XyrofZKUi/VVveRXtOEsHwstvjvXjK1Bo0\r\nCsCL7Z2IPexnMw6tHyrIGcG+ziDlPhMFnFXmDcbU0kfd8jxKYtAff9jkhSky\r\nkJ21qg4mb6KrKpSMVBRnaOzfchm35lzYZh4=\r\n=VEam\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.17": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.10", "@radix-ui/react-dialog": "0.1.8-rc.17", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd71987d5342e150092fac2bc6a2b34a71026f5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.17.tgz", "fileCount": 12, "integrity": "sha512-lRhXvtPvLA3Xw4ekIVXjhbGXBSc3rvmVb5AgMbtkqaoWHnvxMOF4LLz/eOfvqW02NhaoUEDphwRW3PAlYWgOCg==", "signatures": [{"sig": "MEUCIDoQchdiCn5r7HZayUlolJucSQWME+9M7a7rgu9/yaBeAiEAvTW/rR58K6pE5sdj0iDN7Y/GV27/FUhpG48gTp27xf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoofg/8DemviVNiwUgkX2aPm0oUUo85iOgGSN22kwZdBjtLjlu8FaTo\r\n7MzpypPeH5r5wC7G+mngUEkLtDA/ALfdjdYoo16+xnzz+h/uBHpFApvZT7R8\r\nX0aHY18sOKVI/Sxs0rjZU/lqoMN74kVb6ArvrZwl0+ISSGfWdiJCqkXp9aTu\r\nfmSD1A3PY7gwTq7mYzD6rfD6KzfazO8/grElsHy1VTxN0gtHiYn92+ovD/y2\r\nG5U34tHELnRcjAsUt9b0zHzDRgA+c4gf7sAMVgj1q++CL+l9mthrDMNlHtsM\r\nbi+E3alOkjaIkEYKZTaJ1SXnXFHUPvXZO/btHieZeCubiBM3usg0OjlCB92C\r\nyr7DI0CMB7Y1wPfaX7S1PiyUFphlMjW8O8zgBGxyRlRD3ZDTNsSB3qQH4NOG\r\nNsE2GFto473PA4b6uP3RUVThI1o/yMQhrOlvPu2aYr4m9FSR/TBU5rKn/PaN\r\nU6Arjg924cHK14s0P/1llbgzbcJ0I+Y8nrdbE8EnWsdutWemms24mdGBIDdN\r\nMAIa2T58W1Wfy/msEI2gaNu57Ccdiy4RJhZtZqUQN/5q8wDzhhf+wc16znr2\r\n0a+7ndaTAZGVkhLBiVZeMmBGh/kaX5C9z4BEBdERQEXi9t4O28qIoltfy4hv\r\nWVbG89uF8/rEqihGP5qACmKhE8VYOT0nUDo=\r\n=46hg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.18": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.11", "@radix-ui/react-dialog": "0.1.8-rc.18", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ccac80d05d32da37f9420fcb34608fc3a7f02d34", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.18.tgz", "fileCount": 12, "integrity": "sha512-CNvyRuIBULNa2m6jrhm36ZAIQ8psf8YFX6f/Wp+yuNt95W7gCR3wZB1lDjUBmiFLvabnOrQgWCvDrPxMJ+8URw==", "signatures": [{"sig": "MEUCIQDgp10FkOvhJXl+ZyJWrmBpnL5tvgi8fowylG0Wk/OyMQIgPCi0U6KBcwVjQr/Cz6piEukCJMBVe2PNO2HOW/ArJzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSkiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKMA/9F0Fy1KisZuFa6XOfcFCYzCpCGf1fPv++D4/6DzSt8/lm2r8c\r\ngufGxPDDRYZTm5DQNtECGd8nxvjPqYH5iARs9LD2XE4eAuffef/r+6Z4qtjU\r\nxGI0oCAGMcSlIVtvN4YKFsoz4dSFu8SoBkh/GIHtYUDf0bPVXuTxnkQukgWS\r\npRSuGCdgFmM/Y/HjCNgpTMjRTLdFSilVHM2DXt6FUqiGIPGstXlmW2do71xq\r\nIHFncRKGFLkm0PhK/IOwmx/qsDulPzQJe5+auXiNshC48q3LY3cg3w0TU8VJ\r\n6IBzCDgkDccCr9dFE1GX5C5KJ7ezykm2+uWxtpI0gySIOpqhvfLhSFWmgVTV\r\nwKhuOj1FnSCeKsH2Hk8CeNs910BfPsDa+PmOn56CBbraaYfdVWgZX/dqdJ0l\r\nLv0kK7xb9SU0ZyVY4DAT491cSum4Aw97CatR38dYuYs4NbkreQ7WNuOEHuen\r\njf4AWIezEEHzZ/shS+0rrju/erspgxYrG/zNj0ShrR7PKmtOeqomtC7wCPBw\r\neQ7bvq0lRhsYrc/Z8iQTYGgr4CWrGNnq6TyAsHVEALSTGFQo8kupBDuwJztw\r\njk3HICiJnNg16IMrGvnzfxEpj5kZ1Gd1chmj7w/UbP8yEXeRFaVVS8j+bBSo\r\njgU90XOggJKvVXjgPatgI+YEQ8H3MhinVao=\r\n=7cK3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.19": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.12", "@radix-ui/react-dialog": "0.1.8-rc.19", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5b851007e8d30300da8c45e44eda79db0dd5a406", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.19.tgz", "fileCount": 12, "integrity": "sha512-B1JKQMNOe6Z3GBjMoBOH6+Zet2GmrOf8FtkRHJCKD86GTE5IaTc7hxf0dSyiU8RSmCF9lK/isO58Cb6/wptlLA==", "signatures": [{"sig": "MEUCIQC+Yd9O7DbWxX1OZaHakFDeklx4Yh70It8Jn2aaj7O5MgIgCUg3PwSR/ph81zvR2e1bHCVF0nzkpUT2MMV4X0sw2Wk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNYhAAmgo5rtjlbnr+7mMS9HD4ypjj/59Eh4EhylDLA11JF2o5aEgC\r\nnUcHu7kS1+fUMiI/vhJIYoGzn+T1yUiV2yrU6XEPSyIV3EgPqNKfnUMzVktP\r\n+dBg7kKL5OV+GKNUJeAQuiHUmp+DzO0F+GmIm8eTwdNuI7Qp649gMzvUmPV9\r\njR0YTQgN2os6nf/Y65+7l0ouGNcdnjYF9tEgQVIq/1zva5gstYlMcJIMwgv4\r\nuPLv0ySRup4DsyHUK7pl0dPcelZXt+RY3dbvrZ0yZ2OpCH4FxmlkyZT/OLWO\r\nf4uy9UC6UZx1yYmAc1qaajdwW1/Zy4h6T+Lr3BuoV4+oblqegptFlMJ7rSlF\r\nMuxsTlnVspPHF84suFVTLh2UUyCdAgXQWIcFrEV9vhHuW3yqE/5IHReQiM8L\r\ni8IK1VTusiU/iM5LKuUeoAf0EwSK0W7BmrgnkSV4QqwksN9RCBzdHveVh9g9\r\nj4PQGNbow7Dq4iuE3aURuJUd/7EzVFub3GRZQGDMi+wxTtHP1xEgknpYXgVX\r\nrxzTyR/S7W7FSOkS5XqTzDuJ8WXODq3N8INu07QPcaMMdJbMpKJxT4PHdsdZ\r\nIqFyWyfuyfkdOMU3RdS0ypL1VSlYBTaJYtMVrgldnwBp0WVbMAPLn8BPwWvH\r\nV/ip7BCx7RHoReaBi0MQ4uJiAl9qSzU/+Rw=\r\n=pNXt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.20": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.13", "@radix-ui/react-dialog": "0.1.8-rc.20", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "db706962f3f6013683a8d7281cedbf7464d7bbf7", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.20.tgz", "fileCount": 12, "integrity": "sha512-VcyX11A2AU2SY7LWJ3VbuvZ5uKnAc+H5HK0nVdrnru6qRee+NJjGevvJGEzT87S4eBElXtrbBALOkT2V9cbnYg==", "signatures": [{"sig": "MEQCIBG73iK67H9sCXusSHUphdhL0Wb0dF4Mioy0u0gDNcjRAiAqnxHGbODM0LUtDJuGUpoUuehhI6OgDNMYs5zqEAauIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6QQ//XRUc+bhxmLe2ewmS+dxHmOtH1on8mo75L3nrSwpFPlSyEw2P\r\nh95n2qre6kWrDxAiE2n8yyT1eiaEKbfU++GtuEIslXiAbAyX+cxBiOr1wfNH\r\neGUI3qERoiqdawelav4Va7dvrSgYhD6RcS5nJt1iPMJQpqalBXo2L5fGFd7p\r\nChqEWDU+JeEsPdi2oxCelkNRMfj++TM1uVRe8/4Ai3QPLXKxbYyLXyUltx/j\r\nym7mqxYwkdhh03JexMd/1WRnx0V3npfnrpObO/qcJinv5P4+fqtTyqPHWrhC\r\nb4bb8+DrELKQGZpPP3kN4jGACIJth4f6xSYAZWjGcLn1CuokVUDgv3FY0A2O\r\nH0W65JZlKq3tFsmaJcnVmVjoRSTrgEfOaJZKRuBBUToGkJGiwX6DkCxkd3fS\r\noDP+fp9UNy50mYgxTD1cJwQnoKqyyyrTHrn8zHKvy/vfZyHEqfMC1AZ/XkKJ\r\n3obynrDvShKLW5JDIkEGlMYxpZG8kuBm76RVXtTJw8fGJANwVW4VpL+A+W8W\r\n258fT2CJ9lR8YlRufxICwTxvpF5EqL5nJowd5l5SJk/NG8n0cAu5+0fmS0s3\r\nkLvCW+WuAP4VVKmqkhSKrQd0tLEucLOlnKWCQbFzpjEzZM/gTCKzcYQWYJd8\r\nM+KI3PwDiQtLKU6MXZWAeOQDf4jK16ysMJ4=\r\n=eqKc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.21": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.14", "@radix-ui/react-dialog": "0.1.8-rc.21", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "048de765a19e6241647a26bfd20fcbf25f94a5e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.21.tgz", "fileCount": 12, "integrity": "sha512-iBHmgdYli4/VXo5GHbeDuancN0++gUCO4oA5IxD4ndkFUnfL7RKrEbs45cWSWoV+5tI0xwCrUXH6ty3tl3JGzA==", "signatures": [{"sig": "MEUCIQDCn5IMitdRfZdiCeOlJgTHrJyXaV745IzaUSaHfGI73gIgJASlnSeFA5JdI7Rd0xGhuCyBYhZ9Z3Tuuu2FI+CSlfs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8o6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxHg//TPlGQQrLJ2ahsrKbI79B1sNMJpoJ25fwSt2edLDVgdtoFOoH\r\nfKbUEuGxiZwxP8x2+BbnLODBCe7YiXkeZYO6D716osN4fvzD5M9dmkKnEATO\r\nbqjX4cXRaY1A+e5AX6R9oybbmd+AGdvqajiixc+VeMFHzOxKywVQNMiiOoIQ\r\ndp/0Mkv+0yI6/siO+i2MqOiVLWXB8CNrWE54WTisSHsKsFUazb8amDyVtNxB\r\njMUQW3kumHItINT+j1ax8bYsQjDv+Ehuq0xOJYxEka6RdaXjn9+c2M96/EKi\r\nnb5kTb97TblOkmffJonKsm9xNNvs8ku8650KAlEgwUxvl0AvnfqgJSM5maBB\r\n/30S92zND6dio26sjqC72xt2rhHxDFJdu0/7eT5x32tgMwEFrmTWxsLRq5VC\r\nIlbxnr+CSBc8CD0LCOX0mcYg2hNssjKoyiLgWrKByHeYI9w1AF/fGtaaPlZN\r\nt9/KVRnTKApHwNmgNaKTzWjLnNwGcsSGwJlc7gbFwCfC6oyvhlrs7n1nbZ5V\r\nsRABk98BcUNHLoCzwmgX5AW6lkjZEWwmcJkiiUWPuAPba0ctO4JXg1ZGdJa/\r\net3iP8qWJr/vV5Cl5/DNVG2oakEVWgJTdM7dC4gsnG6sC59F8J2LnulqL5u5\r\nrW8hcJTghSNCLkIjBXA115KocEPBuJejFqc=\r\n=1D0y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.22": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.15", "@radix-ui/react-dialog": "0.1.8-rc.22", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b73aceb6d8831edbedcbc911f9b39114c7a6bd94", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.22.tgz", "fileCount": 12, "integrity": "sha512-UjsxadoKmNh8YmiVckQAPTFeFk4dSF60fbaCW8X7qmxmvqONNxPAG7IPCkV6VnxCZZgoizE7CfRiFrAh1jfU3A==", "signatures": [{"sig": "MEQCICkVyOOpElvzhHP/dTU6HhSfgQ1nYRJvMwZW+ITilpxPAiBWNaNBFG8pGrs6QZBeMOGhG+MhD98CUi+NcaVrCs2OiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDhw//bToEcoqVsjkHUPTzBbqt5UlCg1ERFpXtGtDqOIv9PaC2WJzu\r\nb7EbBvL4RpFa4tUJtFxG4+Ects3Kth4byw1WaTEAmWM4Wu8CCLioc1QeJYSo\r\nOud/rpKDaODEnIeRJEgFa0LVn+3AeKvROKJZsn0xIxNq41y5KHgWD0vFySzf\r\n7Do7kesIijLEAleUfscASs5dHJFZ2yRWI5nVk71KdnKq90LDpv7767rTHt31\r\nkyD9igJalUXE/oiAhKvKvfKj+Omkfl+QgmK+EoJSO735EVIRwnGwddm+m4zC\r\nFy3Cqxbv73uUQUO+luH3FqD0X7S4VV21dTU3s4+4EwfmJUvIWusuEC1XV4E2\r\ndZL/khB5WRJk/tblf7PdVPGNObz7gOG/ifTyXOh6oOOgcAYsatHJdlETeTNH\r\nWiM/j3y1Bzh/cH+wRRzw8sBbj7I/8rw1vcjl5as2b1tXuwFnuLTqBWwBf8ei\r\nS6YAi5pAP1Xd7WKz60rDy++07XYzPA+dqcX1tU8VNCRivL+1Tz4LWASdxOOT\r\n+wZkgTjOagpNyaHtFRzR1U7py44fLGDElkAr/iTl3hOnrYlQ+aI2ngw82PRT\r\n7KKLVjn1wQN0bh56xjPXkt1Qb2EnXC+2r26zqNpNZm5Q713n42XZCv8dhgVS\r\nrG5YnU73weWe3hmqSilOaKqv8rFlYpNA2K8=\r\n=E8p9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.23": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.16", "@radix-ui/react-dialog": "0.1.8-rc.23", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b6e92770847987d803f8e2383991a571a8816eef", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.23.tgz", "fileCount": 12, "integrity": "sha512-gfsiPJ9SmLgGUU4s+0jkp2fxyGe7i0Z1NJkvyy6RnU/4+EsP9knY5fFuTZ+Rw/wjORuplfFtjK+6lP/dPb86mA==", "signatures": [{"sig": "MEYCIQDj1qfBC+89HcneANRlS4d2BPaydof+ASc9vvE6Kh5+HgIhAIBk1yGhNfxnhhpdmcZKKJ63wdgt0LLld0iSybXob6T7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTq8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEpxAAld501wdIqw5tPhGt3LG1NvWzkfHTkPW5va/s7fj8sXGfuNzs\r\n9Pse0OOY6S+P4dYVREyADHm1ERCPL+rfL321ShBvAKu/rYnX2AmvwL1f1tXT\r\n3IeJozpOKmsYriVx1kxoq5lKPjc74SspMg7aXs22PyIH7izumbOe0cu9k0XV\r\nCv0Gnl/dTzXPbBKbileqttYMZ0B4WUH4v1CdrPVsEOFRrCXOJic6m4mOojf2\r\nLL1wT6kF+NB/EqfM5EmxjvkkAR0Ffjrex5ynt9SRLr3lEkJA9pxbAvT2VHug\r\n71gv2+g0yLvc0r/K2UyzL+nWwNU7M1E5nN+SuiYD4tk/k4dDsvyYgLl8FSpv\r\nMjHNP9jok8uTfORYDesDFHALgw7sUaxrhXU2Q08I9trEGlYf5m2nyYLu2ioB\r\nmnyPelgZEBI2+4V0+zfzlAjUiWnKLa6YHRjPa7wuOxldsvLGyhMik60qYF1w\r\n+tFVIm8MWQ+V6Slajt1x0JLmhbE1kxaM3cHR62ECfgIcsipgZUCBfYHF4BWz\r\npkDft5NVSbNue5NzkvH9E42rqLLd/aHh4+X75LacDwP55wH6zmg5kglW6g70\r\nw6qHd7ushxzFPshV3rKoK2zsx6mgFsu3WothflpRvUTNKKkCADUemU+dGE3G\r\nlvI2XmOKsOUp4pq1sAQjPFZnrYCPfUHLIRk=\r\n=/fEf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.24": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.17", "@radix-ui/react-dialog": "0.1.8-rc.24", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07e7357a110b056320ccd4a96f119f415f1f946c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.24.tgz", "fileCount": 12, "integrity": "sha512-jCnFRUGYj05FZ6GdpSRinWR3hlB0r+aA42Am0S8kjzCBMlhm1AnMN3jo9frkOjKkCgrho8g//Ak5evjjDTukMw==", "signatures": [{"sig": "MEUCIA8/PDQyt9eEDTliPgz/ZxUfNrIFs49MkhSB+IZqTcaXAiEA5JXQ0EnejtJyiOi07BnPEVvjGWkVbdGPKWCefnAgZyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifhz0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAFg//RzWY19unbGPUR4dmqwe7JVvbbJxc9+NI42iyt+p4rJTlKVNB\r\nfu4kBkRFV1rGCOxo54uHlD1tMAoEwk5u+BIA4hZCM9KAKwjLWh6f7lBms/Ow\r\nHTCBRdVVgZ5Qr9OL1kng3K+f1Km6odWO0eyj43hRN4UT+4G9AqFv4HlfaQyd\r\n0lDbfr5F1xdDdwBNbE/Dhs2eJRsJPxa31budLagOEmaOhn+DyjxLGprw7o5f\r\n663M80SQjfcLN0KgR8ns2EROLRSJe5b0ugWrWmGV3ZV+AN00AZ+YBw0mLpsR\r\nRRQQ7SbRbcCq6H91WSqB5II0e2K4PGid0fII/b5P08RJvdnteHuTOEl2EUdg\r\nSwVFni7rUEy2s8febNb2mm3LfzLgSUb5NeR5+BAZIwbAmC28fG1dKGWI4kb4\r\nV1i3izR1nc3bj/Wbr9ydFrfvVD8Q/64ZGUfNDS87rMiohRotl4CiB4SY/IaC\r\nN2Tgyfu9DJjWKjUBVAzlLFJA2YEuU7OyLNtD0FzePC3tjfptkFC5bSh6+aWH\r\npaC8f1m+hAKZtJ3fgeCZNmaD7MOGQvbav2CMOQYzSl1GSii2BxKaeROUKhBc\r\nhYjveyrYRSCEO046D5a7x+YuvrueuAe7/otinGPVrY2gsioNurzFxwObDfZs\r\ni6T5oMm1wEaUPaSGQ6Pe982FGZk1SVIELfc=\r\n=FH2t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.25": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.18", "@radix-ui/react-dialog": "0.1.8-rc.25", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13081d8a75274de3daf0727a76afbefeeb22473f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.25.tgz", "fileCount": 12, "integrity": "sha512-I3+f57aGxPnQZkqnvzN/rJN7ZknWwVK9L7FhQEmbfZSGvpcglyTgypIhq7qQNWyrcUqbiv8hXFsvPLbgRQqxqg==", "signatures": [{"sig": "MEUCIQDt1ak3IxA9flpJVPM2C3JHZyLw8bL54W1fTNimhvwLxwIgNDvHUKol4qAgUhPURRjpxsvmDX22HG67gHOQfvmukmo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7Ig/+Me9TGafzn6X4eO882a6AyygJq/P0hRoSrgQt5OAxw07TmNsL\r\nMMEe4H1CkfessoBEv5nQ9LnMOUka5nX2W5SYWev4Z/QUe0JprTxRyzFjWMlX\r\nN6qfpPUezy4qZpZEOU0vDOtRAWsqHSnB5Q53NM+edX7feNKCoPILuYvz/n+8\r\nROU9DTif21F5zYPb0FbEPd5qfcGlCfcNOihUwJT/Goq0qT054mZ2MMLuuPas\r\nTs1tSGm9N9hnK5KT4Q4Bju1JW84/T6P3w+Z6Xj/G4uNriv67Z9m2MV0T2N9+\r\n+peHayk94xBE4Ts9XVlyQCoqKIF57S6OKdGMVqC0SyjyKyH+1bPuCGWAeLF6\r\n4TCVoVSo0rQ8GKkHU4t7uXZNkmIbq5/CHsF0Tz8fp3W3+jWhe9Uq4+QGn9Qh\r\nbN7asixKT+yTCYfMbOV168nmBByKYeqi6F+mUPT9m9rjLc2rIIdqXj/xNT3I\r\nyNr6tD5nakLP6mSQ+6cQLcrsHmZ1PCpVrq43Imtip6BaSQLmq+3uSJ//AP0U\r\nVAdUPqqRZ17U0Q1HX+h/GL2wiDUj6ZNQN4tWNbsGyHf0URpxLB7GyTR52b0m\r\nd7BJ7S6pqVFLnSrt8+1RbgGpYjoRewuuhluyGzcpj2LNYJkn/zJyHfzjv+Ib\r\nkLXBmM54qJfVWUFoec7+fVr6e7ol6yOVy8w=\r\n=5MKC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.26": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.19", "@radix-ui/react-dialog": "0.1.8-rc.26", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4734e194eb26358ea95382ff4771b1d1576e943f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.26.tgz", "fileCount": 12, "integrity": "sha512-DV/rE2y9uWqvoe41AD+yCQiZeaEkyy82z8t87Ju5vL9uYZPf60kBeeu0JzVK/kqYIAOE/yY4bRJfTyXG3v26tg==", "signatures": [{"sig": "MEUCIQDKchlzsyTkrgFtAtbIBlwuzzem9ou+YpUuWkao8NPtfQIgEWIjhQv+r50nt2CKAPHnt2aLN/GcLnbKFKGsoOo3VYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2V/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCdg//RBWHOaa4EiQA7n3McjBmRkRdbJ1WZCBsrpH8Mvw89qt1lgva\r\nNs0UWgPgHxPy4tpzBPCSGB+ykJcGfYZz9vDg3Sasyny68EAhRehOtPUOtKHq\r\n4qqiTa+CSwE5zhgcLsfEIrnQrlqlgC57R4R4IoG0P9epYrEoVqkiDG47HUal\r\n4UthojwGr/xfTGZ8CHtREvzcDpcV5CmY8MLkCXIrKEJ1HuzseV5g+wjLWsRT\r\nyGmVkK+12UCRMp2KeDeNNtCpTGz4x11aIzkg8vd/BI9TQH7AOmy2rn9uuXQe\r\nbzUEWQcktIkrZLT7mmAMKxqK/Vd7LV+9fKtQl+ZDpzxP6+WsGzYsiG4yfsqd\r\nf30JAAzno6SghffXKzv+IaRWAWpw6L8Hqy/yWZ5E/GG85C6Bz241FnHHZKF0\r\nKmLLRBdfgM49Eqna9LR/3ho4kQbCtr2DgE3+qLf/8p6pFYHl+FcZpQn49vJi\r\niU00NWpLLBVFdgfdnnNO5d68GoTsJ9pMVk5bqd1Q4fQxmg0+EpRgf8EXH+7Z\r\n8Jtj45g5LtuYbnaZR4eTQMHLYbhgobgf5RVK7wyj+ZBNG/nNm7tkzUm30e6W\r\n4ChvXr5ohQ/FXqzNfueezPBZeYwy5Z0DI+P2u+DXZ9xqDO5Qc3vp9VqSmU81\r\necd+z3fcrjKwAVjpigsVP4AcV8pqQKu21b0=\r\n=hbRp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.27": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.20", "@radix-ui/react-dialog": "0.1.8-rc.27", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c5c0784fdd5576d6be3e5cff6878ec2e6afb4045", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.27.tgz", "fileCount": 12, "integrity": "sha512-0BXy1RyLctNPvQmhIztGkrycIjSLo3/WQ+ZIB7jYNYRiaz0II6rgQVJABPmKkFFf3T1OMLHJ0nYxGd1jxAid+w==", "signatures": [{"sig": "MEUCICxvhtsDfFnWZ2YldMfKyO8OPMWsUp392zJufwtM34DPAiEAgmwpKwsbAY2F2Yrh4UuHpsk3hOsJTxsfM46LiOHf54g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB6g//WNaBSi+/o68pT+1cLOSvs80IRunNeLCFG46JLRh9Jyn61Xh5\r\nuCLjJuanBsKCPy+j/fYvkMx2lhntbw+28VurA+Ai3Bnqa28w/mjlUd7T6R3m\r\nWw2jzFo9EKg+xpyU0QJjWq1QDdVgxzoJRuMvPGUoA32Va0+qOUwAu0SIyDxR\r\nu12mApM6MEl1H/Q/sRrFnbOBcwE00bJV7NhGdTv0WjExrX9JA+CTjleD/5dq\r\nbApCsx69kqarSmWugwvPTV2I8J7PSi8oBm0gLTW2bguI2WJsowz9DKLPK9Mi\r\nZA2Noqk1U/x4kifs7I40E8rWs3p6x5H0Wf1YDhrM1OupCtXES7vkTbadWMS9\r\n0+rVHJvRBf57V9rGAYhOvgA2ldDNWnvkKQ9qgB+JTkjj9g/iaP7hLJm1BPIr\r\nzQnjCGc/puZYn3EKezORhnmKeE+R5Cqg4BFypLszpbjYVPevFKDMLg/cFvMq\r\nQTyQEPxhRf3mCt6ipb7NikBOTJCkKd/3kwWP3K3pbDImjDXaY56ietlrDl1f\r\nsTnkUVTRC9B0tssERBZ9oETq7ZF/dTMgf0lxQBmXdWzzFrx17a9q0obRRM/g\r\nLTehPIuJ+BiO/DdROgU+JVQTcNyiLVmDPg9S7AI3DmC+yBXRJUip89W6r7ho\r\n5ptFMbQXFz/ZgQ4ti6SQ/k2Za5W8qNLkwUM=\r\n=Rs/w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.28": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.21", "@radix-ui/react-dialog": "0.1.8-rc.28", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a71780e181035577bdfe6b75da308a35bb0db1ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.28.tgz", "fileCount": 12, "integrity": "sha512-AuOUJ4joYlZb4+itLAAhHWCW0KYQBkTS2QMHoQINbKj243RTRmjGFI7dJjz16x6RIesqAGEDhXi89qITvwvFqg==", "signatures": [{"sig": "MEYCIQCl9PTrXsSxz9PeAdf6UQGm4103lobeQGxu5uh16DZbbAIhAKBRLRtK1XbWHI1gIpeO+j7hhvSvMVM8IzVdKbLckjzo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0LA/9EAyrQisvljgbZFgLjq7zhYJZuyl/wjSfAX6MWE2qwsUTC3Yv\r\n4p8J7fg75DAciOnwuOCDsSdZjpU5vL90wwK5kwSYeqNh0PjX1VjYPwvHwUla\r\n7GiUP2CfcruTAi6oJ7kz6spD2n9ZOcPBCiaBk+C5yZzBG17xNi5bOBaujSKP\r\n56KXwZxyXcLmGapCocax92sBofHvG6ULlwFW0zzhmjFhk6CyT/ePYxl8ze9A\r\nsegmkXY2w7evtrWpK6SsBkp1WjFVNRvoAM9I7P50VLZ1MufsyijZU5uJXNBR\r\nCCKyZv6CIxE7AxfuDFB2LSDhYI6PXBYIv3tgBqzzGzX6jjooqVmmcGUBKA0B\r\nEKbeigfviccLKYQElj6eo6X0dWEaapDaMIOvLTeq+150SpL/45Pj5TfUYcjp\r\nwLnXADMH9JVeaykax6exZUuAAX1L4JN1rLFEy7sGG8LhWl+OfOXIYGfTTzdO\r\nNkodz46wbMArTHLhvclgJOXNBxAMOXLDiljSUe85ERQOdQJdRAbtFvB4TZSk\r\n8km28n1SFG2ORHwwP4ZN1foEGUjbIJtFege0EZkojore6jxaqOkS/r4st6W/\r\nSW7p667wJGXi1zyX5ykK0/7rggGqTn6vKwEjX1Em9jmJUvfv+eaQSeGKBpOo\r\nLiwJJbIqN6/ZEW/DqIgWh9BFZppQ4ZFzEWo=\r\n=xbUU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.29": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.22", "@radix-ui/react-dialog": "0.1.8-rc.29", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c988c9a69d696d002ec319d2fedb85d98e479420", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.29.tgz", "fileCount": 12, "integrity": "sha512-FOkwTJ+ccc6NV2vuK9TRqMspsq1Hrj4aVaBAA6WGiDGAG0Lnw+6rVGgiqvPXgQNno33dzLPzz/GsddfV5vAurA==", "signatures": [{"sig": "MEQCIB1M2hNIvj7jsrXHotEw75/4aKGo/58eiJZKPwoCq6XqAiAVkDlVARQNK28Y4WLaP7nMVX9oLsKrzyKHMzWcKtyxEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphqxAAmoNJ9izzEOWWTdm4Qxgawm86XigXL+PP/nxETI5Kve+ELFrb\r\nDcMfQZL9ryd2zbso1ftGjrGIXSiDleDBR4AJAxXiSKQ90XXMwnRZxaoxNLKK\r\nwX1iDAf5hV8ZQbz/4uZj8NaY9v6xZ7uVsPDAgjuieNtqHRs2vWh16ogRTFc7\r\nxHp0uunO3i9KfrRMGx4xV0QoOqWR43qlXY0LiqWwfrGei3YTOCEHoQ8kGNsy\r\nu0mEzZ20agFNcVk2+f8WueobIf0UMQmVBAtEpb6QYJ+Bc3MnnBSx2mg+o8Ju\r\nJpTjB/0b8EJumwu6d78EnTZDAqEY2k4RPt7eogg3CNQn/W5SgyNkSzXyw7XS\r\n9tHrn9beLNlkGZg/V9YJpMPEHexX00r8sgEa4DQ67GXmfthVq13YUxtsPKTx\r\nRNr5vvkiIXPDeiyKqdg1BDuS/ZarOIKgDq1Mfaw7zdMZyG6arDhEQ35ToLcx\r\nf01XBIVcO4IJoeODAYs+ucQOMVWveXd/7ZujRTrTQVKXN0LyEF+2iCmSeBp2\r\nxY8yfPj9fE57pTTceAxHEkbdo6ugBT4n5oIFRZnp/+gMJ6ySLRmu7n512KBG\r\nZgEH8Nwkj/+3YllEzwu253aFBEhyt0ZzvEr96EYsOKgQjEdiGMWrtC5oshlz\r\nDP7t206uuOEoo3DxwEcJXhSjKi0FFBXQAQk=\r\n=T0eq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.30": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.23", "@radix-ui/react-dialog": "0.1.8-rc.30", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1dbff71abea516398ae16b8b3a79b0175e69ff5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.30.tgz", "fileCount": 12, "integrity": "sha512-nFmalQ7ueGzYiO01ZnkcUg9ewqO3+U0sKmSGBMPnC0hxVSP1Cw5QRU1pnQ+1WJPPy+8t3vqagKvvJxmwV88CyQ==", "signatures": [{"sig": "MEUCIEZ85UPUPM9qAlQwUTdkBC24jjrLDGEjkYIYHSBOtii2AiEA7sLQqkwuatZFKClZM1/FCAd2Qy8Z4st/OEBDFO3r1Qk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoW5BAAmfGrOeF2MgzxlP7fQVWaUAUh1eiRa+sbl9AuxSGezx10/kxd\r\n4iEfZx9kzrhpET0ikA2Q66JWJ5NIJmaE4cDiqGjTXfVJmsuApm95BK3q7LMQ\r\ne5PdEzsiB23Cb6Ep4EZKT7+OWRza9oE/MSlw+17c4g4D2kHobhGW5ckCI944\r\nDm2pHeZiBufafSb2yZXa24zQIKrLXRMkC3pJqSbofuc8rVRsuY0UASIR2zxo\r\nshx8uPNnzfI6cNYTDaToPhIAXdMs+V9teTAeRmpKDyAoiAMr6SfJLmYiKCbD\r\n7Q0lF5NozXh2pvHnqRV/854vl6h0W9fkf6Kvsf/9VGSnxEshyBRXpfS7wdII\r\nugeW6fJ5atFlA0kY6xS6wBvuxNXxzthJehgfaiJvU6c7yRsWYNV6r460qBIw\r\nA+6qCu6ql1Y7WJo1DJwmezlBBP4N55biy21RRUNHtRwsE+rIcND06oqo3sCe\r\nvFsTDmZWtMiIWPL+tnSE+Vl8PoWvrgLBX41MnjO1bP4jdH0JvcR6AnP+lVvj\r\n6HGX2U0U3IKLszhqyJ+7ypL8l99rl2wUunVHXNAueGG6vVe5bSScp128vtBo\r\n359hYrWvUqpAPESt6izRA+yEX2loVlUPqVzYIT3yHeKf+I6OjzJ30Y0nr6kQ\r\nm9makb+SRw35L3+uJLqpNUOzhXotFf73uLs=\r\n=RcHv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.31": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.24", "@radix-ui/react-dialog": "0.1.8-rc.31", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6218bfbedffcec812f07df6357dfad2abf7319c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.31.tgz", "fileCount": 12, "integrity": "sha512-UF1/SrcaM5zuJgHmPfmhCtb7laxY4u9fdaMH2kUhK9I9CMImL2yFMdEtO4gYcJVHVPZqREEQ9Z/QEXi+Z9/Mbg==", "signatures": [{"sig": "MEQCIHYpq/4ToVlRUHFza4tMOfi+FrFxDMGMV5/ThLi/W21UAiA5nnQ19AyAblL9jNrLc9AxgzNbux8zojIwVIa70+yPsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLg4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrekQ/+KtEkqeefMwMSfnxttMsawYSz0O4hBWXfDAhFoo6X6vvR0Yzk\r\n1oJsw37aS0uRqTvpFk16YrJgrvCZzzYQtK4C3SE+A4UH9qqp/CZeBGenK4gK\r\nWB59Uu+vbLBm9gzoJXkO/WFMqNIq2z8nkikQEBu1SX1XuQQAIXqnsOOg2AXW\r\nFMJ3CRVrcKna+WeKv2DRNjm1rk9u7L6RYvBsBgsiDyeb6guRnXBiSAEzYvn0\r\nrl+LTmCPUl9m//QqqmvMY3C8Aq8C9hwMffVi9z7sc/vuAN4b2A8da6pmFI5v\r\nF/UK5pk5Q6yUljKPW9k2PPfvGpzDLgWixXvBvjslbaBCGDv3dsCfzhySnbKL\r\nvymvq+n/lyt3wZ0v/INFgVw6EWIs8gddY1FpSuWBWp9yLK+1zWgKx6gDrfkB\r\nEPlC8+6ue4vPORsGL8e4RqId7cgqvQ7JCznyr7beXIEjt7N5JDiLygEoAxky\r\nKNzZxQCUwdZ/EFaZ3ppg1LD7jpdmxZDAEVZBwxzZ67teWA4/sYfX8U/owC1g\r\nWt+LpPMhYroRwrrgKLoNHNd5JACSR19d6ZJZUMtsvEirs/aWe+jtCbbKiE2I\r\n5iWxkbQAvDYVxmIcX17+ilqpxQ9j3AeYUWs6Tas7bcX65+Sac2BQ3DLqEZLc\r\nSxwQrGXA8LXe5Xj/Ii69lwexvixeTJ9Emjw=\r\n=k30q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.32": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.25", "@radix-ui/react-dialog": "0.1.8-rc.32", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "00a4947e15b481b486b2fe52fd51233f39facf58", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.32.tgz", "fileCount": 12, "integrity": "sha512-KDgCw77/A04MjSKzPNEd4fdtTJJT5rixyC2Up63/pEdmXg2hfS6gl3xR/fyN046XuWOUp8ABztDkT1Yf+eT86Q==", "signatures": [{"sig": "MEUCIQCRrpKzf4Kh/bTmpIWjTTl8LxsOZrZh5q4HQ3JlKqzvxwIgWJIE22zmY+p5lNHjCWsPQInS+DCfZ4fhktXV3lsITMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjLRAAmQjNbISE0XPSKAbk7g4SpvQp8XMtU8FBGefxaR1Jnp3h6/9l\r\ntfBQhEZN8eT9T/Uwk4P1x9mzriPeFmzoxYfliMSdPYF6NJlTiNJelrPOiZNS\r\nmZbEmzhJ6Ij4JxmwmNVXUvb9pmkm02z4IbNOIYZOF2WU9qE+jP90+Y956fgk\r\nOS8FgwPfIhzxXrpP+OERvXZQSjXnehnUXKlaFMonEmIyCedY9HSbqVKAloOo\r\nVgUoPrVbZI9HXZzLJb0vqa+Vo/XJBKPQ/rqwcCfxHCmcsUX4viUBuCmttTDd\r\nRtFHNs9U+89F+Wlr9ssSSfqSO6d92YQ9sAxIwi6hSqI9+D2pym3obFOb1Euj\r\na2IHQ+KjC0h0PrBxNcMsxntz6MX+iI1pcyyPwZ/aZlCMmB7Du3vAYiUSqsDj\r\n7eU2RtyGlYXb8db2u4qyYycWOoJI6lVjNpLRjG1SD2qzxTE76suJNtcxOi2e\r\n/VLtfWAQfreV+W/tggVYwqa0rmQVGa32b8v06mIv+BjTrKteZcTvW93ZoygW\r\n1lanYSEfYIX7/nOVYkpF1RHWYBt9ozhlPvLgzZs0K6LZ673oI4ggNw7heWae\r\nrf4HIZkL6QGBuiVXkG4i5aPYB7n5QILRxrS8yMm2vmnXbLveMOYpXwSzYYAx\r\nJDQWRdNlQwRV65MnsppGhmh+urzljrGLsS0=\r\n=DSg/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.33": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.26", "@radix-ui/react-dialog": "0.1.8-rc.33", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f5f9dc8af447737dd63e51f1259c2c6fafe86987", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.33.tgz", "fileCount": 12, "integrity": "sha512-HLmG9SgaV7OCRn83jmnjfXQPmLUW7HIpNWskzmgvmr9Oi73QgfXbr4YOhLg2Y+ypylfwdnYs/VBKyUUYsI5FAw==", "signatures": [{"sig": "MEQCICQ7fUwvnOi6oKCYju9Aw3qzHLkqjCwYJ1rHTmPBPjEmAiACQtheVFA65OqsJe/29MFK1wVsUDx4DwWgMoVjHaNUOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAuRAAnOwcYXDulqUmQD0TKbXzHJBiNQjOiVlKAzwTY9ZinW0Ks3nh\r\n7h7O9uzUP0eC2HcMlG6M88HteT1znaEcHqcC3CcNNnt50icNPdfkK3x2K97y\r\n1kktbc9Gk77n13Y94JXTT/FW264oIpf7GE8TwNER/5gUin1CkyEVPkknG0bf\r\nV5eUXRC0bVgoNCMIV4Yh8F3Gzo7TJJ+7+mmwOhhLkXOIIWQMx8eEoyLFq6vE\r\nIgnocl9or5cGx9uTOZs1sPdlEHv6nl+tGpAFufLqf+drBQ63pX9dYF5jQmYE\r\nZiKQKtH6VYGRTwpXeDLVQgbr+NeiIvc5lJslur4jJhpHN6RSkp4c1o535zEM\r\nG5kSXECAthtx07iVyV2rxIWOJWlrO8mIsOy3OKPFEGYUe0GGdN8NsPZ1o26a\r\nAbOlu59aHcLaAfumlE59pwh3Ty0GesttAJzb9PI9CLHOFwl+wTrEiWXe/qYF\r\nvtHYtfPqvwsij7CcRqZuCUk/mezmbTpA1Uiqzd7CtbZNekecAoCLn/+WDGeL\r\nQ25yvPKWn2eQ2KMBoK6T9o+V6ODjYtzYktGjjRP1SXPovbIgLtiNHzPvyZbM\r\nlNTGVwHASmMXcIIRhXE0D5TdQyjMJA2YAPdEJBzc/ZfLFL+vAOeQMFb1OmPy\r\npsxA8eMdv/RXobL/5KdRmb7kdd97JgEpSt0=\r\n=AQaz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.34": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.27", "@radix-ui/react-dialog": "0.1.8-rc.34", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cf694afff1d97972c28e7636b28b48f343e7462d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.34.tgz", "fileCount": 12, "integrity": "sha512-el/6U7yh4RUv/haAfb/Mc3gK0d1O9Qu97y4Sb4cGCruudGAdwRF9xRX13Vy3wUq7q1VgHUD934bFb9kFoIbliw==", "signatures": [{"sig": "MEUCIQDbNZmci7WAyHApv60zXwkSaMruf1CBDkpefwZO0JLZnwIgfUnO8vJ7K52N0/gpj6jEx/zb+5I8AcrSlg0zCXIjGTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ0zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFBRAAil9VGXsy8VxZ7OcAx6mM/Od3mJMnihga6vTZbyaL8Y+can5T\r\nLXDg9gzN7iyUJItWXxcjzbRLV7Q8A8IV3nfbronP7h/R7IpclZF5jiv0LmcD\r\nxgRH9L7rMpJQdjLbhXOkjnFgtEcoY78G/PCY4SaifVaS0YfVZtYYxKAu7KDV\r\nUBAqZcUJEzsZfcf0TbRat3sl3/+Fb9Zrf8GAnBPXlPxTYPVypoGAynnRxDFT\r\n+QV3DgdPIOFfrhyXuw9CfGBMRp1eI9NOaoMSRUKupHQANNFP3KjiPL6GemF4\r\nBGCYXKz+TdwQQBf0B9wAoTC7ntfiYSmvLsKsMRz0KUQ/HGyTxDkM3U0XWv1X\r\nLmd3G9F3FwloJGHTLinfgwnhfQ7p50i1lkLpjXJxGc4U0Pi4Zm9jpK3EoYri\r\nzIqR35MJj4mcFDQUwvztRB9eu9PruVF2KTVG3pu/iseBkgRy8VQ5KGCOSFlH\r\nhVCeBbpMoomYWdsR0kOsPzXaY+DWcthE2Y8f+7vbQQLJkBNEtc7uCdJJ7Dak\r\ngshopW4YAmooKsj11WQe3lU5PwSy0k/Tq9X5oxdfBHF/FIcmvweneiadWX15\r\nJI2Zc51783KByoSciTU6CJQuweP5ThvIbi0UHMeq5fzi4kN/BE1vA3bLJUkl\r\nx2d2pk82K2JPPY0IzA35zZlg6FaN8ErO3Vc=\r\n=Avgn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.35": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.28", "@radix-ui/react-dialog": "0.1.8-rc.35", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6f5383115d166250cb4b25a8d253105c7551238", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.35.tgz", "fileCount": 12, "integrity": "sha512-Wf5Wn1Wm0ruANGfZRVAqYYJp6rmhoEMu+8aT5DWRqpJcsp+3fQlIU/dGEsIKq3UFL2oEanZasAsCNzXn0Ro1WA==", "signatures": [{"sig": "MEUCIAm4+iDyHxOgO4H06msoeqVMjjfNzgOspPJooR4X3PGXAiEAjbUxVwDy9dpWp+8mw6c7tVUCx23jDXODzQ0sqr74NG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildMnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOLxAAm0H0qAwl1/aYQh/zuwUjl9C5kwYmyFleyxfufLpgx0tqBPhL\r\nBADxUkcO+DWbYy4nv9AXF6Klq4bPJDszNNKskWBfuaurBJj7V22fyQVYl7oC\r\nJJDIrwQ0DEmtM1oYL47GKX+N5Oa6pCa2MNywAFXqTAaVoFD38FZcTgkjTIk9\r\nBXuv9xf7b2+PBJhHaCsUgJkMfx00+3v6A+lfY8ZNYiPK9gRNfN3r8rFrW7gB\r\nW4S6ZBaGG0IOZRS6UHRFV4gZzwA7wzzup39DjIUgNEcl/dzjrLlMtLIJVQmG\r\nguN0htRL0zkwu/9bge4Y1Ip5ikZMxuFTgrE38Vi830fr2fPwZ7ijIRp57zny\r\nUGXXe6T0LXzcsKDYMXh0jIPPIwcWBHFkO/9wk+DDY0OXbyiFl1qKIT+KUNBU\r\nfB9uRAsqojw5vCks2NfQ+aux5ctfbY5gm6Ih52+/+9SJ7eFkK7tvUK08Kh+x\r\nb1RcFdq8bWb527qrN2wrsFx+KrT5vNRTpo5tavjzaYTfb13paFyk/3ycsL7X\r\n6zG6+ndtItTaFHZilPbfSpG57hZVIxvMcbZg9mr+GvOVaS/hwLp1OYMl4kyZ\r\n01y7FIc3Qg1S5FdRio0CbSlwp+7uJmQe0Sw9jjEvxiDBTZdpBr0ndYUWMWph\r\nLML9vIdTkSVkm33RQb1lBhs/NdXD1DzTs0w=\r\n=cfss\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.36": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.29", "@radix-ui/react-dialog": "0.1.8-rc.36", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16f693253df7cb900ae70a5b3ac42ad62a0d0032", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.36.tgz", "fileCount": 12, "integrity": "sha512-zRpQ5/57+6+Bxnpz6ZrqFX4otvuI4Ss6Wk0zjQVKYXjlWuPvnLCvYw/fADX3wY98d62kFwJNsznnn5Utv0ekaA==", "signatures": [{"sig": "MEUCIQDw7MkanATa65Eils2Z+hBFZl5Ky3LIgUhi/BkaWdrsCgIgX1km4rxw8PbQDtNFq8g/NZeCTSkU/sFX0rGp55IbJMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwew/+N/su6EICSOcLOWTU7IrAc4hQCPI6HJU08z5VxM6VDRQu8F+W\r\n7/2P8St130mRIR7T3PFrbMiSezKaTTh4BkDOfNb0ZQfS59Vz/h/Rp6E4AL/O\r\nIvGRYRIfkWf7HVe1+Eg3Ais3lwtHo41gvIE4Sk99vMIvmv9+9b2bipD09Lb7\r\nbRGJEJkcYCCGAhrzRJWD4UzrRxVWzCX8zqAcLmCBdRy/xjKHlRKXuxuLudiU\r\nKtHS7quR2ka+vk8XOWwlbi863o2wPYYUOnINRG8d3XchIT4y/KwerXqEwD9b\r\n2z63Xnt6vXcum0lW3JUEQv8KFYtBcMk8lUwqkzF5H6XIPSGtgD0jsJDwnI+k\r\n0I28TQaNu3S6w1AiViu0qrxO9tbLkncjDL9eSfOZRXqse1IwbE7Gg+VsRE/9\r\nIZMX6rUVm7A39GAYExkmlrsc9mLHINWPz/TdRs1k8FVjS/jIRoNnQlgqObAb\r\nFI2DFn5mvINy7aQfX8E+nXsUasMgSN6c1bhbT6GKu9qLcf1brEtbNVYgXPv5\r\nyw1eH2LVZA2OdgI5pOJWiN1/h27hdqhk28Wxv+m7LJwpB1daATapVIuk+MqX\r\nxjZL4zMzZHXNKN0gA0H3MJufkZlLKMyGKArUCVGMPhCSTAY3Y1OOSovuRI23\r\naFCnpTkvcY7/0FXbxMbCZfIlaV24fy6c6+Q=\r\n=7JVL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.37": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.30", "@radix-ui/react-dialog": "0.1.8-rc.37", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3b809e5472fed966564228a81cf9ae64dfbdd877", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.37.tgz", "fileCount": 12, "integrity": "sha512-elkxiiKN0mHa3W3WmGMQCcWOBHQnlf2e6sz9mHxP046ME6P6xDALBQkfpS2e9ZEuf2yADw+RmIsrCGRvM2bLrQ==", "signatures": [{"sig": "MEYCIQC3uIyFKabyMJicpeB6RK9bXwQJRcqxIrTA/LsbA5LbKwIhAJtiCt1q4kciqkGWi85VnWSoUzOc3lO0HSdQUBI8KkZz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrwmw/9FARiWzgI3c+/EWxY+nzrIeo2MUF+PhHfXBW1QiZz2SH5HJF9\r\nDTNKbVdMVIx3N3As5pT5VtZGvfMPtRtrZnU2R5zXDuN0tFtRJVPLeE3H6WKU\r\nNAgv7pH0y+X8YBToDSAKn1AuL8QCIE3Q3a/e2mySQnEAdHwexFeS3bqAMxpg\r\n9qOFx7SxNJifPTe2Yj0CaW//O2aK0Kaq425yaZ3/OQGO4LN5A6O2PbPZub68\r\nKgWBJG1KqqkNRa0HYKTq8t+Cx9Rx2KE7FbZn/xhrFP+wBu5CehZIxByX7IY6\r\nQvbLGBdQjPMgdXVgyrGEl8X5/p8js2Ur+hX7F59dBio8903rlWZLsMdALQsM\r\nxyBthmbMO1Dps5/bSyasI11rWDmjVVHlXaKFdoHBh8TWcWwVb3iN9JMpWbZd\r\n4wEObUqNNgOCNFYFHWAKgMC6I4QTtNJagSKc7Nz4b9HeriEegm9SRL4re82y\r\nFxTa06MH03qREQzFTg5rDH+WcI5RmT0QJvoE/TwlMqfjqm0lT7Z0B2ACt/Ca\r\nQ8Iwu7qjO3IUS5lIHn9L4us6H1AbEQd9c4SxGg+LS9RwAz46NiYL/nBEUfmB\r\nHVxCI50dqqz+SZZi2Zt6jBFqYJD8b08N4JHeGJUikfQRPhN9XoIUPLYzraao\r\njmPAN0r2vpb4UwZaVZX4ZYW04fW36tIPszI=\r\n=+5C0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.38": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.31", "@radix-ui/react-dialog": "0.1.8-rc.38", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "32c0dffc432c227e250e8e321d37e67e84051f67", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.38.tgz", "fileCount": 12, "integrity": "sha512-LYSd9HcVSS8x4XspUZp4SlP18QPNalSGFAjgtHhunM4L+kIrdAVxU5SMZTxf/Q0qH/DmqVyEUChQAbMe6HuzuA==", "signatures": [{"sig": "MEQCIEDRzH8P4ayxhLnwLAQ3ysQyMar+dBo/vIG5WKdzUEBHAiB/xNzOLtD1YYvaJIgDG2r1QTDmcypfSdeVvP+Ql892hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3WqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeSRAAgdpnrpjPxp9opQ4gg9+7z+wwWLCiN09Mm3S+MuBB8oxhyqzH\r\nCvRpQhsJ8ty4LEjTKf2nEINVbUDOBIaLIsGh1L8/+JbOtgLqX9ve9FGT9izy\r\nV/MdZKsY8sJcYixjzFoj1VLdB2k+3cKbx8t5+aBcu/BitAwmCRmH/eyqtgP8\r\nOST4mXAo60mTRY8q+EUo+bEsd9MLBvrUJ+R/dY4jpLMd/bndUSgEcf/p6oIa\r\nwUOENWowmTVJjNDvDbHNuhLigKDGqH78XM2bOJLICHIB6q+YfmFswIcjnZzo\r\n8h4IGnBNR78lBzNHsSCehq0J/dMQYArfSZZ4HTLMKJYiWDfai6zIYwnLCTz1\r\nlw1fpMXt6s++kQVnmeHdabLcm5vd2Gr+jxad3l+ptKvB76y5EXuKE3B+Begj\r\neIwn9vi82CSTMAR60eRM4TgR+hYhIqpHcHxdn+M0IluGywpp91DcxJVBgT95\r\n720AfALay9dkFrX26qtehL8e3MXvq40ABSCJ5qvQm/92Hkd8PxedO9w/l7/D\r\n2apmxof3pmMYKxepfMq9cD90W8zFVgt+ukEq7KY+sIilPU5XLXyucPV4yR9I\r\n9Rzt0e6Q4hwc2doyMO7zBliWLdoOqencBdUR3bowTjrXFOlQfZokeys+n1aE\r\neempT65UIwr/xhXL3jhI5Cq1+PyoUHzAPpU=\r\n=Qw84\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.39": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.32", "@radix-ui/react-dialog": "0.1.8-rc.39", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cbc301a0a6f084c4c360c2afad9cd34879a7669b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.39.tgz", "fileCount": 12, "integrity": "sha512-YB43f5mvvOad49zYrW7SaoxO5+kNZqnNAU2hrOimZm7A9pTZ4esQC65LM1vzwEBFoxIY62gFKivI4TrayWxSyA==", "signatures": [{"sig": "MEYCIQDlYofNsZ1yxRyAJ5anQIgCFnxtmYY5rbzpjtaFZelmeQIhAOOscQ9btCbNDONy/bSNZ9J8jmlmjKHQXISTg3NeTLmF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaCRAAhJR1qJbZ+d98npWTWzW37i82mzIfaslouHaIzjcltMQaEWZq\r\nOlkA0k6XjEUN6/06sbIUqZ6EAN1BUNlPu654kLvVziGevIhfwzFRHuFles0Y\r\nxxxqwQsH2xeMDnOCCB5LPQ6EtBN4BYG0gh8N54oyBJgeieKnYcydX3rGiHjd\r\nxoFTJgutRSqczRD3UZbY7sCOCAu/GzsbxbJtHk7LbU8l1NrVpgAQzUonN4TC\r\nAK9/qMvxJ6ka/TAiTENC45M1HYzovOlgcx3FM9atH8f+RT3r/CEoltd3U3RW\r\nSM1JrFVjy7trguD5/9H/hWpWfsRkxD1VnoL8NVywTy3II5pPb+1mPDrQBLhd\r\n7gYkf36AS4Qsr8IXzf0NdfgsCrnj1zf5WRfw9SwooCOwJ3SMO+tIASNvqtM/\r\nICVgJeeq9qelYscDz/Q+d+8+2gw+0gbd372svVVGV8AWOUle1L/a6sG3mYLF\r\nDqTY2uSQT5S7L2ftcKXRZN3FlQVrYnl0szsJFO6mlKfm7fnZIhUBEpkQtnTw\r\n7wdOjWz74tLzah7qNaZF65IVBUBwwvtguwZXPzhFqy5zOJieEapyBDgv1Rna\r\nu843AgRoOt+m1da0WKA+E2HWgjKQsdzNPieM4/HBjkMgzFQzDbG3Fg/Bvowe\r\nqxrlUuYhPCLgk7tm47DQTl9iD+d7WlTgt0Y=\r\n=Qk16\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.40": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.33", "@radix-ui/react-dialog": "0.1.8-rc.40", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "da242c370dd9065577004f969e7b59336c89a437", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.40.tgz", "fileCount": 12, "integrity": "sha512-MS7hENavx8rkhOfROQyHOn/Yuncn1wNHLNo/czRv+bHaSjxRAWHlN3hGBGAonqbLQ30ZC1UsH8Q6i/zD9eH2Fg==", "signatures": [{"sig": "MEQCIH3BjeAu7qDzKNpyCU8mB/aJqIge6UlVpyADqhn4j6/YAiAF7GVPY3Z8WIwZxmXAIIb+yX/0rRA2chdRQELqTP34yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHbrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruUA//SJxuiK1/hGMhNVXnrzbFy2cCbHRc9M1PgDEvFcDI0PqoZP/r\r\nHNPDsjoi2xzuoFdWsd+CcnhRwYaBZjL2wCWyx8HLAVMY8v2GV0SZqtAknnby\r\n7mnssIwH0F7l6mTC4ewR9mPX1SnJJChdwvoLjn8gDqaorHbmoDwrSG15xHfN\r\n9f4xdhM8M7CITNokIJAEkIIR3ArCJKin1UG0jofMEQVCecdXSlcLLxpVNN4e\r\n2D9jRduLxKW+vwcR+UUgiNeIsnlk7m7BDIjJo2yAYOnyP0HtG43zJ22eEthX\r\npG3QZgqVPQZsdkqveFAI/+IPhzgArErsb7JBiVDYW22c6qR1gCqaQlExNGpI\r\nfzK6K4enhwog52q6A3mNwR7RznP0rDV9m0WKwPZFegHmnAnF8/8DMNBL34Uh\r\nOx4MaUQFfhmLHSU3YIXmxmBydTCLLnVUiBiyl0vIzVi0Pi17H6ikPQjlKfd9\r\n6zeDK33wKbe8JXjiddJP0tqymjXqic7R4Mk/OpoCet/cvQXb7KVWRh8suxQm\r\nPOEtEx9DjNHpR5HqPCsjumhNxCtA1d6e3i/7C+mCwlRIJKV2VCsYrV3gc5Co\r\nfyHn5iHHNzJl122ZA6sJ9bN5mjqitH5MgN13Ju9+PXP2QREuY0cxV18PXjCM\r\nsQu8deJKyS4Uxx3ES1nGHwro2tCpCVYYsSw=\r\n=ppM0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.41": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.34", "@radix-ui/react-dialog": "0.1.8-rc.41", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af7d0562cda63708bbfedf5d768c781fa1945a43", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.41.tgz", "fileCount": 12, "integrity": "sha512-nOU5+rp0XnbYx0qBIqeRMG8xuRCDB0UqB13MpBUvOmpWZKdUiOiD5Uqp7w/c6n6NV1qwoY7k6ozfsdse87raTw==", "signatures": [{"sig": "MEYCIQCb8hkFXuUJWCqxmvmNuIx8wTYUBzRE4VIf9rVfTHQmoQIhAM9dkCHi8iQrPzHNAQ/6C9YV2uSpFNTyjhy3ybBJFZSa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNmA//TSW9YPlJq76W1Z4I+QgVabFzkEAvpZrWO9/cEnOfiy1kG5FF\r\noOOWJIPAnmsZhcqbWVyW30IhlUt/Y/tWPp1LvgQGF3U5R0NTVtnW8saLRAdV\r\nQdV1ZuUohTOnI0D40q/F2n2tXsIhdjCyI4HmlUDHeslK+r+AtBqIteWeYLCm\r\nBfy7Z1sSVoKM6+gqpmrxGu2reDwZnLfhhysRQS5sdjvH12cpbohoUxzcQFRm\r\n1E89Lvx6pL0tTNfJQFAeUc0bgTUdFXef7IKRgaf2y0BmiW3BOC36TRtYoRQr\r\nycvKyfXV6jDI+kfRf/xHfZk9pBWPeBpGN6IwCZOhke4vvXXfcPjmkBV4/E9Q\r\nEJYZE/6tcA6Vx624tqheaICJDlu85rHhjO7Au5Wss0RKnvc2EchMq3BV/K2S\r\nDdK8yMb1MdpKEqmGCZFHbJ5Opz3XDW3eHnIDxggTydw+oBb/HHdmZYEXvcYO\r\nqzQoAC60QnqByk3pjUrLs+mzhXAIBcTWDP5hl14ztqUTZZWjoFEuF6lhMzSD\r\nTit6tQMVuZo9xvkiKp+CVAckysv42rxTGWw1oelna9TyMH7ae+oO0nnkR8gY\r\nd0qgCUHASP+nLx9zelwUhyHX2Xg8E/ke10WW/zet3Ri/zIvKfea4jkX1JJmG\r\nUkyHppe4nDHQTFPnt/0GGqnFXJOpaWY2w6o=\r\n=NG5H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.42": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.35", "@radix-ui/react-dialog": "0.1.8-rc.42", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1d63b4bde1994b621c4753fb317bb919519859df", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.42.tgz", "fileCount": 12, "integrity": "sha512-U9t4/z8MMQfztpEsWPdJ0haFWtHqBhUY1yhAbLjeOzVZqMVKwJGaRLsEYfyBAZ55TafU7A8qhop6OK++K+kDdQ==", "signatures": [{"sig": "MEQCIEAxGkvgh70FmhfpMBB5RrNQNg/nB4aT5Ryo50kyqUQ8AiB3H7v5IoJornwdTzcGffguC/7SsS+4cquvdsP/Y4FadA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeHw/9GSn94UOwzaBWCsQV0NPB+MPDaTbZLo+ZyKYKmLeEs9rwP7pl\r\nNcQxMp/kT7SoLRokCiB4TQAM/jx+Ib6PDBYdbd+vV1L0IH3b7QbcV8z/5TVo\r\nLLxluHf67kRazroYq9zpy2M7g3yHM0zd20Kd6ItTtPeZToQ1xvkbSCi4azKJ\r\nUik4HXdoWIAByYU5dRUHUnVBir1GM934LJFHoVcgLAJIM3yGY3A49rhbZnlr\r\nckiJ00nfcvotQ8Y4jv1kO0l8Jqi4aFuLgBXVkP4R3ohqV2XBf9n12RWRzljp\r\naT6n9X7W9mGiBmDcyZu4J7wh8O/tPMI4k7FXQXaR+7hZT7b8FAzjEbuw3Ful\r\ns9yF11QoBZC9EldVmZNNga9FfdSuX+pPokpNtt84X010LJ4/4CHK6FYo4TNs\r\nawAXcdlnF9TtF9EHXcUnY0K+GF/5BtT/ofJ9atkoOu3WUl9bF0qLUVE9jOmr\r\nSMqGMYar5skJw9o0n6V0N/WgQCgNckCGcT+08A+WTtPPAHhbLkVpP8cHj5sO\r\nhOBJyXFy6kg7bsMBZ81IrpRNumJPpJ60RC/tHbvQ9xpQsorI6NVB3569h2m/\r\nItaiFrji7Uzib75cnw9vsE495wrHoSx548ECzanXLG4R4x9VBNKlcB4/W9/B\r\n2HTWa1uHpRCTzQoXt2SBu5QQesiDnZxgbKQ=\r\n=6NLZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.43": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.36", "@radix-ui/react-dialog": "0.1.8-rc.43", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc148514ba500f360e764e2c7262f92c80640b9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.43.tgz", "fileCount": 12, "integrity": "sha512-Xr8LWzlYOeFeU3N2HyVeVfyAqc0Y2nPneaPp0kLzXMCtnrFZUNNMmUfbbR0nH7zTN0ibhb2ZJS9SmgFhlwGr9g==", "signatures": [{"sig": "MEYCIQCOQ75vzk2VqqYv4c9D+RZHuK5+ugzRaudj7yoa5fvxlgIhAOsfK0xL3sDt65oFh8CL2BL0WDnalOW08Ak3h+XuEWIi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0H2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO8xAAgdar8FlBIHx3vkWxmKj1m++IWaoGFUfqXW0eWDNUn6A4xFWg\r\nDGDyCnXcm9FdyEluC3pQa8aM/Sm4dW0s0yAP4TEBwUDiVaNZH5QcPmkGVuK0\r\n41p/80EKqkv4qaW98Lvh4KVGow7BjY4S7Cu4NKgZJ6TlbrbyC4htRLEwt6tl\r\nUZqYBCoVgGgkIiKQJfPtNKQb/MhJO9i+kp58ocDnpIAc03e6tkAsX1EQzN2n\r\nD2h2aHRB3TU10hkKU9wHTkwOH/FlONQJ2UpJ6n+Vnt6oEve0vttGIB0EYyqk\r\n+x3Y2gmPEJbF5nFQf/j7xFzCXh6l9Ysr4H7mF7RPxLla87W4ZFegbOQFtnUS\r\nvfqSTgWKisFGd7V6ZqMDEdk+mHMZYvWn7Y6gz1CSgVDg2dj0tZwjsSMKtS+d\r\n92NXvavdaGYxsRwkmGkifoTHsng+Hm/HrTpPSD3wxhpWuyuVPaOG0Jtdtuhu\r\n7tC72/q/Dta9z37ekqlj5+4R6PHetr4xege0wqHQTGmDyea/jflgR4mwnbl3\r\nzYhHohbPegFYsyb2Wgc++YllNjEpIVfTLufp+br4+FrCCoJ8i3g+k9kl9Upz\r\nF254QgY9SwtnbWqi6KNyW8LJYK07B2UkoBb56uNCty70EpK3Gxzmzs8LyBLD\r\nbOyCbLgOwtyvG8S6vCorJsp9I75zZv+uXgA=\r\n=PDJr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.44": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.37", "@radix-ui/react-dialog": "0.1.8-rc.44", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5cacf218b35aeb4c24405e7ad4062a70341fae91", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.44.tgz", "fileCount": 12, "integrity": "sha512-zpTjMIIFISc9fQl6eBhgAoCE3I4mc+kjPuEMVwYcSeAHGqyfuv7WWuli3nAHllNgv0DcDMeasv1h0CmJCaaJwg==", "signatures": [{"sig": "MEUCIGOKsH0xlLyvBYKPW4En8TLJDxMd7obvLgIFyoIYVYdDAiEAhLLuEh5BxmYs9veSDDOJm3dFxmaDwkdu3f3DUiqn7F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpP6w/+L7yfmr/5wbDbFn1GXvTFIfAz8KmZoVjY7lek5QaS/7KETzYl\r\nbTF9pVWxgLNfRS0qQrZj+kwVRpx4GUhcSnjTfBGJ2ydA9V7uWlmDAqc/N8fI\r\n4fF2Yw5VHfxntw8s9wr25AwXoJkFjC8Ls2dxzFY6RFHG7mtlZcVijCJAMfE7\r\nmOftBqXXANhjdmPtbvrFdUQEP7xwut0Gn4TlNjJFMqPW5blYq9tk7ZUfVAbw\r\nuuU9IIpvfWUSTVEW+aN4DHtuBd3B48kmcXS5xalORVenDlIju0wtjTr4eTWD\r\nW8xPhAHVs4R4+2wBsMmwKseClgo66ZjrXFPfDGSdT1lvg1ca0Wpifedcezli\r\nDe/gxUBc9dRxodgRtQAYuSPd0TGqwoV/VDUdLP5/dLOPGvmfj8muazgATr4v\r\nXrHKmXuxZE/MDS6jmKYVQhoabv5s3EMqb6Yc+IXnag6ixrfxnB2DzrmEuNZe\r\njFUG98J+/T/zfAcs5wutPqsI81fy8QXb2JFIfjY3N/SyBnGDxIDrCc4F6cY7\r\nyzFmNXBQOED9QTz/kuSLQSVWkZBGrlpN1P5wV5XR7HyI0HTGWkZasV7x1j3/\r\nBO7J3njPAnG/rKkEMmIr8kER2rFX56l1R9+hjfIWaZbe6qlbBdNlmO7UyjgC\r\nzOnjLWP/fW7b7GZpl85IOOZSCg/dizZFDRQ=\r\n=3jUa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.45": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.38", "@radix-ui/react-dialog": "0.1.8-rc.45", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34fe42d71548f835c4eb8ce904664cfde889a6db", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.45.tgz", "fileCount": 12, "integrity": "sha512-bDQ3zQ96sLoJ7cpcfgn7YqoGPzOU9++bAc7e29+0PyvK4v++jgFNVdvN0S3UDFgTQFgmtymsMgayP7iEYvWqyg==", "signatures": [{"sig": "MEYCIQCr5K3EKiSBkqLF2/YpGBDKxtg7+qFXRoAWnOA327d+DwIhAKgU15XaYt6ERu7Qj+ww6xJ6KzAdztYvZSj//LzaTqoo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHWw/+Pa19oh58OPLI4lpjVSPPSWr9DHlja7+CqxcV7haUVWs3gUF9\r\nJOcycz8TnC8yFuwvR1KtQ0CGr1hiQN4miq16ScqM8gTD9Ku0wn57EILVl4yQ\r\nxZs/MVO95zsAbaJkDWYGRbPeRsdstWBdxuazqELSEFobJCXsRo/upalEH5wa\r\nu3waFS41hMGsH8st7WJbZrm8EWUVkRyARYuRwLKs3r4BD6bw7ZVnERQ/4VuD\r\n9DM3j+BlXrxbzVNkfjKnB0vn/i2rx5wuuJLZKmoigVSFf8WUmFuaRJB0rMno\r\nB/bad9tDipZot9CJ/+1NIl3AGOqiYIk9ZGACYAq2dioJh24/GYq6X/qziyAA\r\nOQlxxNOjmVMetBZnzrGENVue2JdJROwTNziUiLYS9ws9Et4Iph/H2ofCjVif\r\nwnmW7hQoaSHpOR6OApgGWt2IyGb98RWkSZ46HnSW6zjymdq3yhgSs5yTaY1i\r\nPcdr0Dmy9FZ9OzPiX9Z5xlIbpuCf9thxYqUGKWXlTM6KGFL5chldn4B2dyuZ\r\n6Gf/KVTxx8bXOZC+8uAX5eVjRI3faPaEryhSbDa7TpzGpAruC8HS8+7sUHqY\r\nJudrQpljPd8QaoD0a4JyjHru2ojC3sXpqzKz0vjLW6UCwiEAE0qfxOu/WXCQ\r\nWozhuIGl3P7xadM/xlYgfaqlbXS+onxGy+Q=\r\n=W7OO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.46": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.39", "@radix-ui/react-dialog": "0.1.8-rc.46", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0846d0d9a204f74699f30db1da6b9fd4094d855", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.46.tgz", "fileCount": 12, "integrity": "sha512-h82MzFRjyZKNu5ak5cII41lo+gPwn3K5ElI2l9XDqA8tQ9NanoWTLARfg20+0TK46+03sSR5QN3LeUFwQ45yLg==", "signatures": [{"sig": "MEUCIQCKBy3co+/Ccfi+XXnEsKVNT9R46NpcSoswJaHPsg8hSAIgMCR2MPSIoxE6zn/tKmOB2UAP4coM2DLHP2Z5J0bPYPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9LACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmro+Q//SCIEU9orj8d5pugfAfkrA4EqfDOeMJ/cG0821d4bUXdgOs79\r\niWEKMANbCTZYMvP+yVH5KF1ftfkoIT1BZR4PX3Vqvl2Alue2KejciqUir6B2\r\nSOmV/6hqExb1V5aQGgEItm833bBMcrjAVPhUnDZgo1ZDD+2M+Cbtxpn59DFt\r\nefdM+Ef4KldCImF26GqviKvjeoql/SBA3CG7LzfF9QDayxRfu2dx0j3bZpqH\r\n+OFYuvvjEQDOYHCszHIYELG0UWl+MZWR+UzPyidYSqG5iLfCGmOYhTGBQOUD\r\npP+YCiVo3Txb9mLiNcr1JIPdIZphhU89X8AIArYNsBPB0k7wmfUVviPiw1no\r\no9OD39adWCFDH8fUV4rZ84eXT2rZpI7R9NCzHxmGmUggvG+fgRSbuVdHjMWv\r\n/Pz0e40l7Et7IDev52oSj7sYyDs2IA3ToIZNCklhLKnwFy0dXco8HlLHN7lV\r\noCeGkjU9GIZLjPiUetbO1mKpiLFvGgEH2gcTZUEzDIsBGU3lzTGlu0xjszbH\r\nynNUClCDHdjFRO2HcHBdZlP19SLnghztHdLdmhiAt5nvxWf0JuuOJpxN/kvg\r\n7VCKzMvf/YrXhGjjsDaEB8xx4h+1xOKIwuyBXy7MlqKLedlbgIiYujJnbLRb\r\nkKavpVbJuS/F9RtklLnlZKAH+h+5JkuKWps=\r\n=zfzr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.47": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-slot": "0.1.3-rc.40", "@radix-ui/react-dialog": "0.1.8-rc.47", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d310961afbdac185708a19ae6e22d149d6172a89", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.47.tgz", "fileCount": 12, "integrity": "sha512-wM/8ikDUhG9vGr5Cmvyounus6fIe22ZNKZNUT1UIt5PfSHiwlCl7T4hB2X3Ks3wjPe/wfEWWKzbisthphs7EXA==", "signatures": [{"sig": "MEQCIAp2lgRTy1Gl56m3RyjqetGMgEBgp97IS0+E8tpMHEa5AiA/qQ9Uyfzw1BapipVUh56XjcNAesCRsG4CUu55lIdtAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6aQ//VPKoIrKelNoUdhnfdSs75N3pb2u9iw6JrgV2hmrmgMlrs/O/\r\n63jN8FpVpseI0IISN5BfpszkZJnw9ujHVgte1dgoDytokro+Z9gUetppmKXY\r\nd4vEDt1z2N1xEcLrZBUo+/gf1Ep5ppDhzHo/U9l3iAn0FE94GD9onIMOj79y\r\nmoydH5Qo7yqWZPQYRuB9wYtg/3b3aIruy9xKJPuDTmYSKRMLj9ILlXzK6c5B\r\nUKkM1KETF/O0rJ99rOKzkY5AJNeomQJTU7V6Y39s8BOabOuXQUlxxoMXykFh\r\n82nxYRSnmv9OXzFTVftqKO8je0Jn0gWu0B8QkJbOwZhbb0/fbnKdd9Xa+Ie1\r\nNxhweGDH7XpcOpvwYAliuVcN4mhqvX600Ykhn119dI6O0lbN23EwAvsgIqEm\r\nrCbXssb07LWVoeoLn/qhayA62i7PEvqtlTPsDESVq7zZkZmhI42tYOFj2c7Q\r\nxaujLmLamrWsrXX5wWEQLAXsetpNkJk7qr1JsSkwdErrZYaauwsRfnAn/bM0\r\n63aOGHYi7DXzGqcbhhLr6P/l735WHAQE06ChdxANf3oV/WVXWRQXp26ilMnn\r\nt77UeWPApneHwezl2w8hNb10b8Bmv94gCvdu8b09elu0nWjHNU9mk9f7JpQJ\r\nL4XpSQnf+ys4ZdWQF4WT/hjRY1hPE9lGT7o=\r\n=taBj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.48": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.48", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-slot": "0.1.3-rc.41", "@radix-ui/react-dialog": "0.1.8-rc.48", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "555b63c56df8fafff3a487014511d03f716ac518", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.48.tgz", "fileCount": 12, "integrity": "sha512-I2iPI62Lp/U8wVMw3eYtR3WU37/2SLXzeNOpMQDPVv/LRV5xRY8UZprjGHD7B3Aq/fqXVc9njllkzxZybA1Q2Q==", "signatures": [{"sig": "MEYCIQCOynZz1UytTyv7jS7ID+rasz/xhOhnaHu5y80FSvq70QIhAIXufpDB4YMzBm9pCKSaq6oO8L63IwiR7IdPTyx2rgPD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaYyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptLQ//Q8FPjJOB17FedWObzsiBx+kh6nlcXhBcYe8yISHt/qciTJIu\r\nFY7FN1/kbh7mOA4m2AevylF1R6nMxim24bHDOdf5RsPgQP6qoRlwoJvnnhDb\r\nxPLI6X8XqC06KlF+yGCw9mlnCs1/8Ph+ibWCmY6lf0GHuUhfbm65g+uT4YOJ\r\nYU/5EDjKCwcfBNoLIaaUK3d3qzRwXHtGKLblMPKLPhFMtIb3pRmLUUEEOvXT\r\n/pf2Gs7n8/GlsxR1BPlEi0CuNPYxHeGkZhHu+vc1GnHzkxAqzZSGRCOZJ0oW\r\nsih6hL+doA7CYubcukAWeQ7KCmqqTc59d5s/RddKs6lIY1xYPx3fVn3R04n0\r\nci5RNrHAsn50CnGvDX/QIJeuTYs5f078ACARzkH90E0F49m5gsiL46KcZkPP\r\nJCVIGCWZBWZTdihJgIINtRtq4MDpVzSdOEHHhTvNUjhxhg+B2VOn5kBrMswT\r\nJ2QfuHn/LqSIFYDhZVvzakvZtIlm+Oclkd4Mf4Nx5VxRMtXRkCY4jT6k+K5/\r\ncGTvYFA0oHlfHNpxP/nR5hdsStyYwcZxLygy+GXqxLZesZmK8t2glW1Vwhxo\r\nGxLthwcbjUMPWJNxauuBInjP5+k6sTG+izkaSLAWJSTLFbmVpS0TXHQgb3iC\r\npU9xeDhK40Ft5ecMIrrzZFxfWht7aJXHVTI=\r\n=CCwI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.49": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.49", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-slot": "0.1.3-rc.42", "@radix-ui/react-dialog": "0.1.8-rc.49", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "340eaa2e199f977f07742401abcf84f91faef094", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.49.tgz", "fileCount": 12, "integrity": "sha512-lfTlPXPyCKqxXxBXXl/AprhHrkgcZRXAPlEvqC6ZIj64R4YhSVt1zIn42ne0qpxTBGuYn5SwH1UA7izj5iOdRg==", "signatures": [{"sig": "MEUCIBtw905f94nlZwpiuZswKHKJr7DQShX/xOCA4AJU4CfRAiEA5GPIGdjHUqhv+xiMtpLw410Ktuzj5XvYJlZSEKqPfpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEXw/8C+L5slkF2T+yeSWbgIEVtsyKBWD8WlpL+PO79pINGaOuV0vO\r\ndmGlHRdz0psDkmD7GQUVKM4agqk+2oVTTpTSEqlsBsBRLpuSYj+b6sAz0HnX\r\ncotYxgjJpW595v1DGqd0jeJ23ssth7NOw9H8bHA/KO+lluYKmUkMvo/TACgS\r\nXJFee3cS0zmQV3KFKeKxZcZYBpY9d+QyV18oiPvmnBa2YTa+KXX0iJwsR1Aq\r\nbjprtCNVfqQcHED08rTi4cKlbArg+SP7QIaz5f8Rcvy2+/MPkVsGmFig/w7R\r\nuVoL/yNxwSUgquCTiB+yRMJyuXCv0xqnSNw8GImGto5xSC1lOnyxgddUhsFS\r\nORWz3o5/x3cqGp+pW+DKQywJslq0nyleyaOMjdzOoUmqyAqfvRV1RxzRfHPq\r\n8xIklnoEq06d41Qh4CViu4dkFEvReKXJqqF6r7QvzLos5gYfy+lFvUQrI64G\r\nTiQFb74K5Lm1+H2asLKNUU7CbziJZ1hyXxGuNG+qRUkrOpgZjgl3DPMgwmWT\r\nWMCB76Emj3qT+S3ObA4erTOZJJAZgboJY6CeuwU9om/+1XOOn8XKvhn5R9sI\r\nkO/eL5neS1NNsbmccqDZ/bnolKmmgrvG3iTdke/kERDbWtl7sy/tTAra7Lk+\r\nE95QrmSut4aPASjYjLoEX/QdNZJdOL1sbLw=\r\n=JkS/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.50": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.50", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-slot": "0.1.3-rc.43", "@radix-ui/react-dialog": "0.1.8-rc.50", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7501ccf0dda0e268b7638c585cb4b67c3afbf1bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.50.tgz", "fileCount": 12, "integrity": "sha512-9WjwUYrzA89YQf3LsF+als2c2CGW0TpSwBoHY97jY6gt5QvDZBqZpCzCIZNZeFMftq9UJsD5W0eYN09ICSqMbw==", "signatures": [{"sig": "MEUCICXSxRFIXAOWorBBZGc4qebLy5DxuPYyuU0HxXWgcF3GAiEAlgcQWAKZ14Y69xjJ49RYahEPgWVlg/jNF7FVHxihrU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrmnw//aNoj3B8fVit2nPv1L8gF3cFECLGIgTFTUGt13BntUSgqgqrW\r\nuxARHnluhMPoCaZoZ3Hq0tmcXyu+ffA+IBuckbGAxT06lGLq5kOUzUIDCEkU\r\nnAPoFuUqFx4Ah5W5baLxbSkIuppSIXywqoQaohoxguELRVhOGMV0/c+hLUfS\r\nYPh6pd3mWwvSF65/sgIOrtoB/wPUjrQS/90O3nQC6adXtrdAo9bCtKzuIkvB\r\nFnNlTOSstm3Egwl2LFulXjxffoPYpsh6Mppvl9HinO0SdQtSuTE4wSsrG7Z6\r\nEoXiDQry2/SAMTqVJtag653tkqTiKROTlFsGlpsCg26Hk3S5PMQ2Atmgl51v\r\n9s7mZXvhLZvhtNToy0F/j9Y8VcMAx/apHmC0I3ncGVyWgtiuk4Y4T9SckRND\r\nRhvMQt4V44NOXnRl5153QJSeQpaPGWa8QK9x3cg8oMl7qAmvPhr3htsWsTdo\r\nyWFLuAgpyiGCQqdrgmHOp/9CApsnlPm7VrDfvZ42R06wMOwiPqQO+jCGnq7a\r\neVlT2LrY4VcZ91v8r2kVMGe2WjhiHG2Oz1gyDj4sLFxQ84yuJmnw7ehogD+e\r\nCJQqwF4SDdthkH+2f7oQRQ2boYYX/8RgKSVvoKmyqG4Jt8E9gm+ri70ja2+/\r\nJ/66ILMHP2lql0thbeJjBPNBDe4Wd5R6oxw=\r\n=Jnji\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.51": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.51", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-slot": "0.1.3-rc.44", "@radix-ui/react-dialog": "0.1.8-rc.51", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d722e9998d57d1f1de3afe2de0fa03c6bed8137d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.51.tgz", "fileCount": 12, "integrity": "sha512-zmS2lBa3lxqXxxZ8NAlZ/rgjZsXxFfYAN4+o0qIv7zyiXH0mEQ7qsahqkbfV1PxK19k+zQ1ImHMBYMNdV+0asQ==", "signatures": [{"sig": "MEYCIQCgd6ufsE+uzgYu9QmLmb0ULpeg/MgUfaNjHEuMPnBLOQIhAO6R+7nSZDcPHg7wPYGEafjYW2QaRaCkbdO5D+JJD5ip", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XFuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4eg/7B01VGT9H95qXnCxiR5l2J/JKbQq4q+o1LbBzqGisSG5KqquW\r\nzCCeo0AGUINPdgsZKfPAjEr/lnM7UoI0BIBg5SCjojurU5Os2u/iWSPX0oZ4\r\nWFj0SqiaMDZUy5wbUyJRrJm4oSbq4QCai84IzstLiUXQ7fm6xKZOWaHfk90S\r\n1wCM6PNBWCpWujeJp25bAOKVEwiWVILg53KapFdhnmckyYLX6DKPxhhME5Bt\r\n6o1tJJUyW0I9jxw8IzamNOkDNlfnJTnUDs8JTUyz45lZJTzUa1hvucIcPl7Q\r\nRcyyewp9U2wGRD6DhfsEQUMs9SqeVS9+kZwlkOINos1SGeGNxPUvTajB8Q2G\r\nC8EKotVqwZNbBhk9J5TvrejiR07Gdz0uRPpqkPF8kcID0xXmUvx3fQi9X/Mc\r\n0nRcR0s0+kiZeG9h1gUkDvkehKcX1fy79TAPqpJ8I2GX2QY0oaWm/MUCigHW\r\nLoEDSB/OHFKnXJWhas2qH0P6qYBRR2uk+KI5c/Drjcs6KRr8sPdSN7y1xPLP\r\noqa6TQwfOxxld+M9yFWoiTrdFeOGWylAbxuai/axVOQH9ffYCO193u/jP9yH\r\nnhohH2ZvlF3ra3+4YpeKeeUc62H60PHA4prTsBzpeS4fAN5EW1kjEcRk+9+c\r\nYX/O6maAZbZ+wQnaLTC5EB+tkiCwiLvT9Zc=\r\n=hzBj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.52": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.52", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-slot": "0.1.3-rc.45", "@radix-ui/react-dialog": "0.1.8-rc.52", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "312fd32ac148f29dcb885cd4e2ac68436668d931", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.52.tgz", "fileCount": 12, "integrity": "sha512-7GQKrxRCHPN7oEiK33HB3H831E5tQdQjbxHQYbcx7X2+83E0+vxfyKbHm9xCbQ2jAX28ZRDpaDCQ3vAclN51EQ==", "signatures": [{"sig": "MEYCIQCcdZ3N156r+CUggzp6lIEFgyosJYfcOaEKKmhQpcFwxAIhAPNbeWT7nXDaYUZw1Y5FtPULalVnJAIoem+VEd1ROtt3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaMg//bKfCDv0U92R43h+LQDAfvVQq+7evHuTE+7GdMplGXPlna402\r\nH34rbyg0v/9OXrmWe8y9yLkAB2zIymXKT5DDS4MBrOYERyA/zZZ7ESOINklT\r\nFRQKSmQyrhCxlGHhb4i/7fYg68Yb+OLTlc/ZUfIFocrcFYHY0U5zgsShngJS\r\nS5vODLAgvLhbG3beG5MB6fw/xqJESyPtoa9Zynk1wBl+R3ekQHair12cJVk2\r\nnSfsNEFs4OYPT1O39tr8IO2Hm9prxSXfb3JLzXs5OKRrwqiUuFHD8I81MJGt\r\nI10Whs33S4C6mk6hCsX3HAY5xOyk7GLBYZv4Zs/FN3xMQPO+0e2QuseuaA5P\r\n0r19DHPkwUgjM/TENnpNHlj+vUNkjZjN2EB0pr5lKcPxMK2nuTH2JJ0h4uW7\r\nAVByrdRww8Kqg1vIVAFj6HhADj9pbTzBQRb0dTNnxXxloJMYj5cRvaw9CVcc\r\nw8Ks8KnkWX3jVGiBux2MT7vhBqPfTEBxAjPrhhtYaxggOAKZtlxFT8ooVUZH\r\nlKrRagmmyxCKglOYKKX1p6y/rVFctRuAtjcEaMQzCj3ion5FL+GVId0LTe+N\r\nsP60OVAQZAU6PtfQwtXLqpGqrSMXlc7k1kbI+LMmVehF9INxKpMjvA6NWSIe\r\nuLOr5pydffu6s3qVs79FfXvO6bazF50uU/U=\r\n=Bpe/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.53": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.53", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-slot": "0.1.3-rc.46", "@radix-ui/react-dialog": "0.1.8-rc.53", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ac74c24b45fd13c2176a89bff2f36a7223c9a8f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.53.tgz", "fileCount": 12, "integrity": "sha512-XTby2vP2mfnvWMxaezW8UCohhkNKX+LkMShzuicrSVlCZ3azgxLz4LM/AQJs4LNNZXhjMBrQhAZqtOwsydM0uQ==", "signatures": [{"sig": "MEUCIQCQiUkF1kmN9dARcco+m69X4FC82OAhqp4ub8aQlNr53gIgFV8I6Ul7YBG+ksX4zeikdhJscvEsSM3hfXthyjQiIJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmom3Q//Vtcq7Ce8nBXx2diSWZ7a/73T3ICZ5YrK7Byk214vjkF7gKvM\r\nG0OrDDuCK/sx4F/8rRnEtyaO+wIyywPklrug79VwGL9M8aY/Z23uXbn5KBOm\r\nL3Sa/PUpsfc5HI0GavhMiqnd/rN2PLgrreclr3HFu01INWebTp7uQRwg7PR1\r\nJ6PuYsbmcQukqLO2FW1QdUx3bZ0BzDQKGaUPmlAo/raQpwAFY1jwSlws0FY6\r\n5kbUGm/iGGkoRx8Xx5E2UyUl6uub8fhpNdbnapmCWddtpSiTqFySyxrwOcj8\r\nywpSNOy6iWjcnld8HQVtVTQSKDBRnVK4jdnBuxqhMonaCgaHHBphUDVOWV5q\r\noBtH0Y0rFmq4Biq1MLnJee9NBA2Ip46tzGcPsKIOiENx8XQ6kw4z6HI9EdHt\r\n5WGdzvLYgnoTGMm/IgcA9RLPF37jPQLZCJWLvHK2mCN0q2sOVq6vJ9uQEhhl\r\nBPecZyhcWNVaQYEOPqhhbSzR6fnDc9Iuy9gLTQZka5FY7e/x1GCk3fB6i9Mh\r\nTzh9wPN27Hz65C19zTmdb+lPBvmGyqQkG+e/CyrnMBssw37Sy9gla6GFbcVT\r\nQ+DSYBth5+LJNaA7go9Fp+L5J+1265b5HFlNws3DqzWvuYjlzkqVzWU0uSEy\r\nReP3Vwzqf9K+4Ru9r4gCsRd+8Uflh6pbt9Y=\r\n=jhDE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8-rc.54": {"name": "@radix-ui/react-alert-dialog", "version": "0.1.8-rc.54", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-slot": "0.1.3-rc.47", "@radix-ui/react-dialog": "0.1.8-rc.54", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8762fb156abd94454bdbe4de8ef4f34cb533ec71", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.1.8-rc.54.tgz", "fileCount": 12, "integrity": "sha512-FbsWtMg1B/8kD3IPmjVQD73KK24fWzyO/G6VmtLiS4RrT0gAru/f04yXsX6UHWCsqXUx1wMs5xc8fWuxOZQ96Q==", "signatures": [{"sig": "MEUCIAM3GC9IOvbbMecwra11NYn2uwqTNkSSTfwWrjBF7KXcAiEAtKvjQKKu+xCRp9TtB2qxluqxFEpdSVJ6X/3zozzpu1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CC5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNLBAAmlPRAxVtyVrPx7EMBKPtCIWztBJZiGKlCzXVXgug8Oe6YrkG\r\nT69PEFHC1dCzPXfrgnEDmEyvY0wfCzH+c9eQiuBA4bZlGJ/P3LIZ4rsuD8/v\r\nrUXfSzdEhFE3RJ4pc08zTk1sF2yFSLfj8yF0geBZLzSwOreC7orX5mEG5/dx\r\n0bWCIaA+agaxWDUlcQHMVU7O9tVlNh2x6q0V7t9J8b8VoBkYUKIEw1Uoz8vC\r\nqpLFECCz1pZZqAFu9tBBDQN1wIPf0w4VsRovezPiEuLNiBD8SJUsvNDebdnH\r\nO5Cp6fzJjkQzF2zdPFWvmXE3+1do1+cE8HnIBUreKL1JDWcRdCwwokLWA8CH\r\ndJ02J47GJUJmAVg33cP6jxQiHv5RjJpmHJ+RKqv/wpelR7lu9WiW75/C2FMm\r\nBJqwZgzgG0zT6oZomFwb0NAcsEypM4/UJPAb3PHiNrvdqv+Iy1EPhWe7rZNF\r\n9S4vLBoSnMpDDvKRgL6pA+MI/lw6xI1JGGWjg0I639CUanlHE6n5BunVPqMy\r\n95iyh3qtNLuq0Ai7V9HqHBGr8jDNj7s5HWC9T9pJ7X4QGHPC0FpPqp+1c3he\r\nQ+oj5KA8Eok5L1wfMyyINeNpZjfxq+buX+hKe87iFnEcesTfQAvnecZPe06C\r\nUKv8p3LxgGS7PpRl+I61JfsO/o9X0Q6Usl8=\r\n=HX5J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-slot": "1.0.0-rc.1", "@radix-ui/react-dialog": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3c777877eaa1f7ba02c86a470bc73707707c880d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.0-rc.1.tgz", "fileCount": 12, "integrity": "sha512-Jr4liUKylvyaYJBJov1iiFNx0r7XBHx1cYxHKu+NJSWD2aEri87gj4f7a29jdbnv4hC1sB8ncC/Bscn7V58ZZg==", "signatures": [{"sig": "MEQCIDUaNEY8LhHm9NIKBgf4XrYWreTzMebQakGlMCkSyWd0AiBaAEWxUoq6MjJLEA1CGZm7Fc6VqGrdZhItxM/C3s4QWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EurACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUxRAAn+oRBj+chrSylNnd+MhqDSvAOa0LGT+yiRT/1WeWE4gTa+wc\r\nz159AylTUaNKaDJuPRgIBuKpNJu/LrKn7riopShYUyT7F0C0QxjiikBwiY7H\r\n/Ua/HuacM0FXZJmEUseK1N000SlL9Z+En0phalMXnFwlZ46ekj3D2g8w4iC1\r\nuavUkDaXt6MQve57YL459hJmolromAME5G2hi8sAKr/DwXcSP/qjYrzaMM7D\r\nLEskj2e3x64ln84uyplA/eRl/vjsulJqPnBOEAjYFGNZqrggLpwwiYG9Y5ON\r\n5EjK82Ky1iu0Ui8rXUnkd2cNjaKlOQuvOvpHQvyvifzL8ZDH8vKENbdxh79+\r\n0dJX3s2RNmMSsFWBF7uQE67okcFJq6vS1jte5MRqh4Uwk0hy76jeMXWB4Ru1\r\nRyesBn7nkF9HnNzb2SZJxm7ot3NtESOcazZM92N2w4DrYt6kBlml2PQQwcIP\r\nL/GuLInytTkUYIdZCrrgzyh5sb4DKWYXKGGIp0kh/Dlgj8sIKhmd30FplR8r\r\nstisVfg+Vz1iKaIgC45ushIDcIEFTtyHe5syw/sZ59X8HcNJmqOvckcbzste\r\nhyDkVxIPqUhv3QiRBXi11OqTcKtKLqEhBFcKAr9yU46o9JSHPgiuofMJv7JO\r\nzlo+rZmSapmJI5UbC79ok2qYBrfxzreebbQ=\r\n=NwR6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7fe3648f443382fc1765bed9c69b04113df7b031", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.0.tgz", "fileCount": 12, "integrity": "sha512-E2N5klf4Mi1QjTIPqSfAPXFPeZG5ZnnBZJ80lcTzJ0fSp8dqLG6bcxrkCrSU4Gm+HVZxxuCPkUHBfntd9aZUcw==", "signatures": [{"sig": "MEUCIQCczIQchh6ZUzaptwIynLr+ENjyq2I+INwE6oMDbhQi2QIgCwMfIOSL1pMuauMm52DI1J9f+vgR/WNqOP+bXlA7Kro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpo0w/+PS7Hwh4U3op44PscSsQVcsEnQtSc8wgWgMr+GSDFDrUphOJd\r\nklbhfvcFZrGu84f7w/96fg6CTJPVVE0DTyF0hluMlG7nnLDueuJilD/iBEM3\r\n6ATgzzoN965ozuC7NlFjBki2Cr0A5YebpDbYZ69FALqwyiSqjCEVXVOFPgKl\r\nqoUkRy4kqyvPLvm7e8FtgOiXdUnOkjr0lej5gzrBnz23VLLYH/b0RMEjs7+T\r\nJ5d+TPBjJWRp6F5AAhSW2DKqTcbnFseFUloXX/h52TMfa1byjebOYgR6MRfT\r\nhzypJNRtkBgQZhL2P1C2KDMh34/BuhsER4oiSUrnxi6RwlsI2884MJPaRvr1\r\nGarV63x8fd/9QwHw/lEptXSvK0USiaUIssEX26r1CEyFN37iRDzNz/URoLE5\r\nG15AS80LCg/BpGKNj1oizHWANUCT7JofLVJ0XvW5szKcm/ojr/EWZn7Bevo4\r\n85S9Wfa0JiNuZKXrOsl6gas0Ms5xBoot/t/YnYdWfNpyfJHogDEQ3N0czauX\r\nFV418Ns7AvBQrhQ5BT72mfDFDo/z0jWGylSlw0vho5GeY9dS105zAG5IQ1AB\r\nOw2L5PTi4nxc5Q+y7hcuzx39Uh7mdScjwBER9+miEtNP6oqWtaa8AqtvLpQN\r\nUroxZAwlAwAbYgWsdPaOTCZNCQLco8gH6kU=\r\n=awWs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cebf45c395a9381831beba25285563f8c1bac5d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.1.tgz", "fileCount": 12, "integrity": "sha512-9oJtJCFC8DThJ/2jHYOa/Xh+aWpuyWppu1Ap7baMvWg9VOz0+1PMEiVqGpYLsl9fvtYfPcBHJeAJeT29ZHF0Mg==", "signatures": [{"sig": "MEYCIQCMa/gzlTDhI4G/JTouEgJ6SZIWyIkypH+W3FPqrfcm0gIhALuZW9m46mIfqkQSGoIVvNyfCkmQH2K7Ru0BCErJ6ioL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2W5QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQsw//UbbAMnws32kK9LEdJHKTGY1g1omLr7yLkeA2403rbjJhM9sw\r\nMuvOBUTuRP4pOuUkD+ipyhVafWRU4uXhAXqulo+uprkS17NIAMqg3tUe1YSa\r\nL5Rv4FtlWchVvmMBa3SxF4pgLsGZQrOi1hNbXNKSRgkNbBctkGmWK/OaET/2\r\nbUTN0Zg+a5w6SOXBunheRduz2PnzcMZxway0UR78V546a1bwt3OsDr8MT8Zk\r\nYR64khGBg0jmPkt19zgFf/zn1w20ANcRlCYLR/uOKEZCS6e8+vgnI2pYh7J9\r\nunBwoqyhIFlD2LSYTgYI3ZCd/p98tX2bduu+w7Nks5SHCTEgmC7vc0uqMNb1\r\nUIKkoEaOvNApoR6Ujn/NdFl5o2kYBI3b8bXI0TEUuoKVABDXPye6aM5ZPR5M\r\no/kQPxOZeGEH8cLbiOP96PiKx+X/s7FC58Pd/5SmTBJR9Txzhep7Crif77HE\r\n3mw4r1MdaChff7DwU0WfujrnJ56aF1u7MifufSQ0NOwmj6tKuIJTJaToUkX2\r\nJsiw2e5KyOvUe9E2uZw2LUOEwoDQ+GlXcDs0KhECZmI6HI/c8yxDM+94slNO\r\n4sIFt0DDW8crZXMVRHXYdjf0j/pHrE8hZP5G8NecvP1h6Rbq7/LDeAIqMCKn\r\ndgA4DhTLNPU5iN9JTyqMbTLsr01uvRY7tT8=\r\n=+y1w\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7f54b09e2435504f9b295aa312b7943c2c2ebfb", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.2.tgz", "fileCount": 12, "integrity": "sha512-2LMpsXseBGkdqa4JZQEPcACy9EQet1hA8DvLZZY5qG9crwM/xnC8fChbF2MEKHIzvJB/XIL6OlZseduX0X4+WA==", "signatures": [{"sig": "MEUCIQDzEcW+Ubz/G5zwQQ/pIbiXOXLKSIihCt+hEoLSP94EJwIgVNz544J4Qc5+7vjYpcrk0WR+STuecGPW2qxq9Yo60fw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2r1hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTmA/8DItxe/37FmOyUO6fxY2PuDTQFMQo2NKw1EXDCwuLDOj23mlf\r\ntDBunzPK3lEa5vVxDRcNqfy8CvbSqS4sH2BtxXrhbXDSOwgBKJ2jA0T3WQaG\r\nwMsUL6ZIAYqgIlIEvmCkPtsrpyrYg3c5ktCQzX0ORygmPWZRII9jbr/eNLrY\r\nsdXJH7S79itrFfblp5MAWYWoMuRcp/CqTNmYwAiuzZtb+kJPk1QVqfl1X87T\r\nZoTd7l3ZRLNAeJIV09rTnQDZ3mhUEw4uJNWb7rwdggeQnVK5izIL1WbPJ5pN\r\nIurvIjxbA2IWnrK4XKdO8EfIpQteSkjF42h1K8d73udvSyay1Iebr0GdBnPJ\r\n4afD5crJFPwDaHXNKUjpYf8Cv0l3W9QS3ryMERbB2lVQvyf3Lwt32gkubqL3\r\n+JXyHMk2w/3TniS+B43not0ig1E+910L+R4dEEA7AH2leKWXQ6Aee8wnsLID\r\nOJp02nWTpW9uwsvutT2jAaYkhOMawsVo3TP7iZk2DensnVkDNIslXi8lQ3EF\r\nqEXn9/Lf5syP4IoUuP4HTaPOAe12K5SG63/HL9e6syJGCWQHUvuBTmTs1QRR\r\njXFC6baRjU6v1eUFHXtf4t7eJ2pakuNlyjVaE0Y2f4OK1aJbbEOfE/DXmBdc\r\n94zlnidwtldsinFUd8+LEIDmZ0yf8L5FfzU=\r\n=rdzR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6689385e6f2a065e0e3596eb93e08ae24173a06", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.3.tgz", "fileCount": 12, "integrity": "sha512-yRwr94+sCHqwIWEkCKrVUratijV0Xtq2JTX5xOMkGSAXULzPPLmUQsOzFoAqakMe+G0fnUkvPr1qC8LZVhdUUw==", "signatures": [{"sig": "MEUCIDVWCWNzdN3QMnRlBN1n+oMqKPJ2PCVgy9m31DxtctkHAiEA8rhEypVH+RD25hBjQx7dNlkjEVv/YPmT/XWirUxJ1gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFyZNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTuw/6AwjOMK8zwChf0u8T3JKCpLFhs66bfK7VR2xH385u7ByTwMxG\r\nheLdFddjUG/BVpVCtEPXRstIHqCu0gkXzgBdCOkZ/5ghXNbxssieZG9aaSYz\r\nWd8jYO7n8a4OBu0jk6psnvGH97jiOgiClF8WomLO1w7RhzHC2v625FHN3iX6\r\nK3RBHRthrmvWihcuB/f932chRXR3HEWr4F/4cJJTeNwDoA1VaUEn2FMZq0Lz\r\nTXgHc4Zs4ptc38nilNpAwPeQDveYo2el1by9cTXkmIZrfUJt8VwgqpDgdd1W\r\ncPhaeQlAXOTI3NpQgYuC5rVSpYFkTl1soKzMviIp0qKJL1vYS5cYXU22euba\r\njeXLqGOjBzhog8FZ+WK4LWcKGstWvZAyAz5p9+WSryeekeWPXVH6g01wA3km\r\ncxKkft9YZ1+rL8Ir/a+unKfqqvyyqXKQulSnnTjstgrn+9S2JFzq5ftnmDFQ\r\n58dkhjTuCY52F2MvxjE0kd4Ib4Rz8mHFIhQv/N5CeuCAcn4OYi9N0APCrCi+\r\n7czr1ZwZwsnCmLgJElZxWbkj6Pvn4PF5PHkVCanJd5VmQfh1EJHqsd48bp5V\r\nbSGN1LZ8n1hgau9kWLWYD9irlvP3D8oHXIplXbyiY8FdxyoCGUjQt0vjUEjG\r\ne3stiQ6cFg2ng29nd0QOEEZL6DCccJ8NWGA=\r\n=fFh3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7e76c06190ba9eefa35ba9b9422f372ae9d7d35c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.4.tgz", "fileCount": 12, "integrity": "sha512-AA33/IamsD/r7LlGeAa2WdZvGoXNnAL5RXMNXibF8JyKJM3WpcmGQSaHRkv67GgGJfuqNmKZuAF463u8pBWzyw==", "signatures": [{"sig": "MEUCIQCJlkSNIWPPJRUxWB5YxPQUqxQM9aoE1RgWevMLnrN7JQIgZguNE4zrm3Nb1gQ7Y1aO4MM8vNMmuLL+tIamPaIKzYs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKbaVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9kg/+NqPff7bEkZlzmdK1q+GMxo254+3coVuG5B0zYuAgyLmJULL2\r\nK9bLgJO8zULi/d6RLWEGhb4gGp9ySgxHMmRRMkW9YNTDlqdtCUZQ3KvbfZLT\r\nwgt2btFhv9NyHja3vAmHB56NabtT1GZ7REZR4tl03+t9lF9NkD+XJys94gVB\r\n9idVYbbw7x6xssD9CN3TRy7C33B654AJxFoOWWsU1WsNiB8/ArfzAa9gnMEg\r\nrtcJJtf4Zg17Xy0m67cJHd4Cz6xt8aoX4G7J4b8X4xZpE/fbmLrYTSIfFlEm\r\nHJRcHaFwfGHgV+ovg3nCAC7BC8Q/0j3M4EYf3bYO5orBJuJ1IK8uTHDWM8ny\r\nku8/yym+Hw9FbWl228tbUBiPELLzg5EP/kmGiHFyyKoGTvfyzAwQxZ+yrp3A\r\ncSR+9M6P+PBZosyBgMJ3SwzItTca4rUTjVLf4lZnlesghnqFHQZhPbopf9g3\r\n/TZZkkz4lfARZP57/tQBVLQ7yHBIC2KcOAPJlsdtRy6JJR5wdC5zlpN2Cmpq\r\ni3iydtfvQB6XA8uQjd2lhtHfhPXH6Lz5gimyIDeaNwzasF4tqSURnNM2/9VD\r\nLiudNbts+FiJ//6I38hk2vM/xt/PgNizgCSa3lCM1bu3LaupvZePLv7c5++u\r\nGbKNKtwqfYdyKvFaA1R2WmV5eFzjmhalsfg=\r\n=cUod\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "77878f9bb73dae594897982a8d592a73426ff5a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.5.tgz", "fileCount": 12, "integrity": "sha512-Taoh2mryXkk51PJwI9pAKUxtVLipKHPriqAoU/VCVU7QBImIRfNuqmCWoB8vCz9BK3RHyoC+qwVVvLQECGdv8Q==", "signatures": [{"sig": "MEUCIQCqfgMRWPBRNV8V5iWATzdqCy9pwNeppvloRnYpYAx6ngIgWf1+xU6f/V2+y+Ec0i2GbigMzHpCYG2uwtbbs8heAD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKbveACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFEQ/9EJkhcog8VgWKmiogVEks2KLpC18TDrr1cIZ2p/RaXsOllCpE\r\n7FFlbBH3gm11AsTKB30Zc/xV+o0b9NwjVYv6Ii5pe+btTtsEud3jFVkqBkAq\r\nCUj7QFzuJGYNpSqgj5o4vOzzi9KT0pQix+qwN9xfjD+2sD+GrV3Xpdr8Xupo\r\nu73pBq6h/bEp5A6dNOP/uUp9fUZ43ldkEmt2a8h0K45G8KFijp8i9XUqyl4Y\r\nkrhxCHOhjwN6rjI6vSTTmP/xXkpts6StJmgrSz/5Zdy2FmQVrAzpjxJW3b0w\r\nBRKdePSNU0wpDTk/CsoCqS8HJMX8QDYCAbUJj2QG2xMcD6fk8UrFd54QYUaZ\r\n/Nqz9N1ANcjYfb4YLAsC+IG/ukFFJtUhJKRn4EBNZOUM+nx9PZFPC80eHeZn\r\n9HkI3oWvoc3QillxI3G7TvUVZTyLc/SOblhrp1k6XhNV+u+Kdr0h+woFRovB\r\nohu/YRaC/bMCYi/CAQ073WQihsSBn+7zdYVgDLmj1sMmadC+OYaPjy4ahXao\r\nToRfDs2RQNOIr8DYsglnRLoqReTay0yI/LJ77pkyf6InsGzXRhHgJonBIOXM\r\nzbbTlrdo0UJJEFk/HoC166XJ+FZ+jiRu6OdJrwnipMkao6RdhtjLMe4E7gDq\r\nUALK3vbPZYp1qrFIV3zQlh6tjTXBlMchD8A=\r\n=THNP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "88f8c1f4a1466c3a9f9d79ef934a89489b0aee0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.6.tgz", "fileCount": 12, "integrity": "sha512-kEFwIcC5aA0UNHRYh4qsx7y8RE36s+InMfTDtqpHMd9lm918DQwhfqEq4feG8ybAK+l+g2dJqfbPkat51YSLag==", "signatures": [{"sig": "MEQCIFG93Mzt5JkD8FMlgEBO6GzPRTa1AtQIzM/FTqSGMWCZAiBogmdfrRJoPLiaA5cCERkVTZbAz1kIReHYc5PHF970Eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKu1vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV9Q//a/q+QAkt+nEW29S89TtmC9pLXoNkoumZEfjFjCqH4/7Ld3t0\r\nGDWqdgO7p1GOhg2bHXqAqKXA6IUoE6Zkqn8Rq9S/tcadHRhvmj8hkbzsmnFH\r\n2jn3wADDrF3sJf3LgPpN85sIx8LsQWcn9i3IZ7KlOJnbjgvqR8ma9SiE/7Rh\r\nutg6FPYsMRkT0yRPDGSqnMsXR/WiWGKoNtr0h7T3p902TZRWtw6RsxiJcKFn\r\nq3hUv7DPmWEsG7FoOpuBq+TCjbw5zP2sZiSbu7p2vxd4A2o0Xn2EN1KXEvX/\r\nNnyTztCXShGohEqJt0turKizqEsev+sUzrC0NusWlEcyetjKl6PhGbZrW3aX\r\nQGY4xLXO2MqiLoF1G8EL1Plc8aS55wRF0SKu+3sBOeyJYNa0E/rpsDVx0lTL\r\n/mjApc7EHHRvNTc8ycGvAGdlBH9kAUGJEW9MIscEI3kvNJECuYJRSbBdjaVT\r\nWt0/CLUkSAq8HbesMrJisYtXUycYmdb77QLOKPF1d1F+CuAyK+08q7mMM4TC\r\nfwZi4OQHQXDflL4gayfUaCRYXvAzX2Fs7cI0thXBufZNrVj6ehtoz9YOrsWG\r\nPx2kMYHFekuWVMPQuBTccxmHuJqzTE+veCNEI2QnMIDIXezkW72jxbq3IKvh\r\nEkXTNF5aHicqHY51VWAl+kIalBRnBYPuWEU=\r\n=l4hs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.7", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "41b76673241229e50cd318e018b396dd23767514", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.7.tgz", "fileCount": 12, "integrity": "sha512-O4r67S72pmG3wyVrqNJK91Mr3vgmnw87p9qtoaOrhWsyHLwn8ZYWFWCcoUq1heSu7avsl56oLYdDODO8gogfdA==", "signatures": [{"sig": "MEYCIQDuPlAPaiUeEP6kIVL4mwsTBHRY73lBwcF3AqzxdZre+wIhAO+5u4Unw++Q3Dyzb7RukioQCyLPcQC4k7QfUISr/U/Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMaxBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkohAAn9cWDF6/Oez9v5vKbLqM9DWeDqSBQZfe/nGoe7i6ENUA9op8\r\nyxnUu+HEsnefsuweuSvJeGOX7tnqMIN3ZyX7PTCqsClFxHCdXL7eEDzU9+77\r\nXSDlEtJ1ZN26FMM18wyx8itQlYiIJ+EaYZKhZ7N34BwXWpE/S1NOxpJRweo1\r\nRDmZY590Unfh27JK+TJjMTGbM7sXmF5vRKwdPB7fR/MjogfbZivXOlLIgKOR\r\nzqFqqBG23CJ3/N+9RWbmnYCfA965CQGe++htOwjgj8orxocptp4q93bDY/Rn\r\nxFbpzMrFNv4FZ1ZkhtyWfgif/ytoB0nnVB/JxKWrThIEdOG+FwCiYW7ozFgE\r\nmw3zIGV7eNodtKtMbx6GDhE0tm53soYibc2jLTXEXkf2h/XrojOl8f2HQ1vi\r\nJFsMiYQK2aqaSNqpN53xnDIssH1CqjWfDVOEMWkoPVilA/NknEn5gRMl/xM0\r\nTNSvaq/k5BrmYshT679gU6k/tsQffQRA8rRElQx7XM0nzdrhx7TYXhEzNRLU\r\ngqFvGPMoNNTH1qtK+iI+SnsifvMCn5HRwERMnnBOtXM9MpHfvmKaTuWAT+Ah\r\nHxFbX0HjDViaPuJ+6w+DEAWxxJBzTbDAWqHRP7uNeOeoCJS37X6nLjG5UOBs\r\nLVXPZ24vs+AJeiRQ0+UgCxjqzIlyRBBdo0A=\r\n=Iwx7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.8", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d63b2b0028555855812c99d745946ef4ec8caf0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.8.tgz", "fileCount": 12, "integrity": "sha512-W9ipDb3tyFTa2ip67Ne0naEnDG4Is661Is6gxD4eEvWLkWIntIycn0uYePIxvkDOcfYSTAVP+0I0OmV0pmAJYA==", "signatures": [{"sig": "MEYCIQCOhZscu17DaMAj3qF+1i1LrxdSYOYmNwMmUIv+rWwAHAIhAOQjdVT2/bemQgpVa+MQuCgpQaLKDVk7UomRDIKWt2F+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqXQ//ehLNdfavklTcPG8ECn405ygkvCYCTCbWw1/1tYLyTX8tM0Tj\r\nbqSwN6o/esWZiJpzqOGPPbmW74GnXBOJT7GlU+lEB4peEb6YiG8WVSSbirHo\r\ng5FpijQuOM7uL3L/EezDAYiuViDAuIPC+AU9xMUWTs9ECirumjcEOW6T6B87\r\nKUfHv8zG0HfyVpTUHKgC8EPaFA4CZlCu2aGpFesdDbGGngJAuPHjrM+Z82wx\r\noOsvZROZ8lnhPAZ124MxmXjWNTt32vjpVxhxu4a5+wa3XCZJ186lf5dSEyi1\r\nqr31I4V6LudzuYmmy5t2fisV35DuddoglPW0JM0lcNgOsXpwnudtAFIA50Cu\r\nlfT1x91fGW/MN5hag2iSyG/d8RKrgffxuHNz7y3c0Q4pgEwjtoO4rtjs+a4N\r\nO0GpIDzkLnoloqvX6Sx4mA2vPOCLx+77WUkz3W2xAnZAz+fgCxQiZnou+pMs\r\nqJ+0mNVech3lB8dMDcceBRlLpYrPR/DNRUDD7IYZgMm8uvSj2ydiulR+eBZa\r\nDup2FaRNqs4cXwoBrWAkO01C+/Jjuo7MR6/HA0AQRzuRFw/txs+72VuSaauU\r\nH8MnccuD3btZB/Hn38ZtIYaBPslSKpamvOT38I6lMKtCz/UsV0w737DZBOEa\r\nPRouGkeYXTMBsRzTIe+E2uo4UmbEzzwAJqM=\r\n=tw9i\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.9", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d478c9eef5e863e8c3e0a36e07309cd0612efee5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.9.tgz", "fileCount": 12, "integrity": "sha512-3MH6sMZWg0Jdt2+B5W5ZJMRZYvDYUrz84ayX+7M5ZoFGzFFh1Rnqb5evGAIehCCdxLp3L8CGT6SAyU4B2pV9rA==", "signatures": [{"sig": "MEUCIQDN0n7L4wyW7OwYMjwcidJ62n5CuRex2KVyKG0/0wtTdwIgGUFhE5P/JZqa4FoSAsrrlpVCHXH/tvR+9ETSbP2SB+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKykACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXZg/9EKU8cHZb9JVgUcib4rkXxisxSRqPyHCPHhT3fPFxMwOlvDPh\r\nZgL0kJlXvAcZC+B6cgUWOC/ySaXBtcthQd+o663AYDcrlH4uH2BSV1HNoeKT\r\nPCuEV+wS01MPkGj/TfK/u2Zs+sAdqjs3DmxGNcE5ZkXCDovF6hMLy+NsQE/y\r\neXfZMU4p9Y0R3km6LFybAZVbrVhcrQvxXa+V8MicZbVoItoBAwmWsr0CCrvI\r\nQbgqMiEX9oFV5HxyLWZD5/tAP1CivVgfzECgGtFISIV2P9deKgeqbL0rDfzL\r\nTBxwF/ulOeUDXFoIBhcroy40/NkT36BmYWD67WJPVyJ/Nqq+7fX4FrjwBFlN\r\nKRsM96mdUkMDfET3W63JuBB7rnCHkkL1539H7uaXAbjwmRGwpAzH0XBa4g/B\r\nwUvpuw0qVbhKyyCshNl1IB+qoB9Hw+GxhWXe1Kdc+nJwOtaP3Yax7UEmOfSa\r\nM0plw5cD1+MHIDn3fAFtc6R1U/v6lGR2/MF7GW186j2gtGaMMkY91pM1c/J6\r\nZ/cZDnHqkGp0g+***************************************+1sEA2B\r\nPhYZgTImWWeXfVv84Eb9lC7RFB05OGXApfQOZ/SCfy7DNtdgC39+tJn28ZPr\r\nSntITnWltVUtC7q7hE/iUHck5dX/edsfEWw=\r\n=strC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1c1b5b943a7bf77605f976e47bf93e3a15bec7ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.10.tgz", "fileCount": 12, "integrity": "sha512-4EYw+o2RXpWdJyppCtVD9xt1AXMzTGjQO9/irhfTEENwEh/WLx87faQOJIVqkSFdEg4N8mC39ju6wdiOb4Ie6g==", "signatures": [{"sig": "MEQCIC6P75OOsx/y/wI6rHmLS3akzUgVlLv8uxNLoDlgxmakAiBwP+8IJqxSkVdzXGQ+lwKhBgWuatKA6cBnDwJUQSItdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdbsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6lw//U79aR6kwM75EN5eIWbVn+irjzda48PEyVd11V3QouT6CNYJl\r\nXCFjuPysoKD6Z/bN4yhtZZmRN/INWyL22Ns0JCC0uDt/rx3fU3tfdUl2+S3q\r\nfKs8/3ezqncV53yANn5o0s4y2UWPxaYac1hoIbEWik+XrmvnVbKY1ZYn6jfQ\r\nmqG8sMo4jMPmVpM8LIw1JV7X/ZQ2wsCyoWOqQIm3ZPIIBdFTjqsg2oVWHTxf\r\n9fbxdKVjKmmPdtAh58HRSskdpF+yEFgta4XZ3VzehkGHYfGPBBTRJ9FhHhWe\r\ndOOnmZB6dAXNjAQtVqkYlbIJ7yes16iiaJhAgyAjbAGi31uYtWT+go8CHL7B\r\nTvyfnIaKYxZbphu11aPv++N+zXjtJKgU6ojFg57Y9m52sMYuUK7iYGiX5uAf\r\nHC6QPIz0tE7yU6J+aRUCPO3y5iWBQhPBJ8/p35/ffGJH21ikwFLQR/CZTT4y\r\nZOayNsBFAmLjssjHjHisUyhevMow8vQUkHj8jlA6+tEoiIFNCdB1pXd9qe5C\r\n7YtA8D4toBfTWukJcVvtTNzwoSnqHbb2h56/UCwmDAnz63DNRlxv6mpkjX3q\r\nFjpfN0GKeAovJXCzPtYTZZZQ3ico+Z7xWhsoQ0ywzw5aOApPdv9AryU3Z2ZJ\r\nJV/Tl87XBhlF9Mq9fZ7EzsDEomQBICRVNZU=\r\n=WIjK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.11", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a9211d61fb89df2407f7a30cd9dddee04081b21", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.11.tgz", "fileCount": 12, "integrity": "sha512-znquqjDcIRgNIJIT/JkXOVpG1slm9eJqRu7txEJNuCOZUFx2W29X1Kun7ToLnOuV4TsUs38ko7y8E8HAXQWjBA==", "signatures": [{"sig": "MEUCIQCJGkK+o2lMeNSAJCAUFwVFqJem1WNVcijC6ciIXU2llwIgDJuJlkEQ5+MH0cnUkE99MxwYnUOiTyBr+7UsZLvyN2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlzQ//QNAFPC3DvpZlzAvzJ4UOVU/fmUM9H0t+zwhf0s4z20f/0VF4\r\nw1s6qjDkeiGD2gDet2qGCVjiLQ9/DbuEXh/vLT8DgSm7XM94yr+7pkci54bF\r\nqfSY9jtI0B+CRIotz839hRmQvyptUle9a/RLn3Y+H8KP3JRuNn+8iOGlcMVw\r\niuA+/NU9bkECfVtLgu71NkxzjeLRkYootdHYrN8zgTDlFwWcHlBr/ix2Znua\r\nXGmCSUgD/oqQH1XCtQj8nzEv+6obclU7ewPXpmIMPaET3CNYP73VqYxybQal\r\nFVT0Gr1zR71M4U1vzLE0rmWyfHTLEaIqgIL8L2Axm/XBhZ+Ferp9a4nKh1J5\r\ntKTu0dy4/GC4YKTWGT8hlHiVvPtoHrapqnaRhhaJCz1JateyRBbnhm4GLUEB\r\nMSoe22xc/lq6+tgSriYEfREy04E5XLwJfRvdBJ803u3JqYHehl//CtHcNYHZ\r\nGUkmZjyLn7UzU+0qlUmQ9dzwsrkr1V9sTlzaVUBn33sV3IP8ZaWEOpETAddo\r\nlpg3/JoHcy0Ff9De0FvQ/tsBGllPva4I92FhGvcDicQVQTclaTWc3Xg+KgAl\r\nCrG5FqihRLL4n2sf+vTJdWfqM8RofAOL7TISANGLvJYTtTBVsw9F5GJn24dG\r\nEuUX4pwMOHQnwZFvkvVC0rKIDFscpTTfGO0=\r\n=zAk2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.12", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad13eccb9d3131282fe1823b858113ca66ab6999", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.12.tgz", "fileCount": 12, "integrity": "sha512-BEuNWe8SzF3zqVe4f2NUhSLpQjBOoUkJAGJ8wuQePJZqkTRh3vAa4dZXI/FCKFLNo9RJs18UBBzrbM0FREKWvQ==", "signatures": [{"sig": "MEUCIHAF4Ebe7lDtm7n5OpbcurLrE8IL/2WuSkUzdXUpUYePAiEA2sM0yh+2XPwGaKI8X649GKDNcp/S9N4SrjuAHuOnJ4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOfA/+Mhb8h+PBv25TwziUpL5C4MCydSXWgPX8f0lhiyhVKeO1FEVf\r\nHh4e+7E9jjaatGHgY7BZaG510GD0JklUKxPBwR56CyWFERxfGe9ejeh58s8f\r\nvIujbjG5IcwsnNo7E/9tl6e1esp78iJcUDv2a5UpwxGJ6IjhR9fT1YYcLZIZ\r\nbazLTek5ji49n8l+w2LawRRx6Zf9NpSu7U6+ruWARw9qr/+PvHIG5Y2TG8fN\r\n8myG9ZQScq0s/yJ5xQZe8SKeRuq6mDoypxfzZI2SW0z/Sl4N4GMUhAfclFA/\r\nxVZ/16wBHFgxQGNkiYtlstKtAnb4Abfrr2z1yG7fFueW3VWu8VuZhTRn2IjE\r\nSoHY/7T0mbjfnHtd1wpNV7Irdda579qJZUqKO8sOok28VyRp6ZEyxpj6xDBf\r\n9T2H8fek86QZocKlDpp8X9J8f9zl9wVRQl1IYyUv7avCNzp78gUVDDspq4qP\r\n1YzmKZhmTxs9gK8b//7kRo5WZcmpmYzGNX1OuIK97hZsHUM7/Ufvfa2782ep\r\nGFtH5NZiafgFsEuWW6L9K4jdAJEOgtygADXnRgj7CCDfs9FiNmIL+nvUj+Xd\r\ntzhIwSGV55wqFa8+kPjJiTBcje54BSJjVMbFuHAfUQG2eJzc+jUpqGaSzsKZ\r\naIDg5c1EUbYKUr/u3haLsG9D2d81xGlNofc=\r\n=vEfG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.13", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b7a3b6b07e7e867b226f1419da1712f157fb7c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.13.tgz", "fileCount": 12, "integrity": "sha512-Y50lge2AgBiLLteZqCsCdh7XPJe6Nuy+CgoVY8ir0zqQ/+f2gUWsVwRH4yNcqRWHLeEMM5pZ/LnXd7VpL4o1wA==", "signatures": [{"sig": "MEUCIGFZ/zdpqU6Q8q2O2AaZ1qsQfeOsWs4K4dGSBpn5uNtQAiEA+j+0NCHu6Qzp99ZORtMjKOYSsd2Der43MCnXL9qs4bo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwOoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrP6A/+KevlXfePGQ5l8kn25ZH3IyFWYqaJuxy5KG98N6qZYmTAWezA\r\nCpecna1f8TzioBsmnc23N6mU4zpCR6pR4D0FPU7ZbaHVKh5jlV2Y4D/szns3\r\noMbfzBSLwyhE8cYOaiEbbn8yS9BZka6kKZbIcrVVqesSDqKJOn4lp63STmE+\r\nzlBN4rA+zMZgSzutdlUVaB3Vijm0WEG6VXz/kVCgeTHOoGRtiShvisDdrncG\r\n1aji4W7E4eD6xzLdm8UvpGxgobkjPxcYO6smclOI7Ez47h1dB6Zz7LxZhyQ2\r\n35oxWdszK33BOkN6uszm+3Zx9y4R3iSZ9C7RvLyuYS2o4GRbTAlJiYh0dzzY\r\nNfhUzej4nJO4YETkryGcW6c5Yy8tpw8WHNct0T+i9njPBWqZLwjyIs3MJzBk\r\n+A3LWRFWCc3BYUJGqPp1ye6UtvDoqeFus3oJusPllnXke0CykmhAt4bxfXvL\r\nQsKPn0lyLj3xXHtvzqMEqev0/kX+a8Ln6uQesF0UYOylluU/heq3ftBc0JQn\r\nLTIxgRwOUza3SaYYw/SINCS00WDo0+h5tNHh7vdW0yptkZ/Pb0sFD2luIj00\r\nJw0Z72f2WpS4x+ShASFVj3CQqIBnRP0aWYKbny8FfROEtxEYYLnnrvtnjUyJ\r\n0FPjcnZLPmPQP0IVEL1sxdlYwRql8GJkgHo=\r\n=YjMU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.14", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc492a2578fe48a6740aa6018d83164d2d8f35c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.14.tgz", "fileCount": 12, "integrity": "sha512-qwJNdIq+Du+TFpj88i6PMNeAo2DnAfveEMEqILOixatVUHSC93SayGWgq7hTq7JCPvUIAnUIbFLQAeZCQxVNXQ==", "signatures": [{"sig": "MEQCIDwTakzjw1IYQ3yceSlbclhcUXPwvZLRzD7AsWfHj9CSAiA4WOylecAjdE61VRdFWYw3ENCn/kQnJz0vdw1EYR1tGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqd6A/9FchnP98qgS0Re+37hPEw/LC80zvkADPtNH26vvZw5DhN1xUP\r\n7tE/BK1vgY52Fv0S5QIvyci29+x+NDbd/0Kdt6QBeyUPxdzWfpg2+AdQQbQo\r\naa56gT64BW16FUPEpoKyv+E5Ix9IjW5/aDf7EXYLMSXDnxa7daJORGF/xpxb\r\nilncoHuWsSEm4gykJj4xOyK52ICcnPchb2QcQ0wTP0R8FNn0IjxawHvDk2dR\r\nrxpW2u3E16F3pIAWfZ6JCyyDt89hfXuGZz1onG2/10pZiIMyWlTWCgNqY/p8\r\na41PJe3bq4HjOpMTm+8O7XrCjMMJUBlGy3tqNPhDCrUaPZ9+XLw2/mtGxa4U\r\n1rLB3L3p6imUgj2A4+WKc6eWEVtjXiNaPpereRpVea8EGIr+nOL0+eiMNlpH\r\ndRDskqTg60DmlaZfZNLZUnNmhx3SU5KLOFwnlmpsOtsFOoPEwvraiPpBJEGp\r\nr0NJwiJFsBjRRGekn19dvpIqExPlBam6iutbWiW6KcV8SNKWIEbgbytIjazU\r\n7rswLmDxLBdjlbdzlbgOjLQIQuq15eKcojDwcAHDsLFrAtazlHkZ1HY5hX8Y\r\niAvptNbQ+4UeCF8KYxkmyLXeWv2wr5ixwoS8hZh3hXmaNxYXYxF6j4y4y6Xk\r\n6cLuP0OpBMAESkKP8wNvSVyXIvuXTcjTBWU=\r\n=KnlT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.15", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0454df61fcdea7468e2442829e282b966e326f68", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.15.tgz", "fileCount": 12, "integrity": "sha512-egz03maSUNCa59QP/Whd8u6z2NmnqdAFKD5S4BW3rnXTJLCI2URT/z/KDA45QIIo9HeQJvuwsYQevM42mnNs6g==", "signatures": [{"sig": "MEUCIQDmJZ20WTz3qLnBpFtnVJ8GQM3mCiz5IiD6vUfBFar6bQIgb0O8EOjRVQ+O7/WI7eY5WkeM6BWQhOpENTZjg6EK0lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrN+g//ehdin+/69wtot6JuxTa6ytdEUbFXAptENREr/6q2AKIxxcIK\r\n7waIcGcojKZZFHY0rfXJyZ49J4i0bZivkUD6DKlrxTlvR60pOQkaq7YJd0hU\r\nUUKff4suKje9i2S82XuJp2tjbTBhX/BlLR1Lz9btT0yMRcKQ2WxF6HJBTnro\r\njfytbFX8cs7J1ITs6et8qw669mERARdCVRcdSHboQQ4dcRdeGSxgBIZdbD1f\r\nOrYI3uu980CmTyT/iroPISEBBEC3A+gtvs5xDN8vGWgls+ZY30jIo1ybeKVX\r\nXHQwu+9NPlu1ThMLlAe9wo4Ewu+/WBH+Gn2CCvVebjtU0qxUEDUOC8QV0k57\r\nW2fRqEbktbJw13ARq6kQCqEbbUazwv/+YTo/WaRbSHTQKYL5RHpzzFtFXQsN\r\nbyhmS4Im7auCocv8LO46exJxmj13XAjZBrXaZT+4ZccIr2r2NNcCmNIQgqEc\r\n5AZeQHcSoM72y7s4beV6uJIR1DBHVkydf0n2mvjxNGme2gPeL0yq2MlBLnGE\r\nhEIkYotXJNqBN9x9rDMGzeLaiRc/R++/uj3Lamvl8v1VPpoFgbuUY/aEnrb+\r\n6+tmAZJS4tnPsderj0jWlKfAw6ldRuNX5PZIQfYpQrRupSxAU3rVMEy63sIE\r\nRKn22Jb+QBPf5jUTQXf5RePtG0HhDTsSgDE=\r\n=emhq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.16", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ad5e1ab20f3132033e1038850d55bd662f9b182", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.16.tgz", "fileCount": 12, "integrity": "sha512-lxFxhJ3tHYjMWxJ5mkpQDdkPlOqo0GSamBfxLPyfVLg/PIaIEB7VZ0X0ph5IeahJ2kKcqRNuXln0paw/syZyfw==", "signatures": [{"sig": "MEYCIQDcag2bmF7xqCWspTnzAlFVz1e2A60mIMnaOGXwHyXs8wIhANRt8HIvKYxpLQAlj+UWsrSudK5mywmY4VBIlx2aqvLf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/atACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm0RAAnaRXpn45pmt9TEUlNbOCc9e329gwCnEN7SwYT5l1mkCF3dMd\r\nzHgnk42P1aCSOpWeOmrjxHNv+cUQHbIQqiD6q3RbzOQjRHRyaWvSjeRLrzu2\r\nFPZ/67DPgjN62TVqLU3xkVtYDbYQA/6HrNj+V8DSODbx+9fGy2fknGN0OHck\r\ndi60r0tX1wugJlb7fE2s3DNM749SRHWWCxBPBFfa8qp3y7VefXi+uXtWlXSk\r\n0aGy28juAHSL9bQXh+WTfvJNjQ6Q3DXsagbtkXWodI0d5uSwrPehXbAfxfvB\r\neSBrpIKuNcaXvV6TLhRPRkJQj6G0iipIHNKr6KY6ie9vdU+GWp4bVdY2XYQo\r\n6ND3JCw9YZae+lkjEmpqdhLQ6LdR2y09C/avpmIqZZntR0bMvEOODseMyaX7\r\no/osvisnR5Nu6/4lmoGXhikNbSponqPnrsDxdTqWGgUpv9X8UUiijJsbrXiy\r\nPgM5yMK/JCKKDiBQ1j987GbPLvnDc1riMCVQvqAGwI90IP6UHXIVbO0b2r5B\r\nbfkZ5YutrkTT84PuveIUiGG8LVU08DqZnMTV0FQevgOIO5LjSAzeefyWRjNC\r\nWuzIlR+dGBqDEa8Woohzsra96HRroCeT5HTYLXm2677oZoix/CSyj5gwM0BH\r\nvan3Ex+VbeasRVULW0/+a5P5W3GOs8baz/s=\r\n=XZGK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.17": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.17", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c395dadddd37efd85dd17839e9c46da367df6e37", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.17.tgz", "fileCount": 12, "integrity": "sha512-5Z9IqIM8fTL1GVwSBinOLxUVuwP3O/3ugXtTCkU3KSdTPrpGxGbJf9P99jLqxP9IbP5/UXVNP8wcN1JaldzGKQ==", "signatures": [{"sig": "MEYCIQCJMoWbLY1vvJZIM7QugGJhYHQQRLJgFfMAY+8Pz7xAHQIhAIgerboLITRfjgwNTrpx3FHBTWVl208ZRyeK2gDNt9eC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoz1BAAikqFS2oipH06swVeExMPraQbzXPA+WCljuxyL3+Cn+3LLTF+\r\n9tgID+WfDUjR8ASWSZcyFfIMUPJrHYKtYQ+pb7v5X/4X3tBBaFelx9tnDIa1\r\nDP/i3jpnKOdeuCyDktZa7K24coAbtkywVJGS6gooCKPdzRXVDWqJ+3AgvNKJ\r\nXDvtrH/xlsaHKX38zr7u0AYIqkdn6WtMfDx2V4lWI1WrsDRmKkDUMYf91McJ\r\nJSN4sUix/bIv1SkS64R3kQqYcfOIfGaP9wf+9gsA0m79C3yGuiXYrLVPk6Us\r\n5LrHLpkoiClDAKuhvAdIyWP5HCGhQVgpDPlx7iMdIBv6KayWEaYBz1XgD/Ep\r\nq+Gjxp4rQXYvO/jP68ZP2l8GSq1zrHsjoczuCSdPoo3rCshEFW8N/uMxF+bI\r\ng+G2Z7P+AUt48FLdkMXIlQqG8U7vyG0uMBWcGUHedT1yrK49j/zQMMhbwd2t\r\n3o+cA87eEvU7jYFC1Zo5SuGAUd9dw0HTXkd/P18HnCL9k4QZyHs/zIJ74HPH\r\n+u4ztnEs/HJpR9jwN6DXAskuXz6Jluqfkxim2rA8qCGAKXVHGs6NkdyehJFW\r\nvQAqDqrDTvq1MW+oPewRT0TpjPDS9MvgbT08qAaSp6QAtGsXVPV/TW3DUOQM\r\nyFgF3rM6amTOFILKxQnHNaoVZzVJcd/uj8o=\r\n=tYor\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.18": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.18", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "27dcbf8007203f4af015c90d74780e1de131cec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.18.tgz", "fileCount": 12, "integrity": "sha512-3Dsa2KWfHLd7geQGaDB+MTm5qxa1vIUUwugiaXuZ3Rp8hHMNtb/eiBwnZdcaI5G7lS9LezCAF/Yhq/ABQWjFzA==", "signatures": [{"sig": "MEUCIQCod4qTR+NeoI+A6Z3e0s6m99Ap/WRtrBQJudjmKelYcAIgKArizOVnBag9YcKzjOuziE2XtoN52T2nI2TjUB3K818=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRw9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTUQ//RgwGWs6iJt//mZbTeP+cDtsqjnigDQtmBy3Cx10odZGiUy0i\r\nNZCMgNVLezeArCVLoP/K7XzZfcV/o+LJFu/MGeZ2uJsagXXuYhF6JInZWLSF\r\n6mpjSZUeWeYZA4H+sLgeOLPCnsAGs77KkGpWgd0Lck+Cq11DPl5WrMJzsTT6\r\nWl3FonS1B8n9Sozr0UVTaK79AHrwG5ThoaBm5BivuzcvaL2mYO7R1K2SGQB7\r\nm69D7GAgBYR0+RqsoVwqpN6+WoYXOuG9UgHqiDSNodvYyeSXNr8U5fk4Ed0d\r\nt72bdbKjNW8WumhjY+Kj++J+orXLPO/QwUgld9HMUp+dt08XRheDzVjPvZSf\r\nyEXx529PgLgP4qIXIIp3etBS+Aq8tDTm5d85PpPyhIR5vJoLq4Qku+8oK/yI\r\nEPTvhyduEe9vRvrw9JSgbs1JS4PegRWHyLCc6XZS1wTrQSAQPvV5LyCfeybU\r\nU5xbgr6RLctX03Db/BlsIOQUVFSMuevonQXDKOFLq3U2qOtzCW3TPFLwP8z8\r\nnzfPePE4Z64jL0vaEHlrEPvxK29Pe/fx21kxoHgK6n6kHhREAtme+yv+gflZ\r\nOmuc9T4IbOco8Nb6Y29KlZoVbc2XNjdgbinDLLvhRYZHv+5auZFr32bou7TA\r\n+HvzUZ6j3HeXSR32j10N5oZL1RsigLbrc/o=\r\n=eogO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.19": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.19", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fde80e70ec6da0d510c274ac813a948bb0d14656", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.19.tgz", "fileCount": 12, "integrity": "sha512-rjFA+dIvm476Fi5OD6UuFM6e6LVK1Z+dNRisRneQ30j+KVvU8ChodaSAxDThsfLhr9iFn+bNIVe16eJ7qeBeYQ==", "signatures": [{"sig": "MEQCIAFzSHWUXjn63dwn0a1xQpGsosdBke5yZuOrEMqFbyE7AiACJcIMBvdU96mUUNMx6oCo7s2spik5+YrEb2fGnZtSlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVLrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZYw/8CgHddGkrGEXqGeDxf8PutCRp1M9DyTDKYbAY4Lf0QAChq6XM\r\nE/dVCKGJRJAn41Agix+Fz21w2epg/AmQQgUCgLdw7y/g2H0KSaSdfgjVR4cO\r\nmyfypOM2nEA4AVL4Nl3iopsyNFhPeVwKV1JgxIbDEzQQsXIyOHQYK2GaEeP5\r\niIcByhaJ7tkTtC77ujMJpOSmU+HzBNyRogFq7/7ft5vVAI5bob2Ir3TIQLJC\r\nfmEAGaj60CyeczvTEiT9pa6RB1Ax6t7didLS8QJqOHvsCV/Z8SjqtZSlUrgZ\r\n0ZJkzmdlNZYXTsz66sF+/YRGK1ap910ojGNwb+ZXX1fjKAHRKjnXI9lXfiFd\r\n1OX4C9BBpd8ilaFXCKjxkeBbLw0lVPFhhKpaPlam3Q3bmQ0iqrX63oGPTTmn\r\npFgzhBat6UPUAIYKAbPDkaEnnQxU6rY4NtmrAsyeylaDSxqSQC0fQBVg6YwF\r\nuRUrQW7DutMYSULmLmm/iRnvOkzlRuKZ+Z47fnlKWjJY4IXYWM1pyQokme1n\r\np6lwuxfE8GrbE06n89KRKB82OESY4k5dWX2wMkf9DgDgcOTPTeQgXkxr6/M6\r\n4bIJVv0ZUjeColW6Kw77/gLy4sacLYnMldVBC10A7d6C7jZa06tDsp/7F7CO\r\nnDqe23cormKZ7s++GZKfe/9KJdVcUoZlWoQ=\r\n=s2r9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.20": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.0", "@radix-ui/react-dialog": "1.0.1-rc.20", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8defd54647e1e2d0ccdebc5a087a99d50ec930ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.20.tgz", "fileCount": 12, "integrity": "sha512-IReBtyvU+Rg8q1csjbyizKk9tBimDffiA7EktYiYw6NJ+iizBPnR0QxrHt0bRxQinlcw6q0J9oLAQpNRcBc4mQ==", "signatures": [{"sig": "MEUCIQDE/zqGSGKv1gpWiCXvhm7MjGve8EzFe6gfbw4RYW/ncwIgQY3BD4pmlIym4iW76yNDAQAHO/rRgmols8unzq9ACgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnJ1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2QQ/9EJYkz3vRK0QbVLMLn5nAFCVDjDaN8UtGDGGX1ymaHQGH15Y/\r\nSkw0fCrbFB1Fin19OaoYnb8Fpp9eM503HNCZcT+pIrg4cISF5SrdL5zremap\r\nlPO13AbNG7BD/FX3G+SBiZp2qMUBW3o88savtxKgHbmt0DuxMRJD4r3FBKC6\r\nxmkE/EyTWEfV/WQwb57sPPl3paCvWuZ26/DB0YPWpq2QDWFt2/x6CmM/wVTt\r\nnbSGPdXcHDKpo/h3rSU0Ak+SmbfEqh2WqcLv+hZU4wSkrJfFqcQ6RiFuLxMU\r\n4bV+41Esc3tLlnAYSi/MsOFwrgnxbRO7L6wk8t1RvLVEleVMryA3+51U4h10\r\nFWv2qpxG2ynyz/4vYT/uculkNhTYUlgbg3+/My4wbh5E+8Vo7YdpHgt8DW8g\r\n5Jt+GtMePXF3bx6+ZtfuVe3TYP+n3erYv/9iPC9HKsNguoNJUHivfsPQzWuQ\r\n/jk5eU1JaMKBB7BbJ4YY6PsNceJS61TpyUVJmePKZHFkn7+rt3idEB4vRcLh\r\nwl0KaePDBuZDiDnNmKQs4HRD15fIgOWKAJ0/ZSdWe4uD9+ebfI9QnoNR8OWA\r\n02pyw3cIjaYfaZFpZ9By98XLiADsZl/VWJhvB83/tZ8E8u9qVgHoV2gyCtqP\r\ncFvz80rTFexr6ZVZzJIJzIlgOLtBjCPi/nU=\r\n=u7QF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.21": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1-rc.1", "@radix-ui/react-dialog": "1.0.1-rc.21", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e588a5975f10ea4b473fc8b8d7c9cabd11c40458", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.21.tgz", "fileCount": 12, "integrity": "sha512-s/zO8LRgz/xLuybLEoxZythpc4VbTgyFjO+m92LUfpWj99Yj5lKPcgmCy0lvad+xyckWUnM4glUx6JydobMW/w==", "signatures": [{"sig": "MEYCIQC3eOiqDnVsli9rARYvsJnftHNefehDS5bc6yLoy4jU9gIhAP3IgluV68/CxuPaYs1miy+/D47JJsgYt2aon3lGJpRW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpkfg/+OTda4TyGl1bYGQGrudSZ36DNhtg5nB+8wOBvO5bbjzc26xEE\r\nMem+P5fPUdbQAh9gtvh/1nw4sJBeKeBN7klh+JKacYy64SGeJ4P3WE6GqEzf\r\nbD+dMiTk7nLmlmXaGSPq6PNQLepjS0q9zS17a9H05Tof9GjwyI+gZ4Wm6Yor\r\nUuXsmtFQ+zDD8LmzcOQCcO9DPmPXVEu/RDNcLY22MkushLAn+uxlOJhil0cq\r\nahMwqpLaTxmGW6Kxt3wf6zWE+SWMG0KalgjrABawFHotGNSfDqYxKGadrFTG\r\nbMMq/Tx4NELKnLFIayNsqEfwVD+ZFZk18PWWHgi8IaZ5IEgbxVRVDQrOdnp+\r\nxIAFpB6vxCouYLWbksTsdKF621O/Le9F8RB8QpFZ+BzaP2s09EGgwJ9lkR/P\r\nkVU3+rg2AqeJIhN01peYaPM9/Ama+M829LAfI0H+G4h+Xmy87hPx0oC4Yhck\r\n+DH0zKZwoDn9ZsG1DxqQ4I6akxBEOUI5yUm05KuTBXIlwyY3ODK1aUav/p5f\r\nTM+yQlE8e98HviEQWcpjWCs+Ffdi3j9cxQ85ZSMEOkmK0wAs2rk1rLbgaoEF\r\nDW4MkjX2ciKWJ+WlBysPlR5QwBZqBBKWu3vhmHnDb68hShLQ1QB2xPgGHdyD\r\nTgyH2En5RdLB5RY2Ur4Y7+DO3k90GLd4REc=\r\n=jTMz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.22": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1-rc.2", "@radix-ui/react-dialog": "1.0.1-rc.22", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8493d0010eb953d4bf3161994495e396947e0ec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.22.tgz", "fileCount": 12, "integrity": "sha512-52+MC0ilqXDovpuqpKSzs6KP7I+Q9tLqj/uxxkSCLAw3bbDaKSEmo3hE5wzUbtvoBkIrNQpaRJOvepuP7PstnQ==", "signatures": [{"sig": "MEUCIQDKjf2zsJCquI34dmn0QudNBX4G1BeZOqLxeiqzA+7JLAIgBEjw+4+TvhTspVj9lN/DqSc42U7CaclDlin7TMCBQGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRQw/9G+S9b8n3tUgGmGt4iLqx22vg8ng6kC4s4N86bMHYB63DcoKJ\r\n9SK5l4fMedkaUffJjkOU3cLuKHAVCV6ZOGLS6HqtRvUkwTMoV9bDnGKBxGUw\r\nIjSLJEgyydvlPnqCCCpC9ql3Qz9MvHEnj2bIzWTiqfC0jk6bKTcvij1q4Y5c\r\nniqeDNExhQYr0ApiI9+rJyFBm9Cgq9VzOE3PwWlAO60FQ0MkId+8zN7Ewhma\r\npFi/XMt5Z+cctbCkB5WxAp3NiUNyQc6FHTZOi1r9j0n8J31Bh17/oFc3GAjr\r\nsGmw2ml0tcI8s8s0aD5mQrOdRoIhBkdSCb9l40q6kO8M2eqkuzyXApSmtb6C\r\nQsawYJ3EQoUyJkFsXCiNf8AIIQLZVv4NDL/FxbsuS1K2yOAy/xeuGt/vrw6O\r\nJE+2Db4ddPbuEMXTETwCzO6N1Pl6GnXUixQ597zPSdFhbUSB5tDcVopb/QWU\r\nbVAO9Qqd+qzn3MgASNzqPoKQepF1YgtFJQRlpAg+kDPuQHf4D3OBW+PBfm1G\r\nFYb0fIIweVShdLYzuqEny+4y2zqqJWHMHWfAeQ0xIdOz2u8gvT3Twg4Rl+t/\r\nrlDao1SSQDOuByGOtGEiFfQ5/AnmGsxw2L2ZK7nbwX0DbdNEXhfHLEnFUt9M\r\nWP7vckKwpyvr37QLCHbaBTpfvlQS6p2sX7k=\r\n=1GBV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.23": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1-rc.3", "@radix-ui/react-dialog": "1.0.1-rc.23", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "036e42a787e7b05c5be8b344c3ade6c7009040df", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1-rc.23.tgz", "fileCount": 12, "integrity": "sha512-QhmMfkRIwKl84PI8JfSB6IrHMd1Z84jg03DpRRqfw2QNej5MMsRmzXo+lq28737D5Kn8bOTa0dOFnLpLBLNxAA==", "signatures": [{"sig": "MEQCIHSwxYE+UrEirfIgF8onQKArNzTVmjMii+ttplNvwTZDAiB3RqJHqtYl/5KE1q5/RHlF+9GXrg3zsRz3hgIxk5gESw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTReTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzDRAAkjMUji49AagaRkL8MIP9VOGO4GNuPZUzHN6Em8JSBZRMt0or\r\njIh+XZx1DZ/dZ5DX8ZJFO4EyqgwIhoIoFgQr+v1t2B31wQr0dctvK5LfvzqB\r\nKLqDv7+JE6wPcjM5MkVyVGhiBH0Hvz33CvGg++1G4GidIXTRjFVTP/pkDgGv\r\n8I7Lq28h75NY3xXY9JJ2VQWio58haTJvnfl++0FKLhjAAqGqMAb4r6lbXT2O\r\n2gCLLCM54ktUA7NM1TaoJNI7uI9Ptheo1/Ma9Txb/oc3Ec6IHt4Pyh/f0E6i\r\nD76X2fiV/+IQ4MLAdspcBeeNhNOfZ+VOFef2pC9L3VaskCD5SrXXgAJIREcB\r\n2Yi6ZYCbWvfnQ46yaVqOym+zEZy8Kq/IU0dXY7z5PBjqHHgq0mbL3w2jsV/e\r\nPbiyGxx3RGFNz1MWfUyDExUcBE+BSE0vQrD0ionZxqdy0cLLzhfOwhR7PdlD\r\nyv2xEb22Ss/0S6LPXLzkR9jnWArVYhTxKmWsDpRhskBWAr16vYwzlR33L7OG\r\nOcf9aEICVqhEM55HRtUrsu6/1o8JfPbSqLrvy6RzvI88exs0j7b4ThPnn5BE\r\nmV+g27/bF09kzUae/laHuKYlf6QPVpHgl+7MTTgcPRmC0qb3KyZD/IrHZZU4\r\nlAxVERAZCQHXRgErjNyNn2QchphWKaoBnxM=\r\n=7+0v\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f24428ae66c0fa3eb56975a8cd8051105a3b7772", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.1.tgz", "fileCount": 12, "integrity": "sha512-4HtJ3LYQKW/xAkXs2bJddF0vJQEgGN+UCTsKfNVd2W2/ivLoivbBMZFeBDI2DppmGJOnSKnJ14mo1VPfLfVQNw==", "signatures": [{"sig": "MEUCIFQxq8jqOrclC24hgo6AsFJycOjwNeR2PDYkr4i9F2MIAiEA6F9tYlrhaCKiT8hb8/sccsuMP/eEYrh3HgDiCMtapw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOAw//VW7iS8XQezTYwhocCZb7Eu/7a7j8fWFi5pxxdflJqBvX3ZtJ\r\nnwr9zvPj+EQTTaAuhfQvVWL2wmzd0tVzmfikTNmLC+cFeHtQoA1VxpmQOl9r\r\n/OND0Zp7i/WETHZTrxh9yVlbnflKI8B9wXaRPlglFmC3ntxJ1916ExGGG1uo\r\n6GqX0U6IW723SdYXq/qE1HEIln+*********************************\r\nrqLQ45f5Ut+wIrfYxVqYG5dJC3mjhXOpgRuZ9hBxmjw5zfiqYCfdJMYiR4f2\r\n8JVUXn5HZkmIXRmbmOKSt7loj/etHW/J6iVenlpjg0fyt3/DXl6KR2T1Jj1Z\r\n4RZ9hj0+X64gR5wUHZiGviAyiMZDLVHeEqZ8i0o2G9ouu3syIbspLZrbdXbk\r\nH4XfR84Qprc2scr0KGySmhSmj0EonzR5Q2BcU/oCGuhtE/RpMk6Ecmf1uqr9\r\nGaiwCFse9LNBqhGf9BjR5wQ7O7dWYW98WDZn3n0OO3flZ/40d/4wz0Uo1k9K\r\nVF/c0rlIXFZInUVW50gYykeeXgQh9T7trUUxNYItU4o0wV//6KUdkd13nMIT\r\n1Y8qD0fhblNXwdaLqNkpK0RSbE4TGk1deeopyHy+E16IVMZ1HOOhCrxFgGsh\r\nYpc9qRGOQbpuOK+oVeuqtGVjp2Z4HDpbeIU=\r\n=WJwP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.2-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "947d298ec87b4bdef9b51665537026191fb0d63b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.2-rc.1.tgz", "fileCount": 12, "integrity": "sha512-yA38YXbRzLkvdQBRAuGq71tXz0oDX5UlXpIPZAqnPIyVHAEWaSI64IrIPxtM2tcik+nN9yP4lQfFwrDcUxPaMQ==", "signatures": [{"sig": "MEUCIQDdirNaZbbhb3RgbQdEFka+Rjssq1cfovHcJjaTU0BQJgIgJs03w8vsy4OPl9ceUi2rJMPHrgCeLKRZNWUqIYaVUw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS8rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGDg//Vs4C1cxpbpm0eZbLzEwNrc51hxX+qZ5kKZ8dMMVGfo4+FlBt\r\nBQTPi7cuVmK5gLfiDYeFqeVHu1BgT0NowyONgnSF91fvYxgARjH0IWMTzQHy\r\nKfRhTbxjnX2lnbSO4qGC6xa87Qnj2ZOZ6SAsgOqh5Q+I2p1N4zqd4CY5+n12\r\ncwwK5kW2XiuPn031d46lwUJHXTP86X3OgQkU43mffJttzNQ0AlwJr3cpR272\r\noJSld9N0KuTy5gycr0hJ3aqqBT2mS3wYh19K3iXtKdhGDJ27qZXrtUk3tf8g\r\nrk2wYJuulNjjo3KCAzyzZCEQQKQVd/3Uvwrv7OByFRR0UaoJweIP4dsSxzDj\r\nf+r2Dg3JuXgG6Qback8y0aWbNwABdSltCoa40MvvhHTX0L7ZY/ht10TGqMzL\r\njuNv1PU0j1+ju3MU4Ls1IiseHMw3nIZEYdInIKHimIqpUPTEJxVwqbR/pP7l\r\nDc9zLiE2VZXUEX1EVdcr8CRS3fSmdHk5wfLPF3pY3ETdpFWvouH9kSqnpe9j\r\nZ0JJCa7uuOO2WoQSHqW0U6PD8hOpIuBn/tj6PYOxPdSu0RspnXHA7+dNrFzZ\r\nOjv8YLEl5DWGbRbPxZTF51WixmWaQaa0AYvXWbR5gsETKWYP+/Dzt6t0wroP\r\nM1aZvrUzjMhBoTGVtGMhhsgpUXyPEuIQJIk=\r\n=Vi2O\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "03ab5a419a750e757af225c2416c30b253dc7117", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.2.tgz", "fileCount": 12, "integrity": "sha512-0MtxV53FaEEBOKRgyLnEqHZKKDS5BldQ9oUBsKVXWI5FHbl2jp35qs+0aJET+K5hJDsc40kQUzP7g+wC7tqrqA==", "signatures": [{"sig": "MEYCIQCSLZF/kyHaVQLeJYajV0Ifvuoqjfpuy2G/brCy4itZCwIhAMbMHMLRmCgSPSX1OsdlguqWzV7odp52HbpZabkdW+TB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTS/DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrt8w/+NwGZGhPvZ3IOLJx04uTwAd84aURdYQIkl0TMqRO43OBMSP1x\r\nFPfLZy9MJ5p5Nbez/D6QsEq44fhaAcFCHs9rQq5wZR8koD9OIqQ4RsB8NKbp\r\nzbhGUyDQvCdLjtxXtjaqxbsOW5LCrbJlA9UEp2lmKBPWb3dViPCCaZyNRQXk\r\nVvMC9Bzr7KfcutCLhE0yC2DpQXG+g1fA4Kv9LDf1cgDE/Y3onrIP+rHId8gd\r\nNLQUcSFpV9TiTMK0NncCs2loLEdVMLEUAgN27erWwQRGf5I66JUmQD6blssq\r\nCz2A1zn7/SO6BYfkuZD1993ayMmbAHeh+OXDvMonxG/Vnhh+mbIk/W5SO9CL\r\nvSORxBVwNuMy58XhWUc3+EWtSCsDc56YK77TARsExByg2r4WcaO/TT83Ur/D\r\nwyQG5IHiWDyEMlpZozpGtJnVE2JFeRvdG5h1hGZf+lR+We1Iw8rbBMGQAMuX\r\nCMFeFDW/0eM9kzaQF10ix4mA1oMvUZ5Ukh3pn6NzA5jZv6gDFnNqEd/nL7Jm\r\nMD+kOQb3sC8zPwRW2p4OK42nNcOEy9mgBdzgVrYMNp0N8Q+bU/yBNRaZ7TD/\r\nfq3TJtxPss69pNYJAOSbOGWJeTEDW+N9aRF3gQUsLy50tJsNYJjSHdn0YzxX\r\nk9KWS9C0VTIcz5aehU04wNVVERTYG7dl/oI=\r\n=USsy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.3-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aaa94840ff713ba7f01717cbc72a935bfecf1f71", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.3-rc.1.tgz", "fileCount": 12, "integrity": "sha512-iIaMWkwSId9fv8hc5XWVMhES91PGr9Q/9zQ3QWU2KTvvfeL6LbqEpGFcNI7D/t8ZN8B1L9cWcpLzFx9jRaTS+Q==", "signatures": [{"sig": "MEUCIQC6vkzFJvP2kCpvsx2c47i/haDnKt3xPFtiu+URCWNVCgIgI6JvIbHlzDFY1k2GoN2Vazz7c0QqUHQwFhjFicMhHKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzeuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpt8Q/8DFltpM7lUK8fp1czo0EQCRpDfNH2CL1HsC4UVGZC/Qu0rakT\r\nvDkUBH0sfsD9nsFa+MbBOWIffKOQzxVrHqyfWY/+HpyB15Mrc+IGmui/fScM\r\noxxoMo/2hVh7Ja2ermSOuBxd6LlH9zFiZHNHQo8R7ZKC2jqAlu/gi6cdJvwy\r\nZ86kOYWhUvSuqtzw4FczGmM/gj8nVncQhl+dnvCRdefKCpCKQAtJVoStxIh4\r\nzcwXWtbGJIOrkpmc73W81G9q3IoAdFylRAc0nDIdO2xS2q9H9C+er6u8cOU1\r\nv1NgtMuTLz8ya+ABsfHC5TETIKqymrLbMMemy7EjsurMn/B7FQO42pEE+9cK\r\ntLEJU8nyD+MkRnT25Jgl/NhQpzsTS3m01MmQq/4d9Lxb38F+U/bPnyCHxsV+\r\nM0K/ah+Vi02Xs53nXmKPH1+c354GNXN5GfNZ6epJLfZYdD3iBeBX4vsgBvXa\r\nABP7tfBPA/jRe/tVqeocpENXVsEwaJrG0pIK6hb8M+rI5H37gi6xEkNzj+mr\r\nsgt0QfyXnE4mB4/zJkToInxAhnvmGFuO6YmUZ1TQdFywOY9ixkDBt4qVnkaO\r\nUWUUBsTSW6u4a7lofO/XxxOePK7XIRuHgULoy613UftN6YpKmCdbuAJIujol\r\nkiE1bjpHqCDMBzLjCKiiv0b9CmhAxjBlQNU=\r\n=WE0n\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "892726f4e4441622535e68a632ec833211b46988", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.3.tgz", "fileCount": 12, "integrity": "sha512-QXFy7+bhGi0u+paF2QbJeSCHZs4gLMJIPm6sajUamyW0fro6g1CaSGc5zmc4QmK2NlSGUrq8m+UsUqJYtzvXow==", "signatures": [{"sig": "MEUCIQDoKKcPWmfGSFgOQBbXib+nEev3/zuQFnW7D6lTpejMhgIgKE6Lw3enIPt9rhmlSVazYd8B721pokIleFgGTf2rmo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJagACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEMw/9E4IByh3AgWg5Vku5pCa8PjFXiYAZnotjoZkS/Bfl78ZAfABJ\r\njB/Lq8SnPXDHsMA3+Fl+UVwiAuGeND8+OzCrWnKa3nzc9Kfq5pznjkhAIQOj\r\nVGm0fjJlAkRgf0HBmDTKlZs5dWP3LjxYl4dGU0TtBr96fNwbsKxmoxApoRJp\r\nXWb3W2JpfZ+4OrruGwj9WvhOwp/N2e2gJzPSH17snpe3gfZ/rc6Vq0FYJSdI\r\nVmU+PoP8Zsu047r/i05HY1CVkBquZG0ryhEWx60atD9UWo/RIcL/uoI1wWC+\r\nqY9q4aO6F6alSbrELVUZenMfEVIAhj/zrhf/7miNncTeOHgdyK9eeBksidRK\r\nURyoxtkWWEpawG3Wf+t1BkjJE4MGrv+weRDe74+xSPEEKJCyhvhWw/CJNwlh\r\nk9yDsMfmLJZkR3RYajzMRDUQZpxqnRtPbrmz6Ly5PfMK8UyeU4BL4h0oI18r\r\nKzKzHKwAAfs4KyMK9ILXAHoydRIDWZJagb4iwFmyp6sPc2i1L3tf7CpjePlF\r\nf5SgbIMgxKoujun+KSxkCEeGcJe2jncp0FC5jwoaofKIE0xnPq+ypE5Or0zu\r\nasFL3uAOFdzijzSD6DmalHfV4SPssTCVljPCfsa+C+qLeP5vP/Qexujcjo89\r\naaPO4q4wAb3lXB63k3w6BqYCwxh6wn65Lv8=\r\n=S8Tw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-dialog": "1.0.4-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d0342055de004adf758ae2627e85357392833d20", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.1.tgz", "fileCount": 12, "integrity": "sha512-q/AqkSLwUpFw6+q+qbvzfcBy8NKOZV1ewISIhs+S7TbaEFzdN0bq8sW9VcrbsjMBjfBt/1WzhsRne7Q6L94HRg==", "signatures": [{"sig": "MEUCIQCE/1sbBURWB9zBAgEtyRD33XevpHgsSqO8mWX8hIA48gIgbk+/QDRNi8E6C6Z5ama0mgAxfOZOek4Nxnyl0RjzN9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/jsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqgA//QH5nDBVSy9mYJbeBeSaqXs9yB/NGwVWQcjPSf0s4RsMkUBBV\r\n16AQNASaFJYdnek8uLIwu4mIjYX23vpAbRmGfip6lRy6buGRdXrCrmwVWPDW\r\nyZaL38pccAHKuxUVXNcd2sR2yl+6VjutwQ4UYf1ZNIKTfPxH2Z6uYC4KZbNV\r\n/k/dXNj7h742kbudfEPzBAEaYtkNXw/q1fQSzdL+cnC+KrNdpeeTZVX52Pq3\r\nJfH2L8VaM7cgFNBPkjIYxunLUwwBCDyAYc4JmGTH9M1R9THKJBoAV04FNUrw\r\n8+Mc96WzpDi1rXt8vQ+Qh/FNTQ7hYyor1lpMLFLrEpdHjUCudjB2s014KtpP\r\nxMMKOcRghnrMbQQKunLi2Ehmdx+Pdk08vvEHC4Z7dEZ6s9LPDNGK5N/T0r8U\r\n/OWU23puHrPvzOFVmy8dWMSfQ08j6gJP82jkmVlcGxYqFS/ZpYvyDXKEOYmL\r\nYjcV9cYtpWh6KnA3+Ajs2RG9LWUVwN87hQW1Sp65of0SpndsMBP5v4dGle3/\r\nGE5DWV5jadOuGSU2HFOt1gE55BTtLqd0nSfEF+azC3Z5GhXW87wnMMUAkLGn\r\nfADpENQ1kapFCp+QctvvFisaVOPBJXzyj24FfCes21b1D9ZQMm6mVBcUvRMQ\r\no3q72p97Mqhgs/q0IeC527GJPTdkEMGOMbk=\r\n=hz98\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.2-rc.1", "@radix-ui/react-dialog": "1.0.4-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "50f9658209e42fcbd54ca6aa2b0c0d5e5edf8bc3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.2.tgz", "fileCount": 12, "integrity": "sha512-26yeMPVAcsc0NchGV6QP3BG4vJ/EPsd5fIWpaWfKTzHoKD0NdEmxfI1mfJsw9VNOWU8Lgt2Cs0UtFv1bsDEdvA==", "signatures": [{"sig": "MEYCIQDxh4E3n3E2sMkHmoWCmYzURBURjDChdGhzGCFIY96rgAIhAOrXYsMIc/aQMPJZJ4Inp7bqn55ejHEIjQxel8eF82J4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIag/6AwDlkKx4Gp6qbh92Ri0Df13LQOIrmn8f7qDzv+A4yzMig8Tq\r\nYA0neIeRNlTuHtB/Adf1Tno4CXV/ydSk+XKiVi9hIPqhbqmCi52p6J4Xg9LF\r\n5/1gLtFqm7toMoMF80V8CLRzwFMULTgr/kxvwneIxVmna9WOgE3tgxfFKtF7\r\nltedgajWnE5Nr0N+8jlEnvOivU+IbXsQEF28A9q7NNQ3O7cOsxY3B+QrlBRY\r\nNWl4BwoPT5R2KYWia29FfeP2qSeaTtjhM+sc6Pa0k55EwzWQ+SlLGTWTxI+R\r\nqYmEDhJjaVdMKQ3uWnY84AMWfYV6uPJSMIXRAP2//4uHGYnH5K95EXApDvNM\r\nCszXyC05K5VVX5fNBe/h4lLcPgv7K3mQN2jYVxRKzgozVHosmoha7yLlnb7C\r\naA+KtPmnxXhcY70G0K3sXbFOyERyLBAzQhs6F8FnjwzU7icwjD4nfal+33fy\r\n8+mmCMG7Zc+M17PLPEEicMtO/fAGue2zo/ptqw/mgl8nAx7X0BGUu4X7O31r\r\na+8rz870tNkykfucGc9XGYLrMoKD0QlKQCIQ8Bjc8pj9HfzxuSPXmWNso2Bc\r\npKfLMwVHSuEsI3sGhH9SDTvn+lCXTiWA+RbZ6otp0KBsaVVvISLdAbsNW31N\r\nn5hNE7BzZqui5JwhTGQPcFO2tvFdzNfjBws=\r\n=zAwZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.2-rc.2", "@radix-ui/react-dialog": "1.0.4-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e8f83eef1c7feb389709b36a4d32ebf4221a04b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.3.tgz", "fileCount": 12, "integrity": "sha512-a4asXbJqc6igYSmDfWIo/+4VhPQ1CjrCA+f+7QJSRyTpxTco/94WcxQEJ9a5FYc2Jdcd4O+OL6wOIqDpkVbJOw==", "signatures": [{"sig": "MEUCIBvxvpmTasCFaIoLqK9/T+k52MvwtWJe5/wzPk8cif1dAiEA9RwbRaK8WMUOL/9hi1byyUmQQtGqSxegFh2x8pnTVkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108053}}, "1.0.4-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.2-rc.3", "@radix-ui/react-dialog": "1.0.4-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9eca35e101349961bf470a051f88c05dedf1ac89", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.4.tgz", "fileCount": 12, "integrity": "sha512-wEDj8wdzgG+/6Han4XJv6oPD4xj5YuyW7yGE74DKz6OYI+GfgcHHmx12EqCQ8VhvIjtmG+wXyx9pFfLtWPYpKg==", "signatures": [{"sig": "MEUCIDbSnE6CKLd1Dek/ZzjtRL8j/se56xhxbltiLWYzf299AiEAgPymzcVWfVfXIdb0Opjpo8t5wwHG550VNdyGbqreQE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108053}}, "1.0.4-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.2-rc.4", "@radix-ui/react-dialog": "1.0.4-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff9b07d0db8e002cdf1fdeac1c13b81e330f2a40", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.5.tgz", "fileCount": 12, "integrity": "sha512-/DmoWHFPjMxIde5LIFDB2biST54aVUASlFBp2ZFffXOqbHOUGpgyTuB0jC36aCCkNccJQLY61TlTo3x3Q1NDVQ==", "signatures": [{"sig": "MEYCIQDxkgaS5eNBNbm+tJVhgrXyXcNZiUkxw2f7RYdElL1OrgIhAIZnYfk3DI0JMp1Tc7XvMAq6P346cXCRsd54jqRq5c3X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108053}}, "1.0.4-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-slot": "1.0.2-rc.5", "@radix-ui/react-dialog": "1.0.4-rc.6", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c697a4f209c9d622fc518c3d8c50fe0c740a6d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.6.tgz", "fileCount": 12, "integrity": "sha512-eq4Hjh1cmn+0b27J5V/SGyRsdvkXljOUKR53WI0cEAZQ0puKlo4daqpq4qVECJcQAfCXebaXkBC8j6hkFd/U1g==", "signatures": [{"sig": "MEYCIQC/UbjNatjhT9/x9YBuNWji5ha1qyXKWhp09dtj+xXXRAIhAOeYFSNE3v8BrviZraM5e5TkGTY37Anyjc0xFVBpEqiS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108053}}, "1.0.4-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-slot": "1.0.2-rc.6", "@radix-ui/react-dialog": "1.0.4-rc.7", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37b4e3aab811e3cd499c6e9e0a70e958870d1f62", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.7.tgz", "fileCount": 13, "integrity": "sha512-MuLgqF75A2J2aXj9vho8+WUnV/GlbO4neRnqOx+2Tuxh1BQaYCr3In+OF4JpPrl9ctrLxst6mU4fw918ZARDqA==", "signatures": [{"sig": "MEQCIHuPuE3hOSUvaOviX/BuXEiVZdxiXX+0KJMyXlE3kyOHAiA0/DDh0TuteX4T24DsxwHJ60t2OO2KVioC5vbxuk2ryA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111959}}, "1.0.4-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-slot": "1.0.2-rc.7", "@radix-ui/react-dialog": "1.0.4-rc.8", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d65de6de4656606e736355b24748552776c0b19d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.8.tgz", "fileCount": 13, "integrity": "sha512-sR1J0Ou9SNmpHh8ntJWm5BFTLxkziVv9sc0oFRqFeZUxThBUNmPfK/jHO0LIEY/4fQy4WYyWC6HujmUs28Dqwg==", "signatures": [{"sig": "MEUCIQD8qG+w9XDd7976l8of6b8AbeOGucr4kPvIOzOKqieaWwIgfqsx6ZiViYQPuBEUae3vZaLK3Km9P/A0WhNGpg8CvUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111959}}, "1.0.4-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-slot": "1.0.2-rc.8", "@radix-ui/react-dialog": "1.0.4-rc.9", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1914339820e8ac203d2588dbca8615333865a908", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.9.tgz", "fileCount": 13, "integrity": "sha512-idMdwN/MG2vueWs+EuRiGqWqf6uWZhAc1wo69qtsfcw3M22rBdtiqSxj+zPf54aeWdTlj87UOK0kwjAi4SwbcA==", "signatures": [{"sig": "MEQCIHmSbFrnAUOG3515YJo2XYcwz1DQCddylAajv+R5YCcBAiA7ceUOLM3VOKVUhb4kBjpxDEXbLAm6CfsCodUtiWseYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112153}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-slot": "1.0.2-rc.9", "@radix-ui/react-dialog": "1.0.4-rc.10", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "886d9d5514ac7929ac4078003580b92df6263a30", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.10.tgz", "fileCount": 13, "integrity": "sha512-bnZbO6f81n6KnIUqtNbku+aPEep3ixCV33H55J5KZOJVFCL1uBm5wyIOOO7waCppAQj8tVi5uNY3TuZKWE3ZDw==", "signatures": [{"sig": "MEQCIE9qakhREfLnb7Z/5REVBBLigxETS8Ndoi0KMYyPsmP5AiAE56kEglH51W8f/g5K+qko4Yt1cVYFDkDC0S9lr/Sb9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112155}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-slot": "1.0.2-rc.10", "@radix-ui/react-dialog": "1.0.4-rc.11", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "32bd435fdb07fd94d185070a44c46bdcd88be1e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.11.tgz", "fileCount": 13, "integrity": "sha512-QUOPoe+G+JRbDASdC5Z1XlEa1yI+jtHUVDzKBPdeRki8ZXZqnUo3IfagD4a9IIPL8Lo5+JVr5VUrxJtLBj/ZRw==", "signatures": [{"sig": "MEYCIQCuTevo/XgZGkSl9lGONzHm1xwrcr5nqV/wACd+v1wDGQIhAIkPJOh8dgiaOCGiFpA6/3XE02RA46xXlV7qDMw+5+8n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112157}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-slot": "1.0.2-rc.11", "@radix-ui/react-dialog": "1.0.4-rc.12", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4efbfb804a78572fe40df4a995c1eda0868f8dd0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4-rc.12.tgz", "fileCount": 13, "integrity": "sha512-gBXoB9yicoHMf897BqSLR1RHolyUaRiJ5GIPh84giyRFDwf6I3++U9bwET/P4pOPfXAXH5sJFu49GKctYPUTog==", "signatures": [{"sig": "MEUCIQDLangspYlPgpSoNUSVe6JGN3SyY4DdaD4Q9anVKJxOkgIgaB/+cLLwgdHwEbcyRcEsEZdXB12ZxXaUQBOdhLbbX/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112157}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.4", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "709d7625efadac503c6276d9e92d3449184f5e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.4.tgz", "fileCount": 13, "integrity": "sha512-jbfBCRlKYlhbitueOAv7z74PXYeIQmWpKwm3jllsdkw7fGWNkxqP3v0nY9WmOzcPqpQuoorNtvViBgL46n5gVg==", "signatures": [{"sig": "MEUCIQD6G/EAOfPtFDZg/a2QYEKO9Js+hh/rKzRFzYeozW226gIgD55y5qnWWAcgmyzQT+YygQkErIQAm2b4+k2Q71Wgy7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112090}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ea3a0f9ffd65967cd844df0d72e9dbb3c172e29", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.1.tgz", "fileCount": 13, "integrity": "sha512-7y7K8ZyEe74bSYCtWuI451Aka75SlmwVwi0o3j3wXczPVSp95hPDK4Bd/lh79LrD4Ma9EN6TOJH1+IQfwb1lFQ==", "signatures": [{"sig": "MEUCIQDZ2YS9YqptEgfzRlaZ4ZEeLz4rGTgABZWLoNgsP0x9PAIgN2hLlwiK5bDPdr9Nw1JPMusANxHyZ89uk5RDpMOb1u8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.2", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "90989f61bbc97535e08d333ddf83214d86947847", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.2.tgz", "fileCount": 13, "integrity": "sha512-19I72TYIKEAM0ePZ6uh34+X9GGFjwGTsqfnYAHa2Qs//q1TCNDpzlHQsWOG/+LHQjINsfYpZpFUA57S1JhwYcw==", "signatures": [{"sig": "MEUCIFu6AEx/lM4zDjFjj2AzY/vges1wkw8AnEfR5ndGqWX2AiEA2cyTozLtJKv4fVNw9PjJEcWBZlcP3n90L8RGz0ynoBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "41ba8b672d0802ab0b3370f0a9631282252d1bd0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.3.tgz", "fileCount": 13, "integrity": "sha512-pFDsI9sa02yfEZ5fNglw/p/vWApZZ9KZDhMN9B9xNL0/BE8z6GjiD969if7zwypjFq7z+N1DiLlotZHspMWe1w==", "signatures": [{"sig": "MEYCIQCiNlwsEYW+Rlb1rilyVs4Ax8MD4anvVVC8ueogKdI5RQIhAM57SH6Pg6mOeYdiPYdi/66kbkPkNCQFFCJtGRQngV7C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.4", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "78c71475b1bb4c2ed47520b133864a5a486072cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.4.tgz", "fileCount": 13, "integrity": "sha512-7kpQww2JUoU2eJo46Nq1xCXeDs3q7uDuq8r2A+hZzpd0AOe5T8WGF5ObV4WtvW7Pm3vBx3iATd0cEsQUbV2B8Q==", "signatures": [{"sig": "MEUCIHxIF/H1PMUNKq9j3/QsidqXCxeR7TFKXvC7M5yzPPxSAiEAkOfM8FqUIGPLFeU84G3k2d2p2Po/m2EmAoXn+iSFy5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5c993b6f96773d7ead8ba547278da011a3c47d35", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.5.tgz", "fileCount": 13, "integrity": "sha512-tJs2kV+JMOOGT3Evot09BVfUNgjHxVIHbuC73vmo2BUCUnUGiEJXVoQ6bwJO3sbN5Nhh8b8YVlCj0njs0PoT7A==", "signatures": [{"sig": "MEUCIQDQyJT/u7pynCMrrcAgNNfKE4fXbtuK4I4mj1U2i76zwQIgZ5+ISOxTkte0xcFKJ9uLSQRrakL2wpIv0kzmHSkOS4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.6", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eeae6fe4d3511309234a502ef35548e1ce663c9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.6.tgz", "fileCount": 13, "integrity": "sha512-l1n3cWy8Cfuu7lPcxKXvGwAaRJJi8HHVZbnw0JRAeLAPUT85VirR4GriTSRo3sZyb3Aq5eW2YgAkGiS0HADEQA==", "signatures": [{"sig": "MEUCIHMA+6IxwFYLkLXAYBjmTy6iSUncl2Wu3LQ3wMpZVvzSAiEA4uCnvusAAMRU6qO0AmNnNBSJdAf05KZXxmr7bj9b1fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.7", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "936b324d6efd0cff88b189fa4428db9a1b8dcc3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.7.tgz", "fileCount": 13, "integrity": "sha512-dkhtks9sqV2k4/wJeoilwDxkbHI1XxfPKhXQ1PCkjKSUFBARZr8IsnakPiLCptuNXxQyMsheFCltZv3QZp1WOA==", "signatures": [{"sig": "MEUCIBjQ1ut9mrsTefekIYaK4VWE/ZHlrHIOgdSSPLhdqotoAiEA4TLXVNtTySP3fUndQ9qWEmVwb3yLpN3T8fMAPtYChx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.8", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5d02b2755c18d188e6eaeef363390a8b3a05fa6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.8.tgz", "fileCount": 13, "integrity": "sha512-phbvvCM9tcoCR3M3w//XKoBzwEXigXmWkFOn9x3pADxYHqfaqXT5/QAA3Q/uBBfGOgE3bjVM6kHdOy/ZYm0FRg==", "signatures": [{"sig": "MEUCIB+ds4PXIAXuMrwnKmIUAAtRmf+vm112G2z9Kqh3fjuwAiEA9Je61OoGbTxexrVr46wo0GzwMg8x1ywcSszNRZqooCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5-rc.9", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "516fb814d0188a04fd3f970b27dd4d1c14ed0eaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5-rc.9.tgz", "fileCount": 13, "integrity": "sha512-QPKR75jKVcWAONqi7uClZiQM3i+RsGF+aRrLFnqdjJqQ0VygaCUNM8rcMRUJuWPKWvcIFDAfxcIcGFnnjaodTA==", "signatures": [{"sig": "MEYCIQDLTU6yIIIjJvagA1HKmkt07T3v6nIsLjr9+IJV1ytIcQIhAJRssinKeHOMA8hby3SNv45TPAh4LJzQjFgRw1lLxyMv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "70dd529cbf1e4bff386814d3776901fcaa131b8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.5.tgz", "fileCount": 13, "integrity": "sha512-OrVIOcZL0tl6xibeuGt5/+UxoT2N27KCFOPjFyfXMnchxSHZ/OW7cCX2nGlIYJrbHK/fczPcFzAwvNBB6XBNMA==", "signatures": [{"sig": "MEUCIQDZK6qNd6DctMzEJIUabH1MM7ldgTm2BjSYmNl7KodKoQIgM77wFVIH0snD3q8J9Jc7qJcP7Yn7Vjrpo6Xr88ljbg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112090}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6590400e0b382467b17afad7179ce232afacc4fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.1.tgz", "fileCount": 13, "integrity": "sha512-Io5RJoykRDOKJ55xZYw7a4iT401J2M4Z+sLJmq3UNyFhvO1+1ubmn9Q0Y4kil7kjABvtq35w9uFCDG3Etv8W1g==", "signatures": [{"sig": "MEUCIQCFHwsssi8WwlqyY2VLqqXraRBIz/Hux3/86LHLLmFIwgIgQrz/SowR99o6Dn9K/PSGxQxwPZblbnspOhhT1dd84/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.2", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "802f9b264b30e0f21ec1fca3173717daad52e90b", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.2.tgz", "fileCount": 13, "integrity": "sha512-zg5KoDik1ttQ9I57sI/M/IBBTrw7LM60RhLtpOekt31XQljPRIPqhVVnMGpJj+ybzcXTk/Cc0mfGyp8Z/9j1zg==", "signatures": [{"sig": "MEUCIQD1v9T5v+AsBqJEbpSXMQAJc25YkDhu5ETlaQq54TgEYAIgKsr4CFv/o/9TTljtOjQHLlCyjXjbQp6qx4HDqDsXTWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ecc3c818222cbac6f96baedbc4ee87535387f93", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.3.tgz", "fileCount": 13, "integrity": "sha512-92+Vawjc0lamGgIxCHXiD9U7W2HYG36zhrdypANsva68y/o3KvFed1HPOalDeNkmA3DZ0BOyXoAg3RqdP4+G+w==", "signatures": [{"sig": "MEUCICSoN/tGVu4w7VAiT1KO1zeBaQlNaTwjRA9vNvpcbrbaAiEAjf6VvbAW4k+LSQqnznlDT8/7EtN6Pu+bmDiR6inlbBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.4", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "af549f5044fc0bfbb8ede189318cd3c79c89f4e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.4.tgz", "fileCount": 13, "integrity": "sha512-UqWLL3DBxEQ3Veu6VUK1UjeMBAHwwh8YvhW4EjYuGXRFgOMLP3USxbb2fR8gJkKObKUX1EGUMQKxra6fKiwCxQ==", "signatures": [{"sig": "MEUCIBPlqpNKG9ZlrAtiunbHrNh6sx+U2dBB0kapbDxQbUrjAiEAhm4ewhFIoIpjeUg4AvG9p3mZx7lVeNneQb6mjpNPens=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c4e834fe55e88d74a9a5f85702e87c0f1914a29", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.5.tgz", "fileCount": 13, "integrity": "sha512-LPrjj0KnOnZ2bQo8gKmA9GVWhT0nEX6ClMgM6llIJ95zMnUqcYA835Nf4kLsxIT5J5uNsrFmr2mbFc5xD6yYZA==", "signatures": [{"sig": "MEQCIF0EJEZdQ+9hUhbKEw3YHEk7r8fYbszksMHgjl8c/bq2AiAoRdWa+f2EhRcz+4E/eDECpOqUdWMjWSyNw3PlJyOPsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.0.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-slot": "1.0.2", "@radix-ui/react-dialog": "1.0.6-rc.6", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f80a8efd6e7bc5752eda930935e36c45fc4e9e54", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.0.6-rc.6.tgz", "fileCount": 13, "integrity": "sha512-8woN0E6160G8JAZ4v4X7FhgTKszpHRI4nKlBTfF07BNsNayLB0skYHbShWURoRbxiavh0D2Ap3cgaCy9hgSiUQ==", "signatures": [{"sig": "MEUCIQDgvAl+OjocnGvBzs9hKURbx5EmCcQ0jYDAQqfDxj53AQIgDwcEXKEWMpf/oB4caVEJpsXwMrqO5VZFEUjm3H9DsXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-slot": "1.1.0-rc.1", "@radix-ui/react-dialog": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4a74a98c7a2e7a6ba059e8bd0a6ad45e9677e7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.1.tgz", "fileCount": 12, "integrity": "sha512-KnvJzZGoRbN8BDja8acEd9EPbUOOYLMHIQhLTQxhkpuK7mgpyJrU4TRi0MbrUArmrl0bnJ6pJVcHygpSiomC3Q==", "signatures": [{"sig": "MEUCIAPAHyTCf/C9/LfRtwzSHy8xwySu8IlHZlS2Qwk988sDAiEA1cFgmoUUKFiQWJcP8vV5+/IfkBI2ZBvjmNGZ5wiQ7HY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89220}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-slot": "1.1.0-rc.2", "@radix-ui/react-dialog": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5e998efaa3e9a729a8656faa95a85f3ea18e873a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.2.tgz", "fileCount": 12, "integrity": "sha512-sp7LM7LVUyzwAWqYxFo4HrSdkNObe+Q8NvR3h/24HRwWIWJCRA5GEhhJgKppGMMvOogP8jpvXTmW5rnYQejSpQ==", "signatures": [{"sig": "MEUCIEf2JO2OHeSTk0RDmJ0tnGovrqPmsm9zdd1mF4nM1bdcAiEA6NJk4763eMDhM658kuMRPd7i/+0R0ksAAXIUzHJOCac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89266}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-slot": "1.1.0-rc.3", "@radix-ui/react-dialog": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2df31b5dd9cd61e4555e35ca517192536ab6e3d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.3.tgz", "fileCount": 12, "integrity": "sha512-0Ve8/6qeIS95S/omFSNUrMdPVkA4s0c4GQqRncmeXyrBTZv1NMMjUa5jnuVKHCASm7l3ts5OK2p77HwLzHQ77g==", "signatures": [{"sig": "MEUCICBXWUr712cTWfy6qxMnvei6svGqtdIF7HykZJdQ7rUuAiEA7uSXK6yNjGASwVhxh8hIqkOiDhH53SUkf2Y1OVwYitA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-slot": "1.1.0-rc.4", "@radix-ui/react-dialog": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ea779a81fa4c736dfc9d0cdd3c60c0c14f9545dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.4.tgz", "fileCount": 12, "integrity": "sha512-8NKwDC+qiokl9gugAipXVzeGhdCTUIGmqd7xnzY8t3X2seZSmGX7eOM5BLxn6acQ790ikl6HkE53NIEBNThFvQ==", "signatures": [{"sig": "MEQCIBB0LvKXjzljuOb0TcIm8xPGh5JrCZVGTbMF2xSouRAOAiB/nP81ummXYPrlwdwkTvx3C+ZdbdYfZRoUXNu11LZ8KA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90092}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-slot": "1.1.0-rc.5", "@radix-ui/react-dialog": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "301701c114b38c94fa6ad7d71f03689f8498de3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.5.tgz", "fileCount": 12, "integrity": "sha512-Lnj59WLv4xpOrZhpOqe8WHuxysSRG5lfKN6fy9TSm3L7LVr1ILgb+PbaJwr+xZ6mQAO8xZZvLLoNlAfCwCM+PA==", "signatures": [{"sig": "MEYCIQD4Mi8p4yCWYYdfPrEIcCX0H53+htBGV96bCxLrU2pTQAIhAOFhEnW/khpz+JyE3w8tBtx4Sjp/H4+2+9CqUffMFPce", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90092}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-slot": "1.1.0-rc.5", "@radix-ui/react-dialog": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ee4276287f0c7aef197082fe579bfb503156095", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.6.tgz", "fileCount": 12, "integrity": "sha512-gUeJlyosO2Kv7pOKF2r1U5f5A0H9dPEjW/F0y4Ye8WFAM6gEo0SmNbZnGsH+sXvjlsUVs7yIaF/dlyFsTvfDmA==", "signatures": [{"sig": "MEUCICEd4MDbM1cUWTp/hiwHhyuWQwDoom0+uJush1Fddg9GAiEAkVbacW0Lg5Kc3W29YdlzA8bbno0VP4v7gnp2b7YVX8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90092}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-slot": "1.1.0-rc.6", "@radix-ui/react-dialog": "1.1.0-rc.8", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0d8f21809c2d067f000fa572e24d269948f6be47", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.7.tgz", "fileCount": 12, "integrity": "sha512-iP08/GV1vXOeTWL0KgD5L2+NQ5o/QoTncVZsoql9El+/adW+/JX5xms7itfclkq0+QtG0EzHw0ukV4yXJJIxoQ==", "signatures": [{"sig": "MEQCIDgWIxqHkgXZhpqkTus38VCiFDykre5TZkmgYZXHSMo0AiAzRKmqKX97UGrv+0FTWIqwyDrPaVtEKGefJTDDu3fSKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90092}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-slot": "1.1.0-rc.7", "@radix-ui/react-dialog": "1.1.0-rc.9", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "73f703dd0d58555989d12b38df7650cdc4c766a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0-rc.8.tgz", "fileCount": 12, "integrity": "sha512-2eZ0qeMivfjouyi+/7bEF6+uLeY/l/oX9b6e5FKd7sBeXix7i+pgNBpogpMDFdcHPyEwqfaaxqHmvP16/1jG4w==", "signatures": [{"sig": "MEUCIQCzTTYTVbKyHCvT5dVpf2psRoNPkQaPhQaMVzkfchmO5AIgeIocPjBkHrEF3qf2f6st1pLB5kTtjkX9M+xENnhHkf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90120}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.0", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a13098b9b239bc31582615c95948fd2a8a866b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.0.tgz", "fileCount": 12, "integrity": "sha512-kM8Pfn78tg0aqFD2E1KfO0sZDY75hTkrZd9DIWkncQFwlIB1FV96J0+KsNvK1AotgQO8IMxntRyPMrh8+2vghA==", "signatures": [{"sig": "MEYCIQDCmkoJ6UW6EarrPyNyrQa/CHdC7/oDgpQp3QfwlxNC9AIhAOplaNcV5N3YrbMRq3xo49dIlEXonys/eTNwYzTQfrmZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90057}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "17172473ecf1cee884f6681f02e3e8c5d6b05764", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.1-rc.1.tgz", "fileCount": 12, "integrity": "sha512-LHd3McBExLD5w9w9l323OCa3rkdgDtfkfptksu5cncpk7D2aNehVbQeZC1tTIlYknShrT5t05sNXEQulsjRUvQ==", "signatures": [{"sig": "MEUCIGNBoGMeBo3sHooeeSQ4AESaZ4kCop4VP806s4ALNynfAiEA1iSMG1TAvWiSd2SgeoOQspmI1tUKaIXJFjwp8jGW0jU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f49c987b9e4f2bf37005b3864933e2b3beac907a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.1.tgz", "fileCount": 12, "integrity": "sha512-wmCoJwj7byuVuiLKqDLlX7ClSUU0vd9sdCeM+2Ls+uf13+cpSJoMgwysHq1SGVVkJj5Xn0XWi1NoRCdkMpr6Mw==", "signatures": [{"sig": "MEUCIHJBF6cqEO5jJ0k5S1GJI3fs1uKp5Vlmas5bAx4E1uu3AiEA3KB/k51XAKDjtNa8hVqAPEL2emd16CAFIkFrolcdSIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90057}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f475968e3891fe772dc181752fc0da0c41d80dd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.1.tgz", "fileCount": 12, "integrity": "sha512-13HPoG69Izw7kypyEGxhOm8n2HVjVf0H0UKqAnBMBLAjxuSlMosPyw8WXQVVGHca0MvDHEA64Tx/7OZzMoMr5A==", "signatures": [{"sig": "MEUCIFcBBauFxsEc4o3CrYkrzEX+r8taza+v/89dD2obEIngAiEAicxqrkwuCqw+nensXHMI0tdnqm9Ffnx2PhERFXDBRC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ad774236885387862c54599407308e6fd4b120b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.2.tgz", "fileCount": 12, "integrity": "sha512-U+YYgy2jxxy94Q2/5kQkywh4z07xYdOTHy3Ov8p1xnWfpsKMC3xRaJoX7KMLc083HAholqtqEn8w4LMtWY+K7w==", "signatures": [{"sig": "MEQCICDjsVgqCEplwbGNELEz47in04GNbN5v07e6Y67pFs5mAiA5ihFN/XsdD+bvjUoAGCD+F1lKjKa4trxHnrvsdGt8UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1566e3569347336da2673f6cf1bf234bcd22f066", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.3.tgz", "fileCount": 12, "integrity": "sha512-QbuIJCkFp9cS3IWs65JxCNC+96hsPzaUg/H97tLEISi77VSVtvAbyxZwb/8wGnAS4KP2K4aO6tKuFUN9TpGzcQ==", "signatures": [{"sig": "MEUCICHiqXVx3qL7bEImQyZbe4AmBCGXdSK6lTUnRO3DY710AiEAqfWbbn1bF6IsfwQBh4JNZlzxpenYRKvy5eDtOO6cH5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54ca7fbf8a4c040dd8e9e8de36fc007951e1a82a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.4.tgz", "fileCount": 12, "integrity": "sha512-stjahMLJcEQfljX/tMHxibm/lvGyrasyTH5pkWUFXBHiiIWqP3Djmw0rz84LhiF9uGw1GaTPcqEYk/iozGKYkA==", "signatures": [{"sig": "MEUCIQDlhitL13R5da3HX2+Pqcs/1Ng6jALueCMbTeTa3cX0swIgNGdUojDU1pAhp1YmAfldxfHoh6/+XVJcq7UzvfuAy74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ac6cc40f338813e39a4f61ecdb852d7d43fb146", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.5.tgz", "fileCount": 12, "integrity": "sha512-/DfnGJ51//CB103qmocE5ms5PStPkj+qmtgCWFhe+mzr0L6e6wPaOSeaKZFvfbjC8q8ICXVH7LcROHG8TJQssg==", "signatures": [{"sig": "MEQCIE4d9UMws/qFJlwVhxLM5lthV6npeeee4Bt9+ANjVw9JAiBSxfKMDbuBYSJLcqcOHnIvs5S303e3dZXXXK9sVo/ENQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ebded96159957440f3244b9b884e78fdc35789e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.6.tgz", "fileCount": 12, "integrity": "sha512-PgCXZKzHhb2NG5F87m/8V0J0fC32lwXjmS5J0mEK6a+dovBFuoFx8GV8raGEWOa4gqw9tqFVAkaDJ4IFV/iLCw==", "signatures": [{"sig": "MEUCIFluxexhXSuBDWu9476hCxPMl16iaH3TyNmyyoSEFDsJAiEA7OO3kiIzCKUwIEaUaszQYbGwzDcZqSjoZM1TlQQmDDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "38d71d82f59f04e71e97c5c95f7cf7d9fc28d375", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.7.tgz", "fileCount": 12, "integrity": "sha512-DvLTpkkIMN2PZ1LvXCup8xxSuekSCvUe/n+Jh3eapwRAmQ3ev6zJ9WKyH0/YwJgNFVfwtI2zV668SwpSpEghlg==", "signatures": [{"sig": "MEQCIBV4Dq7hWvc+wVvXd51DzpIqPy8ZiXzlCI4w71AURcNhAiA1bcAfRyJmIRVPWrEx/AaBHUGfyKiyJhmkdH0eQSwI9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6b270728beb73dd7ecf1f11233be17a9be266963", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.8.tgz", "fileCount": 12, "integrity": "sha512-UUmQN6MKFDWh8NJeBEXWj6u8JHLeewRCpPu9m1agunS1+1zJX5NmJ1laBiJlSc4ydCtpvq7XeIECgM79n4JstQ==", "signatures": [{"sig": "MEQCIEeezt7nx1tOP3Q4JeIDUaa6e27eXqU+qTcO7miEwNoSAiB6s5MOAwbC2jRG+JtNBt7i7kU9vBwWNpEhaoMsTOonvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90095}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1b6e0fb6fd204666885c3d0811a87f8714d03591", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.9.tgz", "fileCount": 12, "integrity": "sha512-DLdj1e6oX1Ef02S9SadNN57S/e+VWpsxSct/9nzI2T6onQt9elajEje9HlkA6Pit8vyFJrPORZxX5u4NEHlHhA==", "signatures": [{"sig": "MEUCIBcE/XfDeZfrz4YjohWTyYBybgezURWzwkqwp3AvamC8AiEA7QGy8nTsvMYwJhY4T5u/zz1F4d6BrFP52qZn8trhikU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.10", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.10", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3498ba6fc5bd52246f43592af425252b8d70b300", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.10.tgz", "fileCount": 12, "integrity": "sha512-/bazewK4IjuRec4fYqgElfGl07VCKMoPpVOS9TSVznp/OMODmFheyXXI0jB1KD+tC8boXjx27FyQDHneLI9kJA==", "signatures": [{"sig": "MEUCIQCN2kQUHZ7TU8FVVXYjudzONzNvpwPku0div2pcYCsw2QIgbQrNHhvbyxbr5Om2rAw+xziiy+ZT5eUdCV+HGoppvj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.11": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.11", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.11", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7fab13147236a2388ddc5ba1abd7440c50481d56", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.11.tgz", "fileCount": 12, "integrity": "sha512-JNwP1UXrmHyMZXND3hjBOCXLFu01w2EajgvHOqkjlFAtGy02XTKp3GLfy6Dgy0FRSXQdy29bEobP+A0VmBJ7hA==", "signatures": [{"sig": "MEYCIQDih6cNfv0LqRqg+dgSw7tb/VF6mp2+tZ33aG5avzsV6QIhANO9TCbiCV0zMtqw03Nrv358zaLJMdabBfgDlSrdCXQA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.12": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.12", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.12", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0e63471916dbde8f8e765d2fb991dbde85454e84", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.12.tgz", "fileCount": 12, "integrity": "sha512-B0om8yzFEI6olaTVsRYHn+5fHWgMM7bRAgkYz1C8hy07O9vNnrQqoJYBs/pUZ1LeCctBom0Ch4mrCDMV0INqaQ==", "signatures": [{"sig": "MEYCIQDSYB9Aa7uJS7IF+UiT2IWGb9Lbz0NbS6du7JpHibG2kAIhAKa3uIB7FeKBvy9FHcMoE9kJolp+e5CrGvdaNKChTKFt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.13": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.13", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.13", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7785ac9ec9cf19e263adb62caddce2685b87587e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.13.tgz", "fileCount": 12, "integrity": "sha512-AZYQAuxYGlYJOnTbtcQ0sJRPnrkWLaYbB6++BAV1wNZkRs5PUXFoJd3ivMk1C2rqXfSlseblK6CJMde+CNnp4Q==", "signatures": [{"sig": "MEYCIQCHFqpwW2pvqbIfrHY/5Z0S4FvGnoCwjTa/vOCeuCHv6QIhAMxGSEmHXpX3mepv6wZ14cu3LQuCc2x32oq1lSvOsw+W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.14": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2-rc.14", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2-rc.14", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "69ae18564afff62ba1b2a66e5fba252018567ab5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2-rc.14.tgz", "fileCount": 12, "integrity": "sha512-0tCoWiqaGauksTS03Kaz9YFXYfVQJ7LyfI9g1ZjqrkpT58J1TkjrBEPOOcvx0Ca7cpTvPlRQnoKeVe04+jvyWw==", "signatures": [{"sig": "MEUCIDjJzmwuVi8Rg+IpU/QEKgPK9pqYUK2fbL67jiwFZgG3AiEAgIVYQfcSwMZsTtypSwLqkIKQMJBoJuZ3QWvyuLBKTMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.2", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-dialog": "1.1.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac3bb7f71f5cbb595d3d0949bb12b598c2a99981", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.2.tgz", "fileCount": 12, "integrity": "sha512-eGSlLzPhKO+TErxkiGcCZGuvbVMnLA1MTnyBksGOeGRGkxHiiJUujsjmNTdWTm4iHVSRaUao9/4Ur671auMghQ==", "signatures": [{"sig": "MEUCIEhdh9D6y1SjJTO9dc131DATWwlyUB8EphX9zlEFmu1/AiEA4dE71RZY+bIldu1l+Wxlxe+Vc2mwEtHJBf2djXPHECE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90057}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-slot": "1.1.1-rc.1", "@radix-ui/react-dialog": "1.1.3-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "04b3b4210ff7681eab75b8d133cecdb709dcd416", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.3-rc.1.tgz", "fileCount": 12, "integrity": "sha512-qT/Nxck9ufnQPcsGF1SBEqDktWFku/3Jk+f9JeTSQ7Aez4X/eQwwtuPEfF1sCA8Qvn5yWhgbOckGj06Zbpjxxw==", "signatures": [{"sig": "MEYCIQCzJzqyGB3P0taHByqWq8hKEauJyLWn1ObPIn/MBBnKaQIhAJhqx36jxsFA58aEDD+1mrrGZMXww46KitipAJXE85IQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89866}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-slot": "1.1.1-rc.2", "@radix-ui/react-dialog": "1.1.3-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54269b81a6e99125705676be1b79345f80536520", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.3-rc.2.tgz", "fileCount": 12, "integrity": "sha512-9lnnmo7QqFBzGJFgtmbGiGsZsttUbAkNQgnGEyupqMeO3biAu5MhGa4r788kdvvFLFU2Icu+nCUNgOSQvo5i6A==", "signatures": [{"sig": "MEQCIAmrNtMQ64ycTwHck791QlHkDjjJAuPvCKCOyj6OsxR8AiASLLpg/NrX0sSHZEpPkTNZjyQJj/fC9RiY9ckfRaippw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89866}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-slot": "1.1.1-rc.3", "@radix-ui/react-dialog": "1.1.3-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2f5611d7fe851d0720953a6171efa32ca478b941", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.3-rc.3.tgz", "fileCount": 12, "integrity": "sha512-pS/viNES7vqaxD3/UXOnVm7PNlJQ/EFO0yMKChAjgPNh156G+uYiQHMtmrAC+vhFjDNs2IeCsaj6q3RqdYZbcQ==", "signatures": [{"sig": "MEQCIH85Ct77TWMozmTt4mq9N3oD6iHIWSpdBkIU+yYOJEDHAiAF+aYOk9WZKHlR75XYEq4ZU62P3C5igyVzmVSSKGbS+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89866}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6bcf7c958f42bc36285febf55f36e564d3c9ecb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.3.tgz", "fileCount": 12, "integrity": "sha512-5xzWppXTNZe6zFrTTwAJIoMJeZmdFe0l8ZqQrPGKAVvhdyOWR4r53/G7SZqx6/uf1J441oxK7GzmTkrrWDroHA==", "signatures": [{"sig": "MEUCIQDG5H/ktHDs//DVc33EgsuYjuRutmOBI0ePM42kRF3enwIgX/oAbd9OIR5SCYmPf9lNn9iQX9/WoHxYTnVBtuQLn0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89808}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1049b8765b3db16b2a992e2fb3d4afbef0c451ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.4.tgz", "fileCount": 12, "integrity": "sha512-A6Kh23qZDLy3PSU4bh2UJZznOrUdHImIXqF8YtUa6CN73f8EOO9XlXSCd9IHyPvIquTaa/kwaSWzZTtUvgXVGw==", "signatures": [{"sig": "MEUCIHpredB7Ey7skxvoeaS12rltW6Znfq+D369t05GapeH9AiEAz6CBEHlBU8bdsP1oisVuReTD/mh9zDfTZrkC1J9n0gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89808}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-slot": "workspace:*", "@radix-ui/react-dialog": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "30a6396a79515976a775c6160ea88fe676669b2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.0-20250116175529.tgz", "fileCount": 13, "integrity": "sha512-3MO78dpCRkU2RM4FyKw8YBy4+m7U2wHhkyb2xOXDqXEeSjDEM7u9vv4sFc/lCycDJgt7Z5kP6Txk1wD0/eVbrg==", "signatures": [{"sig": "MEYCIQChf1MUjl97NO0iMohUCtWucDtWQQzX/Np/uC5BOniZ+gIhAOJRmI8GTC3nIM0pbE3gpmeMuCMZ1xQ9/5KlGQetSXQ+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-slot": "workspace:*", "@radix-ui/react-dialog": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f904bb6e00ffc0aed5027b14f50dff767e40653c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.0-20250116183145.tgz", "fileCount": 13, "integrity": "sha512-uguwU8qcteHMERsmyMA/qjVhYBeiLo1QXvQpoDfAPVKZkL4u79rG38bQgfhBqeUzjLWsyYCxnzUy/Hg0wvDvlw==", "signatures": [{"sig": "MEQCIC9v7+g9zLTta/wjhD5nF44CKTRTk37gDh3Ex4dkF0tWAiBatlVzux5N7CUERE2sxjhXstUxuQ0YKiL9VIoG9LdzyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89343}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8bd4ce48bbb69e6153dd4e3f7fbabf4d2c4e3063", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.0-20250116193558.tgz", "fileCount": 14, "integrity": "sha512-swdEAh3GFa/PFkXRaf/hFPgCwamX3/zVI3fqcOdtGB0V2/xFfSXa0HV25re0CeOg+dvuA2ZDoHOTnG01qKXDwA==", "signatures": [{"sig": "MEUCIQCVkqKTEQ/yV/0xxc09HfbYDMt/MZ3qhQEa0SSB3UpQQQIgOGVAdQ1mL4u7JJYeJPIt7kxyNy+dGs3mS9xUltZahTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89538}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-alert-dialog", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "811df03c2ac4e92402e988c385c5ce75e091a3be", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-0.0.0-20250116194335.tgz", "fileCount": 14, "integrity": "sha512-mSbtOhatWF9Odg49Vklm07/hZ0f2xYYBisCKzV8bADznyPzBJs9VPZ2ypLTHYEf73OB14Ci4WqHcy2G58Sd5ZA==", "signatures": [{"sig": "MEUCIAb7ctahgsGICfc5QiTPCcESdFKROETsacSCcoivpgW3AiEAzgrjzKC1pPnTC76EGJa18S+WDOmcAwGZ4AV0zCagWa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89538}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "26637f900b449102e5256d3c63fdd6bf7254c64d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.1.tgz", "fileCount": 13, "integrity": "sha512-b1aH1p1qkJfyotdSnOsPq5OkeHST+JNNGhv1+DMb0ZiWOCwpcy6JyZW8ssCWEZMBcsNdrcRmp9iwlbJnOr6Xig==", "signatures": [{"sig": "MEUCIQDMY5dUir5fFFPBf3f6fzvwqmwSL6W1btUclT91sEviQAIgRU9r42AuI/alw5ehL45K8e/YIumx9iO6ckVVnXsl/lg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89578}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "592111de2f4d21372c484e8a3edb9ba17566bd1d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.2.tgz", "fileCount": 13, "integrity": "sha512-ndqFF0/LIno6Mufv8uChBjaOqbBIoLshybPI6W65NPjvnMp74gcWEzOJ2t8iIG74MLcjVHcj9oMGhirV40A4nA==", "signatures": [{"sig": "MEUCIDKIkV7YnkuWnkp/STZ0xY60v0OlzB6ZxvE5ylv12J+4AiEA2Ex+6FXR0zXRxA5Xz++F4hbZ0JK+MB7LCd0aYKA0z9g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89578}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d34ef3bd2821ae9ef1fb850265a9ddccd3cf7e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.3.tgz", "fileCount": 13, "integrity": "sha512-SHb929sflmzIgmk56KbU4CGHsPAz/cghHo3nFbPkTPzeoPik+9Wcj2QzGnvVDBrOat/PgJIY81awIo6XBIl0NQ==", "signatures": [{"sig": "MEQCIC4lUQIQ1rKbWH99RC4Vv4hNa5oTXXTT+maxlE73880mAiBj4HKRHV8F2HoYBIzyKvf1p9XwqP2Zkpjfh7fDdVfZqQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89578}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "71f7b3f6646be7405456982b771060ccb2fb5ac1", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.4.tgz", "fileCount": 13, "integrity": "sha512-skhWPV+EdfIopVGxFnFMCmmUZP0yz/Jkf610MwlhgnWQstiOxQQzfwl06Xc0f3zMlpEsBxx4B3FDiqlVueEEdQ==", "signatures": [{"sig": "MEUCIFQfhmPmClho6fYsTAwDJB0i3vHH+oExa8cNQuVGGit2AiEA+7a2PLCxduFwQvhcsajSkAUyMKAQYK6Okvq7YtrzMV0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eca26a291fc31d94ea59694011c55b0adcfc6f04", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.5.tgz", "fileCount": 13, "integrity": "sha512-3irC2hm6t5mlsuEsI7cG8TleBUbR5ls5G/e+Gb6XM3KaaAWYQi/E7MMDTzxgpiBeHiWxvkRMob5dSu1KsL31PA==", "signatures": [{"sig": "MEUCICmD5hJAnGaVX4KdhBzPA7Seog3UyzmB/ZLDd9+RuT8CAiEAxXH5GUPeDclZzF+3Ze105Qcr+HxdzE+o6u5ESRAHwKY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.6", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2aa490d511d4f6dfa76da1a3eec3355810b5ac60", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.6.tgz", "fileCount": 13, "integrity": "sha512-uWXLI8xlu1rQmjbaVnw+GOX4iDe8OVeAzBpyQPkNeQk0/1izwfffjMK90wZJWMoxsq381ltVbQ4RRMUjQXcuhA==", "signatures": [{"sig": "MEQCIBBIKYLWDV4j3MkV96+9Uqn8CRivLsoLq32xrUSIBmpeAiAVXFpAp+NDTJscvJMoLFE/QAkpbJWEAoSGcs6nyeYfnA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.7", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "02cfbe62be5027ce1430ebd4114e2e86165fb2f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.7.tgz", "fileCount": 13, "integrity": "sha512-ADk5NfXzW7S5XLFkUIfxBF4+3YhLq4iCxet1+QEUyjN/zUxaQjuqSbXUlopXIkGwBG4LQWtwKEPZ8NcEiMPuyg==", "signatures": [{"sig": "MEQCID5qfMBQhJPKe9tUXyYJKS13PA52Qqy7lpvbTugx6u9iAiAQq5WomTfwaMooharNNDHqXTfS13It0fsIl3nuYufggA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.8", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c448307daa85446faa9f83fc0869343ae82d3243", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.8.tgz", "fileCount": 13, "integrity": "sha512-C+O7iNtEfxEXzMNfRmSd8nQikAEeEzacGpAa3cAnA/SDeNRrSAVPXwmQElW3b9kalj7OdfyPafRX6uS2MakkQQ==", "signatures": [{"sig": "MEUCIH9XQtXJYemGhrd1YUHWNp3IPzI+w03+Ot9VZlLsiYVWAiEAuRqgKD061QfLkYaZjtW5yUE4TAPH8MX35XwVnSMG2dE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5-rc.9", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fd8b5d862258eb06c6bb7d6744b5441a7bbf2c44", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5-rc.9.tgz", "fileCount": 13, "integrity": "sha512-8667xRwPLB4hCZ26Z9H37A9axFqDq9qUKfLVe5Md/mR1wfvRJh/Rdpl30LTDM5qkywCwdr4b8YRrfYrxy62rmw==", "signatures": [{"sig": "MEYCIQCYbqboq5UKA3f+vS7sm8nNz5yT190g/QCFL/jxrRyudQIhAI8xPJ4Rx7wi/iwAVPu6KWXT1V384xm9mmp5ue+s0iSk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.5", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d937512a727d8b7afa8959d43dbd7e557d52a1eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.5.tgz", "fileCount": 13, "integrity": "sha512-1Y2sI17QzSZP58RjGtrklfSGIf3AF7U/HkD3aAcAnhOUJrm7+7GG1wRDFaUlSe0nW5B/t4mYd/+7RNbP2Wexug==", "signatures": [{"sig": "MEUCIQDg2FPbtctf7d0qSCgR7CbRFLi7JfM0HhCrk57ibGIzLAIgO8Vu/Gh8PeQFIIMabr1GyEvnb0+rXTqJVs3YjOlD43s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89542}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-dialog": "1.1.6-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3be24be8f3bcdb543cef455472ea413ac0965988", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6-rc.1.tgz", "fileCount": 13, "integrity": "sha512-UapJoYFjLgoJoQd85cGNILJBcNPY0X3JDCtetCRs7qySsuahDchsW9WuolyfydVaC9d8PB8PDL1zZjRIljtDYg==", "signatures": [{"sig": "MEQCICpBQoHriG0xwy3LwVZUQybTr8cohmbKKhV497Yz3mWWAiAEk+0AEq/ro3pYu9QtauxvAAtIZYAa06AkAUtd95KUPA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89580}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2-rc.1", "@radix-ui/react-dialog": "1.1.6-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a98b99aae4a663b70a9b644c814b4b5d61429fb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6-rc.2.tgz", "fileCount": 13, "integrity": "sha512-1spz9vlxlmuL+4C1o7jCKT45RUe48NYVOY7UX9tedTHiSYj3HqNw615Il5D2dtgi46X+yBruE1hTUrggHCJCdw==", "signatures": [{"sig": "MEUCIQC/l3Igrp8LWZfG6ZiUZ40NMlatjmHz5d6e0l92vsS6+AIgPSdoSs5nyAHHK7YcBjZjPby4PVSWVixDUOKFQtyrU7g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2-rc.2", "@radix-ui/react-dialog": "1.1.6-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f39139492d4bda29b28cd0e82e173dcb9ebd276c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6-rc.3.tgz", "fileCount": 13, "integrity": "sha512-7rZQvLmM/6zpH8s0lqpdOBffrBrmGF3K28159OxRidBsoEYv6HWl2N/8Le66QP7MZ4D6HkJRefYVCgC7AnMnOQ==", "signatures": [{"sig": "MEUCIQD3ioGULhx0Rj/ohvZN5/kfXgm72Smv7HKKs0aFbOwdDwIgWZk17Rjay+YELawydJ1U/fct2OfRYdzNYeYHHYK1RQM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2-rc.3", "@radix-ui/react-dialog": "1.1.6-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "062c5d28c18027fa653d99552fdb4807545d44d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6-rc.4.tgz", "fileCount": 13, "integrity": "sha512-DIBQEnWo8RaXWaqpVKuAG8+P+Qjbv/rcDWFrA4SHwXfywEP0Z7HwTRXVOrGqbX4cN8SQjoE9eQKTFmLXvEzOfQ==", "signatures": [{"sig": "MEUCIQDcIlR6Mf5hRf7a8dtZMjHZ+T97XHBpvnDDQs61eF0dxgIgZ1PRXCR4nFfwFsj5fKos6Vss3mYMxOJS4XS4lsvm3io=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89694}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2-rc.4", "@radix-ui/react-dialog": "1.1.6-rc.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "45d0747677d587a9d247db81a733975a07832ccb", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6-rc.5.tgz", "fileCount": 13, "integrity": "sha512-X64Hmf2jX/H3lKpymaiXEDfuhXinMx0VOfdUGBQj/E5JrMaBjqE6+EP+Psrfn6YivT1dGrq8nDaD+TMEJQApxw==", "signatures": [{"sig": "MEYCIQDtBlP89FHX3W/edIk/QYy+opbDw2tShbW5iqN//eHRVAIhAKsiBMmkpXZPRzWtzjCR7HOXZKaF9kr42kKjKOp1au3r", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89703}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.6", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-dialog": "1.1.6", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "52187fdaa5110ed6749e75974e90c3505f788c5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.6.tgz", "fileCount": 13, "integrity": "sha512-p4XnPqgej8sZAAReCAKgz1REYZEBLR8hU9Pg27wFnCWIMc8g1ccCs0FjBcy05V15VTu8pAePw/VDYeOm/uZ6yQ==", "signatures": [{"sig": "MEUCIQC/8PdwnttFJzi2jKp8oXKLUQRpXDI2ZUt2iuAc8lU91wIgHYsZSGJmXt6k8q2xDXiyKV1xLyIL894lAkfKA6OcqEc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89655}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-slot": "1.1.2", "@radix-ui/react-dialog": "1.1.7-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "13b83b4f98df7fa0d20984eb9ae46cb95ac3a0b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.1.tgz", "fileCount": 13, "integrity": "sha512-bd4BQO6lldwzXOEq63FIS8ZGfpNUN+ndiFSUP/R/k3lJqbZ7fdOkOU2g6U4t2mCMCawG/7evW6lOpkTMawG/og==", "signatures": [{"sig": "MEYCIQDC3X6d8RY2Q90Xr02m86rDShfDqNBF4G/GsUffZLXYuAIhAJYtJx2B2J7+9Bj4urP8+EZtLpyhPUARgo+LqsWfA2sE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89693}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.2": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-slot": "1.1.3-rc.1", "@radix-ui/react-dialog": "1.1.7-rc.2", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "577b904e5c6aede32962dffbbcc37a30e7ce0c17", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.2.tgz", "fileCount": 13, "integrity": "sha512-G6oNiRxzd9nnpwScA1nrcUHOANGbFRnbyABCCFXq6GQm04skR87W6+dkuE3WnQ6O7DLp/MROeSexAtdr5+deyA==", "signatures": [{"sig": "MEUCIEfUzO66/T3oUHoD8HeoKGSGbDcNPk1oEcGu/pj/BkRBAiEAix3qkosj1p9xQRqXXtQXdNo54go5Q8PJwD1MndnfvCk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89724}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.3": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-slot": "1.1.3-rc.2", "@radix-ui/react-dialog": "1.1.7-rc.3", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "af8ed8db46b3c04462c00aaeea1cc350e84ae9f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.3.tgz", "fileCount": 13, "integrity": "sha512-xq0GXwb2IpUQZ6kxv0ecmRFv19IAdCDEuVa3gPOtmODXRKc1KyXR+Dc/pHhWLe1TdHJeSrBOuR8qO1Yf6bCm6g==", "signatures": [{"sig": "MEQCICigxofTfojYcvtlOT4GtBfXUz0rdzHnxenHbf1VBHUvAiBsbMWnR6wBADIKW5Oh+DU9oKfGVvy2ct4xkGZh82GxtQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89724}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.4": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-slot": "1.1.3-rc.3", "@radix-ui/react-dialog": "1.1.7-rc.4", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5057d0cc59faa4e4583283dd383f9c0c311762ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.4.tgz", "fileCount": 13, "integrity": "sha512-gVITGi6l2ctiBY70RjLFQrKXwdaw70J5HRPcSrQr7nXY02U3OuPPp5+iY7hnGMjWTDPQX2hZya0TspoZwzTHhA==", "signatures": [{"sig": "MEUCIQCdHZJKnXM+0cWxBVDsEapti9pkpliwFR/1bKZ06L2zGgIgBD+NDf6XFdAiABRSJFfpo1mvp5fJXoW8iVOGSun/YLs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89724}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.5": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-slot": "1.1.3-rc.4", "@radix-ui/react-dialog": "1.1.7-rc.5", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db8c13f6e9dbb78237a11b269923c9c88e8c383e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.5.tgz", "fileCount": 13, "integrity": "sha512-UniuQ8bqe2f4WdV7712l2DxIqyqwWN3pCUHTm6fbUKAA681Yv/8hDVqN5hzIVXK+ZUfT4TtQilOmbF5NF+Fg9Q==", "signatures": [{"sig": "MEUCIDG10RrycAqlNRTwhhjlmJPDSSqWezPqAIeSMI269wW6AiEArlwAmsFzzpR6ZQS+iy1IMZzNjzQLr3slCkplM5mkZTw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89724}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.6": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-slot": "1.1.3-rc.5", "@radix-ui/react-dialog": "1.1.7-rc.6", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "05076e1c6dab1871348f964331fcd3a6a9519660", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.6.tgz", "fileCount": 13, "integrity": "sha512-Yc4pVUXl9wNmpRqiy/RvtMfbaBy2+wif6L5JGBTmNUX3CrI3EyaxVz01QMhVRFzeWqxMrKjrzifreiE2R3anOw==", "signatures": [{"sig": "MEUCIQD/Y2qH5KmWntacBn0//o1hl5cFjsYVwRZpiEWh9hZQbQIgdZ32XTmmxJ5MTcxcAz7XsB6wRxfU9LYB/mQ292b3u/4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 89724}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-slot": "1.2.0-rc.1", "@radix-ui/react-dialog": "1.1.7-rc.7", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d0e2c2888957345da5edbb4408d0fe01ba2db4fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.7.tgz", "fileCount": 13, "integrity": "sha512-EQdCpEraMWWimw4TwphK73xZkB8UwYxu1LrTpbyRAfQx64wFw/o0IlXQg5R7p0xFmHGNJuvhrClD2RFKFNdNmw==", "signatures": [{"sig": "MEUCIDpSrIpKGwrfneyi6hFndL/jRcJJRuxgwHzNKsLXY+w4AiEAiMtxrXsiGr1t+B/tL0LSErcP3Cy+mzOJcV0IqMr48+g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90101}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-slot": "1.2.0-rc.2", "@radix-ui/react-dialog": "1.1.7-rc.8", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d8f5f760df14b568ea5f7323f83210312269a5c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.8.tgz", "fileCount": 13, "integrity": "sha512-CtgkufqCwElLc0USm2RipX2qkrSlFTcSwm5OQezEXNvNbw5MzkruqNUIJ+TOibSszzH5lMHIodrgEL6EV+cTTw==", "signatures": [{"sig": "MEYCIQCucU2Zsa9nQN2Uu82D5D+7cQOs+vapZ1cM8AMkGJ1zZwIhAKMMu4QNwiHrLMiNi9C/e4Z1NIDaLPYoNbP5lXUPAYwi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90101}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-slot": "1.2.0-rc.3", "@radix-ui/react-dialog": "1.1.7-rc.9", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3ed39a448d53978e30fc82946b6ffa8baf498ae9", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.9.tgz", "fileCount": 13, "integrity": "sha512-3oFoyp/+D+KqYt8Lwl6+c3JngAkpbcOMomAtYCMghnglb1j/yzXI/T2HlXoK2JHBW2yHIm+G6vGsofX31XTETw==", "signatures": [{"sig": "MEUCIQC2XFV14l5hXn8ExIg/sAiCsJXmwulQ+KzSvN4Lkjup9AIgTtESaZw2bhmoWQVcdBoWleZQPZqQC1lN4x8bvvsxGPc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90492}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.10": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7-rc.10", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-slot": "1.2.0-rc.4", "@radix-ui/react-dialog": "1.1.7-rc.10", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a1e86b59dfb8f44d491cc79af56b7bfcd4d18bb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7-rc.10.tgz", "fileCount": 13, "integrity": "sha512-FUjFEMPy2/ZncAVPkrw2y0Po0jqLMA/ujIS9XPHmqQwUZTVaGhBFqLvqSBPtYLLOg5ZVxE7R92OdT6hHJwSkJg==", "signatures": [{"sig": "MEUCIQCLmfPEIDE9jGZP4JUBTXM/UQ41y4wH6UYsFSrW2Bu1cAIgM/kj+4FKAVH6BvPi9DjaqdXCEVZ8BI0QlUrej24WOKs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90494}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.7", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.7", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d4e07dcf9a3c67c45225c20c2800577773469454", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.7.tgz", "fileCount": 13, "integrity": "sha512-7Gx1gcoltd0VxKoR8mc+TAVbzvChJyZryZsTam0UhoL92z0L+W8ovxvcgvd+nkz24y7Qc51JQKBAGe4+825tYw==", "signatures": [{"sig": "MEYCIQCIp+AJ1yZzB/w7rPLMSpm5nHHFYScTQO8A0f3APAX1UwIhAOnd5rGMFWFZhgdwq+QBehFfLpXSQe4bBYbz5Z2T/b19", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 90429}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744259191780": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744259191780", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744259191780", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d7f9d76d37eece1ffc41b1b28f75c04eaaf6c7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744259191780.tgz", "fileCount": 12, "integrity": "sha512-JNuW9e5i2lPtig+u/n4qY/J6FO1vGkwV6p8g1HS+zzZYlqdxAjpckjI6vYdV0+y239aO3iQLigiZAH/f+Cx99A==", "signatures": [{"sig": "MEQCIFUIqtL8p78E3CyjCh55IlGCHGC8RYYOppdxj8g/BsxoAiBWflRfZcwl23SDHhDbBBZX3ayBpQGATEo+Msfiy/T3dg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744259481941": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744259481941", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744259481941", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b3f57fb6f156883baa4a64f13bc5ee7e1729732", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744259481941.tgz", "fileCount": 12, "integrity": "sha512-Pza5mrqK7pqySaeWkreBfOPnKYP1YcMDcpkMaNyHwsUMYtinSRo7l19T9dmp1STwZwkZaYyEIiTKo0P7jq061A==", "signatures": [{"sig": "MEYCIQDJxoUTSPxWt2MHz2Lt+J4jxYMWNydiAPyAi/heEV9uXAIhAKvyyXlD7fetnVwOVnr69YRmgCqrXoApUi67AR4SIuC+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744311029001": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744311029001", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744311029001", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f94cdd2932605295d24623d6cab4ae5d1981cf02", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744311029001.tgz", "fileCount": 12, "integrity": "sha512-vANds+EbeMFXeqNV7zBM6AEJT3O+HwnCtH+GlVCXc2HWbtlCO0FwzxcpMvn17HohpkO/7u6GgM0V21dZ1DBxYA==", "signatures": [{"sig": "MEQCICRR9NgPl+HSJHV1tf/J8xlPegY3wpUCi74aHBScBZnfAiBuIezRLYzRpcvrRgMkFg1giFCwxIB5DoLkM95p6CRHIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744416976900": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744416976900", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744416976900", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "302e6a9b61974675a3cb8a12197706fa197d5659", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744416976900.tgz", "fileCount": 12, "integrity": "sha512-IIIAAjGSTgR1HjnZPqiifOljgD2ML+SQt7RMW1vMd0NBDNNx7qtQt5VjAi4YkSdTe6as0sUAcBlqY6LpqRrSyw==", "signatures": [{"sig": "MEUCIQDD3ZM/lhJfq+bQLk3t7FW9STIhzaZiHWP/faL+Jc/EygIgVeOYitYxasy44HONb1Z90DI+QqWfk7PdGpgZvKuLyCw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744502104733": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744502104733", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744502104733", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d303dfb70364aa4cf361d0c2df918dd63180b6b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744502104733.tgz", "fileCount": 12, "integrity": "sha512-D+MHlb0rgYKW2139h2CB6AnTGSJLeIVooeYNYVVvxmQ0N/Y2IAULbhc4oAJmp04+DUxwYVNDGWmqmEd1JzsclQ==", "signatures": [{"sig": "MEUCIQCP1WglH5sOgx99VrNO2inemZBOLv5TXWgx3euoBw4cMQIgUZfSWrRynbqxmb2Gr24Gc6thGtfnOgDC9OJBcHxo43I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744518250005": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744518250005", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744518250005", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b630c71f96d79174f9c49071c4a1ade32d721724", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744518250005.tgz", "fileCount": 12, "integrity": "sha512-9yRU7mw0wOmznnaUMDQtpmMqHeWbYd6M+KDhgg54eh4WtsIt2X3rgkDwRs7T17gc56qs3TupSOpUoUVW3olkKg==", "signatures": [{"sig": "MEUCID0VAgVaQY2H5SJrv03E4MUmgfgefapoMJspYsMYBkW+AiEA/CS9ATVGPWcGUgnVjCi1fB4CMDOnnyA51luW/F1hpLY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744519235198": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744519235198", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744519235198", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9eee814ec0a4a5313bf602aafda75b5cbe7ee45c", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744519235198.tgz", "fileCount": 12, "integrity": "sha512-yLxnz8ySwcqJKnNFD6TTE/TmJBhukwDgja3Rike27b1Cyt6bLvwexIcP6rqw/OxAKgWGJlNDthiIgMEJeLYo+g==", "signatures": [{"sig": "MEUCIBlFLkX4GYCkZM8uWSolT/QNNdsfRfQ/xYJ9Cs6uJjg1AiEAl1hnzG5XqINPaMu4GIJjjq/J2mAFUBtM6EbxO9Gs//0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744574857111": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744574857111", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744574857111", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "64f033f5f056aed61f2a438d4501600577b46d26", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744574857111.tgz", "fileCount": 12, "integrity": "sha512-cjsQwefdn20+9w8/88uWSxDy7AlonQYBV/qWznfQx5gA9hE/8BHpTt8z0fjgeChlVcn0IX9Ph3uPGn80eMA9qw==", "signatures": [{"sig": "MEUCIGBlYpnquf1JOxDM8Maunn1M4RFqYkosibMNNQCPkyAZAiEA7JtRB+4xmdIM6+0AzXjWxFjv3growFv8bQ2Yhqj82LQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744660991666": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744660991666", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744660991666", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3bfd53c76aad2c3f3abe8cb8ebc78461825e67f", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744660991666.tgz", "fileCount": 12, "integrity": "sha512-tOA1oxdCEeysT/WEU3Zerg7YSeWwq37EAd4dw/0tef+V7wTrnGKt5OX9eM1+4eYYThHMoePlHQ8bF7bMTlyUEQ==", "signatures": [{"sig": "MEQCICQc/y7ZAMUJLyl3ru15fF0uu/LM86n0DhofVZQ0fwwjAiAKO7Pyc+fbEaG3HNbF7QfsPg5e/Y+pto7R9D8JYW64Sw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744661316162": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744661316162", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744661316162", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a61862ca993742d421fc3b5b11c37bc9e4c08dfe", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744661316162.tgz", "fileCount": 12, "integrity": "sha512-U/ddPAd/6ufmpm3xsOoqRHDG/eGA4Cec4PKatNUSIQj8FPbYM9L8z5RRO8DOE0PE5NjtgG5xyquOHQ9mOA3QRQ==", "signatures": [{"sig": "MEYCIQDCD7EULzVYjwWla0h/PGhNAj/rv1vwfU2CJDn6hMlLoQIhAOFzdBnKs4FCaeF9fX0cZg1dw4mDREaOZClrW/wn2nWi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744830756566": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744830756566", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744830756566", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43e9aa1fa8601c41b6de89f05666c011f55f8a4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744830756566.tgz", "fileCount": 12, "integrity": "sha512-QEKDphOFaekCQR7NEA2TQfoLWsrmx0j4Ox4LD7i0cW37X/oVSQFYaqOZ2h2P2h34/VdYLr3WEtAHzSZSvYaxBQ==", "signatures": [{"sig": "MEUCIQDkfHxgs5wEzrr0rezizIYNSVlfVYP/aPgD/fF/Zrk3CAIgchRWezfP4/qKh0xiHlGcPi5won5EmdZ7BtLbHA9Sr7w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744831331200": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744831331200", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744831331200", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7225b8946491d00a04183c15bb13abc70ff78c06", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744831331200.tgz", "fileCount": 12, "integrity": "sha512-i08ieeE0HPaKioxtDvh6NdNreeIl/2UTb9YZRjhMljCwrtltz8lCfT9duZwQ442Dn7cTh2V48XdLxUDbZgXCGQ==", "signatures": [{"sig": "MEQCIDixJcywoMzCsLFZg406TNvee3Ky5bRlUn+2ScJUDQzZAiB41A9qjqj3Pt2lZa3SsQtDUAlTWewWB7OqjOZeOJ34dQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744836032308": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744836032308", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744836032308", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03d081f53e882debc7d1f58bcd4417b39085e77e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744836032308.tgz", "fileCount": 12, "integrity": "sha512-gDf+lhObiVhmSBbSCdu0Bp17IlhjNfFynaH59h9HHYXcL09xojXStohrkGkPKvZXMER9YjFADpdmBmiWm+eY/A==", "signatures": [{"sig": "MEYCIQCVDpW4bUsbrZQMYTkjKJ9fpeL4+MM1nSZors6p4M6FqgIhAOp6goSLcSdhEyaF6s/yz7I+oaUcig7Z+UwJFCWhiI+N", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744897529216": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744897529216", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744897529216", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4e19298985fe8a581baa27a29ccb8f3f14ef9680", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744897529216.tgz", "fileCount": 12, "integrity": "sha512-IutEOQMzWGGnzT49WARRm+ijXjA4wFmTZdIwBf6oHbHxvv+2j41oklKbfOZ+HvRyQtosS9kFO7s0zuq+pNB1lg==", "signatures": [{"sig": "MEQCIAjstrkYYf+XOC1yYmMKqtvXGnZ1yaLhoGxRQR7iAi7TAiBMSizQTwPhxajNOTh4ES0xsdrh/xDxM470zaneNgNiyQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744898528774": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744898528774", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744898528774", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7268bf906ebcd0c514b03813e90e7f1eaeba9579", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744898528774.tgz", "fileCount": 12, "integrity": "sha512-Qywu/aZL87Ouucyba0my9jNIDaGa6Ca4+TBP3/U01ANo8xxpWOc5kgosMHQSXmI1Wfbq04V4wHXUHZdwnG5dmQ==", "signatures": [{"sig": "MEUCIQD0B5HsPwg9oHp+x0/wF5UD2B5j25SSx9VvMJnaX17clAIgM2E1FD3P9PFWDFhiJG/dElpjb/h+e6875LNVp5sLVCY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744905634543": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744905634543", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744905634543", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3a82d46ffadcd2edbd9366be99939923fc18a7ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744905634543.tgz", "fileCount": 12, "integrity": "sha512-zYO/HguKDsutuy0eNTnvHCKBMTv+sWk0cZBZDWpBAFd2iqP71RrKofRMTfy1BaDpt4hCESLIHlfiEaG8d3hB2w==", "signatures": [{"sig": "MEQCIHjLqpFWnkw/YdkCkL3xRepkoO3yV7I2h7OcrENkHR6tAiAC8vgDu0za3nP0gX+tRAIY4S6pgHZEMNcsfC3fuOSZVw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744910682821": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8-rc.1744910682821", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8-rc.1744910682821", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0254f7e29da1d2561213d0647580b2dc9f7f9d65", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8-rc.1744910682821.tgz", "fileCount": 12, "integrity": "sha512-kpGvAbYuZJAoGyWLURrImF2K88/zh9WQa9Z2W9bGJb765QWzT7uSdPuk80X2y6HBSwY2gfW+rkF4TaK2A125xw==", "signatures": [{"sig": "MEQCIF/gKHCO4jC1YD6BluEoGySyu4LLBULxkWy/XuwVkIBIAiBvUu8Voc/8aBy3ZFaTRiEJygGH0OjkFf98KxZteTQ4ng==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.8", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.8", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1671fee0e5ded2fc4cf6b73e7e112a9f844df8a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.8.tgz", "fileCount": 12, "integrity": "sha512-inu0/9Rm4fRyUMS8TokGzMJ2K8ts4gr8kcKiL7C6AUqMdMm9DV5LAvup3K+R8dR4u9gUhSteRomy4cjeOK4KYA==", "signatures": [{"sig": "MEUCIQCg621A5xPyNM8IykU2R4AMvVEXkziMEzTO3hLV2wG+CAIgegPo8UBQL7dy9buBFQi4TDV0umlB7O78AjJcP5cLD3U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78187}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744998730501": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.9-rc.1744998730501", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.9-rc.1744998730501", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3dd4b9f52433a31dd0259487b211cbf19d0c39d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.9-rc.1744998730501.tgz", "fileCount": 12, "integrity": "sha512-jOtwM6x/XI7tuVHiPAxntypzhXStzpqLDoooRs0kbFvYiES1r0UbwA8Jw5j67h2aqG0CRzdNTXa9scAPtPf1aw==", "signatures": [{"sig": "MEYCIQDNdstlPz+QmFdJ9GbKo094TpKHujbzG9rECLSp6WAnYwIhAOK2AwnBnQpv8Rgv3H+I2AqSZ+rINwkMczXC2AEY+hR0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744998943107": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.9-rc.1744998943107", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.9-rc.1744998943107", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "622499c403269a6bdd9cc0d918f7e16842a2f6be", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.9-rc.1744998943107.tgz", "fileCount": 12, "integrity": "sha512-UcPLg7s034TCoI+NKjJRYFsH8yQ1AdSycaVnHgb39k7xXnauoBsEkMgEko4Y+b/m/IX0j5TH4psOeHnqbOIXcA==", "signatures": [{"sig": "MEQCIHXajEJijKQSMq0Q5VMpg50OJ8vRSiN5UuXHuG+jHkQPAiBSzZiBs805ij9faqwSYr2C7+B82vQlVtknZ2E1+yAWNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744999865452": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.9-rc.1744999865452", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.9-rc.1744999865452", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "49cb6bfe258749449e695f9b81321cd8d7db8b43", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.9-rc.1744999865452.tgz", "fileCount": 12, "integrity": "sha512-HRfUCoS2B2MnUPBkxlmDAAFLX75OWaay0JUJ6pRo2xzogTqWIOCiBtLhDq7NaLJQ6mSzL/siXG8P5XHs5bQT2g==", "signatures": [{"sig": "MEUCIQDNeBcCN8fUBN7fV+1uKfnTiIO0h4QTO+m0cy5A0PV6xAIgeIfNs535tqANqyDO1BZY/S6tFlfnvoE2+WvIvIPlng8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.9", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.9", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8b83469eabf78f118e3a138b41d694d0dc66ed4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.9.tgz", "fileCount": 12, "integrity": "sha512-TpLF<PERSON>Z3LFLZ04CSODOq0x3CvreHMF//OMqBIgMaapWsLaTvXn8po9KU9PyZZpCN1MNQGz8LVIfIB51VMk5kqyg==", "signatures": [{"sig": "MEYCIQCiyWgE9Na7pqJpP7paKSeHVR7SzH8A0s8I3zJFDfZlRAIhALlCvzypQ2k7o2c5mbOvZmGe5j13IPiHhXCPwXEDhRhm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78187}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745001912396": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.10-rc.1745001912396", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.10-rc.1745001912396", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c238e9db9ba797881723b3059695046bb9d3c52e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.10-rc.1745001912396.tgz", "fileCount": 12, "integrity": "sha512-h0OWLUEcwuLgtTZbGajUKm3nE1bQzcaHefYFTJKzjIt9xsBeN1akOH/39Kv/M9tK1WYH53O+KsTgVfwVvgIVZg==", "signatures": [{"sig": "MEQCIHmqJ2chNzfCF8F4plUWm4yViftLYpt2TNfbzfx9TWayAiA6K1IuIGF/Oan3weKr8h0IqRQ2rlaW5kRHe7ZXKmAFyg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78223}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745002236885": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.10-rc.1745002236885", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.10-rc.1745002236885", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "95be1ddac49eec522bc986431449236950dec631", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.10-rc.1745002236885.tgz", "fileCount": 12, "integrity": "sha512-o863HM/yYszjQZ2EPBI9eIV3JGViRjVvw4UNa2BphmY5JxWiEa+3H6zkma4WsrqinaTTTd5ZlGPN0tBi/6dFgg==", "signatures": [{"sig": "MEUCIEypPLCAI0bE6YJ/ocJnVcY08TXZbd38Sda/pvvplOQQAiEAlzCbdNBzcM0JoD24sHmCym9r2EK1wEUvV1uSO9DRttI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78223}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.10", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.10", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db6a59771ae5ebf709caf0cc508618931532ecc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.10.tgz", "fileCount": 12, "integrity": "sha512-E<PERSON>+FGNgLiOw33YOipPZ4/fZC2x1zKELDBjdJJleYsM6kJCBp3lvAPuXeUoYEHXNvv9iWl5VRU3IT7d/f4A5C7g==", "signatures": [{"sig": "MEUCIQDDF3FAyY6VjWxkkD7wuEQuYI5k1SJPsVSWTUpyfitVxAIgP/7cfzOsI8JEX+2+v8UBxnhmTvEgTqdE8dYLxXLXklM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78189}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11-rc.1745097595920": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.11-rc.1745097595920", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.11-rc.1745097595920", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88545a02db9b020170fb8a68ff415b2751593a63", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.11-rc.1745097595920.tgz", "fileCount": 12, "integrity": "sha512-5MWBvCIylXMSy1+KhV/kVeZ2P3TORQjtkQ5+wpK7Ha7EexOYrxEud42RJnbuLWT9vpngk0e8szWN2ezRsqE08g==", "signatures": [{"sig": "MEUCIFOjJDaTUpxUunTyNY2p7GYYeeVVI1AVgeL/lEIIPSBXAiEA/amCVPQ9+2wQO30j/oJS5J5RPGJZ6hGg1XV1OKBIS98=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78223}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11-rc.1745339201309": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.11-rc.1745339201309", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.11-rc.1745339201309", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ace8d8c8a331fe06c6bff36d5c8e627af68e465", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.11-rc.1745339201309.tgz", "fileCount": 12, "integrity": "sha512-jH7dRjx93mm0Ig68nKCfCvKsw9d0Q7OBbjByEZ6i48DBmXzA+2yAnQr0qqAx9bLDSOxcWuLAFAe3hZclWitPkA==", "signatures": [{"sig": "MEUCIQDhJtcrH6wUwyLcVUehChP3GBKHLno0Af3ipwwXgaccQQIgGFJSDR7HNgo6ByQHUl+JiWWOL3BePLkGQ89QS3BPDMw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78223}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.11", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-dialog": "1.1.11", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4f7af5e315fed338e97499aa89d96716db8c14d", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.11.tgz", "fileCount": 12, "integrity": "sha512-4KfkwrFnAw3Y5Jeoq6G+JYSKW0JfIS3uDdFC/79Jw9AsMayZMizSSMxk1gkrolYXsa/WzbbDfOA7/D8N5D+l1g==", "signatures": [{"sig": "MEUCIQDA9DnrUC/389IfudZrcgBrLEsc0/yTrPM9Dn3SRBtH4QIgX0tBjXOrcubrq/Zgxbsca8fv2/OpGzbMhbaDNQ5sDKg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78189}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1745345395380": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1745345395380", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1745345395380", "@radix-ui/react-dialog": "1.1.12-rc.1745345395380", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "13f8f63456bc063249f546750cd84105f18490c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1745345395380.tgz", "fileCount": 12, "integrity": "sha512-wq+pvCnCWotV8p/MqQqUg0A82oFlrI1M/DVBXGWTSCYeFgxz9Zv0Fyc+YyXW5gGqBPanVgRIycTdSi7Y5/Dbrg==", "signatures": [{"sig": "MEYCIQCf+7pu692N7qdL22AXHByH/OIX+FFpZo1G5RDs2ygOdQIhAMGsabmVSOXc6XPxIgCbSF0nthJMSnzn/gRlFTFMzICE", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1745439717073": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1745439717073", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1745439717073", "@radix-ui/react-dialog": "1.1.12-rc.1745439717073", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6df453dcc0c83e0ca9e65fc3b93be3b7baaa9fdf", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1745439717073.tgz", "fileCount": 12, "integrity": "sha512-Pb3FbMKFuCEyigOosHMOdcghze4gN6CMNTjChoBqWrD7LJJRD3llUpWYw1EiL2SC+6Omo9+EGYSEyj93hCWX3Q==", "signatures": [{"sig": "MEYCIQDW0+QIfV6OuEYCd4c8nF8PI4Xj319ieKqOAN4Gv9QasgIhANnYk96/+omwzr5eie2gcWGNgDaZJu54Ud+vzBeyDIq8", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1745972185559": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1745972185559", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1745972185559", "@radix-ui/react-dialog": "1.1.12-rc.1745972185559", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "594e20bc7ef5506f79e5c840dca6d05c4f228d62", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1745972185559.tgz", "fileCount": 12, "integrity": "sha512-qx639VbPBCPIjILJtJn+2eh9zS1ioKGyg6Tm7VmPSHwu9Drcu7XT5F/VupQx0F5QeAOqrXfOzf0BLXvwEo2J/Q==", "signatures": [{"sig": "MEUCIQCX8uf8+fYFwCjzfLjhU+fTa4Yl6TgY+psn4AKDsb8nlQIgQoBC/GKFjr+IUuAKzVK/Ozn4EMZ/6/DjFBxNPXFBHc4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1746044551800": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1746044551800", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1746044551800", "@radix-ui/react-dialog": "1.1.12-rc.1746044551800", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ffe7dbe28165d634dff6b547d56e1e86d7c2321e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1746044551800.tgz", "fileCount": 12, "integrity": "sha512-GbMjISlZIMKyLG7cVbLmYbIcrKVk83E4f8D3tTIDutysp7vcN4qa7yer5/yPngVaWw/Z6eI0jIrpUZtMYef4kQ==", "signatures": [{"sig": "MEUCICUv7JQelWRzmHyFr/pDmx1BjNT0ndWz/SGymeYMFT7YAiEA1GbybH+AdsNmJA1hNnafoREzD1ivp5J1GKGt+gPavrk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1746053194630": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1746053194630", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1746053194630", "@radix-ui/react-dialog": "1.1.12-rc.1746053194630", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c856148875ff77e728140b25273ac4df1d5ed63e", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1746053194630.tgz", "fileCount": 12, "integrity": "sha512-n1d8C6sBRk2f/IGVP3OIUO1z4mZ75NQzGRf4UzHshXZWPYdRU5vxsEeJLy5OP+QE1TkLTco4tcAY5y2mQWqNww==", "signatures": [{"sig": "MEUCIEFZ3zNJOmz/NmEAmXs3/6K8n6A5L8uArA7mVOua4SMkAiEAkM/6B1Uh/UeDUepPKZ0UVGdERdXsxDhfJvZaw/Qwg7Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1746075822931": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1746075822931", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1746075822931", "@radix-ui/react-dialog": "1.1.12-rc.1746075822931", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d81f493f15dec4cd6a8389c50e81da026128ea99", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1746075822931.tgz", "fileCount": 12, "integrity": "sha512-WyY11rLFQ8ogC2oj2A+Xc6Rj5Lp0ierDEuRnzdfLiIK5MMaxY5/M1z1otgqAe3rI+54itOSP81f2UNFcHbKhHQ==", "signatures": [{"sig": "MEUCIQDvwHuAv45OpjW/qTWiTyH26IkfjK8IgJu++l6JQn5awQIgRpdQ0mDnVyqvw/zMm+01QFtdtdyGsl5FEHx/RudV028=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1746466567086": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12-rc.1746466567086", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1-rc.1746466567086", "@radix-ui/react-dialog": "1.1.12-rc.1746466567086", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "813796650eeb5a87588e2e7c95315b7cd7ff6aea", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12-rc.1746466567086.tgz", "fileCount": 12, "integrity": "sha512-soimdqhlA0ElnQDFHmQhqDx40qNyU5pKiXAeUVfm1d8DNP2bTz22zKUDsqF+dcFlENdGs/8Vwv758lAOsC4sbQ==", "signatures": [{"sig": "MEYCIQDC2Q0Y5V2vzynWgaiAHI7ktsTT5W6KIcMD/yt3WX7KBAIhAOWVWih5eBXyht81rzvFHP6iUuqa1F7PWuoxef9gBZE9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78257}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.12", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.1", "@radix-ui/react-dialog": "1.1.12", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5cedc01f454fa50282acfaf40160b097fc79ca25", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.12.tgz", "fileCount": 12, "integrity": "sha512-iw+Cf0uUiesiofUxae2Y1uRmqA4FJEKU1lgQ0aFdKsQFvmY6Hw79NsKB5B2EddMIamLZjgTTPrBtA/AMnQUM9g==", "signatures": [{"sig": "MEUCIQD4NcqhDzavHMk81sIPiRMPxHmmVpAD4TY4+PFzg+7AbgIgcwzerVZQuwf/5UGF1ifN6SfGyyo2SaZX7TJS55g3GpU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78189}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.13", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-slot": "1.2.2", "@radix-ui/react-dialog": "1.1.13", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "00bcb71031c3d711713b77f6e0d6d278709cfc00", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.13.tgz", "fileCount": 12, "integrity": "sha512-/uPs78OwxGxslYOG5TKeUsv9fZC0vo376cXSADdKirTmsLJU2au6L3n34c3p6W26rFDDDze/hwy4fYeNd0qdGA==", "signatures": [{"sig": "MEQCIDyUdGfqN5uxvTYaVXc2VDvwpyX8UMu1hWj1Uru/L2C8AiBO0wOemk5DuMESFn7aOAZL1wqYugC1UqiNUZb2ZXYWgA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 78189}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.14-rc.1746560904918": {"name": "@radix-ui/react-alert-dialog", "version": "1.1.14-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-dialog": "1.1.14-rc.1746560904918", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-slot": "1.2.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/typescript-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-JdSpp6qgwLB6ghtH7pMfPkZhDQpBHVJoK8Ueyp9uLdiIW8ZC97nixjbIxCE/yllsUnPkzjkzm3dXPqwmcD2izw==", "shasum": "d42186914ddb2cb28f34a38e471899c9b4aae239", "tarball": "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.14-rc.1746560904918.tgz", "fileCount": 12, "unpackedSize": 78299, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICrOAXEeoLpAhBMyltS9eY3B15aNC3phzGtN+l7U0Mw2AiEA+VBV9LtFf6OOGclXpdwmWvfgzu8q3J1Uee8eDPh/Yxo="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:36.713Z", "cachedAt": 1747660587680}