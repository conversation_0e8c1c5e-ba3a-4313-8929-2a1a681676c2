{"name": "@radix-ui/react-toggle", "dist-tags": {"next": "1.1.9-rc.1746560904918", "latest": "1.1.8"}, "versions": {"0.0.1": {"name": "@radix-ui/react-toggle", "version": "0.0.1", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06410929a6b4cc3c0893484c123578ff5bb6a652", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-i3gzYmkr1d/XfTtjdVfcqoBYJKAFGem1xtOqE0jQo49ptrGY6rF+oyCi1gFOsfqV88fEc3Ngvl7ila37AetmnA==", "signatures": [{"sig": "MEUCIQCifQ1Guu6vCQKqo9n1WTw1HN81Ai3aIzbHFmIlDuBy5gIgMF6hJQDu3v9RxWywP5/+wSVUeRHvI2v5omEXSyZ32qE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12582, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9uCRA9TVsSAnZWagAAK+AP/jfc9Dh40aoevJ8Wq18R\nLDK3uvNoQcYr8Ttc7IzFapoxaKHU2IjBsvsR50PMk2dozZHYVEOgwW1yee8f\nHaEc90FxePX56UMM3D2vh8n4dNLAJ23EzQha7r/wEPhSqtWuJolDnMAXeFn6\nbnDU63T3DT5NQPfSCg0ZbevNhzAj/hTPxlk57MERLsZC+hvHRG60Qwt14DJn\ngnrW2MYoMzcrPR/Fsjd1AmdlaIkOdAQ0jylAzPxgcbo/7PQGc1zNk4gkqFtv\nm9TRRDKudKycMbFKMPv2hLWuyVelFYBDWPrKo5t4cAGBSMpI/uocX4lbM5IF\n14Hk4T2k5vNzJJOCPZ0KiOsUo7TWhhlSWCde+UTiNFnk0KP4UNR5INbxAKpS\nGOx7kUHFDHp00tHfMGt0gafngrwPB7jcl5FveRedDnU/PiWV38YljdQw4Q4G\n8hc5nC6LyLU8ZLslaKiGcG84RwczT02zrB8S6wgdMJr0hC3Mq2Dd0ayyAYMp\nEpfrSomww2JXb+jOL377eTZ1FMHL+5Gpgd179RiazZOp004vkUlh7+2EWJvO\n2zsF6lYAXvPWDBiMbvNbfQigHZBcqpAB7HqpYWvo1lNSwerce3t8uit4sKqV\nziStk6fBS13XdpI98tDuqqBNNhssM3wxAz3PDvuA/6wb2cC175IRXXULCiiP\nAo1D\r\n=bTk3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-toggle", "version": "0.0.2", "dependencies": {"@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e17663c0aaffb5a03c7e5fc6144f7f81f3e94116", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-kvMahKmVu04b8/ilcl5YCvZBCJcFJrOAxSWAk2qOTHyvpX2PsCBCBZJogsdSxEnvrx9Urd6X6mYBXDXZCHyMQQ==", "signatures": [{"sig": "MEUCICLqk04Te+J++4o7ee1e7SwUsdNO1yextEPJ7r2qlHs7AiEAvMA/9WjcPOaykmHJV77/SGggXP6sY2eRwfjbLNBPXEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPGCRA9TVsSAnZWagAAh5kP/3zK6Dh8azFrEEhL0pcv\nl0rq0qMLt6VQC+6P4Mmf4jeeVy0vtS+d5wkPwJUx7YQlnJd1YBjZrmLc35Zn\nG5ZculXdmCuP3C+vEDBtNTeIoXkHaNClYu4ykyNK9EZ4ukbYHvA5UWmJ1X7Q\nSV7JlItO9qpJxaoklass6JA/4+k/RdpMNdLCkCU47ESW7lB+YEgHC6+0dun1\nX+/ZhhwtS2UkoD39qF8MYg1utthODe5d/S7SnEuV5C75pR5b441x+AP0UjOX\ndB5NAO01zO+NU1A8dx6JiQNP262JMhYZWv/Mf5vuihnridyYZh73oXhNMsLV\nnGlDrQI9qXjN1yHNuxsAboe4K3rQLGLesPiQKUOEOk0HsBlLuVoKGsYaguK+\nlFSVzyUypugMPY4SfrpSxPLmQgpFhwbgn7eanA8aHldcbWW+j8jrLZmfVNy5\nQJC9ov4fatnFmLtMF0MPZwFbDn4jTp27OcykxQTtGdlHKfQHbKYyYIabjTFV\n6+uH+85pF/3IMF3wvCnvuk3GywK7eBYQRFh0PUtc4yTuLwE5MDZm2BNB2sqj\nkoCw62AD9m0JlYq1jgB0ixmz/hEKVxmhdrpcYATz33smw0TVK1k+N1yqCI67\nYs+E37aXy4QMtSigcBihy3n3Kx5LXnRxDx7SVL3nEQJuKFPXtBVxVkewPPsZ\nicef\r\n=BhXe\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-toggle", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "edb567726862c4e7511867ecd312f06bb0e0051d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-0xm9njysM9zMqKhhT6xH5Nk9Cqw4qz0NQ8vsmNqHAtIOLbIGfF9R+HuL+tHEP4IgE7Rj/qnjWSQSB0wkEaKW5g==", "signatures": [{"sig": "MEYCIQDPVYI/3V0NUzcDhZUB108sL0TU9iedFwr4M1eo0QH7EwIhAKAnjkE145fbdRnn0jqrno4sMJZL4hOjAM8N8wEfhc5M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g7CRA9TVsSAnZWagAAblIP/0CHnwmQPG2bJ3k686UZ\nD52zXnV+cocz5NiXgCrONs+nmPN8VT6sNGf1Z+o0jVmZ6mmt9w6QSXN3MNmJ\nwgBUhPlfIx9wDmbAaDCk9qK7HJh3ywryZPT1wPDFt3pzH5HwUOKajx4RhtId\nbavA7AhO6IHQXuydwDre9czLt6Dgw+W2wCV66PPJ7qNV3hg5jePwD61WUAcZ\nr7Jeza+jBU2z+syfsQG2nPFn0N/Wh8p/38mqZvqAKjvKHUhtUCs3B3qSB3rZ\nz7x+Oozy/6UACw/R7U1tzX1Z1iQmpJ4xwJhJ1f2RcLmp0bMqUz3sFUBe2usL\nuErFf0F9jFYlRXr67EQNs7wjhHy/AICjLd9gBcoIYde7IF/aUKqXb8dX8RLA\niQnstphPog8EHHYtN4q1Iy+Xg2UVTexBVnjeFez4hNw0I2CLP2GReYnyNdtj\n0IWIu60UYdbM9fWSxYnf/MPZJHwt0bYoSTyRydkx1hcHliNxATKmX5DRuB+q\nxUjrwHzK6tVj00MWKdoqhMBNr3hxar4RbrmfWTNQlpc12+md3Ui+TEdX4+E0\ntaLaKMdB2R8vZvWYdNOvd/epAbXsbHPFp2Zno+rzPo+kZO4SMQQzRi0er9rX\npJOnM/gq3RnX0jzjdrtIwhQlUIvo3KbdENwSRdubxLXhqRLue/TtuFb62I4m\nzUJT\r\n=3o1o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-toggle", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bece6fc02bedd455e4c8b1ea4136809fa4fca7ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-1l5kRt3UZPWWTDBECDi/dztzHZlf1aS1TsZEt4VMpLdgWCA9kKmq4i7ReRbwlqXKZ7frpSQhNUKL6VgsjlRwnA==", "signatures": [{"sig": "MEUCIEK56VkpGFInfpVFZXJP/mv/JVZvGHEfEXeCchl5ExfxAiEA+a/wKeVNqBOoZh82BwlTVCkScWSPpFUoRzVqZX6a8K8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12431, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IHCRA9TVsSAnZWagAAM+IP+wb1XSvEou2MHi1NLPRh\nnc2L8SBL85zmswSzrcJCIgXTGj89veNSec31hVjVsO2Wyfjd3lZQ/+K5U2FG\nwTZ7soFIhtMTOR2jT6jVKJwmbQuEzAdXYAUQ1UQFh6EDFTo3svXtzaikvcxk\nE3V5SCSuoNcLNmH52x8/zOcjjNDw7mSQQCNxQOrl0IytN4kF6lB0iellD/yd\nwV7253ytW82wHUYbMHLGI0sZSvgXDVHbjau2bMlRre/+VVrqNWSDvjvC0kQP\n01LB5iZubjpuRSlPP94Kc8NxUY77OJAEmxlZx4Sbyef2lbVfc0CbV1g04O3q\nfHELE5LTIdngEpGZFan9dFMI9AAheKS0EeEUazJBbPKi0lRW7CZ12F7DPSmy\nbJWpEXfpIt/vrc6PttgmYRokeWy3D8aNHLDWhwW561FyDz9KkrcnQxtFiqUM\n4BAh00iZy1H1ifbeb/thPTA5nrsv2RLtg3Wa/6zfv42itgvDv7SANV7JZvnW\nNz/0DHJYkVrARaVrnb88jIyW7envq2wkFcegxbQqxLajfjsofJ+urlEgBvB0\nrjzFmxu4CotcesYoE+dEo8rJyXG/MiE+MA3qz8Sj5cYfOUT8B+83Zm8VHdDz\nnutwmr551hLUmPV8Dg/DL/WD7adkcyMj8ZC26FQaK5S1PwW++8xPQN2afGxH\nvlZ8\r\n=9BNd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-toggle", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2bb226daa0408c96e82f368b34a988e6cdef7294", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-TXv2JweS8shjNNjXHuyXS56/NqNiLM0+tWVb94zNd5VfKI8alNcDqMGZLNeHx1ocKW/KD+Nz0A4/pTzwDRbZ6Q==", "signatures": [{"sig": "MEUCIQDJzpt4UmI7hQ1FA3wcq1C0xBu2H1aJ+wLK5fvs9iJRpgIgUh+grbRG7yND+/YXZhA2DU9tZjQYCLp60hh2hhKVKhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11418, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3wBCRA9TVsSAnZWagAA7FMP/2Zxo7K0yMDGWPvu+Mmi\nfZfTt3AJNa19yxsjQ4AVP/QEZdi4mgyjVIqu3p1XXC2TGo4XjaeB2+QBHEHX\nigMK42ElhLngVvuXANEMcly27B409dqYtOYUSoLfGQM/tsqWrEjM7l/AeeOV\nNJW5TXbKWRb/aoR01qHzI7pFNvBUzy/CkVFYfn27ChqAKJUoHizxK3xJwh/x\nxvxI0LMRcWXpmjFPAfIrT53TFAM1KejIrpmK0SNxkfbEua6QZBta4q+QCaon\nwWZ8pTVvzJaHrGb3lqg+0zz8oEYkigYBL3tnNslrAwoAr0qXwlO4fnrlMUo3\nKrRG1+3RsaaAkKjobsBu2UyrI8sWmKzeD5WZvH0z7D2bkmcrRcE0MSxhilGH\ni/c5UkpNPe+alTz7nwU/Sf2d1xfs3BnUuT/jZ+ay1Qhciz3cXue+V0lK+rNt\nG9MAfWT2W4XcoDrBbZ/QgGzvP7Cli9ubGFkQPMLSDKmc4XxEK48eXNi1gGLZ\nLe7pofUuQ9BhhvLGkTUjZME5rJvE5AE9Og7FS7lDIFiCg2gW1f/GEqRRZ/CL\ntjwR8WM2UTSyFcZwF9O6pcTpGVF3NHbGKHNhYQ8jqKXLfJ43V9jOymJNthoe\nqvRKF/YrdeDc778f6DqL8RQhrL/cCKJ0iAnAw+OEnlIoLtx9KD2OBie5vxRi\n4wfs\r\n=mh2T\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-toggle", "version": "0.0.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5507f646a9301d5dc9c12e5a1b639130a14cfe2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-et7oCxZBsV6xtCZ26jONkWAGBUCDrhB0NT2I/7CnMfjWuI8pV4710sao1yCXgqtZIlJD8tyXsI8dMRfd6K7Wuw==", "signatures": [{"sig": "MEQCIGQbpbONuqgjj8haVN3ABxy6xImx5QNi87YvuXN8DVphAiBWSCRsRhY2xyf87r4SxS+4OB6OvU20fSlIQuovKtH9LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbm0CRA9TVsSAnZWagAAD4oQAI5SKU0qscCqe0kFyxuy\ntEc7X7C15DUfsZyTAQNeNwAwT5CoIoBun56oQ5MoZQoLtx8n9W8kcCPDZQnu\n4o8my4o9+UU8hORNpvGgB00EADY/YJTeIbA8POjqW2Nq3MJofAIjuE/Sedie\nOEivqFHuEtqiFalY07WzlJaaLU1CBu7TfGM9ouyH4i3LlnZopPFXVM9z7nd4\ng315WzeWNk1hYYliMrd3G39FGQAPDNiQ9sP4TW8nfKOX3VNekSudKeCe3rHL\nuSEmuZQKxUm8kqsjlSl83G0vxdgXxY5eW/5609o6I4UBAykXcuaOCeYlr+5H\ncQJtjjHt65/CT85HKG5ec6DIzJuOJkJMAN5hK0ekWiAKb5tAvmXJUA4QbPLJ\nc0ew0kIKXY3ZnLRZ3F+haOzB8xYTbeQawUvW6wxynT5gOUGA4YS35rJmigXT\nPfErfbdZK8JYyD829R+qRVL0ufGYy2BsV1JVQQyvqVg2NQOU6KWs35nc1fM6\nY6efXDl4lqSgXB+28dcw7wo9u7BNwyJo9U6ghTvHzDSviO5/wCEiMrko5MC/\nIygdSXkrVgd5RckpXyUvPs2AxhF1qaRVyY+njLIB6DM4uiF4DLpbShPL6MQ8\neoecKKo27sJbgW43C3fRE4DUuBT8FzBtfH7CQRHdSw2106GRg6CF3RrgZih4\njEZg\r\n=VKL6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-toggle", "version": "0.0.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7385d929c4acfe9e5228854a109f726d60b0249c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-Q9LVRiNzSGClJZs32VDZO9Bq1uAaJzjGn8eBIAd2XAHa9s8GVUGZ4yyeYhTuNksApQCXcSwHy25tnIFn8zZdZw==", "signatures": [{"sig": "MEUCICG+gP5f+291zJ9upk2jOEnA+AdoLRnOnzALhdial/sTAiEAiQHi9gSYHfx0olJF6vcVnQvYK1oMPXTyyZWBjMcH1t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7fCRA9TVsSAnZWagAAH38P/0qQRLAfNt6gADDtfeSS\neA+IiFCebPxbfsJrfounDuCH+3Ry+ZDhbfM7bqiShQo9D4TwrODn9JGXgjWt\nWLqzPBaGlfjHFj8Wtv7SBB8ch3TcWkqUjkLv7GtPkVV7R6yckEwNj6St6f8E\n6crjw8EvX8CQKLvfqa+G6VLngiSX3oqJxm3OGmuCo25z2+g167JKr1KcKlcy\ngKPzpe2tBzpjYyzPuYvWi24NFdELgP0VvzpKB7AepYEIltkMM1KVCBcWVUjt\n4qF2BMRbQHBq5WP5eaWC/u7s4FBhz2z6XQpeWARMllONy/h1eTY3m95K14dF\nldq4rZ2hbzfC5baIfXtcM16zo/iyZR5/LWvLvCYdL408DAfxDp1ItSGCvOIl\nYVkb0W/T7vfgZuzxyqAI9C0g/NhbPUunBroJ0jc47ovNRa7rg+eKoskxL4gN\nKLQUl52i2MyeLSUkCnN9IrLHwNwgME6gi72A0yRat8RfE9ggLyYnuOuHAs88\ntiIb5diVMkWR3Wlnwx/bpIKGQIrz7ADXWtMjrD3dIZifunguHDv/rBd6YAmi\nPtNU4zN7akyIV7DL12GqoybZbOJ7PcGnx2XrafsbghRglMnSNS+vrJ7XPX/w\np445Y8TwJwYI3RRN2YLPz4b95Krv0d7gOUOampUKV15qYJJjOCSEagUeZjbk\no1oe\r\n=KQhr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-toggle", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d22c96bad90d24137364464839a82a9193752d30", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-b4Ew8y9xYTNjs5h81YPCcKZak54n1q0/hqd6vNnIdbCnWzJ0XHaFMkAp39yoEf15MG7ScqnG7DJbyNeDl9AbDg==", "signatures": [{"sig": "MEQCIG3N/4nUrnV9/ZC8uPfmQ0KtQOhGJXYB5J4SQRgTac/XAiA1i1ARrcTO05DX8+VuI9BIF6rvY2vWIBtHYV2XAYC6ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYKCRA9TVsSAnZWagAALWEP/1ic2XJtKTFxC3gBbAt5\n6XtYbp30QllQ2nuZwqCmCyJgEvuQyv5i00SDaV7JJc4TVxOeijdpdHtBFDEZ\n8gokudA9r6WJPjnHJmFMw323OT+cSEtvYW6v45zE+GB09/Z3d/oPxB9eK5C3\nVay6YbffmsI83alN28G9gTi9IU9BjVi0eksLuGK7yC1bfszteYaOxJhQtM69\n9VycbLHAFfQ/9dwgIZFxhLnOvBNkRalhaR+bhWZ1G1v4OF+1dEj9fNCgTQA8\nT5ozTSTlXnLR/n5fRvtuaeLT/4Nzk6oYGL9eAc5wYi6nmbIq/qdwnVkWBzd+\ncm1Y27gexZV3cxpxCjORkNxF3QP7C6yFXYz0zlrjlSDESPNYxKrz/5Kg0z+8\nhi7iLj/GAYIGmp+FS4iBV41h8SjLiOpphxlpIMtoHGDIead6QTusjwlL3Ipu\nsBe79Puv0/QOtnwHKl7E6mPr/N56GwKjnB4FFWXVDVDHmMfnUP/H8LnCYsCJ\ncmgKBCC/1hx66fg87y6lROXndZNhQozoOBAzqyI0Ca4hs2SD8PeUYd5I8n4q\niWhZ+8SGS0OKUGsjDmafWog2Oym8gRF+l11l3Ua3qTnugZgpsSPqsjQpSDd6\np63dVFVAUs9SEN6hPML6oM+QLQAlek0VN5WXszRzeHztoffAI1w/HE40g2it\nDFx4\r\n=VdHN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-toggle", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c22ae0ac66ad70bc42571f78ac2519be13e5387f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-Z86L95oZxQM48ad4RwF6vHHAbY4xxIT41z/UHHIRSBi/9DGAvCtBvAkRn+NzN0ePlYPLLaduTm25fPBIdwPZBA==", "signatures": [{"sig": "MEYCIQD2UucdHN+yRRRhY/VQGyirGGdcdthIp6W4nC2aUYcFugIhAKxBT4w6kS5KMfc017I1bMyX7mcN4gQ3gjidTX5gtvgK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ94CRA9TVsSAnZWagAABzcP/ApzjX2GFPZ4FVyWyWwb\nvl6BhpDJ3v7x4uHuC9scQE8xloQVolVjhecaKiA76A0b1ZWH102dHXXiZzMI\nuboMLygNLQhrJ1J1EwKPRdKw4Vr7H6GzO5kfTKVS3d2Ci/6upwLD9ZsORPpC\njQGpfORgsi9pwElLXAIWZFLJ3J2Ivy5zBR/mcCZ3pedCCIWKhHXkwLZPi49S\nMRDHpL+Fb8FvGfWwV4RDpSifF5heOS2LNix8Jso56CbckI6ZD5l1wFLF1nz/\noITpyz9Ma1z3HH0qxX0CsmXOwRo81gBZwdSAeiGml7jylPeqaItsQWrVIrG7\ngnv/fZMLZQ86AnMQbqO7xR+pyY2iJD6LFjS3cESAqTTfsDQnC74IWxmvhSb/\n7i92D93lLCycnWUXZkiYyTqbVYlX3IVUkekcf5bn5cdO1rj7nt5sEx/aINvX\ncL3okboD/on3PsmbfyNF85ZT0wdc1PSsF5xy4ciQyz2+tkbBA9kOlK3F1Cyn\n3eL9LK3GkcyZhQ2+afOZ9dGwSaY6R6zuaLlptrNVyC97gn5DigLYXuwiNN+C\ncWQGxxSWjT3dfU01Q2nL4ekVD63Q4cgyJ9bBhOtwXQ2PP6QizbpNkYrktoBs\nPoCbg1fJW7OmEqPMMUBmh5vZz/kzPBPEQt2M1eputE06QxTwQcRymO5l1NWZ\nszAi\r\n=RcCJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-toggle", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "60133110fad940586c763cf243ede3c6ce283a0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-fr62h2p6tqayDBg3hkrdvVJFQQmzBNHolyOqF+MifeTAryBJ3AsQu9ZvuYxJzkIaPPAIGOohRdR48FSQGJWndA==", "signatures": [{"sig": "MEUCIFKnqL7iV3c0ceUMcUay8mop1D2zrfx+0+VEG3+G9Nk1AiEA3TiHakWLMU0swiL2/HyS9w5/CITrdi3kg217Kjs6u/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTzCRA9TVsSAnZWagAAacIP/1XA1NIc/G+OyPnqKTn6\ntMnrccsQX+RzYb6CzqS0TNToUTlzjAp4uPwWDnbThvk6vzYBTfmrfbENNkWd\ndcYrf6G/Mfa5Wgq0eihxtFMpscBGDSG7JSHoNUvjPAO51R/NMrIIS76l8XQN\ntohx3RYKYUhqQvwrBqakgsLGcVN+YEpqQwQO1hASofIjtH2t4ONJmFSEbcA5\nez/ifP9rrQLPPN8FKhBgf/h7Q8r4GSUGt3oQEXfqActZnHt0yGXVZhZZ1i4V\nfHboCd4x3jXWKCkFMVIG0NASu79HWSkVNi+cGOOdDN+K0Alm7vkRbXBoRMZA\nXD1RPIkyYBnYOoHJoLu4Qr2iv3xqP5GZp8idr6IrUcOXxbTeS36QkyhYhiW9\neDgJW7icxOuI+ft10FPLvncwRIE2oHy/zZE/Jrhmx7IiF2DkYWPlmUjWZ6iu\nzD69BxgFO6CcTpAH7TAqh3Lo2xD9kP9Zx9GQJ74iMe4OMc3b0f6mKp/VPDMC\n4Yc2qx1cyVDjdtLKSRIkBkppWnCgX8t9wPl1zs6WbN+y2nSXDD1fBf1QhQxa\nZaWY2mNhRMPSA8Gdb/CgWaLXOuflJr1NR5QWqzT/xOCV0pCAt5o6DGM4010Z\nIXO35o0NQ+EHvdMZNlSGQn+LprP6VqJ1NNyeAHYBLswy+PZhYjxYmTmC63nv\nAFPG\r\n=x5Cm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ee34200640119a70bcbfb3dd69373261a2308c00", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-cPvEALeq2Me+aPk26JGSU5A+2rpviY8DKGzO6x92keHMHQtvq5SFLENnRGxylltE3BusTNP62WLRqSzCxDhvCA==", "signatures": [{"sig": "MEUCICeXADpJkji+0OuvWZW7HAEKk0/8AakDNrTnIDGadGwTAiEAzDbY2iHh1BZlHz5GdZ3Uj40kXLadyUYyezRH64E/+hc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgptCRA9TVsSAnZWagAAfHUP/3uAfYErR6WmqrWUsNHg\nNCfnvZtIcLV/M8BM9CE26h6aUT/JOvQ+FazdmYcopTZp+IpjD+wXM7slJuad\nyhN6ioTZBVSG+1HlSYOk5L3eAweIhZbOAvOlR8KZutS3BoJgsdxsJigHvca9\nS8LxRDsrimssbiZ6zs2tsg6oVIBXT18CBf/R4kFAfjJkG0O5fSsAXk0cC3pw\nzy3Q1AwIEO0pUB5KlkT9dIUjgXuhMKKZnmek0rmZgqH0jBP18m73DDWdO69F\nYJ+5Vh3HjYg/iwA+s8miuCxKxqE5l7pv1wN0On6YzBiwWtQbZx5SsPFvlM0v\nBAAhGHIkg/wNbCVARFKx/asRuhTvhWjc1aBT1F3cOTebe5sXa6zrWpYRe7gU\nk2g0ZDorU1UlTFj+1oLUvyE1AtJ71edUjvQyIeOMc3QPjWTifniSUKtH4B67\nJ875yckmXin50jEtRKQ/nwrVWw6cI4rYZ0S9M61c/Im4Q8BQK3kE+jz+NhF6\n8FluZBit9CwLq15VEMiFk6LT0qG8iWbECTMgjz/9llvaBR9E/mqHByX+1Q/8\nMJUJEdeIqdwNsKntgbKl0vxwAJZIP71wNWNyVtQgZ2Vw0I0/1h9ivCk0M6O7\ncAouQ+oW7TgJuby9hylTLjEGkIpQNeXUAITnQ+u1JnNu5LJrf0p8LhDJXd6R\n5wec\r\n=GRqP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-toggle", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "791b8376c0370293229d7d230363849cc1ecb1ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-qgxIJuGHNLXoqEF+HiXpeZ3/EzQ5av/Atuh/KhHrfozRXe9rzWvbxN3Pr3ua7yiYgBGjyOiIRs3X+AO+Hsh2bQ==", "signatures": [{"sig": "MEQCIFzxqnQM4kbvw3kIjHFR+ZnzeBCnMbIZ9hbqfbl4TX/9AiB1trOCvv2tQi4FAfsDNU57qmYcVdfH5lE5EYIIJPYbiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11612, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyeCRA9TVsSAnZWagAALQIP/RCU1tPzNR6L743QqeCc\nBt5GpwllcqauUIwrxgPFfL3cij8MIYHY2i4XYQ7h2jylNwGqwzZZMyAFhSFO\n6XbM+OHoZj6AJxiEZGjDM23t9BHbSC9oWwumIm2swmNfXyrcsI94yRYNoHke\naFbQ+pIRQItiuw713Hj7DQuZUx6mLVYQS9OR5hWmIc/KDHi78SPq/N3Bsch8\nyJiQyNmf0tk6pYdAqHSL4ZFlEg+l6BZOqgIuVXpUfaGaaCu43hVtmVpdR73D\nQgipoMN3FRlAUQlUTR7zrF1zgnDHrd66YNz9RmdrgM257ssehSR+rNJ/Hacu\nvu0SMy9AwgEWawsuX1178iwcoT8U83DLcGV37zYC3Qp4DP8fo10L6nt6/CLA\n2kFB4keYjmFJbF3HzwqmaukbvUCSEYWsG0xs5jR6WN3JBKK01VdKqwxiK0uD\nYTZr1+UV20bTNH2BM8I8qBjqIh/ezW7M8ec/UkHLtncJw4g8qDAh+GxEMNu8\n0DnXbIbqyd/tqEIT/ROPieTmhRX/ur9pdAeww0EksglhLVQPMCek6HWM/Sj7\nga6M2IFGbMOTxYlcO3dXSj+I+/p09t6T/+hBwlszJt4+dbUf+FS3R91DV+7y\nb95Uzxe7c+K8BIS/XYFZC8RRFkdlhUiso3/nO0RGB2iZDECajvAEL9Pmh5LA\nHKFg\r\n=Wz58\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-toggle", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "31a5226681fd89fdb1efaf1dead0fec390127b45", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-pk5v93yi8mlWKxAev8WKrL6hvZs5uaaFtUxuITakqtLwnGFCW19GkKryRhp/gRJaFtNwrk0OaR/UmGr2bzfxhQ==", "signatures": [{"sig": "MEYCIQCEPyalNMZOE2N81qGSTroAU6nAQO5aaB+YT6db5XDQwgIhAMd6UBnQyS5Wf8pdduKGsi/9RTI7ZE3nXCZ3e1iWpL/f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmrCRA9TVsSAnZWagAAybIP/i4qPe/kHICRjzP21P8K\nx0TvtpI8iBw4SascX9RiIw4heGZGB5mDemoWch4cZ4CXLmH7OzXHqxbduIXF\npH1lOEDiqI9aX7XUJ7KY4+KzuAdh7CEVepcwduRyoTDg0XzmwG2FV5iBLNc5\nxldPH623ASGoBN1kBkGPtqJWEGaxKDtvdfJCRFikB3GfZ3Mx5WP0MGmQ3ZJY\nHN7Hlf4xpOoudvRq6xhaH0sFTFqxd3FDKSqE4U2KWoiajv1ug6BGWiFsquUp\nkqHTCjQrWFDFuAbKnDmRNwcqs1IN0rZ/6NYi+ycJ1riWYI8X6gLlDo66cKs1\n5fFydAS9TZ6AWvghE6b1XIaIda3ctlDildHD9Thx+OfgBrP36h4wRq+msgC2\n209IxrLvXRjKHseH0v2CrodAueHl/mFAUlnI5rBN03UU2+3ZGDyEP6BwCLmg\nQ/P5LgAVNFDQicfRmHFJE0r4EuUSblBQuH9AUfjV9Y80u1Y7z3C8gsHopvOD\nhyDvQ+GrI9NS+7QCB+bvOztSjINNcWymQA0jRT6fmN59KDdZsiAs/1xJsif8\n7n32VKxVG3CPJR4QNSckMGcqaWxB1/Ea8JU13MYuq5FZI2C/v+JUat0gGxtj\nfxQX2EtV+cJUn9SwGS5TlsVR9KhdrGqK/94AaxKfLItgWQnzT2kvNGhiADgR\n5E2f\r\n=OaN3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1cb95fc4b887c1cc4c380818a71f97c472cf219c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-tMPMOTMngzknqEhPYFfy64aUwjisG9Q+BdlcO3Y2P19OEwtjxZpUdxE0LeCkuxC7VuHXfzhoMlfEAp8ifU/0Vw==", "signatures": [{"sig": "MEUCICiU1Bt1d7E/TRX/0a8q7eVPkbozBYGHHpUNHlu/MR2OAiEAhQr4NiLOZU31Bmbbcg6ONEbXDmWIQ2p4MN59HP3qbD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIn+CRA9TVsSAnZWagAAq6IQAKJgV9aEvWL5B+iYLNw5\nw1Ztegp2oOZLQ1GhtSy08ithlV9tXBXaG4I8EO7dh/Za+5tgYpRbD2qEBKl0\nWTLvEdYoz8bc0GZ3J1qrmwWt4NLXmc1I6WafLbrRtPkvF8RzilocYeROnjOO\nOHaoplJoRduBo7C+Xa4JJ1Vcsh0FQWkDRRwYaU1dVM/s7zWaCnJUjwrETqoF\nZi6IrgyZCEHEt6PmCVbV5feL8IhOQ+mQMBtjf6vjXAbdgiDC0qRbtWhNq7H8\nivaG7d+4D8GiKR0VPl1BZPdzzmz8freKm8le9WKYrrHo52ej6AepaMU4xWfL\nqjbvIkww7m5WwNPq2fN2OAtNGQLoiu2dxwXid2CU0vMxQn1IseRy7QVLUW4Y\nOgJWirUXs1jUdXKQiBd+aiN2mgyYwFecDTRopvDyZyU+TLqUcBLzR/XUKeN0\n2xGMfHnNeUu0IYyHOpyYwPDnjGDN/t9NzZ3I61EQgjIW/RcYdWnrvJkWXswb\nw6NhBpqTjVuUAx1YovQ/YVshjG/jzuOz/d20JRHTcDLSB5FqZOE1wYNt3vl6\nZbPvl3snk82aTRf9nQV3/iCk9+GqOrg5pkzPzDTFNuqfASSaP8tQoj3l1Inj\n0PIlTgUPXuyM5ManuZbHFmTELbYZHbQTsZ1OKD4Ey1NFarD2Nxm7BV16D3AM\nnvX5\r\n=T2ip\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "82ee6f189a0d7fd79dc1402ac22afbc88c5403d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-YhY6qYqfxGsYUwWZ5KNRMg+9MURaXBQUaCYnPE2GRYoXyKkwdXM1J1aVaRR5rc5cKI+gL0zD2Y/CvrHjq8kceg==", "signatures": [{"sig": "MEUCIQCouoCx7pLpa1bfsibM+WVnNBwvuFUjKoiiLyHPxkFbSAIgMhkSsvZGIUAzr6x6WG3BvaLA4OSYi3hlLjf9PeQQT8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwrCRA9TVsSAnZWagAAxEkP+QHOxs1qKC7Dt6MDIfIw\nDo/ni561tlQC3YOVdhDvNSsuOb2LC6s677GXrHfjpd2JuZZ5Jh5WzSJL2cMl\nF/2334BpTpb0GC+2Sr4MArVb2A3l92zGplDvqf560AJkpDj0UFeKuEqc2nGh\n6mN+fbfUfN90PjlYvQQ3c0sFzBR7WJWHspuOP99103HlXUbDb0nKLKLwv0ru\nH7TbiNTGI1vxqikf7nWsHrtUGmCGLuwMYZLZ1ndszKezso2SVgwk7K5SC9jX\nUuQOOIbnhOvCnFplyA4aDf6RsZpl1eoJ59ieY2022bPcyQ+Kk5GPZi5MiTDh\nbJ1lBkY4yBYsGuZ3/jTcnFY2+/E5c7VNGSRWdV6FXL1IM0KrU37Achh6YYUf\noGHk/p2rT4PKKyADhC+C03fiJQqku7WWEGiBLNmqugGCQYGvF5MZdAF7hi2A\nyrG6EDbSSGXi42xcDE6ikRsw7r0JVwluklUNq08gGhorZD9SXNS680oaA601\n5cF05lkm60Ci0ECBvKGEavSJXw5Ihw832DE3o56jBm8jZR7OmBBltGLPPO6z\n0w5FnQrRiTIA6q0Osk/0LYqTIX+4ljgCOzgc8TdpZb7eKBW2UlF/3x8UzdfH\nFxdybfEtSgF/gAFgo7whJET7K85JEPv0w2ghYYEvu53NSypG/hS1SUG+JNA7\n58AX\r\n=F+kz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1f938019ed3c562df5ad5705aa66a6ccdd2280f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Lj3wYEqy9KkRDyeVgIJyonWPGp4lqkPjQ+WVzuoq2snoqgeA0Q8FkVae+nx4qdfjqFK3eg+NAyKlH+GfsCes3Q==", "signatures": [{"sig": "MEUCIQDlveAttcY0Q/Gaf752Y4tP7IkC6Thy7x6vGCwQObybbAIgL4QdzGcmklHT2vIxwxSGChcmSjB6CtmrSA3NOoJKktw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UrCRA9TVsSAnZWagAA4m4QAISPUYaXShlRZNL5AW3n\nj7KEcUDV5yCV09obob7I8GHlzeLk9nqvaQ8bJ2Yv5ojjp2PPSy+5yEHcWfw+\nKFMtwkFHSHqupmaxRqIwjuKyc+YuDzpiMzt4Q16/JZKJSpVoTAtCVjzyrat7\nau5UYrBsNF9vDvQdWeSCQO8sJeWQcJjuD1uy27Fj3Pg0FYChAl0v0k30BwaS\n6+IuSJZ7/XrkDa2IktjbDh/sS0FPgMXZ4i3iOJXV0HDfk1wiYstXw7XP7bSH\nrbfWEoolP/5Xnt1kvVjz74XVn8b99ApcZKgDpRjzZGKfGIDa/oF/vAzJEgT+\nGRPlU1P0BVpdP4Nvnh/3EqCfFsOw7HCH2TSAA7OyXrRJ6TrsqVvHFSFq48Sx\nH5C3ScIgAtQPxcSclQWqtqDntccgsEByr7fZYcOGK89PPWT/5LR9vqMQreMb\nVRreOZs42NxlAHxS25t3BE4OmAASSzfX1K0sFkhJQcST4uERDxf6/IjV3E0+\n0S3npBYo/CH6cONGMNtkzgOJuo4zlOWORP2NYPnR6K+MCJBGL3XxQVgpGonM\n/LcrxlEvlqlLTJrGTMmEqPWMnwSyA+Bt45jQpfDs9LIBgnsxPz2NrCb2tjit\nNfYp7Vf3MOpCEOJ3E3Wh6xdqQoL5tYvCm9Uo2QtpVDuVtK9ZR5nJckL/jHDT\nxKZm\r\n=2nfn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c23ab27ac5a7ebb936b18ee64fc654c22306be73", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-0kmexCijbO6Usj/2Oxc3T/zpadZ2Jq7Z3ib3+bFBT8OKqDr4VB9ISFHoA0VfxFLTZJeuF/92hvlPwbARclLvHQ==", "signatures": [{"sig": "MEQCIA9ZtIdlUHMKFZ4Sn7K0+ze/B1kTaPFOeTnb0qKJfORiAiAGTraCw8d12O5R/CUsdTbLBJ6hKHDw4jVX6lHmMTdsJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10kCRA9TVsSAnZWagAAoqkP/RIau0GaiF+IYD24Tgb7\nznNlleiwu1eIr3O5dsvCHGzcgustCswrygC1KqZNUYC95YYaTq0VliDnun0s\nmGCfiSKDcLb0CEJCN5u4gnMZb5NkqLKxsK9DlU3sd7IK5zcq6550CLydPqMZ\noP7nuByzMxn8pLTUcAE2ZfeuSdpiVxVYsqdKiZ7pcPHeNhKMHIkYc5rm6nfh\nvbJc8FSqC3Z4rWr/waAfyR/t3G7pgMMv3fKBrEUEW0iL8lWK9WLuOpLWsnZi\nx3gkmMTE4NEgzGEL9dmjVF5atVFw6otK426KBDB8i/xMFzR1ZGvpo6h2JYwL\nss5xzEuLsUC3fbiyeb1n892cg3t+94mJVAWZbLFM+A2B7MbqYDS6O4P1uHRv\nHAoHGaWScs5x3tCcIfUcRU7s1iS6doZt0uSr1bAoJzKySJxVHxXIKzbhB3Qc\nP7fHfk58JJ2QdAbpSx+9RN3GL7BJKY4HWGVhauQ3m2L+FloJCSYF19Y9fNuB\nVA/43tIYypEpBwMyQFkmei9GzegUd2xAZhSv0NSSdnU6c93p8oDZbQ4r7DcM\napBFMhgfHI88JdOoH8vkO2qF3MX5u3kjez9PLo8F6XAWqKQnXrphfDDlDAEr\n32gcyTtlRepN+T2vpY71SP8LdzaKkrGzGa6LykizYbH1fbxXN9Ic3Lzd6tDG\n8nAD\r\n=1xQ/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b4c8ef14a051cb15be50e39c89f07bd5151b3944", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ZD9+YvjpwNFHjEihSoKR2N7m21b9CzQ1op3W0w+3hB8cFm0RLw4E1IAP+1LRQpyaUFlI172AfXxNQhfivDASdQ==", "signatures": [{"sig": "MEQCIEzr4stphtDOKyjlAVvSas7YdkoQc47KjTbznfzkwZ58AiA+UBbOV7iY5q1X6k5oMh4baPPfU1nsTNUgtEk3AaQb4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFk7CRA9TVsSAnZWagAAa4UP/isTThagJ8Gdou3sWJsB\nwPYEv2G6ZQJmuDtjPD5O3jusdFVYc6fwNCHB8PLc1I5Zeo013+qh6HmO0LER\n9ayRhd/6fmv8qYAhpBy051vhaBG1vtF5VD976d5a+C4zS+ittv15BGwsx7W+\njEB6L1qDo45XqRLbWXXzXdph0mghELuWdlq0p3Xyv4nKGige2tRjrM6SA2vo\nJTqeX/FzmdBDGygs6qlpAjUR5a8leRb9XJmo2FQxE6E2ZapZZk6MyuqieB9p\nWH07Uh5vv5L2xGGiXf4E4v8kzwWdepBrxp6twXCYm6HkPBiD+skzHrSaB8D3\nNG+6hrXY6+xiEsQHsqAOnd3V0D+bHjPpWgX8MCFfMlj5mrQiRqcax6OV79WM\naWGeDjpqZV4D4LfJiMuyA4fQXKa7ItJTdLnZM+P0GGt5GsBabYkcHSj75fQ0\n2nVz1++ueg6bs6cwHMUbu+ACuJRlzXJ7HKI8BmXmurhJsaOI6n5Qu1GmFD8b\njSlORMSFEbNQHCtVnfjd3d5GMKiWVwFgDoX7eoVWn1Vc++o0hSSzm5XAy1T9\n9Gnla8fvFW0BRciYu2sASsMCa7dQQUXQLZRlsbDbtgobB53O9aiF87+TiAzB\nUvDdSnxnh93eLQ0fORal5SrltRm0NsAEe7GjE7SgNHuxheiJWnZx7DZHpH9o\n0oz4\r\n=AFu5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9206285b8f1608a565bffaee56f08f7ec0a8a1f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-JHgb3jLpBbQaHFrJCJuTiO/WXbkI5G1y4GjfQlKiHKP1/0vZWCwpI23sfKVZxCjoy8jST5nnN6ECG9IldwaZYA==", "signatures": [{"sig": "MEYCIQCkZvp3Pkpye8WNa52oK/DhN+6rSZTdVdi3W19dXf/vagIhAO66VdVP7SQ7cSpuIaSfAmgcEjc/8r++VXwvfUXWjWNf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601}}, "0.1.1-rc.7": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "872d825319d1fa771a2832c31c7c5bc3b68f101a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-j4nn6JJreVKwTKjehcePCI+S6WMmWBB6/yOgec9AMZj/0YUmSSLZktsu1HGWnSAe/f+WAxYbXHmjN5mezFDzLQ==", "signatures": [{"sig": "MEUCIBcO3fLGaidTFSipJvDq9adiCPlvLBVN+zypXzrxRATUAiEAkb//q2lhtaNTGl8AmV/TLgg9VlusqphuOfdYkHqzSiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601}}, "0.1.1-rc.8": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "da853783d1d1a541afad38f9e1ee9bac6dda761f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-/Vrfu1H7ERB0DSoH7ylOTTF7YEY8/GgU0WR9qTZiNwRkhSwBowzyh5EtIOJ+fIb2RYqI1AuA8nxvzsKNIhGO8A==", "signatures": [{"sig": "MEYCIQDe6KO8ql3jt7EpBkTxkipgZMzLiaagEGZTyTs0JdRTjwIhANMSbuYwSwGtngEUmFMPpmZ0ZiOEyOxHsa5EQiTGQ/QE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601}}, "0.1.1-rc.9": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "314cbc3fe6f6ac1b39d02626e4dd5b4946843f02", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-FMGIOamBNnbQCMhMSbssylXgq5wsTMvQQI0GnGpdPfaq/Q5iodf5p1ic7h53rmyRry7BRvZ64c7idxaHiG+LFA==", "signatures": [{"sig": "MEQCIH8DO123r7D87tmPd89hBOk0Aunit+X+dbmoEbNgvxatAiBiFb67aRchCnG8whV+4CDGdnm/WoXhwTm66a0l0DIWXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601}}, "0.1.1-rc.10": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "87b431c8e5ad6d1a49e199de7395470d9fa253a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-/YNLqtFViTR1ochGbAODwOBLYUT2tUNKSGFmoiL77zYSbAzGHmWx7HjebWSsslY82Qxq3+L8Ur3GbQT0wctSTA==", "signatures": [{"sig": "MEUCIQDBMnqDS+LCeahcsmyuzlrhz3ZZeKLEVetYbLKuzo3IcwIgfs8kHlmO1cESHEbxcimm6CQUAp/2O+uEo8IKqq+lg98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.11": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b76f911a9006ca2b22376c157d877740b1509581", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-OLqbMazUaEXc/ETm8ipcdzuxmz4zXu/dtkndN6qDnRoEsg8orX2SCAw+BiIUbgyyFf0Oy5ISksDmD7//G8du2A==", "signatures": [{"sig": "MEQCIBPRc9MsWugB66r2fqNPJL4fnT2jPN3OeEjkt33mtpczAiAArnnBaO9rlFxgDBjNVk8PQ2BpJBPvrDzBGdPDuecERg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.12": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f83335c52eff7ed00cb09045135248b4f2059c78", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-CvSlj+nY7XgClvqHc8FA8OqWs3N112SpaHmYiFS9oVXD13k7Z0tEqWjPXycAQmxKws06xur4OeKgalwO/RhSlg==", "signatures": [{"sig": "MEUCIQCgVkGzA1f5eWpkNYdjBpV+AD2/sYub7GqOPMUuqc97jQIgWZj6wn/nwvpisJW1pzEoIY30BVN/nXmjkw6R0F+onO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.13": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a9c139927961a83d40580c13c3021b03bda7540b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-i+ejv/lQFThDl34CHYRohLj7TAC/CSmEqrWEeaC8rGhqw3OKH3ErV+aqG/339tHxWgGjZeoyomTCda+AIEh45w==", "signatures": [{"sig": "MEUCIQCYW5GM/Oe5EbyB5WoO2GX5mAHeSwerVse1cMRS0hRHxQIgVIGOl4OtvDR+LoxfpvF8Fa5EHv4lwiiWzXMeOFzm3FA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.14": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7ddeeacda75cb81978185e942918bf19da545bb7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ou0z8xk+qT3IoS1D0+8Yr+d79Xs2gFfiV7xZArB+PpjtZBuGgBQLsKd8vrV61j/snsfCwnAXNFW+Rg7up5UUKw==", "signatures": [{"sig": "MEYCIQDfgCzP3LBVNxPSq8ZTrGzD2EExXmFMt8o04MbuYOjLmgIhAMAFXxGAkEd8b2V++WcoSV0gL3GW3vy4oJws+jILJzrr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.15": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "beca303d93f26ee8bf4c346b9dd36d0fec6f91d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-+L2VBaR+2fqGTZ5mGZPRTWz5/rQMI7w/9N/ekmip878GwxAsSmASiXD6VaiYMzmbIstgP5AfEIJperSADUtLBw==", "signatures": [{"sig": "MEQCIEyOnl9u4GS85+VW/FUClsFSjjBGPFY5eW2oiZRVelXjAiBvgQgtrMeC5qa1Ho7slyEahrfhKxCFxwNs/SQi5+P9rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.16": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c5e11d95e42ebc1510ea09dfaf3abdd8c6b31b44", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-s+yXgt0cc5Gv/pfladACYRDiL+BPrWzN5lu04IDZQKmTntFJARz65SrU3FnbCPGOFRqUoqyPNYo/pckGy4/VZA==", "signatures": [{"sig": "MEUCIEy+WVbakDWPOdyRm7rYbciyZTLZyp13KSQlOf2ZcyEKAiEAisO4MbLZGRQaGTg6jWB9WPMayxKCxYW46mFMk/Kc21w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.17": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3a1865e18de746861d37de212cabcef9eff927d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-ED1/Dr/Zc42RaEUaF3v3aw7tKjzjdDOJVz81C6/Q6MyzwXA7h5Q6+h1FJu5pqjt+UFPr641cjSHelunw9wIKkw==", "signatures": [{"sig": "MEQCIBcJASAuaA82iwVoMmyR403ii8+9eSSD3Srme/PZZy/BAiBcHooS8khg9MSf1NKPjSCsYKm5K3Do8+tBZ1Mafm7gpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.18": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d69f98a48cdffdb2b9088a5769e65faf5fc2ef6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-4mZyjrus4J/VAr4sUTAaSlHieZAPnG+LQWpHC4FtFkVFtVPKB3/qwVviTcCfPIC74ZMYtVf8BNkoKPvdyq3ZpQ==", "signatures": [{"sig": "MEUCIQCOMT6qOLG18Ut2KRVD6xJReyqaI5+Gs+qAUbOfx1keZQIgYQ9WIBHZD2DFq7zoh/1wJcd0Vbg2NxY5xDKGkJ1ZPWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1-rc.19": {"name": "@radix-ui/react-toggle", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7cca9574fd1df233a804fe69e1ad0dffc2a8d7b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-o/HSYf8bVctvFNCGZThC8q/2DotdxS2aSc9nLgpt3OffzQrggJub8xhfXYhZe8CN58ZA1Gmh3RngNH+G8MyG8w==", "signatures": [{"sig": "MEQCIGDFD3vCmGS6J6ZYvvUJfafh0Ez4S7gUkUpOM2Nkyoo1AiAN123eIhrKVIAQ3Mm0f1qYn0LDExlGoeryuwreM7miRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603}}, "0.1.1": {"name": "@radix-ui/react-toggle", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0ee89569e8e517593d47117a1d9c90ce05ed37ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-d19eskU1Q1NIs/OwFbzdqPhRtuvdifcF1flKS28lyrhG0m5ToIeq1AqvBib1XGcxnpCxa7X4FpFGIBsiGBtwtg==", "signatures": [{"sig": "MEYCIQD5n4bMXvWFLO2S9xK7T6XUPJ729hvU2CbLS5m5G+ayCwIhAOcgvTrhRy/XW+WnpX120ml0KZY3WPUr8sZJrKqgv5am", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11563}}, "0.1.2-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d0039a999c2cf406e816d62d57eba757daaf33e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-dpu/U+6iCMVr/fXaHfAqePC2IdBdFbbK5CVyI99sXoQ1d9cSRH9nZUU3Xq+749a9WBL7SirC5xKKm8TYRyKg4A==", "signatures": [{"sig": "MEQCIGLvcIgsLhuZyxNFePPL3SbS82nGlu9ye4cw2xmmrksfAiBWZvjFcsWeVpRPHSojqDo5evH91fzeORd8reQeGMJK6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiBMCRA9TVsSAnZWagAAf8cP/ilA66Of76dQREaUZ+ja\nZpO5rZ1AfsjPN8hfugNwfJD/cMbJqcWw5Qwag2ET2c8djrrvuwuYaO5BwNPq\nb5zhLpbQjOEu3mgGJjkZwL7l6dcedtWLKNPw2K0wF5b9rGqDCdlXGpWPABk1\nVzZl7jQ/5LKvSmnH+oG441IxxNZ9pmKCe+hON/bdaa7+iM3LXPoBlVJTD54D\nMMMoI1R1pNJoZcSsVRDP7RVtoyC4zN+z2nNFjisUunXS2KqcgUzkT/EOl6A1\nv8Em1HdEF0e4atJSaamK75/mxKQjEsd1Po+HlRvRHxQkmeYwOcBwHFXJNfms\n+QN3qe4pHCqbYCLDTe0KRfMxFnTbKdt1NiBLFy1GRjMEvjq/BP09OxMFzqw9\nX8SzCnM/UvN6Bgp/4c3qOEIm5D4U7zQm+aoQeWiS7LXnNrE20o5bjQvQ5buS\nWQXUf68+uTkWDbl/ur1JDsz4RGyV+5msduvSZLO4V5dI/bcltkgtP4vRJydQ\n/IffAGod4Jbae9skSJq+zrTzBWs/IsxQZ1DGkKzueL3aPHKN/8JWxe2RFaTi\nBhlQaEuRkxS7rL0nidRqTDgG9BG26QW5DEvbVp1e2ARSt+2s3CMgjin1eW1C\ndqEY8JAFndV2L9SDNhQ/E5JENPWJz1A32adUCbU9jMVGVzvpcAYKs7SK6dYp\nI6oX\r\n=hPqN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-toggle", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2c3b5ded21b6164e51843b554b22bdcbe1a7cc77", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-q7W5g/DxCzWcTz7VO52SKNiXgH3aJ2g8ujl+Ev9lLRFBvcKi33Rp2Ume2jZNFrvWb1ZxcruQu/NZ6Um/u0n6lA==", "signatures": [{"sig": "MEQCIEaapUfPioUXZuMqxWB4dZJyuso+3LRkL8BkD51q7xrPAiBVNkqH/qfHp7k4El+qRSr7Ki4KU4VJIEd9T1+yS4bNfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiO9CRA9TVsSAnZWagAAYM4P/28synygxXqJoIs6Wguj\njf0Tr073Qo3oj7cXg++FXtDq0VAVVAkHSS8JRiPt4cHON71qkzV51JTVWaoC\ntmh7J1/byAjIfjlqY3rVfPOlqgSFws1ERA2+IdIsa2JqcWW2wRwdSwi3Gh3n\nKg8fzhgptMQ1ObguiZF5jDs1v/UPV0p9OtyLDpi+PcLUeiFZMtgWCUdqhWgJ\npw9AQFMAMrxlCpwbNx389YOOP0nk/fOyEIBx2CqV0EXQO1s2hY54s8MUok79\nXqgrlaUC5k6RaAVuj3CSrHtxmnukxqrBGfn9ZW62mSZFs6DX6jrCmYgb3JTV\nJVdcm5FEMF71OX25DGBfrjzTLpMG/EoxL+QclxgEeBGT1v+nVfbEJ3NhgtW2\nZgtmOxyMgpTnkyo7s+7Sdx9DWJLCCUFoFdiXH3HXxhmSLOxKJSQIpgvZD5PV\nhK+QU6MmH2QlkuNicXf0OxsHbOCyyWMhtcr0McJ5No9t4yQveVHJFD/L532V\nza7YJ3uQwsaaOKqooOWRo8VcZNrspAA/7tlTRMB3ooYPVMLBtuQYAeVeyLZ/\nv2MtqCTfHYqd75A/tpzgfThGuItpe/gTzvI65r+4xuX2FELD8Hvdu+jKDj/Z\necO0gt0BW19iTJLk+HV0MpkFbPv7N+79owjxG/T6znRXQBKvncDR7vIFFtat\nm/pH\r\n=StJ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-toggle", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "12c9aec261a6a3c3ea2e0889c7b8469baed20fd9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-eT2x8Qwa2FygxLMNMlteZ5bSMOtIvHP/UXS9Ra+Cd2/5mOAyCahtoZ0rjagbxtwbR1dzhI1/2u27ye8Pd0OIgw==", "signatures": [{"sig": "MEQCIFnCwqpAOhRNT0/FDtUVYQalPbZvSkrgur8kL4M1nKu0AiBofUjFeHlMPJ7mVgoX0Ufso6knXpXa2vhhX5/QBHroZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryj8CRA9TVsSAnZWagAAvDkP/01RTpG5oYvxVPmfvulc\ns+yeJRi7f6VmOomUzOl5qVmq+qQJ2Xnea0Y5rWb0dJAkOarf2vaUBOKDhbEu\nAVBah7Qtii0KEWmIvvuyigvmmC7Ix/NSyPJ67830DvWEld9hvUGlMwuIB+VV\nwIBlFhKZx8Ee62bQAKN9d3Vfu9fdZEYc2AE60ELk0mRcmgWmKjOkPRQRu38f\nyRtn3GQCjcEzKFaqsWAS3vhe9Iq8AieXQN43G9afXQ+vLdCgESsAjCbC1hGV\nKlZStSB42xe7fNA996YgSSlzSv575Ncp/+pMSXv34PZ+HgWndyzOw4Dp9n7z\nQIYpCCXzD0T36kbAWoqx6ZVx4Ocez2pVTOkgw7wrqfuMRTJNb+3ihxPU2wwe\naGq1HGWKKVxobFqJI3VWm1Oh6a8Siq+pwu8IyY7Q7kGuSmHxHfVcMVm16Pix\n6weNNQkORbW5NuVhVTZEllN7gH+oMi9Ao3w0fTdP/8Y3p8cxw1DSgx2sSwMW\nFYU7Ha+4tRMJLmHz4+Asi9uCi+DeDuvPza0Cevk0kRJHPnHunP6ouTOyKT00\nqzVsvR9DJXeM1p7JPbbD32ONvAvtQ4Ig0BV5GxKVaAaa8KoPkQNWU51TXO68\n44R48zSbP2x00slHO73i0MBtp6HoxplL9ZCbDten8FXIKeLicJc9d3lfjnAX\nCd+I\r\n=MHIu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-toggle", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3877d0f391737f598f3a76d5df7711da7fabfde9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-MAhVcS5nb30QMebjx3JgZPo3TROKPPB7ulw7M444TBE4oPI8VOt4HredqNT1bIgQAwLC8Zbv8r5PFAMLgaVCcw==", "signatures": [{"sig": "MEYCIQCMb1v7znNee5iWhvLEaRk9nNmAsFqbwWkFEI4uuc8gBgIhALZ3R5+JX9CNgqzmOhnuqhWm/XKOP4ILSm70LPUxLSQY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzSDCRA9TVsSAnZWagAA4B4P/RwZHKNhtfQPMVXCTiBh\nd8Mmpu7IC15Lt/5Astx4jX2m83MdUyy65c3JcNvLwVNAH6ljP2hFgzMldGYj\nHGfoHFd8XPg6UkK8461kuyHxzhPVhqZlhZnlqmPHZFefts3HN3LbGsKHq7lV\nwuz2UQpcv2wIRw7CQYsiP6Sh4/Ds644Qo/FYq9qhFYPZdmZJ39zeAMT0CCZj\nOvFSm+2+xhLOowkf7Xg7OR/QJtWWC2Zcr6Ej/7tNgZ0RpBdB+c16b3pj<PERSON><PERSON>sz\nkzj2NaklUzogPyc2T5XUOXX9cQfutTgQs3FzmRmdTPztlnVqr3ZsaGBwdU8D\nxJvBj9I2CNjnrLgGymBdm+CfrfQQ91N+wLXWtOC07VTtnMkCxCyuAQOwnoSx\nxLgtHbaG0VXtya9MMq2nbvN2n9RtCklTqwvTDxPtaWaTnBHSKzCD8ebFTG4d\nuE5KMHCa9YJ6GFRixIyLz84lIYhiSZWA1gwy9xy9dh68Xn3z38Ndz7nonvPC\nsMie6z+neT0OktIg59DGP2fEfQoMF5Y4AXtCP1T9Pt4TyXc60MRQznFGqmra\nu0gYOcMrBl1Odmw9mR++JtWSxKqjrRsSKo6sFCqUACHTjQrAPuSBxFE13Bsd\n8EL+TR/wjYJBUqY+j2vNnZJHgNnPZivKmSiZlNv5UlW7YFu/WQwTaUmh8QWC\ndY7g\r\n=SU+U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-toggle", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0249192da9cdf10f58c6f113fa664de7546c934f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-UsqhSL8NBKrqdixgN5hafX6nepCBJ6TYuxKLYvuOrD5t+ZDWqD/ufHyooI4lzhiguUz0oJpLnUnG1wMxV8iQCA==", "signatures": [{"sig": "MEYCIQDH8dLrHG7tR4PbMJqd3H3LAcyRn3QTXCqe65pFlfrViAIhAJpgon82lBnHZw5mkIXqfFvjtuP1lW6wIteyIar/u9J/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr43XCRA9TVsSAnZWagAA86EP/3M2QYoM8T2DFhFSwh73\nuGHajePwWcQ/DI+IbRbikTDsQH9RDGnv8kfUb1sfChDFfgtb9s0puZUZPOrd\nAbt1QmOgc6w/mKfzWfp/sx1++OqfX4UJ+pd8HO6mjiDeppTOgo0orQnZnvHu\n18KkVe6d4PlOV0EQSm1FCRpr/c3oZbSPiZxXfXc/tpkOtfqKPVQqOSCrZLqM\noJ1kkjPljk8j99E3lfAVge6otT1mtA40ARF07MIcAJ9FVLD7vGSbMII2rvEk\nsNLytbJr3kMYcyZnV5GIyLpRTrXgg0YLDF0gKfYjfxEbHbgtUaQNMwxyXX/u\nzCQDu92kQsU2G5yTsWOCqUrX9OCLe1YkDmLor7VnFF/DvcnCr5eM8CZxZfNN\n3TiIVrAYLY6cRzDvtczFTmmc4R6Hl9PwTiTNZRvqQPmQRF2hEdU7LqcVIpB5\ngY9jxTvMLBiOOKZiGsLKOTOnyE28Kpw1DT++5gLczj268vLJNX7/ks6tTcpV\nAasWyHfdgNPxHCFn6lxJuOHpMVy9m0/xogQ+xHRJftigAzyzdnOm7Bz/UEM7\nctqC1Kx2yKlJJvy5PSG7KGsvfB4ZQN+kjY8vTmHCZz+XMlV0cq9SHB0pDtni\nxJq5sUWrt3bOsPXTpRSkF0e1bkrRjB/k6ucnxXl8tFftzp1O8XO5dPvXkz2K\nxehJ\r\n=R009\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-toggle", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d27bf0163476f59f2519ea8a62d0887d945da6b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-8KLct40Zb3qbDS+0+NK7JCrn1nCAzmhKkMZ4OA9zhCzW47RNwt84FZCdZak17l4byznrjX3MLgT3Gnn5p68Kqg==", "signatures": [{"sig": "MEYCIQCnlcnEgBAfyb16da2VkG6d4EmlFnA1BqADsluWuzeW7QIhAPdXjf8gWOw7kJidWZdTiqbmR2+Q3P5fuS00Rq0XXESe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD9CRA9TVsSAnZWagAAP5gP/Ro87Frc22SaWbPeNm6Z\niHbi8q4+CQmHvG62oQuZnToWOiWDa0G3wp+6LYkrfgBAa9sc7U0gZX1DnW7N\n3zrBm9a8fJyNhH4WjJ3t45hVPuP8Q4/n6CYJWqdheK0g8FfFyulmzNH4ECoT\nagZDFIFAx6WRqnnQXxF5dzvlAp39gMY4kAitRMPprhqj7zAFCF/qJpxUjp0h\nAAUhpmX9KbhFbHUVVKya1klugDXkXQjz5dUrNldldFzElM4EsFQbg8h1OURT\nYVl6Y5dpplFjlNJcdsKTUvjaqjrlIqJKDu32GXVJU9TI2JQim6gPHxkxbeOy\nIb5G+n07UIfOdeeRL+WQIjO0GvHQklK6HszZSGs6GvrAwv12cWO2iF5FwXE4\nv5500j7gMDNzb5Al8Qy0PbPIpqNn70GJcNL+mlvBnMtPP0reyHdBqsqMgDWQ\nnFR5teUNkOoMto4jlYhkyNBNzyqZ1FqAWvBKjm3Ys6pXbRQtdpEPGuzW/TfJ\nm0rqaBVx3Lo1G7/Dje+IIxxCxFuw2llnc2nMuKk6VSy+AsI9cxERKxb1ku2h\ntP/iZmFHzqW4nouU4SW4jMJdAKUKX1Ut/hrNQy3n1NNnEnrzdRRVlvuuu5ZS\n+1668Vjwj9ma1O+7ulvnTrcdlpDeE0qYWIW7Lki7pGlfx7x65e9z8MSDMjHy\nweTV\r\n=uOdK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-toggle", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ca471d1b49bdf0c9b821f4c4e39feca70b7cde6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-ig2LsivpVOxbPoBpNy/6dgB5WNm0mkPMaBBgwo7yufDhlUExdp+bRCqkcWkN9EV42SEbd4etNEIrLAqppfgq3g==", "signatures": [{"sig": "MEUCIFemjtjFUohOMgOE/hpCduubTC4wIh1eeEHYBs2oGj5EAiEA6ZJBXd5vzbH+CJqQUOVx+VGx9PlDukXx6UKJgA5QENU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiuCRA9TVsSAnZWagAAVXgP/2YhZMEslk2Rvpg5MGG3\nQ7OKCvPYoMno688i1KDSLPxglUSlVDCqvL7O0AKhFBOVtnqc5z9Q14gT+0S6\nShtV1KE7Kd+Dg4uWBZTtTs4IHF25hVLEvf8J83bQKTHxE2lbBH5dzVCIAqqK\nkowBr7GPIoPT5N9ZQv4Sl9cfU5FUVFJFCUioHDLJVZaBqQHzX1YQxogJSdMj\nw+Iowja/Dco/hktRBI5UBh2FsDc7Cv2hmr+3VwMdAhshuw7VLtaVz2BXBQ8c\nEDVZUwda7wQNQ8d1GONZUwmMDSLlpryYrZp/4FwNowsUHOC23mMSq/09qsOi\nqymWKYmQPr9wwoeQQ254DcIvAJIeOTGKU0fEgxnR865vyjKtohzNyUZKHjA+\nDsfH4CU3jE66mgdAAq4Q+9jaVDinU4peyNveobGH8wV1k8NGUTfxZZ2tw8Zv\nsG/jTDms5PPEuXTKsggP12FfXT9yV8WvfhqhDRTr6gja0CdxBgs73plVZpZh\nv9K8PkcAkY2mNuDoYR82v+qP2mqLHXfTB9pMgQ0rY97Exdlyx7tDXTLuh71v\nJCmW8B8VmULsocyI4eJyyPUqJG3a3I+9XNuTs/+uEWLR9ENceJQp9ZCXFhWt\nswj2mXYkgrWxyAJep60h3JhIQJDgxio9YBoaujRVb/HHUrx9oxRf5C86+Fjr\n09aH\r\n=rWNs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1587fee2b48da3a9a782b158a5e9d708b846ae5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-a/Jo3s48L88ztU5jbWyA4ywQtv/Gt4VNjSGCp2ducYkAHbBqX84uulaTEZ80hd+Of0mNnbP3WLqjR297thTwFw==", "signatures": [{"sig": "MEUCIQC0w68FvXumwnnIgvd0L87NJinJEHueSjNVjRSIFbsbngIgItmD8Pg6+FiRdAkok51I2POTXZAguRf97A8pZ4YdBWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLkKCRA9TVsSAnZWagAAxUcP/1vxlACMmV0X6hnk4maM\nsK7CnAsPceJQpNpEvIxdwn7d0xMH1oD5O8qmNxfam9ZFiR1tNZa0VKQas8i5\nHUQuqnucflJfmynfiMd26lH40yA6aFlYT1ew3fC+AImnYPGBirOOPphFaTg/\nKGKsav2XDSIS9M8r7UyXaIenqO3tO95jOsrL8hS6AVDRKU0EISb3YOcS4Zr+\n8i4z/NHS8RS5iONMV+hfpXpViWjscgIZg3t44mHNSKBZyS29RJDnXxiWpgOV\nFIA4HkRoKa/Zs4gsjKSY/ONuelxvbkpzKbel0UpTWNMfn3aPVOiWJGYO8na6\nVp2pOeKZJB6Iio0Pae95gcBfT/yyRvI2xjjZbIhqG8QiZOEl1vhbUiwU68RF\njCZKVwBn4iYGcCFx3un4q53NaHGjYJPwET1qr3Y+jYyR1XZW8Jfpdhfz9jjg\nrZZPlQkrXdq6jpHFRu1pa8eYlHUbxQrLavuG8fQSFSfBv9E742PKSp2wHSp+\nTCAli7JhAPNu24ujVTQgCSwV0AxSru68a1Obkn0NoOdNpisjmTB9/R0E1y43\n3kxDdT25x+d+vNxo9sPu6SwxCe6TPsV/4fdmoYvddqhcjJiOXRFTL1HSBS3m\nZiGaIDiljAJnUNvrqy2c1/fmr6Z92ckHKjR3ZGGhS2knUmokjP6sHgS03x/S\njgOw\r\n=HF4f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6e7da68b11d7b4d4ebdcab95e4ae3c47e5f920f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-DbkhaNLT1XICSQrlPVwz/LqdpCrnop1df/Jgf5hBFnDnnuADCPJzSArGWhKj7RStA/Ea+X71WMKLN7siHEwN+Q==", "signatures": [{"sig": "MEYCIQCCRDvX841bL/Ns7z8PKGDdzf1VjB5djBY7VsSAqjnnkAIhAKYdDdm1PjxaGbdX7XEOVyk1wthkvDbB7yAf7e/t5Keo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31roCRA9TVsSAnZWagAAWO4P/0gjmsz0aobfEV2aDOLU\nawWv9bRw8kVUi3gh6uzMdTMpAkg4vsD+rMYsNq21cbT2SZhrNpjUIvVw7L1f\n5bm0ON4gzDViG1Md+IVRj05G24RCWhbyHL43m5X0EWUINTn2KUrmCyLsF+ZR\n770NOnJpojHnKaJPDc0HAGv9lmjQY4utrCXPsFQ/56We/4PfZbZRjkk3xJ5j\nDDfR3E0vi+rrF1gNbh2UOPh+SP1dm6v8sLHRL7YEchuW42Jb6y0kzj9c5rEu\nZAhWyydmeQpMol0u6A/mUFxMjGYi+t7sqG+eiRCNmXhyntnY2Cc+hWILOeYO\nSNd7T119etL/+jp2PnMKanGTeK6ZDTNRpwxWF7c+Br/+URRpTuvlsT7ClNs1\nceP93SgIwODSPz5abrrw2Hbso/zSuZaThMRMlM2Zkfl8ePo4KnJWyWVQXnlW\nNIzGP1bdY3r8fPHsGU6p7NvBCXZ4Nqt9nOHfElE97znVhDySRsb42bmfVj9I\n0km7DGcqwla8t9M0V9SD4d8bZnkJUbljBFZP+tCQxet6r1l+FR4l1WEBGOOF\nmgHhP548EVxaqwWwIfFT/PCaGCuE3tU+FPCKkU3o9JTuu2RToITkWJ6HxpJJ\nwVlEG/Rr9ZyeKSrNkdVrg25T09yrntKOHJ558+ZqfqOqD47FjOwz8AK5mAv0\n+thV\r\n=gz1M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b062337059140e9266378f1b002f1ac61124d284", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-u8Eu71IynEiTqqk496T4+Gt/ctD6+MggTjA1qBf5TQa1B6r+SAk7wwpJDUG/rqxWroCWCPnLXybRVzOTOQIegw==", "signatures": [{"sig": "MEYCIQCXjXQIR+LGbDAr/CempbRGIIolfUrtmEiWaIXXfhCikQIhAMrlTOnaYctRtvpFR/hoxgAj9DU71has4crtxrfcOs2T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEkCRA9TVsSAnZWagAAu60P/2vFB6E6HSxDRFU7b/aA\nMUjLVKvAzQ2TZ8o86sAbg6mOGOURGVXRa/Ws2shWPw44Q10QPi4/jsU8xkxD\nJqRWx7rJS3aERsO5MP2cQJ4P9/zzCRoObqOVxn1WyWTwWa4S2vQNeHvRVwJD\nMAjjNkFQWjwDzs5vUqb8sTq/gwmxO5j/C6S0jLaoq44iLS1GiZiSGbx0RnS1\nik8OuBf09uLCv7j1D6nBXM53VEgs9kNXb/dmqNzlVCU/qvn6MGu0DzlX+2Tf\nLQsK70wgQc3X+QYnHxyUrvKqhoLIwD5UzgjUWwEyCafcO2VYbffqMHGNnRXf\nGG/L6qVFTWvOo47lw026TXktb4FA+OaVHE2/A/9cKuC7mybqA1ID8x/yRvmU\nRKEH1fuwVtkbYYKoQHh6t/LMqwf+2DdqyyUBijGkyTJF7oQDCSWHYvPmqORA\nSnvKgZACoZ+R9OK9eTu7Cwd+kzBd+OWOll6B9msg8+h2BChB8sTtdZp6yI4I\naOjajYxRjPmUO6t4LNQO3n2tJrLu0Uscps16XlidMa/gLi00ZRdAaIB/R1hd\n8WiMRnKzH+S1y8hjhNyE2koaz7JaKWIzj5D9teju63bXRJnfFUzk9ShbyzeS\nZ4CPS30QxQ/42UndeAn7bUPCCWOT12k548KCCjcaC5zkpZQmflmSwJZXGRFN\nYQ5B\r\n=bVsY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "401eb59d8f7422f297a6ff30d7ac0100bf264e78", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-6sFVYsDaQmd3++EPcL7uTjIeJxa/U2SRHVte2BLLJ/qFSrhD49qqbe/O9aEXIlWtPEENw4H9rctib8hJdU8qTA==", "signatures": [{"sig": "MEYCIQDyGK/6yih/QdPqmd/b5ulJK1jKIAfuLmQubrloJJx61wIhAJRf5qK1H3vEnwKpuY/X8kHJyKhaqqZZYpzgugJx0Yzv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CnMCRA9TVsSAnZWagAAVx4P/29dx5bnAwl6lHlwTwzA\nuzw2wGj78VjqAFMritNRfcFXBnSTeLxxt/RgxHhG8irle8manFCG9fuK5JLs\n3dyQ6/mAuBEwyaDqzflTbno+yhzY0kFU8u1KxLmIrY/KHDZWsZCGSKHfJtTG\n5IPGyug0epovUKy1nLNFaAgy6DsKdtBGWY/Nf4OiRBrGJir8J4bm1W/WjDHH\nkjARQt3MocqaeOCg9vSanU4VaFko+y+RGe3lNu3Yu/QXDGPTZqQ0+AClpgxa\nfh3Chn0gtHIjOFjbnsL6NC1WFgKEn4EP8F1wjtat+z3vGHqkJY056dLdSZ7h\nzBjC2Sh03AVzoEXu29uM+VK5hTj7xQjKLW36s5LWFajudYq8Nc5dUeHtw7H0\nNU1T5rNXA1b/+B/cBWiy0dBgODH+MT+9E9bW+nGECEQGO9gWCBDvxWJ0eh0t\nQezjJI+VCPfKHYGYeKFNkJmY8EXPkWIaTRAX4bE1/r1haUa3dQd3TVMPkegi\nWLqE4i/uHoYuFVdn5EicHoLdhtltk6rLAYNHwJIcZPq2q8I+OYkR8yb77FKc\n35kFxkPIHDs70EbuN+emTFqC4zcOuL84F/L7Q4ajq586LyR3iFWU9ir7vbT6\ns2YA8SNdmnSb8e+khwhtgkIcPwYfK2tZ++fQOybmVRjShZM0jDRoDyR4AV/Z\nQRU1\r\n=GVyR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4db32f99562d74305f83fd0a064f1ce8345f199b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-r8mVVBx+eJACAyfv0gmkG5JL9blQHlznNFhNHw9FOzLQpbKjqwihV60aHfhLoTktjwhTrRIOf11P/W/6HvOMsA==", "signatures": [{"sig": "MEUCIQC/CgCtu85Tc4SFdm4wkgrp8ZjESWDGab6JymD9J2AjDAIgJjKBmyKZjMmRLClTx/qj3fmZmeZAwrzQXTMhwzJEVwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GrQCRA9TVsSAnZWagAA4bEP/jZ2rg175L+sCrjPMFXn\nOYmX3avmfVRrze/TxksEykNKRxfURQUOTXMJjbNQbdXb6KzeX3pBIRbwLNc/\n8B7XGDZDPdu6wLGQCHpjq2Xqsfz5hqXXRPpoEsjV+wBUNa2c0gPny5JkSn6N\ntNxRsAk3O1yfdXTiE3HM2hYGOT8Wthkq1dxqlbMpXcBNMhzXPtDBNSMbMI7z\n2jY3aUjqsbO4dImZ49c4P8bh+CDjZeZ9iZL46HaNSuT2hWAZyHSvXwBvspSD\nZAT9tJCXBG7w9Pe502CjBFAdexdlu967PEe23dqkoZJmfwgYwZNtQXxWD99E\nWTV6JcEIXNeND1dwqEZGiSj127rymhUeP15vtfGftQNWdxSnuOU/xvMRrvjC\nzTqFdU18MmbN9Ukp6IV1lrA0Dt1zxKo1Rqp2cEyO0Avuj6XwxoeF5YDfqr3J\n8Nz2Q8fbHYxVRKmNkrHqvbm+aRaZ0i6/kcP4diNz0/Gf8Srj26wtgqlnQv51\ndngF9kx3YpwfgtL3+HOFDeqt7sKmA/deJ8/xrTzKGS3vzKPKDJDY4t3vinC/\nDgfUmwhJpEgeouV8xZbFciuyFYUSZ/LJmAiwtW3zya6r31jgwhH7PC+4mt9J\nOhWuTkvgzQB1JC3P45t/6fIDgs2cDA9LWak/BvJTgP/zkK607+LxH+tu0YW3\n+76J\r\n=6dfB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "129ebc75346c598ae04fe40e6038d16a3c5bfe86", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-spp7ADefnzTkf4STP9e1Ms03o6u0mdzYw0RolG52RrsqcMr3qz112WxGrriaq2y76buISIrL/4EjQ2pVUwlrUQ==", "signatures": [{"sig": "MEQCIFvgbOyPU/e97ObH4OANewDUWAQos9uY6EuvbWpbtrivAiAyaQd0bq7stg3UaoJHXF5F8qsQ76VMW/J4dHacAedGaw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZdCCRA9TVsSAnZWagAAZLwP/2I4IVixsB95U+0TPDFh\nVtqP13cnZ5JyMN+hTpSwcTYZ+JkY/v2dWs4JdG28/3OQ0Ni1NINVrI7B0czA\nb5eSxF+24zuzd8F/RMMJjz81G5oHwWPkUfyYq4sh20032SZEsV31rnK+dz08\n683EseSa6/D8S7DEkrHwq2EzH6o366FgAF4W2/4uwjxPgTO+dsJvYFyu7E30\nNS1tlzMvkoAs2nPkbVCwGEeXivCyKbU5wPupWKp8S9HlTiE/SxoQtLsjsS+s\nqe4hyguutDtyFmYx/f+EMMiaypsxxME8kdcbyKQXBr/RTGCzdffAqGhgDEdP\nM9tzaWzmzqJpywJ0GcQCYBhAZWlj9+TK+P0QSkZnbeM+CysD95noep7IX6g1\nOi3RZF8e3ploNxMwoQCEgqGsqL+zXNS7XAbSZa8cdRnsBGN7rzte4xCe1oCo\nPh45T4u8phuUTivHoPO49yDk8qcKM1FFKOST0OyPDMFn7WtsWPZVmZAcvSji\nkDZovWvjJUG/PDOW9jli/5xvYqueyjKbpnn81wg67KJVjLMgHYUST1eou9XJ\ngW3ZeGo3iz7BnQGDx4HGq0vngbtY9UF88owMbY80BPAn1OOmvcEUQ+E5Is+q\n7pfos+Q14OvHmmr5guhOK0RnXEw9eBzqnOnBSo8O0gFHwHcgor4bYEDvKu/+\naa0g\r\n=u06D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9788f12fbaf1b685c733122be61f0c0c82d5274f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-SjHfRSRdAKVNEq4DTJPf+jJ/JD87qlA6jueyEi3+cyHEkK3LRBQET84WBLvBFUvwiioRLacbv2pBHkrBtq3WEg==", "signatures": [{"sig": "MEYCIQCOlJSBAPwwYWyla9EsMB7lN3t1rCwFcmUvPBTUMzlosQIhALBfAPHyx0MiaNTSfHKdjoRHK51n6Vm4D8uKO2tI1LmC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtfCRA9TVsSAnZWagAAh6IP/ie5/SbtHzvAe/MITkts\nmWGtm8pNKNo5nqxLRHtrPM2wORfIdfv6RerEg3mHHLpqHs+vlGdBcbbzV8pe\nNMIovyQMiza2CU4LOtuXt4jfXSFM6fvKpdjl+Vox1yDGnZ9oWycuWjj78HOO\nCgcQ3DwIo3Mc29sIym3jh4KwDmwC491nZf1bulyfRvh2xqv9UBnEosLzdMic\nhhlj4Dyv+ZISSheiS74l6QxLBo8sQrVdLBpu25X78tgnaBsIL4M/WWenF96S\nA6R4j2z8LX6maYJIhwq6/L0gbFYV+BealmTigLvISezNPE7xnVnn4tuDw75p\n0uBGmYuUOjrvAoGhgy7yLTkB9KThOrApft7bOklu2iw24jlzRBCw3kHTVWyf\nZOJk1kEFIJY34SCvYkz1ZP7RwxydAo0kHZgnGvlrRHDIrp4jqElUkZ9dcDvp\n4i06G7NgDuPoDnUYeneddjVzJZsFsBzdIWxMX6ulPTuu0L0peSTuSt4C46QH\nrIRVS2JPxHCP009BB5RPjh35UNhcbayEcWkk/mf5GkNMyDOXzCaBI7CMwoYH\nOjbYkL4MfDBZav8oFtKCpBg69wH0mz58h3trE9Q071ctvJujt7miErZ1ovSp\nqVZHX/KqivC5cZ3nRCygOVgrEoyczc21cE2stqgiigQoUxJMczS5NcSjErJ+\nlL5b\r\n=+2jC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "630baf30b14f1a0053b5203bacc3f405a3f5821e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-c+W+WTqP2IFVK0XA3hyRP4pEccYxGHT9WRXm7rxtiQoJ9FaMUYO9WSRBY2A/NDZB2Y1K7v1jl+p02mjvwii7Sw==", "signatures": [{"sig": "MEUCIDZOgnkn1TpKuxRHUVbtUAN2J1Pm9mksC5oyFT5OX3SBAiEAn7Arj47wNZyb19aG5Yd/wO4+BbADMo96nfrrGaNH4Uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sd5CRA9TVsSAnZWagAAOYAQAJO15G6mFIUSaOlOiXgI\nzOgZ4lEVSrtt8ob/IRcwpkpnj/mVw6KUxCC4fBMo1u3MaLsFiZ6jvJugE4YM\nk4RsjHbvi63e3FPm9n5V5mXzFiaP1oecE13nvsMI5xiRAfxFsm0KtwZZceeb\nt7nVGVzBEnOwxjeoENdjC/5g4EcM+qSBf6VBqi0MX87pDJe9ON965XfF7spG\nqR4Kc6fveZ4rAQpq/uAt1+TScm1eIk+3uhyVXN0kmkvu3TqbfnKo+btqjyTd\nvt3O1FQ+0k9oMp44ZdbBxmOH4Oeec1J8syCREQgbgNLBaNa5iuUHSeTdJC/L\nxPFwek5cS5kkqb7qesI1MD+IBnmdxE56wt3gt9OCHz5AOSvgcXKpEsYEH/68\nzVxHJzz0EnHeiRQRJbRVEk2sdzisoG2kXuHeb5MesNsQ123AQy4YDWaAdiyk\nLrtKAnbOUT1BogGrGCRnFEHhziLetE0uGwYAoHWZkGAdObokrUJeiJ7vF574\nWK7zORQ+XyT9XifjK+Rmt6Oracvbg9CLEmOOa55PYKyQCOuds166P/J83RSp\nLL6zJTYDiP2L0ggV5P8eQ1CyRiCvHDrQoHYmPRz8ykuhs6/q7hfw6+0C2P3U\nRlGUjtsf4wXUb49P2TL8ekcjO5IPkz8kZcycg69M8AvXQdk9RnE3r5xppwSi\nYvPw\r\n=IG9c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a47f78924319244422323036c6cb11fd9730115b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-aGDZ/Cbz3QVKjEMErX2KLGHmC74ojeNDnUmZmweorWbTU18sCsF8M/T0VQog7knYYJ09i8x59CQ1+JJ30mEZtQ==", "signatures": [{"sig": "MEUCIQD/q6kCCmeKA/OiRTe8G4QHGLeGJixRCUFxvOhUaAZ29wIgPAiUn6hCEqASYj1yWq5BeQm7tF05rJ2X3LDKw23Z/xU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xD2CRA9TVsSAnZWagAAW28P/25AJad+rvVbqSvGERxU\nGRHF6AJcp8eFy9+0y0+xov4l/NkwUi3dnZO2DLKGKDKMYdx1JoanUvcpGEEV\nQMlz0tzXtBd25NacMlHeZ8Y7uxpylElz5QfAdg0CGoBI4GWxua9qsmgfdLuZ\nqU5dK0nTEZhny54bpM5stZB1OpFkhL9tPdgrRCm8U4cCwhfmwqcOOlWULFYh\nJbNttLkwn4rreycfOZRCBYVwZ6sZVSFDlpQnS5XYJvLQnZeELnDLIxuxzzuc\n3hhukKuPPgJxRID5DIOMfKyEdYdqNVg9op/IAYGhDVv+epf6o9hzTaQH+9ld\nYLWrAUngPgvNPgK7qTwaKmlRPReqbX/tWqaD/By2fXgggPAeFcNEpeQiGk9u\nanPQoSGfrtifxEOg+h7RjeEOp/4fC3KjVjOwPMDma2CcjThmU63m3kXwVJTW\nRf0ImthEx3H8OM+oFc2EiuyNZC3QKjLzAmInybkygCodjU1NVOWO2UKypjA8\n1pL9huONqizRN9TeYhT4IVp+XPnzMTAO5c5dpknn7pL/Tm1i5iyIOBzio1MK\nYW+CpWsosZwGezMlH2fBTpSnBL1OBpAKg50DpOlZ0yv8AxHT00/IAf0FWCw/\n9sXnE0kbLs9vud4JAlhboSStGqmg88/odU6DMIOCNdRddYcSYrr+thlNJ0+w\neV7G\r\n=YbRI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2b8facf5a25b921f95d763d6d12407445a4dac01", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-oW5sBxjQ/PEtpTQTarj/hD9lSihZGBhYwyK360B5FlYs5EmPYmColoHXxUtJwevOz6H7LABAZWfbTypFcAd3PQ==", "signatures": [{"sig": "MEUCIQDJ2OTFEPPScjGsdkoKiqnw0Fu2JbWmJtLEd6leyj89uwIgb0XO6q+8OgB76ibeXJbyjEhhmuvhpdqiTffddOvMYLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xL3CRA9TVsSAnZWagAAmZEP/j4hUWKowmDqUzS1sQcN\nRByKfVr3BRLqezr9/kVg/XglLwlMgFlpt8LiyottxAAWn2wN9u19XTl+hhEw\ncN8i3R7l0U9EaSBbbq3iQCvZ/8+Er46FXokEmE9TnSF7R/+cJVCYL0mz6q3A\n4h6PwRyp15rQfzCWeUOpwfSd+zUszMG6XYD00fNQ5RLulAoYLIJhYo7KAR8Z\nNFbdfQJDo0SAu6U96Qh+Mjw6YnDN2jvXYA6MM1EmO4EEbMQTHNUVViVijKsz\nKj2NL7AljS/NQJbo3ibGzVqxngYKsDwVdxmg2QuWSWJsl/JvE4+DuTVtoj5s\navMHZeK3+zv/pdaQ9j9mNu+sm+H0Vcrfw+zPhj5Ou4MaKe0Y1OLFG4PlZulu\nKytOeRWlhc7P+5zj79Efzx7tOPQ9FWk0oyyQq4WcSfBQfQyg0HXqBIcNfcS6\n8D5ThjsGd96aZ7pkep1LsUEgqNjmvHuPbZa6x3kUKJB8ytwi+Ty+5jeJ9Y9K\nQuzpBr1etz/yfGU0RhrmXeIoUqaNRZzFYUrYxTe+VqCfZ+wiX7COlrkdibG+\nc60a1njuqFCylFk8GSUntu7ljczsHUptjHJim+c8Q6RDEhU/lGF3r6ifM2kT\naTCujOoIuaqPGQwLyff/4qQxsIWaODdtjcDmQoKURqpWZaHGXxdXnvKN6f3D\nTsqA\r\n=0eH7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "91c07392fbc0b3f2f0e33276f7f88ac262c31cf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-EwxawNf4DdQCpfSWR+HHduH++sMRRKR9rqWoHUmUtpxiVrnLRO0rrL8O4jxu3XZpk0AEjzodkqNmXG5hSxE3mw==", "signatures": [{"sig": "MEUCIQDN/nWAZR9hxky2Gflgff+oU+FGwZ2+1WQOGXvFSS7xIgIgJc4N4+IPUHfg2DMlxs4yLlyPwFmVoYXEP2AJuVqddXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D09CRA9TVsSAnZWagAApbsP/j2HgcLiQJzBV0RE8IK1\npBq3riw5haiX2HhoASDXtvi8kxxNVVEpDNcr/J3Z0K7sG3iNGhskqLHmXeFk\ndELdadqSv90wTIg6L3B/fO8U5DXN4fUHP479tCI4/nHHFmteBSQeQlVRksIe\n2LcL/Y3VQBflB7cIOtnpi4xR0tZWasIfggoBmGxNhFTu205nYW95MqoVb+h0\n6zqp9jG2OAzWbutRZZJdHdGwX1C4JgikJ58h7t9v/v/iY0twSPPDNgkYuCIF\nXajDoMt+WiggkmjLhDJzR9AybO3edAbiLiuspptDEXIFNFNEY7yGPRyGEm5p\nQpQz2fAhcKp3IkpDxpqBiMEIwvB/R+yJET1EM+/Q4jPICh7b683J+7RLIgPM\nEZ639y4PX/Ro9tD+fhcVaauWssbOhtzhGJqh2ytIDy1DKZjypCcN53gkrJ1H\njWtV5KyHEj34cmZXIl+u4/r763b1GDGsU/nZ/e53+sesxDPyK9w9i6WbUUox\nLsUJJqrgZxUDl9n1sI9XOD7159TciOqJ1PI847z1jBpyaqnsf89hWkuSxokh\nG1hEDWm7JbE7K/k1nX7RTIvG4k1t8hY8XKHX/viRk4+OvoSq0fWM8ozf4jSB\nO23ohzVe0OS6knbeG/0LVyYhiTYqVaj21twr7p2MIBQ4+dEMST9BYY/b5nrq\nEzIT\r\n=Mpx9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b8e5e02778294b607da593bedb122eecbe8b0488", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-3f+ViwTFxBC9vmQEWTCNUoz6KaGaFr4/ClQThY/BfAbAaKDFhkBFxVxM2uYHXP1uCtUbDNqsFaosDOuLuMgRTw==", "signatures": [{"sig": "MEYCIQDMeO1HCWxQPTlwXe7+QrXEU1N9TpKbXTGXWwR2GWNbcQIhALc8WZ4Oj9R+RTM09xCGMFvw58OP669HdkR23SK/IUWV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8STkCRA9TVsSAnZWagAANL4P/1NFPeSyyWiqEx1f4mDt\nACgKWyBCUz2eQnAUeD9hvbkBwueogh2FCG2gqMNO8k0uHk1prjfz8gs8xpKb\nnuFDuqLMyRsTXHf1Ee7hzvfCVUGUhRMlUN9rLo0dlanAAjLo2lyJYiNPWrmv\n8ZMnUgLQs8znQ8vCbHZ681LCh0VboATOesEPX0M3qTqPgGL+2v+kTK04qQ/K\nms7IkyiCOdaPOWSfE0j3MDlMkv9ySRWUZQjgB/J4KYNTpPdXmQhP1+8wM/8U\nHfQ766WGdRpVjx56uTuOLUKTUIZAXW8JFRv/iRZem9oCM6eXHp5I3jIL2FZ8\nfOjwfQQqM2zYmCaKHD4TW0jdAPq2GLJPRfMsbsuuecsdrU8q1uKiRt7uk9V4\nEm3JwDL4PaZa8/KrE5kEBaBmlIHKnxpcIX5RORRCqmEDcqQhna7ZTdVzASHD\nQRinel/gWGy8tNYVwiRiESoucRC/d733fy5HwUkLslz3HiYoWfpS6GtMnyS3\n/Nq6DfMitGqvhpbN3R4NjJKg6d90C4F7sz6376EPA0lCNsx3i0sK0KpqrhtE\nKnK8z1HJVNo1hz2hicwjZGHgMYlrFC3Magq9ZoZJhu2Xyv+iQgZiKYThuvQN\n9cMuPDvMPmdKmxA71M/IfcZRKIlG1aN6bAjKYwdKZRGcSfaVWhhMyGGZfl4h\n+776\r\n=yPDb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b4eddd0c0fe4b3bf3019291dc41bfad2fc9e004", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-8KeXgITr9IEr9/K9yzO4kImP1HDQ/0d/Br4vPcZaP6yM2V8gk828A6y0QQEEJMPXpL4YueIGdl1GQP6uASWPLw==", "signatures": [{"sig": "MEUCIClhBDHWbswgWYomIzmuYSAJvl4RuHg7BtZDIcSCUF4kAiEAg+X2j+wCFuBvOey8ewwLfG/2qkAzV2TVnhL1lFpNPf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DadCRA9TVsSAnZWagAA9VEQAIMeahrSpJiWBPicvA+4\n8h98Sc46HuXZ0vwLsVhp/AYJGaQ6hMVIBEI8lZWjTge0MGPoZ8Wi2OnFZ7ix\nf8FU11eNSzqL7fPh2iqOrrWdHzlVVC+zQcaq+v8S13lCp2n4l8iPuZaqKQHN\nYhINBYAo67JNuEo45qcIJwg+7OoJreZAx/jyVFTzhWUVtm4ALTEwcI5FV/53\njCctTBRPfy34rmQ7WuEgIboiOqcFJ6D/DfxdfsWjoMAVvZxYl18ShROvwYe2\n32SHH2HB5i4lic3dJK4Oylbi9LuQXuG17/2XBxTFhb4yqtTPrchmX2d+44zN\ntkxCL9500sb5n0uOnuhfw7iU/SPzmpKlvotVBEZdBqseFGs+r6eEm/RjDg5/\n27vQ7w7gyQ/S+sbC/9Uc8E+BMZ/cnsSRfZO922/t9nfbtmbkxTzqoqbxjdZ2\ncY2iiFwyl3b+jEp+nEuxiwPD/67wXu2smUGhzE7qLVwrpkv6uUV4HEfLjxr2\nQg0zzWMFrOGflh/2hZ3qntnwQrGCTCPuysIKWR2hdaQWSeWabtrP9yBILzuy\nmj7WJb7wIH+KXZv5oG6x6Ki5xk5ORxk/K18mW7fhRD+mNVthGsyJNplBCzCr\nAbNA5kjJITG05FYcpLQHjPnZrz9mi2gsGqBkDsRs9a1bZWYNy8Z4OmQ1IRQg\nsrK8\r\n=Livt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad7ed76384e58cf960a1de21d996348f166070af", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-8RVh2HuWnhqmR2wOvXxaHEcGGS85wvlB1VpZVLIgKBfAj1v+3XaaZq7mbHw/JQ7xjtJn+NoKFzIfKi4N/MwqVw==", "signatures": [{"sig": "MEUCIGQf+37KhuDUyD/tRX9egqWRquJF2HoUdMi0vbd3QWweAiEA/2FMAEF8IdIXZFzCku9x1GngMSlbpFn2cntH5rkMHCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WotCRA9TVsSAnZWagAAcY8P/3ipgHi6FIOPoNm9O3PW\nfmQNg5hd3HRBMHfhjPmzB3NKakk9p/tSnJ8HZYGKp5IFXNnARUw7Ce1qPm21\n7n0JjQH0DJWrq66ks1a1vaowu2FCj5Xzh64SfQaDvC0DPe1WqGKQxIH+wccC\nkxax6bjLXirkS1No7pV+a9F7G7acWz2F/K77P6dd1B9kHYFjbL+pnNlIbh1b\nbHzH5hDqyhBG19wg3KVA5L3C/3ldiehiivxYUeMy0EKVvPcaTqa4lojkpbWL\n1JdgnxqrkX/4zMyCPvmLC85nIK/+S9kJRP7bjP9SbLZwz3s2Y8hmKrc7h2pP\nVw28mzh79h7Qso6zHF45UGWoiDACA9CNB77V0EdOC229ZJCGgy71O8qV2rxP\nEXkxSlk1iKymZWVyPuGDCSamno42IAOB3mtg4Lqsx15MGCDHbYgfmA1bj4gN\nDAdGGOUtHAEjTH7iaPo2DkOHZLRPi0Z7n3uZfPgZyqeuzQl0yTyZtJnghdiN\ncwhoQ19KtLkxwk1Ciz4FjRV8MGtmGPMvbrCuKO6yiaxpkzNZR+9NEizRf+SF\ncNBlIOd1+XQt57ajp8kPatKZqzDqhUM8w4VzLm97L/w9TUaHQaz3VZ1iDJkV\n5KYaMMbitln8CyoP8E5r4uOIns86uHpJU1FwJ9kAAxG/mG3B0hkuLhiGfNzm\nZ8RK\r\n=Y7ce\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "72664b765ddf48ada187ee0f72fb8d58933849ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-WK9tnsqZoIdju0M9xqsUcaoXtawmpczCgJdI2s2ynOwncmnLAVA9paWElg6SlzReUbo/4R5b4Rzm4aRRDoXt+w==", "signatures": [{"sig": "MEYCIQCI+7h+2Tc6R9gsEj8eGnjtz+2fLFch+wgnxKhuHRl4dAIhANKw+u2Uj3gMBwYa4BSpYELicFohwFzjWP5S5EoxFBPM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rVDCRA9TVsSAnZWagAAbU8P/R1fH398h0Q3yqolhjFn\np0YP716Q5jpOxi3pIwVNEBdqicGmeVlJGvKTufGOqk5t+lCnaXWs1Txm0gis\nhwW4ELKAs5JUsSRTvMwnpyZdxzwEM6qI/bA7zrqTi4LU3rrbxj2O5PnyqQhM\nIZDKMyUrgkY2rIqaOLovTNMBQ1uA6a59yOEbUMntcOxAxEle+HVyacRiXndL\nwPTi9vVHBrq2DfA6r5tU0PidfF2Kfu6Ol5P4514ezoFbJWqBSXx6V6E7izIo\nY5q0xILXOF2qj3tcfCwo15MnQZRQ0BRZNguaaMjnPs86WZ25o8DjF7kYAh/h\n61ciqCHuGZNQMaGw+8E6J1l3EABUMRoyeYFxDkQlj3h8/U5n/IAFC3Tat242\nU+4doich1ykRJJ3d+zcIDvGK+sBWA80P0/6Xg1LBXXZAKchn+r7Z2qtHQdgJ\nnS0P5JZGFyTPCF9XPAhqHFgwEULT66fY/NJmj+mLZ64wYeVLjFnxXvNATb5M\n9OQKV1cZWODQ+rcEoNFn2Mkfs76cguKUmH8ZXls/nkwF5D5NXYqHk8B1o/MJ\nKlKALO9GTi2y36ypxUmL2vYzsZIjgMvNhkyTYAW1xB9wUTmHPw7FR4Cg93L5\nmxrv7k2x3p+wkBs+k3KBSecwbu+eKZblM0tHbgtPQNZ4O7ukUFwZ78F0wdkK\nc1wL\r\n=lUSn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a1630c71bf7c1e0d4acbad6d77bece584de350c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-cNP41PEw1RIM+87Z/Ts/2q+WIbPy9mece259DJuz2MInD2tVY0HyEEzM+g4R//slFg+7wpNW9+pty1M7peNRZg==", "signatures": [{"sig": "MEQCIG8805R7Y+/cKExYQp67eGNCNf2wmGnU1ibdlv3SJyL2AiBmVs+ku86bAbbOg1mC5B+bclzJLC96v/qZ7hE74Mkpzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/oKCRA9TVsSAnZWagAAM5sP/23qT9gYacVK4rDDc8KS\nMAyLCJxjABMPc9VShfORM++YEEgPK/q0knsGDnd5HVAbq4v3f9o7ABFxRh1+\nmfxw+TKRlYLZqHkCsngSAwnuFZU9VSrqoPL8IKskfQeFkfVfISCy/M1DuaS/\n8kOTqPpk8hxC/xVfLg0mg0hady2mtzWHZokCfPPe+ymrKWDSw/26WdT5SHq+\n4ZfsZKIT57vh+RHscn1Tbg4evowlNXme+PrlnRf45+i2pCdvUjfMQanA9qgI\n78ku5HcRlKhxT0XqvPi3570USQYlsAsMWYUoVCjNSmmVyKkKzSaU3dqzd3dC\nbZNjTbgjCBy3xoSseJ3JCuLIl+SxTKZXGUc3NMVrMxkvFEjLsNMmToA/t3Xu\nPVswtnIDtTceFhhe9FdYgxXqXfsAfgn4vyvtQlAs76tJiZ3RJthTWTbiPMVB\n80ExTY3fwdmTQSfeqlZDiJ2+0+s/CCqNLpfrMpiFnpl6WtF3Sbbncn5jOeMC\ngZpSK0jERo2c02Bg9Ot29hg1kpxzRLY6c12HKyrTx2ymO0pBeXNbyQFr4tlJ\nLUc+ABUa890bdUW6PxiRJAdt4QIzli6Wl/mn17Z5hpRLJ/IM+OwRSLPL8ebv\nCKlIZ3y4nyg0X69QgFls7jRTaGu3XsqTt28fXhaO1BSmRao/VYKzDCRKp9Ih\nkVoG\r\n=wi5M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ee2f0dc46effc7f229d00ac66bb1e550d33754cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-xZTh5ojdPBeBpk+L1J90lll427fEBTGl34PzhLmmkhXT+pUr8Oq1Mi+QmRqi4ccgvk/ieuEftPD65CBPThN6IA==", "signatures": [{"sig": "MEUCIDitY9H598LKsUduWa1fwOboBNm/bPThdxEy0uDmT/DfAiEAo64f61oPNuoRJZMWFuVrikwlyzePD8qh1SJQk0wgC90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIkCRA9TVsSAnZWagAAqrIQAIts0q3tMMm5pMvwUQRc\nAf6DenKARZN2CeixaajWxrG1+dGBcJ4nWtaHiyMSz4Xg+lRNtgrMD27B07Pn\npR/ecsCmDw1JgkbfXRSn5iX+JJ2OWe1vNctrcqz0lP31kQL1F4BzPUKAKNAh\n0Mn2o4eZ//94go60LF/X9it6SDLhfI6DVTyRqwc0y/W6zqwb3L7bP4UjBelO\n5SXEl3DvOBWUIErUxOKwyCFyhlglqW02NxZ32994M+Gei6AJ3bFYKelBZTPj\npqcxtMEcJJ8BjNiDPUJumJzoc3SkVR9kPxObuZfW9ojWTmkV0v5VHF091rMs\n38GDOccM75hZUf5GLe+TsJ5y4HLtVnmyfu8kYD+LbtJ1jTdbijmSyNYXiR89\ndSS6jEMjC8XY9WIF/TVGwhX2hYLoPtl6T1PtJWr44/KLzg6qkhdhooSXHOM8\nAIaDXdCIBDT7hz6qBOH6PLIHbar9iKlxKDIy5aQN8FmVVeWS4UgsBOAxSMLU\nsDpiH6Ar9o7d2m7unEKngA2doqKmDmOWjs77xgwQMlTJaQ3tGYLlJTHW7wnh\neoxIuYeqLC5VRuy2ZXlm9WAlhjxBKkMnBoFdGDC9RCLofW1Vx05pNxv8DE63\nR6PtkYt8GJKchNScY7GzmaFiYYEAvzXS9ND0Uz6aMafGN0A1mX4xA4x+YHjK\n855e\r\n=m8on\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0291f86c53a044765173d4ffd8b6b30d1821eca9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-5N75SQ+rW5JgX2SZNUyf45+YPpUtkcE61gh9R1NTgE5qX8tT2ClkFJFrf6peOSBf+U/UBxN/u2ebe2xa/vX1+Q==", "signatures": [{"sig": "MEUCIQCR/tlFwd5ulkJy4d4NsAVsFAmexvadvQIc7ltQBVEZSAIgRPl2RHL6zjFpctB6BveMrXDSYCELRY/Jhv0uu5rgxEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYxCRA9TVsSAnZWagAAomUP/2T32IsI33so1l4CpV4X\n9maOflAXS/74fkl6ak9Zp4ix/k4EXodIdkqmObg8zq2ho+cLwgcVs4P5Oykm\naDUpaCwlTDLpwMmRZP0QVc0FwV3Di7LkRGesIN2Pge87l+h/9SN+mfeRPytC\nf1owpQIP8haPn23D431jvb5gc8s/iIhuYosN1W/ofmLv/9f8DdOxIDEPbYz7\nzywceozFK1xs2mmws/Iczw103QuxLhRdHkyHt7akVlOlkEjzwxbOlj3DmJnj\nFFbOscX6s4irDwx3errxjprKBCYRgJk0qre7+4Sr95b1HbEXOjkgjZqgMDy/\nwQgLMUUnH3YhUy0Bjn2wuuQm5UE3Z7EH0SX64Av4m+glxgcR1uWbrrlSLC1R\n0mZyhJUwoqxEGqiCRgjuAydschwTpgNTw19YD4FmAAEp8AlqHdahdYyiwpvi\niLPRSwxhmxa0AUU8aMUR/MriTwRSYu+5KAsA/XhKCkd3yolgpkNvOU9P7g9N\nEAfiz1tTFn9/KzCnhbxXStxFIn9ouWYBeg3uEe5NbpxgfEFnsIozEi9qyqcE\nTbESsbZIQ1xYUhx9DaHAXf+VfRS2lXv1PvOEmNNMyZ3rmVy4cOAPcH1QIez/\n5akH3Dg3XCtz35RJpVlcDPrc6mlGIpYyTkUnadiR5DjXmxCeu3Mx2X/a+jge\nyCW5\r\n=idCl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d7fd8434f0a4cc140e06186c1c1349a5424f6eb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-gLQ8thVeLFRDtbqnr6vloeDmHfntM3czoclDxEbU8yh7/pjnJTmRGLN2bpR6HH2KVUMMZKradSNyWAbmUeT2NA==", "signatures": [{"sig": "MEQCIGsxO6SmbJIBTeJ5E+IgHg0H/+1wJ8dKO1YAE8gU4sLGAiA7AC5sWHuTifEOG4MGscPj28HBcxE4Uk3bkOuO9xS88g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsAQ//RMSx9R3+IWsYU7xUlrTVIqW5+r8L6LtaFUAw6jeLqAdHic8H\r\nP4t2J5tAgjUq1sAPcXjI5IXLFH2RpFLZpAtID6JIqBeumOrhKA+Ie9OwPYvQ\r\n85FYe/J1BZl48VmkOPeg+fIoXlkv9Dh6lfJGKAJxArgCG2IDOC2Zjz2aiSGS\r\nFhRpI1w5VMzW49+DWzylGLU+og9Qv0h+FrWMMMin0qIbUTaYy3CXSr/kfUbY\r\nvZESwjKdKajkDsn1MM11sR1BU+IJ15fJwsCWo1D/RZPFQMFSkwbJTsinWSZa\r\nrPVi88yH9a7pB8FJwITsS5uzm0cJfwPUg8jOsdQBaJlQrhXjW1RdUZxpobU7\r\n/Nm7PvdZRgGr01JLCCAXpfmh73TaUOZ5NpqkSngzExh3QqY4Vh3NbAN7muMa\r\nZn9NwM/tdWcj4cdHT42tmi0lqAIEJvWDi9ViY0nYBMM7PEyM9UlydjsAbeD4\r\nS5QA4GlE1dpou3qyeKfo5reW4y5LhSlU2eQlzGoVnYtT5aGQxErQPVoybo//\r\nWdqDHLf0m+Xy+j+w4XV0vDVNZioQfQx6X6pCC0ESAiNZ8g9E7gyIxnt0lESD\r\nawBzbcq/99jm54rWIFFmyah/5Q69ms7C4oyZWI/3twU4g3Zwq68EBpnAuby8\r\ngPyRwoDA4sMPx2LW3Ty72dhOdmtcCqH3Od8=\r\n=X7tx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ab1dd3ed8ef480dfba9e93146257aa932c8fd5fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Ypji3RTWyni75inOEVlb36m+xXPcp+aZ/oDpsxVlizB4viy8WZguaBEdCqkyMQyyUVbztVETupzpkxihR3zYuA==", "signatures": [{"sig": "MEUCIFr8Ec5qugBDJGUXiSYS4YSjZn9FbEWhSxi/+36mJdfdAiEAvG5ujBZQVGfMSoVo1Og0tt780dMfGwgS5XL7j399XGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrbzg//YE4SisYN1HlqLsnceqnp6kxP+YWX0k0p6leoqPYE4pUuB7qH\r\nlrU7mFiBRt1ZW/0ahldnQAS1N3FJC9R2KQFSVoMlU3wIuiKNkZkh93qQIX+C\r\nJ1Jh1ajH9GS14eSkrBWzpvyn34VagRJwqFQI8ukHda1ykN7IUF9qQVbjFbFC\r\nPdjLJbuIl5vBmLPd6aGeVc5HOKxOqoDEx5GEQGZ8Uqd1QqigoYhjXITqKreL\r\nQhp3k4wqW+ZmMWrQI8+rKAjvrygPTX8NG6t2DY5aufB2j/sUHZK5cYzYMUaU\r\n95WhC8TcRvumoh3kfNZZ4odjecQFdFyrY47aDYL7J+ok7u346wypV1BFtW5s\r\nClfk2pyunM3mk339WAYsrMB7jpQcTqtfp0EyRXK3QaZ+RejjhgNPjN/gEReO\r\nz1pcUttB2PdDT5miVowYJTRO81b+Lqvk65qH5x9x1VWaKjXnfnx0Q2nvAteQ\r\nkydn940nkkv3g+oO9Kvy2V+Aj796bnfN5cinqLedGfL0hb9wbSRRr+82M3fz\r\n8XZPersNcDcSK/f8AIVpHUjeUFHlGoyHmk3qVtCtYtvoo6TJt3+7v567Hinc\r\nUBzFiHQkJjrFTrNcNJqiDEeJRZrYfETeXmytfiHqwShUpK5iFGk6vnStT35d\r\n+M/oiQERP/HZz5MVjSXFm+3wR5B9EWuwtbA=\r\n=Rl/0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "229d3eb2e891b268e51535861ff44bc275d478ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-lF4wUHAa7KoACz07joBTLY/dM6YFmoU++d0lsPqJvjE9eDWtPfXf77DRBUaaZ7FShNjZsb8Ft4XPjzxpEkrGeQ==", "signatures": [{"sig": "MEUCIDeSp9TNhCyVWltKcqqHHSWt/gDJQIGKKt662CK+RkNBAiEA61uy7tX5mRO3oldGehE5Tpv9P9EGBF3ucRFWRKHkbEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorBw//fxL/aFFvdlVnFPy5LeyaIGI1KDQUDBNJsxffEgs1rGJ6hxfG\r\nuW1fBWyISpjxtvIMptqOPdctlXHG1TDrGuu3KujOxll19GmzXV/VZHc1QPnj\r\n1f6o1/8sYABmTqhX+/s2grYE2284qyhP2sfh1Js4v8/QEnPUMmFW4Xsr3oDF\r\nuqJe/yVWP4FKJ9cypefuppY1wXC6XY3VqbKmNah4TDYkJIHReDvoE4xCRrBG\r\nNVTyzXbcRbAdvW+OzimBqI4rlIcl6Y9gQv9Osltl9uPDUQNA9JOV8AQp0RRS\r\nKYYQIJ8gl+Dhv650uac51/TElYltaC1fUFgyXlZtGwRmTPUyLd+56DSJal3w\r\n9RdSjG/tuMRi65x++FXOTV1IjZHiKGZ99iLiKSjQ7lo8AsfqKh24iMFGOTFe\r\nf1v2M/XxGkmkftx2P72aEJXDs6pU1uFJAZwrpETy9jT+fbHCrMpS0I/PUxrq\r\nJ6RMvFW3XD+y6tStwITunf81c7fsjl6g8xou5DNu3iNxpF1L8lA9m0BUdFpU\r\nk/tQvfRhkjeFIeU6pmLL19kHQSXTpgtUIVNPUl3T0V7djTGvEdQFSf9M4Ug/\r\nfst484cuDouKM5xL21M+DMurJoU1Obtt5yqUx5eeVsThl5+xcDHZNjilgTKh\r\nuq5BcHc5LRkCAfd+DGiCKua7VyjcVyCgVDU=\r\n=8Yvt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "43730042aafc84306648526a94cc403e9d10e72f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-kJknhViTxJWVl2X8GN+znbJmIvf0jRdus1PlxSYw2AcFhWhZfUh23JgYm+Q9BATIgtifEay0e7Fd8prVTXDUgg==", "signatures": [{"sig": "MEYCIQCHZQt2L9aBSSCD8/S2Hlx4PtZN8TN2//x5zoKyT3WXNAIhAMbSS3GcdFMCyfzulGT68V/i3m5aVimK1jngdYgb+3EV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopZRAAiyth32VpnZDqVSJ9ASeAPhG0K2/+S0z3VL6ASUgolyCUnYPE\r\nXDhYAE2Lx/zzTN+rajWqfCF9OasFPpO9yvdXLqQ7X+FFC4wgZjcPjMFJRmuD\r\nKfJ7uAODO9AblrP7Lq6Uz66ykwCO6wnH4oaT/52+SvGIe0H6NmY8ST+uRC3R\r\nDobeJu8e5+qiUOtN7aY/kfRLYDRadICg+69zNmz/00/dVrfKBIukHCGLEQvf\r\noJvfk+xgrnvSGiO4IZ45ajgezh5J5HMup6tiJXbVmJAd1liYhz8/IEkdtfbq\r\nWRY5K1KlzAuRFjer25AkrsYP4susMIu94Bpt20oi6blCjnpfVWSaguL3kJ3h\r\nwlI8OedKgJ+Kq6ZphDdXO6EcvUbIk6roSbXWQOvlzZsgT13d3n74SE7UWWJl\r\nk/kHYvYoQw47u/WIi0MJd4cnz2Pe4Rz26rUtascplWZtGOaR6yvoYGG0lHTK\r\nzwShAMkj9CaG/uUE9b34qMQZJgywLi1VdveKndcVaZ4kg4XRBmjVTy61g0Lk\r\nRFQqgR56gg9y5q31PTURIZSIRvO5602Uf5rYsSNIIC883hHTHiGS3KWsPOxC\r\n43CoCB6Ak1ax+XSEllukM55J5AS1uA7MUgwaaseX1Tni2NxStKMAE4CdkzH0\r\nMXMLp+D5zixqI+ODMHk6fklkAS3V4Qr61N8=\r\n=PDUZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0cbb34fb6552576703318d64912ba7bf4a46d955", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-3VXoRdYH18L04wDMpxGhrcar6iOQMkgBNm8BblfoQmqjbC7Z+e+AuF/u/3FIf3f3QBCuVo/MbXRzYwKWQn0/ZQ==", "signatures": [{"sig": "MEQCIDP1wcKog9j8bN/2IJJSF1kJxTp6+muPak6NILaHhKz7AiAQXdvnqcNc/PKXP4Aqmrym+krzgHMY5CdCtHGG0A1liQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpClg//fubCCXiskoDbqoM8mBvuAhJAY34tUyGgAFAiEvCPcioZe9Vv\r\nqVPI6bsgZuEHTbcwQuFFEvuC0V1u+5mNTTn/QzFXcqHJQkwWtov8J/znIxOJ\r\n3X80prvLS81oz8Di9ncIDpf08rIms7DqjD8PsAylWDQ9/tg41A2aRF/LVjIu\r\n3zUxH/bpPM1pUQPdnDktKiYsuqlVB90w3uiy8gKidfxPk4TNdygPHMxGbM4l\r\nJKosaB9grBkhmcqsOFVM81LHYb3XiQrlAaGK1gXZMOcL19Y+K8amOzp4Jrpz\r\nF4D86wf+4ljfKtbLVjA/hcKp/ZNdqkj7uEQcEiF23EW+4CDgNQLKSo1tKukA\r\nj+8QIu6pbxGoYken/n8c5/FjjyknlxNfsT7XBzGSoqPkfPTs28CmOW1Ie6Zf\r\nIDfyUOnah0FKvLRTZ4110q/3wxosbrUMfAUGQMiscy8vbDOL1QFUABbzGN0M\r\nkQe8ciccw7Dqm6KtX5k3m93MLyIKfb+5V2H4Onth7yO8ifReK0utJaZCMybS\r\nTKfEKT2CkLdyGCx23cNTOTTmemButp1IL6iy9lx+P/GIjQNNe52N01Y6LKp1\r\nHNKrMrEHKcs46CEaEjmj+NqDC3L2tZSvxEA0I6fvDGEFSulq2W+2/LukfqEy\r\ntHyoITUSXJX8zLRuVIFtEDC7F3Il5eMliFY=\r\n=twZT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b38bf1212ceab64f21b3f6f3edfc824f97643f19", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-ZwnovMfTN98F7VZXPYCSAyFV+2b11DsZ7D5ytR3jfaF0V0HyOf5kaytZPUN34Fji02mgE1SZnJgqnLDWo8bmyA==", "signatures": [{"sig": "MEYCIQDn7qqChcpVroytsDyzzWdNLeLlYJqe3GuicNnrXGq+LQIhALl0Wws5OiqxGB9IQ1Q+HmZ5J5eKLozQw44/0Nxhy0TR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpEKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeiQ//QnTUi++HpMDdPBFdfjt1nIbB2FbIra+tOBnBRY/oqgec+3lq\r\ncoqx6uJl+dGT6GuFxU0xiwZZ9Jk9kOG+Vt2W2hLL7xjWddybP4oa470+rXJK\r\ndOYvtgmgcX5cN5XSvms3fDgAO6dKhOIxF9b4RfQFXRjD3dVy02/3EZ4B/4GC\r\nytZ6A02+Z3Zd60vTXQ8qyO8QSzu6z2Zgn1Ky8LEXp2BdijkFoteznl5aTNOp\r\nv4qPCA4fTlCm0zqjIVFMlbt15k6OAiAv+Ac0zxntjRTeep3QE+Oy40BJzGmx\r\nNUu43huLq4hVARapsDFUYttyIgbgpj6tyTutXvHYRbbs0eL1D1kUNYUlSQwM\r\nJjAKyl8sushqKk+ewGqdOBahH76ruWva2KTw41ObRh9aH267O8rVl038jiEr\r\nD5aP6EqvwirQfdd6ZJT8hK3nGxirtVC5WKImMvHQGOcaKeIHnw0AFWXs/gwm\r\nKbJ/ArHVZbm++UccCNbSadfxWSVaskUBRy+pJ0ce5rOOnyhz9FHw6Gd1vJZ1\r\nB3FsmNxtprKK81lTK6l+KlHARGXlZQQ+xejI4QxO/frQuQlLi2YBxG3o70rx\r\naeFWJpV341TFDfjLGlNL3zs3yI3qay6WgM+Xbgl9Cpxql3su9sqFNtXbxIiC\r\nhJE8zX1MHcVf34OiZSjxpE0yOdd5luO8WU4=\r\n=H0Bs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "792edf88770115bc7faef702343799c13675cb62", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-4lh3JXdjlfp8OPd7PdzDWu1+jQ4a3D2+dlF/yqGNIhg2hDMQe4iliRxxPuBqYiKsjVFwSrHs5CzB8FpcD2X7DQ==", "signatures": [{"sig": "MEUCIAnlHd1mBq2aNOlCJlRNTnpJA0q9TKP1K920drkDt85MAiEA5akxDsZi6/0VZRElC//5qlbG+8jprdXBdL986XdZt7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRvBAAomHWUcX0qd7PNAGuXhHXMfZacht7y7Ia4JLBH7R0byM8cCvQ\r\n2t22CdvD3Dd9I2bIIWe97ddfnML2k5sTb3tvxwG48d4NeinL0BSjCjuFWO2m\r\nNMhGKntrQ+CD3Zygvq1+rGJzADmYay7nWMCNecqUVqAXDsPX1fjp2m1AgkLp\r\nrhwkVfEGQ9xyr54UCCYX8gXIdpBA6ArLj4DV3fskPeTWu2+JMhDr9iPdZK4t\r\nNvZ+0fiFV3q1pHLWd3cIe8lfNG2/bx/ugvXI2roT8QjQabpSZwxqgtt6KZ2o\r\nWztAce+UUlBTqLfAcpIeujNAExQSkcLl1Yw8V1Hf68ZT1FvXNhYM+BQ+fJIr\r\nhVByG7dSIDmgdbICI73L8QaXWPa7wAfrESkrO+BNGx/eXJ8rzRrXaPyx4Trl\r\nA3opNkvOtQ10cTYtrPWhZOOTUAUcOW8/6XrDO3vVa2mPhRXVWytBg8Z0UPXf\r\nrrjzgmKWNhmXnPHvIpn8UJw7v7n6CT9Auy+Mu9ZffQW9ouI0orF879TbxJxV\r\nFQuCpfR7etL8U2driexU3Wz+dEx630YE+kG/uIlfrp7SxW9x7DrUGyzxWZzk\r\nb3bqPT3MgjCYEA2EkAUqaIyVDUMon/7NwE/azP4ZWEW7Kt8iIeiP7lKWRfIx\r\nxWYWZInPrN6ASf/HULOjv31XeUoNSgE1/2c=\r\n=qwio\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9bef6cc58f6a7dbc664dfdb25e70e89d84c6ca21", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-6TxRnHvAvVe+z2O3zseTquM2OalGXJiE96i1P+eefzr9Acu5kbqBZah6vdtAbc1rSll5EgsddWe44clUktSLhw==", "signatures": [{"sig": "MEQCIFKkzMizdzdLvQNMkuWuvCEBuK2NrXnEQ3n9q4Bju2ogAiAh7YHyhZWpDcJfbsI8BdWSM5nwN8KsPGePWt4aRN6LEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFEg//e95PTnScJBV07ctk8HRXFlCL4vtkKgzRT5imV8yAoOKWyjBO\r\nVob6ZFaNQVepLgrX5obvEAwIxyr4CVAbQCsWQcrdilGicDEMtxQ0cX7oz6JD\r\nkxZ5XrExhykQq+D1oyjkq3K56xT3XcgVvYjTl9WyZyaPzV5T/TcEfMSdj1SF\r\nbRNMghWfUfTJM4tXkKkhqCv2Vptea/eRO2+n6Ffyl25BYwxWaYNXyQJf6AUe\r\n8WXI4dxhJd+I/UIfsv5VMzhtsDU+lGyrHF/euI+J9vUxwxpWgJMkKgey0Wov\r\nEbrZJ3ehXVHR8Vx7Q2X0pk3kMrhX1jxU7JXsDcWvdwdqbqP3Up7Ga6+DemSL\r\nVpbvtBW57c0xkmtUiehWg5XqVYqXkQ7B988aoEDPu7PjrfEAuYXlToD21/Mt\r\n4hUgCsplwki/26s9RDQYRn0Iuv/1ajFW5bLAVa+BGNeViSY6EArXHmBT8CuS\r\nFYwFFcJmgRLIByfuLqDAUS7oejHEvWda7WG1MgccdeMQ9j5iFXklpg0MmQAu\r\n1aTOXB3yUbA3I771GoDDNJ3Iabsp0v073T+YbUuY53Auc++GWxLMFP8JvIbM\r\n97gSCRHrbPIXAy9xcF5vY75AghyVYCSZNgIxCCunE0fM53IR+8VFbBoe/k3j\r\neKpNVGxgUIMpN8WwOgCtJNjHNOIaO6wcTSs=\r\n=fl5p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-toggle", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c75880ba66a8b1786a4c47da0e3e9ecfb9e02c13", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-MW9XwSFdbXihJLovPFUDSTLfpoyYomGv1E1ypgxLpsVyAMmza/8UmtoLleVcPNty0U3pFracVayWveLLl1sR4g==", "signatures": [{"sig": "MEUCIQC/roAk9bOY+q/pMrp1/z2FPJfVryGl2lDlGAwvisKIfwIgFI9mcLLR5PvrgBfpLZqmSdOFvY7Ov2B/RZWKAbCeiJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Z/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPZA/9E2M3vvqCe4GO9MccQR5wGOWySbd7ly8yFsKW1XiKYIb99IDl\r\n5JkdIDS1qYdJva8CT53ErtGxblMcm3Tb4JoY9KdTpiW0s+v11x5A7ByBmkDX\r\nbu6qu3lOiqzmpYhF4krZzxaL4KCxwJkk+TQYbsb22GXhyIUHSr7r1paGUEgO\r\nHGgWAdmdxPJtoBvntOQRlE9qgeAyxEB1hceQ6wgfgr+wepCOHrQpI+8x6vR5\r\nrIwNUToBE1c/aUL0qk9JG7TUXl5hBNItqabyiTPE9NzmYRm449RYAmZp5XCG\r\nhJ1Iaca0so+ObxPk1JzOGC56HNpVZ2n3myKLkwYC8e2NiZYSqWZOIh3017uQ\r\nooaojc50Yaiv4Tw7OiqgRGsXup22a/qmYrK0VggQtHTcuokBkhhgqXkycNOs\r\n2LRK89JNxdGnfROqEvxpgrsmxbn5VGImhqpmEzf8p2oqJjpFHji0aB9+CBt1\r\nQIjlIv6YRcDIv4JmkfbnKz7LPd9FJcCtTmhS08Hk9yjyUedUVopfDUjJ7azN\r\npN3gawpx9VqqSz+k7Dl0ufilHISaOY66QcXcaDpJwEMN37xEyM7Szz3tEVWl\r\nUA3t1yTvTA/FqBq7pktq1oggHBdjCOVdcVuxD53ExdIlfY9wPRTK+Y9ehfsb\r\n9CGtbq3bF3VKQtp4f6gmn+LISxyDDBudCf4=\r\n=r3Fi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-toggle", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c5c63f7cc5a03556bb58e0a763735b41bb0331f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-gxUq6NgMc4ChV8VJnwdYqueeoblspwXHAexYo+jM9N2hFLbI1C587jLjdTHzIcUa9q68Xaw4jtiImWDOokEhRw==", "signatures": [{"sig": "MEUCIQCNJcAM/sVZE+w63CAiuOzrbS+yhxJ2yFJjKN5xv3wRPQIgGNSjZ3GE/1SDfutrfZ1ZdvYnMm/yi/h9Chrj/+XgoT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8keACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3OA/+J74NOR+oTZcL3wizrBbSjevYg93A9hSb4HEhbMfcq7rrE3/2\r\nWtcSG+8ZPlS96e72XP2stckH0p84Q/E6KD0WZCrvMz5U4iKGRjnLp+4KFheM\r\nsSWUqyX/zdy55TEqkFe92nccDJv7qdXDqxcucNI027p0jmcmskAhuT1ZzXPV\r\nGeBbF2eu9YpPZM9UQIR5gxq16pBNgEwgxj9k72vC/LOAa9xA14mAd8IT21Sg\r\no1uIp1z+6qOMenXlwKPB+f2m7Mxc96IzwL6ZyNQvSRNSnmGZ9XtZV+NMuwqB\r\nmI3Uaa5ia5n5iRUIYUULHqktPOyoFGoV5CaIAkVYoHxNtYyi4zTNhqYn/k+h\r\nmIC5FGqIeHLKWc4diLQ1t7Y7FZrcwv/LYmSvhzQ0PpgYGpD9IBY6B56aDVtG\r\neyXY1IHpGIY4it0eLw+026ckw/9x1Hpx25G8Pjpya4qgiCfopPtTvfPM8MJM\r\nknNC31J6MExJIAYr4WGZ20v5z6CcGD9Srs7db9TP1FoY2USNaO5uW864r0HX\r\nOqXredVJALd1HTgk7rquw6+eBzJexZ/mHXn8TOlmMFvXyqCmJdQvQmUGnFRI\r\na7H5rilsyKEODMIB/QRpngC4dyb+A10/hqRk5kv2bQHmFo9+rDvs533IqiHq\r\noZf6JTIU1V8pQnU7wQpb3D4F9SXLoddos+Y=\r\n=HSXi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cb265c2dc5bcac295fe728842b5b877e9bb693ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-T9mu3z0k6ToMChSsFA+yFEjdLUHO07JwuUBGtiVY3DfdrNYY2YrN5GXtyiwB0ql7Y4Bsa15s4X9+ElrwDh7H9w==", "signatures": [{"sig": "MEUCIQCM0lvKIWlbZjDk3wOiZniTLlz+C4Sltd3qPuo2RJqxpwIgIo7KEsMTHRO+C0Qe64rZPN8SgTc4xM2rKtOKo1NWbL0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAR/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpuhAAiWNy5wO9diUp3YmEwfNrOBhfi0h1XccraoNRgdeKnMvZZc0z\r\nCU/6sUdblXAxi7xqwWMAHYcJ1IJoFJr/X4X0p3FgTlj8vACn1IGfpCVR1Wwn\r\n0Tit1/GX/q0TSz1oZ3rxGiIAktctGSXBe1+6UQcptjCT5+DgFzq3FYXYnb+W\r\nvKqFziNYkxTg6vQP9GQGTmf2LGAdrPHAA4jgGlZRBgfteoZcn3CkTtnufh0P\r\n+/QFKVSxETS69gMTh5b7syaaGklqzdiIQTBPbm6J5QILb8vwolB4JdiwN8fq\r\nhqP+FGEDewQR0M0BnuU1ZMafz1ArN6h/n0FLnEh7uWZs3oP4bJKhL/xmqjbx\r\nYJPF1WylnK6ng6LovF2ZvAHzCGcR6yasvSfsPG+kZyFp82O/YtlpviX9fLvq\r\nDFgntNZpdK9ixRO7mZN/xyhXqOrJOEllGaQB1Ez6DlUBl8YyqqUcsVSWHf5S\r\n8qhqmsQwroRLlyQdcsp4+I0rQeobdvAbunDa+i2vVc9JpEDv+qmrMI4+X6qq\r\nz/wJdpaAO/njKxnBxWpy/WZQrczFVL64J0njJPIUibm9MlPfsHVTckHsNa0s\r\nciTSOLaPrAWfdnN7MIY8LS8mR5Phwwlc1PjNrcMxT/LcB8PLlUicoQOYIuA+\r\ngcjffO1OE/UFLR1BJ6Lf/Pl1QIcCA1VlXWk=\r\n=7h21\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b2c250480654b2b45a7c841ba958eea47ddcb81", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-uV9lEyud/i8UwWPUUo3JzwcnMfq/cR8iZr+z3hMkIwFT9ydW9jYFvHVc+MrMRTnlLZmIxiXgd/6cV7gmCYs/Nw==", "signatures": [{"sig": "MEQCIG9X8M915wANRVKXzYQCtIPkXsr6u3g1ibGa+t7/Ol43AiAlepITILe9lDmJ78ICDKeCTiPJcKFq2Xphb2rRRoN8gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11615, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4Ng/+JZ/0X3ZqlhEfxBhG5ze5yUH+r/L34sfNn0i83FUsVuVZvVA/\r\nuX8GI/lO31yYW3/V5qEH2HKKW/8bveCd2x/4PDCd729oB6mRlGl1XhzqkCVB\r\n3OWx9GVHUxxIs6XwHznD0NssVZ+WPvD/X1IsHKkWXX618kM8K40xEIrgrtLB\r\nJMFs8v7OEtRAgTG469h9x2A3De5d9dxJF9BVasDAK+wqZJ8xHciWEruv0kTl\r\nSNJMleK7QubkEeKVBo7+7sbfCixuM6rY+hOjm5l9pvD6+LYlZkboD5u6yIlw\r\nC6aV+7ilESVR0pYdjSi+RUE0zbSwPYE73eJhB3XWyeVQlmySxWjP4k08mSdo\r\n/ZfLulPjtN9WfaOwHyszT2sbpbCuayvqVqysKzCjOULURJcNBNCsV2nXA0Fy\r\nkPCiswqUfYnsSb0uu2dVeM2lHGJkqAskXQw1dRx7HgTWAdGylc8lkRAcNr9P\r\n+NSof31vd8ZXKazSt+1eVsvSt3JrlPmzebqfJHXfPFnBDgBfWA3QcHHQwZDB\r\nG4vA6ORM2Cmnd8PvKtlHo9dho9ANXz+dGkmjWevCeMY0r3BWkeP3P/NV9Jdo\r\nRL1U0HPHfcWvcIsNOTIQk3v/RrpmUQODrru4ziLTIBT63GfHgnpLqBmAG7ps\r\n0PF+uUCzc9Jul8iF8/ugJu4plEoGN7V7JBI=\r\n=mjhH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f07a077782ccf50c185b2554ba4c343b50548d74", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-RucuhUGB4p73id76LEsgnDgPIpCC8OMG0s/oH+j/d759J7lfwsBXquhfMAQZyVgLhg8jhERzI8hBpWS9Rpg2ZQ==", "signatures": [{"sig": "MEUCIQDvNrNdXyKQIiHSeSXT5gzZzFbX5BSpxgXOu37Bf7e0JQIgUkY7VMp/lldzXjO8nsHRL+3HsPtpgPgopH3aZR5iETk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjZQ//SDCo4LM7z0ZYeJo+aTDLcFEf4TV0WYhKyWYbbZIUOd9KEyMz\r\nHPUVOu/7LR6YievlumGM++sP99EQz4ZUZKcqcEzLQhUQx6mxUqDDlxg4T03b\r\nnJVOqSfwCN71lwjtShuxvWEMvafn9MQtTA+fj1PlAgfXVpLUoSUYF1RaRGpV\r\nO9YYvHaxGYrHQjV0W2YNogCVjDCgocxKvKIl8UcMEA4e29xlqodKA4mijc30\r\nFVbDbC20vm4+/csETY2cGvGN3JmaqfzmijEusO3GofyK+XVgCvTXmNHPXNZ8\r\nvVmhf//Uv4PAIXHcHQyZsm/aBoksytsNTaolSBGLYO0sU6P5s7KIKp5snK9c\r\nB1IdUeZpxjLehvmY8LNebO8KwoCvgbaIV9RUVMACi9LisUh/kj1XNn/PoZoC\r\nHT8KZiUMJFkt1lNy8d49/VufLXP4aMg0kYeZhyAzpwuRsO72MY6p3uW/226U\r\nZjvmw1bQGoCsS89umJYg6BFTNm9Lt46NEquemKmDeT0EF2ym/ryXE44MJ4Dq\r\nPaR11bDrPZ/5N68VW15xTucUJvWS/NyLOlGR7k4cI3dqXUtlA5D8XFYQRipO\r\nphZ/fDuIN1lDF4ANXjzq47yJPF0Eq/xAquzLzrXBghT9t4IkDdf6zvZCiTsY\r\ntX3KOaZ71/KZ3vXd4qEANl/zyVPhp+IP7Q4=\r\n=4yiJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f6c2ca1aa5c91bc1246ade8f40ee58286cb1a2c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-5ZnQYZH2QVr6WE7f+XZ83YQeTIyeHo2GkQLHoHzMPIL04gGYTTVope170jDF2Jq8u0dmneFUuUlpR+y6ElnLIA==", "signatures": [{"sig": "MEUCIQCUjQVJIFW0lgMfiI/rHAkqj8InBdf9gnitILDZcyOgQwIgCcXrz4VCb2GI2R5GdSWsbrJ9SA3HAVWWwqi2hNQW/ow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGpA//VULwd3Gu8J2hj2evAihRutyLgTcG6yqqt8jBrsHaRtFClzsF\r\nnvb00FM26WWFOPKjes5I0XrAqgYgA2eLocwbjvNgKw8gc5R1jTYAvqOEjUPz\r\ncTtf0rWZvYqhNFQb8LVZZ/f1AswntylHwERSSlaSa7oHJ12B3VRUwTx+QER0\r\nxM+lXE+eYDv6uNXFbj+Z4syS75cUCox5eKHu8k8hoatUMVf0nZh3f2QUaUgm\r\nb2JjTzihBDemGFqlJaQsCtLO7+a9OnTRdGurhOVFhffSPB0ktMdvW/0czWlv\r\nH59CxLs/OCuKa4oMkcRQrgJe13vnsgeZvvzKSsfkvghunckNYCzINj5peimZ\r\nwwlS1xIXee40rQSSs/bPQt1hKDEsSdLI9wWPfqPp0Waz/yChfaTQfYJDknTK\r\nxRmFtk5RnRc+R4IdF/FG/stspeUZuwzGZa+WSKfQGLWz7aLBhWrBIUc9HVFy\r\n0egduR38CXIG7hHjiOC2DVGQLzKD9p+Lr4I5Fn6wyzj6ExXnSXVaKYaqt8Pu\r\n+izyBQquaAMKPunXvQiQsxLIbTHURC7mtzauam6TTjXBVay6AQzdiOlsb/S3\r\ncbkrGDmlM/AHXl9zJOVwC6gM06lVQ/QDggqQ2RBCv1cuCbz1AzdUbVtT0YE+\r\n14YtPvY+OmMaDEhDRmnmAjlvGmmZW609Cc0=\r\n=zdYf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "19d8ee801413d4c55fc85efbad1174cbb0808099", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-7WHFtbTh7/8hxY3vDJz6vq5vAIk/kmg0FVk42cUaMYcVIcfl3wmE1cnKhhgk8xlBnD7XCSIcxnq85uuSbwhocA==", "signatures": [{"sig": "MEQCIGtVvXrA/mYsKaY1yCUheCiA8AKcKmic6B+BZO2Ac5w+AiBI0yKxhbFvaMZARQZX1WN4vqINfblJnAxERQlm/HQzzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaphCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrI3w//blYa2TywW6QEJbzmMtJ0eDXfslTyF46w0DP2hXvkgEeLKzCD\r\nSmZ0CuTS1TjFAfztmB38scXRAIj/dCEFx1YMxfhIZAUjmBxk7azgK3kHEbcl\r\nqkVSrwRkK9P4UiWMY/ejTE29mpigBFLPZ/IJyvde4AXryu13ov4ciUqj4+EV\r\nrgHunH282gpeScvlTyYJ8/IEctmGxKENIEN9jEk1hY4hFQ/k/KftxWGrOmRH\r\nrrRcN3ToRvhwnrlbcnE++kBo4J517hW3uSqtSdFJTKly6WugThv9K0akIJCf\r\nV5ZN/7Y34qU+4MWEwhC1SlyPooHX419p2f5IZ3BYwT/E3zkfDpFWb1pCZNuC\r\nSg/kNXtfvgxfGaL1sCN4Bu/hWNK6IIzucRiZvTkL5U6rlmw4GQNRFOXuX6xR\r\ngRH4f0gXf6SsB2yIXIBatlvy0RACx05W4O/con1qFPZiYDDwGo5kuEAQTQbq\r\n0/4KGS6zjsCihklxKEaEzpOp6EU2BygEUA2VzDg+LLqv/McxaTPdc76u8aZ9\r\nVDOJnioooA0lbWUC/OZQv0CBp8UzijbJH9hgcV1ZMurn4N8ZHGUEbRMRMtXr\r\nNyR7rfUl5J+C18XQy3ckNvd4BuLSbg5PH7cQuyPvWm9Ef2Afk3ncEl7KIbUt\r\nh04xKT19upr4BzNqOlTQoC2uc8fW6hQdG0g=\r\n=akhN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7143129e1742beacaae8a52be06b8cdec3ca31e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-nAJ6dSLqdDFU/ygIURjuM0VI7bY+1nhpYwRyWZoyyu2YafA3FIGP6t5L1jdJTYSTOUbITsYmlC1K8ZcOCXvtbg==", "signatures": [{"sig": "MEUCIQC2puOc+Y7hWmdiGf6GaxetAJlkaX48mtmCMn7jBDmVGwIgY2vNkkWLfeomENlQszbrvEgzT8+is2MVlcoTtAeOc3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn0Q/+M9oOEXVfqfCO79v+Vj+ee2r9o4MAR7ohHWZb+ZPl+05rjilU\r\nP76m0CuvAvAo9B5wsYi0R1MdZ9fRI7/Guk1JSpV91M0qA+7N5MeS6IKhdVOk\r\n9bNiqSn9pFO7hvzEhW4s/DCqTCo1cPShU0i2q6hNbWIQ6yZn4Ft3AlLIWDGR\r\n2noCnFWkKGioVDKukRnG0pJS6rNUvnRyjoJ5JWKOmXBKOsJ0jRmsXLS588hW\r\nzImH8niuThboxardDVxGrqsNGIcV7R061XICMjWE8XFeNFW+CzvB1ZTHiyYO\r\nz/WwBUSOh0uegrAureDD66z5fOv9Xh6TxyWkykfEcbEOV8i5xIXAn6gHoFCg\r\nRBQLsCg1FUHKARWPBi/DXPNEGNFRP+eyOCc0nUj08PmL5snFRBxPw18DAl2P\r\nMNzmz6F/BHj6RzuaNTW8xT0brZhfTLGR8QY45US+F1P1NCIW2xgyibXqN01C\r\n4ZpxF5TYovVUJuYSy38pBg86ladM8E1Gys3Jz9R1b5A6oIs0TrB5JwbzgEPj\r\n4MFh/0xTpz8b7LkZVzqSQnam4ZLRrmQTc544k8S+llGxTjoewI3De/9H8ZBo\r\nQKSv4COh0e10yhSt7cZBymFLyzkKt2Rfim35XjvsSmlm1J+E2NMf7WaHPi3s\r\noc7JYnLpJssYmt0hxLrRSHKIpILAjGuSsGs=\r\n=Pozl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ba0179827a32970b2a3e4aedf97b0933fd353dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-nHCVWtA/lO4Q2bzVvN8eZeGtHuMUUL6jwF6MFUFcuT7QTr09hJuOxB+rvNzrUQ5MOPSQlF/SmCFVXQJyxulf3Q==", "signatures": [{"sig": "MEQCIG4lJCS8peRLDCJFgxHMHITVWbC/Q78t6HWeHwWWK/rcAiBAbSpXNgz9OerKHEPd0W03GK8DamRIcHU/SkXudGqH+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRrA/+NeHqJuPZdZSROMT/+BweAkV0ONLommNVCrFzwEOBd7MgnPWX\r\nbrq5P4cZzYFL/jheO2W9dVX6DfsYlB2S60LuSX0tJnDqIjPW4V3mQnXeTLyN\r\nzF6ygTyhWknQ+sD53pndw7wzu9j7pVa8gfJqL/eIXePTP6ltAJa5w25RfcgU\r\nIMvnX15q9KCkN6tpMUlDCaIn66d4pJgVrcCd6c0LVbBf1sVwddHkz5TAX2o4\r\nJtLcLlHR3p8JvDT3EUZubwPC3dP5tmZHcvDORxsJkxu2sf/qri85aMGrOEms\r\nVlrLS41jxKF1MXOBl8RXx355FMPHy+KHStA7JQ+ysd2SDCb7u2coGtPOxs4d\r\n+OWD6T1ExbpSR1eQQmOXpLzaaHQmDgdMbJ+RCGPVxPdqBf7IrMJCOAlb9XFn\r\nKJmw73ysYvTkkN5kmZl/otF8EpRp1MQ8/yoohFzVNY0/Hi4PIqKU5HD0Y2v0\r\nkN+CVHA9d2dXWfodFR1k8nRgNhEXHiphg4J3cw0CLZzAT5BvLdtix6HTo1gz\r\nFX6ZkDMAiVSX9aNx08z2gKPOpoRRBpMrAwm+4VTty4/FFtIwmIjDDr/RW+Jq\r\naz0tRWFE6UafM9wPkPYzZi0po9D0+LWs7EtOelTZfjCZ1162l+1bQshudBs7\r\nEskDkUqu/YfGsomP+0ZeMK1h4Z29e4LWJb4=\r\n=uAqh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0323f5b4e6451262e0d215857def4803778e7027", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-HZJW+LSNqPX8VL5Ctpo+O7cCSma8qM8d+oigJFNxohondgam9YLngE61YHnJ6umRpAQwGaFYbziBTdTqLoo2mg==", "signatures": [{"sig": "MEQCIBuIgCgMO76bYIrYR5aiWz+ox/+volFG3LbpJwHq725TAiAWRueLKH3qxkijL4uWSmbAgVPciz+JQB88RwtGi0nS+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVikACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrofRAAnchl5MrMIJm542Dz6XTjvHmV8vxgQCi5uWmnGuyFUWCcKmKw\r\ntv7PekJJI9DKJSly+NQXwHp4U0umQPmyakr3taFk0OEVY3SwRg9xPVuz9/MG\r\ncneIqez3iI/v1nKFdDwWwcNdltmX8SS35vOTJpYvRMzUa3pku9JJ4Nq17+ib\r\ntfMo4D/UcoojulPQKpMuQ23XuhTfppVWo90OqvkBjjvSN+RO+uTFQ/vUnXzm\r\nWaz6Jzr8cvNHTxGgotv2gz44Xl27JNb7/5jkrErlOb+yiM5xEnlDkCncPNcM\r\nMH6TgSA8zC+LZzZ0K3oHteOyGlNCXivQOPyDbJArCdYM0ApGOOGDKrrplKu/\r\nVW4sBJ18JHn8bld/qacIBP9uSCt6UI1UWEeeE/fli+S+ocfVPxWPJ/toHV50\r\n/e1WyuIhrwBXRfK/eNBtBSRrGbqXZtE0PM/e1MRmPOI8puVL7e/McICxmIUD\r\nQ+uctEPLrcTOvFduR2mLG2ti6H1jV8cmlLlzOzHmofUMEsjc9YUtrQ92lkNo\r\nhJa8qzWoOg8cY25XpNmU2qcW0O2BtMlRLPOR0w/sxx7cDWAkje4IkmHAVfUF\r\nWEw68J+a4KQaqIxRt6lE/WArrhZF7goSbAby4INT+l1x209nhy8ep7zCuF88\r\nGk9yFtXzROyXvR+7vnBmwtFte8st2paBakg=\r\n=nf5j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ed9ee90c12212a4b1a11e9da635561aefa048a4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-AeHfJsRxm5XOiUjhpfh+Jwp+s+TvpbMGPShurp1TaHN60oD/Xan9HCNJvVI1XrYN6+/A+Gp4hWwb92XrQHniDA==", "signatures": [{"sig": "MEQCIFYmejfYBVQaeMsGQl8b5lE2W3NfOyQR6chZZB8YPXv/AiBfPcDoIUd8KhhpssNQV2+ctY0AV2O4Vm3vlhnituKTmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/txAAnfVY18DjRdGAR+ALlG0yFNAx1qV/P4HULMTmLEPJwRiC2kqi\r\n2142fj7jIaGD3QqeWqq5c+GJhU8mz6vuZyjkkH6nqnlnOLq3jS0+GZ0IZbcB\r\nQPmqMkqjMKdtQzixYA8YppVBZjULxzUmmK6qs47OvM8cWC+N9kOX2gX6Cra8\r\npD/dV3LaJ2/4okvUR87ON6W8e5DvdNgzsMhp5f7RbM3a+IdbS5jO28BJVz5M\r\nhlF1FivET5ZxtZu4h0Ual/6LF6t0f8JvZFizZrWocDfdaLy/bq16KU1SrqwT\r\nk0PnUFAzAJORPihNG1iniOTeFx8ohNvUvDo9kAV6/hcsIJF6L/6S4+fTTF1E\r\nXNinAh7k83bBiOJpt9zS0xQ20RkGLlDdrIcUXI848sSEf87StPN9wurIbrtI\r\nkdPaXKVN691SP3syEXPPDyBRT2BbPuWKXjDscSNSR4nO5xcSd3NO4b15AO0V\r\nokATYZcPgMspQ8ZJ8sT3BlclYWOyCY8VkowlztVKB/fb57SrmfmSdPBh2s1b\r\nSrJspIKWkNagYOpJNKOuRCiN/9Ks8D/0CXM7ksE01U91qfa4ZXfPoEM/nbF4\r\nTAW3BGvIs+WOwjIxlv6axHO5WD2WVU2Kcw22kQaRpOC3zy5ALVbbzc3eTNXu\r\nrh1xkEE/CVc7n7asJwPUm3VIR3lX+a8z/Eg=\r\n=hr6c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "759b5cba240b0bb546b9f87bbd43e5998967587a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-oRgJMviwpoEO35XzMYw6SAlGe0j6HoEncttfhCaQ7xJvVf9AnPuLP/i3hM+ocxBoroKbNnBhzCAMsAbGw4Hi5Q==", "signatures": [{"sig": "MEQCIDTYWyIIcfWDw0J9TZMZYfCrDUI7HFpZNMSvdnVMev+SAiA076Jg40ziFSK7OhVVECGlUge88fuPVwuNsZgNXmiHow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN/BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUsg/+J5iysaUyDAYgR20sS23gjWV31zZRg5bbvtEOyMNvU8N1bHJk\r\nSuDq/lYpHGPtyLB6sgV5DAYP+87NU2rTbt3+JNwUme4YCP6Y0SRmz3RJ3Yww\r\nP46MbOVYH5x3+vH7YEBEWUr7ZVfQHOyMu8iR2PdWT4YHkDLJUVs8/hugx3c/\r\nJd//6eJiD62DQgN9kYOXTG4yei2yx46uEjvyMqvS694uBhpGYMEIJpbHzCnR\r\n/LY70kFiOsIvp8tqSXe/JGElnkzfiIDp/0TtpCRhFLua5AhZxeIpn+bqSHXG\r\np6X7dny5zxn3c6Z5IjDq5P5X6kwz2jiCsiYqUaANN9iAWJ6HPeY/xFkUAzK1\r\n+SH4iQn/44Rrm/C0YZ4p6zmoRkIBemXnJkJMVTBtAt2OCPiaJHW5vkSR0GSS\r\nyrEjQBVts8mLUmuhFRKHg5gzVZcDs0NpVGiOUIsRBfrG4RyjEtnIc7++JLgz\r\nJTpDyZaTR/10aGvc+NXEVPPAUAHfMdJhFc+udZmiygyqIiZvNevaZwzUvj+v\r\ngcXFxbaSV9Qx2RvPmS7SxGug4E+nkFHWUI9Yp0htzZxDUa5YRGGexwSLTOTh\r\nvqmD2xYQpZCO+UiTwP0If4lZTyrnVXust3UshHzY4pV2I/2QbmxqiqGNFuXU\r\nrLtmujDVhIDRcUVsq2oCPatF4Z9ip7PqlnM=\r\n=nU2Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "60da3bb84df50475944c08e145c2c6814218ac03", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-LlDZ9zOw9h1TT6BYePtFFxvfBSDZAp1iKaa+Mk1yFIlSY0nW3J5j2hknWulq+GMwjKo4Aq/WBMHV4IRIY/Y+Bg==", "signatures": [{"sig": "MEUCIQDtQ/a5cviCdv6CQIQtVOdq1V/N1NQSakI32V0ChNuqzwIgau+6AseWB07f4QYUQhqYxoURMep1jDZ1BuR9/iZuan8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmow9g//eGrLsSv77GLbI7GQSHOK18mLSROkou1gKmfysfxM71l7tWNx\r\nZrVQX+eDE5cmV5/+sgcsLZsi2RkHYyaSOS0tATfU7FKewGp/r9u2szWY8jz2\r\ng7xYk7+Na9+vo03HKd5pmwSFLOE+tGg/E7JiJYmH56qHK+0H4UDr6HXh3rkC\r\nBMlo5xPwb3lMaI4A6njK/xNCZuMxvt/4nkVDjrS5VGgitJ8U/+QEK+AJTq40\r\n/3rukVrVd5lospaaxuaw3dN8WbuewRlpRMo8g1rnX4KTWb/g1B3jPaSJG0RL\r\nA2+KghxbzxAzIG5s4tbjxkzwqAkLDLKb5VGVmB+ZgRHXQunWQ8aRz95NY5Tw\r\nCM47w1JC8d+ZgzmHWxw9dV0TThZLc53GtQdLujnD574BwvCr9IsKGtfhMJSQ\r\nWDqUtoEAt/C2P49C671LHuklZKlFXCNbxRV5lL8bHDMjHGXLg9nzpjq9wCQK\r\nv7i7uE+N3nq0b8DYHbmql9frV1/T5InBYVdY3BMmRVhEo5jCb1o/KjmnYnBG\r\nC55fXv4wR4lSB6ZCoP7rupALIx8KNa5Ne2OivnVfJ0KNSo32D2tIqTy+9rSG\r\nF4ndDueGN8z98SVvC9YpECm1a6w9E1ntguYJSQfo+1PnZOhwJ3QoeOMTFj5Y\r\nUpqWFeS7Ipjjk3JwskJANpXhCrI6njRsJiM=\r\n=0P53\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7d4ec41d6ced2541253e663647cc16161b0de87e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-yXrOPx5uiNsGGH9SjqI2epniV0RXR6+fD3lnmbnIS9F2FadrSDJzmNkl78jEH4XLlGCMMMFZgaRGjliBXk0nPQ==", "signatures": [{"sig": "MEUCIDBzNsSHMXt3gaVCkVOtroOL3axrXUUzhFSCbkQJHDbyAiEAwOEEKROaCZ8cgtj+r4gxHEnmbnp5YZ4Rfam2GwjHWZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK/A//UE5BUlms9nODRB7ZpbZDQEvEPY1dB1pW69rBtqCjlRyJjA3S\r\nnwuZ3oFCgyQWN6atjqcL4fQJ9Y8xhod6Ds8M3DPOhEZHWF4W6M11Ac/UlV9W\r\new2uqmS7mucSAqkB7JN5qcpC99mkJdsRdKjKWzsyv2RVS3dz1Npfp37FoSYI\r\n1ztI6yaSB7eZcUPMTQzWqeX/QOAIfoOwOCCDTrlD0g7brXjD6fcR09Pwiky8\r\n5W8CwASU7AY4swOa8j3Rg8a2HcvhrB04U0jOzaJgnS30TMcDoAMxeMxzoYr5\r\n4mD4nOV5iPLQ4jGJB/zuc+wTCRO+LpRHGRg4yaze8/9Zm50qcW3UDN8MQYE6\r\nwZJNi8iDvSf/aSMQiRQQzDkoo/fb9T1NCd6oJbFPenPlMbGlU3cEzLoQG4Gs\r\nvZI+8FtoxCa8vrf9oMyTUNXvpcBlVfWHuMHMEWkFrVREawGxlxcyuXINqOcB\r\nmC9wRCoRJMWXYEHNKQu7yQSyF0bFxN16okhFXz9kZV7WGtOsex6Vk40Ldxdq\r\noDf6FVzty+hEPIrKv2h/F77xHO85gKblGd4EH1KEvBfa6XTpmOiqmAAPI++C\r\nIvxYKxOWwOSzjrWNeTj8ijVWUeZ0iwPQFK3ciWuS8UTASBOU8B9K88kVfs1n\r\nFjYp8s7uX8kupHFwI70wlGyksh4Mw7cORkI=\r\n=L8ro\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "def25397d5176f5a32ecb17f4edcd7b70750f746", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-oegNeDaV28nP30Nzv+D15X5qEcGpilgL/Eeq86DF4jTq3mSiaLjRGj7z3kQgIB6p8emaJX3JdLz5gsfic4hkNA==", "signatures": [{"sig": "MEQCIARJ1MO0tXELWb15OU5ObQzKJwJelhkvmB+X4Z/CBRN/AiBHANs49MQRBaLqE2TF0QGq+6Ic3zN/99DY2/BAY+HEog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJ4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQrw/8CR458yzJ8VMADpoiX8B6/qJ5TBErRN6sDytit7tcRMOPADv3\r\nqzd06jIgmTD2v+R4E6XhnFhFIUbKDRREsfVJhhAWQnexYa5gZkSHIxw92Uhd\r\nIeC/BIjf/9mWT7zN08GWMv4qhBRTzdYY/OUZDdpawIjsQ8Hjqx8RlT4zhYEf\r\nIOWcz2XcZMgCcyjac+fZLWNka3Iu0ai3YJKVOtbzFJAN2gS64eIOOrZFqFDS\r\ntE5xaFDjnBb1pHM4FenojzrXJGsO8pRdmQPxts8YNjafx7k1nTinmaiBA+FO\r\nK/FqSFr6siOWij5v7aToZRRw/ybNvfPxBPOHnvx5lmRYVCHtOzbrjLl/1cTE\r\nvViMUXZgsQWpjjOW0tj2l/oQVzHq7u3z2FCTG3nySf/AIOgJKZjwadkk1/Ry\r\nXDxYUAu+7g3nPH0/6Bnqxl4frGvbWAhlCLMJJ9kHQG4KXkllGg8X4W7anASp\r\nMhBUOAoidI04ckItWoPnw9T7ZRE4iEWd6n3EPNAPiNxwn5yuVNlR0QLu8C1A\r\nmMExHMS4BI30fbSFIyGHRiIWF4osL4YFLWV9COroJVA3suI+nna+XKiOhuPa\r\nLbhsCfNf3mOTPYT2c+MslEP6g9UgsrPAqpHLVvfJdJsftS183MAruzLykfec\r\n/YWVN/jHlgPyIfms3yPyFrEtnXXIKoLAgAA=\r\n=1sBK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dbc6d80414377d193380882d5940aca0e827ba0f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-Wn+OeiiTXGjnWJxud2/cgHeFItkahTQYORp4K3QhnT9fROLq1VIIeATDtSqz1brSnLsVxjkrtVZI3OhglLt7TQ==", "signatures": [{"sig": "MEUCIQC5VEhPk9tq4jldRD+DmggE7I+/rJThFEENmbd4bjrPKAIgbH6yjt/yNP+nHhCiHsGK4/v0HqE8KwOzfZPhQecpM/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3xg//agg2CGNy/iKyI+Wh0IBr8N9vv0QlL9TgDQXDkiQevZgeQedd\r\nYzg8aBNRRBTiRSoPyf2fsv57sbvrbmFydoaYppctEZlfYxLu5CB7EWq96H0V\r\nro9qG9FJMPAfh22DEh+x5jKeQw/0YRdBBKYmTgc0rR63UJnxPQmpEDahwNWo\r\nTOaE7Oa9Mi6BXyFKMZtt2qeIhSudFyOz1mt5ox50X/c/1VPFKL+Iwxpxi+gi\r\nXOsMBsZGrgR4PqpnHsEgwoMkhHX1j/AIXRQSkhvd4JLG3csU1sDHjoHDrUeB\r\n1THuW1B3u6ClCsAbVE2uzeAF0epu2En2roGCCTHZwln5EZmqB0lfgZDcNdpZ\r\nptamwW/JAUnu380XbcPWJhwwFcDRzuG0K+vQpz+JNjztD6g6lJr5mujOLEY5\r\n6d9clZ81Qr6Rryy9nGFFgXzh91bFcdlfsomoCmNxmYadLUuBV9jyDWoblSgu\r\ndFdRAOP8VFJQBWznh2virvy64odziQP642aMLng4by27wX7Ati/h+N4NmbXJ\r\nWoGrVR05IZwtsj4u43fqFnz1mAjIeKffEmcumCR8+yChWQHzamPgBtNqxlCD\r\nmHFB4yh2vUPaSHadsK+78G19bfXC1fpiTEY2E9G9uAtuStoaLis6X89IMOJN\r\nrVpGFpSwTEt0QWraio3MsOrPGlmSMbs1j7g=\r\n=Qyzl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7d290b386b0610c5dc14d60d8de18d03bd1fcf36", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-kYnEyzD40K/mbtJugfSc7xBuZwmM5D0C8uAzZ4Dmc9QOoxNSM0c4J5DNEkQydK9M3FoxQzfIyknO4LeHvQQJLg==", "signatures": [{"sig": "MEYCIQC0lDcDcEsr948hb/DGSLISHLMnh+JdWyXt0f+ds7NKEwIhAO+PWK8QsLyQ6Xk8MBjZYlpecfhYK/8yPG69NH5slL23", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA07ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7cQ/+O8MTBt8fSzdNZ9vfUz9TeQ5Wz1lyrh0LVedsza7ooRQ51dtA\r\nJHA9F0eUhA3zsSyycfLFifg9YeutGvjlyh9T1bN8bZpV+V/GrMDXfEApydGp\r\nUUczB77vbUcwpu3bDZY9qy5IAPXfk13RreL8dHWWw39ZcAnUzKdEWN5NMcQR\r\n3u1V1Ul6RTRVpNoUrZ5c1ZikFx98s9BPUhS3Yg36lx2XV8cpxunf+9XM2UvO\r\nE4RSIlRxIwKd+qAh5I/+awc/H4Fp19z7A82T61J8aqhVUmDPuGC3ch1lioCf\r\nrTzVklVwutGz5v47vZApzZe2ca4SpIE1YEtHdvcnqad4Nwjk3DiiMGC85WBI\r\n2ypRjXG2JX81V8Utvwz6708quMFo0P7IONQOt501gUiAvTF62RuLp3ty9fMi\r\ns5zUrjgDUKmoi3hiGPMNM9IZlWTVCQL9wHHZ2iebIWKyACyeJ620ZRglxuPE\r\nyaQJzhmcydXglXq5yWxndaDedTNeRhRkFjpqJF1MGUzFWFWyDl7ZuAZ0rJKO\r\nWrvVS+oLwtVbqbfazHW3U3aBkRNsc50n0UKK58uSDvvXFHm39Lg7kN+bK0cm\r\nz6w+TsJKO3xx04xNHYOdSWj0923vEHPOcsQpM79hhh2feGI3u2aTqR6+PtYz\r\nTBY87YaueSBP79ZX/l2SGtAVnYk4Df5N/2M=\r\n=/eH6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7ca85ed52bb2ba8bc6b8f24b4cd59285eda2297b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-knueHKRbvT7LnZVhibaZ6BM6P63e8pwRpVxKWkw6WdEqQRpfkqOdBK6cp8l1BF1sb3isBrwdyT26eBkx9OBCow==", "signatures": [{"sig": "MEQCIHPPu2xkuM5soiloQqIAY5YawnjXYOF/TG2J63U9kuHOAiB9Z/x7us0tguGtJNiW9IPPZ6LSl73suwj0+Td2K7H+Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmRw//VJqKwJZcVyBsbtOMAS0Wxsw+ygX3MxV9jRHKr1HYTNCd/GdI\r\np6Xyhcag/3VGGsAlThsRwBUhuGr0w5Qt5rBknWZSx+XsGEHSwfyhGd0RVXvD\r\nOE/cR4RtMZvyrzEfB7Z3RrVcsXlkrwFhGje+vlXKnQ9NCEAt7yz1bJ5bxmGS\r\n/ZrqHwfRnqm/pw0lkM9xwQC+SyRLNic7GCSPJV9GMhza+ZcAxdU4eg1O8MAy\r\nqAhLs8kW9q+zP+lbAU9dAbB6lgwN9K9qkLQR9eHNMWxQ4oq6JKscOR6i8VXt\r\nKoGUm5GNPhoFvlY3I6OEk/1P9uV/P5hGJB/QoWyacfw3h7x4bBWtyKUIUy4P\r\nm1O9BXWeSBxxkic2dzgXGglcGP4fZX+gSz8dteYnpENZIx09lq7gMTiSqj1R\r\nfQMhUylM9yWrQ2mJAPNPXpSmHQ5Nn2i00f6aHs7fzadIEJrzhjOwhdGl/LRo\r\ngS4rqzWoMAXdewAzGGYG28UjYwMlBhTNR4+snfF5EP6H0idZw87Nzj7vKVRb\r\nb58a6x111DMNgUG+4UlHONmDBgijLzk0wKqrq0dfApInUJClLbEHjTTjeLpQ\r\nnCwz70GoSk+WdiKOVB20THMph+/FABNyqm+nxl3g6NoJmIAVkdaU6D3X/YLK\r\nKDmIMqxA5P9+YOpNrW6jLbMxoAsEjDYNRDA=\r\n=MuMR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc1a5d8adf63aa85f00d541f247407222a0ba5b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-9AfZUsK3+fPt7cXC0GwZrNFoqQpWXRNddyUVV8FjGI6rZaLr1NtUWz6cFcQgwaC1p/nbAv0YkkjyW0tL+f1wDg==", "signatures": [{"sig": "MEQCIFIndCzBwmnAjK8PoBtbLvRTbHPg1fBSgz1q/Yoaka6mAiBszvI+ascQLcQsqI5eApfD4UkA3A91aqvv5v10gfISZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh1DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrixhAAo6g3FAqt1qtbsncUnTOovRyiZ63ETDWtZ8Pv4/B+AeGRF7SA\r\nMD9t4ChorhiZHV1rrM2DvZK/swdlOrRMF7OAFFJ34teR/YsvYFEvM+w2awMa\r\n+eH7I6gADwnQW2LieNsAWFQxuMOLR/bTWbRabm7PslkxRATF9liP8g8XOs/7\r\nSPD+kGQIKo3eOMpzZE4OXMBE1UlWM922/ozSRRejlM8/aPzAJU5PqrvuV85j\r\nKoRRdExBNiQGYJzDzMcw6I7ck0087pEmBQ54/HTpzsvWPonowzH2luYQ0RV/\r\n4a9ZgRsfGB5C3eEIPNx+Zmpu9tQN9HxkQIWeLKoLnxzlLKYQFVQq/B8Sij/f\r\nK0x5Kl82hL31AaQx/dMd5oKjZlE5FRI1z0o/ntk+EZVzd2tUvu2wZTrgak7r\r\nGJJSHI9hViyNDqQSCa6tnOsswCFObTxkI24Y2LCF+SnztT7LBJjrsoAc4aR0\r\nRLXsdlJQinWrngILcbfAh4Nh2bEcCIMrFpDZUmckMKqlPAhX5t7WoNkKTbyG\r\nLNJE33S6qYyWndPn/aRdeTTYM4R5ipV3Ht2yIskvTomkt8SKptR2IbTpEp8V\r\nnPchjNKBJupRO+bubIDh4IRfxV8HRNeXBMnXj6TlTBr0ePNPrL/UHeYldKBs\r\nr/V649rbZbFLNPORdM6fj2DVtJxoxRGmnP0=\r\n=5Sz7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22f4060e4a5a2b1114500bb2c0c64c81d7e348cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-+5dqOvO+ZRvzEWvjTHPfMptnEv+8G8qhotYN7weDWX91BdlD6+Vg7sy0K7ekwFSLBnijOeuu25rSmH5omjbF1Q==", "signatures": [{"sig": "MEUCIC1XknK0vaFybn+ABHYYcTUvvZKdzXHu9mo4nUf/gFSlAiEA0dDFGy/COPmrVXReyJp5b5IjFDISvrCjR5E23uu6fl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoldA/+KrZ+PyTJuAq0T4a93py83QtdEDQLLSy/bAaMsIHocjjVbLRG\r\nByJMwdSiHtX0XWhCnuXSIlx6qIiLuC4V8lDXMuvHS4L5j1OiFAhFfaoD3RqQ\r\njUCOk/LcbC8m0BCjXzQ4InOhbFSHlgoTHh7HtQcU+TwSDyrkZFelX+OJL/C3\r\nv1RnfvY5VJbXYaHInpQ1ycD9+dwuXkKohCIYmcclmVad0l0h6CbKXmShhgkg\r\npDjqFo24xCRLgvS+XaC7hOHyk1dqRxqefJBwNsOe2Gd6ZzYZc5P5wm9wuqSJ\r\nt26sp1XQ5RabNP8nAPetjyEObIt8aSizfcdhz1ckcJkzfF0GPKHGhpZglUTE\r\ntE01KFBpEqQDYUtpIGOg10gDqKPdS3UivJuXy2sx5kZi0CFWqGmiAEMMQNFC\r\nFfL/CEN2pLhILjQRu/ZOSi2S5C6WRD8K3lWuWqtLXDp55Ao/0kybf/toC+7Z\r\nFxlaCBiwzyEeluJASUTeyoF15oJamajOmcKsGhn7rlnq4eS83ON5DIVosT+W\r\nR6F0w5ysTixx94tInzVngcKSR63zwfjUtEa6S735DIwwjNC1gGm6TLncFYPr\r\npH+l45gI6pj9jQOqogHXAQFgt78MYADPFUqlhOQA0i5GqmyeSMlXkNvZx9as\r\n1/1ccIvAp+siPX99rfaRqtFIuHogX4U10YU=\r\n=cx5N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90162754845cfe39885f3ab6ef6c9ebe77e80660", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-znl8cG5Kqfv49FmdHRhNnXuFjJSP78vWlRmPOh4k1PizW3Ii+jHJZpLKs7EaFY58/h8jf+FcqC58HDyXH1Ylkg==", "signatures": [{"sig": "MEQCIDE8gY0ZuyWvgbX7fKOIMJNoFf5EcttY+TQuYxGyPfwFAiAHZeGoWARQbIjpZAzo+p6OewcCYbY1MoY4H36QmiLUKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwKQ//XL/qnh+1IIoGvci8LHq5eQ7nrqF+4JLYDE83z5KaPCjwv1S0\r\nnbMGe71feH0SDgn30gd0EZP6XT3ewXtXELR8sOqzWrBsAbZ/PhLWPV+etGom\r\nGZtTlBwnf9M4E6VafjmiznK9wNn6ErjXOztof70itj8YZtRfgjYyVzPpA6DF\r\nbtYy0eFBihjT2H0aEJyqxggqwGuDWIJHxgpzQl39bjTRwjzbbLsk5YuQ3KuP\r\nfVPpQxDtCFuV4PO9hvZmMg9Zlec49HVdGVB9k6I7caoRMD7LY/Oe36x4idkd\r\nZ7ptSsb5IYLTN28eheP0Tqk+wRzdUwusPSU06jVxUjLWnz9JPZh9aI5DxM9K\r\n034W37bNx0mbmgmgrLx4isfHnYX7bHLC+ekFK5/JbLDByQ6YCb1IcAEPHI+a\r\nvR6ANXvkKJvRdaEvODzuhwWVPmLFrXWnxoKG9sHeVtBnceCNZxRZcTzJUZIx\r\nyNLQuZrrtGeovuCwfsZIygyEB4Hc6VqcwNR5dshrPb1XQw3SvzYFCpbhzU+9\r\nB2AaadTpwCe60IjnSBzUpsACL7RauEYLShOlY7QiNa3LZY36S4pYwnP9QM4W\r\naUicySdfZd/uOy5EDXGUs1yT6gnY+3asqNOyPP/Z2Vo3mqINafDYfuEzdOzz\r\nhvWTRrotmEFlRLkOvINSjEztR05fiBzRU3I=\r\n=m1X4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "19e1d0fbacb703371dbac944448a5f10d163cbde", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-bTtSvlOFa/Oh7goKP3+j15YiOLKVpC9ix8cx6VIPSumnZGMAHqKR7OZeK/uUDS3H5Pk7RCSObdpNiVjgWKVNJw==", "signatures": [{"sig": "MEQCIDvv1Rpw1g606i/6wWaYuQnAofP5uDNEZO/OtR3xbdUQAiAEliPkXfUtTFaSY6SLxEVHLBtlKVKiTPV8ddlUhkdQsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSYRAAj5AzFG5dVsaTOT0vKDcZ0YUsvQHRpTlTkzDgsMWGV8eNLC7n\r\nVNdOJdzxImSxd1c7903noOAQx0qNk6yUJhATt6H4DpYSdKIYk2k9etRKuFx8\r\nJHy+9zafRY2aJfWBVrFZ9yeZhIIYz8EnSAav2MLlEukJ7FA6gpERcNvJF7Xk\r\nsoZpXrMDU/sUJPouQiWD4GTt4jx79WGBaT+NjFcMumlzh9Z2mn3mCr7fSAop\r\nOJcPQ6J0m3+hwUeNS3zWo7J1rttfPVXVzhF45Rt2M0EgpxpfASiV0++/ZthF\r\nKbBzW2JlojLS0RD755dpkf8cUwytiboGif4y+QX5IMkywSsEBkWE3zB5dJ0O\r\nyRVJxLIfX7AGgGBwGeA8oOg90h1EakfhdZesSV4Wyi6dMWS4azqM4FmJkTaA\r\nlV47ztHYOc2e1IeLQRQidVPqbwMqLQTfcdnP15GgUlxVbL7GcWWgTcddiA2n\r\npw/Vv7np5TXwjf8gamdwhMKN96+vNXYnVGe2bPz12tudVFRHV+XIy3jjdPbf\r\nQpvCL2jWTcEOXCDGnrT1GL8G+i9YFmDizG6jcMEyHIvCtXBvnD5P0rL6atUq\r\nDRzq23Cck6sveQ9jtpeobhkBRKl+OOc3YCNj0UM4PSuzH/uziu0w/4Qinx5m\r\nvmF01JWOyr9oSK2SXWRYq5pjL4ngv/hu8sg=\r\n=qt0E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a73b134471b118d9b274298582a298a6bf5f6ed5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-Qt+8cr6UPvvB5h4R/G2ZDYZC0tfkMjgv0/s1wpbY/CKozal9HHYnvJTx9XtjPT4Idmcelp/cCQolZD6YWHNRew==", "signatures": [{"sig": "MEQCIF+YbTb2SyhBCVy9EvgODGGn6dgjA+KpPpGsRrEu1D1RAiBMsCdsMqFznKZhMQqcEaAWC9u2skDF+CRmxFnGhUgWWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKKw//RxmyYmub+k3u0/7K13owArAeCJW5fZ+tvmSCvHTVHcKQJ30j\r\n7mXsS55SWWqIiIkxdmUYlP5aDhVxNkPA/7HySxhgyZeMKCPovVtpcVzPLAzQ\r\n2W/le+WNNAfnVIF4vv1S1Fo5OVWeNFM7Kkpboyz5+EkwqlEi3z9cpEmniJ5i\r\nrX4//j4XT8HJji78MOqZoF6l73VZAKUUp1UtmJYgL6r2D8VHkoPHXB8Y8jWq\r\nVu267vv+2zdHl/W3bmWvg/tXVzvKrGPMOrF4odb1gAalaXSEq6MPgS82a2ps\r\nJDPVP7VUc0Squa4hDYUVGphRTBQ9o5bvFpO8WNr/IzjPdo2CP3xOksCDIP+r\r\ncUnjC4r01aRzadgjtXkWmMNPv0s3dzk2qdAhOE5spnR9bnkOiZuHgTIDemvZ\r\nee3Z5bE2CFU6bjEIRhDb4GyCrE5AnLL+hPPAkWvExDfes5nLaX3ZH+jY6/Cw\r\nHcgTV9Pic5Tuf/0vvW+BRHYWR12bNTVQWiLmaHbM5cqXmk7ivjTYT1KxqRy4\r\ni52xcmfYeT5ITk6SuVfmvheu1H8rA8U1xUSpzqSn062hmbLSj+4uuBxSSFeb\r\n8t6w9BQnq9D+4yt6uxEs5Y6mAmFTpxyuDWwePH2NlsU5QZMyhG1doIU0o4pY\r\n7wDFsY52fuA10BSWWLLLqtDapVGLIUBQrRg=\r\n=M409\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a2adedc4eea4e3a464bb02c58f37555bd41b86cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-4UUU4tXs3z/09oKKPLLH4HoSgnYiijrsscuGloKT8M86+bwpeB47TmNfIGQBMgGtYfu3/udmgYhlAbKa1mRajg==", "signatures": [{"sig": "MEUCIQCLM4WsIl/KUCJkq3j2v/QutGkmxVT0Df6DezFc6Vt01AIgDAfuQCN63clREXjLYE1kDBYgLDXPjOonw4gJL9txd/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh7w//ft3TBFGbJfTtbQW9UgvOzuhU0SOvc+KA/Tn/YERavtbY71rY\r\n2NSxtxxOIKzGqVOgpV02jeWVcGD0dtDzdZA/uQhJjLRE+JleGwN1F3Pl6vku\r\nqsFJC2lG9R3hDtdp7tGao+LJC1jWezcHgWPaZhRKwZTWdpnRE49vTq4/h/QV\r\nv6EVMvbwXJFZ2lMo2gG+BSW6TQ0LfAXyJqQ9TlTzlmJlX2taRr2G9wPvcpbY\r\nIi4KtX2iua4BxRbjfzpiGozUWI2J+yZeKUNmqM95m6HDWrekxNtmMJhaSmzI\r\nUAdomizwBFe41brLHigKh9UfU99dEah12z9JDAkK6lZUJnIA/lZNUyV/Mo5h\r\nc0c/7FMIRNaA7FXQERnTgFWBKtNFyQ7xFwUX3Bxw5Tzn9Ql6Bi4eifA4159z\r\nS33jPRkfwvzuV4zhDCEptEq0d3+OnmC7J6gjbUaPvh5GegKI+3THy3qsNLy8\r\ndhN2cIKUf3jeXUgCVC2++BTMDia+hY2L0F2eNrZgOx3tQQhxwN0KDctrJiZ3\r\nThvMhm1lbXYhRBwnSswNalwHoaI8bnsMd6WExsZsLG3bC4+M+/QOrjwCSzHY\r\nNnkcmR9fs2tF186T4tBP2Ie2JKtiHrDCLaOngdG6ur/sxw67m2zLk3puApSR\r\njQkeIdrZM3OHbsAchdTnH8hHA27PJZ7gNXs=\r\n=5wzF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dd49b31527ffc88745918326dede59976d808012", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-WHK3eDPlZfwxV2aarprGZS3L7eSRVAtTsRgQPPAP+gX1GcIwHcLXrPK5i1+Le3sgOOU9J9uGPo34k+rNKEVa9g==", "signatures": [{"sig": "MEUCIBRcT7jzaGc2f0V+tKHwJ21QWSaUxDR5Fh7Oqf+3Ia4oAiEAsYKVDpJI9UlarbxHwkqFm/XqN7Gjq1GywP88Iudis8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwfBAAlsgNDn37+4haSimbwiEoFTmsPtFWoS9aETvfgrUdk5ZgRkfM\r\nQPSyuvsCLlNTzr5Tdwh+fBOMkqLBas2vyK8lrSK6tHMXGIdCJvC5e3SdZO68\r\nYTdjEtqMYfoGpPwj29o9DYQjavrLy/0NdxVrCzaudneGGHha0j/dOVJ8ppgV\r\n/fQ0kqmg2xdQYELy5S6i8hYcIFtiNxRpW5c1Lp8i/xy9lpM3x24iW9F5HJnX\r\nZjQIyNV715Cvq3k6Y/fxx6r6Q4OS+eDCSpVS1qmCQCohS51yOvmNnoSEL3WI\r\npdHQy0wkTWoMM6U9T/boWDhJwFziC0d+y/jfyLxPMPmqtRbmyScSF+DqIjf3\r\ny+yNTKvlhTYJ+fIVtG1LS+cGdx/0n3Zw4+xCvZci/osy5pXiJvmJumCZbuBb\r\nGsVZrQ/Tca6wDisLwe3Z516cUd3rmpHd6/v5cejCKmhCidTHlrqFGvCeDepH\r\nlfaWGY63CHf/qX99MRI9SZg7Rdd6LWVxPpaXUFxnPOCINbQqgOSeMfyi13Nr\r\nDlvZPRTUSIijInGGbOsvGGWDJ8R8PVQB9KbnqPxPGuXtuONt3SqNdk39S5b0\r\np0ixRtkYkz0LJho5jRAp1k0ucIn4yau61A7R/B+dm83vVEUeleE/FlGtZlYx\r\nw4lt8rKSxAZtZSsfMXWCj3DAjtCvBjSXzwU=\r\n=xkN+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a1ea4ab133067225fb6189116a4fce4a624d3a94", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-IIXd1STaWVZrOmguZmuqZYhOmJlXouDBek6pb+/hwjo9dBtPNiDi2KHwlYtIjuT9OV3Agxm38GP+mv2ePJESig==", "signatures": [{"sig": "MEQCIA4ytMq0wxfYSRYjwlabJnNTzNYuda9cVswb74AUpCNqAiAszmjHdi8HjBu7PPrqiMVNJ9/H1nEf8/pmPhyP2M7YKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLh+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoI8g//Q2VwApvvKPSoPFr9Su576umR5cM1BkrhiJpzkLmUTcSwCoJ8\r\nNSxYnYI3yZ+1FJkf47jH/eiIdatjGDUzuRSy+h3ekENgX5w32std71cxmymx\r\n26BPlotnrURTajgb6oPrB4MDpV802gV8k+/Ub0TfL89QCtNLJhVfQ68oL1ip\r\nJygYkm5dR+yavvMu3HxEPY+gl/821lvW1pSTjyCPrI54ctbySty2G+wO46JF\r\nttJqtL8DzRVE36HmSbFDItXSB+Fuj1n0CH2ZkPj4upMe2YCuomTQiZHgmXaT\r\nt1Y2+VexL27aAjA+eSsS02SsmJhTF0V/IOhF4VPgnVzLk+BTvITqEVSgxaxH\r\nj6YRFIpr7uF/9BUK1kRTdWoIYPJWfr/S+Hu9SliaLXADNA7arWRMkGqGLs5G\r\nXD9AIiULfcjw4WnqeTVgyUEVJg8HVfQgxK3mrYNA73RoAaC18gunjKoMi3IA\r\nXgio5UACxkJhXbNcZF/W7GtTPC2xirDZBiiDzhcNalhHD4Khp+/6MN5OCpBO\r\nuRytJkujnYxhGs8/TGYYOmqT0EKXOImnWsUtlSqKkgHH6TdqsusB11YMoZUl\r\nJqvilkR24TbG9z5Mspk0BR8z9O2GqU9/iPimmBCz2Xu3OG7VSzfyn3nDcJcC\r\n8a2cK2QwNnD3DjbR1Mug1JH3aIXjXP59xGo=\r\n=CR0h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "083f74661806226d9ef9ec39d83f6b9d3635616b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-26TDZG16HO5hK7Kx9knNi/hjk6AbfkCoFvMJGmKjiG1fefWTiJ03Rx/kSg9rPcKAXrMDHveY12/ulN+o41zqgA==", "signatures": [{"sig": "MEUCIQCRUd/DyCc4semXD0POFH9lGpOubRDUDYAL8t8cXqWFCwIgItms8VAj0dPbUFFcKqtoG2QO4wIFxUvibS5KWRkGjdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnWg/9EZ0AUO91bEpmbVsctc76UKDQKFHmbREWS4qrkYgwWwzHvAtM\r\nthT75798MgiWss0u+hI9/TOKPhCkQvmO/2DE5uJXdsBUG0oYIsJcgt1JVhB1\r\nGRT3sKozRQYW4yQ3iOELgmGVKIhBR4Bwz9pk2D/7rHOAS1WzWCPLLqtokI3R\r\n0CANEt9DZd5pbyjW3uV18YCgx6oX1Jd0HfbuazetT1e4HzqfCS61vmB8UnJn\r\nS76OK4arkrfTI8GWb0jO1AQpXUhS/VMmCMEP1YKEoxSnzyuc0u7bBs+pQI58\r\nQrPs+y7/3DuD6/duiFwQ+685eMmvE+AWgvP0lauNFbje311KfsTMXq7ApF1m\r\niCxSAdw36KPJRDw8CLqdZkKAc+kajxe2gQcAW4kzV1VM3iySBGceEXQ0lXjB\r\nrAtV7sOw9jYZZzbKOp+VKLiEipiqATlcYDRMvaSO+XHYJDXRKvBYZcS0x/27\r\nxATNppDEnVbWRXdnM0LnHU71Wr2Y61BWrp6H4nQQl7EvaDNOuDJ5F4hAP/8X\r\nLpjdsTT6vhutBF/26iFpWDd148310Umb3UgGbyRoPs0ANDF4M9WTwDxnZ0fY\r\nRO+eub0ujcA8cyZN99Zsi2IDvF1WieLmr+17nb88m3stRvtpdNernBsRAx/W\r\nW7ipKi0Kj6ChZx5AjnfXVG55KVffrYOhXtQ=\r\n=yplB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e4af927d8073f3aac582ece86251268ee43714f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-lo22EEngJUiTNRq+h0QlNgf7uiltjxYUvfXdk7VP6WRxb/39Zbm2iIvOsrfTHLbatGLh9koWkHqrYpTuz1kCfQ==", "signatures": [{"sig": "MEQCICqzIS7jAubAzVpcjWsaHl1RD3d4ubfArzq2698XHp7rAiBVpOyBd1WsxDrBuRL5gJlrSaK+sRP99CQwadU7bOVEyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorlg/8D0QcN5QUHCLXvPIztbm6mCDR8urJ0IVBvevVNlPDrpoTG2Ge\r\noejB7AyKZqlZy2CIpbIr+XwwZk3wZBU1aj4++jf+tH+sFC+NlIFhPGyfIKvU\r\nBDZWDFuzIhk0O1hCS8PyI+xiIlSCjoIkKyo/iyXQt2eppFCrPj2O6aiWl7oe\r\n/JRpDpudVVDs/lXzTggAkQbcArguG8jR/EWKj86GG5usZQhNE8q0y6OD8HPL\r\n9+d9RbQDzrwAuMM7JePy2eW0EWbb7TgApLka5mtXpKZI6EBzFALyvOnQxbgF\r\nBMJd5YvObhW9aeD0jpmPxFW09q2NODPuYH5XqHdgMTmF26UwoUdwncL9w54j\r\njiCbas6raT2alU8b0D4gTwpC5yad0L5ZgNAAkd1C8ShuScmqmwXy0na/h+o/\r\nW3/Johpw6+R9YL3Gfr1Jl8mBvmreem7sRefgO8iGGFUtKBbUdDeU3at2HfHo\r\n6YYJXc+c2M6FAtlTKMev1MuCV0S8MiAMqrBsiSP/zQNQudIlTCAiHaQeh9Qt\r\n4+XZm32LqEoaeizUzJ3CQV4+eO8xCw9F+YFfEqKWc6RZTie/Ba3/QxX81PE0\r\nbiGlAX5of1ZaTAykRY5Hkonhxdlg3PQdOJH6U1ZR6ElSaYjeIctZoYyr5daG\r\nthbE6jp+ghI5GJbH+Bdcj06lBHQG6xJ8qiE=\r\n=Pn/8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3f73208eadb317fdbd5b605bfcbfe4bc43b3720d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-lLkuR2202Bqc1RgKWOVONVS/eYEGTvBoHMjBEunmLoQq165XO35HQd3kwMLnRQuH/UoJXm/kZWIyGZfW+R/3VQ==", "signatures": [{"sig": "MEUCIDDM4AUo1tiOyJPXDNO9idCXEFJquAuP/w1+cx60nbwqAiEA36Dm7LvNnp+s+gNDdVPn/7wggNIsSNVSsk0ErdGVSjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqOA/8C/gW/BEKhpc4ObdyONa3xxP4VUAMnlo8M2FtsNfOz6fzqciG\r\n+R+m3pRzni2VJ2JNh18VBwmzMLog7ltMm4nmpHfWO0tPMZo7HBr1jgntX9Er\r\nja2yrcFNU2Kqb68aTYzNTWj1Ih6eqEaI6WEKMJHDflsQu1uXa83TEQaRgxaF\r\n44kFFrl7+E9irMmtE5f9fsO9O9VN9ihPlpJj6v4pLTzoEAV//A1Za5Z8BMlj\r\noz+HUB7hUcyW+7GW8vO7AX6p9rJSBemW5IbBPDktkgaSaE16r/Io1BerEG+G\r\n7z+Ha4PC7p6HzjjCVanpMUPSJuzBgtgtU0NZbFR4wQgRCZ9hX39cyigrSnhc\r\nIU33yP3mY4pYagQWJ5Jx/Kojl6Ahe9Dq+dAwHHd9/n3AquNoyq3M2M3CHTX6\r\n1K6coINapPFvedfwbUC5+ixD5PT9P2k3d0z4hfPyzwXaYqG2QgYgd5/I1gOA\r\nOC8o+XD7h7zR9uDxJS1uqpHe+ZPQIDZKTo1ecocpXvTFObHE/rKMNtvKBPgw\r\nqmvW0yptPa9vctwpR+gcSCF5a2lDZ+FmggPt6kEhzQLpRmbpVfMrOx0CXBd1\r\nDpeiupcSxwru2X36LaQ+dQRR3faECnNwyA1hcxLdyPF5txEUqufpU2Q7ZQ8Y\r\nLqpkn5T55vMWHL2OEixiNCLKLJvNMkWFQLA=\r\n=v9Vp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a0b4121b34983378c2cb309ae03742eb3fa27e28", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-fMF3ofJSMApblTAh0pV62xV1xCZ1raE5X/GpQj66ygc/R/Ri7LLk4+1wZ5Q4/xcF4+upvlrLCzJ/QRzgBk+Q0A==", "signatures": [{"sig": "MEUCIForo2krlKWMW20og/wuqxJlCKYwphlcb3apWWDdBrMMAiEAtWqeFYsZ/qRJpf51AHdctuvGmGj74f85a0KPQPwYB1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildN9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyehAAlN0pdGpDt57l08jttRWyEjC/XVLldhxP/ISgzyAt2T/8db8F\r\nZeHJY4peD2/jDUL3s1ZYHwg49AEPLxwYeSFdzjLP3p9oJF0ATezeUL/NDHEX\r\nlE1jkD89LCWU92zwz4//84jIcUjiqTXhoumJjOKXkQMbvMNhJLJBBopemcLv\r\ntjA4idCBU25uUI4dnC12dbSWmtM5YFTEybuLcN9VHo5YPLIoH9CXi6X3q4WZ\r\nzjS+1M84rA8/Ea/rz/biI+RRBlWqWimOt5oN7ne8Kn5JjIa365Ujof9ikw2y\r\nLF5+P/V+Ul0e7VA6Kd8DggLU5n5hqOu2BSLMVA0R9WzpfDpZr9qY9HXcTdXd\r\nozx9VqUymAaTIFV5+79A1vDZPAznjINRA9v8pt1j6n2tTUW9l00x68WRVgrW\r\nhENjpXKU0XIgaff6dy1LZz8YW4pmDgYHM9r94boH1xDz057AWa9CFHghjBv8\r\nGIhXjH0ZJSgwwhX5fVfiOd3GP5VP5Jvx+6KY/6NdMKlSrMf4xXTlKs6B8oJE\r\na1ixceU89uX8+xZKkmXpG4Ui6nVy2IFq47D7QmK1dljLtwCqW7s8xS4ebvzP\r\nrevP8oJmUBD+ZcyWSWcOdhU2DvLiOYFPurbcwj9/ZXA7JNYwrGbLylyVkcbk\r\nibsSE5e6xHjhU8Vv1Ink1Mkxr+emGmkXN8g=\r\n=ghw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c69168c56f5e96c3756f4b78a9b008ec61249cf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-fPO3MDjAw8h7FbsLC/ls5LqXfh8zUVOFhtCCpbnvEOVIs1UzM0+upcZUVRC7qfSKa8KklG7jv4W+sV8EBLNq6Q==", "signatures": [{"sig": "MEQCIHB1QMO91TTgXME3spTU206b1g6+MI7nZyUNnhup0L29AiBxndPmW70lKpAz6tXNryv7S7U1QNN46Dz3eoHAuqqAtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildr1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkJxAAggNhDYK8c1U+0W/cQ5+TFKJYulRnGLWwUvVl7eId9HT4g3xm\r\nhH5iQ8A/5vr+HgIGvmseIUcBr5+S/IZNlKi/TIFpADtWBkdySefMoepZb1AX\r\nPFWc+VlhbRSChn7LrxgXEUl+/MWCGErl3ATld8OiSFBd8j1l0iEVJEeSXvu3\r\nzydCVDUCRi3Ct2XnqlRP182uBBWVuF/EPHUtQhbOoJcSJNXrPOFvWSUZ0Gma\r\n3GZlTA9zM5E7zrIpdItjf74FkqPzj1g4zMa7jaefnnGpe6fbU96D+v2TXY+J\r\nEj5BkxBWMQEGP7E0MaOR66cNFeHdr843fl8evjzYMe6i/3+ML50Aoza7GNWg\r\nIFGlrvsGzIw74OS2WwClrGgmmzI9B3R3ixzmowbVEJPz04VHICVHQAfSbzU9\r\nFWksGl4jqdzrPQJXUfBjud+qEgu+yNHRs8UsAQc7+dsloQBTunq8J7WhuHnU\r\nH+5JBeHh5vLTwTSd2herBnOianBbwMhgHa6EHf/e9CgjWRN0yzTMj0Ise/EQ\r\nzy7/JwnBRqpkZmzMZ9+r2DT4i6Mi6Vf/yPxJ5GIOifjntQ/Z1ttFlwRv+STr\r\n/GR1XadPLehH6kc0IupRvuK1qguNP4fO9C/kLBOLQoPVmwGJJFNQg2fY7b25\r\nict8zw8bfqtEN6/IaPe43nDNooACBjxVo/k=\r\n=n6Nm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "60c4b9b94884092a5c6fa529643d68a0fa171d15", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-2uob9HkT8yGzi07u1FQ8heqyFCIr3F6F8d1JhFwRMLntVgwPpASmbhhyU6Jckg9qKYvqk7FCIh4V2XNtkCQ+5g==", "signatures": [{"sig": "MEUCIQD6HZeftxzZTJd7VEGiszq8BoNoBJx4DIi/TNqEvLirjQIgYdiYtXx8pETMbj6x3EtGWUUSWdrSFLF20iLBniNIcAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLoA/9EF+Sh6Y3ktLQKP5ABTJiUlKo53bVbo19VStMVIjqpVT/B/A5\r\nQT/k6wpC/vXMfrP/QyPeQ7DGPKimdkxaWcPaO01p2wewYIqrEXINEI5wWmFA\r\nTNd/i+FKSIFN0TH0/Luoz+bSdGMv1BVBtiHmnc5BrHYMrsB02N/0lrbzGlk4\r\nhm5wGxxdV1PNRi9lTRQzgrPSvkKrMPguTKk7swquy8cVdxPhbSjvJMdYUhfw\r\nzi9k+9KNtiAUFJI+O5WofB9ndm9EsjCXvi4qABceFq3Cy/yBPkgZ2k/5p/nc\r\neM+vd2US0aOi6UwoO0n4tODvScjQ9w2Avf3dsUqp54pk/S5H2KtaWYv+nYo+\r\nVx3c0jI5MqjceiSqXren/M/pj80JbwIn59dq2KBPWrYWO/WMa56YkV9+uX92\r\nAgom2jyNsuQJUZ5yR4ni2Xxkti9kul8tgkBFNTiqJBd3WTGADRAMbXkIYXGN\r\nFCUJ9s2zMSnuKVPuYLkAsVpuAZiRzq+LXDs8JWD91pk+0jzAw/IWbo0OYbgA\r\njGfXPJMal98yxEvadvRwH5lkslj8326xlH6OFM3QnDMy2sDfRlM4dvogMSZ8\r\nu6H+feOj19b7V+E98nkW0xaFlptuvrLCtwuew3IdCGbbyC1y3GDEUtIKBlbo\r\nMLvbf3qrbAqLioL2AhskKynOXPa8rWvbfOE=\r\n=pkM4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92bf64f18b3d47f4d4f4d1cd9df410f39f0e93d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-wvQWTa9bu2EU9pEobksxMWtamovpqz5XBeDRoGWDcsxVatsJoTvAebU/Rr8TkXyAl03SOGWEAfuNWmXRCNro8Q==", "signatures": [{"sig": "MEQCIDx6TK73J4iBhyw7NpUa06fd38QP5SAFshFc/ZKDNMh/AiA8twHDBZsm37N0Olbb2VfqFF5N5wsfxQiyn5lJ40o8Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqy7xAAg8IVJAykPI0XVDLFMP6AnYj6tYa543cxMcV0rt9OUI3Yt66Y\r\nSbVoUQPLAb0etEXXO2+XHeRoSlhjiPeweo442IV5Vevfv8LtLs0Pct+fORfD\r\n8/AvAxXmIHk1lDLbhaa1zz5V0LzDIMutjaqKhMq+7WmVpH+dLf1fmS/WnOnN\r\n3Iopv3hyyLTv02Li5tfcVLQHL/DggIhhYsAoz6Sec457f5W/5mnSNhTeeyfF\r\nO8Ah1TQq5nNaAgyOg8v3T/9ux6+Jr2A1hQ2i2BQeUu8ZlVvXamzHzc3v5tYz\r\nAIt8TEKNUDMQNK5BCeoKuXa1mkIRMyIGBh4c9/3wdXp6qWkOUgn6i3fY9Esi\r\nPO9LlkPX2LAu9WrzKsAaPLyXJ7cY2FEij58Ko3CG5CjsFm3v8Qubm+zSUG51\r\n0hPm/8T0Mj2tSWFRCJMXem5bulz/fLS7ddR9Fiqx6PdajF6pVRptetNLLZrU\r\n5oqBViUYyRngFzsY+mYVFDofxaWuUayXDA9UoozGzFKr3O0E0upl8ugbgBoQ\r\nF9pdUTqD1FSdzV7P38Qa5lLXXi4ps7HqLup9uSU+EwzRQfiwWi/qja79rrcC\r\n1KumsPKKjONdZdbYTB0YFQTdTe+MCJVopcd3AIJUrQKe0elqXUwzVBaA/r6W\r\nHmfXZjVyNvDKVJHhov7Xg+4Ftw+IXRyYFXg=\r\n=QpgZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "30ac3f2748e1233acad826bf96bfca1f5d094ef6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-kc7d7Lj0XYMMwJfnNuwNOkbDf+XyhfrMZpxLU/c5E9Yj7IxKu/SCycy1Glqo9xP/1EH9J56DfhrF4pS+BzLrbA==", "signatures": [{"sig": "MEUCIGruJTGj6+W2DFu3Mq7bmEQ1JeAJm482iy30J+wyzd85AiEAuCtA8Ydj+KplTTjF4u1+kqlkK2NZu7sLwsXcBgyaBQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouAw//bm18KjHEYZ6kOQwEJSNBjEXoOET4OUyQpFMIlyhSHbrjskEz\r\naSDUchPfgWQFCVKfjmp0OHXASpcqOsOVqZ7kyJVqNTOIYuUYpAgViAg0DLUB\r\nNXjVN2oRFgPUQQ+vXv8Nyi3aCqh4lMHHAb6BVJ8OO8A2SXm54zjVCYOJ9k7P\r\nZGlJyVVnKuNupewsOfAiAZ90cK5m5H1fsuKZx/+YbuCPJy2Jhcgu1Tk0Zj+X\r\n3e9QOo2/enmmpN4ZT+U7mIY76adzhVMf1dVIZPw3q1kbBkg7GSaAsv7i9/Aj\r\nfsxiKGhFF+ZDqS0p+z8/WElpL5O8gi/PzDRCcaG6SEFn92f65gEGaX/qsOfe\r\nKA20w7peXRXRe85B9uGiU44NREG3kfM2eghwFawBwqiyChMd5heMNj54DWK2\r\nvg4BeGiiKdFaw6vUQ5CyUZ7BGYXJKuP4doodL3KVSBHvW5AhfALIrIpaBIaP\r\naiLFzAVg8RGrQ/uAVwSvt6N3OZy5VySsdHkzCXebv57wnuIacikHpYek0i9j\r\nGHdnaZRJrIfpo+jmBT/oHNgSozkiXuB0pC+XdDUcscZQ/ibGpJvJBfS6v9jb\r\nUVjn1oVm0rczDbtJjMuUanoUX9H85Sm5GnV6zkD0a866OKSLRnr5BOrFWF+q\r\nqpne66IE8Fup8yFFfmSa8nLSBnsIsnL7Fvk=\r\n=g0/w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a638bbfc693c0d867428a5bb87a810150083fc9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-tq+0Se3D0u8I39fqYkPsR+GSdFGNVmNl6sy+cS7k+T8gev9VnZhNd0QKHSNOHmzwvtr04u9a3KVchXYfWtBdlQ==", "signatures": [{"sig": "MEUCIQC4+LMcSRovWi9bD7/MEGAiY7i5H+a1wmmL1qV/jwX1lwIgYJiiIbYKyvvKbP4fIrsVW70nqTX8iXc+19TzxhjfQd0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ8w//VHq6hKqiGvF0p+J3nTmwQOsre3IhnQeGRpvXT5wNNLkobcih\r\n+5CeSZRjjMWt6079nR/O1c3qWdlxALxiiHJm0goW2SwxwUSn+2NzAMuSVe/J\r\njCzQeWbXU3HaUQtFwHx+HPuK6wwrfbhIW3JC+6lZ7mybRcwwh2QScNGtDTrN\r\nvq2dK9Y8gJs/7A8JkYoEUUEUOuL1BPmyoX32+gTku0G+M/6eQjvXfsENI5iG\r\nbyVzXwHrYLlKV8sUBxDy40XBw4aLTyBKX0DI5aaOUYKdBJS09Idw1MGC1N8B\r\n7u7rjBK13PiNu2iWFRX/l0jhhHNA7HgR/UgTQUfSx+/JbAndb14SvzLLjLTD\r\nJFkLFTyea7Basv2Eh8svapxjJQJF2de6PHeJZjYVgi2Jhv4Fd7rDaN2TL6E7\r\nDSGmuzgN1iAE4H94sBv40BwJBAI7xbYc5m+/GWA/J2kNjZ+amdgusWZzXFkN\r\nYZBpIt4wnEwFlEFmRh1KV8dZd6/Qs3bTEmjKguLl3ZxV3KFMSh8JnAn5R+fJ\r\nFCUs9eoL63WrkAfnxldHbzz5K4HhIozR4GnulCzR0/yS+wKfSnyCTW0Z/dXb\r\nsIlZanJkUjtB2udmKmaLhXaHZJ5k/IAYwbXZfXPjoaOnqo4QKzc+n0DsPg/a\r\nxUYHvaHeaSARwiOIV0+ZhdY14Tr9xexgqQs=\r\n=ughG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "97547d464c2a43f745c5eebc963f2556042ca6eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-CHD4i1uMcNkBG3ja1Wcvp2t4aZ4FTKfRJc6nMk/hXDBTe4WecuzUdpAFCXUz8pCxf9YO4UF5Vd+g3C1pEIX9JA==", "signatures": [{"sig": "MEUCIBTR7n82FOkh0zWq0h1n8qXgCAtl3mqrt1riZCaEwH+mAiEAuuu2IeaiJbtByA5O4PHPhgFKBkMg40cAukNzwQENt8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmre8Q//VwNdBT1/WHVQ3UUwqcT+2GxO2QZekGvy/BK+PvN8mBQWTIgh\r\n+F3moHmq0FYj7fE/3zoZctCoNqGIkzeM/aT0jk1ovby4K7gDmk8Pe8T9s/Un\r\ndLR2dYRQUEur/fyKo8SCLfmNgDRrTMMn7jA3oTkPLsxfVR6y2G7eDEe3OvbQ\r\nzIelmEHePS7StK9HQX743fSG8SlJ6z0i72DNokY7uOm8gRTkDsk/IAn1Vmnt\r\n29RFOZxtQRblSqG+QTK01xzFInkiojiNFxb7B8kIaSTjPaPFx+2CgciNKwFU\r\nWf/Rlrk9qEOxhMhJMxMZGsPXEsWxmYzjPg3tJIvA6polTpniYARgjsD1zMrf\r\nWYxfwWyz+O7inEq//a8ZqdKTsJ1f+nDsreVl65wiUEZ7yc0LhFSH8ecUFsNM\r\nOnCbTc54V3BAbkuqAzPX1sxv0zqexlGD5YNQ2f7jjbsmrHdQoCiWb7PycR4A\r\nozZf5YIcM2bNv4+RHqRsTR0ioS8Z5mgl4/kbhp02iL3m4mcqRqO1TsvBKMzq\r\nwwBnES2mQWmAu7YBwe4BUede3/mYbGRSPU1QNV+6oiKbTvcawsQ3y9oDqvJl\r\n2fhITGkIbNkohAV+ivStUWJIrrjsizqNth7cWfH+2FbXMsbJGx9MJ5GhW1Dz\r\nNiMaAJSCTci0Vu7QdoXXzG53G1YtbnxQt60=\r\n=oKj2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8db11e12c740cebd6bd60f2408c81581502e1a89", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-NtTQyuLPs1uvEbfWyHCmScDkbWf+mlr1qTfduu3WnKlp1yxkE4Swj9GnuKXAs+RuMj7HHCGoVbX5+0XXG0+EAw==", "signatures": [{"sig": "MEUCIQClFs5a42NlZvpHkYSkjJpuJ4Uddw2k/V+diEeSAv5HCwIgHajt2xdb2buL1Ra7GzERw0abknJ7eit/wpJmlG3XgEs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrANg//dZxU0hHXFbvxd1qgjWOhXpAoX5mp/a2C9rnwT2dY9ntTIJk7\r\nR1YDI0EvtqK8yDXcy+pz/ONn1nAEh2acLXw+4tPaFojxiNWA19+Q5dxBQCIS\r\nxVMRrmESFB4gmdB7KP4n+HSDSltY62ZYltWgrAX5r2DhZNGNGZuieJ6blTz3\r\nddprIiC2TME/38OYSVOrAYKc8n0yNAJ+FF3ogd59gqT7qo1xsFyyx8t8CqLg\r\nvlj4c6RqpZKgnhnzPe7OCWGoR6MiwpFrSCADEXzWSIvo3Yvx+M4O37OcBbiB\r\nexST441xVbGxPzXdmvo+H4r9NDZSXJEIeAYFSQjrv4vhPW+xrAO2eucdtERH\r\na58H++a890HUs6ghjazMHPn8KOCyoTtacXLCbZ5aSaWodyB7gUesgvE00RAv\r\nYy0lgCfJ+qCR5lHeC/KQiHrBvxdhRgVrduIMNRJCZUE0Bv98f9LkLzvtfuba\r\nol59Zagg2x4yFHyyhtyoIwNaKfI4gm3KWgX2exu0Tf2tlAzzscAspguhfSKr\r\nuAfvkFL/ZXhs9uWkSn3LNzrCDVZzl371iUE+1NzqUCyi1uIPeAQJAiDmkXix\r\nd7Atgj854VN6U1XLacuV62a9wWLe5clVDeI2h9dBh+HwkB4TiemdZQ6DCPhD\r\niL7UlaRBSZH72jbFdNsOzN5mxQbJoFVYQv4=\r\n=SKwh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f2f131217095f45877aea816de071b5d32f83757", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-mgXcrLOnH66YriqEzBk58AhxIsKDQnq7cShmldxy+rUmZRYMnmJuIK5LR5mWG0LOMJI6SpNb/3zckBb1tBLDUg==", "signatures": [{"sig": "MEUCIQCBD6EzBvUi4OjeKMWra/TF8SajhgPuBs3uyjDw1fMi3wIgWSiO/8u0GD2RhG1VBLwMV9LXWrLEQyXXT4pm5PVPnps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0JBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOqQ//Xg3Apwdg2DWjcspStagbJ0+aOfQiYVw4mF9D8qydcztYw7UI\r\nYqOvra1oPtoARBfftnfI2z4Wh3n418p8Wbn0X92xZjVyj3adGnindequol/o\r\nrxTamQqgOZfxm0Ce4gshLizCXE5ZaPCCTIxsXYXFkmxvlh1zMFP4L/g8d4Zm\r\nGjNVkRPc0BfDGNo5wT3eiAkKLVW0ftJy3tu5pGROwEepHSR2kYNjaqMIX34Y\r\n1f1fofvurc6P/BD7DBwDMVRFLXWZdp2F29mDuH9k5hOsxrgZJnbgFc7E4i2D\r\n+dOphu116iUKQ9enPxHLstq1FjZruRQvAp93H7MaZkZYnO+pCG6UShOlGxnn\r\n1DfBLKRitvnIy7PAFlLOT3dpmC7ERe+pHNfieIiADQgfx3lQTRNbEfonj+LW\r\nfV6L4s1c4MqHg52x/IfM8Ty4erRhDGkwO48HR7o/NWf3SfUor1TtxAFC5yqX\r\nzJx5//ISoHWOgweKFrsnb8adWL3uDRo8fubLX4+ilDaNfxFzfzGOlLe9r/6p\r\noUTMJeUbXV+jUkaYwlZFvz8FG5SahU4rwMqYzEhk/mC73g/jmxboZGvyWGAM\r\nwRRligKnU/3rNoIa3jrc2ZcvxHnzFHKEpM3eHjupKXtLWipbIVS9zjmbYIGO\r\nyD0zAtyAYl8BmGtrqZaoNxtPrKWgk8GJ0uY=\r\n=x+Aa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dd3cd7327c43e2941172c5b8ba88150456d4e3b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-tW9D5wt/rm0Otoza2I2ip9aUfEl5oCjpoDzOdJGQVRSzrID1CkCzIIHJL1jHYCfGbAHCK0qk5x4giV7KH3PPfA==", "signatures": [{"sig": "MEUCICMXx4bGTkwX3FoXSpdQ+N8Ss/yJN9RckSg8S4mLwNijAiEA5A9bq2yosyEtJqwG0aGf5XIcmrp/PmTlnXbYed6PTSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoifRAAjvK61vARBFDmDzXkewF6f3rOV0eGwWzdLFLERFuA9JIorkVx\r\nIndZ+sZZIs4hePJAdrIAjOapLHhoqurp6SYMVQFK1DdtEOcvthNPgT9aBmVm\r\nwSXwVZBNBBKMowjLOHNz1oQmeTDETuZjYmNlGN+/KUvMc8errE6QjdUBBoH0\r\nDY1UfC9OoSxAAp4ojABDZIjUoJERBDBoLMZvj0seeFbRbB5NiqsERK8SnTSJ\r\nrbs8FkwxMtWkAKseGQAW/JIwXN/qxrL01EdLsTM5Ss0mBRlFedw/uLmjYXeZ\r\nb1k9N/grHd+wyLAKyNnZoj/sCkP8BtgCPjvBwnCmMuJNFZ1t3zyeWmHz/f5w\r\nCXds1P8R5zfjXYWDk5m94O8ffs9AGEGbHPwIfoKAyijN/VJYe1MVVZNFekQo\r\nO1rnm9GpTw5NF/CXdkV7YuRpzQ3oaLqfKH1Fk2JIAkBdii9hCx8C6QtMNTL7\r\n+tRj4TxVJc790HIuSz1OZLIlDtm916JxdH/PMOTOEsRFn96Qiq1wTHa7dxQO\r\nEsSGbXDMt8EYNwahFp0d9pkrObtc1kskNpqth44EAPtAw4NY+Z2Gu3GPpnCG\r\nTooJGnZut+nOGfNQsETTM2oiXhivO7OqREXZMhVdl7PoqN8HBNBVMSeP4ThB\r\nrsZEP7TvKCMa8v8eVN06vsWx7XKYs8siBqA=\r\n=rl2B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6691936370059ab2926c652eac92d268270d4928", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-Ix5PyOeFwc7y6VuXxidwraip47CQVJ1Ciq9hE0V+nqPfpN6Al4iNCmTNfjQxeYkar7F9nFTQblL6/kWdU1bZeQ==", "signatures": [{"sig": "MEQCIELG8A/eETjDQIVEFd4Pjpy0bbS6W62hE5b4MOetcL6MAiBxrbCHnayWHcqYnw/MNiSVP+B7dnKwxQ8j9f3mucfsOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV1w//ZpcMLLV6S4crPpA1glFvDFSLwRikxX/VME4g0ndRS6IJ2QeR\r\npKLjFrLu9Akjc4JFpf2/+IA0IY03jeP5zKUulIOoZpj5O9X4bKwHs0M8QOKg\r\nWDqS7ARQe+hIUtM0tviqWshaSaN7Yhk64HlGVEmFqpKBcIP0fzqcs21tR7Ro\r\nC2NpqZx6SjNNGMyishHpFE6ec0w5KniRkIKa3I8IZrVWZYg9uq9c0JUo66/5\r\nYcNLEck/ZdcuiKokNoGBoPbjhhe243ccjDlqWV5OJo0Iv5z+u4TKREZhYKqg\r\nHbZ8yFL6iJC0cI0eLVGnDEvmMNy6LVDWDUbPyXlLR1PDM03eOnrx7+rHQNY+\r\nByIdZN6M8bGgyz0B6vxh4Xf2E0IG8hpbLBLUKTk/G9FR2+MqATdzn7NwtxvG\r\nOIDpqJv+3/DxnymvJjQlUXPuL1jV7ACqgG1L60aYAGSYXCRfSNTLqpL1V0eP\r\nBWOSiynBxuF5gro1R//yto7FPY97FD6Qq6RUEzpJTiJAWgDuI8DqC8BJnoCi\r\ntg0J9W0Kl3gNo53WzTGY3UJNLw03DVV/K329KbrVvgm6CtD1vK6pxOj2l24D\r\nDBkLHTnoenTYbJiE/d7gQIvq1fuoxO4L7cXwMJT37gxmNzHbC3UaSO6RghFk\r\nKdVXj1I9RnaYTRWJ4E83uTaS7VNh6rhPpN4=\r\n=rImB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fce29674b6648afa26d8c93b2d09e371169421ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-OqdRCzjLQeDZE2HUJyTWJdnOLUvkzGvcR86dIYc4xXTgONL69EQ83+VEE41fOCzYMc5we44u9WetZChDmRSMew==", "signatures": [{"sig": "MEUCIQDzqF7z6s0qPVXzduIHB8N2VvKuszDF7XurXNlVddMNEQIgYaPn4AGOfnt6DUIAxzMNCmseMCYTKvSqnLYhmhtDs/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp66w//cjUCTSWIMQgEzcw+4JvFxNi8YtH6cNfKTT4WPws9E2leyEgb\r\nbTFFFFozLdpWkOdYsPU/VSMrCQyVi4xTM8kJHaGX6jHdPxoQInX4e3nZphuw\r\n//3ZtdAdrfT9qq1vzAAqGmN59+58hGavGdQp8TR8pmNG54dDLU5xDsH6j1E4\r\nbrzPOsdVUztizNj5oKhhqxLeySefacRHgnNJ7mVyFh9mR0iGgiuP8XGpR9tC\r\nv2b+ZMRb9vQOsPPvxKNJ6Ox6WFFA+W0hEK4cAcWhTCdUv3C6Bq1qAz/8/C2w\r\nkVyscveOvW4uTJXvKq7oQS4WpQRCMn+xH1x023CtPEmsrAhLdV+Td5mP6B8l\r\nOafDR3dFamYlQcO8H/vMolt1eO6gAZvLHirhIPNnWliPv3U2aKIn97dj5XYX\r\nZ35nu3UNDRFZiYU8zp7smTUiW0ewF6zhHCZO5l/KNJKlcj/OlxdmjPp+ynnu\r\n0qA7rXDSHZmaGcC0uLMgSicclUC5CEex0EewfHqyLHf3Ay0X1+xh6eSyovtj\r\nmpPh+wdYmRDn/dgEpaLkaXxKh4Z6enYhUajgevbwPVGxnngLjIsRukfX4M30\r\nMvKQFIoSXi3Nu/6OCIziw771+UpLNn2BovbSbtw5FU57PnqG875p83IRS11E\r\n80vIvKUYnUUdu5fnXLSLyeaNH0wBWRychmk=\r\n=Uuvn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8a52b074619633bc23e866e37fe20fc259d6408", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-AZCS+7Zn8RtTMpZ8fkT4SJJAeLVNjODUvF1vTo6iksAgoZr6slyp8Z0IQYVvyg1NU4dC6qb7cD/xUit3vWqmEQ==", "signatures": [{"sig": "MEUCIBpgsLQuRf7xXZM4qw9au5gShiKDVjJdlwhZ+HHm0Ui8AiEApafx3ZweUecWNFxSM7f3SF98KGHt4HUxs/GE35Hk8t4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15160, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1yA/7BNGsyw8OBwcqp2GFIgf/In+Sj3CawZ+zCso3CSWoo2lTjSyb\r\nEcWL1Rggg+19jZ1YoyO51cQi4UhRS31LXBjRHG2ODsVmzRUbBsoVzkh1nHaJ\r\nXyNHWJf/DprmywZkBSZqDavu3934uSyNostOkaJldJnjIwmiSCZEcO2ViOFV\r\nx+tmS97r8/30z8k0nJ7BW1do5mIMMnNPW12ba3mQsPZ+6bZH+wecQ3q4u+w1\r\nDinI1pVYCqDU1tmM9FOz3NZiOMzFjei/ic9I9uePuW8N1Llqt9uIxBm/WERV\r\nE1Xxukd9HRVfdmHfYN4wEXqv+b62O7I9FuvNY6t6CBR0ewk8I9/+3+o4cfpy\r\nHUdrNDqSAby4DzlFcN2JfG6t4L+erUcPNiwamx6t/7FcP2nfT17JhGBjOJVf\r\nEJU864AmfIioI7XZaab+caJLNZ1p1uxjRqrE4FK37pVnOumbSVP9meEpvOZf\r\noLHivtQEWAtPSGB2M/uQeEtwXj9gqg0Pt6BfIGDZDBhZwCsfBPwkQV9IIFbv\r\nHpv8qEEfWtYv3smyXLZeK8gUOopNaIusOqWe4FQNXqxBG9couwTEjsy9p/nK\r\nALlJQEPjtGyk4Xs5WUyediFNq54GAEfUxkq6RM8dI3KXvWc2QWHDjdq5z2bW\r\n92y5F1R/LHPasBF7YvCAMO5ZH2+SabdT9ok=\r\n=IJUy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8061f11836b6bcee83d466f4c70b5f9772fa37b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-qwr9SoAvnrXLtMjIzC4KVUJ0ORtqBR0BioH0g7cDdtLL1jfztZDb5Vb7VR9efUc83zGzk/G4s134/bz+CgWwUQ==", "signatures": [{"sig": "MEQCIAjrn3PIBxgOMALHEm2jIuU74S9b/dDfDl80qTV+BkC6AiAICYZ8wZkQQEDLOhT6FY1o3Z0iLYvrKyS0amdSy4Pb4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXGg/9E0A38qRYjjOvT7HutqVLsDcgZwOvCzTu3C2yPZxi6cb89oJ5\r\nCtGYL78oSYxXowf3MGPNr5TO5qE5iJFV165BJwsT39qpt4EZjbl3gRoP23eo\r\nkLTG45IhhB56oAp5iV3uIgXLAZHZ7Cfk/D3TcZuJJ5MenWlgXWmM6r9x5ws1\r\n8NvDTdoO2tYmpWN8WxebKTcfUiha4agqrDAnD5CIb/8cQM39sjdtVjCGvzlF\r\nZ6P8YV+gmex/3oVB2rlWwjbRUEnwlUwzgE+Ql0czv27pRQsCGY/MT3YpWhSp\r\n7KbrpjIL09yZqtS7GX9dq5R5EPuqggMPUdamT1abPDja5lWAJpPWwzIMha3c\r\ny2FgkuXvxKqki86ty4aXqM19dOR4pdojBdKRORT99fSJa2rQ/9/uUaB9aVLH\r\nxBDdZfY/GSuXz248vn9V+v2m4A5rgM4/2OQAHZqt8mc7fKM/86sWpuzM7sjx\r\n9/Ko5QNPT/k94GwRoHJvl441XG7A34SPcdnSa+UeXezM+xdQDlxdok9xQxYN\r\nGeZYDTDFTqjU1K/2siVAshxKAaIUyrKmA93nSRMwFABH00qlL34EjvM589qV\r\nFcbvYaqfCqQIYbzv4GYs2DdFogOqMCl3UXPZaQVyES7REHgFvsWt7zhzTo3o\r\nHz1tOeQhTJ6vyzwDF5gsiMNoEAGtxeB9ZX4=\r\n=NIWj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d8e78fd132267737941fff9d05d5ab99bc730bc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-F/KYB69gdQqrq7kjFnoL/JnONdmYqxeIhB25wq0TuVTegJH4IEIbp67JbUrfZsawLxV2aVC2gOKaAum3v8gwJQ==", "signatures": [{"sig": "MEUCIQC3bbpPzuwrFFRvcABU4sDd49k0s/ciUfIpwu+Zie5guQIgb0eSNc87kOC7XPFs5J1axFd3wToWDwO5e2UYnalvZBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmozeg//aprbrUTLTUk7D1FT2cDku2D6HH0R0lSfM1siIbvOiW1T+LrB\r\nohRjrY/kckXz3y92uZwNKrxjZ3Unv0JXs7HvEc6UmsqnkjZNJxanzdiqxf06\r\nreGG0R9l9PaeNOCpglqkvz/1ATuuoAJcnqaPaajcENby8mC7Z/EPE4UTlVv5\r\nwXJNKmTEVXiSt+WwaoapXkg0yhIQ9v1wLVu6AGKBpT3tOdpVzw45km2U9CT8\r\nKgKWCB+eksPwNt1U1y4xXzI7SaX48+AZlL44icsyGTtGNSp6LJVsSse75ZH6\r\n8A84AcGvUzGEmOxuzcFm0ehtwpWrkZ131SF26BhkoSzTqKACf32Nt7Ab4XM7\r\ngwKzcpKnH6fXBJwsx58g2/VwkNi/mUY1cxPCjJbAXvwGMkFCPaPCbuW5TqSO\r\n3hNeVfq4keG2p81nz977gmeMNnlZHYuiLSQqbAT86+EvbNuZ7GaEYF6C3HuI\r\nuULgGO0juZpuUce4op6UXHJXDaTI7O1fTfYA1AZRPY5mOLVtdlFIknC5aVRR\r\nE49xblUnhM+FNZjhgaa218auLUXrkXfGEfHGrmpfamVne+HUaM6tp+uvGOsr\r\nk9rKNpwzXJFuOnf2bKGqrJpYsWAf+dP6x8at4lEWEbLtsuxcEQzBTu6zJ0FE\r\ntONsGP5NrHAbO3s40T6nzxsik2Ngw2usf7Q=\r\n=RprN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ab4e78fda49489c5f529e8e8ce674565c196cc6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-5OuRZicj7PeMzJ9hWrCtjbeM6HQixz8fxi4gtJogrT94wRlB0kkNWtYYXSRXkRqBhMtPUrSC7bPzPy7KsL+oQg==", "signatures": [{"sig": "MEYCIQCaeGl02gINS0b/eS1GWGkMGzHYuBWzn3FokqfugaooUAIhAJHdVAeqh91dOIuAWHSKJZaUdfhutRJzhDT/WaMWIepq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvswACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ0A//W4PXJwPPZuIq2oiaeLMdKYi0SAxNi2hchxjNBC4iB0u6GMOB\r\nJq8NMrO84moF+TjZ142NBOyH+D8ny7Uoj65iw9VMS7bigviAYfsakfsCQ2hF\r\nKfChQbnvbpXqRAAVVHG0m1uNUBO0CK8uAdckVjLSrjfDKFCJV36XntnpzfM5\r\n8fkwgxdW72t58tGp6sSQK7EKklzbzcV51rKZKo7rgRINGZmOqSTla3hlhz+M\r\nsz9FwoZH0vxMvJ4Ufx28CdwRx6Cz7gaMHvup3/5WfNC/tQnlxQ3tIoDMLg/P\r\nTojgaqVN2csVgq1BL8ef4s/F9evTxqR0Td6848yk6VL7gQ4yF/taFtvkCMPs\r\nMqakfEB9NG7+P3SYQYhscFpdjbUJLPBUrH69wm1tI0V/X9IiG+2llX4jOkXB\r\n9KQjFaTnYf6e9oru1aoT1kpepyxa8cNiHN4NV6A/LOm+cJ5ap32kEEroFIO+\r\np54uNxTI5eII2dB4GdAK4o363ylKZglWBrbXWWRMn2sfudaEZZd+Qq9lKmcz\r\nv/3EBh2IHoajtFEDII/1+clhzW7eADltAZgWJVB0l8EiVQWJXgQabQOnZpdE\r\nXtlZsSbzsY5o7SNZdmwOsAsyw+XQelL7Sjylq2na0SeONYVtEnz3txVmO9FT\r\nHAwqP92WXmiKo+VmfGqXdkhAfRNbMR/1NaI=\r\n=qszz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "483dda97846a3da046069dbcfb301f762c270470", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-H<PERSON>Agg7eGet6aK9wuEvoNgIpPD3UMvltn38t3dvagh0ov4N3t1kZt2d1w348ZBweAw8/lfiw0osUY+qZnwbzCYA==", "signatures": [{"sig": "MEQCIF0Nvo6uKaHX348v/+VI57UglmebhlejULaHxSCSJOQ5AiAKfj9waQxAb4tVpjeiHL8ZOAah45T4wbANrrICGVAj8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozuQ//V3s5lkLtSkXFp7wgSolCEZwGYbFsqb3QKjdL+QLLQi2NcrqM\r\n/WE1oZjsgVdUv1+Vi6P4lpjJAnowVBzA5vWSdue9XrMDDjasrbGUq/7E4n8t\r\nny8k6OMcnjy9ZsC0YqiXy0Y7uaJLyBckK63u2E4m2oqJ1ej0OpHMgEZQESje\r\ndT/uG4vDP8gfvc4eQ96Q+XFav3me/lDRsYDwxyi2IEf8oY6dAqWPFNOtdiMt\r\nfriSk3M/SNxg3cAe4Lge7a+LuvfkzULkc2Ia0lgxl+1njdRLW4B/lngvYxyr\r\nq+K5FI5ClcDpboezWdiSr+0QZ2HwA08jIn9PG0URFgAF6sjV1iPzlUyt4kso\r\n3I4S5SJ/cHHo8s5wBMWkzTB4rBtVq4vJvhXXVgm/hy6GH+u9SSzycXp5kbAH\r\nbWxYklBx6DR/nVaj+blyvOgMbdArX4+tLVOc5xzH7OHktlJLqOT8U7SlWw1Y\r\nKGwTB0EwcZUoq5nYlXTt+kYjqOe58kKsYueahm9taGWzMnsfTcj2GTF1FrAq\r\nPVMoTC36GNXzvSIUhatJD82gVuPaDZ3i7vw2KDOz1BOwlk21gsqlDLpfU3hC\r\nJ113Qf7rG+ng4UGyROMKBKnvW2uBJG+nEwquQo+WuJCC7Jn/0XfU81Y3QbKz\r\n9l5nFIk3Tk8Ws6+0SiEH4U4yBXJCBJPZoqI=\r\n=JJkv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c7916dd3f535f9fb78f34b29de97e7568656d7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-fExWNn7jeXeFdeuv/blSESRlrCL8vOHBJaKzd0am7f/2xvnRjIgEH/SpkrmNcMDjr1hxi+3AiAZ6Nf1Sp/59ew==", "signatures": [{"sig": "MEUCIQCeqRQmPqaSEEfTU25IQT4QaQ1KJy3E8dH705vxZDu/ywIgYVNcyjkqGmobPUB11S+8ngJ7VHUhaq4xT4aK9+wn55I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSsw/+Jai3X8r+8gCZyeJ1tdK5sgvEyjKUYmPcEBHhprQgABF+QVI8\r\nc/l5HUwAnTEV4qNkpb8X5d9yAFpCBXIlApRYnViHa6gWMaT/+Yt/8f0bp3Y5\r\n0U2ZUjYIwtvTSa3YdVTGqms6cjibLwfF7pK0HxgqOtlwjKr22zLFMwMbwO1F\r\niqGDNlvbWcT3CBDM3GbG9QGt94hJO/AIt+xyL8hCfWMa5s/SrMMzaP7Bhkzr\r\nFIQZN08bkSe9Enh+LmFGY3pq5SzX5+3LdtRwOuBp9CYP+DbKWc9QpHUMna3k\r\nrq6flTG3y9+3f/CkCBxkQLK0he9RKOcvz/wEQKZ8Aw66f1AYljb9j3tgrSW7\r\nDPyBS5PMm12dcjYlO8c883ElysbP2rt5E3oF5mwQfPoUqDIWET3kLADL6PNi\r\n2fpIuJPn4Cb+PEKDeGk6xHprbK1Wo4vt7FE+evmJ/7ySYTagLF5h8SgwatUq\r\nY4YFHbGZgCDWwprgNMy0npnNDiMVofEL+5zkzST0cMWFWjm73PFrC768eRkk\r\njpBttgDL+NPC5sEsfggn6P0Va88/16JGBRhwlEJ+FG3eHJc/NXJQc+6lrH4y\r\nurXjOhEHSm1R8I3qh+8I1BEwx19Fj76d63BeJm3H4AY2InRbJX7SdnNEDONW\r\nBtzT0KV0fQ+HhmWu29FPkZtnCdC0kxM+cgU=\r\n=SZul\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4a53e649e4b312275d0925d0385d113c0057262b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-bQb0OkprYaP182LC/ZeX2HAWG0sIQNK8hLLVS7KR3mxLIZjG0Uww3PhI+SPInJTzYWgcK5a6VadyHAluRGmKFA==", "signatures": [{"sig": "MEUCIGMBH1srjdPeOcnz6rSiNgcphpUkarTfhWkUFMotI/+QAiEAkGssLxMNcqg9ex3ilYxkvNuG9P+v0tSABTsEm94I7zQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+QBAAhisBRc+3Yv7/DmnzVuktHa5P9fNWY9XejfZewIhz1lj5JLlV\r\nUYGjlApzeJl2NYAvFvoFYyjfM30HV3hMMRZeM9DKr8NVpyggov6ldvklMSop\r\nEZPwfhE3LTCzgrjCBdTtPP2mOrHnJeUegtuu/tMfuqJxY4L35YliiutZM3nO\r\nM467PhelBWCrqT2Txsm3Q53SXVkblH/ImDruW3gdjtrhNPLQ0HZLz/duG76S\r\nljq/dS3b//hXLKm1uE91zd/z3k0NketcSfUZ6nHsef27D/b0+fsbzRzWBWlM\r\nzjxdxW8uWCcLsNjyBit1rhoeTXzlESlXvdaSY2GW/VQwMDbFkOjg16P2lnw9\r\nGMfKLpWo1J5/3iQxikIAoUr8M+KlHM6Rm6rE7QpE5e1o8yhgJw5apww222k9\r\nPrJf9TdckIqZEpdM55CuB0tc4wEnuodkzsJKA22MbitPvwwVuvppOGnZIw66\r\n//pbJsWikWRnnDF+XJMZRPGeU+afRqllT1w4hpjvhVI77/AyzRCBdwgnsWqb\r\nenhjoKwoISokH0AmJgrIKO3Qwjnx68ZA4MjkyhiHDkhqbwiS0AyV1iw+ouGK\r\nKZI6LWAYG3lI5OMXVGbNhRTfkIDmOPr52HmyrPs+Xbzy1WCA5o7KNa5knPWE\r\nBvfAFNb51WUHDDMqUh7ERUa/UGfjqekVZ5Y=\r\n=pb82\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-toggle", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f4807beb95f05bd4e709151ee65aafe3678db25f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-Qm8WrFu3LdsUbUXeaOCfKghxoNTViBUNChyxUs+FRY4LDxL/4VvKpqQ+JM4acvpgYVqK3ZOm+OkTWe4Grcv0oA==", "signatures": [{"sig": "MEUCIBVafQyJWjZcKJtt0iRC3O5yo8aRcmgmAzkVA6MqO+dHAiEAlD4o8QsaKSSscerNTbokoqY5H1oUzCkGGX2W/RWJwG8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaiA/+JT6pSzz5SAh4xht6PCr0/nsnUi561spysLQiLBk+wtTCFbH9\r\nI2RvKv5NqEIJ7IYgxpyhNeQ2TNq/iXUZFthGuNwV27nPH1j9rmL5sva9hLOo\r\nI3rGkLrFFW6ovKDm5csscG14FrRkZiLawrqXFMS1Iw/VJGqbiatipINznoHH\r\nPYT45FjEaXAD30yRIxch3ejrSkMoGu762qU8DOQHZoeEavdMgjTsuV0O2rXj\r\nqBkCS+PvlWt85eaaGG6T7EBIEf4YEcxPv6cS0M5T10ZRj5UJxMyV26e282bV\r\nqJfn7Euf6qqQs2Qah0CKru9d2pc3f+M5IbcPcu2kxIPny8e9xM7xpOb27XEK\r\nL41AvO8r5l5V0q82XHj1big8r8R5kkBM6SLtrudpt3ivDgWNuY6PXpr3F6qr\r\nPrtfXVYg8lm56NEBt8LUt05xipNJoV/5HmzZc5gepENAldzIjKjtwWsVggrt\r\neyNNQTk8Q2YUGrGE5DTb3UdWhdMVdVFbn041RGKHKBG/FfxdEcscsoSxYjkZ\r\negKilzXanyCkk51d7hpMJ8cNpxE4B4GVLifwt38qVyT8qqu5ISqgHEG7/PdJ\r\n+QFQRdHkayuXg/3Rr0c+E7JQpabDlxWPBRN6ntZIo/IuMzrjWbW1rRNZoLh0\r\nMufOcH/j2Mp5/8LekyNnKN6w5MqLRFm/ZJo=\r\n=2Frc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "51fe7374f886fcb28c84e5bfb284135c7c810431", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-HIxHZfL+qvvzwxPuYoZaZgpxuDB2YXzRWXuUuM9JN9zWRAEWCs6zZIisPHj0mtqgfiTfUirsEjHgDM+Wf5jMoA==", "signatures": [{"sig": "MEYCIQDQE12mpYnPRNYHGj/OJFxjoBW8rVxXU6wzxHuphy8VdwIhAPdlVwMNgmzLTcnBxLVYwX742XjTSmAbRDVYEcZVCAL6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Ev2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwUBAAhyEWHtHrlGagc0HWKVctyDqili7d35KNZeGZYFJSWXlqdxu7\r\ndzJ665lRMAL5uQiGY+H9vIYUCZmrBkRVIS07AepHeU0JaXzHnT/GRZOK0gsP\r\ngRhNiEGbT2cZRmIVdUkdegdkmMAHoFuSYHmUu+yrzTmSebreGnJgGzRJFQx3\r\nxWPOVLPMSHy0V4NxdMRbHXYRSwkYwwa0VLOdQ6r3ge/Oiey5XNDkSQTLXoUY\r\n7Oz5bRoJCWITgCnJqZRTtxJ7pIu2KWTSIvAXLwbRUB431MK1hO2ZFz7idqLF\r\nybf41URLX5ViDoFfya6K64Y76D8s5T5gBL79OBQGPFyxGlQ7mLP7ZyVrITsk\r\nghg0XZYJ0kLLTStMRpM++/+MKPGvZaZHNV1RtnbJUBZTb0/H/j5b4/JmbtuK\r\nlSzKCVBytCEYvTyCefVbHmJQm758jtbpeS1T8CCZqeQatTNrYW/NlclR31Ey\r\nhsqMxX8zP5syeYNZF455qnU4WIUaJ6BbUZU8ho4V8U+KB6nLkaast7jAUZ+k\r\nmypesTt4kvo0cvjjHMl05FKokBKYWMELN9yHQ3jCKXPVUvPXXL8+d75komwT\r\ncuiaAN/PD2+neopbvwpVgx0qdeVaU6bNUqcfIoaDmwrbyn0hGm42yd3rs5X9\r\n3+YgfTOc3E6lqIvimDKP4tiAE+YnklYkD2Q=\r\n=7qqN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-toggle", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c8f655497b26410766b9c01aa2954159c0cb60b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-RvY06eyDlZMC4rZdWK8jNovEDKf2jBvYFOB4rkQ/ypMOjFQuoh2QodlxlGakrZDrLnfxzyNnn/pg88CWVtAAdw==", "signatures": [{"sig": "MEUCIQCiTj14YHJ2xaxDwZ6dBwQucCgV0go27aQJ7sRTwws3XgIgftLwFzWG/hM4iqW/F0HPesPFnGXmjBRb2WhnHY2jFkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpjcw//SDi6V0BgKFo22PfJRr6KR1BBCKzHJKrh681VPh65wf1Ige9D\r\nh2mOQiYI3q8bIVxP+9LysnuUn4xItmcRtg6Hvo6l+FBm7qCgcNSRcC+grH/X\r\nljXniHbM0edFomNFbDjOnAH881Hb1vHGl3bgbtlYM3iwS6BbILDFIky7bN/k\r\nB+kFWBO09W0cLxrDN0XSsOu4Rmirqp59KrsYvNSETlj4L2rqGQ4qCpCV0vvB\r\nk7u/Q7NPxoC0gqckl+tkbj2nVBnPYsxqccvg6y7rhZGL+q7kU693GVK5AMWn\r\nSniVP5hdgDQD9n6rYtTAawJamkhhL8rg55kBnlTSOuofWQCHO12AwgG1yot5\r\n9Ndn7z3NZcHkOVtCMeeGxceWbQIIUFUkAYxO++w9Y34zKWjh3No1QGltKTgQ\r\nnRuCWn31iFAdWH/bKGpMOS3yaQm9MDXz1iYB5OnWpSOMhePNJObSJ8jItatf\r\nEpkbJImCnPC+xTr2M3LUlH4bYenuoz9H8/3scy6RvPWIOxN8Tm0f7QR3arwK\r\nTk+goldXQg8wmCqSZasxwrg2p/p787BJyCBze2MwgYYO0EeAMb7RIoUGORKq\r\n5AMxMkyZuheDq7tR1MZcDNRDg0mI1KNnLcPIHJjf8Zrwkasn7veAdBG063Zt\r\nGxjcngtIwxaf+siC4pwD76fFOK2IMmh5StU=\r\n=jJG0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4fd6178c3d477f833616ddfd466c28f974a1d710", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-jCMYDfFgFsxGS0krBA2PEOJvtwV2NoWFn2YkuhJUsiqx1Pk/dSOeoFMZG3RVqEstAgrujgTYG6ZYLxZbG0FsFg==", "signatures": [{"sig": "MEUCIDAvMEhYDkkoMr27GBInXUAqfDcTxyzN2UO9cal5Cgt4AiEAtiPxJPL6j8eSvmftFpDLByvthn6ounbQfQf1xpIscN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmru0BAAlibMAwRFhsgo1/RpKvdKyC7j2MmX5VGibAYz9ZinJonmeoFr\r\nl3IfG9tZxNt7G1sOV5G5ilWi70yC5tkHcBYOiSmSs6fLvftTKl7cr7+TbXvC\r\n4phr9eOxPt7ra5e3xk307ZvWj1AaqY1gVTkQq8KrQmq9ksFdcI/kGK0FdENs\r\n9h1xsUoSu3HYgD/JBnBYFblbMNto2sLjggv4KpVFl1AffwIE79/uGQfEYY/Q\r\nVZ5gyO6hn3bYGfGq0g8ex2icla7ZAUCY+CVZJtImFExxfyDpQbLohknTfBYU\r\nWuGBnzhgThc+SrzKGYINiNNSRsyxMS68i7gfM+LPuNWvN2kFrVqfbv+obkqJ\r\na2VoAFeEPzratCBjOSWt223328VRSAT5IRivq7L4RxVoUo3khNLZHzzo6Ff8\r\nYtEGpupRxYWfqxnvccE2TEchgw7CL/OfaLKGybGAsZIRg4IsFEz15nXiJeN0\r\nxwrxX+LACOP0fRYoR12fgWE/rAkFMDVcsNWTL848ZGcIsbQgETQeLymvaxz7\r\nZNb2lIq5F1c029/EJYj3yKQWs0EqkgNpf/gZSNHh1fgSH0KUZH7zQ9Lp32Sg\r\n26hjr49fM/4FF6cVPK3CSqD0FMFf1GDgiip0RoUZby9lHjgWLuySJR6TY3vL\r\nPqHoZjys64TEVfoXmvhH3JMIR3DJs/640Fc=\r\n=f7uo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "62eaf75260c8207f4d7a361ef67a90671afac508", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-doeaNhTx9ywutjHhFVvI9Rj0zOOKuyt82LzoI2VOkusR4OpEDQRzqthstzsi7iDx3VcywwLqxB75IQ/7vKBlYQ==", "signatures": [{"sig": "MEUCIQDG8zEgON5VUJT67EcAmx1jOg/37IF1ctkCHVIX2MATDQIgIgBtHMSaiuQQet/uF28o4z7pN40mgAq2pSz1qdvCcFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKz0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzKg/9EPFrj8hYJOWx2JNOdJP5giJ+A6zSN7r1LQS9O9rqR73r73GE\r\n63IipA368IySH7NwfghS9Dt16dIHF4OfEeH2gpF4koiJ/s9vG31rQ7BBJzz7\r\nH51bBSPvW1vS+zKv+ZDF9/s9B0MZjol3xJ71gUDx/tQxi1yv5NiACRlDTjS+\r\n1Y1uN2GUwWaVbA02HA6CXGoNE5zTnXgn6NjwgLs7MLN1Z5hhrMPlcgMN/dUC\r\nWFHRuf8bkLF/+lGViEM4tSGmrArz2CgW16wTzzLWQb9BdCVTBSIZOY9LUvAb\r\nFVQrprUOy09njkDTgdfKUZ4jbOFpQCl6VIB/UNO9QrYxYSl7eb51PlW14qVk\r\nioUNbftRAArfyfcdntW7l/jTQOAaoafVyXg5ohY/QIXxwpDmBBqniEQn+DRP\r\nc/iBEXkbBZEiYTJE0VSY3/YX3qNevWQ+LX0ACvdHt/RrDDzje1/PvYIOIq5z\r\neyzAa7KZ6ErS7A+457QFDUSkIqxnf4eba0in8JdMV3a5LBxRzSy1j0Zjy0YS\r\neZeEYskPpOHvKJYEeNxHazZXnHXodrEvxHIbsrTcMxbULJcr7mwiHCENdGqg\r\nLwT77NYWo56OOjRiTbFhZo6iz3MR3k9R8eUCOD68ohTjxvreQ18vr04KlX4h\r\nKDS0yva3BOds/Yne3nEKmZ7C7bWlZ1IX/do=\r\n=ySOc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "768cd20f7babc9189f607fdfddc2c24d3e7185ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OC9bHF/iKPGkwMUB+zJJGB9CrnOvP98r/leb6rCw550qJpC0c6x7qjzcz8NBO+vBWjBQM8GF2Fij1YOMubEHHA==", "signatures": [{"sig": "MEQCIBhUIxazHLXIPOTb/9BALS/51qZjv17wJpwPWCYhhYJFAiB2Y4yEhcmOrutkFyXRptdFeAYaSDl9Y61xbup0B8PJDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSFA//e7s2pqEL7GM9RvIoRD8kuN9jPv1HXkjsYpTpP8KjsOZlx5BJ\r\nY0gklDWCxQffz7mbigFpvXvwPJf7U7k9zEtHfXJxcnNwOJtJTfhy9n5NYAbo\r\nsp/kjNztxLJOZI+uo7Mxoj+/B0PTrH9RDz0n/u52aileS5K1FoDEyvuL+gqp\r\n8ZUA6y2N63sw6uu72+o21fPLQMI4UGgJ1+Khb03EeJ8/oskhsYu73nEjH5gL\r\nBvNKgApb2XnhcaONqD6D1ZZikCqzqUUZ5Il50aEzgQzfO9EqqVJ4NrSdTHQY\r\nLpcke/FvjGIZsHaqZ8ghLf/wvn3376gWS+X6FQ5gLZnzUg3lgKJzQkVkVfxY\r\nQ/lgYasOkni5UdNCi81IN1OWHeE2dfeL578gG9jR5ifzd3MiVeDHXRCv72+q\r\nBnyxNpKUZEDOeqbCMaywm4edZvssgYOd47vvBUPJCyQqcDBsMlY0qyUfgd+j\r\n83IUeIGcrk7CCbqgBkAOOS+3bdD5yeer9DMbX5Ob68n45BYuki1JxGqHDwNF\r\nBcW/+QfrWDre6CQg9a4nEK727JI0T8UcbRgrqy+CIFASnjNW9B8v8IZ7lk90\r\nK45Jish1cojk4vCO4A5Sjme0MvoX8bARqmuugH255KhQub3Rwef62O04ARfK\r\nLD2aqKa1t89rncffgMc23xF67NRND/YYmlk=\r\n=hx95\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "38b9aea6c31e1cb3f663783ee463b8c60a733772", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-uVWpim6BFXVwgkDPlYpFnGUo/xUlK9np9RzNH4zJC+Usf/9kJr2WVEmQTuSvEGDWkop0dT4iuNruRyTlfbvj6A==", "signatures": [{"sig": "MEQCICSPWTRk6RdB5BvXPnkZqWdSnES5Xcj88cIdaAy/Q87bAiB2A1BzLybxBUdOnstZD9mmnl6x/rPoHS5fITwuHB/Zpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsBA//VbjfZ27iPu5xg/6Msw2jaddcf250ZbiIJq8ZpNnpNWhAidf0\r\nQoGXjW94s11hifKBiDUs6zfjOVNT2VQQ4v5335o/boa3gLjA1JQgfaF3S1cI\r\n4zBFpfSx0ILNeM5d9LW0+KEQK6zOLrf/jFIc8AcJmp6mer20/ZJMztAYfHSV\r\n1om4s5zbjilDmqnq0p84GaoHlUVwnZGudrg1LsuSbhP/FS33ZOv1azrx1M6d\r\n0wSWdBONFVeND3CpKNZvZjZ+bYjm0ZeMyJv4G2naX6x33vmCGQWD6Q2t9A6d\r\ndSLTCRsg6Jn/Vw+7NozY+QWZQ0Y1ttuFB5ilW04yPc/g6or/Os1kh3m6sZJM\r\n3Y2WYFSAnNQgj7qDx1BzoFgOCEc5HGLqzYogMmIS31RiM8QsspTW9BNbW30f\r\nntwnvxs7stfmFvUicQ5Ao/JLwhwS6M2tkjhrmvJ8kOAysb1VdlKrt/wE+kBX\r\nQ1ZwQMl1oM+znR9kSkCRCHszvJ4HKC7779bLxkFP4eM4oKQkPBWo1AZwCZb1\r\nFCaJa1MeXyXkzlMZAcEq5koswZE2heJWe6Iy7MO+fVodmgTXhlHOm5EOolcm\r\nA3ulYhUj9crshW4Gy/XBEFIuVOQTBunm7Pe7hlJPVoe4VFbgWtWPNuGrr9Qq\r\nBKS6uvePEn4E5L6cOguc90mPKXw+yX9Zz8U=\r\n=+D/I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a757a9979e0be6eaeccd66e73f851db534738b23", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-hcRGjO+qVVXgWx8bWtw7FwR5jXQ/Ylvrg3TchcKplncFmR0Nqpawzl79pibj7enUAh5tI1VfNxwgm3ZpFYDquA==", "signatures": [{"sig": "MEUCIFos/oRM3fGxqj7qZ1f1yaQ02unfkPY/kQfuNh4Le7ElAiEAtEa2ywL1OkTC3A0vpfaf+kLP2BCjCbZwYWHMcLlBNLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo85A//WBPOPO8dMUNaA6N4oL9XHYEBARvdCfkyHPyzxr9EeWlPNbuW\r\nQ1Y1Q9oPUkXWRD4shFduVjmlsdjz9AAwYoW0YWbfDS83rWpgtwoZTEJRLD8i\r\n7I4pUZbBUj4jILh71FRu8PXyM+mrs+MczX01l8a71nHi/YTu09Lk34iZ+d69\r\n+YK3Hvreg0xb7gi2iegMYHdC8wLUTQC797SOYIDEVpJ0F3sdpvFiAAzmrCbE\r\nsETapCOhrRmajl/g9e3c8I8zjYeL6+cM92HGr72MYxTJ1N2Cpqb4LVC6JoRL\r\nb0RLvYjhvFpgaYzWLxScdUwubdCsh4uk2glCz7p1uInleZ6Ep2g+f4Pzg2Px\r\n8kljiYvScScHKx03XVEAZ95yWJi/lliw4QdzRV0fh4wJqBFMbCVf0yEb82c/\r\nXtT61JViBRb79yQX6Olz5DDc9F4HDXBpRZZdUAcjZ5rodqFhTCbcAo2p8MV9\r\nq2QW3yaYvRnLmx94mI/Lp9VZsBPdv/4pIGMgplsUjd+jHMixv7DehNNPi/qd\r\nGOs5dHX4s6lyOiFx3oBCMga1NcmEOn4kJarBS5OplbBqoTcLXJKraqfrCiAw\r\nuYM06QmWJv8Ixi3tU68q3XVP1n5plQ3stfq5nw2DSO6prevbnDFtCRGy7QI5\r\nhla/V1HAOqwJIkVt+TxRX+Hl6UfQp1JnP30=\r\n=L0dN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "61e3264e01cf4506f086c5437f64dd6949c9490f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-NFIlOhjwBeRFbLEmaCr0lDms/u1GYqXEDjfo9yh1+toX78KSPXjN+3uJLWSoM3Q6UrOBOcOMSC4P4okQxMgygA==", "signatures": [{"sig": "MEQCICLig4P0Ned6k5bRz+6SQG3AMrUueXMV+VipCRJ5fMn7AiBvE9o9gJ+gHzu+RNb6P+p9R4/W0VJQe1Nv3q2VJIWm8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwP6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCQQ/8CIAsE/C3SZe9gSLnU/DHmkuuRSa22L7gq8ub850IwoT7asjd\r\naCpqhXOoHoZ3C6U5LuAknicyoVm2sERKx8sQFAjk8B/XEi81jsgvJSgauwkz\r\nZgTcZD23r9dDPrqy8+7sdF7sfnVFG9GjZaydSOUwUnYBXEXENKuqppRTbfFo\r\nqaKvm2lg8ipnfKt+l67jkD9bIWaT7tVR9xOcWEMuwXzXwrordbv/RyDZw8kf\r\n2KM01XonkMRkcTJJwZG8EQBkbl75817ovAlsRBIictRgXfDpjNumW6g2m5UH\r\nOC0WM0X5DoCosNvGwkOCl6JUOQM0IJetmt6dQix8FkdIw4TaaTPEy9XSLn+B\r\nZnthKdaQ2BTfWEggk8EJy+5NAJb/PER+KC4aIbYPL9bMHmlgeuCijzvo+zB3\r\nXt5hVO8+l8R+LT2WGdGEclZkO2EDWGiARyYTpf5wNRvwgZqHbYLYeMChBR7y\r\nSBLO88PyWwcUZ8SHVSIFMXHwPdATJHBefE0X08+u1KPcAelkXpp6ph9xerOZ\r\nLIbvKjQPRawIFIDCYM4dnuNogHmM/uHDxAZ1eRejCNJeALFUyDRtCGcyPBBK\r\neqfKaDjD8VVjU01EZop/PSBZfiqxCMv48l1aSG+dcKqQwQaogj7eAxNeZ0nM\r\nSLvRfK9SlIjZwoNyjMbeSkAo6aJn1BDIA5o=\r\n=f151\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "658579ade43d7e7102a999d89a8043ffd558d1c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-e/qPClctym6P/bRkEyAe2DLf/WXA+i7xs1+kJ/Q/pi0wqRvglnZ9Q6jQeoN8ThLaWvrmA6HhL5t7jdhcOgsCjg==", "signatures": [{"sig": "MEYCIQDtqoZj7UmnqNEeCtjwaLHrHF/2mxDqGqs4b0r6GEmmoQIhAM3Edb1SO7UoelpS2EZts97nMg9yd0Ek5zbvzDnOuXjD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprDRAAmGIz9C2vExDZoytBjBRvddCkPmyjZXdNgyMKrR/yIXQeTDHn\r\nisAAUF+I+fM8PT5ffvLEeDrBAs/v3a0y095Efs9e5e/3Op2ux88tymd3FZD+\r\nvF8snz+UthpZ1AQlIsTe4R4AIBh2BThyhkWnUBRCL9WWZx9obQ07zfiCbeI3\r\nbT6fIVF6ysTFW8l+gewpbqyCkt6bFXUaLVzEGLbMQdcmOIjXYTJFA6ht1Fl8\r\nejC8DZwxbhTFtD73XL6FxkYV1Lka2vRCtB1XYZJBU/HZ/J47G6UUPDjsVTRK\r\nda+j9m7oP99PVy16VynmUD3jCKXTYJbq6GekiBrdON1v9hk8rXmAcv8q+niS\r\nKSs+uTwGeLR9yaM6Tr/qmsE1umGALPjj4n4vrJ5/ErieiY22Z4qI4oYoHP4F\r\ntohl10cisIAHgZBa41a6XS0dSRZNzhYeJo57PBrijv7ir8+KBdI+BOqMuIuj\r\n5qVpgmh93C9QnOEFhoJrsC0pb3ygAmbWGkXkdFMvP8W8BLxk/t/f3PfYZ0FM\r\nt/SfU5tw76h7gA8ktgP1IyWa2udhjBc4g7hmCYRUz2y4TPPaUCKrjVr1NJQr\r\nuj8i9gaXw969EShmWoq8I1L9oqLD6GEKaA1wZoUtQqFJeHR4DSCeyClL0+ne\r\njPD8d+JRImz2P1wFnXdLIVYi4LcpDKdCqas=\r\n=1SGt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2da210c2a2b152300a3f6eb3026e04fab350ee91", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-98KJW9CxM9fr2x4Qc8xnYwfMqtWL2hivYSJZ8tp8dvxlDaBkA+oT7Fnv/yoa3cBGLQqulAPILXUSsEYDj9ak4g==", "signatures": [{"sig": "MEQCIDy3jSZecvF5qC29QnztVQX5BSACu39sPC1nxDVyusxtAiAo0w68ONl+5iLWy+SWc5QE3RqKSgBg3yO2Ld0Tsd6kmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+g+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPURAAlE8vW9VRG5/VuqJdzMIE+TIjpjvgbQYoFvPOJTDBkri3f+Pm\r\nb7Xlz+J7Im6T3FNULF7TCuhKfi947Q/OwchhWVVF4ffx0HlCnvTOyV0gOafh\r\nKWQHcW96VggsZw4K4EUN+iGJYrTXKNo4oh6BEKYeelIhNtRtn0HB7YkdKW3D\r\n+pCCP60OBaobpcR+BoH8GuhKr7GZWMjFt5jaLB30tW1os5iJMNx6BzIiDL2a\r\nd8g/8ppAaGCsukqQAFQND2BhPK0YJ8wS2gpcFiz+WRQZlVhtfAtWDTO8CTfk\r\n6uPz+ZppIOincEWRrv9GQKPUzyGcdmSm7OYy+rRYtO3n6ilgn0xaDkKxeHNR\r\nyECMkr5UZMQBZ0h7kJCWWMSvrRnmtjt12eV17I8b9vo4BLctQdwQ7qBxZM9k\r\np+b830jSzxlR2nFpyzL7TdvKwWXAWClHbAwfd+NWjUlX9l1U/0YoBciLFzyQ\r\n+OlW9sX0DpW9Tb0lXWxORCSQRNWUuTYbxvRLLt4B8NSghVSOYBoOBhyl+AAq\r\nejGu/HMNnsRbghoE8zLJT8wHf6dUcaCxJEcq3O+KfrObX0UpW0oGw8qis1KL\r\noD2LTDgL7YV0X5y/lNSpJ9eHxxIfSoZhoMgXdth+1lyMsclj0P8qASXZXJml\r\nvWs1FPzGUyeVBv5l+7ZNXxiPE6BPLLyOGoc=\r\n=LHB6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22678fe3d978824cc60d0060add7a7ba36c18e00", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-FIcFTj/89zAFCMqdpufQYs5opKhEcAxLVWVf3gusXFLRCJkQyACWO4PeTg8FeLeQTGSCrdADzvdg+JEI8NObPQ==", "signatures": [{"sig": "MEUCIC7CLdp7kAAVjDZ+a0+FZqFVe34lueZUHdJlvxQ38WtbAiEA3G4WScHTstm5/suz5aAdIbwmbXsdelTOW+8LXCYBzyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/boACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3NBAAk73hts9uOtlN2D6uefjq82SEIvPQLrVSeTEOnKeJgIPxwvZr\r\naFXDLmOpDB8wN0mLsyrVhNqS5opyWgWF1IUuZvY0a5GaiTyfCmZpS7m9jyfv\r\nYYEFszXkeUrW48nMQCn6YRDZCNT4ZNGgsczQJdAu9tstgbVz315eVBQeeZs4\r\n+dJIlrHtdF7zGO9zh3bAa2ht5vv1wx8qw+pgcHPsg7srqouYwkg5QvVABcUe\r\nByDM9xoagEIysvpXVBmimGqLmlCmUmI1ZEsr2qCYLKC6QaSvQGEFcer4bVf1\r\n8WZ4Ng3vxaUB9uGkI6292qTtRe8FHzQiIi2CbZDJxZckVL1Xn3XQtSrT2cjz\r\nqfmnmlSNTWlMHpkX+OEswcQDUr+SK996FjeiZ+YxUpv9ZTGh1PV2Cv9br3XG\r\n1m5Hd141zvYs1NZKe+ePMRK2lYrI52jNaRCfBeWib9T6rjMKNSpQpnALchQ7\r\nQbbWVXiTfsBZIagzhQnwnQZYu9Tz9QjTMkpAfdMnZ2qsMRcpUtv4CzgIUiU3\r\nBlCND7z8x4aV4s1Cfm5gXApueAa3tE6SbcWqf0xxctivC1RmMwCrqWjsel58\r\nmdJrRbzWTORz7pdCce8seL9KBK/YaPtlxy666Y4tGZdo14WysCqTrtE4zVJ7\r\n9suP0Uedus4FzNQtUUziV6xU3+b88mIYy9U=\r\n=rZV9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6d5694adce36260ab404b19d33332ed186b65946", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-dUumw7r/PtDymbBXF8woDH6NJTfa4kglDCLGlzUDIHISU2VAJosr6MfnJ59ddw33+7apdMUh5bgWYYoqzenKfw==", "signatures": [{"sig": "MEUCIQDrkUqIelyu/UGPASSZHt8/j41tQ505YDzbhCJW68fPugIgCjCzp0NlHv+JZiXhnk/AH9+9rO5mHWLHFitJstA3Als=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxig//RKUcXud2ZuFjXSO18QBSHRD1uuVpBchzHOaBvhgYooDY8lhL\r\nd8oXVL8d1qNAhtiaWZ4DWfPkJ84FcRnktw4MifBWMZcKW0R0ZYG6YnimR8Mc\r\nJi7hiMF+UaKJupBcMVWlmVMTdxIh6dqb6J+6xUugONbRZ4963/bdklMswcnW\r\n4zc0UbabwqmkHy3d8yDsH1RGRUQ12Y6pLqTxO/nJPlhlrQLXIfruzxuJIB8C\r\ngk20cw3o1SL6F0RACi40AiJNWzHuFTYW6UQXSXIqgbUWy5sxofDritQa60mX\r\nnfKxf1JecIRU4H3aphp/3ei6nVihR8zpb4mRR8xuX+8jSAIYniCUWKJnKYP2\r\noci19kuzBD8Fj+QQCdgmKX90TFsF7F0HaGJlryoMCwOAOUnvvYtZmroAKXHV\r\n3ZMLQDaqW7KXzn9L/y7Vy1IjMKL6RItchAnlSPSyp4t2ph96nHk9S0XiE7TM\r\nl74apdhMB+6SBUDurim6BRXqR2Gmvt+7tiRVf7pHVn+oN+c+REvaFDYPTqgE\r\nA8Ii/06/o5HRg4miuSEclBhKEsGdnp8jvJOUYQ/WZIAEau+zs8uYAJO7niYj\r\nKcI7PooQFj2/53Zj2Hb78vb358DLPiTsG/YT+FHCyT95hgTpshZ43+DoHgOc\r\nL1M9pSncZPN6ru9QJ3f7zLNaIYXR89eGTzA=\r\n=WOdO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "388074c6e59200f1c23280908ad088c92637d034", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-leG6PDefRCaS4r2sw7BSAUz83t9HCOBWusy0osDrRBx3ifbUTzEFID4xluTd2BwUrDT221amVupaKYOS8XM7VQ==", "signatures": [{"sig": "MEQCIHPwpnma/HxjIFtAC18Fuen6xiFCRjOavO/KyJZzd+zsAiBTo6IIdXthocGrtlozRHDrajz5BjvyQii3wnlWtAwDow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRx7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrn9g//dETxG77b/Z8EMwO3kFmeBwGxVZ2q23ORHKTY1AvXLM9pJF/4\r\nK2U41YHz5bdrFK9VSf/al96QmBLbJpBIBRtjXtrrgfL/qgA/M9EHaU/JNT9s\r\n4u0g+fsqsMdg6V4FkerzLfuMYnhPPQkzfdMoFNJ7c/hJoJMuSYbVXtvg8xAs\r\nN+tBRpqPKcI7nuInf5WjROsUTJue4BEk5x1urHHCE2hQRLpcnPnzTTwjBUnu\r\nRk9NTcJluvGAaEdjgJgCQURVTclATppawnZ92a/nCGPIrKiHr97GXauToD5/\r\nrDUiS9VPcMZA3g1zaBveX+l4s8Tebyxua2a6bjtWlV2Qx7JZ5xCvC/zDgGfM\r\nyopOVC4hbQ0ojjnmm0mJYsujA84ZMl8/Vx85IHIF2Is9uA5ybuUEUIvaNvFg\r\n+R0HnPXsg7mqAzUEPFsjpalDEmjNylisyQQQSh1BfozLjus1ZZSA7cTcdOxE\r\nH9MRJsGAzxMx+Q78WZCuygxbLJo2XNhLUEHhHRgSfbo8aMWcczCDlK0zOdrJ\r\nBdvSCdgL8Eh1owyOXKfcnfUgbtjTPdMsR8S3wmZsPvsc9f8NemsPxe7XDUbh\r\nZov9SvRt3+8nvYPGu9nRbGR3A+DlOL4lUw3gUTvXmYseXwdhw/pEpJRs9ttq\r\nvD8xTi1vxWdBWc8kI0tZFTQuEnrIQZJ9Tpk=\r\n=QDkF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "99c62f369d18f2f88746ab21ad3f6fde9dbfe47b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-ENWjGubuDRsBEBCMLuI+pE3qE2ggWmfSA7FMS8u7SQBCJu1pqB1O8UMPUb+V8aEdjgoo0wg3yFkRAhfCmE9Yzw==", "signatures": [{"sig": "MEUCIFxDnBmgqnjfFCLozYg8+SLluLPcHxR2QpUTccDGHr6oAiEA9OLUV+QkDUTW/P/Fl0J5IviNNAniDhbZuZj1iuVO+7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVyQ/9EWywa9FB/NcAPA9V+tvmsSwaEaF7RlI5vTfbSpfphhPlRV9s\r\nFcRZrFXgb4dHydWr0RXxK+9ATNHJXaR+yXkbkTqMQjv9MSg/VctsWh/rhyKs\r\nLL0AwswlcX8itYkbwXKMOLFI3wVtnb9IsAmXX7hn91S95Y2byJGEa8CIo/uz\r\nJs9Pgly2BKw4U64juJI58XZvX0xKcsn2jcXb+9x3Md9hYq+eEMXK+LA4mhQO\r\nk04cpf7WHD2hHhYtuFpW/3oLsfHrshJxcEv3i8jpxTT2o2Ps0Wr09Qxt9WTf\r\n3bejvwOUxbA+TD8YoRL57EBFjfnn0F0j8+aNDhLcKG4ucBTX4+/Q5ee7XWOg\r\nAHOXj2Nsp6q+U0O9Kq8ZwYLgGN6KNzV1m/aJARLSvFgmomQekrMIoahUF2+a\r\nfU3Pz2IHfzSwZK6MNGuXvRS6L4xoRr9grwMNDGS0aaLO3ctdc+SeV7kPzz9i\r\n81F2Nosno7bp297ppMQhtocKNHE2oX75SiD4vmoNdTULTdqLizOzA4vWBZvA\r\n1+oSfLAHG3DHiuabxedThbvsHDdHlW+krhqWNTnPUwA0yFV2JeC69TX6o4tQ\r\n54WJwjRvIaKdOrZwV+hr04U3a0DgHnulFDopXAVctK0z3362IL1a0Mc+suNG\r\np7MQ8kreTQBuZRsFk/vqFWAMfix0OIjAP9c=\r\n=9pXa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d2834f3ab48779af56b01dfaf62e36f2e9b68605", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-dzOAp2CFtRgxq6kW8CU16jwY6ebiQ4BDZWxcqIg4p7XfBslOnbor7V8ZG9/DMhlg4WocfumpV62vCYzwqjeCvA==", "signatures": [{"sig": "MEUCIDzvtLAkYBBsIX/7WFzYeJufSBDg5GFyDue8iw3B/9IzAiEA/SYmpNa8Jpz4GH/txe8vb2Yu1/ORr9W41RgD6/zkthE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnLEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAsQ//Vzs/Acp5eIu9k9FQQg2dNBs1Nf6zKaHuLZXZRttoKLyCDPYe\r\nXe0sEnXbmISeqi+hUA+yqNe6z89iG2JjtMqgS9MK1xT6g7+iA3+vYHx2LV4X\r\n3nsEyAOPTQhgy5F7UqsrNu5bK+trAp86unT56S4gcA4Z1OaD+eaFUTRWPkjD\r\nbNlq6ik5DO9bnhQ7TDgbdlmZWJogXZmFdNslZZ4dcA9LtVYNCHiaVXgudCWh\r\nIY34KJYqaLVquEW7OIv9ixMf6xAjzZsml9ZsFmTfo5z9a2jXt/TeGDQ4b/n7\r\nk1q/DZHxl1H93dLw5YHkVBLkEzcM5V3sQ73fr3Tug8mg7wsXscUCbceKv5gF\r\n1RWn+NCyITN6LZu2BisKrrXqGjeKO3ourselGcJN6d08i/ZML4zwPkuiOLIp\r\nYMUei1LSzTBHac6tGkJyFMKKdg8r4PHZDKk/sZ6MoO9P0mUAS5JAMNLdzC5F\r\nKDm/Z1HC4M5WjTEjtFtLJidXo+sp2jKJ40f8m0Mh1HaRAYm5M8qWCYRURM3G\r\nQIpymVDoedJVCur8sVIzK0HF62WXuECEg76P70IjJvvfOn1Ykt990ChAO02l\r\nOnEMlcutLMAF6yvh6wDF023nUFTZo0sAjf6IQ9XAuSaM4sEWVX+r5SAOBSzD\r\ncEA+AXSWtJy+wcGEjLPJU/SO4ucVPxqt+zc=\r\n=EVD+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "94140d94c9145cd75aca3f44cce217f773727ab4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-cFaj8ZNaHJoa5BWy60U6p/QxUgZGW1SSfw7sQOej0mZF5X1R9uhm/jM5uoQY1KdlTCrY6yru3TX+xGG3j6mIvA==", "signatures": [{"sig": "MEQCIGL4+ScxHajVmr/YfNQnxBTY3262pu03cOAJaD8539ekAiBfvd2mQrU63AUnE/tmsSFGGs1xyYY8j3tNV9n3odKEgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+DQ/+KkABUJWXVzpCbqxkMJ/4NI84QKe4fuOjy5JcDEFwku0BPWNi\r\nhCwJjkwvbe8WLIWYpzwNagVz0mrzRCbYoSownwg28NP8OQYgsY98iIY6S+i4\r\nc73a+SSDAhn5iTxUnjm6jzIPIIx0P16nJNan59yVi64GZ/6FrlI+lvHTbZMY\r\nSwa0pRs20Sa9vsKUTNlA7NohQHiyEe2Bg9dbcdDw6la+sxCNOcLFvrDIBoje\r\n/H0mtpLz78KQA8/BzrQ/0+iWbXHs/d7KrBizunZ7M25x59NNIbFmSpqXQC58\r\nZaUXfVZ+Lbfw7maOyLI3oRHIdgH/vV/NHYgzW+uTVOo2pwhZgbT/5LMxoXVJ\r\n6xYTmbEkpeWSuDxYOeu45nqyHNRpRNrghDWV7iSy1n9RpKMM2BysUPeY650i\r\nwIVwIvztDr/rgVkUgdAlD5zWxVgtr2oG25xMBMDLIC3K6iuPwOCb2TVPtfPC\r\nvJwHITog1S+YsKTjCV1C1mQsAlqq3ubkxdXn3hRJpfY/R7tjOjmsTMZ2lMa2\r\nDncDlYM+KolBfBRULulrI1ljVXKmUBbXsVUq/aEb7jcQXCCuQylnD866ojGB\r\nChcPZfmbeKP1E/xh/v7VPMBnsNYnENg96F8lHkti0OspgrenkEJ5fGS2qxCE\r\nM0lZjs9GJnUZFGzVX/QCOPRMkCnMRvStXc4=\r\n=9fTf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "24c34f34492881cf3e2753d14800423dfb59dbf5", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-B25nnEPjWMN0o/O2sAwZMh3TNHgCGVKiyPcYoRF4t5RQdD6BA+OIOO51xNocvFjTMdCjaRo8XImnh4MIKHx/1g==", "signatures": [{"sig": "MEQCIHPD2/knTx/LPuN0jGf3ecIoVceQB5w+FSbqHXJDW2kgAiBSdZ2AaLC4C2eRPQJprFnF7AmhmRUKDCLwf/Dhc+ZwCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUK3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbEA/+KJVcJL+fntsnroBrC5DFKqnoSvX2MTXsf7gk7HCw83q1owuN\r\nRCVAu3O965Z7GoKc8y4E1YkYMuk28ayvA6P9spU4NAsWGzQ6GFGI9TukE8U+\r\n3wBcVwuLCmt2qeiOaQ3iLw8IKaqgBnePMLj6Sv8g6+ciQJr4b8R9VSsJrLSM\r\nZ8fR5Ndhv2dSQEBO/9GjbL+l907OGQ8dRg6rDFWA2tFybNDNG5AM9DnGfbdf\r\nPxorWGeuGDa4qNHYyCNaOseHIKE06mKNNE6RsHD3QZIw+dEbSpAvHzYBHgBc\r\nPTdPcpEDPTcQwrvq0ZMiiM3RRJGyKoilgQYKagQ3rFzlmWAQhMfedcHK0WI7\r\nQfwi6i8ksu6Z97gqSXk0tElE5Md9lyOVVPpSY9i8KFEAv7/yQlruD3XaERkT\r\n82Hg/QCIYQh95M8LZxQu8cC2GmSq2MTDo9ZLtphhi1fDe8wiJfzIOcRnthVf\r\nDppe+t1pG/HgLQNz1DPyHVBddYyEZju+xPESvdmMS+vbRR5S8tzroyo47Sfe\r\nsaOywtIrYmaYR4poNorWYMEcFgxFSqfiyGJwVGHrYlvljzp6T3Vzmz15/420\r\nOgkGSzzfFqJH8yVeDtBAka1Wrvlt88vWZ+AhR7zJp/peQYoGI2KxKr6sDeVv\r\nbAZkhzxD01onw6/h1QKnTd4vQaADMA8FwdU=\r\n=Y5Qn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-toggle", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c970d355581c0fec043f1e4e6e64c3d9f2faff39", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-4Z4G609prZctlZC/ANfJd9IJS7vwkdhEigrBvbAILbNDyrVZnb1NO+OEckMniwrhqc/RCVggVQA014Zxh/3nhg==", "signatures": [{"sig": "MEQCIHXgf0H0URYGJLllhYGC87S7C0LDyKjnDBLDMbOHW87PAiAac8fA1c4vVrOzbC6VNczQuWunfgdX1g8DFHtnKxCYoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKJxAAkOp96VniLS4FTDjx3tELdaK4PGX8GIYv9qu7fZE/CnVF0FOu\r\neFvYc6Nni18Zgq+SYe/zLqMG93hgijM/cNr5JXF37SzvnsJ1yHDR7/Kp4Myw\r\nESnvMm1QXsovwJpOlNrYArcSRdzf2qE1Ui8i+pyw7tfFzJ9i5WO4a9FXJQos\r\nQEtibxJkPEBTNgAOQAEhusa0zV+hhr2C2oMqH1WwpoSgo/cx4YEHgQzeH9Zi\r\nYRmCQE/CvlR3L2pZH0htN9gG8Ucg5yeBhzL0f0/iBp5wnrw7Cax6Bu8DnjU3\r\nWnzcVEQEwtnP2gRz1ti5NYjcmLTcduFAupE9msKV88nP1ygXx9CsSSS6iRqC\r\ngq1Zlx+1viHVUPs5lcl2kF4wvZR/XTVRlrQ3FohhturvxME1a5E6p3kQmBm9\r\nkBTZt6NZKoVPtsnJym+fF7k/ckwRcPh9cCYly0Xixh/pki64P/fEf5X1jPae\r\ndugqA6ztbNXI59+gY7OK4tt2zDoppX9IXJKLsxKCdZ/3EHGMio4Gv8g3RsvL\r\nBKcLZcOdyYb9o4IpJd8JJ3lPWHPtfgxTJQ6fykKBbV07OJN40sXtEPES2xkv\r\nPWj5mnxUxZdCkKtZqLO8Wz7nSHRrhIoPq0+36EvV1xZ8CWnFudMk/NbnZWTq\r\nOJv3WW4tYxkzg1paklDjyiqSPG/GfKE+Fig=\r\n=O3o0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-toggle", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "20120c060cd3cd6267463a6a9bcb106134b5af57", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-hZIp9ZKnw4NwVqeB4evWBLa91ryaSJhAO0Ed82wkzRPgg/I29ypcY6SuBb3AMZW+GsuBZpIVujpCq+33TdEcyg==", "signatures": [{"sig": "MEQCIAvp/0ZA+U6QGRC2TnW33Ly3u67YVhJeJbyUaKwLNuuvAiBI4eZ8C+ZycfZZ3J6sHVD61xj9Fv0wnlxJoWEWzX4ccg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDNg//dNuzP2cqndujUIQbdtnHPHAznWT4ejYtS2e21V/suMcdMmN4\r\nyRfg+4lAclz9UfQHWrnjF3zfAyANyARUryfcnvO7KkWwn82G7kqeJIM0+s5p\r\nAFSvpWSxGya2Mf0wQ39hKlGfnX4E2Lk1scT5aAYwlafxL0e1YBthYAbOeyM8\r\notbS2KhFGy+q8Rht49nciU7vEF7F4xuDxYXT9m1ILP1tnMtY+8otBJCDymU9\r\nRKIFHu58NSPx1rXSUvX5RViCa3wUJagvxdfuukLXgnvy+mUwF1H3EytrWu1y\r\nOUgIcu91/UCeGnTrIv1Qv/gtwnedXNdE+c2oS50BYlNCS3Xi+iIiCI4IIaJV\r\nCozazSDh2/eUQU1O/k032xG04l7puWOebCInlJLZdb2qsIzpFZexUJmFMUXm\r\nl3OJxor+B7nwwPg0HbxUBUXowXSstXSgo5z482CMDwLlkiApmxHDMQGlz8O9\r\nBJsiEgPPTgDFwgvAPT/qgfFQzYzAgFG6DpKEPf1GIAItIf6hUVfb04uICkGR\r\nSZ3v2qZOScy4bUOVqCl9YlbqJ6jn9CO72OJRMV86rA/RQO8Z6VlGgBr2A/Br\r\n85s0LA5E7PCWYARXpiMPc6HJvCzCWMLYH8ecdshWqwCSEcfLhPHJXy1TX2em\r\nHt5r403O4Rhnald/e7fHF07AE52aAJzdUdk=\r\n=nbaP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1450d6607e1c70364a677cffbc2daf70d2f732fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-r4ytvvJhDVk+rgAOpX/BjUlncSy8B4/Q6nIu9Pa1nLUHYAJQwFjnxaPVCiWl8a+7Kjkd+NMu3k6s3LsLBLTCrQ==", "signatures": [{"sig": "MEYCIQDRrv5snqyhVuXlTGWSPMsoZYTjFEfxgxty5N0YvNPL9wIhALIllv2BT3oR1fNxr8DcFTOyl6zzXMBRAt1O0O/adyZS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzgBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMBQ//V6p4maKbGM5+QPOMLezOWWxw2AGfgUKpQXOJ2lsUkJedhuwc\r\n9V4YSUEBX/Z1MZGy1XZc3a1LHXKNFV2+EgPtqe1dlCqs0TPZI36iyRhIIepU\r\nyE08LY8AOvJ4QkAhfZFOBt3e34y5MvZAA5Z/VSifJlz2C0YWWsCEaQzokt8J\r\njIMC75q1YicqslQVgfy0VL0i3zTAmMfUA13aJO/pKTueijdVBc6ecTpMp4Ds\r\nmOqTk0EXgKQ7Fxu8OsKujScE5JComW/OwY9sjP/4/XXS7TTIyi182ayELY/+\r\nxqX7RE93Luw5lOSzeIVIbG2FtQyG2nQaUN4CkuxjT+e5lK8lKkOaM16Gj5Kt\r\nE7KNJ8L+K5HwDSc9sYhmAA+s6ltlAafYjpMTyctLvyM6yNnoe99eGwbcLljk\r\nv+6pgi6vB9fKfEa3zDeXvIhZL9WljM+d+Eesd/nYM4HIC4Hv4EjiBreHT5+v\r\nmfVh/h+zXnPiDVrtWMMgvAjrHHRkZ2bMAhAbk0BId8pFyka3JoSkvZxeltvS\r\ncaeLiosYJejzmb7IEQoHlf+j8bbpU32yB52s2pM/0ObwQXn3ejdgOC61zTYt\r\n+k4J/z7Crcm5sox/yxbzP07ne5oHUDoVMOmRPKnfAPXYPwIemsOLgWivk/9k\r\ndog5+zyRte5+1+3JYIj6dwmn46Nku+T/kw0=\r\n=iXP4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-toggle", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d27ba93b72ad7d06a0138237b25e2b367d8ac64a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-1MhVrHjgdmYDBgBpmOB0sjK096gFrVqUocsHNapkOTkZIxOwjpGxnW9e24CjQQX9D/c57dI6E8zAAdeAeIdY8g==", "signatures": [{"sig": "MEUCIQDEJl0LjKKgWNaRblTylfLAX55d8XSkUM/H2aStxoMe7gIgTSDN+gJ0Mo01brDQ2YPW0ZuZTNgDnchr80re1mCWvYg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSew/+OAMjNStz2Z6xn4VZA2XkmuzXYIK9yyenS6eJlpd1BFFSYu6G\r\nY5eO26+vqtiSR8jLzzdDzdq9eeZJgBi/Ubg7cC0rlanqnmjvXamsHMahLze4\r\ny00Aw1YrQ+/N+TWOn6RMzFWtsnuRsXuJ6Te10LbRvMSr7lJFS3ffNX3Iu6OJ\r\nHXDsKJgv/SjOT5SGI3Ngr0i2SKhdWh6XzNl/nk7/wtXhbHuNjiRiHXOPoN8U\r\nuOCJNUkVSJnTbM9Ck0XezLfVU5R6+Qk+tKJtXzm58zCQwVm7sLFVb7/HekN3\r\nGZY2UUl9R6Q42r3SZ/+CDlvHB2FqLkOlQzAKpgXjN5IwAwjt6wQ8GQJgXjIb\r\nMg7Rtwx2e3s+S+UGJ8nVpoiLY9Wc6oewweIUXG19silwSLVGRaLZDnKoVNUF\r\nQZOnODRy72wFlea/N35oMJ5f9t1Hxjb2uckHB+yLAnWpirjufYsU//WDyjtc\r\nWxKrVEEH5ooJ38tQ2GZ4Qz+jrQfLxEC9z2xae85xHZifbTOTNmQIG82c02U+\r\nxIeeI1SQJQA1TkSwPs9ScT13wSJRAHB7J/Pl7iBU0wOleKZcuGsuHiHk8z71\r\nidJqXoKkMyz1fyPaFRpwnBsHNPt9TjLPsgs6GAbKM5Q9UO8XwtpSTqXjcRt4\r\nU4aXLA+XqjjRwQ4akdBg+5pC6k248G5oND0=\r\n=+/oc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "11b2386f0ce2f4215c8316a99d754fe785926138", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-DkZ3znNhdyxQD+gajCFBc2Dcm3+ZcIDg9f9W2yKdhJZm4v0Uj44gkKLb9MhccSdkH7KztXXfg2ZxzxQWNEfn1w==", "signatures": [{"sig": "MEUCIQD4RkuzlfbdQuRUPltiVrBpHoleTFOZZsZNHd7Pbcg0HQIgVYXhLuueLttz7QuSfjB3+6K/gBIWkiWIWi9vZZC4eaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqpyw//aqXGjHRdCBwc0GopFZ0bjikU7CoU7x4Eu3Aunk1ls/JlrWrh\r\nRX3P4K/VPK5VHHbyA81A6/yVHf1GNMuDwGAnljqrBE7QORyiS6mQADrTII0K\r\nU29Is0stBPODh78B8n1LC5+JIXPfFFtKbU31dghonQmaTJYV4jlyQuRbIjLJ\r\nscV492GOBALM9aKrh6b4BKfcq/SIXDqbWctm5YF5GPNNa2RM/fWhxksy9WLl\r\nT/ubTnLJ4SMjFyJ5qpdB+68of43C++fuzXyiGiMEndnCUw1CZKmM97TOb3WI\r\nHHCJXMK1p5sehQDXzs3Icgwh47c/HOxIBJTTF0R4LECZ71RXSgZv9/v0Ahye\r\nf93MpEq13KxRIu5POv9Cdt+cEU7tP+2DxPRAHXf+nTC5yfdudrYOGHU+aSzt\r\nCu3z2UQrVg4pnE/nTWmZFxp+U9R/UfHPCXsjZDvwWpsj/2Qp5nHL/2WeWbIY\r\n7NJpBW87Qx2ZfBjq2Fo3E4dFW591M6VRokVDT4cSGfBDZUlJEt1fT1WVMXHp\r\nyr8DnF9eYpzxcp5DA/54YpVCliqj1X2zG19DJnqpWG1W63Q+8HxAIwARrnE6\r\nkfvvoug+K3kq+p++D5Xhts5U1nJlnGpDFn8rMnOlXfIGlls0SYXWkb1VymWZ\r\nbnpt3AYZrstLm32a1qAD3J6EzPAUZ3uWE+s=\r\n=oT4f\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "405d1f3e48549e8179517a753bd99f3569a513c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-EMZkS7HOYBw875E63qkW8aleuxKBNC4HWYeVxmJIqqMnq60uRc2gPv1HfLusfHdDynUAsMC86/l9kzxhDW2Lhw==", "signatures": [{"sig": "MEUCIGTg1ktEf4pLEfUCXrS9yJ8cHYniBiV4xbkTAPyMC/tSAiEA0rWPLBv5YcZD92NKNo1ww1SgQbKJh7lXwqJtN1KpnRk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505}}, "1.0.3-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6e41ab3b6a6ee48b8af55c7bca7c35e61846fd2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-rvSk6FFQWpMMhBltJns5MvOkqOl7hunRH/0Z+UnGzODNC10L3LtE6mQnNznAUPOEAHQX37uo88QQPSv8YwkS0Q==", "signatures": [{"sig": "MEQCIA7tuYo89fyAVex3huKnSuSbi3MVXV7/ltOkgaHd2/TyAiBM1XxKaK/0JKQ2dqvlW95d/gBp/IAKQWv9v3G6+6MH8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505}}, "1.0.3-rc.4": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "825f8637112cccdfcb1e2c159778f50ec14ed4f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Hf+rc0NqY7d7Df1GPfxDyNk6rcQ3PeHGWvLbk5CMbjq+NgZfFPzMRedq7RehNgv6yHvUHHGS68ljAmnbJ6xLnQ==", "signatures": [{"sig": "MEUCIDJK+iKdIzwy2oNfTCIHdnuRgG9Xpg4OUMSZty8aifj1AiEAzrb7T+Vc81+xtVUn2+caRr2slgAAfU2Q5pYh3WdbwRM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505}}, "1.0.3-rc.5": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0996121a922ae79acc09f36a4b74abf23381ffbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-U7TJZyUV7cLB9ZQ3iFHS/hkpuTvAVDSnPie7Ie2S+iJgr26mboXn/wwMdFmDcuifLnhBzbmNgWHRmcZh8of/WA==", "signatures": [{"sig": "MEYCIQColsV4D0WRI7FveRwIrB7CzWa/Z18hohD5Im2s16SUnwIhALa06CKF87Tw8Dgcw03Hfyx/vnqwRjv8647sAvXJkofB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14505}}, "1.0.3-rc.6": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e1803daa6361ae600dc73bb222f2a7d9cd048388", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-pfsoDYhevNJy9i3RCuCcLHaibA80c6CxVdgDVHdbV7xKQJi+INLMnxb67Ebj8E5M7AY0mmLeg1yW+ZiqDG2fCw==", "signatures": [{"sig": "MEQCIBqXD6PQO9elXHtviexKUF/ycYyT0jA46R6ABoS1399GAiBf/7BwYo/fZRdUSQmMFwsPpw9vQyumo9syc2vkOQ8Q9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15710}}, "1.0.3-rc.7": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80671af0914fdfd5ba5f820d05c73f3551c9ec5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-Hz9clngFr+uOJMXVOypcdTegD3UwloIMP1mEHJsdyaJijpxaUXcEFZ9eCAqCDmNP9ZjyvjYtK+oFb3v0eKYVyg==", "signatures": [{"sig": "MEUCIQCqCAO9pWfU5BUJxqhDBrfdWSFLJEEgjvxOQJUuFfF+EwIgasQCzD7zOYTGSubLs+BVgQWk81yjyfymC7VTcI2kfXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15710}}, "1.0.3-rc.8": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ed38bc5b71cd33c7bfd70ef65bd7277374d42ccf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-THEoWcSQSIMsZLCrtPhcilTHmw1GKdOZv/paLyynTHSZdbvKi/7YwcfckBN+/fz39cYROIK1RcsK3vWcmymmkA==", "signatures": [{"sig": "MEUCIQD/x5enhGpBdajlwgDtQI5QF4SfgX39BOtjaCsosOErgwIgDPwhYSao7XXVkaQwSm2AZSISBmMh+p3iTariK0vvg9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7525857043252e199300b6b826002405f97fafa1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-8rZWzqiUgvzYEXlDE+D4jptAoqfRAe0Rjjb6NL8vGdxTVvibYS9+ybvYYUUirBtQy/Dd9P0olyBZL0cxQIf5ww==", "signatures": [{"sig": "MEYCIQCr/KFXzhFfzGt13Lsqd3tXz7kBvyVkq206lNR3bywh5QIhAIohLV16dsl/rHgtQ/xrUOm5xRVFsF4tHz5PzuecOnhC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15904}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bfeb6035075ad55c1b41515bef6824cb37c9c0b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-tHSQIzmMRpDndk9VwpEkC9gAhw2ViMNI9rN7zHgXa7S0aWrAWXFMXgH5aqfYHX3kELRor3IhblDsCaJWyrCm1A==", "signatures": [{"sig": "MEQCIBgLOUgcoCzxKmxYlMlxbvq5rouYPUoJFI+Haf62AtpeAiBmdNXVmO4yVHitr834xeL/TxlqdTSoQMwBNF1yrEoHzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15906}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-toggle", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "078f056923486c34200f8f5f9da6ebc2f1a45c07", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-9MXOLDAgEgmTRsCxRY93//ZsVU+My7mP/fcOpJNsmn0oC3Zrp1JkwhZlUYNMBDzLJfmN5uhYmFNRQdPT0q2R/A==", "signatures": [{"sig": "MEUCIQDxwlEHJdWCr9WYN+sWA/kCIbJ3i1E1BIKfhDjP7SAH1AIgBLxC3fgibE9TDOplxr1tJlN1g7cnhxsgSHD80SddF9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15906}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-toggle", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aecb2945630d1dc5c512997556c57aba894e539e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-Pkqg3+Bc98ftZGsl60CLANXQBBQ4W3mTFS9EJvNxKMZ7magklKV69/id1mlAlOFDDfHvlCms0fx8fA4CMKDJHg==", "signatures": [{"sig": "MEUCIQCeYFFB0iqYeInkhhYPD7jl+1ATViMevV5b6orgUR0UkQIgIMMFVve4rv4tX7a8Jm5tMN4Y6BJSUfKV8lRBOWZtkCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15856}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a22fca89a275ce5d9b9ab766a13236cef328705", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WUfDv/Q4+CyGJTTt2474SLlMh6FuvOyFeDZFx5v7BZSaMl+eJ64mQrUGVCq97hwRPZhtapjTMvcxHvSz+42+WQ==", "signatures": [{"sig": "MEYCIQDp8OYcaPuE9aNfOugvDJQZ4/zZ2M9wtSjpDbS+ULVqCAIhAIhnUl5UjJT7VRCxvqFsD9VpxzlDt7oz7YpsaC7ykHWf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13379}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a29e74bbd0b43a660450f3432e85ec19858f280f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-MI1egIRfyBk8LIkmqqR5ByZC1J5gMi5Px12d9I1VK+lk2Y3gSeDAyFPNwzBD9oYDB6CVWWdQQEXMw9vO4v7neg==", "signatures": [{"sig": "MEQCIERFvKAHwwOpTMLH+Lslcfn4TrmRr7a4z75JSVC6oQNoAiA4MQAPVOaKgL1+auy4E3XCxURg1zBofUj8dx65NpAK7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13411}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9a2a670161157a903a867c0be7d6cc79358ce904", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-r3VkM/ury7NoeKo/B8e9iUrn5oX8EAE9PzsSQN5hPXSZZfGF62AnjEYIiCOHjA3IbM+Hregd6566y4mhVCuO9w==", "signatures": [{"sig": "MEYCIQClE9g5B2tF8OIzKD1iRfgaOf8HL3UYyJSo4b3YTZXEKwIhALdAQGU+NBJAYueAJv0XUMKKxvtjosh+cDV7QQrzcmI9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13460}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "44ea5a650dec82ef483fdea5dd43595dcb412b51", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-U0/JapqKiYXrQjDm56hWo2D34ZhHQDbxLpsgDC74ZaBX9LaBTplAyU/tANk7U89vg1zAkYrN/PMhG0s41K4wOQ==", "signatures": [{"sig": "MEUCICmJhFHWpxVgP3LudW0pVsyHG7aVpEdrp78tOo3ZlxhaAiEA3fRT2exJ1r/QfDg+yu9nDo6wh7HcA7tB0ZGz129XdXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "580d33935d7b1d2dac2a943169d7b64c1632ecc2", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-/mxPJaFu36OMYPRZXmR1msDgaqG+ZoJizPD2tnUVCM9mkGg1/E7opptp8fqGh1c+Enoucaox/OzCIl89wlXgEw==", "signatures": [{"sig": "MEYCIQDXNqnktNEWjaQ+tlFR6JByJ9SGjpFK2h3yAi3fmkeZEgIhAPtXZIPWbEYYCZossmqnIZyW5nq5RhXvEUwieL24xKNz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "440be8da2b80e686991461dcae1e23f15611499e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-T6+w3dlg3qJJcF3TdYsGdS0W8C/j9CU+uzdo8lO7oEZJz4wuQejSvAjh7NmvjK9juPl64FCOPh6oZT/mw2G40A==", "signatures": [{"sig": "MEUCIQCY8kwWdHtoedqK2iBh9fwLy6J2AqLHP7J67IdEtpw6fgIgK8AeOdRB5/XroG3mLNDZjQU/7zWLE6KEyuyGIAxtPZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13238}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-toggle", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28b454b3614fe3cb2a78c46bed56812a085b699e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-MGkfpuJzqKEH/T28Z1NfmIVs36IUi/JpxgDJfsnqh/j34Wr/yqiUl9c8RYxUqmEyBbGWUHw4IjTBlVgH89LhAA==", "signatures": [{"sig": "MEYCIQDH0VrBBhjTfxnq3fuQihIBV444h/xUIdfOTVpN59x6awIhAOi3UXylW+vQ2Y8eTXUt1JqG2j5KOhPdBzrVpCRZsYiW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13266}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-toggle", "version": "1.1.0", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f7697b82917019330a16c6f96f649f46b4606cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-gwoxaKZ0oJ4vIgzsfESBuSgJNdc0rv12VhHgcqN0TEJmmZixXG/2XpsLK8kzNWYcnaoRIEEQc0bEi3dIvdUpjw==", "signatures": [{"sig": "MEUCIGkzy9Z6yntB2meQFlo/EEQ9A01/99LQ9EjEFNZIcDOdAiEA3aNqx1Jm9irEC4m4B3HmGMWi7aB2Vupp8Qm57DAjFWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7cb4c4427e08fe6e22200105bdee67f09d01f314", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-q7gPec4P8nQHLbFQxQGxEXo+6OLbf9DNPY3ZVzsWnJnlqcgZqLnoC7a3tGIH2IqN/o6UC4f9D/qi3FBkT1ZT0w==", "signatures": [{"sig": "MEQCIHzSAVWFF1jh5Nr4cSK71m0No/SXDiQmjefe8u9Rt/BGAiAou2KoMaGCoq/MTFLESe1LZrpDlBG6StH3asWzhFvVXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13261}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b8cf113524f5052837c02b121465ab3f884c6c77", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-7qoDN/QGbc5aogk/TUI3rW2F6tfiT2xZBUavsvkLwCHpVFbVG+XuA9AttrzoeS8q3m++d8mjiyvwG98FRxwGCQ==", "signatures": [{"sig": "MEQCIB5AKh9GYlkcvbWVWqsy6wSl/njLSZ2FBk+UDtGWXwypAiAXQYmRN6ns48vYUcQ/K6TiXZmkwM88gFBNHO/JbQMyHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13261}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a0d8ff60a1b597ce4911bd6006a1f9d4eb56c8fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xEx67KC5bHzZ0oovmLib0GdEMmuW7vui9wgHbHzZ6xU1KopnHME+zmIaDJSCK7z9Orpsj/S4yIfkK7MJUVoerg==", "signatures": [{"sig": "MEQCICZ37ITjrJcstb7y2Zm7hZi/xGKQqzjAjhRbJk2TpKL8AiBGTXuBk80dPZJClERPt4qtzua/ZbJ0WS+E3A6PYBI+8w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13261}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-toggle", "version": "1.1.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "939162f87d2c6cfba912a9908ed5ee651bd1ce8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-i77tcgObYr743IonC1hrsnnPmszDRn8p+EGUsUt+5a/JFn28fxaM88Py6V2mc8J5kELMWishI0rLnuGLFD/nnQ==", "signatures": [{"sig": "MEYCIQCT04kANBZlrVBk5Tc/ony7uryh3IF4m9CMDgMq4GGYdgIhAJFpYkClJk3JgaZrIb81sUpCBKSUSVivFICcD8mxbD2M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-toggle", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c26ac48a7807c28860896729fed9ec1d85261338", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-5nSql4/IcbdAYgWgf0qVIvnuIDRClr6iSMo4Zx0jpA+lHKDztYgt2qasqKssBzGroYHmf8xF9Q8XIdPmosmMjg==", "signatures": [{"sig": "MEUCIESvETbgvVRvs165PJeq5dprIvjdeIvI++WTD1quTraXAiEAkYpeg7I/7oo0uxtOVjmcWbw65CME21oqgZbwQV+YeMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-toggle", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "19cd1cbdf3dc5b1adac5a2e5fd9980a742fc84a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-htz3z9wOhjr4H7DUvn9L9dvKgOUIQDlXo4bMIKbhgdGf/P7FggG+6kgoQIIvFwhZwGwGz9XagjwYrxKXGq2Xxw==", "signatures": [{"sig": "MEUCIQDiWh+OYu1vXRuvHAvrLpABJ6kU39xJHHHU3014dDGZ3AIgOGQKQyEZvvbgtD2gEl0fhCAqopvGC4+vFkry7D33V5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13221}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-toggle", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "959c1231468ae4eb9b07573e4e1eae5497fb098b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-Jo2g3D4GnGIATSTHNpyjL56uzSU4Ji93sy15idJHQ8b7/3YjL/f00OOGn3eN0O1jfXStOOI39MiZ5RW9uAVFNw==", "signatures": [{"sig": "MEQCIAquspRbqZ7Wb3iVnani2DLIpiDK0jiMafJ3vD8pj+qzAiA1j8JM76J17g4iO9qyAQe3FR093ADJ7kuSJzau7B65Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13365}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-toggle", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "896a8adf652d0c23f3cbea9961c7fb7730d25a06", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-R4i2HwBskfYcZQzGkhotDDC4PdS18vS9dGm6NheLxCbAGz5tUJOM7AUSY+BFNdOoD1nRK6hPabjs+gD0T5aZiQ==", "signatures": [{"sig": "MEUCIQCRMPChJj6yfG1TTW0ly3zj559aG/nlb3HtN+XhugM4VAIgfGRr2lhK2O6sPt00ATn585Z863NTkwmQ7bYEZUkLXF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13365}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9b275d325262b1f064b10a139fc7918fe4696afa", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-FKxk5Nvqdkt4GQN9ofs0eTyvKGf3t+I/7XgGxyh0+jdHjNz37WjF/ugsZA/81NDoYHPNJaFobBnpeOAzvpd3oA==", "signatures": [{"sig": "MEUCIQDGzlSVZg6UC5jC3fyoCE1+pUImLLx7sWAyZzl09U+1TgIgRxEpimmCz3T1Z3V0CundEqdyo9AnUlvCHV3N/mw07to=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2da9cd190d47412755f8a6aff4f11053db7cc039", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-BtzipAoqjJx0nXZDPogYeQc3U8qMY2DqiGCIKevogwGCa/cAuvNXGWZcNp6ZheEVfaPIoSiAaBa+kaDnNLv2ZQ==", "signatures": [{"sig": "MEUCIQDaN5NtqknjDuRBfDdAbsoYYcQBCyI8DIANIrYZ3xFrtgIgdfS39lm8648v6RrVqFccanBAthutQJQBxJcxj74i138=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "85b0ea8e55e67674a32afb7e8ff4bae0b2387cd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-+hjdZI5od/OBqOJTau1KPzlnmL8ed21t4NO9f3m5YlPQQQia6svyTk5iGMV6ZvW8DUl5xbx7ODAjJLmzub+EEQ==", "signatures": [{"sig": "MEUCIH1bfzKxt2CSNPeCm8czSRmfk24z99ITkG76fIrjb627AiEAzbEExOeohFBGkK6hKL6/b0lYS2ono5GNFYailjYYNH8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13573}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-toggle", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d951b22050e573f9cd9a594709e9c7b6abe7792f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-oXljqbQT3H44lCQf562lbBh5TTt7O4dZYJwvF+J6HmR4P3qlaODWi1YvbGeJbUUvsvSpEgzFzbgyjOZuytl5Tg==", "signatures": [{"sig": "MEUCIFLTiC2gINe2xpwgrQh7MRMyP7qbM3wOBHU8K9Mhnn8WAiEA7QFcwQQAeq3NfjLv/8u8EsSdjsKeYK4gp3a/X+dxQgI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13573}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-toggle", "version": "1.1.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c11cd1550046a2c78afbdffd35b541979df9ffc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-lntKchNWx3aCHuWKiDY+8WudiegQvBpDRAYL8dKLRvKEH8VOpl0XX6SSU/bUBqIRJbcTy4+MW06Wv8vgp10rzQ==", "signatures": [{"sig": "MEUCIQDl/g7na6pxjIXLNgBVV1JoHxK/hMxTBA+2ndSB7TjCrAIgN0cXHsXpnpHZsQwCPrsgEJW1yPgVDwBfD3eOJpfXwBc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13535}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f9c3f3e4cabb4e32209d2f3dc3f4b974e7485d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-aC3XdpWhHTjRisDThob/cvy1xiscs2711F4nfZMc1V3uE77XJaK6w9W8EG7OCvyLpXk4PZ7RFYsD4oKXORkdtw==", "signatures": [{"sig": "MEYCIQCeuLUITb94+EfbO6S2BDw1YvuMX0YvJVQIHZNOYurN7gIhANeQz3f0gnQbmV/YaBSdbD1Wz0KpN83nhWvnlHr1wIPP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "49f28499e221f4979f292e1866a62942b5656301", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/hEDDcCJ/rXS5bfPIVAbwzGnpeuEutlEdWcrvom9JPrlh2limz76c1Id3trHTB3KZZgO7D+u73Q4cc4Smy+eWg==", "signatures": [{"sig": "MEUCIQDob+FtA1jH7gSdaNnJcd5nhmKxHXp4ckUUgrUy+ARCsQIgQlVpJWJYCKmaFfo9AvVSm+bdplcFQZv91wVOSQyyDH0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0a40995d68cb3702fb754f4d7f133be6e7cea07", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-TVORVtVNbd15vKO+IhXQUZHcvokuWZrGH3DOlTDzSnQpw9rVF3czO+QC5+Rku8alVAX1ZlGwg7o0DXxLL+0R6g==", "signatures": [{"sig": "MEUCIDWmFZwjjnmMr9tt4fxu/HYmr/9WiQ6tFbPkcnzeTcVuAiEA46GOPhoFv42QCyD8TnpxOOXCS5CA58vSIf3pQ+ZI+ak=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bcbaa9642ed9e010c4ffe877865598f253b33842", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-4srvtadZgsAwTdAMHrU2FLc4rbBSWropMoBRRSTvzNvhPRX97Ux90QR862TsgP8HSCNAXcwokTs7rIxtNBpUJg==", "signatures": [{"sig": "MEQCIGE3Mj6ulU6/0JrsIPhChAeIf42HRNZyih0OwZTfCUgHAiALeh8M4o/+nEXo+SleKe3fTXS4NMBmBAg8YHwBksHRrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d233da4404173a706976f0a4d91cb6b1a06190e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-DHlgdJJ7pjuvVgqJmrJXJheNwVeG4cTpokCnsxcNQpqZzJEWmHaZ76R/ZpS+c5yAG2B3LC3MAHvEjl/Gm/wOgw==", "signatures": [{"sig": "MEUCIDgZ6ZE0S0wKM/6vSHCN3QaLVFpWGyW7VUT3p22xQw+/AiEA6yBIAHlC89CBkt+oywaii68C/QQYrqcQtmC1E/FwTmk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1f2a5e40fd096571497909ef636d560cd632a80", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-RFqbSei0io/U7LskzBLrv/4VeUFIwwiOplsuUnlBaXLZaacA23CzxAHJVemBXxrmQDQm6TIPGHzyZVDgLjDjAQ==", "signatures": [{"sig": "MEQCIFIGLm/aLfm22ZJNJ5IWVvhSGfm1HgNqrjbzDfwkQCZ0AiBHq+wrR5Q6D5aKKMcI6wpmZOomfT0UWCx46u10WzxVzA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.7": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7c38a44d96a4c3a396b89979836020b1c10a16b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-kAkdgAgWx1/vyzuLeoTGn+6dD9HClSTQVyh0XRt50mh0P5g87iU51dVtZ3SxPB2kfsSn5uYhGje9EC9R5aSYIQ==", "signatures": [{"sig": "MEUCIAEs4AwBt7dudSPM+yS9idyw1FQ4628tdQ2eKkHKjtLDAiEAmK/leuMQW1DHeCwuFK7v9O6VKvP2i4DsBr0Tbwz/QmU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13589}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.8": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7de79dc2d820320a4f54ed0ed13a8b3a429f1e02", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-BYnxQUC0NsGjr8pWhamjWlo5sDRQv7Uyd0K4r58Te/znp0P5JcPkqLbIjY8EsnS6/08gPZZWPLLFK7Izt8hscg==", "signatures": [{"sig": "MEUCIDBa5ESKZvUmWCjl2WArJVUzbznXD3M0sIWtTdLcrxrtAiEA4lIkvybbWNzew6tRQDSBczZhbaXLFSqr0x01XWLwJD0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13980}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.9": {"name": "@radix-ui/react-toggle", "version": "1.1.3-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b42e3034521baa28e747e1093d374c7ea0f43621", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-rxI0sJjqmGjtzRnfQ5lKadqmD9MNbt4m1xtF8ee2dPnkaV3NN7KrhEGBd0Un165mHNkgmySFWdTCO2qwkpmP3Q==", "signatures": [{"sig": "MEUCIH+UoJHY+ub4LcixlwpLWIrZfkUGMZpghloNUu7IaNrrAiEAu0RD342usMdoWTZQ3+XW5RF35U6UQro09rJzVHVwEiI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13980}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-toggle", "version": "1.1.3", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5e0275c3788a38f77521a47c1446d2efa840641d", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-Za5HHd9nvsiZ2t3EI/dVd4Bm/JydK+D22uHKk46fPtvuPxVCJBUo5mQybN+g5sZe35y7I6GDTTfdkVv5SnxlFg==", "signatures": [{"sig": "MEYCIQCA2xX5mbv5NBV+PTR6trcpNhe3p4B7QKniZ5LWAm39jAIhANZtAFP2H2DRAHWKU+AGKAfFwr/x5q8fLQhxdzyFjez2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13932}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744259191780": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744259191780", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "448f33a79c57dd9f5a3543e14907098bb9ea6b98", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-JtkeAGy2hMNobhU496tSi2tOUV7am5F1N2SKrwrxXvyMqN7Fa/dF3ZEQdiHBLw2iNTfdkXpgGOS8hrj9PRKkkA==", "signatures": [{"sig": "MEUCIQDfTTradBL7et0wbHKZPOeuiaoorqB+e0T25O8pFXuiyQIgeV9l46LeWyI967H5VohFoZECer3pZoFE1sgCpK8mpHA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744259481941": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744259481941", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ebccd5406a8991ba8737f980fdfa0e548646db33", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-hYjd2K3puujP1ZD8gxDodZHumEF+3d1Uml9zWAao7YmdPrWly6MQ/6PsfDrgsivKNj6nJa42Tq2SSmbHEsLzAA==", "signatures": [{"sig": "MEUCIEyNVIligJAe99/+pJXqv5W3ZTfzQKruVKrqEUoI7l92AiEAyaL58MVwohCGGFi0Ar7DcEScRZj/EXHWTROqSXRu8GM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744311029001": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744311029001", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1baa373c9045ffe99e86b3f13a24b6c3ac67512f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-tieAio+VXDAOUN0mv5lR7TdoR1zYG1pYRZB0YARfdVyMQpJf9qUKLR9BDiSkf6kScvg6W9QuTNJa2K7jeXeZbg==", "signatures": [{"sig": "MEUCIQDnHWZO+/TCTr5hD6hzZ1+eeXgmuDDOMK9KupX2uHLsbQIgYk5VD3d8OsJMUrzQ7shO7ZdFaeZGX4oPoflhZvP7YgM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744416976900": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744416976900", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a252ec2b8978bd2ad280794681f2e108921709c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-XVMR1tLz07IC7ck4CcDoPf68VmjR/zjWgdpuqxTroD237l3kqOu9OaML63HlbmLB2jsY2x0q8Jys0LB68lHv/w==", "signatures": [{"sig": "MEUCIGT2b/YQejqAEUZUxwYxXsn2VZDS5DhRfynMTASWLPcyAiEA6GSeisyFgl7y1jxd04FVm9p8BN87kyFAx54blWhd0oA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744502104733": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744502104733", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbdda476f6ce0af25fc3741e06d54b408cf49c74", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-gFH45zBYWTzNULvcOkm4vyqFg+IXwIfSjZtmUlDTXd620GMa4qvbjpGCW5etLuux1UZuOOqg8vI1clQ2zgqYcw==", "signatures": [{"sig": "MEUCIQDsJ4WLYs4toItFZ844yL587+Ps14zieDocOWjeCOWJkgIgYmDfNuYuFuCoKzk2i7sXayS2aFe/pOUFG16x5jgrGho=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744518250005": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744518250005", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3219f9f7dc18aa8d6e13162a617dc7d76ea0e8e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-<PERSON><PERSON><PERSON>aMPM5Zyoc3IfumM3EwbrrRvRD3zr+kD/1hTmbxj2gFwNdWTExZREhpcmymwkxm99WqcmPwMxDQib0GSg==", "signatures": [{"sig": "MEYCIQCN6J1851WcA+evv0mYGKfLAHKI9pH90PYmA1JvqrjmwAIhAJ/6FaPpRWknNnlSpZxfBSMeZx0kTOQ1zGLlYEdIeM2J", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744519235198": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744519235198", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4ec8161ac385994f26113c9fc2fa9bc123062069", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-9BFP9wkJK5YGI5rBTI1+BtoqgNHZhr1UJhp6BSWA41ifqq6MJZcOyC+OQv9tFnMke3L720jGmA+81GibE4XqwA==", "signatures": [{"sig": "MEUCIDUj2+fzEopSvh23Hfp2xeB6Wc916FK+WsxbUPaWZqRsAiEAwOdFvH/zgfp/4ydkqFNyQ5duBNn454Y0MYK6ImjwJgc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744574857111": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744574857111", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "130d1ea55463aac64196b43c712a8015b4c82134", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-Nqba3HN/g2LxV4hP/+stLPCXOEb/GJrUgmZLhDITzcFpUZ5oQ1UOKDBXu0dPWLZjeya63oroAguhcC24H5l7gA==", "signatures": [{"sig": "MEYCIQDuQiksMFh23b0bqH0tL1pwahDVt2ZU8s4/2qLU5Oa/pAIhAIZxUibSqGlqPs/5gfp+FrYbkSNaGfWHFL9U6GShw6Ys", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744660991666": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744660991666", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c9904746383a541ece5a1261f9f4b4c86a6a767e", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-7kQewMALuVmMVJr3ZjrabaGJ3J6IFW1guolWY0+3Yo749XNU2VntB8m6QYNYs84JHxRLgGSmXOYVJ9iRXHhm+w==", "signatures": [{"sig": "MEUCIG9ihG6M+AW2+m4xofxKw4RZp1j7oTmX5h9GO6Xt7Tb/AiEAgck8Yzo38W+InyyxY6imTSek8Se7ljvf2vtL/niCDqc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14535}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744661316162": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744661316162", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d8aedc84a6e53260a3914a89572fe59d2c385a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-z81YJVH6o0p7hZJ4ptY44Vn67qCL9ZcphsBk5GrjaYHnuiXWJd+7MmDh3om2YRHs9x02gwPpZTqHSLZMmd5tcQ==", "signatures": [{"sig": "MEUCIQCyq6Hu6JgVQ09GtssrUudEoNEWkXpYm4R3mhA7GZppSgIgY5/GV/YuOAqly/ukSSH1TdMsUzfcXwOhG+dbIIsR8Sc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744830756566": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744830756566", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bc8fb0239b3839e85aca9ec2b034d1e2136e7a25", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-nyWtS5mE6HqaUrtSdfWazwZwKPjDD4Ft8xccLPw6VpdZJUNttOxeX3mGgnskL2ZHjv6VQjd+zVoz0HFNTEOlCw==", "signatures": [{"sig": "MEYCIQDALhxG9Y8ar4Vtzgq2I7Y4zRv6zaKV7KzY93tyNtYI+QIhALvuvzRuYsHCqGJBcsrOoxMnBm1XOpIud5md9nEgbN3s", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744831331200": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744831331200", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9dd57d0d79d2d2a0e07e6f8de39780cdf9bfa061", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-vMEJnwh3WMyVecY0TTMLTogRIDBylJH276DTKvJ5nhU/uEPE09ge9wAH3l8bjDfGmL5IbbJGRWCtiabukvoSWg==", "signatures": [{"sig": "MEYCIQDr/t9kbTHfknMirmsmPTdsI6o7aWEUCjOnn4L4km0v5QIhAIVWTtHJYNRRU7P2nnOqk7fAg7jzNlg18Pc/qVHSTjAP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744836032308": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744836032308", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5c4b8b14991a5863034343dfac76bca207fc9943", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-9bmSVqgxoi3EzHnwASTuGf9hnx5dXnf6iEVgEaqQZhgafX3dYKUSFD5HO2y9jv3yfvm0VLJHSFEX3ngiH0lhQg==", "signatures": [{"sig": "MEUCIAGbBp1SSTNdytfk0i7sEu8XYmAMRSopovYj6Fyy2E8KAiEAzTys8xtpU3AILQXKtmPaPcbkvP2cIu9m2Hv7TZpEM/k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744897529216": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744897529216", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "51a1453e9d69d5bd1b9d353b177745373349d6e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-hxHFB1FaaSGnDvHK9XbbC1kjUVL5E/eeUaSwS+5BA/ijUom4fKpa9dqKqx+0Q+vzvVy8gi1EgeVrHEtxreYppw==", "signatures": [{"sig": "MEYCIQCjLfrTR/G81+Vt8Jc34oaUE3+c3Qbw032GOI78Y+K8JwIhAOyMTt7dl5GimQtGvQ4WQU9Vu6IvzT/1Uf73xL7B0qde", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744898528774": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744898528774", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "41663e70407bdd716b6d7af28138d719f3279ec8", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-oVqEx2l36XzldYBznkoy8RwpiO034ntPwINPJ7zIKwrSIMpCiS6GDsSX0CWvgzSnzXbONota4gbB6v4bPPMM+A==", "signatures": [{"sig": "MEUCIQCmUCAeiJ/ICWpvp7GdbbXDBmdxTbPz5N0N5zosV/fcBQIgNii3EcjrGd4b9PxxOW/hnOlDXE2UhYfaTwe8bqneXt8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744905634543": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744905634543", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eb1a6cab6ce72861624c9987dfe1d1195de40717", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-O/iaoW43VHl4EJhERcDmrCqyx6igoIBRFPM4w5ja2YdYhT9I7U1CvQLpWbn6TiH+nsOf7T7Jjl9gOa9UhULPoA==", "signatures": [{"sig": "MEYCIQCuVaGrYJ9IsoXJnQwu8Z+3xIb/XXHN+VGXwLiucFeiJQIhAO8m6BPBMJSplKbdmiuYzRRI9llUd6eA1VNyQ7nmU7Db", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744910682821": {"name": "@radix-ui/react-toggle", "version": "1.1.4-rc.1744910682821", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3605befdd5ba34817a97bab6a0cda2066fcb2f2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-9ezAJd2nuhkIY+P91mfZCqoK2UozhNbOsCBcKxGCukUSP9YMNm5lRUl8c5sDD9BZbFFUfVl7ppZsyZLaS9qCeQ==", "signatures": [{"sig": "MEYCIQDNUObvGFo7N7e4boGqcsxXabRrXe2wQ9Btxu8H9GMvgAIhAIB8idSBXrcl+zohx0FQ7ekgWp8uX9440KSpdd1/lKz+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14607}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-toggle", "version": "1.1.4", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c85b6665389a690a60e7098fe3d4a98f47f27e20", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.4.tgz", "fileCount": 9, "integrity": "sha512-qkBiG1gh8aWA8PGbGJsWOx/PoxYHPSwtOG+A+eToBje0ff69+yuztbPsoNK6GL+QrZoAuGOpJG5i/nec1x7n4A==", "signatures": [{"sig": "MEUCIFp0Ku2HSUtn9W/AJa0/gYRIOzNDtCFGmz2a2Kfw2aHrAiEAplH4tPkC+1IFDecg9sPEvAuZ0pAEfQCcsCuCdSHVLV8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14556}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744998730501": {"name": "@radix-ui/react-toggle", "version": "1.1.5-rc.1744998730501", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e882b6bbb6f43d4d4759946fe478b74b665872fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.5-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-r3nfXGAEIfsrO/oZUeuypogQA8nJVGVHe98sRLbwU0w8giDlJx4CJi8e8zswMM24Plm9VBOc5j9NpuCOEaSNDg==", "signatures": [{"sig": "MEYCIQC93V+LOTP+h05oOaDc9SU0avci60rDdfmeuCt9Ia/E8QIhALvvvLyptffed8g5OdTgo33NHNgkizCemvGingFUUGgd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744998943107": {"name": "@radix-ui/react-toggle", "version": "1.1.5-rc.1744998943107", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "398db5f82d0de7e4ad2eebb13bf9bf2fad5190e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.5-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-zdUuurCutWTMEU/I0HzN1ogAyhQ0uxOStw4mkikKvhutvWEEaGtHieY2BQDD0ERVAJvA4vxLdLeUNgM1IkFCQg==", "signatures": [{"sig": "MEUCIQD4nuIzP9K1iwI3hJvcbGO4hJWoUUFH5vkWxYD07BudegIgBaLZfu88nHS/LDpgfbPp+35HDSoTUt5DcgFY1Oep18w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744999865452": {"name": "@radix-ui/react-toggle", "version": "1.1.5-rc.1744999865452", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6e3075146f0f4d53ed4e0ff1bce642386ec9b7db", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.5-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-nlIrXfIJjXQPi7EqRV7FnndlQdM3KKmvULfwuCQmqOceytzL7lhoGsOkEyWPs/UUtOURybiIG7xcXKBaZCOPrg==", "signatures": [{"sig": "MEYCIQC/6Y/D8oW0tBOLM+9G0RlTGRil/SKRtOSBKQgTZpw/gQIhAPd+PT+mCqxVhCStqJTKQcsFfIyhbCNqfBmQy+rL27Ds", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-toggle", "version": "1.1.5", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43f886e6cdf3ded07a6a152b0d223d2eb4655626", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-PvWWb4q5xRPcnaQowr+ux0Wq073P1FVaZeLWGpbjwFigOdPHpmP0VjvK8B+ZTomE3h28flRWjvODcQ6zUuSDqQ==", "signatures": [{"sig": "MEUCIQCUiviUCKCwtt2xiBG8z512NoBheQdRzM/H0Cfzgud0IAIgeUPfQ7M1ua1KNl5zS/pa0+Jx5QrTAmayAhjRdOyaC5E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14556}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1745001912396": {"name": "@radix-ui/react-toggle", "version": "1.1.6-rc.1745001912396", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d09e00b6c7e95a326025c8e51deb7d3c38eb8013", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.6-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-NJ+0812+tjhNcbGKBDD3jlulJui/dNWiYhBWIFxn3OZBYyMNo8nr+tGTKySv8qt/9dEB77XPfJ9QYfDt1DEE2w==", "signatures": [{"sig": "MEYCIQC+W+PQdoa67OWR3wHemqNnqqrEqjejLDPAR4Hi5ULXQwIhAP9RxjTQkd/0PTwkRzbqSzLDexxkx2kjDdGp0dAStqLQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1745002236885": {"name": "@radix-ui/react-toggle", "version": "1.1.6-rc.1745002236885", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "44d557967d64797556a6657768d152fc6d6296a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.6-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-pLntdltgKeZGvZ7Lc/TnOLI3qMMcN1uH/8po5uBmzWkAmcNRWb5+8mMxb+Eojs53RgUDohpOuaZeBCjie4mp5w==", "signatures": [{"sig": "MEYCIQDw7Vl6hYviCvJX6DyQfW5JZ14r/B9wsVSzDpBfoLi57QIhAOw3whVrGuupBkX4EihAQChEXb7UtNkrAY9wul3r045K", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-toggle", "version": "1.1.6", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8d993d88d2abcd327fd18a6126adb2e6e0848a3c", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-3SeJxKeO3TO1zVw1Nl++Cp0krYk6zHDHMCUXXVkosIzl6Nxcvb07EerQpyD2wXQSJ5RZajrYAmPaydU8Hk1IyQ==", "signatures": [{"sig": "MEUCIE1ZckNGcL8zlpgrkltIcgW6ukf8r622clmYpU2Rqj+WAiEA5Nqts9+NmhJ7bwycn6HAP/cxQGkMTNRPNEd/vJ5TJ6s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14556}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745345395380": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1745345395380", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f4fb1cdd957d2f8e945b45862db4fe9461b5d39", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-gbUMWUHZjJRuYzoq5cWbOAgbifmSApUsrJppO9Ec+riGPs3QXvN08pa9j1aKJxMjTJO9qLOMLAPwRPZxmMFIdA==", "signatures": [{"sig": "MEYCIQCqE7aghxOf76/kejBP3BS6GOPfWxvana61b0YbtSjcaQIhAPxtkq8u7tBPezNi1d/Eyi7siK5Y6IK0KirYfUBS8W2z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745439717073": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1745439717073", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1d775a683a2aa7228d01981ec02c30ad92c3c7a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-FTzhVYTr9jBDLHEMiPahTpKOCZqBqhzgADHpvNskzEB8aWR637wDr7jHHSYI333Gd6NMaxKZ+XStrx/EAzabIw==", "signatures": [{"sig": "MEUCIChPL3IvSUtttOw8+4IRj3crPqDCuW/+Zvn7rAa/QFJmAiEAqUDCMODkOgVF0l6hF1Qo8En201NPftajt0PG9tmNo60=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745972185559": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1745972185559", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f4acd12523503373f5aa43d473e89f1e58aacec", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-QwNiLAr2zWTlRKLgzvHF+1CpcTESy97VMmNpTRfWnu9kkg7MOhK899NkX0bVwEJYp1sP21RKkIhhyDVCoYfIag==", "signatures": [{"sig": "MEUCIDcfxMmJpBtXPrC85mbuG51gdoNVf1AlgHWfFZbiLZivAiEAvDEE+8ENyv7J0cwJPui3J4bQWloo9xXVRF15LqoN6NQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746044551800": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1746044551800", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ccf821b3d7f8f00749533f0d3c33abac5d8d0802", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-llmu8n7ejC38cUqUgfyRrbS8gUOM88glShKT2OiFVqu76Osz2ZsvhNdTNDoV8/kTucNv0xHBPQcn9Yd8LOHMUA==", "signatures": [{"sig": "MEUCIHJhCHLE9dGEWJCJ2wF2ueNoZqJltTuDpar+0noflnhgAiEA6RUsiDZzUyKv53ejQXO/MOHBoTigr7rK26BWDun1G1k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746053194630": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1746053194630", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "670a8a684560ac7c21e7f4e380b7d79f3d3dbb20", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-A9ZCFP5f3vaFfz/Uw4+ILBZBxXw4YlrB29fKjsHNP2PWbdeG3sIJ8kTAedatXm0uHJqJ7/CYgD0hq2b4XLSuYA==", "signatures": [{"sig": "MEYCIQDD7H01wYDMSu9HraNF3J3PHqYANmUmHG3+++0j8iyFYAIhAPJtNciRL6X8T7Y/VhEgpOGD8u1od8BjeJGiJVQ4xLdF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746075822931": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1746075822931", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43227f7e7fa20586a1c59fdcb365c7e37043870b", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-Q1TZAFMMMkdXIyDy/465/OyDI7WD+3F9mC1eQjinylmY86Wb+P9+JStX59hAMDaoULQWZwGp89BlfvCuFcE4Sw==", "signatures": [{"sig": "MEYCIQDodft/lZo7wgOCxG3L7JEboUJgZHL/QUt6DMFcthArTQIhAPlSr7XxyM1zKZhW52Xxn8mQD0Dvpc2iiUjYq/BGMpaY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746466567086": {"name": "@radix-ui/react-toggle", "version": "1.1.7-rc.1746466567086", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ece3337758f7e4c119615321d5b1314a0d1a5c87", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-PYC9/sMEuT/ou40IhqlzKiL76BW+UFMZnBm6PAmRmuhsY4+Kde9RynDZf+DxMH3SsXXxI/mtQszvpTefhJxq2Q==", "signatures": [{"sig": "MEUCIQCPUNaVakwyTXyVTZmTlyC1dSL6WD0D/4BcVD4tF66aVQIgL4Eg9DgcGmfjj78gUFxm4s6bsW6zmd2GfDJZ/XlwQ/o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14590}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-toggle", "version": "1.1.7", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "42a05d542bdaffa4eb48e2bd1760ef5457f60862", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-csPusVZxTKWhroruPycWf+GTggB83MszoXyRoj+uvhqHvG913/g9HHGEnMDltXyjzyIE69/nudBCQlvLkxiH4Q==", "signatures": [{"sig": "MEQCIBucMn4HqIjf+YWe8KV2dNiIXsz1IvZI9h5RNudk67GcAiAmi9oAKPFclgbQsgm2+ay3Xe4V5phuKEpwiO4mtuAKkg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14556}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-toggle", "version": "1.1.8", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "49966facbf94aa7d51293790c78c67c1f7487aef", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-hrpa59m3zDnsa35LrTOH5s/a3iGv/VD+KKQjjiCTo/W4r0XwPpiWQvAv6Xl1nupSoaZeNNxW6sJH9ZydsjKdYQ==", "signatures": [{"sig": "MEQCIAwRHuGkQXgj40mbIFbVtuu6/HmFA6D/GaJiNVh4ZtUwAiACwEI6kHF1Ot7atO6GFbMSP/HESNOqdOGnHkorQ5lk/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 14556}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746560904918": {"name": "@radix-ui/react-toggle", "version": "1.1.9-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-L9OEf/To7aDbfXSn21UqoJ9aYCZaP2DW8jgZHl2oe4pJq/xco860TICkp9VPLF5n5qT9pIB+bI4ItGLWd0rolg==", "shasum": "618523b74cf41b3256707d176e554c66754d576f", "tarball": "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.9-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 14594, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCqsVTsIj5BgDJaNFLMUruDeXJo0RRqkzaJWCNybQ0LXQIgX8sxOSq4VAQIybLWQ/wABg6gP8eaFa9w1uza/7dK1gI="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:12.422Z", "cachedAt": 1747660587966}