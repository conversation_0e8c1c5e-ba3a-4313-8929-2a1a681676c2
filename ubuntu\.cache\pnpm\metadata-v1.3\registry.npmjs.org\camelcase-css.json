{"name": "camelcase-css", "dist-tags": {"latest": "2.0.1"}, "versions": {"1.0.0": {"name": "camelcase-css", "version": "1.0.0", "devDependencies": {"chai": "^3.2.0", "mocha": "^2.2.5"}, "dist": {"shasum": "468c391d9c1ebc9540548ab5294a487a5c9032ba", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-1.0.0.tgz", "integrity": "sha512-mcLid07BxNg0ZP1+mKz+CJF2fWskaHBrqKQ7OQPgmncmPVLnkdohA+7ZH82qq7bDX67bCsBrT8j/8LQrR7rhiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGXvMhAdFLkJcdhgQ8jkQoTqbBfuRY3BtHlfIUazqjHcAiBlJUwKp5wNqmM0X5YHb7oVyE0UIpI31/24VbxmgVgP/Q=="}]}, "engines": {"node": ">= 0.10"}}, "1.0.1": {"name": "camelcase-css", "version": "1.0.1", "devDependencies": {"chai": "^3.5.0", "mocha": "^2.4.5"}, "dist": {"shasum": "157c4238265f5cf94a1dffde86446552cbf3f705", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-1.0.1.tgz", "integrity": "sha512-cvhbU5XiKkPbU4TZ+8o8uMFAeNtl31W/EIy9EKLrHKFnz9EsS7/iPaKr1FkU7w5PEmCJXeS/69y2v8iUhFfn4A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDgS39Lp1k5KPWurlItKssnyWvPGOBuy5Xt6YAFA+tX6gIhAINPsTrK/BvhBDAfXEo0PB8g6MuMpLWNKWsT6Rfibdqm"}]}, "engines": {"node": ">= 0.10"}}, "2.0.0": {"name": "camelcase-css", "version": "2.0.0", "devDependencies": {"chai": "^4.1.2", "mocha": "^5.0.5"}, "dist": {"integrity": "sha512-FnvdCC/TkM9eoypxeMPS41Vxu7moPmgsu37oeVxIhiXi8t0KrGGKCLaASkQ+/5tX4X9r/S8UDlV4Efz7VvHIRw==", "shasum": "76f0aed4924b9d3dde36adf14225c06d5a9835ae", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.0.tgz", "fileCount": 4, "unpackedSize": 3104, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDmlODm2+B2vVw3BJM9yrxqtx0O9Ft4T5NEXiINlBHS3wIgP8F8GN+EeqT0DI9BGICkz4TMSQEeJ/QD4SqdAm/oAfI="}]}, "engines": {"node": ">= 6"}}, "2.0.1": {"name": "camelcase-css", "version": "2.0.1", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-optimize-starts-with": "^1.0.1", "babel-preset-env": "^1.7.0", "chai": "^4.1.2", "mocha": "^5.2.0"}, "dist": {"integrity": "sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==", "shasum": "ee978f6947914cc30c6b44741b6ed1df7f043fd5", "tarball": "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz", "fileCount": 5, "unpackedSize": 4051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbjTpCRA9TVsSAnZWagAAzpQP/iM8aenqDc7dc3DGueCT\nVoNqTsE3vvaTGD/bEspr0uJOnJs7DNnrFlhGIuKm+f++dgWZ7AAa/OEnmqoH\nHyTaQeLZjX99459/b8x7fjCIok/W1I4eM/yZWXg+rtLmYAYd0ODPrMm+fZ7p\nEBM+Uo0cV/4aNhsQQVRriZfXR02mR9H11nNx9Bht1R/n4+sFGOSZDkprKH9i\ntHgvXDkxXGPm/da13LKeVVzzHySS9/WOAQi6W5P2nbmTSBYuwpOv0xXKOik0\nDVorsHmHbZtTWtJB9cIQhFLDpxH6sy8xDuz/GODxoK9GLBanPW3bxtCUAcb7\nzG/NRP/lpLoZzl1Tna8Yp994SF/rthyP6fuCqDKXNHbxtFSJoO44muHNEWMH\nOUc8Dbzr3C/NjBJSWAiFzHwZYmRocTeua8DxLl1lcIOHoHe4+l7ljAAhEc/L\nbflnk1Rhv0mCyuz+mwJemHzb5+EItdwh0H6rpxvLdKOiDkGdLLpvjNMkuZ+J\nSeTMoTJfE4k5TykLMsjXwb/FsOvjqPsKrpBIVssQK2ZvrxT0B1ckZlbrjVze\nqucfKIXlOUOlVG8YDGIaI62ET22SqVtkukqwpmLmdlyvf5UhNjZKu9PAi7n+\nGdhiqW8wFKP/+JxR8wk+Y7HXoSHLWzuF7a8tpKgVBaarAjX0EZcqGCXrg4vZ\nYDIj\r\n=gVkR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZtNWVuk0LB2kkUZk0MYujvr33KHVXuhv35PAoApUlCgIhAPxGdVz2zcny3tr0C6ncpfjp4+2r9360hvxd+7gOxKu9"}]}, "engines": {"node": ">= 6"}}}, "modified": "2022-06-13T05:30:29.480Z", "cachedAt": 1747660590395}