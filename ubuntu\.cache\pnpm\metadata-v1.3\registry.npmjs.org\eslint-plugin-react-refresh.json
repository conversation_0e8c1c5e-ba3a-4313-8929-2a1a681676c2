{"name": "eslint-plugin-react-refresh", "dist-tags": {"latest": "0.4.20"}, "versions": {"0.1.0": {"name": "eslint-plugin-react-refresh", "version": "0.1.0", "devDependencies": {"eslint": "^8.7.0", "prettier": "^2.5.1", "typescript": "^4.5.5", "@types/node": "^17.0.10", "@types/eslint": "^8.4.0", "@typescript-eslint/experimental-utils": "^5.10.0"}, "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "2456fde503be49a04392b6ff04e06791daa5d546", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.1.0.tgz", "fileCount": 6, "integrity": "sha512-w3GSvx4KQZ/Z/5vXGBbN2OsccEHhygukHtOcQW3Hrf+2xOH1xeqSJN8NO2To88J4beV2yLauzyrmkeGdS0rzaA==", "signatures": [{"sig": "MEUCIA63dq3bNMmGXGmec7rbLXABMCleCjYP7YEM5AXpN/WZAiEA8cvXrvwtrQ94BZAEORgGvP83ml1f2zpu49HmXAe1vy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7XbXCRA9TVsSAnZWagAAgMcP/3j1uKM9q9lu1MXgirPf\nYLnra3oI0av7xIDqXoybrFwRY56p4RoLNLtFP2nHR2xHTP8NMCnlTNlx3AAB\nItVC4UiLNxhrTIEO0Ou5k82MJcRiHmyY4VWCp9C8IH+rECMB1JXJGaflzmpZ\n697U/eO9mr6sd2f9ASo7+3TrZc+LXpFH+HlrSXyeRM5HVNrQ00eznPHgOK1Q\nu+2P2JNXR5n+5qIV0d5maSoHkmLhQ1cclVbcuBt8hnehk863AOSmmbl5TJeU\nLlG5X2dxRW2W9W6SWi5dFtqdqCbg9NMDz0vNSAt8qxFq44Ij/EbCeLU/yugE\nD+1ntTXcQfiU8K0xjW/QTFK789soT0LAAL8m/A1JTkIHBy/JyKarWDs81tMp\nVl//HpS65vmipJdzFjKLPlvSh7LJOqmeCvmurrz7WsxbgBrb9+UgMBSzk1Si\ng7MMGNQ89jREIlCoOxxUm9MRhxTeI+4/CIHWo+mFM7q+vhcpqM6N9zfNOtj4\nF/MHLtvcQQvP+nvoUcFhi9st+1e6lYfMoBlzXZGxauwDvfI3IFsJ1X+9cOW5\niHn9kZKbPLisr6hz3je9n/ch1MtSk/5yk7ZS4knLIk9edQyWYGmCty0ObIaZ\nxBL7LlJ5pdEm0MVw6brl1WKXZR1LLkl7d1wSuqk4gts3OwF9umwM/KVMaw8g\nK2qp\r\n=6zQk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "eslint-plugin-react-refresh", "version": "0.2.0", "devDependencies": {"eslint": "^8.7.0", "prettier": "^2.5.1", "typescript": "^4.5.5", "@types/node": "^17.0.10", "@types/eslint": "^8.4.0", "@typescript-eslint/experimental-utils": "^5.10.0"}, "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "278171f0d428581ac16ec71e0cd959f6843e60a5", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.2.0.tgz", "fileCount": 6, "integrity": "sha512-oBEFVicl6pS3FJ+LDlRE/l9Fok8J771g5xnRgDCz1Ypg1k+dEQ7wiOZAXCoa7haYqOKwa04ss9yOYzb5FonBGQ==", "signatures": [{"sig": "MEUCICxWIgdpSqcTfUNVE4nJ0kQqFcgi1qvsm4SSpwox1WoiAiEAo7Awf7Rlom8b7jg/vQhsoqHgYt27p6MJbdfEPHmV2CI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7bIBCRA9TVsSAnZWagAAUy4QAIkoocx2vb4RbQBjneuz\n+S0yEMeNe8dG3wYnVMjIqwCkWfuYVRFSiki1BbEY1KwgM5Yc5U8HXUO6n43x\nfXpRroXC91XKirPuEcZeP+gKTI1N17Q2bEv0kHiaUcHuSuTmFKJMkt/rJ5Fc\nQ7spKCw8LfJwAIAMAm6xlNRIaOQpm49lm+UbsxZ8nUAknfD9DWpdnMhgpC36\n1vXbXc6zsn9YQKuKmwyxwomyPid/erkq39Ol+/jgXPtqnTAiqLgzZxASmMmD\n8aFfBm1IUk9Gr3F7BKPRyux3sAAr+hG3vP0B0yp7GWcKZfgNA7WXHYjngWFu\nGjLKb8Y3A6jF4xCHdJAeLpAKq0d5kVw1aELIpCzwmsi/tFiNYP/YFXz1sjCb\njb+hNQnvEEqrqt/oX0Oapg2i7QIe5ZSJOYdUUbSYtcB26DGX+pKWJTK7H4qd\nzqseLrsjSEVXcVKEOpnKYYqGBJS0RdY0FftjVxwm6MeQG/K52xmBX4UWO/fo\n+IcpDrWWZlfZbhQRW7TQY8vyJA0lLH2BY0CpYhSe7CBrAkLdFRW8QBVigWIR\ncR1NUdS516neNVRSCVMKgIkFFzmxnecaKES2hcHW7cj6Yd0zoVfWbSmGSJRQ\nuU3k57Pb7TGRH8bUbiPbqUcTB4kwRUwlBSt6mW0xlzIdwBPnUldoKCrfz64r\nZTLd\r\n=/h0a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.1": {"name": "eslint-plugin-react-refresh", "version": "0.2.1", "devDependencies": {"eslint": "^8.7.0", "prettier": "^2.5.1", "typescript": "^4.5.5", "@types/node": "^17.0.10", "@types/eslint": "^8.4.0", "@typescript-eslint/experimental-utils": "^5.10.0"}, "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "740dc1ca36c59a29516c1b22699cffe3f11e69d8", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.2.1.tgz", "fileCount": 6, "integrity": "sha512-6K5oZZN6LtrCoRfN6w/G0+N7hwfPyjJlR3SHuLXNKGR08z4eaxfgNmGSIvCoXZFysHJ/CeyD5vq2fRTgQt6T8A==", "signatures": [{"sig": "MEYCIQCpgTS16OZQUvVXm8R7yqFlSqfk7Jx+RqRg+IkPMS/U4gIhAP1/mjRJpsPpz26bo76G0KemCuuqDyWFaeTE+3fdV596", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8469, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8uKJCRA9TVsSAnZWagAA0GoP+wYXI362XiZT4NTSDAhI\n93TVOK8+t0OQXcicXEpKfFratcbQbHlMZVHqx2kTeuD+VpgR2ukZiF26rsXd\n+7e0JtveWjYL1Ju9eKT+cmt1jsgTQr6kPwilqGXPimNI/FFaOgCuzSXKUDUQ\n+lMkSUVXDnhSyJSnxmyyY7ILZiZXyP26vIBmcDH2wjy0a5A10jykIRwVkUvm\ndZuPMri8yISTYR870RpMDRfkdwWxDfUWCmhUa45x3SVF40bVd7ZPMW1rRm94\n3hONAO8oP3LlLNeLllZRYiUowStiakeAIQ7WZwFsJDsjvE37Gf0mXTWqTZls\nEGk2t7w19ogQ3xylOaylcDr0uNSRmME1uMynPoxCLlZEqfh+pBRiEr29HxoK\nfE6WQ2l9E5vJqIeWPBF69xB7xzdPvkR2evT/xHez4oxA+1qIaooO3PrgfOLJ\nxgGOVkxtyNmFkmwxCBV6NfAwweFfjUWLZ2Ox1RAma5Wg98FvLB5unayjtKY8\ni5+VK9tLpioplxDgxA9OyFwsZzI4uhE6U3q1tkfsgRkzoB7uXmRGY9CxWLBW\nNOjBXCUSxgDvAaktdLqX5KlIZ/BQD48/8LKmDn/TJx1tvbvmGygjZu4Sa/5V\n8zAHhrGJyFbMSaJq0tsB8Nf02enNRHfbLiNPLvMWig4PadtaY+SaD5YnQkxW\nxCCY\r\n=iIVt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.0": {"name": "eslint-plugin-react-refresh", "version": "0.3.0", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "79188e8a89441d98b35c8a9e4cd889c44aac3501", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.0.tgz", "fileCount": 4, "integrity": "sha512-fTgme5zUX6YDC0JYYo+UkhECuk6+gKvpdpgRPxnK+pl7pAw+TB68/dfZAITFYIpF5NkSg2aP4gvwkHgF5DhU8A==", "signatures": [{"sig": "MEUCIE0wH7jCb0eU8+jJDv7dhk0V0WCjEZsYdmPVGLamG5jNAiEA5ezJ9cV4pjvuTu4C1YkmsrYZT+gCxqZGltSSeOww0Mk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjcBlCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopLg//be8Pr5IeVrpFNAGAIpTLLVf1FjJkkKuvk2KpGCizHh3ts2d0\r\npXANIEFH/1L9o2TG8iku7SBOM+gsj00ldaSS0O/ygyvxzc9R3rck5vQ4hDIR\r\nIEjN98nld3YjvSnZu0RHvgofRsrNW6TgvpLhOUQkqussOeECPnMcHTJK1kYA\r\nOu786vldSHiHhG8sJJ60LJue6a799ZZ/b9m7DxgcNUdyMOTXcUMRkrFpsiB+\r\nKgnC7wh5RuDi9Q27BGGD8q/UoVDHyNOiiy+wmT9v9mAIyufvWEFPCNbvTYNO\r\nJiLTz7/d9iXPtcWGWt1LX3fy14JkoAKYtklmnU7o05BFy2R8juMKBzSaJO94\r\nJRfh4QiLqdpaZE8PXUk5TMhJOhsjAA2skk0Cn7O19+2+Dm44sTBvZBZlf9jG\r\nEYsKltpmR0V4Imhu7GNhjg8TB7JW6neYR2IysryQJhuJ4cAUKtk/jM7Gd/G5\r\nPjNG0R80nhUUx9Jd2BlQFooidiCgFvYWswHwrqC0YHgHY4abrdGb36oaTzR+\r\nzewZUpwGdvsNz4IIR7LFqLRSmIVS3xi1C9h2ALXpB2LbyFQCg8f89WigEpx5\r\nTIu2jNOiuQboUYMXLnLHatfxkWGMV/2yK9HTrVzaFAv6Ptx+S+pFHYRSTJQj\r\nxc/80poAujm6H3sHnpMGUom5oS5HnlZ/SfY=\r\n=ZK4P\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.1": {"name": "eslint-plugin-react-refresh", "version": "0.3.1", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "714c62253b167335b394614dec152a98baa49cf4", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.1.tgz", "fileCount": 4, "integrity": "sha512-eGTKtvzy+qKwTwXVz8Gnp5/7THN/rwAU3AyCvsqle2B4rBtMSPuH0HAoPSfg4ezUocYK919a68n4ftVZ7KT9NA==", "signatures": [{"sig": "MEUCIQCV8PB4vV3eerktfogGeGupccJyaPTzJE/qxCZqii0HxgIgeJVtJZmQ2nvVzATg1bFLdAuVFepP2QEsqoowfLVHvVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc2JEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJFA//eradBWwoS7DRyQrZlMil28/drUgtoAKunxm3cO/Y70yFzwqG\r\n7xBKrqIgI4iSzVIgUgWgAqm+C4NCKhGtGE2gvbBkl9B0Fmqy09ENy66vryGb\r\n6YZvymU8PgFKQiFoxvZ+2nevhzCoucAFl506k6QnZcyI8FzXb7+QS+89jena\r\nK2F4BBfSvVSRqIsPNzYS7FnkeQSdODZ6CZxL98M31An1KIBv+yKDz8JdIMhF\r\nVAeEfRxpYBfRQV1oKtl4JfE7+yIc2VbJdzpP0R+38Rh3h4hHeidOQrR1n3rK\r\n6nl0xyLl0qPuOmLT60rxdWrcEPb73X4+JGQEdha5gAnXV3BfsR+kyLyW4SN3\r\ndDtdx5ysBNpluKZOt+hTfLbL69dErjsoo8TGuqiAIfC25R3606750nhYDw0Y\r\nx9ptL2Al1D+Y2S73TUuhSWz1h8dzy2lX6weWzJBFlqpPEP4iVg0HggNKBP+v\r\nniqy7y2E1yWbKF2/P7cpqK4qOMuosutUQGc2Ctfqb2RKF/wWpA4Rj4e1CwZG\r\nmqF7wvBFtFQXXhJ1kaAut+eWqVCBRrIObbLHzzHahKv3J+/E/eyUHnxrLkPo\r\nj55p1hciTYfQa/iLyKXsJodNtJlQsOuyqnLyW14zueh9awB8QSNGp2X8kF1B\r\nJuGc1osSL26gq/SmNSYk1ulPEYgShOuILs4=\r\n=r2WM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.2": {"name": "eslint-plugin-react-refresh", "version": "0.3.2", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "3c133bad2802b72f302866e20f0e412f5d45b434", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.2.tgz", "fileCount": 4, "integrity": "sha512-FbonmMTOIIskIVbaowKbUYPFdsbIXA1nRbFl/KpXBmKPpg5OjbbF1qXymTu/GAqKsU6Vwkfy7P6ngHNte2XWKw==", "signatures": [{"sig": "MEUCID+RSeqj+absbwGGDLSXNMhwqpcVrc58uWRvUQNR7c4lAiEA61m1IdDfxgSpWzWPN3O1l436dniBV2qOaWEONrMMtX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjmkLTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovzw/9HqaqODNe8xNENpghgZPb2bdMXYCI8oo9X+6LFYv3BfFk/BQh\r\nct+jPUTyr1UzneZ7N0SeNok405oPEx+0DyJyFVi9T3Geg+WBrjV8mGhgkpAy\r\ndtW/8gn3q1wV1lTfV6dH9LopC5SANdLBG/J3bhEpqObYOK6gM2LZR8fSmEEE\r\n5bOkVp5auSYD3MQYoTkx/2vtqPHqtdDcHNdbw600GWdnWCfZq0iiu6MGPLPs\r\nYI5nrF2wMNl/CW3JOI4TvHcl/CryfNoy9llYhyLZKBoWVVuT7PkYfvWjhVMu\r\n9k9w72pEYM9BKAJapWfjlhkiFeyqVuwvtgK6fXSPzQi0BDjZI/dlt0elLJTn\r\nUGve6xC0eMiGrNRmMTJYyilhwDy7pxUEpVjy0OsTauzGlFT59G8lbDnFaBX7\r\njDzvTxmCPjTSq11E9vdkw4wfp80GGrEP7KcVpbff5kxXSKlaLmafR2oMqy4q\r\n0L1Wh7c5sO6AQkTdJgSJFUr+RGcXrE8XIOL8jx60gHCHQpcwkhGffMSAwaUI\r\nPdbblPIrnNWZqK+Q0vnNFmgceVObp7TO8/pxDW2ghgxzP79zJaTNi/OOeZIV\r\nFsDpB8hhl7G/NZhAeDbqUOqP/3dKSuyFVjquW8+d54PjIukvcPNNiOKR8ZBv\r\nYi8SNDtMlJ2zehK9TMw6PrXnWK4akZqjzS4=\r\n=wwgU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.3": {"name": "eslint-plugin-react-refresh", "version": "0.3.3", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "62341ef0d4666223dee51ce4d151cd35094df5e0", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.3.tgz", "fileCount": 4, "integrity": "sha512-wcNtgIs7/WIanzLKyEGRz3/BNhtyEvOpox1onSaNDevyDTNYJ/z37Tqbtfj45QDZH5Gtz9v10Xs2OGX5NhoxWg==", "signatures": [{"sig": "MEUCIQDQlCnjy+ujF56/r6zmeiH0tKVqQwMV2vXuV+PC6I232QIgQO98VWNBbZQ6NMH6U8QLolkdUSoE8v6EAUOa++gMgtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1eb5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh9Q//SJvT6YQWyzwFBcL97OmNUU7OFDGpk76B8C8eUyUHjasNZPpq\r\nvMzYglTd5vikjWtt3etAUyQkLcXsb35+HjECHw8dq3zkZXaBI4opxFXyKh2Q\r\nMrvnl6UDaKGTD+iqM4TDsbJG76njP839aJkHHiYG7J2TtNugCCUITSKhNRQk\r\nNKO1Hdsjs7FEtDJbIb63ocj+A5S0zsn8geTJgXkDGg+FexJu4zZAZVG4P3A4\r\nBuVImTOC7Me/KIqiPx9fvg8Z35AcLfsHo2YVv44ohQ1cuasN0ydFsCVvkrOQ\r\nS/BukOOS1SaJkTudtKMHYQ/uryFL6p5bM8FUy2F7xJYr0pP+za2PTQwi/HRx\r\nomXHBeAvDLm4u+yAX3c0HdvbnvH5wTCra/THtBNajmz+EVjLGrBt3dJcDXfY\r\nYFtIzad2BD3HFue8c0BF3mXVNVtSplevHBFxZqf53rqWPwK0yTVayIqXnfzp\r\nMN4s6IcP/bV5B3lKIkk/YFPiEftz9S8BZLcflN/Ex4cbq/32FwpSByMCoizV\r\nc7YuoC9a0Jjyop9SSnYLlQ9JK6N2HIoWA7CNIQZeTIoU6ExYl4ZSxlldNGNd\r\nMe/E7VknPk5nb4OEGzsdjUSOP7fo57CvgFef2Mw0gmzHhoabwpl/rSQUcO8j\r\nnkF3JhZvyoQFpfmWseFcqRlHCpAs/o7uHwo=\r\n=VqqU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.4": {"name": "eslint-plugin-react-refresh", "version": "0.3.4", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "586eeac5f4a95d2520824dba68511540e62c8491", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.4.tgz", "fileCount": 4, "integrity": "sha512-E0ViBglxSQAERBp6eTj5fPgtCRtDonnbCFiVQBhf4Dto2blJRxg1dFUMdMh7N6ljTI4UwPhHwYDQ3Dyo4m6bwA==", "signatures": [{"sig": "MEUCICzgss66NINcmjo53m1c6dEQa1GGGd+jYijSYWZJrTv1AiEAvFTNuOs+yIGHiP6SOEWC9NomqQg4LEs0BGN11Ogo18k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5kFRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEshAAlNt3BBL7VaSUiptygWXFxxTJo9ouPbHaYTcvmN+0O5Sg4vyE\r\nFUKqZNCGU9hjHdI8+IA7fc8JEGNvy/Pr5cE64gnRSKXtkBv1LEQmh+zLDsxX\r\n87n8Vr0lYI+gIwL3/FZ1MZDicTzruTkHH2sS8nzQy8cLUopuIYwvu/r20xFH\r\nYCLHTEk6cKRXuewM3HSchsYz3MSAbHqSFt2lMGqIMpwDkmpQY5vA04t7PPnN\r\nPY/p8+aewM76DNwKdDqmPzjAS77ml7k95woR1sCczOzit4LglqqJ6Voz63LB\r\nNP6MlRirqG/ccomA3VjooYc/kSZtOQ29fQPeA5Lkow0/ZTk6I31ozelPCjpj\r\nAZJ0XqSN14/Fm3EYy8lRyyRx+GuqlvQQzbSgkAfnoaGTigDDwGqhHBC5PR6C\r\n1lPdNAPhEeOqQgQUoBmm1NU1+lq16051SeIU1GRAELEUw/SRGx+mZ9datPM9\r\n6RpTTY04xHk1XlppjHVcygWZPzVdSA391xEaQ/pjqk7jV62g7cvd3AmUh10d\r\nGzquURuzCu4R4r5xrUlf7pgteKoZMb8/KYm19QzdfEagDGcdnAvMzrDObhHc\r\npqbuc8C9CVIr7qIFVdV9PfTgdWF7UvHaWhQdhUod5OhDcB0ju87Deoaq+gI6\r\nyiV3tdR//NSIyqQSmDQ1RqHmdRSBjCeaZr8=\r\n=GkZx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.3.5": {"name": "eslint-plugin-react-refresh", "version": "0.3.5", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "0121e3f05f940250d3544bfaeff52e1c6adf4117", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.3.5.tgz", "fileCount": 4, "integrity": "sha512-61qNIsc7fo9Pp/mju0J83kzvLm0Bsayu7OQSLEoJxLDCBjIIyb87bkzufoOvdDxLkSlMfkF7UxomC4+eztUBSA==", "signatures": [{"sig": "MEUCIDDf651yronDWrHG659nNpb7Vtpp0hQsT/xPo0J7d7jZAiEAj9EJBnA/PXTI5w+mCqgwbHqPI8fN4kMzN1+5JUPO5HE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSjaNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8JQ//dIVVGv0d7Uevg00MpdDSE2WgND6CXLKCwpeSooZh7watxUUa\r\nggpcs6D+QJ3Gl63KE9OM164LnHXvw2cuqSP56MM4QqFuNd72b+2qQtZ7o5AY\r\nFT5pg2JYZjRNVfD1T5BYnTf/5nuKMopod0H6PcXEPBSG8ccymzejC5a1AaGV\r\nRfM31oxBpHf4L0iPJRdwODN0us6rgmxIE+RHX2FCxnNTQY8IsyVU710mVeRr\r\nb88eyD1ha785dEzVxchCoSFodLgYttNORY0twdcvf67YpFz/qEAy1ppOkDBc\r\nIizEAorBMw8Fm1i/2RETbHfkbYoNW7OnntxzK3Y9c5aD0wJJVIaTRx9Fd4ZH\r\ndEw3kw6AY7mosluuys+M3vHk2zQhm+5T509SiPEyKFEbRIXPm1KIVbCgGjba\r\nHLlJ+9M6WHx9DLAQCkz9p8hMRXJxfbnfMH6el6gVfvPzsENZxRRyXO/qiU2w\r\nP5lvHwl09aDbN3urKrZh9z1thegPSwB7TBQrW685xhdH69BoWny/4NdCoxNT\r\nMlp+DfuRXkqfnUyLAxfFXWiP/TV4nvgpu+OKwMgjPybDr6lqArI/+aG4KrpF\r\nl+X7UpOiQLgC2y9Mnr88bVzLcP6xwNxC2LGpVXiiSnP3VaWcNxZbgTTT32p+\r\n0MQfZ8OmeJmE1dcCQqmdd2l6VKRie+uJolI=\r\n=SPz2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.0": {"name": "eslint-plugin-react-refresh", "version": "0.4.0", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "735b4201ed71cd31f0f98d17843ec0ceef0adc43", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.0.tgz", "fileCount": 4, "integrity": "sha512-mV5V77NNutkhOgg2TCVvSx8BO2jJ46Oq7j9/xNInYxqrO2EWsLRgdYOPcIAwaZwJhfhjdAtfACLdyCA6ja8sEg==", "signatures": [{"sig": "MEYCIQDWasXz/yZemPLms4ou25g7Qi1ZanMVwCq1DGExoQr2LAIhALBRnIcljvwLQrjhbjIjRXHXpbflFMFU574acMgylgvP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11161, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTmYLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbJBAAjpqHpDcpihB/iS6v6V1huuoFOc+Uht6x2PmZ0lgeUogRrSj4\r\nV2lZoQVXH9oBYbAyM3zu/fkY7iSOXl0mBB2HDzsK/aJN6QToMdi0Gu6A683g\r\nyjHHLgEmES0clAGBS9XpEEsxCokFEx+Vi9tynNs1nwhJc57efArbCGWamt7F\r\nrmRQUySKzR/oTeMd4Ov0s7gYOJgEHlN4mCN83SYl3bWfHUjU6idhZSRqxvMx\r\nvvl6krxTYxcwMvFbfP9ToJRS+aE/LuRrbU8XtXnJ49ZPfj4cX1EkFAWj0ZTC\r\nNZ/mjktgARIev2AjflYPKGKCw0xd0l3LIXfmXSQG/hmICC6uBsHg88wQrtyS\r\n98Y/HOr+1x+O9Ay1ciw3eoHcMVDrAGUDsfLNyzY09EAGTejS85b5ucfvnx5c\r\nwXAnLLPYIKnCvaSZhh4fjVcMNI7auOCTRilj9ToFYIenA7ah3H4uhXN1+d7J\r\n9kYq0svgRTpmNBAQrMrqrFLEaseg7NUQ9xNH02lF/mmRWU7PJ4G4jb10j6on\r\nMChSV+FbW5RSzAnR7voDoPwLeCAOyK/USxd0/HPWtMYoGh38W2cBuynlwKRq\r\no27EQCh6GH/MSnt7H4O+NBzwHVDZc8+YexsxGKVW5mnhhe4ysk1TJEoRWuOx\r\nSkftL1AlfDbReQX2KYPcIrb/CgrZi62BWdI=\r\n=w9JM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.1": {"name": "eslint-plugin-react-refresh", "version": "0.4.1", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "e61a1ee4e06180f07ab753fa6fd173b4d8df95ec", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.1.tgz", "fileCount": 4, "integrity": "sha512-QgrvtRJkmV+m4w953LS146+6RwEe5waouubFVNLBfOjXJf6MLczjymO8fOcKj9jMS8aKkTCMJqiPu2WEeFI99A==", "signatures": [{"sig": "MEUCIHn4Ox0r31Kbs08eopYTrnsu9DQfZmfQa7TNd8LSz/p8AiEA4inYbV5yPYiqz9Ddm8/XPuEUzK+RXhkeHnNtd/I0ZAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11493, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUZLLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuTxAAkXjWVzmyJL1tsxSPeHkbo/oMDRIVRzaFqFihMsr8DWHNKna0\r\nlRKaQxBfNGCRuMK3fTdk3e96z21uJpDKOmcTQ3szXpr0AXlKW4KYyvRaFw3g\r\nsWyuGZN5REK076IjFuH6KGIKzH/ZHQ4KsRWpG8GSf+0WyWRRpYxRdCdEoNxR\r\nrWZ1AQw6gFAMO0AM615R1vOYj4U6bHN7Rj1a2UFiSHoZychcsYp1noj1C8Y+\r\naEaxfA0THln4NOrGiwy6ETZXf7YnPvpzogobHxMKatUlGLPJMmgo6sz7KVqv\r\nl1t/pAkyJIaEdBepgS+VgyrBgaOvXmwwzRf/3N/EQgcTOZWskw0farqLx/NN\r\nq8IICSrzKgTOEV1dWFp1v1FsCDABxhSTCSoqsOlb9ljlWqzlEECOqJwXP+uN\r\nKxRK9OY0wWXWv5aVOtFaiDtChIzrKtxVhkkcWX3Ls+ClIkBmimQ/cDVYKa5/\r\nzmgoUKXGvR6m5Cr81LzfbKvzb9FsOZO+AFzCzbFgkU1Nn7bK+jV+oXMzY3ai\r\nn9qCXiZCeCfYfqhNEdwlsOz4tRqlvbOw3A9P63g1iyN6zUcjHqRUJ7+v19ss\r\nWBoLD3FnzajhjTnNQ/UR2QCpp/fzqukvDTyAb0BaKOIGGL+nDS6FtMfvb2aB\r\nAar6Pdmx1F3hd5aVJ4PqkOfcAYP7FZBQ2Uc=\r\n=vao3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.4.2": {"name": "eslint-plugin-react-refresh", "version": "0.4.2", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "0c502828db7341ab9030195c352b09bd23a74d99", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.2.tgz", "fileCount": 4, "integrity": "sha512-AjT+7woNvaIiFoFqiD7nsd/M1MoW1h+8SAdfCpQ/1LSFkIH9DNAawBsTHj/1sWjijkVXRfF/rOiXFm+4xZrZ5Q==", "signatures": [{"sig": "MEQCIF/LAQsv0L2CJU+8x5hGUEAK0j/X+DtNB3mTUl3pURZWAiBtm4MxYGlXQZB+xn+V6ixSELJ/2tMLQhwA1SIchT8Ung==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11499}}, "0.4.3": {"name": "eslint-plugin-react-refresh", "version": "0.4.3", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "59dae8c00a119f06ea16b1d3e6891df3775947c7", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.3.tgz", "fileCount": 4, "integrity": "sha512-Hh0wv8bUNY877+sI0BlCUlsS0TYYQqvzEwJsJJPM2WF4RnTStSnSR3zdJYa2nPOJgg3UghXi54lVyMSmpCalzA==", "signatures": [{"sig": "MEQCIFVGt/CYQ6mEFyGnnrEttwvUCRuyYWv3m6AeWvDKi+yDAiA3+4omTKGirV1PFzfN0PIiI3ba3MAyMfWt4hnqQYlRXw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11604}}, "0.4.4": {"name": "eslint-plugin-react-refresh", "version": "0.4.4", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "b74ed2a06ee998e4126cdf92f638a66f2cc82ecc", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.4.tgz", "fileCount": 4, "integrity": "sha512-eD83+65e8YPVg6603Om2iCIwcQJf/y7++MWm4tACtEswFLYMwxwVWAfwN+e19f5Ad/FOyyNg9Dfi5lXhH3Y3rA==", "signatures": [{"sig": "MEUCIQDP/PFrfNzX/blJSO2F4VYDgCt5lXXJ/EiSudvCqR54awIgZQR1iJW9Q+Cdqna7Pi8DQPdQJyTG2kJyFdf1buiTlHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 448370}}, "0.4.5": {"name": "eslint-plugin-react-refresh", "version": "0.4.5", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "6b9b307bad3feba2244ef64a1a15485ac70a2d0f", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.5.tgz", "fileCount": 4, "integrity": "sha512-D53FYKJa+fDmZMtriODxvhwrO+IOqrxoEo21gMA0sjHdU6dPVH4OhyFip9ypl8HOF5RV5KdTo+rBQLvnY2cO8w==", "signatures": [{"sig": "MEYCIQCykKfWw9VmmtJU4tLvzgyx5Ubtp58+Z7KGW+W1X8K03QIhAMqFYm2kGWuqGEynWaJY8RgTyKoRVOvpzfVFR7naxtxg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13547}}, "0.4.6": {"name": "eslint-plugin-react-refresh", "version": "0.4.6", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "e8e8accab681861baed00c5c12da70267db0936f", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.6.tgz", "fileCount": 4, "integrity": "sha512-NjGXdm7zgcKRkKMua34qVO9doI7VOxZ6ancSvBELJSSoX97jyndXcSoa8XBh69JoB31dNz3EEzlMcizZl7LaMA==", "signatures": [{"sig": "MEUCIGMDinWOJw486+b5dQHLObY6TBKtWYy20MO2bOwgQLERAiEAzjQaW+4PS1ZGbIbO4j0lWdX9K6rRywv8ZycHPRYTg8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13576}}, "0.4.7": {"name": "eslint-plugin-react-refresh", "version": "0.4.7", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "1f597f9093b254f10ee0961c139a749acb19af7d", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.7.tgz", "fileCount": 4, "integrity": "sha512-yrj+KInFmwuQS2UQcg1SF83ha1tuHC1jMQbRNyuWtlEzzKRDgAl7L4Yp4NlDUZTZNlWvHEzOtJhMi40R7JxcSw==", "signatures": [{"sig": "MEYCIQDXbm5f/FfV/f6jKz5oFnp2IAFwBgUMYceYAd9zxHTqzQIhAOiMDAhOSYDLtTrk74xYIs8XBS/6KJy4e1c3JJfo9FWG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13666}}, "0.4.8": {"name": "eslint-plugin-react-refresh", "version": "0.4.8", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "3b1db9188844101213ca637f181e84a016e36732", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.8.tgz", "fileCount": 4, "integrity": "sha512-MIKAclwaDFIiYtVBLzDdm16E+Ty4GwhB6wZlCAG1R3Ur+F9Qbo6PRxpA5DK7XtDgm+WlCoAY2WxAwqhmIDHg6Q==", "signatures": [{"sig": "MEUCIQCcDXSqUiRRxx21DqtHnTpWCSDdsx837yDDTxMHiQ1QogIgDck4YrSAlQa87cgzEArosei6kXuctKm9vP2etxCb3Ho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13771}}, "0.4.9": {"name": "eslint-plugin-react-refresh", "version": "0.4.9", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "bf870372b353b12e1e6fb7fc41b282d9cbc8d93d", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.9.tgz", "fileCount": 4, "integrity": "sha512-QK49YrBAo5CLNLseZ7sZgvgTy21E6NEw22eZqc4teZfH8pxV3yXc9XXOYfUI6JNpw7mfHNkAeWtBxrTyykB6HA==", "signatures": [{"sig": "MEUCIGJzKBSSUnrmrEIPf1qRKVqcScbQPx41FSFtLprGNG4mAiEAr6T05LW05LdwQ+gXEY+w3LTYVzu8uBpeyASsfbNKmHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14130}}, "0.4.10": {"name": "eslint-plugin-react-refresh", "version": "0.4.10", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "1fb514897ee3bdc37ac34446fa5f02b1ce9c7d3f", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.10.tgz", "fileCount": 4, "integrity": "sha512-I39s6G9We7ZxbCRxTTM5XX4KJV2cfWhFbHF4kTuL0ygdEVdQXtCNGqUQ43sBOCbTC/N6dEZXoQKFHr8gp1VHrQ==", "signatures": [{"sig": "MEYCIQDuLsrR+Z35LeCjwwi4wXz7JFLkHMi7NzqeaXbedxppJwIhAMW05y6/3MrXK9/RLPSwmY5TbPy4Sfp8ehT5iahvqlFT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14459}}, "0.4.11": {"name": "eslint-plugin-react-refresh", "version": "0.4.11", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "e450761a2bdb260aa10cfb73f846209a737827cb", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.11.tgz", "fileCount": 4, "integrity": "sha512-wrAKxMbVr8qhXTtIKfXqAn5SAtRZt0aXxe5P23Fh4pUAdC6XEsybGLB8P0PI4j1yYqOgUEUlzKAGDfo7rJOjcw==", "signatures": [{"sig": "MEQCIH8zScfwSvJNAObxw23619Jl0C0l7/XBzt9cwkl0alFNAiBLPK13fYaCzq2XMNmIsVz0x2sC2u5h58aSlJHwQrmkKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14527}}, "0.4.12": {"name": "eslint-plugin-react-refresh", "version": "0.4.12", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "73d61c7fcbe3f7280edb6579380b4350d2f547ed", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.12.tgz", "fileCount": 4, "integrity": "sha512-9neVjoGv20FwYtCP6CB1dzR1vr57ZDNOXst21wd2xJ/cTlM2xLq0GWVlSNTdMn/4BtP6cHYBMCSp1wFBJ9jBsg==", "signatures": [{"sig": "MEYCIQCcSVP00L/fvWHIXOkZcuc0QsvVliRhy+edhIh/JxPMCQIhAJKRbj7895tVC2oG2ECitfJKVaf1XRBTZ+hj7/Qa2qut", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14729}}, "0.4.13": {"name": "eslint-plugin-react-refresh", "version": "0.4.13", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "ed7330da09b6192e6fa9b1b217ad979afbc898bf", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.13.tgz", "fileCount": 4, "integrity": "sha512-f1EppwrpJRWmqDTyvAyomFVDYRtrS7iTEqv3nokETnMiMzs2SSTmKRTACce4O2p4jYyowiSMvpdwC/RLcMFhuQ==", "signatures": [{"sig": "MEYCIQCBL7Pz4KSJwcfHkaewYBt4sBspPBOMFlhwUJFXNUKsFAIhAKOFUDkrhYGluYYv0UldU03Zn+RYB7LeAbjnLn+oPIm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14963}}, "0.4.14": {"name": "eslint-plugin-react-refresh", "version": "0.4.14", "peerDependencies": {"eslint": ">=7"}, "dist": {"shasum": "e3c611ead69bbf7436d01295c853d4abb8c59f68", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.14.tgz", "fileCount": 4, "integrity": "sha512-aXvzCTK7ZBv1e7fahFuR3Z/fyQQSIQ711yPgYRj+Oj64tyTgO4iQIDmYXDBqvSWQ/FA4OSCsXOStlF+noU0/NA==", "signatures": [{"sig": "MEUCIQD8Yf1Gttc7rSrRkkSlIiMmixSzT89QUwH2mmxnyxifPwIgXzR6RP3iwWTgg9hhnkSlL5t7S2z7qCJaHcRehbUX1ho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15683}}, "0.4.15": {"name": "eslint-plugin-react-refresh", "version": "0.4.15", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"shasum": "fbb7e08f6cfe19b197ef36acf98b8cbe5ca82283", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.15.tgz", "fileCount": 5, "integrity": "sha512-poU5qfmwLS5WO69drZnB9J1vXv+NQkE0p+oIY4B85Z9IuvpaIdHa+9IE/sFrN79QW49QcHQIP6c7NHpDMQ9TvA==", "signatures": [{"sig": "MEUCIQDKgCPxuzL4vM2+U/Dn2iuK4S00Ci+yCQVQ+GVCWfek0wIgdnZ4Fud721FDhtTtfNvZ4ztUX9PKPIBtrWAWcy8avWs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18029}}, "0.4.16": {"name": "eslint-plugin-react-refresh", "version": "0.4.16", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"shasum": "149dbc9279bd16942409f1c1d2f0dce3299430ef", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.16.tgz", "fileCount": 5, "integrity": "sha512-slterMlxAhov/DZO8NScf6mEeMBBXodFUolijDvrtTxyezyLoTQaa73FyYus/VbTdftd8wBgBxPMRk3poleXNQ==", "signatures": [{"sig": "MEUCICih25P9wb40JAvqW7uAuJGpl1qUhaOdDQKgT0o+pWM/AiEAn1bosQP9JChO4o0e61d4U/Py8MjgK8WPnYfw4dA5/N8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18161}}, "0.4.17": {"name": "eslint-plugin-react-refresh", "version": "0.4.17", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"shasum": "5e4dd9ba40210ac0dd05a8ebca7b59670f084636", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.17.tgz", "fileCount": 5, "integrity": "sha512-CwInESUBZM8tV3SiCgg0pNOqir9judwt8eQ+WJRkS4xTy0zTDrtiSigRBbBOe6TOX8dvfKLdKrA3BZjXsdKoIg==", "signatures": [{"sig": "MEQCIELON0IwE5N/12GKx1O64CwVXuSIx2rZftb59QBvlgkwAiBRBKU4NXOnCzkRAuiNU4Zvzyy7GZZASTxgQeA9qrhS5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18138}}, "0.4.18": {"name": "eslint-plugin-react-refresh", "version": "0.4.18", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"shasum": "d2ae6dc8d48c87f7722f5304385b0cd8b3a32a54", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.18.tgz", "fileCount": 5, "integrity": "sha512-IRGEoFn3OKalm3hjfolEWGqoF/jPqeEYFp+C8B0WMzwGwBMvlRDQd06kghDhF0C61uJ6WfSDhEZE/sAQjduKgw==", "signatures": [{"sig": "MEYCIQCHudY3M7C0sUYr2RSY7HHTOvla0ADpJS42GoKDnr4oCQIhAPzHrrxX1rnQKlF8LL2uZNFSLnY7qdQ2HFUmroMJl5zI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18125}}, "0.4.19": {"name": "eslint-plugin-react-refresh", "version": "0.4.19", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"shasum": "f15020c0caa58e33fc4efda27d328281ca74e53d", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.19.tgz", "fileCount": 5, "integrity": "sha512-eyy8pcr/YxSYjBoqIFSrlbn9i/xvxUFa8CjzAYo9cFjgGXqq1hyjihcpZvxRLalpaWmueWR81xn7vuKmAFijDQ==", "signatures": [{"sig": "MEUCIE+HhKyFpjMSPVFYWiNCFrkEPd7OioB6IHHD+fJTyyhOAiEAwTowmZ9KMboYlByseTe2BBTVKtE/zRjT8O/VVirc6N8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 18196}}, "0.4.20": {"name": "eslint-plugin-react-refresh", "version": "0.4.20", "peerDependencies": {"eslint": ">=8.40"}, "dist": {"integrity": "sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA==", "shasum": "3bbfb5c8637e28d19ce3443686445e502ecd18ba", "tarball": "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz", "fileCount": 5, "unpackedSize": 18581, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIBWPHOE7UqI/UROC6OGNgNtDn/VkgTnZKFwZ3xIUTyDMAiEA2uXcIrTfzyiRaL6U0dEwn43tSxHwla9CXbGDMp34tUE="}]}}}, "modified": "2025-04-21T22:25:19.712Z", "cachedAt": 1747660587342}