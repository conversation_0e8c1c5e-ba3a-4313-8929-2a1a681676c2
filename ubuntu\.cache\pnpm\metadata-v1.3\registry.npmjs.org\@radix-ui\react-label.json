{"name": "@radix-ui/react-label", "dist-tags": {"next": "2.1.7-rc.1746560904918", "latest": "2.1.6"}, "versions": {"0.0.1": {"name": "@radix-ui/react-label", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e857fa258768d560e6aae42d5437519ec23768f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-q1JC+ULda9PVabrlJim5yM5EA6L8whCQWuDOLGLJpqkAHMRvcFpmCHJCmKH92JIt7g5hX4mCGTNCtTdFDZNvnA==", "signatures": [{"sig": "MEQCIDk5d04xNSj5OJI3se9QHtqFSePtSYMq4Bseg7ylYsFLAiBOnvb8WWxJ0af0R0NrGRb+gS9DqTo8dXuYReUAsxzVYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbNCRA9TVsSAnZWagAArtYP/18aO/yk8OBo69yAI23l\nqg8/10eqVfkuVIP7hJ22ijqo1NsVfrcf751D2GnQPMTkJaL85muRKab9Krqc\nXIIuPbseAqOe7c9/YllF53LAEhzgDJw709pJ3YVobwerwnuSmUjweKprcqln\nDVmmUkze3bk961H44WB6dt5GZBDMaamfAfh3ew9wuiUFo9Ujx6wyi3hDH5et\nYU7Oli8qYa/C9Em0EV8Mt3g/rwV1hYfQoMHm8XaToz83anr5KrwsWaIL7RJZ\naARQKBsIW4ypRVufqYl8Ezok0rKU8otDowYLZoRJtN/xb+fzHZ1m2neJno/s\n3bySRzBCk744PLMrET8rL7uXpsnTlVmexjA1EZ5bG7ShU4lfHwAG/brMr2Mx\npPfnHPUYeI5rFVVGyk1zbqiLKeiipbqYJA127D+I7kQXn3ZzIJ/kdotZEGQW\nrRxpvD/Qp2xkFwgBWeAYVKRgAghTS41ZdjyjwCTLN8IssUHuzLi6hnQFZVyf\nOZ9D9Hd1CHjA6nFwAMj8g+RVf0XSRxzE61fE90ync9S7uYhQ1eaEiZ/Ty13t\nKWkE9A9NynkQEAe1iTBH3inU9aPWKxygypUovekmPaw3TbKZjoeoAHa/zGsB\ndktSHhNn4OXKttBJkwgwl92FLxDWsYEAnxl72rW+CalDO2kZ67A6A+dyrX1/\nm+3I\r\n=74BG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-label", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d4bd9c23132f4b1f40808391f82cd626549c40c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-YEO5JBBHLyJUyL4wZhEVV0/DL4JvcVqp0FiTpEVN/ho6R9MF+BtFEiYiSc0WahwUHBcyjq7/HtXq0reR3PttDQ==", "signatures": [{"sig": "MEQCIFZEGLjlfEvz1lUHvM2cEo+msCbU/CHHO3ki9HUuB3qnAiAYhPj3+hjtK9MK29KummBHoNTv7/7vymS8bncZGkbQGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvICRA9TVsSAnZWagAAzLIP/i9870iI1ZrnAoV0Ev7e\nNqhTP3cnuwP1Z4IbLCfxDOOAuQgy1qX8t2K1wOPiN/Rz2h6a9wnlid/eF9b+\n/9BCVg8hkhKdaUh/xhdRzdL5TyhlFPxmxxlaJwleMYmAJvfuym6ZxMXzmZwY\nkUPakZRb9/riP1Rfdwj0DbN7O3amX7HvflzLxel0LUxA/BNnTWGaFyZ1EdKy\nAxXOSDL19DCQGhIuXpnQF+b9PP7gCweEKBozVoqXS1+zonZw3osHA/16pG/j\nbAPau9rFYB8EC2Y2f6rvTp2WwkgSA3JCpRV1cmkD8ELrIiVH9IBsSgvDWwUP\ntqEg3sCNo7icj2oKQD29bAyKiLnl+rl3iXclG+VpP1EvMsoSfXLu4O5OLEYo\nbPT8XJtcnN1LkL5YbwOGOUbgh9XCklUit/vFuPa4LEN3lwVMkLHUPA+STSQ5\nY5B/AxDCLBXgHI1Fmdmyd3mU/KGhRFqrRgPjasMi84phdB78c1HukkUZyZB1\nPMALtbF52n9HTo8Fr650GONyqIYSl5Wde9yf7i/FlhFQTyBxD+tan6V8kCcg\nLSq6E/WxepiN4cGdO/2drU8X5K2NN00YBVXDM4I5VCKlaQYps06YUgrhcQd7\nAjvKKm+D3VNwLF1fY9jZ3ZZENQAOimfoVlxfy0jG9VpqD7YngxpCoDA4zv4v\n09Jx\r\n=rXPi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-label", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "df9a019e75e99d4e723b52aaffb5af5df8ab7c8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-ZzWc2cNcDaJp6lo2/WSwHrQstNeGNFF6c60uLmnzCPzey+yeVBxkKxmKOkyRkBbxQVyntkFn3YDzTx73MKY+AA==", "signatures": [{"sig": "MEYCIQDY76Udu/yqZAs0cC3CTxHEca153PWQ7qRPtVNhczsMagIhANNLNGig0SyxfSK6Ly1zvi1G4rzLTzAQhnkt91KUlWfY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22316, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtLCRA9TVsSAnZWagAA2DEP/26ZCONhELucyGwG3qqt\n9jDavQKoIYYUhQah+q3ZPTsZeefELI7m+UE7pxI2qADa2CRtGtf9ImvLdHZ9\n+leKMn7TXe9IlDeM7fBAr2RB25DkY/TJMoBXwleRYKGFWiGz3HqhyGDYIFuU\nJI5Af3PHNyM2zggb/yLhWfKAaPhgl9DgKAx+tkHIUXw7K95sIU4RT0u2Ui0P\n2np8jepn7D1c4SdwG4BOGFlMiRqw/Tsqao/7DQ/jZMLmuTvVaTEz1GLt2mDI\nBgnISKAVwiGKJW0fw+TmQ8vASNXnj8EMWLaS0iTC8MJ2XEn9QUcdByYfSzVh\noPnwcpRTKYrXpDHGkSxNlnzs2XxOdFcpvUl3ff2gC9zry+8GKrIXzKtFddNY\nXWnzqSlGDReMjHaIuVLqytA42SlZ695psfO/M892Djz5bDKnvRmFfZNUEHHz\nVQrJ0ypA1yN/q9+FzbaZ0AQklbzrTyIUYdGdkYgK1a5iW22JZcB4hC7hpwUN\nEw1sR5M0GIrKrNTvexlhYQxiCESqgWgb1KU8BMYe8g+u28BZimt/xatOjMWM\nkvNbE8irg490Kc0xLJQAxhCthQJ1lqpT6VGUZJ2eGZSiTiyve4HClN9E6pFG\nIiK4aAmSF+/WxmjbXrrm7YLn0yWHoxSfY5CX/DSfhbSXzK2Rrtng8VhtC1KK\nNVAs\r\n=zLno\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-label", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70af1710083bd170b1838f70bbe0069d9e7f85ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-aaLpMdX5Nnlr9duJ5cowbCT/DU2SH4flznhH0awRy31h057ZCvbyRwZ1hFt+WWf4/Zn4cOrIstRe537OykRNmw==", "signatures": [{"sig": "MEUCICH7PYOXZvSOUjO8f3+YsPgEqqX1LaTzr2RrSZFN95MhAiEAxy9bRZIX38TWZbvszxlHz8gtkvTQ7K9HYa3GyeZu8D4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAECRA9TVsSAnZWagAA4FEP/RqTWhIuEQ99H1Q9DRfw\ny272dWYQWfblDB43tYS7HNEJzAHEV4Wrj36ALlUse6SLMAW9N7w7gxTobvAs\ncnlYqCp2yuyzw6K47SgilwYLpTERH5xtKpS9AL1LXNtBIA5jLCXr2hvIWXI7\ndPuteDLGMhCAJL7lDszKLWeBzr2YF+t+z+8fgzMWoM7D45Rc8N8dDbEGpdYn\nBuP1wB156fijYxDwe75pEZfuRfrTXSzAyJtY+TlkbNWKCrg+1R+WsMEJyOoD\n3g/0ExUcapCqiRo48T3fwcvc3GdIN1JA8sijnVmfYed4BHqcG0eJK9xzL+we\ngar8QSNQkjGCNWXGcBaS6NgqNWIakMvUMXCJiZA33SYK+TmzUJg9YX3TOS8g\n0O8pkvl4VUARI0EQdMXlb4RWXSeTAtYAlU/w8/UgJPa1lKCljdQqBRNWiNtg\ntaKB0lKINNZSdpiJR/QWJVNEfK+02QiUZwloJRp6ROWeOv2P3CgY9zMq5LsZ\nkkBtcCYb0Kr+HAijNiFdb8MV8LLPfy0km7E4lQQS3cXSATn3HPudZ4ND5cSH\nM0lvmM753POCRnFbjCkMhk8RXVmkoCRYdSxm2uqCBH1bwPfZns57dS1Rw1zZ\nAEx4GU08a0SvD12PGoBhCtdtbIATCZcvknDqiJGbxYrsQ9CHOkoJYNCfp1qS\nLUaW\r\n=nXzB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-label", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "68d05c31ed5669190e09967026b8660a2f4cf736", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-zc249n6Ak3GMxvEwljgJ0HVMOUKV3J4eaN84YdijotTeSX04H7b/e2f54oFJuiboaPpvTu32AiwciRKt00reQg==", "signatures": [{"sig": "MEUCICoxgh+EXunu+gVyUmiUdViknX60goQDb6ZrKdP02eO4AiEAs/GdFjBu24mSVkkScF1zozDcWxASa2yhqCdP5Ylo1IM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VyCRA9TVsSAnZWagAArjgP/3yYSindNxZPSivMbkzA\n8U2xGajudbzaGbIRCKX4Lg4988r4UmWGaW5VUvP0SqE4e9LXWTMLE9+GWpSs\niO56P9C4yQZEof0cxvAutqp9gf3+MLPRrj3rPs/MFfHVVh/DOm8BLz7ERKai\n4gULDCxKlHr+utxgqEQDl0XhPBA3R7Vo320XZ5p8/HCKVtTWH8DKawcgLclC\nGRiBozJkkBPmErJ9JNuilH3CNE3anqDyCbrHkqW/ZFNf4Qn4ywaUt0rL6oWm\n3P2+8KHXUJ+jQFXW2E3kHy0VYzneqABnWJnRLObu0AcBo9s42uKJTNXKlslx\n0WXqYA5Ud+utqYiNcZXx8SAzmFXM182JRgGmruuwVsOvjkd92eQes6VVUaiJ\nLwE32tEJ8ekyFBsLn2byR45rHQLsXTAa0eylqv9gLmfvefijzIpsbIjrvPFE\nZywsvutv7RvQaeVdtxVirpYauV70IBIgbbLRyWlJ26Aao56h5JkdeRfTwZkb\nGFftyIBKyZuYouTf6BfqsY0N5SenxK5b7Z/g79GKRMrHEeJOaNPkpWcr8qYL\nAv2U59/LYZPy0m+c7Mc9h354aY+4SyV5h0o4a42owrtwVZPt2QN9vx/psB03\nw9FuzKKxAstUUmGSVgJGsU3qS1+6idQNie53EYqyleAiKFaT5JnO4dfTCBZh\nj6VE\r\n=0e3R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-label", "version": "0.0.6", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bc9a3c9afbb9b21a478db4cfe5971d03e8dac0ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-Fqd3ABPbU+uVIsLutcKgbaf0SMyIUlCmHTCHKYm946+dzuD7Apd4OodZTBQsiuSy/RGK8iD2k1YIXJleWUjlsA==", "signatures": [{"sig": "MEQCIBm1eRB0kwGouw5yweub2KGOboakZm8dkAAnn/WY/iMkAiAOKeuq3R3zYtebHZDKc8zunBZ4063s5HZaW6oIlQvuHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VMCRA9TVsSAnZWagAAHpgP+wRDzOjc0uuYqKpPz/pj\nSZrM8k1IcstY3OOh873mK0j/Mcsb3mbsxFj+C8lZNOLXMwKHuAeW/DYRaxXf\n/2HVqet5cx4dbsAAuWyhP2XLhskuZJtZlEdMtbkcsO1GI2cR0tO1fmwxDpCR\nAie4iAOOh6kMhP925W5X5jn8gpuAwmdpAAeI1hMifRIyUkEzezCrWcckcxol\nji3/IoMMUzRq2yptsSn0+PqoZExX8tx2P/KSb4JlwylnpPkPopcpnA/wVKrA\ntYGjNLBGlquex8jNDvqbJeAd1C/nZUGZlhyYb4G2ebuDEqi3k5nRCKpBzUDR\nrJ6yrA0EAmtm+3qYDcLt7G2En7Gw7GMqspOYaSG7eIj7NBbRXtaWI9LGgH/Z\n+n0NIf76AjtRBgo6aDImH0dTeJ2CGdnHnDy5hjax12tdLkFkxltztrxirSB/\nTIVOpba3tNeQTnhpbIZ0wpn34cr6EzCy1u267NQtkhFnRxMzkqtUk1AYzaHV\nYc8QPF1AbE99Nar9DMK1Daoayty7dK5Dm1iLe/fihTrQ8GCYW4gA5tDUQLWv\nEhVgZzEKnQqjaTwiCvKY5VDZ9Bgpr9DiOX3xLADduY8MUVl0LfJjKHCjWbDB\n+LVjYAqVgp2AHck+QxMoJv1zh/JKVBIrPnOE3ohYCR6fXsDrdupLssBM19GC\nJCGr\r\n=+Oqp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-label", "version": "0.0.7", "dependencies": {"@radix-ui/react-id": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4338d2009e74a50dae4380ee3b987cdaeb232b30", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-AO3rdC85H5IJIg06mYeISPZeFcuggjTh38fL3G3xdZhNPhj+aD8V58DsVYtDkUzslstjEh+SluRqUE3MLSJnAQ==", "signatures": [{"sig": "MEUCIQD5Dnp/Dp+Wmzxx/Ub5j3/dAj2j3McFeBzlimtbnIUqCAIgVYHwERGJyANcFFG+CZLrcw54K93df6XUsdTrvWBjSvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21618, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmO2CRA9TVsSAnZWagAAnpUQAKAgLUto3rAJ5t3bM2Xo\n+sElzfhm+GeXcqtDPOQ5sxtH3MbdQ+BbUHLsp9NpsKH88eSTuff7hGAmA/QE\n2ihqM0rOqiCkk66OHT1pmswEoXfUhicYXB7LwXOp93bVnoysKi9DSunHUesC\n9Iq7ZVYYLHDF+GQcFEoUs2xpyeBnNaJtCMHKsYMzR529FR48VU9uuZTbxoLl\n/+Zj2KFtME6SDbvHUBoR4NBeIO5hTNJBXHJQj6J/n4yY2u0cqBp0YBpw/yTP\nQ4SI/D9/TnufMKTVQxy6zJX04jBjebH6F0ObOdDrC33UKdbRlODwJ/kR/Nxm\ntmhQ0UwPv7qwGjAxaaYEdLbsP88T8+50nb7Mb06JK5iPFWfqfTbwFnUVE137\n9LQIy0vKRgWfUYelB/u45Nl59kQyZjhEea+Kd9ieVV59w8JaDT/1tkR6Bp/B\nGITFEb33d7+ptpa3iwSDhaXV7JLbuW4RUEa8J8RRY1CmRAyUUfqgJ2UluzeI\nIuyPmldhtf27OgZD6Y+8GKv+9OVt90swRlB94om5YAdZftXYzfcai40xHR9A\ni6advA8vd8ecQEtsekMY4LHioByZUo9QzTP4IPcF+x+YL4+J2G5O3y9MZeA2\n0XuC5xSmLhM3bGONCKJmvkgpmgGqCcrncg2fpEgpolXdhFrwhIvlwD+y6j5C\n9Ejv\r\n=A+WE\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-label", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "05c9f6bbbdc9e1b464896ab33d823a71f987f7f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-Zs5XurQa4wrTw61/cLQ2vVOTPXSWrJ27qQkv+KqoC6L30HIIZldAqCezCYoz6EJhR+z9xrQVH5ekqgoOLTgkCw==", "signatures": [{"sig": "MEYCIQCmMJvJssskWL8HSu7Lg1s688PSxO7plPMTAB6T8OWyXgIhAIPdbZWn+CyW9EfHRf1YrhRKr9r08AdrNYXJQ41SkQga", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20724, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0guCRA9TVsSAnZWagAAIEMQAJjv68n+Mx3DHDEsk6/3\nQuChcW6Ft+uwRLLezFAagFMe9uHuH/UY7249NYF5pjcFHw4JKvaqK6DsqYTj\nJKqWtBppFN9H1VG7EKlyFoALlGzp3s+ZHaD21LzeU+1b9IMqSTbR7ynl43Vm\nCRpIgW9dsjPatW1wSRFRZw0HSpc53oravzeo00zF1hz6xuf1lGJMAYwYnZwl\nIAEoKDqyLHpIS2xN1dVJONzwDqE/296HLc+QshlaYV1K+fivHoxH3V9uUyH1\nP0JOukWyZd72lChH872AbaAl2AiI30SYPzztjd7ASFRP4HnzEcUbcTQBCdi5\n3SrUQRfZyoneIw2s5XzEaM4rVTUA/IGp+ztXTQOJ79tsFb1h9oxFAI3OEi/Z\nK/W57aHXsiw87RZiElEeR3m+MaVS2aRN68y2Ye0brXzqKS1J6TNjYtdOaxZA\nCEX8j7nINadnEHQWGFjltNYJ7oA1nFS36kuwZsHRMLob40mWBSfqrj6yyrdT\nrcsCo+LctL2MCtUvGnQsBLrSi77tqzVEHx/t6e8DgWCDri9BZaq1jLQrQZFQ\n6zY2LhdGL3fz2heEjrCkYjFIt5GA4X/x7G25iBH5s2qc0qVA9yzA8vWFUeIi\ntBYgFfEZpb47exZhp+616xwi7kIaEX7e6i1eJi563TjHgHzvdvV1WYiKhHuR\nJGRu\r\n=64TD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-label", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "aabb4aee9bfe306feb1161c996f175cb245490fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-Ah8dAS3IvmoR8chrYT5Can5+op/cR3QQFE4hAP/mN6oaBcdosXoEFQTE6GgAzdaotGa0Xzp/CLNR5d6zuIju8Q==", "signatures": [{"sig": "MEYCIQCTV3USEi7xg0qMl6d+4KCtN9XsvsFGWoU09+Mmm/+G+AIhAOPdZldUs0pCzxjX+4Ku3FhxPH7P38G//USKNSXRY0dE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20724, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HxCRA9TVsSAnZWagAA6DwP/jMFi/qFmYSVg9L42I//\nC50nRRhRma7BPK8y4X0/pjFpcDczV7hn3+bVfIf3cW/P43GExTEsafkH+Ezk\nHZNmGMqAmQAJTSm450llXxeECXNlsju6LCdcEEn/2x6Ajssm/u+IXs/U2+qg\ndEqyuCZ0+e03uqRdc9lsOtsMw24HHhBoelRoI/sL6zUvi51wxOrG4qJWG2C3\nng3N7ENlH17134u6frpDIpaiNGrJ8fvsY9qB0CA1A8k10oeMlmqALuIQilGV\nCUJFtTBU0OicmAv5+npLr+vZLcKEm1rkIgKDVIwrU+DblyV+yvUV9N/a7Zn8\nJawVjw7wOXRgAxnr/dnXOc0jmjlvQ9LeejGKtYzGReeqOINguYi17Oixuanx\nplpVdic7TkI9IyEt1H5XV7vAGwsL4H0BgB1WHnyghPdTB3jvx2PSPWIgS0om\n6fL8H3zVMeqTAcLPf/uBJHdxID5OuFjvmBGd7A3Pw5kQRgQzc+4I5/+7mq/P\nXrQkApTQNWYHrm58co88BPhR968w2be6XewKunxYLo8hxrRmQk+TNfBXslO/\netpe2tTJOnWaMNx9VEpjqkTdXmSLrdkGH5KqMlrwmjVz2kk1t1is9Uye7vng\n3tGw9V2U5FkX2Usnc20ZrGvNgzRxpM9rIXMEKOGe52tP/gW6aJZp6Mb+saGq\na+9L\r\n=Eq7s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-label", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6bd4fddf7f9c443bc169933012020f7a21975483", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-AZf0SDp3/GDtdcY08p00Ph7NQawkRBC6O/wKsOHWVS0KA9+/F2CdYvrNJAuYlK+sUDKd7yeS/i1ZRWpWgr9yvQ==", "signatures": [{"sig": "MEYCIQCOwESD9xAmUYt19tH8HshaZxf/T/Yfq0VSRS/WoXvAEAIhAJKIDXLZRUOThEZfXi16vbJYWpds/3787/veblzI5rWv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vzCRA9TVsSAnZWagAABKQP/2L+/cv7wkGeHQu2usjz\nRwUYqA5OCKxulaxxNLqkBd5yrDl3lfKFu9G3poLjpqkqPHMNVnq9mGUEHieU\nOQZLOhw5+Qit8fBdJAM4CiWLFX2u5wRldzQxKKQz2cXFPmue2m59/DAhEvgO\npOW8loZIs4UdYfk3sjNHo5dgRzVD8SKyXr7cpYmWxV0hXIcdGEJ4R4tsm0NH\npdqY3gBdgBRtqBAdBgSOotjn1wulI4rz1NT8R2xKgeVsNULAd9fRZGNq0095\n/+ASLVR06EUw/nf6lfYBcB14qA5sQWy8Ia//UTodffPcC8YCONlyBZ6JJG1j\nS9Y4aI1KrmEBCOrZyR4wG2uuLq09/UzEnUsPe7owz1NpnF61bQsXn1MK5Ios\nLJf1mOXEanObtD29adEpH9okjyum1RaYCyWWp6qrQlTAsKQWp7xudpqTvtGL\nnbcqpVLvx4jVniI05IPCy9qNCfalY+Eb/90Wtm1uG4R96wFmEOWSovI6cphd\nMdhDZiGy2pKSkzRxy/wZcwaf0Fr5SizJCfmTiaZ+mYMYTt+OC6hpXdutcrn0\nt7VvVPqafJ5NBA3AsmtZt1NzPM1AoXVzhl9tkeXa65CM+6EnxEhTeDId+tz7\nnYO0suOUC334MYpcwA0SziPhQLAhc/r/dd0Zqqhz1LqGKVlSQY1CtjNAGFJq\nT+oS\r\n=RYxk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-label", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b7d55ecfcda088341fa5be730922a3b3359ae2b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-w0uwTAR2DW/9kZucXOgIS/eC0mfmGujOJEhP5FgY3RQLP/FAMIhBXX1VisHT0ING+ftlxzIw1/9rACyyJKINBg==", "signatures": [{"sig": "MEUCIQDoE/FhJcjEjYBnbaNCXM12ZIfXvxvup0OfF2gnnbFMqAIgYJfirAzpyLvQi6IAcnunBNrsP5O7j6VwzXXsSuE4iBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmkCRA9TVsSAnZWagAALFQP/0HrX8Om3bDMqr805Clu\n0padnbw9zAHU2joV6VloGktKwiC2YcRHu+lHfud3rdxc9jZZPrhAxYCoIPQ+\n82p9WeO++bDekBnicH9Vn9HIIKXf1AiUh1UkRv892ClWiEeTvz2jMej0/dT+\nYBJTwmoCE5uquGDH33L1VfFBUUlUNBy1QLqU18OOVT6hozyJ23injJd4lZMc\nHhtSPoqUTDxPN+psN9pXHhuVLxtpJMRhybn9rfHT1tWgtprR1kA6n4RNTYnZ\nGEx64mbAz2DILBaPSuKSfpNaTt6HrhmiReK7ahKKhDQpLQXp1BHTJhnjwVg/\nVew1RLwSb0tFjeQ1RvUQWg0u3+B/hgVHy27UQdiigHOlKQsw0mdK56kaVyUb\nn3pcVAGC2tgswDEw9lcC17GchHbOhE3DiaCls3lo8w7dPM2H9XrjqRCetGaZ\n4zK/9Ou4x82uAAyDqy2yoG8Yvd0frzi9vshVmXVJz14Zbk/sCrNztQO0JywH\nt6oG29Xxmc6PfFwU/jkhT3IGPI8efNyrH4w4xlanIEYy9KwmgAa6MIhMHbQc\nuvfVAKv5/dJWu9DtWxmqYcq6/ktKVBvf/Lm0hqcQdJ4YbSVpfQw4dapaID7k\n6kZzqXRo6PPv9meGFypBYwjDgfLv0MjXf1UIbbi50FkF0n4dOL7U/yYbmyDS\nD9dN\r\n=XW5j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-label", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7b9b89faa6907fa5891fe9ed241697293b3e7e43", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-fOxpifCpPN0kYc5tVCWa7EGs4Gz/R6MewaOtZSOgucNqrFl07Gdwi41irCjeZJgPzbgGD627Z9AmT2aYWN9RmQ==", "signatures": [{"sig": "MEQCIE3hHWmndjxABEj4ofKZnAqEMbG7EGxKiy0pp36b6CH7AiA+Gf9/WfTH3/ViTNE/+7/6KiDQ2c1F5tjdBfX5zOUR+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19676, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7RCRA9TVsSAnZWagAAcLsQAKSzej2Tp0PiLNLgB+Yn\nx8197X68gGkDYf588pTOm9yvtrqT8+ErxzYGmi69fXt+8s/BvHhS/LLmFOZm\nK9F98Jcxei4keyyZGhRs1Opucbod9EFjC/1uE4qnPQcXUup1Od62cucsTLY1\n+oOxud/WCSPTj1PmTyT5a23uWk9sU4bQCtct6dt5id+oXUMCOjWkIvPgbsZ8\noQlvj9SZZbB3wh+GBByrp2lWvdflhIrvplJtGgWyiOtFi9127xm/0uia4vgU\nd9NBarWCawl49VaD3lFhmfqSDcJ4tR8rH93YLREGR6qQ0CUXRvWI8FkAN0Wt\ndU57O/m19wTcUkGGOmEvDuzcKddh9J/acwnsAnhSdfW/c+iHB+AsZ5ck7ual\nmD/BTHZmecjblEg3DBrP+RZRiwOaqLxsxoFRjWRqE6ST4NExeRXs7EnmR7Ey\ntW7qOUmIC9WFjOjm4xiiIvmsJRZmIONcqkhyYpXEstRPLEJ9R7Uu0esqcKEu\nsL4ohbHKgQNjjm5wtwhDsZzANXjwWivbu5KgUsWs7VoJSlKWwTWC4LLCmf7G\nvzcfARU5u4sygXLoFSjfUalDpra/xcXoiTqRt5+3Qd+/I/xRSBbKvgWjPK4+\nj8T36PfN/4oI48bqn4Hdn/blqPydw/iVJZYAm/6GXQCKDiaBKx0/g7TnHDTD\nsGhb\r\n=sTD9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-label", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b71930fa16a2cf859296317436cb88e31efb8ecf", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-csNElm8qA38pOHr772CXIvBXd/eCGaoAMImuLdawUxQNzwxQ4npd8lr/f9fi/4OLkgeNOVOqjsaVamiNmF/lIw==", "signatures": [{"sig": "MEUCIQCL/NaxtSEw0HQFVi1n10ojHm1zAWCOX2m4NzzBPxpToQIgUnumNIcn0S1+iRN98CXW5hdgeZVtU4QFM3ISKCMw3eQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlX8CRA9TVsSAnZWagAADkAP/i9rUMtGJW88qcOPdDAi\n5y3vUAcOaW4ay4kO9S7CBwtH/pVX6Byev0C3hjY3V2PcD/DXK0FlLtfL5J3m\nVk34VX+UChJlCdfK+m2HNETF9Do9y+VEo91QjUTQEk39qI52qvAVskviwGxU\nl66l9CKXEy+7iPcGzM97rlzIj6RJS/84cNjQ/mVsUHf2nzN7PHAJMvOuD2fS\nRd7fXUUXer5xDaO6hKCHNVSNnXd+ewpnTtxjncDxG5/5LbAyZVc2tFtzEEZU\nPHh04MocSIy1LeozhaoQWXcnVEIuSaK63oLzabLz/Sh+0TJnelUQtwmDpBlE\niuWl7PkvS+J1Gxp5Dn+e+rzjXweIXipHaPxdEa1zL/Um3tYXWCcmCFAQGfew\ncC4u1kP+usFjwA9zIreNytqaSgyjchh7Ayw0ya1s8VKKSYS3fT6fIhU+w6UR\no4siCOppBy1G9vansHOgePsM5Mxy09tILFuVEXA9N+x5H6eZnlzwXlbesnD+\nOH9Hj/c5EhH4HE5yFKU1R98K2JOFu9B/gnxXh+MMwRGRL7YImBghm/zb/Vaj\nwK3gCpEeWhreBRksZqJVtjil5sZ1C9Guz3Ag7h5e4zuCkRtncOBgxT/zzKsz\n4gpQgP7WmNWasjwLIOiofxco2eDShcZyF1+eQfbO2BGEarEXmDzUc8xZ5W80\nyA1T\r\n=if8D\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-label", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "32928a0f5d72e3a24b99021f5c6805e96f3d9395", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-gtrEpON6Ye8OBYtOEJBEuB/n/h6x07VPs7JR/1t34LaB74GJ+gACcafLcg5gMGWku3Gbc3eaDwfhOkxhTuJdYQ==", "signatures": [{"sig": "MEQCIHMAA7EFUifq5ZCcHq4/JTtkIJVB1nh2DrNlcxnk+VLCAiALOaI9LI7BJtFlLOhjXg0w9ynwkncjrUtPorEE8P6YUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9tCRA9TVsSAnZWagAAJsMP/A0EenIuHDb3yOqjT4v2\nTLFjngXsYJx9cuxzW5ub7Oz+3x2A3JdYwDB3Vhn/f5YB1hXJ5QrFG4OAR9xj\n9p7EXGDhQd31+AhpoKboduEVmk/qNQ0INS8H2JtVi8UxNUFmqVimUWYsMANe\nd12ce62rfWsFMtOnyy6532eyIbfjyUb3uukYWjQUf27S9XtrYCiRujHbxhoh\n7w/Vh6Ouh1unKqwSSrtFeyZ5uGW8Vqi+q6dWj+Qe/8m2YBg9Tkrnn7XxxN/i\nNpEmZCDl2HCA/265dU2y4eO4U7a7qX3qdPWowZnmYzR4u6RpT2QSanER2QrO\nNQGR+ElPEWpN2HM/OHukgZ8y0dNCNmsBmEC85UzphGFqiIs8RGyfLTXuQkNR\nbNmGWaqFvNLZRbuIWWvDaN6VHwcSon9i99b+6uJZPYQrSUskUqoGTWZpdnhs\nOEds6s+2th03B2ronXSyeI5JW7HJ71HtVt90XZNe6kCxzm0ozZgAYa3TJLT2\n8P0UPle7a3oBSkhqOC2WM8Sw7XFN21fc/QL7diQnxgkCTz9zHDdmQ6SsGs4p\n+m62Bhv4LtTa6NPTj3CcbSTc2ohtzAPhW8rEjl8KDVkC/g4RPnHAajIb0ZhV\nIHpErnyl+dEsPeDp06ZVKZe/Reg4ZSc4q2eLz1zmylTzi8h5XBRNKyZ5RLFB\nFu9y\r\n=q4zk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-label", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ab70d7cd93d6ebaf2e1007cca70e9b1858bcb932", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-p1nM6z2rLkstfHVsqSxcDMn0eAGXkx/G5e4XIGmOCxYa/7EkOQ+lBz0+/7sk+Ut+8B37h7d0bfxnzr3ILVxJUw==", "signatures": [{"sig": "MEUCIQDtzf2iCsgU5+g+iP5lUOb31BBodg5FpgF4wxVie9Nm4QIgOdB6NTPr86WUBb6qGG0ldA0yVCXBdXwswtOdbFYKxIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTrCRA9TVsSAnZWagAA1AsP/jhjdPjnr8nRcQaAO/b6\nxCEg9EFygwwI+58IgRBW2CZ2wNkgPDnRF9sHiUdlfgPuUm/E7a4GcHtD2TrZ\nzhPDhL4STKAEOgGb8jIOgLHutS9aohfXIZNubRc3H8hVqJFYFqUv35nGCL8E\nwWlpOMwxhiNq4fO3m++dUAt4J4f4NPp0ZMN6OO26TFzsGCjV6bqwAKfMkdjS\nvk03ZrXYljBjdY23KyKsOykgChbLQeO2B1I0Q04oF6eDIHpYR7vWlPWpxuQQ\nd2rpQMkETOkEayEZOktMwp8kZnwpuyDeGeiqFaNqSAS3fYHM0tcJbF+5i/yV\ny4tAxkCQd1rwtaUmWNQRRvXaWkFtb5x9rb48O1kgeKvUD7EkO7j2BnQdiUtS\nYOw1/1t3UxLu4bokorUsdv2TsC3C60HheaiivJT2p6sfgXhVtmHhnnGS/+uv\nXFZsDkrV5t0qL1RI2TdUNEG50c6yPxocYVLbfrTR+IHKV/txr3qpDkNCV8KY\nwPp2ES9ZfiwFm8hic69pZwVcjOZviQpQ7yxs6u4DGT83xd7sFs45tpgcB/0L\nFkAFhsY6mhks79nE3xvHLoa2bfSw1wf7lvqyX/+LLbR33Bqq6ifa1YmHXtdl\nE7kA7CASa4y9TRF+df5mq3GxEkl6JZfJ2/7Pvt0ahwdsk8S8X0tJDFv6L94D\nhGlU\r\n=1lCf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1ce9c71e0884df81ac0d4fc2c501b9c96773c1b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-RvdnKchVvquB/ntRzvJIZmYnmjtPglK1VKwueFBULrHIL0ooiUEVJZciz7StTeqaHPRAru4qGilWgcQZQWsGKQ==", "signatures": [{"sig": "MEUCIQCJmNXT1tFv6dFUUynVsbXJ9zrbSYUccAaaX3w1lv6mvwIgYJ3QFTPfX2KbV3yJmQeQCBx2C+l4gsgQjg8iqIvPnYU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1096, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpgCRA9TVsSAnZWagAAlfAP/ibUIf+6gNQCj5peE40Q\neIZyOyft+RMTi4C1kkdQ4Fox3bNVD5AU54uA+/GEz5p7jvVtfF62aSlkDgDo\nlf/mmdj4zYLczfAE/s9CXCSNzb5IoaH4yTHzFPBjIDXOhirpxPK/LKT3+FIW\nOx8r0DRG4yXRtQ32F/JgJmtFOUopljxn6W6HbdtAkLPhvl37vDNV1r/FlMIP\nvY6RBQyCz431P5M/rqQZ2bobANMvYDbEZBHhdwb0we+lZ3Ly4boIPtxAfASM\nmWwdHOIzCE9y+BBxtMVa+RfRbvF3kl7xB+TJTQ7BoCiEcjnrRtU2zeugp/NI\n80UHm1sfVMO8E5N85P/q/vLaU9hGfkgRXvbeSvrpWIMltdAAN6H1Jk5RmoUN\noQhUI5qE5iXjoeRcHSG2iVC+Vy117wb25j5F4xxCVf2cJ8rmSgGsnDkQI3S8\nY7eEwwL2BJ4Ln5LFezKZlCihP5+cMu/aF8jkrQKvXLWHitupLa2zb4/ylzeM\neZK+H5BgQ3ol5gCy03qQA9E0npFlHljvGRbGBczVjUOV5vPG7fO4OetxF4ju\nh7uxXhzhoZm8oKsge6R4aL3+J4NlHKwd1awkL2m5BTDOdU1+LA/gBzxEFjdu\n41axIdQ43xiZbRDZ+RslGnUyCIo4MWMrWDJOOsO2153H7rZIDjlKWSwzLOn2\nLEcf\r\n=rVLE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-label", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "48e6c764ba32306f5386ebf88dfdcb201d4ee6e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-GfJj7sXAEEHIIJ9SOyYJ/Ps/9Ua/szOBALoh0hOSjQihq69TeRMk3de7EL5DMSeq66dm7hQbi4I4Owc4H8a5tQ==", "signatures": [{"sig": "MEQCIB//Iv8hk6S7HMQNWBkTZ1P6jxNd/b0rad4lJ5ekTv5wAiA0jgZn+cvK4lzY+8CnRbSSsFvV/n21/SnQVRnZ1NyRgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhySCRA9TVsSAnZWagAAphYP/3JkAOfVVFbIj5sPaML7\nJ2SBOkGKhL8n1ckatHFAbG70weMfKdXYJlH3DlypcyewPIGY3UnOP1hF2NTN\nXZ9HRefZ9O28t0ywDFxflvVpO9G7ABPsH+3y4XU9o/lmeZSeKNEn7d2fI8Dk\n5nlbHekRNck+bUonTYz5HF+Rty4olX7wGEX/F5B/LQ5p83QAxUauxgnIQ+zl\nKDUK9D6YE8GuE+aFnF51ue3QoiO8Vmbfrp0u/N0h+OXl+R2Iy5M9XyUps4n+\ns1EP/BtbJ88CElqXkvQPFYwog/4FMgrH67+/qolnG+O0CXyboQwn878O7UUD\nf+xxPIKVp3rSRLQ57+Lr0u/RNdrlCOMyycuuBvVDWXtWN5yObrErNbzIEPJE\n6KaTvOxhCjbY20pCCmLbSGcXWxhnjkj8pNRjD0g7rjRUKt797skSIwz1nz8Q\njCxnYV0/wpnVENwRY2G3FJaMzP43k21FRSOwIKNtkBo6OE1uD1QYrfAtJkAe\nUcH/yKOYyure/nnDJRh+APH8tm6ArJgP8dh3CZVJEh8tNhJmvI1fao3NF5Io\nRHQ1svh0eocJWDPgGKWSGLRjjewsKeaLFMOXlYbOwE5lCrVnhCfsRaotjpap\n040/gu0SIhuxYgvqAh0jnJTubtSfb13LflfUFm5ugYQ7eg/O/J39uUMwzRWh\n8mb7\r\n=aZnU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-label", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2f460b4422a87097ae7cc0b4567a7ae5f269979a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-YZObxcY/JKmA1Zzs+mT97Dc65r4IYv95hlW7RQeIyrR1+EMQAsZR0Fu9HbQAvgAd0macb8fg25uOeq3nCV0DRQ==", "signatures": [{"sig": "MEQCIDjzSZOUNcCcFarytO9PqRJs9wpLv0T9jLgKjd6/+ISqAiAsIYNxYPVu2RI3EH0ktl/ZG1GvhSc2Z3RNZVoA1CSykA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmfCRA9TVsSAnZWagAAdc0P/AxuOs1nal9E3Iyx4A0V\nOtIhQy7oE/VvHRv/fpyZAVy7dnrfL6ipAmH4zf44n9/owxKISgP0YXXF9n7D\niKYkDGj/Bi2sFAbdu5VeHA1a13DXKVtEw9p257+KyxZVbyuKIqSITsMJbyWo\nrLdTkNrzDKuXEB+NAqL4uY8fuca5+wJAdzGyL/cU4QPpHbv+CMZ56MUEaaJG\nOTSyOiPpnNqcqH6jJK/oqawz5qvg+k+MkdI/7934AeV8IBRdrX5Op+vVKLfQ\n5XQvvWPuiQo4el9iBLrjSwJUSsJmlb24MKzuEHYEmM61XFlVQ0tBGV5ADcks\nMILrtkKKq+m8wFOPMShien8oA6DrYdt3f4Gp5Z+Bpa2acIC2+78hfQ1tAVbe\nwdl9eORG7MORQCahIFbLdrs3ypmiWf7SEHG4UC9PhXwS8IAHM8JTYukgjhTm\nFmYSn34ZwjrbcVDnIhJn0MgFerEtlGPKbdj/nNx5T0gQY7f/U8S9JgsOUGlw\n2/Q36tA76WkRS8T7S9nybBEDPtfnH6E8Q1IUju++KU6WwKFCXXSvApROUXHI\nyUPxqBKLPZZ6jMymwaTlu4zO9jE0Fi9HAz8B+kujfBpqvxWCkxcsn/pHBN6Q\nL9W8q8Os4372K1Q2LLrXeM5ycM0dxoh428xBBflwQH08t2TW88HiXSGRvCBG\nCCP3\r\n=MUND\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eb048dac20ddda60ed44ac2b3c2e1ccdbf754d6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-awS5URX5GnG5zyqSPKO3GVdyBOcNH3pQ7DaELBdx5dlfzrm5j5zAaKgu6uGVpDSUi6VuhW6fh7dmxzUf+c4DMQ==", "signatures": [{"sig": "MEUCIQCJ+WqjkJc55S5KIsUHez4mSFUtV2uWyZR7/PGCJnBkeQIgCzoGUmHmRJEINM70iWvtCSU4RoAciHIA3+RwopoPiv4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 20330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInBCRA9TVsSAnZWagAAwlgP/2GGIY6Y+tOu+0V8qx8p\nuZ+7xPCKfVeQConi2Pc80YoKOX/jMKABekOdWmdRDWJlshrKbaMujxLg0Gv4\n7Y60DNTc+JuAsmhWiF8IcxZZSsqF1+W/biqX7U8HufIiLulJ+Ao4YNtGb8Jw\n8CuTtFaoviczTMzJSNslEqcBqpbF+ypy4jEJMJJlfhPVf2GydrODVPi98B6D\nMpp7FwV7XOVYZaruf/3RbCGgfA74n6leZEPqPCEalCA4vvFknIjGtZwTQzJB\niRcWQ1Ou/bv0HEA7AcrMZrmzlnnYJRy8Ul2L+tzo4Ox5pLiQD7aYhpSbvr6X\n9IDkOWkY/4JPRbpxdOflLvnY7XjRs/tEBv5PAR3h9UHfh08ZffU8aXk9rwEy\nZ5phCQb42czUs7S9Eal/ijgjGJ35HL9RgMJIBe8GJ86NXi2IVemN5PeAE199\n2h5aHvM3sTSZeIoLzrp5U0khXrqk2cgBA85c8r20w7gQhoNZs8hFW8ehOyjU\nOTkdohS6YRCRFnVUmiH8EwkCqmB0im/SbuFseSPPB2ETQxk/VlWzuk0vKwGf\n1kzftJ+oW/4ttD+RyRj6NK6o7wy3xhP3jaaS+I4t7UcVtqZBShFyGXhwnRPH\noOziQC6/u/qc2b8Mu2lHG1SxXS8MPscA833jswKCTFdP+Thysk3BMxTMAzLY\nnzmI\r\n=1bAw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b78ade1729ca95f7e10bbc6484ab3ec01a621787", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-gjQCdTJrfrJp+MKeB/11t2jQdqPUGBewZpo8J9vsTghjvq7pNIlaAF4eW6RZvdySBOoLA3HcQL/uSZT3VGWGsg==", "signatures": [{"sig": "MEUCIB+da1bewrph8bgaw7g2dAOHhSef72ltm+INsKxlIOa+AiEA0w+jTigiZB624G8amktLbnTpYAlYyS1Iwh+nMKzstUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdv0CRA9TVsSAnZWagAA5w4P/j9RzkKTyOHMtbG9aH0R\natLg/vwCeHZ3+NcXJ5hk4fYf7o7abGBe7SJzLoA2oLv484lIl8UZA09gdkZ+\ntOy0fw2m9eiidch2zSIaI2J1A5nSvoJk4uGjs3MpORmOAUsHlqC8NDYkD7xm\nIpCoQIV0KTHMQUjVWDAFhYir5ODEWYI4xY5Nd7fWSb3WsFJ0IJu2RKgGk5j/\nTu4RqxaUG6dBKbIJB+7uscsKTrpMOoyacqMSsM6hLlXBbKqPy/8Hq3GEE51+\nWAz3pskQ1siEb1C5bB3sTcoQlT3AuqzLUR0upHhC2EjvN5S/HAFIJ5aqHBJ3\nC5QqhPygdiOpn8w/XeXvfwZo7qWhmsu+3KncozqCrHcxd/cmFM3Nha0hoB4j\nuPkkV1hZTrUReB0X7le9T84ZgBWPBn3DmkV3yqF1P5ndqZy9HldDoJWKqSw1\nA4e1OnBwwj66CqBeMvdhOuEeuia+qKW0RIdDBa1q0MDMw9WzpPAdo5BKrWbO\n36Z7PFav2sQs5NgMpRuD3jlKMN44lgqyeS3Nj77Kj+z7FVXY9jTcAiL34cTP\nqVz/bOOOmr6LMgbV42p/i39qxilJluN+7Tej9t4EPTXYjfOiBW/NIJsLTws5\n+FtgI3JAwLt6DLAsYHAyQyuRrZxzD8WJVJ+Rn5NdPhPbuJS8g8AgNnUJW1ZZ\niq05\r\n=PgfZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "13482bf0f134cc75f5c524e8fef7c249bd3a861e", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Xgq3gIcD1+2wjRqva9snLrv94rGCvyMl4Tdhi4BywLh5zH6pvM1qbHCkGClzzwiGZHbjAlpIqvWwVv0u0Kg3tw==", "signatures": [{"sig": "MEYCIQC6psOb24+l3ydNr7YxVAe1B2SYIibdt29cJTu9tzd3SgIhAOzDYY2FAptJTl++lUlYU6tlRRZDERwdNEyFDex/j1BI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TzCRA9TVsSAnZWagAAYooP/j5lkmrGBLdjrdFegyiV\nG52dbvhNBV3tAp44MGdf6ve22dZczUieHTWZBjGUKbvKlwu5KsrkwxeT8WAT\nsqlLRpO21ln8Q9tWvPeOGpsLSB5stwY0U3NuGnGIEPL3IVJp+vJufEwpQi5y\nAVLlmaFyJvXpCevMJMRBUGFDjP+2PJhJ2cNGSbmxG+wXoWz7eXK9967RNEc0\nhNw35SfjkqWkrYbqo3RXNtjjVN5T/kN+BxdeWUHE+KuvMXMPG6+LtnfA2MT5\nUBsfrrTIlQv569hk5nPYWglVWplQE5zUMmujuJ+lXu83hXb0a//eRnHN+sFX\neIAKnzoT/BkUVAUakrRFl0Mi9LpXIrBAav2q5Os7DVXT3kyTECULJaeckOii\nOpikLij81nUWPXjYEuYrrACGSxvP1niSQtdQnBpYUcXZj4WYgMy39e252/a4\nU0UuYH/wT7Cumh3x6hk17J9ZRsqyk7vIlDrWCZuFjhQVGS5V6ILaOxyyiTvz\npO08ZjPyk506i3QrKCAG6LgOcIc5Y7ayh+VgTHD1q+QWfS1Oyai0woqk7r4n\nMPHOKhLVN4ybOUPH4Gt3gysgBpDGKT4cGC139hdr4ZnDPmXquFP3ogxchnly\nPOCOFEFLK19akejZRCsEvKx/644AyV4jkEcmXs5KDtPXc1kEvBKaHMzfDwxb\nR8Iq\r\n=YLyp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6e172a4b800f900ac39b64acf43fd8c06406ea5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-v5/f1vQC+yOHZnO0pl+AXC1IALXt3IXZ+H/h4W5jd31DKspwO3ORd83hqjCEF1BIvEzqsveRVgeSy4K2ML16Dw==", "signatures": [{"sig": "MEUCIQC6tPTvyLY3EZrwZsGfxciSp24zhCEF/ja50/OxgsMf9wIgaYVNvOeVrvtAQ42L/wFBsjcP0t2NMVjk1qgStebTVCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1zfCRA9TVsSAnZWagAA9UcP/0eJ9/TMdXD+Wk4bDNUG\nt3i39sU38LTAciWTl92pYtbr8b+rkS2scsat+jEsYT7BCthl+fBQ/aFrEref\njOI5bc37yHFn95Av8qqvnFk2APGfkw5zAp7FeIwNxU7Gm6LoO5F3XJMxKlLd\nm2L7Xk+/XB67TPcNdpCqyhQZNVO6naa173WDHxzjj6P1dHXPAuk0cJzbBYwo\nD5IgoLUjIAjzap1kO4LNw+1rCiph56kzks3SIYjFNcgUMqjujhhp7nlspetY\n/2S399BEkYvsrFHYjdoUcU4wpNeHtph/1d8aLLpp9a20rn2lN68G+veQs+4v\nXx+ly/h44eQ0DdccQIIx8/UhA4c2YrUNIddpgQff+6q2gRk4Ry1xWCP2RLqS\nCBm//Idj5urexV8TIcarjdv0s6pcrHm6s3M7N1gFvm3KxWxdyNbOwMEFqayH\naiyfJ96gFF8AfDKJOUJseFh2K/kgP3Oc4a1F8LcxDNhKb4912i5/P7Z1qVr/\nmSYITQc0GDTSAWNN7FkpFqmp28bXe8pOn/sZDROdlkhFTYf11cAIE24sPIfv\nruoGx6N27TZjE6Z/v0RR4m37jz3k1TFLPPg4+CKjkwebdYv5stsf1FIrE2sp\no0yPYTiw3iUpCBTNOcehaIK7MksE8uZICtroegQHnRipO+tnmIUoe/Qr0Orm\nDzCZ\r\n=ebC5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5fe2660a4fb3d34aa39259b5c0f3a4682386215d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-I9lIaQ7vxYV3b14+gPPaNBtdzTUPZoDlI+jV8IJD5WChNccPKOUX6JaVkSzPUoEEZ3B1pyX0fSbD4rAhgLO9mw==", "signatures": [{"sig": "MEYCIQDbYNPkVkcxrYup5gyJrEb/oj96m82pYfM2t9VPutvvoQIhAPt95U3TkwVHTjAI0nDlRElkhfvwcI8GGWBPTm+Q1hrk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFitCRA9TVsSAnZWagAAufkP/32bjgHSDc9AcA0viq4C\nYoPGusfW1S/nEQZhKWSnJoo+YNSStoNR8OHGR2yr6oq6l1aT8koBVGuGo3DU\nMfJqbTGgmBqfsJby1NVcYMotTDmUWQ37J2O6d80ntTifJJVUUYXp05aJBTk9\n5lskePwzKgITNEmDC5DJFxvGFPAcFujsPgn7quYUYwpnBCyb3m1uiSqRuol+\n7lQii/7dW5HPJ9Pw/Fv8Be7meclUE/oicUneRPuX3doIE1I3Zn1ekV5QM26H\n5MTihDClw0NfSiGspHdPG2VGAU6j1XbbwuMQrSoyyiiks2xIU6PEjTPOMnjJ\nqvvxPsWpc4sVAWUPCa991oY8RGW4cNwyvIOqBgarSzz4/YI66Qx1UDPTI6Cl\nr9XaeqGo/uh8NUOL6ZZpNl/aQLEEc6x7yeJVjaycYNAKmvDPocsTvreWto+3\nJII0H2BYvrU8wljoRuyDgmKcRsl0/Q/dHUB45frjaIVE9yKMiNoxxcwdrFri\nISl/QE2+gTG3UH7Ky0e9J5BUSMlxzIJRzq1QKsXojVaXvh0N2XUT4o3hjvco\nzyWtLHQnLiU9fu6W1LZt6VIwB7kuMY903mkxe5XSO4/tZApGWX5pmwlxS1oS\n6T0HoNJ1X04U58eo55mPOMSlC1MYjujpb5hUW1C0PMZ7fIOVcTTlc4JKKjY0\n3qDW\r\n=m4+Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61c56d26f2c5246ae8d21bd47907ea23a9282a56", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-XSeaL1ygPdQmuPo0H8I+r1MCWb+VQZkA+Ec42SzAXxIihBClgH47iMk2UvZ37sZc+yYlI8qRMIV5C2Gcas27+Q==", "signatures": [{"sig": "MEYCIQCjNdnfK3xNGm17kq7YEkaN/LzOPIPbO+LmdqGpCwL/VQIhAJCLFm+0/xaBUVaDDj+ZAb+cZyx441a6KjOBP8iFBPHI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710}}, "0.1.1-rc.7": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "693fa32c36ae22dc59b01cb4a84903ba9fe13fd1", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-z/FUolWhzbhOaz2xonrjTA2Ho/qJ+QWOybPajLMixFViW6vuZkg6y3jodwj+e+f/85lqJbYw0AcWija3OtScMQ==", "signatures": [{"sig": "MEQCIHRUSSyTH3oKBSMQSLHXdw2KdsEXNkI6CXbxyN5o2vfIAiB9KTi2w+/3LxrYwnlXWRjxhw5c5ctQ9//Rkr2Nkff4RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710}}, "0.1.1-rc.8": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2e0ac7da3571df6baf919285b152f3d355b3a57e", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-LyqMt1vbWQri3WaYXGrL1BjPOnj8FVhWpJwGHB1OLXsgeRrwGmt0xu/MOYllLWFqDkpMlutwkuDZCtXKpi+pNQ==", "signatures": [{"sig": "MEUCIB3yxUil4x3QotAf6JR33lPXdbHOQallbDMjbbZ9fgK4AiEA7Aktb/QvRDhTszaXwdsEPnFPHZM/befELUMmwurGU5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710}}, "0.1.1-rc.9": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6afc8f48f9c8a0b8199045f9f11ac06c2b6156e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-C1n4CAfvI904z40/sWRbtwgFCaXvy4YXo62UMGSpuWsnDxbFnUM0UsffmaTKG7wUdtXS4OQdCPTw4tl8D/uMNA==", "signatures": [{"sig": "MEUCIQC5bLH9Dz06rJ93TXn8ix+L78c91Kb5k8olkDR109VQgwIgMJu4kBvFxga7E5dF/9cqBULhEFSRoVyspMYIAs+/yQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19710}}, "0.1.1-rc.10": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "36684d4213b31c711e2f89172c7fc86b888c3854", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-OgnUoY/8EBZmY3B1BZCXElfNvZARjQ+NqJ18K1uGaZ80tsQgmj6ZJb5ssupQlpkZMm5P2+ejQA5IEezIwCfJ7g==", "signatures": [{"sig": "MEQCIDj4TzhDcAKQIkkvuESraAQ3syoMTxpIkvEbs6cgxr36AiAqsNSTbX22OZkFImAlaY/okbhcUiZ+CfP5Yd/vurzZZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.11": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "677926de159f4cbd6578f4c323d6caa9a89cce10", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-HZMPFLzapPf6PKrv9KH9pnmqQQNJgfVAEoq/st2o16K/YRQbNat/c9XZ7JThvmFOzI1CxEnuF+AOnMGb9/q3bg==", "signatures": [{"sig": "MEUCIQDlBVxUW+scvH/eqfDhvKmgeHrS4BviSNf1Pkz5lQ2L6AIgTbZJbaMLxISbmAr7k0Ed/gDOukgIAjXuL9KqORGb4YE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.12": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1e9608ddfa24c5185a4eb649fefd63394aca7cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-EOpxnCBnXIJT8dIQz9JCQWG+kwe1lvDhd5otDJz8s2geOsIEX2NBrX4z2vIRL+VKgtL9SGoiGffnuiqXDoloJA==", "signatures": [{"sig": "MEYCIQC/uwgHdt9qoLpGLdKff6aiECgpj8uhWPcCZsIXKwRkyQIhAKnPHi9/yRAJmaj4EE/9mD/UiZrcLKeGrlRy5k5f4E3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.13": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "445473bc2092752bd08a0b7ae211717f948e02a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-49gVqChsvfu/SP3SuGBixuqGmxR7DKHqLF2JFaaKJ6/Gn5pzg2wKNyfMnSLs+DnQoTb/iva+E4nNBL/dNf5lXg==", "signatures": [{"sig": "MEQCICfe/20XE5XnkAtxDClIXrOsuPUjKgFekpVd5/YPXhXcAiBM5fSkerQDcxhKmc125x2kwJvAwX2cqHgWv3AInmAhRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.14": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6375a21e284d3369a8b6eb9d2e4af46185f8af12", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-gOVOxUNjnAfPNA+9DRh99XwV/QYbM5aQJXS4D+WHpCqxNczspxFbRecU5OlNPn5xaH/uWvOyTzDMVkfk6P6SEg==", "signatures": [{"sig": "MEQCIE+liZnxtzfgI+EDNjo0byZko2gqgGusCPs0ll09zc6KAiBN81vTKL5Dc4HGxdXx7oEJN5SqvhldZjuKKkiCq9E7iQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.15": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b78d5e3fd9ca6913b5228493d586af1e02c02055", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-0fxki5+SmYZPnyKUodl4A4q9gb5Itxypv07qADNLgrBmjmeDzOAHkq8tWipyYGmNFbOneSr1jOYA/nTg4CQmag==", "signatures": [{"sig": "MEQCIGJhD0wwl02HFYZnnAhf6qJQfng1ZO00UHD1a7SsbbkTAiBPJEMkkmmW1+v0XTp7aKFroyfbIQzarxwVZ9kCPbBaRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.16": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a9d8feeb0ad8c0795abe3a16dc70e37e83bf90a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-29cZyKyTAUV/QpDX+DfYTGri+x9l7Pdwq13teB5+MXZt9J+cbGJfnUIX51Oy/AWtM7oIQpkQ7wELFkOmp0aAPw==", "signatures": [{"sig": "MEUCIEVUiO2Bjb3TUcplvcD4RS1DUgm5npRHgI+UYqsR+oBiAiEA0I919ALK1mtoY3+ECvqquIDNcZ2ZEssorAwRqEBkors=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19712}}, "0.1.1-rc.17": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "19e9cfdcad96f2784f0b05fb9ce6b0a2d3bd4192", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-XN/7ML1YZEwUyuyCz5rqi8NhbCDIl2wu40mRKVJV5oZFZu91F0sWcTp0kt41BPOQ4YfNgYQ6HgzqJ45XiZcZRA==", "signatures": [{"sig": "MEUCIQCftruwAkAhKrBdw07duwaJkQXSOOYXDsqSpTbGbYqiiwIgWnWfNi/ZDvxqNu23PD8QE0d9+O8QWhS0fejbIx1V94g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19729}}, "0.1.1-rc.18": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "271bc02a44195573f5100a0b4dad911bf18101fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-70I8BSUCiFScZO78oHO8HELOv/4wUP/kiDs/LAAUV60G3jPjDqSfvYuIrejqiOzdj2seb19xFf/MtIeEHvsZsg==", "signatures": [{"sig": "MEQCICBZzksSdThn65MXWieSq8OzbPpSAbNElc51nnIqoF2pAiAr4Dbm+iHkJk09dvc2bspQhneibte9SsVv97k80IXhWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19729}}, "0.1.1-rc.19": {"name": "@radix-ui/react-label", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f2ef8035ae50532fd7cd4039f35aa556f14604e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-muUSASbnkeS+HIockuI0G1j16QkZICNBGlDQWRSTqLo9RnCIlaK39N2RRmFIx2IuSk+cEVlWATjm5Ylt/RIhxQ==", "signatures": [{"sig": "MEQCIFyay+KJyxTC9LdAuqTOPhepLqebppQDZX6iKCKkaaUDAiAGnDsEAm7yJ32ECBSpbOrXYEfcF/OKXJmsUEmn1YTuSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19729}}, "0.1.1": {"name": "@radix-ui/react-label", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c2970b19214248c2b3a0425c3c0d299290b559a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-52mHm7gxDcbY1+XuFwe0zBvUHp+JP424QC5V2nloPH9JUpCsM2MfviqA/nyW4nKuoGAeF6MhedjtlrXyze8DFw==", "signatures": [{"sig": "MEUCIGFaNAstW5lLKoyBbl2IEe98or2xf2hYlbOyDqvXyBEpAiEA1t3aMg1hnbyPB69QWxjBwm37Hm3m1uHB/LjY8rwS9tg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19679}}, "0.1.2-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "aad5a5782e88b6ea274dd82df85652fad7ff3b74", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-grU0Yq4KDJ9VIHyOUgi3WPmWjXCdcrtbEgwqibhmvYbKRlEXRy0L8cizTQuZp9GIhwIuFao+yOM7HQUcFbvm+Q==", "signatures": [{"sig": "MEQCIGyYPFRA9eeQl5s97ZYMxo4WxInDUTQaMVVXPfEzRQEQAiB+Kr/WZQiDiXUJKBY6AJ5JOVHMXEjVWFarFhD/QjAZ7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoT7CRA9TVsSAnZWagAAgfUP/jusZtZfA3vuG4RwKFMF\nyd8vULZRDgAg5doWr2M8fXasaSqtgpeGDFNy2lD6k9J9Ddq8KKn0bgqeUkA7\n4g/bRY9p3tON5zp53fQNZG+LoMQHrzxtB31ybnmdVz47JakvJpP8eg5xW+je\nt35yh8vqHSBR4o+BYFfVSRyZUV7yUoaM09hU07sAi/VMBPzs4bEPCeYLyRqd\ncHS3o0SQb+NJAGZSdFgJDHDQUgqkx/0xfBgVIZUedF98cVPKLraMdS75w1Vm\nOCFzpLYPn8iq4jJPSOOoUAzswMQavMjf3nKuEykmtOS/lsg5n07Jdlz9kkvW\n6kiZFVfjEY10OTbTt60uQvXlt4ZWf/JHzo0/7+laMLdrPsEzXwnG6PyxIHF6\nTS2maMxqXiQknQh+Zvj270oApx8en1sXYqgxwafSnS1JAoTJGqNxgFN4E7aL\nxbVWY/OqXRgHpZtB8dJ9DVQl8UizE+QX869aHHqzpN1kpTJtgJTq9IVewXki\njfIBhjXPuXxfkGNaAKadSKeJMoQEMFZzbKQ7uhWanEOJBDlt9BIOjFR+wsHt\nLFzK/x9a7vvyymlZVE450X7Fp5YDAv4XCSuYOzyS1Cy6sEM4CcqsNeEHZ3Br\nLeJri9gaKfbK8NYQfsYowuXYrXIUgTvTs0onh5g4Sf21fgDrHqqlHTpm5Ms7\n0slp\r\n=Oc0s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "31c33ec5bcea4454aeb2f7d5a49cbe6cd9cc752d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-UUAAI/g3wh92l9fa7Z7pcm24UG1Mq7vtbiCd1YsCJVYNp09EhNq//6JO1s7k0yVJJJLpigwjulHlgmajNsuNeQ==", "signatures": [{"sig": "MEQCIAWlqhksOxqo1tW2eJWiroSZ6Mz2UyPSFRw8K/kk6YCNAiA77lBWTQeNyHxQY52G5DuvzmLZhdMKkrcSAsHvW6mhuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiANCRA9TVsSAnZWagAA3GoP/j798tDkpU82+n5a5av+\naZ1ID6aXd9jFOWMOqtjGaJ20aIr7KgSQzZFJkjsLBsWvaWNyR4GnLP4rofx7\nhK5c7R5h751duPpitTe2Va/w6cOO81PqPsUk3OlxQRvzDyV75Sqr/GOqOi0N\nohLeEz/9OEiS3EJiWvu5Kbhe88kTk3iHnkNKWJUSf6p5+ZiBDjeBJ7ybo+NQ\nWz6+w+E85qvEPfe2c1g0DWDXk/pG3pokwD4P3OA3bAkkl9jGD8LEv+lQwnQ0\ntLMpZsNqSeLbtRUh29W5d/h737LBZenT02YGfBwiFLgdl5WB2G8g3fIt4Wvk\njgS5CS3kQIpd2bVa/Ex/9MBP0aWuxuwrqqXuqja06jG8Z/h4YQj+u4AkjaaO\nsty3pBxAc2Ns6G5P7gwvt6f39AYIbAWTUBf4HnjQI8BKBqTFHWcfjtZSHNQU\nceI86SPItrH3gjts3f+3xmh7AEnXH46X0igv3jssXSu6n7VBElpD40S49lOI\nZdDNsA9ygIeIP6xf3x30tOGPYgZRtheENmanPOpVWuUWzivJktN5+Qlp1Emd\n/6DSmZti9IifPJugLW9IGmcJVen5bVZ7+U2hK+MJZ+vBhzp9a0qbW903LCqQ\n0m1z989Ie+kI7CUbRx6rf63HizX4WEUUBe18G5pNHVJ/BrghKrqL0i6QQS4Z\nJCkF\r\n=d7DZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4bc9d55e47f5a1e3f492c3aedc2350434fb7ace0", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-kk1F94AOm5ARScSZu80FpmDKXaMZJtVFl4b9BmJdpW+hT00KQTRROT9No104NCobM36G6IdEX4I+X7J/Wvag+g==", "signatures": [{"sig": "MEQCIEu5tjwI31INClTJ8rq3nEHQfaWgVU/y4+6mrvTWznzcAiB5irT4SZSwf1aY3qjjW4T/7cTQAOQI0OuV0uafr/s7ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiN1CRA9TVsSAnZWagAA6/UP/RCHj1bG59Q29/wDZULS\nYv4BDPxAqfq85X8D8OVgasoFPHTr3VNWnOdZoks/rHaB95LFsJLeMOPDjnaP\ngWWiat4ecweg2VkxSAn8rm3Hd/qGFGdwPFVNWYihW2xlBx600bDaz6br9zHl\nbwZM4S1WbnNBPJYqXhLg9A/7FttqyKMRsWSDAiyvm1HpOY+WJIG0UprcaZzl\n7l0NZZn93nvDqfDH6y+0sU76mhmaU4cteXb+YDJk5MEkbxeOHDmT3TEjNVT+\nYNn1l2oq7FxnVDx+H/pNxS3QTXRpyUnhPYCvoMgV9/4O+SJ0pWvwFjaoeTdv\nU+OrwEVTRahjQ2TfZju822Ps/Qu7KD42qTRkkHCLNK1kGXcsGNIJwQRtvDB3\nVAzL9Q+Dd5/80UQG1JAE2OGfy2IMJ/Gt1XvRsdgJOSfzNNjsopTOABwqXQlp\n7uJzOqwNCbgbB8NVNyVApVOye33RNk7jhCJkRhbqyX7CvEV+IgoIbXhpFH6a\nPtuwaRm/UuIsQZRsGiUJ5SPvV16ffrlXHOQckx9DIZwMa+fcDW0UE+c1Pygn\n/omD6SLcOIYOfVB0qZjrnxM2NUECkA4pzI2bsxrl/fonhr3Z/8gTlOL+GNGg\nmuiTpugj4eXtVUDTYcCU/4cnMa5A2jLgloWzXuDQg+1H/RSy6ccviHz3TPlQ\nfShT\r\n=T4v+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ff0247e4cb2d694814fe2187b9dc7376718c9e2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-etdzUcbWl69juAUOk9UdJv3iWmiC7UTY/v1bP3biPqOcPRQ8k+1wnq0YL8Xy1J54L2lmpiG5BZW4M2nJW7gFtg==", "signatures": [{"sig": "MEYCIQCRcZ6oSDYz0RMBkS2NAZFjhhi3GsH+i54QeVymYzOCwgIhAL3M72SagVdZzxQmUjdP5MTwcjHDllMPcJdMZLTvqcVH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryi8CRA9TVsSAnZWagAALd4P/iHLhqWjnesp0x2YcMkN\nTnZtABTdmwvpOJ55mAIo5Ll0El/j68Lm8JcbTHNP7Eq6m7iBHWa3PIiSck11\nJT5ikKEXTPTdY+YyGtBR3PdSsW89uEwHRKMzjSABmc/v+d51XmfKCiFSTAOA\nFpCaIKVGKGqPh3WcPdgdHlwDW1JlthELj1Cl0Vl07A0X8VxY2shzg9+VeNt9\nA0IVe+OPa07v+vITpmoEXKtXo/T9CSQGpP4YSv2FN6T0bEs6DGGw/Wa9Ax0+\nOAqYtKUTO+LgKFN53Q4b2tOULZfbFXIgJf6ypnd5BBCYEuEGsHrCyrSwLknF\nqcyYEAhL5XtdXDjNfaLuu2yMRT+YPMmc4w1mrm9Hm0dz1iKuKJqCq2Wr8uGW\ny9neXUeWlwkmw78WRQfN4yfBD/hNxxNdathdd3EY0F5xbL0aHvvWaydK1bJp\noOCXKCW1BrrPuJ2oNtmqH6ihG/2sP9xL4zlFbnrb7ez4boOmXf4TcdoIWnm4\nfeadlihVt+3hyatXID5aeY8F6DxqjsPaFScts/pmSdiC5fn9FH5PguOHYf3A\nliPO1IyJhUiJntS6X+yqxe6MjumYXxx2Tddd718joaRi/JhCyaCoibAxPfx1\nWe2pVz2M6dXtuagI3uhIBjo07+GkrkHF5X9aMfaRReM1vzP2o9vhNGaVP0DD\nvqub\r\n=jEDQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a082e7e456ab8400829f7930fee6fc50a2330761", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-C5KhXw8dzWWy/h/sRZRN0DpYUJh/bP/W4xmQRHNIqO1Y+jNls2rgBG64RIUO/rIVGz44RXBr1uM/qFxWisll+w==", "signatures": [{"sig": "MEQCIEqc1hyN7OX20pg+UzIcYlw2um2EVrdFRqcGaAahy/bhAiB0iF5g6M2y7UPdQyqhVEOWR34TSfomZCYsdfLTy13a+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRFCRA9TVsSAnZWagAA7nIP/2k9w12gMz0TtO0HunZr\nQTGjZzSqcrW6UkmS+COqDX59cBIUGRMIvDv+gzxVyoKOTCp15VK+ZuYrUnhU\nrFjI9ltiNWAEJcC5Aw6i7SdShtpxOuQxTRXxwHzuikjsZF6zsS3LQ/H7bezS\nbBVknLTec2A9Y+kfRRmmYl38VeKuaJwciYafJBR6FWEJd5WBRB3Z63GzRo6/\nZMSMa/A9lhUr8guZDESHSCrnDv8gy48+IeLmhyDAByy2wchDhGCy8qxShuT5\n/ilQO/Wxn24CxqiiwNlhlXx2q918GI2D3D9zoLRnvlrJgAyiTk4NpsvDpCEo\nIuENE+tjse+QKPSZ6wDbFk4Yvl9iTQ0Xsk+8TNEEr1YiYf4UYzfHTR6oSZB+\njW0FO6N84QCUzaai5NJcpudkFvhIosITlZcOP3+DD5PQNIlftuZP8gx6tnVD\nWqgeMT3naqVmGZNXxQArWtHsaqWNS1eUpil19Gj6uOwzByOEDUpB0SAd5idQ\nx21wGgNZ6695kQz3szxbDEbsXH8cm84f3beSkWYNbNPqGrdjUf0H88pF0YC8\ncDdvhy1/wuV7yRR6vJ2EllCFeN9mXo6wp0K31YLO/Ryf7KdA0Ky1CqGEiSTV\n6NaFIqB7wzs/ezyv3j7xveWgmgKWUp03vDTTqaT0h6HXURIepvAsYedl3yKN\nrj74\r\n=W1k+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-label", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "66d09fd8cf15206a69586ca4ff8fce174891d608", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Me/nu4eaIFVc564xgUkIiemMwz62Eyumro1+Yl4qAShW3nNHChk9RscGCj3hOQE9cW1tu3erpFHjnOxIS85HiA==", "signatures": [{"sig": "MEUCIQC6Hmr5EiGC1hlWxg2wUi68a1Ttib+Udl6e2+8oiaKkVQIgLm4R6LvsVXI/+UkzKUluOLWp+/mxOp+r7cioewDT2uQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr42WCRA9TVsSAnZWagAAwlEQAIxuvYe5TZc9sjMYG6h6\nOZI+EYRaBNP3HbbM4TlUqJ2XgdD02DKbfGcrsrKZLV5iIc+8i+UNuEUEhdVS\nvX7mKOyYNlkJgktN+PQP4eFh9XTf+yEZIgw0IzN3tOCvslNbzbKnmm8USjMb\n2DK/mmh5js+mak8PFTreTRi85c6JHKY9jef98a2nvImfiNNMLqnqfgOiesQs\nsfoc0vJZYfxgpbx1DME5YlADCrk5t8asMMT0tTFWK3UCYgwa9GnFQxkCreNb\nyqV8Oh7t27YfgR6BYJCC6z6Nw3vAxl6QIS4vdT17ijYfCrcERMQmqZIBLKkW\n6n1eJKxENoVHLhmc3AuvX7gbfFKW4RwxM1R20KmkA02VZ6s/YHYuDKOll2p5\ny53PY5D98B6RZGfNoNqwyDneZwqB/S4IvV22j+NcPkIHH00qoAlBYs/Llu6k\nmaFZIqgUPuhrEOPCYfWpx+AwgvZXaW7NxT7zvdii7Cds+fHe3Bb+8Oqu5izP\nFs65qwVznAnYDYm7KQzWujkrXmzVcbWmUyjRtdpxCBkvRb7n6F6DeoDZPpXu\nJWI/ZDpZrOxYurVQ0e90cxW4Ns13U49M46VMHEXYstFnxS2pZ2sPo2anpslS\nHWKoVQkGzalqEmRtGotFrIKyimWBATF4SmhPTqnIB/N7cUbooxOeUPcXeI+Y\nR61u\r\n=HvvE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-label", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "58f481ccb3403770971043949bef6263fbd94e9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-o+Zqmci9UfbwlSrgacAi64aeKwdJ+oDL4zdQUggnKo2U2O7y4SrJVWvtKJp2DRh+mKtav+jPYOI1SKqcCoxQ8A==", "signatures": [{"sig": "MEUCIQDZFbwsfULPqviS4nowL/9VlMAkYFpzvX2WYxI7rL7dcAIgZwcCyyTWatQI8Be4ye2MsUHZOiaAw81E/gVHGGaNQ24=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDvCRA9TVsSAnZWagAArZgP+wTxapLPCNsPSCHLrTjb\nVkw2ScsGiAFYJKIzTlbf2y4LeIpyr+R3lXCqSGRO1qsbbYWKTvhN5cBZ8VGf\nFlaqgoSCcqHbCoBHijPm/mqQ1QpeuSZQonfKn+Kc6aqWB1LYXDyBSUcrd/b0\njv250J9JYxhxKOdR6PE3QcFhQoMrXSCs5bVaucCvOJLF5UNQkkB7a3tQdnD2\nRTG3/BpMTW7Gl9bvWdUBwTfSQ8ye/8mP4MzEqA/xuta4Dm2jshur1fjOTZWS\nDnpEYl5dQ3QEPZx48QXXQDLSnPvzG6Tu+l/9S3BnTogNf8/bHGOPBCWc0VUe\nhkIVAI6uDX5k/YG3Dee+Zfd7WzBwFH2Vpgbl+S7LTSEML4Z7K88pxoRbX9Ll\nwj7Ls1ev4JNTUTKdxVCj8rY5zEUBTHqaJOS5jWjNQOUB+fKn61iwlNucnNUv\nVTXQkGGYv5sr/Wv+jklWOCPBg7X5f36XeUq/aoz0U5UxDgFPn0OgUzCrAitm\nyQSl0Hr6bRdmj27okAJR1ALkqA25D6F9hNcrbMOf/oA4e2Cil7UQc83I0w78\nyze/AB8OrMkkSFqq3HdnoNWxczw0yiw54iPoiZILh9W0+AJOShWyLfsBZPlK\ngpxPu1OdHkPDKyu4ymkdY3AH3PVcogilyqj6rNElPCl5Hp61X/8YjbQlO8Kk\nGLJP\r\n=p7d4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2927047f6ef3a4b60da6bf26e2066243a42eb174", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-F9YKBypWyzzMMCxc/9vPIJFT7U27wLdhG4+yKM4+3aSGNKD7VS95uC8gXOtBdHcBVxMKg51bZiCa21fisxIEpw==", "signatures": [{"sig": "MEUCIQDlaBFSRbrv/Xd1jcMlfgmtBhjUBdRfiOHBeDxDO7aZOwIgcj42Mq59O0Py352fVv479aTAdPSbuEeNpj4PSMaD5Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszATCRA9TVsSAnZWagAAmbgP/0AcBcIynRovsjBG6StF\n00j90vQah85v/dZF5Y9jcJnW1Bk+qCDb1i9MtGP1aZQMI1b0+aPLzMoOnA8o\nHUZ4t0M0GaNZEyzRDCyTtI/FqLxKqM3JRgYKKVhF6xfxClv0gt2AaB8MEh16\nkTKuXdsv2R0d24/VQRqVxEG6/69IclQHAB2vVHpW1udDdqr90CvllZ4KwIjB\niFjc6Pq/SILsHivEJvEbZvTuROVt2+FynQDilz/L/5gWzAbyetm8isa7q7Ew\ncRk4z76dsSY5wpDPcS/UodDGYzjQ9eV6PkAhiVHNwGvdIpwxQuSNlJVpZ8q1\nTfgEbwdC5oRuSRE+MDO30tieOjIEj+1j5ifG7XvoSO69kFirD0Bari+SpR3T\nrgojOYMIi74ATgTRf1gCxd5f/iJ8iaLwn36En3pzHmRfVXC09ZB6XN4eNRLc\nzHrPgJywL5s4hNfiewRJry7flSv4s0w4aaoMjP9YHTh0Zg+nUfbFOXR+LDub\nveU1BAwhbMUb9BwZGovZvcelWBKq7OsXcUiS1CIbWHAS5StSMUsGYCUm2HnD\n/bl0rBiRV+VSkvI1wIE6FK2wXwrCYfGvhRJ/j03tRqlzau2/NFb+2pQdusFx\nALPF8VZHqK80iYxNa3ldPRH3TJcIsWUmDzu9hn4M14LDVOe54KwuMqmFH741\nww9U\r\n=ZoHe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-label", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0140afb3e75d240eb0cd5279ee22a6f491c373bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-9yromBBjHkWDy75GSZPEYPjRKovEm35QnBSBQrifvgPDf6wnifoSuHoSJyIKpc8nwXpN2v0lJrtCQ4PPhcS2ZA==", "signatures": [{"sig": "MEQCIEN4fgw7AC0a82t0CfjVKbf8/M8YCMw9A/pHqYheyT1vAiBOpxGh2MqmyFEeupME0ECKtjR9WSNH4LiyVItCC3yQOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszr2CRA9TVsSAnZWagAADZUP/RQubsn2z9r5SfnbnZGQ\ngr04neH7wKrgTjMV3jFT7HSXBYRRbcLlsuR/0mSBB1YJWPUd6lDM4wknmfUy\nr33bz1aRcyQdv1co4wPw63zZXyaakHxZ5iPF5F9PePD++FDs5DJxNwjdHYVE\nt+cOYxjoEl/sLoiZN+roB5DJAAsZHGSxUq/gGgODZIkYxxou3Ypap3ivmWt1\nweavzhSmV9hNnh7+xTJM08ufAeYqCdErZ7E2wyaoKN5J2Qj9Jk8ZghsprylC\nq3hbqHFvtajxqHqRiWBn71VRCmjzg7sWfP3rdkQJYCMz/x7qoQNRCzd0ZC8y\n12cN2mlQgzd41OJHbaVAN7FQzk7sU80pgTM7hccBIO6syh1zjY03Mswqyqpr\nQZYaPLLLHlb3aHhygERZEZkYYk8cf691C93orPkPXbYN3ZkEotUvM+vkBrYO\ny2vafK6kf0JvH3QZ20gCWr0C4K3hmPoJNkLj00wRNKYJblvoJrfEZ3fdl1xv\nyv3gn9Gz7lNHcjKQAhn0fTHr0tKzLqq0OTt8kdeBT+6CqgAbZOK7b+ikGDHI\nQ3xcx1bA1sS+NwglGSR8kkSeXA1O4enScm4IbZqeivuB1OCpCf/6oVa0cdgp\nDLr3fGxu2Qnjho1YbVapxRGGYGldyn8xUjm7W5mC9StZNKomzhHVXk61snTD\nvIHY\r\n=Sb2w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-label", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d62855ff6bb2950d6117462f8e00a0c5378651eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-I59IMdUhHixk6cG4D00UN+oFxTpur9cJQSOl+4EfSTJZs+x4PqDpM7p402/gb9sXJqylsUkDMHDdaPzLwPNf7g==", "signatures": [{"sig": "MEUCIByHliKOWBbsjLlyIkMVwUO+wj/Azp45+oDHe5rTB7MGAiEAkV+sOFc+GAfSglMabaBmwV/b8p/E0OSoPta6MuM7aMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiiCRA9TVsSAnZWagAA4UwP/1oQVuhT21a1ceBTX5cH\nENDPdNolZRCbRlzrw6/CNYpQOK1x5CUY2qnY7GHVPAbmYIYN59vj2x6cTAZm\nZvaZHIKsHtocVpvlNic4m0+vrKuqmjj0PzElTDaxCKF4NajQqqStRV4lpf+U\ng4OQdvQ6QMaSRMgr/qlj7HHsNMIA3d7CFj4X0+RbKK3UYBac8SybJfd/fhOC\n6ujmtfwZpbW+MjGcYeH21Luf/TjR7ILe02fgddpMkX6wCfdTtUvOaOFy5N+P\nj7xQu2R78Bbn8/v/zlztPRHSnSMa+D90LCHRMY3btExTNmK97srGbkfV+IMo\n8qFwcJ747s2gg2xyXE7gHCfUHRRwe7a6Qcb6Yopy8iOJk4bSNPkbgt6xqrms\nqg2YV3Xn3mmxaRupUrvqzof+eAkh6/hulVgsqoVSCFCO+rJv8USxQF0d8KLv\nsQCuyqS115mNeD0bsAqcAsiO0bfz9fgW84Pd76HY+aLdWRhP6TZzNgnF7cTz\nsLvIxF1hmFj6IdflQXZNcpXcSra9povsEbYU/4THdmtRRZvuKUx0rt9z1AjV\nC+JXbnfoi45S5jqPMjgmeAo3s+0ZraQUrLWYnUC+W2TvJUV8SrSkz2B2FsnS\noLg/SjjNiT0aHo4KbDTmZQxaUEYTOkAC0+a/7id+GRzBn0nRG7JNCtvUacnx\nW75V\r\n=2fg1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f6ea42da29194a4196fcd9dcdff51f66f72e4230", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-IGbR7A8HKXvCOWySzc6ixnATk09ZQd/N+cguhxaRr4DGZmoJEsFKzqRXWSX3cRKJEX0ABRuIGvIuJuAEOLOUKw==", "signatures": [{"sig": "MEUCIF4yHpPRN3i0En0rzvXdgs8eWR1RnSfnRuXF5A8i9fqtAiEA2IhEtJQ96a9Dw4rPZqZyh7KY2AOgcQ8arGX5Xkz9olg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLjPCRA9TVsSAnZWagAAkjoQAISQW4g/A+aMhiQdnsof\n3uQF/D5jTke9eYGOFiWSvIB9N8FX/eqKYnKN8cdgKhrLpYb3fnwyn7TwsjHs\n23TUAsTmxJ9upDL8/+8i6PYnuWYUXBA2VBM2feO7doqWZxo38Wp4Hl3F96/i\ngxt2dl/ZYlLChcDzZzefG3oLdmcgEuRLWAykhGZfH/FKl36Kxxx/Hz1x9uJv\nyDHAoLslVhrhb9X/4xUI5HrI9hA2r0ScFAnFf22SbGl0NtqZ+s1AwspwDuQF\nMDshfqZN1SvYpRy1a4gMvo1Hs5NoEDdArDWYlLFCo2NVifI8++KMoc76kDoo\nMRhMRlN4mA1HTZ3mC1YnsRfI9Fs/Jv4W/mw0dDQQgZjp0N1P+4UdMm+Gat67\nJKFTmWH+8pCsg0wBfIV7cYfZBifVbGjxqHoVS7EvaAwu0gUx5tI4q545FbMo\n+gSdSNQMuD8T2W0BtSgCu1s1C2MuV9RkI7zuX9PHPh4nzV6Og5rNHpDkjsO9\ncmGVPvg0tjmByunat5jy4KF3bZPFJmelO9Yo/HlxdkcPFsx6xeOPAsRoSIgG\nZK0GXnRW4e3QTiUSIAvKouDMJ9Ym80rZP0cwQDjCZSK/gbALuphoKb/YHEu1\nXhww6OT8d3ylOCf6rjCsEQEauTb0cpiT7usqjc23ZT0DcD3d/wjIC/9wIBBN\n7f1o\r\n=xzKo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "890f875a89ba18b6dd6e2b634cc692be0663fa70", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xZjSzhW9+ScTaCsmkkp7E4lGknrWWvowrsT8CAbs56YYwvgsDY7GDsKvwCsZjDePmBEI2JP7f8aM3BPL9f7l4Q==", "signatures": [{"sig": "MEUCIQDiI62NQYLHlfpqGtNvPsqhUqLurEd21cgKHwoiUOlNiQIgQP/6PzoHF/lj9loqrsP9E5ywZ3CNI5mEUBeNf74S4Wg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31q0CRA9TVsSAnZWagAAvbgP/RsjVVdlSQZdx2cRprp1\nLMRs0+uCbB3/p4yv1JYHXrt1A2EmLTBbBade9uYdyxZseu1p82hn3Ng/jVZv\nEhQKqU1YjsoUOYCbKb15+9TWjd4H1MCUBBRasQK5YQEuqBBDTJPkXBu71860\nwcTeIneAFZnxECF4/YAO+QvMnz8TQEAjyz+SQpTLKUsyd84bi9fzaUyhYd3E\nn9ZaKQvQ70vleUHBmoLsKO7s/kSz3YWdx7qwgBfa/uvSbeeiSFlUcFBeHT+f\ntJViFnPNsD18YKOqdt19wsWjgdu7xU4dkL8STQsK1/FPsyosvy6BhWbQmO/O\nS4Tu7Vt35680rN7w+HRxt1ANnW3t0h5JBjrC+O8zbSThiwrUi3v8dYAIsji6\nNDFbMyOIdBtUQDyyOQf6PMgT7XliKFJ9BhGScEeTxad0+GceKZtuBktr7u9t\nT7pYeGJbFZR9qo5D538Wiv4YmlukVC4laHgd+heyRlxHQ8IcT1YOT7YzzGQH\nKqh9i87+Z7kayzi/3ITcVeG+aJIdzcalcH5Fkn2kBpoIXI97mYgC+HnM2in6\na6VRBDv5XRJ0czslwbQfZWByVQXvqsFAufAxdFgObz04+xHT2ieuXUXkHB59\nSfhfkYtGx/kljFue5cRGR5YusrQOxzqRlzC4aqxUstmX2cr+9MSMaS8VrsaA\niC5K\r\n=TnUq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6c474991f31e5cbb961ed1747c8a3695c9f9fa4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Os3tYMJ4+9j1+t1Vv9MvJYLHb9go4kIeK63JiQxj0tBAWM8ae4CC0ck6uCCdbusTttVwzCdBZfU35KIQkJm+6w==", "signatures": [{"sig": "MEUCIQDlNfUlg1OJEGEkXF/+afXAqS4FOgptPHhR9+X7HwJ/TwIgUI3mjXco0t3uMPejSpvG5+aE2S29kgaZNorQxJzlMCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BDtCRA9TVsSAnZWagAAivAQAJhg/2+MgKmtK4nvCRHH\nsID20adzqK9QiqVUCnU7XdjLIhCKeM98nEoZGfijOEDk5nuS3bCK8oVuNVfl\nqnrxcrDypKTMJzfhTGUrq5T6YIcQoktkusIe9qEMzfVQ8H5nsFlSO4LYxbV1\nZSbXa/o6C1DuqdK4sq5jVwql5xnczcjiVJbZ169LXgwSpyn7XmR4pHJdKLEo\nhWzW5ubjlDXuZ0+kAi2dPcpYQ8bxWEz685/zrb7IIY9Q4O1wu8/9pui9h70d\nZicpnbF6hRcb3YUIS8xuTxiO6MC76F1uaQPHdXGIFeJWPYn6XGc2R0amhnPQ\nAIwOwuRybDzs0S/SKFucOfMsiiWGzrt4sSLycUSD8QMydUDpoMDVR5gZ2B5F\nHmlpNVNTzisji/mgagS37aLV3ZGAcwt+oGbMefylYJDTzsBYAQX3xrG99wHk\n5u8ff/+uEbVbdy1mWG/yJp0X7PR+y18BuY7eofYu3Xg7Sjv2OWJC7uyhDSmf\nYDSNl4AcFkpvXKqZeT6V61oC30XgM9bq0JPDchoS5+0iwvCNf7x5c5rZj2Ro\ne0hqYrE/IZy9ZIa6Wq20LAr2U7NBaxTg6ztm6p5Z/dog6d+kLUWw5YsBx0aw\nVZunOx8hjHFJsXQH6H7UGbIDo1h2+QggoLj/SI3WEX/wInlKLoaBN8lEAVnG\n/dLb\r\n=FruT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b105140026f1efef68550b88a2d9515bd1974f82", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OFYMd06YGgKkh0aEpXM+B40LlXvB3mGJSCDXVcP102hhxdCfWFBjaD03n3+RNpVoB/MUVFq9lOk5y/mLvnPcxw==", "signatures": [{"sig": "MEQCIEzJZf41/8R6sNOW4Mjdxt5yVENJI4JrqhC0wi4jFMv1AiAPEauFE6j12ivbdFoD7Jw5ZMAfSJgy3zAabGHwvcjR6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CmRCRA9TVsSAnZWagAAvioQAJX9hn5cI35cGWIxepb6\nvA0qtjT/oNXJKLbCZk418QSM+43yxcWd7Mpc3eNz1XEIcYnGWLZnfwDZa2w+\nlUANdmKogFo+pB2RR1SwJfzhj4lTzVQF1z+qxZnPX7YvH6dyTIzSyXet7Vnk\naPHrjvUBSCXTYCyB+/aFwDW9h22WXVP/Px+TcGCx66iBF138jasHJmPr2kwa\nXD5MiWDJCSY6J/AY0VyqynW/+3GwTnZ8ggwiM6hC5/9xHb2rOGN7D9dhw4qP\n3qRcqGQ9i3IOUXaaMudf3MrzDFnWUyL/X3ysj2FzFcKhg6wCTjmGNBvy96kO\nJCTtwKjm5DvxsH+D5ekX0Qaibj0LdA4OmflyzpY2+/n9tZTWO5EPWgfizXt5\n8yftHLhOzbMEPxnTRL9qmzLC/wwbNROfRrx0+d4RbbycEmPrNrN5qWemnI/Q\nAZ8c44PUyqKViRqqKBjX8Nxa566ncgDUaR/nClZGADfSRXjq060rCq0+vFZi\nX1F6EuxSnhiotn2zLKSq1XQCvxEBeXIJWRGgX/7inoXBJQWzqRfFTXFL4OH6\nkHdjboRrv0CxiYTUOX6Jyw0E0akel2vCBR5PPiY1qPxbsPLtH02N9twLTAXp\nB9WA4l7AIXjOKskq6LQgXtmJbyqTJTVOdGMtgd+1SCEnchCeir0TXgmmH6tO\niDjR\r\n=hXHI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "345a76b4da6c8ac96b7444f979beb6d83898e5be", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-zFw5NrBP7Y2rDaRTqwBmBgqR6WmBxvdhhq3oDigXXTLnuuAEoTXgwqjN/itMiJm/TkKY262I/WHSJRdtkVMf1w==", "signatures": [{"sig": "MEQCIHxEJxPq25WnfEsNq75F+E2pS/N73yUOoZWIOG0Vnxq0AiAu8f8HNe2uM/5uMegycx7TUNk0R8TXJZgReBTF/VpUyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GqECRA9TVsSAnZWagAAtvYP/RcxOu63rvSQvHGrwjOQ\nAIJZXGdiKbmrZmaIdcenSYz9hvr1Bg97KW4mHOZux/7EgB2yBbpx2gVT4aap\n3QS02ZHcrlV0h9INnqYJo4cR8CFBWQnknZ4Mb5nfJjgj6AZR+Ff5t/phBkBd\nCYq5ZkoZWMmt6nnGwB7QN/Y3W4M+lMZ8VsRzgYFtUF4sGxvMFf8dTYk0pjLZ\nxKDVqT9GA9BQCKMvPODtXolZXopWTq6WuQ10MxXCuoQkmIHuZ0BcUgM0CzoE\n9gRON9/FC1Wp+10tmplV4WSqNqDSKphCYfdqWY57eHN/zJuKgddVApKOlxm5\neOg6EnYwQiHLHJb9vegzGgK35m0vEYp72Ipy/zgf5AOIf7FXG1s96gsFZyc3\n1Rz8DAb28paMJdIUaJjImyNXtlUg8zoFJMr2IwjxCUJC0+3dtQZQwqgoHHLV\niSvlkMF7fzFVFPYc0XEd2tgB4H0wOePjsKASfBkuOEMt0Pb2dm9tGGVghDmT\nsyrrU2MIXZSedx1eL78RI30CxXGnXDTgvCyM+2e88BiSHfRSYb7ZsyzJ+haO\nsNyK51OvqnmiJlRaQ5DhkcgljymFln+Z/coKvQW7zH/t45hNB7Oxrib+Yr3d\nQU1mxC73sU2yuCgSIAr2cqz0oDF1vubgIlMj4NreN1/xi5xv1KEjqkD5bz2x\nuvWv\r\n=3deA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "22b3883f520094317537f1b6eeb2c7509039ec3a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-PuT4JX4SEYQXTYIDtMj9ioLhyxul4iwFFc6j/1MAqw6MTwcZxGuJAxwxUFX6b9lL37/XPu2h/7Tzp0bswULt/w==", "signatures": [{"sig": "MEUCIFZhjulGBkzXaQcIdALkSenRytLzl2UpM5w9OMIDNxO9AiEAqFQAEw+Zv8s9DQhXx7zm505RWfYlktpG7Eleh3pGhbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZcECRA9TVsSAnZWagAAKcIP/1jaraWJB4WWOJqRYVFy\nwv5ko4R3iaEJsyqL6NM/qGVAMsl54YviVqzEhlrtXtQINdzP2nktonlXAGmY\ntwEg7au1ZUC//BYUSWqcinDHGFwzH+cikBlObgpPSKIiBe4NrI5YxQIETZKY\n3n9bhjHCJLvPkyh5goi4FGACxepClit248DbiArIIantiP8hd9hrERK56URB\n62zKNmFTVa0lX9xeAdH2zU1datUZeRIClDCpktoYy7Rcqh14IR1Wj7Pd8xKc\nXdCU9IbFO7k/T1BBiCzmdtsQ3d80fH/sYaYXYhZ6zPsE06pwtw4ZwOsuGL46\nEjSh9c9rKVfJRCYaPDSNQzHgMtBVHwXlaT+VtHyqK4AAtxBHxxAAvZJUBLog\nvlgYUAzxZ4tI1UgcC7Lx641orgyU7rBgzrHTwEaMFLqgz69fTui7FxuTsOYh\ntwoEIR9DPLIEW76vmzWr+vUbDDS4zGjls1QVCjgEKvtTJZZX9LpeqOmgGCzl\nYh9Na/Co/KNr4bOh6bTGm4gwFmFshGeUcnFw3Fl5UkAaXcMV43P4W/1Ad8IE\nVQ6vGfEshD3ITmrcpC+Uelm9qzThLKKN2Wh7uYuZCPNs+RdfutEWLN/vkI14\nvEhFJoShGsAreznlADxEdyOkVtLcw09jcth8dEy7IwKtufF0SUQKy7TZOAdM\n7FKX\r\n=wwij\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "21d5273b5a0f474b53031d6cb47144a8bfca6039", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-zmywn2pj2GHlEbFO3cYHyqeALbVfl/A5ru7qfX83XxvuTZoSK3UrotnQh8/w0rNTHX+iFqyqAymMqT9TlSAnHA==", "signatures": [{"sig": "MEUCIG6ko8zmdoNMtz3BUSxf8mIJmtTbNZsd0/jTDhWZH0+NAiEAiL4udsg7Tx+zX6sPZ4JnmDSKE3WGfyYJUlYk9pkLSJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YshCRA9TVsSAnZWagAAF74P/A3tXGCV7+DKCpkuTddU\njo4FCVJ0rmaE4UinP8XPile2o2i0m2/rwb0VVp53vEWUaQF1s+sFVGDgFLuD\n16PNKLELMKG1gtG96tud8p8VRwg8gQsRqZeo0J2ywCWNRGPELpLCoCxwi3lj\nFhy8ZrDa02nXXw1ungx6Ijf2FCpZfPqwJm5nDyWe3g1Kxa3dTyII2NtoaA4m\nzTmBmj5KAyOxWCcozD3Lynkb8AyHTmIOT/A+9B819vWPNN69hwoJpYUN2nhQ\nhQyY9HrrxC2BOrZH2rIeS9tgOGl8+6piP7qheF4oC8bDqv0e67MJeGtnzC4y\nKOBAdwmx2S2aSK6irUM3sNtMQQSx50y/d5r6Zh6vWoLcYJGOG/caVlUXSaIX\nhtyX59zCpMbNx46ncWwEvfeTJHcIPjWUhH7W0q3bDLtnHdiCgPznv5tFPUVe\nF2k+aTtCSf9Kh2fNTpI1evwtOfyD3TzJ0EsE6T1CEhQmLFbsCLzFdqxRwtBb\nv/c3PBJknbwhjrtNGbAZkBewG+oHDKFtqXgs/TI64R1HJVZThMbyi7BNQ67K\nj1MKO4ikw87brK4j6fSJBt870OXIobxMz33C2dpRIB3BTwfC0ig85namHcLb\nZfank/HLjNfBL4n6vPWLNscRAgq6VI97pn9w3vQNF3XgS89qRx6kYmxDHRIZ\nhTKc\r\n=098Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "98560683818e2557b622a26720ebcd3e265e980b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-xlpu/sT5gEkiY0BsrivO28xJPQ7LM8mKDyI3TKwx6UetaKss0HeIv65UKxMn+tFA6nFjIk61YX+v5Arm+SGZBQ==", "signatures": [{"sig": "MEUCIB8KRlj+ZvfcLl/MZ3ERE+dXu651klgYl+OJubNZKOzrAiEAip+5DzFZtmsNeIE0wDYLe1t+IcnH3bQ3CMbZYpRxPxc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sc+CRA9TVsSAnZWagAA3cAP/AzahKzLN5te2SsXWinT\nzVXerYpueOrkMEunPoqoSehUtUTYx/XSanPV1UyCWX1qNSnHvOyVifHXtsUZ\nVd7g5mGWYK+ef7TBR4y6qHyYvGBNIdr0/XLlS0ogrccvOraQLsJ80IWWRUbK\nE+KEQeX3yePmWOy9+YUYoe8jNRnLlntgzf5m+MgAUmwb2VyXxvA5JHiWf/yb\naLVJ0kOAale21GU8dYW7QLNCA70xgMQb5gXwIdtXQtNBDCMlfoUO7zqFyJIx\nmHQgRX9aMxovcuWdvo7Kio2MGWfPHvXiOBbZc6EdBAT0R3eKmwdzHYPRar8h\nFCCa9r8eqpxufEXzTXvV1nTOPZm7bySefpGkk4H5MqMtzVLId0McziRpqNRn\nSkBRpVOk6OLSaOpIvgt/KZAatD1UHCZuwKM/G+bhjLYsHemoRGpXi56Q2W6X\nz8tmQHRjyq9iTyxDMY/iqvvjDjCJNgHTP3He4DFnSt2ErPew9MUCkZ6Plh8F\nJaNyT5k1c/MI2qhncINM51tg75QnmlGvT4X2QB7MwbvK9QzAfJOMHbsfFy4n\n4+X1blECiK0s7CTiXrICWeXc83V2JjBVGUuwqQc21pbEINuIG54qLAAN7RWu\nkg4z+PovEoqesJjgi57F6BXWUR4xX7a0y8u4zd52yqyk1XjS/FRBLsuMFgOd\nHFeq\r\n=1TcW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "888998674cb377e2087d7c90a23ecf85a39d4fa9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-m5Cz2+yUuMfF9rYr/PWDFz/DbErrWz0+d+LX8eCaNiOwWQN1hhPRXLXVLSuKsXr/2kTVcI7kRSUrd2U9fX8nFA==", "signatures": [{"sig": "MEUCIQCTPXiLpj2b2mXiP9PtdJI1Bvq7rLWUnbnZdiIaKKUr3QIge+AJxMMcXV6sDe3eEYqX1ySksfNyuGz3kp4i01cSbJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xC/CRA9TVsSAnZWagAA4GYP/2EKn4pp5+H+ZMvLO39r\nesqRxUOUeTTmZ9ofGjVjmnhax4TlIR2kRnWB7BqLFJdE537CIJWMsPCnCEvw\nxnIr5nEjqjoFXuQ+Aeyj0ZfbW1y4pNxUwWf/VZByu5vKZs12RkoEqCEApYrz\nFjlFZT3hcRtK9SWrAYd4Dp5cGPunrwnx/wFXOPEpTh/O6755HDMOd39HuSJU\nYcHwRGhFW9XjX681ZDGojanB43WTBZk5hggC91wYd20XQ7pCHG7W8nZuqqtJ\nODD4AEuMQEp0TM7YAhuyWP4rlIZsmSgHz9NN543tygbgNPwbHk/ekZH9e0qX\nohUNcjkJY21QVhqC5i5N6sE/x04lXqDx4A7c6uP9WcdKAqHI16phSvJEOBE/\nVYJRTEXP3B6HIUWeufNYLVGxT4OzzomEbK3jXCFvefU6mdBjs0vLwJ88Q+Bt\n7OIrbJhe02mqbUYiSdM+as1uMLD8rMnMRLjWlrLT62IrsIpvnxCCDdft9Pxu\nbiCEGLgQThNYINlkl8eF6GTNPLUr1cpqP/BHkzW2nIkDz/OJHdMYe5LBNNvn\n1TWk9B4QRjTW0tjstEDl2U4CvLUIOZ6exOqZdWehQtCvMVU+kpqo/k3XoiJ6\nDhQtvsO+c9ok3lL06mn8m45Z2ecWIj73OYuScWKx3PImhxiQfAy5Wp0u3F5H\n8hg/\r\n=dkeG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d005bf0b83789b0753b1aebe581bae87197b4641", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-97jqH4kiE4LP7TTP1hgm46bVK/nUwRU4Tk1yERGZTAeAVkBWMs1fxFOUO4vLzZc4DZow7sgxuiI8IHmrlowcnQ==", "signatures": [{"sig": "MEUCIQC26/Ra8cJ7tlfsq4/mIDFrFrfc8Z1Zw8ag2EGMXrygBgIgfey/NFQS+39I/9TzEp1ee75UKlh5PF2PgpAU2ADJL0k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xK4CRA9TVsSAnZWagAA8NkP/0l/uLuQyAetDsIn2E0z\nBV3+Il1Z+CuiaXw3uIVmcaYa4/0XX5ntgDl2L50lvRs59gUcYSB14l7Gu7Xs\nRPRzPdFip5X4FnR4nloR1Q3fShBIIiUdk6aJYbeVgp4KswVFdg6lqRDQu857\nJMyRFSO7DJjcfwVeQ5EvulYDJwvdEaGixsf26ydP8Yv3emT1QtmkgFv00q75\n3B0ezSrVYdMFvmGgrmktrcNikUMiKwRIrm/YYIop6N4AWX72Ewlr5AaKWBWO\nRlr09QqgZ3gjoCYr4lGwTeenu/cLUVsmgO93KIbrUshcfaYhT+HgHl8oz53E\np32xGLsKc0Rk/Cwf9Y6uaJfvCUuxzF36YMGxeMC54BKONNKltlk/IxMZlJBl\ntxENP+2hZ4vDVMoiM1CFFK27+NFJ32RgR/09QmVLiuDpyda/GBE829AFER0X\nikcSZyWdXiD/ASwZbHEHBcsQlU2rpaOERGVZwD/msFGEUmxqKXt5HcOmlcxO\nlh/5STaq4JRGERAkYPMIfGXzwfgMop6jycjkJo2RmWWBqsFbcf+ZqFGl6Ot3\n0zhpFEbz+6CikiVr0wcb0GZ8caR64MtC+Hl1CbI4DrfxxHDN3hUxmXx0CeeN\n/UCBheyUk0GhQqppA+epWI89vvcMk12P52mUvjqSUm3DCnEzqIYwATHOI+n8\n1c/S\r\n=O0rd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "862469bf6b13589b0454b0c72d113417f1aeb833", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-n/66E6EQznggwhTHtczmRoEw7CZvSgiYbahIljvhKCH8PROWuTLurp5BW071gicCgMz43jv4LL6Z/8NrLhvT/g==", "signatures": [{"sig": "MEUCIGwI0OKdQgFfLlmamTiLoBcsHUShxATOyUxvbGDO/TdnAiEApBacHYdGAcD56U0OkTxMuL+9t3m4IC2oyvZfWAlty14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8Dz1CRA9TVsSAnZWagAAhwEP/15AdHjBsC0Q9tE+rF1h\nMoHj3gPCxZkzZP4xgTGF4SVaAXE7rTCX5VpH6vMWnJ4/13HIk6rD9AhvTlJI\neqw5oO6a6nw7Nn+TmdBqRt3B3vkbUTavu/rT/kNA/jeomoE5Tju7M4RwT9Qb\n2DMEUgVWqh4yZY5+peQV0x57fCWDOFIk3ZCp1XYaiWKnt6wx0hMcB9URLrzb\n8x3YY4cCkRu2q4kHs1VyQlTUAwoYz0RpHB9BLSm5Ry2pCQlz8xcnnXqp6K6V\n6ZaNEpL7+IzNLvX0JE4yzptPCu6szbl0SsPKgA6Zbj3GSjlc/23XWxPvjz6A\ndieJTayAEhePxyCifv+bi5U6YBM3r4OjVjWILR735ZhNeskqIEpYoPPbebiE\nbzwu+fSEpRWOtTWSd1+5721g2KFg0wxSAcZ4XAa8W0VHxuCelA6CF5IPYoPx\nG82ykJl0KT/wZ02hvKDkDcQPNs2tbuL+cpJeaSB8o1KM8lb0qlPw6iGmZLeJ\nPJ1vLMKupuEv3JjC1Yy//rhtv0rObNflCp85/WqHi4RR4Yyb2v72eDpJ0y79\nHZzGrjxRw8kJx2QTKkaph6gAm5/wfU8AYMxNefUaYYMXmGZ7cAOgkvzddJld\nKEtbCf9GrhEbRBpcQere1ECEeywhTLNdwWVAzCGBgpdabI7uhGhWKl083Pva\nDErj\r\n=a8bl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "58085046696f7e7db119b734aad0bb9b00336747", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-opOmVIfg+J0y4aCDeszo+zbrnUeXrupI7V/nS8cTZYizVHA3xtd3Ur//yA6OopFXeNmJd0fW6MaRPE7PkBvB9g==", "signatures": [{"sig": "MEUCIHZJ6FIacvVLLnvDvlMe9yDBp78Acau6nSSOzOzQ0jNAAiEA2gtp3h+KWMQl2aL1S1ARVwdz/BeY6TKxded+gh/bVvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SSnCRA9TVsSAnZWagAAgHgP/RQUZT+kkbOgDq4ehW1G\nHGDbpohp52WSh/ZmDUlSG0iJU6FpjlMdxsQBQVP08sZn8a/B6Zm+UiFOw8oY\nvAf+9qn2juTHs4CS9gbS9PPLtLZDgefmustP96+NzAGHG8tE8Ne+w3dloUW4\nZ0wCWVxh8YIYn8RhD0gCIpQ4E/0ISl3ukUfJOjBzxvkcTFxtZ1gHGpyE3nZK\nP1Fm0th2YtGstGc90G5iKi5E8JM1BTRONcIbeBRnUUIS18XbnosqnGVNUYqM\noKarW2aeRbvjBUikVYz9woQrIfr/PggW20541X9HijFWWyN1fXxiF9noTfIX\nPeedF0XaYpxfSTv3Dvo8HV5n0XmbNguMc3APCtTGD43Me5EWTFIiUvYcTjmr\nJ/pTQbN5Nvel22GJaR5xSZkuaWPI8n1OK5ylhbsF7eAxzgJnu0ovCLepZSxX\nKn2TpPsH8GczItYOd5HjQLXn/v1Zmr8pt7dWCRGktYMYLPZH3bKVb88CbShE\n8rFQmijwtOZF1wDXFBOGu++xiluJp+7eNv7Sc+GCJ1DLBNlTGa6YB4Ryrv4S\nVPuqZTnbVM8C3De/SAlYbobFF5ycTNv/tWTTFQJHwCa2hcOSpri4cJ9n0/dV\nZM+Km0Yb0A86s9/76MFYRfnXkLdUI2EPsM5ewGAomj+KQivD1NUEh1cxWA8h\n5ta4\r\n=nqz1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "16ec4e681d92074103fa5a705c3d77267a8254e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-FwpnTqMHQX1S38w73ly4zx6yV7HfcMHNat1RDwKCx0E4sz66ajc2IvneHNhXTEgOHbm0p8rQZwUDqDbO8cqc8Q==", "signatures": [{"sig": "MEUCIQCjE+0A3cMSO36jh9tCScFgdu91P9X+e/Y0h+Tg5vHb3AIgP93aw5ZEqKbCnTn1+KZfLWZWOlg5IdfvkZRNvTuGcZg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZiCRA9TVsSAnZWagAAJlQP+QFFluxsv2998MwPhEol\n+2NAU00Blkn4mgFW57QXuGuHyas6jX+bmycg2g7FXGntsYLba4S4j+PLbnGf\nVsIHb4EOLKsHgyM5svyPRs19IKr2DITevqZo+Qbb+VwfYK55v/Z3NtSAjPzc\naX9odJbRwfWBYz2b6tiAftGbbMQF9E39wWII/KUDumeF0+DC3aYS7puhVo1x\n/QVNXXDcm3wFa5hOJaMTHR9FJZm8p1wMWRWjCFQNcge+AbAfodaS5bZO69PX\njFKO8wtoga/zpjfkOmY5yOrCVJ40l3xajj05WDZyKUO6qhBBsXZpjqy3ehOc\nFw8EHtW77POHcu9Ig9Lc/kasaeMV/5878i1PZAhLpikBkwUYF3oIRlBihN3n\ncu10bHVnR6AJXXvAEtz6vYCsn8HL1ab4E5dt+eoKJ+T0uRhdJw3tSC3aNkKC\nDVUFAV8559oRJ7PHDa4XvI+Z3epmHocFpfGdT8Y6fA6/rpgwW/IKpWRL2Mly\nTd2pJFtwN5R0PTWpGMXNBqDvzR9wFcrjMZBLgT2kk7pqA2PsiLasra9E1RLf\nf2ifzG2LpwJDw2w8QHVVZIGiQ/2YoxnSnWSSzb+iBjYHGKHPZd2XOgE1b+Kt\ncIDsVXnRiWnk14xvurRZY1iwPcVo97khO5hm6dQY5Ly5sXnz0Ys854sIjrjX\nu8tR\r\n=Fwtr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4a589192e85db16ac1a46fe40bb0a3a7ba2339b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-qUeXbs9ElAkOAXx6e1tBjX0wpFYqyN+yMtx0BmeUBugXDSZqo1L/vMXk1Qb5SFHwj1np3o9JPdYTebSkPHyFfA==", "signatures": [{"sig": "MEUCIH4Am1uy8kL5MCvCsDKgHNhdiLbbBhFOIl/UCKg/bw9jAiEAvLkgwMp7t0OVS/HFQ/4LZmTZs/DM42mNJnNJWSYsPQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WnpCRA9TVsSAnZWagAAyKcQAKNbAntmudlvjrOLF67w\nYi56lxBJqOCLIUCLBO+Ahj9s7/SzXXDrGMkXnXKFABbxX/8/S2u2Yb6Jzcvx\n6YZr7KskkftoICUNGxbwwhf5Gz4MVpg7G0gbxobUdo+fsBUvx+v/KAF0a6Mm\nmT6EcxfSeYwU2S5bNYEp79hJahVnrubnQy0SQaQzFnlBy4k79WdkYc+EXBL4\njUORAe4kMSv95zFHOkKQwMg8c39vLHZiEiGSq/XGRDPLcsOq4pPUPn7HJNY/\nSFgeYyyYB+yTGXqjm715WN09mYeg9dKLmLKawirCCKDU6PmPhJZD4vJNSGmk\nQsJZ++zubHEBV7Oy+J2g4utstQEFLvZtb0avxHM+Z/O9LVawF4Bk/kY48olk\nRXEZQesjzVuOMF6w4Xq6AR8ZiTiyBCJJJkQXVr1xu7VdcgU81fMcX732Niy+\nHUZeQrU4HRpd+W60SqtQ12OmtEuJpu4gZo5TWb4OJzArbwANTkSLeMuUTGv4\n0ZP9HArQ4tCwL2rcVww2DX6dDPmM2KGZvZiotDdgrthuWuRAhWx1o9cFAPNr\n2N/evdxz8oAb4O8LhwxPKDWdEKWis11pR52U651IVJP0GyixnTSYnE1qrMHe\n5dgoz4Jhq20lN8J9F0SMjFugsHVSW81JEaI+aPO0hTrgC+otldL4DmGhfXYo\ngNiB\r\n=3wVd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8f0c33ff9118f1071d3b49368e5a88640aad5e87", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-eFm2PCreHbiazKb1uxtjnxsUf7XPNHNRScDBVuPGGEcVNRdpHMe9YFyKwpF1kdjZxMu/mCWSF7F0Y5cvV0cIFw==", "signatures": [{"sig": "MEUCIQDFRfenzucUIVmR4Dp21voqugiI+B112M+R6J+NAgDz+gIgYg4pvKkJS9M/W/TccBBfgjuan1lihtFnNXQHxin+GRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUECRA9TVsSAnZWagAAqbsP/0MZ+ggcbcfOrIomiUFE\nu5jFDVNZhSOy+XjAXgigj/JnndWA7Gw84pdqCs239fVrkBuslSwRMDm+PfKi\n1CqSn/MoJJkFr9kVu+CiFaHNP60tBBt7ELl2t1S89QWufJu+RZjV/ZDwK18e\nrWFueo3isQOGikc3vGreYSn7AmXHwvXy2OqxUebiypO2UoyRd1J2DzdnAr+l\nbBAeiB7RyeaD/lLKPYQU5AGNowC9aOdD1/X9aECmY1wnV3K2ScGYhqfSFNjJ\nLM1ii/1cA5CoxoEfWUWHp3m/2wIIAHLfgHDE8WONDDjIbnCozkLjlqPDrqJ2\njlY4QqzQsxMA70SGY6Iz6KX+TcHEWN1Zm1GGYS83Pv9v5auiEJYu0cq9UJkP\nIDSh/8sRv5cnS6IqMCuB0jYL08plV2cmEAk7Avt2nKYGZTT6Qv4AmP36AfQD\n/eeqeTuEx4GiUuVUUwDlHcWFIzgb3G6JgPihNS8pVO0DwnzyojhXhSvcHs2+\nrVd4l2nGDJHEsV26SN5gtVx0SibJn1+Smm1D3Y3IO/pieEhX/+Ecx0l8Ae0n\nnTGSJL4nJCxFCGWIb+w3lNadHqQT/q0X+cylfdrPyFMl2q2WXhc0TYBt46Rd\nXDZxHA9SkR6m+iLGIembYrMk/rydMt+I1LF+JIZtHknXoXIwSs+SRb6geuoD\nMv8/\r\n=4QvH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ffd8cb0e6edccfad7bde2d9a58cbdf1eae5f7d8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-ggqDOVIdnKPEnAe/wLQ/dyvX6mzuT9VNRRFSlytIAulxvdPkBOnXSfmeC3ku9PnFN2sjOy4fexjJzxLjHNKBGg==", "signatures": [{"sig": "MEUCIQDkwweRHFDkhYm7Bdvfqre8blA0w+l8n//7Cv28EPP4yAIgSQy6BvJrdaDxmt6fbNIo7yfRyfDX6EI6AXQIv/hhgpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/nJCRA9TVsSAnZWagAAaLUP/jQGy7Y00ss31LLfPi03\n2AzFh04vopBfi2CTS8XJazncbGA926D8gBW7y0YBHB4WfaDEblu2lpOpyZJy\nBQqsUW6tzX8P8vyuJ1BwKPaF6WkZdai9mZqy20QCYyKkTpB3Vu7YnpgJZ+vg\nIVAmnWZaDziD8T92pUf7pR7hU6fd0k2N55/zTfuemXv5/WmWSnlv7Dvpmco8\nLNiuouQNodkm7xVehAKa66BKyzLKyvJG+4yeo+Ushv3lI6+GljTKX2z1rmth\nj/Qc1NhfPvmSo4afVSMkblDVPnEJq7di/HLwq6ggqqD6wgh8ghtLffpz7VEq\nwKwGjZKs0b12aYhVcTELoWiuryoYkLDDWCK9VBfbBCJPfiz/JOM6gRCq3JQi\npeQC/KUIIEK2eQkWzdgtMzP5ZI39ICae1Z9tvIXQMcZoK88/uP4/6yEMZo7m\nzCbSFq88hPBtkdmJoiikgZ2SpEH3ebgxWCYUXy6ddaeAgkfOx7LDS7mOWiiw\nrzqVuppFZ87vskpCNIW1gvjp+UQeAH0GjiSE/lpkylvsXRbARSwULFD/pjWD\ndIIXLOLjOGhVJ4YLINZ4Iw9mKWi//PUUZS8WrwVV11zORoditCZ+cMA5GwI1\nL3SLLE1alUFng7QvW6eCOUT/88Jy7uV3vg7OAJexvEfOQb41qr52Lbbu8nrA\n1Ps8\r\n=XqDr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e2f5e6f245e18f911e1875a6dbb3e3d48cdd899f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-tItEsJRFDgpDPPfBYdzZgoZEUm6vf0/QpnKuY68ZWygrYzdEKEMZQkODShuclimnt8yV45yt3ypYFpsnWm7OsQ==", "signatures": [{"sig": "MEYCIQCM4W//geTxzsEYNCRMHt1J5tTCf5mVilYiJDDBhSLipAIhAM0SQD2tSJOfr8teZjSWj8kkAAfbP0+89gw2I8bBJjtF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBHmCRA9TVsSAnZWagAAD+UP/3cb3/xxTGzxsKtV1dYW\nrYRv1EACSozIf78dOJsMj/henQy1m6BqOVtidjfLwN/n3TroB0KWRpcFztad\nixOEd8He3PkNLU8JiKBZMzeRJy5JCtloZKT3tkhEMAunoeBzYKRP/rW7u00M\nlJG0qx99GkXYfHg1a0toXTAQr5uds6EFZ0w09iNmroqCxN8M3D5b2MPllwa8\nEFcmJY2V3eqdVd56b7vesSQcYDRDf6gxDxAeHJ41RlaG7mz6pVP4zpuzgWMV\n4Tk/EEgHdCWfwqlMjJv+/PakVx8B2fKZ2rizQwe3ctzbybGeklLTXOjRNDto\nMNBC5xA/eDPwYnpOFxMFE0GAYsmBqxCLM1P7qS72MEG0LB42VyHJd05Yz9kS\n18xgjAM3r3KCtqi0PenMv/bt8c65NKvWdjH5m9e8kBSnFSJRfPxo+LXnfdov\nobZFFhRb3hm2OoYyKHZSdnorgxemnv148zg6gELV5mORjEEfj1WmfirBIpu/\nqCwvLSIOjVuiu+XkV+DBGMoHIJqXFyeGEHb6uI+nU07GR52yn1fvi3q61ZHM\nYqoyOb+gLKscmTr72gwn0lOP862YldaEvHx4vdbpyY5r0pMx0fvDEOfWyabC\ncFZIWgT4cQC+UCJ4f5W02zqg5I8UlNSMw0T+pA+qyqIUpAcB0BAUZ+Bhzx2Y\n9rSd\r\n=3mza\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "de31ec08a54d7e1574207a6bd0fa4ca9ac32b7fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-rrWeVoYi4FHJ6whiPbblTnZkbi0m/jTkW6egCJLSROcLH2a55VKkKomiXBu06ApG/QacsF3OxBqUVwvsOqD4sQ==", "signatures": [{"sig": "MEYCIQDbP2lpKBPXWCCpyfWxfLT33pINoTMwRqmzTSmQRtXE+gIhAJNTw9sdUCYAPDwVNLNZ+RFNq0tGYPuNDpSOLPnRyPE4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBXvCRA9TVsSAnZWagAAuywP+gJVGbePiFNISMlfXkr+\n8rZDHzv6axKLdeiqox1ZhyVDshRvunPLEq4azGUUBu5BIy3BvO73g820buaX\nYeqXt0LrvhkfIhVb94PG4hvCDVu+SZEgERXR34Z9vJg1Bt9XNq9LRMGS5UGt\nyWbO8VwGBcFadjqLyMrbkgXZ28kcbNuycrlx/1NTyT0OIIgcguc7ugOU/i51\na9UnmeuubROhpsN9Nw/s2HRyM0qFL/78nxX2iZVdULJj5j0chELDxHtbrfnw\nQ2wI2OHM/5peMrpdpspyCrLlVrjIRguJszSzv8mmpsf0WpvdP0lCLgnsewT0\nbfwtptaUxeYfF2lc+68qE+Bc/rDFIfLAB1oQJcCMoRcKqEvvtxWk5mS/ndaO\n1ejbnQ3EtKosapw24Sdorlr9g0zd9JOKTJ+GWw1pfeQkNmAUwG6Zk1vv0u+r\n7t9VsSWUuEO0MO1iBQETp1+fa9nZkYFWN9kwnC8PwBHx5FiIl2cfd4Ap48Y+\ny8GTBH2W8+Q4b+Bm1MHq7anEQhC6UdiNidoHb8uMwNXmWJ/jlWk/mfKX9TD4\npR+WHsyFeJM3LCSyPH0dRIc6cTW4ne5g2anLoEGP5fQ118xjBG2I5EYj0XYV\ntRCb2AfzrLErJPSYywvhJcX0QjLOwKhUp7Q2iHvGPJtbJhwZiDAAEvxxm9cB\nPnOd\r\n=mbNY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "26be207a110aa6e045c6cb01a2a895ced0c9a44f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-g5PCiZQ5fNPXo9pt9gZpnNc7mm1GEk4jJvzbxB4N/qRTi6JCJwMhCmEOFhPRVrze4Vroz0ptDaiFtEQmvF6eJw==", "signatures": [{"sig": "MEUCIQCyfrOUAx/q7pOsEnJiNWHmInlAUd2z4icP6T8b4o4dxgIgShdf/lHlnDQotrQ9J0cqEFqIl4TEgQJ21Zvx1LyebvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMnA/+PJvSw1NeosQRgck5Nd0jbIzG+P74Z/YbRtWYhWEBc2rjPDi3\r\ntoZj9eT7QitfnF/Dkxy2YJkRWeSF+6FHiIbRy/z9yORGcS6PD9sn0gyYg8dJ\r\nF6pwqqQDdhF1cj2MlurUO0dS4e6Lt7WK8Q9y8RzFeU0uq18MS6WN9Z6dZcU5\r\neQW7TPHRggCHARIwvEUhiSHghwd5Cwr95qAX9AOpLuIblO8g16HXE9P2k76S\r\nlTlEWrQLDV1PrI9hoY/5AY6DZH3T5FiQHv/fhlkuv5xZSmApeVV1hKRLfhZH\r\nxCctqeRW3Im3WV/5eAXVCn9JTLhKDLqIPysbuT6NajOxtMNYviR+Js2jF1bk\r\nTc1MguayCZ/I/ZqttQOP+O9eRS4t77PeE1lLUtjjy0WrVQz9cEQRt5gl3nA0\r\nBgcSlzZcDOIVCp12mvQvPL47Pkx3rO8ZsUNZtf/V+S40bXuwDYIYcj2tPEb8\r\nGCC7i+RPhsdkUyZFVrHbIgrlrkME7ccPkIJZM4wa7Ltf5smfdhBw2cwiu9lG\r\nfGG1kIBCxe49iFyjVpEwittvXS0ozQgpi4sOUzGdiG9OV7dlPNYW3qsmllA4\r\nxoZTgZarS/wKQHTQsn/0NlF/LzgNtMuUW/e/XYbhp3AILpkDA9BVRQdcPGuH\r\nRcWiMjKys4/iVVpZRHCYm8fzvkoe0y2ouBc=\r\n=MgBF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7484487551c522027ae4888253783baae30230b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-TybJlvuFJcBrVfWIu2a5GghcjQniWCObncrqAyClWJM+lKSyslE8psTj2U0bVzoUq3+Kw6mp05K1ctwqREMb0Q==", "signatures": [{"sig": "MEUCIQC7c+im+ZlPAKk8+VrxfwPjl3x/NVSZLObaZP0XBAs1cAIgBxXWXejKVBFEsV5G813hdUPez7MDoMGVLMzMBNJD9vQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7Fg/+JduQiQdsAjY9Eh5jSblHXJksjJCvjOR2vrxvWihucYwGEEvW\r\netvAaMPz6Uni5us6owMkvLSiazEoWz62689qEoKBmsOsJkshHl5EC9YwnHB8\r\nphpF1Kk+kBP9Ju0mtTQ+Pn2hsyKwfEa1kjmir+c752OjPuVjf22EH+ZvHeI/\r\neH9hcLVW/kzD6afB/Kt5aWzLtayDawy8ERmYk3IL4ysrlHDFjJ2U1hvGuQAA\r\nLKzU4PZhJfuQ/9REUdVSLB9pgn65felUvwZ0w8/ZnRou5Ooufyj0+0EKdDf0\r\n0VayT2IpRerzhAa5mWKrZhNSXkcuzcHz8sXqmSBaSwsNCSCPk8VaY6AWSAva\r\nLzvVNofqR06vIvhtmbEIDBn/rFIwrasT2ol3GkUf2uequXIVUR0hesElIwNK\r\nO+zTLoUMb+n0vwcHzfAz2NLVdbPGGf1xJMADSAcnccFT6g9NtzZ6Yp0hL5wq\r\n2fEJvC3fiyb9uoFLudOcku5sY2kuZAZqIeQkIRi5FWXjr1pwWHpX0/2NA18+\r\nnI8m2Fok+6fwrtVwTe6GLOOE0DpYeLTbicNqUMAd+75AYe/A5XULDdTnROER\r\n1CZv9jhwKwKihnNkWbEcHDsNL7Ab1OoLDFxCff8QcUPIlvhUAW1fyiGDbZSP\r\n+RN7/h2WaMpYxLb9uauHUo8aH+6srzYi9Wg=\r\n=gh3O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b2874a41ed3cfa0ac0dcc696ddf534c9488bf84f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-PpzcbPKUwS3lcv76RoyFCwygrCx5j5SJ2QeDSZQXhuz7XWC1Szxw/gY7i+LVhZrjQ4y6/HPSKPQ9hwdgbdd6RQ==", "signatures": [{"sig": "MEUCIQCJd1MKkgBDnmaU7t2WymmNuURBo9luAmAoDopPoyQ5+QIgIVY7L0q/5iySWjb92KMe9H/0UEV3iHX0CqHoiVUdnjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkc5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVMQ//WIOLV6BXPutG/zGcyEhECGgr1Iqu2VZjgVEhieZH9c+kyQcX\r\nyFVNSD4DuxbDzj1BhlG8YsoPupPiFhre9abXiveaU0Co/4zbN8xJrNHL2geM\r\ncmylDGOKZn053d2eevCGZWxmN5LVFH1HsUNXV/A+K+UOC9frJNhOP4xeApbU\r\nmT0UFVI30/7+AR/00bcagoh3mbyYS9dV4vpV6nHtpa2kmLyK4LCe/rGWoluh\r\nu8qjzYPwxfLtwwvjPYKuTpLbD1zyKACKfBIs8HPgQ+W7gonO2rSm1209Qbqr\r\nsXv+zHav486O3+dqBgBt+VzAszWYBNQwvw9AKWDV9EcXRWc6M79LXVZvIhoj\r\nfiMgNkwQ+bUl0HFiAXlq0QmeAFA5uy3gJdYdgAs/UBHsKzsVqgN75/ggJ0uN\r\nhvUuYgVjpYLWdR8xqkuRCXVH1LZdtbQH2SoJ+ULEyfbYA1CYQGjg1BcOl1To\r\nzH/mPuZT9uv5Vq3gjeBHZNnPPgQb/2EeeklkJttLDJSO27Qq+HCMlbfgGi7D\r\nD2naJKbrhQSto8boQeJtA57ju2S2caztA942/sFpXk+qksnFKh++YUxVhC5b\r\nfTQstt0BVx1D8JVjYlSl2TCy3JBN/aB+u4HbL1rDqBqSYC8cgB+pLCiDPXI1\r\nGfboDXdf9z7Njta7GxRQYXKhseq1xhvwtYY=\r\n=DWlz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad531beee725d9b9067e7f8ea1d3e906fc27d90a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-eaoc6tkaf7WiTIrC/jE5Su/N7gOF2kB8wNDZeRaPN5xw4bo/+m2qnT9c1jPZdfGslmoSZ1b7M1A2r82XTnQvvQ==", "signatures": [{"sig": "MEUCIAIIHQu9VFriO27gHpalYFlwkTYRzqngue3Q6srCA3jDAiEA9RX5wm5TNlZQKMmC4SgiA9YCTrqn+OvtN7NdjLZzKnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkywACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGwA/+JplsyVROJTp/buHuw5IEF+T7uwnwdeezzKl/OZaj+rtx8GiC\r\nU51nZMKSap7JCAGdbLwyBacwaHW3EPSO87JKx1VeCwrWF33DnAFTl66NdNAQ\r\nR/VrE91sfpXFhvJlGvypPbIsvTNQZdl/2vS5gnJ4deG56IiSZJAhZiVPRzH5\r\n269+P9sEMnpRisT0PylEs/UJtO4Ro9Wyw6qmu4UlW7blgQVFJeUQOgyzDogb\r\nwrDRBRUlhFrPFDJu3kNZOiqVtKTwmk5Lg6GKTWvpln71kodqUGiV96OvqCAr\r\nFkniKGe039C9c7tJg5RMwF/6lbB7pgvw6YO1zoKgjLo7Z69lbVTtl+nyxS5T\r\nf0HMaN4oMCMgQ6dXvHxSOjgHKH5q4D6x8cTm0TPEY0kBlmKa7M6P8yJSQ/8x\r\n2nTVOMd6bYWy8a158OxQiofgv4ZgNtjslrbJczH8MOkmc8aFkR7Y3V/4f8h7\r\naeopBNUWW5YTpzD3RF4A9YcpyTZ8Zjs68S+PiCnrZpxdWJa0pMKQlGG6X7W3\r\niLhFlUg+e3BHpMOChwWpQDN9WEliPzaiIc95nz0tHbIqZNqLYKpv0V3pvIf7\r\n02GgmM7OpbF0f5uWS344d8iEofd49upV2ldR062OZpJXSNmV2vzY75GaY7Ev\r\natdAHM/OT921K6GMQiZsLOsMINXXMcz0Qao=\r\n=ZqY9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a0586e8c57294cf50a25fcb695586374c82adace", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-T5525EjkWqbBL1X2SBMygvBptufhUlQeA7JGvG28istxpTSzQhQZTosOAYimEfHjELSIkTfbZxkWv5aAThFTPg==", "signatures": [{"sig": "MEYCIQDeD1lcOKQpBFvZ+gVqRIhiOfDC95qAhNEDWWLkXEeGBwIhAMqcUcNgiD/1chlqaCQAeFsP8lt7uY0Df522YGBp0Qou", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOUg//TN/JA7DC1GY+wvAiavS7sG+RlShigR3t0S3q8UHzu74cMeJ3\r\nfLHd2rnOGSBpWLoitA/lxy3wvX/cDbBZp30kiUeDWqViWULr3THy77FVQ6y3\r\nHbnkY66bIe3tXe2Fn/ypzbthWqKwv2jd/vf/Z3GesR43lI0EkRiqDlbYpjyv\r\nfz367ebYl7HaFMS5+8oy/TaYgu95oFOZ7TS7At/6R8ZrYIic+50jFel9bb7s\r\nmXNA6k0J4mXxH8RrG9YuATsqo05ikArPNWZBfd9mB9VFm6xvf4KKrjUe4g0u\r\nUbUIn3QiLL957ReEdTUbte7eHU6XA2ygZ27t9HsooYdSOXk+xdjk5YZI/v3D\r\nunQmbwengQOwJsUDi2j1xR/kA2X/o8FsNa9/Zk6cXsuMznb77xhw7hWbRebF\r\nNZZ3UDvbNLAB3eMnTwxYPlUKXli1Zf1DwG9pjNfI9Lk3RdJ0vDX1qGbdTHPz\r\nO5rJedM/vcA2slIQ0ZRj4PpuIeFt8bwyBvvH1NxA3nsNPOtfxickyjbMAUGI\r\nr+yvxD+KZ6ew6YFTOMDZHjE77JFS4Vppc0tZSf8EgCe8PCqfKPmekeEtpjFe\r\nKxw+HzszY/rQlQb7FGtY/g1DobCWGAgXcpOCgG3984CnokqvcuALN6GUXGPS\r\nTXSo6RSfalyzd//s65+3EQSzqpgTdoDvVSk=\r\n=KUJI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f83f95b2be236824f7395fce2566dac71e9d7dd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-1eZuz02cEDj5YSnYD68I3stHnN2iaYZAP387P85cXEef0UO2HUy3SzmM01z7GjDjugX32Ab2kkuUa+erktr/yg==", "signatures": [{"sig": "MEQCIHo8tOU8FbYpb8YHt7CkoOzitI1Jdgy0BzgV62/jM5oKAiBFTKXX/jAMUxvZ3SCq2F4vYwCLNoceNh0vnU1y6SuVwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVjQ//YgaesDFtJFbFuoxoVtf8n9o69VAWjsPEqnrJGYWvjiyu64tp\r\n+lm7dlOfo021Mv88+YmHMQN9oiPDj6Mny7bRUDQH3RiZ57LdyDhAua/kd8pQ\r\nhoZgYGuf7vIyff8WiIdyeVSeVqi3u8Hv0k61ocfAvFOdejKg20DYFb7b3Q3Q\r\nqpW3TOEaR+iG+w6mRgbxsZ627+B8sqFutGpbCnQc2IO3uksHWnkbq30GWkLy\r\nEagRRj6BB/RI5OSjOhbmweY33rtTwYeX/Y3j/NEMBCrW1PGXFPTOwvU9xJhv\r\nCi3Mali4ashuX2bOLHmRZkEJKUpI8+yFOl2xr/6ZCgFtvg8rxAPudXm+k98k\r\nkN0TeqmiuPJD8SjbM1tSIsL3hR3V27GaWAbttfJ965S89+cIXQormUqmiHwX\r\n8p4h+U8GR486KGHlS5IiBAPJZ4hAWFmVyT7i/qpE8tRkvu0WDXWZEbHetqeA\r\n09OUA+UI8NhR5f49wNgxI+z8gB6+vab9cK427biC479cPND8lR9Hl6ZPtTae\r\nuBX6UtOmJbNAXpaIsCOjZhJT78f8OPYcl4mM7JMb36ic+GBdLkgA1oZI9Inj\r\n5MvP4WxiXu0ZIosl4KkyzodjOzzErlGAJBczsr90XqSvqbkk0XJEOLdSCmJO\r\nVC3Kq07Gct/QXnAqzrpHpU2QYz/x1V6AAGk=\r\n=v8M+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f2a3ca2e27e0e6bb6b3d44b22fdee2c95cec8b9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-LgCO6iOM4VJDAF3SBhCn3Ao8tGpjpCy0jAKzUxXb+82YmZ4QQRXE3B8EY1ybgGs9WSrDKPt5bKog9wc05Evd3w==", "signatures": [{"sig": "MEUCIQDgRlH1Bhvo6TroOPaq9ZnRXPy9hdg3Bjqglms2sxnzawIgFAu2xDNurBmV5kZ+/ChXHAr1PXWFkJXFv0JJpT7zJ98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF309ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ+A//Ucu7j7dK7chPBwjIKSwnE0pu2Q+ZlsLjd+S+pRAlnTIWbA6I\r\n++IdZF6DqDZw/j7a/6SNw8uFUpkjYFNTbXipJUpItAk4TDfcfHqovN7m76Qp\r\n4M0tel+//8hWiByjlSchjslTUQeXWoZVYCMVeKRqLBkudXuHbcTzrZzfZEM/\r\nZ3vQfAhg3deamCl/2KimzxlWW0exbXh4QwVqfyFykPVZlO8I+MMB9yl2Cra9\r\nYArHXq9cOXEdICFz71Ehhv8znhKCzzjYPaWvMoQvr/TjOyQEX8OPxaor76b4\r\nqUuLiQ2XEQ7kGJmLXMzFZHFsDR5K0q4Fdg+I9ZNalNnS3eGZDliA1gceQwy5\r\n7jpbuInwYEJPywM/O8sOa3UtfBak/R4ewuFYgemEotl3WzZ6rpg+ItOKdmeO\r\nZKKLrqSHZ6dY3eoQE4Bg93DJCQhIe4RL/eLrvcaxHlCr6/duuzE8X5HF+Rap\r\nJ3hE+VtI+vxBkBIeBb5eVfnqA+IIjYCt6Qi3GeP8o2c/bvwtCvMaJ4Xm2Qp3\r\ndlGJg03yfcZK5qyZVWQIB2DrvZOVknK255gqd6mf5rUV1YSjdnTW0U9enmum\r\n8SJQvEUSDtqQTlLCsW29W8s/rAKUly8360ylQ0/PSqjmLgMToNzCQZUCcvE4\r\nRmWQs3Q1PLn5YM6gKa/ihgqPUkko7cu3MYc=\r\n=fzi+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d4d49313b02346203f11dbbbad0ea955d1966701", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-7YHHwvrTND6qz1b7aRQU0xq7N4hLmoqLOjWm62OKuXZL+VND02kZYMwzZtuoWSBGVeDeb4+8EbYBxygJjEvwaA==", "signatures": [{"sig": "MEUCIDxdeyE5lAvlb8ji5J8tiEMd7DjtWIewLbBvvMlJFoKrAiEAzvbt0owwrdhPbjVdSN4w5qRUskpXrC4NdD6cbaKu8XQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4mw//XquREhqYOa8DZqBnngY0FKuiTi9Lc9zFTjn+Uz+Wjuw/+oTx\r\nA6BO8YWeUkwRdE+2WBdNfyVwM0GVGD35rx53nVJHEHJOkXWHjP+0bbJupsUj\r\nv0TxmdJakX4LQD2rFMJqzlNj6Pzpjoqx0s+6GQtY8VwgMinxM+ZfkfgjGN52\r\nqnY2WD/5C9Szd2mGP7Bhl/6b/B3zD5ILu7lr0hgzXKEZGuO7qbdyztTMLDhf\r\nGOszVH8a3b9PZVcfm+oBGVkmfyBA1aaJ8pBGeoVkCZKryMPNHL3tkCq8o7aF\r\ngJ4RaGav4M5drIJlbnHi3jSLmcfFO/4Bi4sCcLsofwyC/E9Y8Ws48DVOTfjc\r\nu4C4SwhMWCjNh2ajD6FRAzVneZBqpDfpwjlCICPnuUkVaw+111oXgqTRhH2D\r\nyQD49cJIgI+9yemNQLELL78Jn7nARDXGw9cdb1uAuUdI8VBSR4mh/71Iv8hu\r\n1VPzXLD8epryG/xKT6uUI20BdMTR40jDK64XJT7DSnVBZor/DTzEkBrSkARF\r\nUKT+ElYnzW9fZTOQgHuG0h2RpCJC9HmbpZoC6s90ES+JYbP2TkSzYUdhAlHB\r\ndpIsyiq5LJmDD7XLwkkJ53bvfPXNOafkZ+we7bpHQ14VvrQj3cqdnZlGa3RD\r\nMoxLxTWeGAVFc854B0r5PX9DSdutCT4dc4k=\r\n=drq4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-label", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3a5d952fa4225999e4c124c5c0c2de487dac3eca", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-dDP74Xvv7GvtY6azrFa/UBcK7mNRqAAPi+Rs0X2zGgu+6wnC+9E79pNXpxqaFc7ksNHhPB+rCf1jjXSGMpEvzw==", "signatures": [{"sig": "MEQCIEQRt/OVQ35r6uZxhg5Eozhm0Kb0/paL2DmDOjDXNSBtAiAbRk99oYukOduPb4E7w32dNS5wqZ1gUU7ycSKWCmgIag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19725, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1ng/5AHx714bzKySm/JG3rHMG0Yo43Ytvbe/vJrGbGHMesRluQDmp\r\n/ifPJRiAHCvbyHRbPpBk4Zcz6eJi5/nnuZO5+Oq2J7KShgbTgu31c4LoEhXn\r\nZWtTlsqv4EDKBVxI+YI2CwlCbIIuvYwqnzKDWODlGwRm+4dCm0mMmnjP2pCb\r\n+4tIo6I/IczD6vM6KMxJ3O+qj1ToYblOxWRowgDriP+LL/+DdLChQI8wY50g\r\njLejiIE2Jtj+DV019OtoLUwOFxzID1WHYGn28oEYX0+aZydn0P8+pRokJXy4\r\n9K7xWTaBLHB75tzHc1v+EBVnPimhBQnuXSBKZWgz2hEVzAxTiX1bVJ4O4+IX\r\noNA4L0R0GCq82pyuFPS08mkw7AjBcffGppXEOy9slsRRsVW/B2HkPyo7qCT6\r\nKOimJm1r/Pe1+OY/kKGKecLOgnI29lWYE0w8LXqXzKwFHSUF7PqMysr2yxsQ\r\nYM8oKC8tPcZSlC4T2bUU+EifgkTitCylGzSxEHTez8BkJcZgc91KXuLASkZL\r\nb+ySqS3GaseAtXhsNKpCQb0iF0Yqg5n3fH08t7cct1Z7b28/4iRhqrEI6yFo\r\namcqg6W06KKnPaFLfjwbI4SpLDOdvnUZlbXrEo0ENeFpwaBNXUPT5AT0K5xo\r\ncumRgtU/KhMlyySkQ5y9/+WNWJdRNKAQkYo=\r\n=sRB3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-label", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "12cd965bfc983e0148121d4c99fb8e27a917c45c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-Au9+n4/DhvjR0IHhvZ1LPdx/OW+3CGDie30ZyCkbSHIuLp4/CV4oPPGBwJ1vY99Jog3zyQhsGww9MXj8O9Aj/A==", "signatures": [{"sig": "MEUCIQCjowO7haqIMgPYw4VHAMl1FKk0DhVyKypGu9S7N74y2gIgIdiKht9CKlo0mPXpyrDM/JgbkbpYKtE3MCtIFNTLvQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLlxAApPwBlo7tPnJh5uUuemcIFGlcbnmUNVje+DtShvZC46cA9pDa\r\nzDMQeoFSNbA6j8wshjVZrEScz07zvt/nrorU30bYyhqjfPDpGmlqaRBGgx/j\r\nPpCTQ3UHSTrtNbMPaKuCMEwJe7jn4uK65d3RK9WgnuPZKkBuPln9is7RYEiJ\r\naSb3q5W2YiOWCkxbvpZJ9jXEEzWNy6oPRRfP5r/sSJJLQB4Z4WM1U8KDb5Px\r\nJ6IyC2BMwKmw91imXh+f+gU7g2MSnHn2LfuCefxlE+3bpKedGpGSASrjgFhg\r\nAq7Srahf5mIFtcmQaw8JtmcTjiqbgrS95SYVyLJ0A/P1UVsMBCC/QNZHpRz8\r\nL24Ou/Q4A1vY5ShbFoUpBq9rpPQsdeTwptBObr62lRVGC9VDmGCiR5TsPomm\r\nQX255QLajhDpB54gh7Fm/H6XRfrvXHYU0I6TWOpKnohC/FaXLw3dSV8DVjV8\r\nrDAP6DdJ2iKNiOpZRHQgSJ8J7if/Q5EhX/hee0jWuUNjMAQom4yOAk0Se06Z\r\nsQ2Crc4mca99sjnnq3ElbLJYtI0nHvQdBx4R8kOP2wNqFkT7BrZAmQ1hNR24\r\nWn3gr+ozQhWm9ox80Znx2sXVgfRq0+Cjny75PHttQXnS6Os8VlhHxyDTPcMp\r\nrpvfZ/5Z7G4dH/NAu2ADpelnFGpa+wPvK8c=\r\n=qsvz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f41a1865d687fbcd1000d6dc23d67657fccd1f4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-+J6R/5/JumLa2Cl8Yjt8f6+d3xhaQd0Qjsgbhvxo3oOYPri/1JmLnCnjPFo9zify0G5zaEQ2OjHAmAiKWsWCow==", "signatures": [{"sig": "MEUCICXKb1PvEgY+mIs2wVJLHu+ThiTyCWF2gvTukD70Dt9hAiEAq6G6+Ei7KYe8uJfRnAh61BbWkS4FRqz4ixa8ofyXRi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAQVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7WhAAmJcxp2tKpKaw9gg0bmWqIfri/P7F5wYV3m8FKc3v9nV3kYQq\r\nGEtMSfqw074We3P+cmQqIvyCdqnDvbHHNKrulx//k9AZ2aNdzoElirZZWTL1\r\nN0LxP1c7MtPXTnLHBGcJZFEUZK8QJEXrrbDVApNuwXfjxxFPivIlmCBLifMw\r\nN+Wlvy7rk7SIchujWMIZq+IRDC+8rCjcLvm2MdUAAopo/xJUg/6gtIF7Tus+\r\nfbsIme6tEnpQaojHRGWw3Tdh1yCy/SurieYJWSAI9p1hZep3cp4iVvd4rqDM\r\nwZ6B78S3B12EXWtiGybUsSDXZIJynp81P4gmVh1aOJZ8N7Oq1OrHjZH+Lqgs\r\n343BIWwZp6fpeA8SSxzQmeAY35LQcwddjs4lbNGmJAyKyMn+749AZrVCmpC1\r\nOYFXnbGY3liifDIP4G5h5KKTxVJwwJFUtz+VjOOiNnpF60wKhqMoFdYUscLB\r\nob1aq/Rr3Rr4b/L3nQLVWqcDhXgqZZZDxVHySO8gZVenkvW85ClvS03JRX7+\r\nmFYyp/U9Y2/+6iDWUeK4ClYOIkC05KYB0ta2oMZyxLERl/a2b2bWdJ0a0/F8\r\nJYnAlkZEj9ph6o3Ib4R1VTrwA6wbeN55Y3jbvZalh9ottVeGiqMyNK6xSJj4\r\nyhz8bCWi3cZ41e/Q3PWPbtemSkVQDOH3oGg=\r\n=kczn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "763eaac310c41fe1590c7535182c0959d540b83e", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OPVmy8h8bc7wex3lKxLSkConxiHxUhivp3z6VSClj+V4vptdP8KLseBc4nVYutpQiUSOGcb2qiJ204LyvDohpA==", "signatures": [{"sig": "MEYCIQCFWZlI3bdoxYktxBRcTyOtVthFGdoiorMuGF2zfKqlFwIhAIH35OlSztnWK4bjIsXE/QwyC+Qx3OsK5uYhSCwZMRkj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCO7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGFQ/8DlcJWjWuHAoa23u/YtUu+/dap3uUSs3RRoOvxPkExwgVXZ5i\r\nMlCTy3tuswfiwyyawWMJRpkHzlhU2Ju+gKpZWPptkmquSF6G9l6K9Oy/0z3r\r\nlu0RNAYLdzBEgJlVU6JGU2NkjR57kowXAahpPR+VRsYqvdB0gWlk9Hj/2VHc\r\n2CwVlWF8KbGucLxrjne+iWtfrWQuq022Fe2Yk6ydf4AfsW8qsNbHzsbp1Kjt\r\nATtKbXpAK/Yb4mzDyXFR0bmf9BHTGhKi385+l7aVGn8k4mzPSk7QUpkKukhX\r\nMiXo08NzRKyfOy5qRC8fqTWwjn8SSh6XxfohXO0VN5wm8BJ0xOr7YPT7O4j7\r\nt1oYqjrofW/b1WyG6ZngT6v5H5No7zMdbTZsVO4AMiTnGv4ws/ghOEqlmOGJ\r\nTSa+N4PFmhdM62nnSOpZVi3j7KBllmkT7OYNSfP1yBYTmOIqlUnrW+UZfoF1\r\nkpAQPslixVpa9/FzLXCOcV2MlIbho5NR4PV395tKXiXCpXevze/jEIra2q57\r\n596emweF38Yii70P4Kck1dA+kQgCh8qt4MFxP6UkKAsrFEpVfIgTeWU52Rw/\r\ndrDPZaNw27OGHCCwaH4LqPNmH2E6m8VtJaFGoQzLvu3dj54mHSGeSV4WF3Ga\r\nzm0osVv1RI4jIusmVQt50t5YwvKrpgW7nps=\r\n=q84q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5938ee34b7d6b38a435cb040a0f9976010835abd", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-7U3Nsugeg+QGBz3rrWGbZDl4cGAzgka8RzmNhiES++3autn5zLRhGYT2AY4gYIkYKbBc1b7yb3LVyhGPBjn2Ng==", "signatures": [{"sig": "MEYCIQCFO99ytqMeIAgFEeGqlh1xvK9G0NWBQaqTfzSo94CWSwIhAN8ydO6FTNUfchiYJl6OpR+36QVlZ7nkwPI1FcHfD/bG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1bA/7Bf6nUPrAAR2i7o7kKyaUUS8gZRijxzxks31XJSCT5DK6DY/k\r\nT082g/UGgBJJrY2kPtwmi593wpQ/qMrbqUksC5kIny6ZJVb7z5L6q/4y1RnH\r\nBzT+xDeGtRhqkjFS0i9Ip/sLzbkyQcLt8NPQQV4S0YwT3AEutPXZRe/eFYJk\r\nTRu6+uILuQn8fLh5W+HC8DXOhFjLzk4AOmeNGb/innrTRbN5p5pr9Gfs4hL3\r\nAOXg6wziuFJx1nGFgfInEsL3u5viloCXvoozfhO3B2BjfTt+RA8g2Fcq9fnZ\r\nfLvGB2OyjUFZkWAhF8x0SJ6Qv13YYlQLBJuzankUPQsfVWg/ScCUCdTLRNjh\r\nYZmJ3pC9Seh9xVwOjrATrodf382QaVfCjMKpSrhaSt98vA6nzWrCBDwnrwMl\r\n+PmWW0A+JkqkeUtkaDEEvraqtA5LIHeiGBJqFV0vHdO1/Qpqjee6s7xAYaLC\r\nwfZmRj3nmOCdI0ywkgoDCKctbuN6jDvIWqbBHWObdnouRNFCs799Eqsd7da0\r\nCsmq+St0ONvjTNsEfLbyXapJQRwEYyKtSPcg0EmazNEgr1dYqCUYVXLWbeeP\r\nHZsv5zFg66s8Lyz1rcZaK0kOF8QRjFHOtGc/qRaTqwygXRmJ5+o2jbTf//+L\r\nq/6BFpgsuP1rWdgFtDDmHjXld6+MhZWo22M=\r\n=mGg+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4ac0b72abab6e6c08f01307701007a36aabfbd34", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-/je0TEhciaElb2CVvJsNOTjjQxXi8Yw9aNDxdeNIQ3r3Bu6wj5dJ/kN4oold503zUfMUc9JH5sPLi/dKOT5xgw==", "signatures": [{"sig": "MEYCIQDTQWvI61TVtnMRX1htY+dU1iw91Nrqfd03q6BD4JP7kQIhAIH+61qZxfxcVDQLCUlE426ZBi3IIBeVAtGxfaBgBF7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAAg/9F2nVmo7zYE6csLOMeY83nVljP8mEmqg9PyD7wg61TvG7K80G\r\nVfpOWfkpYXqlbJKKTLtPsyFvwdiFEsikZIwLKloeQ+TytPNmvPrnF4wIUwCo\r\nTqpsizxuT+B3RNhBMZmW/6X58T5mkVsMccdI1uGzzf90ejbvGS3r07NJs3Ni\r\nh+myrFu2SloyGe0lYqa/mHWzJkzfKCUurWQYGOVCfliheDOJUP5zXA5V/X1k\r\nuph/6VPhwK+XGxQc4xoLP2R0gZNdoTUGIFxTwr40VSg1C/qxLAwKdd0NwF8D\r\nZAmWGzLY/oib9ZR4LZipNlmUMZOjg1HpFxmohC8PESayFB/nyL5JVrHEIK5M\r\nRv9WbyOTMn+v2Z0wq1FvUB2ACmz5bSKmLnCPoawjOJJsnqX8RLjsNHXfpycS\r\n5suQGaTMmer7uURZNUiB016pI5sgsdYt9BDQg6Qlj7h24ytj5EIUomnNbF9j\r\n7g+8MlwzlRNFprvvU7VyAIDAuEg8+4Sc3Hzy526PVAlljYq1bZyKFH16uVGL\r\ndE8iqAFmIyqVjKAz3yooSI88jSNPOk4BeM7B5AecyX8FkjTTeJ4fwDwrzCDR\r\n/qgc44dByKKTSxo9hJd8CDcBxXjUafOS7ZdGeZbIt2G978Z6td+4Mc5XvWUf\r\nJw8QC02CruHHG1R7BYNO9dhQx5r4pdMXpZM=\r\n=5QYg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c79d4539e9e62c23445996dbda3410f51fa631ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-SoDDzWBm7nPDgjeADZqB5QEzM4aQtEigxeM+kKK1IBUlfZNIVcz3fq+K2YYlTQIig4vZtqtsRpsRi6Ya8XmxaQ==", "signatures": [{"sig": "MEUCIDFQOP8fOKwnSwBGAjKIG7JCT+NWwzdvSpbFrYz+hMZMAiEAjzI1q+qb/zl+phkzjrl9tiKy9ZAP6/ucHM+sbK0S/Nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapghACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJBQ/9Fv7WEMzr3ZI7hw489xo4rvybQH4hrTViHc5I+/Ah9VZQYwat\r\nD1PiqVCFM1TPv5HdRY6P/j4rh85PZB/M4s/EMJaTlsCboG7g8rIMbHPtN3U7\r\nkgKPxzOr/6rFUoVvViX+Z8ZJVoQoL6sy94CtteOo5WFIcYqaxPAIXoMTxXdT\r\nA+m1ZsgEZgA8BLHJrxsaM9h/EOZD1uWpHgZDiGdynv3m8V2rPl+13ZCUOXrD\r\n6zzKS+OAa6i2rwUoHm5P4wDvTX2qKX1kWRNELvGWQarymKKjGGY1a28SByBx\r\nILkGoMxBgfM7uM0+99FIqIDL+XLxkaWcTaWiJRF4/Eg785mkFYpuljUzyTFj\r\nCPaurf8V/5nRm9fAlPXbzjYiaCZK1sCM2kVRhGPbfbDda0adg+ox3zLIkIBj\r\niHEuS6mMpYQSZUXvaY6OXAPPRdC2szxY3OBBg0CqpDZcjzmuknsI9ZS7bk9U\r\nbef19gL6yKgNnWCLcHPN6fCpbaHdKP1CGO/l1fiaJujNzDnGQUlwgbK/RdMe\r\nZa/NN96hWSe9DrySH2Mtl8rN7vqbzhyVcc8kEaf87J6T4aTHUnoqCfoQf+1T\r\nAQqXcNhDnMZtf/MoVIxikK0mkOaaw5LYLFBil7Dk7QSxKbYPIBurw2euntmB\r\nm+8r1iq7Wz2DwCf3H1LFmMfuLFLB5HgAyDo=\r\n=W1p6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2d974393784500f056a360aabc709838646a743b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-UP4hhVAIi7aD3CpIEMVtWGsseAaNsJ1DTVV7Of0PRStwrGFZ12zW5XGF4975x7miqpmTmiGSaFFSWArT9yEGmw==", "signatures": [{"sig": "MEUCIF4vbXfiJ8FaOgolVLUyRGQZ6HfVnzjbXMZZCfNGJ0ffAiEAliFAAteX9giHHj8JWmuF0kLV930XxOXa9zUxJST0k2g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoELg//eECkVs6q8IYFxhOlgJmmNzujGMkwoyXKL9K4wh8zBm9JTe5x\r\n9UsJVgxY6r78l8BzvdZyN4uohjOWtYi0Woy7ozxSpHopTnYulKPRnCRUxUdm\r\n3Tk6EairD45RGXnBH+ddHrFrey95L75/2hgUTh3qNIiMZXir1N5ylO8vFTy+\r\nnSNqi+Cgrm05mzk63eDxR9ATSpto1gxidscZSS3nYz9cqcctglXkWOtyQEiz\r\nF9QjSuQCKYXqFehL1seZ34C2dO/wEuKX1bYGgIHcZ/DIcUq+OOiSnLgVH7NM\r\n+2C6yWiqe4TGXu4d8LscYClvtwpcwNkETEKehTFMrNvIz1JS15Gv+o1P6ZNi\r\nFzC+pR66HmnFxgMCg4JY7filXSCqDLrnzCPnDHUsVtuJP1Nn2DckwiF1FIYC\r\n8MW8HcqwobjFSqsbd+ubeTvjzF2BxsQ4fqoHbBv/yRBudIqIZgeXcZCyjsti\r\nfPFov5HkyIyBG76mINTHKZfDBAmCGndieMh3oknCPBZ+OFaiEoSMa6iKqDzj\r\nrIVhFEanXBZoKb+vhxE3+ZuUMXfQgGeaiB6uimcjm9fC68XwbAH1yGIXJPoY\r\nY6ZAvuDsRuseKbdcq4oFODtBSWIYps6KCtQp9ebSyU9G/UiDPzfLoCgK3ar7\r\nm4jKuR93TVXWzR0lHPy5gmaSw1S1Gl8lVt8=\r\n=FZtK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ae10157766e22b71b4a3da78ae219f0858e3eed5", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-nTrLisKjfY9Z6iB1OKzlyrQrdvRCQmo93DvQTEyGQd4/7477bykjHvL9o+e8TworZIGtGdEIH/9gaktwYfvT1g==", "signatures": [{"sig": "MEUCIDy8bqxIsy1pLo3EC4xqPiwYQNqS3ZmgBK4GNYRKZ31tAiEA7VINbMB/ERAlhmHvsM5v6VtCSfGfiuYt50V9O5VXJhU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91qACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPMA//XaS160ExnEF7POhb1A9gx70UgVPulTnXV1rz5cgkQcbyj4cI\r\nsomZW+eu3VjMeHEssJhZ/bU7cABGidXP3fqVUi73F+j/fS/NAYZ04/vMCWFu\r\nZN5xpbC826MFr5VTRTFEKRwN4V6QXigkWaVmBsfWXSZI2fWlwDGoyCd92qL5\r\nqW2gfFgZPKlHCzuqcCC27I9q2KwGnPtxoXE1oWCWYQmnSe0I68b4xkwerNda\r\nkQpTHsqhGh9XNE5GVn2lQyr3eExqDH/Ciq611IafL/Mnfg1WEcQockNAhfTw\r\n987+jaZDl113J04hR0/Gsm321Uo7lVc6g0FnHgU05ZRSNKFNFPUVK8wR4wco\r\n+iIPl7Eyj9lj4uQ0MukrBlkgTuavqkjJbeMcjFZfATeasXaBDgNyH/8bO9tG\r\nnksrNhPE0LEh9EhMUUgaRz5mMC75ltajevhdxzndeJGuCY7FDrd38L7QrKvu\r\nxmivcih/ICmLXBBbMCBZPPnPQsZsQ6vYVbqB6gjjSJjItF9xg4MJ2YYbLxSI\r\nokf1BfBKcENcRiYdvJ5Pq6p0F9Xtkjdah6S8gpgCnvmn2cHQmPEuIVre2ChN\r\nbqNggtD4PWd1hYsWGfHmixAMkJ2k6mLpSBW3vM/CAtbSbq+A3KsKNUwk0xOP\r\n+3eCRhc0IsFddDyj9DhiTd35roNmR6seHSY=\r\n=3RAn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d2f9bfbca37f06f69755e279205c4eadf243c7c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-7tgkbk4pGX7sbJlvGK7EEiF90355FTD95RzYEiOt81G94/eSxCdXICml2U+Lppicxr9eFDV/SJaGmxdnbznE0Q==", "signatures": [{"sig": "MEUCIFChWz9EnYJtU656Jnmcg17E1OHp4oyU5aHq6O5AVSnKAiEAhz5vpsPcsG/ppxnuGXm4vYJg7yFB+GGW3OZVgrhfFy8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXiA//U7yz2RulYAUkV8HOJ54nfuQVlFMZAx39nKjimcTZBs+8hDVz\r\nskXrHKyl3BUJNQpYnAu3p6xzHp9bc3oc7VOal8LCnmUqyBolEELCSLfomOrK\r\ni/acyav65y1AUtncY4IMPTGCtPcmd4KvuDwzBrMY4bYX7rZWMLFyd+plA8/q\r\ngUkfaHYHab7G1EYd6lsJ49OktBzdOLHhMVI8CFD5y9FXyxPZw9IyD3KL25Es\r\nsjwyZNvx9ivUhJV/Tyx9APWJ+jE5JRmDwrObildX91LL+ZH9oSyVqGgUs6G3\r\nv8EypbQ41cvmsMr07hobKAuYv3mbQTh3M5T6Ddx0twreI1RDOV/DJgFtGMLW\r\nc5DNKLTNdLRrWNwhA8YH/lL74kjy0iJ+RQ06cAXm1WLEoWfhRFeaCDWi/sOq\r\nV/uFkC7N98U9ycL0zrkAPcDXjnBcTyaHV2CLN/GX2UemYH3+V69HoWoW6mS+\r\nlQMbPQOzlny9oPTj5qmxHbXzLnG1Ws1H3CvIOdn0QiysUXT1vQcs+K6qILYG\r\nTitScjRz78IWmwmnqkw4QMkDuEp5AJeht8oeSUIaX+UpngVHxUACvgsn+7Zu\r\nLXvS5i8NZpXdVHNIEOaDGXCck1Cm62dzhyFemXxv1MpwaEYF+LRWWndXtdNS\r\nILUWiOVR2htUA4CSaLhdsMVf3lFHn7OjKnM=\r\n=sJOG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "781a129c3c92e26a9f41264d9224482c65cf194b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-/tt3lEjOPllwyAa2w4McRCUum+wGH3ex1d2rDQxA4DoH7ElX8Lc+1e3eSfS8yW/xi0MVoM49+lvu2zOCFNxGfA==", "signatures": [{"sig": "MEUCIE9Nnm/ktWwIPLlENPzx22BOly0mO21CopgDnz6EAqEKAiEAlhd5D143S7WAtSKsDLrZCleuJBmdAsTe5WNP2t+nD4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZ1w/9EXmrpjBQk6CNuWQAtuH8TUW+21CP4JbyKC6gJEb0PmMNkRmf\r\n6aX8LT9rHsVeAqmSy71PTWx2siqfqfgRnw/OucPDXURIb0NjG6yiM8EMPWIE\r\n7xpL4njuLhkn7hRvmiBPcEnvlKXUl/5sLBIAWUarn4EF/uAKzEydKacA6PvW\r\nepldI1BuO+OTXNCVcuBCP/Y5w5JznlhV9lh1bZlPQ8c8jWaVTkZzqvEf/fw/\r\nZvrbBTcqy7XJG2PQOKz06RJoQIH00V4vCtuiKh/+eVurzjY/APZo+jg5/jEN\r\n25/fnD4xbrnMfQdc0INDRxxM8AzVJ2UdnmkMEeX6zieHW55Nwq7o4YZiidOS\r\nDgh4gBLWXG/opYYL7k1siwcSb8V16qjupPeoMnytz7x/2WrwZGXJrEffsx58\r\nEpvrJGrk9+Vixoz9pPS8a99XcaUxFd21Cw20rZxThtk9HB9DC1r3X7+/i7xg\r\nkagyC1frJ+r6dnI6BJwiqviBitl7gsC+B6sBIHdQ13cVezx+ZlMgcT8phvV2\r\nsKUA8O6/G/2qvZK4V8QWNKIDDLVujYGjglhTB9RBtsX76UfWOAo6KuWW8LQf\r\nfdBbDEfclgUfh3ZkwWx1v/SIylPPMPbrgHoAiIqcsUXytCbLIwGWxXAquZUX\r\n0YJXYsvIoy3S0S0tWFGsgetJnJpsR3WGO6E=\r\n=jerx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa10d429d5cafcd1c59f02f3bfaf33268979a073", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-PyG5dbdbcKnDQVDVoVtspcJLBVUA+e5pCsVaEpwV+A5sk/OMbMDRcGiEo9G4BfW/gBgGmnreX62L56IoX6ymaA==", "signatures": [{"sig": "MEYCIQDeLc1GvYhswTSewy1RWudKBj1Z1YkM51r+0FW6DM5RMwIhAPHbO6sJMIpTjS+TfcUZeZ9AWFSPwojoSB5UUl+vVlxz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoh+BAAlPS85Dx1Tj1mQHvJbmhdXIHEe5XAdJbj1y+slS3hBiZS4MBd\r\ng77N2JYROesVYIR5fI40X8GOiLGC6zAhccBaAUrGH48sDmxTW+KgAebpQhig\r\nd9fJJeRf9APNaIEnZx/FWCauelEVUlhZqwJOXqXT2QucosJ+qhblbyK00Rjl\r\nridRkf5UtXyKVT5VXv6dxVkBqBQEaY/DPyof11RbxvMtSXrD3oaAftAz2Vwa\r\nRxXICuyxJuraDwEdfjuKklGn+hW5WU60j8ChyvYev3euW9UGAjD4BaNuICEN\r\nwxU2tAPL9xzpplsTT4AYVC/1Fo7yYyRBUdkuYO4mMOL9or/VAdXJmYbRfqpc\r\nDg9OiiVbgwGd9k9mKYYE+1ObD9NROU2/JBARuaM7TvIPzde1CDzNpeDY/2tf\r\nHJoc0UJdu6n4PjOGwcKEQrDCmaUx8vPXuF01s+vPO/7obsI7LSmE13Mpt/vN\r\nllo1USdoHKkIVXn1BBmpXx6v94GuSyrw3bSs4fLpZVStqBTqhE+cdXd7P2Dn\r\nSN8VxlIsftB82WnCZHXKQdlTBtECFhiPO0m162+pN9uqUckBFs9eiey64E1u\r\nc12benr5cwNq3aZchi0hLK4bf+He4qiDykepbfAuMHuT/irOC1ejfuyHwWvA\r\nn4XII+QN0toxfm9BuEsLVTC5mEaVlhUYuGg=\r\n=ZCd4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74a922bf6e3e55e60a4fc05a8c080ee5b5c9a713", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-9TsqnjKaJt+1lDlg9OrIdZQCRqRhlZc1S7/9/mJiwvs2QlLvrUaSJRIiseRWKKWT8gR80XmiCa5dF0PevbAsJQ==", "signatures": [{"sig": "MEQCIAeYqcNrorqvp29psdIB4kfRafsO4tfaqbuTaqILYdonAiAHNWFBxlV6jEBWfUAcAhtmfW2BqL+q5h4RgU0SeVdMAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlwRAAkURMJ1N/bo8d2+/OiWqTZjpcxHo+yPYG47W46LqBQSylvS/E\r\n1VfndA4AtI377jJSPTeXlxRiw29DRa93QoSchNMjtEHPbIN84sonlBDJ3+tg\r\nE8EV7L3ToIZPUg5HvxxRIEjHy5xIA87mGmMK2Jg+rW6l9Coi8ySltCWuHbj3\r\nO81yZZRVDqrhRfFHFqEiXuK0JXf4EWIcZpG/XJZ/kwjDFwcn0RU+wo+ml5ET\r\n2hFMKg4tBNamtTRGDX5LeaqLEo644AdqYWVlMBXEKbLXi3UsqhHov1a/Vm9t\r\npmsfjW73umGo66+R+2eehSB/XCioWrPUcHwjL0jWLdt3gr8Sw4NfI4RwwixR\r\naNk+2P4nYNa7ce0He46VbMxWDFumYGoq/vQlwUMaQZsKEOV0hBEGhdICsYd9\r\nYuhSXn5DX8ELmHy7xiLOFsyRGI2SqMssH9bsnCBbaHEysPkDkG76YJoXc88x\r\ndLYa3d5TE1Z22JVtO1davrK277KgLdDC3nMwxUuDzBszQXiNm2abUeJu/kxd\r\nDaloY+KzyMeT8RV4w04N4Kpvnvuk/bc/oh+8m/FbHpA6iTIcNgTaiaFA4B8K\r\ngR+cLhkvHJq/7kC5e3vyAHsR9hQobCbP1qW1992Ec9gI8T3UIW/zsYSAwosn\r\n2a60ecY53TnzlzLWE4XGFVrltf4WW1ZsEq8=\r\n=D3Us\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a4673b264a7ac9664ddc7da0dddf3ca6cfd9f2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-e7ae2n/FeiiLvWJQCCRNf3VXOJcyh3baDZMuD+7fgZcLGGszCmiPwmqmbqrJ0H61IoBv0OrxpAoWJlI5NovPfQ==", "signatures": [{"sig": "MEUCIQD7raJpZwo3lusW7DqFDb1DWP//Ww4F/V9buPHmXsDmJQIgd5wyE/DID3OBML9UeHBwD/1dgPC34vFiKYubCT1/Vi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieof+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpskQ/+PxYVwtjqTI1tW8I88j3RyMJpDxtXfBswQz0cILdNY5AlnzV4\r\nuIM8I6GeijKhNQbiJx69b4mduJ/nFdQpcFFMeSoJXz+lIE9KtBgFzKVJu7XY\r\nydiBOqk/yu/4h19FqCjr19JdpXOlr4PbFAkzcDO3kXCBrw+Q/O7xH345JQir\r\nUIAHbeq+lEDSDCXV41mmkMVkiee6/tiY9XdDImDnaA4eECUNV9TbAEtZNWFw\r\nZHKVNlgjndIgkgxMiIQYnVFh3eBCRsej/rgYN6DUOf05gD9DixAqYYRdQCbO\r\nWBr0TIIQWzYGKJNbRHzf4VSFbXnOy57XC2WwhiOQfVts8wZaRvliM1XxVEiM\r\nsPdHymiLlZbuSBSslF2jT/mvDp8E1i6Qo1yh6uJASGtz5/qXG1EcdxjTNtbt\r\nxouUg8KESwqeMxUsN/fcPmCqZvXQh8Q9cjAHCoP9lRrgbIuPdap1dz4cPrjW\r\nnDQzB/HVUsDckP03XT23PnTB6en1ItQ4vHtzZkeVyEVErL6TrbI7dJVzcRto\r\nKG4+xfX0/jqEiR6B16sLMcOJcT9PH19ymlnM9VBbpMb+1y2OOv1lopZFHbFu\r\nf269Qg+QlwPACaaV3924hWIgmHKPQRCObh1zO2A+E2q5uvwATMq1dC8UP8ar\r\nQxhxeoTxq9c4gKELOCqzyF05cZIZXohdI3U=\r\n=zFq+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d7e4f0fa953ad62caa3d3da3f2b7eb508850d02f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-kB6Wc4Eeusw7g5gEoKt7s8X/yENrHtV9JYJQLRl5ghuL/YMfX0Rdz/EL8l0T84IpqFW8aKlVAB44Gk8Ob0CLKw==", "signatures": [{"sig": "MEUCIQDHTjm1jT1QeCoAuOtYQd+2uZ445zRM1kiFG3BtnLY7RQIgKpNwijFNqPQwu5gefij+z1j0EgxnDcWD5MoOSUiVO0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodIBAAniS2IxwpR4HMAZnOR3ah7Z8dojF6APQ2uRuGQIfKh9nZuvmR\r\n/OjxeLu6OHScMTX58+cUFlm2l6VRE7jU+ug5ytaNJV4TZRlKUC4D0lUyuLyy\r\njj77a8ZCD9Te9UVHYaTkRmTcFqj968aSd5Lm0817cj407evfr41RiVyvAq/8\r\ncldYjNFeuitsKHY98gOD1BQGZGACMt6XLMRoGNR9AGjHUarX8OeZ5CeCz0RX\r\nUY1+Lk9SSfEH/D+w0cO/+nB5a9r1UuW6kuReF2Wjh4eR1ZCzjOSg/W/ScLT4\r\nujBwNKSawZtFwNwYUuzmJfdQZbp4vjbI1601DIrlYVbfDO1vFuNC1Xz5lnUN\r\n+/2olnkm/by36hJU+bvkhsPwF/IfYh2PPdTiSCrUBYOxBFPN6UeFW8SM2yun\r\n2YItY/WRpBgE9VHI2ZRQtUT6YTGWyjKBCJcLb0JES3fwd+D27MUkD0kT/y4m\r\nDLcu03/tE5eRn30ozo7dBkL98XjJwimIP5MIB816WzgQNV2yagrb10JrhAoo\r\npI6qa7qN0zHCtrFrrosdBFLfWRDxZxr9iWl4r6MM6ufXnzMNo5AEPQJZzl+B\r\n9lp6+jXaWDXRw4RuaVeEhmz7XkNVL39QHCod5fhYJf6EHyLxdmw8RV7r410O\r\ne+83OAG6OdnpmqQ0rHT2I8NDdAsTe1GPbaI=\r\n=maKE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4ac364e55d6d5d54e6656e320e9a4b739408a45c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-0pNnjj9R/Pt45BoZAF7Dat5EQavSEs6PwpRds10Gisrf6jAy7seLdYIsiROTLcs4dq5clSfEUggWjOZpTvVuLw==", "signatures": [{"sig": "MEUCIQDgQrnOPlC/RdpBzMNkDtz0RgkoDXEiRjwaFCYQAzW5VwIgBtNczRkvnc8Z9JoQ70lVF+U9B2eUFxUXcRWK5NMzCYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8piACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx6A/+L0maGyuI6t/6OoZVSD5B1RKjg3oRcNOfCwYxSicnqE8AlEjw\r\neH3legFTaRvjgX1gLbCg/EUjNI78wg/C0XKjJaQwIl7mU2vgetpWUYe+/oWh\r\nz7CXDHCdGLCtFkSaUUnwPP5LBQxh0hOaIqhS9RTy+NL9nMVLbIWWZJESCvD2\r\n2sKkBF1UUzhqyeuzXBNkIkABRxRG4+DpmfVuKGvYqIfhaoBtlMUj6NwiS999\r\n1FmLILU5iRnqRYiB3trVf3LMv+M3Ji4BFcx2Xt37rM4lTIi67+oxn2IgVI1j\r\nHPZqsGE2gTmH09h3Ap3t/zPWCt6tFQtR+SfC+RDAnYPJwdwRTd1ynwJlNqEh\r\nDQLHFZlH8nUi/k4RZwHeOWPlYL4qBH7jWlx41UG8APtFC6qfZFFG059p7Dlf\r\nZACWBrHxF9zrzIYAiTLmidC9SoNAt21SjzNbBiXkhYKw85y63/WdEq0rLXPx\r\nc8zJAT4XwpNY08RywmZgZEyTpRDVrrtC5SLf+dRrPKdlS2aDVKUsxVjw4eR2\r\nIOmGXsMGZIuZr8bkcXH3IbYBSsv4fhIu6Q1aGUkhSpFx3VoKHRueBaoWCphL\r\nnBNIe1LuKDIG/nZKENR0XCVu4kA/R9lvBjjZ3lAMMi4hYtPLNjQ2X24Y99Li\r\n+wIg4laQzudebq5kaYqGqeKqbZwuz/8FPXc=\r\n=B2aB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cb98e2ef4e94816782c05eafdf651e6bb6250253", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-qaXpO2PQaYoqvuDpHUMuMlylwGkLX22+Yf3aQR5l00F782c9jiUV6koRGLlgHb+gEfdlibSou1hAtW9j3TsFkw==", "signatures": [{"sig": "MEUCIQCwShFa6+Ap5wf/Ex/x9fTfvo8TR2B2fURPngMwwqnpEwIgdb2d6bhfj+xcfTCXHBA6BWLmLPKgqMFK/XMjjNza7YI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppJg//TPvz6iO+TwPEknzoBz9KZSCcV1XHsUtU6/B+tW8Wk0ZLYAmc\r\nRQWvg3LGK8FbNjJzCzh76Z2z0W6eVUgtcycLv/85pdoVJ7ZZsQiqdkq+89zm\r\nobfUDScPhZ9m8d35CJYZXe3Lzm3BuL9NQ+PXaa9x3OBarFUH9JsLeXNqEpRM\r\n2a4QSkdHPQuku5W7jS4F1FCCfTt+nF9/zc5fpn7KXtGCcQvoGT8rmzJoE3U4\r\nuOvgZ/oMlycEznE1tdO8vbVMzvFlVUazastQV8zmJ0LbXs5SlCutig1y4uDL\r\nxv0ZX37e4LQtZeWPWjIthnMs/sB3SgVTwrsQi0qrfKUjEHjA0lT11wil96t4\r\nzvSvCAICwfzGGLhDGgZZfUU5bMrMqMz0yMwIFiJaqW6tIaf/EbCWbkm25e3R\r\nEoFI6yMDYh21CmqQl7yJqwJ7O72qnmtg89VVYew3WTUsjauIWtdWCMuH0lOF\r\nrADTZ8VKmNIzpjugQMA9A9ri1G0jjzBXFeQbOtQTN3t6zmfXgyfZ042zT6vs\r\nF6AAxEVEx0QxnivG4e7p3dr6LbgmJ15f0UfkDrP+aZY1aWR+1VmqjsckJPIT\r\nZF0e6q6TXlbek0NdZzKZJmICu7auLwppb6ropMlXD3qDa8asH+Pp/bXqwLmI\r\nwDOm5VDWDQm/XrbNQXFUY+KopCSUxBBmuCI=\r\n=9qj8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6afae4888d93e02053a041c03be194ba3b3e6cdd", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-KZFuxsVkUvAQx5CO9RYSO29IcFNbb412jjFiH6yV6dcEoDaBMiTgpUAhVBLY0xkkp1VazyaHjAZGpiiZ/yTXpw==", "signatures": [{"sig": "MEYCIQDPrLnUwxksvcaHdc+iyPn25C8M+W/RLZXNpHRX7fLApwIhAOdvjj4V89caBpVBcHy6YaFLfjaKILYmGxFER4tj2+o8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXEw//a0Ex2zZ+NjthgaTM753nYGLdCrDcyNDrByzU9xtTVRSZrTkH\r\nZYX9QujkCBXUvQDE6FL1shuv4LjEbw8flQ/5YZ+iahKwHgWmdwVDVagBUucw\r\njbkQfbUkMYV7SUaHZELJ1aXxEp2pH5MnQ66cE3to8YN/2NzW3dksYcGe2Nr4\r\ntlsQcfYrq9SuDIkbk+ttJA8jCibbmG1h6VEtYqr0aybOQz64+pb3K8zKMUhi\r\nJzX2op6AF6odvDrMxLffde5uKQjx4vzBSCPmTFu2xLGqB31EM2TMD+4ODkST\r\nwfnsM7d9EgOYbaDPpYhZhrlOkJSy/5pAsoUr0MYhHy6roWhIg+MwgyL/arRn\r\nu+o0yLWNNFh8ni/mbab3eaLunuHK1aQ3IgTyaO0IhTFuNIauUjpARLHyPqV6\r\nbFHjToX/w6OUAqrGepN5YdZIY2ER94nc4PvU4r3qCeW63bA1G41aJR3Tcekj\r\nlVsz3z/59aoI+dB02wyoZt0lry9MUFj91AQcTo3CswxV0VBugaxXjD1RDhD3\r\n9NvjrPZV4noKZ2LQdPVQos31Lf2xLYMffLnt0CKjkdDslVAdALPpy2LE/fxJ\r\nJuPVO2iEM697mzdozrKU8J0eA5cUen/Lr9RlOgHf9hWk+PC6Fh4sFXh3XhNJ\r\n+UuhZWucnwkz9HsI/AZIw4dLPJyUwDqgkIw=\r\n=FcQj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dace05427ecd41ddce8fe4e5266a247df9c00b24", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-uXMxzkVaKAaPE+V3pbIDvxSaNrnT23hlnZ5Ddpv8obQyHtqV0oTc2R3Jp6XxprcBCGoX9ME7jaafkjaFZUvcAg==", "signatures": [{"sig": "MEUCIDYLEt6ULRRAYxtsbdNOKoy6znMAdPOQxO376swypZQqAiEA9fxWV1et3qcpSPW1FuP7pOY0G5Lrqj1TNImShawaGc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo93hAAnVRccc8NR5cvo0W+iXLZl6Qw4e0OgXoa9X8CFu1n+NtFNRkJ\r\n+QnwdsOs+EC1x/brMovTQ21fx6NVEKILNWYitByEE7Jb5AtCppDchEVRXgO2\r\n6YoLk+82RQ0WEmxhfYyfpSQgcbSFBjj0ipqs6jpNGPLhRNVo/qwgksSE7KXQ\r\nIyYjcWQhM9r5PR1SU+A5+oU05Yd+p/A4thP5RVnRlzgrXtLoJCXRVYQ0DcJp\r\nl85dGYbcdYMR2PIgFMmUh7m0qbtoFqZSC9cGGCn/KGE6/SB5ApBGVcKtZQeG\r\nFc0cZ9NX8AWIvUyBZTYqfBvsQ2+vE7mBwVEbFneyfj98eitgh2KDFTNvl8x/\r\nH1if7rqi79uM3sw7Yh6f8hRozDDYF324y/5O1M/25QE9Roz638x4iJumDQ4R\r\nRL4NWyhefpNuiUZsJfKvUbqvJmmJgxWNKJGPlaYGu4Vt78Ue1uq9vJ+zRXbA\r\ndnh3+6Rs263nHDh613GwKnxvv5DNXO7PNDyXa+d9z27CXtBsgrxgrvdw38Jb\r\ny/L9wrvIcE2LgS6rj5mG8iHGAjIlq0sMVx2psuUIkj9ZR7arjx/65eqI7tCW\r\nE3PTbdBYJhQUj5aEFGdMYMgTnDx+7kFX/BvsTIVJ3Z3Sh+6AqbBsV5KSqk06\r\nag0icjE9p65REa7LQY4heSid1FH6Fqqsjq4=\r\n=gVCG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "91a01c97553d92f6b3683702dd939b5ff13bd5d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-T+pnKxUTCy4E0TcasaN9mbI80jI3SCmUlcLIofHjT8gMfNKB6ctbZVnRZ+8NhkXdkcPtrTbL+xtX3YAlukn3Xg==", "signatures": [{"sig": "MEUCIQDDlvabVmub6kUaLeXS4GArp+ynX1pxjdPqfoKIdILbcQIgGcTjG5a6595iTqgc0K6dw15VbvS+DfPjuJQuGIG0v+I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoE/w//d2IguWM7GBOUdW8y66RCqQYRoKKu1WXvx1iyq9950VJDojQJ\r\nnuoaMa7xxTEh71m1MkuieDw2xXSkDekPMDFaSdpCqw8KMHvea6Rwl7ZPrbT3\r\nvvV0YJamKf+A8ITp4BLVrJlyf8Fv3lWyxGt5hjUnhv2CBXlqZxlR0CZ82sqV\r\nzLkFWNnPstXEvHdpyfvghikKMOq4Im3sgsVIzlQ5GuZHAbop481lHg6tNj7T\r\nFY4Rjaffkj2kJ7BPA6NfI2PxVeeC4FqVH+l5vBrBrA5cNBRSS0xS9KQtdLmk\r\ncvg8hpz2A9AYPKclkij6a2antC6aBRZto5Un3zUTDqP3BNDpquxwCPP2qFO0\r\nxk0ulxacIwaRPt3X//+dOjM9MocY0NYz3iV4UugJhKfa4BWjl22XwDoPMmLA\r\nQbhZyKhbTHyXxJWeaVHJw2497FwCcA2Lz6ImrOkjphl5Yp0Nbqdn/ULbTNQ3\r\nAImIRtZMtlE5i8aq/QttludwKlScV7Rj3Udiezs8T90PAX1Tnoa9Uiv05ame\r\nHUko82lqy8VFTFz6Ycez5oKp2F120bAlnpAuX5WXsUDinNByXeOpVbLFvDON\r\nlYQqzJgklYxalXXpQB2MHAqzvlDPlb4zKQsXo0CsaJh+rMwZhcmW8ktofALc\r\nb7UPg61J0tHO4LUd1TCpMwrhO+W/SffXroE=\r\n=3+yM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "310d1f1922af72f00da4c2f64180fb252bf2e0fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-T42Qw/nUrs1yddExb2VTbU+cU4+HbWTFjCUvPVq3PYWYb0Btt4aDThEgJSSJtjSkdUaA9d/HMorZNUzwKW9NlA==", "signatures": [{"sig": "MEUCICLub0zRlmYpscqewRkJxOBt7NcjyrkNdbblcnkBuNIFAiEAx0B+9yNTKWMQcXgaU0oBNo8ERuKDKzPffKNu9y6Fqn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzixAAi32YjWamXxJn+sir4nobBi38RwLaSsTuevZv27DOb9yKuFcw\r\n2Pn3hDMpBKCVbh1ekTtgv1Mf3s2h61o1kyPk6BK2CtjyxttI4TIJBCKJrbcQ\r\nZt0pqC/VCvDJbNFJh68d8njt8r/NtxTORHBNJwk8jMjks2Fsn1LzYd0cVqhT\r\nSf1OUm5wJPxmz92ZgUjWbs8IL7yeB3BLwMVP06npcQ/gLsNGfzH0pUfEDFCR\r\nmmvmUmWnnfbo0PzZ/9SYb7PgmruYu/5oWvMbYfpIKNQBUxA55m2isn1fknlm\r\nhfB9Xm3i2Iv9Rqk4emgdZMmjLTaZ8rEIV1nQQn2MKHkJHthd6b4DioJQYS8G\r\n29BKjxwm2KXyIrn4AN64KThFVwVNYmue4rRXmiuB4dzl1ePyS8EQu29lsyYt\r\nm7jLQhJRaqj4ccW7zuleKWXqjdL/riNLz2iHLlA25ZtGM3GA8gvQpPS99HsF\r\nlCXwFdIHNPgNWQxGxQNj3kKx50jjHIFtuf5xsRytsSMAJlpGyyAoGnEve1ol\r\ngp/q9wfOmMxTBej/vZh/O535DrSmp0wZUf/WyEF1RSOpQCEy+Gs32fk4RdL9\r\nUFiM0ZqaXD/w6a1z0psc3UDM3kKv0TMc2PYU3aRShuSpVPpAPMthq99rA34a\r\nW3Bh5rhEw0zylnMzfYGLnqrlA15ziB6j/Qw=\r\n=7cFQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1a305e1baa3510e033e31b2ba6d44fabe0fee712", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-u2BKenvX3sZJ48KZ3LDRzuN2JGwAq3tHxBcTk9HuGCHXcj55ZQPgMaSflwIS5yUPmwxHx6D/XJO9na9JJYUc9w==", "signatures": [{"sig": "MEYCIQC2jua7EOoQPZdq0hHKV3OrXoqh+4UVnIgP4dIU/aR30QIhAOhGrNCOrxliT35+xXzqFXNoBmutiQD0od7OysqwXudy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUJBAAlc3eBa/Giuz2kwOnUHX59iyXZsnuH5hyOc3dtoTl/DpPjofA\r\nld3zUNgf+TrM2y2ZqjL8z6ikoa1jy+xE2UDnzqFUjb5SJadukEYcsxT6s2cs\r\nQzF6vger5g0SDGpA7Je+jf3VCo8km5plUiBBUEzH7aQOHxeDCJdHNOtVF0ZB\r\n7CLFmfNzlWPfaUbd0qQvygkXcNNJSCSzHGTmw7NlvMspPwFLp2sqCMd5mqiC\r\nmLtAKwMCJywxnqK5Ow28Q3+oYsd7N2YIXTu/awdo0KIzNpjwjvv0LKCpUqfn\r\nIua7p5S1MgG6TLgkuPCX7c9Fy1SC7rOYjxenSe8U1Lg+1xMjdSI7s+wuWthd\r\n3QYzUg4saOGarg7VIdd8aqIe4HulGPbCp2wGtxqcP8rP3m607OysbdYsrnQ2\r\ncY1pcJYzuFPvkpn/18tdJfXBJR9R3OqCiMy5yqD/uNKID93VVgDrvPZr7n6N\r\nEcGpnUXcNRzd3MTji7uS6asgVOxBYS+cnOFqBQ9n9MT0amXK7rICt32+yBqs\r\nOrtFCSLcPHRvDHbY4CpS5fF7JsxQ4A2xWZ0zjOS/V1KTmK863wiZxmc6FYJw\r\nY6aVUMyDQYmTxJEmN4caiBAXyYHptX90FtIKKNkgjZJsr3oVlKmtYf/EiPjJ\r\nTHU78+QRlRY04WSbYzaDxHi43/hPTi8NqDI=\r\n=UCDm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a5b43f4831a7c8b25bd197348c19c137a1a2914b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-gBs9g0Y2R/hkjNI464ZCdwnbzA3QKm2VVFx0Nbr/SlKU2Y49UfqwzwL6hNSRD1RG/kGoqpHY/BsgP8FiMjXN0Q==", "signatures": [{"sig": "MEYCIQD6A6QsbgrOtUyqO7QcdNh00GEkLaH2nFB2FXdo38RNqQIhAKmEfM7LdkqW0K4/kwDbSuIkawT4cPappCoPjNSK6T72", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih594ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcnhAAm/LN5sH+b1K5E0RIAps1Grsx69LAjVAyoILb1uvEFNnEDM4z\r\nF8VzC5oNWJOp5qHg70Q/PGVgHuECF7xD5ZMjwHOIRUtoZcVTpZ7xg9YSQbIk\r\ne/2giVAUjpYI3ePg7ecWYfZrLlznpUqyz+NT4n6gyQmWC4xsAncCZNLYAIDX\r\n7PG6mkGVUc786cvTIccMajfLULQXD43o30ZwAgy1KzYrFPvU0+PXJrIgnvnX\r\n0OAaVnqoDUsq11yiqjumuK7DowkCOpAao7iRlKjrS4CMgZEdr2mRP++Ostey\r\nM6VCYNK/RWIw6672amLQy8HTntsRD/Js78YsRXi5Cq2D4+qCo81PuajNmxl7\r\nEgTAxhk05xp4c2GVeqGG15ETGWB4Zrrbf7hOWzJscs9HtPDo3v84yVmUfbYp\r\nDr4sR+IgzYG7ayVcOUS2I9IgDs6soZ+Eif2e+SIAxxRkHV5m6RuwmWi89m4N\r\na3/8UOnHEKsx30eJookUVZ80oFfqO4m4TKhwAPMwAfXV/h9oTNpZxxu09W03\r\npCHvCXJWljdveisAYOHUmCKVpf0U1a0+4CGUouAOFAUK/5Tdgg65LOCuzRK3\r\neiEVMNZu8454wPgAQ+GlJrqMuon8c2o9MLDDu23rHJCmm56Wno7DawT/KhmM\r\nnm7aQ3URdcvIyUHD3ioh2M7V8yeQVtXCOGk=\r\n=yEnN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0b4812a535946e6cc91aa96c58c4c0e766b4811a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-HUjTfhZoaRZ5Ywx7K9Ux55cBBrf2Qr2/B8WLmpD9YnGt/Qn0Wu+95B9Fx84Fk/kSkmSmalKdo22F4K/THiKv3Q==", "signatures": [{"sig": "MEYCIQD76xzZtlDqFAwHNqLQov4qLKjpL4H/GuaW72MqfgNKPQIhAO5jL8o+M1eYlO2DGuK9gSuwrcW2LTKFnPu/CWpt0Eqo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQTQ//Yk6epubk+zy756s3+VXa3OhuVxP3j4uu2Iw5e2ThMb6Mfu3q\r\nb2v9FV9QDwKAI/nzeD75x5DaZzZ5C9b0t6KF+fLUmPiDCMm7zPMzc93DwQpU\r\nG+LsPXZgABUf7VP4uAzj7C975MuSTO7sIxe0RQ4o3MI42OMQmuVd77VrHcnY\r\ndiJnDw7A0upTOhiWmsOqDPR3LMXbPUEzd8Hk6CEAS5UvggAMXi+kFcZ/YCtZ\r\nJJyp2ESkaOLMCv41gg6xcdATh9qaJaGl0TVHnPfdV4iGhBTw36ads2x+PKi6\r\nTfNu5jnXH0pdHah+ZaVJK/tN5BLhKhRvxFjYEZHRg/3zxRDiVBcdNx1731G/\r\ny241f2orJKs75I4fLhxqJewmgBRci6mqMOnk82nFOPUv+JhHejhxKyuNFc14\r\ntT86i2wYwJd+SR7nj3P0K+0g8TCB7CWJUJKgBfI5QvJVWM7fvI81YLwesvyh\r\nrcqXbz5OG3RxMfCRZRFvBekPjaXRp1lzNZv3NcVyp49uo2coKmPsfhFZX8CU\r\nCVKOZuXVJTAdPBIrHu2vtv+6zJmS8F5Eh3lPPOCftgqoAnVtsqbyhM1DxjeH\r\n2JMI+djbn3YbMax1vKkiRMeRGECPFD4o2CSTrbHTPUJrhrqWdUhK5q/lRRpT\r\nvvjyEfO67EuYucctGTJiWMx+bGXhnGXgEaE=\r\n=MlfZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "70684afee4785e1971cfb4cd9336b267595c1f12", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-6XI2qO+jrIHb/nMIfzqZJwg4m1kTY9DBwWmwFd3G14BNpuZckY+0sHhYKK2a7pnS9OOkvKcBHL/ubdAwFoEWEw==", "signatures": [{"sig": "MEUCIQD21obeDPToTRYNSdNWLHE/EPVGg9bH5pitq3kbdkAVKQIgfWOd+1KsoxN0wPxDHiESCC6P2O6amFxqJcgaaHU6rbI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoL7g//Zaz/TM0yviN+1qbnfDyK1xTrV2ol4uRKq/Gxr9yYbLXAyy+y\r\nzQY/fjnjew5kAX7q8HPoaWu2037lyxTySYDk2jJom9vsMpGG7qTBnNbRwwr6\r\nLT6oUE1wDa4ksyBhBd7qZ1Qht4YxH/R+69tSXZSaZ+mwDubWuXgbg4asOT8M\r\nMcNRRHu9+AhzMPZ1xIABbGQ46azZj1yn+F5zDroXrXepwW31AwI1kfVb3msb\r\nTEMzOHwQhQmjp2AuRUJrd1uw8GwQzx2S24MiWicivAL9J1sl64bDEJIX5tfO\r\n14pZOtScZjdMg2OVCpxbdDC9PrMscrZkoGD5QiIxGsM3pIlYW/HrZJ/AGszM\r\nqTjzQ6S8FpQMXI7NKcgOExNaDatTLo/qJk45s32R91Q3dgR44oSQEiibSJCD\r\noKJi+LA3K8/ClXPhcSv7o5FZPFQjkWOZgyfnTWo40BH9R+1sE+KylCOdf8kK\r\n4EY4T3Bph+proG7rSm5csocY1UQuKiavNWNQKsHoY6E5GgqI1bOTMcfEA3XT\r\nY5xCriwjzLA8yiVRbRZuP+slEGtsHeKbsi0CRqZS87v9fxj7sFcVew7KVf7V\r\nU77FJ0UbDTM4sUthsrXRryBw269Ca2Ay6VOmRje6XAnDxxf1R7BRCqsFuq7u\r\nZa0PTNsCtGr1WMlWx6TNW9Ik6ExTNAVdH/s=\r\n=4FI6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1ceada7110223432682e35ffd6ace2126e57fdb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-PzajNjvFOgXKROpQEZQmDct2sFqChaEq/JKXGGCEvxfgAp3JFuPSOaCb1HzuHj9FXv8j6vdnt4E0x5TaT+nYyw==", "signatures": [{"sig": "MEQCIE3KVh2JSLIrC3LmTwWr/imxW4ES4Hl7yZTKS/fMvcRYAiB9/8ePmw5688WJXLJ4YMw4s4OCSTr6lgzQkuB68LsWtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqhcg//b20U4Ag1LujAm3dpY9vBrimlCuIHofQ1UBKLYNkXRp5qDKau\r\njM787AfbOcWCM8aCNsNCKsIzG3z8/IAguvWCU4L3OyT3WfsWCCO/GApvTWoS\r\nV77s/58hSoeIr0hbVGelEZmIQMJa9eynnQ4bW5Z45ghYVPHejetLjQ/H9/vB\r\nKPidVIzobRXfdyEaifE9EIRXm+T9+kJifeKbQsn+X/PF8BSojXAHRL0ysreI\r\nAJ6ygPR2QvG/5bydozm2+CZi21yJLTWy22Jd5+JTddH8vuVNJXrzT6/RgnEy\r\nzgLLW7MwB1Dy1hk5yyyDDvOfCgyJ4/1ureiaYjJ6CYRHFQ6Fpn0mqGnh8gE/\r\ncGLxtfCjyopJx0PrnrP2c6fRcVT4HxX1VEb8Kq1TpUUA5PxNAqkMvTiqNj8F\r\nq68/x07dbzMd1gO/TuDk7YhzK2f4LnWCoZVpPqu7UQ0f0Kyeprtentvqaa1J\r\ny+xk2jV69yatSRt3oPWTUC0kfGCZvRJuyj7b9tceShvn3nE4S79hKwojSU2w\r\nr3gLyXpOX4nSgGpmF7EiIYJ3PHLCsoFEUn3xMxLshpYg2gXkmRTMFaeM4HDP\r\nYcKsq5QEvp9hZNHoeX6w0xi3ho89h6Z1R/h3smE/lvJQRHR3qzyM79glY/3x\r\nMDLrlI35F4B+T6BjVDFXZxFYsjrC8Xo9xFM=\r\n=ayvm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c782e26c88912b28852dec25e5278a265e7103d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-DDtpJG/EVkMlR/qVkujKPauh4jt0gOtol4CLIH+wbGJBTyOZ9N+8gobD1EZP33fHI5Ukn18SjWI6V7KYvh8XVw==", "signatures": [{"sig": "MEQCIBIF/UIwVy92TcvQLB5FSa8YG/4xodvwvHTKYcbtaTR0AiA454QJcv3XTBsSHwdO1+U7D5vSJA/Ps2wL2TL9r+N/xQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrByg/+Mr6oqww3S6faKP3vhY+xwt7z7214sYNEPgYJwRMaHtJ2e3t5\r\n1mXsdjKfvuNTqbaw5WM/gqrLCG7tkVMNdmgtg863GtX3rBNUHJPu/y4699UB\r\nSAk/O6b4dI3Hd9VFRTv8B/VbcFk8WWDhpEGWw+R/a+KBByAGfORkiPq+L4Fb\r\nGBmgmQBLiBYd0Kb+HEmPrf7odEO+JkEudCgT2rfLsfgdIGymAT0rWOu3WMxg\r\nlqx/oQyT/6Wl+W1lKVMfiV8TAkm6wEYPFrO4G5MmfWCjO6CtrNHIHCUxNH6f\r\ni25WyqsLhFe+Kf22tyPeYERz4ujhT5CkXgJA7bzu8t5v5zUZKxkL3vDPOR22\r\nJrZWySoDo5AEqoIds4JkcOQ4RYgPc71126QAecBSDdBiIXqnGhhabYdjMpS7\r\nMQMH2OlK/RNhpbJMLPjS+FrYnNK7sc161pyDEksIFTNqUrgM+w+7axQUWNvK\r\n/PNIzBwJn7Jm8n6CWGLfRJcJ7BEczeNrqlVjwagIjbKprx1zzI++1OEPIAhO\r\nc+/yB8HtylxaIRTIMxvhsrckU6zeRhO5vkCtIQV2Zt+wuNUvw+78jJdLM7c+\r\nZDp2WAl/lkMP4vV9ncF53ngA8ii/UGjGppqLm+SYQHmCCltq6FggpwTWtvc3\r\n+JcZ+8iwJmJcP6ajU7FUnkhKh3peEPQcjbg=\r\n=s7YC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9e37e775343460c09e9ca393bccc535e9395c0a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-53KfWowDQzAx67ijw1wjGCD+jSdcC4I5zAwiDzRqMFTxgLCZfa4ZC+Doh0BfcHeVfzlHM1rv5H/D5U4Un879kQ==", "signatures": [{"sig": "MEUCIQD25tAMrHw08Rysw80Nxg4MsRHswe7h192VHTbTyhBusgIgLKXwpoXPvcZAL5lHgTKxLQ6XYYw2jWe5/XWgjk7K7Y8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDlhAAkW2ic35r6O44mL0h5aeL6Qk6AU04vn3o+wxCSVYqy19cpg/p\r\nt9mFykOhc+PzzvOPqK1wCeWllSw2aVnVyhx2awCrTzhuWRE6M2X9xqSt5++C\r\nPJZSLNo1y7WlnjpQ95c2MplYjDv+rwxW2aw4aVscU2qYRWPzOP15UVyrnDWI\r\nyKjxc+UWBcvtUvVFvgivyvE767nPS93wa17IHRVuFjPHuuk65jagWln3mRRK\r\nEMn2ijsCp8xeWb1uL1QLLs6+5ARv266Fs4hfD5uP4uplQ/tWqdAKAXvWwoJc\r\n8nkk2r1eFGlxVJNfLMjkrMYDSKPOT9ip+QpMhJJudJGiHCxBHsih/6oSAfl4\r\nMEhtAk26tIyr8xvAO/0McNC6t0R7+YwhaGB2g+bo4EF79jHzdPZBFRkV+qFq\r\nr/tC781rhuYFgBu8bJf20aYxoMVFq7es4eoSTZi0xtOEXU4Wo93ZcMJezOSJ\r\niWLiNm36NhvGyDx+iTR5H2b8VdNOJkVDZcqV+Mbdb5RNjp8BK3Kv0lZEc69D\r\nc16pFE+SZtH2vkK1NrW00D0ddL2E435kEUEwUz81t4xLU9geRZ8S3k/v/G2E\r\noGay84j9mdMAznu4KgYXjlu0Nu4ulm6//oH2mqm/++Jff9VqAKY22uPMgQNV\r\nugpFp3Varx8FD8aW7hRtUbg5ImFWffoVdaI=\r\n=mbAF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.27": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "696e5e06f28a014d52951dfbf96d5157c78abe8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.27.tgz", "fileCount": 8, "integrity": "sha512-KB6z2P9BhqEfTCqI8h26tIy1yatAI4ubHTLW7VWh87eRZzFWTAZtfd0e0e3sn8ZgLr0++7jDlZvHBnZQ9VXD8w==", "signatures": [{"sig": "MEQCIENkh5vc10AZ2oP6xyP5j5GEqnjtlyHPsGP0yIKvNCiQAiBmWnyzGZLdA34+VxosqyPm8Lj6/anOvFNmAkrlseAnRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP4RAAgxW+kJGVbhk2XV/zF3TYkJHzzBkQtP88X5fBq/e4451wJCCp\r\nH+mlsljzuTGnOqgaJwMCQFhjigS/fq44QCNWgkOsbAcVitOX2l7rQLZlwglj\r\nYXqBgLui+YfyVbcKMgHrQyCGJB+Fq243Hbxe+D21FsyIO038NKjj+BaSswCM\r\nTyDeD1VQ2Sb3TsA0zWCwj2uf4nPBZb/QrnvI7qUr5NINb0uqZsdkhHKTtk26\r\na+3dH+xmQ0s5paNfMwxM/oUsN59pGF9oP54xFFxj6PjFaEJ4uRtZwsXyOPwK\r\n10hFjU4faxAqLUiLv5ZdJlSvsmrSDVb4DI/mmA1x0PCm4QFBHax4CDWLYyug\r\nrO8On26AKmQ66GwLaxrK9IL5XScgCzFDbtXCMDzX3qA9ju2iNp8y2ULakVpf\r\nLF+pfh8ealsJfxF6yI7qHq2NFelaBrcXdmuQefBHpFj+rsoEv5wqYSugIyJX\r\nliyhLXIyk7eAEijWt0vQp+/wo1+qsQoOV5qyWd7HRuut023obmxq7CGov9IX\r\n5SQRsRDMfNCqH4h1jbrfASJAjk2CcYVMSPB6Dy5V7XVz9HTfZwfcz6bIFcWd\r\nbW48/irkrccGko5/DEfsHVj+WCx/i9bl6q1YMRTZmZDoDZJDPKroDuaEl8NW\r\nNExsq0MqivgZvRrEI+8d2CZHWkHYd+o7468=\r\n=yabt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.28": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "106161b42a23d673edc207026107ef545a7c767a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.28.tgz", "fileCount": 8, "integrity": "sha512-FEFzsS3RwGCh14L8hnI8ZYzVeFiPt9c1tG9fwQKw8l9mN5oql4IxsmFRA3u50NJ0LLQutRJMVeEig7vix6hrJQ==", "signatures": [{"sig": "MEQCIDCS/EBo3rWnAiYIaAQTIYUJJ32wOxaef1WQ6Y4S6sQ4AiAaDPklSOCsWhXUg87apmil2bdJzsZwOqlMrMgfuli5ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7LA//VxtsKkLO9Z62jRD/0/vyFkd3HYsdZUGWuqBDnoKKVcctQUc3\r\nBDhC30G7M0kv/2S7d85Et5GbJUARVcpIv7t3HrkbHqiR8PXoDhi2NQC5E+ST\r\neqJdtJwOV7AgtLJvEYuGe6s6lZ6nYGnGEVc46spQqee5FgFesEQfJiB6FaID\r\n5YbHQMv0BPG+v0+4FLaDG8z6x6GlL15JMz+5PDL1zTcq9ZSG8NuYHY88pt6w\r\nnR60/zc/9x2vEd9lkv0JxbUfBpzBbo9Wq+/pjZveMgBRxpe+AObU0nHk7gHE\r\n2Yx8x8k1/8+lEfifQoWJCvstMR3180RblWWosx6DdL7V1ETL0ARYlqkhbI3Z\r\nyidD7jnO3HeN5uXiEIrNxSmpfOrx3raMUBk+P3W+0CDRPAx602Eg5NoVBhEU\r\nNusD8mFptjkjLeBhCgA+sJi17sUmrDakFKj0jcY4erThkwaVSROnXfr8qB9B\r\n+GWaT/l0//WXncNVUcG8iiSB5w0wSGGjA0fNE7UxrY7K5AtIdn9OIIunvdKA\r\ngFg0/hN8Xi1fQErBDuMmJ1WmyOq0t84PZqFVfikefPdwq6skr57c04pQRy0b\r\n+tJO5NeEByVrqoaDweG4W3p58BKCPo5l4xU8y9xwuc7uKlnh0MdG+4anoeuV\r\nJXhp/7CeufJ5FRHbw31WHDTexTSeQSItTcE=\r\n=QzjH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.29": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad34a66b418b6c7df30a6425e561fecf573865ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.29.tgz", "fileCount": 8, "integrity": "sha512-fT4RYuULrY2rTpOdyymX3YaUVpnDbv3IRdR7hQXnNsHti8Jh1xOERUeNStV1rGDIiIq6SCSH/vJLO7lFci32hQ==", "signatures": [{"sig": "MEUCID8f6O1io2qKfXkMIsCvbCjEkr9eGwVYBY7D1mjE6EPPAiEApc4w+3KqAlNRohTTh1LO04mMH4kT2VcfAOU7Kou4tj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0lQ/9FEp9oZRqsb4n1KthgRY6H1vw8luMNsUopM/2k3TV4py2Rbvj\r\ncYNgR0eRofODwTRESvRaXUUOMyf0Vg9S0nMMg6ismhq5d4ZgzbwtnKDYDwVL\r\n7v2kckL8r+ztEBKLpNVzI2NbZgKdjF0znl7HjuVPWsb24zEXzMr7c3DfCI/X\r\nV3yBUTtijXJp4kogHt7mD9b8oqxpmztWQyQmMwwpRqFFf5Z7da4+tsye9OQ7\r\nHscr8/GaQ3wjOAvBcrqHSj6fTIunIvlW+APdWXnSA+trBf+3b4IlvdTkvQC/\r\nxk3b3pbtm44MmkEi4zo6x7MrfKbKxxFBTRwzFMxD+Zf9+o10+gTnjYgrfNiC\r\nScpcNwZP7Qp/sC8sDFz0qvlLRJHqNVq4YWv5QoHg00ez9M8thhORM0DCsOin\r\nh3KkBE4Yp1b/t9sD4Rw0Jlg4f6kCrIuSouq1uyzqqWGj+yju9tOLb5kMhsQ/\r\nShy0zH8rk854FC3A0s+yMUIi5gPrz/iSxP/v10O5BeA5NS3KY3PT5nOvcC4k\r\nGTQbef1n52tbvVSIM9R5qmaVaheK6ffP19k9SZrmlT2FDzLzoh+JLUK4p0VT\r\nQT79Pu3g7xpNALkpA9gspTsphK3vZgqJrQg3eosewoUDhBqvDgwCReegXHsc\r\nfsGknzartJPcDou0G52hawtMqFy6JJlVCCE=\r\n=J1DS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.30": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed3694b837837a97e57430bb522dbcf186f8d92b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.30.tgz", "fileCount": 8, "integrity": "sha512-ereGPl/9oYUIHuyz/P0Cip++Nl9iXCGKWMzOBi/RZL4GmRRVf1NgIAR50TWaLF+FTUkT8Eb1ULec/E4GrZ2aCw==", "signatures": [{"sig": "MEUCIQCTSWqcL3dETIP0fo8ajeY6G4R01tx9xEfMeuYjbWrqHQIgMeKbl9dWd+wB6dKxrD6SoZaNSdRIyp7Ecs6ycmmFfNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9qhAAnGPXJQfiZPy1347OBDvZxMddRov3dwLLTvRc+siUK63YFkn+\r\noA7AzEr7FrpfRcVmLC5f2/a81nty5CZvYFK5z6X0RqsFs9ZEsUuD8hxUu3xa\r\npetu3CXKBs/dPmdINRQnztGPz1LBuJoZpUqc7Q0g/alb/+rPGQelvusxOzjA\r\nrGK2h56gO5Sg9EAgps8msCQh3fb9Gezgs3OpD/DADJ+cj6kKq/h09fo58YFb\r\nOhJwFPXBTR6JAQLgwLmicwNO7uV96AlwIRojkiKLRsPN15OwA6RYobwg4dDU\r\n7ElInuHd4T6FlFIWprlMPX5Ohb1iN12ryFO4xcPlppUdAyNorTIq5KP9I1yT\r\nogqUvLD2jPqa4RkieoMLHdxkxsk1zLlUfNNc3ugriH0H4662AcNRgwKqoloG\r\nsfx6AaHLpeHl3pOChQELlI3G3THLqnwsJBYgMppmt5efliteRd9rxcbVERQC\r\nz5adEyTiwiChsxHIIsyLD5IGxnhJakEtTtG7OHlCX+HPRMr6lSMIs+szVd3x\r\n0Nx9iainD3OqYEky6XrBwIudoA0t/3eVDVEyNoMEQMlfBUc7Q0tvnTlOZApq\r\nRZzyrTKfNZ7xjxPjK5S+UCbfGlSP8rnCXQ15p70wu+qF87NNXZ5UR+m0htuG\r\nXVkd9nI3b3RPFZGhw9Hn7h2caxDtCHh4uyA=\r\n=Bzhd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.31": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aca95e0425cf5d389aae2504fadbe5dbf63ec628", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.31.tgz", "fileCount": 8, "integrity": "sha512-3sBZxxfbiwBRiOPgoXhf+Ec667HSt7UHMPvgHD7KCAH/RXmRxdcmPGt/NsF2CZDFSsT8d7hrzvVyIOQlWbBlBQ==", "signatures": [{"sig": "MEQCICS3DWjoQba5Q1GEPAtBBT0rnNkuTQf8fZEOyrtWaeKaAiAONEaCqZN6gU6fAKdFAqmqgjviwF1qwlroFFJSLMWy5Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr15w//RQXz+koEjF3S/m2tkFh9ppN61ISMJiHUp7fPSxRLss4VFKYN\r\niWdKp8aNQue8q4BMdBR49ByU5md32KC+05SOfbynMgKP/IcoKvKqyZSSdWHX\r\nGHMv2SxcBcSgVAbJxjMVwYGuRWpAOciPs111L7otWezdi1MM3gYdxY1m3aOP\r\n0CqX983zI1bU3RuUTLkuB2fiQTrovfATO41MZlmUB5gMVbbjrEK2ivaoanAq\r\nd6TTgG+ARp2YqCIqpOWlp+irtfRtuAVi4ETdmkhu9kXcFLtUuBKi2jBaLur2\r\nUdKP0skShCAk2Zl0YnI4RBEswU7WBuo9IUHg1+uCESpej21p/nueWQSY8ZQ9\r\nK6VhfJ0l4A7YtZxFI9zpOsGwgYmBfe7Uk7GfOEuN6OTVQTDEvp4hlS5T/zgI\r\nFxbOER0vstGH8eN0dgTHibIdSscR9frse36JvAclAY7hJr1v1Q7UudvZzptm\r\nc9p6Y0CZ5vIzV39wIrg1RXF0iyB+PqXOl/UJeF6ykT8F5w9/6xau6GOAt7bq\r\nmFVuyzMjqdmos4X5QTolMWteDo2tKABx8W79YkR1IkH98aPIrtYfVlw2Ia3/\r\ndulXTDLyf6oUkQUgtYDpk/8wefbeTEedQsPyxlxig+cM30YtmIgVFIxAO4qm\r\nDyeq+Ml4BhvfsIKLEKn+yzZJYzh8WWylJAw=\r\n=k3CW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.32": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a695f37177367a5260b2e47a578e5bb990380ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.32.tgz", "fileCount": 8, "integrity": "sha512-WzdIJRjzr7dS9+PySQ4/nGWUxhHx0vEHQTyJ2LficFdIpHmUdO27dwGT1RFEb37oNATAjxFuiydF2kxm7rOByg==", "signatures": [{"sig": "MEUCIHQ6lkkp+LIjRmUhvV8vJj6fKr/g5wxuymuyPDVFFtliAiEAvXChlTsFjvI7/eR39pzLyfMCXeSwlLGqKFnCJvv0AmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbVg/9GY0nyeKisQ1Kd+AsqkVbwJCbULf6Kxa42qJTqLk6/Jt2metj\r\ni8ERf4xB8pbaTbtOt44l3M9xit1ynKfs8v3vxoooI3kXX+nRL6a8x8l39uvB\r\n2PCPLI+5Er5qYUPfyEn9xlFa+b1T3g5rT+Zm9KVeFw8U/P3+FGxd0sSPfHDR\r\niMNw6HlytwjtZ6fncVMoAM5i8vYgn9fUe4xKECMwb1DGmYMyXLyWTQ57T0wV\r\n6rJ9YaRXX//SfysrEZeVytA1nUP1+*******************************\r\nEX+CLGhR/OBVL0r7kh1F4yhgkcyMDbxeidBySkiRRC9Cq+Vpx+1BwaDnRa4k\r\nfPqBn6Ub8CXAAnsIIBNdpbqM/0Q2X3QvVcB/uOO6s5yYwc0uwEWIWBTcInh7\r\nZTSSSsIoRdqzNddYcnKii9dt8VXWLZNuAmTcokZQvjnX4Y6+HxBSEi/ANBYw\r\nKwwUD4WCN3ugL4kyTlWpxqGz6pydAKaiUjo5eQ3hJrq/R6H7xqIqUoANJQN2\r\nqn0lwwbg4jtexY+A/+1Phgrdy3t0EkGHNsZcyJ5yJ7QQauMArfxddCVj8mXc\r\nry/jW5e8CfISxfr9pxPXmKW1KiGInSbH3rrSKNlLSZUd7Xe5eqzuq/WoIn0o\r\ncFoVj2lPOiV0Ta9xMguegWCXnbIGJDVTqTI=\r\n=Whfr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.33": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d7fb312901a7d6c3e3f50cad3fe9b3e6d7422103", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.33.tgz", "fileCount": 8, "integrity": "sha512-K6Zpu0z7fxKU5R2Dm/VwsjUEFM9xZP8LMzcWKDbMY0lj5XJ+9cg7GYkJ8k+1pdLG6BQMww/4FrIAHP1VwS0R+Q==", "signatures": [{"sig": "MEQCICNa14FGOvXQz5/ROh5S6bQbdOFQSSnq5qR1TBw4HsJfAiAPkNmod7P/VZBkbqr8ZE0B5XzCnsk3l2NS/98+91neTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8Gw//ZlK6ShBUTkiowzsqxMmNhBXqy9qOgVn6NOjG/wF1iHMY3Rlg\r\nk1pEuggQaqElDCCi4pqpTph+R6auXJBfxyGnVn3uRkQMA7TXtirWSPRTuRwv\r\nbw4Usa54OpJnqVZUNIOkJ4UpfIG5tN+1gV/eJcjfcDQc3xG8OLEY2MctHOmP\r\n1Qb7KeS1SvRuj6Pmtw6WNHxhY3J5cfI5TDUQip/qodFot0aqIsD5Wh8E/Afc\r\nNEtSC9SITJ/1a1cXCfghCSw6jFqns49rXpWh9PiCYoc0/pW3lHtbo1ACjvN8\r\npkQQ+TSMMzo+eMr5cOsVz4n5cy9IG8IHF6dPhqBI1IGEzJ0C8th/hGI/7dYy\r\nBF2m5OeiHrMz2AmmsH738l5U4+9eFFgsz9UB1RnwUNjuRk6JzHeeq2gIi2nw\r\niuAkbEZWUMDDqkLTzzPLq4w8Ihd8tXe+od/Y/vyB8AE+K8/L/jcFpdoQgmEX\r\nTpdfTazmHBzHQE2q48lfhn02iXXWB5LEASW/kPhW8BUrFoI5oUR985pdhF/X\r\nOQN06vr9x0so00KykJvEnHjJptAXt39CnvSMd+/dBi0Qwp4EYxFyuFACDDHZ\r\nFHzOMmXmpoQyKRrWqwkQ8BWrKqpCSwVL26bNcpE3ZT28baVGiu2RJPuUkrz0\r\nF5HecdKfaAcKIKSiTqMfBUVwWECA3YdIFmQ=\r\n=DfCB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.34": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0f55722b701b01dc2f121176cd6fef8cccdc83ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.34.tgz", "fileCount": 8, "integrity": "sha512-aL7uE0KSD4A054nWbCzFTmI1jLgvCGaYIFsHW6Y7BdlUXVLrKccvzdsS3Gkt7IXtfaQUvNKBpzf1WczrwWhNeg==", "signatures": [{"sig": "MEQCIFtBIO13oAU77zVkpJ+HwfGQdXEAJvzn/OhKr76I6aYoAiBf1CLiyAX1oH5rmKpguOwd4buOSyT64m22CRjx0JbjiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH95ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMSg//UqQejMlZKlKLQxNk5b/Sp1hl/OP9i7A9nMBLsRZuuJnnHYpX\r\nkEnGFJLMFljPvqq6/0DF/8wKdNV2zUmKvhcvRK0k5nXN/zu6yvV/Sm5pDOQM\r\nREJhp6sas09FB8uMY6mIQdE9wIiuAP7LppKhoGUsUnOyycwcTDDGKDZIQcY+\r\nc2iq1AZUKiyTxouO5mtxkDiqqWk6/63nGtp2Ya95NnYzcGJf1amrlg1ouyyK\r\nEAcAPeoSTaacFyUB5HZ6c3z7XlpZp9ZZY66mdslCQzzJR5gKmqfT0Y5YrB8P\r\n7y2mSoZ4WTr2ZO3ogxLCicrSV1WKR8rQLr6x6cZSWf4EYY5u2b9EOrafuuEI\r\nD0On81fPwlYRCATFL+ThFyUq74nu7sIbyiJjbJFfmRopsqvlki6+zuHLJR7A\r\nx8n3cgoxKe1J7P0FulYSmKTzbARBSA5jY10kmmey8GKEGnVeb4sBjsEMDJfJ\r\nsjqwBvsOemt5EXEwipkVGvRmDilVHTjqx41v7Vtl/7lGtxNFhppVCpg97nH+\r\nz4X095YfCprFG8H0LRndALdcTB2vhTFfLi1EiytATFvfdZ0Vy5iJNZS/0+25\r\n3WAkDcpUqLD8UzN2HWCJ4pEgqrqocFmwXvMBazTwXj9h/K72K9NsMdw9axUu\r\nlLXqj1GZu4jPWirZKSPZlSaGmNpN2O/37c8=\r\n=MOdm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.35": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "663be8a73efa7d0b43b05e6b279d6d02f9bf4fb2", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.35.tgz", "fileCount": 8, "integrity": "sha512-+kfHws/eB8J+mb54Xs2aDdHg8IBInqLAg4gIrOZa6OMiUh+8yw4Kp3DT/PqUM35sEWaV78AbXT8em83MS4crbg==", "signatures": [{"sig": "MEUCIArHcczZMR6/410NeQLW0f/fD6eRObCLVuVPiLoFBCl4AiEA5QP8+pukJ/Q2WVzUB0465C7CZApkHJtxzulDeLDA9ik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2kBAAouhIrR9VPoS0ECEjjguz2GR2vti5hQbyptQyUDKbWGDJS4IO\r\n8uoeII0j6klOU5e+Oapjyb3d/V53bKixyxOF436tDF7asF4rQ5Xz79ogpwM3\r\n3VneDNexvlSfIwEtPFiiKDQjX0QOg7UwA40h51E4CmUdSsD2yPXy5zyS72rv\r\n429o/CiqA2KQUhPEmafEAo1LtOBWZOVrxZxAerYXfVKFBSp5Ibp0UIR/2JCP\r\nS+9RcndxxI+MdLPCCcSjEn0ylpbU0cP23nvpkUu2CWyguZTjo4P5LoVZb8bp\r\nDcjZyEURtGYqBVom5o6ydEk5b9jYpY1Pfqd6LMK+I/cR0Riiy4L13hBLheLC\r\noyKC2plwQrRFEzU/ImxWzCyyzmFcFqpBorB1L1xvPVMKmereQ+xa/lI91iDu\r\ndVpojUcB7TuBCLLoVHZWSOlXlcfInSxF/CKNp1xSwAtW/yMVHm5kbotIXlWs\r\nG5muS23IvbqgDBjI7V+uopd8okXDWle7jkVAFa+Je1z6XrvXCbV5rpxj3geV\r\nk41uoGrO+V2aaOYeaEP73/W5QOn6lKeTAK9I0WD8n5cea4GEwtJo9sw2/ZhP\r\n4WGsFGhdJQSchOtgfdTWjf1xtMvVzKyaBeIesGQP3bNwRkx8g9idiG9wz90K\r\nWhJN0aDbgI20flpF/s93HuYOxkkjp4n65uo=\r\n=o1zM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.36": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42ad2644b5f02f9160e799674a92140911be0b4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.36.tgz", "fileCount": 8, "integrity": "sha512-fVObxiGHd05xlOM/8/dVUEtofYfjunjavdoxyiQ2/MAZzsVydJo5aDELeoXR1oaUzBSRDXeB3IHjtobtM+ybsg==", "signatures": [{"sig": "MEQCIDIIciW9XYgpTsX4L6aJnNryqNZ9yarSui2pP0Dq1A+XAiAJBgImDfA5xnAGzhKzbnNK7rD+DK92+AilP7/kE7elsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJyw//dPscWCwrCP+Vpu0HA0YRLgaF8MawxBxQSwCCczBeC3v9fmLg\r\nWmKcNPezZrti3kxRI+7dSSsC/rTRUPO3CFN80qYfdQP4bdno3MnC4zi1s1bR\r\nKGEChb5fYFMYx9rr+w2P0AsRXOgr9dZhb7p3GSb300ULEvrcR8U1ZfOEa1/f\r\nsxzUr0SqXUxqovsPy+0AouXNEHItGg5njhQaah/Drg27uOve8cBqO0Cqb41N\r\nwdp/SKuviUR5Bh5vQwkbTEbXGK9vHbpJVgqbkOuubFkgTYyxHSuETyBGHonH\r\n8kwWBXVjEf99qpjdSaD8nkehFcEhTQH7vd8QMmpyMquKOlZlYaURRNsXo26U\r\nl1N80UZlSy5VDYGCM4mFbzawBB1mlnEtHXphP7s8jliwKZ2Jo73vsx3z0ukx\r\n4khFebwK2TRECUBxZ8sa2I8BnoXWoPytdHXymb2egEHjv6OR1Q4GYjPw1Wa6\r\nDWs3iLopNtts6WiDOdbHL3snoPnXn8VQnyFQoRRMvXzernMwP0QOvSC3sxcs\r\nEb0vHZQJr7B37rrffQOlnM0GOm1t3IXB4rn2EVhP6Vf45yKisRPMmojioZj+\r\nI11zkwFesf+UA10Nzfy3e5oulEqrwxWVzD3JL4NHiVFHQ0Px8Rqbyrr7gWP0\r\n33TOLksRk0ZOIFsPdkrC6NjQ8EWtCoceIuk=\r\n=/b1y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.37": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b3db8b9dcd226ab8fad29a66824d91e9d667cbbb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.37.tgz", "fileCount": 8, "integrity": "sha512-uPUF0SwxLJtFmy3zAacVfetlumHp4Px7b/t4uSoQKcD28s7IWQrIdv20TpqOghC/2ii4JHSpGtndbYLg1zAMwQ==", "signatures": [{"sig": "MEUCIHO46Jy0IPATaszLc9+v1rL1e09Q9lRSlAdmkGndv1S1AiEA06Zaa2uh5ANX6IOyT/gFAXWisVWTGoQJ04l4BjfI3ao=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgjg/+ILMYrrT3YuEsz3C/+etaqcib57nTAs0607zh/nk/BaGvcf0j\r\nF6ZXKTh4jYYPRg89GUt48DLfqo/Xx4uLHAIhxUOiZvFwSINTtMJIu+4Bh6Ad\r\niIoW92NirTGdc9WdpZM0yZaY5RmADHoVavvYFD7oEi+QZW+HmUkiu/AzCsEd\r\n9xVhm/udEaeBBz+2heL/Ch8Sn9/JDaSmySdx16J7uHrPITUmiusWLNNM9PBy\r\nJONU4Q2eqmj4VaPy5n4MQrV9GBzOk23BUt4QZHVU1tnZK3+MSvejWcgbfCx4\r\nlaWeOYYu4IAkB3Kazr0EwX8ZmJiD8OAxxe74MeixmFBvBfT2jeSNKWVBmQjB\r\nfCQ4U/W3fpV67FJkEryr/3Oc1etC/Mv9m7Ih1Ku9BHMlCYA3pGDBgnJkU6E/\r\nihCF1zXjUrTEaa4d/ITTeUYuZ/BmJWK41yUo9MHgZfUR35a8ct2hhNvJRbhU\r\n5OXZy5TZsSyAFIpyhrDXDUXEQAH6qZ//NvY0sJumk1vzFDpHmLmy+3q65l0V\r\n7FY4Sul1VjYdMfT2dX0OfNVbdDEdnS9VJ48kSVRbEr7fcxboDjM9q8pKEUCh\r\n2BnUQqU5gI3+w1qIGjDWL/HHwjNZvqnNrtSN1b9WSGmAB4iZs6Xx+ZNix7o5\r\npSCnJq4AQzvXO12DJY3snteJnF7jsD3DXzs=\r\n=fuVi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.38": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "443bf6473914c2648be7be12530aacf894570859", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.38.tgz", "fileCount": 8, "integrity": "sha512-rVZ05eW18Jx70yoimrwVACnjT8JEV91C14ugq8lZNZ9QzBNGVyEyNWMA+5MTKyUOW+Px2BbyntxEJrd3OUtYlA==", "signatures": [{"sig": "MEUCIHtL5UnDGS0gCkw1S01nErAz8GwzmduQ09okiajB7i6aAiEA/IgUpovqguw7zfpkZ954RbOr7Khiem0q9Y6RqEzEGIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosPBAApH7EbXddfQsgg6Rv+mAdj90Jbem9OInlO562RXjsZ8gAbgIS\r\nT0DbaApFvDIsKYcX5W7ZmWVFfwAut99ZpcTUCI9rMaynJ7R+4/jI/8zrXygy\r\n4qi4dKbFpNcw/OV/jFkK/750lwEzZAYgHEULNl984Gy8N4AGDeg2q5ze6hZ7\r\ndwvI1vzvAO3OIhuw6lXCdGZ+PmnVjoRvFp2ApBeZYlvZnQwwk9bx/ggcV7/4\r\nCzno9YP8wYDT+Rk+e09nwBOxr2anVkHBbhORpM8P+85bqbNjLseUWPYXB5gZ\r\nJFMqhRjlWUoSa3byjzeptBXm+xgjN3X/jtFLQ2dyxMdZoZFidy/WZ3R9PEYj\r\nraIFqc1CZUAsVgjiINdvP3jvTtrg2X4irpwctaazOt4jVE3db8q4rFKo74r4\r\nQZ0KFSD7d8+2RO8MOSG3F1ydT/WXjuTES89w+iHt51Le3LuBpzvo002OTkJq\r\neJlAphnck7gADln0AbufUGWCezJR3gZRqruDC10WM4tTKtIR2sMO+tT3jO6D\r\n+hXAT3omM2ThIGkECQCwZ8aOEsGsRVt9uzVDWWoipr0I6jlyOFtsVs4oBm3m\r\nj4IoUdwjJRpD+IwFkIaUOuTGgQuSsyN4y9PW1yxDFt2MQOsPJ/+1uMeMQiy5\r\nfwJ2En19peobtx/LUKz0pn3gcS9Fu9baUEk=\r\n=3hY8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.39": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f21db0d910bf41f3041f3a611e9b59aa6c3c6bad", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.39.tgz", "fileCount": 8, "integrity": "sha512-z3nNaUKfPB0EjsbOmaeVjQUlGZJ/ix4wdmhOOEk9hjb5cW5V+PgPqzKx2nran9lwyEHRvgyoK3lH3fGKfeocXA==", "signatures": [{"sig": "MEUCIBKKQlaTX2LtMW0BAfJlvTNxayj01Zbyw0ikfEkPGBLwAiEAn2+hIahMtH7ysHR02NBYNsrfs3kA83Kz6TQmWfRb4hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIyg//cNDzF/albdSpFycXVjW8kq71c4GANq9RDsCKfoerEKlLpxr7\r\nhlzcjAdYAclxF+AGtRGHbEnBKhdMD54nMyyHFL8SFgYnZBbn30AJ7JO0j1xq\r\neViCg8AAwlGI1OTmHJ6NFxOf24RPP8fVENJCZfFXCrrtvV765SI7z6JxkLyC\r\narmnx2ViR1Wxnf0kO5yiGb02q2WsB4iOYsltgrPppa934tAtQy8XNLLZZZ6I\r\ncBUeoiSM2bfzalz++fVGXQA5tgm1D3nDrn67l7K8UFXSOgQofs9o14e3jR3K\r\nkEhdpcVz02P7V9WjZvgpt/4HPxK+jp1WR+sYiAZdq8o5yWNWOlkfVrororj5\r\nOUblYdxWE9kNnIqVLcGZRQoyVKuQQCkBfuNZQSQ7MGMoMUaZ/IdjnVJtXOmj\r\nHXzrUMTXwULW3AT2OAz8OB9hQ5kNA9Fo0TGCPQ1Y620umh1I94KRLVtHzW7G\r\nZQkCQGp+RUQTQmOR6GMcy0U4qx8YlaA5avSMfLUYFjjTLhoQX4AUGG6gbaLY\r\nKmR3lCTVE5UPPOZddNBLQvz02l7Hnwv0sXu4U60mEuda+JBWhmg0INTpkD/p\r\n4lPH/kn4cs4RxEEvSZQukBuG9YZUUZ3wmZlBcRrWfXUbI28x7D2FPcUgN/G4\r\nP7IMbo0Plq8b+iwjX+njuXVXK/NvwMaL0Gw=\r\n=A5qg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.40": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e691007f9f6ec43162c964c0538257bc23c8e61b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.40.tgz", "fileCount": 8, "integrity": "sha512-3ADQzhz9mmItDvsrLKqAN+5HVP6DPiExHixO7XO7V1o1+9vliAR2DB0W70NmSrQutQwHQJi2Z6MrKbB75DIsjg==", "signatures": [{"sig": "MEUCIFSxsS3m3UQ1C14zPXfn7knW8xEZvqo6JSJ+FK2nZqkBAiEAnoyG7BmSeUapT/uFwh+eLdTbzDIhBh6r1GUcz2qQSJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0V6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqxww/+Orm36lLXzO/DHzmcBIetNUsGlbmv+/Kf4iavnDevCplK4zw6\r\nRelLf23BBCYDyq4T+cbJm+BAz/0s9HIRBSLG9pN0EHTQac3OS4Fz/pnBJowT\r\nsXOBwyUmt1dyCnL2rKyNKHoPrB/Kq+k16nyl5cHKf1J+VkXYu6PHrBjJpzp5\r\n/P/HtBAeSJQ50TnJDMGZBGjfZX6yCPlaKSyjoMGvcZTwlyDXviQZeuat0iXV\r\nAcVhEjoofiFevvZhtPVS0XY1Gbvk27AhVvPAjbDMmbkZ9y697g6lI3qU/0LH\r\njDtTKl3WvjVrVHDtHrQFKQpoEC9hL4JwcTS4pL3aGE2ghStAUAsn/gtC/vM7\r\niLFseo76BnEFXY1tOaQTYjXH+avLu0uEs1BonaphhgZG/EGT2ZLAEdh465xA\r\n/rihx//ugLXh+SG1PoUaEe/tviIufnU/iZ9VhfF4AjfPByMJP90yAj7183j5\r\ncaIceflfpuSBZ+xq+C0qagKAEVWUCbd/kyQz1iGs0PPv1galX8RQsjV7cXku\r\neFgXfkCeF5Mn6ox8VYBHKsWMe60uX0VUJd1bv9q9NydCAS0ATduxxNaajoVd\r\nNyDcqLlNq7hXIYPJozhTbV7bp3VbD1WXsJJulRZ7jMz110mbl0A/Tvy7St/S\r\noUdcHZ59s/m89R0k2MTUdE9sxqQXVByiDYc=\r\n=G0EU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.41": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "57c9f2e3dee54e1105ab44c1af73b582314b6509", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.41.tgz", "fileCount": 8, "integrity": "sha512-AoWzTORAJdBvkAwj4SmduGJGlW0VxNQ//g8xZGHSn5h4vKLpgnIDI1GJ2/9hq7J60J/WKUcrbMUtnwpKOIY9PQ==", "signatures": [{"sig": "MEUCIQDhdrU1nnZkbxnXIk4KBegMaNiqXEHgcgqHcHixMPim+wIgfDXxEVzCfrY3UhTPNkH54Pj/5GDLXfehmeqD+mRpC7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcNQ//ady6t8aOAAx15ZvmC7krAuFYejA67AXZie1/hcQH7D+IQU6v\r\nPBG9a4MtLF3jdObx+vT7/qYs5jnVjbfrgUW3Iq8LJIZW4Q6SMaJfOeBgg5/8\r\nB0wlsncV02svJsLaDlT57Zhlq8I/dlyhJ5ddp6XkHBvmIfybA9/EbeGxLlnF\r\nDFwctayBo+/8ET1Sznk0h3s6yiaA3UogjdiqNM/h2Mn1mo1Otp5QMMxewUaF\r\npydHp+phh4zTFVol8QM0HHTZ1n/OaIGlGdzHsR1wg4+kFRsYXLnDtL8BNLAJ\r\nbVL2Qu069NnvAEEwMdcZvJ3MhstP9w5Q1nW/fzq2il0XzlqRdyoQ96N0wpTS\r\n9+F31nyyBGxeHDZuWYEZJAaAzyAunaA8KF/ZnaRsCC2DtLWJI0G/YkbZ7K89\r\nMkrEQ88UjHLOn62ttKOpjZWCJiQ2MoB0O+Az2Wsw+eNRR7Uo3zsDO9FUt7iA\r\nQ5hFri+fW/PTY4ngk5idXjEVJlg7KvAVyVAvlO1n9DcPRgzhwe1u5D2WW7UN\r\n84lRgtfs4P4U1wyoyaHRQ8EIbXqwlyGxL3LeKGezqM1dwbpllUsgDRDyDqIb\r\naVn0zK8QRup1vJLqtwBiOddY3G3rDjiTbSvIyqEvvCIISBBu0it2oQ2Nmtam\r\n7S5Qt8CszTA3NL1zg98umpgy81cpnIxrTTo=\r\n=X7ha\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.42": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5c6876522111370b61a7b1cd6dea799e96be32e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.42.tgz", "fileCount": 8, "integrity": "sha512-mkQeSnmcY4ffXhbf9Q+GCBquHzWshJlefi3MXR3JXNFneWJaxtB4s9mKkt6naHnPvV0J7wDvWmgFKAjEnBxgUw==", "signatures": [{"sig": "MEQCIHAq2Tp7WpMnvRiyPpfaQvHu73REDUYQnCdRa/nZtBUtAiB7hQQg2OuH2srp7XPAnL+0/TUryHuVAN/PdJPhbCrEyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtZhAAntqBupdAl3LKWQUlpawWiCQNIfTTTffM4IXPo4atbxQZ624M\r\nWuMCff2x3YO5MR0ILGiGKsb1cWAV7FZFIOVNfesPybhT3lF+gnsOK5ZMo3UQ\r\ndoE953R4IjyzAqV5h8Y3vNh+qiz7JAson3BnGayLsogNvIznx0qaqwvk8CpQ\r\nHPKzlDULAUP7cW34cmjPiqpA5htJEo9DIU6c20PKzG4LBQ0HUVv6FlfGNzos\r\nnpdAuW+DBNIy1rQldtQZ7agxPYT/gyrSl8ngaT4xC7BLyHDcCN26/DWz5Vbz\r\nhDkQz4joXVFByKAH6RP5Mgsfu1EJ81Sl7UO7qEcuEai8mxPhzJul6mkzgG+Y\r\nJUM4b20zqkEaPno1wOaSOAMUNUr8X2E+nTiLRZ8ji7juwGkAmHQDi7P7YI3v\r\ndLCry8Qud5VS0tD75Pq/erXEEfK/pH+ZXZeko4OTQ5isQMKkVDReQSnPJhLv\r\njgZg5nc46vajzftVXyJFw9dmyMdKuS2qBxVasweaCLcVBe/Le8eLZaWJZoKK\r\nMpoUQ+so59CTzDmfPlJ7R6u2R1C4S5kRmhHyKQgOEblewN7g5YmrZ3eSJjNs\r\nA476ovxJPWkpr+lkBi7iXgIv8tM26+dx5+ASk/CiI8r8AcO8qMQ/oeGZ/OkY\r\nOvO9mfX2+S6vXTsuFr+E1W7lk+tr1adUtsU=\r\n=V7WQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.43": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80ee221dbd79108e89750a7552747e815490cb80", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.43.tgz", "fileCount": 8, "integrity": "sha512-hcfNA5/LOqy3vbKmf0CY/+XynnP+eh7BWStfJBsveFPv9yVxL6mPGC5MzOPusHxWj3ctn6oKsAWCFKjRc9NzUA==", "signatures": [{"sig": "MEQCIHBhI6/SMWLms58HvNrz9hEWOCkdi89ipN/5QXLGwHhhAiBLNtpqgK5vSuujJYEVD+cGvZLGTtGGxKV58fGXwqnq0A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjAg//RP6zw2cWxBixwVZ86mfujaYOSOseDlQ4pqB4dixc/P7LUqMM\r\nIIpm7SffaadtUGHQcprgj6iUEBQJiSZo4FlghiOen91HtyRGGJ4dz6F0uO21\r\nvAXj9BL7uycwxx0Q0ljHfypH5Txd6KMO55lf3osizRDiWuZklaZTsQ31W3FS\r\npAbMcVTEpZs05373n/3O+D7cvciY85H9vJ53nkWyyXpFw1+sTaWrF/guwZLG\r\nMilblxmJAOauDPfe2ppjCmZROhnmxZl+LU/YTp0dAnDo16KKCYmZwHmhjWKc\r\nR34f2PpsW3GgbzdF/1q23lfBLpHbZf5facQta89ysrK4ZpR6EPU5h2vD7MiI\r\nfJresoB71IjevMl3bRnlsECnOc1wbqEwy2qG6R/A5bpQozrXRgo1F+FHuDOT\r\nTMkXoA2t7gIIGVhcn0zlY9brkLN23KHqU7LgDdoJM32oseX5HwZsHF5QSZ6E\r\nmdlYkyPFsUMVTeoY4pJjGczrSCzxDqeVVrpYbGzoLCpH68ZWiiEQXzv91xGC\r\nPtMJkPHsRnLkxzJ4s/53wF/+oiKOw2MpSAWFC3jgklukQlWJzbW3hokGgaJk\r\n7w9INxJfA+l5yeZVPMze1XuVkc1uKo2qtaEopmpWXgePWpeUfc0wEjMUXEL7\r\nyJyXpZbJSIrfS1LBibXIZE864rM4LQbo/hw=\r\n=b6uy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.44": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b25d399fe283c26b8e1fa167024d4a2b021b8b54", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.44.tgz", "fileCount": 8, "integrity": "sha512-zloaWOTw9eMGUdaDtQTb61aqlBvqIlY9eHqdJya+jvvRr10JIdDYZYKSfg+i9UWH5QwPw0XuH6cU4hJv4WkREQ==", "signatures": [{"sig": "MEUCIQCvduBEhtqN3a4MPLifRTNjWIaqiGLTPCenlZprMHo3zwIgJRF0zHaUIWqow6gPSmuZ0iWkK9spoh2iGp9zbP9R0+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoixA//Y9cRmG+CXz1vphkJc8+hv/YxGV22v4MwsjVjRKDJvBqgKki7\r\nXEof9reHGbexcTOPSp5bMDpAPLjsmjTKh7hsEGBdwJNgoM46nueKtf5/nK7X\r\naazLVLlM3bLsMLyVblLHVX3wK+JiFvqSBdniSUDTtgPxSt8pBZVZz61dfVbg\r\nC0PuHoAdEAho/PDEAzjIEF9Np1vUDcMn368/HE2gjapsAUvMRz/nwENTk+2z\r\n7GUMuaWe1+a8LfcPl4NsxsQjSYled7Va32MVCeUJ3lk3SOg8eC0gAw58gxOd\r\nX27VNOBfhQXzuQXgPOwDhj2JTjSvg3V3iWMFFZJS9UbJUeuo0g+sBcU0Zs8j\r\nmje3pnwu+5Fsv994u3mK69Dv4BnzF1U5eQ2ijC8IPhRuP16wSi77FFoIIePK\r\n5YgC1bzx+yQ5xDUnKGC3oaP93vHH6HKijXzUIWhO3rz+bIrIq2bnZ1fzHxOP\r\nVrkM/mqygmUu80oDhuBza4YkpJSN4/yHJeeks5iJd2JaY2ckjvYOrsMCI3Zd\r\n+Jgj8IV4VO23bbG+JKEhe4bFfGXV4fT0f5zrTDzfCWhAUxYaW3vX4Nvi0+eU\r\nb3gSOX5wGTB7xLDlBUfSVDar9xrhXvQ0Snc+e7I+H92uKcvBLOXjArbQS4as\r\nuSixL/tbGKg6v7tUJedZ/rRtsFAbJCzhfmk=\r\n=y4j8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.45": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.45", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "856a4471464973b7b36407ea40b4dc84116728ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.45.tgz", "fileCount": 8, "integrity": "sha512-251Hm4hLX2Xtt10qM+NugBN3ZUtK3PvS6XDsgBLuMnIOo+YbvvG21F6y2DloOCvfHMu0TnBy25KE5rfv0aWumw==", "signatures": [{"sig": "MEYCIQC08V8rLqlF/iQzxAzltRxYvQ/Jx2VnbGvsOOuOL2DPwAIhAPoCri/lsog9urdBxFwyHIxWWjzfJajTlAE+rWtb9wN2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wV/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUPBAAipiz8CCMbzlp0HMaOROCF/rrt4xchmGVgU0ENoq7M5oGuAsZ\r\nL1Kdjn+ZT7hq4Y0dLrlFiaDj9FC7yD+cmf7dyWq00UbXyfDsgEcCBa4RSNak\r\nd4vAu3gkvKRmedd/QJNSU7scfvFqi/OsMhrUiIs27rcbVOaWutImVZ1453l+\r\nn1ONDY6od0WWqrqLRmqKSIerryHP6x6+s4YgEACU+1Khe/+Q9GjIjYA2lEd+\r\nLXya2kQO5g4eyoWmQMKsVY/xZ3vDLRNkm+2p/zHMpAF7ykpR1/dcThxUBrFu\r\n8ky2O82l0otalQfjK/mOKxQ3F+jQf1DuuuR098Qc+7cLIfYR9x6zI3fjD6Qu\r\nDMdrPqxPcGv5cyPpZ3PCZK8ieobiB/WDbJY0s6NPyB0VYM22BsfcvQtinKFN\r\nT3B7S3bxglUI+Bvresa9I51ZzccqMoAaP+fxGpOa8chHKg0hJtjddCDJiKXi\r\nHsOtLBfrd+VIhtRvxFRkLv4GDLPbux7EiqX0ZoiaakE9K/1xfwnAq2EzZSG9\r\n+SLGlT4nuhmS3O8JgW6i6+yxZF33cB4EFBinQ0tvvbcPClNPnrD2WB0C3a0f\r\nPKPweN3CJUxXhrmADjkLWy/l3ukpS6xngShGylZ9ZHe2MgaDNtx/7T5ITEew\r\nJsJCPSVN8XQzCyhMJjss15SWrZyTeBBJ31k=\r\n=Qe8M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.46": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.46", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd820280c0bec761c362c6a538a37625ebbc09c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.46.tgz", "fileCount": 8, "integrity": "sha512-WVhKpdpFJHkGpXb8vu8PRFiazGn3+CcsdyVAQAGKapskKfo8Lg6apIrrtAeHEMUPQ0CHJLprhj+FnvuZMxqKrQ==", "signatures": [{"sig": "MEUCIERgxD5EiszH76Or/Kc1ScpIEpCu/a9dyxIKu26JwByYAiEAtaYTh5XUXJH/6RZ/CKDAvlsUdt/vz51D95arNnTGwC4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqH3hAAjg+WP04W6T5XxFoP7GuM1G1wiFWSwLSVToAZJduvd5p/ZUxn\r\nWdtUCEWpMzl1pgmYVBC64SxKp43+hOUV/F8KQef3iRLsDln7Tu8ZVYNEN82B\r\nCUpDBuUNXsP3gV/weBsagAPItgpTb0ArbmSlBjRBq2Vx8dqx5jb3vRSwyBWE\r\nBIRW3O8TD50mQPydIAD1nVWbKkwc/MszhWpwY0ruexgKuNcjHAlEIyX8Vzw+\r\nUBDR0mndBRnDt/8J+7CBx3vvwYfWGQt7QRy2ns59pydJkfUWbfchexb/zSWj\r\nggNrtEIzn95wOSfPnU9uxeUEBIKUDYIfK9u36Ah2PK3qBj06T/7hHWIIStVc\r\nJTITbKRokgjoZA5TiZYcjX/bFJjcazmTRoBP41ap5eG53QXeUIxEgO0aZnd5\r\n2qt70dl2pMQremlAffwkkMRWg3b8TozYd41gcHkcNv8gqY6LlqU4KKqDsV6U\r\nwD7U9pWRn2GJrl0K9rZyFdYrFAafpMMnn8GtuHELJJa2BeF1E+qK9AAJHYx1\r\nbpGJo2d3ETdPjld6/xyqw26ovUQq0qrZnbEvHwxqGxuIf5Zty3pDXCjIyvEx\r\n+0Ir2P4myCn0BEaTKEkJFpo0dgBazpJn52gdPWb7XIeLWdOdLWoETJRxWoQa\r\ndMzemPDe8BiwZEzKPHl209bSFI7+Hxi16Xo=\r\n=OpvD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.47": {"name": "@radix-ui/react-label", "version": "0.1.6-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.47", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "78538da29c52fec7784a92cfb84f33672505d751", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.1.6-rc.47.tgz", "fileCount": 8, "integrity": "sha512-WHZvzwJljYSGsA/WltMsTmVy2geUaQGZPSMZzQeySbtDQqav2I4+/1k6C8stDecU7P+NkvZnI286u6RQ/T7UMg==", "signatures": [{"sig": "MEYCIQCWdLWcuU3+UUI8tHYawqjIXdUX9rLzeK9Uxlnldx7gLwIhAMmQEQbQtNF2d/cHNayac8/eZD2Xt5CjrvCF+h35H0nm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCBA//cezrNKYK6f7yiJdVmhhQqsBRwv5rIGLp9yAP1EHCHYz1Asro\r\n/elyQgKe2+tIOX0jmdKxB/tD+mT33u4YCY7GPS3UNfhfyTWKoRkSxpJEHbWa\r\nPaYdBn6ahGrCdpvfhGL9VYq5kaQZ+S3WVnvh8ULHzgO3TBJhfEhElMhYsk1n\r\nmekNNtozNauuhB3s67i27EXyY7MuAnf8RXAYvdB7qIZJ5GD38Mxa+NRBUyzb\r\nT7/7+booi/DF6PyxGQUjqIyiQ42XLYemfWq2zuoAaxMZp8dqupoNzvhfqHLh\r\nkovbl+jnF7sKHCKj5dRHwmt4Tkr1MNp0Un5g0qeFQF4r1TIDNcGpyCVNUZb+\r\nQuLkR37K9ViP/FzIH+NBufZyK2s9JcHSYic69kOMsTDttuOwkFDtYAV/1aUg\r\nCyv6puDPPw/2LwpPe4g2tsMyiKtIOWJCUI9ihdUDcrMLxRWqu3xreX1Yd044\r\nhnUFyPQMnL+BvpsAXd6u5MTSczbOapkQaaKqIsPJrTfAwksHGb1Kxr2I7H5A\r\n77CPK0/Cd2VzeU1MP5ctKS+cmvWFdO60a2ox1FTL1ULmAOasDmZ/TD0ul5uF\r\nmqISwPrRaU2WzhObdJ4PH6me/sGW4MU0jOqz9AbdFiR6ZeWMBdjqJ7vHXPY/\r\nPcKF+EjA00O1ap0qKAKIYQz9Fb5gsfrBg8o=\r\n=52RW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-label", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "711d719860ad199d76b11a144652b2a5c4cb9424", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nCwWdi58/ChWHDhnSFJu+c8UbGiGyrosrDTRuqCHjt7Nwjj70Atqr/xgKaUl2SB7Mumpss0FDKBOIRlebmpKJw==", "signatures": [{"sig": "MEQCICQWozMbaGAsJDdkbVfmFJr6tlutWvb6OEeJxE0Ip2WoAiBqoWKeDZAscdIa4bw2csK66AVHTDvIXoA2YsQwn/NQDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+gg//d13QgAptjsZNc73XkkNeK4wTOkSGbxx8FDUMAbYK0i/pp0xa\r\n+Y1Bd0QfnwOF70yKrqIIo4oCgzTp0hE+PONWu1VzF5xeflU5PDRDOdFSAOLi\r\nmZs7mo5MqoLfV6Qk0UKJDa+kOUlP7G4rdYYJJg1YgZDr+1XZO9QtSZQSLhKi\r\n6zhTwZTTY3MkGu4JwhnJcHIbP1q+y7FM3GnauQLhf+beqaZCbLBRypO8xzPn\r\nvHmr6FsSkPeuO8oECbtH/KvXbXON+7bzQ+FdJ+klZq1306I36zxWw/TQ464G\r\nKaMX3SJit8a+jSnI9ZAEOBEe2zwzylsceix8rcr85xAjFi70ACoUHeD7Y0QX\r\nqJe61lUDwF6SmHRxuoLH5Cv4+P6h2wa6hdQ4X6/AZbhqBjje5pFGDoise04v\r\n3LJ6RbYDEjoq2V/CDY88hmQ4EKhIcuBTGD+f3UHzKjIcQn6AnKFGzWmHt/EY\r\npuvZ9m6d7N+7rn9ooTbAufkqohFJ8Oveo8yFLi02amK2IaBOE1j6faq5s7OU\r\nkmthU4OGcvkyRNXKgWy4vApQ5gsxLr6bP6HkYlA6MVc3YgYuUVQ6xaww0SZY\r\nhOdeCzH3zvdtIYiGOVRkFgbshBDCl8Ti4lg6W4R8v1QR/k0FCaZWZOjWKlKh\r\nw7OMQufzM9RuyDSFJLIRbC1JjeeZOYtVff0=\r\n=g9al\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-label", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c6691b0bec18958512a952c18732279285b61fef", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-k+EbxeRaVbSJ4oaR9eUYuC0cDIGRB4TAPhilbFCIMpP9pXFNcyQPQUvRaVOQBrviuArYM80xh0BQR/0y3kjUdQ==", "signatures": [{"sig": "MEUCIBxD7POogA5GBvpSaFLi6sJr2Uf+4Z3/lbjdbq050hVAAiEAriw/eydwO7XqpI7JbR47TfS/GWwqdrB0JSykdA8D65Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmps0g//VG436lavPS3TFamuK1VmsNwMmjL+cGPLkpiwRP9DW6Mx2waK\r\n7wM4LkTwAX/SqW2HGAIT6HCYD3JLZzgT/173532ShtVm63+W2blAvvy6/82/\r\nttquE48I4XQJ0w83svnWlMPDqFIVwNJrDxFwiHEve/V4AHXGzh5lCtJt5kCp\r\ncvGUknuHQMOFkPYIrLFFDaqvpjiDgUnJaGYGhHUjY3VRcPY3uJA8STAWpr+m\r\nxWh7syuqkMSvKtg4FuiJyyzuEN3zovbAImfMCcJKWTDi7BIS2NfYumaijvjr\r\nCdXv2RCHzXSqgZFhbs6pnTQrVPFdu41eMp9z5+M9KLZcZFbZlkufsqJGh5fd\r\nm1o/NcawalvKN5NxiUIBVXcjlirEqwid3B3jmpyjiP8AeJEjmOxjDE11+E2N\r\nTcVsDjAXbgNTTG0CRiFq+As22bol/t3PbQ7a5fVRVxlDkc/qcwH1cDyC4aL/\r\nOEFUpE2H3sPdaYLXWOo8UzyU97tqhOO6xod8/JqGhaSKSbhKIPYlLU/AXBR7\r\nGx8ckmtTARBLl0t40JqoSheVf93e7FgdxAEGWM8chdDpAuhxm9nEicHvFhF7\r\nDto0rkJAF/8CuOPgd6iHmDQJ2MuxQ722Obi1K3/JfMBJE+PTDpNxQ/euDBdF\r\ns2613kO1Y8AQ9GyxwS+EPgj3buTUUDXP0us=\r\n=bcji\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.1": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "67dcc6a223e6ec095b64aa7ac87efbaafc9283db", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-R0z6b4Gz2VfePP6Fd9LxAaNGCm9SirKbdxADZBZ6r5PpKaMEhxI/XpW8mogBKLELGTyZ2oXPqskEdiiURo+S1Q==", "signatures": [{"sig": "MEUCIQCBvoIL1HwVfgk8JA94kFFBwYNIUC8c9R3C4XVM0WhBygIgbRbOWhRDn70QDMr/CtHY71oaM9L2J6Xs9PpsTx8fUOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbs+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIPQ/8CxiIqSPGiqKf0HQ85IOvQGE6uvfbHDDCxGXPorYQLWS8SYrA\r\npSGj/RhKIYYwlHp2ujgffCchKYEPka6AqkPf/NzMc2PAacLxS8z3AwRVri5s\r\n4tgi+HQkVQgJvzo7Hdi9KnnImvdIeTWRQh7ZcaqWSSlNX3IO/OZoi9aBl8xK\r\ngqXZi2V/WHgQcYkpdEg6jqBS+d1L50eLhteX0oO1+tIZ8WQH6GD89gQcBhVu\r\nhg2RRnIk+UPJmgTQt9hrzfrxztDUB1gectx/b7cPQS9qtEATu0OeXVq0s0TR\r\n2i5Wl70wcTfh9QSS7WyyaBShUVOSOx2vMOOsqVzB9HGmwFSF9Y738za/nOML\r\nSMb/TXNnMBzqRC1CAafKXzF5iKuLzVHuzfFDOJIgvwt5KCV947goDYMCFbO5\r\nb9KzlmkayE4AN/meTQRLgVDFNBCD1yVhRhIEhW+aIu+5wX6fUGtJZ3r3fP8D\r\n29HpqOsEkNSB8DHszSupiM59guQWGiv62ER4S2yVKulcwKnaLsD8cASx2Z6O\r\nWkgt9dldAb8Nx/8iJDri5yDPLda29oxZZvuQI9hYbQIyOO1tzUVSwj8gkyNz\r\nB+tA80JDKwMAYjnhHND+NuWT/u06oclV45om1VwqXyNAyn8y48vIilodL3Rb\r\nFnVdustBgIX0UPSLI4+qctJ3EDDuPON/37w=\r\n=Myni\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.2": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4f7a539f7a51dff47cb49329fec3eeefdf3f2b52", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-+cfFVYZ3U5MTLgxS3wCLQniq2UP2CIPk8ovsh3ENCkZVRPOENHTTfI2cVTTE+QEZCCVHFnOn5WQT8pyY0D1l8g==", "signatures": [{"sig": "MEQCIHUT6ELn2aqneTVJdhWPXrUE4FUSHkbiUSpeBRWzCLkVAiBf/oEeUxgG+eGfgXON8TwnxAbTGH+RwTtUwtejanjpMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEmA/9EN+flgMiyXDMHH1x99It1UA7nGBWWI+mePbu+wfbaUvins9t\r\nzyb5gNNrK6OPpkTF7h97+AReZXZni6L5tE8nHAWLvyNEznb60aEgOSAQX2Af\r\nGWmWPaKKiZ0rWybqvGgIot0X+lTzgYO+TgXe8WopAg3mKLd1eff3z/8QhyIc\r\nQRTFJ4kCVU2I0p2eMUJRu/kooBoc6WSlfNjvnKqJmIqQ3R5d5Mvw59K//Xcw\r\np8OKmoIgwB5Gg9NTkmmBEgHgirLdIIWStYcUIc+Vo7vCe3ZxCiobA9cUn+1M\r\nOI0BWdxFnAdfMPGAErYU8+0c670FYTEa9EMAn29UR9Nu+z0308w1xFx32Qlk\r\nrS6n6yo8mlWFnKstWBawK5dj5dwui9Vewlp6JiOJPbX7Sze2M6esawG1f9Cs\r\nNpIx1kJyeUkObUmdyoJLt0iITul3IDlkvU1DhcTxrbkzJTB50rh3fCdZI7Dw\r\nreFuJF98jJZBh9Pdn7IT29MeGpMpY9xsVaMp9ZTA+hLXTGdEeBoVH/F9sNJv\r\nGCO+dxuUTfTe9h3il9aI86TDpBg3UgfyhTkco1+pAwl1Yxz6D06ckDn7sGsN\r\nG4x/Kl+O7JeedbB4DfXfpsBoAXIS3/J6/Qv7T/ZkNeSqGNyGNGHTFgQNI8/s\r\n26mHq4h9ngvdDDK/d/txFJI20cfcp495FXE=\r\n=/5RW\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.3": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c3cf4648c6adcb3a842c7168e7fee18d345c9701", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-1mEU4xRdleRZIxKWaWjEmUs0NhwNux2oLIOGspjehk4uBO0hwdYswmlsxJ8+O0+1JeiFLDxyW4WPRp4f1j0wlw==", "signatures": [{"sig": "MEYCIQDNgbZHtzTAQ93mPBNyt3sBnDPiW1CMfD/vz+x/QR35yAIhAKtJXW1Owhlj4Ew8SBj4lN+0t1IJv4V54Kk8GtUYczTK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ8RAAiWA/eAAWernWcQYkJbRDOnJOjsgE47KKIu0dw2UR8ibGp8io\r\n+ZLr5kivePspWRsLwW+7QdC2DPNfG7qikCdMJEc+U/E/EY+yC3qUOVGFFLme\r\nIMOjYrfvR9M1n7LngCKj0Mbo2DpTcmUoQpZ41EgzMJNcSS1e1UY2UC/V7B3e\r\nkY2ZiClUYuX5mXYFfbxCPOQWE01sxp+LdUcpZs/3gHZ1i7H7wZM7AGKolpZc\r\nDN4BeUziR0zbuosgUXV+MSw+KuAqIiULLSXN0oO9TEGYNW2WtKMKvRe9Io7l\r\n6AVQXKWjtgTHHz8bzsJDz+3q/Mp6Ac8DYFzctuH8hgsnvES0WSYHKlPWVpoy\r\n9uYwa2Fwpq/GQUyNXslTxBTJo2Lh52BqSv2hpHgLhlk/SY3tjfkvjh/r/I6K\r\nY+IH2jCR7G1Oing51m+bBKDjhI55lfX5rB7OzQDiODxYfMzoa7gZvRR1EkIr\r\nmEUbvibg4fCjfX7ktH6XAYzqGZ8PhPlLnCyPopQ4HdyyIED+ZioGv96tG6mB\r\nMFTJ/7olawsEGvGNihoPB0ZV0mAEnSFZkb54dUK3KY+lkWRPZxvSel4bsW7r\r\nN/RlgavYofL4XX7MsuEXzkM1uWR238+7W7fm4FVJ3oAWHrns9zxkAS4CusDY\r\nSOd9UMmGjH1tMOeAUvSOxhnDVQ3imhRQRck=\r\n=s721\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.4": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c3dd07ffc596876ffc875c619d2818f80cddf2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-19v3mzE7yK8fgMVbrXYybgy9pG/Xwt6Ccmay/N4gOMEs+LIh15B8R7oDAcic6NE8KJpnjO1qec/yaqDIa/yizw==", "signatures": [{"sig": "MEYCIQCP38XJWFV/n/MlxyFRVWKxQc2ACZ2D/Ck1eTzhgo9YJAIhALpmDffBFsqXF/cH+1wMcqW2dqIG+KnQBzJOn/DAFef7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXlg/8DVzvAlCIeg8Y8nvU9Giq1+g2LYOk7kbCMoy0rQ1P//Vc/ssm\r\n2RnFRdiPxI9uwhGVYOQGCZuxSC9TX4WxmP9GI20Jf1dbtA6ZuZFi6ddsw18f\r\nEwzN2XbXVaCgOPSTOa+6JPzo5EApXjdXqQXkI4+eqLMbmjEUIX7g3W4HLc/m\r\nxx7qOUONEREpJjM4COth2YCEfKDSD3CxXgVYeZGyno2o9F7IcM/TiaLej3MO\r\nWGN1nIoYKriBS4jL9XR0Xlp/VbBAa7qQINYe2J4MIoT6+Rk9SEBBVIJofTQM\r\nF1w2/PcqGjKZdf/hwKrIvPIm8yMtccHFxxFQHVQr/0hGo++Wedf69r2fIIwd\r\nMV2dMhsRj5ax2S6cVTdUustB5kFljR2QMektK/2kjHRdINTla8aE6NcGZoMy\r\nPtTOifNwsEid3ZXZiu2yC5eJYS7sscjH1lU9zyD5NlKtlg7XTeC0LjFlK/TM\r\ni5qhL3nugn244nGgyzQs1pRPnP6bbaJ/aZfSSiY16uIRNieXyfMmDr4xz4uG\r\nougzIANAlDrPxxeQW6w+Cov3f81XyZyVUIfowrlDr81dRWx/vQuyxiA7aSCR\r\nvoUgIo06zr4wzUlSgj8mR0exWMRnLVo1r05QGiTf2KQVRO9Svh41AAWX+NnW\r\nqYtW1gtKbwVJukk+ZQU1z2HzcRLFRdctxsM=\r\n=t+2u\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.5": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "54d3663e462a48d32d77601b92a701c472794828", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-5uHJNVkPtFRhoBCxxAuT6R7EFrORkWucb7gTgVjle9w7926UKcCaQVhfTYcOCP0LgHF12buB3qexIotyB91yIA==", "signatures": [{"sig": "MEUCIGqWYJX+J4AwX+LQ6E5bID8/fce2OWl4gNSYUWJ8bdptAiEAkoOTMdvWDKGhpq6gxKIya29KyPhZLQaeSKdD0fpnBDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKhQ//VQ6VJeQwgsqv6XcS1eLeRnW7upf4qA2CQvfF0x4kS85tpHTZ\r\n8tVdWDAdFZ8XFFW7NXwW2ciY/AWMB+094zNbj0ofZR0sQpwMuDDMJEF3Lh61\r\n5F02xrg7L6WjQbIGnSB4g5uq2gO32mJVQ+avwXg17Rz/mM6EnU1n0SXIOg7b\r\npf6ir5XLdxU3XV1fxvFPZkVyWVOw0gUKSKQeN3vdde6pAxO7moU93wKf31M7\r\nMqU7/aiKmu24ajbtQdQar8J0mSjGomwA+tYgF3c4+bvbPnTpPNejJyiO8jXc\r\nizlHqCWttAbJTWTMwQR2vivcI3/sp2y0Yrws8VzDk1hYnE88DmMRe4pKyrqp\r\n6294bu6qIL5brdCS9/hTs/DUXu/VqygVGmLntZIO5etAqfnU2u2BsSGU6G6C\r\nuhH0xTbPwU9N/jKLlGuzoIItBc9VIitYRjNWIosxgGEgNSLNAcw0VpJu/3J1\r\nZDCc6Qzdyk1v/o/n8caSQ+zbzVH9LM942gtD8T9ICkoYQvsfhWvsF753JolI\r\nPBlF1EkKt3VOI4+9TZ5UtjQTJJ6oVTg2LlymZr3P+6C0ZCdW2iZZVg9xNdYm\r\nJAKNxaSbumGzl4TH10grTtya828GXIU2S4yUkOxhpOR+++miF8Dv4noQMfe4\r\ngLTpMYA71DF56JO6VhqBRprv6PjHV2D3DC8=\r\n=CiVr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.6": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "87d190869d047a4931c6e96e991d42dbaf44f640", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-u12ASjK+RLE73QF35Y8ofR1WuanLzYFPFq+mfO55MM72qBcwblaG/+Kg/wd4LgC6xtZy5Nc459WquqBiOc66tQ==", "signatures": [{"sig": "MEQCIA1k/ZpbyKZg42nnSGklnP3yKHuzOinzQjiDkh1HDllfAiAc+wlMHgKNLSnxyyzV675KG1p+95jlquHgtgOnXLvqlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWkg//TEICFuhdfyXL0VaggZD5LQoimQL8UHD3YtDLZ58vBe4Y0T5g\r\nMbPD7t2XonUxT/ywCqfu/141quyHlN1YV5YMvkr5YxHZzKKhKtr8N2ORV8J8\r\ng3XSIrXb8OZS/UFxR4ILJcLVfXg63UfEgFvjseykKBN1tWFR+r4PpcZcrFD+\r\nApTmNylb5as1dgerZT0LOsWnZKuGVS9IQ0YoyTk80gan0Jsu3YiY52qqypZd\r\n7VB2YiiF8BcCYMo4mn9TlkBOxG+UoMc0eUgtqo2t3MYJ4/Lp5DmapP5yhs8K\r\nPBMXw0W+P+wKjgDsQncVzkyXE7CeFsG/t35EueAzoBdK+as5AAzUQJiD6/3l\r\n2xejgbTfDpCRUFzwffbuMwp3vX2AbM1xu/L+CyuCq1E4kkCNpxNZ/2LNRK5x\r\nSB8MHvrSgWqDx3aZXizDWw17DYazMakXPccu4yOY2j6OiH5G5L5AhYla+BQT\r\n1KY2Cfp3f3hL5PYLtE5CozwJave/IEETgjWWfdQKQRPY9t+/OGZkthzZMx1d\r\niBx5p9yB8cwlFwr6cqhKANQMk8gQgeVETBt22EajCB5RwA4qKD9Q/Uvx/jCR\r\nFBABP8Cg7zzhXpQDUXe7XHLgYtFn0PpHottnpLR9KL4nlIHht5mSksYdPdwl\r\nZoA3759p1jxgeOxQ6Nvf7xi50TF1nIWFuCM=\r\n=rKw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.7": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3366f97de40f0021f30e985acb0c81f8a247cd55", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-4kO4WreS3+MJQzlFXJkDBi8m++aC2DR4jI5Yj+oasxxr8G5vJYjj3kSuXT1LvYKsu8uv57j2TEbamGBo+un/lw==", "signatures": [{"sig": "MEUCIGZMoDuhRL/VZcu+pRFqjKkp+t0N4H1jbrwr+gOuCZUrAiEAuIBbjIlxMyDmFekLlxc/lSUVvlil0wiLll4zWTOwv/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNww/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoELA//YRzmuvSeTLMh3EGzHTgMVfZ2DcUai+z4zu5y5QQY/ZTbsKHZ\r\nGrYAyuNtDz5A/vBFzL0JZTxSTUSlDzgQomrGrc2zRVbSPonFuK6u9LGtc73L\r\nC95dwWa8SqWdUslvnWyxPwRr/rGpx4T2z6QiHHcqKoxEnq4AEwp56CMrV/Pk\r\nD1iDPjxlUZr2FZyZnzBBnS22EevtMEM50INz8la/YKoIODV1gM2lMcEqqz3W\r\nSck3eJXPViSkutt8bTdmtawPaoj1WrYwQY7bs07wjjbypHgcp2coMrxjyKVV\r\nDOpTwEkrMGsNHaDXSAVGmCR+QOHAWxwWA/0MWY0Zww6cYk02lW3dDRalO+Aa\r\nIlyDC7yM2A9WwIBlgUOoGb8haGT9JC8pbZjRlIuWxkWT93yUCVsqFxyy0duT\r\nRxCBpSj0Jfem67JfG81S8rf1p23S8WvggdiEnD/y0LSDgng6WuyDjRRHFLU4\r\nnF8cKiAIkApOqcdm8VOEv1MWMpurvKrxhhnmx0t/7oH8ym5ujNb+cyTlldgi\r\nVkWqcOaGEEnOt0haZCyc06eQShnz8DwDiNM+Gvx1zWfSsV1E17kZijla1EAw\r\nfYcGb0WCMHv2Z2iFaNJQgydryvSIJRZVRnD2Qtuq/W2gvoMOaH9NTs/jYA0Y\r\nQtBmIJWldEWZxQKf5nOYzr2L3DdPxBHMkGE=\r\n=Vjy+\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.8": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a133950b7669aa6f5584ec909ecc5c7c317fb2d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.8.tgz", "fileCount": 8, "integrity": "sha512-j02B/i24noQrTNHiouMzuVKDuvSUC6HfsbYIqZHyfx3TtNR+VIhjIxsjFzKl1Hi7ZPaHdQNTrFlcXAcXluSucA==", "signatures": [{"sig": "MEUCIQCczEo+5Z4dPqVdzXLNpatOFf9zhei/j721Q3/pOn7xIwIgS/6GPxP4g0KqE2NWx4QV7IQEa7NiWRYEsp9myvlcNU4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+geACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFGg//cyAyAb4otmmP6nOH3QN6jJWMewqyxWdFKFBQBDde8qkpk3uv\r\nJighvd0YQ74iMyhRazn4Xqn3CL5DoZnaLCrKA1pseBHXTToUnjcBIZ41LGyG\r\n+6ksXl7A+RlQpdvp/aFOCWDpVkUwHOVfttdQZvAs76TZ/F8t3umO1qNYxg9d\r\nVolPNXvdlrWvDEBuMimCG89DGnammZPVXewM6paWRahnj60iB9Vnncbfsbpc\r\n4oWKOrBwKSmysrKIOJ2ARbuaTNFZqh68aQ6oUwd59KmIfh8dCxJ5ekh5z+P9\r\nX8MR6EvbQKGphtfpglE917+YkTmVucZdM9xh+myUVf3ILsjb+oA2mFsd5SKA\r\nYgp1ER1zZ2uLWR/IZ9fp/jT2vekzS9/engjemxgB5KxBM8JpRlr10fk5cQRl\r\nf1RjpRhSS0JzfCw4mLWSb60aKu3cUdc0I2zlmFnhA8NMCpHmKYbK8X1bAJGa\r\njCc+yvdtXYLDxUERBNKjxVaF2qvauEGPSCwSTMOopiSklvykkWhUNH6YHK4o\r\n1e23DjWMRxJaxHA1PD6kgOfPx1ybvK35gOGs+rfqg9eSJ4S54ZXOOdvezbDT\r\nFX9y30ckUmOv4YTV9TdKRcK9VpskcvjepyS/OKVYO66S5cKtZSic2Vz1knWq\r\n4hwWYfa/CZVrod5JQYDgzBy081fxnJWnw3A=\r\n=0NdA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.9": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "218538a725b232f52b8527dcd2c3fcc2369a703f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.9.tgz", "fileCount": 8, "integrity": "sha512-0w+fGsVmI5P2e/e0rhKHSumkwZM7VYsW1tzeOw6FmffBilHspWGpw2m2ZZwjam3lnpalVc5iLdn9KeUXkvHt4A==", "signatures": [{"sig": "MEUCIQDaPAVOBcVt7sibDMnTgjeErhdkVzLiMuLOTIWDRnSiZgIgILSUxMLaxcaxZ1C3Cu88AzFZd7G/Yj1TSZBni5MvBd8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Zg/9EYhH4tl8h/5nvikcGi7foi7/mHz9zF2uOWgHDvrJJsIdwaAK\r\nxanbtnlhbiaaLkQXLPwjKXA42c++dp8OTULDhBoY3ITPH5xm/ucC5UBo1o1o\r\n5EvxOKWc8IoMlo/VWlvn3rL8YoKDPMTiQFnR5fd5KhjZwItxFckHqWZBlrRY\r\nF0I/WscYb+a9g0vCdWk57RuIn1/mX4X19i3M61uZRRryqPeB8xL2WM3wTlVu\r\nf74pqULJpp5F00//UIEVviOInCEz6ec+xIUGmFvRdoJHx2OSOPuoI2BpQK15\r\nc4fMVY0sRUcswbAHQtDCfZdA4yoFYrVruM9hp63XmwW/LBA59DuPeUFAw/6G\r\nBpz0nD/AAjFHs/tXzAn23MlS81wCuKmQZndbbcv/maL8FkLclnyhQIuoXiBR\r\nWv8xNOFku9LrCdqhop6AONQ5xgXRuuQTAYyZD8txIub8ZG0cYUV5u3ZyAi0t\r\nCZj0qJxUf6xTB/+38EYpWWYaiwePeDSRRu4o+HzCVeanp6PAy/oqj735wNSH\r\nlzryCGE6vAkmeTG+nNcSPI8jgzfvjTCE5W3toM0sBqMUSDKuPU6gtdRSROLe\r\nSVEYXNcpOUstHG9tRp6Pp0TugH9qoXME6j0406YZCE15sHyDTEf13W7Cs3Y6\r\nVsSGWswr55LAb9X0RGCuHLx9yhZpiNo2akE=\r\n=Yqmq\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.10": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a37bba49045c231f9a6261c705c706bff58d1e1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.10.tgz", "fileCount": 8, "integrity": "sha512-eWj8nxkPNDBsVievvaJZZ1vVv6Jj40jziHN/ftJkRD1dam4ig3E9Ol7uXchGl6OZMr/psMIZoy7pEubjtG269w==", "signatures": [{"sig": "MEUCIG4GpqJpf2wuJpH5cJ7eHvWPRil74YnYFJ4CSL/vSVVcAiEAk8dP5/EMS99XkajfRV3/eEG1CZfT0aea+3TNObnurr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7Lg//aHP+cnD7DnkY+aFPSwQjwmhWZsM9KJBa/tEy2V0jvng6XgZJ\r\ntZxy9ZdrP6NloT1COPVY71UU4qTBLqabcCZd8yNiVA1G/w8x2zC01CvgcD9X\r\n8aSu5vps0RLIU9DN7O8mlZ/nL+mXgJTlQRGLRA4kLqwQTirp9hWxcyYwzuqx\r\nqMIJSRRTtEnM5N6p9yeCJHc6OCRrBRvWUsZlMWXoSu2tbEhTPBLsiybE4TYK\r\nWKXYPDi5dcdcMUM30ttIxK0vGLMN/dOCFi0gp/mHl1PIDloHjcaEgXQ661aC\r\nAccipUWrJgBS8cmUhRZ/YLJkP5+ifZDXuaC/tTD3kXzTgmwrbyy7NqzaMIem\r\np2WgGi5GZNG1qgbKGKofD1Yw0L/28ySuBpZIwKdiwXnuP/JvlepjWAGTUwwE\r\nSQ/mf+jJOYIA5rFwTpQSONbrojuZv13bB8Pn/ms63BaxPRPh8YS1B+d7liXF\r\nDU8LR65WFOYjDCiSXtmbqhTig4xhOam6g3USr9ve30o9GUW0Umkrby5XppHm\r\nU01A42nPY/SF41501TG5iMbd8TTweQQydD0r6zkUk0d0OIuQz78L5kuDYJWo\r\nLOj46sgly2BnCphNqoXG7+GstSpickYEDNLodR1V6jlfD6bzLZ4XZn5C5dUF\r\n2xZieARXdqSTulF4EPQCI4KXrbVFlLFH6ao=\r\n=rJtT\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.11": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1453ec9e3e1f8aa7512846d3c01e4940a51b3196", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.11.tgz", "fileCount": 8, "integrity": "sha512-RFwID0Yf4ryqxo1dSscEij+G2pGH3iZ/cvcyETYYPFtrHwq0mlXHpVAlygLYCRMY7QkTfCK304DqgFihKLS9TQ==", "signatures": [{"sig": "MEYCIQDP72LRjVLRk0hDS6teyWQ2eyb7XVb3QvwmeaEzu/1NSQIhAI6SNW+ZtZl5sLBFuY0Or3p1HuWY0jKWHtsAyXpY06sY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqURA//VS7xK8+fkarlgyoLU15tyQeaSWHS589Ag933EuPfbFt8sxhC\r\nHxjM4XNREcBhfpksHKH86uY//IF+CihFMnU0+su21x2wojy2A7UJbUheUEEK\r\nM8I2EnGgm7MhfvA6hwS/Wu71aqEMJVcUoiOgDweJNsjfGgzG4dj39TpVZoXB\r\nm1gWAmRXeCRac2kaTny5d2KZXhNSFh4Hq70xsayof+5dNqEzPUAgp5VUoNXD\r\nN07igfGL0/vTdZhpb8vmjKjIE6ddKUubTlbkbMPedIeBozg33+BW6t8eFV3t\r\nb/jvZnZOOuvFtAktkjpTI31mt2pyCl+fVGC0I6f7QvrG99rRL8mI3EibKZvV\r\n3hwHWkwalwzgsmFWia2CxzYKfAvQkMgvBtdibQ1pjbbk6OU6KlFv2NTbmaeR\r\nhK85IaZULkE3W+DuecMP8x/tM6kBOT4xBAZAf82fH4f0yy5NtnGtuxRM/WbE\r\nGSfr7isoNN9qfnqCgNiAnFjyBt87oaXfyjZmgyCLYpe7X+GgfwVUjIVmS+W4\r\nBkZNObBuy1XouSRx/QzmhwwRgkIrLow5h2fBPg8dmmBzbUgBoG6ehi9K2Gxo\r\nVKSUfyaCs7cjpcOma+2VELGNtM6eaFurTG/q6k2MowDSVwtyV29KSUUBT4Ga\r\neuRm6aRtN6IvTKI4u1rK6zd16qZ0GIUaVvg=\r\n=MsR3\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.12": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76e4faa8d74bf39cf2dee04e9392e2b643921e09", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.12.tgz", "fileCount": 8, "integrity": "sha512-lNk9NTfQvNfQJu4vAlryXM9OTYFS3TKw2K2qC73a1ng6yx3iqQvXfM9Dg7yO9Cx8ta7K2zxHtdaT1ehJSDtjnA==", "signatures": [{"sig": "MEUCIQCFKFzrrYBo4WH0WmJX/47bQU99Q1GxdU9yWzNhqC32LwIgYcz0Mj9e+7SARIGT+OVjidhhod3HPmWg4mGfFQfyKJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodUA//fWyn2ydqrfe4RG6o0Kbzo5U0hhkYIQk9QVyF0QvrtbjD1cWS\r\nhFFuMMjAeAc4yrgRCO1Q1m7536lqWH3gbf8y4LzrSwo4K3+DW3D3e3qlKxw0\r\n2dnfocusomowfh5oX+vNVqHC9lpjQkBrjrnlKB/nXYE5sKHqWVemSYEZWfok\r\n6YTp9xnEdWTylnKYMXTSM0VcQypNkVt57hMVUwRjudfrUtxrafHQCStNTCzu\r\ncthNhlsskusJPInJr1zUhGwhrtJJoznKiQqtiKnYtZmiLzyCbSEXG5/GyR1n\r\nsI2B86ejXrJXKq92bT3YKuzHrTy7ZJfb1HNz7qnyIzPGGuRewZttTr4N3PYJ\r\n0K/301gr0cOaD3Yy+VzF6/CupMxCkk7C2Erzm1OZu3zSMs3KrQKmsErRZdMv\r\nSjzGoVYHNhybj1MFpjO5DdOl747XKbAor1JEpTQUEasnNcCZsJk91GByv62q\r\nxYBHSH0tyUXSTtWXoj8d70+3moWTFHgB9LdO+Mmmo3pfb6Qype1f5ZCT8bKe\r\nU/NDhdZ00IgYTNFSiu3NdJRVqTxmdNsmjtmNz/LuioYnE+IePymhTPwM9teG\r\nM8vTAt5/c+THEStP2lJuA2C+hoySIMs1TwB3jfGil2Fv4acPcIvXlSIusnPU\r\n5mgVZWiPm9M2vM41A0Hx4QQ5DrX5X86nb3A=\r\n=pqTC\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.13": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad09b61bafe19e101a6dbaec50faa7cd71b7ee9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.13.tgz", "fileCount": 8, "integrity": "sha512-4x8CUgclu/dzaXNnrUNtvvZZQ7ThqcyhBJsUAAQ5+oxs6fxrqWkBeYSFb1GmNcZU7cLbxJJ5tHKO665Pk2sj7A==", "signatures": [{"sig": "MEUCIQCLNIhfiLI4wMaqN2DrV47Rc2X0f42fxqOGLTYOEkNpDgIgQXYjFa6jobOhZ4SnQdZspT33KiopyNLErEkMVEnDA/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA+w/+LRnLvVmAqBGVxRLfB8Yv9kZ4dWKBlpqp7VCyTy4orBSmjz3Y\r\nFjSlkNd2TuD6f1w1lh4Gh4sW8ubJ9Zke2q0Pdnq/ocVhLkakh5WiP4gTMfo+\r\nmVHOyALQ/vaQib6u/PE+/pOPZomdUbv69x9CXYDHBtWMMhqNecLNGE09vH5a\r\nQjF/cgDKIbp0WlrxUsS2eS0XZSVzWlJdq1Gbk9/BD/tATBKkfNSgDBIQIwpv\r\nnSpAK875eSivlJg+6g9alsC17ogAZ6J2l5ktDDRBFoLNq4FUYiRTumt1vYrZ\r\nRmuAR5l6VP+ZxxxRJBBmjz1a+dPFsIcYFntdjMhpJxJENA59EWflS7LbkWOV\r\ngZXP5HQSDrOovx4ymByFWUBhPKN3ATgpAg82556UGgOWs5BPA10kqQsnQUq9\r\niFdsAkefhlBX6WzzZLKhcLW/Dv9dFg3kf3lFD/wan0RhvJYBVs4H/s617wAQ\r\nqY0ImYDHObfo9VBuyx9EwMwPYLz7DpFkC0+/YYRy6nIK4dilB2P+veiYbvwG\r\nXQNuRtUSGxAMB9MgZSH98RX0wLlL4+qf2jbR8Ly1ddEFfVGuWkcjpQymcice\r\nAVbWWTzftNF+OoejBKqk0KgGZaweytla24rOivO/nWLc7K0vNfMiyzB84P8o\r\nfWhDt/D8lI899H/R0toUENRXwwbDEVlzf1M=\r\n=QXKw\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.14": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7cd093ca9e5fb32b715e791f160d69044ebfbba3", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ViDM3WhWNqi3jJ/lEaScalgTlPxu7apchSK4HJiXhl5vq8NGECwyZ0xSHUE2BP43ubrf4S4yfD9V+TrJeXpX2g==", "signatures": [{"sig": "MEUCIFx4/NWZN6cmK6kNnrrGfztH6J84IdtUW6DQ14CMYJHtAiEAvvsvkFWQnGxnL+KsERFZlN7DpXZrxkn1VKi/S9WJL+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqw5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKIg//d09uDprlRHkiHSdgl81OGVHDlHcnh8rROoQAY2dFenhqVDPg\r\n86EwPa2/7LB1ivWKybJ1eLMpv2zeTP8koIVZQz8qp4EewXiXmfORnr88wH5p\r\ny335FD3lwHTyD9UId48170wcSQu5VUgFikA883a00Tnw5froasa8VrS58BeD\r\nFWwZHqLi16N38xE96KXwWWsdcTEFLC72VkOppjODwkX2Hhx5DQTV14jCCrS+\r\nqEhCzs0WKFwk2itQUqh5CEZn8QiR9DR3bFfxu6Hh+NG3v0cAgxQ37AlbvI7+\r\njEcznXwXdSQXYQJ7jCATPsSDt94zZIH7lmzgl8mmMdrfgZBW8Z7SwrXlc5Hy\r\n73N335HCrOlcBDaLJit4PxOk8atm4n9N5z735SDtRWEuo2w31N0bfT2ynT1P\r\nThme5IiiZDg+xd1J9SlUv+dFL5Xg5zCoAfBiSameoI/1op0qtfDhAi8yYfCe\r\nHSQx7PZ6mugyZKS++gECT9CRTKvXn7JjI6+C3kaMv2M0tBS+IokUvVRvWJMB\r\n8vYoATSV31NYFvKO2oHo8Ad8a8fWr+VK4mpVJUtIsBz5YsoTVBxX8prLKhyk\r\nZb85YFNzavPR/W9fzEpPkm+hUsg5Q9pOLyDm3HRKvBDXj3pxQBN5/y8R5caF\r\nD55tJWBtomq0lB/FVZV4x+yXjyS61GIxpMs=\r\n=fwJb\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.15": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "180f686c22f5aabc2aec56c1a449d743c8c14070", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.15.tgz", "fileCount": 8, "integrity": "sha512-uO8RkNFuk5bqC+RZC07L/HQBV+vlAxpiPUegADT0XNe3mi77uUk7zeWdOAeqf5ZAo6iifVD5qqvNiYqr37OkQw==", "signatures": [{"sig": "MEUCIQC8qlO3eR04O8A8XDZKv0/htLykNXg51nuNevcxfI49/wIgQV5Q/2iF37vnBJIc70ksecXuFlJCZbLUzfOXmKuorqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOtA/9FoAjs54ko3/k/eWM5ThBZiZHQbmNIz/NGEzuokw18GmhrRer\r\n8/AKgGY65T8mQWuiMbq8I5WOf2hlOfxgidqcc1y9VER6Kw3CT2sDn9TznmR+\r\n0MEDXMQ5J8jMgLJO/5gz1CGsZFywEjcZpMMa4RkHTcclfooPQjNm7tGyM3q/\r\nezMh6lFqh7xtjYASiBZlwJye/xm6DtRVSKX1HRmjtk9ZloPxaZTbQUy1RsK/\r\nM5FuX0B7ZR57p8hBQum2TCxhq0u1LkdXVdBfwiTN0PT7rDbUVYOUavm72ws8\r\nOAAMCLv0FFe+k9NzjezKf3zK6GEd2NlMq7sPlvU+HNUB00Gj48J67Txqgp8K\r\nIf6s7vYAAIJqVI6hhLx9tfVaqAOJi7C8msOChwYUVD1h9qDIU4ZmNXnr8XVQ\r\npYlNdPJvfyxmW3MMtetPWJdd8fynX7UdYNLUD7bCx+SmAGyonqOVXhcrvZXJ\r\nPMuHmFq6R+fVxo0N8JVQJ2Vz/mZQj9zWIiq51arNVqVD+cL6PVE6t3FZuzkq\r\n/3C/z3mtNo5mBGApPicNcupty+M6V1p1wZWD8JHuHD00d49ij/LVDk7AN58Q\r\ng291mtTDuyI09n+iz9kl0Z6zUMQZkfdQyyMCCumoJixjoTDx0au0TEPdFbda\r\n5ZfuwBTu6MvT3UhsfpnCwp2NP6A2riE1cjM=\r\n=j08Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.16": {"name": "@radix-ui/react-label", "version": "2.0.0-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d35411d930892f98e6c3b04651973589f91c9e88", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0-rc.16.tgz", "fileCount": 8, "integrity": "sha512-BO2CXt6TU6BUTOKBMs0qQaQK3aFfn44bjZtna8nSkEIpI2sQY1rBsL+bUNRGOGjWypVVVu08Zgqria9b8gSvWw==", "signatures": [{"sig": "MEUCIQCN/Kt1qzAthL4HB80LGbfd7DZLNkhP/Mtn4oMKS4V4dQIgcuaHg8sYdcpQFLIXDEQC1zOnVLUqHEUy0m/FofaBOdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRe2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUdA//fbv9koRo1/H4jZvotxdryFD/uzUmhLUIpCM1VsiKLM3/92TF\r\nWNbmBXed1II0kvUdyy0/T/XRvAvLdpD2QZqP42dn/daJrCHBkODsaDB8F/VQ\r\nEoJhsNYOgAJ8gZwPEsXBpcaIQkdSPLOHP1RZZcUdC58EAzj0KGnsigEeiSnB\r\n2mOPF542Y4OKytb6L4dZMbbnuBMhw3agSkiZrCvEh81gqrT6KPULAhPqnUlZ\r\nxV8f/Cl2hih/emiRq0rsq9lKUIoftFLk1qrr+COwvzHnRQRCWxHCwa0kkaG9\r\nIlHDA5zdYWK8SDTKHmyjeekykHiaQBRvHodXt2+Pj2J/CQM3RCDH1vS1v2rM\r\nrE2yPol12XRGpXuOiF7VM/qlzJKQ5zbBhe/oAhZ3l4zWBzo3u0WIkIjVf4mY\r\n2lRNbPb+Q31B/60i1CtydtXl/3TLyQb/l0e4e+sYeEabR/+K3yjxldKw0oUu\r\nQ0gpqvIH6Bo2+q7oqN9YoJlpxc7nI79lUAUN2sI+GazDB4v4gf8xubgodg6J\r\nJVNG9Qmy5sgPHaQxYUzm4aFonGnmT3Q7o83+ICRoeZJWFgiMpheCYVfOrlg6\r\nSzJjD690rke9UYYcDBEueumfnli7Oa0infLzBPlm/yW/Ky2HzSt8G1nDDJor\r\nSWTf5LP7EtkFPmE8dF2EYGJTCRQq3bwbFbA=\r\n=pcRS\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "@radix-ui/react-label", "version": "2.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f94c60459cc91a69fe0ae5bb326bc3f3c091d7e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.0.tgz", "fileCount": 8, "integrity": "sha512-7qCcZ3j2VQspWjy+gKR4W+V/z0XueQjeiZnlPOtsyiP9HaS8bfSU7ECoI3bvvdYntQj7NElW7OAYsYRW4MQvCg==", "signatures": [{"sig": "MEYCIQCPAcLQbKj4SUIWyglz9gYtxIzIYulIdM4gidErqKLOqwIhAJI+WK+E7rL3pXICJDXgYdP+38yRmMxkJb0S/jQuDdBe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpw+A/+MCPeJ8vWc2XpTixKaqmzW5et/AR4fLF7qGLT694mUOEVCV2s\r\nMBeK+Guz/fh1mT7HWxnxWzUN1jBe69Wqs5cxhpxJWnPcLAUZuT+yLUIYpCYr\r\n5zG4jTDEuJiBlDhGt0tuEOip9NwC4yaITIxBDWi0oLzT1x8zs5ipVO6PpwTT\r\nVD9yOVkGUtaKe4vn/Gmdh6gCgTKQ4ndiYGlyO7MZSKEkjgugel/yJ1jqHQfR\r\n5/+CBoUh14XdFctmh++LVeew5ZdMTWfu0YcL7cQdYURPFDRKmaRnyGhQxS5q\r\nyJaWdaAcwxKoho44zG2eRzTaXFcDsU7VhJAQ5K8YmunBxUCy1P5BDDuPUN3w\r\nbOwsjjOW9NtBBpEc4btBtr+xEWvHhsWCbvlfD7yz1jvn/R4nCdHm442AbHHW\r\no8sYsC39gJXe3atK0F3KAUdiLKgZzxhSWZGWJfqcNN1N9InfQsHgY1gJTDam\r\nU7QaIeIo6vaRzrfR+kE8f0SjBygaQzGj9NNeUdGpe27wxSIrZEQhNZuqlN61\r\nIKPCx7xTZcanMMjKo4XeLTU+4AEphyqJMnWwnmXkNjT+FuVgZhM2KPCwfmI/\r\nY1M06G+u9yRzq6U4BPlOVI0jaP5ni3ff8Jzis5uKmIbEuJQ/DG7HjOFMEUE+\r\nykqoumG55JOiD907Xn4E2Zha6VmT7WCF0Hw=\r\n=pTiO\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1-rc.1": {"name": "@radix-ui/react-label", "version": "2.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e16219e1176a9bb878478da62ccb7a34303fa1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-sPr2YslJywQix5F/bGV7lnpeDd2ZhUVteD3b6c/3SvkDeIe3411jCARL0Aauf4p9HkxoXLlD42IZjhiRJvAPXw==", "signatures": [{"sig": "MEYCIQCqI3zqx+c3GlL0fWpIqxpVDI9L1Z3PoQHlA6KUWS0hAQIhAPjvuH0+4TyTflX6Bas2sItAxjZSAkLQxxW3K4lDnVqy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDtQ//Q6unubNAk3lZzjTFwlaS2ynrT4++B4R2JRE2i+3nS5KN6htw\r\nol786LU4qIBdNblUUDgXp6jRaNH02gTEeJWuVla2NUzsYMbnjzhGG1Lc+0DG\r\n40UxwoEOdq9Zhm4Mfk80a3SEFm0X7/J8CCedOfT1NW2+8GV0PtOK69KfP5wb\r\nwkYm1ENonM6RSmx5760jT2bQ0qWqll/BbDpGnx7lu5wa8DtsMdS47gPdCvyQ\r\nk426YmMVXY1xNFWrE/AxkG6krd0djchbdS9kGp7RO3QyRq9k1//a3yv9SQZq\r\n0IWfi7mWJeh8bzTqXD1FZCLbZSId6wNtdkWHjF5veZQrk+guOja5OD/EemT0\r\nKcR5YM+GkmlXPdnZ3dKYidxCn9dvadm8QLEiWR9vpJevjzJHMff/I/KHdQ9x\r\n3cdK5NSmw76LO5iGrPa1OdhjzavNmSHzq9PfQ60dORIa+43WLabT0vSn6Uw+\r\nwYzuFyn55LFY+BeWs4Devqjl1uzvnesykoi/tfFXR1zztJHEltuL3ymdzlTM\r\n8ymlYfPjyj8gpPvecvZ8HAe89O2IzHACPKp1yyF1F+l8KULUlQwKVuRqZMIH\r\n2mXf9uOLML/0G+8bYL5uLwtVaTGcbTqdY0INjCBiaNw1fo484ZV5SZU3Wpwp\r\nvyorOr2MYgpxywi3l37R2bZJBm2rH1HAQjU=\r\n=YMrz\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "@radix-ui/react-label", "version": "2.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c05c41cb20f7877d6e9c3eac731b11584c95b2fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.1.tgz", "fileCount": 8, "integrity": "sha512-qcfbS3B8hTYmEO44RNcXB6pegkxRsJIbdxTMu0PEX0Luv5O2DvTIwwVYxQfUwLpM88EL84QRPLOLgwUSApMsLQ==", "signatures": [{"sig": "MEUCIB0gfB3vzLdE23BEHKbx5nG/T8TtqT1B8atRRM9rwHH/AiEAjZTJGmTm6vnFTjBUB9kucD6FhJUhLDvYm2qUz/97Ocg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10197, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJavACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmob9Q/+PGDfSJ7Nf6Nlsy/kopR88GfOfooNnyDfvpwAyXSmEOuXJQCv\r\nPYaDB1BgA4ma0F5I+h4cgv8L88dWimdrDW6TywE27A+U5sG3V0jzDcfYQRTH\r\nYInfXademK2tjz5VNuQihafmyq+GO1EtdWEUDVfMZcux7rUeAq2RhI39PIz2\r\nlQoJgGjPhh/ihUic6V1/Wm3HP6GhdhxWp3GpFhhX8X3Av5fNoco2r8RlXTYb\r\nHTr2gxyrW7yYPoB9lrpsZBRL0PQgVypokAPgZh8ACuN3mXzTQMk6wSRwms7C\r\nc9gyidVYLJTX6KgpSEsqFfYpeu+4H9lCvYwARvxuRfEdZcgiBStF6WRTIDOT\r\nGFtK3nTYlaqcwOA85VTzA5koLx2YDexycZ3yVHgmqmEcE+4AWOz+PS5onqZG\r\nB5gXRtJHlRRttByxuXUuk8AHj3/ckT7V2YeaUzUeEzAr7vDa8iNNcE/jVRyA\r\n1AdL0dS+rlsJNGf82HkfGMo2XpKDSQnV7XxUEINMaiFbTVnx/8juBK3pL4gZ\r\nAQIr7EwIyFJc1wuM3Eq8YGEFfSwpsF1y2Y1/VmXDE35R3nopumV0leHOXE01\r\nGEhqL3FWZ0G9TOv9sAJAnY6U5SWoQGhfRr8EnLbnivB52D+lToYXZUPcgnaf\r\ndSfljEuwElIZMg/NpNg0CkiGsSCeHYum6e4=\r\n=x1fg\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2-rc.1": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a79a3b48663e9e10191d0bf7c2d39a449c84c8ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PEIddqOpoiAmpiqAALfr0UfB343yBByGJNBsNk5e6kfBiXwIMlP1OZtbTsYYnScVxBrE7dXBJq5g2HrO4QRs2w==", "signatures": [{"sig": "MEUCIHwIE7Y597dleQvpOc7+xA5T3WTSCK7uNNoBxfUTYZsfAiEA4YE3rJC4lAJxz3mIAlS5E10LydyAmPIV3lwEF3liVb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpcrw//XxlZQgJsIDab0KEQLIlbeogL9O5tm861MRs70Yy0hRSYPSnf\r\nj7NT//VgQeNB5jolZyHatztmxKZfjk3vfmaFeiJ5scEvce4xQQz9nYlJzJAi\r\nutwPiuw7hV8tU08RHhnfzXHVSKLDRIWRdYqZcKlQwYYVThzb6aJmYl0Jcqxk\r\nHIw97PC9D24UvAKjuLMzcCax4CZZx3Kg6WKsi9ijbThYrWak7O3v7ZH85tES\r\nf88FN3nvhRoBicyTgcsWyGLJ4BatmwkNdyJL60imhdhCz2uoGAaqiVb4HRzl\r\nWaCmoTRVG7O/RXzC/r4rV2HFqKQ29uE79W+KFk9iwnrtrSGmWJRY6r2wBwHv\r\nXvv691AvEhEcOT8A3svT6TdBHg8fU+YN8QNwcqvT8Dkk4/Q+ABBPW6oXTm0E\r\nRevIVo/IC7rU3WFoy+s2RG0D6A9AGjPuxCH2b2/EcOm2ZcPG3CVbfGe16W8R\r\n+xXCmN8feeko/PKZCanQDVFMoPGTmeeZE/I4gBDH5+dFdTuITC0MnsFDOGyO\r\nFkog89eoAa2Wohou53ahdPTS/3FNQS+Yz6JGesl+VFOfy4rPNjy+Ww4a/dQm\r\nta37g5TWlQk4E6+JsNqjwHXBOB4rIAaCzg092bN9RdSNgr7dZIMqcdnTpHV3\r\nIBlqtfitJ9PFCnH1IprY70EN0flMbzGqnls=\r\n=9la5\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.2-rc.2": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "60b1b7977ab1417c1d1394b9a60c45affd262cc2", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-0ZGQVhidixQlFWvhjUxaDIgwH0BHFeGD8MQDboX0Deb5MmYNMC2Pe7q+VIkj9l3NDK9lWBHR1jyWnzxujr2w5w==", "signatures": [{"sig": "MEYCIQCrka+9Nu3jnF0Up+zUbrESay2jJxQ0/acNrX9oa9F6PwIhAJPd+TaAiKOjFhZuuld6opOg7kD/TVT4YTz/xyvlikkJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235}}, "2.0.2-rc.3": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "641f2f7a4c823bd13af2ba08f7f0647c2aeba863", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-/RcDM0LAxr0jDBzJlI3FowiQbSSh4mkXZ8GLz6hGGEWMHjzSJF7ReyqKhMHCtx8g4x0w6HPU3EVBHiM9+b/spg==", "signatures": [{"sig": "MEUCIEJWwvXjbeRpNeHcNADzK8SmiUk10+WxeZlSAJW1quBlAiEAsyVZ8RVrnoSXiB5pqbmfoCHpB6f4wqHflQghqCO5DUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235}}, "2.0.2-rc.4": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a1ffcd12d07d7a5a52676cfeeeeabcce019dbfee", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-gH8fJXzD39lQaTNkothspL1vAvOMzp33hrzwiPhnqDFPPhgjv9LYjvcVs/FflX/gi85zuHdhsfsPULDeIswuqQ==", "signatures": [{"sig": "MEUCIQC6/dnlgf0tHjgPy7wUqb2ywTLR9QbqIBCzF6tuHjp94QIgL+iM6Xky7c/pPLr/tOjFiUFqnx8PeebN/DoxxaEPiHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235}}, "2.0.2-rc.5": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9ec7ab61f13be9298719f3692fe54c0cf44dbf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-mUfdZB9435GBx1fS/z/Ve5gsXNIyQpSEs2fgwDI/uALdesPdvqw2ZHGtAwv6yrsJlU2T6+gdaJKuLzEhUzezuQ==", "signatures": [{"sig": "MEYCIQCZUinI2xuqTy2/ftrHy0KLUy4jW3E7Vwe/S+AqIHL12wIhAN5OVP4L14axeSY3P7iRiKcBL1aEdlrqNgbInlpas7of", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10235}}, "2.0.2-rc.6": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74f55986196b73dacb2a5a5c40fff8fc1c743d92", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.6.tgz", "fileCount": 9, "integrity": "sha512-6LyHBYZqduOeV0+qJtpMi/6UjKAIHybo2gnIXgdNwa/Tz8ymrA+18VRXHR7xejpNojEvE5l04Jh8fujCnTf0FA==", "signatures": [{"sig": "MEQCICksOme9pFy/lUw0H43c0y2URu/aDxC88Fe2zmbOaJS9AiBEX5faNahqu4ed5GiH7DXRDgiTVQYbWj2aIPl6bSJSdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10996}}, "2.0.2-rc.7": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d547030f9bf6f9371893bfe83d2d89e02eceffd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.7.tgz", "fileCount": 9, "integrity": "sha512-Wdz66xCaroWEBiDAuXJYKB9JF2aCGmTk5Wmow6/CJlPgIvXcvw3mu2pb8ySu7hLied1/u9q9xvVmlmFd/nex4A==", "signatures": [{"sig": "MEYCIQCVYay310yIkmZ5xU7t2GqZ52GzHCReXhpgMwJ/XMuurQIhALuqAvQlz7mnOaAQ8Xq6MHC3wH0PPNqTtv6BaOP63DeP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10996}}, "2.0.2-rc.8": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ae86c067328d64431e42c76b27fd90f04de04e2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.8.tgz", "fileCount": 9, "integrity": "sha512-FymNfnFB+8TEU3cmIBpygJrGJUh3mO+byV9cjXrfG06mopL/p5hKOEA+Z1GPLMiihhTJAuTsfqd+r4uPLOGEQw==", "signatures": [{"sig": "MEQCIEmgZtoyaWefHsgt/664bZIz5rvZop0ZMXrjrPNQ/mcVAiAGmMRmx6kJXaB6aYbMTur7t5AuYzs065TbH/WbjLiVGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.9": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "942c9a97a33f1ed0dbf98f0c32f5b9f43a95f1cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.9.tgz", "fileCount": 9, "integrity": "sha512-cFBt79PHdxhvv+QGIwFJWzJItmz6kU2mMCTqE2NOSAJSjkCqLk//KZ9SvT1u/XlF+m+z89mZc5a65ePEIRq4/w==", "signatures": [{"sig": "MEUCIQDjHUng22+31h0jJwaFFIkMq2MmdSHy25mN1+fEXyF/sgIgU1V8PpFtybytDTmMPM1eOvJ4+4dH+trdus4nyAQF/IU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11190}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.10": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7f09a70df99413b6621e2c5deaa8c5d48511bb6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.10.tgz", "fileCount": 9, "integrity": "sha512-qc8XzbofVu3H18s05jLHLEeZpAT4fI9pHNQ5knTCQIXqFuuIYKuQV3jyVzCuQm8L6CBDlMAaQZUSNIf4XUzO1Q==", "signatures": [{"sig": "MEYCIQC/PRW8AyCjbgBrgT9fNhjR4sveFeKdk+YjJ5KL4zB3TQIhAK4zlrfXMlT9fkb+AXqvoB5FiDFDAhaoscFf+nH/+VNo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2-rc.11": {"name": "@radix-ui/react-label", "version": "2.0.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "30689088b6f73e3d6a05c4a05d18c5d23a7fc089", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2-rc.11.tgz", "fileCount": 9, "integrity": "sha512-YPbnlQ+h5WawRsWsCU6iAfa+iD9NCTHlm5StQER71RMy7zx8NpgkvbOiDZKc46ORMIHqjkoUSt0rnNTkT1weqA==", "signatures": [{"sig": "MEUCIQCmXP76wSxxhxFxOQxZnvbxcjlt2aooUyok8aZDR59vQQIgbXkZJDLA6co5SPfvR3v/y0G5lVqtM+RxY6zJ/zmrE/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.2": {"name": "@radix-ui/react-label", "version": "2.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9c72f1d334aac996fdc27b48a8bdddd82108fb6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.2.tgz", "fileCount": 9, "integrity": "sha512-N5ehvlM7qoTLx7nWPodsPYPgMzA5WM8zZChQg8nyFJKnDO5WHdba1vv5/H6IO5LtJMfD2Q3wh1qHFGNtK0w3bQ==", "signatures": [{"sig": "MEYCIQC5eTGK4Bl7eQD2rEs2nsvzoHcSoDykWCsVxlEG7r781QIhAKk7Yj46K7qoAGjlpyncHELm4F0+M/SWl9LGyxDUY5GO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11152}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.1": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9be28922983ec942982f0d004389b587fb7c485c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.1.tgz", "fileCount": 9, "integrity": "sha512-1eZLl9ozbjUHkkPO5r6mxQpL2hOzVb08hwLlVsrtSllkWPKSRzPZ+zxxZB/7weAT5w68k4m8bCmXmAHAEUzSCg==", "signatures": [{"sig": "MEYCIQDoWkTomUblGk7qwDFKJTGAdRijHQ41KiS1bR9WrfIv4QIhAMvm6e32ysjBQDoUzbTRLuO2As4zvkyo0TKCiwIFkK+9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.2": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "30cb618a7f9933d4473fe2af5a98c11916150df6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.2.tgz", "fileCount": 9, "integrity": "sha512-x0jHsMtkea5rAwRTLeDASdiirLfus8cXB/TE+LLRjxvqt7GbeCqzXKkuot/sCQEirSH+RVt5Lb1Z5pAttyYEqQ==", "signatures": [{"sig": "MEQCIEqAaZg1ZlHP54ekKOqPIAKwBpIV1Mc3xsSxh8Dqwb4lAiAyU1nxZLqsDaCyAZ7HU/vajECG5PC/MGYxDypcfYJw0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.3": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d9a09e96184755b98aba7064e0139af7749dfc34", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.3.tgz", "fileCount": 9, "integrity": "sha512-nXQcBoqACPlgJKSH/Pe7n+nvNL1aMK3b2RrdOZ6Mq2cNKbLVxsh+O4R+U7GYf41lzPgK9XY3kXSGFLKUEz33Ng==", "signatures": [{"sig": "MEYCIQCWEcNSa7UU0A4AROyaqWkJtcA/Weh4UGQnejwZyLIf9QIhANdxX2BhTxwzyFKjJCOQTr8MrUVGUsOPWCTAizIGdPrg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.4": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d5678a9920ad7f710734b270764fc21e60caa57", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.4.tgz", "fileCount": 9, "integrity": "sha512-K7iFxfgqhEEspCP9/AadZ80TG+5zXpBbn5Ql9e8Xl0248ivIq9fbi/BWnwqYxRrcgFW9suP4D/3C59INXrSWJA==", "signatures": [{"sig": "MEYCIQCUAiHj9GtxQyxANT8RwP+exrl+8eZwg2epiTPtwffHTgIhALFI04GoO6jyxUSNEVvUjxQ/j5Cf0m+IAXw3VeCQMVmp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.5": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "405dbe1e751535bde44d755294da846a91316668", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.5.tgz", "fileCount": 9, "integrity": "sha512-e9uAlGpP+TWirxcH1IeKCcOFZXu1SbCPPgLcxH1zC21SkWMoSKcnuY/fpBQOZi0d75dgJ0fBbCgCHwVXSJ454Q==", "signatures": [{"sig": "MEUCIE+UODJBkbmujdwVysq0hTJzIzjzk0N6/g7TADyqGM3GAiEAskjxIFzny31taUT7LHaMVVdaE5d4RCdYbA3obqO5axk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.6": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3004bf251d916e93f4d871e14c251f3c345b319a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-au40fZwtjr6QpsHT8sOdiLAmMDe5IoDAzd9dTes6YMBBfS8qBG5jRRIGaKRJ7mm8A03wNGwESswC+DEVvMqtzg==", "signatures": [{"sig": "MEUCIENg/7FE8J8t3kV4heFcHSjC4hhRyGEt1TPFys81mgGDAiEAi7uVXSWCXRs/yXroxCVzb6jsSlBRWpYA5yQ/hGHPvBY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.0.3-rc.7": {"name": "@radix-ui/react-label", "version": "2.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "27dd9a36e70db33b3debf9eb230f9d7a7144379b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-iQ095VT0MJo94VPgr3jidwrkw1dZfUWxWG6+3gIX/GmfsK63OcDLM923/fX6kuzDb1IWNXaiT6mmt0bTAp/Trw==", "signatures": [{"sig": "MEYCIQCOM12bBIp02xXov+vEdSQtilyxVUaC20GBEuTUdmjCXgIhAKs2V7SMuU4DshOcToQLcCQr87AP1bUp7EATZ8dJ7Ot3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12242}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.1": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.1", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "27611aa8e7657e01213f03904835bd24f07802a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-gSad/Flva2pc3t/g+r2+mDq4YVY3oLDszKEawzsl9ktnceIuE0ICzrOGH9aaud0YmOVrktHd+QpPpX3YHZvHZw==", "signatures": [{"sig": "MEYCIQD3qrwb+cxQQhI3ced+BKyADC+OwwsiMQB8/IjRXoxNagIhAMRAyHd6M6hyiZt7iw0cO4eSkpUOqmvnWUnGO0kjNBLA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9549}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.2": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.2", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1500bed1f54beda70864f2ac37d04a740e4725f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Hhd+bni4gQC6BdGfv9WpPakFEqYywhBRQFD9ZX4ehvP6bVd4zeVbz0njtdUpnT4//NfM/py1N2JoXOk3Mjo3jw==", "signatures": [{"sig": "MEYCIQDVF1OEYNuEsM8fwULf//W4CyvAEbXw43+L7IhpQvo/mAIhAJsIDKTMgjk31cwG+I2wjwJT6dqP/tnhRyj0Nm3eTjOs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9581}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.3": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.3", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f1cdf7f230bc04981f002c6e695d3ac556341660", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-VVKUHrtQCdX2DrgZ4ArduUtFtr2SV1lIhk3zI3kH6VZjYBMmd0iOzqS162CNxR5tkY7XA7o5WKVGOs9cMbloeA==", "signatures": [{"sig": "MEUCIFZDyjIqxMuLcFtV1cJBRfGkbdPsb95DNzh7SDpRcgMyAiEA+LRahrz03fEGLzbxZsDzYKIHDSLvhfTeKqe3HADBIkI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.4": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "368963de836612309178da0249a54e061ce11fc4", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-bWEpB008B52SOF1t51Qi1ofkvdgOMzzHsjSC4q03VN8Oy4WbOQcITlZsFzuYuDffaskLD83C9STaH46daIrjhA==", "signatures": [{"sig": "MEQCIAvRKtiI0uOf+CLYsVOb3RwBiRD1hSkxsd4welHQkSb0AiB2I3VpZRwyUqoH6Fy++1eqZSHVk2j27EX8zW/BFXtI+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.5": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ee66ab86bfb77a31917723cf48f056817ae4f48", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-WFSrhxORPLf4TusobrNoYHo9GKsBcICIgUj1pgwy/lRmmKYqV/kKK0onEJ99OisidTZgGFpUlUwgOIHyiNHYog==", "signatures": [{"sig": "MEQCIF965Cbd3tmaANrl7wqnpGrnsIGqjavmPXTYg4wq/kGyAiBl8i0GUSvoxhNwfNqbbJxHQe2ERzaenYiG90At8BaHrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.6": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cf64b90c9099e40904c2371ebb0102c975cbdf53", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-T2f6amCjLmMkQuJYWi+tBwI9gl/SgMFCYH+pOeG8M/IhF1WKjV3uDH3vSlRaZQij8zrS86L1YDXlp3cbN50zFA==", "signatures": [{"sig": "MEUCIHRRKx+nOMJGhVaQodNaYhpOnKQFmdgJl52CfaVdp3+dAiEA1rhW1zy4isvZ2A7kqz2jz6erZo8NO44cZjAD/MVnqzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9423}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0-rc.7": {"name": "@radix-ui/react-label", "version": "2.1.0-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f8d37489e6dc86ad01343b6d8ca47cf64564d004", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-cj3KEu/drO9HRIyKX80OLu8pZp0JKBypge9wAeBb/ztvkF4icDpVDmOgLQGh49LAHAKg3t5Wh+rEJ3rMrIovaA==", "signatures": [{"sig": "MEUCIQDVVKtzr1qLsCX9CZ14d1AogUbF5yy0kAahIDRESRaw2wIgYgi5tCVDxDaaReVQmCwqU3MzToQ71H6U3XbCFRkl6Qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9451}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.0": {"name": "@radix-ui/react-label", "version": "2.1.0", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3aa2418d70bb242be37c51ff5e51a2adcbc372e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.0.tgz", "fileCount": 8, "integrity": "sha512-peLblDlFw/ngk3UWq0VnYaOLy6agTZZ+MUO/WhVfm14vJGML+xH4FAl2XQGLqdefjNb7ApRg6Yn7U42ZhmYXdw==", "signatures": [{"sig": "MEUCIQCnfQPeRuG8RaYkDdm/mJXr4VL7sIDFP8yEj/viwwhgKwIgPOyPxv1/tGNQAncpWX+IHcQp93kCPrtm7hODR3dgSks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.1": {"name": "@radix-ui/react-label", "version": "2.1.1-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a0dceb7d5efc812e4d101b14102b65034b00a7dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xVOPDoz2nv4TW30ZM1gVWArAe1Z3X5qSedHyqTw6SxaBHmoqbDT+XNuqEHbUl+HETbKuI4mNiiyvfyUBDwcjig==", "signatures": [{"sig": "MEUCIBeou6DbHxpWKDZ2jvPqvxYqXV4KVTWiX5vhQhu7FjAEAiEApwbPj7jkzLpcffLS7OPa/fTyu70bT72PxwZ0Zt70tAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9451}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.2": {"name": "@radix-ui/react-label", "version": "2.1.1-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7db4630165e7e0ac30c79d2ff69d6cf6b44fc067", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-bYmCOMjZXR1vl1wBmZIYOxkQJVE/O5SX8S1tCvlvKcVSmYn36XgksQAuAQ6g6zjjDqLgLRHK69XaevOhB/XujQ==", "signatures": [{"sig": "MEUCIQCQSfExpW4IAYjh9P4fR50yyPYbc1I6sF0eMeow+Ea15gIgXKwosvlzW7Gua2/r1gGS2MGBds4JLRCF9+KBRF+WJQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9451}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1-rc.3": {"name": "@radix-ui/react-label", "version": "2.1.1-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9716d1dc98717a6d3f889999f4209d95517ecde1", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-4hasjJkzpaacUhosNgTG43gYgVwz3zABvCuWaJdOL7oq18pucHPyNbXrCSJNF5dVbubEGUmbdS8V7M365XdCag==", "signatures": [{"sig": "MEYCIQCR578qRiZmzmpuFZ1GKyxQHAhw9LgjkVoZYywBPoxfGwIhAOMmzUf7bXZJ5R9dkIXAq9BrFTX0UDySfeFCV6oZq/hb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9451}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.1": {"name": "@radix-ui/react-label", "version": "2.1.1", "dependencies": {"@radix-ui/react-primitive": "2.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f30bd577b26873c638006e4f65761d4c6b80566d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.1.tgz", "fileCount": 8, "integrity": "sha512-UUw5E4e/2+4kFMH7+YxORXGWggtY6sM8WIwh5RZchhLuUg2H1hc98Py+pr8HMz6rdaYrK2t296ZEjYLOCO5uUw==", "signatures": [{"sig": "MEUCIQDXte27/VBCVHea4ZdMnyx90FErqbveb04F13v0M2imIgIgM+xVU4IsLHY5+cdoA4HyeGPYSCMT4gkmqBdK6waOBmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9413}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-label", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-primitive": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "55c2e229468ecf60e4756d80c85d65689fdd8389", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-XwrQfdphFLLjj1IaLVcDN+sj5RZ2V1mdBW3tXXY4rqKKgTOjiRIMsnfAje294zIuSS0+JNFWLN7EmU+UXqJMsw==", "signatures": [{"sig": "MEUCIGJMbeyDxAd6qVT4JelXs0abK0xskbDMO3KJ4HRDV1xBAiEA+6GgOR25FmAcQj0U3Pivi8iZGUUaNjGGlzY1SEsvyZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9404}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-label", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-primitive": "2.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7fd5c794476745c44b41f77bd1db7244af1a627", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-bfa52rZb1j9HQRBUElxYc5UpLEEU4a2Jvr2e+0xDrnuy6FskycTUhZFLxKeU9q9GBKz2U83pysQ9xi16rTtEQg==", "signatures": [{"sig": "MEQCIFrsy7iFOm/vjIymz7PdcwBaiZvFkg6lCBwUaf1Jrp5WAiBeiHCfAWhgARTFm3URvYng3LMGLcBW2zZb0A9gSaVAoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9482}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2-rc.1": {"name": "@radix-ui/react-label", "version": "2.1.2-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db29150735102099ff60e6fb8ca317ef295dba45", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-GOwhenzFaX8GVQ/5264LJ6ys2lLupXFvS8ds/1tjZ4rFQ0JnPENj9trC9OVoK6Oxfut2WfccXOe2Qk0F1nA46w==", "signatures": [{"sig": "MEUCIQC+wsNwkrtr/w0E+nNztwARjbP8HPTJpS8IVcNiE7aJzAIgDYQLed63zLLNESUjk3sTpeVEpKGRiWAqKM4uLDPuyOw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9664}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2-rc.2": {"name": "@radix-ui/react-label", "version": "2.1.2-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.2"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "60ae2cf739d081297be281c07a549685c5a8ebce", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-a6ywPib6PGshAj+ahnbCMvgZLc/H9wQ22J5KITIm45uVGuSfm9xCP35aPTMASJ1Ice3M495kcdKkO4eluBFTUg==", "signatures": [{"sig": "MEUCIQCPllHkQKOB3dMAZVqnqWTEYbdoe8BROhln2x+NAtM5mAIgBKSyHuwPA0QBfzSJicaky2cFozNDBWz/sDjo7yUd8HA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9664}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2-rc.3": {"name": "@radix-ui/react-label", "version": "2.1.2-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "741a1fd72ad2086dc5b12446e86e23448e5a3adc", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-LtuhXnSFssvPp+nR8zoCKyCbeVXlTOtD5RF1+u+EoiiTBr2QVC2T1SILOGpWszWjblUKRowXTM+ZVR1cUEL/CA==", "signatures": [{"sig": "MEUCIQC8ycQaVHfBhwlxuXMcaChoupnqLxa82WM5osON5oSk7AIgMSF2Ea5xC5BOI8IxuVcCFYsBdv/1SEpanD/keNvxPSc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9768}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2-rc.4": {"name": "@radix-ui/react-label", "version": "2.1.2-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab04aa515d3c1421fae798a1ec5eb22b7351a182", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-wykhj4WK1leu1cLkU5Wd/67OMsgGhfhsVK9cTZPhPfn3kou3/G0HfqTCRaS6m0u83cw2k+Mh+GkuiLj6HUzIIQ==", "signatures": [{"sig": "MEYCIQCtoxTD6joVTtEqFDiBtm5LkP+bqKHv6gPQWDV/mmkK0gIhAIj1RpscnaND6RR3QJrJsH5i8l61XzmcF6kXggc1zR/w", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9768}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.2": {"name": "@radix-ui/react-label", "version": "2.1.2", "dependencies": {"@radix-ui/react-primitive": "2.0.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "994a5d815c2ff46e151410ae4e301f1b639f9971", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.2.tgz", "fileCount": 8, "integrity": "sha512-zo1uGMTaNlHehDyFQcDZXRJhUPDuukcnHz0/jnrup0JA6qL+AFpAnty+7VKa9esuU5xTblAZzTGYJKSKaBxBhw==", "signatures": [{"sig": "MEUCIAKP/9irKk8Njt9DN4Btw0fAyWlCP+bP/FN0jjssUXctAiEA15MxDxHj2lkZdnWsViIENZAe0oXBicqCgE4fEAlJScg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9730}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.1": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "003bb7e0db09f2a6c06f5d3499102ca857c5e971", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-kdgRLtR+H/raRMBVES4/3hgK+DUGYhXT+ZDkqBdKlsHpNNBZ2iKxMCL1J7OillCqmJv/9vXfngfpAhq+Amw77A==", "signatures": [{"sig": "MEUCIQCMUUjckb5dtXa5qodpEEb3qBPPKSrkdzX7cJXxe2iL6AIgDgFhFi7B6EOS/ad1z2yB0f9U7Fpt27faefsEPaonmj4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.2": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f672fc69c706a509e9f4a1174ca5b8d26ebfded9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-lzfZtGOIJlgCt/lG7oUO81mJD0PGmOVoNOqnur5S+OvSI4vX7zSG9T01gzBZa/XBgmr2qVinuaYzZ8ChxLSBkA==", "signatures": [{"sig": "MEUCIQDq7TRePbX+OOz+3sjwVdk9MUpJcSbW7vqGJQx545rB/gIgMJhpClK3u0+ySDBcMiouBBNNxL+dkLrsgSwwbWZ7Sp8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.3": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1ebd21e17007dcebdf070c2c32ce856c0d002bb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xlE2dGj3gDZUDtS5i74OivC6W8j1Grd/ArFT4Dwk2knyP+BjPb+A3N57R6gItBAIIZCPfHL/k6nDbNJ5CmtHLw==", "signatures": [{"sig": "MEUCIQCxrBsIUX4iVASHcZ/6YmEIvBCuYgqEvwDYtC7ECgkMjAIgWLcNrfVbToYwFcQ5nSgmMctwMzWlLj8DJfiREvDY4J8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.4": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ffbfd0f0a7f2c26dd5b5043d4cc3b20b79bbf72", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-BkXM0mgcqpqXqiRfsX0rYjWSLtk3lAmjYSFmcmyxpO2JKVtEsG5yy4p6tZQ2XXgzXImqywZfgfEui7L0pzWqqg==", "signatures": [{"sig": "MEUCIQDRLEbT+AOf654lov4z4kkoF8jCdloGgg+IXjLktN9a4QIgZFuhAc6VrZGyjjnHRlWlPNCrAKa7AIY1X0SshFObpdg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.5": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3133379b0283ec5a595e06653e18c927aef2e40d", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ID9j44irIl/aZroIAhIEyQHujoqnsirHMMnGwgT1TUlmp/Yz+ksqT2mCHa5r9ix4HhMIFfAX/sUyWmeKnumVNw==", "signatures": [{"sig": "MEUCIDPWzL4NoLdeO1PJgMVX27LL8K7H/wIqbOZNo16KRPahAiEA8FeCZxrsO3/GQb5voyqwRSJtWqifM7smFid9o7WFOWw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.6": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "af001a8d425d237dd1f5b0bd01299935c3f4bffd", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-4jaULxKpf/F0BjXSS/v3XPqAmPuQVfWs48+xwDewgsQXWwQjVqVT0AE2pmsg3nO0xNWgm1dhLxotOQE4Jf4rBA==", "signatures": [{"sig": "MEUCIQCaN/+c/JFal4LhLSnj+k430uL6EgF+DYyI91pR84nOwQIgKs/n+0UxJeZ1S2FuU0M8L3DcA4wV1BvJBNCwKPPStNs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.7": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "66bdf9c5d03ff57ab1b74f72fc32d2c9dfb4fac7", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-IxX8jmg54++F6HtTtnlnwnBBFvnzHOJkKhkCAd6EuwXzUxE37SGEh7BidAS851wxwu85x1C7WSJ2gRozyWYcyg==", "signatures": [{"sig": "MEUCIBrOxFfjXyeJ80gpac1SLIyQCCqJ3RR3xVt39brmvK/1AiEA0F1+xct7kgfuyaKB73CnSddAHV2KEmMc3erseoy0aqQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.8": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.8", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5f49616a4ea85a6115e60a113b273f448d9e09c6", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-dGpaobEGZOOnsYWw8hMf6fWGAoxE6Jd/LbxEYcO4cN3+54MK0eiVqA9y31vOQzz/2GL5O1X6eBs7ickP73U6RQ==", "signatures": [{"sig": "MEYCIQCcazAtonoYvUkg0n/0ZJ/d96D2mgGkKZVDWfHH7A9VfgIhANYb3fd6v6Onsg1oK7BWcaMk1MrF+/1rxKmTe0JkvvNJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10165}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3-rc.9": {"name": "@radix-ui/react-label", "version": "2.1.3-rc.9", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a957d5f9ab372e6aed70c4a488018e3b4c735af", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-OWgT8YZGiGpMgXgbQS5GSn7byXMhkuJZBtNZZA7cWO8goVjaJETA0cHLRf9z61VA0iNt+P1J3Fg8WT6Nl+5aOQ==", "signatures": [{"sig": "MEUCIFZxSTc89Q6otaQRg0iCJpBlnH/OgYK+9hy7QTLidzIyAiEAkI0FAJxSRcH6/K9XnaYIH9RTaQ5ySwN2DAlq++cvDwg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10165}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.3": {"name": "@radix-ui/react-label", "version": "2.1.3", "dependencies": {"@radix-ui/react-primitive": "2.0.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "de83641c90c57e58f0d1e1943d450dbc3936a23f", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.3.tgz", "fileCount": 8, "integrity": "sha512-zwSQ1NzSKG95yA0tvBMgv6XPHoqapJCcg9nsUBaQQ66iRBhZNhlpaQG2ERYYX4O4stkYFK5rxj5NsWfO9CS+Hg==", "signatures": [{"sig": "MEUCIEUqQszzW+rMCSfxWDEgOf31PIn1/+ZGwJX10+IcxGB1AiEAk4JdFSTQgaFmr3Vn2kbslgluybijTApQ/6MpRtjyoqE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10127}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744311029001": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744311029001", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e4f489a2f2aa363733d5332d5d03ab3dd88cb2d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-u4bW9QFGeKLEP35urhJTNXdQpSS40zphnsIWY5SSLbWYL3826seiBn+UFeAozn/uYG290dWam5OJ7GI6QW1tMA==", "signatures": [{"sig": "MEYCIQCFgq4VoDSFThxiLNrLCG2z8xSJ4EHEAfSidP30LXkNRwIhAKzsBUqN6v+yvKj3eEQOY9WoyfzuXoQKGihRKcJpmjnq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744416976900": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744416976900", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "696d83e4f4edb85de2c0a5bade7a8ca749954175", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-Feh6YkpcSOyCPAt6a3bY3YXWwrVpG+eiB0XizF4LM3/EyGJbVxzIBqfo9NuBLKCN8pgKidCSfy/lT9m+SX459w==", "signatures": [{"sig": "MEUCIQC1Bk4jogmCI8DHAh8buU0ryD171zZzdcuS/Zp1aImEAgIgEYAFcntO2gXGi/gdJBFzv8hX4qDChzkLmEMHTmHTsYU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744502104733": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744502104733", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "47b99e802f17f3182f9d0e5cba61e4b482847d2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-FmqWDx6b1xUUMTsfpQLulZgQxEqsg6SBFlElfNzWf2qzPY7WZCVvheNiro940fUIlF3QQWGSrChrr8DWTpF5MQ==", "signatures": [{"sig": "MEUCIHnQ32wRMhIKVhOBA/rDJ7CchikUHPJgRiRrW2TuVoqvAiEAkfWCfPVgxCCiToJp+uyMGiycbPX3BZmnUSErv7813VM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744518250005": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744518250005", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "592fb387c71c46834b07cd3039c257e9503ebe8e", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-7Wq6mjlf9+a6FEyGRhg+4lUmsUfPOnPRAX5RLB4y80t1IcazjFzuuxxvMQWEL68EfuBbOrHaKTcBMS93J8LBhQ==", "signatures": [{"sig": "MEQCIB2W99skedqKo1s7heVfpAz+BWj4fTxTRe3wLUQKy2fcAiBzFXTwO6b96RTQjS/nFonM4wfsV9LwvAb5p/EEZUNDpA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744519235198": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744519235198", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab72391b84060031f3f539fad4c710cba819faca", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-uO0DjzLCyVzQtpjfx6zACriRTeUeiSgDBSLXMunFZgh24aLkp9yGV3AWeGgkFM6KLjAxge06k0maqVOabg+Odw==", "signatures": [{"sig": "MEUCIEg+UBI6nGraP3HV6oNxHanVJpeKUoMifzv6khcZIUvzAiEAj2ocbtM0DpBGJ7Q4Dfha4I6LgBP+BubLy6+gUFFsOjc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744574857111": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744574857111", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "80e2e5da9ba9292609d6b0c9032d7a9eceecc00a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-dlshFKL6tbTn+Lcic55TytTdmKGzXVkNcJ2th8MlnPiD83AtSZUWWnuviPdgp3jruihbfn8DHphZSRshN+3gGg==", "signatures": [{"sig": "MEUCIQCYdWUho0XiwBp6PbTgx35Io8bbNqPWGynX4MOHpg/8rAIgOGjLC6XzrC0wiRi6ToASSq8jFEXoK4iZVbKNTx2RJeI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744660991666": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744660991666", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d4cfd95777789dcdab7ee43150e1598a12d60d34", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-98qO3jW3jEFoEp7mB4f3vLMd4dAA3LYfQOfQtiVXK2Nz+VIjnXBPfMNQf8HKfjsiqL8a+A6xQzwiSm8cKf8Oxg==", "signatures": [{"sig": "MEUCID1CrmylphgTEQuXKybO/GACXyK7yaHc6LJ88VSL67DhAiEAk21Fp2owocinlBTC78gDmjTUzfbLLDECc3JuhZwTwHY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744661316162": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744661316162", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f6f2117d44b6723a6201c4f9f476f8b7ae780f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-jS1Ka3vj08gsQnQ9C5aK07BEpyiDA0qsgp4op1qzgftf5EFZ0Lrdb33erB/gnUf3eH91rKqWKAqA/VWMML9HxQ==", "signatures": [{"sig": "MEUCIFbvMh4ikP/Zf7C2REf8PZJMNQ03FYFsounQGl4gc/CxAiEApe1BCRmlAXxKCpcGzJknGRGaHr0JpETvMDzEXb2WdwY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744830756566": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744830756566", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b364bcb8f31ffbadc32a8ea97959ada36de121fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-SMIy3gTA9Po+XiLk50DG6FtJ69FZpHo73zwVD4+V2rqzE6raJYr4UJcxmKOT76xuieAygPEjHjDGvYnKEoYsRQ==", "signatures": [{"sig": "MEUCIAkfM+hh13ac6R3Cg52L9LluTnnKnl6z8TZcQFDvIYWoAiEA0Y98rzTTQ2oY80kaA6UGomi2YZN3yxjnybI/N1SRFyM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744831331200": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744831331200", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fcc1bb0d528d1184d35aa64c895b04bbfca811b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-iKDBx7lPMu4gCxVDM7GKKb273SROU+62Lh7c5A//ZpiQFGGgSalG7OCEH5fimAypSU0zaPgLg1Dn+6/7l1LoCg==", "signatures": [{"sig": "MEUCIBKcYusf6JZ5e6NdW1YG6pxk038To5WdJkqyo3jc/iQMAiEAuAUyd/3UxQyap2o9KG92tjH+UIWCFiKoaofMr2ZJoc0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744836032308": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744836032308", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1fa3dd8dc75f4592c208249437a04c2cd06d8e3c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-RJQFDF9M73xy6LnP1joU2TzUWfTwNkhycnrGp6LsgQpgYrdZLZEm2NDK7Bfl+66jcm+AnAFvQvEpCbFv6X9kQg==", "signatures": [{"sig": "MEQCIE2yX3IY2JQoVjboHqyfqxno9x4Qah05gXo+NrTlBdRAAiALk96fs4LkNhbXVzYe45YeDv6hcFIWke84Jk5qF6k1AA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744897529216": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744897529216", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f1a90f5dfaa927a126963c5e3f3db818d477025a", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-qZMFwW9QyU1Yaa9A6Hx+POC4gYSyOHe4Sa5YCFVGoZN8+JDVAeifOXYO7iBxi5ncb6T0TBE6V9MosjtR83H/Dg==", "signatures": [{"sig": "MEUCIQDFL38aPhr4cGKBV0eLT6pvq8Frk6/OgY9Q4feK68LdpAIgefAO0fEfUS0CUAAJM+HTdg7bysd8WhSSFMjVxzjUMVw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744898528774": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744898528774", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "edcee671d28baa5a38672b9d33c35a38df9102ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-gu3I7/BV7iQyIIzdDFSfbYn7vC3ZLjsOp48q6ymWmfDqS8McWYbhTXzza8AvuwJgIuo66Ay3i01Yl4Z0P3+62A==", "signatures": [{"sig": "MEYCIQDBgAzLJLqTIoWeO+9jaRmNBOOa+skqrVnxka93LPfljQIhALFbVdY7mJieydetFpBz2pVTRPt5B8o3f0VSHULZF2KQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744905634543": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744905634543", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f81d46c529f9bb90b527dc24780f3ff3332bf77", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-51P8z7S+W7jcoQdFnojYofVNhK/HZxN6zYSL9siXLBkV7V+BoXyzLoLrSx25lAbb5AlzlHY8nb5RS8oVSWMnaA==", "signatures": [{"sig": "MEQCIE/tsbU3AkxAujtKloQde70XdtLiYieXXT5DJVCA0XcmAiBEND2AfCRqRQfqjCNRYmr2xsAlhCJBW87knD4TtdWqxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4-rc.1744910682821": {"name": "@radix-ui/react-label", "version": "2.1.4-rc.1744910682821", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "48b4de8dd5d698157294a1693685e4c3c6790931", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-N8DsZ3sR2bag51US/rbX/NGrZXuwbZtVx3o5JwcWlG02J31OCQ+DoQgQYrvfAaIKmnU2RUxUtx+lZMyNjnR/Zg==", "signatures": [{"sig": "MEQCIDuw8nf1/ZZ0vHzlLhkF4RnrkX1H+3YGjfFif8e+8y/DAiB+F90QMvLsOz7ikZJ3ru7Yo4xZOtJRNXN8dKTVeGqTUg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.4": {"name": "@radix-ui/react-label", "version": "2.1.4", "dependencies": {"@radix-ui/react-primitive": "2.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e89607486b82381f2d28ce1ce022e7fb5f5a158c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.4.tgz", "fileCount": 9, "integrity": "sha512-wy3dqizZnZVV4ja0FNnUhIWNwWdoldXrneEyUcVtLYDAt8ovGS4ridtMAOGgXBBIfggL4BOveVWsjXDORdGEQg==", "signatures": [{"sig": "MEYCIQCg7l1q3UJYYoTZ7u2ZfeB1pgMVd1EnuTaUGbNkKtR6YwIhAKQ2mCY8xRRvKAfuuCepFn4tFetp+yNG/0pGgzgzoeYq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10684}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1745345395380": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1745345395380", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745345395380"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "37ef5b16d703e6b5de53ba7263050be83ab0c792", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-t/OQWikjynVlOZG7RZa8tSDgPEmmclUI4BziybXhK97SVpK6+q0oAS8yH90fYX/9ZIBG2rdO3nvQBtOfrQrdyA==", "signatures": [{"sig": "MEUCIALv2FwJHaoNME1VIh8UK9h+s7CqDvJoTs7Y2nZTy8LtAiEAtzY1eFfy3JWDQJAIStTLcbRvmpkBCWBq8wF8hBH8u1Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1745439717073": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1745439717073", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745439717073"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "12f22fbb99f11c3b23e0d6bd26707aa737d7b06b", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-txI8SxrXU+YLPcoUuBX50hkPTR2Ul+s7jPNHsT4Y5hEmVDzsOYu79gbwjo6Jo8qxeeB2rMlmo07k3kIyexWNDg==", "signatures": [{"sig": "MEYCIQCnA7InJNubwGwn6nkr2FH06sx3txlYjU0TJnldioSGVAIhAIMFoEd74qetLHN6VDqutnEICs5/kXcdddghSPXnDLsk", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1745972185559": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1745972185559", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745972185559"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2651df6c4bab60fcf075858b141e68039c45ebc2", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-xJN+LAV6fr8Xgsf9vUhawq/lafngxkhzE2cCcaiNsk+8tfDycPLYYKNxTn0QjJUFmbSMUjnL/pwh5AkqDt86fQ==", "signatures": [{"sig": "MEQCIDbgiN3j3UEma1AkhkaI/LBc2GsOhIIlnZxcOkoyBAsQAiAxoVbTjlK3PQYmKY7im2udU1qzZ5+n/iof1VLMI4DCcA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1746044551800": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1746044551800", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746044551800"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bfb0a13625c6b9711e79a08e8e6676c7ee327dc8", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-ZJUTjgPNziClyaoAE9zxQ7ibJ3F5PYcPWO5PkaJY52hjYycLY/jvVHQMZsXu1oa+lozJdezsCWqeuycHHScGxQ==", "signatures": [{"sig": "MEQCICctz93tgMe2bpPpCRBq+Bk7SGXpL24RXMcNSsIV95UWAiBiutcY5mfuSRubCmpyKguxIJy0IAAZxdKWrN7Us45BUg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1746053194630": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1746053194630", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746053194630"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "12917dace268c1fdebb3eb781b3a2d4185a81538", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-TvEev1A8uBgLALIaQjCqTPR4djdYJYDtRVdOeHhf7gzDjXOZtWBMmNKOWdZL8Rnv5S5zPTaLqaS7z7MXYm5dWg==", "signatures": [{"sig": "MEUCIQDwJFtgR2Reja0F2y4TtbeK8qMtE8kdUdkikY4qyNkAswIgWboFvUUIjsYvqeksBh+ILF98Ss/dn4/wgHyDJsK+Anw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1746075822931": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1746075822931", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746075822931"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "60800f5f1d5838586897b355460c879265c97f1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-TsV5C2DcmwTnhGj057bvXmCeC8ZleL0ed7Usd6C0r6nnru/MdfZ7u6eQ2sYb900/B/wPQAtAlWS93DgtUNRCZg==", "signatures": [{"sig": "MEUCIQC7QXq1ByX8KagEwFnjgyASz1YQDaUJ9vpDYytE/QvRTwIgZNgOQ1ExsnBzxLXJt68vtGcCIKXmvpE2OEPmQHxGcXw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5-rc.1746466567086": {"name": "@radix-ui/react-label", "version": "2.1.5-rc.1746466567086", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746466567086"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d6307077a9206fa5a9cb1e57934213a8dd59b583", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-A2ryRxniVabYiymsH5l9Jj2HtPFfOEpnHt/nKsRI9XgZcUGFQ3SRhEI1b81TeBL55ajHYwIoQ+htFhAHP88eNQ==", "signatures": [{"sig": "MEYCIQCy1/rSWydV++kc4Sb6x+6pntQ9IJNlEKAQLoFKBoAgpwIhAL+xi3fr73DCNJd1AZLnkKW9yu+C6EdMHREwHrCk4UdP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10718}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.5": {"name": "@radix-ui/react-label", "version": "2.1.5", "dependencies": {"@radix-ui/react-primitive": "2.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3815aa203b654d75222ede51ff728ca9c781dfd9", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.5.tgz", "fileCount": 9, "integrity": "sha512-P+NShZVCQC+6XF4z/fgwOn+ypZRrO/Ls1/Y9Eo58IycZrGDkpX7cH+wGBosDspWK0h/TlOhsb8V5VOScro+nTg==", "signatures": [{"sig": "MEUCIHCrbOh9m04iad1NN7K9KWpZO2h7uHHn3jkgl7BuvrCzAiEAjE08X1d3OtRtPTV2Vcm3P2lD2EipIs2ynY+OtqEh1Zw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10684}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.6": {"name": "@radix-ui/react-label", "version": "2.1.6", "dependencies": {"@radix-ui/react-primitive": "2.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "45ebd1381996d0311d199781bed15a092c7978dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.6.tgz", "fileCount": 9, "integrity": "sha512-S/hv1mTlgcPX2gCTJrWuTjSXf7ER3Zf7zWGtOprxhIIY93Qin3n5VgNA0Ez9AgrK/lEtlYgzLd4f5x6AVar4Yw==", "signatures": [{"sig": "MEUCIHcD0j564G6fmOpfyX74aCpSTdnkRoBJjhR9L6X/yASRAiEAjfDZt3o9vPccLvDN6/uDgPoZ+SUaUACbugozU21fkvI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10684}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "2.1.7-rc.1746560904918": {"name": "@radix-ui/react-label", "version": "2.1.7-rc.1746560904918", "dependencies": {"@radix-ui/react-primitive": "2.1.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/typescript-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-6+WFxPfSqcL9wNR41CkOyUsaYTaKbgSdMqyRsa1aqCc36BWhWBVbpor2WmdeV3eCF/Yck7ZAZLoQacv5ReORtw==", "shasum": "fcda4f803c84cab149e9b6bae804701ee053e773", "tarball": "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.7-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 10722, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCr4YE2BCIFgFkRqeK+oVN9xe+9oQ0QVB1viOro59yeHQIgeNrAe7oVjHzUV0SGqpTGRt27v/26+RSrWpSX/Dc+pH4="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:57.046Z", "cachedAt": 1747660587700}