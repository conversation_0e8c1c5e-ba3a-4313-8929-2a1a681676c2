{"name": "@eslint/object-schema", "dist-tags": {"latest": "2.1.6"}, "versions": {"2.1.3": {"name": "@eslint/object-schema", "version": "2.1.3", "devDependencies": {"mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "e65ae80ee2927b4fd8c5c26b15ecacc2b2a6cc2a", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.3.tgz", "fileCount": 10, "integrity": "sha512-HAbhAYKfsAC2EkTqve00ibWIZlaU74Z1EHwAjYr4PXF0YU2VEA1zSIKSSpKszRLRWwHzzRZXvK632u+uXzvsvw==", "signatures": [{"sig": "MEYCIQCu6KY8smR5L4QDCx9vPWjIgUafFB+awOTpVGxIYt61QgIhALVJpHPwK8J/SwGcCWDEm+lmFf3q/Fk8S5JuiYoYQLtp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52807}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "2.1.4": {"name": "@eslint/object-schema", "version": "2.1.4", "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "9e69f8bb4031e11df79e03db09f9dbbae1740843", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.4.tgz", "fileCount": 10, "integrity": "sha512-BsWiH1yFGjXXS2yvrf5LyuoSIIbPrGUWob917o+BTKuZ7qJdxX8aJLRxs1fS9n6r7vESrq1OUqb68dANcFXuQQ==", "signatures": [{"sig": "MEUCIEvN7PZafbjBuQghL/76RRRPeYJRdB6MAOlRRh6QPhg8AiEAgwf1Pz2DMSBIcyWcfAYXy7nk6Uy2HzkX9AFgsbuPEho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55487}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "2.1.5": {"name": "@eslint/object-schema", "version": "2.1.5", "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "rollup-plugin-copy": "^3.5.0"}, "directories": {"test": "tests"}, "dist": {"shasum": "8670a8f6258a2be5b2c620ff314a1d984c23eb2e", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.5.tgz", "fileCount": 10, "integrity": "sha512-o0bhxnL89h5Bae5T318nFoFzGy+YE5i/gGkoPAgkmTVdRKTiv3p8JHevPiPaMwoloKfEiiaHlawCqaZMqRm+XQ==", "signatures": [{"sig": "MEYCIQC5vaJ//+9g4KTFu4l0uhU86QSPOSWEo6aP7yZ7dybUwwIhAJ2sUapPc+0JhYl0eBboUkjlRqGdr8XcjhTxYHGYnC96", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fobject-schema@2.1.5", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 57040}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "2.1.6": {"name": "@eslint/object-schema", "version": "2.1.6", "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.4.5"}, "directories": {"test": "tests"}, "dist": {"integrity": "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==", "shasum": "58369ab5b5b3ca117880c0f6c0b0f32f6950f24f", "tarball": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz", "fileCount": 10, "unpackedSize": 56960, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fobject-schema@2.1.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDDMEh/N3HSCxrAbPFSoR+5b4AiJV0i+ekupaMB70CpMAIgHUnHk9TpbvgxmZMl9XVz8UxJTBtVNLKTewhnONDeCCk="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}}, "modified": "2025-01-31T17:22:47.181Z", "cachedAt": 1747660589978}