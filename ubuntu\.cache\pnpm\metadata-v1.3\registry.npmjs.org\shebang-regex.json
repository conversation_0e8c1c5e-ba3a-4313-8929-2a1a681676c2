{"name": "shebang-regex", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "shebang-regex", "version": "1.0.0", "devDependencies": {"ava": "0.0.4"}, "dist": {"shasum": "da42f49740c0b42db2ca9728571cb190c98efea3", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCE7dg6EIpMeBB4o/55QVmSiKAhCKAtxA2qZC9HbcmB0QIgaLs6xTt8hgFLOHUzyMrm2czRufJlc6l/e2XYPKZReZI="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "shebang-regex", "version": "2.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "f500bf6851b61356236167de2cc319b0fd7f0681", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-2.0.0.tgz", "integrity": "sha512-qZ2//Zb9flLDxFym9xqhDyquM5WRdmuk/vviks/2cr9c5Vm+mX578TP8DwUc9fbfX58Tgeef2og1elYnkHli2w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEnxo7yRPdiXwa3IuQ0X+yax1Uey9WxeHKsMub5VlfzGAiEAwgkhhtuS9m4jH4Bd/gZm719nkTpjZ2oM5FDgatBouBw="}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "shebang-regex", "version": "3.0.0", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "shasum": "ae16f1644d873ecad843b0307b143362d4c42172", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "fileCount": 5, "unpackedSize": 2828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcxDL7CRA9TVsSAnZWagAAWYAP/R5EbXIFL5aDT7Yavy5P\nrcZIWN4fQwwlFp/YdSocsX/lBlwgJ0axGZsoVxZ78y5TjJZbvJ+6YwYkUGuf\n4bCkNLWdnmI9xNdShULSc1gExlnH/+3ubiKoosm9hJ2z+5CFNB1OWmuxSIxw\nIo/6aSYhhzFmWDrwKkwB4fbb2SMMuyGnHCAq2TolakuSk75OJlHF7xRZWZ5k\nko/2f0CiSaP1OllRMBYWssHCAaEjdXqjOy1fCvoHgTw+aPYWZWnZSlKyQkzu\n20EAgEVhYQkiyZxNNCj40I6VTgr2mZnxTftZx49HwqfVs5qoU8DmCit+pMJd\nY+chiFfltBymUrF54j986kRUc/MkXNsWsRPFzzVg/9mOJAO+n9dW6rMcAM4f\nLbB7ZT5ah8PbgcXlUVCM4nEexLSFKFUhB9GATw/SSEgG/rTdd8sskIPb7lGj\nwMdqDvYh+oTc2r3r8Dl4lQRt/hrmHX3jk5Mf4de+IAPld5+9YDkqCcsev2w8\n44LoqmclULeuxcJPo2IA6K/gbKBmOjWjIuyBdV+8OX4xZFM4Eu9n1NTT7olT\nbGSIkVxASNiIqiatCMl5qqKnClqzY4F+bDVNe+KK6aaPvvV4ShktD4I682pH\nzULKVxadENPKGKhX/Y2xu7va9+GqbHTVwODuFH77n0AXf+BCirHFiKbGSqZg\nGT1V\r\n=FKXn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD9D5iHzHiq8RRcvBjhnVc1TDXKPStoeEAQrqtkEg9qXAIgPFNBElrbSUhCoAjEd/BRJo48gQudG1I1KkUMNo6x+/E="}]}, "engines": {"node": ">=8"}}, "4.0.0": {"name": "shebang-regex", "version": "4.0.0", "devDependencies": {"ava": "^3.15.0", "tsd": "^0.17.0", "xo": "^0.44.0"}, "dist": {"integrity": "sha512-YSKeSljCliLkWidW84GWL1HCguI0iEqhnBOLhrVXw/fN9he9ngekCy8zqJ1jXTPYmJ3Xkf3gLuNDVHQWdRqinw==", "shasum": "86b8202f10d28f4da056d4b905043128b3a6a0a7", "tarball": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-4.0.0.tgz", "fileCount": 5, "unpackedSize": 3234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFvu9CRA9TVsSAnZWagAAyoEP/i+QYJYuSbsrBBAI6yzH\nT3YB0hRhb9HMouskHtGW8LspxwPfxJ5Addd88MU74IfBbnoqJHd6fknQ+Fcb\nP7750BXpLgHuWn4+QtShCAis0tcEzWv6zmI+MINm79lfyRKNkTyXfs1lYccA\nWhRWAa+UmnDHJvrzbFAiS+EIH1FpXgKudkshKEOq/O4Ndt5P+0HGe999Ug76\nKRf9Qe+yLTM5cgsfDMo8a5+EEOlJpRRzt8VdaCy59iL6vJGi07OC6QA12KNx\nMCbftEYkKElpiPTK7jq6NOu7dJmSUc4alHuJRrdb1wfKzMGm8oiLB+DV1006\nmQxtyLgYDA8HrPhFNdPy8b48kNGcO6UWQwsJSjlUnNLwOmMZucA0H0bkktQf\nKu/0ZvZpjIxhXC37xzKoBAYn8scOUhfSHWskCA+jTWYhg7QZ5CkFxPzB2D4S\nvliFgB60l41lsXx2kq7SAd1RcfyH3BU2yoRIxuGuS4nbqEXZaiML1fk1CyZB\nudvdI2nAFcYRccKxngL210awtMN7uctROx+r/rJI77Tq1vj3lrCnQHoMl7S2\nEwygufHlUE3o1FqsFFmMyj8OU/l/5obn1UNZLTaEpCiBfsyZVCJ/vJjdizCt\nHjYOA/AhqH0ObdUa6gH3nH1tbMPnHhtrCKvesKqBDQGK1145plOhj4u3vGli\nUIOw\r\n=8x2+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCS1APGcb5vmKETU2vN9GlwktxBS9hlbbfoKzD8tphGqAIhAO52DW2DVUwAFjeJUs5AgUiXT0+bGMhEjYK41wZbhcxF"}]}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2023-06-16T22:40:59.596Z", "cachedAt": 1747660591310}