{"name": "tiny-invariant", "dist-tags": {"latest": "1.3.3", "beta": "1.3.1-beta.1"}, "versions": {"0.0.1": {"name": "tiny-invariant", "version": "0.0.1", "devDependencies": {"babel-cli": "^6.26.0", "babel-preset-flow": "^6.23.0", "flow-bin": "^0.70.0", "jest": "^22.4.3", "prettier": "^1.12.1"}, "dist": {"integrity": "sha512-jWlWSDB0GwJ7SVzmSrg2DoAjqI9oW8m52bEo/0cXCQ/vyasoJ/s65czwc/jeCul3pp8Abo6ssYXakp9Y7JIGSw==", "shasum": "5740ea9edd01c62112e8a086e6d3243a291bc9c7", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-0.0.1.tgz", "fileCount": 2, "unpackedSize": 1209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1XdFCRA9TVsSAnZWagAAInsP/0MBtOtaogCEKtdIAlA8\n8bN0G2BPjCCO1ZEDPS/TK9mT609u7TGJyhb4o9o3jhKm6NNiYVVqiepAhqFp\n6g/DswDbkLeKtYAKGwmGKhGUbvGLKhd41KYbXYdaY/V4EUkStOjM2JGsVYlG\nFb9Qw8wruNxUunqITg8xlfTbKA7PfpICPWzQuGUOmSapENB4AQz/IxlStzuo\nPe0qveA9/yq0QjC8Q30v6k5c4Dx2WqcXqlgENSPz+lCsrHSod0xPMOu+rQrV\ndYQG4mGMHeey4D/+HQCngBa/bwbrFX7eXfp4rSaeM5eN7o8wIk0JJ4A3sV9y\n9JCCqLlwgFllY8Wlsp39R+eF+NbJNpMZTZeyiH4dnOoPME8FsKFSPeTnBOfA\nBgKaPUrV3XVyPXHFCCHDSddYMKaQr13JBPOoRgaZT6TT7pZDx1kbT4DYHMq4\nbvG4CB0L47+DZ/TlNTYzS5X9/b8BUA49Efpku87ZUwCU8J/4kZ8/B7UbKu6L\nE/9iqDeVh2lNfcRYtRbTWebywn3AUgAjCG+zQG5ah1RGQrtI4y1JPqQ302/j\nYWMcnvazygpALQwpBAPxhYhXBehNj0N0JoRhYDTZai0M0zULPRFQA9ZJcB0X\n3ELau2ha/I2b8MfDiTTSTHiC+LXL8w3eW1pYgltz7IwlcG5ZzFuib7IN96TX\nV16X\r\n=nzzL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCV5NHoVPHIE+L1TCrFPzpux5HRcJgP8bjpc4aIPPcHWQIhAKL13wssTMOGnvTPaZDCRQF+59bQQRpTyx2a3D6xrz4i"}]}}, "0.0.2": {"name": "tiny-invariant", "version": "0.0.2", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "flow-bin": "^0.70.0", "jest": "^22.4.3", "prettier": "^1.12.1", "rimraf": "^2.6.2", "rollup": "^0.58.0", "rollup-plugin-babel": "^3.0.3"}, "dist": {"integrity": "sha512-W4EV+Hy0uiB1m18IR7mC9Cv6qbmXJNDCfPUPg3xJyHlu6EMKswQ9oDEIhChGxet2wwGf578w4hbz6bkpzHSw1g==", "shasum": "5f3ce2f0738ca2c4ecf61944acaccd9df70def11", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-0.0.2.tgz", "fileCount": 5, "unpackedSize": 2420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1Yf9CRA9TVsSAnZWagAADGYP/RRa7m3UsBpIaCdjaJZ+\nwhucfCH8/ZfjsEEMqClI1ka1+iKvWetDOe8SAjW36JhzqrQEl3nMlhYo9U2Z\ndz/S0bsjvgJGI5X86aUif8yZy4Vs4dPHSU6PvYXxmX40dPUKV3cz8AHYssWQ\n8SEnsA9+Xyxf4KR9XFXA4ytc86OPPEf9HgsFcKrIborTpijJHImKxJFX161y\ncO2GlojGdUR5yUoFLzaB4fTn6BIufJpwvAt0aB9PXk2zgqUe80zfyAI4e16Q\nguZHs4pKVsXP/HiuYqWxBx9NKp1yNWlf2Ymu9565FRIR3Iz7B2y9C11jxVjh\nrAo1aLY6N7J8NQthaWzrgeL6+70RRsvA/uNaPVLPNMnqdVCavsqdRprUIwG4\n0uqPKK5tZCuLr3TreBZQYsf1g4e3lBdwDRoADXf88lqAuJidEvMIMfpXlAVi\nmmjU1TxBYcZdx0LLmjZyHmYZUzRIJL7IzEMMeB1FOsYM02NxC2XuV+U862UR\nIpRKlpILI0iu+SBYF9vMiPc8ePXRmW3HbhdZ4YT1LP26yqjFbo5dyzPu3TqF\nrXi4KH1xtae51BsbAuV2y8tNg51z/k8LP3ltQf1u7UdbvMoH6/FigXr1ykoO\nRsFx0o6WAtDfBQ1r7soXYs21mAtPYI3dYNLQk3NgSJoBoI6lP9fWrC4XZaCU\nKEZh\r\n=0E/6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCf/ixSqVQWIJodD2UeIAcr1XYiu7iFE2aZahDFi8DiIwIhAO7AnKmhy34p8quJp7R+ZyABPs33YXK9CenRUP7Uj5JM"}]}}, "0.0.3": {"name": "tiny-invariant", "version": "0.0.3", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-flow": "^6.23.0", "flow-bin": "^0.70.0", "jest": "^22.4.3", "prettier": "^1.12.1", "rimraf": "^2.6.2", "rollup": "^0.58.0", "rollup-plugin-babel": "^3.0.3"}, "dist": {"integrity": "sha512-SA2YwvDrCITM9fTvHTHRpq9W6L2fBsClbqm3maT5PZux4Z73SPPDYwJMtnoWh6WMgmCkJij/LaOlWiqJqFMK8g==", "shasum": "4c7283c950e290889e9e94f64d3586ec9156cf44", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-0.0.3.tgz", "fileCount": 5, "unpackedSize": 2405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1YhWCRA9TVsSAnZWagAAYOsP/jIm1UN/LvqgSdPavqVs\nkvYpoJgbjyPlCaHScgoh05Tmwb/jro+LfWNfJg627H7dpwjuEtYb21qQCjPY\nXZbkgTV5C9nNs3TtLfBR6fgQyeS5km3yS5+OzALQoHn0nFX//1NEoxqTm/Cz\nPKccf8xwelYlXbAfkvwmcp+7R1mCKCotvEF2CbY2tMiyCIM2zJC3dy8/rdBd\nOoK4MTIml6mMHweYxigXNp4OKtFYhsnCXmKn3wb8t2ecmPHEaYP21SXvUazl\nIOaw+hMF7p4P52cQR4z7bQBGk66FhoptN7eQjGZpjt/m+t46XTTEw9wtS/Ji\niVNM4UW2VIc1/hH0T31jOzsCgxM0PzpnBaeLQyl7Qa8/HTSX6SthEjyaLTss\nlewg8A8leq6cOxIMYBxm1DFpnP8D0vtU49HzUddFLfjK29YWdpvPVcLnMgOf\nZJQSI4oDLPIhZ0DTjYFVD9c4Igfn1KhvcMbVSjOKWvLiJbemRTf6q+PqmUYO\nxVFFQiinD8jH7We5ghZ5Gxupx1IetO+ZKhEvWULcX6wYrrX7C5HnRHXrdEEz\njd5k9GvOKAEeeJdJSKWMc95zC7ZSieAyBUQIigzxDww+iOlGM8wqZWYfRMm3\nVnOv5P5VzED0BTjQk6whX848Gfpuqs0A1G1p2rtV2l6r2jnsC0H5eMDEmOfM\nY0rJ\r\n=FFRj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDI2ZXy93dlVGj6Rz1TsHAX7UQ5N2os7oQmWuZx4JWLyAiEAmRF8Uv4EUrZrWwUU7YJtWW10os8jDDU04Q4l2tqexNw="}]}}, "1.0.0": {"name": "tiny-invariant", "version": "1.0.0", "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.2", "babel-preset-env": "^1.7.0", "babel-preset-flow": "^6.23.0", "flow-bin": "^0.78.0", "jest": "^23.5.0", "prettier": "^1.14.2", "rimraf": "^2.6.2", "rollup": "^0.64.1", "rollup-plugin-babel": "^3.0.7", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^4.0.0"}, "dist": {"integrity": "sha512-bCLTQ14OaNO7tSXKy3mXMd/odOvisOAhF/TFzhbH4SdtHPMSS64KvBhWawAHwYTy7dtEKbXo9QGw8LBO/suOIw==", "shasum": "03f96fef4d3ba911f97ed467e1b88bca3d4d9d9f", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.0.tgz", "fileCount": 8, "unpackedSize": 6339, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcNmeCRA9TVsSAnZWagAALmsP/1VgDikU02BkdiWZGGE5\n0IcLJmFDWEqPUtJ4w9PmWq3yaDVc0cwlVie+1oEbxC7IG7IizPfUpFgz2rL+\nS/icJGAXwIPoPHF4MsO3wlbTHLhwHDjczBeYEhuozxPZcSgmYkgN9XtJ7ZWK\ntRwnl8DAcDf3uPyjgcerZg/pzk6d1a8qE5AhW0eQdJKxK/8C012MZwX7fjDD\n14xjJEy/YHNkrc8EmieDes+CDWWctLAZ+aunq/oE7LFjt6mT0kUd8mT8OB3j\nqDW6CY2aJX3lyjAO6vBdsDGtRMRg76BgQUwS8h+QppafBjswLK5E7MWy3I64\nxL38m3JbqFPYt0T799yq9JHdN/WUza2VC2so96aq4eL4sg7n7oYiJrzzcLPw\nILvEXBDOSHyjRY95r+GpQZsJBCNIUR8LNlv7l/mHFuVb2SBE0EqWX1JLPtzL\n3blWli8t4zxono7h7a1EEoYFL595Rkra61rMn4sYjlByw6h1D50haGQ+j5t9\nwO4DvUf+1IUmkXz+OyJnsdwwg9SzQrvFi4m57ZdS0GvhLVHrgmGxX/DFkddg\n2cTyFjighmHKGentDx2yX8FEiMd5dij83HgQuZkzAsUcctxTZKCRJh7tFiTV\noM9Dd1gqVlggcu0aI3RW4IRMmKYH2g2pBu0Xri/P/LZ9/O6U1pkvjw+KPVf8\nS0J3\r\n=iW//\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIASkk3Nc4WzDyfMBRuGSyUQe6iiKV30BXsuHFG02FAHEAiAm6J73xahqFnZMVL4cloXhoykOjAqHsz1R/cbUaovhvw=="}]}}, "1.0.1": {"name": "tiny-invariant", "version": "1.0.1", "devDependencies": {"@babel/core": "^7.1.2", "@babel/preset-env": "^7.1.0", "@babel/preset-flow": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.6.0", "flow-bin": "^0.82.0", "jest": "^23.6.0", "prettier": "^1.14.3", "rimraf": "^2.6.2", "rollup": "^0.66.2", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^6.0.0"}, "dist": {"integrity": "sha512-avcKCNM2yJqD33H/WJLgIu3QNRXFOjARGJSdLi8IpKESCyhm67vyPWTRjnIbX2uI/pn52tqYcao333R3PBVhbQ==", "shasum": "54700d039a0384ef2e83afd0e81af84c6d67b140", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.1.tgz", "fileCount": 8, "unpackedSize": 7131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbs1l/CRA9TVsSAnZWagAA804P/i6kxWb0VAvt1JB7Ux/a\ncyrezQ1utzWY1Wuz7TW72VOrKTs0YWEyvMDp8IPERi8/6yKOnN/SI1ELvW2c\nqGip5+ZqFJFvXFfqhtCIB4fpXv2ud/sGMJcHrCl2oZIk7OBeVlQ8uxDj1BNl\nE8Usi4fqc5F2b4pRs34sylPNNvBBiJ8Ir7dS3EBEcWaO3LfWrItscPc1zeey\n61QeRVdZZ2ipVG4cUG4Zuukbbonkm7/ybY8DY/h+wcCurs3HwgGC9eHOIRLZ\nsdK5p15hVsFnKzono+Jxq1EWI20WedIOUirhfqQ49E87nD0r1lnK1/9UTnp6\nC9G4K/Sj5SxqUUHv7SG1fO9DpnWSA0IdD2sOp6MtZ8bAB5MBi9voaCUft9kg\nba7wXrfzQ17bsoKAngERpYViy+bB3VTBZ4adrhgBiNuO0bcbNsnqj/UsZZYB\nu/UazNJJTOA0Xj4xl58/QZwBSkku0gN1A62KZrsgQCjRy1Lzf39QSrr+5AGH\nBSNA0v2hGIF16dMNFW/AhskD5AqSCIUIs0+UhSyYFX8gN5fAD7OlId3qsLuX\nFI6IkFiIHJf8jWue9ryevwURfTkVjf0P/sS2+5dtounipVR6HgW+HoScwVgl\nWXRv8Ua5p58r5cW18jzkHgtUdfvj4xwmEqchpnvJIPsHcAoOjkjosjb9fSWS\nBBrJ\r\n=0CUc\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCAB/NN55FGroukF1Rj3wmaxVHAKcOg3KtMDlfCrjZzOwIgZnLnHR5yf7G+xorYeyPyxfN6Zl/phHjKUalXVhAaF6w="}]}}, "1.0.2": {"name": "tiny-invariant", "version": "1.0.2", "devDependencies": {"@babel/core": "^7.1.2", "@babel/preset-env": "^7.1.0", "@babel/preset-flow": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.6.0", "flow-bin": "^0.82.0", "jest": "^23.6.0", "prettier": "^1.14.3", "rimraf": "^2.6.2", "rollup": "^0.66.2", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^6.0.0"}, "dist": {"integrity": "sha512-J5T/Z/4GhqAipQlAB5NYJSDNMm/68AxRJyDSAGIK3d1o788OPgAB/zKxHG3KSYws8ncxpQ7NuAnsOMQywdaJOQ==", "shasum": "8d343bf86acce627a2e0857d48a46a297fda2cc3", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.2.tgz", "fileCount": 8, "unpackedSize": 7139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb15RTCRA9TVsSAnZWagAAz3UP/A7OTNAGo3UWRnROqkE5\nNuGvOwdnnG2MWTnXiKNXHkpP+RY2U7ynxVCuh1eSTnTapBoB6g6Uz+A3Gno0\nORTnSHjiOJ2B1YZkXA8kb+c8IbUv/NLqSL0d4iH3YwIoJWsQKWNOkmFQbiyR\nuIFD+o4wgr8zAh2K/dCrkJq8KurtyijRlbg0auHKg3KDC7IcpORj263LGjro\nV9n7HiqR6t3X/YWbfMRadKwg2rcksfPff3kAp5uVhksnZxIbvSGFeqy1+otp\nhwzR5FirrIduR2agRjQ34WD+B+/fwAOCQLd1ZVxn77alVelGweyFPO5hnpu2\nAHWk41gGLX6oHNVUGQfS1r4QhRQI0omFXoLsAvaBvj/ZJz2SEIA/2trmosyA\nQlAIrhRWbjpqcsIvbSRXl8rg2grGnorrQY+h35RVVNlMqrk44laJsavSFATV\nLWRMsMZJiwyCOIXuDvmEB8BEdQY/1QzVWM/o2CNw7sFfWx6B1WMON3xdGxiV\nGoe7kLrgxDHHrl90cEsFHelrnVLpgtHQl9NcBIJOaBI0Hye08tpdyGfv9al+\n2inC7DrI2mVT/B4oW7PzoLisfsHs5jg65qwbL1nRuMSN6fM+bqtEvYPF9Qi3\nLItsKxfSozsZ8keBM79i+k3KTf5YGJdpYX2RKkJMI8pcFDtKW1liWi3nLXj0\npGFh\r\n=SBK7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC+DZqmKZ6J+ACr8uhS/GRlU+ayQpu73NI1GHsXu8HomAIgOB0y/MP6DCojacNqAsa8FYj+gEl0f4Sbr38Ymt2pY/0="}]}}, "1.0.3": {"name": "tiny-invariant", "version": "1.0.3", "devDependencies": {"@babel/core": "^7.1.5", "@babel/preset-env": "^7.1.5", "@babel/preset-flow": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "^23.6.0", "flow-bin": "^0.85.0", "jest": "^23.6.0", "prettier": "^1.15.0", "rimraf": "^2.6.2", "rollup": "^0.67.0", "rollup-plugin-babel": "^4.0.3", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-uglify": "^6.0.0"}, "dist": {"integrity": "sha512-ytQx8T4DL8PjlX53yYzcIC0WhIZbpR0p1qcYjw2pHu3w6UtgWwFJQ/02cnhOnBBhlFx/edUIfcagCaQSe3KMWg==", "shasum": "91efaaa0269ccb6271f0296aeedb05fc3e067b7a", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.3.tgz", "fileCount": 8, "unpackedSize": 7139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4khUCRA9TVsSAnZWagAA1WoP/iFkhSKGCtRDJhuMzzzd\n79UmxztQKtbdB0pEIGUV2ECHNeLm7ft+KWhk2kG+P1ndgV4NX+6XXIYAdloM\nYTnkcuYfGg3pPKa5Q26PBoaWER71yYXTB+zmnAqs11JK7+MwBy63+6RYJREm\nbgDjzJJhH150WtDXoyXtRjNbFTyiODpDRJhTBTi2MX5h7HEKgHnOXzh9zYhS\nQgYUF6BwmM2uQpzyGZxn9FrsUCfRjSIoJxsiuWk8iWQMtpwUYnLaTSk0KQmn\nKT/bMVOobEcCZn+zagiZV0tBP2F7fEXIWrXJ5FKWcqVwJtHtl5l0VH6tDLjZ\nl3Inf1d6A67OYqIy3TkeOeuuPo/aPymeFNGcoxX02yMcx/7qNU6hFUAEMxNR\n9fRgh+YZPKKWfFcoTIRs1FCW2NfM47uchFIDM199cbBJv5+J+P3EN+rsciPZ\nTdvbL3XkA8/GZzabPNilWHstnOVBe0qvODhXY+/kONxsWClHj+KBdb3105Db\nnwdcb7gPtVIpolRfn9pZU0cexMWDB2BDGwC/xQ+9Dk9cXjFuwJJug7zNC1+5\nfSDp9qodmE2flkGCMA1Tb4dJh8yT/pyl+yJSQh9xE3LX07Q/UNkW4kVzWScW\nKukXaCJ/JkfvF3nsreFYkxgg5GUYVysyvvvFQfeKRt/Zsqpz9xS68rGKi5BK\nJRkH\r\n=v248\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCuvd7l2MuyQH0RzemOqDD6XYDXz4TAGJbtE/E0Exd5RAIgfhUR8TS4lc0lr9l6rvkUCGDq6twL+R0UtOIsKX46+g0="}]}}, "1.0.4": {"name": "tiny-invariant", "version": "1.0.4", "devDependencies": {"@babel/core": "^7.4.0", "@babel/preset-env": "^7.4.2", "@babel/preset-flow": "^7.0.0", "@babel/runtime-corejs2": "^7.4.2", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.5.0", "flow-bin": "^0.95.1", "jest": "^24.5.0", "prettier": "^1.16.4", "rimraf": "^2.6.3", "rollup": "^1.7.3", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-replace": "^2.1.1", "rollup-plugin-uglify": "^6.0.2"}, "dist": {"integrity": "sha512-lMhRd/djQJ3MoaHEBrw8e2/uM4rs9YMNk0iOr8rHQ0QdbM7D4l0gFl3szKdeixrlyfm9Zqi4dxHCM2qVG8ND5g==", "shasum": "346b5415fd93cb696b0c4e8a96697ff590f92463", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.4.tgz", "fileCount": 10, "unpackedSize": 8496, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcmsQzCRA9TVsSAnZWagAAnP4P/RdJvoKJ373bQy3f9JGr\npLf0SkoE1rBk3qfgYWLEW/M6xPnAzkDQ0a/dwRU5MPB58u3kEqSN/XKSs1Pb\nYUXAtBWOEVHOjgYNLmvf5xrvZad97ywVW9JHlDG/65NqKMG7D3k2zx801MGh\nO6bAHPxXDA9Yw6XUbwnsIMJw/3QN82aBr32E2XNg9fXAR5vyeGEhtfdijE/w\nPrDTT1WRiiixmwzZCO19GEEN4FOufFnOA6Vcxfor1HE47cvDN/bNM5ci+olF\ni0NzT/r9KuVW2yo/IffXqhuxA+X3FR9M6fBmqxVAZcIohiLJ4FhTcif1QmZF\nN7nOkbyBAZsH5NMHq67cufE0Dp5fxYiZA0dDGCxpKhAb5RuPXZJvRUoiFVu+\nCfxtMf0G2U6oSx2pSyM9qKLaMmRE34TBJfMVOr1AEL/ghKcGPgOT6egT2eVa\nb7l6GMbTv76nrIApWYUbf3WHz0s0BN1AgxF2qdb3RR65vdWw/odJixPHyLpO\n/PniojKniIXxYPR5D9wqeFL3V6FpJ5iXg1tqVV6+Cx8zawtRo7kFA1fRorVU\no11gyiZR5v5jOsRhHcMe1JTetWjjlpwkUuJUP2uJMJkyb21flToE/KxrnIps\n+sVBpCByRSPfgwA2Je76WyLpbRLnmb3aywfxc67iA4FrpTdPyr+Xtum05JQq\nd7Fc\r\n=f45e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBsfGK+uGkFVVfXZGFjO45H7XbRAdBSMqL90yYufOdPQAiEA86+S07IsXrx8RhJx1UXhiJ+gJX2s9hO2it2INeOJjdk="}]}}, "1.0.5": {"name": "tiny-invariant", "version": "1.0.5", "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-flow": "^7.0.0", "@babel/runtime-corejs2": "^7.5.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.8.0", "flow-bin": "^0.102.0", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}, "dist": {"shasum": "0d198cf4f50ba34b28a16d8b28dcc4b02b813c44", "integrity": "sha512-B<PERSON>szNEQNwtyMS9OVJia2LK9N9b6VJ35kBrvhDDDpr4hreLYqhCie15dB35uZMdqv9ZTQ55GHQtkz2FnleTHIA==", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.5.tgz", "fileCount": 13, "unpackedSize": 8577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdI+wiCRA9TVsSAnZWagAAub0P/15QP0kVmIqqYoOpoXaM\nYhdetYEg4zboTOCZzimInJdOVFMY9zwVE8jMR2o/Ll6bFwbDCQiZ/ZqbDQbv\nl/JIxkubu9SMdxodQ0jS1SvTCFROP83Ckscs937D4OIgh9qz8fEdVDYmxKSK\nm0MdaO1T3ZsVOJP/uREiwmEIXny0bw0IzUZj7qB/wmUOYFMlLTs6Bj3lwHPg\nITsqyE9lvPIF2fPkNTrzVHApIh8lggPJgKA5ABGe3FzXyz+zn5El+WWdLiFC\nUiJqooIVmF9g9YNIvH5W75DGyM5NK0aiTVIasP6hNqEBXMSj/WNcTw/xgUUQ\nUSe3YzjNE/6c6w+w2Elrh0EtIXS1jLhhHgfyvzqP2bErjBzlxSMNZjEdpCe/\nImMfCxZjKGaBPlPsmtFMjH168CQeHFRlUvUEHbyp5aYTKvIPB6Sw7QGELR8r\nlGOKWzHOJMeQq/s53ens1NoJmn+NK4mgnbebZpC/MlM2eicmwq3DwCbERXPN\nPtN4A37zRoRt60GOm7t2l72QaQ+4niJwUoav/ZLovrb41/JQzt5uQt2pGSaa\ngafONEkNCkfn6/IHmIvZ+ZY+rQa0hiACs6SqvaCqoUS8LPUeJooIa7JgvQ4n\nv3vrbhZixTkCPsZ/kEI6KEoPfmvMXaQ/UdwknvJv6t3aLeTTX7qumSjtVJlj\nRuFH\r\n=uAhm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDE9BgDHD8THkrZpqNVOVQ1J1TO8sMl3EvaOXTge5FiTwIhAIa6DzRXXlr9UyUMpRtmv6aVhAX1PwiNIwWgIvFgY+4D"}]}}, "1.0.6": {"name": "tiny-invariant", "version": "1.0.6", "devDependencies": {"@babel/core": "^7.5.0", "@babel/preset-env": "^7.5.0", "@babel/preset-flow": "^7.0.0", "@babel/runtime-corejs2": "^7.5.1", "babel-core": "7.0.0-bridge.0", "babel-jest": "^24.8.0", "flow-bin": "^0.102.0", "jest": "^24.8.0", "prettier": "^1.18.2", "rimraf": "^2.6.3", "rollup": "^1.16.6", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.2"}, "dist": {"shasum": "b3f9b38835e36a41c843a3b0907a5a7b3755de73", "integrity": "sha512-FOyLWWVjG+aC0UqG76V53yAWdXfH8bO6FNmyZOuUrzDzK8DI3/JRY25UD7+g49JWM1LXwymsKERB+DzI0dTEQA==", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.6.tgz", "fileCount": 13, "unpackedSize": 8573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdK8XUCRA9TVsSAnZWagAA2ukP/3FvFrK12uYQ27ynA6o7\n3D13N/O4x/Z9rpUKncX7b/iNgxfn2IYF2IeHf9+hGo5JqtRkLN5R4dsmPLe5\nUf4RI07y+4UrxQtyx/bOUFJ2gxwZChqPN8WBPEr/GYYN9lYbiaEQKd2BWmTR\nT2/vn5rb9+CETvdt3Phf5pxmTSVbuehxEAqvxnPVQqhazVozyAR+uVcmgcvQ\nZ3iEpw9fphqdZxxqWmTQZjxldr3B5BOiBPWXcirqqylGyPHscb+lHx9w/aB/\nrz2m2hJwiyrI2bDF04pY9TbxvCB6co5iiwmGetrf5HNl//guzFQa4jTdUyp8\nYP24iouYuKE+8yIkPo7U5YyjAJuBbYzb32ndEIBDc33ktWDoErc0YKM205oK\nSfWJzSw84BPYuLe4EaD1P7Hr8Uz/TscmbZGlzeCa9Ngwz7wz0tBjvtCbNdWx\nkQofaUFf7uqaBocp1BFcfgS3XX9oahWh8CRNQdl9D0WcB7ynrY/e99H7l3Va\nUC8Xqm5Htvv2VajouXtRqGzVDbLDyh+5TulYnopSo53HUGHfAz7K7Ze0DZpA\nJo3krt+EtJX8AvM6+P2YYxxlf3vxXPnzYuVfi81rG9PWLTo10+AwMrlswSU0\nCfEiPWN/POyYgY3pKCgPjC3lQOoRA7BQe1LdinOp8ym6hUL3YbfrX/6iB+5z\nqvSd\r\n=R2jt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC0xVakjPUu6xyiZPy/aok+o2PrefOZ/q9IXG/hOph65QIhAP0WizOYRuLxxDjDiS/ftDU+Yq6OaNtBPMHsZqWyAYp/"}]}}, "1.1.0-beta.1": {"name": "tiny-invariant", "version": "1.1.0-beta.1", "devDependencies": {"@rollup/plugin-replace": "^2.3.0", "@rollup/plugin-typescript": "^3.0.0", "@types/jest": "^25.1.0", "jest": "^25.1.0", "prettier": "^1.19.1", "rimraf": "^3.0.1", "rollup": "^1.30.1", "rollup-plugin-terser": "^5.2.0", "ts-expect": "^1.1.0", "ts-jest": "^25.0.0", "tslib": "^1.10.0", "typescript": "^3.7.5"}, "dist": {"integrity": "sha512-84SAogGoOttvJCbQZCLdu0/sYiRwU8SEaciWYDlJebVhXKthEKW9KjfCy4BcVHCJIePlqae7A88TTPducOhivA==", "shasum": "200e9bf36ca15f1e1433d1de5484a82a1689cddc", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.1.0-beta.1.tgz", "fileCount": 10, "unpackedSize": 9608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMSBtCRA9TVsSAnZWagAAXasP/3J4BtzD6jNEbUN3recw\njjakPELda0e7XhNM2z275/fXqjGZxd8UNCoLVwE9fGf92+z/O495pDkiTgS+\neL5c2Nax6KH9yFj8WTvYu7WOlBghacbQFW5Ud4jCBYv6aeS2H92AWLYMiDnz\nDbMpy+8rKR26DGDioSUUWQZtm8ewegQEWmijLa2Rz3IumuQwjxW4cmZRabFP\nUkt3DukOhYramOl48qNy0YiycskylQZ9ckCwFvnx9ZUPD5UQcVqdp9EzqfhZ\n9aYYU4sJGZyYQ54/sktXm/J9hsAir3yUyiPk5a43OOjIx236MkrYOYvpuUH6\n+bMD5uDW1jzK/iXmXAZN10tHniy1Eu92BEl4DsNwUbaiLwonPAKCHkvIJBUH\nJYAE4Jpky9RdvRlVbDLkh4HcA3xgLJg4oaIBsMjfKANeuLPg6CO6it2goBsG\nWtv9d5sbghB0zXe4VI/505nmf2ZjbJCs66jaoEO08zDAZK6YtXt+uoExYeAV\nbs5IaQVgGdB09cY8rDRrsCay0mGb/eBQPhnAWju9zjon4PnyO9Ck0IVuWOQU\nwPcvrJBEyemqz4CooQ60srZc9VAiWYwlGf5iI/nwoq7qIcMn31clhQFiLrKj\nOkD3rrcBcLyUXKyRvJzrygsmZU+oeGTFIZ5Xn+ehe119BAGT+POzQimyaNVZ\ngouo\r\n=68fL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB+0I+V8OeJfeyNzcRGW6ZEwsJApAUiVcq8KWYGF61A0AiBVjyD1+UVc7buv7Kwt6wm/z3BP+azvWqJPb1O1KjZHDQ=="}]}}, "1.1.0-beta.2": {"name": "tiny-invariant", "version": "1.1.0-beta.2", "devDependencies": {"@rollup/plugin-replace": "^2.3.0", "@rollup/plugin-typescript": "^3.0.0", "@types/jest": "^25.1.0", "jest": "^25.1.0", "prettier": "^1.19.1", "rimraf": "^3.0.1", "rollup": "^1.30.1", "rollup-plugin-terser": "^5.2.0", "ts-expect": "^1.1.0", "ts-jest": "^25.0.0", "tslib": "^1.10.0", "typescript": "^3.7.5"}, "dist": {"integrity": "sha512-AXxedxQhQ1HjuUHWeezH0X6ktObPlS2W4RVDkkBF3+VDfucjPWKEQ8gpfl7Dpe3S66yAO9po9Gla2v+zBftrbA==", "shasum": "b0918bf6140657ec6a20c6f6baae9950d858ca7d", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.1.0-beta.2.tgz", "fileCount": 10, "unpackedSize": 9543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMky4CRA9TVsSAnZWagAA6GAP/A6PymOjwNCvQMEva61E\ntzhTv4ESZ+aVO0cm1ZK3/lgkRJWPgBtF2ds4TVLvYivVnWOwCaXvvtTbr/Xg\njuD4B5rgq9IdU4zRuLO3tF8G5L7N7ov9mtpQSHF7+xjZclWJOTWEIbY8dLPl\nadog8VrMEGqKB0g71gJIKQHEkTbZsSQHhkh+RHdy2jR5rkcxTZNxmaEhR9Xl\nwMoe6q3HQ119kUr59I6lT7JvsG3bf88T9zN1E79Hm7Zc53cyh6RCXmlW61MD\namuJZCimaQXq7zzele+dx99jSUYLP7hEbtGaeZ6PX2pFUCYC/oAOPMPgs0Lu\nFKjeIF7yql2kPlqUs0VFismRId95EU0teqv4+eauAk4Lq1oKG7q2jGBbp1u0\n6Fql4sDqc7BHmXxuzufBhngaalwkPWlMshhCI6gXBt0lqhZ3kVOcE5IRneJ0\nHa4g7Eacdj/a5eH4+7FAfvSXirazABnIIM3BZJdjFC0H8i1esmgXOVYiCZZX\nmEZ/UnPcpMA7h0V/tFgFQJ14B8prvI+Z6hMJQVLeGqHpHURX7kzMZznErqOz\nj9u7QFLQJByJ7X36YXVyv2g1MVNP+O8CjN97xUZWPi4Kv5jtPOKfnhLkwT9B\n5W/KNDpZWP2quXETN6bMP0hmNUkR0ujBBocBYZtxs8Ka8uEnC3y/PxuxPAzu\n8gWK\r\n=i2Hg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAXTSXXKd/JDBEDvHVnlHc1QLcb9kIsFnmXo1BN7b64KAiEAidnGuigtWMDBI5LCOPmXF5se4SQz4/nszGEoBUKKWU4="}]}}, "1.1.0": {"name": "tiny-invariant", "version": "1.1.0", "devDependencies": {"@rollup/plugin-replace": "^2.3.0", "@rollup/plugin-typescript": "^3.0.0", "@types/jest": "^25.1.0", "jest": "^25.1.0", "prettier": "^1.19.1", "rimraf": "^3.0.1", "rollup": "^1.30.1", "rollup-plugin-terser": "^5.2.0", "ts-expect": "^1.1.0", "ts-jest": "^25.0.0", "tslib": "^1.10.0", "typescript": "^3.7.5"}, "dist": {"integrity": "sha512-ytxQvrb1cPc9WBEI/HSeYYoGD0kWnGEOR8RY6KomWLBVhqz0RgTwVO9dLrGz7dC+nN9llyI7OKAgRq8Vq4ZBSw==", "shasum": "634c5f8efdc27714b7f386c35e6760991d230875", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.1.0.tgz", "fileCount": 10, "unpackedSize": 9536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMlDiCRA9TVsSAnZWagAA+mAP/2QC7vVeoZAhp9E12E7K\nTYjoxtidObICJm6JvMJyhixwzJhYaWQu2i6lgnnz34YibY1U3muBjcmxcSov\nwFRMQpNfhWanYvvbmPyatledMdOl4wkY2D/GIe+mt9nLqeqJUwr8PHn4H6FC\nLzXuZhq6m6wQ7Ff2H48f6usniN+ZByMQOe+72B/jWjYJFO+1wdcmQU1CD8xK\nkHQZC6c8x72yi7lYXS2WoJR2/y9skzGS3z7uU7f0YTB6Kf5C0nYKncVwdCrb\nAzGP82w+yp7GmwynagaeX5GLTLqt6LGcHx8vsioht1kjZ8oyl5DAYWPCzC2h\njZ4JPxMoNP6KH5dv9VQy+7EZEX1B1yk+TT7+QXMsi99NJYO3hJbe1H7g/v1K\nCRl51BCIOaX3d9r6FHrRG5hyzI5d9AQxIkQSoonrtxQpX0xudOEbOnOr1BI5\nsIy9yXtBgP7F6Jjl7vtZTgBzzyQ3tlLfVX2Abiyrs6PQlztDJ+zV3xQHXLe7\niVqNcwxxLmU8Jg5pH/NY3jO/DD+1gFvrJENrgMy+Yk2ALXEfMozAvJbW19hc\nHCeTXkMx1W9E5SpJ0aWSdoHa1MBYb1tRQv7A8gAuhcLxNALlYtTMW1lmBmRA\nsFRrflutF4SR4q59sF7ASh41PMr3s3AIejUZUItJ34T4xzt+0J0khUL7e3iv\n9elJ\r\n=flvj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF9Y4K68W/JPwx7SpxGAFllMWzPtuodsKpiuH0uOiSuDAiEA0foZbCEe8n7G8Zv2d/MR/OU91EuPzuzHHeewimnllQ0="}]}}, "1.2.0-beta.1": {"name": "tiny-invariant", "version": "1.2.0-beta.1", "devDependencies": {"@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.3.0", "@size-limit/preset-small-lib": "^6.0.3", "@types/jest": "^27.0.2", "@types/rollup": "^0.54.0", "expect-type": "^0.13.0", "jest": "^27.3.1", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.58.3", "rollup-plugin-terser": "^7.0.2", "size-limit": "^6.0.3", "ts-jest": "^27.0.7", "tslib": "^2.3.1", "typescript": "^4.4.4"}, "dist": {"integrity": "sha512-2tn9fAGY1kIj6ZIfHSzaU0tE8qDZ99SBBjdjPQig+U0mWNz5rCS0Z5r7htWeMuoasBTYymbPRUHdUTntZzAQhw==", "shasum": "bab79f8c36614e2af7e8843b6aa142b93e0e6091", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.2.0-beta.1.tgz", "fileCount": 10, "unpackedSize": 11327, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDo3AdAlwpthGMhO+hAf85o/z27Pu/3/F8/gSmsKnBJlgIgNvhUFoCv/ya6wRCQxAu2UOKDeuZwK6+OXWVidydrXMI="}]}}, "1.2.0": {"name": "tiny-invariant", "version": "1.2.0", "devDependencies": {"@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.3.0", "@size-limit/preset-small-lib": "^6.0.3", "@types/jest": "^27.0.2", "@types/rollup": "^0.54.0", "expect-type": "^0.13.0", "jest": "^27.3.1", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.58.3", "rollup-plugin-terser": "^7.0.2", "size-limit": "^6.0.3", "ts-jest": "^27.0.7", "tslib": "^2.3.1", "typescript": "^4.4.4"}, "dist": {"integrity": "sha512-1Uhn/aqw5C6RI4KejVeTg6mIS7IqxnLJ8Mv2tV5rTc0qWobay7pDUz6Wi392Cnc8ak1H0F2cjoRzb2/AW4+Fvg==", "shasum": "a1141f86b672a9148c72e978a19a73b9b94a15a9", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.2.0.tgz", "fileCount": 10, "unpackedSize": 11213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh296uCRA9TVsSAnZWagAAcqYQAICKk9UiwlW0ooqtVnQa\njI6A7JGO42VvOoEfQuZYQJJNLEV+BOvD4c7OLv744rtsCGlK5jspYzQVtmYX\nZIpsw2bwJh6gtNRUd2/qYZpf0r0IcyTnDKIyDWGt15XMEGivlqor5FqM+LoE\nZz6Y6Q6BRX3DZe3lAnOl9P3KiRh3o7IsZesQJaqfUrjeGCdebWFy+T61YWA0\nTSbIraPm6bkRoZ9gQFHuYGRJPKmFV/syrCGOkNV8Qm9TUnoY3L6LTqwM/5Lf\nfaq5/f7cDuTs3spgWOzlO0DHYUfxyp3KGl6l6XbDl+JdsXfs9ad2XBKmWbky\np9MAm+gPhoMj4/9moma5/6nNY4iUT3+vyArJocJzwVyG+Rk9qoZxY9MT6qPS\nZLkT0qCOAsiCq4aUbDP6ys8wYID19pw0cWX5n5mf15A5mpegJJw6qiT7Hvc4\n+uDnbC9g5bao/1SL+GyT+O27hMoj8im6kp9u4Z0iA+dmWfCFphaDpx0gNOlR\nBx12PS29bfcP0dTWrY0AMOtJcFNUzpKyNUQHCf4Q1KLiJrPEO/Bl97SbbIgY\nDTN5beFgTyNeyi4jvXcDS6QZEsKHjTdGU1m17gsC2M6EFJh6Vjt2uYyExjqN\ntqJe907b6qEJFFcXGC7rM8Z9v/D2X989F+KmTGOe5LdYlHYjs0pr4aQwkW7E\n5E5p\r\n=Anc9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICk6zRXBLei+pq9ZMp8qxiQQdlCWnekFn6+/rDVKGSNNAiBl4np6Z3B4ExwGRa03Rwbp8UkuTj1uqXnHOsUHnpJf6A=="}]}}, "1.3.0-beta.1": {"name": "tiny-invariant", "version": "1.3.0-beta.1", "devDependencies": {"@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.5.0", "@size-limit/preset-small-lib": "^8.1.0", "@types/jest": "^29.0.3", "@types/rollup": "^0.54.0", "expect-type": "^0.14.2", "jest": "^29.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "size-limit": "^8.1.0", "ts-jest": "^29.0.2", "tslib": "^2.4.0", "typescript": "^4.8.4"}, "dist": {"integrity": "sha512-bIHiVXhYokgI5AL2+1COsCZEwT/sumWCyKk9yuR8ksQmpCyUA0Uf6686kX8w4PdFbWWrZc1LC3e6uuz80B1jag==", "shasum": "1cb7b7eb0340b5b7522b0a596f33572ebcdb7b65", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.0-beta.1.tgz", "fileCount": 13, "unpackedSize": 12240, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDLLYDmUWIwRQWJTAlXzGYNwKIooqMwyr6+Tc5Mx2e9rQIhAJoYQatUSukyOjoqxieY1iMZuuKe1uxRcWFmtw5QMtzE"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM7XqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8QA/+O5jzGPuOCJvz36JhflUMcVxCzbgQKJ5lIa3vXi7C8xUFQv/D\r\nbR3jJCZjj6GP2sTq7Vx/umneAc4qMpRCaiS/ymX1B+oLMvqiCjc4VdWAV6FI\r\nqYMcdCWvmsnfhiizW+J7X7fCKhhoseTijNQ7guOrCBDUOiLtwoHI0lbPCJli\r\niEfogMfXro2dPalV3rf1j8MOVeDt429DBPeG7QYJxSL5PRcFqKO8jrLQM2I6\r\nPfCwGlAJCLRcjPQc0pEDsk5TzBk8RsSVps1C3Cy0pn2kZoQjQbWzdGv9JWCi\r\nIeOhX38XOtCB3eLlXRsaGIzMkA6YS2mzyMBQUcPJeJ7Cq0dEcXoiMBbAdqSE\r\nc642n+5WxHja8SrlkqnRGCcopkfJwuCctlfSYU5Vy8GrE7aCBFBgmHJSh715\r\nd/UCExQfvxIsV7g4/4Hb8ZYK7M27WoJe9yKz9wKFypX5p+ItUnYpivSM1E6g\r\nn4hx5Ntc7kKGn8bOyefePF0IVshYFOFPGFWASmr0dehfTYn1g/RUccTAuRn0\r\n+KW7WAAq8QQrptonOxqrj91gygFIEHcKhc1+8hurnlIl/qKm9gwHt6xpFY4z\r\n2IAFrUUMaVdfXtcCOQII+FCnejzrulnoVCmHxJ/bNs2Y1IEwdb3K9++Dy8v2\r\nLjJaTE4nhJO84dof8AyYyB1dmrh0mqlRzOU=\r\n=RDL4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "tiny-invariant", "version": "1.3.0", "devDependencies": {"@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.5.0", "@size-limit/preset-small-lib": "^8.1.0", "@types/jest": "^29.0.3", "@types/rollup": "^0.54.0", "expect-type": "^0.14.2", "jest": "^29.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "size-limit": "^8.1.0", "ts-jest": "^29.0.2", "tslib": "^2.4.0", "typescript": "^4.8.4"}, "dist": {"integrity": "sha512-<PERSON><PERSON><PERSON>s33oJRM5HsIwSsRxu7eXImrgcQiqFXRaU1WzY0bNxWoJjK4AMPnJWVvPbDHIGXMcxBZ5YdrGD8zXqacp75Q==", "shasum": "f967859a92cc0048fe55c43c3f8ad23c72a6d022", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.0.tgz", "fileCount": 13, "unpackedSize": 12233, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCX2NzZJu2bWi+d83SzZU9t1wxOuSguImIdUacuEWIpcwIgYOuZXHkEPgnA/Sx7MAHMrfohY15MhLHNwQxqyb3RWBA="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM7ZVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosPg/8CrQzfQtgQteBugbRy6ZLNDGZXR0QwST1X34ThVFxU6bp3PCh\r\nfVgqkpbgGNQvezhU9E7nTPybb9vzQTM1RG3Yf7EiGfYSJ5tSCGh7P/RmTQQ7\r\n46ymESvjyj3frLmvBB5Bj1TuXSm8llMnGLDGwoHTYq0Z9cIABvigpEiWT92V\r\nGydiBMYpXlFGqNPBhGJubnTNCWnvuDjfyEnO+7zcWNfuM+SxB0DjURe/kWq+\r\nawlcYO0qMLHAg8rwenYB+ngl+tF+XBrQgjGpJF0IP2atSjRD19+oevOiyeil\r\nkrFylETf/2uvQVwNq9gV9+CHtjLt3s1bhvAJdKd/mH2aReLuVHpID+x/w/Pi\r\ndFoNQH8VsDpCUvDE8g903xV4d4Bsq/o/Tej06oN3ConMyX5oKT1BS0ijB2kZ\r\nNxP7kSWM/1UmglSPLXi4Dq1X/gE6cfIJUbnL6GJ5ZT+5XEVOa6rCNnP35AFr\r\nIukTdvmEjMWu/oIUsbDYg37tC2XL8SNLvG74EPkVXobCutl8jQITpyMG37GZ\r\nubjx3p78ocFQJahVbPyLEXoiUtla7qv5+uUwrJXNJuI5nRn3RmVCtqyynaRm\r\nqgUaCa5zocEowtGBVo5Fyp3rVAVXCROOSWU1WJdDeyhfx1rlFYXNtNDJNOQC\r\n4TRysX1EfI209VgqpXzCsSJhtNsnouc6Iok=\r\n=bZPg\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1-beta.1": {"name": "tiny-invariant", "version": "1.3.1-beta.1", "devDependencies": {"@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.5.0", "@size-limit/preset-small-lib": "^8.1.0", "@types/jest": "^29.0.3", "@types/rollup": "^0.54.0", "expect-type": "^0.14.2", "jest": "^29.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "size-limit": "^8.1.0", "ts-jest": "^29.0.2", "tslib": "^2.4.0", "typescript": "^4.8.4"}, "dist": {"integrity": "sha512-/lyY5ClLUQPJzBgLmeR3+zdDIG3v0wx5504wWL6OJMw33ckUUbASPxwTA7iI/0Bz4NnojnrV6P8O/VTTrC5zRA==", "shasum": "08525d852db475956838d61f93aa4eb3aa80002c", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.1-beta.1.tgz", "fileCount": 13, "unpackedSize": 12193, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD6Hfi6JTBv3qSKxYZxtREeYcBwxRe6DYmjuoF0lCkZ2AIgbpCASskAnKainbp3Pp6scDq4Yux/SLyEbE+W4lVFEAk="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNCGWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsGBAAnBh6kaAwzB23IGcebD11twHHIHrlRlmb7kKaJjrR8QzjOAU2\r\nDVFhLxh4ukPiCqfubXPwOnUFLeFLa3zCl4mzEqZRFDhT/kB3o2VV2ReLSBC3\r\nIrAsxXtGqHMOJ/eVTcc+oPPk1ovAzoaSZrG/u5AP1mqI8idQENgAb/juQAfr\r\nfCR8G2bvHLttzlX4UEfIJSpB+ow1McIp76NOMOA3vdqsb6YghWpcAMhdJNAT\r\nwsv9REgwy5RWmiu7C35DAZN3s97BQffHXq3EURMpmiFoe3WWE8Wr/EVYtbGU\r\nHvPHDhrGxZ1aQNv+6g0R99FCsuP6I0/Eend+XaP01ZuNpdAMiPPsRmPnKlxB\r\nbr83ONTU9QoA+DuD9PVB+5nXpoQRdx9eqO9RZnlhmCEJRbZP1c6EZvbH8lZu\r\nAp5IGbkmo48wfB315TSNzgA33tLBgaaltN/IFJSB/1oSaZwE1mM3EeF7smBU\r\nKkUDsQKZ+IZW8vM7eT8bDujv10gZ5cTggduhIfgtZZ2O1UX1x5SDbJOPrDab\r\nqpuv+h6QZ6M5HHVIO6aoZnV7j6jshGhiZYW1VglVckXTTm/MHtdlUlyfbhm0\r\nFUE9RUApMaAGwcHipJ5pLIHGDHIG3dH6BQCcFKcpyeFF1YIAtVbZCBKy0azz\r\nrPjbnyAykxCfXDAJ0BAk+IMu11HnI/omhEE=\r\n=ktKv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "tiny-invariant", "version": "1.3.1", "devDependencies": {"@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^8.5.0", "@size-limit/preset-small-lib": "^8.1.0", "@types/jest": "^29.0.3", "@types/rollup": "^0.54.0", "expect-type": "^0.14.2", "jest": "^29.0.3", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-terser": "^7.0.2", "size-limit": "^8.1.0", "ts-jest": "^29.0.2", "tslib": "^2.4.0", "typescript": "^4.8.4"}, "dist": {"integrity": "sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==", "shasum": "8560808c916ef02ecfd55e66090df23a4b7aa642", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.1.tgz", "fileCount": 13, "unpackedSize": 12186, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIExEWIsz1kHkvI1jC6HQrjMy2Dme/x+NCiXWA9663MLqAiEAs/vCII2vsNkTjH5w+HzFNP0c8nu11Bt8BOvR4/Me4Pg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNCQUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMxg/+I50F1C/z38G1/bmUtRhTnmEmWav2+2wsnvDQITZlywbL0SjQ\r\ng7D1vyvu3BqujSspQs2IyvTE00vd/BPUPavv1JTX8gPuOd2+5Q0rxOdq3nnz\r\nAoel3iyXvAfEa41GX16AEt0M2Q/r5y8B9pKxdg8tWWT7ADyTN7WRZGdR9etz\r\nRHWmHAgjurDWAziRfNHy8z/Z7NftOiH8S0wsEQs4XAQVv8fZ0tyhvWxk7X0Y\r\n5RGf8u8mq9LV+Ln17/R9KQulIWx3bC+oQwYEmxeLC9OHH3n2otojqarbveDH\r\nwTr9AaLrIxu27G11gVfx+nOVRd0hLrUKgsbIzFxDPpdyCBmch68OQjpKRWl3\r\ng2QJhkDoyRiFMEr7WCwbTU4rDz4Fv7PpS8vbzu6EVwSFhxhFqv/AeLt5uFzZ\r\nx8Qsi71Y6qimCNQLYmNn0Nkhi0svabEUj/G4XhTvgSIHdatisK8B0xj+9LaY\r\nQRdaZROx275C1jF6Q1Ik6c+0k9+g6v1lgtEhoLGe9Va/lwb6GbZ3OXrePksi\r\nHVhz7N3dCxq7fL6Bo8p2RiR52mx7drE/VHiKXLHdMWfQsDc4lW4h8CACa5KA\r\nrlIP9eY8A/nhzPKr9AuR0Qtsq07MFHnmMasotBLrZOyywopLHkHYnWM2u32F\r\nMgSfASkEV4vV9nNMZGSSLfmLDNOnuv2sYb8=\r\n=z9kH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.2": {"name": "tiny-invariant", "version": "1.3.2", "devDependencies": {"@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-typescript": "^11.1.6", "@size-limit/preset-small-lib": "^11.0.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.20", "@types/rollup": "^0.54.0", "expect-type": "^0.17.3", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.12.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^11.0.2", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "typescript": "^5.3.3"}, "dist": {"integrity": "sha512-oLXoWt7bk7SI3REp16Hesm0kTBTErhk+FWTvuujYMlIbX42bb3yLN98T3OyzFNkZ3WAjVYDL4sWykCR6kD2mqQ==", "shasum": "1f62dadbb3b037a66ec938f0dd46bf8b3d43553c", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.2.tgz", "fileCount": 13, "unpackedSize": 14809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCXpMpqesGHDbqLVtksp6VhlpMt/OhN5jn1Gj3wwl77dAIhAOIdeCjWGrlSZRZH+0+4NVjA6nL1tDHEgK4bWa2g0zp2"}]}}, "1.3.3": {"name": "tiny-invariant", "version": "1.3.3", "devDependencies": {"@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-typescript": "^11.1.6", "@size-limit/preset-small-lib": "^11.0.2", "@types/jest": "^29.5.12", "@types/node": "^20.11.20", "@types/rollup": "^0.54.0", "expect-type": "^0.17.3", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.12.0", "rollup-plugin-terser": "^7.0.2", "size-limit": "^11.0.2", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "typescript": "^5.3.3"}, "dist": {"integrity": "sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==", "shasum": "46680b7a873a0d5d10005995eb90a70d74d60127", "tarball": "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.3.3.tgz", "fileCount": 13, "unpackedSize": 14809, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDBolJ57zgAEcX5ZpQIZ5XRmATtd4lxp6RJ45L8TR7PDgIhAIdW1AUO0FRFE7Pq8cEwMZOL81UIUfJMNpKE0O+ialwd"}]}}}, "modified": "2024-02-23T21:50:06.005Z", "cachedAt": 1747660589711}