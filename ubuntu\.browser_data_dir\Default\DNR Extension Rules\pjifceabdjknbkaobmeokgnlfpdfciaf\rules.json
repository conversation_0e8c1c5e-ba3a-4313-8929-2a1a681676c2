[{"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\/easylist\\/[0-9]{5}"}, "id": 1000000}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["1337x.to", "cryptobriefing.com", "eztv.io", "eztv.tf", "eztv.yt", "fmovies.taxi", "fmovies.world", "limetorrents.info", "megaup.net", "mrunlock.kim", "newser.com", "sendit.cloud", "tapelovesads.org", "torlock.com", "uiz.io", "userscloud.com", "vev.red", "vidbull.tv", "vidup.io", "yourbittorrent2.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*bit(ly)?\\.(com|ly)\\/"}, "id": 1000001}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[-a-z]{8,15}\\.(?:com|net)\\/400\\/\\d{7}$", "resourceTypes": ["script"]}, "id": 1000002}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[-a-z]{8,15}\\.(?:com|net)\\/401\\/\\d{7}$", "resourceTypes": ["script"]}, "id": 1000003}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["1337x.to", "cashurl.in", "clickndownload.click", "clicknupload.click", "cloudvideo.tv", "downloadpirate.com", "fmovies.taxi", "fmovies.world", "hurawatch.at", "igg-games.com", "indishare.org", "linksly.co", "megaup.net", "mixdrop.ag", "mp3-convert.org", "nutritioninsight.com", "ouo.press", "pcgamestorrents.com", "pcgamestorrents.org", "powvideo.net", "powvldeo.cc", "primewire.sc", "proxyer.org", "sendit.cloud", "sendspace.com", "shrinke.me", "shrinkhere.xyz", "solarmovie.to", "theproxy.ws", "uiz.io", "up-load.io", "upload.ac", "uploadever.com", "uploadrar.com", "uploadrive.com", "uplovd.com", "upstream.to", "userscloud.com", "vidbull.tv", "vidoza.co", "vidoza.net", "vidup.io", "vumoo.life", "xtits.com", "yourbittorrent2.com", "ziperto.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\/.*(sw[0-9a-z._-]{1,6}|\\.notify\\.).*", "resourceTypes": ["script"]}, "id": 1000005}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[a-z]{8,15}\\.[a-z]{3}\\/5\\/\\d{7}\\/\\?", "resourceTypes": ["xmlhttprequest"]}, "id": 1000007}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[-a-z]{8,15}\\.(?:com|net)\\/500\\/\\d{7}\\?", "resourceTypes": ["xmlhttprequest"]}, "id": 1000008}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["555.porn", "abxxx.com", "aniporn.com", "asiantv.fun", "bdsmx.tube", "bigdick.tube", "blackporn.tube", "gaytxxx.com", "hclips.com", "imzog.com", "in-porn.com", "inporn.com", "javdaddy.com", "mrgay.tube", "onlyporn.tube", "porn555.com", "pornclassic.tube", "pornforrelax.com", "porngo.tube", "pornhits.com", "pornj.com", "pornl.com", "pornq.com", "porntop.com", "pornzog.com", "privatehomeclips.com", "puporn.com", "see.xxx", "senzuri.tube", "sextu.com", "shemalez.com", "sss.xxx", "teenorgy.video", "tubepornclassic.com", "tuberel.com", "txxxporn.tube", "vxxx.com", "xjav.tube", "xmilf.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.[a-z]{3,5}\\/[0-9a-z]{8,12}\\/[0-9a-z]{8,12}\\.js$", "resourceTypes": ["script"]}, "id": 1000009}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "excludedRequestDomains": ["edu", "gov"], "regexFilter": "^https:\\/\\/[a-z]+\\.[-a-z0-9]+\\.[a-z]{3,7}\\/[a-z]{2,6}\\.(?:[a-z]{1,8}\\.)?17\\d{8}\\.js\\?(?:revision|sr?c?|ve?r?)=6[a-f0-9]{7}$", "resourceTypes": ["script"]}, "id": 1000010}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "excludedRequestDomains": ["edu", "gov"], "regexFilter": "^https:\\/\\/[a-z]+\\.[-a-z0-9]+\\.[a-z]{3,7}\\/17\\d{8}\\/em\\.js\\?(?:revision|sr?c?|ve?r?)=6[a-f0-9]{7}$", "resourceTypes": ["script"]}, "id": 1000011}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["cc", "co", "net", "vip"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/steamproxy\\.[a-z]+\\/"}, "id": 1000012}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["embedflix.online", "koyso.com", "methstreams.com", "nepu.to"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/z-[a-z0-9]{7,10}(\\.js)?$", "resourceTypes": ["script"]}, "id": 1000013}, {"action": {"type": "block"}, "condition": {"regexFilter": "^https:\\/\\/[a-z]{3}\\.[a-z]{3}\\.mybluehost\\.me\\/[a-z]{2,}\\/[0-9A-z]{3,}\\/contract\\/", "resourceTypes": ["main_frame", "sub_frame"]}, "id": 1000014}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["edu", "gov", "midjourney.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:ai|art|get)?-?midjourney(?:s|ai)[^\\/]+\\/", "resourceTypes": ["main_frame"]}, "id": 1000015}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["edu", "gov", "midjourney.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:ai|art|get)-?midjourney[^\\/]+\\/", "resourceTypes": ["main_frame"]}, "id": 1000016}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["edu", "gov", "midjourney.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/mid-journey\\.[a-z]+\\/", "resourceTypes": ["main_frame"]}, "id": 1000017}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["dienmayxanh.com"], "initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/dienmayxanh[-a-z0-9]+\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000019}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["cc"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/vietg[a-z]v[0-9]*\\.cc\\/", "resourceTypes": ["main_frame"]}, "id": 1000020}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/dich-vu(?:-[a-z0-9]+){2,3}\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000021}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["hdsaison.com.vn"], "initiatorDomains": ["cc", "com", "vip"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/hdsaison-?[a-z]{2,}\\.(?:cc|com|vip)\\/", "resourceTypes": ["main_frame"]}, "id": 1000022}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["edu", "gov", "gov.vn", "tpb.vn"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/tpbank\\.[-a-z0-9]{5,}\\.[a-z]{3}(?:\\.vn)?\\/", "resourceTypes": ["main_frame"]}, "id": 1000023}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["dhl.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/dhl\\.\\d+\\.potabox\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000024}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["shopee.vn"], "initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/sp\\d{2,3}88(?:vn)?\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000025}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["tiki.vn"], "initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/tdkd0[0-9]\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000026}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["garena.vn"], "initiatorDomains": ["vn"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:www\\.)?(?:ff[-.])?member[-.]gare[a-z]+a(?:\\.pro)?\\.vn\\/", "resourceTypes": ["main_frame"]}, "id": 1000027}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["garena.vn"], "initiatorDomains": ["vn"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:www\\.)?(?:ff[-.])?men?mber[-.]garena\\.vn\\/", "resourceTypes": ["main_frame"]}, "id": 1000028}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["usps.com"], "initiatorDomains": ["com", "top"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/usps\\.[-a-z]{6,}\\.(?:com|top)\\/(?:address\\.html|information|verify)\\b", "resourceTypes": ["main_frame"]}, "id": 1000029}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["usps.com"], "initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/ur[cex][a-z]{2,4}\\.com\\/(?:address\\.html|information|verify)\\b", "resourceTypes": ["main_frame"]}, "id": 1000030}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:[a-z0-9]{2,}-)+10\\d{4}\\.weeblysite\\.com\\/", "resourceTypes": ["main_frame"]}, "id": 1000031}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:[a-z0-9]{2,}-)+10\\d{4}\\.square\\.site\\/", "resourceTypes": ["main_frame"]}, "id": 1000032}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[^.]+\\.codeanyapp\\.com\\/wp-content\\/.+\\/spo[a-z]{2}i\\/", "resourceTypes": ["main_frame"]}, "id": 1000033}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[a-z]+bem\\.com\\/auth\\/login", "resourceTypes": ["main_frame"]}, "id": 1000034}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["tgx.rs", "torrentgalaxy.to"], "regexFilter": "\\/common\\/images\\/[a-z0-9]{1,5}\\-[a-z0-9]{1,5}\\.png$", "resourceTypes": ["image"]}, "id": 1000035}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["123movies.tw", "1cloudfile.com", "745mingiestblissfully.com", "9xupload.asia", "adblockeronstape.me", "adblockeronstreamtape.me", "adblockeronstrtape.xyz", "adblockplustape.xyz", "adblockstreamtape.art", "adblockstreamtape.fr", "adblockstreamtape.site", "adblocktape.online", "adblocktape.store", "adblocktape.wiki", "anonymz.com", "antiadtape.com", "audaciousdefaulthouse.com", "bowfile.com", "clicknupload.to", "cloudvideo.tv", "cr7sports.us", "d000d.com", "daddylivehd.sx", "dailyuploads.net", "databasegdriveplayer.xyz", "deltabit.co", "dlhd.sx", "dood.la", "dood.pm", "dood.re", "dood.sh", "dood.so", "dood.to", "dood.watch", "dood.wf", "dood.ws", "dood.yt", "doods.pro", "dooood.com", "dramacool.sr", "drivebuzz.icu", "ds2play.com", "embedplayer.site", "embedsb.com", "embedsito.com", "embedstream.me", "engvideo.net", "enjoy4k.xyz", "eplayvid.net", "evoload.io", "fembed-hd.com", "filemoon.sx", "files.im", "filmy4wap.ink", "flexy.stream", "fmovies.ps", "gamovideo.com", "gaybeeg.info", "gdriveplayer.pro", "gettapeads.com", "givemenbastreams.com", "gogoanimes.org", "gogohd.net", "goload.io", "gomo.to", "greaseball6eventual20.com", "hdtoday.ru", "hexupload.net", "hurawatch.at", "imgtraffic.com", "kesini.in", "kickassanime.mx", "kickasstorrents.to", "linkhub.icu", "lookmyimg.com", "mangareader.cc", "mangareader.to", "mangovideo.pw", "maxsport.one", "membed.net", "meomeo.pw", "mirrorace.org", "mixdroop.co", "mixdrop.ag", "mixdrop.bz", "mixdrop.click", "mixdrop.club", "mixdrop.nu", "mixdrop.si", "mixdrop.sx", "mixdrop.to", "mixdrops.xyz", "mixdrp.co", "movies2watch.tv", "mp4upload.com", "nelion.me", "noblocktape.com", "nsw2u.org", "onlinevideoconverter.com", "ovagames.com", "pahaplayers.click", "papahd.club", "pcgamestorrents.com", "pouvideo.cc", "proxyer.org", "putlocker-website.com", "reputationsheriffkennethsand.com", "rintor.space", "rojadirecta1.site", "scloud.online", "send.cm", "sflix.to", "shavetape.cash", "skidrowcodex.net", "smallencode.me", "soccerstreamslive.co", "sportshighlights.club", "stapadblockuser.art", "stapadblockuser.click", "stapadblockuser.info", "stapadblockuser.xyz", "stape.fun", "stapewithadblock.beauty", "stapewithadblock.monster", "stapewithadblock.xyz", "strcloud.in", "streamadblocker.cc", "streamadblocker.com", "streamadblocker.store", "streamadblocker.xyz", "streamingsite.net", "streamlare.com", "streamnoads.com", "streamta.pe", "streamta.site", "streamtape.com", "streamtape.to", "streamtape.xyz", "streamtapeadblock.art", "streamtapeadblockuser.art", "streamtapeadblockuser.homes", "streamtapeadblockuser.monster", "streamtapeadblockuser.xyz", "strikeout.ws", "strtape.cloud", "strtape.tech", "strtapeadblock.club", "strtapeadblocker.xyz", "strtapewithadblock.art", "strtapewithadblock.xyz", "supervideo.tv", "tapeadsenjoyer.com", "tapeantiads.com", "tapeblocker.com", "tapenoads.com", "tapewithadblock.com", "tapewithadblock.org", "thepiratebay0.org", "thepiratebay10.xyz", "theproxy.ws", "thevideome.com", "toxitabellaeatrebates306.com", "un-block-voe.net", "upbam.org", "upload-4ever.com", "upload.do", "uproxy.to", "upstream.to", "uqload.co", "uqload.io", "userscloud.com", "v-o-e-unblock.com", "vidbam.org", "vido.lol", "vidshar.org", "vidsrc.me", "vidsrc.stream", "vipleague.im", "vipleague.st", "voe-unblock.net", "voe.bar", "voe.sx", "vudeo.io", "vudeo.net", "vumoo.to", "yesmovies.mn", "youtube4kdownloader.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[0-9a-z]{5,}\\.com\\/.*", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000036}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["pancakeswap.finance"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/pancake(?:dro|swa)pclick\\d+\\.vercel\\.app\\/", "resourceTypes": ["main_frame"]}, "id": 1000037}, {"action": {"type": "block"}, "condition": {"excludedInitiatorDomains": ["dana.id"], "initiatorDomains": ["my.id", "web.id"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/dana-indone[a-z]+\\.[a-z]{5,}\\.(?:my|web)\\.id\\/", "resourceTypes": ["main_frame"]}, "id": 1000040}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["distrowatch.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/distrowatch\\.com\\/images\\/[a-z]+\\/[a-z]+$", "resourceTypes": ["image"]}, "id": 1000041}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/([a-z]+\\.)?sythe\\.org\\/[\\w\\W]{30,}", "resourceTypes": ["image"]}, "id": 1000052}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["ipatriot.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?ipatriot\\.com[\\/]{1,}.*[a-zA-Z0-9]{9,}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000053}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["letocard.fr"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?letocard\\.fr[\\/]{1,}.*[a-zA-Z0-9]{3,7}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000054}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["letocard.fr"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?letocard\\.fr\\/[a-zA-Z0-9]{3,7}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000055}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["lovezin.fr"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?lovezin\\.fr[\\/]{1,}.*[a-zA-Z0-9]{7,9}\\/[a-zA-Z0-9]{10,}\\/.*", "resourceTypes": ["image"]}, "id": 1000056}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["naturalblaze.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?naturalblaze\\.com\\/wp-content\\/uploads\\/.*[a-zA-Z0-9]{14,}\\.*", "resourceTypes": ["image"]}, "id": 1000057}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["newser.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?newser\\.com[\\/]{1,}.*[a-zA-Z0-9]{3,7}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000058}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["rightwingnews.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?rightwingnews\\.com[\\/]{1,9}.*[a-zA-Z0-9]{8,}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000059}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["topminceur.fr"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?topminceur\\.fr\\/[a-zA-Z0-9]{6,}\\/[a-zA-Z0-9]{3,}\\/.*", "resourceTypes": ["image"]}, "id": 1000060}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["vitamiiin.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?vitamiiin\\.com\\/[\\/][\\/a-zA-Z0-9]{3,}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000061}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["writerscafe.org"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(.+?\\.)?writerscafe\\.org[\\/]{1,}.*[a-zA-Z0-9]{3,7}\\/[a-zA-Z0-9]{6,}\\/.*", "resourceTypes": ["image"]}, "id": 1000062}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["allthingsvegas.com", "aupetitparieur.com", "beforeitsnews.com", "bigleaguepolitics.com", "canadafreepress.com", "concomber.com", "conservativefiringline.com", "dailylol.com", "ipatriot.com", "mamieastuce.com", "meilleurpronostic.fr", "miaminewtimes.com", "naturalblaze.com", "patriotnationpress.com", "populistpress.com", "thegatewaypundit.com", "thelibertydaily.com", "toptenz.net", "vitamiiin.com", "westword.com", "wltreport.com", "writerscafe.org"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\.(com|net|org|fr)\\/[A-Za-z0-9]{1,}\\/[A-Za-z0-9]{1,}\\/[A-Za-z0-9]{2,}\\/.*", "resourceTypes": ["image"]}, "id": 1000063}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "\\/t\\/[0-9]{3}\\/[0-9]{3}\\/a[0-9]{4,9}\\.js$", "resourceTypes": ["script"]}, "id": 1000064}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["1vag.com", "4tube.com", "adult-channels.com", "analdin.com", "biguz.net", "bogrodius.com", "chikiporn.com", "fantasti.cc", "fuqer.com", "fux.com", "hclips.com", "heavy-r.com", "hog.tv", "megapornx.com", "milfzr.com", "mypornhere.com", "porn555.com", "pornchimp.com", "pornerbros.com", "pornj.com", "pornl.com", "pornototale.com", "porntube.com", "sexu.com", "sss.xxx", "thisav.com", "titkino.net", "tubepornclassic.com", "tuberel.com", "tubev.sex", "txxx.com", "vidmo.org", "vpornvideos.com", "xozilla.com", "youporn.lc", "youpornhub.it", "yourdailypornstars.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\/.*sw[0-9._].*", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000065}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["eporner.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\.eporner\\.com\\/[0-9a-f]{10,}\\/$", "resourceTypes": ["script"]}, "id": 1000067}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["kleinanzeigen.de"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/fdts\\.ebay-kleinanzeigen\\.de\\/[a-z0-9]{13,18}\\.js\\?", "resourceTypes": ["script"]}, "id": 1000068}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["spectrum.net"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/pov\\.spectrum\\.net\\/[a-zA-Z0-9]{14,}\\.js", "resourceTypes": ["script"]}, "id": 1000069}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["tjx.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/tjmaxx\\.tjx\\.com\\/libraries\\/[a-z0-9]{20,}", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000070}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["mbna.ca", "td.com", "tdbank.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/tmx\\.(td|tdbank)\\.com\\/[a-z0-9]{14,18}\\.js.*", "resourceTypes": ["script"]}, "id": 1000071}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["kleinanzeigen.de"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/www\\.ebay-kleinanzeigen\\.de\\/[a-z0-9]{8}\\-[0-9a-f]{4}\\-", "resourceTypes": ["script"]}, "id": 1000072}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["kroger.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/www\\.kroger\\.com\\/content\\/{20,}", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000073}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/(?:ak\\.)?[a-z]{6,12}\\.(?:com|net)\\/4\\/\\d{7}", "resourceTypes": ["main_frame", "sub_frame"]}, "id": 1000074}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "_dating\\d\\/index\\.html\\?aref=", "resourceTypes": ["main_frame"]}, "id": 1000077}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["biz"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[a-z]{2,3}\\d\\.biz\\/go\\/[0-9a-z]{15,18}$", "resourceTypes": ["main_frame"]}, "id": 1000080}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["biz"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/serch\\d{2}\\.biz\\/\\?p=", "resourceTypes": ["main_frame"]}, "id": 1000081}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["com"], "regexFilter": "^http:\\/\\/[a-z]{5}\\.[a-z]{5}\\.com\\/[a-z]{10}\\.apk$", "resourceTypes": ["main_frame"]}, "id": 1000082}, {"action": {"type": "block"}, "condition": {"isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[0-9a-z]{12,16}\\.azureedge\\.net\\/\\d{4}\\/", "resourceTypes": ["main_frame"]}, "id": 1000084}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["com", "top"], "regexFilter": "\\/[0-9a-f]{32}\\/maq\\/$", "resourceTypes": ["main_frame"]}, "id": 1000086}, {"action": {"type": "block"}, "condition": {"initiatorDomains": ["gov.br"], "regexFilter": "\\.br\\/(?:[a-z]{4,8}\\/){1,2}\\?(?:app|patt|videos?)=\\S+\\.shtml$", "resourceTypes": ["main_frame"]}, "id": 1000087}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["hottystop.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/[_0-9a-zA-Z]+\\.jpg$", "resourceTypes": ["image"]}, "id": 1000088}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["playsexgames.xxx"], "isUrlFilterCaseSensitive": false, "regexFilter": "xxx\\/(?:[a-z]+[_-]){1,2}[a-z]+\\.(?:gif|jpg)$", "resourceTypes": ["image"]}, "id": 1000090}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["palcomix.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/(full)?ad[0-9a-z]+\\.(?:gif|jpg)$", "resourceTypes": ["image"]}, "id": 1000091}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["thesquarshers.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/images\\/(?:Banner\\d{1,2}\\.|[a-z]{3}\\/)", "resourceTypes": ["image"]}, "id": 1000092}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["demonoid.is"], "regexFilter": "demonoid\\.is\\/[a-z0-9]{24}\\.jpg$", "resourceTypes": ["image"]}, "id": 1000093}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["imgadult.com", "imgdrive.net", "imgtaxi.com", "imgwallet.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "(?:com|net)\\/[0-9a-f]{12}\\.js$", "resourceTypes": ["script"]}, "id": 1000095}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["hentai2w.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/\\d+\\/[0-9a-z]+\\.js$", "resourceTypes": ["script"]}, "id": 1000096}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["camvideos.org"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/[0-9a-f]{12}\\.js$", "resourceTypes": ["script"]}, "id": 1000097}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["thesun.co.uk"], "regexFilter": "^https:\\/\\/www\\.thesun\\.co\\.uk\\/[0-9a-z]{32}\\.js$", "resourceTypes": ["script"]}, "id": 1000098}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["povaddict.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/[a-zA-Z]{10}\\.js$", "resourceTypes": ["script"]}, "id": 1000099}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["zetporn.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/[A-Za-z]{9,}\\/[A-Za-z]{9,}\\.js$", "resourceTypes": ["script"]}, "id": 1000100}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["teen-hd-sex.com", "tube-teen-18.com", "xxx-asian-tube.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\.com\\/[0-9a-z]{12,}\\/[0-9a-z]{12,}\\.js$", "resourceTypes": ["script"]}, "id": 1000102}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["fleshed.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/static\\/[0-9a-z]+\\/[0-9a-z]+\\.js$", "resourceTypes": ["script"]}, "id": 1000103}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["eropics.to"], "isUrlFilterCaseSensitive": false, "regexFilter": "eropics\\.to\\/[a-zA-Z0-9]{8}\\.js$", "resourceTypes": ["script"]}, "id": 1000104}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["ennovelas.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/www\\.ennovelas\\.com\\/[a-z0-9]{10}$", "resourceTypes": ["script"]}, "id": 1000105}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "isUrlFilterCaseSensitive": false, "regexFilter": "\\/assets\\/jquery\\/jquery-\\d\\.\\d\\.(?:\\d\\.)?min\\.js\\?(?:v=2)?&?type=(?:adult|mainstream)$", "resourceTypes": ["script"]}, "id": 1000106}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["com"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/js\\/\\d{2,3}eka\\d{2,3}\\.js$", "resourceTypes": ["script"]}, "id": 1000107}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["joysporn.sex"], "isUrlFilterCaseSensitive": false, "regexFilter": "\\/js\\/[a-z]j\\.js$", "resourceTypes": ["script"]}, "id": 1000108}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["eldorado.gg"], "isUrlFilterCaseSensitive": false, "regexFilter": "www\\.eldorado\\.gg\\/[a-zA-Z0-9]{10,13}\\.js", "resourceTypes": ["script"]}, "id": 1000109}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["kick.com"], "regexFilter": "^https:\\/\\/kick\\.com\\/[0-9A-z]{16}\\/[0-9A-z]{16}\\?apiKey=", "resourceTypes": ["script"]}, "id": 1000111}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["mudah.my"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/mudah\\.my\\/[a-zA-Z0-9]{16}\\/[a-zA-Z0-9]{16}\\?apiKey=", "resourceTypes": ["script"]}, "id": 1000112}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["bdsmx.tube", "bigdick.tube", "desiporn.tube", "hclips.com", "hdzog.com", "hdzog.tube", "hotmovs.com", "inporn.com", "porn555.com", "shemalez.com", "tubepornclassic.com", "txxx.com", "upornia.com", "vjav.com", "vxxx.com", "youteenporn.net"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/.*\\/[a-z]{4,}\\/[a-z]{4,}\\.js", "resourceTypes": ["script"]}, "id": 1000114}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["chat.openai.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/chat\\.openai\\.com\\/ces\\/v1\\/[a-z]$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000115}, {"action": {"type": "block"}, "condition": {"domainType": "firstParty", "initiatorDomains": ["imgadult.com", "imgdrive.net", "imgtaxi.com", "imgwallet.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "(?:com|net)\\/[a-z-]{3,10}\\.html$", "resourceTypes": ["sub_frame"]}, "id": 1000116}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[0-9a-z]{13,14}\\.cloudfront\\.net\\/\\?[a-z]{3,5}=\\d{6,7}$", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000128}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:cdn77\\.)?aj[0-9a-z]{2}\\d{2}\\.online\\/[0-9a-z]{8}\\.js$", "requestDomains": ["online"], "resourceTypes": ["script"]}, "id": 1000134}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/(?:cdn77\\.)?aj[0-9a-z]{2}\\d{2}\\.bid\\/[0-9a-z]{8}\\.js$", "requestDomains": ["bid"], "resourceTypes": ["script"]}, "id": 1000135}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[a-z]{8,15}\\.[a-z]{2,3}\\/5\\/\\d{6,7}(?:\\?_=\\d+)?$", "resourceTypes": ["script"]}, "id": 1000136}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[a-z]{8,15}\\.(?:com|net)\\/tag\\.min\\.js$", "resourceTypes": ["script"]}, "id": 1000137}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[a-z]{6,12}\\.com\\/script\\/n[a-z]su\\.js$", "resourceTypes": ["script"]}, "id": 1000138}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "excludedInitiatorDomains": ["edu", "gov"], "excludedRequestDomains": ["exploretock.com"], "regexFilter": "^https:\\/\\/www\\.[a-z]{8,14}\\.com\\/[a-z]{1,4}\\.js$", "requestDomains": ["com"], "resourceTypes": ["script"]}, "id": 1000139}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[a-z]{8}\\.xyz\\/main\\.js$", "resourceTypes": ["script"]}, "id": 1000140}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "excludedInitiatorDomains": ["edu", "gov"], "regexFilter": "^https?:\\/\\/cdn\\.[a-z]{3,8}\\.[a-z]{2,6}\\/app\\.js$", "resourceTypes": ["script"]}, "id": 1000141}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "regexFilter": "^https:\\/\\/[a-z]{3,5}\\.[a-z]{10,14}\\.top\\/[a-z]{10,16}\\/[a-z]{5,6}(?:\\?d=\\d)?$", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000147}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "regexFilter": "^https:\\/\\/[a-z]{10,12}\\.com\\/[\\/a-z]{2,}\\?id=[12]\\d{6}$", "resourceTypes": ["script"]}, "id": 1000148}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/[a-z]{2}\\.[a-z]{7,14}\\.com\\/r[0-9A-Za-z]{10,16}\\/[A-Za-z]{5}$", "resourceTypes": ["script"]}, "id": 1000149}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "\\/[0-9a-f]{32}\\/invoke\\.js", "resourceTypes": ["script"]}, "id": 1000152}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["deltabit.co", "nzbstars.com", "papahd.club", "vostfree.online"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/www\\..*.com\\/[a-z]{1,}\\.js$", "resourceTypes": ["script"]}, "id": 1000153}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "excludedInitiatorDomains": ["127.0.0.1", "bitrix24.life", "ccc.ac", "jacksonchen666.com", "lemmy.world", "localhost", "mempool.space", "scribble.ninja", "scribble.website", "spacepub.space", "traineast.co.uk"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[0-9a-z]{5,}\\.(digital|website|life|guru|space|uno|cfd)\\/[a-z0-9]{6,}\\/", "resourceTypes": ["script", "xmlhttprequest"]}, "id": 1000154}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/cdn\\.[0-9a-z]{3,6}\\.xyz\\/[a-z0-9]{8,}\\.js$", "resourceTypes": ["script"]}, "id": 1000155}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["dood.la", "dood.pm", "dood.sh", "dood.so", "dood.to", "dood.watch", "dood.ws"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/www\\.[0-9a-z]{8,}\\.com\\/[0-9a-z]{1,4}\\.js$", "resourceTypes": ["script"]}, "id": 1000156}, {"action": {"type": "block"}, "condition": {"domainType": "third<PERSON><PERSON>y", "initiatorDomains": ["1link.club", "2embed.to", "apiyoutube.cc", "bestmp3converter.com", "clicknupload.red", "clicknupload.to", "daddyhd.com", "dood.wf", "mp4upload.com", "poscitech.com", "sportcast.life", "streamhub.to", "streamvid.net", "tvshows88.live", "uploadbank.com", "uqload.io", "worldstreams.click"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https?:\\/\\/[0-9a-z]{8,}\\.xyz\\/.*", "resourceTypes": ["xmlhttprequest"]}, "id": 1000160}, {"action": {"type": "allow"}, "condition": {"initiatorDomains": ["puzzle-aquarium.com", "puzzle-battleships.com", "puzzle-binairo.com", "puzzle-bridges.com", "puzzle-chess.com", "puzzle-dominosa.com", "puzzle-futoshiki.com", "puzzle-galaxies.com", "puzzle-heyawake.com", "puzzle-hitori.com", "puzzle-jigsaw-sudoku.com", "puzzle-kakurasu.com", "puzzle-kakuro.com", "puzzle-killer-sudoku.com", "puzzle-light-up.com", "puzzle-lits.com", "puzzle-loop.com", "puzzle-masyu.com", "puzzle-minesweeper.com", "puzzle-nonograms.com", "puzzle-norinori.com", "puzzle-nurikabe.com", "puzzle-pipes.com", "puzzle-shakashaka.com", "puzzle-shikaku.com", "puzzle-shingoki.com", "puzzle-skyscrapers.com", "puzzle-slant.com", "puzzle-star-battle.com", "puzzle-stitches.com", "puzzle-sudoku.com", "puzzle-tapa.com", "puzzle-tents.com", "puzzle-thermometers.com", "puzzle-words.com"], "isUrlFilterCaseSensitive": false, "regexFilter": "^https:\\/\\/www\\.puzzle-[a-z]+\\.com\\/", "resourceTypes": ["script"]}, "id": 1000170}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/coomer\\.su\\/api\\/v1\\/(?:fansly|onlyfans)\\/user\\/[.0-9A-Z_a-z]+\\/post\\/[-0-9A-Za-z]+(?:\\/flag)?$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000171}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/coomer\\.su\\/api\\/v1\\/favorites\\/creator\\/(?:fansly|onlyfans)\\/[.0-9A-Z_a-z]+$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000172}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/coomer\\.su\\/api\\/v1\\/favorites\\/post\\/(?:fansly|onlyfans)\\/[.0-9A-Z_a-z]+\\/\\d+$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000173}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/kemono\\.su\\/api\\/v1\\/(?:afdian|boosty|discord|dlsite|fan(?:box|tia)|gumroad|onlyfans|patreon|subscribestar)\\/user\\/[.0-9A-Z_a-z]+\\/post\\/[-0-9A-Za-z]+(?:\\/flag)?$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000174}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/kemono\\.su\\/api\\/v1\\/favorites\\/creator\\/(?:afdian|boosty|discord|dlsite|fan(?:box|tia)|gumroad|onlyfans|patreon|subscribestar)\\/[.0-9A-Z_a-z]+$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000175}, {"action": {"type": "allow"}, "condition": {"domainType": "firstParty", "regexFilter": "^https:\\/\\/kemono\\.su\\/api\\/v1\\/favorites\\/post\\/(?:afdian|boosty|discord|dlsite|fan(?:box|tia)|gumroad|onlyfans|patreon|subscribestar)\\/[.0-9A-Z_a-z]+\\/[0-9A-Za-z]+$", "requestMethods": ["post"], "resourceTypes": ["xmlhttprequest"]}, "id": 1000176}]