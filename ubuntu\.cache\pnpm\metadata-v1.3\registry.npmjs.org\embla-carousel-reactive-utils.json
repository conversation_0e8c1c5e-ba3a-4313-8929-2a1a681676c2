{"name": "embla-carousel-reactive-utils", "dist-tags": {"latest": "8.6.0"}, "versions": {"8.0.0-rc01": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc01", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc01"}, "dist": {"shasum": "601d374541c1fa77ff782dcd8edbf06ef655fd66", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc01.tgz", "fileCount": 9, "integrity": "sha512-JUUtLapI5JSUR0vW+DGGJYhqEn+QuDmxpX01B0/5AXMM/UnNlBmFMaKFab8nXeRPtx0d6irkVEBZSWpjmK25DA==", "signatures": [{"sig": "MEUCIQDCJgqvJmx7cC3qy+i2eDKMrbzRPqzKGN0//JJ4SRdr+gIgREwehdxKmB+LEQdgulJGNq0eilJYN4N/IP09FDoXX+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkSDZVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa8A//SwE1PCehtafv0ClO3vDwgpd8zmvmoh8i3OQvHbgvgscS0eEy\r\nhFVvmCimH7jlSCBaGft4a42fT/1PhuxuCd/FAZi3wdiDJRugQtDmy/RQGRC5\r\nihXFAehI7Z9FU5h/IjX0dLDiHcHpb32/a+qPZx9c5taVDioiteqI6uyOUy4B\r\nFYBcRHg1krI2C6Zb0Bqoks8j2i+9N/Gd1uzIy/rBtPNQzHt2ygXGuLChP88h\r\n7EMZtXab2xLzvydFJ5CUZ04tT6kdLDlKKKLJJsXzKyOtu1UFuNcxxC4SE9J5\r\n/8e0jvpa9dykV1LREmV+BAJy2JlBTmoAE3HMpj0e6KSizfhLUXfn54oqsrt6\r\nXdveUtBrYxpOp2kiE/TWvQI0AI8diZFuJx7eJAonoJciRi4476NNhzD1OADK\r\nNV4E9aBVtATwkajCx8RNm+/zk+Db+wJ6U5eb/wg1QCJFi2zsD7vf/9NNGcgp\r\nf8bW52FWxMeLFCEf3K4l4tEue6XetSkFWqWBW2FpYvF0XldP/KtZeOJlOQ6g\r\njrFmvXQLBi4S43XKzGhGWcfWFIB1LPnefYU9JiZprW69tTNCz1gTkNR1Xymr\r\nDz5pfTbpH7Qgs8g25M3FaGIk0dcg5PEvdS5G9lc97YRuOpdH0t9/bpESbpFx\r\ncijfXQHgRWS+7DHJolR1CanAACbqyo8JMB0=\r\n=cbN9\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc02": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc02", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc02"}, "dist": {"shasum": "f2bb4d4beb049ddd6bdecaa1bb9c74cb1e1d18b8", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc02.tgz", "fileCount": 9, "integrity": "sha512-26ZXUnSiD6eN7rwylPnDYeIM3xNlPd92Wr9PMFou9qlIJSydEA4o3IqScXdiS+3slnlo/hiR81VrmTNjgeGv+Q==", "signatures": [{"sig": "MEUCIBdaOHUUbOxPQCv81ORL+6qz0/ULK+vFIdgA+KPcz/dAAiEA5R5gOUh5b+av9VJTF1gLkdyA7MxUSbxmWObFDebX/3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkS6noACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsaA/+MGM4rM6ysseOwzQtztZsiduFfqgOwHnOA6wlPKDQq2cOWJac\r\nzRwiaoRURJAd+khkfc5JYSFYi6m2ecwL8Q5cWBsQdanJOf440UQYeejCem0F\r\n+I7QxyPS+niYfKGifaCLqqDKhs7cs2rG/VD7ocApZZRzRT3JvznUZOUWxdxk\r\nNMztNRUJhwdFSYrRfodlXizEtpYp2erfam2idyevomxbNjioWJ23mugrMzrV\r\nR8Y213raIjvft6Esv8+0ZuWcigJMutxJTp+dKdrndUfCmCZfsGEb3H3MulYH\r\nldPJcSU9RsBLdpQ1tj4o0/HrXRPndRavut5NNRKzDl3H1PZwze251zZHZSvA\r\nZ7FYoCV2gJ5YCVfGz4h7sx2HrRlwP0KYYVyxC1+TV8jPcoITbFtLthnWE9Mt\r\nUFw9ibMA8fm0QOcTixnB0deKlYunEAEgUVaxxkSXmXZEyKr90ddUZRGOwD8W\r\nu+wzc9/8WSRrLQefVd7X4HA5L3+FThPkeF2lfn76CzLks/cTXc1g0FQxgwMP\r\nFTI2f/eH+M3+aaoS6gjPyTzQE3KUbwNlQZrgYK5yUloUGFWxm41rZuijAEuh\r\nDxGpr1qLtv2DMcuMWU2C1c3CRrT44HWpTmfiIBvxRpih/Mp+xsG/Pf0O9eF1\r\neWJcEKjRNpOpCm7CqNACyicqCWeGnEvJgow=\r\n=QEhJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc03": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc03", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc03"}, "dist": {"shasum": "78dc390965d292ac1d080036435701e49064ffe4", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc03.tgz", "fileCount": 9, "integrity": "sha512-m2ZaxmG0MBs9nFsA3vEz0ZUNiwJaoJM8f//ENe8ODM/rEIiHkSQ1fN90k3k2NX9JemcllYGqz/bmE5cMYGXjJQ==", "signatures": [{"sig": "MEUCIQDUtcTNUeIkibrYJU5z2HEuq9BK+4M0fY+6teB4ZO3NpwIgSmqcvQAhe4uyNnrQUsjN8F053Vgvuyq8C2u/lF7Pyww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUj7tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQTQ//cSGfkU/0xY8TujX4RxZx5XKQs95SK2ASJ+hVSDpKLgobC+tt\r\nR6vx4riem1UJ6VJH9xS1RAuR39U6FR1y1IZdmO0oY9hk2a37xaxhn0QQ0aqi\r\nwgm43NrU8TyWbfzgXuTwbNvBfG4/BnZrvuFJgnaKRPOZWBbWBTkMOhH7b6s8\r\nfCxaw8tlKEBlX5qF9kYqW8mdywUsPm8FSY90/1+US09Z3q1xLv3eFiO8Jsz6\r\n9Y6Q0XgeMGFR8YYpRdz9XSr6BzqV/iG6oK6rdSPqrJJbO7rvVOmLRsjO+jzj\r\n/3GP5g5byX7vTGEstdFQ/NGFrlp0rnxzKJudOkSt4x4EEO+Ookb8w5fx24c8\r\nKjQcQ4340W8KFxRWO1s1ng9MBGj5Pq6lxc5Ri6PGEmJqcBjHl1lYbowuBTl0\r\npgtgp+qWglRsyG+sc/jY5bEWjWZrp3HUFI9wds6H+0UF17CYBnBvGug+207F\r\nGN3XaG3LQzET0eKpAZSPpSVzGBHtb3g3S0ikCOcpwP1sokkdsGVueAhCOzHD\r\npuY0RzSXBQysOj9LlCIqBlFoa/lx08m6ySefsUoC3zhuUZlxyYDDDSbN25IQ\r\ndgWvKzgY8PexUCHYTommJVK4UzYF/sk/nYrCV5foIibqiQOm3oCYN9fiiXMH\r\nE/tqxlgyH0STYZ1SBjczEEyB5g65RDOHpRs=\r\n=2vkX\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0-rc04": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc04", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc04"}, "dist": {"shasum": "66ee2aac922ec6a3f66c69b64a31fa0ccb695062", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc04.tgz", "fileCount": 9, "integrity": "sha512-aXH07V2jht6XL7dN01dCNLWiHeaoYZnFcmHKGCsawGpkJPmylJPQ05o/1icCHSelPZbNk55ztHi5D8uAFOKX8w==", "signatures": [{"sig": "MEUCIQDfByXckgMZL5bdWIRJBcjvJFLtu8kfRgTW9Uxb/dsbbwIgPyEn9AglBNAx38jZK6plSqCZ1klpVBuwVwlc3pnm1a4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574}}, "8.0.0-rc05": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc05", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc05"}, "dist": {"shasum": "cfe4355c2959425aa8ff178009e199f6feee2e51", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc05.tgz", "fileCount": 9, "integrity": "sha512-gWmyjsTm5X4P/8vP7a27lSuboUjVve/QQEqYPP9YDyz97k5PEYd1qncgObB6LRdDuYsLcsJJnV1EinoJZ65YVQ==", "signatures": [{"sig": "MEUCICphZcW70VcVvCSynV2jjqnDS0Vc/UvFU7mHcAK5cUALAiEA0sqUL9aWTAA6FpaSkWttio1WeM53XhsBWdAU4HLA5AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574}}, "8.0.0-rc06": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc06", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc06"}, "dist": {"shasum": "af46eaf15a7444bae9f41ca49d3729bc3baa61c9", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc06.tgz", "fileCount": 9, "integrity": "sha512-AlTrD9eh8aNlDsSjnEOM15Z9f4vQVgG+ULLMSl3BfWdqYvxsbinRcVQd2Buj3KlNq3pH7pm20G63CEz1h8p/Ig==", "signatures": [{"sig": "MEUCIQCVoN34EJPBv/2pAPuiwhxRun7rvQSt8SMVboQErLtSRgIgIIBT/WRgcZcgwggNqTmLe7Foi/c/tDQYVUdFSiQDuUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574}}, "8.0.0-rc07": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc07", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc07"}, "dist": {"shasum": "e998c28972a87da3809d450f8db0032d91faf884", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc07.tgz", "fileCount": 9, "integrity": "sha512-MfdReTR3p0GTcUqLY0DNF5mkYRHF/pler9yBUjRjjjBb1OTQjcFedXYhoCDO49009x/IPM00kcbQQuEtJWpQRQ==", "signatures": [{"sig": "MEUCIB3E2nBEhPMBHa29jJzchM5DO2wsc6ude5WVCyO2kWcTAiEAhkiOvcKwsD4JhVGll/9DdLFd7Q0LsHdh6S0UvnKvnIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574}}, "8.0.0-rc08": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc08", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc08"}, "dist": {"shasum": "7f2d9bb4b4604a7b76c1b600b65aa652f4493271", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc08.tgz", "fileCount": 9, "integrity": "sha512-q+FyiUcK3LF8HjZoBGD2hCPrukF3kYFbC2CK3mPlc17HtFupSXzu/aKR+8GBjaaouCSomdTee8w2fybUL16TVA==", "signatures": [{"sig": "MEQCIDnDBH0xX/ysskWLGDrP5PMqZE8kJEOn0WRWHygvd2u9AiBra+MH4ZkxkWZ6UMmyOnRFbLovJHTJugnycIAidu5gWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9574}}, "8.0.0-rc09": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc09", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc09"}, "dist": {"shasum": "441d097c2fdbcd900bbb9faf9cde0af71acb0f73", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc09.tgz", "fileCount": 9, "integrity": "sha512-AIhcPmP5V1+5KARP8VapWmLYiJAoeT1Iu5Sm/vQB87xp4Z44akT5wHlxJE+9x+e7AWPH5yFsUTkVae7cxzrk/Q==", "signatures": [{"sig": "MEUCIQDpJDoIq08UtJ0t4VDwdYfNOBo2tUzuIQ6G3mb18aYe2AIgGO/uPjMwf5JEgFawz9cicB2lyQLY3ZB31LlKB+mf/TU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}}, "8.0.0-rc10": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc10", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc10"}, "dist": {"shasum": "ed348679d1eb6f473248270feeb7feed2ea305ec", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc10.tgz", "fileCount": 9, "integrity": "sha512-aR6nlBGS0Q+UXcGKZcCOHgo8j2gcWsKuXhrdqlv9QO4Nykhm2Bt30pAw0JuxQQA+VgO66ZAI9sV4DJOAYFeV2g==", "signatures": [{"sig": "MEUCIQCtGalkzC5DbnJ2RVSYYTmFYe+QcjnuA4bMc+SlCP2xawIgJlEiMrz+2m4N6eYOpn07pXvLrwmDE/k09N2r+5DlrjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}}, "8.0.0-rc11": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc11", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc11"}, "dist": {"shasum": "d5493bd2bfeb68b1cbf65d4c836a4d36779a03de", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc11.tgz", "fileCount": 9, "integrity": "sha512-pDNVJNCn0dybLkHw93My+cMfkRQ5oLZff6ZCwgmrw+96aPiZUyo5ANywz8Lb70SWWgD/TNBRrtQCquvjHS31Sg==", "signatures": [{"sig": "MEQCIAghECPqUa+VIEBWMjkisx9GkYZUTr21nBFJbYA/6fuIAiABnzABrfqFks+LEYj5jaF6kgUO9Nc5O3Ezfb2P4Zg4LA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9573}}, "8.0.0-rc12": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc12", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc12"}, "dist": {"shasum": "ecf90c9c9ffd8fa48f4e71934a714ed4f4b33c19", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc12.tgz", "fileCount": 9, "integrity": "sha512-sHYqMYW2qk9UXNMLoBmo4PRe+8qOW8CJDDqfkMB0WlSfrzgi9fCc36nArQz6k9olHsVfEc3haw00KiqRBvVwEg==", "signatures": [{"sig": "MEUCIEvTQqChRr+Um4kRxWKBWG057EGVJ0csqcMe7v9zx0nfAiEAsNpJ9g0ilu7U69ZMBMUFAjnGE3lRomHjq4IAAjnrpmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16199}}, "8.0.0-rc13": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc13", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc13"}, "dist": {"shasum": "383271c9cd20b20fb71328ddd3aa546978a7a542", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc13.tgz", "fileCount": 9, "integrity": "sha512-sH8JRUYTjSAeEJw01YWPLjBOHOCnl9GuqLJKWqMxkYJAluUbB/koshjeewa78und6pZkWUsHUYv/dm0YJ4ZdHg==", "signatures": [{"sig": "MEUCIQC/oasflDjfX6uLwTn3BUUeez8dues4+7iKrcM6ScCQFwIgbyYiNXBn6dvkUlfhL8+fclDyVBm30txqBbXmrNmKMbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16304}}, "8.0.0-rc14": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc14", "devDependencies": {"jest": "^27.5.0", "eslint": "^8.8.0", "rollup": "^2.68.0", "ts-jest": "^27.1.3", "prettier": "2.5.1", "typescript": "^4.5.5", "@types/jest": "^27.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@typescript-eslint/parser": "^5.10.2", "@typescript-eslint/eslint-plugin": "^5.10.2"}, "peerDependencies": {"embla-carousel": "8.0.0-rc14"}, "dist": {"shasum": "a8844b3930fb3b1423e07544b3efb125525c454a", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc14.tgz", "fileCount": 9, "integrity": "sha512-r153bynAo9eTBuWWggPWLYnE9xqVOYmkkeMbAuGX8pkUisJN8aTLMW9b7CYOzjURRB7z85EmYRbeBg+axQzc9g==", "signatures": [{"sig": "MEUCIC6+noix/U6IBJHlWaayYwMoopflFRPyxyMIMCRcmE+1AiEA/lrTc9yfTF6/f8VfWZqD3yRsV81KLmFU/jO2Lo2VvzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16304}}, "8.0.0-rc15": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc15", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc15"}, "dist": {"shasum": "29309e19657944bc3fb6a908a2da5675c7d2a325", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc15.tgz", "fileCount": 15, "integrity": "sha512-17Pr+N2vULFS8Lxzi2jaebwVloiFJLpdJMJLKJKQ26NzEmX8pVtHSu3uuTWc8HPwye5HFugqCPJ2QoWWhKs6Kg==", "signatures": [{"sig": "MEYCIQCd5cElPuf1NLXJzABJ1Z1taJt+G+5Al2C9sqAffwHM+gIhAIh+pHdMpHB9L9Nu2R+BXr/KkRit9AqBv0uotVwytaK2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29705}}, "8.0.0-rc16": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc16", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc16"}, "dist": {"shasum": "21dda479616aed7c97a3b4205770739bd4def8e6", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc16.tgz", "fileCount": 15, "integrity": "sha512-b0ZyEsUxLkg/IO4ePQxPZJvymczB1nmeVPxA5CaqjNq0DgrW0d9i0uywNvVYY8+Z5XHFACwO+q68UfnVSWFUDw==", "signatures": [{"sig": "MEYCIQCwttRWNCAUcZc97zb6cRiqRvwPcoKVw8WvlOMCTmFYAgIhANtg2+SJRDjijf4NLXNkPzfgveNWaIHihpnLso6up3ub", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29705}}, "8.0.0-rc17": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc17", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc17"}, "dist": {"shasum": "a94934fdce7faa7464581d6c9f10d9e8ed63ca42", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc17.tgz", "fileCount": 15, "integrity": "sha512-eluEOK/u5HdjYaTLC4bUG3iTCnyX7RsYix3il0aH4ZECOKa5fS+pVK2vrM17Mgw6C5Hyjcr3r3lfJtGerVzVsQ==", "signatures": [{"sig": "MEYCIQDlhLPrEnwdujb0h55DyMyzCY2atplSKsmxmEbPhUXQMgIhAIDt3IYAL0QHfy4MZsVjOWaeTl84+INzZE3d5F7/vvUY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29705}}, "8.0.0-rc18": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc18", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc18"}, "dist": {"shasum": "d99726d0a532f834e3ae41a9a1d976917ecfcaed", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc18.tgz", "fileCount": 15, "integrity": "sha512-VOFfvhkICz4GKXb/huMTspYVR8mx8C4uDf0Kp+jA9iZNUA4lmlfxxYUr++SwIy1xABycpqML/9hP2tV6Nn0AEQ==", "signatures": [{"sig": "MEQCIEY9g0cSZU2aqsgFYNpch3SQW15C//TUUkOrukVUGnhKAiAjuTs5RFnYLA57vKWxWasgiQ+7qzJML8wNHUgrC4O3qA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30098}}, "8.0.0-rc19": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc19", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc19"}, "dist": {"shasum": "6604f9c9f3a07f13734ca0af7c19310c1adc1f7e", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc19.tgz", "fileCount": 15, "integrity": "sha512-ed9NppY0OxTtrSIwTCYNcMLlQfSNcNy8Zsw8uIG0te3qrhvQ2ePPsbcElK2SRAV8VMU6G7JQweQIb6amzYMDXA==", "signatures": [{"sig": "MEYCIQCEIl8BwwuJu5c9q4Fw2sbpI9k5rkxkSp7LvYxzGAc6iQIhAI030+EHriLtpSik9VOIXrC6YDdn3NzHUCY8PUbAZZV1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30706}}, "8.0.0-rc20": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc20", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc20"}, "dist": {"shasum": "606c2708bdb95c4c3391a3a6cd01d3c7b81e36f3", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc20.tgz", "fileCount": 15, "integrity": "sha512-fE7IeSS8HqwDnTDMP8eo0i4pcYQAemmJq53zCLXnp3Yj/p5+IpB1nC7aKQjd2ug1dGOSwwNRFaPI3shlAVVW/A==", "signatures": [{"sig": "MEQCIGAPvAlq9S1rHxbtfp+NJ7VEHRW6KZsqO75Wjmg1iMjNAiAWcO2gHJmznFrFq2i67M+/x8WqAMX29AkJAnprK1EJlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31738}}, "8.0.0-rc21": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc21", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc21"}, "dist": {"shasum": "bd28b83a6696d0a1b490525638393a4655b2f767", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc21.tgz", "fileCount": 15, "integrity": "sha512-TnV49hoTdwfcKr2vgQHQ3zcCqJSkvLJ5rR/pGzmEx5GeO07CV/e755lkSD7No0C6cz+JFB8dcHV7uS+5Gnc7Lg==", "signatures": [{"sig": "MEUCIQD5BHpFmH5SFf1bTzgFgn1bZ3MmAN8G7DT8RxliaB6aXgIgRqOYfJaj+vEH5sm9+B1A2Wy1gNchD3rHGwI+Q6qEsFo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31739}}, "8.0.0-rc22": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc22", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc22"}, "dist": {"shasum": "de40e32d684487b4635bd9cdef23b3c5c7c599d6", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc22.tgz", "fileCount": 15, "integrity": "sha512-K4b8QhQGXYW5wr4l+U6XryhafsFV5c/IyohDnZN3MGoYIB9xY7qpjUWAcs5bTDjAD+qCZPOuXre0D3IVa28mqw==", "signatures": [{"sig": "MEUCIFFhSao3N55EHofQx6ZV34Mp2q9u4/or+cC1+yZdRIrIAiEA/gzzzbfJAmajnTDRDKIFbX9exuUiK72yT3yEZaGcJnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31739}}, "8.0.0-rc23": {"name": "embla-carousel-reactive-utils", "version": "8.0.0-rc23", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0-rc23"}, "dist": {"shasum": "9d64577a9bde68e4c4cd4bb76bf4daec7110ea9a", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0-rc23.tgz", "fileCount": 15, "integrity": "sha512-/NPejNksrw1iWthTtrps5LNj6gJzylvfCuNTh2+P0FLSPbX/+RlT84Ab5qnbSS/vdmEs8daJbVvb5Bol9v0OdQ==", "signatures": [{"sig": "MEYCIQDawdSpNWQ0BnsNFWEfM+MSXmrARAYeMLibeYASxEuFbQIhAN7n6rt0cH8Ea9zzJ0maRTLjaigOgSXm/ZzJ8JWLkic4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31737}}, "8.0.0": {"name": "embla-carousel-reactive-utils", "version": "8.0.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.0"}, "dist": {"shasum": "65342e9feb8d2780fcb1d1d6050ce41837385efa", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.0.tgz", "fileCount": 15, "integrity": "sha512-JCw0CqCXI7tbHDRogBb9PoeMLyjEC1vpN0lDOzUjmlfVgtfF+ffLaOK8bVtXVUEbNs/3guGe3NSzA5J5aYzLzw==", "signatures": [{"sig": "MEMCHwZjIPl9dwZQSQX75u4tqPMaaGFe9kj8TDMvK8zOU18CIFqMcXIydA7SLCGfrTSnQoLRaGccKt/B9wEZTsU1FB5V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31622}}, "8.0.1": {"name": "embla-carousel-reactive-utils", "version": "8.0.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.1"}, "dist": {"shasum": "7fe11dd07bf9f8e95debdc2e4415d608fe8723c6", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.1.tgz", "fileCount": 15, "integrity": "sha512-KBSkz2h9LwVFkOrwzIJKgXbmEDlIShkreeOHnV8Cph09AdBMzb412nRkcctbeDcuG9x3CVsqLzJrSXnZeYhFPQ==", "signatures": [{"sig": "MEYCIQDvXZqtnyfBb0cxSXdf0cZ+vUD0y5RtAfmLp6seFPB0jQIhAJfMXUQC6qisLSUifh3nacPMlV/Z9Ry8M+VT/3EseLJk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31630}}, "8.0.2": {"name": "embla-carousel-reactive-utils", "version": "8.0.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.2"}, "dist": {"shasum": "cd48cbe46198d9a64b0a8d62ce6832ae97c9fe70", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.2.tgz", "fileCount": 15, "integrity": "sha512-nLZqDkQdO0hvOP49/dUwjkkepMnUXgIzhyRuDjwGqswpB4Ibnc5M+w7rSQQAM+uMj0cPaXnYOTlv8XD7I/zVNw==", "signatures": [{"sig": "MEUCIQCRZ55pthqlW7ScKBlsldqxd18a1EzEfOVQWjQ7qhceFQIgRnVXQQa7ZGqIX/I20v3PAm52N5+kCZOXUtyLpxB/mIY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32559}}, "8.0.3": {"name": "embla-carousel-reactive-utils", "version": "8.0.3", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.3"}, "dist": {"shasum": "4ed56d3310a30902b62b3c40834b5e6e339017cd", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.3.tgz", "fileCount": 15, "integrity": "sha512-9qvdSZJsFh6JokPNJYfsU6J8uDX9sDjBuUsmshuY3y3wS6JPwuE5d45i04DDNV9EB/7hwQew5qwRiemo3CwrIw==", "signatures": [{"sig": "MEUCIQCEItqE1VpWYI1lEAXzsxxkq84AhDZ+v1PmtdX9MiuMKwIgaFDzz9GtwVt+lcQupsubve8HelZy/EK+vsg0dw8UDA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.0.4": {"name": "embla-carousel-reactive-utils", "version": "8.0.4", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.0.4"}, "dist": {"shasum": "e767be401d4abae25e355d0f7d113d32aa4565f9", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.0.4.tgz", "fileCount": 15, "integrity": "sha512-7lWM+8hanIF2roTAjxwfqfr0+VTbr9rYVlAs0r5PwPOVpz1G3/+bRelgAYvwcfWVm5Vydo2pKXVfFOPQjznF7w==", "signatures": [{"sig": "MEYCIQC9bmfXvhLDzEu2ifI0IPS/Xxv8xEGZhW2Puw4Q6WiGLQIhAP8IewEj1zgHLSLiFGTn6XdDAop1zRp22ccj5gv/jPsX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.0": {"name": "embla-carousel-reactive-utils", "version": "8.1.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.0"}, "dist": {"shasum": "21581bc97f71d717c69c93ab7882cbd01664d78c", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.0.tgz", "fileCount": 15, "integrity": "sha512-jiPOa5iT61HATnn6cjym4+0r+VGopJlFTO6MtzDUM435vjZQ1ot995aa7hJJ++ssYOhUlHYt6YJy08Ooigx4hQ==", "signatures": [{"sig": "MEYCIQDOXBe78LXmmzP6QMkZBQ9gFoQdQ69NnrSLSOO17nDIiwIhAIr7OoZe8caB6MNPRvexTcsT0Ep1SY0d9g3X1TNjv0iB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.1": {"name": "embla-carousel-reactive-utils", "version": "8.1.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.1"}, "dist": {"shasum": "0c857b3543359c7dec75e461c5b9d470d90cbd49", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.1.tgz", "fileCount": 15, "integrity": "sha512-zlsaorYN56qtoXoVNw8hA/IbUjxq0pdggZpl9b5V2Cxvrd47ZSL5Lat7GgC+kgyUEdEocP1iOmw5+vpCX9rZPA==", "signatures": [{"sig": "MEYCIQCUlodX+RBB2y1CU1wa8sWlGvB6zkres8nV0JYzn7h4DgIhAODOrxycJx1Ye+b2Hk/KO8j6JWWPbmFCytQqkEsI9Hi2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.2": {"name": "embla-carousel-reactive-utils", "version": "8.1.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.2"}, "dist": {"shasum": "af9d85f59b88c471f098c9d75adb3bda420296ba", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.2.tgz", "fileCount": 15, "integrity": "sha512-iDTx9WKtxz/ljZWmcUTY6Xfd6lx/Zkpz4tDqZHeHnMOmI1Wgu1S4ZWA7yzHWqpj14mND8Gw1BH9kkycuXL/PeQ==", "signatures": [{"sig": "MEUCIBVDW4VWEj2mWVl9vqgGWBVcZqRqTR3d6ATZkP2Fu79YAiEA1eXnQyQAOLjRyTSBoX7luWx12qh1agR+jvhcFILFnls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.3": {"name": "embla-carousel-reactive-utils", "version": "8.1.3", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.3"}, "dist": {"shasum": "b4e7d439ab6aefb9554b14198e567e919b52f167", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.3.tgz", "fileCount": 15, "integrity": "sha512-D8tAK6NRQVEubMWb+b/BJ3VvGPsbEeEFOBM6cCCwfiyfLzNlacOAt0q2dtUEA9DbGxeWkB8ExgXzFRxhGV2Hig==", "signatures": [{"sig": "MEUCIHHZ4IMnPoItGENYUUteKjLSO9NmrqmoG/GM5y3xy+bNAiEAyb02M19fjtY7WCq5uzyFNd46ybhbz5Gspkib4fwqaGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.4": {"name": "embla-carousel-reactive-utils", "version": "8.1.4", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.4"}, "dist": {"shasum": "edac8e2412d3d144c15ce1826a6c3f201f219d38", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.4.tgz", "fileCount": 15, "integrity": "sha512-qkovXTiNaURsnYhJxUBsDx9YV6crxLIZwfO6jIFSKKn6ZZE1JVeZAvy/UsiyNxiFikVrO76iquvD6uBHI9YD8w==", "signatures": [{"sig": "MEQCIFmxQxWZEZxgU3ugAXHQv/31+YEu3p4pRy3WBb8ny0J3AiBbR1snlaLf2OvmDFcbDziF4+Oa8CmFoXnCchvPK2HFaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.5": {"name": "embla-carousel-reactive-utils", "version": "8.1.5", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.5"}, "dist": {"shasum": "87475b9478a60c92be883bccecebbf4f180796f9", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.5.tgz", "fileCount": 15, "integrity": "sha512-76uZTrSaEGGta+qpiGkMFlLK0I7N04TdjZ2obrBhyggYIFDWlxk1CriIEmt2lisLNsa1IYXM85kr863JoCMSyg==", "signatures": [{"sig": "MEQCIFXMNsKC4rACJCPu8AF+qwl89VHunOS9BInumotzY/eAAiBUmXyK/1iksOCxnzBjZjlkEMDogVxGC5Snz6ySNxbC3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.6": {"name": "embla-carousel-reactive-utils", "version": "8.1.6", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.6"}, "dist": {"shasum": "51c4a1dc6df1e608e9480f7a5fc3328e95926f91", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.6.tgz", "fileCount": 15, "integrity": "sha512-Wg+J2YoqLqkaqsXi7fTJaLmXm6BpgDRJ0EfTdvQ4KE/ip5OsUuKGpJsEQDTt4waGXSDyZhIBlfoQtgGJeyYQ1Q==", "signatures": [{"sig": "MEYCIQCPbpzW231/AD1M7hiDccI3EWJ90qLizyT4IKXVRBVAhQIhAIOUiq/Lk82UD9R0gcXIVExelunAMulH5aA5ZAHZPMGJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32943}}, "8.1.7": {"name": "embla-carousel-reactive-utils", "version": "8.1.7", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.7"}, "dist": {"shasum": "3834440bdb4e2e79fbaf466343a2b458f74f0091", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.7.tgz", "fileCount": 15, "integrity": "sha512-FDPcWjNtW04KSuvSfGbVeoB8yl5no3E0++HikO/uW12cNkMnWt68C4OBOakZQZlpUdRQSA9KCYoBuQzfpVGvZQ==", "signatures": [{"sig": "MEYCIQCCo3GCnhnGXsYr1D3Y0Kwj8BdlEaPXfOrSuvJ65tGOFAIhALS1xQEho3mYqLGRCMb2FSQCulNoYdbGa2h3DwwHduxH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33131}}, "8.1.8": {"name": "embla-carousel-reactive-utils", "version": "8.1.8", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.1.8"}, "dist": {"shasum": "8c2577516216c16cfe24b345fda0fa53d8072f40", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.1.8.tgz", "fileCount": 15, "integrity": "sha512-bwV/23WD3Ecm0YuQ4I6Syzs3tdVJw0Oj3VCZlEODv1kH8LZ5kNDLgX2Uvx5brvoe2hpifBHPBQ8gYlxNL5kMPA==", "signatures": [{"sig": "MEUCIBW/MC6OHNSXPtiti6zxTA5t7bRQB0yZ6AkvQJAxbradAiEAhCteUcMzELm3b4S0ORQhLss8jhmRd6kl7WDPTkI9VFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33324}}, "8.2.0": {"name": "embla-carousel-reactive-utils", "version": "8.2.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.2.0"}, "dist": {"shasum": "2abe9a01d639641617486a41704525c1de506021", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.2.0.tgz", "fileCount": 15, "integrity": "sha512-ZdaPNgMydkPBiDRUv+wRIz3hpZJ3LKrTyz+XWi286qlwPyZFJDjbzPBiXnC3czF9N/nsabSc7LTRvGauUzwKEg==", "signatures": [{"sig": "MEUCIDadKoJ8gd01JQbaHmEDjBnB3l/3aEE3WMjTZ+ijzso0AiEAtlBEoqVLJEDZe9HS2TUhJs054n3vmfzk2Q5U9ACIPHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33326}}, "8.2.1": {"name": "embla-carousel-reactive-utils", "version": "8.2.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.2.1"}, "dist": {"shasum": "c62fdb6f77c6dcd68bcdaba62523acacb8e633fc", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.2.1.tgz", "fileCount": 15, "integrity": "sha512-LXMVOOyv09ZKRxRQXYMX1FpVGcypsuxdcidNcNlBQUN2mK7hkmjVFQwwhfnnY39KMi88XYnYPBgMxfTe0vxSrA==", "signatures": [{"sig": "MEUCIEKhwJ+OOthhbAa6ImZS0qvC9rglifrlObjWokNaUUsoAiEA1avD1SqYf3Uk0nayI1eptwwl9Wna0rI4aUz5OcTSsFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33629}}, "8.3.0": {"name": "embla-carousel-reactive-utils", "version": "8.3.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.1.5", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.3.0"}, "dist": {"shasum": "75f177ed2f6bdafbaab8f869f936692d08cd488e", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.3.0.tgz", "fileCount": 15, "integrity": "sha512-EYdhhJ302SC4Lmkx8GRsp0sjUhEN4WyFXPOk0kGu9OXZSRMmcBlRgTvHcq8eKJE1bXWBsOi1T83B+BSSVZSmwQ==", "signatures": [{"sig": "MEYCIQDbTjC2fvURVXiUe0WwLO5OvA5NXu6OrzITgypcUnQ4IwIhAKGrj0zPqcobKvFCDz+FDKWbwN009n0w9XYfC9YYNKlo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33847}}, "8.3.1": {"name": "embla-carousel-reactive-utils", "version": "8.3.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.3.1"}, "dist": {"shasum": "8e92407f92f55e5f38ef22d24c018077476f1e11", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.3.1.tgz", "fileCount": 15, "integrity": "sha512-Js6rTTINNGnUGPu7l5kTcheoSbEnP5Ak2iX0G9uOoI8okTNLMzuWlEIpYFd1WP0Sq82FFcLkKM2oiO6jcElZ/Q==", "signatures": [{"sig": "MEUCIF5jMIiHlCMG8M2obv+OZBLmfS0NvMiU7di98YbVBSb6AiEA9Nbpi3UxFELmK/Tgqje3Xbvi5zb/GWjXwaxBaDHCIjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33773}}, "8.4.0": {"name": "embla-carousel-reactive-utils", "version": "8.4.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.4.0"}, "dist": {"shasum": "8da29a5f6ae97ac1166c49813cd9bc3818642054", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.4.0.tgz", "fileCount": 15, "integrity": "sha512-yi/2paSYQqMGgh983Sj8G4+zt4bhABC1pzN4P4Vmc7UhSxqo9I7r15tR7s3/zS0JEl3orRSw7TxX92IZqj5NXA==", "signatures": [{"sig": "MEUCIEK8p3Abo8PlGTG22GpavAjjPhymcfpwj051ZEm7clo8AiEA0qqDXbdGEKk73Oe2Az7lQoshZR7NiLhQ0FooLY7Wpsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33974}}, "8.5.0": {"name": "embla-carousel-reactive-utils", "version": "8.5.0", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.5.0"}, "dist": {"shasum": "4053773ac2a64eba782d74524f500ddf95127ba7", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.0.tgz", "fileCount": 15, "integrity": "sha512-K4mOsS0ZIEzD1u8vzmAGdeh9rirL9B31WJNd99XJ9aGtXC2oe7Jlb04Z7iFDG/HgjfYC9x9zk7lS6Dyo5NAHiw==", "signatures": [{"sig": "MEQCIE4d4gfO6B5AADZChXEgii0WPhk5FZNXBH+KumH57Y+jAiB7bkO/NHEQlC2KrNdz/rBOpIpoxCYPZpObrpXuk//Czw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33974}}, "8.5.1": {"name": "embla-carousel-reactive-utils", "version": "8.5.1", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.5.1"}, "dist": {"shasum": "3059ab2f72f04988a96694f700a772a72bb75ffb", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.1.tgz", "fileCount": 15, "integrity": "sha512-n7VSoGIiiDIc4MfXF3ZRTO59KDp820QDuyBDGlt5/65+lumPHxX2JLz0EZ23hZ4eg4vZGUXwMkYv02fw2JVo/A==", "signatures": [{"sig": "MEUCIBb7eRsspwxJlcwB2BsMEF59DnK2yuVJET+zwLxOfkv4AiEA/SWdoNRiAODDXpBr0qB6loR8LC6ze+gJDFCjjpaFURk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33974}}, "8.5.2": {"name": "embla-carousel-reactive-utils", "version": "8.5.2", "devDependencies": {"jest": "^29.5.0", "eslint": "^8.52.0", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "prettier": "2.8.8", "typescript": "^5.2.2", "@types/jest": "^29.5.6", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@typescript-eslint/parser": "^6.9.0", "@typescript-eslint/eslint-plugin": "^6.9.0"}, "peerDependencies": {"embla-carousel": "8.5.2"}, "dist": {"shasum": "914bf99c3d91e0809282fc1d14df3d1453f222c1", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.5.2.tgz", "fileCount": 15, "integrity": "sha512-QC8/hYSK/pEmqEdU1IO5O+XNc/Ptmmq7uCB44vKplgLKhB/l0+yvYx0+Cv0sF6Ena8Srld5vUErZkT+yTahtDg==", "signatures": [{"sig": "MEQCIGAAUYIE1lou2z5lzeZD9AOt3hAe5lnK3E1YKeV/9T/AAiB1wvnJKUgHoaHCW0m5isqebPKtlyExZJVgXBeaxRG/NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34183}}, "8.6.0": {"name": "embla-carousel-reactive-utils", "version": "8.6.0", "devDependencies": {"@types/jest": "^29.5.6", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "prettier": "2.8.8", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}, "peerDependencies": {"embla-carousel": "8.6.0"}, "dist": {"shasum": "607f1d8ab9921c906a555c206251b2c6db687223", "integrity": "sha512-fMVUDUEx0/uIEDM0Mz3dHznDhfX+znCCDCeIophYb1QGVM7YThSWX+wz11zlYwWFOr74b4QLGg0hrGPJeG2s4A==", "tarball": "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.6.0.tgz", "fileCount": 15, "unpackedSize": 34372, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFM62LNzQIjaP4BeHEK15Dn4Taf8KL5oAWhVnsVXG/b7AiApOxi7raGkoOuXhQgrkjQyE1vzbLB8hqw2gSj6RHIIqg=="}]}}}, "modified": "2025-04-04T17:37:52.252Z", "cachedAt": 1747660589695}