{"name": "@radix-ui/react-tabs", "dist-tags": {"next": "1.1.12-rc.1746560904918", "latest": "1.1.11"}, "versions": {"0.0.1": {"name": "@radix-ui/react-tabs", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1", "@radix-ui/react-roving-focus": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "da6f3f5c8f22d921a850216448648beccd71d411", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-AfWGL2iDSJB6h34uxFq3q3//xjrSkM0A5YQlC3STK03qwdZiYr6uR4n0g/mIWetVs8E8nZFwN9ibo+8FHa+rrw==", "signatures": [{"sig": "MEUCIQDgV7plIc62Lu4wCfxfCdyIlXZYGKTjx2l3NqXCy2WSwAIgJusj/V/1r11WzJxbupDpwYE3WPF3b6RFiCWK8g2k+xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39103, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbZCRA9TVsSAnZWagAAUVAP/jNZUJNwAs3vz6awsNEO\nCH0JXAGMeC0Gd8k4LI/LsI3y5NbqUUC9rY5kbJjxYrYZfFjs578APurq89gc\nr88nW3zOwnkAOJGmA5wknQTvn0NWnSgiVzvhJMXvbppucowi8eP6Q/syo4jG\n5foloxlI/qG1hFs0r5hOlIcsXe9/BIdkrWyWIEaaRMJ7GDgIbwN1XMVEltiz\nZ/P1fwlUwN1p57PG4DbB1RQmxt8QDzJrY21N52wE+BZWP04ngKCHobbAREto\nWwO/OfA/4nfujr37DKh/tuVEjjLNd4iEJ7G58yZg5s3lsqzriK0BpXPkE4LI\nN5vz1J9iWURGM2E9EuTJIDIXRHBaDDnAI83t2qcUVdoKTw7yNFUlNFz7uQrX\n8aH+84Ij1yt/LogYmUi2tDpzHZDCtRsp1bQBDpK34z5Niu2aHHhFPFOPTVxR\npmyxRvnJrVqI+8pmzknyNTUCWvdfyZJVxb09dnC8/msbrKeZaOdRa74XC3YV\nG0V8/VZt1SedFSXB6jHjhv9vMMwZV+Lm0+FjXf6Kb/PRVZHy2ViIbL381wNG\nfygg9jdxZSSB4dhZB6ak31IG4x+AkHLv1dW2ANMJwQnJu8Guuml6fmnAzu69\nDCCgjgMBeTcn7JPusac1eeNm4KphLMokkbhxP/wrPtXelXPeJ7ddkQhpsmuQ\n8GQH\r\n=0ZNv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-tabs", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2", "@radix-ui/react-roving-focus": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30d307479780c6bdd582c8c82ac3bedcea04607d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-XRLTX6iQefaTVS9u0Wp00h46aD7Q7Mhs1ivj2oMuicPrvw7/1La9mTGwUcRthe2NCq0pIuITxHs3dPB0hgozfA==", "signatures": [{"sig": "MEQCIGu8KKmw0ZZWbL7EflE0u3sG0RJA1gS/+cxmZIaKOXKaAiA2qtjLg/SpyxOZHfXs/BwL7aCKoj4UaT21z8i9KiTOrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvaCRA9TVsSAnZWagAAmDsP/2v+Z+6vF6Qt9t0oBuZM\nXk93/ILEM5M1wMuP3djimr7AWryxu9gNzassTipQjYa0Kxip7Py+wrARnKO5\nmmPqCN9VdGDhP2rqhOXtYrTT/+NLPhqo19po1Rt/3oQdi4mITxk2Lk9N9k1g\ntzmjekF0VGPOAp0mHHUIulbnW9QrLyX81PCta1gkEuBQaRbVatyywOZRAdxs\nxjh6uNEVLVuiUjVlFCrC97wCbvrhC29nwFwvuWsALPVe/8gmAulEvPG0vExk\n55So/aos2cQzEGfS15nWTy+7KfYkXupKLThrhfgVBuUxyjkwKx5Bz+mwVXXM\n+u8fiaPtY+ub4pSxphKILc0WUaS+Jgb812Ta5R7XKQcrR8UIk0CPNkrCuX/Z\n72t0j8vJB0ei1soDGIJGLBMJXws81n4YWbC0f70T++sudZZVf2OOr0xbxQhX\n+1pvMoiCAjXAR44ceQuXth+iPBdRmKgpSPLJrb0Tu3IRcQOyt45ry2gu15wz\nrMMvcxi1Rsoj7JlDcaLOSCYhb0eHpI2EHpbWaaXSfYpiUU06Lkxz2bfPJB1+\nlj//s/txpTPsMLOxF4HDCn4hpKHgwjosxTrFknGDAIoDAlQmkZVmln2LkdpE\nNAp9U1rX5s3a/2GtcwkPa9O85YbeHNaz9GYjYVTZQAlH4QlK7V/+JtHMcroG\nA48S\r\n=QkTE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-tabs", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3", "@radix-ui/react-roving-focus": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f16a1103a20c3a3b4287db9a41ac13a09fc77385", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-JYZ26CJQzk47BauW88FV7IBp9EqbFn3U3PP6sP8S3Njb5NhiOxD9PBzXH3N+2VQaJoMW1k+Yka5eAZWBbIMYeA==", "signatures": [{"sig": "MEUCIGfT89rIMf8ByCpGymMN1o3O1rg9wCZPKmyaYNqwHZDcAiEA3CJGRkWLzvVxc//e5Fg9V9j7wi0PUmoBZ1m26ABRVzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41033, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtzCRA9TVsSAnZWagAA09QP/AopsyuZNPnXNPoKEjoq\nn7q11/CrAolJugdJdoz7h+niLEZPyFI/HFp5wP9uRBg093xwYY6vvmksrbQU\nZGfjknXBqGps9hZuNt5ry0e8AblDzMg8yt0/W1pHlgyzMzPt/4CJNwayf13u\nzluzUIYka0PRYg/UEqIiV6HTHscaLOUEMD+64tZExZ69smxxi652RzoqfTF7\nRqEJsyqm1j9ZxpQAZ2hT0NQoKhAyThQAjERZOAQNL5RyKvWoxehRBKwwYoqE\n0F64HY9rweP1tQNONvFFVhC8sV2sREWlgS8UcMCIoqHzRQwcCX585Bp83+uk\n55C9iii4lOEuhRNc3VvcDCkxwnc56zG0dxkv9nwvc2M7dVDsD90Kdy8CdeNl\nN4vq9rF50XqKNATtq7s+6cSvNrC/ntNZq2DD/B454IfRWgEb2CFQI9bIUT5+\nfSMzyx37CTHXraHujjOAdZ/l6R5B9zY4PgFw+I4YBZBaNsHCm2QsCd3sXoce\nN8+VWFL5qmTGVlJdSnKdYy+5LsRSu4SzqzjZNS08z3+++CnrPZ9tr/HALW97\n2b1z/GwKNFuG1n2YGNU0EpWbWNcsIokce9hRpz37a7E57NTFSoFhkgjnE3tX\nwe2csKtq7CRO6TksmezYcz2lUfOQ7eN+igRYFb55MF7bfGBxga+t+NbOrkY7\nfpgo\r\n=Qea+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-tabs", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4", "@radix-ui/react-roving-focus": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f9a0687ea86d67cf42726bfd2df52331a45e9637", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-4Z2NJL2X92zZYtGn8Kq692nQUXi9AoVWalFceMntu+bgFtZukapZN8tTzawsOB8cTkKw7/HFUdHVt3x0V11GCQ==", "signatures": [{"sig": "MEQCIFYKTfjqM3r8ynGmeUJPqULTh0GHTe/PuXoIE1Kb9g7/AiALtjRsZjRE9U5OfkbiG6r+3G2g8oQ8NnjAU2wsL/fOIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40606, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAeCRA9TVsSAnZWagAAICoP/2gyIMclNJen4WWzcw3X\nRtQ9UfzZy7iACfb/oz/+wXCiCphBnjlTupSQGGyMPW63WqzrY7cHmkeR5mkK\nWTkgA3LMtbJZpc8iayFiK9aOW3msVuceIviVk0sTCZQqd4om67v4nOFPr4bS\nL84xDpjRbiw00PK9Cnnb3SI6F3rikXAezS21W6JwyOFZC5FA0zMqxyzNidIJ\nRCs83YHp64eJP+vuPIGSI3OIT+u9Lvnw6i9c/TcQyCPnUchf7ihHpGE07FNs\nAYM+okI2U6qcWWZ/zfvVk5hjyV4huNcukD4r8ZqXWp6tZk8pvSHvCnJyH7W9\nudDy7V/3bmfzsSaR7wJFWYTWdeuV4jHPG7PnuIAgKD35y/7fYz5JjB7J37hw\naSxL0BgsPuxOrxWlPgLuc/PasKHxJFhVEzQxI14JOcpksfco2gTU8JGR0idh\nd29kklUNW4rMFrx2v7DWrqZLzmiOMFazH3y4GcarM9wnW+qmSz7T9wrg8pq+\nctH9mERJ6T3j57g5hVZZE1TFIPMzcwlu5GUSMgw+b85ghV6QlBaIS59AGW0p\n/1RKKjm8pd+i1yP/je0J50dACEy+Pu5yoNdeuXtjNwgvoXIpG2JseZphlRuB\nR9bhO2C6x7g+7QVnsFBGr0/onvTnmgVHQe84j2r6QVLeiAJ3RR9Erz0avIfq\nRwjW\r\n=xBi4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-tabs", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5", "@radix-ui/react-roving-focus": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b56cadd1c6811d9a9f28305519ed414132c0b546", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-FP6suUnOOyDgj/XhUDPcAUFrAtfjlTjWn1PmxyCnHRBbppSjKOWLCEmC7oHclmrskZEgaKmfoEGCL4JNCpUikg==", "signatures": [{"sig": "MEYCIQDXVKlKvPVZ/lbO32MM2vg9cjBVAIwjMFZt8Zl6ALqV4gIhAP5mlBnSSabEzQc/ZGdzscB0VcrNTKC1WidWy9q2ubIA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/WBCRA9TVsSAnZWagAAaqYP/2G+cm01gKk/NNjE+xPt\n9SjIsHa/pMDkyzLlFLVkxq9ueaCCU1429hRgBNrFQoysOy+Iq58KxsTUB/Tv\ncGjtex13ZpJCEFdeZLBiNWmrXa72XG9sktu5xz70fvv4bUGvGXwNkci+Y0qa\nTBxcaFs+RbtftUF4wdmMxTLBqnbq4VX1jkfIh2bsAW4skT5l3PoIaKKXNr5J\nLDDx0OFlQnbzA4wbRh9NfOm8MAUTgLGrPaxY0DyF6jTG29bewflux/PYkLgp\nE5lS0C6uJqbOt9pObwa0aRvszuCadjkzGaKchtVpHIZQAm1mmUHznf7v7VQz\nWZxU4++7qEu0fMEdNS1QgoxwglZ9Pp/1iTLJlkhxkgapYJKZWMlGQYDRW/Yi\nBwe/5/6KaSRtLimW4HkkDXl+rF4SiC+SUnC8fLCgMzWFvJmImACZ47Yrokr0\nXGuHi6XKWxawSbDmdfsN6cVjZOVusWJCkpThsoqr6+n70nm/mqmG10Y91O1/\nvNelOFnQqBdowm+ALlPuIa6Elp7fsPBFbTB8PlH7pfLoR8Y6PFRPqnvXgpTi\np4jOPCL5cHwaLpeU8E2yP4wOSVcbh8KvMHFXNaomfreBqCkB2nWv9wnrZnU0\n8QMRCsDRp6HERKzbjQJY6SNyB8M6QLrpzM3WB0byGL1UAzaHKVK69ldRNi/T\nzcYg\r\n=Rye8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-tabs", "version": "0.0.6", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-roving-focus": "0.0.6", "@radix-ui/react-use-callback-ref": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e963e789fb612c45dc4a29e0da1b74dc769967e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-PFkZIsmEQOZstWS10l7+IsrF4vLiWCdbTqBJQVw9zNXO2ayyXO+G5Q5Crs3gJAIcQRc4ZhmOiDPCpO3+eUqcIw==", "signatures": [{"sig": "MEUCIB3eBXyLMfEg5AWrYR+nHvUSmjvVd6udDo8iLE2evh3NAiEAl+l1EdEWEVPO3fC37Z2C+igJt64LEPD2/0X/WgfbKF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38547, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VbCRA9TVsSAnZWagAA6aEP/2c48EBss3MNlTcaz1Tg\nEFq7Y2Eq6VRqk7MWnwGp6lIxnFf9hLkLF/A+knR5DdvvNpxW1FufuIt8yyj2\nO+Q6McypUYJ/WR2Q5TfQLkaSnsSSn8vG+q2YWAopENwCEDrRhw66AzaLN2eO\n6Ib+0A8KJK2q+c4n3/P9V9EOvs4D0/Ly/KRi1ORF8dgoV2Hf4of16gglPDwc\nM7Qqt3Tf1ZVHA55pXBm3n01cY8ZU3PEMyONjWh70R2O4OJyaift4vZ5O3Lc7\nUVi5xSi5hQtqgW75hxxGRWbKcW9x55Cz2koZAN2QiSfgRsqTiReLzHFs9KYV\n1nb/4Itf7R5woclpYtOb5u5WWIqcB7CY12aJ1Xe+Unz85zZANfF8ebS2VTZZ\nBVosTxNrI5daGwU1QsTx9Xqx2FQZqV2nT/OFGo5cW8U9Fz+bIkwCah2P+Xr+\nOgSGttAIC+plht1S/6V9j0VGXqKD3hGQXEv5ZLYkAHyeXsr64z7yruoy456J\nbrJmI9lj+6ENKCYHgI5W5XwAvXB0ORwSdkaafdk79pcyV38NAhqiDu00W/Jp\nQvR5dGVESSTTq51oqlKvw5hTGvCgXS8GtrDk3Ft6wxm1ujAPbj7S1RktWY6u\nHSv42m4OlHInSnbBs5SkYpFEoH/3pDSW1rt8xaTfckri3KcSGlYyJfSioD9J\nUrQD\r\n=oM7M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-tabs", "version": "0.0.7", "dependencies": {"@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-roving-focus": "0.0.7", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4da474958fcb74513342b9655363597a96723969", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-IsOCckF4X2WEjyPAJEHzLDAXDMj23ObLuKS5Qzjr5O9aTAb04dh0HElqC4JYooIlgM0t9EGiWY91oaiVzu8h4w==", "signatures": [{"sig": "MEQCIFe0y+1j91Wjk9p5WRZF24ndWkeE7MXEvjHCsSnweX4oAiBzg3gzP7uSrcUdTW+MQ2uFtdZXIRcBFMTqjHxPfGePvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPFCRA9TVsSAnZWagAAZv8P/jR0ctjWAMKrC2qLMtP3\naRkQsjUS6fhg3aDMxJiyl2vMLVnuQznnXVsFGO0+zK7HFR5PkKa6U6l5Zfko\nUlUh+/BnCV+/Dc13CA04VGFJ3DIh+CyoLNNLv2ONA4txv6CL2qoshCIHPHR8\ni6G1RQTlhxF0L4Uc0TfQHL7qk25xOg+tfcs6SNbGfrVQanLelZRI5X6Ilnng\nGY35AT1fyzkP1+VSipEPIakgYyBZ6EA2IWdXa8yr0KOhdZwhiJd/np/raVKl\n71tj3bYADX0aunJ6WmD9He9wKgIBS0BBH1WorlcSn+aBRDbfP/OSz1WMj65t\nZZfc1MPJCuYpE8ijmlBKsnN3uBVdUL5j4Kqk5t3IFGv8CXUtehbmSqRiv/Iw\ntRfG9OjJUlBpj/DAUGs5WfJlp9ljVcoyInElo0A0+h4xVFxJtIyfWm9mc1O3\notSbomlh0MV9X/qFblL/DZEue3ZUIXJgeDsP4kCXr59NR3XEMtM1FxbTFXJ5\nun6XgQBLoSeDRi/EE7A3KkCtGYd35YjhG3kzizvibfcRjbIOYYq1uZw74N3l\nJ26gZuxSFp1UGQWtrlPT8tLwNzm7Yx+YtBlcGDEwJlwuASxbYVulC6nPR8I/\nE+DP49kAGcwEXRZbtGr56GPKRLIlElfCOoeHeAkEnQO4SJPXnKKF3oQnAMKp\nXt42\r\n=QU87\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-tabs", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-roving-focus": "0.0.8", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7bec143b6e056e616d1f5bb38abe643d5d1cac75", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-kF/PDiKNt3oxwUtuxRJ4M3lHNIDrEAScFkVw8DFs7laU9eYQpjTz3gsQLZKmFOPLF0C7jZm103i43Njm+UvohA==", "signatures": [{"sig": "MEYCIQDw914LODFaVBzJJ5F3GO/Mm3c0nDxKO0TyDSLzd6AUzAIhAKvyMf5LTHV7T7WphGm+Oe8XCdLrbgecbVKEj5MNOrn2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g6CRA9TVsSAnZWagAAdNcP/2w5M+VjaUYs/YqgffbQ\nefcL7FFdHCUM88f6eHeX0LjUYmN6vJ0kRDeNRHZ4RVdvxhY61BI4EJvqooeE\nKew3iVbcbupHikMw6pC59gKJP28TWGISGzKQtuoHx8rz+oZFpUpFDtnR8bAA\nJFRPqZF4xvaKzYUGfZhDHZ3btt0JAbC91O6Hc4XDAuR1G9SDgAQ9sFTWnvXH\n2dmJWMKxqHVRfAawbktkPs02XkiEDd2TipjqmNn8ZA4DB/FKrhvHMx8gqi2m\nyL5Db0spEh4xFzjFH00GD4NdNIcyUuXmXvJxe8qSibBpaYVvGeqD9hH/HTjc\n1qbxWiqZyvzVrT2KHVtM98FziWborBbR8/U1Zw5K0+oKAzjsISYo6m/3bDYy\nKlvYcwiRkGLZa/5AvD4YLzzBZY59ANWRBtzx0mrOallbIVPjmfChF+OwN4Jp\nvdMG2Ae+Cpv9rJx0jNGgqobAuZxhOPCx3agpO5bnwhn3cA24dJRCJQnNYDGt\nHBF+q/QQPDMup8R57BfDIacZwWOGeuLUKsJSV43n2UfyDgfi+Qx/aJ/tLroL\nlQxhlPsMoKcV+j8lWBboJIThhIFAmxMrcr9tO2zmjbVwDgVHx6bn9mjBc7rc\nG7hjVZF64u8qPU3s93xFjHYR8ITKI4mmdbKzR1sg4CWkY9yw4VFogPheY4p/\nnIAJ\r\n=mNh5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-tabs", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-roving-focus": "0.0.9", "@radix-ui/react-use-callback-ref": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dfbcfe82251bf5d96f3e6f560570adb8f234340f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-eXABQl5b47D8R+/R/2497oXRjO9RC0diAF1flw2YVjf+X8gbLbmwE1APPhPirgFnWzpr0A2oFXRaAcj1tFlfFQ==", "signatures": [{"sig": "MEQCIHEj+es+YcWV5r0hNmAZ/XoeE1v0cXFbJ4xwI0a8zRYnAiBruLrwDih/8kVzbXbMlHUvRyW5CrxdDypVDvtTgIEeeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IHCRA9TVsSAnZWagAAttUQAIoKNbcIuyvagQo9e6rU\nA33D3mHvWMA4gARxcpfBmT9jNqHYv3/z2tkQo4UeKRf4HdDNgjjG21VUitrt\nJ5E2fzPOgpbswRazLyLia3cFUEOyDl8AGfRU1u+MDFELTPQz8u5tPo2XSkCj\nChImqrDgPU++529CDHxhpCApvBOb4zySNesm3cnDpG14cPhhuYSr5ajLGmgm\nvk4RVD3o6Qxz4H2XTf/phX0HR6pKZPJwtABj/N4pyiuX5+ZqPaP8kcce8nH0\n9CL8VcmkiDZ0GcnhmbE1DfqMK6cyxw1e/blejD67cb2AWT9KvbwKk/qcRFou\nTD3Lyk0weOVxYmZWGByf05KpmYZ1gRe6I47ruftSGVZ3vQXJBenvW3Vcp6zB\niCLyl1RFZcBO0dIRT02SE8dEiRBxYjutsI+8Nbvj2UNVCCiXJzH+6eOuM5L1\nppZ6xm3gSEaJOk5+ZlYS6YMy0wqAdytIQ2bJMQpAyAg4DVFuZ1W9AqmvIVHV\nUWE8pFSKt6LnxCbuapUGRtVC2hxDnfUujEI2qmqibN0/VPcqwmJ7uy70IHIr\n4QjjcdgJvGOJRVwHH0fTSPOH9Rk6FU1vUmVaJS5dix4UR4gWAJr2oeP+iy+X\n/17XbW6FkTVWKLpFoqagNGRmY/Evq8XmRWpLmR+R/KZ5iSl7Ss6e2hRDEyeZ\ni6Gu\r\n=ZD4C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-tabs", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-roving-focus": "0.0.10", "@radix-ui/react-use-callback-ref": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "65bbc9b8e1c4e2084898f579b2a8bd9006e163fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-naD9o6dD+frKB0u7fs15D/iHeWX47xlMTPNcu+DdCMg8CVSZh/BUcqs/rPrVR8WH7mvA+mZnY9WRz/gZFNbONg==", "signatures": [{"sig": "MEUCIQC6THiLithn/9QbsfD42PKaPTSa7G14QJzkkySkLuINYQIgBoPQqbytMikr8IS1wZL5MzCe5E5TjwfLhLzT0G76pWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v+CRA9TVsSAnZWagAA95QP/0X+d4Fd9TMByL6OgZ20\nZHhBYD/0zpbAoLBygfuxxHeNb6Ck5zV38v/AA+l1qUy45HI8M+Wyt367bKP6\nIzFyzT7UdG7qGCovfXjm4x/lGHAxBanWJqyJrLYixdKSxLBVpvhUPEPxBvL5\n+Uwte3/VKLrqLg6Qli2osbrW4NrRcxOaBVOTcapKQzz5IvDbA8JAHXlNxezc\nEmjK2Rhan3D94Kzjjo73jxN2T4GtO2dDGSlPGS4eO17hrwfaNDgj2GgxwxAy\na7Bop50wHxOSjaDhwZhjiGRw/F2hMgh4Cto1cfpvGpykcFz61cs2Zx/wp0kx\ncNpqlXHDDNJAjFM7EpreFV1gUezFsz64B4BJEjgaHumRdqf4rTgtzt28j76+\nLVx60mmGkTmyafVbb/j2uEvEZ26AQhbNsyKismqo59ClvkGOjKqb95Vd1/ZU\nml4A/kciIju9ITrtAfpmFX8sXkqsP9uQ4HCXRkCIydMLGAOn/3pxNrIcTpPK\nG0uJOZg/MSDhOLcRiDcxKu0UkOG43eaeJUc2yuaA7ThtzWiKbIoAGsjMvfpK\n6hE2KLAn8nXVHuqyUUCnI3ggPb5RFjzTXXSye7nNqdeRQ3GAIwwb00jnQSY4\nPRo6HgWG29yvapvE5iSMZIudCglizPQH2y5hTDpT7SGe5YOfye7IaTEqif6Z\npOfO\r\n=0AIp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-tabs", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-roving-focus": "0.0.11", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0115ffde002cfb4cea0b65ee611f5067963148cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-7igaTrJrTEgpeltwu42afXd+cHwGpyAwjjSj6ZwHBUWTREe4e1sj5d14c4Aei6jr0OR04yDo9U0ISrSFNwYbgA==", "signatures": [{"sig": "MEYCIQDtVK4QpX9k009JbtnitwNG89U6Y/zfFKR/q2K9//C4GAIhANdXpemuW6WnFaXuRLfxhXISgyZgSCbbk4JRiZLULr9n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmxCRA9TVsSAnZWagAAfmEP/1yV3BrmujXL6QwmYyFX\nwxEBh3HLc+zUhaEhQgAn5bzbyY74jwanBOL3oN6BIDAuQtK3yqQ2DLP1WFd+\nVa7sN5/ZeXCTCv+YpkN238P9LuZTWr2a4eRGnwd6qD5GW8LIhRWRTSVKzpAY\njIjk9491OKiazLE1RWcd2Er0X3qxTwlu/zPCYZEp4W2O40tvSDudtkb6KZRF\nErJ6wEund3RcmQY2GIS+M9OfoIXV4SIdzKw/bILe6oW+gsdX5hqv0XStzIEJ\ndjJXcf5DClD+aNkB02XIgDMVb2WpYZSvEeNO5ECSZDFuROGnzhmqHIkbF2j5\nnfc/lFnEdBD6Vuks+YEc59iQ6kp6xWg4dH1d98DzMfEIjIeSPWOlXMECQTDm\n+quGjvI5XcYUeMsuQht7EvcWlcwveMT+K4ssBH3nveOfefb39gajeeH11icV\nO4tsRXOGkMHEaWfAd2MRSlATs5R1zPJX3k1dKxcvIuOGLnhuWSyrvL/5+8on\nGrc/rjAi2gGhZBNvKH+yesuccIV3Btxvs1gA5225JecbYGVP1Zw5EZFARArT\nHBEARF5vhBUEbjdjzp89hbVJ7A/DRggnfx7IfnwQxEC7hY12u684ZCHyJlBf\nbdUkMPs5IggeKRFgdaxE0EWbzy69NWX4S6f88/RGFCoGHF9Oz3ftTBHVgWJW\n/3/C\r\n=OiUh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-tabs", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-roving-focus": "0.0.12", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6cdc2c45a8e7a590f85fc423390972a32b6c02b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-xe1mh+wVZ7f1QcnjnsoUUjaDiFu+Mu7d7kUGveu9o42gi9tE0g4LmjkgkCn65pp2wDeccSAPuluifmLoUaK9JQ==", "signatures": [{"sig": "MEYCIQCIFeu8pOKqTeI3joMKHBBfFYspEwU8Jki1mRHp1V7ucgIhAMnz2/b6ZYCg6VyjCb+1OTeUwSwxXGTZDZhU/MOanrPK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7bCRA9TVsSAnZWagAApMAQAJDVwIgFBN++CDc3X2ON\n2I2U+aSzVu/y0gkqUkVrxRdJhL5DlYZlrsey4Qs91zCGFsViZ/rl1sTSeZt3\nrq5Kj7HwoMOsR8bTFCukXGx4bhDXqOi5hMZfmgEe+Y6AexWfrdib9H2WSc54\nBP2F31O0fCBJXZShd2f842vGbLb/ylUZW3jt7+STrhc6uxnvYEwlug5c2O3Y\nsT80ObXqtqKuaj0A3ph00ZBLJZimK18LE9pjKfdslK0zJ5GDoNFU7v069J4D\nSMTJ9C3H9wHiUGzhg+bxIgqmxW44G0a0mISgcrTY9uMIDo30WyppjytJFJoj\nitIIdW5QOR/BOLclf9EU3ilyp0kgeB/EKB1A2oaD/WlG+qEbH81/bAfSiYK1\nFrz3LOMkXyaEG8oWM9vkC1VYYhjMKRVIc+LH+TtRZ13nooaRgCHS6lh+bC6p\no2824DaLCYeVgXFQIrpedCyPifRnIuO8Cni5+45hKlzAuSZTQwJlHkMZLAX8\nhT8U+dnQNfhsAE0pPT0j0gzgP6SXSi1FATwCDXOu8Gl+i/xDodrOtNXpVCkv\nNhbWI6RuuY6/m7ekxUqconr/9iCQu8vxF3+tMT1uU6mlwVs3sy1EWeHs7p2p\nDkXfNejXC74fpVbI7mHDPqoEJLF5xZabkOkK8Tmo/43RYuplxrlq6GONSs2z\nhv65\r\n=gw5J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-tabs", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-roving-focus": "0.0.13", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ec14281a864a00de96756e3a9c15247bccfb783e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-zSa+qRRvSIyw5brgRMujG1/bjJFDvWyWfdlNTyB44JlGEJSjDm35ZsL0NlHubDSeOrAazDWrkzIhp+p9ZHPhhw==", "signatures": [{"sig": "MEUCIQDICIjpuKaej23PghE2MSRqpIe/iGTaBRTuvpClfMftVQIgMgrwkYiFHLcpGOlAO6Jakn6ca7+kg/HXs3JncRXxwi8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34814, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYICRA9TVsSAnZWagAAVKgQAIGmRv1sQYhp0XXQV+LA\nw6/RWuLNuseoYsxdcdSEcR9qiMZLcNkjjlYV1lRJO3GcW+Dt08lE9luFGm7J\ntJlnWfyhKlaX58gm0yy2q6LwHrN/Q9bSDdbh3iwpRVp3TI0NRJfYbHaEwdaF\n7SsqD9vxuGOZfKiyMWTxkBTwnXhhFX4i4xaCxXDnOJ5/z+xmN1riGTb6CCWA\n7azA6Is1a1tIQUH/0249i/iZ/NqDmuAKW366OrUwLiZbvMqfZIJp1KQZrbVL\nijMRURkSd2yw91WuKlZXMANSiIFdnfGmV3npPQDl9AGlMi8n7rac/0rEIOr4\nRR5Eaw3x/XFm5shOgmVSv1wLbdldPOmidzszdHmcFpoGjsvM5ztqQO1Gkmto\nXlHOQjKujWwvdy04fuErr29sEmQ4ArtT2VuEAfOt1IuIU9KL4h3xWtc4xqwE\nib8e2DneaqA5X2zSaOd/qyJT3DDFSPhYn0zTUEdzGS8bVfXcoNfLB5mAWwqO\nqIK8aTaepzyq4gUg6MAjm9yuMmnYj0VyqndD3mUYv5wyJ/g7PgCAaHeQ+Ptc\nYzOuX2TufBortVHHJd658y5yOL+nXGi1tBy1xLqYM7jmjp9+h9CTtZyhCcqK\nTLhDpRneftOHClp1WspDfYu9oS21JI1r2muDRZ2yFDn1wroiJLsLui8BjJws\npGPi\r\n=0ism\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-tabs", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-roving-focus": "0.0.14", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0916d39d62fff028764cca37105fb5398aa49f84", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-TYc6ArLdKv5ZP8ZY8m6swsviqybZZSbVz5BUbC7etYfbTfBtLfZsVU07mKzTDvrw5B5GcZ+ngT+iQpqpnmd0Bg==", "signatures": [{"sig": "MEQCIEKA9HuAjuUy1eIcpXLMjFSM3Ju+gRIwkEoSwbBVv5K1AiA6gTU7aq1MHYLuAeNmTVUIFFrlUteuv855AGFiUt5qyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ94CRA9TVsSAnZWagAARkAP/RfW/V+553jpDU5lw7tQ\nO/6QpdWp8k5H8Fn0Es7cMTzXPv2LAFavsmigWInqFw+dA8qSNSiHr8fc1mK/\nNUpXPpG/ZFNv/N62ju+ouc2Itf0LH/J1LmCJfr5YJ41wHjQlYRhXiGQ9oYnw\nEK8GsaM+rHaXpUmXlQ6W7j8+zEbYShUbk1Atl+zwWOQFXbX9R3XIRNAWhrRV\nCe8t8HvoN2QDQRipWiT3daQA2/INIYx+IhDybYIs5fUY753hDN6DGwdyXdmX\ngO/Qydl5aGKD7LS20QHTLGwjUlbEkv5fx0A5hoMdMCvSRcyE7MssyLSjsN3R\nWTHlL8x+RV7o4lTGTlhompeVNvFdze8gVe3o6Gz9SW2u+U7UWlDUnjBCkQ7u\nWtlfA9h826ipzuXRP5MemvEep3QzAv0yMKw7fqc9922yutNVpA205frLbrNi\n9wEuncVepNE/G76kLSYLtm79GzRM7d+odrXXV3qvUo6iwlbed5fYgveA32RN\nUV8ZfbuhEkjnAAGtnejR4l8lcDQFqxYDDwmQZrZh3ecwpiVzpveoBhIG/lXR\nZ+UbQzlBe7rSGZBEj89s4oVpRlLWo2u/aPlbuAhGGCp8RwMUX0cVsMEGAyat\n12wOgrvYAYq3oht4fpRucSEvZkRpDt1w/wBYq7D7K5XQjYyyLo/q95+E9FOp\nsTvG\r\n=du+5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-tabs", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-roving-focus": "0.0.15", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2559791e4faaae922cd4422246b93c96c582d93c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-r3UL0QvZwp5ThljisjVyZpxJehzS1Jmt4VBa/4eR42swLFwez3qqeKLD/6XkNfe+GfF+NopPUrPaRZCJrpRNGA==", "signatures": [{"sig": "MEUCIC07Y3Q6pvZOMC4Duo/4kX1AgfLJxOWn9V1Sn9xLnuytAiEAmlQciYWJLrHW/d3y5xRQ0uhBkWf5As3qMj+EGfc1UD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GU8CRA9TVsSAnZWagAA2gAP/jcLVG8hvimURtbPRyNJ\n78O9Ld220g5oEwo84XeWvZbwUOheWspMxZuncVtYRkbfZ+1tVwlfrIBuQUxD\nmV95jzx80zp9D97SJtLXfs/vd8sVyH4Csh9RJyPd6aAiW3Os2rP1Q92g7dbI\n46sol1HMoya80U6s2n54v/LyYCsjwxCxn2dTFYeTcjOxXOr7JhWvYui7SoPf\nFEW8Z4O+yUIT6FJ0OEsWcXNdTkcr1mMhpsh/f9yMgUu1zGQns7kbGf2uaA5o\n2zfqba/6QzDdF4PLYWKOUIrgoQ2cLAhhC3LlnXK3ob7NM4BXddKOE9zbTZC/\nlfSvaHlmSo1MiC7pgevN5mK1CGgDIHvAhqXkWb/yzby8Skc8jWGKKMSeFcSE\n+CK8LNxreUAc3g/Lv3wLg/bTI6RIlu3ZA1p0QyXeLCdMAqCFv24WI/3CaO/g\n6O+XCeSSfg9hWnl/fzrF7AHC/W0qoMndfd3Q/YwNuJI6XIhYxyB82sz8ApZe\nZyMHdWHSRIylSccGyo9zKH8iRi0YU4nqbdzu+hvXKRaskyhN4SRcH+KVai+p\n/Acf4kY5oysiG/VEuuLNoe+9S0hGfmPUr5wECqOhYhVihyWFINIqduxdLyYP\nijWG9vA3yeAU54VL2nl44Tkr5knGXRzOzCacb9i6s1q0Azb9TiUJ53RfGibw\nsV3T\r\n=DDV4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-tabs", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-roving-focus": "0.0.16", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e9c2ca83f5d0cc457a8c1819d430ba867bec1949", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-cE9O6PRH9sIB8CFql+iew0yO/w6ayYCaCxsT/NVWo080c2USfdaBwvSkIFNT01LAmBijCgkQ0gJAO6groWuz3w==", "signatures": [{"sig": "MEYCIQDDfu8Evri43+Zz7NIbbHOkTCV0z1whUilRbHbjkkrAhAIhANgdkRY1sZsQpjgxVYa0RfOJNr2B1TBrq5YNp9tcVT04", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTyCRA9TVsSAnZWagAAjDgP/A+VwWP6+M1qgGNV7Hna\nxGnGl4WEreOAehGZvD64pXpikIDRmiyPMRbLTV+VSR7x2XhJxb110XqW1kqa\nexPDL2wDhDFKJzJG9vp3JRbyqeSfbEnp2DqEHnZRZOVuR1VVifYtReBWt55P\nWZYWCiiSkN7p/LvYt55ZnZtWYhJE0ucPY/R6sOSFD8+RmXdXrunaX+e3fvVj\nxwvnOtXq5JfiQu4u6X3zmAwhMmOAvSdCmg2SMnh01nmmvNNjaJXyvj8NX+7Y\nB1yjLUdKF3PKXXYAtu4Ojcd0W4zVsqSSJNIIBu2dWfb0052NlBmdla/X7Kwf\nQpVjdtUpm8+f/pxdK96sZzmmYh552Rg0EbgWwyZmiMmDEhHD20TAng44OZdq\nADUU9GqjChzWe4SobbwXKE4GJ+icicnWNupgSv8ZGL7rAnYf8eRGaAy23AlF\nnvtiFrA7wf34sJ0iZfl54W4tVXIRtwWtGB9TxzRZ6xc79oKontU/gJzZPTMm\nZsrO0V6+TctOGpFLRQv7/yOTREHvSdAqt4OOepCDsAhfwMwPanivNoaagUpI\nEIsbLAhqNPzuYZ7rpLb+zUpTt1nfNHA4WPNdmqpUVXkNumxC6z3YLwXur77D\n8n2gGe/XoWgSieGwPMWjAH122gV0sxu+08XFvMTHU/Xkl+gwNy6/cs2/pCzL\nAMGC\r\n=GQ5M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.1", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-roving-focus": "0.1.0-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cc9cf5588dfb6ec7d66c48f277232cbd4385bbe3", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-21pyIxjZtffHwZHoxRzHfZsehTAQesS6K+Os53Lu9AmttCVACmRRhjLgAXNjq0NFnJwdpNa13shx/ollQxUAog==", "signatures": [{"sig": "MEUCIQCnoyDCbhqbmYfuphQJtgrSyfFQwJFGmevjHsrg2dxYywIgWCGNSv5Oi0ZooGcSDeSdBjjLE6IkFK9X2m5dBheE/as=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgptCRA9TVsSAnZWagAAyvsP/1K3T16xQ9hlF9dXFQ4b\nauyZ53Dm3D+Hr4W+EBRv6RWAF9zw8Nj5l3sjkuCvunImyBeWMEuIsly4ZhZ8\ntqIrCyhsJb6L3pVzhNx8yxLtunEXOC2VXkoBngrhcSriibkITnziBtMi/l/C\neotkQmJoaIrzgLDPjJNxWZ5FLu+o+cvW5HPNZx8ioe0/eHZ0KA/v/GRcXfG2\nlp0sPwa5Q4K5JxdK2pTdVWMlbIMPvBnkwTAsrngfueIHn1zc+/9VfWBBkP9n\nr6EE2bvFBHrcd5kv3oNtf/LHBTakXcVpAm9ZvkhTPCwjtyKdr95vVcr90hlZ\nBBwPjEf46aSlzpq/MYgMRAACMtbXBIakczsbNnlPGZ3jq7qtydUfBDmsKqml\nnygA6JsNQV75j0HDITRML0CFzznuUY4vED2aiW1nqBFdY0FBRGT2+vAzhEzG\n771lsSSJNUZxNBbwvPWhEV506+EPKoars81Al3r3P8BCST101gPuOfTnTX/x\nwt1pavTT14O/yYgyxZfI9Sztmey26NEmgOk0T6T8kjZqv9X+lTUg5xgg4i3W\nlzk+PX4KLofOIFgrGPzeHQ1yXh6QcJdMMstxVD+cH5vLc3kKWQSXtCgNwCol\nOsSmUgT/iAPvcai5KWwbc5PY0KzyZywu99GBkr0HdD9+hSncPlJZghIrTwMk\nql+A\r\n=noMN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.2", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-roving-focus": "0.1.0-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "266bb2a7a5dc1f6d072c0f8eb8c023e0cfc92e63", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-2BRYd1GoeQlZidpGTnSA9xwJubYJzqBRLlcctq4bM11biJc0nJFKIQS6YI5bNETBvD7HkMWZmsqQBnazqIKhdw==", "signatures": [{"sig": "MEYCIQCE9KNaVIzbnzN5Ei4up+RW+1/uMZ4dAsVTlZkO7F8MJQIhAO2scnBb1j9t2s9MH5tzXKAMefHHrz/zJzy0NGyhLOQH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhydCRA9TVsSAnZWagAAOMMQAJbiiAfqxbqDcoTnm10K\n6eHWanBs5to9SdA9G6SKcTihYYDUIR3ZdMiZYEb2TaCYEjLGBqYsBkr1ajL+\n3prpu3LBZd6AConQw010N/3u5QhbLRtegq9PNhJHLsW+STB70Czz64nz1YfE\nB61hrm/GUQFLBVMTL+hCitSrMqjCO+vynPgUGsOU2hFEeU4TtPykh5VwYtw6\nz/etBihi/FdZ64ZEIQyCRQdKxxlvJPa+fTJyRfEaaGjK47Un9xhRYrqwKeTK\nSERgUMxxqUDe7zQZ3f/uNCBESpkpCAPHSxCsCAAyBgU8AW1myxaAUVW29rEy\nHnTbZsG2MrQ3NIthr7y3nvtlxqyQxvWxkG7t92tcbzvz+OyhfgH5mqq18enV\nsFCFsA5mCBiIho3+exnn32q4y/bkPFuMXq99b01ABCS8H1Nm4f4IfUSSjQ3u\nqWYh8faq9S+dYCccHOtxDVh1BDNKui8zvemNUiqpDXGaTDPGD5q//VEfJ7Io\nMB33b/CgoNmEdqOYAG6vNd27tzMktYcPLcToAnzqZq26qU8zvccwj/cEZUV1\nhg57XVNO4mDgYv6FGAi1Tf6OnnQ/R73IgYadGdVQitSiQAOexA8mTlYnC+ys\nTwBlGUUbvuq4Rut1vMzODLJJb+FDmF9QIg0Wq9dGT4rifcMzY3x519L/d9gq\naojH\r\n=yy2B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-tabs", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-roving-focus": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b0a4a78d3d8977ca2771e63013bac452c838cf9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-dkXAH1QlEcodcGlhMQHFV8bE3+2RgOrS7e8TLzffjReXMJ/D7UF9xAxVHK0xShZdXzFJrKVg9iDFelcWMiDc7A==", "signatures": [{"sig": "MEUCIQCUO61rT9142xnGPDIjQdombkeONmVCxHjRveYv5su3NwIgOwRUiMOAXXYiSJW/Ya0EIlEwyAM/3BI/6ngElYk3++A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmqCRA9TVsSAnZWagAA5KcP/jaH9igqiH2QEtYfBvch\nV1eSGBPZE1RH9P+MqQ9jWxhm/LwCNe6FvjxWf4iAW8QFBujhIRfN2+f2/tIs\nwYr0Z56EXumGbhHpkBMyepaw8Fe7hCQtT3AgFtRCOdhc8L2wWp0sGvedlU8+\nDgHv8cICiwYlk3bb5nYPUqMG3WiQi4ik8+T1mluUQAGBXmAg6wbHE8q3p4wF\nlmhJrRk0cgZySUzJwnq1ZvoLjw8NhvcTJa/0cX2dsM7g+OvbjzB2fMgmamxK\n04OXRWsK/3qyN5OfjjjMm2WZdI0+UtncJqcPOiSj1mnIWi4QEoY2xIEwMY7j\nuP8MoSPaGHHEVHvNtrJHX1raEh1+N8tUDZE98Z1Ll4XoF76UmEr9NQgeWZUt\nMDsEhLNtsEwGE1FmpEW7jZQ8tbKLBt9Khb8LxJeugz5kQt+C8fHdPvxgy9P+\nGp6KqibOffzZiTTa5ArULMQTiA4jJkyUVuuK4VhJnvnOOhb1sTVCHGwdtwPx\n6GChXTh/pTgXcT8TS2OierowiE7A2S5rS4qkGf6nP5FO+KAxir2iPweI+jR7\niF70iBosixsRHJqFEi5xpxM8Uqh1xcWWgng2TxS+JUKsOw1CKZG4etzK0KNy\nT8dJ5a1BZr79LMkIaE97A5FGG5lBj/6siOBOPEPrrkbR+vOy2tTyQmnQ7q+g\nWrIj\r\n=OrAd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-roving-focus": "0.1.1-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0905a56d73a32f324bb09588591749cd533e8ab2", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-uzBokhZ7SFX0hhP8eraiFP7ny7Esm0ZXTrXjMZxDuBuuPdbUKNyoqdAnpQqoTN8yDCKLCj0go7M8SHEdvaHMXg==", "signatures": [{"sig": "MEQCIGuDy8mbnA68t/W8x/jUkJIRMR41O5erlMi6lkK5uagsAiBuf4m+HiX4UrCJwEwSjORkS6BUnplPSIIrHceztgWWkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIn6CRA9TVsSAnZWagAAvXEP/AxKNI9dEJHh936HTsjJ\nbzNFnuTg82qQX8EFzl2KR/1e8WQUc/xSTXRu3Vutmb78Hy7BYPqfjDFtTRD6\nNG1rwRMK6F3hHSGPX6fMiRknRy61dUInU0XMxOjKZ4jdyoP/40SAEM/2KvwS\nc0S+RUmbvSNGOnRKopXjScEs3HygmWS2+6H7nEQEgmbykjArC08IuypivghW\n61BJKg2GePIeBahKGF8orYwQ/7xMw9nUNlw4bGLnXT+1m2jj/WW7ODpE6jt1\nOdXu5AarjpU/e6E2waTpZX1mdiKStl46Z5YBakyefu0onlEjkSHc6er8B+qM\nC290uoPuhG+Sba8wMX6thIlHgEUzkVY7A42uETHdBdXIjurg1T7LBeYZSd9d\nD7B7md+xQUjxFOaeUVHzdcmhzpxiWxg/0KA9bm875EzvJyyZtdJaEAdjkHNU\nE39ERMqGKr7Fmi5H/syfP4ug70hd+65k7OwVlpRqhsMrIuvf4TEhNlZQ10XC\ncoyypbM3CoXwYR2liTxzC8Cb359kDUGTbDSYgWri0+jMX7ZxJNro2RNgJX0N\nRGAUvy29FdPzME3N+VrpweLP82oHmJo4lTE71I3YNc5v/OrhffnarLv22ROL\nscp4CF38HXeV2mKtXWeNwXPTm1+gOSMeGGeq/vhdZP8DLsolHnW3bXYp0Rd3\nN7By\r\n=hboV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-roving-focus": "0.1.1-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "398c7c5c6643657937038e2eb29e10c69ce0bcfa", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AcODibgPCUJXmNpx/Oajdx6NOkxKntsAXoWembHsl04rO0pfa05HLetpEnq5bxY16WcQ4ErLoQKR5FUQrx3tSw==", "signatures": [{"sig": "MEUCIQCp4oIazmGnMwhSzP4LOnR3xZAPTmXeIpbp4yiZp5KNBgIgZtZqjvzXjYAcJ3WGx77pgvrggfTMJLnrXAH6phIbZ8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwoCRA9TVsSAnZWagAAJy4P/R6Uix8RxLd6AZYxNg/z\nniUKMrNppIzLsmv+V5w7KelAHQaWKyD1YdSvPM8eLmRfgqnC771l7KG8j8RH\n4NzFZyNR73cYOoUzX9PDgLACuzlBJQHCal6f4xNC3rPEpARNJNKmu7m9UNwJ\n9U/11m32lP1JJEH/kvgyhBB09sFZ3Fi5NaRwzw49jAvb+oiACqrFkIyeAckI\nYoV/oczSkctNOdw/ix2mGHfnYff1Ki/RE1bOEtyeAj8obU+v+gZ9XZPc0BAX\nI/4+6HimI7e8QBZwbvdHTM1n+4W+v/MibS/SVYvjzueQPjbVtooQfZCOIDlq\nFn/iVfzgkO2nZflTwifz0nPqJnznJIC3/3oUj1Cs2g9KQUiRqCGEyDKRWVc/\niCsLzEUr6guZFKWD08nszvqrNTWw9KjBbd5e6mCPEkS6gHPb4u5YX/RSO1A7\naxR29UHvSe/l7HSBWZRIMlC0MYJkLyWMVB8OQocaRwDm+eR77sOeBGepWb+t\nt0cNfHOkBwsSOx3aBm1OiOj+nng1QYlM5rpZzHocuK3x8qrKQqaOXyLLSfm4\n9KEuT0hSlUWd/6GKXnYdcPj6lHzShPIRselsE4EKmkW7DIdnl+qiXDeXl07J\nsYPJvVzJoRGsc0WyL+SD0Nr1hrTo/hV8XvBLn/Z3egMVxSatJ6HiL/IBCURH\nPRSz\r\n=zWsf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-roving-focus": "0.1.1-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bcfba4969149e2d85845b34e70b254facda02a59", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-LqUwiZWLUNw3P/11qh3wcwB2lCwmPQtGQamkWzoi8Gx8Ma8NUeTpacfz2kGVYhyUTMAjwAiKrTZeE2w+CQXbFw==", "signatures": [{"sig": "MEQCIAVkWy12/PqhWurch3KRL4JjN4sWnH+spUhTxsJ/Fv4dAiAFFFW2cyoGVGdXQZ5N7NA0QFKk/D8P91kW5cVf8B/MjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UoCRA9TVsSAnZWagAA3QMQAJnRNkUkne/VZ0JVpFWi\nNr2nTFXV/Gvw9FmGWYd9JVOmve8/GLPMqzlDFvkL51BD3TZHx2JfspEPoMie\n5KVNqkXzHEv11RkM6Z8vj/E5zuYuH2tXFCHyFVvWYwDAwMfk86ihwI80nnZP\nkSsZMD8D59T+SztBnXpO5qgPpNDHXH1Bqcn/ZejEqVzJp2ucev/lIuHOabXy\ng4KrYGu+vY7uVbzoR0AR8yU6rhBe7FyGxOiYpjdLn1YoWZhAMaM5Te56Y6qe\nioiFu/ce70suEcGJKPJtOiXNctSPTVDa7O/E3/R3GcTJZ0lxxafsSPFz1qaG\n9sVl3er9dXIUlnO3rPOxlNAo3//dVfOBfhby66XUArBCo4oHhlAcmIAZpCoN\nb6blngUDE3+MC0ZGbkBaRkhL8ngz9iNR06uMHdHP3zFGQdHTzzDEYc7QS1tz\n0AxIwhNdSvOnu9aM9O0PJFGTYDLo9qpE0tYglhFFf0DBFwAT23+zZHppQxQI\nRD06X17ChTbRuOOzA/Fu0wENlAJ5PZzlP/16tcxESIBAOpKOjIgW7ozA3Cmk\nkJTN90IDxXfeewumG+ZZnSmTtEpIZxrArI1ywcB9XtPfHEe9lLQknUbZ5CoL\nVVjrZGs4kQ2MW0yieI87ULcPIjh9WXF00Ss1ucgy8/HbHQMCLiT+X3yEhCFQ\nCYTk\r\n=p+2A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-roving-focus": "0.1.1-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5576a89a9cc472ef7e1af62495924868e74efc5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-bg1RrlT0Eg5HiquFfOE5maYy+XtFKyYMOKs2gSIEQI5TEZQbwPw+tkZnWx8M/+KNi8XZMPP+HSoKz/bydg9Pyg==", "signatures": [{"sig": "MEYCIQClH28TAmJ1AMjBHEu2ghsVe3DLT0hRWt+rDbc7GBVUvAIhALJSxqoghI/RtF4jwke9XxoianoEm9D3TziKRYVdONi+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10gCRA9TVsSAnZWagAAmsIQAIGOvYwVXLJr6ywgyzPb\nLQGd9J8kYlh4egpq2/liwTmky8jpB8Db80GeuJe+Mu7ZlotIzbaq48D2KYOS\nDo4ZnjvvFGPleGRCgeR0EuKX7Yh2XoIN4/p/2+b7Xr3mpC0P4cEsKcGGYWGi\nzdpD5ut6ckQq2hY958HdsJ0CgtR9zf9WCrZh+2mMBT2a0x34kUeui1zI7zmI\n6pDOCLA1CqSpaZVxiBlLbDaFxcRBZsbaOp1k1e/CVYRIWCLWXEjC8Urxqfb/\nFD28N/Nq/LmB3VwIgrSV4L5dqLm252ADiRlnAatGssPL2ODXZhbCljKoC2hO\nPApJQk0KjKKlXivmOTzY+pvPM4+Y/latLlNHDHzkcG+Fwp/LB2VmJ8to1h/i\nwVzg4qG7pUT3nwQxksNo8SbA3s26IlpLkuQ2WIFz3ewwk+Tm0oO4Sdb+Kohq\n4GCRJUl7Uj88k2rEUV3/8qfeIEW9FsTI+YUdZSzxCMTy4oXmLCYCw+V+UDXC\nbttrPR+SOL27+5mYKTbGnvVuhgoBaSddfN0Xgaa8rqtzxITXlDi0uYhH2MNy\n8l2FRbduPtkEeUY1pjK98+5NgURUX40B1GoTl/1C/0RW5t3x7RP5mFRIupKb\nKb47xl5pzVAEWo2hnpL96J0zF1MqmhNe9rvxsUPS9AR1jkDjTOPNMT5oI3+Y\ntDry\r\n=q4CC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-roving-focus": "0.1.1-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7aa51453f0dde121391a301892b1e626be640534", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-/0hqCQOEGMKpqOXbSaTM+h0jMadi/HMvTA55bauC/ouBxUJBu6IZlL3T1+1tqJMSOtv5tCxSeChlsDsla1oppw==", "signatures": [{"sig": "MEYCIQD8MV3oZyFKPl8niDAQ4/juuAX3f1NwQwNO3zZx2IY4zAIhAJa3kea8ilJ4x9ry0zTE4NmPP1L519gPLMaA0uiuUyJF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33622, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFk2CRA9TVsSAnZWagAAUCkP/jfIt9OQpPqNSX+wFe5g\nPw3ATvDEYxYUyCBtLaMWdbYa/la+44sh81PZscYtkD+Pmu3obOvBplw65MEW\nxaKJGj23r0JVT10X6K/+a8XG2sP9ugZrEToVJaV5UDUvWW8QDArVEZ7iZvyO\nrQr3eSiD23By6b0HtY5wDPAp5DVFNLVW+SeaPqoihwr5S5OuNoRyvgLXmeNN\nFZ0xS3kkyqjOmFJ5sZ9sNJg9z18X015ovAPkjOFvYiPMpFrG/GHaWyr1Wkh3\njUuFsg0qyQo5s8bPm5tjg4uE5Q1oXC0BKdG0pTeqPDgfCuEp9MCgKAr0gujl\nTKUIiJNfxwFRla85bxQXUkzmssWrpQa5xOYBJsE7j8D5Oh17m6Dk/qX3im8V\nbe7DNsaKtppeGmHP9WPtrWIUKtnFR0fA7PugJivS4KEgX9IAJHrmMkI2R971\nusWvickmde0JSxO/f9TZe/sxSsNe4siakrxbX40aPIq4Jvq5mTMRb37AclQN\neXYhPU9lvdE2RMJs00A2rnIoiUutg21ak6H3FPjjQ3eBZC/Qh8ipjbiE8HB7\nNo1U/ofq5YNhhqY4jqbrVhVohR6JqGkzRTsM52LKWvTZfKkPQsD6IbXiYxt+\nbc0W5tL6l7VfLKqh4IncbksSATQ7EAJFQ7kihRTJ+XZyoUrMK1TonTXnhuam\nT8eV\r\n=efSU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-roving-focus": "0.1.1-rc.6", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9d4f3289eb89e250c14d5065cb98c84d40a7936b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-uBLaPvZB71BSqF8riSPOhRP8lxQ3x5plzYc4Ev2RWiXBRN1HYWyeSHUY08XuGkCThb2ABIuWAHJ0TtNwB2jNdA==", "signatures": [{"sig": "MEQCIFTKDcJykVtjaEEhovaETYA8/xj8hFDuMQr2JJmSMkHyAiAgxbH1u9gb0ZOROZK9sZ9r0qq9fG3J7Eqd7VCwriC5yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33864}}, "0.1.1-rc.7": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-roving-focus": "0.1.1-rc.7", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c8f2ee24b86bd6da7d3516852de52b2c809464f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-rfzwdnvUCTCtXNBcV0CAsEfshYR/lTHom3YBSZh9DdATk+dMqrGhFizTCCP3cre1hfpBHIiaDAA7oGcLWeso0g==", "signatures": [{"sig": "MEYCIQDRRcqql5QmdLWnG8Hgl6H0uzED2Tig2fR5Sele5POzuwIhAL8fVK2+/wPjAS+3EHViv7P3OLhpUmbBkabeHQOHZWMC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33864}}, "0.1.1-rc.8": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-roving-focus": "0.1.1-rc.8", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06b9e7f8de0211f5dacc6686b7ac6adf5d3c1d4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-M5RCk8ciK8r4AuN7Ca1oSTs9GNSlKl47YFbf3zXyYozwPfzlQXR8AA4RpcfjtIv+a2kLx97P/P0HYmCUAEVJQA==", "signatures": [{"sig": "MEUCIAS8wvVmooB6NLgcxlGTfLBhoDPuhv+3z+/VXAHvz+Z3AiEA3QUXB/3T4Oa6S5nK/iZYpJkSrh0uQRrdtbr9Pi0Cp9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33864}}, "0.1.1-rc.9": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-roving-focus": "0.1.1-rc.9", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1c02358c3a22e555b85c7b904f71b2af9a0f06f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-tKXVB5hT5TNWxC66KTqJataqarz0y5i1ystiSHzYQE/mbqhRNKMvUbTVTZNX5eR+86wx2OhJISOwQ2heigWnrg==", "signatures": [{"sig": "MEQCID+cAADk4G9IonqYE50OMUPUrbEKgVKLQLtbSc6V6Ak0AiB8Hlw4OBW7AIcwYFzcT2PJjU0zQZv/ulIjC0OV4aiETg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33864}}, "0.1.1-rc.10": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-roving-focus": "0.1.1-rc.10", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3f41c34245a4069b6fc58c81675154ce014f1df7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-O5LjFbiR42t7tBhAMfLY+0DP00th5Zsyb6nzfZQ5UURjhQNuFeYugDq9+p8n8EfSqrXC/4wh1ShYFgNksuDBGg==", "signatures": [{"sig": "MEQCIGgTrxikvgpVtjaVe+5F+lVOPDDNx4ySj+UUVv1nHDzSAiBxAbGZz5erFAYDfg1SS7CTzKiA+u1a+r89D/EoPSkXWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.11": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-roving-focus": "0.1.1-rc.11", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "be8276bd41e41a49b7db4b80b6d5c0fe06e70f22", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ySh2sRpSQlS4hwqYe6KWM88gZuJLA1yo/AYUTXFMz6AZSiF8UhfVSJTL5f1cQN1zzSoyttV27N4P0Vmm9dmIBg==", "signatures": [{"sig": "MEUCIQCk4TNAu0lJJndTRk6FdUFFu3AKttdI6y/1iHWOoTI7RQIgBB1eFVC31ZoJ5+CvRKegrzTAkJTQDdXRlhLHiNb0sow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.12": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-roving-focus": "0.1.1-rc.12", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b2066f10991bb5ae85cd5d3edbb9006dfea87e14", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-2TBfjG1eXKoPDUh5sPz9MuK+6FqRXXaGAaGnA6aEQ/9LR64NsUBnI8dqvghydaE8e1bx8rdyo6vmccI8EEWyow==", "signatures": [{"sig": "MEUCIQCKIvj6ilAACyZk8h7xi9oZQZ+avFT7XE1VUwyhgvXQJQIgBzq5gJSZ0v+M53+U5DpeJ7uaOyXATi3/tJjB/yfsKog=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.13": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-roving-focus": "0.1.1-rc.13", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "945fd3167eee4131a452a758978918cfb5d81852", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-ujGtQ4kTOAicgTAqepEuTQtygWuoqYbtzcamBrVdK7UvT+KAvl5pQqY8wCCkb0ZeNZFs9/Ek4zBTRsEzpe3hNw==", "signatures": [{"sig": "MEYCIQCOngDOT3pEKcLVTSGi3E1g8JdHkM6CcH3nWDggTLgasgIhAJS71/IFLVrfaB03YZBy3/phkL/TVG/GEXX/oqPIVXeR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.14": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-roving-focus": "0.1.1-rc.14", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6de2d59ef537fe139a16b3a8205eb220cfd7dc07", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-kyMcvtzh8OrkKIQ5nnWMs2Awc8JIJ2m2aTl5ROooEfmvLIa+J70KwIVheCMa1/1APWvw9PjpT/+gnqBuz6qedw==", "signatures": [{"sig": "MEQCIAcwVqB9nmtlmx8B0xXE16e8qMNRXpKN1UAFhZj2t0wiAiB+SQdxy11u168L8sCtMP5YzpHwSMF13E516uPyEVkfRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.15": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-roving-focus": "0.1.1-rc.15", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f03226100c3c33bf66e81cd9fc4d95e3b20e198f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-b/Dy/62NhWoWI3tdXIYGl/dLI4EvrrfbuXL1XH7CH5ZzfN0wLAKGdlh/HIeTyvAzGmbODtRkySPBL/i22t7ylg==", "signatures": [{"sig": "MEUCIC+LG6LJE3x6uxDK0AdRLwEVXXtMz4xGivWd5ZA0QzyOAiEAwDgmbHRQlfeGSvr//npZ/qT+afLYdUDuM2LiS2eySls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.16": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-roving-focus": "0.1.1-rc.16", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d3c71e602f7139defe525a3188d84cbeb588cdfc", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-sb8of9pwC8Km3Mowa0B6LgWUpDcBy4mNLXV8ASvTzRjm8HwJ6eu4xCqFkkULGAl16wzXUArXZ9seRxY3UPA0XQ==", "signatures": [{"sig": "MEUCIQC06TnTYrHWdIj60ozpFTc0EngTMILy0995oeu1j84IaAIgDN75YXRZnB1KoC8iUAD5NrMa7rbxRqI1BvElALW73wM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33867}}, "0.1.1-rc.17": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-roving-focus": "0.1.1-rc.17", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "570097a3b7df78598a94a38c6e46ad05d8db64ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-t1NrUVnbllJVcv3MDoznhhsJMTG1Z/zpVftQOQVuV9m58msPNPVynMsML09inPjwWXJpDFuf/W7CH1YPMXxsqQ==", "signatures": [{"sig": "MEUCIQCJYchfQ2ISXCBG0hHziKqMIdiVLDWe3wtOIo1XBLEx/QIgC2vw5Jre/kk+hxTTrJvE8X9xSxtIbmGZeN/yVoF/3pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37265}}, "0.1.1-rc.18": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-roving-focus": "0.1.1-rc.18", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0d3821c0412ed8f2b880b794dc53dc1686ade83c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-8lqpzy8yLV76GVBsc9lNVRRVz+sjHXCJryDuLmYwi2nShTB7vyj7Jk5drKsHZxXKJm1dlHkh8pwuRWbRW5cNgg==", "signatures": [{"sig": "MEQCIFUpum6vxvC7H1+WgLgucmXnXyUFWseM62bMsuFKtO0sAiA0kFY9bEzm8atzPR6ICsUFNuzqFYFOlQSN7QRp2y1qvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37265}}, "0.1.1-rc.19": {"name": "@radix-ui/react-tabs", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-roving-focus": "0.1.1-rc.19", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f0045780400e119000b98ddf71884fc4c7c9d165", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-ekCZlxapiTVBgArmVkS3dVz1Muu5JdT1CgnZgpaM4v1KqkdrHMKb2VRFzGjSZu/NAI5NEeq80tm0i7L80ivc5w==", "signatures": [{"sig": "MEQCIEdJvmwJqVibPWjMkFrZjBwd0CHJBEgmo1mNHMd42AEgAiAxOSYM6m80y2RBhHPdHfi9P1YCXrmoKMKSy4pGR91LWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37265}}, "0.1.1": {"name": "@radix-ui/react-tabs", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2aea3496f0a05e5b5d969305db1203a526f44e97", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-JCIquq7yBwteL1/iepc++hVyH5EnSicDXLrU4IrIkCy6W+RKi73htx6K7nRpinhaQL22MbTLDYXo9Rr9X/5bjg==", "signatures": [{"sig": "MEUCIQCCA0RIxwzNaa2Enc25IqiwTWODQYLm+nn+AI+QaGMrpgIgY2kCNaNo1x2m0zOrmzSSKGDkE+zWN64GF0a5KKqIIGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37209}}, "0.1.2-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "816af00479246bcfc51abbc9dfeaa7594d0043fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-5DwgzY9JJr9ajM4ACilElPULA36Gh2WTP7AOvgjd2GBM2a0CpPAM7pWjQRjPuQBljilYTX51L8LEvYkIfipXTA==", "signatures": [{"sig": "MEQCICusEpm5iPMLThyqaVETtV4zmO/yITkz9+GhVXlnH8KKAiBgRBu26np52vRtrjdC7zcwsYntv0P3SE+pd8J+KnHb3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37553}}, "0.1.2-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "98d616a7068ba000052093e2769e9639e636784a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Zhfiqy0XH3d9v9HoaZPnEQ+8D4kxg1BXsfoU6m8DUDTNEy6GBWFbcR3KhNU3FZJr4+YvIdsJDO/wEOcsh+WQsw==", "signatures": [{"sig": "MEUCIQCRMNPINar9V3EyKjJ4aSnD65CiCpu0HkAs+yrGMLnRXQIgWZRh1xRKIOMjTRD+83pwbp8bit1CZuCiRvSRGS3HI2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37553}}, "0.1.2-rc.3": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "26f71b27e54e263c9994256474519aeb69592ce1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YyTq65mjqOsAgOog1tom4jHRjLlJQRGqdGzXrU0p1TQWXXdCsy+S51l/GW9KzW3Zfv/fatqGAHvU24KeMtHDGg==", "signatures": [{"sig": "MEUCIQCxL5+C2TCP/HZR+uaUg0QGTPbcN8HiWbqaize+lzhBHgIgA/R0O7cDVjsFI8Ed8WvV0D/5qynl3pZumutSrW2Q5Dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37553}}, "0.1.2-rc.4": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1908c7adc4450481f0e5d68574f86fd390fc0c09", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-BOVGTOJuQyn/rYqHZ/SUPAMPDQA7QZuEKv4lDeoAwkWjlNUc5Uf8twr66PjApQMSSL8+f7jS/INRtStiAG2RrA==", "signatures": [{"sig": "MEQCIGEkBdHmpD7qjOiQWjan4L/ZJsF5yn4nbg3K5Pbifi4KAiAe/SnUYkI3+qHHO4CENW3PzTA8pv0whpy655/nuS7cyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQMzCRA9TVsSAnZWagAAgzkP/iDrYmWBvgI4o1f59q0v\nH540/gIijpZrYruCsf8f4g5QqKdgmT787bdurEmY1XEmcpoIpOjKVxuKYMwC\n9MAbsmFZ5i3xXf5JzwJhc5NPrSHP6arVxkSCHoXew3pmq1bEINKxwCS9mOhz\nAeTnsfPl02i4/8qbCL/uy4gafkTkpGmG6g4NHkBWAFN2vaqJzLqmJt0ztlc8\nb2e2IiY3QX+OCdSHOODAgZA1teqG1DkEEPleakR//3OI+ZNERdExPzaexY3a\n90u9geuO6U6L84wJETurC9z0bJktjN5tSd/XRMJOaBESrwUeVZQixbrFdDNo\nOrQuBcJ/wutQVBmlUrwjWGdypM7vVvQaIsdLY3ilFu/PgOkuAdoTL3vqAaHW\n7TIAFGoLnE+UcG5i8EAPZy82Qm4jaJyg5baNktolBy/vEBCsGkPJ4vBSSqwE\n+Cgr+bedoE0WtuHgrsor1mCo7U2r3z5CrUCxJ1Z/yzwDtxO9Q1mXLKlDoZ5l\nkEglgztNxzCaLC/7EnC3BRwgwBbLHtmEnr9WjqIKzUZ/CUFBnbIIk3iF5SYD\n1MlvIsNXcF7ckecFGB6juM6MUiJcDeCAXTpEGOyuX+bXs9jg1s50VrxIUBgD\n2IfjbhUjPmVHvVVWOTuEOVYw+L+6Kknw5eBeNyQ/aDB4ABGg5W+tO+0I733s\nqg+G\r\n=IdSV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "43cbdee382840456afb15bda19c515b500dc4cdd", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-rWO0swMNfsPIq7AzpsGbvD8Ltg1JejcNRqn7C5Hk3jUF4dPqqTM9/+E8Uo2nDMw+iQNaaOeO2uQTxXaS9YSeTg==", "signatures": [{"sig": "MEQCICnZ4T+cQ2P+ISpNNsJ9uLhLVzPcjfaPKD4wv7AuUgLZAiACp2SZCdlNNli/EID5BTczDgp9frbh1K3odGhKvhi/ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQk+CRA9TVsSAnZWagAA/YYP/RdkWc8BIpQxlg4tzds4\nSTWm0JEzjC7QW1YemSALP4IjfLmYN6wmooJRxtHR3QhejZyGjJSp9bNDGuPS\n0/ewh11Alsg77X/XyVXSF5AiaUARonbI7waMgKni8UDeXRVXHSngXIQ6AsYm\ny402KD/q69UyUH3DCVFs/LGi/Zi0AZOe5UVIOSXEAFQZKbYQBrEwzkAerYWo\nU3VcWqmTjsykQ12rQDvorEBr3vzix5+g2qlEGvY9p9i68EqxNh/wKUaoM+Jm\n60EAAPAgSrto/hk+kvOR+8Azvu8kpcot/ChZXNOV7gQxAddJ6+Ksvw0V4hHX\nYDUFNdO6y0EsfIs1IbmL+j+Otwes/PLVUwWor/6TpayjpyxKBB1/KtFHe9TS\niZG69KOaYB7Ps7Vt1RSoHGQEUxrB/jEOTbfjcH3Nccc/lUHwyV+eIRIPMG3h\nQPW2tIRAGcsXmdJH41uybkypdFSDgMIfwjWFgtLOKdxI+KsEcXuGwiVwlMGh\nHjIu6q8zNpbBUIijjrbo3M9PClitFS2P7d9f1+efIifSLyXFm47GMyGKaz98\nyVnrc85cdJOZ12Phkcxd1X+/0Wb7WhiY7A26YwBGKUDIT3wDjYwRZt0ON8ld\nKyB+eM6w6EPr5/MdPbxqwp4T/6Gtle3+s0F90UsHTdgCfUfSRvf1M2GN9BE4\ng+N/\r\n=L9Nk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.6", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3668730cb3ee38c1216ad6c200f406b81adc935f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-bclhEnOhjPb95gLPABWlIzv1ZZx0LlnuqLx6STq/9MajLtPoPirxcvGwiuTjy7VHse6TjmCIROMCvsMpcorFBw==", "signatures": [{"sig": "MEUCIQDfsVztGTbnHU9Tr2X2VgS7i+4xGcWIdYXwVeV+KlX7bQIgCyX+7xEX8AgjGoBOOqr2PwZKq0h8Eux/f1M7os3vtt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljH1CRA9TVsSAnZWagAA9lYP/RuvoXI3QJUW35xKAWHB\nMAy2QMvtTDMSox9hSDgucL1YLrH2up2ZGnxcnTcARmHavnrWvipyl12nQvnJ\nr2KHKx7mxotxvgf7Bb01lpF6dzqt0YsJ3xYJKakl/m+US63EccOXwgq3JtXm\nDUskHjMty/QSgI++RGKVTd44wkomhW4Kug2Xz9o9w0L6bmxZBW6V2iNs4xDw\nXiYOVf0PhDZgJSIMEbWJyv6tuUvwU5i7KIbedpKK+dhK44Eu+xtc3Ly7TQ5i\n79jEjZY4bbi+iyTSuebHwJ1UYyBSVzAYaNc9vuV7V1xGwlNd7PswmmP3i6Bq\nE0fe+uXON2ijg+jJOXrbkK5ERMLqPsr45BGcSuqMIaTwLPQU0CbxMWxO3hqV\nyZyjCEOlwr0wqJMynyMDH+bfivYmDgDt/njyr+1UB0IqbrnU3yXyu7HRLxkO\nikLOHWpVx6WXVLvrk0T04F45KSNlCh6z7ghAZ1G0EXnLznyOR5p40t7EK/tm\nT9IrAclhZApEWDs2yZZLcSzOzr9FtZEnfZte8Elml8KK6Fc9znTSwt84463f\nWREsvENMLv0iwmO7ZggONHEgfu2pdQJYzpMRMR7pw8TthNTRWrgH5RUjv//O\nceFEU3RzeraQfkMZWDym5OMawIjtXHBszhMKjAhKUiSUSFVWpQKHB7PkvIMc\n1gzv\r\n=eW54\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.7", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6f1237aca5d421a81adc2eab26227a8d7f69f56b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON>ig9nYjlj9wGhw1Bpvu8j3VeldLgUaJpD5Rq6mWnlK/Zayolyq++yQk6QtYzZnzNoWY0a5Ls8ytvZLT9sTyQ==", "signatures": [{"sig": "MEYCIQDKrKGKvo3J/tmXkhgcPqD/0BtvQF7d7Nd0fHn3RUFuNAIhAL0SVoUsXm6n6xKtinaNeEi7RTNdEROjHnfXK5fiN9li", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmcPCRA9TVsSAnZWagAAn2MQAJikF+nPiWXifiYfOc5N\nFnjqmpXQsfjQvJ0jt3BYDcHJXEl4BUGvN2iwmLmNvm0vs+pTMXHj8+Fu7F7m\nWaeN31i3KkbjAkmw6fvcCotUNmOwBsOmwCp8yghBXikwVm84WFdbbVpcbp3v\nlxnkz1m5F+dTBqh/VEEX4bBXOCjJ0q1qrwFUMDKNhREG79QMkt62BSuDbkw8\nvLKHOuSVoDT6/TsWKUHER0Cq8gt5p0ewCFPR2s29FVmwddUplNsGHMvKJnHm\ntw2EEDOb4OENCZg9epKMOODd6jAEI6KdmW7Ymh/wqxaeyRO8/943jiSGnx1/\nUtVf3kjmfHkURb1+UcShc9lSqFgbz6TLaAl1c/30w7xngOWaq6/nsFtQPT9v\nMPofyAA83+FgLVThjfYizBnmbYG35PuHbFaXxzYCmfcCgUikvnECCcg9Xzq1\ngb/LWAzPWX7Z6z+0KiWa0JLimL9QlPPLKYMtNi1QB5RRzLu+cLlrqS1yck+t\nTbRofr5OyDkE55EOW9IObRShEIhJPRqM2mHd8rm3GdsLXDvfVKnyHaCOkBu4\nG3PJNN/swTjLGIubEARaoBdVfzERyLN7EeqQbUKA1Z4afB20QixCxKiUGudk\nb4rmnToBiEsjwVTlk419Ru6cCzYFGCcm/bcsyLKBg4aii3QhGxftPPAnPNuE\nqpmt\r\n=G0jL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.8", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5f93ae0d91fd73065e58589e9c39ec342ab11527", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-eJmq5uQzCi8tYOUL/7oIkH8WruM99wj3dRu23yvJe2lOBykMH3+ASJsTybxiBRWcxuokW2ewg0Y4kayG12uQfQ==", "signatures": [{"sig": "MEUCIHu+dmMN5cLt1W6TuKiRS241ZCZUQxHWHNsEoc4CtpojAiEAjhJ/hoxpnj4YxiScq0/yG6wkiRgiLCrsPFtjN/NN2MA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5n9CRA9TVsSAnZWagAAJ/oP+wYOXcuuNZCFQw/9kSwY\nThCWD8Vb7MBfx0Lf/WaX2Gnz1XSkkKOmG4Zr/siESv+jy7tOQ1xuwS6pSCx7\nGm7D3q4FLxnz++EdjEnDxRcCVcmldvrH2laNH07ZN9up7Mm9E+jSKXmd/rfo\nAitA6MdoLchYdO5HEvfAnzVyUq8HWCaje+cF4pQpF3C/FAS9R1SCqWqa81gc\nYOUL8mWtpXS4TIACtueF1vV5eFPj0ZY66Mr4KKde37mRdhRfraCkql51E4xE\nIEqpWTDDD7TOtR+GReAcTh04sz4DAdqKFRG1dQLNCyHSeVz/xcdHw9D7VRnd\nqZi1HvxCz2CC+y57JCi7d+7PzYkNvCImhL3NApzbCiDJx8ZijX+QgnVha2+N\nIv7m57NhXjeALcu3rSw/9t2Wf+1czrPcLxyvjLc/PxUvSYKjL6pUwZtPWEL/\nuXf3q2zBdvbX1/xHxAqKsQYTBBWB4IO8/Lpxwo4ZCd58Omi4M5XU8KzMllGj\n+W5Az85Q5AbJXfGEu5A2IG5uZZolBH6H3JCTri6iokHwxkOKgHW3/Ej0TaY+\nPuLDtqwSLIJezZQUfD3BjjseEjcQ+BAqR+yfN2BrlI4cvEXUa47lAxOODjCU\nOOKkY0YbSBFw6ShZ4hz3bTdertOnUt+EXvPvJJWwQoDDNBfLuSO1Q72FCe0S\nPIXw\r\n=bKFg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.9", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "91b31ab455c7a3d28a9efeb1c2b78eb4c519a2ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-AHL8EyixU6ia5vX+hqg/doCsLcL0BoIHkVXIZnMVL+URbCTtdZbF9NFrPTnP2TuySYoMb4XED+VtvDq/HUo78Q==", "signatures": [{"sig": "MEUCIEOxriK6frJjABj754t/UFo7cFB+zlfJ4f05UHz902vyAiEArsrjhrmWXZCjnYyer+ljiTreEo1NhzBrqqHhqADxETc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN5mCRA9TVsSAnZWagAAJEQP/Ai8rm4lIKBNzRD51JR2\nF/pqvxSv5AWXxz37nsOplg3cCntVzwt9iKUQElpN1rZUlFbFymLmCadqq64G\nnjOTIMYnehOAQkrh5jY11d6/Tw/YiPG1YCqIvLHuRpFDERjlO0C5bvqdSoUa\nXFbcRP2ZCo8K2tRjqWOgwT0K69kshf5VwOiK3Ru/KHllBBN8rYwzhWgD7n+k\n5sT7RnaZyhdzkQQn+vZQJHKcgZO+SC8r2e+lrvMutWDas0QtmPrJDfpdl8Uz\nKFrzcbU/acCl4uQsWkiAr0net+luWau5n3ri8ZIphrNN5bYou8rHf/RoQvuB\ncZeEnySyd+ZF7u5AaML67lEC7uLXjde9+5t7jUL1jV4F3hX2A7BiTzadNr2k\nJXI9oXSW54Q2DJGkGvtM6Z5a/1M0UspZ23fakmm+WMMPB6tBI5rte1utN4xe\nOPAVGOFGtkQ59q4abrPlJU78imMHr2WpLs2Q1f3+9a1+jEjoMPMqMwAzpvXD\nPmNewtrm6laOtkX5yfSLs3pXdqXf/8upFQKzOHnYTRH2XLv4AFwR4u0Czkoh\nJAHbN6mRrUW6AoNeCVxyAH+tp1sT0KRudrRyNvdGEWRB0s7GHI4k7GAbZluN\n6i2Vq2/eQ7yQfjRxEHjtDAqw1uVyGTdHf9YTX3akZhrhAbe6ciscsqyQbfce\nQO6f\r\n=Vi55\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-roving-focus": "0.1.2-rc.10", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0e91b2e16976b3ebc7050e1ec0e0d968f4439b47", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-lPJioloi8wOuPlDoNe/9rbfY8HTQHmadPlUptvDxZc6yb2eIFmECK9+foj3M/hJcnWYsT7SSH5/F7PErPAiU5w==", "signatures": [{"sig": "MEUCIAqfF7Gl50h6q5M8mGIF35+gbjtw4sHV8iYUOOP4eCrEAiEAss6ltrL4+SKK7egRCegrBv/Pp4Ns/FIBsMQHTgp+oJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37743, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoUoCRA9TVsSAnZWagAAGFcP/iU2T0o3tApsTDfC6Dmu\nD6kYW9Ln4rhmKe1n6BQNmBoo8JewP8a/gg2FEuFGQkUT0q5YiadSQN4l9d65\nbxJ7SgfxSn8Rm4qUcXUWrxNLO7lC1JLyPJs894XSurigooyuoyXQYpZ4vPDP\nGb2MFIvExOg5/JAaHVGrBvN+l5qOK5pVmbbjdo7WiiQDPGYEUqAb5oqEEpcx\nGeWrNe0jokFCarc5/YvoO4QlC7wO4I/cw9RiC0N/5ehjRUS1TiGJtKRGdpM/\nRWNwPRpwPp2waXGGYyJnJmYF5m1nbTg/7WMez0UQ7v+NZnUcnUjxWdU2StwL\ncctk4LIlyBmRL6wIQ/EnyNBFm/5/eVAYJrERVgrMuOvDC6cAsgaKvXVAaNLL\nxYqgCQItXWSmcLwHsIxIdYCN5uIO37YzYZxoGN0FC2qC+1k1U+Nv7oO5az41\np+GAxesrLeDsUxSK3J3+Le8yB9caNRsnRQw3GQxU402rzk7pNGeg8AuhNm8T\nLfvHul7R5YZkgOowCRr5WUCZ3xcB2nab0HfNZwsesy+1hzK2YMjY6c5iBMPb\nYlIlnen88v5NH7ZRNG2Vo9NHgyo0Bo671MyEzLStXPNK7nGOhALZaKscH/gg\nIgY4/YxE8i8HmH/pkvali4RJSqgz5eNZZEXNosTcVs1Jn1f7BW/aN6FcVCt7\nnqT0\r\n=AKqV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-roving-focus": "0.1.2-rc.11", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a3d08ecaeb9c0d05b74e5d09fa122010d161e199", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-hCBk9oiwJwzGOnf3PUt4ZAvf7QVjMA+5qSxNoJ4cJw+T/YDihHMT9rG/uu85gkYRlJ+ty3jd2AmIpw2jKmOj2Q==", "signatures": [{"sig": "MEUCIFkV0bDH9cMYU2Fj3+5Ei3fRo+ja782JMZ9YqGNz79N9AiEAgf28HO8ricoFEjhJsm4aZtfmfsWYAqp1F5WHBcFl/zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiBICRA9TVsSAnZWagAANywP/jUsFXEkdZ8WCKbUYGUQ\nfY6gEroMqAf4+uXxxopr7B0Idd5LpYriCWmb9aWNn7IKqNDEyCpyGLBiMfTF\nC6MIf+mibuW9UIZQrXfTmwnJlpbFEsMPUwmYAoKlR+WaBkkZ2uYg75MpCnwV\nBqt8fnjy0sM4YD2wiukyBnNoL2XnWEpVqX3E3dXKI/aquG+eSEDZFLiNEOIY\nBn4sr2XEkJRco+hWSiKxDeOHeCrpiDCL7IAUvmsUlTgmweza0IDRfsJO4sr0\nnXLEhXIS/kDcXZGTBxdL2L1RgIiBZJPYiTfJnZIFt6tENFBPPCPyqT6maLrP\nQH5Hy5OhH7AWEb/Zm4NQrdCA+fYTDoRnVIfiDCY14K52kmc38+paHzOAPH5o\nB0vtctBZ1bnW5IiwYZb5Epq/+PQf5mWkicVumRF2X/ADQ4uV5XWPT8hmCeEC\nXmYBp4vzVzOW6+8FUVxopafM9LBWAXkidsJ1yQzLyM55EnYC1F3SrAfM+SIP\nR967/sQJyt3cSX9198POYrLutoDwQeDdK7RTTP+ZofYWN8GNSR8CScEA0gZp\nBJ33Zp4ZGh63Kb7Wl6Tn8Qw0xjuVcVmYKw/N26H1sBtzWonA2ktpO6te3vkt\nBcHVSTm9+uFQuV9wF9zb2Vw+SyBtNGVb7jUNHmGmyf3xvdIOSTowLgh8xbDe\n7IfS\r\n=b7gm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-roving-focus": "0.1.2-rc.12", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "91a5c664b1c6b50c7e51739f2a2d0f48876cb793", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-S2KlXGSBFj3rBRpC/U5ZD40scFSqrH97pDoVkA34lzES9gucRF+hweHTy4bTK5yePL1mizgc3Nb8D4KVrbrySA==", "signatures": [{"sig": "MEUCIFjP9BWe7B+yeBzrp/F2Zj3jhImz+h6ws9TGViK/KNy/AiEA+xrUJEaEx2f5VtPFRFkP0NAaeO7hboSmTaVTHlSBReE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiO4CRA9TVsSAnZWagAAHnEP/0UhGBlU2T6dr55JBSv/\ncQ1wqfORSUW6IqnK0tksLFkUvA6YPrdif3G+voxvfQSjNZxhUI0Zc2eduiD7\n9HZFrhS4N1KfEsLi3sHZqHza9J/eESC5pCQpsOk7Ha/7DffpgAVIEnYdqnnH\nwOPDC6z29A9MOn1v+C075rh/xt3RRlKcM8ZwICB7oLpYhsYW8r//V2vGUa+w\nI3XMEK4b6BIS4SuDdO0evVV0VhZWwQZGICnDWfAQjHDBPfxp4/M61is36jTS\nYHHOvweSWjhsCTutB87BEbGHvz98m05sH9pzjAYP3c+4oymB1CRfOILhr/8a\nPfLT0GJJ569gEPV8mhX7lVF6qg04TfHJX7Lq55U9lMnwXwTQmD/snXal1Fyh\nzS73eZ6kCDeh9yZCYqQ+hSoYOLjQnABrowZrAFPp708pKHJd261gH/C/yH1b\nOeRzKZyPvpyDd9+WkY5xGKzAX880Nbm4t3RmPJez2ka2tHZXckAXUuxPo8Sr\ncDzlk88C5diKHlqUIGN3dtUXhlGllzAGrcyotwi+FUFhl2Lahj6rfEEt9wCW\nOCZ/HmB7sjzwg/SnqCCF6mmxQvNDXzxRpJYAFP2Lhf5JOXnfaKsptX6DBnYM\nyA2BZMz1lrnx1f2LFTHkQcuk6huCEwuSaBsa4eSLqGFXrpFQ+/nBG6qRWRok\nRtkw\r\n=FGvZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-roving-focus": "0.1.2-rc.13", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8372b18b5b0e8b7797fb5f7e9d6d06a4b1f03bf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Ntml4GxkzokA1ngUhiQz+gPf1Rcy/9u7Svc58E5OmWcL1U8XWRiprKmK1U0UNwqJCnHjCExgIeo+MFBQ+oOz+Q==", "signatures": [{"sig": "MEYCIQCq8z8UayUJf24jR4gWPktUwPNzkf18kDAEeeSR42iUMQIhAM5rSn/TQs9qfua8XV/drVHI9QG9lLmgoFt6us9Yb9T7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryj4CRA9TVsSAnZWagAAS1sP/0Ww92TkOHqgnO2IXxOX\nL1ltenmLg1Czci/st5ua4UO9ebCo1Z3cUwIH/oC5TYYlD824NWPwOTJg2dIV\nBn0OntheQ/s7YLfLqZra75I8WPesI90mfjKEjd5xk9H07n/kBT66EQMw49u7\n2X/ts5ysSW+0IHIfr2/PLSWU4Li5lqoTdUrV2P5gyHtowYtthwwqyoHVFCka\nVeEHL4s2/dwLbxRa3y72yPxzMRV/cHAnbwLX38aKGivnZUgmLj3JM2lCYzaD\n2FbK9bD8Zfss3j/y16epEUWvR0CocEID3gxwmqyDMcYiFHXb/RJ5klxs4Gx8\nAiueUhQgyVUVeGj3OL+0CDDx83wyx+npEBwQGocOyLqLBuKHekx8Qp8JaUPZ\nVErhGMKqe4zvqMG+0LL/HYenJrcy5lqs74XhaYPAYCPDAXsdAG0HSHpoG/MA\nG3mHpg4t730tHw7QSDFazWJf8KiymkiOcRejbrDIlYxmN3oNFWvMKxLsPiqG\ngmG4qVzN3o/2ohA1DDrjTGv9TCyzRlACRBc8o+2ov6cqpwQ9Q+/RRqKjomJA\n5cDrygSCmLLkmutUKq2KHolI0UieNlv1EWl+2PHVgH2yz71FuNX3x/x8H3jm\nGNHbde+iSf1hCxx3T9Qkye2qepHU25U878ZlYbeVLKx5vnYELIlNqf1F6UEI\nOX9k\r\n=Zf8j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-roving-focus": "0.1.2-rc.14", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e7ba6ca63d0b6abd96caeb26278356380e2ad4ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-D5azz+wW3AVJxC9V1KV0GcaZSCWy1mFj3eiBX/VLKZ5KsjjYpIjRFzyRKzgIrw/UjV4AIoM22zFzU4ne8EcR0A==", "signatures": [{"sig": "MEYCIQCTxl26xeNZYIWKjsE1QR+hNSjM3NY24ckOAPYFFx7sOwIhAO384FNv3SUVIEbh6/yPe1qRLpDAnAFuW4+YNaIUyxTP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzR/CRA9TVsSAnZWagAAfZUP/35O+pe3d4D8cY498teD\nRr4B2JtskSUfV8mOJoMbNkemanFLm5HvEo9sazgGmxQR4Gsqj78LflECG71I\nfUOp384DYZeP378mEcdWl3FoTc55hP28QZCrR3oO2ZWdtIzQ/83AdT+ZStmX\nyvqz0Kn2AlIPzDlYuNhKrTI+Ji6dkHJt0II9r65Xbm/FzfljY/FfO9owJJrC\nXFE070Y2QjA8LJ+gt4JlE9Hdxwk9zACVbg0T4Do/+L3AqKQ1ziYZFRBXQgyy\nSthyh8eih0s0lKNCAHzZFOe2AR3DFRx+saXLzQrJkVT+AoTHzkVt2Su+ITj8\nrNa8M7fbeJNeBEZm1jMsKTxFj7XuM3wdxBDk2WWthsIeLkw2zZmD24quuM2C\nfyGoxE3zxj/jB64JEvglpoCGtJYjIMYL49ow9CmvzeFaTTjFYE+EX8HbaWhm\nPaxV2DfuJbHy5V1xJMYJph8l1u0ndu796PwxhD3edy2TC69JzX+BQEkZqgvh\nH0cJW6heEGoU0kmD4FEcuHEmeHOXHokuMuvHdMyGIKrk6LISCLfhmT7dGC1j\n61a3l6uHrXHoWzoWcuKkRh1kGRTPh9fVT7QFM+3CKyuvOa3zd7TedPqxpPRk\nU3zGNyo/0cpYO1PpI5WkQgHAMfWGtZXyvpYCYPrkeWXmFgdBfNmYIVLLhMy7\nj+1k\r\n=Kxp+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-tabs", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-roving-focus": "0.1.2-rc.15", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "874791e0f64b5ef3d91ee02be63b4520392aef41", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-kzOYkZ3A4xyBwT5NJqVKXoDceKufAwuLu+9e41wgmzoWolafdxLfxr/j9rtwsau3PomXZkvFyWzAZzMsW//Fkw==", "signatures": [{"sig": "MEUCIQCuLGDrPOKeC6fx9Y+AzQxZJnAKi/3lIooLNKUgcVNNFQIgKa8s6WFqIXsqoGDPRvXG45wdVNPJyqvXoVVITbVd/Hw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37748, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr43TCRA9TVsSAnZWagAAGSIP/0hRnVsMVnbvGHkHFOTw\nOGRA4MendOS/T82nJro208sy5bB1cDR44qYv/SLyEumXC5I63LzSYOfWzu5q\npTYIeH8PBgsTGvvG6sN2gqdeQLxCjvF6w1aDi7eonQDcAle8t9LPuRbQQOAe\nCNe3Yl7VJOOrdXIK1IUQdEzxdzg9EQJ30EZcluSM1vhUxhPL1hAe+dXamPEN\nZ34TEqvsrp1fr+kjvUS5Fzj1e7L7o+EKB39fg3NwKC+O6lA6kF5cl5+NUQc1\n2OoXBqaTWPLKIIYSXy5vtGaVzoE13RZTL5r1Jxwln9iQz4ho8DLyOE9uiXL/\n0MU0naanAkaOgyk/25HeQuRyrZeuD5JD2Ts9PkwoBfeteV9YoayjOOMdRlM/\n5yUUJTJTa/18+foqqiADEM/P2zYCbsZGiDKYi50fCOxglYRkMPNJF4qpwk6w\nV7a7Dj7kHxbzp0CCZTHI6R2AxmF5/i08EG0f8+lsYFVPlXRzsRbYahGPkojf\nHZqJfNv/D7Jnnn0PTtAH+UANTyAa5I2ZdlkgefhCBhg6la/EQW6h5JgCWBAp\nP2WOjPAHpUo8Zw7Sqw554hBZVMA07rWDPP0Sy7ppgNfg8Int4v0ovBmEiYjx\nxNv5AEJzsAGaNaQSRkJ07PgzSXGLmZcEvUSoYEzjkYMMQy48ccWMlfIpu7Ry\nLuOp\r\n=g7qu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-tabs", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c2dea2c20b9d81eadfec56d1a44559d519df7408", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-dVSYmlk0F4YLjmY8Ie3c6pfBARzC16KmnOuHvk0Ru0lhXjdQomLLcE1v1kk4ub3+NRxpU/HaNGAwuihwyxbhNw==", "signatures": [{"sig": "MEUCIAUgzLDSWjHr1khXPhaDBGTgC7jk1zPAeAqEGojfZ86eAiEA5waAelSGeB4csK9ADfJ5ILvzF26imeXlJjeb4CpsYL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD8CRA9TVsSAnZWagAAlDIP/0eLeH4GpmbxeSZvVw6D\nHFfIuIFOJPdF7r7ZyUcYqUUrcTUhbc0U6YLJ87uVibX/tU2m0FV99EvScF9J\nyxUp+N2U9p+aEpZAds4vW3ATrRqBHfPGWG4j74+FGbUkAZEtfX6z9YBSSIIj\nYH21MWlQAPG+djvtekA1BaVVhfE26WFczVasgo1Wl5Ynd/MfI/808q7xznaB\n7m70mIGoJN/elXKLCAuvAADwUWeT/Xl24d/JNimTVb/tgF38aJK9ZORxF3y9\n6vlyMT6aJtKZ2W+Ka<PERSON>bDsz9B/Uf+Y4Aj2qRrq8l7HOSc6Am5RPiZeADNLfr\nsXzQUWmqHyeNsmMYslPi1BUSAaNh/oeikX+6iqw8siiCFfl4vMV6gWVKl9V5\nut70/a/RnfjwJH1fpmCD+NA2CyXiY1laMnseeAI1GM1uPnyT+X005ay6Nx6o\nPb4xife1ajZ+O9SnEWHI0E6morUM5154EI4A0pdNI6aReUq1Q4kuxRrh6hVR\n0UAx6e0pf03LzUC/Zi0oTIJvpB8qRLf41/cBILf9p6E9k65qHxbdk6x5GUwp\n5ZxseZ07DyEUDDsK9Kqauy7Wxzm4SISVJFvZ2aF8gAGJG+A+tUpkrKPh6S7r\n6U76SKGcoCa3hDdML0/OY5EgvOYLe/JTsSrmbATluzSzlfp0+o44AJ2A0Ink\nyLgF\r\n=c5wy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.3-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "92fd084f488813c3904ca0b740f8ed1846799a4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-xtve+H0kZkS59ZqKGZfDgDYperr9iAyCLhF2Uu26DDHHXv9UjWEqysMMGFFwDtxXnWs14VgPnpwaoyx4IK3lKA==", "signatures": [{"sig": "MEUCIEVF3Cc7Idwi5GQI9i0Hy5PslgckHL79VofY0eW0X9MhAiEAzohdM6vl5x/2KWaGZyrWcnZo5zxpJM/Lcsyn67hGkM4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszA2CRA9TVsSAnZWagAAeusP/2XiZKNn8wgauhsAjr+W\nKcEvIwB3bp8h19/tFIlb20ZL8MUeyyTlOVa8NagpGnnnhv8hqA6+5qEUQbPi\no8Nbc4Cf6o9LIqrvy9CTGsuGBxQvONY5whG8DgMAkzENTSqORDEqdTNEzJad\nudNTulzYZnmr8CkbhETUqGErCbRTs1WJodq36Vt40oSW2iRu4vgDkn4dvF+/\nf3g6uVPon/5zhTmGaJfc+2+U7HD+jurMqX097eVSz6wWIr2pvKBMVl+n9Jpw\nf60qjJJGx5vV8flrl9Rqh8QdmRX5hbXQZ2DgNjB1S+zKNKgjpCAsBOVGDsjJ\nlR1zm87SnxNogbhUJI0z4x90JBtwl5LJQJAHKLSZxtIWTs1LCEhxjP6OPmTH\nfQkiMmnDH81GDjrwIp8qlt9gs9uDPf/InH9LsfbvuVAAHTqrIsgjHgkKBqse\nnppWq+M8Ue9FwCrtug7UwpMPE2xIbgDagAXDLT4AwwIra3IRgtgyvKK0VGgb\nYYA/wr1q+NhSFP5khulSUIX1y7FXTsVrYte/h/eEMGNhAdYYN+Dws/rBQcEx\nsJlDJc8YSGz0wVfhbFDgXbXP0PnNSbRfNWp0g2MtSyOCjIwosY586DLuQNq1\nR4TMB2O9RtQQBlqE7N/ypEpszWUxLIXycqVDxDjFVDxxt7PKVfmqJUu9sIXr\nyMsv\r\n=kb2V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-tabs", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-roving-focus": "0.1.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2dfc11a30f0b2c22ca3a3341a7f5da4ff323444d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-cuI0+OWRc9baghKsQyuKui2DEHBodYtLv//bnQ5TH3guL1GeN8LbxJY7faDu+UZccMRrzjN6ebfi91Y8wqjMqQ==", "signatures": [{"sig": "MEQCIF0NzGeOmo24lvIJN+uV4oJ76O5O1no5oRnb4F/hWuiqAiAAukBFWmsciKQ6CAfYtN67WSjWYpoUq3yhUJ6z7udZug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszr9CRA9TVsSAnZWagAAhmwP/RIQhC6AwzKqP8gbGBn2\nmk/XPWbxJonyyqC+Z6neIxvRe6vArEX4T3utt4IIjrKhqHD/yMoqIp+fIEet\n1Aqe0WSPQCDikwnShFOY3GXcqLib/7vdw6u/T/1cyCP+Knh0d4CEnnaCiK1v\neh4Hv2Is0L9VrNb7FTR5TMXqj7dU99cm0P/uGB7Ywxgd8XSR00C6ADGiAaaL\npnWZ0vUwuOWNx76T42i/LWpkMrAxWWxCf/c84ssbMsRWsJtzm2fBQt9VNI7J\nOPtu3a0z98gjC6iCNxnzceXbBQSNRREb7AQAUjEeJ4djPg4auWjg/nLp6UtE\nM55hbA7eIYM6YdIlfGhnqMAAEdV73IP8oxyVx8IyjlmO+XftdzYAE76VHi+N\ndyawcRsMZxFhETJlVMjRpbMMppL/M1TyLCoHNj63j98vfL4Wn90cgdfkCePr\ng05YLHPOODjQI5U3b8DdZarqFcV6k43OT0A8JLEbj1cvWo0LHorVsoYAqSWk\nhfnNU1yyOjn3L0yiaRjcJ280HL/uMKTmFQpeMlV19UFD9MThlCUACtGCFqj0\niBrrIyVUvll10cZ4q7/pOaYgbOUsh1UlHD9UdSd5a6KOhnFIj3YGTMAuSREx\nmjroK80h7oRQboexagIqMzLtTIwbZfaAQmb0bWCv+ER4wCGzU92KLHnOxrth\nL0SD\r\n=sKxG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-tabs", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-roving-focus": "0.1.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ee31b413b2d87202f8f194cd5b4827b35c26a7a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-5UK1j3vcFQTNlsFjgxLHFw2eGLJ5EYL40/YHWWkH8fK6UC9+JSfVSVZQYImoZOeUVWgHJSEBaNdbikncjnUsKw==", "signatures": [{"sig": "MEUCIHvTiKhsSFjlEjS6nMbOGyaZFUazr7R/O3/nE9MGewVrAiEA9d8DYvgfOdWczXNehoJCOrrP38+x3O1FY19Rxt3/amE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLitCRA9TVsSAnZWagAALCEP/3JSnpXkems5CFalctWK\n7X3LzU1VNRAsiItGIccD/fvAnJVIOeFjxgZb0K0/NBN6Uc5YlBeASDzBZX4F\nD9LGy4y/kW1ZwIstNY29Gl1N0Z42rAG2m3aFzmBnwiIYOPH2vWRRNsbiwG5q\nGcjVuQ2DfxyWjG8qUgppVb3fbQmJzO9wm7BX6W1uJfSU0/x7OkD6A6dFNKnr\nDUA734ryLAi8K2+hdRB6Mbydko5aJm0oH1NBWruNhE4h/40g7Gj1q4kocH0l\ndVLl3XBi0SITtwZC6TehxK4lSxkQWNGGvI5KyVfrEWSQO5Co+5Nkq7IYCPox\nIb6zyPB8RLLGC8uK/DGzbuSlNDHs6u4QqyhW/+ihVS6SUfUCpV46TFXwFFiK\nuM2EkDH3PhXbQco6YSGnRMadgAOTFt065iWHwmcmmoNmBQKHGE70VNgRtCH9\nsMS/Bm3N9sVPI9CrkJ+lhBfOlzX0zEaokplbTNJmaEGwfajsi3OnO+L9w5o0\nShBDI+RhgrsR5IbzGJ2J5c7Yng+d1L8P6hCUrhgy3+YbREvpeG2Nqbc0VIlx\nPVWzTBx1hW2BjSCmhsq8GHEVUfSk4OF64orwefWMd9w83F561kGGOVPKLZEy\nLv0/RTgnQJWdO3FT5as2O/2i0ilvpUCRNWQgvap/2/53P5LQ3/jEKKfq9euU\n45zU\r\n=dl+X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-roving-focus": "0.1.4-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c2eb9088be4377bb8ce7dc763fab0efb51925b6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-36qFORndh9arYA6uRhzenLhFvpOwBhe46G06uu4p+H6YUj7UaF/QPhhZfPcDw+0TtniffhxdCya9lU9ffg5f0Q==", "signatures": [{"sig": "MEYCIQDo4uwrG3p3RMiG/UBpFcO3Ezq20HyN4XKC+zZJAObOZQIhAMLa9pX/OaGw1ZT1IiQwlc9mostlajvbU3HDWufvxpVj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLkGCRA9TVsSAnZWagAAo10P/Rj8E9gJfVJQ7uEL0UTg\numiq3tSRPI0oDsVoPXh/rUtQtAcrbrfJJIHUR4MHf1TpXUR+Va3n0VGVhLC6\nyQHphhJKgEHZXuX9nIpiF7hf+qcGpWAofujqcTPCLQwchZlXk9LekIbN0QwD\n+LTM3wzeYJpGvTiiPiQ7SVl5mjMEa/Xt+b33uCu3KNKihDPyN9I5RDXsoHEt\n5WawP466pCqhUgOteV7qhOG+B8mb1cNNT1T9pUmmGT5DdV8tSzkvNWXHgfmq\nuK4eYZK6aZTjrk/36oXrhGVG5GB1HKybQjfw7w6MIo5Gftakf7psIyv91uDj\ns3x1Tj474gcotKzAJJutg/1W9WOOQ8DitGh+nBq5EIjNZ/++69SsAW+T0LfU\n4oXhiP9F7md2Rq2Uw5rdodwEgGNNS2EBbI7egJL1XBgO0PMbfKQa04GGJBT1\nknFa3Zo263AVNiidSZFgBVZf3RjFAqq43DTOyQ4x5TtN2YFS8adu+r9V/waC\nyvIfZFvkuA4bj8kIPZCMBsC5GKWnUJ5qIRbeP5p+uZPOI9xnvi+OT8W8iltG\nmJbhYREuxR35vTc8EuCv5MpccZB7Fgu7D04j0zjRHHlReRxqT1bWO+x+0/wF\n7yR5kGk/yCXZHoGcD/rr3Bzi5M7JvDLPMdX5yj0rUXGyvBRAE+VOrE2UtaQt\nA5Tq\r\n=E1nL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-roving-focus": "0.1.5-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b81040e3f667133fa7fcbe2739a3abb7e121be51", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-vJtXpsrCRfmTViEm2WpGXgvHwmpDX+UVO9anjHhljlFA9vNnj8jZkD408qrI4T8jPqs28YFU9LQir/QF6ASGCw==", "signatures": [{"sig": "MEQCIEslSsr3LDZOIAQCwrxWwu5/OfOpMn7d8oqqAsOltmj5AiAotBFcP0SH5Tzx4WJFKzM203jypB3tq8e54X02re0Qdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rkCRA9TVsSAnZWagAAQmYQAI+JlLmAL66SQM0h1xPT\nCXyoolBSamDTBbMU9o3jdo+EpzPAHJFRNh0Uhiqk8Fgd2AnnZWkVhRu8OobS\njUixaqTeN3xII8hl5wqpu4hXsjL4s8njru9c7WVnWV0reMqf1dTOKAtE2Uvn\n3xH3236hr/ibVvgR9BgB+ipBkYrkVcLItTh+fOZWA29Af28fbd8E8J6wbOTY\ni0Q0GqlktxTjn5y6oPfb6du5Ekrz15ytqDZ47IYu4yUiZ8Eum8u9Z/rYjCHw\nS83Fe/JwyjMMmNL89iWtDBe4PH7eJVeLFGuElSvJNlfoB3DHY3Gl1qhES8L/\nmI7BDyrrtC+VnFQVD/AlXvB6TjWeu+P/c/eQYMX5QlvpCp+fRRhoOxrqH2Uk\nu+qyYS61KguyizTGHAuQWJ5jv+Wb0AA/rMtDx6qZHiwwSYkM/xjKT0NNPaHo\nHa8Wo5NwghavkRIaoz1wBvS5Q59qTEmOWFITSBd8R9p2Q/M3f3Tc3BWR8EkY\ndhj/LDsBIe/XIwZuikWIClRoaNefZq5EZDGziuTnqrMCQabxIHNz/Vfg8JMh\ncCtBYmHaxygE/yeb31imIeAbv8a6GxZtPKtWH/hZVRQOmwWJ9Sl5tXyhgc0M\nFm0yNVk/FSCM4URIi3pJuNkVealebEg3iIbTaDrZOj51TThf+PJSnhmUYelv\nvMWO\r\n=cw4O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-roving-focus": "0.1.5-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c5f130cb969a862e04b6d9cb13c062bdaaf2a577", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-sUDquQZqcL8XvM9O+x1jcGjiJZkqsewSrHZQTfZXXx4cRym48SDOW6wHPROvWb752shVoN663EcriYImKTaprw==", "signatures": [{"sig": "MEUCIGu7MWzCrMGVMBZBZAS1FkIObRhLA5FczfFAwtmwylznAiEAwk2ffLrvHOayvbckjQKoXbSTVziDGCz+F6Qgd4BU+0w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEgCRA9TVsSAnZWagAAjDsQAKNYLOkZE6RLa66Fwb+9\nIrpO5dxnw8ZoGE9KjP+HqQ49Fld/8Rku5h3IpLq0CZ3/eBaKvZ6O2q4rh3Nx\nZ8CF9KiUe+359l6cTTS/PuzUnxPWZHx97xc9ke63BgkjERawqjkHB9BH72z4\ngUHdHo3tQL4JTVMfyx+k1zlvQNC2ZIbiagYAAINsWt5pP5tIG254nOLE+/Bo\nJ2b79yNhnoR9MYFGJ9exaUO00b79LRuQolTjUY13t/j2mBb4CEAZKuqqAAN4\nSxaWMs5beipWJFRIsIzFG+mwH299ZW+jIvNzehwsK2BzP+uLvXm/8G8J5CAJ\n5NO9GSxQM/+1jQ1R8YQdi1Zg340E5BsB5iF+W2K72kCj2QP3FBtft8abMfIj\nCPZbJAvxswfvbZ2bOmndmNW7UDlYUp4SuTToQFqEe50+ynV0nCpK8qf008d6\nXiJuk2zEux+RYZsTJHDHDGvCZfQpfw3+hXJDl+1VwwKQRqR2uAFlaJfWRX+U\nypqGHgF2C2l+ClrG/XGywqBRQzeTbHPNM36nMtbhvETD03ktmS9fcdzyvrYD\nKpbiwRCL4mxHKNkyyMSTImzCI8e1uGjFRffLwEbcjEump+YFr6bwRwyhCIjY\nBayZeivR/fqOSUSNjlHPzgWrRkt0wndKM20tRCgG2j23vcafgo+icnvrTEAx\njW52\r\n=GM0Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-roving-focus": "0.1.5-rc.3", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "700fa2193a6a8f13198559f00550680736318be2", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-YZyzDadRIp9MXFv/MqArbd3tBmmG1FrhAfBgk9xi7UtXm3RvCD/C4mptwWvwEKrZN5MxHCQvHhXsxchSRKyxeQ==", "signatures": [{"sig": "MEUCIQDoWDQSKW703rexbpDCgWRjb0w3JhDuYRHq7MZ6y9qN4gIgMxlv4qyYntOjz+0+KiiN5qDZKvkSRApqvJCMqfLR5qI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CnHCRA9TVsSAnZWagAALAkP/3XEh4RN6ylE/4HEwuzy\n7d5aJjx3reKaPpj6Ef3LXh1DngFcHGqd7m1ak6LYLpjRN5+c3vI4fWfCrP9+\nDZPwL21YcTZc9/ARaA2iCBvdcbaxPv1qQzY1TJKjgfuxRbRYybps0JQ4c8i4\nyXn0Y/IcZPymb1De2uVN/e19IA3/M/l2fHDIk7gw63FNk+QOJTd5rxY14cW4\nnx+HT+JzDzQNIkbDxEqxmA53gL+yB2/i7Pp4h1OMIUjH1ZS1VsqoprKqDH7V\njmlKH4JbUpgvtZJd6OtJT7avd8chdQnQ3vJfezoZy3FaseDcJcbFKRKh7Cvz\nbjv71/IYxcqpYBkZX5CWRfoMXYxg/Qv1LN8wTwIalw9Xon+3MT54KPoPKQBM\nYaAuvy/yLFNT6qeSN4ab1+Qi56ZZMg3mfP57Vbd8KiTSgoWzyMjgoCjt2jZO\nkSpRERDWTO+AAy7H3/2CwDDHEphK8j2GL4JrSU7CDyqlyNmgvR53WZr0zRs6\ny0ha8P8y57s4eKSCUl/SQ+8WH5pYy8x9mAS8xXLMe5yZh1cWmZcxDiBqnvMl\nY11cVQvSMkGMB8x6sDNVZPseQ5MLTKmMiYD7gVXk6/DVlNoDul/OJRsZmDiG\nulAiA3JDKi/H30Z8vu+D7VFPInq84SGZ9VQRW7AWTPpw58VbErcHZVIBxNWa\neyxL\r\n=jOlH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-roving-focus": "0.1.5-rc.4", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "367bfa57edb66249e467827c3db4c926130cc31d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-uw+Q9xjO8JuJwya29WLSUg49oDDK2hOo0qf6Wr6yXckM6gDTxWRVPbqNBALnKAD13NIY8jgfwFsH/1AAt2SXNg==", "signatures": [{"sig": "MEUCIQDbbk34ij2O0mCwaEpndbMUZMRKdY2Y8rKokHWywjV7pwIgD1NQw1suK45vGvuyKjxxNJxKKyfBQk9AjXDi7pV9s6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GrKCRA9TVsSAnZWagAA5zsP/i1yLqkPytZlDfuVlJD9\n2JsAZN3kRGDTlV2xqYgPX2vNpghtEUCq6mtuKnkv5iw20osTjqtVJQ1334XI\nDeC93UDJInlA8lyUQwhz4Hx0oVHW8Zl24xcYW7dZMhcwhX0AqsRtlA01tAp4\nQAS9eq5Pi713gInI3PB+hvkPcJzCTvbGWvdAt8RhnI+h9qm6KAd1ioHcncIX\nArlz5BmaDaU79K4Wf6Ta7OdOrI4JmsCv4tx2oG2ir4G5h5OtITrzf3iZZ1Ak\nMt7UWoT+J4PmJOk5FdKagQFyDcTECXzDJjFknJvP91MdoKeWXrrr6izt00g2\nuCZMa+WyrKOhENfUS7XuypusqnomZvvNgNEnTph3hoi9DAvBcX57DpQWCz7e\nBub64fAl2qJoevh7sZs+0tNJn8EC58/YvxzCVdIKztiCy8J2vkJ0yej34VG2\nk0HCR+i5/DY2cZrvZX0Tz+toU988/g/vV/jIhsu3XeYa5fkPDIUETOsrSdNj\n1qw5uaW+ADGohFFOloDkoSxrz29xPCHOGWuuvGwgx0XFZTfmlsgbCpOI07tx\nzbzwxct08LdgHYZyf3eHUpERl1u89PauGZenbvmv7Gz7MSDBF2DzAVmVx2Yl\nIgrxMUgFtIqr14DhY2PnR6TcOgq09gSng+CvBUJbIh3RSqJXqh8raH5wGA1/\nw/pJ\r\n=gWew\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-roving-focus": "0.1.5-rc.5", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f5daced403534aabc2edd0b02363311e6d7179a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Fq3NtvKhw6lw4a/5rvH4k7F8yPs8iqelZoSi2fSs/1AMVW7h8aByp1VV7Z89pZpSgU2yM8HbAkJ9Jceygdkhpw==", "signatures": [{"sig": "MEUCID/ws8ocV7CPYwlnFDtYCo9Nd4dC+VdEDweXjqVEmlI7AiEAiD8if3aErBlLBNaXWDaR2AOl4WJefL6EkY905/V8bxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5Zc7CRA9TVsSAnZWagAAdikQAKG2qAxtOsJZtKZyjZVR\nXaPVCYSDKbaK+Dt/DL7pIPbJ63htfxy+tUNM7D6m0DLmmCMcwdt3YT59wrQx\ngGsNmbAd5Oi5kYfPP4pDXpUKYY56FBbGWLEEPerfvhYp4/9iozOIhtqx7O/t\naKFdjq7VnYul+nALxO1wm+vVzyddqACxJBz5iuq6t9yE3mcfTManYVkVL5vt\nX0xQl9+Gxr6gqSXVf3vy1zaXdRnELNATBVQNyP57ohhQ1VYw+mvyI7sbFpBL\nPWszAid+KZjTWp7+827gpHWdoQhPWuI0NaHM2EZSk5gWdESazfekOwdwKE8n\nuotVN/ltAw3n/eU97FADOur+B9ffMCis1/GNQEAh5PZmZ2E6TqRSjTCjAc+p\nzWYC095r1LAgwSMRMs+VmAVJtETnGbynXgY12p6dwAMTGx9pahY+IcL6YPKy\nrhorwyFsb1ldjJrtLW6SA7Q2vDe1Lle5NOyPouYY3uf6/i2mw+lX+SUvX8WA\nYFxSgHZly7B+U2ukABzlT991up12qHtqQ90bOCG2K0zXlu/I2EdR66VgftBQ\nRijrJ4MFmliFfVQM3H8j8fyGimZAhsCxxMVvlPoTlt4J/pJ4P79zSXfowtj3\n0xfRfdc8DnwsIZ6TaNN7wLNXaKJ8ZdMN8wY1ZZ2tcBrG1liV5hBVSaxP81U9\nH8pE\r\n=Ll8j\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-roving-focus": "0.1.5-rc.6", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0bc9e893752e5e0feee798e547c674456d58c9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-2AtWnQ5vxQI+TQeMbfmovVc2oG5T/iTCw3Hhkbpu15pADLs9C3I4Cs9b+laulviPkF2oZ0o+KPiC4AmfUJlQTw==", "signatures": [{"sig": "MEQCIBD13N1lZa3XWstfsBSuDVdITC9PTT89zBkBqO+h1/+mAiAVrJppPVOaFrUqR3hLH9YN87DJaPPZDgKAFxIs7RF7Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtbCRA9TVsSAnZWagAApuUP/0/K2+ieSKglQNQDiQXw\nW9TtA7gXU13bQf9SI3nQsFT0WUn3INIjo6phec3l14upsIjDoTVHjMpMhHym\ny2miO37rmr/GjCq2fYZNzn09x9EGk51APJlHPa74HTQeai7dL5izj8m8ZUoo\nE6rSZOHv3+9nXD3+LahKTsIG5KRTGR1CJg07oSLqSY3p1AgzoYZtJbBrZW9e\ng1lLaA3fmW5S43pESb180E05WAR9Mp12brqg05sAW9gZcTyzPWtgx5z4n19z\nFW3pxJbVNR4ZJc8lxG/cvDxaYQaY4HKu2M/guXToEkfIsFwK4+giuaeRUk9k\nKvsKFG6VDaituij79T4I1BG1vBSIKbeRTblcv15tsJBz/f/wZ0Wzj92kWy3V\nTuY8o/R67vGSNxWItY1q3NKESLc9TStJmTMMPEXPEl23pkAAJOsJ/kK/BAmg\nYo7tCrAkZckdyJylpadQBkEoQRWhyTPcfdmLOzgWNsXjV1pMSBEW7t8RIUzm\nBu7Aa72mjp3PjHmYn70gW5Y6tk6xo8g5bdLmMgkddDQw6zIIkw+tSef5ljDu\nPrg+DhjIQ7Z+Zx/wkhSMT9xKUM5s9BQfzWjKI1QdMmoVPNT0VJDiG3N5kSLa\nRY8PjMlVkFcGLdeumdsK3tyC3JDOw4UMganLf3zP/J5LoWeXXf4hWImZUKdb\nyV2d\r\n=1lAV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-roving-focus": "0.1.5-rc.7", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "78192b183096040d83757f22ddb07092de2eecbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-vq8D0dV/7fMxS2zhXJZP7Qbkseni/Tdif8hkZpuP8EUMDcCS453HR3sVYYPBtfsxvJ8kFDEiaMvX9L/yzKPWUA==", "signatures": [{"sig": "MEUCIHRR4Q0+YDSY/ltBdizec/N+HyVnhQxt1MPfqUyWjb9GAiEAiyBYp+zHYDcvYJC2uSUr6L04wtLrfq4sHTvYlaB6b80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sd1CRA9TVsSAnZWagAAhlsQAI7Wb/fdQP9BjW4cT2Cx\ndYQp1u1yFDin3u41Ow9CNCrJ6waIvUQJImRALTVhYYrVxD02F8wxqQTq+1iN\nV6pvbufe1n9aXuwcrCut/0D0uvVWeMEjm7l7+pmz0WAm8csKO8Vorhfu6Bsb\ndrbnvwov/t2kpblQLmnBrgsIlFnwS2SxXMBXYAkSGfBUJxaqWXII0Jkf4WW5\nJ/vqsYuGCvPv8lWXIHj1dhGo7XdBLT/ZAHm88JCffD7QfnccwCqfX2b33uDC\nPznVhLJFb/9/RGJ6qRKRZVZR27pnTQ/6rXzTUEXwMCLRcXGC7WqMk4uEcAdn\n+cknMS1wV/pEF+0RTF3sdMgt55AYSfsHfYV1XARNBPzbRrquhC0Y9y1X+lku\npi9mXs+gxVc96GK0gfK4HCiMbpRidKQb4jP+rUlplp9r3t43FkLOWmxxPsLA\nkRf5A8o2YUyaNUHAORdXZCaICyNYMAr0yWcbcXjzmklrKVN7J0nweeVXEqmI\nMZR3HI7glcpmeBONORcEStU9ap9emPgh79V9nyRUTsRMtX18sNbiKKZJgktH\nxM6zd3tJGqcElOB5q5CsQrgVSqy3VIZYmR17T9nyFHth7SnN4RXVi2csIwE8\nFCbwx9lCqQzYyW4hPMCxU/42BTYqvFt6PM1r8dqq211f5rfUcdoMgAnu49RX\n3nMv\r\n=JAcy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-roving-focus": "0.1.5-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4359e716d03a3735eec3b41a80483f2f2cde1880", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-cf86AOQsji/k3ARsRGjjK59MzRYUCa48FM5CZhOP+vyKXFllYfqF6lgV6ialD0HMKNJA55x3GsJGKkTl4r2R0Q==", "signatures": [{"sig": "MEQCIAPnmKmqr+rFq1YAReJWmN7nZ4nHovJeg1E1mzQ8DPtHAiBHvjq8W6txmMvek0Dlw5+ByuhGJdyqXmA6hMk4MOsemQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDyCRA9TVsSAnZWagAAZmEP/AizFYQA4uwaPR+bppYM\nvLOaAMwPIBoFjnzj6gi1F0MkPSo1Xc2tGubx340m6LBwNNNCoAO+BBkxWb2K\n1oM0cG40XveoOLAM12J6BXV8wY7A28KZS2eTe3o4Djx3Jdl9kzXoJ0qnhpYr\nhISR3FdtIz9/LBkgztRLBZ4nEgvvzguNG7vh3NcY7k0Ozba3DXnl8JjLPVnh\nw+mO/pAPf06jqWrqiGunUWAc0zoax08rQVj5Y+mqMaXsGzjcDZQxDhOiFa5G\nlFY2ac+y+BeEnWlnPK5gxpCJPDZaC4DGExlCx9q6l2pavItdRsPQD5lc2l7G\nHJ6O0Nv6w2Njl19L9KX/8LMLbkWlKOJ5o7gBtGU6uqxbfMrxncGDVEZ4BDaS\nbS1hmuKYwNuy6d2pKimLXGGiwORN1X/ctfmaOj9SVo5yR8jKOoSufywTEjwo\nEUozksHouK2A2pcjv8Uk4pPGQqD77WGkcOD2HxG0WDE781zOVco4qSFDdelr\n/TPVgFRslpLddeqhW9eFWNFDqgV6WbB9qxx0pywT9lp81TRcYcezto8OYLGa\nSAXzlZfwoYovx0iwAWaBimmx3S3Z07JlODEoNYeApOEUZbMK+UceHHgSJNRB\nKpJtSveViEr6k3Yx5YE2jEAbk2VQGRK2EZAEE/wdqTRkW9BNUsKKk7d7Zuvd\nnKas\r\n=iuOh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-roving-focus": "0.1.5-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8f5b6d888179fd09810886a345d1067c84046177", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Vhpz1w9ItbIpktOzCo7h28IVc7kQeXyqh6LcSvsboLEW5EOyI8Z5o5skx+pMiQpl3UgXJDyIUCdSZpcA+IxEvw==", "signatures": [{"sig": "MEUCICJ5LVN6Qr5+kWJbu/n+eHTMuMG8P7oDsf35lxV6G9dwAiEAznIOU5L6nWmYkLOqtPI3M4ZDP2o1uaSskCjuzlTdm4Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLzCRA9TVsSAnZWagAAlj4P+we2hxuKlXfAZEtohKtB\ntfxdb73MZOGJ4Azcn7vYieGjeuJ9MX48vliT/ytP6mJ8L89HYpfk0Aq4DX1c\nMRaz/JEIzK0Ol1N6mU8B3BSJR79TV5YoAxdkzKC4ZtpvaSUS+jIl194xgQk5\nEFxjeVtTPcJn76ArwwNuaXC8kGZSinAcUQM/svIZOgDjMDCzOAMb6Ki+QKTk\n0yaWx7mw3Tfx7W3WU9vA018cwtgE6IzPozonGc0z0C1FdaTe9FZ5IMqg58P4\nzBtQq9jS1ePiEYvEOcZWGuTTEekeq9ABoQz9vZPB0s1vGZ06XrAmfO+cQ5R8\nQgarlYhYg0/Q+wRAMAuap9cWRZg+yuNecZmW58pHiwsDVXWlJ7ou7+3u10Q3\nMKLAVIfQ1p78Yr+np5oBnBR0TfepO2WCjo5ekLJaypXDoFM+BrQ1F8bQQSoG\n7nX+e0wTRIqxEuFuKy417HRpcNUt6PZQuZQdZBFj9JsQ1HHNm10UM66gb2lW\nkj5qFjwaO5TQbUPERW9u2LkG7RzTkKx9p9sMrC1yLbS1m2wx08DpbsAa9N0q\nv5ELz5+zGyAvydb5T4byjfXqGDueYs9ysZHBySYcJrT+SR9HgW5Xdadq5foG\n2tW1FiQhxuF68zes4oVzkym8Nw4jkwZUZCFvY1ff7tm/b/tgasqhdQqXveRk\nPJHj\r\n=63xo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-roving-focus": "0.1.5-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3bce93722a762f8643cc246a08336d93cd4576f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-8JXgEhcNGOD59XaC5GJUhB1S75d/TkDrQzgbAvg+haDHhMsSxxWPa3DAD1dp/GKR9wVft/ZiCETO6gjBY2+lnQ==", "signatures": [{"sig": "MEQCIFDyidsTV2lGdKa6tlm1dINyDXcZx7J52cQW4WYr/ICFAiAXmAahssihqD1jyy9CpbQLGiAmOzvl7MWQNAu+92JzzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D03CRA9TVsSAnZWagAAszMP/27md+zahtRkseEaqzqy\nmBekTMAS3FlJRBcu9EU02a0BiHD/7Bgk7xVdaeRV6NW69QXY2Acb48WMobkL\nKtp0OZibFlxvpiYNCQ0bXB6g2dyd9dm6VTGmNYEDcx29POiGbV+IuYBTGNPb\nKMrObdcRaayvn8uOjEvafcJ+of4tKgbZQ7zgOcA0s36rL7EuMUXOQ+qNKMgD\ne1knyt6y7D5z81AXIriUeIMmGhefr7bpq9QTV1FTscsMTa4XcD2+pIW38KuK\n5h7sS7nPGb8nrVLUCSdO34ucPx/Dv1Gs1pOknd8c2P7bDGfZ0bghsxatMl/u\nnQ8iRMqZwcuxNl6WSdPFi/isotQ2SC9WW7IzeDuPdAwlWuvCbkXJ/YcN1gHq\nN9lUV79VuhZFpBFKV/11a6vfuLdXdS5Q8eqigxarQwTmOyu4aIKsMvQUNtCq\nHMsCBL8eDWDYuP+hf6nS4SL+gC7OGCcGn0DA+GXTp/OmMrHMn+jQ/gunVa2o\nTJ71dVwe75TpqMmRmlFPjglKUQylWxHb5afa5Iz8K5aF5ApNzaekVqn1Um47\nJpuKm7L5shsJBP6Shl93+jUfn6abQZ7VoZcKa+ii7c92i3ljk3Y6NDAf3hiQ\nMRtL6piGzhzxd0MrtWEUAB9fSxEyqb3SuSMps1UmnGtqGryh5cExhxlXZ7Np\nGnIK\r\n=dHFo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-roving-focus": "0.1.5-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "388938a175e6e6fc6e9a2c2a65b9bc4c9f4fae9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-bC0hViiFw6tEyTwk1YQG7nmP1Py5vNVRvMS2ZoangZaQmCyazj+aHKnOnrdstnaQYe3asUvPgz4UtHok7BwfPQ==", "signatures": [{"sig": "MEYCIQCYkhDHP02HWYu6Zzc1ylP6e6+I5YzAN45YP1F4JYX2IwIhAPCQkSmzscYk8zMTWq3H35VXM/lM+5+0E/8eLT6zpvfQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8STgCRA9TVsSAnZWagAAzBcQAJHlqTSxFGr5lB9yt8WQ\nu55saJ3JsRoXilOyZ2fJAO+Kt8qd9FS1J+5aCAIDM2dsHd6CLAvI/gmGWprd\nHqydC6zFV52tazZYkfezx8fnOK7Kee3+0gtDo7BF8yqZWyrvUtk7CgxzKvHR\nLwqE+WDntjHWdlWByfkK3KlXLXahqN5yP8WNtVixzP11WzCi1y9YVKWLVUai\nj1t5JsKj+PE/SDHALKGmqUb0Mt/dteVQ6ags8ZnwpVog8g14BYaZTI9/gLnq\nYLDgTtUmYG3JBNFdk/DOuaIXFLiBH6hiTeMMBH8ItFh2HaAa3GKziTr8du5E\n/k/o9hWabTDBccOGJybO8G3Ci+tlVGc+TNJ9JVj9m6404MEqSEjd2MYkjexc\nagq47Abl1pW045mjMaOF3Miukdy/0xxrXZK3zSH7DjdMkfMsH8uL6N8GN2kx\n0hQWfmfL7bdAmbPlP9bQkBnKm1BC2wWb6gsmukpIiv9wE2SKgDRACusx0g0p\nL2UcTLGottE2CxZPdAVMSi+6UvoBALqXMGMIjZy2uKp7majw5GH6u1R92ACt\nmeoL/YUUqcv/FUi7tA4wjV8nOTkduYt4pooWGhlmWq4sL70i0ilDLNfFoVVi\n/UKGBkSEbOeqdBRL/HhlwfaRFp/dQFcHAdlAdx6IFXuHLr5mtLmonacvzG9r\nxT8O\r\n=C2lh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-roving-focus": "0.1.5-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "abe56ed6af366753796acd6ccfb6c44e773f61a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-1PHW4hu4P191XiwH7GqHHOhtRdvjSi7e198ShxFzbeaLE8xKxtyV3EBiW+y1hwDGUPQT5jDanVvgswzG2SjtrQ==", "signatures": [{"sig": "MEUCIQDkYkXRoemm5xoGFR05ILa+TZ6CHX4++4UeUBpXYl/U+AIgbsk7lOfb/6VxHzlfZ0AvfmzaWhZM4fvW29+k2ryR/Zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DaZCRA9TVsSAnZWagAAFyMP/2JHKlCwDDNKcNcRrGQc\n/EHq8naVVfjxMIzLL+R1JxB0hdBppP91t3UHjBix9tQB1ph2lXrfWFTRxAzF\nbPyi/bk/1990iVHTwWuZDqCYznp05FcevOW2PYCwJJwwzbyNd0NmtmnhYKFO\n0nDAxmWdG06fmxUW4PymX6Q3IuTFkGgcLQb0+7FubDTM3eGCT7PtF8efYrkM\nGRtmYhrZZQKhTgeYBWtgSbA3QQD1ydBC6e06D5avx5qLPiGNF+STNzr2gZys\nuc4BJnGN3fHgsOc60eJFSmP/YoljRbNnPnNLWxPk5eNth4PEy1zK328UqaKq\n27HixVzhH2wS9ekutb79Ir7ddjAXzS9OVYwro+fDOHpfLSHX58lY5U7y7r+x\nFYXKytutOI+dzakMXx/SjBrilhNxUAN0uvZ/1nD0Ph/3te5IWLwyTbeoS7te\nJbDHH9MQLjpCQ/BGbqzMO9sWGanQSvaE1woIkkTvZH5wgVPn+2y3JWprrNXd\nOEz0Mb5h0GVsek6XME/PgTs3nbTys+EoZav4Dbl5mykoCnkjBEPyIOA5QZFM\n9x+RPY8WfmHEJYSG7OCm7xPlWDJfFUAMceRDOT1VKh9rxYD62zWJx7cfLNAv\n3wuCv4KQIC7tQ3ahGDXdBJ4h903AIzvcPIwNghu/Xj7D167tt+7ZCoVghYJc\nE5lU\r\n=A347\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-roving-focus": "0.1.5-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f45a7214d21661223a1a0dd4f0af03c4253cc81d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-TFkUJF5P8kwBPD97atYVNsEaoIXXDwGGmfKZiMwBVVLYuNpLCIR+wDjFBoBGdhmyZuVRUFUvXGgLHelCkdBiNg==", "signatures": [{"sig": "MEQCIEORrfiQezUH/mysqZ6UhsJleip2/b+9LFc68ZAh6FhpAiBubrdCxVK3B/2Ofu/YV0WFo7zr1lSWcMx6o/1SHcu65A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WooCRA9TVsSAnZWagAAzAAP/i9bOVMJ/DsVTed/3kmh\n9m2ZDHeUj6NgEXp0XugUaiITOaoZoGRPWZ821oHRWb/LED/K4FEa9VRpURCV\nEfIWQaET72jtrIgQYlIMitjek0ommAK0/GR5hXqfPq8q4pzBnaw3t++ZJnIo\nhd2AvWgj0+wMy4ZetCBB9roPA68kqx9hD6tx3NZiC+MeksRIm/RjqRwRe8tP\niJimeAU35JzK0sYbX6ush7jQGlhW0JTQqlb1mnjvSmcoUaOyhYnpGlifF+2k\nCOUbeQc9aC3VOExzOapuk0cw1/sshgp192L6GkhF9zqmxwIW7ZEqPlFxmjR6\nIMFH5NMOhp9cqK6ADjg5JUSDJVeY6pXEf279pdsi5ZC0RLsSrROnbOZ3Ciuc\nm59RJMfDJwlsSgsNMh4dPcZGC4tCWRzQfHyuXRbzqxPM39eu5AbgWSJOl4SC\n28CtHny45m+mzt/jqkQAutgFqyXw1TI1t3g2UP6+n9z0To1x8/6pVZ+okUej\nGfrtlnsU5FvP06pU95voh0Et4ZmdIahfb592O60Wyxxba/oOKimHT0mJpv5N\ndizWOkaNgsUu/QqEUzhIsseEUwRPaqdPwoTCZa6xKh+S9U3im26bmNlxHQ62\nuptzWV+3t7XQwENj48J+32OxRUOKVh73KmMW8g7+2OcvYa3ew53aLoGMBny2\nI8yu\r\n=X+e5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-roving-focus": "0.1.5-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06755f4689015fd9f4f2eabecfbf66b8c9fb4c33", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-nDqI4icILuOyYCQJUR6/Fu782oiCUCY35z9TDsZ7ZHUtpw2FLno7OMH37wlHqXeH3hR7LkTPb9/xX2mf+Rn0Fw==", "signatures": [{"sig": "MEYCIQCJeNvQrYR1qwfEd2Xn6TQNISWKQePk/qW8Gdzhb6QIGQIhAIoge0NVO3FFARKGSxShPp4KfCunTGkqNGmQgrJeEomd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rU/CRA9TVsSAnZWagAAGx4QAIyd+iaCGHQi+dRmq62z\nxaGJ9wU290Fioo4FKbiKrmPNOliX4Wkx80UyoXXnviT1R6FEwAnCoBlzfE+o\nVFIxhNEWnm8HkizeFIWEgWX8/47Zwoym/E4L9ykr68sUPC4BGLrNbewmfW+F\n+ra8C21oengOJYx2JVnpwSaRCx4WBjJR2MolCUG4XHIVtCEc8cgo4Q16ZkdB\nfiNC+Zo4+qxmfgS2LU6TqvP/hZvEoCKlnBPwqoOIFfNHXuQZUR1Njg9QdkyO\nN+F+Ykop/KMVm2BuQ+Jsh0XlIAiS7P9DLUFltRw20zUPTM74okQicXWoLyd8\n6OFT1g6pf0F5VRzLKJM1GptVAjovMAcIIwGN4l+izaY1JHZ7bf5YbKw2+Mw5\nMJhgRleWBE+iRcCJBjJsO75tPLKZTlVrdxEfxniL8QY73RF+hwJN6sfgKRFw\n8kFM9xesrc7E21JqG8LmgJcMs90N7eg6EjWHbHzjNHCMrrYikf/SuHg4s/U+\nEDcUY7iyCrQApaVWebpV5T5CC2bEd+JcTnPOJNguoJUT8mojX4UzsaPdlxpk\necRDMruwp0WKi/gRUeubchDGXwX5MOFBTLkUgdAa4fhsua8EXWNjUEZa/XVk\nVJLaEMPqc8HseUW6pG9pr1OcvU94yyhjZL1GiW9qHpYt9gEAWfHOk8uk54DT\nLC/e\r\n=qBpS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-roving-focus": "0.1.5-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "93baf39a36ff17ca38750d00c9aaa3f1001b93d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-Grydmd3Yf+Tvg+sQVwhXKSc93szxM0r9grAazrxNu/BSX1hhf+GynInV04MUTF0I7aFZnsPyZykc12i316vJ4A==", "signatures": [{"sig": "MEUCIQDFwYydSnFFANHQFYSc/ts9Am4ZGQbtyfW3A9j3H4GhzwIgScc0nUbLGeMCRzup883eEkLf7L4DfnaVjlaVpmz4gIo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/oECRA9TVsSAnZWagAAOA4P/0R/lHD23lexPkrd3SaZ\njmt1d9AdbHR87/tfHUKdGWTElqEvXhGQ/rQX/KV+hKSz/6LOn9LHRwHAd/SZ\nsX9qBCSrET+CWLTNAx4Mttlu4rDRDGhDzb8LTEj8VkxUNGVGm+pYFY0+dYCm\nfcbaogf1InAx4TPS6jMAs64niGAox9wsmR4YK3j75utpJohXEFodhUn0R3Fm\nKYQF2j/j98PdaDM+kD0pKEzLzisRz0cn7d25SYh0+56SkUE6LT0FUuyLqvp7\nRXrqH9beHMz9vAH1iaygmHtycQPGymFBXNEF7U7OSgfa2383sTf9AA6on/wY\nboH15ceY6d0ShhItx20e9YAewQw2eu88SleAl62U9fZu5cGisyUeXQj/XVHZ\nMeynhMD1CYwbzr7H2m/SJB4njSTkI+hrS6oePPBgM5Z4nHYBqeLcM1QE1hJS\nhz413JP4/DcGLkTiXjEIe4EZxtbq7R9hgYfQPggCknix4cyNkktz3zcWeSPK\noPGSsO+ACbMF6jCFShIZG5uodGYvWGYQiTXLzBYPRNNK/rsXCIAR1OViQQvv\nerMcQE+ysIVVS2QPpnTqgy7eAvqObVVOc8BbTQ5wKR0TwwFsiIShIwn2cOnC\ncwIrx0H/J0Zc6tOqIwp/fHqmFDKJ5UuwtkE5Ui5bORhri4VZ7iVy/iBpv/n9\n/gi/\r\n=La4+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-roving-focus": "0.1.5-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dc5c9e17029a4de2fb0576b5c76bf27a4b3866c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-5c7LFU6/D+d/Rf5dlCkZ7ZxN31OOHQU0ddNa1KsfVJImx3aRi5jxtNxHHL7jA0Cj6bleEjuGvReFhBiEnxq9pA==", "signatures": [{"sig": "MEMCHwYNoH7zoawQUgxpLkxiNvrt9sbZi5O8scuVPdU0ACkCIBUF2LgtkjnpRIurIZfauts3Vmg750aJIFIr7ylDGmNY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIgCRA9TVsSAnZWagAA6LIP+wdt6RxD3BFqlkhlekvB\nPBThjU5j91v0tEmMOxmDEzrbnGGgvgGcXZsSmfI8/C0KdGnEEHyXbWuFlQVt\nh4pC3ex0jnKOVMkTL+vsDi2xq2mszNx8PbfBNr71xWy7nC+F9NpChdF3EXba\n7DGAMbz6xozIyNWOTz/gNvw/mo3rXhgB6xoW/Aba1SPjeN3c6Jrfk+n8VcEk\nVcykxHBLHU4W5DyLw4UazMt3+L806mpAImbu7RC5Uofl/hX6MZ2QAXLDffvL\nxQLYGYVqeCVvUR0y/A6E9TWHaj4XzXXS4AMwPfiTCrmZNX+g8IQRqXxG1KgM\nQqAG5lorBCXRIt3cv32W/iR8Xeq5NVr7FTbB/dDyOkbuQvnaVlVXrZ7Bg10J\ncHdMg20vVQjJjbJfLYdUe7q7FbfWsbBFnE0lvoUUCFiQCzzFSRh1mwKvv58l\nhdR312VdrsMca3aic9VQglUExkymlqm1rVDq7qLeXguxdsbfjQT1uyPGNaiW\nghqSac9tM+ThW+rn9rSu/SbxbjIqiV4Rgl6YizzGIhTGIqkt+VEbTfV08X7F\nneilpl8hWYCqpqojzvYcSQH5oSCgAHGAXEUyA30P10rIu6FESzedD4zNwnsE\n5bhqNI8fXl+u2znPFjMuiIW1QrBCHpcWf6hvkFPktoVQz/Of1+07Bc0vJ7VR\nU3pr\r\n=LC+7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-roving-focus": "0.1.5-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b6610232ee8e4075538b22baf4998a98f8556a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-vA/o4CKpHgocNzWbGXOYUGFhoysUyjm7XvmhYkJvUpbThJbNs+tYmABRwYr/XRrA2X6bch2XG5aZSheZnbQlyA==", "signatures": [{"sig": "MEUCIDwSNplhJP8PKVuldePaDVc93NzSu58rIE8RBaLZZMicAiEAlgq2nKwzO1TW0bDnfKTLEF2aRI7IyojeWux29cy4Xgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYuCRA9TVsSAnZWagAAwioQAJQ1bkn/0dAr9a0ROXie\nj/Al/M6IRVMb0VdR/sUDWuqTLcV2AtERDosfqjC6qd870KPuCrwBWkZSLZam\nxpuoRJHjxqqYIPE9Ha2zXg5U4bY9ooVUUlBt4dsSpqihfj41ogpRMlSFQjl1\nBIl5OmVZ+CD0rb2rlM7u0GFWnz6jvXLcpgotAjDEKrWue2ktIaG3cy+ymahW\nHJzaezWTOipoGyaR3JS7PtXhzfrGBIfbSw9aOGmQa7sXQL2a2uAZxK1P+Jb9\nRUEc7wXqV0Pwiio/OnbxejnaG5SifGAJtCTPkbxQuiLVibGq/H4pKUbFZd8S\nSB0ZQhFZRingGn5wTmkvCDvzG2AHoU6xcWeFLnlkYbWzRlt8oWxDQjstHEZW\njfDy/rqWMqDzm9MJ1PV0ydGXIXlqYGS+IQqvjmckkOJAuD3QQjsro1W5d4hs\nCD71FAhzwbqLCfAtj+L6kY8EwphBW3xKjVGKYnrznKYyW9i/MPhvw0Zvey5X\nWTqpCB+DGw2eH5cKp8xPaDKoDIszOTS9EQi6O9Z2F8ggOisPp+P397rJ5SU/\n48lB+Nilb8k0oeAPEPOTaVyDZ8e/b/MZdC0YMO1B20i0wA7X2z9y6OTB8DEu\nfxFN3NHv+j4F2+cVkd5TKuMAE+klZd/tSoyv83aBjjc5v61F9I2wrvY4rvKU\nhm1n\r\n=UmCx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-roving-focus": "0.1.5-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3d550bf9fe936418c82e5b85c61b92b8e4f87897", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-CQEWGSJ2+8OnyGioxu0b1Y/S6zMP4UtPGIXtxjW/rnOXFBp/WyMQSEmwy2uiWIa+zwySM/0BBVWclJaUIaCaoQ==", "signatures": [{"sig": "MEUCIFZ7hAJUOizVGf1420z7lZqs1i8RtWD/jVOI1D18YR5IAiEA9soeVVlUSXrVCV0Ode2sNtKHfM1G9LfdZDm6Xk0pI8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlltACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQNRAApQhRMcl/BTQPXzWAfgxyka9lkhpyoKjsjZj6yBSMtuk9DvOW\r\nZJN9fAHaobd9mGGww6h6h/6iaZl3I0B3xjCn5hHMCpZ7JXBBrUaUJfIFohrF\r\nfmrkfKHhHACMCJSH7t7JYrEJ2FGN2uawm5UbIKEGn2c+d7a+ojlR7PLec+Lj\r\nsGWt0WmYzJHPUPfYOp1gNMFyUeQjav3ikd+SIob78ibFw31/hngt5iU9Ymg2\r\nq2jskFAlpTvNGh09fjaiek5zBTNIj4A1fl2vHloFoPz2PaShPTet60c3IC0H\r\nMUdjFE6weqtXIJXW3dXwSXvdCaBRxbHS8h47Qke0MYD/0Imzq0ZcBpFwdIdM\r\nRWk2O4VGjm0O0E8cfIBorAuawCdR+yytKCjirsDmIaVf8lWjBxshrWGvSyOB\r\nZg0OQfz1aDBMD4UNVpnFdSkdntIWz2dtdcH/XVQ9yu1OlKzTPsPIshcLt7jR\r\ngj+UjjE+WW/T6f/8hxNbDiVBLLGM3Ugfua4/OUL0wj9QNQ4Fwhap2NCOqukm\r\niswN9VRfPPLTu3tOnY4qyjN1dCEvRalwG2aOkirZYfmuEjYBb1EKPGeGw+Mg\r\nSKxfIHgwDJvCR1GLNDFk4TfNVouwu+Rh9vTd/L1SUImaMgmEsQrmRuG5tS6q\r\nt9rpI9cq0sNOiKPKdaojv4gg/Km5/JmzjXk=\r\n=H0SJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-roving-focus": "0.1.5-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad6320b912b4f1a30be3c469beaeeaeaa714e3f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-bHnComifNVw0OAQrmqtotanE6/W7vDCYvFHzDa7xkuK7I47alta4UGn/vbm9cCwe141S0aMt/ZUCa/TIlArK/g==", "signatures": [{"sig": "MEUCIQD3MuVi6kgzFN60LDdIfV2O8nTI0b0l3C91DO72sh/9YAIgZMt3pABHFWoonpqSCDIMlqYL+YHsKrJ5yVydyzqFOLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUFA/8D/BJa+0cnLs5NYt40pHfVz/YRD02ucW/TaVt+04qn064zMF1\r\nvCFsy0C+M4KWmy+Ia+UfruXInwvslNNECtrZNJ9HwctCJBa0P2anEsGss40C\r\nUl3+na8fjoO8tHaKgOf30WRAdwTWBbLcQax2cpEmh4dCb2Xgnbw25ugum260\r\nRVzJ/vvcj5nk3AkIvIxEjRhg2vaIZd2g0vNRgRjEAwWucoeztPCPqIGcTWcu\r\nYelTY69gxyzYcwGHDV//0/x9MQwlTKIhwsq74/GL2bKiolqSXP5YMKPF0ev4\r\nsPq/G2QBH8d54gloLTrrWHMFYUjMwswyuZYAB2Yfa53w+5o73SNPBeJLlnqC\r\nBGfxmmMXva89agYkeQzhZE0y5ArfAZST42Ijbgi3XCo516lxt7mIgnEzrBz9\r\nhvpXEdj9tXrMT1aJu+NyyTCBIwdZa1SJi0HQgwstWDEwrdmnyUv2qv5U4mWQ\r\n8oCLCtlMOYvLwsOHAheER2GCIiSsopLzbuGrUN2+cXiBoD7TFuZ2AAeeFWIs\r\nl7aLTcFq22ugerlL5Uby7BxYYJqyHb5Z2oRdyeGTv3YyQj8xwpdQRHAx5VTW\r\nE9QTrM4FaCRgDLGnwILUAbb0YkeUivMbgRPNy5x4gwAIN4tEhpgK28gZVjJ8\r\nnXNOlri8a9ERozJNePdcUva+4T56W443ZDY=\r\n=GHaY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-roving-focus": "0.1.5-rc.20", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "45221a6b53beb3cf0710478e7b4e0041631a54a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-65eu56HV9gDuYAyJ6Lj1BWjKeOWCLBOppwqpvXFZjJnCyJI/mPAbIfpbT8IqCY0Yj5A2RcGQqtI4ZTe38GmWLA==", "signatures": [{"sig": "MEYCIQCuF/cJvGiZulr88j222wCWUrRNks9iNvsnSqrHK9dNhgIhAMUNsDFrxfjux42o9ViMZ9ic+rldm1cfTInb0VJ2guPd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQJg//QYOgAWTi0OSQ6/g60NDun25oXDv801iYycmMQXGJnxMarbx1\r\nvExkLSI7z2eQDl1LrXB9vW+6UP5sJxYgLqEbvOzIu5qEV+mGfSvj3lTEDpst\r\njeK2AtlMU3VkwEGYtJj+rf5ox3KoaZrGHL9Bjzbne08iBdSFxHVPrsvEmVbn\r\ncW4hvWfyBRMoDpVGwu6loeNsip2BHjy1iYAPVZQKQVXxmpyccaZ2rZtcXTz0\r\nFbek3+q1hw+zOtplgPPdVC4N7sngsE56yfZd6Q0hjrEvSFyd51hXqI1e2ZmU\r\nQFm4YdfTAEYottwtVo2GapOXevjWcCcA9S9buu+WD98O0QBEbYbc3A+OSHAb\r\nPb1Q3/Gj8VsZAxTDDsOxlMalXNYuTb3HihJ7H4PWK9fya+gT1amT5hlgdMZ4\r\nhmb+hbTCR/jP/Ug3sj4gL/wEHerDKlwCjbHZ2nVWE5Y3qzcla+zS0tGBhJmN\r\n17QA/0WX9YBmnru//hs3fyvwuvd0bmjIchXH7z7waUwQlEQwZfLKDvecaAfl\r\nrreIG1OCF4eAAiuAJkHjIyXUr9wl+aGhdXZ30rLMoGWbw2YV94A4GNfUnNvU\r\nc/g15GVev7+Ie05PuqqBMf0GYqxiKJ6upnwFVZ2XBr2lMq4JlmCHpTTEbRuU\r\nxPv4C0MKFACq7DXwJ5h6ygnaJ1ZPtk9EyOc=\r\n=pwzs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-roving-focus": "0.1.5-rc.21", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1dbef58da13798fc3fb8acd804d98873710ae00b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-RaGOs3M6Fj2oAVa/xf+wHHtZ2fql13EScRkUy58UKdX1i9H0RskRDr+H20EtAUgyguu2AFg3YyhehJcmiq+Ybg==", "signatures": [{"sig": "MEUCIQDkKV2l7mIpHnhGMVRhWHahckuaE04rtyeIXISYz+3z7AIgUgMzcaigytw9Upf7Lg5XeQLlS9cDseYw4SyZ4/bxL/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXpA/6AkRueulbCJ+JB8jxrwmBmvvCtXfRcX9NSleRp6fxk48pjpDR\r\noAU+z1pt58n0EARsioSUvC9eAgWptYUrRXvo1vetPv/4fitwbvKHaCi0WDlP\r\nF0TwCzKxmVTST8L6Tvcg/jjUE5kxqZbhL4SkcnM8mtBBHgB4Fsp4mTXQEhhU\r\nnadW/Es1RkddwpsvreUX0F7+VFOkRpIyEiSceKGH0N6yMNO/yrGYvnsLvYji\r\nys1RKjTJw9Ozb8AHrplzKVWFu3TmqNUwUNN24ORx5L1YO/vtAbHVBVd7Dcge\r\nV6jLdz+IxyiSOZDDqMn7ku4W3j4/AcPk/5l9sE540Q1boWb0u3ypWKBbfuSV\r\nGPRKzQ2o9F1DkHLAT/jnrUeOVjxJ1FwXW58cj0bH0ArPb7CDmd6qsKkRkzlt\r\nXDeiCpk9XqYhpVSNcvxc5jKMwYgu1f15IjLOgBwwSCSisqSj0q68phrtsl0X\r\nQCtf+/bwp6TrO/ZmRVRjal1sFIcChAZW60SA6oe+HfcpBpd4AudBBsu5XL1Y\r\n4DUnsKsOEM0lfS7tJxpazv7NGxPj5QC3S7X3ueTmJT4s5KmBgG9Acw9GpZCY\r\ncpKmQEf2VsFmSyt6LputkzPZDx16N3yQ3+GzRiywfizU6KYzPEhSt/jWhxHS\r\nx7NPTGJB2Vl5b4TVS0an2FZvXx/3HOrp4SA=\r\n=2zer\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-roving-focus": "0.1.5-rc.22", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "95dbcdbc7e6c1187663b262c2cbc38b2e1cac15b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-Wkgj9FUJGP6OqaoHcZi1m3PedHHDwg0IK8R18sN4KI4Rbt6DrCsx/Q7fDumRhDbsNqsDud6kp89Dmf1gYAa97w==", "signatures": [{"sig": "MEUCIBuPjPQDShdZEFzpAG2zXMaaBkZ8MxMIXELMDvQ1vGHSAiEA8hx7JCPCZCbY7XsUUkkELG9k93ZbS3KWJvbQJgaSKIA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh1w/7BADdr1J63f6PGfwBA5SOCb81TIBjOaSDG6Pf5Oq7wgoALam0\r\nSPgFKSr79dRjqsMNbc66RmZESNw1JW99gS7YiiJrD3Y/iMM0MAQS2KPawggM\r\naWpNWn3USqaQP9VJdPa6W2VZyKnSjdauPlMcJSQFqHeuNaOJulJgYmka7wa5\r\nA0MG/bKMcYNyi0gkXA2p4fo3OHHioR1cvy6EjZQIyE0F82o5oohm+b79D<PERSON>ih\r\nhjH8D6ZQeOVQPVMN8RBjNLmQd6Pw63LONrhTF56XBK3uJXCpPYBIAL8GRCXB\r\neEQNafY1MRU6qzTq7uXgOS+E+Q0kNP4fbdSf0BorzAQ5bGvvJMcR6ynwokBO\r\nlbSCIBpaGp9Ve8KdpXbznt3csur0hszKj/2zQa1mHQ3DqDKU6Ti/lnK7enE6\r\n8PwKZpH0ALnogPqQ89cKg5b/zV1xq3+O1IXe4OIb3PsAVD5XMoARSQQ668ks\r\na1XYCEBqFt6Ei+rEDtgSSC3KcsQw8PzwkXaLJiALs8/XYx0MuXHATRwunH1J\r\nDzq7GsoZQkz8N07hCYtTKpJ3CXaUEcCKxN6PoJKB+xEfYLrKNAsaUgM4ONmf\r\nnnK7oXuI3Bi1MUaZuMU2lo7tOGXH/ANxV/07N4zu7qQIy8jagTaByqzxZURL\r\ntYByfgiIdL2zb7vq5QKPcU8Vz3D4siY9UEk=\r\n=zEGf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-roving-focus": "0.1.5-rc.23", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fd2f01efc9f0cb9e8471f3e03e425b34dae6d50f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-N4x7uz6F36XkYFce1I6BBsweqRWQJHwK/GWtLbbLnzV7FIVMQzJnNCqzTCOTt0Jihol/XvVjfz13oXyaFc0nOQ==", "signatures": [{"sig": "MEYCIQCj9j3CQixJfDSi5eHSCPQ8yWg4t4Zv5vU8gmVip9R+3wIhAJZgdx+lP/FonOwFJciIj3Q6jJX7+LaSmZui8iUVn/H1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpEGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBRQ/8CwOLChqF22Hyc7GWrMMbDTrnpfMe4isXWH6xH/tAtTttC8hA\r\nFKZ3qwlaTatNJviBXnSHjbU1rKQsVFDIziREpvyl/2724ZBEuwWMtTZPbSSY\r\n1PealzWArivfry0bo8I02H3gNT4OcQoVyTdYnRM7WJUAJ8CisKEQcCgj5ibu\r\nbAmDo1v3RPG/sYHOZxbaB2cH3aO5TMZGeGj0Br5Ra/y74cGqEztSYWrRxAYn\r\nreVgvAsLD4LpzwULOTBPclojFv7SJdTCTwWCeqGpZHL0xBgBCG7JZuITjrm+\r\nIOnZ3x/felSCnaGv5KsN60q0NhJnxf4v40Lk6LpIdDrj9G6JirTm1v6WPDXH\r\nw7jvo9CgKx7RdMufW/PLzQK2k/kN3yJfziktd+PFJcdG6UO1lb02bviA5u91\r\n3EgsHVb54PIwtnkt2hd6ePcrpfDNomOwSUNiBWweVz2iaWKmKd1mtRzlZuil\r\nD1ofuSCr7L6nqXOfiDd+VDlrEPOLmVEEq0FWrFWNrV6i+UCxgCnB24K7l7oh\r\ngawRQvmf/Tiloo+G97cHSG8BQLWCSNEh40q5+SGgM/rs0dUzFSatdpn3b/D7\r\nZ6gBwsfXhXaz1QyHRmWtpOxhw+i4jOmUU7WxQHZsxPyf6Hjv+0SE3j8WagEk\r\n9O6FKbk2GFmH3+CaLG2JMKd16UiTD8JqmBU=\r\n=N4G1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-roving-focus": "0.1.5-rc.24", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5ad49bc71ff15aee542c80c1e5096e4bc8115e6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-AEEEeDKomzK+Rk+TMU3/G6bFwZgm/kpRKNJ65W61+X80KYWXNVRuABtx4It1Nt+FR8uhlm8zX2tgp0hvqsKIPg==", "signatures": [{"sig": "MEUCIFUioHduIPANAdMNouN4P3s7NOxh+GGWDIPifxQT2/YgAiEA5163R2sJUF5pfJjvYyXKiKRH3omHWUYnmH70h9Sow2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoiSA/9GuGOpn4tKa8YvcXayGi+5veNTLN8fTcfOLDtgbKuiOVtPBeA\r\n62Dt4rLCTB1A05Zh+QmWV0Ml6w26hx9zGUI3FnDWkxl10e5Lr8ef5ysGkGPy\r\nvUYHAgDPXqvVLwekagFBeaD5qfsl6e3+FLXvYBZoR+88rEFSqPcRDm5oeW97\r\nIbn8khUJpvKRZlL195bVlY3p94pYSkCBpfLepWsRmtKS5QZj1Y+69foM4B6H\r\nWOF9fTZapX0jY3fPKLtLgAw4mVll36wQSDErJe1STOVx52MEW9RATMcH5Tws\r\nRiGaALauqyJeSwmXswmwBg/IDsZQqWimxwqvmHaxlLNEhY9UPsTkU5qJPkVw\r\nNTDXHFuREC13T7MthJDjsgYMkvpMm12sl9B0bF5SqZ3Z3kl9gEtCy8JmHGCn\r\nbzr0CHmdeoKbMaMadfCr7dWsy6ak6S7zraUKbWDtkBgpjugK+r2a1RWnm3bt\r\nk+46ErBKJcEJqAJ2HcL6EFdPXF8h3reHtQECUJEl0KhlIeya8tHPQguEn8Ou\r\nUk6qIoKp9x1lA2Zp6j7OTb6Lef93pmGTyU3n1kXGqD4xioSsC6XUUNDgPuN7\r\nK+WsxMSHrviITu+YdIHY4AeV5cyUYkwbvOQkMxFktgVqXAV7ibsycSFM3D4o\r\nX2FSpBPFOrko9SbnzK6GL25c8N0FgAqO3GA=\r\n=RIbU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-roving-focus": "0.1.5-rc.25", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a0f11a17ba1c577aef92c2a0105f54b684cbd39a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-HlEYCfBzon1H0zoynpmi5U5HAhEdBAY73mKyhbKjS6AWzacc4mDtHRT3ABOox6Z95g58HGo1mEzkCW//RaupTw==", "signatures": [{"sig": "MEUCIQD7a54kO9e93jCNAOeowpYfz0+1btomHAQgujOj0gBo/AIgSPM4mE+HmIy7yPfNaDyaMXrSPOv4jam1UI0ox6ZBFWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpELhAAmz9fgkXLrO0Joy8jOcgMozAdJBb0UOj1TtHtJe8DICN8RuDb\r\n7/VyGI8B3iMB0BwNNZy3AfVYn7MiPs2cFfXEk+5xgPsCb9nC8r/aAu7jpOzg\r\nwxOv4e4lGOUSVDOEIfadJGSzMwpg1JOa5La76NwVJANk/rFR/Nl0vOYiY7NK\r\nvZR8AwLZienHRHn4cCXzCfM7ahe4MjPVyr8r9hRK01FIqkZbFUnK1m6b2WKR\r\nHjeId3HxGjf8Z44i2ZiLv5PMlRuxJWMESZAe9tiaqFS6A/URwI8n5hvddE7X\r\nwHMk/7M8gzJ3NHbll7f/FJvuYJVCWThx4/3wvxM/ugrMvsIROksjJzjun6I+\r\noMI7COzvxzhYx/qxtuT1r/BZ72J2xZIg4oASdIOsAPD3bTqASQIQs16USaAd\r\n5dSGa3eRKZBZBpMdNPLNHdW0UjqGXuZhoCU0cChHVOsw5ca6qkSTRobfNxn7\r\n8qr+sapyjjXNJBlkDMf1HnNuOFCCNNwtslLHmkDC6qr11GrT+ZMPsGY1eIxv\r\nghimujhWvy+3CO9uwjPFN/hNFKwfurm6iNeXZdEZ4SWwKC105IrPrfdkkosO\r\nxl4xq0fC3h6x54J7HgZvXCfZqCJedq9nqD2ATQmg3cNTnQs0263szaQ8G3PW\r\n9BOEzh4kAKo4Vi2pR5ONKjGfD343y0/Lm0c=\r\n=p4E1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-tabs", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-roving-focus": "0.1.5-rc.26", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bbf31ac8505d0f435e2a8dc84b2814c282643134", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-Ebzb99meBWNnFBlA2od8tpOIbJHVqMgbcYyYbrlsw+4RiUGf23kh8IIZi8PDNqreof5/AHh5+XSipBvVMNVaUQ==", "signatures": [{"sig": "MEUCIB5CEQtOLlpRWYciFQ4NS/0KXxb3NzhO012LiMN9u1GaAiEAxTFQes8teovgBFOeKd4rWIRbe2cnaaxKsT5FAJy06pU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Z7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2Tg/7Bku2O/DJyz26Er8Q05DxDKlYglIsbfIZUS9Os4Bi+AigPveY\r\nBBB/pkmU7hu71OR48B9cQKgbeuieTS4IrVdqd+VX6OPRVfXMlo9iWDYcjPYa\r\nfWeClwSjb6XmOqCSohbmSxhBWsXZJVB5Zx/7qvebTa1wt24zx8gPoY7zD3r2\r\njJFjocRrxdp2RtqEULV0lq6co/b06feGcKgSvhSAxdOorgLE70ta+3lz8Qja\r\nrRYkbZiHYOp4gI9f6akHxVwetS/mkfKrhUgsqdZAwl2QhsMbwXW/YFncMwoa\r\ngUu+GIEqfKz0BMbcIvSzwI02fKkf9QYfMgWsyg7Fwxh6Y+8NZTYS6IBpFBMr\r\nKAr7yom/hoqOi6ESeaK8AIkSAUs8iGO+j9ijQ/P/O2N4KtTbqQBMKU9H+Tjg\r\n2K0TbCPOKYF6YQ15zkgtargryLctoairTG5QDzoDTyZQhR70316ATeGyZhmE\r\n6UjqTnB8G0D43UGt8T+ZtX88eeKD6dlTfDaqN8aukJKaUFx2yqR5MdP+KYNs\r\niMpM12Ni6U9G6hMY4w99DXd8VE6xMXB1606oeQpNpSm7XUSdEoW9CXku4Z0C\r\nLx/xa5gSoCsoXAw4Wb15ine9sDVXqJNTgEmdqqdD3RgWHSjfqt0sHPrpio8R\r\nvgHTXgokPbWAMTUYxkM3sk3l1z1fwlY4Ogw=\r\n=PFum\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-tabs", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ddcf860cc32e186d76477ae767dbb216d1944252", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-ieVQS1TFr0dX1XA8B+CsSFKOE7kcgEaNWWEfItxj9D1GZjn1o3WqPkW+FhQWDAWZLSKCH2PezYF3MNyO41lgJg==", "signatures": [{"sig": "MEUCIQDl1WF+X8gvUNCksvycNnhGSreZB66io82BoyXuVP13vwIgGf2oPq8H5tnZOLiAc8eauOp5mCz/FJThFXFqgXNoD6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodeQ/9HhL8kslyeFR/MrHQfdl5WLcpv9wet1xV9PA63F8E4vI7lC/U\r\nOst3AxW6hIbPe0y2l3EOGXf3gH+YCfpF8j4nAUapfryv9Tak5JboHwApSjJU\r\n8V9RMk7M14bHsbF7w3gHVPJDrUQaZFkjitREUjSKjlE/TvarirPYfPm1a+cu\r\nu3TlY4+6+aMeadLiKF7hCg39UhclBxeSN1mwEx66f0ZOJ2qWN5slQ4Pesnd2\r\nxuFaFavd5Lj/xJ1AJRZtaSRiMmT6Soi0ueoX/XS0NmEx5QBanwzXg4i3r9eX\r\nzjSc/AGIW21oBZrx+SABPWQYzlDcBhDSo7nds4uFPtYbkPXRlV/ujhnY46I/\r\nN99pBQvmPUSPux27y8r3OUQ8cANbMz3GkHwcE6b0pFAM6OABSE8pWtGa+Nfd\r\n0cwmo6ztFAIXcdt6qEeRxFKbL7XEvNPq2Utr1ibJ3s2BcYQ3lzuG37Vr9HlQ\r\n5ed6b99/yyKgbqeS6HM0BgrISw8RgVMJ8w7tWaZOuDBoWKcfZQAtljUlAo/K\r\n3Ne/d1nh+FVPH/tt6XApRpGOlxQtN9P1aEdnpUGmycW5Vvs1v6am13pPmeGt\r\nyCULCjZjGSVy9dUkNzjkhsRBgL2fVTE9IIoVXLWdfuXrUx2m//jIlhogZNIQ\r\nlV/c/50HUyi9H+S2ZXWfeVOAsJBs2l36IGA=\r\n=9o2o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2290fc1116e20320ce8c0bac693d9e9e36adf75a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-BoZHZs+ZJAHdz9HPNGpFwWa/T3tYnrLUjVQiTmleZU+YGlvbb5wB/2DaQJyrtBeg5MBRVkcINVhvrq/pdOBhnw==", "signatures": [{"sig": "MEUCIH/4ck5vRLd0svfEdZHEWX6LHrhuaZYg+oHK7MSLRlIaAiEApbOsY+6P7fG8nRnk/+5koHvhm1R7pfTbkojS1ejeJ2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD7NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZag/9FMOu6kWLOmuJ/x8NNh9QhagdQiBPhTNirDxe4PWCsuCFLlwT\r\nxRUKl1b0oMrRUqcPScVM9QJ5BDCnojhfXb70ZzX1v11fgZg5xZv1huwmD/rv\r\nns20EslU5wsJTsEc6izK8km88OT8PfCjMLPiXhOrAFigoAHtDeqZio1/jyA7\r\nIlyXnwKa/pgMl2UCV5OtG/f7KiCYhWB2DyqL5GwATsTStBNjpdZs5DuC6uoV\r\nhW9JjpZt5wSRZWhWzesV7/GkX+wXmAxYg5WILWGGvx+zL7iWNG91lC3kNzp1\r\nbBJD4h+t0/gBXWvsT+IWf/PV4b6UiYPmH1VI+LOkyiQRLON19ZCIK7CmgUvT\r\nLPYdgbaz01z+aYchWx6WAIDEIWw90vVoghCKA2SN7mmWWMqVjG0VSXZEkOde\r\nI4uwvkV0QhXM2m95rS8nRcfk/CvohIHzFdSMSGwJ/K7R4aLq8LfPG1iy48sM\r\nBsO9vf3+OnqpWYXQ3Ge6SdNp8V08KBC2FG/ImM8/wUf1y6dXMrD94iAbKrm1\r\nQQRZBotsd1GyeAFPlf6etfnf+jYRIBkFkAu0jzE/Nsz1lPExVQ8ddQEX9EB8\r\nMza/4D+RnZFazsEOR+Wqm/tn55kwe8Nl6QYigKy/j1FUMPRPfTlG0AYu/TJX\r\nkt519Hu793SD09LTQmuIN5CmZBp+WkOCHlM=\r\n=NwqF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "530bc1e3ca449b31d3b1548483e6a8a8a8acbfd3", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-6O5KQWZ+XqSDDoQoQgUK42xPFV94yyD5gOa+ALF/EmfLnzQNl7nCwVj6ochRkIqDlbsX9+AkIQyTr+vGemZSdg==", "signatures": [{"sig": "MEQCIDN1wlUlePqHmARRipeu7cPBGXL6bFQp56o8TTGgF+tXAiBukKpUiI2NxgAUEbRYN4oIJG9JkInSq+tJdtl0QwjcYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEITACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1SBAAldAPHevarCLnLGDR52F2EfPBD7YMVteoBF/0wt4gt4xAHHFg\r\nSsIjrCJFsvpViz5yIZETgH7zzxd9l1D0im49H3RhNwu1BmaKj9GZ/C5sQAub\r\nC8cMSpbWYPppGNYmyzi1JJ3/3eebMQvF73MnRIs80Og9Vb+W3sffeFBuDzJN\r\nfX1RYn4wdAFsguzrFTnmXGhFaauKVXfHbnHdRkPoq5w5YLxIKOkJISJhUQHk\r\ngmpRFpFKllHvvr2DzI6FqP44hiwd/9yHa2EWpAeJoyGSe6kHin48X9zM8k+p\r\nZiMM2Dk4A5VEd8nouxUjKASuVOSOmt9/zz7x7umHCN1Omq0qZgqafcLnnPPi\r\nONjHrpWnykLCaUCtBBVhDzVXb8/s3+GOol3dtN2X8A+DecPd5DJ8Vw6C6Xi9\r\nxYbvHbP0m8sHwEGVyZhH1cOq1V/pFIkF9seJkP5km/4b1sZkhbfpOXrUgaGr\r\npJX/AmKJj1RGBnApk2KcFnhYVlx1P0MvKQz8xjJS0FpKjUbV5MaL9mDLRDKa\r\ncjBMC2CCv/3a/y/D8t0/KOKkaqspw5pgCwFBLju9SxAjnMEX2++cG+6rMQ5s\r\nB0qGcWxQx0+AYodRYfur4vDtyoPbHdRWQQqCc9XcPK1iMV/cmsEDYcsH4Ltw\r\n+ARAyNpTZrMyAUvAmjT9n69pIvsW3N/w8WA=\r\n=HRA0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.3", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-roving-focus": "0.1.6-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "85157e6ceca498f6e43229e6a450fe7108acc00d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-DAZL6Uy6T38H8zM5oScu7urCS6SYWMZ1ERfKnXOKJTjIEF5/TRvp4tdaS57ND30nzf2yIGn6Jn3tdXE4PqbgRw==", "signatures": [{"sig": "MEYCIQCdh32uy7VW4AtTi6cDXb5CtbPen9qAz+AvCLhnjYXzAwIhALbZUZb/pp9W/s+FqHOZ46Rr0GflObsqyh+ExkLkfCxn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38114, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUT0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeoxAAkxyt6R0nmbPjROC5JV7dow9UajuoeoA4zNK5W99ZDAeFr/SM\r\ng8yogX0EGCd/7JJ1CfYiMV75/+U/H/NQal/Vzy9zPj0yNNJ5hv8vkgyKGh+f\r\nsx9eFpN3uilLVcfQZew2uEmGU0opUd3dSLcyt3lOSOQXMnaStYW993pXPjcr\r\n0f4wil5+fkYPdcIcYhF0pAS7Qa/oTv+x7hGtJA5pHuZY/EZD7CknFAFUhiUN\r\nJO8F9D6D0ljsJW9kpe8jCWS6ed1SAopGBGiVwednAHHGItYp97hc0JpMr7GK\r\nA3P5iZk0PJ1j6RGJigjzXfSRy19HCuMpXX+c1X7hbBdUqDTNgeeFA/8J6w3+\r\nKwmfcr8bDZiBgJkloa5j7YxarYrUkO7TKJtiOoHvcvTGnZwqNc6CaHIGweaL\r\n4E5SHkGxHJQ8mLocz9ZpqhQxE+mbkIPlX1UPPqbel6pYLdnqbdflk4YWdXu2\r\nTeKfk6zOwyUDpO3VLHJcjHn8bok9RDQ6KrDiZKZlUnO9Ros1YMjCS+vJa7qk\r\nIQ/dXiwNo6C2tQxjFlRyVLMs2WfEaM2BSER8rQ9Vat77StHeVvaczKxmthmy\r\n2IpV4mby5NZ21shjD3aRPSwx8FUmKhWGJvpZATaWMxCPZIXw2kNtBikDt/1E\r\n7isXACrX09iA1hihpy0BQFEAeXRyosFM4Is=\r\n=xpQF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-direction": "0.1.0-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-roving-focus": "0.1.6-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f7dfbb8e6bd9011e6011247c86f75562b0d523f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-rd21nCzCiDjtz4leb86T7cmYK8pDUmeRdde1BuHbr8oSj2Re6RzdC5UNhdMv3UF0qhNsXlWl2WiGjd2EKwD56g==", "signatures": [{"sig": "MEQCIFJygmby3At3EUlYqdMI9C3G1qMp9albD8s2vIGhJVAKAiAiV6jPqhPfh86b481Pok+B/iTEdyeWIBMbtdw/g5zg1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAR7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvZQ/9FizQIhhIJux0unDF669H4I1C8oLxIjwt/pVQdpnoKi6J7Zl6\r\nc9mVc94gI8ZTZf7cJjp0J3hJfWuqBFqZNtNUipenCcIt/IrM3szUksHCXkfH\r\nXmH8PtzX6NlCDwt1sGDK9oheTY70Y7dmfJ7ScS4xbFg4dN21sktuNxB4+kyA\r\nUEKHsUZ7n1kLA9soM7/MlkN26ff9XM17uVYbo+0OPwn5cOPS5kb13daCFHk+\r\nJznNnwM6Iu7RtReCC8IFJ/HYpts22dxNRk3m+0uaAjW0TjJ46KLRmSAFFV+I\r\n8OgcZDOKVxO1gY4TIq3bmJFf10n1pdAtoPvqn2dQyPU8y3vjDsYiCncRkYEk\r\nN4zU1PR2acnpwMlPZf/9UTsErKj2KgCXvz28f1vUQ8bZEcxBPk78hAc/gg9y\r\nnY8Kn2jqrLxbF/CL/3QrPpop3wRjUsgP3cAg3VXD56eHRZM9TxJS4P6maTzN\r\njtRiBABkyaHIqK93M0Y4xmFcOaQqV+UYVkVoa2+T1nXZhlfBcu5zoKMm6/te\r\nmL3b6CSfjD6q5zpBHSPlf5HNkmLx6a4MphnNf23t4/M8Ml7yrncPSUFDpxBK\r\nJFCJVfFD7RxLFwxn0Qog0LB6WMendTCSUA4hN1r63LWa65Dn1/8wQurrULsF\r\nxUpkVUhbeivjCokYLvRm4ovD2UZuR4001Ds=\r\n=OYb3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-direction": "0.1.0-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-roving-focus": "0.1.6-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e997c5e1b37262418b37a93282c6feb88a691036", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-vdyB6E0sBY0BX78HRFn//Gf+qmFqDfAak/yVG9B7mROW7/i+2oTF7TggUeX6UVKqWHqWTSpD0pAa49CP/QgKCQ==", "signatures": [{"sig": "MEUCICxvXoUEGONkcLn9SFza+gzasIynpdQ4UXTJHHNp1cytAiEAosKfFx47u2quFBmECc/+ftgQw5HKJzNi/P5oIENvAg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38143, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDww//a4E7WTFHVJIVXlCyAoi0Mq2hs+7NyicXbcmFMvhtHhOjJusM\r\nNB3tybyjd1TjE4GzXhaQHsxpih4kN/kDnTQ/zdw4PpsxpGFcUARom3mi9Zmp\r\nmo6tHaZgvxIYKVHcjgDo30V5UUb2cJT89/GQHx8GL0GhXSvvZbkEG/kV7sXw\r\nD/QLvLnfx+6C7CqQKr/HzPAWkIZujdIUh7zHQDQyKvEeBMxwGjSO1CAwmwlM\r\n5b2A/PpZRBc2rQMXJcxUrhryDFU4G3qZTJt3M4tNo62Y63AuIqkGooZYremF\r\nAnuhStzUv2xhwdOgAkcIo015OyPdGkeX9OJbEYUE31V4oZnjeOBtEV5l0Bo1\r\n+SMyDJwYGrl7xfTS8+GPLourAl8St02Nlt+bf6uWcyvFPcBsq+ennHXPLqzj\r\nn0gmsv+CByHV1dgIm2QW/XGk71TKVhXyRjXVgjg++8yBsRT+5erDMB8mX+t1\r\ngDk85Aqx3BsHLTV/r/3H04VjKy5CrxdcBbv1DjMjAXEk80FzVR3vz1tSzNoj\r\n5JmcGTlvVawdIYR9ogoeVbXVvOYG409pCwIzYhgwo0s/dbLQVFkGkaLGuAiH\r\nIMJMSpbPng1lqqRtfsSCyd5hSexJ0dIRDIDH8s8w44w3I7kagBRHfbX+1+ed\r\nZH/b7j8sD7vcvlAkiffyQTZIciep3iih7FM=\r\n=JkVi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-direction": "0.1.0-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-roving-focus": "0.1.6-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ea3e9af95f52b324b49ecce3668c00231f3f927", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-C/ZquWEmktadjAH46eV/453vdwePh0lRMxBlWrLu6c0coVCdrRkl7bw2xLVCNRz0SxX31ljwbHbx6kFx1SFG7Q==", "signatures": [{"sig": "MEUCIFE7ZWpTL4pzKM1CjDVf9xRxdx19NWdlqUAdp1p9xAOqAiEA1QNGdf++mqM2lRVXYutzBJIkKcGgW5aQ3/Q/0T+gKJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDThACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr34g//Wz14vEN3xqtDlkQh1ShoL3paXqM0vMMWiu7gfGawL/mJ9NHS\r\n/dJdQWy+02z9D5Hu4lFF0820oDb7DnOaUYHkXD+hJap+WMTRgahTy5IVtxB4\r\nxX0SPAUyejvbabXKLc5QfoZ/pedGQp/LGL5p/5xOcyG8NVBp27QxRsDXoHL3\r\nYv4w4uYl41xyLYCdQT/SBFkOsPLLdF9QH6UMd6Q0C19Dx4PLEybTVXcmM0H2\r\ntyIIandBe2crB9vxUeNmAo3yr5pRgQwfCi/dlwvpzgGdFgNVLlfGFU1nnayt\r\nfjD7hmp0xxnpj3EaG9IFXy/P8aK2sdL878gmvYnXXYZGhsfzQPeSolacHOLS\r\nRx43NyhdqCIehQOZrDbM53OFK1v82/cegmHNUDQ7t2/OdguCrtSNF+jfWXdO\r\nyalxNdW2YdfIm5uzVu/EKf3ByEIbsQL8o4hoWW9NC7xUp/WkadVV7e6SCSqg\r\nFohfPXl1U6RVmf/0XxV7S3c+uuorclCRJFCG5ueUfeu2JsoUCKV0LiiAOfG2\r\nezIRP2qxkk3ssh+Cswmeoip0Pt7lveXr0KXTZGjVRWBy8SrgHIN416WX8nKB\r\n5o7WNHSc1WtmC6805CRDP+qSAtC2d8bFNxwXSPSxu9Vj/jVlc/DDJ4R34akF\r\nNHKWteUPrYYBHGicUDMd35udZNNrQPQ9juQ=\r\n=PaQ9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-direction": "0.1.0-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-roving-focus": "0.1.6-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2626d01bfa112dc4e4299025b50e49450e7f93f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-OqgEGipr2i/P6c1MJkuu+34WaGTqFg2i/LD9Lrd7vxuqz9+hfwRNyXmLD3qRtUzPN3cqXFzc+ubuXyWyV4c3sQ==", "signatures": [{"sig": "MEYCIQCs/T7Tb1Zq7VUIOSFzfsXmQ79rY3XcaTQg8jbBRkQOcwIhAKM0eZshx8P/YLvpXZP0MggQEQQ2Rr7MTZOj5cVCVC5x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiQA/+IkFERQQ5+wH70+TBzLuIVnBiqhH8w1lNw/vHnct+uEcnRMpC\r\n53yLV7c4/McNwJ59IhoNdriU23i3axzLOaPdwsPMG/WH49DjCzNJ3aa3mo/6\r\nQKXYyzVuJ5U3koWyW0CZJb6VHL625CkPnStBK7zKuAUFfbQbS4TT34v3+LV7\r\nZIc/eKHEwW5gTAThFOhuo9nAWfdjbBA25voNJxKd44vLgf5vKsQHoEG0kUxR\r\nfj27RlrFBKlbdO1MwiQTvX2MEU088nbmIoHue+OZDAV6CNmMWItPlp+HtRJA\r\nlwaOgXHWsSTr/qMuCh7tTVDw3PzNKp0PqtMhjaPlV1+Zn29hFhksoe9mUfiB\r\nmh3h6t/7JI+C9Halfn54YQ6N47auA4bOuUwh93WJqaQ2T/QcO8N/SQ1QNeUL\r\n2wa1JlUjNbuzEz5zgDYJEdnEP0LBh3JnUerYusmMaNGtnlHUZy8lDAuhT45B\r\nVcRDKTLplZQZdxyX2m+c7q5EuNtIApIdg1v6j9EjtvJJfAJ3SNhI71mp6Ai1\r\nN0hHEoDMAJcs8fw/JKMasZpbYKV+1+3FU50NjjhXTSyVEj9CHPGl5lixoyUA\r\nearGlQHmIxX2E1CCRhLqX3M+8dE+YTXPZVgNl69BnSOOk9gq75nlJoqO8vXJ\r\nJM3Domi+rEa1NnG6yGNlDjteTOQTmf87/ZM=\r\n=I0tq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-direction": "0.1.0-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-roving-focus": "0.1.6-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ebe5b8b4d4a0cee44db89a9ccc20f317a0a6d03", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-D0EUNTChWh+DWldEG4aWpcz15xBdwXC9U9mQdnlcO3vEB+il+NdxvIrSPVbQYarbpwlccpBaQtepBXCT4cGu8A==", "signatures": [{"sig": "MEUCIQCUQYUnm0kXAglj0YtiKZ1g/udscAuPvmomQxz6dJEZBgIgRnVq+PipsEKT1/kF2d1V0x8hnBp4kLrT6QpOTsCd5w8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapg+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2mw/+Nojt+udG1TzLrMJTjM/q4k/2Z1e6rl4yln99FFHzuVaC8M0/\r\nfglIewxdJRiRUUWy+S1/wTdOavn9NbI80MF89Gii6Ih1+hzVooSxgZsOIpoZ\r\nIwsmZt1ji6QMUG2/cJavYyG95XCUMxzh1BkUj8xX+MT9p44dudXk/G0mfKKy\r\nQj3TCzMc2BCfJvuT7zot6Sbcvd2vAgxFz2V9fDdEUDJYnYOKMHCAIE0RZtfB\r\n9eTIBnyTcXk8qRNCYHpuwEWBZXviKJhOa2ZjmHmN1Jh3DSt7W+VKBgQAmiFF\r\nC9orWkPwMQOorPFL7iB2PSYBbEB9psGM+9hMQ+RA7l6nFRBPUe18RjgjGYE8\r\nWqq4TEIVVVl72C6786DYBjZ6fXMFJl+VO9LSX3+sWnmwLbnbf2l8gq8xZcZr\r\nTolLAnvXmm/Wqg55CgpgfR6DUBic0imGyQxdQDd02qE8L34WSmaBVtIyHJut\r\n7d1RczigY4DT/mGXCT5YukIzBA44cZQ6YIF60J9oJxGNqnwkVQm+y/4pGZnx\r\nDz6Psdlanba1Na0EdZdrV9YsrY/NQt9UrovBSPMSJlk3mNKWcVxmbo4kcRuY\r\n3svWHKNlAezgXjiWJ5FKpfjm3oTXb8JJXYywfAv26l2DYtLbvuPXdzEMXQ5H\r\neQWKNku3EbsDFzxuPmYq2v2Bicoc6D1nxSY=\r\n=q49p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-direction": "0.1.0-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-roving-focus": "0.1.6-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f079f2ceec6994865c7babe8b08eb2ffa401610c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-2Imip6lpXvL42nsOlGwSD3M20wRM/l6/qxxkhZQmIukubGbIzumc9lKnm4GxMPYFjNN4MlnmHsh8g3Nstz0voA==", "signatures": [{"sig": "MEQCIFvqRGBqN/8p2ccQ1tvrxG6atQXc0oq9hTbkX4WMICA/AiBVSiK+S654r/GTa6kCGNT1qOdXFheeguZT13+5Ok0c/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8ySACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQnRAAjfXuusJGTaOVJkFPiKUr+WR7NRimcvhhKVM3LyjnAGlhei3T\r\nOEZUm9VxIEX4ABRX8Qtsiho8AR+CPL1rACu7FAJY8geZU/NLlxK0wJA3Yh/L\r\nIcf4ihQEfiWNxz1779NUnmEpcF2cNN77nHOkrTtRMhsA5K6NuqCUQ5YDq4AY\r\nRAfnQ8wU5Ere0qepFCi6WKpy+IH83t286oTGNT6rdvbUZcAvqBXUt23h6LXo\r\nD9dzgXu/CHGVN8AkIilE5392PZIM6um4xAknHmrJje1TRRd73VhJMvQAAM1j\r\nsuutk90ZmvCz3DPdx7nqlLKOypTr45+B7eMaEQrdF3m0jm+2NwO49DDUOeRi\r\nCfNd2P0Q0BpLNJbJMidQY9MNOB/Vj0WNFNZXa2ZLx7RCEpei3W+KyNxECOrd\r\nS3wCeC42aG+1uTuziS1uh633FMeAKqshrhQXFxLt8i2+a7QCta3CRVSta3bf\r\n/mbFGmApbFrCXv5S/eL8TgHIQtrDZYFQwcCg/RydOd1YqZOBK3GHquKO4yd0\r\n7YOnXOQwBzQJ6WUDJA5xLqIy0G4saO4mRys3jq+AdT287iriNKjCVAF0xNbi\r\nTh3A89TdI/8/zX+Iv4UO5yGaZ7YYCxjTsZ2tSFxj23Ze+qVjMjHTqnLw7mM4\r\nyggDeTDEVQkkhclDP8pzqDCz1Dy7tDL2UNs=\r\n=ejDc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-direction": "0.1.0-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-roving-focus": "0.1.6-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6cfad7c38137c8e9dbc1b52113e3379e6f66c441", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-1B7eXnQNd9VDyhNzfaSVrYMTQx38lXsJjgOKyD9N/ERANR73EIIOjjWzBD1LoemhmIMyNoU1huNwo5W6wQS/9Q==", "signatures": [{"sig": "MEUCICcIGXyG3DvpQ5JstFoApcmGOcsd2kbU3SF9AtGUAGPyAiEAruLaiZAnyvfIVcjgj6aOMoAWwRjqlN6HcVOmNyrbuPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp63g/+IOXvaDQoM0yavmngrFZ3tCQ6Q/Bn/ppc4cqJL53c/p0sGQOV\r\nj3D6Pw4RUSsTGi5LkNafvluaFJ4fu8WbQhs1mlzEgvQt+Yr2CEuoBSD1OOGw\r\nhJ+iy36rqIYGrb9mPdoRxqAe8HJxvVFQp0ZBGfnHnVBC/lXDBKsA9im0Qvpb\r\nzCECWPxTWMPIjFfl2NsuUO54TtxO41I6CetuPt01C67deCeOWKoHB2Q1VCAY\r\nC1adnsjLHYYL16A9jEN4ptdYQ1qDgZVvQPxlJaP8UUPR7md81k46+B/08eW/\r\no4bD82pkf7f8fLN/25x76NDBKjG+44yrJQX2P/kk/mKtbMyzDfSuBFT4XDgt\r\n33wQnJfwJqaaCEqSKV8M+4Tfiu9uBdwwFRPSmmb/GGRTfQWQ2BzeljL0InBM\r\nN3/FVtPGm3HwMOKNBC+mW0AGOtn9l+HUky2acXKak9NXZlh4YOjxPJQtfo0O\r\nDezwqxFqHYcrJeodGDCOWJry5Fwhyij/GSijMnm8HBvDDf31S1dhTGwEQtcA\r\n7gRMqAhIMLKQym6z9yoLQwfT/LobFZsMJ2kG979fDY5EV0VTgdlXyez7upTx\r\nJ0l94EtWCQmEF2jU815ViGbqRg+v1r4c645nwYjjfdk4yiDkMDF3rvNlBFgd\r\noBrCjASg2iZ4HQZXemEtLhEHcdcOls9vW3E=\r\n=eBMe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-direction": "0.1.0-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-roving-focus": "0.1.6-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9a6af49be7c1df02b7dd2f0d6bca34cd9615b7ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-5m+4TiO8UQds/lNKZGspRYZWCijjVAA4ADSh2GyAT72XLL4LUsIQa8RjQ0dJIBmqLFIx8K6ViNICBXCFjn1PYw==", "signatures": [{"sig": "MEQCIBnroBDeM+SBnHt+yze0M5oEBdNCoF5FvLkWVmsDl99GAiAJjX1yxG7gHPU5jJYrdSE7nRcwUF7q7TTW4CalutLnoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVifACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLhQ//auvY19n9UZTAHbqfoaHNEbLGzZgBaiOeW4YXcB3fl0GrJvSD\r\nwJ0/bo3EgrHQibqqjSn6wWi3AcQ1hAj/QxNnou6/KZECrg3wcinMy50Bm77S\r\nIlX3hv7pj6A6xHdKpiM67W4MGInAfBNTEwvwiIxKsGFjO+Hud8lWKWQzepAx\r\nTDpkhI6hz88IMvWDarcmrJ4T1HKTU8hkHSwiF93GAsXBGA6MWMRr4ttmBs4j\r\nevu0nvXgCpqvx8FKFEV4ZEkpln2z7sDYM0rPh6gj+vUeP414ONxZNzY/nHPl\r\nxZoRov5SDb00p8xZajZ05Plgnxs8QwQnl2CNwNnewXK4AoKLSlci5LjuP36C\r\ntK/gSYblY7bQohpmTjqrjVQVA6iko3BvZZKffdPu3AnyUq3Vrl9XeC260Cyg\r\nneNkdfaRQJ4BFR4OBU3RbRV4cPi0HX2opj9JImMD+HvsGmnH9dLSZRJ6v+RP\r\nYlwlmGVAtnTxET5eDrK8vxiEIVIVlwxgVxYt86uTlqO1XJjIeq2FEXyNGUBX\r\nCooqfT1E2r9xkHCAxHGvKQnQ9fPZHx2aXlAmBijtDCHo3R7qIF9byQjYF3Do\r\nA5icWB1csSGPredBzmZ3zkMpnWDHiiFc+3OSRgCmxydDUbh4X+T62KfzRzqO\r\nzQXAplgb8YTVv0nCzszyiIrKPI+vALUEqa4=\r\n=Pimj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-direction": "0.1.0-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-roving-focus": "0.1.6-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ea2c06a9ad7b4825b1500f89364f051e9f6c1276", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Ejd8+4NGdLtoxmosODNHYJca7pLHMcz9JXXxj4b/v2bJ0QeTxULC3f9YUpKaTiPzRL+Ei+gHIq6xvI5yAfwHxg==", "signatures": [{"sig": "MEYCIQCMRNk6480Ukra3zEvYQfhn0inAcmmyQ0SokY648c1D8QIhAOugA/Vn89aavjnMMRGDIw7sFdnTrJIsY3gyBF4y43zx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4lBAAkgFglm1e/LpwiYu93bkE8nXY6iesvRj2xUcEF+tTVczLDQHv\r\nqWOJf+Ur8DaL39225FtviK7XGLHJTVYx7X8PsyFGy2yMPDpYuML54A8jB22l\r\nUv1DNIfhTZRo9A2Dihxn/3UdXdIFVAtVCwFQC+E6U+fOCL7CMrNVJ7XCaxXR\r\nLd9vEfkYFq/lzTBIKRM8rnTzfbkm9p1Gyagf6tcNJ5GS13KFpG2jwhaN5LCq\r\nYa97Y0B2hYv5/IDniXIq9qtIaVp42TAE9jrBfHGydzPu0t1V0APMYPnbaYG2\r\nIwh9KKGJ9eIIHUgHt+svrTVthS1iycP9oDh98crOAsotwpwfHT6F3s6vFdJv\r\nOzqAalgG7jUr3NU69qblobM9JKzW9VueFU0PmYr3MLi8Ljqi4ztRNEUq1k7k\r\nNC29CcdP+uruzOREiBs7OF69DSTKWBYkg9cZIvoXOcEzFm0u9iz5Aun7oQpA\r\nvt/ZjBiVIX2yqyuN/sQYgg+A/0mCY2XoUZO/+a4stYex1KQl/3aTQ5pMSXvg\r\neP1H3MqQymmDLuoyyR7QK3KBUeeVZVZp3v2mHOMzIvduDZE8LtMWyJGco0Cc\r\nrzgoznHD/6NC3fMYtmbNPSCurP9S+5lhYKX4I4xK4p0SrUUEY+ToDbMfsjzE\r\n/os8B9jSXM8mq0IhhHyWPv4arjMfK9plbOU=\r\n=Nq1w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-direction": "0.1.0-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-roving-focus": "0.1.6-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b91fb54cce60738c9551bb9daa140d7953f162f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-yW5AKQ3Ebb2LgBnmQX5RUxB+ammzlZ/3YfjL0erTJw18R72d/GDwWnrpthLZCtht1vk9E9/LocRiQuW+Mi3jaA==", "signatures": [{"sig": "MEUCIQDDcZPUgnQDtwixSFNrO9ASiUbJbdyVTf+8/0c2YqmHqgIgYSZ/tYPNCWsZcxgyE/P4qAmGo9PiYarDwx4TLeAoHjE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXOBAAghjeZqK/0ZmTDivi1JC0GfaA/dttG9icc7YoZZbXoWSJL663\r\n8oIVF3feTe9qQwOYxooEiemRiAEEGw95BUAGN3rOVSuJHkbF0RaahZha0euZ\r\nsImpr5KW8ou3VB1ok0t92g5wOkG78DzAGWfZhXvyqM254XkKEeOt3d+IoAGO\r\nY4B45NIbQVD3Kr8eHS5KXxJaUsFQ8sJIKjNr1GyhHiV6aDfkeu8txhjFWT/5\r\n3b8zQkuclhjamzWJNhMTzPHSXy0fghAiB5tVkBLW/PVJgyRPqLVL9GGJDt1f\r\n9u3TwsRZaocTEGmBR9p2JzM3uLPqzuFAWj17G0sn+k3y70dx7A73MD8rnv6J\r\ntzs3m4OJ/LWuduULzyr1PWQ6uwxKoxHzKdSWIjlBcUOlLFvGFQSG5BX01R3S\r\nbn0vt6YtPATh7hyubBZkYTY91EC0gsBOnB6pbc8BIHzHidjAtDV4BxFsgRTg\r\nFHkdfaL/U4alKXE4MU6I4YiVkpAgcSLtAYTsogkrWtBRdxaQQcJLgKBr2udX\r\nXq60kRuJxfs8u6CqQkL17HbPn9nkTjPlfOJzwuab2GfBP3m5SEx9ZcOMm6gG\r\njLYnCWVeITQL0HDjzPNIHiVN3ONBZzFkmcfCtdddOEtLP/EJWT0R5wfotRee\r\n7rNVdizKxRGM2qs6k57RD0pCTEwwJtDhBus=\r\n=/Vtx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-direction": "0.1.0-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-roving-focus": "0.1.6-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d0032b290d1c9572ea40180720024be5105f5812", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-LxRUuhiWB6FGRsZLNdSpWAklivwR9DeDCfu2xdW0EeqT8uN5XMYe+rtQHNIPoFdplyS7pl1R5hlNVNAjgKvf+Q==", "signatures": [{"sig": "MEYCIQDdMMyrpwSgMTeb7x+gfDARYZj6b6yo9odKJhE+rVsPHQIhANteHjazU5cPPZXXvsLngVh9Q8sGtqSRgVzPT5ec9Pnj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozCQ//aEA+F0dgFfMFB24KWguTKDZSQMTdbNVqjYg38E59j5wc3QNh\r\n3J3Usu7zWlazgnqTTZr89buV8jKAJYhlNbqsrab6BCwr5jxx/eruYcMmk/Nl\r\nMoF0WQF5EI1/AUeD7QvsZK+YYjO5wB0gMtr571fT6h/FvC6tf+bc0PzVN2rw\r\nCUxlxPw9153yC12xPLkmoIuZ6ANBdYoJzu/E+pLsKpJkhY/DH/dOBefXgFVo\r\nxiKvSkk8aczK4wBhqn3SmwUi2XcnXJZjtQnoTuuAB3+XtX0+ZZd+FnqJ3FGT\r\ne4S6hfwq/YqpI5icFM55OvN9hSahyWqSJTltkiA4uocaoXYmNF6s8GY19v5l\r\n2u+7UokRRfv6RRXGnAeOafcdA7z/4melp4ilRSCBwTlRnHnMJDJjkBpTbZKU\r\nk91llU1TMPSTHtpI5f8kjBmWoZesUUWp/BFjW4FZMu7RGuK8+4n5sXx4+PL2\r\ngXRYOboa8pslotw9zZ1HqWXIXi0FaPhVWk/QqbAXh4tWfdtnCjAYtrLfgemq\r\nkKyMlW204WIa99Xf3SoEIxJrQm2dr8kQpTc0ZSIMQDTNWpfHVXgWjKY73NKH\r\ni1B36RuizYSPnTntiXKy7NNFk+cLaGsSky0L9iv3r5r8bGbJ3sh+Rn2DeN1H\r\nmnfZL6xAgv5bkiP7amGOgV2tE4Kf36Zr8jw=\r\n=o8XR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-direction": "0.1.0-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-roving-focus": "0.1.6-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84e2a079d3548b9d02e5fa7e1d5be224c0559696", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-HXvW5m73x3r0/CiKkqb+2YlgiT37JgNxAeN7yEztz8/+hQP/FM0imR0EzxWHKRPTAKxK8yCNia3oHRLLAef3mg==", "signatures": [{"sig": "MEYCIQCr9rja0V++ra5MgCBidjCLJZ0KBolk4QA9m1inGGGRgwIhAIZd7XVOPrq0xQs45zKTVWyojeEpb0hZB8DJ9otZ/Gg6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58159, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbXRAAl0xfNgBsyDCJ8BrOsWlDi3x8mjgJ/3XCQfQ915UDdbe87Zgr\r\nUIMs0FG0Cotkw9Wd61R+0c3gYgyDSJ3UiN/WNN/O7mAqbxs+4qGInSXIjC9E\r\n0SBaqEWGOk2fpNhg8i+BCCFOFH+asro1HyNnH5b1WZgvKZdRJsSz+Nwq95eH\r\nG6+/+P/Mj1WR8tDpK0TXyTVHIy+Lr+QQ5xfk6TS4mAJnGB/DJA3DoAY7vhfh\r\nH2TRiQnBq8okPh2ugQpXcTcY1IJgHEE9Ve5huvSexKrx9jeWQoEASXTvGsx4\r\n/bp7hZbKHr+EDvzymsZnQaj0qIwfxvpQ8YbQXFHntqbaKkWbXtUlkkQrqzFr\r\n9EQQ/pHU+ouvqLecDjC1IhE3NGVekUcmFOI9aM1XjmjDW6OrFrF098wGTnrp\r\nETlbQXZaBPjp4jFqs9Ie1uVI38A0BFKIp/Ew96Yvdu2cUkiRbFqGt+IpLkJz\r\n33i+wM2x5i8Egmh1RKeLkAre8jCoTZx9H3LzxxEmDEG30ap8AiRVR8JNRxmR\r\nHJ3qeMxbgyuoORXRCukoptlhXknmJkxxgKFbaUrZJaciHQjVfiqdrVAaF32z\r\n59WzpEPOqZkKsM7jC/mKrF8gzFVCeabchX/jwTY5U0HzooHemXKauSzxl+sN\r\nCVXZHuEPmUw0dHdhwv7kbeNxEmQoOeNTvLQ=\r\n=gaOR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-direction": "0.1.0-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-roving-focus": "0.1.6-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fccefc2a538a729a32027e94de8adc98836ec8c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-KC3huS5YFTW/xWTroxBPNeacLYQddc18tRDSloMYX7bOQJZh5XLkXn1beEQLgoOdEydIurLW8Eqv+ZBzcKd54Q==", "signatures": [{"sig": "MEUCIGKoI/Xbr0/XYj1O15fOX0Kq8BiyHqJRRaY9AVq5xkVsAiEA9vZ/3tWJBl34leS0fgmJZfFuBsWx93rM0dE0hDH5chU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJ0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUSw/+PxjCBrnvnwOF5cIy5o5YR98CLrv4OL4Xu7SmbE5bKf2zlY9F\r\ndpLxHnuZ/VvK4BO8mhKcrLjQCRujkbR2c5AbT9a3axvfRtexho539kM42tC+\r\nzt8FAHRXOlx8xlwClT/GGcSAwQHTSeQjxbAy3wO9qUm8Wc1gR2BY1x9nXAKn\r\nsYx0Vrdijh8wvTuoHNd43CNVL1wWMQDQqTvI3wvRC0LnxOJ8YRZ49rGo1mNG\r\n7E7VkaxhtfxsDb6H8/Ydpa+0zQ1ow5jWzXcjRmmItEdvlan9F603QHCMQXgn\r\n5yiHAOHywYZ+oFMOYgMRK3caqXZfGSeVzv8bZm38w6jgQYEyLdkTxZ5gAFCq\r\nSmYA20HpiRJTttmIfCB7y1mPhAqdZ8HOWc3MwXdhwt8ipy5ttTTLBXt18VKO\r\nVgftT8Gqlj/UPzzjaT/MeBnKOsMA1YTuk3XBfBECKaEkVS2NPh8nTsz15+6K\r\naa7bA5c+xfh0IWTkUW/a7BkGeCJogwOU8Yc456h2entNp4GZErIXJKZUNqYt\r\n0w16ngVLruRBB+mj5vvwPSbNFaKCs6yEDXlqcGlWaxxjiBrTXyfCqT2q3Ql2\r\nrvnEVAz9D5XUfOW1RE37RRiW493dTV7Bt6Z/7L5D4l8Lo79KHLhJeer/VTGs\r\nKw8STdeBU5BNxR3QzHWV9+pcmk6ODK04YCo=\r\n=7kce\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-direction": "0.1.0-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-roving-focus": "0.1.6-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2cde931ebb199dacfbc31c9af98e318cc403827d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-3lZMhPcmaM0WBUH46or6KidNq+yrYPHgNKN8T0NwUsL8vv75XUJivycTkVsdE+8ZVASqjaFRM9ofoi/duE+Otw==", "signatures": [{"sig": "MEQCIF7IoJr/HktfBV8Cd2SnNHy/rAP9f3cpAIUs2xqv38kvAiAl/qbCaH68GUYdMBtkrdgdhCAE9C0Rufcy9nPrMgL5VA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5Ug/9FWlF/09wVDOpko9qam4xeR2eH0VhnUk6kJTNSYuvTNS+hXgy\r\n/4itVfnFoZ5p3A8bECwHF6KSh0N2lSnpfHjDVbwVenWU1eSH/YF/ZxBElGxj\r\nXmQfovS2jwMCU91iA++9wPSn9AODd3bXfTGq6XvaKV6tEVWcVgy8tsyFeQbq\r\nLKO9P4Fw2kIiDFO7bsXPECu7k/PQ4djgivXtCX1rqNO5x5TYTR8fekQlP2Ls\r\n5HxwKulZBgd2lofQxgI1dNsFNEi9mipu5RoMpRpM8RN10T8z7KQBRjKCvvtT\r\njAXRfnugvwKgFx03ks1Qw251syRPe58ImGhPYrb8cPvbyZnJodBBTySvNtha\r\ndCIjown9qOXI8XNWb0qVwcTwgUTaG6nLaqcVI6ktS0EgpzOObaMfZLKYu/D0\r\nPGYmSZjsB+KqOtVqbzzPdst7PGdrhsP7TDyCj9rVWt9oG4qcp37Zgs/sHGnV\r\nGnadQrPvDTcuonxifCA2MRjgI3AMAd7Be6apWtqEFLqo8zCOnaZD7o7LE+wc\r\nr02PhUMt8Uu78qeWl65NjY9j+ZNxWVG3SDs7kz3C1RuFPFNuq4RnULfcGwH2\r\nA63GXMxEfRniAt2t20pzJatwFRawfP7qTA0CYew+1hYSuBKb5AnaDyzRHFcv\r\n37IDD9x9YnU1hdqP9/okYa7AsAXpYZJrDA8=\r\n=+kYW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-direction": "0.1.0-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-roving-focus": "0.1.6-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f0f2d5fc34e2b376211beaf8374e2f970d50e916", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-zjDOUcnslI4/6dHunoHObq1zap5UV+N2nIJ0yKFNbTLKB+Werz4HmC7ACPcGnv3Jqpw7iojSwP87YoVs3aNqYQ==", "signatures": [{"sig": "MEYCIQCE5W9ZTmPI9NPRAhSZLiqzHkPtSVtd7qrqi5Ky+8HxDAIhAJ8Hev6YvmAob2BE/aslBMY0krKnPWbLBiDHlaBnWI0e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA03ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtFQ/5ABoqSBQgjrMTxiLJ/r3cGMj4tMrKgnfD+X2wh2o2XGLmCd4R\r\n6t0OpBjuQtP0O0GJyAY2wGdNrsdUOCEFwHQb/MqSx6ymoCn1bvZzt20AFq/p\r\nskZ5Gs+ciy6ZUUDEhn5UqJ9p1jb6aY/lzhAs7Q8Scd3DQ9jC1TDK8UHi4v3/\r\nQtOBbAeqk976Fb3nDTQuIl4GIQ6meSA5MPoHeZea9I4+zXpmiOjXx+Ai8aPW\r\nsg6eK76XuM/xddRWgV4nP5ZYBpn/i1VAtq9RPcTqTmVmEMUOcJHFLJgua8Qo\r\n5jeFK6YmNdwk5bPVL2foYD1GA7lUo9Oi1W0gIAw9wyHkc3H9o3obDyP/WNMP\r\n4ErIulUbLnOEowVkEVpKdMf+5Hxd+U0J6IGzsOdAP9PH06C198qKDQ0e0Poa\r\nOfHMPUj/4dH2FbSBLoQ0uAzw63XRVRy4OmHEZItDEuQFTbTz0SF9FUOK4qfc\r\nqictFPYvUJmqbwUITFCKGuUq9lOTVYUG4CC1KM5QSKpXTayhiE9lRNUEiQWW\r\ndnZUFOqWOM6zxqpq5A+BWenf5jz9Lg18WhTQ8aXQkBzTDOhtPNt2mwOKD6bI\r\n9hb5uW12q4kbdkB3qptMU+wx6RN0RpZLJIEwhD7Lbws9fxBU84KL9mLeCrJw\r\nhAafFvCbEJZssrjPOhaQrNcc66HIdb55jLw=\r\n=3yDo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-direction": "0.1.0-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-roving-focus": "0.1.6-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "528e9708e64b35de8f5eef498fe5d92e87751832", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Qs1+31+S6vojr4Ioi5r1NcKzZV0j/Df36lNWZZ63eJj455JH7xQVy8ecGfxR3vcXZwoj+GLizgTUd8y9owBLnA==", "signatures": [{"sig": "MEUCIHuxFzERGBGBzlY9hCypl6FXv5BVxDmoGuptyI1lvl4kAiEAjx5XHkRVya5fjBFX+u1N1RHYk8i6UBXBvYVirI9Qy1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoz9g/9FTXxDlMFaX7XOs77A/7DnQ3KvlaVN3IUjIb+U7vu2/Q4gQPJ\r\nLy73o4W/DFKf2svjeRw3dG3f1THicmNYzCcn1wUb4IeEZp9DSD2gpgNIwAn5\r\nIu3c68xyhcQ+zI8SzSTpK7Z5j9Rftv9kw9khvlMT+4OvLskd3IUlrUnE+zkS\r\nFrv56jgHEhmY3HOInIz4M15s1h9PFjTizJ/9K4dWr6GHNLup1FIgQYbXfVVa\r\nU3rBTm8mhxlr70B2hiZSY2kTvVH9tBpIUMUm/26rmbGLYcf2jV4r+LdD+Fc3\r\n0c2nzIdxbwXpexpqBTl2X52t56yjd71i4wcHQQWy0iWU1Ht/yZJkD47h5dHo\r\nKssT2+Tnrk7qc4xEX3E0qqbdtSE6PvfGI4cbuk1xQR0bmQB8aO4nSruuQhFX\r\n9qrlDBkOCVh4jHlW+I5d5RBE7C5TlN0UUGM1Pj31gLS4Y7a/Bi1uWSU56aVd\r\nUzD55dzSw0JhWJGXeHIwacd1vQIwweQyapLZ+g6m9KGl96zmEh4bJazXbHMu\r\npuc2q6LqdO90Vv8AeLXdkvDG1CvEP+SK0ba0gMYQD1kx70UZS4Ax/hoWyl+C\r\nyrVd3Jg1lOA1FNN80dcFnMuosC2GAKslu5mCsN5HmKWZfuCbHyBqFR3ESB/Q\r\nTSpoMFHTMMyA5x/5a1fALjZrQtOtBfmwS00=\r\n=6yY5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-direction": "0.1.0-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-roving-focus": "0.1.6-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4f30470dc48b8a717324fed213acc6d31e6b8621", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-aStECG7puWuqFD/pOxOizfgFIHsAwlSddwtBmdjwEvVfm2d4QidL9K0L9jMlpzQmJJ8mi8EE6t226MbZOjmL3w==", "signatures": [{"sig": "MEUCIQCKthhbOX+sPnA3MNZ1Pa8mT+o8agBfkkY604Vef2O3oQIgS6a+3s044TUQEoFTQ1iuZsoV/SCEFmtJ+Wi2019Ia9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFJw/9FXgdFdGMRmC77YeNDT99Je668xzWQRlIUhAfP+sPB2ZzFWXp\r\nbC8t9DeYo+OeFrRXbtMObKMZbc8cpSWswvoTJvO+jG5JnxCP1SkiePGXSwf6\r\nWVZasX6K8eD7Ls2Ok5/fkdVN69KVRypP48Pt44vvZRvJng1S5mOBdYUpvvHp\r\nF2xuLXTb+2s9X+Khn+xGbXqeRjZttVVw7A5lK9mM1JDhgtTQ1o55KVXjO3C2\r\nF70bbT3QQjZ0o3OUwQX0nso4GmS5j8R+5na6opAS3Rcssso8CuGhdlylWvrH\r\nLS049vx7yOixllVza5XqHESbnTNm3WsLJfgYfap5wb30vmtgL7m+5c5F4173\r\nBWPh3FwxmdXEYr4tjnfvLw/g4Z1M10HdCXTCWAY9IjS29BcsyGxNRth2jR+O\r\njZ8szQYH3l2M2KDRJ1fwLlI4bJE7a1mYl/cDmDR5tloqnkON89Ta0DrpIqsD\r\nY7wFHcAcdGITbS8YwIwNOjlM1c+kEYhlklRKg2IPftEbbPK1VDnOQvLeDVuq\r\ni8sTj4CE1X5ng9brne3MsUfVI/YFSRGvu7HxUZ4YKiwkJCnCRCqx8mhFBLEd\r\nuyf39Fil1Dez+1ffH23TOfcJge9qh/9Nb79twL84M913NaNN68Q1dvJXG/ZP\r\nPyr1KHeibxJrnXazZ8pfwoyx11Pfayn+xmU=\r\n=gIHV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-direction": "0.1.0-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-roving-focus": "0.1.6-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "045fa3628eda33584bfc5c222f103d6fdcab6198", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-3+L8KCFDlplfTcpPHwXFJCo0ljSP55Dg1mk53tqW2PiKYiWJEU80mIg1gly3Fj1brOolSARG1NL3UwaHq3DWcQ==", "signatures": [{"sig": "MEUCIG0N1W1qNRTF1/7s/1SS2u78jVwmJ5BGUKlxhTo2kyjRAiEA5TcR+b1hYNHgehGEgu2IngZNAEeRC8Ft5L0jCWuKcwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq05Q/+MtPmnoFKpb1TrJWOaFSqxNjRqCwE4qbCDAIWPwUqztU0BFIL\r\nJtNnW/qbsb7R6MG8kQQakrY1Rrksfh74qwevRnmKMEpAx2qJMEFGPtV6lSrB\r\naddicunaQkaqgYieNK9z9Bmk4hoIhem/+KAL7ELrXph4xKNIT0XLpd1rJDuZ\r\n1rdQEChSfcIKuJis1bDNkl8Q9uwKc3LyfQIwK1ZleQdNx2ofR3DmuiG296sz\r\nlDSpgeZjIWbg0S8wZbU3OLxrclNPbY9209fiBTd+dUNJ92DOGKIywG0gVqsl\r\nFXX4Wf/ad7zDzC1Vj8NTc4gE0n81nkYE3HpaVuzlauUcgF3ODcv9FEt03Ds1\r\nsReHkUC9YTEw62UESK0BfVBpCH0WKtCVcfsnsl/MsMjlY0J0mlI2eu3U9SPu\r\nLHZFvtG5PSbK3AFZEyVCKil2r41S/T5Z1oIdGLKgen2XGZx1yDr7n/Nj7M/Q\r\nO16Uz0uwDGc91pMTKgZLk/XyrAWeBmTWIGWhMc2+iVzYJTCnb5//EIXZHsK1\r\nZJt00btp0pU/OIbjZqMQtA7W+H0t51lkZ94K1yD1qDIm+n3JNQ5y8Lkpqf5+\r\npQxEvpvTcQK9gCUYIsl0V+GYxEK8GXvkJxI1DXezKjUPT6YJzz+FsWZeuBoB\r\nc4LZd6ZlbUfOM4REdxCPFCT5gXzgGcWyLu8=\r\n=Oo0U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-direction": "0.1.0-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-roving-focus": "0.1.6-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "620ff58ca1212b9bd3216c6e2fafd7217fbbcaae", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-uD2IGW7MMcTNJvn3SN1t9zjXcblakBnqGA3b9MSQW+YL3WTS6RcAY8Z0cwgqhrpZgqVgoI9VrGKzkFohPZLNLg==", "signatures": [{"sig": "MEQCIF80ZcpsO8K3LO9Eggh8gLYkvOW8yuLiIXYR/Zw/JtbBAiA11sgGoHpGoN0hw7UbIHlOWkskmwRw7+KVhogPTRf7SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaHQ/+O3HBcNK6y1BuGN3FrZk/uvhyPRfMYI4KiPEGnZ71lnWUR8k7\r\nPaIA9BUMZGcijX7btUCVS9ebvcZI5hbosbK4NoafIHBEXWzmb9qEYVoOPHl4\r\njvNPAgRgLb77fBtQJuBKtMfndUt0KiwiDboZmtUTFnzAeJZ9jOWYwbc4303c\r\nNpLcxP21u30YN9yEJ3Lo1Se1SfPlgRjKzarJxrDwTk8POFiryfl8l9Ndqi87\r\nhNKLVcw5/9mgvlLmexV0aLjKew3QMdoprA7JmF06VHHfGPA8KTuSEfpzhYve\r\nmSgJFzQv2SXqfVUZMhqyEzO5sHR8onTqAqqmLcCBTri5a8K+wnBwrkHJhTpx\r\nQhz3eq4EbcONRv3EEPy8kjrMSsUOHjHAZzdvtLiDLRZ8/KJPcm7mmaY9u6Dj\r\nEVMOfyLuoEFXjdZMJ4lDuahPQPxV725jQLp1zNNRa0av7xB4j+mXczdek/yJ\r\nUCxIqr7Riiyir8mEBQy8AwW6SlwWLjo8OdlQrY14/woP9KW3LJPcU1mbB3Ut\r\n+dXne0SNZbiMs6k6Q7u139t1K5orDXplqpOHHZGj5N1AH1cJdJLcp0yhZ7sm\r\nM+KLEUvxSjAMOEzMlAhnqb6YU1BgYR0Qti8/oxrm7TPHIgBSCHcQQD/MB889\r\n0WpQHbUzdHObeHvE535EmQro8bvGShi1vjI=\r\n=4cCf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-tabs", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-direction": "0.1.0-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-roving-focus": "0.1.6-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7de9991e2909b40f9a93e224b344d797b995aac0", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-zagQauAW64F8b6bCSTW0z4Dx3BcfiR++P5c1KKp+t3FtAxciRTLYKK6xrqZ1/98xJEgAHphuNP6V9+Xa5ZCqmg==", "signatures": [{"sig": "MEUCIQC9b4PtSioBWZWsR2bTyitFfAxohdI8s2wk/8T3FwLNRQIgFVYDkLHh5KKZ4Ml8zwpwfydTyFlutHCkpblTlPYDM2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 58203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx4A//W/OBIHO/kSLGbqWSlWKUOPAlNWh1UJVRhw53toe4l0Wa8k5N\r\nSBBwOjeaHuPPfCxF/D/dsGWgIiWakjNCA9zFUyBaKLbB8Ct49QIriLCmKfM2\r\ns8JGTcPvIt3DHPuhZpWP6+Go5XFp9Y1BIJ5A0rRomiJZpwlySo7FGRBV2xFH\r\nZBIWfsIBwPnpBMkT2Nlj66M7z2O5RGU+pSGQpzzb0gKkk92mPAEjoACUKKYY\r\n92vdLZDymJ9J8tc5CBNB34D7o+yMCtCgwVmj9/392yusY2JcerCt/Q1kJ7r7\r\nJSPxQTMvMZcGQdj99x1WSVwDVFAp7o4BfRjEOvugzybTTqU3z63IGJxPOlrT\r\nmo8nTAu7yx+n0DykF6hitF2mq1JhA6EO3MOGJ2FoQZP0g4T3dznqZ6cSHj8E\r\nXZoknmWmzgK3ZEuqdIFU1Q2HUTsCYjZPYe2LEgwgpPeuFBt260LFH+k9u5+5\r\n34L+N8TwGSSA7fQHfsssKGQ1t/uJ84mnwkbCp8k5vKF3UT05hFhAYGn5tQ9h\r\nuwORusFUmNKLS2AqcDOkGTq6RqX9nOHmrYlCgSg2zCUhVOcZXfIWLO9nj5B0\r\n/LSQUYsb9hUeHBjAixfZ9ccD0hn5yLiBUkHcOWQVV9AB5y0Jz4qC8RsbMLWs\r\n4dEDSOPm6ga+r3V3G69TapY0U47YROKXUN8=\r\n=7k0f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.1": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-presence": "0.1.3-rc.21", "@radix-ui/react-direction": "0.1.0-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-roving-focus": "0.1.6-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c8065998254c0eb338a37173f67ca707d6ac76da", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-RDenxam9nfUGIPENJcIM6xX8NnaWhbCA9qQBvMNe+EsmK4V5PQehUCLFrBRHhk9TRE4DRd1Tt2/6swQvf4zRWg==", "signatures": [{"sig": "MEUCIQC0Jv8pmJYwXWes2+ekm1vn0k+OQcgXDB2Q25Vj0F6fFAIgDm9I08v3MqnOgyZHGcxOVw56XfFdDNl0sdXoCjngkss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRlxAAk/iBgN1HeETvPDEjeH+dtayL4SvN7j/S5qpPO88GryLduhtn\r\nWXiHJb35Y5eGa90bR8oK6brMOnaO2Gst+bkfIArWO7rJi2NslZU+GNJmIui0\r\nTYHXn0r9Hk/WVfZ8yvsX4vfHkzq4wAWDm9KtAa1LVC/mnqTjhFTN2NDMH2Iz\r\nFZtGUOz0cUggt1KdeH2fBCHmvQ5En5jqSflDMKBPdvhOGJuWl3taOeMvKqbY\r\ng+aY+8ze8e34BW69T6MYMT9gFYkWL6MP7iej4G279Yf6TRTV74J5ex0dSn8q\r\nG5nhVb8hPXBcN/rnrptiuYuPEdgMdkL553VNHLSvIkc3IwkeB0SvU/N4m7nO\r\ndsFepORxJ4Yi06s28qMkOQbtUuiNAULGk7MEK5xSrUWNWoH5JhpixTubq6vH\r\nV2SmeqiZx6fXQ19DB7ov1NrXY6YXmnXIeLx1x+pfoDb1ezFqB79yAjl5YKY9\r\niVsvcAkHrm5n12rZ7EDzpw5QrwJJQfH718ZUE4gy/p/IzDwHaOBhzh7j7gT1\r\nDWx3pl120Tq63SbsOgcRgwT6SHfmjFqmMUPdqzFFDAeulf57yhGPNNe4M3EX\r\nrfsvtx6AXI/pXKfcim7Tp72GWIi801AUgneegDyJOMi7QCgf38PwFQrzpa05\r\noMkXMqcYQX1pzgiX91RxPdOocNnjc/JyrOU=\r\n=HeqU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.2": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-presence": "0.1.3-rc.22", "@radix-ui/react-direction": "0.1.0-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-roving-focus": "0.1.6-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "82be1950317b1abbc9e20218448118a3889bef65", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-nkdVmXdkY0bZd95cR5uf7V9KbwdMok7CnPkAUEoQ+Pip5K0OjraKGvHjeT4XMGiCv5iwOwbJGuKDPJy5ZpgyNg==", "signatures": [{"sig": "MEYCIQCm7HPXIFTl+e9g4zoHhMo53l3u+SbquxuMm8aOiqHN0gIhAL/G56WMhJn1cVk729alIvHRYUU2JQZhmVimqlANaOWu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolDg//XvSudOe5mreZITblFhfTEfEci0iM7hL+1JlipVNunqGlRrQ/\r\nZOGQt3FWQUSJkx3XgjZTle4IiGItRBn7vCed8N/XVTba9tx8vhC32IPMmIPy\r\nfh2euEl7JHN9KAGjzXwlPSQN0YVvhfyqEZT5TC8X9YnYQdDTGuCVNXY0hA+0\r\nqp0iGZ+BITHGtNRt1upOCMTRMfQ82WPhmlQEs0WfggO3G85O1gv1yo5VhhQ8\r\nEGfct8eMr8PhKVJB66VPKVdJUr6NFa88i/8ebEng9XixI1QrOy0c4p5dXVax\r\nbZkbGKCbZ4XnR1LK2oV953eYZaAj4zJ62ZyW8KAL/5DVoMMAqcTSQI6phoTx\r\nTKb1mnR/0tj+l/0pAhmG0b7b5dz0s5EA4tMc60RNxCS6R+H5gRGZAZgYHbqt\r\nKakeryLQovfqjIobDGC2Z74vv7VgoWFCEjhxbPaehx1FpUnZZNyzM9ktoB7X\r\n6/8Bc7fc6W+t3+Ku3SObukoZYVOEMHgrN8x2v9FdeQ/Me/s3MYRbBWNuvqEL\r\nCT7ow33oH04gTMOKvtT60wOVQQYKi2/BP9xSIMl6B1W5d9wKMfvrWGLyXjDK\r\nXnPacEzsu0JOJMTsQSqDPurwR/9FZ+2AGeMruBtONhvOLcJRod1OfEiGrCmO\r\n8aXAHw0OmL8GIDxqJoqyyIrqf/wXfo0nIyo=\r\n=Lb0J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.3": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-presence": "0.1.3-rc.23", "@radix-ui/react-direction": "0.1.0-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-roving-focus": "0.1.6-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a4b902918edb5341c6e5b2b99b235eeb9ed92ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-jSO4OXpQxO9OrusB6jxH2LsYOqGRYDEgw+aQv0kzMh3q5OPTA58iIzdzBeT4IGrJqAhi/7gj0PmeWPtcSmX0UQ==", "signatures": [{"sig": "MEUCIAF42EDf6puGAOwtBCTekk7c+00+7WipxfjeFUxRRlREAiEAmHXr+iXuPCVGHfPfHPsuzm/oeyPVHVoDMzILBU7LWEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAaQ//ciMo6c2522GJ2IOED5V7cVPCtZVckmTYAlZ1n9W4aHAjrEmr\r\n4TYohBxqyzwJNXrhM2S8Wd9SNj/w4hjsEoyTF/wlc8mXBfh7780iRrNPbKNM\r\n3mQzVNG1aKVZumvnES+tdWkEomMiwZxzsZYnzzWZwxEs7hQOR8EBjRAoeQdG\r\nghabN8iPVklyD1hKoHsPqarmJxDnV6gmWSewg9+HDkTbCtQJ9fy627iMH6s5\r\nnZKTRr5bzLY7wJxfzIuzLqhHQcrw0YFuCFyuxs36tVVzs/mxpCtkhuFxUQ0y\r\n0hXBmIDHpbt7LgsJlZZoXxDInyDYLSjGdMiPQQCEIatPnWoA+WyEFtLVZYHX\r\nZWyVLUIKbwcCyjT4SARviwINJTvVc5Vja12kI3dcrrOKUOhU6jppn1JlPsKV\r\nSaSu2bARp2kqLkDHT20kEQnrjIAmf9W74Cn7zKuF9tG7jB5Y7bFUq0aUplQ7\r\naWAnGIDztJmtzwp3ga6O/ZaYTUa4eG+5wt1hmoFPX1+E/SAfsC0gumlHqLXn\r\ni4up/xlMCyXjhrwDRLbr6wAKmzXCtYJLam9dkX6Sr2dN0kFPFSd7KuKEIEMF\r\nKOZD99aZUwX0PpEEswYqb4SrI2DamxI05KmPVwexNAUNqqr3Wpg74j7xHSk9\r\nBwvd40rQz/NavB185zb3YNfavo6JKLbGlqY=\r\n=0AkY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.4": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-presence": "0.1.3-rc.24", "@radix-ui/react-direction": "0.1.0-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-roving-focus": "0.1.6-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7c7c05b4e5962e544031e5af14745441da8a4222", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-DFnfAySHH/X6LSKqHXu5nPxd4DLscB2OrMf8r+K5Pflg6Qp6/OUGx7/g+GDx5HtzS8+iNOGwHMKoaDpRRUEapQ==", "signatures": [{"sig": "MEYCIQCovpz6Hkvg3Yl0xl6XYu7G4VVZAZsx3Mvkz7tuiOYxwgIhAP6R0sQRK0hA326mNky2yhlLq6zvUl17sMCx6KzE0wiC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLh6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotGA/9Fha19k/MKLeFcwsPoopLEFQ+paOoTf0IFBqdeIs9cHwGDe43\r\nd9HSkHi6wmeByZoK3PUplfLfBHpMOzCqsUHn7X0KCMm5JiIOD1YszF/i6kE7\r\nYeuU6L+WhNgZahczEQrUos1U62BGsPmQc14dNc8dVxUqUeZTkBcRewwUfQKY\r\n9v5g18cOO6gJ8WMS1Y36QqleTe4YKBwQt/8bNrC3hwpJhU2ZfmQOzraOFvLO\r\nIkHsMXvvmsuCPBN1IL8eA2hq8G6TFWZQcP5YXCx/kZ1fcqkiqmwknpcsex4y\r\n2zX+97zGwQG8NHQ3Xloms3bljmcsrFjebt92Uc7cYCOC9zyC6N/kXp/rnLFa\r\nmvfB/J2wwnfKvqkisCeeDBPQi21sWf5LN5uO8ODwV3QXuRuevBKZ3N9/o3Fq\r\nCdcGoaHQZL2D6YMR6fjhN9zZ7DuOKbUg0+BC3yKRYxS1lkob0jNtHiDjxTOv\r\nCdk4ZUHqM2gb7R5jsoTGQpczoWtmEgyN6LwCRU9PikfWo6fjJxO2LenraWMd\r\nZB/LjTdBTdx+Dq3EouQwlZSpcOy9d7t7MBiimT6Nz5+90N2erDIFUZIE5t55\r\nmHXNhl0/nv+S9A07en0FR/qcYrpzR1Ryj90mu14owwlXD/cGDvp6wk5r2xHe\r\nczF28sregn2dWaI1YjXA6Btu70m8bHmaKIU=\r\n=gSYQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.5": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-presence": "0.1.3-rc.25", "@radix-ui/react-direction": "0.1.0-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-roving-focus": "0.1.6-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "baaedf92b67efbaf9b4089ee69fc5002f6d8509c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-sl4TmN9EZLKmAYwjytckIeAPevMZJOJnrVdoN5ijS++WJq1mnPw08T18MQfDyd7XrPy7va5/Mu8a6wVEcsc9NQ==", "signatures": [{"sig": "MEUCIQDdI3NDJpsIiWFsCMYo13P8EPGzL6YxchvhqIhQRrrO1AIgUovKNz/D+sELccVhYmbJ8BWFjVtMRexpl2x/ZyRBE1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl8w/+NwsBasONATsWttyUENFTzeUsPorqFE+L3OdI3Yr7oFXPByvl\r\nsyAp/Mwc4SLCx/xO0f9m3eopRu8EgOzWOVwOk42Mprks+UbxmWKxLbsooQ+q\r\n2Kpnk/iLQdSUW/W6N0ycisQV51MfiaOKlxh/dpMr80toTWzU0hu28a60QUCG\r\na1Do7ZZnadsY/rqgM7fvUwwmygovZX8Eaghmbg4kTPrGmMsykcxOCGYz0My6\r\nYErdQJ0jZIbVoV9BibSGnh2adjlohMO5SoXokIRYD4VQdpNw11kN4cYCv5UX\r\n2Koz4mlRXSwV9Y5i9FywUSbrBt3Gf5+An+wuywY+KrqizC3GLP2VLgX0P5Tg\r\nFksSMCBQ3nZ0068NqwlHYNljw/BzIYQjbNcsS92/DhZaEwRTWv7pogQOBa0N\r\nK8+dZwZZ8okgli6g85FLy9RsNkYBbU7pG8DJsFW38+9h3a6mYT6VnNx5B3Fl\r\nD3xxMjHjAX/usk3ebgZTzeifz9O8Q1NqDOwdIQsWtMGS30OLdc24eWZh1N+R\r\nSu1gJ9L691zuhbrqlC8ayjet0uBZteSlXOGOzdi3C++ysdz+w7IWZUycI6Q9\r\n5tzl0V9ew/fsZ+GcXZYHho9myovJ98VeleY4LMMdaENQ0kCoyGA8LX+qXsXA\r\nSkapP1FXe1IDLBONlxzcOf+lKkVlTvwWdCQ=\r\n=RRpz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.6": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-presence": "0.1.3-rc.26", "@radix-ui/react-direction": "0.1.0-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-roving-focus": "0.1.6-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "812c5bbab74c17bc4b59c6c0945e1ed0d9c2dfc2", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-nLdMcwDUD5b2qexxqC62nmCdghDsBOsIgZ2qL+pXHn/epuEHy+EiIDTYWVpxxzsEzoIJQ1Q9qA/hI65diQQgdA==", "signatures": [{"sig": "MEUCIA5UO2Q+DeSXAr0p+wEnpYyLHiVM7x8nfAhsLgI6SpZcAiEAuXFhiGgUA31ftSc9OJva/Ipo2fHcuThrpCwg3K/PZRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyDw//cFfb7q4uXdQuQ4Y0/GiLBpJAQ1utlKjHLLEpfB8imh7VEqKN\r\nCjUdztJi8NVRPJDD7wd+7qjmsqHOSs6tlzDP7eUw2T/TVUAwRYNfruNt8XBn\r\n54Wju0aTMn3RgiTWNJjZhgPUYkLWsLnrMqPKPFx/u0pc7KC6W5mnioy/LPtO\r\nCbNJmLddHnR8OE2EQ86ajGXPjsLwxw1/fvd/fX4Ch+iO3yk7sI6bTeKHev8Z\r\njCBFkQ+qmhc8enU1aPbSFFZi/M+/uMpd7eaVHVRGJWcCteGlGYcfQoM5xNVG\r\nwpKCg02aNFSCYV2Th8inX2q9X7trdvvuK5zQ+KY9DSrDTJVLgQVfP8ZH4+ds\r\nXxJ6jLyZNHX1xMVVeCptL4vb54+cz/lCcb/ZCTGn4S54y9lF7ebo8at4oAzo\r\nDUtbTXFC9pksuxnBXjN6h3GNa7+00p4V0ion9v6gcbj4uISokVpagSQEbkfN\r\nJl2QwTd1g8dHcRSXpiE92rXTX+eNbgQtePPAC9DvvxTecniTS/JFSQmnhoE5\r\nWV2jyUnyn9MxazOv+CD7c9+mxWUUA+ogT/H8S6xkLuHIMw/HD52QOJBzOrx6\r\nkiocaEUtXXYB1BSdymf0gYHa5nDjFlVq6imJdDOOlkAxvMp/MU4TyYzYOJnP\r\n9g4VgDyKkTPaF3NqdUCli+AimwhngttOw1E=\r\n=lQ8u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.7": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.27", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-presence": "0.1.3-rc.27", "@radix-ui/react-direction": "0.1.0-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-roving-focus": "0.1.6-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "38c37646f109dc7591179a53a024933fbb55a187", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-RFbeO3q9LcnWnUycI5cLSy0XKT5OMhw+n8ilIbf0snhZlNyd5iFuADvxb9UHt+HN9PSThJDEIyyWXPdzkhjz8A==", "signatures": [{"sig": "MEUCIQD8kGCY/FdNnBnpnCiK13UinruxtSV+BrudtNUI4byLUQIgTml/fVngi+X7EMx8Fnr6weJUTYm4fMuEtt0pUcrvkk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2hhAAhM9tIT6u2ywX8OSQTLD1vzWZR37lHe21FFbIEGZH4MG/GrYL\r\nz1HA1mUR3/wPwMG1DVeMPPaq03lu6RrQ13tOtmOFNVRdViFcSR/XtzsqCIQp\r\nlQ03b3orZoZPBXeWZNI91nScOO5F4Yc163do5HB27pFKSUyz3GjZaK/T6FPK\r\n22oUnHJXci/cCPsCUxNt3xWZ9MMNb1rcRtquJNlD5OYI2nCYaquEL4JVdLQo\r\nhEZAbafLjFeyjEL8RI5Kkxcx7iArv1nFkVnar5pY/VPIfoKlj3iN3J6lpfoy\r\nqamkkVTZkK88DhVOLwr81qqgCWvqEvWN+8/zekEv+bYZmjjqm6LTi6a24nPx\r\nBK2/QI5doT5Bd0OQfqPb+5NxmQHqax1dIwQBKpUvGAz3Sxl/qzZEcwzgO5db\r\nNJLmzzR4r2g8QBGEmJZuX8L4SmR25yAUH8vG844GXDXrNpFjH2FgGSs+mZES\r\nh9yHmVs5CgQUKD24kjb2fDQ+N6xcsxZm4tpWWBIrLU/n+uxY/bqHVVjhRIQb\r\n1jh9tQuwkFuaWTw4s9AGeQuzwJ/8TkaiwydT6khLXYf56neJ0x2KpmbLYPfd\r\ne2O8W41vEWwy4NqlWVaXxQVXpnw+gKDfWcMNmZEo//4qkwjuEFblPowby6yw\r\npjYiZnEJF0GVVJi8CqnYK0cDFqBnK4ylkvI=\r\n=0bf0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.8": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.28", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-presence": "0.1.3-rc.28", "@radix-ui/react-direction": "0.1.0-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-roving-focus": "0.1.6-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a79b573938a0ca71b8866c4f7097e9fab24495a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.8.tgz", "fileCount": 8, "integrity": "sha512-0AGW9mtc4aWwVritw9MZTzJeblmEJFnzww6zlqxxBv6GukyW3IW3yAa26QbxTMYBDRs5YArroduLoy1L9HRw9g==", "signatures": [{"sig": "MEUCIAcNanSisZnBBwScykq9VgO4Y/U4tEO820A77+43sbTxAiEAsF7+N/Jf3CFLmqgR92O/tfc9UzW8xxUOCkwV0cckKaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildN5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/3w/+JshfDwc1A9Gd6MqjAy9F2xWc3OpoK/39lCKEAziA+4/Wkecu\r\nwkVx0RDmzJkSIjyhwZAk0bLvfQg8+p81JAshDoBzmwBu1zEF21pQSmbQPnBj\r\nzYvG4F60t6ya5SOR91pXvah4B/k3MG3wJnSdurQXjKQhrgbuYMwHx4TTEPPS\r\nYKwyW69EdUochYUqRpm7xTap/cp80p+E4iPTuv6IfErReSCb7bWlXpdC3TLn\r\niCAfWA3dphO7Ddt3RSejHW29VDwyy1H5yicqV4Py1lEs6FwViRgMlu0rOO3H\r\nKTzkTtWpmicEOWI/YpaCqswhstG6tf743wjawCQ11eok5OHnLoQU15/stzeo\r\nh8VxvYR519wthFW3ZbCIZAXWPa8ijjG/EzPj3cCtISJKAF/m6tIN3E4yrNYG\r\nIaaS9wr6Zm0XdubFXfulmaut5nj+zOBgLGOJkaGd4r/zMDi5eQH3RlxYe1rL\r\n9Iq2SSBE5iTfERGc5/zOM6tyuVPKtW8x2RcRGMyf5ue8ibo6Mu9ql9Tmn0+n\r\nZuPCbGHNjEjJcdWt0rGpDfKjPPO3oDPJ4Z8QzEgFOMSiP9awAh7XESmvByEn\r\nBuZSYEPU4H+jri0pqgUNLh326o5sLRBtSwTONwr7yTJFD2/MUPVMsPTpmRL1\r\n7jz0OArCKHav19vhNzUV88Q/8IQnCbgqU8g=\r\n=YUlz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.9": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.29", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-presence": "0.1.3-rc.29", "@radix-ui/react-direction": "0.1.0-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-roving-focus": "0.1.6-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ec38edab4d19c9a3a286d484a35bb124a93ae847", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.9.tgz", "fileCount": 8, "integrity": "sha512-A/a+bvYuifMApqywXdKx6t6Z7IE7I3qtVB2WpK+GMUAJjLdhVbQuR0Z9NP2EEffotYa4UHUxxhtqQZl93PpYng==", "signatures": [{"sig": "MEYCIQDB3tjzOPnD5Mk/cC3x+XJegAxEDsyftmiI3VfGpLTLpAIhANMvEmPYb9AQN/M+IxAQm8nQNlI+p4AJtq3MWifcr0lo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62915, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildryACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFyg//WX73xmRcUnbsvATDA3qbcbEBMYC3WU4bKUzlE7suIddzSE8m\r\nqHy3siXdk+2IzT2Hzmz7iXtmAwU/vuL2PQ7glxZo9+HaRC8kzPn5VNApTIT0\r\nidawX4WMLIy2+V2h8v3sZt857XcFYILBnA0lln3jHcRcmZpCWHE+EtVM4f95\r\nfqFvcEP097wq714p2/Xm52nIRA9QE2nB6smnjpbzBQtajxpy7YVKuIVMK1Lz\r\nvUgvTa/d98d/49C0tktIbfCnwngZDo06Vjc/MZpKKwVAIWCekTUkpDQcRFaM\r\nLou6aNYYuchFqgElB0l1af/Vl0L5TLBX20BrGs2xoVDSLwXARyIYAWsE2gk+\r\nt9EUOnmobnIqEwKRFy/I96PZ9nUM6qGQAv+SB+w6b+1SQtMFnXVy1GhOpuUm\r\nVXeUNahXz4cjXP3UEie05Z6p05YmJujtbSdA2tqq8ENhiUl7ixFqPBp77QNl\r\nHroIBAGWnNrYI7IqBWtmTIcLntFkgN94k2eAxZrCChbSELKECf7jB3bXhOCo\r\nbc8HWYIgzwFG800o7viXNGyy5W/0YSUwuUGRvwS/UJ6vqDgdIOxOpYBjGcGs\r\n2wAEPD4KxAn+hDc86hwW3J5GHEMZmHSXY6tButxIuL4qEoUxLyduDvUVIIAU\r\ntH1H4OS5lnK+bYYHKAPVVKhA6wCVqVdCLJY=\r\n=mc67\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.10": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.30", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-presence": "0.1.3-rc.30", "@radix-ui/react-direction": "0.1.0-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-roving-focus": "0.1.6-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4b544ed178e133f6697dc93bd26f2642df1e4a81", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.10.tgz", "fileCount": 8, "integrity": "sha512-qS6WIixaiegM+iq/up/QZpEqzHlVTckR2dqyrSLVQyhFFr1Y/tRyvvVhhmQbyHn7enuM/5HMWc0uHeXIufkUag==", "signatures": [{"sig": "MEQCIFneGlwX9dZUn32X5eiOH8777S+ryqvbLS0DvE8qCh0UAiAVhDV8DIXbTNsDRyDK+xiwMRt0AIZe2tTcGWHtgAckpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdBQ//e4Huzkj3DxR/mhq5k0/iM9L+Ewg9bbuC38IWhf1Gue/wVj60\r\nD3TrH+mtMd77WeQSgThyXfBv2EqWuOCBaV/cocKBbpUyKSp6ooWc9cYXDUsv\r\nMXsO/2ps9+QjLQ29FDR0UbXTJdeGhjwh0IcjEgjMmn4cvZttdz+z0OTU2pfj\r\n6E1VbDTrFhZ8nGw97fimNRWCfP3lGADQScc9pE7FAtr+30hajGrsKNBF7VV1\r\njk+SYezOEvM6gdYZs7Zs9acIiJKHB/wp26FnGd+jrZ4iHVfzT2pC/EFvapnp\r\nGCzYZMxeSr5OTfBhwuQsRBFxoQs5fh5svMFtppaIP70GJHMx+a8AuA2WV/ui\r\nTdmbSxpWDAfx5gEVjG4a7dzsQFOuSxtDBgX3zyohrFv/K2kVTS2ZSk6dtMfs\r\nLlt27d8yRFW32Sni7VwPQpUv/te6uHZFUGwg6WWax8MeWJGgKb1ByAPOLC32\r\nOOhBCAy6TQyTY+WmhNxY5BczvhYfBrMBrbibgrbIHV2JFsjqKqxf62WftvYJ\r\nnIRJY3UvrBvpSecHqzMWHUAFMAl2WKA4xT4Q/BOsr73/I6TAaorGctyP5ZGM\r\n9lHBDNSGq4x37xzC3wOOtTFyfv7bRI7BRaVFvlEiErrQzbGeD0kzr6a4dMD2\r\nhDpZpG0ED1WWPzoDgrE7gzwZqVuKRgqVdPs=\r\n=VaBd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.11": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.31", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-presence": "0.1.3-rc.31", "@radix-ui/react-direction": "0.1.0-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-roving-focus": "0.1.6-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96b1aa7fc28fab296c29af793ddfe77c7736cc17", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.11.tgz", "fileCount": 8, "integrity": "sha512-80y9WoKEODqGKZVpdlnndgFhfFLW4aq1d+W2QruFf+8SMa1b0yxbLtNKtmSvpHnEh+SWX6RwbqC9WofiMV1yiw==", "signatures": [{"sig": "MEUCIQDf9936DZ5eMZy1SWwWv1UCV8wqqyoPuEO/lJysXEtg+gIgGmYx6hhRqf5FpRglFDDWGKXXSgmJ4oi+NMUz8d6K8js=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9mA/+K/eecgCmKuzCVj/gs76OKWyV3OASxEQ3h0+u/igrdBPLwHL9\r\n7OOU56S2FFER2KCM5ns17D3piQOSe9o6nvipRuge3MqyOvaz7rr1Z5okVFvF\r\nPtLGUGH8mID0MOEEuaTIJYUv1Pfz23q1hTI6ThHORnzn04Vfvq+XliUW1roY\r\nS9x8ISwL3xJjr3jurLCnvX/zIXsDBoCk1zsOs/ce9na8HC9mAekMURaANoAM\r\nY/4vlAze+k5hzS39rgWZPv/d2f8HJnK2tvHUVRpmLdy5I0y0o33T/JGYNldA\r\nnG9p5a0dyk6IrAY3y2sHAp7/LvddCSOujnX2tlof1qX8vI/YlzEsWG9grv2t\r\nTtwSJyKnfxxu1KMZ+yLR+jNh2vZFAOHKx0VHCgZ34e9cv7ZwwiotAO9pGs1L\r\nXudQHOQSFmrFqHJWXg9WMwAqknIPZpw+cegjW5IS7/s54A8Bl99Zrp5TM2cs\r\nMX7B6RcM5ZYd5l+299FNaZtYTkdbSu+48osqQtFcKGIGlLIaKkq8DAEgQfff\r\n7Tw50ES8Pt2cRm4DwzgIXodWsV7tMHy/y7SEcFSTztXwOfulTSw5mcmSMQWm\r\niI7OP/IdDwseQ2UnbvWJ/AMibW+1oVb3uSJQUHgH6uoy0tqOH1jwQLzzhLWD\r\nU3DzyxIc51n49wqHw6Ij2GBJR4KMStLkQ14=\r\n=p+7r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.12": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.32", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-presence": "0.1.3-rc.32", "@radix-ui/react-direction": "0.1.0-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-roving-focus": "0.1.6-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e58c3be437e52aa399a1ff3049dab881b0c4a3ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.12.tgz", "fileCount": 8, "integrity": "sha512-M8MzFxQH5dSRqF6s/NO8DvJe9grCcdQ1rarZih7OPeovlgxVmOBG71YpBxklN54ZJrZlMkvWa1oSa5uhz/SaxA==", "signatures": [{"sig": "MEUCIAW7P0I/kszDH6eVaBjDQRiWibkrb3e2jxd7ZE3YnKBaAiEAxkQ/pUY+eHI8011A/VtjEnzRrUims5hhkg+UjQgtlAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryXQ//YVVyeTEL+qA9xdOA/D33hV4APs0y03iyV4aWQ7ya4slPZ3yS\r\nTGFG7O6HrlcKgNqAT4jcGdzAhGdS2T21za5tnfWzawc5cCo3X0kKZsoM7dqH\r\nowq9kLmOJa+AB/cJ7qZd8/1z3mGeT/WW0dgnmM5mPe/GMbkeyaPTI8qXxomZ\r\nDObZ4r5Tyic/kgNCJ3tfy63aSa1b4GGk2rS0oOYDNFYELSwBALvvhtQppJoV\r\nLTo6wZ9lE+/HizReIzJvosKHIqoKysP8y8TMtbkfNBiVsxD3EEIKL3Kju0Wi\r\nu7tame2x0kfLMrhbHYh96LGb6IL6x8c6/0LQkHLptmdMOU4Rs7atwGUcNYhN\r\nayQs9V4Nq+yBMcrtmCX1er39rQUh0j3a5MsLlmp4QCziidsqKTDe4VVw+kLx\r\na8nkRfyadS50531DvnzAVJLaqPDnJtYQUM9PvftcOl2ejZIYqam2J9DoBckv\r\nZ/PInaI7BluVcl/9Imjgh7IYijww0vpg4qsxC4Iq3aMKPyZY8Ij4T3zyENKb\r\nbhcRApMIeR8PGJbtXlnXK4ErDZWyHzgD0zP40ynF+a0MQ8xj4Ba8wsf8FyxR\r\nq2S7dT3pCjpvOlzpmDo20EABJl90gv7Z+lUvB3BxnBDUjxXj4ZjxvRlPHlKD\r\nzqg/kqAsaeXaW2hvmaHM1EBUwEDe2fjLOEo=\r\n=dPC3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.13": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.33", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-presence": "0.1.3-rc.33", "@radix-ui/react-direction": "0.1.0-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-roving-focus": "0.1.6-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fdc8f5ef48290abb04feaf6eff0df8a845176da8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.13.tgz", "fileCount": 8, "integrity": "sha512-qKyJwub5mNhJmt1UYltxeParlRnGYdkhYa8sZl6kq7WyGO9ruoHr5Xamtb/Keo9N3AY/HSTywGhqxF+PjaZdiQ==", "signatures": [{"sig": "MEUCIQDcMFZ6dxOgPLpux3xBbL/Isa66R0QtIw1sKCpCCO6TSwIgF5FLWfnOFDNVwCX9orv82XIb26w0c0WgE76fgvmog8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHctACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjEA//UlzkqG1Vk2aslE88i0aoKM/GR0ZS7NGWU+rTgNXhYSs1es5N\r\nLU+14ddpZl9wa1ykop3kGwjMPPNbtAWdip1pkeAt6I+HCfv6o2PKt30AMO3h\r\ngkoVOClPrtBQy4LW+sljsCwDTHg7jR4qF/q2pWAhLd/GUEdfjrDjKl0JRCDc\r\nrOLiCQSqe2uEeNugev72BZsyi33ISBfd/9WadNd63nyYyGeFYoUsRxNBScsf\r\nOfWLKLX8nAnDeMKTkzErB/DvGmsv9XNVVW9iDFoLk63OBhhIGdiLM1JGdZCy\r\n/AXT/JQ4GbzuXtT7rbuCY8wdtBEU50ziwYG4nrU0rA1ZyRjx3QpjmhA6dZ56\r\nuqeB4zsCxHpxmQQrPc3tTtAunEFF9xz0jRyWnOx13vx4XS41tAI39hTgoEP1\r\nurk270ma1XwrgMYAavhAaotLq9SE7EocCA/SKe/MGRvI/Bmam5VJpSK3Q5j5\r\nnMn/uppcb91+h7Uc1xvmG9y6QPBgdvo18bgSQy4goKqDXGmGgQyhof3fN9vb\r\n06PdOTAr5r6IgfXFXmggLpvqO1Rh8RdpaUUMiwrY2dV4F2IZQDBg9GCu250m\r\nEXNkxj3uu7KytS3GSdZ+virVtrKh3T+J6KBrRpnXF7hAwje7NrOgQU2qTj6S\r\nERlrUt/Al3/M5ZAbXks+Dj8tUjBwhHOHp6g=\r\n=13zq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.14": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.34", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-presence": "0.1.3-rc.34", "@radix-ui/react-direction": "0.1.0-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-roving-focus": "0.1.6-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6b9a58b4cf6a6783d28ac3a3e86e578af9518347", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.14.tgz", "fileCount": 8, "integrity": "sha512-HgaXdhSRvv8UlpC5woXXb2S0SHu8fRAkcPL84RH5k8m00uXbTE40UJrV8Q4bHm9z6vIVjAoZNYL/QWzVzJVmTg==", "signatures": [{"sig": "MEQCIBErewoFK2tlRaHzWbKmADGm2Kutu4EAJ9ySSocZ9Q6HAiBUgcCZa5WMCOqNnrr9ZUYQGJPOMnRHNjLH4/x7EXmMeg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZ5w//avQXW8Z85GSNc6k2NjlYzS4x2lptSQuLGzqGR6FZBwOEbuqM\r\nJnaZEa+yyy4RzScvD0+UNxmSZMp19SKERXPBMhGUvfhsT7X9A4jIDjhW/jsh\r\nS7MR4GGhR4jzVGCm1NM3ksA7crStL308qI/bPOSyF5il/nI+faM9r+HwnYXo\r\n4sEOBIucHvA10zKpXFYJKE5znZad9a3+dSq5az28tXlqt0upfXPHUhxZyKg2\r\nzX5Zt+ebhuDOSJB+GAi1K/QgpJIdpr1cE5evW+dyk4yE/Q18NbOOrz7Uxb1X\r\nODrplKZpSXkX/Qd6cL3OYHBqhZIREtRyCujkZMfuR9Am62NHvozQbvWnCRQO\r\nMMXwtEDCi0BpviwaylRbhQLTvemJzrfRfV3SiCG5blN/znu/oZxmmllwWqHR\r\nw1WO2+ae8xCxiM3sBDftdXqwbUjlxEIBlwiIgzCSfwKx46qUDhyHeCzy3prL\r\nInCYLeCNUChlG8VeH7k6xBQPaVxW4Hj8ZI2FsjlQbZO6xRhCcVLYk5nw5FUL\r\nxV94+9iagrMW41DgdfZXkDmMD4nagQegZBksWok22sWsy2KmbChFwVbqJ3k9\r\nsNn8Pf13ykPAlh/+df+Wp+6QpH6tt4hPe8N5X4Fs2PYQvBf8pYwQ41cTEq+0\r\noRtKpV2A+ODB/JACq/vdswzBfnkTJWuhgQ4=\r\n=eTVV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.15": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.35", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-presence": "0.1.3-rc.35", "@radix-ui/react-direction": "0.1.0-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-roving-focus": "0.1.6-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6967331371205495e85acb22f0fc6988e8f1d150", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.15.tgz", "fileCount": 8, "integrity": "sha512-yuY0Tz/lErg0W1guekS5kzGlPFcTGurAbMjkeLSFrji1pDV/fSarEymILwfLTNKjBin/i0FxWWFMSXBVl3XDcw==", "signatures": [{"sig": "MEUCIQC7A9vuQYMpYM5VxypGt4yucSrfiVRrRDp4zBmk0enojAIgbp/X0D9Me1FS82KzzYYR+O/hA0dhx2ddKZlx0uMOXxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoaaQ/+LeLYPSGkEZf35nU+aQFVZYq2c+8IDCCzmdGTHJPK8oJfRibt\r\n1+KW6ey2oxPQDqn3oiEmHSR1jBgzTeXxxIC7K9CV8S7hL4+8Kiapclwmi/+X\r\n5cM9acNz5h+5SOWS4JwES3wju7j/o52cIwZmIezcJSkvoyRej9IhR9o1wzjQ\r\n3fSEHWgrkm4hHIKx3zkYGMEqkpdGmX4vN+kgHr58NHvjxbFYT+pqzKygD8Vj\r\n1PZudAPwn3jXnyF8UETr+DuokbhjR8lL8tWSZPwm8tmJAoYWdDwj2CYil15l\r\n4Z9ujR6fUsaj+OvA20lZCiGoF1daoJvQ+NgSNfW1DbTY3f74WmpBkvLFuXkI\r\n53pzxbND2/W1+MEQvDNHN0qGu//F+ZwkBHE8Gu5U7d+Skm2Oy6AReUtqsZTM\r\nIQcE9DcCxTeimiECjbFmkyHeQKHDbV231mUcLaFzIAohpXQXXJgIyUoAAEx8\r\nJqkz4uoEEB37WfUThEZuU2lI/xR6YrwsOohU9XAsxOcbQxF2uzSoAFhKKhuY\r\nrFkxuEEz8tggdsoAIPvGvO/ZmCp2UApr2cR5JYysnxBFtOF/GL9b0fYg4jHe\r\np+c/v13ap+eTt3q/6Du6JoYg5JQycPtGLDIOC5FaMqoh9gDWkdgy+0vlpblA\r\nXjQFHjwmGaWWMWZ/vbaIyrgjs0OT7155y2c=\r\n=la8y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.16": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.36", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-presence": "0.1.3-rc.36", "@radix-ui/react-direction": "0.1.0-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-roving-focus": "0.1.6-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f0e1a131c9d55f272a344a1b9b16e924a5e7c37", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.16.tgz", "fileCount": 8, "integrity": "sha512-ekcD+SBujU+Dwsa5KNHIE3aJ034GuIxRZ0wXFw6ZgthuOWIGfy7jVJ1VVeIK9avIfzRAnsaHa57sEGgK0tMtyQ==", "signatures": [{"sig": "MEUCIAgNqu/d7jXneQNjeXrzgp+SuXDo6p2MgDB04CAYM9HTAiEAqBeoEzWIjBhlHaSgaMMMKdjtJzL7snIBZ5DzatxgUgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0I9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6rQ/8CM6VbEIsv64v0ZawQFR4GGRW6OIw8BMe4UH3flgxlloCE4c7\r\nq5drH42GrfvgepxVwsFqBa4yBrn5sOu2gBt66O4xLYs3rwAd3MtwcuU8wOnh\r\nMmDd0AQmJIzIeZBStH0Yu+rZVMvrFMZwQttM8LndidMOSxVI+g96HEy7zuta\r\nu2zpJM1J1kvxOTyH72guBx+MfJJmUjLOIFopUXcp2EaglZYgkrsvUTnhZqUQ\r\nu3qIzv3e+WDuYBgAsXr2CARcFb4XlLdE5qItiKkiNqmrLjdF8PGpBe7TMchX\r\ntpFbbow5kaoAO6XsUsVnEChrRBTdBaFhot1gwWMHAHDnFD3cqCv5rahL/lzE\r\nLKOhh614pJK9sgCJXilzkQG8+gvENqp/DMGTMMqOBNxV4I5bThzVthcWf/zA\r\n1GAGZjPfw8PayZDj2WE1xKfrRJ9U911BoGMHHWRI9keBwgEQkD29H8gzHJj2\r\nKOvfLmWCseRg+4wx46okffdH/OkKHFRbJonZG+vFRImOGIpxZvDr8PfWBfJN\r\nDk72KJnQsDsQ5eW0SMDCiAtjzpSwZ4XjeyAFNopPh/B+HZGKaC7/LVwFkqMm\r\nQIZrZISg5Zf8Aiq+moY1X6F4cSVtwfStUR9mCJfC9stxkS0pQe5TBU420aW2\r\nEvcsESUmJjAG8Nt4jy2izj6lrLnxmvhV8q0=\r\n=NDn5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.17": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.37", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-presence": "0.1.3-rc.37", "@radix-ui/react-direction": "0.1.0-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-roving-focus": "0.1.6-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "824fa18053ceadecb0bfdf3acf1d53d56770fa59", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.17.tgz", "fileCount": 8, "integrity": "sha512-hotbmCnXJetzysFq1sob3N7YEq4VY+vpFw0exVtAiHM+E8e/BGv+i8JP4iXYqGYqwzXVG5r+dUvNylQwGMte5Q==", "signatures": [{"sig": "MEUCIQC22lLWhQJ4kwWWw9dj9AbvRpzXycNL2hFreup+3F2r5gIgEm3fQha+w/WRi7qLlmJ0bBvHluh60roa9+w35zPch0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4Kw/+NqJnHyJjLYP3/pe5XdpARh8GPQnU6U37dPU0DPabQwWl5CiP\r\nm+n8HcRLCZ2/GaBIkKkgUUlR5JN+KoHqBgDIWVam/QCi5x/pdhv5kB4QHNDZ\r\nAvnkzkmdX4uKM/SMnH85tVPX+QlS/198t28hsDN8IHeYGWIi0U3CETsI1pdH\r\nfb+yGnGXgNXKCf9+aMOjblKT2lU0y6Zm/mxqGL+UCkbg2kMhwau29iKT02bF\r\n8/+Rs2FBOpAjKKvSv54i+rDRmLjv3D4DuH5nBX/cGTDUEbvoTBgtIE3DxBeN\r\nflso6c3JxvjgRUvfCTvpynnyDq9XnLz+7uawPEVSPWpNk42Mpm1qTXZP/3YI\r\nMNT6GhnmAJmPlumOdXDaf4cu7IGVcKcWMdXKaWHBTIjFWcKkr3NTFuGRuewT\r\nhWT7xRkR8G+L0Atwle+Rfb0iX+A9YvvCe3djDvK+zXhgNP9XoFNRDKGirupD\r\ni632as9EUOBQiSddgS8TPvvT5V2xPIpAPrQtLLr3SjDqqxZsX/uwr6rcv1+F\r\nCrjC7FJNcoaDhxRbDFae/0ABoVa9KFlX4p3h1/pF956hmOje00gwOKHBKkqv\r\nIIF215aQzZADSU8C4oMgtyt/MSvfWAqk5cQyPoedRn69U29O88NR90EmtFzq\r\nRa+AxKf/S8MwmYSLTdbTck9JXzAf8YpvZew=\r\n=LfcT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.18": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.38", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-presence": "0.1.3-rc.38", "@radix-ui/react-direction": "0.1.0-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-roving-focus": "0.1.6-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "81964738e7f5dcf77b6a4f13db1c52d464a552af", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.18.tgz", "fileCount": 8, "integrity": "sha512-B+lQodtC1USUsoEGUtxHG2rFB1SZc1bfWGhcPgZqH/B+gAlTyz1gLbHF7Uew2Zhux4gp+XlvSz0yuHX16WT5Tw==", "signatures": [{"sig": "MEYCIQDf3S0vDeCQ6CAtDvhiVQ6bJQaPmqf5Jr09FzyH1Yzx6QIhAK44yzi25SIYMlMzQBggtrtuxrGDhuyUXoMKl43aAm8l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx0xAAk3B3l6nr5NYYM4AnmeyuDo7na5/sPQ8ntLzI3y5nxp+FYvDF\r\nL5h77R0gKulskKTMn+utYouBkhAEb6eFNUTxnWymhb4H9/kgx5sHsFcRmOew\r\nrdHPQPovUTGi9Q0/aCbenYU2gXFjMey8KNwU2/Oi6lHwJX2R9M3h95sNy3O+\r\n781BkPnF2zWAXK5bGHW6PACrzSaN+iU+t9hkftXvKNzXhQ10m7/HN8BMW3wD\r\n6AUtOTdoKhyj0p9g3yj5mUpXFeF2txJaxVtDLMU5ywxj5Z2v6wHyCEKCRkOI\r\nG+n0wEqygkEcvAr+nDNqUOOBvnrjKM5bP1BbUCoJSfm6kZVWhup/FbuhGCbp\r\nIreaCLSzGkZUMgXEWCkpM+d/DJUmAvFiCms4IM7GSqXgDsmtxIaGXfLtKFNP\r\n5MYTamqX6mTUeFYA149RCQ4UyVNM1hpEJXk0t2r5x1mQLODvPKzNJSxiJSvy\r\ng/FsCApYW2RStvgdwlXHm0HSIc0l+oR+IgMXqb1k8WU//uBoCIYBnwVtOwUs\r\n+n2Z/ougGgJxj/8QWVR2/XPzjwYBYSKQaVRqu890etwilkgvuYcZg2wQx6eg\r\nRgo/7SCXLWRfr2Vjf/22A/pCgE/ZvvpCDk7J2/2QIx6RJZvGdB5Ctf5DFJEe\r\n7TztlSfwTN84RY5WEw7TxCY52cyxplu1Hhc=\r\n=dz/w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.19": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.39", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-presence": "0.1.3-rc.39", "@radix-ui/react-direction": "0.1.0-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-roving-focus": "0.1.6-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92181d99e7244c66e472e99edbb9c54435239a19", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.19.tgz", "fileCount": 8, "integrity": "sha512-BEr4BGe8AfX+2Pi3TKHjWjXrl/Qji24HUbNjp+rIJ3g6QobJXTPl0QePZ64DAdYm7MQXmuRQVNvL2EYUGvfGlA==", "signatures": [{"sig": "MEUCIG7BBLlH3KNAjJ9U5Y3I+XgQFlJ4r+nm1OTUGY9RnVIZAiEAzuhpVrKW/6tfXhr+Ntmna9PjEHytCpwl0zRjKcuwWxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUIA/+KUo/6W3sLXiG3VfOGCrPAHQJoy5AGfy3BGbXSjZcOyh4g/u2\r\nLTOWO88UO2zoFMyxSg9gLRdsSLxlCpjxl/cIZcQUCrmVDjo79DsYIQeTmnX0\r\nI0vGhbOgSJs/MriS/LpQTyltoFdSQT8cmjg7f35kSjA/ed8cshJqs36E5ItW\r\nZX99O43zpbaVBT/gqFkmjlemGtPj6nklYMb4mTBZ+gvJRVLjUFAFPovcFsUW\r\n5RBOpqsW2Q2h8OQh+As2+2oKz/IuZwWjUqgWbrG188gmqbWhTIyPTsKi0nok\r\nEXDkj8WNMC3Idn7fmhT0O/DbT0n3ntOhWAlokjMXFJo57OsrZgO6nO4QiUHT\r\n8NHsc1wRc/HN4R/Q2TzJgj6TA+RorKChwOyrBYyS03w/zFTkiKpyDWgdm9LE\r\nCeYylvRrj8NKUPzQmSY8H5Xjohw5svnV3e19B8N/y2hHWwNwlJq2vZYmXylZ\r\nhx4Ts2blCY3veiTV0V77Vxh+k6QwnDBEXqksuTwJnvXgvGKPGgoVYL/zZNRF\r\nRlEnLptJcYbAyjxrbXiajQ/8VvANDd3T4EagiAfSnKCThw4Bb8T8M7yuhUR9\r\n84UoA/G0ozF/kxMY3QxEQQ6cxlJn8woUdKBV2DloisWC1tsL6quPBLEvUhUC\r\n4KrY4QK5Yzn5kYQdeoOe5+bQ4HPEM03WNDA=\r\n=mlEY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.20": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.40", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-presence": "0.1.3-rc.40", "@radix-ui/react-direction": "0.1.0-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-roving-focus": "0.1.6-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9813e3abc3114ace41522f947a73cb733bc6e468", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.20.tgz", "fileCount": 8, "integrity": "sha512-ckeOAFnXCpih6TLZ2E+MKFYGdX9aBi0CSmAhUCoApPk6Q7ZB4XsVu7M/1qejqQJ8cuT2ymrc8fvV0CSoxLSCQw==", "signatures": [{"sig": "MEYCIQCDXrYtZx2E+doUNycq99AK5/5zgFqV285HOb9LCb1QqAIhAINVx/1xlpVpszd3XA46WiJGZ6C2c7hU+OUzl31clMbV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh+RAAgU1I8BW29wn2dstJ8lOJXMLE61/CENHkWVs4O5FxuII584sH\r\nHHXvjpCRcXVzO6v1wqlnRlHRap+yTDOl22MuDsBJHZZc/PTeP8zgYjqcUmnS\r\nNRSCetTEz+iUJZtFbt4iFthW+hRse6Jv+QnEutMXKdB3wOAHhvx1qSWW++fI\r\nhDCgh7w3RHn4q3mMJ8eBurWgTxBT7hqcwzWGHdC0GQJZTddJDZTunoY+zKVW\r\nzdhcmR3S3i05z+Q7jQTYfoCBjjzXOAX97aJEdMccL4l9zwpr504XzS/VXf4L\r\niaqCkOgOxbJnsF01zEgWtakunYlOGkVxS+X3SkXwzT/8Hiwgv1BUOllBj63K\r\nrqH1ASPImrc4c42mgLFiWUkuZRYMEgnVdDIU9hmQMcdnzwOkwP/uXMYP5fts\r\njY2PVnH/0gji1rKz2AVEx/7r25YZ/tdnuV2m8I0J1B7tUkpNMOIcvWFnUXQ9\r\nEkQtLHFsLXsbbrlCmz6q15O/+eqNEa6RHx4hAve8+THlyFgWETHTLwtLT1KX\r\nTLW6h2S9Ok4Yf0KQO8g+TwNYiGctDChGItHOqRJbPfl13sJw7Lq0pcqWt+V6\r\nz3p2NxwGHdLov7+ZZEiApS9U31DWfZFnbldOUi32CxKLFTY9fxaRlam46WLs\r\nIWP1fz9AuF85gF4SQi2KPWPTNFzUlT1xPL4=\r\n=Xhi3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.21": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.41", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-presence": "0.1.3-rc.41", "@radix-ui/react-direction": "0.1.0-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-roving-focus": "0.1.6-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1013dfaf14e5f537a51f6922d389987bce815daf", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.21.tgz", "fileCount": 8, "integrity": "sha512-Y8WzLAO9kk6CkBu4FV2v2ojVSV6af3vCtpqeajPbS7Dwr2MuGEEOERUcBP16y8SE1W6hA2hatZ3tmqr6jqu6UA==", "signatures": [{"sig": "MEQCIHuMnl5DeJNELrz8ix9Yyksf7rp/6+cqu8z5TnN3cvvlAiAOwIsWZU84pjh1+KroOIot+VoU5AR5wVaiYTHynVfjjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0FQ//U7OdoDVCCiadY/SS31cH8LDWSoAOvhCHdCDSLAu7CiWey+zm\r\nBzLeKy4Eg4CaSjiA5UBJ/2D4EkCSXb3NToq55WQeSNctXJKV0tPDUhy9yqeP\r\nneAuJuKzVYdh46gd1DiyRInCzP8M+B8aDzVfTm+Pj9GgEOuR6viGhEMkEkEt\r\nxPkF2mKOb6V5doLsog+9jFdjuPCanBK39Mev/QAfQ75Sz4YfODSumKKGVcKm\r\ne3u25l1G4f756tjKWJCtyedGEg/aqXMTbiQZCoZMTf+7UQA/9FJWM+0m+C2j\r\nPEZq3Lc+yz8Np22pXodX8yo+9VtUBgxO/3XtnS59PzacyWVYL3wgCyT7R8lQ\r\ncPDm08t8/gOOW8qIJoje4I/x7OiOU5S8L1DgvIqVlVR6xu0c2ZL7PtKSvdNk\r\nxQ51fBRiI+5YUFnRJIP2ZkTPPDtyCpr9MtDvnunDsFW/IlIWBuqj2rCY8Dde\r\n+EAvulhoTtA4bc7nLFpZdiYpkxKwD5gQaGnzB761BqSKhH8UKVyQWiFT8oMw\r\nIhjF/U1KuuT6mCw1bQYISCglX+Kl3K/lUgFw0kFBOamZ6aA8sgtYPWh7FlNe\r\nvO1utweM8dWLpoGpWGRkKKxNkXrKLRDHaLmlg9A0dD9U8vD2V9Qur2Nd2ORb\r\nQZwQ/iVd4iHlcCR4O+krPeGqVfB2vyqlzdk=\r\n=+U8I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.22": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.42", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-presence": "0.1.3-rc.42", "@radix-ui/react-direction": "0.1.0-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-roving-focus": "0.1.6-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "27ee3d93a7100cea9fe27f61860a8049e987b1f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.22.tgz", "fileCount": 8, "integrity": "sha512-+1mUJybulzKkrn+23Eb/43RZF2pSBKgWG8sneBZQERkH8K9kfUeijuqwd43uPzMgdJUQOkn9Qtwv9RUGLALFeg==", "signatures": [{"sig": "MEQCIGqbTPdou5C7qsm2kdHXDlSSYCMsj3wcZivE9lmgMJ+WAiBN7WsVVR+e02Cao0QT1kWJl+6RLHaWrgwn2pm1h9jnXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuNRAAnRwjP9U8A0+c2n71egF6m+DpU0sLRaKJ/WjubohI/mV0M0QV\r\n+Lx//GC5iDT86+Ox8pEwSvpvpRHz0mnq8Xi3UlNB02rMaNF0sKhojo1AcMua\r\nUBzmieCHarA7Qlc+BKq2xvwIz/p1uv4C3MDSanbNRYR8Fl6K/2AUH+u31aLa\r\nybrbtF9OHgegeXTR9dAm2GAWpfUgXQ4u6zThP9OWQoOVLKdDR/2/ONoEd1Lb\r\nd3yr5Cgwb2rDQhW5fg6DJ6TxwieENFhVzBJwk+9XFHI5alF55qb9ZqjBCIgu\r\nKgrfeW++Jxp+QsOb4mGD1nMPDTPgz0T8g3hZRHTobXw2dohz7wsMVvKpU5OL\r\n0AHzvzXvhNHZXUC/YweunQYXmlAOe7kk5FgAkaKyziyE/LJqVpGNeLMfXIyx\r\nAUEuROSZU5A3HnBrYFejYajFE5LM2ObRD0k9l0y/fC3mRK+e1/iqYHri7Ro8\r\nf0/KYsJzIUDfDyavKch6KQkiseoMlBuNA5PGwzIZ6VOcPLte6jJJ74+VeLqQ\r\n37E2pWj8WjjOR71aPoIe+3wLurSmM7wrMTy0JRCNEUXxfwN5cR2duhKeQzeb\r\n/Ijf9hHhchbWvSjJrabiXlNSyaJyNnyqXPym3UUdsobRMUi7+Qb3/pOGBHnY\r\nGjldl41X4e56y/g3083rKChcEcj8UZerutA=\r\n=4iwq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.23": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.43", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-presence": "0.1.3-rc.43", "@radix-ui/react-direction": "0.1.0-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-roving-focus": "0.1.6-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6105c43ea281e860eb7d76b87358e4d9aba8f62", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.23.tgz", "fileCount": 8, "integrity": "sha512-jANtVzW8JZxBmQdfbLr4IfADKMsyB/QakiYDFPJgcvgukarvxh0kZ14pm1vVGqebdMfw95pFh9kTxYX3nnkT4g==", "signatures": [{"sig": "MEQCIFkd+HubGjDThJPmU3EMGFbHrqwZ6OEmK3MKTabnOyt6AiAidZPgETa6d9eoucV84pOOiyjFHsm2fNkU6SunhA6O9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFQQ/+JTAERCaK/7MU5gOJP/L4E2hg5axvntv+9a5l3SmNKZI8BPUx\r\nRb3O44VB2wt++CrYCQ9QKmMcbZM/R8rxbo+Lwq6gyMg1Lioefi0UCFAe8Sv6\r\nP5vxbHKdCSNMAvK43P4+AvgkbuA6Oribmm8MkbvZ6had77SvYlwdnnoYRWpp\r\nnr84a/qH208UmTPrUOlpn85Q+OwPMisqHYlNnSB61KKW2PpQLEue8nzc6Xh3\r\nLZpC647qEcbQzhOpV5VfrMNVlxMgsWQk8TiGjSEywq9gcjrti1iZzzuvYxpJ\r\nYGXXWyQhmeqgoSQ/8BWVCLCe/5ya/xZhiSfyfbES4OxiGXMjp3LjihYmZ507\r\nFdVK9I3druLhxplljsHV5+rwCxFcvE5DUNh7KWe1CJv41KoM7i99ZDBNqoeE\r\nzz+QGBxLIejWxV0goSu9m6JYiUE1Xw7f5UFAGuecfinyQ/1fgjGA/n7aLw8W\r\nHLunCYWQVPFep0tT32+bBAZyotki9DYxYPTmXymtvQhPo9sIGVMGpXSeVybL\r\nwui12LQ97VFaQt5381f/neWEDzDFnufHBjeEuYMy2HpuZKb44JiTH0ktZmLo\r\nnUxacZycPqEJ768Z/ob5DMqYzpcY2tvRG3FkKypqeAbQkc5yTJsnFFyKUkkY\r\nVh+8Zu287gf2ctFvwMoQ6kafR6amX8kkoO4=\r\n=Q4Pu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.24": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.44", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-presence": "0.1.3-rc.44", "@radix-ui/react-direction": "0.1.0-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-roving-focus": "0.1.6-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "741268a8123915c1c30cdb873d11c4dc284214f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.24.tgz", "fileCount": 8, "integrity": "sha512-hTk+FmJmm82cFvIFMyYpOqbHxayf1VNriburNecAhwkelBWWnbHPskazW8EQL8oCjcBM+z5/MHRuwbIa5UMqDQ==", "signatures": [{"sig": "MEQCIF6bY/MZSkPgTVg58ZkmEPnwEEAsB1DQwPz7GeM4k3RwAiBEOKRysLS//C2+vmmPgFArbcPFx+Aor0ktrGG/BOJ6Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XHEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXJw/+NyA9ztlM9dpV4pVbFZvUrdU5VZ70SSTR401waFIpC6RyGpS6\r\nT9bHWaUoudVKJgb+cjQqpOYqfrSa+v1CyvV1OpLmJf0su/XDQZZ/TMEaoeAE\r\n+dWNbz/qiYlIEXcetbm37f6lLd61kskTvsXFfcyt4PT8mESfgKYkjtNcDsWI\r\nn7py9/q5z6iP6PtNgUYBWpWRsMMR0OD7gfOlahGb5NtvUHMtVqaW7/d7iKMG\r\n2Bay4eBxzYFhnpm/GqNx/go4gMVh/C7sQy9Te/kyklTPS77ZdS6eLUvrVYtD\r\nEOt621RmZHEKhnnxsuvYYjrw3lOT8KmR0Q4vmTWJ9biyT03Fz9JCM858mJ8j\r\n7OORTDVi5l7MPpOlVMYIUkTEhqRoG9+PncXnlcYji82fr2FtDSUdHgYxXmlX\r\nALjAfFSiImiTjCID7BKG+faC01//4T/f4JYnwZdD2v7LiX6Y/8u+66v1fAGG\r\nZCIfHyaLheyD46KKt7twKAZth5R0FLVVqWS6KpY/8BQcTgHPA4e5gOHvrzaE\r\nE3EibCKN29pdb9Ky+jUirlGQ1TiL4CC22vZJk3Kc2F56xLOAEHKqB7dX/gP/\r\n1JFTSCssSiwiSHjyTSti2JBwovbgZVcOesvdgsrIxKkKHDfcutcWxdMXRJMG\r\n3zBQ/ST9nTlTmhY+g4IYf1PwW016roMzbG8=\r\n=I6ge\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.25": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.45", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-presence": "0.1.3-rc.45", "@radix-ui/react-direction": "0.1.0-rc.48", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-roving-focus": "0.1.6-rc.48", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa46d9a585fd4ffc134b3296535d6fc38e7e0f32", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.25.tgz", "fileCount": 8, "integrity": "sha512-FGQq7Q+K6IUGSc4rJe64QaI2WHhZKnXq/hsIfR3e15ZsXvAqtf2pMMed/Brq/wbX+NOh7ChgXUcIab66RmiEww==", "signatures": [{"sig": "MEYCIQCL6J9NhnYd7dnhvPX9y6lDTHOJKcpkFDMXYG36axCC7gIhAKS8RBInzp8YldDHjD3xjkzfXF+VYOF7YbQ/tXwHtodJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHrg/8C3JW6cbKsVs+y5R1CcP8xtYeUlUzItBy2Ztm2wb12+4ENwfC\r\n4q08xPEdEL8ZivKCpq0Hj1VQwSyQ5Vu/FcwtUKEJfKaFw7pOWzAFPoYqq4kB\r\noajIEvObLdYdcsbKp9ncAmg1UtXPX2V8UkltQ3C11cvbfBTzPX6CEPQjQG0m\r\nk3hDSMPuprngOY/G2+WY09IU2P+FsdLcsujB5K7OSh4tiOcUEwmuckCixNaS\r\nepXMAmQzWytGFZ7qKZsSGOnM/jr7CkqQ/lmunRf77fkKLzpk+a4VFJBZb8wZ\r\nZzaGLgFcvL6+wIqmAEHt1n/krBAjf8MWMeLPMcjGg4KaWSvBOPnJxQQpnoTe\r\nOWK6R9RyaZ99TSa3SoU6kxrk35QgrJSYoX8D4vcWVEGhGZsDVcDTDPHfkiN1\r\nbnRN5L8cW4c5IgfAW3DdF4hhjZc/G28CcOFFrLYilUyF2mZRtTFkOsrdzYpx\r\nCcLIrTv0aoJQRkyusjk7USJb9NgCp29KYFLwyBS0xQom2EWQKYe1PTTzuXaw\r\nmBeX9HehxcsQFBXvpBRkm6PGnVZwdIqPSgLeEMm6DAsXDXiPOC9NL8liP0Dt\r\nR+ZMC4lHtyjZ8jA7Sfw0J7lc4SBWA1sNDCF4OcZzV7kGRfLBnvhHM2qOpVg0\r\nWcXKbQVk5GMHpANknKln5zyTf8crNqSWP/Y=\r\n=wNS2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.26": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.46", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-presence": "0.1.3-rc.46", "@radix-ui/react-direction": "0.1.0-rc.49", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-roving-focus": "0.1.6-rc.49", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5bedff1edc4c3584594227512f978cafc694ff4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.26.tgz", "fileCount": 8, "integrity": "sha512-HsNEonFma0OGsYFTH4uQvxrcd3+NV/p7VImAnlnKBgAVVwe0cvFHk9oQs63VczyGAchQEW3ryBlLPz/sO0LcIw==", "signatures": [{"sig": "MEYCIQCHJhkPVQ9kc6wreZrjklBZVbbzvs5092IDo8PIy3rzrAIhAJo2BXVRAbkPM/uNG+M16zp7iIRTC3awUrv2ejrmtsI1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2bw//dY+hpJ2TRnz+CXaFaK8Sjh8KkUQDOiTH1u28LfCB/z+RPlLW\r\nbAFg/1MMtRWJu5qLFB++p3DEF/gtGcGI1DtoVhHflw+CygEbZzL4uio9JmAj\r\najImdet5CcmKvMAUHylCUMBPoCCyBD0ufT5GhkM6nHsOpe+WWx4nS+x7HnEI\r\nQhzIKFypRjDWWV5Gm0kgHKaWw2X7XO9miMcW+5+9/GBNa/vprFCkMbE+NnvP\r\nuv9tj5ZCZhseIAE93Ju/upTCLt4dE4B9xR8tX9Oxg7J0+fgdq8PCL4L+p0SA\r\n6fWGaMbGmtxKD1urBjwY99jNRtLHPMDRHRDXG0rMa8zLLEV1QaIwiC0AFwYu\r\nrxvoMmRyJXQ6C/vjwuZQi5OjQQROMgGetuFljeuNfrmhFd/fuKnJI+cAxymH\r\nq3HnY1ba+ruCAXYDCCXrEXaE9lhbCod7wp4QyjkcQuOJRDTwhPvK9s5o8P+8\r\nG8JSIifblmashiqvN0LhKJBC8gOKVCsjvluXtitJl6I6T+FsbJKOmeNBkkRA\r\n/WEek46r6vMLVaEPYAf2rt44yc+Ud9I7IiPmeLsX4AFJ83tufoEvXDtFgKX1\r\niKOsjBwcLor+GgOis4DhCQzZ4wEIoxvUbpV5ZEMkm+WgEC/AjJpoUi3HXvDK\r\nWR1JKA+tdINIm3Mq66FJWRd551r1YPmxSfE=\r\n=uopF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0-rc.27": {"name": "@radix-ui/react-tabs", "version": "0.2.0-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.47", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-presence": "0.1.3-rc.47", "@radix-ui/react-direction": "0.1.0-rc.50", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-roving-focus": "0.1.6-rc.50", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2544266118888156a9ec631c04417a25e072117f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.2.0-rc.27.tgz", "fileCount": 8, "integrity": "sha512-LW3H5SLzWlSxK2/FuwLnuorfHHpbE+L5+KVnaR19bbXSsrbSgN/2xmwkG0uf7AYHpIrX4EOUaI5wqPQt3r2i5g==", "signatures": [{"sig": "MEYCIQC3eo8GJpnZ2SqjQ/TnA1ivV7rZNncKMyvGoA+fQNKczQIhALfEoUHwcfxQC8NFeQ/ejocL7aLqjnzEBW/3zNry/4GG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CFEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1ww/+MJxY0t/lw7XNW0my8W8z6V1ivy5fxC1c1kiI/41+0sjR0fM2\r\nlmtVgMgqcbZjI10xFQnHgPTi2dHZQx3rKJFK+HEc731P0kOAUsCFm5QHsrbF\r\nRvzSzPdJommsYEa52SwgVfQt6tIDH1eWaOzCF/bPTAW2aSIwYMjfrY3Vha7+\r\nolvaVnDkxpLCh2awGiLWOkbCv2EubR91EZ44iH83Std6tMvEwUtVr1MctJiT\r\nlbx6jqsi4s3oiOA8oqWD5u6AKxHpW+4GyWrSgQZq+/rMHxja8Nl9qHMV7pSR\r\nM653LODEZsjw/Wba5LW9yLiaM1bCOrYJpOuHCwGDLK5V+vC8B4y4tzdkDYsl\r\n83n5K4oo89Kulh9nTXbqbCY9rk1fZDEJNNf5ZWGfL3cLUUXfLrfMhmK4mNBr\r\nIWIYQPrmnvPAoqdux+IQqzRfR0giEHxl9V8tiCWgwzJpQrNrfSi2lxr/K2XG\r\nQzPNEudcOfieIlVXLF46+MhdTYphGjzXU6k1q8ODC91r7+fuhy0BH8YJaDJY\r\nmex+zJ6ajif/xzkks//Dx62fVtPG333W4bLa/nZBqnp3tLAgOWFOu7M1XHS6\r\nRiM6owRW2cFG/hLQkiexoiw6ynPvkjhlyS/agKG80T8WS2kCgePqGQPHQDbl\r\nZaYXiiIMMzqgAe3dDHlO+fP4DyR5avmUZR0=\r\n=2rpV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-presence": "1.0.0-rc.1", "@radix-ui/react-direction": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-roving-focus": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "38cd03df3cbd60603dac60462a3a3d6dd98cc41c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4l00kJ3wsCkEdZPrSOpUKQUIaHWmc20FRGBHZVWzXklXz5kg9J0YdiFB7YEacf2Ip8h7QQ3amuUaMlxbeCI43g==", "signatures": [{"sig": "MEUCIANeEp8NcjpTzwpQER8E6EkiVp1tfJKLV/I681D6fuwGAiEAoqM4Gsq2e03mS7C4nwwkPj7IZ5V/oyNUw0WJkpD+GCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPoQ/9Ggnc1wUFpH97Nx/U2fhWXAxRqhzgE7emaP9CJhwd+z1tSqZX\r\n1T7DKuzYy6iTMtg9+dJBgKLWE5bdfGY7Lf1wB9b4EcOnKr34FAo5OFR4k5vj\r\nDr9GrNBvyMJbdOFGC6eHp9iVAylh5zHMvDPZ953KLAx8ZXjyimQ5RjLmY0zm\r\npQtnBRaokVIhzNneNgMPxPZ4MVld9jPmfqrqrfmOZcv+ISnwhWvREG1sy8HA\r\n5pmKjyomnLtXcG4e0kY1WZywAUW2Kj6fSjKCbfz1bNk7DU9ryWjs0ijRQj8Y\r\nlAzmkioJ47fUTcgIsyJ7+s8MwQazCFhL42uC1aKy0t1/pSIEJAuHdDQrvmEX\r\n3fO2/X/dXH7XT82A95s50OtQDY9AS7CtCA2hfLn1kFD7MPzS6srfdzqFv+AG\r\nNdwvkoHgmUv8aT2ld/MGfHYsQXOJA1aJjen+UbLa2uTUzi3dsJjDrJe5b2mj\r\n/4YW08Z0RuAjSN/4bMS7j91w1ylCdjnH0EJWnKn6DjZ6QFJMJc4i8vA+r4Pw\r\njT1YM0GJGrNFHJu8Oy/1KmtPHY6uEV1pkWnIACBU6wnGHAcoxjWDaEy2SVF0\r\nHZ2QNwjpGAzjkEqTU/idNxMb/NLnhK0YTMdY4i8+UUnYm0ID7bvML0Z4YN6d\r\nW8temPSv7Tmqxfl6c15iSCoq9XSG+Ep4OkE=\r\n=pmtF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-tabs", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-roving-focus": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "135c67f1f2bd9ada69a3f6e38dd897d459af5fe5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-oKUwEDsySVC0uuSEH7SHCVt1+ijmiDFAI9p+fHCtuZdqrRDKIFs09zp5nrmu4ggP6xqSx9lj1VSblnDH+n3IBA==", "signatures": [{"sig": "MEQCIAlILhvLHMLZIWJVXdagTECcxl9oTuH7O0EOlGs/1oGWAiAVrU0NPWn8CDROON0TxQ8afqK8KwiVkmKa/FMiqQvOxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeJQ//dPUjcNhxH8LNankJa4xEDVITO6uF6SWCKpGbG/y/bK8aG+/R\r\nQjZZUTcuMnCk0j/0XFo9itqPLFkZQuHKUlrBC3+ETRm16MwAoWLDOfisdR5w\r\nH+W6zre/+Z4GXV7+8+mIdvLw0xuVz7FK2HBNW9tACcjDfm21Y2XP9sPOD+5r\r\nlz9OVtWgA3CiQVw5djpEouzSxaQ8dHnRip6G+vXqjw2qPdkS2+K5yAOdP6As\r\nePWew32g8VrYELSY5iilBABNNPbYgOLkn3u6LvJOSy5xU0znoJdXQqNEVENL\r\ncNmR/DArmW5Opv8XxoLABWu95P+m+XmWWyEuF1/SHnAHWpqqCuJ7965ZNW+T\r\njfoFVPkdsPnxJJMJzTKLa/d6fDeFC7zayzCuBuyC4BD0vj9Y5udU9EuIour/\r\n6RjESF2xVQiIXXgXkZr3D5WgA6V0IOjeMfvnBc5ASERrO1BUl4RPv+MZcHy4\r\nbmoOBN92tOxvuOh9TCiW8PLjeasQLEeF8zaY6ckFSd59fbVp22GTn6foabP/\r\nGSPRwdXBRufJm1sh2MKdXknCTZB4YGKQulZHMPfER00FzXB1xdT9hsbmlhDu\r\n6MeDzqNLtelrMll3mI1VxHIBOJYwGskUg63GEQ4wCQL4RoZzjposQ16Ojb/D\r\noXuu8ODBVMjrRnYvwyBjAX1LnpW1Vm2KN6c=\r\n=rlMc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-roving-focus": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "14ea138a0ac9cfe9bdfa2a662d2700a0a2fdf313", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-i5nHayaSG/xf3Sx0uluBpEAfKzXfFUbGYIL4yBil/wZkI32cmP1n4CiCbqDh7A8uQiMf6PfxPlG6w15kWQnTXA==", "signatures": [{"sig": "MEUCIQCF9K5LpqWk+sIPIEshbbzlf7DoDqz6AzLByXnKzfomRAIgEL9p+ppM8/Lb16XdiLOEPM+8Z5l2w7150c26MIB+Vpk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVQg//VVn2qJgYdR25l4f44oBK5dJ+wmoGtV/eW7xC2J44dElQKS1C\r\nf+7dx4Ku1q5sNGAW9UNaVANQbjkXbhyEm3VMvySqGiOQ3shA8uXHyS0yhZyh\r\n00ufH59A0i+sXX/jYNkZo6DbJ2V7PP9N6Y2N5a+lZdT+3sYd16Fx6WADgMMT\r\nQ4hj7Jo70RCH8uho2yE5qfT6aa0AgPwqC/mzgMAePT0qPM80n6edZNVthyxI\r\n7nJeF5tz4m1Mb4u/ySnEGbKRq+loqprwJd+4qctJ69H+Qso+gLz4JZIU2Vcq\r\npa+gOZxU0V/NLmiyNDQ1m0qC+Vd8r1D7ryhPTtvOP8oZAufH8T3lmj/QUDfQ\r\nmoujHmeVDgsBGaM4EZXn+Z9NrG2by3zIHzaDd9Reo41E9qQKgdF4YsKYWcUS\r\nsOaruu+noX9GZrPlosjpx49zqOPmefsp6D/XSN3v8e/vWe5fYPp12kMwkeDz\r\nizp7jnUrKT6mlTkylfa1jDHnR9fMTdS92j/6Ok77iy3XVRfZ2o3nt3Bi6bFQ\r\nEowf7i2tw8+LIc5zI0F7SLZeo75yUpl12HGQ7gaFoU55tV2hPBQ8QO7dgnAh\r\nHO0GHU7WehboaJGFz5fDEbHdf2rDkEzD/aUc9VyAi/p8Uhxd25pGfobtD798\r\nwW7Dt6tv2ei9QRnLN801vAeSy5M3Xw+Rdas=\r\n=BWfO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-roving-focus": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d73c73c3258455593eab9ad786c45e3649b0d23d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-mKsGp1TmOeG/mBw9sohk6WKm5F/231qQq8jCLJ0+v2MdBnI51pW7iqen9ZU4WIQqUv2KCCl73L4SxjVjkBDxlQ==", "signatures": [{"sig": "MEQCIH4xMFJgUP+7i2Nr7FxRsy5Md6H6pxCEU1ELXQIkT6ljAiAx74TDwrk0N2UkTdD8f8GKDuYS/+dHZSJl4qTbOEASIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYEg//YiHwIq0/Iav46yS6e5Wk1qsIP6zoesRzbyBi0Ip+93qgiBw4\r\nSGtfZ8NBNWQPToiULJGHbhWKurDrELOOnhDb2qhiiV7bigk6cTvY/Jw5VUkm\r\nRlFzsSkNd8l+f5gTTrs9ITGnAfneGUtEP3KRpeWjadxtOk6kXhNsvuzClv8/\r\npvmr+rPLXDSo7YHvKmF859SEsFmA+HGdBSdXNLhNSaZ4LPsCMufXTCZDc1tt\r\nCLHmm+NiWhQm8Hlk0WeoMk4irh2JT2yXjB+DvLqm9JaBjXnVRs1lr+BxCAMT\r\ncqzwakO3RqYgsPcmWoH2OMeBOTVZSsdmD3tLUpdTEZpU4S1xc3NAgq0YRNuN\r\nRF5uqunpectFc0HsLJu6fn54j6mB5jdmQUU/Flbt2mh7qyKYytxVnYrp5yT+\r\neSRb8DK4TsJs6k9JcgnPwtUQ/XJuR9b9+eJAdFPixPQMvd/u2/5MJc5NdFs2\r\nJ8eruDm9HDNMoXt2v7njzWLB7bXfdu6wSDY4f5r8nOljRsC2ZfPeUGAe8K11\r\n/CGybVs5BBS2sty7yBYG3nC8hL2xgRIf1gpFGhbilhtKSreaA0inVC5Ne+V/\r\nFVbe9YNgYjbBza9NYSR3g3s28j3y+XQOXuUUs8B0vi9n5vp1MrejzEeG5jqO\r\nnhxzvvvIbYjuRqtgAMEDCtE6sw84KAU9Sho=\r\n=CjK9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-roving-focus": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2425d9836e485559797857533c819c14fe6ffc8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-sysRe2+jBv6lpwV+h2gGNdILuoygn86RXr9t9qzLJfRwQPDpt5oN9s3ycRtqHhM393KeWIVa1NkQwFnphiaK7g==", "signatures": [{"sig": "MEYCIQCG557pSKZCkgiUUcxkLmdXKvVlORKk0EyyQvXtkgaOpgIhAPMkYU3Okf8mVH47mjdxUYlxRMiiXbZhpBL1YSTpraUu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr35g/+MwVcepFZ5DE6zPodzL38lmgyxHnHUM4DPPMvxsLZ7Bt5nod7\r\njtDbnYaXfAsUyP4Q5i/SsrUWa9pTV6m2nK2duH8gUJqTr7WPA7FEzu4u7JDv\r\n4xbJqEokwRmdAf+yFwUfoyhtbPRnNYrU5kjN9FwjqSUyzkOAnbiSdhNq9WfQ\r\nnzQ2G/7VKVtTVGuujpR6kDU6FOAzMGq3j+gOnUn9LBQu7vYPy3a4SeDfwHAR\r\n9v5InFe8Dddd9EZYIg89kks+EYOYUCsi1waZwP+U+oe4AAS+Jk+VnSigXj7P\r\nUNfcJCnxTXMTjPQDNz10Y7ShVpza59agIXuyVs43vTTX+0LANuwltpryoOo4\r\nXzeVwJmCB+9as+Pa6yWk2++5zAGqRl8hb5PngHR6HJ8WLb9lQdjrW2WlqCMs\r\nJwxcUJK+9K4YGrfhLakKAxYMh6Oe3YDOWuXaRxWa5daz02HcoFjZFDk5WMex\r\nOBiB3qkGNLD6Mw5ieftAbSJaYDSiBl+j2qEu5YDCe6+6xdrePhUGLjCjmGAs\r\nC0Z+7gEdVDzVS7SU+7bUsCzoOu1n7nwnHbpByhyVyXoJ0hiTAtnqP08Totpm\r\npk8Lf8zGPy+WQg3rutuXKocVLxE3JUhosEZUlx0ox1ZjjjB+ue+R5i62LMJa\r\nrMFnGBkozmLLxl9fs0+MuuzsQy0GEryagbY=\r\n=3Az4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-roving-focus": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aafc3f9df3773c60130775d2f7ffb24b1f7e08c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-OW55J7EG2Lwo1RpgggAhYdODFY7/2X3N0nB2UBub2osfNlCgz9KGBTZ6vwY+aqpM9lN36jzslU2dRaCjIWb3Sg==", "signatures": [{"sig": "MEUCIHzHZoD2PAB05faiv9mX34qV3f61cFy/ke6kiUjpF5u1AiEArtIqKzBvTfvBmTCTViFcX6RqAUYeWpYIC0HAAJ0u750=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoDtw//T4thyMY1oxz9j+4traoh7YFUZFs0d5K6IPdIyyUAXCeRtbRP\r\nx2HwA1jXosdIoGF0m7qg3Njctf7rWhAzi8aopboByEh5UWfbCS5g+Hj75GLP\r\n1J7zEnaIJwFyxrqhFvYevYKnbSNi0I32lSKVzSsWcIRqE+YFlj4sz+E4htz6\r\n2Z+9Lqvn6HJYcdhffaaKsct9D8Ud84w2yig7IBS6xF+PQtxbPtviEZhRl4W6\r\ntp8C2AUYghbvD3t0JCMKH17YVb3YZ6luVZzavqucA1JzqIwwWXXgoXjAJXHq\r\nE6vkP208ZZyZY/SgyIZekZvL8LV1mLWNYMbqKHTWONvkSkIpLV/YfMA5QQzl\r\nXmSzEsw3g5iJ8xjXjeU09Oxv6JIBU9I3MZW/5hvHW3bVJAI8i0x0PrK0CdkY\r\nZAUKlfMRaX48it609/l4LAuV/DbFp69R0eK5mNyGErn2lUHSY65fEModGiWV\r\n+0f1poeDDONnGiLoc7gqpafV54NcZk6tdAvvm/A8XRFbqP5rfnQRW3aS1OAB\r\nQkLDDfSiRU50ERJ25eRtxMai+VSITdQ8sgXIRc12wSE1aVLYdspim2arQLj3\r\nHWEJ2dMDXunE9tActVghbbaBFtuPcgrWvlEdRQr9zjVqukx2FUy1d1vIO2vU\r\nyoiM3gFgYiZTb8/O0tNbwkHchnN28TzKpvg=\r\n=ka92\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-roving-focus": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f0f110425df2dfd437ec38566dae781a12b48270", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-uZCsxGyV3wj76tqkyXclLObIxshCkQakce6WNHUk7N4vdACI1Ebz1mGoY9V7skoQ5cogqitP/c+5VEk1hMQnBg==", "signatures": [{"sig": "MEUCIQCsVw4h+baap6k9WVad9E9kWjSLPtU158JBbTsF1KogEgIgKJXXPk6RQuZCva0pGHYhak5Wd6eBzkSoeg7RD6pODIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZpw/+ONm3vIdx5wJovF6/+M+MBUiLNbobcqCj8DfS/L4UpoUxgGxl\r\nS3JV2cLjOGNLkG41YCF0NzuUt1LHQCgm2cmkJcc1yYglvXimqNd7zUA9d0CG\r\no0+Nb9Rw1u+ervQWa7G6ZT2sZOaXmorBnvrSHfN7d+hqJyWPtCr9+AMt5KGh\r\nzQF28DDqGcIcgAUfGCTYe5vxknbcfaJUDBwFWN0SFGKzpIcFMFF7pqILDwvS\r\nfgfNntODJh+ZhSHI+W9qSfIveDJvf1Ni024rfiNUxQ/4G8bawadLC7E26W04\r\nKOw7yHp2YIlseK5wxSdchdMcLXv+y85V1TqSr8rk/04XTNxDKVtmjoB3eCbS\r\nvhjP+pwrKyvbQl0wNQ2SDpDUZ3Uefw/24tokNPk5QWLr+66ZkP1uoJsIvRDb\r\nZA4neoAvOsOZwAVAEWz02kCxR8clOcbfd0q7YvUVftw75gfy4z93ur9zmird\r\nmkLAXsLF6ZiyO2NiAZYqbIrCCpZEGTjB4PV/0fN+fx7IwQz1lXltL+I8BCU8\r\nGLZaYkbHgSVZw7N04o5DSTcA2Qued0Wtgi/HVtQFftH7Onqqy0pGebc9slQC\r\n9CXNjHxqnYUYAAqzLArwNN/DVPEluLu4/oa76nIVfHrsD7kwFBcZ7qk/vdKw\r\nTaL18Z2XY9LAi6kByuNjIwSmuZP3hYbDfcw=\r\n=QEQx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-roving-focus": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "08f871a75ba331f69dc840fb98729abbf5320f94", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-kU+dF/qYe7NjCas6cXavJb0bKM2mYAVnj/kbstCrTMN6srpb7OmXxcBf18Oi8dfw4foyOEIFU12NiuNcNuEH/A==", "signatures": [{"sig": "MEUCIEhSeVBcxvNTH+kgq6pNPs/8VkqdJNF5wB8WRUuL3bDzAiEA/ODERybQutjGUL5lyubNOuuCXChBcxDrE/6CpU/ccWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwP1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3Dg/5AdHjIdcb3gYg9VBoczYxwO0kob5YVgOh3AGmlgylzDNIIJG+\r\n2oCJ5YeiwQ3PWuSCenGTnLOoSM0HAwEey/3eOmvisUWgrFtxzRPPG400w/DA\r\ncuYVM+Nmmz+Y7JuL+CmQ8Co4vbQVjL8oQjC+FuQKNi6f5FtLNdfsiunuWf/Q\r\nVPvu9SYmQH7r2LWQkQWvckNC7EmHY4rA5zIS7KT56sqrvcuHwTWux/8uJ3a8\r\nTzr8FQ4WyV6b1fjWw1/0eiFdNNctCeLmgNZMflBlKoSdx8ylNA4eAjlV6QC0\r\nkU9bOBbLF3ieNMwZreBbT7D4Cr0IJ08fzOM78VTRaek1Fq3mklpLW3ILtsVN\r\ntvlNHn2iCd3H9cWLK0Ub+Ynq251Sqn/9k0lIUc+vUvFe0q5uSQkfQauJOI17\r\ntyKFW7fm43qCeC/0/xbTgx3v5e3qlMGGqI+CnZn/804hQAa950/QPfFWd+YP\r\niQTOijiQkjoB2bV4AY2QinVr0OCkuBeeaasCJzVBAbJKblazVR73EFO5Xduc\r\nb4hgAwSQfiFKz8QZxlL9KnAPSNN8e/HogXdYGyDFyjUGbpQTI7X+pSxYDtWa\r\nIvQ4iziI1oyjvfrCnB/qSiTCfLjtDGUy67qKRpjKZ+8fbczasFS0K/vMSsn6\r\n20mgnvyXnDlmcCeu0HP+oHvx6oNXphBQH84=\r\n=0E4x\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-roving-focus": "1.0.1-rc.7", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1d0025d59311a05d4211f79f756360414b69fd51", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-EURWweK6BYili2YQenbO2qNkZZwHOmfp7db637ZRMTAEP3j78kglhP1GW0IR78WwjVRBKVMX/XWENgt8kXQqMQ==", "signatures": [{"sig": "MEUCIGeRCfiFQsZj29pdQH5VmbaL2qiWMGSxs2VzOWX8lC3HAiEAyqeQR1mkrTSICZ5XxTUK+jOvGp9fmsmtJakmSIofT5w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7Ng//Vszu5dr+6U87gUv3dU0jp1GajLGvWRPCZyo6qlvHsMzje1Pf\r\nF4+nkFCdMYL7QFBpwtQ2rAV0mg3DhHpuLRYrZ1e3hLRpzDuCPmm/GO0l3A6m\r\nB/1d4sMMIybIllkgq1cjbJoq/LjSqvPQ0SCg6iGsdvrBMcXP6J3PT491u8vF\r\n5gkEhKgVvWHqYssE7fyENSao5Rnr4mKCN9D3YjzO4NL8smdep86Ko7ejuyhw\r\nyj/Xv/Q7jqOdE67NCbYksQAUHHE9Z7+0KvCUtMiQg73q3wmx3c2vEj1UAont\r\n0Cqk17vEoMetY4vzFByM4+MQx80/ybbT9cRC4nb6KjOX31yFiqZj5NmwxUAe\r\nFmDvR2ZwqGZgAPVO7dWGYfWxxTCY1fytgpM5nsa78cVT39NDMviOC4280kO6\r\nxCyjCihPwifn2ublzg7trGfbDpVOjjq+fpSMXDiXkNOtvi0tXhWPN8sI5TGl\r\ntt6JooF0mJn1BpHTBZ/WPLsIztW3TZY+H8PW3OZ6qMZrNoVxoW/koxgVD5QX\r\ndSP0mFhrYQv/KcD75qOiOFnrQi8DXpVuSZdLQYTescumRnue2YFpCmcQ24lV\r\nxaHCU48B0HQv8QHcD7H0XrOEMmf0hsBzUMLQeCFKWlrCRYoSSf+SV5kAZM2C\r\n+FHcd1Lfd5pnnFf6sOlMk82wPSqQB5+PR6k=\r\n=VGyJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-roving-focus": "1.0.1-rc.8", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "47bee1f49701b83e984de51cd51cab25339dac89", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-N8oPq44yuAyZMMZ94LLSo8rC5Jra471tE93xklX21Jbh5CXy/FT4xkTKSnQYECn5DqSR8/jc749PrQSlYPsJ7w==", "signatures": [{"sig": "MEQCIA86B4lQTvGgXdDyvi38PHPwTeM/PeFuqca7yWaX9yBNAiBoQkzTaBt26IT0LH41R7z4YQHM+q7cNS4trjIZDf7BRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+g6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6hBAAhiA4j3uYEExgcJM4FuJkozLoJdmOW+UHrO7VWptLJLXhI2tu\r\nIa5YSt4pPdk3IbLXWTdToX1Ma/jyMUTSY0YD2asd/GSv3gj3VPC2UCgqqtPa\r\nchYb8k604W/l47DaDCU5QTfCMPd9V17ld3EaqzZTQyYd8FMhmTHfUM1ZAUUv\r\nd8+gpMMUFA6rG2UnMtyDWquOmiaPQvo+K+NvH7RuoBCHv1NAPywLT5Rt1tYa\r\nYY12znUXziaXHmSqKp1uKV5Mod8xr84XPszYgJi1jPcSO+kobW5FYl9H0IAw\r\nwv/RksHXexEbvi/RsO5RRuCg8Wlaro9L7c4uLjXlAwf2JdandomYo6ag0C4d\r\njSPvuE28+G1sLiBQQsCLcnmNjwVaSggiPfwT+aEDodafUnJ6KMS6s0pEeRWu\r\nGaU9ideu2GmpVL7/nkTWkVTxpfKic5FHX6kktyO2SvXhhiUEdaMq2tHd/zfp\r\nvvZciQIxkEZQpq1DZ/0zLYjKqY29M5WJ5ozOCZlczPZzynxtn3qptEhBNrnY\r\nfD3gEO3zerPck6h33HEZitjYTBUZVHmscbVGjRw0bT3KZQPES9oak1zCi2Js\r\nkcwEuYvDEU+GgN+rE7UAhyDxIQUtqKF8OQXC4f4PlB5NY/Gg/C5mzdbvs/Kh\r\nblsYoa8BmnZTc6oMVvVnyvtJhB0smT3Gn3A=\r\n=sPM9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-roving-focus": "1.0.1-rc.9", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "11b54eca4c9681880298a7d7203377edfd4a3922", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-cUOzrCWH/7+dYcFKZ8c+OAKsoDMZGiW+SpiN6iHhMN/nWp+LI5rvbynh6nzVJvjSU6YwQEes95ZOADW3VunV1Q==", "signatures": [{"sig": "MEQCIAz2dax5J0nKuYcGO64BZGO+/qEa3rcjuGFxiGkWM3g1AiBojIevRBWVv0f7gygjdvamG5sJ8EfseBBm4NxUm2Ri+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomeQ//ZbwpeHL3jO0NdWRu7n0TKpyb/yLviTtwFl8zEZhWH7MZD1TU\r\nNLXsk2ulFAC/Lm/Lu6oAhKA/QGHNJUfUT4YIkO8tdtNZ9ypCyBJ9+44DD/O3\r\n3CDKRXzfxnlLckX225C4KGn3ofXMFWU65DDtu1/47R664R1GN5+AErT4lQa/\r\naKa1TTPO6Ut501vv0RZIyiDuD8PdOgfVPUd70SFabWY/gZT0v4XaDBarhH49\r\nkRDgpoBF5DDTQ2L9fatX9pHi229d7YtpNuR1+sP8/MzcEPhmNT2xBmcFedCE\r\nDOM1S6k9Yjq3Q6hlizS2aRhonFStQLZM54/JV7jFseAe7S4DNE2RLGZyqcFM\r\nbS0PtToIaI5fUbe5IMkD3g4wxxqqgDUOO8R32sF3RIvypJkU5GA/rHyjesmd\r\noKY0SDSpqIG1FyVN1pE329EMEDxBnMAmmvEMGnVG9KMKXNx3uRq2ZBnEWBlp\r\nl+9W4KuPBRzgbNuxVu+rwCfuihzFOAQigipdbaw6kMBvZM1ReYD1V8+N0qyn\r\ntlmaiGb/PvKeKtt2beYafpmSFxJeogs8P6nvN6nuVdLuZr2LHB07+t1c3E/Z\r\n9mFq0xTxDKN4aRonM19JMdmFgCaz8cBUSGnp8qJ/Zhwawg3ofPwWvdTfNGHs\r\n6o6nrqKALcsFYGL9GZ3NpBJ6IVoxRuMn98k=\r\n=ZPGf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-roving-focus": "1.0.1-rc.10", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ec1aa700a2ab804a831c685c6de77a888e141f5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-eHyGFv6LIh/8HdPzr5ETDW8DykbZsdVIi1pFrh8Q4rk+Osq1CfhG1Ifn46CLyquMVylHPfW/S8nphJydjrUnfw==", "signatures": [{"sig": "MEYCIQCFqoAuf6/CormdUzQSwTjlufgfh9SbRia4K2COfUgOpgIhANkE/+CKbr45WOmp71iFd8WzEiJ3d86g8qgjhFgWZblE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqu/RAAkqFvLkdHDgFATu2EakgrGJm+ZpuFygJDfzn5Kb1u23nUPQQO\r\ntNhBHDD3TbC8ShCH12Cbm+DIPuzpSD74NWsJ+YpOMxIG3qWdi5fkMOcc27KG\r\nIciMyxvPW6Otp+b4uwwtL/h3v9Jg7mCvbl8qbvBDqNEHWnMeRSV0Ca9IDF19\r\nPyB/fTUY2x/izvnFoJUIkpRXlC1bi6BNCLpCAIpayK7sKRpKk4Zt8stFMasF\r\nwMROGdNBn9ggBxoOmivTlN9FLayHPN2i0AsvcQfSez/7x/Dn9MIdCOdx4uqE\r\n6gNQMUqys7QEXhTjUSeAx2V4M4CsipSKpL6axqCF1N5VnBoGGQzkbxGLoVA0\r\noctHGhur4Lt8hF/TCR2royR18Stsv5cIP3fC2QU79XgF4/CCznsydkSOYDwW\r\nDgWnoI5qC61AyMxG2U/peNIHpmV7wYv9tQN40ni6BIImK0uR64eh6wEVMHTR\r\nq85br75S9izOgj8NYn11asbgRkeIbg9xh2XcawosnMjUIG9b3JV5aEPjUCdC\r\notlwJh+LeCqU0rdf4t+mwGF81+qlIxoNgFlQgx7wdnQWvv6hrWjAFPE1GvUH\r\nAVBhV9piNVhGR1p3z5tSzdWObjqaBkTNb0QqDa4yoknl4zEwYf3zgcd+UU/9\r\nHtkVSyn4bPXpwEu7gz2tpMtyW+ss9SionuI=\r\n=xORT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-roving-focus": "1.0.1-rc.11", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "98154a0fb410dff6a12b61b73e45ca65a64e5a90", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-0w4jhodfKWJaa27euH2ULXoFYCDcXOqyNcvs7c5LK0dkRszaZAyMTNmguVvg5oxceexecHWwsWopHwSDCYt8sA==", "signatures": [{"sig": "MEQCID8vBvltXOpISQr5PcHTYTJhUPUBmQChGus1zNP9NpY+AiBqfYSrtbPLAPNrblL9WPZ4kvywn1SfA2r0frRKLVLDuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRx3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCbg//b2SIegnC4hL7Gym+VXCLanqVRoluIpny4i538bspVxiBiyPt\r\ni/3rbVnpUhP+rrcmD1r1Xu1osJ4rwRuS4IC9VPt+sKMEXSJg+25H9MUTL4J2\r\nzEe5IaNQ53mChELK0k5cyupz3Q7OYVKv2I9vY7UfqNRxGy8D6m9wpW23Q0EN\r\nI5mhgDI6uPn05AsqUM0g4bbXhaWXLV/6BOiVbpkjXFnQZRWUAX14oS6C2xFT\r\n0W1WFzV9+6ATSKjbNgrZVKVDD4qWxE0ez2cui1PkUl7XaS12QhFT3SszNQOe\r\n7jFfClgpV6cAcdSf4DxEOpTqEs+Aaiye8y7x/rlslRvUSWi6rIUpxWOh8Fb4\r\n07IbTy4uJtFIGzqudnQLEkSM7WkNjsNvSAO4cpuef4H0CxaXD5TEmPjaMnWk\r\nrDa488ing16PAUM4pphKfcRPB8PqiFdMAbTp5f/m16V+N/bWbHgP411L0BrV\r\nEJUnkKHWUz6vq2gtFCbMm/krrJkWAMvTNNkwiz/iwavtY63nAek4Sx1G0wPB\r\nQl5dV8QPFxNMoWSlDEllRGeA1dj50z+ROo/UcxYsDTqoVtO7BoxQTD3JGvh3\r\nD6aM1B+MrrTM1sfBCRPAQzzoqBsY6/Yidqr9vTkzLkOLfNzoZC4EvkPNcpKZ\r\nuhe2dG+xLzCJm6MnJuaZW6FKlDogirEWOX0=\r\n=ZiyO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-roving-focus": "1.0.1-rc.12", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90d622f31aea9e08dca82a174d0a86d382acbc9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-nlpX9De+VMAdus7ovKLXowPigNNzjwAw0gMvaWKY3EbxrTap8CZi/9TG+CLsmEGFn458PZUVtwoJkHfYUHdoyg==", "signatures": [{"sig": "MEUCIHe/9Iy/khrkZPM+j+MsvbeWRk8HAdcuIjqBJP4ZCUPaAiEA1vXOf3xqwmqXlAzyKz6UHDozRS/YQxNn4QxhMymRw8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqBxQ//Q9kPZzThM5HmVVNp3v5w52549u7Er3PiBJGs0NAx8KFmb51k\r\nRfCU//WVYnwPW2L33iqGjnaxAhflLK8tzfeclCMJL+MFGpPkCTJFcvV9lY14\r\npoJ8s+EZmZBkgjjvJq4mzn0X0mxkeR12nWppKhNczbKc21O+JtbzhUDwetb+\r\nyli0BjYIAVnpblAbiPNJGPcI64ewiTFxX/wp6WZK91gporXOoSss9K+kEnGj\r\n5M61vOMK2xFRpbc16ebPFNlipUQUUOiLvVuN2ws192z9oxNEfLbhKFad1oMK\r\nvi8DICZxFTOJDRt6neI8if0Wmge57SojNCFJ0ZLU/LYHnMqoBCocHy07qiY7\r\nhns415UDvua9+/eggjM/NveWJyo2xZfb773T9/gVtRBGG0J46EwGr2JgRopI\r\nVKmFkDRGvwAUI3zWDcyBLA3aZbZSvG8TRdKMt9KoisKtN5wH8AcWi+kZUEUF\r\nIU7DNsr+ZwDS0DEttRWeGLmkAwnblIJk2ek+8s0MAPCltW/4FeyDuReFwoIx\r\nrZJGs0+l5mMSluzu6DLeQV/H8/LV3hjmxF6G5fTo5wWJIcMkZlrOTOgX0RW6\r\ny9PaaqscqrgbRa/tUyfjixgVfVlvwIPzKnZ+pf+6FZ3icczDJThBuXRf8WGe\r\n2IAD4imn4zNvBEi5VvCwrt8LbLJ43AcOtxU=\r\n=oyjV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-roving-focus": "1.0.1-rc.13", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f66380b72aac83230be68d3a1fd28f0cd7be74ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-ytnuFFnh3B7nk7UO9vqjGnNp+mEyuIGNxMkJ75757+rHb45vRetWFcW91kEIRrROoeYMP6ORg/lWDGPLAk8QIA==", "signatures": [{"sig": "MEQCIHb6Dt47lI2OQOv10OjIC74hN9GkYENgyS9CjryooS3MAiBns2SzKudTX+LwZV1rDJSVOxLBh5lwAqrqdcUm2XkDnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnK+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0cw/7BDDWfVRzolAtMs6FoBOu2ABAdR/h2vUPrJKIm5kRDj243yv5\r\nk93YSE2jOI3qTmsddHotV48nGxtEJQyaOsFO/zPH0V3Ht2rvqSx7dmPL55dm\r\nh48hzynKxWJnuDXXykeF7y8yuFnAT6ahfPmNpjWn07HSO6lxSly10TfG+imk\r\nQf4i6Zo23LcoJeaNzjj5cABC9euZBKzBd1YjeMJL0EctG14deurNHsLlcR8g\r\nuzLKPBbuV4ajC9WGNXCHXZAqhkAtx6jj4VqFOnVzhUoG5Fyxhns7maNXPk2r\r\nXVNbKAgrzHxUwgdsrESaCKogBmML/lt5TN70QzWhHpdxWvefuDATBgS+WE1Y\r\n8LJiIXnHMEp1P/ug0nInhU/+VO9DYsIHDzyWCX7GJxo163MOckR+ijkY+ZzF\r\n2yW7CjGF7Ue04B1CIS1u1WQnsYBdecQ12o6R6ziq2pA6kFnnPBUQmeB/wDwz\r\nZQAm2OLwqBFJeVRvfjcFh+x+c6tGbIn4lTyKR/Qc8zSYMwCw19NuBunApZ95\r\ng8xIAYQEpotDAYNMFL5o5qLtWweGCAonqXAAUT8QUP6IcGZ0s+Y1gyOc/iMW\r\ntqJkF6bZVg5iwIEAy/PPIGZUcCRd1gij6P04BTTJF7X/2NxW/9De/fAC6dfn\r\nlKj+KiMSDtTpjxpx7B+GSJG5QlO0KPDYFY4=\r\n=O0te\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-roving-focus": "1.0.1-rc.14", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e6d9366526b0521c31a09acce1140b0b5a94c048", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-iLZFrFlJVN5GH4LH5uoGgKVRqMpR+uOGkr/xlpdVnZ4I1tioez+oILRkldoC2sTRz88GugJRKHgxmfhLzblVpw==", "signatures": [{"sig": "MEUCIQCeEW+qLCrTWSMUgAc84vMzrHFodWuDVC4uYrhjzSSHLwIgJyjOoOOAtuzoDdj9D3H+WLJ1BRuPkjM4sP2J1hBcKaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorJw/+NKCV/XpYy6ihFgNLhc60GEAOFCOe/HXZ/MiuuxR145RqQazJ\r\nyJdtHSx8tG9kbY+5/1SiYulekcLc5wb+9v6UwveHXai7rWnFiYocbrqyRE4J\r\nx52S0z83SSyU4kR1TCPC1H8EFIouQn75sMsnxxCRHtRyE+YeAW55rDVZQ0zb\r\nqg36DxjCrdoQ4tgig6qHpA7ahwXIBCwSLPZNdPIF8pxt9CDN02DMqDMrDfu8\r\n4ycINO0VicPRRSL7a4Ox/7XoohjjqJw8ulEIW1sZzR/mUxAO/dmpJxIHBtWV\r\nsTanbqVFJ4xZEP2wzsHoyneMypFUrAHGHi6nYzhZC/8p2P2k4FXL7/C0IhaH\r\ne2VqGZ/+99lBO7JvCEByMzyXtU6thBYakhlIuWl5UDR/Q+EFxbDe+hR5cnVi\r\ntuTpkqEox69koajS+LzLjpBbKU20okJVkc8Uqkc9b1RLQa7ZN1nIXr+F/siS\r\n0zqzyOG88fsbie88HccAT/QTuEUtQyPWbilJ33Pr/gG7cqnWXcunu+Ue0zUn\r\nlfzntGVJemMP4XbvIrwb0sdRVoqi+2XbUzZTnyyqLrofWGCSsB3JM1dxI2tP\r\nLZPZ+EonhZoPd5U4q5iRnzm28Xc2OSC0xOgglmDQ4xsNEqNIj7VY/6iOee7w\r\nr8rBVWFVDQYO5vThQNSVbsm/pIpXJgUuzBo=\r\n=28Gj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-roving-focus": "1.0.1-rc.15", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3b537d90f018a4e6fd60ca5b2863b11dcbf55eea", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-VjnVdKW3/6mWVBAUGPPUoebqx6DdkVIBPvxD3WanQqos1Uvlhb0j76IsT/OXe6Gkt/FSSAxNO/lflWA+6TPwQA==", "signatures": [{"sig": "MEYCIQChfAHs8YwwloqCf4Rbza6j5rXcow5birngkioDN/Cm5wIhAKAiZKazZq6TmAFp9xgoyJ2btqsuLYhmX+lM3m4cmYnt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnWg//f3lZqpgMaog5dc1aYOqcvdBrjrgnrcD7DE1zxege1YmTkZyG\r\neaIh87jW0dojJJj7TSwMXWE3+Zjf+hCSSmaqj854GgHmmXXUheLgeMiMhwu3\r\nowKd8kGOew9sMFuk0QVaDFC1VH9YrsXgSTv7Nn7DBtu7AdPgxq9dyywONL26\r\nPmJRTRGli/tz/gCYCjSARFaxBdMW1jrpTMt0AR2Ju5adK9PAKi4J760FQC1x\r\nsA2OMp2H+0RR6+VtL6rZUfs2PnoMrPG2CjRNXaAT7QtJ7xsVC7Jk+AKzRoQF\r\n3lQi31DblnJxiv03CNl+1kJbKFYC8MaNDCr5ti/trwu772dNHwF/UY45buRJ\r\nJG99KVZavY+RD9p2BNhcf323uuijqxMaK+yeARWQMHVkr3zP5W+G2YX7W1ZN\r\n6d4xjSUqH2RonB+8Gz1E6qtarQ3f+vAQu9f2QHBXrDUp1vvOFTiKiaDRCYTz\r\nQB9XEHf1VjxRAzr2F17Ns3szxP2e0ZBdUxT9LKBucTccfeWfovHVry46x1NA\r\nEIyCL1pdfRTHchViZXYCAN8TmeH4imcAEm5MNzTvepKNkI8IdrDfOWMFFyJU\r\niwHJ4UmxZNbTcirhK3siyMeCEgaT1jAIzvPmtXzeoTkAQBAq+CtwDLbWPuor\r\n4KtxAzJHmagcSj2BUiF/gAvrcP8oNJPwQ3M=\r\n=luRi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-tabs", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-roving-focus": "1.0.1-rc.16", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3690f0a72b78a3d720c98373b2a87232c415abef", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-emfiUAJra9Qg1m1hEedE2GSzZiTe+NPoUtaY2GkS5p3XSa81NsmSVxbJ1xjVOqpv9m4loOYXPlJ6dTO0P2PpCg==", "signatures": [{"sig": "MEYCIQD8Kv7vk0u725CMDJQGforNc9N8XNlEfBmN6dt6GPYengIhAJGUOueLav2yq00mTYyeWQamBDOoSWbLVXaVN/hKGO2I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGOBAAo0eGz5V7/0yx0wtp9xFj5/xsxiCqT3RPGQYcJAopFU+tvc/i\r\n3dA/8QdKgYhhYgycd4dqNQTrQp8HmF+4wS/fNJr//Qdqc63yhXB2TwpEh9ZC\r\ngc2rIgj1hQBmXn/tk/FuX85a9/UzyxH/kIK+v0ingOdaY8Oqg0xQnsfEHUQZ\r\nl19YsAbjzGg3k1tcSMdiOwzGR3n31wo7l6oEdj0t0MIHvyLZpv+Sh2AURA6B\r\nq6kCQrzo6DHHE0QHP0OptjPb0soyrl03yaVulL+1ldQnolYdD351Qpb62Oqo\r\nSH6NL5NPZvEPXk9EbiFcYJv3ovxsU3nuDQpq5Nd54ylyTs+txUS7o+nWQJsr\r\nW3Vgbr7gHNZ5CStn36o1+v2Gs7luFwDgEw9JG8x9BSfdfYWHaJsFUmKzlzrm\r\nomkt9jpCBn8H/CrquzbGS9ol6vhlD7j/iJzRGot21/GKzoyJeizCuOUfHoPo\r\nW2r/dQTvrUiGuatDuIRBmh56b9GrtawpvBZlOfHYyFcVQME5oqrIkHniGttt\r\n9FVTO4/GSaG4CRWqjTVbcgjlHC74BeSljjB+yVOd/rwahpnrgcJR4ILaFRgX\r\niJfHm5bJio66aEV+zXmco+kjz0XPCTKDdYkvfM2vLU7hEukqF9nE68eh13nP\r\ndjO778qdrN17nlnCnyOh7gPHFYbloC/iNoo=\r\n=lnSp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-tabs", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8fcfd6f70fe3026ff8a839b120f4e87f8d985e05", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-mVNEwHwgjy2G9F7b39f9VY+jF0QUZykTm0Sdv+Uz6KC4KOEIa4HLDiHU8MeEZluRtZE3aqGYDhl93O7QbJDwhg==", "signatures": [{"sig": "MEQCIA+k35Z700JcJ8lff4+gIIO8A4LQTEMTCiAvDuohwTSGAiBPf/kO6JI7CaRk2SMfrMZ3Fu305M4DRZ0ydINGED+PTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFRRAAijPfN0AaOtX9QiPBV2PVOJUaRDRQ7o8i/KZsqShmzs+vQYVK\r\nnZWlrYwS2Vj4XgWspCOgdE6w14RW4AtVLY4Jj+t6FIxMP/fAmrTS04CYtlMJ\r\nJm2Lcx+oY8Umt6U4bDAvt9X6jTmIKTQxkJKBz4Fa/nWCxmkB2PLEFz9ZOZA5\r\niTtplWIdgNP8mtwHLaQzZp0zAsK1Mk1yndehR06R1N321h6Wy63ulwgL3tz1\r\n2U6inp6kjikqLhZza3M6axEVb3DLYp8Z/c6qyTj5beOND0QOF+n8ZPVBgmWK\r\n/uXVfakjFqJurHsIJvXGdT2z749KemVsCl+7D3EtdmTfJNhUhvsxYpUt/oJD\r\nbU5hqOlr+uq1MwDZpICrjrrWb2FVPsEAklZPVEh7cXmeKa3lT0wEH+B2vsz+\r\nUfTQQOqPJNPhun3vXRCgIKD12fqzfdeoFf0brttrx7mxzYSPFkpTJR85ewDX\r\nqgqgos/XiZHa0WyQenickFxcATaxNp/5XwASMziNTSsS0VRGJDAWDxF6DXgF\r\nuE4mdzPLXdId9XfsAiy6+0c4wkfeu2pWwHZ1Pbh3VdeCyxb6d5+zH6zyiH5j\r\nSE2wkLaV5lyRbnAWausOV/9z2mdR2JCOqw9Kwjhfsg1BAcOs/kk6Xiv8Stqp\r\nRtCmJypX+dqv0rZ1CgqNgjzrgCU7lC+oIyU=\r\n=L5bJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "691089a6997e93d267a10e8d3380a0cdfbb59f06", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-wZiNtFUUn3jLF3AULA6rUqVAbRCi+GuwxmpZeB03x4VIZzGs+CGHbu1Xv6ld6sn6vp7oq/W2arQMZpqv5ZJ0yg==", "signatures": [{"sig": "MEUCIC+ZTYOfzxDUv8bO7JlgSMNp6rs7S8ifOAzHLZScviiZAiEA8pmAiyamd23SQMlzcYaPPuK+C3tm1bNO2Fu6jcjK764=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxW9KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKwA//WZa45loME+X8y/4yoxJ7D6m7EKSLWly2vXdZvuXQcK6eV0cT\r\nMLKDB88crBeFgS02FrQfjOeE87VzKNBTAunm8fAQDcC6GcjU/FDw74+0KKp3\r\nvTdcMLS6JzClf9AIclra3fAyq87QgUFf799AV99CnFRoc/gVRUfv38pzmRG0\r\n7MdZil8MWF53+6wgPYSZD3ybpNSkar6FuD1Pzgh5IYfusYABtJDp8KcK8YwK\r\n4nPsDg6BOrNHDPXt+0y32KdpH09lyRY5wF5waZNmBFRXahNfNNaBOGxB5Vmu\r\n71NxxEmC91oLx0uXTGSMqyrEI2drNo2BWEQXraHM3uq++LpzZKu3EGloR/sP\r\np4u1mk+Ic6GWwuFRrS2x7tmtdSoTxhjStLuui/IPCul/eiALc1Up+OQQS9dq\r\nZ0OHDAOl3vDMPlxnGxFcbOEK79JLoVWx7+G2NP0ta80be33lcxsWMwIq8axb\r\nz3VmcDCcCNK+Ti/b15w0ukMtcjGHlxYOd8ZW67ufnAvJv2QBPhnEZiD//E50\r\nqKcJ1OhtlEIuApp85OWXgbrL6q+1k/QAY12PqbtEEAEMmHlzGlWxqniukLH7\r\nV0di2bCkhsNNCapDh5tweb1LgCFih23JDcF5HHB6GbB530bO4mM/MsMwQ93c\r\nb+ngGAUHrNN02z8ExJa70SVurvNfAvI55sA=\r\n=ybyw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.0.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "737e172aeaa7e6cb1a77a6beebc624d08974b9cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/JXHBlhFF/I3wCnV+rjkEnUgqEni15xnwVvI+o02iyqAMXu2cBMCvG5pCAahIpJhGzIu6/hRMwflajxn0v3uXg==", "signatures": [{"sig": "MEUCIG1n/t9kxv74V7PqxynIMP06TFqV17kK+QugNQ0E/qfTAiEAuAKvPi4ISvV+Wz/wvaa7Vb2p182myAaaqiwXfABHIHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxp2rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcIxAAm1jhW0ciVVs9E6rjqy7tHoGseaDIshc0oPPT/8Y1hUP9Yvl4\r\nXDAAiGp6v9vXU+XXpgIDVqK64cqui02I2H+1dhQMeQYcwmpVPuyokGY8+u8c\r\nVDXzaHHaJ3nEVFW9wcT376k0tm/DCZOVG6iIGSpD6XA6nae5p/I14VR/LEJ9\r\n9lPJ7s0LK8QZNnLS6UQQCauwKJcZdzvcWowg/sJp1ONoiT3c+0LpknpmaM+d\r\n2wxwEjz0IADkkYB93XviLaitiLJlVU2zpjAiOzQDp1E/es0k0zE98N3Tbagb\r\nHg/ehdCUVha4ddF+Oh5SGaKwK/lJFVlt4HIwqBogISYgv1MxoP3kogv5yyEu\r\n4vvaWIOyPpSvlDgcPp8g/yo2070KsjPrqHy5ll6iWm9x7xc1NZAgVKR/+2wy\r\nxwvORGpRCUHUyxSWntPFv0gIRWRQ8E5E02yzRHWjMSiz3yzlrKQEuCE5+Kkp\r\nT2jt6FcpEHG+ZeQ80Jz8sUKo6jvM9YqdvxXam8KLzDFifs60QRsNsZOSDs0k\r\nvTGhDw3/YkLNO8FcmL7kA4JtD/s82eElvNR4IfR/Rvu2/I3taAoZFpKRu/8r\r\ns7fyrEqQfa2TA4T1QXQ4SdtrOk2uxbT/Kh6rH3Wx8FiUlWoR0e8V+o8VCUUy\r\n+rohhTEX9BkzdmOx2V8KtIiQSmagtJDQ6Ug=\r\n=9u4m\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.0.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "77623a25bb4eb3581431b2acfe2e5ff5d8eade83", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-WqYY3qLQ1Pdd/bv0XQ2wZcEtloaSKeIXLX0K2S2/nR4SzfFGRoaBGett3uVi/E/bjz8f5EAEKUleoQZuFzXeVA==", "signatures": [{"sig": "MEYCIQCkDAcoOZjzUbMdgOd1Y/ZWGQir+SpeAbADNabvSZgFAwIhALgnO8tczlSOK6yc1WawbNsGpJLGh8e45j+810+RX9aG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61843, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqFTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6zw//Zzt2nBU3zOQ8p4r0QJZ7xKktP1q59E/8gfQK1qUwaxs4vOc8\r\n7VuFKqJjJomlRzRQpA4VlMSS5DlPtgH8d9ZfCxfz0S5SwQyp1EM6gw+iqjau\r\nV3sV32FcICgO9oPuboiovJn0pakC3vBX8RZSqW0Ooj/iscaiqga4QLrPBmkx\r\nQ5WKSWfFzRxpwo6MS8e83vDQd+IUTioZG6fPK2CyaTAYz4g6oYEdNflQkblP\r\ne3mszFTmIHbd7J6qHBWke02dm/taLYegyOyTZvVvhTDTmYQN3zwEF5S0Mgk2\r\nU7cn4iu1LxNHOOztl4cZ7Okks+SKIvyBQSivIyPVfGQX6II0cgwErt9UmlkJ\r\nqMiYzDlSzocMxXdD1vp5LwvDHDriHnvLcF2648QpPfmwWTB3r+7brf2lchkh\r\nmtdwcBuJf6MFr4LB1MQoKGJEBe+aMUwJiGNvY5dbUO6dgVSkgqSKnta3cQ5P\r\nt62RGvwSyhkMb/g46Hla/nUoMP0p2j5RwKXqeo5hgS+GMYJpQDiXqGPqxUuW\r\n86iynVOL/0MCaUGVl0DfX+0CdFphxI0Abd5TfqzG+8VWWxqvZAF73pHC85tm\r\nMnYS64+LzinBmUjFIIwv/EBPOFvsJTNqoK0MpacMcd45qco9dWBzLRzxgWjI\r\nbmjjiZ/cRmHApkVYnj1pekgN4I858LHA21Q=\r\n=EJOM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-tabs", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8f5ec73ca41b151a413bdd6e00553408ff34ce07", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-gOUwh+HbjCuL0UCo8kZ+kdUEG8QtpdO4sMQduJ34ZEz0r4922g9REOBM+vIsfwtGxSug4Yb1msJMJYN2Bk8TpQ==", "signatures": [{"sig": "MEQCIAxpkpUORkazg2xdtf7D9pjgmWGXot+pJ1gEIxQN3EoJAiAfjuNQy3IrdPqTnqFr6QIHJ8WepX2m/CPtFj/vUR0qCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqVgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTAw/9HyXa8kUA7hna3yfgZhHwCiJ29ILEE4Mux5PM8eSBKAsL0A1x\r\nB4zjyxpjNIeQHxRTTB8gRDHBPwYjMXXaQLWWOSAF2l2QlwirfOIGqY0u+nv+\r\nHxPWzygov2vUgT0hXX7y9h4eegFrEPKjhMrhyka98hEjHPwNOCwN6jV/WnLq\r\nPa4V1i0p2Aqd5h8wE/bcMlwfrlY0a28+JFW6fzxfnvpHSu+WCDDGnh+HeUrZ\r\n0JH/LisGXlbTt2LJxMIpuxP4MxFfcFOod5np+TrU3C5TdXCexT9wkXAwVyus\r\nf3AHE2C4B5c2BKkoy5y6DD/zMYcSO+rI9cRsN5DEuKV5ptvVMdskO3r6VLH3\r\nvfjMQB+I6GXykWj/HAfMAjdqiJ/yvcHI6e0R1k1kY6TzbS/UP/hGQERI3SBv\r\nbx9SaHP/JLKdfQukrn3VlAnxvbBfVxLK/JO+hxEQTO5lE5tWw0m1zExIKUjX\r\nh6t8+HYH5eBCcPproa6WrWGK72oOF3dCpxDCKBlA+ASC6/BqVuvn4heFxwV4\r\nukz9ThwvkjZq4Ht16nc9PLnHI//eiQepSHuuzXjDIvYliiPSyRYBpZjj6quG\r\n74PLTgjiRnJeUN8Hc4GzeX67yaLqN04G4+Rw66JaaZYMhOJo5XquomVYRrF8\r\nQH+3MhhIJB/7Skq6Dl6xkCaK19k5MAR3BVQ=\r\n=9HzV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-roving-focus": "1.0.3-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3852f5acff8157aca2776a551818022960458269", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-XCbaXPZUrbbZE9i9x4hUa/4fGqTkeYwIBgzeBAoqQwjeo2yXePlFOItqjrOgtIB4QS8RFCRkiCY9qvUwND6E5Q==", "signatures": [{"sig": "MEUCIQCVY97YcZK4tNp7P1OUoB7on6whetrqYSaKgK6iL8oFUAIgbcHgFyuj4/fE1HQDiifNQbG62KEQsTXaB3gdkjBwuuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzf8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoqsg//UjnO3Jy4EnQCJT/hpKjB7RRYiklvj9KyJE4ZaXB94tpUx0DF\r\nMQnuz/xyQ68u378BFtR8M/CIDWukcPYnyn78WStxmLdv8BF59cet7B1O6bEv\r\n84c0MzvBGUt3vAG5TYYcPlHimU60WUk+2vFD1vC1xoepvtxWSL+qbnNEBfKv\r\nb+MK2IxIPnahZJAEn2MU+wns5aB1g4ZyAYaNNeqlnSPkl5Z4KQRBKZexg64+\r\nWA0UihxzpsU1cpqR6nGhBg8BclbzqzZ4QwlkZar6OaP5Rh0nb/FLKPPS9Mme\r\nAn9G9fc7uckUBGG99HZvc2QkvOc8g/Lhp5mjQMZQHqpteMaghIE9hRs2gmwd\r\nu0pYyGXVIhKboSHqGRm9kEUcJBISTk3yYTGs5r+9jRnmvPEDG/oKuQRCr8E9\r\nLGlJB+AqW0vdUNeDkjNGSOkE25rpQWoZ2x5Fxph/IDWWWjoSf8zokmH3qbkQ\r\nwOH/oDWDmMh+s50oPTq2nRTjvk1xE/0y9u4DS7DYugfV+OqqhIdD22DxoapV\r\no3ubNC+Ob2PjQL6+nUuarumTyvUIJRx00EP7Yc2yYFqhkBdiDcxrUlCf6h28\r\nR9G05X+LxI0le5xOKFnlOaQBMRx09/HND17yLAv1Uxj6pwFEOxkjgoNgKbNi\r\nZuEGtBEEMx0141wdYrrwOS+TqLxiEuF9awM=\r\n=x1Di\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@radix-ui/react-tabs", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b4158160a7c6633c893c74641e929d2708e709a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-4CkF/Rx1GcrusI/JZ1Rvyx4okGUs6wEenWA0RG/N+CwkRhTy7t54y7BLsWUXrAz/GRbBfHQg/Odfs/RoW0CiRA==", "signatures": [{"sig": "MEQCIDbpqSclakEPqFCP8NmkPthY9yquwokQyANrQnPiiZpxAiAIb23/MAOkHpYHLpV0BvvIvL+pw5vujMlSZ6Wc085+Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6sg//W+VOHEC1mCZSbmNVlz0qt22vCtAv0pPPVJePrchSKsbrY+pZ\r\nIvMcjDfUOmvVUFPYzRCM935gnvjxWOTl2p2JqXbLIrI8qRjYeTgdDDsuFMT1\r\nqHjusDfU3WHD1BVBvOoNlXvfRXaPSzi7ue4WMcVYi7yujWukQIjPXvuTVGFE\r\npapD/dlBFheOtiFoGrlYbD4KLZOZ5QBQ9Ks7qWY/cBGML3f2kI8y0kaPcuDA\r\nodASuP9RtNYr4OaCvK95FldEGHdvnbpQWvFRqFmyo0bSWylamYaxp28kPPiU\r\nprnpQ9rsMm8ku2+bnOiSLgA2YUUpDoOH3Yh6Y0QddIMw9maspNilmAGWlTkK\r\nwMC+mkiZqw+b9nmbYyu6RNEgo2Qu3LaMDzPgVTj6za5FgnSszHIoht/ZnTMZ\r\nyUHVUkWskS0EK8TOy0KtxSWBFZGYP3P32SNBEP9vIrm2h6Bf3lT54QyG1/XW\r\nMBafkfsYGLBXS6euyk8XwGTqu+G1lSm6oOAXQCF9gIumh7U/6xEoT+/dvBan\r\nnx6Re85+Y4iLB+24kvQuSoCJDzXxYUv+zoRMyiKZ98ydU3RPXLwoU6nMHCvF\r\n6RVWcO4K1lgGqFLI2za97zFMMMmWZFxSO0gcIhTPmdx6vU3qq6L33ZMuDm/i\r\n51ONlyQkTGipD25jddEMnVuFvMpCJrQ68BM=\r\n=4SyO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-roving-focus": "1.0.4-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c13739b0b2251a839a2e7b871157cde0d12aaab8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-p/okvA1ruKhCpFPPlF87NQSWs88S6MB/Rb1SHMJUhrMqfFSxjHHOTJMWd09ln9weDbPMcB4Z/f+2sErsD+E3dw==", "signatures": [{"sig": "MEUCIQC8fFT2AyStWvg8UqSf7H9c4y/OJGnrHptBEAJC3a43tAIgQH4LdGEl1zHlqDRDfzUf0ytBEUOM0y3oAAmFZZp/xhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMsg/7BGUTJtVVO9M7S2rjv5R5NN4vfhmZsuLZ0kWkjGO+kEqiwYWM\r\n0PFT77L8fHGg1gxDtUr/ZX+Nul2m4zpBgZk+cWddGLRiCKVimVrIVO5w9/Wi\r\nNeGNJXa7jdm1197QrfhIn+SG4QtcQmfsZK8UJu3CroSOZifozYK95mFiRHtF\r\nObM9gtX5qd1Tke71hP8d0yy9n9AdJCt4tC7aGvFJltAi1wrzPINyxW877hXe\r\nOC22ZokPuj+x0z2i+o2pOLGmMTyedh5uzsOz70DZWdZOqgWeH7+nSPQ+SaPx\r\nH3St0hLQr1uOOMwxKBfuFps/XxuKnQqOQbekHbegyS3vNHu+WzgWhRVDg0LM\r\nMN5S4gNNpaXmf5mgf8s1c7X9fAYe8SzHeYdgrE4LvnMpWxtcsEi0PsgKUPnR\r\nsoWG6aNPWtArhbhSn/D6BFlwLa5tviKDol6wf3zxY4spsmFQWJU5RALGFihv\r\nzE8+yxfz8vWnq/iGOxo0hAFK+DtuLccYGj+hVGQEbqHb82NsKK6pvz9aR72m\r\nxS7SQ5jVbn+2Fo9OXgC+U+LjfpX4e5vODbUDDCSLu6Fs9fMSBgGODuxZVQyn\r\nRsgDLQ0fpyZ3ohRN5022huZc3UN9b3Ifp91czKzMMF8/QwLymCKJMsa/bRHv\r\nFaRTQt4ecD+f0WwSdemSoori4J0lfoFtkfo=\r\n=O1o/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-roving-focus": "1.0.4-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bbce02fdd3e6cac31e0c9c90c87baba2dc4708fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-LX9b2yMUGSNjyzjovEQ+8tbQynoLt4tATbKkE+TsrUPTSWaF4sOre4hAB9gYjhe8U8aluYCXQ19opjNUvwv+dw==", "signatures": [{"sig": "MEQCIHC6Dgt3QUN5HoIxmmruTFvhXWHDiv2ldGeX+FA/5HalAiAFxGlFQxtRyiJlvVzXG/lMNNt3Rtymv4B17G2y9dv0xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848}}, "1.0.4-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-roving-focus": "1.0.4-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e398a40629527bdf50734b8c2fe5d654a72f351c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-spXy+kngJcFHE3VWQP/ZGRCPM8jyTcn4tdkxLctb/MPo1NBfCL/Z8Jqd/GcgFg3hIx6BqkRXeNJNpxHc0U54cw==", "signatures": [{"sig": "MEUCIF2xdlTMpXKP9b72iAl0awnmMvj+AYgJwOW9ssedYvs0AiEAuuXRBC47x5lQ7LWfCZ4ZEfXg1GEtYMav0JH/pIFdht4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848}}, "1.0.4-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-roving-focus": "1.0.4-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ffb580880064de5c54ef61df825663702d38e290", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-BgDcrD+kpV15mfqZT4qfA4x4VjjQJQIjkIhv4k7fLSLErMTREY6Ow4QsIq5cAwVbAnoCor8Rb2wnyFYCwQGWKw==", "signatures": [{"sig": "MEUCIGZZ+vhpor1jreijiUYi1VW2+E0k5zgWBv3oXtwGNYv+AiEAjTI+Z4917AFsBa5uGgJFEp9cxuvuY+dPWnEgr9pjaaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848}}, "1.0.4-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-roving-focus": "1.0.4-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9733ccf24654b605a172a7128d084ec1f6751035", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Y5LRxLblcy4106cPrTK+AAcgj5bIkTj+KWym7OcFXgwb6wcrkLZKzJqxe86dxL3T4hMIwHClRAk6qoKtScq6AA==", "signatures": [{"sig": "MEYCIQDHzywyvBe57pKYV4EWjFFLrtxyz4i5SAnnNeaCDmgvHQIhAIZ5r90C9KhgEXv3MKSyTueNlSbLsUQPI1vx7mgfZTWn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61848}}, "1.0.4-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-presence": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-roving-focus": "1.0.4-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0cc13ffbb85fb93bc9c8ae8f127e616dc5711c8f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-DfC9pXCiiTIlJn86iSoqPXsKoFRaxJZ8JzzwTvdzzWQ2DY9tTuZMhU33+1U5xcS2MaQj7INzo3pSrSoBgom6iA==", "signatures": [{"sig": "MEUCIAEcN4Q4FqzdswVrtJ347OQzyWte6zJSs9YTMPVYkP/jAiEA0hjOY+VuZtS+YcrWnOwpYllvZnfVBQ2EZYIPs3mkHQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64837}}, "1.0.4-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-presence": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-roving-focus": "1.0.4-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b7182c41a93c26c22fe642a0689367049f98e3c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-UyvQK8oRGewgzISbLfhKndnmprVTs6x+Mp1pHX707oJ7zjDB0L3bQFhA7MkRGtR2YKFTeBasnWz/SHL1B0nqYA==", "signatures": [{"sig": "MEYCIQCR1wnZ7I66NZxs+yvHzYRodALvm6BSUrWi0UslCMF7ggIhAKaldRoy6oVM2xL8IiYSIyuCR84dEG22HrwDt+sFQe3J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64837}}, "1.0.4-rc.8": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-presence": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-roving-focus": "1.0.4-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a716d230342658d7948ded576c3884d9ff75137", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-TK5R90SbIMAZfX4nn0XyTPkReesAbASubjVWzHZDMOma+OIdJ7kKnXCZmo5D1ByB622KoG8J3PftHmTnCxFaKA==", "signatures": [{"sig": "MEQCIH2oGA2nJR74uK0/QL2NWJuKr4rvJHx0RpwR8AJxwBCVAiAInTtp+uBderHR3Om65Wqmp1MrYjwRz7R4v0Jgz8ZgTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.9": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-presence": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-roving-focus": "1.0.4-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c51c09a10d235a6856b29d017ff05198dd3c40ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-nWftqaHZDpAs9GP91VAZsLhUyhRdtPjNyMMvpdHNxHWv0a/n3WAdvQY5A0NRFQt9GC7oDnoriWYYrneJ/mHZFQ==", "signatures": [{"sig": "MEUCIQC++2soILfdSyiZYpCVlGnS1qcBbwFLAy3cX7KKldEanAIgJLTlU2iC+wqvo4K9moTr7bazueG/qopSYQmDipt6HHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65031}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.10": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-presence": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-roving-focus": "1.0.4-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1ce18514bb45affbcb06de62cb20b5d2defd5923", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.10.tgz", "fileCount": 9, "integrity": "sha512-A2AvIp6RSACpgymCFr56KYCJwitrU2NDnPko7Zi0a55mLABIKLaTW0BLnEMcN2EJHiGUyIth9YY2Pl3vyq8MGw==", "signatures": [{"sig": "MEUCIFuUMwIttHiiX+YkS29zr3h4zOS5hRux2WE06MVAlsE5AiEAxR9qF1/B9MPs/6HZfhN9cH5Kn9w6PNNSw522rj0MC1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65034}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.11": {"name": "@radix-ui/react-tabs", "version": "1.0.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-presence": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-roving-focus": "1.0.4-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "703d510db7d4475fbf4729631dd9ed784c2a130d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4-rc.11.tgz", "fileCount": 9, "integrity": "sha512-I0mBKzTyoNzYfo2PANogzh8lt7lERS/KDJj3db2YHdVuyxepK7UYzbsjh9X0W0nnVnZe1oHkv7Ni3iefIosErQ==", "signatures": [{"sig": "MEUCIQCsRtds4UtJnIRmTZFeQY40ssPNYJn426ajwBo26mtI8gIgYvxFKJWYPf6bJg0kXiBe6EL0zsT8Rp3DEf1cF9mTcsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65034}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-tabs", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "993608eec55a5d1deddd446fa9978d2bc1053da2", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-egZfYY/+wRNCflXNHx+dePvnz9FbmssDTJBtgRfDY7e8SE5oIo3Py2eCB1ckAbh1Q7cQ/6yJZThJ++sgbxibog==", "signatures": [{"sig": "MEUCIQDnRH+fTf9UWrMXrYPqJZUkjG1AnV/gGitfe9/8gNAGpwIgWyH2entixnTPKABQeZLGSBj87Txe+eK+9WVWPd8YAQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64958}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "885c1517a7c2108048f34ec00d0d4934924af3f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.1.tgz", "fileCount": 9, "integrity": "sha512-Fqm4Iu/zNAPCnCzX9QHLOc/OIf40xcw17T7jzKJs4wgvddeAnE6TMt43Mxb5GKNP/DVnWGbp68olgDoFAhvFFA==", "signatures": [{"sig": "MEYCIQCGfAWCnYv1HuYI3EEGf88FHvjC37hvquI328/ErF1gNAIhAMGjxbbQhkFnAAdD3V7POIsouftsY9VLofT4m1nWmgj+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3230077388d297618857247aa6421871ffae2d82", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.2.tgz", "fileCount": 9, "integrity": "sha512-FMxuj+XD5iKCrrLOo1H4eoFTiDIWhJdfAyVwv4jTPp7+LIh+eVJzPQeCSIQh4m/sZADCJQvbE7bsBPqUqk+VCw==", "signatures": [{"sig": "MEYCIQDtxDRrAUrWLmxx4S/2vCRP2mXFTW+clQv7b0/d19w3AwIhAKd13ZtW+mNMnl28kbq9cNlNLC+rtttzSWVFnyjQ+zoW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3c54a58d1380511f33df7c67ee172ac96f0d288", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.3.tgz", "fileCount": 9, "integrity": "sha512-YYTAQAJZZ9/TiBtC9Xf8rd0vvAasz1ABJehra/Dv4IuAioGcmQgOdVOZlk6HPJfjAf/0tygu1wUX2KOXtpngWg==", "signatures": [{"sig": "MEYCIQDI/+11k/1mR0l4r4msNJIo/EbD4n1u/xl36KAWHE7hJgIhALT8v+QJnVz/FqwjkceIlCmeUx90MjjfRTz3Rm8um5D+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0f723c7ca9dca10f05ea1a4bc4ccf2fc06fbced6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.4.tgz", "fileCount": 9, "integrity": "sha512-5FAku4gijINwSCVWygJnaDY4WFMRDFl6KBKxti/F3mY96JU7vaT9ZW0xpwfIyzEfGqyj/SkPLGq1HYt53dWDIQ==", "signatures": [{"sig": "MEUCIQCf1Q4qQFqL/7LzvM4WgI0UMMmspQmV4gAOtgl0b9DhAQIgWoSzTx6twz0hXdjDmWv9D+VdIGlFhWndu6IXr5fWuDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9f5418a0afaf88acc74663643024629718c7c67d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.5.tgz", "fileCount": 9, "integrity": "sha512-/UWiV2MrLOo3lLzYT0GbqBq5q57iMwKL6wyScsyDls/UWW8Bkk+ep+YgzIlBscPNZcXWG3P6QJzFOkO8fOd0rA==", "signatures": [{"sig": "MEUCIHaAt5eihTr7oWbNLomiRNdArgHhN9Mug/8m8ecD+oS0AiEAvhuo6y7+5lxs/MkyzaqKilzW5Kf+bwWbFVpqh5jEGpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "974bf5b06fb91bb73e4d72bc5ae691bb84f0e48a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.6.tgz", "fileCount": 9, "integrity": "sha512-CvNpIMK1YB9cAC+izU3MIS9rRJ2zUNeW2GEBUU1Cuc2JdnpOWnjL2Meu2IGqV7brpd6jhOH6DbMHPlZyKxzvCA==", "signatures": [{"sig": "MEYCIQCop7LU3wwA3VT81G9xIVPpwCMXntiJ9xh3CxDqHWbKKwIhAJxXRAYepLZU60Iq79eTAyJrF3LzhVEP7JXgLM14AyOy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ef6589b8dd707c6cf335131a413cfc667bd9683", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.7.tgz", "fileCount": 9, "integrity": "sha512-wAhGhfa7IgB8E4suehgJEHiTuLBcg5n7ABxo8C3RlfwLH6h0f8AuiGDcoHsjeqC5iAO75hKCIcB2dp6BigiLwA==", "signatures": [{"sig": "MEYCIQCG5XdT7HOq1XLa50cNxl7WbwimFCqDYRC1dm/n+9rbPQIhAMTYNQjW/OKIEMSbUmjdvbn6Rm6EhLqs4JrLHwttjTfK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.8": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e7481cfa9c1fcc4de260e1ac6092f878d6c96c81", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.8.tgz", "fileCount": 9, "integrity": "sha512-5Mqo0vePCpTSEQfc6pklu7gFt2tZrTh+f3dwZ+q1GuP1vvchP4FMnRIP1E8XPBAqd4bAttgcSrdSlhjRHaur6w==", "signatures": [{"sig": "MEUCIQCRRMQJ+bEHo69LyCIz6MpYYuQvqN9v9tWQe+cJ9407gwIgYiuCONBvbVcfpXK2f0ENrYqTS4NfdJ/FUBJyYREaIb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.9": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cfdc10ee2baa55cb2d40d0a6d04804fdb6f83d9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.9.tgz", "fileCount": 9, "integrity": "sha512-abcwTJc3CCpkoHh4YbucJsTzIh2s3RleAJXIJ2eeKelT35PiBY5FvKHfZsxLr7pB6EjVjv3zKP0kz2AzPWjUaw==", "signatures": [{"sig": "MEUCIQCxH69SRuSwTGfz6aPP44MKLK+Np4sY1oJK0hTz6vc4FQIgFrcaa6hgSmFJIcniDGpHO8zLt0Xt4aUgDVBePmb0tK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.10": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f0c422b46efdc7e7cf0d30782ab7c35e51d3bb7e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.10.tgz", "fileCount": 9, "integrity": "sha512-F2L/8pWyMUs3XO7ab0Q0H7z35acUdY1w6uSF9ELEXGPMcySImer+bq6k0r4i+BqkP3fJlDuqC+f4Xwy/x3zRtQ==", "signatures": [{"sig": "MEQCIENjKZ6fs0YPJQiCl6DaJD1rC0STgk9ohap9yjx4+bupAiB7919VK2levm4J2cltV564X22ziocwxBGmfGXQq3mGAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64998}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.11": {"name": "@radix-ui/react-tabs", "version": "1.0.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-roving-focus": "1.0.5-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbefe7371ab2d8638e9954065650fdd5939e80b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.0.5-rc.11.tgz", "fileCount": 9, "integrity": "sha512-SU4gfZHIFaNMY/RPVXfgzsB8884wiqK/1uOO5JZMXLbkuuHqW86XDHQwiKRDqxwHbWiJsW2c/ustW3VYSCPkzQ==", "signatures": [{"sig": "MEUCIQC0CgsA21hpkweWN8yVY3Fb8NCzmA+KO0M8sM2iF67vIAIgCwQkG+3zvNL5cDdoRCOtvzF7E1Fe0Z5d4f1xs7fpNVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64998}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-presence": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-roving-focus": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ba1b885858559ca791f2eacf9572da4f63424aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-0IWhCjQ7WZ4SLJBd36Ya/Tq1u9raKgkACiOWVJ+EMKv8ZLjxlsbvDZbIN5tb1y+OD2dVGrfLnsg37wuVT0ukUQ==", "signatures": [{"sig": "MEUCIDF00oPH0KtUyI/J6NmRY59+/UgssJl11wDlfPZy7VwCAiEAsb4EfGNbubZBugY+Ov3r9PCTNHX/fn4HPNPcsgx6+rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50890}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-presence": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-roving-focus": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7d65dd91b0620d160eca9f77e086ce4cf4ef9dc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-cH2cWsSNFLWtcrjv8Mp1mfylJOErGAZsg4ikBKUSWG4AOEKSu3W6DMuK//t94gtm8UdIuBPuwdgVgOPPo1KP2g==", "signatures": [{"sig": "MEUCIBdwmSrCZf2ydaothoknznolftZd9T8cLSeBwFk/WO4HAiEAx0t+IDKVbUefTr6komR7AID9XfxVX3g/Kx+KIiKpmhE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50922}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-presence": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-roving-focus": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "65fc40ee2cafcf87cd9f8b34bfd55d61c46ba85e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-kZ+TGReWyReVAgBepMIsqG3vIHxmWMYcHUuhPmHXj1uJTHXEri22zzIgIy5mNY/WTSh2+nDKBWjguLp8AhJMLw==", "signatures": [{"sig": "MEUCIEWxLwcdcLBxBlgj7giRN6wjtdBUb8M/xQafvM4YeH1aAiEAraMi2KzxWo9v06MXF4GM+8LlMZC5p7gOLzhsPFsmEXo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51028}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-presence": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-roving-focus": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "774571bb1376412317c1a2df80c033c4b8a5a6cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-A9C3SgFvrzk73lsRsxwlZIfmHVU9ftNiX9P/WWWJZeP/Wq78jO17nfMze2UxNtWrniRU0MlrUXzM6nUdZ9B7yQ==", "signatures": [{"sig": "MEUCIB3n/FALn9RTTZ81AwchFwtpRbfMlMmZS7WVRbsX2AysAiEA18j8KZ/C4YS285WyCFoI/C4wO4V5AScsEczosFWtTRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-presence": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-roving-focus": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f8a253ca2e6ebd177e3af4a7f3d91a6f7e30a108", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-eY0lRISOb96BWjGDmk4xXSkHExVzyeTfzvF7iN5LaKpzmuQRSkzksRym1b+GpovfmDrAUQ2DlWUdVW+FQfNTvw==", "signatures": [{"sig": "MEQCIHPk6RIvC65F70tr46kQthZomikW24P5diRg/WQQ3zYDAiApVbRlrvvUui7Utwcv2p3J+ur5L6voZ41UcYuwqu1Lhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-presence": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-roving-focus": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8554fd32274983a4b4b18cd5d24724156d968998", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-qRpy6Lf8n+R4vH6HFF+oWKGyrLA3ws8Kuh/LSD5/jqp8jbHC4eJcCxzHC7nY71AMIl37l2+8qcWn/AfYhbnmDg==", "signatures": [{"sig": "MEUCIQCUBHwZRChyLPrYFLyReFbZacC8HH3219p2MgdCYYCeZwIgVwguABPxZPL8Y+gcb+/QvGXx6trMOLq6mv/yOZuGrIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-presence": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-roving-focus": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8df8bb97a5cb5c3141b0928bf2de0bc4efdf2967", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Ekg/hhYe1COAAe0aNBPPfilTuMWmmdThZOnqLx/bDnOYquAcwzkNBPTf3iw3ExxrF3RkwNuo4eG/kFPEgUHcPA==", "signatures": [{"sig": "MEYCIQC4qDr3DEUY4RJc1tp2kzg55jtVc2LolmCt2lWjaUOEMgIhAOwY07ujMPPY4ev5up2djnVhfiFUrn8xFDePoSgX1xoW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50774}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-tabs", "version": "1.1.0", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a6db1caed56776a1176aae68532060e301cc1c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-bZgOKB/LtZIij75FSuPzyEti/XBhJH52ExgtdVqjCIh+Nx/FW+LhnbXtbCzIi34ccyMsyOja8T0thCzoHFXNKA==", "signatures": [{"sig": "MEYCIQCAQhuw731wMj2Mx3/nnb/nALuSe/3YlaWpeppPs1Wy4AIhAP8DNeNRRVWgLhD5ga/zk1r39XLMwnVSiaIXZUUoVAmw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50701}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53b7e09809965779680bdb0565456dba1055fa01", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-VLBvZ4/EUurSeOwU/m7PdmHhJZ8nRY7QVYSrkoDLqylxhEvLCJIyWUBSMRqLz5z1oJiwyn440SmXkO+WlSt2kQ==", "signatures": [{"sig": "MEQCIEnwoOeMrPqbDQJGZtBzlQ66YBOUarLPQnCL2JxfEf8XAiAJM6o1uwoOTY+01VasySDnPg/JrH/tYa6zGZcYUZM+dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4ba77cbf582bcffbbc2e1e42581ffed442efd796", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OVY9jLzVjVThh+wi/D6d1Ahi8p8b3PFCyZS1N2eDUZsQL38HMIvS2YTY2fc5IesOUHJaqohPKh3h1Duvxd+OLA==", "signatures": [{"sig": "MEUCIQC6T1akvld8vMI0ureo0CDrjR9ZVoyCD5GBJjOkvPDkvwIgavBTRa0HZClyI+MKEoJ27OVgkEc8iSHOK5K7Z4okQoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1d4689b2ad30d59d305543548c5ae8d49cfdefaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-0S0v57/AyPCV3RlMejTNC45oHF5cnvurlQrjpnpnLQQ7IZEwtsxSw240bo1eeAoqc2Z6eVe947NhhL2Fm9wV8g==", "signatures": [{"sig": "MEQCIEQo/hNpAxN2tVPpk8An927qMlXhmgAs+/2TMzGA/ET3AiB1xxZVCu17kNMRZ+ZfDpD6cV+Jk5TgQgWHSapDrmxEvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8d28378d0a6867e45f45ba57ce222864deb27294", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-S+HjtI3hDhvvcM3vK37onYDt1s5O95Bgv0KYOUA8NuH5oM/cYRUxjQdvp2ykG9LgFAw1TjzKib+jzeyPHNAHdw==", "signatures": [{"sig": "MEUCIQCwsJjv94fb1LARLZdB/upV8w55I4KTNJLd73SQkQukGgIgMitPIXQg4W2wH3h/PUxj4txiudN/cb26oybfbRtwUsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1c65dbbebd6dc6d02927e8c0c2ffcd798e77f0ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-bdx/u81YIfjHi1iPiIUlYgnluhYq6V6WbOBJd5HQ0yNg/1UzfLhOxYqV78gxUOK9N3b1uc1s5XbZS7tuv5IXLg==", "signatures": [{"sig": "MEUCIBiHUxe/lWKd97sup1g9I5+E+/JMdJekZyIbCACph5p7AiEAsbHIoOFrVUq9OF2QrRUuYNl+SsP4V4iV51l5ydaeQW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "105a8197c3b946726c5442c1ce5b24f824bd1deb", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Ubd9dLBHGIfG4n5hH/HM9AC4oDAtxJbH3eFw8ZGsHCXM/mMrLPlQn3ZSVfrrsil0ayknczw9j0ujs3KxbYaElQ==", "signatures": [{"sig": "MEUCIQDqa0MV0pPxj/1yrpqGbY8n/lD5Te2FTUWnWbQbxONh3gIgEJj/445KHvGosX+hXzu1cfu0TejTjoZUhymLRMvPQvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d35ffabb0060762280f869feb3bfca543eb4765", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-zXHwap3hl98toFzCMCaQB6bl++SmJsbta2Qc/BexPKQslZQ/8mR/tFs7oawnuiWMsH/GvZaVM08p/Dqlx+bR2A==", "signatures": [{"sig": "MEUCIQC7FR0y+Yzi2j7j+xzkW+OuL44dAsFhGRy8x09YmnKIrQIgKWLZhRXBMLdjK75A6+mXLPChjYBdU+MMCCY1+oxQL9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.8", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "028be87c5cf094bd37d981aa7a0b0ffad5532d0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-qcusdoCxohSSXSOEwuqR4sndVLHIFvfDMSBmpdCdhLzfG5TaHKT+rWTT5tkocP0b5y6HqQBrBmRtpP/CPFS++A==", "signatures": [{"sig": "MEYCIQDA0izBkiwcupgY2UDtkoLKWNZOwOM8Iwyc8Xh1NF2NmwIhAL+L5uLWtdNeESW9G7x1OU7On1MfIretvusPJSYNe5WJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50739}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-presence": "1.1.1-rc.9", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4850f052c6a96dbcfdd8cc9f017ec3952a0b4490", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-xFw0YF+QIEb+RLKS5FZTezClQj38U4n1P5tdxtkjXWUXfVwA+nZ02HeIActfEj0IVtS//GfC5Oq5c5oAIGOjHQ==", "signatures": [{"sig": "MEYCIQCHySSf7HKx5pReE2+2JRF44AKuyglh/fhz6nkhTQvMvAIhAIKnHUOntMibZ56wduaJczlZFrsXOlv4aoEIp/ulD07+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50744}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.10": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-presence": "1.1.1-rc.10", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "89645622be6be179151fa1fe0045170dabead90d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-uFbKffjngozMfyRdA1rqGzp5R7te4S8pOpQguc/hnDfPmQoUsp/ys9aobzzXtmP37nW9tmqQotsWc2U10whhjg==", "signatures": [{"sig": "MEQCIEkOQ3lSg1ohyob309RQve//M5ihL+yKUMuNRzf5r0SgAiAH5FgKGxAeJ3y/+q27HQn0gGcB7zEPWm1JSyEDtc/0yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.11": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.11", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-presence": "1.1.1-rc.11", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "879143f0cd385c4e092a73c53556874490340c17", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-KAjtI9jrDjnzEyUOvy8wa2K7kANOUc47rHQLTcpUK/BCwcW1r9yNzvRhIyWjLhpNThUvf4uHoM68HMXE4FLWxA==", "signatures": [{"sig": "MEYCIQDyzyqKmMDWBkhDeeZgofVkWnWbVsD/6Sn7+CCVBtaNjwIhAJe23BEys1E3SwZ4otMJ8gt6F+d3vQ7ZQI/98V3mLDgY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.12": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.12", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-presence": "1.1.1-rc.12", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c03eb0503aa17f3f75c5c6d59ccae6198b4c8b77", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-8tp0GsUL8m0X+yiEYfuHuxTnsIlC05fnBB5NhrcGVJDI/xvG4q6NhCLMH0Nl0RKw8XOqu+nalH1pAFZLPjHNTQ==", "signatures": [{"sig": "MEUCIQC1UEYtcdn+Po1pwn4+9MUK1TNL4mQndw0tQQ9UnBaeBAIgbv6xg9Lsb3YktENKg6bhVqvpk9vHSc90MuTFLt/QB9Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.13": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.13", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-presence": "1.1.1-rc.13", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0fc4694f903669539aa5e4ddd401937099bceb96", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-1SPd9GTUkGAWKF+e+11u5Wwg/TJMJt0w7Y5w5nKvze0XqQ6GYC1Cm4tBMepfG3+r1kIYO+azoc8wFeHAe9q7rw==", "signatures": [{"sig": "MEQCIDcmiA8nHvA3QMKBETdaZOb1uWLmvQe28jfRkBc0qN1FAiAieAk/IVu7+Qnvmc0pagDjwR8U/w5XZf2+NZETKn4fWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.14": {"name": "@radix-ui/react-tabs", "version": "1.1.1-rc.14", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-presence": "1.1.1-rc.14", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c3c795802ba41843b54d309ab233279ba8152471", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-a8roDajp5D4Eh5Ka+GTGVqOgUioLYOgzr3vsh5l2BXFpxDD3tKIbFjYSrTxGO9ofyPlCLYI22qVPJNPsUZH76w==", "signatures": [{"sig": "MEUCIBh8y5To/nPo5VePxgUvNou8gNPclDYwsJGCBFzZIt1QAiEAvLDn5DbilTO4+0TA+3Gj46Dn87r/E9y1YeHVPFJJYkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50746}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-tabs", "version": "1.1.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "698bd97923f6bcd629738198a73beebcc4c88b30", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-3GBUDmP2DvzmtYLMsHmpA1GtR46ZDZ+OreXM/N+kkQJOPIgytFWWTfDQmBQKBvaFS0Vno0FktdbVzN28KGrMdw==", "signatures": [{"sig": "MEYCIQCtzDVKB7s8PGwzFY/UTyqgYhLyO1l/3fZC4BSHRZO1LAIhAOriHE95c1a+klAHeQDNPDj3e9ukeU9y/rqIb8A+elek", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50701}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-roving-focus": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "312e331e96fb3fec97c2b60afa85b91084826edc", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-seei+H/D1TNFsppI53edMYhN23Q/3SKOFRYYHIl+WHy8xS9VY5ozDaQKFfxSpBc2btp8njpBPkPqzRwaJemP4A==", "signatures": [{"sig": "MEUCIQCgilTidEpgYlwY+anPAfT6yXe2UGdYnNlQa+6ANsfM2AIgU2CcStUcSUA46xupdmSYij2UVCp4w7rKRoLxB5spLEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50484}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-roving-focus": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1cda7a4499eab1a868b9bd0ea843543e5eb809c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-b4DR2D4i0/F47eU4vCZX9bp0Ezg90LYH538F3cACXaSDP56FOlY+uQxO7GmElw0Lbgz4tNi2kekBPDnAg7M1RA==", "signatures": [{"sig": "MEQCIAvrLdFTuGTMNj3gADyq5c6jH3SoXIXaAdCYhmu1ZqcsAiAM18ZeXuacLhzgfjkThmNDAGaxF4sMMTD5+devPsQOKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50484}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-roving-focus": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c83ffadbeffd5f66daa22441618c4977c99e14e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-uXE094jK83rcNr1kaKnNNij6qMaZpDOPIg/H3SAqRarxyIhfv/pq4xV9JSxzBnMuOYDtoJMsldSD0L+E8sa8Jw==", "signatures": [{"sig": "MEUCIQC2WwhM5ufCyN5/2A4YQNPRz0mR7ckKlgru+pf3SzabZwIgGzpJvAA2fpj3XyqwFvzOBfhqnajWVRllJPofjs59xFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50484}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-tabs", "version": "1.1.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a72da059593cba30fccb30a226d63af686b32854", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-9u/tQJMcC2aGq7KXpGivMm1mgq7oRJKXphDwdypPd/j21j/2znamPU8WkXgnhUaTrSFNIt8XhOyCAupg8/GbwQ==", "signatures": [{"sig": "MEYCIQDS9V0+mgIzNzSyh7mx5WHI+C+WF66lYveiNOC2I20rqwIhAMnqQePCc/cqolOPVELTk/K2XyrG7KDuocript2MGZJ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50431}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-tabs", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb23c6317b61a7301dc99528308ff35a15ae369d", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-zmYat4HTGDhxY6swdpJHveadjYIQ8DPiR5qRZ+gEcb6Xlwk02pVoGR7UPZ8Qyp+Q/4in1vI7AbtYKVHbE84dJA==", "signatures": [{"sig": "MEUCIG+9zS8pY4ZZaf3IpyiRjr8uB6vob6Vn+2g0cJIc/xt0AiEAhS2dMTa9pTyP/ijhqS/DL76zUlMCEVyLu6njC1Mcnrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50464}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-tabs", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "70dbf1ae19470676c55e38efea1e95c26e5437f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-gnTBC+HplIxoKCLI+yxhk1GbbFs6lgDJyVuQ7bgJ1IIDuzHdijU0+dY7sYDmuSZCJR3P7KZgd5oYs1GpJwleyw==", "signatures": [{"sig": "MEUCIQDofoifV3vUxa80qsNjYTnm4AhoxqEFStIRGBW23cp/HwIgOyOY2Nqcp9SVmuobz0zfOpWPEeQs/6m9AuYv4ID/BZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50464}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-tabs", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "0.0.0-20250116193558", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "02e54d591cd34fc07c0263d8b2e9586c350e82c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-4MzP7JQP7XB3xAAf5l+xkAtlPUnHrM8RHH43XXGFvLgs5ZH5ytax+N3sWJ3CbJ9WUEfvZalAWQE7ma1F3zJ8ig==", "signatures": [{"sig": "MEYCIQC7DmuzGuv7pfzH3w0hr6o1T2gvBTM5hJRJcsksUAEKFgIhAMm0XzgVxw96v9yUNaVHDIuLsFa2lyE71sCDJZ1U2Fn2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50645}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-tabs", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-roving-focus": "0.0.0-20250116194335", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "62a25aaf6d1bcb39217af8a1e92152bc14a18e50", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-bXFAf6y64XffFZDWb+nSqFxkv03kFA15zAcUIp4QbaPzRfH4YEtgEAdfTyBNLt7Xo/fBGk9lwPQT+lunSsqq8g==", "signatures": [{"sig": "MEYCIQCCWRwbtj7Puwjc6YTjMbeW+O5EUtF7T9OeqFTKhDQddwIhAIZzNPGH62tnYoVuOFeN+4amXn6tMTAtfsNPd6kdoUcy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50645}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-roving-focus": "1.1.2-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ea5c398649efa119962ce9fba90b723abdd98742", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-NgdicVhBnDktE3rzS7vK8n3UezcOw+c8Ekv8OScj2hpA3Gz+4mERsy+gN8XYhwB5yLNHUvcx26WFJj+zASUN3Q==", "signatures": [{"sig": "MEUCIHarOAaNXaHPIfEF58ZN9b1GGId9dYXHM4IunGf1fhUCAiEArNV+dak6ZGWeJdv39ZsR8Z42JakQLEr7kg1AIJPGiYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50687}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-roving-focus": "1.1.2-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88cbf42dc0a7f64ccb4fb90d0a5dbc53d0841f9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-lSygzfz+uMxy5xPJX7KyBReqslizBxd1Pfrvivk5q5gnW/hBOC75bpbhok0Ws7DEzDnwZ1bSPCo0t4T1oqcKig==", "signatures": [{"sig": "MEUCIQCvveitEbwBgyXjB1IFBnz7Xnt1Ovu4h00elUSqwdL4rgIgPjP1CteeaX7siwu/rZHErG5JrZ7u6TAuK77cCvcHh4w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50687}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-roving-focus": "1.1.2-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a83e515e84e42b6099b3d3a8cc508e6300c5b3b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-+2bNl5JFxXw0VOQu5lKNw98lsZBuRrzj/Q8kPPdpxd6GwPK63N6L7haIiP/lJ00EzAinr0Dgf2sqEhW7IRzOCg==", "signatures": [{"sig": "MEQCIENI06HbHyjrg5RMYlLvwm2336CyDRs3K9BEdanA30TJAiAkPi3LYY1JCvfkidsQraSKq4woJaGfIzwffQbDvV2rEQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50791}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-roving-focus": "1.1.2-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7c2b8949d3f68ce37db6f5b1e2acdcd845960132", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-mB+iG9Enxu/3RXWAmfkJrLZb4izsAXormMQ/SXLats+u6kidTpgCkzbHDbGhzJqscSLfaPDUelC+CHHs7wZWHQ==", "signatures": [{"sig": "MEUCIArJ/U1wIZPwnN46aLQfR2BkJnq1wurfT5cJlUHAc22EAiEA66j2SixlZggFt5yl1cu7seL6K40k8mc8+fzTu0CkGv4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50791}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-tabs", "version": "1.1.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-roving-focus": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c47c8202dc676dea47676215863d2ef9b141c17a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-9mFyI30cuRDImbmFF6O2KUJdgEOsGh9Vmx9x/Dh9tOhL7BngmQPQfwW4aejKm5OHpfWIdmeV6ySyuxoOGjtNng==", "signatures": [{"sig": "MEQCIDmaiPImfp5UuHbmz09DuLzV+P2yCBnsLDNMN32xdaYGAiBaAKmIrKxIzNJ7McYM8pTImGqgIJ5J3QznVmtSOUpJkA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50748}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.3-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-roving-focus": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "17bc455af0ea9db3f4b86de281024b9b321e39fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-SCOWoxjMEHEwmaBgEuzvvXGnuHhggn8OvolKoWLRCjYgMvCT/uPvQm94TE1H7SgFsyDpv0upgo7+OicY6t4deg==", "signatures": [{"sig": "MEQCIHXNDOxV2gtTQNcETg9N8IKdHIy1IQLbGTl5lpFoOpgkAiAWuYAilhv0E5VDeWbAahUjv7+qGQfNd6qizpaVWiBWdA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50786}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-presence": "1.1.3-rc.2", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-roving-focus": "1.1.3-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f211533c974834146c15d425dca2ef4916ecc8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-kKs86iganMtGzIyRlXSoidtdskXxpaXlpJw13xbnQ5K/sA/Vr8oGXt0klf05r5sZgyxpvB22nkoaHUBTeZUPRQ==", "signatures": [{"sig": "MEYCIQD5HT5EEVvbXj+8YT4IT/L0ew5nB1Rlzmku6wMGVgnLAwIhAMX9OcAbmy08n9Zv34RCy8nV5ftMj6yhdSMPpeQCq9z3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-presence": "1.1.3-rc.3", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-roving-focus": "1.1.3-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df7e4075ff528efad2de6f3f9b93dc07def67e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-PYhN8DM5RRGSJiKfELHZz4KIzcMXhtU5iJbE8Su3KSpwZ1k32AgX6aFbU9zdXZne9GGR3wJxMI/VKB0Y9pTp9g==", "signatures": [{"sig": "MEYCIQDeGwzOWA35p8mGJOKRsy86cjltNCm3izsbe7x+ka5RfAIhAOzfxrQ2no3pCtQz08XI7AcO1+S9SDf2aR0PVcXfId5z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-presence": "1.1.3-rc.4", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-roving-focus": "1.1.3-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f94b91492983f0f72f9a723c62b6e62ccc14780", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ADXLQuyH0O9p8tYe5gfkINZq1oJL+APAFAScJNHoLQ83cBpVdeR46Twql9JIzvKVtUXKoALsddNwUQCrvIf6gQ==", "signatures": [{"sig": "MEUCIGoa0tLnOt9644hmWO1RBn93ohduxCZCT2WSxv0YAyVvAiEA1YmtJkG6lN2Zizp0/8HbKzxaoMV5UdXdytxicNuxcuA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-presence": "1.1.3-rc.5", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-roving-focus": "1.1.3-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2310051596bc54ffde4f279dd09cf765b756b48e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-39ugUaW7/H3uCMvdeS/O45zXpEq0Rl7GuaXUlvJd/NMJ1fNKpcx/Pbo2kv1bEDV1i1StlhhI834pa5Y99c8Q1Q==", "signatures": [{"sig": "MEYCIQCGRJRY0+ZPnl1U7Wo+J3765+DBlXn/nuQUP5HaceGf6gIhAPMZlOYYexe6aQfuiCqDDYFoxkaJIg+kMU6aqUauE4mi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-presence": "1.1.3-rc.6", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-roving-focus": "1.1.3-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c8cb46c334ad784a07a4f58b108a62b06a28cf73", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-RhDMuORxabm2OFlz4noDn2EXmJ2GXPTMC6DGR3k/3zlykDiIXwVHVAnL9tFEUu8y1HAoJD+F+XexncuaGDHtyQ==", "signatures": [{"sig": "MEYCIQCkGSckrSeqwFNM9WwO7Ap5+iksoGQSb2X4iRvtBswFZwIhAIs8O21IaGEY0DFtDAefEIjwMiIBC9kiRXwePeJo86FF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-presence": "1.1.3-rc.7", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-roving-focus": "1.1.3-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7275086b678b2b5e3a6f80bfd90cdba2f871028", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-qzHf2xsXOQWQGr1sdif56zvVsEFnp38NTX2WWWjJXk+gZTHdotBKy5QWaF51pcwR4tsAf80kTnjEAeLACQxBow==", "signatures": [{"sig": "MEUCIFEoaDgrJqfck7XLsg2Cgqrn9AU5tA2XnzmeqYrb7lFuAiEAuhiMC5bQBcUDyYETX2G2DLMRbZMHovSe+zvWRpju90c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-presence": "1.1.3-rc.8", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-roving-focus": "1.1.3-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3232ebf8f1cea43687063b8fc5fba4d9675be796", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-L8RSJoO/QGjDJQPHy5TjwASsKj/sNuAeoYi92+AC6vLZISIWF4PsnB7a5YQqkkr+wG2tHy59YTycFRf5Shbs0w==", "signatures": [{"sig": "MEUCIEggLwR42J7NE0jgDn2JW6gXYwZhETYIgaqpkl0/34R3AiEA2fJAl+q8JK8hoESfj78MiIxRACML/1nP+QiYdEq3x68=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 50827}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-presence": "1.1.3-rc.9", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-roving-focus": "1.1.3-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8ca49cf0b3ecd36c3034343724144c5beff398ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-SjbMlFgYsdktIrd0v2r7Nnzd2hIdut3a21i4vqg0AB2/CEDqBgB4h6Vq1FTeLXoWAPuJVVe2ScJqbDBZIrP3vw==", "signatures": [{"sig": "MEYCIQDO6EeoFp98Bg/YibWi9zUE6nGPnkytr1/tHeXvLCq0jQIhANUAnBOISka+aaa3vFxK0Zi7OaRxRKRPU3/96XQU6Qhl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.10": {"name": "@radix-ui/react-tabs", "version": "1.1.4-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-presence": "1.1.3-rc.10", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-roving-focus": "1.1.3-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c86c70db1927b2a6386496c281ae099817c0acbd", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-g79xOFCdklhgS5l/iTMKeMBXoMAE0ntlNtlv5hm9rVxYa5tPP3vOB5gV0mbI17PqSrDB6uvedSh+pFgubPUK7Q==", "signatures": [{"sig": "MEUCIAitM6k8TVI6l2mx84q4IQLxCjbRnZ7cSnwEjR+xe6DdAiEAqZa9PbA1n6eioFparXsQsvzDi4q9yvAY1myb8vpvG6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51220}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-tabs", "version": "1.1.4", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.3", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2e43f3ef3450143281e7c1491da1e5d7941b9826", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-fuHMHWSf5SRhXke+DbHXj2wVMo+ghVH30vhX3XVacdXqDl+J4XWafMIGOOER861QpBx1jxgwKXL2dQnfrsd8MQ==", "signatures": [{"sig": "MEUCIG/VKv/JKU4K0NO360JxnJhFXUy1uc8j9T8Zxd4N3lvjAiEAzjVgwGStjZ4BruMGSSA9HRwdmHvGTfbd0pHQwQsMMgY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51145}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259191780": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744259191780", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259191780", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "49c99d6491b9c46544ad8988f8d4e9a9c6494b7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-DMn6GQZxXUnKxbGPZMtNfy6r86zZJOpULiC+CTN1lsKbMinEk1nwnf9je5SAWwGGvd8/k1bKqSmBr01KgitbLA==", "signatures": [{"sig": "MEUCIHdg0x4Rke8mTTzIiAT6hMzOy6tXqXFfeeXQlHvjBzLhAiEA5arAIxFnmjWj6y8XMC3LVHrrXVuiwNBGeMlsJs0w/Vo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259481941": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744259481941", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259481941", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c8a4d418d8a0f723b6efd84d50fe5af256b6121f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-jIQux1FpJgainKU2hEGeMGcxHcLy460qDu9cq8dA0ytCjuXy2UCkqsUeiF3TasJDRxjPrLY9JrifrV+INepZnQ==", "signatures": [{"sig": "MEYCIQD5rz6Q1kJo4Z53fazPih8WCKL4WBV6px0Ao2hYiPRIqQIhANNqF8OSlBzTEd765zWwjs34nbwSUdHIt5y74JbNyBMM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744311029001": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744311029001", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-roving-focus": "1.1.4-rc.1744311029001", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec7cfb3116e141d649f7b02287b3898d15dc838b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-UaLP4yjFckWgh1UG5xRcXZpIIHfdNnteqNOuvShZkBY04TCdEFWgZBXuw6lqjbyKucEknwq6azPSQ+Ca4ylzWw==", "signatures": [{"sig": "MEQCICLAGtrBa+jXU6J2M7pLNMnwaVV8EcgjklCgOgoi09AlAiB//+Ow5F5al/IrrII30GKqoAO2WSrWyCJF9SqThIbD8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744416976900": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744416976900", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-roving-focus": "1.1.4-rc.1744416976900", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b55bffc202abbe1aaa2c6215c4ebef2ffbec97d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-NR1DMRMOfAzQm1F5Q5RdKthS0YI2+cyRUkMChg8VppfGQcsbm4PwlD8si1xNypb8KtL/xc9KXfVq2kF+/d8zZA==", "signatures": [{"sig": "MEYCIQDLgY/NDOyqGmfcqyOHRpxsuPEIHIRmK3wtWD2U8BDLoAIhAM4AIUNi84Gryr3eNvhu47wrHwhzS7owc66yirlMAKl+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744502104733": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744502104733", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-roving-focus": "1.1.4-rc.1744502104733", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a258f6a1dd18e008f291896d8934928f190fb4f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-Vc7m7Uj4kYiDhcWLWuu2R9Okx2tFGgN2zmwe6QpIikYjGxUR7ZucNNNbp8GvjpdmyC/+H5ikZES0EHnZG+Pfvw==", "signatures": [{"sig": "MEQCIAe+D8+97/9GAyL4bl09CJtbDuBqdum9GSjx3pQuO/xEAiAdRi2m9FcI8gLGIxjX7eE/QbsA9vasfh8goL48FWBSjw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744518250005": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744518250005", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-roving-focus": "1.1.4-rc.1744518250005", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "86951a1508c896be3754510bd0a1566082b16f32", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-QXrCZjAz/LXnOJr9/9L5FpWEUFn7LChdzDjG+/XqebAX9PhD+fYLlHMrjvM3qZg606Avu2akByk2AVVtMFyx6Q==", "signatures": [{"sig": "MEYCIQD39f1pQFdftV8NQsggVaVcWbosXFxan/0QhlMXuBt2SQIhALhejUxAvDIhtZKOYGvRMNekcpMjzun1rqCuIBh1N5FX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744519235198": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744519235198", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-roving-focus": "1.1.4-rc.1744519235198", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "97720c7e5e23a66167e12ac70b0eae73a5f62540", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-FsAqIGE26enSlawu/bjWLKDn0NaP43fQHdM8CaKblY+ShFP5wIodv6xjx+ZAvz9gYJxKnuNp7zWMLAZJez50VQ==", "signatures": [{"sig": "MEQCICGG2nKS70t4Tp8Fr3AqJYHjjDeSUA5pGudTK1N5X/lPAiAuxLjr8ItZ2mk2IXe5i7xtF7zlPMX6/UsIm9e1Kzbkrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744574857111": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744574857111", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-roving-focus": "1.1.4-rc.1744574857111", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "91dfe588ea1e9beee60e74d9242a1aea2de0965f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-1d0U68CNSGDcuSaXJHJ1hqlCnA8/qHhVHmgH8OukS0fhB74oVyoUSS+UoTS0a/4qY1yKI6GHIejr+cru8JWFlA==", "signatures": [{"sig": "MEYCIQCccQXPnQN6hhzPbwY6ReTzSF5oiKbBNNgYC9sbsVJ6iQIhAKadsgSNaiLzwbykfFeGzhBiFEyFt7F7k4BuMiWCYxy/", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744660991666": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744660991666", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-roving-focus": "1.1.4-rc.1744660991666", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "92ec3a93ba8c7c31a7fcfc1a3c64f3c2a47a7121", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-kc+hxCAya0ILXvhlmNsFoS/n5dTvF24eJhLvggvgriUTVz8lOjrYWAd0XKeRXZYQzgHp7e6LwNvdeuaicdYXRA==", "signatures": [{"sig": "MEUCIQDkQb5itsf32nucAy8vAdt9t0dC1oD6Te/qT3TcjMywpgIgGWqzV6QVQxMwl5HNssT53ovkrpU052xDcL+7sJNvX3U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51775}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744661316162": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744661316162", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-roving-focus": "1.1.4-rc.1744661316162", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a79d3089cf98907aa78b6434d55196a9d76372b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-ZqyYZhFWbrKy0UarX0vYdicsMQwR3pYaHnYmfMz4gkI5fGBf5t5tf2JRSdJjHXSUGWAC5dxwLf4oqXRepPwteQ==", "signatures": [{"sig": "MEUCIA6RYX45XhAeEPRr/bJqtzDoA0a2bT62uBwHYMjuNfwsAiEAnJcbkLMDdTAXxVdp13eltBtcFQaVC1bTyAMlMbatoBs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744830756566": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744830756566", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-roving-focus": "1.1.4-rc.1744830756566", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "efae8414e7c0ff2c03acb960e1c8a2320f4e2b7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-NexumEY2NuTLLsMKXj4CrgyG338agMaYvxRbbWeukoddQllxJx2pnbRfKlIW+km4tX3+JUq61bFjT2ehXj0wpw==", "signatures": [{"sig": "MEYCIQD4ZYS9e+rEnafAdXLasbICFUhvCfD3P0MDXmqV+W9mewIhAKEG6vOexAwhN7TiY2aw/zvCvQ4Mq/BgZuXzqqlsol5l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744831331200": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744831331200", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-roving-focus": "1.1.4-rc.1744831331200", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a51ec60f577d281c9958251c3eddd0c753fcde4", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-4eD+eAJFnzVldd0ZXkplj1JtmD11oV7BRmX05wOLRPk/EMkGhkNDCBqJ4TQrgy2vNLgWESrENwCTB5yIU3ERmw==", "signatures": [{"sig": "MEQCIBwxE7qNpy+cG53CCW405+T4Xpf9NdnFa/wRrYlDcEVsAiAony66Mvek+e4ifIep8EzQF50xGRZHwrmW/KxuDZhMaA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744836032308": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744836032308", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-roving-focus": "1.1.4-rc.1744836032308", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fb98a051669af5dbdaa8b56296d7f445f0f3ee26", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-9/IR/M/ck9PptvYsqoGHOtzTDxyJa0t6IMz3/a3uBnDvrPjSHq+T/DxINENVjPMXgXjO7dgaup3quUrO80KCPw==", "signatures": [{"sig": "MEUCIQD8fvmXTa9eREe3Re8+jnvkzI/3EgMbeM3EfTu2kTlODgIgE9V/wTC/lHN9WeqFNdMRrYigQ6CIMaWSaxF84mSVu3c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744897529216": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744897529216", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-roving-focus": "1.1.4-rc.1744897529216", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94ed9ee130aa463a1c28eeddbc9106e1cdde3a05", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-ZOfjofqoUSEM6iPAJ5ID9tehaTvfwWYY7J0O+eSUmDywjdRfGk+kr86YjLe7c66uv+B/rCkqfcxKZCYG1d61yw==", "signatures": [{"sig": "MEQCICIVhcJrK+tTPOYN3auVD5Aiv2GW4ZJ2JfkCOVqBpnhkAiAQEtGlnZ9qJYd1E2enr15duH/h7ONQzMaHdSM8GyydNw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744898528774": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744898528774", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-roving-focus": "1.1.4-rc.1744898528774", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "32454f7cfde0bb3629dc3bb58d641d740835eb44", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-/H8pPNoI3MKtf/2f4r+aXl6ZsxIIVATfxA7YEPr+pJyNVf/NE3RyS3FAYbYVx4DsC2h4p93r/PJ6qt0/ek3pJA==", "signatures": [{"sig": "MEQCIH8eYC8w+ytI8v94uJQPuJTE7tSLLOn+Wc5MvJr3bLMvAiA3kJxhxlHytAY3BeMd2DKv6f7fCSDXJD/UlSFMH35uNA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744905634543": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744905634543", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-roving-focus": "1.1.4-rc.1744905634543", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "42bcb3f1748b4d9eb24d8ef18d11b1677171e3bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-jmTzEnUOb6vdUp2UN1TosdxMQBUn0/0Tw4FYnJbSlk3jn+0umFq4bN1fPkBgD/LsB0ivrleUVkT+WYYbpqVJmg==", "signatures": [{"sig": "MEUCID7zxC/PGFsGA5DwE28d3422ADlMDZuBv+z7/VkMKd/4AiEA3xsOTWFEQwHNXXRu5VFjpk2xgRmA7v2QU2OmkrVOiaM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744910682821": {"name": "@radix-ui/react-tabs", "version": "1.1.5-rc.1744910682821", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-roving-focus": "1.1.4-rc.1744910682821", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab8c68c6305879194374d7901537a1111ae95dfb", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-HdsIOS3OfWoLLXXsdBq+nSpqp3ZvVCIdN1GwfjzSWXPVrVIVwLe+coyGVefI2BZL7wZ0SJMmKY6cemIWi6iusw==", "signatures": [{"sig": "MEUCICnpTR9sW+gHRcSQwWoU3brX6YFRdieUcmEl1BM3cI5HAiEAwwONgYvlyc3ktm3v5EgQUZGmwj+bBXdU7dr8PT7DwLE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51945}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-tabs", "version": "1.1.5", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.4", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8afaa143bb7936c8c83a5065b80d2c106c6d20ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-mqIRWDZOeLm58x5P/kkN2r75Nw/Y0Nvx/G8DE/up1Xr8e6NYjCaHxS+0s0ENpI7NbqEDwk7C3xNvAx/NClLxCQ==", "signatures": [{"sig": "MEYCIQD4MV363pEphT2w091aaM6JndzE55c4lCwGlZWE/ARFwgIhAOU6mr/9p0u0Y7SvxpdfsJximkLuIWxzgWboHlljgubO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51877}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744998730501": {"name": "@radix-ui/react-tabs", "version": "1.1.6-rc.1744998730501", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998730501", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1ca7857734af9bd3cb6e8ee1c66566a9bee89b86", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.6-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-isdBmojKc1NULAG71CpW0pjMRMurJG+RR4rTGmwpoqHKQs0ECi9mRpIkvcXNKkn2dtG7g8kqNEDcJnPp+6ilcA==", "signatures": [{"sig": "MEYCIQDLY8j1f9j/9BftU0LlrGeFmLatGtx5VIduONb9lrGwpwIhALvZbKIF//O9t/+zY0isgJOsquF+US4axe3WrtMPOpnT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744998943107": {"name": "@radix-ui/react-tabs", "version": "1.1.6-rc.1744998943107", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998943107", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac22e0cf0314737a58e8e8268ba222f6a2e289df", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.6-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-zW2b+uaV61/zMXwTzHCtcKecbKKFEQglQCzP6I+jmvm6jUhao6VLs9CI3fveZMc0zIgJ/wWFXXpvRe3UPRb7fA==", "signatures": [{"sig": "MEYCIQCxzVGfH64RRf5728dDBR1to94NucHxarlyffFFlem4+gIhAIRpliPW9kXz6eDqYw35YTK+X42cVNXCMOoMDOzgMIIt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744999865452": {"name": "@radix-ui/react-tabs", "version": "1.1.6-rc.1744999865452", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5-rc.1744999865452", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff28672f64ab4dcd8b3a0b71346cabfeaf15aea1", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.6-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-f/nCcgYVAkPXQSBTN+1bjq/sQGaqr3+p7k/77VFYNmqT+zNwUpXH7Kik2Tfv1l6mf9f4hH5ju69Mg52nmz3lNw==", "signatures": [{"sig": "MEUCID5qGk+Mp4MA1SmXIFiogvEFgODqHYuDRrsXYyDj4orBAiEAyA4B7xyAytWZXEI8ho8oWEM5sg9ccoYoaP4LosrL2aE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-tabs", "version": "1.1.6", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.5", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8101e457756a1ba2bd366ce8887bf19d42e02ae5", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-aglCVrMIYX8bLEobfep1Hv2sQ9rLzk9FF0pQh/cCSAzD+aaceFptVVEQNWkrchlBhqgXnhNhzFFJ2w4oB3XMBQ==", "signatures": [{"sig": "MEUCICRWGs0nWMJFGeYJyR9oHNqoMl5QCURkg2Zw3nfE7VmIAiEAjZYCkDlb+2X93EcjaG9F+q0dLW94jcuCmmBV1Mfrpz0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51877}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745001912396": {"name": "@radix-ui/react-tabs", "version": "1.1.7-rc.1745001912396", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6-rc.1745001912396", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6ac53c5e41a6f248eb08ff445863b2c53fcc864b", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.7-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-xl7fZgGp2iuaX1fKUXRoBweBaAIY3IurrJlkNmx12q6OIsujO0L24WO5gYWoB01j8SWAJC3idGVWNb8x4BYL2w==", "signatures": [{"sig": "MEYCIQCC87xS6TFoQlw4AntyzWgTcbdZuNYAvP8enQJabSDvEQIhAKwIBN75x/YiuSLB7BdwzYnEToX8rBLsGftCGcoiAIOl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745002236885": {"name": "@radix-ui/react-tabs", "version": "1.1.7-rc.1745002236885", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6-rc.1745002236885", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94d142067556877344d5078450956eeecb7512b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.7-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-9qFCIKBSkEmNuBLv5q14o+nRJA3ReYHwOPmrkFs0U5uTp4Q6JJGF5GPFZMHIunMEE+2EOSSYXIq12nb8KccSaw==", "signatures": [{"sig": "MEYCIQCHNvscbCzm6BXP6A74WDHiM4fUB9U3JV167ouugG3FxAIhALiVsR3oSGEoOShpnLgzfb5Rj7SFoapcOKVNubBRYLV7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51928}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-tabs", "version": "1.1.7", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.6", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "55caac4902824fa309caf84517cbeb683276e47e", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-sawt4HkD+6haVGjYOC3BMIiCumBpqTK6o407n6zN/6yReed2EN7bXyykNrpqg+xCfudpBUZg7Y2cJBd/x/iybA==", "signatures": [{"sig": "MEUCIQCT3CImwAE6P5JYRPdjsZnRU78Qq/SNFAzSlNW4FGCo+AIgCHrNU9DhPu5Jv3ROcH9DPwh+ZUSegbP+yCLcjv9mrR0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51877}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745092579314": {"name": "@radix-ui/react-tabs", "version": "1.1.8-rc.1745092579314", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7-rc.1745092579314", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "61af18f3ac935408a141e88a6a152ee004d94ec6", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.8-rc.1745092579314.tgz", "fileCount": 9, "integrity": "sha512-9EO+Wl5NRWFKDnphBitaDsfWVzht9zuAZCL6xPskfnP1PZm7tgHJYu2TpX6TiQqk5McRH9kPRL9gi23kHFEnuw==", "signatures": [{"sig": "MEYCIQD2iF3uDJ0xdrQDwHUx3y7FhmD8klMRSl0CRGx6zpKcgQIhAKD+ax41Uh/5XvRP7dtQXRDyYa8+AxpwoILhCBzTKf3x", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51911}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-tabs", "version": "1.1.8", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "120d0699f1fc99c22c8d5327be2dbd249ef55782", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-4iUaN9SYtG+/E+hJ7jRks/Nv90f+uAsRHbLYA6BcA9EsR6GNWgsvtS4iwU2SP0tOZfDGAyqIT0yz7ckgohEIFA==", "signatures": [{"sig": "MEUCIQDbvt/F8vaNDMAPMFXlm4jen8bwlVNOPFMQgBT5WfjO7gIgBBV97gmA2txLSGpkGuJNqYACpkm5rJ9w9ld3RVN3iiQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51877}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1745097595920": {"name": "@radix-ui/react-tabs", "version": "1.1.9-rc.1745097595920", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745097595920", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3794ed8272c7ad64915b00128beb0ea3e46c13bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.9-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-9MPjhwlH2tMk8rlU/q7W8AE1EVRFL9YQGVWhFYqSGHdB9PFSr1FoBF3/Syqo4TmM0GxwMYMikZu/jvYy6IbeEg==", "signatures": [{"sig": "MEUCIQCYDaJD1LjgGNc29h86CusDHA85+owLKxQG1O+5oPmM6wIgVf3EnNIE09Zu0BnY/Hd7UDrHpcNwCMLUKc8EBU3Gjew=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51911}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1745339201309": {"name": "@radix-ui/react-tabs", "version": "1.1.9-rc.1745339201309", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745339201309", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1e42d037c980da794f77670886e176d85d39a278", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.9-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-Sl/VEiibIwgb4WeOva7sy1zT+gHqmseRZD2SsKnBa0jCFBNaSLS9GT6Pv15u+X5bPblwu2EGY1OxGybXSv4U/g==", "signatures": [{"sig": "MEYCIQCGLekbh61itRndvO6Qh41aUeTjM/C+awrMjJHPyWaeVQIhAOcCdcxZNpu7uyaFygY7u6aDQz13Hh7K1J1dTleqsPgU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51911}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-tabs", "version": "1.1.9", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4170432717f32d2e75de3a9853ad0a557ccc1346", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.9.tgz", "fileCount": 9, "integrity": "sha512-KIjtwciYvquiW/wAFkELZCVnaNLBsYNhTNcvl+zfMAbMhRkcvNuCLXDDd22L0j7tagpzVh/QwbFpwAATg7ILPw==", "signatures": [{"sig": "MEUCIAldFbzjKo2kVvJTqfFPjcu7Pw6/GAqPFiRsJs18akEgAiEAmzUT5VDwDECoEvDqcfd03jA08o7aLU/Dxjh6h2VHkbA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51877}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745345395380": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1745345395380", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-roving-focus": "1.1.8-rc.1745345395380", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "822c12bf2685df8546d061638a44ed023dde77a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-hTenZXVWgwV01IEQ3xokLPIeE/Weu3BCHOx3SNGuuy1Gmk/CR2O9r6m2PGv13x/8HUD/K8y3aJtMuAj2HNj3pA==", "signatures": [{"sig": "MEQCIHN3RfRgqn1HiPlkbAm4tE1mooG8pV94YL18T2FLB6RpAiBGLPGFr4DQKlwUaEQmKzjPcm6PFJ+ijBQtmHnjHOYmvA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745439717073": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1745439717073", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-roving-focus": "1.1.8-rc.1745439717073", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e300626955c320db44e3db3609adc5f14741634", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-j/rLl6Wp2uJkXA6JBEjMg4ZUhEE+5XMQxdEqg9T6cMc2aP1czkj7H2K436H9kU0MXM897SvJhwJ5kYKCwgFInA==", "signatures": [{"sig": "MEUCICyr1Cu5ilOHFmkjxd9VpxSNj3zmhUtluPkGl9oWzYIfAiEAjGhY4Wae5WhuVLK1iFUB2213TdDGj+tVZ+fEWU38+EY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745972185559": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1745972185559", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-roving-focus": "1.1.8-rc.1745972185559", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d1a32081de4c607f03538d85ae90a3b1f89c6e46", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-Vvk4BfyLPcVVd+Lb76+M4gP5gKob4tm9xRk0N0VuXKeq2x6I1/UyPF4dfgj26rEyHKCHY0FuVdcdNvBHNZR1+Q==", "signatures": [{"sig": "MEUCIHb0WpHF2/TtW+ajqsBLSD4GIZqqV0wAbmEt6QDV3Uf2AiEAgI30KEoDd4AWFSe7MNc1Gu3mCr697xa+TUuEM8PAJUQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746044551800": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1746044551800", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-roving-focus": "1.1.8-rc.1746044551800", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cecbd8bc689734e7e0d7c30ac5c198d2eb80aa17", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-5SVrFLIv68sK9UcdjrLwjcVCzAo4cJfOlODjiYpIZOjjO7pgxhp93lfKPIgLx3IcK6cocg3BfI82XWzRym9Iow==", "signatures": [{"sig": "MEYCIQCvafUrqeDJrXrnkcEf4wxq7fTIHwoMwIEPtLo0D4p5OgIhAIjFFctL4S05qr8Hv42YtlmaOubKw9l0eQZiaEvsy2s3", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746053194630": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1746053194630", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-roving-focus": "1.1.8-rc.1746053194630", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "015a598db68a574edd4266fded12118a00c02f47", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-s+QJcFk0me1APCClurxNfPJew7jHLI3Dgd8LD7sCMclBIW+gOIXrE30AdXhiIX+6J0TUZ6/GGYqV8Cza5pLGSA==", "signatures": [{"sig": "MEUCIQC+Mr4/cITbooDSYf2jMdpOaHpW981OihgeehKpgC7bQgIgXI5eP4P4CXnBuGHz91l1VpldCXA8jn25FgMWj1WCfR4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746075822931": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1746075822931", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-roving-focus": "1.1.8-rc.1746075822931", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb6508d91c597e9561fb4b696046f3a6be85ceb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-ezy<PERSON>H<PERSON>E34EUZYlGMYT2LEGaG3Tbv3wwthkHMI259MKjPY4I+LNZhsu8XQkz0xWEdwRTtbvR95awmTgqNH0OHJA==", "signatures": [{"sig": "MEUCIFh2SkF0Z/Y0BfmTxHKErxSqEg8G4uXG3PbVXmGXGyMoAiEA3Rx+0aXafeLrCu9Oso8iS3hhz/3ttSjk+NfrPP+IQZs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1746466567086": {"name": "@radix-ui/react-tabs", "version": "1.1.10-rc.1746466567086", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-roving-focus": "1.1.8-rc.1746466567086", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "45e526fa5ca20fbe7c0f711cd2ab038ccc3eedf9", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-Ge0kOJDN7jsrUwdtPzH68+6hBG7GNCaT7QKqnpYctwfGRjhiFz45GY5mbWl14t4n/q0FauyZDKr5+z6Y9ef87w==", "signatures": [{"sig": "MEQCIH1hke6zsp8Bwa38Qq2KfBu3/d8plH2so/YJKCJBUx/HAiBXhrUbvzRQbFo+eRd1D4XIRo2eLMDZ5zo6HnvopsjsRQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51929}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10": {"name": "@radix-ui/react-tabs", "version": "1.1.10", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-roving-focus": "1.1.8", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e8b0113d14df2c47dcb35863a94182020fee757f", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.10.tgz", "fileCount": 9, "integrity": "sha512-8Z/GnJNk+6haBS7kdjTj1nSM759tIABWpXoEUt9Ii2ctgwPCXCKvjMKnHl4KyBB2dOEKeTbiEJdjLGMgiKP9ow==", "signatures": [{"sig": "MEQCIF/y3+kRps+MmyFt036RDVh6OW+Jatcwbzqz+TxSPyP4AiBmChqhOjNHy0bdTmeFHa7MNUEtwdnTAmpfU7X8KWj9HA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51878}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11": {"name": "@radix-ui/react-tabs", "version": "1.1.11", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-roving-focus": "1.1.9", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9dc002ea6f8ad6830bc20f349afdc57c6039009c", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.11.tgz", "fileCount": 9, "integrity": "sha512-4FiKSVoXqPP/KfzlB7lwwqoFV6EPwkrrqGp9cUYXjwDYHhvpnqq79P+EPHKcdoTE7Rl8w/+6s9rTlsfXHES9GA==", "signatures": [{"sig": "MEQCIB9PH3t+D1DRyUk5EECbGP/f30ewhA12uJOQhmkMhhSzAiA9W1zBxPBevl5DjDrYhQ3vXvpwFV7xqN3jeDM42dh/eQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 51878}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1746560904918": {"name": "@radix-ui/react-tabs", "version": "1.1.12-rc.1746560904918", "dependencies": {"@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-presence": "1.1.4", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-roving-focus": "1.1.10-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-XAgxy936PWDhXvNCbKk+GCxoS59AiuIBVyij8UvLEsCfRRozI77XOzkpKGuXjWVjmQkFTP5Cno3jixVRd+DKIg==", "shasum": "27a7a17ad52ecae1d806cb565a633f26f1ea4df7", "tarball": "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.12-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 51946, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQD1X2HzDbX7H0PKZGSFwYtW8s7+DjpYQaYuiRlspI9pWgIhALgALH+MpH6Qnaa4p2/OwzZ3ilhizQOfw6EI+8tqaBpZ"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:53.608Z", "cachedAt": 1747660587935}