{"name": "json5", "dist-tags": {"latest": "2.2.3", "previous": "1.0.2"}, "versions": {"0.0.0": {"name": "json5", "version": "0.0.0", "dist": {"shasum": "f6e55f0133dc3b2c66c1fd3a51707b8bb6149f11", "tarball": "https://registry.npmjs.org/json5/-/json5-0.0.0.tgz", "integrity": "sha512-ZDjkQaNThtnUkaozqQf0uzMYj7DwVigcBoOCSyZKSAryxtFMcAO+/zYiCAsBClAowpoDHohTmYGZr9WhZb8kmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGfZggGYHeR74e7+AdfHPvYZRcyi+z3Fyr5PXzfVjqNbAiEAkh6DVhDyj+uwRIzi7Te9uPC2QNVPonabgOhkiVemMHM="}]}, "engines": {"node": "*"}}, "0.0.1": {"name": "json5", "version": "0.0.1", "devDependencies": {"mocha": "~1.0.3"}, "dist": {"shasum": "d9611910a4a438138b3ff4c9de68f1fa63cbde5b", "tarball": "https://registry.npmjs.org/json5/-/json5-0.0.1.tgz", "integrity": "sha512-SK4a+2DrenC8IvBzDtJbXWmmhVBoee2y3HfJzpv893NxTCRP+iGXSg0I6pKGULoo0cdCYDoXdHTPNdQKQ9TO+g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC7/T3XKqyNCpnNa0RFfsONBaAvOb+fqjAhvQGO1jkCRgIgYChQSzV4P2EM920uvg12ipq4bZ//W68HD+2ZrJDkLFM="}]}, "engines": {"node": "*"}}, "0.1.0": {"name": "json5", "version": "0.1.0", "devDependencies": {"mocha": "~1.0.3"}, "bin": {"json5": "lib/cli.js"}, "dist": {"shasum": "b23045f81f720422b0f3c7f3842be035765ccf2b", "tarball": "https://registry.npmjs.org/json5/-/json5-0.1.0.tgz", "integrity": "sha512-AHUF8eY1d/DMQN7I95o80l7t/kq5ORGFQfsm/aNMpRw1RAas2YtKPPd1zplyUo92CDM25mBHkxJ+TgcyEpvFgg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDxUIjtbmRbtQSaY3zBiDmOcOkdxIiqR0wp4VpBrg1gEAiAOzTu949H7Db3bg1iZCR3mLvpYXLsI+oMYYuVtCXmxXw=="}]}, "engines": {"node": "*"}}, "0.2.0": {"name": "json5", "version": "0.2.0", "devDependencies": {"mocha": "~1.0.3"}, "bin": {"json5": "lib/cli.js"}, "dist": {"shasum": "b6d7035c70c4570f883c7edc759de3ae03db3343", "tarball": "https://registry.npmjs.org/json5/-/json5-0.2.0.tgz", "integrity": "sha512-jzu3hxGhztAzldgKTbsW240mtPIgR6foddu9HqQgpv0ML2RcjE0mjyLro0XE92YAQYpTpcByY80vVzlKOM64xA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDp0Ek0D86yz2hVqeKs1qAOnJHT2IpPrR43KrBxkOnKUQIhALyliOcQvDX1p5PSUsPWqMDtcJrN7O3ipjX1aV+SpEUi"}]}, "engines": {"node": "*"}}, "0.4.0": {"name": "json5", "version": "0.4.0", "devDependencies": {"mocha": "~1.0.3"}, "bin": {"json5": "lib/cli.js"}, "dist": {"shasum": "054352e4c4c80c86c0923877d449de176a732c8d", "tarball": "https://registry.npmjs.org/json5/-/json5-0.4.0.tgz", "integrity": "sha512-5EEuuI7oad0d6c2PcrTRLoLH2JNuI/aJxHsVT2hVFK6fKHu+MXONdhzzzNAlb3JXMeuN1o+kDU78fV1YH6VmKQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH714i2QrT2XCQu+YGwxe/ynpNs4MN0epHcyUJiJTI3pAiBUbssnJoSJyxUEgp0mVFRuMJC536UAFgDSUuXjUXZYAg=="}]}}, "0.5.0": {"name": "json5", "version": "0.5.0", "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.0", "jshint": "^2.9.1", "jshint-stylish": "^2.1.0", "mocha": "^2.4.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"shasum": "9b20715b026cbe3778fd769edccd822d8332a5b2", "tarball": "https://registry.npmjs.org/json5/-/json5-0.5.0.tgz", "integrity": "sha512-WDgahySBucTVnQuzQHoVh6BKKg3TFBUExSwYOPwA4it9xtspn3erHYkdEx1AXXkHN38L7O6v6lmqLiXh/GnxhA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBmtEfZTLKwFk6O8iyXi+m0N6fIJy9bfgbjPd8uNzkrKAiEA4irgxACPbdoR1iSQ1XLdI+Ez0zngHbPh74Y3OpW6nEc="}]}}, "0.5.1": {"name": "json5", "version": "0.5.1", "devDependencies": {"gulp": "^3.9.1", "gulp-jshint": "^2.0.1", "jshint": "^2.9.3", "jshint-stylish": "^2.2.1", "mocha": "^3.1.0"}, "bin": {"json5": "lib/cli.js"}, "dist": {"shasum": "1eade7acc012034ad84e2396767ead9fa5495821", "tarball": "https://registry.npmjs.org/json5/-/json5-0.5.1.tgz", "integrity": "sha512-4xrs1aW+6N5DalkqSVA8fxh458CXvR99WU8WLKmq4v8eWAL86Xo3BVqyd3SkA9wEVjCMqyvvRRkshAdOnBp5rw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICJ8a/nbvIS5MEGcA4wfsTHtMQRPaq8UXWWd8Angn5CBAiB+R9n0Qj1SbYR+Sgka0V6IcsXTml1gl8ho/JCoPJ9HEA=="}]}}, "1.0.0-dates": {"name": "json5", "version": "1.0.0-dates", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-oRVnDhzcXyhsn6Bie8Uhzf08sk8vgl1terZbaHyoU43njf/XrRX7TaT7c4X3+UrT3v4pTz4V7IZae3dPywLjaA==", "shasum": "dbaef53fb06f3cc1868d280805240642d9672d2d", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-dates.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC01uVdhbIM8vwovz8YlZZwu1bfpvNw7ugL5ZR9USHpqAIgaGObo0JRBgjRzqfEqajYTsEaqVtNO8X4Eq9P49/2J2Y="}]}}, "1.0.0-regexps": {"name": "json5", "version": "1.0.0-regexps", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-+EiGNxMroEMZltFs+BMzQ5uDABfTdCQcezFlPqvxaZyTspya6wIiLkf5HruO+tAAyA+N06OhokhDKeitxGUrIw==", "shasum": "b9104fab83c81ee0e6f375ed1a191ed6d16af6f5", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-regexps.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFmIm2wkLjy8aEYiKKx+DsJTasqEEaqV28ZgiEoAGrEtAiEApgQ/Ap1VnArZlQ1apu0DdReOvWs2D0BAWBoOSRKtCdA="}]}}, "1.0.0-beta": {"name": "json5", "version": "1.0.0-beta", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-Opyud0vCd0lggCT1y9KcfqK1U4IigqsoyA2r63xmq3QrJFA4O4iwgA712s7o5RX5x+bY9le0EN1Xh3JNFzXp7Q==", "shasum": "ae83de99802cef1fa29b7b60a9330658b898eb1e", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYxEGQJ7G4IHjyeqmM8hW/GkA0DHz2ryzmgBjy17m9+gIgeGMhJaLKvmAwwTokE9yi1AKtDHGiEipKF2VcJmsl/wk="}]}}, "1.0.0-dates-2": {"name": "json5", "version": "1.0.0-dates-2", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-+vdcrGOSPf+7U1WkAMaBTMZbg15pdWjyTYsQpLBgSgJWhu+ES1gcGC9t4vJz6gTzdMn9/KVN/EdckJp9DHy1bQ==", "shasum": "87452e3f68b42fa8e7dfd51ad504cf54fc70397e", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-dates-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDa5dJWZ+MR8AM0NsliZfcC2sasFto02H7cW+wkV7XH6wIgXeoF+isX3GxEKoaeYWfOxipAswpAFzDBF7CTbIJygpA="}]}}, "1.0.0-regexps-2": {"name": "json5", "version": "1.0.0-regexps-2", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-2d/NVn+0xhqc0yZ1YKcUjC14OrC5WByLFnti88Sj/supkFczwTlpyuM8gJsn6xoO9Fh5bEqoYXBvnw3Xt1bdeA==", "shasum": "a631fc69924f9494f14ebf4ba037c871f2e38349", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-regexps-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAl+MIaXWUo3BvZe9FLP7UUKOPFm/Zi1IBLugY0Q5+57AiEAihDiukVzWD1K/Dy/ofrlnzKYMfAUKXJbjOYujTuZ9Y4="}]}}, "1.0.0-beta-2": {"name": "json5", "version": "1.0.0-beta-2", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-0+bPwFn6BoYvmvCTMgJQHp41gyRi3qx0rgxDRdMLAj0Z2ZnAvV66uMen2sl6T9Mc1GAT1T1gIam4t9O+DJojQg==", "shasum": "49302613041a604cc5de1f53c500e5e3e8bcd3a4", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta-2.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGMh4Oy1em9ImH8V3dkFpRda6d4Ch/6vJe+Z/HJkBXK6AiAX2yv7Q+w2H2lMBbXDaj9NY99Yf3sxpllHZKkzlMEsdg=="}]}}, "1.0.0-beta.4": {"name": "json5", "version": "1.0.0-beta.4", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.0", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "cross-env": "^5.0.5", "del": "^3.0.0", "eslint": "^4.7.1", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.1.1", "eslint-plugin-promise": "^3.5.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^3.5.3", "nyc": "^11.2.1", "regenerate": "^1.3.3", "rollup": "^0.50.0", "rollup-plugin-babel": "^3.0.2", "rollup-plugin-commonjs": "^8.2.1", "rollup-plugin-node-resolve": "^3.0.0", "rollup-plugin-uglify": "^2.0.1", "sinon": "^3.3.0", "unicode-9.0.0": "^0.7.4"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-GRMqinNVDjenUZXIYmabsbdgfIRm/5+qOtfu9DynLtM/w3nG5GsYCipq0hh2yk/yJu5ZBiOKbb+hdqkdKNwiYg==", "shasum": "32cd529f822b28574dce7458502b05e1fa78a311", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0-beta.4.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCs7PJrmG/u4EujRWZJVEW54dpbCcVhoPEP7EdvFazR5QIgaowTbFGldkg62lPet1D/DXSxjZha3+B27ajSDAScvE8="}]}}, "1.0.0": {"name": "json5", "version": "1.0.0", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-4HE2dgY5QAv1hryDi6Nt9p6cKEppXljgBB1z8bn+vrPlXzQTzkE4Qzm5yieSVMnRTcHBaLJgCg5WW7v8FYt4jA==", "shasum": "45f7f2db63d918e468a13197df6cfd9c06448054", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.0.tgz", "fileCount": 13, "unpackedSize": 87561, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDt0z4P+y8p4GmHw2CGp2TD8tBbUkAV5FdHwS+KY/NTSAiEAoEs/nrNFLvlWSBROfeYdRvYsxRGsmjNwnMU4j5b6iN0="}]}}, "1.0.1": {"name": "json5", "version": "1.0.1", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-aKS4WQjPenRxiQsC93MNfjx+nbF4PAdYzmd/1JIj8HYzqfbu86beTuNgXDzPknWk0n0uARlyewZo4s++ES36Ow==", "shasum": "779fb0018604fa854eacbf6252180d83543e3dbe", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.1.tgz", "fileCount": 13, "unpackedSize": 88338, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDiMFzoY6UvgCwfgrqJmDes4K1ShCOvSkzTGiPNLsjsRAIgAPpHEPBbICt0FbEZZQctsY+bf//zIZIvdJDsZeXuFSo="}]}}, "2.0.0": {"name": "json5", "version": "2.0.0", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-0EdQvHuLm7yJ7lyG5dp7Q3X2ku++BG5ZHaJ5FTnaXpKqDrw4pMxel5Bt3oAYMthnrthFBdnZ1FcsXTPyrQlV0w==", "shasum": "b61abf97aa178c4b5853a66cc8eecafd03045d78", "tarball": "https://registry.npmjs.org/json5/-/json5-2.0.0.tgz", "fileCount": 14, "unpackedSize": 147350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdktkCRA9TVsSAnZWagAASwwP/iHfO7OIEckQkRPb07vs\nh/n7n2Z3Dc4HWGmxHd5fyLJFsCaa/0DzzhyEHECQ+I5QZbkZg7bVUq+a8d/v\nB/ICBW9ecysFpW0l6HYY7u3Tv+gyyCujqxBC38ak0w+N016Abs4gUe1Th80V\n5dTxgZlvjmrdSi4CyylSrP9ZLaUd4FMFRNF18vi8MmzBWdAyaL9hJkgkcupY\nBkL1uw8jx5RkxoijqjdjCWQeBkIqYqii/9ahlkIVzCnmd3ZOJJ1b0uaAhaKE\nFRYe79SCAY6b+nbqgNGWN3U4DVwgqfiP+Nct+0cFdkRaHmWbmbVNWh4bZFMe\nemibVFdg1QIL15Y9iIGQ9YqdSWxF/CkYVXh/FSn1ey4Qt5UTNu7GFN+6aojD\nQ70/pO9sBogDxOXZ4hT3pIzSIY+tkIyWfmZWroYKMZZNpTrOj4DQss2mG8NK\nRDJco3MZbbWPSza//XmFwvcJrRcMDJbgmlXYDTca2V3CgJMZ+2OzRYRhC/cU\nRC9x7vf6XLulmA+rHLmp0sNnMioXZOulPIDPJxh3GxQXnbdHrjcH30OJSrug\npqbUgVYFG4n1mAtv7uQ/NQ5UojMcGOcGLU1oPDox/UVR+JQQOy8tjFFUOhzS\nr0vm+kyRuDnKVmmCCzQ3PvmL6w0DivZClJseYhr/xh5MNNgwrrmWncaUQX70\nqTl4\r\n=fzaU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHiZE1Ux30wUbDO4BEaXCWaAd9e9jI3aNxW2XwXYuqdWAiBDjLubLC5B5amHAIFYoHZNTk/omG7SF2akGm0kmVyhaA=="}]}, "engines": {"node": ">=6"}}, "2.0.1": {"name": "json5", "version": "2.0.1", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.5.7", "eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-t6N/86QDIRYvOL259jR5c5TbtMnekl2Ib314mGeMh37zAwjgbWHieqijPH7pWaogmJq1F2I4Sphg19U1s+ZnXQ==", "shasum": "3d6d0d1066039eb50984e66a7840e4f4b7a2c660", "tarball": "https://registry.npmjs.org/json5/-/json5-2.0.1.tgz", "fileCount": 14, "unpackedSize": 158770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeGhfCRA9TVsSAnZWagAAVFQQAIT3bHmfbfs1bMDybcfr\nF4Q7gR74UrMpwozFmeWfz0TAYln2w5khp1RkO6iFBY5yJKRnhkPFnhgvbTjH\npptW2lVHVfYTpyK+dw3w/b/ni6FNL11r0LDuUti4dez/IFxcE1qV8uLgx2UA\ng/qXYb56iNLbUpuFpMQwfk4z+jVC+qN6yzoDxv1d5r5PnhVsaeUIoYWNY1cZ\nZyyJkb7KnZLNStqLHqSr+h9Ra82sAs7xkmYRfa52hjfrQyo6lwBNAT0KFPlf\nzEYJAjr7AjBzDTvuBTb2DQ8pHiUPt5kF0063/B0GWuBz3PVEtkg/pHAfODkg\n3QufgBiz9n1hnRfuAIMNRFEJ+nDcVMBYwsXZyk/+heFHYHezrBa9XlU3eKWf\nYENFfftMpdSHM9W2tDMF6BVJRqxNytq8G0C8jD7/qb5Dds5I+mWDzXXMaQW2\n2sYItH9P8V0N/3s25tzvVVg4MryHYS2gUKANL43xHj/FGZDFroeIBVS88dPU\ncA+6qP5dO2j93bu27FxVeq12kV3hJxX/dP0h4++VdbOKimlikgb2aw9kSHKM\nARNau2a528duNppF/kLH0hXSQQSGxfVj4bVcxdNuOKGJynwQqIXnke9IlUpf\nexRqoDf4q4spsr6fOOGRlctz/BBomWB1RKEpAWK0TDUA0r1keJlw2AkomxZ+\nLdmW\r\n=6qOI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2gMWBZgtNd5KtnbN6NZxhN64so4bskmEAwlK/7SSI+gIhAIsF1Srk+YzHEy/+HVPgmOHETXJscnxoToGrMH39foEI"}]}, "engines": {"node": ">=6"}}, "2.1.0": {"name": "json5", "version": "2.1.0", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.5.7", "eslint": "^5.3.0", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^3.8.0", "eslint-plugin-standard": "^3.1.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.2", "rollup-plugin-commonjs": "^9.1.5", "rollup-plugin-node-resolve": "^3.3.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.1.5", "tap": "^12.0.1", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-8Mh9h6xViijj36g7Dxi+Y4S6hNGV96vcJZr/SrlHh1LR/pEn/8j/+qIBbs44YKl69Lrfctp4QD+AdWLTMqEZAQ==", "shasum": "e7a0c62c48285c628d20a10b85c89bb807c32850", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.0.tgz", "fileCount": 16, "unpackedSize": 233133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrbgVCRA9TVsSAnZWagAAamIP/0ocnmt26H7vE3NHdpA1\noyu/1NUXH4KlkglZnAhPiqcq6xyVf9KMM4Nq+1i4tpqDCZth1j0MCu5DanmU\n1FHY/XQEiW9YyNK1mNIorYMxGfcyFAhzGzQmKnW2Kf49yNRyIlD9lgVH+O4E\nfJxGkkVlGVxnxaUBufYzE5JPRGGhljg5nBAt5g7Q5tyBvDLLVDf69SsIsfRF\nPRpFJGZB2zYyxY9PUTGsSt7o9awZJKeH3OQnz9LuHLP0+y5DAEDQkyjR/g8R\nIpkUQ+wXIttVaOagvO3hHRZF0llA10hpyASmjvT+042KOe2njo41xZzNuPr2\nAbBpq8JXuTey1O7Ie4M7tlc6JkOwr18ZVws8YQr01RXYHueC9WhU34RNUAII\n84NkWkb43qBMOr+brq1rAs3QILrDsYbhK38mCD6IzJQSDJejfP6xnpAmtwU1\nXnu6FrDn+4fnOxGEfxIKct7NzEl1mVzG6X/SpT30EdgTKmpPB6AEALfNM+F+\nidirdG5IU6i6JJYNiyqC2JJOWgb/paKGYWkHtRGpM19bJImd+1Hnb/9Gg26Z\nsQ2mTZd3adJhwurZY6lUnDcDrLyVYYRnuhJBnUKrSTLDK/VkdhzhkblvAi3d\n1O6I6VLEYcxUijJE8dEs0PFRw6EeMJ0B6Lr9TLEprqcxbgNXEOEaqOhP2Prc\nr4HH\r\n=n65n\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2dKDBj1xa9CVl4wNXSJY77btfQ7cYL7iaQDQHjqYH7AIhAOrVdq5xL90VRuYQba+AgtTMaGwL+NPNpTaR8SE8x/r8"}]}, "engines": {"node": ">=6"}}, "2.1.1": {"name": "json5", "version": "2.1.1", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-l+3HXD0GEI3huGq1njuqtzYK8OYJyXMkOLtQ53pjWh89tvWS2h6l+1zMkYWqlb57+SiQodKZyvMEFb2X+KrFhQ==", "shasum": "81b6cb04e9ba496f1c7005d07b4368a2638f90b6", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.1.tgz", "fileCount": 16, "unpackedSize": 235263, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlQ2aCRA9TVsSAnZWagAA7wYP/3zt+LDk86xnARG4b2KI\nejD9LOxo421J1vHFhPwE6jIZuat3qvo5F5MLUWLqD1JkzoSUtZW7xEubSbXf\nco80S1gPjlxcBRo1PhxT8mfBcxP0tSb1Y55b9stg08dWbHc2jbx5zvssJ+sd\nEgKaG8q9RtYfjn+X1yvvmWl/pNjwKWwHiikDc8HGtBX0aeXVAFv/oIZim1vs\nH5RK795alMP5fLHq4Ss4DT/2bk2GPZ+aumPr/STx0aw5XWNPedEwR2ceThLN\nKSn2VR6dt/VDP58aQ6rQwGPZm/kQZgxdVeCPf1BeALp8SrZykndQM3zRTTrR\nshJl9Qs4mInTB6684YybqhlNdmwPYeuBeFmpAJorIMSbcLB9R6c1CWpwRgB2\nVVbp1RBQMFPG9NAOn1XAGkFwigg/EYyYsAt7Wk1AWa6KKjWKA0COyfv2UkkV\nGchDDt/zwUL1NOBGcYN+NYEiTUGjD9qb/oVdKg2UsDvOpGoEL4ehXh2oTWsq\nT54j37UK7U5ocGyjaYZmmKVGKE4Ci/DBFh76guAF90hraclQ0ueh8tMeu4LZ\nam418RMmkW4YVWzEx/nZQyBlYKDHcd+C4X2uLHhkXI8BHOoDeeZ8AF/uh8s3\nuNvDBY8TKs8P3nJDJxclKDjSVpYpnqPBAMxF+a5CgQjwkxzjT3yItqdyADRY\nknS8\r\n=rj8Q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICl3kdVH7cocHdNAQA4RbidD1QHn2PkIWw1+eTP6cjyvAiB1ZwMFfSSEeIhYEa/2lEc5zPpdK/edPJP5u0GZDf1E5Q=="}]}, "engines": {"node": ">=6"}}, "2.1.2": {"name": "json5", "version": "2.1.2", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-MoUOQ4WdiN3yxhm7NEVJSJrieAo5hNSLQ5sj05OTRHPL9HOBy8u4Bu88jsC1jvqAdN+E1bJmsUcZH+1HQxliqQ==", "shasum": "43ef1f0af9835dd624751a6b7fa48874fb2d608e", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.2.tgz", "fileCount": 16, "unpackedSize": 235557, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeb9hxCRA9TVsSAnZWagAAMz8QAJyodmBx/6/GrlNkWWe4\no+GeI3fE/VoeYPE2t6PYpIyA2jtnWfiMYbc5UEzPEFjVoVLjG9gyLyyraKNR\nVtC5tKL+J5go4QqN+GZDdxSI573fCsmHeD17LfFeMwpH3kES9ULwTmmcSz9Q\nhw15eKvTibgltXqUR9jzFFt1lQZUNtIairGhpd9fQ8+F/aZc5MVzP16B27fv\nxJzpEt6+80Taii+gxY9brZDd+ToL3K4j2ezKXQVVnwNffI1CT+WPOdYHgkes\n+9qV3WjHCO/yV8KvD4hDwrEhZa5WjpnBeSq4Yl65nlGd/gOT7bKJC2gBVyOn\nfpqb5bNfRjv9LyK/hqQcru9SzbIEy2xFYQTJaE6WnQ+DCJh85Mkns7HcLMHx\nGwviGPoVTaVpKsdQ0ZePhTZEBCZDUL/fsgKdz1XlDV8Sf7w0Vt+pdyyT3dN5\nJfatWgnKWqMryZ47fwbFv/+Z9ruRKvDkbO+jyYxr2wBrRNs1YDEbboSja8FI\nGzDq1Q4VvC6mReSq0RgmRPgfeYLge/8gDUq6D10oPKvCVV80Eduvt17pYQqc\nSSFa6KRBXe5AzRfXGUuHmigYC4yKgXm3hyvSwf02pVpjbz6mrKpn+bG4F40h\nJnIoRyNGI3r4ak/tRowodqu7cFRiU51fhgRZDEjPry/LFvMUDNi91bWeBIRs\nhzcJ\r\n=LHov\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFRcBxeH0Q9Wh7AKNou25A2bskuLXw+FcbXXabuavteyAiBL2q8eayTOT4vK/bVvgAUP0Fj9TdfaXkv9Vf66q8J5Qg=="}]}, "engines": {"node": ">=6"}}, "2.1.3": {"name": "json5", "version": "2.1.3", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-KXPvOm8K9IJKFM0bmdn8QXh7udDh1g/giieX0NLCaMnb4hEiVFqnop2ImTXCc5e0/oHz3LTqmHGtExn5hfMkOA==", "shasum": "c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43", "tarball": "https://registry.npmjs.org/json5/-/json5-2.1.3.tgz", "fileCount": 16, "unpackedSize": 236336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiREsCRA9TVsSAnZWagAAlCMP/1y7UinfaaTR/JjVT5Bh\nEuSlDfdo7la2tYNEh3dwn8R6dnEPZAwdzimdJ9gzNVaQL2ICs1a4XPcgbUHM\nIkz4AQ79FoSoUkzKmkcWVSuqSIrShGh5J8iSWdQfxxzijbWyCQtq9C3YSWBl\nvNWJNBYnQ7OA/+txjWAaYI2rokcdZv3VsE/dbexxZFjboKKSsR+ZRnu9F6+t\nfqn32olMTOLSBV3g7VF+vVpdBWa3GMCKeZ7NFx05bOtost0/SAVwPXAnDZFn\nhShY7/V/VTx2tnhYaYrFogzP55vLJSru9DXnS8TVDdAbHQ6+EsLAqcPIDR7R\nsVaaxXP8fnGcL6FeLyzljUHELe2P/l7DRkaoF5vRUSTwn+MNvBV5oGaGhehi\n/0K7UYEeduKXghVa2o9bXxIItTrIjygl0bBULAgvSHD2o99OiWDbNnFWFA06\nxAJnbJ8qEZfDUYkKIryDuBvk0ROTpBTAe3PfRHNk2QD0ZFQcghXcVHCs+40X\nZ97sQ7pc4XtsOFM9FiF+rzqg3OLy7BfeADPJJxuLGxMpaFkxRw/SN5Q8e2RQ\nUVPwOBi2qihYyCRoHACkALF59U5dYQ3OTATic+jSEAyF5a26rAbfXOxpePo4\nf9GkQjTsY6f0CE/h5rCYDSFZm+6w9rODvYpXCWbosYFXYI7Dn/RCpoliRTaw\neRo5\r\n=aFBt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBIumz2zsyEMkBiQ6nCCyoLtQnjwlkKyMwhdlG93rZYVAiEA9fDBlGG/tvsfMOfvb3z82f8Cen0CUuUpL8xQeFbfWxI="}]}, "engines": {"node": ">=6"}}, "2.2.0": {"name": "json5", "version": "2.2.0", "dependencies": {"minimist": "^1.2.5"}, "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-f+8cldu7X/y7RAJurMEJmdoKXGB/X550w2Nr3tTbezL6RwEE/iMcm+tZnXeoZtKuOq6ft8+CqzEkrIgx1fPoQA==", "shasum": "2dfefe720c6ba525d9ebd909950f0515316c89a3", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.0.tgz", "fileCount": 21, "unpackedSize": 242315, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgF2BKCRA9TVsSAnZWagAA+gkP/A/cB1upj1OY6FDHeLcx\npL/j1KQ8SIjXrMdtu0Cl182OPx3lxBsfNiYS15C6MKYzLVvukJ64Ht1HRBdI\nwpnD5FXyJydzKDpD13/oKmLCCxe+Ujckm4eyVBsyrUSY96z7idR5nxIGTbzx\n08+O4bOOJ2V2v3m+/LWd7J5SU4x6rebCHRt6C8yKe2HeJcJOAMkZzGtt4hhk\nU/2V/Tpu2H52rfggnmILk0vUudUddaLjkYZjcPTsxbAfJCeyfZx0Iw45e9a2\nPnE0JAWRNBsQYSDicm6/lmnCT4oRo7B0q1d8AaKXqlcLVTwyWVII6wDGySIg\nSFlHH2pQZdriy4E1vXtHikFWBbWRO0+5keiI9w2msWSxxgpRv9m1PhgWgTaa\ni69xfXcZBZfCcIQbgHom3B4gJfY7NwLEz/u+igILTVzaSJS/b2nXxSW485nh\nToHQ7IUDbHPyZQJsA7egE6ZADqDiMNWWgvbq4ZhErEQmYyeU0Kw/g5cLUYCA\nLdSG7jZLjPGTf5FbU5ynxcj058FWx9fCL3wLkxi/EBjcAlupXxh4qKfbXPnP\nWELZTf1Q1A8D4RzwEi0P+4ftCbjkrLeeQ+L/zfydCp897ZBz1FLHTPXb+WsS\n2ncocfPWCYrLDhFBUdy8UqKMERsfbsNRSP96MmLV3RrTpaz3cgKYvc6aBXjI\nQd9t\r\n=fbzO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFFyU4dMfi691Isu57fv54LD52YFuU7rz60pMxxP7+NTAiBVQHrM3fIgocP48lGx9+r1FXY9YvpNDoUggkZ6MvuI0w=="}]}, "engines": {"node": ">=6"}}, "2.2.1": {"name": "json5", "version": "2.2.1", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "shasum": "655d50ed1e6f95ad1a3caababd2b0efda10b395c", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "fileCount": 20, "unpackedSize": 228701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiOKiUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc1A//SuuKE6imiqSY0/zKdRwp5cXeHbu8vi/GaCwNu9m8Ab50PZGT\r\nDlEoFppkYYqa1pJiwFW4OxXYJH8yIeaBFYqu1XeSvlWJrYWvlL5Hnf/e7hS+\r\ndF6YfFgHquMdf+e6wY1jhSaA4qodJauwk7919KTSTDLvtQU0dP7u4HRpKNUZ\r\nSzlzhIP8/PD4BUv4VDTwrzHSxIEd3rKYJgNpChvkkPf/FnK7OWROJNy3n8+q\r\nwxYDAb366euyw4vw0phlWS+5JeHgOZbjIZNeR+ndjfkMZHQZpcUAew231Kw+\r\nPh0xgLHe3rMbV4ioDNtm2WmzFzrQtfFZqNgLySOHcC1jGBiFBCBFy9aAv6uN\r\nGacxM9vcGOGV29XTKhfR4Lxzho93JECRrK0GShSZjD8Q0Kv/QJZSc4lmj1sL\r\nxm0JCbhlo3kW3sbxopPOrMHvSdbZHMTAOWIlAJKrko7YrKtnpuaj3S9WqYRt\r\n31pfXj5RpIRtdvKJv5+fLkEYTWS/zk/efOS0w+G8n0bwZxC4AiSs71P+ya2b\r\ns2K0RxxPD3wyOx8PW1eJkjyxySXQy6MBaKazCCR1uN1Df04sqT04PAwu8U1o\r\noOSzm9W6uOLnVUWnZiv2otkTmZaHwRjidsIV5dteWcR47xVx2E9/54RU1S1Y\r\n4vYHm7wMEwIKo2jjz1vrmxpsMqRGUjt4mFY=\r\n=HXXR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDO2LJBVui71RDqZAsMwMEj5a/4iVzpUpMFzAERCZtvVAiB3AOTzvcWOIXUy73ankaCmMM5TtWnhUoTEj5tn3oma0g=="}]}, "engines": {"node": ">=6"}}, "2.2.2": {"name": "json5", "version": "2.2.2", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "npm-run-all": "^4.1.5", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-46Tk9JiOL2z7ytNQWFLpj99RZkVgeHf87yGQKsIkaPz1qSH9UczKH1rO7K3wgRselo0tYMUNfecYpm/p1vC7tQ==", "shasum": "64471c5bdcc564c18f7c1d4df2e2297f2457c5ab", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.2.tgz", "fileCount": 20, "unpackedSize": 235025, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICC0CeC8FMiPUQwBriLANhdc7Dxh4m9/YdnHOQ7ItsAdAiAsSKPZyyD5JFpKpVurHIkWIYHkKFcebR/aVHfckuZ8zw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjnBJnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruZRAAmLfrYCNkUckPI2IIxxSoIlc1mPpTYDJ8XN24rs/OMk9ffamB\r\nX7KPaTCLwgfT3gDl2/7d49YTr8hUdP02LuiOsvgYGTLIDHbXf+XrfUrbUS0d\r\n0pFGEm9LJAzSIOqJJcgBRqkIzJI+3f1NxhNk6LQGCw8r7CPqJzvUGOVBns9D\r\n+057N8l7jcfHQvyGqQ8XiSGltASdH06wZrXRGxckLj9YX3BzO9tg/JAgDt30\r\niLknxmb4+0EXWGLVwCk+TIqRJTB+lFuIYSJyYdeeNadk6TlGxCwyE5LRUp7U\r\nH4t1rJluK/58jbwBwhpdXvFGuJsVhs5jb5JUKrPdlz/P0b1bQKNFMWzin31d\r\nyvjK47QL87j4WVHuPm8+3Df3QE83AZwrEzEyDP6Yx9iweBtMsAEcBBpuXz3r\r\nVKx8MHma2zvFR+qpBQ1BF2fo546U5FqAYgKF2K9IrsZXIGM/awGSYkpn/c9j\r\nnRlxkFPeaaPllPSVC04pq1dwDkwoKY35wxgEk0NWtfUSfaxdKgIxherndK5d\r\nv4uy6YiUsO1KO3Dp2HZ76/C+bSQRUA1kWy1oSw1naglQnG2UuBOsFEHCRhmt\r\n2MxM6YbnqZyNrmucXgiHiVa9fHQlZStp5lKREPCMCzPnXpJ+HBX0J48q9sxO\r\nvyRBQoYBuE2gs5w9PUo+5ItPoMFjBfgZUzE=\r\n=ZSFY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "1.0.2": {"name": "json5", "version": "1.0.2", "dependencies": {"minimist": "^1.2.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-external-helpers": "^6.22.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.26.0", "babelrc-rollup": "^3.0.0", "coveralls": "^3.0.0", "cross-env": "^5.1.4", "del": "^3.0.0", "eslint": "^4.18.2", "eslint-config-standard": "^11.0.0", "eslint-plugin-import": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-promise": "^3.7.0", "eslint-plugin-standard": "^3.0.1", "mocha": "^5.0.4", "nyc": "^11.4.1", "regenerate": "^1.3.3", "rollup": "^0.56.5", "rollup-plugin-babel": "^3.0.3", "rollup-plugin-commonjs": "^9.0.0", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-uglify": "^3.0.0", "sinon": "^4.4.2", "unicode-9.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "shasum": "63d98d60f21b313b77c4d6da18bfa69d80e1d593", "tarball": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "fileCount": 12, "unpackedSize": 78288, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDwJqRTX/IozS44xtpjqVGLP2by4uSiYfI9zPZWqytwnAiEAhN0bCIdi2fmBbGw524rigqGJKIw/QX6EcY5It+mosXw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjrxtWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMAQ/8CdnPfppIS1NhVAtGfJMvdtpMMGLR5eXLSYNkuiw/JuzsBF46\r\neCxagzBhM3o6fnP4FwcXpEjkrTgRLdD3o3+/qhsWy7F6jlIfayQlQAaxM/DQ\r\n859vOWzBBDU5GxpaK1iu2wSs0Rryce2dQJvnsUUJf0yxCeC2mGSDYSJbVLiS\r\nCmNHGRQCPS90+Yg6lb+T4+TSFdCx06XMEleCOX4XjvHA6KzORjDy5D2jvFIw\r\nsSqCtiAUhOL/HPy8UEkHBrPdMv96uKY3reeK6fzVig9BeUGn23AhTm4g/2uO\r\nblE42q//urqVFrDgOhEoL64w/hdgQ6Wp/xao5h65fM7bgDA/9uOgWqjq9tJL\r\nTbxzoKXfJ8jYwxe6V8OkjJyu0xs37s3ClD9t5UoukCBeViD1pRoZzoqyp01l\r\nXeCNqzph/cj1VyYX4aeLUGhq4wfgagBe0fEGgw7bZlzKGI3XlFTd4cH9ifXK\r\nDQA+x/34cBF63JkS6rFqyAgPElgLMj2z9dlQDTomFUwzTtvOAcvl71sOsUxw\r\nD/w3tQT/fijv/z7RPqxBPl9o+aFySl/tipi7j6FexSYS2M7zGJI6S1wH55WF\r\nWCEjd+UFMcltOak5L0c+h7OS3t6p3gkkWVQG3laEWXpaU3+/I53M95Dkfgw2\r\nSlYPBkxtHr+GSU4PEdfaxz0rQSgyLmyic70=\r\n=1f2N\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.3": {"name": "json5", "version": "2.2.3", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "npm-run-all": "^4.1.5", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "bin": {"json5": "lib/cli.js"}, "dist": {"integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "shasum": "78cd6f1a19bdc12b73db5ad0c61efd66c1e29283", "tarball": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "fileCount": 20, "unpackedSize": 235198, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGYgWBvGgtEDWvyYrn00kJYCK8D0UCZEVYyCOYToyoUTAiBsZaKBJLypUDXZCZaRq3f0hbUDquj5NBmJX95feSPTPg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjsG1EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSEBAAl31BrAqhv7nx0GI6xZTmLpos6GmugbTl6ZTHWVih3SSYkkMs\r\nER4OCchqVYheF54SB6xG2FzptxWIthW3/i0+7/1nkm/Pj0bzVQ9dMIB4cS7O\r\nuHORhg7eHuxoSuaMamKZ5nYEKf38mOsB0B5rQUrafiLeK/MH0MHD2+u9SYCF\r\nAsiYxf14OtZsvQVGVYwLdFv2IjxidZCRnMtB6WF81fmCbmlgbO0hmv9Y7xCd\r\naysaZ+M+RE5fDVUdz2qj62oO2hYiODcng5Z+doMqVHOCb/rjaE0LFdK8OyNt\r\nRH8WKR2TMsAOorcQgbvj5df7JvFetdSTV9N/bGmsHf+u1um8OOGu2gCncgBl\r\nnL40jYyw1+Cjor8BpTLsvoZjPU0zuThgwJBmFx6MSkrWJtjXi4Ajzx2lwVXQ\r\nE8Ph1DL//D/TeYibBGKPJtGr2poOtUBEl1QvlHxpNatWRUjokLlyyUaVkZHG\r\nxZJTJoFFe5FN/yHZpV6/9R4o56kktUZiPh/xkfm6g/Nwu8J6pNLypClMRUt+\r\n9DoHy6gJchbXZ/f9rNmRKwX09Decf5rLxRBteWCuKc03GJzFLCs2AYcROuKk\r\nWe4jw0dYqAmRz1Z/ddDBHZFJR8Mca/TV6WjtRxH2KtidxXvbqgJ0euIhtxx4\r\nVOhM334ovfcgbtP97EjdwxO36Utk9DWT/pE=\r\n=PrQ7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}}, "modified": "2023-03-04T17:04:33.373Z", "cachedAt": 1747660589951}