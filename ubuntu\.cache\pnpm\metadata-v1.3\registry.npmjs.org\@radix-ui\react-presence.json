{"name": "@radix-ui/react-presence", "dist-tags": {"latest": "1.1.4", "next": "1.1.4-rc.1745339201309"}, "versions": {"0.0.1": {"name": "@radix-ui/react-presence", "version": "0.0.1", "dependencies": {"@xstate/fsm": "^1.5.1", "@radix-ui/react-utils": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "3c2137a90fd99c5ae0eb476a6d5e77045e780496", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-6NVYnSJS18ynRgg+z/nBAdjRRhzho2CDblyiDEw6DiMKOhjFGdFngcSVQuAK3ZBpG6km0k0rKgx9xOrbJFFm3Q==", "signatures": [{"sig": "MEYCIQCflJEQ6KsrgMAL+PY4L6YN+zLnXvrVvYtaHddwmCTTaAIhAL1AopRRz/TmAba0KTxHVJjfs7RVcUoFZS4kVZit4587", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbTCRA9TVsSAnZWagAAEncQAKR9dfngVr9GoTss4XOr\nGCssZ7eNDk/uW0n1YkWx4qy3xFLAGkcH+C4WneJr6FxjDVVspmzQd1rUCghh\nnUskP37sydCG/GBsXqO53eJMdSB2GSvV6+MuRn3SeDDYsPJfM4XJu9JdD3fF\ncSR/sQ5zYH3GnQkezf/CFVLXRco6INP1MI6uXSRFJuoZ4H1JVq921isVYLhL\nHfpM6v73sfiahHvwlNGGfZSPRYptFGtnpwmwZTlA3WzHP8Rh1Dves4oBejl/\nEhtJjE4Wr4awXeMEzh4qKKBHbXVySV+zQ7Qsi5M82nkx+QB4RRY6a/UvMLGy\nI1OqfT7FjyU6DmMos0zPyQJH+dO2rHDLiYWtZobuNd3FhdJbmD9/DgV2w/Kz\nApf97jCIk60jCRwCLSo7b2mKnJCjDka4+CJ/xvmKlFdsBILGEbn3NMk2jBfB\n+N/fwO5IGw9pmKcPSg4gxWQNpq/ZiKMMQ8+/o9FRyAGx8635YsnRQtji0Gao\nNKbHhpdRxJdjdvm2tIdv4JBBfRYYE3k61oq0WSKf//1RStAsH1L8/t5m3Txp\nfcc4FWwxv8uHT3pCPxGzUn8Hk1hNu4UUZ1Qmi3b4iPzdDGlAtXx8WyUKcmTr\nHN4yKNR3zVo8Y9vRJP9zbWckdGxgQ4u41IW5YY8/Vu7y9yVCZgTS0C34tnmu\nOnT1\r\n=RWGK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-presence", "version": "0.0.2", "dependencies": {"@xstate/fsm": "^1.5.1", "@radix-ui/react-utils": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "0866edbe062b24e4992bf3a58c90337d9acd05ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-VROEUBYjXiK23e54a7U5vHtyzXRHjZgkcLL3XfestpTl+POgeOcdxtWXfoBm2YPpEsadfINJHqMyOuSlrYG+Mw==", "signatures": [{"sig": "MEYCIQCSMIM4E/t+ItOgk4BKnHvGLRF7mOU72KDkKYLkIOA46QIhAMqK7Y7JpVgd4MEfO6nMbBKhyKUs6PqBbYKCfARif1Vx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvTCRA9TVsSAnZWagAAp98P/1ZSDMhMivONOTawvPtQ\n+BGInBZJk+zX4RG2VDpJN7vC6oxxV4yK6AbnhplHZMLCoWttFKBMu1+8NFQt\nClgXbvPRznsb1kvHHdcFlYu5KtlRCOQfvTqEb36rttaojY0aiSgjNEcY6fTE\nA0M4aGc5kO+LZQ9+NUwzs0g9YtnNFmAtgs1z42cMsugv4RQGf5ZChv/1MvCI\nsXWbkKzZvJXIWmNGIFCWUhe2+dQ4R0qygzf+2nYSxhkpbPuE0hJR0Hh0gECY\nv0yvB/oyjrDXjJFXcUQP9HNyUwYxxhadcSug+f7QCs1TMARhpgQpPnV6sCPg\nTQMwvzDVTL0jUi2BAqFhNLll4EqoE6xssV5idwsopL31LdBHH4jfIfzw+Yld\nFNviARkwyzZv26OprhXNHDX8UGpXuMbUcz02q6IsxXtt3PbtXYYqVJ2ZWUGC\n0u1u3QC84qrrH/y1Aqzzv320m/6t8Z5WSnQE3+k8K8AGde45pQPzvlMZv3q3\nCMTc7P920cylSD/1uyqBvUoXadyqqbRclWDCFeIX/m3H4ona6nFbtnp0LI9/\nH5kWsAu6zosUaNP/ST+KwCaI60nM8VEqVbRrZemuAAXtbUSdeTC4GriXTo97\nqWot9Rhd/3u4rmjM/6+V240SrHxS1IwYfLKGwLuKwMRVLdTUhuLxWXkL+7fi\nqS3C\r\n=ipnj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-presence", "version": "0.0.3", "dependencies": {"@xstate/fsm": "^1.5.1", "@radix-ui/react-utils": "0.0.3"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "eb1f1f9fcc432d190b118117221e601aa9a0e87e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-07kLCU5s8aDdhLqe2OsfX0BBCr5aEcCE/ynVvvMe0sWFwLc15KIoMCNWYpB4VnvOarjv7gGYJbEGkrq5u942GQ==", "signatures": [{"sig": "MEUCIHzAJ4ToKCW+d4RaLqXOVyUDE8lK0ctxrUdRv26A2oidAiEAv9mXG8McU3EsL2ar3hrcsDUY95EC4T8wV1dw2O/RH2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtSCRA9TVsSAnZWagAA/ywP+wcxbpLo1ChUYMtMWiyH\n4RAUk3jL9Y21WqTF4kkn1VwIC0zj0muWBk+hqQYj58LWkArOAw2dSpU/lNn6\niLMksURJcSlWjnWt4xu6l4gDJ4r5+MarkJ5IAJHqy2h17UWV4tJrHxE+nqb9\nYxuMv5qwi5zQ25VaMkO6VaqaoBDTvhr/OX1Gn97LNoQEOUw60c8cYFeOkgiR\nB32gG+hUBZCaI46Iw6SEdwYSpJUjbmd4L8OOlCU56GCToKepw2GfLm2JATLq\nPdRoy6LifRpvMo+a0Ohcvr8MpQjCY96fIkbl6Bf7ZWVXnIujqLYILcCuwpRE\nuiYrMVw9GjkQC8yUVZVBfc2OWGuc0ZCpHN6tIlFXox5R9YPeeLzFXJy6G+Xy\ny7OkpFJ59u/ijCQYHkKLACOe+P/ePj+/EmP6NsfIeI2doYlJklKdO5vL5h+B\nRrjynx4kGgtl3+jqdx9bFANE5uomGX7QgGthEX3wLWyfegqp6qkXLvof9oXt\nUhYVzrHPdXKKjRRNgQlfCk1yr/t47YuL5h0v9EUZgBfrKuwQcitGOzACrs/5\namboQ7PpsM2xA+b3xjEnuo1nPQeKoEEXCkLdRNBhGjNFu+IfFw4Ns3i58WhL\nLf82TYSS7X3xAEhROmf97QxQq/hP3ApRE6ZciarCfEisWspOgBcaI8QyhStt\nCZEE\r\n=Lrwr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-presence", "version": "0.0.4", "dependencies": {"@xstate/fsm": "^1.5.1", "@radix-ui/react-utils": "0.0.4"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "f557942065293bbc081f6e3e4230282560e95f82", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-6Cb5AZSb4pgXfNhdjBSCVZ4ib3Z8X6oxwZNLMyHNyCIltokNNl9WqMTO5jcXi/RqpFm8sBqQNZtCwnk59LhFEg==", "signatures": [{"sig": "MEUCID4xtodHUu642ojN1hj7mwSBHLSKhj5SOkbdeKOnF6MjAiEA0G73YtT9Ya9y2jmjVeLOgYAdvD9q23R8LzavAR+mwbE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAaCRA9TVsSAnZWagAAASwP/3wM78mXzjDw8C3EDE2n\nB2ZgkAshYaTAn1n7MBzWtaESHwhk35uI37cxCLEWuBTPRpHF9iiHl6IWroCW\nMQVplTqvrxZtR2ph/VNjNSmeNvxZ+sUTW8BArV2RPJfch9dst4zhlcG2DfZI\ndbpJhApF2ERrMBzsD5m4vB6iufhIVCgLczlxCsC8oEWRKax83AGwGKQ/gfpr\npdfAIhOuPeUtVPlnS3ycd79Hpj71wn2oRYDrPGjF5P6/GXttZpYnsVEPn86+\nK5rBivqZMV33u4FlcB+HWcw46pyxaYTZUf1utzhnkl6wAPXP5Ol99QUI2amX\nWmX7+2QTnwoay6e0EMcv2TdfG40dIju4qutvEfMM7mUBFIG6FEsoBmXMFnGw\n9qpEUOLVXd00Xhn94I0v2Eei3whFo3l+5Z3IskyWR2TkCAt/5EbJqtb044ie\n3CNx9RCvLSDDNilntpT3LnhCczljHZbesyR5XknzZuRvrk94vN3lo16wic/+\n3qY49uUdget22WjB8XFV7Z7iDq/93sZtflauO3xMEkbpVfqYoHSStK3EcbE1\nigXsB+xKgMInUZnSZny0X+j2w1mm+8FhJILyHD9XqH9J7WIYbWU2xXEiwtef\nmAk1wi70G2N8B7TSvZokz/YWb5JcxRCPKPvjtqAZakWZyCMWZuyiz++uMtBK\nZjbn\r\n=Dvyy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-presence", "version": "0.0.5", "dependencies": {"@xstate/fsm": "^1.5.1", "@radix-ui/react-utils": "0.0.5"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "18661555b388f2d00db88921adf4a8b8e5369bb2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-r2jpfsz3P15RXgz6EdoUfLhAZCiss95/CF2RYQhIRROWQ/cKfNc4FpnD1mQxYry/UULcZ8RZ4UcyBkxLzzbLnQ==", "signatures": [{"sig": "MEUCIQC9Ukq2lFMNrk8Pq0tpGl8Tviy9fwJLdRkQylb342A/BQIgDNPWrbNUlhZ9r/0y7qXsJ7yFPO2kAyfdvHnlkCibG5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V4CRA9TVsSAnZWagAAECAP/ic+lj6bd8l4LBqw0jzT\nDH/7FAxTbaIT9CN45++rqh5qr+Dz+/NG9a49CcxFSrVC8x+Gy7QcQfISkSr/\n1iwWczadNQvxRMn1vDo3Skh47OdHUhm2YDGUgftAh9tiFjCfMsK+w0dT8Tfl\nk0WKpz+Pjzzs2a0Ga+PhzOl8J8BqvvGdGwogN+dwLZtaD1lx8oqbIUC/6tsU\nUUgFG1GqRtqBkmoa4BHlW1cHxmErPO1GyTuxbFi6aRe8RcuX8dH1ty/ymQGQ\nafMalHfIlOfa8NGkQrhEMl4BYxO52WiVmaY0YcutPDziKA4dD4Ya0xcVImuU\nXc1/UuGdweYJcP54CazD7i9o14msqCyBqZEVWFWpsVOC6sgpI510rNsN78p8\nKhfkKyOfl5CzbnFym9qMtlbCGTWxl5sa6P4ZXKS//9WYCdEoF/hFYTTRD9gk\n/fBKTJA0QGVo2x6VR9K+wIWECkAuYujc+ccw7a9WSgNveQpoYTSCkP5y8Loq\nJff1AIYGho/FDVN784mElc3ZgTbvZGPzXJSa+eKAsQJTZdjk51BqaB1OtfjM\nVOZ7bVknHARz33XLSuw+ysUOWTdIr4XqVqE9I2be4WI0Vgdtwi750ndF7fJQ\nrq+SQFxhcP4QcaXOFC7RTZcQTwrR+PnkRMUIq7Nl2MAgOIwHZ2apzzNQ/utP\ntnDH\r\n=noJ3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-presence", "version": "0.0.6", "dependencies": {"@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "bbd7ddcf442db57c2ab21348413c2fad7a3d7ece", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-xa8XDHl9pJov/hmhCppwKUQtMkSB3GXIFHUPF7cW/LqC/SKf6go74cDHnxOceGczfA1dH9ORarZUd9uIEWAz0A==", "signatures": [{"sig": "MEQCIFgPeW9ndlid8/w9SO2vrDz9rHPz230MGDNgvPfe2tArAiA7+CZJciJJIkLH+SfVmYtCKt0lNq37ZPioJ4x+EWMqjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29687, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VRCRA9TVsSAnZWagAAAhoQAIclklzqJ7Y9laj51wb8\ntFHlHZ8QqXI7Ipy9zYvIaCA0TJ87D8cNbGX1gucPormuivmeuepu4Xtuud9N\ni03PCXOlDzDEHF4EaKESs8hRTEuORARqucZNhWnqY7IkXLtvt+Ta8BjbhHKj\nDDmSokrVtns1TWuTeIFzoB3DHXwU+VqjXFRqCztBg8Dh+VT0PvNzLG3kCpit\nU0RGkSVrVUqh000uj89lnpxxpOGZRZf43eo7Xf0B7lYgQV9EdGqIKfcQ1pVW\nNUiN8EwJF57pvwWCnIhKSVtmiAtl3+LVj7V2DSfdoqWVuXo95+mqr2QJTy09\n5BncpaO09Sm8kDw9c6DPJ2srXfL5XBn62lGbNJ2Xa0+vLWIiSqX7nW6P9Var\nidqC6j2SknsMLb3rRUXX+nZpGwRvGj3pntO0jECApA4XnPcSdKD2LJeUetL7\n+fUoBiDi5CPzFiYdc/3KTlYQFWbNs9v/SgtAYb8liRtZB9s8TviQdRNnw5qF\ntd/EdIZh0mHXIbt0rerwQ7kEc3pVUE4isrHMsggaZV9R/LJoSMkU/FawcqXP\ngzjbhyiYbyMpQoU1aWMgTPESp/u+j0Hrs2rwky8TdJEF/V7BuhhPD7pj+QV1\nhOssiQZRGK6MBofsyKa63kv0hNcdR3AVnsdo7Nr6lhBIWHNaMTpXcuaMHGv8\ni5hv\r\n=DTUa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-presence", "version": "0.0.7", "dependencies": {"@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "5b0d8535b4e725b9304a4ebb0141c37d4c5831c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-sDd7245YIL7aSS2XsxpKClhHrTx7hcsinoBDRXsyRrcTO2CHpnduuUjO7PzWaqRDZniOMFdFSP4j6878o/S6YA==", "signatures": [{"sig": "MEUCIBqH8OUpOkkDviRgwm1LtMBLILaGnEUTv+9BOI9PeeNOAiEAjyUPLXki8Yw2E044d9xy7q3LG2Q8gGB46CNKgqxlK/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmV6CRA9TVsSAnZWagAACYkP/006e5iPXev3U5tSyidE\n8s2vo1+CQ9rPdF6DHnbsQJNYdToDTCzOeKIcwXdmqumA/cusBSX7Ia2wK1e4\n686ad4kVHRAUt2uyfCl9I5xDxkOW9O0uRMeTtVqYOVRPWM4aq9DFeyl98pfH\nM014mU1fzkl9NqvD1vGEz9cWS+Fm1hB4mmmlYHg8Mo07DP5UQZSYLjfU9jpO\nW/OtMzicPmOAbWKtWVJ5TR6D3v3E+NMnj6jfAx1omzHahKkMtNY9QL95HL3V\nD8WR7+3Y86OUOU8eihAMy9seQW4axIAcT7sjILsMNQJaslxHm2PkZmRs4kJ0\nijd/M9gyzxOVQysuqV+ly+m+2/JPId8am1inbaW99LCLkYayscTVoD3OJkOK\nawvkj/Xl9HuO3lVfew36qZ1Bb8PDzymZG7eQwKkp8AjUVbLCAFY+1f7Cv0E3\nPD+XGg75+Atheypb8VyBKqpELewUaxZCQJlcD9Tx1v2BRxOWh2ftSTndBwyx\n8ps1Ry+8FuGAv1CzrdzmdEehIXg6ML+/Fu/cQebZtFXa+RVydPS113c0XX9A\naSYFDwDgWBA/aGVkj6UKZp0ZOOzplHv1BYmICsC5K0PClnsNTPSLtuQBoQcT\nzK+PoPdCHx/Hp7+mC2/3d00h9or5hRMD9+4dH82SIgaG3Zb98na7KX+XMn+8\nnQPI\r\n=zJLD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-presence", "version": "0.0.8", "dependencies": {"@radix-ui/react-compose-refs": "0.0.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "05a5eea07a8d443773c8a2ef057b5cdfbed6884c", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-898q/XFfXhTd5OGyC6bsBhqQYteT6l3YR1F5N0S4eW6BC/KEBxECuYYkFAcqMCtmc9mWzpRyA7esG27VL9fzNw==", "signatures": [{"sig": "MEUCIQDNX1CybOFF2nMMqMysUMYX59udprm4ocp2dAxdA1J6ZAIgRU/hJ/dPxRodHZdhSgreZ+iON1LIsvGcfgDyQGlKoWc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9qCRA9TVsSAnZWagAAOI0QAJpahErBi+Wci0LBN/m+\nzZDxh0SyMcizRjpO6jPlxeX9owWu6ujgphdWLzFllv5Dv0M37xFK7ulpOwHp\nYPZJPHh+GChhioKv17BD3NR5kajMLC9PAiSlvPqMj807bT59ah3T7HLPchq4\n0LDqvqo3hTRIWPx1itfnH+7BcvpzP0Ppl9NGiwFytWx5CrU2RhDv91rmGY+K\nY0FgF+FkHjIaSDzlIP6Bo+UW3cLNu8yRZcObtux6rL7H7TKsUGH8TsFY0/Bx\nmZW1kuRNAWSwh1Fsnsa129DB354MA0Gwo/4usWqun/Xbwb7YSi6Y/gXAOiUV\ngMfLiEw+RFGnPcqejEtgFI5+TC687IhQ8ChKFJKON6rr2vs/zqYkrLPY9+J4\nZbAmJieIvZ0o/uzN1do1Gu83IG5xyEmxr1oN8KuuRmaMdcam9aWKbqVFJM3u\nL5gNkKXvUnzos9+RwAIVREb4MmJMvPVD7Zrl73XENhw5AOE8QYxUIKfhTUG/\nbBGSGM+2g9ld7AZcuPN+tX5pNFVPTclbaCKcLftGdt3/i/hRTM496Y4peCPh\niGL3JzFZ/Wxj8cELzVCf7rZmRe6Jt5PhcAOnfuvC2tfabpbz8C/RjNOu1XUK\nAHIzKSRG6WPQywEFSCcde7YBTnPzD1COL+FSf2F3qXTzngzQOqDIkFiTV2aU\nvA2I\r\n=7bNw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-presence", "version": "0.0.9", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "848c5626bd8cb7dd82a7bda3b072082f15955a5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-RLYZmCy+GKU6aG5vkDZ7LnPqdH7LgmiOjU11hTz05UlzESRsIHaEIgz+NVyIsOq0AZtuYOT69Cwnm747DpmKmA==", "signatures": [{"sig": "MEUCIQD/sQ5nMudyitlSYrll/nK9MbhKGD3+GTPBHmkXb8z58AIgcFnzgl89U4KmV79VH6nhhzoF94jH9I+Kj1kPlawKTFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmO6CRA9TVsSAnZWagAAN8AP/i1Mj3iOutt6mU9DKQsV\nE0dLn24QUMDiO0wQIFfYwCG3seIwvF2pVtcCEMFUkZwup2yZFAHnOmTMOwEM\n59eojdsGFXhdMtauQW0ybUHzkfuS3auj7HoLkQwiu7QNoofGhxM4uIHr/dr9\nKoFShRY05wexZz2Uy91hGbASJtWQXILbRzyP4OBelbbFjtZBm9FpXyzEbwCF\nQHsuo9hWOcGvhYw3lxjWMVQ235SFFLE5i1BzS7W9am8EgMBEIWQhoPV0M2NX\nbFmYoDgELh66QsU1cSlOREOGmlYGLE2PH9tEM6+kcTomY1e3ZY0nl6+z8J4f\nk/9S5rFkvmbSo3zrIJ5/NsbKcS5dvFkhfoOuOY1X1RU9544bFvI8o4DV/Yjd\nsnNoGUtOlXF5C6qOdK8kzfL3t1yDgh5rErpDVIc7RQsYr1YFQeVg8ZbDKIgo\nn0Zz1vAa2zuJGZX0gn+95oDvBXnr5egyUDwPZDrEY2K3DfgWZqyTdXgvlZ4K\nGp+Xs6MUqibXVTu3PmmEr65dbc7EMCe04S1E9JB3YqlgLopTP2L+21LVnELO\nVju+7V8QU8F/J2yFAcjDxYXNJ2bZqK//0F4H1Nbk26uQEN7G5rmwIdFVM2wy\nyl3W1jciCutpBf0URF+swHJicg5tkkr7L+NNGffojfN6IV0TpU8LJNztj40O\neZjP\r\n=kA/S\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.10": {"name": "@radix-ui/react-presence", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "94da5a14ee85950eae4646e35ce43b678b7807c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-Azu6sN9PmMi0BS/QZs4qCKtieLp8DPjn/PO/W5XgG9HF+iQnSq91B3Ysaowxa493Dq0YkEY3Dl02iU2le2tYpw==", "signatures": [{"sig": "MEYCIQDIrUNy0+jRhU1MuE6jhDj+viCQYR7Gv8wkCxngJ7QZDgIhALwDCz5W0ArLgMMeTRx7tFzUMzJ7dTS7fxy1WHN8mTuF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gtCRA9TVsSAnZWagAAJPQP/1f2H6J1hOI8S3rsJvQD\neJUxkeBq8+i5ArnlBkrr/rrzJH21pG/mfTOK7b+tSQkbeQnpBUCWw6jDjaq/\nCoQWrFVVv+X0vqhgJr5Bh/doB1ejcYoixUdJmKmMFdJ6OU5IyFASTBdJKRWu\n6tZ0uOtkaGUiwWLKRnlIOY4FnjdUo6g/qidg60AGzmZ8cWbjqBiia0t0kiPD\n5b1S8UFntV/jtACl7RT/MmfPNskM4/7y2R88nsab9LraEH99rRhEy7Uv6/8c\nGldV+4o+oT14mmUbFMO2uDC7upBDMIIEjoDwqfmDU1YET66ilM3KGZPv/5Am\nltC7JhwIfRf+pZ1vj2XiUNtBgBafQJbRIRoWzkO7/+9TCDM1PkkmQ6BcT8Iw\nvEJ6Lcvl3sDQf6kufWDSnsInDeprn49AuinQlSq18MXajWnLzTm6LXHya4AG\ndOidedvSzAh3dwxwHERkAIns218ZoofX3tbB6Dxa5V/Xghn1WCjsupCV8MxU\nvpc723bmQLRwy40jg21nx7jUGJMa19y8+pDlbKe7p/wpXrEP+eNVeylZewl7\nO2d9lvfvDMdj7HeacRwttdNuWSnOfCjM1UmanHQuK/FtRpfkULN+lxwsJAO/\n0+3iQcaKh9aC5H5znUl07Jd122lNgu+wdo+dpmyZXVO6OYjMgDIl5PrgPhz/\nOCJF\r\n=/Pyj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-presence", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.3"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "6f2b73e6e2799b9cce6d1554ecdf2f34f62410f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-v/lsMDLuK6cdXryDU3ZThe8VQWr3qJkNAAzWN0X1zbsKA0sdvt3CKzc4L/ontOqvG6YuOmxD/zjVdC32+XwobQ==", "signatures": [{"sig": "MEYCIQCvxrUGTRBdPOCgIRhFOyj5QtJD+LNMjy8k1/pkv++F6gIhAMreSSNJ3NZgKUGdXFvwy0I+Yqk+Bw0hifJ+pCa/MFM5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23669, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H8CRA9TVsSAnZWagAAaiEP/RhtKWTpYP7mvnquHoUu\no/orjqHJdKMX+BSNoxlMx4BaFHh4Udv8iCFkvh82rQy8eHXWsUQvXVvn9Ccx\nNZzI61Rfas8C+B2q6ow9jZVjvJsykRmPDJLJBpVVsIsxTG1WihCFuHMK4BBd\nGMfMXLw9wpEVxxdp6gz7L4WS1Qky7HAI1XB5r5atSc191ioJc+UmOlyCzrzd\nczaXulh0eV/zIaiAkRwTwlr1zgvF8MRJZLNPedDtluSzk7ONzwHWRZyJSDbh\n6K5gelUNJV3G4vajH1Dp/2spltzsCFYxBpa15urgkYVRe1LzVHc3Qraeobj3\nQ7rpBBc8Ctm3XGIcD3OdEjBRs1wt+WFscNCzsez7Wfvl8vZQU+Oo2gwkYqyj\nKYF64Z00WqV+dtITOKFpQ3Nl5R53GdtDq4dm7luoV9UlFGq0FRcryNz/BH1v\n5MH4NNbT/ertcx7pvGiq+WMZC7UUlu81GJ+fNC2eLvbechCGMq5h61vQX9vM\nsByP3bPl7pQ+wx8QStGY541wHAuzcKEcVlvRruIHNumDG0MEcZTIgjwr408I\ne5oU6SOetahlrj/9DGMXp00AzU7aq0coMzIdYzpTbm158YI68nmowp74Gshg\nma4FdjN6YgrS4dx2gwqaCw4+/KSdJ1p3lF9wIBOJXhxXJaBi3Dph3x97f/9G\nJuJW\r\n=klL4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-presence", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.4"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "42ab21e898c859d350574b029ffc7acb234f36e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-iO428pb2Ko7O2uFMZEnj0l3loPbB2s8NExZhDyjgkTjFwhLHv7XtncG+8bGN2xDhWLCr7KttLq4W3Tjrzd6Cyg==", "signatures": [{"sig": "MEUCIHVEtq0/8xeYhu5UYfPAGjz/aTIewbsGgwlp/EQG2eqoAiEAyVdUPN73N0r/PyRjQm/zjEdNtQuurlPdpefIda9zqJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v0CRA9TVsSAnZWagAA3UcQAIOkWrCXSwLkggbj4HdE\nPKaNOydACC3Me1xOWQCPh1iRB0yEj7Iuwcu+2VAo1Xqq4s4kwTrDFtNV7ZLJ\nvA7lqawgYt+XLXcabdS9lHuDVo92cGk8IhxrGo72Q364M05moIx3C7sd/I+u\n/olJdqQ2nLIbk1Fp4ZvVTCMZ65UVr/qd5QkCGdsAG9ob/gPOUjC0+RJMDwaR\nFQ4ndUUrlX42OlfHceD6Cwhwcd+XE3oSoXb05MuBm6KJDdYPI1CYCK/sK3Z3\nyZfnquBl3Fihdc9L7bN07KUerosKVmllFZ23l7II9htgnmyDgz8O/sp2CDfl\n4zII6l2Bhqc4F5ax3GEM1FAB+iK1kN+uKt8UW+jTsPBaN6/YLWCuZaIigEls\ncG5LXtO36N++CQqjMUKuOb42/tEaTNdv8Dm6FDIrbpLS3t+IhFmTjBCv7RLU\nomrwxh1CNUMzFbF2ELVwZVH+ri6CI2ut8i+dX9RJ/cpHAzEvvg2YbcBAxJZB\nNBHPxznbpuje2Et6qL9JlWDuhNnSzy9PxKOHBTNChEeP6GJk+ApiMdFmhihi\n8OXk1MP2JTJHZZECCOWJ7XFZKQy0yxC83xHbWXMdePm+xKfiumaba5IirbAY\nNx7IgfQJEME0XEQuSnQcS0bxheElYJenEFb4Ny5tTi2lRRyqAs5+PUrV2kqE\ncNeD\r\n=evv9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-presence", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "ac66f2f8b509440031b9dd015cfe9d793011efad", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-VpHYMYIRdOO6ic0Pp3QeFLrwQGepAGP8G8pnkGKVO9pSwNQQsHjqzbLkV9l4IaITz25u000POnnxvTOlEgPXEw==", "signatures": [{"sig": "MEQCIA+8zAKgOBsmlMBfP/9tWUHriusKqFm2mfA98dGuLYC5AiBfbCnpbMhi87Ubva7AfvTVgSZpUJdFROiFSQ9IDglFng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmqCRA9TVsSAnZWagAATFAP/3iycd0/dKLU3KT+yXSs\nlE6w4DBo5zI1twDs0ZhOlpp71i6lmrUtY2qIthJmEgIxLNJCFXjfkXJBBq7s\nPdNd8fZCx+x4l73VIYdnTPktws/f9LgpLOpg38PwObUfHPO+JVbZf9sRmG53\ndUGL4q+ERAipqJ2kj++s8Rq6ZolVJUJ2Aa2FKANQEoWO0hQ7MTzNhG2vMDeM\nMAD/ENZzWNF1FBIpiNp8agYc3qqtz1H6TZbPmtptxuaBtt6FzL8oWj5CcGov\nfUsKhIyQBKhDKir5lG9GClmCtOZti+wn9GrOKYcSIvt1uEW68ntMkO+++waI\nXumb99N4Y1C7wVXAXjxd79WBOUwvxE6SwofFV6ncI3JmfaAbO7Ikys2ia/9U\naIdukyKjxujLlEVqfhEGwOjebK+pLJUOkv6lxOWCPpVjq4N38+WU3pofPmFL\nXACfnIQF93AoviJSOJgZ5xLPHVJQ9Zs12Zd06QAadimCt2aTNpvVZwOvvFQJ\nUrd+dNZk6VP7AtCyosT2bdILEB6wjx1mn8URsi8VWKqog8iELx7JvIEI4Q0Q\np3lDRprTahrfvQPDpnSfx/6IF1drNV8awOzzBw4WqvxuJNVnk3+yYboJHAPz\n66TQm6i65zxoYwh6w0dIVIphoBhO4nGi5ZzcgFrtfkwsamFc7hrFc1w67j87\nud2m\r\n=dSRp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-presence", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.5"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "6a86058bbbf46234dd8840dacd620b3ac5797025", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-ufof9B76DHXV0sC8H7Lswh2AepdJFG8qEtF32JWrbA9N1bl2Jnf9px76KsagyC0MA8crGEZO5A96wizGuSgGWQ==", "signatures": [{"sig": "MEYCIQDuwRuE78w6za2k/nthC30TdML+TDiVgBizJSftO+uRIAIhANzuIQnDHe5Zilxw1t0wXqyHY6j83b133ajbP4qEA/A/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYACRA9TVsSAnZWagAAhAoP/1iTDZGyGXjo6wlxqg1e\nh8hWRKEiu1d2x8pEQw3yutU3JeLc2wgVMdhiJBdwg1KeKsjpmse95DU+5yXM\n225d0RUmBVOMugwzDDuPNLqUbstu2Fypshi6gpBlQCW6K79ZcGDvop+Aiysi\nthPYM1Z5xPFPvALftI/nKpiMutH92tJc8CnWSLeNTPIlRP035cQUzUi6KiA6\nszHVaammMEPkF9WfE8udoW3zAEmcmqfaBJLkshiCqqgjzKOGSgIJG7Y3Pf71\nl3MiSbDXDQ/dRagUbmHkTyUOSNsuy3xNLC9Ja/PcNpVQ9RwuebJCiVROZx0W\niVfW5btUAWL6TwngBLupvnqGu26HevolrPsWqWgE1A2nGqWbSBiunScE2GJE\n9PiOE4uBq9jzdfHTSbnihdmDUMUY9iAc6e6j6uNYhF/Ff16jYc8eizFxIc+p\n0SpNyTUe2Qy0HdiMbUQv/ch3jg3VoopDljObVYJJxBq2JcfFzJJ9uI+h7dEF\nlBaupOU10nonFEkXiOAkk40jA74CitVGmnTiNaQvM48hrLWgTd7naaEZwySB\nnW7OIe+iPJ+WCzyIzodZUgE2IFBcuNX2H/R39nlq2n+vkI+TXUt+reHyvqRY\nwHAH2gUdTmfyMEuBVq4GWQPKoMaiwpH9aILUhV7jy2PSF0K9I5XowCVJOvWj\nw0Ut\r\n=dzUu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-presence", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "4ff12feb436f1499148feb11c3a63a5d8fab568a", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-+5+ePKUdTkqN1ze7nYmcoeHSsmKCcREwt0NhvNgDocPaqEUoZSkK9Mq6eMiMXSj02NkXH9P+bK32VCClYFnMBQ==", "signatures": [{"sig": "MEUCIQCdJDEFOwqEjkTRaJY8SYXfmlTgoNG6VONAZ66wnCZzNAIgDkrsp/DysgnWORGTQR0BfyNnxvPrK7LmKFHemWvQ1Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTuCRA9TVsSAnZWagAAXcQP/ju+aQBLNATO3Sv40oUh\n4WqXqsED+erzC0nRIOWNjPMwBgsOEt2Sa7uxv32rFVy1F7bo0KkzvtC9lFrp\nLS4Ersk1002CI+TDi5Mq6lpqeJJqhNtNAfxIa/sMRWksPG12iWZgPabVYJ0X\n1glznrDqoYjNH1G3DMcdVVwtbt2t5zuLnBjQlCzgtljjC7ZtGJi0MVf5TdzP\nCGyp3gD9Jmcr1kI0sCV6QQOKYBy1DzR9k63GnW1RV0ZvM7n8l9QBaL4NCwbV\nZFpBYsUbi+GWCoAdkQudJux+AnUUBpXUMdGByoADOPoHMCxZfBP3QkOZXCs7\nUzl05p8awTfQ8WGkB5i/Yobd2AzEj+Rg6xXYa0fdRpH34nwXPnP3QunlUftl\nL4DQLSV54a2qcx7C9jC+PKBsgd5yT7Vw69RrWNUe/5bc+SIBiB2H8jbMOhcy\nOI6KTNf/17+Uw3JC/YgwsYZp4IVQsmamjR7cufCqLabV+5eAJGZuCgWF6d4B\nR4xWv+MIYVbakzmKAodTd/DhnDCR4O1Yno4g4BdpaAUhH/Hw7O5BxN2/u61b\nAcftmTpgw0Y6ITCQZf9DERFBBwoVBFIpeU4tnQmGkQ01ALyBPM9tAgSvcKMd\nqkBGsthtg1ndG6ZrsqtsL7kut0haxiOt3dlHJYwB9+Gs8n4atRHcZFngJzE1\nlhxi\r\n=icod\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-presence", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "03c05a35aaf530f5c8a53075da804de4e06055c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-rOBVOHNvBBcrw4utb4MRtO2ggXYUhtgiSA7n2gpJKAjHyQdfiJB70rApnAzG5vGxHNg5RR5C40/ZceGnFqK3zQ==", "signatures": [{"sig": "MEYCIQC1VD8z/KZVwKDbanNqaeoLQapV+gwHCtJb2oIelNlkPwIhAJUx/7GZfd53FCFu8mTwxmGM/VGaQ9TP34oXi+ZHzg7P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpiCRA9TVsSAnZWagAAtR8P/A7qfTJ+j0F/+DOr0hlU\n4MII5TgbD2SxRhhJRcACYH0bOCblgOZ6btQgOq1LDCmEeTsf245NBIcMOybR\nWhRAmcUhwiep2DzAuK9Ngp1KYWl6oCVG96qgyRm/AZD6v80R/Id1EQenxiro\nlIHozQXVSyeAwzdEeHsd/qXg4V4sTHNob2jqozqrvdnCkYn2j6kdQ6mgNogm\nNZ2/Ij6UMUEuBTf+xlnWZ/4LnyZmvLv2eUBNSUuqyjdNo9HBSUKSjSdIUZBT\npfzBNBez0OfdHEv90W27POSaKZwaxjAB5GiMEe+XL2kLh1/cKc2o+xjAeK47\ndYE60ycPEZa+bTqlMfZJhNelzYIb3UIPhTRV/NCz0IAMwl3gfxjZ0wbIW7FX\nxswvu6WNRpZOeMHcnFWFRkd57rq3Ev3Ozy1kehI0wAwEIYvkwkswKUb8HUAw\n1AHHA5lWYi6QWNKJKsF/0OPD5WtoQBfxeV8e+WD1ITQYqYhiC1wWPNhiHcuQ\nZ9bMMQ39wl4+Y6x/oaQu4dbEWkVmewYvbVdei1lMakYC7dZjpfBZL8S4hNXW\nywMvkkmW5s2mmSHeEq7ySBsnIYl7BB1bsY8G0gwmQ4dTEsq6+2B0YGaF8JV1\nRJTrc9njkN/EMJEiIHtZ4LBBG5foOwBy6KiBFNb2apfYvvGBsb17EyWhSYQY\njjwD\r\n=jttr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-presence", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "08cdd09d59c063896825704335f2cd6780501c93", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-HpD19cjFgz5+4JSDyXzMoatU2l2rLQCIBJRYuXKgSJOKFYJVEz8+AxcTTrGx12HQn3eR/rA85b2KTJuQ1dAF/g==", "signatures": [{"sig": "MEUCIQDSfkbk7fdnhcArCH5A36dzPWKiFI4gpIC9UEli4779qwIgNqK2lUPDLPm3IEnUbWXJ8Bs3aaU20qj6S9QZh85oPIE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyXCRA9TVsSAnZWagAAtlcP/0wXP2am2rrmX/2dyJZq\ncV02pLqXL04kGad8OagloMjm4iu709rLewdvTAOtMMnetz9vCXshg0eTsjqY\nARI/bfK9U5ad71Mn21NdJl/fSsDN94g4aSUyGhZsAX1vW3C5f8TIDdVrrq8N\njatuqcf5c/w2X3kTqXcYrvsuaIW5kg6a6828aCHtEZcnyzXyE84ASuzQ7Iop\n4BpDTlI/zDQ0/vrh8oLHWKWm+pPNR2le528Wcw/+OZATos55jV7L1DaZOwei\no1PssHz+tAOwVdT4cljyUKZa4dtRtQjlpslOzYKVYbKrEQ0zmF6IF7J0iiGK\nfKL4d1V5jSiBl7I5LWFKKCX0YgFaEg2wEpFb+os5UJbOey7raquGl/gD9vCp\n5S7UyGW+Rgfn75kEYEY6B8yXcn6AgaGx1m/f40kHIdsAJhwh4oN+m/RD1B64\nDEBSlnVxay2vnaDUEVUw4WFsfz/Jvy1Uc0WYaa0fKIoeeH4awMRgWFy0NFHw\nYh6/Fs92xdXQ608wJSCk7mSSV7NS0NcDX222pJXZIhaTOgjsFBkMDOUL+Tt9\nfzm7AmfXO7geM+ecY9c4PS8xbOojYSmfe1C6RSVyXQsRq3qXq+bCufW7JTVt\nWIKRSivkD+LMgizScbAK36fnZwnsRvDDJAVpP8bsoAwNIfwPkQsb45pdGBsa\nrZsh\r\n=a3jj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-presence", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "e7931009cbaa383f17be7d9863da9f0424efae7b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-MIj5dywsSB1mWi7f9EqsxNoR5hfIScquYANbMdRmzxqNQzq2UrO8GEhOMXPo99YssdfpK9d0Pc9nLNwsFyq5Eg==", "signatures": [{"sig": "MEYCIQCp8r4UruKR/NCCxXv8jRBj8wEedjDP/W095b8ACvb1zgIhALGmil3MLxs9RduiMp40BPS7HhknB1uZ1R/QNxG2bxHd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmlCRA9TVsSAnZWagAAjOgP/16mzA42ojwpip6tbtsr\n9UKpJB3ofu3F+giCkRj+F0CMwGe2vbSmazZHGWS/WNCovhbTAK5am9wO34tM\n7316KgOQd2cOUuKZGIROtmag1xOYiASLjmeVdKIAWT12G4DP8PkDvjVNJq4j\n1DPKwyqu/a6x1W3wxWhTWUlNxEdSHD955Gn9bKY71lC7ssEFSqI/VanIj9tR\nzS0xaOBwMiNjf0inQAXsfK5mERpOMSgVeCNVH9QrgI/tPogfYE7SxRd1KQZZ\nj6AG2Ai3jrKXI21oykG9PjHXLvokz0kHwwhw6tvwBOviWQwZy7Y3EWA7qXL2\nQz9yAmVzF9WR8HsGDkD7j+zSH/PHA2WcVrmj+os+UIII4FIwq7vBDs6j6NgU\nsNMz2fjg9AB5l6FJM+uq7kmh2VqPUHneRysvbi7gn7XBGEkv4jrhwb+eTes0\n1w637ol00R3bW7YlhJLSgya1LsSmH7QgYz4Mj2PrWKklfjXRPeXehYtEAkoW\nUFYqy9sI4p0BUmOIMyZbzehmw3mUlIXm3F0z/mxPH5RJqXVgrzjRLe+jokb5\nLZoYtHXcO7yojZUuW7Wuis0j45HoNFo62OT75Udq2OGi3lEw/bVM/ueiL3ta\ntQP07yDqw9/UKnj/gwI/hbOyagV6+0pInx3IQGVcbEohFFRXCKGWgCtyU4mt\n0SBR\r\n=2P7r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "fa2135db53be65d1165e0862df0649fb9ffda0a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nTkHozM1pUcnY7fJ5HHKYjrk+9Gw4Cg2wfn8YSfIymAbd6Qrq2n1aDafmpFeasuVwQeAZGniyxBkGL1pmLP7ww==", "signatures": [{"sig": "MEQCIHW/hUkp90TLtifECPIScgVs04s/hpuYiwxQGYYWnJfcAiBBjY1CtqYrga4Gi68XFb9/luluuqQAnGX9cfrMVCJoTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1z4CRA9TVsSAnZWagAA7IoP/AjBVsPWjeaJXyYwLBeQ\nyF8vphBHBT0K9miWZFvJO8I+qV0dsAY+kuCqrF+97+rNgExJOJ2CuWTHGoJR\npS9b0XNOuvWu7ZCHiWIcOUJ5IR8e66I9mk97dzLVjlmlsLmDuFrdaELfLmPy\n6yMJ42FKILM68ivj5Jm0TCH6jD9poaRUltMGp5JM4Ojbz+cjZ5q9zYCWIsaF\nC9DG3wgdkx35de3y6oHY1B7+gRHSlFM7ZsP4wfObjHKKGoMv9cmp3nsbUIQz\nS1n9OU/3pC+UJgP/IMu4rPvQXUW9CaEQxJfN60Uw+iiJass4zJG2CxfwfCEK\nTlwwR+BFjzWrRMxD/zQ1ksrBsoNQSA3hZEJQr9luZKH/xd8m2xpnpQnRB6gp\n3AKThZiTS3+1Bb7m9KiIm3u4gDMX/UGxNNoQ/w4lx+TCZzUh3S1UciBhg150\nZTde/0dZrLkNrQ9GwZdQfIEvOSQQa59ln/kazoQhAkyvM5LeBnggBQqX73NV\nZ88S/8882n58isNs91NmW56YQS7B133btHJ0USyBo3U6bHlZk0CepJdE6HMu\nVsOx6nAs8JMtI5dx6OuJTpxBXE6Vjs5fNQ/B7JZctkDP05WTIPveGBxLkH+D\nAigPy2BX2xex6KzFYNaMLCxoo7bwtfSlYLTGBa1hXG7YNTtgBPiiNPD9S8TY\n9H86\r\n=8uua\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "1757ec7161c828c3b17ef6fb8ab9184e893a966e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-z2tf2E/Yw0bQv6fcm/3qXmNnTjEeUEwTY9IR2XT/N2NYnpV9zeCQ9tqNL9Zug5IPeihLo3cZgGOGtYFcbHxWiA==", "signatures": [{"sig": "MEQCIFBy90GRiaV5ZLiw1zOIQkG5YHi0Ju1V6R0baeIjPQafAiA6yg21TDry13hK3mzcjWFGHzyV5TlVQh0ab7VC43FOdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkKCRA9TVsSAnZWagAAVsoP/jHfdzQP6HwoCy/CTNPd\nY2JpPHERJczFouqsWSyp/jpORL2kb6Aq417XYWkF3r2frXnJPIm3eNRM1J/4\nD89wAX//2VUcLIiKnMNw0TLEW/pmspu1Lf9v79HpvJ5384rz2w/d6gMSeW9L\nBjphYgPt9uzRHw3X5vdmDDoYOZW9FDF2OBRPOJmaKwFLwSrrmjZMK03GDvy+\nx8jZ1XfMhsr6wPBwKbEiRWehONHEj43KZyXv78UyQDoowWb5WZML3sQKchWe\noDcfc9VRiGG7r2eq/i9PCwX8XC4FIuYrV0dsrBXaxDlYcfTudqighC59N8e6\nntE8HpKztGBsDs9oDohAaBz7Yna+hUlk+IGFr4XY1hVgVIKR7JHOYg7vHRkd\njBOvsUDjVa2u91FneJpLTimTSNrbmGB2C07ITd7Ig5cBxhok11FvpYXUEKYJ\nnC6DuSswacjCFDE+AyENxMfPQmf93iMkqkH7KiJR/iOKWvfDjlD9N3SVamtu\nW8WudmRZoNAA/lg2glzsUqSkd9dOFH4N0KxtJBRfsFh+ELfmlZ/rBcGP2pNh\nos0grHS1JDw70bXmB7RQq77AIkywO0dfQXzNrTz9iIZAX2NKYaujjusSWRHy\nNRjB4AiSwvDeGBOm2O5KqpLtFXurL2H2w+zIYFi5Pf3Gk3U3fEPVJBQ1BPsw\nbnNQ\r\n=529Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "1ac728c94ec33ec561b45bc64da62dda5f5e2d37", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-+tqDyml8iBCnMSLVgx0Q+1IxFqKqZ36LPJDAMfFcErqU0rPUlPlSPnZpfl4CMby5OPaBxkS5Lh/kbAc5pKy6RQ==", "signatures": [{"sig": "MEQCIGsxMWHwJAYvFuxwH+0S3OmW+uejI5X4Pmf51WbJWIj2AiByMLWAaZqF7NppY8RXOYKGVzM5CweX2pHC27ck3AHquQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.4": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "d50f0ca97a95b623995bdea9da887b1e2a19c2c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-NN9jZ+/z+MZgIyl5v+TCuWmnT0rwkuvcRxUufhLsyPA02wknZI31p2O3qXKAvy1JppZaq466Cl3JMleKOVXjmw==", "signatures": [{"sig": "MEYCIQDuWZodQezYon1NpISFqUB0qsGxq36fmZN47zpXkf9R1AIhAI6ZQ5aYlqUwt8J9u4FnSV6YNsB2r+qGAEgctbbnltwg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.5": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "fe357aa70603bba4ac9430dd37135ddccce44ee4", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-xEfXU/+PmbS3ZxfPq7ZLQebsKaEfEHjmt9SzXYvL7Y8/vZU+6ElIMcJQ1PbDi9zaGoObnjO7XohO6uUUPP+uCw==", "signatures": [{"sig": "MEYCIQCOX1HqweFnPLT6GKZ23IaWzIqOaOMTaAbxdujXnceqDQIhALlCkwoKqzw4BTZ5UeCCcQh/I1RRkkp6IuFaiBlawsl2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.6": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "9920b7ff721e92079a15ee9aad9a0948eefbd4f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-K9/kuIWKKaBI/x8VV6J7AvJnATf38j8urlz0KG4Uq/ikIw7Z8m19Gx3sm8FK3Qon4mOpi6ijZtV8QHeY1luw5Q==", "signatures": [{"sig": "MEYCIQCbL2vVkAN1sZvnpxT1VVCc+ysmsULLaNLPJi/rclCGAwIhALdGb3xRprS6F3dsuo/Ci2OGhZr3u9KZpnAiHPnqOk0O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.7": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "59874d0c8256d6b51c5a23e03d554eb3d5ec07d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-sfvAE81xNQX6UanNteBmis5kYPevWhlPd5N0x6Ed/CxpVerq13UQdXwV2FgkN0XnNlpRI3MmXOfhIRx5Zh+D6A==", "signatures": [{"sig": "MEUCIQCKivzJLQS7vOVIsMBuMoNChgFfd6ObDRGpjq4l73wrNAIgRignkNYDRneXTNMhqS5TyRRS5QNBTNHtWNIjU0n9SJA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.8": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "a2542710c0a200729f8e3e075e928240ebb08990", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-5uvQofVzcGzKGVeWScIc7hRJbLyafWCnRE4I4Ko+YIa2LYiD8Pb6vBBL2A0IDrVWcG/hA7BgQoGF585v0XtGOg==", "signatures": [{"sig": "MEYCIQDgLkAaU6JvakiGTW/UN7NkqibhtnLYuxqPYOiQ4spTbQIhAPGs1YdbuALNMa6rU8qd/Zzvj3MP3DwU20+RlqW58cua", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.9": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "4c7426a202cb29f91bb13f089a8466c899d70c3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-EmzZC6leDRnXSDCdUokzmFlrXOtO9M4yfSPPEUnwUa02lRPBzWGA4vvfYLI9WppS7QP8vFJT32ZyfFgI6+iffw==", "signatures": [{"sig": "MEYCIQDUAkdxEXC+9KmLgRcwKOs/ba0QjnAJXqHx5R1x0XsmagIhAM0hFe4Dkp9zFSzLck2RqyGt6fV+NAiL3E0Kvu14pHME", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24102}}, "0.1.1-rc.10": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "897ab64c15966557765d1adc54a854fad31b8558", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-eNT1g88KiQtf+DqN7xmWuFMCAHGKjl4YDiA3mfJpXXCTrXxhfrJId3fXAmEf4I7ddc2D7OWQ2hPf7F7toJ68Eg==", "signatures": [{"sig": "MEYCIQDYn6TR+EqbTa2lUxZ0500FPhk6U3iN37b/NTKYBwDk2gIhALNYz+puEHXUuIBhFc3DYsL3lskmrw1Y+7+XLdWu+f/C", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.11": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "0fb3482b9b9e2e13011007b4b43b3ce4eaf8731f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-IyT2JSFqxDLB6p8hNS0+G6vbWllKRzQo8biS+JpuxGdP/HpDP1wd2GHldsH0U5H8GtSDGmOUSf0QPZWFp8qrnA==", "signatures": [{"sig": "MEYCIQDf1r3Dpvbgxzl7bQACVkeeYasgcS8ogUm0tUu4gcDtqQIhAIEh9flLKfAfpWk4X7LBB57bMW3Gbyz7jOsyEqTZ1vem", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.12": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "b0d50a9fb34b64192479649d64fa487a627a7f2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-nlS9ghncVy+huiWD+ThenWKVcULAf4mfU0p6WQ9QMZ+hvxfX1WLIxqAsnbfm/Iwjz/9s36gbkOuDO8b/VEXzhw==", "signatures": [{"sig": "MEUCIEkvnzVeOqsn4ke+h0iOnpCiQHNgGBv8NIy2VdqXiteTAiEAnzF1eLfWvzPk9vjyjpy17z+gZ6hJQnkHIYhALF7mHps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.13": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "a416bc54c9160d629e588e3873df7715aaeeee97", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-i5afO5UCE6FkD+4EQIOtx9/Qwbp45CsVt5Ax+LbJJpwm0PytZ+Rt1gRgoEM5uWlZgpcCpXITmYCVxnF6RmyAww==", "signatures": [{"sig": "MEQCIFjxy5dwHer2I2pRrdpC2EVfrIGRd+RJEvgiqT0ZD4tQAiA7yzpGvW0GaLGXYn7Tqk0Bg4eYXsDhNxRtx1mkxgsBvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.14": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "da53e56b6d41acbdedd783e308d8d428775a5185", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-tUwlp0UW8YV6lcg7cKEIDCUhE5qF4MRBGJ6qKEHVWozq/pxxRXvWAtzmRfC/jQUeYAn9Vm/xv8KLIirAtuBM3w==", "signatures": [{"sig": "MEYCIQDoFSOX/h7trkLVyA1xDpQQzFwTj4yxMHMqCSyKBVlwmAIhAMB2AHdnjKXHgl16+CbtZiEJZDgivD42QP4zia19SPFq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.15": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "648bb9d317cd73384d30a27ec98cca6d6b8b9958", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-NseARVi4Ltxk1FdDwbFIMeybuskH1wuYl7Bwmx2eTnL+LFUCJmZZvzH/Hu8AvwQ8lefkhLuIiYb3m/0cLhvopA==", "signatures": [{"sig": "MEYCIQCVM2aWR0rDcjHtsG9HTf5pAzNbAu+mt7KqU7kgDtjDkwIhAJzGsX/d7VCgmlwgnUvXB9EEpw6vv51lat0HAJ3hNOSJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1-rc.16": {"name": "@radix-ui/react-presence", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "f94cd0565dbb43f3b9f3591050152129ebc792e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-hi8P3LDTYfqzgCduXnTh62jPs+aVYnmQbzz+cWQRtOcH5ggfSWaquto6OxGlcvFiIbJ/cgBZKl0g/tGCRVsY0g==", "signatures": [{"sig": "MEUCIAJGJUKCs3wtr8baKmtBsMYGCp1kRzGXCKbdWPW8ozZKAiEAg20iAvXqIdpSFazeNYV5LFpd7z4vqi3AsObSrJnR91s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24103}}, "0.1.1": {"name": "@radix-ui/react-presence", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "2088dec6f4f8042f83dd2d6bf9e8ef09dadbbc15", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-LsL+NcWDpFUAYCmXeH02o4pgqcSLpwxP84UIjCtpIKrsPe2vLuhcp79KC/jZJeXz+of2lUpMAxpM+eCpxFZtlg==", "signatures": [{"sig": "MEUCIQCYurP2CEV/tdrFeYPflK1OsmH3HepS3hop0JMaO66NHwIgXnJoNYM9gZeWveZmgLB26kT8KhpvGgn0WQ7c/xK1UyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2X4tCRA9TVsSAnZWagAAKU0P/238i3fCpu1LOKqO2mhp\nm9hYvmbwrJRDDUhzcHgTX6ubn/12L6ecMR0dVvwgIsKRRWvlbUgPUFJjUW+A\nNmi9V5M/cxFoRtlzpD5pn9tAIVRIPNwVt4VZ2+bLGnETQcecuQB+XVAAnteS\nWfiE6b/RpRvOuJvFWlGED3pg9lSs+KEq0XjZsLcgextbvJaeCAhQgZjy+PQc\ndG6l5DVv0FS87RGhQPOIZEoQnnSrZi2iEO5MXdHjT3rHtrGO0w4aph+zsDZl\nWNnyPXL1t8OFaHipbhkt6E6xDc+5aKCu8catwbr9bXRl/ZtGCLZroNnP7m+r\nvUx8tmQloWULFlo7OPiMZxKx1mQ95otqbv8LVrwGG6h47XwDRGS+vFXKJPnD\nAWhysVQGtPjtYf0Fci3zLrat9mrAD2g/h9DJeift8/5ZBPmofcvCN3J7/eEx\nPDf6nTwR4Wk1+4yk9j6QV97nRtOC/YangAZP1/En3Jl2PKeeVule25KuK7lX\nWbGx830Mq7PqlflxCtyXXSIJJdw4FZePtnKfPz4lrUdVnoGbqPJiNnYyq2yo\n1F80BNGeqteIcwT+5+aO3RfLd1EmFClrVExRcz4i8gRJtDNh/uEjuk1d9ynq\nx52Ak87ySLwp7Z0qpDuzBrI11UdHbYq4wpAGSISwxgALR2GLBOnBblyp5D/M\nUAQ7\r\n=ZHKl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.1": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "8c28fb1f69fb4d6d382e7c49d9d60505edb5c857", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-A1h+fKxIV94Xh/QfIZ1wl0HYzU0LYBSaqqGywJC2j7OQ43CwgMzw0iPgoJb80CAJoLY8wgyM/4kf7wj2gMgsww==", "signatures": [{"sig": "MEUCIQDo3+Vz5vKlSCL8wGaeDwFVu87nSPgPiy/yThUBqqHD2wIgRSbcmgy7D0wW4mm/TGQpIJlDTjM6LMixXOsGvUw1JxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLOCRA9TVsSAnZWagAAAm0P/RhtEEqjV8fQukmCoHw9\nnaN2IElwd8+AXQ8awjgxriNJh5qyxFyldvHDf/5hWdPfI5b8DzAAs2e6R0+x\nmr0cA29FOoXmn3b40IV7vk7EyWqve8f9L+y4gHXoK0JXJedXaGWP+Z8AlOu2\nYMEhOte1wA9mh3C3ZABmJKpm4avA+UTBal/Mg2bn1rbFnaPCkjBkGDeMZwNW\nX4QKbaPAP/xFx+rKM8yGNJxJsUY12PNI4dCGaL3Iar/5aDhD1N3tnnVOD4LZ\nSRFBztuu9W4GDER3aTVbmT4JsojdjR432wucH2lQ2eHljPQjUzFmc7wRSvQr\np3UwfJHhi6S60x0id6gudXXx+wQwOpsn6enWcRNieGs7Gbhe22ztbiZ8TbqD\nGkWrf+cBiV+eFt9mKtD+YmrspvaWFewCgJvDoTM+LzAcI3VDDSOGtRcYY30Q\npYju0661sUYWZLfjXsq+YJGYYs2RggvMzjrfUwbOR/h/ihC6eL7XJm/nuGtz\n8v63L7/B0iUT5bqK0K5gF1yvLqiLgyWPgBBgFDNb8ce6rVU0ucZRM8GxjaKP\najJyCEeFBc2FOZec69v+34q/pdCGlBTznZHzSsCrnwX81LxlOaytDLQCJ+jm\nozm4mbOjkJx/qVJJyC/zZxBYG+xpbCcrgyvanWaqHKtdoWcxej3Hb14xofSC\nYaMw\r\n=tliz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "0ff3e2ff396454963a78526529210764c55ae214", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-E6d2SpmdJTRkWK7FhvtC9tvqYhT3G4xRsbcrDNEcZqyO5VaeLEZLQ0I/gQjQ8pH27hJJBMQcJK7V8UHGbD1mYg==", "signatures": [{"sig": "MEUCIGUxdv41/r0H2Wr6wrrCNhjL3Cv73mXr5z9HsOTRtYQBAiEAtQnCmJrDgPNjTBMs8kdPLe9kUMr8QmfoXnCVI1+m1uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0NCRA9TVsSAnZWagAAd0sP/1DQlX5FBrzifzZ6PQ1U\nlwtaNp7H/vUsOBN77y9bg7bOI2FEaUZ1+wZnEqtGS5Q7Z11mZrrUEiO3qUa8\nFFHKMKZBtPNgq7XfZaqHNhr373WWhIxHacdTbXuwBrgVG+eU5b7HN9a5/CsL\nkDZ6b3gcG0NOJotaOVez24+VawRrkG+4h6igGp0Ns/+T3uWard5L5aI0+jm1\nDxbRUgklUIFdrjrCztWbM2vFf5tCphoPciCpxCYiUvaL/3bC+Ydt07A0atgk\nbNCIhFaYaBiUm17vsodquYec8Beox2zPcOEf7If7qKQ9dWYtadegAKmxsXr0\nEXzj8vSw1MvO936VfykTMBnXBlPET2nZ9zb5Ueh+1nEywLAMoEp1iOd+Merv\njEuWCEM/tNTZVZZ+90vWvvnzFGBtw4BBvq9FfwEgRfg8m4ljELKf9ZrMbz2s\n0OBEF+QEsVt5ClDysNXbSG+4qlIukunxAn0qi/cmvqMFYMcO7AUUR3SFxzat\nQFCmpq8ob6Yo6Sdgig9CjY/lcMWNnowa89y++/NtEdfeIENsDX8iru0hfSSX\nSYNDepon8Jl5EXq+qLJM6GI+9++MCIAJ1440IR827Lbe+w+R1h2fJnP395ee\nyaQAkbLvCLx5ZLsQOp5wup4f7y1HUqjhLdOBm/iJ369acbjaAVRwf4dwrU/g\nVXbp\r\n=193l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "88dfeea242c9e7c76cab18604dbc041a8a9a94ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-2YjYpUnnCZC/6DwhQutDbX3wXE6zJVPNISyOVI/GyXlrhFiuRJoy5B3cgQWkACqF7RwBAdREFjUXQstP58c0gQ==", "signatures": [{"sig": "MEUCIHNDK0NbuEZEm1jCWIZDrMxiYU3sc4qA6+68y9Up5tiAAiEAgUK9Q/mEuNgrp2CFnUyv2VD6MHO8CtJf3KlSpYqpQnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SS7CRA9TVsSAnZWagAAw4AP/1qd5lCpgvifXXI82dmA\nwwa9LJhw/DTVPt+cJk3mzoHEb5MSLURtPWNXZuOKMjNKcQkPO78Vf7XZAfog\nMVlgP6xxfWzLQNQwW1CGzegfSMJYoaLAp8X5WQZqOfhctyFU5hWBZUQW7vkd\n+sbDa+iYhCRg1kq7DnAxPI12qX/VlKotfIGlHkM49rGjmY3xscDWVNjZJWrJ\nANPG6n9NorKd3M2dqIh/Husm/apjolsx2/2Q8+f2NUTVeuRkP1K5qhnqbX7y\nKv22SSExJSgFAIeLPThv538901BGDMYVPEx9l7KLukm8jGstoZjgOP8ImotM\nzk5T+tbr07KgCGuuc8a53i30JAjOw/3l8ETj2rPM9zc+DV4xllyZCUGWZ6GS\n7tKpFtmVwH0yOTBYynaRCKOxMWibCJrJTYfnCdHbMUY0guCz/HIrxN2xp6p5\ny6K+KzsneF3gvEzHsJv9Jg0D/uvLbhYJg2xjTe+dJyoSDcqF5/YAYA0CKysO\nJXX5QmXXDmuXiSrevs4pEPUR1+5eLfi3rUgCrpAcEoc+RWNhR0Mhz1UTR1lI\nNe2+lqeVvjsOqD8qnRuYkEjaD1AF+A6p/dNrxPNdfyOOQbYvx5Xwdi5+yqaG\n1mneTJdolgHZaoBR3Kg8kt19KyugqTsrIEPovuJHdB7RUaERPthlvmZoCURd\nCTAn\r\n=DCCU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "b7dd698038ea69eeffb05132b4205cf43bcba96e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-v46WeMiTLXGa7xONodOTuuvZZaGKJD7yukDUVpxdajWdBS8qBZuGVEcq5XaeXMgmyT6MA7JH9t2y3sGbl+Oeuw==", "signatures": [{"sig": "MEUCIAH3UVkeDt3yvm3gD16FH1Lz2kZe4dVmbTr+aO4H/JODAiEAkLFEIEEreY/kee7WLZze9HYm0JhBzv1mu8j0xpUIUo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZ2CRA9TVsSAnZWagAAaKgP+QEyg26ObuOHYlyUaf0d\nSU1yJtf7ibBuqcuFzJLgt1aXYK2kRZKCqwkvTALJ+6w6XSU/PctqLmNR3r2o\nU7XXzaAP+Yj/AfBh4EMCwiXsusVniosaTB24BcCA1fViIgD14/H6QUOe8PVo\ncYRkIgrYS4XCPOwP6TIADZpX2J7yMsX0hoberj3fzR2Qp/CUm4mcCVD4qLq5\nEXL0DHQ+kyTeRwW+Xo2PFRaoMqPNFWUM++pVGN8Ve4C4dCeQL2F08+IlxsDU\nne9BinDCnTd4RtnjKq12qtaIayRfaf/m1l5lYgBk9yOePgyALwiGkrwVSaEQ\nhLu4VV5tacrydVUrmrkj6b3Tn1N9okrakZnGm9Ps17aFk6xvF6KRCsiOlGIW\ny6L08EtlR2X0OTqf7h6RpFOU3xckTQWXT975i8XJ4Si5bIiwKUtApIdY7tmS\nJ8WxomLZj6qJvT36YYG+vrT+Tqfrl9m0akMxX4U+b2Y8PqCvdy3x0vwxRvRT\n7Oq2xPS+BwrqzQq2ayATotokN3ytXs8XYSJ+QhaNga3GQh3QajHL0fsKGSyc\n+rG5mVk5/fo22llkToXCUH1pEBKXnj3xtBVzuTuQTDfHznvR+335XqGiVH1s\nTsVqgVW19T3HOwyyLRuQwIo/GhV4kG1FsjqXxVcdsKsWngnyruIRzuERgE8W\nYyBO\r\n=/8LO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "cd4bbdc81025ab9ef8f39cd5e1f18455c20c7925", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-NHZ+hGi7A1mjTxQftRK3JFYfv3n6PlMLVGbJ9/u45zp8hrAayjyhSpM9qGBMnAFknh4IZwD1L2kTGAPJX7qrsA==", "signatures": [{"sig": "MEUCIQDsA4XSqeuxe6WsPruM4HH7zXSO88A4WyrSnQ4SDqohFgIgT1t6qpUFzfhyp1lZ3OuqaTEZ6YAiJa9O1vOvxUmbias=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wn/CRA9TVsSAnZWagAAu1IP/2G3miiChXu4qJDoBoDM\neuN+FFOVlOwbiHB2vnJ/66aYFFHaI1/H4XzgI3nihvJRLX1lUU8hKrlDKKUl\nlYPZtGGr0prBPxULr6+axaKuO8k2qZWlqZJHN4X8kiA1LkaP/hfy9/yPFWWw\nuUK+BEsUPKEoLRbddH5g7QMTlPZQmYhTYu1KgZRq6vS/kTVporP7KhPPi/Mm\nN4XMQjmEKuyHZTvQ9d9K+tR2gKjGe0IAcpSedxtHJMPom8MlLTbtUgoV//8E\nV09IBTgvjSSrwXvYfUZnWGWfvSb/kCSolUeZp+EAi1q4hxrilGHtbNghx+UC\nhfhXE2eZkKr8YjLCtjn88FCWwbQU2Iom3UY1onIaPiAdTmHsz5p7t1Lyu9sG\nXyGDFFQ0zPtuQpJXysqZujU1FoytVH4YmhQLNQPUWslh/y1e3F+jmtsTeWcH\nowH+mH/OWiJ2qD6E0K3FIaT7C3Jd3ZePP4PCU2UQ2233+zNc5CkfwP/ikOPq\nU297ckVWdg5wldD2CKIqofqCYG5wtuYKP6Ex41Nq33Okaj5rvERhXwJAm/tT\nckW51UTa4vi+u1wh8JXC8ir9UJh0q2UDVGHhfovBaNH8IO0szAtSTsu1BTfc\nrO8c/Exzn7gyUbX3EKNoUJDAsja4R0Nj9fTL+fkvxKSX1DbwAiNYK6R2+vly\nr4IP\r\n=6Q3x\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "f60d54eb03da30c581699e6f9969d4d9639693be", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-EfhZPLtAepyGxCT98PclHztH3ZIPqg0LfVwXc57CDErRqTZUh3UbPv6N1BwrwCy3tttXfReyYXmFGJp9cr1uPQ==", "signatures": [{"sig": "MEQCIB02Der1hgC4vVXGojGuesF1hb83QZlVv0uj4qIQlJxTAiASIONECHJpiwMQdk6ckPWjsyA4zXjdA/PY5Zfd8GuVcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUaCRA9TVsSAnZWagAAxyYP/jVCGFE61WMxbvLIR+ul\n/wHRHB9bXvE1qFzYgwDQWC+WxFg+MymHxFkSZkRrIuj5TXpY24qRZHaV8tt+\nsb6d0XbH5vGnZvpEIe1ueZah7fWEp4FrUgTP5BAL6ZlCsqihXVKJEuT+wx32\nZ0y/wgU+38jzKb1y2GpeGssp19ph7tRkmV9fG2Uc68BVwePyTzERM7wkM6pL\nJRMO0roakW2Y1qQZ2HywhNWOS5o9RNDNeHn47sZWmzTV+5rRxYi8GtM95AnZ\nz7ao1+vwBO+9ZPh8f2kCXxmnwDAhpK37x+ItiH4VxvWN8Duor3DBQAvFRUjI\n0zRgWd4Ko3xwNCC6KNVS+4LQPjfmUX8PeR6AagY5DTTzG/lNWj2YLa2QM6tf\nv0FRJVkQCsq02P8MIU6Ey4JFurE9uWsBKwuPIWkNnV+An9c1k2sN+NHKPpGr\nX5H14zhoMXda3c++IP/TDOdoayNkFEPgC/Bm6vmKFuc9hHfJGdi2mTnQ9eWl\nDuKzbKhvEvPyeuL3YZlXJVkJfe2imsT4qQAT2LnLKpMMZfPFED0YSpE9KoGQ\nK+/3i6h8RN5OPr0onFSqjdS7lkKvYain5/QuRsPHb6vNxrD2sHi24zIQ4Zxi\n2NeD9ZIuMkrNXAYXmS0vu7T3BTdtiluSUboExV1ss7KfWSSNSI1eXo1RKWoH\nZIRk\r\n=uAlW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "4bd67d1365fffd33d8633bac1ed9854f6b9a2423", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-fuos1PSfJx7CnqIgMahPRavJgKm8yTtQlKqGGtvpQWd4nRcioFDvZSlpr1yNW1zwsE72BweuOFoJBVd/zz79rg==", "signatures": [{"sig": "MEQCIHIDpDh8YdNi23MR01vHbWXpO/4Tl25d/nnNV+KGtkrDAiByn1hw/AS+gxrwIOpZoczUwBUGR0/M/0nNX64R4Tmbqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/nbCRA9TVsSAnZWagAAJqAP/2zafWan3TdcBkog03rG\nx0EhyN70zXitvRbl6Pw5gUuBugfMGA71DS+GWH2VH3wzPddgmEB5hBhGi40r\nnMaNybK28ez3SeTBJFOqo+9krqp4sDlJdbvF1sEQKUAEQoRY9RQlKjqdRQAT\n1zVQ9fpyy9zXQNRtdllhfYNvGKmutBYZt0F2FL22Y/LLrxQ3dnL8t2m88ecf\nLoZtWGMW1SFdc98wLp8Lr12EQzra5/ZXI8g0H2SxyRRnIDMV0NklZrdNWNYy\nLeovZRUMsTsi/nesjpZ9G1pKiWTWclyZWGkUMbhkQ15aKlv3N1Oj/1ETWw9y\n0/VuqV8iF6VpQUhl3+tJunrTf1wtn6xbJC3hJzegTj3KSrX7vhGjP7jnBHFt\n9XwjgYe2tB4RwBO/sMdfAMxezkVu4JXKIxiF/38BSOG7LrV3lrqdzsDr9rmx\nL8uMoPBYkNEkLFpwat5TqGDh3mFvFjKNhueLJiNK6XQjVJF9IyHu3/PIXBu7\nATq0Hg/+TosgFK4idCtETEIjmVe4OYF39TA5dlxcwGSBrXhNkJMuwVG1JBbh\nZluX4zWZZZirv4w9CeaNjKTsKtl+4MKo0ABAVN6C708pySbPJlrOUyrBOIMI\nf+54PTKyfgobeV/wslVsmxuv3HygFpAJr4R6kPiayzEGpYzz6+Ec3rgSqwn4\nItF/\r\n=PiJ0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "977fb5750a4d2733b51d206af7e6a78c28dafb0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-TsTWHtx194c2YdSdl8i3crxdO2BHO40mSvFVYv9DlugzHERcd746ab+MgTlT5jZSHj18N0LDmPNqoD3W97CO5A==", "signatures": [{"sig": "MEUCIQDhdAnPMbtJv6uLSdaeaUYjdLy1gzRQpZe3DzsKgYyMXwIgNOI4jM7yFFsxIC09aSOBzrAqGmY39QpdIWWoLXrjZ6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBH7CRA9TVsSAnZWagAAoGUQAIK0hWhenfvw6hw3Fu+C\ndeHnTvVPso7WVKMVwwHmr8IK85tmjVx/BE7VEwQOo5OWIoTrNXAi+1Zm08sT\nI/xzGb8nrzl6YuKMaOUKl/tUPxQNHTHA/ch5K7qZBnxYsmJPhJogzgomqr0Z\nVsiZyi4NhHyJS5kbPwfyvDvcm82MnWXiiMlLr3PRPpMZm/2PZGYTpF3ZQRJb\nRKunuuYCspdpD8b4yto3qq3M1EETsqlbX0SQuDI2hRtAMWaDFnq3+iHN6f9K\nRV+avN8w8h3wzPIM9IINYuv19MpRPUQiRj97gpnmXAu2ZSyp4O3yP75RowqR\nEH7sJO0VoowlryHqSjqGIDqiUPrQ7hX14SU15J59zk6bzHc6oqck9fPwiv9I\nlJ89EGsAD0KoE5vfoHTQvTZkoWC1DFnpsgfo3marXRybtdnS8eEPww/ugyFs\nOqwkLcHfbUV8W5KXTkiYdMB3d3rJULRpeulnC1VXIITYSixHxviiGiwNsOhA\n9FbbcJZEIf3LU5HSSvVUjF6aIvrDCd/t5DvY7fGly7UvvByYEn1SQrg21Y5F\ngq1omadFk45SMBIEYwbmg9zPvufdkF/FGRQU7ri6rMzFxMg4e3WahFRzX3Wn\n8nlhmSQ98f3WDqRp34xeINVKoUzNDmwcr4R2QU4kXhDyN+BDLUgF1AdD9kzm\nyhlc\r\n=SxpF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "3869ef78ec9a292ca5f951784a9de3605230fe22", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-gxYHthXZfQuoKoZcMGpQXQk5QuXN56xeRQSmU27y4bdYVCYxQvlTzH8xaubA5q+/4HMLD/86ib2iA7NhTix24g==", "signatures": [{"sig": "MEUCIQDQudHMIup14yl78rvFK/hGGApm3ozeDsqYWaC347IR7wIgbDB+4MoQe2FmP2Tj06Vi1uBoOR7U3FNCfkcAH2SOuzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYHCRA9TVsSAnZWagAADPAP/1KXNB48E6ztDjZRhzTK\nBbQdy/fHKbGCb8Ynqa/S85B9F3j85jWM6gXvkMftZqPIeoV9K+uCycrvfdJo\n0RuSv9XgHzYyAuOptv9jfQhjXuRKjno/LD5SHollSRwgtcEBAFv9hLcE8Fe7\nsfvhaE/FxR7quNXP1xgmer1PzyTUGBxgOkUcXKDfsJYUQAfQmeVkWklgfcxU\n0w2pbVYRJguflRpN64SC8pquKhQZ4zeR9ilYRao7EvDElgdVmxKzx3cFVQZr\nTSaY1mRj3ccdI+hPsXr9OWyxb4SAlfZdHwdlWl3GwqoSBkXEt8iFY3OpFU2z\nTxK2iS2UMkDqYsrBdIbpYD71gXr3JzTAUduZ/p+DblPljAq7DHMB1ABaC90f\nA8Bq7mizhAfKshzAxQIEbxjMYDciUMw5e6+aVTV55bnqYoVpj2pGP2XCmEQg\nAsGkDZMASm2ykExf6OA/EAkqlk18lvFDoEL0I4Qs9ylSuggZJdTaT1VEqjl2\nBokhqexOB9JDayGJkZMF8QePqqGadB4FVLS20ErKVF7IWCWvTenyDCiv+Kjo\nsNXIDfUMLk7dC52/rGLNlPXE1tdn+EKKgtIY2yEMsJ6mY80DDvIax99QRuGP\ntPVBVe5pLG3nOIvLHQHHbgYeszKOo7txSwuycA80UVh3QljZYTjpcUFzhCIv\n4XtN\r\n=3pWE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "593a64cd05adc9aca4fcd06a502fbdac031a36fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-pUHzk+dIpBe5V6VIMRUXyfCS0cZ7srZwO20Pg05a6GBXh+k48ypyaE+dCtVsnkzqimgT2iqSzYhOohmqeajgXw==", "signatures": [{"sig": "MEYCIQDkBWodKHPeKDmhGABpzvTwdjS0rSl169J7gp60VharXQIhAOLzFI5oO0h8Ctn/x88rvB01cDL2WXg1Ua2bNF/1KWWb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBlA/7BbdWQZvmZk2hJffUKVkUt1Mcua3LS9bHnUh6MCyNQ3Ntlk9l\r\nbGTf56ikAey0Fj2ZqdKekDI6Mh1at9DRR8YczJOYF+ObnaTj1069x+pTmaIY\r\nAL4g2YGPDtif6zczvMPBapMJkt+CYvEedOtzuxYFO2Zu2LDNRn2+CIjyyqFq\r\n2KyzHpOiPeNQvBgXiE0Z2+Cp9jIiUHG70b6nyvy3tNVYyhYuRRNxzqknDirG\r\n9l9LHKeqaELQKPNoLkE9qSP4Ikw7orsElFQfFgXnqm+LlAHDlK8Bffez4cIB\r\nNKIzdqwXN2s45siO3xWom1zjcsQf0/XFTK0piUt4ZvyA+IKlhtIIhZwc+sXQ\r\nPcO9asaAWh0J8qgng5PYAnCWulvxwXVh+WxLNLlS/vMhGlKhNGwyAP+CyWn4\r\n6swVDG9xc8aWVsyh7ecd2D6ttpAjvO7u7Q4Kq23uxhH/0+cG8CZCjbR0JTTK\r\nBRpJZteg9Kd7EjYw1tQWRD2blKwabWSQBm6yJh/REIJzW4uibV3q0cQ7f2fW\r\n2zQ2P4hAP98wL/FwGcIMrqqlhaSkpG9wIdgL2TRvpdAF0zPuYOKyh+ipfoFq\r\nnHtn56mbRH2I0fGveee9Zl63k+9M2saOREyWbFLDORC4aHCDES+SoIdhsAss\r\nIq4f/i1EQJ1EWAzL9lf3J4HANKq1dbARmiE=\r\n=ia1n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "aac39dc336140ff5e02db4bf99fe913c4539ccaf", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-RzSws9mE3SfJuruFwneamMx1NU49IAp1VipcmKUmAER4aavKb4aug/c17r51+zUD/S3qLt7UvAUAGiPREr0XqQ==", "signatures": [{"sig": "MEQCIBejBG8kZCcNMHs7sIompGX/SbDg8FtA79Hv86h/ZqDgAiBlpPbV+f8xKeMFY5V8X8v3J/v+REPPwLMOvKWdpJzXaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkU5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUCQ/+LRAV0niuy1biH/KaYlA7BkDwoOkhufLUpCfg/LG8BMxeNbtb\r\nApkyvZk2jwz85xrLGDHgEfukLWanssOj21cQeeByXgvv5WSRpRClAcPoHaa8\r\nFPP6EPNFWd7QSsSJiVLyP2zJBduAegEJlHiYdPI6Ry4/L/AcC915c5vOSPOS\r\nAkMMkGk9ADJx2Hgex0EIcB/wP/UUYikS7xybeoXEYhbcvFKt85A2Wf84A3OM\r\nhga+Qw+MwiLFZ+hj5QsYBzEleq78xFqpTkTn6BjmL5mU8k1Bv3qv9H5FQB1h\r\nn4fQUmxOVB1UYJxJJIUoJ+4hxQtU/8TisrcZo0Ek0fhgbzZ4MLb5XKycV5X3\r\n9ep0XREKuBBoHRQGTjGdt55A0ZyN1G4ysHdNuD3ouSeVQSw5FMQfLRbFZ475\r\n9Ol9QjEDx7TgS7ELj1JBfdiP8qKxLS8YvsLLCrPkC8Khy/ntguxV9yCREKE0\r\nezlxINB/X4MbHEo5GhiZ3VpTXmeV/iLN5fIPQqFesl37dyWLxbmgwEUkfZsy\r\noJVxynYBv469Z8OKP31lx6LENScdtLREdMVylTSKYmOL1LkDicvU+7zX4f1W\r\nteR/5bJRMjMd71ln8gPrmDcDyE8E815Dw7zy70qed9KyOGKpJjhx0xJFU6/v\r\n8raj6QHVapf74rD2EDqseGMozAvHR51IFwE=\r\n=/WzS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "e61eca0649f5c6f223f1a9df69fc15014000c9f5", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-jXlQHUCKP5w3K6q0kdZ4nU7k9sNq1WkmJKHzjVp5hsBX6QMKs7e6T4BLglPTi2Ar0zyYLLKZKGQ+bSIXesbvmw==", "signatures": [{"sig": "MEQCICfc9OMBbG/j+H93BALuUH2mWpgsKUWSrjMNXNKgV4kBAiBI6Gv75zmMQzob4qVFL5SSIbm1vsieVowc5keedhycXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0YRAAoPKkJ9osVty9kg3D6pxZvydZQHJn14F2wq59h3ZxJ1T/xG6x\r\n5KZjncwO+c1IP6MT4OuyCLBiyIJ/PVoDdWTYlFnyAxh594QXJq3JKGLRxnu/\r\nVXbDyGp5pC/qZKSf7+kivzMFblhael5zAxIn5EU1DzYcqP7l+MFX1niZvXXg\r\naJjELCWVjr7oMKgp/+WeE89Wa8melZK6CWITw8irBEzPK985qxgVmwfoLq9h\r\nacBKYl11W/SgOMUBnM6i8d8pJWJp1cD/jkHq39kCI7Y+VTxLYpO7PfsR82jH\r\nZMKMqHQT5K2Sbjbz0g1CI3FD1jY7FXxFFOnrpUWOb3diaeSNV0HVN9/YUSiE\r\n4tvWNZSZ/5Fa5zXhfKVn35lyEd13CD/Kxa3zAIeNHumIi0KDeV2cUq2KIaYT\r\nHg/PbMuDBi2lU485hNpauoRJQLKSs9OOkHX2WdbhXja0Sv3+UbwIVmqBxmkv\r\n442ozZBEgMsAXMZxAEQl+pxVpYVeXzq83cwx3zZqx6H3d2B6aCmxJcHUZb6z\r\nXVAMj2rzgQm4fe70r5zXE5/N6qbe+gcMisq2tm4RXzb89FgHK8N0pgUogPa1\r\ndbUgYhm9xXYsALeKPPwcoOZVId3UJlP/nwYxo9nA8TEB7tpfY+LSrDmHfF8Z\r\nzX0osvu9TzXoxXXTOPfHYV/ALIBpUPcjoGk=\r\n=A/vk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "1b7af04509af5faede573bb2de293c659bd760b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-DjimT/JFOYc7eh/ZRE5aa1iyexlKEfqxwwJY0w69cCtIffyNM/lza1joBE7+kwn4AXeePnLG59K91p29vJ0wPw==", "signatures": [{"sig": "MEUCIQCeeoejQLK9a+30rTuPJ2eFrWpFlVURtANgbSQvwiWMfgIgeXyaBiU7A6WfwIXBZlfVGOOcapIyhLdvMzhX/rY54iI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5NQ//cnA8kLuIc+RnCkhuOzpdZnEJB90ZVCWgWw9bfEn03dwiitM4\r\nOevC69kME8YG2OFEAryy8t+1wARVQk3eox7WULpXIP3hxusFTVLLyLIa001O\r\nU3imys1JRavjLyErkNJ/4wMjuulMfm/3kPZs4+MgpLSs9oWEMJ/bj8bPKYQg\r\ntPTowZ1GUdV/7H7QKDtVvKHNw8CikTw0i4WOiBD33jKI3lG261F4ji7wnf7Z\r\no+U7cq9X/CF8N5fKvFwp+rblsZueElK4AdFwby6r4P6J2Ug32ddPlful8ZIK\r\nYmefkwPJUbehionSn9KolG1o3G9ilFTueL5mf85ZBxqTJSAgsQ8LYSIsqskV\r\nR8h5KxFVvuyWfgp0s/8JOyJOljJ8Ob4QQL4HFOhnybXk1lGHu6kO9ETWgBdC\r\nvNDZM/GPYzsuIYz+mYsMtSZvPIP+x14WiSGD9y9tydzm0flD0Eh6iPFVHUlF\r\nSSTsf42OxQu62nBJEQ0osaBLP2fnSKBYC6Cs5O/Rj8PKn8lLTUJNFHNpLC/+\r\niVcP/Mz3rw9UrmooRb88scl53q+K14FRiO/NeoUi7NrEDQMeS1+ja17D7yDG\r\nZXR2Oi2ol8rQB52S8k2U0+STIyW2cyQzS5q75IfQBevdCrllQ7VXsNJiX5Yz\r\nYCWMiE4t+EB0SdWOI7g3pSYz67D8xBY78E0=\r\n=7cCp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "03ba91ee23cb728ed4523da99236fd70f67ef358", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-x5mOaF+TZgnG1cN+NiB1pTTtidCtsztI9ZZmNXEopZfRB2baEcbF6GqEKWqqPgYlqe8xhu3epXJLBvrqQWGUjQ==", "signatures": [{"sig": "MEQCIGP6QNRZ1o0WCRfpdF4lJmoXORmYuUnXgUK+60+CKCy1AiAvXgXAFq+D8faDKZiiQSljimE8h3P3sdB4OHgn73tvDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNCw//eR/60kQTZi/mykz348EQMM4wZYIl4xOXwwzrldGhmbfob7qB\r\nPISjx9vdb07SLIiOzkcVxiGZLWIyg31fuxugIVXE2v/WSas+mDDq4q7+l0yk\r\nz53NuQ5sir7LmcGKSk1oH/QoKIbF7JLp5xWnSCzSphWXOMbPLCyphwE1DLTY\r\nnWNeeAfPo/olfSMwWQhAJXmt1ZPDfXdGSumMs6ffBpAGUr38dblGvxaP/ncA\r\nNRJqrxYolsHN6IxSUkKa5pmbawgwTz0KENBdPhNoC7sEHcof4S813yR3kX1t\r\nf9QLHQZsOrY2jAImLNUex/kRpeK4yi4SGjJmdEpQ64m/nUpGatd8Sfq5k5Hd\r\nf+zkyx0MXX3vJXzqDhnoTp+O3utJPGYeO9By+k2VuhGa3qW1KFi1N8dHutDb\r\nLk/nB/U5aIZTVriyLJejgrzoYOi4QCyfGU8WZi154FgiVhANN6FgkWRi4w3N\r\nu2JVdE+OIAaIMA1QCNbP74NCWYwZLWBUQr0zyMQRkrWSw/9HSd2kp9HO09jO\r\nA7IQvKEoKnzS/glHu4x+YODoEUOBuTlYRk7JRW9fjAUKBDNG4i5aaoGvDFqG\r\nz39olvoSWSytDjudRPc5F98xNUsYsgTjPcwylGkSEmQfLIWpgDwt3wldZzqi\r\nCOWS1XhAppt0AndTSsnRC4wHF+bsbMMwLXc=\r\n=iTnk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "e4624e55c6cebf4d40ee632ae7ce1a87cce087d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-8Z0jBfzli9O3+HOR7e+HCyXDjdKutf+VeeGzINWB1RxzrGXEnfHZIWZhlGZGizCFMmT4LM9nrlE0R0zhyaceTw==", "signatures": [{"sig": "MEUCIQCoNuvbpj9G/2kweWC/SaPcOoQVs6DE8v46JgFBlGS9CgIgXFEuqnupFiuOSP1s83eEITvKALbah0Gx+NL3Ki6U+iM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAbQ/9GYTafvgIm/B7qItZOwO1hgVJ2xSOAjtQ2Md1NS/NyXLI9jR7\r\nbQ8SUD6ziRUIkXhrjPO9+sXsYGekxH6Dl5dzrBaWHHWVFwHyjVac3SrViEXl\r\nh/+6SSL12FJXu2qagPBrNrVt8WbQMK/z+erOFslWDz6yZS5mQlwwfgz1o65O\r\n7bPXOxR1qMhmC+486F18IRi+9jy9nJ0YRGwtFSoV6NNX1mO5WJiE6yCURwgc\r\nKzgiaSH5mXN/hFcJSaZ6YXCRN/LWG8dSyPYpKUjcdTNA36Lyj2/sOK8mNNKR\r\nNKonLpE8XSzEdn6qYKnLprLzDwg8XoSdGAWtAGD8poP9mq4aq81xTiXzWTmM\r\nrtV+peY1djNXdeO6WK4XGKqVMPHkR/FBa/PjOReVHu5qkJ/fCDp8O6sUXcU+\r\nb/5CkjgxAjciI0bwmTZCnwIjU2EBB/bku8Nys8z6CATnXlqYE2kTEBmiH/5q\r\nFXphP7EsnBN/bqzVbSDEiWf9dAWqcbz4DrWLr45eWVBOvXO4JOYnQcUPLvIY\r\nrNxOGyjAEO75sFMGflzHPBIWx6AqrENRn5JuV/nYINQ0+EhAUFwlMHuIj/Cd\r\n0gCyx9GxO5fxdmPLSkVSH2UtHDBRov8A6MZAtgrrQJq39LmodPSN3xK6RL1l\r\nshALTUnvDZVZGeE80a6lpwCWR8PjqWaymeQ=\r\n=jIF9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "5a5fa434a0a9b3f5c6407dbada2cb2b96d9af521", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-z/h5PEEcBDPWtkx4r0srF3rcZH/7MZxFm9m7iwBzXmpAVG/NwNUcTsADZFgoy9X9qSS2hsFJ3ZXlsS5LNzr8WA==", "signatures": [{"sig": "MEQCIEmz3K51F6gsEq+JWskM+FhZVuRMSlrcSxjNt9DiFWGUAiAZ7WdT91f/1K4T0NJLpyNs8wQWvcqpkSm0Ps7phfCLZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoyaA/9FmyMa5NREgWiMYt1Uk+IBGHNjDbOPWQOY7iH4STbn2fKneTk\r\n9PjwTOTJQh442DJqVVuvZvxjpooF+Kev7XUDX1qBhgnnVktfhGnkIW+7DH7Y\r\n4WhXlLJvPLMn3JEBZd47Xk+uTJck4pOU7AIPseZ4SOEuXTsSmXZCGXcl52jq\r\n7a+IfQwvYBC73veoguKcFsxE8NGPuSVn5wBx0pjdCzrCuOg/cl2ILlfN2MlF\r\ndJklcxpPSGEHGJ50XLiqu0+WeZsfABipxy6xso7e0jJ5Al/cDfWYITDro19N\r\nB3pHMKFd8xXJB1VaUCRJc1cpb/w4mZsA4QZ/sKabyG87NnA3L/mMtdrL/5v2\r\nFzznRfLrtqcJTEKO/mqLsvGcTmg5XZqD1JiyJmc2ubJMHinUVfZzrPOkbyn/\r\nffZL5l3hS5uE4UnMoA6240dFyrvMOyeHGzQeGAoX6PHmoxVk2X0BQ7BJ0jUB\r\nIijekF6obIDGVX5B3ngJ0HQDcg0IiSE5rsq0hEISms3t+fxNAgu2weDjrNpX\r\n9KJsfZc5ZjB21YPr/R+T1mG0m4GaolHGN3N6484P42sLJ1xWbH4clgUJtoc7\r\ndtELji3SSOQF/z1DgZPRXTT9F9d6z4OA2clwiNodaYWK+NoBDgazUQ2TLnOe\r\nT0AxhIPokxWVaeKTTdlYOUbRIq1UszHnryA=\r\n=4aGF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "0d054ecfa95642e3569ca9905f948cddcf6973a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-0xKX+i0ort0S6D4FDZ7knSA7JUie7JXHedHvHOdqVqRU2Tr2QCaZf4VsCinWxav+HxkamUsGx8/GRrdv61FbzA==", "signatures": [{"sig": "MEUCIFBdAQqYj4KqLlYGiEFzNo5xjFCondzDDM5j4ZfuJ/jxAiEAuTZOO8oaOlCfGJBiaN8EmWJ/Vx97a1SnEVbKB53H0AU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4X2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQJg/+OFIPeTUGF9Yz8+r+X0xdelzZUf/SjzQll+5ryDOELJOX5KrZ\r\nqD/PD1RVtFLTl6wwRJL0qt2otmkRbQ2Ms+Jpiv5EELoIGUuUzbu0h3bKlxcg\r\nI5GEYIFT7Ja24Ttymmv6hDfg6q37IQY5v5tJRBtq4lDS8Jvy38pYfohYz/AA\r\nVom7L4vrOmw5IGRKfxF0uL//sB86qHZTiX/W/rfhIHSCYhRSrePNFAvpfruo\r\npULaNTet3UfFBI7hxnfEgdj/2ApNwiCS3Imz9O/pYEM3te3hosHqzGf4s4xp\r\nNKPFp9hF0SFc0/V+YBWpI3x2VZCQZhXpZJJQsJjjoL65GZO3jZSbXQRKcQds\r\nGSl74ZW6pJwtxXgh6gOWVikuoEd6C//+dZiigOBMY9Gs4wqVltXn4I+gyxNf\r\n0pbgAdXHTapBG0RFoM+BkFKJvg9uxjS2KabUVOFaptXLpwrSPj9bfDMaPLBV\r\nP0bi0yj0HlXc5D5H0Ve4VRgZwsLoWmr4B5/QCb7kFo8guMAKSKuiIPJK2W6A\r\nZuwogFi1Q3/Znen1ekuvRVbXCXX+HkrgGGJwLiuhGlq/26+ikms958cyx3NH\r\n5vi9ZGvn883GnKURCrWxcO+Q4qn93ajKV5pOXSI+rP73x+JdHPHH33Femjto\r\n5FsjbDZGY/e7+ZOGvaJeCWoy1u7TdP4NTHg=\r\n=y4U8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-presence", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "f013435d329b3fbb45f9ff1a8c6c847c4827aa68", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-WQvxTI4kKqpCH7U05uRKkREbNEcDIF3la1ygmhBN3yjVVaOTpeda4CJWNF3XzW0EGfLpoG+1Xpsf4h8vedoHcw==", "signatures": [{"sig": "MEQCICw0GiIDpS/2a2erWndsE1Nx0RcJ/j57U7jQQvmV6wwNAiAFB7bHrP8+nEiQre1XytVEq3WRJW81RBoN8eNuYMUr2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1ihAAkSMQWFCCoHM+uXNb/G6saPL+tPNViqqOG3TUt7zrCyzssXin\r\n4b+kNQN+rYHsttHYy1GZK1GuTss+BZsrDrjT+yTPWU74PZwFxUqV9/jA1hVD\r\npc7ng8Qup043fDzcr6JNoRcyfRyGzrAtMGZ6MTh8HKRLackhLKQQOqPeIR0Z\r\nP3vXACvKxUN2VLireXZQSgWFmW+L0KpECahXhPuM2JpGbJQOg35EiaAUq4K4\r\nT5++kmkWZDOz30mHyokfqYfnlbMrVue2wYCwPvLbclSNSqiy46KqrJin/dft\r\nFFYA6u5VOKw/NFuEaoVdrxL+m846KLH5tYe6ZATZOV7ML4GFKB64Vh/H+2oi\r\nOVn5H9D8EvTxoB14uwKg9nQlQ5//ZN+/Iw/8W0yJfx3RjQOe7Y/12I7iyG6Y\r\ng/Ezp1n0/dKIFNuX3WRlhNc7LjoojQFyZ4JSRDVOoSzAHPnCyl00R1wFW6s8\r\nsiOyk5h37jxGkIKhgb1luusjIXTbMaO6Jiy5fNFa5SkpSsFZVS8NZRXevCBm\r\nPXeZ0D7ewC/2G0aMjC1Hook3WRzIBsK6xa+meg08nEjuTKTO6O2ECWkk88Ck\r\nPbnzBdOJyGpqsz3M+v5PVFFO9PXRz9zw8gh7rdIxKWG4QJatScY5zmW9wv4n\r\nTHe+2FjDYV3PFWTf/2ICmJN/hvfi2Lfw7Ac=\r\n=9qwT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-presence", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": ">=16.8"}, "dist": {"shasum": "9f11cce3df73cf65bc348e8b76d891f0d54c1fe3", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-3BRlFZraooIUfRlyN+b/Xs5hq1lanOOo/+3h6Pwu2GMFjkGKKa4Rd51fcqGqnVlbr3jYg+WLuGyAV4KlgqwrQw==", "signatures": [{"sig": "MEUCIQDKfUmeMuaiylsd15AFp3HlQM6cfZ1Hxa45uCRPlmzzegIgHpWMzLIrw2nO4Ugs5wy5lbchiA/IUZd3+r/GC4HdmQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxmg//Rkw5oiALfapGS6WXQfUoTIOUl1cFptzG5LS03Zf42CSA4ryG\r\nc80s2pRIbvi3ASwcXk1KdDu+g3UJnzF5aX3qQ7qaDZL64SJs7a670vavR20L\r\nFy7Rfz+ihQxqGfFEhJJkIv3uHmT5EfxJNGBcxGBJm+d8A6HYtpbZm7Rhql8t\r\ntikRghF9NfYbjQ2OJN1KPKSksEcADYRI073d2MR7yl8kScre9yt55Kk1ezMB\r\niXNt9CHi97JSu6btnRhNxJtiaINlv46UrBns9vKmGuv8niBmumGMnFNO7klg\r\n9KF80menvfYLw3CbvCnae4HweKZ1EpZAzpyvHp3aUq6JZSVdVzFCIon1qEyK\r\n0xA2OR1N8oUGRcG/OqOTPDE0opXs9rfY2NNW0PLEUO32TeglglNxI+zsK2TN\r\nFlD8JxJchI2xUHE4cG/S0xyDzSA4Z7ppL/GSes3kk78LxUeKZjQD9X67cwgO\r\n1zNKB511ZYsNJ4/ikgkShA3b8WwLOEoeoA8jSUat5pmMdePG362uwb8VIwOp\r\nUC8oJ4rkWr3lG1vTmPOlMVmLr3lHSRh7vVfdkS2oM29Vx0uXhC5tU6a9VTQn\r\nEqPl+O3V3pOOPX4laybMTErXkJaCByPB8MTs7fQo3PbvQsYOGtADPSdCKzQU\r\neJeRCJ733gPLHOOVBjmOS/bLtuuoBn9EW2E=\r\n=Ngep\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7b4bb0a6348f0cd452afe87e727b7ef0aeebb2d", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-1moMjJYliK0KuEJX5jFSjtT/VVpMCNBOvNA/0epWLwPDhQ0NgieN76/Iyicg2kP1tKsrf2IHLLH6Nzy0xW7rWQ==", "signatures": [{"sig": "MEQCIBAt96hJbuUkq67YGonKoDgdkR9n/eb6FnC2iFTCuvaqAiByAiZKfxr4fb5woaZ5u6piuGcY7d/QlEclbY7oiT/sPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWARFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdIA/+PglFOKr/SqZ7zkHazKDvPM/Q18TggtOsTxijNYfiqw+OFWS+\r\ntGLOCTUqgv4i8a9vZfD94WTHKU/fUBC+z0uqf/FPtj4vTZfAukVAslLddSgV\r\nJEFzXUhScRd09gKgfEgU5XK1objJdu2fPdfGLiHNHSpzvjn6mD7IoDtOFi8A\r\nnLDr91G19uUd43nvhsBFSaU7/C++ypl+ZNoiTmX7QtCozAIlabphqQvac979\r\n3rOKCS4rdx11f0Q1aqQ7eDkoffbot4ZwL3qYnsDZKKK7VdV5Jtn9f+eC6LfZ\r\nE5I9WgyhPKCVBlX4e77HU6hRKu4V+w6t2DxxeMI3vXDIY05ZMS6hyYrhd5h4\r\nAHvfwuAo7ZDCnYRhys7CFvZHcDMCHIKqpicqN2riyBXOxjxTyIQalhYq6SzT\r\nR+VegcYevsKp9A5bvDZpepBOLoYkSoODWkG3GPpb/o8+IgQFVCmsx9O5YujF\r\nNvkJrfGyzC5bMObwxhxxDHGuI44pCw6bpNK0TzbBeBx2sGlGOrXVvKTktu/y\r\ntWoBuUMZ3l6qy2aQN2qLgRHVZc5kKaUvi6MObd1DiBphhmGaorhqaJQq4yzX\r\n/c7TIRnCg+A/EHfGgiQ5ROEZNeK+Vug3rPkJhKdhPFygHl+ZDxEpFMk+kcJW\r\n6Y+AacYv37tUGIcjJyUSyaFEG2+qBBoQYjc=\r\n=VAG8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.2": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff9a03ef3205073d96e6837659d500b089ac2eb2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-3YjF0jeOUYVLjEdBwB+98TEcdgguhRrdS2+eftKPXS8ZKmzk9AOd0N4yjj+r9rnD0NiknMvh1OHsN3G0yMGfEw==", "signatures": [{"sig": "MEYCIQDBSUCpS1Ya2YzGVfIsrsAFDEPRkzSdtaNkn948sdDUKAIhAIsOrTspZAHL6blF/TqNwd1lN6/yNzfeKQM/WtiziUqe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAKg/+M8tpGwjx4JcI3iGXbXLZFlHaK2wO7NGKLil8+G90yO42LQKE\r\ntOANKewAqfnXQaKhFIHIaJcRYkcAMWTJvXlOrS1T4bA3srOJkD60onUjUQ8+\r\nZSXWVjKVbEZ951/sn/KCICp1pjWb4d3mPaJTNoE1fTILbDx+COFvVaK3WNWV\r\nWjS27V1aQfpzesBqtaGm4ltIhuiKp1vjfL820lT39w3gVuS0EGUVkWljerRO\r\n/F8MreCmgXbg+WvLsnf8oU5AyfsFjDVwjeoGeM8LuJQLps3p7bgS0Sk5hluc\r\nb6TJ0HuNwRzI8X/NT3gZWCyaXd0S1y3UZ+YPSz81r2HIQ1jTWZl+nhKDGSOI\r\ng78NzGCfJw8xslI6bK+WaeTTT3xa9pCb/8hYnkJptPb5iu/3sYSha2+eXc5I\r\n6w98YXr3Yw1XIvYTqafiZmzSEoJgndamcQr5wSqTUxPN3h5n4pSMW7vNcMrA\r\ngOxWFWqPI/RAI8ByoXeoT+i5EMLxTdbQUvLn3PdUCl/kNrPmSLXR5JDWGz9w\r\n1PHAzxalEa2l4wyK/GG7RtXzsIB3zQE6PrdnlJXe9WIMKqpwyHBqgTmTeQIe\r\nAVy6yB3Arktr7RyaPTFDW119trAgRiExorurzYFdbGBY+fUkSc8co3lwMVjx\r\naZF4cCnrrol6zRpI5egdfu2J+3Tczk48Cm4=\r\n=yKax\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.3": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76fa35359bebd1d882170be26876c74fe751a4af", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-uBExZlI71yZ69kk2xVO/9D1wkuyUUYCXCVFYJvrZtMj1JZQITcyef+zwzJDBrks0JB7PZDFZpspRr1NHQTSQ5g==", "signatures": [{"sig": "MEQCIEk3K6t4pMlU5Qjc8ZmaFZRt+TNkm4ZMnkPbsyhybYgNAiB2Q5ayDTpZqaaMngqvQM/4OdVcnHsez+F6Y/ar8skVeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37688, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9QQ//eO0cAqR889WG8HNwjIeCRshHxsDpH9ENLlCuBCVqeyW5bu9I\r\nxx5q7I8Ey2RVQ1hhOqXa73zfzBef6iutBKq/ENBpDuEY+2y9gZkTfvYlToav\r\ne9Oe5ZiOaAiaPlajxngveRDpNUmfcggRIOmI96zS3QgbimAvBSMkK8KwHfrg\r\nV9RcWRAm1IjIl0tkeLAicV7B9O2Z7wf8zX6GGmQSzT0p7dZGE64DbAzlDAEE\r\n13KI7Vk4rUeau2VRg0YmxRUf1E8rYy9Ow38oH13pSam8QiEmKMssD+6b9WkR\r\nhdDVJnq/iOm3D2cWG4kN9gShx9tY0BwlnmRxO30wHdzKcLCoUgqMls7EbU3l\r\n6eIQh/T18Kc+VQIneP32rkT+1jG1Mu6euTuzBZe15rfhQ1q9ERqT2wZDljpk\r\nEVeSi9rjhebGn0r1scNkf5K5E5Er+fI9sOiu4GSO6/t3civDVCwEjNwlnoof\r\nLwYNJ2K+yREQfgUrkpDC1iOyBrC2eBnTph3gZQjX+XzlCskYrHe8Vrr05IPF\r\nh5kK/HljWf2DMds5kcz7XvG7fl1Vo7s4gkb6iwk7ymKtf1pVmsYO1hGOhMjz\r\nJKmql7LSZ7BlweT5yU7/TwrYfMP5+H8JgcQjWg2x1zT97EyQa3JdgV453oxC\r\nGh1WocuqgbtXBSFMu7YaSJ69IErLHSzfwfw=\r\n=7Ngd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.4": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a779d5f2c965bf034e32eafae600ca705fadd263", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-WG8taNsVg627osuf8F71JJrHYV3Ag4uj2dw16qJ8q0cnAaxJC/q49otXNuPsMVvrtAGV1LapN27sdzcEtxKykA==", "signatures": [{"sig": "MEUCIHQS67Q+OUN6Rr6Wmsy8QpfxsNN+kuRRQQCeqK00A6LQAiEAyHtGN3Xmn2V43pDPG8M4+SBPGm2/0nFpuIi8nZFSEu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvRQ//SsYKUm6xk+p9TPov7tp7Q/26r8rE/RP907s2c7jVkmRX4b/3\r\n8FVKdjGQ1ND3Ma+79+kDiYMqUkBX2WNH3/X7f/JEbbHG69mnBv9cEtwEZK1u\r\n2zJ6yHOTmpsn08VH5CdLORV+Ms5bSgdbwoBZ8lmMTFOiqrDVo0GEUe163TQD\r\nBalG1W0Jtq+QbiJSuUjxZ9E75VdZvR8N9nTSuNgKl5sFwVh1ICpEVOK19JR8\r\nM8bqRhZIpBPNYwhJGXMwdq0yoyoTkWdrtGRm8aVGtFvt1M4DTPchRnlYLtWI\r\n5sFAa7uvcHBLOxZjc9fiBXxju/q7wTJbO7UnSxFcOXeRG405d5T73e+Xl+yv\r\nIXppBvz0Ofu3d0kUxOnNgkfNA17R/SOhPo9g1KJ7ASddhwRYTRzMn7LtcyHf\r\nnAfLTB1OY3ryeZuzYfgqfdXQv+iHUiZi1npI/Ut4ugVrWII1Vn0fM4eGQhTK\r\nN//Qiz8W9DvnQLNJeyxORLjfgwTc3mdYAxMMjHO30U2jRLlSKw2g5IpiYIVU\r\ne3baymnmDV6b/vun/ZUaeJmaNlJOJ/hpUWf9/oPhS07RR8SnVsW6IojsA+SH\r\nY5/xiPPW7/0z6OMfSla91EEAkSIVFFB4Wa/kFggxhWfiYOs2gBwn2sTIwXW4\r\nobYkMi7WgKh7JzTdXeghmWI4EgDvUv8gdN4=\r\n=095F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.5": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cb4a3f5e97035d5098858eb81fd516cb7558dfc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-e4pwIhQtde3Z5Gy8st7g/9VtwioHtrMOk1Un/IQtNHjP3W9k6WascI534UJd8jCsYjI/A3cNnOuGOCzxSRb8Qw==", "signatures": [{"sig": "MEUCIQD+6XXhR5w040ZyETD0/9RboODqkEnQobozNhKCGuLnswIgZ6nTMzMOlINoFITsyzYLrcqpInIt/fwG58VSBqrHsJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxDw//VWJcj0WyLgRBMa9sx/6pVS/iJ+/e5gwV8VZ5jfTO1SQKylc2\r\nkLNE+w0WhTNXXRaGd4GrPswsHLTuigi+pC7jC3WvmEu6vYQFHlSRWCkkaSv7\r\nNeEeUWOanZKYQ5dn2N1DMRtoPRc7/ZeRdwh5XHviMQbp/oERDs7FEpFPFevU\r\nwHePRTDGdEg7+9yXL+dx4yXSiX1BPgrsIGV0eX8dCnc0awhyOO4p3SddJG7Q\r\njE7BMXDLnyN7bZVgp+0q7kKjotZsEfAqET6ahm1zTc4+7nMqiLTaStI149Ga\r\nl5vpdDjP+aQxRe7QI14ZRognxEoPBMvsgWIz5W4p3tsL6t93aQ1PUv04VQyE\r\n/DyYM+NDsNSbc2ConjLRo4nXX4Tb5p+QTYih8/ylh2XfNlzEesU+b/3a/bEk\r\nyvTHBr0RxxJTOkgNrRbDA3Rx2gEP1esZL6YtMZ9Qnj7LuBMUv1Wcd6JadmjA\r\n2U2A+UCN9aOsl2r0z0XfOKZj87CTPSJAORcjfopgy+nxsClLcc6Xf36ScbOW\r\nW7/k2/x8NudWRHfoo8Fq6kkLE/Z/JWhhh8qVYFznKX+dg2+EDOD1n6ryp+bx\r\n1uspqdmQlS6vhbG7L+vTtCyzl22TFQy5OtLcJg/cLwzqbSEe/wAZVhvM/j/f\r\n0zrb/TsiFs5xjGegk26x3Hb88D2FDWk2ODA=\r\n=J2rn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.6": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1834128e8cf9ef8599b2fdf84a5cedd571f8a166", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Hgts673cJ0pbyMxEg4WWVIXGoBMFGcDBjWOpaWDVJP+wSrmzD5KGDeGVfRvnRPhNw0QN9oj4TeYE+1nJoC/2Cw==", "signatures": [{"sig": "MEYCIQCP9TfcIE5ttEXXfOIxiSt5/1UAZoCDxehK2xDBHZHBDgIhALSS59H4zmoyJMmInVMtzTxLDHK5kLTlGii+CLSR/dcF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8x9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPwg//ahNTCa3FO93TR4MTXalprPkBU7pk14iM2YCqDf/dccNapEvF\r\n4Q9fyGl1j2px3/NAXDH3Y84MRYJRnMcTpn+SWSadpO09yYyfjc6ponzvi8V1\r\npXWrwYbPR6OOh7L3Reg8uimL8V93T1rKxBwuBC4Gj932vZPDB2Fiwa3MF3F2\r\nySLeWQCFkyEnYf38hZbacZSvuaAY5CpPzU7l2v0NobaSvtWsRheJOU4UVGBE\r\njJUL5iEZf1TUkegqs9HxRPKohtUJEwilzZVksrw0cYzCG83hctDg2txrM7vJ\r\nW+ugEcesm3LjMes5Yxd3N5JoadBsVTSiSjS6vJiXy/sDC7FVn2ZIYXwreKS7\r\nyewXpbdDsaObPEXNj6RWc/ZKoIK18VRSdAv6k9GQbMOCkqWsPKXG2Eg2nN9g\r\nVEpVGE0uOHabAsYg9SZR+ESRSNEOUr/oHuGoYyVIgU8a2V0PbJUmt345OrH0\r\nyiv2GWRU3V3+esUcOpAVC0a2yC6xuwbnFTTtySeFhpQ0UPBXBvYYluXTmAII\r\nQVsMzhW0bHxm/HPQRh1KCXoEdVNdjlzj8HY9gwI09qqxE+Ev7/tCNHV0otyW\r\n66MS6kcgVUUZQcwsnRd25p2lRzmm9u+77GMXvUqwkZIAnAqUWng6AfCP0+Xn\r\nEbI9QDxDP0uPE156f3Y3cRajDZfm701F5j4=\r\n=KeSu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.7": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7dd72a5b765dde16ea0efc6c98b4cdf6308d66e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-TVJFM99Uh9Fa2wJK4PJSRvGUePr7/B6lm4VD6UY3xfac/+NQaRC0X9u4yIUdmYktMgJCYXIK8UalhulmatU76g==", "signatures": [{"sig": "MEUCIHtU9l66+2UzkOQvreMcJ7ZamjpCSuJjmWTy4Jmq8wY7AiEAh/ZR9M3rXjyCsxpz1eJ6LbKjDci+1icLQiK4eLFU3rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia911ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaChAAibS9Wr/krwL3K8qotuOJUcAuVjUEyzXPWXBftwSE9mPOnT3r\r\n9FWdwGkBNSbwKhgLR/F5ZHLl4KKq0ymzP6AAmLjWuFI1ahVJDXoNJyaI7RYn\r\n9e3RRFwlrOKjveQlnIKWwXDFANO+4d6Mu7cj5i4ts0Jmoq3mBuVgouuIuhJt\r\njonzTLPKvfQxtGJ4eKCARrBRManTswRQjfXPk0oYJH66v0uHTKK34MdHiMum\r\nANRm3j395MWSVuK1+I2ROVqrAJifVNeOJUJ0phyOUIjLzAiTN1jLdi7hpVhj\r\n6hFrWe5AgnPJviOBw1iKaxobxdsdfXW2YyMOLSJHIxfXbHToEGoBga0saa/6\r\n/oiREsQ1UOoBC0HkEBs0eptVkfNufHv2rXzTtHwF+kw9vIP8RwAzBTa7Hq73\r\nS0GlgB0I80zozXoVnK06Ql66g0ftfUVVx4oqS1rtilNlHvIskPcb9arhRAS0\r\nducaqFuMpCy/FUaHog0uET7EQMzgHXVk0lXFucJ2GDmnyGQKBLmpQsROnota\r\nO+zM+Pc0XJq+DFCrQPtFwMYbSHRC/KZ6bIw6pJ4vSbVMdTu4hZ2K7ULyaPCl\r\nVdBi4ioJtOiZ/XQcAl4641FVLXmC8nNFqukRG6DjDKkUkRs5okbcDDokvDNV\r\nFJHCrATGggQNov1P4j8Hmn6Rpgsdw+PYkJ0=\r\n=aZnB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.8": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f8c85f9ab30bb251bb27bf89d2289fbd44180cac", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-rd+RpGR2giFePaqaCWICC9fQUTzZAqeFBU8bHsl8Rk9R6ghsjublKAdP+SQiC3wyL0D+J/VuaNoQqg8Kyktomg==", "signatures": [{"sig": "MEUCIQC6ep3MUioIrQPyQ1+6SL8fv7AzSToV7hLE8v/okzJicgIgJhDA8KwQFsOj5T/+hsKMKXMPFlPzn/q9BErw33bmWA8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomGg/+LHjfOG8hL8Csop3KPsq6maZCNoqxddxMufhXIk1qhu/XzEb/\r\nJQUYtATq8luKYqBpbNG6XzO8dCJCn4oRInEzLo17S7l8x9yAeKWKuP67Nvfz\r\n+NpUI55RiyU7fHPGMV527GIw3r+mYjeZ1bocEqcidViRCLnzmtCFwcAfF7as\r\n51xMJ3NkcrVLJVaqI7iYQwM4MuxdD1k1iWq3vTzWneN9HsbWJgFIX2x42ddU\r\nr86III5k5xukwWA6QMggFg4ALtdC21q9mzNgIkPUTYsRnW9Li6AlS24cnlsR\r\nk0BbHMthC9mVxP4OJuHe6hKaULcfeipKhmWFL2CpEGC5CkBFHkR8yAzJoLqL\r\nDvRRltzWON4Ks8Tfat3G4lvYBKrrXWrLBhbOQMpECtFf1pLVpAVsZHaP0zPV\r\n1e8Ea3w87VnO7on4v1MKvDJb+itg4FIiKhZ/r/kgD/O0VW66Je2ay+5Gpd29\r\nNgAhB9XS3Wl1Wua9oyZM+KpKCb5/yoZ19hH/z8ZVL4wbg1k40qheeGmRvFqF\r\nY01ay9yMnHYwYGAr5F4PRE9kcS5zdInLCIJN1R1V877MXpzfmnwaSCkKIhfd\r\nKrYnP4HliiiEDf4K3D8V6B71GbhKpbIUvIEkTdT7f6bUdnuT9aJ48IJOMU3y\r\nhCXUKnJbUPD31tBBtzOCxDUC/ifREoOgrt4=\r\n=xoJD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.9": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8b0a221010a7cdced1f56f0a64bac268486322bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-0ZO/1h6FUliW/DXsxaSuSUyUSP4GHHCfbT8xyBRklqVWMWIo+T5OdtGBshZca0zEk3RMMpTWpcl9sUWq72mkng==", "signatures": [{"sig": "MEUCIQDbqizfQsKHBW+BU1sxZIoMLVmFMhEuYWk1AagTmSsNHwIgR9VdJjqRnb5WaOSh/MwrLZbHaGAPZIVMPPP2sSPKB8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNh4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKnQ//Zgj8FFqBLfEp+K+zzbi7CBt6UiDNVybaCzVoJBBjg9zhQCQ7\r\nEPk5Xnaa7PHkfXTWWuxaaKKq3hpNQCAVKpzGpuNww+mhUGW+R5YQU9L7zRn0\r\n9zKq4yacFS+L7A5yoRMu3MvESxzCSe+bz9zDXGzXIImNuGMEXlXbjMYi1at6\r\nxNm0/O3yXyof97FTumADj1JmKg4YsiGrkGyj6fFQvb6CFZEVBBDqvv5i2wiJ\r\ntPeGtHDI8Ol2h+n+cfrQFok+uEibxektqZgkTXCIGYRLMcWm13SKe2dLfkQd\r\nemvbvjviiobDGighPjbbwnXiZ0FBVTxycCY4qVVYe7B33v3A7mZKsB2UymPn\r\nzcVXs9gJ40N4QycU5wp+QQwWLSoG0ZoCARgtQj+Y+DpxYLXDp7E/fpc3ZUg1\r\nPYC2Mch+j5wiSOrEqRwpOvePpkbyP8z5vFmKBuEiz8+zdCiezL66u23pbGZn\r\nyQ3oUX5FZgjQv2xdv7rA9K/acUywMdRMl+O8CU+J+flSiGm0qy6lNqW0FTDu\r\nQC5eMB5hU0RN9pO01OSA5ytFyVBZRbv3cYwZqAQCxmLoCzMsNaBqefR/Ds/L\r\njoG3oeg3zzdyNWF8QdekSmVA81auZS4o2yD1qGqHw/NC+fo4Zhp8DhtNpkAP\r\nBmt5D3B17c8rcn93ah0fAoYw2j4EiyQdtaM=\r\n=ulCg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.10": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad3e9ea892ecdebe768b31841aead91ba295cc5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.10.tgz", "fileCount": 8, "integrity": "sha512-1zIgNr6Uwv21Dlw0q6Rrg1/0WhqGgM1Fxt5itaEUrRaJzsV/bfmNuyutPCgNXCjhEia4yPc+APlEhJVufCg9rQ==", "signatures": [{"sig": "MEUCIFYVBHfFaEdMLHtl6Tfh/VQO5eZfFkXxLcwYD2trHvFmAiEA60zlkTFgnrkIbt+sRyuGTymQrjdNNvSWu07ZhP80R6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPRxAAgrrDS0gClijTGkOlmyzkxS66FEj3VOdtUs56f+2g9sZ2LKI0\r\nINQRnU3B0WNAipLoGkCue9+VU7bOCgTHvPg5SZhfVrmQlZU0RWUqs26MTfh1\r\n9B2KLSfCCK1YXfRR59zNx2jvq6hXMaRHCcmzoozS5n4Uz+cRuGBFs06T80ta\r\nAxEvdjkTUNSnmjzXO4YI2t7HP9gC+DXeUaEzpZ7KNwIwtIXZcvj62viIsvk1\r\nc9r4gXNfqOW4v1Owt5f2tW6Y1YzV4yfvHFwVUKsMYxVRvlcsM+Z1SeAfhBIh\r\nCXrSTVzZCvlX1ZbmFTNKMkuti2qsrSzPMeHKazeS396avYlYyeVDowtuR3NY\r\nLnB/6MFBS2Z7hjQ2wbJl1zEpJ8C9zsvd53hLkNBqklCRQS1uSEcCst9KGl+u\r\nP5brleMB0WSolI8l78/41RdZb46MFHsX2RctYUWoGLoVCvYxDrT/Qx7JAz1x\r\nm0IDhOUl5H4mfTX0qAV5DcdyVIa0H5F4wTJAzs0L/i2dOQx591vtwWNBRbKE\r\n2oBahGOwVhRo24ZFw9JK5bpUYDchDvQFpKalz9jOGxDHoR/HI0Qaouz3glNM\r\nz6GNmTMxmbxbNB02aF5eV6vwosI6GlH7M7Tw4SSLIdeSkW6DJpMjCbVA7Z7i\r\neopMeZZlxYoGuYUjNUFXKPklpoP9zHGqIoc=\r\n=6Zf4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.11": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "495e881f66e7630d58671e6a2a5e53c20cd2ef70", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.11.tgz", "fileCount": 8, "integrity": "sha512-tZbe8e1gw1KuLDrO8DumkXBh+t/nm1iLv4z2ff6D6PER3A3EmyBXsdC/Fb86Z5YMQjUD2KTOKxar2ZFmGPvqLQ==", "signatures": [{"sig": "MEQCIBHpNc7oZsz6qa54ddoGniZX9oXCbxF44sHSmr3DolaYAiAqkzT1tCb+TlVFLtu+aIwthHGk3Wsy11X7RuD62yV5ag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokIA//acNM1+KdyZndJjZ6khtUGQY+4uNnK8JzEwdprs/LjDgESd2U\r\na9/78PBS2kjC/uItBWhmI/lTQuowzQsD+8RugeYQcPP0jI1BR48Au36Hs2C1\r\nCijwaPRtFdiv4o0YMdVohn8A14VNO3M2z5gweyCOaeQ+19+28iDzGHBL4AtA\r\n3R4XV/ZeFd5DS/DMjZu0adTLJsKTEy6pKOdjKsH7uAmPoWm5gQEN+oaLlyfG\r\nuB2iKaNFsUpt+/J3X6YkQKMuYE3hFjN6pLJSs/lGe4cy0aAfPmodOxAqvWkR\r\n3w29S3kXzltAKNQ0Jth8zfL3lDe8zsAUyluv6z+r8Ua3XmFgtgC94B/Xk/mf\r\n6fki8NyZpcOiDsE7UDPQtoA3Z6KNX4neY4HOh0a8KsnrBEFBhg1+rKENVh1y\r\nJaEgHp8JgiAwL5fPmsi5/Qloaxa43kyda0BR0xO73tc2OaQ+FfLyKHZsM4Lg\r\nv/6fupIEsEN5mCjFVZJwWnfEyZ6S8+v9hzv7xpt0zSgeeViDiSRB/7qg5utn\r\ntr+ho4bNSFr445YM/euhd8Kufpht8zSUTyun3y46AIiposKs/sLusGNktn9N\r\nEDsml5+h43hMmP9EDylt2a1rDNfbAaBCSMMjFcFUOIuS9/40sJ0HBp1Gh3np\r\n2dhJjv9c7a9o/X+urHMPqR6SM/TsiS+x67o=\r\n=FyFz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.12": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d756389dbeaf6b6c9ace3149ad480345f0e3e4ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.12.tgz", "fileCount": 8, "integrity": "sha512-us6k4WorBOePt/znF6Ipp6RaTiWOkxtDmmBmq0GShRu7YXpyC1vLlKeHRRbaEfj/speso2kHLe8gF3mzDFuo0g==", "signatures": [{"sig": "MEYCIQDacSmfreAyr3KbZ4JUkieAqpYi1wc1sa3dvOjwbOElsQIhANm5CKvbmmfLZQJrOnh3Ow9GKMYAK97ZubWB35y4wXjM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwCBAAj27HeFc3/62Y4y3qzeg3V/ZNU9yYuxZ6yyROY5WIUsWecmSI\r\nCfFk9diNGBVqi7eSIHBwJxVFxgdPiusSL1K544zr9vp0BoxasHpTvn8fyndR\r\n9Qjc+viFuPuyLLq1HV7ilDJ3+SpoXs4Syd+1u6riIsm4PoRg0IihiXquNxX4\r\n/EvUecr+5EggJ/dy8zOLHi90DtKN1kuPWFllZU96TmwsQnVKGTLM5GwFFyWY\r\n1eRiNxsbQsF3ZHI8m3P/rgKWtkb3b6+ZGCiEVVPhf2ShC360s/euAsl0bmns\r\nVKqJspzhDGBg9n/mo+IfFWXsCnpWAmxeCClKNZsnLjVVoPNdC7IKcscKIskc\r\n4z8Gc/c5GVTcLl/2IC//59+0Ss14F4d/iTt5owHt2BUPf/XvbIu3Tvh27TLz\r\n8O05NPO1GfFG8YJmu/viMm8lNmXs3+01lgOGM9Tb6ziBLuHVkJng+DB1OXrL\r\nK1ChJp0Sy5+F60jzBd7AI8O8vRem2wDwep3jCowO2egnwb2HI2A17WNb7O9w\r\noG8n0tKFhk4ArWxo/sPQJAS9L2CY5LBIHNAAT7mRt0TJLN8pSq5UntTRjYuW\r\n/HrELAiQ9PqTfyT+QF3eH6LqYYZSZ4w5sE+1fgn21/+zaCzmOxP03AwHncjC\r\nbV9lXa+nIPj0MS7hvGhjjlj4JH68U0dyBCc=\r\n=NKzy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.13": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f3194fdf9676a1d887722ebd0ddba457ebef7066", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.13.tgz", "fileCount": 8, "integrity": "sha512-nw82yQ45R8SnRoqp7EN6IbzRsQoQgILbUn2VOd8vvZO+Wai5ccgznbkrBTC7svI0HZC7pozTEJ36MqXVJx0iDA==", "signatures": [{"sig": "MEUCIQCklasBi8fAyuvTo9VRVykTSm0OgQSSKc22+3QfUogScwIgYQRwYjtfg0NBEDokNZNlKNWazGEg66wn3DB5A3wmsT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCtA//bVhjl5w1tAePpqttGl+VzYKchRmrHz8OEtIJw2IDJpLodbzV\r\njzIsA7eVrYr8Cfw0Dp6gOgADP3FofaCdEYDkOQ5Y67QLxpSP5zpgrVDpb8Dc\r\nji9wM1DyzcWnM4+xwNSN1bMLVNs4S24pu2EIHuuhHzZ/X5unZfRZOHU5UsI/\r\ncKXDRw0BmBVpoelzUJjU8BtJYjtUkuqZtzQZzwtnryMuxN9/+jdQNXG2L+7V\r\nZAY5bwf2GuXA167vTipZZSvomAr+ihTRcMANXRQCfQBoQD64sWZOHWl/C56Q\r\nP4l1rj2YsPzXfYHe4OFu/atZKdf0srtL7GyxzWkdvS36+v0MnoCXhSdaX13o\r\nMYaXefLG4Acy4sLQPU6uRI2cj+qRGqxwH2g3OwaMdpFO+2J1WUEv1ULcwTKH\r\nATJ/xlGXq7wI+55iN1PobApn1f+D1wGJScSCEvQB/R68F7p9DhquAYo39ay5\r\nte3cSQFHvwOps5PkMsniQA4tifW7ocV4wz359o94Rz35qQZ/0XC6TVjfBrlP\r\nOcppvWIMXjFN6xp+A/4Ja35q8jYfI42fUDKlEPnNCzBeIAfaMnqXYMY13dil\r\n8HQd8QGfGZ6DzrXwievuhOrGZyrDuB4TGjbm7PlImLGhdkntSHOpKSRKi1fU\r\nx6DDO3lO0tdvyUnHtY1eTJsVlzejzW+dM4c=\r\n=pNqi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.14": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2b18c1f753c9199fbe327b10ed09dbbae893902a", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ucBwo/Mn0UeZDsx90vcE97QEQ1SvJp6rp5Z7VB8bdAs46FGNi/qfvJdRupzVCcOKho6cicPF2HCfl9XV4SR+yw==", "signatures": [{"sig": "MEYCIQCVPMyv7MKbF6xO4h8NdM7WqwD2hsOwAf5XdYDCkE0MlgIhAJ5hp14/k90U0vslu9ae+mg0McFkDQAQID8pjgwnpOZZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8ptACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGOhAAonMQ9/YT5VB9cvJDOUOqT5wn1lWexaznBCvmzxHJ3deOV9b7\r\nfcI0HDmBu5U41sB/lomQoK7sTUE9DHVNtPQ1eJhh26aa/Kon9+h2/dYwRH3R\r\nh3a2vLX8bHYuX/9YEw999dYCMjfaj6DNFxkjgGDaDhirm/hKsaLSiTfn6vHe\r\n6dmPMdpsBAoxndCXlJFfGXxIX3t48YL7Qx1cn7eh4M7stZgJ+02Wsck4hmQg\r\n1DW1VmXCEfWvJu9mX73q44sS3tDRnkF5ZmzfwWgMiajyotmDSsEPiECjXVWa\r\nAyNlGC0RDYFm6xeOBdlnyRGS74O/lwZRhugfSnggMDpU+9FCw/xptlXxeeRw\r\n4YCoyRNyfM4qO59z202WisTclBL7Iq/7R/Vu9fEUpEYtaTr9ndU6sxNtqmzd\r\nxprxSYMrK/4GN+7YBGIq7sUNFl/qnPG8MhVCRLeY19J5JF+FxFtpZfAj7vK5\r\nN4ptmmX3//oRbV8SCQtV0WxTCfTA/yASgl9Jmq19J/QByPUDSKko4xqcSTGT\r\nYPAJHYJ6npRhykr2HEC9AD8q8CfK/wcNoy4VAklfJaCJeoaUFV+9aRCZSLas\r\ntb3eNrtcoD3fsxaGcE6ABWk3iEPYMgGH+ndr6FHHL1rXBe+1/RyZ3Qeb38YD\r\njYaL2BTKmpAKymvYKYFW9OtcUHoJc6uNUVw=\r\n=Th/a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.15": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d1aab37867b4405a5ac33f1ccd2b052dbcd501da", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.15.tgz", "fileCount": 8, "integrity": "sha512-Xa2YulxQbMNMmlbXdOViepA4Pj7j3C/+/zBLSemBd9FnUWCXrzl2Xj6RF1BU3OuBii3GKYBHqzVLOpzmzmphdQ==", "signatures": [{"sig": "MEUCIQCkMxEHg9Muqb7V8W8q8ornSVgYkNNbHykjSV8DTs/RKAIgUHsRCCIdsWgp3Six0c7cPrVB+aJlbPkwBG5bQNhJRVw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkhA/7BFWIcpm4P9Wqyos9Svd/ZUwN0jnO2lJOX0FVBOaVGTCrIrCa\r\nNMxMCsRFwjJ/HQbCnbKP/GlFmFcIDEYlt6WkWVbIT96V2aal3OJc5uUseRJg\r\n5AqwtkT5JTKtieuFy67dCEupoFA+fmLnVBbYpkpbnPDrbUiC0Cdm4wpB2H2v\r\nUDRcAi/s2EarokbLC4kKKhjMz/lZqN6yhju6esg+o5t4zQMhGBhLIWE4f41V\r\nCFlNwEssWm4WhHkwoNWkioy+EwtPYaJOPuOxeHDHZOatcijhH3ZrjAgLzE7e\r\nOHmRKhhxoxGlQKTePDlezyeSXYsTkVbz1WLnG/soKyAd1p4Z4Fs51Y6rDmvR\r\nVbEdMoYjzEgZdrYmTiOEAOBCOrbMiWztjdbofP4Qm+yv/sN2RHx1VU9YOwl/\r\n0z9V4wtJmhAEKjqQPsZdXvF7+br4dUPuGsqsjdhW+HSQBCyx2fxDHDlxuiZx\r\nyzm0AV5Zwd/aPPqlfox5fD1uZxp3rIrWIl93c2YePumX8M90pvr9emRnjJ4x\r\nHHV42WieV2bhEZIDpBOpQyLh7C7eyn5eIi3nwYfXL/ZcMg0E0rm8+v/J6WVB\r\ndvV7UIuA/e/gvATbX2MMB3EN0UpqZQa+w6l9QdvH/asBnE+gqdk3D/sdO72/\r\ni71WfIKbLcv/3k6yhUE2vZvds9DhvZDqIho=\r\n=ar3x\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.16": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4734c766923d83c58ca9a0737ad9c29f48fb149", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.16.tgz", "fileCount": 8, "integrity": "sha512-o8jPnQzvoDRVB6KWJ0Tnp0FO5pqqB+IlP9DJtyUkmVicY7Odf7lupHW7iS43rFfXcKahZkAnna1F1MBA2D9DCw==", "signatures": [{"sig": "MEYCIQCF+gRUn5s5YE9hcHBUk1lr7kiIVnUZ+MAgWSFel0soSQIhAMdyDwVSR3vMDzy79STwS2JnThqAtwJdWUwL8GKta8Vi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqO8Q//XF7sBvyQqvqJazTk6I7uOyfTpn2f99EH5inLeOAtWgoHuW2N\r\nYMiSd6Gwc6np1nah9KFDgeaV9oE1ZoivFQe3bZZ4idN7EChYPQbvTFVdX9pa\r\nF72cODlbtorijOF1UhFnLVwAFipaDc8TrkOo0wG1szu9E76d2gzpneOuHvYR\r\nAILUODPhXekR7vliDSAxAmnER9E+zuCH03wLHYo0u5OOXGzlahmJsDl9WXDp\r\nKiepfP0brNWNl4zpsvr8CF0n8ito3xPhh8wmtDijhP1j+6JhJYhCc1ycxVRz\r\nkfpw7cZhibnxf8974NxqIJ5yPH6yAtY3vZyIw7MaxxQf5uAaOmBBdr+vou3F\r\nNBJdJHVQ9kcLxDjGhoTVIjLpd7BHrXVyRCRJ+VvW3zZDrvqOXWXB3FhFyCoW\r\njzSH5i0N8r5ps/oHqC1HLlue10AY03MTkQZYlrrtjjSlrikFuGDeGh+J2QBZ\r\nyQKfuXEjqzSIYv67tV2Vd0pG2o5j+qpehdScIssYTO3TubdT1UQchQVj3SqH\r\ndCBwE6X6M3uLrauJbuaiwdbDUGaeOyQXgnoe1Tg8WdjORZDRS+lcA/kUhQMt\r\nalLTMO9m86gn8JwoxJTIzggYvTIKG22V2wzBZ8SZ5DbQFLDbzcpT3FwLdG1n\r\nAgQ5PdfEMGi0sqgBi1O++qhZfbWsEC6JvZo=\r\n=ei2h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.17": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6e1709690950503962519a9379e54b4bed9fa7bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.17.tgz", "fileCount": 8, "integrity": "sha512-ys4Vqdh52ZOKR7lVqx5GP48EODbf1Ntbahg06ZPxU45VBOeff29pLNstHOY4oc40detP9hxhoDVdxc/ZwCQ28w==", "signatures": [{"sig": "MEQCIFS2WODDYy2DqKP+QTo/zOGWz8nswvY/bA7ACUeLQP8vAiAM3MpQU530LUSXSzPY3iPVJjiYDl4u3/WVedE3kACieA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomXxAAjo9xNLv+hPr7APDJ+3X9KDFb8kkhwchx2O81JAd3WWYnL2r4\r\nAnAWsL84GFsjD9pc66vOJHISqlol3VKht2HShWMKJech+fk+VMFaBoShYQ9Y\r\nH/DLliBcE01OpbhTlCGv2IbuY/mL5porZVI8V0PZD+9Yi8bz8F4RAPk5EUO/\r\n7wVF7ZHSJe6YsaNFHWooS91WL7Q6vjhu867UUst3qnqqecAnvdux0UgT/T43\r\nMBdQyTOzM1mPJOJpLptN6lX8j8MhJFt70lM/UgiAS7sIKX5xuBSq3zTvj7UM\r\nt4YOfow63H4N/ieZic+erABtBE5pcgT2pd3Z3x58CciPGOo+ZFcK9gmF7elL\r\nAZt9IrEnwClI4EEnpfW0Nhzad7sdGgeyXHHI8RmZX6+pBmU+ewj/q35Y2ufc\r\niwqKc9eTYzymOgIcXw3gO2Kjr2WA7vpuXELaebP+jQwy06kxT8Wa7ad3bUSq\r\n5M2Pr8XW8xfOuVGeA8XgvbPBl54jjIsN5JzzyQqoXORKPjAymi+sVNea39MK\r\nAupEfGoZ4JXcFRZIR0p1llmggt6ZE3EEJi3QsIUG+5385W1HSajH/E5jjyJm\r\nuVViVmRkPEtFLaEGBSDdkvZELEwkaMSewgmBCqfz3s3HwNBJqbZLDTcQHwdE\r\nXDEJb5641/kpYlZRuxiVZiXIQocHSSQBZWo=\r\n=o0KS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.18": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "53994cf898d29f41246e4eb81c4627a5fe46da61", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.18.tgz", "fileCount": 8, "integrity": "sha512-hr1/71w5alELTUnap4FtTCSkT83HROwRr3+J+5NAU3niEeebdUJTpCBGg+hV7h/SV67vmIivZdI2VmPuWyx69A==", "signatures": [{"sig": "MEYCIQCIpiV7O0OuLUfauiaB/Y+tv/kobLV8I3m/fgA1rox52wIhAMYwIDf5xg/A9MeXbD1GVlXuoOiJk0tHC2/NhGimk4H7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6Mg/8CeDHSmerMe8NAAwQtq7KrsBJt+DzWNqrf6P8oiiTFl4FwzMV\r\nuAeCMh0qmeRspOpLjEIoib1TLVy/J65vEE3m+Bx9LgLl0I8okuG6uEHSy9s0\r\n6V+jz5HoYa7i+vs8lrRWBlOCfR/hFBTT0i0/mJ+b8K4FEYDPVhU/l4m75xdq\r\nGAZ3iJdUAufomqd6iOYP8dzcDGz3JJovUDLY9obsdaQBqf7+RXo3ouwvy8Q+\r\nuSOboVwmp+9ztM8oA0aMdzMdWHscKg1brZYQEK2xOeSHneftu5Tp7TD2/r+W\r\nJb2+ZBtZB8Qv44VAq+TuwCBGXk78EhI6GiigT5peY+JPhJJI7Y2vUDghxeKu\r\ns2jGncDfN03TVfpx1UXRlxnbjL3/LqA5RmrwrcedCDMk4h78/9L6ZFJmgVd0\r\nB2mIyjhct6DNtHVkL9KdSDnoM88OOIdXFWKpvUbKOkwvYD9PTVzesvgxK3R4\r\nRCQ/8hAjbLf9pc8kLgU7zl70F7iHOTaZcf0YiCTWYQJveM3hx/d5mgsjCCuM\r\nKFKyYXPa2ugJcqjoTNC3e5vs0LX87blhBI2MHFD4VEEp02znAlbXUSVhPhaN\r\nsz0jCr2WbZ4JzVgRhe7AcH/Cjmd/2/L0n8wfBK1bvVRA+MPanLf+gelaCcQG\r\nNqv2KOdSqkvfDOjNKpcGc9i+8bLXwuBRfe8=\r\n=s+BP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.19": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92f56b225170ada16126406037420edef4ec4548", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.19.tgz", "fileCount": 8, "integrity": "sha512-2NoHBnewY3SoLXMzcYCWpiDZxiFFvxk22ln50w40ikasuUtSu98D/79GhSRFFHMZ2Q1OHlR5SZx+1C2aZJe5Ng==", "signatures": [{"sig": "MEUCIA/hL+zHJoxyEKzhRfcLcBFee4HsHEk/eZFVlKySmnh/AiEAn5blhFHva2yU8Fg9xeO3OG41ra52e+lgQN0sA2hHKTU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAbA//RfG3mhKuZy+iVbxExJ5gbohejOa/I+ZyS89bFR21Nef7q/6G\r\np38tsdJqRrvnl23e0H58fl35zg5IFsBuamNHnu7BGdQNJVebpOy7s1sm4R+/\r\nAPxyvmRktH82z6vK31ogIwt+20omMMPysrJO3OSHBPaZg7Jn9fF4FApmQC6G\r\nXVKAJuUbGnqnsZAcDrN8Y8xoAYJOAfbvfqJfhnoTIyP9QOlWYnWqA6BTQZSf\r\nW+SW8S11P3eIr7yievpesLOCraJ+kdD+9kPRZlicnJxCsz98va6UTHxlHsxS\r\nTr3CFyNoNSF7F2PZL+lMmf6fkuo/03otYCZLK4oycmuRmHChcqEF1QzYn8GW\r\nhEqTkGEjTBcLgjBFIPzx9Ak8oVJBpwgJWcOF29Z1UzsFn+fEERRHGqH8DJPj\r\ncOXRjUSS8f6uDBOpz9Sx/Zb44xbNDPsyKj40TJzPcn6X5iml8/XwbEsr7evh\r\nh+sZXI8+qzFN33KrrptlX1ry7TDS+gg6brTmsMHP2pZdp/eo7HVQoHGDsEj4\r\npZSUNhrfbeq/ZX4dI7jDKBxMR1hiM1bUqtU4fQapBgONxvCZ/6iPutP/l/ha\r\n4fVtrnLOyrNHhv+BcBTOrL+jMjHBYnv4Aam71O1CcV/XKNGuze+qNjZj8Duz\r\nnpBbsixbuEB7bbpLv6kj4eHF2sy4RXRC4uY=\r\n=ImH+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.20": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2257f5927077bd826a4181c7dd1b659a1b0b7db7", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.20.tgz", "fileCount": 8, "integrity": "sha512-zEOXWdstYJK8B58yfsy8uN5nnnvB9sE1AB3ph1jXaQr1dK0EdAX9dqGQrTxY09PqkSHks1p5hZLl0N8s4+ZW1w==", "signatures": [{"sig": "MEYCIQDKVBCQQxT0gSy8+FDhu/tjtpUKp0nitBJTuOJLzqAeWgIhAK9fUo3V7A2u8a0uaJ/wKcA2AFq0I619ZWm5WGT/nqJ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3blACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT7A//eeneAxQOp6E1M7dFYnRRanprHDCOWY/q+EwU9RtEPaJb/03g\r\n8SMbVzQCVdIMXUUioLsBigWgWt6drcuh31hccaKXK44bRGCoU9b91uTYskEl\r\nNAshwFiJ62BnTRcR2IxrbSk/EYPy8oSpVGz7jqtlU6Dgr4tirw4PJ5AbWWdg\r\nTtfg+jfRxqSR5v6XLLXcA5Cv5W301vlR1u8SYqpCugjaomhIBE05sKeLP8fR\r\nAG934+wCvqPQzC0/XfR/Dc/D6k2feQenDXW6dx6s+VX2mtrkPbDmdIYA511B\r\nKoEwAffI1xO7p9hzNvhv5w6AFEG7sPNSB9/QCc1WyYmDxAGJMdUB5QOB9xrH\r\n5WA74nHeZeGIlxnagCrPZegjv+/vKHpSS1cN7ELRr7FP5xd0c3hmC2e3cFdg\r\naDytmmLB8iIHhsFmohLkJYd00o59xLohCe/jyxflflwoYuBGOVIKYzDc/+zu\r\nWBRa0tCj1VbYWC7CL9iTUxYOcdqHfsWC3kVzpUgB0R4H3AaX6ru2eHBv9Dqa\r\noWhMmvARfM6QAGo9vsTzc7C/ZSB4EHbl8BjalqALBWc/X65Q98xuyeDYkhKD\r\neMwtz5nWQm32c6mXIVEG4b98kmw1FnGZIKNjSHX9izzEjHTzUcNojef2JSjw\r\nUVmDDg3tjFq0C/zHpJvl4HYhxhLpMDA53pM=\r\n=yUqK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.21": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c3cc82376cfed56e0619abe9c9b40f78f5328d22", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.21.tgz", "fileCount": 8, "integrity": "sha512-TWrfaEGmd3aXQ5RS0dJM+/vXBLsNThX/DaSoxgcTzcQJ3eHdITD09H8QgnYqdfDlurOaCwIcUtpHSUNndgVLIg==", "signatures": [{"sig": "MEQCICO1c2DqmUGzsqwCB0T0iO0jFtZdkb36LnXQbwPs6i+8AiBWc2LMsKK9qQUcvEuW9SAec8heC0eEJgBO4a7iZX/Lmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRtg/6AmQcszVS8uOl0TOj/ghmerUj73+HWkwwXuQQ1nj5GpaziGV8\r\njotf2+PiOcmKWyUxPjCYUQn6wDL695e9ijBWWeR9P5NpyFiLpmVRgRpXd5iz\r\nQPcS07u8k3i45GAIOtstFiJEtcqPtmhM+kt+fAyPwi/jan/ES4XudkiIsT0i\r\nGsekXEycUJXoaVfbpU8QJBZkP//pZSz0y5yYI7QekZa/gKt2Ioo9guYdIm+n\r\n2l/puZW4kYzn1JKZNNy7L6MbJ7goL63NaqHIIZcIETxA3+yo+4AXGp4yAAOb\r\nuGekcad295cSb2ovbFtC+ox0YLN/OXOt++BeYCwB0sNsTWJayEbKZ3UmO/GC\r\nNIu3YQY95uQ2xdJ/6j9FdxrB++KAAZ3WyjzwsktCgw6Wk+AQGejw93Gc5nfS\r\ngS9+Y+DyudmaJyOEPGbnxkUeTTcfs6ypwYD2F0Yg3QP62AU7UmJQDsOVmubh\r\nt7A/n/WOveygCxHDTmxxxCyRnNLfmDPqMhwmawNleoJxtIn3P+zco606NpsD\r\nMeHFGtS6SU9xYExE+e0zG/g7La2SkMq+1inaWeVx50hkJtTUEqXNdwhHuagZ\r\nt11kffBfOoLCPi/Y37ps6VebH8ud1b3acnslUmtBneCn68DIWGp1BdHVq4nq\r\ne/TcNZAGMxjDyIPDr08E0/jHXVZ45rPhsHo=\r\n=nsWG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.22": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7ed02698f0930e90653e9181f8a0d645ab53d20c", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.22.tgz", "fileCount": 8, "integrity": "sha512-JsPTTELEMD7s11Q+El7fMPjWsQCIatLy8bsEUyXkWBd7Oy8HaMyUA+uCwXNiyon38sGIDKz9/wjfTopyI4OrhA==", "signatures": [{"sig": "MEYCIQCLLmjZ7y/r55qDZoLaY/quauxfDJ9+WzY8jSuJXkYS5gIhAO+vLlNlxQATeQxDz9o/OTAGDB05l+QG0I9l3nGpnohV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFEA//YR6NEUOpFQ4lDulG4mqj1WpUfRP8hLNQI192iTRabUwN+vA2\r\nHnBhFRhN7y8KJ0/QWfWdZf8okDYTmpit0GYduzSvtHoKeMMhWXu6wJKN1p3/\r\ntMjNVsCuIzewnpCymSyNcOSirY0gVbkmknaExa1gvRN/BBeK1/LDMsEGrEZW\r\nOXb0NV9O5atkNvWUwHuD/vcXKZeIjWvxFuwLzkGYfgzI+oeGA3CfkI04PuGR\r\nRgPK5XRM6Q6KNiGA2R8LV5bPxD76u6vDvR+oCmv27PYwaaTL01uRZZD7subZ\r\nqNSDF5ZtUU9ajNqNPCA8Ge+DKshqd7ll8loqeNQszni7t2BRNMYwx2bHv2tq\r\nDy8Zac880r63RffEiEaaIqgCV4rMDXaO4iF/JnLj0DOetadNAbEj4vfPv5Px\r\nojY3U5k0dwD4NIkA+KG6kKA0+N86o7f2DgOWN1hy/bG+IrFb6bHSMb9AuZ/i\r\nzXa1vpvNYuw8nVZWlGWmuw9bTlevTyCh25KC0MFWnCuT1YIxIaj8ss4iMv9p\r\nn/ACOOpQQC0Xu+iXZWbaFCYB6zUyezrZmCr8Y/Vptlgh2cIUXzJCmzHW4y70\r\nwiKXem/K/65OZWBSPAjEiZo7WAn1H5Rz+lT8orHueo+epTNBz/7/5eEUY3E1\r\nVl+bwh5f9enoF3S127dOXr+ygLAuVOibAVk=\r\n=lVdw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.23": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bfcc79dbed7fd1687a7e213c603ae76fbed4092c", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.23.tgz", "fileCount": 8, "integrity": "sha512-AUubve0b5aqwy9+DfxqUzKqF4910CLx+qyooSzwLPzEX7gQml43/bpCNCEWsncAcQ15b9Gj64/FiAfiNqhjgOw==", "signatures": [{"sig": "MEQCIFIjirGrwtCZuyKwEuKmgFrAqEXP1GhDIhg8lHzFZyP+AiBUNeFmblQY9KD4tyPvh2GJgMtgFvB3bUTdmEwT3pHLrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0cA//Y39hik5XVPRqA44ZUfC39HuUp7TI2Rt6UEMcP/u+IHiQa41d\r\nS3OW46kQLRdtfYWqaTRYcVJCyoWFoYJMtjmFMltAvaosFWoEMpYuTMoIivVM\r\n+JHBW93eUd7e1XgOI+MDEKqzaCUFL1bWevTE2AxYVm1XTWZJijmyVuxqV9Th\r\nJRG/lpgI+T0WlE7v+g6OG01+s+Ik+Xy1iFQ1Yk0Dlv8wuujzOyUm0MTUkZXu\r\ndVeP0M0kqYuC4DEE9NNndxj1UdqMZSWGb/oi25FWgtB8gxxAMIei1hFwxlFJ\r\nrjawOx8oXRPFcJKIVw81J1EupeFjnrNFacCzM9ZhF8mwg7bSAJvYKXaCi4cB\r\nYm/W0byUToIalW062r87+Oz8y0yZFQq70aME03ITo8nm8djrBTSXkkl3ZSEQ\r\nlVMeeJ5aARhpz6J4swQZJismyT6pyaJvPg6wHs5IaK/n1i9KePnIqv98qOHV\r\nm3k15xN/OZO6EIFMWHBZ2+KoFYtns7t1EfDup6ZTz9bIVdRsXJE2zMHWe198\r\nrALKY43ppab6T0YdO8Kgw/s6ygCvVosPDJlCXLFON2jYJHUxAqGBi6VtYM1Z\r\nWTTHmeUzK+Yzcej5jeeQAE7xbLfPjJ9NyODcnH9dDKILm62NIOOMmd58tzkO\r\nxxeqPbda9sw93K4K5x63iJck3GsBSX9p2Ks=\r\n=kN6F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.24": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d9125983f0e8a0280b46933e9d99a429512238b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.24.tgz", "fileCount": 8, "integrity": "sha512-mNZYHNR100ytmRkMNn7ComDwlQ/L02tOUw7H9PvWO2YjF8AljKpfKYvGf82aPYUUnQmkpjJuhHnomDC56+3GWQ==", "signatures": [{"sig": "MEYCIQDy9w4DyiPhgerC2G3esMU317/OL71IpxymKVcE2DI3gAIhANM8aaqQbETLRYzkSdquQNV0Qpa9qJKQGH+xNXABKife", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgGA//TRo8O6RsJAv96G6eYt2cLVwdcuPUHYyF8RXR4aCnk/xYYkGQ\r\nRujoq9dw0oJtRESw9lrA/KF5Y9nIKO5+QeONPpczawMCXK0DfwuVOdeM1QaY\r\nTKdcLmx+3qAPC1fX/rGZoeYaQ9jiG0i8f+w+pW++A8P02fCfp3sWwdlh7pjZ\r\nHNEphPDoIE6K0mk9SLOcsySmwOvpRtDHb2M9ykwXtrN46JJtQBlWIqpmW9YV\r\npT49ANqGremeqw7vPsdgmfDOJsa3iSfOwpa7kFwmo0Gtc6CNkVbtN9sq30Zr\r\nB63q0k1LcoFo6GmnZ6N9Kewpr4Ih7/Npz6KgElzNY+Dpb2o59GapreJM8n+P\r\njrECR29/N6zfqcT2dwJA4nJLJDE27MaVv+RnbLlj8EVReQSvm1rRHkIFxBnc\r\ngzT9eucTEwr1Pi2IctyxJmZ0LB4nzRGVGO5FtUEqgMNT2DyOr1FetsKt6BJ2\r\nEFLkFsywh/1QdlHK4rEVVUF+O9HVPlzlJbgcdxJiUaM28PlpnK2i5vuUINUQ\r\nTj9PJ28H5Zm7snWwk6TqZmLILlT2+77Dgp8YAzwWVPtNATgDUhbcO7ml5V+D\r\n27pw4xeSMfSVMGMFIzi1yQyDz1YSryCi/CUq0Zu5expb3CISSACwhZk7k4IV\r\naD5iDa7sKIbzwmJtAceTxqxoJUu/XZ367fQ=\r\n=Dhw4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.25": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74b8e40d872a874c1b8a9d6216fbeb1398cb765b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.25.tgz", "fileCount": 8, "integrity": "sha512-ZBFPW8vjkcDbwZb5waf1Wtms1k+9Nn/1ZXlWEtlaXyqUwMJ1XiTvl1lXoixrfpAHjPW0vDL+C2v3H/z2+wGYCw==", "signatures": [{"sig": "MEYCIQDEkG3BROxVTkg+mbUXH1ecfAT7A4cjsCJgF0FK7OFIIAIhAOw3Cjxf+gONGOJfyfI9tX23BJyEnL4M+ASMXOGVeAoV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj38ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRpw/7B4JZXkLpDvcAw1H/mgS5nbd9WjF4RiMnuXNCaUwzWFDztdo+\r\nVebxddQZ8gwu61fBw/GDR4keYeslzMGNfKCK8E1nieGY5pMofQrwJ+h2MTd1\r\nJ44Om5kTlOOpZPJmSP8VjrD1HakmYHYFh3e++HaEIrEHn3cDXtlz+5C5lfDQ\r\nsxwSte+/FisqaGn3xyrbTUTGb2iwc8zmfuGoWvLm5M8T7njXx7cNJROABbyX\r\n2lOdXtQvhhXldMTC+JJQabQcbOybrRHR8FPX8wDbiVaEg2yrHMnSv6kVSdil\r\n6Is6M7tA3cTCqmdjaOXReHt//6R2R6h5w4xOmP7hY7igYmmExVwV8P9Gvxq2\r\nQKr7IRh1U4wn+32XRjBetxA45YBfAfgaxdefwNa+x/a/3Wl5Fggy0bPxhJy2\r\nWqTCxwgliX3opANsqMHD+F4ZwtCcUY3dh15G3Wer1PqpLKM3M/NZZIUFTROl\r\n2Cy6iynMxLhagnNRE6xSjydkDm+0TesFWNbD/WpUcgFQHluU/04Uq0P8zy7D\r\nRhea0S9KxkWSnGodYu8xvu9tX3GujZtiz7vw+m2BMn15J98Ra8rnL+WgiGEd\r\n/jIzOxonZXmn8dBO9wi34I5RHZTQDCLAZrA3DngWitlRdwoHvpCwh2pcwmu4\r\n2pFw4yQumHRWnLwzkeb0N26/2CNs0Kbw0ho=\r\n=aThA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.26": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0d4710148ebbee69d49e5968966efc4147ccd275", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.26.tgz", "fileCount": 8, "integrity": "sha512-2bclHUgwmNfWwdOVHNnaL6ReOpQYcly8RpFeQjfkSasNoXhj68/0QgQ81MWFx/jm6zlFFggIGencB7WhDcA2bg==", "signatures": [{"sig": "MEYCIQCNJ6ec2cumm8U5eEzXRNINrlxmzN8YvQz2zgXN+EeoSQIhALXiL2DHQIQIQH+gZkSCVqFybac2zCa/yPzSdP3xY9vX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPiQ//alCWkyJPXnuP2Od7rGSRkbpweF3vWmenJDNbdHmPjzbNS2Nf\r\nlkrIpIuOFlmcI2+c+xvmEohc8Rk6rgkNdJDk7daTElbo+YDCVWJlgYg4SoLQ\r\noNkP7lYfRPdSZuILqgV5/YUZoAO4R1j4v6GpHiCvDJsqDchODv7efp2PgzJa\r\nSul5W006Y+djiSoMZ76n0ijByJnMg29RA0b4DCHpf/UwolugZK4SwBQKotCY\r\nTK9ELw+rvzr9SWK2HYIO+dOyHQndvS21IvezRYcCDCFf7B1AdCgnGodrOb/0\r\ntSV2SbN1b/phB8vQVsPJYDLr7PDfWLmVYDfMEKln5kK2wjVznsYfTF1JqHHv\r\nd/LgL66R8wUJ9ea8T6fClL+CYemV+CQK3y+h7YJ2QUSgb9iqtTnrD1+rrZ+2\r\nBgkJ1sJzP3EWE9lB1k2r+kDDuMT+703OG4CSr1klZKnI9t8IVcdw6S3ssq6X\r\nwFdaV26lqjI/Wsg2Do2/b5bRO6HgfJ8GlgwwLug0bxUUBXfCiIomkZo+oXG0\r\nInBfnpBdT+EKnir0AmljkQA50t0kAG8HjSVHmRtLewpZih3uEeR4EewNtfub\r\nQt9868+X5mrKy9360WOyCUD6WnD1YGYo1xaas3dXgtmDKUjcX0WQm7+QhcNu\r\nUXlkD4QcT9ihZH5LKqaeZlTgzO5SF4AK2Pg=\r\n=4sDm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.27": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d935035817697de0c68f5fdecf4b409007af081e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.27.tgz", "fileCount": 8, "integrity": "sha512-PLPTShOcbetensaXZ4oi1U67AUi6ZcueD2rUQgW4N0k5PFCAOqmbv1+i3fLSviWVDkyHeHxLIN+pZqs/EVT8Kg==", "signatures": [{"sig": "MEQCIGUw25G8uaNwy6+xifpWl36x6C6fCrsiF4OPekZbMQKXAiA45E+GaUL8cTQsgFo/71juuXm/CpHwGBFJc0hHW5D8aA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYhQ//VgSI6S9l1XJKcl6t/cuOY6rvXGgMCjuqUCiAkeyxvBocHaSu\r\nj+zWQ4VDiukO2DYMlnqaqKjIibGhlOECWnGgY05qp1YCzlbxYSSApEoE9ibc\r\nwpxxpZNM5VWwECNIJQtcBe67lwKQAkD5sWomxognHs9+skFbRh1WQZUdVvTs\r\nrFrH17PkAhejlGSXEj2a91lP+nQcGeqsKtPscYxyYuBZW3/fkGhIRcRTT9zk\r\niIi9H7Tzo9seKV1oaZbAL5XcfdKV1/c5A1v3XAchpjMFigAvWbpTH+Fv7qvG\r\nRGq/v6LzG9zIDUgNH746g3pFDYIguYgXCSp9/N4GFPvVDSN/GONq4cMe7jbD\r\n4mMXS3b8wN8QMfr3JLyoIuXAenSRZx/3yrkrg/TkaS3uDlx0Ib324UD9SHA+\r\nM0gLaXTKPf5Ko4jx71iJUb/U+ZF6z3Y5Yyzmqps5yzUN9KmKTZ/5cHFP03C2\r\nau27domv5G/3FgLcmemGyfd0Ttp2ymvEttsUNBB9OKQ/DT+xKBdBh3luN1Fz\r\n0lvO7NRrCSZU16PRec52LkO8EGj2J04taLFQgxo5a62nJGxtODxoaT2FDQG+\r\nYFcleWWc7x8p8QvT95QOrhCtZXYWbEJC6CDymrlewI5PTl1qPkoHVrOpF3kb\r\nwXXvGxE8KWF20dmxiXlcmVtB9YgBxphjbmM=\r\n=OMgm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.28": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c59e71e214701ace6623ab35c08906be410629ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.28.tgz", "fileCount": 8, "integrity": "sha512-XHlQnjXFK0SgLovnfAQ+eHBHcmC5jfHevgudh7kQwoCgemm5YE5P659ZV066POX4xvv2CAPvGjENMcZi5SSH/g==", "signatures": [{"sig": "MEUCIQDldGYckb6v77zoKDM1e22GvVp81AwVT/QTqRTFvQ5AOgIgJyiptwtHt8FfTBsf/EUX8kCV2esLRXGtTrCQZHmEvxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqiQ/+IlbBR/ZI2UIVliqBjJJMRgMNulIFVzIwZl5xnTYSaSjTDN8c\r\n6knzP46eiq74AQDNbIFgnxdRZyzRtSjzmBsnN1lkVz9bV/eDXVwZ/WLDCQBq\r\n02coP+eEvitN7SNRvJk1GCprC8UwmaoZ0oKPi6bdgUlUbAppIFYWPeUJNqE+\r\nLbwmbDTsamZ3VF/j6cGP28DnYCiyHzi0WdJGND0iTDSXWpnaVV8pywkglarN\r\njSjIJi4na/ctbAmxG2Mk9f7SapBazCQG62GJjrU2W1kx74eU18aXvkiviHWe\r\n43QpLHR2+UYPRpMH3wYlJY5UFhJa9HS7gtrrRSeHvTmSnev09JNG0IQLVm1N\r\n8tQ4Lzyh8SeqMtm5i4Zyu4Xz6XJRQYRfrvwIU2Wc0pmNUANPe3CifB4b7BD6\r\nUWvmzQq/zM/fWhm0p8MkIOBEew1MofyuvZU7kkAor2Pafc4bW9xNCwg3HRzn\r\nQfER8qGS74sGWJyr9kcVPJP9QVWUXPEcItXVAanv757RgVq0v1z549ZT+6mZ\r\nl/dcUwNtGQqb+kU2iROWqWL757CuFgWmTxYszYY4KoUlzUlmXiPcCQ7+SIV5\r\nSGNkPr74CJTaV37NrMJz0YHiVBL3YBMmiNW5KVdTxUfJMKMndF4K4OwYA+xm\r\n59cOaDV27g/JkzEnOP6cyEncuzw/I9R+jmo=\r\n=2TyW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.29": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "22394db07e3578b19c6613824736d10bb092f7e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.29.tgz", "fileCount": 8, "integrity": "sha512-dzDnJQSIo6NkN35nSipBMGK0J2JtaqZks9C82zPBsGDEjSkI70Se6bfmYRKPIAKRFI5YpcE9TE+Nfn0gBzzbYQ==", "signatures": [{"sig": "MEYCIQCwrOp0609Hvhr4/fLjO7fjdjzcBhkBBKpzJ7aP/H3xNwIhAL/lNcvlLphG9xr5Y3ALaJJdRHFGrBD5eHub56oFqjVC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqstA//YwytNqalwW79J6NIZ6Jns+q1qV65hxWIyIVULdQuoywg5+82\r\nMApd474K/8FVTNllFguiMm9Y39UWEwMvCX5+SSAWvzrvjFN27L3CX8cbfQ8P\r\ncNcdH4jsecbPOoZ3uFn2v0ViIGBFaC2kxfTcUp7dKT0aUbmIqNNRaRrlFeG+\r\n/wFCP7TAhGqsjLDbD9JUp2lbDZkiY1wxhDtvg4vyPS9ic7Hs3uW8uL2mKK9h\r\nT9YMRVijWy10ciDfiRL/mXdoiAZhV2SWfFfJxUy9z4rF6BpP04zNiJOwygTw\r\nLCEYCKFra3RevufTDdyqkNAQT6l+e5neAw1CdBwATOwjm9/8xwsN5CMM3TGR\r\nddG+lIDNi//78eW7C+RD1pELXJrQ9LKCDpZb9v5uje3gQlBd6v9h8uQ68lq4\r\n3SZmcuK+s+Lz5NKML+TL9CzeBJIMUnshSiMlVEWud6MUhtD/CGe8zRzFPofg\r\nEP2R5tJHxoPC57w2O+eBHo/jqoFPw0uHGSOLPMrCpt77IPp9XeiW+5IXsjyQ\r\nh7bj9RxrEyr0xX+OYlao+09P7m3ckOBczE+1kCvROF2HjDOgAaEFzH4KB/Qx\r\nEJ6YjkCAamBV2wTzpU6OzBPtcdM9e3MBrytL46mYHJdp8J2jytBQNOLumVso\r\nd20o3K1nVl5mSN1lerZ6u7/gxS3WiZVLwVg=\r\n=b2B0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.30": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4d5885e7d7644c19673e6cdb436599ca0bd28a30", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.30.tgz", "fileCount": 8, "integrity": "sha512-+LeFVlMueym6OyQyzqQ5N1+vni/io3lP4PuMVsJjd47N1TcI2rb5ZNKx6dbbDpBzu1QN41mCAeV/NJNpie6iqQ==", "signatures": [{"sig": "MEYCIQDgpjIH5gBqvpCaVD/bWBBiN87wIK1HmN1FMZR7jYxXVwIhAMWRPF0jb1/F9is1+eLGxZeAOdfM2jwqilf1R8t7AVo/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjQA//Xk3hF1I5gC/tftzcHmVyj+OCGTkplvCOuOtt44DyhS64yBih\r\n0Ex+2FafD+pgP19F7tHlFAw1/fN5Oi8nAdmrNkv+IyvfdOeGa4E0mu+Wws6j\r\nB0YYmtkAWe254Aur5pQfzrUZTRt8BXFBMs/Cr8xcNM5MD69DC2RqLPopIjVW\r\nW9v1fqwdfyM251qlTia6pVakbEt8bAIz/Uh2eiR05gXt8JnQaqUCLnz1+CpN\r\n0sITP/EyTKI652rAig3tdc3CTPmUnxtwusQ0XmgEyp5nVd0LipzjsThXfz1B\r\n9KOh4z7WgVoj//COUiyBxj94dKDJEJWgATOxGL1nfmbIZRtDJta1QfkQE1kn\r\n6DiR1eW5W0g261nL94MC67Zs/Trnb8kLnnIRgdcJmfcP30a3Qxn53JKMRZKf\r\n+3Y/cf49Y1X6VCAZmoXqmiwnuktH20quAyPPPZNzL2QgvTxsXMPBFY27YyeN\r\n4Lwk29+IEcZJl2r3ekI9w1B8P0fHtkUjFqRe7JmEancAn4lElWgI0mIvD5tZ\r\nPHgN5YwMq3gbS+dXrIlNl75L7TLUXqR6S7w6yKJfnBkdLCmEeahO8wdW7tPn\r\nnKXPObwuxj7it4nqgqqs7mS7VT4CM/IHI7DW4DhE+IBTkajG+dhB904h7DXk\r\nKmZs3xtMoZSUgrTuCwjigR0ArJTV2e1YBFI=\r\n=iaK6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.31": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "44409e16454c40702900945936b93a47ecdfe7d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.31.tgz", "fileCount": 8, "integrity": "sha512-Hr1u74Kl/5o0l/vBqcxG7TFM3/v6rdpXJbnxxZStWTo6vhIf38/bQnILMZDxM9i6V39qDmgtvZxhAoLI/9Pjgg==", "signatures": [{"sig": "MEQCIH1aesY8LYWCLP60waStV+qCmHE5BboJoVSAo9Mm064tAiAS/CCEnbb0nnw9CP+sF70JM3zYjEwcTszIowjCv2kxqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpAw/+JmB3eV4i1ha7k+ttJ5IyXFVuHkmzIs/pgOR9a63O+6aTD0Do\r\nU+Gob3IZUTXhYOu996FPdVgeLGrXPsWod8Pul928nMa4XrjQWumUE/qm4Mxu\r\nugfJLD6LbnTZgfkk6Ex0+JDGKYWlmx9bfoxPy9njwn5H1GmNwwWDUSoXxthO\r\ndWohH3E9+dCopjz37YP0UcTUoM1CFpvqrhJScU2VmJHsnILHVZZ0DeL81knZ\r\nh/E2gPpQ0oF/MJZzZs9GEh9l4e8WU32iwuPBza5NEP4oo2NLDG8NWTEtr97d\r\nI/KSHtZmq1ydkFnaGRQ1pEbrKLaEkll3pW+ASk3BQMeTDjdTgjBZ4Hdwf8ZK\r\nhi+wDLdJNMkuq2+JJSg5F5eVj25BoByHlkfe+ABEyjs+g+UTQlNEnYYpyOQe\r\nfCtwonC6HwNLtmsM0AJDMGsCKF3LLtrHnwkTeSaBiPDOBsDvnXBy/yyLa22r\r\nzmhCv/rCtZHPyFLYOYukBR/MOdT34ysmhBVPIiX4YEjQzReQ8bZcyynG0UOT\r\n5TF8aHx9OfZkBZxSA7AI+GJDOggroC+fhnu1UbTwzE8NPvdmyvU0FBp/WNUa\r\nBQqfy3y/iSQa0/5FmslASfTCxRL/mDYR7CYS/xy9IFoagSnZJXNP2ka2LrCU\r\nWzYyJEe9Bqvv91lzAysz1JynH1bNpGfpL5A=\r\n=gKIO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.32": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d9e7bf586849e535650f6295a779dbf81d3bed45", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.32.tgz", "fileCount": 8, "integrity": "sha512-jQ45he1n6CwHr1nL22ZFrIhIEaLSzGOGQ2WPrfY9aRVy+nt2Qb2u/HpCTv96SnDlH+gMNhpTl+HXyyuPtle9vg==", "signatures": [{"sig": "MEUCIQC7KGaFMVQVYdFKoWBaCAFoJesJbaK4fEcylZ7AjHqLQAIgchTYLk+UaO3N8MYVDITpumGkWVhSPEipPUpLbln0lng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniR1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA/w/7BGwJp83rBUfARqqVQAZuGWg8kuS6M+DtLxNx5+ves1vPFq11\r\nxkN7Hv9Nfxc/qb0n8AC7UyUc5hsBboK0ZLKFXuruTe9EkcRAJsGHRnnG1R8S\r\npDSjHeCCjkwyLSYfF/6qrfX/syFli0F2Rz1dAcHn/cPbe3fkIuCxHwj9vUl+\r\nPZoNZXjRu8z6cD1Nf0r5/qhNxv1ZhbJtISuywr5pH/3i2pOXaTLdcdSDO6+o\r\nIJkHdiJesLkyE925CIscBrbyOSqyHNuXpdAn/6cl9w+YidwCEJSe0Y3h3IVs\r\nPkk/6zpmb4J3Mf02kkxqxsg1Smmc5o4DiP0Gt6nzmaX96LXlCQCF42+7dbeL\r\nOzuKo6i10NFRwswcWeR3bH9q6v7fQ3clPTUyMC1AB82lP1HiYibE+xVvONYJ\r\nwa5RFiG++RnEt26GsKpruTEiVxRIo3kQ0qmoFanWk8wdihh8yQPUvYdEx/P6\r\nmuDfO/Okba9itCd4Gy39/HExKNYwQUyzCedDePfUsR7uB26UJVL34H8L2kji\r\nc0Rx3rJYNkRc15HF/4Xm6JYMdZmtS/2BFngDGqX77N4RvLOGYqg8+h9TXyy8\r\nKBHGxfF90CDP/xDoYjp0wZKaDfQWd+d7cZjf+w+H2NVEKwu+8dVBoW3HD7CZ\r\nZcE7B3nygb2CgAGvzUUrGthbMLQvtJlXByI=\r\n=+xL+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.33": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "501276c31dbbb65928da17d7262970c5c8bfabd2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.33.tgz", "fileCount": 8, "integrity": "sha512-zFva/YEZmgvBna1OHKVrdQnFCN7Cg7yMLD3KBk6TI2o/01EPnkieGEH76k9aRaguQF921kOUJ6gHhp5IMQ1CAQ==", "signatures": [{"sig": "MEQCIFH32hLPCnsnyjSKiS6b0xj0fFlILd2L9nsgJKm/jtUmAiBcGaCFnXRTS/8fgbf8iCJ4O1mRkmcNUGAzGfUJtEIbQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0nQ//f3+AGd217tRNKjeL1uIHRhYfcu55lqKsSD5HbHU5TbZcr/1X\r\n92TznwSD7xbr67lYfi0Gq/wc6H1CRfXhpxlvBt2HaGkkK7YZ0L0geqfWGeg2\r\nl+TDqk7LX8vWIFWQNiDsxlbtrWOgfOADRYmYohZsucYi/iMG2dUmoC+FWM90\r\nQK0Mu/9mWqYo4xp8AUAumthZTvVJ+ewOwmnR0AJSzp2OkkasHiPJxGyHNS0P\r\nQO9ZmTjLaxrAGDSuSElW1ezWC2tPDSLw8jvhW33Q7tDmU2G16r/mmoQtYgwf\r\nRvVXR+7MOKfp+WwtRquFnaYwP4GcvBQxhp60vnM7YYI/9y3y5+T6whi7JdGz\r\nLh6dHI9g/fIOAqQqH64Rc9Mf2+STCaf/pryW3er4JfVUuB0pmzvlJhJsWdDO\r\neX5dfrr6jGoKr/g8Q6r42k/7272A8bY/qOQhr5u9OmFl8U1aErF89ghLCUw7\r\nwESXAQBRtGw/N5qg0Ld5sDbe/conuLHSu3MaaVdI7AqsiTdG96xDT9jei1/3\r\nCyVit+pVmwg0NihAgYbJVWqjaRC4MRn3BCk3m0SY7gUntbrB4Za6BrPHT9TP\r\nF/j6VdrZacwMJMUvbkYaISHy1EFLmncbUw5LQPMsW8M7+bZtsr2q4YBHXhSQ\r\n0RmAUrZP3CTzsGZKHZ2kb0nOjQChH+fp8A8=\r\n=QE3n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.34": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6a47ddbad1dcc42b1055b4fc89f0595b6ac0286", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.34.tgz", "fileCount": 8, "integrity": "sha512-ZveEyHHL44pJy4KhxogW+4fAnJQyJkeCaFjTYEmesoQYYTzWTBjPxbRP46uVSAPyEZi2eXPF6xuwMH/2JdzLnw==", "signatures": [{"sig": "MEUCIGRbBBBjc/aCENLA+gGzk4GBCy2EPM1DD6pJouvttlyzAiEAxrcOgSHuY1y7BC1vbxJ0LRfbE/Espd33gSRdZoOlXCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmognw/8CQWOFrPi7E2nvulQb6LdojtQo56rm84pvKvqZRqh94amst/S\r\nxYM8I/d63yWWXr2P3SZUiBtzdlsTyylpvrKkaSGvI4gZHg1PXmySua1D3h1p\r\n7z0K31NMrrt2V8iOReVGYzX3VM7d5mC1wWoGuBXNag01F5lbBS3eFdkUJhWp\r\n1cX0kK7Pw2HTk4nDKmVdK55SCEAwAboqiIrZwGvGQiUPBhyj8rDxzGoWJzOG\r\nQNnsJ5Q4VuGRRd0loZWeuDGBx+tuw0Aj8dzqDwQ5Fh+UspyNjAr2JPRFLrYV\r\nznyJnEs1KNLXGyAONNBd+AsznfsJgjOE1jz38uAC+/6e4El7em9iKsm5DHe+\r\nIv0KQVCn6WWdhAs8C1oGY8YdkhRDvxr0/c4P5qtRGBo4yfonj4PKfpMSw/RO\r\n2lA2+bDmfy+o19fHBHzhLEBvhh0q2NCy9RMEJ/FmAKtlPx7BWi+MYBMKfIt7\r\ncsuD0Vri6q3+zn5LcfsOmdVQAxntVrsNnWsKS6qsHJSlCSG27St3MDjzhmKH\r\ngjTVqrGPXQ40ilmK1Exx/7OtAzif1144OtdYWJMyMo0RByFfjapROMEYc44V\r\n9l/VZ7ipTMFiKtjZ6ayTl2TtF6Ykt7uVYK3gLiX/oOMSYktiuRoH6UE1z6zX\r\nNShe/bombCtHnCuAP6guMGi57mxmK9F0zDw=\r\n=PZsx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.35": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "074cb1cd198ecf263aa694d04117561a2a48f98f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.35.tgz", "fileCount": 8, "integrity": "sha512-qr5tC3KXhGLh0bg6Zqm6oDfYXzMgoeM0dmbHUBDshwF5NBSSRmXn1M5i5g3RNjHctBS/eIgXSAwoWgS5U24zaQ==", "signatures": [{"sig": "MEQCIDEotI458u+VSN2C9KjQEPjf0aCFmRotF7DdzOn2qRQiAiB0v/nMyU4+8C5DJzAbQM811OyHK1mZRfeUV1tAyci11A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOY5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqDQ//SLWxbBpimo6E+/Cxf7462x2Z7MA1N/mi3yoZxU75JbQdZSx5\r\nA4pwP9Tcnq9++b4cy9sWnC3RhgD3O5S0NpbjP/AcHNpCtuQxB/pSrddmIpYh\r\n1KKo38Up7g8seKFZYuNqZ91QBcxbQszpcsVTlYRNiPieahHKi0etyn4YvazH\r\nECr2MqMhvq5Um4rNZts2R0YsudwyRzw0SVN2o9j0dWCD/kzz4uDNnAznF1nM\r\nTg/aZAnNNSXIFQVbfE65iK/cRRBIIQHqb0fHHj77V3TxLUHvtCvtAboPvr96\r\nOq8P0BRyEJqsnJygxo8nzTZAYbkSDh/R3W/HeUYw7tuBUnHZy3z29RKck3g0\r\n1RvUQ6upLvErsI7yBhzKzEUZ+mzFdu5vU0vmUlfqV3p6QBn/nqWZEMTtcdLP\r\nV4xofUeOYfmQTfv0E08KrEzpu/ONpQIKkGvtIVx3Y6qvfwyUbIvH7x3vOfnu\r\n7yigIxMGwA/nlNCAxEPCVL2of7bAixHiaO35Xeq2+KCp7+ZWPb6caaa+8V1H\r\nu69pqwVL5kvLWxp72foOlSntU1kqVs3wwbU3WjNCbEw8KDvARiW3vswUIyI2\r\nAKYyN/uz7E2AKUL1o0bemBKaKWM8I58fK3Ykt8i8hs2gT/mLdmt6r4ya9tmh\r\nHivA6UOu5Lzk2WYhcTz5XqsASEvkh/hnVGQ=\r\n=tJ9L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.36": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8f38c0390d7830d47ae0c2a82600ed47c58269bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.36.tgz", "fileCount": 8, "integrity": "sha512-LG8BpyCTSmRCCydzzePCQ2o07LFoRHyPPWwqAoDukgouqa6u7ANgTx+ejKL+Oa2ooF3nWVlzTmJCjs5fzgHQDQ==", "signatures": [{"sig": "MEUCIGvB8w76eESbW8IJwvTRBQBsdx1ZUH4O6JGClol0KQe+AiEAzbFdVaFxVSiaA7bX3ZrlS7N3PrL+3SpVMcupDwplpis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrz7Q/7BloadeAMYMoq7XqRFha+gNYVBDmFR6xuWWNK+mi0tzTLimEC\r\nL1BEVcGI5I8Wg5+tKanRzP+pQ/DlF8/+9mIGTo3hSwGn3lGZkruxeL9zzrTh\r\nOBQYuIFA2w4sGR+pg7ti+IuzDulhgZ44XXHIw4Q7lwqnAj9Ns/4ri2icHukQ\r\nvuwq/BuP4Z5XmASd5h50Kqw8ZUSYQs8LxgWfhEMAt38DfUiOYMvv5Cc/GtTi\r\nD+cimclfjTVggeM1pNY5pQ1gnSznuHb688BcCUAfRuJQBQV7gCY9e8Ghilzo\r\nmyfRq7+0/kjUVPwLEUprtM41P2ydlDgO2HcWXYhnVZb8TlhVyrpIcgE3svjL\r\nya5OKHdznFxmpctztyj6kLJTyvc0AnvNISMtWZ1mXPVq9FZINn/ysxAjsqIw\r\ntyDBdNjZ6Pthw2JZgsJPSgjHhVSoQj2I0SQSOqcLgkss1zj4UHJ7ibKrz8cw\r\nvhpfA4K8u8xYmvBiFPMAYl3WbY6I+ZLxSBkxbK423qEXadI2ZlcQFlYTbBMQ\r\nj1Wetc+iEyOHn+P0LZ+C569AE9LPKomFqkl4udkQeB3w6LjeBxjRY/YAWZoW\r\np/BIKNR6aQ5E1zLdVzlJYnteTgzPqxy5naNwA40Vbr7WhXlrparbEeDAfLFs\r\nknAcKmkycCohLYrG54/v/6FzJEKRlFWWr7A=\r\n=kp5O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.37": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3d09f8560a194354d5d32edaa773b2a0f6d91869", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.37.tgz", "fileCount": 8, "integrity": "sha512-m6LY7TTYh/W0Pm8huCOxLBo74GHUz5yXgKpqGFyB0Uvow75GLSnHdmzr/mgrxr/hune8NYVpHRYAe9VtJEy8vQ==", "signatures": [{"sig": "MEUCIQC/0puDSrFZ3YLEv9sYTDUjRc+uo4ue1hnhlNsIN0tdfQIgTdGY8TYvNW7j1v/Glx3xzReT2dNU2jWbj/7h1uXsceU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0n+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpthg//Surf3HMxTGvSDMzFahzr7XIdmnUHjkbWP7BygBUjhmyMiZBQ\r\n9HlrDMi1hNwCtc/LqtdPnZ0JwhBREEX84XVFhbLhvsmvG86QKKeNJ6q8N3Vy\r\n7OybKtkQ9XZMlV09d7a11dxxCbW9s94qu+2NyUMUA3vsxgjoPn11JiBZLXZJ\r\nCyx3/qzXSWRkI0YUoNal0UDvhX85VGex4H4IoHNixC8soX9SeqntCT4tSLj4\r\npcAt/PyJgBrX0vexIVS0BkngZNzmAhMvpDucv2QqeiG3FReNjb7xl3gHLoGW\r\nEYz0br6qPfu/vkPVv0bjZG6hIGLS2nPKERUO2RONhiLnkhgJPpSTHUukMBKU\r\npt3kVGYIo7TfQRRHgbm7u+FmJnblMJDPvDdWkbC/t8Bx2vd2hj71PESO4URN\r\nhTmO9KEEKmmQOjV/LhHgPUMIrG4ywtznO8xMKz4sg2VxWxUMq3KQvvoVGMY7\r\nOG1DPpucBNMY88tR41PVxw9EmTgE9Ku3oZuHRkW9878Qud2Cq9/dKSBJdQXp\r\nALOtyDWhzfMWslJQ7QiGDiphyhHS/DEN8MN0M7c+RFglUe3hxoiKkgGIHT73\r\nkACzvTHLBPnIGoOi8OKa8wGTkp0Fp+JXMJB+PBQcutY23LTPc3b/ufOvPXsW\r\nSSskVzH0LfrkiCndaNbyObRhfq7Oiy4/lWM=\r\n=a6YJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.38": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d9d4dcb517d2915584b3127f8d520b47a7528a14", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.38.tgz", "fileCount": 8, "integrity": "sha512-shKxg+AgnsGsaObLtXhI/2V1hAU1xlDJ733MD28oV6x+CUuYSjvHDNxUMPbkHMyFiUgN0o1e1XWXUmBnTVp0ew==", "signatures": [{"sig": "MEUCIQCT/FJTte01PQrAYytG8/75ruDPo/gbF/SCMdaifwL4MQIgcYwA/RKJCrMbB/D6Iv0to32hJGSHF+k5Cn+7TIEWdJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzp+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+OA/8DdyQrEoQn49y3nmMVk41/EgBMeDPtJxvr+UIPbLL4gf4G2Te\r\nt/Ggkl7LJsuF4dA6+uFjDPyNDCXt7CGgMz0Dn+6IiKrZ4hLjnVMhhljkFT4/\r\nnJzIaWalclaPlcYkhc5HwqTtoJ5jPcxr+LXj+ytNbTq3b9CEvA8O2KIkZ+S4\r\nI8tbJEMVJDYLe9mu9G5iPe0NO2zul3UDwZo1po/C5dRhi4G1NJiqdwBExdD9\r\nSK2vSPpToASO9Y44tmdMnysAP6skQ0gtXXQAVDFby8XBFAWQQ7ouY6n4o6DR\r\nAPYJ3qV3x6fj7RZaKpVDqeIvf/isYhJSJ8mGhhZQ8cNXY++pzPPugZUMrgx4\r\nxSb45XVqwx1EZBjJ5d8v/qMAAwL+gR1XVKWC9wBL89ZjsJllli4Or3li2zo1\r\npckBK0Kl0/BmIc3rNqjkY/YsrthQ0tRjWWjR3rcoEuXpYEsjlqTCjhRHwrpj\r\nFeK7N44/hh1hxsnDB9rxUXCF4OiULEGwwqgwkgAqYJ91csoa54fSzyMBoiFU\r\nPBAFWSrFg17AHxvyof+dGb5ADJdjiCW2vEz3viHSB2N0Nj5YXap8uSj7k4+H\r\n40obDxLzJ0wd73M8l0pyTmVNlaAcT/S9RYLZTzG9Vy4cHaFcff5MR2GD8z5A\r\nXBgC7hAy08ubxRjNolvevayioZ3QbUezV6o=\r\n=2PWu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.39": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d40c00280082c022aea108d9a271a725cb1abb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.39.tgz", "fileCount": 8, "integrity": "sha512-cioSgL1m9A5on7t1EE4tKBsAxs7WyO+3/qgc59g73zZoEUArWiVdJTt1M51SetKPTzsS4E1p4CzZQpOJUot8vQ==", "signatures": [{"sig": "MEYCIQDUSxltEEku8hBWhs4eIaEYe6+fPgTvNOyYKw8hs02WFQIhANEuodY635VDPIWQ/gCfAtNY4TM6g/jF5gNEkNwOJ1Sq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz95ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoF6RAAlN6e98wGQQ/vr5ffZQBn893x8y0MGGOBdFqpr7qEs1FQ1hFU\r\nMU4YwA2ceO3kjFW3qfI2PwN7RNV62RLz6YtQPzVtnkVe9x3yi497tk96YC8k\r\nu5lUnK0Mc7hSKS80bzt1Aainta1IJS8AmoGLuN8BdpWTVJuYLDXdJeEHUGKd\r\nViwqoTINwmdoXDnbtlopBwowWaJ7Fio5DSHyesnN6JQn/l10XAAs6NC/mw+H\r\nSCypYNzu2NrazV47lWI0mY0atELl5Ve3oFnLsKRUKM1PWxqzq+/jMNqsTX9n\r\nsCdIAMhfDIIAg16A05tg9BHb2eevCNjG747odJv8S+/kdsPwgfhpFqNS2zgF\r\nQba1MG7gy5bIaCI2ciM0z+679Jph3XFEIJJofGn5exX0WY0LQxxDGB4VMn54\r\nB4VLkD/8g+y3QDNIYSMqQirtrJupSXBDhQFd2jZywyffD79u+CqyWAX20z85\r\ng6sg0D1cs9mZwz8iV01jHBaLV7L/GC/8OzV1r+R5xFGya63pp8khPI/zTCc0\r\nfwp1+gXcWyHnzttxM5yT2yZh7v8jvG4dOI7gEswOFiJZ7t/XkT2/P0b56WtD\r\nVrCUa+hW6WMP+RY32YNnw0pbkPXWENcEingpCO4npkM5askPCcHvRK1CZqtY\r\nBHT8e1p7LAYBkXIAHzBLvKErX2ZvspQoj/c=\r\n=KaTP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.40": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6877a4071a95e24c79d8197f90f002fbfd4e6bf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.40.tgz", "fileCount": 8, "integrity": "sha512-kN3UX/ux8ajawOcnAv4q5hakdEdLj+xOc/HdRBSZxWxuEuhtV8u67p5d/2yypKWlsq/wbJjDJ11O90lN3bNbTQ==", "signatures": [{"sig": "MEUCIQDDB0Y9VsztCdz5KBN2KkK1Kwara9ZiRKipcmwFWf/TxAIgbN1OWhVJ2Mu0tM98jknp1jgKPUXUbqB9168ErFMzvE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgjA/+L8lK6iIm7wJjqjOUNrL1JC9dWC1RvnCdQraT/K2/ac0z9ZP9\r\n5/+QT0fm+713OT7y0ifeQVgLchp/ZVhK1gM74Pzb7BmxptxJgXA7FUA3j7ki\r\nxZw8HvN1XkhjjC25yWqqsOboLPdHF2h92PCPjlGdj9OyU3B3QsRjhx8tKDeC\r\nhyEzn6lxIjLn22fxVZ1qNfU/OrgHu0JHlX2WmZ46reB0E7azP2PGefUUc7wJ\r\nV7NJ+LbEafXjvAeAdGj+YMqZsM85GRrdG4sWtSP1zLBLwZtSNH6Dc64wsEjD\r\nY1xd+sgcH1XqIoqk9ILo/W/JaOsrTBbS0iO1A3NDvQzwXCt6f1VL666rD1cX\r\nYKVKzRa3anIB6ziU+FYIUuGbO4nl3PzeiSdKGhQD4nXJNrVaf0m4jcp+g71k\r\nzd88Zh/iHLYSy3NAx31ax78E3epqJjSUPLQ+ZrLRwqDJ7uTgTjGSqTAu40Hw\r\nMY3auu4K+V1SYsD7C96hiE66o1MnY+ecAMZ+MWlVCATUicEduPhmU6HvYSzJ\r\nPyWB2XXDYAv+M/uZ/wP4NTKVK1h/F6i/DSIs7VUEXu62YL2aqX6XnVPJsW2A\r\nkbnQoVc/mrwgm5B7iFUdyZloKCWk9K1RZI0W3Dvosuyz1d3qn3bWZTIy1pYU\r\nOkahOub/gCi8l2UTqPY0z8B9qJf+g3/BGAM=\r\n=/UM+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.41": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7930fede4b02502cad4dd749997de93f498b0c95", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.41.tgz", "fileCount": 8, "integrity": "sha512-PxvDZ36zemCAUvSY3tqtjt6gSjJitrnTz7jHGlsPyzjtX6OfvSLLZ+WRXf45fGeSUWhCS/SvlY2/InMWboNFVg==", "signatures": [{"sig": "MEUCIQDzNTkBAfvdWkCB5JUoIRwlcqsxVFslZKU18S/HA4zhpwIgVA/cuPf/RXgEWcLjKCLg1zG+D0xiT0X97/yycl9BC1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/0w//Za7UWtUqjXBiOQXyEtB+IelDVQYd6xqB8BE85QnMBSVYtv5W\r\np/D6ZvR5jtakF/TrpWxGj/5VptDWLwNhku0tdogK9XO551oECoN0kW3Gautp\r\nqBqRZWNKNnnz1eovoMAjubpu2eTESX3bvAEgwL+IUYqELvMJYv2G4dHDKut+\r\nEw0omZeLaO2TI2VsmqXX4X1TS2jXHr82/2KQlZoZ38biOgy1/MGChfYxHWzx\r\ne3o/MqZcyYHNJOevX0rW4sxpeJvEaQGrb72MnpGTwviCXRzP9sA2rglTeKaY\r\nioS0Mi8KcIx8vKSvjHK7xwtAIvBQDEv11IssscgP8O09UcDhHaEFIg0SNRiG\r\n9WaMNiNDMsY2EEbVoZUbF9h0/v5I2ojG2rhypOs87weCqK6DMHJqlRMwB0w2\r\n0FI6v7hDSWJUr8C1ZGujoVxMsWN0vN9HRT2AT3yaAti+c8qdqJZzVw1lK4n8\r\nsaKhVfj+14+le2UH9N/7lhAtNDpmVIt3BMmcc4cVBLBhgNWiulNhmQbqopsX\r\n8I5FMwEXC5sxTwO29I4vBz1wVI1Fvio5+fweYSV6Ol08AXFzOVARoi/at/rF\r\n24TjxNG780XhwzUDSjrrdokfZHt36mHL2W1UkiELcsZx7trN86Gbiqzh+p2B\r\nLDi/k00h9HwXumfwxYHrLIoQAvyqX91IRto=\r\n=8z/8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.42": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "58e4c5b9fdd3b5c9c3c445de7155c38c09f1f2b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.42.tgz", "fileCount": 8, "integrity": "sha512-3jcUkhp12QK7O6rI3VgK20DM9KMtboimTRKZ9JQqvIgzGBHEnuwdIBYyd2AK+4a2chPcYPb4VoGy5mjLahfFTA==", "signatures": [{"sig": "MEUCIH+g0qW+SvcOGYZgkh5hLnjtKd743e1+WUoxxFBWBTcLAiEAgvfClfY54zFz7zG44JVZW1jjkexFVoXssGL0NZOi7/s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvd7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUUQ//b5MahJmxKiiPA5Lh4qdAbkE3YYgDuwDArEt1z0nvoZetnf0e\r\nCT0h4OlSgqfM+OTe8FEToEuq354cHj6qN5uqMpM1JFGR4JVkG/rwPJfnPN2E\r\nMYZoy9hzCC9Uyg8Om9rv01fA6ldYEVHxZ8rTg/U34ynQeArSk7uFajbqP7uE\r\ntHqtjZrKE0T6yFQqRSRebgaO3mIuoKrEZ9WPIXzY5kp3OEDmmhBkJQ3gGkEP\r\nGOsnKiJbKq27bYp4x2/Wt1TVkIJWN0Wm81azsACP02m6s0YImi1AzVBn2CPX\r\nwnGIRjUQBs+BzHjT8ZiDz4NB9b7CECZzk9Jmw6Vv6MrDudKv2yEYPbaE+Yj8\r\nDDrOHFzqITBksw6pKCnUorwKzT8MPL9u6+5kFfNI35WbhfjlW2Bli6KPfF87\r\nJ4GIjfr7kNGpu0/KsF9qf5MH3RBedsmclm8RVYnLWczf894PxYM0e2PV0Tlb\r\nlPr4aPl59UGQ2ArvArXz6Qr8oogXsHp+Hbw4SGKh5kqHGQfHczIo4Jq5MY2+\r\nZStQvtaJf73K2yt/DXb/KIMsd+F2ju1+sEPPMKkq6YiKkBmGDkbuf76kVy37\r\nL32mZS/N3+LD1guX319bdga4LVlBanwcQS8ZT2Up7gf+nhlttaI7vUqXLDcD\r\n/vywJ32SP3mofMsT1HxvGzA+ANnHymyRgdc=\r\n=PI+v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.43": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "386bd53eb1f980229f4e0433a1dbf6e7b732b4a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.43.tgz", "fileCount": 8, "integrity": "sha512-/0kwlE1xxe3zo5GLPmSOQ9XRb93OSc07hZxUVRpgmpqgsp5zdVsSVgLtJYMj8EN/SEvjny8wlgKpf598Jg7PbQ==", "signatures": [{"sig": "MEYCIQCS5s4OjAFEoTawgJoxNQeAGFQe7gPrDB5j3/7rB5cWTAIhAJ99x38LvNTbOh1fpJhELTPGob44ErF1fScl4p/T8xPV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrauA//efdQndZ+51jPrfwSOtfGi2nKxAqEobSyLZMw1K1ddeF4O1wL\r\nRMvTmYivF+Gk+oPIw2cEazCjUESDItElJQ+bLYc6yv92+3k9XIcmEqb4CjXc\r\nNkt86ZPmzB79DlIKqbLgoYcSMPJ+KzSZe7Hc9Kaw4aMVooY9toguG0Wl51iv\r\n2j94iXlSDywF0b0ZjOf/Be/FAPiY0RvDxdt+MOIZNfrQSpUF7m5kalMzESdI\r\ndqvKyuz8tlBVRkeJds0Cze7Zs2TsuVQTXvjf1GotpCB4V1+isWeq4BwIJi8Z\r\nG0aWCLUH+COmHedPV0z5i6Tnl7bo5HK6CAILrPAOxkiNoh7vsaHwv7S6rnhR\r\nMaqRv52CzSMrIqdHntvb2aj7mJPw081VgXzq05+fzX/fhDuOFdbvJa4tyF6e\r\nvZWJ4vyrbPXqJQdqVe7prgXfo5JlcSabFXKPBAs/iaYeX6u1EkbtOzJhnIxJ\r\noqPbJJfkthy4U9MydbuWxE+AdBKLMefziZ2A47wpLQ9dmw+7VaGPjXX0Oojo\r\njdhL50zC9umMJPEdivqpEzzMA4OfVdh+6PBKwjdg9ocmkU+OAj3cHO/MEwIk\r\ngbMxJvy4cRkmQpIXmbsqivhtCSn5InNzruBAU3BBVDp9VQ4+f6SPfpLuDY67\r\n7EHDolIXBh7Ri/zm1APWhkzPTBnSKg0nGsg=\r\n=y7tM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.44": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b417ab26fe0011536742c95638b833a17cde2c4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.44.tgz", "fileCount": 8, "integrity": "sha512-/mnqYL7NenQoUNWctUf8pWhRq1sAqZeYsXkqM+BtYzxYenwqfNDgo1mRVXFHZCChu+jHXBD3+FVvytznm/HMNg==", "signatures": [{"sig": "MEYCIQDCJ9IoakAeHALwJ44BwM1i7C7NhE6eba6MZe8n4DFx2wIhAPfsjDFw1TlfpbaEFJVfpDFT4HqPGn0IYAsGywsMrtei", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHCg//VOMzYmh6t1T+krzvPd2NC3pB5IFVrOv5TjZy62Y7HgWlgYeQ\r\nwUtqE3qQzmX/n5uRqfD5N4Ot8Toe2889L79XhWcnEy0/g3/yU7E488TL1G0R\r\n6X0wFLMDqrgELG65kwxVL5xTIZKqit180X5zIi1ozWZeLpKQEcMHXgq7+YvL\r\n46JHfOIVf2P3D6vepMZ/0DQdeHQnWkarm4d8Jk93NcgpAbl4WM2T37nwmvzv\r\nOZ47NamgBOwFR9BLSNIsvikxZlramgZwBDf533TiCdLQqWyayXDSF1Y555xI\r\np0HMstpY4/D+pPHnRVyBAi5TzZgtFlHv6scKjre5my4YE05lkEtZIAT0dyWM\r\nhUW4U/nfRxSAj9758CHrO9wu0hNsJjSVQzWo5kBey4Pk2Hzo9TEC7YnzgcH4\r\n94pgJXDgp91Uxs2HGd0AVt9UGWKwo4VuoilJPTHax/n5XupewxwqRY5Ai57t\r\nryrwPzYUiM4Sf/L/KGYjyCW23ktvdbY9aXOFZb1guDzpoxkuWdsipzX4QhH8\r\n0lSlrxhwXPhTMhhJo6ByKjAmrajP9aa0b+IPNMw05nTD+rl0Zm41nOXAsuAm\r\nmPyuWlUEOJvy7Su2OhCH4P52oOm2v8dKkKDWRVesnEdPWFxjoFdqdxJ7XyHJ\r\n4UJ00lHqBoHL06E/psB0qybToAvEUTpaMNI=\r\n=n8MR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.45": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90e000ff4ff20e09c973aa962d5f520f5a629b34", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.45.tgz", "fileCount": 8, "integrity": "sha512-bWBwTuGeWCtYJllcQwB8+pNDCq0AgMJAewlVcMzLhIUrHtYaWWnBQb2NXo3eeanC3IwGA4pNeMA3jM8L6gZUXQ==", "signatures": [{"sig": "MEYCIQD8I5TiNtYAlZQYeda+5Cdyo7FaHwxlRqG6ThxONCBjWgIhALcZ6TjarVNicp21/JLSAUHI0OsILuHdNl+7o4ndOaic", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoE/g/8DOodB4PORAvmI/+fWX4J6Kt3vKJtDLJQ1B+BEEbRa647T0L6\r\nDgAbaaNLJxZv5Y7ZwBUGASobdIXF/J1LS/pA1YJCijHyuv3BLaDecYwht5nm\r\nT4ftQbihgHgLjyvMDq3Z4OjjLk7Qey+OWZOsg1JKM5FdroV9nGyXg2uoeXa4\r\no9QpiqPWF17OcfNazH2JID5K5tB2/ARlylQgq2ZvFjvEqs+PBbjTH1ST7vCF\r\nnUOacEeh20BO6s4KLy3ua+roO2DnCdU5HvFHmljAtWlUCu6f+cN+ztE3IhWG\r\nWXx9/SLLlVD8HXUZxwhBPQDnsPEedtIZ3rEW1hBM+cjnW6r/7/JToYWpgKs3\r\nLYN21wa9koAfNZlgtNqkhDvjlFBWLEIQA7eQQlGjljZ1QmiUPQ0mtbU98wQg\r\nmj2NJPV3gGHxCpLxedmJcafhaFJekI77pYJ7brP7PhbvHkGDUrr96neRHqqA\r\nyOXXBLdjvJdv9lnppbdh50Fyc+kTJQhipJhQic0M5e95J7HbEV44IMEOQg3i\r\n0J1qSyiscEto1He8Jq1QGWbNo0rvlzLBjssy8NsMPzIj1pLNR22B55OMww7f\r\nxEm1ZwbVHo4L+vbMTTrXUsLlew66wuaO7ylMO7OQr4++jqC3YJBl84j8LpOd\r\nMSgyXbzbnEytWne8cfphvHfeLSQbw4ElPO0=\r\n=Z1E4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.46": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f5f998658d824e4c2679118be2b378a8de80dc57", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.46.tgz", "fileCount": 8, "integrity": "sha512-hK6aiDOCByUlcUt+aeWRvZjpotd7pIHAtqwRGt0DK7Pi38hOD0L8BR4dc2eock2shVByNXtual0sCSmcMo3ONg==", "signatures": [{"sig": "MEYCIQD5zJrdAHKiC3zmljhO/vqHtrzfUba3jGSqRNDC58LGyQIhALyRqWA3lsRcViKr6XJDE84akGrPjpI6+GkGm3ThNyd7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdqw//flEMLBUpB4Zko7uCXHUzBl2MNDxCwFuYeKaVtDzjszrVkt7k\r\n9Rdb+/pRMShBaLOAZshnnJvJqjkMouI/3s8PBvpQe35JplOtEXeNUcDz1Lir\r\nur5C+sablQz+4TKZORjJXeWooINOK3WVk1sKDYYP+cF1zFNHaMtTcUJsEzrZ\r\nwHmOySFyZEqy6VpZviqLF+iSj5Z3JFu/ksA8bfCzU1Rr4PXZGXjDKFoCpKrg\r\nIAjOXn6sefkvCoXNg9QTGv7IbRi5W7E3tp123DG+2l1AX4vpWC4oPwlZh4ff\r\nwRe4ZYzZCvLA5QslppJV7j4WEP0zi+4PUDm1VJSU1NvML7/hFZEKIuapFeMo\r\nNPiRTy2TZ0W6tiuuQ6oH1QNrZaf9JTwH4Cx2S+MxXP5O7HBPTve+Kptouj9b\r\nxKpL5mYN2Y6UI6KtVL7h5pGIQQPehcoD0p+DM0gBS8TiKQqWeHitxHJIoUmL\r\nci6gtAefyrozyG03vuFAVLtmRZWnw5KNI5UiPXa+88mqSMVGFxGARWOp3idc\r\n79MBGd8ogwvIHDKN07Oe87bgOutkEWBgqN8LiIAffTxtEt7isgqfutIhBBEz\r\nKooP0hDsFyLuuCU+0x02cgZyrIP8I53aEWJAazlufI5O7onXHwdn36HcCa3P\r\nyKu2J9l0oBv0hS5t+gfmD/S32SFF1+Sa2OA=\r\n=ECDz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.47": {"name": "@radix-ui/react-presence", "version": "0.1.3-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3343e05a030a3ccbd877be0be32aa4a327df6924", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.1.3-rc.47.tgz", "fileCount": 8, "integrity": "sha512-7Ey537IfQvXVBq66FyUEVfSPG5/4k46by/3VTmKgDYVD9MXG1TSyML7sJxaphydDAYTqCcPTFcWNTwbMDGiMvw==", "signatures": [{"sig": "MEUCIGoLU0EDXQkFotTvQp8CEDWHOvYk2Xqi91eD6oGn9pKCAiEAih9qXlk3WwHtvNJ6G2BV36pwD7KzdNGY+x6cH3VJpjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38656, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CEOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRjQ/9HpBvMgJwfUn1wSWKCIVkKNp0FXtjxjkVt9gq0rYE0hKQmCW2\r\nQgaeuSuHyeAhImynONttolg1aUAfF/mswe3rMZ1iBCR6nkMZfVbp1hO5UyPc\r\n0k/ynx2Gko+eHu90mz4HG7fT8WivH4PVcyYOpnVYydCyZjKRMhWBlow+oVlY\r\nmkYKgPaisO6/olDdlgf9CFPgqV+8HN3d/DvAbpd7sgroLhqJxxDq62SPey/9\r\n+Z89ETwd/XJ/0pYI/hzaESAPESuuQsTyWrBIzNara7ey76dEbN5nJl6IT1rT\r\naOoFKNU57GMojZp3Lz0hi+JpGkaVe//iR6jSczPU+L0Grz5d/uBHNDikvceW\r\nmWFxHtMwRshL4kmPxRvUW8IVo9nSOewmfEkf5K7Ytw/YFxQIER4oOyvYrWA2\r\n36Qqm7vF5JDefZHDHtG7V7m+3lLAlu2RRSCTAK1hm1On40KKKHDNnkljVmeB\r\nUVl0X/2FzzphO4BqZbaRvMiVI2CukZOd44Xozx0V/4BTPA0oYyk3yF4LuOJR\r\nsOt9pzyODQBrGlhERwKlYvKXoV7wUrLhxbicHV+mz7AYiXLxNyCBM6k1j2wr\r\nG2F93HWU8bzjJKs/LpeP8Z6VJhB0nT3BHMYy7pqx3OuMf21wdLn/J0Mi8DZq\r\nGtSu2NiBZM+jz7hkYxPSeX+z3tgqjvH+Imc=\r\n=yBV2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-presence", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ae8a683e2aa71bece85dd800b2e4204040a7999f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KmBtNc/kZSUh9luMxgT4bPxgPqcHVkeE8sLp23mWzfbevnz02F8+JVTSNRb6iWkHOXbgQcXb3iSAO2s+QcjkDQ==", "signatures": [{"sig": "MEYCIQDtEuZICvCO7UuVcIPAYQdxz0QciM/Epbv9eNqVVKYuzAIhAM5+/8AE6ECgechjaHaap2frsN32BvTpAOVQ/os/l+v7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8Vw//cWnUKU3Zko0Q4gGi8hCuNQrXmc/fgRRQSJw7kWK/lXViUG3u\r\n6cMnirQug+3PpXYan+Lv1zZCwjRm8HzdA+rZPs6l3dZFmCoWRsOtABcImwdk\r\n8rIetGlqUuzMKqGooSEuysRflpbPGGbzu/riGUd+06WCasax25QFKjUCUZ7/\r\no+PrKSg2XPebCDvVYwFYn43F+rshIePUbsfsCMSe2QLr1ALph1qvtGNIFE8H\r\nSqy8KDYD3KqHhmvKkbkwX4qQgk1u29YCqL15OD2xb6bakGuehjEamOstxXGd\r\n9ZTiY5r0PAzofo3ynpkLA99dUCFNm05i0H7ebYCghalLE3L1bTjebh6AXKZ1\r\n7rJMC55DoURp9JQpECwnWviB7dEXhVu5HWFrmyC7u3q9pISTBODv3kMKHP1p\r\nN0E7Mo7mR1BDkoJwYZaVFejyWjsAuTfvH3mZW2JnHMnBm6uDetQtc7vqUJnC\r\nZm9J0bMEN0ZTFF9RYfhx/pRA94aC94hsE2otmh0ZuKcwH0HbtUWWZ2qaEjoN\r\nDLM2pVrZl5LMyeje7qHsOFbDbx8eicSjQB4470sGeLJex+n5GVbwZgHXhvz/\r\nipBxBJSez1d+Y24yBq0uBf9NvLffSFo/CnE5rEscqg/3jDeGmi+3I/8J7H3g\r\nxgVuLTy+B0C99jIziydCw/MYvQMX4jL+DPc=\r\n=qEdS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-presence", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "814fe46df11f9a468808a6010e3f3ca7e0b2e84a", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-A+6XEvN01NfVWiKu38ybawfHsBjWum42MRPnEuqPsBZ4eV7e/7K321B5VgYMPv3Xx5An6o1/l9ZuDBgmcmWK3w==", "signatures": [{"sig": "MEQCIDFmO9yxREEixZ8/24ndze+dVNOVLgjSWs1zndiX2ZT1AiBiAZu3EORexHOQLEJYJ79OrmHwVgYkd3ddAiPfyPpUTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38610, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokuw/9ENL9vzplw+8FAvjA31FneXUdomTa6fY5xqwVv7CsadiE02gC\r\nYoIsyC4w6XqzQkZADggqzkn3BVgO396Og4fBFxqVccboPOUatoVL0jrq4dLi\r\nyTwBZ660krM2pY5lduc+MePwkg+I/RIa7JOtiktiOrUW2mHYlMZtmO3G1Ji2\r\nEZRJ3IOLPmHH7Bzd4AzO1eN2pQkjuIo/Xj/jUfMjlW5p/ICwkSYPmGCuPxTq\r\nIfividrarNcrv2Ja3k1O6Y2bvUd9l2LEVcpACfBiwWhyZiapPx0EtAotDbbG\r\n9K0vvnn++2zhrQ07DM1ESL/2OIBZXj6/sxHmgFI6g561qRm3akRFcmihnl15\r\n7UZn/OrSbDdc/t6THHRnkVchSnRdOoqr0yh+xm/S4kTiX8t9gmsd60AtK3NU\r\nL+qRWIZCvvX2gTkrCdNRdUm6ZpwrwV4gsjlMERzpQeI75HUU1xJ01NPPwA4L\r\nxVE3zpfmzBAoFV74TpFzU6TXh4oV/3hsonMBuFV2U8boMo2KiNqYRFURtNsQ\r\ngB6BggPe1QI/+ZJ07j2inTkqjC/e4U0tvLZwohlMJPT/mx8okKVgSCHv162l\r\nXZ9r60soDw85AOJf326WgeeA3n+S6zsSBVOanhdJIhcQAAszK+7l+K84wDEy\r\nKoJVJJQ5JA2RD6RqTZax/kWlClPU0TcmiEM=\r\n=ng15\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fc85098f408f9c23fabdd1235f31c9887bfe5f97", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-Iwi5UGGeACM08jC1y8NkNKax5WVAjk6U4AL8ZRwpMWVsF3qTpLNFpYvAAavs4VmBc+qThAG51PREEw5IT8vL5A==", "signatures": [{"sig": "MEUCIHbRqhNiOWhaQseHu6i30Aa/Qo5raqQACqDXgqbUfbVOAiEA/F8NG7YtkCNIGabdggKEzHBNB4OeZwuVgnhTNbkOM+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39163}}, "1.0.1-rc.2": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4391a5f25d910e00a168b455e3ebd520ed226dd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-57qyWYxGcHGcF+xY/6sHHYeag/Kuq8z2t0O0MbSROTtFpRUapNWj62b5wuBh3GqOFQU7so3UMM8Q1LE0u3IbPg==", "signatures": [{"sig": "MEUCIHeFhbHpulDmtkPwAsuymY5+UYPGZmjWQrUuRNiTluhZAiEAyhJF+NgOWYCc+keXCzH3oViq/FhhJWYm7t8DKIzOlnE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39163}}, "1.0.1-rc.3": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "11138eaba30398a1929cf7a0c977678c329fa216", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-8dTKRFf1cJbkdggLVf/DeHzY7b7PNHZ+YJ6RWdxmunuTM/STOuvH4X3IzLC6UsxmyFdujqTQu+V/RGleUcZ6iA==", "signatures": [{"sig": "MEQCIB06GaIMeovNMY6ScFwHfBYv0q+rqPBak6AmEi38lQXJAiAngbXix1750F5/4FS7Q/8Z9Wpt/m4ELwB5EINdwtGFQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39357}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "71a1c9d2ea6b5d2426ff0658cb3b1b1f5202add6", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-qDl/FOB85jYQg8dMMBvfk8nZftgvSSLTnKvZuoTPv7cUA6htQ4K07Dt4yjzjsQeZJ4eZTDkxQG3ndT4RxTbc9g==", "signatures": [{"sig": "MEUCIQCIJH+SmQptF3W5dKU+zqWNdO9bUK0pWn9b6qbB3fnLmgIgGaRICgPW7F/rBE+jRZWOgSMx6q/POU1w1InkRBMVYDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39357}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5f2660dfadb828ef39cc46d477411c482984df84", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-T8Afb7wpd7bzD+4ge85+IkwMVZ88tPZLUnWqnKfwbiCM0SjSsYe8ruJrb7ZK8TyaMAk+Pi5VmNMKWUHqd8sPSg==", "signatures": [{"sig": "MEQCIH2i+O23n1ooijfOBu4rdjX+QgON03vmiTVRd0Wxa34zAiAZLriGahICifXnxVRliDNaVOb3RaJ0DAWFXzLjCQYFyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39357}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-presence", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28504cec53362ba9341827d6c7171dca125d1748", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-+PkSIr8UXa/A4XyQ+4ah5Raniz6PEh2jv/vOLALFcRpDV906D7bvlaiQ4yHdvr/Fw04iJZrVf/DlNTROyeuLwA==", "signatures": [{"sig": "MEQCIDth7vitlwNUTjKiVHBF32CL1QM8wYuwFg/hZM1RnaDFAiAQQrQvDn7s6SwnX+DwoTn4/Gjyk0ZlrsRtO6UTA0kwgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39357}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-presence", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "491990ba913b8e2a5db1b06b203cb24b5cdef9ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-UXLW4UAbIY5ZjcvzjfRFo5gxva8QirC9hF7wRE4U5gz+TP0DbRk+//qyuAQ1McDxBt1xNMBTaciFGvEmJvAZCg==", "signatures": [{"sig": "MEYCIQD9Oye5GEmfebSP9y5ZB7Tr1A4jDkYrA598v4bddsuxMQIhAPWvh15iXpWJkBWc2qNxDs0KCPu/+U6/7S+tQ+N7xUy/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 39314}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "15d420e3d47a682631732a87536f91b4036d5471", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-5sGqF/qCLWlCWoxYK7WvOTYebm6jIfmhOatpv6iCorvBupa8Uz25CQoCGO+3eRvTEbXPBCMKGERn+qdBBWNjoQ==", "signatures": [{"sig": "MEYCIQDGUaMc4lFIj4gQFTyBHog+5BKiIOqTsLZnoRi80sXoTgIhAJTxcRn090UPceFneundNxKWNd0+awRXS/uuxe5WfIdp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29994}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a411c4b432855ff49d8075d5d5256748c108ee9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-g/Ux7ScX/00hyKuWs5kXx+gXQNA7EP+mRLB9Iq+3b+zMzey12HeRduy+mVu267zatfQ4woF74v9H2KB/s1YtAw==", "signatures": [{"sig": "MEYCIQDJtkjgs7q2WH45XZMKAmT+60L7RIXVSOOfuBUliw79BgIhALiJjRIMtkEQz/fGp77rm5Pt2dusgfyDSVNW09XVBcps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30026}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b06322a673fd6aa26f9fc0377acee8445b17f818", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-cSL1i8l9p8oxDDZpfu/Kgqb1dk0tRkUMgtGiX3UEwpW6IHh+5pnqPTDKCYbiAvwbLbjM6c7fURgauPykPorIZQ==", "signatures": [{"sig": "MEUCIQDF1Epa/gLQISbnmYbE4avL98QC0IrOZcproiltYKONJQIgd3fdMpRcWHC1JeLas/7l8ARUV9kk3zcTNlzXTdkQojY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29878}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2dc9ef227b38ae14355bd1f02b271bace6e4986b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-xhpQBsG7L/NcRq0urlnIrolAz7kpSfbDj166X/8th7M9tz//Jc2bDOqTfs9UtqfI//YILSSvOpi+9u9QXVVDGg==", "signatures": [{"sig": "MEQCIBHAfukHVzxnoAqKkDP7zQaOSEPPDQRnOqfKu/5w1CntAiBKdbjo1606eB145/nPjGUeXcqs97//qSXTpS2rAKeq6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "60ee85073f0927b4f361f57d41c8828b0bc9e114", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-736xC7eLlbqRQ6cvsT3vL5Oh7UF6A+aVyvzgGIlWnpHs3qHBcWgwFekgUevoCL+4oRqWknTAGzv0nch8NLSsZA==", "signatures": [{"sig": "MEYCIQCzR0PUxr6/CB+DZ/IPd2m2MkzSXZaqJf1KOiykND3aGAIhAIhx2Guj4QpGqGiox9NXDr+wR5YDKVracMDn06jBj2rB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0894391179f9ac7cb4b68ff2b1ea38923f669b6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-6CK8q1UYhUkqPIHxWbtf/si0YVpcCrMusuZfJ6Ot8m2cogpS0Ndt5DcnxUmSl1XUjIRzgIfSt4qXLK5GXrnSGg==", "signatures": [{"sig": "MEUCIQCLaSKxeVAQZ92zXVbBIK97lMFYUf/AJtO25cuNmTrCKgIgav6gDUE/KOojgi1WzTYC/gCF6GZJZN1obn6j0QR7UP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33218}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-presence", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6a9114476445672812d2f3a4bfb6d37fc2377947", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-wb9zjIn3FzNioCIjX+3p/hbgmTOZ4zXQPg30Nwh4sacNJpuw2OPSURoQysBKi/rf5WEi8/Fmn1RiGd1T+8nVSg==", "signatures": [{"sig": "MEYCIQDONSMH2EvBLYdj3xo7M6vxAGcnxjimSlZ5RwbfP/eWiAIhAM81OT7pOz5xfqN/Uf/7aVXpDFd3TA0pJivashk6MH69", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33246}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-presence", "version": "1.1.0", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "227d84d20ca6bfe7da97104b1a8b48a833bfb478", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-Gq6wuRN/asf9H/E/VzdKoUtT8GC9PQc9z40/vEr0VCJ4u5XvvhWIrSsCB6vD2/cH7ugTdSfYq9fLJCcM00acrQ==", "signatures": [{"sig": "MEYCIQCA7fVXiQ28gBR5T/CZtvjivZ0nM0y/ZyEZXGWUttKUOAIhAK9bpw4j/NCaKyxOMbYoy/TnBemSY3+WR+Tir0nPzNMe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33203}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c670a191623e21c7d84d461e2f5b8a1e86ffe7d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Q7mBnaYdbOdCr/yqZtHjV4dLxpItD59tLOQnrutluJSZwVUFKUw0XatqWxFNSKT58fCyYmz/7/l2sN7M7c0+rg==", "signatures": [{"sig": "MEQCID8tDeQdWjeK1cngofaU1xDDM+B399QOFjuZu3S7OEbAAiA3J6p9eOQ49gvkbrUimyFfuVZH88OzgtP+G6uDSfqGUQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35397}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0105f8414365b1aa29c659c77e71d0af22eedbe", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ZCj1pkKD5TUN+XXfQRHK+pGhgFM7e8TpZQoqEsyss/vpwzZZ4iYuRKg2WOlnOqtADJs8HgISY55H7RjxvfGr1Q==", "signatures": [{"sig": "MEYCIQDnIiewcTP/hMEOSKNiwzQpxLuboJ+MrB6+CAVPvuffnQIhAOpBnPvWG86JGiStkQ4jlTvOizseN0Rg2jYQfFJEEq5L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "069d8b26f0aecf9403799c3b62016190822bb771", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-5ixLd3aG2+S+Jnikv/d3EAhRRj214z9v61Rpfaf1SMnGh6TVmDcYxumjKr3oZCf8+oRVUBqZEejGTbarqmSdmg==", "signatures": [{"sig": "MEQCIFZYw5dIHshkljECRX3WIIBlXx+0NSKW0EeBW7ghRRyzAiBWDhSpwXceXKoV1xKHNZbxDWygpaII3gl0VmssDsc+Mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db1a5f1211feb297f1246906b6e6c3a18bf636bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-+IptglW43C217YlykaFS7FvpCO9lN/1vscrEJkveFZ4AY6uw24dzAGbqQF+tvnRYUluzVoIy9cfI05Dsxad86A==", "signatures": [{"sig": "MEYCIQDQTcVY8Xst6nQmgX9q6UNXHPAMlIwdqkUefxTiOdjrwgIhAJpVVhiHdbntM0moHiQNQMxdVwlw4BoxEJdPrVw18YHy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b8c0d7fcc8bcfe27f5426f228b9e42e08a7e2944", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-BUKUHGsSR4LfK+cWTO/uhzGnD4QFT+gJtomQBe9X7WTUP7L53HTPOSYCKG54RBv0vAFMQvlYfz5vqwZjy6EIkg==", "signatures": [{"sig": "MEUCIQDImeMWUCZum84B/NBkCrPv/Rw1mgZapnr2K2TWc/OzxAIgYX1dDfEFyG9GLpJIlrNWpYUrZynJlkSbu8I3CCaUVt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3d3ae26bcda0acc94a97b5fffa3702b4bc4c1e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-RgxqtbMcnwDH4I7+eOTCE+UNS3/ErSVQuoB99DbMMN1NpxDx73zLL4Db884yHyd8slOF6ryqC0v8BNTsFjuZGA==", "signatures": [{"sig": "MEUCIQCRYXF7uvjBPSqY+0s5PQD7USwGz/6eC0lqPHtfn6VVFAIgLxifaWO6qd8cClFdzBRHBr6XiT82vcLz7VvwBSoWnqU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b04640745af77b02b3b4e1af9497a4ed092c23f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-lGiFjLmccvzlhwsdLRL+ey6H3PRyvF206cwX+uG70J7ensSMELNva+xsvOasgt2m3o486G3x6dpCIZYLzOu4dA==", "signatures": [{"sig": "MEQCID6hP7cADG10NME+2MV5+8l8PsfTWwxtGXkarYK/J2oFAiAtkZ3bBNtMLJ5d+v7q6Wn+8MJuc+Xt9YlyKScCgrIYnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28dffe8330259126aeb2f25f6b983d2cc23fbc6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-RhFqWx2J1EI0Unf65m18mbttla2riUj9+6t7xfAflxMbnlYf+5UpL72hmnq7suMLtkb5/rbXfSuXcZPyRe/fhQ==", "signatures": [{"sig": "MEQCIEy63HcUJKyjHSU/wyIZS9uhAlNrny9u08PFnAWvJQ/DAiA/JVcJd7P+2zmiiGqp72LJtRVGzbN4h5mDPKbt9eUrjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2fca10f3856e4162190a1515c2c26afdab9945ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-bipzE8EWzWGLgBKEVjQJT82sq7rl6wtt8UobQUFjAg3jZcHWHp6V8LrdzMTTJRHO9oyisqD1bzdVwtYlT5U4tA==", "signatures": [{"sig": "MEUCIHDorozX/3NetvzekYaBEeT1PQemqYYW3WMTmPFtv6g+AiEAqKUqa16p1bACYJh5QnOSCqqvekq5d3VuwGYDlG7DdUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36995}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.10": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.10", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3bf2515261c372439858dac194810d98fb39329b", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-K5WWZvwdbZAbpXH9WAIXghuO7KnFqI+LFu4cSUBs3uuDJAfC9ShWZrQ1C25YqY8VR/dDb/t4a1n3kcQ8hwUksQ==", "signatures": [{"sig": "MEUCIQChRjb9XlDEHiiIKf8tJRwGAVDPnw/BfOsa/BzckeP3gAIgPeqO02KpNiIEuFqtVj8AnH+xQMGd+Q2EZDLcAFUDMF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.11": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.11", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98e380a11895d99904cb97324f6b4f5a62d5a3e5", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-pYHOpkbKJIAl2IKa9A51xvZqRfLzDGdMmsFYEzDQ60YVHjGqXJAXJL10s2PpRU2LjS4nQVlenuCQXCbmYcZNCw==", "signatures": [{"sig": "MEQCIDsy1zwaD6OngB0GGb0Ypnwj6Hg/3jUPAx2lob7syk2AAiAGIqHmcT92lvBp+9cXZoDSvzyzLr2F5iHPNo+AqX/7JA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.12": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.12", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d86615e156e5dbf1d8a1fd295c45b8adda28717f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-SD0Fs0KEhioOKz77INzkhSmE99yc2jVy7oPa3j2wK0NfYz7UiFyVmtjQNw2G/qSkGWvkw5SpKnSM4izNgbSzbA==", "signatures": [{"sig": "MEUCIQC2FdnaijRMC9a4BfJR2u6QarHndEWri2QKS4/QAIsQxQIgIBL23DQgb5mQV2g0l6t8BWxYVbvAGUaZg08ekJbn+LI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.13": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.13", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "44854b4500ef6d2965c517ea08131449b7a49a4f", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-EiHoz8qNm49YJqcGwvdE0dRtokaQiDbJl7Z5aw3w/dqAtX3VgcC2Iwrt2vcjLbr1PaGqk1ijCqbsdEHH2DuxXA==", "signatures": [{"sig": "MEUCIEyFeEOmztnm+Av6s8m5xHzx5FhJ0NWh/h/nnSkQ846MAiEAoHuiFB9JA6BnrVE47YzZXWPQI4QE+gVvQKTnjvZY80M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.14": {"name": "@radix-ui/react-presence", "version": "1.1.1-rc.14", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7e27d76311f66980fcdf1ae9998b9dd37f85ad13", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-Eb7/k8bHYRAsI7WsfNi+yx0jae7TrFUQKMkyopi/Rgt0zVEIc5jp6wS/jgFJFF+paBgAO0IWbiq3XI1yYW+9XQ==", "signatures": [{"sig": "MEYCIQCmy+e1mm53JnWOzdHCqNJdP/4Mk6ySMDBQKdkOpiBIdAIhAK0xye1ruAVKplaNn1h24rPIMYRZq/haOj4hHbREURTO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36996}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-presence", "version": "1.1.1", "dependencies": {"@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98aba423dba5e0c687a782c0669dcd99de17f9b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-IeFXVi4YS1K0wVZzXNrbaaUvIJ3qdY+/Ih4eHFhWA9SwGR9UDX7Ck8abvL57C4cv3wwMvUE0OG69Qc3NCcTe/A==", "signatures": [{"sig": "MEUCIQDQR6939J/ly98aiUDX5OinGo4zQ2INk7P0XOWiVtcudwIgRxP3Mg5tVrIDv48jofXQfQWfsW4N+rjuH26bTzcraWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36962}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-presence", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "746fb68c168efbfe129eb72ffea2515db72da029", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Nm3ypgMfiKUog2yn82yfgCH7K0dlaftmcnwGbxfOuJozWsDKi2cYtz5reYTDG70sR2jKSd71ZBBA1fD/6V3CTA==", "signatures": [{"sig": "MEYCIQCYTjsLgjPlE122yCsBaX9IfbvYOQI3KU5lcz8JcjvapAIhAPRkHBjyNtZprtwbgNf1jHxqUGq7+HPSvLaVcVuZXcYG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-presence", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "845bf5f943cd1bd811190f622206051528a0fcf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-T3cqLVN6WzJBm6SIhlU704ZejbN2KK0hp/IRTQcCMPbnEmLS9IJ2bi7Q4N9P3cwMP6XA7IbdDmldClrNXsH/UQ==", "signatures": [{"sig": "MEUCIQD9CJVH/8MUkRVLuKOgbaFJ12JZrvHakS6E3q3IlQd2rAIgf9Ypr3uPlT5uhC8aqRzlh88Lfu+VB/sGuMCnQ9boh3w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-presence", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2759482ace0823c7d0fb7d2fe95dad70ae7090f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-FT+EmnRPu7wfZyy8K2izzHBHV/F25ZW1AkBaJOCcT1SDD7iP0m9wJT/c4ZfGP+Vs2iNt2dwZyp51BiyezGUjEA==", "signatures": [{"sig": "MEYCIQDokO/o/9dN/vP576CW0MWXljpkfCynIq/svEr9bwuPtQIhANeiq4ODydqY7//PjK074BciXgkAXOc8mWpV9tDSnM5i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37128}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-presence", "version": "1.1.2", "dependencies": {"@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bb764ed8a9118b7ec4512da5ece306ded8703cdc", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-18TFr80t5EVgL9x1SwF/YGtfG+l0BS0PRAlCWBDoBEiDQjeKgnNZRVJp/oVBl24sr3Gbfwc/Qpj4OcWTQMsAEg==", "signatures": [{"sig": "MEUCIApM8I1CYpyRg5C3UnC3/bww2Z1/RWVzpuAQX99Xf6O5AiEA6kFhf6QxXFiL5gkxQnvwr22Fn6giAFAPIDAHg5n+Wbw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37090}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-presence", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c3e2611fd3ba540fb0991a20354514cc3067df79", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-Eo/6vRoUbEDU8dquHnRY47EUsJtQV4rg0JCnkyntdLnChJX7TUg4mqtUps4U7ZUSh/KQg8McIy3Qb367jMgDFg==", "signatures": [{"sig": "MEUCICmU2lWEkVKIshOhS9crxjj8ABj+tKXUsZaGGMlUUHneAiEAnUuimQSh+iJLl19Cb/czdB6HAf79jjEYwkf2ITCd0Rk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37087}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "14d03b1e50a6da45281783b7ac333754d612edf5", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yw3ZMyjansIO6I1oiImc+DPF7mGZgndVXAoOZStn3cs0pigbz3InGbyHI53Hdfu3hVUyNBN5W/RXmGnpMM9/Rw==", "signatures": [{"sig": "MEUCIQCt+gCbSit20RcRFsL2RAWf5sJARgjCSS9HenQ2fmjXnwIgYlT4xZV371QZMyFE0FKZO31LWB9xW7P1PpvCkLtAzag=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37757}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fa0b0b383a15cdf3568c55ad6d96d7422b104b04", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-/E9eubvcR4+sXoIKCkExTL0/TDfAcb9D1fmV8raSy3dK5566Sau0jkkgYC9KnA4WpDgn/6wFof8DRzVNdfMLbA==", "signatures": [{"sig": "MEYCIQCV0pXeLDJXoOnsU3YFEN0DP8ycGJfmM3FE1Q8VS7gBIQIhAKBX/JLOdn8omu1U0+Ivj1VSEEMVaclqWmHxKANh73V+", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8152b94f21177e5812a0447e12fc2a8e1ddf0d3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-CrxHB14VhPEoEuVszrp3sAxZacavhZkOhMG1DvyHe7UxKMhvV0xkBLkSUMr+J4TvfbfvqSYzo+HmUuAEpF8hTw==", "signatures": [{"sig": "MEUCIQC3SruRt75d0HLNL3cG4Svp8+gjMh1DWRvTd9ex8HqVYgIgMschVbRnW7Q/0Q7o4LT6dAPo3IZCuPLVbbF7Tv8hVSs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "99c356b28c38cdc26d079051e6340e62c7e8e105", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-CBm+xDxlizozHdNKlXGBucP+1Jd9eca3h4accksLgVc01sg9wtIdRp2R+SiiIHZ3Rwv4aN8I+kvzsND1cw4evQ==", "signatures": [{"sig": "MEUCIQCdJsdNgFm86EDAydHp8KKK06e0Dm0pM8MM7mRxspecowIgbAl7XPgbSzJP8z5BYWDK3ktbSH18iE3EFSrSRFZm3/8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.5", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9b5a102eeff66b0cc07a299d12b2964114286360", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-CbgphNSGUU9y2X/J05p6xabZKgDAEbYRvO+V39eldBfVcutMQp74KaVtgyXau69hW+KKBA4ZSXOG+ZnlpmvIjw==", "signatures": [{"sig": "MEYCIQCkRC791pYpSE2/EicJyzznzlhtMQ+4H+rrsibvgnzAQQIhAM/RBnVUS1KeWsQFJtMh4PUhVEHi48pZCxxgFK6sGf3z", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.6", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ec943dce9e006dc8913f06288f7efcfa2469430", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-MbuWmhTfDbhuZmAODr6XRF5N077g1kvgQhKE/Di7oVXR41soU8OQtvOwsuO8jTOCrL9S2yXIKOJqHC3UHkQXLQ==", "signatures": [{"sig": "MEYCIQDDLoAR5xXTI55CsRxWS1/KhbCXyFpUqOJrvmOC2BKXgQIhAIoMjmGxiMp4UdMBEOnXb+UdhLgLXOfHzSUpqLcs4YZh", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.7": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.7", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "751d1a2d2264f43463cfa3a936cd2cb179f4ce89", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-/R1qj8CgzwlN8hCbnEyiRhaf0ftL3Vzfm/a83TVq4wyMg59tMTygXIrR3fMHtr+mas04ncuSOIF4QfjBR2+7yQ==", "signatures": [{"sig": "MEUCIF4WZ5FB6R6HnnKAnd9QUv72DZjRT5Wtn6NKKNGJ6eunAiEA7scBo2Vf2T+5Y1X1BpnwvwoOvg4oj2wzAs60M8gnO5k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.8": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.8", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c79c4618d9efb655ceacb2bf5f52709e52698ab3", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-xIWMavJklyZLTLNCXSxzF38O8KH6FlqcurYPW7AaUKCd0aogYEXXUUsQJ/kHbOGuzm2RAiOcsjX2OyATRnSYUA==", "signatures": [{"sig": "MEUCICeSR25zV5xB07iECT6aUthFz3itZx5dgQ3ydDfiPIkGAiEA6BeobV/zjakzrXXU6Wrw1GmvLmpDV4aQRoddiIc/zEM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 37773}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.9": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.9", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5b07b45a776f269f3c6e1768f861b42e0a54361e", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-TaH108KFgOdrP8ZppUwgC+4IP0kdGRD9jh3IbaQL4HmE58pvykm6HaG40pn37Ht53FLcYDP+f3sWvVzzPtfMfQ==", "signatures": [{"sig": "MEUCIQCi5A8/QJXlh9gGmKxoQbsNKI/3sp/zVkpDGdcKtpeacwIgIMxhIyFc0iz2VypOKy3DIEzmjokiSszUnTpx8JbJw7I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38164}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.10": {"name": "@radix-ui/react-presence", "version": "1.1.3-rc.10", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54d4bd0cefc38b250c3f1684f5ca129da02f6caa", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3-rc.10.tgz", "fileCount": 8, "integrity": "sha512-8Cvg6Wv3N4ITT6pEV1YpAKegPHQXCIAGvPKYTSXvksAJZzLfL4Ov3n+JZa/YZVZuNSkghUQdHMrqyq/BLofDbg==", "signatures": [{"sig": "MEUCIAftAvVxnlAiremmPJJ9Im2IFDBm7y4hG9BmhoDXcRRRAiEAhcCFi0E+hCmYMolR7nHa8nIGf5tAD8xhcXLuRr7GC/w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38165}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-presence", "version": "1.1.3", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ce3400caec9892ceb862f96ddaa2add080c09b90", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-IrVLIhskYhH3nLvtcBLQFZr61tBG7wx7O3kEmdzcYwRGAEBmBicGGL7ATzNgruYJ3xBTbuzEEq9OXJM3PAX3tA==", "signatures": [{"sig": "MEYCIQCSLJPjLc+ISxf7MsZwKxgP0bfWNi7NcJjp37Of77X8WwIhAOKzLlFJJGpQ2G3JfxTxXH+cShF7/mNZ/k1PT+tmMfv7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38121}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1745097595920": {"name": "@radix-ui/react-presence", "version": "1.1.4-rc.1745097595920", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d8c371a5f0aff749f742f537c31030d47134e092", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-5A6m4XGtQ2uGldMI3IsDV+70SlVGnN03M7n02noxFOfto0hvp6stwSxxo7h7NAgbjLkQXuLXruJPVADaSScotg==", "signatures": [{"sig": "MEUCIHcAIBSBA9rYXBCE52yDtjH9oAS5oKqxbtAnMAFtcmNbAiEAuIU9TkDcKDTkJfCPlWMX6aHH+80w6dSRgSVU8Q8JF5U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38612}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1745339201309": {"name": "@radix-ui/react-presence", "version": "1.1.4-rc.1745339201309", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b58a8014dadbb86e9de09fec89438f96d9f30939", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-pkb7WbP4Yi6yiJ+ww7PUcg2ejXv7uTNM3PCr0ODDEio1y9S8C7kssMEhGaEB6P1b+V6AX9P16/8hwRNnm3yJ2A==", "signatures": [{"sig": "MEYCIQCJx8IsbLqX6L34PMNlXognj/iyC1nqHwieMh9aP3CeSwIhAPhF/0Rt29q9+/7sbyaDyv7CVBKNCRaNAj0N4kdqlYQK", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38612}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-presence", "version": "1.1.4", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/typescript-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA==", "shasum": "253ac0ad4946c5b4a9c66878335f5cf07c967ced", "tarball": "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz", "fileCount": 9, "unpackedSize": 38595, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCk6BGGdY6mOC6HkFqbezPYnfP8jNJNrMAjOzIHq2XcvQIhAOif+BPj/ms7EsfRKD98UZJK9iNiJXu3RmeKMv8njowF"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-04-22T16:38:35.176Z", "cachedAt": 1747660589395}