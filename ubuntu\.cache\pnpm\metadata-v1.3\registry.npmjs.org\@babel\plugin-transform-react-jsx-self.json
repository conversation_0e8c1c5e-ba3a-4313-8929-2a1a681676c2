{"name": "@babel/plugin-transform-react-jsx-self", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-alpha.17"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "dist": {"shasum": "3c00cf28f2e843705797ff400cca88dce8efe6bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.4.tgz", "integrity": "sha512-/0jnnp42dqD8lKtBP8yKY+VuAfwXVM951jjR0dFDsDzocwSgAnStKssa5jEbH95OQDsAshZ0I/U/znLPnyFerA==", "signatures": [{"sig": "MEUCIQDD/NxYsXSZSx/OIEC/NrvyRCI/BfAIICT3cUPlQP9c2AIgYr4Vcm2+EQqqIhVm0+61cXDoCt5bGfFWz9UpQtrnwLY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.5", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "dist": {"shasum": "dfc57175695608a709f3b3f5f11d8b5af6a4ec14", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.5.tgz", "integrity": "sha512-XVceH/wJbBOHEyTYeV5rYziPM+qUhj3TQp3rVLK0l7pCjPQ6FtMR73XQKRa3qIm57sLGz2oDvb7Wspuup3Tsfg==", "signatures": [{"sig": "MEQCICX8ZCrvGDMltdWA9R278N4+Kkn3bXj6MfW89WEE4PJFAiALXePkNWZFAGpNBZ1YIZ5Xf4NdoU9vomDS216VefCshQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.31", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "dist": {"shasum": "2ef37afffc3e7f7dd30d432726d5e306713e0d39", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.31.tgz", "integrity": "sha512-82KJ3Irk2ud3V2GmK6oBlckcxgF9X1jihWS79puD5gUNiB1kL3K2ObSbyECnkq6L41SsPzt0NrbnpFRvbanFRQ==", "signatures": [{"sig": "MEYCIQDOsbLjXYuCNdgB6RPFH/WIPzIki79TRM7DPUw2wULsLwIhANtMzfbcsoR6clXYdrDW2eDxqMwUQUv1edHCEvOV/cz/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.32", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "dist": {"shasum": "4826b0fc7763db645fa8535d9cbd927fcbb717e1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.32.tgz", "integrity": "sha512-uuhzydY2dVllihBF2y4+UIXafhyN9+UfXlMJQf2aVKROm6l12cezXUZ5KdqeyzCqye7OBgVBBWOGoQYZOwGFEw==", "signatures": [{"sig": "MEUCIQDTfXWyvwaawfWOHZQLWtQFvnpJeGKNq5JQd5jIHZjkJwIgPAMdXJy+rTeB6M5xJyeJdIPtTtmV9Sn4XLtyuVkhahA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.33", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "dist": {"shasum": "d89db55270ff9cb2473d1e20f52653f864377ada", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.33.tgz", "integrity": "sha512-Vc5+WayQVP7kYLXO3cduGglA+g91hEk3J39O8AF7BUa9z54W9/HxK5jIesNzhfVhM9oOdCKSV/LEcLjj+m0Y4Q==", "signatures": [{"sig": "MEYCIQD8PW6abZU1EPtOl/QKLiZqlWS/AEa4KrXBcPEp/oE/QAIhAMGP/VjX9Fd7ZeJoeWdpnanyf6CwPnamFkskLjMRpm/y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.34", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "dist": {"shasum": "7088b22f6e59b6f9ca4f03bd439eb22b7b612b22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.34.tgz", "integrity": "sha512-pHfuWmIUgn4R78+9CvbtTeG1zx+3l7uVWOz0sqgTqnLnaWFzpDnwbPOeTYW73htP+NkqTHb5aNBQyN3hi3/85g==", "signatures": [{"sig": "MEUCIQCDVwYQi3zvJqvqVR1/GuRSPwe88y64SLQg3MHEjLucCwIgJzFYL3xn7t5CHJh8LzIW60yMzaxRCSGa0Q4slzP0YKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.35", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "dist": {"shasum": "007d1ba47e1716b0777d4edbb10120ea0857a6a0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.35.tgz", "integrity": "sha512-fOdI6u+yaKAmepCJJPkQzhKcPApXzWFYqozp5c00juA2E5oQPER+ZEPaZskb5O3OBSprDj8C/0zQ/U/erM1rKA==", "signatures": [{"sig": "MEYCIQCKJRCkwI+L9FTXGieyXghA57fwTtW4kK6lUFB9XXNgmwIhAN3ysnIPinI5TDJO8+PYVAhlXM3ShHoZiYtOb7irs7/x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.36", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "dist": {"shasum": "e6cded1d86a37bea8331e73239d22ad991a359fa", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.36.tgz", "integrity": "sha512-psZg8WajGOLJBiqiVcv+axFsi1RIFEg/pOG8SWvd+CEgnAw4lt8u4YBrjqYKQ/0igSfuGjdLxuLdiIAIhS6fYQ==", "signatures": [{"sig": "MEYCIQCRRfs+150J9viM3JrINmXl0s2bu0WFEtMnfPIePK5CRwIhAI4tRoX9PBGq6hMJ64bSEZiW3M6rtkqMK9P1r9dJcVM4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.37", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "dist": {"shasum": "2b515680495cc85c685c030252c74934272b3c51", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.37.tgz", "integrity": "sha512-0fq/ik4arhVHRgUSOCJdsVThVRTVAFcpdt0mZ6PsZo0AJ3A2FgI3XTPl3qAdjhhlMzA0b9N8gF+6wfSow9S5HQ==", "signatures": [{"sig": "MEQCIDfeqEB7jfmLorNg4O4dwVsH3XjKHviCC/DIR2GxY/fZAiAAiwQNUx7b3yfvs4HF6WfzOTUabaWn8RjoLfAIpnLopQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.38", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "dist": {"shasum": "1cd41178d16373a64280de0d62355c96e8fbe6af", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.38.tgz", "integrity": "sha512-ULj82jVcpRfXLV75w9mG++mqZA4PYRIEFHl1GXe39+E330FK3NuhdzuJ7Wi2XpIycbH1ICJ5rh1IfdamdrXuxw==", "signatures": [{"sig": "MEYCIQCCysKF+X97dLEFIb99gK6emLThBC16OsHqDFo75suI6QIhANCNYbMN6/F/80PD3BJ9Bf8cdo1+D4FsK2cf04zi1qC5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.39", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "dist": {"shasum": "ad2f4f357b5be61929d989b5be60dac943e8434a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.39.tgz", "integrity": "sha512-dA85ufk2NFpJMy6ms1I6JB4jgtmz4SvIJ1YBAtBX4pEjXvTnY09qo4P46cf1CuXdpc8UXIy7nuUrgWgTDn6kLQ==", "signatures": [{"sig": "MEQCIGf8z+HRnDj+DlG8jj11Nb2KQcZuyDnXiQW8ClxfVOqjAiBtbDScHL2wliCZ6mt61qD5EelhAQ3OqT4Gh8fHPzJaGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.40", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "dist": {"shasum": "cbf0286ec9e52129840e16d1a173adb98e52fb97", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-jTulUz95ll3eNWJ+lulQ+A4CxuHP0Qs77zhAMuetbyiqE4d5fq6XT+BUNaaCvgLOuhmf6hS4v4oxzdad9IPdNg==", "signatures": [{"sig": "MEQCIF256EJGy78wy33PWK1Bong3tyki+SyD5SDOGXkDeXN7AiAPZOq+p86T0H9LV8XhIItlarwgL6ytdn9zP06fPC4T5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1836}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.41", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "dist": {"shasum": "49b75496d78bd97a1f2710074383d4270542c4ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-GrXnoUJKkyJ7EdKY5p7iC4+TzNz89oeR9oWpk2hSii1Snun5uNwBgOKiLABy79T5uCuufKuoSo8zx1yQO8+z0A==", "signatures": [{"sig": "MEUCIBOuUff4eHjqTVTM4OUiJv682qthsQLc1d2Lqf/U5tUUAiEAs0+Bl7baFptpn5AROXwXjx2hbMqffssh/j81QUawiR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2047}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.42", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "dist": {"shasum": "f471407f6d87f5456db716ed7ed24dff6864c3de", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-bpG/Z6D8RtckyYPbqMfU/DaV7H/xHaTNAJLzYLHqQhtQT70pwmLVQ8E6ara18yp7k+6OJytrrmJm9F6ClT4pnA==", "signatures": [{"sig": "MEUCICaB3ODgMZhuWxSeoispZIZZ8uCHC457qqT16c0EiRPmAiEA1YRwTKuw7aOX2r5+JuQQB8BrSIB01S9zTcMl4ZZBsU0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2047}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.43", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "dist": {"shasum": "e8903f873b504df3d99680b68d7b016414f5d6df", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-0hcl3vRHbITsPa4E5iPMdfMaZpNH5rj98s/yoPvktQJmK9ocpnVEPKCodPWsPPZfRT/OxGiECX7/jrGz+iUI3g==", "signatures": [{"sig": "MEQCIA7S3AYGqiwzEJaR02MBjFSTc3ORCstvWQtHJMcvyZaLAiBmHtguyrXm71z8AWmXJGlYjpT3Fj2YJ3FTRrOfU1QduA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.44", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "dist": {"shasum": "5ae463928b5a8d432f8523ef783add643b5c2bc4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-xw/e+bhfTkV6cjYv+Yw2Fdlgb5AjWI9ouSogK77Iq8QSq+vx3aOY84uxgSdUw1gJ8lPgjxtIFAS4WjT+acH/1A==", "signatures": [{"sig": "MEQCIEq9QDAh6oiDBhFvRBvDlzZLMCjVxraXeatt6wvy9sa7AiB2IbIgOFs4BOvLFqxV/ANLMLZv4jwuuUxuTcjrO1iP/w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2301}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.45", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "dist": {"shasum": "bf82dc2cc229f78a276359b7cfff3c2c3a431126", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-qlFM7uoET7wf6EklHNsyhz6djFWmBB8x1D2G+Apcv1y14ICWv938+qyxnmm0flmkAxXus454Hj+/hfJUQ7DPWg==", "signatures": [{"sig": "MEUCICxXOI9SaQDUwjQCFifBbTf8q2xO39wXoPQMCH2GCiWiAiEAqkGx/BioPqLjh7CcFxBWI0ZpqTR2mx4jFQuLSyRFpSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2WCRA9TVsSAnZWagAAkGYP/1SdJkCg1Cs2S8cWYP4n\nQlingExDKuFsJPguTW+3ZAgv3iyX4B/7jdR+MPUV91iyLrIw5jim6vsEYjgA\nJGaTBSOXpLt9YAwziuQ41inEqtRFWwP+i7FR2Erv6bZtPuk63e6uCvbuG0xb\nyz9HdvIP7jE9GaH8GxuDweWYSpLyIMcC5UDBsXQs5QjPNdZBnu2R18p/XBiA\n5tOmdnMiwP4R+fk/KThS0TU97RAI+W5SsumOjugBmu4kmW1OdhqddrKSKngQ\noyn2Eau/pp1Q1wgp1+XiEhoUv9XwtFl7mBimvsBgNHjRBkRmLggW0TOjFiZP\n4xR8bfhjGY5VfZyj5FYB16/vuJLRiRH316lGtlHdj7dfbESVgnuOoeCnb2cS\nLiaVlSGtGWco4IoG2lmX4c8dDIGFO1nMyF9qo3rrNPdEMu06l4T/OBGx9+6b\n4WCpr+d7ymb170MCHYbH1+iEwAb+fLsIT689fawyZqOL4RJ26QKynv28XPCk\n0nhVce/RcQ2ztDZ+zrVFmbJYcB6HwO+BuVMPqvbYLUZqrdYEJO96dxc0CgVz\nKS48RPOnYzTW/uuPt1eGn7uvexitO5a3q061G9gJGC6hHp7XGgaTGk/A3boc\nI/ElO/FCISWcKQ9SJ0/hj9fzsF1BJOXDIsj8Lta55/uwCWEdkzPHxkKK6YD+\nlapd\r\n=eUWG\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.46", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "dist": {"shasum": "0c3d89727f5fadc87294ca58463b392466b5906e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-keXGiKQNzqHXpplpz/Eu+F+OC/k6M3kbEBOqoDbbYeJVmWARVADWwRnCCsW6yevSyON4CTU5mouVadTIY9YSpA==", "signatures": [{"sig": "MEUCIQCjs9/gTi7hGiv/DX2S7SZTxcmQfvtq+oc3b66CzEUamAIgRpOfbRT/XpToMHnKvZ7L7Euypy6eafNys9Y8v094Mck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHGCRA9TVsSAnZWagAAqc0P/iaj38VIoVHvvo1OSdr1\nvGW8c/Gs0Hl5KafHxT5zKAGQS0WV21W6h1alkK/va//zhenZRKavZ0K5ANlL\nQVU4qLXMPDNRiRapaBYc2qsZw7pDWsQcLKH8Nq2s603RvvGdRx5EvnkkUg1u\nL5xL9yXqVf0H/ZwTxmTxIRu4jQOT6W5+2qq8k9r1i9Dto2fXidBBX/VWIYWi\nAwQWqBmNAX0afAz5gSfnPd/HuvRW5nstxoOsSujBkwsHobdQOG2odrvmr2ei\nHHXpntKJ0d6z/NDbkBVLlg11iHkODDQWQAMswMKuhis6Y5lYIFPxDyjA5mKU\nOTjzxqTsXGlroyQ1DuVwho4sd4qyAvR9e87QCQT2vbaGkPbT1ZGZ+PzibcRK\nDUM9vOYrFV1WDHyWHvDC46LrEZxk/DyycKSvFON2lzUy6d40jZdpPcBzeU2z\n0IH3S8LXObHB+mBGlEBLF6tsmvRyI/SsRSFA5qFl1BHmLPsMvt5IpUoB9zr1\nKKVRcDCMkHZA5ULRuaPH0c0hVRPKyteJ69juWJ7YJU7zT6pv++EKOUs6Cb2e\n7G9acFEQ3yNUNsviKoRfRapfV8rwY1V5vgw+oPmQvVVPuypMO6A82Xwq7Utm\nRkShXdF3cWe7UYjlI3MQ2RBtMjtXPl/8PdfKoAjYXNsfaxus+5nVdqUqLtcg\niO0I\r\n=7ym+\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.47", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "dist": {"shasum": "64125e6045f1e50bfa6acedc7986c7cfc981014b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-hlk+iHdlGtUWo1Y//2snSODh4swBaAcnSLC2qHTbWDE5myEO6ZFGV0nVS3eLOictgKFjrzzPZm6P0X/0w5tVBQ==", "signatures": [{"sig": "MEUCIQCp/zrIdB0/ic9UlHi4zJmib8eXXfxY048BzA3NZrQMJgIgdw1eaV1a4kmRBz0PqCMNybpO0wHtthlKDKkb8k6ZcGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2254, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVYCRA9TVsSAnZWagAA+/YP/3to4W4Tji7fygLjQX3G\nBC1J6mJK86jRdaCl5FWht+2LzwjwJWOnlavEaFkJNRkM0jd/3/F2SviB8Cxt\nRSjk/0YsjmfSpt3UzEY07rbHbIFuSNOU3/Dx2FvWfb0Fv5srVBZqivAufz8X\nQu8N8jJ+WRBj3p7E3VnUdDGRI9y5025Djhrf/cvubGnaVQ466y0ggYC9tRTX\nEwlOxuVFA86UJD9tjGTL8zmUX2PX93UHm+Nkt7ezQZ7mvvqGIptTU8O3muRU\nRVvs0nypPTE88QUj8QId3Z2WsQJBM6SOd8ug+NGgGZ5HYxSOZ/6lvWjdHcQR\nF3268MFAvTk823svOfP81nbxSqNun/kbeEXvs4zLzHJMa3nxSQlGIZt5eYuA\nMy4yDvEunOi/tMGRD5tLYaM/yAn+xxyaMjyxVHeVZk7Fice4LDOJRm/bWRPC\nI6yaUR+NhZtlhFCGJ1uNogc7gR1aeQsRXSarP3uEBg4nLKf8IfYvuClLLHkE\npAJ40wfL5n3aKEl4KSH9vIUIQ2f8s5o8BKOyq1QIh4i6Mci0rvq7vp1gYv9O\nI9iUGcpNYGLUIuRkqNYNEIXKYGGiyat2qryYNqgnY8jD7YjkzpZjI+b2bPW6\nEQEMuThH4UuioXlmbrK+tB4puIKsdYpad/tIoJ/ZNxAKZSqU7vUuXfGdEarM\nHzPr\r\n=yi+o\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.48", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "dist": {"shasum": "fb0e87a38af96706ff642c64fa2e68be126aaf4f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-Z28mE15q+yf/havcN9LRjBOLCUw02QllRTQH9T+nbVDVDUSqAgeYSZmmxWzoe0zVt+sdoZ2NUgrSrPTzwdcOYQ==", "signatures": [{"sig": "MEQCIFDkirpk0AfvKmw9fjeU2/ORbwx5tsKkddzNqL/K+/9kAiAPpb/P3oPlmxbQRCkwnpbA8nhbGcRnYBDLqFq7khUg6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2231, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFQCRA9TVsSAnZWagAA/XAP/32hfuoqlF+/cNN1Ed+6\njpo5uIaEt16QOJt8CUbvNwqqbLu4poGW679Rjc7AA3Yz69Pyf47maXcxQpUk\nQ+UMtRkudDGdOAHibIBMNqtQt3tZRi2nvh2Yuy0U7+yjAx/5jGJto7M8MK/m\nb+1C2U2SbntNJaoQxYoMRBYiQe0SDYkvpxDKbMysM8PQeMH1wx9dVhB+1adI\nuE9xKjYZ9MJJIe6YD3L8ycxQhpkYT08sAn3goOamjeeZ7FzkLjwc6sKbM6w7\n6MAYk6SRpeepqdWwELkHadOkBXYnGvFLMhx1DrQ1wm41zgkmfuPAe+ftsZGf\nJc6JY7dtYnp8Tvmm4SHSjHnEWOtAh5/TsPAx1VCieVUGbjdYtOQ5WdL0BJrN\nauAqu7HWKtLjAi1s4zlvzQPt1Q19xUjfMWpHmL8kg8rw+RB00Y+yVGNu/Nei\nBxASt0Bz8YUCEDr+z8cIdzVLLxynFR3UBBIN3XrM6SQnWP8LTpwa1I/zH+P5\n0ELO0zUGYD0iAquUVWHh6a/g2aYeaDtjJX0rbuBwFTCn0WYB3ORxhpIeEdZk\nCxFtbRXLxaefm4GhtNvc2fIMrXdvM8dHcoRyLI0uo8X6Kzo7KRpz07YAGvkZ\nWJolXpandGYMQU+ZviU0Xf/JYUgRLNsluVEjIiV9QJ+n5He8hH3EwOHiBml1\n17Bv\r\n=scWH\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.49", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "dist": {"shasum": "a11828ba38035c1aa93fd44099b9897019fa546c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-GS5EhQfxm5DPGTO0VDNBHjM3gX0MditLfGh6ObvOVHewwXeQiUOdOCLmtGGKORrYeb6owoHEYGt9s1txcaIA5w==", "signatures": [{"sig": "MEYCIQDKHnnLT+ClerCSDgEmJhfbSZKlbA8l5rbdJpSalBqwXwIhAPCEwtFyehwqoD996EGhjDX3gtRy7EPUhshGWYVqBZ3j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2246, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPLCRA9TVsSAnZWagAA4RYQAJARvB95pA0lVi6GVhtR\n+eoL3gyL92Q9b5H7VQHi1p/woCM5zFATLCsSf8aaZMTotVdw40htohIPbAMc\nrloLGplNH3tH+9m33vtXSNJdBw3hK47j17UilvcBa1g4MhqP5yZSzLHPCkdM\n8dGg5fl4qvn2YS5Ji7I7fFSV3kAVkhOh0H6uRDfbeULOPUofAM2LlnvkXILt\n0Ru0XYdo1J257rTTnTPWm4J2+4xHb07LamjjrbvRyE81Tb08Gbj2IhccMbok\nhGcV/uvrE1MH3cmi0cKq5wb8OHSTIguAERKHfU+iw334MXIj++plBC6Z9R3z\n2Df/rV41ngo9vp75KwNJ1xtIiY/angfnnUDuRZEntBochy9SXGuihMRL6KUe\nDghpOW9uVKkADwfxVcr9ApP9y++BT3WxN5j+YYguo/JouzX4NNgLtv2Md0Xf\nMlTFrOV/nsqixB3XnjwcwRtBh1Af42a2Kwg0WP8M+NgxJM6EjidoVBOSJSP7\nsI6U2AFMDxKvgTGjv4cqgEyzceCv6+uQYYSXKO9Tqov42RHk43ai23pyEjHw\nseTwUeStC9vBbxnn44O2Uyq8Erz9569Xu5XJHaSUxQ5Ye6nICBstTI1qWZC7\nVtnubnqWPlbpHHh9CsQqkMudcMmEnHqdrTHzu9wB8zCRSfs37yxC8m6yOCoh\ntk3y\r\n=7MZi\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.50", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "dist": {"shasum": "4f089ef2940e324979aa901f8e9b0bd1fae94a53", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-sL+AmTpS12f2hU0y7iHgTNg0Mxn38hrUUV3qSivK/adhobLB72oHXOBC1WpIw2XkD/4Fa8OQQfygnjzsQS8Epg==", "signatures": [{"sig": "MEUCIC7LyTrKZznQp+29FHh6hGC60wJDAUr7wRgy4y4KpMu5AiEAgOiSTXJZf1xAUmjTIIqiqN4JWujnEfcohz01iCWYcCY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1920}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.51", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "a4f098597fe70985544366f893ac47389864d894", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-iAzjK7/bHypQn4lW8CqGIAVIvGvYLI2PmGJoz6w/N6nIb1NyQ7KAJ3liOcCyrhszioZRm5fqksIe36oCW4kC5g==", "signatures": [{"sig": "MEUCIQCyNDi+Es+8B2yAxfPaiZjC5DvsBNjMoNXIbABCXiSf2AIgVbyoeJJKiqmtWxhZRLzsQ2m+RZ8dLYPfuGzELtKQrts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1934}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.52", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "22fd93f19210911b172d38af3a813ce082d1c6d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-ZCtYGCCk8397DD3KV88gdT2DzD1uvHcR/nX91EEJTqI9Ji3IH2AZm+gJSFjcwaDDO4+YUUV5OsjjvKTe4T2fbA==", "signatures": [{"sig": "MEYCIQD/MvYj6yEa1uuEfT5f1tNrETlsbWMabDwDr4OkecqLEwIhAKC4myU0xydoi+5W27/w9vBIiT30kGZ9qgqzIEW3hB2G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.53", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "6ca9885526b41136ebfc7f07949e108b3edef8b6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-zFYJ3YSsmUdw/+usffBSOFTalTDWAssZWGdyoWVSwcygUIX4o/4JP+rSW0F9jL8WDdeBuXGGZbSweQ/SWsvXAQ==", "signatures": [{"sig": "MEQCIBh+RNKdg7R0gkgLjeLv5SF1zHdL1YfbnEdfGOwOgMCWAiB4M12ulNZJuf66Njt44z0/uvhWuv16JQCaaR2sPEWNZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.54", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "004e6358bcdecd0f9e9042cbbf80b6e9d81a6e75", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-IKyVvyxBUnf09dfXqtz3AXoL1GW14101H+uWrqtr+Q+rwMAElQo1rQjqa1DVJlsEtMBHC00oIP4G3/KDbRhv3Q==", "signatures": [{"sig": "MEQCIAxzVfjwIIVXVh8N7ZrYwVg0pvUyMYKZyvE/uxOmv/kxAiB20Vr8XNiI6lcoAJ/pbeJ29I7lGp777CwIO1svp2zpUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.55", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "83138f82f51c4dcc3d75e6cbe40af69ab302b806", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-TsivMLShKu9dcii70P+aeaTDV3yCQ8zhOwckdggetTnxTHDNLzTjh90Y7ts1l2XO//uwqsNY+qXSZm6ts7EOvQ==", "signatures": [{"sig": "MEQCICxERhT5+dFa0c7Kj/K4YV7Tky9nxanZoeIcv7l8V9JMAiBbAm0E1GEuDVyntCpt+luZB2IyyzIxPTKl3iCR3kET2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-beta.56", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "739a1eec00954e066e382b2a0670abc48957b8c9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-drNQEnMMaHaY1jANaMQVO0wRP2yf0nVstf6QHECzlCuOUaXvT7KtTgePv4fcpv2z/WSZCwQTM7ryHpeYobmNNQ==", "signatures": [{"sig": "MEYCIQCcQ5aiG/p4UamiEFr5TQZqdeU0wRLr7FL2xnQ6BZa7AgIhAISnthCKYnWiZLrvdX6PgUlSqZz+Fvx2NKhigg3/5yjC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxQCRA9TVsSAnZWagAAs9QP/1zf+GXcF4UQXmPCrZU2\njzZ8Z0myoWTxMBc3yj2Pf7YrLOFA3LZrBRkD7yan6U6OR7IgicUmo2/6mLSf\nOg5g/owG2lL7zobKHaANBKXmE3ltn2ybrFgz1moABewJPbO1xJ5GgU0CFjGn\nJQAeJTG1Dkxn9ql+rHqIChAxFS1uvvhGzYXaMlSbaEZhk8PbR2EQ5cuxlmSl\n3a4yxQLnfG5p+v+9/qWLaM2xMrL1NAYiadW3gNzdp2fsijhiyCVVDNPM8/lV\njhUTvPdxl7F5hFp8od+Xuf04/AJauIZpldudIJcSheNYlp4JNfEgkdLm+BGB\n5vDF/lYoJjCP/ZVxGkfs6Jw0wEjWSMQB4N53Y4veEQhU54e6h0xZVthOCkey\nbBZGbuAWma2Lpjzopz/wTnIBOOjbtXPYqn6W5QPtu6HXcYOBaYNvtABSde37\nX+yGGZyp2oNz8aYX2pfZywA2f0uTJnppLHmlxoKn6fOolc2eLmku1KndikSV\nDDb7ky4Vzfd7oGurJlP4uVrMuKQN1rhQZcqPQMke5Ld6XjzZv0g43AKqtao/\nfHfTN8QA4odcVNCUQgb2nKsCobzhM5KDDaKREc5i9uRppm1kFvLbYhNTVlb8\nNl3hN/Fd0tKmbEOX6KILvsZvE66aqqAiZYbfVZJciBNGoFLr7ZbR4j3AhFpb\nDJrs\r\n=McEz\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-rc.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "0f0a87c33e2243b5ae29105bd66ad67e296d4935", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-R7+/PSWwHpfYc42twaeLZOuz4r5YdQJni+WQxVY2aU6Map8zSSYx8uXJeADQWqizV1z88vPyDG/AhlANu3Zt3g==", "signatures": [{"sig": "MEUCIQDi0rzGLvvOANtUgX71oAw72LBkmEP3hiYvxuyEnd7fzwIgdIF7qVdHbELxkmm2G8HzX2rKxPFUzhm02DYbH6AuKg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTVCRA9TVsSAnZWagAAf8EP/2f+0PbbCdJaofDXaJPM\nHx32RCxwBRWU8rRAHoRL3XEb7dG7RTKUazIFkONdqDxVVERzzIdx7dgAGYHo\nxzohgvAyHcKOTV4eMUqBU1aHV77pQ2rSFb3yA95kj507Pl3iyKDXnrvagly6\nohpq4kRBunFEaJTCxzEYnMHdD3ISH+jbTG5T5ghxDv4+whOGtVhd8UUpiX6n\nCgSWW9jPRu97XRDbMlvNOiwq4zkI7+0BN+duxgaUjJ9SPRI1amJhpeJu86ql\nvcrtT52rcsjmrGxdMnYhdLlSL/RJRvU8ZZDa984FLM2GPngWvLxzzn7Ob7mY\nyRujj487jEY/+CiM3cLvoJZpZQD0Q+L7LiXz1583EtrjBsPAuRzsLJqJ/gNQ\nHzLpzWHhofHpDR+xsWTxFfm2av8J5lHcndhG5mNU/TjsUL0LMLtNNNYGmNyu\n2Viq4bb8wgQ/07t9unsHKBZb/bG9mDGeo0Tzk98GVf+JQMySCuRWw3t22mEr\nJLhbsYEKOWbWVU+13KCr7rczc0bwKeW0Inn0EmXmTCSOSD/bq/qC1Hb4TlVY\n61uF91uh2p95usKaRkSbwIFoL+Wyi+Qw94Vgopy9kQen6fIPc9KEVUO9E5/n\nG6dPQtpIbT2+5UQpxiLL18NBZshFSG9ipszuwdTPWYKG1n4XTdWBzron2r2F\nRCuQ\r\n=LJAQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-rc.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "45557ef5662e4f59aedb0910b2bdfbe45769a4a7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-cItXIWcpge64i4FTowxuRdEBIqV0DReJNi3Iu8pEwNH4LLZbZ1OdYBZOL8nVLPP2vrfvsigt1qFJfYsbkPxcbw==", "signatures": [{"sig": "MEUCIF+75cvqvjQ0bp3mwzmeHTuB/hWyb94yoVOsRKqa5XW4AiEAj7Ltniv5BAeFRjzwQPgBgrZGnF0YlcuDlpI1iPPYBsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9fCRA9TVsSAnZWagAAQy8P/0LFK5ZeoZUINylGBoPq\n2N0j5648+pXoneB5JtTG7r+Zo3F1pxNgVzTovQDJSNEarTEe3uylo4BVGUD9\njQ1d1QSI/MFTfp44iPHgPx2Veuv4E+HdHyAy9OI0XmPWLSkc4h2bw6El3gw1\nBM6ZeA6GoJ820YQYRNDZesOlsUrZBpvjrlAXMWAQlvBVbKx2C+orDjn4HVXE\neb99eS3n+a9Yp2XsNVe0Nybo080MQO8lot6mxSnTB2E5hnP4S35wTxBJD121\nMPg17gKIeCCZqCE54HAWkRqKQzSr/TpSbLNkCsqxOxN4JhDXxyrTxuHWvTNS\n9olfpoK3zURswdL3iyRfOa/YOyGNp/q32URKwiv5wZP+ZMRDCP2tdi+RV1aQ\nA3km7Tk+lATW2OvouCZO5rl31qC4LmExL+om80slIw9thlo8xsuwlVa4dTiz\nOwnI+fnp0lhhXZBb1dtdWOCuwXXSZZRkp8LBI3LTVylk7a36jq4uFbrcm+u1\nI1gQS8VBsHAJjUKZAJs/J8nVENsz0qm2RCjNIdF0I2GlUBXj6Gg69DzIY0+G\nnbivrjP9VDkz+QSKUWGyWrLD5xpBzV5y4oyV4AdPW80d5rgF4E3L08/YakrJ\nEeSSL9KelOfU1hMSgdKlilsBDLrtAZSOaqztIClTNB6WUttM+fIiUll8gpjJ\nrJjI\r\n=Zh7i\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-rc.2", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "12ed61957d968a0f9c694064f720f7f4246ce980", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-recQB7RQJObp6UucFyWN2ykguqVFyFRKELCRP75Ogv43k+Tk0vmk5hjmXeRsyFj8k+kcPss4zqNdYndP8Qdu5A==", "signatures": [{"sig": "MEUCIQCkP7vMbrNdSPRtnZiBWieazEH1eGiCawdPvj1J0DGdMAIgII3cFuFD0ap/L0SzJEClghcSC/80b3uH4GvpTr95EBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1899, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcWCRA9TVsSAnZWagAA2bMQAJXlxoCHwZkGsIPtEl46\nwRC/A1oEoydwcf1TlIsfOxJcv6+z7JLyXyb5qfTeSl9SkaRpiqpV81fY+SmD\nAGnUYpoOJreYPYGdhdF2yH50b9VittMsz5vr9mUxThJ230pYS5ExnZ6Ebxjp\n8ayQn2ysp1RbbE13wPYNTnojh4ya/ibo/sS/lDeL9E6Wd5iF+XVOF44uq1Qi\nFts5RRFzt8ROXvMpl1g7gtkM9OVkxsBXzD3KEkqGytuvGQhKvARV3OXL9KRo\nQscjfwOxtCrnA78KowaX4uZUaTrMApiyawvjWjhFVA2Kx8uBgUKv03ystKDK\nYX8VAnmQGL66Le+Wds1awKFsyHPD2eVn5qgLMVnxdGegA0eaJxyNSTF67d7k\n2JW3+6DS1ogtkCyx5xOHMBJqJr3JINx272QtAkcGoCUFPC6EMRYtcdg8tVkX\nxIK59R337Sx4/KuXyVFeCJqwW5nTyEW/KztmNbCcQBzmOxcNHKjBZBl+b/IV\nepJjSkTVEx++iblIo7lIsHSBdYbtNTwxvuxtwp+FWAMB2fHFT/n0aGsz+Oyz\nt5n+FwuwYO0/VyDq7d+CmPBr4UpMSF+RZEyK2nq3CZ9psoes4WOkgoJ2BUdv\nryC9B90Ucq2gbKzL4FVDH6Fjk3he0ylXef+B2z7Du/e7H6UGwcd/hCMb/gs/\nztua\r\n=MJKq\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-rc.3", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "bc0cd329619c2b867a7892770ddf8b0215c6469c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-lxmueF6iCiCu6b2/yOwbbWbFKNzWx1vMpCu0Np+p+ehp41gcD0GicmcFRZk6RCuTabwgJp5R3IVRL3CXC6RjGg==", "signatures": [{"sig": "MEUCIQDQcw3CD1GsBnBlMVlrtE+/kgZ8QiSmWwT9arcnnSjvZAIgVDsl/d2ljrFs5F73t5dJn51LqwlzOsP3CDFjHdQK09A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEm0CRA9TVsSAnZWagAAtDQQAKIcf9PfutuqUZTA2vzK\nyOfAJbee4oid84s7Uu/L7ms9D3pcKMCvYhFLX/C5rCNiqw04aG4wMLKGBjTq\n5wFzxZLBbTLolLSoKi1WY05994YbfeaCPC3i9V87PUxhuYmIB9I7lFGuVaY6\n3e9ye2UxY8ps+YnREhp54f7J+PyQHvM7N0jx0VOIE9B0RoRQkncS860tzwbK\nCyUILUYsr/+ir6DwP5pilQ+/MpaMPrnyj8JuVSsISt5dAOGuJGW2UuYmAoWH\npIjCnaTShqwEE+S7PFte+DvgY9/LY1LiwxB8gPQjW5edH19/wavXOJP5la0Y\nxZ8eq7VRZi5lCJ72zubR/NoPO5qcINCS+mJsB0GSwaDH1f0APJdAs4gBKqAC\ngLnyWc8y8Qv/I2xXIpBV18BIwmMb7Oh6HyCEdeURkCrRbPXXS4QhX8/68w9p\n9f1GNQzgRLRMArZI3OOGmqmCFLyeUXZCKAfPg7UgRt8toMPrb9+TMrqkNsZ/\nygkwvKrqz4k7NN6xvzLFuGuVbo3Z5YU9Qm0X9Aoh/JVmfE4iZO6BippCe7jm\nM7dxJ7OdWBHQOm2utlhdmzf6SXXi0aVVoF2Wq61pgy5UF3DO1sd5rcd9dfDS\n9QsfT7kfCazby2KOZAXU8OkvXdV++A2b8ccSuj6KMQ8X350yKyOdEWqZZreq\ni2Oc\r\n=apqd\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0-rc.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "7aa18e71f48790ed060a4efd4fbbd54eb54e1fac", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-/D/aHbAhyo8LnljmQAs52hHpXabRLwoUlk5jP65nfBUQkJymvjEPIZsHz43vyH0ysSSq4H18CLItfmgFZnsk2w==", "signatures": [{"sig": "MEYCIQDhSb9+yS/xc9M553k2ASsimxw8rqYW296tU9ZG+InXTwIhAIHvKd1fTpgED/0hhzPeAJp55+5J874MrlN33nvk2wKr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCqdCRA9TVsSAnZWagAAiFsP/RSIofQpgmtIjdq3IBi8\nNj81GnJz5zgs+o3Y8QsX+YtZF6e+10a5F7u3cgbYxeYxCsXlrz0PAAVDiN0+\naHMXaxCUkKxH4/REPpssJqEerBLN97k+VvYC5qbs+4w07g6xJ7vw4X0WkvOz\nNlsz1El1cSOWty4CXc01hx+gISmbN7cMeUxkGTRjjK4zmccfPBbN8AFUf7vj\nPh9AJLFKRy94RZE6tuXlG52aYKerrIDNvPt2L0Ur5AY7zc90R5tlND84Eaae\n8CJaI2VPy5XRak/VRYaUbwNL0CpqRjStYshpdUTwQr5z0W7kJU9Qh7sbSyFu\nMel+rlrf5HzMy5kNKGUn8GsP6nIgaHg2hS0kndj6N9YtVZDVIBtDnkIk/cbk\nq3MNYZ5CsM3Tsv05zMVANNxCfrRuidKijVQcb3EYmsUw3eILhduvpG7p2hGP\nOX0jWC4UCgtIdRLqXofvsGytra3OijpuOOcW8JpjGpmShwQAN9WE5L2evcNe\nWeoiHr9JoWCGOhOHoX1950SogdP1+pQrziKjSZif5N2pkuTCWpUZhg7NXPSP\nFS7+QtdwgqcZq6HUqFHjdAE7B3RYVJ4pAgiiAmhu5pA+fVfA0UDDpoHQyjCk\nkzEHLauLUG7CzJkolRfl/7waZrs5YsIetgsL7S9L9INtcKYZkNSZNgDbSoZx\n/2GX\r\n=Hzpa\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.0.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "a84bb70fea302d915ea81d9809e628266bb0bc11", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-pymy+AK12WO4safW1HmBpwagUQRl9cevNX+82AIAtU1pIdugqcH+nuYP03Ja6B+N4gliAaKWAegIBL/ymALPHA==", "signatures": [{"sig": "MEUCIHFQgrlDtBfAcWefXW87hqOZ1QYvPBXxzc4WWXRG9ep2AiEAgN2srm1zCiBiK0LQp0b9BPsOeIynTYqRO39wCooe/rI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCpCRA9TVsSAnZWagAAEV4P/iuOc4mY5VBA8+Hb2YsF\nq6+yeikDIWkfIDlil3VRIyI9CUOsiV42GKYSclY/CDA1buO3/cKwTPyoBhla\nW3UEkC9GVVxfnxfEDrOAH8jKMgdFLi/aEwS5t+i/PggLsDBDBjhwhc29d33i\nzs9U5VYHIDF+4NETI6Y79MEU3U4cCZRyNnFHpt4v8SPbNb8A0skpjZL2RvtI\nnSshbddKQ3oR/CwjgGHzA+exVOrC+7Y8WgrkX++03jb/0GZUty5yyGOo4pjj\nST25oXsmMwAaNRV0l/Ctiy6vK7XswkbRdedkMbaypqzYf1HSEVMXLF8ZoJ1f\naQf8Yix0bZW7mwgRTrxNtoqqnjGl+6w7Dp2RnHeDFzjjilFo0xQIcRsJ2BSX\nnZ4D4E6KETeogn4x3GS+LhSlExmyM5suNLJGuGHo/8IiCq1VcaCoJC/awnVP\np+9FUW5sqp+13FdIAeBj4jyQyEl2YT5VTvTb5Ypy/SaDhNOkdTR8m2FpDGvC\np9ANCy5Foe4y+Qlt2BBfFhiCBiL9f42VvIzlyyyUmYShS181/hyllcE88CVL\nz3Sr/brZzliKjL3X6pWBLT9iJm21TIYZzTfl65PNE8kyOaRjB+6HJ/CNCSTx\nOGyXSuRcefF1YY5WCGMPuyAgNqvfs8t0larTBQqiz3zcPc8LjVB0KguvJdem\nE+Pn\r\n=i/nc\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.2.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "461e21ad9478f1031dd5e276108d027f1b5240ba", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-v6S5L/myicZEy+jr6ielB0OR8h+EH/1QFx/YJ7c7Ua+7lqsjj/vW6fD5FR9hB/6y7mGbfT4vAURn3xqBxsUcdg==", "signatures": [{"sig": "MEUCIQD1xgZopWe6pYPmuG6+NQASAVJQuHdyczBZneGhcyE8twIgIbvadEPllqzjZd4lmjyTieLtfmcRHgo9nPMyvOm/OXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX3VCRA9TVsSAnZWagAASFIP/3OChO78WZLKFe1a+z9e\nXLg78u9TvUuyrA8pah6Q3Y7VsNrDWmTHmp+yU4V+HdouRnYUD6a+jpUVe9xA\n/TEZ7NaxeuSuguoJM7UO5SbF9VNTBrPkBWRespCSsLy69HtjZxxWn8s94S+J\ndk2EtEnjbQQiO3OTU6Y7XndKrvHkGCGtoVuUL+mtBmXWfjV0HVyxRbUfN/xK\nfPrGxRVjV8i/yMffqiy8VouvF6bnF7/1BnUIrot0OQfRX8nv8vbKDxSn3CkL\nmQt4FezXmSA+3G0hflKTe1l1xsL6q10m63QuCsGGAir87x/lh2K9Ro+rOwvR\nIir/ZfxUVGianhXqBdKp9vnyiWxsjeYCQOkL11+/4qXoXNg9/6di3GCpJe6Y\nNepV9rCPmRELlNPg0r6KdkSqC4LvrtiXN6Rci1RH+QxOgsyXp2aPuxD2e05y\nYxrEarUJTa2pIuE+wtroldj2CdAyRlaTNPRFllUdsCtbIbuj55CrBASUJ+o3\nT8gs0/hgwrAo9gQ8tTUiXvxrMSwtaP/t+eTYdy3cUJDR/8Lo+A9/6tHJ+426\nVoSaFLz1gvO+NDRT71aQG/1LuZhTTAIcZh+kihAQBBR9ZI1c7IfF7TnFT7zz\n/wVTmir/5NxRBCVd2tsWwSuZ3ZdTdm1+M+JC4lFi45mZV2P1LOp6gmaQ/OvV\n599f\r\n=4nvA\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.7.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.7.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "81b8fbfd14b2215e8f1c2c3adfba266127b0231c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-PWYjSfqrO273mc1pKCRTIJXyqfc9vWYBax88yIhQb+bpw3XChVC7VWS4VwRVs63wFHKxizvGSd00XEr+YB9Q2A==", "signatures": [{"sig": "MEUCIQCsb1bg5Fjoi4t94oa/0W4Nn2yw1ob4pVhiZESYOjcFOAIgezU0mvRNtJU7adlU0Go9mzN6ipDR3yKGow08ug3Dl5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HAzCRA9TVsSAnZWagAAdPQP/iBd4FNlK47x74oZuZ/p\n0H2TSUGsIjWVx3gtwV2G6nmGpponfSO3JQ1s3w+umGRKIrmcBaWVdRqteUKz\nlbMTSw5L4nqXje3Y4+mGpPxIW73puvA7OmR6nVJ9L2ZrRnYV+4Vn4DEQHRUj\nJVMfdPIKYbwY3cGbSdlkNIoG1OcSuqRtW5QfIPgqElejmd536TP4BKzl4Oz0\nftLJPVyi+95MSmyEYctvnu7SanSUg4lYoYsWKlDUgDb7FbY9zxCLX0wgO9d/\nltL6vPSBIZOWxdE0+LoXWNd3vnTiqRTitd4jbZ1uzRzjs4sAph2r8Cdq0gBj\no0fUtp72CS0lNa2k0Urizhmc+/cfmCQNmatGH1NwTolAp61unzG+NCJp4vf1\nyFPzDjBQyqYut6Ot+tW5wbwg2cBxljnTwcbWMKzK43HLWLdcsGbAFK2CK397\nwrRhjkX+iUOGcFKODCW2y6vkPtRmjoyJfUXMvx7qcCkpfZ7J+qlDmBPQ9RxQ\nN4Js4kRmUhsQZt/A0j9gX1NmxGewENWFsO4ApYai2k4YD6tv4gYQQpoe+TDQ\n7XluljK7mlpCic8IhvlDZdgWxIy3//3qBfDkor4M+9To8gnzyM64GTZ2IsHF\n/3rJ2eExbyyEy7/7szVKAxxlvaCobb66KWIHXxIY7qzgfzKG3QYyKEUuTytQ\nM2YA\r\n=HlK/\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.8.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.8.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "bd067b0ca21cf33eac80b7dfddf8699f1d13a943", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-hJXfJdLDDlJoxW/rAjkuIpGUUTizQ6fN9tIciW1M8KIqFsmpEf9psBPNTXYRCOLYLEsra+/WgVq+sc+1z05nQw==", "signatures": [{"sig": "MEUCIQCFs2XnuMwzxPKCgyeJDcatOo88GQa/hbRpv6s9m/9enwIgULZwyFfJ3buXZF28Nt0gD8uF/bqOzx4JZKBiyolZg6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWCCRA9TVsSAnZWagAAENwP/1MeHtNfsfo52IC+qkeI\n9mPfdLEk9ANsOYpqaU4DSnTWEVSoASvoEo05s2AwoEF9h2unZESV/Uvqi3I7\nF3PExgXq0My4zbePlxqiRlsvJ4sN2DPohDmtoQZVIWxMvwIBLaQQtVYwcnWk\nc3UrHxqX1Y5Kq//ZeIPN/I0q3FxVOwqqVI5o+Fu3PUj2JS9UGb52S+d+m6Gq\nO9N6JiLbNaG7uNjYyiGVHWo3XscQYNm0ZDGAR5BipQ7vs/GbqvzgpcARhna2\n2i84vzZPGO9OdbBcF2TQ9WT76doJj/IrLxnG6hPamVOzrC7a2hQ7JLmU+hTF\nAaRnajs6aA4r1zdTivtbVIwhGfDYXLlZTaGxR6K0dsQpAcZpOenpsiLedQ57\nsqMyRZXfDxNr9l5z7SViSt5ZfQ6tynqe/0IyptdWW0SDA+cYB16bboJc2A2q\ng4t53Kr1yO4zmtC3WWe4VQlfVzrB5l4Z5su1NEWOARXUmHD0MITqpsZls5IM\njTQmwS8Oc/PoUy3BkGtvIRwahyWcGofIcfDw7ON92uoQ8mkbQRfPaLW9C2Xv\nsP8z7AN19V4vtJBVqgnISrigDuwLQtY0RakKGgSbNdnpSw1t4oRezph5H7K5\nYFBPiOcka6NpqsIBOnvPm0uSp8QSQtda+A66bzpNWfvApxDkZCL1sa/LKDPa\nN9ch\r\n=/2q/\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.8.3": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.8.3", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "c4f178b2aa588ecfa8d077ea80d4194ee77ed702", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-01OT7s5oa0XTLf2I8XGsL8+KqV9lx3EZV+jxn/L2LQ97CGKila2YMroTkCEIE0HV/FF7CMSRsIAybopdN9NTdg==", "signatures": [{"sig": "MEYCIQC3atlQXeI5U3/5VL+YvAPInGRhrtxIOEBgNkNsWt8tdQIhAOMFZhvGeXczBR4QhSlpafTcifOBSLmquAB9PaLM1Y0B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQlCRA9TVsSAnZWagAAoZgQAJ7eQoeQpv04LdYlGuXQ\nueF9QPi994B3DPKTbOEoAkjGAsmd/sbjv28XN49nmoo+7jOHWmxYvF3T526q\nroLJmH1h+wMMk/lxjBZDWU9P7BvXoCw2GfTzF91vTH36w+7gw+x3Be++47Nk\njYwvEDeZYNM9rD3G3x0TKTwXOn0zDu5/uDO/C4I8GXuj9QWNoAuqmESJVOVu\no/3a2dxPg7cgPi++R9r1PNiqyVHWAEKjHKHcJ/UG+4lANIslWZWf4TXBBF8Y\nlVLvxNL971oqUPYrpWp2K/77kCxTAMT1fsnYSvbdmszrU6Ywpj6+fvg31eZG\njfbCqWCFrYSwqfjvpStiBIqRdGIRvIhv1iunKYV+gzfPMmWtQpFHkf/9B8qq\n2LT+0N9YAarYhdr9yWr8q+ILmBjDyMJr2Hn0ymcjHvcOIDwZSeBuRD19UpGu\nxWVZ4Yv4l4YaHB3uTU2DR3w/PXT6avPPzEjIGxId6uchdt6Q+U61cf+StJM9\nuPlN0SrjDYHdW1clbGxG80AcmUKt5L8TFttLYpTZbwjxARkkIqCJPOfzPNue\nN3h0lp+W7z+0VimDZr26xb/+8xR+fTVbYeZH1nA8d58uymCoZqnKbmlVLrkY\nzyepQfpQPjPzCs9kuM8DAS8j9gnNXji1lEp6aV9mAT+tlacfxCz4TrKROejK\n1EE3\r\n=gsz4\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.9.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.9.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "f4f26a325820205239bb915bad8e06fcadabb49b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-K2ObbWPKT7KUTAoyjCsFilOkEgMvFG+y0FqOl6Lezd0/13kMkkjHskVsZvblRPj1PHA44PrToaZANrryppzTvQ==", "signatures": [{"sig": "MEQCIGzWmtKOQXawtYxNeYyycESq5EEbP2HcF6J7hHtlA6kyAiAgYhAP/l1qji6ebbPqLV5LearJdhECN/yQlCGabHtW1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3003, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOrCRA9TVsSAnZWagAAihQP/i21ipSep66Vzr9lWRcv\n7qtg6MJJPBgfXBkMwtaw3A3BPNUUCY9a6jiJAy8BUH/pF/YUgPFA2UQcc8/7\nxwfWjVxhBb3iqwAo2xkJLvbZAGfox+JIWu59i+hZi8iCwJ+Svvrrnj0/85i6\nYctENhafDaPDl8UGZ9x+utzV0wgO3ceSl4gr5zzhJpqoGNUiN71CXf19llsS\nPSxOBwFxJaVogqtev/ZT7aq2jmWj/7FejqdqoyleUHf7yLJyZmmVAOMqn7yk\n2HbkpbUipFidaEP3eDoP453VBMAAPLvq5OE3qz+WpR7vF6LWymCjyUFjQJc1\nyEfp3rtl6dAn3L534CHEz44QpTZrIsMMgFr+Py/QW7peJCfN4jtcPv1KaeaA\nFFf9Fz1AW+VNgeqsg9n4fJSRKNgeDcHZ726XTylJBQbHobDVYHqYe24MI3Kf\n8mW7FqfOHrSwYoBOUDz9dDJiH0OP5p7dAPBuGDH0AUnbPPb7ZTN2YpEdww/r\nOMaSdbfTPGp1xN69pkVjSH4k9a/LJCEFA1HGB4i4E8fYODMJRdpn+v5JQ7cz\npz16SC5wQ60TEe+dA6Ij/w6Ltu0TdyYvFq++jEn4iYTlrblxJjYl0QR9B0Fl\nzuVxQHo3iVZs8Plz5LZ/wgAAnOtEuIOUv7eFNwx3n/MRk5bltlLbheK6M9gd\nTp7r\r\n=m+Ul\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.10.1", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "22143e14388d72eb88649606bb9e46f421bc3821", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-4p+RBw9d1qV4S749J42<PERSON>ooeQaBomFPrSxa9JONLHJ1TxCBo3TzJ79vtmG2S2erUT8PDDrPdw4ZbXGr2/1+dILA==", "signatures": [{"sig": "MEYCIQDP2xnioNQl28aSsBzGQ81f9rNLsGWTOOi5dWwmx2761gIhAPnl8niXSm7S84J2VhmmgweGPn9iQ9cmhe7FLcitlaF6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSrCRA9TVsSAnZWagAAsSMQAJHlUPmoOAVCUDCQz5iB\nXzEEcFqXvZcCnB7qRElgKsIwW7Byb470xeOlUff/PBPxWhag1Tcg9u6VpN5m\n9CfZwWaRFTvZDF/Fy7cP5zUbC0wlIkpKp2C6J60ONmRNpJ7zTgfexycwS9fo\nY58ZXUk4Bv6tZdNcHopbY3gMPyEF0Qvw9/UYVVClX53bqlb/Y2CzZxVlTCrY\n/rA2cjRBV9VhDAolLayEJEEAX4Vj7UJ1gEaBmJqr+bgLd9x3YCj1T3KSC06F\nWeQKj2RIfjm6kS3Mou1oKiSFbrXJQD48/9EWiZ1Y53/ercLDd1OYFzyt6TLt\nxa1wKlk5Af9gXiyrpowGwIUAzmUgrandlCnbLRXm5ZZykiGjIcgobMTd8UiV\no+zLEGkAIqzyflTO2iuzpYqg0D21UxHyuyEMhRSIa4E7rFb910r5OmaHAOKY\nFv4wPI2Fj1Ngd5hpW9OrC5GCcGrxwJjCAc2jtSKWNgJIrgF/on6Wy04JghhW\nNM5B4QghneehoYlh9MmuD9T1r7K2UoHvtbE94tWkBgNlZUKvd3jWGxMPlzZT\nPmRnvWsQJkYXlA+6O7DyyVX/SjzL95SmuXwr1KLeXC1fVk//VHjguK/tTxSZ\neISwz5dKURaz6yPnybkyN/yPXXW8pXsQYadnrjtpSdEfqPT5chwXZltFjaYY\nstYo\r\n=ndij\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.10.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "cd301a5fed8988c182ed0b9d55e9bd6db0bd9369", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-yOvxY2pDiVJi0axdTWHSMi5T0DILN+H+SaeJeACHKjQLezEzhLx9nEF9xgpBLPtkZsks9cnb5P9iBEi21En3gg==", "signatures": [{"sig": "MEYCIQDMz2inGEVxQon1d4zYpRwvr0GDoXmwcJv/5osIfJquTAIhANOngq6r9SrE8uvKh1JMK7SuA+ZRE62Bv5BC0nr5gEUq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zo4CRA9TVsSAnZWagAAjy8P/1fGzudUhw2zU5K4bMxH\nfk0ZAv9koILHaWb5rOrjsDuSBbUd/u/fEDwTMr4F7Oo4hfueeWZyWL/aKKUk\nqSuk1XoGVTqpGEQTrKa2EGqp1w5yB6UIqqAHAb7rQzmfpmM0Bl5UvUGU5LKA\nRmr8inVndN/liOsWXraT4zZnqLGSBRXCsN4y0cHY4O7CsUhSwUnaSx1TgjLg\nSevd5aje0bvuq1p105U++2b0+xtwIX3E3lSiMyJsIayHomwb1wIxwGjSLNkG\n7LHE6JeTOPfPTt4/7UlINxS+FNU7QzEQelU55WKKgaGLkzGWasDIK8NGnzv3\nFVqit8s9ljYCwYC0aGq626NzSEg25dm2Jck7ltXvhbPEiYrdoe8+BL7LCQVS\n7P+sF2VchEiyVRWIV/FOhmY1xe+AUbIdTZqWK0mn+yQkMfVKJEplCFvaqbRd\nf8xSLpC+2vqH9qHVFbAE0X6MgWlIiGfrCZLEhhOF5f+n+PvJkafZhGHYRroh\nOMm0h658lIjPjVtj63kUSBSgkEGdh9OJv9niaXKcnw+DGB/WCdEZNu5lHY98\n8NnI0F3FUUiXTol7OG5qlpqNUKX3pMwsFfiUCBW9zYHlwcsyLIdhd4yNqs1u\n93NXhB9zkglQqUy/a68bEsX4MZwsli3pv/Cl73qA7cKykT4oT9H+ta2rlh4b\nMrOX\r\n=R8EI\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.12.1", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "ef43cbca2a14f1bd17807dbe4376ff89d714cf28", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-FbpL0ieNWiiBB5tCldX17EtXgmzeEZjFrix72rQYeq9X6nUK38HCaxexzVQrZWXanxKJPKVVIU37gFjEQYkPkA==", "signatures": [{"sig": "MEUCICaso5sC4hkYX9CfSRmgRdIGCl1UK3/WRZULIKw6Bv5nAiEAnNxDlRe14woYQU9ie10H8eLRCy77Wjtwe9/TVGeDUu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2997, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/YCRA9TVsSAnZWagAAtLgP/2UBdY3h+Teb4KZFSgni\neyP70wJj+lhFjK8xOen6DDR3vLYHMF8Yt5gC5hqquIcM7PVaGm4KRqkhHdWa\nDPI6+KoGrbEFJuYlqp7sT3nfSw8Z5Q1NZxqfzbtL7eYLp24GiRqnIfKiTYs4\nXlKqA3jBjSOivrZASEjYGVFcL5RphSp3qz+b4avmL2lCYaGvIOc5x2L4Hp0P\neh1vIOruWbvrlytfWA1ujq+WJRUCIEEqhE2FaK5J8GPtJvPXjFDXf61mm3NU\n79zPHmVrHrwxiaXTJfz69J2CD00A4fpXozQDWn/ZGvnE0x9l6HzAxuKqYK/O\n7fa5cfBV894ZhU4GydxFocLOg7OPp3sUHJBdvzvQiA0gwFGP/CLntO30zqEM\n0M59m9VDGpT8mcyzlOfIyOkyiDv3yyfZTvBeZyy1nmtCGrfHQNInbagPEK8+\n6Cm358x0X/3Rpi6fnfagW+qnqd9CP2v46bA5whRQe0lRWijiNvIvUpFeZoei\nT1Kp/gh9xRDdDP3aRsxRMouPwLZBV6TqF0gquTZe+lb77sk+j5nuQhpvqsmK\nYW6DlKNO1Bj/NBvBF9QLPcS/0BexR7nCSl9X2ckoe4r/C0boylQAqeY3+xkZ\nYQ7D0zgvKBE335l12+avwv9qocMVlMG4W2NmyMR5PmqMI5rNXedWG3VMpgb6\nG1Nz\r\n=x+RC\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.13": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.12.13", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/plugin-syntax-jsx": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "422d99d122d592acab9c35ea22a6cfd9bf189f60", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-FXYw98TTJ125GVCCkFLZXlZ1qGcsYqNQhVBQcZjyrwf8FEUtVfKIoidnO8S0q+KBQpDYNTmiGo1gn67Vti04lQ==", "signatures": [{"sig": "MEYCIQCKIBgoYGlbUBQFx8cIdD5ONK1N8iPOTNnbL12ASjIiqgIhAMZQbzmkrIhXvmCLZB2R0pYIdoz6dZN3cECw1kJ7cAoK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgoCRA9TVsSAnZWagAA2gcP/3I96ezRHzTRJ+IQDx6w\nXSH/fH6D9Cd7Ki0BQ7xbl1LVIQZd8XyrJYc5vPrJfXEuP6ofHKRw66p04Ptf\nY98ru7Utyah/rnzPUrKD6/y5u1V2lOtK0Xe1Iwz3LsB9lk4beMVmbxzPjCoE\nffdKiQrIy3XHI+cGNix9NBrgxGj/EDrkQR0Gy8krikXxbnx0kC2xHx3NXAhz\nAKuSnqMsA2y4qWg5r9qNDPXfC8RKdL1dR19pbrBaX/pa4yHS+4jmjX7ZnXmT\nvfyH8yKg9cfQaSJ0lpOZoU96+HUCJVFIv165QowU1vFARnR50xbJqGYYVyNk\nAUvrIIwEueSVVklkZZGUe1VJM2EbObuJidj4uOtZm1VtQn1OqL0FYm2fOb9W\nMOaj1ffCq6doyOe3JNEoeUWHrmfWDNWat2f5YocfxiDTtafJtromW965rr2a\nyOTwR7uZahBfyB3G70VXWp/Ay673dFmWVKRPTKyXbPx1SrODdTDLhxdqKijh\n9rN8L1fwi45n/vkmOp67nXnz1Wmpq39Lj17FJNtHhdU1rCrUMJLyIL31vb0E\nalI50ij7pp2abhHVRfvAyUqzo2uH0O5omDOWoFHwq4kNhoj32qf9C8xNxFzi\njsWi8of6CRsuYJ77PEueVAflb5Nz2vM5sDYv+fl3NUvR3L4UeDUxWg2GDfwT\nE0i8\r\n=6JbJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.14.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.14.5", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/plugin-syntax-jsx": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "703b5d1edccd342179c2a99ee8c7065c2b4403cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-M/fmDX6n0cfHK/NLTcPmrfVAORKDhK8tyjDhyxlUjYyPYYO8FRWwuxBA3WBx8kWN/uBUuwGa3s/0+hQ9JIN3Tg==", "signatures": [{"sig": "MEUCICnoIuddyZjeMv8xC2WDvD2QetMStXtYRFJR0U7UyqjpAiEA/WW4YAJp/A/BNQJi1HEC+scynV9nb2ukfK1QAR1GO9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrKCRA9TVsSAnZWagAA4o4QAJm9DGbKIHriNA87nDTK\ns9SGkvKk3RPC+heqoCOsdfgBibDRaq7aFjorTANsHGXHSmRu8a9vJ7GAXow4\nSzmPM/EzlJuKvv08t0dnUS4Bo1CyFG3+q1rM2oY28wjpoQ8jgoB2hZp7MwJy\njGQVypZ2sz48VR8hF5YuIMmvi+YI5jrkK9KkGiUk/+GXElpqYxVlBkffex23\n8PCBNyQ+l7SfnoBa1gFyBxIQ/cWrkiG2Yy3lb/A9IqxeWqofeun88nAJAJuw\ndA753TZChrr8Tbb6UJbhDZFy4UKOTTK4mv7deCVS/0qQ4rYtWEii0l4PGDOd\nS5eJtgYCBtPFvqjeHY4/luZm1L8to3jOgePSRioGZktuLjmzer9oB84BN8Z7\ntSqQfXUtA28pe+taZVR2EHtIoXD+fq9991nw9ikhTxF89FyZrvJ6dzSmkA/X\nce1sdFv6DKli81S2oWzW6qI/6iv9nSolbts36owHR4JY2yOvlIlxzyvrxA2s\ncUieBeundftX/kl8GNxkVsSDcoauTsSwVOlMpSNR6TGGgchy8GoMwhZCy2hT\nlhruHahgrDF/nDIlCvA6qoGFtMSFW1yLV4i7Y8oKcB+hqkDbsY3tWdOzWV5x\nsnPe3hAADGeZH26bVz+jC3lSXdKPNk0T6r625Pkhd6rqJ49Sd++gdnD8O5lm\nSTjB\r\n=o/HQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.14.9": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.14.9", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "devDependencies": {"@babel/core": "7.14.8", "@babel/plugin-syntax-jsx": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "33041e665453391eb6ee54a2ecf3ba1d46bd30f4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.14.9.tgz", "fileCount": 4, "integrity": "sha512-Fqqu0f8zv9W+RyOnx29BX/RlEsBRANbOf5xs5oxb2aHP4FKbLXxIaVPUiCti56LAR1IixMH4EyaixhUsKqoBHw==", "signatures": [{"sig": "MEUCICsMaFe7Df7cj+hauYgC2i+0JGREniXjPd53M6BxSfwgAiEAiJEwafnJg0C7QXWPGusHa3HhDXKj9gWBcsRkygf7Cx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLrCRA9TVsSAnZWagAAQ/gQAIuWBa2iG7YW1XmjQNOO\nEwvlPipihDIKQp8ztQY5uLe0/YYW14iDmge8MXnOZrPRdhrGcbRlzzv4YnV6\n7CIgV3gusYSUAJzAYJQE4DYMpwWCtNQrQYXr21zodisPL70agJo5qrQ4kXOi\nNwjYFrWkz6sI9UjYQ9iJv0ildb/8xtF8qGzuJMMwkfc75VDnts67GkKU8zpb\nCcmrcWfsyhaiDbqm1Cdg3Yo5LwdEK246pd6PZ+yMGioEpyFy3c0KCgCZfzkt\nisZltKdIuMYzGFmJwKHcSxMk//10sYVC2gXB9CHDb+HXjpiDca5Wq3xU3Ziu\nAFI375mVHNDUcYg6EPuaC2T+UVra7xDSOmeUTWqslpaHuAFOP0ex2zrxYjL7\npqZe5AHdorNX8B7dnGBU0cHHrlHyYnypxeHHm6wPAIZWgDauwOgrvMhVRxE6\nZsa6RKEGaQwebEK85kVcBwrxASoulS4ZeDriYh6FqCubsh8ovPmNp1mF5ugQ\ntrGD9f6t1nYxKQhYCDYGx4EOty0r1BXnro1Lnch11MxImalMcUZOFDRPqXZi\ne+o2kDVzMv8dKLznFI9S2Vfz6wkh8puxGX0s3gPpUyExGzAYZlIfhihAa62s\njNox2o1niMZ5Dpo/cG/bAC7MZ6GHU20zQMg5uCwHHAlfI04oq0+je/8/UvpK\nnKHm\r\n=9041\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.16.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.16.0", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-syntax-jsx": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "09202158abbc716a08330f392bfb98d6b9acfa0c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-97yCFY+2GvniqOThOSjPor8xUoDiQ0STVWAQMl3pjhJoFVe5DuXDLZCRSZxu9clx+oRCbTiXGgKEG/Yoyo6Y+w==", "signatures": [{"sig": "MEUCIDMCZ4nvBBe8GBudDnfsZng1uP7ql0O9pKetpVVdZuyoAiEApvY791pfk8x6QwOfuZQMnsSZZ44jht8uUmJfcZKHM9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3955}, "engines": {"node": ">=6.9.0"}}, "7.16.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.16.5", "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/plugin-syntax-jsx": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "e16bf9cd52f2e8ea11f9d7edfb48458586c760bf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-fvwq+jir1Vn4f5oBS0H/J/gD5CneTD53MHs+NMjlHcha4Sq35fwxI5RtmJGEBXO+M93f/eeD9cAhRPhmLyJiVw==", "signatures": [{"sig": "MEQCIAd6uewxbEWXNxwggxHeTFY3KDh6vOZoJZG9t7Xzli4BAiAKAoFvF1p8Fbadvs3Xjn5m/IrDAuGZxT0+krv+MAMcAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kKCRA9TVsSAnZWagAAhGMP/jEJCz3ln6Qwd73lVr0e\nXElti1w/QDfLtttmyRdspyz7xQyybTJCr+x2OeXcA6T4XKqz9EtYvLjJ11zW\nlEC3onpnK82C7wZcCvKkig0tJuKcRP5adVsZrtQaMG+EgRDGknNdbXImVDju\n408mfZNhLAZBhTv/pIjpu7ikTww3WD5E8UaIV3F7DlSzL2gwoe+M4r/bPmAf\nG3+N9Qbvv7DiOvgq4Yd4hH/NpC5+jCB7/tgzmH5bkvZbDiUxMHqAX5QjrC0d\n/VY5Bu8Tp16uS0pvmEHeJ5pXaUXNmGnH2byxUMb7pRiqIUevfYghOLzr6AQY\nm0loPrgDyxGecjBgURt3iFQUnJkiN02UW+2aRQW4CgV70W1Are4Ygkh+7wDV\nfxUUHeCtuf4Q/7f5yvjqJM+aIAjcrm7NCcopiJdSd4xZVHVGYF3y8E6o4LpT\nLWUWMjHLkbJbSeQJkaWl/Q22aEgOgY+9eGuktkZjIQ6oRUOFjOJak4P7QIdt\n/SI4Ue7/SnKrlS01OsyY0rVbvu4PMFX1IPjP89X/P7mRxLgSHZ7W5GR/37i2\n819/VcPCQoiT3BTuDTNCPYww7jz5bSKTVYNeF0t5AErKw+1J1FVBKYK7DjFo\nECYVSiEUf8ILthdzG+vhKf4NxmbUWDvbMsLfIoG1fTp+hK+YniAAyuuGo5Iz\nXlhN\r\n=LNgC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.16.7": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.16.7", "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-syntax-jsx": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "f432ad0cba14c4a1faf44f0076c69e42a4d4479e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-oe5VuWs7J9ilH3BCCApGoYjHoSO48vkjX2CbA5bFVhIuO2HKxA3vyF7rleA4o6/4rTDbk6r8hBW7Ul8E+UZrpA==", "signatures": [{"sig": "MEQCIDFJ/4YVkppa2okCIi0sF01/5bVrCbgx0ZtedOayQ1KqAiAdRCUVN0q/xG412lpoY8j2y25gzRw4cf9HsqneWYrLJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0vCRA9TVsSAnZWagAAyloP/iR9HWVtaupXLRASSsIF\nr39e28sMXmYuLO9ov2eMXBZi8mrmOK6019zAfnsuXk7TdyX6LGw43W4e8zk/\nVPYQCiar2zMuPjHBBynVi/69xHNicFBOm/Rvvm36hkghOYeMV34+oOnHqzpk\n0LWqVNc2TBi23SMScRpgcraezbUyWR5vFYo3u6sgyfC5oYSwS/0WlrDpUuIM\nZyVyuev4rr9Qyp5e3r/xbLsyGjGpHS46/jQPMmtz91dVMuxHqz9RupwqlRPV\nFMhL7XoiA0wEJzn1tYXPaU2DSqabGDqKNZxp5ep1Q3UgYXwFXVo485CZjSAL\newRfSU3sL6o8ChJBZ/NVhjlSK9byzJtVTpjTE+3Pl9mH3kZ7+l6Kb4TKAar5\nRSotdPtorfTIrDBA8WsUdLR/AH25zpneEuMWCb5GItj/PCKMvzf22M+tUXpl\nWfA3jR2YBGXP4yYlFNsIatoQixWwFHz5YRDzawihukANEiYDvjAn3G6MIHnV\nreSPEntBZBXH68xPwdGopltemVs0cMrXxlgTsPB4OABXxOjyoYrz8vk6NxXP\nRBGH40/cXjuV4veC1vMSC92HO8q7bH4/yzYDTcm+cu6FXMCA24qL42rQyQo+\n+X3A3VXWGxtpmLB0/s5rEL6A79aHIcX9vqfjt5yUIeT33wJagIwK5f/QwNPw\nCDCi\r\n=Qr0D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.17.12": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.17.12", "dependencies": {"@babel/helper-plugin-utils": "^7.17.12"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/plugin-syntax-jsx": "^7.17.12", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "7f2e9b8c08d6a4204733138d8c29d4dba4bb66c2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.17.12.tgz", "fileCount": 4, "integrity": "sha512-7S9G2B44EnYOx74mue02t1uD8ckWZ/ee6Uz/qfdzc35uWHX5NgRy9i+iJSb2LFRgMd+QV9zNcStQaazzzZ3n3Q==", "signatures": [{"sig": "MEUCIE1us/7Iy1FCLfB4w2FBHXhDIg5fjAFQIBTIVuEr1EyYAiEArYCmftsOu04MJCuV08fYtYhihl6CRx3zEvtLuxoQqqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3980, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp3g/8CWE++yzaOOFK2tH6bKCU+ALSjHk0EDJotyjtLvbwwDFgnPpt\r\n9HwDkYbeLQBNPj8QX+4jKudpYpif6Qx2gaY8t0ZbKVQbncq4X42ZJuvYg1Hu\r\nROyjEARcnSx+aRmYgQi886gytdpp05eA2BWCIsgaLBg2B+AGumEcFJ+H4bzt\r\nqvATi58Lx/n7mTbhLc+70gob/gT5x43x8osJ/VvnlFamcPVWHg8exWrq469q\r\nKifghqnwzE4eD1OWo3iNNcmsYiz1G/Hmd3sbtMqee95eFg4v1Krp1VYGasMS\r\n+ZvTsMCW09t8WQSoRWfhlhE2ANCDtdnjcWMGGsq7mmtzMquQ1ril/bL4E3BW\r\noqDYL7TDAL1We4xYbtuVVX6ufJpJwUTU0zEvq+1+gh1vHcw8XX0QL/Bmxnud\r\nb6Ood1zCN6bmhSJC19U4x7MGN2Ip1K5zh/DvxsrJv80dVa5Wrid8dWaAPx+6\r\nqWomVn18hyCItbAzEgTW1b0VfXFOhIqWDUmi+3r1zQG+P6gK8livHhLY1FPd\r\nFjCmKuVw9mRcNEBUBcmd8tCfevPGlncX8qi//vc1ILbjlWGmtqMjVqsOOqlt\r\nGHQl4uPZ1YpKc5WOoSAQ1n1nfQj4SRfgRohWD+Z/IEotmZp8jLH7ocuXjNG9\r\n+6jabW537vlS6gSINOqUQIRMaj29WE6V5I4=\r\n=E/Le\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.18.6": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.18.6", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "3849401bab7ae8ffa1e3e5687c94a753fc75bda7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-A0LQGx4+4Jv7u/tWzoJF7alZwnBDQd6cGLh9P+Ttk4dpiL+J5p7NSNv/9tlEFFJDq3kjxOavWmbm6t0Gk+A3Ig==", "signatures": [{"sig": "MEUCIFTuSENDoUoLZQLy7X8dkJGLhWIb1aXspuP7KvlAUvX5AiEAj9U5kbZyvXQGpinvFWedE5AThoBetSWXslJDZ0fWeJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Zg/9FjthewBFwKTa+rj2KPCSXLgfiTc68ZBT2yph7neLKEcETzZh\r\nwCFKRzAd6/mah7w87epJE4EChJUIkNXOTL3WUufmyNzJWbDxM3e0ED3jc38o\r\nDjjrYjjLFhvvNzNuXN5PUZuhe9MErbnNMH31wGXzf49xaK8uQwDNpFZELsna\r\ninKgmr3qM61dX9Gle/OU+MSwrVXfmycIsYURHXNznKDGwnPDkaartAt9sVAy\r\nCwNLIyxTNfrlofVJbQqJ+PjHaIGZKBT7qdluctV+2clorGLhmGfggJ8Kqi2T\r\nC+1891fKDL6GdiW7CTK/oNR8e/GOGZLDDKh/JEzf6AggXc+bThxhyNNMFJ5H\r\nMNgT6mPWIM1HJJZAGh/7PIZc0UVPreIDh8aYRl1jU0h1U6/Kt/7sGlVJn8xU\r\nIzF/xYERkUOZwurvvkF1hCCXGyFcR1nOsLnsMadrD3tvdhhEuFMf8+hLfmSz\r\nYBdI/o3UM320pdANv+jIqu0J+c+smaKkMXkBp8qJ67EAQ1AHomeDeWLonyia\r\nUe+9NYyXS/YQZUXslNGDVkd0F0MJ5VqUtk2HEDIwPTwGYIpJM0lzJlh5VDES\r\n+NkcYvIMHAcwrztWM1nGmsrZR0xUcJoRRMxFQ1cVaMc592rxLWIEWVx0r9x1\r\nWdNocHsksoxCRZM3l8vpP4o2MgGvVgz+06w=\r\n=ylJt\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.0", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "ec98d4a9baafc5a1eb398da4cf94afbb40254a54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.0.tgz", "fileCount": 5, "integrity": "sha512-f/Eq+79JEu+KUANFks9UZCcvydOOGMgF7jBrcwjHa5jTZD8JivnhCJYvmlhR/WTXBWonDExPoW0eO/CR4QJirA==", "signatures": [{"sig": "MEUCIEYAbC65gkuaCVZNE3m5QtuguVEyajrM6oUcrPx2H/f4AiEA17txmrWTtm0UM3PMjAl5qqT/fLcz8anA9qMzA7RGv44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85I0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsphAAgFxDRprIrO5djErGKvxxHtCebPEbeUjO0Q6iPbHDEY1NqjFl\r\n76dbehKxudconySaG4cbUiy20kSxYNe8V9cp17AvhC/31Dfg81aIeNqXLVGK\r\ngdKJErV+mTlAHy72zaTe7yiXiguvwl6OMR19HyE3f2rtL6CBuRs15gFJBucL\r\nUyXH0g9m/hbOJyhFt4y7d9O1WML4JEsQshP6tAj14FZoIGbo5Tl4Ve9JD9FK\r\ndYsEieEi6NSOCTnMVeR6tTOTHsJrx9ivR6pqJ9Qf0F35spOglcFM3+Sankoq\r\nBdJVi0vTH6DMecMf+utNHNPsU0xaTS0pMmgEr3GwWup9hiKwsTBy1gDPcVlI\r\ncRnZMD3DXpIiBcS9u71wAeWRRT+dgxF5cqMq+VjNPB4OMszSxz/PUx9ZRrBw\r\n0e/mNk9w567b3FPoNjx7zSQnxH3zBwIuVRr5tAoC+d4YLpkwdzP+EAplpyvS\r\nr3q6X+oL7OJlqRz29QMDCMND0xqw1bT/Y1jr88rBXMFZdmpIYu6/djADerby\r\n6Ob9tdghh4cx+ip73HZP7VEd4jSEr9ogLK5YpLc9pv/cBotbGQQnd7VeZ0s6\r\n2/6CghDusFQnaCHVmkx69WZsCzOjkKtR0KAc1h28zW8Wm/Uc194A1f1PrEwd\r\n8JTScUCpqwZQd2UCMeIapNYXjHiQ7CpnEjU=\r\n=WpW2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.4-esm", "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/plugin-syntax-jsx": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "dist": {"shasum": "b621ace38a574c3be6a0c466a8ad3f9c2d02c931", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-kp9vNUDaxVko2v3l6laNlYnXukj9mCuss75Kl8eTlv/bTnF1l6H+FgjXYSaO6NS09w/Pbg2Xk/ZbEsjofAZWLg==", "signatures": [{"sig": "MEUCIBANWe2TKc8qE/XduujSa5y54az+tcBD5v25SCg5QHJCAiEA+Rq+RIi/NqbzvFMIT/6BHs4ACZIpa48HAgmAmOz4cqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgJA/9E7ImUdS2lxm1Qofsq4BsbyO/eKy1vJDcnIqIcfRFCiEP+sqD\r\nnMwRka7ZpD17FD0dYqRRw/UzFhaB/DXFbMZZ0SCTd7cz5dJpmrVczSlBWsMn\r\nu7v33iLuInjLNE40qpVrjj8Rdw4W3P+CI+NFKXHfmB8CK3Ky+e/1Qv66UbnK\r\nZg/hdnQQ3Tx4SCkeSZNZ0SqzQdDeBSi/NSvJitMt+0ptUmSyemNxRJ58pb9i\r\noXhmdUGzx5NMeCMTzN0hcEQFj5qWR5QKrZ4NP3igbPSk08smMLAlXTyGGp/5\r\nC2d6e0b8thC8YSnljFBPVWakBXa5GBurbW2hvitM8iWTqj4X5d1u9kEwnMLU\r\np3GkN+bKZ0xTdK9NhbG0IHH7YRHLUmucxTDDq6d5xaitPlbruelOxokNRqg8\r\nBJ2EF2jTbrZ9MVo4oZTXD2CjCaRfXaC4Gbi3oa9LiR71j4KdatSfgo5+QWzf\r\n+JVi9mkdE4MOTiIoshLS+eJMfYW+7dMysUSWVQASu9uBVcka1EDBk6nnQPf0\r\nihWISqk+//LQWus0KBGlYZ3yZpDF3KpHMqs4gmY5qk2OLcJtgkjVypTwy/bJ\r\nY4KZ8Dc+AxjSsUF6I1Odhmn4jvFaC5vIf5f2NUNgdFlQLHg4CX1UpniCqRDP\r\nTmWhZhqzVE7xFob9bYOEZTrB6Ka0i7dKTqQ=\r\n=wxEU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.4-esm.1", "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/plugin-syntax-jsx": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "dist": {"shasum": "ed7daab36e70159579ce00e5b533f6bf836fed68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-MFlQinEL0YcsRsTELSwwsfErOR6shb2ivqwTtqVpinLGrBXjQj8I5c3olSNDXpzyWurCfwWVO9Oylv0mtNc/CA==", "signatures": [{"sig": "MEUCICtiX6mSF1kYwtI5xwPBXjvzphcfl4xzGmHhQAjWzPXqAiEAm5zN08qw90CRYPT9WPlIfOWsz7RulmuYXn8DT0oF0ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGKg//Z6FThN+fTzAAk6FqbIzx1rWwtalLxD6JlpdyBBzDThGw8hHF\r\nCbdbi1F4+c5ctv4aJO3NxINf3jauWjHkcBwrAbivALKRaafEqOXURm3MRfZa\r\nQCX8KRmx1zc53BoULvvK7Mek+Sa5lCo5oBvqOGfDWSXljF2j038ctecooES2\r\nz/Hfnp83zfDzAo0s1jaI4MtysVaYAkvDnWdt8Z3WpIHJUK1GMRZaY0hAHnr2\r\ngRE69UZjK0Do8ZRaFeBy0ElIz+Xg8u1eBJK+HFIaA5o6nDmvCslx78Wu3aCi\r\n71LWo2D36Unj0I5DyHU/4LxVzvZLbEHUZ2VNb86Iu9JjK44h6jlGKFCt30Mf\r\n6AxNzVTfPIA0EeebT1/gFCKf/CEs5DaXop97eqJotXBgk8G4LBGqoNjJCWqz\r\nKiNXwPoMBDEfRphAG3Ok4AsV0c3UN1mfp4l7/tiDEmbelsyFOYZB7dxjfpjt\r\n0FL5/GTzsON85T74tWQZJw0EU+8UwJ77KpWd7zZv7FDQAvVR8rpUlibga+wS\r\nyrQ8oiXQyaI5qUnvWhM02IG60OEvIjGjiLsTUOSSDS0UivpRw+EJS8KsPiFQ\r\n+lsFEdyrttLm7lHP+RHF7hOMdox+QSNgexx6oqR3vV1Hg+mY9DG/6G/auGpd\r\nuBjQqyNvvf+uJYd0/xNWTsv4VKzpB9ydUSQ=\r\n=MdlR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.4-esm.2", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/plugin-syntax-jsx": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "fbdfe426f23d38656bb37e53ff6f1bbf48233e18", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-XCSHSc1+axHiP4qf2ByPLeaGSXpTNOWBAooQ8adeQZWS4rg+HNXp4OYWPqQEKfGlr0OI3c287x86mP18iNHMeQ==", "signatures": [{"sig": "MEUCIQCqtDiCk3dCu5rY4mNRPdIJSjJYpVRlrgTS9V9Pc6iOoAIgQ+Gn7DOHFli6uJ3X0oXLx8daUakTvnoHcJBI5HxgfZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmok6g//TslvMMYGYSpwf9nXaf7/xZJQv64uLa1wwdfKDBXonkMfboQh\r\nkdW3X8yRfO4KF9+IR2Wj4R57I9FcDw8b35Ys3ObmbyJ6/PvDxpAH4hzT1dEd\r\naQu8RtJ9ZMhQn9LvFSnOV8Xqn1Ohqazap4rQ7ykFN9Xp8TGoNdUAXUN9jJwZ\r\no2ieowt4HajueW+BR3uyuO9QTgzphtyLmIEUM8dEbzbdTmZHG8/FnwndST/G\r\n83rXzI+NDYifezkuCMjJTYjLAc+IY2EflqrHnGSxnhb+jAH2i2hORsCBSNVo\r\naVjVeugce1juFkxvhwBi6NZ+W9q1OHVXXZut8AqaUoMLx7iDoQHfGsrQ7v6F\r\nnVDiYgF/tXLyBTS0Zq6awVuLAfIHFam0bfU2Jx0KY0HZLoIQ3oCsWz2xWc9M\r\nsCtbsOnns/OOR0b8yUQGk+mmljWpft5mPF30Y5t76HwToP1s/fEn3QMWxMnr\r\nXdvsOXPWbQov7Kvm7ZwFJgWXTHA5qU9nXKFOtsRNvXOBTCPPWbN1S1Qd1Olr\r\nxrTm2//RBAqF/WxPNL0FVl0/TYqEuF+zw9Yv5fLGpdPCziOyNucMnlKGx/b/\r\nUEY0fnzzfGmk5YEOOBKTFyB94FaeSiUgj8nRiK2o5mrILX2IY//U+blweanP\r\nM7tdgsuRyeJmtepAtVPIXoPb19DP2zBBUFE=\r\n=XNE3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.4-esm.3", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/plugin-syntax-jsx": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "ad5a6799fbf7a1f2b33dd614df1565c263757db3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-dRcBFOUMB+jLmdPDbcshuS0+bL5e8p6M88DAQmcBH/6H6442UScbcVflqZ2TxRPgpesogrrotkBnp1x7ldcNtA==", "signatures": [{"sig": "MEUCIF6eYu13x+K5kl9PnPN4D6712yxGt6AaQArDeGHElBc6AiEAySSputMgwUNscl0xDuGVN6RhNVDKQLEu7Wb6sW0faFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSLw/7BkTNX909gHZp4mc0igMr1K5KvQvNvTLERykFPTOeCjwE/DlG\r\npJhH4x71XO0DKMZJFnzrPFomYP53dSBBYQccUByoI8fklENthwj0tkVZYGUK\r\nw0j/b9grE+k8BsK5kl+7y8+NUNubcYxOKgqHAq9Wgy4X6rK++iNyYZx1F/a0\r\nCXHEaRzMbwfoKfQ1UX49ydWmL8zDXib0ChbM1dTFPJx9H7Z1JMeotqyonnPF\r\nlFur6WAlXO9CT+mNcKp+SBMjhCwRkD83CXvKocWcmdl9Jfj4pV4B77C4Qzw2\r\n88GOcFdCX14n3nmmUV54aB4hlhOkbz0f1d5PTUpDRqGFr+AN/m5uyp6ILzs6\r\nROzTVhkq3m95IqrNsYtDwqr1qA+NR9cyRY/1z8xndp2yf58r3L4D431ZrPaG\r\nyqMjUkhJok6vLyZaKybFSDDW92whvNQaQykK7tp6pzZ0zMVSlSDN5pDrNOqx\r\nC32WSYM1cjzyZxy/drKBlgqlQ5k2+CRkkirA/a29ud103VQfhg6awfA3BqHV\r\nMEto4ir/Z11hPQY12WTn0Nj7wNzP/Oajz6hTjfESi4QJsozgp+H9xblLHIun\r\nccJCz0pNh4eTxmmCHNNTAYqI3yH2Tjzn5z/mQrq4/nZJe9PnJk9yyaFd98Da\r\n8UPTh13IaXqWC5zvRGjxk1dtUmcIEvItnmw=\r\n=GLHb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.21.4-esm.4", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/plugin-syntax-jsx": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "ad4d63824c2203ab9dc2c81ac3f0a53ea70becd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-dns6KqkAVQ/EpXxvgFsllfe1Fp8BtPEoMWU+0ljQl+q/Hq0uMEXpRGjb3q172kEyuvRf2+G+paGhLEqGbujx9g==", "signatures": [{"sig": "MEUCIHwkvU+OT0O1eM7Ahli/9/q/oiFg9ULOns+Hf8Io2lLqAiEA0dsatexOFyxMtfarmGkoEeAIq7y8S48BcJ39M1VqkJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8847, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOxxAAmIbmrB4d7hIl3MY4J3/otq9Di/N5aLJK91p/Mbk6NPYX212n\r\nqu/tqLZLQMUOsFtmdAUdQYdP76VRvYIEaKW9hlSf/O/rDtKNsp6Brm5n8lTu\r\n7Xc4OUtNAUhVr6R8gylztnF1SwqvAEJ2K8lejwXcYJVRfcvGZPfxTSShlnaM\r\n62M9HaJniAxgM6H7OSY2kjIdOiDdvWooVn/nF9lLRDnZV2CImusT1SXOMiDx\r\nR8xyvVPgn0oEVkd2cWvsWYlKda+Q5Dq+2SB2zVXTkDO85W9s1VRKJGj57W6T\r\nPAhc0msG6Jax70VlOpNYnWmEO49ymCmHNKmx+neEH3hsv97lQpwkVZTmsSB5\r\nrShZpMXu3MU8nmAvj2brtQricBD2q61955m/aeRO25xbpQuwHpdfbr9PRIgP\r\nVhNyZV1ZHxegOiO9VjGlS3SP+7Ud91aig2JOIN1oCOxbLHrJZ4aP9nYlBb4L\r\nSSvzV7nZqfPPuyO2qp1HSbDzeYxxk70ye3jFJxE0F6I+k+mJHvThCrVUMdnm\r\ngtP2DqSD2OfCZRvLZmhjM4/J9Lp4GVvBIfPWGrzhT3JDHiLURCwLG1u2HI8d\r\n2XXjn+68W5KDsM6jyGOYGPJDtkTUAWDDTE5Rr5v9eC5GPoMA1aUZA7OWzAJ7\r\nh8Dv+0Hj+xOyImp50wgTDK9FmEUAV2Wt2qc=\r\n=ea7M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.22.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.22.5", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "ca2fdc11bc20d4d46de01137318b13d04e481d8e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-nTh2ogNUtxbiSbxaT4Ds6aXnXEipHweN9YRgOX/oNXdf0cCrGn/+2LozFa3lnPV5D90MkjhgckCPBrsoSc1a7g==", "signatures": [{"sig": "MEUCIALiR5gkEkQWU/TLNwBsEOpcMR2DcyJq+sMnsJBGrp4JAiEA2Hgbt6nvdpLbG/xf6SdpWdFxbYiNzTLaNKNhRjirvQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9107}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.0", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "dist": {"shasum": "4cb7b7c379f7c662d3e311c23c789eb7db1b40a5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-fweLH+vIAT6GmtG5oLY1PavIsaRHdlrEbQ7IJ2EZH4uDIY0JtEWSa94Iuml6dyC+q1UP6YKlXa0HMSCeqEvqKQ==", "signatures": [{"sig": "MEYCIQCzVC4emq0u73xBHcTEHhyZPJm0PX2pEDYwe9QU9UW1EwIhAKs+Yv/6TNYsQMealVtiZneTtSrl76zaYeRW16C4YArm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.1", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "dist": {"shasum": "a7370e6b72675b76fb567521ee856e0873c60e5d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-08UZh+aRH41R1nS1wCLjkcLICYpQuRqFBsPhSyQXLW6Ixjis83AO0deCxSUFuASmOTPBaRLl2Ies9gBVHH/1Rg==", "signatures": [{"sig": "MEYCIQChmQNqjPaTAFEc2yvihz4HgjjTijZYhIbeg7gWzBmLGwIhAIWpcVUAPLz7h4ovftNOrvNOjaQDxENPT3fRpcC02nZl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.2", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "dist": {"shasum": "3ba2178f2ad8b6952cd729f14d30bec16cb96c7f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-yEllElGKuddjMwYMZEPQvCtk8czk6PF7CI0EyFK7t2tSCc9Rb4TKgba+nkQo4SOeS7oTXCUy1PwVlEGSJrCdDQ==", "signatures": [{"sig": "MEUCIGD5PzkK1+fphD0G/ND8Qikdg2DUtFk2Xklh+Q5vbAa8AiEAlbsf+2XmU62o2NusIRBfbWsdy0Cm98xEDIQbTPxKtGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.3", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "dist": {"shasum": "519a47aad2a6c46bf6fb0a555c8b909b9e23ab43", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-6IbZjUlov2VootnRDgsyYhCTtaEG+UPR36ARQUZwj3860QqB3MS1yKnlDKTUBJLlViBPWB72gh701b3g4bdCcw==", "signatures": [{"sig": "MEYCIQDXZ82KrmAfzfNGopXDvDeQNpTmIcbksl044U/r9e4mPQIhAKnEMe070FRb0X0MhdghBMeBB/e3dMjPZArYMeBUNDJX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.4", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "dist": {"shasum": "466d27302bc04a41ad8183662ab11927c80c0567", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-UIAiyobqHxhOAFu0EQf7Z6b+IwI8MyDidaCsWdMZkiAr33jzCUZDAYC7h/ZByVwMcQ5VszS0y66WyzdTSOU3RQ==", "signatures": [{"sig": "MEQCIE/bYiyhhpVMzfpUXfXF15S0GZvjxnAOS1SkI5YCd6R4AiApyEX4oqxhv2qzKP+ziuJcCcBR9J8gxpxp+1rn+KlVrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9191}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.23.3": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.23.3", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "ed3e7dadde046cce761a8e3cf003a13d1a7972d9", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-qXRvbeKDSfwnlJnanVRp0SfuWE5DQhwQr5xtLBzp56Wabyo+4CMosF6Kfp+eOD/4FYpql64XVJ2W0pVLlJZxOQ==", "signatures": [{"sig": "MEQCIFtNiYwd/OFe2BdXjZKLxkpAGaCP+Q3n+aoARNpCFwNDAiAc7JKwjoHCgnRi/073VOBGTUqDhNkBdebyR3qVIHe5EQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9186}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.5", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "dist": {"shasum": "18dc9cc0637e3943926aedf53440e31eac83dcce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-wIBbGqZ9iz/X9FLwUuqYsQKvOmr33sO8vCBDI1BvqoxhOwKJt18OwoYlQoGuIqaWKMeCmR8iDRDzB2ZGm1zdNg==", "signatures": [{"sig": "MEUCIGQrg5yY3G0+AYSGdTfiA+OEPbB8UIomPWA9m9L/6cMHAiEA6rPBw9gkNRKgLN7q4yrvBlbn6Rh4WEG41h0AoBJ6Fhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9304}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.6", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "dist": {"shasum": "c5469e253e8c8c4b7b9d254c7dd21266ea1b71d2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-jLLDdLjWe9fQV/CrHqy6nVR6XjiOmTJtkXVTAvP0TjzRl/9qMieRv6g9sznqyoyeuCueI+a+Rbkt+P8eEXganA==", "signatures": [{"sig": "MEYCIQCoBmvw+3ghjCVhABJtQUQxCTAJDj9mRkMCH28GbEVrAwIhALNt7tIcSmEt8CLm4DuQApajnt7r8dXBgWAV8/dti5+W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9304}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.7", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "dist": {"shasum": "115e6bb57621f6a5da76d233b90a5380debc7f1e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-ycPRoqDrDITdrxo6+rnHb0Jg2Drm95C+Fy80PqDkhbkvHLix+pcLDXjbBF9ezf8bPTj+mgL6x72vuV3pFWeUGA==", "signatures": [{"sig": "MEUCIQDji4RLUweRlpW6eHV5Fb85XU4jC7w3mo4+zgsQsO8j8gIgNr5Hak6oK3dOdxh7YGaTgqhv8aFMI3heGTbfhZ4FYUk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9304}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.24.1", "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/plugin-syntax-jsx": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "a21d866d8167e752c6a7c4555dba8afcdfce6268", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-kDJgnPujTmAZ/9q2CN4m2/lRsUUPDvsG3+tSHWUJIzMGTt5U/b/fwWd3RO3n+5mjLrsBrVa5eKFRVSQbi3dF1w==", "signatures": [{"sig": "MEUCIQCiYYxo48RTt4PwcejEbUOq5ujUM365JnzoAzaUn9VrxwIgQmvco+1atf6vKbjNpIzd0OTV98O79kZP1XuMqKBlWhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9117}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.8", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "dist": {"shasum": "892b977d755e6369816adaf01cd74b6f0fc52f56", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-ZM3+NgrJonlHILUUBmqvYjexKdhiXjmQssF6EuzURlPV/gym26mYmHBHbxMgfVRv+1I6mTh63N0h5JJUdq1G4Q==", "signatures": [{"sig": "MEQCIDVcxkD/dAubHLHq2An0SZn/8sDUlIiIZJz0gsLcgjdTAiA3hr+mniQAkquXW3/dMog2QzCpqzbTO3AbHxhEcinhHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9218}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.5": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.24.5", "dependencies": {"@babel/helper-plugin-utils": "^7.24.5"}, "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-syntax-jsx": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "22cc7572947895c8e4cd034462e65d8ecf857756", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.24.5.tgz", "fileCount": 7, "integrity": "sha512-RtCJoUO2oYrYwFPtR1/jkoBEcFuI1ae9a9IMxeyAVa3a1Ap4AnxmyIKG2b2FaJKqkidw/0cxRbWN+HOs6ZWd1w==", "signatures": [{"sig": "MEUCIQCK692IvxDJzPnQ90HKH/9nrWRnKag8b7N9iA8IYuXViAIgWx3gSqQv36Je8NkMBtD7G5ALhDfmh2fROjcn0HDTPUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74786}, "engines": {"node": ">=6.9.0"}}, "7.24.6": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.24.6", "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/plugin-syntax-jsx": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "4fa4870d594d6840d724d2006d0f98b19be6f502", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-FfZfHXtQ5jYPQsCRyLpOv2GeLIIJhs8aydpNh39vRDjhD411XcfWDni5i7OjP/Rs8GAtTn7sWFFELJSHqkIxYg==", "signatures": [{"sig": "MEYCIQCw/QXcF//8NWzqSUoTdMKwiwq/2Cn7LHw/p4JB3TOjfAIhAMMiuylUepZXa2xf9AVtm5/ehExlQ2FGybJb69diPo07", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74955}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.9", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "dist": {"shasum": "228e12e41c762e093f87d09d791cf6d5872da4cb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-J/kkN86yEMtPGCE6jyZ9g17ZVD57XI2Jg8dGf7dBkCnyDj82dzKIJNVKHbmgefXCslNbK9spSkIPaBX0WCuFHg==", "signatures": [{"sig": "MEYCIQCT2c25aTT+OlIAQDY95HgCN92AvqAyNYM1M/+W4SZC+gIhAMZZXvvIoYBnciymSsM/nn6BSJwnjMHFY2oeJtHLi8vW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75358}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.10", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "dist": {"shasum": "061950d9b468e5d08b3b8fe32956705a7ce0eef8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-97BfvvZB1L9YZaOdu9r2cVNFNQ0e15ceff7c/U1dW8r96+ZPzuKzxy/+Xx0sb6FZ96fXXhU5Bd116jd4oYrzBg==", "signatures": [{"sig": "MEYCIQCa1kbTxNKiNGYRxpoZyy+xs3Vm7L04DvkQj/jhR8YBDAIhAMQ95JhkHCQut3glNXCx0tRj+3iOmU6Oq++qNQSj2Uxh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75366}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.7": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.24.7", "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "66bff0248ea0b549972e733516ffad577477bdab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-fOPQYbGSgH0HUp4UJO4sMBFjY6DuWq+2i8rixyUMb3CdGixs/gccURvYOAhajBdKDoGajFr3mUq5rH3phtkGzw==", "signatures": [{"sig": "MEQCIBIY45tVRwrM0ApI4lXCqhE9LJB40b4OCIIjgbICLGl9AiBf6aValgN6KixFAxALqieLtt0DjzvFF+EKNilF6qs83w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74943}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.11", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "dist": {"shasum": "e4271711fb0b187ca7bb1ad689d8db73283fe3fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-YvJkEEriRQ0TVACIZADDyfJMPgXeY8xBtHSSofrOcONmEjH0vHG9PEwW6VxXqHhJJOjdtvq2JBtjWh/5ZbuZBg==", "signatures": [{"sig": "MEUCIGn29wxPfPrF3ovh6NhuJ+GW4VuxWWNFxuydpZy7DNeCAiEA9SrH8jsl8NXokNjlXIFYAS+1NAkOkcj1Dk2UMU43UhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75255}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.12", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "dist": {"shasum": "7ec03d4242b81d451504f086f68351ab995effce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-xS2wx/EhgYai4gn5uha9mQUtMxIZDwM53l8+sFwi1WupvQN9YQRUNZF8GQomUyGOpKoJiGQBr2bOXvSu7TZFTg==", "signatures": [{"sig": "MEYCIQC7d0IyDPKDOsF32CuFbkeIzfjb1/98cDKeSHS+5Y+iuwIhAL2hGtLuCn8drRGp8U+V/4LTYzW9OM4Wj8cOle+Yn2MK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72051}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.25.7": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.25.7", "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "3d11df143131fd8f5486a1f7d3839890f88f8c85", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-JD9MUnLbPL0WdVK8AWC7F7tTG2OS6u/AKKnsK+NdRhUiVdnzyR1S3kKQCaRLOiaULvUiqK6Z4JQE635VgtCFeg==", "signatures": [{"sig": "MEYCIQCbO598XGajNllnUIow160nCt5OyJQGlp2CF0xrp7VbaAIhAKfBS0j4SiHg01uMd4+UsJliIaHMXW/2yZgr+9xBwN0r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79481}, "engines": {"node": ">=6.9.0"}}, "7.25.9": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.25.9", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "c0b6cae9c1b73967f7f9eb2fca9536ba2fad2858", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==", "signatures": [{"sig": "MEYCIQDA40YKdeSvGmFXi7pdaYQZy80k2ky06F2bPHFbpL5h3QIhAOLxPq3/gFU+flEMHpNi3Ekh+2tAQG3/ks3HQrshGyIO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9022}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.13", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "dist": {"shasum": "74fe714f61a092913b68196074b964a446aa3de7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-wxY8syC8YZfNZaC/+McdaUx/7aCqlWO2/1+ifZyKLHEx8Ceg977fVLefFPdrpW6wT4mz8V9ilPJyKe67nkEHaQ==", "signatures": [{"sig": "MEYCIQD1SRHzvGynJqFdnx6Uh08jFxD3C3DmQQekYud87YuZVgIhAIz4fiBxFPeL0yjNjWuetSOSaW53gLumuaGrNdLJVLue", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9462}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.14", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "dist": {"shasum": "77ec920e14b7058d1b0a5789d84443c5eb085f54", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-k03j5LS3O9SMTJR3G1HKNQi3+Gdm+XWnRv/xtUpwN99zStZaZn2jzk6Fu5zNNC43jzNrd53W2L2/ZGVFxCLEPQ==", "signatures": [{"sig": "MEYCIQCISoFNSL5IZj6oK2sjs9q5cpFZpkjcGbi9sTw/fyNBngIhAN3ELKeIDpUww869h2Smj66LYZDI06l6ZG0JQHUE5gfb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9462}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.15", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "dist": {"shasum": "4c7192c7069e1985b65e4a02b2fbca0d17e27579", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-7A62D0vmAlsQLRtMcZjEhTctbh9lICleRL0lKaslH2J6w4Q3iIJGPU3C76UVX5Bd/9Qwy0Y6dO969KfFecGGNA==", "signatures": [{"sig": "MEUCIQCSt+sa1GIJChvHlJAPbLidaqGnMGBptk/voxCLBuiGgwIge0BTkLHhkDVM1s/SQcC+xOGd0g51iDoDEIr63iUSoTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9462}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.16", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "dist": {"shasum": "d771ae8473be356ecd154a22349e70b3d2afc1b8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-/7T/Wri5DXWjtcW1FDy/E2wYJ8DjNluz1EiM9+9vIalCJOwr8YdPhbo436k8WEiMlYH8a7OjxtjKDi5VPQhrCA==", "signatures": [{"sig": "MEQCIFQS5tOWTEbUOVQZdADHr1E2Huud58YevsA0o4I7Y3i6AiBNJZPErRU9ys4sOyZMywtOrCzXUo2zAJFPVP0Zq/GKIA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9462}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-react-jsx-self", "version": "8.0.0-alpha.17", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "dist": {"shasum": "ef95cff3c6e2c336af2b11801e4ae02a1e9b2dbc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-UMGs41Em0W2kcxyoqDzAICq2RXtIeaR90soQJpJKUSDiJQ7iSOlrpW3cGMGOmEPdXxTZ0uh6zXZ/pDT7WbchWg==", "signatures": [{"sig": "MEYCIQCPbvko6WRKYSlLp/fneORe/E1zI/X9md7Tq1Yul9qkBgIhAOz3HXoZTUZoO41HoUxFylNCR724cq3MIXcfKDDl9j6I", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9462}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.27.1": {"name": "@babel/plugin-transform-react-jsx-self", "version": "7.27.1", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "af678d8506acf52c577cac73ff7fe6615c85fc92", "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "fileCount": 5, "unpackedSize": 9022, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIDSUJ2cjNJ7q/R0d4NQUBbrABjQI+6Dcb+RnyCcW+4S7AiBGSoPmBywpspMv4WVGUvAKVYXg0w4xMk/qoVCwIJQKWQ=="}]}, "engines": {"node": ">=6.9.0"}}}, "modified": "2025-04-30T15:08:55.247Z", "cachedAt": 1747660588844}