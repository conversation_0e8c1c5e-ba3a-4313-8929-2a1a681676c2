{"name": "import-fresh", "dist-tags": {"latest": "3.3.1"}, "versions": {"2.0.0": {"name": "import-fresh", "version": "2.0.0", "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "devDependencies": {"xo": "^0.18.2", "ava": "*", "optional-dev-dependency": "^2.0.1"}, "dist": {"shasum": "d81355c15612d386c61f9ddd3922d4304822a546", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz", "integrity": "sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg==", "signatures": [{"sig": "MEYCIQC+okRnQF+EXiY/FfFv3vJKBXPCfRNwilEJoBGOEGaNRQIhALUg8EGTdNitVuiA+v8Wt07uox6e4dLCM60+ZxdV0ltI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=4"}}, "3.0.0": {"name": "import-fresh", "version": "3.0.0", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "heapdump": "^0.3.12"}, "dist": {"shasum": "a3d897f420cab0e671236897f75bc14b4885c390", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-pOnA9tfM3Uwics+SaBLCNyZZZbK+4PTu0OPZtLlMIrv17EdBoC15S9Kn8ckJ9TZTyKb3ywNE5y1yeDxxGA7nTQ==", "signatures": [{"sig": "MEYCIQCvwQmtVMnyePgjzxOGaZQgR2zYT66n+/c2nX6GqmBycwIhAJkSc85yBt9z4iJcHW0EarBMTKW8X0duTbh9eYMd57Ht", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3658, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcIlmcCRA9TVsSAnZWagAAV5cQAJsYOnPbZc4Zq52zI4Ug\n1HWTZGWJtTFl+Q+oPmUyHgJ/URc2TbmWZOm4FMh22Cp5W9bqD+vNp/5d6lkt\n8vsLm+aHlLgFKBCp8fffH/DZnnYemsj2yWx4UAv/09HYfbmpLwh+AcY48Z0O\n5W8uIGD95vZyXGUQ0HZ6IsJFFotLrD5PKR1I4aMzltK1bSbPZRa6PR6f052T\n4WXhanMMlM1HwNlv53j6qpgErEuFzw7TmVyEoe74TNV6p9ZDOdPn/WoN9ZQk\nzNcUW6g6+EijAppxKGpeqWFKDsb04pDzHu1zSxt7yLDvH1MFnH7vRAU4CBql\nZEG3Y6MyDJZenkyr+Mm7bQwZMYySRVgpjSTIkocMc1p3eUEwOUD7IZ6YhbPL\nTcvvI1adpdoQqvF/dRH1wDgbIgTr+Js4JS5l7ihvkEXSnTZulUMvJirjHPJP\nJq8OhbHO4ebP7TNdFyJlRywtxVVFvMGIbTYxnQnmlLORaJcA7v1eoFFQavCU\n/EH194VooCS27mii5Wt5VvRqOr6sW228sw+SPKsOz/wVR6fOjqmymI1sLvtA\nZWWSmR+fdov3dTWjT4v3HskDQiqvrlnZmlkpMPMS/2FNIHh7DO/XlkBQhFHc\n9myB1pW2Xp+abXVhlZEGG60rmmZ8r0QRWVXx1b2Qy0MjsuJ+U/JTX3Cgooir\nK+BT\r\n=Jbhi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.1.0": {"name": "import-fresh", "version": "3.1.0", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd": "^0.7.3", "heapdump": "^0.3.12"}, "dist": {"shasum": "6d33fa1dcef6df930fae003446f33415af905118", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-PpuksHKGt8rXfWEr9m9EHIpgyyaltBy8+eF6GJM0QCAxMgxCfucMF3mjecK2QsJr0amJW7gTqh5/wht0z2UhEQ==", "signatures": [{"sig": "MEUCIQDvYGT06wG+bIzqoqrnla+/NmnFM5CtVFxj8o7XDbtjMwIgKyBpgk1mRPsC84UZ7XzxeKXdKu4yhbCdImq1d4zHv84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdGIiKCRA9TVsSAnZWagAAYE4P/3qWXKRccVedN2jMkrDd\npV6CdtsSooo11bzcfsTxZpw9zqATd4qYC9VRT2fXjLhpjpOeG5d6zANhnqO/\nYTWGl8sXrEaqR3/AA+O2yYrfYltmp4aLCoMjhC1jejsGaHZG1NMDNG9WUiyX\nPaHpLy7zM81Zt3qG03P/zFGHroZPAqZODCGbgzsDstR4U6x9XHRsY9RF3PC3\nBmzeDdaSfwJ96i9icoSToH6CKlN3jZsEvMGvaWQr+lW8uKqOeG09g3bIBq/I\nd8YGZGAxXcmBJ8/5en0kKHYrN4AN+DKtzSePTwdV6DNQ7aP6Fv4lgK377NSZ\nMtyauKRL7ktgLDOFlGiEINS5MImEyz9k//67gqFZuhHZb5PyeSbxF0eKZIp3\nM+WpKH7ZAG/FOcBpeeofTj4IsQnSmhZYlcL18hMOG3bIBFbj+XBvUr/Gb4/D\nhur7O/Xk/tDmwulfm7jIFuPBmArZYOG6bmqQdw6Cq22ZfjAKQ3C9BqTTguLE\ninK2LAb8o54tqr2+ncFH7EXwwdYexuX611rftb89JAkJ5hV1Nl5d+bUEpf3a\nKEc0N8UUOm6b6MCwUbE9zNGCkksTkSKHhVg0YtG9bxKuMTBC6kpXPL9WrzTB\nlQ3NPg/cckKtlFHn0DHeuHUNXx3P7NGGMPvNNZloz3np66cvZUCDcPTIBY3X\nKnmq\r\n=j0eY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.2.0": {"name": "import-fresh", "version": "3.2.0", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd": "^0.7.3", "heapdump": "^0.3.12"}, "dist": {"shasum": "fed4d772a13949de7d9894b647e77297db14dedf", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.0.tgz", "fileCount": 5, "integrity": "sha512-w1waegcRoBrRQ6kfX2+KWc3lIaVUvHiib7F1ZpggbL5+GyZZrpbOnOVNNuylJonNHO6aCJubjfDUzH6J8HmwSA==", "signatures": [{"sig": "MEQCIEjAnwRItuwq36z4bI3+R99FjGj96GEDQMreTci7v4FKAiAb8UnU+bwq3xQ9WlvCGp6NrlC3fkhRthMeRfO5odCEDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4603, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdy919CRA9TVsSAnZWagAA4LcP/2ZAgcZgfvAWvMzwqyAM\nDnTksMfMpSumEAmSujNsH+tzxbjx68czOrf0w5kOw6UcrCXGskmHShsC40hN\nGX6gCbiygWW6GVIlgY6hhseBfeULVXhhjusuc1hVoQINtlQmXlRr1BlLNCTk\nXma0qHXtV9naTLYsu8+o+i/M9wh9PqSY+lGiTK4tyZeIwFDRm7FvFCIT5mno\nZKkhRt5pxIifDPicaXwx8vQWutzxveJEDihpral50cxEwpi649Qj4KKm2Scz\noC64qOrVeE6hIeZ3+S8sDlIu/QNCpFuhxBDZ7Q6crQBCLWxBeORMp6PdjW3V\nWtjCQ4PkC6Y9/h00arOTg5rSLQ1TSRlAelxUKNvMEbnVJCcpUTL9qel3xNBt\n3X48efro19OB1WvWiahD2m77+7MFbc5JDGJpD8UdALMWFSSIFjEn7lRVsg0I\n/zpob0M9bR0/2Mh3/p5MtQqCRFMuTfv094Q0vVEs3Ih2zgL43US1XOIsZhqV\n77sZENxT+ssdP31p2cXa7SMwbq8F3Qt6UA5yjaxWNg1Luo8U1PJrbecRqpcC\n/J9dtbhQZlBBa4WdqBwwlBrUUtI6/e7qAfYVipW3QxZKGzA8JgKGEi4C+qtJ\nIm2E/Vw1QsAlUtZP/u6PtjxK7DMnXRU+Fi62jHrTmHQugv6b2/BlBA3myVEC\nDrd2\r\n=ad3S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.2.1": {"name": "import-fresh", "version": "3.2.1", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd": "^0.7.3", "heapdump": "^0.3.12"}, "dist": {"shasum": "633ff618506e793af5ac91bf48b72677e15cbe66", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.1.tgz", "fileCount": 5, "integrity": "sha512-6e1q1cnWP2RXD9/keSkxHScg508CdXqXWgWBaETNhyuBFz+kUZlKboh+ISK+bU++DmbHimVBrOz/zzPe0sZ3sQ==", "signatures": [{"sig": "MEQCIGTfg2qBpWyXPTLpi3vAjdbnUi7GsfvjA/Xr7sFlgp7wAiAEKYca4HcOmKiIc0BzxG08eIDwf32RJyfsNjZjutgfjQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4833, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzDiuCRA9TVsSAnZWagAA/9EP/1uUk0BSmmvMlOBfc7LP\npbkVDpiY4NhBP6Cws6LNdyTj/7GLEL4E/idujY/gpXF5BpeYg3vGtT+YGlKC\n2ENuWgRQGXeAf/4J0q4KuQvgEWnLm55j+RpHwXLzJYKi35zE/Gp9h//BwBWr\nfJtZCB9v4gqYZCq4JwyLHmu+TtHojjzVz+a8jmfXRgEFG1/2tKGpNsXH8+2D\ntgUZrMx1dkJXNewyQH3GTQn2otnLinaaAKS0Oc/1YPSS04dXJkE07ZjOLZOE\nBmTspaziqXLwyFBHHf8/extR7Rd4QCGe97swAQ+7UBqEobKZEmpIHilo2AFI\nVbYcEIBU5bIs2qzHFPJE4KTazJzlx0pMfmu0JPNPPuqy+MvFX5yVhBVLCSlf\n9SD1vRKNjBdCE7End8FawZrLzibm5sR1P8+hPIsz6CNijhCDnloBeogJZkLj\nnj4QlbszX8V6/9rAUd3y4izuM2VTkfotafb26octGpt0xp8krRiPafyi5mV3\nsmQrfw0l1k+x/5OupEXdGa/JjATBHbKDIY15PANbGgUeICxUZsmxK8RKvJC4\nyd296jPlfkmG07vgcl3Eh/4PVX3Sz3CSpKfibWYFgRmy7tRR4Vuls1qwHPIT\nIvlyM0wvvovwOxjahGgA+X0hRrO+s7vjp/ehIBPdqy70YLNcGny1VS1y5srF\nsCG8\r\n=Z6Nn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.2.2": {"name": "import-fresh", "version": "3.2.2", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd": "^0.7.3", "heapdump": "^0.3.12"}, "dist": {"shasum": "fc129c160c5d68235507f4331a6baad186bdbc3e", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.2.2.tgz", "fileCount": 5, "integrity": "sha512-cTPNrlvJT6twpYy+YmKUKrTSjWFs3bjYjAhCwm+z4EOCubZxAuO+hHpRN64TqjEaYSHs7tJAE0w1CKMGmsG/lw==", "signatures": [{"sig": "MEQCIFMclYIIEIaoF6UfkbFQ9frHRbxMspdMqvc70B4ggi2KAiABgRZyxHppws7QX6dnB8YKKlM5vM6yXIbN51WZ2N3E6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfnwZnCRA9TVsSAnZWagAAZKoP/jYWbs3h/WCEZRXdxPRi\nPis/DOoRKVBkQwiTpOGtggUIc7OxLTtr8pf69UH3KKJvY8LLQ/t/KcmN35ka\n2+URfRit2VQfVRX0B4UorAMVkCjwKsNsZtrRo2K4jcz+bHw763ZwMhMnr8s2\nWFj2ginkiHh3X7N//PanbZ6o5ubcPVVKFRu58psS9fvWTuagCq3MAy+15yaC\ncVoCZ1izIvkYPvROzY+7RA2HaISk5PCiWgkyPLu4miecKDEC2uGq0/UUhg7O\nylH7z9Mp46ThileHRcfHCExtlEPLtuVLjKBg9IWB5Ylv9bEIZe4y/VG7pFQx\nuEVk+22z/WeI6mGaLDzQP9eli9kY9Rm5SMhzmvveXFNxoTSv+SRYs2tKgjqa\nHalwxHom4GnPU9xuKYAHQPnDG9ILaUdWVmh6OqwZy5qOkPX/6FPv+deOQFpG\n+WwL9mwI4gjxeBaMe2EGV/oHPXE5P12uqPy5kftoLnGj7cpktO57hOLfWIcQ\n1ab5snmETpXUORr41jqZ2Qd+b2DGzk0QkWkRs2pJxcuJmorGQaEWRcRjJVQ+\nzsTCbk00ri3MSrXt91GlpJVwmd8MvE+0c9Jc3WhtjRlMqTbQTB/tEIWUYTvn\nzXNbXPKzK0SwXm5WehFFKpcD4ER+lXGdO2Irf3P0H06mwvkJYw4QPNaCLJyh\n1gjk\r\n=ctZH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}}, "3.3.0": {"name": "import-fresh", "version": "3.3.0", "dependencies": {"resolve-from": "^4.0.0", "parent-module": "^1.0.0"}, "devDependencies": {"xo": "^0.23.0", "ava": "^1.0.1", "tsd": "^0.7.3", "heapdump": "^0.3.12"}, "dist": {"shasum": "37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "fileCount": 5, "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "signatures": [{"sig": "MEUCIQC+WNpCgiJKCYRI700fV/+YhxQJqk9z82PAW0I9BJG3wgIgZhfgu5epfoL8Toda8yFrIy68FX6Yp3AwonvP+PokCQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf31MtCRA9TVsSAnZWagAAdgMP/jqssukwWjy8es55q7qv\npQL/80b5Fs6m4iIIfPcQxAdA+tnI+/C1SPqlkV8+BuWQShykSYC5jfpuV88u\nmSMTx3L12AlxBbn9AQbuaHdlFhACBsMzW/UJPQ1Tt7IW8SBFPnVL8pSsf+ox\nUMk03C1PgpgdZ/rv+BAji32LZsqSVFG7GoBiz7cjr1uAryiaSYh8Rbxc8fhw\nvYuNqIdnRv8OxMlH0EBUrULoPb4zaYkZR2aQSLn7S7i8tlQJYaqDpHqop1e/\n4Lo3MzJvEntke+vEYSketsImNh7+7ztcU8+pdy27akcyD8Ge/dmb7bIE1YBo\nUiuM8QExZWZfDv4mkZ+5EcUdDF/wF2AW9iT6R4oRgYREghIhain2cHVD/L4B\n0tb3aCYMAs0fwoGIsOqjoVZHCIqB8l55U/sXF0IUZUz0qkQTmKyymOqPQMTQ\nivaknpM511wFUfY/HkMi8OFrIMsyvo732Us9EKu/IJKBk3RhqK0HBDXEcAVw\nWQkBDzK9/fWyVM98XxaYmcCF4htTCMXPLC+YYdrazCN5kL21S+DZa2sOzx+c\nmA9ImScJjknALNv+cxQeMLs9M5jj2jBUBWzKRkKXxfKqTK2de0ooSM9ld0/E\nY7KW4blaH0+xhUVoVLza/+j+nR5O9TGjBCexbQW5dS4wzQKTVUqMP7ZiJhv7\nQbyd\r\n=V64p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6"}, "funding": "https://github.com/sponsors/sindresorhus"}, "3.3.1": {"name": "import-fresh", "version": "3.3.1", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "devDependencies": {"ava": "^1.0.1", "heapdump": "^0.3.12", "tsd": "^0.7.3", "xo": "^0.23.0"}, "dist": {"integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==", "shasum": "9cecb56503c0ada1f2741dbbd6546e4b13b57ccf", "tarball": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "fileCount": 5, "unpackedSize": 4693, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQDK1iiHtPpYzaAHuvkWyQ/MZlbJAgu+a5S2T5HA8E45WwIga63wDHMqIut2oXoz64+RzXroFStjdvvBG0uDJc7k5l0="}]}, "engines": {"node": ">=6"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2025-02-02T09:45:41.907Z", "cachedAt": 1747660590032}