{"name": "ignore", "dist-tags": {"latest": "7.0.4"}, "versions": {"0.1.0": {"name": "ignore", "version": "0.1.0", "dependencies": {"glob": "~3.2.6", "ignore-rules": "~0.1.0"}, "devDependencies": {"chai": "~1.7.2"}, "dist": {"shasum": "1d4f6970e026bb91570f7dac2ad1d4cfa5166ee4", "tarball": "https://registry.npmjs.org/ignore/-/ignore-0.1.0.tgz", "integrity": "sha512-cTt2NyCjswWIZAlFpmPKpk/sqb+GuCxiXFzbdfJp47v/AiqY77KY2ZAZD1hVT90B4EX61/kAWDKBBEqfuXs3mg==", "signatures": [{"sig": "MEUCIA+KlmDiCAxtY539ZILfmfgWsVLmEAb+9dP4ltOPnjTrAiEA2pKAEMA1kvrEXElMR1InnxeAMpdSwHIrJfRXqSFKs0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "0.1.2": {"name": "ignore", "version": "0.1.2", "dependencies": {"glob": "~3.2.6", "ignore-rules": "~0.1.0"}, "devDependencies": {"chai": "~1.7.2"}, "dist": {"shasum": "b6cf8b175ef476ce74140e47fe66e179e8d57c2f", "tarball": "https://registry.npmjs.org/ignore/-/ignore-0.1.2.tgz", "integrity": "sha512-tH/+LmIRGLbM6JYJD+05N5wMQjhRIXGg72qF457Dep1MURYscX/NvQ/aGh5Y3jlbiwr98l9PvrKND277kB/pqQ==", "signatures": [{"sig": "MEUCIAu6fYMD6e4GUKjSgO3CxOnn7ly2x0MFheFpjgnNueeaAiEA2sv/tCRbyqmg0aWf+Df+6z5SvZjHSHor+5VYL2K80+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "1.1.3": {"name": "ignore", "version": "1.1.3", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "5108ccf1af942980adb696fad73024f9bd18929d", "tarball": "https://registry.npmjs.org/ignore/-/ignore-1.1.3.tgz", "integrity": "sha512-oSbKlhDKx7W7+vHoOFmld+aUrejHDjIqDzjZTsLAtIyD2qB49Bn+bZmqmtNnnCvc7OCMR4FthwpJ0u0VEw0tCA==", "signatures": [{"sig": "MEUCIQDH6cavQmQmaLyc3Y1gPuNx9YsbiV9LU8FnJJh//A0UIAIgc+TPvgcBat1MlMfgdY/hZjkI1jlmQBuA7jLeeIPEJks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.1": {"name": "ignore", "version": "2.2.1", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "b04d8a6d8258a4f59e8efb53a1c8099fcb1eace0", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.1.tgz", "integrity": "sha512-k9n4EqeqsnvlOqtYfw2ULhn1Km6Nsz5Hr+Gv/BchrIIyQUxQ9AjENt05UqnCeEGyK6IUkSM0auX/TmFNGpzAMA==", "signatures": [{"sig": "MEYCIQDVd88/PGT6HyK3Pr54OK5eS5wI8E8zB7egOjRWY8sGNAIhAIW8lIewfezKEgA+lykL0MpiawQ27bPGkqdNYEvt7J0Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.7": {"name": "ignore", "version": "2.2.7", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "81f4245db439f1757d85a79cd06c675ed65aeeb7", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.7.tgz", "integrity": "sha512-jkWK7gj7Owu2/qAY+KavfoLhGdGhwN/2BQGoY4yvlKeoUjagh0FLFhegG0pV9l4h9JQGIUSCyPoK7cKpPpxlUQ==", "signatures": [{"sig": "MEUCIQCLeOpYf6COpkIfWAIkpli30yChqp9FDrHXHO5XiaIiUQIgdvW+pISYXDMpWqOvl4C7pqpPdS8xuTWjGOQIXG4A5C8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.8": {"name": "ignore", "version": "2.2.8", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "87a5b5832ca9d4b539261d0cc1bcce1835f59ad1", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.8.tgz", "integrity": "sha512-0jtBO/rTyIw/R21Qa/VjUby6xb/2AMTF+8lt8p415vRIo3Y50spxeQr5YOE8g31IGJoVz4kyPTjlwLiWpoi8/w==", "signatures": [{"sig": "MEQCIDSwEM+SJAWcDcpFBe1kcg3o2IDnaCTEeM464Docdi2tAiAKsc2AKO/wu0lVjRwfqy84t4+ZGH24bUk84jbZxV4wMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.9": {"name": "ignore", "version": "2.2.9", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "e6c2d894a947be9b12672c8e769032100bfcdc52", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.9.tgz", "integrity": "sha512-rxdQuCf6Yimf0tvrowTvBTvndWYbIkxwYpIlTbqnF1snz+MTU554zRUUpzM0vfLGvD6vdGmt4DJ1mocA9O9Bdg==", "signatures": [{"sig": "MEYCIQCKE5oA29rjXFTCp6XXprgjIMBIl6JA++kJVqY57xk0fQIhAJG8Bnx8WwZ0lx2P/qnpTvMPnwkiCWPFRGluXnTL49bh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.10": {"name": "ignore", "version": "2.2.10", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "9972e7a3f79dd1c95888f9c83765d85d03ee361a", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.10.tgz", "integrity": "sha512-xYPoDtdIfCQrfXnEkUSs43zbWBbVumFTWE47a8eP2KvoKcL6R5ml4w6F2ZraQ/Za1TKRfEOITn4hcsoTTlvBDA==", "signatures": [{"sig": "MEQCIBuXChJX5+h7fBDFNzE9V/fGS8ZdZwWp/ydPiJhlB8mNAiBDxnled2r44yGAZCfYpGhl0m7X6C8t9IcV11hS5DwrYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.11": {"name": "ignore", "version": "2.2.11", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "f7e8b9fa7905a68d5492c3102e31c3bd674b232f", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.11.tgz", "integrity": "sha512-kk6P52m1hXS/FphC7KtxMo1YZrD6Jg0IhrRf+OsEYVRD8Aozc3EVXuBKN69hbCMZPEbFreHhI4dFIg2qgbgIPA==", "signatures": [{"sig": "MEUCIQCHS0CXLcpCdKm8Xt0UWIF0VNpqI/CVaeJblin/uh+7YAIgFQa/XpjbhOr3MZo0NcKjiyekgmQ+gOJJ5xqI0vc4IyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.12": {"name": "ignore", "version": "2.2.12", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "4a5353392cd47b258615f5d2cb9cca9997ad55cd", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.12.tgz", "integrity": "sha512-naUJc5nqqUUmV9QK65PogyAaX2qS6sGVrtT5YvZ0pjDCWHEZbHHlhTli6dsLvmPDBwkaxyJxakMF2/eHRfXqdg==", "signatures": [{"sig": "MEUCIQDoo6CRekZ4XSHKfS3f6lXriUp45/13CTHpSCwM2xzV7gIgVvGw4h68QeItbbVt6ywnByeK/AVJp5xUmcedl9BAakI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.13": {"name": "ignore", "version": "2.2.13", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "13b34543d62b017cf9eb184dd4218b79ac9b2185", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.13.tgz", "integrity": "sha512-F/sDvorVzoZ3JW5dW1pdy1eVMKhGFtZxIoPPTTiSL56gLaEIpwPpmIzMfslU3OYcbAAhQEcejQG9MbnfDe9W2A==", "signatures": [{"sig": "MEYCIQDGs1xb4cHphmsh2FMol2ezgu6xE1vmNkevfYxPOOdlggIhAJFUBH1yNia6J4zG2dOD18vGTUow4JpFfTJI2WFCN4F0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.14": {"name": "ignore", "version": "2.2.14", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "1c9034ef6a2a71f9216abeefe6256acb8547a01e", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.14.tgz", "integrity": "sha512-3Pfmq5shzQHMQb/vLeyDK3NQPgsxQ9veb5WuH8F6g3X6F5gXsmk7uo8iKho9kG+3QPEX/hsUISaPRpd480BF4Q==", "signatures": [{"sig": "MEYCIQDYcSFLnGf8kNW9niLoMOeuqOmXY9b1c54Qstn4Y/mSWAIhAN93eNc8GOInK9vFjLX3+H4VukaVJChzhSdmMu/75W7t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.15": {"name": "ignore", "version": "2.2.15", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "6bd552185e0d1cd393b416603ee686879ec3bc3b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.15.tgz", "integrity": "sha512-BKmNJZ4GRibszZqYaUBhsGTYSHh1x9B4Z5yak6UKd2RdebehxcR6Hq8pk5V6YRZuRIyDh/RL5CH6HFJqgW5d8g==", "signatures": [{"sig": "MEUCIQC3K2XdRystX7GyGTaTU+tzaHVd91OmOoGLDlnaVqnafAIgcPzKqXeq0n2l1MlDgidEDVAo6YMVQj0x+db+lpA5yds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.16": {"name": "ignore", "version": "2.2.16", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "7da08c5db2200e26955af017eec23bc8c915cfe5", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.16.tgz", "integrity": "sha512-7yC4e+7cJPSmIFBTaQrPxcU1lX7Du2z+k5LsYvOVxzPD8hBn+RNmIkLWqAfZ+zyQnlVNKMmyAWBRapaiEHoWGw==", "signatures": [{"sig": "MEUCIQDXMgzBayCTUzux8aScKkaPHmDSviIZNjefPbYtke235AIgaZ4zr7TbESBWoAbaEuDE0qRrYn7O7L4uvPOjhAMmhT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.17": {"name": "ignore", "version": "2.2.17", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "e597d8f221cff1c377df3f7c4bc1d0042af3dd72", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.17.tgz", "integrity": "sha512-QPzSd17UGQxsEidUm/p3mqI9v/2u2ZMylpoYEPsYmuNeAsw3nnTsJGaE56r8acDH3e8VWHa2rpL/EVfac7IAkw==", "signatures": [{"sig": "MEQCIGwCYoolwmrzx6ldnLMtea4bOr8fxQycYRqXGwmGs9MFAiBLTaehmt+1u5mlF25C635jAahNDdfbKOv/47U1oUBwLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.18": {"name": "ignore", "version": "2.2.18", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "fb1d11a785613a05cfdc56c670dde24fb569ea2f", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.18.tgz", "integrity": "sha512-ZQjgEPaqKT9knDaYRdgv9p92Np+61PZkbwmlZ3o+AcD8BgTcejBuEQ6ic5mYRbuKhOU81fjiHGEelH89uHCdiQ==", "signatures": [{"sig": "MEUCIEGeB5kR0oAd/yGbBBXeXW9EkQuoL5F9OTc3ZoiaFR4OAiEA0V4x8bzzooKYY93KfttGIjNEb0zJW/6Je33rSb5d1+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "2.2.19": {"name": "ignore", "version": "2.2.19", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "4c845a61f7e50b4a410f6156aaa38b6ad95e0c8f", "tarball": "https://registry.npmjs.org/ignore/-/ignore-2.2.19.tgz", "integrity": "sha512-Mq1FKJDxSky/AR+Goatl1RmCzWbAHE4fNvgkmJu9Ln8T8A8JbKAd/BHHZQaqknHSFCBe0zQJg7akDpI57pKTlQ==", "signatures": [{"sig": "MEUCIQDr9pU4ThmCy/inEwV/S9wvwKjt42eKKxKVzLlbCmVHRAIgY/a5BSX5U6ohfZ5T4LRYAFQ7MZYpJOIeJh7grQbWwuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.0": {"name": "ignore", "version": "3.0.0", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "e4c906213706b1c8648a05d56c0f2916e85ed492", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.0.tgz", "integrity": "sha512-qxOPGwo4FVXzEkwvE7GoeWqXDDIeyyrr2alAbdFBuGP40DZ5GzFB9panjseZmENLzLZn0qH0IxIV4yqeXPt+wA==", "signatures": [{"sig": "MEUCIQCtpv7Iu+Tto49T1InZYVr/UcgEpeH1HMZPzMFatgB35QIgeMS8EpfzNyGKNwZu8vk4uWoSZGvDrGIzMfgo464ML0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.1": {"name": "ignore", "version": "3.0.1", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "6253f0321ee2b4e665129bd503caafc16da6e840", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.1.tgz", "integrity": "sha512-SJiHdna9NsVq9HpTrpBZaQvjlRvhz2o0JCYPSwiCtf+1tnm6MZYIohLffmMISC0O8KLuBQ5wspB4kGnyYOQIUg==", "signatures": [{"sig": "MEUCIQDlyrCbUlB2o+UKmwx5gPN0HpzqzFiVNXmAqLGGPWDowwIgb0GktVPO43SyFvH6xCwC8JIAvo2SyE38mDsVNAeW5mE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.2": {"name": "ignore", "version": "3.0.2", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "b3fa8a9e057d2e9ad79f0411494a3c2c6fab4b8b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.2.tgz", "integrity": "sha512-Icp936PhDoVMAMchOr3vM15R+LiAC0/7tj5kFwxkEVczrUVNpVxexjbGi0TbxWN/oNjjiv1e8Kz4vabGLgJVsw==", "signatures": [{"sig": "MEYCIQDBW9dNfy3wINvVIysoBEykR+lbAJfRiQ3HGCYj6ldwaQIhAPdxPqML0uLmEhDJmbt+pr2I58rNnauOfSXUBva95Wke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.3": {"name": "ignore", "version": "3.0.3", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "8caf5840f838b994e0ebb5bcf774f0f3fd0c713a", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.3.tgz", "integrity": "sha512-8CVD72gfqrhH4ZqzHa0OG7A6BAB3VmuhRivZ4bSRVvFxRG+jApHl+usiKAZgiZCqGlOFbGOfr/RhTy37SpYtJA==", "signatures": [{"sig": "MEQCIGhxt9HGW8bwhTFDPCKHC3ENvpbbbwlt7f9wXtp0V6ajAiBP9YouDKj8/L2CqUfj/r4kHlzUV/JBnSIrEMlh+sPePQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.4": {"name": "ignore", "version": "3.0.4", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "426b30ed0a589682390e9ac174b20099c589151a", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.4.tgz", "integrity": "sha512-qhorCnPDr7fYt10BVHpY7njBl5wWvD23MPqIoFE8IibDsS4RgW7MjzmNrx9n9YJwosYGNII+y/VdeQV0wOnM4w==", "signatures": [{"sig": "MEUCIHQH1//MRf8USwKqgyhx6QF+P8Fis+QG/nrz3+b12Xu8AiEA42d7SsWde5ggbp7k+HjOsAKgbOVwqrrJ8Ihm/FbeeL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.5": {"name": "ignore", "version": "3.0.5", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "b2613cfc4666183185544b48c55169071ed5d2f4", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.5.tgz", "integrity": "sha512-oxdUnfeB41HJY///6trfbGKn/ahljlbX3+VBgxXEXFeOwk727TkMGij9KRC2KX6csXjZn3JlLIqkdD+UIURlwQ==", "signatures": [{"sig": "MEYCIQDtFnqRxuaYgKYc1YmMggXTVb23QXh3DpXb13FnwZlbEAIhANeyOtoLV5WgltGTkOxAQlDyGikjR7Lcz/1yK3WrAjvF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.6": {"name": "ignore", "version": "3.0.6", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "d21f5d2ac2c91c2ebdd7c419e13450cd6a87746b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.6.tgz", "integrity": "sha512-cIRt7nTyu0Ub+wh+8SD3hQ1cNxFgpJQ8udu5fz6ss4PALcBYHedmdw4O8nPEWhfYWgTrKVW5lv6Or3vUdZX8BQ==", "signatures": [{"sig": "MEUCIQC3qFQEyP2QX6sm1VPMmw8uMpWhvzIwKuALqCd8FgIJuAIgQ3U+n+t8N7E+sK7svnQqMOBiF+nJ08MSWLE7XNdjVT8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.7": {"name": "ignore", "version": "3.0.7", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "c3c7e62d660f17fb086e9e8008ab9ecbeaf24bcd", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.7.tgz", "integrity": "sha512-+Z3TT5k/j1EYo/0XHvmlBTAgjHE8Sq7M4nJJQwuZdEAHhc5iEzMZ+mSUqhoPbTtnHYC16/6YX4JUHY+uAUoddQ==", "signatures": [{"sig": "MEUCIA/7KuE5FiGuqiAtTaQQqMthO9ZURTbnGxUJFok/oUIjAiEA49T3bA8YKhXYJR1AzDpFsOnUge2xNFR1r+WMPMlB+w8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.8": {"name": "ignore", "version": "3.0.8", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "ea5f65a087935d261410333221f7d55061229ef1", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.8.tgz", "integrity": "sha512-/PyJWOOPB/JOiDSMjhtCcMzYp4sdYGZkuHuiLgYeyNPaL0dMQQhLPZiReQ1zJS3wQ5sn/2D9kry1Hu/8ZwcKbw==", "signatures": [{"sig": "MEUCIFIBibpa1UhCcWRURJDkGjm3TgDSYUjvBauup4TZGLSYAiEAtIrhPiYIQ+4GVt1WqpB+C6XSR9sEKqtutx/7M8E4Hh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.9": {"name": "ignore", "version": "3.0.9", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "6c0d8609818c5689f71621511ec3454a262d5c83", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.9.tgz", "integrity": "sha512-V6ffA/gKBg8hBA8kO0HlmhzAcWjFYeEwGNCxfQkwZbQpoqkQqvuTulTIoU6DPNh11bKqyPlQysKGDYTzg7NOtw==", "signatures": [{"sig": "MEUCIDHmS8PTKJGIhwzdSRe9lhxNNza8TgmT2h5ZtVAvPmweAiEAst1KvdBp31/bZpB+BMqn1tNdc8DKfZcnAk1C61bKWgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.10": {"name": "ignore", "version": "3.0.10", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "f729abd3397a960ed805c853ec8f1d9567f5f102", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.10.tgz", "integrity": "sha512-0GwPOEDbsk+Lb+Cghe+RJfMVoqR02/lewBWGbSdrE9RZh0zk6iJbNPH5oAhrh2ilMWG5MS7FYxBq/dDMBdz09A==", "signatures": [{"sig": "MEUCIQCH+yyhnYEHLFAAcWdSecG7TgNfEdaELR9Wdy+tBCpEhgIgc+b2B1WK3ottY+s6DUC1TKbV+CsJWRcAybkvlpkWS4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.11": {"name": "ignore", "version": "3.0.11", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "bb3439e55bdcdc18aee4d85f265b315791c06fcf", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.11.tgz", "integrity": "sha512-nocgeSgLcsj4ayF4UOh93GuCuSM76jmD/PIqV0Ab07vcKnhYRC6A9izPe0EiYUq8a46HWZRRrvZitzqjJirRew==", "signatures": [{"sig": "MEQCIFeJhREfXb0l9qM1YbdCQJFt7bXZ69KroF7Smm+L7KyxAiBJDw5b8gm9fND1nraoqlAEtGQmD7UFArBC29cFwvaTmA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.12": {"name": "ignore", "version": "3.0.12", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "6f76d84ac38279c49d1c66201a5dd9371120db8c", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.12.tgz", "integrity": "sha512-V94De+D85RINmaiyOKdzvwVqRkCwzhH2XbvBb6kVOC1SLbPSwZ86SnU1OVjjEqcDU9GvkyJZLx6IwyW2cwtKyA==", "signatures": [{"sig": "MEUCIQDJmKICuRPiZIlpTEWl0jiBl9fHZPFSnlhnz0LwqswDZQIgRMKEc5J+MGfg8nKju7bdfx4cwnYW34A+iOGSlbVk67E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.13": {"name": "ignore", "version": "3.0.13", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "b6ffbdd44496e9fd588bf5ebe6d01c5380938647", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.13.tgz", "integrity": "sha512-OHSI2tT8U05AsloieR3ZnaCxhPlXzqw8htlH22EfXQfGIO3h0fruq/TzOJlyTj0Ak7qwpjv0VBtm7byB9szPOA==", "signatures": [{"sig": "MEUCIBu+DeckBqm0z2Ic9ZO/dqwLiOcY0T3UGjzvTnNXUnGRAiEA31SIRX7oG8YTFKOFqJn8JrmdHlPM+VXkYt0zIbaf5mY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.0.14": {"name": "ignore", "version": "3.0.14", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "1b07d5d0810ac2571aab1a8b33485f2c48fb130e", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.0.14.tgz", "integrity": "sha512-t6yCBw237TWLa4WuOfrglbD4/tXHa4IxwVbhp2lc519o4oE2TK6n1hh1rin9BHx04UNMMrbuiXdz38JpEq4HDg==", "signatures": [{"sig": "MEYCIQDARh2iq/hft8yhgF9AdARsm52exhoThXlAECEmaxP1nwIhAIl0QCGr9tWQllgn7SDsqJzsuKjq9FqGHNm+N4iHmZBu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.0": {"name": "ignore", "version": "3.1.0", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "9c712aad19cb19ec1a4ca1d88038ff7b4b3e7a75", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.0.tgz", "integrity": "sha512-5AvQgW2Rg/WLIJ0hTvVY4wRu3j+9I0O6Gx9HDF8ZHdQTBkJF2OMlYxRosez5mkpeQj7V6MiO+MBECzfjJCo7kA==", "signatures": [{"sig": "MEUCIEO4qumEadL9XyZWB+Iu0w1jItSXlOdO3HAmVs/utsIdAiEAtdp7pycm5t+DouWjJie04X3TwVdxAW3JdRgS6PTI52s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.1": {"name": "ignore", "version": "3.1.1", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "09e941c520c61452a8ec127fe70b4b6e4f281cde", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.1.tgz", "integrity": "sha512-NlUQyvew/se0/GekcwcLv0dJMwS2Ecfr1spahien0Uf32NgM65JORQG1a0yySXW9iUP1Md1KRBJ4qmPOb0kGcQ==", "signatures": [{"sig": "MEUCICQAQ+nmgsv2q2xaGk9TlOeNy0EX6DwxDvflKsjhMTZ7AiEA1cvE9G6v3fInKtmGQKp+/lKOUDBEqxTSopcAvkWQ+9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.2": {"name": "ignore", "version": "3.1.2", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "dd17765e9233b4019762ba82b892202b0980161b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.2.tgz", "integrity": "sha512-PKb/XF7/6PPJq918K57m8c42cjo8MGr8d1INLVR1kxSoRdkjIXpOiaxFo5LuYTUWByc+ukNYLfHfU4kji9zahQ==", "signatures": [{"sig": "MEQCIDDUaqdxA/a44jmxlOGBhScbHjPpFMmcRxw4inYz2CSNAiAg8yMZbxZ2MCpsFt2Cqei5w8dz9VVzqeyVErTH9DBmQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.3": {"name": "ignore", "version": "3.1.3", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "9e890c0652519115ae9427da47516bd54d1d6999", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.3.tgz", "integrity": "sha512-8jbofPRzl/bV3ydXBHqvr9OnuC4wzp5sMUB6f/gopdnb8jEUmUYMitUAGi0IDcGNd69N2N2cJYQfGqWxnHO5xg==", "signatures": [{"sig": "MEYCIQDeb6PcVmk1PcjeaqMK63aM4mcTGczjLQSH9piERQHRpgIhAJ+13osyxAczFKlWRkwQPJNmE/igf3kuxL2U6gMnGYxn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.4": {"name": "ignore", "version": "3.1.4", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "aac13c8a69f58088474e5140bb0723f1345e7aaa", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.4.tgz", "integrity": "sha512-XRTlkU+oIVOcWNCIKc5V8IJDfO4lVUzoG3VYp4B4sD0aHXRQjW+NURx6oIbJfOdUBlYW/WYd27MaWtojY/5Tyw==", "signatures": [{"sig": "MEUCIQDpX9zpZGQOEy0I64OQ84cERPU8nS6Vbr40WsmmpqOVAAIgL0N/KgcVqyWn1rCjDyhSJHxfoM7PTVQVSqyDFGexKcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.1.5": {"name": "ignore", "version": "3.1.5", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "54ba1eb92ef9fff8d49e5a1fb23961cdba77eb7a", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.1.5.tgz", "integrity": "sha512-FZ+dWDQIXh+XNGyC92lpLajvhZsRhjjNQ+RV7fS9fQ4kEvx9WRXGTUQin4wfDePbWIDWNJavfgkVPK+fGqSuiQ==", "signatures": [{"sig": "MEYCIQCNrVuQ0WmeXzN/wTh4EbuG0jY2Vfa/9gClf30VQd2sFwIhANxTZHZ38x8LsD4qYjAlZxpXuLs5eqv16YYrfXQHV2+A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.2.0": {"name": "ignore", "version": "3.2.0", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "8d88f03c3002a0ac52114db25d2c673b0bf1e435", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.0.tgz", "integrity": "sha512-cH6yGq1zCxPOcN8tUmbBABAn2wOKiIHBIYnoy1vMtUJ/h9YogNGEWEKDD9FtJ4+6LqR5H+CrA+XhrjVbwAE/sw==", "signatures": [{"sig": "MEUCIQCcEGh+ZHah2uht8ioZFoo0PnqKfVzXaWS3uiUqFfD/JgIgcxVPaV1pCMuoSXWn87abclzousJ3exolStuRB8IsAUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.2.1": {"name": "ignore", "version": "3.2.1", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "c614608a4a5406e8297cf517a7b333234068a7bc", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.1.tgz", "integrity": "sha512-jtXc7+t90aXYTa+44StdX0R032cfCeW6Ylv5JOOHFFQYjCy+5vUYHEcKD3wI/k13BtDxxOrlclMaAPhJ8wlkOg==", "signatures": [{"sig": "MEUCIAtO5xfnG8Rlgs3yDQg59AuS4JC4qMlZnbU0u6aTINDKAiEA7qt7759xWT7IxGMtBYKEq65e/HVhBGHerSEF7g2o9k4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "several bugs fixed in v3.2.1"}, "3.2.2": {"name": "ignore", "version": "3.2.2", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0"}, "dist": {"shasum": "1c51e1ef53bab6ddc15db4d9ac4ec139eceb3410", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.2.tgz", "integrity": "sha512-/kEEGWvuI3XdEtm3W2BU93omTfehSgf5R22nR3TkZ4I0slYbiVnjqlc73uHfndf12Pn0Wo+uXUva0n1p3/VyDg==", "signatures": [{"sig": "MEUCIQDKsVR/snsurob81CufQwzgUMk83Uz0+MwoTY5lO2FY0QIgW34C7ju2w1y7wwLN4NhpnaCUKZTQ2JNAnQyu9yZ5T98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.3": {"name": "ignore", "version": "3.2.3", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "bb266840ab8c38f7556df998682d45b2102e433c", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.3.tgz", "integrity": "sha512-mlS5frVrRQnfBJwS0kwYNmLNAwwWaB9nynb+DsEKw6o7nu9IpNPqvUJmjSauh88+RNbCj1TdLqoQUqfED+lRtw==", "signatures": [{"sig": "MEQCIC2w1iIYe+uOgRj0sJRxyKJFAYf3QPT9NE+XAnNh/GOqAiBVcnSpqV0VeJKkpk/Pg0Qymesd0p+DH/wo6MJxkelArQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.4": {"name": "ignore", "version": "3.2.4", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "4055e03596729a8fabe45a43c100ad5ed815c4e8", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.4.tgz", "integrity": "sha512-pAgA00hxl+F8OBKYLi7DGis7KveroArYbZowZibOn+tLhU48SkEPhcf32IsACPxAIQoQH6dPUd1het511782DA==", "signatures": [{"sig": "MEQCIHaCl88Y1iiiAcYlAATFixIlk8UpHPWSUnXjnbZukmTZAiBAI3LLol/GbXStuGHkaRm91ooFNZ1A3MYHddx3X1ig2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.5": {"name": "ignore", "version": "3.2.5", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "6437903354653e32ebbf562c45e68e4922a95df6", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.5.tgz", "integrity": "sha512-6P+5blgXq9zk2/u5ho46hq8WavXkVqY1nwbGfEaP4J07W09PVOsM85A2FJTHiRBqPL0drI62iDPEaZaQ5oBl+A==", "signatures": [{"sig": "MEUCIQCRHlylQiwfY+G4nk1wynTMz0G0dYyEMDle6XYyx10B0QIgPL8kbO+kC7UTfXUWBilhDWWBJJyPl+Ji6nnEeqdeOuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.6": {"name": "ignore", "version": "3.2.6", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "26e8da0644be0bb4cb39516f6c79f0e0f4ffe48c", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.6.tgz", "integrity": "sha512-6X+/4O51yfrZ5F/eTL2XO8EU2USFgt9UWaWkVJou/V9JMHm+MdyQf0ZQqjc7Y2CtxJwepmrwbAdTDGuaWRo0JA==", "signatures": [{"sig": "MEUCIDlPRQTzzLXnMOKBAQR90AT5l8WVSUqYFVX8yIHTp9KdAiEA9clkvHf7Q5BVkAcXF7Ez5Syog4vvA4+OHjnK+tys0Yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.7": {"name": "ignore", "version": "3.2.7", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "4810ca5f1d8eca5595213a34b94f2eb4ed926bbd", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.2.7.tgz", "integrity": "sha512-1TOmnRz9dPdENb7r5U4ChOc4Nf9KajGJArEPDs//IBHGisx7OY0KbQZN0ipmGFImuTzFDv7u0hsGY8Hbhtb5aQ==", "signatures": [{"sig": "MEYCIQDB36tY9Vwwbwdjy4EhEaUPzAwR0FhE8K3vu6m/XebqqAIhAO0XzYzuI1LtjXitMJGboVduVYYHZV1d0C4ZSlzGpq7F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.0": {"name": "ignore", "version": "3.3.0", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "3812d22cbe9125f2c2b4915755a1b8abd745a001", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.0.tgz", "integrity": "sha512-JkX5lAQk9mBbAa5NMZ2o8IgyRgsxV+Fq77T0q5gW1FNuAh7QfE8S9mwj8pv6XeBHihS6f11dqLPYKqwD7t9Ggw==", "signatures": [{"sig": "MEUCIDSllT5VsNgcm8/Wcre2j4StHW1mYIAdgWfqtmlp+b9XAiEA2RipEw+lrONH6AzMmIUKp3Zo7LNtuu5eYNHWXAeX31U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.1": {"name": "ignore", "version": "3.3.1", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "2778399a2c12c6a1f1a34df590a5a07d5ef15fb5", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.1.tgz", "integrity": "sha512-5pezEUwgnVATQnhnncdh58VNAvFtSCQ7389I9HqBDjrCgURnFAPcp2WQJ9xS42e+/fqF23mDsnfa5U0FZVEngQ==", "signatures": [{"sig": "MEUCIHvk5hVmWhpVs8W8DR/zYdL/ig21a3WPZQGUxD1jhgRbAiEArqWj+aAps2eqXyD2ub3O/pdM5O2/Fz53eJdC7oY9Q4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.2": {"name": "ignore", "version": "3.3.2", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "93b43a377aa22de9878defc60be9371cb894f784", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.2.tgz", "integrity": "sha512-+nshm8sGAskHzwcQM7Lp5WjKND6OF2tLfqusuq7E/QjAPBPB36hk5hGlJUFji/C2LVjcTzonr1bFBjhal+uJxw==", "signatures": [{"sig": "MEUCIQDbUqJxtvCa/PUZ74h7jv8loMQiTt7alLerkHwuntG6zQIgOmMW+qYejy4IERCKoa+Gg8yHQ/jbq+GyzXEEzaRVjW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.3": {"name": "ignore", "version": "3.3.3", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "432352e57accd87ab3110e82d3fea0e47812156d", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.3.tgz", "integrity": "sha512-EreSWopcoOuUkFfoYLwnaiDVfyyI4vmaYJN2k9XtwUH0GBRjXcJ6WC9yLrx7+5V1IL9VW+AltFnFG+N9Dp467Q==", "signatures": [{"sig": "MEQCIDIgg8e/iy5tJgUxkVF9oGRcWdAZ0sNj855QNMwpQVlaAiA2PhLyaLIamJYH3uyrdh5lF5E59sjNiqWPxZ6JmVwTjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.4": {"name": "ignore", "version": "3.3.4", "devDependencies": {"chai": "~1.7.2", "mocha": "~1.13.0", "codecov": "^1.0.1", "istanbul": "^0.4.5"}, "dist": {"shasum": "85ab6d0a9ca8b27b31604c09efe1c14dc21ab872", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.4.tgz", "integrity": "sha512-KjHyHxUgicfgFiTJaIA9DoeY3TIQz5thaKqm35re7RTVVB7zjF1fTMIDMXM4GUUBipR4FW8BvGnA115pZ/AxQQ==", "signatures": [{"sig": "MEUCIQDr8HVn/qjDdbFRqvcS9h16T6Koqd9/DjdzbNDl2heojAIgNPebQC4iolNBGjrdluir5FU5KNy5mgjB954pDj57eng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.5": {"name": "ignore", "version": "3.3.5", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.1", "codecov": "^1.0.1", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "spawn-sync": "^1.0.15"}, "dist": {"shasum": "c4e715455f6073a8d7e5dae72d2fc9d71663dba6", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.5.tgz", "integrity": "sha512-JLH93mL8amZQhh/p6mfQgVBH3M6epNq3DfsXsTSuSrInVjwyYlFE1nv2AgfRCC8PoOhM0jwQ5v8s9LgbK7yGDw==", "signatures": [{"sig": "MEUCIBdPay/kcRibMy73+mxnH/EedfLypgI5m5PEHL9UkgheAiEAri0HilzXm5jXE1C2an04hiug2ZDfa6VWB+YmQJTdXTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.6": {"name": "ignore", "version": "3.3.6", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^1.0.1", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "spawn-sync": "^1.0.15"}, "dist": {"shasum": "b6f3196b38ed92f0c86e52f6f79b7fc4c8266c8d", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.6.tgz", "integrity": "sha512-HrxmNxKTGZ9a3uAl/FNG66Sdt0G9L4TtMbbUQjP1WhGmSj0FOyHvSgx7623aGJvXfPOur8MwmarlHT+37jmzlw==", "signatures": [{"sig": "MEUCIEP0AkFAcWYoJy6u8zxOCqFXGgElJdE53DASPjouh9EQAiEAwTPQ47kg+TEpbcj/0Y9XdSpavOuQX3ljr6shh0uY/VQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.7": {"name": "ignore", "version": "3.3.7", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^1.0.1", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "spawn-sync": "^1.0.15", "babel-preset-es2015": "^6.24.1"}, "dist": {"shasum": "612289bfb3c220e186a58118618d5be8c1bab021", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.7.tgz", "integrity": "sha512-YGG3ejvBNHRqu0559EOxxNFihD0AjpvHlC/pdGKd3X3ofe+CoJkYazwNJYTNebqpPKN+VVQbh4ZFn1DivMNuHA==", "signatures": [{"sig": "MEQCIEh/D3/KBmcFf33dmmW/Zh+WMVJqmUDoR9HqTxpR8hqjAiAxamDVp0lNg5V3M373/bwE/1VLvTZZzTGz+fG8QlSW5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.8": {"name": "ignore", "version": "3.3.8", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^1.0.1", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "spawn-sync": "^1.0.15", "babel-preset-es2015": "^6.24.1"}, "dist": {"shasum": "3f8e9c35d38708a3a7e0e9abb6c73e7ee7707b2b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.8.tgz", "fileCount": 4, "integrity": "sha512-pUh+xUQQhQzevjRHHFqqcTy0/dP/kS9I8HSrUydhihjuD09W6ldVWFtIrwhXdUJHis3i2rZNqEHpZH/cbinFbg==", "signatures": [{"sig": "MEUCIQDh3z5j/+QecO3uKtVycBAKZohOaBu44shgUQIvFmttWgIgPBsFtDDmMl1lzF1zbc4jgQm3CMFX6C3OJfNTXGiW6Uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21608, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4TP1CRA9TVsSAnZWagAA4p4P/R5oqUaLw0LpV8RYwsYg\nDlhrA8BfYsKvAtC/N16olXT2zBpcY/ubvaUHQSy5DhLnl/ZCGoO/QxZ+pyDo\neqc0JQlF6W73ivNBPreRlTL8kgcDPAZgEnvzanG9JypgsArvDWSbOV1IjpSe\nuvoMq9zDyctNyEzJTA+LhzglJJtbbQ4xBqDUfWV+6CTouRI96EzDgti2yEoS\nDnZu6mo775zkC48rB3ZPv3LWv3fvKnMhlZ/BH6V/HjRIlTY9EEJzHbwNggdT\ngkIvRO/uIGBGDY+yn7deu5hTTXZ4SdJ/Zm8ivrF8TDKr+8DcUraHbIxKXbJq\nO9aYULxj5GtB1mVNuXOPelC/D+JwVD6ReK1wzGwT8yrkF/ola8J3boshmq/W\n3l4pgqcLe31/RgyqX1nBVb9Q7wk5MX+PpSfKDSrLpeDFgmZkZZH2FUETfs6T\nOSzYR1pTkuhJ9RNg5heoplmyruRu27jjIlgBD6KXyKAKZAOtnlMMWg9/nCFI\na+XNbCMr+Y1Ikm5tGjNplBe4jzxiZ9eCnfJdVhalgxgvWPcoeLTpt9b276uE\nadPDysyhBK+28lYegyZFifKURsXvGjRJYce3H4odRIA/Zw3nlj/h1NmqE1nO\nTSVYwX1dQ+CzciJhEuh/vVnXDGbba3lq6AOoAubjpJWgQjXuBgnIL831ACBx\nalAO\r\n=cI3/\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.9": {"name": "ignore", "version": "3.3.9", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^1.0.1", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "spawn-sync": "^1.0.15", "babel-preset-es2015": "^6.24.1"}, "dist": {"shasum": "64c3cb5b4f100f064e44b05e64e1b3a1d43b0d7b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.9.tgz", "fileCount": 4, "integrity": "sha512-9LVn4F46ZRXwh/qYI1qvo2N6ixSCll/3AqTprkNt9lUQBUxRIZYTx3AZWwPXQaEYkjX75Dc4nq0ZZwIcPyruiQ==", "signatures": [{"sig": "MEUCIQDML8/fPsPvTWY9O+8j66/GoL8SC94oh35G+myawoGqyAIgFyxY37f1GdwoeDdas5wN/EaHsKMRJb2VUQ+w5mgWVmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKwFcCRA9TVsSAnZWagAAYpoP/3GnkmKiUO1g1nZyj9pa\nfCGxHbhJ5EV0ZDJTR+zLXD/Si9jX7QVxZWqQMFEUYD8Rn9wo00uU708o6B//\nnlXVUV4flViSHOYnfz7DMIAtQNJqNa04CO8evK8y47O9uJncEUTWH+pJwQMc\ncfwkGDilzOw7IemaAqfuCsor3Gd2OxiLfOYY4AL0NbXdHVa16cgURrGqJsX7\n4G6tU7hTfL/M25XB5znzV+V5pQ1J5HJXq/8WudnJf2nPbLYEeheb+CWuDAFv\nLAVi+UiRUSmWzO+0D4Jl9P16evcHoUE+tP9YYUE6RZ7qjbmVFjq9Smr3RCpH\nMr5YrnmWiafNPEE0tIHBEiGWeScAlDq93yJ7uJd0JA4Bbit8aVd2AeAr1lN9\nuh0RDcGFgccn434cdjp4qylXYREEAxzCZhmgauHOul2rRqxzXlXM43/vL/kH\na+ifRjOdv31AFRX1bRAUHEFHcsQsfrf+27ayjZM2eBlDWpFhAKOsNoCKnosO\nyQyIuGXmedIJpUT68qfFcPWoK6Q187wQpDlgPmBXjGOH7uVZJOTeAgv43Ub7\nyermPwOV012jcrbZvWbrsjqWu1lSQggahGM+tWZO3kP6R9AA54R+0/5sIFWZ\n8VRw40Yf5nRX/ccebge742B2beqlSH5IZ6mpfbSRLOOGZf6pu2689ybbfQ/b\ndWn0\r\n=c/L5\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.3.10": {"name": "ignore", "version": "3.3.10", "devDependencies": {"tmp": "0.0.33", "chai": "~1.7.2", "mocha": "~1.13.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.0.4", "istanbul": "^0.4.5", "babel-cli": "^6.26.0", "spawn-sync": "^1.0.15", "typescript": "^2.9.2", "babel-preset-es2015": "^6.24.1"}, "dist": {"shasum": "0a97fb876986e8081c631160f8f9f389157f0043", "tarball": "https://registry.npmjs.org/ignore/-/ignore-3.3.10.tgz", "fileCount": 4, "integrity": "sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug==", "signatures": [{"sig": "MEUCIH6ctW4sW/GEBgDPV6i44guFP3GLAODT4yJ/kyH6fynnAiEAtfbahjJiZQD6lI3mJP7ZbzE24NWmlD1nia3giQsb+78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbKwhICRA9TVsSAnZWagAAuwMP/0Rv62d2WHVVMxPQ+tRN\nxmlJ0WCU9qCoXE1PHbrO5ehoqCs3bM11fLiyDAvccCmqo7KqPMqbg4eDvHk/\nli0SZ/xu8sRVn4shTZp37iLuWRa9wYH9yo11bXgzvG4bqs+WR0kzrsOuB/XC\n2K0WsJl+auIZu7LoQQj2A3ImHq56g9yto97u7fLKHg4t5xd1ATH3LPPrEWm2\nKg9HSufS+SD0JNxmVN/SLCfrrNwHQXviAJQkrYm+tyCqkEfPZrSsqH5y+hVa\nxPxewTs279l+1R5VMUTeBjILMywPu5AlJbWDhRKUjIN/ZCWDtbdFidHUV1wQ\nb9UGs3z+2//qL9Ywlhj6aL0yT/NGg2D6FU8iXUhDfsk2+284kyzdDbcczri5\nOi8iW/vjGjknklwCKk+kZ5F5WJ1GoRjjKsj9PBfsb5Vd+bN59bAGr32W3wfO\n8AIRxQrytygjul+c9Lkb4yHj2DxefQUaNXpYUMkgpw25M1oa2bXYzhNi2UaB\nAXZURUWr6yCt//E5oeyHHKQb6bkHeSFe72wR/L8fR5P7z/eAB04Bu7dImRom\nIg0GyiQLuJjZs3+/kGKRae0ZZJ4UK8p5ge0BYjh9mxqk2yXOoNRrER7ioPEr\nlwy/ZtXmOKNKwM3X1QhsBA0fYsIL8P6R5fxbuBZpVHX+oqD5qLlzpOgQgSDW\nsdI7\r\n=ZTby\r\n-----END PGP SIGNATURE-----\r\n"}}, "4.0.0": {"name": "ignore", "version": "4.0.0", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "43b1839bed2e6011d91a6f3a80d7253467782ad1", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-mpvs0moBKEwjP/i7V7txWinseHOqvIb8Ej0LahMzpP8T7TavluadZ6HsRSpO0JmZcapSumU1j3NlCcxvyLCefg==", "signatures": [{"sig": "MEYCIQCkfrr2ADDRmTNz/TdTSxI6nt5NRbJyhWMf3s41MvHr8QIhAJ3Fo67kHRBwowLe3cZOMaMm81trvXt455kQ10L10pOi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34782, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLDe9CRA9TVsSAnZWagAAdwMQAJmrEE7EvQS5TIdqM4Et\nEsP7kniEDiznOYdenE6pWboIi6lgC8hlKKT0gYbyHm4zBwbd5vAgGUd5+oIC\n23l/IF8yhSF9nVKMkqhnftsKBA1Po1z8Olkcrf5SaGonGEOfMIqgNsS5YVpr\n80oNiCTALaIh/qI34YdsvCnHfXW5iu99NHNrAjXt92bFbwAxnjIxxr87Cbmi\nUPC0CshhFY0xT8PsBj6R/t+0g+jbBJA9wBy1CaOVCBsMZjNMy+ITmlgaYYQE\nTircz1ufSado4jUYDWM+SjdHYO0k2Ab3ReMxzUTdRbxps7EZEnOVI48APoKJ\nFErDUzqIQD8a8pUY4ZZ2Xr9qbdfpRjl8rOmPL0OcU+1Nzixr3Zy/CLkPQKcQ\nrpFILQVN+6yoxLcOAB6/BNo5t/QNaN8s98Wf6UOclV/V9Dw8aafxo4gs9dt3\nyzq1NO8XuWqoGLUttPhRP0+eJxgBTJLxZtYOdRv2ZPFO2LM5MAbwzjo+3Gj/\nQJft9vCU1VN1km5cFrS+iSi6P8Mf4cx2CojlYUbhFhAs33JEICvzea57hYIn\noMrRjxI+a26X3Tv6QHvchfjfRxjL2ebhhpdLRzP6FY4CXS481bi9Hw6F3lGF\nA6BKMSqPEkIldIUAD1Ywj4QaeFBBuAY7d0F2w9Xmf3agywrtcIdusWPDOPwg\n4dy/\r\n=dV3p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.1": {"name": "ignore", "version": "4.0.1", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "030066be226d0376532d9022d50f8dcd5905fedd", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.1.tgz", "fileCount": 6, "integrity": "sha512-LX0UA2/v3je72Ppc/dsqEC752wQriqFQS5ZATVYLljhisN92fS5eSvwzCQc4zjFmhMFMeTNtRu77RdeFgReDkg==", "signatures": [{"sig": "MEYCIQCyvXUP6EH2SQOmLghLU1eqfbdpgLcIvZ1scqxYZEe4CwIhAPFNxYrl/3i0GTTUHcxWqeUHelsiwOf8P/KjW1gMYKFC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLFDmCRA9TVsSAnZWagAAF18P/0I0XyKbA7l1MC053ntn\naTWmfzD3JL2aSp4s/mR8aNApGwb61Apupr7UOzqw0K77c50aEv5KF9a2UcPn\n9xvt1uYP33RzVUH6+XclSGI3aq36BBLV/qdSpdIbSANESFugKafJFbYpXqiI\nP9jsOqAjQzgPIU/BTSLNGIXzIIzqX45/wW9S70htELSUciMMv0MoBejTgsk1\nXCcU1YAwBd5Bt/4dBOfs0p2ZtKVKFn072PjIfHVDR0kvUdERKJvW9HGIuyrL\nvt1bddWZF9nVeT91/PxZd3V6CVNfcqrqJ80GytmwH++BHrLH9zOZpYRMMU1/\ng6gKOjQPJ6FVYVZlRwOqdCtu8LH8qHqltOzibd8lcb/GEbkAx6IdY3l23lEa\nXjPPgylZzAebAo763jdwbxqRjK758bIypCTJGTAnLS9pPyVul1BfhWQa3nRV\nFZvz5UaDbzcEysyOMQMc/60j4RIiSnjAd3hfcPXOe3rfN0AHnsvuVTR77y++\nW44AjleJyfdfSCP5E20UZlHgvhGbG6pVAyes1gi6N82+R1le0G+NsrzGi7K9\nq21BPWOKBHH4CAAcYREqs9QKnOKTPvw6JVQ9sRxYr8mMEm+sU2PuVBBRShyI\nwq/jKTqa2OLs74d7oHuDj36TqtbBmbtJ8iqPd7nui60/S7UMumCd6myLHcfx\n+tI3\r\n=SNmn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.2": {"name": "ignore", "version": "4.0.2", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "0a8dd228947ec78c2d7f736b1642a9f7317c1905", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.2.tgz", "fileCount": 6, "integrity": "sha512-uoxnT7PYpyEnsja+yX+7v49B7LXxmzDJ2JALqHH3oEGzpM2U1IGcbfnOr8Dt57z3B/UWs7/iAgPFbmye8m4I0g==", "signatures": [{"sig": "MEUCIQDmCart6CfzqUJ5tRf5caj4lphMKJqV1NujHZHGj5w0IwIgN4U45pJFU6kQzPOCTz2XP35m2OPFSS3Uvv0JqED2ORU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLFhcCRA9TVsSAnZWagAAeuQP/RKo+j9qz0IIs9vOgDjZ\no+GFGGpm2qGJkGvE8bZqtLCou4PO1olJba6371l6Hm9V8CyO0Ikb8D94yMt9\nisXyCHX0DGbZMnVIRk5ZQmkM7XcVTwndSlxV8IpvZVFK81MPYlDJ8i/d54va\n2CXUT5Cgo0wC2t4TF0YnUn/MFfDGydlEUP7iwfAmJT2Bk9p0+d2M0dSKHX4T\nnQ5gtdtETorhRwB8COU40DiLbge4XZ2ZgbdT59m7F1DWj+OJcU/i+6B+W6lx\nqs+E66PA+WGl59e1YW8YRsGtP+Z9VqJLUT/m0l2l/eg0+5RCJBvrdtFw7qYY\n3KX9qSb/+Ci6qjG6l4er7ikSqDtN4dXKsN/x+vgFS3zytS2xY0fr11Tj8uwa\nfyAnGe0KlBzKGL04kVfwkYqtv7zx/GbVrslaQ7WdE4BVj1lv01gB+P2vogTu\nvmjiyBg/VM6d7Rr15ub1VxHaqw0+ULsXNkuaRuXxlfK1UmkOAzX25lAaoMcV\nbwu7zMK/c4UH3LHfTjj9ssjCp0hh2mYMB3nGZA8GoSzTn8prK/yKkhxcECfQ\neo6CAl4D2ctvPuHuTcwyptwhl0utJOKJiorYr7QXIEvlHwWfyEcYnYwIBfZ6\nodA5qu8i3su88JtUNyXRFLKIlJk2rQs5tKgZa5TmEwaT/4PLdbJCGuyJ1IjO\nIR42\r\n=stbO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.3": {"name": "ignore", "version": "4.0.3", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "e2d58c9654d75b542529fa28d80ac95b29e4f467", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.3.tgz", "fileCount": 7, "integrity": "sha512-Z/vAH2GGIEATQnBVXMclE2IGV6i0GyVngKThcGZ5kHgHMxLo9Ow2+XHRq1aEKEej5vOF1TPJNbvX6J/anT0M7A==", "signatures": [{"sig": "MEUCIASnsS7WUeTwl7h0qc6vNukb6u137cvTYjWjeBf1kbAbAiEA+ofY8C/5ymXr8Cw0xP8ndYJu1QkFBUHqbZ5Lj8K/QWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbYF6vCRA9TVsSAnZWagAAuYEP/jAoNa0eKcR1KNqBSJ5x\n21+gYUygyKO0T0h4eVt0fjEfRbpCiZdkNoD4H80rsVAKIrvLTlsJsz+ZZdVA\nGHR5lMzyyHYnQ7SIL0J0NI3pe0GPU0ZpPM/kyaBB9abxntAqtIzMYPRih+EZ\nz2b2V4+3bfg18Ied6h9/6SR0mVMaBKIFWPbBJsQEr9TKMz99mRFY8BBRv19c\nd8v1zKjcguWug0Tlk9IiZvezXnhnDFL0ba4tbfo4Htwy18GqqDF7nVjcQjtu\nL0oaMsofaVLfQxWytb9tpnQw+ZKW4icDks2Lz8jxwCuPcRFGKsTx2hA90R7l\nDOgL0friE/sz4bvdwzfUx4xVf2fpdOHIOgon4Fmq9On5feaSbaIuVs3EF42H\nJv3W9e+fBmWzgqAddSQSVvSQObBbZkiV3PaG63LZSqYb30/1DnRd/+h2Eazz\nQs496ajtnqh1ZYFuKABceXjftld4/0uCIEI4CH5511qhjHv9qIc9zgRM4vEq\nqhIg5lhfojGB6EOe5fhSzMIn9aEyurXO3bwoqXxUKKXUdZMusnL61IWy/pIC\n3AggPX2TaHl4d49it0z9m1t2IOMGlnBQEElNAXJrRjkrLdxq6Y6Y0gSgvGrV\nwq+cLjwqppXqH3qUYZJklS1PO+cZkJswmxOG9+HMIoC9yR8NbOT0unt6C6lS\nKQnE\r\n=Pn8y\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.4": {"name": "ignore", "version": "4.0.4", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "5b3ef31afe749eea3cd47ca8090f3087f3e956f9", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.4.tgz", "fileCount": 7, "integrity": "sha512-q3/Ov3oZyltXb8WJGZr/aJ/k3/3b64VqOJqA+2RYZPGqcZJAU8LftTnhrxYIfhe09UPzPEWiNdeJ/7wCgYVppQ==", "signatures": [{"sig": "MEYCIQDNX31PfD1wCoWd9leyv0/dGrXc3Bv11wdOpts8HQlgqAIhAN45DevBAhQIba1WJ9axxPL8mzFstodiMnzN9eprWvcx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJba87fCRA9TVsSAnZWagAADz4P/An45BhkDVWY7oiDU0JG\n9U8GGrytUUVb/3QdDzgiWR/kAdZt790ZYYIdgpt35jD8k4krZ797402BnHAi\nYaelpdVukccFS3Um+xW1H6BKdYgGeFHRGs6yHnnEJGXQinoUkygt4OHhSdIX\nJRwstGRtVptS4VDIYH7gLzsfnwkGhrquLAR9WsfqytRo7ZgDxKmTiy1W5SgF\n7EXCRh+4aQVvV6McJTuPyu5hhzB0iiUIQ0739ys9DB+XgTEZ0W+cBPvQYEGr\nyNFO5bMHxP8xQ0cfwll5mXlBoshr2oqyfQWO70Yg24mhtE1s0Moi9HKQRKNi\nrEaLjHt+rkOgmgkQlw+gB6D7nTpCmspzcu83iGYUYhscg/x3UqT7hwd7yMtr\n7cZzR9t22omOf8uwVHpdEI67KfH7X2IAtFjrgAaeQB89kbdJTCYEE+gystqA\nWr4Ior6zne4LJuykIJ5yV4+a9atmbOmoMyvLG7iaNRZ+QeSqlElAz70hKIj0\nHDupNHQqIbLhAoCRLp4E9wYclV41LNRsHCj/wFL/h2+WDRPs6nUq1Kq+Nss4\neyy0quxwt/sO10fqaICwmuHFcUjhkTkCmWQ13qG1ckxLID5b416peYTUPd9d\nbDZRUTiFQIj+lPWbUOc2N9Eju3s3OOuxYbI6U3RwDJm47oAgM49kGPEjc5Cz\nfMt6\r\n=QwGQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.5": {"name": "ignore", "version": "4.0.5", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.0.0-rc.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.2", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^2.9.2", "babel-preset-env": "^1.7.0", "eslint-plugin-import": "^2.12.0", "eslint-config-airbnb-base": "^12.1.0"}, "dist": {"shasum": "333535a20325ba4852c4ddb135d47392aa035e6d", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.5.tgz", "fileCount": 7, "integrity": "sha512-Q2daVnMtQJPacGrcCRyOEiI+syPCt+mR4YotoC0KEYeinV/6HztT5mUuVEj7UYyoNZ1jGYiu2XEem7I8oM44bg==", "signatures": [{"sig": "MEUCIQChNTinylze+D+Q8/IPkb0IIv1ZWdZpNGIp3EiyqKmG6AIgd77V4KDFcZlStqXTu8y7ChK4NpiLxCSuUwvt3GUDo9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37597, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJba9AiCRA9TVsSAnZWagAAAZUQAIR7YUW52nQN0I5Yx2Vk\nxDey86fnxAQAWELydgemSzkRkoQNrlVH6cdIwsR3S4p1Ta3N+3uPjjbdC4w4\nICI3ytZ69uFRdBMRT4OrUdZqnj/VSZaO/iFAuFeN6p2q4aZLroeAztteLmqb\nPiZC/JEhKlZSEHKP78JjI+ZZLWbWX3o84pJa5rBZvhQd9y9dGKBab+t4rQAZ\nL9WxEYbsuDK71wUEGus7p+1pWmaY2Lj2JzrShSn++nNpPp8G019wMldXBJgG\nWQwA7aBvduglYZZmahyA8gV1sE+uFIZrq8OQB8z5nca3nCkbQYIxUSQQHsli\n+30zYyUnr18sqgtgOFtpCY5Y+CutcwjsC7KeCxP0bob3zSyGWsy0JULQDFST\nScsgQzmaDR3pT5aqXsowUb9GIY24Ovk3qkCQysfp+lkQIdx5kHIfcAY0n4eY\n70emNtaTVi/cBU0C5XjlWYB0uyASM3wEW+MPZSZVlsuAu+0syCT+1754o3Ly\nHZ9CQ5ZqasTyqnY+vaaaYtNKe3oI2zDDAhHq/jNPNHn9c9IL0gL7t84aWFVD\n+biQMNiFsvtabr7UPyYgObQbk/qlzgxzjGFJI9wE8Uj1FdUfqGUJtrI91EUs\nG3sq3IQvrSOkfstqyCujNu9qoZZclfuVJ6b9NuifTwKLGqg44DUwc48J4E55\nQJT7\r\n=CMm3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "4.0.6": {"name": "ignore", "version": "4.0.6", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "750e3db5862087b4737ebac8207ffd1ef27b25fc", "tarball": "https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz", "fileCount": 7, "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==", "signatures": [{"sig": "MEUCIFvU+vDnyPcQnS7TmIR+lbi5R22U69/FQOZrdZrJWXC7AiEAzTp1lM/css6pGXab0MaQCcucQiSQvXnP+8m5XzqBwLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbb7n6CRA9TVsSAnZWagAA+gkP/20VCdu95Or/JwOHxRMx\nkf9Sa3LRjjeUbprdwFUgY/0szFUysXOWaeUNkDAQbifQvBUXg7QJsJ/khvSG\nN4/U7reDQoNyVyTC9NAP9ftYfySy6D6lYNku6qcTI01Kv5Lgka1EitJP65KD\n/UhpZO3gA+BdgZ1w+W6zwwxk2HgilyYVwO2s4Dv9HFwc7VqsiFpMatjqu/TM\nLbbHDH9h2SCKEFV6GC5UmA3GCAGTQFQrU/0LLiMFBvyALieUJ3YixxxEDemo\nIo69n3JkwwtdDo4AQsjVaBP7BO/BAOb3ImspFCnm8QGXV05y1I6Bk/zPSnpm\nCcqS7yKByTLdONlQERKLslsqGVPEs4XMFuYfVxYhRgQtKo1DUwKgcNjNDCbo\nKJSKRaxcwFfuSB9i4gTXhvnuPHbvOpbwPB8BJ1lRz7Tfa+XS+DMcJnF/gxRT\nqYmfZoZ08h/iY17TKR+0X9Ycgirx4hHTI+SZSe9o5OFxrBsjEUz1kbqNQ0Nd\nF9QoZ7rYjJaYQJ5CeFrg327Za3aXpLNcdWVaSVnyYO+pNEmSChpaHS+CarNe\nnTCcyOjSN31fOwaaNryDoUVtsEMMg05XJDotSRf3xNf0o8zAxqqnQcKJSDcB\nqAka3qsIjnB6SZ1BbJ8lSFZpBc3B7tlPSjbsQzcPD4n6lmHqCTT0HyrDNlhA\n+I1a\r\n=lO1t\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.0.0": {"name": "ignore", "version": "5.0.0", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "472ae5bde65b41995a7ca43ad5634d3fdcb60eeb", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-pztwzOo3khA5uk+1N6erjer98k1hBPsCA3EYoCBYEjeRKo1lYUEwLfOhnHuHE9ahHepH2o+Fc3KtUzQbmKTHAA==", "signatures": [{"sig": "MEUCIQDL0Vb2X7+4cymjRc2jV4/xWav015y8BWrkeRAYe3Z7gQIgcD7lmBytVBD5aKtuj6YuBu5LuNXz0j0IYlm/NBFbGzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44442, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcZ1sCRA9TVsSAnZWagAA16kP/A9zvcjbA+A9j3ZvPXP2\nK5Cw0xHX1dXxerFi+OM/TcCFvdIbis7albsxH5GnjtapCpwv6C8kz572Sept\n2/jezEOPW5oZ9EFaSRaPloK8AM3pC4cRGzHTtqV6KJ2ckRO/derjpOusiMTQ\nZRbSN2BhZHrVA7cqcUanNpdpOTPqubSeIL4OnXoJy60Cb01aTcXmvh9cJ1/J\ntv33cbS2uMz/bP/sYmrvHUhjDtIAFD+G8kwk3NNRnv4LJqqm/tVH+eduarJv\np31gVPm9MuHV5ZoNqhi7AjorOqKjoxrkFIT3cEsfDLSZwha37MXHiOwxwjKk\nR31vBkuR2zszw2WCeenb0g5ATV/LJV9uCvN3JBj7jqzWHo73DV1uyjZKut5m\nuv5LnED/WzJUC3+sGeQGSLHKqhiphZodQkHnYw3ex6WX3+pevjmpcbuqHoGj\nevRzVg8KE4NYa/2YmADcoHqeRjXBsZgVlfrluHGagE97qMKJbDc/52myYcnZ\nCaGIMCRLa12yqRAhbUM19cSpPCfX4ZbJB7z/Ti9L2kTSQzDRmzWsdo7fIIXA\nut8egCb1iR/QYYewe3fKc7RVQac5YpWM5fg6q5i6Ub2d4nALAUqtGvDMzdlM\nM2Zwmq4IyQO5GMIX4+U0yYjDRHDh67GcGvtrBa7hOH+LkoRPksT6gVGDr4yp\nlhZE\r\n=XJTQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "deprecated": "due to failure of importing in typescript"}, "5.0.1": {"name": "ignore", "version": "5.0.1", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "1c3a8fdab8d98b2a1408d5b136d64f13fd5512b1", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.1.tgz", "fileCount": 7, "integrity": "sha512-Hk5rrTD3446f7JwRRe5oFDb8uMKmA9XlaxSrixKzEryN8qxS/rVS1chEsKNQ8ploITn/2JUsKXq0saVIbXNPZQ==", "signatures": [{"sig": "MEUCIQDgewK/TvnMsi3jruPBJv5ZDmx7u9uUYh9k+nf1JCsMHAIgP0oprDaJ8fsA58AjZ6GuqHHW9A95OtMruX3kGsQkHH0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbciCsCRA9TVsSAnZWagAAWz8P/iGMgxIbEQiGUvgKeaa2\nNt2no0Rw1bNkIU+3beF2MQdYcspRpW5nfqPV5NDkk/kSzRk6HYAaZaFUvGkD\nu1EMpIIf7nqUv1/9lv5U33dPi/vDlgIu+IwURbnBmTr7AqYtROjh2sExLw83\nahhBgK5S3GuwqwP0scQY7J7UIeTVZ02sK5fvKyGJFCU7q6lJ3005Eqcnmq6F\nhKFdcqRimXy/7WZ0yqKYV8iNOzz1BGIv2nB82EgB9xoRuv/pOjlwX+sy47nM\naPqDXehlaiKbF6Q33eLUthE18uSl6gaAthonjkpa99/eTuj96gqfeAS5PeiT\nRcCOAt1i8m0Z0NydhvOtLC7+zALmM7aJAtggLfryGlfTYLkgILw1VAkjHEL0\nMXw5yLgkCTTIn07koVmdnv07zCzISDrt8Bb1fHQGXzhDdJDzHAgkIq6YUtKm\ntrNgrzrOdWk4IxHPLdsoyhcR2SWGHZV7JqkaBl2Kag+W4hjLAdBiFHSLltdQ\n9MlRFBmhRl7R4rRsea1gpHCQnqwytTQ8GFrodhovamHisTW9YicAV5rufnbf\nZ2K0e/IC8F9bBdSCMya34YMsGpNZHfQw1RezOCC0g45xAlHPC9FOqoLfNEAW\ntHsfMQrIf1Pm3lXB3hikCOjh8uPlY0rCrdV+8guGOndY6gwpW/6GhuOmagtr\nhX9K\r\n=zAIJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}, "deprecated": "due to failure of importing in typescript"}, "5.0.2": {"name": "ignore", "version": "5.0.2", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "42ae2651beb40c8c53b1e16b2af1c8387d492f88", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.2.tgz", "fileCount": 7, "integrity": "sha512-ilxkgh36cTqJxlipxQdCOxkbQae5dIeCwo5fSw6pBDW8m8GiMTnadClKST2+aATqjs9BTHsi0IqOsTp0jiihAw==", "signatures": [{"sig": "MEYCIQCmZoiFzhkTjCK2G+Dh+XWnyQvNG0GW/UNJ7Isa4HTxhgIhAJQ/cySctntOUTSnzaCpyy88wG42tfHDrt+iGRyj2lwt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45121, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJberY8CRA9TVsSAnZWagAA9UIP/1Ypa8E7rRudzMbFyc04\nd4ltITSfNZdBzKE1FnTEu5rYhyezE9MUVLcRMWv28bLZL/TiDXrTKXoK0ejj\nCGyk79qiYevp+gGpNbq1SeNoSBPnmUiK1Q7XIGydEJf9yEkxbSbt+kxdDuU2\nxeJs7UYJ4qZqR5RNus3qF1j/OLEtvYdHpbhLR9AGj03AYalPSx2XvEzFupiK\nkNuhseizLrUkfq+/VxL0KLhJa+Z+J6m5x0pbGQm+osnYRC/cRx5U4PaibvYP\nUUsjhyVMc3N92Eal1Pqz5oIXhcwn+kna5Lm/1vRYTtF96PamQYLgu4MTTQOH\ngZvWcaAxSGUXHYlrEcNd+d8GMORQZlejmAzzHom0Ss1TuJIPF3rcY4my6BJO\n/wVVmkmNIKPx6Z8wXQaWIWiWS+YWMPuRn4gmEUhBJH4+UZGmiyog07jYHFVf\nA/5b/1ZsaIUlPblPm0GQSGw7Vkv5xuEU3q8DaBFNaJMowILTynK60TtWCV66\nZS4cHGfFGwblrpywr66BAsFEf8DWmoTB6Kzwo1Qh1ypKEvGLKYskqV52bnwl\nlisSzIYJ2NarC3Lhp5tH/hQpbKtB5rgIsmL+h3ae7fVv5dJ80nf52ILv5sNR\nbvdnJFYepFmWIHfznPVnx1Vm4RlA2A5MXay0uuCOvmBrSZu9E9mOmlp+VKzc\nAO8e\r\n=OBWu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.0.3": {"name": "ignore", "version": "5.0.3", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "b1ec93c7d9c3207937248ba06579dda6bf4657bf", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.3.tgz", "fileCount": 7, "integrity": "sha512-jJ7mKezpwiCj29DWDPORNJ6P90RpT2i4kfKLxioSb0VcGnoWuib5eg9dOXR45bghMYxVNUeKoJR1UGJ/sS3Oqw==", "signatures": [{"sig": "MEUCIQDfA6woWV4dmgyWlEDOZbtjqhdmYFFwDQeP08YoqI+DTgIgDvaQ8Ju8vcQZMQV9H9CJcq+DdZwD3RUH8AlmnsPYKGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb1ka4CRA9TVsSAnZWagAAt6sQAITvWmW2qwIhQuecYKuA\n50Jy4suoS1lhppQs75KwS5BnWjWXEnTsxnAYlSISfnWvD83J2Cn4hCJXmfP4\nn1P1f5qG/RlGYE6Lq64qdUw2gjN0yasVxZtfD4cg+w0AGlfCy0zifbaIvSrl\nQpmKA8JuS1thyLc3jFPujvzO6y0vVZ496Yba/6T2GaBy3iHTlVtp01GE5ae8\n3kI0uxD7WNGzaNIWj+xacQ2It4Ql2pSr9RwSAsROYOKiVANKS5tmgYnsBKiQ\nWE/y7SxCd6vxSD/gQWU2HqO9BNI04abylZYOpeOwPpnDGvRfi28EJBMQLXZq\naTLJ0dS5fjR3yyaQD7d4lzb0ced0T2gpOGTtoqJA9uD0OA6I0Yk5PCDHo5xW\nPIGJwctowkPTPXtxEsfhiGh0BvYJIZCfLUZ9GAdsnkdhf2w3mjL02cJYoVHo\nrgaPNNwir7/SgjktD5ReG8zgDXAu3jgludr0FWI9VELhUt+2muSL+8HwhPUi\n/RCMQu+oAlzEjfwkoJmsVA+ad4RuhTdj9HlDR2wrSo83R/lVwvtXkyF3S2Q7\nQUKxmPiw7qH5Lgc5xX2a1WA2N2mPmBGpEitUdPuW68h0E0VkT2iEIFjLPYHL\nf4HHw7a9DPqFDc6DH89lJuciONM4slOMBUz6zyQ8Ek4VVB/yxADZUboJwo4+\nx6w/\r\n=nKyi\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.0.4": {"name": "ignore", "version": "5.0.4", "devDependencies": {"tap": "^12.0.1", "tmp": "0.0.33", "eslint": "^5.3.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.2", "codecov": "^3.0.4", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.0.1", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.3.2", "eslint-plugin-import": "^2.13.0"}, "dist": {"shasum": "33168af4a21e99b00c5d41cbadb6a6cb49903a45", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.4.tgz", "fileCount": 7, "integrity": "sha512-WLsTMEhsQuXpCiG173+f3aymI43SXa+fB1rSfbzyP4GkPP+ZFVuO0/3sFUGNBtifisPeDcl/uD/Y2NxZ7xFq4g==", "signatures": [{"sig": "MEUCIAx2qeHG2hGu772XPNqRAFIq8nyy2RFIYHPSFQAC0ScmAiEAlenu/6nLYVUgYs5X1YRuT6uGp9O0aCAq2c/yG3wQI0o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3P9oCRA9TVsSAnZWagAANNEP/1bzRSjR/94+3wMm3FYW\nFPGCCiybg78MYnxYVyPJetMIel8mDdrBB5u88/4SjSHQOZ9/RQzXmaTeV89v\no+nf/ZFZEnw8eR0lVsRSngJPRlPRcb4KdhXBtvQznQIW35OXJNZAR2t/7VM4\nQ3chvkQdJNox2ljiEM1eJ/6G4EClhNPqHKH4NIPKgTw4OOqbhp1PbxfwPx2E\nH10pR3lM+3HbBU5JwTKT8/DjsyDTUTrwZCmgoh+EqateVRyt6ike5wAR3sDz\nHOmy5TRk/pve6irVUZZI1uY6liydaY5rQTnQg+mqTlqACd3xHwFRmM4RMp5y\nfmgMSvK5UHdR5T/yYgML8FSis1/YsnfuohJOkhtWUlfLfPmUw5UrqXReGfsM\nqtYmSp4JCRRiP8/s+4pdXKD1BfQD9QYg10nHvwCKZl191OYHKj3UM7AU23Bm\nJwj+YMaKCusWSKJMMN8/Gb7CR8ji4bi+vykyHi3sPQ6zJuke/Il0oxCAeKF6\no1pKmv3D4lLi3PGbhvJbGVoYrFVbA59mpH4Ru22N9xqHn6quJpmNehb4+D5j\n7i4k4nbteS6TcFos1MKIV4IDVNzwT+QU/RuW1NmjQvkGgEcnxARD90VeqGoG\nmUlHzMnB1pfCDHi3uLVY9DDw0lCrD0nrgBe05ZIqVA21TRvUJrlhUacZIfA8\n7IwZ\r\n=t4n/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.0.5": {"name": "ignore", "version": "5.0.5", "devDependencies": {"tap": "^12.1.1", "tmp": "0.0.33", "debug": "^4.1.1", "eslint": "^5.12.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.1.0", "pre-suf": "^1.1.0", "babel-cli": "^6.26.0", "spawn-sync": "^2.0.0", "typescript": "^3.2.2", "babel-preset-env": "^1.7.0", "eslint-config-ostai": "^1.4.0", "eslint-plugin-import": "^2.14.0"}, "dist": {"shasum": "c663c548d6ce186fb33616a8ccb5d46e56bdbbf9", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.5.tgz", "fileCount": 7, "integrity": "sha512-kOC8IUb8HSDMVcYrDVezCxpJkzSQWTAzf3olpKM6o9rM5zpojx23O0Fl8Wr4+qJ6ZbPEHqf1fdwev/DS7v7pmA==", "signatures": [{"sig": "MEUCIF6NthmkwqFB2CNkzXLyXWKcu7mM+BFbhB1K4nTopcATAiEA7ZdFgh26owNrZt61uotT00ZRq9V4PLLtIuhYX+W9IB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcS67vCRA9TVsSAnZWagAAcRAP/3tsZwRpTWjGKl14q+yk\nmj/Pl10hoSiDZgL2YPO0ZIROi3WnWLxlICeQEkRZU7Y35QIEoRXg/mlyzLTL\nDEq49vdxI3lJJNpopupLCUrB5MJ+vR6MyjgCAR2kPpOJMmOxLak7hp1ytbL3\njPGFfS6GJkBEYUXG9xkOTRafWs2k9ALqEiFa6Vajs7W5WqQ0TisKTxCRfIja\n+pABO/OTNuDd4zFyJpkgfRGuTyrlitcsu+m4G0eIcsdfyrSntjUzFbzu1lkK\n7G6KyvJYaDKdlOWna4/y8ehVLftIzCG5KDSXeuycUhh7Chx6XkchtlkPvEwd\ntbL7I+/5CEDRgYOc5Jdr12xybPxltLDorSSDlSiMzWDhAaAN8wxGkwh7p2lV\n3NzT54AsOIVuIQN6kmEwZ6Rq6yd+4cSxZB9BTyxBYxEesOevrUrf71Dg5zHY\nZXei/QUGJB4jv1KMfac8la4UNOq/Y4SJmnVMW93N2mktpBbRtaHVrh+URYH6\nvB8AtBZTkwCGEy3IkaMpBIecF34iAzI3b2FMupFfdX0afYfTlo6T9/lcNjkH\nWCZgxYJFB4QdQS2zVUL5ZfPFHsOTk1L1AK5TEQ19v6bFGmZQ+6AcFEVCtOtp\nNYtL6wbUh2rA3bAquBNu3wXNxzkAHEu+lm9HuRhI45aKXeEMrm5DTsQfPkJi\nJDBi\r\n=T0db\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.0.6": {"name": "ignore", "version": "5.0.6", "devDependencies": {"tap": "^12.6.0", "tmp": "0.0.33", "debug": "^4.1.1", "eslint": "^5.15.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.2.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.2.3", "spawn-sync": "^2.0.0", "typescript": "^3.3.3333", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0"}, "dist": {"shasum": "562dacc7ec27d672dde433aa683c543b24c17694", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.0.6.tgz", "fileCount": 7, "integrity": "sha512-/+hp3kUf/Csa32ktIaj0OlRqQxrgs30n62M90UBpNd9k+ENEch5S+hmbW3DtcJGz3sYFTh4F3A6fQ0q7KWsp4w==", "signatures": [{"sig": "MEUCIQC3+8tI9213mDPtkIjfReJ7hEngjyzC4k6clYyVhmYB8AIgPXmCEemC8EKWzLt0aMFF7Az1pjZkjJhB+A4vRBtHoxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcjjwzCRA9TVsSAnZWagAAwVkP/A0CQrpTVV8KpZMVfZl4\n1QMkUlbtl0yaBkhATKX8qrvBQ3rOz6KJj1DOdXDKkTzKKRqGVfTfXUXQmuot\nimGwOZDJ4Z8V6mdkI3GcVHxxV7+25HDAT+Bxkprt0WRXVGjLPgfUcpDy7d2P\ncEw039c5po0EJjBRsbyaYe+vMt3mWafgKXvyHKhPfkWtPQ9QA/88t8MbOJTH\ncKcSL/rB4AGDChc9f8TITll+ohjbeTkMwd4mb+oc5lO8BucvPdZvp0HhzkcN\n4vmjowyIDJt9UDNaP9CfOTQskixsCw6TUhCZ3NE2ia9vQ2MH1J8ctpAC51JB\nAYpLC7KvynYujYBSyb6qIvrcxK388/rnjqJjcQ4bNmLMRtlSyigiNIspPu/S\nMQoI8/Xow3H5gNQRvKQspjqJsGSfVdvqwZLNbfmtJ4/0XYGs/GghzeVxUaxp\n2pGu8Ik89yBpMSHvaxrk49BkIT2cCx37aj6AbtJK/LJ5DLfJOuEnROGQAvZe\nyHh0A7ujt+lWrkfzFqOlcRwPGqQvWDEGOdE7H5LBrYSR/jks77gVo48HCjVW\nwFtcyIZjz8yFFSh2I00zLAA7WXadNu8yKcyqd6C43cfXSR5J6O/6bKvPSkJo\ni4e18WxMcuUiUNsNmQqqdxnlov4tnQCZmyDC4ZWfj0EgMJJlR6JQUd89EV+N\nT2i5\r\n=PBBz\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.0": {"name": "ignore", "version": "5.1.0", "devDependencies": {"tap": "^12.6.0", "tmp": "0.0.33", "debug": "^4.1.1", "eslint": "^5.15.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.2.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.2.3", "spawn-sync": "^2.0.0", "typescript": "^3.3.3333", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0"}, "dist": {"shasum": "a949efb645e5d67fd78e46f470bee6b8c5d862f9", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.0.tgz", "fileCount": 7, "integrity": "sha512-d<PERSON><PERSON><PERSON><PERSON>wloo0gq40chdtDmE4tMp67ZGwN7MFTgjNqWi2VHEi5Ya6JkuvPWasjcAIm7lg+2if8xxn5R199wspcplg==", "signatures": [{"sig": "MEQCIBXQkNAY40dyz8G+lqYygmcXixJhXklhTblXF4Gk+g3eAiB9EmPrJ60RP1DtCY8KJrDRf4q3Y2QFv4/oq/OGBsqRwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsvQVCRA9TVsSAnZWagAAIn8P/3sCIZwdQCIdr+sEm8oL\nu6H2pl7FXaG6amhQr5Ha2o4vEuoFWjyCGPcFU6V//LdcCIM0uvICZ2EW6d1Z\nMCrjGgz9yrwjc87LC++1v1GMjUzgd1qHah9MgfH2ZBMhqx82jJgPJjKwVPaJ\niThzGECOezzXAWzi6hIxUhYWxfb7b/iaQSGUZDLU3IpddloVgwVaR3lW1io9\nFGz0XLt1maqpGIZEZNQQI0BiOHzL8jSImbEWaSDu5RED2ImERyeaNsYhkoEL\nXENn0KJwSEp+qZoMgmBD1ykxFAOUh4xljrTg89wPULSmiBQBvVkHi+n4dDGd\nco2ul18JncdNrFekQRsBWlIsSwt+N+ZPsNVZWc5ZxGcNpGwR8qk0ftCqJfzg\nZbeabmp2GQaiBrHFEmECZ1StFAntAIReMmCV/UvTaY8JT0abB+n7iFf4LUCg\nF/L9bW45IGRhR461zZia9Ka1GuSzN+pj1HIot4paUqwwEbTwGavj83dWBklS\n9DxA/jvESxC5dUdyS9aV10ho2iSXCiGRhWWZlxMB28IXPZZ/TLEA95pgTeBL\nMr8GgCZrx8uD+lMFZvoTsKqGcKZ8XVGWccghMX83/fdUzK2NM0RcnErTjs3z\nzrB1k9CyN3kKP2kHk13kEi9T+iVCCZlE8JFkvpIxtTO2Av60vOJdt4b+F2cV\nI6Ka\r\n=S/wh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.1": {"name": "ignore", "version": "5.1.1", "devDependencies": {"tap": "^12.6.0", "tmp": "0.0.33", "debug": "^4.1.1", "eslint": "^5.15.2", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.2.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.2.3", "spawn-sync": "^2.0.0", "typescript": "^3.3.3333", "@babel/core": "^7.3.4", "@babel/preset-env": "^7.3.4", "eslint-config-ostai": "^2.0.0", "eslint-plugin-import": "^2.16.0"}, "dist": {"shasum": "2fc6b8f518aff48fef65a7f348ed85632448e4a5", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.1.tgz", "fileCount": 7, "integrity": "sha512-DWjnQIFLenVrwyRCKZT+7a7/U4Cqgar4WG8V++K3hw+lrW1hc/SIwdiGmtxKCVACmHULTuGeBbHJmbwW7/sAvA==", "signatures": [{"sig": "MEYCIQDK9yqJ/6k9/pG1yMUDnG9Z3VFfSNAkEMLh0B8Mh/anlQIhANtMsmABlqmfQfdnEYv9SXR+a4f/pg4QyKQftO7nGPse", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcttvYCRA9TVsSAnZWagAAA7sP/06RDILF4Q5w28cD9Ad5\n2Jatup/5owgAUjfN0upXJytMc1ichOqa/5ALYbHsjuP7dTmTIXTmPwHoTr3e\nzBCvsSiM3tys5PCkNzQS7ELVKai5yBUPujGMZCkWAIQerilKZU5XK/vgs+S7\nl0IOxISy9ZzftDohmjR98bkbUUQhsV9kCVHyUxz40f8LvOy+aJdbCKwBtEey\nJA+l8C+GE0JAovi3eQl4otoaWmAvbEYhy3e3wBtazgcdM3doGsE9Q/NVKaog\ncxqV2l94ElJx/+o+Xb9Q+RUNVORNKi+ledeoT+cT6l57nEnjAq+hJn0kaTPp\nX/sr0j4IsloQ7S9sOGM865lxA/IQVG6R52/1mzZfrkL93RnFs8Mxf/duUnIG\niaimX01ceCORq6Uw5fSj5xHT/902la32XejUFKMWP5m1phjRz6NHW6ZXdZUS\nXgIOy9904AU424OavVWzZsnT9npRZbvrcGk6rqXPALpy6fAVsjIWQgVStdoy\nTu7jvgsTNsoqxklVwyWKLa7oZb2UmOrbVXwLBmsCUutPO5JHkSyM43FJPYZT\niuW1hErnWaabtMECvM7A9eFfgNLcAyKXn2CGo1UE5g1r5mXVlsV01yVto+o9\nXUssEareAvWrtPk0tJguqmleJnPxKQS0gJN5qCmi/0XFITywI1KrzCg1g3ZE\nfx+p\r\n=UnY4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.2": {"name": "ignore", "version": "5.1.2", "devDependencies": {"tap": "^14.2.0", "tmp": "0.1.0", "debug": "^4.1.1", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.5.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.4.4", "spawn-sync": "^2.0.0", "typescript": "^3.4.5", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.17.3"}, "dist": {"shasum": "e28e584d43ad7e92f96995019cc43b9e1ac49558", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.2.tgz", "fileCount": 7, "integrity": "sha512-vdqWBp7MyzdmHkkRWV5nY+PfGRbYbahfuvsBCh277tq+w9zyNi7h5CYJCK0kmzti9kU+O/cB7sE8HvKv6aXAKQ==", "signatures": [{"sig": "MEYCIQCvMHGvDTYHlmLGUet5w7Y0f+VhSVl/AHBWcsIXNCGXxAIhAMWbttfPiyhSJnXbUid/rUJ0k1S+2+anMgJyvZR2AS4j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc7TmCCRA9TVsSAnZWagAAR3UP/3NowrVGzSyFSuVSqPcT\nwB+D39W8A0VOoD+aSS8drnAECHqxtiBFCi/JbgtZWI3jEUETOPrRawENMDDR\npDVimR0eNMgSoZCOZJUX1fmsqr+/yBeyP5lKCpphGYB4FqgZsTB9xq/AfJ2v\nGOSbK54lIF89w3F3lCeNXMxaesvqhs7dABNyn7ie5P+ToJtZTnIuwyjlu18P\nkm2UcYpjfs0dpRUWmrY5BwiTzi3ubSy2y6k7XcXxszDzP5VVSWTAnkk+JR3W\np/gn0GVihy1sae1/7MlzEqqj78vZPgYGdeE7HmtUon5kxwq/SpjUiVr/yR6p\ntBnKbqlijZDCfpiNSaNMkTRkvrPOoK81GNaQ9CezKZWf+EfW2czSgBWRCJqu\noEWEaOmvXSy8gWvLynpsG1vZE4xs3jeWGkLQyfJCNywvbS2DPNNufvcLvtye\niAh9jvTEyRs/xK7iVR58raeuBE4TsGHqcCce0x84gPtHVI+So88U0sUXrI2c\niM+G9Tq4qGjHDYG2RsmeVbPEpVRd4HsF+8mQFzdNWEYHqYr+X4N8qApMpMgr\nSgnqYh/kOF+TyfLsix6P3lQ/7YHZpEjdt0UfXYllqSWCHX/xq2Sa0iDKbuMT\nBi0uewqwKmgbwGx+obpHnRlUBqJNKrFx+o+iInAzE160gL9SYujiQVm7Ts4h\ne8LD\r\n=t/RA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.3": {"name": "ignore", "version": "5.1.3", "devDependencies": {"tap": "^14.2.0", "tmp": "0.1.0", "debug": "^4.1.1", "eslint": "^5.16.0", "mkdirp": "^0.5.1", "rimraf": "^2.6.3", "codecov": "^3.5.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.4.4", "spawn-sync": "^2.0.0", "typescript": "^3.4.5", "@babel/core": "^7.4.5", "@babel/preset-env": "^7.4.5", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.17.3"}, "dist": {"shasum": "8c76acc9610103c2f5e9693412c70fab57861930", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.3.tgz", "fileCount": 7, "integrity": "sha512-HPatWVAd5BSzrBA+BiMv6vpZAzKVLl5oc35nyw8gDkJpi6aYBPIVMjKJFIJqOyvaoeCZMTv3Do/bPwNy2ta7Zw==", "signatures": [{"sig": "MEQCICRpzuwxk3kcyo1JIpW2ga9pThLA70gJegGKXqh9jY8OAiBhIT6o65w1ctYDVU7s8e2Y9Z6IOLFz6GariUNXuoCLIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU9D7CRA9TVsSAnZWagAAC4MP/At3wGaVk/aqi8IRTsZL\nzqE3OSaHZSYkGI0Xfrw+ajcT2IJuj2QQLmoN6a9YeutgDLyWlI9LWqmnUHqC\nfTF4kKioGZL3alYBMC9IiuXqh5jyvdajOc1x6DE2JtEQTWbmoxwNdHjmX4mf\nQzJTOlyvsOwsoTDzwSqWBIQAA451RU8wj4tHpk64ZTadExovmgo9c4WBd9/a\nbLnvizbT3O9UKWjynyHGJ1XmrzooHc4oUf5LCJmG8PvhR4I4K9Gg/AFXzzN3\nm6Eyyo0dQAm9CtDXkYrnqROEuELj3K2xpyygAtwAJI7JtZsxzSD5z/TplGpK\nKWCq209LrP/ORQ9l6+Bb43iy+Akr9tuPHiREaw4dSyhDgfO8Zi7YpOdUleil\ncWDRCO+OTmu0kjZWryc1SkAMBMz74Yo+9sX2DfVLi14LZG9ZTrmRnygmkkBf\nNdJw2XWa/d/4hJy+IHxS9ZaQBtKsV88nQDJcDT3RvOuYjs+xaplyqKNIOEDG\nIg3JiiafEzQgbVFSCifUrUiKDKDEjDEUKKeZcwbsoTwxXGcOJ7zClfv49y6e\n5teTKGOuFh8onc/KzLhoUbw8pH9Kf5XyuTyOzctlKdQJYCwxzh9AG/jQyvd+\nKXM24qIzANUfQd5R7WykiHBFYqP8UsQH7ncu0ry8SiAl5QAIekPbdOHl50aL\ncPlO\r\n=oWRa\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.4": {"name": "ignore", "version": "5.1.4", "devDependencies": {"tap": "^14.6.1", "tmp": "0.1.0", "debug": "^4.1.1", "eslint": "^6.1.0", "mkdirp": "^0.5.1", "rimraf": "^2.7.0", "codecov": "^3.5.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.5.5", "spawn-sync": "^2.0.0", "typescript": "^3.5.3", "@babel/core": "^7.5.5", "@babel/preset-env": "^7.5.5", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.18.2"}, "dist": {"shasum": "84b7b3dbe64552b6ef0eca99f6743dbec6d97adf", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.4.tgz", "fileCount": 7, "integrity": "sha512-MzbUSahkTW1u7JpKKjY7LCARd1fU5W2rLdxlM4kdkayuCwZImjkpluF9CM1aLewYJguPDqewLam18Y6AU69A8A==", "signatures": [{"sig": "MEQCIAT/QIiFWgSvFuNakzMYKT4K0dDASxr5SKRVYB07WtyoAiAhiCZ3Bx2xEyweTQmhD2TuuKXPSqnwN9ZXoHijTfSdpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdU9S4CRA9TVsSAnZWagAASEIP/1d0L4ZdefgYaJ2UmgXP\n2SZH9l8RcUQwphdJdSuyKCVikdYPbgCCoRfyuvS/s/y2ARSiuvMbJGiWZXF8\nE1VM5i1xlcX59Ex2KQEjzWLvQRVo0sizCD1cPgEk802m6f4Rxl/5ws0kQ5KB\n0CJQkOSnwt1MackMpZPvh7XUNJDJdL7JyegGRMtFCxqdQMSRaox/F/IA1dVD\nsYQOcrew5xgoMWC5FfC8qAu1MRocmAZwwL9cyirwqiv1StarEMGUSXQoupQL\nEtssEbIbt2HqqbXYEHbYnScItdwvQnhDheGTRM9E3NQwVKCXtjq69H64l7OZ\nkg8uIElRomZQp2LG6WkUjAksxjeTKIMFLRKQ+/QmOAJceHp+P4XxeyK3AINT\nypAwChdyPl8EZAQ7is7oU8vVa/bbK2+w845eLbdM7+wq58k+tliOfhjNazJU\nyvdINjzRAR/E8+iBvRZX/VmBgQQ274fuPBSRJG2ifTpLL5nr/ky/7cX43NPH\n1sjBuSpYZH0vxCsYB4KbSG0IpBKxmIvyP8SdrfYgmt149N6A69Ai0n3X1A4A\nlcIcWnXvC7NjotGUja5RdvOzF9hni+CE6NDF7wOX230MrDonoal0UHZaMaBi\nUM9fFKDydIUxZathVT/YayinCUrB2OyHC/UvhLWeQywB4Jjwy66irLLi8s2a\n57yj\r\n=h1TO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.5": {"name": "ignore", "version": "5.1.5", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "4e2f418f16d019e7fb97d0ea6ed1dcd67864d96b", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.5.tgz", "fileCount": 7, "integrity": "sha512-QD4ngLBjCwACR+Wq7ZPzjgCpOWOOBm+5+qwXN0rQfiht0kjTGmBcLXlJRxiyL9rpyq19Z8tuLyG5LLWEEfjXWw==", "signatures": [{"sig": "MEUCIQD5T4S7GZw0B/zSbMEB0Qd6y/jcJ8Mm7DlRiQYLTAO5JwIgXfwMZ+ArANTTHGqjXLrUyRvIWlGY1B9Ls42GYoDeUtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex909CRA9TVsSAnZWagAAnj8P/38S/Zl9J94PAdG+cktI\nggFr6W8avCogam3ic/rednSB5HG4gM99CmCL9Sq5SJ2PmyBU7FK+I3v5f1Tb\nUiipuQIvCgoqGmG7xN+6bdGCwV24KDOyvyUEb33JnKX2l9DEMwk9bO1FWYcD\nCuRg+7FCBA8WA3N3gZ2tpffu1k9i/vkZyzaeOVODYu8tsoBIIPn/TLgtASrK\nHOnlSP/ewXM8DSG8nd6nKWouvYdx8YSJRyOpDehz/71IPcdIkkRZdQinhtJZ\nIeIaFVSbl0Lns8NPHOk4K0IvWj8kgTkHYYoI3HlXa9/UD5hbsjXKOpn1tNL4\njI0mkx2Bho+KHuFZJk4/TBXzPXmH8jerYoD72qp1exjJQ/h7/EvwYR4k62HP\n7d+D+VHHtuQr7ewsuXOGNTVIcKha5vE0BaN42y+zJcldmgMJyGc38654kPBB\n44axl57WgkK9NjLRorgYYQnSIq5IUJQUX6/MYq5gbXTPmvSCSKyIHSp9rAWQ\nxsvuktVlp6feQY/DnRTcTRwiISZ90nFRb7C+HBYz1oEhGb+onbC7ljMHCfm3\n39xkw6rsbwd+9xd96pY44hCmRFvo3dOWmPUrJEF0R29Vj6ICU2ZY8S6eNIRk\ntRGVSrn+/zkL45x/umH2X+zXXQYTTOPGCqNamlHfD5bn9t+z0Hb6i3lOOmAS\n5fPM\r\n=pf5R\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.6": {"name": "ignore", "version": "5.1.6", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "643194ad4bf2712f37852e386b6998eff0db2106", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.6.tgz", "fileCount": 7, "integrity": "sha512-cgXgkypZBcCnOgSihyeqbo6gjIaIyDqPQB7Ra4vhE9m6kigdGoQDMHjviFhRZo3IMlRy6yElosoviMs5YxZXUA==", "signatures": [{"sig": "MEUCIQDzI1SpccWVQD7K7IT5a67DbPfezJE2cstJN+zp/EYUJQIgK5H9LkGM/6XfPZSG9D5texdte95AvAaPAKP3QKEYKdk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48765, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJex+x0CRA9TVsSAnZWagAAgXQP/18JfnC6gaQNjd2lNkH7\nmSM/wu93g1DsP1/VMNVfXSnUMlScGqRGUKTB3wuRTlNrKS715LbZAfWGMAsR\ne0+pLKX/v3FlDlZ/WFn7Z20m2+nAJDUCp9sDOQtofRc14Zozcmy9hzDlyTRj\n89NjecAMGQNPBxZ53afC87M6RjaC9Z53jpo/8pp0JNn9cpW0GNVQxNkRCo72\nZC9rgktxd5VhKsl6wsMWzbp37iSPFRfX1KI1ZQfvyErtSiwme9MBoOjUd+hm\nPF2BRDUcizUl44XPgktWr8bPCyQD3T94I9F7urt1k7FTYflXm48r0eAO2wB2\nqgnnBeNvw88N+eRvWJx+fu8gTeGdMt2gO3pjecmbrJrzgv1L7FRPQ+h+qyQF\nHhIcy7F8tS7AJG02H7oo0iCqzs8VVtK8/lb98tKDqt6fc1eGQOCZbTKLzSiM\ni4xYsajJ1nB29/7/sqhybUSM+gCcVVmYgtRHnZMvifQdcynL5wd5Sk/EioxL\nwzlIP/GaHpSrXDGaKBJFA6okBzgv8OtrjtHj1k0hMxv5m4Q9JpAwZW6FFxZ/\nyPuyha5eK2psz8BWcEHMo5WauD49rZrN5XQbZuvJ9ymaSre52EoKs04rB0WR\nkK+zW19Xu1RLNQeghKpAYtcqfprDsQmBvp6CkxjevKfDmky5ENJxNFk5NW1M\n6Yo0\r\n=q8Ix\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.7": {"name": "ignore", "version": "5.1.7", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "9288bb665bbcc7e155ad10ec4aefa6d27fcc603e", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.7.tgz", "fileCount": 7, "integrity": "sha512-INBK+PWLI4T9G9OgQukZu6kbDi9wVTebIEr9pC4EoXwpUPVyHaW+F+bZqB7F3kSTsTG4uJHNgPwXzJ9URoSsyw==", "signatures": [{"sig": "MEYCIQCCX/dTcuWq7JnDjfq97k9GK4Wt5vBCO8i5IIOKtpvDowIhANwHHZYcqxGK0RtEnPtieceDLB2t2+Xo8Ht0dN83YPnP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0dIQCRA9TVsSAnZWagAAgLQP/3qCNr7K9keH/2dsZzfj\nSlv6j/Lch/fTT0i5uWOCUnp4ukryDjhaDGuxPEOGCpLEExmnbxEspMAmcI1i\nBe8WlQ/17kWlSA6yOFx1Y//NCtvcZwBKSpidjXIkL/XC7EPjAlvvMrDrdCLw\nXVBlEf4qq2tr2S+zUS2nVG1O5WgR0004bC9GT3F6WVRu/SjpHiWT+oMmd41K\nZz1bkK//dmVv4WbbExCdF5D9hU/2ez/ALEqR5u9cB7tp7EMOo494VbSRKrOv\nXFT1Luq4Yo4yu6bVJFzEObLeTziOQsqQENqacvBRg1mqwtyv2pLWsrEQj2pi\nF+5s/5MUAGKyk8oxqQr7JtvzTPdexkYuuouZcbA9WozMKNlmoixaITXACVP8\nWNl6YTDMoV1Scu5GSYjnWmwTuBExIlWjluEhnSIywj8Y4MbMhgXjhyORfP5F\nPkFXlM9uC88cl+g5WwMkyoNc40IOqgYA1f1cLHiSHRM9YowKV1dVyxeDi+6V\nz2MVM3vqDU6ysCkJEA1JwZBXe7qi7b14KxFPMHrnxY6n9MsRUNARHvceO0Ig\nGl+Pnd3nyHAhw56gO6pfrfCQujCZIBEUPRMn/dRjqAjGDX0ZgoJlIzPjPSZ1\n1ynh299OVDBEilqUmljQ2oG1ZvuopkTl31S0Q7bGgWZelb/xZwEBRNbQor6i\nq4v4\r\n=77qk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.8": {"name": "ignore", "version": "5.1.8", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "f150a8b50a34289b33e22f5889abd4d8016f0e57", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.8.tgz", "fileCount": 7, "integrity": "sha512-BMpfD7PpiETpBl/A6S498BaIJ6Y/ABT93ETbby2fP00v4EbvPBXWEoaR1UBPKs3iR53pJY7EtZk5KACI57i1Uw==", "signatures": [{"sig": "MEUCIQDCZ5YlP4yVYG/kR6TmXecjVtRMwXMba1Btwc+RTs2GvQIgEKfNJnceJYffxb5+cI/pi+8Wa3HXjSmKVn9XeZPzt+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0dK2CRA9TVsSAnZWagAAP+UQAJrGwjSleLJHdcoLM5yi\ncnlSw+FQBX6NXXPSpOrJ1OjEzrYyNQBwXuV5DbZnvwdracA3bafKa3ifT8mg\nDc0ZKVMF3+eg1O0ksc+u2WHrR2LEDKIG10NNnAKFqsmm+66Ec5AqUbhf5Ycg\nT7O54xjV993HeVEga+8txZrcLXZ+FMSIEtwd8MPJAzQy9+T0cyAZR3dtPx0/\nuJc1eZDw7GCIAtL4gizzs4Xou9c9NOkVOuJwngDrIBWSqI/SuLrMwOmW4pBm\nlno9vKDjI1YLXWzQqShZe2QRWOxcV9pCJrYCSS8JyG1yptsaLyoAqInKxoPa\nXQMnTiE4KG2SoGfUhy6XYCsqiwH4icKgSaDkMDpAbDFWpsBz9rgVo5ZRu2XZ\n9k69D1q2M92+yhsoSuIkcbnSr3v6wUSDOhN7ocBbNu4FvSsVT3hi9TfWcHpt\nLvjxEPqCzijspN4DwcSwP7UCCZvkzQwGBv1lcPk5Xk0P2WqrIXZBZVF5deDZ\n98AazfW3PXQw1VWFlX/MCF30p8Odgc26r0PuvLs1rGREFUQDGTfUHcyKoO2p\nmc4w6MdpdEuULlV148RRaQwQWQPO9FqzWHmwijQULy+k7A1wak2igX/ud14T\nQIsIYvuQOlfLuYrlDViJLv/17L9dKjAuRhNpL6jEG0tYvyBrip64AusYlXUY\niA9O\r\n=4fPA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.1.9": {"name": "ignore", "version": "5.1.9", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "9ec1a5cbe8e1446ec60d4420060d43aa6e7382fb", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.1.9.tgz", "fileCount": 6, "integrity": "sha512-2zeMQpbKz5dhZ9IwL0gbxSW5w0NK/MSAMtNuhgIHEPmaU3vPdKPL0UdvUCXs5SS4JAwsBxysK5sFMW8ocFiVjQ==", "signatures": [{"sig": "MEUCIBgu+0uJ9I1InmCpD7pszJGFX7rKWUDuM73OQcenY/iBAiEAseLZ5HI31N784xuAa0L8/ApIvP48MYi8Ebi9zS/TOPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47597}, "engines": {"node": ">= 4"}}, "5.2.0": {"name": "ignore", "version": "5.2.0", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "6d3bac8fa7fe0d45d9f9be7bac2fc279577e345a", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz", "fileCount": 6, "integrity": "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==", "signatures": [{"sig": "MEUCIQDxcvccyfxpmLTcCYnWv6k7D+BkxzGLIH3WEJPSywWhSgIgKwXZ07xw29xqC15cFOHZtVdSeo45/EVyk5Hb4XVARlI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvqupCRA9TVsSAnZWagAAJ9AP/j859IffZXFncyHhHI3R\nhKZcgSzAagGrMAWucRLalshB1iWTGkWlSUQGcuBaVBoJrvlCSTSEuABi4FN9\nZKsIPxbVrMnKVg6F8n9o4fYsqrJnk8ms4pqUbIwkNJhsz6PSi9HDHOdcnsJQ\nZz5DCDDjdY5dcuXSP9r0tLzrRazWzgKYydXRCggkYkHeQWCkVq6FP+Rvo5te\nsKecptFqJnfj67AGdvOs/vXtEsrw90atyZ4GO/m0GrkPNUcoMibPDHZgva/4\n0e1PGW5uvhmfrxfjOtu21yy+mPmGtIlLcWNXCkYBV4BVlDDSQJohGXjOPD/s\n8CKXnBYZ0jvK3cbYTiX5ilYRHWzaAE0trxMzUNRMiEo4AI1F+cEIYJnLWKap\nEEfs8LhDiOclteIKrlFM9ZrMaJu/GrDytzk+bHWD7WPmZ92wXY9zjSPR5hZt\nW7imyqiXUevtLG4FHiKwrulb1CpIpYACCv/ITUNualIoMVMDK2Sr348TtJ97\nheZjdd4eQ4FsMBIl9nlT3rWFg6Z8EiVF09a5AB5dhZNvalV/yIzgSueK8p/8\n/2ZEb96wOczAwMYeO4fY099Sp/yu2CozYRs4fp1mSeWgCt5QeAKdcdMhD0BU\nTCdpIA0E9yvFx6034FmDpJazqAwz1aht3L3zXrCAwRQX1A+w0rsygRJp81Kd\nUsIu\r\n=O9r1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.2.1": {"name": "ignore", "version": "5.2.1", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "c2b1f76cb999ede1502f3a226a9310fdfe88d46c", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.2.1.tgz", "fileCount": 6, "integrity": "sha512-d2qQLzTJ9WxQftPAuEQpSPmKqzxePjzVbpAVv62AQ64NTL+wR4JkrVqR/LqFsFEUsHDAiId52mJteHDFuDkElA==", "signatures": [{"sig": "MEYCIQCu88Y+4lNKL+sn41adsZV5cDCazJn+0ApYTLdPYN9CnQIhALj5OEGds4/6CYPtzaw6dpwQkCemkuadI1ZJym6uWyri", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48871, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjg9umACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGTA/9EpSIQunIOEWUybnYhKpBkSxgXlG10/77IykmcqUlJrnHYiL7\r\nRZ6K8EWY7fIaj3REpfGH5E7au3Q+wFsL2C2fZ1Zv0pmgys14W4puc9v3jNnS\r\nFSAjUQxYzvBSUC8mH2szDwWKqLysGvsXd+WGqybdsgTIMu/7uBD3A31Qf8Wj\r\nWDgEkqAsWOvDGpgbvxaaHb9pwzID//d5RTrLvxZjCpVN42UNXg25BcIhOHfi\r\nZBRwXW0kp5kqVllN/H0RagGGGjj+BOYt+uItngDNNAzunQW1HWYUHKJByksr\r\n4JZuMHlmVAT7Mn7XRqsAe0IUBm8HbzNuYx8vn2yp9OKOwJ8ssiwqBeFG060A\r\nFkTsoAVEshC4PsviDBmAn0DIiDIQAo1KA//VhBjzQZBFC40Py/KBv3p+hFMx\r\nDkUBK3hmjfQ4C9h7ZiIkIjKkgjKTZ+g7VF3BD2aXAhc4Uob+7M2G2KPs5ENO\r\nwIAtlM9+O4y2BAFd9M8bNJ/TvJmDewAZ00rMrCqsKc8Oy0eeD4mAV30pyohM\r\nAi8j2ys/k+VG1i+lN+DEo59hQFjF7Px1rukN4GWMWdTWdhtdaRhOOdEO8a2H\r\nhXjRv3xOuevnDBdUDG/FoRWh1iPfjPNj+wpRAdPk02iWG35UX7B5dYhp3s54\r\nSxZ8d3/GLilQl3MHQIFb1WP22bgwV74HWto=\r\n=P9tY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.2.2": {"name": "ignore", "version": "5.2.2", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "7e5f30224584b67aeeefe383a24a61dce4cb370d", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.2.2.tgz", "fileCount": 6, "integrity": "sha512-m1MJSy4Z2NAcyhoYpxQeBsc1ZdNQwYjN0wGbLBlnVArdJ90Gtr8IhNSfZZcCoR0fM/0E0BJ0mf1KnLNDOCJP4w==", "signatures": [{"sig": "MEQCIG+M7RGQqAOODGJpwy6uBAv4434xGzEf9sfe7LJlLIo8AiBlenh24A+5iJGKDOsnNcGEcb8oRrIzms8O7we5mkoyxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoBRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJLg/+MLpHjg26vRnsNYaL/n+lILt4A4znvvD/J406356ZcWYc2MPK\r\nPmBj8u9MVGKZ8W05F7cqHft4RQt5EAsjV2r34s1ZWj/67/ZxZk0ysPjsKY5p\r\nnLEqDAFHvp7YT0iFBvvks8L15rPeWYUGjvSbylsuQjlIBJGJaQC4Kza72fYn\r\n57MYifjxP/v9412djgrwMY6Ohw9MiIJAGeyqt9iXX2C6TvTPX0th//9QE8lL\r\nu2nFxMiM077t5IqaqT6RK1ilD9xyWO4GV/RTnCGXKsd/yfDSd3iBLdn2NDtY\r\niQurIQrMduNCU5Vbnzw/3yhDbakw/jcJQ54eKj0HAF4tllwWxojsGdODdcO+\r\ndBDoPHEWMfceEYyVu74MvuHdj5iT3HLBoQCm6uUoqdzJKMfVqeFcdVSSviIL\r\nRkQTtyHxrz6LE+b2Jsg6L017v2DLjo7DC9qHfdpxMOZ42oYNcTHf7aALrV5G\r\n9Bw85zERC4tUzRhBwNKzEAT5eAMnxbNLMw6Xmdkiqu3d7V/i5sar4Fepvada\r\nWYQsgldj9ApR2CNn/RBiIcEQ3bFvDF2kNPk9+/CLpmS7SrhHlANMXgcVcE24\r\n1eqbgKBPf2K7CQZmCqDL5Lp9rA9C/jSy/SzWlVy8ZIWSeCcu92yzYpGOzw8/\r\nDWzc4NOLQIuQYdu3Ea7igeimiiVCYzbneqI=\r\n=/qhH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.2.3": {"name": "ignore", "version": "5.2.3", "devDependencies": {"tap": "^14.10.7", "tmp": "0.2.1", "debug": "^4.1.1", "eslint": "^7.0.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.7.0", "pre-suf": "^1.1.1", "@babel/cli": "^7.8.4", "spawn-sync": "^2.0.0", "typescript": "^3.9.3", "@babel/core": "^7.9.6", "@babel/preset-env": "^7.9.6", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.20.2"}, "dist": {"shasum": "b0910aec0816d7c9f8c83ba0912a7ac16e125154", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.2.3.tgz", "fileCount": 6, "integrity": "sha512-6BCmRZymdnasx6eA82MnzchMPSxA7ZwIYDLnUER0T9Xhf9XrsceU05+7nt9KPC1yjG3fDA1yk37yPlld3YX7oA==", "signatures": [{"sig": "MEQCIB5jahaLdOrmLjZH8t7Ia1uZYixChhvTi/5Gfk8UlvJSAiAhyBX50H2JIiWCG9WWT75ChaVE0gXajLQCRB6Gw8A3Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoHupACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmretQ//YXnn5HEKHFqGaitIijF2aAlCsL6koVuEjgyZMVJhNBw1V48K\r\nGc9LKpkat8rf3aQVqryOKgkz37BGxcfkNAIf3T2+UNNIsEKD6CCYzMyPsh0p\r\nvR0Ll6lQW256Pv4mU35+Vp88Ir2aZuEnlMQbDXw/PCrewSd2O01Z8ZA7AdX/\r\neMCcDOfWKcXMVs9RtWsj3eoA1Ri00xw5pY27TRtGK3wJlgDix/jTFNsG3i1i\r\npYQfm0Umigywyg6/9xIbsfVDXtWN05zd4ScwABABi+jiBK7r7CPMXpEuiqZ2\r\nrE0/8eiDwcy/WUNgP/lE33o+435Tw0HH5GBDA0CX0fbEHUzQ5GTeeVYuKprT\r\nFNSW43oD2hvqDOVpq2NUJ5/GIl7crcDzWYMlr/lEYdzksSccqLYNDId3UZC5\r\nJr25blbe1RLhsKC6q8c8WBNW2ffFbkwEAVJ+jTa+XYGonWo7VOf3PbySWkTP\r\nwW1DdxEX1ROkIHL4/4dXTxFou9EAeb1HrUrpjt3KPom9Gdl7aUIUqwJ9xdtI\r\nzMt5374MMnCnOk4GmbfjBfU0y4/LmrE6d2vRa10QxVbPtE54WShvEBKB6Npo\r\nJDW40jUqgZj9oxBftdXBSnFP0GQy21VKkvENRk97FmDv414okpngt98XYxVa\r\neEGgmUWb7H6k4LO2l8g4DIhPDFjfxufFgKQ=\r\n=8hl6\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.2.4": {"name": "ignore", "version": "5.2.4", "devDependencies": {"tap": "^16.3.2", "tmp": "0.2.1", "debug": "^4.3.4", "eslint": "^8.30.0", "mkdirp": "^1.0.4", "rimraf": "^3.0.2", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.19.3", "spawn-sync": "^2.0.0", "typescript": "^4.9.4", "@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.26.0"}, "dist": {"shasum": "a291c0c6178ff1b960befe47fcdec301674a6324", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz", "fileCount": 6, "integrity": "sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==", "signatures": [{"sig": "MEUCIQC9eXqD/21MNnzkMZE+PNmblT30zcRDlVuwXcn94/4tcwIgSLztdyKnkD/36z2Ya5zTjvHSPn+eT249uueyL8PjQ7M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjoIrnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTIhAAhstLEnzvu6SBdWIiA/ft4Onxe/A2SzLtqyrfaAqbiZVQjR8N\r\nIt3dO9elHJvri4nmYxq+rSEF4uWQphBvJu9N3Ey4S9ZgGXYmZHUAY6rEKeNI\r\nTqhVbLxuvPK7jl6MjXWJ7KE42yT7fXM4hGrxHBOhNJUu1HpFMqHphBGUPzcm\r\nq1vzFpLgIZImLIA+lrPDb3GrMzoIMvYp7iK8cqlVdRMU4Bx90rkYVJAg1BKb\r\ndX7tgq7G+6iykuchmkHPP6wkVX4SbW5Oy/aufBzN/5NhVuzDKCuS/bqhcucW\r\noK+1tN+AgfePuiFIzXiv1hnfzXI2mHxhdPADov+GTZrJ8bRJrvVeQM+iD9+d\r\nFjI2E5bnrVSVDhPqo9as/X6U8GwYUSZWj9fCeitys7WPAZVvwPfkWqGq0HlL\r\nQxVZd/ocQOOYDxOMBjFXZ6/wFpXJcXQso4P6QZ76z0szHf6Ql2y3iKpbsFbO\r\n4XrESGQsbpONznLkq6B+HZ8DVCMekaQe6mdmwEdJt8ZT4dNVoiArZN/k1CAg\r\nLwjK//zsMygwd6NNxB4NjY3l7kPaSy1+trkaQsojSK5lekwYL+qSZzWb0941\r\nkx/hahHP0IISLj1MYvgPn58x+rT7fWbTFuyBYpYVUKilu3EYr2Yr9hTZgr46\r\nb4IGrYmte5OPL41qWnD6GEwnAawsAbNB+yk=\r\n=qaEQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 4"}}, "5.3.0": {"name": "ignore", "version": "5.3.0", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.1", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^5.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.1.6", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "67418ae40d34d6999c95ff56016759c718c82f78", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.3.0.tgz", "fileCount": 6, "integrity": "sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg==", "signatures": [{"sig": "MEUCIQDPg/6Vpns+12WBOmOJZBOnr8rrsxZEf1Cg2DYxjzoY5gIgR99gB2KW1v/AVTaEtidfBWPqli5O/QZ7wpkFxi0MzJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51230}, "engines": {"node": ">= 4"}}, "5.3.1": {"name": "ignore", "version": "5.3.1", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.1", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^5.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.1.6", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "5073e554cd42c5b33b394375f538b8593e34d4ef", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.3.1.tgz", "fileCount": 6, "integrity": "sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==", "signatures": [{"sig": "MEUCICNWFIDxMP736k7iT38Rs9oysecolj9dKu6uyrWovYnrAiEA96gTJoFchGJL/XYauufH0rB5lKGS+e1ivt2Uwya5/rs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51455}, "engines": {"node": ">= 4"}}, "5.3.2": {"name": "ignore", "version": "5.3.2", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.1.6", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "3cd40e729f3643fd87cb04e50bf0eb722bc596f5", "tarball": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "fileCount": 6, "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "signatures": [{"sig": "MEQCIHPELocnAYOzbeD3jL5ncfyVJlmuYK8abQDwvVcwzPplAiAwInO5nUYUQ9IEYbQ0OGHigYYpYXL2ECaTsl/mzGMpuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53630}, "engines": {"node": ">= 4"}}, "6.0.0": {"name": "ignore", "version": "6.0.0", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "2ebe982d9acc114fd5ca60598911cfb5d25e2d6f", "tarball": "https://registry.npmjs.org/ignore/-/ignore-6.0.0.tgz", "fileCount": 7, "integrity": "sha512-mLmIlWsE32X6sO4Cd48j/uLEhg9Cm8lH4i6xJEsqaDKaBWyZdY4YVEGOEQONuPR3At+42Bn+30OieW6eM+86iA==", "signatures": [{"sig": "MEUCIQCiq8c6U9jIpmoHsx0A8zwPSj1m4J2n5YOK7J1vBGP7swIgRzJP7K55kFk0wL+tDpoyASr3Wd9nHh1lGhFpb5Bnsuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70411}, "engines": {"node": ">= 4"}, "deprecated": "this package has been deprecated due to issues"}, "6.0.1": {"name": "ignore", "version": "6.0.1", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "18cac00bcbaa3d892bc9ba146b2ee5e488f87f61", "tarball": "https://registry.npmjs.org/ignore/-/ignore-6.0.1.tgz", "fileCount": 7, "integrity": "sha512-9hCx6FGveEYzwsldacntlq0RdPsTjOAALVL4nqi1O8JU6OIzzchHELMNE9f+6ZMtuHG1vd+owvczaMhu6EM2Xw==", "signatures": [{"sig": "MEQCIDtVBFKrpeY4Y8kYGyo3o3ld2SseyQz9u0riyyQ5IA7IAiAyMgo0IEwjvfzkV8/9XdSPEmLIX20cEuKhunZ1uQpY5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 70442}, "engines": {"node": ">= 4"}, "deprecated": "this package has been deprecated due to issues"}, "6.0.2": {"name": "ignore", "version": "6.0.2", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.2", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.1.6", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "77cccb72a55796af1b6d2f9eb14fa326d24f4283", "tarball": "https://registry.npmjs.org/ignore/-/ignore-6.0.2.tgz", "fileCount": 6, "integrity": "sha512-InwqeHHN2XpumIkMvpl/DCJVrAHgCsG5+cn1XlnLWGwtZBm8QJfSusItfrwx81CTp5agNZqpKU2J/ccC5nGT4A==", "signatures": [{"sig": "MEUCIQCUhd4zGOf3Qm/Ba7pIjQqRkTTRjT2CQfKTlnOGg9+kpQIgeQ0ee+wt2V69AhYRc2zZfrOv4azPyhU+zkrN04wlQS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53630}, "engines": {"node": ">= 4"}}, "7.0.0": {"name": "ignore", "version": "7.0.0", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.3", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "52da780b009bd0845d1f9dd4d8ae6a7569ae73c4", "tarball": "https://registry.npmjs.org/ignore/-/ignore-7.0.0.tgz", "fileCount": 7, "integrity": "sha512-lcX8PNQygAa22u/0BysEY8VhaFRzlOkvdlKczDPnJvrkJD1EuqzEky5VYYKM2iySIuaVIDv9N190DfSreSLw2A==", "signatures": [{"sig": "MEUCIB3rLrwTeI75RfssTIbnula1AlYOBIwh19kMCZ3IcG/lAiEAvYhofscdcMMo63cpcUJt8ql4AyXUEG+fkEFqKVuiK6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81505}, "engines": {"node": ">= 4"}}, "7.0.1": {"name": "ignore", "version": "7.0.1", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.3", "pre-suf": "^1.1.1", "ts-node": "^10.9.2", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "@typescript-eslint/eslint-plugin": "^8.19.1"}, "dist": {"shasum": "e0aca8bd28ed81f45bfbc042c57f24eb682aa2b9", "tarball": "https://registry.npmjs.org/ignore/-/ignore-7.0.1.tgz", "fileCount": 6, "integrity": "sha512-D1gVletsbVOoiXF963rgZnfobGAbq7Lb+dz3fcBmlOmZg6hHkpbycLqL8PLNB8f4GVv6dOVYwhPL/r7hwiH0Fw==", "signatures": [{"sig": "MEUCIQDlBitKLhfuqkazrrSCsbVu3gmZ2LjOehOlqIOIRSFHbAIgaHCz+Fiv5uBDSzZoWxhiHQsB5Q2hA1ne9qxMc/x7T34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62461}, "engines": {"node": ">= 4"}}, "7.0.2": {"name": "ignore", "version": "7.0.2", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.3", "pre-suf": "^1.1.1", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0"}, "dist": {"shasum": "f3159fb41f147eab6bbc1242a5469ba5d9af6f65", "tarball": "https://registry.npmjs.org/ignore/-/ignore-7.0.2.tgz", "fileCount": 7, "integrity": "sha512-Wx5VKTZatJNNa26J1dMfJF1bZu4Lw31EHwhFRcSjTvro8Mqsrd3rJanyW48W43Eyd+gpaiDNkveYd62DvXaZeQ==", "signatures": [{"sig": "MEUCIQDWeENFurlTZB7N/JLsWfAXhVGtJgw4QG2rcLb7XLdVVgIgT641E2xYIZZ0lAYIX8rAjemxVRn/W2gidKjhloR15n4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81505}, "engines": {"node": ">= 4"}}, "7.0.3": {"name": "ignore", "version": "7.0.3", "devDependencies": {"tap": "^16.3.9", "tmp": "0.2.3", "debug": "^4.3.4", "eslint": "^8.46.0", "mkdirp": "^3.0.1", "rimraf": "^6.0.1", "codecov": "^3.8.3", "pre-suf": "^1.1.1", "ts-node": "^10.9.2", "@babel/cli": "^7.22.9", "spawn-sync": "^2.0.0", "typescript": "^5.6.2", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "@typescript-eslint/eslint-plugin": "^8.19.1"}, "dist": {"shasum": "397ef9315dfe0595671eefe8b633fec6943ab733", "tarball": "https://registry.npmjs.org/ignore/-/ignore-7.0.3.tgz", "fileCount": 6, "integrity": "sha512-bAH5jbK/F3T3Jls4I0SO1hmPR0dKU0a7+SY6n1yzRtG54FLO8d6w/nxLFX2Nb7dBu6cCWXPaAME6cYqFUMmuCA==", "signatures": [{"sig": "MEUCIQCf+xD/td3ut8+nqNebo96773yMc3NKNz0lJMNOvbu28wIgSeFamXXPPFbewYRHDOvMsfAniVHvCFLQmBAUBdHfqRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63148}, "engines": {"node": ">= 4"}}, "7.0.4": {"name": "ignore", "version": "7.0.4", "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@typescript-eslint/eslint-plugin": "^8.19.1", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "dist": {"integrity": "sha512-gJzzk+PQNznz8ysRrC0aOkBNVRBDtE1n53IqyqEf3PXrYwomFs5q4pGMizBMJF+ykh03insJ27hB8gSrD2Hn8A==", "shasum": "a12c70d0f2607c5bf508fb65a40c75f037d7a078", "tarball": "https://registry.npmjs.org/ignore/-/ignore-7.0.4.tgz", "fileCount": 6, "unpackedSize": 63139, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCFSvmkDSxak9lDs6IuOnsp/8onheQTqehW19T3aY4GogIhAIyRmNyqUYhvDAjRdQ+BOROEXWkuDkV/K3hn3U2Y81Xd"}]}, "engines": {"node": ">= 4"}}}, "modified": "2025-04-25T02:29:12.403Z", "cachedAt": 1747660589087}