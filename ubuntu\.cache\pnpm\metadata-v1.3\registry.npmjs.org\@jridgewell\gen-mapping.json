{"name": "@jridgewell/gen-mapping", "dist-tags": {"latest": "0.3.8", "beta": "0.3.6-beta.4"}, "versions": {"0.1.0": {"name": "@jridgewell/gen-mapping", "version": "0.1.0", "dependencies": {"@jridgewell/set-array": "1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.0", "mocha": "9.2.0", "eslint": "8.7.0", "rollup": "2.66.0", "prettier": "2.5.1", "typescript": "4.5.5", "@types/node": "17.0.28", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.3.0", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/parser": "5.10.0", "@typescript-eslint/eslint-plugin": "5.10.0"}, "dist": {"shasum": "b30fd66b5426bb7f44bb6043ab127b20ee404063", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-YH+BnkvuCiPR+MUOY6JIArdTIGrRtsxnLaIxPRy4CpGJ/V6OO6Gq/1J+FJEc4j5e5h6Bcy3/K7prlMrm93BJoA==", "signatures": [{"sig": "MEUCIE8LCis1wibHAp8m7ylvT4oV6ZoKAyXKzyaEDJoFXSPHAiEA7PoJ1C/LkFHU7AQmR0qEfBcD1Djk5GEE78yuapFlT6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaJXYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXKw/+Jamfdm8Me/onrhC89agEYNrPe9sQxdU0K+BZxKeqk5KgprY2\r\nsM7ZyVQVGyKJAaFw1OXNmWAX6D7ogfp9JZltFze4P/r1yeA8erYwYEozFmcX\r\nUzWk2ta/WihuMJRMukxc+3z23DLg+DTV1okHKdTgazyWLSbCHtL++AgTZX2/\r\n12dKNa320XczYrgGxRhAOJDjJ93U4tiSXXhPCXdB+7edrU17drwSA7VLRDBJ\r\n679CIFDZ6MY9TUSrHCDwytcR13Lvsnpj9S0qDC3uv0J4CObhg116mQxFxTgL\r\nIS0xb/8dTlpAZtYRwh/f3RL2vgLRuSx4KbW7RVYfg80Bykhbcn2nLB8vfYsN\r\nyH9CDZig06TzuHb2TSqnoJDDGXEDhhtBFpcWELwGc5Z17jSpzpvO81PjUMzA\r\nN0kdni7iYTd++QNYJZGgWunDqyRctOjYIPde9mRvqXCThdc8OKJbgLyo5EC0\r\nXXD/daseYd6ujP/RXCOhek7dETS6aDZ654MafrrvOEnnyLYwvfMdh8qOc8RA\r\nbRsGQ8wtEy/OB3B3qtzigxiE0nfXH7kP7v6L9hZb+nte+2GKwZ4wKxiMhPCG\r\no8fdIpMjb9UUcaPrfFPnmQMLqcw6zpjSw+4AMWOrmROVW4VEKwTnQFXWS3i+\r\n3kf1MHH9l14iB5QsZEoDMq+5m3tYLYFVN6Y=\r\n=98+U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.1.1": {"name": "@jridgewell/gen-mapping", "version": "0.1.1", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "e5d2e450306a9491e3bd77e323e38d7aff315996", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "fileCount": 10, "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "signatures": [{"sig": "MEUCIQCxFajn4WxQBcvGauk/zCQIVpao7hMCQs6Dk9/vwvObFQIgc05CdcnkJt2iwL/UolCMJ8xxe4D7Eb52vfZ2gm8Ev5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 52350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaVScACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmou9g//Qlejq4PF0fUpsSoaBLyNnkUwXHtwsj9xJ1M7NU2MFc6S0cKb\r\nvCNoFMxBzongHTEyWwJ9/zd/cirZMXVS5k2ecZPM6r3DgR7bitsBTjW89xO+\r\nDiwsgJ2yMlufZIbd1pXv8ZMsyfV/SxJPSPT74udOeU7qu1s8MI46vRylnUIi\r\nMVhl8WFw8RxD/ojjrgQfECLb6TIEtK1LpLj1z6trsCCPla3fEiSe7kHcp81X\r\n3DYgAHnDVKpSxNAjbgG4KUYKKo1//+2WuvyRf+E3o9FSoEpS64FBjErX80OY\r\nGzqIDBlJzFVP8AZFT5Y2zdballRfSBYBjyYslIDnRFFI4FD5uAS+XuSYme/E\r\nnHHHpdcuiyj8AotD003ASoZ+7k0eXasJ22idV/gsovg288XRTWCctQf7LOWu\r\nEVa7bw5VpV044XM0vVg43WZb6543zaHJxiCP/Eu9rrOY9GJJk85RQJLhWBIf\r\nECoTTCzcomv4i0ghgkHgdmHRzJbH5DOSbYc1Y86rc+A4FfKIgo9nl4h70pSQ\r\nNenYKooVdtG51B1RoaFAmwCrPvxz4FLGgYVJBi6vEdXdyLixsekvwL18OHYt\r\nWcYkjjoadwJrtBfGovi5v5UAijfFSZX/iRsf62ApoJcRvqsCrZTy05yQEk2S\r\nEPTIwNmDDDfNXuWsF4NqLFTtPOgJUHXaMpk=\r\n=d+en\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.2.0": {"name": "@jridgewell/gen-mapping", "version": "0.2.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "8b4d9da3c3a75e06187d1e926f8f8b7a4fab12d0", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-wcKrTKpG3DIUivSPbzsG/YauY77Ljfhe0OnMRvzveOTZ5mJEwT7h2NhquJ9vEIkxMeeyqHz9YnRYXGPbMeriUw==", "signatures": [{"sig": "MEQCIFGCXYHUQOBERh7/JHxgsp1f7lW86j9eHtYp8owMGs9qAiBH/E2NJuB99Bqy/7Fywu7kINx4X8XYRjMk9iQ9fNeGoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaZFDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmonpw//ZFCKPmI8yNmftl64Rl7w1AQHwW6EgIqaGi7uWtx7mD6ldV5U\r\nNhPFInURW5e4Klq1t6XKxSKA+KeAhXUdJi0WZYWqHsw6R6VKIQvXn14tB71z\r\ntR1gENaJ4Vzhw3yU1/pIlDhB++/n252VY5zwNW/1ZnLZ9MiRbfmrm5OErn0K\r\nixqKAGh8a4LrrYcBPRM/ZxHKliEeKBrdaX94uLGAaOMcQWA7Vi3xnKMZou4B\r\nNZIYuVD9wO0oqU33/kL5FYT8n56Uu3+TgvR5IrpA2R+FNtn0o+3Bp+PypZt/\r\nbJQg85eqUQ7jh8p3mAQZ23JWKjs0pyG3ZAHN/svEG26BjE0Qf845axrQgHf3\r\nktdmEHrM/oTgVEa02RND3cf8GQY/s7pJ6ir+c0go63j0m2zL8SQCCKkqe04O\r\nozW0/6fSwPEFgiqquZoXVStTNCWr+y0+6A/75JN7oPNntqdoGdHMhyTsYQ6M\r\ntQpq8uH3WM1uzOuUIubgnEK7AMd1FUUCMXZJl1irynaJ20ufFIOkSHBIjDE7\r\nfj1EQ3fuyVPs89e4xHYeo0g7qrW8c6D/U43RwR7vSKHsMjRmL+JMtj75qkgL\r\n7l2emGGwUfxZSnLwqHzclPr3z7jCw4F48xUh42v+LXqgZAlVLyi0E0NcQ8h6\r\nir7r4WiTuAD5vzpxVtku5fxcnMeUkrDwyyU=\r\n=Vd+0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.3.0": {"name": "@jridgewell/gen-mapping", "version": "0.3.0", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "62d7a390ad9f98be6af7daddb327174a3f295acc", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.0.tgz", "fileCount": 10, "integrity": "sha512-bFDbLEZ84DtNwXILbufXeQMz3tcGSfy+ROgvnB9jUm+t48G4Po+1UhhyFh6GIsFOE2R31Ab0ddeMv+Z9gcYC4g==", "signatures": [{"sig": "MEUCICyFmtOMI2HQhw0pfl8jWdgVIrC/AjsbSikAE96FYvMqAiEAtBu18L046jyqskwPgZpaIdpk4oI8NsCBj7C2piRVwLA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibLpzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcXw/6A7qXzgeim9f247UMhLdlpznjDfMOUN8OdhrFEhO67T6nigPw\r\nMRLpfKuAutBCFKpqyf5KrI5L00LPxReyz/0Sg3l780SBo2aK8oQA4euDUjyS\r\nRzU0/yJxAHf2YJ1BaUNZYcAX1dcHUsIpKjMfjQg/d7ReWkYN2B7i7hKj8niu\r\nFjaUhwRCSgObbLoz8TqVhg2//faTZnh7/5UPA4PVw4IO6z4danTSaE9iWvQq\r\npCf5JMs50J2x55hCZyRxQ1+rrCjAY+7gGtB0myquBBrrzQWoaOiphixVyvfi\r\n+MhampLnc3ZAo4GZ3rwGC/lpYeKwzv4d1ek1Kuj2vQVd/V74BduiWLZ9P4hQ\r\nFnB/cKeAftWaPDhKCqQ/FUhda9u1sKc1wd0aFLkEI24ROyaQkARADbkjsvQR\r\nzZsipRPZWICpCLz+E9Q0DVa3x3PZtF7mcRzeqX1f2sKCikr6wuMcApmEuwuG\r\nHg6xKsmBcxn02rcDy44+QzT9wiTrX85FE8CgLQaJ3O9r7RHROH4iRhKqpOLV\r\ntuG71UniHw3NiRe5BIeZzFBecOawGjRKWxQFdIjX5Yba8N+GpQQ1A3EqDv6e\r\nDt0Y0h1OEPO+ejLeGbdmSohlcUSKPY1zHnrgMDCAfmJ6BMeC2CcfnLqHUfDL\r\nxWel3VVzYVyDkxs2pxCAaa4avRor6fusEyU=\r\n=AiyA\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.3.1": {"name": "@jridgewell/gen-mapping", "version": "0.3.1", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "cf92a983c83466b8c0ce9124fadeaf09f7c66ea9", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.1.tgz", "fileCount": 10, "integrity": "sha512-GcHwniMlA2z+WFPWuY8lp3fsza0I8xPFMWL5+n8LYyP6PSvPrXf4+n8stDHZY2DM0zy9sVkRDy1jDI4XGzYVqg==", "signatures": [{"sig": "MEYCIQDF63oilzcbNgK+ZOZtxvTAm/rw7dfZg3ZGZ9NEMWLSvgIhAJGtj13MHxy6fVUEVkDDljJX0b/CVtCcSY6wTiN2m3S2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79144, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEjDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUZg//Uet8QJcEEcg9oK0h+wtwJ1swUD8BY415IMZkyc7gy/nvm3Py\r\nAGYu0+0AO5yoRd3xETitt3OWea+sKkiEKUw7OGYzBEq/09xM5p6Z08vkrz9b\r\nx/h9OL73hIwWUVSeq7a4rmVoXXfHZEeEzOOjnk9L/pCDvjSzh+a+d8jwdAnv\r\nj8WA64bL2urh8OHMaGVe6tZa7wDiCSiA1OOjgMAVNkTHEY3MJhMkgO8lP29l\r\nx0OPsNpdRRpZ+fMZ49fSFGQT0GcwAilEs0Xcywb+m7Kag+niA3n2ZOGkqezH\r\n3cdZ1AIoFFF8S2n3JK9iGfRKMnCgolxsnoCKV5+UPhizcKgRv+q0MPb/cBIF\r\ntf6AWBmyEaA4hbEWLpJ6q2EdeBp5t3t0HVxjMcxIQ1oXWe/OuFxwneglGTGK\r\nk1lEDbefPqCmLyOIam0h4cRKDf4WxBPAe6DjrJYOa2A3Dk9VDod6F1iSlc0w\r\nv3Ioe58EzQ0SPRlHkhtC7KnqJW6oYYJnWUjDM/DuEVWpqtp6uhIkdVREwbo7\r\neTVp7G0VS0bFHgMar8dIv6ABI46yn5325JKUPA0Tz4AHdPFiZWPSsCFCBMyH\r\ngqRAid7GhlF5Qc78RW4bECgfeRaJdeyOXeskNUT1dKtPtJOvEE84nGp2smoR\r\nJ9wYNFQXLgERzxWfamEKGNnjlsO4TMq6e/I=\r\n=crJb\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.3.2": {"name": "@jridgewell/gen-mapping", "version": "0.3.2", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "c1aedc61e853f2bb9f5dfe6d4442d3b565b253b9", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "fileCount": 13, "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "signatures": [{"sig": "MEYCIQCkHcj7Kz+CGFdhrZb78rbL2v+aoKA/terBPdEPvgBtSwIhAJArXPn+VaKXaw8QzGOWUgn27lVTFamUoNBM6q3oKlxq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuIAhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoauA//WHClHK3DAb90491O82xa/s+PeNjCvEYeOtBgJeWajZ7qC66U\r\n1ao5+lHKNWSgoH0sSRTUa8JdMREf/yLsoa+p+MWpYY7tiBjozMCADdRF6DY0\r\nLDEm2CdMBSMHGtj2AkWKuBO3c4AhXOXpShJD8qy5AgoplOSQFXjWJgAVj+l2\r\nJcXN7ATOOaxsBT2s4oDojf8oIfIBHQNnvjUsBeMvuXr+31yukVLL3ZNqpoyw\r\nzGZTkgWJTTDgUajvL1CpCWO1zexH4D13vr8ioytcg2hcgZBJWanlQ4vurAQn\r\nHC5bJo2gaibKnm/MR6k2sChYZBFT/P9k9AB20jEEbJ8aLFBNMuE9IMAf5tr0\r\nfery99/tnoxgylR3xX27f9EzZVMTWYY6ZLSydhHHCAYRPaNwIyPWM0C6HLi/\r\ntxwv4N2xldRb1ppjcCbJA5lMgUDCLLeohINHYbx6lSO3ynaIrwdQkuP0lhI5\r\nRfUwQXSto3qTUTwGSa5c24Z3xBcxDKgZ5agGdYkKVi7Ee0osBY8QIZnZMje0\r\nEAX+lc1UjzFyf1pNZGplPPi3QEjXcoThTuoUBHbRN417tC/7JeeR3M4uXHuM\r\nc5A2zf3/VWquo/VT5NY8Lgm/L7SlQNlWe12G8cww3BmeDNMuRIBwkEdMwwXa\r\nA7nKAoF4kL5nhmaeFJwaTJJVPlo6b1lDBNg=\r\n=dVFM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.3.3": {"name": "@jridgewell/gen-mapping", "version": "0.3.3", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "7e02e6eb5df901aaedb08514203b096614024098", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "fileCount": 10, "integrity": "sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==", "signatures": [{"sig": "MEUCID/RBvg06WdLrqJIqxHUYCFRnV2Pe6ib7iQ7LhM5kB2+AiEAkJxeaNM1iEgu7b2sSTDaFHGMffbmy3wbYBxrq4i1dMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79189, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4MnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjcg//aIoa0GhUePPWVXJf9kaSyG6PwlH9pFki00HtHzQXAJrKA5Rl\r\nLt9xuCN9JqcvhdyseSiFeKeRcGU77EN2XXbSYRQ6qfstBCMr4Ol493vyRjUp\r\njhgVJvIXUzSm/0xuraXTed2zqVnQMnVfv0DabFmoR07zMlObfnwqrkw0KlfS\r\n/M6FHpYoCr5x0qZiAFRm6tdiu59BaAcMUrmEEKWtlQWcW1l7C0Crdd2NGsPN\r\nhK56yc7q3oHhlz5as+3KC6B5ouWMWUmKQt6OKj+KkH2An8OrD3K2lsbKvd50\r\n8UG1nHGcZ8p1lB+iHGk7hOLCOADkjPjf3GI1TagdvcL9izlrWnTGiaG9rhZY\r\nrYwqSy4qPRW274YV1+Mkh4EYUTpBLs6ZPtllJh3LQcF2f3/EfaYxUrLBu/91\r\nV6Bpmp3nOAjFkomfTSY4wZ4czpzqMmkZGbjUR8KWuA2BYxr5efODYzTihTUW\r\n2bTbzP61A4JczJ3zx7uhC+3ue3at+S6RlsHb8Yg/Jnom2pD6aXcLUOy3V3v2\r\nwDtGD5H2HBbUILGhgF8zdDNSMj0wkBLYfjSeJQtKD+T9QOXGrxh0dWJt3PO1\r\n6HSSun5A0c0eLP4K/eoAhMj26yi6bZbWqGsm7bd+F2yPVKDeZS4RSYGO/Xf4\r\nolsDo4qQQM6N0HkFvLSOUZ7rCyJCg1C2j8k=\r\n=bKff\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "0.3.4": {"name": "@jridgewell/gen-mapping", "version": "0.3.4", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/trace-mapping": "^0.3.9", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "9b18145d26cf33d08576cf4c7665b28554480ed7", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.4.tgz", "fileCount": 10, "integrity": "sha512-Oud2QPM5dHviZNn4y/WhhYKSXksv+1xLEIsNrAbGcFzUN3ubqWRFT5gwPchNc5NuzILOU4tPBDTZ4VwhL8Y7cw==", "signatures": [{"sig": "MEYCIQC1RousGmGX2TqpU4Jn101c+LAQ+4yMvoq9o/fDn8L+eAIhAMe6URtOMuG7cbUb1dD/XA3PalwHcNoT5ir9U//MUx07", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77305}, "engines": {"node": ">=6.0.0"}}, "0.3.5": {"name": "@jridgewell/gen-mapping", "version": "0.3.5", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "^1.4.10"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "dcce6aff74bdf6dad1a95802b69b04a2fcb1fb36", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz", "fileCount": 10, "integrity": "sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==", "signatures": [{"sig": "MEYCIQDeR9RZQLHph7BRYaG73JvLbl9cQVxZ9bFziFanwoCrUgIhAIwauVzEpz+zTXlmCHDLmW+RKcXr38SETZYVlqq2j086", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81596}, "engines": {"node": ">=6.0.0"}}, "0.3.6-beta.0": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.0", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "a86ab183cd73712d7a13ee481de343aa13ee4bdf", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.0.tgz", "fileCount": 10, "integrity": "sha512-c01tGUmxYTkK35boqusPP6pYhf1E38oJ7cTtUApXQSGEhDLuWLfUtt7aqi8NPWiSjI/0OuJYeIevlA51Yzh0GQ==", "signatures": [{"sig": "MEQCICUYvF1Uu0TetGnCtV5IKnSBZoaNwRHZHPB8MOmUcq4yAiBZZBSiZpPGumcup9wpxUenzTXC6X9Wd9Dgvp8zEyTIog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104990}, "engines": {"node": ">=6.0.0"}}, "0.3.6-beta.1": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.1", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "585cea7c2d3ec86824f7643168e9b1b8da6bcdda", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.1.tgz", "fileCount": 10, "integrity": "sha512-HLhGll6NZkn1dvNy5IwEUnTaGOaZFQljViMrWXsNmTdRtA4CA9ZybHEBVUv4jOUJF9TB8ioV0LvzK0D6jshKZQ==", "signatures": [{"sig": "MEYCIQC9fb5FHw7Gj2wv5BK/PWAgPxpmlWlIQHJZJD2iALKftAIhAP+MKLC5UxJyoGborj8hiVSAfbGBIjv0BV63yk6ZKPAl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104963}, "engines": {"node": ">=6.0.0"}}, "0.3.6-beta.2": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.2", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "91ce86a2c8566073e3a8e559ce6bc13ae3fb9b25", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.2.tgz", "fileCount": 10, "integrity": "sha512-n1d8d3LnT5uU665vCUlQpBtVdZ0tMXiTm/8+YaqMLCMQ0tjMgrPoOd38njoAmtYuUVyvR+sd9gfDWpMDTH8k5w==", "signatures": [{"sig": "MEUCIQDAgLtr4Sn8DscFqz/oSQXUm/0i1y8rroyLiD0rtvWx2AIgRbnjnO9OVayt9NIfw2DOZWS7hT8BG4fImZsQML2IfuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105375}, "engines": {"node": ">=6.0.0"}}, "0.3.6-beta.3": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.3", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "7f97480a8d83a7c5f06a9a1a70b4e9b1808d526b", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.3.tgz", "fileCount": 10, "integrity": "sha512-3Ok+ILMAeGsxRhVnv6uKgMx/BH36Y+h0D+BzAypGsXPK6+KPU37rd/wU2qj2/Eypo8EYOPHkDF5dbjZIErRtUw==", "signatures": [{"sig": "MEQCIExhqlRPeC5zurJp6C6Lytf5LSchnQlOvLxeELqHrtqVAiBD0yaf+K+gpYtxQhs7opcQm6zKvniUrLUMIp0Iw7Hp1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106289}, "engines": {"node": ">=6.0.0"}}, "0.3.6-beta.4": {"name": "@jridgewell/gen-mapping", "version": "0.3.6-beta.4", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "devDependencies": {"c8": "7.11.2", "tsx": "4.7.1", "mocha": "9.2.2", "eslint": "8.14.0", "rollup": "2.70.2", "prettier": "2.6.2", "benchmark": "2.1.4", "typescript": "4.6.3", "@types/node": "17.0.29", "npm-run-all": "4.1.5", "@types/mocha": "9.1.1", "eslint-config-prettier": "8.5.0", "@rollup/plugin-typescript": "8.3.2", "@typescript-eslint/parser": "5.21.0", "@typescript-eslint/eslint-plugin": "5.21.0"}, "dist": {"shasum": "b63fec0ead8041a154f157dc20b20809cd55d89f", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6-beta.4.tgz", "fileCount": 10, "integrity": "sha512-R9Zbvfrltklz5QAgf8sYeiwPnkbLRD4HfS3DfAzeZF4CmM0iY2EckGgNDschXN6aBt8PgeNYCBfp12xuR5GpLg==", "signatures": [{"sig": "MEYCIQCXmPRyf7hqW4eZO/TVlMcuMimXmilsf7JXXB2uIGsDhwIhALSxXXyiVQiplR1L5YJwk0BPQ0QLTKsUH7d/FoykVPah", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106318}, "engines": {"node": ">=6.0.0"}}, "0.3.6": {"name": "@jridgewell/gen-mapping", "version": "0.3.6", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "dist": {"shasum": "9c3ced7fc4ceee01fbd5f8cd257310aa71341a0b", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.6.tgz", "fileCount": 7, "integrity": "sha512-e8fGuQbA+pfsS2fGGRZgciyzstnkNrZCRIuOzgej9WCxPjHW3fn6h9SoJC3MrvfylMegiJPse94+mBJGf/qltQ==", "signatures": [{"sig": "MEYCIQCorULB0DpN2ouxXL8p8KrTTklDDqFjuUWndC0cGJAv3gIhANEsBSVAiJNktY9UFL2T2AEHQJkndtrD9QSvo4cW2OvW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31627}, "deprecated": "publish failed to build the dist directory"}, "0.3.7": {"name": "@jridgewell/gen-mapping", "version": "0.3.7", "dependencies": {"@jridgewell/trace-mapping": "^0.3.24", "@jridgewell/sourcemap-codec": "1.4.16-beta.0"}, "dist": {"shasum": "724ecf7ce7bb6aca0b4bf8d15ec40fb435162ef3", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.7.tgz", "fileCount": 15, "integrity": "sha512-rNoqXKSm/P1EJw/lrzyaQepC6KfryLFSShA+jl1mbPYqutXlvZWwm0x03W6jMHaYJqIOmc7QarD+GVfG9hO7XQ==", "signatures": [{"sig": "MEUCIBKzkj5J8/qUAjI0EOX/IgFRBMPKyapGVvkWgu1OmiBbAiEAqzr54uEtoYXKAnTzDWA4IPZW6yLJKj0ynsyDBZ27ucY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 275633}, "deprecated": "newer syntax features were not transpiled"}, "0.3.8": {"name": "@jridgewell/gen-mapping", "version": "0.3.8", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "devDependencies": {"@rollup/plugin-typescript": "8.3.2", "@types/mocha": "9.1.1", "@types/node": "17.0.29", "@typescript-eslint/eslint-plugin": "5.21.0", "@typescript-eslint/parser": "5.21.0", "benchmark": "2.1.4", "c8": "7.11.2", "eslint": "8.14.0", "eslint-config-prettier": "8.5.0", "mocha": "9.2.2", "npm-run-all": "4.1.5", "prettier": "2.6.2", "rollup": "2.70.2", "tsx": "4.7.1", "typescript": "4.6.3"}, "dist": {"integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "shasum": "4f0e06362e01362f823d348f1872b08f666d8142", "tarball": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "fileCount": 10, "unpackedSize": 81596, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC4lRjYrY+X+5qmRros2HRnGDP5pe42Kep5Q6wv9SLM2QIhAMwX3MD9c4IwKUY6A0pA9ZWM+Mwt9gks23jCUURBbuzB"}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2024-12-11T19:10:03.864Z", "cachedAt": 1747660590399}