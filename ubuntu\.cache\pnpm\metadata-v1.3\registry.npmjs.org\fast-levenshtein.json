{"name": "fast-le<PERSON><PERSON><PERSON>", "dist-tags": {"latest": "3.0.0"}, "versions": {"1.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.0", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-mocha-test": "~0.2.2", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "directories": {"test": "test"}, "dist": {"shasum": "0370eda31cff867c88745cdb89921e4c76ae8393", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.0.tgz", "integrity": "sha512-hx0D818+r2dQtEDGYWwyO5UTXpalnBh5B+93Udhb+J4TEdP1Cil15VDgLf2f5oS8gbA+B6N9I5Mr1oeSW9Yz1A==", "signatures": [{"sig": "MEYCIQCTNmf6iMIYXCOTWonNQdVIbaJF9C72hAPZNV2wrMMHEAIhAPdJDTCKOfi/VGjfBHY0mTteK1cbrBDWl7KVBllpypTa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.1", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "0548ef9dd6f2578b1ad22dc64515807eba2247b5", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.1.tgz", "integrity": "sha512-NP9Rw24KNmDXupnfoiGU+enA0upReUTVjt1jL8yrCd+vDup7lAlYjHTvWThjxDdpXZLshSJUqEL/wFGSDBh6rw==", "signatures": [{"sig": "MEYCIQCH6K7iGbxlfHLkOiFI1btvNE0kzXXYHxn6EnHbH98BLAIhAJx5yij7LCpcb0faYGEhx1ENwtWdIuZpZom2MUg+Wn7K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.2", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "cf564134f8339d92125a7b2634aab2a885d7df39", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.2.tgz", "integrity": "sha512-429sQelDI9wPYju5wVmeYLtuZHeI5i5/AgaeORQ1GA0NjWqvQiEsfPagsJMvaELXCLtDMtcZIiJu3OxHWz8qhw==", "signatures": [{"sig": "MEQCIAD4+Qfd4D5uD65wWEiFXOX4MBJQksfl3W97lcO0emw2AiBGH1TAKLWM/jWWx2yrsiB2jEtMjO1Zvf2miHHydBzGag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.3", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "fcee403eaa08374e9028b8bd11d09a4d2f75e62b", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.3.tgz", "integrity": "sha512-DpHWl+dZY8EJfPMdPHqQyBC5dn3YDx03WMg72m0ixzCFlwDihdVSyluv8c73CU3ly/poEY0GZvGmGSheQ6Bd3w==", "signatures": [{"sig": "MEUCIHHa1WuC2arzMagQcqRYwz4QZCAR58FDSrPdDn3qo6uwAiEAntojrR6XFPlanfA1uRO6jbpx/QqWoX3KObP1K6DagUY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.4", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "4d48aabd04a9e5790b2851f93ea84cf4634eeb0c", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.4.tgz", "integrity": "sha512-q0Xdux1nM3eiKoCw4bsJFN+AzS2m2iGZDg+a95nv5tOunxkZlkbYKTLukf8R1F+5bXbOPRA6xCmX3JrIi7qQzA==", "signatures": [{"sig": "MEUCIQCtsBpjOA+sBWhLYTa/aQZFg+z4TtXiyUAA5h4LbV1PoAIgEzmnFO9MnnLI/l8wkwm6N1odEgMHRrwVx3gQKTSpsiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.5": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "d21e2fa0a784a4d2147476df5b8b5048f9d67a5d", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.5.tgz", "integrity": "sha512-C<PERSON><PERSON>RUG93XcJcp1Do6neSU1T58ndMGP7sRGLYVO32HvWmOeAAEqp18vU3Sn0h91NHxgunC9HUWUqkxCzKAq3mQ==", "signatures": [{"sig": "MEYCIQDpNoUiswTiRdixHb2V78RbVafSjD8EXPoJT9DCKKS1dwIhAP591welkK2lqHSCCpaBTPkToCMsuBBriwtKOZ8iagWq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.6": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.6", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "3bedb184e39f95cb0d88928688e6b1ee3273446a", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.6.tgz", "integrity": "sha512-g+RPaM3i4gaTlGSIjItZI0jHGQvGeDzYsrKAuOq2KcgYcbEFOZWdV5DN5aEVnYSDdVwmTysS1EQyohsKwwKJiA==", "signatures": [{"sig": "MEQCICuWQwdcZucZL56T4yq068szW4WeVd00JUXK5ur2wlqzAiB0Fs0qQalWUHcC/pUTMROhHWKklBN8ESgAhvWrLGvEzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.7": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.0.7", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "~1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "0178dcdee023b92905193af0959e8a7639cfdcb9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.0.7.tgz", "integrity": "sha512-hYsfI0s4lfQ2rHVFKXwAr/L/ZSbq9TZwgXtZqW7ANcn9o9GKvcbWxOnxx7jykXf/Ezv1V8TvaBEKcGK7DWKX5A==", "signatures": [{"sig": "MEYCIQCKU0ew+4UvqIASzRRdwAfnfAqBZGOrAO95+zAx8lrsDQIhAJKb52uFkmX3MasMuRvwx0wEBQF0sbhBr7/L9OQFhdUw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.0", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": ">=3.0.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "87fa00416ee6b80fc326ca61993bce3b93974469", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.0.tgz", "integrity": "sha512-+BofjGJr1hVwh/qbt8tcLTYazQ9mo+7U38pyQmxwmpT+9+qlLAytiRVi43MO2LRbmeosPJtKUEdIx0iygpx6bQ==", "signatures": [{"sig": "MEYCIQDpx2rOzZNKvjAq85xuh3CNDjoYip++KH+gZ+iGZDmM3AIhAI3fQKyjJYeqCXX0Ih9/B9hv0bALz4ouaw+1Txd5yOP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.2", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "5df059fc7a131a7471eff164e6884e5993f72777", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.2.tgz", "integrity": "sha512-lKf7jPz/vR+HbZwAzFySUP6Yy6WMNkvUh6cvmuq49ZAgarcixNMZ7oscrZXTr5Ucdq8g8+iAkQjETl/oqvRz/w==", "signatures": [{"sig": "MEUCIDiGkrpN8dntSy7zbLI4jUaVgwPDJTz9FWO7+A6fTr5fAiEA6ZYEd2/nOSS2EndRmIKTmVsD3/tUT3/UZMC/DOnrC00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.3", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "2ae7b32abc1e612da48a4e13849b888a2f61e7e9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.3.tgz", "integrity": "sha512-zd0Ci3bYxNv/UpEQfS0dLzBn25s2c60T7oX6j7WYH/mhe9+OXYGphb5ZVyJbqvJM713f7l9OAg/12fJZ8VNmdg==", "signatures": [{"sig": "MEQCIAu3QIqcccYZ4UK1NISDP5vnptCek+bPSBBzVTjOjLn1AiBZG4N2FvPcMKCR1XMyKX1xE8g7uyEeXedld3NBrGsBAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "1.1.4", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "e6a754cc8f15e58987aa9cbd27af66fd6f4e5af9", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-1.1.4.tgz", "integrity": "sha512-Ia0sQNrMPXXkqVFt6w6M1n1oKo3NfKs+mvaV811Jwir7vAk9a6PVV9VPYf6X3BU97QiLEmuW3uXH9u87zDFfdw==", "signatures": [{"sig": "MEUCIGcBiTbrVkXeFWPHzKHqUW/8oPdn/D5qSiPGk5+rFcsWAiEAnfunHIgt1dAbY/rs7Xs+ZDYMSX0DOuGcR3YL4A2ZKDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.0", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "0e833c3710f11982027e23963dc48c03c31a81b5", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.0.tgz", "integrity": "sha512-h9jLQGo+61esNmfWhgXeTDxwHx3Q8HrvIj1i6EmugXfRN1+FZk48L5AOsfA/Wh5yLATboWhTughjgqS/buEZQA==", "signatures": [{"sig": "MEUCIQCs+wh1STd9Si3B+c38eAx/K8Kf0tfZBEP5n2/PvTv5ZAIgU3kEWWIqT1p0dIa9N5GYecfmax9mLjGwOohpARkxEqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.1", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "3fa8823203529e8453f2ed8f2852bd2a6db182a6", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.1.tgz", "integrity": "sha512-3AD61Yz2v2ehvRQxnpHozUZmPNlYHu5DiHcbF1OXx7Krholt3OkLZEhYR731S0i90lB+k4vhPrBRGy5SZIzJnQ==", "signatures": [{"sig": "MEUCIQC1NiboFo6qo7xPVTnKh7VpkULVWsdM2dEEc6uEc7+fhwIgRNJoZ5qunlv8Sq+bTZ77+9buSiahQgEsKzFG4JC1c9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.2": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.2", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "8b7a7c9b135353941b5283e4cb1e3e16831c0719", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.2.tgz", "integrity": "sha512-yCGlZJsnJ2xpVjJNeOXEDg+rbRRLPu+o+HR4AtoQOPN3Qtxj3jrXnn9CsQdoOlan/Xn0iPOAOek2LCKgiDv2JQ==", "signatures": [{"sig": "MEQCIHdu4/Q9W19UQ3fFvL18dYkV3zJJXuay6Ckd03hjp7HaAiA6RNDcingkY2rkTX2Fxqfx9MsiE6VqG5oAJWM8BBu2Zg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.3": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.3", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "2e4b613e0a0a2f53df583c765a816b97b0c98a9e", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.3.tgz", "integrity": "sha512-XZJQcE7MJydu1y2McHEl315JybTyLdgiXWTPHoLtxNyAjcZNRsQgg9yVxY6bsUaHG9vWiGeQX9sqkwkA3gV+XQ==", "signatures": [{"sig": "MEUCIQDA95lkSZ3fMz2TcM/ITdo6Efiy9jCvqvcE+CW8zbfBcgIgUbSK7TzjL/k92wD5X5mzTctJ1pdFdQ9Zw0bTB5UgweU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.4": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.4", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "e31e729eea62233c60a7bc9dce2bdcc88b4fffe3", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.4.tgz", "integrity": "sha512-rXyNkgl+50O/pR2wLRXW92TjZGSEEkLb/xW0PE/BFPlfae23ljaFvvkGjerrmytV/Yzr99f5Mfs71pRnHIm5Mw==", "signatures": [{"sig": "MEUCIQDBhhTihvRrS4YYRd+LPEobtReEWt1mJFOgNvLS7ysPQAIga9bGZRpOVSKZ/tLZDttQZf0LoMcm+G0EDeftnc4d2Bg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.5": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.5", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "bd33145744519ab1c36c3ee9f31f08e9079b67f2", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.5.tgz", "integrity": "sha512-k/Cyamv0o4QoCvYZIRx1vza6Pu54UV4HLCR//rWS83ZJYD8FAGSyf5u43u6y+eDpzKK+az2+uZdESTY7G2Q+Yw==", "signatures": [{"sig": "MEQCIAf9/E59JGiz5mQ/YhVkcMHU4VX0yjgKnUxda4OdF2B+AiAUazzl1oSX3ntKKgseWUfqfCsk4h/Jxyn0DDYffkAkHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.6": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "2.0.6", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "3d8a5c66883a16a30ca8643e851f19baa7797917", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "signatures": [{"sig": "MEQCIAOQFCpX5RDz9dO3fg+1X0nUZ5rtlMAZAyNZtp638CJpAiAERjRKr0xakMHlk9hrdu4nPCpyy8TCa7W+4eVFC+z8Wg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "fast-le<PERSON><PERSON><PERSON>", "version": "3.0.0", "dependencies": {"fastest-levenshtein": "^1.0.7"}, "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "mocha": "~1.9.0", "lodash": "^4.0.1", "grunt-cli": "^1.2.0", "grunt-benchmark": "~0.2.0", "grunt-mocha-test": "~0.2.2", "load-grunt-tasks": "~0.6.0", "grunt-npm-install": "~0.1.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0"}, "dist": {"shasum": "37b899ae47e1090e40e3fd2318e4d5f0142ca912", "tarball": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-3.0.0.tgz", "fileCount": 4, "integrity": "sha512-hKKNajm46uNmTlhHSyZkmToAc56uZJwYq7yrciZjqOxnlfQwERDQJmHPUp7m1m9wx8vgOe8IaCKZ5Kv2k1DdCQ==", "signatures": [{"sig": "MEUCIHsSsc5Rk4bKSA2O9rbMGbXUqlftlrZiOel4PnufXCp4AiEAkFTOISsdiiHtd1Emw8RSxzjhqKm1AUOzvrcbqQnsREg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGEyOCRA9TVsSAnZWagAA9wkP/0OtwmE7r4Zttrw5HjFF\n+KZR9XY29uR8BLdqoPYZRnP+FVthRNVQDCI/W69ufVL8RjFwaBXgYHHAC6NV\n/gO1N4O6P0RSRrSESlqek/0OWNmsRN+YKi0QCavD1qgRPtiJRUp/wmO5Z4lQ\necWnVaCn0J19gLlxW9qPkeF0TFwHKCQZW1D66M2bxrdkkyxGEcXWW8E/xMjr\nryas7hWZWHFolz4v3xarqjBMuFHYf0Ia6KC7NAp7mOMdjp7245p38cm+oMfC\n0n9KJJaeFvuslsdV3ggaKWTUyZ4afuVaoxxK2twcR3CvFgTRPGihhG03zGc/\n9YwMtPb0q3Gg0pvguUPPhtJ4FW+G2Rg5pMsPov90CsX8GAqAJu0tnm+Ezdvo\nXKeVM4LChO3KrBXnjbfsfbuhm52uD6gZrQhkEkNkNwmrcC8YkXqZ1jF5s5Um\nZ71gHQXAOp4RR3weT+3YT6zFJcBQcE+SN2AVPlDpofOccxTJ+URxX8AUbKk2\neQo8oXsJUtSojiJN5f5Py5s8eIApGNxljnKg1mCh9UnSUtpYzgYSW2EhxEcl\nmqTVCgqwvikfQvrP+3a+jtqt548ES3vG6svIfwI4EZsl6O2EYoi53JoQmLci\numAV8s4I+T3L35vB+jwkiuE80DrRKkbJI1DYi8aTjd3OJTHllxfrYZqUNM9/\n4kYD\r\n=sqfc\r\n-----END PGP SIGNATURE-----\r\n"}}}, "modified": "2024-11-04T19:13:47.279Z", "cachedAt": 1747660590353}