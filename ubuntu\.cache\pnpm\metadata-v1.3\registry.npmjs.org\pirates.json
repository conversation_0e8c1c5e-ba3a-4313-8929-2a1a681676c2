{"name": "pirates", "dist-tags": {"latest": "4.0.7"}, "versions": {"1.0.0": {"name": "pirates", "version": "1.0.0", "dependencies": {"npmlog": "2.0.0"}, "devDependencies": {"chai": "3.4.1", "mocha": "2.3.4", "eslint": "1.10.3", "rewire": "2.5.1", "istanbul": "1.0.0-alpha.2", "coveralls": "2.11.4", "babel-eslint": "4.1.5", "chai-as-promised": "5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "2.0.0", "cz-conventional-changelog": "1.1.5"}, "dist": {"shasum": "e4baf01b78ea92cb02877946dfd7495fb06fe25b", "tarball": "https://registry.npmjs.org/pirates/-/pirates-1.0.0.tgz", "integrity": "sha512-NeMpKB/vo29gwVT81g8H3BjxojjJ9QMEl7rtxj1gsMcw6ckecXGwy/vvOS0tk9gMeIykJxVtZzHMAyisesxI1w==", "signatures": [{"sig": "MEUCIQCCYC18FbTNjvGZFQUD1rjlRFVxNUfjfDbgG0BsfSPpxQIgaHPyrr1xqiynHtP1om5WyKbeHpeI/Bd09IvjTaQX6oo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "pirates", "version": "1.0.1", "dependencies": {"npmlog": "2.0.0"}, "devDependencies": {"chai": "3.4.1", "mocha": "2.3.4", "eslint": "1.10.3", "rewire": "2.5.1", "istanbul": "1.0.0-alpha.2", "coveralls": "2.11.4", "babel-eslint": "4.1.5", "chai-as-promised": "5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "2.0.0", "cz-conventional-changelog": "1.1.5"}, "dist": {"shasum": "7bfbcceb8e8273ce58ca5e055e9b0f1a45757fec", "tarball": "https://registry.npmjs.org/pirates/-/pirates-1.0.1.tgz", "integrity": "sha512-uEZPYZCwvfCB4EQI0GZdxWIUyOTeroSMLxc5/vn4liUS71N7lgs3jeJ2XQ6bPa0/Bv2Ot8ALnKwpTc9tVIpW3A==", "signatures": [{"sig": "MEUCIQCBDhHF0a6CMKc9jO8bcgr3b7+T7O1yVLeyauTN9bCugwIgUIiQW7U7UARteVKCVH7Jl901JhshsGQ0DJJahsdO9Yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "pirates", "version": "1.0.2", "dependencies": {"npmlog": "2.0.0"}, "devDependencies": {"chai": "3.4.1", "mocha": "2.3.4", "eslint": "1.10.3", "rewire": "2.5.1", "istanbul": "1.0.0-alpha.2", "coveralls": "2.11.4", "babel-eslint": "4.1.5", "chai-as-promised": "5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "2.0.0", "cz-conventional-changelog": "1.1.5"}, "dist": {"shasum": "11836302052908a335e2cd12374686e6cc06fcfe", "tarball": "https://registry.npmjs.org/pirates/-/pirates-1.0.2.tgz", "integrity": "sha512-sH2fqf9VnI4NuJSsxlZwxFxClia2e4h533l4h6V9Rs9CxzfUhSfjkicSk54g1OED1G0dXRLMyqDs0vqAEKbH/Q==", "signatures": [{"sig": "MEYCIQCE9RVzfPUrxR0aesocShRVrvH986fUKAJKjGltinIWDwIhAPNr6H/aX7NqnycwZ7PjpBMiYDQe1x9tk80nkI/sm+q6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "pirates", "version": "1.1.0", "devDependencies": {"chai": "3.4.1", "mocha": "2.3.4", "eslint": "1.10.3", "rewire": "2.5.1", "istanbul": "1.0.0-alpha.2", "coveralls": "2.11.4", "babel-eslint": "4.1.5", "chai-as-promised": "5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "2.0.0", "cz-conventional-changelog": "1.1.5"}, "dist": {"shasum": "5e3b44b2cb94419134afe6ccd461a69a0f12a9fe", "tarball": "https://registry.npmjs.org/pirates/-/pirates-1.1.0.tgz", "integrity": "sha512-LgUqm+vueWJkmH51tbOk9aouqsFiQIyt+4FE4Ea8zmPfA4LhTbkDgGaq8b6WDheiG3Jwbe+sqWsL9YwNwiaKsQ==", "signatures": [{"sig": "MEQCIHqbh/H1vMqMmenDPy3TyriLgxDU/epQnEmKOW/zuZxBAiBhJ4r/jOPp06Zklp56dI+qDmaEMCZsVWPNR8yCo8YbLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "pirates", "version": "2.0.0", "devDependencies": {"chai": "3.4.1", "mocha": "2.3.4", "eslint": "1.10.3", "rewire": "2.5.1", "istanbul": "1.0.0-alpha.2", "coveralls": "2.11.4", "babel-eslint": "4.1.5", "chai-as-promised": "5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "2.0.0", "cz-conventional-changelog": "1.1.5"}, "dist": {"shasum": "5b4d106db8b5133736e2c947be2fb0301b62f2a8", "tarball": "https://registry.npmjs.org/pirates/-/pirates-2.0.0.tgz", "integrity": "sha512-ncmDtsCEKhy32HXwRkBHHgDy8qkRqydet4hLTIQ9cTMpRlF5Xnp0Ek64kagnewKiPjr8lO3UUMZqOBqQZLmlJQ==", "signatures": [{"sig": "MEYCIQCDJSL5iMf/KZgGL+yMZU3HMKwWcTL3+cS/nMQ6OSgk8AIhAIgKozCXSBa4BKdiegQf88PJ5Rk5NAeRs3l/qjQYadTo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "pirates", "version": "2.0.1", "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "eslint": "^1.10.3", "rewire": "^2.5.1", "istanbul": "^1.0.0-alpha.2", "coveralls": "^2.11.4", "babel-eslint": "^4.1.5", "chai-as-promised": "^5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "^2.0.0", "cz-conventional-changelog": "^1.1.5"}, "dist": {"shasum": "cf4126f9e546bbe6a086b2288a5982e10be9d3b7", "tarball": "https://registry.npmjs.org/pirates/-/pirates-2.0.1.tgz", "integrity": "sha512-KwZnyl4DL0O59CsuSIU6A+Q3MWqiH7qtMf+43nshKH50jFk6TnMycuSTHADpIHm8r+/MUI4SI+oUjrFWuLcfaw==", "signatures": [{"sig": "MEQCIAiOGrKgyV2SrqjvWtavN6Zeu2P2bpuIoc0OZyMPS7ikAiAC7ki72anXKY9LMeyDw0M03fWLIx9NEmTZ0Ix+Ag0Bhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "pirates", "version": "2.1.0", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"chai": "^3.4.1", "mocha": "^2.3.4", "eslint": "^1.10.3", "rewire": "^2.5.1", "istanbul": "^1.0.0-alpha.2", "coveralls": "^2.11.4", "babel-eslint": "^4.1.5", "chai-as-promised": "^5.1.0", "semantic-release": "^4.3.5", "eslint-config-airbnb": "^2.0.0", "cz-conventional-changelog": "^1.1.5"}, "dist": {"shasum": "a3deffd3d72e1ed8dd6d50b6da1e46f9d0a649f1", "tarball": "https://registry.npmjs.org/pirates/-/pirates-2.1.0.tgz", "integrity": "sha512-0uHdMpDsoWwzoPeXvh72qj/qNQdEcslkbJxXvvkQdffjy68j+j1GhwkEu3P5uffMTe/H6EU46szxr3COOaPtWA==", "signatures": [{"sig": "MEQCIFfFNwixgs+KehDGxY3i3t4K669M++tPpfOtr8wZxgN8AiBTgbUlc00f5LJWlGM2D5FAQMWgoRmiaUNphNkSJWSceg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "pirates", "version": "2.1.1", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.7.0", "eslint": "^1.10.3", "rewire": "^2.5.1", "istanbul": "^1.0.0-alpha.2", "coveralls": "^2.11.4", "babel-eslint": "^4.1.5", "mock-require": "^1.2.1", "semantic-release": "^4.3.5", "eslint-config-airbnb": "^2.0.0", "cz-conventional-changelog": "^1.1.5"}, "dist": {"shasum": "ff362e6084efdfef9033b5d2ae11ba312bc4de34", "tarball": "https://registry.npmjs.org/pirates/-/pirates-2.1.1.tgz", "integrity": "sha512-PEZ24vaWFFqz+Q34Nq3HF8NmjRmkdOHGr60TlDKAKt61bwG0UOSjrbcFYy4W+zt8s1uvE/lk8gW3e8VRaHw5SQ==", "signatures": [{"sig": "MEYCIQC8/5mAJX24bKDBJ90USUedLyLcLZYdHm2db+g9bmyTsQIhAMRpO5FKXxuYKqwEF/jUFFW+HRskDFVm0D289N/JGaAn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.2": {"name": "pirates", "version": "2.1.2", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.7.0", "eslint": "^1.10.3", "rewire": "^2.5.1", "istanbul": "^1.0.0-alpha.2", "coveralls": "^2.11.4", "babel-eslint": "^4.1.5", "mock-require": "^1.2.1", "semantic-release": "^4.3.5", "eslint-config-airbnb": "^2.0.0", "cz-conventional-changelog": "^1.1.5"}, "dist": {"shasum": "69e92d12177b5fe358e3418a11d63c69b3b4b842", "tarball": "https://registry.npmjs.org/pirates/-/pirates-2.1.2.tgz", "integrity": "sha512-kJjh846m4REmFYAZAwRpyqqva9TjY8qDRw+CI3KCCm/Hfj+ZSjFz18lXXamEPHShMwI1caMOB7cAH2GRVrJryg==", "signatures": [{"sig": "MEYCIQCrKqmDDanCVzMMlGEGvxGFiPRIbVNXryZ1RbjBNxORagIhAJsKfj8MSdF7fEVFqrXSr9qMnxaTrZRmtpSy8MKSa2/k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "pirates", "version": "3.0.0", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.19.0", "eslint": "^3.19.0", "rewire": "^2.5.1", "decache": "^4.1.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.24.0", "coveralls": "^2.11.4", "babel-eslint": "^7.2.1", "mock-require": "^2.0.2", "babel-preset-env": "^1.3.2", "semantic-release": "^6.3.2", "eslint-plugin-import": "^2.2.0", "cz-conventional-changelog": "^2.0.0", "eslint-config-airbnb-base": "^11.1.3"}, "dist": {"shasum": "30e8934fcf01694b4d212ed004501ffb26b8971f", "tarball": "https://registry.npmjs.org/pirates/-/pirates-3.0.0.tgz", "integrity": "sha512-Sf+jixF05HgkzrOLAqucdfF5l9oPS+Mt+4Z7rJDAIeeDD1thKHhyGxXVu4kDaNW3OfDg/5i+XAdyDzBsuNzxuQ==", "signatures": [{"sig": "MEUCICl5VwhCJnuiO5jYtTepkyPqY7xr7H59qQeZWg4PJ2rXAiEA2rl3AdVWK8uRq0o+X7hyso01GkegcJSG6ctOHIQPUGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}}, "3.0.1": {"name": "pirates", "version": "3.0.1", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.19.0", "nyc": "^10.2.0", "eslint": "^3.19.0", "rewire": "^2.5.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "babel-cli": "^6.24.0", "cross-env": "^4.0.0", "babel-eslint": "^7.2.1", "mock-require": "^2.0.2", "babel-preset-env": "^1.3.2", "semantic-release": "^6.3.2", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^4.1.1", "cz-conventional-changelog": "^2.0.0", "eslint-config-airbnb-base": "^11.1.3"}, "dist": {"shasum": "8a87bf9ab7f58d6c3deee6a1a1c892ffe823811f", "tarball": "https://registry.npmjs.org/pirates/-/pirates-3.0.1.tgz", "integrity": "sha512-dNflV76WKubwuS1qEFQV16pwXkQEBu8fJxNd0enGJAz5SPFLBsMLOvfzPsaiXrI+cVKKyBrEL2O2C6gZurJGRQ==", "signatures": [{"sig": "MEYCIQDwREJyVOwlfncsUQvFMmiOaUIdsItxzwbRwBONp3TDXwIhAOSXM82fe+KigPGFhAGovj2ZPcKA/wbCm0gMhSXpsNSJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}}, "3.0.2": {"name": "pirates", "version": "3.0.2", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.23.0", "nyc": "^11.1.0", "eslint": "^4.6.0", "rewire": "^2.5.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "babel-cli": "^6.24.0", "cross-env": "^5.0.5", "babel-eslint": "^8.0.2", "mock-require": "^2.0.2", "babel-preset-env": "^1.3.2", "semantic-release": "^9.0.0", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^4.1.1", "cz-conventional-changelog": "^2.0.0", "eslint-config-airbnb-base": "^12.0.0"}, "dist": {"shasum": "7e6f85413fd9161ab4e12b539b06010d85954bb9", "tarball": "https://registry.npmjs.org/pirates/-/pirates-3.0.2.tgz", "integrity": "sha512-c5CgUJq6H2k6MJz72Ak1F5sN9n9wlSlJyEnwvpm9/y3WB4E3pHBDT2c6PEiS1vyJvq2bUxUAIu0EGf8Cx4Ic7Q==", "signatures": [{"sig": "MEUCICDMB6Qau65+re8HveSuVNp2jvBviXpJpE/dx8Sy/A3dAiEAu6pSsCv2DepqftNnQKsH1LXy2dT4XCL5op0Ma5M/7eM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 4"}}, "4.0.0": {"name": "pirates", "version": "4.0.0", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^0.25.0", "nyc": "^12.0.2", "eslint": "^5.1.0", "rewire": "^4.0.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "cross-env": "^5.0.5", "@babel/cli": "^7.0.0-beta.53", "babel-core": "^7.0.0-0", "@babel/core": "^7.0.0-beta.53", "babel-eslint": "^9.0.0-beta.2", "mock-require": "^3.0.2", "semantic-release": "^15.7.0", "@babel/preset-env": "^7.0.0-beta.53", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^4.1.1", "cz-conventional-changelog": "^2.0.0", "eslint-config-airbnb-base": "^13.0.0"}, "dist": {"shasum": "850b18781b4ac6ec58a43c9ed9ec5fe6796addbd", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.0.tgz", "fileCount": 4, "integrity": "sha512-8t5BsXy1LUIjn3WWOlOuFDuKswhQb/tkak641lvBgmPOBUQHXveORtlMCp6OdPV1dtuTaEahKA8VNz6uLfKBtA==", "signatures": [{"sig": "MEYCIQCtJn5aWX6r7rWFGYYo/ceyygJ8KU9H21KecuM0zsKdRQIhAM+AYeCKPkTo6VwWV5Dc9gDQ9nUyfIcAglLE4mydR1Hd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbRlkiCRA9TVsSAnZWagAAeoMP/igbxexeiLg3GT7zQQ6x\nv+h2fvBq+ifgabiwms/LttUJkYBIgt5TCOkbdjg5y/ngnr27Ik1kI1qOginF\nVSnKrTPmxgMwWucivZnX1OV8SJtte39lk7S2FD9YHlBeW4NvTqB6l7aS1oh2\nhxrbg2tHd/IYvWVCf6camugA+TBdwxsVhMQCwo0IAlH0wpKuw2ZJ/n61Ii5/\nkNLiztA2Dnz4pwaTzgj0uyrdIlNf66KNOBts8PEPiAZ5h4UGXeVZWyB/kaA7\nlJ2vNH/7NGYriPyFL4/tUkIMSGiJqiBquAJMBBK4Z/GLj2O62rA3dNfTumTi\nhUbDVLD8rG9WQdlYoee4j2FYvLTsX4KFWYWFk4zQ65e+c6nhEuXyOUZ6YWd9\nEHbJVZukXED1EXXfEh8wjxZiXGq4NMdtVqu7FyltA5Y92nukpCJPMnIOS9Jo\nNFhdh0Zb3Fiv2cSj+MLHzNIncdr0t1mbVrAl+4XERaQSjFEBdLgbZmF+BPOq\njGP1sy98pTDqCkAqolWrllMymy9f9HqvL2KSvLkB6M/r4zwP/Nl1jiIEEjoA\nL4R7CcRgqA2O8s1s4fgWzmlYnunhPaUwl9RsK4dw/+7tmTJxdecyzdkCOFkG\notbZ6nPBXWz0gUIgsEf8Rmq9kovVbhtyyvhnzWVgxz9tuN1Abo1fAhgx9Cx/\ncM7W\r\n=6o31\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.1": {"name": "pirates", "version": "4.0.1", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^1.2.1", "nyc": "^13.2.0", "eslint": "^5.1.0", "rewire": "^4.0.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "prettier": "^1.16.4", "cross-env": "^5.0.5", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-0", "@babel/core": "^7.0.0", "babel-eslint": "^10.0.1", "mock-require": "^3.0.2", "semantic-release": "^15.7.0", "@babel/preset-env": "^7.0.0", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^5.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "cz-conventional-changelog": "^2.0.0"}, "dist": {"shasum": "643a92caf894566f91b2b986d2c66950a8e2fb87", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-WuNqLTbMI3tmfef2TKxlQmAiLHKtFhlsCZnPIpuv2Ow0RDVO8lfy1Opf4NUzlMXLjPl+Men7AuVdX6TA+s+uGA==", "signatures": [{"sig": "MEUCIQCBCn/1jhmaehcBH+Gd7i+jrYJzUcpryELUIO8eYMInMgIgVajRyGLgIu4zUuUBPsyKTdH1kQy3fjcFpuSSuCd2SIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12617, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcaa95CRA9TVsSAnZWagAA52gQAJOpJt7ROoCLbtxmtLKc\nc74NtFq/8RjG+d758XJoHPlvYybGwdYg9BEYDLYkB2GloSllL9lIE8CoZAvT\na55oE8YOt9nyjALv8RI3aewtVCC9At702qddUfGtzlCZNtuD9LkjhfhE3SdI\n7h/VvScJo+qrJNUenLAQK+u/wFAchbOSzrhmPTCqbJu/VUw6LHq2FurCRs8/\n9arci7I4eqeyZezRtHpNpPBZHc9oXT3w96WyFQZg7LzaVqRaJuKXE4mRhLYZ\nluyBWtoyoZPqAQ7xDhpfWJCKBIVGv3A73JdTVl9fKY6br85HGl0es15jzBXy\noUknQVN5/N2+oX5qHeATRU3iRCEdCZU5uSZW+CC1yp6F6Q92IO4VxOjZUzKA\nqEfu8C0JyFSeeeiK4a1VO0H7u6hBFDUCpT0+IuwP8fF1Pv2GetX58stq7gXz\nvM0cuCA8ysPWhGEyc/mBzPofeD69H2Br8BTEfy5NV6/FtqtMrQAFHqIbJ3Un\nTpGRt1BTGf+O1seSRsUrbdRme/CdrvbiNeyUGJAoLx78GfwrY3zUhG3huCcr\nux/jfzRkLs8SrpqJaHAVj+pcznAnXD041NSyG1cAXu69a/q2bBaElLrrYPUm\n6zmZ2E417DzvbWzwjCnZfLX3xig9vLA/KkcLfES4JnvgOVVocZVOaE2Akm6t\nJPl6\r\n=T18/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.2": {"name": "pirates", "version": "4.0.2", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^1.2.1", "nyc": "^13.2.0", "eslint": "^5.1.0", "rewire": "^4.0.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "prettier": "^1.16.4", "cross-env": "^5.0.5", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-0", "@babel/core": "^7.0.0", "babel-eslint": "^10.0.1", "mock-require": "^3.0.2", "@babel/preset-env": "^7.0.0", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^5.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "cz-conventional-changelog": "^2.0.0"}, "dist": {"shasum": "74c3e094412a3f13a94eccadc0e8fd7f8ab70cb3", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.2.tgz", "fileCount": 4, "integrity": "sha512-VPxHpPfPbxIEdH2/KPkBl1/YbrBFzOxxTyQ4zEWh9hgWozoSGfgx25qx8yuhe5wnD3/pwamKXu3v3VoFbkLeaA==", "signatures": [{"sig": "MEYCIQCf47obpLYeN+PEjB8N384+yPG1rGP62RdtdQIBnAEA5AIhANad1DCOGjGs8F24uUyOo3Ckd3NCcZVKiB/LdtSjCZws", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr3oECRA9TVsSAnZWagAAIcgP/RqYXCMb/hu2mu2UVWOt\n1sphj3n/yEQRsT8HccmR8kzcZKsexRJU+OwymGoaXLOBXLoB9ghA9HCBy1g8\n5FaRZmoIy2N//BrtjYF9au0AuYoORPe9U/+bVKb+/6XpUBpMIE4wDW/7iHCk\n8g224Geu/FhFmyKUTWvxxN0IeUohFh9HBmY6erBtk+R+etaWo5RXeo4I6cLW\nkS5VEaf4+hrHVt0ZqlKqcyLE4Pn+KN1Tpa52CsrSaDkLWfQ+mGfcBfigst/W\nKyMxJMJQykksp5OsASIxkRdScnVjSTZ7+/tKbPU/CCqGlrRwhypxKy3KbLo2\nFwPDT8/z2bNeEYxnBr/FC/HjbWTj4faLr2jXCY6mW+4QdaK4LPcUGkfMeJpT\nGRUF8j0JBrzHHa12TjiWMhWiopuBsObVdSQEiZzOpuxQibIG9YxDkgy9I33+\n4meAbB3qcQb/BOOlrxUIuWj6/JoNWu1F6c0f+6Fv0rpas+8LZBDWcFHG2R/A\nR8RkOmjZZbHzFsUtTZ3H/fvoMP+CmbaE1kZ6IifsrqwUywiKFcJUSN77MkkX\nKb92Z5dt2mpBnjln1C8njfLuENf3RwmpJp1dlNemG1xV5H0HmA6gRiNJ8I06\nnUPR3ICKMvTuCNRiR0rFGBHpu3tO/vF23Z68iYaL17+wYSQXtPVkmUuSYcPG\nQaVy\r\n=XnCw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.3": {"name": "pirates", "version": "4.0.3", "dependencies": {"node-modules-regexp": "^1.0.0"}, "devDependencies": {"ava": "^1.2.1", "nyc": "^13.2.0", "eslint": "^5.1.0", "rewire": "^4.0.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "prettier": "^1.16.4", "cross-env": "^5.0.5", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-0", "@babel/core": "^7.0.0", "npm-run-all": "^4.1.5", "babel-eslint": "^10.0.1", "mock-require": "^3.0.2", "@babel/preset-env": "^7.0.0", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^5.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "cz-conventional-changelog": "^2.0.0"}, "dist": {"shasum": "41a4acbc9bc78decd7f205ee828f34929b9d5749", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-e30YDq2NWgpyIpaKWgols6m/YfgyIyLEmVvrLH5mGrwUs6R6N2h5+DKluT502tex/BK+DO9DIRZ0qqm1/M3FrQ==", "signatures": [{"sig": "MEYCIQCEk9sqKh9SzxIZqOLfLK1DWNpxw8JwfN405r4QPFvmAAIhALAIilq057Hw7Gf7AahJkyVWLVeBMbGV8JVguKwbCuds", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr311CRA9TVsSAnZWagAAfY0P/AtrLtgrhFSMfjVMH/2S\nSACkU+MIqDTOEhGU9cXLi4zXlH3bxh/x13kdfCh3nbAAHjSIiknNXquI1Pri\nrDFgnIM89bd4PpvLwvi621BINLaMSuhzSW/DYg+yvctEtPmFIy/GrhqPBTBA\n6eboPpmhK1DRULWaG0PJ481uLHNmE1cMjzZvThVJIWiv7poohXJrnbf6zuUx\nc1aFjg4LGfuha1B/s59iWGbPX1KBVVslg8N/ECww1PFXyJfrDbpJe0J1m5Oz\nt9LZ8YauIBjYzLCZHpgpmhHYLhJ0KxPmkCDBM3MyTbwx8eORu3W7ZIFmofA4\nMel7GIPbIBabk1eFd6sKpwkCUsPT2Rgf7fJ3X4Car5vRwiRDrqWKMNh/72H0\no38/oSK5QCbB2ExT2l6DWuvCiLEFdcBe//wjhkvxb25a5fgywMC8Kv/qQp5D\nGOJAw2L8PaOSVMT3TLqKQFI5NasDkWP3nB9v8Gfsy3QPmhKBL8oUSJL4cSTU\nx7UwKd1JmAaSNovZ3hi7mpKauZdwRf2GpiQmGdlxLvkSlJHklae+yIUFTEzy\npdzLulRL2Bp6+aP44rMmf2svDT2fuUC9+/M/wCEqyJ5vQC2h9F3r5Psd71nM\nG+8Vwn6RtUh5z8BKG1tnpkDb5pKSh5TXJc4edDxL9NiM8eptP696+oV9NZqc\nKNTh\r\n=ibtF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.4": {"name": "pirates", "version": "4.0.4", "devDependencies": {"ava": "^1.2.1", "nyc": "^13.2.0", "eslint": "^5.1.0", "rewire": "^4.0.1", "rimraf": "^2.6.1", "decache": "^4.1.0", "prettier": "^1.16.4", "cross-env": "^5.0.5", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-0", "@babel/core": "^7.0.0", "babel-eslint": "^10.0.1", "mock-require": "^3.0.2", "@babel/preset-env": "^7.0.0", "eslint-plugin-import": "^2.2.0", "babel-plugin-istanbul": "^5.1.0", "eslint-config-prettier": "^4.0.0", "eslint-plugin-prettier": "^3.0.1", "cz-conventional-changelog": "^2.0.0"}, "dist": {"shasum": "07df81e61028e402735cdd49db701e4885b4e6e6", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-ZIrVPH+A52Dw84R0L3/VS9Op04PuQ2SEoJL6bkshmiTic/HldyW9Tf7oH5mhJZBK7NmDx27vSMrYEXPXclpDKw==", "signatures": [{"sig": "MEQCIGvP+1J48/cNhAAfBLwaZ1YvhoHnDJnK45MwRD8tLmM6AiAkHPryfqiR211nQC3Al740zncepbp/IGLeSmqTgBVECQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13456, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr6AKCRA9TVsSAnZWagAAQckP/iiaUaqeOzO1EbCPkn4E\n93SxQooC+p2TvY7mjEJxR/PPdS7Y3azAf75R5KsGAlW4fw/yBm7q6Mq/IUuV\nDwCS90Oz23Tu/RAeqGJvqtqzMImpHOzguZL7tCBGYiKlB1a84wkKVSdr5Gr5\ntLUjkBInOb110aGCy7cnjtF9DH3OnV6Aceb7urDgMiT5Fn7ANGITD6nqKM2i\nsNvmoV2ILfk1E15i13zw6l6hRSHXuF7uge7wRnRWLz4NcoT5H8mp5uAl2aGq\nqsUJviobksy8ilCnY7DmbPT75FYAZA1BHzkcIShspegfTiWCj5i0zxX01RZ0\nl8INB69Xznxc4srm1tzUPFIXaIpkLgS4jHc6NVhdDBKvbvofBKE8fz16gGEd\nYzEBJA6i1nHeYExyXyVb/7vamXvD6nvrrVpTVKjucde3RcztLptpkoPX8eGl\n9IzHTBSlKCzdZU6wM4u6tEfbH+B0yJF4SQyO9+1DIx57isk6+bsVq4mXJ3GK\nWr7O5dUKSON5+qHuKgSjvPTDtBzrMLzjlq3/RTC1bAFzM9Rg11rHNAJ3V8cA\nR24gX29We0W0OeVmtw7Z8OkxEvqKY/Cw+9U0rLTMMB5Q1CBxAtv1Bvx1fIh+\nhMrXt7Sgp2yjpOlv8N+sy3Yfzctv+WITlXYN0+IspUwxb7DO345ajawpdRD0\n6T0O\r\n=VQOy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.5": {"name": "pirates", "version": "4.0.5", "devDependencies": {"ava": "1.4.1", "nyc": "13.3.0", "eslint": "5.16.0", "rewire": "4.0.1", "rimraf": "3.0.2", "decache": "4.6.1", "prettier": "1.19.1", "cross-env": "5.2.1", "@babel/cli": "7.16.7", "babel-core": "7.0.0-bridge.0", "@babel/core": "7.16.7", "babel-eslint": "10.1.0", "mock-require": "3.0.3", "@babel/preset-env": "7.16.7", "eslint-plugin-import": "2.25.4", "babel-plugin-istanbul": "5.2.0", "eslint-config-prettier": "4.3.0", "eslint-plugin-prettier": "3.4.1"}, "dist": {"shasum": "feec352ea5c3268fb23a37c702ab1699f35a5f3b", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.5.tgz", "fileCount": 5, "integrity": "sha512-8V9+HQPupnaXMA23c5hvl69zXvTwTzyAYasnkb0Tts4XvO4CliqONMOnvlq26rkhLC3nWDFBJf73LU1e1VZLaQ==", "signatures": [{"sig": "MEQCIAk6zQFHhj6tHHBXQcFiE3gBVcp8qTot32UnJa6oXE7AAiAtKIs0/mr98lr14rC6fztkaeXbrC+/UFb5YjRidtNyKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7qRwCRA9TVsSAnZWagAA3IkP/2+xRsfGuBcyAMOvC5Sp\nZTMu9RxX/qvwDZC99nrQ5TkFbpfdxQOT83OuKwQh8DQ9hXtIJCerA5G1SoGH\nhSZ1n8gJku4pkyWV4/TLGQ+Pt/kgwfB5ODcPxCPdYuOp8S3tOcDP4e4+MNH+\nF9586ZX0oUuosz9H+9M32IFmvmfoIBt5pVXk639qa9xpgnhNDHJk/jfyTKCV\nTwP2YRE5ME5F9ssY5UQqIk36IxuwJFY8wk6wJa53DXVkTOesd/O/59A9lk0o\nWncwLBlfNPKDDBuRJQx4qwtq7cb34HYtirN5BxaJP9dBMOI2UwmNswOP3/9L\nXaUugC0U1oAIAHx1VNRng9McNiCsJeQ7o6AlTbi5Epi8VWt8IXonUG1cu0uV\n+zbZgfwLmz5E7c579hn75SYJtPoOxU/7CU7c0pMNU0aHBig6d29R8DyeQ/Po\ne5CtB+AqyjNmn0Hk8iPF+aigvve7kWnvYkJpZYA0iUcmjUX+F2Nq5iDQ+sqH\n9nKZ/2NvG6/Ea6hET9+kHaSxmWNQVRogNHcRSIh6Bg37M93UtrJIy8PRmsft\nxPW2F1RbyoGQeVwLVKlYGTPv+SEU5naKBWsCgWjRJ0UpvMj49/2efnoJnWZ1\nR+F8nBwWHv9PDuA0Eyu+HHdZOo3z05vzhv8QRwrPFkF3rJcZcmWDd44t3Yv8\nFB2D\r\n=grGO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 6"}}, "4.0.6": {"name": "pirates", "version": "4.0.6", "devDependencies": {"ava": "1.4.1", "nyc": "13.3.0", "eslint": "5.16.0", "rewire": "4.0.1", "rimraf": "3.0.2", "decache": "4.6.1", "prettier": "1.19.1", "cross-env": "5.2.1", "@babel/cli": "7.21.0", "babel-core": "7.0.0-bridge.0", "@babel/core": "7.21.4", "babel-eslint": "10.1.0", "mock-require": "3.0.3", "@babel/preset-env": "7.21.4", "eslint-plugin-import": "2.27.5", "babel-plugin-istanbul": "5.2.0", "eslint-config-prettier": "4.3.0", "eslint-plugin-prettier": "3.4.1"}, "dist": {"shasum": "3018ae32ecfcff6c29ba2267cbf21166ac1f36b9", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz", "fileCount": 5, "integrity": "sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==", "signatures": [{"sig": "MEUCIQCEnNWV5nEmNel094md8G+2M0ThdTeMH25d++ZN1NH9lgIgFzeSOdtobtzPD1GAmQNMaxHvfsMoVX2q6yiH9/yJMDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13459}, "engines": {"node": ">= 6"}}, "4.0.7": {"name": "pirates", "version": "4.0.7", "devDependencies": {"ava": "1.4.1", "decache": "4.6.2"}, "dist": {"integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==", "shasum": "643b4a18c4257c8a65104b73f3049ce9a0a15e22", "tarball": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "fileCount": 5, "unpackedSize": 12564, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIEDOaLXgpfdHi3YSvPXAvpsCx6ZY+mE+vZoGT89aoFzTAiA9O91Dygz7ZOQ7Z9hjpxWufLJX2uP/hfTB6XfCussq8A=="}]}, "engines": {"node": ">= 6"}}}, "modified": "2025-03-27T11:21:58.651Z", "cachedAt": 1747660590422}