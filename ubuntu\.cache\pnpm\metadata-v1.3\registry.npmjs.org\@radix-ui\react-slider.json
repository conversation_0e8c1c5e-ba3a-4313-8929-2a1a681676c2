{"name": "@radix-ui/react-slider", "dist-tags": {"next": "1.3.5-rc.1746560904918", "latest": "1.3.4"}, "versions": {"0.0.1": {"name": "@radix-ui/react-slider", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-collection": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "18871c0c20699e3490017fc0c9789b41f75862ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-KXN8q15ukJAsB7H/e/B1e9FJPvRXQnbNPDU/biJZ63vMUs5EiHYNygptRwjoWX/JFpQX4Vrrv9YPW7uDUjqOpg==", "signatures": [{"sig": "MEUCIAlDCJI5mOkUKOssuuFDERwTo8lZ3vNIeQTP3e7qAkvrAiEA2V+mOSzwYK1YK7xh6KRPUs+X4BG+f/gRCRwvTzz8HR8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117108, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbXCRA9TVsSAnZWagAAb/IP/3vxzBXLAXuMsjvfbkA7\nY8vmGbYbU2cFQGqFlf+LrPnLeGNIJRjw/tF//MWRwX1aSs3JrhcoXK+nbsWz\nADKKsOFM2xIfh3/KCbKCgIfDFFCTP35rVhyrOg6gtvASj3priXqtRGiC26ig\nWvMePbVMqVDqD+P8ke5OpFL/J0AahNQDue20rq+qzn7doyQywO6ngowYkSTJ\nn63Hg5a1ZKbc//UpZLaVU0Xnb5t4iG2UaoSu8AUAHF6x/WEmF09v4xy/+4mA\nmFt96IjFvKeKksq1uCGrRukxW+ESV8sAbNISoOGSSMZqA5hnjgVM/6P531OY\nB4pin/l/pwSG/VRhE+X88A+Tr1VWdHH5fCLNnOVzfKjApsVCH/ZXrAQmZE9k\ndvbjxxuWp4VfYbAtDCzQMlOsa53TmqDVTu1FxerNYQsdqaqg/hIr46irrZQG\ntoJeIlBiGLUFGJ4lEgMJ1P/asqCHozazpdXMFXHIIxL3Bg70AOgW6o2Zp9lO\nqahPnc8DvWjBJnyq+b7OFfWVns4Posh0DwcbAN/fhXYlj/rt3/odehe16lK4\nHBjcsyw+6UAE9PqzbYuE2sZMklF6BkHTcFhZ7AhoS1kIoX38wtAHkoMjiIdh\nwVBsTsTTk+EWheb8k4rSRAvrgNvMUGwGiKtIoBgx/8Jnvne/eBlXVsrN7Ah1\nwvk3\r\n=Or45\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-slider", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-collection": "0.0.2", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9649111116ced67865b8f927d5d3ad28613ca325", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-39pQVyf9JaQMTvjYvm/1tO6B+8x+5UCl2YDKt0iZXPEdem7U4Zrw+OZASgKXjD6m+fNNVJge+wUmtPx6ifIH0g==", "signatures": [{"sig": "MEQCIEbi96uE0/2Zizwd8OYMQq8BcME0OI2goGSiFqgJX8+YAiAPcRe2TpV0srJ/VMkX64e3Y3KkxwH1PRFiiFlgRxF+ZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvXCRA9TVsSAnZWagAAwtIP/RSU5WvJrE4NJg+TUC33\n4zw6VaDxeQdj+ygHEEL3MyZFsGl6y0zjVfxpFdZR5Vg2NKzRj3kMwbGeTIOB\nXrzKo0rMQoho1eghlGg2J2WzMOoD68vQD/dpglTexzbc3WZn0kbvJAnc+5Zv\nLyEnK1POIbiaDv1ROnUai6AmfYvNAf4eTmCbLt76ICqh4VBjvJoTK0JCUQHN\nwtZK7ddIImXmvIEN+5+nKzxOHQN/yG5S+e51AG/XB6/5CzAFEEUxb3NI7l9u\nCgFrh70Bl+cZXJ5t6e9yBohhMUnFPuA2l+WscfqGHcYu+pKDtUAsX47bIZP2\npXsyWampJ1eotZoXlxrpG0yLVt3QxssKp3iZzVAj7tiD1fjCj/aW8xe7k3NT\nLkdgO3extvecpAb47voYAfWVKKI8Rtnd/1bVMmvEkBVDAd4fRqpd6jFkcgw2\nY05qfhRwbyJpCzCjIYxMhxwNmxzUDzGeFtUOgMd5/9KQ8UCHgW7mHdFRwiq+\n6Rk/SO1jqrYZonv4b/h+Qc8Uc9XCwIiifnsTD/d+PK/HR0TQUxCdQhFVSp/J\nqHrvvXaGRgUvBQa6vCb13Ty3BLAYnZv3q9IbpH01mLx3erAeQ+jWgNbzUMoU\nvjzgZj5LCD2/e+4LcNk26ahLCkN1kCV8aM2Kg4XGdSNypXMHJ3GZZbm5ah6m\nkRcb\r\n=N3yU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-slider", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-collection": "0.0.3", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e9f6e13494080a2fda9bece964936c13ae6054f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-uBsqBhxJssr0fTTpKCSV+upf38FawpDZwOqrq6wcnfK9UfI2QlLVkbE/LjT5YEGuBFUnWyLIADnMgdWVe03MVw==", "signatures": [{"sig": "MEYCIQCUzPHBKZ235cLoSl5Z5ye75+DAZoNEgw8ogxDBle8G6QIhAOmwMa68f1brm32F++s47sC2uN+UxPhIBjHdjwOtmEre", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtqCRA9TVsSAnZWagAAopsP/37jkN41Bh7F7LX5YDhd\nIFng2fs3g9fXuxvBmRayetcBPABz/+krQNHLmJeJfc9oEacBK8dek45xjYOm\nj1UYxx4GtVig7FDFGCni2qCOEL04qvwtfQvRwAxxgxZq9ViQNE384UMMjLl+\nx52U72YiAgxhkpbmTv9us9S3TQka+3Ni2GEbSQr5ncKBZ7gCPdInc8UV9pYI\nBgpOPaI7JEtV2oRxshnmzlZH/Qm7zvaefH8ggFBPdKqTCt8ED+oP8/Zf66tV\nWDJumtwfOg79imWggN1FOKa+XYywVSqn5yi8S0TsfiKruUO02jy+TtqCs4Cb\nP4RgXE0ATu1b/jOZewzt2zfH/Y4idBxo0D+Mzw9aT3eZjeyJfR7hdE8bjNXO\nVGAlw45k2u0+eIo28UZRvEHvij2x1gXsoamCKi3bTXy16JNtccqzocNMO8Ay\niIadE47yuB5/di5sKA0gZHVJNClsR5rndKELYBbnRgirtuRoYAfXsxP3ZxSe\n/hH9CF5yVDXJ7W+u8ntayza1eJV7vxoT8wbyyG4mGe+molFjU6yT9U7QJ9AO\njg+ep9/3zztWh6o1IjH4rcxXLW3mS7Xmr1rf3fmwvOwtcbdDXSDDHemW8tb6\nvuzp4PQOt6B4m1/U/wZQC0jD7rZkUEQ5fyfDELSjLX2CEZdA+JiabS+cX10/\n9z/E\r\n=TX/T\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-slider", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-collection": "0.0.4", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a234e79304f9120207cb53002b043fbfde24448a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-IEwP7CpLq5t090+NoVnzRalqdlj9+nhnZsqoHAbfAGtfRWAFv/kSNNTrh/wHGTTWvwF7liQUCl24HqAhMrGg6w==", "signatures": [{"sig": "MEUCIQCJga+geL1j2G5xNOtHLxZsqKq3cTXApwRLRcMZL0lGBQIgLaGjRu1Ur+umSHnASUNs9D7M1+d2BK6jg/fZrjhrC8E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAdCRA9TVsSAnZWagAAoxUP/3jkBiHIbHX4fhWft5P9\nj2eVs0Q4y1F/yxC2nzemMjbA1QRccf43iXzLRDN21dhh3bFYCwCcXCNwFsVD\ntictmIKu9JXYgqE1xEYVujLcKWh0zDTlkGi+yMU0mHUUMB9TDDzzpM62VlBU\nPcfW1tutuoWZhkNlR/26/st8/Of7Ymrrbay9zmjs+f+76PD/uokaYF0HDXxe\nNeu5n2HQO/k6I3VKo717t1pU1beenZBOjBrHrXgGElwb1+y6TQKeqbs3ljbY\nJcDoo7kp59FxgV+rkJoAfxr002b6ICZsiU5mGngt/jDXKLcq2MhWzem1nPKw\nsE6Dk/fwjLLhKyGd7cI/aNDEex4zNtdToBRaxfhqvUawDOgVmM3XCYiumUih\nVtjmYbrGjIRViz+vZk42gNQ5w1v/9EcjtPnxkP+4Al4TpADKZ5JdJnswhinp\nier/L9wcLyQE2uScSNht4LUUUbYVT7UX8Z8kDlxpdCSBc/so/hgqjLr/B/8J\nr0ejjeFlNQxkvXIntK0JwzVzkZM0fJlCDSzpdtm/qhSifFfhKQ9mwCuY9q8m\nNPs3GI4z+cf2C0eoo8+7/TNKdrP8c2xgGXEnY+PTaJ7Zjx5yRSZmBJRJ7ZqC\n0BPiZnlqs1/Y1gqjqgSDm5FC5Mi7vIx1j2MYU0LD/7dy3JIJWGt4GSb2Dt1w\natkw\r\n=G32p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-slider", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-collection": "0.0.5", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b6c6ee87818ac2f30ad83bda5f8c30c5e22fbdac", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-QnBArvkQ50QsNGPsf5Cr9wFv7fwTXa6ZLH3czJd76Si57g6m0OnaSUDdjIFBL/xTN5e/3oAdUfRVCqFfflg2ww==", "signatures": [{"sig": "MEUCIQDtXNgJbCOj8R5TNODzH1LgsnisBiqUngfh4p4vpRCCcwIgfGX8nBDhi2qdh13HkdQOX0JQ1DK93nhDRPMo6ihmhSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121494, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V+CRA9TVsSAnZWagAA0rEP/i5dTXK8NhkqxG1ZwR/0\noevdEiV/aELFo/ngaHkWcGNLyFhq4q5FlUvX5oQ6W6nfOeiwbXqfCqxxdcX3\nYpLpgHOwU/zrj061113DzsfPnzFO2GhgDWLIvJhxuVOnMiY16uyrtADDD9pm\nD1iAjam2WiYK8yVd7f3VACiXPiNQeT3edFlJNOItYwtmhf6w7UB2jZUp1A0t\na8cmmsxbzilBH6109V3W7+6a18REORwlUnvWcMMu0zj7vM+vYGsN8SSP8ov9\n/W7eR+4EugK<PERSON>jcgXJd5VJulnTSxtDOUv8TS2fau4/8Y/KIaxURe8XPdC04G\nskr89VH23oXZrv82EnJ+GL0p3ugucJzHRectBvjvPhD+4Y4lhtxDS0cXcj6C\neRRXx2TTLiB07f3xtZgf2xCuyp6UQSD6qp5orGm8Va4B33PGq8ArlYKsoeF7\nUJ0+AnV59F6EXM6hpwY2Eruc+xQeTaeA9X1vI//t7ozzDtgd1A4pSEgyGZHW\nGmEhEOaVNS7YoOa8Mdumg2YWusvNQ1lbWSYAdJ5MOJ0uImP7Lq5e/l9qQSLu\nXmRzZRqKERKqAXh228ze7zC93+E8L+qrcPpWJpWi6KuNLBDee/50/VXXEMWE\nelKJpleZ49ouyZ6tFmS73b7JJQkXcHtxDe/ekK+iyU0gZH2oz5r4Jtuf/iPV\n8v2N\r\n=4n13\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-slider", "version": "0.0.6", "dependencies": {"@radix-ui/number": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-use-size": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-collection": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30a1f50a49b1f62b86d578466fec19c23dd8f27c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-mblcnEb2pQ5j7CXEQH8O+aar2NEndz7Ri+jKwpL3DAbvqarFLP0tIE1nXX1/KiS88nl3+Kfx69yMw1/xJUfcRA==", "signatures": [{"sig": "MEQCIA5FCDokYukXlq+m0FeIIN63GafuvoCXsY8LmIBifogCAiA0ciwN6ilNdrLKW6ohb23fXSrlXY85ELDOS40PL1rlxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119025, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VXCRA9TVsSAnZWagAAk5AP/14bE95gJGrnmMnO9X5n\nzeeyIPOFTu56mN2/vfHcflHyNgUYpKUzb+8Pe6wJnvuEx57Ic+p/rDll27L/\nWx1l+AqXZA1Ux1BoBWWns26sQgP32kRtWucHixcLp/x9cqNJbOu2JrTV+ATN\nsqgJniXuTPsTk0zXZAs5X0+T9v7wN3cSTIS82iDL8TuRglx1RPKlxX+16ALT\nj2VP/wUHeF/ckj36Ol0QqtsTC7JH+/mLcR2m7UXHbxxDb8md2Mpe7gIPfSaC\nTVGGRGZMC7/WwhtzGtXaTIIUu8/hrITzJnQ5cdn1WAYEALyHHAcO0ncgPuSC\nXMNzd82Ar/YQ4inzkRg42/t0rT+d8MOMSbQhAanY4VibmWdFNjXmNPaPeG+y\ns4JN4cvgqvC/YsbdLIWT7hqB6RC6jD0erC8iSFDe9s+aScRcgnNRkvq7jkhQ\nEYo+sL7ZYp9K+ByQuLORuawfGBVGdzdsX+MKzM33UHlnp3QKJiW5sXqyAZhE\nCqHToA2Uc3mku71v8anqpxinDfXJcxfiyDV73JOp9EEyxQjvGSx23XmZakFb\n8CRZXT5ifoGnfVjPD74zGVC7IUAi+5yCqEHvGB6/1mnrV9ZLfaQ4ZHE2fdSD\nkMcgHPVZwhzDuKLY9onXvXUuQhaMXlu4v8PC8vSjj7D8EwOuJcjFADXKuIbl\nT4Lq\r\n=NBzv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-slider", "version": "0.0.7", "dependencies": {"@radix-ui/number": "0.0.2", "@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-size": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-collection": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "989154838a169796553537e16ad02c3785f81d53", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-VI5x+sAyS6mlgAcgPnk9R4ubUey4xX7pf919POICBqJxzHSqWam2j/OgZUXPmbLaI9TuhT7jiJeSC/bWNQsx6w==", "signatures": [{"sig": "MEUCIQCp5OVMG6NPRhn16hwmdHvXxk4+NNL5NNkcXeLgJbEQTwIgR8gMb9s6UfFgG/2frBl3ouwdkyJwRZBUjds1EjzPCYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123884, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPCCRA9TVsSAnZWagAAIOYP/iTnt4ge0JhAiX7krcAl\nDBiBx3FYkG8Ou/QXdP7Jd9Jvct8EXCh10rrXRGd/gbY6bD/Wxhr5eaKaytSw\nemfgAlkDysKVvEJ6G8RBFf1Os3uIFPqcQIp13PLnpHRWONE6mr0RLyz8+pzy\nkuffHXO9z4Ir3+hMIdGc8di5fYVuP/n5y10rzvGdoSAEXOTuCfUx9/UNhCzR\nwD4jCUHzSSq2CaEuoniL7vQfVuVqHJbf3Od2uaXrm1dZjg0KrxWxDZrezs3U\n133MjjU2FLMtptzhF+lu7fWuxK8++n6SS5XLnAXBs19D/V9BgepnKPzysiB3\n/qdG662tpqw19xUE8ECca2ldaSXQOjzM6Hek6FRZd4Kfzku0cysH86tUDBe4\nucva4Z7h6MR85R/OxkP/KK6RgGTnUO4NxkPLTlfBU/b2NR04+RhjneRSPmvk\nOPUjojo6IFsAnlZlT2wCrFfJOFEKL4kIuBuPa/wnHmWTct2jLL2Pb4MQNrEF\nvK48R2WKtpfj0jtNVi27ou+XWlQ+/3k94eW4MYErnSr7XTVUGP7/0qFyAjeA\nmSNvg0k8DwkbSegrIzh3qrzUq24rf0InulZPeGLpGd2KfshvcGKrkUxJ6o6g\nfs0hZ8QTv0jJLS7sKRulZsVK4XSxJ6LQ5STquiPgPXnTY/D/OoRWuYfF1tKb\n+A/A\r\n=mUcL\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-slider", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-use-size": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-collection": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "aadfdb54c615c4063ab017afd3e48a2b3ecb6dd1", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-0vGjw1x3CBDWfj/Kw69IhZsED85C1wA6zxFVm63p89T3XXYktAfr9VbkQmFIwddfvtCFUwY/YQvAmg/yY74n2Q==", "signatures": [{"sig": "MEYCIQCIDSEI7oUm9MMrlaQtu9wCsqLMYlUweYLcrN1FyHoRUAIhAMUAq4bOigSU2nC/cPp3G4mW+wZErecNHLYEvi49AQxL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g3CRA9TVsSAnZWagAAEEEP/2hnpFm/1GPYLVfONaVY\nutXZoTPrV777yjt80L77MPucWLC8t0jEEXC4AoI7biKGshuZoN+ONcg/xmHZ\n+XJ6G7Mv4rXsNcQR/AEAOyozJABx4d6bjap5wrIf9lHfbpJ/3aEJ7wi0e23o\nwU3vyUwirC8bbWJWp8Kag5ywuZ8d1G+jevH4Ls4TQ1mT1Kh1MhGg3OZwqBFh\nhkpusYC6hpHIe12HO6+MBSfgDE+224g4KWpdttlVXthZddrd7PniykK0LGEq\nYuiDCZhbRdlubr392b+HWQedOupayXF/J3ngV4U9jfBd2BkzcuGYIMOfeFxU\nhEZOXyBqtrV/9NR0hImI/jRAonsqJv7w9pG096SLTps3LL+mccJJYw6av05H\nDqRk1dp5BG+jagAQSKLXI2G4Gi6EnRXQDOLYyaXyqj14LBiAEWmNTf/7F8PH\nJ4YyoWW9BUcLVvJsxzhMNauIBoYyP1yPCgZ0xP8tQrUftb1/ewkJmCtjF9ey\njCDNcntPmJVRPFYF54kIPzd0jKRUcZm3Nqi5PAK0FIwyz01ixWUfMQT6BJ6M\nk7kyAY0QSb79SJvqSd/Yu3WZ134vrpv7mUlLWhbiSU5aWJ0Z6Qg81BAzR3Rm\nEy8SshegqEM5ZlWTyYMaEwlpRFOg/HeXRSwdOnhw+rvar5N09aP6LvWLGB1J\nFowq\r\n=7HUT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-slider", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-use-size": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-collection": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-callback-ref": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a2392a7b3b660040d0e40d712b0f32dc3c455ed3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-LRDq/EytV5Gc9H88zX09aBr/N9+OF2xGvVJyjZPHp98k4beCnl0HvSi+VfUyJIRHTS3Y8XdQX5QCOwzaM0JwWQ==", "signatures": [{"sig": "MEUCIBCNempasFzbwPWad579xX624XowJL5X9mBtWsZFNW34AiEArc6CSwXqTDhWKzaGJZCbRb6v2SN+1uhlWP6sRxnU9lk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H/CRA9TVsSAnZWagAAsfwP/Rm/NV3Za7fL8cYwFoLg\nK4tAc08T1kH7sYgLJf/rCfL/umUtW/BHxyUFbw44l4bVGcPE6ExtPul8PcUt\nKza8gcDZ5KcNrjd4eQCQR5Osub/OrX2yKiy6TbpuXcMxwf/8pi0UkBY0GiU5\nktGKEha0MLawFYyC6QR4H84tGO02p2N0So8diOD9WnWseNUlZqGqx7dbp+Fl\nMAg0TfggzYb84H3EY0GKehmkqFthQyoMXVSmyr+TpZzYITCGLeBoyt1VGFeV\nBuYxq7mB3tSBCursJpdqw6ga5lqijWZ7lfuxUowLbsRpOyc1y5yHbVZVwDUM\ng4u3CMAPWKMXM6aHH7PSiOW9otFSQ1MQIH5oRdTjoKjZ7Q2/8aA0NCZcgdTh\nnyH376SitfE5IyHUNP1t8zs0m33ujMf2+kqXTK1tDIjm1VViavqpY6eop+Ba\nX7d7myDCApDLfyfPukNn5eoBnrxy3kS+hQ5u3BjtXVwYzBYc5WkCN5hYLvEz\nnyIBekyqCF+Rx38aYPbRUFoRj0BhrB66wBrUbpDzcVZP9LOFWx/gSyLTmGde\neA9KlPXDipa7bsiFGhHaLXbkxh4//CeU9TLNK/hIXOumvvK3o8uBY4jRr96k\nTA5TvNiUY4K8cbqCqtDQjnwTSTEPr6vQSD/6mT44BHlYBxV9HhIvzplNevOF\ny8FL\r\n=vSdZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-slider", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-use-size": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-collection": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-callback-ref": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ecbded0f34fd43a1954361a6b65df2d2cb6db748", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-R+fY7eeGNzqQlUrLjVSUMvKip7Vrl3OkofyEhDqVenGNcYNJsmIe5cF5nwfBahBLKKy0Ub6K5XSNhZdE9UwoIQ==", "signatures": [{"sig": "MEYCIQDBun6DqhkQglmt9gS01zKQk/uWyv+8K4cpiaP/ULIbCQIhAMQJ0sMAA7xyuXZMeTCODKylUvz5/j4yObfDLkxYYmXD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112056, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v8CRA9TVsSAnZWagAA2kwP/1aNiZSaH/YO4dIybiJs\nxg5Hz/97noC+HuCU6OXNVvhji/JJK2P9cpQELU9JxUDrrYzToyzceEKdFHnO\nIrzlFrW6QTNhTic0HTww68P9BxgzA6w4/9YkERSN6FoMbVqhdd2K+EYYFbNU\n6Z9TdK7uulvI1b8DA82KbOD3V0f66k/EbVjURfpeHl09xxnRVgtRDr3JXRwZ\nJ5RX09kuIjSxqgyg9CCrAiXTcyeY4eGbTN/z5KgjKKbdMxip5VCtQH5rwxGM\n5zF81VeGycISMYk79xKPaAz+gXxRTMFLjpANbeixZuoa+S4ESqrK+uRX1Mg3\nRLr9jwkSiwXJFspIcXYjRCHAgXdtl6OSwbqiULPcYv81T7dTN6JArGNOzSVK\nEfhU46julAzaik1qqeWvNvmbifV7KnMZ2MlVLQ2Nj5d/Vkl2F35uyRfCmEEZ\nwR3ha46h/volnAx0do3hEQYsUGJuQWBDCq84D09HRfMHPrEJTDpVQL2tc34n\nOdBUm8hHJ1pCRJ6lwi/vepg37Px65lFVp8gVHhFLDeYj2HOv4XJrfKWZqiJM\n6EeKh1N3ucUyZE3T5+qkUxNqDaKV9e7RAxFn4Gksev9WsvYugOCCm/PQ9NV/\nQmzNYR0CWkSUPE8v87EoO9UIq2QT7/W+y1X9M3FQXT+NkCMQ8mJNk9pkVh6v\nwNCF\r\n=KQfb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-slider", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-collection": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "21d7b07ac7d9ce9ef554fb9fca3ea92964f850a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-c/jqYPtZdGcIkSoT2ruD4EDXBVCuWpeUS0CWb21aeVS74q0EkFB4FePExOsNnUruiAy8eRG1hA929GyZqPkyDw==", "signatures": [{"sig": "MEYCIQDk2qIEXPHOeJwSjz/SPjGaj54Qu/Im89HveClzpWbv9gIhANVJyvxHOwfXB/9HwQ4CvcRmtcgpzrQeNLThTvB92tuX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112336, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmvCRA9TVsSAnZWagAAJyQP/1r99QkfpwPb/POCmawE\nLnTfDelEL/TVuH8XMjzCRfGEdsKK8FovthgDd96jwVnzwiX98zJaSbFxv8x7\nhHLSC94p+EKdGmOJZ0q12HJ+Jew9f7e6N6nakbpNesgzlOZi91Ivsfydm8Qk\nyzCV5RTDis2T+wOEJJiwhVUl8BUhqHC/5iFvuXJSK+o8kHb1lgePgNKNIkEu\n5iEVG+zgKQ1QYBns3xILP617BeKprdoNx5c0HhODS0Cz6TaSIm5aDR7FuNpm\nuTxfRmDihAPxLCeHKrdu+owOL46yhoGZlmM9QpopXaO33UQ3UHoVZ1hQP3mS\nttcql2/LomBMnwLPhE7o5NRkfYpaCm/k7EQi/Xz1LplIDXH2oDizYWbvKKkC\nWOkiHs1b00omBcfkisTYB7mfKGPAjWw47II2eDSI55ZR/XBuE3YgwRw91TEb\nHxpBoxGL7WYaVuATbxmz2W/WamW7t59avo02RaVVKjSJuiIhQeguvf4vl+/d\neNgi08AQFAZoHJ0OOmOHLyRWfSvaaRqRYa2l9hCJ2S+wZnl743eaHcTLGwcE\nDouFnx3d+E3D6Px8GH3AwjldIU1RVfjvoa0O0THRmyuVwWmpTaYZI9a1KwGP\nPsBLqesw0uf0T1cT5qJBRlRQSa+PFy56IkvfQu0EssZ99sGZcAcDTLNJHO0k\n3GF7\r\n=KRxx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-slider", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2fd67cbe61867ecaa802e6b930d7353a28ed6f6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-ECqdbN6BTcdP+iJjedhkxmeYy3uKXuZqPV9+3VtauW0uv8aDPD/2hQn7dCq7IGx5TOhBcFbmVVM/d84wyyP38A==", "signatures": [{"sig": "MEYCIQDYFQQzvRvGD5STaISqqKmJgrbM1TABOMM4n2H4NJm+igIhAMQLpmIHkUzZ9qF9UcaMVP/9/EQX3uSKM2RyAEgA+oZ2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7YCRA9TVsSAnZWagAA7swP/jAD9LiZkBfP5+9vEsk5\nzaquq/X5uEHt8tqJT9GnBrxMUbJhUxr1TdjzgvgNBWv4lrR4r1v1vaQY7bme\nuXv1RCB+rAqA2cFkrnvaDVOaOuG+BUzb+myLko7Br7Ct6Prqkl6fEsd+QBZ6\nvuD5vVrmDSlIR9B2J9LY1aT36/rco6rGcxIOcViLBID39FzmPNQxLu5sOoiA\nE5Uf2unxembCkK39BrJOqD0JDa/fxIcbA4ExYa5p1/i/EZO18Y+AOJ+B24bK\nm8yjmv20uhYwmD6Tt6MBA9K7/pc1z4jvvzOAYcsLqGNNPcMcFB+dPlHSaULd\niFjCw31UcoKPWPoKbO5W7FzXWPK2jj+TlaZU+IIBCxKZMSoVf1fOx7LAlCqZ\nXZOMe6rK3FrJZ9PLRNG+SRu60ZaqLwpPhvCf5B/3J2bjUIxA/5+O76c9Yeug\nmPyTs2LCGk+vkyK58GDQ3rKFu3vxDk4HyTo4P3hjraHbke7NgG7Sp9lnZhMH\nkLxPyzXBIVwDAne6k84EhWiunHBqxxZ5ncvmN+OxOWgoHpJgG2OAVpzGKlkX\n++WuhifTFozze58LThvy0IhXkQs9iA4xx0XwZxGFo46LinjFoOX666gn43U8\ncXpbjX82OxhhkP1a1pQpxFeSfHnU8PdLpMCmYnbFjM6aCgF68i120mO5iNAM\nhY7m\r\n=kxMZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-slider", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e62361ad33b786926ccdef9a0c9adf5aaf64e4da", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-Ft8L3kJ/W6obKzC19OyPu9R/y52KP5LSPKT2SnQv+46exP4kZJK/yI5M4gst5LIRgijjWNE+THpHFL/8b4EY3g==", "signatures": [{"sig": "MEUCIEXXe4Az4FPm0RgljyYs8xfGc1T4XGpTtw3uDq3vzrCiAiEAt4LfwBelCe/D3ckaS0GJSsgOEl8IpN7hOi6IGZeA7fE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127558, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYFCRA9TVsSAnZWagAACSwP/iNa2OF5Cb0YevAe2lU/\nC8xizEyzHZk1/DaLWJIcPKzzeIF8IGNsH3SdzxYxFJqtx9V73ZXsj7/9eBSJ\nRH5jDRqSo8x3YuhNq12B90TfrqDmLwknK0AbJb1VlhLj1hgZhqbv6Dy4b89Z\nL8oChytAdW4e8lFl2iLqhupdSGb3cJ/UJLyP6S1INqfYsnw5HgLstkkrimsC\n9QYTjxkeHwx6Ws4BvJdx6UOaSzE7BKCEz/nVNQxI45oDnBPvpJyrNtba1sZ4\n9eS8BGJFsyP7gMDSli71cycN2xIJ0o+C0WW97suQv+tguxa6na2y3XMfnNVP\nOXv8zY/wv7UinXvvUs9xV2ASnfltmF+IqRO4/ArZipaLaf38Z4AZm02dCT0K\nLI1wOLQ47Xaq4d995TPU/oZfnDeFZMjpKjeqh/5FMopJdIy045iix3QY37LY\nncoOnCWh1fd/ljCW/wNtTXUM3OzHKXUzwPjDwWNuzD2z2ytXQCWxtvnucZj+\nLgWM820WNE3YI6ETb4F5orEfwztLdlLEqZp+BdLOewUXje7rhh77dHVWlEG8\nFRse9Ovu4TjQujzLPRCky5K3neG6MQU7LttgiJdW064bspMsPzqWQ8SYoTTS\nJe08dmi4OvpAU3w5olQprX+9ky6Da37nv8S6mcLD82nASrpe7EM2k6xIjx5M\nVPHF\r\n=zykp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-slider", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0d7b03ca6bbdb50300b09a51ad7f69a09bbb89fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-DIgH1yUzVR5WLpqd8t1BcHY1gAc4gvlzWdl5Y7zlB4sB82SVNB1o9SMPa5NE7wZ60SH/OAZf/WLHRgyIWy0W9Q==", "signatures": [{"sig": "MEUCIDcRo8vH38ApRe++c9d20Kedz8WUvkxUUF73mb404EzaAiEAyDFs3pETybZIiwWhfiYNYv1X3KEguC8Nm7vsgeJKd70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125098, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ91CRA9TVsSAnZWagAAatgQAJHf4PgSpX2e8+29kLlm\nIlwhbnQldkGcTM8+vCrOFeiC4G1PK06G8D7UOKxUT8gHYLb5l/e17E4lPUZk\ntzVyjvElYyb2fustFPj3by0SyP4sTY9UjKG01U52xCsnb6GkrJ9qFrXdSo1X\npqz5UNcPasFZZbvLH5Wv47mOygEWV5G4GywwL2Rr2iSWDu73bT2VDkHl+ACL\nmv95ozev+MI/DqTqjW6D91okWcf6mKy9ogMhrtjh8StVhg9o7YaiSWIx4FAG\nSZgY2bCs2eGIvfWLV5EMO2/xWjUbfVdTLHhG8f/zSLImAch7i/4yZA08QbHr\nUY5zEfix2HWzaUXjqJa2p6NwxXp/Hm/eTxU5Y2yTrHbMTgSORtZCSYj6N4JO\nwDj6qm/w4+CzMCSu3OEy7g9IfmlwWrrrFopj4a+bQEPf6p1K0ue4ZGKskKa3\nLW2xgxRaghMzi6Vqp9P2M5UjU8+saGc4QzRmDBDhCl/w28NxSwkDb3+ixRPu\nzJtPv/BmOl2sfdt9K0ckr+pF8gaDkfe6YWfR/cjMfeJhwa3HbYQeRmD0XTik\nZX49j4bAcLgLusn0/aM/6JQWD9/R2xwdQgA6szcRQZVJpxXe5fqQbGJOIpDO\nL6o7J0bcLoCOnQYIeWS/rVHDrV/7NwyfNxFeU2tyOqx0QsVyh4RMo3Wb7vkK\n14ls\r\n=x/ME\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-slider", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d2d5a0a271b94941b6181e2389dd7b795317e474", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-TpFQR6zEg7h2c9UQ0ZZomZu0/SPtfKWMDLZ9OSIRPhC8xuaoTYCCHxYDWKvB+wjoqhZh1XDQFeGU0fLIFvV1kg==", "signatures": [{"sig": "MEUCIQDbJMMhNH1AhIc+gyuLiO9paHERiiRIo5x0ZHA+5GqnqQIgQMnY0njhrf9U7aktoqowcg8XeKPfpEtl8yPU3LbMDz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 125106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyhyzCRA9TVsSAnZWagAAthwQAJZGELGGT7YPwj8o+RJi\ntvmiNJ4idDzfzJwCdwOGA8lfHPbgXUCmBSbBQNO/5mXxWH6o50lydHt5VD4d\npxylTbveqtgnuOlLNPe2UEcWcAj6yb4/dCGDh9NvzobflvSU3mCq9nFXz+Bg\nLgJsHiOmSwd46zs6jlqNxYFDyA6HG5AT5P89Oi7geyRm4hSlCyabPciq3aIR\nke3GBSFKQ5bGAwyaEH1svzyA/FbP8IgNWBpIZVJE9uN6w9oI5WkrEO6PJ6kv\nQyNs/J17CQYx0NDb2FqpWd7XV7h71m05b3tUmytGsF+2KYFue0/2kbolhnNl\nI2AJ0r8JjgC7BKLrfX9Tur6jEOoNPtnLUfjGNoNkFJHJcP/4K667I6CgiQXA\n7aQObkIJPD5bITuTeSuNSTqtBZj0bKI8Rx6CIP/kh6vYaGl0OI/xMf2bevsa\nIeaSkjfwTs3kN7GfLNpD9eol2ivylCNNS4NHXW5c9X5ZJDSFlldzisAUWRMt\nm7tC5sQCE3qHHKXCc1CP0i6aa/xOeHRFc/Wc/dAcxnSvewNR2d9JSMQzT9l0\n/bNu6taCRiHyY9g/Enltw4iqbnBtQTa1ssT3lHfruaFLaLQepdYcx49vsE5S\nmUM1QSuBAj5Lqf2ImyebntrWOnN08+nivjAOL5CK0/LlYKIyFvZjz2VDuIvG\niZyJ\r\n=DK5b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-slider", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "014fe1a054e058781e180544a79a37050e07eb8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>rc<PERSON>3lIFbsYDmo5MMNjH8AO14U6NdNGqbbFhdHQD6FNu0tQTg2PlCt7tD9QTwtZ7Gue5n/5ADp4r5m67RXIaA==", "signatures": [{"sig": "MEUCIQCF8dKiR2E99G1ANawOgSt2lxDv6pqUZOTWzZA6ioQNwQIgKZcLna1N4S4gjIpZ4JBMMvlnvtnCT0zVq4jacF1PCIM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GU9CRA9TVsSAnZWagAArW0P/2mrcva8YL9Zg3axZPpF\nsJqn0Ouu6dSsBkbAn1A7k7qlTMFQeOvRBkGcqFBCU/DxE4mxUwHjn3aZ+SVV\niKisbvVPnQ6NXygy+7lq1sGaFfho8afjrowwUpS5wjP8Y1/4XDnZqlaE5p4F\n4iQ6AEtj5NOhzGUusOW7C3eYSHX8Q0DaQQmZ8ZcnuyncVzMwWm4aE/qEeNDq\ngNGh6txbIhOS32/ZKGH5b6InA8n0ATT/l27TVbUpdZmtWdRB9USpG3PUdmw5\niCuHcHShB0Tk52iZ1PHhUW4lEhEJx1vK6zldAQV28KjcqpDaAuwvz9yze0O4\nsMIc7wbVJOvIw4x16LjjIhfBe5RRgQkMUZYkWaPG3LuC8WGt4yDGKO+Zp8uZ\nNgTybZBsRj1L6N/OWIpCFBq6jpebbdydPqgddjwUELlozDQMTcpFSlZu/4kR\nTv6KffvGVPia8SJz6m60LlYBLw9RC6pVNC0D6kfDZfmCLnMc0j4c7W23TKvw\nEKEeTIOiQf5T5SZpVTYUYp+Z4/cqPAgi20Kfv3vLAq3akgIXvNPHn4u2jYGP\nAFKdZvs+96phSQlqaNBYJ/dWV3u/mzw8BtXIQW2cv4briN8yB0KUf8V1pQFr\njDabnichQ/U9ceW3aB0okUkEbDJ3KDR0JdoGfDJd1ghsPoSNMu8tK0LxHYzY\nGU08\r\n=gLun\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-slider", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-collection": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "14cc1bb89ff189fb082bc9efda8fe6161e491d6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.17.tgz", "fileCount": 8, "integrity": "sha512-bYu+SuHnQnPCx7oOsMIujPPqgRi83PF7rVQ6/bRaLUjbJutHB8Arye8KhcvW/OUcqMqWllt0Jwp5aV6akM+ukA==", "signatures": [{"sig": "MEUCIHH1cR/lVjqy+ZdS5U+azPsk10rMymj5hmU0QHLd4wTmAiEA4a8fkNNiWrtQV8EJsWTUUNnnJmoRU+70EdoQzqoRi2I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTyCRA9TVsSAnZWagAA9iIP/i2mDziM75SB1zV0QJer\nWmWFSkQ4qgJ5eQ+PMF772t4IxUMsekKUo4eWXeh8qO+ooM+P/hTM6uq3rfko\nIpja2DbBCkhZQzm8UgFtyJrBID9pdOZsGXxM0WI8sjSNccPWEbq/2aWSisFs\n2enSYHyH0nioOvwuHDLF4C0/oGZsT4bpJXJ1goo3N7Tkh8yThcqsdP7sRZB0\nibU868D2+3kFoA6rxV9tVhlX+C9jgmB4pVtutX1c5RyDHeFZorbp8L56F+zt\nWzfSuOYm0o1dDItdUE01MfPMVu21u5cAUqG52EnlmfBtZzbWMuJAuxy3XekZ\nREymK5QfM/EY2YO2SKJhhbnLMaS0N07IBfoGbDXuVURuqKI4d7TcJhPxt18K\nUj+LkYgRn9s7X1N8kSYku493v/n6XYzMjgneqjt/ev4s/88/1mc9h5f1POma\n0U7HRUlANTpULb5GPCTtFbDeu94GVzhkLRQTMrcz6B1bS4ACah81kJBZuRJO\n1mmcD+gKPFFO1ZK+K/FEe8YkhjpmY4bQXL2embgNA7sbzdy/pDp1lMT1cLi5\nI175punRtSJ0+z+klAiH3Yvp0jrKUVr/ptVZcAwhz2ucS/G0dkO1nHqRoCZH\nMhhIyZO85QiDCykKdhs0xP6YpZyr0MEqMnriTKIkeDdlcjNdhYICTH/RtWnR\nq2x2\r\n=uZS+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0-rc.1", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-use-size": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-collection": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-previous": "0.1.0-rc.1", "@radix-ui/react-use-direction": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5976807d004231c50517986c92c6a92b2cffe4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-Fh7Kp494Oj6xgaIbjk9Gs9eqtK/Xi72X47UTUoOo5GWUznK00CLB8eYPa057ykseW2VnMvHqZdgljvonTKCTIA==", "signatures": [{"sig": "MEUCIHY13NC+5jZyJDFICl27bPFkOYPBKB2xqAe/UOROmyObAiEAowG7+B1ENV6Cr8/02+C+ltE8s10YihW62OZ34nbycgY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1590, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgppCRA9TVsSAnZWagAArQwQAJrT5MuT8DvfB3fVkRfj\n9AkYL3Zd+BUQddTyrBDJvdXKg7RpnDQDbuF82mC4f45E1TMzZ10Xa1iRqfzJ\nT0AXqJHMG7RSmGne2/tUGUR7JexARQ1IwM1zp7RNq/jShfhSTySfMG9Gu9jY\nJ6cpqhvkTXOseOaGl8NYuQUJ0+JvsCHASkue2OL9gj8aSMCHqGDl3VZncb0Q\nA07KVtZReLgekBiAC85nfm22hwPcbYneWx8dY1/d06Szcn419C7jFOAHnt/e\nLKstDJ8J39GFOJOhWKaLu6KZcUvmdedPhf+tYSNLj6fbNM7MF+UQ2E32dduJ\nuieVFYLmTaEzIhQ1n4sAuVDagAjTKDV1jPNntiQrpjF7qJ5dDcvbbxBwQ9m6\nIo2Ed8akmoZ1BvaJm6D/9o7JGQSqVzZv1R9nHuLrzC+aqUMseO+vSlWiBpi+\nCOc6A/9QEdqjFBsrnOUKjAgBRf4knTs0c0wPqdfDKSu5e9N+l+m/GdWI4mqk\nr87iUm1fmZ3lBU6UdoLudzQzwPU/BkDPdl1Nic1viJR6JQvl4pmaLl7eVFrm\ne42PoKn8KoqmB6263YAqqX5II6okAOp3wNfeSX8ONp0QgCsMwInjc5YiwVN+\n2bZU3R/Poq8RkhKypysoCMCwBuYsJYVTWLK+v6J3cF5f8btm7QvulerZMDij\nxvn/\r\n=5X0p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-slider", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0-rc.2", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-use-size": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-collection": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-previous": "0.1.0-rc.2", "@radix-ui/react-use-direction": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "19cadf500a3d0a4d2ffaa55bcd7adb7e2617fd0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-J51ZWQmJmo8UuElJbTt8er5CXG4ECn6yHI59t+Yh1F1QEkDkjnjkfOZRO4PJmEhQBjO3Gm6RC0YS/zbGZmY96A==", "signatures": [{"sig": "MEUCIFfA7jXUmtN2bOlGLCP9VqHbx3i9TkZbBsaXZVW3m/eZAiEA3enzkcrUXgxUcInWmK3YbAfeu/h2iY05lmKo4Zdkdoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhydCRA9TVsSAnZWagAAw5AP/30HbPVOpu1qLULs4DiS\nbZTTpYUdBmeFv9re7kwoxADSxMI4YIBIKD91JzXeLnFeazz2tbWf4s5PTgd5\nM6ptD+5wxKFZv+n4eZ63hypjSq9IP7oaQmOgcEQ52XEYVgLi4MvqxoMQJ784\nVZQ3dYQAnqOYeFLVvKlqTPAF42EogZzKKpO0V6WW+r/nKeVCE3+D3yC4iiWn\nF1aPrv8OISYj/RF6UBCA9URYHZMjP01f+S8aEXuAloKU9ht9plsTaBlae5Ph\nJxRsM/MkWgtpBilcue2hl65KH01Xohqt9Z6Jq91zI6XuDLEnXGcwrtadUWpB\nmDsAHYQf4/AyZSOC1gheNzrr+qdhwBqT0v0NqIcorbybEFrX2pJCM/qeImfp\n14ckNKC6yT7amLN5xiutMX7jZem+t7//pLefnRsV6VhPzqWQC7AcsA2S/BKw\nAsP3t2fHBe2mXyyc9iAaF8sJN2ZHIbVRMf29Hq0AddbI8pvoOqhoDS57ZC3z\nRxeYLzNNiEi8vAiN6v7eFTAkFBwWJa4xCbpyEzEpcSsfaoi3m2cnq27yQI+Q\nzyY1yY9/2juAy5bAYvOdC/y9tUd9wbt9Ny/0IokJ5rLyTPQlaz+KZU194cmx\nwuc3ih6r/NO8Q+WOROOB20TXZmNS8CxdSRvw5auxAufxxExhRDhVXJTW/TNE\n3Pd5\r\n=PQnr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-slider", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-collection": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a94d3a1a5d8570c78ee88615b48aa4fcfefe13d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-igzRMqgauyT3pxF6nSdKL0L31BmSR/J+gt+jvz8wqYdftkGTrGKuJgFWwPoso7z6VJ6Oc8izdXN+Z3S/8QB3Lg==", "signatures": [{"sig": "MEUCIAPGgVIANmgI03So7AiubzBrMARsX3Puiu2g0/yMYQZUAiEAq2O+e3JWW406q4Q8kWQhJmvAEB3nl6hIDNWaSXGEaZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmoCRA9TVsSAnZWagAA9zYP/As/x5zEqTQlTvS9IDnN\nJYoFsoOh2ol4bP9kAerxmO+SyhUXgknKZX5hChmJuV8JOJdONRbhUOG8D55N\nPoTbqwlLkmb9M6hmE9CXW6U6EtfqgTRqnD+MOuQ2sE6Qo5UgTufWXratmCWx\npNd6lT8DvwwjT3Cr0xZGbE9Q4U7uw5GARfWcoYEUuTSbCvsfzE4FypeAJYKt\nOmuR5ON3X3VHIItPnp92Thc249CNpDwCwEtEpZcYsc8/hf4QCQ1CyeFksB9/\njjqG2wwKKaTGSVfK+gU+xQsFdSNl/LnTrIultfPXl2Prc4Z3OS7p3KDntIBY\nn7GfhTZTMNAm3lWwcUFRL0OC9gdi3Z5RtPNLBAfLN5Shh8q1xBf0Id58mEDW\nakvlTh/X26UWAhYWUmtXQNRQrulRZtNsxA1s9d54/d/K9VRES5h1jTl1aOzG\n0AB1RdjcBPGku/lYIqZ4exzLiqtpA5kG3P7Bn0rm8za7sc61TWo37kEFBMlX\njunDbrnvW6LqgDjG58cMA1Oo/RF/q6FHpHDpvNRuxarRgPwtcAdAAAkQ23XA\nE65mMVk1bNB7mayEbYCPyHz7C3ud4FdzWYi902rT8BZLrwe1yaJLURDJJeZ7\nV8KzeEn8JDmNQa8g5YSCbncQ1uLh+1gHeUjYaBuvuWCepXoYTsKqE403tg/H\ng88J\r\n=As6X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-collection": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b30357e63ab3f8752022f4c174f61a49e6e49712", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8LwFQX8XUYHNzzPk2w+vWCzi/g/Io619RoLWSZ96vHZiiCMVJ7+i03yw9r/+wrXpa1/wAOm4tm8/i5f5wBULaw==", "signatures": [{"sig": "MEUCIGA73K2EfIO7g6pmpasYp1tqzBV/BRJUFdf3b/pxwSgLAiEA97GMD6tYSLXfrM46ZjPS3+cN9W8BeKhpeVojpgwQG2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInwCRA9TVsSAnZWagAAjGAQAJK6fMJ7g8kwVyPq6P2T\nf5Qfl1f/NCUXPkBQoLCnMZwbCAY72bPs3RQghltZ4uOsNLFzYKWWbU3G1HOZ\n3yD86qQBVaXDr7u59zjTo1G9VqeZ+btgcOS0ZR3MW83xKOYlr46AgQo3ZITB\n5GOAkCdqE7RrSb3Hk/52infwhZi1dNMLMpwVXdMK3wWnNvO9KV9v0NQRcYeB\nqyqhqkkkeO7pv5aa7UGjUyQeHWIK/gzm8qqfCD/99IQHhruXy+AoeFwMRILX\nxa9RCw04Ymf4y6EAZPDFqXsJiFB4Qc9S1l/BTtRPpD4Z+XvYYGYQtUQH/uq5\nmfD48D0Z+1ViMZwS/iR+dn8HZWXgTQKLlpTr5l2mPH1GztcjB7TdZfuKthLM\nFVaQjfmf8qSe/BDJvLOd6mkcx0XdaEavpvfxyquAZgfUxiKikrn1Xi/YMSCt\nDzFWK/RRmBHWAQLRQva4t3EA8Eu8p5jwllRBiCaeVjdSCkzNmzr7A08AjxKX\niJV0sT6PyNK78dFL5U+G+OdMfw78wOlnG7GLSRurKmSjrk7H6tmRYwb2JQem\nyLJF8VpVNfi2gg5jzrb9GujsJ+TtILkDssg3CPtM0aQWhfCQ7EunWiIbZQJ2\ng6UVrFoErMFcxwOrcjtaeTUxrQ4VSWx6KLl35ruewGFRDwVKaZULDW99fiv0\nnmiH\r\n=nedG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-collection": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "707451f83edb518cbba4da95e2083de269327a12", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-odjQoMP2nw2N35nAPTGt16KNYvs7vmZQgszVg23ESReHr4sNMWSsFLkYVVVTg5J5kD0kXs2GY/9pFl6nHY5e6g==", "signatures": [{"sig": "MEYCIQC0/Y/ykj5FJXryasW6tNYsJOggM46yDg57X9E6rINisgIhAP19KIF/9/uUxxOI6FDRw0kSQA0ic+L6py/gI6fJm2od", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98748, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwfCRA9TVsSAnZWagAA2pAP/1S/c1GBW2N5QSKH4GLx\nOrJOtvfhFjRToBd+fbA5dDjZ5JBvo+pf5Red/AYD5XyPe6CCtdexP6cADvnt\nJAaVrlhlUuxDL6JivDDINmt92ZlJVkwjJnmAPpyJrrbaNO+64u0C2uFC2jR/\n8dXZ2IQxJ9/8U22OiM4iJVjcO1j+4O0vsjUDC0w0guOTofAwU6saPIHxlraC\n7b6xM7Z/YiueHear1SKsRgM8ENAue0Y+BVaEQh4XkoSiEjYFdoDFStX5VpBf\n/oMBkMZt1FO2sMxt0Ah0MqAN2F7i4vFzzj7d0sS12u1R1Jhr5818g0azz7PZ\nMcF3k+2n9Q9kqD8LBczFtSBrQBgHzPcZ02Pi2B3PWe/Ni+sMFtZcxeVsh3GT\n6u3NaWQWmGyQvEavamVmfpOG+PKtO34c4Wcqo6AZ3jlGJuVxABN8z9qa3mNL\nzY5kDDBQLYR6Ur9oG9TTRExbNGVEeu8DNpuRKqt13/YHV0KVrmLqjWXIKYyT\nmkrL2osddg3E+xfMmSx/i4jGYTiLqBkIozYR23xhiMVJ1WkmkmG9YfukVXzM\nOBxCqGbA3Bp0wYVFiSlIrQJFcLulCmIF/cmtQxzwjiaEjhw1grJNkoMb6Gl4\nE5sLx2jqsuJeZO8mDYMnjyv6VQZkd5+VRiN4kiFuruq7d2NfaDIb8Khj7Wgz\ng2pn\r\n=l9az\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-collection": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d1c60656814163d7238f695c8075be1128faac51", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-BfOQxv8UuY8MR3tIU83h+1h2f12oe37BK9A9Z7aWZktMLd2CIoqSASTxmeMpx9ZQgzjx5mmkBxbsRSeEYyXQ4g==", "signatures": [{"sig": "MEYCIQDZ58rd0I6B4rwBhtCl+qBtAw3SfJqlEm8ZQZsJoeuPKgIhAJWfnlMU6MZfYkYiNtdLTYeIixG0CQbui6ManyvjakBs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UfCRA9TVsSAnZWagAAZLcP+wdft3N0EMEfsGWWDlxE\nhXX+X/Qug11dz/D3lyMbDOvrNiMaIBICoDdSM0PheM4gm71QSV+0Ch/aGsDX\nk2KAJLJ2CEf+yeVmd03isyRWJJmHebX361ah0wee8qwffN2LFOX0UGP/gfZA\nyfBogYtTPnS4XKYTouuqls2W/XfpuONkJJjyncgmtVBj4/W8jLEORpesfwTA\nWWi6l90YIkjmY9cYc3ttLXh0KrIW9RyBJf1Sy9ibCcJWAPo+M5M9H5aJorsh\nk00k1ibSOU9W4s91uC/TOjP/saZ607mqgRHIiWmJuUbIi8zgW7PRNWaUspUc\nDejib1Fl9cULmY59ES8LhyyUUNHDZrHymqC9r5NEL5rHaup/vibG8iBlu7IQ\nrRhvdk6KWwdr5Q3R6jgPv+Rsun2QcDnXYq/BWjeaYAQVX5ctETRJLaZUz/eF\n46HipbMW1we9yQ39ir4cL7pmr3JYj5Vd32IALYD9xfgRkdesMaWkw10/662P\nwVh5TExg3sg9P3WQp0WCrUDZOOMi/x9ss3JAisVG0JJ6BjZ/f36o8MLm0Ff1\nCNc7IhKd0brapIENyWGvp59uvhPnJOXjBGqoi4DruOWTH172SjHqnYhV4WqB\nGPwG6PIIv1FmDBX4RBhqXGIOrSV7PZQMbgGE+YfrMUV2M63dSrNHhxyNsg7y\nG2c9\r\n=ml2J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-collection": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b88a29a62d070b6031860ce43025f59b1268a444", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-INF8U3T5lqP2dHmG77ewJpPVX2oDrkO+wdlbLcZlKxSPv+UVDbJakSaLQVgn92usCw9IYRmASIa3JkEJA8cocw==", "signatures": [{"sig": "MEUCICJzBMumSGbYeOkDEymPeEngEaTXYbJQehD+/9pB/tCVAiEArZyJumeZ434ItAQ7KV0K7FpfbGZfcndCYoZoyvAgTTA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10WCRA9TVsSAnZWagAACYsQAJxacgq2tgCOaQRD9vOq\npQq92i/LvxG1hCsVz52Dy/qg3lTYo6zT6Bobm/0yiFcs9QuIorAslZUCgA8g\nZKysRNZ4a7QSvhsjJGK3VhkeWztV+JduPUOfsNm/rydDCXp+EJfNaokwk3kg\ncO+11T/xVoWmaRUFZEWt4yLHKikgnmfePNcPjtFE4wjE2tnBAbFehpPag+9v\nHIYF+ridPFkbWbjXMfqukSSksKbaZfSf4AcDqtsfP1dtxw1zXhHvBqcVUFbU\n2cadbhyJOhHcLFZF/RPUlfYMgLY8yZA6WczFo19aZkcO0vPyKFZRjVh22Zhe\naE5vUp691EWcpfW9D2Y+9Eoy47y03BSqW9lsKe7ZEVOzla/jbf74KFjveMPw\ngKqXuh8nH2JKu7gF/is+3l86G6ZHO+bnjLtfdYSWz50l4NGydCS+6TyVsAkq\nf51R7vSqoiuLfKVENDYizbY9qc0QwC1KZt12dp5txfLo8aA5Imsu8XFfMpnY\nf8U/CWPP4J4Vg/rxJeD/CiCeUdtFUOLN91XNwXByvumFUrctdTcVj2H+MmLF\niGCe09sAFPQePqjN+WAIPxl2y8CAjXVYKsZOM/QPBH7AmppaDYchcBm/nYul\nOdXhpb903DqbQKq0+KWTTBOESprqqvmGnuYhU1tdvgdEWApCKPE33frUGdw5\nV2Zk\r\n=7YWP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-collection": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d5d00d48d6f07e4c7a6d47fccbcce080ead14337", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-QddaYkdtrtlay7cB+VlYAktsqcaTjmca1KFPlAB6Fbl01bECVconQX+oZm3+Dih3XPfRAGkUlH3cJmTspvNTig==", "signatures": [{"sig": "MEYCIQD4AVvgunBdKu+sWZ0YJ8ccxwY+vlWu22Ke2sOGKEuyPgIhAM0Puviz9o6uNZNjJrydXsqo53JNXGrnx50x8n97z1Ai", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkqCRA9TVsSAnZWagAAvqEP/2zIacn66Uzm/8j+ysNb\n3Tbrs96E7/zXx31xDel4wQ6b+2O6QErCNjwgG79GObZ0VBr1S4GSlPEYQslU\nfWUl0WNRUHlK//eakhfxZyjUcmvLJMFnX8rj20KK8fKoVEPEpfpCL0/TSiu9\nXATS8VCb20/XSqj3GwymoP3s8A814XuwGUBOU9ZhQp/oaqQPau1Ndfb+A10L\nW1lPG1qR9oF+oD+J5b5zlFDKR8ZAgJ+45p1Y+lttcd6jyXxnJXE74J7iB1ZV\ndboZba9Ec47+EtgKsuU4TcdnPqy/xbBZSnZesy64p+GxDLa/HOqBsyXtikmJ\nxAOj8hXmDhvrje62THp7TBVTNoHHWVF5/JPsiAVBZSKb5KOJU9Rw5QMoOh5O\nhBBfRYtZGJRNxVsbhKvFOkkZccpE3kE/Foz6CpYsXV+R4ULLVNwCa9LMqahQ\non0Qxx8jyav6vn+8bZJPRZSQSFIb6hi9TeDEQGKftbX2iYfPrmczID1Iuw9W\ncGDihYNEqBTe9cmJkFqGA0bthZLLy042Hl1yBCY99BmNhlQtOYDAovYJYdgn\nILVRnmUr0hfY+tEY0qVn+Y+aACQQVBN+7hzLBPcUxpwq0XIUZzbIVjoc1UgE\nFUyyszwTTMm6zXBMbjh+a/P9VeDxMTqVt8iDCzFCCwShKyGoS5YWvyhBM4Py\nvaCl\r\n=EBPS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-collection": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "31333ac263f139f9a84a54517bd94ea54731ca33", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-l0ORrzuE3UUewfeXN056/CpE0uDhhzXoYozF4WCC05Ud3rIRHMSvhyxN1nsJisxGfB9hMfWHZ/Uicj6g6SXMJQ==", "signatures": [{"sig": "MEUCIBV8ynZl1bdsu0/J532jNdor+5ZnAYeu5JMDfAA+A1guAiEAl06s+QbEEbkyWEoIsGKNYgAJfqEvBHKzkPvcrA8C7Gk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824}}, "0.1.1-rc.7": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-collection": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fec3ba312839ee404e41a8b653ff1a9e20c2719c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-pX0FS4TrEdTrOGktEXdtVtDq1uAkV0Cy0+2NCfE9XXTp8RYVSbvmA9633Qyas0C8xXpXtXRqdhyLoM8abJzsFA==", "signatures": [{"sig": "MEQCIEvyFnvIwW2P+wE5cz/7t6VVmiabkbRBJ9WmYcdKSIocAiAITPchmYAKwmiRAj3/Qo9Qw0gWB5BOJkNeBbERc4WyWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824}}, "0.1.1-rc.8": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-collection": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9d578a66d2e2f07b4714a4cb68b350fcfcfe32b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ARd+nm+THdrhLcuaKbMgQhWZSYoJ186SCIPIHQ6/p/pqeGlbbdD3p0MITHzGvxQW2aDeqNlbIMz/3ksJWJeP0g==", "signatures": [{"sig": "MEUCIC86uzoynZiX+DdLX++KhK8Fmp4ZevrwDwbdI7mDk8wFAiEAqPcbAthNYbZE9HfG00OPymUDtNfjX05mLXfWoRNkLCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824}}, "0.1.1-rc.9": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-collection": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c46c22b9cfd51a8c219650e44a7ef2bbc892d415", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-cfrdCpqJ2+6XRmqgnDlgbZQMA4JF0+pJtnd8EzMXc32kneNZ1bqv9+bCGaCsbamo80uIzforFnxlAZvXekqmRw==", "signatures": [{"sig": "MEUCIQCfaCp+p/HvFd9X225dkm3ohMS2+ON5Ttapey40pUD/+gIgBg6Hb34TYMx8lccVmcQy1hRCjMTuYsyoW34TJJm2+gQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98824}}, "0.1.1-rc.10": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-collection": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c414d5303ba1f14933aa559c2519900481e9ae4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-KY+IaBEj+HcrZmOnHLUw5QWJJZmlLNOqRhA4wqXM0wiGhzDIYUsfhEtNEAXhnyGqSnc1IwObdCCxpvn7ioFkgg==", "signatures": [{"sig": "MEUCIQD7Qjmu1uaTFWJN3u6R/wOgCY76mnNhJ3xEF29ZxkbfngIgEmwwbXRyjB0BrIYq7TBlAmWD4vZ2Y8Mxv0E5g208HKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.11": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-collection": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "35f81d28633d4ac675ce9c9ecf7aa2fb16803ee6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-/Ke6RYNS5yYO1ABs/isoIj7/5Jh/GDrewFyyDfvXQVVzLXwVHqFq91J4aDGL8aIKFVdDagxYU5pOuewCwt60QQ==", "signatures": [{"sig": "MEUCIGaqP813kVR9oMysKuxaRBBNz5H0Uwokys3TXBxiUsvsAiEA6IPe9MTuIM/pAEK2NWvgfP75BGysApLI/P7z3p1PJ5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.12": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-collection": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d8e4facbfa4b6b6552fb4961f5d0d84e0d585223", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-KsvAhm7dC9E1s9Uvdsay3GzLejFiA2xlY73T66tn8FSyvCnPKYwi+3RmXNU/5a0UajfRYkkzY77AbOBlQ/V8Zw==", "signatures": [{"sig": "MEUCIQDeiDsLvkMA23QBkF8D5/xnX8UcLyKUqcbYIlo2ME9DZwIgJXVTSP8Pw0xFJJmuY9nzuVnRLV5GaLm5+BsW/9L2DUc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.13": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-collection": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f40ab3bc3e9c697974eb338759e0914d4bc02ffa", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-zjb3e8OAhcIpkgYAaoizll2dSy/f1RTMrKe95aB+FLIbuIyxcEEuzMRVRcWixJldldDlFkC6nqIZ/oCk99scQw==", "signatures": [{"sig": "MEUCIFUSTwKzcy5Kh1kX7ijT8xELRwAkgKyD+dzHuVAylT/IAiEA8AoYZHNx0HNXiG/liOoa0VktsDxMwZ+M6Gq4jzanL4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.14": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-collection": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c73776c5b764325fbe389c980d706dc0b4819fee", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-gmriBK8Du1ikmwcnG6ZhqpJ7OBa35/HveDs4exOgn6V1DKpp03obZebk5E60Duz8bLEkkAWYVC26KCjJJZCkYA==", "signatures": [{"sig": "MEYCIQDXHDYpvchXrDqmOv0zTahbVF3+sJ24fz+NNQJ83ivq9QIhAMMYz74lpAnW5u7AFn4kle6U/EqEejgzqWw8XvlsuCmJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.15": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-collection": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e97c5bbbc794de1cc2199de09675d0b80d8ba38c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-TPRixT61oxZvK0kRxOe7eYVPauIZhYaDf5dp0VY4Qd1gBiHOndu8OwvolEhVYGmqhj/m30FskKZBAydViKnv4Q==", "signatures": [{"sig": "MEUCIA0+9gbAZpbcuCNRWEj63lnWWt/XHu+TAp7zgXuf0x9+AiEA1aH45B6rfpp3u/Ajz59Ywwmrg5E/Dmnv41xc85on7ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.16": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-collection": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4e7454dd6ddb5c7afba2817d10c64313cad4f048", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-Lc+aqeBuvrKtwIiBn4cFQmpg1EwNi2vvWX2JAFUIzNvzg4FgSXnhMSvT72ntqTW4kxc7MKDMr+knVCJ2ic+O5A==", "signatures": [{"sig": "MEYCIQDszFYF9KSj6YbvWCncbEOcqV914R4q4a+EJA3/ICtobAIhAICv8XHUiYAncp4evvCFiRA026uAaAEtjcpCOXrNMS02", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98787}}, "0.1.1-rc.17": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-collection": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "badceb9b495e1260dd954ddb774a9c198491ce7c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-mxrTC6pNuPbnSF8xzvoczk+ww3DWmzQkVHAOzp81bu78uVNzySu3Gbk+OA/flzhmdlphNXg08mEHUGeeo1fd8w==", "signatures": [{"sig": "MEUCICemCAZ/+biqol/ejBTsKP3P5fdGh8RFQwJN1ruoF1nxAiEA6+iEcjwmbtrs734or18hHsSBYpcQvB+CUJWE+UOUxX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102433}}, "0.1.1-rc.18": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-collection": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0d3906d3fd8d20f88b22fe02e476d2be151e24d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-K3WZt/193JSyokEDKiCLl3ToC0GiYKiob12/3FkE+IIZsHKYWdhsC7VPP60yUl3lhFoazAxS9j1J8KMlpV79nQ==", "signatures": [{"sig": "MEUCIFbVZfRlSSO87yury7mzmYst0Y+/eu67NZMCBZ+2Po2gAiEAsk/QrLuCn68OAB6zcKeLZ5cxJ9u4Cu/27Zx7cF/Kbhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102433}}, "0.1.1-rc.19": {"name": "@radix-ui/react-slider", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-collection": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "048b770b9142f1d70bef5b087325a31f0b5a92cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-mp6WszXyBumgCjdatwvbk8Y9jAK/BDQGEqEIhgkYxQTHPqMaNwkOc2f/Jp/MloOOMxcJq0LOYYzP2sOv/4n6Xw==", "signatures": [{"sig": "MEYCIQCx12SkV04/euDaPhtngsQkkggIjYtcWLiUWs4q/ctW2QIhAOMIsJA/8D+we5Ym7Kj1/7nMah6Cmu3gaosc3RyVwqcA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102433}}, "0.1.1": {"name": "@radix-ui/react-slider", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collection": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cbf793b7a7e23907252bffc791693f84518e16ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-4OK46wlX2BmVsYbVYw3gml6CitQSTohkOP6mJEXVVlGAAJXgRWt5GmC35cMNpQFdmmQ5vj1oqTEDEB/8dZAQEA==", "signatures": [{"sig": "MEUCIAEK+1n6Xb8dETp2IyS61ykHdI9jyHnPS+8y8+pJwJEGAiEAo+fh7z2jzNRl+HWbLiU6inDQFOXv86CeyX0sxnhbz9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102382}}, "0.1.2-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collection": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "58a5b077a04b2dc6a0088baeef6d432b96c06535", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-OuFH7aU66KDMQo2W15wllquN2BPUC3DVsxJh8No/030hTIeuqZgt3mxtt6RhY+gsYSIx3xBfUbhxStMJIJScWg==", "signatures": [{"sig": "MEUCIQCfRe/G7jWWSH+qkQzuIGpZRLL+dyclMRXHZBIXkk8DVwIgYYzY7lmqZEN6Izp17sizHOfRR/WKYd78cp0MTyE2bKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5n3CRA9TVsSAnZWagAA4YUP/A7t7ae9+6R0cgYfu+M8\nyWtHWpXhNHyTqi3Qd4NwLuXAI6yz+wDPvhj7OZTf8CeNTpAvVzWPZBXU43yL\ny+laacd/nDYkBS4bQpGKVU5Fs5OPm3rGpwE9jX+eHjBzDMIQHt6PR8H3J9BL\n0FFBRW+DX4oG03EDBKUnzTBU7I5BgH+TBJe2Q06r1XfKNdmRxnHdBqcnWvVq\nnSOmgWpBsZtsXBHWgEq3KmyovzTKvZ60Jj+ytdKTLzH8e9YDALTJCR0TWiN0\nL83AonfNBlWdohZr17pdn2MLdr/5iotPzqyIde4W/EaIRTBjHzfe/jqfos29\n9Ugs4K9sjU2OoqEcWPK0XSZf1hsqp8xV++CPvXlNgofbExMDtnhHDISefgy6\nnMS8g2IQ+GxuBxLeNyE4QCU2BZadblskP6VNuuNIz+USUI9tZaadoU4XbzFa\n8DCUgISpSIx3+zyHrIHl/lLwwwSNIZTMog/0DRnKaeK52FLAc/w0KuIYi9GJ\nhhH3N2Vgdp86dziieFiOZG1OBzIMkjnQeJc3ZJnEp83ACrCcbQsZriNo+gFn\nQklcEsjq0ZULflDydhlSPf/NIxsMBaAS3YrvcW6c7ME3GqwLf3n7dxcVNBjh\nTRDu2o+YkZJpk7F6Rls4C6hRD6XCJ2LUOs/DgI7mXVd8qzZSR0ItwxcixIc1\npYmd\r\n=yEM+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collection": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8610de7db13172e6d923ec4bd3b7f1c04d3073db", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-1FmNMWvjbUzHBxypao+oEmj6VZUbqspTyRFSdGCPEInZStiID6TeMBiptRzkyZv35o81j+dYL28BtoR/Iq5+Cg==", "signatures": [{"sig": "MEYCIQCsa+y5EuWKVuYM7IOY/o6tddv5HiMBbVHiYzHikos6xQIhAJSdViNqooDV1Y7qp0Cb/yI5Ju4NZMKKXSkiB32aMtL3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN5eCRA9TVsSAnZWagAA4z4P/3dMHU1mlz0ENf9JVFSz\nFkKN2IaV03KFKOu1Gn/iEcTx2j/M5vlGB0XgW+jcVqYXN4ZVO/Nbrt6j1Nw5\n7BdQ6SMHpwVXtxklXp4/WIeii5TDz0ALMVeosvBXxCHVcYYUYA3eiiCOf/5+\nGksIDFnE9RIMi0Jxy26yct8Vl0Ebe2Ghn+W6sDyscfAvF2k1jXBofZDBJ3d6\nAoKpyHcSD0oMO7gEzDy+c8ea289HyBq/RXM2EOojjTgx6Zhy5ZLc9OeOr4YE\noC3De2+Ve4hhVdEOVS4i8mVu9dI+daHmKrxQ3qFTUT3b3mypWM+sGcvA6sL4\niSUUcwBQuEiSxPBrwyi0u8Fv/JBk0IrtW6F5KvIsK1D8NRYS5ASCC7pRrTeT\n6VdSmsuvP6nbqPiCB2dx3CSfONM0hsjKECiTud9XKlZ/KzBeKUopiyv7G98V\nskv14oC6Apv/ucMCqwaUkAoyTrdXnMxzcPNxoS7eUeTMEnobZpLoYSrlADOK\n8gLKE7KyE7Ru0uXiBLGxkcEdsbI3zezP+LmYaFwRzRCL6Kkt1+xNZILwJ1sm\nxYrMHuxkSVr4qBAmi9gF9fmY1LgXZiTmuhQ7Xq5ohS/LkCNh/3qgUAO6VIqz\nPb18h07HhCmw0qxM4eoKvmgKyhkFjcLkAqhX7EzHZTng0f8Ac/nEcjQUbEg/\njrrN\r\n=KCPA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-collection": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "581900c0424221532dd93382bdc0e69340a2e5d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Sl5QPpXqnu2nj4UtsBy1zhb/Bltof69ZfgVt/ZXlFprNwC4yzefaF+2L+8xl69INhAIp/tpIpUKk2k/lVD4CzQ==", "signatures": [{"sig": "MEUCICrQS1S4jkT62PZ0p+trA6gA6wsQYstGfvUm8TD9wm0vAiEArd2POlXuljxHfKgSLyF0JfJkSMRXWLhtZDylh+ve9Sc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoUdCRA9TVsSAnZWagAAj1sP/0rTlvysIAiZKVqZz0VC\ncgasSBIAyv0Qm5Zy7JeANcmglvj1hboBt4V/n83BzNK4oQ3ZhD4EQRq0atRk\ntRDN4yLyg/t2hWFZhL/BOP2HBRxTh192YjCBfXHvkJ12heDDknbZzXUHXKM/\naY05lQ7k7BuO75tvMvMf43crltHCzJMPPSGiPeIDOn6coH4Z1eEfnoytug8l\neN82MSD48qL3SqARc+0OvprSxwUDzWc8D+mEC48k0PSIFadM//tUbOcI2K+w\nwA6oYn3wwa21DO/jwByyU9eJZYLgKLgJRiZGPtijg8/o4FZNuDvbxVfRe1dB\nlxA/R/LNixmPA9VWL5qBruIrwYyKe8V2Q+YSIz0M9lr+/CLrzhFQ1h/AJD7o\nXHq8E5TwLCxLnJfMv0Evvqy1SGyc08sD8gorpo4cYlaq88kAxApiiDZNGXND\nDg9ov1JpQCPVVIcQHbk7eaZLtWbft67VGuVyLC5avvH0JgFwnOvWKLpYz0iW\nKI5tNk01jC+D58pz7dI0Z9Y5EPAaCxGYW6ZtyHY30k+hrns2GPo7y/cVjeQz\n7MylHq9QUcRHamPeZb064dOyTXeYtbZ/TJ5hSj0vKNgPUt/guzUkMf9Y3Kr/\noz8Ykg7LKyTH4gLrTSwUHHOTHBE8ZkrUCCiLz+cuszvaPBtwmaCxKrs6qv+B\nsb1X\r\n=rdKk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-collection": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d5b01b08efdacf0e1eb91118a564bfa2019746c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-84F77pala3gRcIgWmT+AcNF8C7qlOnMPledRr9YIAOB77p7REtZio9R15ho0q6JohbwiyEqFe9JDKQQo5EOVtQ==", "signatures": [{"sig": "MEQCIBFwqCfnJiIYHaww29Zoc9jR7sAs0RyN/ZECWuwWYWU/AiBPaGY+L96UMT+/xihnaibDETIWKaBHC23QG2XE0DBs9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiA8CRA9TVsSAnZWagAAePMP/2Qw4hKduowiNEmwdLlW\n+p5gRWkkmDfqZp86ou3SYc0jFwN3vd+V6esGwBcDR7nWmNrox+GMiItbwJYI\nfHeTdBIkVSo8m+3VLYw077IQrb6n2yxzMiI9jGXvBNi66Sgt4nCkoG9Jh8Hh\n2u4VuQtKxY0N532KCGNCBQKAfASTo398G+bw6u5LjeBdLx19AjcEwV9cXw9v\nFXzYcuOGqPs7aQ/8N0W5GRLAqqZUgkjgfxp4Qk19uINdfURTHMWeRsC25VVp\nxLAm3msF+4uEq4BZ1Rz/bM1H2LCr8aLyrSQXMU3FeU1vEM8EeJuJfSSsNkSC\ntn4oto4pCDpzjS79qXRjbkqAw72IL4+onvx29EkXE9haNj4l2V/FKz7nmN5n\nWwH4f398U/FFjuwrMVRdkmBwkMcWiLEj8DQBRc+hSLx6zIdG+UwIBlhi3IzG\nFA0osaUrQurFPCAAdgGmPvrcBHqXnoi6mQbK1/CL8Gl1rEW9VRXtacD6K6GA\nfU5dNt3Ma836BqYTIPEW+rBoh4PZ4T3hVfx0OMh5lmB9KwTEhTzXt0rcENhI\nopDtAble0fwhP0xzq0rPaBJ7dpEhSrwyB8W+Uj15ahZmIBVY4hMU3KFXFgvr\nOaXCGEoC/q7ui60mcobqWSpR6oJbQySIMW/qBbVSsqNyKqMGYOfACdC8a3bc\n08ez\r\n=ZkzV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-collection": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61fbf0f5329ae19644a3491a301a21782396c816", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-PsC6gOtCdRvMK1AsdmKlHRR+1LsCsVerXFkjY6F5fbuDNzbxXgO3aL+SjJ1B/bsgh3H4Ksra2v6vQxw6S+FT8w==", "signatures": [{"sig": "MEYCIQDr5Rr3BPj7lmAQZFW6QZHT0xL1YmKyyXp+qcYmCvytJQIhAMXc9J0VsogGdqbpgs60wavCheitDzJD18tUbMmnP3MK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiOsCRA9TVsSAnZWagAAaY8P/3eIkbsfrfxFkHZWXfAB\nXaKCr3+KjRDUQ/eGhKdqtYbxNdDu+8Gr9bj0AIYgn+9L1YAac8WIHC7N9P+/\nvLypY4ttUuuZu6OcQZqaL9qAJsr7s/cDbPQvHM8EsLrbR963GI91Alyy3pdU\nb79PZvEBHaS0mXvnSk5vPty+fjEnE2FwBx85UzthzUaR2J8gquJra0R8oV+a\nFwEk+n6ie0s0ybcG+V4SKiuV8wBQB8otjdEWFeK3EL8eV9fSjGN4r35QjDrX\n9/fp/2hiFTW5dENA0UUi0pvB5X9JEt4tic/mgMQ5m37e3NqIRXQMnV/Nzqxh\nUTEmq0HQ4zoP7PkKuLxkVGR2q2gtt23PjBCU627wcXzPa4bGH3zs3nK9ExEJ\n0yjL0RcFgUaKnsYDBEitB/jywOaoKpTbZ9ahlRVOmCieEkFu0ecCjgnLytLd\n1hsXZrLLob/Wd2eTAdjnbZY2b9gnsd18BDY84JKpM582LiHhUojyYWGUNbLD\n85xZqBCQrmns+xxu7wA+vSlcQRFIyvX539Adny0HJgVmFILgQ66qH09t8MSp\nn1m1pGzpvUmfX2ILkwa6DJaoBbMqNp1+OqNp7B9Fycd+dxeIaqTMEenLdCIZ\nX63dF28hF81QjseNfN3DK+QjTDgwtFliAHRc6oE3OaQAbNy0+hnp9kZdp+oL\nhK02\r\n=OJ7s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-collection": "0.1.2-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1308f2b5a2329bd000459a1cb79a0083a3999470", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-EsjBfybBLzQoGZ6gew6u5Md+fltvx8B6Id2KujSrK0wipLcXpAuPOgIBw40zXEUM1uA6VXLKgTy2GBvzWN5xlg==", "signatures": [{"sig": "MEQCIEnWq0DF8CF0JLRmxQPOhc0N3NsSawPbQ0aJ+Swes9/VAiAvuM+tIy4sMF5ExZaKJr3GV6PyJkeUzCwkRzvwHteyJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjrCRA9TVsSAnZWagAAlXQP/jYbRQn8eZfxuJtV4c2G\n9N0vc9FN0r/UywaK3d68VEegH0XqiQDWeBqUpOleWDZ7OaE86165ZaxroTiP\ncG2IiwGzmjy7J/HMma55ubo9YjP/g0ETlzAz33e1M9YRzgHoi3OLid5gOJvX\nIs2gcw4pw0pqkvcSvA++DJEufsuTTCTk/fdXRgyFIlrFUWRrdjR1PsQJbZz9\nK6jmU+vabjxTDmRszamwl2x/1+rHgczlDJ0jMvdm/vxMUhUC1UeTXI+bRCdn\nUCTfTVdNL5yaAAhMCuoNJhOBIjhpZHJm8dwSONUuLwyAsjBe0uUNEz8iIcoF\nUV+Kk9B4apTx18S8tlFmUYw6OVnJqdBgxDIzYAPi6XsyTVRsLpNw2TiqayEo\n/5RLWm6t0G6tfBC3IRdh5qPDOd3+XIgpfsHInyhLMo5hRPipf6jOL1pZR2+a\nqIVVMSv1CI4T8fCIwiCC3N+XkvSYq/DWrLfumBpdCVI0Ifbdb2TbeIp9L0gn\nYBvvh5BQuQT5hfqVyYrKhF0LIys7t2ECa6NxIP/f7F90QXBwiL/Ohmv/4Eef\ntCee9TmW3mX/huGv29vi8nRXp5KgG2FVnDbuoSiP97PxC8DauHiY+Wnz6r27\nhSkVcTU4GBROl9F9QtVDENaqmVt7a284KJ+npnZtFM2ZMABkBRd6BUnE0tq2\n2qOm\r\n=WFsc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-collection": "0.1.2-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a3cefcc6722fe1a09a7f70b8c0cec9364a5bafd3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-zya/q/yyzA4S4vkoZMY7XEBf89N23V2wPyihFEc2uCRm6BfQ9lAhGwkBtzQJyLt7UxeHQav5AbbfnbiRzTsmfw==", "signatures": [{"sig": "MEUCIF7u8tl5IGh7pMlSRawPWjPHf8mBcTlV+l/A0I2ES+EgAiEAxj/2OLzWJCbOa1qmR8xFtdqvVJhOn0tJl8mDJSskiQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRzCRA9TVsSAnZWagAA1g4P/3PA90kVYk39ACEzO+fo\n0v5dJ1e9rAgHiUBzd0c7nutGC7XW7xD4bBC8G1I+JAxWaG4ndTraBy23CEGG\nYh+IUfNIwQW45rR7tzcn3MVno+LheU/iB5nSFgJEPsLw7L2nmce67sfA/jrH\nEQFBHw0WGD5b3XlmxEGX9ZFe1J4h5OIEi7nPUdh0vG1Km0BXgWm7/1nvSHV4\nfIcDnReSASvbYc0fXVMaOPpMR4ileViqN7++NEPVGTrxRRZd02WmTzphqNN/\nwyio95ZNvktNsUkN2TwXAFe11+8JL31QpbJkguR2b4jPLj2GHjke3bFUVCLU\nG2teNmXeIle9NQEobI32bAXBpkViwWoc593OHmQRUdkTWsJYaBxN/wK67u/W\nGcN/IFjH1C/ijm3JxF9DkOjweiHIuW8ZvlI0Wt5V2sGpmGmm7V7HQzNzKEX1\nj8Nlqkt9OLhAL8mGX8IWvRhplU/2VJUciflXKo7TlschoC0yrfVPiBxOv1sr\n/9QE6l/bw2R4ARdz8pgcMPEH0vDtVwlHmdlJWqBBm5PdoNzhzsHNCTvIY5ro\n01oUtYysCcIrTbtnF8VtWP4uHEOEDpR6raLC5+3dFqC5ty/iGSTBg+2Q5Dyy\nSV2Z5Br/WK4I/QYnyNpwAl9dvrSsMHEF1JmzX14xHGWyhTQKpSuwzA3oaLsR\nDC+V\r\n=LRMa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-slider", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-collection": "0.1.2-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5924b1ab6cafaadef0e8fd62624e7d7738717dc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-H1hdUVeFgLr6HcrTpCZblRCNBzCLftVbWYz2JLZQpvab/4XDHyuP1CViqZhPjQSrCFKhy6wszhT/PJnCcGJrfw==", "signatures": [{"sig": "MEUCIQCmGCFyn5l3ipNslIMW28soJ5E0rBEFDNkyoMDI35qJVgIgXOrZlzPP+3bVlxH+RuvPAS411quXWGp4lki7FUinkNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr43HCRA9TVsSAnZWagAAC8cQAI7T9So5PBgD6dxVJjsW\nkyqlcvufHxDxjk7nUDJrsGomjc49Y+d8Ojc2r0lLm9jBZYc73cdtwYAhpNHK\nvPYmBH4uSLvumEmQsqZsmNo+w6sj+8bp2e9LwLS1trBHBx8gk1JUWTPiK552\nGWmxoh8qi5IRxwjwfcI84MstjUsVymYZFJWrEquM22nQHpeJso6HEH7s3Quf\n46Iah4PdjqJZ/SSfX60UhGIgjETVLVjKzJ+Ic2tNMWnyzE6ODWqVO3oxu4dU\nw/eQYY7sL3kq84sHBch1QxErboRjeCjidYYwvRO3fYLfjA5J5Jq0DV5C88/C\njzMfFGBSgOb2BraZcnYdDtmZj46oDj9GrH1CgaYlVDBoxMCZJKYVyRchI66x\nGpgkrs5UVF6jxjAy/76xkEKJ9qank+6IusJb+FoON6wJ+aZqbkjopQeJpSvs\nXWJeBf1K7qx6XtisRjR7eGULV5raw19TMOcbpYQwrtSlkHEoLLG1t4UAZ6lA\n51O70ynOBA7ru2fIaK36gynDxt5zI4s4HMJPRbNJRDEMf8gQqawrlVMC3Sx1\nOyH6bqyq4K3+kl8FtmpozIPneSPC8IGu7r8kVWMTM0jRnrIDAq1jfZWbu6M6\nuGOAD2r9yDEvASsH1kDqH/VOAFSltwqc0pksuf6FuSMZMnK7ILeM9FrlhAoM\nv7Sq\r\n=mPwM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-slider", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-collection": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1620983a4857bfdc61cc78dd6bb7d75f71a446c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-VDH8XoVBUymzzyrJNfTAJy+CqPPvcnMjkOpmBPjJD79GGazkAmVYe8gGobIiwEuHLTNHJuTxFsaeckyMVHGA+A==", "signatures": [{"sig": "MEQCIBPZfzRBAvJQ3Z5AgqUWiHdCxoryBiyISzY97afy9yl1AiBXpMOFINOP3lRSOh36QRHIN9sxUzugKPpE/1P5Ryy5SQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD3CRA9TVsSAnZWagAAM5cP/3uGyAFJDv8oeVegD1pY\n5UQSJ3eWfKs4UNhgnCPcn5XkYboJ4NtB19v46mhno+Gky2jFtvKKdCR1+7i1\nZSosaOpzyLlFnYntg7GGw+i2v+hZ3OLf31kkxIJVh7R9kagT3Fw+wwXQLZkN\nwmhSfZynHlFiohc7FffMuyNpD2ZIKFWs7CNfje4OjZ3xDCO/llwINMJz16w8\nbzmz7B+i/trK+t85/MYy54jfZjyMzQ5g7/6PNNO5EHetSb0yQPnCN9R0Fy+R\nQPVhrsNU6fxZsmlO5wfSoE+R/vhvygTcSq3K05eLYabXvu6E+GwavK++GDNp\n8ayZfdUG7yWZ9KKYYK1an8ltVWqk9KR5o4P/jfujveAF1/j2WAyB0Yc0gbLr\nI75r8ME6B9XsGE09O9sFRcUxNZennmipUgx5u3kQMOsf9rtz1HBgEdS7hdHm\n3Zc26xcBdr4TgQPKolQsfYj5wyxTgt5XkeEtVLu+4dzdeP3jlMLSgImd8fnk\nZWUtVMco5hmMm5td+BIF/iAuWvT58FdMUfvIu2fDwJR0hbyFDNuy+ul/JTop\ni4jHEN/XewCmkgB+7rlZ5VQDXNL8vReF7xq3eoRMzkgiSaEoe+Q1d3hCRtmK\nsMSV4miHl+z+m2cNhA863jQiwHT5H1I3zA8vfOdH9MUze779y1caS9ehZi4W\nV4U/\r\n=rC7m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-slider", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-collection": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f3de16ca348a367dec8aac0f29af1c45459465fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-B4qf7ex4DHJsRv5wFCUeIRpzkDRjzTey9OclrLrp5yLI/AHij+zNgT9JBM9b8Q/uoPrrKQ2mulYl0NpBy9ghGA==", "signatures": [{"sig": "MEQCIDfyhJ/ytB+vdfUiqn6Byb8RtXQyO4sxQnYJYHeCAV1kAiAf194+dUARRCPhlYM7gt8+nLYe307jh8LHXvpWQPlvNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLipCRA9TVsSAnZWagAApHAP/3DlojDXmXfuXmsRhwXx\nuS6eOVnu3hrwOjY6/Xab8GMxhu6luvIpbN0hFPk3KL0XhdipANSCzZN5A0bX\nBBJKLzO78OEA3NkUwGXXU1MRcmoe3/m3eh47YPdP2GJNqOURe6/hrnfS0biq\nt3yKgrzXtxVzO1t8ZW9Z+0N6SUCmqtf6fBMFZUpAdRzUa++4F863ozsk45Fg\neVI344ZRx8rdwJWapgrqP70lpY/+DlY8DdljoKuNwkXETglSTFn5D4+OmW8C\nt2RSpONAH9E7mOdAiGKd+UDyV25IdALNMRVNvELOqz/xVrZ8Ud7ErbZzLIua\n/wy3QDjFsfZZrYL0YvLy8zRU0ihGWDTBIXRibkzq2y4avVZ2tbZ5BgXAZ72S\nQkyZE3NPZS0YDZWyU1Ygly4nwwi3M/RWO6cpm3eqL+nmajKfxZj3qBkRTkNn\nVWkbXEbByAd3ugOf2LOSwx/aLojllRFH4A/FVMl+Ky/90wXC56HpDPx73K0D\nG8n1nwEgFUblZwT79cw0tT9/Hvv25yr+TkZ51MeJu/kP1gxinQyLJMIuWU7R\nHvtI6pyuYAmO9XfoDM4m7KD6YhH3Bn4BKTTWlngbvc8rtSpqtK5PiuMjICdR\nE79ocRklcIRhQm86vNFSvk3uYvGCLjuCJYuSRoxhcIMCCOzlktogM4UTN0pc\n/tWE\r\n=yY7r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-collection": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4790dfc8a75fad07ec44830f88d71aa0ffc1457b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-BW9ZESVesKjaszDo/vdJWHtFaPjLkSXvbhJ5ZZlxtY6d5325DHLUnBTBmla62dI8x76gx0jTBND2GO9fA0BJ+Q==", "signatures": [{"sig": "MEQCIETxjxIHyMz77GlxQ9b0V+7f2wd9fiUuP5KL1XZc6YY+AiBa/pDKZU1ICpj5jwg1Dt/a9Tz1Yx4YvWUhyMiFMBX2kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102415, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLj+CRA9TVsSAnZWagAAxawP/jm9pjVyliogEs/Zpgfo\nJGAfOU3aCc4jnI9L5GTFmNU4bWaGHqCWBeuZgmKunmV08E3nLO7bAawWRr0n\n9c0vCa2UFztGIcayftsn7voxX0kipySgQKt1zbUmEmOiiK2xfg4zZdvRDTUj\nQKbfmB6/E5HC4yeZv+FkPol4EPS9w0TEhVdZJ6FqoVU5LmaGBS9d0k1FN74w\nXi6DuKqpB2yux8D0sflBAyVMwCGCPYf33IG6Y1A07XVql3NmtHzEKzboC0l9\nimV2ZTqfJfEKHZcVLUqYXEcfSkw2hkSLKf3IIV4DS7wvj99OAHQinZQghUNJ\n2I292Wy0t3tfrzciqmh+nDipoBhv/D2y4xVKAerTLK7qvtDcpiWYurWltdCP\nFPgCLOfvEdIilcyNEPW4u7TNj4BVrwILT+1SXHXPMvfFBfxQyxccLtea/FIJ\nCEX37gTtKq/r87ZNeM+5xIgmrlE0d+2tx/Jzkdk0wX6uElDT/gsccwt8iHHm\nptAkc2UsHobtN4W3ixwNJdQDX/zJmtowsL5t/CKx7JkDywYskz0TxY5Q1yg0\n/doNWGe+h8L5qWfRLICujtyECtZR77L7GXm4l8IBQt00ENh0mDL0xHFTfpkk\n0EgoNxOMqk85eCJkB2IDPcUL56gsACiU668+dsnhLZ3aIJbU7gxPrc4xXeut\n7gyz\r\n=LlaF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-collection": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "44ecffab205676d153a12c53739e4cd3541eeb55", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4UlFs2pr02CffzwdUIINx3n7N9HTfSZ5VmKkefhZOzxtLujyKvFnhZDZMCvc2YESq2BedHsKoQkaYpnN9RE33w==", "signatures": [{"sig": "MEUCIQDixNql6zVWYSfGojRtLp8KFH8PCfoW9u0tXMVCdFUEXgIgddWwCDhPg29LV1xu9Wl9vtYbjY/CXCWSvJCVItBAt2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102410, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3duyCRA9TVsSAnZWagAA1lEP/3yxPJ//vzbfonGRgqM9\nq2lUaZCeyiyw3I2qpqAYW8bw1KjEZXsbQ8/L3DXGbU3dmmylqRR77uADkFST\ns285uB1wXky7azBF+25eMvz5onAeApyUrPkDbEXUzN5HYM1Z9HUF2wOXo7N4\ngIJOBIS4JfunIWJYVdSuFGwG/+lXkRAOPDITHTALY6yiBNQ4sVeH8QsRfZAu\n0k0MImOyELCc8g90uaFcbcr8yJkzOeBL1mLNNm7LQjUT6VBrc1+7hVNy4/CA\nF06xoEqGxWpMn//OYnccgy0DZn5OGnbrSoboImSXUeOdU9Tvrpwh7PNvABpA\nQMIEJ91wMr24EdRfAa3j/t/bUg96vFW1/E8CfP1LAq3rdUxH2qULXoyf84BJ\nqzkTKh5EBs5lsj4mO7w3iqtu//x8MaWzmV16cmXpnOXLRopQNWaf1quKdMAo\ntCRafvhqz9w6kum9y5VzLc7ZhisBczpcxLwk9CviCucQgGbkUmT29eQNSg+Y\nK20azVQ97kBt2bMmGDM3pIBob2ED9YYS0S46E3OS6DxQJfbBP1zbNxg8bl9Y\nLw96QefP5nMq52PpWdh0UjDPL0/MU42rDjXOJQ1xfbbpyhHWME6A8bClVb5f\ntRBherXVog5u2gvrCQXV0gkeLq34nAokqe++ZRwM5M61XDhK4wkeae1Dz6e3\nMEWN\r\n=2x3e\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-collection": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.2", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c595fd25fb82fb469891349a6a908339f71bcce", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-pi1IOgeiLJ9zqMP2/rHZdylLYEnhxnCMIid0RFBtTwN7smVKQifsaxc51ztzNqyF/i6laE3OA8gvuKEUYh1MCg==", "signatures": [{"sig": "MEQCIHKBbkN/8GCeeRvHMPVmyZjF/KnlPD2/QaRxo0ZOeCq0AiBskVJUVcu1lPpyVO4s4YjXl0pvNDopj8fSyCAfA+vWGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEYCRA9TVsSAnZWagAA1nwP/A47602DnHJAwa6JtuuT\nLDwA2RVNzQGyVhUSProNI6lP1PBI6gpsHGFap+nJMbaTWvEhPlcObMqxrlGy\n/TkzI3j8VvJsvqfqbrFzbVZOCCsrjm/8jHz0UMuInU5MORmzMEWHXVTKSBek\ngb9aea8IvXcSs0rz/eesBGJmhhe/6a8xTMYPS+e2nX28uvMKmj+XdDdbS5C2\nPxlJOKVqsppAlOKdQ+ZPmAjmK3R/QB5izzG8pvherMV+6gXsvBsaqM4Dr+CV\nOagbLt1ufNGmPKs4QqBjltjsdWCsdEOdMJboGcAC/ppw4B49RUCXWVMpDjyJ\nISDOnukalGSfU6K7e/JrInucrjCTUE2ImYmZ+q/4q5bfcZRsrC9k0/Bl9Hj7\n/GqhSO9v3d3H5jBavZ2+5rs6T9mV8iamoDP7ujFAb7OB8p9cNn88htorAV9V\nhczlOsSOWatkgCIeY6mMgGzkJDQpTLQ7m69lYZYHLxVhZ9o/05bxjKfcsAgd\nYsEhO7eLSpsaMtb1vEHbzUILJg0mzsBnApjUclXtWVP12X7ldLp1LtZIYjc4\nTqFHJhYTn0G/5xVVpYaw18dytvDv7Df5PO5/HitRZ40HcjZgnm6X5WvaVo1j\nAOu1rCM+3KOUKHydylcL7tdU+im8zMv8Qu/tjto1TOgNGFuGVkX7lnGdgJa3\nuQz5\r\n=kpfZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-collection": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.3", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "93c820833763e9da9adc214b3c92bd08bb7e4440", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-GAT5vLEPcA7Xs4lY2sv09DJFPKT888E6ycK6rs1S239JyAwsN7zxmd5jw9UIp9wbhba6wX3EHeUXpBSmPlw+SA==", "signatures": [{"sig": "MEUCIQCWtcKpPAt4oIiwvJPNR9UW6SziMK0kUjHczaqMv+xLawIgHv9pr0/7EhdQXtaqXfZ/gvJhJeVJa8PRtnzWaW/W2Ks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Cm9CRA9TVsSAnZWagAAQZwP/0YIW2wkKOZSdKXDb3O3\nMwLl8NdoXSJpAhxE0fPxM81+TFuFk+Ch3dudrX/2sr1kG+HI+WGI5ZdtJBb6\nNhJOcrE7PpFF3mhVZBsqqahlVIrGvzG9yhQdwFCq0Jv68iH7F+EvqEmo7AUN\nIBHnF/QbGtC67ai97pmSCqocxcI3UQh8GkHCIGxRJUD5PITbw26D74K3RcE/\nf3i/vWMtPESCWBVzRr7ALxktjTKq1ri0CfMTSfGWU2XUmWX6k25VEuF3W/ji\nUyjF2j/JvtdoOQ0w5tM8eQx3I5+cU+HRisX4r8q5i3+NihdUWztf7vcjfjmu\nvJfpy6l8UJ4wiII3/VwAHI1GOzwhONYMvWMuaDiqVH1TQyha3mWGwyMDju3S\nv8s2e6eCcFveNU3KOhKPr6LNc48+2Hb3EXJsntCpCcxts5v8q4RJPRZROLqL\nc8lcxRibmpvmfAbOY4+1s3B0BWF35hP1bAi9ocnRm3mkrKCE8rjDov2Dac7/\nDdpnF2ISZ8b7eFFKT3a1WXgI8LJExr7dwiy+Hl/wdjJsXoSzqWbe30mE26p0\nFsY9abMeZkgJdT5f02xRYgq0tNNrVKw03g0JvUVT/rZ2LmqvAqDDE6o+hl6T\nhlH3m3kq5B0TP82fNd9yEcpSTz2bHfTL63rRqTDNqstONQKG8Y6aIqd8vtM1\nyahM\r\n=EEwS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-collection": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.4", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "69899a2561e9f643aa0a2117d169cdc646a71377", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-AI/L3M9//anZuTvl+OBx3688aRTz3xJCU8I0L+5ygBR0Z5WRg+UMa5UZnXD7teXUJyHD2/rIhW3wcVnG7dFpAQ==", "signatures": [{"sig": "MEUCIQCypZh6tmdG7InwwGjwB1caono7gSDgDhD/yHDngiaVWQIgBi7HeOG7cWZyNyCRBD1xtY/to/9sJOdSQ5zBlL3BCo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Gq8CRA9TVsSAnZWagAAhVwP+wd8VCp5Z/pH57Nkki4H\nJ0piaOFqGz38URJjrAW0uHnS4lDCqXuxQmJrMTC47JdPELa/w1vH+HABAzia\n/8YxQMGxPNsksT48zwY284wf+ZOdCspKZ9nQc0cPba2GqhJ9/46DaJTx2EUF\n9M70m0qaL2S+UFSjfusgSNWtaUz09ra72Y5U6gTJoXNlfS9QPQvxcFtyG7iz\nKe4oATbpbhCqhl6a2TraWsp9m2kLywdIFzEQrM+bE5yP8EhY8bbOs8NV21xr\n65nkRQ/+6kn5EmlPVvajaYWdpHy/gZYKVWW+z4Ff22hcACmpcCijTflaHEsS\nwrlxODFEKaH6lDrqiTsybIYYzPLJCXi+tBWOMIw1DLSjEPkVF0oDcDi0f9as\n7gKkmb77BWwL3g9Av9dOXHdtxCa4PAYfRUx1PNOkhn8egTVuRsWoR9YT1d5T\n7EsnNdIeDkk5BGDEnUjiR76cMA2NISyoDL6GZ6+maV//heeJTfG2XWsIc6ta\nX8Cvo4gdxWzoPYGntsAlWbLd6eWH+p1xxpExT7ypGd1DFhquVFdwU1Z/i42Z\n4WgLVBYW387IkCl9RJYvAYG6rBVEd3T+nqfWSl9BnbmRlmavI0Of4wYnzYnJ\nM8hqEXHkMR2ncZizqn1PK044oX1OhEcVzDZXrFOSqz5EPZ1awCyb7nkBYOnk\nK6st\r\n=yuKn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-collection": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.5", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f0b522ff2019939bea297ce2f9fe472fa9393e6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-v22FiMCm+v0dyVX+IPBKhwNiQLRnickCB/UUHqVgyBVFJeO0fdi++hW0Evvf7VtUAyZjqj5D/1FDuoBx0TWewg==", "signatures": [{"sig": "MEUCIQCCPrViJfI/NZf7mbgHlvILgOg9nJn21ePTh0O7XvaEHgIgMOKdy4UWR+sWbP93lYIfUVeYxCfQxj52u6jTIFMIfbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5Zc0CRA9TVsSAnZWagAAisAP/j3cj1YGd32eLLVNR+oM\nkYL33lxvFJgIFZsYRT0zXwIRpSlT4AnrSHBsdtvGBKzdlTcKIduJzTgyVmXu\nA1JYg6cbTxk1xrI4g3ZczwNPqR6gAscsCthn8tIq+sGkU0YwHLdHtXSQ3CfJ\nsgkAdbcbvRZtWDmdQPcux2EMAr7XnsjVAw+CbU7U1JAA9cOAoDxWTPfT3z9z\n1oXOdDO1ZToCkCp04/yqnn+tDRxtjs1msc70pf2fdiuYa6oPympj1yH+fAM2\nymyiolNar6jBQxuPhcQhbG46x72IFZc4Ls5loe/4POv7NP+QrSrpyvoRzw5M\nQb6uw4fHdtpSCF633mQRyOZMV8g30JLTNjd0jPHZx2ifvnhv03GLZXhOWP/9\najileR3KvstlbYXgZzWRiT6r8px71Fh0TSFfbZmxPH1co1rVwDaWHzZqnhll\n1D8FxMYD3aO1rhQvvajgZi7cPb4YrhMAC2/MXeRLhXvhG60O0iwErwMnYCv9\nnOzma5+MnZMNLke/axIgDxrmJYwW2iTkOquCuQl+ytLEwz0+FQt0ZXzxTBaY\niiS/Le2nXPbDGzmaLqlxHd3uYoT/tZHN5rniOM3hWORpZ4qdLzzUBtvoBdl2\nB2t4d4r523SxU+aG2F+C/wU5HfHlNiH33YZJXuwoYm8VmFCbhTEKhNcE9Ema\nkJAf\r\n=lyjv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-collection": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.6", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "692bf2e6ad8e55e0e7780dd51b1e310f96806fee", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-OtrrPBl+NSJhXe99XL1xBEWyYqV9a30ReYx58IP9m7fG2M7Xz2lq8lRfkWG8OIVAljljATmJ+MdarHh9pFd0lw==", "signatures": [{"sig": "MEYCIQDV1HGuz3imGKsn5Yqe3a04nI0BgyBvu2V7kHQX7rCqbgIhAI1+LKfBAYlvepyAsprOh8FoQJJbbO6mfQTjJYYMGaCi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtRCRA9TVsSAnZWagAA+owP/1UjqkZEzh9XsUTOcN4e\n3M1xaRSUoc7qkJkdBnLIK8Y8Tge4Q6U1FB4XYuC8Eu8S/SuWItYOXwwyI3sM\n2dw93YN1LZ6D7fnQhKxeaBFdlImiOBXOMx/zd9kYsxhE15LEPj3tvFTzWllo\n0ufo3abYEW2WEs0SVDj/tYiLDobn3fK2/0tIkFUVmPWEm+iCfSlr5Sp1SiwQ\nSoXaleat7uga12shPtWK45xZgN4F12sAi41ZzVrIMpitVsLTB5mNt/SQ9Cm1\ne7AwgpFi8LvAPJRT/M8tJ03ICCpNdFq/ssyiTDR+f4+kdfijEj7tffjUUSLF\nX+F9LqYN+RVjHq/e8fjK60mTcf8XFY0hcegKYqG+eG8BihEZ4tH1tPJu114W\n6isWFHUjsvlVYDb1xUTljL4bazr2G7inTk2/lM+D+knOHZDbYgTPu7Ih1Ecq\nQCVtafU/Z8nrbtePXvQ6lhSpSAF+igXjaGykbFZMv/DuKVHyJN9dR1pYD/1R\nSG8O+LvIGVME81z+SzyMVGYhW+62dDFKr3wEA/rQPTD7Q5ZV+zAS/K5O4by+\n3MhPbcmjA2/DMRSN/v5QdVS+27uZhXmCwOOPoKTRdF4NOW5PCMwKzz03xVuv\nPObmsrPZGZAw/BP7XQ8GjgFEZxsVHDJYfn5Ub+XqMDkJp0wGCxz2mL9aruZJ\nq26g\r\n=PBm/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-collection": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.7", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2fb5942536a64a28abfcc8c2ebd43a704f2e0519", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-48cuNN+mDcXyJB+Y5EnAQPWUkhIxnvw+4d/6lqdcq3DaSIrcS6Ka/qx+8heFebEXSNhmJe3f+PIxXHf25rJv+A==", "signatures": [{"sig": "MEUCIG0jnbY94zujL3mYbiCNCaVGsXQA3W1MBBLzkLM58AzMAiEAk8gABD3N6vI+Bw6ZtYhJcdR+a1yAnLvYN9DLN9QhWpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdqCRA9TVsSAnZWagAASGIP/2JGmaYFix+jtSHHXgQU\n5B3rA+5OwmgA+OiRY7rCj2SBpkfxFYYT5pnK/4Y/ioluw+IX+ad0rPRsy8j/\npsED4/H1p5wzb+ZivikyLLwOeKyCbt6KPhGwilb6DXaARQXCnn+riNs1gHWC\nTgDKtlaJkFms2bdXHCfzV2xK9B83T+jTEwgzF2APdk5nssPFLBhwzPgosKbM\nGFMuxufYn/SUOjoaX02AmZyuc/yAdK6XIXB/N7vCK+wx8R7AX36iV+FMTFSY\nft5ULiBFOmLGoxEEPgXH7ROqfBccS8Wtm5PBajPYpmQlJi5NUTGMN5xErqSP\n6Du9bsWHyhvqpX0ToDWpNCBn0+BiQeSetPbH36tGGgjTqSi7MPLtXjFduPzN\ni9qxU1kqWFfWj6k9/ov2uFUe6Gm5Qp1qtt25uG/KXyWq6FeFmQDfsBr8G9gy\nNm3mBhSE6hVV2zZoAw6xKzkAIh96oSsHaMx2Q7SzWoKDuj+7W7l0kyvmLIRh\n9k6u0W19yRKIHMt7kr06zikc91PKlzqGVIh5HlBSXUS3nFpdsZICmwqGmepJ\npohN4eL+vRhr8E4jrmggKtG1Nc0HevN3LLA96cXlcf3fSTWlksqVMCJx/qQ7\n1YihMYZvfiO3iwNLfLpPyHZmYmURL/AAtmqcoAX2ah9qJzoSxKi3RHuawBDI\nXaEL\r\n=ouZD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-collection": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.8", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b6177bfd73f72d79f83d992538aa4c7cca8587a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-5oEBk49pTxaC/XkrdhkxkI2lvO+pGk1qrJ73TeiTdvTYrIhw23HyAazBCA5WAXACzBn8t/p76EJhr/i7FuJPhg==", "signatures": [{"sig": "MEUCIQDKv6+UnGfgEw/haWC41OIE1kIoVG10qCN2PwcVFeWDXAIgILIr+UXY1zA/nwk8DPWAdtiEjPxOrptNRGBcOiOeYLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDpCRA9TVsSAnZWagAA8kAP/0VGSQZrEmJAaEC+Nt5z\nFZJ/RHb/ICT03bVP8/EIAkRJ4/g0baiYWKzsgTNuY+b3QpKGiO1pZYPRb3Ys\nnQUJqD1O2rujc88KeVe+Iv6y/xfzUhdjpmAAZWNEbtuiX3WjqEDrmBkep5pW\nXvt0htrXcbANRWxSqAvs15PvJ15HlYiM1qaeJopPs4Ea448/YbkDwlfFFKPq\n69PGLmWOVQqP5aKfhTeLa3MgBFENkSWnKBV05RUM6GAcHwDztyoRvfbtzhAe\nD8TeZCfNI6ZTp7OKGHL6RuTZ5rPJAZD9JWxCiAbLkuWOCtenMxVoIRXuiSLu\nCi8t0lIOg9zZe7nO2XOhyW3YFo2brSXa/ESeUdy38wcAMwBzwtH6K+Jsg0r0\nN1nJaa97vRynmbGGL8UVls5WXNdpJzTPmZ5vyOT3HAJWfU/Tz5404uJYdGaW\npvQsr6cvopoaYhQjsX19qcNC8lKv6orsajrDaNjhQ31SVwIxh3Esqk0RV+fT\nwEiTOiOrK3JcGjH3KkhFcwdAdlNQldXvNGoP1SJ88pd9RQtwB0XdqRZDwszd\nAPD/GU2TivYzISUJjbQKHM+i/s6xZgLgmGiQf18xKT9Hh75nBN8cjGMkU7eF\n6jlNQqZ6vCscXA0Rdw4MZjK/cZPf/GG9rsSYkqc6Rruy+eS9p4raZOVDzgcG\n/KSE\r\n=hcoZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-collection": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.9", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fd8e9a1cfd8d3dc3c3b3892e8dfa13831d9803b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-NbyUw/ZHl9Pz61iQNCxYh1gNlCF/1YfdNTuZ1vvjB3tJvUbBqGRicb3pZ0mS8rsgtINL/JEUYHPxVxBjf/ZfeA==", "signatures": [{"sig": "MEYCIQDT8lOTqxx9xf6SVoDXmm2yxENEJ302IT8KUby0QGsVoQIhALF7vJncFHUPl0JFQA+sJY2nDNEAFddUv1iI+ZiSqGGq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLrCRA9TVsSAnZWagAAfN4P/1ihhpa6iYf1acaI9sb8\nm/xXloLaGmKWvRkYXPa6/XqbwRuBHHGeZd6uDiNQ+TKrWrh2eyHm3y3g7NeL\ngsqRLuZJNHqEfjT9vIwGTsyzSYHZbXCuXOzZ5S5iINTd5mRLiwXwtWNkDVBc\nwk/jK9U2PWiFgARjzM36qsVFvgesI6xlGNxbySBrLA1ZViSikAXOce7epRhH\nutviQKqIegWvxQFVO4C2LsaV/Vf4AUAV254T70OjLtTSvITlvSlGPDO03WDE\nuuJlOUSdWxow4wvENdzFDm9YRIoRjict8UyOBLifM6sXo+IR6S0ElvAC68P0\n+bJ/e8NcM41tPaDZ/5eOx78b50SV7pvVT6ajKohUMkVFE8fL8E5aIqlJnRfv\n1E0ijRnPetpJqZnCCE096Ay81kECQbK1bofO5we4gAVfHwemX5maxbX44fs8\neQT4VPzY6RsZ6orVC9XPeMEVJLG3J8Ts9gl1GIwLxS8RatSsI5dOi264Ob9Q\n6b5GeVLwsG45bhki9w3pRkIQUAf6VuDGzBweFRxFod487qWkh55ELFrybCT+\ndti9wLlfZtDkI00MhFF1DBprgimz6GJlRYLvv6MdWP+PcvsV6mp8Wltglnr5\nYFHWgHm+9PnMngSE6gb2Z4yqGnuhyAUGLXwTWruA6LTcblLm3MIXi3lvwPGV\nJaPp\r\n=kjhT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-collection": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.10", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3b74572331db13d3d28167a4457741739c386988", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-lUQVP/f1Y2dqnuhLYXRnPz9pFwK9NgfyDcsgs6aPdVfFZY7B1IllubJMAjUOA3M6dtidtwTRoUs6I8z0LiUeCg==", "signatures": [{"sig": "MEUCIQCFY5Yvv/7XBH7pEDO1v7Iob6KPeEbptpRaSqA6uj9EuQIgaQXT42yEMaiL1kbu/rlosi07C/Zzf3SjCGd3kIyaM0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0sCRA9TVsSAnZWagAAqpAQAIuaU/kCLjtnHekKZ3KS\nZV+OMZm0z/1HF5Kd9NXHS1phidDCRXX8JYUGTusEBL9SJ8oWC83+W1tAS/yC\nKC54N0DM41Y5ru5te9PV9CcRKTNVXDc6XSwO71Dpu2M7uEv5eFh2GgJFwljH\nySu0/cC48Unuy9+vADusnXJXWcF1qACx54NrtMsJsg3/v3/5bh0iT8Qx3u6w\n+dTfn5beNC0tQyCk80wpx1gdLo5XkigrtE4OL+drQRMDC0n5V4MRqCtqiXck\nT0oNccF21AKunyxANwcdxV03Gq481cufh4Z5iLNm2W2CNnfr9mrrK3NHPk5n\nXqP5XOVRCF+aG7alnQXbowX+j/eTDzC3vhfpKvuKYVTx459XttRazA0O2ZEl\nESXIQvwTTP43NyqWy/vlZIMWR+aSW8Vnn9xUazlY8qTqx4GAQhQXVJ/QP+HT\nInlQ7LOmNeHR/4NZSBGG6K0ixGovvJMVnF+mz/45Nq/y3ht0dC9MfwlBUxnY\n/o8yaRcN+to3CoyCaB4t1J6gwwjEMn8Igi+Zm96MKG7d5lrrks7n1jrbjAuQ\n3P/2WtAuDcPB/exE+SrNOKa4uIj8erEzmJ5neapryUWDDGonwe0rZN6CxwBu\nVcdd2FJ+gjtDRv7Zn2Tdtj943DLbNUR2E1DpsjKnWnOHLSrJLttINES7eyaE\ncFeT\r\n=ZzKK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-collection": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.11", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "48308beeabe275b182002093236df0eed7ef7846", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-l5uX7sqSkwYFcn3h8eX/fSD/vwJa9RsHmZtHG40BWzQkKWQN3rFUx3g93kgFfYyTZkQEn1MS0jF15Vn3lL+3hQ==", "signatures": [{"sig": "MEUCIAYXkYRyqmk4pMsCgX5aUZPwCfabj2Wn+NbwLC08W6sXAiEAhVKqItFfBnyVZ3+kOpjX5IG/Cu02oGfdk4zGS4RhHkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8STXCRA9TVsSAnZWagAA2OUP/39X4GE7X8Ayf4FoYVGe\nwSiUge2es0LuxUbCf6Oyx6axh1ocnUuck7+jPBql3hF7qlbuvedNaeZEwBRa\n8S1LUZLgWvU2hVMnKkPhsuHu8uwKQlKcTSHyNsdxvRaDQqDki7kjC1HEPlvy\nlQcUUur9ZNw0Vpe7beC2Mo6rSNV4me0iT9t3aaGqO03BvN3DpdGrdCVBe02w\nb6tcxvE0AFQW3bKpSW6ut8ca3PqnZ9sqOlrDL+Pr6oSf9mbGm9LtoIiTAp47\nT+KjVf/jblAbI4/BxoMDMCgxsi9WwcsPUz7AGyE4FqaxSYBAN0lJ4sdH6IEc\nKrBa4orf1f6kvKBUmjySPCBFTWLDpkLERGldwFqreVynLJwz8cg1As0oaysd\nWBJRkpZAC8xCDwfEHbKM1D/SS6BzJ4ErJB/9jneOFxHpGtpEIv3QXipr4ajD\nbYfJ1eF9WDypw2Z49JRLAAy8ymRqUjNNQe8DuhnHqxUce7iAFQ7i7H+HIpdw\nqXt9W1qJFEIXCQ8vGzHP+yqaW9DszjmOIWNbJD3sI9jyBSzOGz/wqcKHe99/\n3YnyQYgAMdW1YN4PZp3Qj5GxlP6t5l8qx3Uuwc2AyZX8fpee5IQOwZ+XYDYn\nCxrJmS3ppP3d6mO26lVt1Jir8v8PhcEqFDHJT3pH6GL1QFek1OFChEXiFi9g\nJpLk\r\n=6XP/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-collection": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.12", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "099e325dfa8e1c78bd678c3f751453b41c08658b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-qPbGJr7SonZuwC1Bo750y7NmwDs3hHOG2f7A9BhlJo3DIPMvkp86PbhAYHHtSJWBmFcDWuTu9y/dG0mOw/2LOQ==", "signatures": [{"sig": "MEQCIEgnEMBgXXxkWeBpGwIQQ2rtsCSu94BdSgQ1oOScOMXGAiA/4G2PNGKDt/2Kug3dhpTO7Glpmd4tl/0n6x8XThB8Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DaRCRA9TVsSAnZWagAAGsgP/jedE6FRyMIoyH4zGPdf\nOWEgMomR1y3g94NhyfSa3UtQrhMo5mbYx4LVpr66hRbuDu1YlPRFZ3+Z+l1Q\ndIbg2AFVTEYrSzzH5DXQCDi7mOJhi0PwGUF5m2GSfD+uYzsqcpAaF1kaJCmu\nTCp+Jk+ry72hFZoCf8uuA/aFwyl5MdhJ89SZ0C2p4z7abgdckBZDkh2yQw0p\nlvMKfvfy+xic+Pd+OkIfBltKHPsJBBne5u//uUlLL63mh7oQUY9XR2NNnC3i\n4Tuhe/jXC/y4H7qYrfSZKA49vDJpoDtqjCbL+9HC83edYHl+SZSRgros3yro\n4XHBh8PnjYiS/uzuAl9C2DkjPV04yXajmuURPt1fpTYDyog8WzRqARje+8mk\npxrIvvtsnIMcuXdBpXUuWqX7h09w6JKSCOBPMr+6GF+8xXf4ecfGxPs99T1e\nbcx8+MlKIEAfuLO/mr8nX9sj9kud9x6leDzy84zgbtPqL2/7wFlL33WsKCp5\nmabhemZ75w3zMWXIahfOxqUISTAxGyAVzhYL7l4cJ61OKr44cRfcHbvrdv3l\noLahdhXmiVlbNjAaIs1/913QjxOSXQOon0elRlk+FFnAPwQjklDXcHkEbCdM\nyzK//8+FfJQU4XRXas5T9t0rGP78dbkRoEjbuFS2m8wmcC5+L9extTnyp9Wp\nad26\r\n=2Cqg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-collection": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.13", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c148a97e7740df2b6b7186291c9fbf4dc7adb275", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-flvIKDxCMLivWQiU0U7fil3pzV8mB5En8o0hhc5cGMWqhPII+RM+PvrA19VIg2pHdbyBYHKuLzyXXCLzBRZO3g==", "signatures": [{"sig": "MEUCIQDtKiBMLRhdBKhFWu5HwEVEqq6x041xaIJofqnzgdVCsQIgaAYVcy9Li5aIyCKmsUeBSy7rktRzJcBRBDnnVGkGYKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WodCRA9TVsSAnZWagAAcQsP/3MQ2lChkDXPOyLF4ymg\nGlx9nsmAAaYBB3jxjH+vcjLoOjF71O4B8oro4BppckKqMcMhEenw22zqLHus\nEMrW7Ey90apZ899d5LxpNFAs917onSETgzze2UPUSte8o3WW6BzKxKIafJgI\niFFaxpLBTcP/eP/f4vl+XoL725GbHEH9J3H6pAe5pGoY7d40BX2AL9/nPV53\nRHwOXJJOhBq9aH4lRC3l+6Unwa0fEgNI/e/NLskdrvwiPQWUKpjF9+DdNm0C\nTwfVZV1C28NdNmpUkm7cGpjVfEnJO8EPvH5oBRHbePhcxIHafot3WTqf7Hxm\nM4jH9dJRvFiTRt/cqhRwIrQFywcou9lfefsQbX5teJ0afooDrqrcULIGHMnP\n+/7LVKfWnIkP0T2uOKEsQrbnVGxdLOKsvVjPoShhua5IjBEJd/yd9SK55n+o\n79JIoST2YjcpoDT/Z2OJF68eU8UoWRe2tlue/lN/xPoXG7jsIXsEfGbYF4hH\nh6aPzKFXK6xz7JgQ7nfh/Y1JW5EcNYmJ/ho+3x5uJLxNz8u5i0RiLs4hivFQ\nJDAzgGZAfRpehqPuj2yh9OV74zPxaNZUV7d+qxSqd2Ipm5oRupkJsCIzPqdu\nHH3Vl1FgFYYQzJqI3sqJSVfAHPxZmH86NawjHjZTQzQ6TtiI1TNFn+Otfy5Z\nLbXZ\r\n=nUlq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-collection": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.14", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "95020a239a524bd3b7598df610fbbab6342d9500", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-p5FnT6FuMI61T4QCKNK7QipgBT367P+BGCgIDs6adcOzGYsUGEMekWeh8eu3LwOger/jPB672tSFpNjvNpVBcw==", "signatures": [{"sig": "MEUCIBPoHEkdJm3d1iQ/otxwNLgnS6vHzhTOo2tsMCudlOvbAiEAjetL5rlTy7bR3UFXcgmmsIQcYJwrar2wwGR48ui7Sho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rU3CRA9TVsSAnZWagAANq8QAKTk9yBD6hirDSI97R+l\n2RfksdhbOGHVs4g+KLqJFDPN7gdaTDX2etcGVXWehLuPVT6tIoWmGLvFbHGk\nOgKOk6drlq6aFKf7It5aywNgAX3vw7U0yMCijgoz/zpR+orJ9kw8QBaXo8Ez\nVg+9jx6lquVYLHG/1BjLXSTvufg6BL/WBXw/iV3Lz6/5s6weDwyTgx0khniw\n3zeNsdAJ0EI7psVeZqWz4cxr21nUJBVaV0CruaDdAcFU6JVVJ9qPTSWc6cuo\nGEhcC4lHR6lNuRYjXBIEmVD2tcORQC4+IQvHA6SFacI4dHB/PK36iUzahdWm\nvvNUelU5bwBJk/Cj8paI1TxOHkv4BUP8W+d2UpbumgxZgPIwxzWY3LEJEDpg\nRDXuxMEH4IyOGt6yB6wKsQpBb+gSyMbNgdwAXZ0IUWl5BXGaxoNR1om41xR0\nLfv5VEQaUYMRI+Q/yokc40ZGTKJe6Ec7GaeF9AOansei9UrtoFweB0ONvezd\nRl44gVL0H8GVqM3iPqltP9nkU8ErqHwU6BXk3005KdKJLisBn9AQSu8yXB0A\nJMuX/LoGkinTz/nwk34BirAFPuF+wSIJbx4zq2M96Gp+kgErX14tqG7cgsLS\nwQknAFXrgNWFOX354sqr8D/lrLIFZuV3oFolpAqBe2bEIpFlFCGGMJcA+Cac\n8hfX\r\n=V1pN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-collection": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.15", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a8197da0f3acbce4509ee1b3af27e1ef6a0dd86e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-xGzzKlowZbClIH7uCVlRQ8SPwGLlpDW7nLVFlkxZkgsb8UVi97HfawkG8dDp9QNBxQ1VOUyd2/BRj7+L5H/2TQ==", "signatures": [{"sig": "MEYCIQCOEviDTPcghqJiztxbmGKdpPLQS3RIknskzh87mK3WqgIhALcSbRrOokU9e83WfI1YyNlqypnGzZunxUGqHhqPgsvg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/n8CRA9TVsSAnZWagAAB18P/29unQARP7vNO3lG9Cje\nYCRuqxPZRMPPbMdty32nNyKo7lB1vDx+mF3NWgNeJbR2jAhdXdynHq9yrddw\noc3TysZNTiDHbm6h9N1fYoRZ9iQFgiBczzRTjGAPW6k2zUOraTnO8kkshiW5\n/KIIZmR0FzEpCLQsiwkR2kK6+o4pioGh63S38s5S04OJzfDEYsxijSTKu5M/\noIbE0WFuwxzTqddyvh6kuuHC3pb4uEMIE5TXRVpYGQNTZ1Axv7LiQ9iGV2db\n41TjAY3cGuB43IDtXyiivSLyYx6hbaRMm9/gQu/9sYWIPWxeZWM9CcdMnPw9\n47U1xMEoFPVKGNGukQltq8aGVO5V25cExPh3VOLwGOnlvQwrRHKLnyukZJjf\nwgjtk00YVZ8C7XpWl5apSZo+uo83yf24W5HCXToKXAuDwLbz8gwpjc4849S6\n70v/2PFS4+w1XIGVXRqT5b1re3rDxCSYgVdG81h0u9fM/YXnJBc6w+oBW+1f\nc8V9kWoIN24cLH/LHPbUPcTFZE00l30kBa2BbQMVEJ5nvS7w1WdgTy0904ty\neQajVxfZUPo3aEKdSuPTQBGGL9iSZLBwuNT83zL4JhIDOBLRJXLyLGzhbsAl\nLMgTkv2xi94psGQ4l21st1nGB/HHqK/xa8Esi3Kw4VTJogTncNMo8qC3r7H+\nYKzQ\r\n=L2eE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-collection": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.16", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "020832a9902998af2052516880ecba65553ecb0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-CjqLQJxcrJ0RFD5r2ITPJ+J2H13GKF9XI5hejyV1r4xWo46BDrazvquYFzYINHvmkE45GLqVjW0SQHAvRAGDRg==", "signatures": [{"sig": "MEYCIQCCy6oL5vg04zeToZbY3oOaaec6ns7aDPYV1GuEJFOQAwIhAKSFLyieyHOP8diDJ8pHMyMBUF6efgoWQOY4KV/1jI8B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIXCRA9TVsSAnZWagAAzuAQAKKgH0ep8UfUcHXziC3M\nOMBtz2z1iJv61JBwKXbcCo9T6ruCb9tiIsNzcrUihJ5phw3qj0G8x1uqkUpx\n9WuQnYKWdJrxX9Sx0XywQn4hVc6IVM54XZkuTr3g5vYY+SAAKg9/wfH4tnbj\n0SQg/3OuGcjyVok2fecxOwXgjurLl9jrPfaa2ng4e7qiZlCgx5s0zjn7NGcA\nT8FQsiHC8t8cfhIQrLNG5NpthaCoCviDrhxtXE2QXmSLcha46CvX5yEU0cdJ\nDud2W3ivql+AjJNqiJgE20TsiL5XREqrDIPI4frnPIE07+NC8ZIVs51FH6sX\nbQSJ1dueqxVqfV7jkC//93+svD3zk6cn/sxJ2oTHSp9rGbWwE/TFMwfOnMSQ\n8GeLRd2xLK0VDPAhebdN8cSgqnPOjIqoqVOy/+ajxztGTms9GD4r8YApSdU6\n/YSE+/mNw/AeEIB4HcWhTYsgKmE7MTncxo7MLdaZ0+rcrq+uI0JbvPKxCSAi\n/9ZQIYxF9+FWfyqLqcdk+Q+e5XXoIa/DHtYYVClxE4LZGfJrEN08q+5WD8VX\nxIXZ1dqvYR66WGt19bek7+TE4nFSu72Xxd5wzdcQGIKYHyTnrXSbzheXjANq\nRrOsL+9aXEMtJCuMJnvJGOnRlPWhn8Vo0YrM7xM3cZSmHgzd/Qbtjx5oSZBB\nxyvX\r\n=g3t0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-collection": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.17", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f9499f211015669bdd9dc611f3d1ec26d7e84396", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-e3/MPu8Ua6lZuSUxhriTF/InU+zDHpawqmQAxlbMMJtK4FI6eniAlh4eH6ZdtlHDbZXisYgh+sJi4aypTvwzSQ==", "signatures": [{"sig": "MEUCIQCnf1zxLTT7c2l665xQ/T3Wk4xaWkxFtd3twgbJyoIZjgIgLZMbo7Mr+yv4TxCqwFiODNpg+YoNP9joSUpGtkX15Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYkCRA9TVsSAnZWagAA5fUP/2XHziX1oi1kR5sP2bNR\nd2XDUGyk2cohnLD7LWs+0l2yxxgXBfvoyCdCfzWyBSKWFujpp50vYbWk9ZFq\n5MgMzBu279DKo8S/9AKQry9VlKH8Pf5RvbBamQKOfSS/t53tjH4p0naueWFg\nQzuTT0SMcYepzzOnOMHAsSTsDGYEUQBdrlGb8+IYkFyp/TvCwjtrdRC5WNom\nrkYAuPd1axVCAmxWSv0tsR1OQi/fx5AgPtavevq9NFv+fcm1wJCtgxXWh/5z\nRW5OwhfGGvrZ8Mj4muoZdrD7C/OahHRxj5sscGO3wVcJ8VZ0apTgyEQrda3e\nlfEvD2o9umMIxtelwbAVJ+j9bNaDjeicnSLxWMmJNOLtOLC+IC9UDvlGOgji\n+0GUPkhT41ZrgVf++8ZzeUuOCnNqHXNoww81e0RpsfuVbNVdWDSCoJU2Gv/L\nN6qXVTAbK1G57njUbVSXcxhJDE49oOhJAI9F1el/avmcJFx1MdlD/rywcxOE\nFYL0qZeS8HkFyg0silQ6ogk/ELcY3WvAyPQwOqOk3j254F2vxoUYGXZ7xoPN\nBOHhXfqhRebP5pOpryaTEad+FfSCbMHC7KCwraECKJH8xrcrVHVqR8kuWuzz\nFxABVo8lvSCP0i2V3Isd7+Hs6ghPEDmUZh4ZzKDYTQowFjp3qGJ5XvCRIPJ0\nyRae\r\n=3hy0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-collection": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.18", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6705134165fe92216945dfd7505795ec6d7c226f", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-GVSqTq6VbXT4W5brVL379/QSvf5ZfICBSFQTp4U8DWXyZuGoy5+hEu+19wtJ9Za/dAdvzl8qdGLhUFIP4XOjYQ==", "signatures": [{"sig": "MEUCIDSLsaLGrmG1Ne6MwweJPJubenrAe3MFAiRTj6V6VD/MAiEA+7HuORkHIUQs/P7xevRwuEKd3t7zzJRwT86lEXha+NE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmql9w//btdI4EjWWmO57h2ThVAMN/fse1RVvCILWsn3EY7sudOZjxZz\r\nhahD3N/1HUBiHq7TDANlAAOul2tpqA+eOT6Ist4BmW6on7kb0KbuEnDEJ3Ak\r\nv5DRx/9A9lDXwL/U3kBMJxm/gAScGPaoF+LkHsVIeT11etnX4i2djcEJ0ZxI\r\n/4Y+IaWly8dLMPIzvdjOOwiPgcbbM0EsjUDlB894lX8K0AXdPDYFq4Lyrx43\r\nphg3PyTzowNfJZSozFOTp6M/xdPGi8sgAMHC+XTUowfHz4N4Q9aMnCfoOOpR\r\nAbXvZmfdm/eGO0W6jSXFwpU4GnAgDRssI+qjOdynzS5HSRdW8ULDAYLlUh2Q\r\nrlxx/QGjM6eX9eDvU63X/p5H/aDk7Cy0nwujNJpBsOS9smJDYda0fZQrTqHS\r\n+8axwLE7z8KT7+oNkhE09UkJdxjBIL6IlcljveE15IFojDtEOvEnvG5DV/9q\r\nXzQnVGG5mZM/BiMFhpZ1NjLbw0uydvdk5b7GbbUs3z1F9pqsfYRaRdZ11kdt\r\njcOcKsnl/XCokljxiUr+uwX6HH7n1tfyeu4aIPHJ5d6LcCGP9Wiy8axEaIO3\r\nNmD1oGa9crrzH7Z4QXz4X5hu8lObBSJtphcAdTFN4W3UgFmZuKSshfr3Vae4\r\nC+wzD3GQnGyshHJV3ncYY5FDr5jjSuMKRHs=\r\n=dIkB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-collection": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.19", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fcda09b4694bc98d539b4a5ab6e1c173b2ec4c09", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-S0YOvTwMenSO/EVeJQ5AUZxPOAXhIEeATnnEeuW9DRcE+n5FVpzVNLabMTtW2d4oXdFCtsnOEmT0ClrVsb9FTw==", "signatures": [{"sig": "MEUCIBvUuU/Qw8KRxjoBBM3CDIMY+VequZHQ7qctApHjgZ7yAiEAgsHdajXKs9mewbiNixC6DLQHvAFUzf7E64gNuJaPKNU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9DhAAlrhv8Vkvh0ES49YgWMcdXIAL4Tge8iZW844lxDyf2rTHDJq3\r\nj+s8fQypDeWzxMYu71liGq61Of0sCm/1Wp8Pz2gDmc7YfkEQDZGikvurqgOj\r\nU5IDS+Sl09arnW5Cqg1fqe+8bPFV0gRv7xEPLRyg48R3UYPJ7t3KuV8H4Hq6\r\nuIhe6jhWPITbol4VzlxzTMZyFkX4a5CosY2L0ns3Bqpy/6LhAxCMqH/wdt9H\r\ntMtPMm8fRWmu80PSOzSWqa1KrAKjrhTSy+hovqAskz/Ot1e8QaOb1GWv0LdW\r\nPYds3HXUIfdOTTMrN3U/qnTcseYq0tJ7qfASzlPT4YFRLqaB62YvzN6Ykmnq\r\nyr9HS6L4tV9A1Qm92UDMZ0CRnmPD82QIYv5wnVORE7zyEHtZQLn2EX8URBKv\r\ncvbtUEHzotoKulmV54QwfNuVZrQMjnI+1xS1neNlEbeF8fNNIW3iR4vN9/43\r\nLhEX9gp/Q+4YloRz7iEzxJW/X8BVE7f3lNjd/fdFUCSQr8xd1P/wVfEkpqVI\r\nOi88hj9lLX0MOTyIPdzenyDPe/l2R8To90AC8AXHdp7LA7GrTtAbwZceNA/F\r\n2H6XK20XpLezjT7nJUUGF4rVsGI98lWMflDnin79uL5pW7apA37OI8Lk9uZH\r\njFq+3efuDyJh2M4DpsDNOqPc7NMLYuac88o=\r\n=NW6E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-collection": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.20", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e570a725eab5b174466b8eb9fe6dbe8c6fbe53e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-utjMqTMpYFglBplJ1qoTPL4+8BNhwo4I/LjA+YBQ4uE/hdk1SDoa6nuqHOPBxNbfyWo6OhH1jF3YC70WFe530A==", "signatures": [{"sig": "MEUCIQDaSGBHooRknmoamQe2al1fbRr8pnyLRSp4M06368gJKQIgcwIn51A94jZxHo7uUmZucMBcWkXNnRTBmerHGAAo0kk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokug/+IX+bXM9ssADmGEDIMUT+CqLy8kUHirV908xM4SNLVYFQYshg\r\nAb0Pr4nvblMss1cDfUTfh/TL5iNsW5LR1b5swI4CnG+feAxbALOqQDqjy55I\r\nQGImYfe+ry9g6OXapDJckZJLBCGFhFC3vfbk1PfhtZnwYTgKRgLKRKDNTC9m\r\nM7pgKwyxPQlXfauvbG5uh/8J06wulp3GaPXsu3OQRNRcNDbmdDXT+xvMcMiy\r\n0yHgiKLcGxewaj+d45kO/984NId0mA/ZrKIFs3JZf7/ZnsZ+IugQTQIUdhD/\r\nq/VzwKc0SbM4GsCpmz+bUEjxYWXIu6T3J+xu4nnxn+DyDjM0CXaYcPFs8Kwj\r\nGfr3QeRyH0b75bPCRpKqnywgeDou2eFOuDq4yePowc1TWldVlxQJCV7DlaaX\r\nOGJBpnJAX8hhwPd8Tumc7frN+HodXo9EA7dMIQ9bckL6ackjJPJZOcBpzPc4\r\nzRLp3YOaJK726GsSgIFyEKbYGqVxw6DiXuox/8phmu25zZxW7lUtIkpzYJ7a\r\n75T62q0O4E9ZVEiAoQ2Kh1GrCoorQ3evtUZiJ5vVWGc1xZ+UrjTVVinvT3lP\r\nE3yFFa+PEaHnZPga2fvrccWJtlwZGfQ+MMtysvVYUzWjW9+1LNqDgPiGykCU\r\nEi8Eln8ALPPRm07eZVeigEkecT0H8yDycWY=\r\n=c/hs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-collection": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.21", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a5f6d09545ee1c35151ecdd50ef5404127970cde", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-cEBxoamVG324jZBrYrt+Vo4m4pjIxAveCBBCANaszou4LKSTADuAEbVbBsYAFEdg40USN0plk8n2Re9DxgBokA==", "signatures": [{"sig": "MEUCIEvvv0ySh38cAldk6WNsG29xvUdKb7AFlmuXxwrctvPEAiEA4C+rBmo+kFjnZwwDHnr2//w4FiWGU1hvJKPAk/2rB2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZyw//QM9l0GMeDfu62PEvgsyRbI0kxwFWg/XFXiMRxTt+uNtknvuP\r\nDxFbBGMOixx+a3SJWR8d3kNBQ6kJtikONL9O97gmWlyiydmxysG+b5iZnKGy\r\nLX8B9EK3nmgUdaUltHwB9CY8FbNBHDpIf6565tqlBxnpWZciOh8FWWvdzE+K\r\n3Vsxk5PjBNq9WuXp9HNc+nRnKta6RxgIy10gjGWtWPkUcvhSOsvBHOxkGuLe\r\nKSVm5xEUOXE+tzKlLkt4sf7Px6QyEKlqi+IvEIT8qbb3WC9xilrpj3z3spd5\r\nwUtBWbANYJXitrPNCGg+Iji6Z6aDS2YA0v6OlSaAUZMApOgXNhDm4rYxXHVe\r\nSMdZov+GCMPm/+bXDltJ75Pat9b0wPk+NMA5O4oCtFD8PpAG4WKOBJXlHsMY\r\nMpirXgrvTfncEnJqlFHRSheWIEW6zCFBzTaIjfBOmOAO8+xvzN2L+ddyV1S9\r\nXPg9YKUmQOVwKxNkl2H+uNMURMAfOtumNvj7fZ1alVrOWnjOfam+O5Ei6R/v\r\nGl8bAyRPMHu62w8e1iDVuNVxt7KHKIivJuGb97XXhaRra+ZCNy0yZRTBxk2L\r\nmCIqgfyZ+sZVFIsBpUqf13LyO/LnFS51LkTJtLlixvbYu+A56XYcBQ7E+Gog\r\n/ffML94GwgPsLV8UMS9QcOx2f6gbbRJ4lyE=\r\n=HUfk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-collection": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.22", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "22c14cb0b3a8cc57f19f311e68a8460f5a8500ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-VUVBYfz0Lx3moeJqaX5HLge/eWdHh1opjcZlXavxM8ZluUXi7w1D98yBt5b39O/Km3C7v7X5gadeCvWyIBU6dQ==", "signatures": [{"sig": "MEUCIGNb9vFS98Na0uQGpciXDNX/9YzcSCAKpxYqWjj8o4xQAiEAnp+fkgkFBZSkiwNiKO1UqgBA0LGDVdAkxUg/F8rAYeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVRA/+LD36hCFpoXdoDutEPISKO2DW8s5A9GMHNHPk8qzT2ciEDo3I\r\nASf76zlokN9bVgcqqWs4e3hx1ihsul/41QqUEWgojrEeRjlqi0/BuxR0Vt5d\r\nWoBx46DS2Ga4pCx09Dmb2w98GfLfLhRpRy08vMOjCj3aD9EKmZ0hlNmSxMmS\r\nR/xAC1fg2bPOFfqyEhshHGV1PdPbn7L+AE0vIWvy8eltg6ILFfi49N5jxxCR\r\ngZulNiEsH4q2pTfwEnAdA5vbaJ+l+oFDcECyqRoSOIIttDEGNmaKO5Ydeov8\r\nR7aEwSuO6jF0WAlC82upYwMEzRnL7Fu6ListY9dSlcIT4u95DhAmATqOLQdf\r\nH0Aq4pE7h3wrdoi18LdHfxR93k+Td4pVvEqif1rg/8NHX6429MnSE+p+zvDS\r\n3oNqeom3Aa9UK0T1toodDkSWtvP8l5OkAZp+GoxCVum/wX//5bxnEVzihiqI\r\nYXeNaCOaBmIyuLCvcCezFlJa4Xl940WwY7W5+2DAtKsDjrVaEwa0wxhu9177\r\nUKioO9fuSADAkoSaBgiLHwsDwn6lqPnzoUeK/s1HseX3OrSYO8ckdTMiXlp3\r\nodwiY+1IXGx0h6DFolAF4Nr46yyDYtNP7w1MlMzuXs/SI93twGUGFGL3cOZA\r\nxdFN5xQewbqwHQjbklfQtEm/26tsCZ96A88=\r\n=Wgne\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-collection": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.23", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "92dbeedf1d38c3665cd18fd2866b6578255d1225", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-4XFlp19xF7rcA9pCNSs9+rgFwbK38ZNDqi6/cyW10K7y3d7d/z2mFo9oWfH8NzylhH19uBH+eSA24RBAZnYfWA==", "signatures": [{"sig": "MEUCIA3wMEJUhf3piCcSV/1pAiRXTIqEBuI/ydgjfXdjC+r1AiEA3FjalgPQblM8juLsUm0xE8ytzl+Rjh4fPM+inYshTeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpEAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpqUQ/9GG7dpfv6w6Q2Rjcd3jRKDq1uHCebpr/Z5jXxIBG0OzrVHQDQ\r\npdKhgdj20kMVEL5aIeISt0ewqKewOgjwk2ugUFi6O5xCGbk2Iif7AorjzgvK\r\nyAEuNcedA1Bx8rR3wtbJRBJRJz5EOJO8UzZ2IMF/ALTpjMCoTYbo2DsqGuW7\r\n/ogrpVa4oirZzv84CmzxpaWmuOxVLRi9b51rHMApo7THzhHQhiJBuIZQhxB3\r\nua80oJ34LNg61O7QeBr9faYIT7Kets4hHbCgMv3fSzptIkXhzatnhZ19d4XF\r\nL6a1d7ECVtGtdvUYCAVCgpefSOZbGYHuRnYhuWNVgMUsdM2ild9EWCYe7kiw\r\nBsooJfuvt/i7Y6acjoNAyKcR9duB9WQEHagmBUPWAiP5Ei5Z0hiyGmTz3NlU\r\nflLnjlllrsxSpLC6S8iPzqna6t+TnmF01vF1yHle9rsElXCAiO0zbBf6u+Mo\r\nLIZm+rUJHZV8ww0kNEYn3RqGMETqjDB+FPB9jh138JWVK5CO6tSOkH677MZ8\r\nk+Xop91X5utFgNHbg9tFi3K9NXaJgXRb60CRoBFdITr5amb11daUIfXRmhaC\r\nCZCZoHc1HrnhjXXBFXLCEgk4v6zk3sWUmjYIxRJvSO9BExsCvETWD41vHN8m\r\n24kK44FLdg3nFsWASa8KUCAGxGqJMkbWw9U=\r\n=DNEH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-collection": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.24", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f87e00e9f3924d8cf1be8e8b488c3de4f1832dcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-DKzT+iFOinXve5ePj0TNQ0MzYqKI5xxnS5aXROCo1rEQxuwLYWeRLmwbhnyphnkh/Z3dO/SIEw0GfUlmm5m47Q==", "signatures": [{"sig": "MEUCIHBE/L4kf0qxip0oKSug9y6TzbBmRXlZHhVeGsh55I/5AiEA9ZRxowuAjV9tCeHOOcdDWIuv0/4cgFnhRgrj0J+Sews=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNpA//bIeHwrNmMXrLiMmd4etN20YZJYVHbPenBOoFxF33cVB9/vk6\r\n1lfjTuEGtMZMeyS46d6EXWoQLUz6eSnqTMoY3qPjwowJE6MnjOD33KN0yQlo\r\nZ1X4iRbzX29+5DZw2hx4u78QERvBWfJlr0eCubJKUHRlf4mtxre3lyo53AOG\r\n2s+0E6V6k7z4bDabwflNzrL2/CswOHToa0SCzMm3Q1Y4qTGvR6UkFWbCRnC/\r\nXNL1LN7eykzzcxCQAjheiwlV9c/T9YtUJU1HCIg5th8SuJDdmdniSh07hw4q\r\nQiKTJGT3fwlPny78b81WGXAZLXrTxImfVe96xQrASupvn3xyMLCWRi2NZVIO\r\nxnXynN+7X68GIWZXW5O8iJmWgG8Ts7EhaPBIGbUGTWfSoslSKSfe/kjIqUI9\r\nsZm6GX47aBfaU+l8lV579eiqHu/2sRuVvM5WUMUZ802QgIdDDmwql0tZo5a/\r\n695rRJFhugrHZM20gEUHV64lb/vYuLJ5e4PDy+H71PxNYS40mPq8IOEh4YNi\r\nVbhifitLkQ97ei+PGuVvBWN1vXdDDqj/JzyisGp89vrVON4/X8ngGo77sKMs\r\nLMZRg/9xVZnmdGtPQtTZwMvxwiwZB7jvahCDJj8QBFtvUvcyffh62Fqz4qGs\r\n80cjV3S5tJ5wv6rnC1RQUly4isfTOKE8NPY=\r\n=L8ta\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-collection": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.25", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "68bfd6486818b0ab9ad98a8c255098ea6e2b14d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-HTAZ5bu5B9MzzhMKv5H1vqgZSLeJf6OHM/cfyTjS9qVcKr8AYw0bmIQ7n1IFuHGZnuFIUyNvMJmkFWdbkbVs8g==", "signatures": [{"sig": "MEQCIFrWDHAZyLCAnfwUDTPvnUynMcs5NT68o/rCsty75v6FAiANfztFIov2PfBH/egRRR8OdsDW+Ffyrx2e7/+XwUk8wA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHjxAAgujNk91aT+s8F+5mclI9uB+e2JZyxO9zIlc2QITgraTB0oq7\r\nbJCpLpREoV7Zpv7STWc2taY/yHNlndkmdUMU1KnCcfmW2Cch1NWneVIqIF/M\r\nxqaw+t/x+9cvABwLS94jHdDPmK0SRpdq6YZxjfDv7hGMPfQB2NadbEApu062\r\nLkCwHT4VQs/BP2DcjX01apGttO+wts+qVTbyKpzwfr5k7ErFYiDTWqW/+v6o\r\nMLfpFMazIK++852A7m67SxknEg6pjJTOxtSXZGNbths1hDVi5aj794xYWJYz\r\n5ui57qIT02t2NJyFADPd65ODd7hYXKJj4qcJ6pxwNDDxo6JVV3siUyFxq0uY\r\nYUGAspv/0M89pPW5LT22x/flmfKYDBTfGNrFgMD+gwS+iDxB/5zDCaCBzrXa\r\nzQ9yS9Ulv+ywE0N8koOe/Zc6dk8BbMAPjX+wzXmXYtip2Ok5kqa84aTTZwdx\r\nELfRGGDv0y+toz69j4MDpjHT56Yg46oRaL/A+ZQz4vHYQANXjUYsLmRCYK/2\r\nfMKzZWoxGukr4VjEaWpV1DYVfOK5q2qQAdeVRd93ts2FfPM95kuuCB48rGBR\r\nhosy11jj/4pbRnRCLBfqNuH9WuKWOk02h9PWdChY/Ji63AB3ht8NPICG+fMU\r\nFmPR7mLOqpElzfLn9ouQ+HUpYyeG7gzMMDM=\r\n=cjqy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-slider", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-collection": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.26", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cd802b4f2413a320b575897f76e5a17bf85f8453", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-WC3kzLIC+dFgum9Rd7fePcIqd0ITO5gsN5FMMHaB0uug2BLsWhnqDsuDYPlTFWBNxgNpdCAreXB3Htsl+NSOvA==", "signatures": [{"sig": "MEQCIG6OwN1PiqiNS8HwRu77PmFRr37tlERVIHRI619L9B40AiAOMmiZQJkZnuMko1SL1noBzu4ugtyoBxqNxwe8bkmXDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Z2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpCXw/+PWk7aAK7TAEqszoKchht0YrkLJHttw+Mca6KJG9L4Np+VGVZ\r\nehdUA7CD30O1fC2VhrmKCONskjEHW8AntyZUEmCSVh4L+xTaQp4hBLwEEHT7\r\nNcnAC5YQqos4mR2Ae55wGMUnwSF+9ufmPnQUGmx2+0HGi0QqfQaqDa5OIDQ6\r\nPs5fofv6IIehcvmfwrsQowCsiSnn7OS8oZnrjzwi6zCbn/LsBDLAX5S6q2AD\r\ndvx2VvKoTnAKWwyB14J+FEwDgv4EHOrFoZzOWkHPcsYJ+5hKP+GcX+18D7OE\r\ndETCcB/6HMH9tqIA2dLR6flnssN8kojuvtts65ygdQrglMMrLGlhh0sypGAK\r\nNztqTf2PJgF93nd9QJif12k1o9g6QEWTFvr+7qnYOixiMIr36PCRtAv7mX8u\r\ne7DIxPW+/aqZtV+MpxyVvelic6+ft6EWWTtnTa83R/iEYpHV7E7696QIHPcf\r\niGsADlyZ0JJnmPM+HsPNJv2C2AdpETR5indYw7+HtOQLDZPSn/Q0J1j+2mBj\r\nlK4DVaOWdagMJIXgWCAmgJyPrgdTILPXDF4orPP1x60y+vzaWuZyEupK13Wi\r\nd/3bVs+txu7qj5+9mADYr71gpyZu5BmJyM+hjfceiFn6LqMZSIKtSxuj0Wg1\r\n7U7tMfMqu/aJ70aeYIVMy4S8kvPTNCT/QvY=\r\n=Vma0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-slider", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a7b7a480ee00158195794b08cd3f1583cf102518", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-0z3bCcdrAi+FIcoLXS6r0ESVWuuyMnUJoCsFm7tC7Rtv95x34YtaI8YfSyQmzuMVS4rTsNtCCTZ/s727uRaVkQ==", "signatures": [{"sig": "MEQCIAgvXO7aHpml71usmd//clN/2740nJguJJ1UJ1SOKgj7AiBezjYT2PcDzn8QI3ZJpDOXKR9KOnGZMeZbuvlAM05+tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102852, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLFg//Z+0HryFNmnOYOmSL63EirFbyqDntM+aPqNm2oC/wciykB1U+\r\nkxow6BkK2DYLANYquMQ1hP1rGN09qzzzM7aSKe6AoiEq9LBiVNO4YMezFxIW\r\nKxXyy6boxXBEh48/ijoQnZT09GW4+ATnA3xbYBzoxZGcYC1Hcfo6LcLqgL8Z\r\nnpLgGUv7EU3f29Wivor5D9zS+4RCmpOqRqiseuYUNd4j1DUfNMtbUqTVupJY\r\n3nIlWqH3B0c5fEFmr2KfOkMVNDsDcv1xgAe2ZbFJ2+9ehBacBP9KizkLtcQa\r\n+R2PaxFVuBIc0jQIO6WaVvebLorgSeh0q+bJi0y7BUum4s1vT2V7tlBy7osJ\r\ns3et6unxev/9RFVWdZAmd7SLM9+61gF8riMV8I8bsij1jpHUtCzZ9MdnSeR+\r\nDrqG++CsR3VSXvjo4UyJrqnVZWq1EhXT7SdbtynW+/7umrOWQTiMWh4X+yLH\r\nJv98cdGtYgI91LwSpuksQH5ncHH2ffGh7fAG6svQDm7H8xZ4+1K1p0iEjXjb\r\nPephUmXWQXsZorMcuLAtn3hHk2b+Jjjx80ko+Dt1nmuFEjR9yBKA9k/ulO03\r\nQsj1qfZ8WvHDh9mlt+1TD101b27e77iu75PJ31FDN+GUfCMq/+bW43TP4uUu\r\nUbkIZL9IvA+SAYLdpdVsW4Vr6M2fDsoZXHk=\r\n=+24V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3915d900d915449ef1e5d9304bfa43e9d0f22552", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-FgLhnfwcg0QS6NEJUGAXkf3PHKQiP48Okqoxcg+GfEGOYvyMru5kSpNn4/dsz3M1wdiGSOoE9TUalQMHYqln/g==", "signatures": [{"sig": "MEUCIQD/tebLZHQvcmaFmtVUZhAVZnkD3aD6+zkfwJMVAVGdsQIgQ3zoiTrnl9QKhOs5+lao8l4dzGesoiEOjobd/PuLHIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD7IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1fw/+N7FQa4BJICU+lTwdWQXkaZKS8hUBNosX47hlrRPUuTQBFaqN\r\nGVhYFClgfJKXk+JKw/HUQ1SH7SBrYrtXmjXT3W8GgcPo64GjedeLWeUyhjCt\r\n/nYEoLjPUkwDbOmSFhv4AoLdOpcsKAr95/rIab7UAiGfYIK8waTILw6myutC\r\nmQPDYuw6RWiYklR+Ji4ey+ValFbOU7GIMA5e6GxBeW8iQXpTR0VYQvj8jlHL\r\nD+jS+r0h3OaPNWu7kvpqplMp/6AUFvPLJIt/uYAbpoBmAVD+nKzRTWWogTJt\r\nXhFbXoUsuCLAovOm2drqlWxDfrFMkyLmU6jNNXfOvNnAD/3d+CXTThz2PZBt\r\nmN2Bg3TfLXyw+dZuDHgab3WdJSE/9VoodE8zZyBJGRkgTroStC80mENw8QZc\r\nEtdRYc1PYGGcONCLRF5qT+o+yScnxf0thstw9t9c+Dk9aJKzj8mdYffU0oeI\r\nTgSki9kjjLslLQrizQ4ks8OrcnY3+0EMqrg9pNFfHqnrByifjONEQGmjjw4z\r\nWC8X8Vw/1/ARYx/srYu9gtayCAiWdjClP1Kg9NhzdTlcez8mAAwfAMo7O6VA\r\nOzDKpVz8lZwAPLsM58kcQDbHACm0ZpG2mClhRHM4E9xvCTIJddoKMtc7Po8V\r\nFmi+KjSzaBvgAly5bJmxGKf/EP/4p8XXvFk=\r\n=uS23\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "448967b0e847aa3d9cc94912c214406e69cfd150", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-PYv0agYt4Ds42cJ3oqJVcblNGIYRlem1gv5nEwZfUfNRobEi6h4TC/kCZQ4BMtyecxkeUVA4a22fyldY966BPg==", "signatures": [{"sig": "MEUCIQCHogPO0gh87xFzxiKPvDxviCCgn15d7TkMtstonMFaQQIgHdWK25dVnKIFInQqapqk0vx+hVucZ57Op0/6QpbFGwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEIPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHBQ/9HyEyBMeWVdPpjWun3ZpqaYfFV6GVnc8gYPuGQW63wl/dc9kH\r\nwnjRHUzIHrx8IjFVk5cB50t7fVw540DwEF9XG/AmtCdVQj0Qt+kz4h3I3AkT\r\nx1IcVP1Zyl1AD9S2Bs+mzi0esqliJlmSAo0kobkW3LOB9lOyDJCMn5TpJtaU\r\n1lvOsGCmoE2A+/q26Gz/2ol2+zQrtLgWsaUF43JTL7poteNQeChixqbMzmoK\r\nZiE7TUz5siDrPv78U1hCKxrRtBnLjoNv4Nx3gPl+ivWxitDmM3zUfkvJ+ZJ/\r\nnXr3KzmK9anjrMArerf2s6vrPETdP/2DnpraNzFLy/aoof4hcm4VuTrIOH1v\r\nXb5XhzzIC4SG97skQQFQFzZt6AdL+v1GXzRBoRKYyqDtcFUnamCFFy6Mb8r3\r\npRvV7kQlasDF2rEjjfUzDxiuVxzlI6RScIceL3rr7MkO/He7HkZ+RDlc+fWd\r\nKJCdElANs1mbwIzN5be0eZEQgpFKnr39ut+wEyntTMP/AFpPIL+HZbjs3ePe\r\nSIc9YpLPN6bRWIKB3SHIJE6np7jMkLzkM/OEl3KAGjMBwYZEykUYUQCPOES6\r\n2BVm6ytewlP29srf2hRs3sTlY5znMmaIqkfCkE277MpLPQ39A1S7vosR0K4P\r\nKR4T8z9sH/s39IHRZ90syCtAkmFxKUuIASs=\r\n=gR5E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-direction": "0.1.0-rc.3", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-collection": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bd21f4a23e0f47fc881389d3f11ace97ae22e792", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-DYthJmJxwQW0FWa0OxSc/wMQOB/mfu2l2DgG7gqjAZiHTkdSImGi0u2sdzMdLKWJScPGqNP6Ua2IgwKI692apA==", "signatures": [{"sig": "MEQCIFxJWrZwVEgzuaiNZuFER1JV8To6yi4wMnxjGn/UxwakAiAk1nbNIoC0wS+vtMu1mnd4kuOcgTpFoJRbBeGU7+SRPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 102926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUTvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9UBAAgk9FOhi7cMnFs/N+PEPD5CXBuvhjIX9lh+qWsWRDaGqTQ63c\r\nCTBs/ew+x8yk6bQg6g6rawsjaFOzxvDtKvd96/UxVtzykPaqZuaUA5kDHoKM\r\n68IiFVSGgsbLaXBI5NfeqP/aTIGKcs7O4QVAlLrHYWvUuNT9WuP0cgiUBjXj\r\nC2tQhuSow7pI7bs5BsFrn9uPwDGYu7inL8+P1YxgK3LzpFBQvsWIYqLPxPBV\r\n9iv78U+NJsy1hFkuHEZQngRrNZAkgVlDL0NIF3CJlkXgG//H7y8WRKEsRiLU\r\njgfnnf1H5LxOcuKstjJ+VH/9UOnXTmCWbZ10rZtya0eflIJIVnO592DoGra0\r\n4QEJxfkNWzSbkiwnvNOuMGPzNtey16n22OBH4TDD9U7SD7V0foHXI0uxzQIr\r\nHl8C+kKWUZwpqdaGiFr+Q1zZ5Ry4n3K0iEcWzq5kWb/NE0g7TOPAfvw3wss/\r\nvNEt0+ULgN6yVwcB/QMobUq1cmKSbdsPRpPATmv/wJcgcx2Ek7c/bVsGHYm+\r\nsiTOnoN8nkZGjdGXXJ6k2LNANWBe/zrDx4dSxThl6ECAUi4XyufQ/600uNF+\r\nXlSNg3G5YFlS9jm7bg65iAuv792vCQRLY5tROXma0O/J5jLLcUEqAs/W9eI/\r\nYhVYvSA/oQxXQScdEsA9lkn53wNWkcGph24=\r\n=yk5Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-use-size": "0.1.2-rc.1", "@radix-ui/react-direction": "0.1.0-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-collection": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-previous": "0.1.2-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c547dd92be73311ed0cd285612faea68f10378e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-uX2BKtglSK87vnwaBGJ+rktThXgjBw6mYeK7ZNpbDN6xBqLsCcBhK8erL9Q4EacCSi2W+ydPnxYAsbYDSQelMQ==", "signatures": [{"sig": "MEYCIQDJkd6MIMcQXm+5mpIaMdtbE+xRvOgR+eFNbyRQAt5aTwIhAJh7sfzRc/ytJPyc3uzP9GpvVtrbIl7gDBp5I3yJNs/M", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAR1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMhg//Zbcuo2fvSwV2hmZhVXi/4jO9zH5LE7TgRj4Yr3K1WHqeMYpv\r\n9+RHK33Mv8XwMP3HT/o7FPNg/wRLWC6oe9aV550UVzW2ZyjPfYVV3CCPJfaK\r\n//yvQVPYwuZcPKR7NkLlFiVEoBmqgmS+JB7NFi90No3PjoqLrd5UDy+4a3W/\r\n1ysDaS6wb4U+O6PPKXv2POf/RPMMHBXe5XFcWvEpCr8UblVLo/q9OaNrF2q9\r\nM3vVV/0XMtua42NoFbeXXpukRZyNMaiEPPXfEfvYyUOhNa3fogDEW8Px1DUz\r\nreTGId66YWy4zBztVuXr6GQqqKI/Tw2RfDfntC/QrJbAxNsU79AM81dKJDDb\r\nseTK4NNcZMwJupPOw4ruD8uuuP7Ksu5WR4XbOcwvfdsWEq/NORJxqFC6dZFq\r\nYAhOxhca20cpdvyoqefPyb60uowDgZkjgQVr9lQ32VeUuQIq7iQRgt5vmap3\r\n4v8dvK5JFrTd0IeSP7q+IdXGWR5ucZwEecsSos2pxUlOPowLWX3nf34xWDDa\r\nBRLS/PIqc4o8BoVSATLKxkQcbvNbJR/vr+xKW9ZqFEStlQLglAUc4e38qPwP\r\nJ414no5CIZaExAwldxdoh3wpriKStFynELXd/+q/fYRQCALRiYLF4zWLg7IH\r\nBb4g3UMRGlsdMegSu3tBU+uvzA+XpLg1A30=\r\n=xto7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-use-size": "0.1.2-rc.2", "@radix-ui/react-direction": "0.1.0-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-collection": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-previous": "0.1.2-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6eba7ef04afe72423c861472a26a68cac0877694", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-amV9gFOF5x0rOpPD0N9pbUVTwtl7ZHr9oHr+9E+14mECcPCQFyGYMx8N4ciF6FoOsBBRA1eKIXTg+rvO2v87RA==", "signatures": [{"sig": "MEQCID39wZ55qkbLSIhgLzOFRUDyIVZIvqLy+MSwVODjtSRZAiBuQvCDTtNmfSYxwccZpWCWV1YyzRBjVSpcRfEMp3JRvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103059, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2sw/9GpVzivEfm8oQ58+XB026YGQ7K9xaGoHfufBMzmsCYt616O9f\r\nI+v00lWuMx/Jeayae0BKt+6kViqipIlTveQ0XyoobejWoys3pNONSmN1EugM\r\nWLo63V2sbzuiPZk4+jBzfBwQlw/rNB/+xu+K+0AL2UMWOoNlH8jpyeEIj4XW\r\nTcmc/C/g61/rO8L7paS3p6yvc6Nqsta+qeKO9TJ0X+EJOG+NI4cVERxDwAR0\r\nPf17Ja/T3BuYWYCZ/Y3JYq0SX2hx8RUA29U3JNYXI0jkAj3EThFqcs4eey67\r\nukQkaj0jNCwEGBsQ7KVDSEr5JyftMPyZCjUEeKhA3yuOj0SYhz6KfEIfLXQe\r\nmYHx1AVQDC0+wLci66R28cbTITs4aY2R/1g4VFv2w0V8D7QaD4SJIosskBjd\r\n9jzfJQxfv0azTvJ+Upmmk4zByCnG0hXX6e3Rihqfzx8uNSBXHsNAlz3HgG4q\r\nrMDOUK/N23WpG/IfRmpB9oq6wgdX6213LtrTXzgdNYqOvv3BxStG9+I/2JuS\r\nZ00Udtv08zfc+E5djuVrePuqmfDj0ubRNv34i9YhuYbokKZwhnhCbNWbrN/t\r\nZxsd50qdPi7YMvLs9ckA6dmthdR7FJ6IeNfB5zzP9XsgdH1Z1rJHk6W35S//\r\nCEnv8qa1zOXs/xw5Qhp8SSb/QKFGJLPMPsg=\r\n=314c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-use-size": "0.1.2-rc.3", "@radix-ui/react-direction": "0.1.0-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-collection": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-previous": "0.1.2-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d432f39a3cab874deaf18dada2227d9ccff8199b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-v0iot3SzucQUBB/XA1IiNv5TN5y2gVkWfjhHP1xu5aOuNkL6LtV2u/1M0HLITt1gNe7M7OaiFnqmRrh86ieYtA==", "signatures": [{"sig": "MEUCIQCKDFJAhk6U790Z440eFcYTOQ3HybIZ+GPH9EkRj3pEWAIgBfyWstJU+k1sD1jZ0txGk6LWV2lhnaTzLVpT9jFok5Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZYw//dIo9FDC3fU81giAHaM8Uq0fkoJgfVh1RTUI6Tk80/YLYqxiz\r\noi5psKHSmnwdSbT9WBkztBMPqTgqZ1NKRszOp67RQsSTAQ42LV4pdqJxQp5M\r\nAJDEfRa03w5Lf4a6vNhjSrudX8ZuyS2BA6Jnp21mJB/YSH1TcaU60EPtvwIH\r\nMlKQMaWobFy86nDc82CIoebGTwQ2q78gYIPhG9ygF9i8+f8Pwae/KjR5NevX\r\nJrK2ohbiZy4xw7Grxvl9JGgOMJnXLWbOGWBsoZaOFPc7yMQXrrat5lC9+qsk\r\nDWuPITSQQoChiioY/0cX3XW6CbqXRiB33cay7dwWNvCO3WUletrx0Z6GbHg9\r\nfRFdBibsywYlMAO++833MT+Ozn23x7coTqxZeJ+6KZ9elXoUSLioQUCw+KTs\r\neppwAAiw76MbXou6sivIs8bI5DYn0ZzjjTMQ+KT1fnHicqZVFt+oPsqUIHW1\r\n8LH5qHqanKmsxFMyWVS6WMwRwUhXXUGi7cnWfa2p5VWRco4APj0+jAjO2U8/\r\nmUnCwObMm//2qKFJQlpjijlp1JewOpOnSAFpes5kvTcMwAEvOaM9HE8ysVPQ\r\n10Juw3cvLmxPHPEeiAVyImHG7OiD/x5gz8r+MCfZS6AnIEM+dlbi8inU33/j\r\n73n2QezuFxDxkWt5zOBV/M6Jx9W4ktClm4w=\r\n=m+B4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-use-size": "0.1.2-rc.4", "@radix-ui/react-direction": "0.1.0-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-collection": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-previous": "0.1.2-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "67c81b18e660e8d68ca506918dce0ec910df3194", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-jwYz3icuGOziOLFYFqdirTH8AfvhKkzwOPBMQ1IKeqzNJpgXjVboPjgfO0Sq16h9wiPL8MuZpWE0Sy4e+TK6DA==", "signatures": [{"sig": "MEYCIQCUPiVXJVwpu1l0i8eo2WehgYclmw0kf8ZA5K7F/K1Q5gIhAOVsLYvTuIXIU4TtBrHL44AtoYGF19peA6GnGliZEnhf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRr/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXdQ//bbFxMmVplCW+lHCwEbWpwMBBNaxGjOs7t76vFv1qElYvRoO4\r\no3+AKJOIW8QfAgbTiwhUtIZJwCcOyrKmpO8NhVFG7V2tJx943SxJD6U1iYnx\r\n/iES8oT57DaC+S7slM+U7231Sv/JxmFH8pCDnNhSe9zgIFSjBO637ZgEjvNK\r\nzuLrbCsZlmfmGioYlZoPULZ+TxnOhXRMKEVUerxs4rOjvzK9a8DWFJSqVaUV\r\nsf871YSrdrNH17a0tDDvR/iDwEcIvbojOZkVEeW26gV9KPpK/OfxJEvzG6N3\r\ndhSnLSMVE/j8oU/HHGP+iPvyhy5sXIiq0uq7klEZSXcz2nnwze6tEYdYFMDa\r\nPQa9LoBWC2+HxqE55XAhfNoPJGSMCdIQ/MZymXTMvKgkvLh90fnnrOyf4MUT\r\n54Op9mMIjRcb6QgRB0j+yMxN3Hn4xHnMBYBFxi0OxiFIIPda19UPrNT08N+Y\r\n6fH7B5607E8Mw7LZQUgxQh+IcmYoHMjEHAsDJa0SRr2ShT/L56inPaEzFRjh\r\n8nC8s1CCasR3qLdfBii6QSsRVVnLgvcWzs2/uSsLkAP7sSLzwg0m56IkB5Zf\r\nIxBcOiZMd+5KGFgesTRdxE6VZm+Rw2e4A9nDZfmKBcrxdOUt/mHao62RVVFy\r\neOzu5VFlsyz+MlM1gyMU3DaeJl+Vwl5Fqhs=\r\n=NzXG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-use-size": "0.1.2-rc.5", "@radix-ui/react-direction": "0.1.0-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-collection": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-previous": "0.1.2-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15e9044e5fdf24cbab88ff3fb289180d082e17b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-k/YjBsMDu9WAgaeCDOQ5udfDrJ8NmexKbygP53WIxvf4J0z7+7+9V6A9mn2Hv4OLOK8YMKmjXaT6ZALOPyGHlg==", "signatures": [{"sig": "MEYCIQDy0F1b29vHHr7Z4/0Hrdi0fXXAChpsjMTD0L/YiL3V/wIhAN6RSX5+FImI30IFjiO7iog7AytSVAOzh1MSZN/irNOH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapg5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzHQ//ZM5pXcnwZMFB8Oh9aAzygPfOMeCdQ5ttRPG6j0Kjd8KuUyhG\r\n4rD/wuUnEbBAZMhHLi/nUl8o1gCwwoxBvCyO1h86/BfM9NeNXJ+KX1LHRcdo\r\nl9a7SjTfp7ppECqU2Y/pSwZJYLW7VmogS/9IOtflNbDnaiXNbZfxAeV/wYAm\r\nw7l7t9SsjLbc3n4aWatm0PBnWCCUEUtQCqhfDRYhYMheoqhS+DlMg0/U2s1E\r\nGqgdfmugowxvZWNLrfB6tYCR/7q0aJCyExURfVM/ooimFpL7U2BtyQdZgvaK\r\nLJU7BogHAIUNMJAWC13QVl4vOx+EhtXpbJgytVotpih51U+lLPtQzc5BDprP\r\nJlev4nIexDfQkrhKs54GBnEOliUIdgWbglP1qSFtiV0sF+df5pwkefhJZv57\r\nM+o5/JYHrSjtYB7JjQ957gKHX1ySHlEkebJknGFhC+NuEhB6S7hIQ+b97c3a\r\nt3/Y+0lovZNdngce1djdWmnNj4Rk/PuQVRykrzH8BkWm8O1if8u1Y9kPds4Y\r\net9g+PNnH90v9sTXKimv2Y5pisj45+ye2z3EsbP21gKk+o+yslnXMb+fItte\r\nqEVwFhYUvrJNpEojUC+ZiW/mapR19TNZs87tam95Hyh+IsYyHczoWM3ZznW2\r\n2Y1o8OGoWboLEED8on66trDEu9m9oG3wm6A=\r\n=g9Vn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-use-size": "0.1.2-rc.6", "@radix-ui/react-direction": "0.1.0-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-collection": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-previous": "0.1.2-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c62784ed06bb5bf61f5b563d7597a980a7331178", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-yFB/ax7HvMgR+lNqdqhVgJcxIC7qU6cC81O2N++/3lWYoHWALap3LWWLkvisObm9hLa7mVU37oOx51/Hp6CDTA==", "signatures": [{"sig": "MEUCIQC7KipEHfgm22wB3qejqOPzI8wPPVmRRyZG0a9Ck4FLIgIgbaqGUP4EcMWXeeIuU5F8jNgQG5VFdzO6h2PNsuZ8dnk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164872, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZCRAAnp5dYw+0udXsMDwkpddVvWuvLv+e3SnB0ZHa5lzXEfOhQMI+\r\nDfGY3jxoRws+zyULmJFI+/QC35DRx5i2qlNlSccxNsYl0pr0y9epeoGv/TsB\r\n3DxHM9bXPsZzlO4XHyhPX8UAH8QyKBKAj9brAcPBFi7W3ve+3If/7KjN+qX7\r\nthhSMTgIRPUpnuwVoPyUFZKQEf6SIVBqVgVUGNr12ZgsYoNksAE5vfeOHalR\r\nYnNJGCs8Bq+lAodhAnpheGJeU06UaF89nV0Gi8zPEWSNCOEYUq92V2nOgAUp\r\nU/8nswjJYxWsUq7nE6L40ZCAPySnzSBnZmDXkcl/khvPGAtA6YOBcBB406Ze\r\nD1svmNqCH7WX7R+rie8rLLj+O9+HhWq5K14z6h3IadevkJ5g/Nr2aThasPgD\r\nu4dWcehZXtRyrGrESW1Zs4YqE4L75Pc3cvH+guYm7muPwZttsi2BLMUr/pcd\r\n4oQaSqOpOz1V8B2CI5fFA8Lae3cZeS+LhVBv4aJHSNfUNWM/T6zXeNfMqeG+\r\ne9dDW64lZuh5PwRLjEtBZnxwVh5l/Ay1JmYLLmufVBgpK92nLdz9SAEvNJnc\r\n9yen9xzM5paKf0y25r2fLh34efdMcfDDolWZcxAfvbI0miNNQi+XVbfJyaLp\r\nE3Bc0ryUo1klzF12/9pWQxv7ZtzfzSnO1TY=\r\n=M5d7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-use-size": "0.1.2-rc.7", "@radix-ui/react-direction": "0.1.0-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-collection": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-previous": "0.1.2-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa8664a9383810d4518cf26f0d59e3fcccf7b7fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-TSZCKXpG1ZqLn9+mcxdhzuaxlE1eB0Ci1ZHHk8QCQnuNelWR5zIH7XsgFAAjj77VarFr3T6VvOw5323xEYfLXg==", "signatures": [{"sig": "MEQCIEeKQa20FD0WVr4agMz9go1dzy4u9aLdpodpVHUhF4AFAiB/DyrAY61ByVQLXZ88JduusI1TjCeNUIRTUBDmt1K8/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJbQ/6A1otaRvjrLOvnlQPBeuTtBJnZpfhGqrO80n07orjelJYhdR9\r\nbJbYUAD+Ffre0zr4kQaMpFmT5G+OCFxffkTZmLvWNJovSwyuTl+SXwXqzUHm\r\ngpSp1pz8sJAgMtG2B9v1SSS+cKIrBxi9IRqKnYEgEUrM2N6/RpauS3iKWWdx\r\nibSE3+xPepWjT20jr+TY9bFPxT5F5/L+eHLJJDbZ/KVQNIveNUsh9umEfTp8\r\n0mMzlrPjvQJUPxMmqr5dlut3MIjlD2N2kMYdsp6k7qi40ftoGnm1gv8eopFf\r\nD9FL6j218gfAa4QZArCHDR3reRM9FP0IP+P/4eFSSZGkXkFFlKOGhsCEwRYd\r\nGpJYUm3CJdXwftlzSQfAmL/2eEu1fE49bo59iWktEgCYFLRvk55zs+FMbxni\r\nT2NHr6z6lolNhdmP01SxJLoGfBcX7NBxIPfj5beEK5nqKNFvr3QWV21t/lfb\r\nvoAs3+dT/sHNzUwB0NCm/pv6kvoh0P89s711D/nhww90Or9yK9OXBFDab1Is\r\n+pzX3vI/s9rox9O/rQDrCTsUP2KWaQP/LHIhnKFwN+/Oql0vwroB0dBsDGQ3\r\nS4gKivqnJZPCmAB+9Lmx6aQ7ITdlSCXYhFgL/+rSpVGIF5NKIhyS+N3gHfDs\r\nl08uJmOM7jKhv5Ftj/3bDI/Bun9P5e6Tm1o=\r\n=NGsR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-use-size": "0.1.2-rc.8", "@radix-ui/react-direction": "0.1.0-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-collection": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-previous": "0.1.2-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d1ffe9eac39531668eef365a634434c8bc8279ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-Wl2JesE/EBNi+7VY/U9nDxkzPwahM2mKRlHRnXi6/8+1m/Z9r9y9z+MlqVN+QPT07MZjI+iJnQUeNSiRwMi6YA==", "signatures": [{"sig": "MEUCIQCFpjpxrKQaabFExffd2U/bDZ1c/C7tl1ZjuYVk+nJeGwIge8qmu0U0R/SSAsEJZw/PNz7nsHTPHCgqNepSj7znP3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKMg//enc+ULKo3r8gvrp6yxrcUe5bsoab/2uqQ77ZumYr7Pz3ZmK3\r\nZXRGL+QiH/uK3UoEvevBEVs1fSdDGvVxWC7rLs28taYdKSBmQQAFfx2qaFky\r\n8v/w1P0T+YqnSznt9G3PfSJBCV8rZM92Ouk9w09Bk16nj1HEZdpfHNpsnBvM\r\nkqRW0dvlVWac1ynRlCCq2VUJ1ABe7aNJ7ZY77/ubJSKEs8DkxVazFSl4fwYY\r\nxUAg44JbHih+BWnjApqCS3gbfUfarDDGfyzWr8T/tZQTiMuPqNoMMokHzeYn\r\n9cm9AO2JoGq6ysZykVA5N8yDI+74H0Caf4NucmVPiU7nqvy7e/GKVMMOI84C\r\n2aMAl8DAVvriloaWuXPWplUVI1u3KcY1vURBxBdfDXidI255SBUcWpwH1KQK\r\nDh3Txt46UVSAIQVfanEb83quJ3oJatnKo4RC9xcnc31Y2Dprt/3CbaGd/iR5\r\n6uf9jV1O9CmzZrsQBgO7QSn1wch2kL3tvPYsirhAD9PPBZyIjHR4X48CKyiZ\r\nt80Etua27G1klRes9HFfCi7my/bgmPfEPvN9EFAq5uemkl6Hd0pp+KBbYFMt\r\nSepvEOpTsE8FBPa4iX6HtzKh5gU7Umo2U6DIVf7GkaTxpaWf3iiZtAxeKC1g\r\nQf/w+CyUAI10/lXf7kvJpg2qCAnEPYVM23s=\r\n=vX40\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-use-size": "0.1.2-rc.9", "@radix-ui/react-direction": "0.1.0-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-collection": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-previous": "0.1.2-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a7ab448daae2c91d5b98235f4bac9d228533489a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-VEuEYFhVv3d2BRzUCZFet/yfVtg7qWJ1bLjnKNeT37+zAcoA72Gz9ECtgpm1dBxExCNZSVM9HFOMPbgeZNDqaQ==", "signatures": [{"sig": "MEYCIQCpNcZ/hB8NZJPC7YIo0QcrwPqRadRkBLyGN9MCmNp+AgIhAKaHSn8Fc702w3XCPuVjtVbPNPWNmSj2che3dZHPmlxv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNURAAlA0vjzGrZ3SoQvsggSaXy+Q11NCL7Lx74tQrxnpRLTT8oAgJ\r\nM4u3C3Qlsi+WvQp0ZXwQvODk7oTaB6nyjL81Bn+QBEAruUXIFgV7b6F7/+NU\r\nN42CC4POkWv7FXzmTOgYWGSMxnB8OilAekGkV/tmC0z2FB7l48A+KWqmKhq/\r\nYROvtQ8aC/4yCGEWew5iqCX9jpuRNBe/ocSMAIRVw7PcRNk2pE6RbF7ydbTW\r\nuDTBlT5tl6OkRd1kEJCu1ZfRTy9V+h58wRDPIv/TrFF6xoW6K323hG8bQ4Uq\r\n+uUK3ohTMzy46ZUpx/Veg+pt5HApy9URov6In7QTqWbfY9TvWa4oLz0pse0I\r\nfzOTVa2oblpiW+6Wk5JjmZJwNaq8VKJNz1I6okL/fHVd7gFlMu1IfBGmVfsW\r\nL4y2bGERNg3wH2SS9j5F31JydlN2Gy6ZlwBlCsLOZPj5ABuZAlRWAUk00ORW\r\n3H8GHM3oxN8S0JSQtcY5X8QEpi+/pijQR/VvI1Ahx9yMd2dj0lYv34PiOspv\r\nLHokApD0ca54HycFEp1Nx4aGKmvoOt2pDtR3xGF2mZ/ko6H6Q5LHnvcx7Bng\r\ngvW9wpNVWeu86/+fygvAnkKli7uoXwEWjpPBZ/sXgsdpT+LMnDz7SiIEG992\r\njza6Wkfj/2M0kqBlrVf7Sco78oGZsLWnIjw=\r\n=4G4S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-use-size": "0.1.2-rc.10", "@radix-ui/react-direction": "0.1.0-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-collection": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-previous": "0.1.2-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "323f6acadce6b1a0fe84f689e80f02241a57fba8", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-B4L05+3CsTz7+LUPe0MnVlTAa2wHXZk7Umghu5Y+Og0hJvxKpGVCMtlWGL2crbwiE3dguxB7lpI+yMerJchuCQ==", "signatures": [{"sig": "MEUCIB3OOgJ3L54p8PX1l2W0qjzc6wrtKp6N8GFtcfZHILYvAiEA0I9s1Phfh1wzwvt8LaBjDO6WmHbMJbLEUr/43fMGXAI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrj5A//U+XWwbJLzhEnlY086VMBkr5rk68HUqtIxMSBdPCXpil7IO+z\r\nkzVpyiqgD0TuOshBFcaiQwPqwnf3KYdMssHmzmlGW1lGiw1C4unljB/GGJ3u\r\nzvfQVYMQxAeanN1F5GVFabI5Lshy9NR2QQN4FfAsHvQt0JTpSknLB21PnDEI\r\nEy3zACr5WPdgJ6AmVs8WvRiYc8JogJXEqjOcsJ2fojdZwQBGme6OJtIhTRg+\r\n+5VrWyrb1pvWKo5AfCVQKJoeNeVKpoEJJkdKzfQdSPAW7LEFQMmbypWwygaU\r\nQsG3HppG8TEEgQY6e63tQ15osZUc7U8hI9TPbi2NZvUC6v+1IgYR4PxvmlRt\r\nDu9a4dDdGV9RNbmOc8SgmpruP9QBeqBnhLzH+yM4YvzZVwk1RIitLyJwilaS\r\nWzLgYk1qLUGIDv4GOxnv1PMn5T6uJ7dOgTnHZvwc+YYROPfvysXSEsIMpvEF\r\n2wRcRw+bxlecvy/pov/ZKnMZCYl0p54haSByNbMpVYD6dTpcCL6QU5QtBpQD\r\nBL6tdNHwwOuskOsi5P3v+RQtmDvMg7GFMxmpblwbhNPqKwVbZYL03ti3acmK\r\n+jNlHpJESYFsqA9mwTHYGXX9+uvXyUJRke/WgVpTcLsnm2827p6m77bWdr8C\r\n7VmNpPJkakXwRuCi2sQ7QARs7HJh6UjJUTQ=\r\n=kl7C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-use-size": "0.1.2-rc.11", "@radix-ui/react-direction": "0.1.0-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-collection": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-previous": "0.1.2-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "39fb7b4b772e980ac43b740944d6dcc96d1eed35", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-QYdvgoQXbjhBwIYwWN4ol9wmaX6YkOAVtSBDnNbjM/uONBvoGbnCKJjaaP60wTDWchfvb0WzuftZMujgvCWSTQ==", "signatures": [{"sig": "MEUCICe8Sdi1uGui/3uRYd7t7mwuXjhaQ4cGzltDoAQyjnB6AiEAxPjVwOK0QF11+nVOzG6O51lENLRDSh6JL8Rt52YDlmA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSloACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoecg//YUFQ/74CjZ5xmVfW7orlH23wW4BGWyCnk4orZ/zkO6rIGDEn\r\nwn1et/3axau2EXDiUGve/c74IKyqnKYHTm8Tod8rWkFByVuECmv0dguU4W23\r\nD17j2F/xsayGDufj2MCN6Z8To734elqYtu1KTag3hL/jj9gIznUqeVR1GLcX\r\njZRhmD3JAg6F7/2TR9nNr3Qs5luzp22Ieza7Gzds5skUdfXYINJ1nIZX/9mF\r\nW/rCV3FDJpFZnkXF+c1lug0YXV1U1ZUkdRy2dLT4NYkEZKnuKD+Mps5rLyXS\r\nX1eJy6g7U7F9Q+I3NE3OOL+TrVn4llb+2g+DxlxVvlkveUooNIKegCUUsR7V\r\n9jNX3vzsIHyqXBEu+TYeqOhsGdGKZbz5vbEa5VmEhQWCeoCr6tBR5pHwuSpc\r\n935g3ZfkB1Xu6t3y5VNliM/8Syj5UEuTCrMA3/X+7KpSFJsfaCA1SWnl9eIF\r\n88NMuRNm/8eXMaIZb5imoS6YLkrgKyGBoueYbLRAz8eov4lwNyxvph8afa+3\r\ngO0SwUEA0ShOKn7uZQFniTAORl02wpF1we1gNVFBRJPf/dQCzAPbR2nd941f\r\nTXhoixHnLWf6wY5I8Y+UBq9PmUPhJL7/3i+2zn/1CjSyRK3olLThVZ0Jz3yI\r\nQqa2unRGQ1Y0avdHM8l+QaOgLZ9BJbJ+Ktc=\r\n=AKzY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-use-size": "0.1.2-rc.12", "@radix-ui/react-direction": "0.1.0-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-collection": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-previous": "0.1.2-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8ffcbab80481b4a18bd59176220356ff05229380", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-9cHRxf4oH5CAJE6KbNn0VioCUuPs+jpOZuu8dC3Vg7sApHf1HL1UntpKnqacPE4WrFBR960U5Kjgcj3/FSWvLA==", "signatures": [{"sig": "MEYCIQDpy7EVEw5dDojrByOKoYh0jhY5KbEToSGAmbP5ODkAvAIhAL3Z1HquMQRRV2J9dbhxUNWj6R6r2r6hPFRk2EeAjByM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164882, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLdA/9E7yFzGDk5ubpi529btNT+HvnUGAgYcXB/Dy0aCXDJ3tAeI9T\r\nKqYZtRqr5sKzFks9SrNAS8HovALkFAzvIdaEXTFZq6WHg7mjvkoTD6OiLqdl\r\nqdiygHOxLjYIsBmmQQGx8USAaN5CpGEg9HD51x9nATnCeraKA9/f6GGd60yo\r\nL2nScVgBpmbN1xE3nsKKo4g11lOCELPgF9pTUVL+CyTFQv0RjaYZdIT9tqqM\r\nkWHVd/0qFKhkidX43Sy0WVV3PFL9LFbRVETVGdsdWUL+d1Xzd8wJXGLDthrM\r\nC6kh+z29LJVgifml6HwqBMI/Xu2a0Mx0ijf4SA8AWrdqRDCplBDTq/q/wa0z\r\nPUVGM4JVp1n80jp3lMYwNyZLNUbAcp4kJO5Z6M+av7yN9nih1hMBrA5LyEBW\r\nM8W2CyqSzj0b9vmCeS2WoCuw7fB1AVps+IVnwF+pCkS5ab0E6BYSA2bk0/CZ\r\nnvzUwJUFEkHFp90OqWB9GlYh5QRpaJPCmBNXj6q7KvMp4qgmTVBiMGp4WLCg\r\nnG1yJISFvtToaNW2dYT52zbjH80fYmAXkKrmTtP/ICJQJvaKL/maiUFzv2fX\r\n7UrAtzT8E4qqEi6VBJuQ6r1DtRcgNBDlBSrMFneTSbBn1X2u2B3ePZhD9gT/\r\nRumcwAl1BHWcjQESpxf3SUaMewskQDJiEYs=\r\n=mJf9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-use-size": "0.1.2-rc.13", "@radix-ui/react-direction": "0.1.0-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-collection": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-previous": "0.1.2-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e2a7820294c3ff2e040a5c4b38fd0af6be787ed6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-yHlJ7TZ+SRLZYsym3KkTnJaJoePTddX4urirRy6PJBZXZwZEivzgZDBXKPKyUtgfWQZN9H7WAgjxJuwSQax9fA==", "signatures": [{"sig": "MEUCIQD3VWnLTLPQOfTWy3eWj+gdHNVUomO6+POQNFZ4lPha9AIgNwm+DM1Ikc8BIjgoOHT/rWXFA68V/GcWRGW6zN3un5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplKw//RrEb1omMIYNF6iCfOuBmIc12sLhOoLcqAm4TNkbWHjBYbiDg\r\nYtX9TctJRVmDDUUYe8QgcRVVEIaRf6uKhM9WzrSA1nWkOniGbLI0B+L/xCzP\r\ndGkzVitzscFp20CF8wn1io31w6XMi3fABG9MhQZAziCmxR90eHipedo+I4RE\r\nDe1dp71vIcj0sZdw8FgjvUCk6390XJ/sqlH5JxgP0eYqaX3i9UhzHtFtdFn2\r\nK6sjlO/egiBA01BWU6RCMWX5ZqOqAA8s2gmh2+x3u97eqSoIccSvLmqw4NuE\r\nEtLyECdwuR3d3hQFcwbAS9wOXGMAhXJXgYC4bJ14j/BLHmjNln2/UKdV3hs3\r\nbKpIHyJS6q8TyOEc9lYkr9TVyjsmeb+VeCqK2RWwAnJLXRSGE6+jcCAvcelC\r\nhJc6PlTYOIledFf7kYykBrkWw+nqOQMmahOW/AOLGmVnkSEyjVHiRpe654tO\r\nDlsRtFTfaXCktPoCbiPIUuNtm88V4WOPmcR1A/Jd+WMMNuTRQ+91GOQB5ZJ/\r\n/C9rRSYZzWuk9FGIhh9tGJBVokhQnzDAqTyKfIpmk8sqCXrkvV5nZxeBgrbG\r\nsPbTrVdMNVPUlfDI81dKSxc2L63RHL6DhTqe8dQmQWk3OoA6kdDrTHYg2kfg\r\n9ADbR9OQYNvKHRO4TNypa1PAY81l3gksNj4=\r\n=y+5b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-use-size": "0.1.2-rc.14", "@radix-ui/react-direction": "0.1.0-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-collection": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-previous": "0.1.2-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5b37df1ebad32648581b97af58165ef93da8bf8e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-GC+Ctqhezn8YrXj26ALNiLA3EUggzFyV/lGN62UTwgSejzrPhqqMqaxCrMlMFit+3hiRC8YPdYNcwHcYLw0+Tg==", "signatures": [{"sig": "MEQCICWTY4b58P38bxOTVUxteBV5Ta8csDTTS18isQLcpVX6AiBmh4f/uha7z7CxmpL/94x5SEm8OBTswtEPkac97IOuJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8p8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocAA/+PVb42Zw2sFwzhrRGEFc0YIo+qMSby+2HPUX1rVUza6323glk\r\ntf4PcbbPt/qmWGzd9Tle+W7kr+l4J8QvOoNUhMImg7Hc0EKBLiShGEcbGq84\r\njocNKR6NhB94Ff6fLZB3tRwHO7GmaE2U4KXPW3hsbQCUQAz6KX79dYa/2Q9t\r\nBSwPXbxTW0ecrBDzMlOMrdyZEk3vTXQekX7CzXruZyxLnDV0+4QYdAGcO5db\r\n9+aU42p8ZWvonk9sCQ8ul5zvt6Fn0R5JoRKwITw68AHo+hYp/bCqfzjQ/SF0\r\nR0GiS/YLtOOTFuRYqzna+Y+V0W1Bq66UQIRXXCSRSPzipUQ0/RtzMdbexQB3\r\n2gZ8yC2IpEWUp/er2yVGgJdR2Lu9HaFxkiywwiFshGQ2h4l5WIyZKuZAqUCs\r\nqpoJTlRuM3ycSg2q3Tlu7FK3US74+CpZc9y9JMfdTOqiv0XUaR2pnu+ZCJwm\r\nAVtg3zfaMFjfrgbQ4iBEMxvy/U4bN1dIy4fkmfWR79TynVmJJI3ylZEy9Pon\r\njBeczOYHTB7AZ+ssw45G8Dxrtz7O2KcBKlXsAz92LCmweGin2qjAvNJUHKES\r\n1wrVN8Q23bH7Lvh3J2aSq1TArR5lDJZ9adsTjDtEUTxV6DeLKtCx2xEwj7g8\r\nZt/O7hC4/El3OZ/mTBtdAHNjMw5CNLy6XZY=\r\n=wgY5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-use-size": "0.1.2-rc.15", "@radix-ui/react-direction": "0.1.0-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-collection": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-previous": "0.1.2-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8547c1e65d19b09799cdfb3c4b10acece3bd60f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-+V85q4rhWRZkgcr1aFXcUHAdYOv/ADXS3h7UGZSBI1gYJIhVGuVWj8uvdti+NvbxuGOFQ8Tprkc1eFQTD2GxCg==", "signatures": [{"sig": "MEYCIQDUxE9kHTsxxUmTTfH2tUpE/y1sreNRC2m9nsEstNOVRwIhAIKmp/9h2wThV7jKRZoT83j85m1g1yRzY72f7na0fsIY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPTQ//cJyjwteY1EKJGpij1+eBpJV6mdEYonUJ45ipFW3jp8RyT0mJ\r\nCehe4i3Y1gVNgieUT0d53WNWg7DUFXMtA7P8GwfTfsSTeEX/lGKgTPiba4A4\r\nj3SaHg9KjvXw9pSqkd/NnP+HBKbl0qfBRstfVuJCQ/fKwJtij9GjBXKxXSZk\r\nebxq+P3uQoLHMwikNF7lJJGPtkR7KLKsIjhie0Bf54ZuLvhK5FObDDI95SHW\r\nx21Z6vcoXCy4tG0dkKoxm+1x0bf4Jz9/AT22hIs/gbBgV/cMe71LgD0smtIP\r\nyEqQQSJXpez8EPDHL7sc/b+e6/d1Rxm9BwHtcu1Yp2IUNobr8IbH6VjDchbm\r\nN+u9j/7+7elHSMejgmoRAwznNOkl8Ynd0TKFl3YNW7V903a984p8ssd9g54n\r\nNOazhKqWtMOhdWIKF4aO2LeF1v3ZstWJPPvFe0FPPXXnOL+IWRP6DxyKpMPK\r\nzPzRIRV4Fj+OKWazYBqAAfECuWgUBtWAJGus98NQJVEciuwSLNT+NYwLCC3V\r\n0RehZVGTWsIVsNVdfs7OzqEpk7KFxVuh6M9nLKRQpFWnKrwIYwG3DwCOcG5C\r\nIAJbCVf8NCwCQ+1AU/AQweV7QOYiYGuhbrdWFwaGAAaVHoTO91oIQExj6pxK\r\njPwQalLFn78Qcw0ibDJ1Ym+ZVb9RLF0m71k=\r\n=+6TU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-use-size": "0.1.2-rc.16", "@radix-ui/react-direction": "0.1.0-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-collection": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-previous": "0.1.2-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9969a9500123cf06294f37f6f8577882ff47ff52", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-CGfGVJBKl/xJuU3JmEJNM+GpfR+BUZxRPf9iE+GexJwFYaiv4312T3oIIWakaxOA6xQoEPMmkgSXtYrPQT28jA==", "signatures": [{"sig": "MEQCIFurMstZqYuzLPsyFttwBF0rEKv6/31KXNFKmXn0ZfFcAiA7GXSOz5RerSaCnX/qyLLyl+f99GUHFI420srfwst+3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgdg/6AlAMflyMxO3eTqwwBbA7oj+2HkzQQtsld6k38rLb57l+zMua\r\nlOMDFuPs9ouxOVWV8PwgaTMM5P2j2M1Tt7BPcfEPcBcQQIXIG2W33d+uJrCY\r\npEDAaQk7J8UCicW2a0LcObXjL4+rB9DDUMxd1Ufywfjb9mLC75CWmHBO9gwY\r\nlZKs/Q32xVZDW2An3nAt87+UMQ/XVJFBt5a8/qplSFB3vFm5R65HiRA5/TMK\r\n4V5wSwXz5xfv+jSJCI+14w5qZ52O85x9PLAnlM2pz4fByD3GYza/Z6TzAJ4T\r\nwCUL5678Evi24SoncaGeTF93OVoRE5UH/SgQzSzJDT4Qh1wHe4Bg1CUHlGIg\r\nqS1SrfnQKPyc7fPiwsNjM4SdLINLhkbghcg4FBDGnYj8jp5Wsv76joavG06E\r\ncwicZ+hwL8kTHss0LefFzAlIFXMHjXSwskAlMTlgTNJ47TJf1S9bZ6zfIR4A\r\n25ZWr1QE7WZwkA14ekwlpK1ltOBK5vn/l/7sstrgYZ8AMClMgZ9vv9GkrFfC\r\nrIMdf1VRPLf2tSLnYa9/fMM08ya3s0tgZEmOKg4GqNmXL4ed8Z9dVa+nwzA/\r\nriuvAbMdGro+GgI56ZZ+pn5EZcBWUBDrMhGmfNLEBYTiaSBHOMsM45O5Fwxf\r\nphpG1LSvEscrrB/A1aijf2YUhnpPO5vrGl4=\r\n=nvqb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-use-size": "0.1.2-rc.17", "@radix-ui/react-direction": "0.1.0-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-collection": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-previous": "0.1.2-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "62e3374fb0fb8bb4822177b685ca3013ff549ecd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-bHG/puem8+M03cmpqAEF1IhqHnB1TpyfXvAOScwyNVpWxrEp6olRqu/JDwqnNBMjTOX1c4OSqZ1I0ktXvhhc2w==", "signatures": [{"sig": "MEYCIQDei00e0uDVvuZjBD04T/0Cu/OcaNI7d66AQzy1aPmDYAIhAMOsn+q813Xg+Fzqfe7t0n0r9sW0kQpr2GO8IAi2jV6Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh05ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+vg//YElVRFTnmr9QazbBCLbBriKr0YwulAuAVGGuXYZYrkRPd9Yf\r\nHw5LkfdqrkQlvWup0KLC6ShhvLL0E/ep364/72VRWIQ4IckvrXN8bUvYQUD2\r\nEZjIO4wJKGco48nPFHNDAwW6AqsYJSICKdWe6GUuCtmAAgnAegUI8MIYgcYo\r\niqNXJb8CG7t7Bphm52LSocB6NJ8I8H5BObRFklDwUgfmyqH0eRSuosLZh032\r\nd3fAAybYeoy/W4aXyk2mrdSH7dbaFPfcA9JoBx+kWyG55cO0PEF0n0Y7RWDl\r\n1l5Ssc278FpG+xCYjqLgB3XQRXyAMX562icBLiJMWRiCE1gu0ZpRoz7bHvHl\r\n78ZD+OGZ1Ht18+DgsMoGmB4rXQFn7oVIrUeI7E7deQ0T1NOUxttJmycSEmH1\r\n6qU0op1oTLsGogPBN5pXTh5m3GvwlyTo+QtOgyJazhfcGndluFl+hAFEqX4B\r\nm/9dD3mNKOva1nCYZ8rsOkkVWaHsB2y/qjO3H4ICWoSPki7/LtT83OEPe0iI\r\nzMjYb6a3HUEGaqEfmFlH0Fz2iGNKAZTHvFeXLkPhmbQ76+4sIXDUfu99Rhbh\r\na8FhXs1JjY7ofwsAYdusFUsjKqgwPED6IBkc7m3VRaw4wmtU6A4CHecX2ONU\r\nd4v5f+ffVyz1RSKu/DETfgAqumQriEO4m3w=\r\n=5shV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-use-size": "0.1.2-rc.18", "@radix-ui/react-direction": "0.1.0-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-collection": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-previous": "0.1.2-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "67d36ff6a8733a072f277259bfc88a52f7b0ddf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-0tE9xJuGAbwlkiwH5od/Ofhbtway9YaqY+26qcFg2ImvhLvvrq1AKHN3+OlA29QljKqOVpN4F0k6GXWri3tdJw==", "signatures": [{"sig": "MEUCIQCDOEwFTIHclkgzPg3Hx3HTtH29fz46WeT1MiAPle5UvQIgRrV6aHyxvu9mjgPFY3v57FJUyQTRG56tjjTY16Roprw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpo5xAAj+nYSJDDYcBWKTmZWwptOsGqZ3uSSLdcaQCHBRBJqLlL0RfM\r\n1tuB1KK4eFSFyBHIAwcuRvWrS+vTzpTKBZdvFdTedGk0dbsQW7uNuBS2H/zC\r\n1PvEaLrtVovE1ee9lUiHoTwkG6f8hH2Nu4futoXxOzj9LqBpkr0Tw19e5YO8\r\nXsE8AVevxmpxmtP0nkui/UKIQCq5gPzx78PwceoUFs3uXZfIiGIJt21tVotJ\r\nAPTesGGxYnB3NhMANaen5sDyAtCPXOkUG8uvMLfmoZ5jZQdLQ0Sr+5SYqznq\r\nCVGPIkwmkllkOl3qliWdNA7tQxuIdJc/aczG1s983SeQAlph2rwkF9sc5YDA\r\n5Dd0jLIrE9QRJuBcbWw7kz07YWgXhGB8o67ooNJtkEL7Rej6UaNEhGyB5AD3\r\nrWJE/PTgJL6CCR9LTUqHfnds+6aqNI1wmfKa3jLucyqKn+breO1B6FBHP3JP\r\nwfg8xYh0B32u9VKcR5i2/VY0GOQ7Gn22aqT/y2PWnz1JGDYniikKhQHdEXlc\r\n/qQBJYlLpaVGxlcoxsiUf708vb+ebbXzRQPEO5YAmj0UR9da0lL/1ZypxWnr\r\nSGeIN0yV9RuxYati1BjxQ9NANj24FxW8kBEvZ+MkIBUuFRTZ9nDasoCB24zG\r\nX3xcaCIJbm4Uf+RHCx6693NByYUV6jxnoW0=\r\n=4RDU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-use-size": "0.1.2-rc.19", "@radix-ui/react-direction": "0.1.0-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-collection": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-previous": "0.1.2-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b7302a4a5d0eb7ac6fc4751211e71e70303aa9ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-LX1n1KRtjECzg92J94TGzOJdTK1NcYf/dziuvOtHM8AC5UDAte1WTZZVNX7IuSAp3Ju7RIYntj3iVzCMQq6vtg==", "signatures": [{"sig": "MEUCIDs2jukcDdNc8Aay6fWuvfOpg5szSsFIPnYRhMa0VqhqAiEAymeYhizhHaqyhMLoN9rcfd5JvOKQGzztEQRfNmNpYKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2W9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqdA/+IRjxdaG5hQLnZ3RkG63E/gf/hHJy90DkrVW5E36f+MJo/VeM\r\nEAfVpd+FJgL+TsuNsKInH7yMA0JY2xO5S/dkpxjaeueRFZ4p88xnzvmmFD3+\r\n0gmCG2nMMqORw37EsVTmOPgziPSQlwnSXGA6me0oDcVjGA66eg2kD7RmQeLG\r\nTc/pgvok3mmgMh6X5pw24jR5oKrODDlLP+v4geAfSB49oaPl9pVnPm5Ag5kt\r\n3kUgVTYaGyC5kBhY6PYPodi2X4ipaQMBMMu7tbJW8mZ19QXth3Z0+FJEQ1hN\r\nFoC+b9W93Ur1BtLkY5zwPM5Z/aQjMMFD4VITAosHqFQ4mEjglbrS/zanPI3u\r\nR6g6+gkwSXOnuy8tGdIUvfRNbQ3jSxKh1/G6puIfd6mDyfRCDlwmejSoWDC7\r\nkQkXENjpCS8NTzXz9ZF8psrFHJZZfHMXljHQg9941s0IuAvxN8sgjGDxH2wi\r\n/N6rN4q0k/UCgK8/1E+SAFM1XfS86F2o/HHsq3EmE3XnlSZo1qoO0mOCW33G\r\nmXELjR7riou3kbPNNFEjIkkiWf4xXWjibBCFVGiUrPfNi4JlSXPr/E1ToeIm\r\nUzvF6AbJ0DD8law3XGONszoN4ZX91w/y5fVNaehwwgctXakXnwjNWr6vfVh4\r\n53jp2tUJT2AGalVDLNhXU4AvT9uMRKMr218=\r\n=HvW1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-use-size": "0.1.2-rc.20", "@radix-ui/react-direction": "0.1.0-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-collection": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-previous": "0.1.2-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eae253014a7ff64d554e298f28296de641b301d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-8e2S8zzrniVSnhefFW4IEeK3wEOjzv44gBonVVk5WDoROeKF0iRE+XzxYVvuHmhFeU9v1Aoz7YGELw2lRkIuqA==", "signatures": [{"sig": "MEUCIQDUDoVwneMaya3LGnNEDLFDhLgA8/BbICber+2U72lfugIgdPVyS0WExg9lp0Ju9/k8J7Dhxf2CY+BHHY8UwdVMngU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuVA//QTLMDz9AVqpoCYBOK1UGc+4pSIDiPX+goKF0B5nQ14EU5zE6\r\nU0T2KFKzqCE7WjZSssSVllms9b5sZL5zU08eTsKgIQ6IsVDI1ZYeHrqkIY12\r\n0s5icKotwtCgzx1aLq0dobcWnvH4Lr9a+Gq3/iK361EXiHsbC2EliStOir7C\r\nvKLAWYLX7lw9uG1VqEJgFwP3Az6RdgIKDAYfHgwR/r+EY2CpZYz8RPmB5ho8\r\n8SRuPlagQoGF7kG5tLtKo67qeJ0mxi6bduLnrKJ71p6+XhWgh3MM1+o5hoQ/\r\ncWRGt3dvegkHYofvHG7c6BjaUtgFOahh2k7afm2Ry1/NrXqJuVZX5r5kwnG5\r\nYqC4tXu9uAeZH2m6bj4nSSyDqfFHxwun8r8XqXDZbpZvFV0ZShEJdip10jNx\r\nufZ8/telzIVP7b8Z3BLD2dR5g0Ka3ID4Z62CKL9RTuEhz0OFqxIdW6gkzVcc\r\nYpupgbdCVLSnfHwd/0a85mg8n4nAhiDF3B/g7SYcKJLi8601/W5hxrhtqE+l\r\n37WzSJBt9MKlpZahVWxnncltmj7e82w7o/a+EpV/WjMYXCu9iMYzX//xqUcx\r\n1G84AWq908u+hb2G8xEzeV4nCkng5IlMtxqqp2b00HRLCTSdfi2cQ/oZPe10\r\n+5NlFpR/k+QanBM9M0TM2r4XL4QBJi1r720=\r\n=D3w4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-use-size": "0.1.2-rc.21", "@radix-ui/react-direction": "0.1.0-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-collection": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-previous": "0.1.2-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "319547db815284d4b1bc8bcfa91183e5ff9aaa8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-d5LIgjjy5JgSTtZT1oUXhS9LlnRiEvaL0Rl8Y/XN0t6JB9aL8eAtyqmEAyC09nCTFRvAom8fLeGotZQcREgH4Q==", "signatures": [{"sig": "MEUCIQDf4/BtNMZWOaK8yUqF/mkpWYAvjP+CtHf1uWq0aLSMjAIgLGF/1h23huUq1feV42Qyyzwu7u/CndlxR4apqmgqBCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVpg//UfFarYcA/3P6E4/hmBBIOS7Khl8XU3Sonbs/DLO8h1r1uijl\r\nAZjW+BcYSTSwbF4qVpvd99G5ul8DtHS2OqXSz2Y/8mlXYJEupacAJTx6aHzn\r\nvCcaAMIIOyahERXMZGz6kJ4q4G9V1KXfQatOfuYIOGQ7AyDXLy3WzDr67GqT\r\nPCcg+aN7gh25t+Sir7QpsMIy8/mwWHObLxrIJkL7rGp2B2MgaWXoEaOzguC7\r\nQiTgkPICRgvNvxpzEYSEW6lLSO/rGNkNz7+/qkrwkFLv0jHEBeYnEeo+0uSY\r\nNclkXqeHI7kAnR+B1+2HcsZo3JXZiZ+6j1hTn8P1CMG5hkWelJAkVRgFRnbl\r\nueXoTx4HPsV1a5YyliWxaCnaAnf8QOJAk1KNPdN/2jM6iIEjEYRAWelWMPLg\r\nDXtN4JJGNxDoxNuWvxPjs6nDct1bPYe72xjnslF/ZoDE7C2hvo9sEOF7wr02\r\n/e7QKP3MEh46e+u9ac1JtcKvJGPxj7JMXwclANSVmpoTfSSPUgwWH4Ac9C8B\r\nys3+SH6zgGVo7woB/V2bggPUn9GrHOTBcZOJr9+cmLBVIOyysrZeERKD3rkL\r\nsihwPoH5ok8kNYfzf8pbWNltVXIK57CJiIW7DIX7MNFku+EDAEa9BhZ7r2lS\r\ndy657Wx0UOZgQwPdd9CuuuvhZTATDh8rL8k=\r\n=Q8hD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-use-size": "0.1.2-rc.22", "@radix-ui/react-direction": "0.1.0-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-collection": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-previous": "0.1.2-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a55994755c81b84a614215e0b43f39841fb0985", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-nw56DaB3RPm5y1Voph+2Mx6JQ2RTFpwQ+0OKK/WGpD5m/d/O3MjGFEeHrKG7UHhYWINRcj9fomVxDqVQBWJFeg==", "signatures": [{"sig": "MEYCIQCtS8w6rir+Jcx8xN5PEusTsVOM5P9BsCGDT+n+JLN8ngIhAMwNf7r+/6bO0aYBkhC0i95zJHbgc/3Ae/Ai6OiCeBpT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0hQ/+OYCcVdOigpSGpMadIhSmatvtWPv17RE/zKLQ3P2E3nUH8994\r\ncSwMryhaB4Xbg7w//TzJnB3uNvniykeWwrsOfdYKMu0XVbdKhEbxChx88Tvb\r\nWuO4nMD2Hs5rdO18qcgLuH+BPAQtQ4nqbAffyFl8EutRT8WEH4xfZqAg/nch\r\n990yvfhe6Ocz2tV4iB2KtQj0Rs4O0mckHm+kHtpbnLhanSMHrUrElZscDCk3\r\nAmf1phQookpYwBL5WgqgnjyAEWPEbsMDpsjxYQQZnCQvO4AecpKO0f119JBt\r\nr6GtOmZeHYkwLfKKRJuoUpaKgDl0ftUBRAjOZ/8f66hsfTFON4+4zXCW+gO6\r\nU7iK0hhj1T8Moi6DkB6KD7S2AxT7UfTLYwHplbpxg8h1djlkDaYlAteIr0Ek\r\n99dU0q485rrKUR2YRWZ15LyIPO3+iUJxgfNj1Teool+u8+61lzx0i9gjbeLj\r\n4xjUinhslUQh3pOETV/o7/1Q1gR4i20vm95vdZivImPkWcNLjuBpy5JwRQjo\r\nC1Ra0U2xf8F31HlhYPW7WmWtr94xqmxLzQJ9tAdqvg7ZlN4h1ZQ+8luA4gDs\r\nuwsc/qcCcG4fafWJJBA7GAjXJiKBFMbvuwz6c+bUqBCq7O5GHh67oWZCjkEB\r\n8JTGnFA/l4dmcESYVc+zK8yUQfYjgbUlw5M=\r\n=tIij\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-use-size": "0.1.2-rc.23", "@radix-ui/react-direction": "0.1.0-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-collection": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-previous": "0.1.2-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "89cfdd739f43b3e83715e020029c43e8365d6c6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-ZCiWn1opiZDjAg4BtYa5n9XVhDQG0Cut6Eet2jpaGE2zFINBrTn4HUIfWWDplgMR8dVruQ/TeFABDHxGmxtWXQ==", "signatures": [{"sig": "MEQCICSTPHXfhlWKJA+lXB5mb0T54GLS3K0F7G5GmxuW8JgHAiAQtAdZFkp+ZrfqPaQQhoOOWWpSiNI4hLpBZ8EaDqIuyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlgA/8C4h6jub/5GkcMi2oSzTCLGnvxxZHbUAvGwjoA1I+cTglr9XP\r\nOnkp1c9x10skEDSJn9fvUGxjufeZ9ubDHrTgWXFHhPuygN2AMqpjx8JGpRoP\r\n1Lo1gNE9mGQP4g/WuEneInweuuI9vJk+X1rDdv7f7V1/J2yoJV5rtbYWEBaA\r\nTdMcoAQZkM8rbQRbkqpgANX8Jwyaqbe7eQ34T+pZ211s/ZlorebKD4vUNh1G\r\n+vyEwY6162PzFhXEP17Dry2GdbwVymT07eccKd/iBkWzhI0KS2XLWldVJkGa\r\nkWBjDPRuByC8ypZGxphCHs24nX3e/guKkSnhKFzU+80l82v4ySe5Q2K2vxoW\r\nqcxA1ZONa+UhsBu0DJoInpx+McPvSTxVBW25/NRPzrJbd92EVo5LoLioGNMK\r\nep9dn3aGmkbZVVV+GvgqwIc2/lN8ra+II3juDxpcW69zqKHin0azG2wcmQx1\r\nU2pajxwxWA2luOEqNvQZiN2alxqxawt/yOaFL49CTSCVGO7OgVOY1beG9SIu\r\nEglgnf1ad0ZZRKAGUuMBLSYeXWU4x0Xecl5xVZ24qsZEDw/6Dv24vWI3J7ap\r\njotexLVX8NEk9T6r68MuNS9v8B9Ya++wbJPSK4mKvGQ4HNkdJdTszMhjHP28\r\n2zqKeV92XUf4TIFXP6SE2LW1Fo1bwlVfNaQ=\r\n=/n+F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-use-size": "0.1.2-rc.24", "@radix-ui/react-direction": "0.1.0-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-collection": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-previous": "0.1.2-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ec58fe5facfc32a18b5fe1828e5afd102662802", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-iDJfwndpfir4jH2o+G9/4uClnN9gIhI1EI3GNWQXyHJyQnWbnvXcuSviubxdHkid+YakkndouIufK0yYgrc9pQ==", "signatures": [{"sig": "MEUCIHhRu8YW3z6G+pNOhUGvO04fJpJ+60ZnHo/8EV9nUl/lAiEAoYJZVUh0zQoGTjImm8g+aFqeQwgRz0sRY2/eFjFksX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLh1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMRg/+KCLRyYmlJ0aO83i59hzKfTPriYh5AveY5Nh4T2TqQ7LU0oXa\r\n7qTcCE1gFKOSwd89zqIsmPGgRtxfJh0ZW7Rkc4cp4shRcLKuWz0rlWA2mSu6\r\nRkDxg1SFCMu6EhMAn3hvoA/xkguySslVBKkXQhVcCbG30cLYZEQL1ur7h0OW\r\nq0tJZkMl8Oc1oCd3dFPZxfkzknjzFfvjfcjhTcNhNjqITBzNwf6InHUTjTg/\r\nQGlsZgJLb5sVjEDKM/v6xQ5GIk1bvT/kLbSCNdYxHrQvSdem8WdHIKquHqLO\r\nuGF/15I7+InoBB2rra+SOK1JkpXY29Sq+SyT9Yx+/Tf2qV+f9QeXk1oM57n0\r\n5ghRMCGmDQN1t5q4ZKacfYr4J3ija3A29eL3+eLCwe0PxOWKXNnuV7bysBSd\r\nrgbVRnLxYYNZxYqJ65KTUYuZpN5Qn2GECCt5baisDP4inw2hgJOVIoFah+gQ\r\ncaY5Oq1h9179XZLAB+Y0c0EN4m1DQEUTbsZl2Pz3kKJhMWBcHr0juGctVDdU\r\n5nruzKsV51TCsfoVpdd8D5r99tq6UdqLou24iJac5GF6QC9WjHAYBtc0NPid\r\n7HaprGW9YM5rrTSIp1AOyXl331retn0WRk9geMf8ngdXYO1gGvvEB0L2ruuq\r\nvpYb0m8PhwXsTq3+0RXpmqE+ug5kJknUXS8=\r\n=Ne+y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-use-size": "0.1.2-rc.25", "@radix-ui/react-direction": "0.1.0-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-collection": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-previous": "0.1.2-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "03cecf001eea8d4feaa0b2ed364c8388dc1dbd13", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-nVKckCphbVZvwbvMFx+0wyoOg7pM8qxikSuYO1q5ZHmFG+8dhkhvR2N9ITAIzWxTHynoyMkdzBbOtTT84iV8eg==", "signatures": [{"sig": "MEUCIBXadZ8ZmbLJV1IuNsNYYXw0FALSQ5kXdFMCWey9zgcxAiEAzxO6h0otUia7HHHfFfzJ6hQhJYObTPg6NJuIUjReVKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9vA//WSxoVqCgxyy6EVz9coA798w9BtlDsjsbjNQOvvcIHrnNHxMq\r\nGEGb6841TjbcvcJOwbmo32kKw+QFU7dzPjEZJr6SwCGqrXROlFL7xszULMdB\r\nnLx2Pbj14zI4MDzgiiOoUiBXJ+Q7KMWecBS1ef+sZLDa+NNoLWPEVgoG8QYh\r\nonCCmUbugeRKR6ifBQ9G58qpzYiLqRDOtXggu+v4taphlFCEDd4siqisfB5J\r\nIfg97aOsuT8d9g2EsdUWob5AMfyTch/2sxBBf6l3L8kASMi1PWwuNlE8j4Kt\r\nAArfb9q+TUMpRKHf9snpJHeAUFD4e/Dzy4OVyDt9Q8/Qjvv+EKQ3jAOlO17+\r\nkqv04ZNi+O8Nd1W3fEKFbOMSJlORoG3lRdgbJpLtOIrggAvolIMd4CYyiR1Z\r\n17ZImf6998N+mj3TecErK3w3vCX2uS9xui6OJE/H6Wd/2aWzd9N8v/VM82AJ\r\nkJSzdpPgy3TcA4O5dK2Z9WPup6f9JctChnRmyQSNNT2cV9zVHcSShMcuz0hN\r\nYJzB8bUvCmZQSpw8Z57+nzIjA4/6GsgHjZVL/5pGjAjOQMfCVkJ0KpgGJYN4\r\nzElrc5LeQ+gAVLN9S0YMdyJj/qQoyX9ur4GfnWSGhmbq1W1V5AiNC5RzqZun\r\nmtv+3nm8YvE33oB0mZ4DwPMzglDsGFWKXAU=\r\n=/POu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-use-size": "0.1.2-rc.26", "@radix-ui/react-direction": "0.1.0-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-collection": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-previous": "0.1.2-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42e97b1c02bcd06c6d660e2e47625061b8f4a4af", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-MxqfyFYaQttgth8ysnwmWFH9E9FeRfxDCeLKCnTxwBy0tOh2m0wdupUx4PuQgTdXQIHaUeOZ4YBSrdYmaWkM7w==", "signatures": [{"sig": "MEYCIQCvu7pe8Fc5NLh+4MUitOrOrmRx8mDkIdKqWbnd4/Pz1QIhAKfMhi2NLgw6ejAMUoJXe/F0EbvscsJN+LsG9zizSJn3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphUw/8DQonsP2c7ATf+VynpWzIkXaZVXse8M/5z9NuAhynKKicJC6L\r\nIhOSuYSqXsXKcwFJYHI/CgIp6ghhfWFNJ3YbwUWcn9CnM+bMRI16My4QN/cr\r\nmdjYeDEtoV35w46wXG+4ZUuS+r24qBELd1DKH1QDKHS4uMEXMw+4wP8Rw3f0\r\nSSLvIVjFuk/speQAWPaKk9X9rsyUG4914EAqV2StcoenMVDNbvJWyzjvJMV8\r\n5GoW133y2IdMJSVc0sbNQTfbNlnjetL5k8mk4cKvHUBb6b56NOftXRTuPSeh\r\nxUqbU/xv2WDmafvEc0+CvTDiIoXSJ6XP10lkspej24lj/vsVjygSkFWXkUfA\r\n5qlCieqZoGJMYZSfGf9SOut30j3zbP/lxFmR/N1ULOinnh5qo0/iGtQQLUPL\r\ny2bPpgfq/PgjsFE367MeSuCPHpWiTqqr7NgWlOe7Nn52OPCg2blOgAvYR9Kj\r\niwA60pQfh+LpuVz9N0FGWC6HUHAwzk65dKjLByIP20+o4B9pF5hINvv9BsAg\r\nqZ/eu6OJzfoQD1TWCLffhF8ujfue4gO77EjvFDque/zYttX43ZzgLkR+L5OJ\r\n4CFmXzGbIXCodjOK1L61oFOuV4qcK/ApWCk457dhxNiWYxE6sJq9MbhVjIwx\r\nWFuIgCiOecKioKKuaTCriaz9aaiJo3xgMv0=\r\n=2HHa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-use-size": "0.1.2-rc.27", "@radix-ui/react-direction": "0.1.0-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-collection": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-previous": "0.1.2-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5767ef97f883046bd04d7b6b5c5aa0f9bb533e7b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-O/Rf/65Qe13IxOgO5cdoSVZ+IPUmwVLK0+g1tO4Lxe0qF0RoWMHsUZffuCg2V9iZ5qglGun1d8/ilorfkWHzAw==", "signatures": [{"sig": "MEUCIQDlUtpB/uNrOwG3CY2tZ7cJ86yGKk2qsvi8NQzTwTRvsQIgbVIZqri/SagLY6dcR3+IlCOzjn4C0kyDzhOX9CnAzTg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOVw/9H3Xf8R6Yd96IObNL8pyS5bC2+S7huqC9qMdtkNMIUegysKYk\r\nIS9JmKBGxvcXHP6FzY3kYwPGXlmVBAhZOshguT+OW2PwHjlz6W40E81JCin5\r\nZGYbN233xQ46QRigu+NXDnQ2vaymSfNl9NYlMUX/a2RFnB8GvLvUOLvYpHWH\r\nzMsXbOjB6e4dKDPmj73kctswyR185fi1AizuxFj+in0cme4eSdDilgR3p5+R\r\nmo2KowoOLJmq5zOWQLnnX0SsG3faa7h30wXX0lmNQqMzBY5dPHE6A0ZXpJSp\r\nT2Tk5OmYmdYowqZUhBmoBBu7NTvPIoojvvrlGbRWmnWTsUR+B/wnb8oTeUn9\r\noUK5KgvfAAUi5mFx/yi75TBfjBtrKvUig/vpJbZgzYfvpECO7oX7l93dEuXI\r\nHYGZtadJ8qHRGWiebTbUvnhbMRxlpERbBuEhxfe+cb8EymxCjaDQbAIL0Ctd\r\nd5aig98xr5oilZrExhN9inO1Q7bV3EQS35ervhLsXBKRqQgxNQYKq5R6wfK8\r\nukXmzl+pFpQ2qwkvDlompc9g4woz/DteEILNINUZ0P/5Ujp/UPQzFiRdc3Q9\r\nbHsAw/NoUCvxfEWjmRPH+KmgLzxwvpHQ7RC5p2586Py0cTQDjy9YC9HdP6NA\r\nO0kauxQ94sWxF4ywEcIybtQyYx7p/ykZ3II=\r\n=swPd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-use-size": "0.1.2-rc.28", "@radix-ui/react-direction": "0.1.0-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-collection": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-previous": "0.1.2-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "23f91f68274887e65caeffc715b5463a328f21ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-KelZtkmlJHTtPEAx3mFrvrxcXuQS4ZM63aFHfLYw+pnU2plsIZjurHhh7awA98HWAyLDNtJq53jbVm3Fw4pB+Q==", "signatures": [{"sig": "MEQCICPlzftCRywmBYG7YqVhhLQCSIOhhTsSV7+LyD4Mh4UGAiAlug+L0bOj/EndxOztJb0WGAbyrJ2scHkGz7NLekAewQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbqQ//c2KnlphuL/xQm7KxDh6KiaiRJju3QqIA0Bm58Dnh1yRqVzol\r\nPrAfcl3CuLB51bKZ7Ygfr/n6TArK17Ni3uYRGpJjcozbP/MKRqMTlDD+7DtI\r\ncXOw9L8y+XAwSp81goHi/YMBft3Wqt8esLew/T+8d/PA91z72xytiBdmDO3W\r\nWiOuk+uIPwm5vu7jWREm5k0yS/W+Zm+6jMQB11mqjs9jfCgwtqG2zFQKgMHz\r\n0vyOKMxdNyUs8GH5iABn53DlqOjPQ9OtZaJ7Xa8ufgNBIa3iUxWCnqBFIf9R\r\nM0zLKo96sAT04wABlJR6cFnGvz/etwzULR5NRvNO55+giuxsHenxM0WEDSdb\r\nQF7k7I8HtHdTJ9oiHE/+xUbax9exiCpxFYIt/2vpyECiT9kTuhyGW2ePPiBX\r\nftA67bJvBOiJo2Vxfv9jX0h1hwZUNU+PHdyB3vUyIc6SpK0UHTYvbcrxp8W7\r\nNSSTwKpywk87r05+EiPiFzCIGSTbqd//tfYkgz1l2N5wyQ4/YAJ8ksa8hVDK\r\n1vPpCfa1QZPiSpd65XVfT0tUipVkrszZDdd6IL2RoPlE+3rGYyKQUvqw669E\r\n/OyeJr7lAtaQInzPobN2+owMcw/piTSjyGfrrIjbahp0BckSyt6b8HIHYEJt\r\nXqvSFwBbh1PQPBIN1NtKi96oSXzLH8SpSQY=\r\n=3/6G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-use-size": "0.1.2-rc.29", "@radix-ui/react-direction": "0.1.0-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-collection": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-previous": "0.1.2-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b7237d65649eed444eec22f0a7c7c983a425cced", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-LkDpyGH3SoYKaZewjjoAaS4QDWdrh4MF/y4Iem4rmSVoAFO+LvBzoMx/hURzfPMycBNN2BnpvfIoNbMXBhWqJw==", "signatures": [{"sig": "MEUCIQDg8mUTgJYZuZ8mrxEWcBwwG0pIoDpD6DISyAeX1iuHaAIgMxWiMiRcgdYet3Lg4t12gh+eoUYSDX6eoyTlQinbpSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIZBAAk55548CmjJHVBrocvLYq25Vs+XvLZ6efVj1cBTSpPJD1o4k6\r\npGmY77cO8pscuW1iNduaTRuTQ0OsucO6O+dY+xpO74rLt51DTahV0a3K2ROT\r\nD0IvNfLyIiEnwd5HXOcIiGzpVktxPwB5E4PlCOo3w53ig1/0Xu+ADnD6ppAe\r\nBXGIyPJcwNzMxIjKbRisiTfBsHeChYTSVuMU73BQG1uXXLKRHMf0HvtEoKoZ\r\nUwH27aFAOztlueO1pCpLOMcFYVbSypI/I/Os0NjkIkNPAllhHUQuha+8xNn1\r\nIz3pINjk9/pxuS75xpDF6SNxh6CekriTos2tL20vHLh95H5kymsQrZA27Y6V\r\n9r8c22lwN9vA8xxQ5wp1kfFxR/8ofVu3c7t8gKQbBs2IeOpTXfMfyQCY2ToN\r\nrHsH3YCcjVpO35//mMq2/qgI/CivhWLBU0JERIkDBG/djQkHaDXt2yKC1TB9\r\nv/ytNYUww1wqjDEyhmxndqP+yGgOb0gYlNF6rNfP7ZMcNx3jLWKa0nmDxzV5\r\noLC7jsD+Yozij396uqtPYK1EbC3CI8aI+eBFXqnHd+jk6ZnbwiWD+P6+YPwG\r\nFRJkiGLqbev4de1/BWZ+3g+4eiwOq/uVlBUfNWH/qsnro3OeVG/62EM8Qsgr\r\nbtvvg/WTWspyw55ijzvnElAX/XBwPTpBJec=\r\n=9HxV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-use-size": "0.1.2-rc.30", "@radix-ui/react-direction": "0.1.0-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-collection": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-previous": "0.1.2-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "35518c6917d4e1ab1928e6d7b39fbd4681fe82e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-UyxX4O1xvsN0q13G+7czkO8IVGWT87ELZXBx40xBpNgwmFovQRapsy5RP7QPVx5FB/sUo+YeHwqnVtFovZ40ag==", "signatures": [{"sig": "MEUCIHzVyjfO8ft/36V7K64iiR7sgH4VHz/AqzhRav17cU7tAiEAgrdmt81SAL89H1tTWvy6euZPeTgVq8JAqQP+HhiO0m4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJ4g/+K7s9bj0BCI6L8RW95a6SN6iYFjKzz7nmjR+ezTvJ0qI6e6o8\r\nK6O3hnnQGFp1AheiykKO5khosb0dSZ8C5VsXoFcamAnZ8JSfsBEZtPPHn3eh\r\nHkLVgzitWIoZmkXh0IVjsg+ADLYE2xKCQNtmaz20pELDc73iMh8J1RVkEzNg\r\nKrgbIlfTWz2MKC4lqKDJNz6QyMkyEL+U2G7SjouI3S7TU+2w6mA96yo76gEg\r\nTqKeges7Am4cUSsCE+SLRMz6nVs14Px/DHUMMsO802HtK6pVOV1KbEwP6K1y\r\n4+n5/ne4vc7VcXtwUUELrGBtUvHRb52Hf4ux42n2ummJ615+HyxHsflGnPj4\r\nB/wl2qUJm9mmmzH8L8DSSCDj9wXNzyBhNCp+XEM7DAPxYniCmsbPaLJQQnDU\r\n1Q5XErA3eGW/XHQIAWNdRRqdZ6eaQEGRiaxMpdjZcNrMiYkRBw4hRA7wuNQq\r\nsvoZx5kmfIZzB8Q5ULf8wi9PySMgkTco/E61jXt/r1KzZFVjkHwUlin+sBoF\r\nL0WdXLjjit1tTPw5mfjotz9tIHslAErIXUWgREbuTM10Wdjp0MWmKFOvOVTC\r\n8nkuZkU6h8z2PcYFFhuSFdnStXMXhhQuONpZWsuUUuDGrGMr3a6n0YL9Pz/7\r\nFqIKeGQDk9DEQ+skDhnSqq5B9YTag1rqnn4=\r\n=v9WC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-use-size": "0.1.2-rc.31", "@radix-ui/react-direction": "0.1.0-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-collection": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-previous": "0.1.2-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c6ebd25b9b18aadf252b96d71e7373c0f9e7327c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-GBq3kF1t4dhl4GwDzg5VmzG4hnPqWEKHQiiDiBpkeuZDcTwQ90CR1cSZD5SGkr/NxCcYZ9Qts3oZxTN2eJvdrA==", "signatures": [{"sig": "MEUCIDvJM659NS3FgyEudeSsNoCCl6GRn3i8UXoagwcFAooeAiEAxXjueW8FBTNeKhFXrcbS2daCoYa8XetqAffAkODMoyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVmhAAhk4ce3Bjr7WRAuWVeVSNH01unx8CzIpyhtAzKgyhh5+V9InY\r\n56xSi9N70s4RbMvCCSOxtDazMf9PR3qinzwMOIDyiRGUnOXPfgILtMdEmTfj\r\nSElKDSVp9r53G87vK2ofQ+2Jex0T2mOE9M0ARSMi35wwvI2agfwwGCkhsqW5\r\naxDx/OOV0qOMJccGvAfk5Lv6N7yLY+jcvX04nMvjX0slMR7a2NkDg+L+qeCI\r\n/O18jmajnsqYYrgs444syvPs7kJS+MfdlPa4YI+yp4uM0B1dJyooRL80Swxe\r\nu5eGGN0KQnOl/23gdYCpK96ICIiE6V1+gaP72ICnYGpuF63Q5K8OWCB7cZDq\r\ng+96QpBMYylbT8B2rAoS2bUlQssdj9DthLyEz5dYyuwvRYy3hvSwGnrtvG2L\r\nDvfseo2gT5p00LC8utUYB6pqwRAPiGELVgxfHGkBmrSrV+9dhW7cuxOxOgZb\r\nkHATd9ffjVwI8O3uhpnyi6BuotGM6Y4VEfQ8NQ1GwSeM8VEkOEx5OvjyuEf0\r\ng+GRJbgW5SR7EeQ+62Hoqn0lSSsfPgkf1YxX3rWcq6GNqLdYuY3HnGmWuDox\r\nZSWcdotu6hTSB+VULLpIovKci7vtTUIuMVWVEbnaWQDRkfUx2oZm+xQnVRAy\r\nOV2tHn1NTPWbIFsOTnJevQmhr2VcC2GPNm0=\r\n=0Ocw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-use-size": "0.1.2-rc.32", "@radix-ui/react-direction": "0.1.0-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-collection": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-previous": "0.1.2-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "44d8d272ab3221c3357476c8635c33abbb63a366", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-wQsneqZzoEeuzugv3O86ZRM3SrXsDtzLPNPYOK8dtsQFsat17O15VCYdsAMTiKflTI6TGxBvy+igf/BowAAljA==", "signatures": [{"sig": "MEQCIA/DQeIJ7ppgHaB8OSCxRyRYA8IrWTOM/CV5XZO+iM1hAiBrR/5gDe2Hzyjki2OjHjd4cKp1l15f2+znX9qIwdztSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHuA//fFx/7hnQn4ahv07Bf5oc1luhtQ4Jdy6bUBdEaAaobAnXGkUl\r\nMr2F5qJyYf2oRvPyIJSV+6HGvrbPJAu0mrCvHBilpQfcL8S+dpWCuUuZTS4t\r\nbKqp0k65Ezz6aGTRI9T3EEUCWo7dbYRL7qe+pFO2q6hIN1WVSoVSiQMWUck2\r\nsECLHw2X3HkwIflpIaCCG3OQm7AOVsVl2tHeHk3Fbiulz6KCllEmCJT5WHgL\r\nZKW5e06gb9SaPsoJjaJcvwC0s/v8ja2SDE5GMTyfIWRTKDsfefCWgp7o68k8\r\n39EgUNXUU+RJyhbd3vQA1KMKgBe6lqR4SaamE87NDczNvAPSTLp8FRJglcG4\r\naeZVkbeE7+6ATJ7TJV46pTVbtyz8N2tbizFWMZBgsNqKHI8mFP6zCxUKajZ4\r\njjWtfdr9CRoa60TG6nPzrkj2rmY5f65oFhReuxPZmJQ9k1IuTCyCTExZQX+S\r\njwPlJsoe8is7V42fiFlSLTG3MT9dB+ON6idTbcS8JIoJ9/VvVMB0C0DfMiTh\r\nOFb/TMpqO1nxjxyty4mK0pQojF4un4h6FJHTi25lphfPO9oslR2JEqAvZBEj\r\nSmaeXr6r+GiaUGmBDzB3sFINi3BnxmRw4t59E/lS3bWIXE9LXmcz6xobwiHH\r\nSNDX2GGqSCms8/GzSiirD8UetEG7mWGVYcs=\r\n=EQs9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-use-size": "0.1.2-rc.33", "@radix-ui/react-direction": "0.1.0-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-collection": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-previous": "0.1.2-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eaf95103c4dc9a291ac0316d2735693e70ee5145", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-TyvNbLzh9lxmW7UxS//hjketmiqRpn+MOIIrCuV1h5Gm0zEB9lNmeW3ShyXKDaS+3wFgvJpPSkpXbxFEaBzELw==", "signatures": [{"sig": "MEUCIBFz1wDEvkKjZzotb8Fe0JKdaM/NmeZ3uib8MFq1LUaiAiEAncK3rVDh7d7FlAWCzQ/U4NEL3GdyCPdb5sAGdHiSgno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmovng//QAxOAmiEGq+U+tv3Z7mlOMyjSIHwq/7Yadx/Rg23v8ldrC8T\r\nrvfoDuK5v57X208La2l5H38yZ+YjOiW+KHz7XhJPiKedSBviNzPCSYzU84VW\r\nSDXt6ajsEPkUkxtAnQJ+O3tGijRvK9Q7wliu8LB41OVsF1lP4Zfw8YpN6mn5\r\nbfY0hDYVp0XkQ/REJIibpkzYvEFtHxd2V6GnaCbMnmvUHrknqdyGeWkcw2m/\r\npOIMq7phzv6BMd5jxXda7LdajbWt8IzacSMO5EXHk94NSmxSdYDeMgrb9HZN\r\nWdZUXfl8EEsA8hj2bJmHQlb3DO/J4G9o0jt1PiNqvk2cipteyrRix8bmwQx8\r\nKL/IUfHwhuyKDSKJhF4OZSO3YUf9pHMUIOTha6smphDVZMh4op1AbDM9T8z9\r\nZr9oh8ndDhSt4zSqpKbq+CoHp/7eUktYaTiITNseygGdOjQwAttuuJnLgjjq\r\ncGqIlnBZRSSfpx0XTbO+yH/H7p8bmPkjw0sAsk9SlMeBG/ho6/Mv1SvptxeW\r\n2DXBOyP3yGubSwCLtTWo0783IqCkzT5oaXPz9hPpR2HH8pLK8vJTzJlPNkA+\r\negLT6QxdW7EOHjQRiXNzGbJgGnVENdyMG9lYsGq4R854W6ZmFNIuA/B6gaRB\r\nz+p0NbFP+qeyJ61N2Ul8ZCkSLhC9cRkBwg0=\r\n=eShO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-use-size": "0.1.2-rc.34", "@radix-ui/react-direction": "0.1.0-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-collection": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-previous": "0.1.2-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b9cd70ed0eb69e1ef9c26df65a37424513c96950", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-rBqNj5bP5ej4RumzXajQD7zOz2hFH1h3lqiFB+7oni4xIM17Sxlh1EIBCdtIA5LsYFXgQawKTybsLGRS4kAbxg==", "signatures": [{"sig": "MEUCIQC4RgdnJXKa0ALdAe+Hw2kxcsCFrsrhgcKgKX54f3k19AIgMvE2UcNUWAgQZ7S/S4GPn+qll2C4/aofkJUJvjK0yzs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptBA/+IXc5TSI6Stxh1GD70GOS7h1HZkHKYcoJTfZ1NumZLZmIm9kw\r\n7Q5+wTzXiNM6JszYjZYvFubCoZEINSPVOhJe2MUlukyTS8JLCFCMNghaXLBA\r\nF00hmK89qzClgbKAcKXU/o2dNhiZecbyB9yO7u561X2VOLhjNKeKK63fd1fc\r\njYrSFIEmXJ6loGiZnNU8oK2E1frYXVkXdBVIG4aigGg2ry3GfraVVxW22Wlm\r\nfdnAM+qqxvPFdhtzz4+Xp83UOes6AqwLTixOsVzO0eFKVZDE1vtkdR916VR6\r\n8F1niNx1h2vKgbUsZH8Ml1YPTE+YUlHkbgeyLx1zjiOT6XdqdQ0tyIIrs5Tq\r\nYutJCSLS6Zd1kNCrj5763xKXslClJN5vBwMS70ZU0Ipn6Ixnyp3va0UTH4bC\r\n97iaZiPWRg2hdei4PBVY4jrorURUWSG+fbv2Im+cC1Unv32bqOGQAA4qDWnL\r\nhJvHL7iID/lbnCUAgGEUQ1+HrLNUjsDZqDzRu6a8K1rFaalS2Vp/wIbE3mvs\r\n8YtalCDLChMR1SZOcHLvXp/rTY2uBkiySDX0fydxnakpi8EuKdqTSSbAaZRq\r\nUqknEIJrxlcn5qnoErwQYOLYSGo5r3rOM3FaFfbNHr+isyPdnQnNefUXW+y7\r\nG+IUjr3d/8EvnCSvrDVlXXSGnVihI5xbZuE=\r\n=iDDB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-use-size": "0.1.2-rc.35", "@radix-ui/react-direction": "0.1.0-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-collection": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-previous": "0.1.2-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0f129ac5240fbf0894873c9d6529d2db229a9fca", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-t/tqwJZJLOq9fQv1JVUnPxP/lkBBDOkZtUafqIxaKmkYTbVGKElt/31GRZmJL6g3Vw3K+QiFjnKQ9W3+WO6JGQ==", "signatures": [{"sig": "MEYCIQDr3nOik/9m4q8wmnUsOvx0w9Uq6c2zHjOSPxdDCQIM4AIhAIAFUj4x8wHdv/5zn5Ac0NUoLHROr3Wme6gapyk73fv/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEZw//TFI1miXTNLMyv2P6rMTp+PniQAnzDw49ZUrIKZc0wzwOV/tA\r\nIjnCh1AoFtamnHWblm6c8YkV8wnUOgfTXFbyP0/EbfD4zBJHaB2EE7DxRbD9\r\nEwDZkBw7gchwKzZgQBmwl8DPD/zrC0RE8wCEi6AkOPgygdc1yUUNnS4MtFBe\r\nv2xXzUmW2+JKE4RwwmH0fzxpXFYPBi65eTayRf3chMmubdWYzwX2U59jckG0\r\na1DXb71LlIWjykS45iFa7PPflkEe6KS0n7/1nfAhDcZFPT7On0RauoX6EC2Z\r\nW2xwPJ5v6+4dH7hLzg2/d3/lR9kKEGeZG1sd5214/OqEWPLt0SBXUAkm6QMk\r\naPQaa53SgUtAIJmnAN51pCThwBKFkQlY2vpeOd2VsCp0AVS1cXVfuytSqf5w\r\nuOl6aQWZKC4W6vzOdvzHgPEz6/cbh1h8Yt6Ft9wgurdBSQ1AXmSZu791JqXj\r\nebMbViF3VjF/LiUD8jNoFxnFDSxcljeRMiVsuCslOMELx9J5rER/D4LdJ9lR\r\nis+H3bLDB89Xsxe/sTKREtOeuEOWYQ5bIZPtXwWfnhDtFKMbSVTWZQCvb1Na\r\nUFj3nvps3Yp0DL3uviRDRpLVn6+kd6qzRG/fLY9uMAUvxfTD4AWB+klLibma\r\nlxGvW/1UUN3zQwvrdI8VA+/dPlcXv/JBncI=\r\n=i44V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-use-size": "0.1.2-rc.36", "@radix-ui/react-direction": "0.1.0-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-collection": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-previous": "0.1.2-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e4b4250920a53b83569c668c7b9b034cd097d0a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-2RU9JIjtz0fB11rdSZjsHk/g+vKZtO8mDJKA+vEtMUpGFv1P3O+xNo8wteGHuf+32zov7aqVUk2Zt5or257FnA==", "signatures": [{"sig": "MEUCIFHHashQjEbBS3ddanT7YccIQdFDH+ViN3dgBtbEph0yAiEAjYg8UndOIlqoAdgao5tc1VrhRohWKpQ5013s310xEWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0I5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDyA//UTFVg4p5J37oPbYT7LvNdwuu1XTM/UiqcqFATzcg4547gnzM\r\nbCKdhsfJ8qLXwM7hjf3LljiNPhKa04zZivBgkz32/psVBotnoiOJLyh6BLrF\r\nT7gtkI2nOqHDskpiUgcwTH785zixI1Xc+z+pWBXOBOZF3BZxqvhwuwdq01IW\r\nYo3CFfW4cJOVZLpjBngzYXCBMU2+HsT7wss0Z7Avl/grDDpLaf8xPlwm75dc\r\nP2MPm0DynHGQIg/adly/gFYJND3rc+1hawmUC1+Di+vq6zSFF7KTuGSncyUJ\r\nY/SEhQgNA99OgsYlEqXhpY6LqKUlM+s0c3qvprmtV7puZjO/ov9nmWLx4RiY\r\nflFPpOqpqxUgl16is0uq105B5w14HyfC35r610c7ZEZTTTTW8XEnOVlzOkvv\r\nREEc64flChOzEPDFryxfGC0phkRherLIWWvwB1Ft+F/RUXxTIH6ISAvm/dci\r\nxBA/FsvvCpdxwNyUrbRrRQyDUivJfaTJbMtXtRd+3xrTesh6h4qXZbCg/QDW\r\nSVlm4N0lJuY2nLyH6S3wh+DtzL1Sa9iye2ILgchvVuhYfPM7e56HpN6EhR2S\r\nhxTJT0MWX3xglOK5pQZiKzr2/BVnmqwMdqc6uk0qxpu2B1Z0r2Ps5RX+MZeB\r\nH+dlvDVGyrQrRLZFvS6dvXHYKgSTFOFA/Z8=\r\n=ujJy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-use-size": "0.1.2-rc.37", "@radix-ui/react-direction": "0.1.0-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-collection": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-previous": "0.1.2-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "94ab56e433e8fd4c033a9fb1ee3de83c8f394720", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-G7eMhvJTMa6vGSIqGEroHo0ZPbjJGYTgbP420ZoTpq6CdiAe2Xx+koCJSPHX7MPhnDTVlxdIhrZgHJSODVCGUA==", "signatures": [{"sig": "MEYCIQCueLTw+28xXw3scjiGGVJHPp8fP0EIW3KPvBlqyKKtkAIhAMvMS1SML606cQw4YXWB1G9uaU3ibVpAB0JazW+UO5xe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozvA/+LVsLK/87EfLno1w7MZC17h/bOCXT8ywlWBkG3/j1gMNNFjHf\r\n8ZKIZ8VRZyHIKf4QLl0WDKWu1fldfgWJnkPVz5gAc+BzlIIDl3GfRVjgSJjK\r\nBoKo20fXokpmZNz+EXaubvDbQCZ7g1/p1fJA/cxYHv5MQ4FcSokr/cMj42cr\r\n/3zG3X96gnix6E6FVhQn7VFNXHBqmogFQfuH3OfFKX8Nvg+CvdU5iG9qS6NS\r\ngIOTrErmvk/YC/2zzDHIJ2xf9Dyu8JUiHGjcZnAxARipZP++ikTxVAlXtsDh\r\nUX+HG/IFuEZ1dGK3RdCYnaj5rwkIqjq9alQ5qWXXj4oRASMHSMM78w7tQ/i9\r\nfRMLaD1QD+/GFiZcdaCvK+roiEZ/KqkB363MEjy6YNmLNQfjwa326bY1DUiz\r\nDxCdiLNVlqIHI5zKj5rAIK97LC0hIBtCcB9Va6cjKNdFS/oCJfb5Tokq4+tO\r\nju1gHLPdEYZRGcK9R5o/zDOQQx3FE3Aul7TiLyuY2j45m+2QRlBR+AaBjxmD\r\nb2FZQ2dPTmqSos5xPVrKdkRbkVbJIMBmNmQbO7eUYhWYygOmzeochLjG37aX\r\nchwYyqARqyjeiU7ydp0OAbnuE+BYU/BeioFqP9dg3pRDoifzs72aQ+RWSobZ\r\nucHnKY422Ut0brb/NX18cn903bEhqwNshWk=\r\n=zU7a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-use-size": "0.1.2-rc.38", "@radix-ui/react-direction": "0.1.0-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-collection": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-previous": "0.1.2-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b704651463e049f8c5843bd85dc633d142a22c87", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-FZ4WVhV5pD2p6F5aLBSbItwZDZNc9Awt/oHG0BprIVCx/8JK3KyyCu+W+EqAamr3+37S1HrbzemBD8ff3ea1SQ==", "signatures": [{"sig": "MEQCIBHDd9Hg2luR8tTa6TdzSc0PP2g6rZDq2rIhzXtIqSGtAiAk5G5LRM6LYHPDK80JdiGk9A/33p57BZu4t253S5eNLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/8A/8CArQqJ87rd6CBugKUbBWGzsCqAXdQ8l4BT8u1Eq976RNxb8x\r\n8tGu3pwuRZxjkpebq4vpQetQICSN0pYpVKYTPHFbtD/k2b+EJEpFqdNzKcns\r\nXcTIxgTh+Ok86s9GG5xPcAu2h02kOWoTx3Lp0MGAyan1sAbVQ0AnlwSYktuN\r\nMZNo1QhRPDYQtwkmYJEO5U8lgu/ToPqlALVucW4EeaQbryvcAyIHmKiKeiN/\r\njnXDzkZxFrzj+svSeFl0ZvcwWMVyDqqVusa3KBw12km5zDSaktApUO1u/IF7\r\n8DeU7QXqbFx+eoZelVjZ6HK93ACJqs/HoNRJFQTONZQmK6LRvjBLypbdwbfD\r\nM7LUzRDIbMo2tpBqmZgr+5OfGiaAWSCQ6+OaC11DRg3gWa9uEe1DqBvXgdwN\r\n3D3G2SFGGY6EJLuLXGO/4iK0bXG1N/N6gfatRYA2tSFvQoD9AWEGYkQB/7jY\r\n8EJ8I4wS9xeABTv2g2UZokpKtldBBIqecJCUueR4YsjSIcnYdsau5XMm9Dd2\r\nAfGe49hUD/PGWvaKkZ1D71qxQtBQYrp/OZP42PX3NUTvudZKvfVx0gER9th5\r\n+IBB03hltjd94QGe5rQ+ZGsmGC5igpDqbbpw+U5xyMIRNcwrKAZl/3twNk6D\r\nH3EC9LqQAgiG2nHobwLiJN35KtP+Ee5JKeY=\r\n=r3XO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-use-size": "0.1.2-rc.39", "@radix-ui/react-direction": "0.1.0-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-collection": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-previous": "0.1.2-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "07cb7fd6ff2a9a1c588cdd250f345524d43b43d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-Wz64Gf+OC/2k0Vb6fpQV+LKnlwSzq38LHsNsbjXlLWdC6jRIey692cSz13QlHq9DrWBy3wqLxvD3opta78RYAg==", "signatures": [{"sig": "MEYCIQD8x3ljiEilQaTqiT2pdPr8ZW6baoM5iIvcoiCnWCRu+AIhAJ98eEGbhwze4w3lzkRLeSj6y3W5MgN8DzMknMhV+TNR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpDbg//aG+1JNZRhbjS0V02utu/AxzsQRRHSTmusqiRUyRQBBDQHPre\r\nQpuCeNPWioZ6mfiJXSwHu/WsRBm+4Fp5hXDjxY8DZOCWXeUNJW+ekNpjPDWR\r\nDtlHhj4L68h/7BaxJu3v8m9kLScIu/tb5mmSu3dn2puEj+c9TXRpGD9kZYb/\r\nx56YrIL85hyvyUkW6Cu0g+7GMAQigasfdTxbQpQlAFSANGmifR6ybGX/y7cy\r\n8cocA4a/6ul60ulyVpiRKjPrFM6AcEUmf5y/4O1D6922NmpejkVHPuzHJwJY\r\nyp3IOX4gcXdv26fGkkalmbja0lXnwYmTAvScP07o7080KvCrSn2mfkrKsT48\r\n3sI6PeUgdEqi+/x9TEKu1EgKPmy6NpA5pA2YWcdci421sJmYHo1RwAJhOe5y\r\nYACssRjuMYkiRzJmxdhSS3DSWu++PDETQsqY/55fy+VSdmZR52U0LYrRh4d0\r\nTNqke967+hN3NCTS+U/NPQEeUr6Yn8IQm5XRhvTFyY0CL4ILSU+fk310uGRv\r\n/vOle/meXo6x8LFL4FphjeQXcrhwgmykg8MP9rUqZrgwoVKOXlLI7/hvmM9+\r\nT20H+uqw5b+QCHxnyq6FFYrk6jR8nfbQYzhofCPOCCBCwGgu2BKcypE/re1g\r\nlO96cumN+j/FdWFfzpPqpl+1pnQGxB4hT0A=\r\n=2bMK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-use-size": "0.1.2-rc.40", "@radix-ui/react-direction": "0.1.0-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-collection": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-previous": "0.1.2-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93e327074289730ef5dc687d618f6a2261dee86d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-+LypqhL6AvXTfuGEG4j0PhXxmVI7OBfDue9eJbzcJ6ruUk/DaQLoGcZwrLT6KKIwV12MHOe9xl9l4UDjsqiCVw==", "signatures": [{"sig": "MEUCIHUIWEwr9AwKAoWerB0XQv/J/WEJX5A4T2fbmHiaskz6AiEA7DkOZvMCl449hrvMWZKC3REHu6hXBRE6Am8z+i3/KOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 164926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3Ow/+OL5EJ/KzlhANxZZFipWYSVx79/rU32DhgdwHtXHfO249QZ5+\r\nGETZfFzX+++18RnTwWereeZ+FhB4MR7LTko1HVQKRqH2CSqkNaTUmRuOmYT7\r\n0cQv8rUJ6V357yljUGUUH9BUzwhOY5iglhQVvlSRBRNJqoJS/EACO28I4BfJ\r\n5xz9qHpwdnm2zj87jxsgyuinAVt/wotNgI0GP80YEV8aIzkXjMKbyjlT87YE\r\ncoTDX9uDA4jwZJ42bdbCikwrz60EYhdbzUVs+0k391NYwbfI6s0Qgvav3LMO\r\n0ts4BOWEWjTFsg2rh8rjj4/1csYWB4ub6sfe0bG56NFhXo7HFXOCT3SNv1Vi\r\nOpASpA1VYDiCxL2IMkxCtuqbtwjw2RUTVkUs1yDJ17+BNBxFzq4TXNEYrbmH\r\n27QZr3R/m8/NSUT2idAH/5rrR1qj+Sp7lXxdkdNJYIdiMwcRjRG4DB8fUc+0\r\n0U7bJ50H0RgHziOhTz0Y3H+owNC+GXlIhE8bXF31IJG72QxPB/pSH0t29zi+\r\n498syFhnD/QbTrZG6Xl5rQ6lUtysoV3oO8KvvkeA31MRx6/MiiSyBjjB1mAf\r\nhPSjnj6M/i1TLRNXKfvxMvh96fbNd9GuyI9Ff8S+PJZx+0KK+O5RXUpD/qgZ\r\nngi1lbM6/sLrjwVZGID8UwoHg8cpSM+8+v0=\r\n=uoA5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.1", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-use-size": "0.1.2-rc.41", "@radix-ui/react-direction": "0.1.0-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-collection": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-previous": "0.1.2-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b24363e394915008d7091463d8226039cef1fda4", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-4iZT2YkHH2hyx+JuD/2K+ALWAS/70nXza8Pb0qxCAa2Q2vnxtKJxY+C5HkoguzTImpUsGCX0mRadwK7ss7PTEw==", "signatures": [{"sig": "MEUCIQCwhES7ejW+wCzo3vsXpWIx3khfuV0JvfRGXeXOznsuQQIgfR+/bpEf9eZ6diZmFbCpRuOnpqCwsHT3YAxqqX3RvAA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9AxAAhocWyt4G5wZZmP1addmx7OJmAkoGu2RBtY5Iq3qjZbi9yPhd\r\nyPVtfacRmRh9jOY/Dd9l+YKLejki3wZ/AWeSDj30uYKglbTDB1aViJjt3Qtn\r\nABo2g4+ornIo0bZdXhfmIqN2mOGOCPPcB2L07c1OWexR30fQC3ntuzZdb0Yr\r\nxzuCED7PkQZ6hv06VRFcaaYuAAmlJ96xaSBpIDGiaL1hE3oM0dzJFMiKUhn1\r\nABTWVpsSXU5Df54wQPQXkI+zg2DpbMEWkTDD/79kUeUaJ9byOJ8bVMipqSxX\r\nxzQprXw3pFPbBoaYGdmZryIa87lG9k7qIgbxRJJ+mPt8br3orA70K2cglZim\r\nsWFEs7/slHTWjUzn6o0/tV6Pf1UHf4tVkcAll5t1tyM4lSIrwvIKtVXAEXvh\r\nNcGvd+dz5soR3Yw/CRbR9tkSqR/zBaRsbMDdTM1uF07M97MVJy8pao7tHee0\r\nYv1IRUICBQqQjiXkYdjCUZSSwhwK9bXRtd6q8p3tWZuj3YF0VtCqOLzpCxur\r\nShvIdP1Hcs8hCMsXJt1cj/s9yuuGTLDsZ+nzs6sFhvIwPNhgd03YNTjmYxLa\r\n8kvMm0fqKwjJB+J0KkI0VsGBUj8Tf09OpI3hsJFfv1PO6/dt9uB/2hqKfbjE\r\nDSo3NkyY7eQJQfzKMp9Yx9foS00ITAHJ8wE=\r\n=XDeo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.2", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-use-size": "0.1.2-rc.42", "@radix-ui/react-direction": "0.1.0-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-collection": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-previous": "0.1.2-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "369b39963a8bd6042c5055f43dde48e9cab135bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-0BGtnjk2duFxGB402VEv6763eIVxaZNoDXDRvBjUmchFnLeoQUFjWv/hgTwOwGWZ8d5fjq8xLTMkN0bmqIafkg==", "signatures": [{"sig": "MEUCIQD0ClmVFNKBh2lW/ASp56ZeppHkJmUmrVOdtn+NfNpf1AIgCgagob41OhYSl6m8st2tryjZn+qROsrOdel2wr8fccY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6fg/9FfYh4juHdgN1ZOlYqUoctZwlt71l/5/Yvy9JQDel80lZAuED\r\nXnPhoQ1R7b3w8D7xGDcQi00a+HNG/IMISOpuS73fnig8+7kpOv+4tk/Vh/le\r\nSjbEOMUNdkKDSoY3ZJcOPAVUWuPL6yz7+vKbu7bem/Lk9P9w5e8PVdGphblJ\r\n+KoQeVBYL5Nj4FeHTyl1T71OzPd1++mJJ3pxRH38crOKJOnFR7zVzw2tSojz\r\neS/5x1f4TkWJcYWp+gxejJRVz6Vu+ty5fnw9j0NlewzQANTsbk2fTq/cNQeo\r\nofBjfFVI710XLIWY5CaS36iKJwPIMJGNbmkEx1I1selnuCcmXwr3Xl5KQiEr\r\nyUecn3hM1GJELo/IgTE8f8pqRifgoY4zcVjbpJrh8KZXl00blWHBBijEpzVD\r\nOGwgwBxHMvr+1hff4u7r6QxUTJ9JAzTCWIgaG98R7E34KNvTu1VjZTIV7XPD\r\ngSBmKRt6IIdstWje1QEPYI1zTTA6/h3O6YUFEQr7NuouG9fQWilHJzLoy77+\r\netDr+4OXoMIrav4JnQFmEhPsF3a0XeDyBvB66fX1mVR3F+QJ7ntFGGm2vTwK\r\nO0Jbe4BomGV2vD6xs8JNtQIq2JQ2IfCUQNeHCmC2aa3SgEzT0Ke3OsYTnguT\r\nlsj6P7H8WU5FpID0dQyIb7PJXC1l8MA7obM=\r\n=12CL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.3", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-use-size": "0.1.2-rc.43", "@radix-ui/react-direction": "0.1.0-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-collection": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-previous": "0.1.2-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe175255584e65f4eaab19532e666b88326696f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-7e9OE0mFb9WEOeXHE/PqN92SBmT4smiG+Rr/x5FSsBcwJuRd7sGChT3duYlqe06IUANR3rMgy0BwuzgAmUMCsw==", "signatures": [{"sig": "MEQCIGS1Irnj94PeDXmxjRBg30DinReQOEJS+6aOv6J9J18mAiAj85qJtL89kU+9UMyUFH3liZxNxXnT8E761vIc2PwBYA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpATw/6At/CgMNOvjC5DtGYh8KqlE9/sGnbMuODt4L5I6Lo3NU9x9Jt\r\nF0GNyZnTktIYhJDvampUAF9UljioMRxfz80mV2/+AXqeYTIQnw0w0hMRL6o3\r\nYfKEKTT0e6jfY5oEaTS8VDyNyWvkmroXI+CNJZ0tRJY96YgHiOVaYAlEoRvz\r\nZvPfo42nRFXGhrxOSsUSwgyFJ492hUDdoiZ4/fDh7jxGntfnq/EGOX19PmjM\r\nsP4eeMul7Ei8JGFs06AwD+Qcy7r/oXy5dF8mynYDvcF1WyHXG/PIqyxbxnZ6\r\nuY344LzMEE+bOgKcAcxKxQha473FTSerfpgXU/gPwgIKUvgSvAXbATTs5KL7\r\nEUg/KDNc+2GH2ltVNDzb6+GyuZ/R5OY5/kuOUuKQfF5iXAuMt2zAiZohiNUY\r\nQLVsBF6MMb38tfZRf5wNHxs8CPsxJZLPH6MmSiLhBL5bXZUTjgVFN5h8mMTH\r\nhF5fyqqVimx1I/Qg+i5Q76LcNuvmZqyi9hobY2a9NAKruZ+ck2BvVQTzEeK0\r\ncelwn+GkO1BRDL9mJqmDVGuVLDZkiGsSei26W+/tL8WXrB2701D50wcaX3O9\r\nwhvSUxDMRYV74LpLawmAdZh8LEaI3ADJ31aPJ7dBGDpG7zgbAOav91YfgW3I\r\naHLIWWKvidYIJo7AdA6Go9f6PsqPq4wHcFM=\r\n=hUoJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.4", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-use-size": "0.1.2-rc.44", "@radix-ui/react-direction": "0.1.0-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-collection": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-previous": "0.1.2-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "67c554f6038402f0fed8d16dde87402ce951e9ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-lqVmqimTHwUNiRHHLk+bhVqaKXmg3YKNf3w3Ar6+zQRIeGE5pRi7oXkDFCDy5ynFrJ3YVh5rjLwjC9NK6MgYqQ==", "signatures": [{"sig": "MEUCIQCrNW18iM8Mkvpy2v0P4IUIe1DrGsGYa4U/fiR+EnEHGAIgbuYaV2MKm6qc10MI0XjFxT9Vxmd6QNAjda/LzS3ydDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XG6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr87Q/+PTzZrfyDccaixCCtUY5DflohYDviJWf6WGe001Z8jx2BJaad\r\n+qgrRaPKCRy+0ybVDEZl1QHTP15RiQeVjuJV64m7qlhENA724bN+Z1R4B8Yd\r\n4FGy8zYxHPkLCEkZHwBmQD0s3nAm54zaVVpZBbrhxzcnMl58FPL5OI6+Ws9u\r\nLCPgQfWXbGu9jaC6ZzDKO29iJphidCQ7G7eCmq/bEmBIw/XCSEs2k16W06Xu\r\nJRPvL+EePTUIQ6nFcma/u494Re7R8iD+W6QND+o0OF0KW1pBGVpBIKOwePif\r\n5nLDbfR19njaGhlH+rlbI9kaFPu/RYo3AxPwFf7PApG4grhQvm3QQ4ABQ3hL\r\n5b/OiF8HHrIzb4/19vipJOWG9KpAL0uZAPiG8d4vQxPDrR6+Cd7uPjlCw3Ib\r\neP7EUjZpbyB+8NqyG25s8CDQrxpzUAQ+vOBv93vnQTcwbLL6ddhKoyvQeyTj\r\nSX3sIsCoI2Eh+zYwTqEuPBKCKzn4GZQy9hLePyWZ3L4WkIz/olmUsvD8qRWF\r\nJojtfouLid1YS6FfIDFvpIbfvC1gvJJ1R7n1uW1dHn7U6MEmTLH5hqRcO6Gk\r\nIxtZ3bQy9K//+H6wcP0zBfdZCke6lAOs+VMG9zztrvQ+gJJn3EJuuXPdEWtM\r\nd3G541XwTiJGG/BmVGZEL3UuqjOULv+k3ME=\r\n=fXa3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.48": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.48", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.5", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-use-size": "0.1.2-rc.45", "@radix-ui/react-direction": "0.1.0-rc.48", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-collection": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-previous": "0.1.2-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0d15cbe001c33d801db91aec192d9cc03f6a85b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.48.tgz", "fileCount": 8, "integrity": "sha512-7ZmGt3Al+vAW25VU1gmWTcuELxk68aqYm1gF0/azAPCV8PvnTxiaOkijpGAGynkt8QIrmtUAiu+/52bCWOLokA==", "signatures": [{"sig": "MEUCIE9CkzTBp7R40TUqojren///CScus6X6PGcs8/vt11fvAiEAuAYVw14yBYe/nYkWXQmQwFToHFxo62O3kqX9KO6yXTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowcQ/+KvKT/M4x2Fp31Zl7CyhvwD4C6t0LJmyylbbpvRptGtnoKQu4\r\ncLVTvmqxdYRREMSEP/giihX7Tcns1vu2SJNiHpZQjKSkBPWb23FBI6w12X24\r\nK4jZLJYVOPJGLbUuXbccmtk38klQWSFxF/muWm6NyqH/As5uH1bO+zEwhnt1\r\nzHlMqtw7BEoYPNuR6Vq+EYxmVbkWJm6+gBRmyAo517gK+2bCuuQaSJbqVQ0Y\r\n9QGsqIggWQw4cQ3bQNegfF6NZ1lwBQm2q9HOQJiDBqoWCycLwznEvagJZxvV\r\n4ZZZ+To0xQGgSVu/xquNAToeC4oKqVSwmAWUle3vg1pFFGr6WV7KvHGp5rgX\r\nKcZFklflLlKLwXj0hY+63Sg9efM3sf9UOCIEc5Gwd3J4jyCBz6n2iDtLPNu3\r\nMVNHkhAFoB/8FFMlJLOZI40Kxh/D/uHZEINH25xtr+rjKu4awcxakemIUeEj\r\nPsvPvLrWUyJvkdHVH5T9NdMehdEvCoLX9AmQk1DdhB7ArRAIOu4OzyeI6nQe\r\nzcPq7XibPk3cMF/MQQTg4q+2T5obIbxNv0wQCHT+Dyw0gyacSgPU130HAGCh\r\nlfQAA7CvpGUQexAPhK74yoHlaLdHjRplMcQncxFFuL28H6dalHbbPyu2fp/j\r\noGlW6h+biqDl42acbImTX/2KAdU1fDhfdk0=\r\n=5R0B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.49": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.49", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.6", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-use-size": "0.1.2-rc.46", "@radix-ui/react-direction": "0.1.0-rc.49", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-collection": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-previous": "0.1.2-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a64dcfbfb0af14cb4e88438df8f9b89e7925a954", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.49.tgz", "fileCount": 8, "integrity": "sha512-txJLqnQzvp8titLFFd9pyi/kcdM2KsnM4Xw6Tsz7AZgsFA4cTBtbOh9wOsU3bSKC0wyYeinn+H2JtMaXBHG4fA==", "signatures": [{"sig": "MEUCIQCJd5hf4MYiJhR9Hd0toOkPpMl4gbQXDKjOx95u7iv/qQIgH7Pm4ANO2keL06SAGRDgWQQKiQJp8r0rCuj6dCc3MeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198AACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmsA//d9fY20CyJkvws1jkKeLh4VtNe+0gBQlrNrnk5zXAGl5lCGuV\r\nPFlmoIN/p7/kvDTGUUa7pObZXwz3JYYyRIBlvrrGn2a6GWzTnR0rlBEqoeCa\r\nQFkDAzOVRmo3DiF+cgPh1Mbf64ewVPprm//IbSc100gerOecNvQt63p+2KxS\r\nn4kKISeTq9qMhobKxs3mpTPebbFTBiGNQsmdjocJohfIrVIqNfXiDrPjq7Yv\r\niHWPfepy7+xXGXcvntLe7Qj1K0hTwMBjPj3SOby4izBjPhbkHEzxJwWJ2Vsh\r\nte4xEiTnDW2C/lID4mZZmOVTYWVvd3Ne9M6Jl+F9OhFN3DQgScU1zXev+GG4\r\nIOpqGqzCL8LUT/gE+FXdFqPSe06aZi+hBQtpQbwLxgS8KN6GE3vHplIDhhsn\r\nN6UxEza3UwM1e76FRlXH4ts7BQ+0/nemQDJ6IzdwBI0nqzjxIfacR32kn7ln\r\n+gSCgZBKTzYLH0LhR86lpUmmOJZyPVBJcKkWf2lg+ZVu+LM+m3T08aGTB2G0\r\nRVT1K6QowHQx9eGrLeWS/37nVBUmci8stg2s3Tpi8RkYX653j6gS8SgCpk94\r\nAzooz+yStIYYm5zbrds8/KIitwSanHiGZqmaFFwSb0CRa2GC0igbk9elXBTJ\r\nNwIaxzkpLHeKXcpOua3CK8OW14fjdmOgZ3s=\r\n=8glm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.50": {"name": "@radix-ui/react-slider", "version": "0.1.5-rc.50", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.7", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-use-size": "0.1.2-rc.47", "@radix-ui/react-direction": "0.1.0-rc.50", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-collection": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-previous": "0.1.2-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa7faf2a12bcb0e2690afaa1f754c7acda381f81", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.1.5-rc.50.tgz", "fileCount": 8, "integrity": "sha512-MFrwfarbeZjaCE49RmpJysi3EtLvR0aZ1LWJWi/8amcs3bpS4fecFsYN/6rUBG2K/dlvTSVZlBP9oqrTjnsDEQ==", "signatures": [{"sig": "MEYCIQD7abfMcgo6jl7H09qa0Lb83z3T3UKJ9RmaQE9eLx8HfAIhAOx7N6bKBpBeVZggjd90Jt/3ynmcEkxAuUwAhTcziWMk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CE3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLnxAAlNyOYOsFf38Fx+YwIV7s7+SfBZnMaLhnL3qWbXFJEw/nk2YW\r\nFSYnC9KAizBlV8ExKSnCCwNpLfGDJM8S1y0aQNf3GJOTFoys0d4B6Yt1xILy\r\nAMw24O5TjxNuoKWINsn2MNT2X4wluI3+tChwIB672MzhNGOC5CiU4EABAPrB\r\nRI4W8fXtsCCMPMiMog0siWElda5Yuzmj8v6i/R2DbrJKIkzigvZMX6fs0fcm\r\nJpNnUDduXaPY8tbYW4CRbgOxR18hK7sLpqW5xCBxmLaNALt9o/RsstEZ/9Po\r\nheKddnJB15hOphr7usCjEq/jiRajQeK5cVeTR9Wwg/JnIBpqAqx0aizIN/66\r\nMdPSYXBt5WWmEzN1yHm4d14L7aJM6C4p/p9NzZN8TmpqlDYKUGqzPMisodYf\r\nBDFcIP0PqcE4ntZmVh9h/3aQceIEdVilZ348xufZVA4kdGivCBj/9pZNq0IV\r\nnPeatZpmxrrmPlh86qHMqg2mnkzHyOCfZBbQJV+h6gbgiKjMHIBcfg5MBdXN\r\nnRWttDhcRYsxj8fJJQjEcubP8CFN1LiIIeW9eZ6E/iHoVttjKXrvtAxKygzX\r\nqmYILsvwPfIsk073slPwQwDNbY6fO3eCcgm+ssvWe+HO1+qGbdF4yaf2d/5H\r\n6qIeQjdIjLTwbagzi1u0Z8FXPZZhIAJ96Rc=\r\n=Gixd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-slider", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-use-size": "1.0.0-rc.1", "@radix-ui/react-direction": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-collection": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-previous": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f2ad991c6f5a2a69ee1adafd33d3c097d875fec8", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8AQYxvn467k4FveHAHmPGSQPl4dunRbhOpdTxAnc3QRZUz+FV4oY8ofjB+IX5ere/Nfo6AW4xP4C7ZRPmzCqvQ==", "signatures": [{"sig": "MEQCIAudfULdkrpQvvLGcnwtjGW4s+bysj18pLWgm69tO60DAiAN5EzIB26QpIovsmNgyI4fZaEhrQ3rbT1QVLsl4Vl3hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163930, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQTA//QGiw4NF6u74QGb4UJfKbAzo00/k98GNIZKLKZv+IyiO2l2QI\r\nzdwZAgNotq0neBxr8VM05+oqFbeaijGwLdXMHZB49B6WKRR8DIlkPCsvwpWX\r\nqCM80oPTxBv5EifWVTnLXcc4ToC/+38Ov39c7DzjtSo1roqik3FDoXHxVacF\r\nvJG2kyJjM8c7vadXiW3sys6d+ntOeZaeDxJh87X8gyhAaxQvp3zrNDa/1ZIN\r\nSSozlKWYk1TlHd4hbvKVD6F0BiYv4Dp62J5yk3Iu6hDOpNvXDjZjDVJNU8Qw\r\nQnFJwwAP+mIAA3qQIxsOHIEKbkmjvuxO+IYXGCN2RgV2t6sNoxzMDiFqD/wK\r\n+L5u5l/RBVBe0QWYdJIWYbYaErDc3zAh1nAjh19nReAaXivg23zP0oaoaQ2+\r\nSDaDqSegRVQ6/WgoSIFlbv+M691H1YdI3vwT+GjTh7+aeqlpxrS6kOaURN+w\r\ngAznGGl1rUuPq/tMpwDqBgk+3H50TVpwiSfIcD0JeV9Fsd0ugpx5ss8EEDjw\r\nO48+1IwNCxqhFGWvxBbubVF/JtGlWDJPIcCjmpfxM0qZ3QLK/9jHyNpqqUYU\r\nfYgcF8RnVP2IrMSainYeEgQwikWVixouMs4OUhPlhIKPkM6NwgGa/mbv3gzX\r\n+GohTGC4KGdmFQS5kGW3VX23IMcUjwOmegs=\r\n=qIpU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-slider", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-collection": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4cabadd243aa088eb45ac710cd7cdc518fafb07e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-LMZET7vn7HYwYSjsc9Jcen8Vn4cJXZZxQT7T+lGlqp+F+FofX+H86TBF2yDq+L51d99f1KLEsflTGBz9WRLSig==", "signatures": [{"sig": "MEYCIQCYcvP0jhDZyIhc31qAQAURHOXpnMQp3nu7mkRkj6yvQwIhAOB1C/LWH4HH6rBec8nyXE1V5qWgPdZs6WYwnuhGzVDZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMbA//VFOpUSDGuzmBRWYqmKIITO9Aj9tVaYvAGLdce7C99bkPrGeQ\r\nJRCx1GvggBKaFoPkg06EcfkfQYIsVhJORPbHYgwFupWV/ZJOHz6/oTS4CLgD\r\n4I8YEmKeattWvP9JNoZHmEsk6I6sjDhKGsT1/myRMSH/sR9+Y/ea7gurTDm8\r\nsTiRkUo0U74mwKhTpUCwwrngalRLTrCf3I4tmnHWRju0/+SYdPiu0gOn7A0S\r\nwvg8Lk/MS1VVNqtk5RUiczfz1gzRQYfx7vNnrZNxzQ4niQn4ovx/UKsShJcT\r\nMrW7ZqtTWxbXf7g24Prm/7jWOivOA74u6zvzyi63vnnZ8SRDO13wsj1wpRZh\r\n4yLAukHdXRPvr1Qh2UAMffsu/ypj7WZRnFL2UFxt6JMlEFkYo61o5fege7xa\r\nFJmDnoQVY5D3W14AnHAdYfcrBayzNICYmfT4HN0NDh13rVvz5Tpvd+7sMnym\r\ntdJffWzMKI6/UkW1kRQO8GGNWMTvdyn3hyvGTWIyeh+tk1Xrk8qEOY/6kQ3M\r\ng8QmURmhflKOWTCDXiRhH+HXumU9/MU9XkpQQDMS2qi4lqM5Dkw7npLF7ai7\r\niaSdR9IhNUmfX62U3kx2Lp9Qf8uQ0X99MKVaq5HqPMKzRvPe6pQmR66PI0pj\r\nxUvBPt1s6vnLcDvBorHwoLF4lHcz8CI051M=\r\n=dMky\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-collection": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a854b558f6e4bb0d197f34387138d1cf2e47a1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-aiZeR+nptngvDL17OlF+hJkajPLYiQQrSPNxfCXpBLjpUdmR0YEqBqgyHz1VuvI6niKQg/0zj0el8VcAuyutbw==", "signatures": [{"sig": "MEQCIBW6kRqEgKDZOokOK7ZV39d6+HlvGLQZUSZquKRxPgAaAiB9QRs5sxIodYl8gkmPocJjlOSuiLwKj/H5Od6EFebCkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqlfw//S7MMRlWhMXKGWB9epQ9ZXU5QbFwKyDGIU4dXp9bDFdkRV1rm\r\n5Wq6x/i5fjrTl5yLo3J3K5qBo9oTANDNiT01LHYNuZf/rfLovaRWqHSrVmMt\r\n/BhyawiBgp9RztNq58SdQi3K1J5VRNxu6FmRyy2YM5xfST1pBOOY4nK0Ek4j\r\nLh6UgjCs7Z2YihZLIXT0Cg6FWs1J9cPX5RgQvQrgBjMOJImk86Cmrv37aq7A\r\n00zWNuS7LVZ3hfoGd1dbYtvs14+IisOZwKDZ3whr1CVBFYvQNChyw9eyaMsL\r\ntIK/dNPnRXAb+e5FDnXpoZnf/qdU8kEgtHXh/GQ+WmVXx4Z1hrBKKZXl9XsO\r\nYQ8ZrMOIleHvoZ6vsiubQNn5uknIZy6O1WvxFqEYWJBwE6wYCd6y/ZiHDdey\r\nBwzkJONH8NsPXuncldudW6vhCPYVOahTPy5ltVZO/7lhgr84dTF4Yk54vg2S\r\nJeoG7l5rLyS3+Wzc/o1gsPHBx2DxAOVRUCH2uNssayShBof+PMNnSO2L6cKI\r\nee+bxxGa1oOyO/VhC7o4+sJcowLMKk3G7JzI6eVoTE2Is2g/6bDrfCh+WEa0\r\nTud2oBZfyZ2j+NCpWunfP88lpwE+b752Nmy5mISoZkjInUap3zj68miWTDEa\r\npyXhXRO8xflw0PaTEQxDo0XdEWjhrQ/Njd4=\r\n=QxGk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-collection": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90c27fc40344845e1fb5fa8d65b4a80277a71f80", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-uW8gJYB4e10FtkcpdXDADYGzhd6U1d5ycoNp1jimaEP+dZczezCe08DTjqNMTMPg94yIeWGPMHkOlYEQdVmLzA==", "signatures": [{"sig": "MEUCIB8k2boElg6VKQE46eKbcZQEp52UHLb1FosRuot2e1WMAiEAxU1ZL/gBEwWQruEO6JBycoEsZuls9I7FmGaRvJePF1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqr0w/9EsNPqqF78E0w9RO0QsZvT3c7Z0/03sQWKUYU7tF7GzSKMaMb\r\nwXFASpYmG4L7mKgIMbs32iA1ave8ej65Qhr35LTEyTKovBdisnz7z+RIr8ST\r\n8NNvkdyvil4ahHMm0d5Vim2/PSuaw5TjVh0yuRzV6Lg2Ky1gVcP/al+F3ASw\r\nws2B3AVua4Qd8xgPiya2l4Yx2/110Vvd9liGSf5eDLviCLOXEmiefR/b+Gv/\r\nOM36Q6PYEVKAcVeh3qWW7ESL2n62zlI4dhpb9z2UeE+ptykDJBPKDuEd691f\r\nC4gJ8VWt620WHhk8Pau3W9u+JdeISvbWcOJbxnH3hnwmvBaL9VfXVK8KfKfB\r\nqWN2225/Eyx6yvwWTBAmpaKVL1Z435PZUApbJT+U3JFOp1hLIJrSgii1ANT3\r\nnCPR37Zxwy0kgim5oehc8Wg+1FzqvqlP7QKlgXR73cFaISp6TX4DGSPOkCLS\r\nfyKbmOkgl/xQCoX4HrWJ+8PmrqWXZMYdG42dsIRmbts10kJOgir375ZH+Ngu\r\nDf/Jn8nzCSkedcRChgWdptC0suAKoHCRg3xKBurpNMxxobivwGCSx12HXdYm\r\nvX0sBHeT/VWTKlZA6mCbNQHOUx3uXQMgFy+OlrBz6bX2I9eMQx83ca7n/Txb\r\nLvKVKmfD77XhsKrLPHD1pnz1nrZBl0nxvgk=\r\n=cuTW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-collection": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d0dbc08d20e07750daecfb8da235fe663ac21c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-VZwZUdseBYKJ61IaS6izF0bWU/FT1ovYzwoe5t0jMrUhjKL8AARXteuAiDjn35cRfVyZyBAuxDgyQD0c1fggrA==", "signatures": [{"sig": "MEQCID2y58HwigmZD+IpGJ3X6V5oVc/33y8t91Y5QlsWXvavAiB3cOKz50RVn9iYdLY9/00de5PQozwLzysR55gDLUJq+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq77w/9Hu11BYxvajKjxol1xVx4VW/l4ZzYnqFVmrNIZCUcH33vHiIE\r\n9yhryyqrA9HJt1z0hzSZSB9Ub9uSvfP0EBY+t10FoFIP17fyzpX0rxJziMh7\r\nw9aVvDAb0DHKhBBsZcjHmg+wO0SFJkYj0ALPncIPpyomQBfTR5sVEZ8rQiSc\r\nWNyqhO0u3Tn3JilhtX5DCBDikE30ZJc6MjiaIvuVoz4gIGjptNY3ktCuBDET\r\nekJBfg8fkGKUhncXpAsVlpD/ysvjgP8sPOnBCsz95weNZg43DK7pyfK0R8nB\r\nKvLgHjZJTJAUEV9J9sfxDHSx4If5Pk7IYrJAquYXwcka5JoFv6D8CATgWUNp\r\nyLbYAcSMnurUHXStZxJ/viWEMdzsKrsEe/Bhq9DZXbGXW4fVHjvAU64R+r6Z\r\ng0wvaWWXY8sFvc3rQ5AQRiy9fvlmeH9rjCdmSi6j7X7CmgyOR/r/pq1eK7ya\r\nJ40ctGg2ZHL8Wqe3d3q0HBNbDQcQXZFY2KVO0ByAdI0n92JyLaZa12Wgzc7q\r\nxYtnAfiSoM9YzF/hNRE7hljDkZs4oS1LEQgTCVPd7yQwNh7fri6CmESBeIcD\r\nuU85TzYxIQ/5eCGARUkY50CgqWl06gobKlqSCtbx26C0mkhWqpotaYlt0UCS\r\nmfwZ4bNzn413g12ik2zlsO2739xU2rzs6EA=\r\n=dZci\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-collection": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d5a5c336e1ead003754a8779a8967489cbadd07d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-gsrYbU6NfwB2aYosxp7ydDbeh/H79E09mlws7XVx/rUMhH8koP+8OCQOBjXXoUxcvKyuRM/jBFRdrwlcxUdeFQ==", "signatures": [{"sig": "MEQCIEE3erAQLP6Qv17TpeAyZVqdryVOI8ddYVHoYLG4HJ/fAiA+CdOVfBAdKV2zn0zKBBWFOrJoYKzaAsDUr0HR3K2wNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrcCw//XPh4sId+vzXu2KWCMsMdJQWPTnfzL4NaFgZSUOKPoQI7vquo\r\nW3TIFJGKxnguXb++Djcdtpuflb+a59W4fKIDn6cvB1tAWp6YE9tptb4pi3i9\r\nVyi06pRlxiA4ATlpb5Dv6wudi5KJqV4XuO515yfhGTl8ZUJGZiwxOw2I6COM\r\nzlrpjAqsYr2ey80a6QKqKeEDtGwij/ZF2WFhXjXv67f81jSPQ0g2qwD6q1gx\r\npwLXKdZB4q/1Rv5+vStcpN7CdvCZ76Optwk4HJ/nFpfvqigWQ36u8zRo76vC\r\nhjTvAVhvXy5uUJ1SlBQZP5JEeSGivMFyqR2K9EJlIXmx7JgFXoLj5FVwT3Qc\r\n4yf1e92qHEwhGR+V2me6snNtNGJkVCiyhB6smCbRh4aCrcRTmZ76N/0IZ4tZ\r\nuYeODTn4cyhILRzDtP9bW/oTtgRz77MelXdfNGWe40cPpAC58/zknBjezo3d\r\nXaVcE/KnlJkyfd0DYaQH/OldhMp9jyFwCkIxp4qauQAHctVnrGUEBxgd8HD/\r\n+flfmI5DBPVLxTrwoBfpcKCH2Jdjtr8t2GUu6yFH8dyr/XdlpkF0OOh3LjzY\r\nr4KEjsLc1fn17k+HGgM7umXV7CTWGBWdXCp0YMBcu2Tyjwwj0Zr8HNsSscQn\r\n2buBBKGNlH2aZJ92kN8+PSvlKkVVbSdZ7g8=\r\n=Ke0f\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-collection": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "02d1119ace1a2bc4557862c6d2e3734c5e7bb751", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-1B8NcrZSypSPlKEExcmnRjfYgq/1uB2//wvNX5W+tMojj5VmXvKkz01w//xegyr6E+fQPvK7t8JS4LMi8cZXIA==", "signatures": [{"sig": "MEYCIQCAUaLamJY92UJTymTuZC4+VVL1yXILfhKoQl9zvlENAgIhAMm+LPRxw1BNbqubDA6J/ixLOyi2xfH8ORf0GHUzVQfV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEkRAAhVobfZ2Rb+qAFICt25ubeEl87wcDMLqItIjUeMSG0Y4CLKRE\r\nb7fI+NsLXJHjhK7a57qnidRf+9BNyuCO7hKUA1wGu21T3UoSbTlUawwwv8Os\r\n9UQY/ss38zQtH6YF7tLkO/Wi+iStPViMts+11ckvXTg8ViTRNPzfKiJ2TBja\r\n0yXJ4qkMf8ctZXDCZ0PnhryIvNFocuTg/oREpMGD1H5rGW8TBG6ZTh7aL5Yv\r\nEFiKDl0rYgkFkiPcre0PlnWFZl46peAX46v4cbnYyUoS6bILIQ/gZp2SggpB\r\nHUZHOfxOSnQM6J4+WFUC150PBiBIviLyoFq8AD486ZPvJ6l/trM5hZrgzkVp\r\nGgpOax9K0F5bmVVYUjgPUpzJCBmnC2GxILQN0PIv0M+tFJ4HNh0sEK8k7Yky\r\nxnBzDb9JVMXBD+mJc2ErJXoIDySBDxymSdz7eEfAT0A30w91HaOUi77ofIDo\r\n3Ou5TQJcKZpSOQq0dkAnwkOrqytFOT76WF09mMsWYmrqhG5OoDQWYwJ9LGBc\r\nms17l9BVZhc6/bEpEMzx3z1SOumCqsBpSX3jLN2G/dKLrg2bdu5Pxh3+noe3\r\n3ZgH8OBkod2b90PgK/eCZgemgCKtn+yGdUmFrdfzm2CGrplBRdg26vfhF7pi\r\nzDuB0SDDqLoIQKf19fCNgRrn+LAwM4TD91c=\r\n=/EHX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-collection": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bab2de00169569b1b889fe8bca4480ddfe559d04", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-gCckPp7MngVuzy64ZqYNeojnZfOjwWp23Xa9N8+2jKaskccC/gMS1jRSN5iTqoX0jCWFhCQIiGtG7lxUPoqXwg==", "signatures": [{"sig": "MEUCIQDBUh0SoioIOxt3nS2dvokl1ghknLgEjKhqtp8Gc7FZ4gIge89JOWmbk7+M/TRsUtYC/t6I9Tp+NArNk/7PDm6qe2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFqBAAnro8Vi2hFrSkgBJzF9IbP8V4sz8wmmB3oOF6e/3vgsxGPQ/0\r\nEnMcSA9piENt/CQOPKYZepi6JksfIk82oNt6naxFmel+HtWd3CT2ng7rvynJ\r\n1/MJOo2L5xnkJUv9niirJsJ/9ghioZLlPfmfOp2fiHw5g1iVcWgkA+q44sFe\r\nZPikqIThAevAEd/a6kbfi5HXuhliZ5xL6WownYP6aJRg06QubghiPKRuVjlS\r\nH5KsUkfbaJxjnmAaieYkIG3r0mW11W6n9BTrpWYwH2MYIN/UWQdbE4wbyYyO\r\n0xspVJJ+tapNFhouMhv/58F0FXqClvoIWCBBcGO46xAfXjHb7Q2WpJsQgAUH\r\nxOk8xF7Erc0GnkAJP6vibf9GuwLo9F4xU1T1VVUMP1s72As2nqcE2xaZqlde\r\nSjmGdHfOstBvS72IEP2yDYxOezjcP2ET/uxIFR1Enx5zPxLlLXscN4vWrgIK\r\ndxzMu7XOohqSvBQ3MaaT7p7K5IuDDFXJHYC/FeynFsWAo82ta+fkpqXi2KJo\r\naxEGHSMh7gXZecMjUrs2LNknWywajCpupV9Hyig61VyRvSyt82TnChkAztEa\r\naCEILs5SJpyEu/EsgEV2ES/TFGkQq7anNeRMw24JWCFcKaW7ZA5O9IykYqKn\r\ntHtwozimV5TcswwLuJuuBeGqqOZWTnFWYFA=\r\n=6yLY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-collection": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eabbeff08d9ac2f802505f0701368c04daf53857", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-eQbxOcrrhPh3mdKzxN3duXc3cL8ZBc1UAJW7vJPI9YVJ0MgZTVGwZd5utpbY1nx6RadT5UgQZ6vc64AONbhHNw==", "signatures": [{"sig": "MEQCIDOR+tXEyQ0tp837xYOHCUOjV02ls60Zwjryk3SiB8cGAiAIYNjsLYtXmd4l4iNSk6PgjAmQBvcd3CQIxgTolIyQ1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBxg/8CdTLASIbFbHpqbHsQyVgEBjFx8zWy2gACZxDPfWJ0zwe4RlN\r\nORhYNJVTSU78lhE+FCxr2h9HdWVc7+CDzhJCEzrL6qr8BQhdiAsA3M1vplYh\r\nqQHkp9eM5N7zpceOi8MBgx6DGcLu9EAAGXa6j/8IhozaCfS45SMFGW+7f5rG\r\niT8o7v6L/q4b9JLAPt+igH5zUYKQuZO3RldSsoa2nbxXZqJ0dPAmPgWNn2zf\r\nmT6d6HWdRyV+l1+2CdozrAJMXVcXqcNuiQiDOLC4PFWu9PhQ8AoxO67tZmKi\r\nbS6HSoFP+RgeaYyLLfO0fyH+3RIPSUNVUGmrQqvjklzasbUeCb85fg62s90u\r\nPfyRCoPPl5GnSUCOlBEkgFjT2tZKbjH6iTp8n+7lmEdTCc5k1cU8KM4QSMzH\r\ntuh7Jq6k6KvE/cb/Tdh8UJBFE9h+UptTTvolz8o2eUuSiKqegXUzrw8/552H\r\nLOzJ4iv63ps+kce7w4vabGZ3SJXuY7EWWOeX8BYg1GBu7IDqp3QLIIrFE9WS\r\nZx69zXYKo2GZnla2lEt7VLW+vCYDd1S0zWUczATfGFR/De1C7+hN9xjz5YbG\r\nt0vAIWexpI4RaPwbvTGp8U+gT+NJQleF7HQDhoSWcC0vryTmy3WnnzQfYHaI\r\n8ozwcmzMeqITRHp/h31QmjZvUsQ8AaWcUzg=\r\n=cwMc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-collection": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d1f6ad0c787ac0cf158d6715311a2bd13a3cecd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-hqKa5qLSAf+Qo+7O2XP5N3+/18xIkVuyKzjUE3lJN24RNzypMNheprq6O6eJ/0iN1mnDUbqVEXIKF5gr5OJ+xA==", "signatures": [{"sig": "MEUCIQDmDgpjo3dT8f9MXi0ulxOnOXAFJB0JIGXrTxr7mwagowIgWtrux72n/7lbaIRYynB5MqR+rXWaTRyUrLdjHpCcu5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+g2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq70w//Tq/hnF1gqws4TMR220L9ApkJ9wRhLcuAvKaReb9zrXgYGpXF\r\nrnM9/Fw5s4Wod8jCW3LNXlhDQ86xuk8pJg/35HC80jlVYIt88TFeid/uS8B8\r\nkru0NcqlkWeeitSFKYFgUYoWpHdAknRxKqvR77WU6ylf+0WtaZMbXaH08+XG\r\nD30O4z7ksB9K//PVWM+1ZOXmyhMhPVSJOzxyIAbO9WThhMGbR6TITRUMv9MQ\r\nqN9d0le1TSi/BaMKf4j9YOoVFq/7xs3Ighwx6IYFuaF3eUdj+mE/6QSyT5NZ\r\nHZhWJlo606X0kpS/GYmX3eJMTmSLAGP3KhnbZXJPQ4grLhvnvSXV71lhB69y\r\nY4u08c3WYjRTtvDfHPdFZpDqGIizDp35ZEkmjDvp7sU0TuhvKyj+wLmON0g3\r\ns6pFSXgtQO4mZ6hJjBQ7cAk+HlS+gYMkjKEEm9Xl/bmevv3qe2HVcFe8KPoQ\r\nERhwNYNE7qRk3UIMyQ/3y3YsJDYZ3J8pOXSSKM/x5LDbD8vVvr5jQlP7Epy+\r\nY0EGvE77/+gXyo0PpQJvZwK4jp2Hearxhjq4wdyeyvAmzk5j2Sq2Nsm1F7Sb\r\nlomq8K3m7wNGtfxk4I9HWIENWUr5bsGQTNvqRoVYz2ORosA8sQS6KczkslOx\r\nzpTu6Ag2Ir6reQChflSO4OffiVefXq+hEco=\r\n=IwZi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-slider", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-collection": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd50846e7cbca558526590cac68c3c5aee9199ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-dlp3KqWSpkpCYYJxPRpmEV7MUbiZlg3+b7vAvsujUfVB/wrtlI+w7iFqq/EUIvBhejmMEA/+HWjnhJgHoc3/ig==", "signatures": [{"sig": "MEYCIQDmNJEUiSy3DaOSSUYTuKh9Lt6wcN/bF4LpkT5nqAAbOAIhAOru9EfMd3lxXFZZ5KThK+zO8A9Dozfe58YzDQQLOQU6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 168567, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSBA/+MZacxufj0uP58jVJKyGeOk78f+jQPyDMRJoJHDNXy33Zqlnp\r\nphg0VsH+JE4uruo/ru7fVHdoNz/JSyv85csrAaVL87PkUvHgm637wD3lKmK6\r\nQSvR4e5nRENO2B4UVbkm8ovHfGPoq/jHGtWsNvQ/GaIJ/V+X4vtvz15FpuRq\r\na98rvVc4s67eraUFNulUai/5hYZ6YTfbEV1roKtcop//NH0Gk8DB3yQzsJSw\r\nXRJBxH/cx72h3KBWJE4J4uEuNqIF3bHT/mC9csHsLBwVMy/AewxgeCIJtYTm\r\n9+ioJ1bEY5CwoMeZM2IJEJ5sneMBs2qv3D1F6Di0Y9Wc8Ui30FeGY4FDZCxJ\r\nJvJGt/Pa1t9Uc8lRa45LHzQPBFXv8eiW3FbVH0zq3QQdnMNXj4gr//O01c+K\r\nPUcEzPXXKo1tFdi0WhsgfxDNFI81XmfKbK6JB9WgSVTwxs8Fy++r6nQFu0dL\r\nYYdPmuB++0CV92yvkKSch+DKzbujodcqAKMIQa5ER5W4FGS3c8Bkiz0XH3qR\r\nZalXKC08vkC8XA6N5DOAaXHw0M5dFqOHaVX4GYouyvyZU/XQMGMmX9N7Y11o\r\nAoVyYPIK5JgoHVB08J46gV+mZ9MWVoQ2sGJ0u7a2cj/rXUhZV1cnQPx8Kgcq\r\nWCKXQ77bpJ1EjkgiLjhYHHuY//ZJMdgDiQ4=\r\n=ZCpM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.1": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-collection": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d13ba39e39752a57e04b006a00ed6579c7dbdd7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Rmd1GXNGFSuFHND/ns0odBhxAcCKjSeEKf6xX0fYlanMYXYM8KG9RiCe7tu9zEzDdS60DknmzAu5UT+CUghYEw==", "signatures": [{"sig": "MEUCIQCuUAzW0klgCpl0anxe3KhwczjTlaC+/ECPjkwPXXTo7AIgCAG73u0xzIEWZ5++md1/aStQZmswFOJqfRiNs/3DTc0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsYw/9HxY4RzXfUtl8Lh5gbC+1cHayRzztVM4v2rotDieFdFKUpTQA\r\nZeKGLK5O+HTMh1PWZCbo+R0p4petuurxMM9jqP/wIIS2WT7GFsAXhLjDmqyA\r\nrP3//NJjrERBK8jOrqg/M1wWYzVZ/E+CUw/FmOkT0TqgvndYfQuBvHBFPih8\r\nY6DodgqpXXW1w7tFk6JRuqxjfoUnesQjtQBYwf6laokOsXhGPgsR53gCUDSu\r\ntXUD1oKUYMnGfx+J<PERSON><PERSON><PERSON>5FcVZSU4SQyDNx1Qyo90+HijOH7f2zfDaJI1crQ1\r\nnNzGr/jiUrlblxfk27+Fkc7WD7IWYdeQVpdQE9Ku/9owaT/pJfN4cNtWLqTy\r\nMZ5t7ydCJRwM3wjFeeAldwEPMa4RG3vkVn5C/aoe2CLTL00tukBiEbas6fUE\r\n61ZOf3H9Cy3JvOdd7EpLa5XAwipchMumgl96YdUAyVlN1ApJ7jRqClbD+FGS\r\n7zbuXQctkpzBfdpIJ7kmX77+X6CYXeiDqIi9aEiuvj3dQlHr4Gh7vmxm7Byk\r\nyEGsWYaiBOwxeWoMUaVqphMnymsgI5Bu+prxyMP3lD9xkeFynfp4wzVgPZV0\r\neMOm2Z0+xBF3KNUeiaklBuwXMm/4DsAd4i9odV2BvFrCyXcb2D2AgzPYxOgN\r\n1WcS6WsAOTfMoxiCcz/RfPyu8YWmuVvzpXw=\r\n=gmTE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.2": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-collection": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "04a407c29f6e80b06a9c4c138722c1c2b0156275", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ydJsXVazzSzPp1CLCUoMkF/qoe3kOGYHbuhOzQtZ0jdYV3wT3eqJOuli/XnhGF+sqCwYj3hr+trVPLh1M10dQg==", "signatures": [{"sig": "MEUCIQCYnX9oMdbsUaOWwEW/Z/72KEa0zA0Weugu7mk8cCjk7wIgYKTSYspJO5So2++k2tHYm/kh6LRQNJHYg5kAq5csj2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3sQ/+KKDQYc1od2V/BzGPEaPLI0QozuXTT2jUk1S6WkTx5kM2tN5N\r\n9paMtPei+tIxgrXm+8KhfKsgBK9k11t4lMmQcxuH5nLq9mq9XFhR/9GIOnQd\r\nm3aIxggPkl4Pf1joZ77d1/QT8hOEebqm/XseyJIXHAdYGvurpBW96AQsuN0c\r\n0RVD5+x97UjpAQblNveTj+FOyYus+iwrDg6a4YXCbplyixs0o0DewMrg6O7P\r\nXIiHSbpsrN+Ct0ipBfs0BBVqkGG1nNhqqxhWHepK1Gu59QF4WwxZTPzDhpWm\r\n1l/E4u9u/KM/73L/391O40+vUbwYmMJvV1vEeJ1ZJCKZDXClqkSK4Wawd0my\r\nUPqaXRJ6xP3D9lOOBCyoEz2nICIL3ghBUpo5bgoo9r70zkW8WLBd5hGGAQf/\r\nvaw1JoHMSEKGtSuIHR1IamoSBq8NCQdCYI+4jBXjSIBpqcU/fAtUtdb5VStd\r\nF0ZA7Sp+FPIMvsKYeTk6lBfFbMs1zXVwtBvPObV6nQUUBOpkvi5htToH/rJE\r\ndIHeKkkCVCl7uZ+7Ksuxzk6ekbRpXmNVrjrUSXraDGvPJb7Qs3N7ppbnidyX\r\ns7NDEfJCglvbp5jD4/+c/SIBJeOLr3/vh0YI7N9r0NYgoi3lz2ZH7ylN9lWa\r\ndJuccSfRDSPr1Nrx1xggpvUUHYmaVDUZYlo=\r\n=jQ+a\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.3": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-collection": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "deb4444c7bfd1206f92facac39d989e4aecafed9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-g506pWmVSmLznNArzs0UqUizEhEWzBF2leH18czrejSYLFKuqCD7Wy69VENuE2qmhILYem5Zxltm7TPng+5zcA==", "signatures": [{"sig": "MEUCIQCk9jkjAq8a2dwAZK3dZnc4zj0Wixg6B/7SSatVwh16eQIgQFR9alh/NCKUklVMiASu7i+0CVCEoQuqre7sXklG9/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6SQ/6AmFyCrb0TpjCZ4Gg/WHmUlMNsvjJ4TJ2UDrazegOXjC8I/cS\r\nZA34STqAATfZlE9n640yipdeA0GLLzfIRcupKUne6z4Z8LMH0oblQ4I2shpS\r\nVaci1CWimwpmLWl+DkeGDXMevJZoDa0QRw2JUoXflhLlLbNxcnQqsBEyShLG\r\nqo4lwoxMcBK2akOaQMhOCg0NxTEmyjiYz06MQruMOSGQIN+T8SVnKZb2XMEM\r\nJqWbQFbASHWKj0gyZnMo3lCMsEcMiijaZMcnz9Bv7wpIv/Uz2/oJ2CVXJvMO\r\n2PA38r8faZpd5MLG/kiO+VJX8RAdMfdVZ5FQ0wz7NfvhXp8l75FvFgRDbKfS\r\nLTSpeBhs+q3n9hvcMQi9FoHD4UWb67QfWhDZ0LisuHlW39tYZYH3R26x94Wj\r\ngvxUqcW7rWDTDzs+DjzfRGMvzVpfRn7sKjoGvx42EalPBRSsHdPVkJO1vjJ+\r\nhCP2ka+GtPOO+E4kIMV3Qx9zW7sgfFRRCdIa7rmn/EgU8g9evAlZDyQw3IlY\r\noP1lvlAJhbbZB4LZPqxQ9DykwDVSkrPkPXrd81l56QLBAxr9ze8bvYEuRwtc\r\nfKueoWCR7JWAW/y2X0rTziHRelMLqeokvrkJKOlZuo1V/gujClepv/d2Y0Mo\r\nHcClwSvN3MUedsFlA5aia7WuVF08kRfpoiw=\r\n=IjSY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.4": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-collection": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "77b49459b3b1a46d47d5bf8f16b82fb6993e48a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-iHuzdTsRCmISOIq7Dk7YdJNAGcHa/qKun3XSFsLASxk8FrMpzBF6knu5k3yRfJWqvYxhHbJzcBKwEtGtBF30Hg==", "signatures": [{"sig": "MEUCIQD2W0LlPL7052SctlYlI4cFPhqmCyAcjQE31W5SsfCfRgIgfwxwPM3s/iZPsTz+3hyGTnD5T+3qmUVrIS1y8IErhkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnK4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIsRAAmFpylFBFStvVEiOezMLt9NB0RJQceliwXU/waHF0s1dvPgbV\r\nYm6CutnvrvzlLLCxQaFvaHRamM13PWardr5E/BZV5imBYDHmVy3InZwpDtQw\r\n9EuAUVPrX7BUKEbUHKLaafZf1tOjcVmacsjqAQimxQdCR/XjQX9M7S0sGXRT\r\n37igV1jB2d615ayWdyWNcruoRcQBPdb/5NUGZKEwvttYOkTq3eu/tR3Yfuwi\r\nLxkjdSctMwH0BSGY+k/GWu5Yu57/mndkCeROK9x9UpxRlTEI1C/rUj+yzcka\r\nAnrPz0BCUIz/WWQyckSkIhsHCaaciByQJyvqj1dtOXHMsC7sacEDmt8GNuXx\r\n7cUPGu9Jw4rhM+A2USOv3v+Cu67Hrg25cbfAG+mJLrf7XvEaDLECVC40yYCe\r\ndSlwXiIkleVxC0iNrlUdSNiqS3ocS5jdwWwXFIcvHqEv9XzKPOJF8zOY1RU3\r\nVSsxjto/fDA3lMJNkPTqd76kOSaB+ZHdKDu0RPJ2g/xZATERXiG1vm3qmpjI\r\njZY95yQY6sOPLBZPekLyfTpTLB1W3tL7fzcHgeUSBEZzVA+wHT9hi9GEKnz6\r\n53bqbyxWL3+qNGeuQ2K9Y0PwtAH7+ssrt7jk4onY/n/8HcjFt5HlB8YMW2ue\r\nfoT6ebAa9cpuNP5wnokXI0yM39chXovpabQ=\r\n=ZOrP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.5": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-collection": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f4c037a9c13b4742460155f22ddbadf722d79533", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-UjY3YQ7gtvty4oT189Kcgv8xuKB5GL2ROokBpXL83rcu1cWprbx2nk0NPoIQHWJT4064+wDQQAdk7LH9Ct3c5w==", "signatures": [{"sig": "MEQCIHaagKHRb4UewB+qDwZmcn/8CMSoWmOQ/i9HbwnMzHQDAiAwNSAnGW+bLw7LLgRMln7X+0K95cGcdh5QpTNcEWqk2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxqQ//QigQd7WyW7LiQbEuxaaXIit3JqCFcC+Pciq9TiBe1e7XreJ8\r\nCkcUz15rrlw65yhiy7qLYCsd2VettTqSlPt4YKkJRK16hOKMjmSSfdyfSSFW\r\nH5vft41iXEpT93MMHqLfCiD/n+B4GPwm8GCz2WmwaDflatXd0nK+K10DjSXl\r\nW84EPtrbfscpEaM27MdD3ZrbxuwlYBd1TRokhoeraWEX8b0DPJK1gzX/+JF1\r\nRyU+pMTRP8bae5MnN9XPp1clB405H2It07VJ54e0UVT67asTPc7gIU6f0t3g\r\nRcoEFp9nyoSmiSJW8USNhOu39SW8IdgUicHIRasSNcAA71es6I5g8N5Nstyi\r\n0nrarcFsK43Gohx3eKGJMliCfuGmDH+bOhxv+tzVX6fxJ3yryNVFNk2UpUhJ\r\ncQCdkmkg1TjM4Mwr+54TwboPuBxmiDix/wnzq6uA0L3a4CvdH+Cod1XLRdzx\r\nSbho7C1N4fs0Rv6tlwriytNzGjDqysgDRZmKQlMAAzrl2Gh2PTm8ndHcOiK9\r\nZtk6h2V3YBx8qsXG/fp5DngVJPvGEd1Ouhl+d8nqdqkpkmzk4zVLyP8ApWxw\r\nOgbXF9j43BjkmZ9gTC6vvmrdINYGea4XyVbOmIlvliIwk/YCSiDHAHjw5eU4\r\nfuE1sevhNR1b1/fqu23g9pTdBkpgd2+341A=\r\n=GqWi\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.6": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-collection": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "47bd2d9bf419899bba26ab9e40f2efe238e96391", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-60hjnubsAVIInRGg/KWZx7LdM6MGMbaE8e7g/AvSrt0rGP3O7EHx1hzPgPKEsXpyVVzISJDNJF8M203blg9pMA==", "signatures": [{"sig": "MEQCIC46S+jdG1TmkJNonNdmcKzA75rT8uiTwCyEw9hDycNsAiAo57oxSDJpcdGv02C0YBUTDf0un6e6XeIj0qVpezYXJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHfg/+Nq4wYlCAcgdJ0qWoZF470guE4vkoZebR0HZFU1SHcT+D2r4k\r\nlsMyko/rjVIskbTtYi8XyFYQfXW7lC5wZA86U6JF3vwzNHMil/98OFF3FZ76\r\nL5eRWO1OUClnIiJvp9oVxEjdqOjbgcVXPBPf3ioybaW4fI8FEqTQfqRxB79l\r\nn2nhOpZKD/0tUCPyIRAiNxBWKqrvBivXjl4dIioC4wVQNn4GjMiITnVR0PBJ\r\nZqjjCnPv8ag+xWbWQiFDnOWIe9XUtAoUi9nDSq4ZajHkyt9EGCeAest0cQwv\r\nWhCQPpQ7lSsmmtjjJJmuGebfXBPbVTrb785XTbxjnMR0cM7/UaXodOYJBGB+\r\nKjnBrUAR3Yq7wU+A+1gk+5biOHRmdnuAcGtmyHWxJtdXjJV8junxxQo74ycq\r\nRjlYvHjZQ5a84eQlVsCHtTW2UMoS4GAEHmA0lf/k0YDX/ECq4saNaVVOiNhy\r\nUrwW2Nn19/f7HRKYYCqVcOUJYN6OglAempoRgXmOqbtoCdTfsp2GaYUX+nCt\r\nx9vfFaR5EXM3KxKaSwLeGNMdaBAYjlYZ/uul7pJ5gNmnxvuBZYq99R9lPVjg\r\nhAVwbb4G2D3hc0yfQjgN73K9i8dvvbrpiIqXlMliQmky0Ecs4olpTCd9swdJ\r\nmgI4bFvlj1S9rFD4UVpB8+FqxoPf6WezoT4=\r\n=aWcv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.7": {"name": "@radix-ui/react-slider", "version": "1.1.0-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-collection": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c3346a4ab53d9e880e2ced3e4aa6ee718333a1d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-seaiEKecYxOFkjXqmmHbhuJEcBg/ZUca+T5HUiutI6TUTW7fO1BizPUBe4YShfcw+R1+SGKwR9LSjVX5lDQf0w==", "signatures": [{"sig": "MEYCIQDis/ItETb0Cg58zKw/IMguJ9LITMcF3T6g/0RDEO8HZAIhAOJBIt9vxZyzBzY5Z9qUoGzUaACi3p4gJBZSLtSz8Ilz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPQw/+MUWPSl2lIEnnQQ3bp2tWwyeDN+4dzH8y1BlJAWMSww7FTthf\r\nwi9AO9K6GFXtKaEa+GTSElL7phahjQiDHqOuSwb0x0YWT8CeeFH3MYu6IsUf\r\nNgdgz7H7F3hMxUnJmw8S5481khx+IYjzWd/qn5UI2FgqAKK7of6h7aZAT/M+\r\n5uUG+oa8i4qjgrbi18YQRW8UrtuBNy4YV+xuo12KZfGL+l5InsSU5S8gVLV4\r\nd96gIhdfgqaejchckkmkucr0/+plDtqIp/2xJFVxnIwk5HxaIai/of5brkWb\r\n8kbnPz3E1KcmSfJj9hjbkunW5ixvNZ4fWKVNrLY1waJwvC2h1GYdEy/2g8MT\r\nNtX022gPPVZp/OCOBUCUlL3Y0zU6od8gjdpFTrDXuenk9PiIiNhyKyHw1GKM\r\nJUH/wmT2QgLC15rhhWRb921/eNlLe80U52CloaSZbFJ5Nwn5j4h6ID7tdgnj\r\ne33GlARLM7u/GV1Rds7GQFx6lEe0ze/FmwpqbdGGCFA9szUFgBne81iwk7/H\r\nVFnKejpx9Ocg8B9JHf2PVkHX4QUqIalCp26VJVkJDYk4ariccSkzZ8i2h0Je\r\nYE8y4uwJNUzwVVTJP1F3mYSU1WLQvH06gM2MeL1qBf+OJhY+GzTabkvKdB5C\r\nBf6qxLMxSxHrh2yjhdigO4Bc+A5/uD18kZQ=\r\n=ZT3K\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@radix-ui/react-slider", "version": "1.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b3fdaca27619150e9e6067ad9f979a4535f68d5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-5H/QB4xD3GF9UfoSCVLBx2JjlXamMcmTyL6gr4kkd/MiAGaYB0W7Exi4MQa0tJApBFJe+KmS5InKCI56p2kmjA==", "signatures": [{"sig": "MEQCIEH9jD+XT2iCX/CQzQPnzzvj0Ek9/zaq3Nkb2c2/pr64AiAgPBiHTNbXSCFRWLPATwBoG5JAIGS4qOyrSWB47SB7GQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175557, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbhRAAjQkLGQ1xMFr8BUUfZTIvGbCU7SaZcc0pEjl7e1QniR1+N3Tg\r\n6cDri8dvIKnIajEuUmfQK5lj5/Tkq7wXeIiG5VxQZvE0Aqq+IcyDyO8mtHuU\r\ndQ2h5qR0uI/OHgyistYvJAHIo47XHRjloFlB6M345MJTqr+3/0CqT8F2vX9t\r\nf6uaW1T73/IgeMHHEC8UqQ1A5yKOdzmghodQEiw7Rvqg3wcFoZSS+Y9TssoQ\r\n85KWL+6D9FAddvHI4TiFljvpjRaduKnuFJW7sRa4UET2RjttaykGX9+3Y3Y+\r\nujmHhCX2RsjzxlGHGOzWF4Ohgf7IZrkvfsyZs7GrRZ2znnVo2+Fi7Az8iiET\r\n2Ovs4pkYd7iX5dE6VsND5IEc9jaylIKVbEvjTrKBm5mAiHjjcN0n0Pod51XV\r\noqINZVhqMmsJlYNUuENtSscsNJgUHXQmF7nLmrdubojpLUGm+/Hx3a/fd6Zj\r\njT7F+C6RlpxN6jGm5tnXtghRwDGMGweUOC2Oa3bfYtxmZ0v3hCvmkcFasEpN\r\nGmAfNp//IsvEGczkqR0UMOyFBugSzmMn2P76yK46H8ZfXB2sy71Nj/e5liFo\r\nkpXNX2boWbygtk3eT8tvZ1kXXXLkBd9La6gpa2K9Brqpo2/sslXUC83Bbijc\r\nCOMBqJG7ZIqGKFtQVla0X1Ui2qT6wRbLtKA=\r\n=1hsj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1-rc.1": {"name": "@radix-ui/react-slider", "version": "1.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-collection": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8932896eb1e820b893427971b909002c7aa29582", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-SdtA1Z6i7BU/cGR/Pb+yVErBq9Z3QoMoIofYcCZZ260k1ogofqHHeXPcASHVmNC11u/JeZ4HhNjdzxEjlI0gqw==", "signatures": [{"sig": "MEUCIQC9t/sy32ZTaXi+61yI9LFPF4xUPQRmnpqgWUXy+9U4UQIgEauL5u+66S2K62FiSDL3+y4LX1+V5O7OTz0sM+gOfN4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzf3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQvA//RyCVCVKTmHHdP8kfgtkrlECEprL+8x2+yCugUPTZSxcbvzEQ\r\nb78GHkpSlPMBH5/wyXcbGgykmXRJCNQNoo30YMUlP8YWEohBfeNSPvdUoV5X\r\nGN5g2ngFlWGFTZMXvowSgzuKEyZfHvB+Zds1lzRrplo4xTs/KNYtdWEqm4md\r\n5QnMEyIXCXnYFuO3Y7SM4rNDk33hKkxz1Rvzzuvjocfeu8tIAhdV70Nj+weV\r\nsH+ljb2N3zO8HGnqigEf0vp9OdyWJQ/Eskru94Mj8ZrmXdpITydcToPyn2jG\r\nK5AIDP2fMLgKwdtYtmsVGI2GU6hss2sRLmJ9vTNx8KflRfrlBUrUZJ5djWyD\r\n3PkKLyMoDs6SuJDQOVuXFyoQmEhCOVaZpvggp6KFOU5xVm5pJjGMxrA1ODvg\r\n3HBZ+xPrmVyb0eIlQZ5Y7J5Hg7T2fy3bj76+Y3yjSTHGoyq8vpXczwFKApef\r\noC3jKH5ByoZbNa5JgK2W6I51XFIWAWT4IPt+0bYSFIYBLN7rgquRzvJV6bUC\r\nVcxzqRNiLBnKDnVnbrCYijG4U6yJtPfFs3zrNIPiQpWhDcMOi3AMkqpcqF7U\r\nVKy4w28b+U/QdVufEF+BXJPsF4sahyhJEKXXs9mEj257AGPzFiRMWA5tRhd5\r\nuMu4gAFndWsDUGwdnN6BXefogd90EqUsJ2U=\r\n=XTiJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "@radix-ui/react-slider", "version": "1.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5685f23b6244804a5b1f55cee2d321a2acd1878e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-0aswLpUKZIraPEOcXfwW25N1KPfLA6Mvm1TxogUChGsbLbys2ihd7uk9XAKsol9ZQPucxh2/mybwdRtAKrs/MQ==", "signatures": [{"sig": "MEUCIEsIo3Z1vygal855aTZEzB9RRFiBlLCsInGbj9krJLsbAiEAzFi3wpXVUmUJ4p86IAChzPqgqwlberDQWukVJyovdrc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 175557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfrBAAiVN39dt1vUxfW0aVTHOVAUFxxx2flGVdGblB4H0RWvUeYzXW\r\n+QjDegV15DaXjbI0OeHDGZYj/zE6NtTI/fkN+zTM3I1y2GlWz/PBOc2rX3F/\r\n44IDyI1Rd9J0xRW4B6mWXIznNGsZK9iPJ2H7g6iFxTtOy69QAoqfwIqiS2RA\r\nTiFTagyzJQW7f0BSdypJYFb1NFDc3K3DTn1paJDXunkN7QuHbCKhchQ++yD5\r\n+AmtWA5FL4jptHdwJSUoaERvQebZKyJ/maZpN+0CjnlcVBRweZhNKs1+8MQY\r\ndZJ+Jjf10Dg9IqGwX6wrXCAUx3KRHiy9FXaTgEz4BKIkHxDHvvd7Eb790Net\r\nQUukkP5KG/4mh95ae9D5A2JFJLyW1bOttuQpCmdPy1kayhYCGkdskG5emxsn\r\nvbX/oeus+m7YznAuS/xH0r0fWJBYDmzjGpKzYL2N21tMzwp/V7d281SVAcDh\r\n6tjMYYLQ9QsVkA+w6z5xUAM+cKyH7LUkTq/Px8ZSsPOYRH8XAmkfC5ACw23K\r\nLXg/lZwivYJKBfXaqfWKTt6hs83YswMoyXsjC7R2kOzqip2wp6Duwq/4nz2n\r\n4XjkzwsVqP0F2EBQf/NQlwu/8+y2vG640882bhQ4EHphWObcs0hk2+EWx15s\r\nQeRgcIZ9xHeDeo/zvcyfiNlKNoXb35Onw+c=\r\n=DcRP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.1": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "42676467172767cac534335f0c59e52d36ae83ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-937vnPuigHk7p+XhwOga8HQIH0YJizBWmvB50A+P7/6r9XdrdzLdYtazyfpCpI/JZAL2SMUDvOpSB4CpQxEcqw==", "signatures": [{"sig": "MEYCIQD4mAU7sB5SAXSz3qGCDwOhARWIu7hPoZUyIsSv09RexAIhAPu9FsXgtTF8zUFkrfMTXWVl8GfFViLLhPfwFbqbYXzv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFHucACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpj+Q/6Aht6L1ShEgepJsaUepxEKR0Y5Ecd1Bi0EGODuYB6NECwZW29\r\nDdzH9Y0zdbK9UAfhGqYclsHLSqQuvTUTMSY3AJl2akGM5O788DbAk2TpK+WS\r\nQKl53CSnJf4cWMB45ClW9H6nM1EqQrxLDY9zpjtCrq9ZeOkS5Mq6Uk3B/DxM\r\nmP8MwJ27lxWJ/XXLU6TYYoeweXPFA/oRje2wA5teOVovIPhe/Qx5rWsAcPI+\r\nLDeHqRxxML0u107Gw3qirwJHcmu3l2DKccYClvwSQmF5sz+UcFAm8qc7qvA3\r\n+OjbcgNGRyAT+atENcIjy93j486ivSR0nQkObVSniMI0TFk/Q5IztwmzMzt0\r\nJiOvdbYvq41iVqB95ggmQUjkdoLKlmK9hhtW4N0AcZ8e28BqZIXGrGO9Omxp\r\n8LWVXKtbN7D/QRDP9oP/I90NLS21406lrT/L6dJYZLoCyTEqarnttN+6zgjV\r\nP+M4CteyHWexmI+5fG9USRHgqexc0dJCpMUunsc2RWgu/W4StH9XDf1e41AN\r\n1PAXeugOm5DDqdBrulJ83XOWjcT6uOk8mw3UQ2eoVKLV6Uu6W2KbsB32Gr/M\r\nraHEZXtX9fgIQ64nw03/XflmqcPg5RaXWzF+pzq7uIgXDMdL3F28EnvuVKBk\r\nuxepYExoup2/SW9sHmfbRvq+8MpHN3zc1S4=\r\n=qgue\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.2": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "364ed64259f9dabd71a12d610fbf44aae12c7d9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Iivt3MJETGaza2WVdATd640aQgq6dev2b5m1/h91tZHFjGay+ZGORX9N1IKQp2YnHCAKzMm7mg1YByZEA7GzfA==", "signatures": [{"sig": "MEUCIAPlKUyxGTKQuK91q6SQmibtNCciqGwkWJ+7LzYy2xxeAiEAvMxEvJ7JbJp5HYDY95AF3bOi5kJQnZv4WdPy6WMnpTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnLsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqS3hAAjn7PbQ5S7YLWLY6kJyeJtcyjTFtm30gtkypCeL6Yzc+jFUbO\r\nTD747cy7RKkEwD9NtLZ22ls3nQVlPkkNPoDF9k29DUsdyBITlPrPyZDWLjPO\r\n6DBZtOYom1/FkfQdXf7+Uvpd0LhV87nFdshYPHhMXfHlrkVCgMqRRkTcYAN0\r\n0F+nrLfSw69A7ddIwK35Hb+Lgo5Dc5QoxZkfEC8YWF0Lkmkxl5kiqezHCpJD\r\nPqxFLss0fJ0HQMW5kFuJRgR2188mVKtEhkhvub8XilIE7ar+LbFcUsD12ZhZ\r\nP7vGF/joRVX6t1zvjYu1fBYIWOFtnWy6Ffgwqb0EgmTA4ZsP2CYFvIY1A68f\r\nx/PT3RuhGD3l3SEukZFVvajmuZw1hZdubrY1OlD8ztzS6iN8qiRrHEHPda7E\r\nM82cfx7sVIo9uI1FId8pw+8IdrarrEj0LHHddXUeMaIt5b6ppxPktt4W6q9j\r\nzfsk3ZeGRqeDKloQdlGvqIhrCgKwC0ZykjnWcZrcrHDVOXL5OPf4Tgp68xit\r\nrjlL9VuXSBoF1AQACsTLxqWUC6GJg9E5xQXKXXGroJsJYs1nmcjgd//ISTP5\r\ni/6B0Vb7hkS0O/Ho8mdB4sA8kedjtSwYEzIgaOFw/HoLz7bub1xaA9ptmXRn\r\ni0Qaqxtb6J0yvIyF7qzTwBUl6iSH3g6sl1Q=\r\n=e3Yb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.3": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74998e133e0c97ffc50b051f67a3d793ad60889e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-dhxO77PK7lto95jVVyIXQs7uW6nL7Ulzk3gKV2ftXayf/jqonNwlRtZQ3sNu2mgKQghKOzSA/gYW55NbJJUQXw==", "signatures": [{"sig": "MEUCIQDByhoKTr2efGJcAjKgboEMxS5eutHqtTIaTySRV4pz0QIgfGskC+J6m8ymTckWFklBTDCWkqrcE2pZtJo7iwrKd+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnnjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhug//Rl1fTdsKYEdMYpkmn8JOu1/vEe/PL639lk/MJn3m/TiHdE0l\r\npkRuE1K92+f7+PemwGoM96DYOlkUqz/Nd5HQarueloiRKIiIyAd15DXYtbNN\r\nMywdCKZcV0CzBfLEwHo8K4aORONZZgRcT9bmjgD9zGBrC+S4anEJfYvr0ArL\r\njs+Y+qRp1Xf3m9fLRtYfyHpymfE7dELWuuDFe5j/H4Cfy392yFNsp1od5iF+\r\nLtr48HQsHEgUxUQiEeHKlpL5noYCzcgBa2BvVy9AcuR08prXrMFFRSbsHOpm\r\nZv0WAEOoazZIIgs216R4EQ2jJkfrM6yUDkzVmt2DFikKMSOltDRYFi9bpfTw\r\nX3Sp7IQOufkvaN7uaLuZ3jrD+fa4C8YMzQqudxPAE7D0oDoETjpN1P+Sz1cb\r\n6DUpih9XVpTcoYkeAu8SE2FHCOqBtXOZ5jA5fZM3v1GRLx6afjAfaqcqVTWC\r\nDuGxVpF4UPtqa0xlSqlKS4TyWXSiaE8PtQe/JA6N4zIApYuDhyUuptDW1ORt\r\nySo5aaV2jS5aMS9yoY8hZVs9QnYeWn2mTLipE+hsZ+sCG8o8J0VJTwOsa75G\r\n+XncLvAZ3KJa4oxSA62yvhWdYAwFK7tDPgn43lHxRxfQ6SApFCBiIYiC5FQR\r\nhnljaPtwPWY7DzorO1ClHJe6J+GG1nVqKsw=\r\n=1ysP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.4": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1af6af3f96d85a1126fe0e3467cebb3a67a90633", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-RXsZ+lCXCCtSYjYX1Vc90YMVb6TmkZDG74QkQhJOGzI8KhKCD3LdLTzn8OiYDFdRCHUH94nli3gJmptCJ7be0A==", "signatures": [{"sig": "MEYCIQDcg/nD0TX2gu63ofD1mMEl4jJneFegBz3a+lWzKC9jmAIhAOiOg6Nte4L+3KBhM2KUvgAe7jWenwf7B+JUMokVB6uN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/keACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUjw/9H0z9YU+iM9WBXtAO+ITolnsVpCr28bEy77RxfY67d9rD7CV3\r\nlLuasCKG2BZ8fo9z7uPt8gr7u0FgZgIA7mv0aVPg8XWSroari1H285uzuufJ\r\n9vAo9yqoW0EJmHmMM7b8O5cq1VUAcZszig5HWWuij6UMohkFEHFwYAYs+l6j\r\n1adVF17iF/lFN8PRhAXwjEk2gBCchA/cYKiN0Q8pE98n+9eOxvFKy3Lijn/d\r\n8dRYbDZENtDOiM4taWxajMoxpMfFhxqhxLPSa5eJrRoT+XOeGm38s/sYUeO6\r\nNm/T4vd3hfB+sLFiYh1cGqjiszW2Tx7cfSXRCJUi0y7GAeLourvOQD45MAEa\r\nhzNtdvRmQPJaRizKAy8r5sFFz1aTgnPfGCgpVcs+ddHVOKwvABw5Ur7aCaGi\r\nhh1BdnK01RNAy8rmx0OEteMSgdHP+yHa+fMVDfot3cb+ig9hbFW96M3mK/JQ\r\ndnOpifC3V48ZibMy2QlBqS5MUoSimQBVm/ZD3skpZibRBmcEp1/ZIOxKOsOW\r\nK5uGwhpWbMF30w3kkp333/pir5eyxdDgi90diB19QLdcAQj8mLa4hy+JeVh+\r\nuWO2TLy0KfzQf9j5KB8QpEwTL5XyBFR9VxuwPabhOmTkYPiDyz0uhMCMFoiB\r\n5uA5vhxfb5aFxH+9Ci4kXiOAjTYM4z/IluM=\r\n=kUGK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.5": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-collection": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "48f0929e8a1950bc6db2bcb5f113894dc6346aed", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-bClyBp9YT34ofz+DrUE4cWKrSvHFI30K90m1DTs5oay+YNXr7CVTBE1MJK2BZ5vEUHh0yM3jQBc2kinpDOZRxQ==", "signatures": [{"sig": "MEUCICM7cviCwfqBrMYa7Pa6iM7qN+kCnxpuRUgffujwsjanAiEAuZdzsH2ZtkLAechZaF+w/zt0MKVMffkmJU/0BAlcLic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp03A/+Mls7QLnTk97K2GOAwdza67q5dyxxhT0o6ToXCcGGh1aWf6Lj\r\nCmR3y6LYdarYj3f1T//pVG9cIY2grHcgC696cb5GMC7XaKr8ptDxAGwyg3Px\r\nyzVhbuuU8WUJeX1XX6RGSHuvjO7jyVJWQ7Mgo+OjqrwIHuKmEbeXlQ1UfZVg\r\nUNngIBC+F9GukKTQVbNF0vTLSajsqom8LYrAmiqkAYn1bgv2bhT5vi1f3JIf\r\nMLe2iAUElgocGMK4A3jBgnz64Oy0ohH0bz70sLaQzrm5mqegxaydKPX1NIlc\r\nvaR2kEMMOpc3ZBPcmYgE/AYOuQnCljfm4Ma+q4m4t/Ume1NAsi2q3pofKBtF\r\nJw2920IbziyN6x12KbnUIU2DzyvWyQC1YzLUh4zb/RKMPckzJAXOQWNC6/mz\r\nnVGE022Ycva6daeMnZQR7a4AUN/nG8nr8H1R/fKLQgYZ2FEjHguLUQvzfbfA\r\nFV8W0qyN/LN2uyxkfjNPNkNlNQv2pm3M0W/SE5iFpwMs3I+xTkm0wPjyVMgE\r\nSZtdCeADLxikTg4vWiGly7opP9LaDCXPHmsBgWUY5ADwjgMCJECvR2ab8sYn\r\nKg19F++ZvF8E4B8IMQ2lmRmCXzZUS4anQCkDQBRF2yCwQxcHDrKE80NBY6R8\r\nkLT4OOOiTUDu250SZn7Z+/hBdvu46mw3oMU=\r\n=P2y3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.2-rc.6": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-collection": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c62a2e418a21468ecd230eb5a126c10d531a501b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-hZxULqMwPR76+Mtr6y57KP06F9d6R9QiOvlRDhM5c9lsvt4MLRufWfIggRqGBY8CUOeHDENq/o1CUW3i1hv7wA==", "signatures": [{"sig": "MEQCID4PRYcjBEK59MqE91EB6p/9tFbfLg7l/myfIegS6f9kAiBoh7eq4oqQskvLeZWGsn5J6hyY7d7yl5swjD0kHVOgnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176065}}, "1.1.2-rc.7": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-collection": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a1a1a4fd343be71a87336ec56a8d10cdab42e45", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-E7e8IvTcCzQTSKjCM+5eYVck2joy50p+aWLNX04bcZ118XjLrO0aVuieLQyTSmTibCfXxH9nEej+Lq1wUui5bg==", "signatures": [{"sig": "MEUCIQDx/zezIXkOmnzpFPS8SmbG1mePqTH/wLnGgaplN+v9BwIgWIRHaB0Mxg7o49GM498ndiF2ENWXlDRu+CEcpW7wYKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176065}}, "1.1.2-rc.8": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-collection": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ab022bd632fdef6ebd79e43c810ddc653c64dec6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Xy9g9rgwX+2G0WGFt2LqNfUSnhD/JAHl81aTW5Ju6Z9IkRb/dbEgERrGzv4mi7quun7bnSFpG0QKMHXY2nHjwQ==", "signatures": [{"sig": "MEUCIGaKE/kZoO1+d4CedJ4N92yMPCPvEb+EdcfhwUlUYztRAiEAqTLPoYKQ6P6V48iIX6Qrjr8q0DnctPVayQR9hKkkM3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176065}}, "1.1.2-rc.9": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-collection": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "368b2580fa6be351d0ce873f0dee60b7685639af", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-DaWJwP5OXuWu1bVw9wEwLqRNwGUnF3QlWmb+sWO8eXDQ7mtxxGWSc964FH3iQ6aSADFObGxf3+M6Lz5Mjh2AhQ==", "signatures": [{"sig": "MEQCIEZ+136FHaBZ/4aTEhrMkKcR7atFBpsrwAw0u/w9miThAiBc1dtyxYHxhwNSxE8pHIzCvRjrptlwOUaDjLSoOPJ3Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176065}}, "1.1.2-rc.10": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-use-size": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-collection": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-previous": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4a83ebeb546b03986bc2cb8af763c5b6eba05280", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.10.tgz", "fileCount": 9, "integrity": "sha512-BeK4PMdamTUwsiFxMQgzc0nUqQrH0IwhJMK12AotAvTqyE0G8MFsJVj6IUaMfEFRNQYWkYg+gqzScu5+koyfZQ==", "signatures": [{"sig": "MEYCIQDaaZc3KCIZxkLVCyT95BEvwQMyee3vpnird7UYr/6G0AIhAIviKH/NIsBj1inuXp4HI/a3db3ZIg7Tqprsm3h6SgJk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179608}}, "1.1.2-rc.11": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-use-size": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-collection": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-previous": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "179af9f505049c2eb2deadc3891cb6e33d648cd1", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.11.tgz", "fileCount": 9, "integrity": "sha512-soFscGCGB+SVU/dn+AW0/KdopX+++m91RyjwXwX3zeRcqlM+d2O/yD29qzk8Ky2NDdvMKTB4VXB3Rg8ZxDvnMw==", "signatures": [{"sig": "MEQCIFDsHjFaZAivnsct9AoehmYEEV7b5WiHf0Sq0oP1bOJwAiB4BIboiVoyD5dqbiOZPwOBVN/eI7sTKu3Jr73zFae1NA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179608}}, "1.1.2-rc.12": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-use-size": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-collection": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-previous": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5fd4297906f8732a174cc820ce67f4766f6f113", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.12.tgz", "fileCount": 9, "integrity": "sha512-JKt23f2Mt46JX7KqG+2pxqmeRRbUDgJok29ROrqYwLHXYP1AYQh4+kvvI2E/2OcUO1MbMD/6eOkRc3gwUsDL6A==", "signatures": [{"sig": "MEUCIFv41hcavjEqhZI/fBk39eFDFqQvXUhpoTnDfEgnRrtYAiEAkSi/Wb2t6k/c9UfSlFNKL1J8aB/F0zUmar71gpcuJJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179802}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.13": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-use-size": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-collection": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-previous": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d2578dc59f1c7d46138c453ddb55a3c8cb3546b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.13.tgz", "fileCount": 9, "integrity": "sha512-PChucOfSdhNs1QksaMtN8jYFzolsfDvguWGCVDXARm/NPvQgwWWn5Kn72HlkDe8g+IKQOL+ZqIbfCHS+dBxzlQ==", "signatures": [{"sig": "MEUCIQD7t1at3U5JPW5TUckDa9z+pr3wZMx6fha1Nu+INK8lDAIgYowRe9Hd/inztfQWuJPfXYN0N4EY6VbHqrn31FsNG4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179802}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.14": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-use-size": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-collection": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-previous": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b477f7057fd410358668b5ef42404fcab9d14367", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.14.tgz", "fileCount": 9, "integrity": "sha512-0rMR+wSUArKfLkwF8D1n35gKw1+HOk2s97UdOjM9aOgz9tCvbchl7ts3IBN4qLouQIZIiFElRqOx+biQ9bbOcg==", "signatures": [{"sig": "MEUCIQDDzgKoY31QOk6z6za4F3n6q33pQJ0/sgmA8iW9+zxr5AIgHk01/gBy7sYQAuRHfhxOifTHRpi8Qh6hZ28UqvO3bQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179804}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.15": {"name": "@radix-ui/react-slider", "version": "1.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-use-size": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-collection": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-previous": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81ae28f0bd3ec3cea841adefc11ddd553652213f", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2-rc.15.tgz", "fileCount": 9, "integrity": "sha512-TKYdNFXFRn+j608Imd3l2vxn+OOa/p0ba08qfZka/nE+IrOS40g1Jrb3DPeUidz1BROc9aZ6DHcSABYNTvBRmQ==", "signatures": [{"sig": "MEUCIF3pVQ3MiRcc7rafdrYHPUSGnoGXis3hvqGHM5PSXOvgAiEAgWntG807GWQAif+Y8UmTnix1i/Vk6KK/neiN/W7Fmok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179804}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-slider", "version": "1.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "330ff2a0e1f6c19aace76590004f229a7e8fbe6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.1.2.tgz", "fileCount": 9, "integrity": "sha512-NKs15MJylfzVsCagVSWKhGGLNR1W9qWs+HtgbmjjVUB3B9+lb3PYoXxVju3kOrpf0VKyVCtZp+iTwVoqpa1Chw==", "signatures": [{"sig": "MEYCIQC1Cuuhu3P/I2ABeag0pc85OzzfskC9zFes8tGJyy+ocgIhANnMo/EZiKUlZm/yr3je2D3adtmLV67Ua7rGEunNG6Gj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179713}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d97444ab0d6d4270047d361c0c117196f4d92da6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.1.tgz", "fileCount": 9, "integrity": "sha512-WEXG7IkweW+dgNp+dMCtuy2oOQRlSWuTT9469SP8mvl5KzRHf0PW6RekplPkGjcrKJhA6pvGJSvjuE6roz7axA==", "signatures": [{"sig": "MEYCIQDj33EUDXaSmY/fHN7eVafHE+7Bowau7Pi+OkmmHWywDwIhANPeggkZ1ctLV2mnq1N79cXS7wZYLs6wBVisZIMFAuWe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.2": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b15494e5b063f3b5a5676ed7764106e93f98238a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.2.tgz", "fileCount": 9, "integrity": "sha512-2XzGTZ0Dix8LaFYyxQ2UaXp8+R05mpmQRcvebLPnU2jRzECMl2khq3w2TYCKml//kslkog0WE8w0KO+lBOcvpA==", "signatures": [{"sig": "MEUCICF0JXtQ38cJa3IwYqOai1ZiRAiPdR07EvI0xEZykxdaAiEA8L3T1MlZN/Hz9ptpvOOBwB0d3dCXp1iTDKpbk7I9Nbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.3": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9730ca6e9301d731af04aca5de7906a752e36d8e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.3.tgz", "fileCount": 9, "integrity": "sha512-T602kaAqp5nKTbKlncjRn5DaK5gjkFLaSCtdwTPoMtX4j8zt1j2fRAe8NeCe/68PQfk4UM1LFmOykCW3SrDfXQ==", "signatures": [{"sig": "MEUCIBC1cKT1qte1deFykkixDY7l0zjKbyO/OSOE/nop/h85AiEA1KmObaX6SAilFAbXTOM3agPv0hVy7GOIlseETzmXwiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.4": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.4", "dependencies": {"@radix-ui/number": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-use-size": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-previous": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cc47eb0a8608f35e1f987dfd857eb0e1ff88b1a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-3h+NQWYFoh+vm1dXd2vHcZmIlnR1efQCclA1oANKjgDYiuwNceDcch0UpnVrDmHgvb6pD9vNaUs6LBDMy2ibUA==", "signatures": [{"sig": "MEQCIFWaA7u/QK3cR3zhLQtDxr+A2KFW/gRiGWrUN0p1ik8hAiBZk8tK8rmdqasCnfJ3l/3bXpv6pCT3Em6CngcstPbuxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135340}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.5": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.5", "dependencies": {"@radix-ui/number": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-use-size": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-previous": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ab43e86806e4eed34d96ed765049f383881ad682", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-NHZ95C7LEkXRtZaoBvxDdNhBnGScIMtSeixuc5VyzZGS9RUxTkAE0u/ugAn9iVlpUU67wftfoeay4Y2UrBi9bQ==", "signatures": [{"sig": "MEUCIHtAjIHSXyIUhZsIKRuXQ5yNu4MQPFOk32GGqDu1EJq6AiEAnjr3SBO0QGV5AhFzgfAvWdv87zEJU9lxkzN50Ab+XXU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 135372}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.6": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.6", "dependencies": {"@radix-ui/number": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-use-size": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-previous": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "696ecf27de9f65cb92347fa337320822fc54b22e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-jAUpFodAdYCKMBEBqLV1FyDLhJp8UNJEBCNEzS8QIfVJ2OUJaLb100JwOCnunLbkPcx/VLNeeaPEq2fWtqDmYg==", "signatures": [{"sig": "MEUCIQCNfVAvLCKs6Gl+bMV/PLF58Zah0FaAHOAeYpVB+hWUdgIgP4UBGe/RTRhAJh1stHL4ROqdDj0c7MAZao6TsB/m4fU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134672}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.7": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.7", "dependencies": {"@radix-ui/number": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-use-size": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-previous": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5e5728add8a9c9027338657d6b2db188e5d1a59", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-5NC/Co2H9M3maMhtSeeWaZniL1vUCrMdVIQl2yZcCWoV9nBybxt6m+jyHdvOENpdcWBL0XhlKSvANj7IOCtrSQ==", "signatures": [{"sig": "MEQCIDJvupsv3Yv+ecSoU2VPVPWfaTK/knGY+Ju76mwSnYcLAiA5DQCwv4Lo1Obr2rcpN9AQNnAMec/5VHqcKrrSDVvPUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134358}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.8": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.8", "dependencies": {"@radix-ui/number": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-use-size": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.5", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-previous": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6ccef6a2a164667025cc17a8c346426677a09739", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.8.tgz", "fileCount": 8, "integrity": "sha512-o5cYOKnrcVlR5obpiYL3V+svKgar/ccPXpWzTi7kX4hN1hCC7UPW31Ux0C/2oKDg+4emZhU34IpOyiOLHUg2Wg==", "signatures": [{"sig": "MEUCIDC9Hdr1NcI/2j9EBMG4ZL6nGQk7UTUj3dMPs1oTQINaAiEAyzU9Hbeo7xLWpWCh3CMm7ZgVLjbVaVg19GqcpRmnj6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134358}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.9": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.9", "dependencies": {"@radix-ui/number": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-use-size": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.6", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-previous": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4fb96aae9f7fd8d79be318fdf50cbd03bdfb1678", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.9.tgz", "fileCount": 8, "integrity": "sha512-YBpWuyTpMtm0N82135izcbnLVNsITrZ9SK32LgIgvJRtCf9ve3U+BKIh/tT3+QFjIXTkdp5mnyybN2aJJtOgjg==", "signatures": [{"sig": "MEYCIQCFkaWtZNPliwCrhc1D6z7woBt4zwkixHu1q35HLYPA7AIhAOZcC5gREN9UisAJrWOkX6/qHl1BieEvgo5LO85mhHPm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134358}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.10": {"name": "@radix-ui/react-slider", "version": "1.2.0-rc.10", "dependencies": {"@radix-ui/number": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-use-size": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-collection": "1.1.0-rc.7", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-previous": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "85fc5524bfb7724f4d3df5b0b0af6df8251fe57c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0-rc.10.tgz", "fileCount": 8, "integrity": "sha512-SCR77dfkrHwVOC3tr19XBWsUCMtmBIGm6y2T0TC7DSIOIcLUov+5DB+qNFqnU1NDzcFS5e+hjBy7lnThQNqVcA==", "signatures": [{"sig": "MEUCIQDLRi86KIKY5NnUiStXXts7PGbOCylCFoJNh26XlNkGJAIgJxVLSc95jMbWwPV42qADDjVHU1+gh+AVo48ElKmafbc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134387}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-slider", "version": "1.2.0", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a4c817d24386b420631a3fdc75563706d743472", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-dAHCDA4/ySXROEPaRtaMV5WHL8+JB/DbtyTbJjYkY0RXmKMO2Ln8DFZhywG5/mVQ4WqHDBc8smc14yPXPqZHYA==", "signatures": [{"sig": "MEUCIDQu/tK2g9bLcmdAmwRwlmWPjTZDxjGjm5F9TMoVavXeAiEA81n+eLGe5NB5q+KVIRDqutBQF1URtves2rkvGz0n8SU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134298}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1": {"name": "@radix-ui/react-slider", "version": "1.2.1-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6e091b71a91dcbf8370f77205a2961291354c306", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Gk73OaIWk+/7ZhuyBHK4UR3xzwkhmQ4kjWdMckx4iJScAQWrEV+C4thr+hZ45C1ugQ7xnc2eiBbMkyoKpOKygw==", "signatures": [{"sig": "MEQCIF9tRGpjPXt+ZNn0tfrnGupiinRhMYWmCPvbUOTg5TfKAiAkhQdP4EYla2vPPj7yeHsGF3QBkWNCF273ROIaX7ja0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134894}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-slider", "version": "1.2.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "acb0804309890f3cd7a224b2b0c4c4704f32921b", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-bEzQoDW0XP+h/oGbutF5VMWJPAl/UU8IJjr7h02SOHDIIIxq+cep8nItVNoBV+OMmahCdqdF38FTpmXoqQUGvw==", "signatures": [{"sig": "MEUCIQDCUV2QctUuaWsQkP5vL+kGGS9C8VoOYyMYwPxM7960JgIgQ9Z6tH0YP6n6rvwOwLvo0DADgKH6/RNHuTSmtgiYPf0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134856}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1": {"name": "@radix-ui/react-slider", "version": "1.2.2-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-collection": "1.1.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8e9d8dae761fe58c0f3a9dffdef4a9c17e11500c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Yu4gnKCAwEM4u/oXWsBhNcwBwzHXA/qbXorNHpVOg9rj6mU4DS10C2gwsqbuAkvSdl8lLPN0H6smx8pIjBlNlw==", "signatures": [{"sig": "MEQCIGZCwbgkVfBuX2pm/LHWLio9zYZjhBlBd2Vh9YU2siElAiAkht31t7IPeZxRDbQJQouknbe0ooHL/TczKaJT7agHgA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134719}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.2": {"name": "@radix-ui/react-slider", "version": "1.2.2-rc.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-collection": "1.1.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8d5703e0dc857423e3d3c27a371f0dddcb70f3b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-QpJQvXv/N2RJaFXqZVbe0lX19hDS4XTgkungHAckQep4dYAwq2/71G4vb7ptsi7DBl56YiHIFb4JkK453e7IxA==", "signatures": [{"sig": "MEUCIQDYYDfN+Qpv0m50mcgkWJhpIw11BHPDCb4RE5DOwck9CQIgfQy0looU8lTVYAlHRsHmu1FZNVpEf68bL7UV3bAyBS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134719}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.3": {"name": "@radix-ui/react-slider", "version": "1.2.2-rc.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-collection": "1.1.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "588dd51f875aa426e0a3cab403d10b4d752ef474", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-KoBddkhKWeUrSQBRYFXnQA+EXTB0V9O5Oq/WQAXAoo0IL+fqf3HheqW7wKDP5Di8GjEku854YRxbESfIYoJ7Rw==", "signatures": [{"sig": "MEYCIQDIs8gO01wy3vesq2HGOESt7onTLFIXPvp9K390cnQs+wIhAOy2smMeQrSx/6UC0K2TCtkS8dE0+wKRHJPKllPlV5b2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134719}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-slider", "version": "1.2.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4ca883e3f0dea7b97d43c6cbc6c4305c64e75a86", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-sNlU06ii1/ZcbHf8I9En54ZPW0Vil/yPVg4vQMcFNjrIx51jsHbFl1HYHQvCIWJSr1q0ZmA+iIs/ZTv8h7HHSA==", "signatures": [{"sig": "MEUCIQDZu6yN2Id4ZiSPuzhdVj+eUIjXkR0EafYkC7cU2HrWHQIgfoPZrGIAgiKrdLxWVYCtWONFK9V9OflG3CLJS7NOM5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134666}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-slider", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/number": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "460e6c2fa127b78c02deafbd38000361a2216550", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-V8cNNadN6kInxjKcY9ce7I1i2iBldrQc/pGiDE85G0d7K1h4YZhlaEy9Tf1o37kAoEyc6tEOR/7O87HDOL5Vng==", "signatures": [{"sig": "MEQCIBIVNI9TUgEAsvDSJym6YJ4Cxnp94kNyPdo3BlgnEgZsAiBGSuTSBADZfFEMZVSU1Uc2B58WKZqH1ZE3S1ruZvi4dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134717}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-slider", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/number": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0db28c35f9b2130d0cfa7aeba5dd6b663bae6458", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-ud6CGhiwUFa6jWSZ/TcP4R7z5cCPvnTFGssvNWT2w14Btw68K7x2mdMjoMG5k/7seHeKZcZCkIVQYn6SK6AQwQ==", "signatures": [{"sig": "MEYCIQCgbo5/TBMZJofIzZXnHSKRJNrYB15soVD0wGFMiBBIwQIhAMHGmPF7KpNpkc/AvT+NlLAGW7HqDWt2RBH7iM1Q1ZYC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134717}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-slider", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3f45c129ca4e613fb2844d3ce0375a1292b5250d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-LZg4VJFDdQ4fUHb8u3s8TRB3SR4Q5bpgDEZo/F5kDQ7TgIfHMWB0EMIgmm/b20xTvukwDPIOYB8aLYabIaVwFQ==", "signatures": [{"sig": "MEYCIQCoiArQHCDq9r1aExIbIcsbr1l56LUdx/FBvkM9vDbDlwIhANnpe8Ghcy+qglbXKNhTA0CdwAZ0dfkZfvnvhCLEAH+b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134813}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-slider", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"form-serialize": "^0.7.2", "@types/form-serialize": "^0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "285738903bf7e3a76dcd130c89b45b6adfcddaf9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-BwTkikydAXzBpAKHuAUfYaxTsTum1YdTDK2RtSzmwOWIb8hnT/f2eir32+OMVw6YEyh3ipZaFB666ZD9E7ulMg==", "signatures": [{"sig": "MEQCIEpwGk29X7rpMZZqFuwp6qlI/QD74DFM/UomG81dq5iJAiApzsN7nkpA0YwikvY4XOKkQoBD0igjeeUGQi5RJv0QPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 134813}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1": {"name": "@radix-ui/react-slider", "version": "1.2.3-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-collection": "1.1.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eb12d827ca70294b72be0117a520ce71b3815ec0", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-DzMPvWPGTVK4NxjToX/ZqiA5ZBOFsir4LjSNH7FpFiqIZcJpnHO7gEmhsA7/RWl2fPMhPSjMZifuyBBiP/9dbw==", "signatures": [{"sig": "MEYCIQCctkFHitXJmchyHWg6oY1BVKLCGtSmyU2Xt0UXYPbzHgIhANFBTnwI9hlpbzU6UKCVwzIqIUIqJSiQ9zNAAdfKXq4g", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 134895}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.2": {"name": "@radix-ui/react-slider", "version": "1.2.3-rc.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-collection": "1.1.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "08bb381da342dd6c1b8b76fc5de2d53ce66b0038", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-002uDjPp897yD2RC//QSyalW3QNZzt8Wn57owNiung8vjXF20dOsU27EMGUMM01AJ0fqRsyHgg/qEoo50rhNaw==", "signatures": [{"sig": "MEUCIBuF5P+ZNPWTdXZ67u0IUETC9BZwEqodjGCYtNixXX+UAiEAtfgPghviYhVs+kFnoqC/7ilRUd4C9dJMF7ZUpDD7+wI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 134895}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.3": {"name": "@radix-ui/react-slider", "version": "1.2.3-rc.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-collection": "1.1.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9268f426205bf776dccf6e376df2f6e5297528e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-bdJ5SOkiw233uY1M73yI14JG2GPZEgMyMFyXmm5RV3dWCB549VwmtJFZY2qlhio41fXY0zj0wZ6LeudewTbb8A==", "signatures": [{"sig": "MEUCIHK77SElQhFysjVYVZCVIrhw8LniZUOWenfdviGtS1UxAiEA6CpPIDYRRnEE6dHhoZIVQXqPRl0JZxCw7H4Scfo7Zi8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 134999}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.4": {"name": "@radix-ui/react-slider", "version": "1.2.3-rc.4", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-collection": "1.1.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2ca80d113dd119c4404f967bda3d37afd9e22473", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Um5bod4zO6t7N0Crp1UA2QnIBUUIFzOTxP4CAm3hfCrVWpuZ7oKAzqP2FjEBk4eB6SYsBeQZvyHqF1NezOS/iw==", "signatures": [{"sig": "MEYCIQDerYMcO59fpjF+Na8wc+pTSQJsO6M8q7RLEF2YEL4FNgIhANdaApA41o++ByclzMxIOy2EXfza5fJVQeAjULSlngMS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 134999}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-slider", "version": "1.2.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f40072e88891d756493f27bb53285e32d0e6af37", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-nNrLAWLjGESnhqBqcCNW4w2nn7LxudyMzeB6VgdyAnFLC6kfQgnAjSL2v6UkQTnDctJBlxrmxfplWS4iYjdUTw==", "signatures": [{"sig": "MEYCIQCLUPNZkEyDwPnKmvpliyUKwQ/iTJu+Kpg70LO/EIdAkgIhAMQ22yucL6kMY7QFfgaLdSwhKxjQABzTmNS/LQygIsRd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 134956}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.1", "dependencies": {"@radix-ui/number": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-use-size": "1.1.1-rc.1", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-collection": "1.1.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-previous": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a8af1eccae2550b49d04734d38a3f3f7aa06ed68", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-/sihg34Hg5J6NVKLj3aEp6LehMzAI2p9oJqzAV5BrcXUhNMjUoskgSCfCyvILIQgQh00VcGI2xnFWi5x5Ad87g==", "signatures": [{"sig": "MEUCIQCpUfxt5nVJ7VqsIq4nLD1El3Fuw/OQw8kXOPERMyWppgIgHqqCspweU0770bLQF8/JmOovhC7Ujem2fIiw6mpVZfo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.2": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.2", "dependencies": {"@radix-ui/number": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-use-size": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-collection": "1.1.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-previous": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f4c0df92e28ea9442636afbf283afe4f43787fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-v3w16hSelIgA/N55KOZFEnbB3+ZLG+Igh4st6gply3Ycnoe7JNIgluh2uWr2MuT4r165l6G0jTIoR+oEtmikQw==", "signatures": [{"sig": "MEUCIQD/gNHG4HmVJZ96ANm0FNfD29oR8v3mxSosYzBVNE75jwIgC2nIvImzUiirX/VQ9wCG62hZXqn4LR12D7PRZYrdVcM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.3": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.3", "dependencies": {"@radix-ui/number": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-use-size": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-collection": "1.1.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-previous": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dc378ad156cad44c20b1a93802bce9d867bb1aa7", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-yqo4yGGdycYxOg+oNfxg8zI50m8lEYDT1w4nNevLUejWt8m74TBvEAbF/kQutt8Der59ZgzBTF+FHMtPIlFhWA==", "signatures": [{"sig": "MEQCIHZQUGD0ItPZ4X2UOnhujqjviv2G24O1wpXHDUKIQyz5AiAGVQ10B+F2RYIs5xgQFvZZimttAiVSgrzXZIxlBVc9hg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.4": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.4", "dependencies": {"@radix-ui/number": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-use-size": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-collection": "1.1.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-previous": "1.1.1-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fedacd4c9284a8095761de59881f304207c48e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-/qgaIL4AxAi1v5tQeL+ffo7wI1Bynu+DnhYv38zcjLZon86/XB8pk8s5JoEJYTE/JS1SYUo4Pujc1Th+MlbX/g==", "signatures": [{"sig": "MEYCIQDbRPakhlQh5E/644j6jDtM8G534bgMsKi53JLvnVFrsQIhAM6HO7ES72tzXqohaS3LXI1lTYeA7FUsqSuBzzO8GoMm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.5": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.5", "dependencies": {"@radix-ui/number": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-use-size": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-collection": "1.1.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-previous": "1.1.1-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "041ee473d702cb055f8116b17985ff53f5196398", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-hP3PXKd2iGb+HGv35/qmU4+n1IHBBx7cxQt51rRRfsd7+bk53X1dYm2PF+Vw5K1hXWLS5iqrAKU7v8g/JgvqkQ==", "signatures": [{"sig": "MEQCIAH7qSdmVhunAY5VLx/dE2YHiEmxljAwTygRGBS0ijYEAiAjxDkhEXyV3DsQn/3D2za239KlLrWo43qixHk5JxHJ/g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.6": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.6", "dependencies": {"@radix-ui/number": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-use-size": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-collection": "1.1.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-previous": "1.1.1-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e817d3ada0007c83d047d36f74b96916160f1d92", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-N7zxKEZg+v0CzfajVrKzIUB2A/vjk6218mGrcSJ0KKiiCUFXMo2KaXfr+clqLYq3YDIg4hhvJfcozLGnKWJzzg==", "signatures": [{"sig": "MEUCIAta3Ti6czbFK5yZsNRAs1ZzgSCX6SGAtwhG7WA8v6+7AiEAn5UzcUbnWOIOY7wuibPoBBQ211jF1gFzslD4IGL1wfE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.7": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.7", "dependencies": {"@radix-ui/number": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-use-size": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-collection": "1.1.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-previous": "1.1.1-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d25739d310617dd48478f8aaa2f4bbcb621407c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-LVuoB7pdXoMh94WEUDX/2NkRiqp6XUgjWvwK2XP6h1KwdbQh/1eTpG/Ra8iuY+Qd7b6CljKMKtWe/6KHsYjMhg==", "signatures": [{"sig": "MEUCIQCctzDdpj6kNWYy/yX8hNAuxxHy9aNxe7t87TcO2kIyfQIgYGDFWK+VkfoVdPDWjJEEgShB0K4pSennifajZHq3QTs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135050}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.8": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.8", "dependencies": {"@radix-ui/number": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-use-size": "1.1.1-rc.8", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-collection": "1.1.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-previous": "1.1.1-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fa88d461b15f60f5cac03aa8e9f693cef1637594", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Bx1+viqqarUoeCu/pk1LUfj2i9gOSKNoqMVM/PRFLpjvy3rEn75AHVBtCPxogNhpRywb8hVvek9hKWCcmNglHA==", "signatures": [{"sig": "MEYCIQCnuAfoAj7uQ979CDdGRpXaFKp0aG3KlnLq8/KqcPfY+gIhAJGExzUgT7YKp6PSJ6fQoL4Vz9j/prhaSdl0rgXPZNq0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135441}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.9": {"name": "@radix-ui/react-slider", "version": "1.2.4-rc.9", "dependencies": {"@radix-ui/number": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-use-size": "1.1.1-rc.9", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-collection": "1.1.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-previous": "1.1.1-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d9ab90b260a67a37343ab87ec6518c1a8e132349", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-dbTKH0G9v0n+00wtqT1BEkx5Vsz0AH6YkgiVVl8FHKk1igdcAivGmGNxQBvr5OQBm2B9BRVG7SGn8eThMj6AyA==", "signatures": [{"sig": "MEUCIQDdemdti7a8u/U1hT9XVHZkwYwxFQD2vdIxnSQtkMWTdAIgZiiRra9J/f8PKwc3l9hpAg/LazdrMCoJFhnl3BbjAr8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135441}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-slider", "version": "1.2.4", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "form-serialize": "^0.7.2", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@types/form-serialize": "^0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "29cd5e53806d828f81f32af8ff88489a25c1072d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-Vr/OgNejNJPAghIhjS7Mf/2F/EXGDT0qgtiHf2BHz71+KqgN+jndFLKq5xAB9JOGejGzejfJLIvT04Do+yzhcg==", "signatures": [{"sig": "MEYCIQCfBDpAHoiu/oU39kUeEhJiRN7jHU4DPBkeiwDviTIGFgIhALeYZDIS3RvqAiDeTeeLIVn0gU48NW19b2x6RWmTBBo0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135353}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744259191780": {"name": "@radix-ui/react-slider", "version": "1.2.5-rc.1744259191780", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e6c14935d06318eae5ae3b55fd71a2bc94ccd8ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.5-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-YE3XJ4zmkNh+kjdhiILAOvRAUtVBRwP9vfs8vWbIZwZbyIaykREHZbIXX/tSEK+5UlJxPI3Wxvie5+WGayQhiQ==", "signatures": [{"sig": "MEYCIQD6HcYV3kfAEfNVaXgFnltL5723AVz5gCQQ5JiQmphgdgIhALY2ffkBK7LWQ/e71VmasF8GYNMdqC3lR/77KIVoG15q", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135876}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744259481941": {"name": "@radix-ui/react-slider", "version": "1.2.5-rc.1744259481941", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bece4eb96f640eecf90c4b2f959a4cc15bb477ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.2.5-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-rIhIlKPbCJ/WBo2SZrB96jywQRZcvMDJLTc1qvMB2XsFY3gC7fl+ys9fjd1+iFJ5ip51raNckCASN9PtbBqGAg==", "signatures": [{"sig": "MEUCIQCkYQZkivJHx1LdzbNO24zZ5vRS4Rp9tme4GlT+Gd1iLgIgVI69wLJStb94TtByumbmV4lq4w1Jr3QCyACoOpfnJmM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135876}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744311029001": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744311029001", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-collection": "1.1.4-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7247f093c5cb3688ed8f1d7f28e64eb41bd397e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-5LGG3AtOztwOvrOumL09ot1+ZdBMZGkpmWuuxzj7oheBfB0fMQg0kqY2TkpsyrBhYLh/ZjBkf6ziY6C7v+lx6Q==", "signatures": [{"sig": "MEQCID3WlbDlA76xpMrzsJ03g0e/8TO6uJz5sCp4XX7qs1BZAiBIwiWzhUn6ArSOe6Rrv45SJ4PlVYOKY14eujsdvGnjrQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744416976900": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744416976900", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-collection": "1.1.4-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bcf216418745dd07a750114bba98b23c42be5b65", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-jxd3kcsx6sM1HxDKlL0+o9Qmxh2NT2C/Cwg1P4NJACgq6OymOJvIST6nMztW0AN8erPqJjD4e4QhbS3LWYGAiQ==", "signatures": [{"sig": "MEQCIAIlIrN3JIG9dLy+8ZE0Y57PfHpPMILcUTzDv0oilUEBAiAhgkHKDrdRG2ozr9DjSq1kVpJG2ElWbMZLZBlz3bjy0A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744502104733": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744502104733", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-collection": "1.1.4-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "29642c6a07f6070d9668cddf62dfb9e909bc4a35", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-YRIkxC7s7/xeWa+EM305EkKwYA/ZTM1fioee80ZgGFkyWJxNLohtOnAvvLJaJMcUvXBYcd6veNgG9v1vLXEc8Q==", "signatures": [{"sig": "MEUCIQD/aCkEY+7X5eIo9v8/q5K3MOEGgBtCuheuQP7ZA+4dkAIgdzIyTVnvmEO+KVb1S39B0aWVwnpPTqbi9ZhtTedgq4A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744518250005": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744518250005", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-collection": "1.1.4-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f695b925bf90fa3a808d30e6deacf732a981584", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-vl3ZVg2DzyoK315XxlkM4JbBoaZQdthjCilMxr0vr1XB8nxj+KigLMOburcahcDE2XDYWIFUy1s+10t4h/dqOQ==", "signatures": [{"sig": "MEYCIQD80PQdlUAoXF5l5jRlkWqNU0L8sC7PKAJ/VTjBwNp+ZAIhAOHN5JpbS9I9W12jN30V0vB3434hWRPyfh9rZEKk+2yO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744519235198": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744519235198", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-collection": "1.1.4-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c6d084369c0dd19cd1df91285accfc6a1fb57cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-I7QgkIOVLaOIIjoPwkX406dqpp4G821GySzHC/KezLEh2Vm+nR+dD3NSFTpf5nVrF5HGYMvtQV+pOtqVSrk/9Q==", "signatures": [{"sig": "MEYCIQDWpMTPs/EeQT1RnHbjEjvBSdcz5jbEyP/WblTv3R1BkgIhAPhiNPXBezzmN18IDouUH4lz9QyXWjFCFHqEdpThSC+W", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744574857111": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744574857111", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-collection": "1.1.4-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a2ef77d82d281fad6b17337f22cb36fe94f1a733", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-isxwChcpl+MTONFrijmkO4gGQyOTXPBbabJ7CwgNwSs36BzQN2yiHKFp2JbXJALH9/iwc/C3H3LHqKG0BOQ1gQ==", "signatures": [{"sig": "MEUCIQC8mKX92qrVXl8gEStGv5OfODWkO/z5XKbxcS+8IJUq/wIgI8rZpjwcyhr2/eSRdJyuN2l2BVAXBCBqPpzj9S33yeI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744660991666": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744660991666", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-collection": "1.1.4-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f67faa948f877c7a265ef7e2f11180252fd95ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-cP2uZS04GlAtSFlXui4L17Vjru4gpm/KBGMq6V5CFF5UFo+3K2VCcXtsbTG5JM9Mr0eX1aMXWp5kEt7RUj35BA==", "signatures": [{"sig": "MEYCIQD089gmGk9+SrAv8OLeFd1XjZwL0gBRfUsgeh3ImWcoKQIhALPtb8ZyxQJ0TofYN7SMqgUlozEQpN4llPolAyLsJPK1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 135910}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744661316162": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744661316162", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-collection": "1.1.4-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "686b1f738976be6535599aa84dedc38d091061bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-rEntKPX+UjNuH8LgvLcWIHfOd6VsYhvlBCoMnnwCtKxL2rwglpJJno0PRjjaa9x/Y+cREQrPDtEID6Iahw88BA==", "signatures": [{"sig": "MEQCIHzb/JCK0nmDEmMoJXRWIfOQFbGlQ/ca+OZzGyV2QZUcAiBI9CCUN9I5pbB83a5e3io5DXnq+DYi4JgCZMouQ0W0mA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744830756566": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744830756566", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-collection": "1.1.4-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eacc8a6bd20729c362f7c26774457f82accb2bcd", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-LIX2rfBJqIiiAxQt1BrkB2D3PVLEJSntiX7iyQXK9KY/1WotABg2yP71V89qxNr7Rf5U709MQREmZA4J6qBkJg==", "signatures": [{"sig": "MEQCIDKY9R3rOIzqEpUFKyG0VyRQCW3GyIyAICpcdE/4wtCiAiAcWloT6q1RqMJdM3q0Ommab6RbwpLlSGPOt8owO+qd+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744831331200": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744831331200", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-collection": "1.1.4-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0a772d72b7b7d5567a11c60d1c09cfd2570b6a94", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-FSz9lGj4M3vW49HF2UQx5Nr6GDx2fjk7HhYqJLI0yt1N4HAElzajP70vpWI285hVwgJUDfapnAXFdjhPOk6yUw==", "signatures": [{"sig": "MEUCIQDD2rKrH82mdW+Q0+GsRFlSxU2NEINcgUGHbpbT+Om8LQIgTju2jHs6A8hFSDet2ZpUhOoCC0+blMX+qhiZsSADyPM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744836032308": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744836032308", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-collection": "1.1.4-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "32203e78403ed2ea4120538c782bc95bdca1e0bc", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-QQt8xkss4VShbT+DzoHrwZigXLKaxu8x4Ivtf/FPdZ4KRzq+cLwDjUq4oPonZpJiqGh2jzDoKRWoUXMlX66Kpg==", "signatures": [{"sig": "MEYCIQCruPLr17q9ojJEJO6PQI70mo2JoHMtAg8F7dsaEFXj5AIhAMDF4gaa6NCR18AzHznZPdCPOz9EDvAPVNzSZ2IAu7GU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744897529216": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744897529216", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-collection": "1.1.4-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0723eb3f86e50455ff27bd035cf42bda611ff31c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-FocV7HCDc/1UZvqi9DLebgCBCY7gpIVRHVqvQhVCclxV085iWhL79YfDSYrWHuK2jAPl6WE+uSjTZqhaycJqsg==", "signatures": [{"sig": "MEUCIQDre6oiSGHsl1YZ/KQaYHFgMfSMi02yBLLCr6FK5nRSNwIgUnnucPUH8AjkSBH6gKtBnm8Am4WRBhlUo4YPuyv+L7I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744898528774": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744898528774", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-collection": "1.1.4-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3a73a3b2dc7d159c1f0e6ef4e8453f1fcccca83a", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-wiecDBhxXpCfZYyegDycm0BQAzSpyVyrt9MEmCOLa0YcXA22e1gGlRZ7kmLDCrVVbYpVPqoVPaYUno4fSeViYQ==", "signatures": [{"sig": "MEUCIBb0sV1EOYuMekjz6n5+E2b4MMFMQS77AZ//e07Khn/GAiEA9Edglnw7nHdOgDkVKX8/QaqNHqlFPiv9T66q0O8/81E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744905634543": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744905634543", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-collection": "1.1.4-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2762452cf867f2d6f76ab57ccdcb214b465daf87", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-gZBhGFeDHMIEfw2hjvlYoffN1ko4yJrE4PcJkl8xu847xHoPyU3YoGOjzWcfHT1dV440XlotCrFWBP2Hj7t3lg==", "signatures": [{"sig": "MEUCIBsqF5jx+bEq2mCN1dWAfQWsu07S86AEOxD1S2pS6+SYAiEAn74wFeT5ujvp6k2jYipC7Ueuc6djVdGqN42PpIab624=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0-rc.1744910682821": {"name": "@radix-ui/react-slider", "version": "1.3.0-rc.1744910682821", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-collection": "1.1.4-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d1d610d83c0e174c2db49894ffea8c4565dcee77", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-nFzZX2+5zKjIA1CVGNPXoGUd6Azgaq5M9QeFtqYG0qY4fpcipivhfXSUHBtyx2iZZDE35FHZf2bdd3o5YxK+9g==", "signatures": [{"sig": "MEUCIDdsti4baRAAkq4QEX1TV/ccJdZRpQMqU2rcLqGk/MVpAiEA8MelGlD1opoWVQDkok3QKwaQ1Y5OVrfQdApdnrz8P90=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.0": {"name": "@radix-ui/react-slider", "version": "1.3.0", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "006a2cde4dafffc568a9ba95a5c952a84b82f8bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.0.tgz", "fileCount": 9, "integrity": "sha512-Xrl1cC+auaE7IgVmeVJH2teod5fEvx3ct1LEzhKfkmHskk52VkmanjXmwjHWuK3g6wKWl8zsf6ZbDum23QUdFQ==", "signatures": [{"sig": "MEUCIQCGkM87sYU/ZaCy7ckjYA1hzS3hQgCZ5MoH9YKoIptp/AIgHUoSvlj7pPFay2glT9uVh0/QNxVEU4KDn/aUYR9/eNc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138368}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1-rc.1744998730501": {"name": "@radix-ui/react-slider", "version": "1.3.1-rc.1744998730501", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f32eb5790f56941fa3eb6f49577782048526d8df", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.1-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-fsjokjUuFM4p8tvjA5oKgSP0Th/8r8iP4rogNbzRBMQtOm5HfgHaKtxuVyPc5/Gw1XP3cMx8ucPuBXH/Z7ZTlg==", "signatures": [{"sig": "MEUCIQCt80TUvjqmRaliA4OICpbtRZ4PgzqPCR6mMy81Q3KfygIgMz3yzzVb8DSKBIzrEV/QyCKLbhz4VYladn3rEI6R8XQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1-rc.1744998943107": {"name": "@radix-ui/react-slider", "version": "1.3.1-rc.1744998943107", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c77eb3e8e7ce06c267d86ec16e75e288a7bc653c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.1-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-aIl0dM9upl4RUXBoTjx6KJvz0DweRvq9Ia6oMfuy2RIaiZjwPJlWeu6i5wa650Dxg6F2bgKBSBKs0CrmTmu77Q==", "signatures": [{"sig": "MEYCIQDGY5x2ShTgahItqndiE6YR1057S80BaZZva3kfyjJT/AIhAKN5CIXfDiFVDrbkyVlKld2o2Q1voM2R/WIHqHbbCW8X", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1-rc.1744999865452": {"name": "@radix-ui/react-slider", "version": "1.3.1-rc.1744999865452", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f451fb45391fc8e46f7e204b8c0fa5e304dbf779", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.1-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-rlLOk1lM1Ov/oV3X12l36qkxAfrEnutSNxT1fR8nmOyJtYgOt6H0LYYsaRQefcy7uHPxDAi0Q+rgMD41UwFqSQ==", "signatures": [{"sig": "MEQCIEAEjBkk8bI6QY6kJoGw55o2/JqJVlGiZ/8eTa9p/0EoAiBWIykz7amIy/eE8KlZ0lJekADq2bFQ//gM61GFlje4Uw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.1": {"name": "@radix-ui/react-slider", "version": "1.3.1", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "38fb55987ebaa191e058b8bf0d326a7c82b4e00e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.1.tgz", "fileCount": 9, "integrity": "sha512-emQJ7BC+V2OfygV53qlJ9O7fYND4oqKZ69qX/N8287lNUh+Ov8HrjLq0DpGq2odGOFkYZMfaDj529LCHQMvadA==", "signatures": [{"sig": "MEUCIQCPCLHAONo+gAz+W1VbNsoCHDK6twh1lE/jZmvgeoX4cAIga+NXjzUEv2pMJHdpr6AD/bNV4XDda0wip/SDufUhkPs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138368}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.2-rc.1745001912396": {"name": "@radix-ui/react-slider", "version": "1.3.2-rc.1745001912396", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "23235f0bd777dada73bad37a466a7b461b149450", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.2-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-/VFKXjP+aym49mNwcMOaqBK1Jaz3OPppUT3uOjSMXMW7kvp/ex7ez7CtvrWVl8X/8Eoof1WwoizVqU+NqKOVzQ==", "signatures": [{"sig": "MEUCIDDm8DLBs7dgZhqOVqDOMWdUYcyRDfvcPWbux2/BI4JHAiEA9BBcBnQU9GvDV+0JVjQ1uHX9TSLYN5ULqCFXA6ooOe0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.2-rc.1745002236885": {"name": "@radix-ui/react-slider", "version": "1.3.2-rc.1745002236885", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dd9250e931926bc5fb8e07175db570c5fcbf839d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.2-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-QAqQMbjvuWUokSakInuWneDD997NOyZniTfnH4kSm5ltNQr/L1gMAMnOil5dM3cGtYRN8pIpYOiQa0nNyFLPWw==", "signatures": [{"sig": "MEQCIA98JUVVA8yRwJMgmGXA18vFaML0JgSIWKCajF8KCxysAiB/dNWGNM+BwBCZAoGdBbUXyb0//m9I81ZlG4Ijtnrt4Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.2": {"name": "@radix-ui/react-slider", "version": "1.3.2", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0849ba97f841a03b1be6a3ec5622575aa356f098", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.2.tgz", "fileCount": 9, "integrity": "sha512-oQnqfgSiYkxZ1MrF6672jw2/zZvpB+PJsrIc3Zm1zof1JHf/kj7WhmROw7JahLfOwYQ5/+Ip0rFORgF1tjSiaQ==", "signatures": [{"sig": "MEUCIQDpYb4hyNS+Zo+Ygw+fY/zeixRKnM9IPbLRXfWeUedjaQIgbE2J2yv3JMsey1E4bnRcc/xaEKAjWgcGOx2DjptSTmc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138368}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1745345395380": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1745345395380", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-collection": "1.1.5-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c9343874fd8e659b5bc25ed468dcec4e3a01d05c", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-jq0hpMhKhkMRXXBHsyMC5E/p1+iRZUNemmhtlloRYkSfiqWniWQFHXkudIZLnH/qIw+FxWkABGP/SevsrxcYEQ==", "signatures": [{"sig": "MEUCIGkjkGwOn+5Djuwr2L0REevBiDYdjTuGUmQpHcLTUYV4AiEA/MIWrYgoB1zkKwNPmtO6MKqA+0IlnRN1PN7VKmgrC8w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1745439717073": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1745439717073", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-collection": "1.1.5-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0530efcf3402e1f80b97534912786afa41b92b99", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-nXp9e2fuEs4mTu7i0IpRu4CT1AdCdvqnrzGb5uNBmoK6J1J8UdN8xxM1WnxovyhtteW+HkBjB6zhOyzhLoOiMQ==", "signatures": [{"sig": "MEUCIAQCLUoda9jt1RYZPmvhdGS72ybG/W8pX20yH2FWme+QAiEA2nrtTAojMGcbhLfAvfCtKV9Gm/jCxekhvD+zqbmHS5k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1745972185559": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1745972185559", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-collection": "1.1.5-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "937e71366bb1d0310b4a6b883dcae417cafe92fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-Hw/exI5BB3j02QiVumg92MyD0XNa5BTdCNFH2cwD6pHgUqqBlAC3nMpmTbvK+aFcw2syecUJLJNfAbyStvRcEA==", "signatures": [{"sig": "MEUCIAjqNEFgt4lz55UAZ7m5s8tvLrXyica2Wz0jNa1/ScofAiEAw6rlH5xZbHYYEBzBEGI2rrq+YxURMybVzo5/GIAIxHs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1746044551800": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1746044551800", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-collection": "1.1.5-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a79b1e6110205608349ab0b33d8a76c6b3498803", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-3pOFYtoDnVf+Eyti8ERVaQh3Oe343gudyRPlQCAUxnjd6Mch77+d85DbPmyx+/UFy1CidpfrCKVToWvPzYiO0w==", "signatures": [{"sig": "MEUCIQCw4V4orSYH3l+5tKblH0d7jj5sOLaMO6rAycchNYT+OgIgMzL2BwS7oiqYlQHYEkI0wJK7ohFwLxHCVn4GtDpbzMg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1746053194630": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1746053194630", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-collection": "1.1.5-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "154bb5df92456b0d99b6f4a9c988c16061fee3b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-6UQaFB89UOGnuL7Xj5dZkUPPflb1kon5fZHmEXffkOmqWhXf/JxEYWZPZ/uKfBjyfxUK08HJOfDaNNtjEp6GMQ==", "signatures": [{"sig": "MEQCIBXfsdmIyUxYNn1VA1yMgH4MmtszftNWoRRunW+M80PrAiBnwRDKk3tzlV57CKVQGaIEF+RAuusdTtjVlK6mAjC8uQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1746075822931": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1746075822931", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-collection": "1.1.5-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac420f2499d10377faa084378ccee055dc4cb97e", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-lmzMlaGvvFWssrqF7UUnHDxd0CWA3sBCOfxWRuHXe+c/1+ih8Lu0nTIxgzaZR8+tnIwz61yXGjC11KtIJG2m/A==", "signatures": [{"sig": "MEYCIQCHzd9mEjOpyPHYiT0h01tNQqL5FTthn55/tHKhtbrPGgIhALcqoKjNHyaY3I5463QsbmdgS4pWYPYGhNdC3MEkr/3U", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3-rc.1746466567086": {"name": "@radix-ui/react-slider", "version": "1.3.3-rc.1746466567086", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-collection": "1.1.5-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "87ed30261adafd7bb28bd6acec955fe096640c7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-kgCksKQc8TDQ5bL3oP7XOS6EwGGSoNFELQnvYvuo6Cz8uTsmo9ec9QLLClIHBPBgrphCkJwt/bC2FyDUOhzi8Q==", "signatures": [{"sig": "MEUCIEVkeCbwolcYaMmxqM/4zJzl6bzVWDTgyTyDGbzkNdY1AiEA1WbqzUlHOlN4F6rUqcyQjaYCHeGoM3wZ9C5aF2aCmq8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138419}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.3": {"name": "@radix-ui/react-slider", "version": "1.3.3", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-collection": "1.1.5", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d6cfe5100b14afb17a8f6b243b74981bc18acfd6", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.3.tgz", "fileCount": 9, "integrity": "sha512-Pt3xN1rYNXCqeZUkqZkeKMo7USXxCWkr1dQD/27vt5m5Sh5PLy3QPNLnBD5NIA+qL4e7jtCohQD8hgmm22jvLg==", "signatures": [{"sig": "MEYCIQCF9BANKt33zXLV3wks3oblArU/NqkqRmmohRgdrsO/9wIhAP7z/h4eIm3P53WQLJwKCOYE+f0Jum/gVVvgX2p615TB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138368}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.4": {"name": "@radix-ui/react-slider", "version": "1.3.4", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c52c4ffb20cd16470480d70f28ab73dd2f8aec4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.4.tgz", "fileCount": 9, "integrity": "sha512-Cp6hEmQtRJFci285vkdIJ+HCDLTRDk+25VhFwa1fcubywjMUE3PynBgtN5RLudOgSCYMlT4jizCXdmV+8J7Y2w==", "signatures": [{"sig": "MEQCICJpkF3T2Q5Wv6YRvcHEGmuX+ziOytAbOIGo8vTS1qVcAiB9pY+aIfAdjL35o1RgMfQ29XLX0it/KvU8/x3Pf1N40g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 138368}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.3.5-rc.1746560904918": {"name": "@radix-ui/react-slider", "version": "1.3.5-rc.1746560904918", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-collection": "1.1.7-rc.1746560904918", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-size": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-S/8jtxlmHbOrm+jjLLx/77fdj9L4y2MR2vh0tHDVz4gGKMuyNFiMqAPXM2KTKTaU6FpvYVCQhd8DyXtZuFIZgw==", "shasum": "ad27b460d18d9e56f8590f572393e5146f2cfc67", "tarball": "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.5-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 138435, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICmzRaUOjb8vwLqlj7TNJ4dZmwiWChclyYL87GJb1IxuAiAPTzg0SAkoDyV3Z/Dfp0rpRtYq3Br7nbVkVUVadQW3jg=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:32.536Z", "cachedAt": 1747660587898}